// lib: , url: package:flutter/src/widgets/scroll_delegate.dart

// class id: 1049178, size: 0x8
class :: {

  static _ _createErrorWidget(/* No info */) {
    // ** addr: 0xda8814, size: 0xb4
    // 0xda8814: EnterFrame
    //     0xda8814: stp             fp, lr, [SP, #-0x10]!
    //     0xda8818: mov             fp, SP
    // 0xda881c: AllocStack(0x20)
    //     0xda881c: sub             SP, SP, #0x20
    // 0xda8820: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xda8820: mov             x0, x1
    //     0xda8824: stur            x1, [fp, #-8]
    //     0xda8828: stur            x2, [fp, #-0x10]
    // 0xda882c: CheckStackOverflow
    //     0xda882c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda8830: cmp             SP, x16
    //     0xda8834: b.ls            #0xda88c0
    // 0xda8838: r1 = <List<Object>>
    //     0xda8838: ldr             x1, [PP, #0x440]  ; [pp+0x440] TypeArguments: <List<Object>>
    // 0xda883c: r0 = ErrorDescription()
    //     0xda883c: bl              #0x64483c  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x2c)
    // 0xda8840: mov             x1, x0
    // 0xda8844: r2 = "building"
    //     0xda8844: add             x2, PP, #0x4f, lsl #12  ; [pp+0x4fbe8] "building"
    //     0xda8848: ldr             x2, [x2, #0xbe8]
    // 0xda884c: r3 = Instance_DiagnosticLevel
    //     0xda884c: ldr             x3, [PP, #0x450]  ; [pp+0x450] Obj!DiagnosticLevel@e37021
    // 0xda8850: stur            x0, [fp, #-0x18]
    // 0xda8854: r0 = _ErrorDiagnostic()
    //     0xda8854: bl              #0x644784  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0xda8858: r0 = FlutterErrorDetails()
    //     0xda8858: bl              #0x644b40  ; AllocateFlutterErrorDetailsStub -> FlutterErrorDetails (size=0x1c)
    // 0xda885c: mov             x2, x0
    // 0xda8860: ldur            x0, [fp, #-8]
    // 0xda8864: stur            x2, [fp, #-0x20]
    // 0xda8868: StoreField: r2->field_7 = r0
    //     0xda8868: stur            w0, [x2, #7]
    // 0xda886c: ldur            x0, [fp, #-0x10]
    // 0xda8870: StoreField: r2->field_b = r0
    //     0xda8870: stur            w0, [x2, #0xb]
    // 0xda8874: ldur            x0, [fp, #-0x18]
    // 0xda8878: StoreField: r2->field_f = r0
    //     0xda8878: stur            w0, [x2, #0xf]
    // 0xda887c: r0 = false
    //     0xda887c: add             x0, NULL, #0x30  ; false
    // 0xda8880: ArrayStore: r2[0] = r0  ; List_4
    //     0xda8880: stur            w0, [x2, #0x17]
    // 0xda8884: mov             x1, x2
    // 0xda8888: r0 = reportError()
    //     0xda8888: bl              #0x63f6cc  ; [package:flutter/src/foundation/assertions.dart] FlutterError::reportError
    // 0xda888c: r0 = InitLateStaticField(0x818) // [package:flutter/src/widgets/framework.dart] ErrorWidget::builder
    //     0xda888c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xda8890: ldr             x0, [x0, #0x1030]
    //     0xda8894: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xda8898: cmp             w0, w16
    //     0xda889c: b.ne            #0xda88ac
    //     0xda88a0: add             x2, PP, #0x22, lsl #12  ; [pp+0x22558] Field <ErrorWidget.builder>: static late (offset: 0x818)
    //     0xda88a4: ldr             x2, [x2, #0x558]
    //     0xda88a8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xda88ac: ldur            x1, [fp, #-0x20]
    // 0xda88b0: r0 = _defaultErrorWidgetBuilder()
    //     0xda88b0: bl              #0x870294  ; [package:flutter/src/widgets/framework.dart] ErrorWidget::_defaultErrorWidgetBuilder
    // 0xda88b4: LeaveFrame
    //     0xda88b4: mov             SP, fp
    //     0xda88b8: ldp             fp, lr, [SP], #0x10
    // 0xda88bc: ret
    //     0xda88bc: ret             
    // 0xda88c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda88c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda88c4: b               #0xda8838
  }
}

// class id: 2582, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class SliverChildDelegate extends Object {
}

// class id: 2583, size: 0x28, field offset: 0x8
//   const constructor, 
class SliverChildListDelegate extends SliverChildDelegate {

  _ _findChildIndex(/* No info */) {
    // ** addr: 0x89d324, size: 0x268
    // 0x89d324: EnterFrame
    //     0x89d324: stp             fp, lr, [SP, #-0x10]!
    //     0x89d328: mov             fp, SP
    // 0x89d32c: AllocStack(0x48)
    //     0x89d32c: sub             SP, SP, #0x48
    // 0x89d330: SetupParameters(SliverChildListDelegate this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x89d330: mov             x3, x1
    //     0x89d334: mov             x0, x2
    //     0x89d338: stur            x1, [fp, #-0x10]
    //     0x89d33c: stur            x2, [fp, #-0x18]
    // 0x89d340: CheckStackOverflow
    //     0x89d340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89d344: cmp             SP, x16
    //     0x89d348: b.ls            #0x89d578
    // 0x89d34c: LoadField: r4 = r3->field_23
    //     0x89d34c: ldur            w4, [x3, #0x23]
    // 0x89d350: DecompressPointer r4
    //     0x89d350: add             x4, x4, HEAP, lsl #32
    // 0x89d354: stur            x4, [fp, #-8]
    // 0x89d358: cmp             w4, NULL
    // 0x89d35c: b.ne            #0x89d370
    // 0x89d360: r0 = Null
    //     0x89d360: mov             x0, NULL
    // 0x89d364: LeaveFrame
    //     0x89d364: mov             SP, fp
    //     0x89d368: ldp             fp, lr, [SP], #0x10
    // 0x89d36c: ret
    //     0x89d36c: ret             
    // 0x89d370: mov             x1, x4
    // 0x89d374: mov             x2, x0
    // 0x89d378: r0 = containsKey()
    //     0x89d378: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x89d37c: tbz             w0, #4, #0x89d560
    // 0x89d380: ldur            x0, [fp, #-0x10]
    // 0x89d384: ldur            x1, [fp, #-8]
    // 0x89d388: r2 = Null
    //     0x89d388: mov             x2, NULL
    // 0x89d38c: r0 = []()
    //     0x89d38c: bl              #0xd82734  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]
    // 0x89d390: cmp             w0, NULL
    // 0x89d394: b.eq            #0x89d580
    // 0x89d398: r1 = LoadInt32Instr(r0)
    //     0x89d398: sbfx            x1, x0, #1, #0x1f
    //     0x89d39c: tbz             w0, #0, #0x89d3a4
    //     0x89d3a0: ldur            x1, [x0, #7]
    // 0x89d3a4: ldur            x0, [fp, #-0x10]
    // 0x89d3a8: LoadField: r2 = r0->field_1f
    //     0x89d3a8: ldur            w2, [x0, #0x1f]
    // 0x89d3ac: DecompressPointer r2
    //     0x89d3ac: add             x2, x2, HEAP, lsl #32
    // 0x89d3b0: stur            x2, [fp, #-0x28]
    // 0x89d3b4: stur            x1, [fp, #-0x20]
    // 0x89d3b8: CheckStackOverflow
    //     0x89d3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89d3bc: cmp             SP, x16
    //     0x89d3c0: b.ls            #0x89d584
    // 0x89d3c4: r0 = LoadClassIdInstr(r2)
    //     0x89d3c4: ldur            x0, [x2, #-1]
    //     0x89d3c8: ubfx            x0, x0, #0xc, #0x14
    // 0x89d3cc: str             x2, [SP]
    // 0x89d3d0: r0 = GDT[cid_x0 + 0xc834]()
    //     0x89d3d0: movz            x17, #0xc834
    //     0x89d3d4: add             lr, x0, x17
    //     0x89d3d8: ldr             lr, [x21, lr, lsl #3]
    //     0x89d3dc: blr             lr
    // 0x89d3e0: r1 = LoadInt32Instr(r0)
    //     0x89d3e0: sbfx            x1, x0, #1, #0x1f
    // 0x89d3e4: ldur            x2, [fp, #-0x20]
    // 0x89d3e8: cmp             x2, x1
    // 0x89d3ec: b.ge            #0x89d52c
    // 0x89d3f0: ldur            x3, [fp, #-0x28]
    // 0x89d3f4: r0 = BoxInt64Instr(r2)
    //     0x89d3f4: sbfiz           x0, x2, #1, #0x1f
    //     0x89d3f8: cmp             x2, x0, asr #1
    //     0x89d3fc: b.eq            #0x89d408
    //     0x89d400: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x89d404: stur            x2, [x0, #7]
    // 0x89d408: mov             x1, x0
    // 0x89d40c: stur            x1, [fp, #-0x10]
    // 0x89d410: r0 = LoadClassIdInstr(r3)
    //     0x89d410: ldur            x0, [x3, #-1]
    //     0x89d414: ubfx            x0, x0, #0xc, #0x14
    // 0x89d418: stp             x1, x3, [SP]
    // 0x89d41c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x89d41c: movz            x17, #0x3037
    //     0x89d420: movk            x17, #0x1, lsl #16
    //     0x89d424: add             lr, x0, x17
    //     0x89d428: ldr             lr, [x21, lr, lsl #3]
    //     0x89d42c: blr             lr
    // 0x89d430: mov             x2, x0
    // 0x89d434: stur            x2, [fp, #-0x30]
    // 0x89d438: r0 = LoadClassIdInstr(r2)
    //     0x89d438: ldur            x0, [x2, #-1]
    //     0x89d43c: ubfx            x0, x0, #0xc, #0x14
    // 0x89d440: mov             x1, x2
    // 0x89d444: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0x89d444: movz            x17, #0xaa0c
    //     0x89d448: add             lr, x0, x17
    //     0x89d44c: ldr             lr, [x21, lr, lsl #3]
    //     0x89d450: blr             lr
    // 0x89d454: cmp             w0, NULL
    // 0x89d458: b.eq            #0x89d4a0
    // 0x89d45c: ldur            x2, [fp, #-0x30]
    // 0x89d460: r0 = LoadClassIdInstr(r2)
    //     0x89d460: ldur            x0, [x2, #-1]
    //     0x89d464: ubfx            x0, x0, #0xc, #0x14
    // 0x89d468: mov             x1, x2
    // 0x89d46c: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0x89d46c: movz            x17, #0xaa0c
    //     0x89d470: add             lr, x0, x17
    //     0x89d474: ldr             lr, [x21, lr, lsl #3]
    //     0x89d478: blr             lr
    // 0x89d47c: ldur            x1, [fp, #-8]
    // 0x89d480: mov             x2, x0
    // 0x89d484: stur            x0, [fp, #-0x38]
    // 0x89d488: r0 = _hashCode()
    //     0x89d488: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x89d48c: ldur            x1, [fp, #-8]
    // 0x89d490: ldur            x2, [fp, #-0x38]
    // 0x89d494: ldur            x3, [fp, #-0x10]
    // 0x89d498: mov             x5, x0
    // 0x89d49c: r0 = _set()
    //     0x89d49c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x89d4a0: ldur            x1, [fp, #-0x30]
    // 0x89d4a4: r0 = LoadClassIdInstr(r1)
    //     0x89d4a4: ldur            x0, [x1, #-1]
    //     0x89d4a8: ubfx            x0, x0, #0xc, #0x14
    // 0x89d4ac: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0x89d4ac: movz            x17, #0xaa0c
    //     0x89d4b0: add             lr, x0, x17
    //     0x89d4b4: ldr             lr, [x21, lr, lsl #3]
    //     0x89d4b8: blr             lr
    // 0x89d4bc: r1 = LoadClassIdInstr(r0)
    //     0x89d4bc: ldur            x1, [x0, #-1]
    //     0x89d4c0: ubfx            x1, x1, #0xc, #0x14
    // 0x89d4c4: ldur            x16, [fp, #-0x18]
    // 0x89d4c8: stp             x16, x0, [SP]
    // 0x89d4cc: mov             x0, x1
    // 0x89d4d0: mov             lr, x0
    // 0x89d4d4: ldr             lr, [x21, lr, lsl #3]
    // 0x89d4d8: blr             lr
    // 0x89d4dc: tbz             w0, #4, #0x89d4f0
    // 0x89d4e0: ldur            x2, [fp, #-0x20]
    // 0x89d4e4: add             x1, x2, #1
    // 0x89d4e8: ldur            x2, [fp, #-0x28]
    // 0x89d4ec: b               #0x89d3b4
    // 0x89d4f0: ldur            x2, [fp, #-0x20]
    // 0x89d4f4: add             x3, x2, #1
    // 0x89d4f8: r0 = BoxInt64Instr(r3)
    //     0x89d4f8: sbfiz           x0, x3, #1, #0x1f
    //     0x89d4fc: cmp             x3, x0, asr #1
    //     0x89d500: b.eq            #0x89d50c
    //     0x89d504: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x89d508: stur            x3, [x0, #7]
    // 0x89d50c: ldur            x1, [fp, #-8]
    // 0x89d510: mov             x3, x0
    // 0x89d514: r2 = Null
    //     0x89d514: mov             x2, NULL
    // 0x89d518: r0 = []=()
    //     0x89d518: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x89d51c: ldur            x0, [fp, #-0x10]
    // 0x89d520: LeaveFrame
    //     0x89d520: mov             SP, fp
    //     0x89d524: ldp             fp, lr, [SP], #0x10
    // 0x89d528: ret
    //     0x89d528: ret             
    // 0x89d52c: r0 = BoxInt64Instr(r2)
    //     0x89d52c: sbfiz           x0, x2, #1, #0x1f
    //     0x89d530: cmp             x2, x0, asr #1
    //     0x89d534: b.eq            #0x89d540
    //     0x89d538: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x89d53c: stur            x2, [x0, #7]
    // 0x89d540: ldur            x1, [fp, #-8]
    // 0x89d544: mov             x3, x0
    // 0x89d548: r2 = Null
    //     0x89d548: mov             x2, NULL
    // 0x89d54c: r0 = []=()
    //     0x89d54c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x89d550: r0 = Null
    //     0x89d550: mov             x0, NULL
    // 0x89d554: LeaveFrame
    //     0x89d554: mov             SP, fp
    //     0x89d558: ldp             fp, lr, [SP], #0x10
    // 0x89d55c: ret
    //     0x89d55c: ret             
    // 0x89d560: ldur            x1, [fp, #-8]
    // 0x89d564: ldur            x2, [fp, #-0x18]
    // 0x89d568: r0 = []()
    //     0x89d568: bl              #0xd82734  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]
    // 0x89d56c: LeaveFrame
    //     0x89d56c: mov             SP, fp
    //     0x89d570: ldp             fp, lr, [SP], #0x10
    // 0x89d574: ret
    //     0x89d574: ret             
    // 0x89d578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89d578: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89d57c: b               #0x89d34c
    // 0x89d580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x89d580: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x89d584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89d584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89d588: b               #0x89d3c4
  }
  _ SliverChildListDelegate(/* No info */) {
    // ** addr: 0xa07d8c, size: 0xbc
    // 0xa07d8c: EnterFrame
    //     0xa07d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xa07d90: mov             fp, SP
    // 0xa07d94: AllocStack(0x18)
    //     0xa07d94: sub             SP, SP, #0x18
    // 0xa07d98: r4 = true
    //     0xa07d98: add             x4, NULL, #0x20  ; true
    // 0xa07d9c: r3 = Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static.
    //     0xa07d9c: add             x3, PP, #0x26, lsl #12  ; [pp+0x26ef8] Closure: (Widget, int) => int from Function '_kDefaultSemanticIndexCallback@329070758': static. (0x7e54fb8bd554)
    //     0xa07da0: ldr             x3, [x3, #0xef8]
    // 0xa07da4: mov             x5, x1
    // 0xa07da8: mov             x0, x2
    // 0xa07dac: stur            x1, [fp, #-8]
    // 0xa07db0: CheckStackOverflow
    //     0xa07db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa07db4: cmp             SP, x16
    //     0xa07db8: b.ls            #0xa07e40
    // 0xa07dbc: StoreField: r5->field_1f = r0
    //     0xa07dbc: stur            w0, [x5, #0x1f]
    //     0xa07dc0: ldurb           w16, [x5, #-1]
    //     0xa07dc4: ldurb           w17, [x0, #-1]
    //     0xa07dc8: and             x16, x17, x16, lsr #2
    //     0xa07dcc: tst             x16, HEAP, lsr #32
    //     0xa07dd0: b.eq            #0xa07dd8
    //     0xa07dd4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xa07dd8: StoreField: r5->field_7 = r4
    //     0xa07dd8: stur            w4, [x5, #7]
    // 0xa07ddc: StoreField: r5->field_b = r4
    //     0xa07ddc: stur            w4, [x5, #0xb]
    // 0xa07de0: StoreField: r5->field_f = r4
    //     0xa07de0: stur            w4, [x5, #0xf]
    // 0xa07de4: StoreField: r5->field_1b = r3
    //     0xa07de4: stur            w3, [x5, #0x1b]
    // 0xa07de8: StoreField: r5->field_13 = rZR
    //     0xa07de8: stur            xzr, [x5, #0x13]
    // 0xa07dec: r1 = Null
    //     0xa07dec: mov             x1, NULL
    // 0xa07df0: r2 = 4
    //     0xa07df0: movz            x2, #0x4
    // 0xa07df4: r0 = AllocateArray()
    //     0xa07df4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa07df8: StoreField: r0->field_f = rNULL
    //     0xa07df8: stur            NULL, [x0, #0xf]
    // 0xa07dfc: StoreField: r0->field_13 = rZR
    //     0xa07dfc: stur            wzr, [x0, #0x13]
    // 0xa07e00: r16 = <Key?, int>
    //     0xa07e00: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d8] TypeArguments: <Key?, int>
    //     0xa07e04: ldr             x16, [x16, #0x3d8]
    // 0xa07e08: stp             x0, x16, [SP]
    // 0xa07e0c: r0 = Map._fromLiteral()
    //     0xa07e0c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa07e10: ldur            x1, [fp, #-8]
    // 0xa07e14: StoreField: r1->field_23 = r0
    //     0xa07e14: stur            w0, [x1, #0x23]
    //     0xa07e18: ldurb           w16, [x1, #-1]
    //     0xa07e1c: ldurb           w17, [x0, #-1]
    //     0xa07e20: and             x16, x17, x16, lsr #2
    //     0xa07e24: tst             x16, HEAP, lsr #32
    //     0xa07e28: b.eq            #0xa07e30
    //     0xa07e2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa07e30: r0 = Null
    //     0xa07e30: mov             x0, NULL
    // 0xa07e34: LeaveFrame
    //     0xa07e34: mov             SP, fp
    //     0xa07e38: ldp             fp, lr, [SP], #0x10
    // 0xa07e3c: ret
    //     0xa07e3c: ret             
    // 0xa07e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa07e40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa07e44: b               #0xa07dbc
  }
  _ build(/* No info */) {
    // ** addr: 0xda88c8, size: 0x1b0
    // 0xda88c8: EnterFrame
    //     0xda88c8: stp             fp, lr, [SP, #-0x10]!
    //     0xda88cc: mov             fp, SP
    // 0xda88d0: AllocStack(0x30)
    //     0xda88d0: sub             SP, SP, #0x30
    // 0xda88d4: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xda88d4: stur            x3, [fp, #-0x10]
    // 0xda88d8: CheckStackOverflow
    //     0xda88d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda88dc: cmp             SP, x16
    //     0xda88e0: b.ls            #0xda8a6c
    // 0xda88e4: tbnz            x3, #0x3f, #0xda8920
    // 0xda88e8: LoadField: r2 = r1->field_1f
    //     0xda88e8: ldur            w2, [x1, #0x1f]
    // 0xda88ec: DecompressPointer r2
    //     0xda88ec: add             x2, x2, HEAP, lsl #32
    // 0xda88f0: stur            x2, [fp, #-8]
    // 0xda88f4: r0 = LoadClassIdInstr(r2)
    //     0xda88f4: ldur            x0, [x2, #-1]
    //     0xda88f8: ubfx            x0, x0, #0xc, #0x14
    // 0xda88fc: str             x2, [SP]
    // 0xda8900: r0 = GDT[cid_x0 + 0xc834]()
    //     0xda8900: movz            x17, #0xc834
    //     0xda8904: add             lr, x0, x17
    //     0xda8908: ldr             lr, [x21, lr, lsl #3]
    //     0xda890c: blr             lr
    // 0xda8910: r1 = LoadInt32Instr(r0)
    //     0xda8910: sbfx            x1, x0, #1, #0x1f
    // 0xda8914: ldur            x2, [fp, #-0x10]
    // 0xda8918: cmp             x2, x1
    // 0xda891c: b.lt            #0xda8930
    // 0xda8920: r0 = Null
    //     0xda8920: mov             x0, NULL
    // 0xda8924: LeaveFrame
    //     0xda8924: mov             SP, fp
    //     0xda8928: ldp             fp, lr, [SP], #0x10
    // 0xda892c: ret
    //     0xda892c: ret             
    // 0xda8930: ldur            x3, [fp, #-8]
    // 0xda8934: r0 = BoxInt64Instr(r2)
    //     0xda8934: sbfiz           x0, x2, #1, #0x1f
    //     0xda8938: cmp             x2, x0, asr #1
    //     0xda893c: b.eq            #0xda8948
    //     0xda8940: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xda8944: stur            x2, [x0, #7]
    // 0xda8948: r1 = LoadClassIdInstr(r3)
    //     0xda8948: ldur            x1, [x3, #-1]
    //     0xda894c: ubfx            x1, x1, #0xc, #0x14
    // 0xda8950: stp             x0, x3, [SP]
    // 0xda8954: mov             x0, x1
    // 0xda8958: r0 = GDT[cid_x0 + 0x13037]()
    //     0xda8958: movz            x17, #0x3037
    //     0xda895c: movk            x17, #0x1, lsl #16
    //     0xda8960: add             lr, x0, x17
    //     0xda8964: ldr             lr, [x21, lr, lsl #3]
    //     0xda8968: blr             lr
    // 0xda896c: mov             x2, x0
    // 0xda8970: stur            x2, [fp, #-8]
    // 0xda8974: r0 = LoadClassIdInstr(r2)
    //     0xda8974: ldur            x0, [x2, #-1]
    //     0xda8978: ubfx            x0, x0, #0xc, #0x14
    // 0xda897c: mov             x1, x2
    // 0xda8980: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0xda8980: movz            x17, #0xaa0c
    //     0xda8984: add             lr, x0, x17
    //     0xda8988: ldr             lr, [x21, lr, lsl #3]
    //     0xda898c: blr             lr
    // 0xda8990: cmp             w0, NULL
    // 0xda8994: b.eq            #0xda89e4
    // 0xda8998: ldur            x2, [fp, #-8]
    // 0xda899c: r0 = LoadClassIdInstr(r2)
    //     0xda899c: ldur            x0, [x2, #-1]
    //     0xda89a0: ubfx            x0, x0, #0xc, #0x14
    // 0xda89a4: mov             x1, x2
    // 0xda89a8: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0xda89a8: movz            x17, #0xaa0c
    //     0xda89ac: add             lr, x0, x17
    //     0xda89b0: ldr             lr, [x21, lr, lsl #3]
    //     0xda89b4: blr             lr
    // 0xda89b8: stur            x0, [fp, #-0x18]
    // 0xda89bc: cmp             w0, NULL
    // 0xda89c0: b.eq            #0xda8a74
    // 0xda89c4: r1 = <Key>
    //     0xda89c4: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fbe0] TypeArguments: <Key>
    //     0xda89c8: ldr             x1, [x1, #0xbe0]
    // 0xda89cc: r0 = _SaltedValueKey()
    //     0xda89cc: bl              #0xda8808  ; Allocate_SaltedValueKeyStub -> _SaltedValueKey (size=0x10)
    // 0xda89d0: mov             x1, x0
    // 0xda89d4: ldur            x0, [fp, #-0x18]
    // 0xda89d8: StoreField: r1->field_b = r0
    //     0xda89d8: stur            w0, [x1, #0xb]
    // 0xda89dc: mov             x2, x1
    // 0xda89e0: b               #0xda89e8
    // 0xda89e4: r2 = Null
    //     0xda89e4: mov             x2, NULL
    // 0xda89e8: ldur            x1, [fp, #-0x10]
    // 0xda89ec: ldur            x0, [fp, #-8]
    // 0xda89f0: stur            x2, [fp, #-0x18]
    // 0xda89f4: r0 = RepaintBoundary()
    //     0xda89f4: bl              #0x9dae3c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xda89f8: mov             x1, x0
    // 0xda89fc: ldur            x0, [fp, #-8]
    // 0xda8a00: stur            x1, [fp, #-0x20]
    // 0xda8a04: StoreField: r1->field_b = r0
    //     0xda8a04: stur            w0, [x1, #0xb]
    // 0xda8a08: r0 = IndexedSemantics()
    //     0xda8a08: bl              #0xa4bbd8  ; AllocateIndexedSemanticsStub -> IndexedSemantics (size=0x18)
    // 0xda8a0c: mov             x1, x0
    // 0xda8a10: ldur            x0, [fp, #-0x10]
    // 0xda8a14: stur            x1, [fp, #-8]
    // 0xda8a18: StoreField: r1->field_f = r0
    //     0xda8a18: stur            x0, [x1, #0xf]
    // 0xda8a1c: ldur            x0, [fp, #-0x20]
    // 0xda8a20: StoreField: r1->field_b = r0
    //     0xda8a20: stur            w0, [x1, #0xb]
    // 0xda8a24: r0 = _SelectionKeepAlive()
    //     0xda8a24: bl              #0xda87fc  ; Allocate_SelectionKeepAliveStub -> _SelectionKeepAlive (size=0x10)
    // 0xda8a28: mov             x1, x0
    // 0xda8a2c: ldur            x0, [fp, #-8]
    // 0xda8a30: stur            x1, [fp, #-0x20]
    // 0xda8a34: StoreField: r1->field_b = r0
    //     0xda8a34: stur            w0, [x1, #0xb]
    // 0xda8a38: r0 = AutomaticKeepAlive()
    //     0xda8a38: bl              #0xda87f0  ; AllocateAutomaticKeepAliveStub -> AutomaticKeepAlive (size=0x10)
    // 0xda8a3c: mov             x1, x0
    // 0xda8a40: ldur            x0, [fp, #-0x20]
    // 0xda8a44: stur            x1, [fp, #-8]
    // 0xda8a48: StoreField: r1->field_b = r0
    //     0xda8a48: stur            w0, [x1, #0xb]
    // 0xda8a4c: r0 = KeyedSubtree()
    //     0xda8a4c: bl              #0x939bd4  ; AllocateKeyedSubtreeStub -> KeyedSubtree (size=0x10)
    // 0xda8a50: ldur            x1, [fp, #-8]
    // 0xda8a54: StoreField: r0->field_b = r1
    //     0xda8a54: stur            w1, [x0, #0xb]
    // 0xda8a58: ldur            x1, [fp, #-0x18]
    // 0xda8a5c: StoreField: r0->field_7 = r1
    //     0xda8a5c: stur            w1, [x0, #7]
    // 0xda8a60: LeaveFrame
    //     0xda8a60: mov             SP, fp
    //     0xda8a64: ldp             fp, lr, [SP], #0x10
    // 0xda8a68: ret
    //     0xda8a68: ret             
    // 0xda8a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda8a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda8a70: b               #0xda88e4
    // 0xda8a74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8a74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2584, size: 0x2c, field offset: 0x8
//   const constructor, 
class SliverChildBuilderDelegate extends SliverChildDelegate {

  _ build(/* No info */) {
    // ** addr: 0xda8588, size: 0x268
    // 0xda8588: EnterFrame
    //     0xda8588: stp             fp, lr, [SP, #-0x10]!
    //     0xda858c: mov             fp, SP
    // 0xda8590: AllocStack(0xb8)
    //     0xda8590: sub             SP, SP, #0xb8
    // 0xda8594: SetupParameters(SliverChildBuilderDelegate this /* r1 => r4, fp-0x88 */, dynamic _ /* r3 => r3, fp-0x90 */)
    //     0xda8594: mov             x4, x1
    //     0xda8598: stur            x1, [fp, #-0x88]
    //     0xda859c: stur            x3, [fp, #-0x90]
    // 0xda85a0: CheckStackOverflow
    //     0xda85a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xda85a4: cmp             SP, x16
    //     0xda85a8: b.ls            #0xda87e4
    // 0xda85ac: tbnz            x3, #0x3f, #0xda85d4
    // 0xda85b0: LoadField: r0 = r4->field_b
    //     0xda85b0: ldur            w0, [x4, #0xb]
    // 0xda85b4: DecompressPointer r0
    //     0xda85b4: add             x0, x0, HEAP, lsl #32
    // 0xda85b8: cmp             w0, NULL
    // 0xda85bc: b.eq            #0xda85e4
    // 0xda85c0: r1 = LoadInt32Instr(r0)
    //     0xda85c0: sbfx            x1, x0, #1, #0x1f
    //     0xda85c4: tbz             w0, #0, #0xda85cc
    //     0xda85c8: ldur            x1, [x0, #7]
    // 0xda85cc: cmp             x3, x1
    // 0xda85d0: b.lt            #0xda85e4
    // 0xda85d4: r0 = Null
    //     0xda85d4: mov             x0, NULL
    // 0xda85d8: LeaveFrame
    //     0xda85d8: mov             SP, fp
    //     0xda85dc: ldp             fp, lr, [SP], #0x10
    // 0xda85e0: ret
    //     0xda85e0: ret             
    // 0xda85e4: LoadField: r5 = r4->field_7
    //     0xda85e4: ldur            w5, [x4, #7]
    // 0xda85e8: DecompressPointer r5
    //     0xda85e8: add             x5, x5, HEAP, lsl #32
    // 0xda85ec: stur            x5, [fp, #-0x80]
    // 0xda85f0: r0 = BoxInt64Instr(r3)
    //     0xda85f0: sbfiz           x0, x3, #1, #0x1f
    //     0xda85f4: cmp             x3, x0, asr #1
    //     0xda85f8: b.eq            #0xda8604
    //     0xda85fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xda8600: stur            x3, [x0, #7]
    // 0xda8604: mov             x1, x0
    // 0xda8608: stur            x1, [fp, #-0x78]
    // 0xda860c: stp             x2, x5, [SP, #8]
    // 0xda8610: str             x1, [SP]
    // 0xda8614: mov             x0, x5
    // 0xda8618: ClosureCall
    //     0xda8618: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xda861c: ldur            x2, [x0, #0x1f]
    //     0xda8620: blr             x2
    // 0xda8624: ldur            x4, [fp, #-0x88]
    // 0xda8628: ldur            x3, [fp, #-0x78]
    // 0xda862c: mov             x2, x0
    // 0xda8630: b               #0xda8658
    // 0xda8634: sub             SP, fp, #0xb8
    // 0xda8638: mov             x2, x1
    // 0xda863c: mov             x1, x0
    // 0xda8640: r0 = _createErrorWidget()
    //     0xda8640: bl              #0xda8814  ; [package:flutter/src/widgets/scroll_delegate.dart] ::_createErrorWidget
    // 0xda8644: mov             x2, x0
    // 0xda8648: ldur            x1, [fp, #-0x60]
    // 0xda864c: ldur            x0, [fp, #-0x70]
    // 0xda8650: mov             x4, x1
    // 0xda8654: mov             x3, x0
    // 0xda8658: stur            x4, [fp, #-0x78]
    // 0xda865c: stur            x3, [fp, #-0x80]
    // 0xda8660: stur            x2, [fp, #-0x88]
    // 0xda8664: cmp             w2, NULL
    // 0xda8668: b.ne            #0xda867c
    // 0xda866c: r0 = Null
    //     0xda866c: mov             x0, NULL
    // 0xda8670: LeaveFrame
    //     0xda8670: mov             SP, fp
    //     0xda8674: ldp             fp, lr, [SP], #0x10
    // 0xda8678: ret
    //     0xda8678: ret             
    // 0xda867c: r0 = LoadClassIdInstr(r2)
    //     0xda867c: ldur            x0, [x2, #-1]
    //     0xda8680: ubfx            x0, x0, #0xc, #0x14
    // 0xda8684: mov             x1, x2
    // 0xda8688: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0xda8688: movz            x17, #0xaa0c
    //     0xda868c: add             lr, x0, x17
    //     0xda8690: ldr             lr, [x21, lr, lsl #3]
    //     0xda8694: blr             lr
    // 0xda8698: cmp             w0, NULL
    // 0xda869c: b.eq            #0xda86ec
    // 0xda86a0: ldur            x2, [fp, #-0x88]
    // 0xda86a4: r0 = LoadClassIdInstr(r2)
    //     0xda86a4: ldur            x0, [x2, #-1]
    //     0xda86a8: ubfx            x0, x0, #0xc, #0x14
    // 0xda86ac: mov             x1, x2
    // 0xda86b0: r0 = GDT[cid_x0 + 0xaa0c]()
    //     0xda86b0: movz            x17, #0xaa0c
    //     0xda86b4: add             lr, x0, x17
    //     0xda86b8: ldr             lr, [x21, lr, lsl #3]
    //     0xda86bc: blr             lr
    // 0xda86c0: stur            x0, [fp, #-0x98]
    // 0xda86c4: cmp             w0, NULL
    // 0xda86c8: b.eq            #0xda87ec
    // 0xda86cc: r1 = <Key>
    //     0xda86cc: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4fbe0] TypeArguments: <Key>
    //     0xda86d0: ldr             x1, [x1, #0xbe0]
    // 0xda86d4: r0 = _SaltedValueKey()
    //     0xda86d4: bl              #0xda8808  ; Allocate_SaltedValueKeyStub -> _SaltedValueKey (size=0x10)
    // 0xda86d8: mov             x1, x0
    // 0xda86dc: ldur            x0, [fp, #-0x98]
    // 0xda86e0: StoreField: r1->field_b = r0
    //     0xda86e0: stur            w0, [x1, #0xb]
    // 0xda86e4: mov             x2, x1
    // 0xda86e8: b               #0xda86f0
    // 0xda86ec: r2 = Null
    //     0xda86ec: mov             x2, NULL
    // 0xda86f0: ldur            x1, [fp, #-0x78]
    // 0xda86f4: ldur            x0, [fp, #-0x88]
    // 0xda86f8: stur            x2, [fp, #-0x98]
    // 0xda86fc: r0 = RepaintBoundary()
    //     0xda86fc: bl              #0x9dae3c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xda8700: mov             x1, x0
    // 0xda8704: ldur            x0, [fp, #-0x88]
    // 0xda8708: stur            x1, [fp, #-0xa0]
    // 0xda870c: StoreField: r1->field_b = r0
    //     0xda870c: stur            w0, [x1, #0xb]
    // 0xda8710: ldur            x0, [fp, #-0x78]
    // 0xda8714: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xda8714: ldur            w2, [x0, #0x17]
    // 0xda8718: DecompressPointer r2
    //     0xda8718: add             x2, x2, HEAP, lsl #32
    // 0xda871c: tbnz            w2, #4, #0xda878c
    // 0xda8720: LoadField: r2 = r0->field_23
    //     0xda8720: ldur            w2, [x0, #0x23]
    // 0xda8724: DecompressPointer r2
    //     0xda8724: add             x2, x2, HEAP, lsl #32
    // 0xda8728: stp             x1, x2, [SP, #8]
    // 0xda872c: ldur            x16, [fp, #-0x80]
    // 0xda8730: str             x16, [SP]
    // 0xda8734: mov             x0, x2
    // 0xda8738: ClosureCall
    //     0xda8738: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xda873c: ldur            x2, [x0, #0x1f]
    //     0xda8740: blr             x2
    // 0xda8744: cmp             w0, NULL
    // 0xda8748: b.eq            #0xda8780
    // 0xda874c: ldur            x1, [fp, #-0xa0]
    // 0xda8750: r2 = LoadInt32Instr(r0)
    //     0xda8750: sbfx            x2, x0, #1, #0x1f
    //     0xda8754: tbz             w0, #0, #0xda875c
    //     0xda8758: ldur            x2, [x0, #7]
    // 0xda875c: stur            x2, [fp, #-0x90]
    // 0xda8760: r0 = IndexedSemantics()
    //     0xda8760: bl              #0xa4bbd8  ; AllocateIndexedSemanticsStub -> IndexedSemantics (size=0x18)
    // 0xda8764: mov             x1, x0
    // 0xda8768: ldur            x0, [fp, #-0x90]
    // 0xda876c: StoreField: r1->field_f = r0
    //     0xda876c: stur            x0, [x1, #0xf]
    // 0xda8770: ldur            x0, [fp, #-0xa0]
    // 0xda8774: StoreField: r1->field_b = r0
    //     0xda8774: stur            w0, [x1, #0xb]
    // 0xda8778: mov             x0, x1
    // 0xda877c: b               #0xda8784
    // 0xda8780: ldur            x0, [fp, #-0xa0]
    // 0xda8784: mov             x1, x0
    // 0xda8788: b               #0xda8794
    // 0xda878c: mov             x0, x1
    // 0xda8790: mov             x1, x0
    // 0xda8794: ldur            x0, [fp, #-0x98]
    // 0xda8798: stur            x1, [fp, #-0x78]
    // 0xda879c: r0 = _SelectionKeepAlive()
    //     0xda879c: bl              #0xda87fc  ; Allocate_SelectionKeepAliveStub -> _SelectionKeepAlive (size=0x10)
    // 0xda87a0: mov             x1, x0
    // 0xda87a4: ldur            x0, [fp, #-0x78]
    // 0xda87a8: stur            x1, [fp, #-0x80]
    // 0xda87ac: StoreField: r1->field_b = r0
    //     0xda87ac: stur            w0, [x1, #0xb]
    // 0xda87b0: r0 = AutomaticKeepAlive()
    //     0xda87b0: bl              #0xda87f0  ; AllocateAutomaticKeepAliveStub -> AutomaticKeepAlive (size=0x10)
    // 0xda87b4: mov             x1, x0
    // 0xda87b8: ldur            x0, [fp, #-0x80]
    // 0xda87bc: stur            x1, [fp, #-0x78]
    // 0xda87c0: StoreField: r1->field_b = r0
    //     0xda87c0: stur            w0, [x1, #0xb]
    // 0xda87c4: r0 = KeyedSubtree()
    //     0xda87c4: bl              #0x939bd4  ; AllocateKeyedSubtreeStub -> KeyedSubtree (size=0x10)
    // 0xda87c8: ldur            x1, [fp, #-0x78]
    // 0xda87cc: StoreField: r0->field_b = r1
    //     0xda87cc: stur            w1, [x0, #0xb]
    // 0xda87d0: ldur            x1, [fp, #-0x98]
    // 0xda87d4: StoreField: r0->field_7 = r1
    //     0xda87d4: stur            w1, [x0, #7]
    // 0xda87d8: LeaveFrame
    //     0xda87d8: mov             SP, fp
    //     0xda87dc: ldp             fp, lr, [SP], #0x10
    // 0xda87e0: ret
    //     0xda87e0: ret             
    // 0xda87e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xda87e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xda87e8: b               #0xda85ac
    // 0xda87ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda87ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3580, size: 0x10, field offset: 0x10
//   const constructor, 
class _SaltedValueKey extends ValueKey<dynamic> {
}

// class id: 4178, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class __SelectionKeepAliveState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x92ba40, size: 0x40
    // 0x92ba40: EnterFrame
    //     0x92ba40: stp             fp, lr, [SP, #-0x10]!
    //     0x92ba44: mov             fp, SP
    // 0x92ba48: CheckStackOverflow
    //     0x92ba48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92ba4c: cmp             SP, x16
    //     0x92ba50: b.ls            #0x92ba78
    // 0x92ba54: LoadField: r0 = r1->field_13
    //     0x92ba54: ldur            w0, [x1, #0x13]
    // 0x92ba58: DecompressPointer r0
    //     0x92ba58: add             x0, x0, HEAP, lsl #32
    // 0x92ba5c: cmp             w0, NULL
    // 0x92ba60: b.eq            #0x92ba68
    // 0x92ba64: r0 = _releaseKeepAlive()
    //     0x92ba64: bl              #0x92b178  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x92ba68: r0 = Null
    //     0x92ba68: mov             x0, NULL
    // 0x92ba6c: LeaveFrame
    //     0x92ba6c: mov             SP, fp
    //     0x92ba70: ldp             fp, lr, [SP], #0x10
    // 0x92ba74: ret
    //     0x92ba74: ret             
    // 0x92ba78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ba78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ba7c: b               #0x92ba54
  }
  _ initState(/* No info */) {
    // ** addr: 0x945d2c, size: 0x3c
    // 0x945d2c: EnterFrame
    //     0x945d2c: stp             fp, lr, [SP, #-0x10]!
    //     0x945d30: mov             fp, SP
    // 0x945d34: CheckStackOverflow
    //     0x945d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945d38: cmp             SP, x16
    //     0x945d3c: b.ls            #0x945d60
    // 0x945d40: LoadField: r0 = r1->field_23
    //     0x945d40: ldur            w0, [x1, #0x23]
    // 0x945d44: DecompressPointer r0
    //     0x945d44: add             x0, x0, HEAP, lsl #32
    // 0x945d48: tbnz            w0, #4, #0x945d50
    // 0x945d4c: r0 = _ensureKeepAlive()
    //     0x945d4c: bl              #0x933d08  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x945d50: r0 = Null
    //     0x945d50: mov             x0, NULL
    // 0x945d54: LeaveFrame
    //     0x945d54: mov             SP, fp
    //     0x945d58: ldp             fp, lr, [SP], #0x10
    // 0x945d5c: ret
    //     0x945d5c: ret             
    // 0x945d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945d64: b               #0x945d40
  }
  _ build(/* No info */) {
    // ** addr: 0xa1dedc, size: 0x50
    // 0xa1dedc: EnterFrame
    //     0xa1dedc: stp             fp, lr, [SP, #-0x10]!
    //     0xa1dee0: mov             fp, SP
    // 0xa1dee4: CheckStackOverflow
    //     0xa1dee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1dee8: cmp             SP, x16
    //     0xa1deec: b.ls            #0xa1df24
    // 0xa1def0: LoadField: r0 = r1->field_23
    //     0xa1def0: ldur            w0, [x1, #0x23]
    // 0xa1def4: DecompressPointer r0
    //     0xa1def4: add             x0, x0, HEAP, lsl #32
    // 0xa1def8: tbnz            w0, #4, #0xa1df10
    // 0xa1defc: LoadField: r0 = r1->field_13
    //     0xa1defc: ldur            w0, [x1, #0x13]
    // 0xa1df00: DecompressPointer r0
    //     0xa1df00: add             x0, x0, HEAP, lsl #32
    // 0xa1df04: cmp             w0, NULL
    // 0xa1df08: b.ne            #0xa1df10
    // 0xa1df0c: r0 = _ensureKeepAlive()
    //     0xa1df0c: bl              #0x933d08  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xa1df10: r0 = Instance__NullWidget
    //     0xa1df10: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d8] Obj!_NullWidget@e25381
    //     0xa1df14: ldr             x0, [x0, #0x7d8]
    // 0xa1df18: LeaveFrame
    //     0xa1df18: mov             SP, fp
    //     0xa1df1c: ldp             fp, lr, [SP], #0x10
    // 0xa1df20: ret
    //     0xa1df20: ret             
    // 0xa1df24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1df24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1df28: b               #0xa1def0
  }
  _ updateKeepAlive(/* No info */) {
    // ** addr: 0xd14548, size: 0x64
    // 0xd14548: EnterFrame
    //     0xd14548: stp             fp, lr, [SP, #-0x10]!
    //     0xd1454c: mov             fp, SP
    // 0xd14550: CheckStackOverflow
    //     0xd14550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd14554: cmp             SP, x16
    //     0xd14558: b.ls            #0xd145a4
    // 0xd1455c: LoadField: r0 = r1->field_23
    //     0xd1455c: ldur            w0, [x1, #0x23]
    // 0xd14560: DecompressPointer r0
    //     0xd14560: add             x0, x0, HEAP, lsl #32
    // 0xd14564: tbnz            w0, #4, #0xd14580
    // 0xd14568: LoadField: r0 = r1->field_13
    //     0xd14568: ldur            w0, [x1, #0x13]
    // 0xd1456c: DecompressPointer r0
    //     0xd1456c: add             x0, x0, HEAP, lsl #32
    // 0xd14570: cmp             w0, NULL
    // 0xd14574: b.ne            #0xd14594
    // 0xd14578: r0 = _ensureKeepAlive()
    //     0xd14578: bl              #0x933d08  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xd1457c: b               #0xd14594
    // 0xd14580: LoadField: r0 = r1->field_13
    //     0xd14580: ldur            w0, [x1, #0x13]
    // 0xd14584: DecompressPointer r0
    //     0xd14584: add             x0, x0, HEAP, lsl #32
    // 0xd14588: cmp             w0, NULL
    // 0xd1458c: b.eq            #0xd14594
    // 0xd14590: r0 = _releaseKeepAlive()
    //     0xd14590: bl              #0x92b178  ; [package:flutter/src/widgets/editable_text.dart] _EditableTextState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0xd14594: r0 = Null
    //     0xd14594: mov             x0, NULL
    // 0xd14598: LeaveFrame
    //     0xd14598: mov             SP, fp
    //     0xd1459c: ldp             fp, lr, [SP], #0x10
    // 0xd145a0: ret
    //     0xd145a0: ret             
    // 0xd145a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd145a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd145a8: b               #0xd1455c
  }
}

// class id: 4179, size: 0x28, field offset: 0x18
class _SelectionKeepAliveState extends __SelectionKeepAliveState&State&AutomaticKeepAliveClientMixin
    implements SelectionRegistrar {

  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9a7fa4, size: 0x1a4
    // 0x9a7fa4: EnterFrame
    //     0x9a7fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a7fa8: mov             fp, SP
    // 0x9a7fac: AllocStack(0x38)
    //     0x9a7fac: sub             SP, SP, #0x38
    // 0x9a7fb0: SetupParameters(_SelectionKeepAliveState this /* r1 => r0, fp-0x8 */)
    //     0x9a7fb0: mov             x0, x1
    //     0x9a7fb4: stur            x1, [fp, #-8]
    // 0x9a7fb8: CheckStackOverflow
    //     0x9a7fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a7fbc: cmp             SP, x16
    //     0x9a7fc0: b.ls            #0x9a813c
    // 0x9a7fc4: LoadField: r1 = r0->field_f
    //     0x9a7fc4: ldur            w1, [x0, #0xf]
    // 0x9a7fc8: DecompressPointer r1
    //     0x9a7fc8: add             x1, x1, HEAP, lsl #32
    // 0x9a7fcc: cmp             w1, NULL
    // 0x9a7fd0: b.eq            #0x9a8144
    // 0x9a7fd4: r0 = maybeOf()
    //     0x9a7fd4: bl              #0x9a8148  ; [package:flutter/src/widgets/selection_container.dart] SelectionContainer::maybeOf
    // 0x9a7fd8: mov             x2, x0
    // 0x9a7fdc: ldur            x1, [fp, #-8]
    // 0x9a7fe0: stur            x2, [fp, #-0x10]
    // 0x9a7fe4: LoadField: r0 = r1->field_1f
    //     0x9a7fe4: ldur            w0, [x1, #0x1f]
    // 0x9a7fe8: DecompressPointer r0
    //     0x9a7fe8: add             x0, x0, HEAP, lsl #32
    // 0x9a7fec: r3 = LoadClassIdInstr(r0)
    //     0x9a7fec: ldur            x3, [x0, #-1]
    //     0x9a7ff0: ubfx            x3, x3, #0xc, #0x14
    // 0x9a7ff4: stp             x2, x0, [SP]
    // 0x9a7ff8: mov             x0, x3
    // 0x9a7ffc: mov             lr, x0
    // 0x9a8000: ldr             lr, [x21, lr, lsl #3]
    // 0x9a8004: blr             lr
    // 0x9a8008: tbz             w0, #4, #0x9a812c
    // 0x9a800c: ldur            x0, [fp, #-8]
    // 0x9a8010: LoadField: r2 = r0->field_1f
    //     0x9a8010: ldur            w2, [x0, #0x1f]
    // 0x9a8014: DecompressPointer r2
    //     0x9a8014: add             x2, x2, HEAP, lsl #32
    // 0x9a8018: stur            x2, [fp, #-0x20]
    // 0x9a801c: cmp             w2, NULL
    // 0x9a8020: b.eq            #0x9a8090
    // 0x9a8024: LoadField: r3 = r0->field_1b
    //     0x9a8024: ldur            w3, [x0, #0x1b]
    // 0x9a8028: DecompressPointer r3
    //     0x9a8028: add             x3, x3, HEAP, lsl #32
    // 0x9a802c: stur            x3, [fp, #-0x18]
    // 0x9a8030: cmp             w3, NULL
    // 0x9a8034: b.eq            #0x9a8090
    // 0x9a8038: LoadField: r1 = r3->field_7
    //     0x9a8038: ldur            w1, [x3, #7]
    // 0x9a803c: DecompressPointer r1
    //     0x9a803c: add             x1, x1, HEAP, lsl #32
    // 0x9a8040: r0 = _CompactIterable()
    //     0x9a8040: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x9a8044: mov             x1, x0
    // 0x9a8048: ldur            x0, [fp, #-0x18]
    // 0x9a804c: stur            x1, [fp, #-0x28]
    // 0x9a8050: StoreField: r1->field_b = r0
    //     0x9a8050: stur            w0, [x1, #0xb]
    // 0x9a8054: r2 = -2
    //     0x9a8054: orr             x2, xzr, #0xfffffffffffffffe
    // 0x9a8058: StoreField: r1->field_f = r2
    //     0x9a8058: stur            x2, [x1, #0xf]
    // 0x9a805c: r3 = 2
    //     0x9a805c: movz            x3, #0x2
    // 0x9a8060: ArrayStore: r1[0] = r3  ; List_8
    //     0x9a8060: stur            x3, [x1, #0x17]
    // 0x9a8064: ldur            x0, [fp, #-0x20]
    // 0x9a8068: r4 = LoadClassIdInstr(r0)
    //     0x9a8068: ldur            x4, [x0, #-1]
    //     0x9a806c: ubfx            x4, x4, #0xc, #0x14
    // 0x9a8070: str             x0, [SP]
    // 0x9a8074: mov             x0, x4
    // 0x9a8078: r0 = GDT[cid_x0 + 0xf6a]()
    //     0x9a8078: add             lr, x0, #0xf6a
    //     0x9a807c: ldr             lr, [x21, lr, lsl #3]
    //     0x9a8080: blr             lr
    // 0x9a8084: ldur            x1, [fp, #-0x28]
    // 0x9a8088: mov             x2, x0
    // 0x9a808c: r0 = forEach()
    //     0x9a808c: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0x9a8090: ldur            x1, [fp, #-8]
    // 0x9a8094: ldur            x2, [fp, #-0x10]
    // 0x9a8098: mov             x0, x2
    // 0x9a809c: StoreField: r1->field_1f = r0
    //     0x9a809c: stur            w0, [x1, #0x1f]
    //     0x9a80a0: ldurb           w16, [x1, #-1]
    //     0x9a80a4: ldurb           w17, [x0, #-1]
    //     0x9a80a8: and             x16, x17, x16, lsr #2
    //     0x9a80ac: tst             x16, HEAP, lsr #32
    //     0x9a80b0: b.eq            #0x9a80b8
    //     0x9a80b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9a80b8: cmp             w2, NULL
    // 0x9a80bc: b.eq            #0x9a812c
    // 0x9a80c0: LoadField: r0 = r1->field_1b
    //     0x9a80c0: ldur            w0, [x1, #0x1b]
    // 0x9a80c4: DecompressPointer r0
    //     0x9a80c4: add             x0, x0, HEAP, lsl #32
    // 0x9a80c8: stur            x0, [fp, #-0x18]
    // 0x9a80cc: cmp             w0, NULL
    // 0x9a80d0: b.eq            #0x9a812c
    // 0x9a80d4: LoadField: r1 = r0->field_7
    //     0x9a80d4: ldur            w1, [x0, #7]
    // 0x9a80d8: DecompressPointer r1
    //     0x9a80d8: add             x1, x1, HEAP, lsl #32
    // 0x9a80dc: r0 = _CompactIterable()
    //     0x9a80dc: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x9a80e0: mov             x1, x0
    // 0x9a80e4: ldur            x0, [fp, #-0x18]
    // 0x9a80e8: stur            x1, [fp, #-8]
    // 0x9a80ec: StoreField: r1->field_b = r0
    //     0x9a80ec: stur            w0, [x1, #0xb]
    // 0x9a80f0: r0 = -2
    //     0x9a80f0: orr             x0, xzr, #0xfffffffffffffffe
    // 0x9a80f4: StoreField: r1->field_f = r0
    //     0x9a80f4: stur            x0, [x1, #0xf]
    // 0x9a80f8: r0 = 2
    //     0x9a80f8: movz            x0, #0x2
    // 0x9a80fc: ArrayStore: r1[0] = r0  ; List_8
    //     0x9a80fc: stur            x0, [x1, #0x17]
    // 0x9a8100: ldur            x0, [fp, #-0x10]
    // 0x9a8104: r2 = LoadClassIdInstr(r0)
    //     0x9a8104: ldur            x2, [x0, #-1]
    //     0x9a8108: ubfx            x2, x2, #0xc, #0x14
    // 0x9a810c: str             x0, [SP]
    // 0x9a8110: mov             x0, x2
    // 0x9a8114: r0 = GDT[cid_x0 + 0xf5f]()
    //     0x9a8114: add             lr, x0, #0xf5f
    //     0x9a8118: ldr             lr, [x21, lr, lsl #3]
    //     0x9a811c: blr             lr
    // 0x9a8120: ldur            x1, [fp, #-8]
    // 0x9a8124: mov             x2, x0
    // 0x9a8128: r0 = forEach()
    //     0x9a8128: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0x9a812c: r0 = Null
    //     0x9a812c: mov             x0, NULL
    // 0x9a8130: LeaveFrame
    //     0x9a8130: mov             SP, fp
    //     0x9a8134: ldp             fp, lr, [SP], #0x10
    // 0x9a8138: ret
    //     0x9a8138: ret             
    // 0x9a813c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a813c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a8140: b               #0x9a7fc4
    // 0x9a8144: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a8144: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa1de24, size: 0xac
    // 0xa1de24: EnterFrame
    //     0xa1de24: stp             fp, lr, [SP, #-0x10]!
    //     0xa1de28: mov             fp, SP
    // 0xa1de2c: AllocStack(0x10)
    //     0xa1de2c: sub             SP, SP, #0x10
    // 0xa1de30: SetupParameters(_SelectionKeepAliveState this /* r1 => r0, fp-0x8 */)
    //     0xa1de30: mov             x0, x1
    //     0xa1de34: stur            x1, [fp, #-8]
    // 0xa1de38: CheckStackOverflow
    //     0xa1de38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1de3c: cmp             SP, x16
    //     0xa1de40: b.ls            #0xa1dec0
    // 0xa1de44: mov             x1, x0
    // 0xa1de48: r0 = build()
    //     0xa1de48: bl              #0xa1dedc  ; [package:flutter/src/widgets/scroll_delegate.dart] __SelectionKeepAliveState&State&AutomaticKeepAliveClientMixin::build
    // 0xa1de4c: ldur            x0, [fp, #-8]
    // 0xa1de50: LoadField: r1 = r0->field_1f
    //     0xa1de50: ldur            w1, [x0, #0x1f]
    // 0xa1de54: DecompressPointer r1
    //     0xa1de54: add             x1, x1, HEAP, lsl #32
    // 0xa1de58: cmp             w1, NULL
    // 0xa1de5c: b.ne            #0xa1de84
    // 0xa1de60: LoadField: r1 = r0->field_b
    //     0xa1de60: ldur            w1, [x0, #0xb]
    // 0xa1de64: DecompressPointer r1
    //     0xa1de64: add             x1, x1, HEAP, lsl #32
    // 0xa1de68: cmp             w1, NULL
    // 0xa1de6c: b.eq            #0xa1dec8
    // 0xa1de70: LoadField: r0 = r1->field_b
    //     0xa1de70: ldur            w0, [x1, #0xb]
    // 0xa1de74: DecompressPointer r0
    //     0xa1de74: add             x0, x0, HEAP, lsl #32
    // 0xa1de78: LeaveFrame
    //     0xa1de78: mov             SP, fp
    //     0xa1de7c: ldp             fp, lr, [SP], #0x10
    // 0xa1de80: ret
    //     0xa1de80: ret             
    // 0xa1de84: LoadField: r1 = r0->field_b
    //     0xa1de84: ldur            w1, [x0, #0xb]
    // 0xa1de88: DecompressPointer r1
    //     0xa1de88: add             x1, x1, HEAP, lsl #32
    // 0xa1de8c: cmp             w1, NULL
    // 0xa1de90: b.eq            #0xa1decc
    // 0xa1de94: LoadField: r2 = r1->field_b
    //     0xa1de94: ldur            w2, [x1, #0xb]
    // 0xa1de98: DecompressPointer r2
    //     0xa1de98: add             x2, x2, HEAP, lsl #32
    // 0xa1de9c: stur            x2, [fp, #-0x10]
    // 0xa1dea0: r0 = SelectionRegistrarScope()
    //     0xa1dea0: bl              #0xa1ded0  ; AllocateSelectionRegistrarScopeStub -> SelectionRegistrarScope (size=0x14)
    // 0xa1dea4: ldur            x1, [fp, #-8]
    // 0xa1dea8: StoreField: r0->field_f = r1
    //     0xa1dea8: stur            w1, [x0, #0xf]
    // 0xa1deac: ldur            x1, [fp, #-0x10]
    // 0xa1deb0: StoreField: r0->field_b = r1
    //     0xa1deb0: stur            w1, [x0, #0xb]
    // 0xa1deb4: LeaveFrame
    //     0xa1deb4: mov             SP, fp
    //     0xa1deb8: ldp             fp, lr, [SP], #0x10
    // 0xa1debc: ret
    //     0xa1debc: ret             
    // 0xa1dec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1dec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1dec4: b               #0xa1de44
    // 0xa1dec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1dec8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1decc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1decc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa80ce0, size: 0x1b8
    // 0xa80ce0: EnterFrame
    //     0xa80ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xa80ce4: mov             fp, SP
    // 0xa80ce8: AllocStack(0x28)
    //     0xa80ce8: sub             SP, SP, #0x28
    // 0xa80cec: SetupParameters(_SelectionKeepAliveState this /* r1 => r0, fp-0x10 */)
    //     0xa80cec: mov             x0, x1
    //     0xa80cf0: stur            x1, [fp, #-0x10]
    // 0xa80cf4: CheckStackOverflow
    //     0xa80cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa80cf8: cmp             SP, x16
    //     0xa80cfc: b.ls            #0xa80e7c
    // 0xa80d00: LoadField: r2 = r0->field_1b
    //     0xa80d00: ldur            w2, [x0, #0x1b]
    // 0xa80d04: DecompressPointer r2
    //     0xa80d04: add             x2, x2, HEAP, lsl #32
    // 0xa80d08: stur            x2, [fp, #-8]
    // 0xa80d0c: cmp             w2, NULL
    // 0xa80d10: b.eq            #0xa80e64
    // 0xa80d14: LoadField: r1 = r2->field_7
    //     0xa80d14: ldur            w1, [x2, #7]
    // 0xa80d18: DecompressPointer r1
    //     0xa80d18: add             x1, x1, HEAP, lsl #32
    // 0xa80d1c: r0 = _CompactIterable()
    //     0xa80d1c: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xa80d20: mov             x1, x0
    // 0xa80d24: ldur            x0, [fp, #-8]
    // 0xa80d28: StoreField: r1->field_b = r0
    //     0xa80d28: stur            w0, [x1, #0xb]
    // 0xa80d2c: r0 = -2
    //     0xa80d2c: orr             x0, xzr, #0xfffffffffffffffe
    // 0xa80d30: StoreField: r1->field_f = r0
    //     0xa80d30: stur            x0, [x1, #0xf]
    // 0xa80d34: r0 = 2
    //     0xa80d34: movz            x0, #0x2
    // 0xa80d38: ArrayStore: r1[0] = r0  ; List_8
    //     0xa80d38: stur            x0, [x1, #0x17]
    // 0xa80d3c: r0 = iterator()
    //     0xa80d3c: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xa80d40: stur            x0, [fp, #-0x18]
    // 0xa80d44: LoadField: r2 = r0->field_7
    //     0xa80d44: ldur            w2, [x0, #7]
    // 0xa80d48: DecompressPointer r2
    //     0xa80d48: add             x2, x2, HEAP, lsl #32
    // 0xa80d4c: stur            x2, [fp, #-8]
    // 0xa80d50: ldur            x3, [fp, #-0x10]
    // 0xa80d54: CheckStackOverflow
    //     0xa80d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa80d58: cmp             SP, x16
    //     0xa80d5c: b.ls            #0xa80e84
    // 0xa80d60: mov             x1, x0
    // 0xa80d64: r0 = moveNext()
    //     0xa80d64: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xa80d68: tbnz            w0, #4, #0xa80e58
    // 0xa80d6c: ldur            x3, [fp, #-0x18]
    // 0xa80d70: LoadField: r4 = r3->field_33
    //     0xa80d70: ldur            w4, [x3, #0x33]
    // 0xa80d74: DecompressPointer r4
    //     0xa80d74: add             x4, x4, HEAP, lsl #32
    // 0xa80d78: stur            x4, [fp, #-0x20]
    // 0xa80d7c: cmp             w4, NULL
    // 0xa80d80: b.ne            #0xa80db4
    // 0xa80d84: mov             x0, x4
    // 0xa80d88: ldur            x2, [fp, #-8]
    // 0xa80d8c: r1 = Null
    //     0xa80d8c: mov             x1, NULL
    // 0xa80d90: cmp             w2, NULL
    // 0xa80d94: b.eq            #0xa80db4
    // 0xa80d98: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa80d98: ldur            w4, [x2, #0x17]
    // 0xa80d9c: DecompressPointer r4
    //     0xa80d9c: add             x4, x4, HEAP, lsl #32
    // 0xa80da0: r8 = X0
    //     0xa80da0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xa80da4: LoadField: r9 = r4->field_7
    //     0xa80da4: ldur            x9, [x4, #7]
    // 0xa80da8: r3 = Null
    //     0xa80da8: add             x3, PP, #0x59, lsl #12  ; [pp+0x59b78] Null
    //     0xa80dac: ldr             x3, [x3, #0xb78]
    // 0xa80db0: blr             x9
    // 0xa80db4: ldur            x3, [fp, #-0x10]
    // 0xa80db8: LoadField: r1 = r3->field_1f
    //     0xa80db8: ldur            w1, [x3, #0x1f]
    // 0xa80dbc: DecompressPointer r1
    //     0xa80dbc: add             x1, x1, HEAP, lsl #32
    // 0xa80dc0: cmp             w1, NULL
    // 0xa80dc4: b.eq            #0xa80e8c
    // 0xa80dc8: r0 = LoadClassIdInstr(r1)
    //     0xa80dc8: ldur            x0, [x1, #-1]
    //     0xa80dcc: ubfx            x0, x0, #0xc, #0x14
    // 0xa80dd0: ldur            x2, [fp, #-0x20]
    // 0xa80dd4: r0 = GDT[cid_x0 + 0x4ec]()
    //     0xa80dd4: add             lr, x0, #0x4ec
    //     0xa80dd8: ldr             lr, [x21, lr, lsl #3]
    //     0xa80ddc: blr             lr
    // 0xa80de0: ldur            x0, [fp, #-0x10]
    // 0xa80de4: LoadField: r3 = r0->field_1b
    //     0xa80de4: ldur            w3, [x0, #0x1b]
    // 0xa80de8: DecompressPointer r3
    //     0xa80de8: add             x3, x3, HEAP, lsl #32
    // 0xa80dec: stur            x3, [fp, #-0x28]
    // 0xa80df0: cmp             w3, NULL
    // 0xa80df4: b.eq            #0xa80e90
    // 0xa80df8: mov             x1, x3
    // 0xa80dfc: ldur            x2, [fp, #-0x20]
    // 0xa80e00: r0 = _getValueOrData()
    //     0xa80e00: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa80e04: mov             x1, x0
    // 0xa80e08: ldur            x0, [fp, #-0x28]
    // 0xa80e0c: LoadField: r2 = r0->field_f
    //     0xa80e0c: ldur            w2, [x0, #0xf]
    // 0xa80e10: DecompressPointer r2
    //     0xa80e10: add             x2, x2, HEAP, lsl #32
    // 0xa80e14: cmp             w2, w1
    // 0xa80e18: b.ne            #0xa80e24
    // 0xa80e1c: r2 = Null
    //     0xa80e1c: mov             x2, NULL
    // 0xa80e20: b               #0xa80e28
    // 0xa80e24: mov             x2, x1
    // 0xa80e28: ldur            x1, [fp, #-0x20]
    // 0xa80e2c: cmp             w2, NULL
    // 0xa80e30: b.eq            #0xa80e94
    // 0xa80e34: r0 = LoadClassIdInstr(r1)
    //     0xa80e34: ldur            x0, [x1, #-1]
    //     0xa80e38: ubfx            x0, x0, #0xc, #0x14
    // 0xa80e3c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa80e3c: movz            x17, #0xbf5c
    //     0xa80e40: add             lr, x0, x17
    //     0xa80e44: ldr             lr, [x21, lr, lsl #3]
    //     0xa80e48: blr             lr
    // 0xa80e4c: ldur            x0, [fp, #-0x18]
    // 0xa80e50: ldur            x2, [fp, #-8]
    // 0xa80e54: b               #0xa80d50
    // 0xa80e58: ldur            x1, [fp, #-0x10]
    // 0xa80e5c: StoreField: r1->field_1b = rNULL
    //     0xa80e5c: stur            NULL, [x1, #0x1b]
    // 0xa80e60: b               #0xa80e68
    // 0xa80e64: mov             x1, x0
    // 0xa80e68: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa80e68: stur            NULL, [x1, #0x17]
    // 0xa80e6c: r0 = Null
    //     0xa80e6c: mov             x0, NULL
    // 0xa80e70: LeaveFrame
    //     0xa80e70: mov             SP, fp
    //     0xa80e74: ldp             fp, lr, [SP], #0x10
    // 0xa80e78: ret
    //     0xa80e78: ret             
    // 0xa80e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80e7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80e80: b               #0xa80d00
    // 0xa80e84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa80e84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa80e88: b               #0xa80d60
    // 0xa80e8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa80e8c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa80e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa80e90: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa80e94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa80e94: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic remove(dynamic) {
    // ** addr: 0xd13c0c, size: 0x24
    // 0xd13c0c: EnterFrame
    //     0xd13c0c: stp             fp, lr, [SP, #-0x10]!
    //     0xd13c10: mov             fp, SP
    // 0xd13c14: ldr             x2, [fp, #0x10]
    // 0xd13c18: r1 = Function 'remove':.
    //     0xd13c18: add             x1, PP, #0x59, lsl #12  ; [pp+0x59b58] AnonymousClosure: (0xd13c30), in [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::remove (0xd4459c)
    //     0xd13c1c: ldr             x1, [x1, #0xb58]
    // 0xd13c20: r0 = AllocateClosure()
    //     0xd13c20: bl              #0xec1630  ; AllocateClosureStub
    // 0xd13c24: LeaveFrame
    //     0xd13c24: mov             SP, fp
    //     0xd13c28: ldp             fp, lr, [SP], #0x10
    // 0xd13c2c: ret
    //     0xd13c2c: ret             
  }
  [closure] void remove(dynamic, Selectable) {
    // ** addr: 0xd13c30, size: 0x3c
    // 0xd13c30: EnterFrame
    //     0xd13c30: stp             fp, lr, [SP, #-0x10]!
    //     0xd13c34: mov             fp, SP
    // 0xd13c38: ldr             x0, [fp, #0x18]
    // 0xd13c3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd13c3c: ldur            w1, [x0, #0x17]
    // 0xd13c40: DecompressPointer r1
    //     0xd13c40: add             x1, x1, HEAP, lsl #32
    // 0xd13c44: CheckStackOverflow
    //     0xd13c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd13c48: cmp             SP, x16
    //     0xd13c4c: b.ls            #0xd13c64
    // 0xd13c50: ldr             x2, [fp, #0x10]
    // 0xd13c54: r0 = remove()
    //     0xd13c54: bl              #0xd4459c  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::remove
    // 0xd13c58: LeaveFrame
    //     0xd13c58: mov             SP, fp
    //     0xd13c5c: ldp             fp, lr, [SP], #0x10
    // 0xd13c60: ret
    //     0xd13c60: ret             
    // 0xd13c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd13c64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd13c68: b               #0xd13c50
  }
  dynamic add(dynamic) {
    // ** addr: 0xd13ccc, size: 0x24
    // 0xd13ccc: EnterFrame
    //     0xd13ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xd13cd0: mov             fp, SP
    // 0xd13cd4: ldr             x2, [fp, #0x10]
    // 0xd13cd8: r1 = Function 'add':.
    //     0xd13cd8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59b60] AnonymousClosure: (0xd13cf0), in [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::add (0xd14220)
    //     0xd13cdc: ldr             x1, [x1, #0xb60]
    // 0xd13ce0: r0 = AllocateClosure()
    //     0xd13ce0: bl              #0xec1630  ; AllocateClosureStub
    // 0xd13ce4: LeaveFrame
    //     0xd13ce4: mov             SP, fp
    //     0xd13ce8: ldp             fp, lr, [SP], #0x10
    // 0xd13cec: ret
    //     0xd13cec: ret             
  }
  [closure] void add(dynamic, Selectable) {
    // ** addr: 0xd13cf0, size: 0x3c
    // 0xd13cf0: EnterFrame
    //     0xd13cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xd13cf4: mov             fp, SP
    // 0xd13cf8: ldr             x0, [fp, #0x18]
    // 0xd13cfc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd13cfc: ldur            w1, [x0, #0x17]
    // 0xd13d00: DecompressPointer r1
    //     0xd13d00: add             x1, x1, HEAP, lsl #32
    // 0xd13d04: CheckStackOverflow
    //     0xd13d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd13d08: cmp             SP, x16
    //     0xd13d0c: b.ls            #0xd13d24
    // 0xd13d10: ldr             x2, [fp, #0x10]
    // 0xd13d14: r0 = add()
    //     0xd13d14: bl              #0xd14220  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::add
    // 0xd13d18: LeaveFrame
    //     0xd13d18: mov             SP, fp
    //     0xd13d1c: ldp             fp, lr, [SP], #0x10
    // 0xd13d20: ret
    //     0xd13d20: ret             
    // 0xd13d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd13d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd13d28: b               #0xd13d10
  }
  _ add(/* No info */) {
    // ** addr: 0xd14220, size: 0x17c
    // 0xd14220: EnterFrame
    //     0xd14220: stp             fp, lr, [SP, #-0x10]!
    //     0xd14224: mov             fp, SP
    // 0xd14228: AllocStack(0x28)
    //     0xd14228: sub             SP, SP, #0x28
    // 0xd1422c: SetupParameters(_SelectionKeepAliveState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xd1422c: mov             x0, x1
    //     0xd14230: stur            x1, [fp, #-8]
    //     0xd14234: mov             x1, x2
    //     0xd14238: stur            x2, [fp, #-0x10]
    // 0xd1423c: CheckStackOverflow
    //     0xd1423c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd14240: cmp             SP, x16
    //     0xd14244: b.ls            #0xd14390
    // 0xd14248: r1 = 2
    //     0xd14248: movz            x1, #0x2
    // 0xd1424c: r0 = AllocateContext()
    //     0xd1424c: bl              #0xec126c  ; AllocateContextStub
    // 0xd14250: mov             x1, x0
    // 0xd14254: ldur            x0, [fp, #-8]
    // 0xd14258: StoreField: r1->field_f = r0
    //     0xd14258: stur            w0, [x1, #0xf]
    // 0xd1425c: ldur            x3, [fp, #-0x10]
    // 0xd14260: StoreField: r1->field_13 = r3
    //     0xd14260: stur            w3, [x1, #0x13]
    // 0xd14264: mov             x2, x1
    // 0xd14268: r1 = Function '<anonymous closure>':.
    //     0xd14268: add             x1, PP, #0x59, lsl #12  ; [pp+0x59b68] AnonymousClosure: (0xd145ac), of [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState
    //     0xd1426c: ldr             x1, [x1, #0xb68]
    // 0xd14270: r0 = AllocateClosure()
    //     0xd14270: bl              #0xec1630  ; AllocateClosureStub
    // 0xd14274: mov             x4, x0
    // 0xd14278: ldur            x3, [fp, #-0x10]
    // 0xd1427c: stur            x4, [fp, #-0x18]
    // 0xd14280: r0 = LoadClassIdInstr(r3)
    //     0xd14280: ldur            x0, [x3, #-1]
    //     0xd14284: ubfx            x0, x0, #0xc, #0x14
    // 0xd14288: mov             x1, x3
    // 0xd1428c: mov             x2, x4
    // 0xd14290: r0 = GDT[cid_x0 + 0xc407]()
    //     0xd14290: movz            x17, #0xc407
    //     0xd14294: add             lr, x0, x17
    //     0xd14298: ldr             lr, [x21, lr, lsl #3]
    //     0xd1429c: blr             lr
    // 0xd142a0: ldur            x1, [fp, #-8]
    // 0xd142a4: LoadField: r0 = r1->field_1b
    //     0xd142a4: ldur            w0, [x1, #0x1b]
    // 0xd142a8: DecompressPointer r0
    //     0xd142a8: add             x0, x0, HEAP, lsl #32
    // 0xd142ac: cmp             w0, NULL
    // 0xd142b0: b.ne            #0xd142f0
    // 0xd142b4: r16 = <Selectable, (dynamic this) => void?>
    //     0xd142b4: add             x16, PP, #0x59, lsl #12  ; [pp+0x59b70] TypeArguments: <Selectable, (dynamic this) => void?>
    //     0xd142b8: ldr             x16, [x16, #0xb70]
    // 0xd142bc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xd142c0: stp             lr, x16, [SP]
    // 0xd142c4: r0 = Map._fromLiteral()
    //     0xd142c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xd142c8: mov             x1, x0
    // 0xd142cc: ldur            x4, [fp, #-8]
    // 0xd142d0: StoreField: r4->field_1b = r0
    //     0xd142d0: stur            w0, [x4, #0x1b]
    //     0xd142d4: ldurb           w16, [x4, #-1]
    //     0xd142d8: ldurb           w17, [x0, #-1]
    //     0xd142dc: and             x16, x17, x16, lsr #2
    //     0xd142e0: tst             x16, HEAP, lsr #32
    //     0xd142e4: b.eq            #0xd142ec
    //     0xd142e8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xd142ec: b               #0xd142f8
    // 0xd142f0: mov             x4, x1
    // 0xd142f4: mov             x1, x0
    // 0xd142f8: ldur            x0, [fp, #-0x10]
    // 0xd142fc: mov             x2, x0
    // 0xd14300: ldur            x3, [fp, #-0x18]
    // 0xd14304: r0 = []=()
    //     0xd14304: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xd14308: ldur            x3, [fp, #-8]
    // 0xd1430c: LoadField: r1 = r3->field_1f
    //     0xd1430c: ldur            w1, [x3, #0x1f]
    // 0xd14310: DecompressPointer r1
    //     0xd14310: add             x1, x1, HEAP, lsl #32
    // 0xd14314: cmp             w1, NULL
    // 0xd14318: b.eq            #0xd14398
    // 0xd1431c: r0 = LoadClassIdInstr(r1)
    //     0xd1431c: ldur            x0, [x1, #-1]
    //     0xd14320: ubfx            x0, x0, #0xc, #0x14
    // 0xd14324: ldur            x2, [fp, #-0x10]
    // 0xd14328: r0 = GDT[cid_x0 + 0xf49]()
    //     0xd14328: add             lr, x0, #0xf49
    //     0xd1432c: ldr             lr, [x21, lr, lsl #3]
    //     0xd14330: blr             lr
    // 0xd14334: ldur            x2, [fp, #-0x10]
    // 0xd14338: r0 = LoadClassIdInstr(r2)
    //     0xd14338: ldur            x0, [x2, #-1]
    //     0xd1433c: ubfx            x0, x0, #0xc, #0x14
    // 0xd14340: mov             x1, x2
    // 0xd14344: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd14344: movz            x17, #0x276f
    //     0xd14348: movk            x17, #0x1, lsl #16
    //     0xd1434c: add             lr, x0, x17
    //     0xd14350: ldr             lr, [x21, lr, lsl #3]
    //     0xd14354: blr             lr
    // 0xd14358: LoadField: r1 = r0->field_f
    //     0xd14358: ldur            w1, [x0, #0xf]
    // 0xd1435c: DecompressPointer r1
    //     0xd1435c: add             x1, x1, HEAP, lsl #32
    // 0xd14360: r16 = Instance_SelectionStatus
    //     0xd14360: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf0] Obj!SelectionStatus@e35561
    //     0xd14364: ldr             x16, [x16, #0xcf0]
    // 0xd14368: cmp             w1, w16
    // 0xd1436c: b.eq            #0xd14380
    // 0xd14370: ldur            x1, [fp, #-8]
    // 0xd14374: ldur            x2, [fp, #-0x10]
    // 0xd14378: r3 = true
    //     0xd14378: add             x3, NULL, #0x20  ; true
    // 0xd1437c: r0 = _updateSelectablesWithSelections()
    //     0xd1437c: bl              #0xd1439c  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::_updateSelectablesWithSelections
    // 0xd14380: r0 = Null
    //     0xd14380: mov             x0, NULL
    // 0xd14384: LeaveFrame
    //     0xd14384: mov             SP, fp
    //     0xd14388: ldp             fp, lr, [SP], #0x10
    // 0xd1438c: ret
    //     0xd1438c: ret             
    // 0xd14390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd14390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd14394: b               #0xd14248
    // 0xd14398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd14398: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateSelectablesWithSelections(/* No info */) {
    // ** addr: 0xd1439c, size: 0x168
    // 0xd1439c: EnterFrame
    //     0xd1439c: stp             fp, lr, [SP, #-0x10]!
    //     0xd143a0: mov             fp, SP
    // 0xd143a4: AllocStack(0x20)
    //     0xd143a4: sub             SP, SP, #0x20
    // 0xd143a8: SetupParameters(_SelectionKeepAliveState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xd143a8: stur            x1, [fp, #-8]
    //     0xd143ac: stur            x2, [fp, #-0x10]
    // 0xd143b0: CheckStackOverflow
    //     0xd143b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd143b4: cmp             SP, x16
    //     0xd143b8: b.ls            #0xd144fc
    // 0xd143bc: tbnz            w3, #4, #0xd14478
    // 0xd143c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd143c0: ldur            w0, [x1, #0x17]
    // 0xd143c4: DecompressPointer r0
    //     0xd143c4: add             x0, x0, HEAP, lsl #32
    // 0xd143c8: cmp             w0, NULL
    // 0xd143cc: b.ne            #0xd14464
    // 0xd143d0: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xd143d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd143d4: ldr             x0, [x0, #0x778]
    //     0xd143d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd143dc: cmp             w0, w16
    //     0xd143e0: b.ne            #0xd143ec
    //     0xd143e4: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xd143e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd143ec: r1 = <Selectable>
    //     0xd143ec: add             x1, PP, #0x4f, lsl #12  ; [pp+0x4f318] TypeArguments: <Selectable>
    //     0xd143f0: ldr             x1, [x1, #0x318]
    // 0xd143f4: stur            x0, [fp, #-0x18]
    // 0xd143f8: r0 = _Set()
    //     0xd143f8: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xd143fc: mov             x1, x0
    // 0xd14400: ldur            x0, [fp, #-0x18]
    // 0xd14404: stur            x1, [fp, #-0x20]
    // 0xd14408: StoreField: r1->field_1b = r0
    //     0xd14408: stur            w0, [x1, #0x1b]
    // 0xd1440c: StoreField: r1->field_b = rZR
    //     0xd1440c: stur            wzr, [x1, #0xb]
    // 0xd14410: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xd14410: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd14414: ldr             x0, [x0, #0x780]
    //     0xd14418: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd1441c: cmp             w0, w16
    //     0xd14420: b.ne            #0xd1442c
    //     0xd14424: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xd14428: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xd1442c: ldur            x1, [fp, #-0x20]
    // 0xd14430: StoreField: r1->field_f = r0
    //     0xd14430: stur            w0, [x1, #0xf]
    // 0xd14434: StoreField: r1->field_13 = rZR
    //     0xd14434: stur            wzr, [x1, #0x13]
    // 0xd14438: ArrayStore: r1[0] = rZR  ; List_4
    //     0xd14438: stur            wzr, [x1, #0x17]
    // 0xd1443c: mov             x0, x1
    // 0xd14440: ldur            x3, [fp, #-8]
    // 0xd14444: ArrayStore: r3[0] = r0  ; List_4
    //     0xd14444: stur            w0, [x3, #0x17]
    //     0xd14448: ldurb           w16, [x3, #-1]
    //     0xd1444c: ldurb           w17, [x0, #-1]
    //     0xd14450: and             x16, x17, x16, lsr #2
    //     0xd14454: tst             x16, HEAP, lsr #32
    //     0xd14458: b.eq            #0xd14460
    //     0xd1445c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xd14460: b               #0xd1446c
    // 0xd14464: mov             x3, x1
    // 0xd14468: mov             x1, x0
    // 0xd1446c: ldur            x2, [fp, #-0x10]
    // 0xd14470: r0 = add()
    //     0xd14470: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xd14474: b               #0xd14494
    // 0xd14478: mov             x0, x1
    // 0xd1447c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd1447c: ldur            w1, [x0, #0x17]
    // 0xd14480: DecompressPointer r1
    //     0xd14480: add             x1, x1, HEAP, lsl #32
    // 0xd14484: cmp             w1, NULL
    // 0xd14488: b.eq            #0xd14494
    // 0xd1448c: ldur            x2, [fp, #-0x10]
    // 0xd14490: r0 = remove()
    //     0xd14490: bl              #0xd89c04  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xd14494: ldur            x1, [fp, #-8]
    // 0xd14498: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd14498: ldur            w0, [x1, #0x17]
    // 0xd1449c: DecompressPointer r0
    //     0xd1449c: add             x0, x0, HEAP, lsl #32
    // 0xd144a0: cmp             w0, NULL
    // 0xd144a4: b.ne            #0xd144b0
    // 0xd144a8: r0 = Null
    //     0xd144a8: mov             x0, NULL
    // 0xd144ac: b               #0xd144d4
    // 0xd144b0: LoadField: r2 = r0->field_13
    //     0xd144b0: ldur            w2, [x0, #0x13]
    // 0xd144b4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xd144b4: ldur            w3, [x0, #0x17]
    // 0xd144b8: r0 = LoadInt32Instr(r2)
    //     0xd144b8: sbfx            x0, x2, #1, #0x1f
    // 0xd144bc: r2 = LoadInt32Instr(r3)
    //     0xd144bc: sbfx            x2, x3, #1, #0x1f
    // 0xd144c0: sub             x3, x0, x2
    // 0xd144c4: cbnz            x3, #0xd144d0
    // 0xd144c8: r0 = false
    //     0xd144c8: add             x0, NULL, #0x30  ; false
    // 0xd144cc: b               #0xd144d4
    // 0xd144d0: r0 = true
    //     0xd144d0: add             x0, NULL, #0x20  ; true
    // 0xd144d4: cmp             w0, NULL
    // 0xd144d8: b.ne            #0xd144e4
    // 0xd144dc: r2 = false
    //     0xd144dc: add             x2, NULL, #0x30  ; false
    // 0xd144e0: b               #0xd144e8
    // 0xd144e4: mov             x2, x0
    // 0xd144e8: r0 = wantKeepAlive=()
    //     0xd144e8: bl              #0xd14504  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::wantKeepAlive=
    // 0xd144ec: r0 = Null
    //     0xd144ec: mov             x0, NULL
    // 0xd144f0: LeaveFrame
    //     0xd144f0: mov             SP, fp
    //     0xd144f4: ldp             fp, lr, [SP], #0x10
    // 0xd144f8: ret
    //     0xd144f8: ret             
    // 0xd144fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd144fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd14500: b               #0xd143bc
  }
  set _ wantKeepAlive=(/* No info */) {
    // ** addr: 0xd14504, size: 0x44
    // 0xd14504: EnterFrame
    //     0xd14504: stp             fp, lr, [SP, #-0x10]!
    //     0xd14508: mov             fp, SP
    // 0xd1450c: CheckStackOverflow
    //     0xd1450c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd14510: cmp             SP, x16
    //     0xd14514: b.ls            #0xd14540
    // 0xd14518: LoadField: r0 = r1->field_23
    //     0xd14518: ldur            w0, [x1, #0x23]
    // 0xd1451c: DecompressPointer r0
    //     0xd1451c: add             x0, x0, HEAP, lsl #32
    // 0xd14520: cmp             w0, w2
    // 0xd14524: b.eq            #0xd14530
    // 0xd14528: StoreField: r1->field_23 = r2
    //     0xd14528: stur            w2, [x1, #0x23]
    // 0xd1452c: r0 = updateKeepAlive()
    //     0xd1452c: bl              #0xd14548  ; [package:flutter/src/widgets/scroll_delegate.dart] __SelectionKeepAliveState&State&AutomaticKeepAliveClientMixin::updateKeepAlive
    // 0xd14530: r0 = Null
    //     0xd14530: mov             x0, NULL
    // 0xd14534: LeaveFrame
    //     0xd14534: mov             SP, fp
    //     0xd14538: ldp             fp, lr, [SP], #0x10
    // 0xd1453c: ret
    //     0xd1453c: ret             
    // 0xd14540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd14540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd14544: b               #0xd14518
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xd145ac, size: 0xb8
    // 0xd145ac: EnterFrame
    //     0xd145ac: stp             fp, lr, [SP, #-0x10]!
    //     0xd145b0: mov             fp, SP
    // 0xd145b4: AllocStack(0x8)
    //     0xd145b4: sub             SP, SP, #8
    // 0xd145b8: SetupParameters()
    //     0xd145b8: ldr             x0, [fp, #0x10]
    //     0xd145bc: ldur            w2, [x0, #0x17]
    //     0xd145c0: add             x2, x2, HEAP, lsl #32
    //     0xd145c4: stur            x2, [fp, #-8]
    // 0xd145c8: CheckStackOverflow
    //     0xd145c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd145cc: cmp             SP, x16
    //     0xd145d0: b.ls            #0xd1465c
    // 0xd145d4: LoadField: r1 = r2->field_13
    //     0xd145d4: ldur            w1, [x2, #0x13]
    // 0xd145d8: DecompressPointer r1
    //     0xd145d8: add             x1, x1, HEAP, lsl #32
    // 0xd145dc: r0 = LoadClassIdInstr(r1)
    //     0xd145dc: ldur            x0, [x1, #-1]
    //     0xd145e0: ubfx            x0, x0, #0xc, #0x14
    // 0xd145e4: r0 = GDT[cid_x0 + 0x1276f]()
    //     0xd145e4: movz            x17, #0x276f
    //     0xd145e8: movk            x17, #0x1, lsl #16
    //     0xd145ec: add             lr, x0, x17
    //     0xd145f0: ldr             lr, [x21, lr, lsl #3]
    //     0xd145f4: blr             lr
    // 0xd145f8: LoadField: r1 = r0->field_f
    //     0xd145f8: ldur            w1, [x0, #0xf]
    // 0xd145fc: DecompressPointer r1
    //     0xd145fc: add             x1, x1, HEAP, lsl #32
    // 0xd14600: r16 = Instance_SelectionStatus
    //     0xd14600: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf0] Obj!SelectionStatus@e35561
    //     0xd14604: ldr             x16, [x16, #0xcf0]
    // 0xd14608: cmp             w1, w16
    // 0xd1460c: b.eq            #0xd14630
    // 0xd14610: ldur            x0, [fp, #-8]
    // 0xd14614: LoadField: r1 = r0->field_f
    //     0xd14614: ldur            w1, [x0, #0xf]
    // 0xd14618: DecompressPointer r1
    //     0xd14618: add             x1, x1, HEAP, lsl #32
    // 0xd1461c: LoadField: r2 = r0->field_13
    //     0xd1461c: ldur            w2, [x0, #0x13]
    // 0xd14620: DecompressPointer r2
    //     0xd14620: add             x2, x2, HEAP, lsl #32
    // 0xd14624: r3 = true
    //     0xd14624: add             x3, NULL, #0x20  ; true
    // 0xd14628: r0 = _updateSelectablesWithSelections()
    //     0xd14628: bl              #0xd1439c  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::_updateSelectablesWithSelections
    // 0xd1462c: b               #0xd1464c
    // 0xd14630: ldur            x0, [fp, #-8]
    // 0xd14634: LoadField: r1 = r0->field_f
    //     0xd14634: ldur            w1, [x0, #0xf]
    // 0xd14638: DecompressPointer r1
    //     0xd14638: add             x1, x1, HEAP, lsl #32
    // 0xd1463c: LoadField: r2 = r0->field_13
    //     0xd1463c: ldur            w2, [x0, #0x13]
    // 0xd14640: DecompressPointer r2
    //     0xd14640: add             x2, x2, HEAP, lsl #32
    // 0xd14644: r3 = false
    //     0xd14644: add             x3, NULL, #0x30  ; false
    // 0xd14648: r0 = _updateSelectablesWithSelections()
    //     0xd14648: bl              #0xd1439c  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::_updateSelectablesWithSelections
    // 0xd1464c: r0 = Null
    //     0xd1464c: mov             x0, NULL
    // 0xd14650: LeaveFrame
    //     0xd14650: mov             SP, fp
    //     0xd14654: ldp             fp, lr, [SP], #0x10
    // 0xd14658: ret
    //     0xd14658: ret             
    // 0xd1465c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd1465c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd14660: b               #0xd145d4
  }
  _ remove(/* No info */) {
    // ** addr: 0xd4459c, size: 0xdc
    // 0xd4459c: EnterFrame
    //     0xd4459c: stp             fp, lr, [SP, #-0x10]!
    //     0xd445a0: mov             fp, SP
    // 0xd445a4: AllocStack(0x10)
    //     0xd445a4: sub             SP, SP, #0x10
    // 0xd445a8: SetupParameters(_SelectionKeepAliveState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xd445a8: mov             x3, x1
    //     0xd445ac: mov             x0, x2
    //     0xd445b0: stur            x1, [fp, #-8]
    //     0xd445b4: stur            x2, [fp, #-0x10]
    // 0xd445b8: CheckStackOverflow
    //     0xd445b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd445bc: cmp             SP, x16
    //     0xd445c0: b.ls            #0xd44668
    // 0xd445c4: LoadField: r1 = r3->field_1b
    //     0xd445c4: ldur            w1, [x3, #0x1b]
    // 0xd445c8: DecompressPointer r1
    //     0xd445c8: add             x1, x1, HEAP, lsl #32
    // 0xd445cc: cmp             w1, NULL
    // 0xd445d0: b.ne            #0xd445e4
    // 0xd445d4: r0 = Null
    //     0xd445d4: mov             x0, NULL
    // 0xd445d8: LeaveFrame
    //     0xd445d8: mov             SP, fp
    //     0xd445dc: ldp             fp, lr, [SP], #0x10
    // 0xd445e0: ret
    //     0xd445e0: ret             
    // 0xd445e4: mov             x2, x0
    // 0xd445e8: r0 = remove()
    //     0xd445e8: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xd445ec: cmp             w0, NULL
    // 0xd445f0: b.eq            #0xd44670
    // 0xd445f4: ldur            x3, [fp, #-0x10]
    // 0xd445f8: r1 = LoadClassIdInstr(r3)
    //     0xd445f8: ldur            x1, [x3, #-1]
    //     0xd445fc: ubfx            x1, x1, #0xc, #0x14
    // 0xd44600: mov             x2, x0
    // 0xd44604: mov             x0, x1
    // 0xd44608: mov             x1, x3
    // 0xd4460c: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xd4460c: movz            x17, #0xbf5c
    //     0xd44610: add             lr, x0, x17
    //     0xd44614: ldr             lr, [x21, lr, lsl #3]
    //     0xd44618: blr             lr
    // 0xd4461c: ldur            x3, [fp, #-8]
    // 0xd44620: LoadField: r1 = r3->field_1f
    //     0xd44620: ldur            w1, [x3, #0x1f]
    // 0xd44624: DecompressPointer r1
    //     0xd44624: add             x1, x1, HEAP, lsl #32
    // 0xd44628: cmp             w1, NULL
    // 0xd4462c: b.eq            #0xd44674
    // 0xd44630: r0 = LoadClassIdInstr(r1)
    //     0xd44630: ldur            x0, [x1, #-1]
    //     0xd44634: ubfx            x0, x0, #0xc, #0x14
    // 0xd44638: ldur            x2, [fp, #-0x10]
    // 0xd4463c: r0 = GDT[cid_x0 + 0x4ec]()
    //     0xd4463c: add             lr, x0, #0x4ec
    //     0xd44640: ldr             lr, [x21, lr, lsl #3]
    //     0xd44644: blr             lr
    // 0xd44648: ldur            x1, [fp, #-8]
    // 0xd4464c: ldur            x2, [fp, #-0x10]
    // 0xd44650: r3 = false
    //     0xd44650: add             x3, NULL, #0x30  ; false
    // 0xd44654: r0 = _updateSelectablesWithSelections()
    //     0xd44654: bl              #0xd1439c  ; [package:flutter/src/widgets/scroll_delegate.dart] _SelectionKeepAliveState::_updateSelectablesWithSelections
    // 0xd44658: r0 = Null
    //     0xd44658: mov             x0, NULL
    // 0xd4465c: LeaveFrame
    //     0xd4465c: mov             SP, fp
    //     0xd44660: ldp             fp, lr, [SP], #0x10
    // 0xd44664: ret
    //     0xd44664: ret             
    // 0xd44668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd44668: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4466c: b               #0xd445c4
    // 0xd44670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd44670: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd44674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd44674: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4758, size: 0x10, field offset: 0xc
//   const constructor, 
class _SelectionKeepAlive extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa92c4c, size: 0x2c
    // 0xa92c4c: EnterFrame
    //     0xa92c4c: stp             fp, lr, [SP, #-0x10]!
    //     0xa92c50: mov             fp, SP
    // 0xa92c54: mov             x0, x1
    // 0xa92c58: r1 = <_SelectionKeepAlive>
    //     0xa92c58: add             x1, PP, #0x56, lsl #12  ; [pp+0x567a0] TypeArguments: <_SelectionKeepAlive>
    //     0xa92c5c: ldr             x1, [x1, #0x7a0]
    // 0xa92c60: r0 = _SelectionKeepAliveState()
    //     0xa92c60: bl              #0xa92c78  ; Allocate_SelectionKeepAliveStateStub -> _SelectionKeepAliveState (size=0x28)
    // 0xa92c64: r1 = false
    //     0xa92c64: add             x1, NULL, #0x30  ; false
    // 0xa92c68: StoreField: r0->field_23 = r1
    //     0xa92c68: stur            w1, [x0, #0x23]
    // 0xa92c6c: LeaveFrame
    //     0xa92c6c: mov             SP, fp
    //     0xa92c70: ldp             fp, lr, [SP], #0x10
    // 0xa92c74: ret
    //     0xa92c74: ret             
  }
}
