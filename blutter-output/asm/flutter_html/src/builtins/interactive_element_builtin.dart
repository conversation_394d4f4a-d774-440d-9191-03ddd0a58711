// lib: , url: package:flutter_html/src/builtins/interactive_element_builtin.dart

// class id: 1049260, size: 0x8
class :: {
}

// class id: 2510, size: 0x8, field offset: 0x8
//   const constructor, 
class InteractiveElementBuiltIn extends HtmlExtension {

  get _ supportedTags(/* No info */) {
    // ** addr: 0xc403d4, size: 0xac
    // 0xc403d4: EnterFrame
    //     0xc403d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc403d8: mov             fp, SP
    // 0xc403dc: AllocStack(0x10)
    //     0xc403dc: sub             SP, SP, #0x10
    // 0xc403e0: CheckStackOverflow
    //     0xc403e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc403e4: cmp             SP, x16
    //     0xc403e8: b.ls            #0xc40478
    // 0xc403ec: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xc403ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc403f0: ldr             x0, [x0, #0x778]
    //     0xc403f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc403f8: cmp             w0, w16
    //     0xc403fc: b.ne            #0xc40408
    //     0xc40400: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xc40404: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc40408: r1 = <String>
    //     0xc40408: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc4040c: stur            x0, [fp, #-8]
    // 0xc40410: r0 = _Set()
    //     0xc40410: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xc40414: mov             x1, x0
    // 0xc40418: ldur            x0, [fp, #-8]
    // 0xc4041c: stur            x1, [fp, #-0x10]
    // 0xc40420: StoreField: r1->field_1b = r0
    //     0xc40420: stur            w0, [x1, #0x1b]
    // 0xc40424: StoreField: r1->field_b = rZR
    //     0xc40424: stur            wzr, [x1, #0xb]
    // 0xc40428: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xc40428: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc4042c: ldr             x0, [x0, #0x780]
    //     0xc40430: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc40434: cmp             w0, w16
    //     0xc40438: b.ne            #0xc40444
    //     0xc4043c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xc40440: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc40444: mov             x1, x0
    // 0xc40448: ldur            x0, [fp, #-0x10]
    // 0xc4044c: StoreField: r0->field_f = r1
    //     0xc4044c: stur            w1, [x0, #0xf]
    // 0xc40450: StoreField: r0->field_13 = rZR
    //     0xc40450: stur            wzr, [x0, #0x13]
    // 0xc40454: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc40454: stur            wzr, [x0, #0x17]
    // 0xc40458: mov             x1, x0
    // 0xc4045c: r2 = "a"
    //     0xc4045c: add             x2, PP, #0x11, lsl #12  ; [pp+0x11ea8] "a"
    //     0xc40460: ldr             x2, [x2, #0xea8]
    // 0xc40464: r0 = add()
    //     0xc40464: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xc40468: ldur            x0, [fp, #-0x10]
    // 0xc4046c: LeaveFrame
    //     0xc4046c: mov             SP, fp
    //     0xc40470: ldp             fp, lr, [SP], #0x10
    // 0xc40474: ret
    //     0xc40474: ret             
    // 0xc40478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc40478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4047c: b               #0xc403ec
  }
  _ build(/* No info */) {
    // ** addr: 0xdbd2e0, size: 0xc4
    // 0xdbd2e0: EnterFrame
    //     0xdbd2e0: stp             fp, lr, [SP, #-0x10]!
    //     0xdbd2e4: mov             fp, SP
    // 0xdbd2e8: AllocStack(0x30)
    //     0xdbd2e8: sub             SP, SP, #0x30
    // 0xdbd2ec: SetupParameters(InteractiveElementBuiltIn this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xdbd2ec: mov             x0, x1
    //     0xdbd2f0: stur            x1, [fp, #-8]
    //     0xdbd2f4: mov             x1, x2
    //     0xdbd2f8: stur            x2, [fp, #-0x10]
    // 0xdbd2fc: CheckStackOverflow
    //     0xdbd2fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbd300: cmp             SP, x16
    //     0xdbd304: b.ls            #0xdbd398
    // 0xdbd308: r1 = 2
    //     0xdbd308: movz            x1, #0x2
    // 0xdbd30c: r0 = AllocateContext()
    //     0xdbd30c: bl              #0xec126c  ; AllocateContextStub
    // 0xdbd310: mov             x2, x0
    // 0xdbd314: ldur            x0, [fp, #-8]
    // 0xdbd318: stur            x2, [fp, #-0x18]
    // 0xdbd31c: StoreField: r2->field_f = r0
    //     0xdbd31c: stur            w0, [x2, #0xf]
    // 0xdbd320: ldur            x1, [fp, #-0x10]
    // 0xdbd324: StoreField: r2->field_13 = r1
    //     0xdbd324: stur            w1, [x2, #0x13]
    // 0xdbd328: r0 = inlineSpanChildren()
    //     0xdbd328: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdbd32c: stur            x0, [fp, #-8]
    // 0xdbd330: cmp             w0, NULL
    // 0xdbd334: b.eq            #0xdbd3a0
    // 0xdbd338: ldur            x2, [fp, #-0x18]
    // 0xdbd33c: r1 = Function '<anonymous closure>':.
    //     0xdbd33c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58658] AnonymousClosure: (0xdbd4a8), in [package:flutter_html/src/builtins/interactive_element_builtin.dart] InteractiveElementBuiltIn::build (0xdbd2e0)
    //     0xdbd340: ldr             x1, [x1, #0x658]
    // 0xdbd344: r0 = AllocateClosure()
    //     0xdbd344: bl              #0xec1630  ; AllocateClosureStub
    // 0xdbd348: r16 = <InlineSpan>
    //     0xdbd348: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xdbd34c: ldr             x16, [x16, #0x5f0]
    // 0xdbd350: ldur            lr, [fp, #-8]
    // 0xdbd354: stp             lr, x16, [SP, #8]
    // 0xdbd358: str             x0, [SP]
    // 0xdbd35c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xdbd35c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xdbd360: r0 = map()
    //     0xdbd360: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xdbd364: LoadField: r1 = r0->field_7
    //     0xdbd364: ldur            w1, [x0, #7]
    // 0xdbd368: DecompressPointer r1
    //     0xdbd368: add             x1, x1, HEAP, lsl #32
    // 0xdbd36c: mov             x2, x0
    // 0xdbd370: r0 = _GrowableList.of()
    //     0xdbd370: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xdbd374: stur            x0, [fp, #-8]
    // 0xdbd378: r0 = TextSpan()
    //     0xdbd378: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xdbd37c: ldur            x1, [fp, #-8]
    // 0xdbd380: StoreField: r0->field_f = r1
    //     0xdbd380: stur            w1, [x0, #0xf]
    // 0xdbd384: r1 = Instance__DeferringMouseCursor
    //     0xdbd384: ldr             x1, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xdbd388: ArrayStore: r0[0] = r1  ; List_4
    //     0xdbd388: stur            w1, [x0, #0x17]
    // 0xdbd38c: LeaveFrame
    //     0xdbd38c: mov             SP, fp
    //     0xdbd390: ldp             fp, lr, [SP], #0x10
    // 0xdbd394: ret
    //     0xdbd394: ret             
    // 0xdbd398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbd398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbd39c: b               #0xdbd308
    // 0xdbd3a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdbd3a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InlineSpan <anonymous closure>(dynamic, InlineSpan) {
    // ** addr: 0xdbd4a8, size: 0x50
    // 0xdbd4a8: EnterFrame
    //     0xdbd4a8: stp             fp, lr, [SP, #-0x10]!
    //     0xdbd4ac: mov             fp, SP
    // 0xdbd4b0: ldr             x0, [fp, #0x18]
    // 0xdbd4b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdbd4b4: ldur            w1, [x0, #0x17]
    // 0xdbd4b8: DecompressPointer r1
    //     0xdbd4b8: add             x1, x1, HEAP, lsl #32
    // 0xdbd4bc: CheckStackOverflow
    //     0xdbd4bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbd4c0: cmp             SP, x16
    //     0xdbd4c4: b.ls            #0xdbd4f0
    // 0xdbd4c8: LoadField: r0 = r1->field_f
    //     0xdbd4c8: ldur            w0, [x1, #0xf]
    // 0xdbd4cc: DecompressPointer r0
    //     0xdbd4cc: add             x0, x0, HEAP, lsl #32
    // 0xdbd4d0: LoadField: r2 = r1->field_13
    //     0xdbd4d0: ldur            w2, [x1, #0x13]
    // 0xdbd4d4: DecompressPointer r2
    //     0xdbd4d4: add             x2, x2, HEAP, lsl #32
    // 0xdbd4d8: mov             x1, x0
    // 0xdbd4dc: ldr             x3, [fp, #0x10]
    // 0xdbd4e0: r0 = _processInteractableChild()
    //     0xdbd4e0: bl              #0xdbd4f8  ; [package:flutter_html/src/builtins/interactive_element_builtin.dart] InteractiveElementBuiltIn::_processInteractableChild
    // 0xdbd4e4: LeaveFrame
    //     0xdbd4e4: mov             SP, fp
    //     0xdbd4e8: ldp             fp, lr, [SP], #0x10
    // 0xdbd4ec: ret
    //     0xdbd4ec: ret             
    // 0xdbd4f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbd4f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbd4f4: b               #0xdbd4c8
  }
  _ _processInteractableChild(/* No info */) {
    // ** addr: 0xdbd4f8, size: 0x27c
    // 0xdbd4f8: EnterFrame
    //     0xdbd4f8: stp             fp, lr, [SP, #-0x10]!
    //     0xdbd4fc: mov             fp, SP
    // 0xdbd500: AllocStack(0x50)
    //     0xdbd500: sub             SP, SP, #0x50
    // 0xdbd504: SetupParameters(InteractiveElementBuiltIn this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xdbd504: mov             x0, x3
    //     0xdbd508: stur            x1, [fp, #-8]
    //     0xdbd50c: stur            x2, [fp, #-0x10]
    //     0xdbd510: stur            x3, [fp, #-0x18]
    // 0xdbd514: CheckStackOverflow
    //     0xdbd514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbd518: cmp             SP, x16
    //     0xdbd51c: b.ls            #0xdbd76c
    // 0xdbd520: r1 = 2
    //     0xdbd520: movz            x1, #0x2
    // 0xdbd524: r0 = AllocateContext()
    //     0xdbd524: bl              #0xec126c  ; AllocateContextStub
    // 0xdbd528: mov             x3, x0
    // 0xdbd52c: ldur            x0, [fp, #-8]
    // 0xdbd530: stur            x3, [fp, #-0x20]
    // 0xdbd534: StoreField: r3->field_f = r0
    //     0xdbd534: stur            w0, [x3, #0xf]
    // 0xdbd538: ldur            x0, [fp, #-0x10]
    // 0xdbd53c: StoreField: r3->field_13 = r0
    //     0xdbd53c: stur            w0, [x3, #0x13]
    // 0xdbd540: mov             x2, x3
    // 0xdbd544: r1 = Function 'onTap':.
    //     0xdbd544: add             x1, PP, #0x58, lsl #12  ; [pp+0x58660] AnonymousClosure: (0xdbd780), in [package:flutter_html/src/builtins/interactive_element_builtin.dart] InteractiveElementBuiltIn::_processInteractableChild (0xdbd4f8)
    //     0xdbd548: ldr             x1, [x1, #0x660]
    // 0xdbd54c: r0 = AllocateClosure()
    //     0xdbd54c: bl              #0xec1630  ; AllocateClosureStub
    // 0xdbd550: mov             x3, x0
    // 0xdbd554: ldur            x0, [fp, #-0x18]
    // 0xdbd558: stur            x3, [fp, #-0x30]
    // 0xdbd55c: r1 = LoadClassIdInstr(r0)
    //     0xdbd55c: ldur            x1, [x0, #-1]
    //     0xdbd560: ubfx            x1, x1, #0xc, #0x14
    // 0xdbd564: r17 = -4380
    //     0xdbd564: movn            x17, #0x111b
    // 0xdbd568: add             x16, x1, x17
    // 0xdbd56c: cmp             x16, #1
    // 0xdbd570: b.hi            #0xdbd694
    // 0xdbd574: LoadField: r4 = r0->field_b
    //     0xdbd574: ldur            w4, [x0, #0xb]
    // 0xdbd578: DecompressPointer r4
    //     0xdbd578: add             x4, x4, HEAP, lsl #32
    // 0xdbd57c: stur            x4, [fp, #-0x28]
    // 0xdbd580: LoadField: r5 = r0->field_f
    //     0xdbd580: ldur            w5, [x0, #0xf]
    // 0xdbd584: DecompressPointer r5
    //     0xdbd584: add             x5, x5, HEAP, lsl #32
    // 0xdbd588: stur            x5, [fp, #-8]
    // 0xdbd58c: cmp             w5, NULL
    // 0xdbd590: b.ne            #0xdbd5a0
    // 0xdbd594: mov             x1, x4
    // 0xdbd598: r2 = Null
    //     0xdbd598: mov             x2, NULL
    // 0xdbd59c: b               #0xdbd5e8
    // 0xdbd5a0: ldur            x2, [fp, #-0x20]
    // 0xdbd5a4: r1 = Function '<anonymous closure>':.
    //     0xdbd5a4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58668] AnonymousClosure: (0xdbd4a8), in [package:flutter_html/src/builtins/interactive_element_builtin.dart] InteractiveElementBuiltIn::build (0xdbd2e0)
    //     0xdbd5a8: ldr             x1, [x1, #0x668]
    // 0xdbd5ac: r0 = AllocateClosure()
    //     0xdbd5ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xdbd5b0: r16 = <InlineSpan>
    //     0xdbd5b0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xdbd5b4: ldr             x16, [x16, #0x5f0]
    // 0xdbd5b8: ldur            lr, [fp, #-8]
    // 0xdbd5bc: stp             lr, x16, [SP, #8]
    // 0xdbd5c0: str             x0, [SP]
    // 0xdbd5c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xdbd5c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xdbd5c8: r0 = map()
    //     0xdbd5c8: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xdbd5cc: LoadField: r1 = r0->field_7
    //     0xdbd5cc: ldur            w1, [x0, #7]
    // 0xdbd5d0: DecompressPointer r1
    //     0xdbd5d0: add             x1, x1, HEAP, lsl #32
    // 0xdbd5d4: mov             x2, x0
    // 0xdbd5d8: r0 = _GrowableList.of()
    //     0xdbd5d8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xdbd5dc: mov             x2, x0
    // 0xdbd5e0: ldur            x0, [fp, #-0x18]
    // 0xdbd5e4: ldur            x1, [fp, #-0x28]
    // 0xdbd5e8: stur            x2, [fp, #-0x20]
    // 0xdbd5ec: LoadField: r3 = r0->field_7
    //     0xdbd5ec: ldur            w3, [x0, #7]
    // 0xdbd5f0: DecompressPointer r3
    //     0xdbd5f0: add             x3, x3, HEAP, lsl #32
    // 0xdbd5f4: stur            x3, [fp, #-8]
    // 0xdbd5f8: r0 = TapGestureRecognizer()
    //     0xdbd5f8: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0xdbd5fc: mov             x4, x0
    // 0xdbd600: r0 = false
    //     0xdbd600: add             x0, NULL, #0x30  ; false
    // 0xdbd604: stur            x4, [fp, #-0x38]
    // 0xdbd608: StoreField: r4->field_47 = r0
    //     0xdbd608: stur            w0, [x4, #0x47]
    // 0xdbd60c: StoreField: r4->field_4b = r0
    //     0xdbd60c: stur            w0, [x4, #0x4b]
    // 0xdbd610: mov             x1, x4
    // 0xdbd614: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xdbd614: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xdbd618: ldr             x2, [x2, #0x3d8]
    // 0xdbd61c: r3 = Instance_Duration
    //     0xdbd61c: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xdbd620: r5 = Null
    //     0xdbd620: mov             x5, NULL
    // 0xdbd624: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xdbd624: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xdbd628: r0 = PrimaryPointerGestureRecognizer()
    //     0xdbd628: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xdbd62c: ldur            x0, [fp, #-0x30]
    // 0xdbd630: ldur            x1, [fp, #-0x38]
    // 0xdbd634: StoreField: r1->field_5f = r0
    //     0xdbd634: stur            w0, [x1, #0x5f]
    //     0xdbd638: ldurb           w16, [x1, #-1]
    //     0xdbd63c: ldurb           w17, [x0, #-1]
    //     0xdbd640: and             x16, x17, x16, lsr #2
    //     0xdbd644: tst             x16, HEAP, lsr #32
    //     0xdbd648: b.eq            #0xdbd650
    //     0xdbd64c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdbd650: r0 = TextSpan()
    //     0xdbd650: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xdbd654: mov             x1, x0
    // 0xdbd658: ldur            x0, [fp, #-0x28]
    // 0xdbd65c: StoreField: r1->field_b = r0
    //     0xdbd65c: stur            w0, [x1, #0xb]
    // 0xdbd660: ldur            x0, [fp, #-0x20]
    // 0xdbd664: StoreField: r1->field_f = r0
    //     0xdbd664: stur            w0, [x1, #0xf]
    // 0xdbd668: ldur            x0, [fp, #-0x38]
    // 0xdbd66c: StoreField: r1->field_13 = r0
    //     0xdbd66c: stur            w0, [x1, #0x13]
    // 0xdbd670: r0 = Instance_SystemMouseCursor
    //     0xdbd670: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xdbd674: ldr             x0, [x0, #0xbf0]
    // 0xdbd678: ArrayStore: r1[0] = r0  ; List_4
    //     0xdbd678: stur            w0, [x1, #0x17]
    // 0xdbd67c: ldur            x0, [fp, #-8]
    // 0xdbd680: StoreField: r1->field_7 = r0
    //     0xdbd680: stur            w0, [x1, #7]
    // 0xdbd684: mov             x0, x1
    // 0xdbd688: LeaveFrame
    //     0xdbd688: mov             SP, fp
    //     0xdbd68c: ldp             fp, lr, [SP], #0x10
    // 0xdbd690: ret
    //     0xdbd690: ret             
    // 0xdbd694: ldur            x1, [fp, #-0x10]
    // 0xdbd698: LoadField: r2 = r1->field_b
    //     0xdbd698: ldur            w2, [x1, #0xb]
    // 0xdbd69c: DecompressPointer r2
    //     0xdbd69c: add             x2, x2, HEAP, lsl #32
    // 0xdbd6a0: LoadField: r3 = r2->field_7
    //     0xdbd6a0: ldur            w3, [x2, #7]
    // 0xdbd6a4: DecompressPointer r3
    //     0xdbd6a4: add             x3, x3, HEAP, lsl #32
    // 0xdbd6a8: LoadField: r2 = r1->field_f
    //     0xdbd6a8: ldur            w2, [x1, #0xf]
    // 0xdbd6ac: DecompressPointer r2
    //     0xdbd6ac: add             x2, x2, HEAP, lsl #32
    // 0xdbd6b0: mov             x1, x3
    // 0xdbd6b4: r0 = of()
    //     0xdbd6b4: bl              #0xdbbf10  ; [package:flutter_html/src/anchor.dart] AnchorKey::of
    // 0xdbd6b8: mov             x3, x0
    // 0xdbd6bc: ldur            x0, [fp, #-0x18]
    // 0xdbd6c0: r2 = Null
    //     0xdbd6c0: mov             x2, NULL
    // 0xdbd6c4: r1 = Null
    //     0xdbd6c4: mov             x1, NULL
    // 0xdbd6c8: stur            x3, [fp, #-8]
    // 0xdbd6cc: r4 = LoadClassIdInstr(r0)
    //     0xdbd6cc: ldur            x4, [x0, #-1]
    //     0xdbd6d0: ubfx            x4, x4, #0xc, #0x14
    // 0xdbd6d4: r17 = -4383
    //     0xdbd6d4: movn            x17, #0x111e
    // 0xdbd6d8: add             x4, x4, x17
    // 0xdbd6dc: cmp             x4, #1
    // 0xdbd6e0: b.ls            #0xdbd6f8
    // 0xdbd6e4: r8 = WidgetSpan
    //     0xdbd6e4: add             x8, PP, #0x58, lsl #12  ; [pp+0x58670] Type: WidgetSpan
    //     0xdbd6e8: ldr             x8, [x8, #0x670]
    // 0xdbd6ec: r3 = Null
    //     0xdbd6ec: add             x3, PP, #0x58, lsl #12  ; [pp+0x58678] Null
    //     0xdbd6f0: ldr             x3, [x3, #0x678]
    // 0xdbd6f4: r0 = DefaultTypeTest()
    //     0xdbd6f4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xdbd6f8: ldur            x0, [fp, #-0x18]
    // 0xdbd6fc: LoadField: r1 = r0->field_13
    //     0xdbd6fc: ldur            w1, [x0, #0x13]
    // 0xdbd700: DecompressPointer r1
    //     0xdbd700: add             x1, x1, HEAP, lsl #32
    // 0xdbd704: stur            x1, [fp, #-0x10]
    // 0xdbd708: r0 = GestureDetector()
    //     0xdbd708: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xdbd70c: stur            x0, [fp, #-0x18]
    // 0xdbd710: ldur            x16, [fp, #-8]
    // 0xdbd714: ldur            lr, [fp, #-0x30]
    // 0xdbd718: stp             lr, x16, [SP, #8]
    // 0xdbd71c: ldur            x16, [fp, #-0x10]
    // 0xdbd720: str             x16, [SP]
    // 0xdbd724: mov             x1, x0
    // 0xdbd728: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, key, 0x1, onTap, 0x2, null]
    //     0xdbd728: add             x4, PP, #0x58, lsl #12  ; [pp+0x58688] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "key", 0x1, "onTap", 0x2, Null]
    //     0xdbd72c: ldr             x4, [x4, #0x688]
    // 0xdbd730: r0 = GestureDetector()
    //     0xdbd730: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xdbd734: r0 = MultipleTapGestureDetector()
    //     0xdbd734: bl              #0xdbd774  ; AllocateMultipleTapGestureDetectorStub -> MultipleTapGestureDetector (size=0x10)
    // 0xdbd738: mov             x1, x0
    // 0xdbd73c: ldur            x0, [fp, #-0x18]
    // 0xdbd740: stur            x1, [fp, #-8]
    // 0xdbd744: StoreField: r1->field_b = r0
    //     0xdbd744: stur            w0, [x1, #0xb]
    // 0xdbd748: r0 = WidgetSpan()
    //     0xdbd748: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbd74c: ldur            x1, [fp, #-8]
    // 0xdbd750: StoreField: r0->field_13 = r1
    //     0xdbd750: stur            w1, [x0, #0x13]
    // 0xdbd754: r1 = Instance_PlaceholderAlignment
    //     0xdbd754: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbd758: ldr             x1, [x1, #0xde8]
    // 0xdbd75c: StoreField: r0->field_b = r1
    //     0xdbd75c: stur            w1, [x0, #0xb]
    // 0xdbd760: LeaveFrame
    //     0xdbd760: mov             SP, fp
    //     0xdbd764: ldp             fp, lr, [SP], #0x10
    // 0xdbd768: ret
    //     0xdbd768: ret             
    // 0xdbd76c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbd76c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbd770: b               #0xdbd520
  }
  [closure] void onTap(dynamic) {
    // ** addr: 0xdbd780, size: 0x144
    // 0xdbd780: EnterFrame
    //     0xdbd780: stp             fp, lr, [SP, #-0x10]!
    //     0xdbd784: mov             fp, SP
    // 0xdbd788: AllocStack(0x48)
    //     0xdbd788: sub             SP, SP, #0x48
    // 0xdbd78c: SetupParameters()
    //     0xdbd78c: ldr             x0, [fp, #0x10]
    //     0xdbd790: ldur            w3, [x0, #0x17]
    //     0xdbd794: add             x3, x3, HEAP, lsl #32
    //     0xdbd798: stur            x3, [fp, #-0x20]
    // 0xdbd79c: CheckStackOverflow
    //     0xdbd79c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbd7a0: cmp             SP, x16
    //     0xdbd7a4: b.ls            #0xdbd8b4
    // 0xdbd7a8: LoadField: r4 = r3->field_13
    //     0xdbd7a8: ldur            w4, [x3, #0x13]
    // 0xdbd7ac: DecompressPointer r4
    //     0xdbd7ac: add             x4, x4, HEAP, lsl #32
    // 0xdbd7b0: stur            x4, [fp, #-0x18]
    // 0xdbd7b4: LoadField: r0 = r4->field_b
    //     0xdbd7b4: ldur            w0, [x4, #0xb]
    // 0xdbd7b8: DecompressPointer r0
    //     0xdbd7b8: add             x0, x0, HEAP, lsl #32
    // 0xdbd7bc: LoadField: r5 = r0->field_27
    //     0xdbd7bc: ldur            w5, [x0, #0x27]
    // 0xdbd7c0: DecompressPointer r5
    //     0xdbd7c0: add             x5, x5, HEAP, lsl #32
    // 0xdbd7c4: stur            x5, [fp, #-0x10]
    // 0xdbd7c8: LoadField: r6 = r4->field_f
    //     0xdbd7c8: ldur            w6, [x4, #0xf]
    // 0xdbd7cc: DecompressPointer r6
    //     0xdbd7cc: add             x6, x6, HEAP, lsl #32
    // 0xdbd7d0: stur            x6, [fp, #-8]
    // 0xdbd7d4: cmp             w6, NULL
    // 0xdbd7d8: b.eq            #0xdbd8bc
    // 0xdbd7dc: mov             x0, x6
    // 0xdbd7e0: r2 = Null
    //     0xdbd7e0: mov             x2, NULL
    // 0xdbd7e4: r1 = Null
    //     0xdbd7e4: mov             x1, NULL
    // 0xdbd7e8: r4 = LoadClassIdInstr(r0)
    //     0xdbd7e8: ldur            x4, [x0, #-1]
    //     0xdbd7ec: ubfx            x4, x4, #0xc, #0x14
    // 0xdbd7f0: sub             x4, x4, #0x9ac
    // 0xdbd7f4: cmp             x4, #1
    // 0xdbd7f8: b.ls            #0xdbd810
    // 0xdbd7fc: r8 = InteractiveElement
    //     0xdbd7fc: add             x8, PP, #0x58, lsl #12  ; [pp+0x58690] Type: InteractiveElement
    //     0xdbd800: ldr             x8, [x8, #0x690]
    // 0xdbd804: r3 = Null
    //     0xdbd804: add             x3, PP, #0x58, lsl #12  ; [pp+0x58698] Null
    //     0xdbd808: ldr             x3, [x3, #0x698]
    // 0xdbd80c: r0 = DefaultTypeTest()
    //     0xdbd80c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xdbd810: ldur            x0, [fp, #-8]
    // 0xdbd814: LoadField: r2 = r0->field_23
    //     0xdbd814: ldur            w2, [x0, #0x23]
    // 0xdbd818: DecompressPointer r2
    //     0xdbd818: add             x2, x2, HEAP, lsl #32
    // 0xdbd81c: ldur            x1, [fp, #-0x18]
    // 0xdbd820: stur            x2, [fp, #-0x28]
    // 0xdbd824: r0 = attributes()
    //     0xdbd824: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbd828: mov             x3, x0
    // 0xdbd82c: ldur            x0, [fp, #-0x20]
    // 0xdbd830: stur            x3, [fp, #-0x18]
    // 0xdbd834: LoadField: r1 = r0->field_13
    //     0xdbd834: ldur            w1, [x0, #0x13]
    // 0xdbd838: DecompressPointer r1
    //     0xdbd838: add             x1, x1, HEAP, lsl #32
    // 0xdbd83c: LoadField: r4 = r1->field_7
    //     0xdbd83c: ldur            w4, [x1, #7]
    // 0xdbd840: DecompressPointer r4
    //     0xdbd840: add             x4, x4, HEAP, lsl #32
    // 0xdbd844: mov             x0, x4
    // 0xdbd848: stur            x4, [fp, #-8]
    // 0xdbd84c: r2 = Null
    //     0xdbd84c: mov             x2, NULL
    // 0xdbd850: r1 = Null
    //     0xdbd850: mov             x1, NULL
    // 0xdbd854: r4 = LoadClassIdInstr(r0)
    //     0xdbd854: ldur            x4, [x0, #-1]
    //     0xdbd858: ubfx            x4, x4, #0xc, #0x14
    // 0xdbd85c: cmp             x4, #0x62f
    // 0xdbd860: b.eq            #0xdbd878
    // 0xdbd864: r8 = Element
    //     0xdbd864: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdbd868: ldr             x8, [x8, #0xf80]
    // 0xdbd86c: r3 = Null
    //     0xdbd86c: add             x3, PP, #0x58, lsl #12  ; [pp+0x586a8] Null
    //     0xdbd870: ldr             x3, [x3, #0x6a8]
    // 0xdbd874: r0 = Element()
    //     0xdbd874: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdbd878: ldur            x0, [fp, #-0x10]
    // 0xdbd87c: cmp             w0, NULL
    // 0xdbd880: b.eq            #0xdbd8c0
    // 0xdbd884: ldur            x16, [fp, #-0x28]
    // 0xdbd888: stp             x16, x0, [SP, #0x10]
    // 0xdbd88c: ldur            x16, [fp, #-0x18]
    // 0xdbd890: ldur            lr, [fp, #-8]
    // 0xdbd894: stp             lr, x16, [SP]
    // 0xdbd898: ClosureCall
    //     0xdbd898: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xdbd89c: ldur            x2, [x0, #0x1f]
    //     0xdbd8a0: blr             x2
    // 0xdbd8a4: r0 = Null
    //     0xdbd8a4: mov             x0, NULL
    // 0xdbd8a8: LeaveFrame
    //     0xdbd8a8: mov             SP, fp
    //     0xdbd8ac: ldp             fp, lr, [SP], #0x10
    // 0xdbd8b0: ret
    //     0xdbd8b0: ret             
    // 0xdbd8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbd8b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbd8b8: b               #0xdbd7a8
    // 0xdbd8bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdbd8bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdbd8c0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xdbd8c0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ prepare(/* No info */) {
    // ** addr: 0xdc3de8, size: 0x168
    // 0xdc3de8: EnterFrame
    //     0xdc3de8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc3dec: mov             fp, SP
    // 0xdc3df0: AllocStack(0x48)
    //     0xdc3df0: sub             SP, SP, #0x48
    // 0xdc3df4: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xdc3df4: mov             x4, x2
    //     0xdc3df8: stur            x2, [fp, #-0x10]
    //     0xdc3dfc: stur            x3, [fp, #-0x18]
    // 0xdc3e00: CheckStackOverflow
    //     0xdc3e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc3e04: cmp             SP, x16
    //     0xdc3e08: b.ls            #0xdc3f48
    // 0xdc3e0c: LoadField: r5 = r4->field_7
    //     0xdc3e0c: ldur            w5, [x4, #7]
    // 0xdc3e10: DecompressPointer r5
    //     0xdc3e10: add             x5, x5, HEAP, lsl #32
    // 0xdc3e14: stur            x5, [fp, #-8]
    // 0xdc3e18: r0 = LoadClassIdInstr(r5)
    //     0xdc3e18: ldur            x0, [x5, #-1]
    //     0xdc3e1c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc3e20: cmp             x0, #0x62f
    // 0xdc3e24: b.ne            #0xdc3e78
    // 0xdc3e28: mov             x0, x5
    // 0xdc3e2c: r2 = Null
    //     0xdc3e2c: mov             x2, NULL
    // 0xdc3e30: r1 = Null
    //     0xdc3e30: mov             x1, NULL
    // 0xdc3e34: r4 = LoadClassIdInstr(r0)
    //     0xdc3e34: ldur            x4, [x0, #-1]
    //     0xdc3e38: ubfx            x4, x4, #0xc, #0x14
    // 0xdc3e3c: cmp             x4, #0x62f
    // 0xdc3e40: b.eq            #0xdc3e58
    // 0xdc3e44: r8 = Element
    //     0xdc3e44: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdc3e48: ldr             x8, [x8, #0xf80]
    // 0xdc3e4c: r3 = Null
    //     0xdc3e4c: add             x3, PP, #0x58, lsl #12  ; [pp+0x586b8] Null
    //     0xdc3e50: ldr             x3, [x3, #0x6b8]
    // 0xdc3e54: r0 = Element()
    //     0xdc3e54: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdc3e58: ldur            x5, [fp, #-8]
    // 0xdc3e5c: LoadField: r0 = r5->field_1b
    //     0xdc3e5c: ldur            w0, [x5, #0x1b]
    // 0xdc3e60: DecompressPointer r0
    //     0xdc3e60: add             x0, x0, HEAP, lsl #32
    // 0xdc3e64: cmp             w0, NULL
    // 0xdc3e68: b.ne            #0xdc3e70
    // 0xdc3e6c: r0 = ""
    //     0xdc3e6c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc3e70: mov             x3, x0
    // 0xdc3e74: b               #0xdc3e7c
    // 0xdc3e78: r3 = ""
    //     0xdc3e78: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc3e7c: ldur            x1, [fp, #-0x10]
    // 0xdc3e80: stur            x3, [fp, #-0x20]
    // 0xdc3e84: r0 = attributes()
    //     0xdc3e84: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdc3e88: mov             x1, x0
    // 0xdc3e8c: r2 = "href"
    //     0xdc3e8c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xdc3e90: ldr             x2, [x2, #0x28]
    // 0xdc3e94: stur            x0, [fp, #-0x28]
    // 0xdc3e98: r0 = _getValueOrData()
    //     0xdc3e98: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc3e9c: mov             x1, x0
    // 0xdc3ea0: ldur            x0, [fp, #-0x28]
    // 0xdc3ea4: LoadField: r2 = r0->field_f
    //     0xdc3ea4: ldur            w2, [x0, #0xf]
    // 0xdc3ea8: DecompressPointer r2
    //     0xdc3ea8: add             x2, x2, HEAP, lsl #32
    // 0xdc3eac: cmp             w2, w1
    // 0xdc3eb0: b.ne            #0xdc3ebc
    // 0xdc3eb4: r0 = Null
    //     0xdc3eb4: mov             x0, NULL
    // 0xdc3eb8: b               #0xdc3ec0
    // 0xdc3ebc: mov             x0, x1
    // 0xdc3ec0: stur            x0, [fp, #-0x28]
    // 0xdc3ec4: r0 = Style()
    //     0xdc3ec4: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xdc3ec8: stur            x0, [fp, #-0x30]
    // 0xdc3ecc: r16 = Instance_MaterialColor
    //     0xdc3ecc: add             x16, PP, #0x58, lsl #12  ; [pp+0x586c8] Obj!MaterialColor@e2bcb1
    //     0xdc3ed0: ldr             x16, [x16, #0x6c8]
    // 0xdc3ed4: r30 = Instance_TextDecoration
    //     0xdc3ed4: add             lr, PP, #0x25, lsl #12  ; [pp+0x25c20] Obj!TextDecoration@e264b1
    //     0xdc3ed8: ldr             lr, [lr, #0xc20]
    // 0xdc3edc: stp             lr, x16, [SP]
    // 0xdc3ee0: mov             x1, x0
    // 0xdc3ee4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, textDecoration, 0x2, null]
    //     0xdc3ee4: add             x4, PP, #0x58, lsl #12  ; [pp+0x586d0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "textDecoration", 0x2, Null]
    //     0xdc3ee8: ldr             x4, [x4, #0x6d0]
    // 0xdc3eec: r0 = Style()
    //     0xdc3eec: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xdc3ef0: ldur            x1, [fp, #-0x10]
    // 0xdc3ef4: r0 = id()
    //     0xdc3ef4: bl              #0x9ba940  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::id
    // 0xdc3ef8: stur            x0, [fp, #-0x10]
    // 0xdc3efc: r0 = InteractiveElement()
    //     0xdc3efc: bl              #0xdc3f50  ; AllocateInteractiveElementStub -> InteractiveElement (size=0x28)
    // 0xdc3f00: mov             x4, x0
    // 0xdc3f04: ldur            x0, [fp, #-0x28]
    // 0xdc3f08: stur            x4, [fp, #-0x38]
    // 0xdc3f0c: StoreField: r4->field_23 = r0
    //     0xdc3f0c: stur            w0, [x4, #0x23]
    // 0xdc3f10: ldur            x16, [fp, #-0x10]
    // 0xdc3f14: str             x16, [SP]
    // 0xdc3f18: mov             x1, x4
    // 0xdc3f1c: ldur            x2, [fp, #-0x18]
    // 0xdc3f20: ldur            x3, [fp, #-0x20]
    // 0xdc3f24: ldur            x5, [fp, #-8]
    // 0xdc3f28: ldur            x6, [fp, #-0x30]
    // 0xdc3f2c: r4 = const [0, 0x6, 0x1, 0x5, elementId, 0x5, null]
    //     0xdc3f2c: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x6, 0x1, 0x5, "elementId", 0x5, Null]
    //     0xdc3f30: ldr             x4, [x4, #0xe0]
    // 0xdc3f34: r0 = StyledElement()
    //     0xdc3f34: bl              #0x9aaf88  ; [package:flutter_html/src/tree/styled_element.dart] StyledElement::StyledElement
    // 0xdc3f38: ldur            x0, [fp, #-0x38]
    // 0xdc3f3c: LeaveFrame
    //     0xdc3f3c: mov             SP, fp
    //     0xdc3f40: ldp             fp, lr, [SP], #0x10
    // 0xdc3f44: ret
    //     0xdc3f44: ret             
    // 0xdc3f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc3f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc3f4c: b               #0xdc3e0c
  }
  _ matches(/* No info */) {
    // ** addr: 0xdc6d6c, size: 0xe8
    // 0xdc6d6c: EnterFrame
    //     0xdc6d6c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc6d70: mov             fp, SP
    // 0xdc6d74: AllocStack(0x18)
    //     0xdc6d74: sub             SP, SP, #0x18
    // 0xdc6d78: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xdc6d78: mov             x0, x2
    //     0xdc6d7c: stur            x2, [fp, #-8]
    // 0xdc6d80: CheckStackOverflow
    //     0xdc6d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc6d84: cmp             SP, x16
    //     0xdc6d88: b.ls            #0xdc6e4c
    // 0xdc6d8c: r0 = supportedTags()
    //     0xdc6d8c: bl              #0xc403d4  ; [package:flutter_html/src/builtins/interactive_element_builtin.dart] InteractiveElementBuiltIn::supportedTags
    // 0xdc6d90: mov             x4, x0
    // 0xdc6d94: ldur            x3, [fp, #-8]
    // 0xdc6d98: stur            x4, [fp, #-0x18]
    // 0xdc6d9c: LoadField: r5 = r3->field_7
    //     0xdc6d9c: ldur            w5, [x3, #7]
    // 0xdc6da0: DecompressPointer r5
    //     0xdc6da0: add             x5, x5, HEAP, lsl #32
    // 0xdc6da4: stur            x5, [fp, #-0x10]
    // 0xdc6da8: r0 = LoadClassIdInstr(r5)
    //     0xdc6da8: ldur            x0, [x5, #-1]
    //     0xdc6dac: ubfx            x0, x0, #0xc, #0x14
    // 0xdc6db0: cmp             x0, #0x62f
    // 0xdc6db4: b.ne            #0xdc6e10
    // 0xdc6db8: mov             x0, x5
    // 0xdc6dbc: r2 = Null
    //     0xdc6dbc: mov             x2, NULL
    // 0xdc6dc0: r1 = Null
    //     0xdc6dc0: mov             x1, NULL
    // 0xdc6dc4: r4 = LoadClassIdInstr(r0)
    //     0xdc6dc4: ldur            x4, [x0, #-1]
    //     0xdc6dc8: ubfx            x4, x4, #0xc, #0x14
    // 0xdc6dcc: cmp             x4, #0x62f
    // 0xdc6dd0: b.eq            #0xdc6de8
    // 0xdc6dd4: r8 = Element
    //     0xdc6dd4: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdc6dd8: ldr             x8, [x8, #0xf80]
    // 0xdc6ddc: r3 = Null
    //     0xdc6ddc: add             x3, PP, #0x58, lsl #12  ; [pp+0x586d8] Null
    //     0xdc6de0: ldr             x3, [x3, #0x6d8]
    // 0xdc6de4: r0 = Element()
    //     0xdc6de4: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdc6de8: ldur            x0, [fp, #-0x10]
    // 0xdc6dec: LoadField: r1 = r0->field_1b
    //     0xdc6dec: ldur            w1, [x0, #0x1b]
    // 0xdc6df0: DecompressPointer r1
    //     0xdc6df0: add             x1, x1, HEAP, lsl #32
    // 0xdc6df4: cmp             w1, NULL
    // 0xdc6df8: b.ne            #0xdc6e04
    // 0xdc6dfc: r0 = ""
    //     0xdc6dfc: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc6e00: b               #0xdc6e08
    // 0xdc6e04: mov             x0, x1
    // 0xdc6e08: mov             x2, x0
    // 0xdc6e0c: b               #0xdc6e14
    // 0xdc6e10: r2 = ""
    //     0xdc6e10: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc6e14: ldur            x1, [fp, #-0x18]
    // 0xdc6e18: r0 = contains()
    //     0xdc6e18: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xdc6e1c: tbnz            w0, #4, #0xdc6e3c
    // 0xdc6e20: ldur            x1, [fp, #-8]
    // 0xdc6e24: r0 = attributes()
    //     0xdc6e24: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdc6e28: mov             x1, x0
    // 0xdc6e2c: r2 = "href"
    //     0xdc6e2c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26028] "href"
    //     0xdc6e30: ldr             x2, [x2, #0x28]
    // 0xdc6e34: r0 = containsKey()
    //     0xdc6e34: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xdc6e38: b               #0xdc6e40
    // 0xdc6e3c: r0 = false
    //     0xdc6e3c: add             x0, NULL, #0x30  ; false
    // 0xdc6e40: LeaveFrame
    //     0xdc6e40: mov             SP, fp
    //     0xdc6e44: ldp             fp, lr, [SP], #0x10
    // 0xdc6e48: ret
    //     0xdc6e48: ret             
    // 0xdc6e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc6e4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc6e50: b               #0xdc6d8c
  }
}
