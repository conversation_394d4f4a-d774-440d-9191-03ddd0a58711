// lib: , url: package:flutter_html/src/css_box_widget.dart

// class id: 1049265, size: 0x8
class :: {

  static _ Normalize.normalize(/* No info */) {
    // ** addr: 0x859c60, size: 0x58
    // 0x859c60: LoadField: r2 = r1->field_f
    //     0x859c60: ldur            w2, [x1, #0xf]
    // 0x859c64: DecompressPointer r2
    //     0x859c64: add             x2, x2, HEAP, lsl #32
    // 0x859c68: LoadField: r3 = r2->field_7
    //     0x859c68: ldur            x3, [x2, #7]
    // 0x859c6c: cmp             x3, #2
    // 0x859c70: b.gt            #0x859c88
    // 0x859c74: cmp             x3, #1
    // 0x859c78: b.gt            #0x859cb0
    // 0x859c7c: cmp             x3, #0
    // 0x859c80: b.gt            #0x859cb0
    // 0x859c84: b               #0x859c90
    // 0x859c88: cmp             x3, #3
    // 0x859c8c: b.gt            #0x859cb0
    // 0x859c90: r2 = Instance_Unit
    //     0x859c90: add             x2, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859c94: ldr             x2, [x2, #0xa98]
    // 0x859c98: LoadField: d1 = r1->field_7
    //     0x859c98: ldur            d1, [x1, #7]
    // 0x859c9c: fmul            d2, d1, d0
    // 0x859ca0: StoreField: r1->field_7 = d2
    //     0x859ca0: stur            d2, [x1, #7]
    // 0x859ca4: StoreField: r1->field_f = r2
    //     0x859ca4: stur            w2, [x1, #0xf]
    // 0x859ca8: r0 = Null
    //     0x859ca8: mov             x0, NULL
    // 0x859cac: ret
    //     0x859cac: ret             
    // 0x859cb0: r0 = Null
    //     0x859cb0: mov             x0, NULL
    // 0x859cb4: ret
    //     0x859cb4: ret             
  }
  static _ _calculateEmValue(/* No info */) {
    // ** addr: 0xab896c, size: 0xdc
    // 0xab896c: EnterFrame
    //     0xab896c: stp             fp, lr, [SP, #-0x10]!
    //     0xab8970: mov             fp, SP
    // 0xab8974: AllocStack(0x18)
    //     0xab8974: sub             SP, SP, #0x18
    // 0xab8978: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xab8978: mov             x0, x2
    //     0xab897c: stur            x2, [fp, #-8]
    // 0xab8980: CheckStackOverflow
    //     0xab8980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab8984: cmp             SP, x16
    //     0xab8988: b.ls            #0xab8a24
    // 0xab898c: LoadField: r2 = r1->field_2b
    //     0xab898c: ldur            w2, [x1, #0x2b]
    // 0xab8990: DecompressPointer r2
    //     0xab8990: add             x2, x2, HEAP, lsl #32
    // 0xab8994: cmp             w2, NULL
    // 0xab8998: b.ne            #0xab89a4
    // 0xab899c: r1 = Null
    //     0xab899c: mov             x1, NULL
    // 0xab89a0: b               #0xab89d0
    // 0xab89a4: LoadField: d0 = r2->field_7
    //     0xab89a4: ldur            d0, [x2, #7]
    // 0xab89a8: r1 = inline_Allocate_Double()
    //     0xab89a8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xab89ac: add             x1, x1, #0x10
    //     0xab89b0: cmp             x2, x1
    //     0xab89b4: b.ls            #0xab8a2c
    //     0xab89b8: str             x1, [THR, #0x50]  ; THR::top
    //     0xab89bc: sub             x1, x1, #0xf
    //     0xab89c0: movz            x2, #0xe15c
    //     0xab89c4: movk            x2, #0x3, lsl #16
    //     0xab89c8: stur            x2, [x1, #-1]
    // 0xab89cc: StoreField: r1->field_7 = d0
    //     0xab89cc: stur            d0, [x1, #7]
    // 0xab89d0: cmp             w1, NULL
    // 0xab89d4: b.ne            #0xab89e0
    // 0xab89d8: d0 = 16.000000
    //     0xab89d8: fmov            d0, #16.00000000
    // 0xab89dc: b               #0xab89e4
    // 0xab89e0: LoadField: d0 = r1->field_7
    //     0xab89e0: ldur            d0, [x1, #7]
    // 0xab89e4: mov             x1, x0
    // 0xab89e8: stur            d0, [fp, #-0x10]
    // 0xab89ec: r0 = textScaleFactorOf()
    //     0xab89ec: bl              #0xab8a48  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::textScaleFactorOf
    // 0xab89f0: mov             v1.16b, v0.16b
    // 0xab89f4: ldur            d0, [fp, #-0x10]
    // 0xab89f8: fmul            d2, d0, d1
    // 0xab89fc: ldur            x1, [fp, #-8]
    // 0xab8a00: stur            d2, [fp, #-0x18]
    // 0xab8a04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab8a04: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab8a08: r0 = _of()
    //     0xab8a08: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xab8a0c: LoadField: d1 = r0->field_b
    //     0xab8a0c: ldur            d1, [x0, #0xb]
    // 0xab8a10: ldur            d2, [fp, #-0x18]
    // 0xab8a14: fmul            d0, d2, d1
    // 0xab8a18: LeaveFrame
    //     0xab8a18: mov             SP, fp
    //     0xab8a1c: ldp             fp, lr, [SP], #0x10
    // 0xab8a20: ret
    //     0xab8a20: ret             
    // 0xab8a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab8a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab8a28: b               #0xab898c
    // 0xab8a2c: SaveReg d0
    //     0xab8a2c: str             q0, [SP, #-0x10]!
    // 0xab8a30: SaveReg r0
    //     0xab8a30: str             x0, [SP, #-8]!
    // 0xab8a34: r0 = AllocateDouble()
    //     0xab8a34: bl              #0xec2254  ; AllocateDoubleStub
    // 0xab8a38: mov             x1, x0
    // 0xab8a3c: RestoreReg r0
    //     0xab8a3c: ldr             x0, [SP], #8
    // 0xab8a40: RestoreReg d0
    //     0xab8a40: ldr             q0, [SP], #0x10
    // 0xab8a44: b               #0xab89cc
  }
}

// class id: 2500, size: 0x10, field offset: 0x8
//   const constructor, 
class _Sizes extends Object {
}

// class id: 3007, size: 0x68, field offset: 0x58
//   transformed mixin,
abstract class _RenderCSSBox&RenderBox&ContainerRenderObjectMixin extends RenderBox
     with ContainerRenderObjectMixin<X0 bound RenderObject, X1 bound ContainerParentDataMixin> {

  _ attach(/* No info */) {
    // ** addr: 0x764768, size: 0xfc
    // 0x764768: EnterFrame
    //     0x764768: stp             fp, lr, [SP, #-0x10]!
    //     0x76476c: mov             fp, SP
    // 0x764770: AllocStack(0x18)
    //     0x764770: sub             SP, SP, #0x18
    // 0x764774: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x764774: mov             x3, x1
    //     0x764778: mov             x0, x2
    //     0x76477c: stur            x1, [fp, #-8]
    //     0x764780: stur            x2, [fp, #-0x10]
    // 0x764784: CheckStackOverflow
    //     0x764784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x764788: cmp             SP, x16
    //     0x76478c: b.ls            #0x764850
    // 0x764790: mov             x1, x3
    // 0x764794: mov             x2, x0
    // 0x764798: r0 = attach()
    //     0x764798: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x76479c: ldur            x0, [fp, #-8]
    // 0x7647a0: LoadField: r1 = r0->field_5f
    //     0x7647a0: ldur            w1, [x0, #0x5f]
    // 0x7647a4: DecompressPointer r1
    //     0x7647a4: add             x1, x1, HEAP, lsl #32
    // 0x7647a8: mov             x3, x1
    // 0x7647ac: stur            x3, [fp, #-8]
    // 0x7647b0: CheckStackOverflow
    //     0x7647b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7647b4: cmp             SP, x16
    //     0x7647b8: b.ls            #0x764858
    // 0x7647bc: cmp             w3, NULL
    // 0x7647c0: b.eq            #0x764840
    // 0x7647c4: r0 = LoadClassIdInstr(r3)
    //     0x7647c4: ldur            x0, [x3, #-1]
    //     0x7647c8: ubfx            x0, x0, #0xc, #0x14
    // 0x7647cc: mov             x1, x3
    // 0x7647d0: ldur            x2, [fp, #-0x10]
    // 0x7647d4: r0 = GDT[cid_x0 + 0x11974]()
    //     0x7647d4: movz            x17, #0x1974
    //     0x7647d8: movk            x17, #0x1, lsl #16
    //     0x7647dc: add             lr, x0, x17
    //     0x7647e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7647e4: blr             lr
    // 0x7647e8: ldur            x0, [fp, #-8]
    // 0x7647ec: LoadField: r3 = r0->field_7
    //     0x7647ec: ldur            w3, [x0, #7]
    // 0x7647f0: DecompressPointer r3
    //     0x7647f0: add             x3, x3, HEAP, lsl #32
    // 0x7647f4: stur            x3, [fp, #-0x18]
    // 0x7647f8: cmp             w3, NULL
    // 0x7647fc: b.eq            #0x764860
    // 0x764800: mov             x0, x3
    // 0x764804: r2 = Null
    //     0x764804: mov             x2, NULL
    // 0x764808: r1 = Null
    //     0x764808: mov             x1, NULL
    // 0x76480c: r4 = LoadClassIdInstr(r0)
    //     0x76480c: ldur            x4, [x0, #-1]
    //     0x764810: ubfx            x4, x4, #0xc, #0x14
    // 0x764814: cmp             x4, #0xc76
    // 0x764818: b.eq            #0x764830
    // 0x76481c: r8 = CSSBoxParentData
    //     0x76481c: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x764820: ldr             x8, [x8, #0x3d8]
    // 0x764824: r3 = Null
    //     0x764824: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d410] Null
    //     0x764828: ldr             x3, [x3, #0x410]
    // 0x76482c: r0 = DefaultTypeTest()
    //     0x76482c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x764830: ldur            x1, [fp, #-0x18]
    // 0x764834: LoadField: r3 = r1->field_13
    //     0x764834: ldur            w3, [x1, #0x13]
    // 0x764838: DecompressPointer r3
    //     0x764838: add             x3, x3, HEAP, lsl #32
    // 0x76483c: b               #0x7647ac
    // 0x764840: r0 = Null
    //     0x764840: mov             x0, NULL
    // 0x764844: LeaveFrame
    //     0x764844: mov             SP, fp
    //     0x764848: ldp             fp, lr, [SP], #0x10
    // 0x76484c: ret
    //     0x76484c: ret             
    // 0x764850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x764850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x764854: b               #0x764790
    // 0x764858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x764858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76485c: b               #0x7647bc
    // 0x764860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x764860: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x7875ec, size: 0xd8
    // 0x7875ec: EnterFrame
    //     0x7875ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7875f0: mov             fp, SP
    // 0x7875f4: AllocStack(0x28)
    //     0x7875f4: sub             SP, SP, #0x28
    // 0x7875f8: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7875f8: mov             x0, x1
    //     0x7875fc: mov             x1, x2
    //     0x787600: stur            x2, [fp, #-0x10]
    // 0x787604: CheckStackOverflow
    //     0x787604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x787608: cmp             SP, x16
    //     0x78760c: b.ls            #0x7876b0
    // 0x787610: LoadField: r2 = r0->field_5f
    //     0x787610: ldur            w2, [x0, #0x5f]
    // 0x787614: DecompressPointer r2
    //     0x787614: add             x2, x2, HEAP, lsl #32
    // 0x787618: stur            x2, [fp, #-8]
    // 0x78761c: CheckStackOverflow
    //     0x78761c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x787620: cmp             SP, x16
    //     0x787624: b.ls            #0x7876b8
    // 0x787628: cmp             w2, NULL
    // 0x78762c: b.eq            #0x7876a0
    // 0x787630: stp             x2, x1, [SP]
    // 0x787634: mov             x0, x1
    // 0x787638: ClosureCall
    //     0x787638: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x78763c: ldur            x2, [x0, #0x1f]
    //     0x787640: blr             x2
    // 0x787644: ldur            x0, [fp, #-8]
    // 0x787648: LoadField: r3 = r0->field_7
    //     0x787648: ldur            w3, [x0, #7]
    // 0x78764c: DecompressPointer r3
    //     0x78764c: add             x3, x3, HEAP, lsl #32
    // 0x787650: stur            x3, [fp, #-0x18]
    // 0x787654: cmp             w3, NULL
    // 0x787658: b.eq            #0x7876c0
    // 0x78765c: mov             x0, x3
    // 0x787660: r2 = Null
    //     0x787660: mov             x2, NULL
    // 0x787664: r1 = Null
    //     0x787664: mov             x1, NULL
    // 0x787668: r4 = LoadClassIdInstr(r0)
    //     0x787668: ldur            x4, [x0, #-1]
    //     0x78766c: ubfx            x4, x4, #0xc, #0x14
    // 0x787670: cmp             x4, #0xc76
    // 0x787674: b.eq            #0x78768c
    // 0x787678: r8 = CSSBoxParentData
    //     0x787678: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x78767c: ldr             x8, [x8, #0x3d8]
    // 0x787680: r3 = Null
    //     0x787680: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d3e0] Null
    //     0x787684: ldr             x3, [x3, #0x3e0]
    // 0x787688: r0 = DefaultTypeTest()
    //     0x787688: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x78768c: ldur            x1, [fp, #-0x18]
    // 0x787690: LoadField: r2 = r1->field_13
    //     0x787690: ldur            w2, [x1, #0x13]
    // 0x787694: DecompressPointer r2
    //     0x787694: add             x2, x2, HEAP, lsl #32
    // 0x787698: ldur            x1, [fp, #-0x10]
    // 0x78769c: b               #0x787618
    // 0x7876a0: r0 = Null
    //     0x7876a0: mov             x0, NULL
    // 0x7876a4: LeaveFrame
    //     0x7876a4: mov             SP, fp
    //     0x7876a8: ldp             fp, lr, [SP], #0x10
    // 0x7876ac: ret
    //     0x7876ac: ret             
    // 0x7876b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7876b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7876b4: b               #0x787610
    // 0x7876b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7876b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7876bc: b               #0x787628
    // 0x7876c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7876c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7b152c, size: 0xd0
    // 0x7b152c: EnterFrame
    //     0x7b152c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1530: mov             fp, SP
    // 0x7b1534: AllocStack(0x18)
    //     0x7b1534: sub             SP, SP, #0x18
    // 0x7b1538: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b1538: mov             x5, x1
    //     0x7b153c: mov             x4, x2
    //     0x7b1540: stur            x1, [fp, #-8]
    //     0x7b1544: stur            x2, [fp, #-0x10]
    //     0x7b1548: stur            x3, [fp, #-0x18]
    // 0x7b154c: CheckStackOverflow
    //     0x7b154c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1550: cmp             SP, x16
    //     0x7b1554: b.ls            #0x7b15f4
    // 0x7b1558: mov             x0, x4
    // 0x7b155c: r2 = Null
    //     0x7b155c: mov             x2, NULL
    // 0x7b1560: r1 = Null
    //     0x7b1560: mov             x1, NULL
    // 0x7b1564: r4 = 60
    //     0x7b1564: movz            x4, #0x3c
    // 0x7b1568: branchIfSmi(r0, 0x7b1574)
    //     0x7b1568: tbz             w0, #0, #0x7b1574
    // 0x7b156c: r4 = LoadClassIdInstr(r0)
    //     0x7b156c: ldur            x4, [x0, #-1]
    //     0x7b1570: ubfx            x4, x4, #0xc, #0x14
    // 0x7b1574: sub             x4, x4, #0xbba
    // 0x7b1578: cmp             x4, #0x9a
    // 0x7b157c: b.ls            #0x7b1590
    // 0x7b1580: r8 = RenderBox
    //     0x7b1580: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b1584: r3 = Null
    //     0x7b1584: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d580] Null
    //     0x7b1588: ldr             x3, [x3, #0x580]
    // 0x7b158c: r0 = RenderBox()
    //     0x7b158c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b1590: ldur            x0, [fp, #-0x18]
    // 0x7b1594: r2 = Null
    //     0x7b1594: mov             x2, NULL
    // 0x7b1598: r1 = Null
    //     0x7b1598: mov             x1, NULL
    // 0x7b159c: r4 = 60
    //     0x7b159c: movz            x4, #0x3c
    // 0x7b15a0: branchIfSmi(r0, 0x7b15ac)
    //     0x7b15a0: tbz             w0, #0, #0x7b15ac
    // 0x7b15a4: r4 = LoadClassIdInstr(r0)
    //     0x7b15a4: ldur            x4, [x0, #-1]
    //     0x7b15a8: ubfx            x4, x4, #0xc, #0x14
    // 0x7b15ac: sub             x4, x4, #0xbba
    // 0x7b15b0: cmp             x4, #0x9a
    // 0x7b15b4: b.ls            #0x7b15c8
    // 0x7b15b8: r8 = RenderBox?
    //     0x7b15b8: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7b15bc: r3 = Null
    //     0x7b15bc: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d590] Null
    //     0x7b15c0: ldr             x3, [x3, #0x590]
    // 0x7b15c4: r0 = RenderBox?()
    //     0x7b15c4: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7b15c8: ldur            x1, [fp, #-8]
    // 0x7b15cc: ldur            x2, [fp, #-0x10]
    // 0x7b15d0: r0 = adoptChild()
    //     0x7b15d0: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x7b15d4: ldur            x1, [fp, #-8]
    // 0x7b15d8: ldur            x2, [fp, #-0x10]
    // 0x7b15dc: ldur            x3, [fp, #-0x18]
    // 0x7b15e0: r0 = _insertIntoChildList()
    //     0x7b15e0: bl              #0xda7aa8  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7b15e4: r0 = Null
    //     0x7b15e4: mov             x0, NULL
    // 0x7b15e8: LeaveFrame
    //     0x7b15e8: mov             SP, fp
    //     0x7b15ec: ldp             fp, lr, [SP], #0x10
    // 0x7b15f0: ret
    //     0x7b15f0: ret             
    // 0x7b15f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b15f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b15f8: b               #0x7b1558
  }
  _ remove(/* No info */) {
    // ** addr: 0x7b437c, size: 0x90
    // 0x7b437c: EnterFrame
    //     0x7b437c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b4380: mov             fp, SP
    // 0x7b4384: AllocStack(0x10)
    //     0x7b4384: sub             SP, SP, #0x10
    // 0x7b4388: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b4388: mov             x4, x1
    //     0x7b438c: mov             x3, x2
    //     0x7b4390: stur            x1, [fp, #-8]
    //     0x7b4394: stur            x2, [fp, #-0x10]
    // 0x7b4398: CheckStackOverflow
    //     0x7b4398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b439c: cmp             SP, x16
    //     0x7b43a0: b.ls            #0x7b4404
    // 0x7b43a4: mov             x0, x3
    // 0x7b43a8: r2 = Null
    //     0x7b43a8: mov             x2, NULL
    // 0x7b43ac: r1 = Null
    //     0x7b43ac: mov             x1, NULL
    // 0x7b43b0: r4 = 60
    //     0x7b43b0: movz            x4, #0x3c
    // 0x7b43b4: branchIfSmi(r0, 0x7b43c0)
    //     0x7b43b4: tbz             w0, #0, #0x7b43c0
    // 0x7b43b8: r4 = LoadClassIdInstr(r0)
    //     0x7b43b8: ldur            x4, [x0, #-1]
    //     0x7b43bc: ubfx            x4, x4, #0xc, #0x14
    // 0x7b43c0: sub             x4, x4, #0xbba
    // 0x7b43c4: cmp             x4, #0x9a
    // 0x7b43c8: b.ls            #0x7b43dc
    // 0x7b43cc: r8 = RenderBox
    //     0x7b43cc: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b43d0: r3 = Null
    //     0x7b43d0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d570] Null
    //     0x7b43d4: ldr             x3, [x3, #0x570]
    // 0x7b43d8: r0 = RenderBox()
    //     0x7b43d8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b43dc: ldur            x1, [fp, #-8]
    // 0x7b43e0: ldur            x2, [fp, #-0x10]
    // 0x7b43e4: r0 = _removeFromChildList()
    //     0x7b43e4: bl              #0x7b440c  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7b43e8: ldur            x1, [fp, #-8]
    // 0x7b43ec: ldur            x2, [fp, #-0x10]
    // 0x7b43f0: r0 = dropChild()
    //     0x7b43f0: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x7b43f4: r0 = Null
    //     0x7b43f4: mov             x0, NULL
    // 0x7b43f8: LeaveFrame
    //     0x7b43f8: mov             SP, fp
    //     0x7b43fc: ldp             fp, lr, [SP], #0x10
    // 0x7b4400: ret
    //     0x7b4400: ret             
    // 0x7b4404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b4404: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b4408: b               #0x7b43a4
  }
  _ _removeFromChildList(/* No info */) {
    // ** addr: 0x7b440c, size: 0x2c8
    // 0x7b440c: EnterFrame
    //     0x7b440c: stp             fp, lr, [SP, #-0x10]!
    //     0x7b4410: mov             fp, SP
    // 0x7b4414: AllocStack(0x28)
    //     0x7b4414: sub             SP, SP, #0x28
    // 0x7b4418: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x10 */)
    //     0x7b4418: mov             x3, x1
    //     0x7b441c: stur            x1, [fp, #-0x10]
    // 0x7b4420: LoadField: r4 = r2->field_7
    //     0x7b4420: ldur            w4, [x2, #7]
    // 0x7b4424: DecompressPointer r4
    //     0x7b4424: add             x4, x4, HEAP, lsl #32
    // 0x7b4428: stur            x4, [fp, #-8]
    // 0x7b442c: cmp             w4, NULL
    // 0x7b4430: b.eq            #0x7b46c8
    // 0x7b4434: mov             x0, x4
    // 0x7b4438: r2 = Null
    //     0x7b4438: mov             x2, NULL
    // 0x7b443c: r1 = Null
    //     0x7b443c: mov             x1, NULL
    // 0x7b4440: r4 = LoadClassIdInstr(r0)
    //     0x7b4440: ldur            x4, [x0, #-1]
    //     0x7b4444: ubfx            x4, x4, #0xc, #0x14
    // 0x7b4448: cmp             x4, #0xc76
    // 0x7b444c: b.eq            #0x7b4464
    // 0x7b4450: r8 = CSSBoxParentData
    //     0x7b4450: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x7b4454: ldr             x8, [x8, #0x3d8]
    // 0x7b4458: r3 = Null
    //     0x7b4458: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d510] Null
    //     0x7b445c: ldr             x3, [x3, #0x510]
    // 0x7b4460: r0 = DefaultTypeTest()
    //     0x7b4460: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b4464: ldur            x3, [fp, #-8]
    // 0x7b4468: LoadField: r4 = r3->field_f
    //     0x7b4468: ldur            w4, [x3, #0xf]
    // 0x7b446c: DecompressPointer r4
    //     0x7b446c: add             x4, x4, HEAP, lsl #32
    // 0x7b4470: stur            x4, [fp, #-0x20]
    // 0x7b4474: cmp             w4, NULL
    // 0x7b4478: b.ne            #0x7b44a8
    // 0x7b447c: ldur            x5, [fp, #-0x10]
    // 0x7b4480: LoadField: r0 = r3->field_13
    //     0x7b4480: ldur            w0, [x3, #0x13]
    // 0x7b4484: DecompressPointer r0
    //     0x7b4484: add             x0, x0, HEAP, lsl #32
    // 0x7b4488: StoreField: r5->field_5f = r0
    //     0x7b4488: stur            w0, [x5, #0x5f]
    //     0x7b448c: ldurb           w16, [x5, #-1]
    //     0x7b4490: ldurb           w17, [x0, #-1]
    //     0x7b4494: and             x16, x17, x16, lsr #2
    //     0x7b4498: tst             x16, HEAP, lsr #32
    //     0x7b449c: b.eq            #0x7b44a4
    //     0x7b44a0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x7b44a4: b               #0x7b456c
    // 0x7b44a8: ldur            x5, [fp, #-0x10]
    // 0x7b44ac: LoadField: r6 = r4->field_7
    //     0x7b44ac: ldur            w6, [x4, #7]
    // 0x7b44b0: DecompressPointer r6
    //     0x7b44b0: add             x6, x6, HEAP, lsl #32
    // 0x7b44b4: stur            x6, [fp, #-0x18]
    // 0x7b44b8: cmp             w6, NULL
    // 0x7b44bc: b.eq            #0x7b46cc
    // 0x7b44c0: mov             x0, x6
    // 0x7b44c4: r2 = Null
    //     0x7b44c4: mov             x2, NULL
    // 0x7b44c8: r1 = Null
    //     0x7b44c8: mov             x1, NULL
    // 0x7b44cc: r4 = LoadClassIdInstr(r0)
    //     0x7b44cc: ldur            x4, [x0, #-1]
    //     0x7b44d0: ubfx            x4, x4, #0xc, #0x14
    // 0x7b44d4: cmp             x4, #0xc76
    // 0x7b44d8: b.eq            #0x7b44f0
    // 0x7b44dc: r8 = CSSBoxParentData
    //     0x7b44dc: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x7b44e0: ldr             x8, [x8, #0x3d8]
    // 0x7b44e4: r3 = Null
    //     0x7b44e4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d520] Null
    //     0x7b44e8: ldr             x3, [x3, #0x520]
    // 0x7b44ec: r0 = DefaultTypeTest()
    //     0x7b44ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b44f0: ldur            x3, [fp, #-8]
    // 0x7b44f4: LoadField: r4 = r3->field_13
    //     0x7b44f4: ldur            w4, [x3, #0x13]
    // 0x7b44f8: DecompressPointer r4
    //     0x7b44f8: add             x4, x4, HEAP, lsl #32
    // 0x7b44fc: ldur            x5, [fp, #-0x18]
    // 0x7b4500: stur            x4, [fp, #-0x28]
    // 0x7b4504: LoadField: r2 = r5->field_b
    //     0x7b4504: ldur            w2, [x5, #0xb]
    // 0x7b4508: DecompressPointer r2
    //     0x7b4508: add             x2, x2, HEAP, lsl #32
    // 0x7b450c: mov             x0, x4
    // 0x7b4510: r1 = Null
    //     0x7b4510: mov             x1, NULL
    // 0x7b4514: cmp             w0, NULL
    // 0x7b4518: b.eq            #0x7b4544
    // 0x7b451c: cmp             w2, NULL
    // 0x7b4520: b.eq            #0x7b4544
    // 0x7b4524: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b4524: ldur            w4, [x2, #0x17]
    // 0x7b4528: DecompressPointer r4
    //     0x7b4528: add             x4, x4, HEAP, lsl #32
    // 0x7b452c: r8 = X0? bound RenderObject
    //     0x7b452c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b4530: ldr             x8, [x8, #0x1a8]
    // 0x7b4534: LoadField: r9 = r4->field_7
    //     0x7b4534: ldur            x9, [x4, #7]
    // 0x7b4538: r3 = Null
    //     0x7b4538: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d530] Null
    //     0x7b453c: ldr             x3, [x3, #0x530]
    // 0x7b4540: blr             x9
    // 0x7b4544: ldur            x0, [fp, #-0x28]
    // 0x7b4548: ldur            x1, [fp, #-0x18]
    // 0x7b454c: StoreField: r1->field_13 = r0
    //     0x7b454c: stur            w0, [x1, #0x13]
    //     0x7b4550: ldurb           w16, [x1, #-1]
    //     0x7b4554: ldurb           w17, [x0, #-1]
    //     0x7b4558: and             x16, x17, x16, lsr #2
    //     0x7b455c: tst             x16, HEAP, lsr #32
    //     0x7b4560: b.eq            #0x7b4568
    //     0x7b4564: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b4568: ldur            x3, [fp, #-8]
    // 0x7b456c: LoadField: r0 = r3->field_13
    //     0x7b456c: ldur            w0, [x3, #0x13]
    // 0x7b4570: DecompressPointer r0
    //     0x7b4570: add             x0, x0, HEAP, lsl #32
    // 0x7b4574: cmp             w0, NULL
    // 0x7b4578: b.ne            #0x7b45a4
    // 0x7b457c: ldur            x4, [fp, #-0x10]
    // 0x7b4580: ldur            x0, [fp, #-0x20]
    // 0x7b4584: StoreField: r4->field_63 = r0
    //     0x7b4584: stur            w0, [x4, #0x63]
    //     0x7b4588: ldurb           w16, [x4, #-1]
    //     0x7b458c: ldurb           w17, [x0, #-1]
    //     0x7b4590: and             x16, x17, x16, lsr #2
    //     0x7b4594: tst             x16, HEAP, lsr #32
    //     0x7b4598: b.eq            #0x7b45a0
    //     0x7b459c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7b45a0: b               #0x7b465c
    // 0x7b45a4: ldur            x4, [fp, #-0x10]
    // 0x7b45a8: LoadField: r5 = r0->field_7
    //     0x7b45a8: ldur            w5, [x0, #7]
    // 0x7b45ac: DecompressPointer r5
    //     0x7b45ac: add             x5, x5, HEAP, lsl #32
    // 0x7b45b0: stur            x5, [fp, #-0x18]
    // 0x7b45b4: cmp             w5, NULL
    // 0x7b45b8: b.eq            #0x7b46d0
    // 0x7b45bc: mov             x0, x5
    // 0x7b45c0: r2 = Null
    //     0x7b45c0: mov             x2, NULL
    // 0x7b45c4: r1 = Null
    //     0x7b45c4: mov             x1, NULL
    // 0x7b45c8: r4 = LoadClassIdInstr(r0)
    //     0x7b45c8: ldur            x4, [x0, #-1]
    //     0x7b45cc: ubfx            x4, x4, #0xc, #0x14
    // 0x7b45d0: cmp             x4, #0xc76
    // 0x7b45d4: b.eq            #0x7b45ec
    // 0x7b45d8: r8 = CSSBoxParentData
    //     0x7b45d8: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x7b45dc: ldr             x8, [x8, #0x3d8]
    // 0x7b45e0: r3 = Null
    //     0x7b45e0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d540] Null
    //     0x7b45e4: ldr             x3, [x3, #0x540]
    // 0x7b45e8: r0 = DefaultTypeTest()
    //     0x7b45e8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b45ec: ldur            x3, [fp, #-0x18]
    // 0x7b45f0: LoadField: r2 = r3->field_b
    //     0x7b45f0: ldur            w2, [x3, #0xb]
    // 0x7b45f4: DecompressPointer r2
    //     0x7b45f4: add             x2, x2, HEAP, lsl #32
    // 0x7b45f8: ldur            x0, [fp, #-0x20]
    // 0x7b45fc: r1 = Null
    //     0x7b45fc: mov             x1, NULL
    // 0x7b4600: cmp             w0, NULL
    // 0x7b4604: b.eq            #0x7b4630
    // 0x7b4608: cmp             w2, NULL
    // 0x7b460c: b.eq            #0x7b4630
    // 0x7b4610: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b4610: ldur            w4, [x2, #0x17]
    // 0x7b4614: DecompressPointer r4
    //     0x7b4614: add             x4, x4, HEAP, lsl #32
    // 0x7b4618: r8 = X0? bound RenderObject
    //     0x7b4618: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b461c: ldr             x8, [x8, #0x1a8]
    // 0x7b4620: LoadField: r9 = r4->field_7
    //     0x7b4620: ldur            x9, [x4, #7]
    // 0x7b4624: r3 = Null
    //     0x7b4624: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d550] Null
    //     0x7b4628: ldr             x3, [x3, #0x550]
    // 0x7b462c: blr             x9
    // 0x7b4630: ldur            x0, [fp, #-0x20]
    // 0x7b4634: ldur            x1, [fp, #-0x18]
    // 0x7b4638: StoreField: r1->field_f = r0
    //     0x7b4638: stur            w0, [x1, #0xf]
    //     0x7b463c: ldurb           w16, [x1, #-1]
    //     0x7b4640: ldurb           w17, [x0, #-1]
    //     0x7b4644: and             x16, x17, x16, lsr #2
    //     0x7b4648: tst             x16, HEAP, lsr #32
    //     0x7b464c: b.eq            #0x7b4654
    //     0x7b4650: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b4654: ldur            x4, [fp, #-0x10]
    // 0x7b4658: ldur            x3, [fp, #-8]
    // 0x7b465c: LoadField: r2 = r3->field_b
    //     0x7b465c: ldur            w2, [x3, #0xb]
    // 0x7b4660: DecompressPointer r2
    //     0x7b4660: add             x2, x2, HEAP, lsl #32
    // 0x7b4664: r0 = Null
    //     0x7b4664: mov             x0, NULL
    // 0x7b4668: r1 = Null
    //     0x7b4668: mov             x1, NULL
    // 0x7b466c: cmp             w0, NULL
    // 0x7b4670: b.eq            #0x7b469c
    // 0x7b4674: cmp             w2, NULL
    // 0x7b4678: b.eq            #0x7b469c
    // 0x7b467c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b467c: ldur            w4, [x2, #0x17]
    // 0x7b4680: DecompressPointer r4
    //     0x7b4680: add             x4, x4, HEAP, lsl #32
    // 0x7b4684: r8 = X0? bound RenderObject
    //     0x7b4684: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b4688: ldr             x8, [x8, #0x1a8]
    // 0x7b468c: LoadField: r9 = r4->field_7
    //     0x7b468c: ldur            x9, [x4, #7]
    // 0x7b4690: r3 = Null
    //     0x7b4690: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d560] Null
    //     0x7b4694: ldr             x3, [x3, #0x560]
    // 0x7b4698: blr             x9
    // 0x7b469c: ldur            x1, [fp, #-8]
    // 0x7b46a0: StoreField: r1->field_f = rNULL
    //     0x7b46a0: stur            NULL, [x1, #0xf]
    // 0x7b46a4: StoreField: r1->field_13 = rNULL
    //     0x7b46a4: stur            NULL, [x1, #0x13]
    // 0x7b46a8: ldur            x1, [fp, #-0x10]
    // 0x7b46ac: LoadField: r2 = r1->field_57
    //     0x7b46ac: ldur            x2, [x1, #0x57]
    // 0x7b46b0: sub             x3, x2, #1
    // 0x7b46b4: StoreField: r1->field_57 = r3
    //     0x7b46b4: stur            x3, [x1, #0x57]
    // 0x7b46b8: r0 = Null
    //     0x7b46b8: mov             x0, NULL
    // 0x7b46bc: LeaveFrame
    //     0x7b46bc: mov             SP, fp
    //     0x7b46c0: ldp             fp, lr, [SP], #0x10
    // 0x7b46c4: ret
    //     0x7b46c4: ret             
    // 0x7b46c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b46c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b46cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b46cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b46d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b46d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ move(/* No info */) {
    // ** addr: 0x7cea68, size: 0x160
    // 0x7cea68: EnterFrame
    //     0x7cea68: stp             fp, lr, [SP, #-0x10]!
    //     0x7cea6c: mov             fp, SP
    // 0x7cea70: AllocStack(0x30)
    //     0x7cea70: sub             SP, SP, #0x30
    // 0x7cea74: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7cea74: mov             x5, x1
    //     0x7cea78: mov             x4, x2
    //     0x7cea7c: stur            x1, [fp, #-8]
    //     0x7cea80: stur            x2, [fp, #-0x10]
    //     0x7cea84: stur            x3, [fp, #-0x18]
    // 0x7cea88: CheckStackOverflow
    //     0x7cea88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cea8c: cmp             SP, x16
    //     0x7cea90: b.ls            #0x7cebbc
    // 0x7cea94: mov             x0, x4
    // 0x7cea98: r2 = Null
    //     0x7cea98: mov             x2, NULL
    // 0x7cea9c: r1 = Null
    //     0x7cea9c: mov             x1, NULL
    // 0x7ceaa0: r4 = 60
    //     0x7ceaa0: movz            x4, #0x3c
    // 0x7ceaa4: branchIfSmi(r0, 0x7ceab0)
    //     0x7ceaa4: tbz             w0, #0, #0x7ceab0
    // 0x7ceaa8: r4 = LoadClassIdInstr(r0)
    //     0x7ceaa8: ldur            x4, [x0, #-1]
    //     0x7ceaac: ubfx            x4, x4, #0xc, #0x14
    // 0x7ceab0: sub             x4, x4, #0xbba
    // 0x7ceab4: cmp             x4, #0x9a
    // 0x7ceab8: b.ls            #0x7ceacc
    // 0x7ceabc: r8 = RenderBox
    //     0x7ceabc: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7ceac0: r3 = Null
    //     0x7ceac0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d420] Null
    //     0x7ceac4: ldr             x3, [x3, #0x420]
    // 0x7ceac8: r0 = RenderBox()
    //     0x7ceac8: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7ceacc: ldur            x0, [fp, #-0x18]
    // 0x7cead0: r2 = Null
    //     0x7cead0: mov             x2, NULL
    // 0x7cead4: r1 = Null
    //     0x7cead4: mov             x1, NULL
    // 0x7cead8: r4 = 60
    //     0x7cead8: movz            x4, #0x3c
    // 0x7ceadc: branchIfSmi(r0, 0x7ceae8)
    //     0x7ceadc: tbz             w0, #0, #0x7ceae8
    // 0x7ceae0: r4 = LoadClassIdInstr(r0)
    //     0x7ceae0: ldur            x4, [x0, #-1]
    //     0x7ceae4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ceae8: sub             x4, x4, #0xbba
    // 0x7ceaec: cmp             x4, #0x9a
    // 0x7ceaf0: b.ls            #0x7ceb04
    // 0x7ceaf4: r8 = RenderBox?
    //     0x7ceaf4: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7ceaf8: r3 = Null
    //     0x7ceaf8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d430] Null
    //     0x7ceafc: ldr             x3, [x3, #0x430]
    // 0x7ceb00: r0 = RenderBox?()
    //     0x7ceb00: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7ceb04: ldur            x3, [fp, #-0x10]
    // 0x7ceb08: LoadField: r4 = r3->field_7
    //     0x7ceb08: ldur            w4, [x3, #7]
    // 0x7ceb0c: DecompressPointer r4
    //     0x7ceb0c: add             x4, x4, HEAP, lsl #32
    // 0x7ceb10: stur            x4, [fp, #-0x20]
    // 0x7ceb14: cmp             w4, NULL
    // 0x7ceb18: b.eq            #0x7cebc4
    // 0x7ceb1c: mov             x0, x4
    // 0x7ceb20: r2 = Null
    //     0x7ceb20: mov             x2, NULL
    // 0x7ceb24: r1 = Null
    //     0x7ceb24: mov             x1, NULL
    // 0x7ceb28: r4 = LoadClassIdInstr(r0)
    //     0x7ceb28: ldur            x4, [x0, #-1]
    //     0x7ceb2c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ceb30: cmp             x4, #0xc76
    // 0x7ceb34: b.eq            #0x7ceb4c
    // 0x7ceb38: r8 = CSSBoxParentData
    //     0x7ceb38: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x7ceb3c: ldr             x8, [x8, #0x3d8]
    // 0x7ceb40: r3 = Null
    //     0x7ceb40: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d440] Null
    //     0x7ceb44: ldr             x3, [x3, #0x440]
    // 0x7ceb48: r0 = DefaultTypeTest()
    //     0x7ceb48: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7ceb4c: ldur            x0, [fp, #-0x20]
    // 0x7ceb50: LoadField: r1 = r0->field_f
    //     0x7ceb50: ldur            w1, [x0, #0xf]
    // 0x7ceb54: DecompressPointer r1
    //     0x7ceb54: add             x1, x1, HEAP, lsl #32
    // 0x7ceb58: r0 = LoadClassIdInstr(r1)
    //     0x7ceb58: ldur            x0, [x1, #-1]
    //     0x7ceb5c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ceb60: ldur            x16, [fp, #-0x18]
    // 0x7ceb64: stp             x16, x1, [SP]
    // 0x7ceb68: mov             lr, x0
    // 0x7ceb6c: ldr             lr, [x21, lr, lsl #3]
    // 0x7ceb70: blr             lr
    // 0x7ceb74: tbnz            w0, #4, #0x7ceb88
    // 0x7ceb78: r0 = Null
    //     0x7ceb78: mov             x0, NULL
    // 0x7ceb7c: LeaveFrame
    //     0x7ceb7c: mov             SP, fp
    //     0x7ceb80: ldp             fp, lr, [SP], #0x10
    // 0x7ceb84: ret
    //     0x7ceb84: ret             
    // 0x7ceb88: ldur            x1, [fp, #-8]
    // 0x7ceb8c: ldur            x2, [fp, #-0x10]
    // 0x7ceb90: r0 = _removeFromChildList()
    //     0x7ceb90: bl              #0x7b440c  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7ceb94: ldur            x1, [fp, #-8]
    // 0x7ceb98: ldur            x2, [fp, #-0x10]
    // 0x7ceb9c: ldur            x3, [fp, #-0x18]
    // 0x7ceba0: r0 = _insertIntoChildList()
    //     0x7ceba0: bl              #0xda7aa8  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7ceba4: ldur            x1, [fp, #-8]
    // 0x7ceba8: r0 = markNeedsLayout()
    //     0x7ceba8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x7cebac: r0 = Null
    //     0x7cebac: mov             x0, NULL
    // 0x7cebb0: LeaveFrame
    //     0x7cebb0: mov             SP, fp
    //     0x7cebb4: ldp             fp, lr, [SP], #0x10
    // 0x7cebb8: ret
    //     0x7cebb8: ret             
    // 0x7cebbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7cebbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7cebc0: b               #0x7cea94
    // 0x7cebc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7cebc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x807ee0, size: 0xe8
    // 0x807ee0: EnterFrame
    //     0x807ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x807ee4: mov             fp, SP
    // 0x807ee8: AllocStack(0x10)
    //     0x807ee8: sub             SP, SP, #0x10
    // 0x807eec: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x807eec: mov             x0, x1
    //     0x807ef0: stur            x1, [fp, #-8]
    // 0x807ef4: CheckStackOverflow
    //     0x807ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807ef8: cmp             SP, x16
    //     0x807efc: b.ls            #0x807fb4
    // 0x807f00: mov             x1, x0
    // 0x807f04: r0 = detach()
    //     0x807f04: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x807f08: ldur            x0, [fp, #-8]
    // 0x807f0c: LoadField: r1 = r0->field_5f
    //     0x807f0c: ldur            w1, [x0, #0x5f]
    // 0x807f10: DecompressPointer r1
    //     0x807f10: add             x1, x1, HEAP, lsl #32
    // 0x807f14: mov             x2, x1
    // 0x807f18: stur            x2, [fp, #-8]
    // 0x807f1c: CheckStackOverflow
    //     0x807f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807f20: cmp             SP, x16
    //     0x807f24: b.ls            #0x807fbc
    // 0x807f28: cmp             w2, NULL
    // 0x807f2c: b.eq            #0x807fa4
    // 0x807f30: r0 = LoadClassIdInstr(r2)
    //     0x807f30: ldur            x0, [x2, #-1]
    //     0x807f34: ubfx            x0, x0, #0xc, #0x14
    // 0x807f38: mov             x1, x2
    // 0x807f3c: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x807f3c: movz            x17, #0xeec9
    //     0x807f40: add             lr, x0, x17
    //     0x807f44: ldr             lr, [x21, lr, lsl #3]
    //     0x807f48: blr             lr
    // 0x807f4c: ldur            x0, [fp, #-8]
    // 0x807f50: LoadField: r3 = r0->field_7
    //     0x807f50: ldur            w3, [x0, #7]
    // 0x807f54: DecompressPointer r3
    //     0x807f54: add             x3, x3, HEAP, lsl #32
    // 0x807f58: stur            x3, [fp, #-0x10]
    // 0x807f5c: cmp             w3, NULL
    // 0x807f60: b.eq            #0x807fc4
    // 0x807f64: mov             x0, x3
    // 0x807f68: r2 = Null
    //     0x807f68: mov             x2, NULL
    // 0x807f6c: r1 = Null
    //     0x807f6c: mov             x1, NULL
    // 0x807f70: r4 = LoadClassIdInstr(r0)
    //     0x807f70: ldur            x4, [x0, #-1]
    //     0x807f74: ubfx            x4, x4, #0xc, #0x14
    // 0x807f78: cmp             x4, #0xc76
    // 0x807f7c: b.eq            #0x807f94
    // 0x807f80: r8 = CSSBoxParentData
    //     0x807f80: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x807f84: ldr             x8, [x8, #0x3d8]
    // 0x807f88: r3 = Null
    //     0x807f88: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d400] Null
    //     0x807f8c: ldr             x3, [x3, #0x400]
    // 0x807f90: r0 = DefaultTypeTest()
    //     0x807f90: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x807f94: ldur            x1, [fp, #-0x10]
    // 0x807f98: LoadField: r2 = r1->field_13
    //     0x807f98: ldur            w2, [x1, #0x13]
    // 0x807f9c: DecompressPointer r2
    //     0x807f9c: add             x2, x2, HEAP, lsl #32
    // 0x807fa0: b               #0x807f18
    // 0x807fa4: r0 = Null
    //     0x807fa4: mov             x0, NULL
    // 0x807fa8: LeaveFrame
    //     0x807fa8: mov             SP, fp
    //     0x807fac: ldp             fp, lr, [SP], #0x10
    // 0x807fb0: ret
    //     0x807fb0: ret             
    // 0x807fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807fb8: b               #0x807f00
    // 0x807fbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807fbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807fc0: b               #0x807f28
    // 0x807fc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x807fc4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x809340, size: 0xf8
    // 0x809340: EnterFrame
    //     0x809340: stp             fp, lr, [SP, #-0x10]!
    //     0x809344: mov             fp, SP
    // 0x809348: AllocStack(0x18)
    //     0x809348: sub             SP, SP, #0x18
    // 0x80934c: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r2, fp-0x10 */)
    //     0x80934c: mov             x2, x1
    //     0x809350: stur            x1, [fp, #-0x10]
    // 0x809354: CheckStackOverflow
    //     0x809354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x809358: cmp             SP, x16
    //     0x80935c: b.ls            #0x809424
    // 0x809360: LoadField: r0 = r2->field_5f
    //     0x809360: ldur            w0, [x2, #0x5f]
    // 0x809364: DecompressPointer r0
    //     0x809364: add             x0, x0, HEAP, lsl #32
    // 0x809368: mov             x3, x0
    // 0x80936c: stur            x3, [fp, #-8]
    // 0x809370: CheckStackOverflow
    //     0x809370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x809374: cmp             SP, x16
    //     0x809378: b.ls            #0x80942c
    // 0x80937c: cmp             w3, NULL
    // 0x809380: b.eq            #0x809414
    // 0x809384: LoadField: r0 = r3->field_b
    //     0x809384: ldur            x0, [x3, #0xb]
    // 0x809388: LoadField: r1 = r2->field_b
    //     0x809388: ldur            x1, [x2, #0xb]
    // 0x80938c: cmp             x0, x1
    // 0x809390: b.gt            #0x8093b8
    // 0x809394: add             x0, x1, #1
    // 0x809398: StoreField: r3->field_b = r0
    //     0x809398: stur            x0, [x3, #0xb]
    // 0x80939c: r0 = LoadClassIdInstr(r3)
    //     0x80939c: ldur            x0, [x3, #-1]
    //     0x8093a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8093a4: mov             x1, x3
    // 0x8093a8: r0 = GDT[cid_x0 + 0xedec]()
    //     0x8093a8: movz            x17, #0xedec
    //     0x8093ac: add             lr, x0, x17
    //     0x8093b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8093b4: blr             lr
    // 0x8093b8: ldur            x0, [fp, #-8]
    // 0x8093bc: LoadField: r3 = r0->field_7
    //     0x8093bc: ldur            w3, [x0, #7]
    // 0x8093c0: DecompressPointer r3
    //     0x8093c0: add             x3, x3, HEAP, lsl #32
    // 0x8093c4: stur            x3, [fp, #-0x18]
    // 0x8093c8: cmp             w3, NULL
    // 0x8093cc: b.eq            #0x809434
    // 0x8093d0: mov             x0, x3
    // 0x8093d4: r2 = Null
    //     0x8093d4: mov             x2, NULL
    // 0x8093d8: r1 = Null
    //     0x8093d8: mov             x1, NULL
    // 0x8093dc: r4 = LoadClassIdInstr(r0)
    //     0x8093dc: ldur            x4, [x0, #-1]
    //     0x8093e0: ubfx            x4, x4, #0xc, #0x14
    // 0x8093e4: cmp             x4, #0xc76
    // 0x8093e8: b.eq            #0x809400
    // 0x8093ec: r8 = CSSBoxParentData
    //     0x8093ec: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x8093f0: ldr             x8, [x8, #0x3d8]
    // 0x8093f4: r3 = Null
    //     0x8093f4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d3f0] Null
    //     0x8093f8: ldr             x3, [x3, #0x3f0]
    // 0x8093fc: r0 = DefaultTypeTest()
    //     0x8093fc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x809400: ldur            x1, [fp, #-0x18]
    // 0x809404: LoadField: r3 = r1->field_13
    //     0x809404: ldur            w3, [x1, #0x13]
    // 0x809408: DecompressPointer r3
    //     0x809408: add             x3, x3, HEAP, lsl #32
    // 0x80940c: ldur            x2, [fp, #-0x10]
    // 0x809410: b               #0x80936c
    // 0x809414: r0 = Null
    //     0x809414: mov             x0, NULL
    // 0x809418: LeaveFrame
    //     0x809418: mov             SP, fp
    //     0x80941c: ldp             fp, lr, [SP], #0x10
    // 0x809420: ret
    //     0x809420: ret             
    // 0x809424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x809424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809428: b               #0x809360
    // 0x80942c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80942c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809430: b               #0x80937c
    // 0x809434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x809434: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _insertIntoChildList(/* No info */) {
    // ** addr: 0xda7aa8, size: 0x570
    // 0xda7aa8: EnterFrame
    //     0xda7aa8: stp             fp, lr, [SP, #-0x10]!
    //     0xda7aac: mov             fp, SP
    // 0xda7ab0: AllocStack(0x30)
    //     0xda7ab0: sub             SP, SP, #0x30
    // 0xda7ab4: SetupParameters(_RenderCSSBox&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xda7ab4: mov             x5, x1
    //     0xda7ab8: mov             x4, x2
    //     0xda7abc: stur            x1, [fp, #-0x10]
    //     0xda7ac0: stur            x2, [fp, #-0x18]
    //     0xda7ac4: stur            x3, [fp, #-0x20]
    // 0xda7ac8: LoadField: r6 = r4->field_7
    //     0xda7ac8: ldur            w6, [x4, #7]
    // 0xda7acc: DecompressPointer r6
    //     0xda7acc: add             x6, x6, HEAP, lsl #32
    // 0xda7ad0: stur            x6, [fp, #-8]
    // 0xda7ad4: cmp             w6, NULL
    // 0xda7ad8: b.eq            #0xda8008
    // 0xda7adc: mov             x0, x6
    // 0xda7ae0: r2 = Null
    //     0xda7ae0: mov             x2, NULL
    // 0xda7ae4: r1 = Null
    //     0xda7ae4: mov             x1, NULL
    // 0xda7ae8: r4 = LoadClassIdInstr(r0)
    //     0xda7ae8: ldur            x4, [x0, #-1]
    //     0xda7aec: ubfx            x4, x4, #0xc, #0x14
    // 0xda7af0: cmp             x4, #0xc76
    // 0xda7af4: b.eq            #0xda7b0c
    // 0xda7af8: r8 = CSSBoxParentData
    //     0xda7af8: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0xda7afc: ldr             x8, [x8, #0x3d8]
    // 0xda7b00: r3 = Null
    //     0xda7b00: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d450] Null
    //     0xda7b04: ldr             x3, [x3, #0x450]
    // 0xda7b08: r0 = DefaultTypeTest()
    //     0xda7b08: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda7b0c: ldur            x3, [fp, #-0x10]
    // 0xda7b10: LoadField: r0 = r3->field_57
    //     0xda7b10: ldur            x0, [x3, #0x57]
    // 0xda7b14: add             x1, x0, #1
    // 0xda7b18: StoreField: r3->field_57 = r1
    //     0xda7b18: stur            x1, [x3, #0x57]
    // 0xda7b1c: ldur            x4, [fp, #-0x20]
    // 0xda7b20: cmp             w4, NULL
    // 0xda7b24: b.ne            #0xda7cac
    // 0xda7b28: ldur            x4, [fp, #-8]
    // 0xda7b2c: LoadField: r5 = r3->field_5f
    //     0xda7b2c: ldur            w5, [x3, #0x5f]
    // 0xda7b30: DecompressPointer r5
    //     0xda7b30: add             x5, x5, HEAP, lsl #32
    // 0xda7b34: stur            x5, [fp, #-0x28]
    // 0xda7b38: LoadField: r2 = r4->field_b
    //     0xda7b38: ldur            w2, [x4, #0xb]
    // 0xda7b3c: DecompressPointer r2
    //     0xda7b3c: add             x2, x2, HEAP, lsl #32
    // 0xda7b40: mov             x0, x5
    // 0xda7b44: r1 = Null
    //     0xda7b44: mov             x1, NULL
    // 0xda7b48: cmp             w0, NULL
    // 0xda7b4c: b.eq            #0xda7b78
    // 0xda7b50: cmp             w2, NULL
    // 0xda7b54: b.eq            #0xda7b78
    // 0xda7b58: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7b58: ldur            w4, [x2, #0x17]
    // 0xda7b5c: DecompressPointer r4
    //     0xda7b5c: add             x4, x4, HEAP, lsl #32
    // 0xda7b60: r8 = X0? bound RenderObject
    //     0xda7b60: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7b64: ldr             x8, [x8, #0x1a8]
    // 0xda7b68: LoadField: r9 = r4->field_7
    //     0xda7b68: ldur            x9, [x4, #7]
    // 0xda7b6c: r3 = Null
    //     0xda7b6c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d460] Null
    //     0xda7b70: ldr             x3, [x3, #0x460]
    // 0xda7b74: blr             x9
    // 0xda7b78: ldur            x0, [fp, #-0x28]
    // 0xda7b7c: ldur            x3, [fp, #-8]
    // 0xda7b80: StoreField: r3->field_13 = r0
    //     0xda7b80: stur            w0, [x3, #0x13]
    //     0xda7b84: ldurb           w16, [x3, #-1]
    //     0xda7b88: ldurb           w17, [x0, #-1]
    //     0xda7b8c: and             x16, x17, x16, lsr #2
    //     0xda7b90: tst             x16, HEAP, lsr #32
    //     0xda7b94: b.eq            #0xda7b9c
    //     0xda7b98: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda7b9c: ldur            x0, [fp, #-0x28]
    // 0xda7ba0: cmp             w0, NULL
    // 0xda7ba4: b.eq            #0xda7c54
    // 0xda7ba8: LoadField: r3 = r0->field_7
    //     0xda7ba8: ldur            w3, [x0, #7]
    // 0xda7bac: DecompressPointer r3
    //     0xda7bac: add             x3, x3, HEAP, lsl #32
    // 0xda7bb0: stur            x3, [fp, #-0x30]
    // 0xda7bb4: cmp             w3, NULL
    // 0xda7bb8: b.eq            #0xda800c
    // 0xda7bbc: mov             x0, x3
    // 0xda7bc0: r2 = Null
    //     0xda7bc0: mov             x2, NULL
    // 0xda7bc4: r1 = Null
    //     0xda7bc4: mov             x1, NULL
    // 0xda7bc8: r4 = LoadClassIdInstr(r0)
    //     0xda7bc8: ldur            x4, [x0, #-1]
    //     0xda7bcc: ubfx            x4, x4, #0xc, #0x14
    // 0xda7bd0: cmp             x4, #0xc76
    // 0xda7bd4: b.eq            #0xda7bec
    // 0xda7bd8: r8 = CSSBoxParentData
    //     0xda7bd8: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0xda7bdc: ldr             x8, [x8, #0x3d8]
    // 0xda7be0: r3 = Null
    //     0xda7be0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d470] Null
    //     0xda7be4: ldr             x3, [x3, #0x470]
    // 0xda7be8: r0 = DefaultTypeTest()
    //     0xda7be8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda7bec: ldur            x3, [fp, #-0x30]
    // 0xda7bf0: LoadField: r2 = r3->field_b
    //     0xda7bf0: ldur            w2, [x3, #0xb]
    // 0xda7bf4: DecompressPointer r2
    //     0xda7bf4: add             x2, x2, HEAP, lsl #32
    // 0xda7bf8: ldur            x0, [fp, #-0x18]
    // 0xda7bfc: r1 = Null
    //     0xda7bfc: mov             x1, NULL
    // 0xda7c00: cmp             w0, NULL
    // 0xda7c04: b.eq            #0xda7c30
    // 0xda7c08: cmp             w2, NULL
    // 0xda7c0c: b.eq            #0xda7c30
    // 0xda7c10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7c10: ldur            w4, [x2, #0x17]
    // 0xda7c14: DecompressPointer r4
    //     0xda7c14: add             x4, x4, HEAP, lsl #32
    // 0xda7c18: r8 = X0? bound RenderObject
    //     0xda7c18: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7c1c: ldr             x8, [x8, #0x1a8]
    // 0xda7c20: LoadField: r9 = r4->field_7
    //     0xda7c20: ldur            x9, [x4, #7]
    // 0xda7c24: r3 = Null
    //     0xda7c24: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d480] Null
    //     0xda7c28: ldr             x3, [x3, #0x480]
    // 0xda7c2c: blr             x9
    // 0xda7c30: ldur            x0, [fp, #-0x18]
    // 0xda7c34: ldur            x1, [fp, #-0x30]
    // 0xda7c38: StoreField: r1->field_f = r0
    //     0xda7c38: stur            w0, [x1, #0xf]
    //     0xda7c3c: ldurb           w16, [x1, #-1]
    //     0xda7c40: ldurb           w17, [x0, #-1]
    //     0xda7c44: and             x16, x17, x16, lsr #2
    //     0xda7c48: tst             x16, HEAP, lsr #32
    //     0xda7c4c: b.eq            #0xda7c54
    //     0xda7c50: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7c54: ldur            x5, [fp, #-0x10]
    // 0xda7c58: ldur            x0, [fp, #-0x18]
    // 0xda7c5c: StoreField: r5->field_5f = r0
    //     0xda7c5c: stur            w0, [x5, #0x5f]
    //     0xda7c60: ldurb           w16, [x5, #-1]
    //     0xda7c64: ldurb           w17, [x0, #-1]
    //     0xda7c68: and             x16, x17, x16, lsr #2
    //     0xda7c6c: tst             x16, HEAP, lsr #32
    //     0xda7c70: b.eq            #0xda7c78
    //     0xda7c74: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda7c78: LoadField: r0 = r5->field_63
    //     0xda7c78: ldur            w0, [x5, #0x63]
    // 0xda7c7c: DecompressPointer r0
    //     0xda7c7c: add             x0, x0, HEAP, lsl #32
    // 0xda7c80: cmp             w0, NULL
    // 0xda7c84: b.ne            #0xda7ff8
    // 0xda7c88: ldur            x0, [fp, #-0x18]
    // 0xda7c8c: StoreField: r5->field_63 = r0
    //     0xda7c8c: stur            w0, [x5, #0x63]
    //     0xda7c90: ldurb           w16, [x5, #-1]
    //     0xda7c94: ldurb           w17, [x0, #-1]
    //     0xda7c98: and             x16, x17, x16, lsr #2
    //     0xda7c9c: tst             x16, HEAP, lsr #32
    //     0xda7ca0: b.eq            #0xda7ca8
    //     0xda7ca4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda7ca8: b               #0xda7ff8
    // 0xda7cac: mov             x5, x3
    // 0xda7cb0: ldur            x3, [fp, #-8]
    // 0xda7cb4: LoadField: r6 = r4->field_7
    //     0xda7cb4: ldur            w6, [x4, #7]
    // 0xda7cb8: DecompressPointer r6
    //     0xda7cb8: add             x6, x6, HEAP, lsl #32
    // 0xda7cbc: stur            x6, [fp, #-0x28]
    // 0xda7cc0: cmp             w6, NULL
    // 0xda7cc4: b.eq            #0xda8010
    // 0xda7cc8: mov             x0, x6
    // 0xda7ccc: r2 = Null
    //     0xda7ccc: mov             x2, NULL
    // 0xda7cd0: r1 = Null
    //     0xda7cd0: mov             x1, NULL
    // 0xda7cd4: r4 = LoadClassIdInstr(r0)
    //     0xda7cd4: ldur            x4, [x0, #-1]
    //     0xda7cd8: ubfx            x4, x4, #0xc, #0x14
    // 0xda7cdc: cmp             x4, #0xc76
    // 0xda7ce0: b.eq            #0xda7cf8
    // 0xda7ce4: r8 = CSSBoxParentData
    //     0xda7ce4: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0xda7ce8: ldr             x8, [x8, #0x3d8]
    // 0xda7cec: r3 = Null
    //     0xda7cec: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d490] Null
    //     0xda7cf0: ldr             x3, [x3, #0x490]
    // 0xda7cf4: r0 = DefaultTypeTest()
    //     0xda7cf4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda7cf8: ldur            x3, [fp, #-0x28]
    // 0xda7cfc: LoadField: r4 = r3->field_13
    //     0xda7cfc: ldur            w4, [x3, #0x13]
    // 0xda7d00: DecompressPointer r4
    //     0xda7d00: add             x4, x4, HEAP, lsl #32
    // 0xda7d04: stur            x4, [fp, #-0x30]
    // 0xda7d08: cmp             w4, NULL
    // 0xda7d0c: b.ne            #0xda7e0c
    // 0xda7d10: ldur            x5, [fp, #-0x10]
    // 0xda7d14: ldur            x4, [fp, #-8]
    // 0xda7d18: LoadField: r2 = r4->field_b
    //     0xda7d18: ldur            w2, [x4, #0xb]
    // 0xda7d1c: DecompressPointer r2
    //     0xda7d1c: add             x2, x2, HEAP, lsl #32
    // 0xda7d20: ldur            x0, [fp, #-0x20]
    // 0xda7d24: r1 = Null
    //     0xda7d24: mov             x1, NULL
    // 0xda7d28: cmp             w0, NULL
    // 0xda7d2c: b.eq            #0xda7d58
    // 0xda7d30: cmp             w2, NULL
    // 0xda7d34: b.eq            #0xda7d58
    // 0xda7d38: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7d38: ldur            w4, [x2, #0x17]
    // 0xda7d3c: DecompressPointer r4
    //     0xda7d3c: add             x4, x4, HEAP, lsl #32
    // 0xda7d40: r8 = X0? bound RenderObject
    //     0xda7d40: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7d44: ldr             x8, [x8, #0x1a8]
    // 0xda7d48: LoadField: r9 = r4->field_7
    //     0xda7d48: ldur            x9, [x4, #7]
    // 0xda7d4c: r3 = Null
    //     0xda7d4c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4a0] Null
    //     0xda7d50: ldr             x3, [x3, #0x4a0]
    // 0xda7d54: blr             x9
    // 0xda7d58: ldur            x0, [fp, #-0x20]
    // 0xda7d5c: ldur            x3, [fp, #-8]
    // 0xda7d60: StoreField: r3->field_f = r0
    //     0xda7d60: stur            w0, [x3, #0xf]
    //     0xda7d64: ldurb           w16, [x3, #-1]
    //     0xda7d68: ldurb           w17, [x0, #-1]
    //     0xda7d6c: and             x16, x17, x16, lsr #2
    //     0xda7d70: tst             x16, HEAP, lsr #32
    //     0xda7d74: b.eq            #0xda7d7c
    //     0xda7d78: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda7d7c: ldur            x3, [fp, #-0x28]
    // 0xda7d80: LoadField: r2 = r3->field_b
    //     0xda7d80: ldur            w2, [x3, #0xb]
    // 0xda7d84: DecompressPointer r2
    //     0xda7d84: add             x2, x2, HEAP, lsl #32
    // 0xda7d88: ldur            x0, [fp, #-0x18]
    // 0xda7d8c: r1 = Null
    //     0xda7d8c: mov             x1, NULL
    // 0xda7d90: cmp             w0, NULL
    // 0xda7d94: b.eq            #0xda7dc0
    // 0xda7d98: cmp             w2, NULL
    // 0xda7d9c: b.eq            #0xda7dc0
    // 0xda7da0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7da0: ldur            w4, [x2, #0x17]
    // 0xda7da4: DecompressPointer r4
    //     0xda7da4: add             x4, x4, HEAP, lsl #32
    // 0xda7da8: r8 = X0? bound RenderObject
    //     0xda7da8: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7dac: ldr             x8, [x8, #0x1a8]
    // 0xda7db0: LoadField: r9 = r4->field_7
    //     0xda7db0: ldur            x9, [x4, #7]
    // 0xda7db4: r3 = Null
    //     0xda7db4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4b0] Null
    //     0xda7db8: ldr             x3, [x3, #0x4b0]
    // 0xda7dbc: blr             x9
    // 0xda7dc0: ldur            x0, [fp, #-0x18]
    // 0xda7dc4: ldur            x5, [fp, #-0x28]
    // 0xda7dc8: StoreField: r5->field_13 = r0
    //     0xda7dc8: stur            w0, [x5, #0x13]
    //     0xda7dcc: ldurb           w16, [x5, #-1]
    //     0xda7dd0: ldurb           w17, [x0, #-1]
    //     0xda7dd4: and             x16, x17, x16, lsr #2
    //     0xda7dd8: tst             x16, HEAP, lsr #32
    //     0xda7ddc: b.eq            #0xda7de4
    //     0xda7de0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda7de4: ldur            x0, [fp, #-0x18]
    // 0xda7de8: ldur            x1, [fp, #-0x10]
    // 0xda7dec: StoreField: r1->field_63 = r0
    //     0xda7dec: stur            w0, [x1, #0x63]
    //     0xda7df0: ldurb           w16, [x1, #-1]
    //     0xda7df4: ldurb           w17, [x0, #-1]
    //     0xda7df8: and             x16, x17, x16, lsr #2
    //     0xda7dfc: tst             x16, HEAP, lsr #32
    //     0xda7e00: b.eq            #0xda7e08
    //     0xda7e04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7e08: b               #0xda7ff8
    // 0xda7e0c: mov             x5, x3
    // 0xda7e10: ldur            x3, [fp, #-8]
    // 0xda7e14: LoadField: r6 = r3->field_b
    //     0xda7e14: ldur            w6, [x3, #0xb]
    // 0xda7e18: DecompressPointer r6
    //     0xda7e18: add             x6, x6, HEAP, lsl #32
    // 0xda7e1c: mov             x0, x4
    // 0xda7e20: mov             x2, x6
    // 0xda7e24: stur            x6, [fp, #-0x10]
    // 0xda7e28: r1 = Null
    //     0xda7e28: mov             x1, NULL
    // 0xda7e2c: cmp             w0, NULL
    // 0xda7e30: b.eq            #0xda7e5c
    // 0xda7e34: cmp             w2, NULL
    // 0xda7e38: b.eq            #0xda7e5c
    // 0xda7e3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7e3c: ldur            w4, [x2, #0x17]
    // 0xda7e40: DecompressPointer r4
    //     0xda7e40: add             x4, x4, HEAP, lsl #32
    // 0xda7e44: r8 = X0? bound RenderObject
    //     0xda7e44: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7e48: ldr             x8, [x8, #0x1a8]
    // 0xda7e4c: LoadField: r9 = r4->field_7
    //     0xda7e4c: ldur            x9, [x4, #7]
    // 0xda7e50: r3 = Null
    //     0xda7e50: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4c0] Null
    //     0xda7e54: ldr             x3, [x3, #0x4c0]
    // 0xda7e58: blr             x9
    // 0xda7e5c: ldur            x0, [fp, #-0x30]
    // 0xda7e60: ldur            x3, [fp, #-8]
    // 0xda7e64: StoreField: r3->field_13 = r0
    //     0xda7e64: stur            w0, [x3, #0x13]
    //     0xda7e68: ldurb           w16, [x3, #-1]
    //     0xda7e6c: ldurb           w17, [x0, #-1]
    //     0xda7e70: and             x16, x17, x16, lsr #2
    //     0xda7e74: tst             x16, HEAP, lsr #32
    //     0xda7e78: b.eq            #0xda7e80
    //     0xda7e7c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda7e80: ldur            x0, [fp, #-0x20]
    // 0xda7e84: ldur            x2, [fp, #-0x10]
    // 0xda7e88: r1 = Null
    //     0xda7e88: mov             x1, NULL
    // 0xda7e8c: cmp             w0, NULL
    // 0xda7e90: b.eq            #0xda7ebc
    // 0xda7e94: cmp             w2, NULL
    // 0xda7e98: b.eq            #0xda7ebc
    // 0xda7e9c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7e9c: ldur            w4, [x2, #0x17]
    // 0xda7ea0: DecompressPointer r4
    //     0xda7ea0: add             x4, x4, HEAP, lsl #32
    // 0xda7ea4: r8 = X0? bound RenderObject
    //     0xda7ea4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7ea8: ldr             x8, [x8, #0x1a8]
    // 0xda7eac: LoadField: r9 = r4->field_7
    //     0xda7eac: ldur            x9, [x4, #7]
    // 0xda7eb0: r3 = Null
    //     0xda7eb0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4d0] Null
    //     0xda7eb4: ldr             x3, [x3, #0x4d0]
    // 0xda7eb8: blr             x9
    // 0xda7ebc: ldur            x0, [fp, #-0x20]
    // 0xda7ec0: ldur            x1, [fp, #-8]
    // 0xda7ec4: StoreField: r1->field_f = r0
    //     0xda7ec4: stur            w0, [x1, #0xf]
    //     0xda7ec8: ldurb           w16, [x1, #-1]
    //     0xda7ecc: ldurb           w17, [x0, #-1]
    //     0xda7ed0: and             x16, x17, x16, lsr #2
    //     0xda7ed4: tst             x16, HEAP, lsr #32
    //     0xda7ed8: b.eq            #0xda7ee0
    //     0xda7edc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7ee0: ldur            x0, [fp, #-0x30]
    // 0xda7ee4: LoadField: r3 = r0->field_7
    //     0xda7ee4: ldur            w3, [x0, #7]
    // 0xda7ee8: DecompressPointer r3
    //     0xda7ee8: add             x3, x3, HEAP, lsl #32
    // 0xda7eec: stur            x3, [fp, #-8]
    // 0xda7ef0: cmp             w3, NULL
    // 0xda7ef4: b.eq            #0xda8014
    // 0xda7ef8: mov             x0, x3
    // 0xda7efc: r2 = Null
    //     0xda7efc: mov             x2, NULL
    // 0xda7f00: r1 = Null
    //     0xda7f00: mov             x1, NULL
    // 0xda7f04: r4 = LoadClassIdInstr(r0)
    //     0xda7f04: ldur            x4, [x0, #-1]
    //     0xda7f08: ubfx            x4, x4, #0xc, #0x14
    // 0xda7f0c: cmp             x4, #0xc76
    // 0xda7f10: b.eq            #0xda7f28
    // 0xda7f14: r8 = CSSBoxParentData
    //     0xda7f14: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0xda7f18: ldr             x8, [x8, #0x3d8]
    // 0xda7f1c: r3 = Null
    //     0xda7f1c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4e0] Null
    //     0xda7f20: ldr             x3, [x3, #0x4e0]
    // 0xda7f24: r0 = DefaultTypeTest()
    //     0xda7f24: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda7f28: ldur            x3, [fp, #-0x28]
    // 0xda7f2c: LoadField: r2 = r3->field_b
    //     0xda7f2c: ldur            w2, [x3, #0xb]
    // 0xda7f30: DecompressPointer r2
    //     0xda7f30: add             x2, x2, HEAP, lsl #32
    // 0xda7f34: ldur            x0, [fp, #-0x18]
    // 0xda7f38: r1 = Null
    //     0xda7f38: mov             x1, NULL
    // 0xda7f3c: cmp             w0, NULL
    // 0xda7f40: b.eq            #0xda7f6c
    // 0xda7f44: cmp             w2, NULL
    // 0xda7f48: b.eq            #0xda7f6c
    // 0xda7f4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7f4c: ldur            w4, [x2, #0x17]
    // 0xda7f50: DecompressPointer r4
    //     0xda7f50: add             x4, x4, HEAP, lsl #32
    // 0xda7f54: r8 = X0? bound RenderObject
    //     0xda7f54: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7f58: ldr             x8, [x8, #0x1a8]
    // 0xda7f5c: LoadField: r9 = r4->field_7
    //     0xda7f5c: ldur            x9, [x4, #7]
    // 0xda7f60: r3 = Null
    //     0xda7f60: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d4f0] Null
    //     0xda7f64: ldr             x3, [x3, #0x4f0]
    // 0xda7f68: blr             x9
    // 0xda7f6c: ldur            x0, [fp, #-0x18]
    // 0xda7f70: ldur            x1, [fp, #-0x28]
    // 0xda7f74: StoreField: r1->field_13 = r0
    //     0xda7f74: stur            w0, [x1, #0x13]
    //     0xda7f78: ldurb           w16, [x1, #-1]
    //     0xda7f7c: ldurb           w17, [x0, #-1]
    //     0xda7f80: and             x16, x17, x16, lsr #2
    //     0xda7f84: tst             x16, HEAP, lsr #32
    //     0xda7f88: b.eq            #0xda7f90
    //     0xda7f8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7f90: ldur            x3, [fp, #-8]
    // 0xda7f94: LoadField: r2 = r3->field_b
    //     0xda7f94: ldur            w2, [x3, #0xb]
    // 0xda7f98: DecompressPointer r2
    //     0xda7f98: add             x2, x2, HEAP, lsl #32
    // 0xda7f9c: ldur            x0, [fp, #-0x18]
    // 0xda7fa0: r1 = Null
    //     0xda7fa0: mov             x1, NULL
    // 0xda7fa4: cmp             w0, NULL
    // 0xda7fa8: b.eq            #0xda7fd4
    // 0xda7fac: cmp             w2, NULL
    // 0xda7fb0: b.eq            #0xda7fd4
    // 0xda7fb4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda7fb4: ldur            w4, [x2, #0x17]
    // 0xda7fb8: DecompressPointer r4
    //     0xda7fb8: add             x4, x4, HEAP, lsl #32
    // 0xda7fbc: r8 = X0? bound RenderObject
    //     0xda7fbc: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda7fc0: ldr             x8, [x8, #0x1a8]
    // 0xda7fc4: LoadField: r9 = r4->field_7
    //     0xda7fc4: ldur            x9, [x4, #7]
    // 0xda7fc8: r3 = Null
    //     0xda7fc8: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d500] Null
    //     0xda7fcc: ldr             x3, [x3, #0x500]
    // 0xda7fd0: blr             x9
    // 0xda7fd4: ldur            x0, [fp, #-0x18]
    // 0xda7fd8: ldur            x1, [fp, #-8]
    // 0xda7fdc: StoreField: r1->field_f = r0
    //     0xda7fdc: stur            w0, [x1, #0xf]
    //     0xda7fe0: ldurb           w16, [x1, #-1]
    //     0xda7fe4: ldurb           w17, [x0, #-1]
    //     0xda7fe8: and             x16, x17, x16, lsr #2
    //     0xda7fec: tst             x16, HEAP, lsr #32
    //     0xda7ff0: b.eq            #0xda7ff8
    //     0xda7ff4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda7ff8: r0 = Null
    //     0xda7ff8: mov             x0, NULL
    // 0xda7ffc: LeaveFrame
    //     0xda7ffc: mov             SP, fp
    //     0xda8000: ldp             fp, lr, [SP], #0x10
    // 0xda8004: ret
    //     0xda8004: ret             
    // 0xda8008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8008: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda800c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda800c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda8010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8010: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda8014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8014: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3008, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _RenderCSSBox&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends _RenderCSSBox&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {

  _ defaultPaint(/* No info */) {
    // ** addr: 0x79f148, size: 0x128
    // 0x79f148: EnterFrame
    //     0x79f148: stp             fp, lr, [SP, #-0x10]!
    //     0x79f14c: mov             fp, SP
    // 0x79f150: AllocStack(0x38)
    //     0x79f150: sub             SP, SP, #0x38
    // 0x79f154: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */)
    //     0x79f154: mov             x4, x2
    //     0x79f158: stur            x2, [fp, #-0x18]
    // 0x79f15c: CheckStackOverflow
    //     0x79f15c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79f160: cmp             SP, x16
    //     0x79f164: b.ls            #0x79f25c
    // 0x79f168: LoadField: r0 = r1->field_5f
    //     0x79f168: ldur            w0, [x1, #0x5f]
    // 0x79f16c: DecompressPointer r0
    //     0x79f16c: add             x0, x0, HEAP, lsl #32
    // 0x79f170: LoadField: d0 = r3->field_7
    //     0x79f170: ldur            d0, [x3, #7]
    // 0x79f174: stur            d0, [fp, #-0x28]
    // 0x79f178: LoadField: d1 = r3->field_f
    //     0x79f178: ldur            d1, [x3, #0xf]
    // 0x79f17c: stur            d1, [fp, #-0x20]
    // 0x79f180: mov             x3, x0
    // 0x79f184: stur            x3, [fp, #-0x10]
    // 0x79f188: CheckStackOverflow
    //     0x79f188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79f18c: cmp             SP, x16
    //     0x79f190: b.ls            #0x79f264
    // 0x79f194: cmp             w3, NULL
    // 0x79f198: b.eq            #0x79f24c
    // 0x79f19c: LoadField: r5 = r3->field_7
    //     0x79f19c: ldur            w5, [x3, #7]
    // 0x79f1a0: DecompressPointer r5
    //     0x79f1a0: add             x5, x5, HEAP, lsl #32
    // 0x79f1a4: stur            x5, [fp, #-8]
    // 0x79f1a8: cmp             w5, NULL
    // 0x79f1ac: b.eq            #0x79f26c
    // 0x79f1b0: mov             x0, x5
    // 0x79f1b4: r2 = Null
    //     0x79f1b4: mov             x2, NULL
    // 0x79f1b8: r1 = Null
    //     0x79f1b8: mov             x1, NULL
    // 0x79f1bc: r4 = LoadClassIdInstr(r0)
    //     0x79f1bc: ldur            x4, [x0, #-1]
    //     0x79f1c0: ubfx            x4, x4, #0xc, #0x14
    // 0x79f1c4: cmp             x4, #0xc76
    // 0x79f1c8: b.eq            #0x79f1e0
    // 0x79f1cc: r8 = CSSBoxParentData
    //     0x79f1cc: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x79f1d0: ldr             x8, [x8, #0x3d8]
    // 0x79f1d4: r3 = Null
    //     0x79f1d4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d5f0] Null
    //     0x79f1d8: ldr             x3, [x3, #0x5f0]
    // 0x79f1dc: r0 = DefaultTypeTest()
    //     0x79f1dc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x79f1e0: ldur            x0, [fp, #-8]
    // 0x79f1e4: LoadField: r1 = r0->field_7
    //     0x79f1e4: ldur            w1, [x0, #7]
    // 0x79f1e8: DecompressPointer r1
    //     0x79f1e8: add             x1, x1, HEAP, lsl #32
    // 0x79f1ec: LoadField: d0 = r1->field_7
    //     0x79f1ec: ldur            d0, [x1, #7]
    // 0x79f1f0: ldur            d1, [fp, #-0x28]
    // 0x79f1f4: fadd            d2, d0, d1
    // 0x79f1f8: stur            d2, [fp, #-0x38]
    // 0x79f1fc: LoadField: d0 = r1->field_f
    //     0x79f1fc: ldur            d0, [x1, #0xf]
    // 0x79f200: ldur            d3, [fp, #-0x20]
    // 0x79f204: fadd            d4, d0, d3
    // 0x79f208: stur            d4, [fp, #-0x30]
    // 0x79f20c: r0 = Offset()
    //     0x79f20c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x79f210: ldur            d0, [fp, #-0x38]
    // 0x79f214: StoreField: r0->field_7 = d0
    //     0x79f214: stur            d0, [x0, #7]
    // 0x79f218: ldur            d0, [fp, #-0x30]
    // 0x79f21c: StoreField: r0->field_f = d0
    //     0x79f21c: stur            d0, [x0, #0xf]
    // 0x79f220: ldur            x1, [fp, #-0x18]
    // 0x79f224: ldur            x2, [fp, #-0x10]
    // 0x79f228: mov             x3, x0
    // 0x79f22c: r0 = paintChild()
    //     0x79f22c: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79f230: ldur            x1, [fp, #-8]
    // 0x79f234: LoadField: r3 = r1->field_13
    //     0x79f234: ldur            w3, [x1, #0x13]
    // 0x79f238: DecompressPointer r3
    //     0x79f238: add             x3, x3, HEAP, lsl #32
    // 0x79f23c: ldur            x4, [fp, #-0x18]
    // 0x79f240: ldur            d0, [fp, #-0x28]
    // 0x79f244: ldur            d1, [fp, #-0x20]
    // 0x79f248: b               #0x79f184
    // 0x79f24c: r0 = Null
    //     0x79f24c: mov             x0, NULL
    // 0x79f250: LeaveFrame
    //     0x79f250: mov             SP, fp
    //     0x79f254: ldp             fp, lr, [SP], #0x10
    // 0x79f258: ret
    //     0x79f258: ret             
    // 0x79f25c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79f25c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79f260: b               #0x79f168
    // 0x79f264: r0 = StackOverflowSharedWithFPURegs()
    //     0x79f264: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x79f268: b               #0x79f194
    // 0x79f26c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x79f26c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ defaultHitTestChildren(/* No info */) {
    // ** addr: 0x8000ac, size: 0x144
    // 0x8000ac: EnterFrame
    //     0x8000ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8000b0: mov             fp, SP
    // 0x8000b4: AllocStack(0x28)
    //     0x8000b4: sub             SP, SP, #0x28
    // 0x8000b8: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x8000b8: mov             x4, x2
    //     0x8000bc: stur            x2, [fp, #-0x18]
    //     0x8000c0: stur            x3, [fp, #-0x20]
    // 0x8000c4: CheckStackOverflow
    //     0x8000c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8000c8: cmp             SP, x16
    //     0x8000cc: b.ls            #0x8001dc
    // 0x8000d0: LoadField: r0 = r1->field_63
    //     0x8000d0: ldur            w0, [x1, #0x63]
    // 0x8000d4: DecompressPointer r0
    //     0x8000d4: add             x0, x0, HEAP, lsl #32
    // 0x8000d8: mov             x5, x0
    // 0x8000dc: stur            x5, [fp, #-0x10]
    // 0x8000e0: CheckStackOverflow
    //     0x8000e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8000e4: cmp             SP, x16
    //     0x8000e8: b.ls            #0x8001e4
    // 0x8000ec: cmp             w5, NULL
    // 0x8000f0: b.eq            #0x8001cc
    // 0x8000f4: LoadField: r6 = r5->field_7
    //     0x8000f4: ldur            w6, [x5, #7]
    // 0x8000f8: DecompressPointer r6
    //     0x8000f8: add             x6, x6, HEAP, lsl #32
    // 0x8000fc: stur            x6, [fp, #-8]
    // 0x800100: cmp             w6, NULL
    // 0x800104: b.eq            #0x8001ec
    // 0x800108: mov             x0, x6
    // 0x80010c: r2 = Null
    //     0x80010c: mov             x2, NULL
    // 0x800110: r1 = Null
    //     0x800110: mov             x1, NULL
    // 0x800114: r4 = LoadClassIdInstr(r0)
    //     0x800114: ldur            x4, [x0, #-1]
    //     0x800118: ubfx            x4, x4, #0xc, #0x14
    // 0x80011c: cmp             x4, #0xc76
    // 0x800120: b.eq            #0x800138
    // 0x800124: r8 = CSSBoxParentData
    //     0x800124: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x800128: ldr             x8, [x8, #0x3d8]
    // 0x80012c: r3 = Null
    //     0x80012c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d600] Null
    //     0x800130: ldr             x3, [x3, #0x600]
    // 0x800134: r0 = DefaultTypeTest()
    //     0x800134: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x800138: ldur            x0, [fp, #-8]
    // 0x80013c: LoadField: r3 = r0->field_7
    //     0x80013c: ldur            w3, [x0, #7]
    // 0x800140: DecompressPointer r3
    //     0x800140: add             x3, x3, HEAP, lsl #32
    // 0x800144: ldur            x1, [fp, #-0x20]
    // 0x800148: mov             x2, x3
    // 0x80014c: stur            x3, [fp, #-0x28]
    // 0x800150: r0 = -()
    //     0x800150: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x800154: ldur            x1, [fp, #-0x28]
    // 0x800158: stur            x0, [fp, #-0x28]
    // 0x80015c: r0 = unary-()
    //     0x80015c: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x800160: ldur            x1, [fp, #-0x18]
    // 0x800164: mov             x2, x0
    // 0x800168: r0 = pushOffset()
    //     0x800168: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x80016c: ldur            x1, [fp, #-0x10]
    // 0x800170: r0 = LoadClassIdInstr(r1)
    //     0x800170: ldur            x0, [x1, #-1]
    //     0x800174: ubfx            x0, x0, #0xc, #0x14
    // 0x800178: ldur            x2, [fp, #-0x18]
    // 0x80017c: ldur            x3, [fp, #-0x28]
    // 0x800180: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x800180: movz            x17, #0xdf93
    //     0x800184: add             lr, x0, x17
    //     0x800188: ldr             lr, [x21, lr, lsl #3]
    //     0x80018c: blr             lr
    // 0x800190: ldur            x1, [fp, #-0x18]
    // 0x800194: stur            x0, [fp, #-0x10]
    // 0x800198: r0 = popTransform()
    //     0x800198: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x80019c: ldur            x1, [fp, #-0x10]
    // 0x8001a0: tbz             w1, #4, #0x8001bc
    // 0x8001a4: ldur            x1, [fp, #-8]
    // 0x8001a8: LoadField: r5 = r1->field_f
    //     0x8001a8: ldur            w5, [x1, #0xf]
    // 0x8001ac: DecompressPointer r5
    //     0x8001ac: add             x5, x5, HEAP, lsl #32
    // 0x8001b0: ldur            x4, [fp, #-0x18]
    // 0x8001b4: ldur            x3, [fp, #-0x20]
    // 0x8001b8: b               #0x8000dc
    // 0x8001bc: r0 = true
    //     0x8001bc: add             x0, NULL, #0x20  ; true
    // 0x8001c0: LeaveFrame
    //     0x8001c0: mov             SP, fp
    //     0x8001c4: ldp             fp, lr, [SP], #0x10
    // 0x8001c8: ret
    //     0x8001c8: ret             
    // 0x8001cc: r0 = false
    //     0x8001cc: add             x0, NULL, #0x30  ; false
    // 0x8001d0: LeaveFrame
    //     0x8001d0: mov             SP, fp
    //     0x8001d4: ldp             fp, lr, [SP], #0x10
    // 0x8001d8: ret
    //     0x8001d8: ret             
    // 0x8001dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8001dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8001e0: b               #0x8000d0
    // 0x8001e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8001e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8001e8: b               #0x8000ec
    // 0x8001ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8001ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3009, size: 0x84, field offset: 0x68
class RenderCSSBox extends _RenderCSSBox&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin {

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x734ea8, size: 0x24
    // 0x734ea8: EnterFrame
    //     0x734ea8: stp             fp, lr, [SP, #-0x10]!
    //     0x734eac: mov             fp, SP
    // 0x734eb0: ldr             x2, [fp, #0x10]
    // 0x734eb4: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x734eb4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5e0] AnonymousClosure: (0x734ecc), in [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMinIntrinsicWidth (0x734f40)
    //     0x734eb8: ldr             x1, [x1, #0x5e0]
    // 0x734ebc: r0 = AllocateClosure()
    //     0x734ebc: bl              #0xec1630  ; AllocateClosureStub
    // 0x734ec0: LeaveFrame
    //     0x734ec0: mov             SP, fp
    //     0x734ec4: ldp             fp, lr, [SP], #0x10
    // 0x734ec8: ret
    //     0x734ec8: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x734ecc, size: 0x74
    // 0x734ecc: EnterFrame
    //     0x734ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x734ed0: mov             fp, SP
    // 0x734ed4: ldr             x0, [fp, #0x18]
    // 0x734ed8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x734ed8: ldur            w1, [x0, #0x17]
    // 0x734edc: DecompressPointer r1
    //     0x734edc: add             x1, x1, HEAP, lsl #32
    // 0x734ee0: CheckStackOverflow
    //     0x734ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734ee4: cmp             SP, x16
    //     0x734ee8: b.ls            #0x734f28
    // 0x734eec: ldr             x2, [fp, #0x10]
    // 0x734ef0: r0 = computeMinIntrinsicWidth()
    //     0x734ef0: bl              #0x734f40  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMinIntrinsicWidth
    // 0x734ef4: r0 = inline_Allocate_Double()
    //     0x734ef4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x734ef8: add             x0, x0, #0x10
    //     0x734efc: cmp             x1, x0
    //     0x734f00: b.ls            #0x734f30
    //     0x734f04: str             x0, [THR, #0x50]  ; THR::top
    //     0x734f08: sub             x0, x0, #0xf
    //     0x734f0c: movz            x1, #0xe15c
    //     0x734f10: movk            x1, #0x3, lsl #16
    //     0x734f14: stur            x1, [x0, #-1]
    // 0x734f18: StoreField: r0->field_7 = d0
    //     0x734f18: stur            d0, [x0, #7]
    // 0x734f1c: LeaveFrame
    //     0x734f1c: mov             SP, fp
    //     0x734f20: ldp             fp, lr, [SP], #0x10
    // 0x734f24: ret
    //     0x734f24: ret             
    // 0x734f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734f2c: b               #0x734eec
    // 0x734f30: SaveReg d0
    //     0x734f30: str             q0, [SP, #-0x10]!
    // 0x734f34: r0 = AllocateDouble()
    //     0x734f34: bl              #0xec2254  ; AllocateDoubleStub
    // 0x734f38: RestoreReg d0
    //     0x734f38: ldr             q0, [SP], #0x10
    // 0x734f3c: b               #0x734f18
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x734f40, size: 0x74
    // 0x734f40: EnterFrame
    //     0x734f40: stp             fp, lr, [SP, #-0x10]!
    //     0x734f44: mov             fp, SP
    // 0x734f48: AllocStack(0x10)
    //     0x734f48: sub             SP, SP, #0x10
    // 0x734f4c: SetupParameters(RenderCSSBox this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x734f4c: stur            x1, [fp, #-8]
    //     0x734f50: stur            x2, [fp, #-0x10]
    // 0x734f54: CheckStackOverflow
    //     0x734f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734f58: cmp             SP, x16
    //     0x734f5c: b.ls            #0x734fac
    // 0x734f60: r1 = 1
    //     0x734f60: movz            x1, #0x1
    // 0x734f64: r0 = AllocateContext()
    //     0x734f64: bl              #0xec126c  ; AllocateContextStub
    // 0x734f68: mov             x1, x0
    // 0x734f6c: ldur            x0, [fp, #-0x10]
    // 0x734f70: StoreField: r1->field_f = r0
    //     0x734f70: stur            w0, [x1, #0xf]
    // 0x734f74: ldur            x0, [fp, #-8]
    // 0x734f78: LoadField: r3 = r0->field_5f
    //     0x734f78: ldur            w3, [x0, #0x5f]
    // 0x734f7c: DecompressPointer r3
    //     0x734f7c: add             x3, x3, HEAP, lsl #32
    // 0x734f80: mov             x2, x1
    // 0x734f84: stur            x3, [fp, #-0x10]
    // 0x734f88: r1 = Function '<anonymous closure>':.
    //     0x734f88: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5e8] AnonymousClosure: (0x733c7c), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicWidth (0x733cfc)
    //     0x734f8c: ldr             x1, [x1, #0x5e8]
    // 0x734f90: r0 = AllocateClosure()
    //     0x734f90: bl              #0xec1630  ; AllocateClosureStub
    // 0x734f94: ldur            x1, [fp, #-0x10]
    // 0x734f98: mov             x2, x0
    // 0x734f9c: r0 = getIntrinsicDimension()
    //     0x734f9c: bl              #0x734fb4  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::getIntrinsicDimension
    // 0x734fa0: LeaveFrame
    //     0x734fa0: mov             SP, fp
    //     0x734fa4: ldp             fp, lr, [SP], #0x10
    // 0x734fa8: ret
    //     0x734fa8: ret             
    // 0x734fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734fac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734fb0: b               #0x734f60
  }
  static _ getIntrinsicDimension(/* No info */) {
    // ** addr: 0x734fb4, size: 0x25c
    // 0x734fb4: EnterFrame
    //     0x734fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x734fb8: mov             fp, SP
    // 0x734fbc: AllocStack(0x30)
    //     0x734fbc: sub             SP, SP, #0x30
    // 0x734fc0: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x734fc0: mov             x3, x2
    //     0x734fc4: stur            x2, [fp, #-0x20]
    // 0x734fc8: CheckStackOverflow
    //     0x734fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734fcc: cmp             SP, x16
    //     0x734fd0: b.ls            #0x7351ec
    // 0x734fd4: mov             x4, x1
    // 0x734fd8: r5 = 0.000000
    //     0x734fd8: ldr             x5, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x734fdc: stur            x5, [fp, #-0x10]
    // 0x734fe0: stur            x4, [fp, #-0x18]
    // 0x734fe4: CheckStackOverflow
    //     0x734fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734fe8: cmp             SP, x16
    //     0x734fec: b.ls            #0x7351f4
    // 0x734ff0: cmp             w4, NULL
    // 0x734ff4: b.eq            #0x7351d8
    // 0x734ff8: LoadField: r6 = r4->field_7
    //     0x734ff8: ldur            w6, [x4, #7]
    // 0x734ffc: DecompressPointer r6
    //     0x734ffc: add             x6, x6, HEAP, lsl #32
    // 0x735000: stur            x6, [fp, #-8]
    // 0x735004: cmp             w6, NULL
    // 0x735008: b.eq            #0x7351fc
    // 0x73500c: mov             x0, x6
    // 0x735010: r2 = Null
    //     0x735010: mov             x2, NULL
    // 0x735014: r1 = Null
    //     0x735014: mov             x1, NULL
    // 0x735018: r4 = LoadClassIdInstr(r0)
    //     0x735018: ldur            x4, [x0, #-1]
    //     0x73501c: ubfx            x4, x4, #0xc, #0x14
    // 0x735020: cmp             x4, #0xc76
    // 0x735024: b.eq            #0x73503c
    // 0x735028: r8 = CSSBoxParentData
    //     0x735028: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x73502c: ldr             x8, [x8, #0x3d8]
    // 0x735030: r3 = Null
    //     0x735030: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d5b0] Null
    //     0x735034: ldr             x3, [x3, #0x5b0]
    // 0x735038: r0 = DefaultTypeTest()
    //     0x735038: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x73503c: ldur            x16, [fp, #-0x20]
    // 0x735040: ldur            lr, [fp, #-0x18]
    // 0x735044: stp             lr, x16, [SP]
    // 0x735048: ldur            x0, [fp, #-0x20]
    // 0x73504c: ClosureCall
    //     0x73504c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x735050: ldur            x2, [x0, #0x1f]
    //     0x735054: blr             x2
    // 0x735058: mov             x2, x0
    // 0x73505c: ldur            x1, [fp, #-0x10]
    // 0x735060: stur            x2, [fp, #-0x18]
    // 0x735064: r0 = 60
    //     0x735064: movz            x0, #0x3c
    // 0x735068: branchIfSmi(r1, 0x735074)
    //     0x735068: tbz             w1, #0, #0x735074
    // 0x73506c: r0 = LoadClassIdInstr(r1)
    //     0x73506c: ldur            x0, [x1, #-1]
    //     0x735070: ubfx            x0, x0, #0xc, #0x14
    // 0x735074: stp             x2, x1, [SP]
    // 0x735078: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x735078: sub             lr, x0, #0xfe3
    //     0x73507c: ldr             lr, [x21, lr, lsl #3]
    //     0x735080: blr             lr
    // 0x735084: tbnz            w0, #4, #0x735090
    // 0x735088: ldur            x5, [fp, #-0x10]
    // 0x73508c: b               #0x7351c4
    // 0x735090: ldur            x1, [fp, #-0x10]
    // 0x735094: r0 = 60
    //     0x735094: movz            x0, #0x3c
    // 0x735098: branchIfSmi(r1, 0x7350a4)
    //     0x735098: tbz             w1, #0, #0x7350a4
    // 0x73509c: r0 = LoadClassIdInstr(r1)
    //     0x73509c: ldur            x0, [x1, #-1]
    //     0x7350a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7350a4: ldur            x16, [fp, #-0x18]
    // 0x7350a8: stp             x16, x1, [SP]
    // 0x7350ac: r0 = GDT[cid_x0 + -0xfd2]()
    //     0x7350ac: sub             lr, x0, #0xfd2
    //     0x7350b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7350b4: blr             lr
    // 0x7350b8: tbnz            w0, #4, #0x7350c4
    // 0x7350bc: ldur            x5, [fp, #-0x18]
    // 0x7350c0: b               #0x7351c4
    // 0x7350c4: ldur            x1, [fp, #-0x18]
    // 0x7350c8: r0 = 60
    //     0x7350c8: movz            x0, #0x3c
    // 0x7350cc: branchIfSmi(r1, 0x7350d8)
    //     0x7350cc: tbz             w1, #0, #0x7350d8
    // 0x7350d0: r0 = LoadClassIdInstr(r1)
    //     0x7350d0: ldur            x0, [x1, #-1]
    //     0x7350d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7350d8: cmp             x0, #0x3e
    // 0x7350dc: b.ne            #0x735164
    // 0x7350e0: ldur            x2, [fp, #-0x10]
    // 0x7350e4: r0 = 60
    //     0x7350e4: movz            x0, #0x3c
    // 0x7350e8: branchIfSmi(r2, 0x7350f4)
    //     0x7350e8: tbz             w2, #0, #0x7350f4
    // 0x7350ec: r0 = LoadClassIdInstr(r2)
    //     0x7350ec: ldur            x0, [x2, #-1]
    //     0x7350f0: ubfx            x0, x0, #0xc, #0x14
    // 0x7350f4: cmp             x0, #0x3e
    // 0x7350f8: b.ne            #0x735144
    // 0x7350fc: d0 = 0.000000
    //     0x7350fc: eor             v0.16b, v0.16b, v0.16b
    // 0x735100: LoadField: d1 = r2->field_7
    //     0x735100: ldur            d1, [x2, #7]
    // 0x735104: fcmp            d1, d0
    // 0x735108: b.ne            #0x735148
    // 0x73510c: LoadField: d2 = r1->field_7
    //     0x73510c: ldur            d2, [x1, #7]
    // 0x735110: fadd            d3, d1, d2
    // 0x735114: r0 = inline_Allocate_Double()
    //     0x735114: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x735118: add             x0, x0, #0x10
    //     0x73511c: cmp             x1, x0
    //     0x735120: b.ls            #0x735200
    //     0x735124: str             x0, [THR, #0x50]  ; THR::top
    //     0x735128: sub             x0, x0, #0xf
    //     0x73512c: movz            x1, #0xe15c
    //     0x735130: movk            x1, #0x3, lsl #16
    //     0x735134: stur            x1, [x0, #-1]
    // 0x735138: StoreField: r0->field_7 = d3
    //     0x735138: stur            d3, [x0, #7]
    // 0x73513c: mov             x5, x0
    // 0x735140: b               #0x7351c4
    // 0x735144: d0 = 0.000000
    //     0x735144: eor             v0.16b, v0.16b, v0.16b
    // 0x735148: LoadField: d1 = r1->field_7
    //     0x735148: ldur            d1, [x1, #7]
    // 0x73514c: fcmp            d1, d1
    // 0x735150: b.vc            #0x73515c
    // 0x735154: mov             x5, x1
    // 0x735158: b               #0x7351c4
    // 0x73515c: mov             x5, x2
    // 0x735160: b               #0x7351c4
    // 0x735164: ldur            x2, [fp, #-0x10]
    // 0x735168: d0 = 0.000000
    //     0x735168: eor             v0.16b, v0.16b, v0.16b
    // 0x73516c: r0 = 60
    //     0x73516c: movz            x0, #0x3c
    // 0x735170: branchIfSmi(r1, 0x73517c)
    //     0x735170: tbz             w1, #0, #0x73517c
    // 0x735174: r0 = LoadClassIdInstr(r1)
    //     0x735174: ldur            x0, [x1, #-1]
    //     0x735178: ubfx            x0, x0, #0xc, #0x14
    // 0x73517c: stp             xzr, x1, [SP]
    // 0x735180: mov             lr, x0
    // 0x735184: ldr             lr, [x21, lr, lsl #3]
    // 0x735188: blr             lr
    // 0x73518c: tbnz            w0, #4, #0x7351c0
    // 0x735190: ldur            x1, [fp, #-0x10]
    // 0x735194: r0 = 60
    //     0x735194: movz            x0, #0x3c
    // 0x735198: branchIfSmi(r1, 0x7351a4)
    //     0x735198: tbz             w1, #0, #0x7351a4
    // 0x73519c: r0 = LoadClassIdInstr(r1)
    //     0x73519c: ldur            x0, [x1, #-1]
    //     0x7351a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7351a4: str             x1, [SP]
    // 0x7351a8: r0 = GDT[cid_x0 + -0xfb8]()
    //     0x7351a8: sub             lr, x0, #0xfb8
    //     0x7351ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7351b0: blr             lr
    // 0x7351b4: tbnz            w0, #4, #0x7351c0
    // 0x7351b8: ldur            x5, [fp, #-0x18]
    // 0x7351bc: b               #0x7351c4
    // 0x7351c0: ldur            x5, [fp, #-0x10]
    // 0x7351c4: ldur            x0, [fp, #-8]
    // 0x7351c8: LoadField: r4 = r0->field_13
    //     0x7351c8: ldur            w4, [x0, #0x13]
    // 0x7351cc: DecompressPointer r4
    //     0x7351cc: add             x4, x4, HEAP, lsl #32
    // 0x7351d0: ldur            x3, [fp, #-0x20]
    // 0x7351d4: b               #0x734fdc
    // 0x7351d8: mov             x0, x5
    // 0x7351dc: LoadField: d0 = r0->field_7
    //     0x7351dc: ldur            d0, [x0, #7]
    // 0x7351e0: LeaveFrame
    //     0x7351e0: mov             SP, fp
    //     0x7351e4: ldp             fp, lr, [SP], #0x10
    // 0x7351e8: ret
    //     0x7351e8: ret             
    // 0x7351ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7351ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7351f0: b               #0x734fd4
    // 0x7351f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7351f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7351f8: b               #0x734ff0
    // 0x7351fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7351fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x735200: stp             q0, q3, [SP, #-0x20]!
    // 0x735204: r0 = AllocateDouble()
    //     0x735204: bl              #0xec2254  ; AllocateDoubleStub
    // 0x735208: ldp             q0, q3, [SP], #0x20
    // 0x73520c: b               #0x735138
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x74ac58, size: 0x24
    // 0x74ac58: EnterFrame
    //     0x74ac58: stp             fp, lr, [SP, #-0x10]!
    //     0x74ac5c: mov             fp, SP
    // 0x74ac60: ldr             x2, [fp, #0x10]
    // 0x74ac64: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x74ac64: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5c0] AnonymousClosure: (0x74ac7c), in [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMinIntrinsicHeight (0x74acf0)
    //     0x74ac68: ldr             x1, [x1, #0x5c0]
    // 0x74ac6c: r0 = AllocateClosure()
    //     0x74ac6c: bl              #0xec1630  ; AllocateClosureStub
    // 0x74ac70: LeaveFrame
    //     0x74ac70: mov             SP, fp
    //     0x74ac74: ldp             fp, lr, [SP], #0x10
    // 0x74ac78: ret
    //     0x74ac78: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x74ac7c, size: 0x74
    // 0x74ac7c: EnterFrame
    //     0x74ac7c: stp             fp, lr, [SP, #-0x10]!
    //     0x74ac80: mov             fp, SP
    // 0x74ac84: ldr             x0, [fp, #0x18]
    // 0x74ac88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74ac88: ldur            w1, [x0, #0x17]
    // 0x74ac8c: DecompressPointer r1
    //     0x74ac8c: add             x1, x1, HEAP, lsl #32
    // 0x74ac90: CheckStackOverflow
    //     0x74ac90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ac94: cmp             SP, x16
    //     0x74ac98: b.ls            #0x74acd8
    // 0x74ac9c: ldr             x2, [fp, #0x10]
    // 0x74aca0: r0 = computeMinIntrinsicHeight()
    //     0x74aca0: bl              #0x74acf0  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMinIntrinsicHeight
    // 0x74aca4: r0 = inline_Allocate_Double()
    //     0x74aca4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74aca8: add             x0, x0, #0x10
    //     0x74acac: cmp             x1, x0
    //     0x74acb0: b.ls            #0x74ace0
    //     0x74acb4: str             x0, [THR, #0x50]  ; THR::top
    //     0x74acb8: sub             x0, x0, #0xf
    //     0x74acbc: movz            x1, #0xe15c
    //     0x74acc0: movk            x1, #0x3, lsl #16
    //     0x74acc4: stur            x1, [x0, #-1]
    // 0x74acc8: StoreField: r0->field_7 = d0
    //     0x74acc8: stur            d0, [x0, #7]
    // 0x74accc: LeaveFrame
    //     0x74accc: mov             SP, fp
    //     0x74acd0: ldp             fp, lr, [SP], #0x10
    // 0x74acd4: ret
    //     0x74acd4: ret             
    // 0x74acd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74acd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74acdc: b               #0x74ac9c
    // 0x74ace0: SaveReg d0
    //     0x74ace0: str             q0, [SP, #-0x10]!
    // 0x74ace4: r0 = AllocateDouble()
    //     0x74ace4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74ace8: RestoreReg d0
    //     0x74ace8: ldr             q0, [SP], #0x10
    // 0x74acec: b               #0x74acc8
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x74acf0, size: 0x74
    // 0x74acf0: EnterFrame
    //     0x74acf0: stp             fp, lr, [SP, #-0x10]!
    //     0x74acf4: mov             fp, SP
    // 0x74acf8: AllocStack(0x10)
    //     0x74acf8: sub             SP, SP, #0x10
    // 0x74acfc: SetupParameters(RenderCSSBox this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x74acfc: stur            x1, [fp, #-8]
    //     0x74ad00: stur            x2, [fp, #-0x10]
    // 0x74ad04: CheckStackOverflow
    //     0x74ad04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ad08: cmp             SP, x16
    //     0x74ad0c: b.ls            #0x74ad5c
    // 0x74ad10: r1 = 1
    //     0x74ad10: movz            x1, #0x1
    // 0x74ad14: r0 = AllocateContext()
    //     0x74ad14: bl              #0xec126c  ; AllocateContextStub
    // 0x74ad18: mov             x1, x0
    // 0x74ad1c: ldur            x0, [fp, #-0x10]
    // 0x74ad20: StoreField: r1->field_f = r0
    //     0x74ad20: stur            w0, [x1, #0xf]
    // 0x74ad24: ldur            x0, [fp, #-8]
    // 0x74ad28: LoadField: r3 = r0->field_5f
    //     0x74ad28: ldur            w3, [x0, #0x5f]
    // 0x74ad2c: DecompressPointer r3
    //     0x74ad2c: add             x3, x3, HEAP, lsl #32
    // 0x74ad30: mov             x2, x1
    // 0x74ad34: stur            x3, [fp, #-0x10]
    // 0x74ad38: r1 = Function '<anonymous closure>':.
    //     0x74ad38: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5c8] AnonymousClosure: (0x749db8), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMinIntrinsicHeight (0x749e38)
    //     0x74ad3c: ldr             x1, [x1, #0x5c8]
    // 0x74ad40: r0 = AllocateClosure()
    //     0x74ad40: bl              #0xec1630  ; AllocateClosureStub
    // 0x74ad44: ldur            x1, [fp, #-0x10]
    // 0x74ad48: mov             x2, x0
    // 0x74ad4c: r0 = getIntrinsicDimension()
    //     0x74ad4c: bl              #0x734fb4  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::getIntrinsicDimension
    // 0x74ad50: LeaveFrame
    //     0x74ad50: mov             SP, fp
    //     0x74ad54: ldp             fp, lr, [SP], #0x10
    // 0x74ad58: ret
    //     0x74ad58: ret             
    // 0x74ad5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ad5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ad60: b               #0x74ad10
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74d690, size: 0x48
    // 0x74d690: EnterFrame
    //     0x74d690: stp             fp, lr, [SP, #-0x10]!
    //     0x74d694: mov             fp, SP
    // 0x74d698: CheckStackOverflow
    //     0x74d698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d69c: cmp             SP, x16
    //     0x74d6a0: b.ls            #0x74d6d0
    // 0x74d6a4: LoadField: r0 = r1->field_5f
    //     0x74d6a4: ldur            w0, [x1, #0x5f]
    // 0x74d6a8: DecompressPointer r0
    //     0x74d6a8: add             x0, x0, HEAP, lsl #32
    // 0x74d6ac: cmp             w0, NULL
    // 0x74d6b0: b.ne            #0x74d6bc
    // 0x74d6b4: r0 = Null
    //     0x74d6b4: mov             x0, NULL
    // 0x74d6b8: b               #0x74d6c4
    // 0x74d6bc: mov             x1, x0
    // 0x74d6c0: r0 = getDistanceToActualBaseline()
    //     0x74d6c0: bl              #0x74b4d4  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline
    // 0x74d6c4: LeaveFrame
    //     0x74d6c4: mov             SP, fp
    //     0x74d6c8: ldp             fp, lr, [SP], #0x10
    // 0x74d6cc: ret
    //     0x74d6cc: ret             
    // 0x74d6d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d6d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d6d4: b               #0x74d6a4
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x7507f0, size: 0x24
    // 0x7507f0: EnterFrame
    //     0x7507f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7507f4: mov             fp, SP
    // 0x7507f8: ldr             x2, [fp, #0x10]
    // 0x7507fc: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x7507fc: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5d0] AnonymousClosure: (0x750814), in [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMaxIntrinsicWidth (0x750888)
    //     0x750800: ldr             x1, [x1, #0x5d0]
    // 0x750804: r0 = AllocateClosure()
    //     0x750804: bl              #0xec1630  ; AllocateClosureStub
    // 0x750808: LeaveFrame
    //     0x750808: mov             SP, fp
    //     0x75080c: ldp             fp, lr, [SP], #0x10
    // 0x750810: ret
    //     0x750810: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x750814, size: 0x74
    // 0x750814: EnterFrame
    //     0x750814: stp             fp, lr, [SP, #-0x10]!
    //     0x750818: mov             fp, SP
    // 0x75081c: ldr             x0, [fp, #0x18]
    // 0x750820: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x750820: ldur            w1, [x0, #0x17]
    // 0x750824: DecompressPointer r1
    //     0x750824: add             x1, x1, HEAP, lsl #32
    // 0x750828: CheckStackOverflow
    //     0x750828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75082c: cmp             SP, x16
    //     0x750830: b.ls            #0x750870
    // 0x750834: ldr             x2, [fp, #0x10]
    // 0x750838: r0 = computeMaxIntrinsicWidth()
    //     0x750838: bl              #0x750888  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMaxIntrinsicWidth
    // 0x75083c: r0 = inline_Allocate_Double()
    //     0x75083c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x750840: add             x0, x0, #0x10
    //     0x750844: cmp             x1, x0
    //     0x750848: b.ls            #0x750878
    //     0x75084c: str             x0, [THR, #0x50]  ; THR::top
    //     0x750850: sub             x0, x0, #0xf
    //     0x750854: movz            x1, #0xe15c
    //     0x750858: movk            x1, #0x3, lsl #16
    //     0x75085c: stur            x1, [x0, #-1]
    // 0x750860: StoreField: r0->field_7 = d0
    //     0x750860: stur            d0, [x0, #7]
    // 0x750864: LeaveFrame
    //     0x750864: mov             SP, fp
    //     0x750868: ldp             fp, lr, [SP], #0x10
    // 0x75086c: ret
    //     0x75086c: ret             
    // 0x750870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x750870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x750874: b               #0x750834
    // 0x750878: SaveReg d0
    //     0x750878: str             q0, [SP, #-0x10]!
    // 0x75087c: r0 = AllocateDouble()
    //     0x75087c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x750880: RestoreReg d0
    //     0x750880: ldr             q0, [SP], #0x10
    // 0x750884: b               #0x750860
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x750888, size: 0x74
    // 0x750888: EnterFrame
    //     0x750888: stp             fp, lr, [SP, #-0x10]!
    //     0x75088c: mov             fp, SP
    // 0x750890: AllocStack(0x10)
    //     0x750890: sub             SP, SP, #0x10
    // 0x750894: SetupParameters(RenderCSSBox this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x750894: stur            x1, [fp, #-8]
    //     0x750898: stur            x2, [fp, #-0x10]
    // 0x75089c: CheckStackOverflow
    //     0x75089c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7508a0: cmp             SP, x16
    //     0x7508a4: b.ls            #0x7508f4
    // 0x7508a8: r1 = 1
    //     0x7508a8: movz            x1, #0x1
    // 0x7508ac: r0 = AllocateContext()
    //     0x7508ac: bl              #0xec126c  ; AllocateContextStub
    // 0x7508b0: mov             x1, x0
    // 0x7508b4: ldur            x0, [fp, #-0x10]
    // 0x7508b8: StoreField: r1->field_f = r0
    //     0x7508b8: stur            w0, [x1, #0xf]
    // 0x7508bc: ldur            x0, [fp, #-8]
    // 0x7508c0: LoadField: r3 = r0->field_5f
    //     0x7508c0: ldur            w3, [x0, #0x5f]
    // 0x7508c4: DecompressPointer r3
    //     0x7508c4: add             x3, x3, HEAP, lsl #32
    // 0x7508c8: mov             x2, x1
    // 0x7508cc: stur            x3, [fp, #-0x10]
    // 0x7508d0: r1 = Function '<anonymous closure>':.
    //     0x7508d0: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5d8] AnonymousClosure: (0x74fe0c), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicWidth (0x74fe8c)
    //     0x7508d4: ldr             x1, [x1, #0x5d8]
    // 0x7508d8: r0 = AllocateClosure()
    //     0x7508d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7508dc: ldur            x1, [fp, #-0x10]
    // 0x7508e0: mov             x2, x0
    // 0x7508e4: r0 = getIntrinsicDimension()
    //     0x7508e4: bl              #0x734fb4  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::getIntrinsicDimension
    // 0x7508e8: LeaveFrame
    //     0x7508e8: mov             SP, fp
    //     0x7508ec: ldp             fp, lr, [SP], #0x10
    // 0x7508f0: ret
    //     0x7508f0: ret             
    // 0x7508f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7508f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7508f8: b               #0x7508a8
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x753b98, size: 0x24
    // 0x753b98: EnterFrame
    //     0x753b98: stp             fp, lr, [SP, #-0x10]!
    //     0x753b9c: mov             fp, SP
    // 0x753ba0: ldr             x2, [fp, #0x10]
    // 0x753ba4: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x753ba4: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5a0] AnonymousClosure: (0x753bbc), in [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMaxIntrinsicHeight (0x753c30)
    //     0x753ba8: ldr             x1, [x1, #0x5a0]
    // 0x753bac: r0 = AllocateClosure()
    //     0x753bac: bl              #0xec1630  ; AllocateClosureStub
    // 0x753bb0: LeaveFrame
    //     0x753bb0: mov             SP, fp
    //     0x753bb4: ldp             fp, lr, [SP], #0x10
    // 0x753bb8: ret
    //     0x753bb8: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x753bbc, size: 0x74
    // 0x753bbc: EnterFrame
    //     0x753bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x753bc0: mov             fp, SP
    // 0x753bc4: ldr             x0, [fp, #0x18]
    // 0x753bc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x753bc8: ldur            w1, [x0, #0x17]
    // 0x753bcc: DecompressPointer r1
    //     0x753bcc: add             x1, x1, HEAP, lsl #32
    // 0x753bd0: CheckStackOverflow
    //     0x753bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753bd4: cmp             SP, x16
    //     0x753bd8: b.ls            #0x753c18
    // 0x753bdc: ldr             x2, [fp, #0x10]
    // 0x753be0: r0 = computeMaxIntrinsicHeight()
    //     0x753be0: bl              #0x753c30  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::computeMaxIntrinsicHeight
    // 0x753be4: r0 = inline_Allocate_Double()
    //     0x753be4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x753be8: add             x0, x0, #0x10
    //     0x753bec: cmp             x1, x0
    //     0x753bf0: b.ls            #0x753c20
    //     0x753bf4: str             x0, [THR, #0x50]  ; THR::top
    //     0x753bf8: sub             x0, x0, #0xf
    //     0x753bfc: movz            x1, #0xe15c
    //     0x753c00: movk            x1, #0x3, lsl #16
    //     0x753c04: stur            x1, [x0, #-1]
    // 0x753c08: StoreField: r0->field_7 = d0
    //     0x753c08: stur            d0, [x0, #7]
    // 0x753c0c: LeaveFrame
    //     0x753c0c: mov             SP, fp
    //     0x753c10: ldp             fp, lr, [SP], #0x10
    // 0x753c14: ret
    //     0x753c14: ret             
    // 0x753c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753c1c: b               #0x753bdc
    // 0x753c20: SaveReg d0
    //     0x753c20: str             q0, [SP, #-0x10]!
    // 0x753c24: r0 = AllocateDouble()
    //     0x753c24: bl              #0xec2254  ; AllocateDoubleStub
    // 0x753c28: RestoreReg d0
    //     0x753c28: ldr             q0, [SP], #0x10
    // 0x753c2c: b               #0x753c08
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x753c30, size: 0x74
    // 0x753c30: EnterFrame
    //     0x753c30: stp             fp, lr, [SP, #-0x10]!
    //     0x753c34: mov             fp, SP
    // 0x753c38: AllocStack(0x10)
    //     0x753c38: sub             SP, SP, #0x10
    // 0x753c3c: SetupParameters(RenderCSSBox this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x753c3c: stur            x1, [fp, #-8]
    //     0x753c40: stur            x2, [fp, #-0x10]
    // 0x753c44: CheckStackOverflow
    //     0x753c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753c48: cmp             SP, x16
    //     0x753c4c: b.ls            #0x753c9c
    // 0x753c50: r1 = 1
    //     0x753c50: movz            x1, #0x1
    // 0x753c54: r0 = AllocateContext()
    //     0x753c54: bl              #0xec126c  ; AllocateContextStub
    // 0x753c58: mov             x1, x0
    // 0x753c5c: ldur            x0, [fp, #-0x10]
    // 0x753c60: StoreField: r1->field_f = r0
    //     0x753c60: stur            w0, [x1, #0xf]
    // 0x753c64: ldur            x0, [fp, #-8]
    // 0x753c68: LoadField: r3 = r0->field_5f
    //     0x753c68: ldur            w3, [x0, #0x5f]
    // 0x753c6c: DecompressPointer r3
    //     0x753c6c: add             x3, x3, HEAP, lsl #32
    // 0x753c70: mov             x2, x1
    // 0x753c74: stur            x3, [fp, #-0x10]
    // 0x753c78: r1 = Function '<anonymous closure>':.
    //     0x753c78: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d5a8] AnonymousClosure: (0x7532bc), in [package:flutter/src/rendering/stack.dart] RenderStack::computeMaxIntrinsicHeight (0x75333c)
    //     0x753c7c: ldr             x1, [x1, #0x5a8]
    // 0x753c80: r0 = AllocateClosure()
    //     0x753c80: bl              #0xec1630  ; AllocateClosureStub
    // 0x753c84: ldur            x1, [fp, #-0x10]
    // 0x753c88: mov             x2, x0
    // 0x753c8c: r0 = getIntrinsicDimension()
    //     0x753c8c: bl              #0x734fb4  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::getIntrinsicDimension
    // 0x753c90: LeaveFrame
    //     0x753c90: mov             SP, fp
    //     0x753c94: ldp             fp, lr, [SP], #0x10
    // 0x753c98: ret
    //     0x753c98: ret             
    // 0x753c9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753c9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753ca0: b               #0x753c50
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x7573b8, size: 0x40
    // 0x7573b8: EnterFrame
    //     0x7573b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7573bc: mov             fp, SP
    // 0x7573c0: CheckStackOverflow
    //     0x7573c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7573c4: cmp             SP, x16
    //     0x7573c8: b.ls            #0x7573f0
    // 0x7573cc: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static.
    //     0x7573cc: add             x3, PP, #0x44, lsl #12  ; [pp+0x44d20] Closure: (RenderBox, BoxConstraints) => Size from Function 'dryLayoutChild': static. (0x7e54fb140c14)
    //     0x7573d0: ldr             x3, [x3, #0xd20]
    // 0x7573d4: r0 = _computeSize()
    //     0x7573d4: bl              #0x7573f8  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::_computeSize
    // 0x7573d8: LoadField: r1 = r0->field_7
    //     0x7573d8: ldur            w1, [x0, #7]
    // 0x7573dc: DecompressPointer r1
    //     0x7573dc: add             x1, x1, HEAP, lsl #32
    // 0x7573e0: mov             x0, x1
    // 0x7573e4: LeaveFrame
    //     0x7573e4: mov             SP, fp
    //     0x7573e8: ldp             fp, lr, [SP], #0x10
    // 0x7573ec: ret
    //     0x7573ec: ret             
    // 0x7573f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7573f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7573f4: b               #0x7573cc
  }
  _ _computeSize(/* No info */) {
    // ** addr: 0x7573f8, size: 0xa28
    // 0x7573f8: EnterFrame
    //     0x7573f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7573fc: mov             fp, SP
    // 0x757400: AllocStack(0x70)
    //     0x757400: sub             SP, SP, #0x70
    // 0x757404: SetupParameters(RenderCSSBox this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x757404: mov             x0, x3
    //     0x757408: stur            x3, [fp, #-0x20]
    //     0x75740c: mov             x3, x1
    //     0x757410: stur            x2, [fp, #-8]
    //     0x757414: stur            x1, [fp, #-0x18]
    // 0x757418: CheckStackOverflow
    //     0x757418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75741c: cmp             SP, x16
    //     0x757420: b.ls            #0x757c18
    // 0x757424: LoadField: r1 = r3->field_57
    //     0x757424: ldur            x1, [x3, #0x57]
    // 0x757428: cbnz            x1, #0x757490
    // 0x75742c: mov             x1, x2
    // 0x757430: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x757430: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x757434: r0 = constrainWidth()
    //     0x757434: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x757438: ldur            x1, [fp, #-8]
    // 0x75743c: stur            d0, [fp, #-0x40]
    // 0x757440: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x757440: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x757444: r0 = constrainHeight()
    //     0x757444: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x757448: stur            d0, [fp, #-0x48]
    // 0x75744c: r0 = Size()
    //     0x75744c: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x757450: ldur            d0, [fp, #-0x40]
    // 0x757454: stur            x0, [fp, #-0x10]
    // 0x757458: StoreField: r0->field_7 = d0
    //     0x757458: stur            d0, [x0, #7]
    // 0x75745c: ldur            d0, [fp, #-0x48]
    // 0x757460: StoreField: r0->field_f = d0
    //     0x757460: stur            d0, [x0, #0xf]
    // 0x757464: r0 = _Sizes()
    //     0x757464: bl              #0x7582ec  ; Allocate_SizesStub -> _Sizes (size=0x10)
    // 0x757468: mov             x1, x0
    // 0x75746c: ldur            x0, [fp, #-0x10]
    // 0x757470: StoreField: r1->field_7 = r0
    //     0x757470: stur            w0, [x1, #7]
    // 0x757474: r0 = Instance_Size
    //     0x757474: add             x0, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0x757478: ldr             x0, [x0, #0xa20]
    // 0x75747c: StoreField: r1->field_b = r0
    //     0x75747c: stur            w0, [x1, #0xb]
    // 0x757480: mov             x0, x1
    // 0x757484: LeaveFrame
    //     0x757484: mov             SP, fp
    //     0x757488: ldp             fp, lr, [SP], #0x10
    // 0x75748c: ret
    //     0x75748c: ret             
    // 0x757490: ldur            x1, [fp, #-8]
    // 0x757494: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x757494: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x757498: r0 = constrainWidth()
    //     0x757498: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x75749c: ldur            x1, [fp, #-8]
    // 0x7574a0: stur            d0, [fp, #-0x40]
    // 0x7574a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7574a4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7574a8: r0 = constrainHeight()
    //     0x7574a8: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x7574ac: stur            d0, [fp, #-0x48]
    // 0x7574b0: r0 = Size()
    //     0x7574b0: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7574b4: mov             x3, x0
    // 0x7574b8: ldur            d0, [fp, #-0x40]
    // 0x7574bc: stur            x3, [fp, #-0x30]
    // 0x7574c0: StoreField: r3->field_7 = d0
    //     0x7574c0: stur            d0, [x3, #7]
    // 0x7574c4: ldur            d1, [fp, #-0x48]
    // 0x7574c8: StoreField: r3->field_f = d1
    //     0x7574c8: stur            d1, [x3, #0xf]
    // 0x7574cc: ldur            x4, [fp, #-0x18]
    // 0x7574d0: LoadField: r5 = r4->field_5f
    //     0x7574d0: ldur            w5, [x4, #0x5f]
    // 0x7574d4: DecompressPointer r5
    //     0x7574d4: add             x5, x5, HEAP, lsl #32
    // 0x7574d8: stur            x5, [fp, #-0x28]
    // 0x7574dc: cmp             w5, NULL
    // 0x7574e0: b.eq            #0x757c20
    // 0x7574e4: LoadField: r6 = r5->field_7
    //     0x7574e4: ldur            w6, [x5, #7]
    // 0x7574e8: DecompressPointer r6
    //     0x7574e8: add             x6, x6, HEAP, lsl #32
    // 0x7574ec: stur            x6, [fp, #-0x10]
    // 0x7574f0: cmp             w6, NULL
    // 0x7574f4: b.eq            #0x757c24
    // 0x7574f8: mov             x0, x6
    // 0x7574fc: r2 = Null
    //     0x7574fc: mov             x2, NULL
    // 0x757500: r1 = Null
    //     0x757500: mov             x1, NULL
    // 0x757504: r4 = LoadClassIdInstr(r0)
    //     0x757504: ldur            x4, [x0, #-1]
    //     0x757508: ubfx            x4, x4, #0xc, #0x14
    // 0x75750c: cmp             x4, #0xc76
    // 0x757510: b.eq            #0x757528
    // 0x757514: r8 = CSSBoxParentData
    //     0x757514: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x757518: ldr             x8, [x8, #0x3d8]
    // 0x75751c: r3 = Null
    //     0x75751c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d640] Null
    //     0x757520: ldr             x3, [x3, #0x640]
    // 0x757524: r0 = DefaultTypeTest()
    //     0x757524: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x757528: ldur            x0, [fp, #-0x10]
    // 0x75752c: LoadField: r1 = r0->field_13
    //     0x75752c: ldur            w1, [x0, #0x13]
    // 0x757530: DecompressPointer r1
    //     0x757530: add             x1, x1, HEAP, lsl #32
    // 0x757534: ldur            x0, [fp, #-0x18]
    // 0x757538: stur            x1, [fp, #-0x38]
    // 0x75753c: LoadField: r2 = r0->field_6b
    //     0x75753c: ldur            w2, [x0, #0x6b]
    // 0x757540: DecompressPointer r2
    //     0x757540: add             x2, x2, HEAP, lsl #32
    // 0x757544: LoadField: r3 = r2->field_f
    //     0x757544: ldur            w3, [x2, #0xf]
    // 0x757548: DecompressPointer r3
    //     0x757548: add             x3, x3, HEAP, lsl #32
    // 0x75754c: r16 = Instance_Unit
    //     0x75754c: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x757550: ldr             x16, [x16, #0x950]
    // 0x757554: cmp             w3, w16
    // 0x757558: b.eq            #0x757568
    // 0x75755c: LoadField: d0 = r2->field_7
    //     0x75755c: ldur            d0, [x2, #7]
    // 0x757560: mov             x1, x0
    // 0x757564: b               #0x757660
    // 0x757568: LoadField: r2 = r0->field_73
    //     0x757568: ldur            w2, [x0, #0x73]
    // 0x75756c: DecompressPointer r2
    //     0x75756c: add             x2, x2, HEAP, lsl #32
    // 0x757570: LoadField: r3 = r2->field_7
    //     0x757570: ldur            w3, [x2, #7]
    // 0x757574: DecompressPointer r3
    //     0x757574: add             x3, x3, HEAP, lsl #32
    // 0x757578: cmp             w3, NULL
    // 0x75757c: b.ne            #0x757588
    // 0x757580: r2 = Null
    //     0x757580: mov             x2, NULL
    // 0x757584: b               #0x7575b4
    // 0x757588: LoadField: d0 = r3->field_7
    //     0x757588: ldur            d0, [x3, #7]
    // 0x75758c: r2 = inline_Allocate_Double()
    //     0x75758c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x757590: add             x2, x2, #0x10
    //     0x757594: cmp             x3, x2
    //     0x757598: b.ls            #0x757c28
    //     0x75759c: str             x2, [THR, #0x50]  ; THR::top
    //     0x7575a0: sub             x2, x2, #0xf
    //     0x7575a4: movz            x3, #0xe15c
    //     0x7575a8: movk            x3, #0x3, lsl #16
    //     0x7575ac: stur            x3, [x2, #-1]
    // 0x7575b0: StoreField: r2->field_7 = d0
    //     0x7575b0: stur            d0, [x2, #7]
    // 0x7575b4: cmp             w2, NULL
    // 0x7575b8: b.ne            #0x7575c0
    // 0x7575bc: r2 = 0
    //     0x7575bc: movz            x2, #0
    // 0x7575c0: ldur            d0, [fp, #-0x40]
    // 0x7575c4: r3 = inline_Allocate_Double()
    //     0x7575c4: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0x7575c8: add             x3, x3, #0x10
    //     0x7575cc: cmp             x4, x3
    //     0x7575d0: b.ls            #0x757c44
    //     0x7575d4: str             x3, [THR, #0x50]  ; THR::top
    //     0x7575d8: sub             x3, x3, #0xf
    //     0x7575dc: movz            x4, #0xe15c
    //     0x7575e0: movk            x4, #0x3, lsl #16
    //     0x7575e4: stur            x4, [x3, #-1]
    // 0x7575e8: StoreField: r3->field_7 = d0
    //     0x7575e8: stur            d0, [x3, #7]
    // 0x7575ec: stp             x2, x3, [SP]
    // 0x7575f0: r0 = -()
    //     0x7575f0: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x7575f4: ldur            x1, [fp, #-0x18]
    // 0x7575f8: LoadField: r2 = r1->field_73
    //     0x7575f8: ldur            w2, [x1, #0x73]
    // 0x7575fc: DecompressPointer r2
    //     0x7575fc: add             x2, x2, HEAP, lsl #32
    // 0x757600: LoadField: r3 = r2->field_b
    //     0x757600: ldur            w3, [x2, #0xb]
    // 0x757604: DecompressPointer r3
    //     0x757604: add             x3, x3, HEAP, lsl #32
    // 0x757608: cmp             w3, NULL
    // 0x75760c: b.ne            #0x757618
    // 0x757610: r2 = Null
    //     0x757610: mov             x2, NULL
    // 0x757614: b               #0x757644
    // 0x757618: LoadField: d0 = r3->field_7
    //     0x757618: ldur            d0, [x3, #7]
    // 0x75761c: r2 = inline_Allocate_Double()
    //     0x75761c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x757620: add             x2, x2, #0x10
    //     0x757624: cmp             x3, x2
    //     0x757628: b.ls            #0x757c68
    //     0x75762c: str             x2, [THR, #0x50]  ; THR::top
    //     0x757630: sub             x2, x2, #0xf
    //     0x757634: movz            x3, #0xe15c
    //     0x757638: movk            x3, #0x3, lsl #16
    //     0x75763c: stur            x3, [x2, #-1]
    // 0x757640: StoreField: r2->field_7 = d0
    //     0x757640: stur            d0, [x2, #7]
    // 0x757644: cmp             w2, NULL
    // 0x757648: b.ne            #0x757650
    // 0x75764c: r2 = 0
    //     0x75764c: movz            x2, #0
    // 0x757650: stp             x2, x0, [SP]
    // 0x757654: r0 = -()
    //     0x757654: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x757658: LoadField: d0 = r0->field_7
    //     0x757658: ldur            d0, [x0, #7]
    // 0x75765c: ldur            x1, [fp, #-0x18]
    // 0x757660: stur            d0, [fp, #-0x50]
    // 0x757664: LoadField: r0 = r1->field_6f
    //     0x757664: ldur            w0, [x1, #0x6f]
    // 0x757668: DecompressPointer r0
    //     0x757668: add             x0, x0, HEAP, lsl #32
    // 0x75766c: LoadField: r2 = r0->field_f
    //     0x75766c: ldur            w2, [x0, #0xf]
    // 0x757670: DecompressPointer r2
    //     0x757670: add             x2, x2, HEAP, lsl #32
    // 0x757674: r16 = Instance_Unit
    //     0x757674: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x757678: ldr             x16, [x16, #0x950]
    // 0x75767c: cmp             w2, w16
    // 0x757680: b.eq            #0x757694
    // 0x757684: LoadField: d1 = r0->field_7
    //     0x757684: ldur            d1, [x0, #7]
    // 0x757688: mov             v0.16b, v1.16b
    // 0x75768c: mov             x0, x1
    // 0x757690: b               #0x75778c
    // 0x757694: LoadField: r0 = r1->field_73
    //     0x757694: ldur            w0, [x1, #0x73]
    // 0x757698: DecompressPointer r0
    //     0x757698: add             x0, x0, HEAP, lsl #32
    // 0x75769c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x75769c: ldur            w2, [x0, #0x17]
    // 0x7576a0: DecompressPointer r2
    //     0x7576a0: add             x2, x2, HEAP, lsl #32
    // 0x7576a4: cmp             w2, NULL
    // 0x7576a8: b.ne            #0x7576b4
    // 0x7576ac: r0 = Null
    //     0x7576ac: mov             x0, NULL
    // 0x7576b0: b               #0x7576e0
    // 0x7576b4: LoadField: d1 = r2->field_7
    //     0x7576b4: ldur            d1, [x2, #7]
    // 0x7576b8: r0 = inline_Allocate_Double()
    //     0x7576b8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x7576bc: add             x0, x0, #0x10
    //     0x7576c0: cmp             x2, x0
    //     0x7576c4: b.ls            #0x757c84
    //     0x7576c8: str             x0, [THR, #0x50]  ; THR::top
    //     0x7576cc: sub             x0, x0, #0xf
    //     0x7576d0: movz            x2, #0xe15c
    //     0x7576d4: movk            x2, #0x3, lsl #16
    //     0x7576d8: stur            x2, [x0, #-1]
    // 0x7576dc: StoreField: r0->field_7 = d1
    //     0x7576dc: stur            d1, [x0, #7]
    // 0x7576e0: cmp             w0, NULL
    // 0x7576e4: b.ne            #0x7576ec
    // 0x7576e8: r0 = 0
    //     0x7576e8: movz            x0, #0
    // 0x7576ec: ldur            d1, [fp, #-0x48]
    // 0x7576f0: r2 = inline_Allocate_Double()
    //     0x7576f0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7576f4: add             x2, x2, #0x10
    //     0x7576f8: cmp             x3, x2
    //     0x7576fc: b.ls            #0x757c9c
    //     0x757700: str             x2, [THR, #0x50]  ; THR::top
    //     0x757704: sub             x2, x2, #0xf
    //     0x757708: movz            x3, #0xe15c
    //     0x75770c: movk            x3, #0x3, lsl #16
    //     0x757710: stur            x3, [x2, #-1]
    // 0x757714: StoreField: r2->field_7 = d1
    //     0x757714: stur            d1, [x2, #7]
    // 0x757718: stp             x0, x2, [SP]
    // 0x75771c: r0 = -()
    //     0x75771c: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x757720: ldur            x1, [fp, #-0x18]
    // 0x757724: LoadField: r2 = r1->field_73
    //     0x757724: ldur            w2, [x1, #0x73]
    // 0x757728: DecompressPointer r2
    //     0x757728: add             x2, x2, HEAP, lsl #32
    // 0x75772c: LoadField: r3 = r2->field_1b
    //     0x75772c: ldur            w3, [x2, #0x1b]
    // 0x757730: DecompressPointer r3
    //     0x757730: add             x3, x3, HEAP, lsl #32
    // 0x757734: cmp             w3, NULL
    // 0x757738: b.ne            #0x757744
    // 0x75773c: r2 = Null
    //     0x75773c: mov             x2, NULL
    // 0x757740: b               #0x757770
    // 0x757744: LoadField: d0 = r3->field_7
    //     0x757744: ldur            d0, [x3, #7]
    // 0x757748: r2 = inline_Allocate_Double()
    //     0x757748: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x75774c: add             x2, x2, #0x10
    //     0x757750: cmp             x3, x2
    //     0x757754: b.ls            #0x757cb8
    //     0x757758: str             x2, [THR, #0x50]  ; THR::top
    //     0x75775c: sub             x2, x2, #0xf
    //     0x757760: movz            x3, #0xe15c
    //     0x757764: movk            x3, #0x3, lsl #16
    //     0x757768: stur            x3, [x2, #-1]
    // 0x75776c: StoreField: r2->field_7 = d0
    //     0x75776c: stur            d0, [x2, #7]
    // 0x757770: cmp             w2, NULL
    // 0x757774: b.ne            #0x75777c
    // 0x757778: r2 = 0
    //     0x757778: movz            x2, #0
    // 0x75777c: stp             x2, x0, [SP]
    // 0x757780: r0 = -()
    //     0x757780: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x757784: LoadField: d0 = r0->field_7
    //     0x757784: ldur            d0, [x0, #7]
    // 0x757788: ldur            x0, [fp, #-0x18]
    // 0x75778c: LoadField: r1 = r0->field_6b
    //     0x75778c: ldur            w1, [x0, #0x6b]
    // 0x757790: DecompressPointer r1
    //     0x757790: add             x1, x1, HEAP, lsl #32
    // 0x757794: LoadField: r2 = r1->field_f
    //     0x757794: ldur            w2, [x1, #0xf]
    // 0x757798: DecompressPointer r2
    //     0x757798: add             x2, x2, HEAP, lsl #32
    // 0x75779c: r16 = Instance_Unit
    //     0x75779c: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x7577a0: ldr             x16, [x16, #0x950]
    // 0x7577a4: cmp             w2, w16
    // 0x7577a8: b.eq            #0x7577b4
    // 0x7577ac: LoadField: d1 = r1->field_7
    //     0x7577ac: ldur            d1, [x1, #7]
    // 0x7577b0: b               #0x7577b8
    // 0x7577b4: d1 = 0.000000
    //     0x7577b4: eor             v1.16b, v1.16b, v1.16b
    // 0x7577b8: LoadField: r1 = r0->field_6f
    //     0x7577b8: ldur            w1, [x0, #0x6f]
    // 0x7577bc: DecompressPointer r1
    //     0x7577bc: add             x1, x1, HEAP, lsl #32
    // 0x7577c0: LoadField: r2 = r1->field_f
    //     0x7577c0: ldur            w2, [x1, #0xf]
    // 0x7577c4: DecompressPointer r2
    //     0x7577c4: add             x2, x2, HEAP, lsl #32
    // 0x7577c8: r16 = Instance_Unit
    //     0x7577c8: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x7577cc: ldr             x16, [x16, #0x950]
    // 0x7577d0: cmp             w2, w16
    // 0x7577d4: b.eq            #0x7577e4
    // 0x7577d8: LoadField: d2 = r1->field_7
    //     0x7577d8: ldur            d2, [x1, #7]
    // 0x7577dc: mov             v3.16b, v2.16b
    // 0x7577e0: b               #0x7577e8
    // 0x7577e4: d3 = 0.000000
    //     0x7577e4: eor             v3.16b, v3.16b, v3.16b
    // 0x7577e8: ldur            x2, [fp, #-0x38]
    // 0x7577ec: ldur            d2, [fp, #-0x50]
    // 0x7577f0: r1 = inline_Allocate_Double()
    //     0x7577f0: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0x7577f4: add             x1, x1, #0x10
    //     0x7577f8: cmp             x3, x1
    //     0x7577fc: b.ls            #0x757cd4
    //     0x757800: str             x1, [THR, #0x50]  ; THR::top
    //     0x757804: sub             x1, x1, #0xf
    //     0x757808: movz            x3, #0xe15c
    //     0x75780c: movk            x3, #0x3, lsl #16
    //     0x757810: stur            x3, [x1, #-1]
    // 0x757814: StoreField: r1->field_7 = d2
    //     0x757814: stur            d2, [x1, #7]
    // 0x757818: r3 = inline_Allocate_Double()
    //     0x757818: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0x75781c: add             x3, x3, #0x10
    //     0x757820: cmp             x4, x3
    //     0x757824: b.ls            #0x757cf8
    //     0x757828: str             x3, [THR, #0x50]  ; THR::top
    //     0x75782c: sub             x3, x3, #0xf
    //     0x757830: movz            x4, #0xe15c
    //     0x757834: movk            x4, #0x3, lsl #16
    //     0x757838: stur            x4, [x3, #-1]
    // 0x75783c: StoreField: r3->field_7 = d0
    //     0x75783c: stur            d0, [x3, #7]
    // 0x757840: r4 = inline_Allocate_Double()
    //     0x757840: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0x757844: add             x4, x4, #0x10
    //     0x757848: cmp             x5, x4
    //     0x75784c: b.ls            #0x757d24
    //     0x757850: str             x4, [THR, #0x50]  ; THR::top
    //     0x757854: sub             x4, x4, #0xf
    //     0x757858: movz            x5, #0xe15c
    //     0x75785c: movk            x5, #0x3, lsl #16
    //     0x757860: stur            x5, [x4, #-1]
    // 0x757864: StoreField: r4->field_7 = d1
    //     0x757864: stur            d1, [x4, #7]
    // 0x757868: r5 = inline_Allocate_Double()
    //     0x757868: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0x75786c: add             x5, x5, #0x10
    //     0x757870: cmp             x6, x5
    //     0x757874: b.ls            #0x757d48
    //     0x757878: str             x5, [THR, #0x50]  ; THR::top
    //     0x75787c: sub             x5, x5, #0xf
    //     0x757880: movz            x6, #0xe15c
    //     0x757884: movk            x6, #0x3, lsl #16
    //     0x757888: stur            x6, [x5, #-1]
    // 0x75788c: StoreField: r5->field_7 = d3
    //     0x75788c: stur            d3, [x5, #7]
    // 0x757890: stp             x3, x1, [SP, #0x10]
    // 0x757894: stp             x5, x4, [SP]
    // 0x757898: ldur            x1, [fp, #-8]
    // 0x75789c: r4 = const [0, 0x5, 0x4, 0x1, maxHeight, 0x2, maxWidth, 0x1, minHeight, 0x4, minWidth, 0x3, null]
    //     0x75789c: add             x4, PP, #0x5d, lsl #12  ; [pp+0x5d650] List(13) [0, 0x5, 0x4, 0x1, "maxHeight", 0x2, "maxWidth", 0x1, "minHeight", 0x4, "minWidth", 0x3, Null]
    //     0x7578a0: ldr             x4, [x4, #0x650]
    // 0x7578a4: r0 = copyWith()
    //     0x7578a4: bl              #0x73d230  ; [package:flutter/src/rendering/box.dart] BoxConstraints::copyWith
    // 0x7578a8: mov             x1, x0
    // 0x7578ac: stur            x1, [fp, #-0x10]
    // 0x7578b0: ldur            x16, [fp, #-0x20]
    // 0x7578b4: ldur            lr, [fp, #-0x28]
    // 0x7578b8: stp             lr, x16, [SP, #8]
    // 0x7578bc: str             x1, [SP]
    // 0x7578c0: ldur            x0, [fp, #-0x20]
    // 0x7578c4: ClosureCall
    //     0x7578c4: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7578c8: ldur            x2, [x0, #0x1f]
    //     0x7578cc: blr             x2
    // 0x7578d0: mov             x1, x0
    // 0x7578d4: ldur            x0, [fp, #-0x38]
    // 0x7578d8: stur            x1, [fp, #-0x28]
    // 0x7578dc: cmp             w0, NULL
    // 0x7578e0: b.eq            #0x757904
    // 0x7578e4: ldur            x16, [fp, #-0x20]
    // 0x7578e8: stp             x0, x16, [SP, #8]
    // 0x7578ec: ldur            x16, [fp, #-0x10]
    // 0x7578f0: str             x16, [SP]
    // 0x7578f4: ldur            x0, [fp, #-0x20]
    // 0x7578f8: ClosureCall
    //     0x7578f8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7578fc: ldur            x2, [x0, #0x1f]
    //     0x757900: blr             x2
    // 0x757904: ldur            x1, [fp, #-0x18]
    // 0x757908: ldur            x2, [fp, #-0x28]
    // 0x75790c: ldur            x3, [fp, #-0x30]
    // 0x757910: r0 = _calculateUsedMargins()
    //     0x757910: bl              #0x757e20  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::_calculateUsedMargins
    // 0x757914: stur            x0, [fp, #-0x10]
    // 0x757918: LoadField: r1 = r0->field_7
    //     0x757918: ldur            w1, [x0, #7]
    // 0x75791c: DecompressPointer r1
    //     0x75791c: add             x1, x1, HEAP, lsl #32
    // 0x757920: cmp             w1, NULL
    // 0x757924: b.ne            #0x757930
    // 0x757928: r1 = Null
    //     0x757928: mov             x1, NULL
    // 0x75792c: b               #0x75795c
    // 0x757930: LoadField: d0 = r1->field_7
    //     0x757930: ldur            d0, [x1, #7]
    // 0x757934: r1 = inline_Allocate_Double()
    //     0x757934: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x757938: add             x1, x1, #0x10
    //     0x75793c: cmp             x2, x1
    //     0x757940: b.ls            #0x757d74
    //     0x757944: str             x1, [THR, #0x50]  ; THR::top
    //     0x757948: sub             x1, x1, #0xf
    //     0x75794c: movz            x2, #0xe15c
    //     0x757950: movk            x2, #0x3, lsl #16
    //     0x757954: stur            x2, [x1, #-1]
    // 0x757958: StoreField: r1->field_7 = d0
    //     0x757958: stur            d0, [x1, #7]
    // 0x75795c: cmp             w1, NULL
    // 0x757960: b.ne            #0x75796c
    // 0x757964: d0 = 0.000000
    //     0x757964: eor             v0.16b, v0.16b, v0.16b
    // 0x757968: b               #0x757970
    // 0x75796c: LoadField: d0 = r1->field_7
    //     0x75796c: ldur            d0, [x1, #7]
    // 0x757970: LoadField: r1 = r0->field_b
    //     0x757970: ldur            w1, [x0, #0xb]
    // 0x757974: DecompressPointer r1
    //     0x757974: add             x1, x1, HEAP, lsl #32
    // 0x757978: cmp             w1, NULL
    // 0x75797c: b.ne            #0x757988
    // 0x757980: r1 = Null
    //     0x757980: mov             x1, NULL
    // 0x757984: b               #0x7579b4
    // 0x757988: LoadField: d1 = r1->field_7
    //     0x757988: ldur            d1, [x1, #7]
    // 0x75798c: r1 = inline_Allocate_Double()
    //     0x75798c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x757990: add             x1, x1, #0x10
    //     0x757994: cmp             x2, x1
    //     0x757998: b.ls            #0x757d90
    //     0x75799c: str             x1, [THR, #0x50]  ; THR::top
    //     0x7579a0: sub             x1, x1, #0xf
    //     0x7579a4: movz            x2, #0xe15c
    //     0x7579a8: movk            x2, #0x3, lsl #16
    //     0x7579ac: stur            x2, [x1, #-1]
    // 0x7579b0: StoreField: r1->field_7 = d1
    //     0x7579b0: stur            d1, [x1, #7]
    // 0x7579b4: cmp             w1, NULL
    // 0x7579b8: b.ne            #0x7579c0
    // 0x7579bc: r1 = 0
    //     0x7579bc: movz            x1, #0
    // 0x7579c0: r2 = inline_Allocate_Double()
    //     0x7579c0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7579c4: add             x2, x2, #0x10
    //     0x7579c8: cmp             x3, x2
    //     0x7579cc: b.ls            #0x757dac
    //     0x7579d0: str             x2, [THR, #0x50]  ; THR::top
    //     0x7579d4: sub             x2, x2, #0xf
    //     0x7579d8: movz            x3, #0xe15c
    //     0x7579dc: movk            x3, #0x3, lsl #16
    //     0x7579e0: stur            x3, [x2, #-1]
    // 0x7579e4: StoreField: r2->field_7 = d0
    //     0x7579e4: stur            d0, [x2, #7]
    // 0x7579e8: stp             x1, x2, [SP]
    // 0x7579ec: r0 = +()
    //     0x7579ec: bl              #0xebf900  ; [dart:core] _Double::+
    // 0x7579f0: mov             x1, x0
    // 0x7579f4: ldur            x0, [fp, #-0x10]
    // 0x7579f8: stur            x1, [fp, #-0x20]
    // 0x7579fc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7579fc: ldur            w2, [x0, #0x17]
    // 0x757a00: DecompressPointer r2
    //     0x757a00: add             x2, x2, HEAP, lsl #32
    // 0x757a04: cmp             w2, NULL
    // 0x757a08: b.ne            #0x757a14
    // 0x757a0c: r2 = Null
    //     0x757a0c: mov             x2, NULL
    // 0x757a10: b               #0x757a40
    // 0x757a14: LoadField: d0 = r2->field_7
    //     0x757a14: ldur            d0, [x2, #7]
    // 0x757a18: r2 = inline_Allocate_Double()
    //     0x757a18: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x757a1c: add             x2, x2, #0x10
    //     0x757a20: cmp             x3, x2
    //     0x757a24: b.ls            #0x757dc8
    //     0x757a28: str             x2, [THR, #0x50]  ; THR::top
    //     0x757a2c: sub             x2, x2, #0xf
    //     0x757a30: movz            x3, #0xe15c
    //     0x757a34: movk            x3, #0x3, lsl #16
    //     0x757a38: stur            x3, [x2, #-1]
    // 0x757a3c: StoreField: r2->field_7 = d0
    //     0x757a3c: stur            d0, [x2, #7]
    // 0x757a40: cmp             w2, NULL
    // 0x757a44: b.ne            #0x757a50
    // 0x757a48: d0 = 0.000000
    //     0x757a48: eor             v0.16b, v0.16b, v0.16b
    // 0x757a4c: b               #0x757a54
    // 0x757a50: LoadField: d0 = r2->field_7
    //     0x757a50: ldur            d0, [x2, #7]
    // 0x757a54: LoadField: r2 = r0->field_1b
    //     0x757a54: ldur            w2, [x0, #0x1b]
    // 0x757a58: DecompressPointer r2
    //     0x757a58: add             x2, x2, HEAP, lsl #32
    // 0x757a5c: cmp             w2, NULL
    // 0x757a60: b.ne            #0x757a6c
    // 0x757a64: r0 = Null
    //     0x757a64: mov             x0, NULL
    // 0x757a68: b               #0x757a98
    // 0x757a6c: LoadField: d1 = r2->field_7
    //     0x757a6c: ldur            d1, [x2, #7]
    // 0x757a70: r0 = inline_Allocate_Double()
    //     0x757a70: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x757a74: add             x0, x0, #0x10
    //     0x757a78: cmp             x2, x0
    //     0x757a7c: b.ls            #0x757de4
    //     0x757a80: str             x0, [THR, #0x50]  ; THR::top
    //     0x757a84: sub             x0, x0, #0xf
    //     0x757a88: movz            x2, #0xe15c
    //     0x757a8c: movk            x2, #0x3, lsl #16
    //     0x757a90: stur            x2, [x0, #-1]
    // 0x757a94: StoreField: r0->field_7 = d1
    //     0x757a94: stur            d1, [x0, #7]
    // 0x757a98: cmp             w0, NULL
    // 0x757a9c: b.ne            #0x757aa8
    // 0x757aa0: r2 = 0
    //     0x757aa0: movz            x2, #0
    // 0x757aa4: b               #0x757aac
    // 0x757aa8: mov             x2, x0
    // 0x757aac: ldur            x0, [fp, #-0x18]
    // 0x757ab0: r3 = inline_Allocate_Double()
    //     0x757ab0: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0x757ab4: add             x3, x3, #0x10
    //     0x757ab8: cmp             x4, x3
    //     0x757abc: b.ls            #0x757dfc
    //     0x757ac0: str             x3, [THR, #0x50]  ; THR::top
    //     0x757ac4: sub             x3, x3, #0xf
    //     0x757ac8: movz            x4, #0xe15c
    //     0x757acc: movk            x4, #0x3, lsl #16
    //     0x757ad0: stur            x4, [x3, #-1]
    // 0x757ad4: StoreField: r3->field_7 = d0
    //     0x757ad4: stur            d0, [x3, #7]
    // 0x757ad8: stp             x2, x3, [SP]
    // 0x757adc: r0 = +()
    //     0x757adc: bl              #0xebf900  ; [dart:core] _Double::+
    // 0x757ae0: mov             x1, x0
    // 0x757ae4: ldur            x0, [fp, #-0x18]
    // 0x757ae8: LoadField: r2 = r0->field_67
    //     0x757ae8: ldur            w2, [x0, #0x67]
    // 0x757aec: DecompressPointer r2
    //     0x757aec: add             x2, x2, HEAP, lsl #32
    // 0x757af0: LoadField: r3 = r2->field_7
    //     0x757af0: ldur            x3, [x2, #7]
    // 0x757af4: cmp             x3, #2
    // 0x757af8: b.gt            #0x757ba0
    // 0x757afc: cmp             x3, #1
    // 0x757b00: b.gt            #0x757b74
    // 0x757b04: cmp             x3, #0
    // 0x757b08: b.gt            #0x757b54
    // 0x757b0c: LoadField: r2 = r0->field_7b
    //     0x757b0c: ldur            w2, [x0, #0x7b]
    // 0x757b10: DecompressPointer r2
    //     0x757b10: add             x2, x2, HEAP, lsl #32
    // 0x757b14: tbnz            w2, #4, #0x757b34
    // 0x757b18: ldur            x2, [fp, #-0x28]
    // 0x757b1c: ldur            x0, [fp, #-0x20]
    // 0x757b20: LoadField: d0 = r2->field_7
    //     0x757b20: ldur            d0, [x2, #7]
    // 0x757b24: LoadField: d1 = r0->field_7
    //     0x757b24: ldur            d1, [x0, #7]
    // 0x757b28: fadd            d2, d0, d1
    // 0x757b2c: mov             v0.16b, v2.16b
    // 0x757b30: b               #0x757b3c
    // 0x757b34: ldur            x2, [fp, #-0x28]
    // 0x757b38: ldur            d0, [fp, #-0x40]
    // 0x757b3c: LoadField: d1 = r2->field_f
    //     0x757b3c: ldur            d1, [x2, #0xf]
    // 0x757b40: LoadField: d2 = r1->field_7
    //     0x757b40: ldur            d2, [x1, #7]
    // 0x757b44: fadd            d3, d1, d2
    // 0x757b48: mov             v1.16b, v0.16b
    // 0x757b4c: mov             v0.16b, v3.16b
    // 0x757b50: b               #0x757bcc
    // 0x757b54: ldur            x2, [fp, #-0x28]
    // 0x757b58: ldur            x0, [fp, #-0x20]
    // 0x757b5c: LoadField: d0 = r2->field_7
    //     0x757b5c: ldur            d0, [x2, #7]
    // 0x757b60: LoadField: d1 = r0->field_7
    //     0x757b60: ldur            d1, [x0, #7]
    // 0x757b64: fadd            d2, d0, d1
    // 0x757b68: LoadField: d0 = r2->field_f
    //     0x757b68: ldur            d0, [x2, #0xf]
    // 0x757b6c: mov             v1.16b, v2.16b
    // 0x757b70: b               #0x757bcc
    // 0x757b74: ldur            x2, [fp, #-0x28]
    // 0x757b78: ldur            x0, [fp, #-0x20]
    // 0x757b7c: LoadField: d0 = r2->field_7
    //     0x757b7c: ldur            d0, [x2, #7]
    // 0x757b80: LoadField: d1 = r0->field_7
    //     0x757b80: ldur            d1, [x0, #7]
    // 0x757b84: fadd            d2, d0, d1
    // 0x757b88: LoadField: d0 = r2->field_f
    //     0x757b88: ldur            d0, [x2, #0xf]
    // 0x757b8c: LoadField: d1 = r1->field_7
    //     0x757b8c: ldur            d1, [x1, #7]
    // 0x757b90: fadd            d3, d0, d1
    // 0x757b94: mov             v1.16b, v2.16b
    // 0x757b98: mov             v0.16b, v3.16b
    // 0x757b9c: b               #0x757bcc
    // 0x757ba0: ldur            x2, [fp, #-0x28]
    // 0x757ba4: cmp             x3, #3
    // 0x757ba8: b.gt            #0x757bc4
    // 0x757bac: LoadField: d0 = r2->field_f
    //     0x757bac: ldur            d0, [x2, #0xf]
    // 0x757bb0: LoadField: d1 = r1->field_7
    //     0x757bb0: ldur            d1, [x1, #7]
    // 0x757bb4: fadd            d2, d0, d1
    // 0x757bb8: ldur            d1, [fp, #-0x40]
    // 0x757bbc: mov             v0.16b, v2.16b
    // 0x757bc0: b               #0x757bcc
    // 0x757bc4: d1 = 0.000000
    //     0x757bc4: eor             v1.16b, v1.16b, v1.16b
    // 0x757bc8: d0 = 0.000000
    //     0x757bc8: eor             v0.16b, v0.16b, v0.16b
    // 0x757bcc: stur            d1, [fp, #-0x40]
    // 0x757bd0: stur            d0, [fp, #-0x48]
    // 0x757bd4: r0 = Size()
    //     0x757bd4: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x757bd8: ldur            d0, [fp, #-0x40]
    // 0x757bdc: StoreField: r0->field_7 = d0
    //     0x757bdc: stur            d0, [x0, #7]
    // 0x757be0: ldur            d0, [fp, #-0x48]
    // 0x757be4: StoreField: r0->field_f = d0
    //     0x757be4: stur            d0, [x0, #0xf]
    // 0x757be8: ldur            x1, [fp, #-8]
    // 0x757bec: mov             x2, x0
    // 0x757bf0: r0 = constrain()
    //     0x757bf0: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x757bf4: stur            x0, [fp, #-8]
    // 0x757bf8: r0 = _Sizes()
    //     0x757bf8: bl              #0x7582ec  ; Allocate_SizesStub -> _Sizes (size=0x10)
    // 0x757bfc: ldur            x1, [fp, #-8]
    // 0x757c00: StoreField: r0->field_7 = r1
    //     0x757c00: stur            w1, [x0, #7]
    // 0x757c04: ldur            x1, [fp, #-0x28]
    // 0x757c08: StoreField: r0->field_b = r1
    //     0x757c08: stur            w1, [x0, #0xb]
    // 0x757c0c: LeaveFrame
    //     0x757c0c: mov             SP, fp
    //     0x757c10: ldp             fp, lr, [SP], #0x10
    // 0x757c14: ret
    //     0x757c14: ret             
    // 0x757c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x757c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x757c1c: b               #0x757424
    // 0x757c20: r0 = NullCastErrorSharedWithFPURegs()
    //     0x757c20: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x757c24: r0 = NullCastErrorSharedWithFPURegs()
    //     0x757c24: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x757c28: SaveReg d0
    //     0x757c28: str             q0, [SP, #-0x10]!
    // 0x757c2c: stp             x0, x1, [SP, #-0x10]!
    // 0x757c30: r0 = AllocateDouble()
    //     0x757c30: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757c34: mov             x2, x0
    // 0x757c38: ldp             x0, x1, [SP], #0x10
    // 0x757c3c: RestoreReg d0
    //     0x757c3c: ldr             q0, [SP], #0x10
    // 0x757c40: b               #0x7575b0
    // 0x757c44: SaveReg d0
    //     0x757c44: str             q0, [SP, #-0x10]!
    // 0x757c48: stp             x1, x2, [SP, #-0x10]!
    // 0x757c4c: SaveReg r0
    //     0x757c4c: str             x0, [SP, #-8]!
    // 0x757c50: r0 = AllocateDouble()
    //     0x757c50: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757c54: mov             x3, x0
    // 0x757c58: RestoreReg r0
    //     0x757c58: ldr             x0, [SP], #8
    // 0x757c5c: ldp             x1, x2, [SP], #0x10
    // 0x757c60: RestoreReg d0
    //     0x757c60: ldr             q0, [SP], #0x10
    // 0x757c64: b               #0x7575e8
    // 0x757c68: SaveReg d0
    //     0x757c68: str             q0, [SP, #-0x10]!
    // 0x757c6c: stp             x0, x1, [SP, #-0x10]!
    // 0x757c70: r0 = AllocateDouble()
    //     0x757c70: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757c74: mov             x2, x0
    // 0x757c78: ldp             x0, x1, [SP], #0x10
    // 0x757c7c: RestoreReg d0
    //     0x757c7c: ldr             q0, [SP], #0x10
    // 0x757c80: b               #0x757640
    // 0x757c84: stp             q0, q1, [SP, #-0x20]!
    // 0x757c88: SaveReg r1
    //     0x757c88: str             x1, [SP, #-8]!
    // 0x757c8c: r0 = AllocateDouble()
    //     0x757c8c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757c90: RestoreReg r1
    //     0x757c90: ldr             x1, [SP], #8
    // 0x757c94: ldp             q0, q1, [SP], #0x20
    // 0x757c98: b               #0x7576dc
    // 0x757c9c: stp             q0, q1, [SP, #-0x20]!
    // 0x757ca0: stp             x0, x1, [SP, #-0x10]!
    // 0x757ca4: r0 = AllocateDouble()
    //     0x757ca4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757ca8: mov             x2, x0
    // 0x757cac: ldp             x0, x1, [SP], #0x10
    // 0x757cb0: ldp             q0, q1, [SP], #0x20
    // 0x757cb4: b               #0x757714
    // 0x757cb8: SaveReg d0
    //     0x757cb8: str             q0, [SP, #-0x10]!
    // 0x757cbc: stp             x0, x1, [SP, #-0x10]!
    // 0x757cc0: r0 = AllocateDouble()
    //     0x757cc0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757cc4: mov             x2, x0
    // 0x757cc8: ldp             x0, x1, [SP], #0x10
    // 0x757ccc: RestoreReg d0
    //     0x757ccc: ldr             q0, [SP], #0x10
    // 0x757cd0: b               #0x75776c
    // 0x757cd4: stp             q2, q3, [SP, #-0x20]!
    // 0x757cd8: stp             q0, q1, [SP, #-0x20]!
    // 0x757cdc: stp             x0, x2, [SP, #-0x10]!
    // 0x757ce0: r0 = AllocateDouble()
    //     0x757ce0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757ce4: mov             x1, x0
    // 0x757ce8: ldp             x0, x2, [SP], #0x10
    // 0x757cec: ldp             q0, q1, [SP], #0x20
    // 0x757cf0: ldp             q2, q3, [SP], #0x20
    // 0x757cf4: b               #0x757814
    // 0x757cf8: stp             q1, q3, [SP, #-0x20]!
    // 0x757cfc: SaveReg d0
    //     0x757cfc: str             q0, [SP, #-0x10]!
    // 0x757d00: stp             x1, x2, [SP, #-0x10]!
    // 0x757d04: SaveReg r0
    //     0x757d04: str             x0, [SP, #-8]!
    // 0x757d08: r0 = AllocateDouble()
    //     0x757d08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757d0c: mov             x3, x0
    // 0x757d10: RestoreReg r0
    //     0x757d10: ldr             x0, [SP], #8
    // 0x757d14: ldp             x1, x2, [SP], #0x10
    // 0x757d18: RestoreReg d0
    //     0x757d18: ldr             q0, [SP], #0x10
    // 0x757d1c: ldp             q1, q3, [SP], #0x20
    // 0x757d20: b               #0x75783c
    // 0x757d24: stp             q1, q3, [SP, #-0x20]!
    // 0x757d28: stp             x2, x3, [SP, #-0x10]!
    // 0x757d2c: stp             x0, x1, [SP, #-0x10]!
    // 0x757d30: r0 = AllocateDouble()
    //     0x757d30: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757d34: mov             x4, x0
    // 0x757d38: ldp             x0, x1, [SP], #0x10
    // 0x757d3c: ldp             x2, x3, [SP], #0x10
    // 0x757d40: ldp             q1, q3, [SP], #0x20
    // 0x757d44: b               #0x757864
    // 0x757d48: SaveReg d3
    //     0x757d48: str             q3, [SP, #-0x10]!
    // 0x757d4c: stp             x3, x4, [SP, #-0x10]!
    // 0x757d50: stp             x1, x2, [SP, #-0x10]!
    // 0x757d54: SaveReg r0
    //     0x757d54: str             x0, [SP, #-8]!
    // 0x757d58: r0 = AllocateDouble()
    //     0x757d58: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757d5c: mov             x5, x0
    // 0x757d60: RestoreReg r0
    //     0x757d60: ldr             x0, [SP], #8
    // 0x757d64: ldp             x1, x2, [SP], #0x10
    // 0x757d68: ldp             x3, x4, [SP], #0x10
    // 0x757d6c: RestoreReg d3
    //     0x757d6c: ldr             q3, [SP], #0x10
    // 0x757d70: b               #0x75788c
    // 0x757d74: SaveReg d0
    //     0x757d74: str             q0, [SP, #-0x10]!
    // 0x757d78: SaveReg r0
    //     0x757d78: str             x0, [SP, #-8]!
    // 0x757d7c: r0 = AllocateDouble()
    //     0x757d7c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757d80: mov             x1, x0
    // 0x757d84: RestoreReg r0
    //     0x757d84: ldr             x0, [SP], #8
    // 0x757d88: RestoreReg d0
    //     0x757d88: ldr             q0, [SP], #0x10
    // 0x757d8c: b               #0x757958
    // 0x757d90: stp             q0, q1, [SP, #-0x20]!
    // 0x757d94: SaveReg r0
    //     0x757d94: str             x0, [SP, #-8]!
    // 0x757d98: r0 = AllocateDouble()
    //     0x757d98: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757d9c: mov             x1, x0
    // 0x757da0: RestoreReg r0
    //     0x757da0: ldr             x0, [SP], #8
    // 0x757da4: ldp             q0, q1, [SP], #0x20
    // 0x757da8: b               #0x7579b0
    // 0x757dac: SaveReg d0
    //     0x757dac: str             q0, [SP, #-0x10]!
    // 0x757db0: stp             x0, x1, [SP, #-0x10]!
    // 0x757db4: r0 = AllocateDouble()
    //     0x757db4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757db8: mov             x2, x0
    // 0x757dbc: ldp             x0, x1, [SP], #0x10
    // 0x757dc0: RestoreReg d0
    //     0x757dc0: ldr             q0, [SP], #0x10
    // 0x757dc4: b               #0x7579e4
    // 0x757dc8: SaveReg d0
    //     0x757dc8: str             q0, [SP, #-0x10]!
    // 0x757dcc: stp             x0, x1, [SP, #-0x10]!
    // 0x757dd0: r0 = AllocateDouble()
    //     0x757dd0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757dd4: mov             x2, x0
    // 0x757dd8: ldp             x0, x1, [SP], #0x10
    // 0x757ddc: RestoreReg d0
    //     0x757ddc: ldr             q0, [SP], #0x10
    // 0x757de0: b               #0x757a3c
    // 0x757de4: stp             q0, q1, [SP, #-0x20]!
    // 0x757de8: SaveReg r1
    //     0x757de8: str             x1, [SP, #-8]!
    // 0x757dec: r0 = AllocateDouble()
    //     0x757dec: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757df0: RestoreReg r1
    //     0x757df0: ldr             x1, [SP], #8
    // 0x757df4: ldp             q0, q1, [SP], #0x20
    // 0x757df8: b               #0x757a94
    // 0x757dfc: SaveReg d0
    //     0x757dfc: str             q0, [SP, #-0x10]!
    // 0x757e00: stp             x1, x2, [SP, #-0x10]!
    // 0x757e04: SaveReg r0
    //     0x757e04: str             x0, [SP, #-8]!
    // 0x757e08: r0 = AllocateDouble()
    //     0x757e08: bl              #0xec2254  ; AllocateDoubleStub
    // 0x757e0c: mov             x3, x0
    // 0x757e10: RestoreReg r0
    //     0x757e10: ldr             x0, [SP], #8
    // 0x757e14: ldp             x1, x2, [SP], #0x10
    // 0x757e18: RestoreReg d0
    //     0x757e18: ldr             q0, [SP], #0x10
    // 0x757e1c: b               #0x757ad4
  }
  _ _calculateUsedMargins(/* No info */) {
    // ** addr: 0x757e20, size: 0x4b4
    // 0x757e20: EnterFrame
    //     0x757e20: stp             fp, lr, [SP, #-0x10]!
    //     0x757e24: mov             fp, SP
    // 0x757e28: AllocStack(0x58)
    //     0x757e28: sub             SP, SP, #0x58
    // 0x757e2c: SetupParameters(RenderCSSBox this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */, dynamic _ /* r3 => r3, fp-0x30 */)
    //     0x757e2c: stur            x1, [fp, #-0x20]
    //     0x757e30: stur            x2, [fp, #-0x28]
    //     0x757e34: stur            x3, [fp, #-0x30]
    // 0x757e38: LoadField: r0 = r1->field_73
    //     0x757e38: ldur            w0, [x1, #0x73]
    // 0x757e3c: DecompressPointer r0
    //     0x757e3c: add             x0, x0, HEAP, lsl #32
    // 0x757e40: stur            x0, [fp, #-0x18]
    // 0x757e44: LoadField: r4 = r0->field_7
    //     0x757e44: ldur            w4, [x0, #7]
    // 0x757e48: DecompressPointer r4
    //     0x757e48: add             x4, x4, HEAP, lsl #32
    // 0x757e4c: cmp             w4, NULL
    // 0x757e50: b.eq            #0x7582cc
    // 0x757e54: LoadField: r5 = r0->field_b
    //     0x757e54: ldur            w5, [x0, #0xb]
    // 0x757e58: DecompressPointer r5
    //     0x757e58: add             x5, x5, HEAP, lsl #32
    // 0x757e5c: cmp             w5, NULL
    // 0x757e60: b.eq            #0x7582d0
    // 0x757e64: LoadField: r6 = r1->field_6b
    //     0x757e64: ldur            w6, [x1, #0x6b]
    // 0x757e68: DecompressPointer r6
    //     0x757e68: add             x6, x6, HEAP, lsl #32
    // 0x757e6c: LoadField: r7 = r6->field_f
    //     0x757e6c: ldur            w7, [x6, #0xf]
    // 0x757e70: DecompressPointer r7
    //     0x757e70: add             x7, x7, HEAP, lsl #32
    // 0x757e74: r16 = Instance_Unit
    //     0x757e74: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x757e78: ldr             x16, [x16, #0x950]
    // 0x757e7c: cmp             w7, w16
    // 0x757e80: r16 = true
    //     0x757e80: add             x16, NULL, #0x20  ; true
    // 0x757e84: r17 = false
    //     0x757e84: add             x17, NULL, #0x30  ; false
    // 0x757e88: csel            x6, x16, x17, eq
    // 0x757e8c: LoadField: r7 = r4->field_f
    //     0x757e8c: ldur            w7, [x4, #0xf]
    // 0x757e90: DecompressPointer r7
    //     0x757e90: add             x7, x7, HEAP, lsl #32
    // 0x757e94: r16 = Instance_Unit
    //     0x757e94: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x757e98: ldr             x16, [x16, #0x950]
    // 0x757e9c: cmp             w7, w16
    // 0x757ea0: r16 = true
    //     0x757ea0: add             x16, NULL, #0x20  ; true
    // 0x757ea4: r17 = false
    //     0x757ea4: add             x17, NULL, #0x30  ; false
    // 0x757ea8: csel            x8, x16, x17, eq
    // 0x757eac: LoadField: r7 = r5->field_f
    //     0x757eac: ldur            w7, [x5, #0xf]
    // 0x757eb0: DecompressPointer r7
    //     0x757eb0: add             x7, x7, HEAP, lsl #32
    // 0x757eb4: r16 = Instance_Unit
    //     0x757eb4: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x757eb8: ldr             x16, [x16, #0x950]
    // 0x757ebc: cmp             w7, w16
    // 0x757ec0: r16 = true
    //     0x757ec0: add             x16, NULL, #0x20  ; true
    // 0x757ec4: r17 = false
    //     0x757ec4: add             x17, NULL, #0x30  ; false
    // 0x757ec8: csel            x9, x16, x17, eq
    // 0x757ecc: LoadField: r7 = r1->field_67
    //     0x757ecc: ldur            w7, [x1, #0x67]
    // 0x757ed0: DecompressPointer r7
    //     0x757ed0: add             x7, x7, HEAP, lsl #32
    // 0x757ed4: r16 = Instance_Display
    //     0x757ed4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b68] Obj!Display@e33781
    //     0x757ed8: ldr             x16, [x16, #0xb68]
    // 0x757edc: cmp             w7, w16
    // 0x757ee0: b.ne            #0x758270
    // 0x757ee4: LoadField: r7 = r1->field_7b
    //     0x757ee4: ldur            w7, [x1, #0x7b]
    // 0x757ee8: DecompressPointer r7
    //     0x757ee8: add             x7, x7, HEAP, lsl #32
    // 0x757eec: stur            x7, [fp, #-0x10]
    // 0x757ef0: tbnz            w7, #4, #0x757ef8
    // 0x757ef4: r6 = false
    //     0x757ef4: add             x6, NULL, #0x30  ; false
    // 0x757ef8: stur            x6, [fp, #-8]
    // 0x757efc: tbz             w6, #4, #0x757f88
    // 0x757f00: LoadField: d0 = r2->field_7
    //     0x757f00: ldur            d0, [x2, #7]
    // 0x757f04: LoadField: d1 = r4->field_7
    //     0x757f04: ldur            d1, [x4, #7]
    // 0x757f08: fadd            d2, d0, d1
    // 0x757f0c: LoadField: d0 = r5->field_7
    //     0x757f0c: ldur            d0, [x5, #7]
    // 0x757f10: fadd            d1, d2, d0
    // 0x757f14: LoadField: d0 = r3->field_7
    //     0x757f14: ldur            d0, [x3, #7]
    // 0x757f18: fcmp            d1, d0
    // 0x757f1c: b.le            #0x757f60
    // 0x757f20: r0 = Margin()
    //     0x757f20: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x757f24: stur            x0, [fp, #-0x38]
    // 0x757f28: StoreField: r0->field_7 = rZR
    //     0x757f28: stur            xzr, [x0, #7]
    // 0x757f2c: r1 = Instance_Unit
    //     0x757f2c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x757f30: ldr             x1, [x1, #0xa98]
    // 0x757f34: StoreField: r0->field_f = r1
    //     0x757f34: stur            w1, [x0, #0xf]
    // 0x757f38: r0 = Margin()
    //     0x757f38: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x757f3c: StoreField: r0->field_7 = rZR
    //     0x757f3c: stur            xzr, [x0, #7]
    // 0x757f40: r1 = Instance_Unit
    //     0x757f40: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x757f44: ldr             x1, [x1, #0xa98]
    // 0x757f48: StoreField: r0->field_f = r1
    //     0x757f48: stur            w1, [x0, #0xf]
    // 0x757f4c: ldur            x4, [fp, #-0x38]
    // 0x757f50: mov             x3, x0
    // 0x757f54: r2 = false
    //     0x757f54: add             x2, NULL, #0x30  ; false
    // 0x757f58: r0 = false
    //     0x757f58: add             x0, NULL, #0x30  ; false
    // 0x757f5c: b               #0x757f74
    // 0x757f60: r1 = Instance_Unit
    //     0x757f60: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x757f64: ldr             x1, [x1, #0xa98]
    // 0x757f68: mov             x3, x5
    // 0x757f6c: mov             x2, x8
    // 0x757f70: mov             x0, x9
    // 0x757f74: mov             x5, x4
    // 0x757f78: mov             x4, x3
    // 0x757f7c: mov             x3, x2
    // 0x757f80: mov             x2, x0
    // 0x757f84: b               #0x757fa4
    // 0x757f88: r1 = Instance_Unit
    //     0x757f88: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x757f8c: ldr             x1, [x1, #0xa98]
    // 0x757f90: mov             x16, x5
    // 0x757f94: mov             x5, x4
    // 0x757f98: mov             x4, x16
    // 0x757f9c: mov             x3, x8
    // 0x757fa0: mov             x2, x9
    // 0x757fa4: ldur            x0, [fp, #-8]
    // 0x757fa8: stur            x4, [fp, #-0x38]
    // 0x757fac: stur            x3, [fp, #-0x40]
    // 0x757fb0: stur            x2, [fp, #-0x48]
    // 0x757fb4: stur            x5, [fp, #-0x50]
    // 0x757fb8: tbz             w0, #4, #0x758070
    // 0x757fbc: tbz             w3, #4, #0x758070
    // 0x757fc0: tbz             w2, #4, #0x758070
    // 0x757fc4: ldur            x6, [fp, #-0x10]
    // 0x757fc8: tbz             w6, #4, #0x758070
    // 0x757fcc: ldur            x6, [fp, #-0x20]
    // 0x757fd0: LoadField: r7 = r6->field_77
    //     0x757fd0: ldur            w7, [x6, #0x77]
    // 0x757fd4: DecompressPointer r7
    //     0x757fd4: add             x7, x7, HEAP, lsl #32
    // 0x757fd8: LoadField: r6 = r7->field_7
    //     0x757fd8: ldur            x6, [x7, #7]
    // 0x757fdc: cmp             x6, #0
    // 0x757fe0: b.gt            #0x758028
    // 0x757fe4: ldur            x6, [fp, #-0x28]
    // 0x757fe8: ldur            x5, [fp, #-0x30]
    // 0x757fec: LoadField: d0 = r5->field_7
    //     0x757fec: ldur            d0, [x5, #7]
    // 0x757ff0: LoadField: d1 = r6->field_7
    //     0x757ff0: ldur            d1, [x6, #7]
    // 0x757ff4: fsub            d2, d0, d1
    // 0x757ff8: LoadField: d0 = r4->field_7
    //     0x757ff8: ldur            d0, [x4, #7]
    // 0x757ffc: fsub            d1, d2, d0
    // 0x758000: stur            d1, [fp, #-0x58]
    // 0x758004: r0 = Margin()
    //     0x758004: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x758008: ldur            d0, [fp, #-0x58]
    // 0x75800c: StoreField: r0->field_7 = d0
    //     0x75800c: stur            d0, [x0, #7]
    // 0x758010: r1 = Instance_Unit
    //     0x758010: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x758014: ldr             x1, [x1, #0xa98]
    // 0x758018: StoreField: r0->field_f = r1
    //     0x758018: stur            w1, [x0, #0xf]
    // 0x75801c: mov             x2, x0
    // 0x758020: ldur            x0, [fp, #-0x38]
    // 0x758024: b               #0x758064
    // 0x758028: ldur            x2, [fp, #-0x28]
    // 0x75802c: ldur            x0, [fp, #-0x30]
    // 0x758030: LoadField: d0 = r0->field_7
    //     0x758030: ldur            d0, [x0, #7]
    // 0x758034: LoadField: d1 = r2->field_7
    //     0x758034: ldur            d1, [x2, #7]
    // 0x758038: fsub            d2, d0, d1
    // 0x75803c: LoadField: d0 = r5->field_7
    //     0x75803c: ldur            d0, [x5, #7]
    // 0x758040: fsub            d1, d2, d0
    // 0x758044: stur            d1, [fp, #-0x58]
    // 0x758048: r0 = Margin()
    //     0x758048: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x75804c: ldur            d0, [fp, #-0x58]
    // 0x758050: StoreField: r0->field_7 = d0
    //     0x758050: stur            d0, [x0, #7]
    // 0x758054: r1 = Instance_Unit
    //     0x758054: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x758058: ldr             x1, [x1, #0xa98]
    // 0x75805c: StoreField: r0->field_f = r1
    //     0x75805c: stur            w1, [x0, #0xf]
    // 0x758060: ldur            x2, [fp, #-0x50]
    // 0x758064: mov             x3, x2
    // 0x758068: mov             x2, x0
    // 0x75806c: b               #0x758078
    // 0x758070: ldur            x3, [fp, #-0x50]
    // 0x758074: ldur            x2, [fp, #-0x38]
    // 0x758078: ldur            x0, [fp, #-8]
    // 0x75807c: stur            x2, [fp, #-0x10]
    // 0x758080: stur            x3, [fp, #-0x20]
    // 0x758084: tbnz            w0, #4, #0x7580bc
    // 0x758088: ldur            x4, [fp, #-0x40]
    // 0x75808c: tbz             w4, #4, #0x7580b4
    // 0x758090: ldur            x5, [fp, #-0x48]
    // 0x758094: tbz             w5, #4, #0x7580c4
    // 0x758098: mov             x0, x5
    // 0x75809c: mov             x5, x3
    // 0x7580a0: mov             x16, x4
    // 0x7580a4: mov             x4, x2
    // 0x7580a8: mov             x2, x16
    // 0x7580ac: r3 = false
    //     0x7580ac: add             x3, NULL, #0x30  ; false
    // 0x7580b0: b               #0x7581a0
    // 0x7580b4: ldur            x5, [fp, #-0x48]
    // 0x7580b8: b               #0x7580c4
    // 0x7580bc: ldur            x4, [fp, #-0x40]
    // 0x7580c0: ldur            x5, [fp, #-0x48]
    // 0x7580c4: tbz             w0, #4, #0x75811c
    // 0x7580c8: tbnz            w4, #4, #0x75811c
    // 0x7580cc: tbz             w5, #4, #0x75811c
    // 0x7580d0: ldur            x4, [fp, #-0x28]
    // 0x7580d4: ldur            x3, [fp, #-0x30]
    // 0x7580d8: LoadField: d0 = r3->field_7
    //     0x7580d8: ldur            d0, [x3, #7]
    // 0x7580dc: LoadField: d1 = r4->field_7
    //     0x7580dc: ldur            d1, [x4, #7]
    // 0x7580e0: fsub            d2, d0, d1
    // 0x7580e4: LoadField: d0 = r2->field_7
    //     0x7580e4: ldur            d0, [x2, #7]
    // 0x7580e8: fsub            d1, d2, d0
    // 0x7580ec: stur            d1, [fp, #-0x58]
    // 0x7580f0: r0 = Margin()
    //     0x7580f0: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x7580f4: ldur            d0, [fp, #-0x58]
    // 0x7580f8: StoreField: r0->field_7 = d0
    //     0x7580f8: stur            d0, [x0, #7]
    // 0x7580fc: r1 = Instance_Unit
    //     0x7580fc: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x758100: ldr             x1, [x1, #0xa98]
    // 0x758104: StoreField: r0->field_f = r1
    //     0x758104: stur            w1, [x0, #0xf]
    // 0x758108: mov             x4, x0
    // 0x75810c: ldur            x3, [fp, #-0x10]
    // 0x758110: ldur            x0, [fp, #-0x48]
    // 0x758114: r2 = false
    //     0x758114: add             x2, NULL, #0x30  ; false
    // 0x758118: b               #0x758194
    // 0x75811c: ldur            x0, [fp, #-8]
    // 0x758120: tbz             w0, #4, #0x75817c
    // 0x758124: tbz             w4, #4, #0x758174
    // 0x758128: ldur            x2, [fp, #-0x48]
    // 0x75812c: tbnz            w2, #4, #0x758180
    // 0x758130: ldur            x5, [fp, #-0x28]
    // 0x758134: ldur            x2, [fp, #-0x30]
    // 0x758138: LoadField: d0 = r2->field_7
    //     0x758138: ldur            d0, [x2, #7]
    // 0x75813c: LoadField: d1 = r5->field_7
    //     0x75813c: ldur            d1, [x5, #7]
    // 0x758140: fsub            d2, d0, d1
    // 0x758144: LoadField: d0 = r3->field_7
    //     0x758144: ldur            d0, [x3, #7]
    // 0x758148: fsub            d1, d2, d0
    // 0x75814c: stur            d1, [fp, #-0x58]
    // 0x758150: r0 = Margin()
    //     0x758150: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x758154: ldur            d0, [fp, #-0x58]
    // 0x758158: StoreField: r0->field_7 = d0
    //     0x758158: stur            d0, [x0, #7]
    // 0x75815c: r1 = Instance_Unit
    //     0x75815c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x758160: ldr             x1, [x1, #0xa98]
    // 0x758164: StoreField: r0->field_f = r1
    //     0x758164: stur            w1, [x0, #0xf]
    // 0x758168: mov             x2, x0
    // 0x75816c: r0 = false
    //     0x75816c: add             x0, NULL, #0x30  ; false
    // 0x758170: b               #0x758188
    // 0x758174: ldur            x2, [fp, #-0x48]
    // 0x758178: b               #0x758180
    // 0x75817c: ldur            x2, [fp, #-0x48]
    // 0x758180: mov             x0, x2
    // 0x758184: ldur            x2, [fp, #-0x10]
    // 0x758188: ldur            x4, [fp, #-0x20]
    // 0x75818c: mov             x3, x2
    // 0x758190: ldur            x2, [fp, #-0x40]
    // 0x758194: mov             x5, x4
    // 0x758198: mov             x4, x3
    // 0x75819c: ldur            x3, [fp, #-8]
    // 0x7581a0: stur            x4, [fp, #-8]
    // 0x7581a4: stur            x0, [fp, #-0x10]
    // 0x7581a8: tbnz            w3, #4, #0x75820c
    // 0x7581ac: tbnz            w2, #4, #0x7581cc
    // 0x7581b0: r0 = Margin()
    //     0x7581b0: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x7581b4: StoreField: r0->field_7 = rZR
    //     0x7581b4: stur            xzr, [x0, #7]
    // 0x7581b8: r1 = Instance_Unit
    //     0x7581b8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x7581bc: ldr             x1, [x1, #0xa98]
    // 0x7581c0: StoreField: r0->field_f = r1
    //     0x7581c0: stur            w1, [x0, #0xf]
    // 0x7581c4: mov             x2, x0
    // 0x7581c8: b               #0x7581d0
    // 0x7581cc: mov             x2, x5
    // 0x7581d0: ldur            x0, [fp, #-0x10]
    // 0x7581d4: stur            x2, [fp, #-0x20]
    // 0x7581d8: tbnz            w0, #4, #0x7581f4
    // 0x7581dc: r0 = Margin()
    //     0x7581dc: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x7581e0: StoreField: r0->field_7 = rZR
    //     0x7581e0: stur            xzr, [x0, #7]
    // 0x7581e4: r1 = Instance_Unit
    //     0x7581e4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x7581e8: ldr             x1, [x1, #0xa98]
    // 0x7581ec: StoreField: r0->field_f = r1
    //     0x7581ec: stur            w1, [x0, #0xf]
    // 0x7581f0: b               #0x7581f8
    // 0x7581f4: ldur            x0, [fp, #-8]
    // 0x7581f8: ldur            x4, [fp, #-0x20]
    // 0x7581fc: mov             x3, x0
    // 0x758200: r2 = false
    //     0x758200: add             x2, NULL, #0x30  ; false
    // 0x758204: r0 = false
    //     0x758204: add             x0, NULL, #0x30  ; false
    // 0x758208: b               #0x758214
    // 0x75820c: mov             x4, x5
    // 0x758210: ldur            x3, [fp, #-8]
    // 0x758214: tbnz            w2, #4, #0x75825c
    // 0x758218: tbnz            w0, #4, #0x75825c
    // 0x75821c: ldur            x2, [fp, #-0x28]
    // 0x758220: ldur            x0, [fp, #-0x30]
    // 0x758224: d0 = 2.000000
    //     0x758224: fmov            d0, #2.00000000
    // 0x758228: LoadField: d1 = r0->field_7
    //     0x758228: ldur            d1, [x0, #7]
    // 0x75822c: LoadField: d2 = r2->field_7
    //     0x75822c: ldur            d2, [x2, #7]
    // 0x758230: fsub            d3, d1, d2
    // 0x758234: fdiv            d1, d3, d0
    // 0x758238: stur            d1, [fp, #-0x58]
    // 0x75823c: r0 = Margin()
    //     0x75823c: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x758240: ldur            d0, [fp, #-0x58]
    // 0x758244: StoreField: r0->field_7 = d0
    //     0x758244: stur            d0, [x0, #7]
    // 0x758248: r1 = Instance_Unit
    //     0x758248: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x75824c: ldr             x1, [x1, #0xa98]
    // 0x758250: StoreField: r0->field_f = r1
    //     0x758250: stur            w1, [x0, #0xf]
    // 0x758254: mov             x1, x0
    // 0x758258: b               #0x758264
    // 0x75825c: mov             x1, x4
    // 0x758260: mov             x0, x3
    // 0x758264: mov             x2, x1
    // 0x758268: mov             x1, x0
    // 0x75826c: b               #0x758278
    // 0x758270: mov             x2, x4
    // 0x758274: mov             x1, x5
    // 0x758278: ldur            x0, [fp, #-0x18]
    // 0x75827c: stur            x2, [fp, #-0x20]
    // 0x758280: stur            x1, [fp, #-0x28]
    // 0x758284: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x758284: ldur            w3, [x0, #0x17]
    // 0x758288: DecompressPointer r3
    //     0x758288: add             x3, x3, HEAP, lsl #32
    // 0x75828c: stur            x3, [fp, #-0x10]
    // 0x758290: LoadField: r4 = r0->field_1b
    //     0x758290: ldur            w4, [x0, #0x1b]
    // 0x758294: DecompressPointer r4
    //     0x758294: add             x4, x4, HEAP, lsl #32
    // 0x758298: stur            x4, [fp, #-8]
    // 0x75829c: r0 = Margins()
    //     0x75829c: bl              #0x7582d4  ; AllocateMarginsStub -> Margins (size=0x28)
    // 0x7582a0: ldur            x1, [fp, #-0x20]
    // 0x7582a4: StoreField: r0->field_7 = r1
    //     0x7582a4: stur            w1, [x0, #7]
    // 0x7582a8: ldur            x1, [fp, #-0x28]
    // 0x7582ac: StoreField: r0->field_b = r1
    //     0x7582ac: stur            w1, [x0, #0xb]
    // 0x7582b0: ldur            x1, [fp, #-0x10]
    // 0x7582b4: ArrayStore: r0[0] = r1  ; List_4
    //     0x7582b4: stur            w1, [x0, #0x17]
    // 0x7582b8: ldur            x1, [fp, #-8]
    // 0x7582bc: StoreField: r0->field_1b = r1
    //     0x7582bc: stur            w1, [x0, #0x1b]
    // 0x7582c0: LeaveFrame
    //     0x7582c0: mov             SP, fp
    //     0x7582c4: ldp             fp, lr, [SP], #0x10
    // 0x7582c8: ret
    //     0x7582c8: ret             
    // 0x7582cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7582cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7582d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7582d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x77a078, size: 0x4b8
    // 0x77a078: EnterFrame
    //     0x77a078: stp             fp, lr, [SP, #-0x10]!
    //     0x77a07c: mov             fp, SP
    // 0x77a080: AllocStack(0x48)
    //     0x77a080: sub             SP, SP, #0x48
    // 0x77a084: SetupParameters(RenderCSSBox this /* r1 => r3, fp-0x10 */)
    //     0x77a084: mov             x3, x1
    //     0x77a088: stur            x1, [fp, #-0x10]
    // 0x77a08c: CheckStackOverflow
    //     0x77a08c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77a090: cmp             SP, x16
    //     0x77a094: b.ls            #0x77a4f0
    // 0x77a098: LoadField: r4 = r3->field_27
    //     0x77a098: ldur            w4, [x3, #0x27]
    // 0x77a09c: DecompressPointer r4
    //     0x77a09c: add             x4, x4, HEAP, lsl #32
    // 0x77a0a0: stur            x4, [fp, #-8]
    // 0x77a0a4: cmp             w4, NULL
    // 0x77a0a8: b.eq            #0x77a4d4
    // 0x77a0ac: mov             x0, x4
    // 0x77a0b0: r2 = Null
    //     0x77a0b0: mov             x2, NULL
    // 0x77a0b4: r1 = Null
    //     0x77a0b4: mov             x1, NULL
    // 0x77a0b8: r4 = LoadClassIdInstr(r0)
    //     0x77a0b8: ldur            x4, [x0, #-1]
    //     0x77a0bc: ubfx            x4, x4, #0xc, #0x14
    // 0x77a0c0: sub             x4, x4, #0xc83
    // 0x77a0c4: cmp             x4, #1
    // 0x77a0c8: b.ls            #0x77a0dc
    // 0x77a0cc: r8 = BoxConstraints
    //     0x77a0cc: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x77a0d0: r3 = Null
    //     0x77a0d0: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d610] Null
    //     0x77a0d4: ldr             x3, [x3, #0x610]
    // 0x77a0d8: r0 = BoxConstraints()
    //     0x77a0d8: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x77a0dc: ldur            x1, [fp, #-0x10]
    // 0x77a0e0: ldur            x2, [fp, #-8]
    // 0x77a0e4: r3 = Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static.
    //     0x77a0e4: add             x3, PP, #0x44, lsl #12  ; [pp+0x44b28] Closure: (RenderBox, BoxConstraints) => Size from Function 'layoutChild': static. (0x7e54fb1673f8)
    //     0x77a0e8: ldr             x3, [x3, #0xb28]
    // 0x77a0ec: r0 = _computeSize()
    //     0x77a0ec: bl              #0x7573f8  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::_computeSize
    // 0x77a0f0: mov             x3, x0
    // 0x77a0f4: stur            x3, [fp, #-0x28]
    // 0x77a0f8: LoadField: r0 = r3->field_7
    //     0x77a0f8: ldur            w0, [x3, #7]
    // 0x77a0fc: DecompressPointer r0
    //     0x77a0fc: add             x0, x0, HEAP, lsl #32
    // 0x77a100: ldur            x4, [fp, #-0x10]
    // 0x77a104: StoreField: r4->field_53 = r0
    //     0x77a104: stur            w0, [x4, #0x53]
    //     0x77a108: ldurb           w16, [x4, #-1]
    //     0x77a10c: ldurb           w17, [x0, #-1]
    //     0x77a110: and             x16, x17, x16, lsr #2
    //     0x77a114: tst             x16, HEAP, lsr #32
    //     0x77a118: b.eq            #0x77a120
    //     0x77a11c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x77a120: LoadField: r5 = r4->field_5f
    //     0x77a120: ldur            w5, [x4, #0x5f]
    // 0x77a124: DecompressPointer r5
    //     0x77a124: add             x5, x5, HEAP, lsl #32
    // 0x77a128: stur            x5, [fp, #-0x20]
    // 0x77a12c: cmp             w5, NULL
    // 0x77a130: b.eq            #0x77a4f8
    // 0x77a134: LoadField: r6 = r5->field_7
    //     0x77a134: ldur            w6, [x5, #7]
    // 0x77a138: DecompressPointer r6
    //     0x77a138: add             x6, x6, HEAP, lsl #32
    // 0x77a13c: stur            x6, [fp, #-0x18]
    // 0x77a140: cmp             w6, NULL
    // 0x77a144: b.eq            #0x77a4fc
    // 0x77a148: mov             x0, x6
    // 0x77a14c: r2 = Null
    //     0x77a14c: mov             x2, NULL
    // 0x77a150: r1 = Null
    //     0x77a150: mov             x1, NULL
    // 0x77a154: r4 = LoadClassIdInstr(r0)
    //     0x77a154: ldur            x4, [x0, #-1]
    //     0x77a158: ubfx            x4, x4, #0xc, #0x14
    // 0x77a15c: cmp             x4, #0xc76
    // 0x77a160: b.eq            #0x77a178
    // 0x77a164: r8 = CSSBoxParentData
    //     0x77a164: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x77a168: ldr             x8, [x8, #0x3d8]
    // 0x77a16c: r3 = Null
    //     0x77a16c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d620] Null
    //     0x77a170: ldr             x3, [x3, #0x620]
    // 0x77a174: r0 = DefaultTypeTest()
    //     0x77a174: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77a178: ldur            x0, [fp, #-0x28]
    // 0x77a17c: LoadField: r2 = r0->field_b
    //     0x77a17c: ldur            w2, [x0, #0xb]
    // 0x77a180: DecompressPointer r2
    //     0x77a180: add             x2, x2, HEAP, lsl #32
    // 0x77a184: ldur            x1, [fp, #-8]
    // 0x77a188: stur            x2, [fp, #-0x30]
    // 0x77a18c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x77a18c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x77a190: r0 = constrainWidth()
    //     0x77a190: bl              #0x6cea58  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainWidth
    // 0x77a194: ldur            x1, [fp, #-8]
    // 0x77a198: stur            d0, [fp, #-0x38]
    // 0x77a19c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x77a19c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x77a1a0: r0 = constrainHeight()
    //     0x77a1a0: bl              #0x6ce9e4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrainHeight
    // 0x77a1a4: stur            d0, [fp, #-0x40]
    // 0x77a1a8: r0 = Size()
    //     0x77a1a8: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x77a1ac: ldur            d0, [fp, #-0x38]
    // 0x77a1b0: StoreField: r0->field_7 = d0
    //     0x77a1b0: stur            d0, [x0, #7]
    // 0x77a1b4: ldur            d0, [fp, #-0x40]
    // 0x77a1b8: StoreField: r0->field_f = d0
    //     0x77a1b8: stur            d0, [x0, #0xf]
    // 0x77a1bc: ldur            x1, [fp, #-0x10]
    // 0x77a1c0: ldur            x2, [fp, #-0x30]
    // 0x77a1c4: mov             x3, x0
    // 0x77a1c8: r0 = _calculateUsedMargins()
    //     0x77a1c8: bl              #0x757e20  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::_calculateUsedMargins
    // 0x77a1cc: LoadField: r1 = r0->field_7
    //     0x77a1cc: ldur            w1, [x0, #7]
    // 0x77a1d0: DecompressPointer r1
    //     0x77a1d0: add             x1, x1, HEAP, lsl #32
    // 0x77a1d4: cmp             w1, NULL
    // 0x77a1d8: b.ne            #0x77a1e4
    // 0x77a1dc: r1 = Null
    //     0x77a1dc: mov             x1, NULL
    // 0x77a1e0: b               #0x77a210
    // 0x77a1e4: LoadField: d0 = r1->field_7
    //     0x77a1e4: ldur            d0, [x1, #7]
    // 0x77a1e8: r1 = inline_Allocate_Double()
    //     0x77a1e8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x77a1ec: add             x1, x1, #0x10
    //     0x77a1f0: cmp             x2, x1
    //     0x77a1f4: b.ls            #0x77a500
    //     0x77a1f8: str             x1, [THR, #0x50]  ; THR::top
    //     0x77a1fc: sub             x1, x1, #0xf
    //     0x77a200: movz            x2, #0xe15c
    //     0x77a204: movk            x2, #0x3, lsl #16
    //     0x77a208: stur            x2, [x1, #-1]
    // 0x77a20c: StoreField: r1->field_7 = d0
    //     0x77a20c: stur            d0, [x1, #7]
    // 0x77a210: cmp             w1, NULL
    // 0x77a214: b.ne            #0x77a220
    // 0x77a218: d0 = 0.000000
    //     0x77a218: eor             v0.16b, v0.16b, v0.16b
    // 0x77a21c: b               #0x77a224
    // 0x77a220: LoadField: d0 = r1->field_7
    //     0x77a220: ldur            d0, [x1, #7]
    // 0x77a224: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x77a224: ldur            w1, [x0, #0x17]
    // 0x77a228: DecompressPointer r1
    //     0x77a228: add             x1, x1, HEAP, lsl #32
    // 0x77a22c: cmp             w1, NULL
    // 0x77a230: b.ne            #0x77a23c
    // 0x77a234: r0 = Null
    //     0x77a234: mov             x0, NULL
    // 0x77a238: b               #0x77a268
    // 0x77a23c: LoadField: d1 = r1->field_7
    //     0x77a23c: ldur            d1, [x1, #7]
    // 0x77a240: r0 = inline_Allocate_Double()
    //     0x77a240: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x77a244: add             x0, x0, #0x10
    //     0x77a248: cmp             x1, x0
    //     0x77a24c: b.ls            #0x77a51c
    //     0x77a250: str             x0, [THR, #0x50]  ; THR::top
    //     0x77a254: sub             x0, x0, #0xf
    //     0x77a258: movz            x1, #0xe15c
    //     0x77a25c: movk            x1, #0x3, lsl #16
    //     0x77a260: stur            x1, [x0, #-1]
    // 0x77a264: StoreField: r0->field_7 = d1
    //     0x77a264: stur            d1, [x0, #7]
    // 0x77a268: cmp             w0, NULL
    // 0x77a26c: b.ne            #0x77a278
    // 0x77a270: d1 = 0.000000
    //     0x77a270: eor             v1.16b, v1.16b, v1.16b
    // 0x77a274: b               #0x77a27c
    // 0x77a278: LoadField: d1 = r0->field_7
    //     0x77a278: ldur            d1, [x0, #7]
    // 0x77a27c: ldur            x0, [fp, #-0x10]
    // 0x77a280: LoadField: r1 = r0->field_67
    //     0x77a280: ldur            w1, [x0, #0x67]
    // 0x77a284: DecompressPointer r1
    //     0x77a284: add             x1, x1, HEAP, lsl #32
    // 0x77a288: LoadField: r2 = r1->field_7
    //     0x77a288: ldur            x2, [x1, #7]
    // 0x77a28c: cmp             x2, #2
    // 0x77a290: b.gt            #0x77a2d0
    // 0x77a294: cmp             x2, #1
    // 0x77a298: b.gt            #0x77a2c0
    // 0x77a29c: cmp             x2, #0
    // 0x77a2a0: b.gt            #0x77a2b4
    // 0x77a2a4: mov             v31.16b, v1.16b
    // 0x77a2a8: mov             v1.16b, v0.16b
    // 0x77a2ac: mov             v0.16b, v31.16b
    // 0x77a2b0: b               #0x77a2f0
    // 0x77a2b4: mov             v1.16b, v0.16b
    // 0x77a2b8: d0 = 0.000000
    //     0x77a2b8: eor             v0.16b, v0.16b, v0.16b
    // 0x77a2bc: b               #0x77a2f0
    // 0x77a2c0: mov             v31.16b, v1.16b
    // 0x77a2c4: mov             v1.16b, v0.16b
    // 0x77a2c8: mov             v0.16b, v31.16b
    // 0x77a2cc: b               #0x77a2f0
    // 0x77a2d0: cmp             x2, #3
    // 0x77a2d4: b.gt            #0x77a2e8
    // 0x77a2d8: mov             v31.16b, v1.16b
    // 0x77a2dc: mov             v1.16b, v0.16b
    // 0x77a2e0: mov             v0.16b, v31.16b
    // 0x77a2e4: b               #0x77a2f0
    // 0x77a2e8: d1 = 0.000000
    //     0x77a2e8: eor             v1.16b, v1.16b, v1.16b
    // 0x77a2ec: d0 = 0.000000
    //     0x77a2ec: eor             v0.16b, v0.16b, v0.16b
    // 0x77a2f0: ldur            x1, [fp, #-0x18]
    // 0x77a2f4: stur            d1, [fp, #-0x38]
    // 0x77a2f8: stur            d0, [fp, #-0x40]
    // 0x77a2fc: r0 = Offset()
    //     0x77a2fc: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x77a300: ldur            d0, [fp, #-0x38]
    // 0x77a304: StoreField: r0->field_7 = d0
    //     0x77a304: stur            d0, [x0, #7]
    // 0x77a308: ldur            d0, [fp, #-0x40]
    // 0x77a30c: StoreField: r0->field_f = d0
    //     0x77a30c: stur            d0, [x0, #0xf]
    // 0x77a310: ldur            x1, [fp, #-0x18]
    // 0x77a314: StoreField: r1->field_7 = r0
    //     0x77a314: stur            w0, [x1, #7]
    //     0x77a318: ldurb           w16, [x1, #-1]
    //     0x77a31c: ldurb           w17, [x0, #-1]
    //     0x77a320: and             x16, x17, x16, lsr #2
    //     0x77a324: tst             x16, HEAP, lsr #32
    //     0x77a328: b.eq            #0x77a330
    //     0x77a32c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x77a330: LoadField: r3 = r1->field_13
    //     0x77a330: ldur            w3, [x1, #0x13]
    // 0x77a334: DecompressPointer r3
    //     0x77a334: add             x3, x3, HEAP, lsl #32
    // 0x77a338: stur            x3, [fp, #-0x28]
    // 0x77a33c: cmp             w3, NULL
    // 0x77a340: b.eq            #0x77a4c4
    // 0x77a344: LoadField: r4 = r3->field_7
    //     0x77a344: ldur            w4, [x3, #7]
    // 0x77a348: DecompressPointer r4
    //     0x77a348: add             x4, x4, HEAP, lsl #32
    // 0x77a34c: stur            x4, [fp, #-8]
    // 0x77a350: cmp             w4, NULL
    // 0x77a354: b.eq            #0x77a52c
    // 0x77a358: mov             x0, x4
    // 0x77a35c: r2 = Null
    //     0x77a35c: mov             x2, NULL
    // 0x77a360: r1 = Null
    //     0x77a360: mov             x1, NULL
    // 0x77a364: r4 = LoadClassIdInstr(r0)
    //     0x77a364: ldur            x4, [x0, #-1]
    //     0x77a368: ubfx            x4, x4, #0xc, #0x14
    // 0x77a36c: cmp             x4, #0xc76
    // 0x77a370: b.eq            #0x77a388
    // 0x77a374: r8 = CSSBoxParentData
    //     0x77a374: add             x8, PP, #0x5d, lsl #12  ; [pp+0x5d3d8] Type: CSSBoxParentData
    //     0x77a378: ldr             x8, [x8, #0x3d8]
    // 0x77a37c: r3 = Null
    //     0x77a37c: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d630] Null
    //     0x77a380: ldr             x3, [x3, #0x630]
    // 0x77a384: r0 = DefaultTypeTest()
    //     0x77a384: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77a388: r16 = true
    //     0x77a388: add             x16, NULL, #0x20  ; true
    // 0x77a38c: str             x16, [SP]
    // 0x77a390: ldur            x1, [fp, #-0x20]
    // 0x77a394: r2 = Instance_TextBaseline
    //     0x77a394: add             x2, PP, #0x4e, lsl #12  ; [pp+0x4ee18] Obj!TextBaseline@e393e1
    //     0x77a398: ldr             x2, [x2, #0xe18]
    // 0x77a39c: r4 = const [0, 0x3, 0x1, 0x2, onlyReal, 0x2, null]
    //     0x77a39c: add             x4, PP, #0x45, lsl #12  ; [pp+0x456a8] List(7) [0, 0x3, 0x1, 0x2, "onlyReal", 0x2, Null]
    //     0x77a3a0: ldr             x4, [x4, #0x6a8]
    // 0x77a3a4: r0 = getDistanceToBaseline()
    //     0x77a3a4: bl              #0x76f914  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToBaseline
    // 0x77a3a8: cmp             w0, NULL
    // 0x77a3ac: b.ne            #0x77a3b8
    // 0x77a3b0: d1 = 0.000000
    //     0x77a3b0: eor             v1.16b, v1.16b, v1.16b
    // 0x77a3b4: b               #0x77a3c0
    // 0x77a3b8: LoadField: d0 = r0->field_7
    //     0x77a3b8: ldur            d0, [x0, #7]
    // 0x77a3bc: mov             v1.16b, v0.16b
    // 0x77a3c0: ldur            d0, [fp, #-0x40]
    // 0x77a3c4: fadd            d2, d1, d0
    // 0x77a3c8: ldur            x1, [fp, #-0x28]
    // 0x77a3cc: stur            d2, [fp, #-0x38]
    // 0x77a3d0: r2 = Instance_TextBaseline
    //     0x77a3d0: add             x2, PP, #0x4e, lsl #12  ; [pp+0x4ee18] Obj!TextBaseline@e393e1
    //     0x77a3d4: ldr             x2, [x2, #0xe18]
    // 0x77a3d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x77a3d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x77a3dc: r0 = getDistanceToBaseline()
    //     0x77a3dc: bl              #0x76f914  ; [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToBaseline
    // 0x77a3e0: cmp             w0, NULL
    // 0x77a3e4: b.ne            #0x77a3fc
    // 0x77a3e8: ldur            x1, [fp, #-0x28]
    // 0x77a3ec: r0 = size()
    //     0x77a3ec: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x77a3f0: LoadField: d0 = r0->field_f
    //     0x77a3f0: ldur            d0, [x0, #0xf]
    // 0x77a3f4: mov             v1.16b, v0.16b
    // 0x77a3f8: b               #0x77a404
    // 0x77a3fc: LoadField: d0 = r0->field_7
    //     0x77a3fc: ldur            d0, [x0, #7]
    // 0x77a400: mov             v1.16b, v0.16b
    // 0x77a404: ldur            x0, [fp, #-0x10]
    // 0x77a408: ldur            d0, [fp, #-0x38]
    // 0x77a40c: fsub            d2, d0, d1
    // 0x77a410: stur            d2, [fp, #-0x40]
    // 0x77a414: LoadField: r1 = r0->field_77
    //     0x77a414: ldur            w1, [x0, #0x77]
    // 0x77a418: DecompressPointer r1
    //     0x77a418: add             x1, x1, HEAP, lsl #32
    // 0x77a41c: LoadField: r0 = r1->field_7
    //     0x77a41c: ldur            x0, [x1, #7]
    // 0x77a420: cmp             x0, #0
    // 0x77a424: b.gt            #0x77a474
    // 0x77a428: ldur            x0, [fp, #-8]
    // 0x77a42c: ldur            x1, [fp, #-0x20]
    // 0x77a430: r0 = size()
    //     0x77a430: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x77a434: LoadField: d0 = r0->field_7
    //     0x77a434: ldur            d0, [x0, #7]
    // 0x77a438: stur            d0, [fp, #-0x38]
    // 0x77a43c: r0 = Offset()
    //     0x77a43c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x77a440: ldur            d0, [fp, #-0x38]
    // 0x77a444: StoreField: r0->field_7 = d0
    //     0x77a444: stur            d0, [x0, #7]
    // 0x77a448: ldur            d0, [fp, #-0x40]
    // 0x77a44c: StoreField: r0->field_f = d0
    //     0x77a44c: stur            d0, [x0, #0xf]
    // 0x77a450: ldur            x2, [fp, #-8]
    // 0x77a454: StoreField: r2->field_7 = r0
    //     0x77a454: stur            w0, [x2, #7]
    //     0x77a458: ldurb           w16, [x2, #-1]
    //     0x77a45c: ldurb           w17, [x0, #-1]
    //     0x77a460: and             x16, x17, x16, lsr #2
    //     0x77a464: tst             x16, HEAP, lsr #32
    //     0x77a468: b.eq            #0x77a470
    //     0x77a46c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x77a470: b               #0x77a4c4
    // 0x77a474: ldur            x2, [fp, #-8]
    // 0x77a478: mov             v0.16b, v2.16b
    // 0x77a47c: ldur            x1, [fp, #-0x28]
    // 0x77a480: r0 = size()
    //     0x77a480: bl              #0x67f07c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x77a484: LoadField: d0 = r0->field_7
    //     0x77a484: ldur            d0, [x0, #7]
    // 0x77a488: fneg            d1, d0
    // 0x77a48c: stur            d1, [fp, #-0x38]
    // 0x77a490: r0 = Offset()
    //     0x77a490: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x77a494: ldur            d0, [fp, #-0x38]
    // 0x77a498: StoreField: r0->field_7 = d0
    //     0x77a498: stur            d0, [x0, #7]
    // 0x77a49c: ldur            d0, [fp, #-0x40]
    // 0x77a4a0: StoreField: r0->field_f = d0
    //     0x77a4a0: stur            d0, [x0, #0xf]
    // 0x77a4a4: ldur            x1, [fp, #-8]
    // 0x77a4a8: StoreField: r1->field_7 = r0
    //     0x77a4a8: stur            w0, [x1, #7]
    //     0x77a4ac: ldurb           w16, [x1, #-1]
    //     0x77a4b0: ldurb           w17, [x0, #-1]
    //     0x77a4b4: and             x16, x17, x16, lsr #2
    //     0x77a4b8: tst             x16, HEAP, lsr #32
    //     0x77a4bc: b.eq            #0x77a4c4
    //     0x77a4c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x77a4c4: r0 = Null
    //     0x77a4c4: mov             x0, NULL
    // 0x77a4c8: LeaveFrame
    //     0x77a4c8: mov             SP, fp
    //     0x77a4cc: ldp             fp, lr, [SP], #0x10
    // 0x77a4d0: ret
    //     0x77a4d0: ret             
    // 0x77a4d4: r0 = StateError()
    //     0x77a4d4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77a4d8: mov             x1, x0
    // 0x77a4dc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77a4dc: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77a4e0: StoreField: r1->field_b = r0
    //     0x77a4e0: stur            w0, [x1, #0xb]
    // 0x77a4e4: mov             x0, x1
    // 0x77a4e8: r0 = Throw()
    //     0x77a4e8: bl              #0xec04b8  ; ThrowStub
    // 0x77a4ec: brk             #0
    // 0x77a4f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77a4f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77a4f4: b               #0x77a098
    // 0x77a4f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77a4f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77a4fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77a4fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77a500: SaveReg d0
    //     0x77a500: str             q0, [SP, #-0x10]!
    // 0x77a504: SaveReg r0
    //     0x77a504: str             x0, [SP, #-8]!
    // 0x77a508: r0 = AllocateDouble()
    //     0x77a508: bl              #0xec2254  ; AllocateDoubleStub
    // 0x77a50c: mov             x1, x0
    // 0x77a510: RestoreReg r0
    //     0x77a510: ldr             x0, [SP], #8
    // 0x77a514: RestoreReg d0
    //     0x77a514: ldr             q0, [SP], #0x10
    // 0x77a518: b               #0x77a20c
    // 0x77a51c: stp             q0, q1, [SP, #-0x20]!
    // 0x77a520: r0 = AllocateDouble()
    //     0x77a520: bl              #0xec2254  ; AllocateDoubleStub
    // 0x77a524: ldp             q0, q1, [SP], #0x20
    // 0x77a528: b               #0x77a264
    // 0x77a52c: r0 = NullCastErrorSharedWithFPURegs()
    //     0x77a52c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x788768, size: 0xb0
    // 0x788768: EnterFrame
    //     0x788768: stp             fp, lr, [SP, #-0x10]!
    //     0x78876c: mov             fp, SP
    // 0x788770: AllocStack(0x8)
    //     0x788770: sub             SP, SP, #8
    // 0x788774: SetupParameters(RenderCSSBox this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x788774: mov             x0, x2
    //     0x788778: mov             x4, x1
    //     0x78877c: mov             x3, x2
    //     0x788780: stur            x2, [fp, #-8]
    // 0x788784: r2 = Null
    //     0x788784: mov             x2, NULL
    // 0x788788: r1 = Null
    //     0x788788: mov             x1, NULL
    // 0x78878c: r4 = 60
    //     0x78878c: movz            x4, #0x3c
    // 0x788790: branchIfSmi(r0, 0x78879c)
    //     0x788790: tbz             w0, #0, #0x78879c
    // 0x788794: r4 = LoadClassIdInstr(r0)
    //     0x788794: ldur            x4, [x0, #-1]
    //     0x788798: ubfx            x4, x4, #0xc, #0x14
    // 0x78879c: sub             x4, x4, #0xbba
    // 0x7887a0: cmp             x4, #0x9a
    // 0x7887a4: b.ls            #0x7887b8
    // 0x7887a8: r8 = RenderBox
    //     0x7887a8: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7887ac: r3 = Null
    //     0x7887ac: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d658] Null
    //     0x7887b0: ldr             x3, [x3, #0x658]
    // 0x7887b4: r0 = RenderBox()
    //     0x7887b4: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7887b8: ldur            x0, [fp, #-8]
    // 0x7887bc: LoadField: r1 = r0->field_7
    //     0x7887bc: ldur            w1, [x0, #7]
    // 0x7887c0: DecompressPointer r1
    //     0x7887c0: add             x1, x1, HEAP, lsl #32
    // 0x7887c4: r2 = LoadClassIdInstr(r1)
    //     0x7887c4: ldur            x2, [x1, #-1]
    //     0x7887c8: ubfx            x2, x2, #0xc, #0x14
    // 0x7887cc: cmp             x2, #0xc76
    // 0x7887d0: b.eq            #0x788808
    // 0x7887d4: r1 = <RenderBox>
    //     0x7887d4: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x7887d8: ldr             x1, [x1, #0x1d8]
    // 0x7887dc: r0 = CSSBoxParentData()
    //     0x7887dc: bl              #0x788818  ; AllocateCSSBoxParentDataStub -> CSSBoxParentData (size=0x18)
    // 0x7887e0: r1 = Instance_Offset
    //     0x7887e0: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7887e4: StoreField: r0->field_7 = r1
    //     0x7887e4: stur            w1, [x0, #7]
    // 0x7887e8: ldur            x1, [fp, #-8]
    // 0x7887ec: StoreField: r1->field_7 = r0
    //     0x7887ec: stur            w0, [x1, #7]
    //     0x7887f0: ldurb           w16, [x1, #-1]
    //     0x7887f4: ldurb           w17, [x0, #-1]
    //     0x7887f8: and             x16, x17, x16, lsr #2
    //     0x7887fc: tst             x16, HEAP, lsr #32
    //     0x788800: b.eq            #0x788808
    //     0x788804: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x788808: r0 = Null
    //     0x788808: mov             x0, NULL
    // 0x78880c: LeaveFrame
    //     0x78880c: mov             SP, fp
    //     0x788810: ldp             fp, lr, [SP], #0x10
    // 0x788814: ret
    //     0x788814: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x79f118, size: 0x30
    // 0x79f118: EnterFrame
    //     0x79f118: stp             fp, lr, [SP, #-0x10]!
    //     0x79f11c: mov             fp, SP
    // 0x79f120: CheckStackOverflow
    //     0x79f120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79f124: cmp             SP, x16
    //     0x79f128: b.ls            #0x79f140
    // 0x79f12c: r0 = defaultPaint()
    //     0x79f12c: bl              #0x79f148  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultPaint
    // 0x79f130: r0 = Null
    //     0x79f130: mov             x0, NULL
    // 0x79f134: LeaveFrame
    //     0x79f134: mov             SP, fp
    //     0x79f138: ldp             fp, lr, [SP], #0x10
    // 0x79f13c: ret
    //     0x79f13c: ret             
    // 0x79f140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79f140: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79f144: b               #0x79f12c
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x800080, size: 0x2c
    // 0x800080: EnterFrame
    //     0x800080: stp             fp, lr, [SP, #-0x10]!
    //     0x800084: mov             fp, SP
    // 0x800088: CheckStackOverflow
    //     0x800088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80008c: cmp             SP, x16
    //     0x800090: b.ls            #0x8000a4
    // 0x800094: r0 = defaultHitTestChildren()
    //     0x800094: bl              #0x8000ac  ; [package:flutter_html/src/css_box_widget.dart] _RenderCSSBox&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultHitTestChildren
    // 0x800098: LeaveFrame
    //     0x800098: mov             SP, fp
    //     0x80009c: ldp             fp, lr, [SP], #0x10
    // 0x8000a0: ret
    //     0x8000a0: ret             
    // 0x8000a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8000a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8000a8: b               #0x800094
  }
  _ RenderCSSBox(/* No info */) {
    // ** addr: 0x859790, size: 0x120
    // 0x859790: EnterFrame
    //     0x859790: stp             fp, lr, [SP, #-0x10]!
    //     0x859794: mov             fp, SP
    // 0x859798: AllocStack(0x8)
    //     0x859798: sub             SP, SP, #8
    // 0x85979c: r4 = false
    //     0x85979c: add             x4, NULL, #0x30  ; false
    // 0x8597a0: mov             x0, x3
    // 0x8597a4: mov             x3, x5
    // 0x8597a8: mov             x5, x2
    // 0x8597ac: mov             x2, x6
    // 0x8597b0: mov             x6, x1
    // 0x8597b4: stur            x1, [fp, #-8]
    // 0x8597b8: mov             x1, x7
    // 0x8597bc: CheckStackOverflow
    //     0x8597bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8597c0: cmp             SP, x16
    //     0x8597c4: b.ls            #0x8598a8
    // 0x8597c8: StoreField: r6->field_67 = r0
    //     0x8597c8: stur            w0, [x6, #0x67]
    //     0x8597cc: ldurb           w16, [x6, #-1]
    //     0x8597d0: ldurb           w17, [x0, #-1]
    //     0x8597d4: and             x16, x17, x16, lsr #2
    //     0x8597d8: tst             x16, HEAP, lsr #32
    //     0x8597dc: b.eq            #0x8597e4
    //     0x8597e0: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8597e4: ldr             x0, [fp, #0x10]
    // 0x8597e8: StoreField: r6->field_6b = r0
    //     0x8597e8: stur            w0, [x6, #0x6b]
    //     0x8597ec: ldurb           w16, [x6, #-1]
    //     0x8597f0: ldurb           w17, [x0, #-1]
    //     0x8597f4: and             x16, x17, x16, lsr #2
    //     0x8597f8: tst             x16, HEAP, lsr #32
    //     0x8597fc: b.eq            #0x859804
    //     0x859800: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x859804: mov             x0, x3
    // 0x859808: StoreField: r6->field_6f = r0
    //     0x859808: stur            w0, [x6, #0x6f]
    //     0x85980c: ldurb           w16, [x6, #-1]
    //     0x859810: ldurb           w17, [x0, #-1]
    //     0x859814: and             x16, x17, x16, lsr #2
    //     0x859818: tst             x16, HEAP, lsr #32
    //     0x85981c: b.eq            #0x859824
    //     0x859820: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x859824: mov             x0, x2
    // 0x859828: StoreField: r6->field_73 = r0
    //     0x859828: stur            w0, [x6, #0x73]
    //     0x85982c: ldurb           w16, [x6, #-1]
    //     0x859830: ldurb           w17, [x0, #-1]
    //     0x859834: and             x16, x17, x16, lsr #2
    //     0x859838: tst             x16, HEAP, lsr #32
    //     0x85983c: b.eq            #0x859844
    //     0x859840: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x859844: mov             x0, x1
    // 0x859848: StoreField: r6->field_77 = r0
    //     0x859848: stur            w0, [x6, #0x77]
    //     0x85984c: ldurb           w16, [x6, #-1]
    //     0x859850: ldurb           w17, [x0, #-1]
    //     0x859854: and             x16, x17, x16, lsr #2
    //     0x859858: tst             x16, HEAP, lsr #32
    //     0x85985c: b.eq            #0x859864
    //     0x859860: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x859864: StoreField: r6->field_7b = r5
    //     0x859864: stur            w5, [x6, #0x7b]
    // 0x859868: StoreField: r6->field_7f = r4
    //     0x859868: stur            w4, [x6, #0x7f]
    // 0x85986c: StoreField: r6->field_57 = rZR
    //     0x85986c: stur            xzr, [x6, #0x57]
    // 0x859870: r0 = _LayoutCacheStorage()
    //     0x859870: bl              #0x85382c  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x859874: ldur            x1, [fp, #-8]
    // 0x859878: StoreField: r1->field_4f = r0
    //     0x859878: stur            w0, [x1, #0x4f]
    //     0x85987c: ldurb           w16, [x1, #-1]
    //     0x859880: ldurb           w17, [x0, #-1]
    //     0x859884: and             x16, x17, x16, lsr #2
    //     0x859888: tst             x16, HEAP, lsr #32
    //     0x85988c: b.eq            #0x859894
    //     0x859890: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x859894: r0 = RenderObject()
    //     0x859894: bl              #0x853718  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x859898: r0 = Null
    //     0x859898: mov             x0, NULL
    // 0x85989c: LeaveFrame
    //     0x85989c: mov             SP, fp
    //     0x8598a0: ldp             fp, lr, [SP], #0x10
    // 0x8598a4: ret
    //     0x8598a4: ret             
    // 0x8598a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8598a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8598ac: b               #0x8597c8
  }
  set _ shrinkWrap=(/* No info */) {
    // ** addr: 0xc6c404, size: 0x38
    // 0xc6c404: EnterFrame
    //     0xc6c404: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c408: mov             fp, SP
    // 0xc6c40c: r0 = false
    //     0xc6c40c: add             x0, NULL, #0x30  ; false
    // 0xc6c410: CheckStackOverflow
    //     0xc6c410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c414: cmp             SP, x16
    //     0xc6c418: b.ls            #0xc6c434
    // 0xc6c41c: StoreField: r1->field_7f = r0
    //     0xc6c41c: stur            w0, [x1, #0x7f]
    // 0xc6c420: r0 = markNeedsLayout()
    //     0xc6c420: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c424: r0 = Null
    //     0xc6c424: mov             x0, NULL
    // 0xc6c428: LeaveFrame
    //     0xc6c428: mov             SP, fp
    //     0xc6c42c: ldp             fp, lr, [SP], #0x10
    // 0xc6c430: ret
    //     0xc6c430: ret             
    // 0xc6c434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c438: b               #0xc6c41c
  }
  set _ childIsReplaced=(/* No info */) {
    // ** addr: 0xc6c43c, size: 0x34
    // 0xc6c43c: EnterFrame
    //     0xc6c43c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c440: mov             fp, SP
    // 0xc6c444: CheckStackOverflow
    //     0xc6c444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c448: cmp             SP, x16
    //     0xc6c44c: b.ls            #0xc6c468
    // 0xc6c450: StoreField: r1->field_7b = r2
    //     0xc6c450: stur            w2, [x1, #0x7b]
    // 0xc6c454: r0 = markNeedsLayout()
    //     0xc6c454: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c458: r0 = Null
    //     0xc6c458: mov             x0, NULL
    // 0xc6c45c: LeaveFrame
    //     0xc6c45c: mov             SP, fp
    //     0xc6c460: ldp             fp, lr, [SP], #0x10
    // 0xc6c464: ret
    //     0xc6c464: ret             
    // 0xc6c468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c468: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c46c: b               #0xc6c450
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc6c470, size: 0x50
    // 0xc6c470: EnterFrame
    //     0xc6c470: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c474: mov             fp, SP
    // 0xc6c478: mov             x0, x2
    // 0xc6c47c: CheckStackOverflow
    //     0xc6c47c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c480: cmp             SP, x16
    //     0xc6c484: b.ls            #0xc6c4b8
    // 0xc6c488: StoreField: r1->field_77 = r0
    //     0xc6c488: stur            w0, [x1, #0x77]
    //     0xc6c48c: ldurb           w16, [x1, #-1]
    //     0xc6c490: ldurb           w17, [x0, #-1]
    //     0xc6c494: and             x16, x17, x16, lsr #2
    //     0xc6c498: tst             x16, HEAP, lsr #32
    //     0xc6c49c: b.eq            #0xc6c4a4
    //     0xc6c4a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c4a4: r0 = markNeedsLayout()
    //     0xc6c4a4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c4a8: r0 = Null
    //     0xc6c4a8: mov             x0, NULL
    // 0xc6c4ac: LeaveFrame
    //     0xc6c4ac: mov             SP, fp
    //     0xc6c4b0: ldp             fp, lr, [SP], #0x10
    // 0xc6c4b4: ret
    //     0xc6c4b4: ret             
    // 0xc6c4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c4bc: b               #0xc6c488
  }
  set _ borderSize=(/* No info */) {
    // ** addr: 0xc6c4c0, size: 0x30
    // 0xc6c4c0: EnterFrame
    //     0xc6c4c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c4c4: mov             fp, SP
    // 0xc6c4c8: CheckStackOverflow
    //     0xc6c4c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c4cc: cmp             SP, x16
    //     0xc6c4d0: b.ls            #0xc6c4e8
    // 0xc6c4d4: r0 = markNeedsLayout()
    //     0xc6c4d4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c4d8: r0 = Null
    //     0xc6c4d8: mov             x0, NULL
    // 0xc6c4dc: LeaveFrame
    //     0xc6c4dc: mov             SP, fp
    //     0xc6c4e0: ldp             fp, lr, [SP], #0x10
    // 0xc6c4e4: ret
    //     0xc6c4e4: ret             
    // 0xc6c4e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c4e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c4ec: b               #0xc6c4d4
  }
  set _ margins=(/* No info */) {
    // ** addr: 0xc6c4f0, size: 0x50
    // 0xc6c4f0: EnterFrame
    //     0xc6c4f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c4f4: mov             fp, SP
    // 0xc6c4f8: mov             x0, x2
    // 0xc6c4fc: CheckStackOverflow
    //     0xc6c4fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c500: cmp             SP, x16
    //     0xc6c504: b.ls            #0xc6c538
    // 0xc6c508: StoreField: r1->field_73 = r0
    //     0xc6c508: stur            w0, [x1, #0x73]
    //     0xc6c50c: ldurb           w16, [x1, #-1]
    //     0xc6c510: ldurb           w17, [x0, #-1]
    //     0xc6c514: and             x16, x17, x16, lsr #2
    //     0xc6c518: tst             x16, HEAP, lsr #32
    //     0xc6c51c: b.eq            #0xc6c524
    //     0xc6c520: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c524: r0 = markNeedsLayout()
    //     0xc6c524: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c528: r0 = Null
    //     0xc6c528: mov             x0, NULL
    // 0xc6c52c: LeaveFrame
    //     0xc6c52c: mov             SP, fp
    //     0xc6c530: ldp             fp, lr, [SP], #0x10
    // 0xc6c534: ret
    //     0xc6c534: ret             
    // 0xc6c538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c538: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c53c: b               #0xc6c508
  }
  set _ height=(/* No info */) {
    // ** addr: 0xc6c540, size: 0x50
    // 0xc6c540: EnterFrame
    //     0xc6c540: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c544: mov             fp, SP
    // 0xc6c548: mov             x0, x2
    // 0xc6c54c: CheckStackOverflow
    //     0xc6c54c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c550: cmp             SP, x16
    //     0xc6c554: b.ls            #0xc6c588
    // 0xc6c558: StoreField: r1->field_6f = r0
    //     0xc6c558: stur            w0, [x1, #0x6f]
    //     0xc6c55c: ldurb           w16, [x1, #-1]
    //     0xc6c560: ldurb           w17, [x0, #-1]
    //     0xc6c564: and             x16, x17, x16, lsr #2
    //     0xc6c568: tst             x16, HEAP, lsr #32
    //     0xc6c56c: b.eq            #0xc6c574
    //     0xc6c570: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c574: r0 = markNeedsLayout()
    //     0xc6c574: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c578: r0 = Null
    //     0xc6c578: mov             x0, NULL
    // 0xc6c57c: LeaveFrame
    //     0xc6c57c: mov             SP, fp
    //     0xc6c580: ldp             fp, lr, [SP], #0x10
    // 0xc6c584: ret
    //     0xc6c584: ret             
    // 0xc6c588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c58c: b               #0xc6c558
  }
  set _ width=(/* No info */) {
    // ** addr: 0xc6c590, size: 0x50
    // 0xc6c590: EnterFrame
    //     0xc6c590: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c594: mov             fp, SP
    // 0xc6c598: mov             x0, x2
    // 0xc6c59c: CheckStackOverflow
    //     0xc6c59c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c5a0: cmp             SP, x16
    //     0xc6c5a4: b.ls            #0xc6c5d8
    // 0xc6c5a8: StoreField: r1->field_6b = r0
    //     0xc6c5a8: stur            w0, [x1, #0x6b]
    //     0xc6c5ac: ldurb           w16, [x1, #-1]
    //     0xc6c5b0: ldurb           w17, [x0, #-1]
    //     0xc6c5b4: and             x16, x17, x16, lsr #2
    //     0xc6c5b8: tst             x16, HEAP, lsr #32
    //     0xc6c5bc: b.eq            #0xc6c5c4
    //     0xc6c5c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c5c4: r0 = markNeedsLayout()
    //     0xc6c5c4: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c5c8: r0 = Null
    //     0xc6c5c8: mov             x0, NULL
    // 0xc6c5cc: LeaveFrame
    //     0xc6c5cc: mov             SP, fp
    //     0xc6c5d0: ldp             fp, lr, [SP], #0x10
    // 0xc6c5d4: ret
    //     0xc6c5d4: ret             
    // 0xc6c5d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c5d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c5dc: b               #0xc6c5a8
  }
  set _ display=(/* No info */) {
    // ** addr: 0xc6c5e0, size: 0x50
    // 0xc6c5e0: EnterFrame
    //     0xc6c5e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c5e4: mov             fp, SP
    // 0xc6c5e8: mov             x0, x2
    // 0xc6c5ec: CheckStackOverflow
    //     0xc6c5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c5f0: cmp             SP, x16
    //     0xc6c5f4: b.ls            #0xc6c628
    // 0xc6c5f8: StoreField: r1->field_67 = r0
    //     0xc6c5f8: stur            w0, [x1, #0x67]
    //     0xc6c5fc: ldurb           w16, [x1, #-1]
    //     0xc6c600: ldurb           w17, [x0, #-1]
    //     0xc6c604: and             x16, x17, x16, lsr #2
    //     0xc6c608: tst             x16, HEAP, lsr #32
    //     0xc6c60c: b.eq            #0xc6c614
    //     0xc6c610: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c614: r0 = markNeedsLayout()
    //     0xc6c614: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c618: r0 = Null
    //     0xc6c618: mov             x0, NULL
    // 0xc6c61c: LeaveFrame
    //     0xc6c61c: mov             SP, fp
    //     0xc6c620: ldp             fp, lr, [SP], #0x10
    // 0xc6c624: ret
    //     0xc6c624: ret             
    // 0xc6c628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c62c: b               #0xc6c5f8
  }
}

// class id: 3190, size: 0x18, field offset: 0x18
class CSSBoxParentData extends ContainerBoxParentData<dynamic> {
}

// class id: 4567, size: 0x3c, field offset: 0x10
class _CSSBoxRenderer extends MultiChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x8596ac, size: 0xe4
    // 0x8596ac: EnterFrame
    //     0x8596ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8596b0: mov             fp, SP
    // 0x8596b4: AllocStack(0x48)
    //     0x8596b4: sub             SP, SP, #0x48
    // 0x8596b8: SetupParameters(_CSSBoxRenderer this /* r1 => r0, fp-0x18 */)
    //     0x8596b8: mov             x0, x1
    //     0x8596bc: stur            x1, [fp, #-0x18]
    // 0x8596c0: CheckStackOverflow
    //     0x8596c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8596c4: cmp             SP, x16
    //     0x8596c8: b.ls            #0x859788
    // 0x8596cc: LoadField: r3 = r0->field_f
    //     0x8596cc: ldur            w3, [x0, #0xf]
    // 0x8596d0: DecompressPointer r3
    //     0x8596d0: add             x3, x3, HEAP, lsl #32
    // 0x8596d4: stur            x3, [fp, #-0x10]
    // 0x8596d8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8596d8: ldur            w2, [x0, #0x17]
    // 0x8596dc: DecompressPointer r2
    //     0x8596dc: add             x2, x2, HEAP, lsl #32
    // 0x8596e0: stur            x2, [fp, #-8]
    // 0x8596e4: LoadField: d1 = r0->field_2f
    //     0x8596e4: ldur            d1, [x0, #0x2f]
    // 0x8596e8: mov             x1, x2
    // 0x8596ec: mov             v0.16b, v1.16b
    // 0x8596f0: stur            d1, [fp, #-0x40]
    // 0x8596f4: r0 = Normalize.normalize()
    //     0x8596f4: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x8596f8: ldur            x0, [fp, #-0x18]
    // 0x8596fc: LoadField: r2 = r0->field_1b
    //     0x8596fc: ldur            w2, [x0, #0x1b]
    // 0x859700: DecompressPointer r2
    //     0x859700: add             x2, x2, HEAP, lsl #32
    // 0x859704: mov             x1, x2
    // 0x859708: ldur            d0, [fp, #-0x40]
    // 0x85970c: stur            x2, [fp, #-0x20]
    // 0x859710: r0 = Normalize.normalize()
    //     0x859710: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x859714: ldur            x0, [fp, #-0x18]
    // 0x859718: LoadField: r2 = r0->field_13
    //     0x859718: ldur            w2, [x0, #0x13]
    // 0x85971c: DecompressPointer r2
    //     0x85971c: add             x2, x2, HEAP, lsl #32
    // 0x859720: mov             x1, x0
    // 0x859724: r0 = _preProcessMargins()
    //     0x859724: bl              #0x8598bc  ; [package:flutter_html/src/css_box_widget.dart] _CSSBoxRenderer::_preProcessMargins
    // 0x859728: mov             x1, x0
    // 0x85972c: ldur            x0, [fp, #-0x18]
    // 0x859730: stur            x1, [fp, #-0x38]
    // 0x859734: LoadField: r7 = r0->field_27
    //     0x859734: ldur            w7, [x0, #0x27]
    // 0x859738: DecompressPointer r7
    //     0x859738: add             x7, x7, HEAP, lsl #32
    // 0x85973c: stur            x7, [fp, #-0x30]
    // 0x859740: LoadField: r2 = r0->field_2b
    //     0x859740: ldur            w2, [x0, #0x2b]
    // 0x859744: DecompressPointer r2
    //     0x859744: add             x2, x2, HEAP, lsl #32
    // 0x859748: stur            x2, [fp, #-0x28]
    // 0x85974c: r0 = RenderCSSBox()
    //     0x85974c: bl              #0x8598b0  ; AllocateRenderCSSBoxStub -> RenderCSSBox (size=0x84)
    // 0x859750: stur            x0, [fp, #-0x18]
    // 0x859754: ldur            x16, [fp, #-8]
    // 0x859758: str             x16, [SP]
    // 0x85975c: mov             x1, x0
    // 0x859760: ldur            x2, [fp, #-0x28]
    // 0x859764: ldur            x3, [fp, #-0x10]
    // 0x859768: ldur            x5, [fp, #-0x20]
    // 0x85976c: ldur            x6, [fp, #-0x38]
    // 0x859770: ldur            x7, [fp, #-0x30]
    // 0x859774: r0 = RenderCSSBox()
    //     0x859774: bl              #0x859790  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::RenderCSSBox
    // 0x859778: ldur            x0, [fp, #-0x18]
    // 0x85977c: LeaveFrame
    //     0x85977c: mov             SP, fp
    //     0x859780: ldp             fp, lr, [SP], #0x10
    // 0x859784: ret
    //     0x859784: ret             
    // 0x859788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85978c: b               #0x8596cc
  }
  _ _preProcessMargins(/* No info */) {
    // ** addr: 0x8598bc, size: 0x3a4
    // 0x8598bc: EnterFrame
    //     0x8598bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8598c0: mov             fp, SP
    // 0x8598c4: AllocStack(0x40)
    //     0x8598c4: sub             SP, SP, #0x40
    // 0x8598c8: SetupParameters(_CSSBoxRenderer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8598c8: stur            x1, [fp, #-8]
    //     0x8598cc: stur            x2, [fp, #-0x10]
    // 0x8598d0: CheckStackOverflow
    //     0x8598d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8598d4: cmp             SP, x16
    //     0x8598d8: b.ls            #0x859c58
    // 0x8598dc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x8598dc: ldur            w0, [x2, #0x17]
    // 0x8598e0: DecompressPointer r0
    //     0x8598e0: add             x0, x0, HEAP, lsl #32
    // 0x8598e4: cmp             w0, NULL
    // 0x8598e8: b.ne            #0x8598f4
    // 0x8598ec: LoadField: r0 = r2->field_23
    //     0x8598ec: ldur            w0, [x2, #0x23]
    // 0x8598f0: DecompressPointer r0
    //     0x8598f0: add             x0, x0, HEAP, lsl #32
    // 0x8598f4: cmp             w0, NULL
    // 0x8598f8: b.ne            #0x859918
    // 0x8598fc: r0 = Margin()
    //     0x8598fc: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859900: StoreField: r0->field_7 = rZR
    //     0x859900: stur            xzr, [x0, #7]
    // 0x859904: r1 = Instance_Unit
    //     0x859904: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859908: ldr             x1, [x1, #0xa98]
    // 0x85990c: StoreField: r0->field_f = r1
    //     0x85990c: stur            w1, [x0, #0xf]
    // 0x859910: mov             x2, x0
    // 0x859914: b               #0x859924
    // 0x859918: r1 = Instance_Unit
    //     0x859918: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x85991c: ldr             x1, [x1, #0xa98]
    // 0x859920: mov             x2, x0
    // 0x859924: ldur            x0, [fp, #-0x10]
    // 0x859928: stur            x2, [fp, #-0x18]
    // 0x85992c: LoadField: r3 = r0->field_1b
    //     0x85992c: ldur            w3, [x0, #0x1b]
    // 0x859930: DecompressPointer r3
    //     0x859930: add             x3, x3, HEAP, lsl #32
    // 0x859934: cmp             w3, NULL
    // 0x859938: b.ne            #0x859944
    // 0x85993c: LoadField: r3 = r0->field_1f
    //     0x85993c: ldur            w3, [x0, #0x1f]
    // 0x859940: DecompressPointer r3
    //     0x859940: add             x3, x3, HEAP, lsl #32
    // 0x859944: cmp             w3, NULL
    // 0x859948: b.ne            #0x859968
    // 0x85994c: r0 = Margin()
    //     0x85994c: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859950: StoreField: r0->field_7 = rZR
    //     0x859950: stur            xzr, [x0, #7]
    // 0x859954: r1 = Instance_Unit
    //     0x859954: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859958: ldr             x1, [x1, #0xa98]
    // 0x85995c: StoreField: r0->field_f = r1
    //     0x85995c: stur            w1, [x0, #0xf]
    // 0x859960: mov             x2, x0
    // 0x859964: b               #0x85996c
    // 0x859968: mov             x2, x3
    // 0x85996c: ldur            x0, [fp, #-8]
    // 0x859970: stur            x2, [fp, #-0x28]
    // 0x859974: LoadField: r3 = r0->field_27
    //     0x859974: ldur            w3, [x0, #0x27]
    // 0x859978: DecompressPointer r3
    //     0x859978: add             x3, x3, HEAP, lsl #32
    // 0x85997c: LoadField: r4 = r3->field_7
    //     0x85997c: ldur            x4, [x3, #7]
    // 0x859980: cmp             x4, #0
    // 0x859984: b.gt            #0x859a3c
    // 0x859988: ldur            x3, [fp, #-0x10]
    // 0x85998c: LoadField: r4 = r3->field_7
    //     0x85998c: ldur            w4, [x3, #7]
    // 0x859990: DecompressPointer r4
    //     0x859990: add             x4, x4, HEAP, lsl #32
    // 0x859994: stur            x4, [fp, #-0x20]
    // 0x859998: cmp             w4, NULL
    // 0x85999c: b.ne            #0x8599ac
    // 0x8599a0: LoadField: r5 = r3->field_f
    //     0x8599a0: ldur            w5, [x3, #0xf]
    // 0x8599a4: DecompressPointer r5
    //     0x8599a4: add             x5, x5, HEAP, lsl #32
    // 0x8599a8: b               #0x8599b0
    // 0x8599ac: mov             x5, x4
    // 0x8599b0: cmp             w5, NULL
    // 0x8599b4: b.ne            #0x8599d4
    // 0x8599b8: r0 = Margin()
    //     0x8599b8: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x8599bc: StoreField: r0->field_7 = rZR
    //     0x8599bc: stur            xzr, [x0, #7]
    // 0x8599c0: r1 = Instance_Unit
    //     0x8599c0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x8599c4: ldr             x1, [x1, #0xa98]
    // 0x8599c8: StoreField: r0->field_f = r1
    //     0x8599c8: stur            w1, [x0, #0xf]
    // 0x8599cc: mov             x2, x0
    // 0x8599d0: b               #0x8599d8
    // 0x8599d4: mov             x2, x5
    // 0x8599d8: ldur            x0, [fp, #-0x10]
    // 0x8599dc: stur            x2, [fp, #-0x38]
    // 0x8599e0: LoadField: r3 = r0->field_b
    //     0x8599e0: ldur            w3, [x0, #0xb]
    // 0x8599e4: DecompressPointer r3
    //     0x8599e4: add             x3, x3, HEAP, lsl #32
    // 0x8599e8: stur            x3, [fp, #-0x30]
    // 0x8599ec: cmp             w3, NULL
    // 0x8599f0: b.ne            #0x859a04
    // 0x8599f4: LoadField: r4 = r0->field_13
    //     0x8599f4: ldur            w4, [x0, #0x13]
    // 0x8599f8: DecompressPointer r4
    //     0x8599f8: add             x4, x4, HEAP, lsl #32
    // 0x8599fc: mov             x0, x4
    // 0x859a00: b               #0x859a08
    // 0x859a04: mov             x0, x3
    // 0x859a08: cmp             w0, NULL
    // 0x859a0c: b.ne            #0x859a24
    // 0x859a10: r0 = Margin()
    //     0x859a10: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859a14: StoreField: r0->field_7 = rZR
    //     0x859a14: stur            xzr, [x0, #7]
    // 0x859a18: r1 = Instance_Unit
    //     0x859a18: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859a1c: ldr             x1, [x1, #0xa98]
    // 0x859a20: StoreField: r0->field_f = r1
    //     0x859a20: stur            w1, [x0, #0xf]
    // 0x859a24: ldur            x6, [fp, #-0x38]
    // 0x859a28: mov             x5, x0
    // 0x859a2c: ldur            x4, [fp, #-0x20]
    // 0x859a30: ldur            x3, [fp, #-0x30]
    // 0x859a34: mov             x2, x1
    // 0x859a38: b               #0x859af0
    // 0x859a3c: ldur            x0, [fp, #-0x10]
    // 0x859a40: LoadField: r2 = r0->field_7
    //     0x859a40: ldur            w2, [x0, #7]
    // 0x859a44: DecompressPointer r2
    //     0x859a44: add             x2, x2, HEAP, lsl #32
    // 0x859a48: stur            x2, [fp, #-0x20]
    // 0x859a4c: cmp             w2, NULL
    // 0x859a50: b.ne            #0x859a60
    // 0x859a54: LoadField: r3 = r0->field_13
    //     0x859a54: ldur            w3, [x0, #0x13]
    // 0x859a58: DecompressPointer r3
    //     0x859a58: add             x3, x3, HEAP, lsl #32
    // 0x859a5c: b               #0x859a64
    // 0x859a60: mov             x3, x2
    // 0x859a64: cmp             w3, NULL
    // 0x859a68: b.ne            #0x859a88
    // 0x859a6c: r0 = Margin()
    //     0x859a6c: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859a70: StoreField: r0->field_7 = rZR
    //     0x859a70: stur            xzr, [x0, #7]
    // 0x859a74: r1 = Instance_Unit
    //     0x859a74: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859a78: ldr             x1, [x1, #0xa98]
    // 0x859a7c: StoreField: r0->field_f = r1
    //     0x859a7c: stur            w1, [x0, #0xf]
    // 0x859a80: mov             x2, x0
    // 0x859a84: b               #0x859a8c
    // 0x859a88: mov             x2, x3
    // 0x859a8c: ldur            x0, [fp, #-0x10]
    // 0x859a90: stur            x2, [fp, #-0x38]
    // 0x859a94: LoadField: r3 = r0->field_b
    //     0x859a94: ldur            w3, [x0, #0xb]
    // 0x859a98: DecompressPointer r3
    //     0x859a98: add             x3, x3, HEAP, lsl #32
    // 0x859a9c: stur            x3, [fp, #-0x30]
    // 0x859aa0: cmp             w3, NULL
    // 0x859aa4: b.ne            #0x859ab8
    // 0x859aa8: LoadField: r4 = r0->field_f
    //     0x859aa8: ldur            w4, [x0, #0xf]
    // 0x859aac: DecompressPointer r4
    //     0x859aac: add             x4, x4, HEAP, lsl #32
    // 0x859ab0: mov             x0, x4
    // 0x859ab4: b               #0x859abc
    // 0x859ab8: mov             x0, x3
    // 0x859abc: cmp             w0, NULL
    // 0x859ac0: b.ne            #0x859adc
    // 0x859ac4: r0 = Margin()
    //     0x859ac4: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859ac8: StoreField: r0->field_7 = rZR
    //     0x859ac8: stur            xzr, [x0, #7]
    // 0x859acc: r2 = Instance_Unit
    //     0x859acc: add             x2, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859ad0: ldr             x2, [x2, #0xa98]
    // 0x859ad4: StoreField: r0->field_f = r2
    //     0x859ad4: stur            w2, [x0, #0xf]
    // 0x859ad8: b               #0x859ae0
    // 0x859adc: mov             x2, x1
    // 0x859ae0: ldur            x6, [fp, #-0x38]
    // 0x859ae4: mov             x5, x0
    // 0x859ae8: ldur            x4, [fp, #-0x20]
    // 0x859aec: ldur            x3, [fp, #-0x30]
    // 0x859af0: ldur            x0, [fp, #-8]
    // 0x859af4: stur            x6, [fp, #-0x10]
    // 0x859af8: stur            x5, [fp, #-0x20]
    // 0x859afc: stur            x4, [fp, #-0x30]
    // 0x859b00: stur            x3, [fp, #-0x38]
    // 0x859b04: LoadField: d1 = r0->field_2f
    //     0x859b04: ldur            d1, [x0, #0x2f]
    // 0x859b08: mov             x1, x6
    // 0x859b0c: mov             v0.16b, v1.16b
    // 0x859b10: stur            d1, [fp, #-0x40]
    // 0x859b14: r0 = Normalize.normalize()
    //     0x859b14: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x859b18: ldur            x1, [fp, #-0x20]
    // 0x859b1c: ldur            d0, [fp, #-0x40]
    // 0x859b20: r0 = Normalize.normalize()
    //     0x859b20: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x859b24: ldur            x1, [fp, #-0x18]
    // 0x859b28: ldur            d0, [fp, #-0x40]
    // 0x859b2c: r0 = Normalize.normalize()
    //     0x859b2c: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x859b30: ldur            x1, [fp, #-0x28]
    // 0x859b34: ldur            d0, [fp, #-0x40]
    // 0x859b38: r0 = Normalize.normalize()
    //     0x859b38: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0x859b3c: ldur            x0, [fp, #-8]
    // 0x859b40: LoadField: r1 = r0->field_f
    //     0x859b40: ldur            w1, [x0, #0xf]
    // 0x859b44: DecompressPointer r1
    //     0x859b44: add             x1, x1, HEAP, lsl #32
    // 0x859b48: r16 = Instance_Display
    //     0x859b48: add             x16, PP, #0x52, lsl #12  ; [pp+0x520b0] Obj!Display@e337c1
    //     0x859b4c: ldr             x16, [x16, #0xb0]
    // 0x859b50: cmp             w1, w16
    // 0x859b54: b.eq            #0x859b68
    // 0x859b58: r16 = Instance_Display
    //     0x859b58: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ed0] Obj!Display@e337a1
    //     0x859b5c: ldr             x16, [x16, #0xed0]
    // 0x859b60: cmp             w1, w16
    // 0x859b64: b.ne            #0x859c10
    // 0x859b68: ldur            x0, [fp, #-0x30]
    // 0x859b6c: cmp             w0, NULL
    // 0x859b70: b.ne            #0x859b80
    // 0x859b74: r1 = Instance_Unit
    //     0x859b74: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859b78: ldr             x1, [x1, #0xa98]
    // 0x859b7c: b               #0x859bbc
    // 0x859b80: LoadField: r1 = r0->field_f
    //     0x859b80: ldur            w1, [x0, #0xf]
    // 0x859b84: DecompressPointer r1
    //     0x859b84: add             x1, x1, HEAP, lsl #32
    // 0x859b88: r16 = Instance_Unit
    //     0x859b88: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x859b8c: ldr             x16, [x16, #0x950]
    // 0x859b90: cmp             w1, w16
    // 0x859b94: b.ne            #0x859bb4
    // 0x859b98: r0 = Margin()
    //     0x859b98: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859b9c: StoreField: r0->field_7 = rZR
    //     0x859b9c: stur            xzr, [x0, #7]
    // 0x859ba0: r1 = Instance_Unit
    //     0x859ba0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859ba4: ldr             x1, [x1, #0xa98]
    // 0x859ba8: StoreField: r0->field_f = r1
    //     0x859ba8: stur            w1, [x0, #0xf]
    // 0x859bac: mov             x2, x0
    // 0x859bb0: b               #0x859bc0
    // 0x859bb4: r1 = Instance_Unit
    //     0x859bb4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859bb8: ldr             x1, [x1, #0xa98]
    // 0x859bbc: ldur            x2, [fp, #-0x10]
    // 0x859bc0: ldur            x0, [fp, #-0x38]
    // 0x859bc4: stur            x2, [fp, #-8]
    // 0x859bc8: cmp             w0, NULL
    // 0x859bcc: b.eq            #0x859c00
    // 0x859bd0: LoadField: r3 = r0->field_f
    //     0x859bd0: ldur            w3, [x0, #0xf]
    // 0x859bd4: DecompressPointer r3
    //     0x859bd4: add             x3, x3, HEAP, lsl #32
    // 0x859bd8: r16 = Instance_Unit
    //     0x859bd8: add             x16, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0x859bdc: ldr             x16, [x16, #0x950]
    // 0x859be0: cmp             w3, w16
    // 0x859be4: b.ne            #0x859c00
    // 0x859be8: r0 = Margin()
    //     0x859be8: bl              #0x7582e0  ; AllocateMarginStub -> Margin (size=0x14)
    // 0x859bec: StoreField: r0->field_7 = rZR
    //     0x859bec: stur            xzr, [x0, #7]
    // 0x859bf0: r1 = Instance_Unit
    //     0x859bf0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0x859bf4: ldr             x1, [x1, #0xa98]
    // 0x859bf8: StoreField: r0->field_f = r1
    //     0x859bf8: stur            w1, [x0, #0xf]
    // 0x859bfc: b               #0x859c04
    // 0x859c00: ldur            x0, [fp, #-0x20]
    // 0x859c04: ldur            x3, [fp, #-8]
    // 0x859c08: mov             x2, x0
    // 0x859c0c: b               #0x859c18
    // 0x859c10: ldur            x3, [fp, #-0x10]
    // 0x859c14: ldur            x2, [fp, #-0x20]
    // 0x859c18: ldur            x1, [fp, #-0x18]
    // 0x859c1c: ldur            x0, [fp, #-0x28]
    // 0x859c20: stur            x3, [fp, #-8]
    // 0x859c24: stur            x2, [fp, #-0x10]
    // 0x859c28: r0 = Margins()
    //     0x859c28: bl              #0x7582d4  ; AllocateMarginsStub -> Margins (size=0x28)
    // 0x859c2c: ldur            x1, [fp, #-8]
    // 0x859c30: StoreField: r0->field_7 = r1
    //     0x859c30: stur            w1, [x0, #7]
    // 0x859c34: ldur            x1, [fp, #-0x10]
    // 0x859c38: StoreField: r0->field_b = r1
    //     0x859c38: stur            w1, [x0, #0xb]
    // 0x859c3c: ldur            x1, [fp, #-0x18]
    // 0x859c40: ArrayStore: r0[0] = r1  ; List_4
    //     0x859c40: stur            w1, [x0, #0x17]
    // 0x859c44: ldur            x1, [fp, #-0x28]
    // 0x859c48: StoreField: r0->field_1b = r1
    //     0x859c48: stur            w1, [x0, #0x1b]
    // 0x859c4c: LeaveFrame
    //     0x859c4c: mov             SP, fp
    //     0x859c50: ldp             fp, lr, [SP], #0x10
    // 0x859c54: ret
    //     0x859c54: ret             
    // 0x859c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x859c58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x859c5c: b               #0x8598dc
  }
  _ _CSSBoxRenderer(/* No info */) {
    // ** addr: 0xab8820, size: 0x140
    // 0xab8820: EnterFrame
    //     0xab8820: stp             fp, lr, [SP, #-0x10]!
    //     0xab8824: mov             fp, SP
    // 0xab8828: r4 = false
    //     0xab8828: add             x4, NULL, #0x30  ; false
    // 0xab882c: mov             x0, x6
    // 0xab8830: mov             x16, x7
    // 0xab8834: mov             x7, x1
    // 0xab8838: mov             x1, x16
    // 0xab883c: mov             x16, x5
    // 0xab8840: mov             x5, x2
    // 0xab8844: mov             x2, x16
    // 0xab8848: StoreField: r7->field_f = r0
    //     0xab8848: stur            w0, [x7, #0xf]
    //     0xab884c: ldurb           w16, [x7, #-1]
    //     0xab8850: ldurb           w17, [x0, #-1]
    //     0xab8854: and             x16, x17, x16, lsr #2
    //     0xab8858: tst             x16, HEAP, lsr #32
    //     0xab885c: b.eq            #0xab8864
    //     0xab8860: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab8864: ldr             x0, [fp, #0x28]
    // 0xab8868: StoreField: r7->field_13 = r0
    //     0xab8868: stur            w0, [x7, #0x13]
    //     0xab886c: ldurb           w16, [x7, #-1]
    //     0xab8870: ldurb           w17, [x0, #-1]
    //     0xab8874: and             x16, x17, x16, lsr #2
    //     0xab8878: tst             x16, HEAP, lsr #32
    //     0xab887c: b.eq            #0xab8884
    //     0xab8880: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab8884: ldr             x0, [fp, #0x10]
    // 0xab8888: ArrayStore: r7[0] = r0  ; List_4
    //     0xab8888: stur            w0, [x7, #0x17]
    //     0xab888c: ldurb           w16, [x7, #-1]
    //     0xab8890: ldurb           w17, [x0, #-1]
    //     0xab8894: and             x16, x17, x16, lsr #2
    //     0xab8898: tst             x16, HEAP, lsr #32
    //     0xab889c: b.eq            #0xab88a4
    //     0xab88a0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab88a4: mov             x0, x1
    // 0xab88a8: StoreField: r7->field_1b = r0
    //     0xab88a8: stur            w0, [x7, #0x1b]
    //     0xab88ac: ldurb           w16, [x7, #-1]
    //     0xab88b0: ldurb           w17, [x0, #-1]
    //     0xab88b4: and             x16, x17, x16, lsr #2
    //     0xab88b8: tst             x16, HEAP, lsr #32
    //     0xab88bc: b.eq            #0xab88c4
    //     0xab88c0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab88c4: mov             x0, x5
    // 0xab88c8: StoreField: r7->field_1f = r0
    //     0xab88c8: stur            w0, [x7, #0x1f]
    //     0xab88cc: ldurb           w16, [x7, #-1]
    //     0xab88d0: ldurb           w17, [x0, #-1]
    //     0xab88d4: and             x16, x17, x16, lsr #2
    //     0xab88d8: tst             x16, HEAP, lsr #32
    //     0xab88dc: b.eq            #0xab88e4
    //     0xab88e0: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab88e4: ldr             x0, [fp, #0x20]
    // 0xab88e8: StoreField: r7->field_23 = r0
    //     0xab88e8: stur            w0, [x7, #0x23]
    //     0xab88ec: ldurb           w16, [x7, #-1]
    //     0xab88f0: ldurb           w17, [x0, #-1]
    //     0xab88f4: and             x16, x17, x16, lsr #2
    //     0xab88f8: tst             x16, HEAP, lsr #32
    //     0xab88fc: b.eq            #0xab8904
    //     0xab8900: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab8904: ldr             x0, [fp, #0x18]
    // 0xab8908: StoreField: r7->field_27 = r0
    //     0xab8908: stur            w0, [x7, #0x27]
    //     0xab890c: ldurb           w16, [x7, #-1]
    //     0xab8910: ldurb           w17, [x0, #-1]
    //     0xab8914: and             x16, x17, x16, lsr #2
    //     0xab8918: tst             x16, HEAP, lsr #32
    //     0xab891c: b.eq            #0xab8924
    //     0xab8920: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab8924: StoreField: r7->field_2b = r3
    //     0xab8924: stur            w3, [x7, #0x2b]
    // 0xab8928: StoreField: r7->field_2f = d0
    //     0xab8928: stur            d0, [x7, #0x2f]
    // 0xab892c: StoreField: r7->field_37 = r4
    //     0xab892c: stur            w4, [x7, #0x37]
    // 0xab8930: mov             x0, x2
    // 0xab8934: StoreField: r7->field_b = r0
    //     0xab8934: stur            w0, [x7, #0xb]
    //     0xab8938: ldurb           w16, [x7, #-1]
    //     0xab893c: ldurb           w17, [x0, #-1]
    //     0xab8940: and             x16, x17, x16, lsr #2
    //     0xab8944: tst             x16, HEAP, lsr #32
    //     0xab8948: b.eq            #0xab8950
    //     0xab894c: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xab8950: r0 = Null
    //     0xab8950: mov             x0, NULL
    // 0xab8954: LeaveFrame
    //     0xab8954: mov             SP, fp
    //     0xab8958: ldp             fp, lr, [SP], #0x10
    // 0xab895c: ret
    //     0xab895c: ret             
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xc6c2a8, size: 0x15c
    // 0xc6c2a8: EnterFrame
    //     0xc6c2a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c2ac: mov             fp, SP
    // 0xc6c2b0: AllocStack(0x20)
    //     0xc6c2b0: sub             SP, SP, #0x20
    // 0xc6c2b4: SetupParameters(_CSSBoxRenderer this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xc6c2b4: mov             x4, x1
    //     0xc6c2b8: stur            x1, [fp, #-8]
    //     0xc6c2bc: stur            x3, [fp, #-0x10]
    // 0xc6c2c0: CheckStackOverflow
    //     0xc6c2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c2c4: cmp             SP, x16
    //     0xc6c2c8: b.ls            #0xc6c3fc
    // 0xc6c2cc: mov             x0, x3
    // 0xc6c2d0: r2 = Null
    //     0xc6c2d0: mov             x2, NULL
    // 0xc6c2d4: r1 = Null
    //     0xc6c2d4: mov             x1, NULL
    // 0xc6c2d8: r4 = 60
    //     0xc6c2d8: movz            x4, #0x3c
    // 0xc6c2dc: branchIfSmi(r0, 0xc6c2e8)
    //     0xc6c2dc: tbz             w0, #0, #0xc6c2e8
    // 0xc6c2e0: r4 = LoadClassIdInstr(r0)
    //     0xc6c2e0: ldur            x4, [x0, #-1]
    //     0xc6c2e4: ubfx            x4, x4, #0xc, #0x14
    // 0xc6c2e8: cmp             x4, #0xbc1
    // 0xc6c2ec: b.eq            #0xc6c304
    // 0xc6c2f0: r8 = RenderCSSBox
    //     0xc6c2f0: add             x8, PP, #0x5b, lsl #12  ; [pp+0x5b308] Type: RenderCSSBox
    //     0xc6c2f4: ldr             x8, [x8, #0x308]
    // 0xc6c2f8: r3 = Null
    //     0xc6c2f8: add             x3, PP, #0x5b, lsl #12  ; [pp+0x5b310] Null
    //     0xc6c2fc: ldr             x3, [x3, #0x310]
    // 0xc6c300: r0 = DefaultTypeTest()
    //     0xc6c300: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc6c304: ldur            x0, [fp, #-8]
    // 0xc6c308: LoadField: r2 = r0->field_f
    //     0xc6c308: ldur            w2, [x0, #0xf]
    // 0xc6c30c: DecompressPointer r2
    //     0xc6c30c: add             x2, x2, HEAP, lsl #32
    // 0xc6c310: ldur            x1, [fp, #-0x10]
    // 0xc6c314: r0 = display=()
    //     0xc6c314: bl              #0xc6c5e0  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::display=
    // 0xc6c318: ldur            x0, [fp, #-8]
    // 0xc6c31c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc6c31c: ldur            w2, [x0, #0x17]
    // 0xc6c320: DecompressPointer r2
    //     0xc6c320: add             x2, x2, HEAP, lsl #32
    // 0xc6c324: stur            x2, [fp, #-0x18]
    // 0xc6c328: LoadField: d1 = r0->field_2f
    //     0xc6c328: ldur            d1, [x0, #0x2f]
    // 0xc6c32c: mov             x1, x2
    // 0xc6c330: mov             v0.16b, v1.16b
    // 0xc6c334: stur            d1, [fp, #-0x20]
    // 0xc6c338: r0 = Normalize.normalize()
    //     0xc6c338: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0xc6c33c: ldur            x1, [fp, #-0x10]
    // 0xc6c340: ldur            x2, [fp, #-0x18]
    // 0xc6c344: r0 = width=()
    //     0xc6c344: bl              #0xc6c590  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::width=
    // 0xc6c348: ldur            x0, [fp, #-8]
    // 0xc6c34c: LoadField: r2 = r0->field_1b
    //     0xc6c34c: ldur            w2, [x0, #0x1b]
    // 0xc6c350: DecompressPointer r2
    //     0xc6c350: add             x2, x2, HEAP, lsl #32
    // 0xc6c354: mov             x1, x2
    // 0xc6c358: ldur            d0, [fp, #-0x20]
    // 0xc6c35c: stur            x2, [fp, #-0x18]
    // 0xc6c360: r0 = Normalize.normalize()
    //     0xc6c360: bl              #0x859c60  ; [package:flutter_html/src/css_box_widget.dart] ::Normalize.normalize
    // 0xc6c364: ldur            x1, [fp, #-0x10]
    // 0xc6c368: ldur            x2, [fp, #-0x18]
    // 0xc6c36c: r0 = height=()
    //     0xc6c36c: bl              #0xc6c540  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::height=
    // 0xc6c370: ldur            x0, [fp, #-8]
    // 0xc6c374: LoadField: r2 = r0->field_13
    //     0xc6c374: ldur            w2, [x0, #0x13]
    // 0xc6c378: DecompressPointer r2
    //     0xc6c378: add             x2, x2, HEAP, lsl #32
    // 0xc6c37c: mov             x1, x0
    // 0xc6c380: r0 = _preProcessMargins()
    //     0xc6c380: bl              #0x8598bc  ; [package:flutter_html/src/css_box_widget.dart] _CSSBoxRenderer::_preProcessMargins
    // 0xc6c384: ldur            x1, [fp, #-0x10]
    // 0xc6c388: mov             x2, x0
    // 0xc6c38c: r0 = margins=()
    //     0xc6c38c: bl              #0xc6c4f0  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::margins=
    // 0xc6c390: ldur            x0, [fp, #-8]
    // 0xc6c394: LoadField: r2 = r0->field_1f
    //     0xc6c394: ldur            w2, [x0, #0x1f]
    // 0xc6c398: DecompressPointer r2
    //     0xc6c398: add             x2, x2, HEAP, lsl #32
    // 0xc6c39c: ldur            x1, [fp, #-0x10]
    // 0xc6c3a0: r0 = borderSize=()
    //     0xc6c3a0: bl              #0xc6c4c0  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::borderSize=
    // 0xc6c3a4: ldur            x0, [fp, #-8]
    // 0xc6c3a8: LoadField: r2 = r0->field_23
    //     0xc6c3a8: ldur            w2, [x0, #0x23]
    // 0xc6c3ac: DecompressPointer r2
    //     0xc6c3ac: add             x2, x2, HEAP, lsl #32
    // 0xc6c3b0: ldur            x1, [fp, #-0x10]
    // 0xc6c3b4: r0 = borderSize=()
    //     0xc6c3b4: bl              #0xc6c4c0  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::borderSize=
    // 0xc6c3b8: ldur            x0, [fp, #-8]
    // 0xc6c3bc: LoadField: r2 = r0->field_27
    //     0xc6c3bc: ldur            w2, [x0, #0x27]
    // 0xc6c3c0: DecompressPointer r2
    //     0xc6c3c0: add             x2, x2, HEAP, lsl #32
    // 0xc6c3c4: ldur            x1, [fp, #-0x10]
    // 0xc6c3c8: r0 = textDirection=()
    //     0xc6c3c8: bl              #0xc6c470  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::textDirection=
    // 0xc6c3cc: ldur            x0, [fp, #-8]
    // 0xc6c3d0: LoadField: r2 = r0->field_2b
    //     0xc6c3d0: ldur            w2, [x0, #0x2b]
    // 0xc6c3d4: DecompressPointer r2
    //     0xc6c3d4: add             x2, x2, HEAP, lsl #32
    // 0xc6c3d8: ldur            x1, [fp, #-0x10]
    // 0xc6c3dc: r0 = childIsReplaced=()
    //     0xc6c3dc: bl              #0xc6c43c  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::childIsReplaced=
    // 0xc6c3e0: ldur            x1, [fp, #-0x10]
    // 0xc6c3e4: r2 = false
    //     0xc6c3e4: add             x2, NULL, #0x30  ; false
    // 0xc6c3e8: r0 = shrinkWrap=()
    //     0xc6c3e8: bl              #0xc6c404  ; [package:flutter_html/src/css_box_widget.dart] RenderCSSBox::shrinkWrap=
    // 0xc6c3ec: r0 = Null
    //     0xc6c3ec: mov             x0, NULL
    // 0xc6c3f0: LeaveFrame
    //     0xc6c3f0: mov             SP, fp
    //     0xc6c3f4: ldp             fp, lr, [SP], #0x10
    // 0xc6c3f8: ret
    //     0xc6c3f8: ret             
    // 0xc6c3fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c3fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c400: b               #0xc6c2cc
  }
}

// class id: 5314, size: 0x24, field offset: 0xc
//   const constructor, 
class CssBoxWidget extends StatelessWidget {

  _ CssBoxWidget.withInlineSpanChildren(/* No info */) {
    // ** addr: 0xa23eec, size: 0x1ac
    // 0xa23eec: EnterFrame
    //     0xa23eec: stp             fp, lr, [SP, #-0x10]!
    //     0xa23ef0: mov             fp, SP
    // 0xa23ef4: AllocStack(0x10)
    //     0xa23ef4: sub             SP, SP, #0x10
    // 0xa23ef8: SetupParameters(CssBoxWidget this /* r1 => r3, fp-0x10 */, dynamic _ /* r3 => r1 */, {dynamic childIsReplaced = false /* r6 */, dynamic key = Null /* r7, fp-0x8 */, dynamic top = false /* r5 */})
    //     0xa23ef8: stur            x1, [fp, #-0x10]
    //     0xa23efc: mov             x16, x3
    //     0xa23f00: mov             x3, x1
    //     0xa23f04: mov             x1, x16
    //     0xa23f08: ldur            w0, [x4, #0x13]
    //     0xa23f0c: ldur            w5, [x4, #0x1f]
    //     0xa23f10: add             x5, x5, HEAP, lsl #32
    //     0xa23f14: add             x16, PP, #0x51, lsl #12  ; [pp+0x51fe8] "childIsReplaced"
    //     0xa23f18: ldr             x16, [x16, #0xfe8]
    //     0xa23f1c: cmp             w5, w16
    //     0xa23f20: b.ne            #0xa23f44
    //     0xa23f24: ldur            w5, [x4, #0x23]
    //     0xa23f28: add             x5, x5, HEAP, lsl #32
    //     0xa23f2c: sub             w6, w0, w5
    //     0xa23f30: add             x5, fp, w6, sxtw #2
    //     0xa23f34: ldr             x5, [x5, #8]
    //     0xa23f38: mov             x6, x5
    //     0xa23f3c: movz            x5, #0x1
    //     0xa23f40: b               #0xa23f4c
    //     0xa23f44: add             x6, NULL, #0x30  ; false
    //     0xa23f48: movz            x5, #0
    //     0xa23f4c: lsl             x7, x5, #1
    //     0xa23f50: lsl             w8, w7, #1
    //     0xa23f54: add             w9, w8, #8
    //     0xa23f58: add             x16, x4, w9, sxtw #1
    //     0xa23f5c: ldur            w10, [x16, #0xf]
    //     0xa23f60: add             x10, x10, HEAP, lsl #32
    //     0xa23f64: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0xa23f68: cmp             w10, w16
    //     0xa23f6c: b.ne            #0xa23fa0
    //     0xa23f70: add             w5, w8, #0xa
    //     0xa23f74: add             x16, x4, w5, sxtw #1
    //     0xa23f78: ldur            w8, [x16, #0xf]
    //     0xa23f7c: add             x8, x8, HEAP, lsl #32
    //     0xa23f80: sub             w5, w0, w8
    //     0xa23f84: add             x8, fp, w5, sxtw #2
    //     0xa23f88: ldr             x8, [x8, #8]
    //     0xa23f8c: add             w5, w7, #2
    //     0xa23f90: sbfx            x7, x5, #1, #0x1f
    //     0xa23f94: mov             x5, x7
    //     0xa23f98: mov             x7, x8
    //     0xa23f9c: b               #0xa23fa4
    //     0xa23fa0: mov             x7, NULL
    //     0xa23fa4: stur            x7, [fp, #-8]
    //     0xa23fa8: lsl             x8, x5, #1
    //     0xa23fac: lsl             w5, w8, #1
    //     0xa23fb0: add             w8, w5, #8
    //     0xa23fb4: add             x16, x4, w8, sxtw #1
    //     0xa23fb8: ldur            w9, [x16, #0xf]
    //     0xa23fbc: add             x9, x9, HEAP, lsl #32
    //     0xa23fc0: ldr             x16, [PP, #0x7048]  ; [pp+0x7048] "top"
    //     0xa23fc4: cmp             w9, w16
    //     0xa23fc8: b.ne            #0xa23ff0
    //     0xa23fcc: add             w8, w5, #0xa
    //     0xa23fd0: add             x16, x4, w8, sxtw #1
    //     0xa23fd4: ldur            w5, [x16, #0xf]
    //     0xa23fd8: add             x5, x5, HEAP, lsl #32
    //     0xa23fdc: sub             w4, w0, w5
    //     0xa23fe0: add             x0, fp, w4, sxtw #2
    //     0xa23fe4: ldr             x0, [x0, #8]
    //     0xa23fe8: mov             x5, x0
    //     0xa23fec: b               #0xa23ff4
    //     0xa23ff0: add             x5, NULL, #0x30  ; false
    //     0xa23ff4: add             x4, NULL, #0x30  ; false
    // 0xa23ff4: r4 = false
    // 0xa23ff8: CheckStackOverflow
    //     0xa23ff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa23ffc: cmp             SP, x16
    //     0xa24000: b.ls            #0xa24090
    // 0xa24004: mov             x0, x1
    // 0xa24008: StoreField: r3->field_f = r0
    //     0xa24008: stur            w0, [x3, #0xf]
    //     0xa2400c: ldurb           w16, [x3, #-1]
    //     0xa24010: ldurb           w17, [x0, #-1]
    //     0xa24014: and             x16, x17, x16, lsr #2
    //     0xa24018: tst             x16, HEAP, lsr #32
    //     0xa2401c: b.eq            #0xa24024
    //     0xa24020: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa24024: ArrayStore: r3[0] = r6  ; List_4
    //     0xa24024: stur            w6, [x3, #0x17]
    // 0xa24028: StoreField: r3->field_1b = r4
    //     0xa24028: stur            w4, [x3, #0x1b]
    // 0xa2402c: StoreField: r3->field_1f = r5
    //     0xa2402c: stur            w5, [x3, #0x1f]
    // 0xa24030: mov             x16, x1
    // 0xa24034: mov             x1, x2
    // 0xa24038: mov             x2, x16
    // 0xa2403c: r0 = _generateWidgetChild()
    //     0xa2403c: bl              #0xa24098  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::_generateWidgetChild
    // 0xa24040: ldur            x1, [fp, #-0x10]
    // 0xa24044: StoreField: r1->field_b = r0
    //     0xa24044: stur            w0, [x1, #0xb]
    //     0xa24048: ldurb           w16, [x1, #-1]
    //     0xa2404c: ldurb           w17, [x0, #-1]
    //     0xa24050: and             x16, x17, x16, lsr #2
    //     0xa24054: tst             x16, HEAP, lsr #32
    //     0xa24058: b.eq            #0xa24060
    //     0xa2405c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa24060: ldur            x0, [fp, #-8]
    // 0xa24064: StoreField: r1->field_7 = r0
    //     0xa24064: stur            w0, [x1, #7]
    //     0xa24068: ldurb           w16, [x1, #-1]
    //     0xa2406c: ldurb           w17, [x0, #-1]
    //     0xa24070: and             x16, x17, x16, lsr #2
    //     0xa24074: tst             x16, HEAP, lsr #32
    //     0xa24078: b.eq            #0xa24080
    //     0xa2407c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa24080: r0 = Null
    //     0xa24080: mov             x0, NULL
    // 0xa24084: LeaveFrame
    //     0xa24084: mov             SP, fp
    //     0xa24088: ldp             fp, lr, [SP], #0x10
    // 0xa2408c: ret
    //     0xa2408c: ret             
    // 0xa24090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa24090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa24094: b               #0xa24004
  }
  static _ _generateWidgetChild(/* No info */) {
    // ** addr: 0xa24098, size: 0x128
    // 0xa24098: EnterFrame
    //     0xa24098: stp             fp, lr, [SP, #-0x10]!
    //     0xa2409c: mov             fp, SP
    // 0xa240a0: AllocStack(0x20)
    //     0xa240a0: sub             SP, SP, #0x20
    // 0xa240a4: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xa240a4: mov             x0, x2
    //     0xa240a8: stur            x2, [fp, #-0x18]
    //     0xa240ac: mov             x2, x1
    //     0xa240b0: stur            x1, [fp, #-0x10]
    // 0xa240b4: CheckStackOverflow
    //     0xa240b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa240b8: cmp             SP, x16
    //     0xa240bc: b.ls            #0xa241b8
    // 0xa240c0: LoadField: r1 = r2->field_b
    //     0xa240c0: ldur            w1, [x2, #0xb]
    // 0xa240c4: cbnz            w1, #0xa240ec
    // 0xa240c8: r0 = Container()
    //     0xa240c8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa240cc: mov             x1, x0
    // 0xa240d0: stur            x0, [fp, #-8]
    // 0xa240d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa240d4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa240d8: r0 = Container()
    //     0xa240d8: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa240dc: ldur            x0, [fp, #-8]
    // 0xa240e0: LeaveFrame
    //     0xa240e0: mov             SP, fp
    //     0xa240e4: ldp             fp, lr, [SP], #0x10
    // 0xa240e8: ret
    //     0xa240e8: ret             
    // 0xa240ec: LoadField: r1 = r0->field_47
    //     0xa240ec: ldur            w1, [x0, #0x47]
    // 0xa240f0: DecompressPointer r1
    //     0xa240f0: add             x1, x1, HEAP, lsl #32
    // 0xa240f4: r16 = Instance_ListStylePosition
    //     0xa240f4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51ff0] Obj!ListStylePosition@e33061
    //     0xa240f8: ldr             x16, [x16, #0xff0]
    // 0xa240fc: cmp             w1, w16
    // 0xa24100: b.ne            #0xa24124
    // 0xa24104: mov             x1, x0
    // 0xa24108: r0 = _generateMarkerBoxSpan()
    //     0xa24108: bl              #0xa2440c  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::_generateMarkerBoxSpan
    // 0xa2410c: cmp             w0, NULL
    // 0xa24110: b.eq            #0xa24124
    // 0xa24114: ldur            x1, [fp, #-0x10]
    // 0xa24118: mov             x3, x0
    // 0xa2411c: r2 = 0
    //     0xa2411c: movz            x2, #0
    // 0xa24120: r0 = insert()
    //     0xa24120: bl              #0x6e39fc  ; [dart:core] _GrowableList::insert
    // 0xa24124: ldur            x2, [fp, #-0x10]
    // 0xa24128: ldur            x0, [fp, #-0x18]
    // 0xa2412c: mov             x1, x0
    // 0xa24130: r0 = generateTextStyle()
    //     0xa24130: bl              #0xa241c0  ; [package:flutter_html/src/style.dart] Style::generateTextStyle
    // 0xa24134: stur            x0, [fp, #-8]
    // 0xa24138: r0 = TextSpan()
    //     0xa24138: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xa2413c: mov             x1, x0
    // 0xa24140: ldur            x0, [fp, #-0x10]
    // 0xa24144: stur            x1, [fp, #-0x20]
    // 0xa24148: StoreField: r1->field_f = r0
    //     0xa24148: stur            w0, [x1, #0xf]
    // 0xa2414c: r0 = Instance__DeferringMouseCursor
    //     0xa2414c: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xa24150: ArrayStore: r1[0] = r0  ; List_4
    //     0xa24150: stur            w0, [x1, #0x17]
    // 0xa24154: ldur            x0, [fp, #-8]
    // 0xa24158: StoreField: r1->field_7 = r0
    //     0xa24158: stur            w0, [x1, #7]
    // 0xa2415c: ldur            x0, [fp, #-0x18]
    // 0xa24160: LoadField: r2 = r0->field_57
    //     0xa24160: ldur            w2, [x0, #0x57]
    // 0xa24164: DecompressPointer r2
    //     0xa24164: add             x2, x2, HEAP, lsl #32
    // 0xa24168: cmp             w2, NULL
    // 0xa2416c: b.ne            #0xa24174
    // 0xa24170: r2 = Instance_TextAlign
    //     0xa24170: ldr             x2, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xa24174: stur            x2, [fp, #-0x10]
    // 0xa24178: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa24178: ldur            w3, [x0, #0x17]
    // 0xa2417c: DecompressPointer r3
    //     0xa2417c: add             x3, x3, HEAP, lsl #32
    // 0xa24180: stur            x3, [fp, #-8]
    // 0xa24184: r0 = Text()
    //     0xa24184: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa24188: ldur            x1, [fp, #-0x20]
    // 0xa2418c: StoreField: r0->field_f = r1
    //     0xa2418c: stur            w1, [x0, #0xf]
    // 0xa24190: ldur            x1, [fp, #-0x10]
    // 0xa24194: StoreField: r0->field_1b = r1
    //     0xa24194: stur            w1, [x0, #0x1b]
    // 0xa24198: ldur            x1, [fp, #-8]
    // 0xa2419c: StoreField: r0->field_1f = r1
    //     0xa2419c: stur            w1, [x0, #0x1f]
    // 0xa241a0: r1 = Instance_TextOverflow
    //     0xa241a0: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xa241a4: ldr             x1, [x1, #0xc60]
    // 0xa241a8: StoreField: r0->field_2b = r1
    //     0xa241a8: stur            w1, [x0, #0x2b]
    // 0xa241ac: LeaveFrame
    //     0xa241ac: mov             SP, fp
    //     0xa241b0: ldp             fp, lr, [SP], #0x10
    // 0xa241b4: ret
    //     0xa241b4: ret             
    // 0xa241b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa241b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa241bc: b               #0xa240c0
  }
  static _ _generateMarkerBoxSpan(/* No info */) {
    // ** addr: 0xa2440c, size: 0x1bc
    // 0xa2440c: EnterFrame
    //     0xa2440c: stp             fp, lr, [SP, #-0x10]!
    //     0xa24410: mov             fp, SP
    // 0xa24414: AllocStack(0x20)
    //     0xa24414: sub             SP, SP, #0x20
    // 0xa24418: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xa24418: stur            x1, [fp, #-8]
    // 0xa2441c: CheckStackOverflow
    //     0xa2441c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa24420: cmp             SP, x16
    //     0xa24424: b.ls            #0xa245b8
    // 0xa24428: r1 = 1
    //     0xa24428: movz            x1, #0x1
    // 0xa2442c: r0 = AllocateContext()
    //     0xa2442c: bl              #0xec126c  ; AllocateContextStub
    // 0xa24430: mov             x1, x0
    // 0xa24434: ldur            x0, [fp, #-8]
    // 0xa24438: stur            x1, [fp, #-0x18]
    // 0xa2443c: StoreField: r1->field_f = r0
    //     0xa2443c: stur            w0, [x1, #0xf]
    // 0xa24440: LoadField: r2 = r0->field_1b
    //     0xa24440: ldur            w2, [x0, #0x1b]
    // 0xa24444: DecompressPointer r2
    //     0xa24444: add             x2, x2, HEAP, lsl #32
    // 0xa24448: r16 = Instance_Display
    //     0xa24448: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b70] Obj!Display@e337e1
    //     0xa2444c: ldr             x16, [x16, #0xb70]
    // 0xa24450: cmp             w2, w16
    // 0xa24454: b.ne            #0xa245a8
    // 0xa24458: LoadField: r2 = r0->field_3f
    //     0xa24458: ldur            w2, [x0, #0x3f]
    // 0xa2445c: DecompressPointer r2
    //     0xa2445c: add             x2, x2, HEAP, lsl #32
    // 0xa24460: cmp             w2, NULL
    // 0xa24464: b.eq            #0xa244d0
    // 0xa24468: LoadField: r0 = r2->field_7
    //     0xa24468: ldur            w0, [x2, #7]
    // 0xa2446c: DecompressPointer r0
    //     0xa2446c: add             x0, x0, HEAP, lsl #32
    // 0xa24470: stur            x0, [fp, #-0x10]
    // 0xa24474: r0 = Image()
    //     0xa24474: bl              #0x92219c  ; AllocateImageStub -> Image (size=0x58)
    // 0xa24478: ldur            x2, [fp, #-0x18]
    // 0xa2447c: r1 = Function '<anonymous closure>': static.
    //     0xa2447c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ff8] AnonymousClosure: static (0xa24888), in [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::_generateMarkerBoxSpan (0xa2440c)
    //     0xa24480: ldr             x1, [x1, #0xff8]
    // 0xa24484: stur            x0, [fp, #-0x18]
    // 0xa24488: r0 = AllocateClosure()
    //     0xa24488: bl              #0xec1630  ; AllocateClosureStub
    // 0xa2448c: str             x0, [SP]
    // 0xa24490: ldur            x1, [fp, #-0x18]
    // 0xa24494: ldur            x2, [fp, #-0x10]
    // 0xa24498: r4 = const [0, 0x3, 0x1, 0x2, errorBuilder, 0x2, null]
    //     0xa24498: add             x4, PP, #0x52, lsl #12  ; [pp+0x52000] List(7) [0, 0x3, 0x1, 0x2, "errorBuilder", 0x2, Null]
    //     0xa2449c: ldr             x4, [x4]
    // 0xa244a0: r0 = Image.network()
    //     0xa244a0: bl              #0xa245d4  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0xa244a4: r0 = WidgetSpan()
    //     0xa244a4: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xa244a8: mov             x1, x0
    // 0xa244ac: ldur            x0, [fp, #-0x18]
    // 0xa244b0: StoreField: r1->field_13 = r0
    //     0xa244b0: stur            w0, [x1, #0x13]
    // 0xa244b4: r0 = Instance_PlaceholderAlignment
    //     0xa244b4: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4edf0] Obj!PlaceholderAlignment@e391a1
    //     0xa244b8: ldr             x0, [x0, #0xdf0]
    // 0xa244bc: StoreField: r1->field_b = r0
    //     0xa244bc: stur            w0, [x1, #0xb]
    // 0xa244c0: mov             x0, x1
    // 0xa244c4: LeaveFrame
    //     0xa244c4: mov             SP, fp
    //     0xa244c8: ldp             fp, lr, [SP], #0x10
    // 0xa244cc: ret
    //     0xa244cc: ret             
    // 0xa244d0: LoadField: r1 = r0->field_4b
    //     0xa244d0: ldur            w1, [x0, #0x4b]
    // 0xa244d4: DecompressPointer r1
    //     0xa244d4: add             x1, x1, HEAP, lsl #32
    // 0xa244d8: cmp             w1, NULL
    // 0xa244dc: b.ne            #0xa244e8
    // 0xa244e0: r0 = Null
    //     0xa244e0: mov             x0, NULL
    // 0xa244e4: b               #0xa24520
    // 0xa244e8: LoadField: r0 = r1->field_7
    //     0xa244e8: ldur            w0, [x1, #7]
    // 0xa244ec: DecompressPointer r0
    //     0xa244ec: add             x0, x0, HEAP, lsl #32
    // 0xa244f0: LoadField: r2 = r0->field_7
    //     0xa244f0: ldur            w2, [x0, #7]
    // 0xa244f4: DecompressPointer r2
    //     0xa244f4: add             x2, x2, HEAP, lsl #32
    // 0xa244f8: cmp             w2, NULL
    // 0xa244fc: b.ne            #0xa24508
    // 0xa24500: r0 = Null
    //     0xa24500: mov             x0, NULL
    // 0xa24504: b               #0xa24520
    // 0xa24508: LoadField: r0 = r2->field_7
    //     0xa24508: ldur            w0, [x2, #7]
    // 0xa2450c: cbnz            w0, #0xa24518
    // 0xa24510: r2 = false
    //     0xa24510: add             x2, NULL, #0x30  ; false
    // 0xa24514: b               #0xa2451c
    // 0xa24518: r2 = true
    //     0xa24518: add             x2, NULL, #0x20  ; true
    // 0xa2451c: mov             x0, x2
    // 0xa24520: cmp             w0, NULL
    // 0xa24524: b.eq            #0xa245a8
    // 0xa24528: tbnz            w0, #4, #0xa245a8
    // 0xa2452c: cmp             w1, NULL
    // 0xa24530: b.eq            #0xa245c0
    // 0xa24534: LoadField: r0 = r1->field_7
    //     0xa24534: ldur            w0, [x1, #7]
    // 0xa24538: DecompressPointer r0
    //     0xa24538: add             x0, x0, HEAP, lsl #32
    // 0xa2453c: LoadField: r2 = r0->field_7
    //     0xa2453c: ldur            w2, [x0, #7]
    // 0xa24540: DecompressPointer r2
    //     0xa24540: add             x2, x2, HEAP, lsl #32
    // 0xa24544: stur            x2, [fp, #-8]
    // 0xa24548: cmp             w2, NULL
    // 0xa2454c: b.eq            #0xa245c4
    // 0xa24550: LoadField: r0 = r1->field_b
    //     0xa24550: ldur            w0, [x1, #0xb]
    // 0xa24554: DecompressPointer r0
    //     0xa24554: add             x0, x0, HEAP, lsl #32
    // 0xa24558: cmp             w0, NULL
    // 0xa2455c: b.ne            #0xa2456c
    // 0xa24560: mov             x0, x2
    // 0xa24564: r1 = Null
    //     0xa24564: mov             x1, NULL
    // 0xa24568: b               #0xa2457c
    // 0xa2456c: mov             x1, x0
    // 0xa24570: r0 = generateTextStyle()
    //     0xa24570: bl              #0xa241c0  ; [package:flutter_html/src/style.dart] Style::generateTextStyle
    // 0xa24574: mov             x1, x0
    // 0xa24578: ldur            x0, [fp, #-8]
    // 0xa2457c: stur            x1, [fp, #-0x10]
    // 0xa24580: r0 = TextSpan()
    //     0xa24580: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xa24584: ldur            x1, [fp, #-8]
    // 0xa24588: StoreField: r0->field_b = r1
    //     0xa24588: stur            w1, [x0, #0xb]
    // 0xa2458c: r1 = Instance__DeferringMouseCursor
    //     0xa2458c: ldr             x1, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xa24590: ArrayStore: r0[0] = r1  ; List_4
    //     0xa24590: stur            w1, [x0, #0x17]
    // 0xa24594: ldur            x1, [fp, #-0x10]
    // 0xa24598: StoreField: r0->field_7 = r1
    //     0xa24598: stur            w1, [x0, #7]
    // 0xa2459c: LeaveFrame
    //     0xa2459c: mov             SP, fp
    //     0xa245a0: ldp             fp, lr, [SP], #0x10
    // 0xa245a4: ret
    //     0xa245a4: ret             
    // 0xa245a8: r0 = Null
    //     0xa245a8: mov             x0, NULL
    // 0xa245ac: LeaveFrame
    //     0xa245ac: mov             SP, fp
    //     0xa245b0: ldp             fp, lr, [SP], #0x10
    // 0xa245b4: ret
    //     0xa245b4: ret             
    // 0xa245b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa245b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa245bc: b               #0xa24428
    // 0xa245c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa245c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa245c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa245c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static StatelessWidget <anonymous closure>(dynamic, BuildContext, Object, StackTrace?) {
    // ** addr: 0xa24888, size: 0x154
    // 0xa24888: EnterFrame
    //     0xa24888: stp             fp, lr, [SP, #-0x10]!
    //     0xa2488c: mov             fp, SP
    // 0xa24890: AllocStack(0x18)
    //     0xa24890: sub             SP, SP, #0x18
    // 0xa24894: SetupParameters()
    //     0xa24894: ldr             x0, [fp, #0x28]
    //     0xa24898: ldur            w1, [x0, #0x17]
    //     0xa2489c: add             x1, x1, HEAP, lsl #32
    // 0xa248a0: CheckStackOverflow
    //     0xa248a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa248a4: cmp             SP, x16
    //     0xa248a8: b.ls            #0xa249cc
    // 0xa248ac: LoadField: r0 = r1->field_f
    //     0xa248ac: ldur            w0, [x1, #0xf]
    // 0xa248b0: DecompressPointer r0
    //     0xa248b0: add             x0, x0, HEAP, lsl #32
    // 0xa248b4: LoadField: r1 = r0->field_4b
    //     0xa248b4: ldur            w1, [x0, #0x4b]
    // 0xa248b8: DecompressPointer r1
    //     0xa248b8: add             x1, x1, HEAP, lsl #32
    // 0xa248bc: cmp             w1, NULL
    // 0xa248c0: b.ne            #0xa248cc
    // 0xa248c4: r0 = Null
    //     0xa248c4: mov             x0, NULL
    // 0xa248c8: b               #0xa24904
    // 0xa248cc: LoadField: r0 = r1->field_7
    //     0xa248cc: ldur            w0, [x1, #7]
    // 0xa248d0: DecompressPointer r0
    //     0xa248d0: add             x0, x0, HEAP, lsl #32
    // 0xa248d4: LoadField: r2 = r0->field_7
    //     0xa248d4: ldur            w2, [x0, #7]
    // 0xa248d8: DecompressPointer r2
    //     0xa248d8: add             x2, x2, HEAP, lsl #32
    // 0xa248dc: cmp             w2, NULL
    // 0xa248e0: b.ne            #0xa248ec
    // 0xa248e4: r0 = Null
    //     0xa248e4: mov             x0, NULL
    // 0xa248e8: b               #0xa24904
    // 0xa248ec: LoadField: r0 = r2->field_7
    //     0xa248ec: ldur            w0, [x2, #7]
    // 0xa248f0: cbnz            w0, #0xa248fc
    // 0xa248f4: r2 = false
    //     0xa248f4: add             x2, NULL, #0x30  ; false
    // 0xa248f8: b               #0xa24900
    // 0xa248fc: r2 = true
    //     0xa248fc: add             x2, NULL, #0x20  ; true
    // 0xa24900: mov             x0, x2
    // 0xa24904: cmp             w0, NULL
    // 0xa24908: b.eq            #0xa249a8
    // 0xa2490c: tbnz            w0, #4, #0xa249a8
    // 0xa24910: cmp             w1, NULL
    // 0xa24914: b.eq            #0xa249d4
    // 0xa24918: LoadField: r0 = r1->field_7
    //     0xa24918: ldur            w0, [x1, #7]
    // 0xa2491c: DecompressPointer r0
    //     0xa2491c: add             x0, x0, HEAP, lsl #32
    // 0xa24920: LoadField: r2 = r0->field_7
    //     0xa24920: ldur            w2, [x0, #7]
    // 0xa24924: DecompressPointer r2
    //     0xa24924: add             x2, x2, HEAP, lsl #32
    // 0xa24928: stur            x2, [fp, #-8]
    // 0xa2492c: cmp             w2, NULL
    // 0xa24930: b.eq            #0xa249d8
    // 0xa24934: LoadField: r0 = r1->field_b
    //     0xa24934: ldur            w0, [x1, #0xb]
    // 0xa24938: DecompressPointer r0
    //     0xa24938: add             x0, x0, HEAP, lsl #32
    // 0xa2493c: cmp             w0, NULL
    // 0xa24940: b.ne            #0xa24950
    // 0xa24944: mov             x0, x2
    // 0xa24948: r1 = Null
    //     0xa24948: mov             x1, NULL
    // 0xa2494c: b               #0xa24960
    // 0xa24950: mov             x1, x0
    // 0xa24954: r0 = generateTextStyle()
    //     0xa24954: bl              #0xa241c0  ; [package:flutter_html/src/style.dart] Style::generateTextStyle
    // 0xa24958: mov             x1, x0
    // 0xa2495c: ldur            x0, [fp, #-8]
    // 0xa24960: stur            x1, [fp, #-0x10]
    // 0xa24964: r0 = TextSpan()
    //     0xa24964: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xa24968: mov             x1, x0
    // 0xa2496c: ldur            x0, [fp, #-8]
    // 0xa24970: stur            x1, [fp, #-0x18]
    // 0xa24974: StoreField: r1->field_b = r0
    //     0xa24974: stur            w0, [x1, #0xb]
    // 0xa24978: r0 = Instance__DeferringMouseCursor
    //     0xa24978: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xa2497c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa2497c: stur            w0, [x1, #0x17]
    // 0xa24980: ldur            x0, [fp, #-0x10]
    // 0xa24984: StoreField: r1->field_7 = r0
    //     0xa24984: stur            w0, [x1, #7]
    // 0xa24988: r0 = Text()
    //     0xa24988: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa2498c: mov             x1, x0
    // 0xa24990: ldur            x0, [fp, #-0x18]
    // 0xa24994: StoreField: r1->field_f = r0
    //     0xa24994: stur            w0, [x1, #0xf]
    // 0xa24998: mov             x0, x1
    // 0xa2499c: LeaveFrame
    //     0xa2499c: mov             SP, fp
    //     0xa249a0: ldp             fp, lr, [SP], #0x10
    // 0xa249a4: ret
    //     0xa249a4: ret             
    // 0xa249a8: r0 = Container()
    //     0xa249a8: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa249ac: mov             x1, x0
    // 0xa249b0: stur            x0, [fp, #-8]
    // 0xa249b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa249b4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa249b8: r0 = Container()
    //     0xa249b8: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa249bc: ldur            x0, [fp, #-8]
    // 0xa249c0: LeaveFrame
    //     0xa249c0: mov             SP, fp
    //     0xa249c4: ldp             fp, lr, [SP], #0x10
    // 0xa249c8: ret
    //     0xa249c8: ret             
    // 0xa249cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa249cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa249d0: b               #0xa248ac
    // 0xa249d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa249d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa249d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa249d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xab837c, size: 0x4a4
    // 0xab837c: EnterFrame
    //     0xab837c: stp             fp, lr, [SP, #-0x10]!
    //     0xab8380: mov             fp, SP
    // 0xab8384: AllocStack(0xb0)
    //     0xab8384: sub             SP, SP, #0xb0
    // 0xab8388: SetupParameters(CssBoxWidget this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xab8388: mov             x0, x2
    //     0xab838c: stur            x2, [fp, #-0x18]
    //     0xab8390: mov             x2, x1
    //     0xab8394: stur            x1, [fp, #-0x10]
    // 0xab8398: CheckStackOverflow
    //     0xab8398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab839c: cmp             SP, x16
    //     0xab83a0: b.ls            #0xab8814
    // 0xab83a4: LoadField: r3 = r2->field_f
    //     0xab83a4: ldur            w3, [x2, #0xf]
    // 0xab83a8: DecompressPointer r3
    //     0xab83a8: add             x3, x3, HEAP, lsl #32
    // 0xab83ac: stur            x3, [fp, #-8]
    // 0xab83b0: LoadField: r1 = r3->field_47
    //     0xab83b0: ldur            w1, [x3, #0x47]
    // 0xab83b4: DecompressPointer r1
    //     0xab83b4: add             x1, x1, HEAP, lsl #32
    // 0xab83b8: r16 = Instance_ListStylePosition
    //     0xab83b8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53928] Obj!ListStylePosition@e33041
    //     0xab83bc: ldr             x16, [x16, #0x928]
    // 0xab83c0: cmp             w1, w16
    // 0xab83c4: b.ne            #0xab83d8
    // 0xab83c8: mov             x1, x3
    // 0xab83cc: r0 = _generateMarkerBoxSpan()
    //     0xab83cc: bl              #0xa2440c  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::_generateMarkerBoxSpan
    // 0xab83d0: mov             x2, x0
    // 0xab83d4: b               #0xab83dc
    // 0xab83d8: r2 = Null
    //     0xab83d8: mov             x2, NULL
    // 0xab83dc: ldur            x0, [fp, #-8]
    // 0xab83e0: ldur            x1, [fp, #-0x18]
    // 0xab83e4: stur            x2, [fp, #-0x20]
    // 0xab83e8: r0 = maybeOf()
    //     0xab83e8: bl              #0x856504  ; [package:flutter/src/widgets/basic.dart] Directionality::maybeOf
    // 0xab83ec: stur            x0, [fp, #-0x28]
    // 0xab83f0: cmp             w0, NULL
    // 0xab83f4: b.eq            #0xab881c
    // 0xab83f8: ldur            x3, [fp, #-8]
    // 0xab83fc: LoadField: r1 = r3->field_53
    //     0xab83fc: ldur            w1, [x3, #0x53]
    // 0xab8400: DecompressPointer r1
    //     0xab8400: add             x1, x1, HEAP, lsl #32
    // 0xab8404: cmp             w1, NULL
    // 0xab8408: b.ne            #0xab8418
    // 0xab840c: mov             x1, x3
    // 0xab8410: r0 = Null
    //     0xab8410: mov             x0, NULL
    // 0xab8414: b               #0xab8424
    // 0xab8418: mov             x2, x0
    // 0xab841c: r0 = resolve()
    //     0xab841c: bl              #0xab8b2c  ; [package:flutter_html/src/style/padding.dart] HtmlPaddings::resolve
    // 0xab8420: ldur            x1, [fp, #-8]
    // 0xab8424: stur            x0, [fp, #-0x30]
    // 0xab8428: LoadField: r2 = r1->field_77
    //     0xab8428: ldur            w2, [x1, #0x77]
    // 0xab842c: DecompressPointer r2
    //     0xab842c: add             x2, x2, HEAP, lsl #32
    // 0xab8430: cmp             w2, NULL
    // 0xab8434: b.ne            #0xab8454
    // 0xab8438: r0 = Width()
    //     0xab8438: bl              #0x9b8758  ; AllocateWidthStub -> Width (size=0x14)
    // 0xab843c: StoreField: r0->field_7 = rZR
    //     0xab843c: stur            xzr, [x0, #7]
    // 0xab8440: r1 = Instance_Unit
    //     0xab8440: add             x1, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0xab8444: ldr             x1, [x1, #0x950]
    // 0xab8448: StoreField: r0->field_f = r1
    //     0xab8448: stur            w1, [x0, #0xf]
    // 0xab844c: mov             x2, x0
    // 0xab8450: b               #0xab845c
    // 0xab8454: r1 = Instance_Unit
    //     0xab8454: add             x1, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0xab8458: ldr             x1, [x1, #0x950]
    // 0xab845c: ldur            x0, [fp, #-8]
    // 0xab8460: stur            x2, [fp, #-0x38]
    // 0xab8464: LoadField: r3 = r0->field_37
    //     0xab8464: ldur            w3, [x0, #0x37]
    // 0xab8468: DecompressPointer r3
    //     0xab8468: add             x3, x3, HEAP, lsl #32
    // 0xab846c: cmp             w3, NULL
    // 0xab8470: b.ne            #0xab8490
    // 0xab8474: r0 = Height()
    //     0xab8474: bl              #0x9b874c  ; AllocateHeightStub -> Height (size=0x14)
    // 0xab8478: StoreField: r0->field_7 = rZR
    //     0xab8478: stur            xzr, [x0, #7]
    // 0xab847c: r1 = Instance_Unit
    //     0xab847c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42950] Obj!Unit@e32e01
    //     0xab8480: ldr             x1, [x1, #0x950]
    // 0xab8484: StoreField: r0->field_f = r1
    //     0xab8484: stur            w1, [x0, #0xf]
    // 0xab8488: mov             x7, x0
    // 0xab848c: b               #0xab8494
    // 0xab8490: mov             x7, x3
    // 0xab8494: ldur            x0, [fp, #-0x30]
    // 0xab8498: stur            x7, [fp, #-0x40]
    // 0xab849c: cmp             w0, NULL
    // 0xab84a0: b.ne            #0xab84ac
    // 0xab84a4: r0 = Null
    //     0xab84a4: mov             x0, NULL
    // 0xab84a8: b               #0xab84d8
    // 0xab84ac: mov             x1, x0
    // 0xab84b0: r0 = horizontal()
    //     0xab84b0: bl              #0x72e19c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::horizontal
    // 0xab84b4: ldur            x1, [fp, #-0x30]
    // 0xab84b8: stur            d0, [fp, #-0x88]
    // 0xab84bc: r0 = vertical()
    //     0xab84bc: bl              #0x72e244  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0xab84c0: stur            d0, [fp, #-0x90]
    // 0xab84c4: r0 = Size()
    //     0xab84c4: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0xab84c8: ldur            d0, [fp, #-0x88]
    // 0xab84cc: StoreField: r0->field_7 = d0
    //     0xab84cc: stur            d0, [x0, #7]
    // 0xab84d0: ldur            d0, [fp, #-0x90]
    // 0xab84d4: StoreField: r0->field_f = d0
    //     0xab84d4: stur            d0, [x0, #0xf]
    // 0xab84d8: cmp             w0, NULL
    // 0xab84dc: b.ne            #0xab84ec
    // 0xab84e0: r2 = Instance_Size
    //     0xab84e0: add             x2, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0xab84e4: ldr             x2, [x2, #0xa20]
    // 0xab84e8: b               #0xab84f0
    // 0xab84ec: mov             x2, x0
    // 0xab84f0: ldur            x0, [fp, #-8]
    // 0xab84f4: stur            x2, [fp, #-0x48]
    // 0xab84f8: LoadField: r1 = r0->field_8b
    //     0xab84f8: ldur            w1, [x0, #0x8b]
    // 0xab84fc: DecompressPointer r1
    //     0xab84fc: add             x1, x1, HEAP, lsl #32
    // 0xab8500: cmp             w1, NULL
    // 0xab8504: b.ne            #0xab8510
    // 0xab8508: r0 = Null
    //     0xab8508: mov             x0, NULL
    // 0xab850c: b               #0xab8544
    // 0xab8510: r0 = dimensions()
    //     0xab8510: bl              #0xd83b44  ; [package:flutter/src/painting/box_border.dart] Border::dimensions
    // 0xab8514: mov             x1, x0
    // 0xab8518: stur            x0, [fp, #-0x50]
    // 0xab851c: r0 = horizontal()
    //     0xab851c: bl              #0x72e19c  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::horizontal
    // 0xab8520: ldur            x1, [fp, #-0x50]
    // 0xab8524: stur            d0, [fp, #-0x88]
    // 0xab8528: r0 = vertical()
    //     0xab8528: bl              #0x72e244  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0xab852c: stur            d0, [fp, #-0x90]
    // 0xab8530: r0 = Size()
    //     0xab8530: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0xab8534: ldur            d0, [fp, #-0x88]
    // 0xab8538: StoreField: r0->field_7 = d0
    //     0xab8538: stur            d0, [x0, #7]
    // 0xab853c: ldur            d0, [fp, #-0x90]
    // 0xab8540: StoreField: r0->field_f = d0
    //     0xab8540: stur            d0, [x0, #0xf]
    // 0xab8544: cmp             w0, NULL
    // 0xab8548: b.ne            #0xab8558
    // 0xab854c: r2 = Instance_Size
    //     0xab854c: add             x2, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0xab8550: ldr             x2, [x2, #0xa20]
    // 0xab8554: b               #0xab855c
    // 0xab8558: mov             x2, x0
    // 0xab855c: ldur            x1, [fp, #-8]
    // 0xab8560: stur            x2, [fp, #-0x50]
    // 0xab8564: LoadField: r0 = r1->field_4f
    //     0xab8564: ldur            w0, [x1, #0x4f]
    // 0xab8568: DecompressPointer r0
    //     0xab8568: add             x0, x0, HEAP, lsl #32
    // 0xab856c: cmp             w0, NULL
    // 0xab8570: b.ne            #0xab8590
    // 0xab8574: r0 = Margins()
    //     0xab8574: bl              #0x7582d4  ; AllocateMarginsStub -> Margins (size=0x28)
    // 0xab8578: mov             x1, x0
    // 0xab857c: d0 = 0.000000
    //     0xab857c: eor             v0.16b, v0.16b, v0.16b
    // 0xab8580: stur            x0, [fp, #-0x58]
    // 0xab8584: r0 = Margins.all()
    //     0xab8584: bl              #0x9aa758  ; [package:flutter_html/src/style/margin.dart] Margins::Margins.all
    // 0xab8588: ldur            x3, [fp, #-0x58]
    // 0xab858c: b               #0xab8594
    // 0xab8590: mov             x3, x0
    // 0xab8594: ldur            x0, [fp, #-8]
    // 0xab8598: stur            x3, [fp, #-0x68]
    // 0xab859c: LoadField: r1 = r0->field_1b
    //     0xab859c: ldur            w1, [x0, #0x1b]
    // 0xab85a0: DecompressPointer r1
    //     0xab85a0: add             x1, x1, HEAP, lsl #32
    // 0xab85a4: cmp             w1, NULL
    // 0xab85a8: b.ne            #0xab85b8
    // 0xab85ac: r6 = Instance_Display
    //     0xab85ac: add             x6, PP, #0x52, lsl #12  ; [pp+0x520b0] Obj!Display@e337c1
    //     0xab85b0: ldr             x6, [x6, #0xb0]
    // 0xab85b4: b               #0xab85bc
    // 0xab85b8: mov             x6, x1
    // 0xab85bc: ldur            x4, [fp, #-0x10]
    // 0xab85c0: stur            x6, [fp, #-0x60]
    // 0xab85c4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xab85c4: ldur            w5, [x4, #0x17]
    // 0xab85c8: DecompressPointer r5
    //     0xab85c8: add             x5, x5, HEAP, lsl #32
    // 0xab85cc: mov             x1, x0
    // 0xab85d0: ldur            x2, [fp, #-0x18]
    // 0xab85d4: stur            x5, [fp, #-0x58]
    // 0xab85d8: r0 = _calculateEmValue()
    //     0xab85d8: bl              #0xab896c  ; [package:flutter_html/src/css_box_widget.dart] ::_calculateEmValue
    // 0xab85dc: ldur            x0, [fp, #-8]
    // 0xab85e0: stur            d0, [fp, #-0x88]
    // 0xab85e4: LoadField: r1 = r0->field_8b
    //     0xab85e4: ldur            w1, [x0, #0x8b]
    // 0xab85e8: DecompressPointer r1
    //     0xab85e8: add             x1, x1, HEAP, lsl #32
    // 0xab85ec: stur            x1, [fp, #-0x78]
    // 0xab85f0: LoadField: r2 = r0->field_7
    //     0xab85f0: ldur            w2, [x0, #7]
    // 0xab85f4: DecompressPointer r2
    //     0xab85f4: add             x2, x2, HEAP, lsl #32
    // 0xab85f8: stur            x2, [fp, #-0x70]
    // 0xab85fc: r0 = BoxDecoration()
    //     0xab85fc: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xab8600: mov             x2, x0
    // 0xab8604: ldur            x0, [fp, #-0x70]
    // 0xab8608: stur            x2, [fp, #-0x80]
    // 0xab860c: StoreField: r2->field_7 = r0
    //     0xab860c: stur            w0, [x2, #7]
    // 0xab8610: ldur            x0, [fp, #-0x78]
    // 0xab8614: StoreField: r2->field_f = r0
    //     0xab8614: stur            w0, [x2, #0xf]
    // 0xab8618: r0 = Instance_BoxShape
    //     0xab8618: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xab861c: ldr             x0, [x0, #0xca8]
    // 0xab8620: StoreField: r2->field_23 = r0
    //     0xab8620: stur            w0, [x2, #0x23]
    // 0xab8624: ldur            x0, [fp, #-8]
    // 0xab8628: LoadField: r1 = r0->field_1b
    //     0xab8628: ldur            w1, [x0, #0x1b]
    // 0xab862c: DecompressPointer r1
    //     0xab862c: add             x1, x1, HEAP, lsl #32
    // 0xab8630: r16 = Instance_Display
    //     0xab8630: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b68] Obj!Display@e33781
    //     0xab8634: ldr             x16, [x16, #0xb68]
    // 0xab8638: cmp             w1, w16
    // 0xab863c: b.eq            #0xab8650
    // 0xab8640: r16 = Instance_Display
    //     0xab8640: add             x16, PP, #0x24, lsl #12  ; [pp+0x24b70] Obj!Display@e337e1
    //     0xab8644: ldr             x16, [x16, #0xb70]
    // 0xab8648: cmp             w1, w16
    // 0xab864c: b.ne            #0xab8660
    // 0xab8650: ldur            x3, [fp, #-0x58]
    // 0xab8654: tbz             w3, #4, #0xab8664
    // 0xab8658: r4 = inf
    //     0xab8658: ldr             x4, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xab865c: b               #0xab8668
    // 0xab8660: ldur            x3, [fp, #-0x58]
    // 0xab8664: r4 = Null
    //     0xab8664: mov             x4, NULL
    // 0xab8668: ldur            x0, [fp, #-0x10]
    // 0xab866c: stur            x4, [fp, #-8]
    // 0xab8670: LoadField: r1 = r0->field_1f
    //     0xab8670: ldur            w1, [x0, #0x1f]
    // 0xab8674: DecompressPointer r1
    //     0xab8674: add             x1, x1, HEAP, lsl #32
    // 0xab8678: tbnz            w1, #4, #0xab8688
    // 0xab867c: LoadField: r1 = r0->field_b
    //     0xab867c: ldur            w1, [x0, #0xb]
    // 0xab8680: DecompressPointer r1
    //     0xab8680: add             x1, x1, HEAP, lsl #32
    // 0xab8684: b               #0xab86e4
    // 0xab8688: ldur            x1, [fp, #-0x18]
    // 0xab868c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab868c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab8690: r0 = _of()
    //     0xab8690: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xab8694: r16 = 1.000000
    //     0xab8694: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xab8698: str             x16, [SP]
    // 0xab869c: mov             x1, x0
    // 0xab86a0: r4 = const [0, 0x2, 0x1, 0x1, textScaleFactor, 0x1, null]
    //     0xab86a0: add             x4, PP, #0x58, lsl #12  ; [pp+0x58830] List(7) [0, 0x2, 0x1, 0x1, "textScaleFactor", 0x1, Null]
    //     0xab86a4: ldr             x4, [x4, #0x830]
    // 0xab86a8: r0 = copyWith()
    //     0xab86a8: bl              #0x9e6f18  ; [package:flutter/src/widgets/media_query.dart] MediaQueryData::copyWith
    // 0xab86ac: mov             x2, x0
    // 0xab86b0: ldur            x0, [fp, #-0x10]
    // 0xab86b4: stur            x2, [fp, #-0x70]
    // 0xab86b8: LoadField: r3 = r0->field_b
    //     0xab86b8: ldur            w3, [x0, #0xb]
    // 0xab86bc: DecompressPointer r3
    //     0xab86bc: add             x3, x3, HEAP, lsl #32
    // 0xab86c0: stur            x3, [fp, #-0x18]
    // 0xab86c4: r1 = <_MediaQueryAspect>
    //     0xab86c4: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d80] TypeArguments: <_MediaQueryAspect>
    //     0xab86c8: ldr             x1, [x1, #0xd80]
    // 0xab86cc: r0 = MediaQuery()
    //     0xab86cc: bl              #0x9e6f0c  ; AllocateMediaQueryStub -> MediaQuery (size=0x18)
    // 0xab86d0: mov             x1, x0
    // 0xab86d4: ldur            x0, [fp, #-0x70]
    // 0xab86d8: StoreField: r1->field_13 = r0
    //     0xab86d8: stur            w0, [x1, #0x13]
    // 0xab86dc: ldur            x0, [fp, #-0x18]
    // 0xab86e0: StoreField: r1->field_b = r0
    //     0xab86e0: stur            w0, [x1, #0xb]
    // 0xab86e4: ldur            x0, [fp, #-0x20]
    // 0xab86e8: stur            x1, [fp, #-0x10]
    // 0xab86ec: r0 = Container()
    //     0xab86ec: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab86f0: stur            x0, [fp, #-0x18]
    // 0xab86f4: ldur            x16, [fp, #-0x80]
    // 0xab86f8: ldur            lr, [fp, #-8]
    // 0xab86fc: stp             lr, x16, [SP, #0x10]
    // 0xab8700: ldur            x16, [fp, #-0x30]
    // 0xab8704: ldur            lr, [fp, #-0x10]
    // 0xab8708: stp             lr, x16, [SP]
    // 0xab870c: mov             x1, x0
    // 0xab8710: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, padding, 0x3, width, 0x2, null]
    //     0xab8710: add             x4, PP, #0x58, lsl #12  ; [pp+0x58838] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xab8714: ldr             x4, [x4, #0x838]
    // 0xab8718: r0 = Container()
    //     0xab8718: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab871c: r1 = Null
    //     0xab871c: mov             x1, NULL
    // 0xab8720: r2 = 2
    //     0xab8720: movz            x2, #0x2
    // 0xab8724: r0 = AllocateArray()
    //     0xab8724: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab8728: mov             x2, x0
    // 0xab872c: ldur            x0, [fp, #-0x18]
    // 0xab8730: stur            x2, [fp, #-8]
    // 0xab8734: StoreField: r2->field_f = r0
    //     0xab8734: stur            w0, [x2, #0xf]
    // 0xab8738: r1 = <Widget>
    //     0xab8738: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xab873c: r0 = AllocateGrowableArray()
    //     0xab873c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xab8740: mov             x1, x0
    // 0xab8744: ldur            x0, [fp, #-8]
    // 0xab8748: stur            x1, [fp, #-0x10]
    // 0xab874c: StoreField: r1->field_f = r0
    //     0xab874c: stur            w0, [x1, #0xf]
    // 0xab8750: r0 = 2
    //     0xab8750: movz            x0, #0x2
    // 0xab8754: StoreField: r1->field_b = r0
    //     0xab8754: stur            w0, [x1, #0xb]
    // 0xab8758: ldur            x0, [fp, #-0x20]
    // 0xab875c: cmp             w0, NULL
    // 0xab8760: b.eq            #0xab87c0
    // 0xab8764: r0 = Text()
    //     0xab8764: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xab8768: mov             x2, x0
    // 0xab876c: ldur            x0, [fp, #-0x20]
    // 0xab8770: stur            x2, [fp, #-8]
    // 0xab8774: StoreField: r2->field_f = r0
    //     0xab8774: stur            w0, [x2, #0xf]
    // 0xab8778: ldur            x1, [fp, #-0x10]
    // 0xab877c: r0 = _growToNextCapacity()
    //     0xab877c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xab8780: ldur            x5, [fp, #-0x10]
    // 0xab8784: r0 = 4
    //     0xab8784: movz            x0, #0x4
    // 0xab8788: StoreField: r5->field_b = r0
    //     0xab8788: stur            w0, [x5, #0xb]
    // 0xab878c: LoadField: r1 = r5->field_f
    //     0xab878c: ldur            w1, [x5, #0xf]
    // 0xab8790: DecompressPointer r1
    //     0xab8790: add             x1, x1, HEAP, lsl #32
    // 0xab8794: ldur            x0, [fp, #-8]
    // 0xab8798: ArrayStore: r1[1] = r0  ; List_4
    //     0xab8798: add             x25, x1, #0x13
    //     0xab879c: str             w0, [x25]
    //     0xab87a0: tbz             w0, #0, #0xab87bc
    //     0xab87a4: ldurb           w16, [x1, #-1]
    //     0xab87a8: ldurb           w17, [x0, #-1]
    //     0xab87ac: and             x16, x17, x16, lsr #2
    //     0xab87b0: tst             x16, HEAP, lsr #32
    //     0xab87b4: b.eq            #0xab87bc
    //     0xab87b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xab87bc: b               #0xab87c4
    // 0xab87c0: mov             x5, x1
    // 0xab87c4: r0 = _CSSBoxRenderer()
    //     0xab87c4: bl              #0xab8960  ; Allocate_CSSBoxRendererStub -> _CSSBoxRenderer (size=0x3c)
    // 0xab87c8: stur            x0, [fp, #-8]
    // 0xab87cc: ldur            x16, [fp, #-0x68]
    // 0xab87d0: ldur            lr, [fp, #-0x48]
    // 0xab87d4: stp             lr, x16, [SP, #0x10]
    // 0xab87d8: ldur            x16, [fp, #-0x28]
    // 0xab87dc: ldur            lr, [fp, #-0x38]
    // 0xab87e0: stp             lr, x16, [SP]
    // 0xab87e4: mov             x1, x0
    // 0xab87e8: ldur            x2, [fp, #-0x50]
    // 0xab87ec: ldur            x3, [fp, #-0x58]
    // 0xab87f0: ldur            x5, [fp, #-0x10]
    // 0xab87f4: ldur            x6, [fp, #-0x60]
    // 0xab87f8: ldur            d0, [fp, #-0x88]
    // 0xab87fc: ldur            x7, [fp, #-0x40]
    // 0xab8800: r0 = _CSSBoxRenderer()
    //     0xab8800: bl              #0xab8820  ; [package:flutter_html/src/css_box_widget.dart] _CSSBoxRenderer::_CSSBoxRenderer
    // 0xab8804: ldur            x0, [fp, #-8]
    // 0xab8808: LeaveFrame
    //     0xab8808: mov             SP, fp
    //     0xab880c: ldp             fp, lr, [SP], #0x10
    // 0xab8810: ret
    //     0xab8810: ret             
    // 0xab8814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab8814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab8818: b               #0xab83a4
    // 0xab881c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab881c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
