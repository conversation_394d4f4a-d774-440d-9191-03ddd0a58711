// lib: , url: package:flutter_layout_grid/src/foundation/collections.dart

// class id: 1049439, size: 0x8
class :: {

  static Y0 sum<Y0 extends num>(Iterable<Y0>) {
    // ** addr: 0x7355b8, size: 0xb8
    // 0x7355b8: EnterFrame
    //     0x7355b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7355bc: mov             fp, SP
    // 0x7355c0: AllocStack(0x30)
    //     0x7355c0: sub             SP, SP, #0x30
    // 0x7355c4: SetupParameters()
    //     0x7355c4: ldur            w0, [x4, #0xf]
    //     0x7355c8: cbnz            w0, #0x7355d4
    //     0x7355cc: mov             x1, NULL
    //     0x7355d0: b               #0x7355e4
    //     0x7355d4: ldur            w1, [x4, #0x17]
    //     0x7355d8: add             x2, fp, w1, sxtw #2
    //     0x7355dc: ldr             x2, [x2, #0x10]
    //     0x7355e0: mov             x1, x2
    // 0x7355e4: CheckStackOverflow
    //     0x7355e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7355e8: cmp             SP, x16
    //     0x7355ec: b.ls            #0x735668
    // 0x7355f0: cbnz            w0, #0x7355f8
    // 0x7355f4: r1 = <num>
    //     0x7355f4: ldr             x1, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x7355f8: ldr             x0, [fp, #0x10]
    // 0x7355fc: stur            x1, [fp, #-8]
    // 0x735600: str             x1, [SP]
    // 0x735604: r4 = const [0x1, 0, 0, 0, null]
    //     0x735604: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0x735608: r0 = zeroForType()
    //     0x735608: bl              #0x735670  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::zeroForType
    // 0x73560c: r1 = Function '<anonymous closure>': static.
    //     0x73560c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c360] AnonymousClosure: static (0x735758), in [package:flutter_layout_grid/src/foundation/collections.dart] ::sum (0x7355b8)
    //     0x735610: ldr             x1, [x1, #0x360]
    // 0x735614: r2 = Null
    //     0x735614: mov             x2, NULL
    // 0x735618: stur            x0, [fp, #-0x10]
    // 0x73561c: r0 = AllocateClosure()
    //     0x73561c: bl              #0xec1630  ; AllocateClosureStub
    // 0x735620: mov             x1, x0
    // 0x735624: ldur            x0, [fp, #-8]
    // 0x735628: StoreField: r1->field_b = r0
    //     0x735628: stur            w0, [x1, #0xb]
    // 0x73562c: ldr             x2, [fp, #0x10]
    // 0x735630: r3 = LoadClassIdInstr(r2)
    //     0x735630: ldur            x3, [x2, #-1]
    //     0x735634: ubfx            x3, x3, #0xc, #0x14
    // 0x735638: stp             x2, x0, [SP, #0x10]
    // 0x73563c: ldur            x16, [fp, #-0x10]
    // 0x735640: stp             x1, x16, [SP]
    // 0x735644: mov             x0, x3
    // 0x735648: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x735648: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x73564c: r0 = GDT[cid_x0 + 0xed1f]()
    //     0x73564c: movz            x17, #0xed1f
    //     0x735650: add             lr, x0, x17
    //     0x735654: ldr             lr, [x21, lr, lsl #3]
    //     0x735658: blr             lr
    // 0x73565c: LeaveFrame
    //     0x73565c: mov             SP, fp
    //     0x735660: ldp             fp, lr, [SP], #0x10
    // 0x735664: ret
    //     0x735664: ret             
    // 0x735668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735668: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73566c: b               #0x7355f0
  }
  static Y0 zeroForType<Y0 extends num>() {
    // ** addr: 0x735670, size: 0xe8
    // 0x735670: EnterFrame
    //     0x735670: stp             fp, lr, [SP, #-0x10]!
    //     0x735674: mov             fp, SP
    // 0x735678: AllocStack(0x20)
    //     0x735678: sub             SP, SP, #0x20
    // 0x73567c: SetupParameters()
    //     0x73567c: ldur            w0, [x4, #0xf]
    //     0x735680: cbnz            w0, #0x73568c
    //     0x735684: mov             x1, NULL
    //     0x735688: b               #0x73569c
    //     0x73568c: ldur            w1, [x4, #0x17]
    //     0x735690: add             x2, fp, w1, sxtw #2
    //     0x735694: ldr             x2, [x2, #0x10]
    //     0x735698: mov             x1, x2
    // 0x73569c: CheckStackOverflow
    //     0x73569c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7356a0: cmp             SP, x16
    //     0x7356a4: b.ls            #0x735750
    // 0x7356a8: cbnz            w0, #0x7356b4
    // 0x7356ac: r0 = <num>
    //     0x7356ac: ldr             x0, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x7356b0: b               #0x7356b8
    // 0x7356b4: mov             x0, x1
    // 0x7356b8: mov             x1, x0
    // 0x7356bc: stur            x0, [fp, #-8]
    // 0x7356c0: r2 = Null
    //     0x7356c0: mov             x2, NULL
    // 0x7356c4: r3 = Y0 bound num
    //     0x7356c4: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c380] TypeParameter: Y0 bound num
    //     0x7356c8: ldr             x3, [x3, #0x380]
    // 0x7356cc: r30 = InstantiateTypeNonNullableFunctionTypeParameterStub
    //     0x7356cc: ldr             lr, [PP, #0x24e0]  ; [pp+0x24e0] Stub: InstantiateTypeNonNullableFunctionTypeParameter (0x5e10cc)
    // 0x7356d0: LoadField: r30 = r30->field_7
    //     0x7356d0: ldur            lr, [lr, #7]
    // 0x7356d4: blr             lr
    // 0x7356d8: r1 = LoadClassIdInstr(r0)
    //     0x7356d8: ldur            x1, [x0, #-1]
    //     0x7356dc: ubfx            x1, x1, #0xc, #0x14
    // 0x7356e0: r16 = int
    //     0x7356e0: ldr             x16, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7356e4: stp             x16, x0, [SP]
    // 0x7356e8: mov             x0, x1
    // 0x7356ec: mov             lr, x0
    // 0x7356f0: ldr             lr, [x21, lr, lsl #3]
    // 0x7356f4: blr             lr
    // 0x7356f8: tbnz            w0, #4, #0x735704
    // 0x7356fc: r3 = 0
    //     0x7356fc: movz            x3, #0
    // 0x735700: b               #0x735708
    // 0x735704: r3 = 0.000000
    //     0x735704: ldr             x3, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x735708: mov             x0, x3
    // 0x73570c: ldur            x1, [fp, #-8]
    // 0x735710: stur            x3, [fp, #-0x10]
    // 0x735714: r2 = Null
    //     0x735714: mov             x2, NULL
    // 0x735718: cmp             w1, NULL
    // 0x73571c: b.eq            #0x735740
    // 0x735720: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x735720: ldur            w4, [x1, #0x17]
    // 0x735724: DecompressPointer r4
    //     0x735724: add             x4, x4, HEAP, lsl #32
    // 0x735728: r8 = Y0 bound num
    //     0x735728: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c380] TypeParameter: Y0 bound num
    //     0x73572c: ldr             x8, [x8, #0x380]
    // 0x735730: LoadField: r9 = r4->field_7
    //     0x735730: ldur            x9, [x4, #7]
    // 0x735734: r3 = Null
    //     0x735734: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c388] Null
    //     0x735738: ldr             x3, [x3, #0x388]
    // 0x73573c: blr             x9
    // 0x735740: ldur            x0, [fp, #-0x10]
    // 0x735744: LeaveFrame
    //     0x735744: mov             SP, fp
    //     0x735748: ldp             fp, lr, [SP], #0x10
    // 0x73574c: ret
    //     0x73574c: ret             
    // 0x735750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735754: b               #0x7356a8
  }
  [closure] static Y0 <anonymous closure>(dynamic, Y0, Y0) {
    // ** addr: 0x735758, size: 0xa4
    // 0x735758: EnterFrame
    //     0x735758: stp             fp, lr, [SP, #-0x10]!
    //     0x73575c: mov             fp, SP
    // 0x735760: AllocStack(0x18)
    //     0x735760: sub             SP, SP, #0x18
    // 0x735764: CheckStackOverflow
    //     0x735764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735768: cmp             SP, x16
    //     0x73576c: b.ls            #0x7357f4
    // 0x735770: ldr             x0, [fp, #0x20]
    // 0x735774: LoadField: r1 = r0->field_b
    //     0x735774: ldur            w1, [x0, #0xb]
    // 0x735778: DecompressPointer r1
    //     0x735778: add             x1, x1, HEAP, lsl #32
    // 0x73577c: ldr             x0, [fp, #0x18]
    // 0x735780: stur            x1, [fp, #-8]
    // 0x735784: r2 = 60
    //     0x735784: movz            x2, #0x3c
    // 0x735788: branchIfSmi(r0, 0x735794)
    //     0x735788: tbz             w0, #0, #0x735794
    // 0x73578c: r2 = LoadClassIdInstr(r0)
    //     0x73578c: ldur            x2, [x0, #-1]
    //     0x735790: ubfx            x2, x2, #0xc, #0x14
    // 0x735794: ldr             x16, [fp, #0x10]
    // 0x735798: stp             x16, x0, [SP]
    // 0x73579c: mov             x0, x2
    // 0x7357a0: r0 = GDT[cid_x0 + -0xff2]()
    //     0x7357a0: sub             lr, x0, #0xff2
    //     0x7357a4: ldr             lr, [x21, lr, lsl #3]
    //     0x7357a8: blr             lr
    // 0x7357ac: ldur            x1, [fp, #-8]
    // 0x7357b0: mov             x3, x0
    // 0x7357b4: r2 = Null
    //     0x7357b4: mov             x2, NULL
    // 0x7357b8: stur            x3, [fp, #-8]
    // 0x7357bc: cmp             w1, NULL
    // 0x7357c0: b.eq            #0x7357e4
    // 0x7357c4: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x7357c4: ldur            w4, [x1, #0x17]
    // 0x7357c8: DecompressPointer r4
    //     0x7357c8: add             x4, x4, HEAP, lsl #32
    // 0x7357cc: r8 = Y0 bound num
    //     0x7357cc: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c368] TypeParameter: Y0 bound num
    //     0x7357d0: ldr             x8, [x8, #0x368]
    // 0x7357d4: LoadField: r9 = r4->field_7
    //     0x7357d4: ldur            x9, [x4, #7]
    // 0x7357d8: r3 = Null
    //     0x7357d8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c370] Null
    //     0x7357dc: ldr             x3, [x3, #0x370]
    // 0x7357e0: blr             x9
    // 0x7357e4: ldur            x0, [fp, #-8]
    // 0x7357e8: LeaveFrame
    //     0x7357e8: mov             SP, fp
    //     0x7357ec: ldp             fp, lr, [SP], #0x10
    // 0x7357f0: ret
    //     0x7357f0: ret             
    // 0x7357f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7357f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7357f8: b               #0x735770
  }
  static _ IterableExt.removeDuplicates(/* No info */) {
    // ** addr: 0x7381dc, size: 0xd4
    // 0x7381dc: EnterFrame
    //     0x7381dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7381e0: mov             fp, SP
    // 0x7381e4: AllocStack(0x30)
    //     0x7381e4: sub             SP, SP, #0x30
    // 0x7381e8: SetupParameters()
    //     0x7381e8: ldur            w0, [x4, #0xf]
    //     0x7381ec: cbnz            w0, #0x7381f8
    //     0x7381f0: mov             x2, NULL
    //     0x7381f4: b               #0x738208
    //     0x7381f8: ldur            w0, [x4, #0x17]
    //     0x7381fc: add             x1, fp, w0, sxtw #2
    //     0x738200: ldr             x1, [x1, #0x10]
    //     0x738204: mov             x2, x1
    //     0x738208: ldr             x1, [fp, #0x10]
    //     0x73820c: add             x0, PP, #0x4c, lsl #12  ; [pp+0x4c540] Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static. (0x7e54fb1382bc)
    //     0x738210: ldr             x0, [x0, #0x540]
    //     0x738214: stur            x2, [fp, #-0x20]
    // 0x73820c: r0 = Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static.
    // 0x738218: CheckStackOverflow
    //     0x738218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73821c: cmp             SP, x16
    //     0x738220: b.ls            #0x7382a8
    // 0x738224: LoadField: r3 = r0->field_13
    //     0x738224: ldur            w3, [x0, #0x13]
    // 0x738228: DecompressPointer r3
    //     0x738228: add             x3, x3, HEAP, lsl #32
    // 0x73822c: stur            x3, [fp, #-0x18]
    // 0x738230: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x738230: ldur            w4, [x0, #0x17]
    // 0x738234: DecompressPointer r4
    //     0x738234: add             x4, x4, HEAP, lsl #32
    // 0x738238: stur            x4, [fp, #-0x10]
    // 0x73823c: LoadField: r5 = r0->field_7
    //     0x73823c: ldur            w5, [x0, #7]
    // 0x738240: DecompressPointer r5
    //     0x738240: add             x5, x5, HEAP, lsl #32
    // 0x738244: stur            x5, [fp, #-8]
    // 0x738248: r16 = Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static.
    //     0x738248: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c540] Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static. (0x7e54fb1382bc)
    //     0x73824c: ldr             x16, [x16, #0x540]
    // 0x738250: stp             x2, x16, [SP]
    // 0x738254: r0 = _boundsCheckForPartialInstantiation()
    //     0x738254: bl              #0x6022c8  ; [dart:_internal] ::_boundsCheckForPartialInstantiation
    // 0x738258: ldur            x1, [fp, #-0x18]
    // 0x73825c: ldur            x2, [fp, #-0x10]
    // 0x738260: ldur            x3, [fp, #-8]
    // 0x738264: r0 = AllocateClosureTA()
    //     0x738264: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x738268: ldur            x1, [fp, #-0x20]
    // 0x73826c: stur            x0, [fp, #-8]
    // 0x738270: StoreField: r0->field_f = r1
    //     0x738270: stur            w1, [x0, #0xf]
    // 0x738274: r2 = Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static.
    //     0x738274: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c540] Closure: <Y0>() => (Y0) => bool from Function '_removeDuplicatesPredicate@1169357453': static. (0x7e54fb1382bc)
    //     0x738278: ldr             x2, [x2, #0x540]
    // 0x73827c: LoadField: r3 = r2->field_b
    //     0x73827c: ldur            w3, [x2, #0xb]
    // 0x738280: DecompressPointer r3
    //     0x738280: add             x3, x3, HEAP, lsl #32
    // 0x738284: StoreField: r0->field_b = r3
    //     0x738284: stur            w3, [x0, #0xb]
    // 0x738288: r0 = _WhereBuilderIterable()
    //     0x738288: bl              #0x7382b0  ; Allocate_WhereBuilderIterableStub -> _WhereBuilderIterable<X0> (size=0x14)
    // 0x73828c: ldr             x1, [fp, #0x10]
    // 0x738290: StoreField: r0->field_b = r1
    //     0x738290: stur            w1, [x0, #0xb]
    // 0x738294: ldur            x1, [fp, #-8]
    // 0x738298: StoreField: r0->field_f = r1
    //     0x738298: stur            w1, [x0, #0xf]
    // 0x73829c: LeaveFrame
    //     0x73829c: mov             SP, fp
    //     0x7382a0: ldp             fp, lr, [SP], #0x10
    // 0x7382a4: ret
    //     0x7382a4: ret             
    // 0x7382a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7382a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7382ac: b               #0x738224
  }
  [closure] static (dynamic, Y0) => bool _removeDuplicatesPredicate<Y0>(dynamic) {
    // ** addr: 0x7382bc, size: 0x78
    // 0x7382bc: EnterFrame
    //     0x7382bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7382c0: mov             fp, SP
    // 0x7382c4: AllocStack(0x8)
    //     0x7382c4: sub             SP, SP, #8
    // 0x7382c8: SetupParameters()
    //     0x7382c8: ldur            w0, [x4, #0xf]
    //     0x7382cc: cbnz            w0, #0x7382d8
    //     0x7382d0: mov             x1, NULL
    //     0x7382d4: b               #0x7382e4
    //     0x7382d8: ldur            w0, [x4, #0x17]
    //     0x7382dc: add             x1, fp, w0, sxtw #2
    //     0x7382e0: ldr             x1, [x1, #0x10]
    //     0x7382e4: ldr             x0, [fp, #0x10]
    //     0x7382e8: ldur            w2, [x0, #0xf]
    //     0x7382ec: add             x2, x2, HEAP, lsl #32
    //     0x7382f0: ldr             x16, [THR, #0x98]  ; THR::empty_type_arguments
    //     0x7382f4: cmp             w2, w16
    //     0x7382f8: b.ne            #0x738304
    //     0x7382fc: mov             x0, x1
    //     0x738300: b               #0x738308
    //     0x738304: mov             x0, x2
    // 0x738308: CheckStackOverflow
    //     0x738308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73830c: cmp             SP, x16
    //     0x738310: b.ls            #0x73832c
    // 0x738314: str             x0, [SP]
    // 0x738318: r4 = const [0x1, 0, 0, 0, null]
    //     0x738318: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0x73831c: r0 = _removeDuplicatesPredicate()
    //     0x73831c: bl              #0x738334  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::_removeDuplicatesPredicate
    // 0x738320: LeaveFrame
    //     0x738320: mov             SP, fp
    //     0x738324: ldp             fp, lr, [SP], #0x10
    // 0x738328: ret
    //     0x738328: ret             
    // 0x73832c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73832c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x738330: b               #0x738314
  }
  static (dynamic, Y0) => bool _removeDuplicatesPredicate<Y0>() {
    // ** addr: 0x738334, size: 0xec
    // 0x738334: EnterFrame
    //     0x738334: stp             fp, lr, [SP, #-0x10]!
    //     0x738338: mov             fp, SP
    // 0x73833c: AllocStack(0x18)
    //     0x73833c: sub             SP, SP, #0x18
    // 0x738340: SetupParameters()
    //     0x738340: ldur            w0, [x4, #0xf]
    //     0x738344: cbnz            w0, #0x738350
    //     0x738348: mov             x1, NULL
    //     0x73834c: b               #0x73835c
    //     0x738350: ldur            w0, [x4, #0x17]
    //     0x738354: add             x1, fp, w0, sxtw #2
    //     0x738358: ldr             x1, [x1, #0x10]
    //     0x73835c: stur            x1, [fp, #-8]
    // 0x738360: CheckStackOverflow
    //     0x738360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x738364: cmp             SP, x16
    //     0x738368: b.ls            #0x738418
    // 0x73836c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0x73836c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x738370: ldr             x0, [x0, #0x778]
    //     0x738374: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x738378: cmp             w0, w16
    //     0x73837c: b.ne            #0x738388
    //     0x738380: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0x738384: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x738388: ldur            x1, [fp, #-8]
    // 0x73838c: stur            x0, [fp, #-0x10]
    // 0x738390: r0 = _Set()
    //     0x738390: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x738394: mov             x1, x0
    // 0x738398: ldur            x0, [fp, #-0x10]
    // 0x73839c: stur            x1, [fp, #-0x18]
    // 0x7383a0: StoreField: r1->field_1b = r0
    //     0x7383a0: stur            w0, [x1, #0x1b]
    // 0x7383a4: StoreField: r1->field_b = rZR
    //     0x7383a4: stur            wzr, [x1, #0xb]
    // 0x7383a8: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0x7383a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7383ac: ldr             x0, [x0, #0x780]
    //     0x7383b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7383b4: cmp             w0, w16
    //     0x7383b8: b.ne            #0x7383c4
    //     0x7383bc: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0x7383c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7383c4: ldur            x2, [fp, #-0x18]
    // 0x7383c8: StoreField: r2->field_f = r0
    //     0x7383c8: stur            w0, [x2, #0xf]
    // 0x7383cc: StoreField: r2->field_13 = rZR
    //     0x7383cc: stur            wzr, [x2, #0x13]
    // 0x7383d0: ArrayStore: r2[0] = rZR  ; List_4
    //     0x7383d0: stur            wzr, [x2, #0x17]
    // 0x7383d4: r1 = Function 'add':.
    //     0x7383d4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c548] AnonymousClosure: (0x69b410), in [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add (0xe548f0)
    //     0x7383d8: ldr             x1, [x1, #0x548]
    // 0x7383dc: r0 = AllocateClosure()
    //     0x7383dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7383e0: ldur            x1, [fp, #-8]
    // 0x7383e4: mov             x3, x0
    // 0x7383e8: r2 = Null
    //     0x7383e8: mov             x2, NULL
    // 0x7383ec: stur            x3, [fp, #-8]
    // 0x7383f0: r8 = (dynamic this, Y0) => bool
    //     0x7383f0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c550] FunctionType: (dynamic this, Y0) => bool
    //     0x7383f4: ldr             x8, [x8, #0x550]
    // 0x7383f8: LoadField: r9 = r8->field_7
    //     0x7383f8: ldur            x9, [x8, #7]
    // 0x7383fc: r3 = Null
    //     0x7383fc: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c558] Null
    //     0x738400: ldr             x3, [x3, #0x558]
    // 0x738404: blr             x9
    // 0x738408: ldur            x0, [fp, #-8]
    // 0x73840c: LeaveFrame
    //     0x73840c: mov             SP, fp
    //     0x738410: ldp             fp, lr, [SP], #0x10
    // 0x738414: ret
    //     0x738414: ret             
    // 0x738418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x738418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73841c: b               #0x73836c
  }
  static Iterable<Y0> cumulativeSum<Y0 extends num>(Iterable<Y0>) {
    // ** addr: 0x77aa70, size: 0x29c
    // 0x77aa70: EnterFrame
    //     0x77aa70: stp             fp, lr, [SP, #-0x10]!
    //     0x77aa74: mov             fp, SP
    // 0x77aa78: AllocStack(0x50)
    //     0x77aa78: sub             SP, SP, #0x50
    // 0x77aa7c: SetupParameters(dynamic _ /* r2, fp-0x18 */)
    //     0x77aa7c: stur            NULL, [fp, #-8]
    //     0x77aa80: movz            x1, #0
    //     0x77aa84: add             x2, fp, w1, sxtw #2
    //     0x77aa88: ldr             x2, [x2, #0x10]
    //     0x77aa8c: stur            x2, [fp, #-0x18]
    // 0x77aa90: LoadField: r0 = r4->field_f
    //     0x77aa90: ldur            w0, [x4, #0xf]
    // 0x77aa94: cbnz            w0, #0x77aaa0
    // 0x77aa98: r3 = Null
    //     0x77aa98: mov             x3, NULL
    // 0x77aa9c: b               #0x77aab0
    // 0x77aaa0: ArrayLoad: r3 = r4[0]  ; List_4
    //     0x77aaa0: ldur            w3, [x4, #0x17]
    // 0x77aaa4: add             x4, fp, w3, sxtw #2
    // 0x77aaa8: ldr             x4, [x4, #0x10]
    // 0x77aaac: mov             x3, x4
    // 0x77aab0: CheckStackOverflow
    //     0x77aab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77aab4: cmp             SP, x16
    //     0x77aab8: b.ls            #0x77acfc
    // 0x77aabc: cbnz            w0, #0x77aac4
    // 0x77aac0: r3 = <num>
    //     0x77aac0: ldr             x3, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0x77aac4: mov             x0, x3
    // 0x77aac8: stur            x3, [fp, #-0x10]
    // 0x77aacc: r0 = InitAsync()
    //     0x77aacc: bl              #0x7348c0  ; InitAsyncStub
    // 0x77aad0: r0 = Null
    //     0x77aad0: mov             x0, NULL
    // 0x77aad4: r0 = SuspendSyncStarAtStart()
    //     0x77aad4: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0x77aad8: ldur            x16, [fp, #-0x10]
    // 0x77aadc: str             x16, [SP]
    // 0x77aae0: r4 = const [0x1, 0, 0, 0, null]
    //     0x77aae0: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0x77aae4: r0 = zeroForType()
    //     0x77aae4: bl              #0x735670  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::zeroForType
    // 0x77aae8: ldur            x1, [fp, #-0x18]
    // 0x77aaec: stur            x0, [fp, #-0x18]
    // 0x77aaf0: r0 = iterator()
    //     0x77aaf0: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x77aaf4: mov             x1, x0
    // 0x77aaf8: stur            x1, [fp, #-0x38]
    // 0x77aafc: LoadField: r2 = r1->field_b
    //     0x77aafc: ldur            w2, [x1, #0xb]
    // 0x77ab00: DecompressPointer r2
    //     0x77ab00: add             x2, x2, HEAP, lsl #32
    // 0x77ab04: stur            x2, [fp, #-0x30]
    // 0x77ab08: LoadField: r3 = r1->field_f
    //     0x77ab08: ldur            x3, [x1, #0xf]
    // 0x77ab0c: stur            x3, [fp, #-0x28]
    // 0x77ab10: LoadField: r4 = r1->field_7
    //     0x77ab10: ldur            w4, [x1, #7]
    // 0x77ab14: DecompressPointer r4
    //     0x77ab14: add             x4, x4, HEAP, lsl #32
    // 0x77ab18: stur            x4, [fp, #-0x20]
    // 0x77ab1c: ldur            x5, [fp, #-0x18]
    // 0x77ab20: stur            x5, [fp, #-0x18]
    // 0x77ab24: CheckStackOverflow
    //     0x77ab24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77ab28: cmp             SP, x16
    //     0x77ab2c: b.ls            #0x77ad04
    // 0x77ab30: r0 = LoadClassIdInstr(r2)
    //     0x77ab30: ldur            x0, [x2, #-1]
    //     0x77ab34: ubfx            x0, x0, #0xc, #0x14
    // 0x77ab38: str             x2, [SP]
    // 0x77ab3c: r0 = GDT[cid_x0 + 0xc834]()
    //     0x77ab3c: movz            x17, #0xc834
    //     0x77ab40: add             lr, x0, x17
    //     0x77ab44: ldr             lr, [x21, lr, lsl #3]
    //     0x77ab48: blr             lr
    // 0x77ab4c: r1 = LoadInt32Instr(r0)
    //     0x77ab4c: sbfx            x1, x0, #1, #0x1f
    //     0x77ab50: tbz             w0, #0, #0x77ab58
    //     0x77ab54: ldur            x1, [x0, #7]
    // 0x77ab58: ldur            x3, [fp, #-0x28]
    // 0x77ab5c: cmp             x3, x1
    // 0x77ab60: b.ne            #0x77acdc
    // 0x77ab64: ldur            x4, [fp, #-0x38]
    // 0x77ab68: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x77ab68: ldur            x2, [x4, #0x17]
    // 0x77ab6c: cmp             x2, x1
    // 0x77ab70: b.ge            #0x77acc4
    // 0x77ab74: ldur            x5, [fp, #-0x30]
    // 0x77ab78: r0 = LoadClassIdInstr(r5)
    //     0x77ab78: ldur            x0, [x5, #-1]
    //     0x77ab7c: ubfx            x0, x0, #0xc, #0x14
    // 0x77ab80: mov             x1, x5
    // 0x77ab84: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x77ab84: movz            x17, #0xd28f
    //     0x77ab88: add             lr, x0, x17
    //     0x77ab8c: ldr             lr, [x21, lr, lsl #3]
    //     0x77ab90: blr             lr
    // 0x77ab94: mov             x4, x0
    // 0x77ab98: ldur            x3, [fp, #-0x38]
    // 0x77ab9c: stur            x4, [fp, #-0x40]
    // 0x77aba0: StoreField: r3->field_1f = r0
    //     0x77aba0: stur            w0, [x3, #0x1f]
    //     0x77aba4: tbz             w0, #0, #0x77abc0
    //     0x77aba8: ldurb           w16, [x3, #-1]
    //     0x77abac: ldurb           w17, [x0, #-1]
    //     0x77abb0: and             x16, x17, x16, lsr #2
    //     0x77abb4: tst             x16, HEAP, lsr #32
    //     0x77abb8: b.eq            #0x77abc0
    //     0x77abbc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x77abc0: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x77abc0: ldur            x0, [x3, #0x17]
    // 0x77abc4: add             x1, x0, #1
    // 0x77abc8: ArrayStore: r3[0] = r1  ; List_8
    //     0x77abc8: stur            x1, [x3, #0x17]
    // 0x77abcc: cmp             w4, NULL
    // 0x77abd0: b.ne            #0x77ac04
    // 0x77abd4: mov             x0, x4
    // 0x77abd8: ldur            x2, [fp, #-0x20]
    // 0x77abdc: r1 = Null
    //     0x77abdc: mov             x1, NULL
    // 0x77abe0: cmp             w2, NULL
    // 0x77abe4: b.eq            #0x77ac04
    // 0x77abe8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x77abe8: ldur            w4, [x2, #0x17]
    // 0x77abec: DecompressPointer r4
    //     0x77abec: add             x4, x4, HEAP, lsl #32
    // 0x77abf0: r8 = X0
    //     0x77abf0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x77abf4: LoadField: r9 = r4->field_7
    //     0x77abf4: ldur            x9, [x4, #7]
    // 0x77abf8: r3 = Null
    //     0x77abf8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c690] Null
    //     0x77abfc: ldr             x3, [x3, #0x690]
    // 0x77ac00: blr             x9
    // 0x77ac04: ldur            x2, [fp, #-0x18]
    // 0x77ac08: r1 = 0
    //     0x77ac08: movz            x1, #0
    // 0x77ac0c: add             x0, fp, w1, sxtw #2
    // 0x77ac10: LoadField: r0 = r0->field_fffffff8
    //     0x77ac10: ldur            x0, [x0, #-8]
    // 0x77ac14: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x77ac14: ldur            w3, [x0, #0x17]
    // 0x77ac18: DecompressPointer r3
    //     0x77ac18: add             x3, x3, HEAP, lsl #32
    // 0x77ac1c: mov             x0, x2
    // 0x77ac20: ArrayStore: r3[0] = r0  ; List_4
    //     0x77ac20: stur            w0, [x3, #0x17]
    //     0x77ac24: tbz             w0, #0, #0x77ac40
    //     0x77ac28: ldurb           w16, [x3, #-1]
    //     0x77ac2c: ldurb           w17, [x0, #-1]
    //     0x77ac30: and             x16, x17, x16, lsr #2
    //     0x77ac34: tst             x16, HEAP, lsr #32
    //     0x77ac38: b.eq            #0x77ac40
    //     0x77ac3c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x77ac40: r0 = true
    //     0x77ac40: add             x0, NULL, #0x20  ; true
    // 0x77ac44: r0 = SuspendSyncStarAtYield()
    //     0x77ac44: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0x77ac48: ldur            x0, [fp, #-0x18]
    // 0x77ac4c: r1 = 60
    //     0x77ac4c: movz            x1, #0x3c
    // 0x77ac50: branchIfSmi(r0, 0x77ac5c)
    //     0x77ac50: tbz             w0, #0, #0x77ac5c
    // 0x77ac54: r1 = LoadClassIdInstr(r0)
    //     0x77ac54: ldur            x1, [x0, #-1]
    //     0x77ac58: ubfx            x1, x1, #0xc, #0x14
    // 0x77ac5c: ldur            x16, [fp, #-0x40]
    // 0x77ac60: stp             x16, x0, [SP]
    // 0x77ac64: mov             x0, x1
    // 0x77ac68: r0 = GDT[cid_x0 + -0xff2]()
    //     0x77ac68: sub             lr, x0, #0xff2
    //     0x77ac6c: ldr             lr, [x21, lr, lsl #3]
    //     0x77ac70: blr             lr
    // 0x77ac74: ldur            x1, [fp, #-0x10]
    // 0x77ac78: mov             x3, x0
    // 0x77ac7c: r2 = Null
    //     0x77ac7c: mov             x2, NULL
    // 0x77ac80: stur            x3, [fp, #-0x18]
    // 0x77ac84: cmp             w1, NULL
    // 0x77ac88: b.eq            #0x77acac
    // 0x77ac8c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x77ac8c: ldur            w4, [x1, #0x17]
    // 0x77ac90: DecompressPointer r4
    //     0x77ac90: add             x4, x4, HEAP, lsl #32
    // 0x77ac94: r8 = Y0 bound num
    //     0x77ac94: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c6a0] TypeParameter: Y0 bound num
    //     0x77ac98: ldr             x8, [x8, #0x6a0]
    // 0x77ac9c: LoadField: r9 = r4->field_7
    //     0x77ac9c: ldur            x9, [x4, #7]
    // 0x77aca0: r3 = Null
    //     0x77aca0: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c6a8] Null
    //     0x77aca4: ldr             x3, [x3, #0x6a8]
    // 0x77aca8: blr             x9
    // 0x77acac: ldur            x5, [fp, #-0x18]
    // 0x77acb0: ldur            x1, [fp, #-0x38]
    // 0x77acb4: ldur            x4, [fp, #-0x20]
    // 0x77acb8: ldur            x2, [fp, #-0x30]
    // 0x77acbc: ldur            x3, [fp, #-0x28]
    // 0x77acc0: b               #0x77ab20
    // 0x77acc4: mov             x0, x4
    // 0x77acc8: StoreField: r0->field_1f = rNULL
    //     0x77acc8: stur            NULL, [x0, #0x1f]
    // 0x77accc: r0 = false
    //     0x77accc: add             x0, NULL, #0x30  ; false
    // 0x77acd0: LeaveFrame
    //     0x77acd0: mov             SP, fp
    //     0x77acd4: ldp             fp, lr, [SP], #0x10
    // 0x77acd8: ret
    //     0x77acd8: ret             
    // 0x77acdc: ldur            x0, [fp, #-0x30]
    // 0x77ace0: r0 = ConcurrentModificationError()
    //     0x77ace0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x77ace4: mov             x1, x0
    // 0x77ace8: ldur            x0, [fp, #-0x30]
    // 0x77acec: StoreField: r1->field_b = r0
    //     0x77acec: stur            w0, [x1, #0xb]
    // 0x77acf0: mov             x0, x1
    // 0x77acf4: r0 = Throw()
    //     0x77acf4: bl              #0xec04b8  ; ThrowStub
    // 0x77acf8: brk             #0
    // 0x77acfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77acfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77ad00: b               #0x77aabc
    // 0x77ad04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77ad04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77ad08: b               #0x77ab30
  }
}

// class id: 7256, size: 0x14, field offset: 0xc
class _WhereBuilderIterable<X0> extends Iterable<X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x8882cc, size: 0x90
    // 0x8882cc: EnterFrame
    //     0x8882cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8882d0: mov             fp, SP
    // 0x8882d4: AllocStack(0x20)
    //     0x8882d4: sub             SP, SP, #0x20
    // 0x8882d8: SetupParameters(_WhereBuilderIterable<X0> this /* r1 => r0, fp-0x10 */)
    //     0x8882d8: mov             x0, x1
    //     0x8882dc: stur            x1, [fp, #-0x10]
    // 0x8882e0: CheckStackOverflow
    //     0x8882e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8882e4: cmp             SP, x16
    //     0x8882e8: b.ls            #0x888354
    // 0x8882ec: LoadField: r2 = r0->field_7
    //     0x8882ec: ldur            w2, [x0, #7]
    // 0x8882f0: DecompressPointer r2
    //     0x8882f0: add             x2, x2, HEAP, lsl #32
    // 0x8882f4: stur            x2, [fp, #-8]
    // 0x8882f8: LoadField: r1 = r0->field_b
    //     0x8882f8: ldur            w1, [x0, #0xb]
    // 0x8882fc: DecompressPointer r1
    //     0x8882fc: add             x1, x1, HEAP, lsl #32
    // 0x888300: r0 = iterator()
    //     0x888300: bl              #0x887ea8  ; [dart:_internal] ExpandIterable::iterator
    // 0x888304: mov             x1, x0
    // 0x888308: ldur            x0, [fp, #-0x10]
    // 0x88830c: stur            x1, [fp, #-0x18]
    // 0x888310: LoadField: r2 = r0->field_f
    //     0x888310: ldur            w2, [x0, #0xf]
    // 0x888314: DecompressPointer r2
    //     0x888314: add             x2, x2, HEAP, lsl #32
    // 0x888318: str             x2, [SP]
    // 0x88831c: mov             x0, x2
    // 0x888320: ClosureCall
    //     0x888320: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x888324: ldur            x2, [x0, #0x1f]
    //     0x888328: blr             x2
    // 0x88832c: ldur            x1, [fp, #-8]
    // 0x888330: stur            x0, [fp, #-8]
    // 0x888334: r0 = _WhereIterator()
    //     0x888334: bl              #0x88835c  ; Allocate_WhereIteratorStub -> _WhereIterator<X0> (size=0x14)
    // 0x888338: ldur            x1, [fp, #-0x18]
    // 0x88833c: StoreField: r0->field_b = r1
    //     0x88833c: stur            w1, [x0, #0xb]
    // 0x888340: ldur            x1, [fp, #-8]
    // 0x888344: StoreField: r0->field_f = r1
    //     0x888344: stur            w1, [x0, #0xf]
    // 0x888348: LeaveFrame
    //     0x888348: mov             SP, fp
    //     0x88834c: ldp             fp, lr, [SP], #0x10
    // 0x888350: ret
    //     0x888350: ret             
    // 0x888354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888358: b               #0x8882ec
  }
}

// class id: 7371, size: 0x14, field offset: 0xc
class _WhereIterator<X0> extends Iterator<X0> {

  get _ current(/* No info */) {
    // ** addr: 0x5f6c24, size: 0x6c
    // 0x5f6c24: EnterFrame
    //     0x5f6c24: stp             fp, lr, [SP, #-0x10]!
    //     0x5f6c28: mov             fp, SP
    // 0x5f6c2c: AllocStack(0x8)
    //     0x5f6c2c: sub             SP, SP, #8
    // 0x5f6c30: LoadField: r0 = r1->field_b
    //     0x5f6c30: ldur            w0, [x1, #0xb]
    // 0x5f6c34: DecompressPointer r0
    //     0x5f6c34: add             x0, x0, HEAP, lsl #32
    // 0x5f6c38: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x5f6c38: ldur            w3, [x0, #0x17]
    // 0x5f6c3c: DecompressPointer r3
    //     0x5f6c3c: add             x3, x3, HEAP, lsl #32
    // 0x5f6c40: stur            x3, [fp, #-8]
    // 0x5f6c44: cmp             w3, NULL
    // 0x5f6c48: b.ne            #0x5f6c80
    // 0x5f6c4c: LoadField: r2 = r0->field_7
    //     0x5f6c4c: ldur            w2, [x0, #7]
    // 0x5f6c50: DecompressPointer r2
    //     0x5f6c50: add             x2, x2, HEAP, lsl #32
    // 0x5f6c54: mov             x0, x3
    // 0x5f6c58: r1 = Null
    //     0x5f6c58: mov             x1, NULL
    // 0x5f6c5c: cmp             w2, NULL
    // 0x5f6c60: b.eq            #0x5f6c80
    // 0x5f6c64: LoadField: r4 = r2->field_1b
    //     0x5f6c64: ldur            w4, [x2, #0x1b]
    // 0x5f6c68: DecompressPointer r4
    //     0x5f6c68: add             x4, x4, HEAP, lsl #32
    // 0x5f6c6c: r8 = X1
    //     0x5f6c6c: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x5f6c70: LoadField: r9 = r4->field_7
    //     0x5f6c70: ldur            x9, [x4, #7]
    // 0x5f6c74: r3 = Null
    //     0x5f6c74: add             x3, PP, #0x58, lsl #12  ; [pp+0x58350] Null
    //     0x5f6c78: ldr             x3, [x3, #0x350]
    // 0x5f6c7c: blr             x9
    // 0x5f6c80: ldur            x0, [fp, #-8]
    // 0x5f6c84: LeaveFrame
    //     0x5f6c84: mov             SP, fp
    //     0x5f6c88: ldp             fp, lr, [SP], #0x10
    // 0x5f6c8c: ret
    //     0x5f6c8c: ret             
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x6740d4, size: 0x280
    // 0x6740d4: EnterFrame
    //     0x6740d4: stp             fp, lr, [SP, #-0x10]!
    //     0x6740d8: mov             fp, SP
    // 0x6740dc: AllocStack(0x40)
    //     0x6740dc: sub             SP, SP, #0x40
    // 0x6740e0: CheckStackOverflow
    //     0x6740e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6740e4: cmp             SP, x16
    //     0x6740e8: b.ls            #0x674338
    // 0x6740ec: LoadField: r2 = r1->field_b
    //     0x6740ec: ldur            w2, [x1, #0xb]
    // 0x6740f0: DecompressPointer r2
    //     0x6740f0: add             x2, x2, HEAP, lsl #32
    // 0x6740f4: stur            x2, [fp, #-0x28]
    // 0x6740f8: LoadField: r3 = r2->field_b
    //     0x6740f8: ldur            w3, [x2, #0xb]
    // 0x6740fc: DecompressPointer r3
    //     0x6740fc: add             x3, x3, HEAP, lsl #32
    // 0x674100: stur            x3, [fp, #-0x20]
    // 0x674104: LoadField: r4 = r2->field_f
    //     0x674104: ldur            w4, [x2, #0xf]
    // 0x674108: DecompressPointer r4
    //     0x674108: add             x4, x4, HEAP, lsl #32
    // 0x67410c: stur            x4, [fp, #-0x18]
    // 0x674110: LoadField: r5 = r1->field_f
    //     0x674110: ldur            w5, [x1, #0xf]
    // 0x674114: DecompressPointer r5
    //     0x674114: add             x5, x5, HEAP, lsl #32
    // 0x674118: stur            x5, [fp, #-0x10]
    // 0x67411c: LoadField: r6 = r2->field_7
    //     0x67411c: ldur            w6, [x2, #7]
    // 0x674120: DecompressPointer r6
    //     0x674120: add             x6, x6, HEAP, lsl #32
    // 0x674124: stur            x6, [fp, #-8]
    // 0x674128: CheckStackOverflow
    //     0x674128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67412c: cmp             SP, x16
    //     0x674130: b.ls            #0x674340
    // 0x674134: LoadField: r0 = r2->field_13
    //     0x674134: ldur            w0, [x2, #0x13]
    // 0x674138: DecompressPointer r0
    //     0x674138: add             x0, x0, HEAP, lsl #32
    // 0x67413c: cmp             w0, NULL
    // 0x674140: b.eq            #0x674328
    // 0x674144: mov             x1, x0
    // 0x674148: CheckStackOverflow
    //     0x674148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67414c: cmp             SP, x16
    //     0x674150: b.ls            #0x674348
    // 0x674154: r0 = LoadClassIdInstr(r1)
    //     0x674154: ldur            x0, [x1, #-1]
    //     0x674158: ubfx            x0, x0, #0xc, #0x14
    // 0x67415c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x67415c: movz            x17, #0x292d
    //     0x674160: movk            x17, #0x1, lsl #16
    //     0x674164: add             lr, x0, x17
    //     0x674168: ldr             lr, [x21, lr, lsl #3]
    //     0x67416c: blr             lr
    // 0x674170: tbz             w0, #4, #0x674244
    // 0x674174: ldur            x2, [fp, #-0x28]
    // 0x674178: ldur            x3, [fp, #-0x20]
    // 0x67417c: ArrayStore: r2[0] = rNULL  ; List_4
    //     0x67417c: stur            NULL, [x2, #0x17]
    // 0x674180: r0 = LoadClassIdInstr(r3)
    //     0x674180: ldur            x0, [x3, #-1]
    //     0x674184: ubfx            x0, x0, #0xc, #0x14
    // 0x674188: mov             x1, x3
    // 0x67418c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x67418c: movz            x17, #0x292d
    //     0x674190: movk            x17, #0x1, lsl #16
    //     0x674194: add             lr, x0, x17
    //     0x674198: ldr             lr, [x21, lr, lsl #3]
    //     0x67419c: blr             lr
    // 0x6741a0: tbnz            w0, #4, #0x674328
    // 0x6741a4: ldur            x2, [fp, #-0x28]
    // 0x6741a8: ldur            x3, [fp, #-0x20]
    // 0x6741ac: StoreField: r2->field_13 = rNULL
    //     0x6741ac: stur            NULL, [x2, #0x13]
    // 0x6741b0: r0 = LoadClassIdInstr(r3)
    //     0x6741b0: ldur            x0, [x3, #-1]
    //     0x6741b4: ubfx            x0, x0, #0xc, #0x14
    // 0x6741b8: mov             x1, x3
    // 0x6741bc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x6741bc: movz            x17, #0x384d
    //     0x6741c0: movk            x17, #0x1, lsl #16
    //     0x6741c4: add             lr, x0, x17
    //     0x6741c8: ldr             lr, [x21, lr, lsl #3]
    //     0x6741cc: blr             lr
    // 0x6741d0: ldur            x16, [fp, #-0x18]
    // 0x6741d4: stp             x0, x16, [SP]
    // 0x6741d8: ldur            x0, [fp, #-0x18]
    // 0x6741dc: ClosureCall
    //     0x6741dc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6741e0: ldur            x2, [x0, #0x1f]
    //     0x6741e4: blr             x2
    // 0x6741e8: r1 = LoadClassIdInstr(r0)
    //     0x6741e8: ldur            x1, [x0, #-1]
    //     0x6741ec: ubfx            x1, x1, #0xc, #0x14
    // 0x6741f0: mov             x16, x0
    // 0x6741f4: mov             x0, x1
    // 0x6741f8: mov             x1, x16
    // 0x6741fc: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x6741fc: movz            x17, #0xd35d
    //     0x674200: add             lr, x0, x17
    //     0x674204: ldr             lr, [x21, lr, lsl #3]
    //     0x674208: blr             lr
    // 0x67420c: mov             x1, x0
    // 0x674210: ldur            x2, [fp, #-0x28]
    // 0x674214: StoreField: r2->field_13 = r0
    //     0x674214: stur            w0, [x2, #0x13]
    //     0x674218: ldurb           w16, [x2, #-1]
    //     0x67421c: ldurb           w17, [x0, #-1]
    //     0x674220: and             x16, x17, x16, lsr #2
    //     0x674224: tst             x16, HEAP, lsr #32
    //     0x674228: b.eq            #0x674230
    //     0x67422c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x674230: ldur            x5, [fp, #-0x10]
    // 0x674234: ldur            x6, [fp, #-8]
    // 0x674238: ldur            x3, [fp, #-0x20]
    // 0x67423c: ldur            x4, [fp, #-0x18]
    // 0x674240: b               #0x674148
    // 0x674244: ldur            x2, [fp, #-0x28]
    // 0x674248: LoadField: r1 = r2->field_13
    //     0x674248: ldur            w1, [x2, #0x13]
    // 0x67424c: DecompressPointer r1
    //     0x67424c: add             x1, x1, HEAP, lsl #32
    // 0x674250: cmp             w1, NULL
    // 0x674254: b.eq            #0x674350
    // 0x674258: r0 = LoadClassIdInstr(r1)
    //     0x674258: ldur            x0, [x1, #-1]
    //     0x67425c: ubfx            x0, x0, #0xc, #0x14
    // 0x674260: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x674260: movz            x17, #0x384d
    //     0x674264: movk            x17, #0x1, lsl #16
    //     0x674268: add             lr, x0, x17
    //     0x67426c: ldr             lr, [x21, lr, lsl #3]
    //     0x674270: blr             lr
    // 0x674274: mov             x4, x0
    // 0x674278: ldur            x3, [fp, #-0x28]
    // 0x67427c: stur            x4, [fp, #-0x30]
    // 0x674280: ArrayStore: r3[0] = r0  ; List_4
    //     0x674280: stur            w0, [x3, #0x17]
    //     0x674284: tbz             w0, #0, #0x6742a0
    //     0x674288: ldurb           w16, [x3, #-1]
    //     0x67428c: ldurb           w17, [x0, #-1]
    //     0x674290: and             x16, x17, x16, lsr #2
    //     0x674294: tst             x16, HEAP, lsr #32
    //     0x674298: b.eq            #0x6742a0
    //     0x67429c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x6742a0: cmp             w4, NULL
    // 0x6742a4: b.ne            #0x6742d8
    // 0x6742a8: mov             x0, x4
    // 0x6742ac: ldur            x2, [fp, #-8]
    // 0x6742b0: r1 = Null
    //     0x6742b0: mov             x1, NULL
    // 0x6742b4: cmp             w2, NULL
    // 0x6742b8: b.eq            #0x6742d8
    // 0x6742bc: LoadField: r4 = r2->field_1b
    //     0x6742bc: ldur            w4, [x2, #0x1b]
    // 0x6742c0: DecompressPointer r4
    //     0x6742c0: add             x4, x4, HEAP, lsl #32
    // 0x6742c4: r8 = X1
    //     0x6742c4: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x6742c8: LoadField: r9 = r4->field_7
    //     0x6742c8: ldur            x9, [x4, #7]
    // 0x6742cc: r3 = Null
    //     0x6742cc: add             x3, PP, #0x58, lsl #12  ; [pp+0x58360] Null
    //     0x6742d0: ldr             x3, [x3, #0x360]
    // 0x6742d4: blr             x9
    // 0x6742d8: ldur            x16, [fp, #-0x10]
    // 0x6742dc: ldur            lr, [fp, #-0x30]
    // 0x6742e0: stp             lr, x16, [SP]
    // 0x6742e4: ldur            x0, [fp, #-0x10]
    // 0x6742e8: ClosureCall
    //     0x6742e8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6742ec: ldur            x2, [x0, #0x1f]
    //     0x6742f0: blr             x2
    // 0x6742f4: r16 = true
    //     0x6742f4: add             x16, NULL, #0x20  ; true
    // 0x6742f8: cmp             w0, w16
    // 0x6742fc: b.eq            #0x674318
    // 0x674300: ldur            x2, [fp, #-0x28]
    // 0x674304: ldur            x5, [fp, #-0x10]
    // 0x674308: ldur            x6, [fp, #-8]
    // 0x67430c: ldur            x3, [fp, #-0x20]
    // 0x674310: ldur            x4, [fp, #-0x18]
    // 0x674314: b               #0x674128
    // 0x674318: r0 = true
    //     0x674318: add             x0, NULL, #0x20  ; true
    // 0x67431c: LeaveFrame
    //     0x67431c: mov             SP, fp
    //     0x674320: ldp             fp, lr, [SP], #0x10
    // 0x674324: ret
    //     0x674324: ret             
    // 0x674328: r0 = false
    //     0x674328: add             x0, NULL, #0x30  ; false
    // 0x67432c: LeaveFrame
    //     0x67432c: mov             SP, fp
    //     0x674330: ldp             fp, lr, [SP], #0x10
    // 0x674334: ret
    //     0x674334: ret             
    // 0x674338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67433c: b               #0x6740ec
    // 0x674340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x674344: b               #0x674134
    // 0x674348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x674348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67434c: b               #0x674154
    // 0x674350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x674350: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
