// lib: , url: package:flutter_layout_grid/src/rendering/layout_grid.dart

// class id: 1049443, size: 0x8
class :: {

  static _ constraintBoundsForType(/* No info */) {
    // ** addr: 0x735cd4, size: 0x7c
    // 0x735cd4: EnterFrame
    //     0x735cd4: stp             fp, lr, [SP, #-0x10]!
    //     0x735cd8: mov             fp, SP
    // 0x735cdc: AllocStack(0x10)
    //     0x735cdc: sub             SP, SP, #0x10
    // 0x735ce0: r16 = Instance_TrackType
    //     0x735ce0: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735ce4: ldr             x16, [x16, #0x340]
    // 0x735ce8: cmp             w2, w16
    // 0x735cec: b.ne            #0x735d1c
    // 0x735cf0: LoadField: d0 = r1->field_7
    //     0x735cf0: ldur            d0, [x1, #7]
    // 0x735cf4: stur            d0, [fp, #-0x10]
    // 0x735cf8: LoadField: d1 = r1->field_f
    //     0x735cf8: ldur            d1, [x1, #0xf]
    // 0x735cfc: stur            d1, [fp, #-8]
    // 0x735d00: r1 = <double>
    //     0x735d00: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735d04: r0 = MinMax()
    //     0x735d04: bl              #0x735d50  ; AllocateMinMaxStub -> MinMax<X0 bound num> (size=0x1c)
    // 0x735d08: ldur            d0, [fp, #-0x10]
    // 0x735d0c: StoreField: r0->field_b = d0
    //     0x735d0c: stur            d0, [x0, #0xb]
    // 0x735d10: ldur            d0, [fp, #-8]
    // 0x735d14: StoreField: r0->field_13 = d0
    //     0x735d14: stur            d0, [x0, #0x13]
    // 0x735d18: b               #0x735d44
    // 0x735d1c: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x735d1c: ldur            d0, [x1, #0x17]
    // 0x735d20: stur            d0, [fp, #-0x10]
    // 0x735d24: LoadField: d1 = r1->field_1f
    //     0x735d24: ldur            d1, [x1, #0x1f]
    // 0x735d28: stur            d1, [fp, #-8]
    // 0x735d2c: r1 = <double>
    //     0x735d2c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735d30: r0 = MinMax()
    //     0x735d30: bl              #0x735d50  ; AllocateMinMaxStub -> MinMax<X0 bound num> (size=0x1c)
    // 0x735d34: ldur            d0, [fp, #-0x10]
    // 0x735d38: StoreField: r0->field_b = d0
    //     0x735d38: stur            d0, [x0, #0xb]
    // 0x735d3c: ldur            d0, [fp, #-8]
    // 0x735d40: StoreField: r0->field_13 = d0
    //     0x735d40: stur            d0, [x0, #0x13]
    // 0x735d44: LeaveFrame
    //     0x735d44: mov             SP, fp
    //     0x735d48: ldp             fp, lr, [SP], #0x10
    // 0x735d4c: ret
    //     0x735d4c: ret             
  }
  [closure] static int _sortByGrowthPotential(dynamic, GridTrack, GridTrack) {
    // ** addr: 0x736de0, size: 0x4c
    // 0x736de0: EnterFrame
    //     0x736de0: stp             fp, lr, [SP, #-0x10]!
    //     0x736de4: mov             fp, SP
    // 0x736de8: CheckStackOverflow
    //     0x736de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736dec: cmp             SP, x16
    //     0x736df0: b.ls            #0x736e24
    // 0x736df4: ldr             x1, [fp, #0x18]
    // 0x736df8: ldr             x2, [fp, #0x10]
    // 0x736dfc: r0 = _sortByGrowthPotential()
    //     0x736dfc: bl              #0x736e2c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::_sortByGrowthPotential
    // 0x736e00: mov             x2, x0
    // 0x736e04: r0 = BoxInt64Instr(r2)
    //     0x736e04: sbfiz           x0, x2, #1, #0x1f
    //     0x736e08: cmp             x2, x0, asr #1
    //     0x736e0c: b.eq            #0x736e18
    //     0x736e10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x736e14: stur            x2, [x0, #7]
    // 0x736e18: LeaveFrame
    //     0x736e18: mov             SP, fp
    //     0x736e1c: ldp             fp, lr, [SP], #0x10
    // 0x736e20: ret
    //     0x736e20: ret             
    // 0x736e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x736e24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x736e28: b               #0x736df4
  }
  static _ _sortByGrowthPotential(/* No info */) {
    // ** addr: 0x736e2c, size: 0x110
    // 0x736e2c: EnterFrame
    //     0x736e2c: stp             fp, lr, [SP, #-0x10]!
    //     0x736e30: mov             fp, SP
    // 0x736e34: d0 = inf
    //     0x736e34: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x736e38: CheckStackOverflow
    //     0x736e38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736e3c: cmp             SP, x16
    //     0x736e40: b.ls            #0x736f04
    // 0x736e44: LoadField: d1 = r1->field_1b
    //     0x736e44: ldur            d1, [x1, #0x1b]
    // 0x736e48: fcmp            d1, d0
    // 0x736e4c: r16 = true
    //     0x736e4c: add             x16, NULL, #0x20  ; true
    // 0x736e50: r17 = false
    //     0x736e50: add             x17, NULL, #0x30  ; false
    // 0x736e54: csel            x0, x16, x17, eq
    // 0x736e58: LoadField: d2 = r2->field_1b
    //     0x736e58: ldur            d2, [x2, #0x1b]
    // 0x736e5c: fcmp            d2, d0
    // 0x736e60: r16 = true
    //     0x736e60: add             x16, NULL, #0x20  ; true
    // 0x736e64: r17 = false
    //     0x736e64: add             x17, NULL, #0x30  ; false
    // 0x736e68: csel            x3, x16, x17, eq
    // 0x736e6c: cmp             w0, w3
    // 0x736e70: b.eq            #0x736e94
    // 0x736e74: fcmp            d1, d0
    // 0x736e78: b.ne            #0x736e84
    // 0x736e7c: r0 = -1
    //     0x736e7c: movn            x0, #0
    // 0x736e80: b               #0x736e88
    // 0x736e84: r0 = 1
    //     0x736e84: movz            x0, #0x1
    // 0x736e88: LeaveFrame
    //     0x736e88: mov             SP, fp
    //     0x736e8c: ldp             fp, lr, [SP], #0x10
    // 0x736e90: ret
    //     0x736e90: ret             
    // 0x736e94: LoadField: d0 = r1->field_13
    //     0x736e94: ldur            d0, [x1, #0x13]
    // 0x736e98: fsub            d3, d1, d0
    // 0x736e9c: LoadField: d0 = r2->field_13
    //     0x736e9c: ldur            d0, [x2, #0x13]
    // 0x736ea0: fsub            d1, d2, d0
    // 0x736ea4: r1 = inline_Allocate_Double()
    //     0x736ea4: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0x736ea8: add             x1, x1, #0x10
    //     0x736eac: cmp             x0, x1
    //     0x736eb0: b.ls            #0x736f0c
    //     0x736eb4: str             x1, [THR, #0x50]  ; THR::top
    //     0x736eb8: sub             x1, x1, #0xf
    //     0x736ebc: movz            x0, #0xe15c
    //     0x736ec0: movk            x0, #0x3, lsl #16
    //     0x736ec4: stur            x0, [x1, #-1]
    // 0x736ec8: StoreField: r1->field_7 = d3
    //     0x736ec8: stur            d3, [x1, #7]
    // 0x736ecc: r2 = inline_Allocate_Double()
    //     0x736ecc: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x736ed0: add             x2, x2, #0x10
    //     0x736ed4: cmp             x0, x2
    //     0x736ed8: b.ls            #0x736f20
    //     0x736edc: str             x2, [THR, #0x50]  ; THR::top
    //     0x736ee0: sub             x2, x2, #0xf
    //     0x736ee4: movz            x0, #0xe15c
    //     0x736ee8: movk            x0, #0x3, lsl #16
    //     0x736eec: stur            x0, [x2, #-1]
    // 0x736ef0: StoreField: r2->field_7 = d1
    //     0x736ef0: stur            d1, [x2, #7]
    // 0x736ef4: r0 = compareTo()
    //     0x736ef4: bl              #0x6da84c  ; [dart:core] _Double::compareTo
    // 0x736ef8: LeaveFrame
    //     0x736ef8: mov             SP, fp
    //     0x736efc: ldp             fp, lr, [SP], #0x10
    // 0x736f00: ret
    //     0x736f00: ret             
    // 0x736f04: r0 = StackOverflowSharedWithFPURegs()
    //     0x736f04: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x736f08: b               #0x736e44
    // 0x736f0c: stp             q1, q3, [SP, #-0x20]!
    // 0x736f10: r0 = AllocateDouble()
    //     0x736f10: bl              #0xec2254  ; AllocateDoubleStub
    // 0x736f14: mov             x1, x0
    // 0x736f18: ldp             q1, q3, [SP], #0x20
    // 0x736f1c: b               #0x736ec8
    // 0x736f20: SaveReg d1
    //     0x736f20: str             q1, [SP, #-0x10]!
    // 0x736f24: SaveReg r1
    //     0x736f24: str             x1, [SP, #-8]!
    // 0x736f28: r0 = AllocateDouble()
    //     0x736f28: bl              #0xec2254  ; AllocateDoubleStub
    // 0x736f2c: mov             x2, x0
    // 0x736f30: RestoreReg r1
    //     0x736f30: ldr             x1, [SP], #8
    // 0x736f34: RestoreReg d1
    //     0x736f34: ldr             q1, [SP], #0x10
    // 0x736f38: b               #0x736ef0
  }
  static _ _sizesToTracks(/* No info */) {
    // ** addr: 0x739568, size: 0x98
    // 0x739568: EnterFrame
    //     0x739568: stp             fp, lr, [SP, #-0x10]!
    //     0x73956c: mov             fp, SP
    // 0x739570: AllocStack(0x20)
    //     0x739570: sub             SP, SP, #0x20
    // 0x739574: CheckStackOverflow
    //     0x739574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x739578: cmp             SP, x16
    //     0x73957c: b.ls            #0x7395f8
    // 0x739580: r16 = <TrackSize>
    //     0x739580: add             x16, PP, #0x31, lsl #12  ; [pp+0x31770] TypeArguments: <TrackSize>
    //     0x739584: ldr             x16, [x16, #0x770]
    // 0x739588: stp             x1, x16, [SP]
    // 0x73958c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x73958c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x739590: r0 = enumerate()
    //     0x739590: bl              #0x739600  ; [package:quiver/src/iterables/enumerate.dart] ::enumerate
    // 0x739594: r1 = Function '<anonymous closure>': static.
    //     0x739594: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c598] AnonymousClosure: static (0x739668), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::_sizesToTracks (0x739568)
    //     0x739598: ldr             x1, [x1, #0x598]
    // 0x73959c: r2 = Null
    //     0x73959c: mov             x2, NULL
    // 0x7395a0: stur            x0, [fp, #-8]
    // 0x7395a4: r0 = AllocateClosure()
    //     0x7395a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7395a8: r16 = <GridTrack>
    //     0x7395a8: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x7395ac: ldr             x16, [x16, #0x3a8]
    // 0x7395b0: ldur            lr, [fp, #-8]
    // 0x7395b4: stp             lr, x16, [SP, #8]
    // 0x7395b8: str             x0, [SP]
    // 0x7395bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7395bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7395c0: r0 = map()
    //     0x7395c0: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x7395c4: LoadField: r1 = r0->field_7
    //     0x7395c4: ldur            w1, [x0, #7]
    // 0x7395c8: DecompressPointer r1
    //     0x7395c8: add             x1, x1, HEAP, lsl #32
    // 0x7395cc: mov             x2, x0
    // 0x7395d0: r0 = _List.of()
    //     0x7395d0: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x7395d4: r1 = <GridTrack>
    //     0x7395d4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x7395d8: ldr             x1, [x1, #0x3a8]
    // 0x7395dc: stur            x0, [fp, #-8]
    // 0x7395e0: r0 = UnmodifiableListView()
    //     0x7395e0: bl              #0x73955c  ; AllocateUnmodifiableListViewStub -> UnmodifiableListView<X0> (size=0x10)
    // 0x7395e4: ldur            x1, [fp, #-8]
    // 0x7395e8: StoreField: r0->field_b = r1
    //     0x7395e8: stur            w1, [x0, #0xb]
    // 0x7395ec: LeaveFrame
    //     0x7395ec: mov             SP, fp
    //     0x7395f0: ldp             fp, lr, [SP], #0x10
    // 0x7395f4: ret
    //     0x7395f4: ret             
    // 0x7395f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7395f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7395fc: b               #0x739580
  }
  [closure] static GridTrack <anonymous closure>(dynamic, IndexedValue<TrackSize>) {
    // ** addr: 0x739668, size: 0x50
    // 0x739668: EnterFrame
    //     0x739668: stp             fp, lr, [SP, #-0x10]!
    //     0x73966c: mov             fp, SP
    // 0x739670: AllocStack(0x10)
    //     0x739670: sub             SP, SP, #0x10
    // 0x739674: ldr             x0, [fp, #0x10]
    // 0x739678: LoadField: r1 = r0->field_b
    //     0x739678: ldur            x1, [x0, #0xb]
    // 0x73967c: stur            x1, [fp, #-0x10]
    // 0x739680: LoadField: r2 = r0->field_13
    //     0x739680: ldur            w2, [x0, #0x13]
    // 0x739684: DecompressPointer r2
    //     0x739684: add             x2, x2, HEAP, lsl #32
    // 0x739688: stur            x2, [fp, #-8]
    // 0x73968c: r0 = GridTrack()
    //     0x73968c: bl              #0x7396b8  ; AllocateGridTrackStub -> GridTrack (size=0x2c)
    // 0x739690: StoreField: r0->field_13 = rZR
    //     0x739690: stur            xzr, [x0, #0x13]
    // 0x739694: StoreField: r0->field_1b = rZR
    //     0x739694: stur            xzr, [x0, #0x1b]
    // 0x739698: StoreField: r0->field_23 = rZR
    //     0x739698: stur            xzr, [x0, #0x23]
    // 0x73969c: ldur            x1, [fp, #-0x10]
    // 0x7396a0: StoreField: r0->field_7 = r1
    //     0x7396a0: stur            x1, [x0, #7]
    // 0x7396a4: ldur            x1, [fp, #-8]
    // 0x7396a8: StoreField: r0->field_f = r1
    //     0x7396a8: stur            w1, [x0, #0xf]
    // 0x7396ac: LeaveFrame
    //     0x7396ac: mov             SP, fp
    //     0x7396b0: ldp             fp, lr, [SP], #0x10
    // 0x7396b4: ret
    //     0x7396b4: ret             
  }
}

// class id: 2290, size: 0x1c, field offset: 0x8
//   const constructor, 
class MinMax<X0 bound num> extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc28144, size: 0x184
    // 0xc28144: EnterFrame
    //     0xc28144: stp             fp, lr, [SP, #-0x10]!
    //     0xc28148: mov             fp, SP
    // 0xc2814c: AllocStack(0x28)
    //     0xc2814c: sub             SP, SP, #0x28
    // 0xc28150: CheckStackOverflow
    //     0xc28150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc28154: cmp             SP, x16
    //     0xc28158: b.ls            #0xc28288
    // 0xc2815c: ldr             x0, [fp, #0x10]
    // 0xc28160: LoadField: d0 = r0->field_b
    //     0xc28160: ldur            d0, [x0, #0xb]
    // 0xc28164: stur            d0, [fp, #-0x18]
    // 0xc28168: r1 = inline_Allocate_Double()
    //     0xc28168: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc2816c: add             x1, x1, #0x10
    //     0xc28170: cmp             x2, x1
    //     0xc28174: b.ls            #0xc28290
    //     0xc28178: str             x1, [THR, #0x50]  ; THR::top
    //     0xc2817c: sub             x1, x1, #0xf
    //     0xc28180: movz            x2, #0xe15c
    //     0xc28184: movk            x2, #0x3, lsl #16
    //     0xc28188: stur            x2, [x1, #-1]
    // 0xc2818c: StoreField: r1->field_7 = d0
    //     0xc2818c: stur            d0, [x1, #7]
    // 0xc28190: r2 = 1
    //     0xc28190: movz            x2, #0x1
    // 0xc28194: r0 = toStringAsFixed()
    //     0xc28194: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc28198: r1 = Null
    //     0xc28198: mov             x1, NULL
    // 0xc2819c: r2 = 8
    //     0xc2819c: movz            x2, #0x8
    // 0xc281a0: stur            x0, [fp, #-8]
    // 0xc281a4: r0 = AllocateArray()
    //     0xc281a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc281a8: mov             x3, x0
    // 0xc281ac: ldur            x0, [fp, #-8]
    // 0xc281b0: stur            x3, [fp, #-0x10]
    // 0xc281b4: StoreField: r3->field_f = r0
    //     0xc281b4: stur            w0, [x3, #0xf]
    // 0xc281b8: r16 = "->"
    //     0xc281b8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e78] "->"
    //     0xc281bc: ldr             x16, [x16, #0xe78]
    // 0xc281c0: StoreField: r3->field_13 = r16
    //     0xc281c0: stur            w16, [x3, #0x13]
    // 0xc281c4: ldr             x0, [fp, #0x10]
    // 0xc281c8: LoadField: d0 = r0->field_13
    //     0xc281c8: ldur            d0, [x0, #0x13]
    // 0xc281cc: stur            d0, [fp, #-0x20]
    // 0xc281d0: r1 = inline_Allocate_Double()
    //     0xc281d0: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xc281d4: add             x1, x1, #0x10
    //     0xc281d8: cmp             x0, x1
    //     0xc281dc: b.ls            #0xc282ac
    //     0xc281e0: str             x1, [THR, #0x50]  ; THR::top
    //     0xc281e4: sub             x1, x1, #0xf
    //     0xc281e8: movz            x0, #0xe15c
    //     0xc281ec: movk            x0, #0x3, lsl #16
    //     0xc281f0: stur            x0, [x1, #-1]
    // 0xc281f4: StoreField: r1->field_7 = d0
    //     0xc281f4: stur            d0, [x1, #7]
    // 0xc281f8: r2 = 1
    //     0xc281f8: movz            x2, #0x1
    // 0xc281fc: r0 = toStringAsFixed()
    //     0xc281fc: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xc28200: ldur            x1, [fp, #-0x10]
    // 0xc28204: ArrayStore: r1[2] = r0  ; List_4
    //     0xc28204: add             x25, x1, #0x17
    //     0xc28208: str             w0, [x25]
    //     0xc2820c: tbz             w0, #0, #0xc28228
    //     0xc28210: ldurb           w16, [x1, #-1]
    //     0xc28214: ldurb           w17, [x0, #-1]
    //     0xc28218: and             x16, x17, x16, lsr #2
    //     0xc2821c: tst             x16, HEAP, lsr #32
    //     0xc28220: b.eq            #0xc28228
    //     0xc28224: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc28228: ldur            d1, [fp, #-0x18]
    // 0xc2822c: ldur            d0, [fp, #-0x20]
    // 0xc28230: fcmp            d1, d0
    // 0xc28234: b.ne            #0xc28244
    // 0xc28238: r0 = " (same)"
    //     0xc28238: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e80] " (same)"
    //     0xc2823c: ldr             x0, [x0, #0xe80]
    // 0xc28240: b               #0xc28248
    // 0xc28244: r0 = ""
    //     0xc28244: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc28248: ldur            x1, [fp, #-0x10]
    // 0xc2824c: ArrayStore: r1[3] = r0  ; List_4
    //     0xc2824c: add             x25, x1, #0x1b
    //     0xc28250: str             w0, [x25]
    //     0xc28254: tbz             w0, #0, #0xc28270
    //     0xc28258: ldurb           w16, [x1, #-1]
    //     0xc2825c: ldurb           w17, [x0, #-1]
    //     0xc28260: and             x16, x17, x16, lsr #2
    //     0xc28264: tst             x16, HEAP, lsr #32
    //     0xc28268: b.eq            #0xc28270
    //     0xc2826c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc28270: ldur            x16, [fp, #-0x10]
    // 0xc28274: str             x16, [SP]
    // 0xc28278: r0 = _interpolate()
    //     0xc28278: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc2827c: LeaveFrame
    //     0xc2827c: mov             SP, fp
    //     0xc28280: ldp             fp, lr, [SP], #0x10
    // 0xc28284: ret
    //     0xc28284: ret             
    // 0xc28288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2828c: b               #0xc2815c
    // 0xc28290: SaveReg d0
    //     0xc28290: str             q0, [SP, #-0x10]!
    // 0xc28294: SaveReg r0
    //     0xc28294: str             x0, [SP, #-8]!
    // 0xc28298: r0 = AllocateDouble()
    //     0xc28298: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc2829c: mov             x1, x0
    // 0xc282a0: RestoreReg r0
    //     0xc282a0: ldr             x0, [SP], #8
    // 0xc282a4: RestoreReg d0
    //     0xc282a4: ldr             q0, [SP], #0x10
    // 0xc282a8: b               #0xc2818c
    // 0xc282ac: SaveReg d0
    //     0xc282ac: str             q0, [SP, #-0x10]!
    // 0xc282b0: SaveReg r3
    //     0xc282b0: str             x3, [SP, #-8]!
    // 0xc282b4: r0 = AllocateDouble()
    //     0xc282b4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc282b8: mov             x1, x0
    // 0xc282bc: RestoreReg r3
    //     0xc282bc: ldr             x3, [SP], #8
    // 0xc282c0: RestoreReg d0
    //     0xc282c0: ldr             q0, [SP], #0x10
    // 0xc282c4: b               #0xc281f4
  }
}

// class id: 2291, size: 0x58, field offset: 0x8
class GridSizingInfo extends Object {

  get _ internalGridSize(/* No info */) {
    // ** addr: 0x73543c, size: 0x17c
    // 0x73543c: EnterFrame
    //     0x73543c: stp             fp, lr, [SP, #-0x10]!
    //     0x735440: mov             fp, SP
    // 0x735444: AllocStack(0x40)
    //     0x735444: sub             SP, SP, #0x40
    // 0x735448: SetupParameters(GridSizingInfo this /* r1 => r0, fp-0x10 */)
    //     0x735448: mov             x0, x1
    //     0x73544c: stur            x1, [fp, #-0x10]
    // 0x735450: CheckStackOverflow
    //     0x735450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735454: cmp             SP, x16
    //     0x735458: b.ls            #0x7355b0
    // 0x73545c: LoadField: r3 = r0->field_1b
    //     0x73545c: ldur            w3, [x0, #0x1b]
    // 0x735460: DecompressPointer r3
    //     0x735460: add             x3, x3, HEAP, lsl #32
    // 0x735464: stur            x3, [fp, #-8]
    // 0x735468: r1 = Function '<anonymous closure>':.
    //     0x735468: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c350] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x73546c: ldr             x1, [x1, #0x350]
    // 0x735470: r2 = Null
    //     0x735470: mov             x2, NULL
    // 0x735474: r0 = AllocateClosure()
    //     0x735474: bl              #0xec1630  ; AllocateClosureStub
    // 0x735478: r16 = <double>
    //     0x735478: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x73547c: ldur            lr, [fp, #-8]
    // 0x735480: stp             lr, x16, [SP, #8]
    // 0x735484: str             x0, [SP]
    // 0x735488: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x735488: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x73548c: r0 = map()
    //     0x73548c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x735490: r16 = <double>
    //     0x735490: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735494: stp             x0, x16, [SP]
    // 0x735498: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x735498: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x73549c: r0 = sum()
    //     0x73549c: bl              #0x7355b8  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::sum
    // 0x7354a0: mov             x1, x0
    // 0x7354a4: ldur            x0, [fp, #-8]
    // 0x7354a8: stur            x1, [fp, #-0x18]
    // 0x7354ac: LoadField: r2 = r0->field_b
    //     0x7354ac: ldur            w2, [x0, #0xb]
    // 0x7354b0: DecompressPointer r2
    //     0x7354b0: add             x2, x2, HEAP, lsl #32
    // 0x7354b4: r0 = LoadClassIdInstr(r2)
    //     0x7354b4: ldur            x0, [x2, #-1]
    //     0x7354b8: ubfx            x0, x0, #0xc, #0x14
    // 0x7354bc: str             x2, [SP]
    // 0x7354c0: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7354c0: movz            x17, #0xc834
    //     0x7354c4: add             lr, x0, x17
    //     0x7354c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7354cc: blr             lr
    // 0x7354d0: r1 = LoadInt32Instr(r0)
    //     0x7354d0: sbfx            x1, x0, #1, #0x1f
    //     0x7354d4: tbz             w0, #0, #0x7354dc
    //     0x7354d8: ldur            x1, [x0, #7]
    // 0x7354dc: sub             x0, x1, #1
    // 0x7354e0: scvtf           d0, x0
    // 0x7354e4: ldur            x0, [fp, #-0x18]
    // 0x7354e8: LoadField: d1 = r0->field_7
    //     0x7354e8: ldur            d1, [x0, #7]
    // 0x7354ec: fadd            d2, d1, d0
    // 0x7354f0: ldur            x0, [fp, #-0x10]
    // 0x7354f4: stur            d2, [fp, #-0x20]
    // 0x7354f8: LoadField: r3 = r0->field_1f
    //     0x7354f8: ldur            w3, [x0, #0x1f]
    // 0x7354fc: DecompressPointer r3
    //     0x7354fc: add             x3, x3, HEAP, lsl #32
    // 0x735500: stur            x3, [fp, #-8]
    // 0x735504: r1 = Function '<anonymous closure>':.
    //     0x735504: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c358] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x735508: ldr             x1, [x1, #0x358]
    // 0x73550c: r2 = Null
    //     0x73550c: mov             x2, NULL
    // 0x735510: r0 = AllocateClosure()
    //     0x735510: bl              #0xec1630  ; AllocateClosureStub
    // 0x735514: r16 = <double>
    //     0x735514: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735518: ldur            lr, [fp, #-8]
    // 0x73551c: stp             lr, x16, [SP, #8]
    // 0x735520: str             x0, [SP]
    // 0x735524: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x735524: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x735528: r0 = map()
    //     0x735528: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x73552c: r16 = <double>
    //     0x73552c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735530: stp             x0, x16, [SP]
    // 0x735534: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x735534: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x735538: r0 = sum()
    //     0x735538: bl              #0x7355b8  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::sum
    // 0x73553c: mov             x1, x0
    // 0x735540: ldur            x0, [fp, #-8]
    // 0x735544: stur            x1, [fp, #-0x10]
    // 0x735548: LoadField: r2 = r0->field_b
    //     0x735548: ldur            w2, [x0, #0xb]
    // 0x73554c: DecompressPointer r2
    //     0x73554c: add             x2, x2, HEAP, lsl #32
    // 0x735550: r0 = LoadClassIdInstr(r2)
    //     0x735550: ldur            x0, [x2, #-1]
    //     0x735554: ubfx            x0, x0, #0xc, #0x14
    // 0x735558: str             x2, [SP]
    // 0x73555c: r0 = GDT[cid_x0 + 0xc834]()
    //     0x73555c: movz            x17, #0xc834
    //     0x735560: add             lr, x0, x17
    //     0x735564: ldr             lr, [x21, lr, lsl #3]
    //     0x735568: blr             lr
    // 0x73556c: r1 = LoadInt32Instr(r0)
    //     0x73556c: sbfx            x1, x0, #1, #0x1f
    //     0x735570: tbz             w0, #0, #0x735578
    //     0x735574: ldur            x1, [x0, #7]
    // 0x735578: sub             x0, x1, #1
    // 0x73557c: scvtf           d0, x0
    // 0x735580: ldur            x0, [fp, #-0x10]
    // 0x735584: LoadField: d1 = r0->field_7
    //     0x735584: ldur            d1, [x0, #7]
    // 0x735588: fadd            d2, d1, d0
    // 0x73558c: stur            d2, [fp, #-0x28]
    // 0x735590: r0 = Size()
    //     0x735590: bl              #0x61877c  ; AllocateSizeStub -> Size (size=0x18)
    // 0x735594: ldur            d0, [fp, #-0x20]
    // 0x735598: StoreField: r0->field_7 = d0
    //     0x735598: stur            d0, [x0, #7]
    // 0x73559c: ldur            d0, [fp, #-0x28]
    // 0x7355a0: StoreField: r0->field_f = d0
    //     0x7355a0: stur            d0, [x0, #0xf]
    // 0x7355a4: LeaveFrame
    //     0x7355a4: mov             SP, fp
    //     0x7355a8: ldp             fp, lr, [SP], #0x10
    // 0x7355ac: ret
    //     0x7355ac: ret             
    // 0x7355b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7355b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7355b4: b               #0x73545c
  }
  [closure] double <anonymous closure>(dynamic, GridTrack) {
    // ** addr: 0x7357fc, size: 0x54
    // 0x7357fc: EnterFrame
    //     0x7357fc: stp             fp, lr, [SP, #-0x10]!
    //     0x735800: mov             fp, SP
    // 0x735804: ldr             x1, [fp, #0x10]
    // 0x735808: LoadField: d0 = r1->field_13
    //     0x735808: ldur            d0, [x1, #0x13]
    // 0x73580c: r0 = inline_Allocate_Double()
    //     0x73580c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x735810: add             x0, x0, #0x10
    //     0x735814: cmp             x1, x0
    //     0x735818: b.ls            #0x735840
    //     0x73581c: str             x0, [THR, #0x50]  ; THR::top
    //     0x735820: sub             x0, x0, #0xf
    //     0x735824: movz            x1, #0xe15c
    //     0x735828: movk            x1, #0x3, lsl #16
    //     0x73582c: stur            x1, [x0, #-1]
    // 0x735830: StoreField: r0->field_7 = d0
    //     0x735830: stur            d0, [x0, #7]
    // 0x735834: LeaveFrame
    //     0x735834: mov             SP, fp
    //     0x735838: ldp             fp, lr, [SP], #0x10
    // 0x73583c: ret
    //     0x73583c: ret             
    // 0x735840: SaveReg d0
    //     0x735840: str             q0, [SP, #-0x10]!
    // 0x735844: r0 = AllocateDouble()
    //     0x735844: bl              #0xec2254  ; AllocateDoubleStub
    // 0x735848: RestoreReg d0
    //     0x735848: ldr             q0, [SP], #0x10
    // 0x73584c: b               #0x735830
  }
  _ invalidateTrackStartsForType(/* No info */) {
    // ** addr: 0x735b0c, size: 0x24
    // 0x735b0c: r16 = Instance_TrackType
    //     0x735b0c: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735b10: ldr             x16, [x16, #0x340]
    // 0x735b14: cmp             w2, w16
    // 0x735b18: b.ne            #0x735b24
    // 0x735b1c: StoreField: r1->field_27 = rNULL
    //     0x735b1c: stur            NULL, [x1, #0x27]
    // 0x735b20: b               #0x735b28
    // 0x735b24: StoreField: r1->field_2b = rNULL
    //     0x735b24: stur            NULL, [x1, #0x2b]
    // 0x735b28: r0 = Null
    //     0x735b28: mov             x0, NULL
    // 0x735b2c: ret
    //     0x735b2c: ret             
  }
  _ totalGapForType(/* No info */) {
    // ** addr: 0x735b58, size: 0xa4
    // 0x735b58: EnterFrame
    //     0x735b58: stp             fp, lr, [SP, #-0x10]!
    //     0x735b5c: mov             fp, SP
    // 0x735b60: AllocStack(0x20)
    //     0x735b60: sub             SP, SP, #0x20
    // 0x735b64: SetupParameters(GridSizingInfo this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x735b64: stur            x1, [fp, #-8]
    //     0x735b68: stur            x2, [fp, #-0x10]
    // 0x735b6c: CheckStackOverflow
    //     0x735b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735b70: cmp             SP, x16
    //     0x735b74: b.ls            #0x735bf4
    // 0x735b78: r16 = Instance_TrackType
    //     0x735b78: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735b7c: ldr             x16, [x16, #0x340]
    // 0x735b80: cmp             w2, w16
    // 0x735b84: b.ne            #0x735b94
    // 0x735b88: LoadField: r0 = r1->field_1b
    //     0x735b88: ldur            w0, [x1, #0x1b]
    // 0x735b8c: DecompressPointer r0
    //     0x735b8c: add             x0, x0, HEAP, lsl #32
    // 0x735b90: b               #0x735b9c
    // 0x735b94: LoadField: r0 = r1->field_1f
    //     0x735b94: ldur            w0, [x1, #0x1f]
    // 0x735b98: DecompressPointer r0
    //     0x735b98: add             x0, x0, HEAP, lsl #32
    // 0x735b9c: LoadField: r3 = r0->field_b
    //     0x735b9c: ldur            w3, [x0, #0xb]
    // 0x735ba0: DecompressPointer r3
    //     0x735ba0: add             x3, x3, HEAP, lsl #32
    // 0x735ba4: r0 = LoadClassIdInstr(r3)
    //     0x735ba4: ldur            x0, [x3, #-1]
    //     0x735ba8: ubfx            x0, x0, #0xc, #0x14
    // 0x735bac: str             x3, [SP]
    // 0x735bb0: r0 = GDT[cid_x0 + 0xc834]()
    //     0x735bb0: movz            x17, #0xc834
    //     0x735bb4: add             lr, x0, x17
    //     0x735bb8: ldr             lr, [x21, lr, lsl #3]
    //     0x735bbc: blr             lr
    // 0x735bc0: r1 = LoadInt32Instr(r0)
    //     0x735bc0: sbfx            x1, x0, #1, #0x1f
    //     0x735bc4: tbz             w0, #0, #0x735bcc
    //     0x735bc8: ldur            x1, [x0, #7]
    // 0x735bcc: sub             x0, x1, #1
    // 0x735bd0: ldur            x1, [fp, #-8]
    // 0x735bd4: ldur            x2, [fp, #-0x10]
    // 0x735bd8: stur            x0, [fp, #-0x18]
    // 0x735bdc: r0 = unitGapForType()
    //     0x735bdc: bl              #0x735bfc  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::unitGapForType
    // 0x735be0: ldur            x0, [fp, #-0x18]
    // 0x735be4: scvtf           d0, x0
    // 0x735be8: LeaveFrame
    //     0x735be8: mov             SP, fp
    //     0x735bec: ldp             fp, lr, [SP], #0x10
    // 0x735bf0: ret
    //     0x735bf0: ret             
    // 0x735bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735bf8: b               #0x735b78
  }
  _ unitGapForType(/* No info */) {
    // ** addr: 0x735bfc, size: 0x8
    // 0x735bfc: d0 = 1.000000
    //     0x735bfc: fmov            d0, #1.00000000
    // 0x735c00: ret
    //     0x735c00: ret             
  }
  _ totalBaseSizeOfTracksForType(/* No info */) {
    // ** addr: 0x735c04, size: 0x44
    // 0x735c04: EnterFrame
    //     0x735c04: stp             fp, lr, [SP, #-0x10]!
    //     0x735c08: mov             fp, SP
    // 0x735c0c: AllocStack(0x10)
    //     0x735c0c: sub             SP, SP, #0x10
    // 0x735c10: CheckStackOverflow
    //     0x735c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735c14: cmp             SP, x16
    //     0x735c18: b.ls            #0x735c40
    // 0x735c1c: r0 = baseSizesForType()
    //     0x735c1c: bl              #0x735c48  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::baseSizesForType
    // 0x735c20: r16 = <double>
    //     0x735c20: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735c24: stp             x0, x16, [SP]
    // 0x735c28: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x735c28: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x735c2c: r0 = sum()
    //     0x735c2c: bl              #0x7355b8  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::sum
    // 0x735c30: LoadField: d0 = r0->field_7
    //     0x735c30: ldur            d0, [x0, #7]
    // 0x735c34: LeaveFrame
    //     0x735c34: mov             SP, fp
    //     0x735c38: ldp             fp, lr, [SP], #0x10
    // 0x735c3c: ret
    //     0x735c3c: ret             
    // 0x735c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735c40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735c44: b               #0x735c1c
  }
  _ baseSizesForType(/* No info */) {
    // ** addr: 0x735c48, size: 0x8c
    // 0x735c48: EnterFrame
    //     0x735c48: stp             fp, lr, [SP, #-0x10]!
    //     0x735c4c: mov             fp, SP
    // 0x735c50: AllocStack(0x20)
    //     0x735c50: sub             SP, SP, #0x20
    // 0x735c54: CheckStackOverflow
    //     0x735c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735c58: cmp             SP, x16
    //     0x735c5c: b.ls            #0x735ccc
    // 0x735c60: r16 = Instance_TrackType
    //     0x735c60: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735c64: ldr             x16, [x16, #0x340]
    // 0x735c68: cmp             w2, w16
    // 0x735c6c: b.ne            #0x735c7c
    // 0x735c70: LoadField: r0 = r1->field_1b
    //     0x735c70: ldur            w0, [x1, #0x1b]
    // 0x735c74: DecompressPointer r0
    //     0x735c74: add             x0, x0, HEAP, lsl #32
    // 0x735c78: b               #0x735c84
    // 0x735c7c: LoadField: r0 = r1->field_1f
    //     0x735c7c: ldur            w0, [x1, #0x1f]
    // 0x735c80: DecompressPointer r0
    //     0x735c80: add             x0, x0, HEAP, lsl #32
    // 0x735c84: stur            x0, [fp, #-8]
    // 0x735c88: r1 = Function '<anonymous closure>':.
    //     0x735c88: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a0] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x735c8c: ldr             x1, [x1, #0x3a0]
    // 0x735c90: r2 = Null
    //     0x735c90: mov             x2, NULL
    // 0x735c94: r0 = AllocateClosure()
    //     0x735c94: bl              #0xec1630  ; AllocateClosureStub
    // 0x735c98: r16 = <double>
    //     0x735c98: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x735c9c: ldur            lr, [fp, #-8]
    // 0x735ca0: stp             lr, x16, [SP, #8]
    // 0x735ca4: str             x0, [SP]
    // 0x735ca8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x735ca8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x735cac: r0 = map()
    //     0x735cac: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x735cb0: LoadField: r1 = r0->field_7
    //     0x735cb0: ldur            w1, [x0, #7]
    // 0x735cb4: DecompressPointer r1
    //     0x735cb4: add             x1, x1, HEAP, lsl #32
    // 0x735cb8: mov             x2, x0
    // 0x735cbc: r0 = _GrowableList.of()
    //     0x735cbc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x735cc0: LeaveFrame
    //     0x735cc0: mov             SP, fp
    //     0x735cc4: ldp             fp, lr, [SP], #0x10
    // 0x735cc8: ret
    //     0x735cc8: ret             
    // 0x735ccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735ccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735cd0: b               #0x735c60
  }
  _ markTrackTypeSized(/* No info */) {
    // ** addr: 0x735de4, size: 0x2c
    // 0x735de4: r16 = Instance_TrackType
    //     0x735de4: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735de8: ldr             x16, [x16, #0x340]
    // 0x735dec: cmp             w2, w16
    // 0x735df0: b.ne            #0x735e00
    // 0x735df4: r2 = true
    //     0x735df4: add             x2, NULL, #0x20  ; true
    // 0x735df8: StoreField: r1->field_4f = r2
    //     0x735df8: stur            w2, [x1, #0x4f]
    // 0x735dfc: b               #0x735e08
    // 0x735e00: r2 = true
    //     0x735e00: add             x2, NULL, #0x20  ; true
    // 0x735e04: StoreField: r1->field_53 = r2
    //     0x735e04: stur            w2, [x1, #0x53]
    // 0x735e08: r0 = Null
    //     0x735e08: mov             x0, NULL
    // 0x735e0c: ret
    //     0x735e0c: ret             
  }
  _ setMinMaxTrackSizesForAxis(/* No info */) {
    // ** addr: 0x737154, size: 0x28
    // 0x737154: r16 = Instance_Axis
    //     0x737154: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x737158: cmp             w2, w16
    // 0x73715c: b.ne            #0x73716c
    // 0x737160: StoreField: r1->field_2f = d0
    //     0x737160: stur            d0, [x1, #0x2f]
    // 0x737164: StoreField: r1->field_3f = d1
    //     0x737164: stur            d1, [x1, #0x3f]
    // 0x737168: b               #0x737174
    // 0x73716c: StoreField: r1->field_37 = d0
    //     0x73716c: stur            d0, [x1, #0x37]
    // 0x737170: StoreField: r1->field_47 = d1
    //     0x737170: stur            d1, [x1, #0x47]
    // 0x737174: r0 = Null
    //     0x737174: mov             x0, NULL
    // 0x737178: ret
    //     0x737178: ret             
  }
  _ sizeForAreaOnAxis(/* No info */) {
    // ** addr: 0x738650, size: 0x14c
    // 0x738650: EnterFrame
    //     0x738650: stp             fp, lr, [SP, #-0x10]!
    //     0x738654: mov             fp, SP
    // 0x738658: AllocStack(0x38)
    //     0x738658: sub             SP, SP, #0x38
    // 0x73865c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x73865c: mov             x4, x2
    //     0x738660: mov             x0, x3
    //     0x738664: stur            x2, [fp, #-8]
    //     0x738668: stur            x3, [fp, #-0x10]
    // 0x73866c: CheckStackOverflow
    //     0x73866c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x738670: cmp             SP, x16
    //     0x738674: b.ls            #0x738794
    // 0x738678: r16 = Instance_Axis
    //     0x738678: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x73867c: cmp             w0, w16
    // 0x738680: b.ne            #0x738694
    // 0x738684: LoadField: r2 = r1->field_1b
    //     0x738684: ldur            w2, [x1, #0x1b]
    // 0x738688: DecompressPointer r2
    //     0x738688: add             x2, x2, HEAP, lsl #32
    // 0x73868c: mov             x1, x2
    // 0x738690: b               #0x7386a0
    // 0x738694: LoadField: r2 = r1->field_1f
    //     0x738694: ldur            w2, [x1, #0x1f]
    // 0x738698: DecompressPointer r2
    //     0x738698: add             x2, x2, HEAP, lsl #32
    // 0x73869c: mov             x1, x2
    // 0x7386a0: r16 = Instance_Axis
    //     0x7386a0: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x7386a4: cmp             w0, w16
    // 0x7386a8: b.ne            #0x7386b4
    // 0x7386ac: LoadField: r2 = r4->field_b
    //     0x7386ac: ldur            x2, [x4, #0xb]
    // 0x7386b0: b               #0x7386b8
    // 0x7386b4: LoadField: r2 = r4->field_13
    //     0x7386b4: ldur            x2, [x4, #0x13]
    // 0x7386b8: r16 = Instance_Axis
    //     0x7386b8: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x7386bc: cmp             w0, w16
    // 0x7386c0: b.ne            #0x7386cc
    // 0x7386c4: LoadField: r3 = r4->field_1b
    //     0x7386c4: ldur            x3, [x4, #0x1b]
    // 0x7386c8: b               #0x7386d0
    // 0x7386cc: LoadField: r3 = r4->field_23
    //     0x7386cc: ldur            x3, [x4, #0x23]
    // 0x7386d0: r0 = getRange()
    //     0x7386d0: bl              #0x6dbf3c  ; [dart:collection] ListBase::getRange
    // 0x7386d4: r1 = Function '<anonymous closure>':.
    //     0x7386d4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c4e8] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x7386d8: ldr             x1, [x1, #0x4e8]
    // 0x7386dc: r2 = Null
    //     0x7386dc: mov             x2, NULL
    // 0x7386e0: stur            x0, [fp, #-0x18]
    // 0x7386e4: r0 = AllocateClosure()
    //     0x7386e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7386e8: r16 = <double>
    //     0x7386e8: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x7386ec: ldur            lr, [fp, #-0x18]
    // 0x7386f0: stp             lr, x16, [SP, #8]
    // 0x7386f4: str             x0, [SP]
    // 0x7386f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7386f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7386fc: r0 = map()
    //     0x7386fc: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0x738700: ldur            x1, [fp, #-8]
    // 0x738704: ldur            x2, [fp, #-0x10]
    // 0x738708: stur            x0, [fp, #-8]
    // 0x73870c: r0 = spanForAxis()
    //     0x73870c: bl              #0x73879c  ; [package:flutter_layout_grid/src/foundation/placement.dart] GridArea::spanForAxis
    // 0x738710: sub             x1, x0, #1
    // 0x738714: scvtf           d0, x1
    // 0x738718: stur            d0, [fp, #-0x20]
    // 0x73871c: r16 = <double>
    //     0x73871c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x738720: ldur            lr, [fp, #-8]
    // 0x738724: stp             lr, x16, [SP]
    // 0x738728: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x738728: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x73872c: r0 = sum()
    //     0x73872c: bl              #0x7355b8  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::sum
    // 0x738730: LoadField: d1 = r0->field_7
    //     0x738730: ldur            d1, [x0, #7]
    // 0x738734: ldur            d2, [fp, #-0x20]
    // 0x738738: fadd            d3, d1, d2
    // 0x73873c: d1 = 0.000000
    //     0x73873c: eor             v1.16b, v1.16b, v1.16b
    // 0x738740: fcmp            d1, d3
    // 0x738744: b.le            #0x738750
    // 0x738748: d0 = 0.000000
    //     0x738748: eor             v0.16b, v0.16b, v0.16b
    // 0x73874c: b               #0x738788
    // 0x738750: fcmp            d3, d1
    // 0x738754: b.le            #0x738760
    // 0x738758: mov             v0.16b, v3.16b
    // 0x73875c: b               #0x738788
    // 0x738760: fcmp            d1, d1
    // 0x738764: b.ne            #0x738774
    // 0x738768: fadd            d2, d3, d1
    // 0x73876c: mov             v0.16b, v2.16b
    // 0x738770: b               #0x738788
    // 0x738774: fcmp            d3, d3
    // 0x738778: b.vc            #0x738784
    // 0x73877c: mov             v0.16b, v3.16b
    // 0x738780: b               #0x738788
    // 0x738784: d0 = 0.000000
    //     0x738784: eor             v0.16b, v0.16b, v0.16b
    // 0x738788: LeaveFrame
    //     0x738788: mov             SP, fp
    //     0x73878c: ldp             fp, lr, [SP], #0x10
    // 0x738790: ret
    //     0x738790: ret             
    // 0x738794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x738794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x738798: b               #0x738678
  }
  _ GridSizingInfo.fromTrackSizeFunctions(/* No info */) {
    // ** addr: 0x739404, size: 0x70
    // 0x739404: EnterFrame
    //     0x739404: stp             fp, lr, [SP, #-0x10]!
    //     0x739408: mov             fp, SP
    // 0x73940c: AllocStack(0x18)
    //     0x73940c: sub             SP, SP, #0x18
    // 0x739410: SetupParameters(GridSizingInfo this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0x739410: stur            x1, [fp, #-8]
    //     0x739414: mov             x16, x2
    //     0x739418: mov             x2, x1
    //     0x73941c: mov             x1, x16
    //     0x739420: mov             x0, x3
    //     0x739424: stur            x3, [fp, #-0x10]
    //     0x739428: stur            x5, [fp, #-0x18]
    // 0x73942c: CheckStackOverflow
    //     0x73942c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x739430: cmp             SP, x16
    //     0x739434: b.ls            #0x73946c
    // 0x739438: r0 = _sizesToTracks()
    //     0x739438: bl              #0x739568  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::_sizesToTracks
    // 0x73943c: ldur            x1, [fp, #-0x10]
    // 0x739440: stur            x0, [fp, #-0x10]
    // 0x739444: r0 = _sizesToTracks()
    //     0x739444: bl              #0x739568  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::_sizesToTracks
    // 0x739448: ldur            x1, [fp, #-8]
    // 0x73944c: ldur            x2, [fp, #-0x10]
    // 0x739450: mov             x3, x0
    // 0x739454: ldur            x5, [fp, #-0x18]
    // 0x739458: r0 = GridSizingInfo()
    //     0x739458: bl              #0x739474  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::GridSizingInfo
    // 0x73945c: r0 = Null
    //     0x73945c: mov             x0, NULL
    // 0x739460: LeaveFrame
    //     0x739460: mov             SP, fp
    //     0x739464: ldp             fp, lr, [SP], #0x10
    // 0x739468: ret
    //     0x739468: ret             
    // 0x73946c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73946c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x739470: b               #0x739438
  }
  _ GridSizingInfo(/* No info */) {
    // ** addr: 0x739474, size: 0xe8
    // 0x739474: EnterFrame
    //     0x739474: stp             fp, lr, [SP, #-0x10]!
    //     0x739478: mov             fp, SP
    // 0x73947c: AllocStack(0x18)
    //     0x73947c: sub             SP, SP, #0x18
    // 0x739480: r0 = false
    //     0x739480: add             x0, NULL, #0x30  ; false
    // 0x739484: d0 = 1.000000
    //     0x739484: fmov            d0, #1.00000000
    // 0x739488: mov             x4, x1
    // 0x73948c: stur            x1, [fp, #-8]
    // 0x739490: mov             x1, x5
    // 0x739494: stur            x2, [fp, #-0x10]
    // 0x739498: stur            x3, [fp, #-0x18]
    // 0x73949c: StoreField: r4->field_2f = rZR
    //     0x73949c: stur            xzr, [x4, #0x2f]
    // 0x7394a0: StoreField: r4->field_37 = rZR
    //     0x7394a0: stur            xzr, [x4, #0x37]
    // 0x7394a4: StoreField: r4->field_3f = rZR
    //     0x7394a4: stur            xzr, [x4, #0x3f]
    // 0x7394a8: StoreField: r4->field_47 = rZR
    //     0x7394a8: stur            xzr, [x4, #0x47]
    // 0x7394ac: StoreField: r4->field_4f = r0
    //     0x7394ac: stur            w0, [x4, #0x4f]
    // 0x7394b0: StoreField: r4->field_53 = r0
    //     0x7394b0: stur            w0, [x4, #0x53]
    // 0x7394b4: StoreField: r4->field_b = d0
    //     0x7394b4: stur            d0, [x4, #0xb]
    // 0x7394b8: StoreField: r4->field_13 = d0
    //     0x7394b8: stur            d0, [x4, #0x13]
    // 0x7394bc: mov             x0, x1
    // 0x7394c0: StoreField: r4->field_23 = r0
    //     0x7394c0: stur            w0, [x4, #0x23]
    //     0x7394c4: ldurb           w16, [x4, #-1]
    //     0x7394c8: ldurb           w17, [x0, #-1]
    //     0x7394cc: and             x16, x17, x16, lsr #2
    //     0x7394d0: tst             x16, HEAP, lsr #32
    //     0x7394d4: b.eq            #0x7394dc
    //     0x7394d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7394dc: r1 = <GridTrack>
    //     0x7394dc: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x7394e0: ldr             x1, [x1, #0x3a8]
    // 0x7394e4: r0 = UnmodifiableListView()
    //     0x7394e4: bl              #0x73955c  ; AllocateUnmodifiableListViewStub -> UnmodifiableListView<X0> (size=0x10)
    // 0x7394e8: mov             x1, x0
    // 0x7394ec: ldur            x0, [fp, #-0x10]
    // 0x7394f0: StoreField: r1->field_b = r0
    //     0x7394f0: stur            w0, [x1, #0xb]
    // 0x7394f4: mov             x0, x1
    // 0x7394f8: ldur            x2, [fp, #-8]
    // 0x7394fc: StoreField: r2->field_1b = r0
    //     0x7394fc: stur            w0, [x2, #0x1b]
    //     0x739500: ldurb           w16, [x2, #-1]
    //     0x739504: ldurb           w17, [x0, #-1]
    //     0x739508: and             x16, x17, x16, lsr #2
    //     0x73950c: tst             x16, HEAP, lsr #32
    //     0x739510: b.eq            #0x739518
    //     0x739514: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x739518: r1 = <GridTrack>
    //     0x739518: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x73951c: ldr             x1, [x1, #0x3a8]
    // 0x739520: r0 = UnmodifiableListView()
    //     0x739520: bl              #0x73955c  ; AllocateUnmodifiableListViewStub -> UnmodifiableListView<X0> (size=0x10)
    // 0x739524: ldur            x1, [fp, #-0x18]
    // 0x739528: StoreField: r0->field_b = r1
    //     0x739528: stur            w1, [x0, #0xb]
    // 0x73952c: ldur            x1, [fp, #-8]
    // 0x739530: StoreField: r1->field_1f = r0
    //     0x739530: stur            w0, [x1, #0x1f]
    //     0x739534: ldurb           w16, [x1, #-1]
    //     0x739538: ldurb           w17, [x0, #-1]
    //     0x73953c: and             x16, x17, x16, lsr #2
    //     0x739540: tst             x16, HEAP, lsr #32
    //     0x739544: b.eq            #0x73954c
    //     0x739548: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73954c: r0 = Null
    //     0x73954c: mov             x0, NULL
    // 0x739550: LeaveFrame
    //     0x739550: mov             SP, fp
    //     0x739554: ldp             fp, lr, [SP], #0x10
    // 0x739558: ret
    //     0x739558: ret             
  }
  _ offsetForArea(/* No info */) {
    // ** addr: 0x77a808, size: 0x1a0
    // 0x77a808: EnterFrame
    //     0x77a808: stp             fp, lr, [SP, #-0x10]!
    //     0x77a80c: mov             fp, SP
    // 0x77a810: AllocStack(0x28)
    //     0x77a810: sub             SP, SP, #0x28
    // 0x77a814: SetupParameters(GridSizingInfo this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x77a814: mov             x0, x1
    //     0x77a818: stur            x1, [fp, #-8]
    //     0x77a81c: stur            x2, [fp, #-0x10]
    // 0x77a820: CheckStackOverflow
    //     0x77a820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77a824: cmp             SP, x16
    //     0x77a828: b.ls            #0x77a990
    // 0x77a82c: LoadField: r1 = r0->field_23
    //     0x77a82c: ldur            w1, [x0, #0x23]
    // 0x77a830: DecompressPointer r1
    //     0x77a830: add             x1, x1, HEAP, lsl #32
    // 0x77a834: r16 = Instance_TextDirection
    //     0x77a834: ldr             x16, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0x77a838: cmp             w1, w16
    // 0x77a83c: b.ne            #0x77a890
    // 0x77a840: mov             x1, x0
    // 0x77a844: r0 = columnStartsWithoutGaps()
    //     0x77a844: bl              #0x77ad0c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::columnStartsWithoutGaps
    // 0x77a848: mov             x3, x0
    // 0x77a84c: ldur            x2, [fp, #-0x10]
    // 0x77a850: LoadField: r4 = r2->field_b
    //     0x77a850: ldur            x4, [x2, #0xb]
    // 0x77a854: LoadField: r0 = r3->field_b
    //     0x77a854: ldur            w0, [x3, #0xb]
    // 0x77a858: r1 = LoadInt32Instr(r0)
    //     0x77a858: sbfx            x1, x0, #1, #0x1f
    // 0x77a85c: mov             x0, x1
    // 0x77a860: mov             x1, x4
    // 0x77a864: cmp             x1, x0
    // 0x77a868: b.hs            #0x77a998
    // 0x77a86c: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0x77a86c: add             x16, x3, x4, lsl #2
    //     0x77a870: ldur            w0, [x16, #0xf]
    // 0x77a874: DecompressPointer r0
    //     0x77a874: add             x0, x0, HEAP, lsl #32
    // 0x77a878: scvtf           d0, x4
    // 0x77a87c: LoadField: d1 = r0->field_7
    //     0x77a87c: ldur            d1, [x0, #7]
    // 0x77a880: fadd            d2, d1, d0
    // 0x77a884: mov             v0.16b, v2.16b
    // 0x77a888: mov             x0, x2
    // 0x77a88c: b               #0x77a924
    // 0x77a890: LoadField: r1 = r0->field_7
    //     0x77a890: ldur            w1, [x0, #7]
    // 0x77a894: DecompressPointer r1
    //     0x77a894: add             x1, x1, HEAP, lsl #32
    // 0x77a898: cmp             w1, NULL
    // 0x77a89c: b.eq            #0x77a99c
    // 0x77a8a0: LoadField: d0 = r1->field_7
    //     0x77a8a0: ldur            d0, [x1, #7]
    // 0x77a8a4: mov             x1, x0
    // 0x77a8a8: stur            d0, [fp, #-0x20]
    // 0x77a8ac: r0 = columnStartsWithoutGaps()
    //     0x77a8ac: bl              #0x77ad0c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::columnStartsWithoutGaps
    // 0x77a8b0: mov             x2, x0
    // 0x77a8b4: ldur            x4, [fp, #-0x10]
    // 0x77a8b8: LoadField: r5 = r4->field_b
    //     0x77a8b8: ldur            x5, [x4, #0xb]
    // 0x77a8bc: stur            x5, [fp, #-0x18]
    // 0x77a8c0: LoadField: r0 = r2->field_b
    //     0x77a8c0: ldur            w0, [x2, #0xb]
    // 0x77a8c4: r1 = LoadInt32Instr(r0)
    //     0x77a8c4: sbfx            x1, x0, #1, #0x1f
    // 0x77a8c8: mov             x0, x1
    // 0x77a8cc: mov             x1, x5
    // 0x77a8d0: cmp             x1, x0
    // 0x77a8d4: b.hs            #0x77a9a0
    // 0x77a8d8: ArrayLoad: r0 = r2[r5]  ; Unknown_4
    //     0x77a8d8: add             x16, x2, x5, lsl #2
    //     0x77a8dc: ldur            w0, [x16, #0xf]
    // 0x77a8e0: DecompressPointer r0
    //     0x77a8e0: add             x0, x0, HEAP, lsl #32
    // 0x77a8e4: LoadField: d0 = r0->field_7
    //     0x77a8e4: ldur            d0, [x0, #7]
    // 0x77a8e8: ldur            d1, [fp, #-0x20]
    // 0x77a8ec: fsub            d2, d1, d0
    // 0x77a8f0: ldur            x1, [fp, #-8]
    // 0x77a8f4: mov             x2, x4
    // 0x77a8f8: stur            d2, [fp, #-0x28]
    // 0x77a8fc: r3 = Instance_Axis
    //     0x77a8fc: ldr             x3, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x77a900: r0 = sizeForAreaOnAxis()
    //     0x77a900: bl              #0x738650  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::sizeForAreaOnAxis
    // 0x77a904: mov             v1.16b, v0.16b
    // 0x77a908: ldur            d0, [fp, #-0x28]
    // 0x77a90c: fsub            d2, d0, d1
    // 0x77a910: ldur            x0, [fp, #-0x18]
    // 0x77a914: scvtf           d0, x0
    // 0x77a918: fsub            d1, d2, d0
    // 0x77a91c: mov             v0.16b, v1.16b
    // 0x77a920: ldur            x0, [fp, #-0x10]
    // 0x77a924: ldur            x1, [fp, #-8]
    // 0x77a928: stur            d0, [fp, #-0x20]
    // 0x77a92c: r0 = rowStartsWithoutGaps()
    //     0x77a92c: bl              #0x77a9a8  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::rowStartsWithoutGaps
    // 0x77a930: mov             x2, x0
    // 0x77a934: ldur            x0, [fp, #-0x10]
    // 0x77a938: LoadField: r3 = r0->field_13
    //     0x77a938: ldur            x3, [x0, #0x13]
    // 0x77a93c: LoadField: r0 = r2->field_b
    //     0x77a93c: ldur            w0, [x2, #0xb]
    // 0x77a940: r1 = LoadInt32Instr(r0)
    //     0x77a940: sbfx            x1, x0, #1, #0x1f
    // 0x77a944: mov             x0, x1
    // 0x77a948: mov             x1, x3
    // 0x77a94c: cmp             x1, x0
    // 0x77a950: b.hs            #0x77a9a4
    // 0x77a954: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0x77a954: add             x16, x2, x3, lsl #2
    //     0x77a958: ldur            w0, [x16, #0xf]
    // 0x77a95c: DecompressPointer r0
    //     0x77a95c: add             x0, x0, HEAP, lsl #32
    // 0x77a960: scvtf           d0, x3
    // 0x77a964: LoadField: d1 = r0->field_7
    //     0x77a964: ldur            d1, [x0, #7]
    // 0x77a968: fadd            d2, d1, d0
    // 0x77a96c: stur            d2, [fp, #-0x28]
    // 0x77a970: r0 = Offset()
    //     0x77a970: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x77a974: ldur            d0, [fp, #-0x20]
    // 0x77a978: StoreField: r0->field_7 = d0
    //     0x77a978: stur            d0, [x0, #7]
    // 0x77a97c: ldur            d0, [fp, #-0x28]
    // 0x77a980: StoreField: r0->field_f = d0
    //     0x77a980: stur            d0, [x0, #0xf]
    // 0x77a984: LeaveFrame
    //     0x77a984: mov             SP, fp
    //     0x77a988: ldp             fp, lr, [SP], #0x10
    // 0x77a98c: ret
    //     0x77a98c: ret             
    // 0x77a990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77a990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77a994: b               #0x77a82c
    // 0x77a998: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x77a998: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x77a99c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77a99c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77a9a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x77a9a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x77a9a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x77a9a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ rowStartsWithoutGaps(/* No info */) {
    // ** addr: 0x77a9a8, size: 0xc8
    // 0x77a9a8: EnterFrame
    //     0x77a9a8: stp             fp, lr, [SP, #-0x10]!
    //     0x77a9ac: mov             fp, SP
    // 0x77a9b0: AllocStack(0x28)
    //     0x77a9b0: sub             SP, SP, #0x28
    // 0x77a9b4: SetupParameters(GridSizingInfo this /* r1 => r0, fp-0x10 */)
    //     0x77a9b4: mov             x0, x1
    //     0x77a9b8: stur            x1, [fp, #-0x10]
    // 0x77a9bc: CheckStackOverflow
    //     0x77a9bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77a9c0: cmp             SP, x16
    //     0x77a9c4: b.ls            #0x77aa68
    // 0x77a9c8: LoadField: r1 = r0->field_2b
    //     0x77a9c8: ldur            w1, [x0, #0x2b]
    // 0x77a9cc: DecompressPointer r1
    //     0x77a9cc: add             x1, x1, HEAP, lsl #32
    // 0x77a9d0: cmp             w1, NULL
    // 0x77a9d4: b.ne            #0x77aa58
    // 0x77a9d8: LoadField: r3 = r0->field_1f
    //     0x77a9d8: ldur            w3, [x0, #0x1f]
    // 0x77a9dc: DecompressPointer r3
    //     0x77a9dc: add             x3, x3, HEAP, lsl #32
    // 0x77a9e0: stur            x3, [fp, #-8]
    // 0x77a9e4: r1 = Function '<anonymous closure>':.
    //     0x77a9e4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c688] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x77a9e8: ldr             x1, [x1, #0x688]
    // 0x77a9ec: r2 = Null
    //     0x77a9ec: mov             x2, NULL
    // 0x77a9f0: r0 = AllocateClosure()
    //     0x77a9f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x77a9f4: r16 = <double>
    //     0x77a9f4: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x77a9f8: ldur            lr, [fp, #-8]
    // 0x77a9fc: stp             lr, x16, [SP, #8]
    // 0x77aa00: str             x0, [SP]
    // 0x77aa04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x77aa04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x77aa08: r0 = map()
    //     0x77aa08: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x77aa0c: r16 = <double>
    //     0x77aa0c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x77aa10: stp             x0, x16, [SP]
    // 0x77aa14: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x77aa14: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x77aa18: r0 = cumulativeSum()
    //     0x77aa18: bl              #0x77aa70  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::cumulativeSum
    // 0x77aa1c: LoadField: r1 = r0->field_7
    //     0x77aa1c: ldur            w1, [x0, #7]
    // 0x77aa20: DecompressPointer r1
    //     0x77aa20: add             x1, x1, HEAP, lsl #32
    // 0x77aa24: mov             x2, x0
    // 0x77aa28: r0 = _List.of()
    //     0x77aa28: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x77aa2c: mov             x1, x0
    // 0x77aa30: ldur            x2, [fp, #-0x10]
    // 0x77aa34: StoreField: r2->field_2b = r0
    //     0x77aa34: stur            w0, [x2, #0x2b]
    //     0x77aa38: ldurb           w16, [x2, #-1]
    //     0x77aa3c: ldurb           w17, [x0, #-1]
    //     0x77aa40: and             x16, x17, x16, lsr #2
    //     0x77aa44: tst             x16, HEAP, lsr #32
    //     0x77aa48: b.eq            #0x77aa50
    //     0x77aa4c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x77aa50: mov             x0, x1
    // 0x77aa54: b               #0x77aa5c
    // 0x77aa58: mov             x0, x1
    // 0x77aa5c: LeaveFrame
    //     0x77aa5c: mov             SP, fp
    //     0x77aa60: ldp             fp, lr, [SP], #0x10
    // 0x77aa64: ret
    //     0x77aa64: ret             
    // 0x77aa68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77aa68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77aa6c: b               #0x77a9c8
  }
  get _ columnStartsWithoutGaps(/* No info */) {
    // ** addr: 0x77ad0c, size: 0xc8
    // 0x77ad0c: EnterFrame
    //     0x77ad0c: stp             fp, lr, [SP, #-0x10]!
    //     0x77ad10: mov             fp, SP
    // 0x77ad14: AllocStack(0x28)
    //     0x77ad14: sub             SP, SP, #0x28
    // 0x77ad18: SetupParameters(GridSizingInfo this /* r1 => r0, fp-0x10 */)
    //     0x77ad18: mov             x0, x1
    //     0x77ad1c: stur            x1, [fp, #-0x10]
    // 0x77ad20: CheckStackOverflow
    //     0x77ad20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77ad24: cmp             SP, x16
    //     0x77ad28: b.ls            #0x77adcc
    // 0x77ad2c: LoadField: r1 = r0->field_27
    //     0x77ad2c: ldur            w1, [x0, #0x27]
    // 0x77ad30: DecompressPointer r1
    //     0x77ad30: add             x1, x1, HEAP, lsl #32
    // 0x77ad34: cmp             w1, NULL
    // 0x77ad38: b.ne            #0x77adbc
    // 0x77ad3c: LoadField: r3 = r0->field_1b
    //     0x77ad3c: ldur            w3, [x0, #0x1b]
    // 0x77ad40: DecompressPointer r3
    //     0x77ad40: add             x3, x3, HEAP, lsl #32
    // 0x77ad44: stur            x3, [fp, #-8]
    // 0x77ad48: r1 = Function '<anonymous closure>':.
    //     0x77ad48: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c6b8] AnonymousClosure: (0x7357fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize (0x73543c)
    //     0x77ad4c: ldr             x1, [x1, #0x6b8]
    // 0x77ad50: r2 = Null
    //     0x77ad50: mov             x2, NULL
    // 0x77ad54: r0 = AllocateClosure()
    //     0x77ad54: bl              #0xec1630  ; AllocateClosureStub
    // 0x77ad58: r16 = <double>
    //     0x77ad58: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x77ad5c: ldur            lr, [fp, #-8]
    // 0x77ad60: stp             lr, x16, [SP, #8]
    // 0x77ad64: str             x0, [SP]
    // 0x77ad68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x77ad68: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x77ad6c: r0 = map()
    //     0x77ad6c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x77ad70: r16 = <double>
    //     0x77ad70: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x77ad74: stp             x0, x16, [SP]
    // 0x77ad78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x77ad78: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x77ad7c: r0 = cumulativeSum()
    //     0x77ad7c: bl              #0x77aa70  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::cumulativeSum
    // 0x77ad80: LoadField: r1 = r0->field_7
    //     0x77ad80: ldur            w1, [x0, #7]
    // 0x77ad84: DecompressPointer r1
    //     0x77ad84: add             x1, x1, HEAP, lsl #32
    // 0x77ad88: mov             x2, x0
    // 0x77ad8c: r0 = _List.of()
    //     0x77ad8c: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x77ad90: mov             x1, x0
    // 0x77ad94: ldur            x2, [fp, #-0x10]
    // 0x77ad98: StoreField: r2->field_27 = r0
    //     0x77ad98: stur            w0, [x2, #0x27]
    //     0x77ad9c: ldurb           w16, [x2, #-1]
    //     0x77ada0: ldurb           w17, [x0, #-1]
    //     0x77ada4: and             x16, x17, x16, lsr #2
    //     0x77ada8: tst             x16, HEAP, lsr #32
    //     0x77adac: b.eq            #0x77adb4
    //     0x77adb0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x77adb4: mov             x0, x1
    // 0x77adb8: b               #0x77adc0
    // 0x77adbc: mov             x0, x1
    // 0x77adc0: LeaveFrame
    //     0x77adc0: mov             SP, fp
    //     0x77adc4: ldp             fp, lr, [SP], #0x10
    // 0x77adc8: ret
    //     0x77adc8: ret             
    // 0x77adcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77adcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77add0: b               #0x77ad2c
  }
}

// class id: 2292, size: 0x2c, field offset: 0x8
class GridTrack extends Object {

  _ _increaseGrowthLimitIfNecessary(/* No info */) {
    // ** addr: 0x735b30, size: 0x28
    // 0x735b30: d0 = inf
    //     0x735b30: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x735b34: LoadField: d1 = r1->field_1b
    //     0x735b34: ldur            d1, [x1, #0x1b]
    // 0x735b38: fcmp            d1, d0
    // 0x735b3c: b.eq            #0x735b50
    // 0x735b40: LoadField: d0 = r1->field_13
    //     0x735b40: ldur            d0, [x1, #0x13]
    // 0x735b44: fcmp            d0, d1
    // 0x735b48: b.le            #0x735b50
    // 0x735b4c: StoreField: r1->field_1b = d0
    //     0x735b4c: stur            d0, [x1, #0x1b]
    // 0x735b50: r0 = Null
    //     0x735b50: mov             x0, NULL
    // 0x735b54: ret
    //     0x735b54: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc28030, size: 0x114
    // 0xc28030: EnterFrame
    //     0xc28030: stp             fp, lr, [SP, #-0x10]!
    //     0xc28034: mov             fp, SP
    // 0xc28038: AllocStack(0x8)
    //     0xc28038: sub             SP, SP, #8
    // 0xc2803c: CheckStackOverflow
    //     0xc2803c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc28040: cmp             SP, x16
    //     0xc28044: b.ls            #0xc28104
    // 0xc28048: r1 = Null
    //     0xc28048: mov             x1, NULL
    // 0xc2804c: r2 = 14
    //     0xc2804c: movz            x2, #0xe
    // 0xc28050: r0 = AllocateArray()
    //     0xc28050: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc28054: r16 = "GridTrack(baseSize="
    //     0xc28054: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e20] "GridTrack(baseSize="
    //     0xc28058: ldr             x16, [x16, #0xe20]
    // 0xc2805c: StoreField: r0->field_f = r16
    //     0xc2805c: stur            w16, [x0, #0xf]
    // 0xc28060: ldr             x1, [fp, #0x10]
    // 0xc28064: LoadField: d0 = r1->field_13
    //     0xc28064: ldur            d0, [x1, #0x13]
    // 0xc28068: r2 = inline_Allocate_Double()
    //     0xc28068: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc2806c: add             x2, x2, #0x10
    //     0xc28070: cmp             x3, x2
    //     0xc28074: b.ls            #0xc2810c
    //     0xc28078: str             x2, [THR, #0x50]  ; THR::top
    //     0xc2807c: sub             x2, x2, #0xf
    //     0xc28080: movz            x3, #0xe15c
    //     0xc28084: movk            x3, #0x3, lsl #16
    //     0xc28088: stur            x3, [x2, #-1]
    // 0xc2808c: StoreField: r2->field_7 = d0
    //     0xc2808c: stur            d0, [x2, #7]
    // 0xc28090: StoreField: r0->field_13 = r2
    //     0xc28090: stur            w2, [x0, #0x13]
    // 0xc28094: r16 = ", growthLimit="
    //     0xc28094: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e28] ", growthLimit="
    //     0xc28098: ldr             x16, [x16, #0xe28]
    // 0xc2809c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc2809c: stur            w16, [x0, #0x17]
    // 0xc280a0: LoadField: d0 = r1->field_1b
    //     0xc280a0: ldur            d0, [x1, #0x1b]
    // 0xc280a4: r2 = inline_Allocate_Double()
    //     0xc280a4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc280a8: add             x2, x2, #0x10
    //     0xc280ac: cmp             x3, x2
    //     0xc280b0: b.ls            #0xc28128
    //     0xc280b4: str             x2, [THR, #0x50]  ; THR::top
    //     0xc280b8: sub             x2, x2, #0xf
    //     0xc280bc: movz            x3, #0xe15c
    //     0xc280c0: movk            x3, #0x3, lsl #16
    //     0xc280c4: stur            x3, [x2, #-1]
    // 0xc280c8: StoreField: r2->field_7 = d0
    //     0xc280c8: stur            d0, [x2, #7]
    // 0xc280cc: StoreField: r0->field_1b = r2
    //     0xc280cc: stur            w2, [x0, #0x1b]
    // 0xc280d0: r16 = ", sizeFunction="
    //     0xc280d0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e30] ", sizeFunction="
    //     0xc280d4: ldr             x16, [x16, #0xe30]
    // 0xc280d8: StoreField: r0->field_1f = r16
    //     0xc280d8: stur            w16, [x0, #0x1f]
    // 0xc280dc: LoadField: r2 = r1->field_f
    //     0xc280dc: ldur            w2, [x1, #0xf]
    // 0xc280e0: DecompressPointer r2
    //     0xc280e0: add             x2, x2, HEAP, lsl #32
    // 0xc280e4: StoreField: r0->field_23 = r2
    //     0xc280e4: stur            w2, [x0, #0x23]
    // 0xc280e8: r16 = ")"
    //     0xc280e8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc280ec: StoreField: r0->field_27 = r16
    //     0xc280ec: stur            w16, [x0, #0x27]
    // 0xc280f0: str             x0, [SP]
    // 0xc280f4: r0 = _interpolate()
    //     0xc280f4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc280f8: LeaveFrame
    //     0xc280f8: mov             SP, fp
    //     0xc280fc: ldp             fp, lr, [SP], #0x10
    // 0xc28100: ret
    //     0xc28100: ret             
    // 0xc28104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28108: b               #0xc28048
    // 0xc2810c: SaveReg d0
    //     0xc2810c: str             q0, [SP, #-0x10]!
    // 0xc28110: stp             x0, x1, [SP, #-0x10]!
    // 0xc28114: r0 = AllocateDouble()
    //     0xc28114: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc28118: mov             x2, x0
    // 0xc2811c: ldp             x0, x1, [SP], #0x10
    // 0xc28120: RestoreReg d0
    //     0xc28120: ldr             q0, [SP], #0x10
    // 0xc28124: b               #0xc2808c
    // 0xc28128: SaveReg d0
    //     0xc28128: str             q0, [SP, #-0x10]!
    // 0xc2812c: stp             x0, x1, [SP, #-0x10]!
    // 0xc28130: r0 = AllocateDouble()
    //     0xc28130: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc28134: mov             x2, x0
    // 0xc28138: ldp             x0, x1, [SP], #0x10
    // 0xc2813c: RestoreReg d0
    //     0xc2813c: ldr             q0, [SP], #0x10
    // 0xc28140: b               #0xc280c8
  }
}

// class id: 3003, size: 0x68, field offset: 0x58
//   transformed mixin,
abstract class _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin extends RenderBox
     with ContainerRenderObjectMixin<X0 bound RenderObject, X1 bound ContainerParentDataMixin> {

  _ attach(/* No info */) {
    // ** addr: 0x764864, size: 0xfc
    // 0x764864: EnterFrame
    //     0x764864: stp             fp, lr, [SP, #-0x10]!
    //     0x764868: mov             fp, SP
    // 0x76486c: AllocStack(0x18)
    //     0x76486c: sub             SP, SP, #0x18
    // 0x764870: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x764870: mov             x3, x1
    //     0x764874: mov             x0, x2
    //     0x764878: stur            x1, [fp, #-8]
    //     0x76487c: stur            x2, [fp, #-0x10]
    // 0x764880: CheckStackOverflow
    //     0x764880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x764884: cmp             SP, x16
    //     0x764888: b.ls            #0x76494c
    // 0x76488c: mov             x1, x3
    // 0x764890: mov             x2, x0
    // 0x764894: r0 = attach()
    //     0x764894: bl              #0x765268  ; [package:flutter/src/rendering/object.dart] RenderObject::attach
    // 0x764898: ldur            x0, [fp, #-8]
    // 0x76489c: LoadField: r1 = r0->field_5f
    //     0x76489c: ldur            w1, [x0, #0x5f]
    // 0x7648a0: DecompressPointer r1
    //     0x7648a0: add             x1, x1, HEAP, lsl #32
    // 0x7648a4: mov             x3, x1
    // 0x7648a8: stur            x3, [fp, #-8]
    // 0x7648ac: CheckStackOverflow
    //     0x7648ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7648b0: cmp             SP, x16
    //     0x7648b4: b.ls            #0x764954
    // 0x7648b8: cmp             w3, NULL
    // 0x7648bc: b.eq            #0x76493c
    // 0x7648c0: r0 = LoadClassIdInstr(r3)
    //     0x7648c0: ldur            x0, [x3, #-1]
    //     0x7648c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7648c8: mov             x1, x3
    // 0x7648cc: ldur            x2, [fp, #-0x10]
    // 0x7648d0: r0 = GDT[cid_x0 + 0x11974]()
    //     0x7648d0: movz            x17, #0x1974
    //     0x7648d4: movk            x17, #0x1, lsl #16
    //     0x7648d8: add             lr, x0, x17
    //     0x7648dc: ldr             lr, [x21, lr, lsl #3]
    //     0x7648e0: blr             lr
    // 0x7648e4: ldur            x0, [fp, #-8]
    // 0x7648e8: LoadField: r3 = r0->field_7
    //     0x7648e8: ldur            w3, [x0, #7]
    // 0x7648ec: DecompressPointer r3
    //     0x7648ec: add             x3, x3, HEAP, lsl #32
    // 0x7648f0: stur            x3, [fp, #-0x18]
    // 0x7648f4: cmp             w3, NULL
    // 0x7648f8: b.eq            #0x76495c
    // 0x7648fc: mov             x0, x3
    // 0x764900: r2 = Null
    //     0x764900: mov             x2, NULL
    // 0x764904: r1 = Null
    //     0x764904: mov             x1, NULL
    // 0x764908: r4 = LoadClassIdInstr(r0)
    //     0x764908: ldur            x4, [x0, #-1]
    //     0x76490c: ubfx            x4, x4, #0xc, #0x14
    // 0x764910: cmp             x4, #0xc75
    // 0x764914: b.eq            #0x76492c
    // 0x764918: r8 = GridParentData
    //     0x764918: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x76491c: ldr             x8, [x8, #0xca0]
    // 0x764920: r3 = Null
    //     0x764920: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c158] Null
    //     0x764924: ldr             x3, [x3, #0x158]
    // 0x764928: r0 = DefaultTypeTest()
    //     0x764928: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x76492c: ldur            x1, [fp, #-0x18]
    // 0x764930: LoadField: r3 = r1->field_13
    //     0x764930: ldur            w3, [x1, #0x13]
    // 0x764934: DecompressPointer r3
    //     0x764934: add             x3, x3, HEAP, lsl #32
    // 0x764938: b               #0x7648a8
    // 0x76493c: r0 = Null
    //     0x76493c: mov             x0, NULL
    // 0x764940: LeaveFrame
    //     0x764940: mov             SP, fp
    //     0x764944: ldp             fp, lr, [SP], #0x10
    // 0x764948: ret
    //     0x764948: ret             
    // 0x76494c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76494c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x764950: b               #0x76488c
    // 0x764954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x764954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x764958: b               #0x7648b8
    // 0x76495c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76495c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x7876c4, size: 0xd8
    // 0x7876c4: EnterFrame
    //     0x7876c4: stp             fp, lr, [SP, #-0x10]!
    //     0x7876c8: mov             fp, SP
    // 0x7876cc: AllocStack(0x28)
    //     0x7876cc: sub             SP, SP, #0x28
    // 0x7876d0: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7876d0: mov             x0, x1
    //     0x7876d4: mov             x1, x2
    //     0x7876d8: stur            x2, [fp, #-0x10]
    // 0x7876dc: CheckStackOverflow
    //     0x7876dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7876e0: cmp             SP, x16
    //     0x7876e4: b.ls            #0x787788
    // 0x7876e8: LoadField: r2 = r0->field_5f
    //     0x7876e8: ldur            w2, [x0, #0x5f]
    // 0x7876ec: DecompressPointer r2
    //     0x7876ec: add             x2, x2, HEAP, lsl #32
    // 0x7876f0: stur            x2, [fp, #-8]
    // 0x7876f4: CheckStackOverflow
    //     0x7876f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7876f8: cmp             SP, x16
    //     0x7876fc: b.ls            #0x787790
    // 0x787700: cmp             w2, NULL
    // 0x787704: b.eq            #0x787778
    // 0x787708: stp             x2, x1, [SP]
    // 0x78770c: mov             x0, x1
    // 0x787710: ClosureCall
    //     0x787710: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x787714: ldur            x2, [x0, #0x1f]
    //     0x787718: blr             x2
    // 0x78771c: ldur            x0, [fp, #-8]
    // 0x787720: LoadField: r3 = r0->field_7
    //     0x787720: ldur            w3, [x0, #7]
    // 0x787724: DecompressPointer r3
    //     0x787724: add             x3, x3, HEAP, lsl #32
    // 0x787728: stur            x3, [fp, #-0x18]
    // 0x78772c: cmp             w3, NULL
    // 0x787730: b.eq            #0x787798
    // 0x787734: mov             x0, x3
    // 0x787738: r2 = Null
    //     0x787738: mov             x2, NULL
    // 0x78773c: r1 = Null
    //     0x78773c: mov             x1, NULL
    // 0x787740: r4 = LoadClassIdInstr(r0)
    //     0x787740: ldur            x4, [x0, #-1]
    //     0x787744: ubfx            x4, x4, #0xc, #0x14
    // 0x787748: cmp             x4, #0xc75
    // 0x78774c: b.eq            #0x787764
    // 0x787750: r8 = GridParentData
    //     0x787750: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x787754: ldr             x8, [x8, #0xca0]
    // 0x787758: r3 = Null
    //     0x787758: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c128] Null
    //     0x78775c: ldr             x3, [x3, #0x128]
    // 0x787760: r0 = DefaultTypeTest()
    //     0x787760: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x787764: ldur            x1, [fp, #-0x18]
    // 0x787768: LoadField: r2 = r1->field_13
    //     0x787768: ldur            w2, [x1, #0x13]
    // 0x78776c: DecompressPointer r2
    //     0x78776c: add             x2, x2, HEAP, lsl #32
    // 0x787770: ldur            x1, [fp, #-0x10]
    // 0x787774: b               #0x7876f0
    // 0x787778: r0 = Null
    //     0x787778: mov             x0, NULL
    // 0x78777c: LeaveFrame
    //     0x78777c: mov             SP, fp
    //     0x787780: ldp             fp, lr, [SP], #0x10
    // 0x787784: ret
    //     0x787784: ret             
    // 0x787788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x787788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78778c: b               #0x7876e8
    // 0x787790: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x787790: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x787794: b               #0x787700
    // 0x787798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x787798: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7b15fc, size: 0xd0
    // 0x7b15fc: EnterFrame
    //     0x7b15fc: stp             fp, lr, [SP, #-0x10]!
    //     0x7b1600: mov             fp, SP
    // 0x7b1604: AllocStack(0x18)
    //     0x7b1604: sub             SP, SP, #0x18
    // 0x7b1608: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7b1608: mov             x5, x1
    //     0x7b160c: mov             x4, x2
    //     0x7b1610: stur            x1, [fp, #-8]
    //     0x7b1614: stur            x2, [fp, #-0x10]
    //     0x7b1618: stur            x3, [fp, #-0x18]
    // 0x7b161c: CheckStackOverflow
    //     0x7b161c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b1620: cmp             SP, x16
    //     0x7b1624: b.ls            #0x7b16c4
    // 0x7b1628: mov             x0, x4
    // 0x7b162c: r2 = Null
    //     0x7b162c: mov             x2, NULL
    // 0x7b1630: r1 = Null
    //     0x7b1630: mov             x1, NULL
    // 0x7b1634: r4 = 60
    //     0x7b1634: movz            x4, #0x3c
    // 0x7b1638: branchIfSmi(r0, 0x7b1644)
    //     0x7b1638: tbz             w0, #0, #0x7b1644
    // 0x7b163c: r4 = LoadClassIdInstr(r0)
    //     0x7b163c: ldur            x4, [x0, #-1]
    //     0x7b1640: ubfx            x4, x4, #0xc, #0x14
    // 0x7b1644: sub             x4, x4, #0xbba
    // 0x7b1648: cmp             x4, #0x9a
    // 0x7b164c: b.ls            #0x7b1660
    // 0x7b1650: r8 = RenderBox
    //     0x7b1650: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b1654: r3 = Null
    //     0x7b1654: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2e8] Null
    //     0x7b1658: ldr             x3, [x3, #0x2e8]
    // 0x7b165c: r0 = RenderBox()
    //     0x7b165c: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b1660: ldur            x0, [fp, #-0x18]
    // 0x7b1664: r2 = Null
    //     0x7b1664: mov             x2, NULL
    // 0x7b1668: r1 = Null
    //     0x7b1668: mov             x1, NULL
    // 0x7b166c: r4 = 60
    //     0x7b166c: movz            x4, #0x3c
    // 0x7b1670: branchIfSmi(r0, 0x7b167c)
    //     0x7b1670: tbz             w0, #0, #0x7b167c
    // 0x7b1674: r4 = LoadClassIdInstr(r0)
    //     0x7b1674: ldur            x4, [x0, #-1]
    //     0x7b1678: ubfx            x4, x4, #0xc, #0x14
    // 0x7b167c: sub             x4, x4, #0xbba
    // 0x7b1680: cmp             x4, #0x9a
    // 0x7b1684: b.ls            #0x7b1698
    // 0x7b1688: r8 = RenderBox?
    //     0x7b1688: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7b168c: r3 = Null
    //     0x7b168c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2f8] Null
    //     0x7b1690: ldr             x3, [x3, #0x2f8]
    // 0x7b1694: r0 = RenderBox?()
    //     0x7b1694: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7b1698: ldur            x1, [fp, #-8]
    // 0x7b169c: ldur            x2, [fp, #-0x10]
    // 0x7b16a0: r0 = adoptChild()
    //     0x7b16a0: bl              #0x805624  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::adoptChild
    // 0x7b16a4: ldur            x1, [fp, #-8]
    // 0x7b16a8: ldur            x2, [fp, #-0x10]
    // 0x7b16ac: ldur            x3, [fp, #-0x18]
    // 0x7b16b0: r0 = _insertIntoChildList()
    //     0x7b16b0: bl              #0xda8018  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7b16b4: r0 = Null
    //     0x7b16b4: mov             x0, NULL
    // 0x7b16b8: LeaveFrame
    //     0x7b16b8: mov             SP, fp
    //     0x7b16bc: ldp             fp, lr, [SP], #0x10
    // 0x7b16c0: ret
    //     0x7b16c0: ret             
    // 0x7b16c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b16c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b16c8: b               #0x7b1628
  }
  _ remove(/* No info */) {
    // ** addr: 0x7b46d4, size: 0x90
    // 0x7b46d4: EnterFrame
    //     0x7b46d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7b46d8: mov             fp, SP
    // 0x7b46dc: AllocStack(0x10)
    //     0x7b46dc: sub             SP, SP, #0x10
    // 0x7b46e0: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7b46e0: mov             x4, x1
    //     0x7b46e4: mov             x3, x2
    //     0x7b46e8: stur            x1, [fp, #-8]
    //     0x7b46ec: stur            x2, [fp, #-0x10]
    // 0x7b46f0: CheckStackOverflow
    //     0x7b46f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7b46f4: cmp             SP, x16
    //     0x7b46f8: b.ls            #0x7b475c
    // 0x7b46fc: mov             x0, x3
    // 0x7b4700: r2 = Null
    //     0x7b4700: mov             x2, NULL
    // 0x7b4704: r1 = Null
    //     0x7b4704: mov             x1, NULL
    // 0x7b4708: r4 = 60
    //     0x7b4708: movz            x4, #0x3c
    // 0x7b470c: branchIfSmi(r0, 0x7b4718)
    //     0x7b470c: tbz             w0, #0, #0x7b4718
    // 0x7b4710: r4 = LoadClassIdInstr(r0)
    //     0x7b4710: ldur            x4, [x0, #-1]
    //     0x7b4714: ubfx            x4, x4, #0xc, #0x14
    // 0x7b4718: sub             x4, x4, #0xbba
    // 0x7b471c: cmp             x4, #0x9a
    // 0x7b4720: b.ls            #0x7b4734
    // 0x7b4724: r8 = RenderBox
    //     0x7b4724: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7b4728: r3 = Null
    //     0x7b4728: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2c0] Null
    //     0x7b472c: ldr             x3, [x3, #0x2c0]
    // 0x7b4730: r0 = RenderBox()
    //     0x7b4730: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7b4734: ldur            x1, [fp, #-8]
    // 0x7b4738: ldur            x2, [fp, #-0x10]
    // 0x7b473c: r0 = _removeFromChildList()
    //     0x7b473c: bl              #0x7b4764  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7b4740: ldur            x1, [fp, #-8]
    // 0x7b4744: ldur            x2, [fp, #-0x10]
    // 0x7b4748: r0 = dropChild()
    //     0x7b4748: bl              #0x8016bc  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::dropChild
    // 0x7b474c: r0 = Null
    //     0x7b474c: mov             x0, NULL
    // 0x7b4750: LeaveFrame
    //     0x7b4750: mov             SP, fp
    //     0x7b4754: ldp             fp, lr, [SP], #0x10
    // 0x7b4758: ret
    //     0x7b4758: ret             
    // 0x7b475c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7b475c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7b4760: b               #0x7b46fc
  }
  _ _removeFromChildList(/* No info */) {
    // ** addr: 0x7b4764, size: 0x2c8
    // 0x7b4764: EnterFrame
    //     0x7b4764: stp             fp, lr, [SP, #-0x10]!
    //     0x7b4768: mov             fp, SP
    // 0x7b476c: AllocStack(0x28)
    //     0x7b476c: sub             SP, SP, #0x28
    // 0x7b4770: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r3, fp-0x10 */)
    //     0x7b4770: mov             x3, x1
    //     0x7b4774: stur            x1, [fp, #-0x10]
    // 0x7b4778: LoadField: r4 = r2->field_7
    //     0x7b4778: ldur            w4, [x2, #7]
    // 0x7b477c: DecompressPointer r4
    //     0x7b477c: add             x4, x4, HEAP, lsl #32
    // 0x7b4780: stur            x4, [fp, #-8]
    // 0x7b4784: cmp             w4, NULL
    // 0x7b4788: b.eq            #0x7b4a20
    // 0x7b478c: mov             x0, x4
    // 0x7b4790: r2 = Null
    //     0x7b4790: mov             x2, NULL
    // 0x7b4794: r1 = Null
    //     0x7b4794: mov             x1, NULL
    // 0x7b4798: r4 = LoadClassIdInstr(r0)
    //     0x7b4798: ldur            x4, [x0, #-1]
    //     0x7b479c: ubfx            x4, x4, #0xc, #0x14
    // 0x7b47a0: cmp             x4, #0xc75
    // 0x7b47a4: b.eq            #0x7b47bc
    // 0x7b47a8: r8 = GridParentData
    //     0x7b47a8: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x7b47ac: ldr             x8, [x8, #0xca0]
    // 0x7b47b0: r3 = Null
    //     0x7b47b0: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c260] Null
    //     0x7b47b4: ldr             x3, [x3, #0x260]
    // 0x7b47b8: r0 = DefaultTypeTest()
    //     0x7b47b8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b47bc: ldur            x3, [fp, #-8]
    // 0x7b47c0: LoadField: r4 = r3->field_f
    //     0x7b47c0: ldur            w4, [x3, #0xf]
    // 0x7b47c4: DecompressPointer r4
    //     0x7b47c4: add             x4, x4, HEAP, lsl #32
    // 0x7b47c8: stur            x4, [fp, #-0x20]
    // 0x7b47cc: cmp             w4, NULL
    // 0x7b47d0: b.ne            #0x7b4800
    // 0x7b47d4: ldur            x5, [fp, #-0x10]
    // 0x7b47d8: LoadField: r0 = r3->field_13
    //     0x7b47d8: ldur            w0, [x3, #0x13]
    // 0x7b47dc: DecompressPointer r0
    //     0x7b47dc: add             x0, x0, HEAP, lsl #32
    // 0x7b47e0: StoreField: r5->field_5f = r0
    //     0x7b47e0: stur            w0, [x5, #0x5f]
    //     0x7b47e4: ldurb           w16, [x5, #-1]
    //     0x7b47e8: ldurb           w17, [x0, #-1]
    //     0x7b47ec: and             x16, x17, x16, lsr #2
    //     0x7b47f0: tst             x16, HEAP, lsr #32
    //     0x7b47f4: b.eq            #0x7b47fc
    //     0x7b47f8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x7b47fc: b               #0x7b48c4
    // 0x7b4800: ldur            x5, [fp, #-0x10]
    // 0x7b4804: LoadField: r6 = r4->field_7
    //     0x7b4804: ldur            w6, [x4, #7]
    // 0x7b4808: DecompressPointer r6
    //     0x7b4808: add             x6, x6, HEAP, lsl #32
    // 0x7b480c: stur            x6, [fp, #-0x18]
    // 0x7b4810: cmp             w6, NULL
    // 0x7b4814: b.eq            #0x7b4a24
    // 0x7b4818: mov             x0, x6
    // 0x7b481c: r2 = Null
    //     0x7b481c: mov             x2, NULL
    // 0x7b4820: r1 = Null
    //     0x7b4820: mov             x1, NULL
    // 0x7b4824: r4 = LoadClassIdInstr(r0)
    //     0x7b4824: ldur            x4, [x0, #-1]
    //     0x7b4828: ubfx            x4, x4, #0xc, #0x14
    // 0x7b482c: cmp             x4, #0xc75
    // 0x7b4830: b.eq            #0x7b4848
    // 0x7b4834: r8 = GridParentData
    //     0x7b4834: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x7b4838: ldr             x8, [x8, #0xca0]
    // 0x7b483c: r3 = Null
    //     0x7b483c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c270] Null
    //     0x7b4840: ldr             x3, [x3, #0x270]
    // 0x7b4844: r0 = DefaultTypeTest()
    //     0x7b4844: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b4848: ldur            x3, [fp, #-8]
    // 0x7b484c: LoadField: r4 = r3->field_13
    //     0x7b484c: ldur            w4, [x3, #0x13]
    // 0x7b4850: DecompressPointer r4
    //     0x7b4850: add             x4, x4, HEAP, lsl #32
    // 0x7b4854: ldur            x5, [fp, #-0x18]
    // 0x7b4858: stur            x4, [fp, #-0x28]
    // 0x7b485c: LoadField: r2 = r5->field_b
    //     0x7b485c: ldur            w2, [x5, #0xb]
    // 0x7b4860: DecompressPointer r2
    //     0x7b4860: add             x2, x2, HEAP, lsl #32
    // 0x7b4864: mov             x0, x4
    // 0x7b4868: r1 = Null
    //     0x7b4868: mov             x1, NULL
    // 0x7b486c: cmp             w0, NULL
    // 0x7b4870: b.eq            #0x7b489c
    // 0x7b4874: cmp             w2, NULL
    // 0x7b4878: b.eq            #0x7b489c
    // 0x7b487c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b487c: ldur            w4, [x2, #0x17]
    // 0x7b4880: DecompressPointer r4
    //     0x7b4880: add             x4, x4, HEAP, lsl #32
    // 0x7b4884: r8 = X0? bound RenderObject
    //     0x7b4884: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b4888: ldr             x8, [x8, #0x1a8]
    // 0x7b488c: LoadField: r9 = r4->field_7
    //     0x7b488c: ldur            x9, [x4, #7]
    // 0x7b4890: r3 = Null
    //     0x7b4890: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c280] Null
    //     0x7b4894: ldr             x3, [x3, #0x280]
    // 0x7b4898: blr             x9
    // 0x7b489c: ldur            x0, [fp, #-0x28]
    // 0x7b48a0: ldur            x1, [fp, #-0x18]
    // 0x7b48a4: StoreField: r1->field_13 = r0
    //     0x7b48a4: stur            w0, [x1, #0x13]
    //     0x7b48a8: ldurb           w16, [x1, #-1]
    //     0x7b48ac: ldurb           w17, [x0, #-1]
    //     0x7b48b0: and             x16, x17, x16, lsr #2
    //     0x7b48b4: tst             x16, HEAP, lsr #32
    //     0x7b48b8: b.eq            #0x7b48c0
    //     0x7b48bc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b48c0: ldur            x3, [fp, #-8]
    // 0x7b48c4: LoadField: r0 = r3->field_13
    //     0x7b48c4: ldur            w0, [x3, #0x13]
    // 0x7b48c8: DecompressPointer r0
    //     0x7b48c8: add             x0, x0, HEAP, lsl #32
    // 0x7b48cc: cmp             w0, NULL
    // 0x7b48d0: b.ne            #0x7b48fc
    // 0x7b48d4: ldur            x4, [fp, #-0x10]
    // 0x7b48d8: ldur            x0, [fp, #-0x20]
    // 0x7b48dc: StoreField: r4->field_63 = r0
    //     0x7b48dc: stur            w0, [x4, #0x63]
    //     0x7b48e0: ldurb           w16, [x4, #-1]
    //     0x7b48e4: ldurb           w17, [x0, #-1]
    //     0x7b48e8: and             x16, x17, x16, lsr #2
    //     0x7b48ec: tst             x16, HEAP, lsr #32
    //     0x7b48f0: b.eq            #0x7b48f8
    //     0x7b48f4: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x7b48f8: b               #0x7b49b4
    // 0x7b48fc: ldur            x4, [fp, #-0x10]
    // 0x7b4900: LoadField: r5 = r0->field_7
    //     0x7b4900: ldur            w5, [x0, #7]
    // 0x7b4904: DecompressPointer r5
    //     0x7b4904: add             x5, x5, HEAP, lsl #32
    // 0x7b4908: stur            x5, [fp, #-0x18]
    // 0x7b490c: cmp             w5, NULL
    // 0x7b4910: b.eq            #0x7b4a28
    // 0x7b4914: mov             x0, x5
    // 0x7b4918: r2 = Null
    //     0x7b4918: mov             x2, NULL
    // 0x7b491c: r1 = Null
    //     0x7b491c: mov             x1, NULL
    // 0x7b4920: r4 = LoadClassIdInstr(r0)
    //     0x7b4920: ldur            x4, [x0, #-1]
    //     0x7b4924: ubfx            x4, x4, #0xc, #0x14
    // 0x7b4928: cmp             x4, #0xc75
    // 0x7b492c: b.eq            #0x7b4944
    // 0x7b4930: r8 = GridParentData
    //     0x7b4930: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x7b4934: ldr             x8, [x8, #0xca0]
    // 0x7b4938: r3 = Null
    //     0x7b4938: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c290] Null
    //     0x7b493c: ldr             x3, [x3, #0x290]
    // 0x7b4940: r0 = DefaultTypeTest()
    //     0x7b4940: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7b4944: ldur            x3, [fp, #-0x18]
    // 0x7b4948: LoadField: r2 = r3->field_b
    //     0x7b4948: ldur            w2, [x3, #0xb]
    // 0x7b494c: DecompressPointer r2
    //     0x7b494c: add             x2, x2, HEAP, lsl #32
    // 0x7b4950: ldur            x0, [fp, #-0x20]
    // 0x7b4954: r1 = Null
    //     0x7b4954: mov             x1, NULL
    // 0x7b4958: cmp             w0, NULL
    // 0x7b495c: b.eq            #0x7b4988
    // 0x7b4960: cmp             w2, NULL
    // 0x7b4964: b.eq            #0x7b4988
    // 0x7b4968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b4968: ldur            w4, [x2, #0x17]
    // 0x7b496c: DecompressPointer r4
    //     0x7b496c: add             x4, x4, HEAP, lsl #32
    // 0x7b4970: r8 = X0? bound RenderObject
    //     0x7b4970: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b4974: ldr             x8, [x8, #0x1a8]
    // 0x7b4978: LoadField: r9 = r4->field_7
    //     0x7b4978: ldur            x9, [x4, #7]
    // 0x7b497c: r3 = Null
    //     0x7b497c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2a0] Null
    //     0x7b4980: ldr             x3, [x3, #0x2a0]
    // 0x7b4984: blr             x9
    // 0x7b4988: ldur            x0, [fp, #-0x20]
    // 0x7b498c: ldur            x1, [fp, #-0x18]
    // 0x7b4990: StoreField: r1->field_f = r0
    //     0x7b4990: stur            w0, [x1, #0xf]
    //     0x7b4994: ldurb           w16, [x1, #-1]
    //     0x7b4998: ldurb           w17, [x0, #-1]
    //     0x7b499c: and             x16, x17, x16, lsr #2
    //     0x7b49a0: tst             x16, HEAP, lsr #32
    //     0x7b49a4: b.eq            #0x7b49ac
    //     0x7b49a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7b49ac: ldur            x4, [fp, #-0x10]
    // 0x7b49b0: ldur            x3, [fp, #-8]
    // 0x7b49b4: LoadField: r2 = r3->field_b
    //     0x7b49b4: ldur            w2, [x3, #0xb]
    // 0x7b49b8: DecompressPointer r2
    //     0x7b49b8: add             x2, x2, HEAP, lsl #32
    // 0x7b49bc: r0 = Null
    //     0x7b49bc: mov             x0, NULL
    // 0x7b49c0: r1 = Null
    //     0x7b49c0: mov             x1, NULL
    // 0x7b49c4: cmp             w0, NULL
    // 0x7b49c8: b.eq            #0x7b49f4
    // 0x7b49cc: cmp             w2, NULL
    // 0x7b49d0: b.eq            #0x7b49f4
    // 0x7b49d4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7b49d4: ldur            w4, [x2, #0x17]
    // 0x7b49d8: DecompressPointer r4
    //     0x7b49d8: add             x4, x4, HEAP, lsl #32
    // 0x7b49dc: r8 = X0? bound RenderObject
    //     0x7b49dc: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0x7b49e0: ldr             x8, [x8, #0x1a8]
    // 0x7b49e4: LoadField: r9 = r4->field_7
    //     0x7b49e4: ldur            x9, [x4, #7]
    // 0x7b49e8: r3 = Null
    //     0x7b49e8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2b0] Null
    //     0x7b49ec: ldr             x3, [x3, #0x2b0]
    // 0x7b49f0: blr             x9
    // 0x7b49f4: ldur            x1, [fp, #-8]
    // 0x7b49f8: StoreField: r1->field_f = rNULL
    //     0x7b49f8: stur            NULL, [x1, #0xf]
    // 0x7b49fc: StoreField: r1->field_13 = rNULL
    //     0x7b49fc: stur            NULL, [x1, #0x13]
    // 0x7b4a00: ldur            x1, [fp, #-0x10]
    // 0x7b4a04: LoadField: r2 = r1->field_57
    //     0x7b4a04: ldur            x2, [x1, #0x57]
    // 0x7b4a08: sub             x3, x2, #1
    // 0x7b4a0c: StoreField: r1->field_57 = r3
    //     0x7b4a0c: stur            x3, [x1, #0x57]
    // 0x7b4a10: r0 = Null
    //     0x7b4a10: mov             x0, NULL
    // 0x7b4a14: LeaveFrame
    //     0x7b4a14: mov             SP, fp
    //     0x7b4a18: ldp             fp, lr, [SP], #0x10
    // 0x7b4a1c: ret
    //     0x7b4a1c: ret             
    // 0x7b4a20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b4a20: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b4a24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b4a24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7b4a28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7b4a28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ move(/* No info */) {
    // ** addr: 0x7cebc8, size: 0x160
    // 0x7cebc8: EnterFrame
    //     0x7cebc8: stp             fp, lr, [SP, #-0x10]!
    //     0x7cebcc: mov             fp, SP
    // 0x7cebd0: AllocStack(0x30)
    //     0x7cebd0: sub             SP, SP, #0x30
    // 0x7cebd4: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7cebd4: mov             x5, x1
    //     0x7cebd8: mov             x4, x2
    //     0x7cebdc: stur            x1, [fp, #-8]
    //     0x7cebe0: stur            x2, [fp, #-0x10]
    //     0x7cebe4: stur            x3, [fp, #-0x18]
    // 0x7cebe8: CheckStackOverflow
    //     0x7cebe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7cebec: cmp             SP, x16
    //     0x7cebf0: b.ls            #0x7ced1c
    // 0x7cebf4: mov             x0, x4
    // 0x7cebf8: r2 = Null
    //     0x7cebf8: mov             x2, NULL
    // 0x7cebfc: r1 = Null
    //     0x7cebfc: mov             x1, NULL
    // 0x7cec00: r4 = 60
    //     0x7cec00: movz            x4, #0x3c
    // 0x7cec04: branchIfSmi(r0, 0x7cec10)
    //     0x7cec04: tbz             w0, #0, #0x7cec10
    // 0x7cec08: r4 = LoadClassIdInstr(r0)
    //     0x7cec08: ldur            x4, [x0, #-1]
    //     0x7cec0c: ubfx            x4, x4, #0xc, #0x14
    // 0x7cec10: sub             x4, x4, #0xbba
    // 0x7cec14: cmp             x4, #0x9a
    // 0x7cec18: b.ls            #0x7cec2c
    // 0x7cec1c: r8 = RenderBox
    //     0x7cec1c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x7cec20: r3 = Null
    //     0x7cec20: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c168] Null
    //     0x7cec24: ldr             x3, [x3, #0x168]
    // 0x7cec28: r0 = RenderBox()
    //     0x7cec28: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x7cec2c: ldur            x0, [fp, #-0x18]
    // 0x7cec30: r2 = Null
    //     0x7cec30: mov             x2, NULL
    // 0x7cec34: r1 = Null
    //     0x7cec34: mov             x1, NULL
    // 0x7cec38: r4 = 60
    //     0x7cec38: movz            x4, #0x3c
    // 0x7cec3c: branchIfSmi(r0, 0x7cec48)
    //     0x7cec3c: tbz             w0, #0, #0x7cec48
    // 0x7cec40: r4 = LoadClassIdInstr(r0)
    //     0x7cec40: ldur            x4, [x0, #-1]
    //     0x7cec44: ubfx            x4, x4, #0xc, #0x14
    // 0x7cec48: sub             x4, x4, #0xbba
    // 0x7cec4c: cmp             x4, #0x9a
    // 0x7cec50: b.ls            #0x7cec64
    // 0x7cec54: r8 = RenderBox?
    //     0x7cec54: ldr             x8, [PP, #0x4498]  ; [pp+0x4498] Type: RenderBox?
    // 0x7cec58: r3 = Null
    //     0x7cec58: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c178] Null
    //     0x7cec5c: ldr             x3, [x3, #0x178]
    // 0x7cec60: r0 = RenderBox?()
    //     0x7cec60: bl              #0x6af230  ; IsType_RenderBox?_Stub
    // 0x7cec64: ldur            x3, [fp, #-0x10]
    // 0x7cec68: LoadField: r4 = r3->field_7
    //     0x7cec68: ldur            w4, [x3, #7]
    // 0x7cec6c: DecompressPointer r4
    //     0x7cec6c: add             x4, x4, HEAP, lsl #32
    // 0x7cec70: stur            x4, [fp, #-0x20]
    // 0x7cec74: cmp             w4, NULL
    // 0x7cec78: b.eq            #0x7ced24
    // 0x7cec7c: mov             x0, x4
    // 0x7cec80: r2 = Null
    //     0x7cec80: mov             x2, NULL
    // 0x7cec84: r1 = Null
    //     0x7cec84: mov             x1, NULL
    // 0x7cec88: r4 = LoadClassIdInstr(r0)
    //     0x7cec88: ldur            x4, [x0, #-1]
    //     0x7cec8c: ubfx            x4, x4, #0xc, #0x14
    // 0x7cec90: cmp             x4, #0xc75
    // 0x7cec94: b.eq            #0x7cecac
    // 0x7cec98: r8 = GridParentData
    //     0x7cec98: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x7cec9c: ldr             x8, [x8, #0xca0]
    // 0x7ceca0: r3 = Null
    //     0x7ceca0: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c188] Null
    //     0x7ceca4: ldr             x3, [x3, #0x188]
    // 0x7ceca8: r0 = DefaultTypeTest()
    //     0x7ceca8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x7cecac: ldur            x0, [fp, #-0x20]
    // 0x7cecb0: LoadField: r1 = r0->field_f
    //     0x7cecb0: ldur            w1, [x0, #0xf]
    // 0x7cecb4: DecompressPointer r1
    //     0x7cecb4: add             x1, x1, HEAP, lsl #32
    // 0x7cecb8: r0 = LoadClassIdInstr(r1)
    //     0x7cecb8: ldur            x0, [x1, #-1]
    //     0x7cecbc: ubfx            x0, x0, #0xc, #0x14
    // 0x7cecc0: ldur            x16, [fp, #-0x18]
    // 0x7cecc4: stp             x16, x1, [SP]
    // 0x7cecc8: mov             lr, x0
    // 0x7ceccc: ldr             lr, [x21, lr, lsl #3]
    // 0x7cecd0: blr             lr
    // 0x7cecd4: tbnz            w0, #4, #0x7cece8
    // 0x7cecd8: r0 = Null
    //     0x7cecd8: mov             x0, NULL
    // 0x7cecdc: LeaveFrame
    //     0x7cecdc: mov             SP, fp
    //     0x7cece0: ldp             fp, lr, [SP], #0x10
    // 0x7cece4: ret
    //     0x7cece4: ret             
    // 0x7cece8: ldur            x1, [fp, #-8]
    // 0x7cecec: ldur            x2, [fp, #-0x10]
    // 0x7cecf0: r0 = _removeFromChildList()
    //     0x7cecf0: bl              #0x7b4764  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin::_removeFromChildList
    // 0x7cecf4: ldur            x1, [fp, #-8]
    // 0x7cecf8: ldur            x2, [fp, #-0x10]
    // 0x7cecfc: ldur            x3, [fp, #-0x18]
    // 0x7ced00: r0 = _insertIntoChildList()
    //     0x7ced00: bl              #0xda8018  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin::_insertIntoChildList
    // 0x7ced04: ldur            x1, [fp, #-8]
    // 0x7ced08: r0 = markNeedsLayout()
    //     0x7ced08: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0x7ced0c: r0 = Null
    //     0x7ced0c: mov             x0, NULL
    // 0x7ced10: LeaveFrame
    //     0x7ced10: mov             SP, fp
    //     0x7ced14: ldp             fp, lr, [SP], #0x10
    // 0x7ced18: ret
    //     0x7ced18: ret             
    // 0x7ced1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ced1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ced20: b               #0x7cebf4
    // 0x7ced24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7ced24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ detach(/* No info */) {
    // ** addr: 0x807fc8, size: 0xe8
    // 0x807fc8: EnterFrame
    //     0x807fc8: stp             fp, lr, [SP, #-0x10]!
    //     0x807fcc: mov             fp, SP
    // 0x807fd0: AllocStack(0x10)
    //     0x807fd0: sub             SP, SP, #0x10
    // 0x807fd4: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r0, fp-0x8 */)
    //     0x807fd4: mov             x0, x1
    //     0x807fd8: stur            x1, [fp, #-8]
    // 0x807fdc: CheckStackOverflow
    //     0x807fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807fe0: cmp             SP, x16
    //     0x807fe4: b.ls            #0x80809c
    // 0x807fe8: mov             x1, x0
    // 0x807fec: r0 = detach()
    //     0x807fec: bl              #0x8083b4  ; [package:flutter/src/rendering/object.dart] RenderObject::detach
    // 0x807ff0: ldur            x0, [fp, #-8]
    // 0x807ff4: LoadField: r1 = r0->field_5f
    //     0x807ff4: ldur            w1, [x0, #0x5f]
    // 0x807ff8: DecompressPointer r1
    //     0x807ff8: add             x1, x1, HEAP, lsl #32
    // 0x807ffc: mov             x2, x1
    // 0x808000: stur            x2, [fp, #-8]
    // 0x808004: CheckStackOverflow
    //     0x808004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x808008: cmp             SP, x16
    //     0x80800c: b.ls            #0x8080a4
    // 0x808010: cmp             w2, NULL
    // 0x808014: b.eq            #0x80808c
    // 0x808018: r0 = LoadClassIdInstr(r2)
    //     0x808018: ldur            x0, [x2, #-1]
    //     0x80801c: ubfx            x0, x0, #0xc, #0x14
    // 0x808020: mov             x1, x2
    // 0x808024: r0 = GDT[cid_x0 + 0xeec9]()
    //     0x808024: movz            x17, #0xeec9
    //     0x808028: add             lr, x0, x17
    //     0x80802c: ldr             lr, [x21, lr, lsl #3]
    //     0x808030: blr             lr
    // 0x808034: ldur            x0, [fp, #-8]
    // 0x808038: LoadField: r3 = r0->field_7
    //     0x808038: ldur            w3, [x0, #7]
    // 0x80803c: DecompressPointer r3
    //     0x80803c: add             x3, x3, HEAP, lsl #32
    // 0x808040: stur            x3, [fp, #-0x10]
    // 0x808044: cmp             w3, NULL
    // 0x808048: b.eq            #0x8080ac
    // 0x80804c: mov             x0, x3
    // 0x808050: r2 = Null
    //     0x808050: mov             x2, NULL
    // 0x808054: r1 = Null
    //     0x808054: mov             x1, NULL
    // 0x808058: r4 = LoadClassIdInstr(r0)
    //     0x808058: ldur            x4, [x0, #-1]
    //     0x80805c: ubfx            x4, x4, #0xc, #0x14
    // 0x808060: cmp             x4, #0xc75
    // 0x808064: b.eq            #0x80807c
    // 0x808068: r8 = GridParentData
    //     0x808068: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x80806c: ldr             x8, [x8, #0xca0]
    // 0x808070: r3 = Null
    //     0x808070: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c148] Null
    //     0x808074: ldr             x3, [x3, #0x148]
    // 0x808078: r0 = DefaultTypeTest()
    //     0x808078: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x80807c: ldur            x1, [fp, #-0x10]
    // 0x808080: LoadField: r2 = r1->field_13
    //     0x808080: ldur            w2, [x1, #0x13]
    // 0x808084: DecompressPointer r2
    //     0x808084: add             x2, x2, HEAP, lsl #32
    // 0x808088: b               #0x808000
    // 0x80808c: r0 = Null
    //     0x80808c: mov             x0, NULL
    // 0x808090: LeaveFrame
    //     0x808090: mov             SP, fp
    //     0x808094: ldp             fp, lr, [SP], #0x10
    // 0x808098: ret
    //     0x808098: ret             
    // 0x80809c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80809c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8080a0: b               #0x807fe8
    // 0x8080a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8080a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8080a8: b               #0x808010
    // 0x8080ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8080ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x809438, size: 0xf8
    // 0x809438: EnterFrame
    //     0x809438: stp             fp, lr, [SP, #-0x10]!
    //     0x80943c: mov             fp, SP
    // 0x809440: AllocStack(0x18)
    //     0x809440: sub             SP, SP, #0x18
    // 0x809444: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r2, fp-0x10 */)
    //     0x809444: mov             x2, x1
    //     0x809448: stur            x1, [fp, #-0x10]
    // 0x80944c: CheckStackOverflow
    //     0x80944c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x809450: cmp             SP, x16
    //     0x809454: b.ls            #0x80951c
    // 0x809458: LoadField: r0 = r2->field_5f
    //     0x809458: ldur            w0, [x2, #0x5f]
    // 0x80945c: DecompressPointer r0
    //     0x80945c: add             x0, x0, HEAP, lsl #32
    // 0x809460: mov             x3, x0
    // 0x809464: stur            x3, [fp, #-8]
    // 0x809468: CheckStackOverflow
    //     0x809468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80946c: cmp             SP, x16
    //     0x809470: b.ls            #0x809524
    // 0x809474: cmp             w3, NULL
    // 0x809478: b.eq            #0x80950c
    // 0x80947c: LoadField: r0 = r3->field_b
    //     0x80947c: ldur            x0, [x3, #0xb]
    // 0x809480: LoadField: r1 = r2->field_b
    //     0x809480: ldur            x1, [x2, #0xb]
    // 0x809484: cmp             x0, x1
    // 0x809488: b.gt            #0x8094b0
    // 0x80948c: add             x0, x1, #1
    // 0x809490: StoreField: r3->field_b = r0
    //     0x809490: stur            x0, [x3, #0xb]
    // 0x809494: r0 = LoadClassIdInstr(r3)
    //     0x809494: ldur            x0, [x3, #-1]
    //     0x809498: ubfx            x0, x0, #0xc, #0x14
    // 0x80949c: mov             x1, x3
    // 0x8094a0: r0 = GDT[cid_x0 + 0xedec]()
    //     0x8094a0: movz            x17, #0xedec
    //     0x8094a4: add             lr, x0, x17
    //     0x8094a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8094ac: blr             lr
    // 0x8094b0: ldur            x0, [fp, #-8]
    // 0x8094b4: LoadField: r3 = r0->field_7
    //     0x8094b4: ldur            w3, [x0, #7]
    // 0x8094b8: DecompressPointer r3
    //     0x8094b8: add             x3, x3, HEAP, lsl #32
    // 0x8094bc: stur            x3, [fp, #-0x18]
    // 0x8094c0: cmp             w3, NULL
    // 0x8094c4: b.eq            #0x80952c
    // 0x8094c8: mov             x0, x3
    // 0x8094cc: r2 = Null
    //     0x8094cc: mov             x2, NULL
    // 0x8094d0: r1 = Null
    //     0x8094d0: mov             x1, NULL
    // 0x8094d4: r4 = LoadClassIdInstr(r0)
    //     0x8094d4: ldur            x4, [x0, #-1]
    //     0x8094d8: ubfx            x4, x4, #0xc, #0x14
    // 0x8094dc: cmp             x4, #0xc75
    // 0x8094e0: b.eq            #0x8094f8
    // 0x8094e4: r8 = GridParentData
    //     0x8094e4: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x8094e8: ldr             x8, [x8, #0xca0]
    // 0x8094ec: r3 = Null
    //     0x8094ec: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c138] Null
    //     0x8094f0: ldr             x3, [x3, #0x138]
    // 0x8094f4: r0 = DefaultTypeTest()
    //     0x8094f4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8094f8: ldur            x1, [fp, #-0x18]
    // 0x8094fc: LoadField: r3 = r1->field_13
    //     0x8094fc: ldur            w3, [x1, #0x13]
    // 0x809500: DecompressPointer r3
    //     0x809500: add             x3, x3, HEAP, lsl #32
    // 0x809504: ldur            x2, [fp, #-0x10]
    // 0x809508: b               #0x809464
    // 0x80950c: r0 = Null
    //     0x80950c: mov             x0, NULL
    // 0x809510: LeaveFrame
    //     0x809510: mov             SP, fp
    //     0x809514: ldp             fp, lr, [SP], #0x10
    // 0x809518: ret
    //     0x809518: ret             
    // 0x80951c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80951c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809520: b               #0x809458
    // 0x809524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x809524: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x809528: b               #0x809474
    // 0x80952c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80952c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _insertIntoChildList(/* No info */) {
    // ** addr: 0xda8018, size: 0x570
    // 0xda8018: EnterFrame
    //     0xda8018: stp             fp, lr, [SP, #-0x10]!
    //     0xda801c: mov             fp, SP
    // 0xda8020: AllocStack(0x30)
    //     0xda8020: sub             SP, SP, #0x30
    // 0xda8024: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xda8024: mov             x5, x1
    //     0xda8028: mov             x4, x2
    //     0xda802c: stur            x1, [fp, #-0x10]
    //     0xda8030: stur            x2, [fp, #-0x18]
    //     0xda8034: stur            x3, [fp, #-0x20]
    // 0xda8038: LoadField: r6 = r4->field_7
    //     0xda8038: ldur            w6, [x4, #7]
    // 0xda803c: DecompressPointer r6
    //     0xda803c: add             x6, x6, HEAP, lsl #32
    // 0xda8040: stur            x6, [fp, #-8]
    // 0xda8044: cmp             w6, NULL
    // 0xda8048: b.eq            #0xda8578
    // 0xda804c: mov             x0, x6
    // 0xda8050: r2 = Null
    //     0xda8050: mov             x2, NULL
    // 0xda8054: r1 = Null
    //     0xda8054: mov             x1, NULL
    // 0xda8058: r4 = LoadClassIdInstr(r0)
    //     0xda8058: ldur            x4, [x0, #-1]
    //     0xda805c: ubfx            x4, x4, #0xc, #0x14
    // 0xda8060: cmp             x4, #0xc75
    // 0xda8064: b.eq            #0xda807c
    // 0xda8068: r8 = GridParentData
    //     0xda8068: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0xda806c: ldr             x8, [x8, #0xca0]
    // 0xda8070: r3 = Null
    //     0xda8070: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c198] Null
    //     0xda8074: ldr             x3, [x3, #0x198]
    // 0xda8078: r0 = DefaultTypeTest()
    //     0xda8078: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda807c: ldur            x3, [fp, #-0x10]
    // 0xda8080: LoadField: r0 = r3->field_57
    //     0xda8080: ldur            x0, [x3, #0x57]
    // 0xda8084: add             x1, x0, #1
    // 0xda8088: StoreField: r3->field_57 = r1
    //     0xda8088: stur            x1, [x3, #0x57]
    // 0xda808c: ldur            x4, [fp, #-0x20]
    // 0xda8090: cmp             w4, NULL
    // 0xda8094: b.ne            #0xda821c
    // 0xda8098: ldur            x4, [fp, #-8]
    // 0xda809c: LoadField: r5 = r3->field_5f
    //     0xda809c: ldur            w5, [x3, #0x5f]
    // 0xda80a0: DecompressPointer r5
    //     0xda80a0: add             x5, x5, HEAP, lsl #32
    // 0xda80a4: stur            x5, [fp, #-0x28]
    // 0xda80a8: LoadField: r2 = r4->field_b
    //     0xda80a8: ldur            w2, [x4, #0xb]
    // 0xda80ac: DecompressPointer r2
    //     0xda80ac: add             x2, x2, HEAP, lsl #32
    // 0xda80b0: mov             x0, x5
    // 0xda80b4: r1 = Null
    //     0xda80b4: mov             x1, NULL
    // 0xda80b8: cmp             w0, NULL
    // 0xda80bc: b.eq            #0xda80e8
    // 0xda80c0: cmp             w2, NULL
    // 0xda80c4: b.eq            #0xda80e8
    // 0xda80c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda80c8: ldur            w4, [x2, #0x17]
    // 0xda80cc: DecompressPointer r4
    //     0xda80cc: add             x4, x4, HEAP, lsl #32
    // 0xda80d0: r8 = X0? bound RenderObject
    //     0xda80d0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda80d4: ldr             x8, [x8, #0x1a8]
    // 0xda80d8: LoadField: r9 = r4->field_7
    //     0xda80d8: ldur            x9, [x4, #7]
    // 0xda80dc: r3 = Null
    //     0xda80dc: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c1b0] Null
    //     0xda80e0: ldr             x3, [x3, #0x1b0]
    // 0xda80e4: blr             x9
    // 0xda80e8: ldur            x0, [fp, #-0x28]
    // 0xda80ec: ldur            x3, [fp, #-8]
    // 0xda80f0: StoreField: r3->field_13 = r0
    //     0xda80f0: stur            w0, [x3, #0x13]
    //     0xda80f4: ldurb           w16, [x3, #-1]
    //     0xda80f8: ldurb           w17, [x0, #-1]
    //     0xda80fc: and             x16, x17, x16, lsr #2
    //     0xda8100: tst             x16, HEAP, lsr #32
    //     0xda8104: b.eq            #0xda810c
    //     0xda8108: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda810c: ldur            x0, [fp, #-0x28]
    // 0xda8110: cmp             w0, NULL
    // 0xda8114: b.eq            #0xda81c4
    // 0xda8118: LoadField: r3 = r0->field_7
    //     0xda8118: ldur            w3, [x0, #7]
    // 0xda811c: DecompressPointer r3
    //     0xda811c: add             x3, x3, HEAP, lsl #32
    // 0xda8120: stur            x3, [fp, #-0x30]
    // 0xda8124: cmp             w3, NULL
    // 0xda8128: b.eq            #0xda857c
    // 0xda812c: mov             x0, x3
    // 0xda8130: r2 = Null
    //     0xda8130: mov             x2, NULL
    // 0xda8134: r1 = Null
    //     0xda8134: mov             x1, NULL
    // 0xda8138: r4 = LoadClassIdInstr(r0)
    //     0xda8138: ldur            x4, [x0, #-1]
    //     0xda813c: ubfx            x4, x4, #0xc, #0x14
    // 0xda8140: cmp             x4, #0xc75
    // 0xda8144: b.eq            #0xda815c
    // 0xda8148: r8 = GridParentData
    //     0xda8148: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0xda814c: ldr             x8, [x8, #0xca0]
    // 0xda8150: r3 = Null
    //     0xda8150: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c1c0] Null
    //     0xda8154: ldr             x3, [x3, #0x1c0]
    // 0xda8158: r0 = DefaultTypeTest()
    //     0xda8158: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda815c: ldur            x3, [fp, #-0x30]
    // 0xda8160: LoadField: r2 = r3->field_b
    //     0xda8160: ldur            w2, [x3, #0xb]
    // 0xda8164: DecompressPointer r2
    //     0xda8164: add             x2, x2, HEAP, lsl #32
    // 0xda8168: ldur            x0, [fp, #-0x18]
    // 0xda816c: r1 = Null
    //     0xda816c: mov             x1, NULL
    // 0xda8170: cmp             w0, NULL
    // 0xda8174: b.eq            #0xda81a0
    // 0xda8178: cmp             w2, NULL
    // 0xda817c: b.eq            #0xda81a0
    // 0xda8180: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda8180: ldur            w4, [x2, #0x17]
    // 0xda8184: DecompressPointer r4
    //     0xda8184: add             x4, x4, HEAP, lsl #32
    // 0xda8188: r8 = X0? bound RenderObject
    //     0xda8188: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda818c: ldr             x8, [x8, #0x1a8]
    // 0xda8190: LoadField: r9 = r4->field_7
    //     0xda8190: ldur            x9, [x4, #7]
    // 0xda8194: r3 = Null
    //     0xda8194: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c1d0] Null
    //     0xda8198: ldr             x3, [x3, #0x1d0]
    // 0xda819c: blr             x9
    // 0xda81a0: ldur            x0, [fp, #-0x18]
    // 0xda81a4: ldur            x1, [fp, #-0x30]
    // 0xda81a8: StoreField: r1->field_f = r0
    //     0xda81a8: stur            w0, [x1, #0xf]
    //     0xda81ac: ldurb           w16, [x1, #-1]
    //     0xda81b0: ldurb           w17, [x0, #-1]
    //     0xda81b4: and             x16, x17, x16, lsr #2
    //     0xda81b8: tst             x16, HEAP, lsr #32
    //     0xda81bc: b.eq            #0xda81c4
    //     0xda81c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda81c4: ldur            x5, [fp, #-0x10]
    // 0xda81c8: ldur            x0, [fp, #-0x18]
    // 0xda81cc: StoreField: r5->field_5f = r0
    //     0xda81cc: stur            w0, [x5, #0x5f]
    //     0xda81d0: ldurb           w16, [x5, #-1]
    //     0xda81d4: ldurb           w17, [x0, #-1]
    //     0xda81d8: and             x16, x17, x16, lsr #2
    //     0xda81dc: tst             x16, HEAP, lsr #32
    //     0xda81e0: b.eq            #0xda81e8
    //     0xda81e4: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda81e8: LoadField: r0 = r5->field_63
    //     0xda81e8: ldur            w0, [x5, #0x63]
    // 0xda81ec: DecompressPointer r0
    //     0xda81ec: add             x0, x0, HEAP, lsl #32
    // 0xda81f0: cmp             w0, NULL
    // 0xda81f4: b.ne            #0xda8568
    // 0xda81f8: ldur            x0, [fp, #-0x18]
    // 0xda81fc: StoreField: r5->field_63 = r0
    //     0xda81fc: stur            w0, [x5, #0x63]
    //     0xda8200: ldurb           w16, [x5, #-1]
    //     0xda8204: ldurb           w17, [x0, #-1]
    //     0xda8208: and             x16, x17, x16, lsr #2
    //     0xda820c: tst             x16, HEAP, lsr #32
    //     0xda8210: b.eq            #0xda8218
    //     0xda8214: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda8218: b               #0xda8568
    // 0xda821c: mov             x5, x3
    // 0xda8220: ldur            x3, [fp, #-8]
    // 0xda8224: LoadField: r6 = r4->field_7
    //     0xda8224: ldur            w6, [x4, #7]
    // 0xda8228: DecompressPointer r6
    //     0xda8228: add             x6, x6, HEAP, lsl #32
    // 0xda822c: stur            x6, [fp, #-0x28]
    // 0xda8230: cmp             w6, NULL
    // 0xda8234: b.eq            #0xda8580
    // 0xda8238: mov             x0, x6
    // 0xda823c: r2 = Null
    //     0xda823c: mov             x2, NULL
    // 0xda8240: r1 = Null
    //     0xda8240: mov             x1, NULL
    // 0xda8244: r4 = LoadClassIdInstr(r0)
    //     0xda8244: ldur            x4, [x0, #-1]
    //     0xda8248: ubfx            x4, x4, #0xc, #0x14
    // 0xda824c: cmp             x4, #0xc75
    // 0xda8250: b.eq            #0xda8268
    // 0xda8254: r8 = GridParentData
    //     0xda8254: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0xda8258: ldr             x8, [x8, #0xca0]
    // 0xda825c: r3 = Null
    //     0xda825c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c1e0] Null
    //     0xda8260: ldr             x3, [x3, #0x1e0]
    // 0xda8264: r0 = DefaultTypeTest()
    //     0xda8264: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda8268: ldur            x3, [fp, #-0x28]
    // 0xda826c: LoadField: r4 = r3->field_13
    //     0xda826c: ldur            w4, [x3, #0x13]
    // 0xda8270: DecompressPointer r4
    //     0xda8270: add             x4, x4, HEAP, lsl #32
    // 0xda8274: stur            x4, [fp, #-0x30]
    // 0xda8278: cmp             w4, NULL
    // 0xda827c: b.ne            #0xda837c
    // 0xda8280: ldur            x5, [fp, #-0x10]
    // 0xda8284: ldur            x4, [fp, #-8]
    // 0xda8288: LoadField: r2 = r4->field_b
    //     0xda8288: ldur            w2, [x4, #0xb]
    // 0xda828c: DecompressPointer r2
    //     0xda828c: add             x2, x2, HEAP, lsl #32
    // 0xda8290: ldur            x0, [fp, #-0x20]
    // 0xda8294: r1 = Null
    //     0xda8294: mov             x1, NULL
    // 0xda8298: cmp             w0, NULL
    // 0xda829c: b.eq            #0xda82c8
    // 0xda82a0: cmp             w2, NULL
    // 0xda82a4: b.eq            #0xda82c8
    // 0xda82a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda82a8: ldur            w4, [x2, #0x17]
    // 0xda82ac: DecompressPointer r4
    //     0xda82ac: add             x4, x4, HEAP, lsl #32
    // 0xda82b0: r8 = X0? bound RenderObject
    //     0xda82b0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda82b4: ldr             x8, [x8, #0x1a8]
    // 0xda82b8: LoadField: r9 = r4->field_7
    //     0xda82b8: ldur            x9, [x4, #7]
    // 0xda82bc: r3 = Null
    //     0xda82bc: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c1f0] Null
    //     0xda82c0: ldr             x3, [x3, #0x1f0]
    // 0xda82c4: blr             x9
    // 0xda82c8: ldur            x0, [fp, #-0x20]
    // 0xda82cc: ldur            x3, [fp, #-8]
    // 0xda82d0: StoreField: r3->field_f = r0
    //     0xda82d0: stur            w0, [x3, #0xf]
    //     0xda82d4: ldurb           w16, [x3, #-1]
    //     0xda82d8: ldurb           w17, [x0, #-1]
    //     0xda82dc: and             x16, x17, x16, lsr #2
    //     0xda82e0: tst             x16, HEAP, lsr #32
    //     0xda82e4: b.eq            #0xda82ec
    //     0xda82e8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda82ec: ldur            x3, [fp, #-0x28]
    // 0xda82f0: LoadField: r2 = r3->field_b
    //     0xda82f0: ldur            w2, [x3, #0xb]
    // 0xda82f4: DecompressPointer r2
    //     0xda82f4: add             x2, x2, HEAP, lsl #32
    // 0xda82f8: ldur            x0, [fp, #-0x18]
    // 0xda82fc: r1 = Null
    //     0xda82fc: mov             x1, NULL
    // 0xda8300: cmp             w0, NULL
    // 0xda8304: b.eq            #0xda8330
    // 0xda8308: cmp             w2, NULL
    // 0xda830c: b.eq            #0xda8330
    // 0xda8310: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda8310: ldur            w4, [x2, #0x17]
    // 0xda8314: DecompressPointer r4
    //     0xda8314: add             x4, x4, HEAP, lsl #32
    // 0xda8318: r8 = X0? bound RenderObject
    //     0xda8318: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda831c: ldr             x8, [x8, #0x1a8]
    // 0xda8320: LoadField: r9 = r4->field_7
    //     0xda8320: ldur            x9, [x4, #7]
    // 0xda8324: r3 = Null
    //     0xda8324: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c200] Null
    //     0xda8328: ldr             x3, [x3, #0x200]
    // 0xda832c: blr             x9
    // 0xda8330: ldur            x0, [fp, #-0x18]
    // 0xda8334: ldur            x5, [fp, #-0x28]
    // 0xda8338: StoreField: r5->field_13 = r0
    //     0xda8338: stur            w0, [x5, #0x13]
    //     0xda833c: ldurb           w16, [x5, #-1]
    //     0xda8340: ldurb           w17, [x0, #-1]
    //     0xda8344: and             x16, x17, x16, lsr #2
    //     0xda8348: tst             x16, HEAP, lsr #32
    //     0xda834c: b.eq            #0xda8354
    //     0xda8350: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xda8354: ldur            x0, [fp, #-0x18]
    // 0xda8358: ldur            x1, [fp, #-0x10]
    // 0xda835c: StoreField: r1->field_63 = r0
    //     0xda835c: stur            w0, [x1, #0x63]
    //     0xda8360: ldurb           w16, [x1, #-1]
    //     0xda8364: ldurb           w17, [x0, #-1]
    //     0xda8368: and             x16, x17, x16, lsr #2
    //     0xda836c: tst             x16, HEAP, lsr #32
    //     0xda8370: b.eq            #0xda8378
    //     0xda8374: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda8378: b               #0xda8568
    // 0xda837c: mov             x5, x3
    // 0xda8380: ldur            x3, [fp, #-8]
    // 0xda8384: LoadField: r6 = r3->field_b
    //     0xda8384: ldur            w6, [x3, #0xb]
    // 0xda8388: DecompressPointer r6
    //     0xda8388: add             x6, x6, HEAP, lsl #32
    // 0xda838c: mov             x0, x4
    // 0xda8390: mov             x2, x6
    // 0xda8394: stur            x6, [fp, #-0x10]
    // 0xda8398: r1 = Null
    //     0xda8398: mov             x1, NULL
    // 0xda839c: cmp             w0, NULL
    // 0xda83a0: b.eq            #0xda83cc
    // 0xda83a4: cmp             w2, NULL
    // 0xda83a8: b.eq            #0xda83cc
    // 0xda83ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda83ac: ldur            w4, [x2, #0x17]
    // 0xda83b0: DecompressPointer r4
    //     0xda83b0: add             x4, x4, HEAP, lsl #32
    // 0xda83b4: r8 = X0? bound RenderObject
    //     0xda83b4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda83b8: ldr             x8, [x8, #0x1a8]
    // 0xda83bc: LoadField: r9 = r4->field_7
    //     0xda83bc: ldur            x9, [x4, #7]
    // 0xda83c0: r3 = Null
    //     0xda83c0: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c210] Null
    //     0xda83c4: ldr             x3, [x3, #0x210]
    // 0xda83c8: blr             x9
    // 0xda83cc: ldur            x0, [fp, #-0x30]
    // 0xda83d0: ldur            x3, [fp, #-8]
    // 0xda83d4: StoreField: r3->field_13 = r0
    //     0xda83d4: stur            w0, [x3, #0x13]
    //     0xda83d8: ldurb           w16, [x3, #-1]
    //     0xda83dc: ldurb           w17, [x0, #-1]
    //     0xda83e0: and             x16, x17, x16, lsr #2
    //     0xda83e4: tst             x16, HEAP, lsr #32
    //     0xda83e8: b.eq            #0xda83f0
    //     0xda83ec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xda83f0: ldur            x0, [fp, #-0x20]
    // 0xda83f4: ldur            x2, [fp, #-0x10]
    // 0xda83f8: r1 = Null
    //     0xda83f8: mov             x1, NULL
    // 0xda83fc: cmp             w0, NULL
    // 0xda8400: b.eq            #0xda842c
    // 0xda8404: cmp             w2, NULL
    // 0xda8408: b.eq            #0xda842c
    // 0xda840c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda840c: ldur            w4, [x2, #0x17]
    // 0xda8410: DecompressPointer r4
    //     0xda8410: add             x4, x4, HEAP, lsl #32
    // 0xda8414: r8 = X0? bound RenderObject
    //     0xda8414: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda8418: ldr             x8, [x8, #0x1a8]
    // 0xda841c: LoadField: r9 = r4->field_7
    //     0xda841c: ldur            x9, [x4, #7]
    // 0xda8420: r3 = Null
    //     0xda8420: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c220] Null
    //     0xda8424: ldr             x3, [x3, #0x220]
    // 0xda8428: blr             x9
    // 0xda842c: ldur            x0, [fp, #-0x20]
    // 0xda8430: ldur            x1, [fp, #-8]
    // 0xda8434: StoreField: r1->field_f = r0
    //     0xda8434: stur            w0, [x1, #0xf]
    //     0xda8438: ldurb           w16, [x1, #-1]
    //     0xda843c: ldurb           w17, [x0, #-1]
    //     0xda8440: and             x16, x17, x16, lsr #2
    //     0xda8444: tst             x16, HEAP, lsr #32
    //     0xda8448: b.eq            #0xda8450
    //     0xda844c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda8450: ldur            x0, [fp, #-0x30]
    // 0xda8454: LoadField: r3 = r0->field_7
    //     0xda8454: ldur            w3, [x0, #7]
    // 0xda8458: DecompressPointer r3
    //     0xda8458: add             x3, x3, HEAP, lsl #32
    // 0xda845c: stur            x3, [fp, #-8]
    // 0xda8460: cmp             w3, NULL
    // 0xda8464: b.eq            #0xda8584
    // 0xda8468: mov             x0, x3
    // 0xda846c: r2 = Null
    //     0xda846c: mov             x2, NULL
    // 0xda8470: r1 = Null
    //     0xda8470: mov             x1, NULL
    // 0xda8474: r4 = LoadClassIdInstr(r0)
    //     0xda8474: ldur            x4, [x0, #-1]
    //     0xda8478: ubfx            x4, x4, #0xc, #0x14
    // 0xda847c: cmp             x4, #0xc75
    // 0xda8480: b.eq            #0xda8498
    // 0xda8484: r8 = GridParentData
    //     0xda8484: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0xda8488: ldr             x8, [x8, #0xca0]
    // 0xda848c: r3 = Null
    //     0xda848c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c230] Null
    //     0xda8490: ldr             x3, [x3, #0x230]
    // 0xda8494: r0 = DefaultTypeTest()
    //     0xda8494: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xda8498: ldur            x3, [fp, #-0x28]
    // 0xda849c: LoadField: r2 = r3->field_b
    //     0xda849c: ldur            w2, [x3, #0xb]
    // 0xda84a0: DecompressPointer r2
    //     0xda84a0: add             x2, x2, HEAP, lsl #32
    // 0xda84a4: ldur            x0, [fp, #-0x18]
    // 0xda84a8: r1 = Null
    //     0xda84a8: mov             x1, NULL
    // 0xda84ac: cmp             w0, NULL
    // 0xda84b0: b.eq            #0xda84dc
    // 0xda84b4: cmp             w2, NULL
    // 0xda84b8: b.eq            #0xda84dc
    // 0xda84bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda84bc: ldur            w4, [x2, #0x17]
    // 0xda84c0: DecompressPointer r4
    //     0xda84c0: add             x4, x4, HEAP, lsl #32
    // 0xda84c4: r8 = X0? bound RenderObject
    //     0xda84c4: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda84c8: ldr             x8, [x8, #0x1a8]
    // 0xda84cc: LoadField: r9 = r4->field_7
    //     0xda84cc: ldur            x9, [x4, #7]
    // 0xda84d0: r3 = Null
    //     0xda84d0: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c240] Null
    //     0xda84d4: ldr             x3, [x3, #0x240]
    // 0xda84d8: blr             x9
    // 0xda84dc: ldur            x0, [fp, #-0x18]
    // 0xda84e0: ldur            x1, [fp, #-0x28]
    // 0xda84e4: StoreField: r1->field_13 = r0
    //     0xda84e4: stur            w0, [x1, #0x13]
    //     0xda84e8: ldurb           w16, [x1, #-1]
    //     0xda84ec: ldurb           w17, [x0, #-1]
    //     0xda84f0: and             x16, x17, x16, lsr #2
    //     0xda84f4: tst             x16, HEAP, lsr #32
    //     0xda84f8: b.eq            #0xda8500
    //     0xda84fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda8500: ldur            x3, [fp, #-8]
    // 0xda8504: LoadField: r2 = r3->field_b
    //     0xda8504: ldur            w2, [x3, #0xb]
    // 0xda8508: DecompressPointer r2
    //     0xda8508: add             x2, x2, HEAP, lsl #32
    // 0xda850c: ldur            x0, [fp, #-0x18]
    // 0xda8510: r1 = Null
    //     0xda8510: mov             x1, NULL
    // 0xda8514: cmp             w0, NULL
    // 0xda8518: b.eq            #0xda8544
    // 0xda851c: cmp             w2, NULL
    // 0xda8520: b.eq            #0xda8544
    // 0xda8524: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xda8524: ldur            w4, [x2, #0x17]
    // 0xda8528: DecompressPointer r4
    //     0xda8528: add             x4, x4, HEAP, lsl #32
    // 0xda852c: r8 = X0? bound RenderObject
    //     0xda852c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c1a8] TypeParameter: X0? bound RenderObject
    //     0xda8530: ldr             x8, [x8, #0x1a8]
    // 0xda8534: LoadField: r9 = r4->field_7
    //     0xda8534: ldur            x9, [x4, #7]
    // 0xda8538: r3 = Null
    //     0xda8538: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c250] Null
    //     0xda853c: ldr             x3, [x3, #0x250]
    // 0xda8540: blr             x9
    // 0xda8544: ldur            x0, [fp, #-0x18]
    // 0xda8548: ldur            x1, [fp, #-8]
    // 0xda854c: StoreField: r1->field_f = r0
    //     0xda854c: stur            w0, [x1, #0xf]
    //     0xda8550: ldurb           w16, [x1, #-1]
    //     0xda8554: ldurb           w17, [x0, #-1]
    //     0xda8558: and             x16, x17, x16, lsr #2
    //     0xda855c: tst             x16, HEAP, lsr #32
    //     0xda8560: b.eq            #0xda8568
    //     0xda8564: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xda8568: r0 = Null
    //     0xda8568: mov             x0, NULL
    // 0xda856c: LeaveFrame
    //     0xda856c: mov             SP, fp
    //     0xda8570: ldp             fp, lr, [SP], #0x10
    // 0xda8574: ret
    //     0xda8574: ret             
    // 0xda8578: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8578: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda857c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda857c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda8580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8580: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xda8584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xda8584: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3004, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {

  _ defaultComputeDistanceToHighestActualBaseline(/* No info */) {
    // ** addr: 0x74d704, size: 0x2f0
    // 0x74d704: EnterFrame
    //     0x74d704: stp             fp, lr, [SP, #-0x10]!
    //     0x74d708: mov             fp, SP
    // 0x74d70c: AllocStack(0x58)
    //     0x74d70c: sub             SP, SP, #0x58
    // 0x74d710: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x74d710: mov             x3, x2
    //     0x74d714: stur            x2, [fp, #-0x20]
    // 0x74d718: CheckStackOverflow
    //     0x74d718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d71c: cmp             SP, x16
    //     0x74d720: b.ls            #0x74d9a8
    // 0x74d724: LoadField: r0 = r1->field_5f
    //     0x74d724: ldur            w0, [x1, #0x5f]
    // 0x74d728: DecompressPointer r0
    //     0x74d728: add             x0, x0, HEAP, lsl #32
    // 0x74d72c: mov             x4, x0
    // 0x74d730: r5 = Null
    //     0x74d730: mov             x5, NULL
    // 0x74d734: stur            x5, [fp, #-0x10]
    // 0x74d738: stur            x4, [fp, #-0x18]
    // 0x74d73c: CheckStackOverflow
    //     0x74d73c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d740: cmp             SP, x16
    //     0x74d744: b.ls            #0x74d9b0
    // 0x74d748: cmp             w4, NULL
    // 0x74d74c: b.eq            #0x74d978
    // 0x74d750: LoadField: r6 = r4->field_7
    //     0x74d750: ldur            w6, [x4, #7]
    // 0x74d754: DecompressPointer r6
    //     0x74d754: add             x6, x6, HEAP, lsl #32
    // 0x74d758: stur            x6, [fp, #-8]
    // 0x74d75c: cmp             w6, NULL
    // 0x74d760: b.eq            #0x74d9b8
    // 0x74d764: mov             x0, x6
    // 0x74d768: r2 = Null
    //     0x74d768: mov             x2, NULL
    // 0x74d76c: r1 = Null
    //     0x74d76c: mov             x1, NULL
    // 0x74d770: r4 = LoadClassIdInstr(r0)
    //     0x74d770: ldur            x4, [x0, #-1]
    //     0x74d774: ubfx            x4, x4, #0xc, #0x14
    // 0x74d778: cmp             x4, #0xc75
    // 0x74d77c: b.eq            #0x74d794
    // 0x74d780: r8 = GridParentData
    //     0x74d780: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x74d784: ldr             x8, [x8, #0xca0]
    // 0x74d788: r3 = Null
    //     0x74d788: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c6c0] Null
    //     0x74d78c: ldr             x3, [x3, #0x6c0]
    // 0x74d790: r0 = DefaultTypeTest()
    //     0x74d790: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x74d794: r1 = 1
    //     0x74d794: movz            x1, #0x1
    // 0x74d798: r0 = AllocateContext()
    //     0x74d798: bl              #0xec126c  ; AllocateContextStub
    // 0x74d79c: mov             x4, x0
    // 0x74d7a0: ldur            x3, [fp, #-0x18]
    // 0x74d7a4: stur            x4, [fp, #-0x30]
    // 0x74d7a8: StoreField: r4->field_f = r3
    //     0x74d7a8: stur            w3, [x4, #0xf]
    // 0x74d7ac: LoadField: r5 = r3->field_27
    //     0x74d7ac: ldur            w5, [x3, #0x27]
    // 0x74d7b0: DecompressPointer r5
    //     0x74d7b0: add             x5, x5, HEAP, lsl #32
    // 0x74d7b4: stur            x5, [fp, #-0x28]
    // 0x74d7b8: cmp             w5, NULL
    // 0x74d7bc: b.eq            #0x74d98c
    // 0x74d7c0: ldur            x6, [fp, #-8]
    // 0x74d7c4: mov             x0, x5
    // 0x74d7c8: r2 = Null
    //     0x74d7c8: mov             x2, NULL
    // 0x74d7cc: r1 = Null
    //     0x74d7cc: mov             x1, NULL
    // 0x74d7d0: r4 = LoadClassIdInstr(r0)
    //     0x74d7d0: ldur            x4, [x0, #-1]
    //     0x74d7d4: ubfx            x4, x4, #0xc, #0x14
    // 0x74d7d8: sub             x4, x4, #0xc83
    // 0x74d7dc: cmp             x4, #1
    // 0x74d7e0: b.ls            #0x74d7f4
    // 0x74d7e4: r8 = BoxConstraints
    //     0x74d7e4: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x74d7e8: r3 = Null
    //     0x74d7e8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c6d0] Null
    //     0x74d7ec: ldr             x3, [x3, #0x6d0]
    // 0x74d7f0: r0 = BoxConstraints()
    //     0x74d7f0: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x74d7f4: ldur            x2, [fp, #-0x28]
    // 0x74d7f8: ldur            x3, [fp, #-0x20]
    // 0x74d7fc: r0 = AllocateRecord2()
    //     0x74d7fc: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x74d800: ldur            x2, [fp, #-0x30]
    // 0x74d804: r1 = Function '<anonymous closure>':.
    //     0x74d804: add             x1, PP, #0x45, lsl #12  ; [pp+0x456d0] AnonymousClosure: (0x74b7cc), in [package:flutter/src/rendering/box.dart] RenderBox::getDistanceToActualBaseline (0x74b4d4)
    //     0x74d808: ldr             x1, [x1, #0x6d0]
    // 0x74d80c: stur            x0, [fp, #-0x28]
    // 0x74d810: r0 = AllocateClosure()
    //     0x74d810: bl              #0xec1630  ; AllocateClosureStub
    // 0x74d814: r16 = <(BoxConstraints, TextBaseline), double?>
    //     0x74d814: add             x16, PP, #0x45, lsl #12  ; [pp+0x456d8] TypeArguments: <(BoxConstraints, TextBaseline), double?>
    //     0x74d818: ldr             x16, [x16, #0x6d8]
    // 0x74d81c: ldur            lr, [fp, #-0x18]
    // 0x74d820: stp             lr, x16, [SP, #0x18]
    // 0x74d824: r16 = Instance__Baseline
    //     0x74d824: add             x16, PP, #0x45, lsl #12  ; [pp+0x456e0] Obj!_Baseline@e117c1
    //     0x74d828: ldr             x16, [x16, #0x6e0]
    // 0x74d82c: ldur            lr, [fp, #-0x28]
    // 0x74d830: stp             lr, x16, [SP, #8]
    // 0x74d834: str             x0, [SP]
    // 0x74d838: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x74d838: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x74d83c: r0 = _computeIntrinsics()
    //     0x74d83c: bl              #0x72d344  ; [package:flutter/src/rendering/box.dart] RenderBox::_computeIntrinsics
    // 0x74d840: mov             x1, x0
    // 0x74d844: ldur            x0, [fp, #-8]
    // 0x74d848: LoadField: r2 = r0->field_7
    //     0x74d848: ldur            w2, [x0, #7]
    // 0x74d84c: DecompressPointer r2
    //     0x74d84c: add             x2, x2, HEAP, lsl #32
    // 0x74d850: LoadField: d0 = r2->field_f
    //     0x74d850: ldur            d0, [x2, #0xf]
    // 0x74d854: cmp             w1, NULL
    // 0x74d858: b.ne            #0x74d864
    // 0x74d85c: r2 = Null
    //     0x74d85c: mov             x2, NULL
    // 0x74d860: b               #0x74d898
    // 0x74d864: LoadField: d1 = r1->field_7
    //     0x74d864: ldur            d1, [x1, #7]
    // 0x74d868: fadd            d2, d1, d0
    // 0x74d86c: r1 = inline_Allocate_Double()
    //     0x74d86c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74d870: add             x1, x1, #0x10
    //     0x74d874: cmp             x2, x1
    //     0x74d878: b.ls            #0x74d9bc
    //     0x74d87c: str             x1, [THR, #0x50]  ; THR::top
    //     0x74d880: sub             x1, x1, #0xf
    //     0x74d884: movz            x2, #0xe15c
    //     0x74d888: movk            x2, #0x3, lsl #16
    //     0x74d88c: stur            x2, [x1, #-1]
    // 0x74d890: StoreField: r1->field_7 = d2
    //     0x74d890: stur            d2, [x1, #7]
    // 0x74d894: mov             x2, x1
    // 0x74d898: ldur            x1, [fp, #-0x10]
    // 0x74d89c: cmp             w1, NULL
    // 0x74d8a0: b.eq            #0x74d900
    // 0x74d8a4: cmp             w2, NULL
    // 0x74d8a8: b.eq            #0x74d8f8
    // 0x74d8ac: LoadField: d0 = r1->field_7
    //     0x74d8ac: ldur            d0, [x1, #7]
    // 0x74d8b0: LoadField: d1 = r2->field_7
    //     0x74d8b0: ldur            d1, [x2, #7]
    // 0x74d8b4: fcmp            d0, d1
    // 0x74d8b8: b.lt            #0x74d8c4
    // 0x74d8bc: LoadField: d0 = r2->field_7
    //     0x74d8bc: ldur            d0, [x2, #7]
    // 0x74d8c0: b               #0x74d8c8
    // 0x74d8c4: LoadField: d0 = r1->field_7
    //     0x74d8c4: ldur            d0, [x1, #7]
    // 0x74d8c8: r1 = inline_Allocate_Double()
    //     0x74d8c8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x74d8cc: add             x1, x1, #0x10
    //     0x74d8d0: cmp             x2, x1
    //     0x74d8d4: b.ls            #0x74d9d8
    //     0x74d8d8: str             x1, [THR, #0x50]  ; THR::top
    //     0x74d8dc: sub             x1, x1, #0xf
    //     0x74d8e0: movz            x2, #0xe15c
    //     0x74d8e4: movk            x2, #0x3, lsl #16
    //     0x74d8e8: stur            x2, [x1, #-1]
    // 0x74d8ec: StoreField: r1->field_7 = d0
    //     0x74d8ec: stur            d0, [x1, #7]
    // 0x74d8f0: mov             x5, x1
    // 0x74d8f4: b               #0x74d968
    // 0x74d8f8: r3 = true
    //     0x74d8f8: add             x3, NULL, #0x20  ; true
    // 0x74d8fc: b               #0x74d904
    // 0x74d900: r3 = false
    //     0x74d900: add             x3, NULL, #0x30  ; false
    // 0x74d904: cmp             w1, NULL
    // 0x74d908: b.eq            #0x74d940
    // 0x74d90c: tbnz            w3, #4, #0x74d91c
    // 0x74d910: r4 = Null
    //     0x74d910: mov             x4, NULL
    // 0x74d914: r3 = Null
    //     0x74d914: mov             x3, NULL
    // 0x74d918: b               #0x74d924
    // 0x74d91c: mov             x4, x2
    // 0x74d920: mov             x3, x2
    // 0x74d924: cmp             w4, NULL
    // 0x74d928: b.ne            #0x74d934
    // 0x74d92c: mov             x5, x1
    // 0x74d930: b               #0x74d968
    // 0x74d934: mov             x5, x3
    // 0x74d938: r3 = true
    //     0x74d938: add             x3, NULL, #0x20  ; true
    // 0x74d93c: b               #0x74d944
    // 0x74d940: r5 = Null
    //     0x74d940: mov             x5, NULL
    // 0x74d944: cmp             w1, NULL
    // 0x74d948: b.ne            #0x74d964
    // 0x74d94c: tbnz            w3, #4, #0x74d958
    // 0x74d950: mov             x1, x5
    // 0x74d954: b               #0x74d95c
    // 0x74d958: mov             x1, x2
    // 0x74d95c: mov             x5, x1
    // 0x74d960: b               #0x74d968
    // 0x74d964: r5 = Null
    //     0x74d964: mov             x5, NULL
    // 0x74d968: LoadField: r4 = r0->field_13
    //     0x74d968: ldur            w4, [x0, #0x13]
    // 0x74d96c: DecompressPointer r4
    //     0x74d96c: add             x4, x4, HEAP, lsl #32
    // 0x74d970: ldur            x3, [fp, #-0x20]
    // 0x74d974: b               #0x74d734
    // 0x74d978: mov             x1, x5
    // 0x74d97c: mov             x0, x1
    // 0x74d980: LeaveFrame
    //     0x74d980: mov             SP, fp
    //     0x74d984: ldp             fp, lr, [SP], #0x10
    // 0x74d988: ret
    //     0x74d988: ret             
    // 0x74d98c: r0 = StateError()
    //     0x74d98c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x74d990: mov             x1, x0
    // 0x74d994: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x74d994: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x74d998: StoreField: r1->field_b = r0
    //     0x74d998: stur            w0, [x1, #0xb]
    // 0x74d99c: mov             x0, x1
    // 0x74d9a0: r0 = Throw()
    //     0x74d9a0: bl              #0xec04b8  ; ThrowStub
    // 0x74d9a4: brk             #0
    // 0x74d9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d9a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d9ac: b               #0x74d724
    // 0x74d9b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d9b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d9b4: b               #0x74d748
    // 0x74d9b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74d9b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74d9bc: SaveReg d2
    //     0x74d9bc: str             q2, [SP, #-0x10]!
    // 0x74d9c0: SaveReg r0
    //     0x74d9c0: str             x0, [SP, #-8]!
    // 0x74d9c4: r0 = AllocateDouble()
    //     0x74d9c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74d9c8: mov             x1, x0
    // 0x74d9cc: RestoreReg r0
    //     0x74d9cc: ldr             x0, [SP], #8
    // 0x74d9d0: RestoreReg d2
    //     0x74d9d0: ldr             q2, [SP], #0x10
    // 0x74d9d4: b               #0x74d890
    // 0x74d9d8: SaveReg d0
    //     0x74d9d8: str             q0, [SP, #-0x10]!
    // 0x74d9dc: SaveReg r0
    //     0x74d9dc: str             x0, [SP, #-8]!
    // 0x74d9e0: r0 = AllocateDouble()
    //     0x74d9e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74d9e4: mov             x1, x0
    // 0x74d9e8: RestoreReg r0
    //     0x74d9e8: ldr             x0, [SP], #8
    // 0x74d9ec: RestoreReg d0
    //     0x74d9ec: ldr             q0, [SP], #0x10
    // 0x74d9f0: b               #0x74d8ec
  }
  _ defaultHitTestChildren(/* No info */) {
    // ** addr: 0x80021c, size: 0x144
    // 0x80021c: EnterFrame
    //     0x80021c: stp             fp, lr, [SP, #-0x10]!
    //     0x800220: mov             fp, SP
    // 0x800224: AllocStack(0x28)
    //     0x800224: sub             SP, SP, #0x28
    // 0x800228: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x800228: mov             x4, x2
    //     0x80022c: stur            x2, [fp, #-0x18]
    //     0x800230: stur            x3, [fp, #-0x20]
    // 0x800234: CheckStackOverflow
    //     0x800234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800238: cmp             SP, x16
    //     0x80023c: b.ls            #0x80034c
    // 0x800240: LoadField: r0 = r1->field_63
    //     0x800240: ldur            w0, [x1, #0x63]
    // 0x800244: DecompressPointer r0
    //     0x800244: add             x0, x0, HEAP, lsl #32
    // 0x800248: mov             x5, x0
    // 0x80024c: stur            x5, [fp, #-0x10]
    // 0x800250: CheckStackOverflow
    //     0x800250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800254: cmp             SP, x16
    //     0x800258: b.ls            #0x800354
    // 0x80025c: cmp             w5, NULL
    // 0x800260: b.eq            #0x80033c
    // 0x800264: LoadField: r6 = r5->field_7
    //     0x800264: ldur            w6, [x5, #7]
    // 0x800268: DecompressPointer r6
    //     0x800268: add             x6, x6, HEAP, lsl #32
    // 0x80026c: stur            x6, [fp, #-8]
    // 0x800270: cmp             w6, NULL
    // 0x800274: b.eq            #0x80035c
    // 0x800278: mov             x0, x6
    // 0x80027c: r2 = Null
    //     0x80027c: mov             x2, NULL
    // 0x800280: r1 = Null
    //     0x800280: mov             x1, NULL
    // 0x800284: r4 = LoadClassIdInstr(r0)
    //     0x800284: ldur            x4, [x0, #-1]
    //     0x800288: ubfx            x4, x4, #0xc, #0x14
    // 0x80028c: cmp             x4, #0xc75
    // 0x800290: b.eq            #0x8002a8
    // 0x800294: r8 = GridParentData
    //     0x800294: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x800298: ldr             x8, [x8, #0xca0]
    // 0x80029c: r3 = Null
    //     0x80029c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c330] Null
    //     0x8002a0: ldr             x3, [x3, #0x330]
    // 0x8002a4: r0 = DefaultTypeTest()
    //     0x8002a4: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8002a8: ldur            x0, [fp, #-8]
    // 0x8002ac: LoadField: r3 = r0->field_7
    //     0x8002ac: ldur            w3, [x0, #7]
    // 0x8002b0: DecompressPointer r3
    //     0x8002b0: add             x3, x3, HEAP, lsl #32
    // 0x8002b4: ldur            x1, [fp, #-0x20]
    // 0x8002b8: mov             x2, x3
    // 0x8002bc: stur            x3, [fp, #-0x28]
    // 0x8002c0: r0 = -()
    //     0x8002c0: bl              #0x618980  ; [dart:ui] Offset::-
    // 0x8002c4: ldur            x1, [fp, #-0x28]
    // 0x8002c8: stur            x0, [fp, #-0x28]
    // 0x8002cc: r0 = unary-()
    //     0x8002cc: bl              #0x6a58ac  ; [dart:ui] Offset::unary-
    // 0x8002d0: ldur            x1, [fp, #-0x18]
    // 0x8002d4: mov             x2, x0
    // 0x8002d8: r0 = pushOffset()
    //     0x8002d8: bl              #0x7faa44  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::pushOffset
    // 0x8002dc: ldur            x1, [fp, #-0x10]
    // 0x8002e0: r0 = LoadClassIdInstr(r1)
    //     0x8002e0: ldur            x0, [x1, #-1]
    //     0x8002e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8002e8: ldur            x2, [fp, #-0x18]
    // 0x8002ec: ldur            x3, [fp, #-0x28]
    // 0x8002f0: r0 = GDT[cid_x0 + 0xdf93]()
    //     0x8002f0: movz            x17, #0xdf93
    //     0x8002f4: add             lr, x0, x17
    //     0x8002f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8002fc: blr             lr
    // 0x800300: ldur            x1, [fp, #-0x18]
    // 0x800304: stur            x0, [fp, #-0x10]
    // 0x800308: r0 = popTransform()
    //     0x800308: bl              #0x7fa9a8  ; [package:flutter/src/gestures/hit_test.dart] HitTestResult::popTransform
    // 0x80030c: ldur            x1, [fp, #-0x10]
    // 0x800310: tbz             w1, #4, #0x80032c
    // 0x800314: ldur            x1, [fp, #-8]
    // 0x800318: LoadField: r5 = r1->field_f
    //     0x800318: ldur            w5, [x1, #0xf]
    // 0x80031c: DecompressPointer r5
    //     0x80031c: add             x5, x5, HEAP, lsl #32
    // 0x800320: ldur            x4, [fp, #-0x18]
    // 0x800324: ldur            x3, [fp, #-0x20]
    // 0x800328: b               #0x80024c
    // 0x80032c: r0 = true
    //     0x80032c: add             x0, NULL, #0x20  ; true
    // 0x800330: LeaveFrame
    //     0x800330: mov             SP, fp
    //     0x800334: ldp             fp, lr, [SP], #0x10
    // 0x800338: ret
    //     0x800338: ret             
    // 0x80033c: r0 = false
    //     0x80033c: add             x0, NULL, #0x30  ; false
    // 0x800340: LeaveFrame
    //     0x800340: mov             SP, fp
    //     0x800344: ldp             fp, lr, [SP], #0x10
    // 0x800348: ret
    //     0x800348: ret             
    // 0x80034c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80034c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800350: b               #0x800240
    // 0x800354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800358: b               #0x80025c
    // 0x80035c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80035c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3005, size: 0x6c, field offset: 0x68
//   transformed mixin,
abstract class _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin extends _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin
     with DebugOverflowIndicatorMixin {

  _ dispose(/* No info */) {
    // ** addr: 0x7a1568, size: 0x194
    // 0x7a1568: EnterFrame
    //     0x7a1568: stp             fp, lr, [SP, #-0x10]!
    //     0x7a156c: mov             fp, SP
    // 0x7a1570: AllocStack(0x40)
    //     0x7a1570: sub             SP, SP, #0x40
    // 0x7a1574: SetupParameters(_RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin this /* r1 => r1, fp-0x38 */)
    //     0x7a1574: stur            x1, [fp, #-0x38]
    // 0x7a1578: CheckStackOverflow
    //     0x7a1578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a157c: cmp             SP, x16
    //     0x7a1580: b.ls            #0x7a16e4
    // 0x7a1584: LoadField: r0 = r1->field_67
    //     0x7a1584: ldur            w0, [x1, #0x67]
    // 0x7a1588: DecompressPointer r0
    //     0x7a1588: add             x0, x0, HEAP, lsl #32
    // 0x7a158c: stur            x0, [fp, #-0x30]
    // 0x7a1590: LoadField: r2 = r0->field_b
    //     0x7a1590: ldur            w2, [x0, #0xb]
    // 0x7a1594: r3 = LoadInt32Instr(r2)
    //     0x7a1594: sbfx            x3, x2, #1, #0x1f
    // 0x7a1598: stur            x3, [fp, #-0x28]
    // 0x7a159c: r2 = 0
    //     0x7a159c: movz            x2, #0
    // 0x7a15a0: CheckStackOverflow
    //     0x7a15a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a15a4: cmp             SP, x16
    //     0x7a15a8: b.ls            #0x7a16ec
    // 0x7a15ac: cmp             x2, x3
    // 0x7a15b0: b.ge            #0x7a16cc
    // 0x7a15b4: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0x7a15b4: add             x16, x0, x2, lsl #2
    //     0x7a15b8: ldur            w4, [x16, #0xf]
    // 0x7a15bc: DecompressPointer r4
    //     0x7a15bc: add             x4, x4, HEAP, lsl #32
    // 0x7a15c0: stur            x4, [fp, #-0x20]
    // 0x7a15c4: add             x5, x2, #1
    // 0x7a15c8: stur            x5, [fp, #-0x18]
    // 0x7a15cc: LoadField: r2 = r4->field_3f
    //     0x7a15cc: ldur            w2, [x4, #0x3f]
    // 0x7a15d0: DecompressPointer r2
    //     0x7a15d0: add             x2, x2, HEAP, lsl #32
    // 0x7a15d4: stur            x2, [fp, #-0x10]
    // 0x7a15d8: cmp             w2, NULL
    // 0x7a15dc: b.ne            #0x7a15e8
    // 0x7a15e0: mov             x0, x4
    // 0x7a15e4: b               #0x7a1638
    // 0x7a15e8: LoadField: r6 = r2->field_7
    //     0x7a15e8: ldur            w6, [x2, #7]
    // 0x7a15ec: DecompressPointer r6
    //     0x7a15ec: add             x6, x6, HEAP, lsl #32
    // 0x7a15f0: cmp             w6, NULL
    // 0x7a15f4: b.eq            #0x7a16f4
    // 0x7a15f8: LoadField: r7 = r6->field_7
    //     0x7a15f8: ldur            x7, [x6, #7]
    // 0x7a15fc: ldr             x6, [x7]
    // 0x7a1600: stur            x6, [fp, #-8]
    // 0x7a1604: cbnz            x6, #0x7a1614
    // 0x7a1608: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7a1608: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7a160c: str             x16, [SP]
    // 0x7a1610: r0 = _throwNew()
    //     0x7a1610: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7a1614: ldur            x0, [fp, #-8]
    // 0x7a1618: stur            x0, [fp, #-8]
    // 0x7a161c: r1 = <Never>
    //     0x7a161c: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7a1620: r0 = Pointer()
    //     0x7a1620: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7a1624: mov             x1, x0
    // 0x7a1628: ldur            x0, [fp, #-8]
    // 0x7a162c: StoreField: r1->field_7 = r0
    //     0x7a162c: stur            x0, [x1, #7]
    // 0x7a1630: r0 = __dispose$Method$FfiNative()
    //     0x7a1630: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x7a1634: ldur            x0, [fp, #-0x20]
    // 0x7a1638: StoreField: r0->field_3f = rNULL
    //     0x7a1638: stur            NULL, [x0, #0x3f]
    // 0x7a163c: LoadField: r1 = r0->field_7
    //     0x7a163c: ldur            w1, [x0, #7]
    // 0x7a1640: DecompressPointer r1
    //     0x7a1640: add             x1, x1, HEAP, lsl #32
    // 0x7a1644: cmp             w1, NULL
    // 0x7a1648: b.eq            #0x7a16b0
    // 0x7a164c: LoadField: r2 = r1->field_7
    //     0x7a164c: ldur            w2, [x1, #7]
    // 0x7a1650: DecompressPointer r2
    //     0x7a1650: add             x2, x2, HEAP, lsl #32
    // 0x7a1654: LoadField: r1 = r2->field_f
    //     0x7a1654: ldur            w1, [x2, #0xf]
    // 0x7a1658: DecompressPointer r1
    //     0x7a1658: add             x1, x1, HEAP, lsl #32
    // 0x7a165c: stur            x1, [fp, #-0x10]
    // 0x7a1660: LoadField: r2 = r1->field_7
    //     0x7a1660: ldur            w2, [x1, #7]
    // 0x7a1664: DecompressPointer r2
    //     0x7a1664: add             x2, x2, HEAP, lsl #32
    // 0x7a1668: cmp             w2, NULL
    // 0x7a166c: b.eq            #0x7a16f8
    // 0x7a1670: LoadField: r3 = r2->field_7
    //     0x7a1670: ldur            x3, [x2, #7]
    // 0x7a1674: ldr             x2, [x3]
    // 0x7a1678: stur            x2, [fp, #-8]
    // 0x7a167c: cbnz            x2, #0x7a168c
    // 0x7a1680: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7a1680: ldr             x16, [PP, #0x2800]  ; [pp+0x2800] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7a1684: str             x16, [SP]
    // 0x7a1688: r0 = _throwNew()
    //     0x7a1688: bl              #0x5f7854  ; [dart:core] StateError::_throwNew
    // 0x7a168c: ldur            x0, [fp, #-8]
    // 0x7a1690: stur            x0, [fp, #-8]
    // 0x7a1694: r1 = <Never>
    //     0x7a1694: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7a1698: r0 = Pointer()
    //     0x7a1698: bl              #0x617448  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7a169c: mov             x1, x0
    // 0x7a16a0: ldur            x0, [fp, #-8]
    // 0x7a16a4: StoreField: r1->field_7 = r0
    //     0x7a16a4: stur            x0, [x1, #7]
    // 0x7a16a8: r0 = __dispose$Method$FfiNative()
    //     0x7a16a8: bl              #0x72f87c  ; [dart:ui] _NativeParagraph::__dispose$Method$FfiNative
    // 0x7a16ac: ldur            x0, [fp, #-0x20]
    // 0x7a16b0: StoreField: r0->field_7 = rNULL
    //     0x7a16b0: stur            NULL, [x0, #7]
    // 0x7a16b4: StoreField: r0->field_f = rNULL
    //     0x7a16b4: stur            NULL, [x0, #0xf]
    // 0x7a16b8: ldur            x2, [fp, #-0x18]
    // 0x7a16bc: ldur            x1, [fp, #-0x38]
    // 0x7a16c0: ldur            x0, [fp, #-0x30]
    // 0x7a16c4: ldur            x3, [fp, #-0x28]
    // 0x7a16c8: b               #0x7a15a0
    // 0x7a16cc: ldur            x1, [fp, #-0x38]
    // 0x7a16d0: r0 = dispose()
    //     0x7a16d0: bl              #0x7a17a8  ; [package:flutter/src/rendering/object.dart] RenderObject::dispose
    // 0x7a16d4: r0 = Null
    //     0x7a16d4: mov             x0, NULL
    // 0x7a16d8: LeaveFrame
    //     0x7a16d8: mov             SP, fp
    //     0x7a16dc: ldp             fp, lr, [SP], #0x10
    // 0x7a16e0: ret
    //     0x7a16e0: ret             
    // 0x7a16e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a16e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a16e8: b               #0x7a1584
    // 0x7a16ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a16ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a16f0: b               #0x7a15ac
    // 0x7a16f4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7a16f4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x7a16f8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7a16f8: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ reassemble(/* No info */) {
    // ** addr: 0x80052c, size: 0x30
    // 0x80052c: EnterFrame
    //     0x80052c: stp             fp, lr, [SP, #-0x10]!
    //     0x800530: mov             fp, SP
    // 0x800534: CheckStackOverflow
    //     0x800534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x800538: cmp             SP, x16
    //     0x80053c: b.ls            #0x800554
    // 0x800540: r0 = reassemble()
    //     0x800540: bl              #0x80055c  ; [package:flutter/src/rendering/object.dart] RenderObject::reassemble
    // 0x800544: r0 = Null
    //     0x800544: mov             x0, NULL
    // 0x800548: LeaveFrame
    //     0x800548: mov             SP, fp
    //     0x80054c: ldp             fp, lr, [SP], #0x10
    // 0x800550: ret
    //     0x800550: ret             
    // 0x800554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800554: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800558: b               #0x800540
  }
}

// class id: 3006, size: 0xa0, field offset: 0x6c
class RenderLayoutGrid extends _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin {

  late PlacementGrid _placementGrid; // offset: 0x70

  dynamic computeMinIntrinsicWidth(dynamic) {
    // ** addr: 0x735210, size: 0x24
    // 0x735210: EnterFrame
    //     0x735210: stp             fp, lr, [SP, #-0x10]!
    //     0x735214: mov             fp, SP
    // 0x735218: ldr             x2, [fp, #0x10]
    // 0x73521c: r1 = Function 'computeMinIntrinsicWidth':.
    //     0x73521c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e70] AnonymousClosure: (0x735234), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMinIntrinsicWidth (0x7352a8)
    //     0x735220: ldr             x1, [x1, #0xe70]
    // 0x735224: r0 = AllocateClosure()
    //     0x735224: bl              #0xec1630  ; AllocateClosureStub
    // 0x735228: LeaveFrame
    //     0x735228: mov             SP, fp
    //     0x73522c: ldp             fp, lr, [SP], #0x10
    // 0x735230: ret
    //     0x735230: ret             
  }
  [closure] double computeMinIntrinsicWidth(dynamic, double) {
    // ** addr: 0x735234, size: 0x74
    // 0x735234: EnterFrame
    //     0x735234: stp             fp, lr, [SP, #-0x10]!
    //     0x735238: mov             fp, SP
    // 0x73523c: ldr             x0, [fp, #0x18]
    // 0x735240: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x735240: ldur            w1, [x0, #0x17]
    // 0x735244: DecompressPointer r1
    //     0x735244: add             x1, x1, HEAP, lsl #32
    // 0x735248: CheckStackOverflow
    //     0x735248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73524c: cmp             SP, x16
    //     0x735250: b.ls            #0x735290
    // 0x735254: ldr             x2, [fp, #0x10]
    // 0x735258: r0 = computeMinIntrinsicWidth()
    //     0x735258: bl              #0x7352a8  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMinIntrinsicWidth
    // 0x73525c: r0 = inline_Allocate_Double()
    //     0x73525c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x735260: add             x0, x0, #0x10
    //     0x735264: cmp             x1, x0
    //     0x735268: b.ls            #0x735298
    //     0x73526c: str             x0, [THR, #0x50]  ; THR::top
    //     0x735270: sub             x0, x0, #0xf
    //     0x735274: movz            x1, #0xe15c
    //     0x735278: movk            x1, #0x3, lsl #16
    //     0x73527c: stur            x1, [x0, #-1]
    // 0x735280: StoreField: r0->field_7 = d0
    //     0x735280: stur            d0, [x0, #7]
    // 0x735284: LeaveFrame
    //     0x735284: mov             SP, fp
    //     0x735288: ldp             fp, lr, [SP], #0x10
    // 0x73528c: ret
    //     0x73528c: ret             
    // 0x735290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735294: b               #0x735254
    // 0x735298: SaveReg d0
    //     0x735298: str             q0, [SP, #-0x10]!
    // 0x73529c: r0 = AllocateDouble()
    //     0x73529c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x7352a0: RestoreReg d0
    //     0x7352a0: ldr             q0, [SP], #0x10
    // 0x7352a4: b               #0x735280
  }
  _ computeMinIntrinsicWidth(/* No info */) {
    // ** addr: 0x7352a8, size: 0x68
    // 0x7352a8: EnterFrame
    //     0x7352a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7352ac: mov             fp, SP
    // 0x7352b0: AllocStack(0x10)
    //     0x7352b0: sub             SP, SP, #0x10
    // 0x7352b4: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7352b4: stur            x1, [fp, #-8]
    //     0x7352b8: stur            x2, [fp, #-0x10]
    // 0x7352bc: CheckStackOverflow
    //     0x7352bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7352c0: cmp             SP, x16
    //     0x7352c4: b.ls            #0x735308
    // 0x7352c8: r0 = BoxConstraints()
    //     0x7352c8: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x7352cc: StoreField: r0->field_7 = rZR
    //     0x7352cc: stur            xzr, [x0, #7]
    // 0x7352d0: d0 = inf
    //     0x7352d0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x7352d4: StoreField: r0->field_f = d0
    //     0x7352d4: stur            d0, [x0, #0xf]
    // 0x7352d8: ldur            x1, [fp, #-0x10]
    // 0x7352dc: LoadField: d0 = r1->field_7
    //     0x7352dc: ldur            d0, [x1, #7]
    // 0x7352e0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7352e0: stur            d0, [x0, #0x17]
    // 0x7352e4: StoreField: r0->field_1f = d0
    //     0x7352e4: stur            d0, [x0, #0x1f]
    // 0x7352e8: ldur            x1, [fp, #-8]
    // 0x7352ec: mov             x2, x0
    // 0x7352f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7352f0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7352f4: r0 = computeGridSize()
    //     0x7352f4: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x7352f8: LoadField: d0 = r0->field_2f
    //     0x7352f8: ldur            d0, [x0, #0x2f]
    // 0x7352fc: LeaveFrame
    //     0x7352fc: mov             SP, fp
    //     0x735300: ldp             fp, lr, [SP], #0x10
    // 0x735304: ret
    //     0x735304: ret             
    // 0x735308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735308: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73530c: b               #0x7352c8
  }
  _ computeGridSize(/* No info */) {
    // ** addr: 0x735310, size: 0x12c
    // 0x735310: EnterFrame
    //     0x735310: stp             fp, lr, [SP, #-0x10]!
    //     0x735314: mov             fp, SP
    // 0x735318: AllocStack(0x30)
    //     0x735318: sub             SP, SP, #0x30
    // 0x73531c: SetupParameters(RenderLayoutGrid this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x73531c: mov             x0, x2
    //     0x735320: stur            x2, [fp, #-0x10]
    //     0x735324: mov             x2, x1
    //     0x735328: stur            x1, [fp, #-8]
    // 0x73532c: CheckStackOverflow
    //     0x73532c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735330: cmp             SP, x16
    //     0x735334: b.ls            #0x735434
    // 0x735338: mov             x1, x0
    // 0x73533c: r0 = LayoutGridExtensionsForBoxConstraints.constraintsForGridFit()
    //     0x73533c: bl              #0x73b1fc  ; [package:flutter_layout_grid/src/foundation/box.dart] ::LayoutGridExtensionsForBoxConstraints.constraintsForGridFit
    // 0x735340: ldur            x1, [fp, #-8]
    // 0x735344: stur            x0, [fp, #-0x18]
    // 0x735348: r0 = performItemPlacement()
    //     0x735348: bl              #0x7396d0  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::performItemPlacement
    // 0x73534c: ldur            x1, [fp, #-8]
    // 0x735350: LoadField: r2 = r1->field_83
    //     0x735350: ldur            w2, [x1, #0x83]
    // 0x735354: DecompressPointer r2
    //     0x735354: add             x2, x2, HEAP, lsl #32
    // 0x735358: stur            x2, [fp, #-0x30]
    // 0x73535c: LoadField: r3 = r1->field_87
    //     0x73535c: ldur            w3, [x1, #0x87]
    // 0x735360: DecompressPointer r3
    //     0x735360: add             x3, x3, HEAP, lsl #32
    // 0x735364: stur            x3, [fp, #-0x28]
    // 0x735368: LoadField: r5 = r1->field_9b
    //     0x735368: ldur            w5, [x1, #0x9b]
    // 0x73536c: DecompressPointer r5
    //     0x73536c: add             x5, x5, HEAP, lsl #32
    // 0x735370: stur            x5, [fp, #-0x20]
    // 0x735374: r0 = GridSizingInfo()
    //     0x735374: bl              #0x7396c4  ; AllocateGridSizingInfoStub -> GridSizingInfo (size=0x58)
    // 0x735378: mov             x1, x0
    // 0x73537c: ldur            x2, [fp, #-0x30]
    // 0x735380: ldur            x3, [fp, #-0x28]
    // 0x735384: ldur            x5, [fp, #-0x20]
    // 0x735388: stur            x0, [fp, #-0x20]
    // 0x73538c: r0 = GridSizingInfo.fromTrackSizeFunctions()
    //     0x73538c: bl              #0x739404  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::GridSizingInfo.fromTrackSizeFunctions
    // 0x735390: ldur            x1, [fp, #-8]
    // 0x735394: ldur            x3, [fp, #-0x20]
    // 0x735398: ldur            x5, [fp, #-0x18]
    // 0x73539c: r2 = Instance_TrackType
    //     0x73539c: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x7353a0: ldr             x2, [x2, #0x340]
    // 0x7353a4: r0 = _performTrackSizing()
    //     0x7353a4: bl              #0x735d88  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_performTrackSizing
    // 0x7353a8: ldur            x1, [fp, #-8]
    // 0x7353ac: ldur            x3, [fp, #-0x20]
    // 0x7353b0: ldur            x5, [fp, #-0x18]
    // 0x7353b4: r2 = Instance_TrackType
    //     0x7353b4: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c348] Obj!TrackType@e32d61
    //     0x7353b8: ldr             x2, [x2, #0x348]
    // 0x7353bc: r0 = _performTrackSizing()
    //     0x7353bc: bl              #0x735d88  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_performTrackSizing
    // 0x7353c0: ldur            x1, [fp, #-8]
    // 0x7353c4: ldur            x3, [fp, #-0x20]
    // 0x7353c8: ldur            x5, [fp, #-0x18]
    // 0x7353cc: r2 = Instance_TrackType
    //     0x7353cc: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x7353d0: ldr             x2, [x2, #0x340]
    // 0x7353d4: r0 = _stretchIntrinsicTracks()
    //     0x7353d4: bl              #0x735870  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_stretchIntrinsicTracks
    // 0x7353d8: ldur            x1, [fp, #-8]
    // 0x7353dc: ldur            x3, [fp, #-0x20]
    // 0x7353e0: ldur            x5, [fp, #-0x18]
    // 0x7353e4: r2 = Instance_TrackType
    //     0x7353e4: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c348] Obj!TrackType@e32d61
    //     0x7353e8: ldr             x2, [x2, #0x348]
    // 0x7353ec: r0 = _stretchIntrinsicTracks()
    //     0x7353ec: bl              #0x735870  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_stretchIntrinsicTracks
    // 0x7353f0: ldur            x1, [fp, #-0x20]
    // 0x7353f4: r0 = internalGridSize()
    //     0x7353f4: bl              #0x73543c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::internalGridSize
    // 0x7353f8: ldur            x1, [fp, #-0x10]
    // 0x7353fc: mov             x2, x0
    // 0x735400: r0 = constrain()
    //     0x735400: bl              #0x6ce8e8  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x735404: ldur            x1, [fp, #-0x20]
    // 0x735408: StoreField: r1->field_7 = r0
    //     0x735408: stur            w0, [x1, #7]
    //     0x73540c: ldurb           w16, [x1, #-1]
    //     0x735410: ldurb           w17, [x0, #-1]
    //     0x735414: and             x16, x17, x16, lsr #2
    //     0x735418: tst             x16, HEAP, lsr #32
    //     0x73541c: b.eq            #0x735424
    //     0x735420: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x735424: mov             x0, x1
    // 0x735428: LeaveFrame
    //     0x735428: mov             SP, fp
    //     0x73542c: ldp             fp, lr, [SP], #0x10
    // 0x735430: ret
    //     0x735430: ret             
    // 0x735434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735438: b               #0x735338
  }
  _ _stretchIntrinsicTracks(/* No info */) {
    // ** addr: 0x735870, size: 0x29c
    // 0x735870: EnterFrame
    //     0x735870: stp             fp, lr, [SP, #-0x10]!
    //     0x735874: mov             fp, SP
    // 0x735878: AllocStack(0x40)
    //     0x735878: sub             SP, SP, #0x40
    // 0x73587c: SetupParameters(RenderLayoutGrid this /* r1 => r2 */, dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r1 */)
    //     0x73587c: mov             x0, x3
    //     0x735880: stur            x3, [fp, #-0x10]
    //     0x735884: mov             x3, x2
    //     0x735888: stur            x2, [fp, #-8]
    //     0x73588c: mov             x2, x1
    //     0x735890: mov             x1, x5
    // 0x735894: CheckStackOverflow
    //     0x735894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735898: cmp             SP, x16
    //     0x73589c: b.ls            #0x735af4
    // 0x7358a0: mov             x2, x3
    // 0x7358a4: r0 = constraintBoundsForType()
    //     0x7358a4: bl              #0x735cd4  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::constraintBoundsForType
    // 0x7358a8: LoadField: d0 = r0->field_b
    //     0x7358a8: ldur            d0, [x0, #0xb]
    // 0x7358ac: ldur            x1, [fp, #-0x10]
    // 0x7358b0: ldur            x2, [fp, #-8]
    // 0x7358b4: stur            d0, [fp, #-0x28]
    // 0x7358b8: r0 = totalBaseSizeOfTracksForType()
    //     0x7358b8: bl              #0x735c04  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::totalBaseSizeOfTracksForType
    // 0x7358bc: mov             v1.16b, v0.16b
    // 0x7358c0: ldur            d0, [fp, #-0x28]
    // 0x7358c4: fsub            d2, d0, d1
    // 0x7358c8: ldur            x1, [fp, #-0x10]
    // 0x7358cc: ldur            x2, [fp, #-8]
    // 0x7358d0: stur            d2, [fp, #-0x30]
    // 0x7358d4: r0 = totalGapForType()
    //     0x7358d4: bl              #0x735b58  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::totalGapForType
    // 0x7358d8: mov             v1.16b, v0.16b
    // 0x7358dc: ldur            d0, [fp, #-0x30]
    // 0x7358e0: fsub            d2, d0, d1
    // 0x7358e4: stur            d2, [fp, #-0x28]
    // 0x7358e8: d0 = 0.000000
    //     0x7358e8: eor             v0.16b, v0.16b, v0.16b
    // 0x7358ec: fcmp            d0, d2
    // 0x7358f0: b.lt            #0x735904
    // 0x7358f4: r0 = Null
    //     0x7358f4: mov             x0, NULL
    // 0x7358f8: LeaveFrame
    //     0x7358f8: mov             SP, fp
    //     0x7358fc: ldp             fp, lr, [SP], #0x10
    // 0x735900: ret
    //     0x735900: ret             
    // 0x735904: ldur            x0, [fp, #-8]
    // 0x735908: r16 = Instance_TrackType
    //     0x735908: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x73590c: ldr             x16, [x16, #0x340]
    // 0x735910: cmp             w0, w16
    // 0x735914: b.ne            #0x73592c
    // 0x735918: ldur            x3, [fp, #-0x10]
    // 0x73591c: LoadField: r1 = r3->field_1b
    //     0x73591c: ldur            w1, [x3, #0x1b]
    // 0x735920: DecompressPointer r1
    //     0x735920: add             x1, x1, HEAP, lsl #32
    // 0x735924: mov             x4, x1
    // 0x735928: b               #0x73593c
    // 0x73592c: ldur            x3, [fp, #-0x10]
    // 0x735930: LoadField: r1 = r3->field_1f
    //     0x735930: ldur            w1, [x3, #0x1f]
    // 0x735934: DecompressPointer r1
    //     0x735934: add             x1, x1, HEAP, lsl #32
    // 0x735938: mov             x4, x1
    // 0x73593c: stur            x4, [fp, #-0x18]
    // 0x735940: r1 = Function '<anonymous closure>':.
    //     0x735940: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c398] AnonymousClosure: (0x735d5c), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_stretchIntrinsicTracks (0x735870)
    //     0x735944: ldr             x1, [x1, #0x398]
    // 0x735948: r2 = Null
    //     0x735948: mov             x2, NULL
    // 0x73594c: r0 = AllocateClosure()
    //     0x73594c: bl              #0xec1630  ; AllocateClosureStub
    // 0x735950: ldur            x1, [fp, #-0x18]
    // 0x735954: mov             x2, x0
    // 0x735958: r0 = where()
    //     0x735958: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x73595c: mov             x1, x0
    // 0x735960: stur            x0, [fp, #-0x18]
    // 0x735964: r0 = iterator()
    //     0x735964: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0x735968: r1 = LoadClassIdInstr(r0)
    //     0x735968: ldur            x1, [x0, #-1]
    //     0x73596c: ubfx            x1, x1, #0xc, #0x14
    // 0x735970: mov             x16, x0
    // 0x735974: mov             x0, x1
    // 0x735978: mov             x1, x16
    // 0x73597c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x73597c: movz            x17, #0x292d
    //     0x735980: movk            x17, #0x1, lsl #16
    //     0x735984: add             lr, x0, x17
    //     0x735988: ldr             lr, [x21, lr, lsl #3]
    //     0x73598c: blr             lr
    // 0x735990: eor             x1, x0, #0x10
    // 0x735994: tbnz            w1, #4, #0x7359a8
    // 0x735998: r0 = Null
    //     0x735998: mov             x0, NULL
    // 0x73599c: LeaveFrame
    //     0x73599c: mov             SP, fp
    //     0x7359a0: ldp             fp, lr, [SP], #0x10
    // 0x7359a4: ret
    //     0x7359a4: ret             
    // 0x7359a8: ldur            d0, [fp, #-0x28]
    // 0x7359ac: ldur            x16, [fp, #-0x18]
    // 0x7359b0: str             x16, [SP]
    // 0x7359b4: r0 = length()
    //     0x7359b4: bl              #0x913c28  ; [dart:core] Iterable::length
    // 0x7359b8: r1 = LoadInt32Instr(r0)
    //     0x7359b8: sbfx            x1, x0, #1, #0x1f
    //     0x7359bc: tbz             w0, #0, #0x7359c4
    //     0x7359c0: ldur            x1, [x0, #7]
    // 0x7359c4: scvtf           d0, x1
    // 0x7359c8: ldur            d1, [fp, #-0x28]
    // 0x7359cc: fdiv            d2, d1, d0
    // 0x7359d0: ldur            x1, [fp, #-0x18]
    // 0x7359d4: stur            d2, [fp, #-0x30]
    // 0x7359d8: r0 = iterator()
    //     0x7359d8: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0x7359dc: LoadField: r2 = r0->field_b
    //     0x7359dc: ldur            w2, [x0, #0xb]
    // 0x7359e0: DecompressPointer r2
    //     0x7359e0: add             x2, x2, HEAP, lsl #32
    // 0x7359e4: stur            x2, [fp, #-0x20]
    // 0x7359e8: LoadField: r3 = r0->field_f
    //     0x7359e8: ldur            w3, [x0, #0xf]
    // 0x7359ec: DecompressPointer r3
    //     0x7359ec: add             x3, x3, HEAP, lsl #32
    // 0x7359f0: stur            x3, [fp, #-0x18]
    // 0x7359f4: ldur            d0, [fp, #-0x30]
    // 0x7359f8: CheckStackOverflow
    //     0x7359f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7359fc: cmp             SP, x16
    //     0x735a00: b.ls            #0x735afc
    // 0x735a04: CheckStackOverflow
    //     0x735a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735a08: cmp             SP, x16
    //     0x735a0c: b.ls            #0x735b04
    // 0x735a10: r0 = LoadClassIdInstr(r2)
    //     0x735a10: ldur            x0, [x2, #-1]
    //     0x735a14: ubfx            x0, x0, #0xc, #0x14
    // 0x735a18: mov             x1, x2
    // 0x735a1c: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x735a1c: movz            x17, #0x292d
    //     0x735a20: movk            x17, #0x1, lsl #16
    //     0x735a24: add             lr, x0, x17
    //     0x735a28: ldr             lr, [x21, lr, lsl #3]
    //     0x735a2c: blr             lr
    // 0x735a30: tbnz            w0, #4, #0x735ad8
    // 0x735a34: ldur            x2, [fp, #-0x20]
    // 0x735a38: r0 = LoadClassIdInstr(r2)
    //     0x735a38: ldur            x0, [x2, #-1]
    //     0x735a3c: ubfx            x0, x0, #0xc, #0x14
    // 0x735a40: mov             x1, x2
    // 0x735a44: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x735a44: movz            x17, #0x384d
    //     0x735a48: movk            x17, #0x1, lsl #16
    //     0x735a4c: add             lr, x0, x17
    //     0x735a50: ldr             lr, [x21, lr, lsl #3]
    //     0x735a54: blr             lr
    // 0x735a58: ldur            x16, [fp, #-0x18]
    // 0x735a5c: stp             x0, x16, [SP]
    // 0x735a60: ldur            x0, [fp, #-0x18]
    // 0x735a64: ClosureCall
    //     0x735a64: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x735a68: ldur            x2, [x0, #0x1f]
    //     0x735a6c: blr             x2
    // 0x735a70: r16 = true
    //     0x735a70: add             x16, NULL, #0x20  ; true
    // 0x735a74: cmp             w0, w16
    // 0x735a78: b.eq            #0x735a8c
    // 0x735a7c: ldur            d0, [fp, #-0x30]
    // 0x735a80: ldur            x2, [fp, #-0x20]
    // 0x735a84: ldur            x3, [fp, #-0x18]
    // 0x735a88: b               #0x735a04
    // 0x735a8c: ldur            d0, [fp, #-0x30]
    // 0x735a90: ldur            x2, [fp, #-0x20]
    // 0x735a94: r0 = LoadClassIdInstr(r2)
    //     0x735a94: ldur            x0, [x2, #-1]
    //     0x735a98: ubfx            x0, x0, #0xc, #0x14
    // 0x735a9c: mov             x1, x2
    // 0x735aa0: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x735aa0: movz            x17, #0x384d
    //     0x735aa4: movk            x17, #0x1, lsl #16
    //     0x735aa8: add             lr, x0, x17
    //     0x735aac: ldr             lr, [x21, lr, lsl #3]
    //     0x735ab0: blr             lr
    // 0x735ab4: LoadField: d0 = r0->field_13
    //     0x735ab4: ldur            d0, [x0, #0x13]
    // 0x735ab8: ldur            d1, [fp, #-0x30]
    // 0x735abc: fadd            d2, d0, d1
    // 0x735ac0: StoreField: r0->field_13 = d2
    //     0x735ac0: stur            d2, [x0, #0x13]
    // 0x735ac4: mov             x1, x0
    // 0x735ac8: r0 = _increaseGrowthLimitIfNecessary()
    //     0x735ac8: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x735acc: ldur            x2, [fp, #-0x20]
    // 0x735ad0: ldur            x3, [fp, #-0x18]
    // 0x735ad4: b               #0x7359f4
    // 0x735ad8: ldur            x1, [fp, #-0x10]
    // 0x735adc: ldur            x2, [fp, #-8]
    // 0x735ae0: r0 = invalidateTrackStartsForType()
    //     0x735ae0: bl              #0x735b0c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::invalidateTrackStartsForType
    // 0x735ae4: r0 = Null
    //     0x735ae4: mov             x0, NULL
    // 0x735ae8: LeaveFrame
    //     0x735ae8: mov             SP, fp
    //     0x735aec: ldp             fp, lr, [SP], #0x10
    // 0x735af0: ret
    //     0x735af0: ret             
    // 0x735af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735af4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735af8: b               #0x7358a0
    // 0x735afc: r0 = StackOverflowSharedWithFPURegs()
    //     0x735afc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x735b00: b               #0x735a04
    // 0x735b04: r0 = StackOverflowSharedWithFPURegs()
    //     0x735b04: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x735b08: b               #0x735a10
  }
  [closure] bool <anonymous closure>(dynamic, GridTrack) {
    // ** addr: 0x735d5c, size: 0x2c
    // 0x735d5c: ldr             x1, [SP]
    // 0x735d60: LoadField: r2 = r1->field_f
    //     0x735d60: ldur            w2, [x1, #0xf]
    // 0x735d64: DecompressPointer r2
    //     0x735d64: add             x2, x2, HEAP, lsl #32
    // 0x735d68: r1 = LoadClassIdInstr(r2)
    //     0x735d68: ldur            x1, [x2, #-1]
    //     0x735d6c: ubfx            x1, x1, #0xc, #0x14
    // 0x735d70: cmp             x1, #0xec2
    // 0x735d74: b.ne            #0x735d80
    // 0x735d78: r0 = true
    //     0x735d78: add             x0, NULL, #0x20  ; true
    // 0x735d7c: b               #0x735d84
    // 0x735d80: r0 = false
    //     0x735d80: add             x0, NULL, #0x30  ; false
    // 0x735d84: ret
    //     0x735d84: ret             
  }
  _ _performTrackSizing(/* No info */) {
    // ** addr: 0x735d88, size: 0x5c
    // 0x735d88: EnterFrame
    //     0x735d88: stp             fp, lr, [SP, #-0x10]!
    //     0x735d8c: mov             fp, SP
    // 0x735d90: AllocStack(0x10)
    //     0x735d90: sub             SP, SP, #0x10
    // 0x735d94: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x735d94: mov             x4, x2
    //     0x735d98: mov             x0, x3
    //     0x735d9c: stur            x2, [fp, #-8]
    //     0x735da0: stur            x3, [fp, #-0x10]
    // 0x735da4: CheckStackOverflow
    //     0x735da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735da8: cmp             SP, x16
    //     0x735dac: b.ls            #0x735ddc
    // 0x735db0: mov             x2, x4
    // 0x735db4: mov             x3, x0
    // 0x735db8: r0 = _performTrackSizingInternal()
    //     0x735db8: bl              #0x735e10  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_performTrackSizingInternal
    // 0x735dbc: ldur            x1, [fp, #-0x10]
    // 0x735dc0: ldur            x2, [fp, #-8]
    // 0x735dc4: stur            x0, [fp, #-8]
    // 0x735dc8: r0 = markTrackTypeSized()
    //     0x735dc8: bl              #0x735de4  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::markTrackTypeSized
    // 0x735dcc: ldur            x0, [fp, #-8]
    // 0x735dd0: LeaveFrame
    //     0x735dd0: mov             SP, fp
    //     0x735dd4: ldp             fp, lr, [SP], #0x10
    // 0x735dd8: ret
    //     0x735dd8: ret             
    // 0x735ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x735ddc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x735de0: b               #0x735db0
  }
  _ _performTrackSizingInternal(/* No info */) {
    // ** addr: 0x735e10, size: 0x8a8
    // 0x735e10: EnterFrame
    //     0x735e10: stp             fp, lr, [SP, #-0x10]!
    //     0x735e14: mov             fp, SP
    // 0x735e18: AllocStack(0xc0)
    //     0x735e18: sub             SP, SP, #0xc0
    // 0x735e1c: SetupParameters(RenderLayoutGrid this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r7, fp-0x20 */, dynamic _ /* r5 => r0, fp-0x28 */)
    //     0x735e1c: mov             x4, x1
    //     0x735e20: mov             x7, x3
    //     0x735e24: stur            x3, [fp, #-0x20]
    //     0x735e28: mov             x3, x2
    //     0x735e2c: mov             x0, x5
    //     0x735e30: stur            x1, [fp, #-0x10]
    //     0x735e34: stur            x2, [fp, #-0x18]
    //     0x735e38: stur            x5, [fp, #-0x28]
    // 0x735e3c: CheckStackOverflow
    //     0x735e3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735e40: cmp             SP, x16
    //     0x735e44: b.ls            #0x73668c
    // 0x735e48: r16 = Instance_TrackType
    //     0x735e48: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735e4c: ldr             x16, [x16, #0x340]
    // 0x735e50: cmp             w3, w16
    // 0x735e54: b.ne            #0x735e60
    // 0x735e58: r5 = Instance_Axis
    //     0x735e58: ldr             x5, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x735e5c: b               #0x735e64
    // 0x735e60: r5 = Instance_Axis
    //     0x735e60: ldr             x5, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x735e64: stur            x5, [fp, #-8]
    // 0x735e68: r1 = <GridTrack>
    //     0x735e68: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x735e6c: ldr             x1, [x1, #0x3a8]
    // 0x735e70: r2 = 0
    //     0x735e70: movz            x2, #0
    // 0x735e74: r0 = _GrowableList()
    //     0x735e74: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x735e78: r1 = <GridTrack>
    //     0x735e78: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x735e7c: ldr             x1, [x1, #0x3a8]
    // 0x735e80: r2 = 0
    //     0x735e80: movz            x2, #0
    // 0x735e84: stur            x0, [fp, #-0x30]
    // 0x735e88: r0 = _GrowableList()
    //     0x735e88: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x735e8c: mov             x3, x0
    // 0x735e90: ldur            x0, [fp, #-0x18]
    // 0x735e94: stur            x3, [fp, #-0x40]
    // 0x735e98: r16 = Instance_TrackType
    //     0x735e98: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c340] Obj!TrackType@e32d81
    //     0x735e9c: ldr             x16, [x16, #0x340]
    // 0x735ea0: cmp             w0, w16
    // 0x735ea4: b.ne            #0x735ebc
    // 0x735ea8: ldur            x7, [fp, #-0x20]
    // 0x735eac: LoadField: r1 = r7->field_1b
    //     0x735eac: ldur            w1, [x7, #0x1b]
    // 0x735eb0: DecompressPointer r1
    //     0x735eb0: add             x1, x1, HEAP, lsl #32
    // 0x735eb4: mov             x5, x1
    // 0x735eb8: b               #0x735ecc
    // 0x735ebc: ldur            x7, [fp, #-0x20]
    // 0x735ec0: LoadField: r1 = r7->field_1f
    //     0x735ec0: ldur            w1, [x7, #0x1f]
    // 0x735ec4: DecompressPointer r1
    //     0x735ec4: add             x1, x1, HEAP, lsl #32
    // 0x735ec8: mov             x5, x1
    // 0x735ecc: ldur            x1, [fp, #-0x28]
    // 0x735ed0: mov             x2, x0
    // 0x735ed4: stur            x5, [fp, #-0x38]
    // 0x735ed8: r0 = constraintBoundsForType()
    //     0x735ed8: bl              #0x735cd4  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] ::constraintBoundsForType
    // 0x735edc: mov             x1, x0
    // 0x735ee0: ldur            x5, [fp, #-0x38]
    // 0x735ee4: stur            x1, [fp, #-0x48]
    // 0x735ee8: LoadField: r2 = r5->field_b
    //     0x735ee8: ldur            w2, [x5, #0xb]
    // 0x735eec: DecompressPointer r2
    //     0x735eec: add             x2, x2, HEAP, lsl #32
    // 0x735ef0: stur            x2, [fp, #-0x28]
    // 0x735ef4: r0 = LoadClassIdInstr(r2)
    //     0x735ef4: ldur            x0, [x2, #-1]
    //     0x735ef8: ubfx            x0, x0, #0xc, #0x14
    // 0x735efc: str             x2, [SP]
    // 0x735f00: r0 = GDT[cid_x0 + 0xc834]()
    //     0x735f00: movz            x17, #0xc834
    //     0x735f04: add             lr, x0, x17
    //     0x735f08: ldr             lr, [x21, lr, lsl #3]
    //     0x735f0c: blr             lr
    // 0x735f10: r1 = LoadInt32Instr(r0)
    //     0x735f10: sbfx            x1, x0, #1, #0x1f
    //     0x735f14: tbz             w0, #0, #0x735f1c
    //     0x735f18: ldur            x1, [x0, #7]
    // 0x735f1c: sub             x0, x1, #1
    // 0x735f20: scvtf           d0, x0
    // 0x735f24: ldur            x0, [fp, #-0x48]
    // 0x735f28: stur            d0, [fp, #-0x90]
    // 0x735f2c: LoadField: d1 = r0->field_13
    //     0x735f2c: ldur            d1, [x0, #0x13]
    // 0x735f30: mov             x0, v1.d[0]
    // 0x735f34: and             x0, x0, #0x7fffffffffffffff
    // 0x735f38: r17 = 9218868437227405312
    //     0x735f38: orr             x17, xzr, #0x7ff0000000000000
    // 0x735f3c: cmp             x0, x17
    // 0x735f40: b.eq            #0x735f54
    // 0x735f44: fcmp            d1, d1
    // 0x735f48: b.vs            #0x735f54
    // 0x735f4c: fsub            d2, d1, d0
    // 0x735f50: b               #0x735f58
    // 0x735f54: d2 = 0.000000
    //     0x735f54: eor             v2.16b, v2.16b, v2.16b
    // 0x735f58: stur            d2, [fp, #-0x88]
    // 0x735f5c: mov             x0, v1.d[0]
    // 0x735f60: and             x0, x0, #0x7fffffffffffffff
    // 0x735f64: r17 = 9218868437227405312
    //     0x735f64: orr             x17, xzr, #0x7ff0000000000000
    // 0x735f68: cmp             x0, x17
    // 0x735f6c: b.eq            #0x735f88
    // 0x735f70: fcmp            d1, d1
    // 0x735f74: r16 = true
    //     0x735f74: add             x16, NULL, #0x20  ; true
    // 0x735f78: r17 = false
    //     0x735f78: add             x17, NULL, #0x30  ; false
    // 0x735f7c: csel            x0, x16, x17, vc
    // 0x735f80: mov             x1, x0
    // 0x735f84: b               #0x735f8c
    // 0x735f88: r1 = false
    //     0x735f88: add             x1, NULL, #0x30  ; false
    // 0x735f8c: stur            x1, [fp, #-0x48]
    // 0x735f90: r5 = 0
    //     0x735f90: movz            x5, #0
    // 0x735f94: ldur            x3, [fp, #-0x18]
    // 0x735f98: ldur            x4, [fp, #-0x30]
    // 0x735f9c: ldur            x2, [fp, #-0x28]
    // 0x735fa0: stur            x5, [fp, #-0x50]
    // 0x735fa4: CheckStackOverflow
    //     0x735fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x735fa8: cmp             SP, x16
    //     0x735fac: b.ls            #0x736694
    // 0x735fb0: r0 = LoadClassIdInstr(r2)
    //     0x735fb0: ldur            x0, [x2, #-1]
    //     0x735fb4: ubfx            x0, x0, #0xc, #0x14
    // 0x735fb8: str             x2, [SP]
    // 0x735fbc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x735fbc: movz            x17, #0xc834
    //     0x735fc0: add             lr, x0, x17
    //     0x735fc4: ldr             lr, [x21, lr, lsl #3]
    //     0x735fc8: blr             lr
    // 0x735fcc: r1 = LoadInt32Instr(r0)
    //     0x735fcc: sbfx            x1, x0, #1, #0x1f
    //     0x735fd0: tbz             w0, #0, #0x735fd8
    //     0x735fd4: ldur            x1, [x0, #7]
    // 0x735fd8: ldur            x3, [fp, #-0x50]
    // 0x735fdc: cmp             x3, x1
    // 0x735fe0: b.ge            #0x736278
    // 0x735fe4: ldur            x4, [fp, #-0x28]
    // 0x735fe8: r0 = LoadClassIdInstr(r4)
    //     0x735fe8: ldur            x0, [x4, #-1]
    //     0x735fec: ubfx            x0, x0, #0xc, #0x14
    // 0x735ff0: mov             x1, x4
    // 0x735ff4: mov             x2, x3
    // 0x735ff8: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x735ff8: movz            x17, #0xd28f
    //     0x735ffc: add             lr, x0, x17
    //     0x736000: ldr             lr, [x21, lr, lsl #3]
    //     0x736004: blr             lr
    // 0x736008: stur            x0, [fp, #-0x58]
    // 0x73600c: LoadField: r1 = r0->field_f
    //     0x73600c: ldur            w1, [x0, #0xf]
    // 0x736010: DecompressPointer r1
    //     0x736010: add             x1, x1, HEAP, lsl #32
    // 0x736014: stur            x1, [fp, #-0x70]
    // 0x736018: r2 = LoadClassIdInstr(r1)
    //     0x736018: ldur            x2, [x1, #-1]
    //     0x73601c: ubfx            x2, x2, #0xc, #0x14
    // 0x736020: stur            x2, [fp, #-0x68]
    // 0x736024: cmp             x2, #0xec2
    // 0x736028: b.ne            #0x7360fc
    // 0x73602c: ldur            x2, [fp, #-0x30]
    // 0x736030: StoreField: r0->field_13 = rZR
    //     0x736030: stur            xzr, [x0, #0x13]
    // 0x736034: mov             x1, x0
    // 0x736038: r0 = _increaseGrowthLimitIfNecessary()
    //     0x736038: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x73603c: ldur            x0, [fp, #-0x58]
    // 0x736040: d0 = inf
    //     0x736040: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x736044: StoreField: r0->field_1b = d0
    //     0x736044: stur            d0, [x0, #0x1b]
    // 0x736048: mov             x1, x0
    // 0x73604c: r0 = _increaseGrowthLimitIfNecessary()
    //     0x73604c: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x736050: ldur            x0, [fp, #-0x58]
    // 0x736054: r2 = Null
    //     0x736054: mov             x2, NULL
    // 0x736058: r1 = Null
    //     0x736058: mov             x1, NULL
    // 0x73605c: r4 = LoadClassIdInstr(r0)
    //     0x73605c: ldur            x4, [x0, #-1]
    //     0x736060: ubfx            x4, x4, #0xc, #0x14
    // 0x736064: cmp             x4, #0x8f4
    // 0x736068: b.eq            #0x736080
    // 0x73606c: r8 = GridTrack
    //     0x73606c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c3b0] Type: GridTrack
    //     0x736070: ldr             x8, [x8, #0x3b0]
    // 0x736074: r3 = Null
    //     0x736074: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c3b8] Null
    //     0x736078: ldr             x3, [x3, #0x3b8]
    // 0x73607c: r0 = GridTrack()
    //     0x73607c: bl              #0x735850  ; IsType_GridTrack_Stub
    // 0x736080: ldur            x0, [fp, #-0x30]
    // 0x736084: LoadField: r1 = r0->field_b
    //     0x736084: ldur            w1, [x0, #0xb]
    // 0x736088: LoadField: r2 = r0->field_f
    //     0x736088: ldur            w2, [x0, #0xf]
    // 0x73608c: DecompressPointer r2
    //     0x73608c: add             x2, x2, HEAP, lsl #32
    // 0x736090: LoadField: r3 = r2->field_b
    //     0x736090: ldur            w3, [x2, #0xb]
    // 0x736094: r2 = LoadInt32Instr(r1)
    //     0x736094: sbfx            x2, x1, #1, #0x1f
    // 0x736098: stur            x2, [fp, #-0x60]
    // 0x73609c: r1 = LoadInt32Instr(r3)
    //     0x73609c: sbfx            x1, x3, #1, #0x1f
    // 0x7360a0: cmp             x2, x1
    // 0x7360a4: b.ne            #0x7360b0
    // 0x7360a8: mov             x1, x0
    // 0x7360ac: r0 = _growToNextCapacity()
    //     0x7360ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7360b0: ldur            x6, [fp, #-0x30]
    // 0x7360b4: ldur            x2, [fp, #-0x60]
    // 0x7360b8: add             x0, x2, #1
    // 0x7360bc: lsl             x1, x0, #1
    // 0x7360c0: StoreField: r6->field_b = r1
    //     0x7360c0: stur            w1, [x6, #0xb]
    // 0x7360c4: LoadField: r1 = r6->field_f
    //     0x7360c4: ldur            w1, [x6, #0xf]
    // 0x7360c8: DecompressPointer r1
    //     0x7360c8: add             x1, x1, HEAP, lsl #32
    // 0x7360cc: ldur            x0, [fp, #-0x58]
    // 0x7360d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7360d0: add             x25, x1, x2, lsl #2
    //     0x7360d4: add             x25, x25, #0xf
    //     0x7360d8: str             w0, [x25]
    //     0x7360dc: tbz             w0, #0, #0x7360f8
    //     0x7360e0: ldurb           w16, [x1, #-1]
    //     0x7360e4: ldurb           w17, [x0, #-1]
    //     0x7360e8: and             x16, x17, x16, lsr #2
    //     0x7360ec: tst             x16, HEAP, lsr #32
    //     0x7360f0: b.eq            #0x7360f8
    //     0x7360f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7360f8: b               #0x736200
    // 0x7360fc: ldur            x6, [fp, #-0x30]
    // 0x736100: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x736100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x736104: ldr             x0, [x0]
    //     0x736108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x73610c: cmp             w0, w16
    //     0x736110: b.ne            #0x73611c
    //     0x736114: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x736118: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x73611c: r1 = <RenderBox>
    //     0x73611c: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x736120: ldr             x1, [x1, #0x1d8]
    // 0x736124: stur            x0, [fp, #-0x78]
    // 0x736128: r0 = AllocateGrowableArray()
    //     0x736128: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x73612c: mov             x1, x0
    // 0x736130: ldur            x0, [fp, #-0x78]
    // 0x736134: stur            x1, [fp, #-0x80]
    // 0x736138: StoreField: r1->field_f = r0
    //     0x736138: stur            w0, [x1, #0xf]
    // 0x73613c: StoreField: r1->field_b = rZR
    //     0x73613c: stur            wzr, [x1, #0xb]
    // 0x736140: ldur            x0, [fp, #-0x68]
    // 0x736144: cmp             x0, #0xec2
    // 0x736148: b.ne            #0x7361d0
    // 0x73614c: ldur            x2, [fp, #-0x18]
    // 0x736150: ldur            x0, [fp, #-0x70]
    // 0x736154: r1 = 3
    //     0x736154: movz            x1, #0x3
    // 0x736158: r0 = AllocateContext()
    //     0x736158: bl              #0xec126c  ; AllocateContextStub
    // 0x73615c: mov             x3, x0
    // 0x736160: ldur            x0, [fp, #-0x70]
    // 0x736164: stur            x3, [fp, #-0x78]
    // 0x736168: StoreField: r3->field_f = r0
    //     0x736168: stur            w0, [x3, #0xf]
    // 0x73616c: ldur            x0, [fp, #-0x18]
    // 0x736170: StoreField: r3->field_13 = r0
    //     0x736170: stur            w0, [x3, #0x13]
    // 0x736174: r1 = Function '<anonymous closure>':.
    //     0x736174: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3c8] AnonymousClosure: (0x73853c), of [package:flutter_layout_grid/src/rendering/track_size.dart] IntrinsicContentTrackSize
    //     0x736178: ldr             x1, [x1, #0x3c8]
    // 0x73617c: r2 = Null
    //     0x73617c: mov             x2, NULL
    // 0x736180: r0 = AllocateClosure()
    //     0x736180: bl              #0xec1630  ; AllocateClosureStub
    // 0x736184: ldur            x2, [fp, #-0x78]
    // 0x736188: ArrayStore: r2[0] = r0  ; List_4
    //     0x736188: stur            w0, [x2, #0x17]
    // 0x73618c: r1 = Function '<anonymous closure>':.
    //     0x73618c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3d0] AnonymousClosure: (0x739214), of [package:flutter_layout_grid/src/rendering/track_size.dart] IntrinsicContentTrackSize
    //     0x736190: ldr             x1, [x1, #0x3d0]
    // 0x736194: r0 = AllocateClosure()
    //     0x736194: bl              #0xec1630  ; AllocateClosureStub
    // 0x736198: r16 = <double>
    //     0x736198: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x73619c: ldur            lr, [fp, #-0x80]
    // 0x7361a0: stp             lr, x16, [SP, #8]
    // 0x7361a4: str             x0, [SP]
    // 0x7361a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7361a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7361ac: r0 = map()
    //     0x7361ac: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x7361b0: r16 = <double>
    //     0x7361b0: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x7361b4: stp             x0, x16, [SP]
    // 0x7361b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7361b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7361bc: r0 = max()
    //     0x7361bc: bl              #0x738f14  ; [package:quiver/src/iterables/min_max.dart] ::max
    // 0x7361c0: cmp             w0, NULL
    // 0x7361c4: b.eq            #0x73669c
    // 0x7361c8: LoadField: d0 = r0->field_7
    //     0x7361c8: ldur            d0, [x0, #7]
    // 0x7361cc: b               #0x7361d8
    // 0x7361d0: ldur            x0, [fp, #-0x70]
    // 0x7361d4: LoadField: d0 = r0->field_b
    //     0x7361d4: ldur            d0, [x0, #0xb]
    // 0x7361d8: ldur            x0, [fp, #-0x58]
    // 0x7361dc: stur            d0, [fp, #-0x98]
    // 0x7361e0: StoreField: r0->field_1b = d0
    //     0x7361e0: stur            d0, [x0, #0x1b]
    // 0x7361e4: mov             x1, x0
    // 0x7361e8: r0 = _increaseGrowthLimitIfNecessary()
    //     0x7361e8: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x7361ec: ldur            d0, [fp, #-0x98]
    // 0x7361f0: ldur            x0, [fp, #-0x58]
    // 0x7361f4: StoreField: r0->field_13 = d0
    //     0x7361f4: stur            d0, [x0, #0x13]
    // 0x7361f8: mov             x1, x0
    // 0x7361fc: r0 = _increaseGrowthLimitIfNecessary()
    //     0x7361fc: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x736200: ldur            x1, [fp, #-0x58]
    // 0x736204: LoadField: d0 = r1->field_1b
    //     0x736204: ldur            d0, [x1, #0x1b]
    // 0x736208: LoadField: d1 = r1->field_13
    //     0x736208: ldur            d1, [x1, #0x13]
    // 0x73620c: fcmp            d0, d1
    // 0x736210: b.le            #0x73621c
    // 0x736214: d2 = 0.000000
    //     0x736214: eor             v2.16b, v2.16b, v2.16b
    // 0x736218: b               #0x736254
    // 0x73621c: fcmp            d1, d0
    // 0x736220: b.le            #0x736230
    // 0x736224: mov             v0.16b, v1.16b
    // 0x736228: d2 = 0.000000
    //     0x736228: eor             v2.16b, v2.16b, v2.16b
    // 0x73622c: b               #0x736254
    // 0x736230: d2 = 0.000000
    //     0x736230: eor             v2.16b, v2.16b, v2.16b
    // 0x736234: fcmp            d0, d2
    // 0x736238: b.ne            #0x736248
    // 0x73623c: fadd            d3, d0, d1
    // 0x736240: mov             v0.16b, v3.16b
    // 0x736244: b               #0x736254
    // 0x736248: fcmp            d1, d1
    // 0x73624c: b.vc            #0x736254
    // 0x736250: mov             v0.16b, v1.16b
    // 0x736254: ldur            x0, [fp, #-0x50]
    // 0x736258: StoreField: r1->field_1b = d0
    //     0x736258: stur            d0, [x1, #0x1b]
    // 0x73625c: r0 = _increaseGrowthLimitIfNecessary()
    //     0x73625c: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x736260: ldur            x0, [fp, #-0x50]
    // 0x736264: add             x5, x0, #1
    // 0x736268: ldur            d2, [fp, #-0x88]
    // 0x73626c: ldur            d0, [fp, #-0x90]
    // 0x736270: ldur            x1, [fp, #-0x48]
    // 0x736274: b               #0x735f94
    // 0x736278: ldur            x4, [fp, #-0x38]
    // 0x73627c: ldur            x0, [fp, #-0x28]
    // 0x736280: ldur            x1, [fp, #-0x10]
    // 0x736284: ldur            x2, [fp, #-0x18]
    // 0x736288: ldur            x3, [fp, #-8]
    // 0x73628c: mov             x5, x4
    // 0x736290: ldur            x6, [fp, #-0x30]
    // 0x736294: ldur            x7, [fp, #-0x20]
    // 0x736298: r0 = _resolveIntrinsicTrackSizes()
    //     0x736298: bl              #0x73717c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_resolveIntrinsicTrackSizes
    // 0x73629c: ldur            x1, [fp, #-0x38]
    // 0x7362a0: LoadField: r2 = r1->field_7
    //     0x7362a0: ldur            w2, [x1, #7]
    // 0x7362a4: DecompressPointer r2
    //     0x7362a4: add             x2, x2, HEAP, lsl #32
    // 0x7362a8: ldur            x3, [fp, #-0x28]
    // 0x7362ac: stur            x2, [fp, #-0x18]
    // 0x7362b0: r0 = LoadClassIdInstr(r3)
    //     0x7362b0: ldur            x0, [x3, #-1]
    //     0x7362b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7362b8: str             x3, [SP]
    // 0x7362bc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7362bc: movz            x17, #0xc834
    //     0x7362c0: add             lr, x0, x17
    //     0x7362c4: ldr             lr, [x21, lr, lsl #3]
    //     0x7362c8: blr             lr
    // 0x7362cc: r1 = LoadInt32Instr(r0)
    //     0x7362cc: sbfx            x1, x0, #1, #0x1f
    //     0x7362d0: tbz             w0, #0, #0x7362d8
    //     0x7362d4: ldur            x1, [x0, #7]
    // 0x7362d8: stur            x1, [fp, #-0x60]
    // 0x7362dc: ldur            d0, [fp, #-0x90]
    // 0x7362e0: ldur            d1, [fp, #-0x90]
    // 0x7362e4: r3 = 0
    //     0x7362e4: movz            x3, #0
    // 0x7362e8: ldur            x2, [fp, #-0x28]
    // 0x7362ec: stur            x3, [fp, #-0x50]
    // 0x7362f0: stur            d0, [fp, #-0x90]
    // 0x7362f4: stur            d1, [fp, #-0x98]
    // 0x7362f8: CheckStackOverflow
    //     0x7362f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7362fc: cmp             SP, x16
    //     0x736300: b.ls            #0x7366a0
    // 0x736304: r0 = LoadClassIdInstr(r2)
    //     0x736304: ldur            x0, [x2, #-1]
    //     0x736308: ubfx            x0, x0, #0xc, #0x14
    // 0x73630c: str             x2, [SP]
    // 0x736310: r0 = GDT[cid_x0 + 0xc834]()
    //     0x736310: movz            x17, #0xc834
    //     0x736314: add             lr, x0, x17
    //     0x736318: ldr             lr, [x21, lr, lsl #3]
    //     0x73631c: blr             lr
    // 0x736320: r1 = LoadInt32Instr(r0)
    //     0x736320: sbfx            x1, x0, #1, #0x1f
    //     0x736324: tbz             w0, #0, #0x73632c
    //     0x736328: ldur            x1, [x0, #7]
    // 0x73632c: ldur            x3, [fp, #-0x60]
    // 0x736330: cmp             x3, x1
    // 0x736334: b.ne            #0x73666c
    // 0x736338: ldur            x4, [fp, #-0x50]
    // 0x73633c: cmp             x4, x1
    // 0x736340: b.ge            #0x7363e0
    // 0x736344: ldur            x5, [fp, #-0x28]
    // 0x736348: r0 = LoadClassIdInstr(r5)
    //     0x736348: ldur            x0, [x5, #-1]
    //     0x73634c: ubfx            x0, x0, #0xc, #0x14
    // 0x736350: mov             x1, x5
    // 0x736354: mov             x2, x4
    // 0x736358: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x736358: movz            x17, #0xd28f
    //     0x73635c: add             lr, x0, x17
    //     0x736360: ldr             lr, [x21, lr, lsl #3]
    //     0x736364: blr             lr
    // 0x736368: mov             x3, x0
    // 0x73636c: ldur            x0, [fp, #-0x50]
    // 0x736370: stur            x3, [fp, #-0x30]
    // 0x736374: add             x4, x0, #1
    // 0x736378: stur            x4, [fp, #-0x68]
    // 0x73637c: cmp             w3, NULL
    // 0x736380: b.ne            #0x7363b4
    // 0x736384: mov             x0, x3
    // 0x736388: ldur            x2, [fp, #-0x18]
    // 0x73638c: r1 = Null
    //     0x73638c: mov             x1, NULL
    // 0x736390: cmp             w2, NULL
    // 0x736394: b.eq            #0x7363b4
    // 0x736398: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x736398: ldur            w4, [x2, #0x17]
    // 0x73639c: DecompressPointer r4
    //     0x73639c: add             x4, x4, HEAP, lsl #32
    // 0x7363a0: r8 = X0
    //     0x7363a0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7363a4: LoadField: r9 = r4->field_7
    //     0x7363a4: ldur            x9, [x4, #7]
    // 0x7363a8: r3 = Null
    //     0x7363a8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c3d8] Null
    //     0x7363ac: ldr             x3, [x3, #0x3d8]
    // 0x7363b0: blr             x9
    // 0x7363b4: ldur            d3, [fp, #-0x90]
    // 0x7363b8: ldur            d2, [fp, #-0x98]
    // 0x7363bc: ldur            x0, [fp, #-0x30]
    // 0x7363c0: LoadField: d0 = r0->field_13
    //     0x7363c0: ldur            d0, [x0, #0x13]
    // 0x7363c4: fadd            d4, d3, d0
    // 0x7363c8: LoadField: d0 = r0->field_1b
    //     0x7363c8: ldur            d0, [x0, #0x1b]
    // 0x7363cc: fadd            d1, d2, d0
    // 0x7363d0: mov             v0.16b, v4.16b
    // 0x7363d4: ldur            x3, [fp, #-0x68]
    // 0x7363d8: ldur            x1, [fp, #-0x60]
    // 0x7363dc: b               #0x7362e8
    // 0x7363e0: ldur            d4, [fp, #-0x88]
    // 0x7363e4: ldur            d3, [fp, #-0x90]
    // 0x7363e8: ldur            d2, [fp, #-0x98]
    // 0x7363ec: ldur            x0, [fp, #-0x48]
    // 0x7363f0: fsub            d5, d4, d3
    // 0x7363f4: ldur            x1, [fp, #-0x20]
    // 0x7363f8: mov             v0.16b, v3.16b
    // 0x7363fc: mov             v1.16b, v2.16b
    // 0x736400: ldur            x2, [fp, #-8]
    // 0x736404: stur            d5, [fp, #-0xa0]
    // 0x736408: r0 = setMinMaxTrackSizesForAxis()
    //     0x736408: bl              #0x737154  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::setMinMaxTrackSizesForAxis
    // 0x73640c: ldur            x0, [fp, #-0x48]
    // 0x736410: tbnz            w0, #4, #0x736434
    // 0x736414: ldur            d0, [fp, #-0xa0]
    // 0x736418: d1 = 0.000000
    //     0x736418: eor             v1.16b, v1.16b, v1.16b
    // 0x73641c: fcmp            d1, d0
    // 0x736420: b.le            #0x73643c
    // 0x736424: ldur            x0, [fp, #-0x38]
    // 0x736428: LeaveFrame
    //     0x736428: mov             SP, fp
    //     0x73642c: ldp             fp, lr, [SP], #0x10
    // 0x736430: ret
    //     0x736430: ret             
    // 0x736434: ldur            d0, [fp, #-0xa0]
    // 0x736438: d1 = 0.000000
    //     0x736438: eor             v1.16b, v1.16b, v1.16b
    // 0x73643c: tbnz            w0, #4, #0x736480
    // 0x736440: ldur            d3, [fp, #-0x90]
    // 0x736444: ldur            d2, [fp, #-0x98]
    // 0x736448: fcmp            d2, d3
    // 0x73644c: b.le            #0x736480
    // 0x736450: r1 = <GridTrack>
    //     0x736450: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3a8] TypeArguments: <GridTrack>
    //     0x736454: ldr             x1, [x1, #0x3a8]
    // 0x736458: r2 = 0
    //     0x736458: movz            x2, #0
    // 0x73645c: r0 = _GrowableList()
    //     0x73645c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x736460: ldur            x1, [fp, #-0x10]
    // 0x736464: ldur            d0, [fp, #-0xa0]
    // 0x736468: ldur            x2, [fp, #-0x38]
    // 0x73646c: mov             x3, x0
    // 0x736470: r5 = Instance__IntrinsicDimension
    //     0x736470: add             x5, PP, #0x4c, lsl #12  ; [pp+0x4c3e8] Obj!_IntrinsicDimension@e32dc1
    //     0x736474: ldr             x5, [x5, #0x3e8]
    // 0x736478: r0 = _distributeFreeSpace()
    //     0x736478: bl              #0x73687c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeFreeSpace
    // 0x73647c: b               #0x7365b8
    // 0x736480: ldur            x1, [fp, #-0x28]
    // 0x736484: r0 = LoadClassIdInstr(r1)
    //     0x736484: ldur            x0, [x1, #-1]
    //     0x736488: ubfx            x0, x0, #0xc, #0x14
    // 0x73648c: str             x1, [SP]
    // 0x736490: r0 = GDT[cid_x0 + 0xc834]()
    //     0x736490: movz            x17, #0xc834
    //     0x736494: add             lr, x0, x17
    //     0x736498: ldr             lr, [x21, lr, lsl #3]
    //     0x73649c: blr             lr
    // 0x7364a0: r1 = LoadInt32Instr(r0)
    //     0x7364a0: sbfx            x1, x0, #1, #0x1f
    //     0x7364a4: tbz             w0, #0, #0x7364ac
    //     0x7364a8: ldur            x1, [x0, #7]
    // 0x7364ac: stur            x1, [fp, #-0x60]
    // 0x7364b0: ldur            d0, [fp, #-0xa0]
    // 0x7364b4: r3 = 0
    //     0x7364b4: movz            x3, #0
    // 0x7364b8: ldur            x2, [fp, #-0x28]
    // 0x7364bc: stur            x3, [fp, #-0x50]
    // 0x7364c0: stur            d0, [fp, #-0xa0]
    // 0x7364c4: CheckStackOverflow
    //     0x7364c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7364c8: cmp             SP, x16
    //     0x7364cc: b.ls            #0x7366a8
    // 0x7364d0: r0 = LoadClassIdInstr(r2)
    //     0x7364d0: ldur            x0, [x2, #-1]
    //     0x7364d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7364d8: str             x2, [SP]
    // 0x7364dc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x7364dc: movz            x17, #0xc834
    //     0x7364e0: add             lr, x0, x17
    //     0x7364e4: ldr             lr, [x21, lr, lsl #3]
    //     0x7364e8: blr             lr
    // 0x7364ec: r1 = LoadInt32Instr(r0)
    //     0x7364ec: sbfx            x1, x0, #1, #0x1f
    //     0x7364f0: tbz             w0, #0, #0x7364f8
    //     0x7364f4: ldur            x1, [x0, #7]
    // 0x7364f8: ldur            x3, [fp, #-0x60]
    // 0x7364fc: cmp             x3, x1
    // 0x736500: b.ne            #0x73664c
    // 0x736504: ldur            x4, [fp, #-0x50]
    // 0x736508: cmp             x4, x1
    // 0x73650c: b.ge            #0x7365b4
    // 0x736510: ldur            x5, [fp, #-0x28]
    // 0x736514: r0 = LoadClassIdInstr(r5)
    //     0x736514: ldur            x0, [x5, #-1]
    //     0x736518: ubfx            x0, x0, #0xc, #0x14
    // 0x73651c: mov             x1, x5
    // 0x736520: mov             x2, x4
    // 0x736524: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x736524: movz            x17, #0xd28f
    //     0x736528: add             lr, x0, x17
    //     0x73652c: ldr             lr, [x21, lr, lsl #3]
    //     0x736530: blr             lr
    // 0x736534: mov             x3, x0
    // 0x736538: ldur            x0, [fp, #-0x50]
    // 0x73653c: stur            x3, [fp, #-0x30]
    // 0x736540: add             x4, x0, #1
    // 0x736544: stur            x4, [fp, #-0x68]
    // 0x736548: cmp             w3, NULL
    // 0x73654c: b.ne            #0x736580
    // 0x736550: mov             x0, x3
    // 0x736554: ldur            x2, [fp, #-0x18]
    // 0x736558: r1 = Null
    //     0x736558: mov             x1, NULL
    // 0x73655c: cmp             w2, NULL
    // 0x736560: b.eq            #0x736580
    // 0x736564: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x736564: ldur            w4, [x2, #0x17]
    // 0x736568: DecompressPointer r4
    //     0x736568: add             x4, x4, HEAP, lsl #32
    // 0x73656c: r8 = X0
    //     0x73656c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x736570: LoadField: r9 = r4->field_7
    //     0x736570: ldur            x9, [x4, #7]
    // 0x736574: r3 = Null
    //     0x736574: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c3f0] Null
    //     0x736578: ldr             x3, [x3, #0x3f0]
    // 0x73657c: blr             x9
    // 0x736580: ldur            d0, [fp, #-0xa0]
    // 0x736584: ldur            x1, [fp, #-0x30]
    // 0x736588: LoadField: d1 = r1->field_1b
    //     0x736588: ldur            d1, [x1, #0x1b]
    // 0x73658c: LoadField: d2 = r1->field_13
    //     0x73658c: ldur            d2, [x1, #0x13]
    // 0x736590: fsub            d3, d1, d2
    // 0x736594: fsub            d2, d0, d3
    // 0x736598: stur            d2, [fp, #-0xa8]
    // 0x73659c: StoreField: r1->field_13 = d1
    //     0x73659c: stur            d1, [x1, #0x13]
    // 0x7365a0: r0 = _increaseGrowthLimitIfNecessary()
    //     0x7365a0: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x7365a4: ldur            d0, [fp, #-0xa8]
    // 0x7365a8: ldur            x3, [fp, #-0x68]
    // 0x7365ac: ldur            x1, [fp, #-0x60]
    // 0x7365b0: b               #0x7364b8
    // 0x7365b4: ldur            d0, [fp, #-0xa0]
    // 0x7365b8: ldur            x0, [fp, #-0x40]
    // 0x7365bc: LoadField: r1 = r0->field_b
    //     0x7365bc: ldur            w1, [x0, #0xb]
    // 0x7365c0: r0 = LoadInt32Instr(r1)
    //     0x7365c0: sbfx            x0, x1, #1, #0x1f
    // 0x7365c4: stur            x0, [fp, #-0x50]
    // 0x7365c8: cbz             x0, #0x7365d8
    // 0x7365cc: d1 = 0.000000
    //     0x7365cc: eor             v1.16b, v1.16b, v1.16b
    // 0x7365d0: fcmp            d1, d0
    // 0x7365d4: b.lt            #0x7365e8
    // 0x7365d8: ldur            x0, [fp, #-0x38]
    // 0x7365dc: LeaveFrame
    //     0x7365dc: mov             SP, fp
    //     0x7365e0: ldp             fp, lr, [SP], #0x10
    // 0x7365e4: ret
    //     0x7365e4: ret             
    // 0x7365e8: ldur            x1, [fp, #-0x10]
    // 0x7365ec: ldur            x2, [fp, #-0x38]
    // 0x7365f0: ldur            d0, [fp, #-0x88]
    // 0x7365f4: r0 = _findFlexFactorUnitSize()
    //     0x7365f4: bl              #0x7366dc  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_findFlexFactorUnitSize
    // 0x7365f8: ldur            x0, [fp, #-0x50]
    // 0x7365fc: cmp             x0, #0
    // 0x736600: b.gt            #0x736628
    // 0x736604: ldur            x1, [fp, #-0x20]
    // 0x736608: ldur            d0, [fp, #-0x90]
    // 0x73660c: ldur            d1, [fp, #-0x98]
    // 0x736610: ldur            x2, [fp, #-8]
    // 0x736614: r0 = setMinMaxTrackSizesForAxis()
    //     0x736614: bl              #0x737154  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::setMinMaxTrackSizesForAxis
    // 0x736618: ldur            x0, [fp, #-0x38]
    // 0x73661c: LeaveFrame
    //     0x73661c: mov             SP, fp
    //     0x736620: ldp             fp, lr, [SP], #0x10
    // 0x736624: ret
    //     0x736624: ret             
    // 0x736628: r2 = Null
    //     0x736628: mov             x2, NULL
    // 0x73662c: r1 = 0
    //     0x73662c: movz            x1, #0
    // 0x736630: cmp             x1, x0
    // 0x736634: b.hs            #0x7366b0
    // 0x736638: cmp             w2, NULL
    // 0x73663c: b.eq            #0x7366b4
    // 0x736640: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x736640: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x736644: r0 = Throw()
    //     0x736644: bl              #0xec04b8  ; ThrowStub
    // 0x736648: brk             #0
    // 0x73664c: ldur            x0, [fp, #-0x38]
    // 0x736650: r0 = ConcurrentModificationError()
    //     0x736650: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x736654: mov             x1, x0
    // 0x736658: ldur            x0, [fp, #-0x38]
    // 0x73665c: StoreField: r1->field_b = r0
    //     0x73665c: stur            w0, [x1, #0xb]
    // 0x736660: mov             x0, x1
    // 0x736664: r0 = Throw()
    //     0x736664: bl              #0xec04b8  ; ThrowStub
    // 0x736668: brk             #0
    // 0x73666c: ldur            x0, [fp, #-0x38]
    // 0x736670: r0 = ConcurrentModificationError()
    //     0x736670: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x736674: mov             x1, x0
    // 0x736678: ldur            x0, [fp, #-0x38]
    // 0x73667c: StoreField: r1->field_b = r0
    //     0x73667c: stur            w0, [x1, #0xb]
    // 0x736680: mov             x0, x1
    // 0x736684: r0 = Throw()
    //     0x736684: bl              #0xec04b8  ; ThrowStub
    // 0x736688: brk             #0
    // 0x73668c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73668c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x736690: b               #0x735e48
    // 0x736694: r0 = StackOverflowSharedWithFPURegs()
    //     0x736694: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x736698: b               #0x735fb0
    // 0x73669c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x73669c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7366a0: r0 = StackOverflowSharedWithFPURegs()
    //     0x7366a0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7366a4: b               #0x736304
    // 0x7366a8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7366a8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7366ac: b               #0x7364d0
    // 0x7366b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7366b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7366b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7366b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _findFlexFactorUnitSize(/* No info */) {
    // ** addr: 0x7366dc, size: 0x1a0
    // 0x7366dc: EnterFrame
    //     0x7366dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7366e0: mov             fp, SP
    // 0x7366e4: AllocStack(0x48)
    //     0x7366e4: sub             SP, SP, #0x48
    // 0x7366e8: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x40 */)
    //     0x7366e8: stur            x2, [fp, #-0x18]
    //     0x7366ec: stur            d0, [fp, #-0x40]
    // 0x7366f0: CheckStackOverflow
    //     0x7366f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7366f4: cmp             SP, x16
    //     0x7366f8: b.ls            #0x73686c
    // 0x7366fc: LoadField: r1 = r2->field_7
    //     0x7366fc: ldur            w1, [x2, #7]
    // 0x736700: DecompressPointer r1
    //     0x736700: add             x1, x1, HEAP, lsl #32
    // 0x736704: stur            x1, [fp, #-0x10]
    // 0x736708: LoadField: r3 = r2->field_b
    //     0x736708: ldur            w3, [x2, #0xb]
    // 0x73670c: DecompressPointer r3
    //     0x73670c: add             x3, x3, HEAP, lsl #32
    // 0x736710: stur            x3, [fp, #-8]
    // 0x736714: r0 = LoadClassIdInstr(r3)
    //     0x736714: ldur            x0, [x3, #-1]
    //     0x736718: ubfx            x0, x0, #0xc, #0x14
    // 0x73671c: str             x3, [SP]
    // 0x736720: r0 = GDT[cid_x0 + 0xc834]()
    //     0x736720: movz            x17, #0xc834
    //     0x736724: add             lr, x0, x17
    //     0x736728: ldr             lr, [x21, lr, lsl #3]
    //     0x73672c: blr             lr
    // 0x736730: r1 = LoadInt32Instr(r0)
    //     0x736730: sbfx            x1, x0, #1, #0x1f
    //     0x736734: tbz             w0, #0, #0x73673c
    //     0x736738: ldur            x1, [x0, #7]
    // 0x73673c: stur            x1, [fp, #-0x28]
    // 0x736740: ldur            d0, [fp, #-0x40]
    // 0x736744: r3 = 0
    //     0x736744: movz            x3, #0
    // 0x736748: ldur            x2, [fp, #-8]
    // 0x73674c: stur            x3, [fp, #-0x20]
    // 0x736750: stur            d0, [fp, #-0x40]
    // 0x736754: CheckStackOverflow
    //     0x736754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736758: cmp             SP, x16
    //     0x73675c: b.ls            #0x736874
    // 0x736760: r0 = LoadClassIdInstr(r2)
    //     0x736760: ldur            x0, [x2, #-1]
    //     0x736764: ubfx            x0, x0, #0xc, #0x14
    // 0x736768: str             x2, [SP]
    // 0x73676c: r0 = GDT[cid_x0 + 0xc834]()
    //     0x73676c: movz            x17, #0xc834
    //     0x736770: add             lr, x0, x17
    //     0x736774: ldr             lr, [x21, lr, lsl #3]
    //     0x736778: blr             lr
    // 0x73677c: r1 = LoadInt32Instr(r0)
    //     0x73677c: sbfx            x1, x0, #1, #0x1f
    //     0x736780: tbz             w0, #0, #0x736788
    //     0x736784: ldur            x1, [x0, #7]
    // 0x736788: ldur            x3, [fp, #-0x28]
    // 0x73678c: cmp             x3, x1
    // 0x736790: b.ne            #0x73684c
    // 0x736794: ldur            x4, [fp, #-0x20]
    // 0x736798: cmp             x4, x1
    // 0x73679c: b.ge            #0x736830
    // 0x7367a0: ldur            x5, [fp, #-8]
    // 0x7367a4: r0 = LoadClassIdInstr(r5)
    //     0x7367a4: ldur            x0, [x5, #-1]
    //     0x7367a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7367ac: mov             x1, x5
    // 0x7367b0: mov             x2, x4
    // 0x7367b4: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x7367b4: movz            x17, #0xd28f
    //     0x7367b8: add             lr, x0, x17
    //     0x7367bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7367c0: blr             lr
    // 0x7367c4: mov             x3, x0
    // 0x7367c8: ldur            x0, [fp, #-0x20]
    // 0x7367cc: stur            x3, [fp, #-0x38]
    // 0x7367d0: add             x4, x0, #1
    // 0x7367d4: stur            x4, [fp, #-0x30]
    // 0x7367d8: cmp             w3, NULL
    // 0x7367dc: b.ne            #0x736810
    // 0x7367e0: mov             x0, x3
    // 0x7367e4: ldur            x2, [fp, #-0x10]
    // 0x7367e8: r1 = Null
    //     0x7367e8: mov             x1, NULL
    // 0x7367ec: cmp             w2, NULL
    // 0x7367f0: b.eq            #0x736810
    // 0x7367f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7367f4: ldur            w4, [x2, #0x17]
    // 0x7367f8: DecompressPointer r4
    //     0x7367f8: add             x4, x4, HEAP, lsl #32
    // 0x7367fc: r8 = X0
    //     0x7367fc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x736800: LoadField: r9 = r4->field_7
    //     0x736800: ldur            x9, [x4, #7]
    // 0x736804: r3 = Null
    //     0x736804: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c418] Null
    //     0x736808: ldr             x3, [x3, #0x418]
    // 0x73680c: blr             x9
    // 0x736810: ldur            d0, [fp, #-0x40]
    // 0x736814: ldur            x0, [fp, #-0x38]
    // 0x736818: LoadField: d1 = r0->field_13
    //     0x736818: ldur            d1, [x0, #0x13]
    // 0x73681c: fsub            d2, d0, d1
    // 0x736820: mov             v0.16b, v2.16b
    // 0x736824: ldur            x3, [fp, #-0x30]
    // 0x736828: ldur            x1, [fp, #-0x28]
    // 0x73682c: b               #0x736748
    // 0x736830: ldur            d0, [fp, #-0x40]
    // 0x736834: d1 = 0.000000
    //     0x736834: eor             v1.16b, v1.16b, v1.16b
    // 0x736838: fdiv            d2, d0, d1
    // 0x73683c: mov             v0.16b, v2.16b
    // 0x736840: LeaveFrame
    //     0x736840: mov             SP, fp
    //     0x736844: ldp             fp, lr, [SP], #0x10
    // 0x736848: ret
    //     0x736848: ret             
    // 0x73684c: ldur            x0, [fp, #-0x18]
    // 0x736850: r0 = ConcurrentModificationError()
    //     0x736850: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x736854: mov             x1, x0
    // 0x736858: ldur            x0, [fp, #-0x18]
    // 0x73685c: StoreField: r1->field_b = r0
    //     0x73685c: stur            w0, [x1, #0xb]
    // 0x736860: mov             x0, x1
    // 0x736864: r0 = Throw()
    //     0x736864: bl              #0xec04b8  ; ThrowStub
    // 0x736868: brk             #0
    // 0x73686c: r0 = StackOverflowSharedWithFPURegs()
    //     0x73686c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x736870: b               #0x7366fc
    // 0x736874: r0 = StackOverflowSharedWithFPURegs()
    //     0x736874: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x736878: b               #0x736760
  }
  _ _distributeFreeSpace(/* No info */) {
    // ** addr: 0x73687c, size: 0x470
    // 0x73687c: EnterFrame
    //     0x73687c: stp             fp, lr, [SP, #-0x10]!
    //     0x736880: mov             fp, SP
    // 0x736884: AllocStack(0x68)
    //     0x736884: sub             SP, SP, #0x68
    // 0x736888: SetupParameters(RenderLayoutGrid this /* r1 => r2 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x736888: mov             x0, x2
    //     0x73688c: stur            x2, [fp, #-0x10]
    //     0x736890: mov             x2, x1
    //     0x736894: mov             x1, x3
    //     0x736898: stur            x3, [fp, #-0x18]
    //     0x73689c: stur            x5, [fp, #-0x20]
    // 0x7368a0: CheckStackOverflow
    //     0x7368a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7368a4: cmp             SP, x16
    //     0x7368a8: b.ls            #0x736cb0
    // 0x7368ac: r2 = inline_Allocate_Double()
    //     0x7368ac: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x7368b0: add             x2, x2, #0x10
    //     0x7368b4: cmp             x3, x2
    //     0x7368b8: b.ls            #0x736cb8
    //     0x7368bc: str             x2, [THR, #0x50]  ; THR::top
    //     0x7368c0: sub             x2, x2, #0xf
    //     0x7368c4: movz            x3, #0xe15c
    //     0x7368c8: movk            x3, #0x3, lsl #16
    //     0x7368cc: stur            x3, [x2, #-1]
    // 0x7368d0: StoreField: r2->field_7 = d0
    //     0x7368d0: stur            d0, [x2, #7]
    // 0x7368d4: stur            x2, [fp, #-8]
    // 0x7368d8: r1 = 1
    //     0x7368d8: movz            x1, #0x1
    // 0x7368dc: r0 = AllocateContext()
    //     0x7368dc: bl              #0xec126c  ; AllocateContextStub
    // 0x7368e0: mov             x2, x0
    // 0x7368e4: ldur            x0, [fp, #-8]
    // 0x7368e8: stur            x2, [fp, #-0x28]
    // 0x7368ec: StoreField: r2->field_f = r0
    //     0x7368ec: stur            w0, [x2, #0xf]
    // 0x7368f0: ldur            x1, [fp, #-0x10]
    // 0x7368f4: r0 = LoadClassIdInstr(r1)
    //     0x7368f4: ldur            x0, [x1, #-1]
    //     0x7368f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7368fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7368fc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x736900: r0 = GDT[cid_x0 + 0xd889]()
    //     0x736900: movz            x17, #0xd889
    //     0x736904: add             lr, x0, x17
    //     0x736908: ldr             lr, [x21, lr, lsl #3]
    //     0x73690c: blr             lr
    // 0x736910: ldur            x2, [fp, #-0x28]
    // 0x736914: r1 = Function 'distribute':.
    //     0x736914: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c428] AnonymousClosure: (0x736f3c), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeFreeSpace (0x73687c)
    //     0x736918: ldr             x1, [x1, #0x428]
    // 0x73691c: stur            x0, [fp, #-8]
    // 0x736920: r0 = AllocateClosure()
    //     0x736920: bl              #0xec1630  ; AllocateClosureStub
    // 0x736924: mov             x4, x0
    // 0x736928: ldur            x3, [fp, #-8]
    // 0x73692c: stur            x4, [fp, #-0x50]
    // 0x736930: LoadField: r5 = r3->field_7
    //     0x736930: ldur            w5, [x3, #7]
    // 0x736934: DecompressPointer r5
    //     0x736934: add             x5, x5, HEAP, lsl #32
    // 0x736938: stur            x5, [fp, #-0x48]
    // 0x73693c: LoadField: r0 = r3->field_b
    //     0x73693c: ldur            w0, [x3, #0xb]
    // 0x736940: r6 = LoadInt32Instr(r0)
    //     0x736940: sbfx            x6, x0, #1, #0x1f
    // 0x736944: stur            x6, [fp, #-0x40]
    // 0x736948: LoadField: r7 = r3->field_f
    //     0x736948: ldur            w7, [x3, #0xf]
    // 0x73694c: DecompressPointer r7
    //     0x73694c: add             x7, x7, HEAP, lsl #32
    // 0x736950: stur            x7, [fp, #-0x38]
    // 0x736954: ldur            x8, [fp, #-0x20]
    // 0x736958: r0 = 0
    //     0x736958: movz            x0, #0
    // 0x73695c: CheckStackOverflow
    //     0x73695c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736960: cmp             SP, x16
    //     0x736964: b.ls            #0x736cdc
    // 0x736968: cmp             x0, x6
    // 0x73696c: b.ge            #0x736a24
    // 0x736970: ArrayLoad: r9 = r7[r0]  ; Unknown_4
    //     0x736970: add             x16, x7, x0, lsl #2
    //     0x736974: ldur            w9, [x16, #0xf]
    // 0x736978: DecompressPointer r9
    //     0x736978: add             x9, x9, HEAP, lsl #32
    // 0x73697c: stur            x9, [fp, #-0x10]
    // 0x736980: add             x10, x0, #1
    // 0x736984: stur            x10, [fp, #-0x30]
    // 0x736988: cmp             w9, NULL
    // 0x73698c: b.ne            #0x7369c0
    // 0x736990: mov             x0, x9
    // 0x736994: mov             x2, x5
    // 0x736998: r1 = Null
    //     0x736998: mov             x1, NULL
    // 0x73699c: cmp             w2, NULL
    // 0x7369a0: b.eq            #0x7369c0
    // 0x7369a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7369a4: ldur            w4, [x2, #0x17]
    // 0x7369a8: DecompressPointer r4
    //     0x7369a8: add             x4, x4, HEAP, lsl #32
    // 0x7369ac: r8 = X0
    //     0x7369ac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7369b0: LoadField: r9 = r4->field_7
    //     0x7369b0: ldur            x9, [x4, #7]
    // 0x7369b4: r3 = Null
    //     0x7369b4: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c430] Null
    //     0x7369b8: ldr             x3, [x3, #0x430]
    // 0x7369bc: blr             x9
    // 0x7369c0: ldur            x2, [fp, #-0x20]
    // 0x7369c4: r16 = Instance__IntrinsicDimension
    //     0x7369c4: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c3e8] Obj!_IntrinsicDimension@e32dc1
    //     0x7369c8: ldr             x16, [x16, #0x3e8]
    // 0x7369cc: cmp             w2, w16
    // 0x7369d0: b.ne            #0x7369e8
    // 0x7369d4: ldur            x0, [fp, #-0x10]
    // 0x7369d8: LoadField: d0 = r0->field_13
    //     0x7369d8: ldur            d0, [x0, #0x13]
    // 0x7369dc: mov             v1.16b, v0.16b
    // 0x7369e0: d0 = inf
    //     0x7369e0: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x7369e4: b               #0x736a00
    // 0x7369e8: ldur            x0, [fp, #-0x10]
    // 0x7369ec: d0 = inf
    //     0x7369ec: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x7369f0: LoadField: d1 = r0->field_1b
    //     0x7369f0: ldur            d1, [x0, #0x1b]
    // 0x7369f4: fcmp            d1, d0
    // 0x7369f8: b.ne            #0x736a00
    // 0x7369fc: LoadField: d1 = r0->field_13
    //     0x7369fc: ldur            d1, [x0, #0x13]
    // 0x736a00: StoreField: r0->field_23 = d1
    //     0x736a00: stur            d1, [x0, #0x23]
    // 0x736a04: ldur            x0, [fp, #-0x30]
    // 0x736a08: mov             x8, x2
    // 0x736a0c: ldur            x3, [fp, #-8]
    // 0x736a10: ldur            x4, [fp, #-0x50]
    // 0x736a14: ldur            x5, [fp, #-0x48]
    // 0x736a18: ldur            x7, [fp, #-0x38]
    // 0x736a1c: ldur            x6, [fp, #-0x40]
    // 0x736a20: b               #0x73695c
    // 0x736a24: mov             x2, x8
    // 0x736a28: ldur            x0, [fp, #-0x28]
    // 0x736a2c: d0 = inf
    //     0x736a2c: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x736a30: r16 = Closure: (GridTrack, GridTrack) => int from Function '_sortByGrowthPotential@1176515497': static.
    //     0x736a30: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c440] Closure: (GridTrack, GridTrack) => int from Function '_sortByGrowthPotential@1176515497': static. (0x7e54fb136de0)
    //     0x736a34: ldr             x16, [x16, #0x440]
    // 0x736a38: str             x16, [SP]
    // 0x736a3c: ldur            x1, [fp, #-8]
    // 0x736a40: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x736a40: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x736a44: r0 = sort()
    //     0x736a44: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x736a48: r1 = Function '<anonymous closure>':.
    //     0x736a48: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c448] AnonymousClosure: (0x736cec), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeFreeSpace (0x73687c)
    //     0x736a4c: ldr             x1, [x1, #0x448]
    // 0x736a50: r2 = Null
    //     0x736a50: mov             x2, NULL
    // 0x736a54: r0 = AllocateClosure()
    //     0x736a54: bl              #0xec1630  ; AllocateClosureStub
    // 0x736a58: ldur            x16, [fp, #-0x50]
    // 0x736a5c: ldur            lr, [fp, #-8]
    // 0x736a60: stp             lr, x16, [SP, #8]
    // 0x736a64: str             x0, [SP]
    // 0x736a68: ldur            x0, [fp, #-0x50]
    // 0x736a6c: ClosureCall
    //     0x736a6c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x736a70: ldur            x2, [x0, #0x1f]
    //     0x736a74: blr             x2
    // 0x736a78: ldur            x2, [fp, #-0x28]
    // 0x736a7c: LoadField: r0 = r2->field_f
    //     0x736a7c: ldur            w0, [x2, #0xf]
    // 0x736a80: DecompressPointer r0
    //     0x736a80: add             x0, x0, HEAP, lsl #32
    // 0x736a84: LoadField: d0 = r0->field_7
    //     0x736a84: ldur            d0, [x0, #7]
    // 0x736a88: d1 = 0.000000
    //     0x736a88: eor             v1.16b, v1.16b, v1.16b
    // 0x736a8c: fcmp            d0, d1
    // 0x736a90: b.le            #0x736ae8
    // 0x736a94: ldur            x3, [fp, #-0x18]
    // 0x736a98: r0 = LoadClassIdInstr(r3)
    //     0x736a98: ldur            x0, [x3, #-1]
    //     0x736a9c: ubfx            x0, x0, #0xc, #0x14
    // 0x736aa0: mov             x1, x3
    // 0x736aa4: r0 = GDT[cid_x0 + 0xd488]()
    //     0x736aa4: movz            x17, #0xd488
    //     0x736aa8: add             lr, x0, x17
    //     0x736aac: ldr             lr, [x21, lr, lsl #3]
    //     0x736ab0: blr             lr
    // 0x736ab4: tbnz            w0, #4, #0x736ae8
    // 0x736ab8: r1 = Function '<anonymous closure>':.
    //     0x736ab8: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c450] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x736abc: ldr             x1, [x1, #0x450]
    // 0x736ac0: r2 = Null
    //     0x736ac0: mov             x2, NULL
    // 0x736ac4: r0 = AllocateClosure()
    //     0x736ac4: bl              #0xec1630  ; AllocateClosureStub
    // 0x736ac8: ldur            x16, [fp, #-0x50]
    // 0x736acc: ldur            lr, [fp, #-0x18]
    // 0x736ad0: stp             lr, x16, [SP, #8]
    // 0x736ad4: str             x0, [SP]
    // 0x736ad8: ldur            x0, [fp, #-0x50]
    // 0x736adc: ClosureCall
    //     0x736adc: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x736ae0: ldur            x2, [x0, #0x1f]
    //     0x736ae4: blr             x2
    // 0x736ae8: ldur            x3, [fp, #-8]
    // 0x736aec: LoadField: r0 = r3->field_b
    //     0x736aec: ldur            w0, [x3, #0xb]
    // 0x736af0: r4 = LoadInt32Instr(r0)
    //     0x736af0: sbfx            x4, x0, #1, #0x1f
    // 0x736af4: stur            x4, [fp, #-0x40]
    // 0x736af8: r0 = 0
    //     0x736af8: movz            x0, #0
    // 0x736afc: ldur            x5, [fp, #-0x20]
    // 0x736b00: CheckStackOverflow
    //     0x736b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736b04: cmp             SP, x16
    //     0x736b08: b.ls            #0x736ce4
    // 0x736b0c: LoadField: r1 = r3->field_b
    //     0x736b0c: ldur            w1, [x3, #0xb]
    // 0x736b10: r2 = LoadInt32Instr(r1)
    //     0x736b10: sbfx            x2, x1, #1, #0x1f
    // 0x736b14: cmp             x4, x2
    // 0x736b18: b.ne            #0x736c90
    // 0x736b1c: cmp             x0, x2
    // 0x736b20: b.ge            #0x736c74
    // 0x736b24: LoadField: r1 = r3->field_f
    //     0x736b24: ldur            w1, [x3, #0xf]
    // 0x736b28: DecompressPointer r1
    //     0x736b28: add             x1, x1, HEAP, lsl #32
    // 0x736b2c: ArrayLoad: r6 = r1[r0]  ; Unknown_4
    //     0x736b2c: add             x16, x1, x0, lsl #2
    //     0x736b30: ldur            w6, [x16, #0xf]
    // 0x736b34: DecompressPointer r6
    //     0x736b34: add             x6, x6, HEAP, lsl #32
    // 0x736b38: stur            x6, [fp, #-0x10]
    // 0x736b3c: add             x7, x0, #1
    // 0x736b40: stur            x7, [fp, #-0x30]
    // 0x736b44: cmp             w6, NULL
    // 0x736b48: b.ne            #0x736b7c
    // 0x736b4c: mov             x0, x6
    // 0x736b50: ldur            x2, [fp, #-0x48]
    // 0x736b54: r1 = Null
    //     0x736b54: mov             x1, NULL
    // 0x736b58: cmp             w2, NULL
    // 0x736b5c: b.eq            #0x736b7c
    // 0x736b60: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x736b60: ldur            w4, [x2, #0x17]
    // 0x736b64: DecompressPointer r4
    //     0x736b64: add             x4, x4, HEAP, lsl #32
    // 0x736b68: r8 = X0
    //     0x736b68: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x736b6c: LoadField: r9 = r4->field_7
    //     0x736b6c: ldur            x9, [x4, #7]
    // 0x736b70: r3 = Null
    //     0x736b70: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c458] Null
    //     0x736b74: ldr             x3, [x3, #0x458]
    // 0x736b78: blr             x9
    // 0x736b7c: ldur            x0, [fp, #-0x20]
    // 0x736b80: r16 = Instance__IntrinsicDimension
    //     0x736b80: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c3e8] Obj!_IntrinsicDimension@e32dc1
    //     0x736b84: ldr             x16, [x16, #0x3e8]
    // 0x736b88: cmp             w0, w16
    // 0x736b8c: b.ne            #0x736bf0
    // 0x736b90: ldur            x1, [fp, #-0x10]
    // 0x736b94: LoadField: d0 = r1->field_13
    //     0x736b94: ldur            d0, [x1, #0x13]
    // 0x736b98: LoadField: d1 = r1->field_23
    //     0x736b98: ldur            d1, [x1, #0x23]
    // 0x736b9c: fcmp            d0, d1
    // 0x736ba0: b.le            #0x736bac
    // 0x736ba4: d2 = 0.000000
    //     0x736ba4: eor             v2.16b, v2.16b, v2.16b
    // 0x736ba8: b               #0x736be4
    // 0x736bac: fcmp            d1, d0
    // 0x736bb0: b.le            #0x736bc0
    // 0x736bb4: mov             v0.16b, v1.16b
    // 0x736bb8: d2 = 0.000000
    //     0x736bb8: eor             v2.16b, v2.16b, v2.16b
    // 0x736bbc: b               #0x736be4
    // 0x736bc0: d2 = 0.000000
    //     0x736bc0: eor             v2.16b, v2.16b, v2.16b
    // 0x736bc4: fcmp            d0, d2
    // 0x736bc8: b.ne            #0x736bd8
    // 0x736bcc: fadd            d3, d0, d1
    // 0x736bd0: mov             v0.16b, v3.16b
    // 0x736bd4: b               #0x736be4
    // 0x736bd8: fcmp            d1, d1
    // 0x736bdc: b.vc            #0x736be4
    // 0x736be0: mov             v0.16b, v1.16b
    // 0x736be4: StoreField: r1->field_13 = d0
    //     0x736be4: stur            d0, [x1, #0x13]
    // 0x736be8: r0 = _increaseGrowthLimitIfNecessary()
    //     0x736be8: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x736bec: b               #0x736c64
    // 0x736bf0: ldur            x1, [fp, #-0x10]
    // 0x736bf4: d0 = inf
    //     0x736bf4: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x736bf8: LoadField: d1 = r1->field_1b
    //     0x736bf8: ldur            d1, [x1, #0x1b]
    // 0x736bfc: fcmp            d1, d0
    // 0x736c00: b.ne            #0x736c10
    // 0x736c04: LoadField: d1 = r1->field_23
    //     0x736c04: ldur            d1, [x1, #0x23]
    // 0x736c08: d3 = 0.000000
    //     0x736c08: eor             v3.16b, v3.16b, v3.16b
    // 0x736c0c: b               #0x736c5c
    // 0x736c10: LoadField: d2 = r1->field_23
    //     0x736c10: ldur            d2, [x1, #0x23]
    // 0x736c14: fcmp            d1, d2
    // 0x736c18: b.le            #0x736c24
    // 0x736c1c: d3 = 0.000000
    //     0x736c1c: eor             v3.16b, v3.16b, v3.16b
    // 0x736c20: b               #0x736c5c
    // 0x736c24: fcmp            d2, d1
    // 0x736c28: b.le            #0x736c38
    // 0x736c2c: mov             v1.16b, v2.16b
    // 0x736c30: d3 = 0.000000
    //     0x736c30: eor             v3.16b, v3.16b, v3.16b
    // 0x736c34: b               #0x736c5c
    // 0x736c38: d3 = 0.000000
    //     0x736c38: eor             v3.16b, v3.16b, v3.16b
    // 0x736c3c: fcmp            d1, d3
    // 0x736c40: b.ne            #0x736c50
    // 0x736c44: fadd            d4, d1, d2
    // 0x736c48: mov             v1.16b, v4.16b
    // 0x736c4c: b               #0x736c5c
    // 0x736c50: fcmp            d2, d2
    // 0x736c54: b.vc            #0x736c5c
    // 0x736c58: mov             v1.16b, v2.16b
    // 0x736c5c: StoreField: r1->field_1b = d1
    //     0x736c5c: stur            d1, [x1, #0x1b]
    // 0x736c60: r0 = _increaseGrowthLimitIfNecessary()
    //     0x736c60: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x736c64: ldur            x0, [fp, #-0x30]
    // 0x736c68: ldur            x3, [fp, #-8]
    // 0x736c6c: ldur            x4, [fp, #-0x40]
    // 0x736c70: b               #0x736afc
    // 0x736c74: ldur            x0, [fp, #-0x28]
    // 0x736c78: LoadField: r1 = r0->field_f
    //     0x736c78: ldur            w1, [x0, #0xf]
    // 0x736c7c: DecompressPointer r1
    //     0x736c7c: add             x1, x1, HEAP, lsl #32
    // 0x736c80: LoadField: d0 = r1->field_7
    //     0x736c80: ldur            d0, [x1, #7]
    // 0x736c84: LeaveFrame
    //     0x736c84: mov             SP, fp
    //     0x736c88: ldp             fp, lr, [SP], #0x10
    // 0x736c8c: ret
    //     0x736c8c: ret             
    // 0x736c90: mov             x0, x3
    // 0x736c94: r0 = ConcurrentModificationError()
    //     0x736c94: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x736c98: mov             x1, x0
    // 0x736c9c: ldur            x0, [fp, #-8]
    // 0x736ca0: StoreField: r1->field_b = r0
    //     0x736ca0: stur            w0, [x1, #0xb]
    // 0x736ca4: mov             x0, x1
    // 0x736ca8: r0 = Throw()
    //     0x736ca8: bl              #0xec04b8  ; ThrowStub
    // 0x736cac: brk             #0
    // 0x736cb0: r0 = StackOverflowSharedWithFPURegs()
    //     0x736cb0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x736cb4: b               #0x7368ac
    // 0x736cb8: SaveReg d0
    //     0x736cb8: str             q0, [SP, #-0x10]!
    // 0x736cbc: stp             x1, x5, [SP, #-0x10]!
    // 0x736cc0: SaveReg r0
    //     0x736cc0: str             x0, [SP, #-8]!
    // 0x736cc4: r0 = AllocateDouble()
    //     0x736cc4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x736cc8: mov             x2, x0
    // 0x736ccc: RestoreReg r0
    //     0x736ccc: ldr             x0, [SP], #8
    // 0x736cd0: ldp             x1, x5, [SP], #0x10
    // 0x736cd4: RestoreReg d0
    //     0x736cd4: ldr             q0, [SP], #0x10
    // 0x736cd8: b               #0x7368d0
    // 0x736cdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x736cdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x736ce0: b               #0x736968
    // 0x736ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x736ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x736ce8: b               #0x736b0c
  }
  [closure] double <anonymous closure>(dynamic, GridTrack, double) {
    // ** addr: 0x736cec, size: 0xf4
    // 0x736cec: EnterFrame
    //     0x736cec: stp             fp, lr, [SP, #-0x10]!
    //     0x736cf0: mov             fp, SP
    // 0x736cf4: d0 = inf
    //     0x736cf4: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x736cf8: ldr             x1, [fp, #0x18]
    // 0x736cfc: LoadField: d1 = r1->field_1b
    //     0x736cfc: ldur            d1, [x1, #0x1b]
    // 0x736d00: fcmp            d1, d0
    // 0x736d04: b.ne            #0x736d14
    // 0x736d08: ldr             x2, [fp, #0x10]
    // 0x736d0c: LoadField: d0 = r2->field_7
    //     0x736d0c: ldur            d0, [x2, #7]
    // 0x736d10: b               #0x736d9c
    // 0x736d14: ldr             x2, [fp, #0x10]
    // 0x736d18: LoadField: d0 = r1->field_23
    //     0x736d18: ldur            d0, [x1, #0x23]
    // 0x736d1c: fsub            d2, d1, d0
    // 0x736d20: LoadField: d0 = r2->field_7
    //     0x736d20: ldur            d0, [x2, #7]
    // 0x736d24: fcmp            d0, d2
    // 0x736d28: b.le            #0x736d34
    // 0x736d2c: mov             v0.16b, v2.16b
    // 0x736d30: b               #0x736d9c
    // 0x736d34: fcmp            d2, d0
    // 0x736d38: b.le            #0x736d48
    // 0x736d3c: LoadField: d1 = r2->field_7
    //     0x736d3c: ldur            d1, [x2, #7]
    // 0x736d40: mov             v0.16b, v1.16b
    // 0x736d44: b               #0x736d9c
    // 0x736d48: d1 = 0.000000
    //     0x736d48: eor             v1.16b, v1.16b, v1.16b
    // 0x736d4c: fcmp            d0, d1
    // 0x736d50: b.ne            #0x736d68
    // 0x736d54: fadd            d3, d0, d2
    // 0x736d58: fmul            d4, d3, d0
    // 0x736d5c: fmul            d3, d4, d2
    // 0x736d60: mov             v0.16b, v3.16b
    // 0x736d64: b               #0x736d9c
    // 0x736d68: fcmp            d0, d1
    // 0x736d6c: b.ne            #0x736d88
    // 0x736d70: fcmp            d2, #0.0
    // 0x736d74: b.vs            #0x736d88
    // 0x736d78: b.ne            #0x736d84
    // 0x736d7c: r1 = 0.000000
    //     0x736d7c: fmov            x1, d2
    // 0x736d80: cmp             x1, #0
    // 0x736d84: b.lt            #0x736d90
    // 0x736d88: fcmp            d2, d2
    // 0x736d8c: b.vc            #0x736d98
    // 0x736d90: mov             v0.16b, v2.16b
    // 0x736d94: b               #0x736d9c
    // 0x736d98: LoadField: d0 = r2->field_7
    //     0x736d98: ldur            d0, [x2, #7]
    // 0x736d9c: r0 = inline_Allocate_Double()
    //     0x736d9c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x736da0: add             x0, x0, #0x10
    //     0x736da4: cmp             x1, x0
    //     0x736da8: b.ls            #0x736dd0
    //     0x736dac: str             x0, [THR, #0x50]  ; THR::top
    //     0x736db0: sub             x0, x0, #0xf
    //     0x736db4: movz            x1, #0xe15c
    //     0x736db8: movk            x1, #0x3, lsl #16
    //     0x736dbc: stur            x1, [x0, #-1]
    // 0x736dc0: StoreField: r0->field_7 = d0
    //     0x736dc0: stur            d0, [x0, #7]
    // 0x736dc4: LeaveFrame
    //     0x736dc4: mov             SP, fp
    //     0x736dc8: ldp             fp, lr, [SP], #0x10
    // 0x736dcc: ret
    //     0x736dcc: ret             
    // 0x736dd0: SaveReg d0
    //     0x736dd0: str             q0, [SP, #-0x10]!
    // 0x736dd4: r0 = AllocateDouble()
    //     0x736dd4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x736dd8: RestoreReg d0
    //     0x736dd8: ldr             q0, [SP], #0x10
    // 0x736ddc: b               #0x736dc0
  }
  [closure] void distribute(dynamic, List<GridTrack>, (dynamic, GridTrack, double) => double) {
    // ** addr: 0x736f3c, size: 0x218
    // 0x736f3c: EnterFrame
    //     0x736f3c: stp             fp, lr, [SP, #-0x10]!
    //     0x736f40: mov             fp, SP
    // 0x736f44: AllocStack(0x40)
    //     0x736f44: sub             SP, SP, #0x40
    // 0x736f48: SetupParameters()
    //     0x736f48: ldr             x0, [fp, #0x20]
    //     0x736f4c: ldur            w1, [x0, #0x17]
    //     0x736f50: add             x1, x1, HEAP, lsl #32
    //     0x736f54: stur            x1, [fp, #-8]
    // 0x736f58: CheckStackOverflow
    //     0x736f58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736f5c: cmp             SP, x16
    //     0x736f60: b.ls            #0x737108
    // 0x736f64: ldr             x2, [fp, #0x18]
    // 0x736f68: r0 = LoadClassIdInstr(r2)
    //     0x736f68: ldur            x0, [x2, #-1]
    //     0x736f6c: ubfx            x0, x0, #0xc, #0x14
    // 0x736f70: str             x2, [SP]
    // 0x736f74: r0 = GDT[cid_x0 + 0xc834]()
    //     0x736f74: movz            x17, #0xc834
    //     0x736f78: add             lr, x0, x17
    //     0x736f7c: ldr             lr, [x21, lr, lsl #3]
    //     0x736f80: blr             lr
    // 0x736f84: r2 = LoadInt32Instr(r0)
    //     0x736f84: sbfx            x2, x0, #1, #0x1f
    //     0x736f88: tbz             w0, #0, #0x736f90
    //     0x736f8c: ldur            x2, [x0, #7]
    // 0x736f90: stur            x2, [fp, #-0x18]
    // 0x736f94: ldur            x3, [fp, #-8]
    // 0x736f98: r5 = 0
    //     0x736f98: movz            x5, #0
    // 0x736f9c: ldr             x4, [fp, #0x18]
    // 0x736fa0: stur            x5, [fp, #-0x10]
    // 0x736fa4: CheckStackOverflow
    //     0x736fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x736fa8: cmp             SP, x16
    //     0x736fac: b.ls            #0x737110
    // 0x736fb0: cmp             x5, x2
    // 0x736fb4: b.ge            #0x7370f8
    // 0x736fb8: r0 = BoxInt64Instr(r5)
    //     0x736fb8: sbfiz           x0, x5, #1, #0x1f
    //     0x736fbc: cmp             x5, x0, asr #1
    //     0x736fc0: b.eq            #0x736fcc
    //     0x736fc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x736fc8: stur            x5, [x0, #7]
    // 0x736fcc: r1 = LoadClassIdInstr(r4)
    //     0x736fcc: ldur            x1, [x4, #-1]
    //     0x736fd0: ubfx            x1, x1, #0xc, #0x14
    // 0x736fd4: stp             x0, x4, [SP]
    // 0x736fd8: mov             x0, x1
    // 0x736fdc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x736fdc: movz            x17, #0x3037
    //     0x736fe0: movk            x17, #0x1, lsl #16
    //     0x736fe4: add             lr, x0, x17
    //     0x736fe8: ldr             lr, [x21, lr, lsl #3]
    //     0x736fec: blr             lr
    // 0x736ff0: mov             x2, x0
    // 0x736ff4: ldur            x1, [fp, #-8]
    // 0x736ff8: stur            x2, [fp, #-0x20]
    // 0x736ffc: LoadField: r0 = r1->field_f
    //     0x736ffc: ldur            w0, [x1, #0xf]
    // 0x737000: DecompressPointer r0
    //     0x737000: add             x0, x0, HEAP, lsl #32
    // 0x737004: ldur            x4, [fp, #-0x10]
    // 0x737008: ldur            x3, [fp, #-0x18]
    // 0x73700c: sub             x5, x3, x4
    // 0x737010: scvtf           d0, x5
    // 0x737014: LoadField: d1 = r0->field_7
    //     0x737014: ldur            d1, [x0, #7]
    // 0x737018: fdiv            d2, d1, d0
    // 0x73701c: r0 = inline_Allocate_Double()
    //     0x73701c: ldp             x0, x5, [THR, #0x50]  ; THR::top
    //     0x737020: add             x0, x0, #0x10
    //     0x737024: cmp             x5, x0
    //     0x737028: b.ls            #0x737118
    //     0x73702c: str             x0, [THR, #0x50]  ; THR::top
    //     0x737030: sub             x0, x0, #0xf
    //     0x737034: movz            x5, #0xe15c
    //     0x737038: movk            x5, #0x3, lsl #16
    //     0x73703c: stur            x5, [x0, #-1]
    // 0x737040: StoreField: r0->field_7 = d2
    //     0x737040: stur            d2, [x0, #7]
    // 0x737044: ldr             x16, [fp, #0x10]
    // 0x737048: stp             x2, x16, [SP, #8]
    // 0x73704c: str             x0, [SP]
    // 0x737050: ldr             x0, [fp, #0x10]
    // 0x737054: ClosureCall
    //     0x737054: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x737058: ldur            x2, [x0, #0x1f]
    //     0x73705c: blr             x2
    // 0x737060: mov             x1, x0
    // 0x737064: ldur            x0, [fp, #-0x20]
    // 0x737068: stur            x1, [fp, #-0x28]
    // 0x73706c: LoadField: d0 = r0->field_23
    //     0x73706c: ldur            d0, [x0, #0x23]
    // 0x737070: r2 = inline_Allocate_Double()
    //     0x737070: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x737074: add             x2, x2, #0x10
    //     0x737078: cmp             x3, x2
    //     0x73707c: b.ls            #0x737138
    //     0x737080: str             x2, [THR, #0x50]  ; THR::top
    //     0x737084: sub             x2, x2, #0xf
    //     0x737088: movz            x3, #0xe15c
    //     0x73708c: movk            x3, #0x3, lsl #16
    //     0x737090: stur            x3, [x2, #-1]
    // 0x737094: StoreField: r2->field_7 = d0
    //     0x737094: stur            d0, [x2, #7]
    // 0x737098: stp             x1, x2, [SP]
    // 0x73709c: r0 = +()
    //     0x73709c: bl              #0xebf900  ; [dart:core] _Double::+
    // 0x7370a0: LoadField: d0 = r0->field_7
    //     0x7370a0: ldur            d0, [x0, #7]
    // 0x7370a4: ldur            x0, [fp, #-0x20]
    // 0x7370a8: StoreField: r0->field_23 = d0
    //     0x7370a8: stur            d0, [x0, #0x23]
    // 0x7370ac: ldur            x0, [fp, #-8]
    // 0x7370b0: LoadField: r1 = r0->field_f
    //     0x7370b0: ldur            w1, [x0, #0xf]
    // 0x7370b4: DecompressPointer r1
    //     0x7370b4: add             x1, x1, HEAP, lsl #32
    // 0x7370b8: ldur            x16, [fp, #-0x28]
    // 0x7370bc: stp             x16, x1, [SP]
    // 0x7370c0: r0 = -()
    //     0x7370c0: bl              #0xebf790  ; [dart:core] _Double::-
    // 0x7370c4: ldur            x1, [fp, #-8]
    // 0x7370c8: StoreField: r1->field_f = r0
    //     0x7370c8: stur            w0, [x1, #0xf]
    //     0x7370cc: ldurb           w16, [x1, #-1]
    //     0x7370d0: ldurb           w17, [x0, #-1]
    //     0x7370d4: and             x16, x17, x16, lsr #2
    //     0x7370d8: tst             x16, HEAP, lsr #32
    //     0x7370dc: b.eq            #0x7370e4
    //     0x7370e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7370e4: ldur            x2, [fp, #-0x10]
    // 0x7370e8: add             x5, x2, #1
    // 0x7370ec: mov             x3, x1
    // 0x7370f0: ldur            x2, [fp, #-0x18]
    // 0x7370f4: b               #0x736f9c
    // 0x7370f8: r0 = Null
    //     0x7370f8: mov             x0, NULL
    // 0x7370fc: LeaveFrame
    //     0x7370fc: mov             SP, fp
    //     0x737100: ldp             fp, lr, [SP], #0x10
    // 0x737104: ret
    //     0x737104: ret             
    // 0x737108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x737108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73710c: b               #0x736f64
    // 0x737110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x737110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x737114: b               #0x736fb0
    // 0x737118: SaveReg d2
    //     0x737118: str             q2, [SP, #-0x10]!
    // 0x73711c: stp             x3, x4, [SP, #-0x10]!
    // 0x737120: stp             x1, x2, [SP, #-0x10]!
    // 0x737124: r0 = AllocateDouble()
    //     0x737124: bl              #0xec2254  ; AllocateDoubleStub
    // 0x737128: ldp             x1, x2, [SP], #0x10
    // 0x73712c: ldp             x3, x4, [SP], #0x10
    // 0x737130: RestoreReg d2
    //     0x737130: ldr             q2, [SP], #0x10
    // 0x737134: b               #0x737040
    // 0x737138: SaveReg d0
    //     0x737138: str             q0, [SP, #-0x10]!
    // 0x73713c: stp             x0, x1, [SP, #-0x10]!
    // 0x737140: r0 = AllocateDouble()
    //     0x737140: bl              #0xec2254  ; AllocateDoubleStub
    // 0x737144: mov             x2, x0
    // 0x737148: ldp             x0, x1, [SP], #0x10
    // 0x73714c: RestoreReg d0
    //     0x73714c: ldr             q0, [SP], #0x10
    // 0x737150: b               #0x737094
  }
  _ _resolveIntrinsicTrackSizes(/* No info */) {
    // ** addr: 0x73717c, size: 0x870
    // 0x73717c: EnterFrame
    //     0x73717c: stp             fp, lr, [SP, #-0x10]!
    //     0x737180: mov             fp, SP
    // 0x737184: AllocStack(0xd0)
    //     0x737184: sub             SP, SP, #0xd0
    // 0x737188: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0x737188: mov             x0, x2
    //     0x73718c: stur            x2, [fp, #-0x10]
    //     0x737190: mov             x2, x5
    //     0x737194: stur            x1, [fp, #-8]
    //     0x737198: stur            x3, [fp, #-0x18]
    //     0x73719c: stur            x5, [fp, #-0x20]
    //     0x7371a0: stur            x6, [fp, #-0x28]
    //     0x7371a4: stur            x7, [fp, #-0x30]
    // 0x7371a8: CheckStackOverflow
    //     0x7371a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7371ac: cmp             SP, x16
    //     0x7371b0: b.ls            #0x7379b4
    // 0x7371b4: r1 = 4
    //     0x7371b4: movz            x1, #0x4
    // 0x7371b8: r0 = AllocateContext()
    //     0x7371b8: bl              #0xec126c  ; AllocateContextStub
    // 0x7371bc: mov             x3, x0
    // 0x7371c0: ldur            x0, [fp, #-8]
    // 0x7371c4: stur            x3, [fp, #-0x38]
    // 0x7371c8: StoreField: r3->field_f = r0
    //     0x7371c8: stur            w0, [x3, #0xf]
    // 0x7371cc: ldur            x1, [fp, #-0x10]
    // 0x7371d0: StoreField: r3->field_13 = r1
    //     0x7371d0: stur            w1, [x3, #0x13]
    // 0x7371d4: ldur            x1, [fp, #-0x18]
    // 0x7371d8: ArrayStore: r3[0] = r1  ; List_4
    //     0x7371d8: stur            w1, [x3, #0x17]
    // 0x7371dc: ldur            x1, [fp, #-0x30]
    // 0x7371e0: StoreField: r3->field_1b = r1
    //     0x7371e0: stur            w1, [x3, #0x1b]
    // 0x7371e4: mov             x2, x3
    // 0x7371e8: r1 = Function '<anonymous closure>':.
    //     0x7371e8: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c468] AnonymousClosure: (0x7389d8), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_resolveIntrinsicTrackSizes (0x73717c)
    //     0x7371ec: ldr             x1, [x1, #0x468]
    // 0x7371f0: r0 = AllocateClosure()
    //     0x7371f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7371f4: r16 = <RenderBox>
    //     0x7371f4: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x7371f8: ldr             x16, [x16, #0x1d8]
    // 0x7371fc: ldur            lr, [fp, #-0x28]
    // 0x737200: stp             lr, x16, [SP, #8]
    // 0x737204: str             x0, [SP]
    // 0x737208: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x737208: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x73720c: r0 = expand()
    //     0x73720c: bl              #0x6409ac  ; [dart:collection] ListBase::expand
    // 0x737210: r16 = <RenderBox>
    //     0x737210: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x737214: ldr             x16, [x16, #0x1d8]
    // 0x737218: stp             x0, x16, [SP]
    // 0x73721c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x73721c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x737220: r0 = IterableExt.removeDuplicates()
    //     0x737220: bl              #0x7381dc  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::IterableExt.removeDuplicates
    // 0x737224: ldur            x2, [fp, #-0x38]
    // 0x737228: r1 = Function '<anonymous closure>':.
    //     0x737228: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c470] AnonymousClosure: (0x7388dc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_resolveIntrinsicTrackSizes (0x73717c)
    //     0x73722c: ldr             x1, [x1, #0x470]
    // 0x737230: stur            x0, [fp, #-0x10]
    // 0x737234: r0 = AllocateClosure()
    //     0x737234: bl              #0xec1630  ; AllocateClosureStub
    // 0x737238: r16 = <RenderBox, int>
    //     0x737238: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c478] TypeArguments: <RenderBox, int>
    //     0x73723c: ldr             x16, [x16, #0x478]
    // 0x737240: ldur            lr, [fp, #-0x10]
    // 0x737244: stp             lr, x16, [SP, #8]
    // 0x737248: str             x0, [SP]
    // 0x73724c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x73724c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x737250: r0 = groupBy()
    //     0x737250: bl              #0x737f78  ; [package:collection/src/functions.dart] ::groupBy
    // 0x737254: stur            x0, [fp, #-0x10]
    // 0x737258: LoadField: r1 = r0->field_7
    //     0x737258: ldur            w1, [x0, #7]
    // 0x73725c: DecompressPointer r1
    //     0x73725c: add             x1, x1, HEAP, lsl #32
    // 0x737260: r0 = _CompactIterable()
    //     0x737260: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x737264: mov             x1, x0
    // 0x737268: ldur            x0, [fp, #-0x10]
    // 0x73726c: StoreField: r1->field_b = r0
    //     0x73726c: stur            w0, [x1, #0xb]
    // 0x737270: r6 = -2
    //     0x737270: orr             x6, xzr, #0xfffffffffffffffe
    // 0x737274: StoreField: r1->field_f = r6
    //     0x737274: stur            x6, [x1, #0xf]
    // 0x737278: r7 = 2
    //     0x737278: movz            x7, #0x2
    // 0x73727c: ArrayStore: r1[0] = r7  ; List_8
    //     0x73727c: stur            x7, [x1, #0x17]
    // 0x737280: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x737280: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x737284: r0 = toList()
    //     0x737284: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x737288: mov             x1, x0
    // 0x73728c: stur            x0, [fp, #-0x18]
    // 0x737290: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x737290: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x737294: r0 = sort()
    //     0x737294: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x737298: ldur            x3, [fp, #-0x18]
    // 0x73729c: LoadField: r4 = r3->field_7
    //     0x73729c: ldur            w4, [x3, #7]
    // 0x7372a0: DecompressPointer r4
    //     0x7372a0: add             x4, x4, HEAP, lsl #32
    // 0x7372a4: stur            x4, [fp, #-0x60]
    // 0x7372a8: LoadField: r0 = r3->field_b
    //     0x7372a8: ldur            w0, [x3, #0xb]
    // 0x7372ac: r5 = LoadInt32Instr(r0)
    //     0x7372ac: sbfx            x5, x0, #1, #0x1f
    // 0x7372b0: ldur            x6, [fp, #-0x20]
    // 0x7372b4: stur            x5, [fp, #-0x58]
    // 0x7372b8: LoadField: r7 = r6->field_b
    //     0x7372b8: ldur            w7, [x6, #0xb]
    // 0x7372bc: DecompressPointer r7
    //     0x7372bc: add             x7, x7, HEAP, lsl #32
    // 0x7372c0: stur            x7, [fp, #-0x50]
    // 0x7372c4: LoadField: r8 = r6->field_7
    //     0x7372c4: ldur            w8, [x6, #7]
    // 0x7372c8: DecompressPointer r8
    //     0x7372c8: add             x8, x8, HEAP, lsl #32
    // 0x7372cc: stur            x8, [fp, #-0x48]
    // 0x7372d0: ldur            x10, [fp, #-0x38]
    // 0x7372d4: r0 = 0
    //     0x7372d4: movz            x0, #0
    // 0x7372d8: ldur            x9, [fp, #-0x10]
    // 0x7372dc: stur            x10, [fp, #-0x38]
    // 0x7372e0: CheckStackOverflow
    //     0x7372e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7372e4: cmp             SP, x16
    //     0x7372e8: b.ls            #0x7379bc
    // 0x7372ec: LoadField: r1 = r3->field_b
    //     0x7372ec: ldur            w1, [x3, #0xb]
    // 0x7372f0: r2 = LoadInt32Instr(r1)
    //     0x7372f0: sbfx            x2, x1, #1, #0x1f
    // 0x7372f4: cmp             x5, x2
    // 0x7372f8: b.ne            #0x737994
    // 0x7372fc: cmp             x0, x2
    // 0x737300: b.ge            #0x7378e4
    // 0x737304: LoadField: r1 = r3->field_f
    //     0x737304: ldur            w1, [x3, #0xf]
    // 0x737308: DecompressPointer r1
    //     0x737308: add             x1, x1, HEAP, lsl #32
    // 0x73730c: ArrayLoad: r11 = r1[r0]  ; Unknown_4
    //     0x73730c: add             x16, x1, x0, lsl #2
    //     0x737310: ldur            w11, [x16, #0xf]
    // 0x737314: DecompressPointer r11
    //     0x737314: add             x11, x11, HEAP, lsl #32
    // 0x737318: stur            x11, [fp, #-0x30]
    // 0x73731c: add             x12, x0, #1
    // 0x737320: stur            x12, [fp, #-0x40]
    // 0x737324: cmp             w11, NULL
    // 0x737328: b.ne            #0x73735c
    // 0x73732c: mov             x0, x11
    // 0x737330: mov             x2, x4
    // 0x737334: r1 = Null
    //     0x737334: mov             x1, NULL
    // 0x737338: cmp             w2, NULL
    // 0x73733c: b.eq            #0x73735c
    // 0x737340: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x737340: ldur            w4, [x2, #0x17]
    // 0x737344: DecompressPointer r4
    //     0x737344: add             x4, x4, HEAP, lsl #32
    // 0x737348: r8 = X0
    //     0x737348: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x73734c: LoadField: r9 = r4->field_7
    //     0x73734c: ldur            x9, [x4, #7]
    // 0x737350: r3 = Null
    //     0x737350: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c480] Null
    //     0x737354: ldr             x3, [x3, #0x480]
    // 0x737358: blr             x9
    // 0x73735c: ldur            x0, [fp, #-0x10]
    // 0x737360: mov             x1, x0
    // 0x737364: ldur            x2, [fp, #-0x30]
    // 0x737368: r0 = _getValueOrData()
    //     0x737368: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x73736c: mov             x1, x0
    // 0x737370: ldur            x0, [fp, #-0x10]
    // 0x737374: LoadField: r2 = r0->field_f
    //     0x737374: ldur            w2, [x0, #0xf]
    // 0x737378: DecompressPointer r2
    //     0x737378: add             x2, x2, HEAP, lsl #32
    // 0x73737c: cmp             w2, w1
    // 0x737380: b.ne            #0x73738c
    // 0x737384: r4 = Null
    //     0x737384: mov             x4, NULL
    // 0x737388: b               #0x737390
    // 0x73738c: mov             x4, x1
    // 0x737390: ldur            x3, [fp, #-0x30]
    // 0x737394: stur            x4, [fp, #-0x68]
    // 0x737398: cmp             w4, NULL
    // 0x73739c: b.eq            #0x7379c4
    // 0x7373a0: ldur            x2, [fp, #-0x38]
    // 0x7373a4: r1 = Function '<anonymous closure>':.
    //     0x7373a4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c490] AnonymousClosure: (0x7387fc), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_resolveIntrinsicTrackSizes (0x73717c)
    //     0x7373a8: ldr             x1, [x1, #0x490]
    // 0x7373ac: r0 = AllocateClosure()
    //     0x7373ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x7373b0: r16 = <RenderBox, int>
    //     0x7373b0: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c478] TypeArguments: <RenderBox, int>
    //     0x7373b4: ldr             x16, [x16, #0x478]
    // 0x7373b8: ldur            lr, [fp, #-0x68]
    // 0x7373bc: stp             lr, x16, [SP, #8]
    // 0x7373c0: str             x0, [SP]
    // 0x7373c4: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x7373c4: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x7373c8: r0 = groupBy()
    //     0x7373c8: bl              #0x737f78  ; [package:collection/src/functions.dart] ::groupBy
    // 0x7373cc: stur            x0, [fp, #-0x80]
    // 0x7373d0: LoadField: r2 = r0->field_7
    //     0x7373d0: ldur            w2, [x0, #7]
    // 0x7373d4: DecompressPointer r2
    //     0x7373d4: add             x2, x2, HEAP, lsl #32
    // 0x7373d8: stur            x2, [fp, #-0x78]
    // 0x7373dc: LoadField: r3 = r0->field_f
    //     0x7373dc: ldur            w3, [x0, #0xf]
    // 0x7373e0: DecompressPointer r3
    //     0x7373e0: add             x3, x3, HEAP, lsl #32
    // 0x7373e4: stur            x3, [fp, #-0x68]
    // 0x7373e8: LoadField: r1 = r0->field_13
    //     0x7373e8: ldur            w1, [x0, #0x13]
    // 0x7373ec: r5 = LoadInt32Instr(r1)
    //     0x7373ec: sbfx            x5, x1, #1, #0x1f
    // 0x7373f0: mov             x1, x2
    // 0x7373f4: stur            x5, [fp, #-0x70]
    // 0x7373f8: r0 = _CompactIterator()
    //     0x7373f8: bl              #0x737f6c  ; Allocate_CompactIteratorStub -> _CompactIterator<X0> (size=0x38)
    // 0x7373fc: mov             x1, x0
    // 0x737400: ldur            x2, [fp, #-0x80]
    // 0x737404: ldur            x3, [fp, #-0x68]
    // 0x737408: ldur            x5, [fp, #-0x70]
    // 0x73740c: r6 = -2
    //     0x73740c: orr             x6, xzr, #0xfffffffffffffffe
    // 0x737410: r7 = 2
    //     0x737410: movz            x7, #0x2
    // 0x737414: stur            x0, [fp, #-0x68]
    // 0x737418: r0 = _CompactIterator()
    //     0x737418: bl              #0x737ec0  ; [dart:_compact_hash] _CompactIterator::_CompactIterator
    // 0x73741c: ldur            x0, [fp, #-0x30]
    // 0x737420: r2 = LoadInt32Instr(r0)
    //     0x737420: sbfx            x2, x0, #1, #0x1f
    //     0x737424: tbz             w0, #0, #0x73742c
    //     0x737428: ldur            x2, [x0, #7]
    // 0x73742c: stur            x2, [fp, #-0x70]
    // 0x737430: ldur            x5, [fp, #-0x38]
    // 0x737434: ldur            x3, [fp, #-0x80]
    // 0x737438: ldur            x0, [fp, #-0x68]
    // 0x73743c: ldur            x4, [fp, #-0x50]
    // 0x737440: stur            x5, [fp, #-0x30]
    // 0x737444: CheckStackOverflow
    //     0x737444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x737448: cmp             SP, x16
    //     0x73744c: b.ls            #0x7379c8
    // 0x737450: mov             x1, x0
    // 0x737454: r0 = moveNext()
    //     0x737454: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x737458: tbnz            w0, #4, #0x7378c0
    // 0x73745c: ldur            x3, [fp, #-0x68]
    // 0x737460: LoadField: r4 = r3->field_33
    //     0x737460: ldur            w4, [x3, #0x33]
    // 0x737464: DecompressPointer r4
    //     0x737464: add             x4, x4, HEAP, lsl #32
    // 0x737468: stur            x4, [fp, #-0x38]
    // 0x73746c: cmp             w4, NULL
    // 0x737470: b.ne            #0x7374a4
    // 0x737474: mov             x0, x4
    // 0x737478: ldur            x2, [fp, #-0x78]
    // 0x73747c: r1 = Null
    //     0x73747c: mov             x1, NULL
    // 0x737480: cmp             w2, NULL
    // 0x737484: b.eq            #0x7374a4
    // 0x737488: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x737488: ldur            w4, [x2, #0x17]
    // 0x73748c: DecompressPointer r4
    //     0x73748c: add             x4, x4, HEAP, lsl #32
    // 0x737490: r8 = X0
    //     0x737490: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x737494: LoadField: r9 = r4->field_7
    //     0x737494: ldur            x9, [x4, #7]
    // 0x737498: r3 = Null
    //     0x737498: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c498] Null
    //     0x73749c: ldr             x3, [x3, #0x498]
    // 0x7374a0: blr             x9
    // 0x7374a4: ldur            x1, [fp, #-0x80]
    // 0x7374a8: ldur            x5, [fp, #-0x30]
    // 0x7374ac: ldur            x2, [fp, #-0x38]
    // 0x7374b0: ldur            x3, [fp, #-0x50]
    // 0x7374b4: ldur            x0, [fp, #-0x70]
    // 0x7374b8: r1 = 1
    //     0x7374b8: movz            x1, #0x1
    // 0x7374bc: r0 = AllocateContext()
    //     0x7374bc: bl              #0xec126c  ; AllocateContextStub
    // 0x7374c0: mov             x1, x0
    // 0x7374c4: ldur            x5, [fp, #-0x30]
    // 0x7374c8: stur            x1, [fp, #-0x98]
    // 0x7374cc: StoreField: r1->field_b = r5
    //     0x7374cc: stur            w5, [x1, #0xb]
    // 0x7374d0: ldur            x2, [fp, #-0x38]
    // 0x7374d4: r3 = LoadInt32Instr(r2)
    //     0x7374d4: sbfx            x3, x2, #1, #0x1f
    //     0x7374d8: tbz             w2, #0, #0x7374e0
    //     0x7374dc: ldur            x3, [x2, #7]
    // 0x7374e0: ldur            x4, [fp, #-0x70]
    // 0x7374e4: stur            x3, [fp, #-0x90]
    // 0x7374e8: add             x6, x3, x4
    // 0x7374ec: ldur            x7, [fp, #-0x50]
    // 0x7374f0: stur            x6, [fp, #-0x88]
    // 0x7374f4: r0 = LoadClassIdInstr(r7)
    //     0x7374f4: ldur            x0, [x7, #-1]
    //     0x7374f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7374fc: str             x7, [SP]
    // 0x737500: r0 = GDT[cid_x0 + 0xc834]()
    //     0x737500: movz            x17, #0xc834
    //     0x737504: add             lr, x0, x17
    //     0x737508: ldr             lr, [x21, lr, lsl #3]
    //     0x73750c: blr             lr
    // 0x737510: mov             x3, x0
    // 0x737514: ldur            x2, [fp, #-0x88]
    // 0x737518: r0 = BoxInt64Instr(r2)
    //     0x737518: sbfiz           x0, x2, #1, #0x1f
    //     0x73751c: cmp             x2, x0, asr #1
    //     0x737520: b.eq            #0x73752c
    //     0x737524: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x737528: stur            x2, [x0, #7]
    // 0x73752c: stur            x0, [fp, #-0xa0]
    // 0x737530: r1 = LoadInt32Instr(r3)
    //     0x737530: sbfx            x1, x3, #1, #0x1f
    //     0x737534: tbz             w3, #0, #0x73753c
    //     0x737538: ldur            x1, [x3, #7]
    // 0x73753c: mov             x3, x1
    // 0x737540: ldur            x1, [fp, #-0x90]
    // 0x737544: mov             x2, x0
    // 0x737548: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x737548: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x73754c: r0 = checkValidRange()
    //     0x73754c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x737550: ldur            x1, [fp, #-0x48]
    // 0x737554: r0 = SubListIterable()
    //     0x737554: bl              #0x66cb70  ; AllocateSubListIterableStub -> SubListIterable<X0> (size=0x1c)
    // 0x737558: mov             x1, x0
    // 0x73755c: ldur            x2, [fp, #-0x20]
    // 0x737560: ldur            x3, [fp, #-0x90]
    // 0x737564: ldur            x5, [fp, #-0xa0]
    // 0x737568: stur            x0, [fp, #-0xa0]
    // 0x73756c: r0 = SubListIterable()
    //     0x73756c: bl              #0x66c994  ; [dart:_internal] SubListIterable::SubListIterable
    // 0x737570: ldur            x1, [fp, #-0x80]
    // 0x737574: ldur            x2, [fp, #-0x38]
    // 0x737578: r0 = _getValueOrData()
    //     0x737578: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x73757c: mov             x1, x0
    // 0x737580: ldur            x0, [fp, #-0x80]
    // 0x737584: LoadField: r2 = r0->field_f
    //     0x737584: ldur            w2, [x0, #0xf]
    // 0x737588: DecompressPointer r2
    //     0x737588: add             x2, x2, HEAP, lsl #32
    // 0x73758c: cmp             w2, w1
    // 0x737590: b.ne            #0x73759c
    // 0x737594: r2 = Null
    //     0x737594: mov             x2, NULL
    // 0x737598: b               #0x7375a0
    // 0x73759c: mov             x2, x1
    // 0x7375a0: ldur            x1, [fp, #-0xa0]
    // 0x7375a4: stur            x2, [fp, #-0x38]
    // 0x7375a8: r0 = iterator()
    //     0x7375a8: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x7375ac: mov             x2, x0
    // 0x7375b0: stur            x2, [fp, #-0xa8]
    // 0x7375b4: CheckStackOverflow
    //     0x7375b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7375b8: cmp             SP, x16
    //     0x7375bc: b.ls            #0x7379d0
    // 0x7375c0: r0 = LoadClassIdInstr(r2)
    //     0x7375c0: ldur            x0, [x2, #-1]
    //     0x7375c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7375c8: mov             x1, x2
    // 0x7375cc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x7375cc: movz            x17, #0x292d
    //     0x7375d0: movk            x17, #0x1, lsl #16
    //     0x7375d4: add             lr, x0, x17
    //     0x7375d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7375dc: blr             lr
    // 0x7375e0: tbnz            w0, #4, #0x737628
    // 0x7375e4: ldur            x2, [fp, #-0xa8]
    // 0x7375e8: r0 = LoadClassIdInstr(r2)
    //     0x7375e8: ldur            x0, [x2, #-1]
    //     0x7375ec: ubfx            x0, x0, #0xc, #0x14
    // 0x7375f0: mov             x1, x2
    // 0x7375f4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x7375f4: movz            x17, #0x384d
    //     0x7375f8: movk            x17, #0x1, lsl #16
    //     0x7375fc: add             lr, x0, x17
    //     0x737600: ldr             lr, [x21, lr, lsl #3]
    //     0x737604: blr             lr
    // 0x737608: LoadField: r1 = r0->field_f
    //     0x737608: ldur            w1, [x0, #0xf]
    // 0x73760c: DecompressPointer r1
    //     0x73760c: add             x1, x1, HEAP, lsl #32
    // 0x737610: r2 = LoadClassIdInstr(r1)
    //     0x737610: ldur            x2, [x1, #-1]
    //     0x737614: ubfx            x2, x2, #0xc, #0x14
    // 0x737618: cmp             x2, #0xec2
    // 0x73761c: b.eq            #0x73762c
    // 0x737620: ldur            x2, [fp, #-0xa8]
    // 0x737624: b               #0x7375b4
    // 0x737628: r0 = Null
    //     0x737628: mov             x0, NULL
    // 0x73762c: stur            x0, [fp, #-0xa8]
    // 0x737630: cmp             w0, NULL
    // 0x737634: b.eq            #0x7378b4
    // 0x737638: ldur            x5, [fp, #-0x30]
    // 0x73763c: r1 = Function '<anonymous closure>':.
    //     0x73763c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c4a8] Function: [dart:core] Object::_simpleInstanceOfFalse (0xebd578)
    //     0x737640: ldr             x1, [x1, #0x4a8]
    // 0x737644: r2 = Null
    //     0x737644: mov             x2, NULL
    // 0x737648: r0 = AllocateClosure()
    //     0x737648: bl              #0xec1630  ; AllocateClosureStub
    // 0x73764c: ldur            x1, [fp, #-0xa0]
    // 0x737650: mov             x2, x0
    // 0x737654: r0 = any()
    //     0x737654: bl              #0x758340  ; [dart:_internal] ListIterable::any
    // 0x737658: ldur            x5, [fp, #-0x30]
    // 0x73765c: ArrayLoad: r0 = r5[0]  ; List_4
    //     0x73765c: ldur            w0, [x5, #0x17]
    // 0x737660: DecompressPointer r0
    //     0x737660: add             x0, x0, HEAP, lsl #32
    // 0x737664: LoadField: r1 = r0->field_7
    //     0x737664: ldur            x1, [x0, #7]
    // 0x737668: cmp             x1, #0
    // 0x73766c: b.gt            #0x737678
    // 0x737670: r1 = Instance_Axis
    //     0x737670: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x737674: b               #0x73767c
    // 0x737678: r1 = Instance_Axis
    //     0x737678: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x73767c: ldur            x2, [fp, #-0x98]
    // 0x737680: mov             x0, x1
    // 0x737684: StoreField: r2->field_f = r0
    //     0x737684: stur            w0, [x2, #0xf]
    //     0x737688: ldurb           w16, [x2, #-1]
    //     0x73768c: ldurb           w17, [x0, #-1]
    //     0x737690: and             x16, x17, x16, lsr #2
    //     0x737694: tst             x16, HEAP, lsr #32
    //     0x737698: b.eq            #0x7376a0
    //     0x73769c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x7376a0: LoadField: r0 = r5->field_1b
    //     0x7376a0: ldur            w0, [x5, #0x1b]
    // 0x7376a4: DecompressPointer r0
    //     0x7376a4: add             x0, x0, HEAP, lsl #32
    // 0x7376a8: r16 = Instance_Axis
    //     0x7376a8: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x7376ac: cmp             w1, w16
    // 0x7376b0: b.ne            #0x7376c4
    // 0x7376b4: LoadField: r1 = r0->field_4f
    //     0x7376b4: ldur            w1, [x0, #0x4f]
    // 0x7376b8: DecompressPointer r1
    //     0x7376b8: add             x1, x1, HEAP, lsl #32
    // 0x7376bc: tbnz            w1, #4, #0x7376e4
    // 0x7376c0: b               #0x7376d0
    // 0x7376c4: LoadField: r1 = r0->field_53
    //     0x7376c4: ldur            w1, [x0, #0x53]
    // 0x7376c8: DecompressPointer r1
    //     0x7376c8: add             x1, x1, HEAP, lsl #32
    // 0x7376cc: tbnz            w1, #4, #0x7376e4
    // 0x7376d0: r1 = Function '<anonymous closure>':.
    //     0x7376d0: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c4b0] AnonymousClosure: (0x738544), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_resolveIntrinsicTrackSizes (0x73717c)
    //     0x7376d4: ldr             x1, [x1, #0x4b0]
    // 0x7376d8: r0 = AllocateClosure()
    //     0x7376d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7376dc: mov             x2, x0
    // 0x7376e0: b               #0x7376f8
    // 0x7376e4: r1 = Function '<anonymous closure>':.
    //     0x7376e4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c4b8] AnonymousClosure: (0x73853c), of [package:flutter_layout_grid/src/rendering/track_size.dart] IntrinsicContentTrackSize
    //     0x7376e8: ldr             x1, [x1, #0x4b8]
    // 0x7376ec: r2 = Null
    //     0x7376ec: mov             x2, NULL
    // 0x7376f0: r0 = AllocateClosure()
    //     0x7376f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7376f4: mov             x2, x0
    // 0x7376f8: ldur            x5, [fp, #-0x30]
    // 0x7376fc: ldur            x1, [fp, #-0x38]
    // 0x737700: ldur            x0, [fp, #-0xa8]
    // 0x737704: stur            x2, [fp, #-0xb8]
    // 0x737708: LoadField: r3 = r0->field_f
    //     0x737708: ldur            w3, [x0, #0xf]
    // 0x73770c: DecompressPointer r3
    //     0x73770c: add             x3, x3, HEAP, lsl #32
    // 0x737710: stur            x3, [fp, #-0xb0]
    // 0x737714: LoadField: r0 = r5->field_13
    //     0x737714: ldur            w0, [x5, #0x13]
    // 0x737718: DecompressPointer r0
    //     0x737718: add             x0, x0, HEAP, lsl #32
    // 0x73771c: stur            x0, [fp, #-0x98]
    // 0x737720: cmp             w1, NULL
    // 0x737724: b.eq            #0x7379d8
    // 0x737728: r4 = LoadClassIdInstr(r3)
    //     0x737728: ldur            x4, [x3, #-1]
    //     0x73772c: ubfx            x4, x4, #0xc, #0x14
    // 0x737730: stur            x4, [fp, #-0x88]
    // 0x737734: cmp             x4, #0xec2
    // 0x737738: b.ne            #0x7377c4
    // 0x73773c: r1 = 3
    //     0x73773c: movz            x1, #0x3
    // 0x737740: r0 = AllocateContext()
    //     0x737740: bl              #0xec126c  ; AllocateContextStub
    // 0x737744: mov             x1, x0
    // 0x737748: ldur            x0, [fp, #-0xb0]
    // 0x73774c: StoreField: r1->field_f = r0
    //     0x73774c: stur            w0, [x1, #0xf]
    // 0x737750: ldur            x2, [fp, #-0x98]
    // 0x737754: StoreField: r1->field_13 = r2
    //     0x737754: stur            w2, [x1, #0x13]
    // 0x737758: ldur            x3, [fp, #-0xb8]
    // 0x73775c: ArrayStore: r1[0] = r3  ; List_4
    //     0x73775c: stur            w3, [x1, #0x17]
    // 0x737760: mov             x2, x1
    // 0x737764: r1 = Function '<anonymous closure>':.
    //     0x737764: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c3d0] AnonymousClosure: (0x739214), of [package:flutter_layout_grid/src/rendering/track_size.dart] IntrinsicContentTrackSize
    //     0x737768: ldr             x1, [x1, #0x3d0]
    // 0x73776c: r0 = AllocateClosure()
    //     0x73776c: bl              #0xec1630  ; AllocateClosureStub
    // 0x737770: ldur            x1, [fp, #-0x38]
    // 0x737774: r2 = LoadClassIdInstr(r1)
    //     0x737774: ldur            x2, [x1, #-1]
    //     0x737778: ubfx            x2, x2, #0xc, #0x14
    // 0x73777c: r16 = <double>
    //     0x73777c: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x737780: stp             x1, x16, [SP, #8]
    // 0x737784: str             x0, [SP]
    // 0x737788: mov             x0, x2
    // 0x73778c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x73778c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x737790: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x737790: movz            x17, #0xf28c
    //     0x737794: add             lr, x0, x17
    //     0x737798: ldr             lr, [x21, lr, lsl #3]
    //     0x73779c: blr             lr
    // 0x7377a0: r16 = <double>
    //     0x7377a0: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x7377a4: stp             x0, x16, [SP]
    // 0x7377a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7377a8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7377ac: r0 = max()
    //     0x7377ac: bl              #0x738f14  ; [package:quiver/src/iterables/min_max.dart] ::max
    // 0x7377b0: cmp             w0, NULL
    // 0x7377b4: b.eq            #0x7379dc
    // 0x7377b8: LoadField: d0 = r0->field_7
    //     0x7377b8: ldur            d0, [x0, #7]
    // 0x7377bc: ldur            x0, [fp, #-0xb0]
    // 0x7377c0: b               #0x7377cc
    // 0x7377c4: mov             x0, x3
    // 0x7377c8: LoadField: d0 = r0->field_b
    //     0x7377c8: ldur            d0, [x0, #0xb]
    // 0x7377cc: ldur            x5, [fp, #-0x30]
    // 0x7377d0: ldur            x4, [fp, #-0x88]
    // 0x7377d4: ldur            x1, [fp, #-8]
    // 0x7377d8: ldur            x2, [fp, #-0xa0]
    // 0x7377dc: r3 = Instance__IntrinsicDimension
    //     0x7377dc: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c3e8] Obj!_IntrinsicDimension@e32dc1
    //     0x7377e0: ldr             x3, [x3, #0x3e8]
    // 0x7377e4: r0 = _distributeCalculatedSpaceToSpannedTracks()
    //     0x7377e4: bl              #0x737ae0  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeCalculatedSpaceToSpannedTracks
    // 0x7377e8: ldur            x5, [fp, #-0x30]
    // 0x7377ec: LoadField: r0 = r5->field_13
    //     0x7377ec: ldur            w0, [x5, #0x13]
    // 0x7377f0: DecompressPointer r0
    //     0x7377f0: add             x0, x0, HEAP, lsl #32
    // 0x7377f4: ldur            x1, [fp, #-0x88]
    // 0x7377f8: stur            x0, [fp, #-0x98]
    // 0x7377fc: cmp             x1, #0xec2
    // 0x737800: b.ne            #0x737898
    // 0x737804: ldur            x3, [fp, #-0xb8]
    // 0x737808: ldur            x1, [fp, #-0xb0]
    // 0x73780c: ldur            x2, [fp, #-0x38]
    // 0x737810: r1 = 3
    //     0x737810: movz            x1, #0x3
    // 0x737814: r0 = AllocateContext()
    //     0x737814: bl              #0xec126c  ; AllocateContextStub
    // 0x737818: mov             x1, x0
    // 0x73781c: ldur            x0, [fp, #-0xb0]
    // 0x737820: StoreField: r1->field_f = r0
    //     0x737820: stur            w0, [x1, #0xf]
    // 0x737824: ldur            x0, [fp, #-0x98]
    // 0x737828: StoreField: r1->field_13 = r0
    //     0x737828: stur            w0, [x1, #0x13]
    // 0x73782c: ldur            x0, [fp, #-0xb8]
    // 0x737830: ArrayStore: r1[0] = r0  ; List_4
    //     0x737830: stur            w0, [x1, #0x17]
    // 0x737834: mov             x2, x1
    // 0x737838: r1 = Function '<anonymous closure>':.
    //     0x737838: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c4c0] AnonymousClosure: (0x738420), of [package:flutter_layout_grid/src/rendering/track_size.dart] IntrinsicContentTrackSize
    //     0x73783c: ldr             x1, [x1, #0x4c0]
    // 0x737840: r0 = AllocateClosure()
    //     0x737840: bl              #0xec1630  ; AllocateClosureStub
    // 0x737844: mov             x1, x0
    // 0x737848: ldur            x0, [fp, #-0x38]
    // 0x73784c: r2 = LoadClassIdInstr(r0)
    //     0x73784c: ldur            x2, [x0, #-1]
    //     0x737850: ubfx            x2, x2, #0xc, #0x14
    // 0x737854: r16 = <double>
    //     0x737854: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x737858: stp             x0, x16, [SP, #8]
    // 0x73785c: str             x1, [SP]
    // 0x737860: mov             x0, x2
    // 0x737864: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x737864: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x737868: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x737868: movz            x17, #0xf28c
    //     0x73786c: add             lr, x0, x17
    //     0x737870: ldr             lr, [x21, lr, lsl #3]
    //     0x737874: blr             lr
    // 0x737878: r16 = <double>
    //     0x737878: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x73787c: stp             x0, x16, [SP]
    // 0x737880: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x737880: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x737884: r0 = max()
    //     0x737884: bl              #0x738f14  ; [package:quiver/src/iterables/min_max.dart] ::max
    // 0x737888: cmp             w0, NULL
    // 0x73788c: b.eq            #0x7379e0
    // 0x737890: LoadField: d0 = r0->field_7
    //     0x737890: ldur            d0, [x0, #7]
    // 0x737894: b               #0x7378a0
    // 0x737898: ldur            x0, [fp, #-0xb0]
    // 0x73789c: LoadField: d0 = r0->field_b
    //     0x73789c: ldur            d0, [x0, #0xb]
    // 0x7378a0: ldur            x1, [fp, #-8]
    // 0x7378a4: ldur            x2, [fp, #-0xa0]
    // 0x7378a8: r3 = Instance__IntrinsicDimension
    //     0x7378a8: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c4c8] Obj!_IntrinsicDimension@e32da1
    //     0x7378ac: ldr             x3, [x3, #0x4c8]
    // 0x7378b0: r0 = _distributeCalculatedSpaceToSpannedTracks()
    //     0x7378b0: bl              #0x737ae0  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeCalculatedSpaceToSpannedTracks
    // 0x7378b4: ldur            x5, [fp, #-0x30]
    // 0x7378b8: ldur            x2, [fp, #-0x70]
    // 0x7378bc: b               #0x737434
    // 0x7378c0: ldur            x10, [fp, #-0x30]
    // 0x7378c4: ldur            x0, [fp, #-0x40]
    // 0x7378c8: ldur            x6, [fp, #-0x20]
    // 0x7378cc: ldur            x3, [fp, #-0x18]
    // 0x7378d0: ldur            x8, [fp, #-0x48]
    // 0x7378d4: ldur            x4, [fp, #-0x60]
    // 0x7378d8: ldur            x7, [fp, #-0x50]
    // 0x7378dc: ldur            x5, [fp, #-0x58]
    // 0x7378e0: b               #0x7372d8
    // 0x7378e4: ldur            x0, [fp, #-0x28]
    // 0x7378e8: LoadField: r1 = r0->field_b
    //     0x7378e8: ldur            w1, [x0, #0xb]
    // 0x7378ec: r2 = LoadInt32Instr(r1)
    //     0x7378ec: sbfx            x2, x1, #1, #0x1f
    // 0x7378f0: stur            x2, [fp, #-0x58]
    // 0x7378f4: r1 = 0
    //     0x7378f4: movz            x1, #0
    // 0x7378f8: d0 = inf
    //     0x7378f8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x7378fc: CheckStackOverflow
    //     0x7378fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x737900: cmp             SP, x16
    //     0x737904: b.ls            #0x7379e4
    // 0x737908: LoadField: r3 = r0->field_b
    //     0x737908: ldur            w3, [x0, #0xb]
    // 0x73790c: r4 = LoadInt32Instr(r3)
    //     0x73790c: sbfx            x4, x3, #1, #0x1f
    // 0x737910: cmp             x2, x4
    // 0x737914: b.ne            #0x737978
    // 0x737918: cmp             x1, x4
    // 0x73791c: b.ge            #0x737968
    // 0x737920: LoadField: r3 = r0->field_f
    //     0x737920: ldur            w3, [x0, #0xf]
    // 0x737924: DecompressPointer r3
    //     0x737924: add             x3, x3, HEAP, lsl #32
    // 0x737928: ArrayLoad: r4 = r3[r1]  ; Unknown_4
    //     0x737928: add             x16, x3, x1, lsl #2
    //     0x73792c: ldur            w4, [x16, #0xf]
    // 0x737930: DecompressPointer r4
    //     0x737930: add             x4, x4, HEAP, lsl #32
    // 0x737934: add             x3, x1, #1
    // 0x737938: stur            x3, [fp, #-0x40]
    // 0x73793c: LoadField: d1 = r4->field_1b
    //     0x73793c: ldur            d1, [x4, #0x1b]
    // 0x737940: fcmp            d1, d0
    // 0x737944: b.ne            #0x737958
    // 0x737948: LoadField: d1 = r4->field_13
    //     0x737948: ldur            d1, [x4, #0x13]
    // 0x73794c: StoreField: r4->field_1b = d1
    //     0x73794c: stur            d1, [x4, #0x1b]
    // 0x737950: mov             x1, x4
    // 0x737954: r0 = _increaseGrowthLimitIfNecessary()
    //     0x737954: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x737958: ldur            x1, [fp, #-0x40]
    // 0x73795c: ldur            x0, [fp, #-0x28]
    // 0x737960: ldur            x2, [fp, #-0x58]
    // 0x737964: b               #0x7378f8
    // 0x737968: r0 = Null
    //     0x737968: mov             x0, NULL
    // 0x73796c: LeaveFrame
    //     0x73796c: mov             SP, fp
    //     0x737970: ldp             fp, lr, [SP], #0x10
    // 0x737974: ret
    //     0x737974: ret             
    // 0x737978: r0 = ConcurrentModificationError()
    //     0x737978: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x73797c: mov             x1, x0
    // 0x737980: ldur            x0, [fp, #-0x28]
    // 0x737984: StoreField: r1->field_b = r0
    //     0x737984: stur            w0, [x1, #0xb]
    // 0x737988: mov             x0, x1
    // 0x73798c: r0 = Throw()
    //     0x73798c: bl              #0xec04b8  ; ThrowStub
    // 0x737990: brk             #0
    // 0x737994: mov             x0, x3
    // 0x737998: r0 = ConcurrentModificationError()
    //     0x737998: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x73799c: mov             x1, x0
    // 0x7379a0: ldur            x0, [fp, #-0x18]
    // 0x7379a4: StoreField: r1->field_b = r0
    //     0x7379a4: stur            w0, [x1, #0xb]
    // 0x7379a8: mov             x0, x1
    // 0x7379ac: r0 = Throw()
    //     0x7379ac: bl              #0xec04b8  ; ThrowStub
    // 0x7379b0: brk             #0
    // 0x7379b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7379b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7379b8: b               #0x7371b4
    // 0x7379bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7379bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7379c0: b               #0x7372ec
    // 0x7379c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7379c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7379c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7379c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7379cc: b               #0x737450
    // 0x7379d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7379d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7379d4: b               #0x7375c0
    // 0x7379d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7379d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7379dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7379dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7379e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7379e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7379e4: r0 = StackOverflowSharedWithFPURegs()
    //     0x7379e4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7379e8: b               #0x737908
  }
  _ _distributeCalculatedSpaceToSpannedTracks(/* No info */) {
    // ** addr: 0x737ae0, size: 0x3e0
    // 0x737ae0: EnterFrame
    //     0x737ae0: stp             fp, lr, [SP, #-0x10]!
    //     0x737ae4: mov             fp, SP
    // 0x737ae8: AllocStack(0x58)
    //     0x737ae8: sub             SP, SP, #0x58
    // 0x737aec: SetupParameters(RenderLayoutGrid this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x50 */)
    //     0x737aec: mov             x0, x2
    //     0x737af0: stur            x2, [fp, #-0x10]
    //     0x737af4: mov             x2, x1
    //     0x737af8: mov             x5, x3
    //     0x737afc: stur            x1, [fp, #-8]
    //     0x737b00: stur            x3, [fp, #-0x18]
    //     0x737b04: stur            d0, [fp, #-0x50]
    // 0x737b08: CheckStackOverflow
    //     0x737b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x737b0c: cmp             SP, x16
    //     0x737b10: b.ls            #0x737ea8
    // 0x737b14: mov             x1, x0
    // 0x737b18: r0 = iterator()
    //     0x737b18: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x737b1c: mov             x1, x0
    // 0x737b20: stur            x1, [fp, #-0x38]
    // 0x737b24: LoadField: r2 = r1->field_b
    //     0x737b24: ldur            w2, [x1, #0xb]
    // 0x737b28: DecompressPointer r2
    //     0x737b28: add             x2, x2, HEAP, lsl #32
    // 0x737b2c: stur            x2, [fp, #-0x30]
    // 0x737b30: LoadField: r3 = r1->field_f
    //     0x737b30: ldur            x3, [x1, #0xf]
    // 0x737b34: stur            x3, [fp, #-0x28]
    // 0x737b38: LoadField: r4 = r1->field_7
    //     0x737b38: ldur            w4, [x1, #7]
    // 0x737b3c: DecompressPointer r4
    //     0x737b3c: add             x4, x4, HEAP, lsl #32
    // 0x737b40: stur            x4, [fp, #-0x20]
    // 0x737b44: ldur            d0, [fp, #-0x50]
    // 0x737b48: ldur            x5, [fp, #-0x18]
    // 0x737b4c: stur            d0, [fp, #-0x50]
    // 0x737b50: CheckStackOverflow
    //     0x737b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x737b54: cmp             SP, x16
    //     0x737b58: b.ls            #0x737eb0
    // 0x737b5c: r0 = LoadClassIdInstr(r2)
    //     0x737b5c: ldur            x0, [x2, #-1]
    //     0x737b60: ubfx            x0, x0, #0xc, #0x14
    // 0x737b64: str             x2, [SP]
    // 0x737b68: r0 = GDT[cid_x0 + 0xc834]()
    //     0x737b68: movz            x17, #0xc834
    //     0x737b6c: add             lr, x0, x17
    //     0x737b70: ldr             lr, [x21, lr, lsl #3]
    //     0x737b74: blr             lr
    // 0x737b78: r1 = LoadInt32Instr(r0)
    //     0x737b78: sbfx            x1, x0, #1, #0x1f
    //     0x737b7c: tbz             w0, #0, #0x737b84
    //     0x737b80: ldur            x1, [x0, #7]
    // 0x737b84: ldur            x3, [fp, #-0x28]
    // 0x737b88: cmp             x3, x1
    // 0x737b8c: b.ne            #0x737e88
    // 0x737b90: ldur            x4, [fp, #-0x38]
    // 0x737b94: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x737b94: ldur            x2, [x4, #0x17]
    // 0x737b98: cmp             x2, x1
    // 0x737b9c: b.ge            #0x737c90
    // 0x737ba0: ldur            x5, [fp, #-0x30]
    // 0x737ba4: r0 = LoadClassIdInstr(r5)
    //     0x737ba4: ldur            x0, [x5, #-1]
    //     0x737ba8: ubfx            x0, x0, #0xc, #0x14
    // 0x737bac: mov             x1, x5
    // 0x737bb0: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x737bb0: movz            x17, #0xd28f
    //     0x737bb4: add             lr, x0, x17
    //     0x737bb8: ldr             lr, [x21, lr, lsl #3]
    //     0x737bbc: blr             lr
    // 0x737bc0: mov             x4, x0
    // 0x737bc4: ldur            x3, [fp, #-0x38]
    // 0x737bc8: stur            x4, [fp, #-0x40]
    // 0x737bcc: StoreField: r3->field_1f = r0
    //     0x737bcc: stur            w0, [x3, #0x1f]
    //     0x737bd0: tbz             w0, #0, #0x737bec
    //     0x737bd4: ldurb           w16, [x3, #-1]
    //     0x737bd8: ldurb           w17, [x0, #-1]
    //     0x737bdc: and             x16, x17, x16, lsr #2
    //     0x737be0: tst             x16, HEAP, lsr #32
    //     0x737be4: b.eq            #0x737bec
    //     0x737be8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x737bec: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x737bec: ldur            x0, [x3, #0x17]
    // 0x737bf0: add             x1, x0, #1
    // 0x737bf4: ArrayStore: r3[0] = r1  ; List_8
    //     0x737bf4: stur            x1, [x3, #0x17]
    // 0x737bf8: cmp             w4, NULL
    // 0x737bfc: b.ne            #0x737c30
    // 0x737c00: mov             x0, x4
    // 0x737c04: ldur            x2, [fp, #-0x20]
    // 0x737c08: r1 = Null
    //     0x737c08: mov             x1, NULL
    // 0x737c0c: cmp             w2, NULL
    // 0x737c10: b.eq            #0x737c30
    // 0x737c14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x737c14: ldur            w4, [x2, #0x17]
    // 0x737c18: DecompressPointer r4
    //     0x737c18: add             x4, x4, HEAP, lsl #32
    // 0x737c1c: r8 = X0
    //     0x737c1c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x737c20: LoadField: r9 = r4->field_7
    //     0x737c20: ldur            x9, [x4, #7]
    // 0x737c24: r3 = Null
    //     0x737c24: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c518] Null
    //     0x737c28: ldr             x3, [x3, #0x518]
    // 0x737c2c: blr             x9
    // 0x737c30: ldur            x5, [fp, #-0x18]
    // 0x737c34: r16 = Instance__IntrinsicDimension
    //     0x737c34: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4c3e8] Obj!_IntrinsicDimension@e32dc1
    //     0x737c38: ldr             x16, [x16, #0x3e8]
    // 0x737c3c: cmp             w5, w16
    // 0x737c40: b.ne            #0x737c58
    // 0x737c44: ldur            x0, [fp, #-0x40]
    // 0x737c48: LoadField: d1 = r0->field_13
    //     0x737c48: ldur            d1, [x0, #0x13]
    // 0x737c4c: mov             v2.16b, v1.16b
    // 0x737c50: d1 = inf
    //     0x737c50: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x737c54: b               #0x737c70
    // 0x737c58: ldur            x0, [fp, #-0x40]
    // 0x737c5c: d1 = inf
    //     0x737c5c: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x737c60: LoadField: d2 = r0->field_1b
    //     0x737c60: ldur            d2, [x0, #0x1b]
    // 0x737c64: fcmp            d2, d1
    // 0x737c68: b.ne            #0x737c70
    // 0x737c6c: LoadField: d2 = r0->field_13
    //     0x737c6c: ldur            d2, [x0, #0x13]
    // 0x737c70: ldur            d0, [fp, #-0x50]
    // 0x737c74: fsub            d3, d0, d2
    // 0x737c78: mov             v0.16b, v3.16b
    // 0x737c7c: ldur            x1, [fp, #-0x38]
    // 0x737c80: ldur            x4, [fp, #-0x20]
    // 0x737c84: ldur            x2, [fp, #-0x30]
    // 0x737c88: ldur            x3, [fp, #-0x28]
    // 0x737c8c: b               #0x737b4c
    // 0x737c90: ldur            x5, [fp, #-0x18]
    // 0x737c94: mov             x0, x4
    // 0x737c98: ldur            d0, [fp, #-0x50]
    // 0x737c9c: d2 = 0.000000
    //     0x737c9c: eor             v2.16b, v2.16b, v2.16b
    // 0x737ca0: d1 = inf
    //     0x737ca0: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x737ca4: StoreField: r0->field_1f = rNULL
    //     0x737ca4: stur            NULL, [x0, #0x1f]
    // 0x737ca8: fcmp            d2, d0
    // 0x737cac: b.lt            #0x737e0c
    // 0x737cb0: ldur            x1, [fp, #-0x10]
    // 0x737cb4: r0 = iterator()
    //     0x737cb4: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0x737cb8: mov             x1, x0
    // 0x737cbc: stur            x1, [fp, #-0x40]
    // 0x737cc0: LoadField: r2 = r1->field_b
    //     0x737cc0: ldur            w2, [x1, #0xb]
    // 0x737cc4: DecompressPointer r2
    //     0x737cc4: add             x2, x2, HEAP, lsl #32
    // 0x737cc8: stur            x2, [fp, #-0x38]
    // 0x737ccc: LoadField: r3 = r1->field_f
    //     0x737ccc: ldur            x3, [x1, #0xf]
    // 0x737cd0: stur            x3, [fp, #-0x28]
    // 0x737cd4: LoadField: r4 = r1->field_7
    //     0x737cd4: ldur            w4, [x1, #7]
    // 0x737cd8: DecompressPointer r4
    //     0x737cd8: add             x4, x4, HEAP, lsl #32
    // 0x737cdc: stur            x4, [fp, #-0x20]
    // 0x737ce0: CheckStackOverflow
    //     0x737ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x737ce4: cmp             SP, x16
    //     0x737ce8: b.ls            #0x737eb8
    // 0x737cec: r0 = LoadClassIdInstr(r2)
    //     0x737cec: ldur            x0, [x2, #-1]
    //     0x737cf0: ubfx            x0, x0, #0xc, #0x14
    // 0x737cf4: str             x2, [SP]
    // 0x737cf8: r0 = GDT[cid_x0 + 0xc834]()
    //     0x737cf8: movz            x17, #0xc834
    //     0x737cfc: add             lr, x0, x17
    //     0x737d00: ldr             lr, [x21, lr, lsl #3]
    //     0x737d04: blr             lr
    // 0x737d08: r1 = LoadInt32Instr(r0)
    //     0x737d08: sbfx            x1, x0, #1, #0x1f
    //     0x737d0c: tbz             w0, #0, #0x737d14
    //     0x737d10: ldur            x1, [x0, #7]
    // 0x737d14: ldur            x3, [fp, #-0x28]
    // 0x737d18: cmp             x3, x1
    // 0x737d1c: b.ne            #0x737e68
    // 0x737d20: ldur            x4, [fp, #-0x40]
    // 0x737d24: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x737d24: ldur            x2, [x4, #0x17]
    // 0x737d28: cmp             x2, x1
    // 0x737d2c: b.ge            #0x737df4
    // 0x737d30: ldur            x5, [fp, #-0x38]
    // 0x737d34: r0 = LoadClassIdInstr(r5)
    //     0x737d34: ldur            x0, [x5, #-1]
    //     0x737d38: ubfx            x0, x0, #0xc, #0x14
    // 0x737d3c: mov             x1, x5
    // 0x737d40: r0 = GDT[cid_x0 + 0xd28f]()
    //     0x737d40: movz            x17, #0xd28f
    //     0x737d44: add             lr, x0, x17
    //     0x737d48: ldr             lr, [x21, lr, lsl #3]
    //     0x737d4c: blr             lr
    // 0x737d50: mov             x4, x0
    // 0x737d54: ldur            x3, [fp, #-0x40]
    // 0x737d58: stur            x4, [fp, #-0x48]
    // 0x737d5c: StoreField: r3->field_1f = r0
    //     0x737d5c: stur            w0, [x3, #0x1f]
    //     0x737d60: tbz             w0, #0, #0x737d7c
    //     0x737d64: ldurb           w16, [x3, #-1]
    //     0x737d68: ldurb           w17, [x0, #-1]
    //     0x737d6c: and             x16, x17, x16, lsr #2
    //     0x737d70: tst             x16, HEAP, lsr #32
    //     0x737d74: b.eq            #0x737d7c
    //     0x737d78: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x737d7c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x737d7c: ldur            x0, [x3, #0x17]
    // 0x737d80: add             x1, x0, #1
    // 0x737d84: ArrayStore: r3[0] = r1  ; List_8
    //     0x737d84: stur            x1, [x3, #0x17]
    // 0x737d88: cmp             w4, NULL
    // 0x737d8c: b.ne            #0x737dc0
    // 0x737d90: mov             x0, x4
    // 0x737d94: ldur            x2, [fp, #-0x20]
    // 0x737d98: r1 = Null
    //     0x737d98: mov             x1, NULL
    // 0x737d9c: cmp             w2, NULL
    // 0x737da0: b.eq            #0x737dc0
    // 0x737da4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x737da4: ldur            w4, [x2, #0x17]
    // 0x737da8: DecompressPointer r4
    //     0x737da8: add             x4, x4, HEAP, lsl #32
    // 0x737dac: r8 = X0
    //     0x737dac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x737db0: LoadField: r9 = r4->field_7
    //     0x737db0: ldur            x9, [x4, #7]
    // 0x737db4: r3 = Null
    //     0x737db4: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c528] Null
    //     0x737db8: ldr             x3, [x3, #0x528]
    // 0x737dbc: blr             x9
    // 0x737dc0: ldur            x1, [fp, #-0x48]
    // 0x737dc4: d0 = inf
    //     0x737dc4: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x737dc8: LoadField: d1 = r1->field_1b
    //     0x737dc8: ldur            d1, [x1, #0x1b]
    // 0x737dcc: fcmp            d1, d0
    // 0x737dd0: b.ne            #0x737de0
    // 0x737dd4: LoadField: d1 = r1->field_13
    //     0x737dd4: ldur            d1, [x1, #0x13]
    // 0x737dd8: StoreField: r1->field_1b = d1
    //     0x737dd8: stur            d1, [x1, #0x1b]
    // 0x737ddc: r0 = _increaseGrowthLimitIfNecessary()
    //     0x737ddc: bl              #0x735b30  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridTrack::_increaseGrowthLimitIfNecessary
    // 0x737de0: ldur            x1, [fp, #-0x40]
    // 0x737de4: ldur            x4, [fp, #-0x20]
    // 0x737de8: ldur            x2, [fp, #-0x38]
    // 0x737dec: ldur            x3, [fp, #-0x28]
    // 0x737df0: b               #0x737ce0
    // 0x737df4: mov             x0, x4
    // 0x737df8: StoreField: r0->field_1f = rNULL
    //     0x737df8: stur            NULL, [x0, #0x1f]
    // 0x737dfc: r0 = Null
    //     0x737dfc: mov             x0, NULL
    // 0x737e00: LeaveFrame
    //     0x737e00: mov             SP, fp
    //     0x737e04: ldp             fp, lr, [SP], #0x10
    // 0x737e08: ret
    //     0x737e08: ret             
    // 0x737e0c: r1 = Function '<anonymous closure>':.
    //     0x737e0c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c538] AnonymousClosure: (0x735d5c), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_stretchIntrinsicTracks (0x735870)
    //     0x737e10: ldr             x1, [x1, #0x538]
    // 0x737e14: r2 = Null
    //     0x737e14: mov             x2, NULL
    // 0x737e18: r0 = AllocateClosure()
    //     0x737e18: bl              #0xec1630  ; AllocateClosureStub
    // 0x737e1c: ldur            x1, [fp, #-0x10]
    // 0x737e20: mov             x2, x0
    // 0x737e24: r0 = where()
    //     0x737e24: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x737e28: LoadField: r1 = r0->field_7
    //     0x737e28: ldur            w1, [x0, #7]
    // 0x737e2c: DecompressPointer r1
    //     0x737e2c: add             x1, x1, HEAP, lsl #32
    // 0x737e30: mov             x2, x0
    // 0x737e34: r0 = _List.of()
    //     0x737e34: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x737e38: LoadField: r1 = r0->field_b
    //     0x737e38: ldur            w1, [x0, #0xb]
    // 0x737e3c: cbz             w1, #0x737e58
    // 0x737e40: ldur            x1, [fp, #-8]
    // 0x737e44: ldur            d0, [fp, #-0x50]
    // 0x737e48: mov             x2, x0
    // 0x737e4c: mov             x3, x0
    // 0x737e50: ldur            x5, [fp, #-0x18]
    // 0x737e54: r0 = _distributeFreeSpace()
    //     0x737e54: bl              #0x73687c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::_distributeFreeSpace
    // 0x737e58: r0 = Null
    //     0x737e58: mov             x0, NULL
    // 0x737e5c: LeaveFrame
    //     0x737e5c: mov             SP, fp
    //     0x737e60: ldp             fp, lr, [SP], #0x10
    // 0x737e64: ret
    //     0x737e64: ret             
    // 0x737e68: ldur            x0, [fp, #-0x38]
    // 0x737e6c: r0 = ConcurrentModificationError()
    //     0x737e6c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x737e70: mov             x1, x0
    // 0x737e74: ldur            x0, [fp, #-0x38]
    // 0x737e78: StoreField: r1->field_b = r0
    //     0x737e78: stur            w0, [x1, #0xb]
    // 0x737e7c: mov             x0, x1
    // 0x737e80: r0 = Throw()
    //     0x737e80: bl              #0xec04b8  ; ThrowStub
    // 0x737e84: brk             #0
    // 0x737e88: ldur            x0, [fp, #-0x30]
    // 0x737e8c: r0 = ConcurrentModificationError()
    //     0x737e8c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x737e90: mov             x1, x0
    // 0x737e94: ldur            x0, [fp, #-0x30]
    // 0x737e98: StoreField: r1->field_b = r0
    //     0x737e98: stur            w0, [x1, #0xb]
    // 0x737e9c: mov             x0, x1
    // 0x737ea0: r0 = Throw()
    //     0x737ea0: bl              #0xec04b8  ; ThrowStub
    // 0x737ea4: brk             #0
    // 0x737ea8: r0 = StackOverflowSharedWithFPURegs()
    //     0x737ea8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x737eac: b               #0x737b14
    // 0x737eb0: r0 = StackOverflowSharedWithFPURegs()
    //     0x737eb0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x737eb4: b               #0x737b5c
    // 0x737eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x737eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x737ebc: b               #0x737cec
  }
  [closure] double <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x738544, size: 0x10c
    // 0x738544: EnterFrame
    //     0x738544: stp             fp, lr, [SP, #-0x10]!
    //     0x738548: mov             fp, SP
    // 0x73854c: AllocStack(0x18)
    //     0x73854c: sub             SP, SP, #0x18
    // 0x738550: SetupParameters()
    //     0x738550: ldr             x0, [fp, #0x18]
    //     0x738554: ldur            w3, [x0, #0x17]
    //     0x738558: add             x3, x3, HEAP, lsl #32
    //     0x73855c: stur            x3, [fp, #-0x18]
    // 0x738560: CheckStackOverflow
    //     0x738560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x738564: cmp             SP, x16
    //     0x738568: b.ls            #0x738628
    // 0x73856c: LoadField: r0 = r3->field_b
    //     0x73856c: ldur            w0, [x3, #0xb]
    // 0x738570: DecompressPointer r0
    //     0x738570: add             x0, x0, HEAP, lsl #32
    // 0x738574: LoadField: r4 = r0->field_1b
    //     0x738574: ldur            w4, [x0, #0x1b]
    // 0x738578: DecompressPointer r4
    //     0x738578: add             x4, x4, HEAP, lsl #32
    // 0x73857c: stur            x4, [fp, #-0x10]
    // 0x738580: LoadField: r1 = r0->field_f
    //     0x738580: ldur            w1, [x0, #0xf]
    // 0x738584: DecompressPointer r1
    //     0x738584: add             x1, x1, HEAP, lsl #32
    // 0x738588: LoadField: r0 = r1->field_6f
    //     0x738588: ldur            w0, [x1, #0x6f]
    // 0x73858c: DecompressPointer r0
    //     0x73858c: add             x0, x0, HEAP, lsl #32
    // 0x738590: r16 = Sentinel
    //     0x738590: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x738594: cmp             w0, w16
    // 0x738598: b.eq            #0x738630
    // 0x73859c: LoadField: r5 = r0->field_1b
    //     0x73859c: ldur            w5, [x0, #0x1b]
    // 0x7385a0: DecompressPointer r5
    //     0x7385a0: add             x5, x5, HEAP, lsl #32
    // 0x7385a4: mov             x1, x5
    // 0x7385a8: ldr             x2, [fp, #0x10]
    // 0x7385ac: stur            x5, [fp, #-8]
    // 0x7385b0: r0 = _getValueOrData()
    //     0x7385b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x7385b4: mov             x1, x0
    // 0x7385b8: ldur            x0, [fp, #-8]
    // 0x7385bc: LoadField: r2 = r0->field_f
    //     0x7385bc: ldur            w2, [x0, #0xf]
    // 0x7385c0: DecompressPointer r2
    //     0x7385c0: add             x2, x2, HEAP, lsl #32
    // 0x7385c4: cmp             w2, w1
    // 0x7385c8: b.ne            #0x7385d4
    // 0x7385cc: r2 = Null
    //     0x7385cc: mov             x2, NULL
    // 0x7385d0: b               #0x7385d8
    // 0x7385d4: mov             x2, x1
    // 0x7385d8: ldur            x0, [fp, #-0x18]
    // 0x7385dc: cmp             w2, NULL
    // 0x7385e0: b.eq            #0x73863c
    // 0x7385e4: LoadField: r3 = r0->field_f
    //     0x7385e4: ldur            w3, [x0, #0xf]
    // 0x7385e8: DecompressPointer r3
    //     0x7385e8: add             x3, x3, HEAP, lsl #32
    // 0x7385ec: ldur            x1, [fp, #-0x10]
    // 0x7385f0: r0 = sizeForAreaOnAxis()
    //     0x7385f0: bl              #0x738650  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::sizeForAreaOnAxis
    // 0x7385f4: r0 = inline_Allocate_Double()
    //     0x7385f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x7385f8: add             x0, x0, #0x10
    //     0x7385fc: cmp             x1, x0
    //     0x738600: b.ls            #0x738640
    //     0x738604: str             x0, [THR, #0x50]  ; THR::top
    //     0x738608: sub             x0, x0, #0xf
    //     0x73860c: movz            x1, #0xe15c
    //     0x738610: movk            x1, #0x3, lsl #16
    //     0x738614: stur            x1, [x0, #-1]
    // 0x738618: StoreField: r0->field_7 = d0
    //     0x738618: stur            d0, [x0, #7]
    // 0x73861c: LeaveFrame
    //     0x73861c: mov             SP, fp
    //     0x738620: ldp             fp, lr, [SP], #0x10
    // 0x738624: ret
    //     0x738624: ret             
    // 0x738628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x738628: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73862c: b               #0x73856c
    // 0x738630: r9 = _placementGrid
    //     0x738630: add             x9, PP, #0x4c, lsl #12  ; [pp+0x4c4e0] Field <RenderLayoutGrid._placementGrid@1176515497>: late (offset: 0x70)
    //     0x738634: ldr             x9, [x9, #0x4e0]
    // 0x738638: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x738638: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x73863c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x73863c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x738640: SaveReg d0
    //     0x738640: str             q0, [SP, #-0x10]!
    // 0x738644: r0 = AllocateDouble()
    //     0x738644: bl              #0xec2254  ; AllocateDoubleStub
    // 0x738648: RestoreReg d0
    //     0x738648: ldr             q0, [SP], #0x10
    // 0x73864c: b               #0x738618
  }
  [closure] int <anonymous closure>(dynamic, RenderBox) {
    // ** addr: 0x7387fc, size: 0xe0
    // 0x7387fc: EnterFrame
    //     0x7387fc: stp             fp, lr, [SP, #-0x10]!
    //     0x738800: mov             fp, SP
    // 0x738804: AllocStack(0x10)
    //     0x738804: sub             SP, SP, #0x10
    // 0x738808: SetupParameters()
    //     0x738808: ldr             x0, [fp, #0x18]
    //     0x73880c: ldur            w3, [x0, #0x17]
    //     0x738810: add             x3, x3, HEAP, lsl #32
    //     0x738814: stur            x3, [fp, #-0x10]
    // 0x738818: CheckStackOverflow
    //     0x738818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73881c: cmp             SP, x16
    //     0x738820: b.ls            #0x7388c4
    // 0x738824: LoadField: r0 = r3->field_f
    //     0x738824: ldur            w0, [x3, #0xf]
    // 0x738828: DecompressPointer r0
    //     0x738828: add             x0, x0, HEAP, lsl #32
    // 0x73882c: LoadField: r1 = r0->field_6f
    //     0x73882c: ldur            w1, [x0, #0x6f]
    // 0x738830: DecompressPointer r1
    //     0x738830: add             x1, x1, HEAP, lsl #32
    // 0x738834: r16 = Sentinel
    //     0x738834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x738838: cmp             w1, w16
    // 0x73883c: b.eq            #0x7388cc
    // 0x738840: LoadField: r0 = r1->field_1b
    //     0x738840: ldur            w0, [x1, #0x1b]
    // 0x738844: DecompressPointer r0
    //     0x738844: add             x0, x0, HEAP, lsl #32
    // 0x738848: mov             x1, x0
    // 0x73884c: ldr             x2, [fp, #0x10]
    // 0x738850: stur            x0, [fp, #-8]
    // 0x738854: r0 = _getValueOrData()
    //     0x738854: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x738858: ldur            x2, [fp, #-8]
    // 0x73885c: LoadField: r3 = r2->field_f
    //     0x73885c: ldur            w3, [x2, #0xf]
    // 0x738860: DecompressPointer r3
    //     0x738860: add             x3, x3, HEAP, lsl #32
    // 0x738864: cmp             w3, w0
    // 0x738868: b.ne            #0x738874
    // 0x73886c: r3 = Null
    //     0x73886c: mov             x3, NULL
    // 0x738870: b               #0x738878
    // 0x738874: mov             x3, x0
    // 0x738878: ldur            x2, [fp, #-0x10]
    // 0x73887c: cmp             w3, NULL
    // 0x738880: b.eq            #0x7388d8
    // 0x738884: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x738884: ldur            w4, [x2, #0x17]
    // 0x738888: DecompressPointer r4
    //     0x738888: add             x4, x4, HEAP, lsl #32
    // 0x73888c: r16 = Instance_Axis
    //     0x73888c: ldr             x16, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x738890: cmp             w4, w16
    // 0x738894: b.ne            #0x7388a0
    // 0x738898: LoadField: r2 = r3->field_b
    //     0x738898: ldur            x2, [x3, #0xb]
    // 0x73889c: b               #0x7388a4
    // 0x7388a0: LoadField: r2 = r3->field_13
    //     0x7388a0: ldur            x2, [x3, #0x13]
    // 0x7388a4: r0 = BoxInt64Instr(r2)
    //     0x7388a4: sbfiz           x0, x2, #1, #0x1f
    //     0x7388a8: cmp             x2, x0, asr #1
    //     0x7388ac: b.eq            #0x7388b8
    //     0x7388b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7388b4: stur            x2, [x0, #7]
    // 0x7388b8: LeaveFrame
    //     0x7388b8: mov             SP, fp
    //     0x7388bc: ldp             fp, lr, [SP], #0x10
    // 0x7388c0: ret
    //     0x7388c0: ret             
    // 0x7388c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7388c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7388c8: b               #0x738824
    // 0x7388cc: r9 = _placementGrid
    //     0x7388cc: add             x9, PP, #0x4c, lsl #12  ; [pp+0x4c4e0] Field <RenderLayoutGrid._placementGrid@1176515497>: late (offset: 0x70)
    //     0x7388d0: ldr             x9, [x9, #0x4e0]
    // 0x7388d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7388d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7388d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7388d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x7388dc, size: 0xfc
    // 0x7388dc: EnterFrame
    //     0x7388dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7388e0: mov             fp, SP
    // 0x7388e4: AllocStack(0x10)
    //     0x7388e4: sub             SP, SP, #0x10
    // 0x7388e8: SetupParameters()
    //     0x7388e8: ldr             x0, [fp, #0x18]
    //     0x7388ec: ldur            w3, [x0, #0x17]
    //     0x7388f0: add             x3, x3, HEAP, lsl #32
    //     0x7388f4: stur            x3, [fp, #-0x10]
    // 0x7388f8: CheckStackOverflow
    //     0x7388f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7388fc: cmp             SP, x16
    //     0x738900: b.ls            #0x7389c0
    // 0x738904: LoadField: r0 = r3->field_f
    //     0x738904: ldur            w0, [x3, #0xf]
    // 0x738908: DecompressPointer r0
    //     0x738908: add             x0, x0, HEAP, lsl #32
    // 0x73890c: LoadField: r1 = r0->field_6f
    //     0x73890c: ldur            w1, [x0, #0x6f]
    // 0x738910: DecompressPointer r1
    //     0x738910: add             x1, x1, HEAP, lsl #32
    // 0x738914: r16 = Sentinel
    //     0x738914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x738918: cmp             w1, w16
    // 0x73891c: b.eq            #0x7389c8
    // 0x738920: LoadField: r4 = r1->field_1b
    //     0x738920: ldur            w4, [x1, #0x1b]
    // 0x738924: DecompressPointer r4
    //     0x738924: add             x4, x4, HEAP, lsl #32
    // 0x738928: ldr             x0, [fp, #0x10]
    // 0x73892c: stur            x4, [fp, #-8]
    // 0x738930: r2 = Null
    //     0x738930: mov             x2, NULL
    // 0x738934: r1 = Null
    //     0x738934: mov             x1, NULL
    // 0x738938: r4 = LoadClassIdInstr(r0)
    //     0x738938: ldur            x4, [x0, #-1]
    //     0x73893c: ubfx            x4, x4, #0xc, #0x14
    // 0x738940: sub             x4, x4, #0xbba
    // 0x738944: cmp             x4, #0x9a
    // 0x738948: b.ls            #0x73895c
    // 0x73894c: r8 = RenderBox
    //     0x73894c: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x738950: r3 = Null
    //     0x738950: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c4f0] Null
    //     0x738954: ldr             x3, [x3, #0x4f0]
    // 0x738958: r0 = RenderBox()
    //     0x738958: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x73895c: ldur            x1, [fp, #-8]
    // 0x738960: ldr             x2, [fp, #0x10]
    // 0x738964: r0 = _getValueOrData()
    //     0x738964: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x738968: mov             x1, x0
    // 0x73896c: ldur            x0, [fp, #-8]
    // 0x738970: LoadField: r2 = r0->field_f
    //     0x738970: ldur            w2, [x0, #0xf]
    // 0x738974: DecompressPointer r2
    //     0x738974: add             x2, x2, HEAP, lsl #32
    // 0x738978: cmp             w2, w1
    // 0x73897c: b.ne            #0x738984
    // 0x738980: r1 = Null
    //     0x738980: mov             x1, NULL
    // 0x738984: ldur            x0, [fp, #-0x10]
    // 0x738988: cmp             w1, NULL
    // 0x73898c: b.eq            #0x7389d4
    // 0x738990: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x738990: ldur            w2, [x0, #0x17]
    // 0x738994: DecompressPointer r2
    //     0x738994: add             x2, x2, HEAP, lsl #32
    // 0x738998: r0 = spanForAxis()
    //     0x738998: bl              #0x73879c  ; [package:flutter_layout_grid/src/foundation/placement.dart] GridArea::spanForAxis
    // 0x73899c: mov             x2, x0
    // 0x7389a0: r0 = BoxInt64Instr(r2)
    //     0x7389a0: sbfiz           x0, x2, #1, #0x1f
    //     0x7389a4: cmp             x2, x0, asr #1
    //     0x7389a8: b.eq            #0x7389b4
    //     0x7389ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7389b0: stur            x2, [x0, #7]
    // 0x7389b4: LeaveFrame
    //     0x7389b4: mov             SP, fp
    //     0x7389b8: ldp             fp, lr, [SP], #0x10
    // 0x7389bc: ret
    //     0x7389bc: ret             
    // 0x7389c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7389c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7389c4: b               #0x738904
    // 0x7389c8: r9 = _placementGrid
    //     0x7389c8: add             x9, PP, #0x4c, lsl #12  ; [pp+0x4c4e0] Field <RenderLayoutGrid._placementGrid@1176515497>: late (offset: 0x70)
    //     0x7389cc: ldr             x9, [x9, #0x4e0]
    // 0x7389d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7389d0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7389d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7389d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] List<RenderBox> <anonymous closure>(dynamic, GridTrack) {
    // ** addr: 0x7389d8, size: 0x54
    // 0x7389d8: EnterFrame
    //     0x7389d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7389dc: mov             fp, SP
    // 0x7389e0: ldr             x0, [fp, #0x18]
    // 0x7389e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7389e4: ldur            w1, [x0, #0x17]
    // 0x7389e8: DecompressPointer r1
    //     0x7389e8: add             x1, x1, HEAP, lsl #32
    // 0x7389ec: CheckStackOverflow
    //     0x7389ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7389f0: cmp             SP, x16
    //     0x7389f4: b.ls            #0x738a24
    // 0x7389f8: LoadField: r0 = r1->field_f
    //     0x7389f8: ldur            w0, [x1, #0xf]
    // 0x7389fc: DecompressPointer r0
    //     0x7389fc: add             x0, x0, HEAP, lsl #32
    // 0x738a00: LoadField: r2 = r1->field_13
    //     0x738a00: ldur            w2, [x1, #0x13]
    // 0x738a04: DecompressPointer r2
    //     0x738a04: add             x2, x2, HEAP, lsl #32
    // 0x738a08: ldr             x1, [fp, #0x10]
    // 0x738a0c: LoadField: r3 = r1->field_7
    //     0x738a0c: ldur            x3, [x1, #7]
    // 0x738a10: mov             x1, x0
    // 0x738a14: r0 = getChildrenInTrack()
    //     0x738a14: bl              #0x738a2c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::getChildrenInTrack
    // 0x738a18: LeaveFrame
    //     0x738a18: mov             SP, fp
    //     0x738a1c: ldp             fp, lr, [SP], #0x10
    // 0x738a20: ret
    //     0x738a20: ret             
    // 0x738a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x738a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x738a28: b               #0x7389f8
  }
  _ getChildrenInTrack(/* No info */) {
    // ** addr: 0x738a2c, size: 0xb4
    // 0x738a2c: EnterFrame
    //     0x738a2c: stp             fp, lr, [SP, #-0x10]!
    //     0x738a30: mov             fp, SP
    // 0x738a34: AllocStack(0x20)
    //     0x738a34: sub             SP, SP, #0x20
    // 0x738a38: SetupParameters(dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r2 */)
    //     0x738a38: mov             x16, x3
    //     0x738a3c: mov             x3, x2
    //     0x738a40: mov             x2, x16
    // 0x738a44: CheckStackOverflow
    //     0x738a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x738a48: cmp             SP, x16
    //     0x738a4c: b.ls            #0x738acc
    // 0x738a50: LoadField: r0 = r1->field_6f
    //     0x738a50: ldur            w0, [x1, #0x6f]
    // 0x738a54: DecompressPointer r0
    //     0x738a54: add             x0, x0, HEAP, lsl #32
    // 0x738a58: r16 = Sentinel
    //     0x738a58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x738a5c: cmp             w0, w16
    // 0x738a60: b.eq            #0x738ad4
    // 0x738a64: mov             x1, x0
    // 0x738a68: r0 = getCellsInTrack()
    //     0x738a68: bl              #0x738ae0  ; [package:flutter_layout_grid/src/rendering/placement.dart] PlacementGrid::getCellsInTrack
    // 0x738a6c: r1 = Function '<anonymous closure>':.
    //     0x738a6c: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c500] Function: [dart:async] _BufferingStreamSubscription::_onDone (0xc1f1bc)
    //     0x738a70: ldr             x1, [x1, #0x500]
    // 0x738a74: r2 = Null
    //     0x738a74: mov             x2, NULL
    // 0x738a78: stur            x0, [fp, #-8]
    // 0x738a7c: r0 = AllocateClosure()
    //     0x738a7c: bl              #0xec1630  ; AllocateClosureStub
    // 0x738a80: r16 = <RenderBox>
    //     0x738a80: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x738a84: ldr             x16, [x16, #0x1d8]
    // 0x738a88: ldur            lr, [fp, #-8]
    // 0x738a8c: stp             lr, x16, [SP, #8]
    // 0x738a90: str             x0, [SP]
    // 0x738a94: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x738a94: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x738a98: r0 = expand()
    //     0x738a98: bl              #0x6a0158  ; [dart:core] Iterable::expand
    // 0x738a9c: r16 = <RenderBox>
    //     0x738a9c: add             x16, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x738aa0: ldr             x16, [x16, #0x1d8]
    // 0x738aa4: stp             x0, x16, [SP]
    // 0x738aa8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x738aa8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x738aac: r0 = IterableExt.removeDuplicates()
    //     0x738aac: bl              #0x7381dc  ; [package:flutter_layout_grid/src/foundation/collections.dart] ::IterableExt.removeDuplicates
    // 0x738ab0: LoadField: r1 = r0->field_7
    //     0x738ab0: ldur            w1, [x0, #7]
    // 0x738ab4: DecompressPointer r1
    //     0x738ab4: add             x1, x1, HEAP, lsl #32
    // 0x738ab8: mov             x2, x0
    // 0x738abc: r0 = _List.of()
    //     0x738abc: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x738ac0: LeaveFrame
    //     0x738ac0: mov             SP, fp
    //     0x738ac4: ldp             fp, lr, [SP], #0x10
    // 0x738ac8: ret
    //     0x738ac8: ret             
    // 0x738acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x738acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x738ad0: b               #0x738a50
    // 0x738ad4: r9 = _placementGrid
    //     0x738ad4: add             x9, PP, #0x4c, lsl #12  ; [pp+0x4c4e0] Field <RenderLayoutGrid._placementGrid@1176515497>: late (offset: 0x70)
    //     0x738ad8: ldr             x9, [x9, #0x4e0]
    // 0x738adc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x738adc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ performItemPlacement(/* No info */) {
    // ** addr: 0x7396d0, size: 0x74
    // 0x7396d0: EnterFrame
    //     0x7396d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7396d4: mov             fp, SP
    // 0x7396d8: AllocStack(0x8)
    //     0x7396d8: sub             SP, SP, #8
    // 0x7396dc: SetupParameters(RenderLayoutGrid this /* r1 => r0, fp-0x8 */)
    //     0x7396dc: mov             x0, x1
    //     0x7396e0: stur            x1, [fp, #-8]
    // 0x7396e4: CheckStackOverflow
    //     0x7396e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7396e8: cmp             SP, x16
    //     0x7396ec: b.ls            #0x73973c
    // 0x7396f0: LoadField: r1 = r0->field_6b
    //     0x7396f0: ldur            w1, [x0, #0x6b]
    // 0x7396f4: DecompressPointer r1
    //     0x7396f4: add             x1, x1, HEAP, lsl #32
    // 0x7396f8: tbnz            w1, #4, #0x73972c
    // 0x7396fc: r1 = false
    //     0x7396fc: add             x1, NULL, #0x30  ; false
    // 0x739700: StoreField: r0->field_6b = r1
    //     0x739700: stur            w1, [x0, #0x6b]
    // 0x739704: mov             x1, x0
    // 0x739708: r0 = computeItemPlacement()
    //     0x739708: bl              #0x739744  ; [package:flutter_layout_grid/src/rendering/placement.dart] ::computeItemPlacement
    // 0x73970c: ldur            x1, [fp, #-8]
    // 0x739710: StoreField: r1->field_6f = r0
    //     0x739710: stur            w0, [x1, #0x6f]
    //     0x739714: ldurb           w16, [x1, #-1]
    //     0x739718: ldurb           w17, [x0, #-1]
    //     0x73971c: and             x16, x17, x16, lsr #2
    //     0x739720: tst             x16, HEAP, lsr #32
    //     0x739724: b.eq            #0x73972c
    //     0x739728: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x73972c: r0 = Null
    //     0x73972c: mov             x0, NULL
    // 0x739730: LeaveFrame
    //     0x739730: mov             SP, fp
    //     0x739734: ldp             fp, lr, [SP], #0x10
    // 0x739738: ret
    //     0x739738: ret             
    // 0x73973c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73973c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x739740: b               #0x7396f0
  }
  dynamic computeMinIntrinsicHeight(dynamic) {
    // ** addr: 0x74ad64, size: 0x24
    // 0x74ad64: EnterFrame
    //     0x74ad64: stp             fp, lr, [SP, #-0x10]!
    //     0x74ad68: mov             fp, SP
    // 0x74ad6c: ldr             x2, [fp, #0x10]
    // 0x74ad70: r1 = Function 'computeMinIntrinsicHeight':.
    //     0x74ad70: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e60] AnonymousClosure: (0x74ad88), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMinIntrinsicHeight (0x74adfc)
    //     0x74ad74: ldr             x1, [x1, #0xe60]
    // 0x74ad78: r0 = AllocateClosure()
    //     0x74ad78: bl              #0xec1630  ; AllocateClosureStub
    // 0x74ad7c: LeaveFrame
    //     0x74ad7c: mov             SP, fp
    //     0x74ad80: ldp             fp, lr, [SP], #0x10
    // 0x74ad84: ret
    //     0x74ad84: ret             
  }
  [closure] double computeMinIntrinsicHeight(dynamic, double) {
    // ** addr: 0x74ad88, size: 0x74
    // 0x74ad88: EnterFrame
    //     0x74ad88: stp             fp, lr, [SP, #-0x10]!
    //     0x74ad8c: mov             fp, SP
    // 0x74ad90: ldr             x0, [fp, #0x18]
    // 0x74ad94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x74ad94: ldur            w1, [x0, #0x17]
    // 0x74ad98: DecompressPointer r1
    //     0x74ad98: add             x1, x1, HEAP, lsl #32
    // 0x74ad9c: CheckStackOverflow
    //     0x74ad9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ada0: cmp             SP, x16
    //     0x74ada4: b.ls            #0x74ade4
    // 0x74ada8: ldr             x2, [fp, #0x10]
    // 0x74adac: r0 = computeMinIntrinsicHeight()
    //     0x74adac: bl              #0x74adfc  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMinIntrinsicHeight
    // 0x74adb0: r0 = inline_Allocate_Double()
    //     0x74adb0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74adb4: add             x0, x0, #0x10
    //     0x74adb8: cmp             x1, x0
    //     0x74adbc: b.ls            #0x74adec
    //     0x74adc0: str             x0, [THR, #0x50]  ; THR::top
    //     0x74adc4: sub             x0, x0, #0xf
    //     0x74adc8: movz            x1, #0xe15c
    //     0x74adcc: movk            x1, #0x3, lsl #16
    //     0x74add0: stur            x1, [x0, #-1]
    // 0x74add4: StoreField: r0->field_7 = d0
    //     0x74add4: stur            d0, [x0, #7]
    // 0x74add8: LeaveFrame
    //     0x74add8: mov             SP, fp
    //     0x74addc: ldp             fp, lr, [SP], #0x10
    // 0x74ade0: ret
    //     0x74ade0: ret             
    // 0x74ade4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ade4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ade8: b               #0x74ada8
    // 0x74adec: SaveReg d0
    //     0x74adec: str             q0, [SP, #-0x10]!
    // 0x74adf0: r0 = AllocateDouble()
    //     0x74adf0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x74adf4: RestoreReg d0
    //     0x74adf4: ldr             q0, [SP], #0x10
    // 0x74adf8: b               #0x74add4
  }
  _ computeMinIntrinsicHeight(/* No info */) {
    // ** addr: 0x74adfc, size: 0x68
    // 0x74adfc: EnterFrame
    //     0x74adfc: stp             fp, lr, [SP, #-0x10]!
    //     0x74ae00: mov             fp, SP
    // 0x74ae04: AllocStack(0x10)
    //     0x74ae04: sub             SP, SP, #0x10
    // 0x74ae08: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */)
    //     0x74ae08: stur            x1, [fp, #-8]
    // 0x74ae0c: CheckStackOverflow
    //     0x74ae0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ae10: cmp             SP, x16
    //     0x74ae14: b.ls            #0x74ae5c
    // 0x74ae18: LoadField: d0 = r2->field_7
    //     0x74ae18: ldur            d0, [x2, #7]
    // 0x74ae1c: stur            d0, [fp, #-0x10]
    // 0x74ae20: r0 = BoxConstraints()
    //     0x74ae20: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x74ae24: ldur            d0, [fp, #-0x10]
    // 0x74ae28: StoreField: r0->field_7 = d0
    //     0x74ae28: stur            d0, [x0, #7]
    // 0x74ae2c: StoreField: r0->field_f = d0
    //     0x74ae2c: stur            d0, [x0, #0xf]
    // 0x74ae30: ArrayStore: r0[0] = rZR  ; List_8
    //     0x74ae30: stur            xzr, [x0, #0x17]
    // 0x74ae34: d0 = inf
    //     0x74ae34: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x74ae38: StoreField: r0->field_1f = d0
    //     0x74ae38: stur            d0, [x0, #0x1f]
    // 0x74ae3c: ldur            x1, [fp, #-8]
    // 0x74ae40: mov             x2, x0
    // 0x74ae44: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x74ae44: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x74ae48: r0 = computeGridSize()
    //     0x74ae48: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x74ae4c: LoadField: d0 = r0->field_37
    //     0x74ae4c: ldur            d0, [x0, #0x37]
    // 0x74ae50: LeaveFrame
    //     0x74ae50: mov             SP, fp
    //     0x74ae54: ldp             fp, lr, [SP], #0x10
    // 0x74ae58: ret
    //     0x74ae58: ret             
    // 0x74ae5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74ae5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74ae60: b               #0x74ae18
  }
  _ computeDistanceToActualBaseline(/* No info */) {
    // ** addr: 0x74d6d8, size: 0x2c
    // 0x74d6d8: EnterFrame
    //     0x74d6d8: stp             fp, lr, [SP, #-0x10]!
    //     0x74d6dc: mov             fp, SP
    // 0x74d6e0: CheckStackOverflow
    //     0x74d6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d6e4: cmp             SP, x16
    //     0x74d6e8: b.ls            #0x74d6fc
    // 0x74d6ec: r0 = defaultComputeDistanceToHighestActualBaseline()
    //     0x74d6ec: bl              #0x74d704  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultComputeDistanceToHighestActualBaseline
    // 0x74d6f0: LeaveFrame
    //     0x74d6f0: mov             SP, fp
    //     0x74d6f4: ldp             fp, lr, [SP], #0x10
    // 0x74d6f8: ret
    //     0x74d6f8: ret             
    // 0x74d6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d6fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d700: b               #0x74d6ec
  }
  dynamic computeMaxIntrinsicWidth(dynamic) {
    // ** addr: 0x7508fc, size: 0x24
    // 0x7508fc: EnterFrame
    //     0x7508fc: stp             fp, lr, [SP, #-0x10]!
    //     0x750900: mov             fp, SP
    // 0x750904: ldr             x2, [fp, #0x10]
    // 0x750908: r1 = Function 'computeMaxIntrinsicWidth':.
    //     0x750908: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e68] AnonymousClosure: (0x750920), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMaxIntrinsicWidth (0x750994)
    //     0x75090c: ldr             x1, [x1, #0xe68]
    // 0x750910: r0 = AllocateClosure()
    //     0x750910: bl              #0xec1630  ; AllocateClosureStub
    // 0x750914: LeaveFrame
    //     0x750914: mov             SP, fp
    //     0x750918: ldp             fp, lr, [SP], #0x10
    // 0x75091c: ret
    //     0x75091c: ret             
  }
  [closure] double computeMaxIntrinsicWidth(dynamic, double) {
    // ** addr: 0x750920, size: 0x74
    // 0x750920: EnterFrame
    //     0x750920: stp             fp, lr, [SP, #-0x10]!
    //     0x750924: mov             fp, SP
    // 0x750928: ldr             x0, [fp, #0x18]
    // 0x75092c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x75092c: ldur            w1, [x0, #0x17]
    // 0x750930: DecompressPointer r1
    //     0x750930: add             x1, x1, HEAP, lsl #32
    // 0x750934: CheckStackOverflow
    //     0x750934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x750938: cmp             SP, x16
    //     0x75093c: b.ls            #0x75097c
    // 0x750940: ldr             x2, [fp, #0x10]
    // 0x750944: r0 = computeMaxIntrinsicWidth()
    //     0x750944: bl              #0x750994  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMaxIntrinsicWidth
    // 0x750948: r0 = inline_Allocate_Double()
    //     0x750948: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x75094c: add             x0, x0, #0x10
    //     0x750950: cmp             x1, x0
    //     0x750954: b.ls            #0x750984
    //     0x750958: str             x0, [THR, #0x50]  ; THR::top
    //     0x75095c: sub             x0, x0, #0xf
    //     0x750960: movz            x1, #0xe15c
    //     0x750964: movk            x1, #0x3, lsl #16
    //     0x750968: stur            x1, [x0, #-1]
    // 0x75096c: StoreField: r0->field_7 = d0
    //     0x75096c: stur            d0, [x0, #7]
    // 0x750970: LeaveFrame
    //     0x750970: mov             SP, fp
    //     0x750974: ldp             fp, lr, [SP], #0x10
    // 0x750978: ret
    //     0x750978: ret             
    // 0x75097c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75097c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x750980: b               #0x750940
    // 0x750984: SaveReg d0
    //     0x750984: str             q0, [SP, #-0x10]!
    // 0x750988: r0 = AllocateDouble()
    //     0x750988: bl              #0xec2254  ; AllocateDoubleStub
    // 0x75098c: RestoreReg d0
    //     0x75098c: ldr             q0, [SP], #0x10
    // 0x750990: b               #0x75096c
  }
  _ computeMaxIntrinsicWidth(/* No info */) {
    // ** addr: 0x750994, size: 0x68
    // 0x750994: EnterFrame
    //     0x750994: stp             fp, lr, [SP, #-0x10]!
    //     0x750998: mov             fp, SP
    // 0x75099c: AllocStack(0x10)
    //     0x75099c: sub             SP, SP, #0x10
    // 0x7509a0: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7509a0: stur            x1, [fp, #-8]
    //     0x7509a4: stur            x2, [fp, #-0x10]
    // 0x7509a8: CheckStackOverflow
    //     0x7509a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7509ac: cmp             SP, x16
    //     0x7509b0: b.ls            #0x7509f4
    // 0x7509b4: r0 = BoxConstraints()
    //     0x7509b4: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x7509b8: StoreField: r0->field_7 = rZR
    //     0x7509b8: stur            xzr, [x0, #7]
    // 0x7509bc: d0 = inf
    //     0x7509bc: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x7509c0: StoreField: r0->field_f = d0
    //     0x7509c0: stur            d0, [x0, #0xf]
    // 0x7509c4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x7509c4: stur            xzr, [x0, #0x17]
    // 0x7509c8: ldur            x1, [fp, #-0x10]
    // 0x7509cc: LoadField: d0 = r1->field_7
    //     0x7509cc: ldur            d0, [x1, #7]
    // 0x7509d0: StoreField: r0->field_1f = d0
    //     0x7509d0: stur            d0, [x0, #0x1f]
    // 0x7509d4: ldur            x1, [fp, #-8]
    // 0x7509d8: mov             x2, x0
    // 0x7509dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7509dc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7509e0: r0 = computeGridSize()
    //     0x7509e0: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x7509e4: LoadField: d0 = r0->field_3f
    //     0x7509e4: ldur            d0, [x0, #0x3f]
    // 0x7509e8: LeaveFrame
    //     0x7509e8: mov             SP, fp
    //     0x7509ec: ldp             fp, lr, [SP], #0x10
    // 0x7509f0: ret
    //     0x7509f0: ret             
    // 0x7509f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7509f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7509f8: b               #0x7509b4
  }
  dynamic computeMaxIntrinsicHeight(dynamic) {
    // ** addr: 0x753ca4, size: 0x24
    // 0x753ca4: EnterFrame
    //     0x753ca4: stp             fp, lr, [SP, #-0x10]!
    //     0x753ca8: mov             fp, SP
    // 0x753cac: ldr             x2, [fp, #0x10]
    // 0x753cb0: r1 = Function 'computeMaxIntrinsicHeight':.
    //     0x753cb0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e58] AnonymousClosure: (0x753cc8), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMaxIntrinsicHeight (0x753d3c)
    //     0x753cb4: ldr             x1, [x1, #0xe58]
    // 0x753cb8: r0 = AllocateClosure()
    //     0x753cb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x753cbc: LeaveFrame
    //     0x753cbc: mov             SP, fp
    //     0x753cc0: ldp             fp, lr, [SP], #0x10
    // 0x753cc4: ret
    //     0x753cc4: ret             
  }
  [closure] double computeMaxIntrinsicHeight(dynamic, double) {
    // ** addr: 0x753cc8, size: 0x74
    // 0x753cc8: EnterFrame
    //     0x753cc8: stp             fp, lr, [SP, #-0x10]!
    //     0x753ccc: mov             fp, SP
    // 0x753cd0: ldr             x0, [fp, #0x18]
    // 0x753cd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x753cd4: ldur            w1, [x0, #0x17]
    // 0x753cd8: DecompressPointer r1
    //     0x753cd8: add             x1, x1, HEAP, lsl #32
    // 0x753cdc: CheckStackOverflow
    //     0x753cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753ce0: cmp             SP, x16
    //     0x753ce4: b.ls            #0x753d24
    // 0x753ce8: ldr             x2, [fp, #0x10]
    // 0x753cec: r0 = computeMaxIntrinsicHeight()
    //     0x753cec: bl              #0x753d3c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeMaxIntrinsicHeight
    // 0x753cf0: r0 = inline_Allocate_Double()
    //     0x753cf0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x753cf4: add             x0, x0, #0x10
    //     0x753cf8: cmp             x1, x0
    //     0x753cfc: b.ls            #0x753d2c
    //     0x753d00: str             x0, [THR, #0x50]  ; THR::top
    //     0x753d04: sub             x0, x0, #0xf
    //     0x753d08: movz            x1, #0xe15c
    //     0x753d0c: movk            x1, #0x3, lsl #16
    //     0x753d10: stur            x1, [x0, #-1]
    // 0x753d14: StoreField: r0->field_7 = d0
    //     0x753d14: stur            d0, [x0, #7]
    // 0x753d18: LeaveFrame
    //     0x753d18: mov             SP, fp
    //     0x753d1c: ldp             fp, lr, [SP], #0x10
    // 0x753d20: ret
    //     0x753d20: ret             
    // 0x753d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753d28: b               #0x753ce8
    // 0x753d2c: SaveReg d0
    //     0x753d2c: str             q0, [SP, #-0x10]!
    // 0x753d30: r0 = AllocateDouble()
    //     0x753d30: bl              #0xec2254  ; AllocateDoubleStub
    // 0x753d34: RestoreReg d0
    //     0x753d34: ldr             q0, [SP], #0x10
    // 0x753d38: b               #0x753d14
  }
  _ computeMaxIntrinsicHeight(/* No info */) {
    // ** addr: 0x753d3c, size: 0x68
    // 0x753d3c: EnterFrame
    //     0x753d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x753d40: mov             fp, SP
    // 0x753d44: AllocStack(0x10)
    //     0x753d44: sub             SP, SP, #0x10
    // 0x753d48: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x753d48: stur            x1, [fp, #-8]
    //     0x753d4c: stur            x2, [fp, #-0x10]
    // 0x753d50: CheckStackOverflow
    //     0x753d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x753d54: cmp             SP, x16
    //     0x753d58: b.ls            #0x753d9c
    // 0x753d5c: r0 = BoxConstraints()
    //     0x753d5c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x753d60: StoreField: r0->field_7 = rZR
    //     0x753d60: stur            xzr, [x0, #7]
    // 0x753d64: ldur            x1, [fp, #-0x10]
    // 0x753d68: LoadField: d0 = r1->field_7
    //     0x753d68: ldur            d0, [x1, #7]
    // 0x753d6c: StoreField: r0->field_f = d0
    //     0x753d6c: stur            d0, [x0, #0xf]
    // 0x753d70: ArrayStore: r0[0] = rZR  ; List_8
    //     0x753d70: stur            xzr, [x0, #0x17]
    // 0x753d74: d0 = inf
    //     0x753d74: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0x753d78: StoreField: r0->field_1f = d0
    //     0x753d78: stur            d0, [x0, #0x1f]
    // 0x753d7c: ldur            x1, [fp, #-8]
    // 0x753d80: mov             x2, x0
    // 0x753d84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x753d84: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x753d88: r0 = computeGridSize()
    //     0x753d88: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x753d8c: LoadField: d0 = r0->field_47
    //     0x753d8c: ldur            d0, [x0, #0x47]
    // 0x753d90: LeaveFrame
    //     0x753d90: mov             SP, fp
    //     0x753d94: ldp             fp, lr, [SP], #0x10
    // 0x753d98: ret
    //     0x753d98: ret             
    // 0x753d9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x753d9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x753da0: b               #0x753d5c
  }
  _ computeDryLayout(/* No info */) {
    // ** addr: 0x7582f8, size: 0x48
    // 0x7582f8: EnterFrame
    //     0x7582f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7582fc: mov             fp, SP
    // 0x758300: CheckStackOverflow
    //     0x758300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x758304: cmp             SP, x16
    //     0x758308: b.ls            #0x758334
    // 0x75830c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x75830c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x758310: r0 = computeGridSize()
    //     0x758310: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x758314: LoadField: r1 = r0->field_7
    //     0x758314: ldur            w1, [x0, #7]
    // 0x758318: DecompressPointer r1
    //     0x758318: add             x1, x1, HEAP, lsl #32
    // 0x75831c: cmp             w1, NULL
    // 0x758320: b.eq            #0x75833c
    // 0x758324: mov             x0, x1
    // 0x758328: LeaveFrame
    //     0x758328: mov             SP, fp
    //     0x75832c: ldp             fp, lr, [SP], #0x10
    // 0x758330: ret
    //     0x758330: ret             
    // 0x758334: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x758334: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x758338: b               #0x75830c
    // 0x75833c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x75833c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x77a530, size: 0x2d8
    // 0x77a530: EnterFrame
    //     0x77a530: stp             fp, lr, [SP, #-0x10]!
    //     0x77a534: mov             fp, SP
    // 0x77a538: AllocStack(0x60)
    //     0x77a538: sub             SP, SP, #0x60
    // 0x77a53c: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x10 */)
    //     0x77a53c: mov             x3, x1
    //     0x77a540: stur            x1, [fp, #-0x10]
    // 0x77a544: CheckStackOverflow
    //     0x77a544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77a548: cmp             SP, x16
    //     0x77a54c: b.ls            #0x77a7e4
    // 0x77a550: LoadField: r4 = r3->field_27
    //     0x77a550: ldur            w4, [x3, #0x27]
    // 0x77a554: DecompressPointer r4
    //     0x77a554: add             x4, x4, HEAP, lsl #32
    // 0x77a558: stur            x4, [fp, #-8]
    // 0x77a55c: cmp             w4, NULL
    // 0x77a560: b.eq            #0x77a7c8
    // 0x77a564: mov             x0, x4
    // 0x77a568: r2 = Null
    //     0x77a568: mov             x2, NULL
    // 0x77a56c: r1 = Null
    //     0x77a56c: mov             x1, NULL
    // 0x77a570: r4 = LoadClassIdInstr(r0)
    //     0x77a570: ldur            x4, [x0, #-1]
    //     0x77a574: ubfx            x4, x4, #0xc, #0x14
    // 0x77a578: sub             x4, x4, #0xc83
    // 0x77a57c: cmp             x4, #1
    // 0x77a580: b.ls            #0x77a594
    // 0x77a584: r8 = BoxConstraints
    //     0x77a584: ldr             x8, [PP, #0x4948]  ; [pp+0x4948] Type: BoxConstraints
    // 0x77a588: r3 = Null
    //     0x77a588: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c668] Null
    //     0x77a58c: ldr             x3, [x3, #0x668]
    // 0x77a590: r0 = BoxConstraints()
    //     0x77a590: bl              #0x65dc1c  ; IsType_BoxConstraints_Stub
    // 0x77a594: ldur            x1, [fp, #-0x10]
    // 0x77a598: ldur            x2, [fp, #-8]
    // 0x77a59c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x77a59c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x77a5a0: r0 = computeGridSize()
    //     0x77a5a0: bl              #0x735310  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::computeGridSize
    // 0x77a5a4: mov             x3, x0
    // 0x77a5a8: stur            x3, [fp, #-0x20]
    // 0x77a5ac: LoadField: r0 = r3->field_7
    //     0x77a5ac: ldur            w0, [x3, #7]
    // 0x77a5b0: DecompressPointer r0
    //     0x77a5b0: add             x0, x0, HEAP, lsl #32
    // 0x77a5b4: cmp             w0, NULL
    // 0x77a5b8: b.eq            #0x77a7ec
    // 0x77a5bc: ldur            x4, [fp, #-0x10]
    // 0x77a5c0: StoreField: r4->field_53 = r0
    //     0x77a5c0: stur            w0, [x4, #0x53]
    //     0x77a5c4: ldurb           w16, [x4, #-1]
    //     0x77a5c8: ldurb           w17, [x0, #-1]
    //     0x77a5cc: and             x16, x17, x16, lsr #2
    //     0x77a5d0: tst             x16, HEAP, lsr #32
    //     0x77a5d4: b.eq            #0x77a5dc
    //     0x77a5d8: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x77a5dc: LoadField: r0 = r4->field_5f
    //     0x77a5dc: ldur            w0, [x4, #0x5f]
    // 0x77a5e0: DecompressPointer r0
    //     0x77a5e0: add             x0, x0, HEAP, lsl #32
    // 0x77a5e4: mov             x5, x0
    // 0x77a5e8: stur            x5, [fp, #-0x18]
    // 0x77a5ec: CheckStackOverflow
    //     0x77a5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x77a5f0: cmp             SP, x16
    //     0x77a5f4: b.ls            #0x77a7f0
    // 0x77a5f8: cmp             w5, NULL
    // 0x77a5fc: b.eq            #0x77a7b8
    // 0x77a600: LoadField: r6 = r5->field_7
    //     0x77a600: ldur            w6, [x5, #7]
    // 0x77a604: DecompressPointer r6
    //     0x77a604: add             x6, x6, HEAP, lsl #32
    // 0x77a608: mov             x0, x6
    // 0x77a60c: stur            x6, [fp, #-8]
    // 0x77a610: r2 = Null
    //     0x77a610: mov             x2, NULL
    // 0x77a614: r1 = Null
    //     0x77a614: mov             x1, NULL
    // 0x77a618: r4 = LoadClassIdInstr(r0)
    //     0x77a618: ldur            x4, [x0, #-1]
    //     0x77a61c: ubfx            x4, x4, #0xc, #0x14
    // 0x77a620: cmp             x4, #0xc75
    // 0x77a624: b.eq            #0x77a63c
    // 0x77a628: r8 = GridParentData
    //     0x77a628: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x77a62c: ldr             x8, [x8, #0xca0]
    // 0x77a630: r3 = Null
    //     0x77a630: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c678] Null
    //     0x77a634: ldr             x3, [x3, #0x678]
    // 0x77a638: r0 = DefaultTypeTest()
    //     0x77a638: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x77a63c: ldur            x0, [fp, #-0x10]
    // 0x77a640: LoadField: r1 = r0->field_6f
    //     0x77a640: ldur            w1, [x0, #0x6f]
    // 0x77a644: DecompressPointer r1
    //     0x77a644: add             x1, x1, HEAP, lsl #32
    // 0x77a648: r16 = Sentinel
    //     0x77a648: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x77a64c: cmp             w1, w16
    // 0x77a650: b.eq            #0x77a7f8
    // 0x77a654: LoadField: r3 = r1->field_1b
    //     0x77a654: ldur            w3, [x1, #0x1b]
    // 0x77a658: DecompressPointer r3
    //     0x77a658: add             x3, x3, HEAP, lsl #32
    // 0x77a65c: mov             x1, x3
    // 0x77a660: ldur            x2, [fp, #-0x18]
    // 0x77a664: stur            x3, [fp, #-0x28]
    // 0x77a668: r0 = _getValueOrData()
    //     0x77a668: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x77a66c: mov             x1, x0
    // 0x77a670: ldur            x0, [fp, #-0x28]
    // 0x77a674: LoadField: r2 = r0->field_f
    //     0x77a674: ldur            w2, [x0, #0xf]
    // 0x77a678: DecompressPointer r2
    //     0x77a678: add             x2, x2, HEAP, lsl #32
    // 0x77a67c: cmp             w2, w1
    // 0x77a680: b.ne            #0x77a68c
    // 0x77a684: r4 = Null
    //     0x77a684: mov             x4, NULL
    // 0x77a688: b               #0x77a690
    // 0x77a68c: mov             x4, x1
    // 0x77a690: ldur            x0, [fp, #-0x18]
    // 0x77a694: ldur            x3, [fp, #-8]
    // 0x77a698: stur            x4, [fp, #-0x28]
    // 0x77a69c: cmp             w4, NULL
    // 0x77a6a0: b.eq            #0x77a804
    // 0x77a6a4: ldur            x1, [fp, #-0x20]
    // 0x77a6a8: mov             x2, x4
    // 0x77a6ac: r0 = offsetForArea()
    //     0x77a6ac: bl              #0x77a808  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::offsetForArea
    // 0x77a6b0: ldur            x1, [fp, #-0x20]
    // 0x77a6b4: ldur            x2, [fp, #-0x28]
    // 0x77a6b8: r3 = Instance_Axis
    //     0x77a6b8: ldr             x3, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0x77a6bc: stur            x0, [fp, #-0x30]
    // 0x77a6c0: r0 = sizeForAreaOnAxis()
    //     0x77a6c0: bl              #0x738650  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::sizeForAreaOnAxis
    // 0x77a6c4: ldur            x1, [fp, #-0x20]
    // 0x77a6c8: ldur            x2, [fp, #-0x28]
    // 0x77a6cc: r3 = Instance_Axis
    //     0x77a6cc: ldr             x3, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0x77a6d0: stur            d0, [fp, #-0x38]
    // 0x77a6d4: r0 = sizeForAreaOnAxis()
    //     0x77a6d4: bl              #0x738650  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] GridSizingInfo::sizeForAreaOnAxis
    // 0x77a6d8: ldur            x0, [fp, #-0x30]
    // 0x77a6dc: LoadField: d1 = r0->field_7
    //     0x77a6dc: ldur            d1, [x0, #7]
    // 0x77a6e0: stur            d1, [fp, #-0x50]
    // 0x77a6e4: LoadField: d2 = r0->field_f
    //     0x77a6e4: ldur            d2, [x0, #0xf]
    // 0x77a6e8: ldur            d3, [fp, #-0x38]
    // 0x77a6ec: stur            d2, [fp, #-0x48]
    // 0x77a6f0: fadd            d4, d1, d3
    // 0x77a6f4: stur            d4, [fp, #-0x40]
    // 0x77a6f8: fadd            d3, d2, d0
    // 0x77a6fc: stur            d3, [fp, #-0x38]
    // 0x77a700: r0 = Offset()
    //     0x77a700: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x77a704: ldur            d0, [fp, #-0x50]
    // 0x77a708: StoreField: r0->field_7 = d0
    //     0x77a708: stur            d0, [x0, #7]
    // 0x77a70c: ldur            d1, [fp, #-0x48]
    // 0x77a710: StoreField: r0->field_f = d1
    //     0x77a710: stur            d1, [x0, #0xf]
    // 0x77a714: ldur            x1, [fp, #-8]
    // 0x77a718: StoreField: r1->field_7 = r0
    //     0x77a718: stur            w0, [x1, #7]
    //     0x77a71c: ldurb           w16, [x1, #-1]
    //     0x77a720: ldurb           w17, [x0, #-1]
    //     0x77a724: and             x16, x17, x16, lsr #2
    //     0x77a728: tst             x16, HEAP, lsr #32
    //     0x77a72c: b.eq            #0x77a734
    //     0x77a730: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x77a734: ldur            d2, [fp, #-0x40]
    // 0x77a738: fsub            d3, d2, d0
    // 0x77a73c: ldur            d0, [fp, #-0x38]
    // 0x77a740: stur            d3, [fp, #-0x58]
    // 0x77a744: fsub            d2, d0, d1
    // 0x77a748: stur            d2, [fp, #-0x40]
    // 0x77a74c: r0 = BoxConstraints()
    //     0x77a74c: bl              #0x65dc10  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x77a750: StoreField: r0->field_7 = rZR
    //     0x77a750: stur            xzr, [x0, #7]
    // 0x77a754: ldur            d0, [fp, #-0x58]
    // 0x77a758: StoreField: r0->field_f = d0
    //     0x77a758: stur            d0, [x0, #0xf]
    // 0x77a75c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x77a75c: stur            xzr, [x0, #0x17]
    // 0x77a760: ldur            d0, [fp, #-0x40]
    // 0x77a764: StoreField: r0->field_1f = d0
    //     0x77a764: stur            d0, [x0, #0x1f]
    // 0x77a768: ldur            x1, [fp, #-0x18]
    // 0x77a76c: r2 = LoadClassIdInstr(r1)
    //     0x77a76c: ldur            x2, [x1, #-1]
    //     0x77a770: ubfx            x2, x2, #0xc, #0x14
    // 0x77a774: r16 = false
    //     0x77a774: add             x16, NULL, #0x30  ; false
    // 0x77a778: str             x16, [SP]
    // 0x77a77c: mov             x16, x0
    // 0x77a780: mov             x0, x2
    // 0x77a784: mov             x2, x16
    // 0x77a788: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x77a788: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d5c0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x77a78c: ldr             x4, [x4, #0x5c0]
    // 0x77a790: r0 = GDT[cid_x0 + 0xed1d]()
    //     0x77a790: movz            x17, #0xed1d
    //     0x77a794: add             lr, x0, x17
    //     0x77a798: ldr             lr, [x21, lr, lsl #3]
    //     0x77a79c: blr             lr
    // 0x77a7a0: ldur            x0, [fp, #-8]
    // 0x77a7a4: LoadField: r5 = r0->field_13
    //     0x77a7a4: ldur            w5, [x0, #0x13]
    // 0x77a7a8: DecompressPointer r5
    //     0x77a7a8: add             x5, x5, HEAP, lsl #32
    // 0x77a7ac: ldur            x4, [fp, #-0x10]
    // 0x77a7b0: ldur            x3, [fp, #-0x20]
    // 0x77a7b4: b               #0x77a5e8
    // 0x77a7b8: r0 = Null
    //     0x77a7b8: mov             x0, NULL
    // 0x77a7bc: LeaveFrame
    //     0x77a7bc: mov             SP, fp
    //     0x77a7c0: ldp             fp, lr, [SP], #0x10
    // 0x77a7c4: ret
    //     0x77a7c4: ret             
    // 0x77a7c8: r0 = StateError()
    //     0x77a7c8: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x77a7cc: mov             x1, x0
    // 0x77a7d0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x77a7d0: ldr             x0, [PP, #0x49e8]  ; [pp+0x49e8] "A RenderObject does not have any constraints before it has been laid out."
    // 0x77a7d4: StoreField: r1->field_b = r0
    //     0x77a7d4: stur            w0, [x1, #0xb]
    // 0x77a7d8: mov             x0, x1
    // 0x77a7dc: r0 = Throw()
    //     0x77a7dc: bl              #0xec04b8  ; ThrowStub
    // 0x77a7e0: brk             #0
    // 0x77a7e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77a7e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77a7e8: b               #0x77a550
    // 0x77a7ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77a7ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x77a7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x77a7f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x77a7f4: b               #0x77a5f8
    // 0x77a7f8: r9 = _placementGrid
    //     0x77a7f8: add             x9, PP, #0x4c, lsl #12  ; [pp+0x4c4e0] Field <RenderLayoutGrid._placementGrid@1176515497>: late (offset: 0x70)
    //     0x77a7fc: ldr             x9, [x9, #0x4e0]
    // 0x77a800: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x77a800: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x77a804: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x77a804: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x788824, size: 0xbc
    // 0x788824: EnterFrame
    //     0x788824: stp             fp, lr, [SP, #-0x10]!
    //     0x788828: mov             fp, SP
    // 0x78882c: AllocStack(0x8)
    //     0x78882c: sub             SP, SP, #8
    // 0x788830: SetupParameters(RenderLayoutGrid this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x788830: mov             x0, x2
    //     0x788834: mov             x4, x1
    //     0x788838: mov             x3, x2
    //     0x78883c: stur            x2, [fp, #-8]
    // 0x788840: r2 = Null
    //     0x788840: mov             x2, NULL
    // 0x788844: r1 = Null
    //     0x788844: mov             x1, NULL
    // 0x788848: r4 = 60
    //     0x788848: movz            x4, #0x3c
    // 0x78884c: branchIfSmi(r0, 0x788858)
    //     0x78884c: tbz             w0, #0, #0x788858
    // 0x788850: r4 = LoadClassIdInstr(r0)
    //     0x788850: ldur            x4, [x0, #-1]
    //     0x788854: ubfx            x4, x4, #0xc, #0x14
    // 0x788858: sub             x4, x4, #0xbba
    // 0x78885c: cmp             x4, #0x9a
    // 0x788860: b.ls            #0x788874
    // 0x788864: r8 = RenderBox
    //     0x788864: ldr             x8, [PP, #0x4c70]  ; [pp+0x4c70] Type: RenderBox
    // 0x788868: r3 = Null
    //     0x788868: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c6e0] Null
    //     0x78886c: ldr             x3, [x3, #0x6e0]
    // 0x788870: r0 = RenderBox()
    //     0x788870: bl              #0x5fbb60  ; IsType_RenderBox_Stub
    // 0x788874: ldur            x0, [fp, #-8]
    // 0x788878: LoadField: r1 = r0->field_7
    //     0x788878: ldur            w1, [x0, #7]
    // 0x78887c: DecompressPointer r1
    //     0x78887c: add             x1, x1, HEAP, lsl #32
    // 0x788880: r2 = LoadClassIdInstr(r1)
    //     0x788880: ldur            x2, [x1, #-1]
    //     0x788884: ubfx            x2, x2, #0xc, #0x14
    // 0x788888: cmp             x2, #0xc75
    // 0x78888c: b.eq            #0x7888d0
    // 0x788890: r1 = <RenderBox>
    //     0x788890: add             x1, PP, #0x25, lsl #12  ; [pp+0x251d8] TypeArguments: <RenderBox>
    //     0x788894: ldr             x1, [x1, #0x1d8]
    // 0x788898: r0 = GridParentData()
    //     0x788898: bl              #0x7888e0  ; AllocateGridParentDataStub -> GridParentData (size=0x38)
    // 0x78889c: r1 = 1
    //     0x78889c: movz            x1, #0x1
    // 0x7888a0: StoreField: r0->field_1b = r1
    //     0x7888a0: stur            x1, [x0, #0x1b]
    // 0x7888a4: StoreField: r0->field_27 = r1
    //     0x7888a4: stur            x1, [x0, #0x27]
    // 0x7888a8: r1 = Instance_Offset
    //     0x7888a8: ldr             x1, [PP, #0x2b38]  ; [pp+0x2b38] Obj!Offset@e2c341
    // 0x7888ac: StoreField: r0->field_7 = r1
    //     0x7888ac: stur            w1, [x0, #7]
    // 0x7888b0: ldur            x1, [fp, #-8]
    // 0x7888b4: StoreField: r1->field_7 = r0
    //     0x7888b4: stur            w0, [x1, #7]
    //     0x7888b8: ldurb           w16, [x1, #-1]
    //     0x7888bc: ldurb           w17, [x0, #-1]
    //     0x7888c0: and             x16, x17, x16, lsr #2
    //     0x7888c4: tst             x16, HEAP, lsr #32
    //     0x7888c8: b.eq            #0x7888d0
    //     0x7888cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7888d0: r0 = Null
    //     0x7888d0: mov             x0, NULL
    // 0x7888d4: LeaveFrame
    //     0x7888d4: mov             SP, fp
    //     0x7888d8: ldp             fp, lr, [SP], #0x10
    // 0x7888dc: ret
    //     0x7888dc: ret             
  }
  _ paint(/* No info */) {
    // ** addr: 0x79f270, size: 0x74
    // 0x79f270: EnterFrame
    //     0x79f270: stp             fp, lr, [SP, #-0x10]!
    //     0x79f274: mov             fp, SP
    // 0x79f278: AllocStack(0x18)
    //     0x79f278: sub             SP, SP, #0x18
    // 0x79f27c: SetupParameters(RenderLayoutGrid this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x79f27c: stur            x1, [fp, #-8]
    //     0x79f280: stur            x2, [fp, #-0x10]
    //     0x79f284: stur            x3, [fp, #-0x18]
    // 0x79f288: CheckStackOverflow
    //     0x79f288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79f28c: cmp             SP, x16
    //     0x79f290: b.ls            #0x79f2dc
    // 0x79f294: r1 = 2
    //     0x79f294: movz            x1, #0x2
    // 0x79f298: r0 = AllocateContext()
    //     0x79f298: bl              #0xec126c  ; AllocateContextStub
    // 0x79f29c: mov             x1, x0
    // 0x79f2a0: ldur            x0, [fp, #-0x10]
    // 0x79f2a4: StoreField: r1->field_f = r0
    //     0x79f2a4: stur            w0, [x1, #0xf]
    // 0x79f2a8: ldur            x0, [fp, #-0x18]
    // 0x79f2ac: StoreField: r1->field_13 = r0
    //     0x79f2ac: stur            w0, [x1, #0x13]
    // 0x79f2b0: mov             x2, x1
    // 0x79f2b4: r1 = Function '<anonymous closure>':.
    //     0x79f2b4: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4c308] AnonymousClosure: (0x79f2e4), in [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::paint (0x79f270)
    //     0x79f2b8: ldr             x1, [x1, #0x308]
    // 0x79f2bc: r0 = AllocateClosure()
    //     0x79f2bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x79f2c0: ldur            x1, [fp, #-8]
    // 0x79f2c4: mov             x2, x0
    // 0x79f2c8: r0 = visitChildrenForSemantics()
    //     0x79f2c8: bl              #0x8014d4  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::visitChildrenForSemantics
    // 0x79f2cc: r0 = Null
    //     0x79f2cc: mov             x0, NULL
    // 0x79f2d0: LeaveFrame
    //     0x79f2d0: mov             SP, fp
    //     0x79f2d4: ldp             fp, lr, [SP], #0x10
    // 0x79f2d8: ret
    //     0x79f2d8: ret             
    // 0x79f2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79f2dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79f2e0: b               #0x79f294
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x79f2e4, size: 0xc4
    // 0x79f2e4: EnterFrame
    //     0x79f2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x79f2e8: mov             fp, SP
    // 0x79f2ec: AllocStack(0x18)
    //     0x79f2ec: sub             SP, SP, #0x18
    // 0x79f2f0: SetupParameters()
    //     0x79f2f0: ldr             x0, [fp, #0x18]
    //     0x79f2f4: ldur            w3, [x0, #0x17]
    //     0x79f2f8: add             x3, x3, HEAP, lsl #32
    //     0x79f2fc: stur            x3, [fp, #-0x10]
    // 0x79f300: CheckStackOverflow
    //     0x79f300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x79f304: cmp             SP, x16
    //     0x79f308: b.ls            #0x79f3a0
    // 0x79f30c: ldr             x4, [fp, #0x10]
    // 0x79f310: LoadField: r5 = r4->field_7
    //     0x79f310: ldur            w5, [x4, #7]
    // 0x79f314: DecompressPointer r5
    //     0x79f314: add             x5, x5, HEAP, lsl #32
    // 0x79f318: mov             x0, x5
    // 0x79f31c: stur            x5, [fp, #-8]
    // 0x79f320: r2 = Null
    //     0x79f320: mov             x2, NULL
    // 0x79f324: r1 = Null
    //     0x79f324: mov             x1, NULL
    // 0x79f328: r4 = LoadClassIdInstr(r0)
    //     0x79f328: ldur            x4, [x0, #-1]
    //     0x79f32c: ubfx            x4, x4, #0xc, #0x14
    // 0x79f330: cmp             x4, #0xc75
    // 0x79f334: b.eq            #0x79f34c
    // 0x79f338: r8 = GridParentData
    //     0x79f338: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x79f33c: ldr             x8, [x8, #0xca0]
    // 0x79f340: r3 = Null
    //     0x79f340: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c310] Null
    //     0x79f344: ldr             x3, [x3, #0x310]
    // 0x79f348: r0 = DefaultTypeTest()
    //     0x79f348: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x79f34c: ldur            x0, [fp, #-0x10]
    // 0x79f350: LoadField: r3 = r0->field_f
    //     0x79f350: ldur            w3, [x0, #0xf]
    // 0x79f354: DecompressPointer r3
    //     0x79f354: add             x3, x3, HEAP, lsl #32
    // 0x79f358: ldur            x1, [fp, #-8]
    // 0x79f35c: stur            x3, [fp, #-0x18]
    // 0x79f360: LoadField: r2 = r1->field_7
    //     0x79f360: ldur            w2, [x1, #7]
    // 0x79f364: DecompressPointer r2
    //     0x79f364: add             x2, x2, HEAP, lsl #32
    // 0x79f368: LoadField: r1 = r0->field_13
    //     0x79f368: ldur            w1, [x0, #0x13]
    // 0x79f36c: DecompressPointer r1
    //     0x79f36c: add             x1, x1, HEAP, lsl #32
    // 0x79f370: mov             x16, x1
    // 0x79f374: mov             x1, x2
    // 0x79f378: mov             x2, x16
    // 0x79f37c: r0 = +()
    //     0x79f37c: bl              #0x618ae8  ; [dart:ui] Offset::+
    // 0x79f380: ldur            x1, [fp, #-0x18]
    // 0x79f384: ldr             x2, [fp, #0x10]
    // 0x79f388: mov             x3, x0
    // 0x79f38c: r0 = paintChild()
    //     0x79f38c: bl              #0x78bb40  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x79f390: r0 = Null
    //     0x79f390: mov             x0, NULL
    // 0x79f394: LeaveFrame
    //     0x79f394: mov             SP, fp
    //     0x79f398: ldp             fp, lr, [SP], #0x10
    // 0x79f39c: ret
    //     0x79f39c: ret             
    // 0x79f3a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x79f3a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x79f3a4: b               #0x79f30c
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x8001f0, size: 0x2c
    // 0x8001f0: EnterFrame
    //     0x8001f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8001f4: mov             fp, SP
    // 0x8001f8: CheckStackOverflow
    //     0x8001f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8001fc: cmp             SP, x16
    //     0x800200: b.ls            #0x800214
    // 0x800204: r0 = defaultHitTestChildren()
    //     0x800204: bl              #0x80021c  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] _RenderLayoutGrid&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin::defaultHitTestChildren
    // 0x800208: LeaveFrame
    //     0x800208: mov             SP, fp
    //     0x80020c: ldp             fp, lr, [SP], #0x10
    // 0x800210: ret
    //     0x800210: ret             
    // 0x800214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x800214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x800218: b               #0x800204
  }
  _ visitChildrenForSemantics(/* No info */) {
    // ** addr: 0x8014d4, size: 0xd0
    // 0x8014d4: EnterFrame
    //     0x8014d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8014d8: mov             fp, SP
    // 0x8014dc: AllocStack(0x28)
    //     0x8014dc: sub             SP, SP, #0x28
    // 0x8014e0: SetupParameters(dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x8014e0: mov             x3, x2
    //     0x8014e4: stur            x2, [fp, #-0x18]
    // 0x8014e8: CheckStackOverflow
    //     0x8014e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8014ec: cmp             SP, x16
    //     0x8014f0: b.ls            #0x801594
    // 0x8014f4: LoadField: r0 = r1->field_5f
    //     0x8014f4: ldur            w0, [x1, #0x5f]
    // 0x8014f8: DecompressPointer r0
    //     0x8014f8: add             x0, x0, HEAP, lsl #32
    // 0x8014fc: mov             x4, x0
    // 0x801500: stur            x4, [fp, #-0x10]
    // 0x801504: CheckStackOverflow
    //     0x801504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x801508: cmp             SP, x16
    //     0x80150c: b.ls            #0x80159c
    // 0x801510: cmp             w4, NULL
    // 0x801514: b.eq            #0x801584
    // 0x801518: LoadField: r5 = r4->field_7
    //     0x801518: ldur            w5, [x4, #7]
    // 0x80151c: DecompressPointer r5
    //     0x80151c: add             x5, x5, HEAP, lsl #32
    // 0x801520: mov             x0, x5
    // 0x801524: stur            x5, [fp, #-8]
    // 0x801528: r2 = Null
    //     0x801528: mov             x2, NULL
    // 0x80152c: r1 = Null
    //     0x80152c: mov             x1, NULL
    // 0x801530: r4 = LoadClassIdInstr(r0)
    //     0x801530: ldur            x4, [x0, #-1]
    //     0x801534: ubfx            x4, x4, #0xc, #0x14
    // 0x801538: cmp             x4, #0xc75
    // 0x80153c: b.eq            #0x801554
    // 0x801540: r8 = GridParentData
    //     0x801540: add             x8, PP, #0x41, lsl #12  ; [pp+0x41ca0] Type: GridParentData
    //     0x801544: ldr             x8, [x8, #0xca0]
    // 0x801548: r3 = Null
    //     0x801548: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c320] Null
    //     0x80154c: ldr             x3, [x3, #0x320]
    // 0x801550: r0 = DefaultTypeTest()
    //     0x801550: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x801554: ldur            x16, [fp, #-0x18]
    // 0x801558: ldur            lr, [fp, #-0x10]
    // 0x80155c: stp             lr, x16, [SP]
    // 0x801560: ldur            x0, [fp, #-0x18]
    // 0x801564: ClosureCall
    //     0x801564: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x801568: ldur            x2, [x0, #0x1f]
    //     0x80156c: blr             x2
    // 0x801570: ldur            x1, [fp, #-8]
    // 0x801574: LoadField: r4 = r1->field_13
    //     0x801574: ldur            w4, [x1, #0x13]
    // 0x801578: DecompressPointer r4
    //     0x801578: add             x4, x4, HEAP, lsl #32
    // 0x80157c: ldur            x3, [fp, #-0x18]
    // 0x801580: b               #0x801500
    // 0x801584: r0 = Null
    //     0x801584: mov             x0, NULL
    // 0x801588: LeaveFrame
    //     0x801588: mov             SP, fp
    //     0x80158c: ldp             fp, lr, [SP], #0x10
    // 0x801590: ret
    //     0x801590: ret             
    // 0x801594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x801594: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801598: b               #0x8014f4
    // 0x80159c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80159c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8015a0: b               #0x801510
  }
  _ dropChild(/* No info */) {
    // ** addr: 0x8016bc, size: 0x58
    // 0x8016bc: EnterFrame
    //     0x8016bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8016c0: mov             fp, SP
    // 0x8016c4: AllocStack(0x10)
    //     0x8016c4: sub             SP, SP, #0x10
    // 0x8016c8: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x8016c8: mov             x3, x1
    //     0x8016cc: mov             x0, x2
    //     0x8016d0: stur            x1, [fp, #-8]
    //     0x8016d4: stur            x2, [fp, #-0x10]
    // 0x8016d8: CheckStackOverflow
    //     0x8016d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8016dc: cmp             SP, x16
    //     0x8016e0: b.ls            #0x80170c
    // 0x8016e4: mov             x1, x3
    // 0x8016e8: mov             x2, x0
    // 0x8016ec: r0 = dropChild()
    //     0x8016ec: bl              #0x8019dc  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x8016f0: ldur            x1, [fp, #-8]
    // 0x8016f4: ldur            x2, [fp, #-0x10]
    // 0x8016f8: r0 = markNeedsPlacementIfRequired()
    //     0x8016f8: bl              #0x801714  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::markNeedsPlacementIfRequired
    // 0x8016fc: r0 = Null
    //     0x8016fc: mov             x0, NULL
    // 0x801700: LeaveFrame
    //     0x801700: mov             SP, fp
    //     0x801704: ldp             fp, lr, [SP], #0x10
    // 0x801708: ret
    //     0x801708: ret             
    // 0x80170c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80170c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x801710: b               #0x8016e4
  }
  _ markNeedsPlacementIfRequired(/* No info */) {
    // ** addr: 0x801714, size: 0xc4
    // 0x801714: EnterFrame
    //     0x801714: stp             fp, lr, [SP, #-0x10]!
    //     0x801718: mov             fp, SP
    // 0x80171c: AllocStack(0x10)
    //     0x80171c: sub             SP, SP, #0x10
    // 0x801720: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x10 */)
    //     0x801720: mov             x3, x1
    //     0x801724: stur            x1, [fp, #-0x10]
    // 0x801728: CheckStackOverflow
    //     0x801728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80172c: cmp             SP, x16
    //     0x801730: b.ls            #0x8017d0
    // 0x801734: LoadField: r0 = r3->field_6b
    //     0x801734: ldur            w0, [x3, #0x6b]
    // 0x801738: DecompressPointer r0
    //     0x801738: add             x0, x0, HEAP, lsl #32
    // 0x80173c: tbnz            w0, #4, #0x801750
    // 0x801740: r0 = Null
    //     0x801740: mov             x0, NULL
    // 0x801744: LeaveFrame
    //     0x801744: mov             SP, fp
    //     0x801748: ldp             fp, lr, [SP], #0x10
    // 0x80174c: ret
    //     0x80174c: ret             
    // 0x801750: LoadField: r4 = r2->field_7
    //     0x801750: ldur            w4, [x2, #7]
    // 0x801754: DecompressPointer r4
    //     0x801754: add             x4, x4, HEAP, lsl #32
    // 0x801758: mov             x0, x4
    // 0x80175c: stur            x4, [fp, #-8]
    // 0x801760: r2 = Null
    //     0x801760: mov             x2, NULL
    // 0x801764: r1 = Null
    //     0x801764: mov             x1, NULL
    // 0x801768: r4 = LoadClassIdInstr(r0)
    //     0x801768: ldur            x4, [x0, #-1]
    //     0x80176c: ubfx            x4, x4, #0xc, #0x14
    // 0x801770: cmp             x4, #0xc75
    // 0x801774: b.eq            #0x80178c
    // 0x801778: r8 = GridParentData?
    //     0x801778: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4c2d0] Type: GridParentData?
    //     0x80177c: ldr             x8, [x8, #0x2d0]
    // 0x801780: r3 = Null
    //     0x801780: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4c2d8] Null
    //     0x801784: ldr             x3, [x3, #0x2d8]
    // 0x801788: r0 = DefaultNullableTypeTest()
    //     0x801788: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x80178c: ldur            x0, [fp, #-8]
    // 0x801790: cmp             w0, NULL
    // 0x801794: b.eq            #0x8017c0
    // 0x801798: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x801798: ldur            w1, [x0, #0x17]
    // 0x80179c: DecompressPointer r1
    //     0x80179c: add             x1, x1, HEAP, lsl #32
    // 0x8017a0: cmp             w1, NULL
    // 0x8017a4: b.eq            #0x8017b8
    // 0x8017a8: LoadField: r1 = r0->field_23
    //     0x8017a8: ldur            w1, [x0, #0x23]
    // 0x8017ac: DecompressPointer r1
    //     0x8017ac: add             x1, x1, HEAP, lsl #32
    // 0x8017b0: cmp             w1, NULL
    // 0x8017b4: b.ne            #0x8017c0
    // 0x8017b8: ldur            x1, [fp, #-0x10]
    // 0x8017bc: r0 = markNeedsPlacement()
    //     0x8017bc: bl              #0x8017d8  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::markNeedsPlacement
    // 0x8017c0: r0 = Null
    //     0x8017c0: mov             x0, NULL
    // 0x8017c4: LeaveFrame
    //     0x8017c4: mov             SP, fp
    //     0x8017c8: ldp             fp, lr, [SP], #0x10
    // 0x8017cc: ret
    //     0x8017cc: ret             
    // 0x8017d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8017d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8017d4: b               #0x801734
  }
  _ markNeedsPlacement(/* No info */) {
    // ** addr: 0x8017d8, size: 0xc
    // 0x8017d8: r0 = true
    //     0x8017d8: add             x0, NULL, #0x20  ; true
    // 0x8017dc: StoreField: r1->field_6b = r0
    //     0x8017dc: stur            w0, [x1, #0x6b]
    // 0x8017e0: ret
    //     0x8017e0: ret             
  }
  _ adoptChild(/* No info */) {
    // ** addr: 0x805624, size: 0x58
    // 0x805624: EnterFrame
    //     0x805624: stp             fp, lr, [SP, #-0x10]!
    //     0x805628: mov             fp, SP
    // 0x80562c: AllocStack(0x10)
    //     0x80562c: sub             SP, SP, #0x10
    // 0x805630: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x805630: mov             x3, x1
    //     0x805634: mov             x0, x2
    //     0x805638: stur            x1, [fp, #-8]
    //     0x80563c: stur            x2, [fp, #-0x10]
    // 0x805640: CheckStackOverflow
    //     0x805640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805644: cmp             SP, x16
    //     0x805648: b.ls            #0x805674
    // 0x80564c: mov             x1, x3
    // 0x805650: mov             x2, x0
    // 0x805654: r0 = adoptChild()
    //     0x805654: bl              #0x8057b8  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x805658: ldur            x1, [fp, #-8]
    // 0x80565c: ldur            x2, [fp, #-0x10]
    // 0x805660: r0 = markNeedsPlacementIfRequired()
    //     0x805660: bl              #0x801714  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::markNeedsPlacementIfRequired
    // 0x805664: r0 = Null
    //     0x805664: mov             x0, NULL
    // 0x805668: LeaveFrame
    //     0x805668: mov             SP, fp
    //     0x80566c: ldp             fp, lr, [SP], #0x10
    // 0x805670: ret
    //     0x805670: ret             
    // 0x805674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805674: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805678: b               #0x80564c
  }
  _ RenderLayoutGrid(/* No info */) {
    // ** addr: 0x859d2c, size: 0xe0
    // 0x859d2c: EnterFrame
    //     0x859d2c: stp             fp, lr, [SP, #-0x10]!
    //     0x859d30: mov             fp, SP
    // 0x859d34: r7 = true
    //     0x859d34: add             x7, NULL, #0x20  ; true
    // 0x859d38: r6 = Sentinel
    //     0x859d38: ldr             x6, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x859d3c: r4 = Instance_AutoPlacement
    //     0x859d3c: add             x4, PP, #0x31, lsl #12  ; [pp+0x317b0] Obj!AutoPlacement@e0f4a1
    //     0x859d40: ldr             x4, [x4, #0x7b0]
    // 0x859d44: r0 = Instance_GridFit
    //     0x859d44: add             x0, PP, #0x31, lsl #12  ; [pp+0x317b8] Obj!GridFit@e32d41
    //     0x859d48: ldr             x0, [x0, #0x7b8]
    // 0x859d4c: d0 = 1.000000
    //     0x859d4c: fmov            d0, #1.00000000
    // 0x859d50: mov             x16, x5
    // 0x859d54: mov             x5, x1
    // 0x859d58: mov             x1, x16
    // 0x859d5c: mov             x16, x3
    // 0x859d60: mov             x3, x2
    // 0x859d64: mov             x2, x16
    // 0x859d68: CheckStackOverflow
    //     0x859d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x859d6c: cmp             SP, x16
    //     0x859d70: b.ls            #0x859e04
    // 0x859d74: StoreField: r5->field_6b = r7
    //     0x859d74: stur            w7, [x5, #0x6b]
    // 0x859d78: StoreField: r5->field_6f = r6
    //     0x859d78: stur            w6, [x5, #0x6f]
    // 0x859d7c: StoreField: r5->field_73 = r4
    //     0x859d7c: stur            w4, [x5, #0x73]
    // 0x859d80: StoreField: r5->field_77 = r0
    //     0x859d80: stur            w0, [x5, #0x77]
    // 0x859d84: mov             x0, x3
    // 0x859d88: StoreField: r5->field_83 = r0
    //     0x859d88: stur            w0, [x5, #0x83]
    //     0x859d8c: ldurb           w16, [x5, #-1]
    //     0x859d90: ldurb           w17, [x0, #-1]
    //     0x859d94: and             x16, x17, x16, lsr #2
    //     0x859d98: tst             x16, HEAP, lsr #32
    //     0x859d9c: b.eq            #0x859da4
    //     0x859da0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x859da4: mov             x0, x2
    // 0x859da8: StoreField: r5->field_87 = r0
    //     0x859da8: stur            w0, [x5, #0x87]
    //     0x859dac: ldurb           w16, [x5, #-1]
    //     0x859db0: ldurb           w17, [x0, #-1]
    //     0x859db4: and             x16, x17, x16, lsr #2
    //     0x859db8: tst             x16, HEAP, lsr #32
    //     0x859dbc: b.eq            #0x859dc4
    //     0x859dc0: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x859dc4: StoreField: r5->field_8b = d0
    //     0x859dc4: stur            d0, [x5, #0x8b]
    // 0x859dc8: StoreField: r5->field_93 = d0
    //     0x859dc8: stur            d0, [x5, #0x93]
    // 0x859dcc: mov             x0, x1
    // 0x859dd0: StoreField: r5->field_9b = r0
    //     0x859dd0: stur            w0, [x5, #0x9b]
    //     0x859dd4: ldurb           w16, [x5, #-1]
    //     0x859dd8: ldurb           w17, [x0, #-1]
    //     0x859ddc: and             x16, x17, x16, lsr #2
    //     0x859de0: tst             x16, HEAP, lsr #32
    //     0x859de4: b.eq            #0x859dec
    //     0x859de8: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x859dec: mov             x1, x5
    // 0x859df0: r0 = _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin()
    //     0x859df0: bl              #0x856340  ; [package:flutter/src/rendering/flex.dart] _RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin::_RenderFlex&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin&DebugOverflowIndicatorMixin
    // 0x859df4: r0 = Null
    //     0x859df4: mov             x0, NULL
    // 0x859df8: LeaveFrame
    //     0x859df8: mov             SP, fp
    //     0x859dfc: ldp             fp, lr, [SP], #0x10
    // 0x859e00: ret
    //     0x859e00: ret             
    // 0x859e04: r0 = StackOverflowSharedWithFPURegs()
    //     0x859e04: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x859e08: b               #0x859d74
  }
  set _ textDirection=(/* No info */) {
    // ** addr: 0xc6c72c, size: 0x70
    // 0xc6c72c: EnterFrame
    //     0xc6c72c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c730: mov             fp, SP
    // 0xc6c734: mov             x0, x2
    // 0xc6c738: CheckStackOverflow
    //     0xc6c738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c73c: cmp             SP, x16
    //     0xc6c740: b.ls            #0xc6c794
    // 0xc6c744: LoadField: r2 = r1->field_9b
    //     0xc6c744: ldur            w2, [x1, #0x9b]
    // 0xc6c748: DecompressPointer r2
    //     0xc6c748: add             x2, x2, HEAP, lsl #32
    // 0xc6c74c: cmp             w2, w0
    // 0xc6c750: b.ne            #0xc6c764
    // 0xc6c754: r0 = Null
    //     0xc6c754: mov             x0, NULL
    // 0xc6c758: LeaveFrame
    //     0xc6c758: mov             SP, fp
    //     0xc6c75c: ldp             fp, lr, [SP], #0x10
    // 0xc6c760: ret
    //     0xc6c760: ret             
    // 0xc6c764: StoreField: r1->field_9b = r0
    //     0xc6c764: stur            w0, [x1, #0x9b]
    //     0xc6c768: ldurb           w16, [x1, #-1]
    //     0xc6c76c: ldurb           w17, [x0, #-1]
    //     0xc6c770: and             x16, x17, x16, lsr #2
    //     0xc6c774: tst             x16, HEAP, lsr #32
    //     0xc6c778: b.eq            #0xc6c780
    //     0xc6c77c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c780: r0 = markNeedsLayout()
    //     0xc6c780: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c784: r0 = Null
    //     0xc6c784: mov             x0, NULL
    // 0xc6c788: LeaveFrame
    //     0xc6c788: mov             SP, fp
    //     0xc6c78c: ldp             fp, lr, [SP], #0x10
    // 0xc6c790: ret
    //     0xc6c790: ret             
    // 0xc6c794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c798: b               #0xc6c744
  }
  set _ rowGap=(/* No info */) {
    // ** addr: 0xc6c79c, size: 0x50
    // 0xc6c79c: EnterFrame
    //     0xc6c79c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c7a0: mov             fp, SP
    // 0xc6c7a4: d1 = 1.000000
    //     0xc6c7a4: fmov            d1, #1.00000000
    // 0xc6c7a8: CheckStackOverflow
    //     0xc6c7a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c7ac: cmp             SP, x16
    //     0xc6c7b0: b.ls            #0xc6c7e4
    // 0xc6c7b4: fcmp            d1, d1
    // 0xc6c7b8: b.ne            #0xc6c7cc
    // 0xc6c7bc: r0 = Null
    //     0xc6c7bc: mov             x0, NULL
    // 0xc6c7c0: LeaveFrame
    //     0xc6c7c0: mov             SP, fp
    //     0xc6c7c4: ldp             fp, lr, [SP], #0x10
    // 0xc6c7c8: ret
    //     0xc6c7c8: ret             
    // 0xc6c7cc: StoreField: r1->field_93 = d1
    //     0xc6c7cc: stur            d1, [x1, #0x93]
    // 0xc6c7d0: r0 = markNeedsLayout()
    //     0xc6c7d0: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c7d4: r0 = Null
    //     0xc6c7d4: mov             x0, NULL
    // 0xc6c7d8: LeaveFrame
    //     0xc6c7d8: mov             SP, fp
    //     0xc6c7dc: ldp             fp, lr, [SP], #0x10
    // 0xc6c7e0: ret
    //     0xc6c7e0: ret             
    // 0xc6c7e4: r0 = StackOverflowSharedWithFPURegs()
    //     0xc6c7e4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc6c7e8: b               #0xc6c7b4
  }
  set _ columnGap=(/* No info */) {
    // ** addr: 0xc6c7ec, size: 0x50
    // 0xc6c7ec: EnterFrame
    //     0xc6c7ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c7f0: mov             fp, SP
    // 0xc6c7f4: d1 = 1.000000
    //     0xc6c7f4: fmov            d1, #1.00000000
    // 0xc6c7f8: CheckStackOverflow
    //     0xc6c7f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c7fc: cmp             SP, x16
    //     0xc6c800: b.ls            #0xc6c834
    // 0xc6c804: fcmp            d1, d1
    // 0xc6c808: b.ne            #0xc6c81c
    // 0xc6c80c: r0 = Null
    //     0xc6c80c: mov             x0, NULL
    // 0xc6c810: LeaveFrame
    //     0xc6c810: mov             SP, fp
    //     0xc6c814: ldp             fp, lr, [SP], #0x10
    // 0xc6c818: ret
    //     0xc6c818: ret             
    // 0xc6c81c: StoreField: r1->field_8b = d1
    //     0xc6c81c: stur            d1, [x1, #0x8b]
    // 0xc6c820: r0 = markNeedsLayout()
    //     0xc6c820: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c824: r0 = Null
    //     0xc6c824: mov             x0, NULL
    // 0xc6c828: LeaveFrame
    //     0xc6c828: mov             SP, fp
    //     0xc6c82c: ldp             fp, lr, [SP], #0x10
    // 0xc6c830: ret
    //     0xc6c830: ret             
    // 0xc6c834: r0 = StackOverflowSharedWithFPURegs()
    //     0xc6c834: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc6c838: b               #0xc6c804
  }
  set _ rowSizes=(/* No info */) {
    // ** addr: 0xc6c83c, size: 0xbc
    // 0xc6c83c: EnterFrame
    //     0xc6c83c: stp             fp, lr, [SP, #-0x10]!
    //     0xc6c840: mov             fp, SP
    // 0xc6c844: AllocStack(0x10)
    //     0xc6c844: sub             SP, SP, #0x10
    // 0xc6c848: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc6c848: mov             x3, x1
    //     0xc6c84c: mov             x0, x2
    //     0xc6c850: stur            x1, [fp, #-8]
    //     0xc6c854: stur            x2, [fp, #-0x10]
    // 0xc6c858: CheckStackOverflow
    //     0xc6c858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6c85c: cmp             SP, x16
    //     0xc6c860: b.ls            #0xc6c8f0
    // 0xc6c864: LoadField: r1 = r3->field_87
    //     0xc6c864: ldur            w1, [x3, #0x87]
    // 0xc6c868: DecompressPointer r1
    //     0xc6c868: add             x1, x1, HEAP, lsl #32
    // 0xc6c86c: mov             x2, x0
    // 0xc6c870: r0 = trackSizeListsEqual()
    //     0xc6c870: bl              #0xc6c8f8  ; [package:flutter_layout_grid/src/rendering/track_size.dart] ::trackSizeListsEqual
    // 0xc6c874: tbnz            w0, #4, #0xc6c888
    // 0xc6c878: r0 = Null
    //     0xc6c878: mov             x0, NULL
    // 0xc6c87c: LeaveFrame
    //     0xc6c87c: mov             SP, fp
    //     0xc6c880: ldp             fp, lr, [SP], #0x10
    // 0xc6c884: ret
    //     0xc6c884: ret             
    // 0xc6c888: ldur            x2, [fp, #-8]
    // 0xc6c88c: ldur            x0, [fp, #-0x10]
    // 0xc6c890: LoadField: r1 = r0->field_b
    //     0xc6c890: ldur            w1, [x0, #0xb]
    // 0xc6c894: LoadField: r3 = r2->field_87
    //     0xc6c894: ldur            w3, [x2, #0x87]
    // 0xc6c898: DecompressPointer r3
    //     0xc6c898: add             x3, x3, HEAP, lsl #32
    // 0xc6c89c: LoadField: r4 = r3->field_b
    //     0xc6c89c: ldur            w4, [x3, #0xb]
    // 0xc6c8a0: cmp             w1, w4
    // 0xc6c8a4: b.eq            #0xc6c8b0
    // 0xc6c8a8: mov             x1, x2
    // 0xc6c8ac: r0 = markNeedsPlacement()
    //     0xc6c8ac: bl              #0x8017d8  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::markNeedsPlacement
    // 0xc6c8b0: ldur            x0, [fp, #-8]
    // 0xc6c8b4: mov             x1, x0
    // 0xc6c8b8: r0 = markNeedsLayout()
    //     0xc6c8b8: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6c8bc: ldur            x0, [fp, #-0x10]
    // 0xc6c8c0: ldur            x1, [fp, #-8]
    // 0xc6c8c4: StoreField: r1->field_87 = r0
    //     0xc6c8c4: stur            w0, [x1, #0x87]
    //     0xc6c8c8: ldurb           w16, [x1, #-1]
    //     0xc6c8cc: ldurb           w17, [x0, #-1]
    //     0xc6c8d0: and             x16, x17, x16, lsr #2
    //     0xc6c8d4: tst             x16, HEAP, lsr #32
    //     0xc6c8d8: b.eq            #0xc6c8e0
    //     0xc6c8dc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6c8e0: r0 = Null
    //     0xc6c8e0: mov             x0, NULL
    // 0xc6c8e4: LeaveFrame
    //     0xc6c8e4: mov             SP, fp
    //     0xc6c8e8: ldp             fp, lr, [SP], #0x10
    // 0xc6c8ec: ret
    //     0xc6c8ec: ret             
    // 0xc6c8f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6c8f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6c8f4: b               #0xc6c864
  }
  set _ columnSizes=(/* No info */) {
    // ** addr: 0xc6ce94, size: 0xbc
    // 0xc6ce94: EnterFrame
    //     0xc6ce94: stp             fp, lr, [SP, #-0x10]!
    //     0xc6ce98: mov             fp, SP
    // 0xc6ce9c: AllocStack(0x10)
    //     0xc6ce9c: sub             SP, SP, #0x10
    // 0xc6cea0: SetupParameters(RenderLayoutGrid this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc6cea0: mov             x3, x1
    //     0xc6cea4: mov             x0, x2
    //     0xc6cea8: stur            x1, [fp, #-8]
    //     0xc6ceac: stur            x2, [fp, #-0x10]
    // 0xc6ceb0: CheckStackOverflow
    //     0xc6ceb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc6ceb4: cmp             SP, x16
    //     0xc6ceb8: b.ls            #0xc6cf48
    // 0xc6cebc: LoadField: r1 = r3->field_83
    //     0xc6cebc: ldur            w1, [x3, #0x83]
    // 0xc6cec0: DecompressPointer r1
    //     0xc6cec0: add             x1, x1, HEAP, lsl #32
    // 0xc6cec4: mov             x2, x0
    // 0xc6cec8: r0 = trackSizeListsEqual()
    //     0xc6cec8: bl              #0xc6c8f8  ; [package:flutter_layout_grid/src/rendering/track_size.dart] ::trackSizeListsEqual
    // 0xc6cecc: tbnz            w0, #4, #0xc6cee0
    // 0xc6ced0: r0 = Null
    //     0xc6ced0: mov             x0, NULL
    // 0xc6ced4: LeaveFrame
    //     0xc6ced4: mov             SP, fp
    //     0xc6ced8: ldp             fp, lr, [SP], #0x10
    // 0xc6cedc: ret
    //     0xc6cedc: ret             
    // 0xc6cee0: ldur            x2, [fp, #-8]
    // 0xc6cee4: ldur            x0, [fp, #-0x10]
    // 0xc6cee8: LoadField: r1 = r0->field_b
    //     0xc6cee8: ldur            w1, [x0, #0xb]
    // 0xc6ceec: LoadField: r3 = r2->field_83
    //     0xc6ceec: ldur            w3, [x2, #0x83]
    // 0xc6cef0: DecompressPointer r3
    //     0xc6cef0: add             x3, x3, HEAP, lsl #32
    // 0xc6cef4: LoadField: r4 = r3->field_b
    //     0xc6cef4: ldur            w4, [x3, #0xb]
    // 0xc6cef8: cmp             w1, w4
    // 0xc6cefc: b.eq            #0xc6cf08
    // 0xc6cf00: mov             x1, x2
    // 0xc6cf04: r0 = markNeedsPlacement()
    //     0xc6cf04: bl              #0x8017d8  ; [package:flutter_layout_grid/src/rendering/layout_grid.dart] RenderLayoutGrid::markNeedsPlacement
    // 0xc6cf08: ldur            x0, [fp, #-8]
    // 0xc6cf0c: mov             x1, x0
    // 0xc6cf10: r0 = markNeedsLayout()
    //     0xc6cf10: bl              #0x803ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xc6cf14: ldur            x0, [fp, #-0x10]
    // 0xc6cf18: ldur            x1, [fp, #-8]
    // 0xc6cf1c: StoreField: r1->field_83 = r0
    //     0xc6cf1c: stur            w0, [x1, #0x83]
    //     0xc6cf20: ldurb           w16, [x1, #-1]
    //     0xc6cf24: ldurb           w17, [x0, #-1]
    //     0xc6cf28: and             x16, x17, x16, lsr #2
    //     0xc6cf2c: tst             x16, HEAP, lsr #32
    //     0xc6cf30: b.eq            #0xc6cf38
    //     0xc6cf34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc6cf38: r0 = Null
    //     0xc6cf38: mov             x0, NULL
    // 0xc6cf3c: LeaveFrame
    //     0xc6cf3c: mov             SP, fp
    //     0xc6cf40: ldp             fp, lr, [SP], #0x10
    // 0xc6cf44: ret
    //     0xc6cf44: ret             
    // 0xc6cf48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc6cf48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc6cf4c: b               #0xc6cebc
  }
}

// class id: 3189, size: 0x38, field offset: 0x18
class GridParentData extends ContainerBoxParentData<dynamic> {

  _ toString(/* No info */) {
    // ** addr: 0xc228c8, size: 0x3f4
    // 0xc228c8: EnterFrame
    //     0xc228c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc228cc: mov             fp, SP
    // 0xc228d0: AllocStack(0x20)
    //     0xc228d0: sub             SP, SP, #0x20
    // 0xc228d4: CheckStackOverflow
    //     0xc228d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc228d8: cmp             SP, x16
    //     0xc228dc: b.ls            #0xc22cb4
    // 0xc228e0: r1 = <String>
    //     0xc228e0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc228e4: r2 = 0
    //     0xc228e4: movz            x2, #0
    // 0xc228e8: r0 = _GrowableList()
    //     0xc228e8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc228ec: mov             x3, x0
    // 0xc228f0: ldr             x0, [fp, #0x10]
    // 0xc228f4: stur            x3, [fp, #-0x10]
    // 0xc228f8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xc228f8: ldur            w4, [x0, #0x17]
    // 0xc228fc: DecompressPointer r4
    //     0xc228fc: add             x4, x4, HEAP, lsl #32
    // 0xc22900: stur            x4, [fp, #-8]
    // 0xc22904: cmp             w4, NULL
    // 0xc22908: b.eq            #0xc229b4
    // 0xc2290c: r1 = Null
    //     0xc2290c: mov             x1, NULL
    // 0xc22910: r2 = 4
    //     0xc22910: movz            x2, #0x4
    // 0xc22914: r0 = AllocateArray()
    //     0xc22914: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc22918: r16 = "columnStart="
    //     0xc22918: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e38] "columnStart="
    //     0xc2291c: ldr             x16, [x16, #0xe38]
    // 0xc22920: StoreField: r0->field_f = r16
    //     0xc22920: stur            w16, [x0, #0xf]
    // 0xc22924: ldur            x1, [fp, #-8]
    // 0xc22928: StoreField: r0->field_13 = r1
    //     0xc22928: stur            w1, [x0, #0x13]
    // 0xc2292c: str             x0, [SP]
    // 0xc22930: r0 = _interpolate()
    //     0xc22930: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc22934: mov             x2, x0
    // 0xc22938: ldur            x0, [fp, #-0x10]
    // 0xc2293c: stur            x2, [fp, #-8]
    // 0xc22940: LoadField: r1 = r0->field_b
    //     0xc22940: ldur            w1, [x0, #0xb]
    // 0xc22944: LoadField: r3 = r0->field_f
    //     0xc22944: ldur            w3, [x0, #0xf]
    // 0xc22948: DecompressPointer r3
    //     0xc22948: add             x3, x3, HEAP, lsl #32
    // 0xc2294c: LoadField: r4 = r3->field_b
    //     0xc2294c: ldur            w4, [x3, #0xb]
    // 0xc22950: r3 = LoadInt32Instr(r1)
    //     0xc22950: sbfx            x3, x1, #1, #0x1f
    // 0xc22954: stur            x3, [fp, #-0x18]
    // 0xc22958: r1 = LoadInt32Instr(r4)
    //     0xc22958: sbfx            x1, x4, #1, #0x1f
    // 0xc2295c: cmp             x3, x1
    // 0xc22960: b.ne            #0xc2296c
    // 0xc22964: mov             x1, x0
    // 0xc22968: r0 = _growToNextCapacity()
    //     0xc22968: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc2296c: ldur            x3, [fp, #-0x10]
    // 0xc22970: ldur            x2, [fp, #-0x18]
    // 0xc22974: add             x0, x2, #1
    // 0xc22978: lsl             x1, x0, #1
    // 0xc2297c: StoreField: r3->field_b = r1
    //     0xc2297c: stur            w1, [x3, #0xb]
    // 0xc22980: LoadField: r1 = r3->field_f
    //     0xc22980: ldur            w1, [x3, #0xf]
    // 0xc22984: DecompressPointer r1
    //     0xc22984: add             x1, x1, HEAP, lsl #32
    // 0xc22988: ldur            x0, [fp, #-8]
    // 0xc2298c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc2298c: add             x25, x1, x2, lsl #2
    //     0xc22990: add             x25, x25, #0xf
    //     0xc22994: str             w0, [x25]
    //     0xc22998: tbz             w0, #0, #0xc229b4
    //     0xc2299c: ldurb           w16, [x1, #-1]
    //     0xc229a0: ldurb           w17, [x0, #-1]
    //     0xc229a4: and             x16, x17, x16, lsr #2
    //     0xc229a8: tst             x16, HEAP, lsr #32
    //     0xc229ac: b.eq            #0xc229b4
    //     0xc229b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc229b4: ldr             x0, [fp, #0x10]
    // 0xc229b8: r1 = Null
    //     0xc229b8: mov             x1, NULL
    // 0xc229bc: r2 = 4
    //     0xc229bc: movz            x2, #0x4
    // 0xc229c0: r0 = AllocateArray()
    //     0xc229c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc229c4: mov             x2, x0
    // 0xc229c8: r16 = "columnSpan="
    //     0xc229c8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e40] "columnSpan="
    //     0xc229cc: ldr             x16, [x16, #0xe40]
    // 0xc229d0: StoreField: r2->field_f = r16
    //     0xc229d0: stur            w16, [x2, #0xf]
    // 0xc229d4: ldr             x3, [fp, #0x10]
    // 0xc229d8: LoadField: r4 = r3->field_1b
    //     0xc229d8: ldur            x4, [x3, #0x1b]
    // 0xc229dc: r0 = BoxInt64Instr(r4)
    //     0xc229dc: sbfiz           x0, x4, #1, #0x1f
    //     0xc229e0: cmp             x4, x0, asr #1
    //     0xc229e4: b.eq            #0xc229f0
    //     0xc229e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc229ec: stur            x4, [x0, #7]
    // 0xc229f0: StoreField: r2->field_13 = r0
    //     0xc229f0: stur            w0, [x2, #0x13]
    // 0xc229f4: str             x2, [SP]
    // 0xc229f8: r0 = _interpolate()
    //     0xc229f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc229fc: mov             x2, x0
    // 0xc22a00: ldur            x0, [fp, #-0x10]
    // 0xc22a04: stur            x2, [fp, #-8]
    // 0xc22a08: LoadField: r1 = r0->field_b
    //     0xc22a08: ldur            w1, [x0, #0xb]
    // 0xc22a0c: LoadField: r3 = r0->field_f
    //     0xc22a0c: ldur            w3, [x0, #0xf]
    // 0xc22a10: DecompressPointer r3
    //     0xc22a10: add             x3, x3, HEAP, lsl #32
    // 0xc22a14: LoadField: r4 = r3->field_b
    //     0xc22a14: ldur            w4, [x3, #0xb]
    // 0xc22a18: r3 = LoadInt32Instr(r1)
    //     0xc22a18: sbfx            x3, x1, #1, #0x1f
    // 0xc22a1c: stur            x3, [fp, #-0x18]
    // 0xc22a20: r1 = LoadInt32Instr(r4)
    //     0xc22a20: sbfx            x1, x4, #1, #0x1f
    // 0xc22a24: cmp             x3, x1
    // 0xc22a28: b.ne            #0xc22a34
    // 0xc22a2c: mov             x1, x0
    // 0xc22a30: r0 = _growToNextCapacity()
    //     0xc22a30: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc22a34: ldr             x4, [fp, #0x10]
    // 0xc22a38: ldur            x3, [fp, #-0x10]
    // 0xc22a3c: ldur            x2, [fp, #-0x18]
    // 0xc22a40: add             x0, x2, #1
    // 0xc22a44: lsl             x1, x0, #1
    // 0xc22a48: StoreField: r3->field_b = r1
    //     0xc22a48: stur            w1, [x3, #0xb]
    // 0xc22a4c: LoadField: r1 = r3->field_f
    //     0xc22a4c: ldur            w1, [x3, #0xf]
    // 0xc22a50: DecompressPointer r1
    //     0xc22a50: add             x1, x1, HEAP, lsl #32
    // 0xc22a54: ldur            x0, [fp, #-8]
    // 0xc22a58: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc22a58: add             x25, x1, x2, lsl #2
    //     0xc22a5c: add             x25, x25, #0xf
    //     0xc22a60: str             w0, [x25]
    //     0xc22a64: tbz             w0, #0, #0xc22a80
    //     0xc22a68: ldurb           w16, [x1, #-1]
    //     0xc22a6c: ldurb           w17, [x0, #-1]
    //     0xc22a70: and             x16, x17, x16, lsr #2
    //     0xc22a74: tst             x16, HEAP, lsr #32
    //     0xc22a78: b.eq            #0xc22a80
    //     0xc22a7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc22a80: LoadField: r0 = r4->field_23
    //     0xc22a80: ldur            w0, [x4, #0x23]
    // 0xc22a84: DecompressPointer r0
    //     0xc22a84: add             x0, x0, HEAP, lsl #32
    // 0xc22a88: stur            x0, [fp, #-8]
    // 0xc22a8c: cmp             w0, NULL
    // 0xc22a90: b.eq            #0xc22b3c
    // 0xc22a94: r1 = Null
    //     0xc22a94: mov             x1, NULL
    // 0xc22a98: r2 = 4
    //     0xc22a98: movz            x2, #0x4
    // 0xc22a9c: r0 = AllocateArray()
    //     0xc22a9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc22aa0: r16 = "rowStart="
    //     0xc22aa0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e48] "rowStart="
    //     0xc22aa4: ldr             x16, [x16, #0xe48]
    // 0xc22aa8: StoreField: r0->field_f = r16
    //     0xc22aa8: stur            w16, [x0, #0xf]
    // 0xc22aac: ldur            x1, [fp, #-8]
    // 0xc22ab0: StoreField: r0->field_13 = r1
    //     0xc22ab0: stur            w1, [x0, #0x13]
    // 0xc22ab4: str             x0, [SP]
    // 0xc22ab8: r0 = _interpolate()
    //     0xc22ab8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc22abc: mov             x2, x0
    // 0xc22ac0: ldur            x0, [fp, #-0x10]
    // 0xc22ac4: stur            x2, [fp, #-8]
    // 0xc22ac8: LoadField: r1 = r0->field_b
    //     0xc22ac8: ldur            w1, [x0, #0xb]
    // 0xc22acc: LoadField: r3 = r0->field_f
    //     0xc22acc: ldur            w3, [x0, #0xf]
    // 0xc22ad0: DecompressPointer r3
    //     0xc22ad0: add             x3, x3, HEAP, lsl #32
    // 0xc22ad4: LoadField: r4 = r3->field_b
    //     0xc22ad4: ldur            w4, [x3, #0xb]
    // 0xc22ad8: r3 = LoadInt32Instr(r1)
    //     0xc22ad8: sbfx            x3, x1, #1, #0x1f
    // 0xc22adc: stur            x3, [fp, #-0x18]
    // 0xc22ae0: r1 = LoadInt32Instr(r4)
    //     0xc22ae0: sbfx            x1, x4, #1, #0x1f
    // 0xc22ae4: cmp             x3, x1
    // 0xc22ae8: b.ne            #0xc22af4
    // 0xc22aec: mov             x1, x0
    // 0xc22af0: r0 = _growToNextCapacity()
    //     0xc22af0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc22af4: ldur            x3, [fp, #-0x10]
    // 0xc22af8: ldur            x2, [fp, #-0x18]
    // 0xc22afc: add             x0, x2, #1
    // 0xc22b00: lsl             x1, x0, #1
    // 0xc22b04: StoreField: r3->field_b = r1
    //     0xc22b04: stur            w1, [x3, #0xb]
    // 0xc22b08: LoadField: r1 = r3->field_f
    //     0xc22b08: ldur            w1, [x3, #0xf]
    // 0xc22b0c: DecompressPointer r1
    //     0xc22b0c: add             x1, x1, HEAP, lsl #32
    // 0xc22b10: ldur            x0, [fp, #-8]
    // 0xc22b14: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc22b14: add             x25, x1, x2, lsl #2
    //     0xc22b18: add             x25, x25, #0xf
    //     0xc22b1c: str             w0, [x25]
    //     0xc22b20: tbz             w0, #0, #0xc22b3c
    //     0xc22b24: ldurb           w16, [x1, #-1]
    //     0xc22b28: ldurb           w17, [x0, #-1]
    //     0xc22b2c: and             x16, x17, x16, lsr #2
    //     0xc22b30: tst             x16, HEAP, lsr #32
    //     0xc22b34: b.eq            #0xc22b3c
    //     0xc22b38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc22b3c: ldr             x0, [fp, #0x10]
    // 0xc22b40: r1 = Null
    //     0xc22b40: mov             x1, NULL
    // 0xc22b44: r2 = 4
    //     0xc22b44: movz            x2, #0x4
    // 0xc22b48: r0 = AllocateArray()
    //     0xc22b48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc22b4c: mov             x2, x0
    // 0xc22b50: r16 = "rowSpan="
    //     0xc22b50: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e50] "rowSpan="
    //     0xc22b54: ldr             x16, [x16, #0xe50]
    // 0xc22b58: StoreField: r2->field_f = r16
    //     0xc22b58: stur            w16, [x2, #0xf]
    // 0xc22b5c: ldr             x3, [fp, #0x10]
    // 0xc22b60: LoadField: r4 = r3->field_27
    //     0xc22b60: ldur            x4, [x3, #0x27]
    // 0xc22b64: r0 = BoxInt64Instr(r4)
    //     0xc22b64: sbfiz           x0, x4, #1, #0x1f
    //     0xc22b68: cmp             x4, x0, asr #1
    //     0xc22b6c: b.eq            #0xc22b78
    //     0xc22b70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc22b74: stur            x4, [x0, #7]
    // 0xc22b78: StoreField: r2->field_13 = r0
    //     0xc22b78: stur            w0, [x2, #0x13]
    // 0xc22b7c: str             x2, [SP]
    // 0xc22b80: r0 = _interpolate()
    //     0xc22b80: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc22b84: mov             x2, x0
    // 0xc22b88: ldur            x0, [fp, #-0x10]
    // 0xc22b8c: stur            x2, [fp, #-8]
    // 0xc22b90: LoadField: r1 = r0->field_b
    //     0xc22b90: ldur            w1, [x0, #0xb]
    // 0xc22b94: LoadField: r3 = r0->field_f
    //     0xc22b94: ldur            w3, [x0, #0xf]
    // 0xc22b98: DecompressPointer r3
    //     0xc22b98: add             x3, x3, HEAP, lsl #32
    // 0xc22b9c: LoadField: r4 = r3->field_b
    //     0xc22b9c: ldur            w4, [x3, #0xb]
    // 0xc22ba0: r3 = LoadInt32Instr(r1)
    //     0xc22ba0: sbfx            x3, x1, #1, #0x1f
    // 0xc22ba4: stur            x3, [fp, #-0x18]
    // 0xc22ba8: r1 = LoadInt32Instr(r4)
    //     0xc22ba8: sbfx            x1, x4, #1, #0x1f
    // 0xc22bac: cmp             x3, x1
    // 0xc22bb0: b.ne            #0xc22bbc
    // 0xc22bb4: mov             x1, x0
    // 0xc22bb8: r0 = _growToNextCapacity()
    //     0xc22bb8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc22bbc: ldur            x2, [fp, #-0x10]
    // 0xc22bc0: ldur            x3, [fp, #-0x18]
    // 0xc22bc4: add             x0, x3, #1
    // 0xc22bc8: lsl             x1, x0, #1
    // 0xc22bcc: StoreField: r2->field_b = r1
    //     0xc22bcc: stur            w1, [x2, #0xb]
    // 0xc22bd0: LoadField: r1 = r2->field_f
    //     0xc22bd0: ldur            w1, [x2, #0xf]
    // 0xc22bd4: DecompressPointer r1
    //     0xc22bd4: add             x1, x1, HEAP, lsl #32
    // 0xc22bd8: ldur            x0, [fp, #-8]
    // 0xc22bdc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc22bdc: add             x25, x1, x3, lsl #2
    //     0xc22be0: add             x25, x25, #0xf
    //     0xc22be4: str             w0, [x25]
    //     0xc22be8: tbz             w0, #0, #0xc22c04
    //     0xc22bec: ldurb           w16, [x1, #-1]
    //     0xc22bf0: ldurb           w17, [x0, #-1]
    //     0xc22bf4: and             x16, x17, x16, lsr #2
    //     0xc22bf8: tst             x16, HEAP, lsr #32
    //     0xc22bfc: b.eq            #0xc22c04
    //     0xc22c00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc22c04: ldr             x16, [fp, #0x10]
    // 0xc22c08: str             x16, [SP]
    // 0xc22c0c: r0 = toString()
    //     0xc22c0c: bl              #0xc467e8  ; [dart:core] Object::toString
    // 0xc22c10: mov             x2, x0
    // 0xc22c14: ldur            x0, [fp, #-0x10]
    // 0xc22c18: stur            x2, [fp, #-8]
    // 0xc22c1c: LoadField: r1 = r0->field_b
    //     0xc22c1c: ldur            w1, [x0, #0xb]
    // 0xc22c20: LoadField: r3 = r0->field_f
    //     0xc22c20: ldur            w3, [x0, #0xf]
    // 0xc22c24: DecompressPointer r3
    //     0xc22c24: add             x3, x3, HEAP, lsl #32
    // 0xc22c28: LoadField: r4 = r3->field_b
    //     0xc22c28: ldur            w4, [x3, #0xb]
    // 0xc22c2c: r3 = LoadInt32Instr(r1)
    //     0xc22c2c: sbfx            x3, x1, #1, #0x1f
    // 0xc22c30: stur            x3, [fp, #-0x18]
    // 0xc22c34: r1 = LoadInt32Instr(r4)
    //     0xc22c34: sbfx            x1, x4, #1, #0x1f
    // 0xc22c38: cmp             x3, x1
    // 0xc22c3c: b.ne            #0xc22c48
    // 0xc22c40: mov             x1, x0
    // 0xc22c44: r0 = _growToNextCapacity()
    //     0xc22c44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc22c48: ldur            x2, [fp, #-0x10]
    // 0xc22c4c: ldur            x3, [fp, #-0x18]
    // 0xc22c50: add             x0, x3, #1
    // 0xc22c54: lsl             x1, x0, #1
    // 0xc22c58: StoreField: r2->field_b = r1
    //     0xc22c58: stur            w1, [x2, #0xb]
    // 0xc22c5c: LoadField: r1 = r2->field_f
    //     0xc22c5c: ldur            w1, [x2, #0xf]
    // 0xc22c60: DecompressPointer r1
    //     0xc22c60: add             x1, x1, HEAP, lsl #32
    // 0xc22c64: ldur            x0, [fp, #-8]
    // 0xc22c68: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc22c68: add             x25, x1, x3, lsl #2
    //     0xc22c6c: add             x25, x25, #0xf
    //     0xc22c70: str             w0, [x25]
    //     0xc22c74: tbz             w0, #0, #0xc22c90
    //     0xc22c78: ldurb           w16, [x1, #-1]
    //     0xc22c7c: ldurb           w17, [x0, #-1]
    //     0xc22c80: and             x16, x17, x16, lsr #2
    //     0xc22c84: tst             x16, HEAP, lsr #32
    //     0xc22c88: b.eq            #0xc22c90
    //     0xc22c8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc22c90: r16 = "; "
    //     0xc22c90: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c3b8] "; "
    //     0xc22c94: ldr             x16, [x16, #0x3b8]
    // 0xc22c98: str             x16, [SP]
    // 0xc22c9c: mov             x1, x2
    // 0xc22ca0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc22ca0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc22ca4: r0 = join()
    //     0xc22ca4: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xc22ca8: LeaveFrame
    //     0xc22ca8: mov             SP, fp
    //     0xc22cac: ldp             fp, lr, [SP], #0x10
    // 0xc22cb0: ret
    //     0xc22cb0: ret             
    // 0xc22cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc22cb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc22cb8: b               #0xc228e0
  }
}

// class id: 6922, size: 0x14, field offset: 0x14
enum _IntrinsicDimension extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4b750, size: 0x64
    // 0xc4b750: EnterFrame
    //     0xc4b750: stp             fp, lr, [SP, #-0x10]!
    //     0xc4b754: mov             fp, SP
    // 0xc4b758: AllocStack(0x10)
    //     0xc4b758: sub             SP, SP, #0x10
    // 0xc4b75c: SetupParameters(_IntrinsicDimension this /* r1 => r0, fp-0x8 */)
    //     0xc4b75c: mov             x0, x1
    //     0xc4b760: stur            x1, [fp, #-8]
    // 0xc4b764: CheckStackOverflow
    //     0xc4b764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4b768: cmp             SP, x16
    //     0xc4b76c: b.ls            #0xc4b7ac
    // 0xc4b770: r1 = Null
    //     0xc4b770: mov             x1, NULL
    // 0xc4b774: r2 = 4
    //     0xc4b774: movz            x2, #0x4
    // 0xc4b778: r0 = AllocateArray()
    //     0xc4b778: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4b77c: r16 = "_IntrinsicDimension."
    //     0xc4b77c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e88] "_IntrinsicDimension."
    //     0xc4b780: ldr             x16, [x16, #0xe88]
    // 0xc4b784: StoreField: r0->field_f = r16
    //     0xc4b784: stur            w16, [x0, #0xf]
    // 0xc4b788: ldur            x1, [fp, #-8]
    // 0xc4b78c: LoadField: r2 = r1->field_f
    //     0xc4b78c: ldur            w2, [x1, #0xf]
    // 0xc4b790: DecompressPointer r2
    //     0xc4b790: add             x2, x2, HEAP, lsl #32
    // 0xc4b794: StoreField: r0->field_13 = r2
    //     0xc4b794: stur            w2, [x0, #0x13]
    // 0xc4b798: str             x0, [SP]
    // 0xc4b79c: r0 = _interpolate()
    //     0xc4b79c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4b7a0: LeaveFrame
    //     0xc4b7a0: mov             SP, fp
    //     0xc4b7a4: ldp             fp, lr, [SP], #0x10
    // 0xc4b7a8: ret
    //     0xc4b7a8: ret             
    // 0xc4b7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4b7ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4b7b0: b               #0xc4b770
  }
}
