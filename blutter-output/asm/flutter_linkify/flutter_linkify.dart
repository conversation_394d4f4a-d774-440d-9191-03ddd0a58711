// lib: , url: package:flutter_linkify/flutter_linkify.dart

// class id: 1049448, size: 0x8
class :: {

  static _ buildTextSpan(/* No info */) {
    // ** addr: 0xab91ec, size: 0x48
    // 0xab91ec: EnterFrame
    //     0xab91ec: stp             fp, lr, [SP, #-0x10]!
    //     0xab91f0: mov             fp, SP
    // 0xab91f4: AllocStack(0x8)
    //     0xab91f4: sub             SP, SP, #8
    // 0xab91f8: CheckStackOverflow
    //     0xab91f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab91fc: cmp             SP, x16
    //     0xab9200: b.ls            #0xab922c
    // 0xab9204: r0 = buildTextSpanChildren()
    //     0xab9204: bl              #0xab9234  ; [package:flutter_linkify/flutter_linkify.dart] ::buildTextSpanChildren
    // 0xab9208: stur            x0, [fp, #-8]
    // 0xab920c: r0 = TextSpan()
    //     0xab920c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xab9210: ldur            x1, [fp, #-8]
    // 0xab9214: StoreField: r0->field_f = r1
    //     0xab9214: stur            w1, [x0, #0xf]
    // 0xab9218: r1 = Instance__DeferringMouseCursor
    //     0xab9218: ldr             x1, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xab921c: ArrayStore: r0[0] = r1  ; List_4
    //     0xab921c: stur            w1, [x0, #0x17]
    // 0xab9220: LeaveFrame
    //     0xab9220: mov             SP, fp
    //     0xab9224: ldp             fp, lr, [SP], #0x10
    // 0xab9228: ret
    //     0xab9228: ret             
    // 0xab922c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab922c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab9230: b               #0xab9204
  }
  static _ buildTextSpanChildren(/* No info */) {
    // ** addr: 0xab9234, size: 0x31c
    // 0xab9234: EnterFrame
    //     0xab9234: stp             fp, lr, [SP, #-0x10]!
    //     0xab9238: mov             fp, SP
    // 0xab923c: AllocStack(0x60)
    //     0xab923c: sub             SP, SP, #0x60
    // 0xab9240: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xab9240: stur            x1, [fp, #-8]
    //     0xab9244: stur            x2, [fp, #-0x10]
    //     0xab9248: stur            x3, [fp, #-0x18]
    //     0xab924c: stur            x5, [fp, #-0x20]
    // 0xab9250: CheckStackOverflow
    //     0xab9250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab9254: cmp             SP, x16
    //     0xab9258: b.ls            #0xab9540
    // 0xab925c: r1 = 1
    //     0xab925c: movz            x1, #0x1
    // 0xab9260: r0 = AllocateContext()
    //     0xab9260: bl              #0xec126c  ; AllocateContextStub
    // 0xab9264: mov             x3, x0
    // 0xab9268: ldur            x0, [fp, #-0x18]
    // 0xab926c: stur            x3, [fp, #-0x28]
    // 0xab9270: StoreField: r3->field_f = r0
    //     0xab9270: stur            w0, [x3, #0xf]
    // 0xab9274: r1 = <InlineSpan>
    //     0xab9274: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xab9278: ldr             x1, [x1, #0x5f0]
    // 0xab927c: r2 = 0
    //     0xab927c: movz            x2, #0
    // 0xab9280: r0 = _GrowableList()
    //     0xab9280: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xab9284: mov             x1, x0
    // 0xab9288: ldur            x0, [fp, #-8]
    // 0xab928c: stur            x1, [fp, #-0x40]
    // 0xab9290: LoadField: r2 = r0->field_b
    //     0xab9290: ldur            w2, [x0, #0xb]
    // 0xab9294: r3 = LoadInt32Instr(r2)
    //     0xab9294: sbfx            x3, x2, #1, #0x1f
    // 0xab9298: stur            x3, [fp, #-0x38]
    // 0xab929c: r6 = 0
    //     0xab929c: movz            x6, #0
    // 0xab92a0: ldur            x5, [fp, #-0x10]
    // 0xab92a4: ldur            x4, [fp, #-0x20]
    // 0xab92a8: ldur            x2, [fp, #-0x28]
    // 0xab92ac: CheckStackOverflow
    //     0xab92ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab92b0: cmp             SP, x16
    //     0xab92b4: b.ls            #0xab9548
    // 0xab92b8: LoadField: r7 = r0->field_b
    //     0xab92b8: ldur            w7, [x0, #0xb]
    // 0xab92bc: r8 = LoadInt32Instr(r7)
    //     0xab92bc: sbfx            x8, x7, #1, #0x1f
    // 0xab92c0: cmp             x3, x8
    // 0xab92c4: b.ne            #0xab9524
    // 0xab92c8: cmp             x6, x8
    // 0xab92cc: b.ge            #0xab9510
    // 0xab92d0: LoadField: r7 = r0->field_f
    //     0xab92d0: ldur            w7, [x0, #0xf]
    // 0xab92d4: DecompressPointer r7
    //     0xab92d4: add             x7, x7, HEAP, lsl #32
    // 0xab92d8: ArrayLoad: r8 = r7[r6]  ; Unknown_4
    //     0xab92d8: add             x16, x7, x6, lsl #2
    //     0xab92dc: ldur            w8, [x16, #0xf]
    // 0xab92e0: DecompressPointer r8
    //     0xab92e0: add             x8, x8, HEAP, lsl #32
    // 0xab92e4: stur            x8, [fp, #-0x18]
    // 0xab92e8: add             x7, x6, #1
    // 0xab92ec: stur            x7, [fp, #-0x30]
    // 0xab92f0: r1 = 1
    //     0xab92f0: movz            x1, #0x1
    // 0xab92f4: r0 = AllocateContext()
    //     0xab92f4: bl              #0xec126c  ; AllocateContextStub
    // 0xab92f8: mov             x1, x0
    // 0xab92fc: ldur            x0, [fp, #-0x28]
    // 0xab9300: stur            x1, [fp, #-0x50]
    // 0xab9304: StoreField: r1->field_b = r0
    //     0xab9304: stur            w0, [x1, #0xb]
    // 0xab9308: ldur            x2, [fp, #-0x18]
    // 0xab930c: StoreField: r1->field_f = r2
    //     0xab930c: stur            w2, [x1, #0xf]
    // 0xab9310: r3 = LoadClassIdInstr(r2)
    //     0xab9310: ldur            x3, [x2, #-1]
    //     0xab9314: ubfx            x3, x3, #0xc, #0x14
    // 0xab9318: sub             x16, x3, #0x4bb
    // 0xab931c: cmp             x16, #1
    // 0xab9320: b.hi            #0xab944c
    // 0xab9324: ldur            x4, [fp, #-0x10]
    // 0xab9328: ldur            x3, [fp, #-0x40]
    // 0xab932c: LoadField: r5 = r2->field_7
    //     0xab932c: ldur            w5, [x2, #7]
    // 0xab9330: DecompressPointer r5
    //     0xab9330: add             x5, x5, HEAP, lsl #32
    // 0xab9334: stur            x5, [fp, #-0x48]
    // 0xab9338: r0 = TapGestureRecognizer()
    //     0xab9338: bl              #0x7632dc  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x84)
    // 0xab933c: mov             x4, x0
    // 0xab9340: r0 = false
    //     0xab9340: add             x0, NULL, #0x30  ; false
    // 0xab9344: stur            x4, [fp, #-0x58]
    // 0xab9348: StoreField: r4->field_47 = r0
    //     0xab9348: stur            w0, [x4, #0x47]
    // 0xab934c: StoreField: r4->field_4b = r0
    //     0xab934c: stur            w0, [x4, #0x4b]
    // 0xab9350: mov             x1, x4
    // 0xab9354: r2 = Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static.
    //     0xab9354: add             x2, PP, #0x25, lsl #12  ; [pp+0x253d8] Closure: (int) => bool from Function '_defaultButtonAcceptBehavior@433296176': static. (0x7e54fb8bbd8c)
    //     0xab9358: ldr             x2, [x2, #0x3d8]
    // 0xab935c: r3 = Instance_Duration
    //     0xab935c: ldr             x3, [PP, #0x6e28]  ; [pp+0x6e28] Obj!Duration@e3a0a1
    // 0xab9360: r5 = Null
    //     0xab9360: mov             x5, NULL
    // 0xab9364: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xab9364: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xab9368: r0 = PrimaryPointerGestureRecognizer()
    //     0xab9368: bl              #0x762f7c  ; [package:flutter/src/gestures/recognizer.dart] PrimaryPointerGestureRecognizer::PrimaryPointerGestureRecognizer
    // 0xab936c: ldur            x2, [fp, #-0x50]
    // 0xab9370: r1 = Function '<anonymous closure>': static.
    //     0xab9370: add             x1, PP, #0x51, lsl #12  ; [pp+0x51db8] AnonymousClosure: static (0xab9550), in [package:flutter_linkify/flutter_linkify.dart] ::buildTextSpanChildren (0xab9234)
    //     0xab9374: ldr             x1, [x1, #0xdb8]
    // 0xab9378: r0 = AllocateClosure()
    //     0xab9378: bl              #0xec1630  ; AllocateClosureStub
    // 0xab937c: ldur            x1, [fp, #-0x58]
    // 0xab9380: StoreField: r1->field_5f = r0
    //     0xab9380: stur            w0, [x1, #0x5f]
    //     0xab9384: ldurb           w16, [x1, #-1]
    //     0xab9388: ldurb           w17, [x0, #-1]
    //     0xab938c: and             x16, x17, x16, lsr #2
    //     0xab9390: tst             x16, HEAP, lsr #32
    //     0xab9394: b.eq            #0xab939c
    //     0xab9398: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab939c: r0 = TextSpan()
    //     0xab939c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xab93a0: mov             x2, x0
    // 0xab93a4: ldur            x0, [fp, #-0x48]
    // 0xab93a8: stur            x2, [fp, #-0x50]
    // 0xab93ac: StoreField: r2->field_b = r0
    //     0xab93ac: stur            w0, [x2, #0xb]
    // 0xab93b0: ldur            x0, [fp, #-0x58]
    // 0xab93b4: StoreField: r2->field_13 = r0
    //     0xab93b4: stur            w0, [x2, #0x13]
    // 0xab93b8: r0 = Instance_SystemMouseCursor
    //     0xab93b8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31bf0] Obj!SystemMouseCursor@e1cf01
    //     0xab93bc: ldr             x0, [x0, #0xbf0]
    // 0xab93c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xab93c0: stur            w0, [x2, #0x17]
    // 0xab93c4: ldur            x3, [fp, #-0x10]
    // 0xab93c8: StoreField: r2->field_7 = r3
    //     0xab93c8: stur            w3, [x2, #7]
    // 0xab93cc: ldur            x4, [fp, #-0x40]
    // 0xab93d0: LoadField: r1 = r4->field_b
    //     0xab93d0: ldur            w1, [x4, #0xb]
    // 0xab93d4: LoadField: r5 = r4->field_f
    //     0xab93d4: ldur            w5, [x4, #0xf]
    // 0xab93d8: DecompressPointer r5
    //     0xab93d8: add             x5, x5, HEAP, lsl #32
    // 0xab93dc: LoadField: r6 = r5->field_b
    //     0xab93dc: ldur            w6, [x5, #0xb]
    // 0xab93e0: r5 = LoadInt32Instr(r1)
    //     0xab93e0: sbfx            x5, x1, #1, #0x1f
    // 0xab93e4: stur            x5, [fp, #-0x60]
    // 0xab93e8: r1 = LoadInt32Instr(r6)
    //     0xab93e8: sbfx            x1, x6, #1, #0x1f
    // 0xab93ec: cmp             x5, x1
    // 0xab93f0: b.ne            #0xab93fc
    // 0xab93f4: mov             x1, x4
    // 0xab93f8: r0 = _growToNextCapacity()
    //     0xab93f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xab93fc: ldur            x3, [fp, #-0x40]
    // 0xab9400: ldur            x2, [fp, #-0x60]
    // 0xab9404: add             x0, x2, #1
    // 0xab9408: lsl             x1, x0, #1
    // 0xab940c: StoreField: r3->field_b = r1
    //     0xab940c: stur            w1, [x3, #0xb]
    // 0xab9410: LoadField: r1 = r3->field_f
    //     0xab9410: ldur            w1, [x3, #0xf]
    // 0xab9414: DecompressPointer r1
    //     0xab9414: add             x1, x1, HEAP, lsl #32
    // 0xab9418: ldur            x0, [fp, #-0x50]
    // 0xab941c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xab941c: add             x25, x1, x2, lsl #2
    //     0xab9420: add             x25, x25, #0xf
    //     0xab9424: str             w0, [x25]
    //     0xab9428: tbz             w0, #0, #0xab9444
    //     0xab942c: ldurb           w16, [x1, #-1]
    //     0xab9430: ldurb           w17, [x0, #-1]
    //     0xab9434: and             x16, x17, x16, lsr #2
    //     0xab9438: tst             x16, HEAP, lsr #32
    //     0xab943c: b.eq            #0xab9444
    //     0xab9440: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xab9444: mov             x2, x3
    // 0xab9448: b               #0xab94fc
    // 0xab944c: ldur            x0, [fp, #-0x20]
    // 0xab9450: ldur            x3, [fp, #-0x40]
    // 0xab9454: LoadField: r1 = r2->field_7
    //     0xab9454: ldur            w1, [x2, #7]
    // 0xab9458: DecompressPointer r1
    //     0xab9458: add             x1, x1, HEAP, lsl #32
    // 0xab945c: stur            x1, [fp, #-0x48]
    // 0xab9460: r0 = TextSpan()
    //     0xab9460: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xab9464: mov             x2, x0
    // 0xab9468: ldur            x0, [fp, #-0x48]
    // 0xab946c: stur            x2, [fp, #-0x18]
    // 0xab9470: StoreField: r2->field_b = r0
    //     0xab9470: stur            w0, [x2, #0xb]
    // 0xab9474: r0 = Instance__DeferringMouseCursor
    //     0xab9474: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xab9478: ArrayStore: r2[0] = r0  ; List_4
    //     0xab9478: stur            w0, [x2, #0x17]
    // 0xab947c: ldur            x3, [fp, #-0x20]
    // 0xab9480: StoreField: r2->field_7 = r3
    //     0xab9480: stur            w3, [x2, #7]
    // 0xab9484: ldur            x4, [fp, #-0x40]
    // 0xab9488: LoadField: r1 = r4->field_b
    //     0xab9488: ldur            w1, [x4, #0xb]
    // 0xab948c: LoadField: r5 = r4->field_f
    //     0xab948c: ldur            w5, [x4, #0xf]
    // 0xab9490: DecompressPointer r5
    //     0xab9490: add             x5, x5, HEAP, lsl #32
    // 0xab9494: LoadField: r6 = r5->field_b
    //     0xab9494: ldur            w6, [x5, #0xb]
    // 0xab9498: r5 = LoadInt32Instr(r1)
    //     0xab9498: sbfx            x5, x1, #1, #0x1f
    // 0xab949c: stur            x5, [fp, #-0x60]
    // 0xab94a0: r1 = LoadInt32Instr(r6)
    //     0xab94a0: sbfx            x1, x6, #1, #0x1f
    // 0xab94a4: cmp             x5, x1
    // 0xab94a8: b.ne            #0xab94b4
    // 0xab94ac: mov             x1, x4
    // 0xab94b0: r0 = _growToNextCapacity()
    //     0xab94b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xab94b4: ldur            x2, [fp, #-0x40]
    // 0xab94b8: ldur            x3, [fp, #-0x60]
    // 0xab94bc: add             x0, x3, #1
    // 0xab94c0: lsl             x1, x0, #1
    // 0xab94c4: StoreField: r2->field_b = r1
    //     0xab94c4: stur            w1, [x2, #0xb]
    // 0xab94c8: LoadField: r1 = r2->field_f
    //     0xab94c8: ldur            w1, [x2, #0xf]
    // 0xab94cc: DecompressPointer r1
    //     0xab94cc: add             x1, x1, HEAP, lsl #32
    // 0xab94d0: ldur            x0, [fp, #-0x18]
    // 0xab94d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xab94d4: add             x25, x1, x3, lsl #2
    //     0xab94d8: add             x25, x25, #0xf
    //     0xab94dc: str             w0, [x25]
    //     0xab94e0: tbz             w0, #0, #0xab94fc
    //     0xab94e4: ldurb           w16, [x1, #-1]
    //     0xab94e8: ldurb           w17, [x0, #-1]
    //     0xab94ec: and             x16, x17, x16, lsr #2
    //     0xab94f0: tst             x16, HEAP, lsr #32
    //     0xab94f4: b.eq            #0xab94fc
    //     0xab94f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xab94fc: ldur            x6, [fp, #-0x30]
    // 0xab9500: ldur            x0, [fp, #-8]
    // 0xab9504: mov             x1, x2
    // 0xab9508: ldur            x3, [fp, #-0x38]
    // 0xab950c: b               #0xab92a0
    // 0xab9510: mov             x2, x1
    // 0xab9514: mov             x0, x2
    // 0xab9518: LeaveFrame
    //     0xab9518: mov             SP, fp
    //     0xab951c: ldp             fp, lr, [SP], #0x10
    // 0xab9520: ret
    //     0xab9520: ret             
    // 0xab9524: r0 = ConcurrentModificationError()
    //     0xab9524: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xab9528: mov             x1, x0
    // 0xab952c: ldur            x0, [fp, #-8]
    // 0xab9530: StoreField: r1->field_b = r0
    //     0xab9530: stur            w0, [x1, #0xb]
    // 0xab9534: mov             x0, x1
    // 0xab9538: r0 = Throw()
    //     0xab9538: bl              #0xec04b8  ; ThrowStub
    // 0xab953c: brk             #0
    // 0xab9540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab9540: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab9544: b               #0xab925c
    // 0xab9548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab9548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab954c: b               #0xab92b8
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0xab9550, size: 0x64
    // 0xab9550: EnterFrame
    //     0xab9550: stp             fp, lr, [SP, #-0x10]!
    //     0xab9554: mov             fp, SP
    // 0xab9558: AllocStack(0x10)
    //     0xab9558: sub             SP, SP, #0x10
    // 0xab955c: SetupParameters()
    //     0xab955c: ldr             x0, [fp, #0x10]
    //     0xab9560: ldur            w1, [x0, #0x17]
    //     0xab9564: add             x1, x1, HEAP, lsl #32
    // 0xab9568: CheckStackOverflow
    //     0xab9568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab956c: cmp             SP, x16
    //     0xab9570: b.ls            #0xab95ac
    // 0xab9574: LoadField: r0 = r1->field_b
    //     0xab9574: ldur            w0, [x1, #0xb]
    // 0xab9578: DecompressPointer r0
    //     0xab9578: add             x0, x0, HEAP, lsl #32
    // 0xab957c: LoadField: r2 = r0->field_f
    //     0xab957c: ldur            w2, [x0, #0xf]
    // 0xab9580: DecompressPointer r2
    //     0xab9580: add             x2, x2, HEAP, lsl #32
    // 0xab9584: LoadField: r0 = r1->field_f
    //     0xab9584: ldur            w0, [x1, #0xf]
    // 0xab9588: DecompressPointer r0
    //     0xab9588: add             x0, x0, HEAP, lsl #32
    // 0xab958c: stp             x0, x2, [SP]
    // 0xab9590: mov             x0, x2
    // 0xab9594: ClosureCall
    //     0xab9594: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab9598: ldur            x2, [x0, #0x1f]
    //     0xab959c: blr             x2
    // 0xab95a0: LeaveFrame
    //     0xab95a0: mov             SP, fp
    //     0xab95a4: ldp             fp, lr, [SP], #0x10
    // 0xab95a8: ret
    //     0xab95a8: ret             
    // 0xab95ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab95ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab95b0: b               #0xab9574
  }
}

// class id: 5313, size: 0x54, field offset: 0xc
//   const constructor, 
class Linkify extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xab90c8, size: 0x124
    // 0xab90c8: EnterFrame
    //     0xab90c8: stp             fp, lr, [SP, #-0x10]!
    //     0xab90cc: mov             fp, SP
    // 0xab90d0: AllocStack(0x38)
    //     0xab90d0: sub             SP, SP, #0x38
    // 0xab90d4: SetupParameters(Linkify this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xab90d4: mov             x0, x2
    //     0xab90d8: stur            x2, [fp, #-0x10]
    //     0xab90dc: mov             x2, x1
    //     0xab90e0: stur            x1, [fp, #-8]
    // 0xab90e4: CheckStackOverflow
    //     0xab90e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab90e8: cmp             SP, x16
    //     0xab90ec: b.ls            #0xab91e4
    // 0xab90f0: LoadField: r1 = r2->field_b
    //     0xab90f0: ldur            w1, [x2, #0xb]
    // 0xab90f4: DecompressPointer r1
    //     0xab90f4: add             x1, x1, HEAP, lsl #32
    // 0xab90f8: r0 = linkify()
    //     0xab90f8: bl              #0xab95b4  ; [package:linkify/linkify.dart] ::linkify
    // 0xab90fc: ldur            x1, [fp, #-0x10]
    // 0xab9100: stur            x0, [fp, #-0x18]
    // 0xab9104: r0 = of()
    //     0xab9104: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9108: LoadField: r1 = r0->field_8f
    //     0xab9108: ldur            w1, [x0, #0x8f]
    // 0xab910c: DecompressPointer r1
    //     0xab910c: add             x1, x1, HEAP, lsl #32
    // 0xab9110: LoadField: r5 = r1->field_2f
    //     0xab9110: ldur            w5, [x1, #0x2f]
    // 0xab9114: DecompressPointer r5
    //     0xab9114: add             x5, x5, HEAP, lsl #32
    // 0xab9118: ldur            x0, [fp, #-8]
    // 0xab911c: stur            x5, [fp, #-0x28]
    // 0xab9120: LoadField: r3 = r0->field_13
    //     0xab9120: ldur            w3, [x0, #0x13]
    // 0xab9124: DecompressPointer r3
    //     0xab9124: add             x3, x3, HEAP, lsl #32
    // 0xab9128: ldur            x1, [fp, #-0x10]
    // 0xab912c: stur            x3, [fp, #-0x20]
    // 0xab9130: r0 = of()
    //     0xab9130: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab9134: LoadField: r1 = r0->field_8f
    //     0xab9134: ldur            w1, [x0, #0x8f]
    // 0xab9138: DecompressPointer r1
    //     0xab9138: add             x1, x1, HEAP, lsl #32
    // 0xab913c: LoadField: r0 = r1->field_2f
    //     0xab913c: ldur            w0, [x1, #0x2f]
    // 0xab9140: DecompressPointer r0
    //     0xab9140: add             x0, x0, HEAP, lsl #32
    // 0xab9144: cmp             w0, NULL
    // 0xab9148: b.ne            #0xab9154
    // 0xab914c: r2 = Null
    //     0xab914c: mov             x2, NULL
    // 0xab9150: b               #0xab9188
    // 0xab9154: r16 = Instance_MaterialAccentColor
    //     0xab9154: add             x16, PP, #0x51, lsl #12  ; [pp+0x51da8] Obj!MaterialAccentColor@e2ba71
    //     0xab9158: ldr             x16, [x16, #0xda8]
    // 0xab915c: r30 = Instance_TextDecoration
    //     0xab915c: add             lr, PP, #0x25, lsl #12  ; [pp+0x25c20] Obj!TextDecoration@e264b1
    //     0xab9160: ldr             lr, [lr, #0xc20]
    // 0xab9164: stp             lr, x16, [SP]
    // 0xab9168: mov             x1, x0
    // 0xab916c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, decoration, 0x2, null]
    //     0xab916c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51db0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "decoration", 0x2, Null]
    //     0xab9170: ldr             x4, [x4, #0xdb0]
    // 0xab9174: r0 = copyWith()
    //     0xab9174: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab9178: mov             x1, x0
    // 0xab917c: r2 = Null
    //     0xab917c: mov             x2, NULL
    // 0xab9180: r0 = merge()
    //     0xab9180: bl              #0x626f48  ; [package:flutter/src/painting/text_style.dart] TextStyle::merge
    // 0xab9184: mov             x2, x0
    // 0xab9188: ldur            x1, [fp, #-0x18]
    // 0xab918c: ldur            x3, [fp, #-0x20]
    // 0xab9190: ldur            x5, [fp, #-0x28]
    // 0xab9194: r0 = buildTextSpan()
    //     0xab9194: bl              #0xab91ec  ; [package:flutter_linkify/flutter_linkify.dart] ::buildTextSpan
    // 0xab9198: stur            x0, [fp, #-8]
    // 0xab919c: r0 = Text()
    //     0xab919c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xab91a0: ldur            x1, [fp, #-8]
    // 0xab91a4: StoreField: r0->field_f = r1
    //     0xab91a4: stur            w1, [x0, #0xf]
    // 0xab91a8: r1 = Instance_TextAlign
    //     0xab91a8: ldr             x1, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xab91ac: StoreField: r0->field_1b = r1
    //     0xab91ac: stur            w1, [x0, #0x1b]
    // 0xab91b0: r1 = true
    //     0xab91b0: add             x1, NULL, #0x20  ; true
    // 0xab91b4: StoreField: r0->field_27 = r1
    //     0xab91b4: stur            w1, [x0, #0x27]
    // 0xab91b8: r1 = Instance_TextOverflow
    //     0xab91b8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xab91bc: ldr             x1, [x1, #0xc60]
    // 0xab91c0: StoreField: r0->field_2b = r1
    //     0xab91c0: stur            w1, [x0, #0x2b]
    // 0xab91c4: r1 = 1.000000
    //     0xab91c4: ldr             x1, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xab91c8: StoreField: r0->field_2f = r1
    //     0xab91c8: stur            w1, [x0, #0x2f]
    // 0xab91cc: r1 = Instance_TextWidthBasis
    //     0xab91cc: add             x1, PP, #0x33, lsl #12  ; [pp+0x331d8] Obj!TextWidthBasis@e35c81
    //     0xab91d0: ldr             x1, [x1, #0x1d8]
    // 0xab91d4: StoreField: r0->field_3f = r1
    //     0xab91d4: stur            w1, [x0, #0x3f]
    // 0xab91d8: LeaveFrame
    //     0xab91d8: mov             SP, fp
    //     0xab91dc: ldp             fp, lr, [SP], #0x10
    // 0xab91e0: ret
    //     0xab91e0: ret             
    // 0xab91e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab91e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab91e8: b               #0xab90f0
  }
}
