// lib: , url: package:geocoding_platform_interface/src/models/placemark.dart

// class id: 1049511, size: 0x8
class :: {
}

// class id: 2215, size: 0x34, field offset: 0x8
//   const constructor, 
class Placemark extends Object {

  static _ fromMaps(/* No info */) {
    // ** addr: 0x9197a4, size: 0xd0
    // 0x9197a4: EnterFrame
    //     0x9197a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9197a8: mov             fp, SP
    // 0x9197ac: AllocStack(0x20)
    //     0x9197ac: sub             SP, SP, #0x20
    // 0x9197b0: CheckStackOverflow
    //     0x9197b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9197b4: cmp             SP, x16
    //     0x9197b8: b.ls            #0x91986c
    // 0x9197bc: cmp             w1, NULL
    // 0x9197c0: b.eq            #0x919844
    // 0x9197c4: r16 = <Placemark>
    //     0x9197c4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c490] TypeArguments: <Placemark>
    //     0x9197c8: ldr             x16, [x16, #0x490]
    // 0x9197cc: stp             x1, x16, [SP, #8]
    // 0x9197d0: r16 = Closure: (dynamic) => Placemark from Function 'fromMap': static.
    //     0x9197d0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c498] Closure: (dynamic) => Placemark from Function 'fromMap': static. (0x7e54fb319ab4)
    //     0x9197d4: ldr             x16, [x16, #0x498]
    // 0x9197d8: str             x16, [SP]
    // 0x9197dc: r4 = 0
    //     0x9197dc: movz            x4, #0
    // 0x9197e0: ldr             x0, [SP, #8]
    // 0x9197e4: r16 = UnlinkedCall_0x5f3c08
    //     0x9197e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c4a0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x9197e8: add             x16, x16, #0x4a0
    // 0x9197ec: ldp             x5, lr, [x16]
    // 0x9197f0: blr             lr
    // 0x9197f4: str             x0, [SP]
    // 0x9197f8: r4 = 0
    //     0x9197f8: movz            x4, #0
    // 0x9197fc: ldr             x0, [SP]
    // 0x919800: r16 = UnlinkedCall_0x5f3c08
    //     0x919800: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c4b0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x919804: add             x16, x16, #0x4b0
    // 0x919808: ldp             x5, lr, [x16]
    // 0x91980c: blr             lr
    // 0x919810: mov             x3, x0
    // 0x919814: r2 = Null
    //     0x919814: mov             x2, NULL
    // 0x919818: r1 = Null
    //     0x919818: mov             x1, NULL
    // 0x91981c: stur            x3, [fp, #-8]
    // 0x919820: r8 = List<Placemark>
    //     0x919820: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c4c0] Type: List<Placemark>
    //     0x919824: ldr             x8, [x8, #0x4c0]
    // 0x919828: r3 = Null
    //     0x919828: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c4c8] Null
    //     0x91982c: ldr             x3, [x3, #0x4c8]
    // 0x919830: r0 = List<Placemark>()
    //     0x919830: bl              #0x919a10  ; IsType_List<Placemark>_Stub
    // 0x919834: ldur            x0, [fp, #-8]
    // 0x919838: LeaveFrame
    //     0x919838: mov             SP, fp
    //     0x91983c: ldp             fp, lr, [SP], #0x10
    // 0x919840: ret
    //     0x919840: ret             
    // 0x919844: r0 = ArgumentError()
    //     0x919844: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x919848: mov             x1, x0
    // 0x91984c: r0 = "The parameter \'message\' should not be null."
    //     0x91984c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c4d8] "The parameter \'message\' should not be null."
    //     0x919850: ldr             x0, [x0, #0x4d8]
    // 0x919854: ArrayStore: r1[0] = r0  ; List_4
    //     0x919854: stur            w0, [x1, #0x17]
    // 0x919858: r0 = false
    //     0x919858: add             x0, NULL, #0x30  ; false
    // 0x91985c: StoreField: r1->field_b = r0
    //     0x91985c: stur            w0, [x1, #0xb]
    // 0x919860: mov             x0, x1
    // 0x919864: r0 = Throw()
    //     0x919864: bl              #0xec04b8  ; ThrowStub
    // 0x919868: brk             #0
    // 0x91986c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91986c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x919870: b               #0x9197bc
  }
  Map<String, dynamic> toJson(Placemark) {
    // ** addr: 0x91988c, size: 0x48
    // 0x91988c: EnterFrame
    //     0x91988c: stp             fp, lr, [SP, #-0x10]!
    //     0x919890: mov             fp, SP
    // 0x919894: CheckStackOverflow
    //     0x919894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x919898: cmp             SP, x16
    //     0x91989c: b.ls            #0x9198b4
    // 0x9198a0: ldr             x1, [fp, #0x10]
    // 0x9198a4: r0 = toJson()
    //     0x9198a4: bl              #0x9198bc  ; [package:geocoding_platform_interface/src/models/placemark.dart] Placemark::toJson
    // 0x9198a8: LeaveFrame
    //     0x9198a8: mov             SP, fp
    //     0x9198ac: ldp             fp, lr, [SP], #0x10
    // 0x9198b0: ret
    //     0x9198b0: ret             
    // 0x9198b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9198b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9198b8: b               #0x9198a0
  }
  Map<String, dynamic> toJson(Placemark) {
    // ** addr: 0x9198bc, size: 0x154
    // 0x9198bc: EnterFrame
    //     0x9198bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9198c0: mov             fp, SP
    // 0x9198c4: AllocStack(0x18)
    //     0x9198c4: sub             SP, SP, #0x18
    // 0x9198c8: SetupParameters(Placemark this /* r1 => r0, fp-0x8 */)
    //     0x9198c8: mov             x0, x1
    //     0x9198cc: stur            x1, [fp, #-8]
    // 0x9198d0: CheckStackOverflow
    //     0x9198d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9198d4: cmp             SP, x16
    //     0x9198d8: b.ls            #0x919a08
    // 0x9198dc: r1 = Null
    //     0x9198dc: mov             x1, NULL
    // 0x9198e0: r2 = 44
    //     0x9198e0: movz            x2, #0x2c
    // 0x9198e4: r0 = AllocateArray()
    //     0x9198e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9198e8: r16 = "name"
    //     0x9198e8: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x9198ec: StoreField: r0->field_f = r16
    //     0x9198ec: stur            w16, [x0, #0xf]
    // 0x9198f0: ldur            x1, [fp, #-8]
    // 0x9198f4: LoadField: r2 = r1->field_7
    //     0x9198f4: ldur            w2, [x1, #7]
    // 0x9198f8: DecompressPointer r2
    //     0x9198f8: add             x2, x2, HEAP, lsl #32
    // 0x9198fc: StoreField: r0->field_13 = r2
    //     0x9198fc: stur            w2, [x0, #0x13]
    // 0x919900: r16 = "street"
    //     0x919900: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c500] "street"
    //     0x919904: ldr             x16, [x16, #0x500]
    // 0x919908: ArrayStore: r0[0] = r16  ; List_4
    //     0x919908: stur            w16, [x0, #0x17]
    // 0x91990c: LoadField: r2 = r1->field_b
    //     0x91990c: ldur            w2, [x1, #0xb]
    // 0x919910: DecompressPointer r2
    //     0x919910: add             x2, x2, HEAP, lsl #32
    // 0x919914: StoreField: r0->field_1b = r2
    //     0x919914: stur            w2, [x0, #0x1b]
    // 0x919918: r16 = "isoCountryCode"
    //     0x919918: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c518] "isoCountryCode"
    //     0x91991c: ldr             x16, [x16, #0x518]
    // 0x919920: StoreField: r0->field_1f = r16
    //     0x919920: stur            w16, [x0, #0x1f]
    // 0x919924: LoadField: r2 = r1->field_f
    //     0x919924: ldur            w2, [x1, #0xf]
    // 0x919928: DecompressPointer r2
    //     0x919928: add             x2, x2, HEAP, lsl #32
    // 0x91992c: StoreField: r0->field_23 = r2
    //     0x91992c: stur            w2, [x0, #0x23]
    // 0x919930: r16 = "country"
    //     0x919930: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c530] "country"
    //     0x919934: ldr             x16, [x16, #0x530]
    // 0x919938: StoreField: r0->field_27 = r16
    //     0x919938: stur            w16, [x0, #0x27]
    // 0x91993c: LoadField: r2 = r1->field_13
    //     0x91993c: ldur            w2, [x1, #0x13]
    // 0x919940: DecompressPointer r2
    //     0x919940: add             x2, x2, HEAP, lsl #32
    // 0x919944: StoreField: r0->field_2b = r2
    //     0x919944: stur            w2, [x0, #0x2b]
    // 0x919948: r16 = "postalCode"
    //     0x919948: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c548] "postalCode"
    //     0x91994c: ldr             x16, [x16, #0x548]
    // 0x919950: StoreField: r0->field_2f = r16
    //     0x919950: stur            w16, [x0, #0x2f]
    // 0x919954: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x919954: ldur            w2, [x1, #0x17]
    // 0x919958: DecompressPointer r2
    //     0x919958: add             x2, x2, HEAP, lsl #32
    // 0x91995c: StoreField: r0->field_33 = r2
    //     0x91995c: stur            w2, [x0, #0x33]
    // 0x919960: r16 = "administrativeArea"
    //     0x919960: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c560] "administrativeArea"
    //     0x919964: ldr             x16, [x16, #0x560]
    // 0x919968: StoreField: r0->field_37 = r16
    //     0x919968: stur            w16, [x0, #0x37]
    // 0x91996c: LoadField: r2 = r1->field_1b
    //     0x91996c: ldur            w2, [x1, #0x1b]
    // 0x919970: DecompressPointer r2
    //     0x919970: add             x2, x2, HEAP, lsl #32
    // 0x919974: StoreField: r0->field_3b = r2
    //     0x919974: stur            w2, [x0, #0x3b]
    // 0x919978: r16 = "subAdministrativeArea"
    //     0x919978: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c578] "subAdministrativeArea"
    //     0x91997c: ldr             x16, [x16, #0x578]
    // 0x919980: StoreField: r0->field_3f = r16
    //     0x919980: stur            w16, [x0, #0x3f]
    // 0x919984: LoadField: r2 = r1->field_1f
    //     0x919984: ldur            w2, [x1, #0x1f]
    // 0x919988: DecompressPointer r2
    //     0x919988: add             x2, x2, HEAP, lsl #32
    // 0x91998c: StoreField: r0->field_43 = r2
    //     0x91998c: stur            w2, [x0, #0x43]
    // 0x919990: r16 = "locality"
    //     0x919990: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c440] "locality"
    //     0x919994: ldr             x16, [x16, #0x440]
    // 0x919998: StoreField: r0->field_47 = r16
    //     0x919998: stur            w16, [x0, #0x47]
    // 0x91999c: LoadField: r2 = r1->field_23
    //     0x91999c: ldur            w2, [x1, #0x23]
    // 0x9199a0: DecompressPointer r2
    //     0x9199a0: add             x2, x2, HEAP, lsl #32
    // 0x9199a4: StoreField: r0->field_4b = r2
    //     0x9199a4: stur            w2, [x0, #0x4b]
    // 0x9199a8: r16 = "subLocality"
    //     0x9199a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c5a0] "subLocality"
    //     0x9199ac: ldr             x16, [x16, #0x5a0]
    // 0x9199b0: StoreField: r0->field_4f = r16
    //     0x9199b0: stur            w16, [x0, #0x4f]
    // 0x9199b4: LoadField: r2 = r1->field_27
    //     0x9199b4: ldur            w2, [x1, #0x27]
    // 0x9199b8: DecompressPointer r2
    //     0x9199b8: add             x2, x2, HEAP, lsl #32
    // 0x9199bc: StoreField: r0->field_53 = r2
    //     0x9199bc: stur            w2, [x0, #0x53]
    // 0x9199c0: r16 = "thoroughfare"
    //     0x9199c0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c5b8] "thoroughfare"
    //     0x9199c4: ldr             x16, [x16, #0x5b8]
    // 0x9199c8: StoreField: r0->field_57 = r16
    //     0x9199c8: stur            w16, [x0, #0x57]
    // 0x9199cc: LoadField: r2 = r1->field_2b
    //     0x9199cc: ldur            w2, [x1, #0x2b]
    // 0x9199d0: DecompressPointer r2
    //     0x9199d0: add             x2, x2, HEAP, lsl #32
    // 0x9199d4: StoreField: r0->field_5b = r2
    //     0x9199d4: stur            w2, [x0, #0x5b]
    // 0x9199d8: r16 = "subThoroughfare"
    //     0x9199d8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c5d0] "subThoroughfare"
    //     0x9199dc: ldr             x16, [x16, #0x5d0]
    // 0x9199e0: StoreField: r0->field_5f = r16
    //     0x9199e0: stur            w16, [x0, #0x5f]
    // 0x9199e4: LoadField: r2 = r1->field_2f
    //     0x9199e4: ldur            w2, [x1, #0x2f]
    // 0x9199e8: DecompressPointer r2
    //     0x9199e8: add             x2, x2, HEAP, lsl #32
    // 0x9199ec: StoreField: r0->field_63 = r2
    //     0x9199ec: stur            w2, [x0, #0x63]
    // 0x9199f0: r16 = <String, dynamic>
    //     0x9199f0: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x9199f4: stp             x0, x16, [SP]
    // 0x9199f8: r0 = Map._fromLiteral()
    //     0x9199f8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x9199fc: LeaveFrame
    //     0x9199fc: mov             SP, fp
    //     0x919a00: ldp             fp, lr, [SP], #0x10
    // 0x919a04: ret
    //     0x919a04: ret             
    // 0x919a08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x919a08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x919a0c: b               #0x9198dc
  }
  [closure] static Placemark fromMap(dynamic, dynamic) {
    // ** addr: 0x919ab4, size: 0x30
    // 0x919ab4: EnterFrame
    //     0x919ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x919ab8: mov             fp, SP
    // 0x919abc: CheckStackOverflow
    //     0x919abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x919ac0: cmp             SP, x16
    //     0x919ac4: b.ls            #0x919adc
    // 0x919ac8: ldr             x1, [fp, #0x10]
    // 0x919acc: r0 = fromMap()
    //     0x919acc: bl              #0x919ae4  ; [package:geocoding_platform_interface/src/models/placemark.dart] Placemark::fromMap
    // 0x919ad0: LeaveFrame
    //     0x919ad0: mov             SP, fp
    //     0x919ad4: ldp             fp, lr, [SP], #0x10
    // 0x919ad8: ret
    //     0x919ad8: ret             
    // 0x919adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x919adc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x919ae0: b               #0x919ac8
  }
  static _ fromMap(/* No info */) {
    // ** addr: 0x919ae4, size: 0x628
    // 0x919ae4: EnterFrame
    //     0x919ae4: stp             fp, lr, [SP, #-0x10]!
    //     0x919ae8: mov             fp, SP
    // 0x919aec: AllocStack(0x58)
    //     0x919aec: sub             SP, SP, #0x58
    // 0x919af0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x919af0: mov             x3, x1
    //     0x919af4: stur            x1, [fp, #-8]
    // 0x919af8: CheckStackOverflow
    //     0x919af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x919afc: cmp             SP, x16
    //     0x919b00: b.ls            #0x91a104
    // 0x919b04: cmp             w3, NULL
    // 0x919b08: b.eq            #0x91a0dc
    // 0x919b0c: mov             x0, x3
    // 0x919b10: r2 = Null
    //     0x919b10: mov             x2, NULL
    // 0x919b14: r1 = Null
    //     0x919b14: mov             x1, NULL
    // 0x919b18: r8 = Map
    //     0x919b18: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x919b1c: r3 = Null
    //     0x919b1c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c4e0] Null
    //     0x919b20: ldr             x3, [x3, #0x4e0]
    // 0x919b24: r0 = Map()
    //     0x919b24: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x919b28: ldur            x3, [fp, #-8]
    // 0x919b2c: r0 = LoadClassIdInstr(r3)
    //     0x919b2c: ldur            x0, [x3, #-1]
    //     0x919b30: ubfx            x0, x0, #0xc, #0x14
    // 0x919b34: mov             x1, x3
    // 0x919b38: r2 = "name"
    //     0x919b38: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x919b3c: r0 = GDT[cid_x0 + -0x114]()
    //     0x919b3c: sub             lr, x0, #0x114
    //     0x919b40: ldr             lr, [x21, lr, lsl #3]
    //     0x919b44: blr             lr
    // 0x919b48: cmp             w0, NULL
    // 0x919b4c: b.ne            #0x919b58
    // 0x919b50: r4 = ""
    //     0x919b50: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919b54: b               #0x919b5c
    // 0x919b58: mov             x4, x0
    // 0x919b5c: ldur            x3, [fp, #-8]
    // 0x919b60: mov             x0, x4
    // 0x919b64: stur            x4, [fp, #-0x10]
    // 0x919b68: r2 = Null
    //     0x919b68: mov             x2, NULL
    // 0x919b6c: r1 = Null
    //     0x919b6c: mov             x1, NULL
    // 0x919b70: r4 = 60
    //     0x919b70: movz            x4, #0x3c
    // 0x919b74: branchIfSmi(r0, 0x919b80)
    //     0x919b74: tbz             w0, #0, #0x919b80
    // 0x919b78: r4 = LoadClassIdInstr(r0)
    //     0x919b78: ldur            x4, [x0, #-1]
    //     0x919b7c: ubfx            x4, x4, #0xc, #0x14
    // 0x919b80: sub             x4, x4, #0x5e
    // 0x919b84: cmp             x4, #1
    // 0x919b88: b.ls            #0x919b9c
    // 0x919b8c: r8 = String?
    //     0x919b8c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919b90: r3 = Null
    //     0x919b90: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c4f0] Null
    //     0x919b94: ldr             x3, [x3, #0x4f0]
    // 0x919b98: r0 = String?()
    //     0x919b98: bl              #0x600324  ; IsType_String?_Stub
    // 0x919b9c: ldur            x3, [fp, #-8]
    // 0x919ba0: r0 = LoadClassIdInstr(r3)
    //     0x919ba0: ldur            x0, [x3, #-1]
    //     0x919ba4: ubfx            x0, x0, #0xc, #0x14
    // 0x919ba8: mov             x1, x3
    // 0x919bac: r2 = "street"
    //     0x919bac: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c500] "street"
    //     0x919bb0: ldr             x2, [x2, #0x500]
    // 0x919bb4: r0 = GDT[cid_x0 + -0x114]()
    //     0x919bb4: sub             lr, x0, #0x114
    //     0x919bb8: ldr             lr, [x21, lr, lsl #3]
    //     0x919bbc: blr             lr
    // 0x919bc0: cmp             w0, NULL
    // 0x919bc4: b.ne            #0x919bd0
    // 0x919bc8: r4 = ""
    //     0x919bc8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919bcc: b               #0x919bd4
    // 0x919bd0: mov             x4, x0
    // 0x919bd4: ldur            x3, [fp, #-8]
    // 0x919bd8: mov             x0, x4
    // 0x919bdc: stur            x4, [fp, #-0x18]
    // 0x919be0: r2 = Null
    //     0x919be0: mov             x2, NULL
    // 0x919be4: r1 = Null
    //     0x919be4: mov             x1, NULL
    // 0x919be8: r4 = 60
    //     0x919be8: movz            x4, #0x3c
    // 0x919bec: branchIfSmi(r0, 0x919bf8)
    //     0x919bec: tbz             w0, #0, #0x919bf8
    // 0x919bf0: r4 = LoadClassIdInstr(r0)
    //     0x919bf0: ldur            x4, [x0, #-1]
    //     0x919bf4: ubfx            x4, x4, #0xc, #0x14
    // 0x919bf8: sub             x4, x4, #0x5e
    // 0x919bfc: cmp             x4, #1
    // 0x919c00: b.ls            #0x919c14
    // 0x919c04: r8 = String?
    //     0x919c04: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919c08: r3 = Null
    //     0x919c08: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c508] Null
    //     0x919c0c: ldr             x3, [x3, #0x508]
    // 0x919c10: r0 = String?()
    //     0x919c10: bl              #0x600324  ; IsType_String?_Stub
    // 0x919c14: ldur            x3, [fp, #-8]
    // 0x919c18: r0 = LoadClassIdInstr(r3)
    //     0x919c18: ldur            x0, [x3, #-1]
    //     0x919c1c: ubfx            x0, x0, #0xc, #0x14
    // 0x919c20: mov             x1, x3
    // 0x919c24: r2 = "isoCountryCode"
    //     0x919c24: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c518] "isoCountryCode"
    //     0x919c28: ldr             x2, [x2, #0x518]
    // 0x919c2c: r0 = GDT[cid_x0 + -0x114]()
    //     0x919c2c: sub             lr, x0, #0x114
    //     0x919c30: ldr             lr, [x21, lr, lsl #3]
    //     0x919c34: blr             lr
    // 0x919c38: cmp             w0, NULL
    // 0x919c3c: b.ne            #0x919c48
    // 0x919c40: r4 = ""
    //     0x919c40: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919c44: b               #0x919c4c
    // 0x919c48: mov             x4, x0
    // 0x919c4c: ldur            x3, [fp, #-8]
    // 0x919c50: mov             x0, x4
    // 0x919c54: stur            x4, [fp, #-0x20]
    // 0x919c58: r2 = Null
    //     0x919c58: mov             x2, NULL
    // 0x919c5c: r1 = Null
    //     0x919c5c: mov             x1, NULL
    // 0x919c60: r4 = 60
    //     0x919c60: movz            x4, #0x3c
    // 0x919c64: branchIfSmi(r0, 0x919c70)
    //     0x919c64: tbz             w0, #0, #0x919c70
    // 0x919c68: r4 = LoadClassIdInstr(r0)
    //     0x919c68: ldur            x4, [x0, #-1]
    //     0x919c6c: ubfx            x4, x4, #0xc, #0x14
    // 0x919c70: sub             x4, x4, #0x5e
    // 0x919c74: cmp             x4, #1
    // 0x919c78: b.ls            #0x919c8c
    // 0x919c7c: r8 = String?
    //     0x919c7c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919c80: r3 = Null
    //     0x919c80: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c520] Null
    //     0x919c84: ldr             x3, [x3, #0x520]
    // 0x919c88: r0 = String?()
    //     0x919c88: bl              #0x600324  ; IsType_String?_Stub
    // 0x919c8c: ldur            x3, [fp, #-8]
    // 0x919c90: r0 = LoadClassIdInstr(r3)
    //     0x919c90: ldur            x0, [x3, #-1]
    //     0x919c94: ubfx            x0, x0, #0xc, #0x14
    // 0x919c98: mov             x1, x3
    // 0x919c9c: r2 = "country"
    //     0x919c9c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c530] "country"
    //     0x919ca0: ldr             x2, [x2, #0x530]
    // 0x919ca4: r0 = GDT[cid_x0 + -0x114]()
    //     0x919ca4: sub             lr, x0, #0x114
    //     0x919ca8: ldr             lr, [x21, lr, lsl #3]
    //     0x919cac: blr             lr
    // 0x919cb0: cmp             w0, NULL
    // 0x919cb4: b.ne            #0x919cc0
    // 0x919cb8: r4 = ""
    //     0x919cb8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919cbc: b               #0x919cc4
    // 0x919cc0: mov             x4, x0
    // 0x919cc4: ldur            x3, [fp, #-8]
    // 0x919cc8: mov             x0, x4
    // 0x919ccc: stur            x4, [fp, #-0x28]
    // 0x919cd0: r2 = Null
    //     0x919cd0: mov             x2, NULL
    // 0x919cd4: r1 = Null
    //     0x919cd4: mov             x1, NULL
    // 0x919cd8: r4 = 60
    //     0x919cd8: movz            x4, #0x3c
    // 0x919cdc: branchIfSmi(r0, 0x919ce8)
    //     0x919cdc: tbz             w0, #0, #0x919ce8
    // 0x919ce0: r4 = LoadClassIdInstr(r0)
    //     0x919ce0: ldur            x4, [x0, #-1]
    //     0x919ce4: ubfx            x4, x4, #0xc, #0x14
    // 0x919ce8: sub             x4, x4, #0x5e
    // 0x919cec: cmp             x4, #1
    // 0x919cf0: b.ls            #0x919d04
    // 0x919cf4: r8 = String?
    //     0x919cf4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919cf8: r3 = Null
    //     0x919cf8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c538] Null
    //     0x919cfc: ldr             x3, [x3, #0x538]
    // 0x919d00: r0 = String?()
    //     0x919d00: bl              #0x600324  ; IsType_String?_Stub
    // 0x919d04: ldur            x3, [fp, #-8]
    // 0x919d08: r0 = LoadClassIdInstr(r3)
    //     0x919d08: ldur            x0, [x3, #-1]
    //     0x919d0c: ubfx            x0, x0, #0xc, #0x14
    // 0x919d10: mov             x1, x3
    // 0x919d14: r2 = "postalCode"
    //     0x919d14: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c548] "postalCode"
    //     0x919d18: ldr             x2, [x2, #0x548]
    // 0x919d1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x919d1c: sub             lr, x0, #0x114
    //     0x919d20: ldr             lr, [x21, lr, lsl #3]
    //     0x919d24: blr             lr
    // 0x919d28: cmp             w0, NULL
    // 0x919d2c: b.ne            #0x919d38
    // 0x919d30: r4 = ""
    //     0x919d30: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919d34: b               #0x919d3c
    // 0x919d38: mov             x4, x0
    // 0x919d3c: ldur            x3, [fp, #-8]
    // 0x919d40: mov             x0, x4
    // 0x919d44: stur            x4, [fp, #-0x30]
    // 0x919d48: r2 = Null
    //     0x919d48: mov             x2, NULL
    // 0x919d4c: r1 = Null
    //     0x919d4c: mov             x1, NULL
    // 0x919d50: r4 = 60
    //     0x919d50: movz            x4, #0x3c
    // 0x919d54: branchIfSmi(r0, 0x919d60)
    //     0x919d54: tbz             w0, #0, #0x919d60
    // 0x919d58: r4 = LoadClassIdInstr(r0)
    //     0x919d58: ldur            x4, [x0, #-1]
    //     0x919d5c: ubfx            x4, x4, #0xc, #0x14
    // 0x919d60: sub             x4, x4, #0x5e
    // 0x919d64: cmp             x4, #1
    // 0x919d68: b.ls            #0x919d7c
    // 0x919d6c: r8 = String?
    //     0x919d6c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919d70: r3 = Null
    //     0x919d70: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c550] Null
    //     0x919d74: ldr             x3, [x3, #0x550]
    // 0x919d78: r0 = String?()
    //     0x919d78: bl              #0x600324  ; IsType_String?_Stub
    // 0x919d7c: ldur            x3, [fp, #-8]
    // 0x919d80: r0 = LoadClassIdInstr(r3)
    //     0x919d80: ldur            x0, [x3, #-1]
    //     0x919d84: ubfx            x0, x0, #0xc, #0x14
    // 0x919d88: mov             x1, x3
    // 0x919d8c: r2 = "administrativeArea"
    //     0x919d8c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c560] "administrativeArea"
    //     0x919d90: ldr             x2, [x2, #0x560]
    // 0x919d94: r0 = GDT[cid_x0 + -0x114]()
    //     0x919d94: sub             lr, x0, #0x114
    //     0x919d98: ldr             lr, [x21, lr, lsl #3]
    //     0x919d9c: blr             lr
    // 0x919da0: cmp             w0, NULL
    // 0x919da4: b.ne            #0x919db0
    // 0x919da8: r4 = ""
    //     0x919da8: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919dac: b               #0x919db4
    // 0x919db0: mov             x4, x0
    // 0x919db4: ldur            x3, [fp, #-8]
    // 0x919db8: mov             x0, x4
    // 0x919dbc: stur            x4, [fp, #-0x38]
    // 0x919dc0: r2 = Null
    //     0x919dc0: mov             x2, NULL
    // 0x919dc4: r1 = Null
    //     0x919dc4: mov             x1, NULL
    // 0x919dc8: r4 = 60
    //     0x919dc8: movz            x4, #0x3c
    // 0x919dcc: branchIfSmi(r0, 0x919dd8)
    //     0x919dcc: tbz             w0, #0, #0x919dd8
    // 0x919dd0: r4 = LoadClassIdInstr(r0)
    //     0x919dd0: ldur            x4, [x0, #-1]
    //     0x919dd4: ubfx            x4, x4, #0xc, #0x14
    // 0x919dd8: sub             x4, x4, #0x5e
    // 0x919ddc: cmp             x4, #1
    // 0x919de0: b.ls            #0x919df4
    // 0x919de4: r8 = String?
    //     0x919de4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919de8: r3 = Null
    //     0x919de8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c568] Null
    //     0x919dec: ldr             x3, [x3, #0x568]
    // 0x919df0: r0 = String?()
    //     0x919df0: bl              #0x600324  ; IsType_String?_Stub
    // 0x919df4: ldur            x3, [fp, #-8]
    // 0x919df8: r0 = LoadClassIdInstr(r3)
    //     0x919df8: ldur            x0, [x3, #-1]
    //     0x919dfc: ubfx            x0, x0, #0xc, #0x14
    // 0x919e00: mov             x1, x3
    // 0x919e04: r2 = "subAdministrativeArea"
    //     0x919e04: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c578] "subAdministrativeArea"
    //     0x919e08: ldr             x2, [x2, #0x578]
    // 0x919e0c: r0 = GDT[cid_x0 + -0x114]()
    //     0x919e0c: sub             lr, x0, #0x114
    //     0x919e10: ldr             lr, [x21, lr, lsl #3]
    //     0x919e14: blr             lr
    // 0x919e18: cmp             w0, NULL
    // 0x919e1c: b.ne            #0x919e28
    // 0x919e20: r4 = ""
    //     0x919e20: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919e24: b               #0x919e2c
    // 0x919e28: mov             x4, x0
    // 0x919e2c: ldur            x3, [fp, #-8]
    // 0x919e30: mov             x0, x4
    // 0x919e34: stur            x4, [fp, #-0x40]
    // 0x919e38: r2 = Null
    //     0x919e38: mov             x2, NULL
    // 0x919e3c: r1 = Null
    //     0x919e3c: mov             x1, NULL
    // 0x919e40: r4 = 60
    //     0x919e40: movz            x4, #0x3c
    // 0x919e44: branchIfSmi(r0, 0x919e50)
    //     0x919e44: tbz             w0, #0, #0x919e50
    // 0x919e48: r4 = LoadClassIdInstr(r0)
    //     0x919e48: ldur            x4, [x0, #-1]
    //     0x919e4c: ubfx            x4, x4, #0xc, #0x14
    // 0x919e50: sub             x4, x4, #0x5e
    // 0x919e54: cmp             x4, #1
    // 0x919e58: b.ls            #0x919e6c
    // 0x919e5c: r8 = String?
    //     0x919e5c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919e60: r3 = Null
    //     0x919e60: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c580] Null
    //     0x919e64: ldr             x3, [x3, #0x580]
    // 0x919e68: r0 = String?()
    //     0x919e68: bl              #0x600324  ; IsType_String?_Stub
    // 0x919e6c: ldur            x3, [fp, #-8]
    // 0x919e70: r0 = LoadClassIdInstr(r3)
    //     0x919e70: ldur            x0, [x3, #-1]
    //     0x919e74: ubfx            x0, x0, #0xc, #0x14
    // 0x919e78: mov             x1, x3
    // 0x919e7c: r2 = "locality"
    //     0x919e7c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c440] "locality"
    //     0x919e80: ldr             x2, [x2, #0x440]
    // 0x919e84: r0 = GDT[cid_x0 + -0x114]()
    //     0x919e84: sub             lr, x0, #0x114
    //     0x919e88: ldr             lr, [x21, lr, lsl #3]
    //     0x919e8c: blr             lr
    // 0x919e90: cmp             w0, NULL
    // 0x919e94: b.ne            #0x919ea0
    // 0x919e98: r4 = ""
    //     0x919e98: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919e9c: b               #0x919ea4
    // 0x919ea0: mov             x4, x0
    // 0x919ea4: ldur            x3, [fp, #-8]
    // 0x919ea8: mov             x0, x4
    // 0x919eac: stur            x4, [fp, #-0x48]
    // 0x919eb0: r2 = Null
    //     0x919eb0: mov             x2, NULL
    // 0x919eb4: r1 = Null
    //     0x919eb4: mov             x1, NULL
    // 0x919eb8: r4 = 60
    //     0x919eb8: movz            x4, #0x3c
    // 0x919ebc: branchIfSmi(r0, 0x919ec8)
    //     0x919ebc: tbz             w0, #0, #0x919ec8
    // 0x919ec0: r4 = LoadClassIdInstr(r0)
    //     0x919ec0: ldur            x4, [x0, #-1]
    //     0x919ec4: ubfx            x4, x4, #0xc, #0x14
    // 0x919ec8: sub             x4, x4, #0x5e
    // 0x919ecc: cmp             x4, #1
    // 0x919ed0: b.ls            #0x919ee4
    // 0x919ed4: r8 = String?
    //     0x919ed4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919ed8: r3 = Null
    //     0x919ed8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c590] Null
    //     0x919edc: ldr             x3, [x3, #0x590]
    // 0x919ee0: r0 = String?()
    //     0x919ee0: bl              #0x600324  ; IsType_String?_Stub
    // 0x919ee4: ldur            x3, [fp, #-8]
    // 0x919ee8: r0 = LoadClassIdInstr(r3)
    //     0x919ee8: ldur            x0, [x3, #-1]
    //     0x919eec: ubfx            x0, x0, #0xc, #0x14
    // 0x919ef0: mov             x1, x3
    // 0x919ef4: r2 = "subLocality"
    //     0x919ef4: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c5a0] "subLocality"
    //     0x919ef8: ldr             x2, [x2, #0x5a0]
    // 0x919efc: r0 = GDT[cid_x0 + -0x114]()
    //     0x919efc: sub             lr, x0, #0x114
    //     0x919f00: ldr             lr, [x21, lr, lsl #3]
    //     0x919f04: blr             lr
    // 0x919f08: cmp             w0, NULL
    // 0x919f0c: b.ne            #0x919f18
    // 0x919f10: r4 = ""
    //     0x919f10: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919f14: b               #0x919f1c
    // 0x919f18: mov             x4, x0
    // 0x919f1c: ldur            x3, [fp, #-8]
    // 0x919f20: mov             x0, x4
    // 0x919f24: stur            x4, [fp, #-0x50]
    // 0x919f28: r2 = Null
    //     0x919f28: mov             x2, NULL
    // 0x919f2c: r1 = Null
    //     0x919f2c: mov             x1, NULL
    // 0x919f30: r4 = 60
    //     0x919f30: movz            x4, #0x3c
    // 0x919f34: branchIfSmi(r0, 0x919f40)
    //     0x919f34: tbz             w0, #0, #0x919f40
    // 0x919f38: r4 = LoadClassIdInstr(r0)
    //     0x919f38: ldur            x4, [x0, #-1]
    //     0x919f3c: ubfx            x4, x4, #0xc, #0x14
    // 0x919f40: sub             x4, x4, #0x5e
    // 0x919f44: cmp             x4, #1
    // 0x919f48: b.ls            #0x919f5c
    // 0x919f4c: r8 = String?
    //     0x919f4c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919f50: r3 = Null
    //     0x919f50: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c5a8] Null
    //     0x919f54: ldr             x3, [x3, #0x5a8]
    // 0x919f58: r0 = String?()
    //     0x919f58: bl              #0x600324  ; IsType_String?_Stub
    // 0x919f5c: ldur            x3, [fp, #-8]
    // 0x919f60: r0 = LoadClassIdInstr(r3)
    //     0x919f60: ldur            x0, [x3, #-1]
    //     0x919f64: ubfx            x0, x0, #0xc, #0x14
    // 0x919f68: mov             x1, x3
    // 0x919f6c: r2 = "thoroughfare"
    //     0x919f6c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c5b8] "thoroughfare"
    //     0x919f70: ldr             x2, [x2, #0x5b8]
    // 0x919f74: r0 = GDT[cid_x0 + -0x114]()
    //     0x919f74: sub             lr, x0, #0x114
    //     0x919f78: ldr             lr, [x21, lr, lsl #3]
    //     0x919f7c: blr             lr
    // 0x919f80: cmp             w0, NULL
    // 0x919f84: b.ne            #0x919f90
    // 0x919f88: r4 = ""
    //     0x919f88: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x919f8c: b               #0x919f94
    // 0x919f90: mov             x4, x0
    // 0x919f94: ldur            x3, [fp, #-8]
    // 0x919f98: mov             x0, x4
    // 0x919f9c: stur            x4, [fp, #-0x58]
    // 0x919fa0: r2 = Null
    //     0x919fa0: mov             x2, NULL
    // 0x919fa4: r1 = Null
    //     0x919fa4: mov             x1, NULL
    // 0x919fa8: r4 = 60
    //     0x919fa8: movz            x4, #0x3c
    // 0x919fac: branchIfSmi(r0, 0x919fb8)
    //     0x919fac: tbz             w0, #0, #0x919fb8
    // 0x919fb0: r4 = LoadClassIdInstr(r0)
    //     0x919fb0: ldur            x4, [x0, #-1]
    //     0x919fb4: ubfx            x4, x4, #0xc, #0x14
    // 0x919fb8: sub             x4, x4, #0x5e
    // 0x919fbc: cmp             x4, #1
    // 0x919fc0: b.ls            #0x919fd4
    // 0x919fc4: r8 = String?
    //     0x919fc4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x919fc8: r3 = Null
    //     0x919fc8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c5c0] Null
    //     0x919fcc: ldr             x3, [x3, #0x5c0]
    // 0x919fd0: r0 = String?()
    //     0x919fd0: bl              #0x600324  ; IsType_String?_Stub
    // 0x919fd4: ldur            x1, [fp, #-8]
    // 0x919fd8: r0 = LoadClassIdInstr(r1)
    //     0x919fd8: ldur            x0, [x1, #-1]
    //     0x919fdc: ubfx            x0, x0, #0xc, #0x14
    // 0x919fe0: r2 = "subThoroughfare"
    //     0x919fe0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c5d0] "subThoroughfare"
    //     0x919fe4: ldr             x2, [x2, #0x5d0]
    // 0x919fe8: r0 = GDT[cid_x0 + -0x114]()
    //     0x919fe8: sub             lr, x0, #0x114
    //     0x919fec: ldr             lr, [x21, lr, lsl #3]
    //     0x919ff0: blr             lr
    // 0x919ff4: cmp             w0, NULL
    // 0x919ff8: b.ne            #0x91a004
    // 0x919ffc: r13 = ""
    //     0x919ffc: ldr             x13, [PP, #0x288]  ; [pp+0x288] ""
    // 0x91a000: b               #0x91a008
    // 0x91a004: mov             x13, x0
    // 0x91a008: ldur            x12, [fp, #-0x10]
    // 0x91a00c: ldur            x11, [fp, #-0x18]
    // 0x91a010: ldur            x10, [fp, #-0x20]
    // 0x91a014: ldur            x9, [fp, #-0x28]
    // 0x91a018: ldur            x8, [fp, #-0x30]
    // 0x91a01c: ldur            x7, [fp, #-0x38]
    // 0x91a020: ldur            x6, [fp, #-0x40]
    // 0x91a024: ldur            x5, [fp, #-0x48]
    // 0x91a028: ldur            x4, [fp, #-0x50]
    // 0x91a02c: ldur            x3, [fp, #-0x58]
    // 0x91a030: mov             x0, x13
    // 0x91a034: stur            x13, [fp, #-8]
    // 0x91a038: r2 = Null
    //     0x91a038: mov             x2, NULL
    // 0x91a03c: r1 = Null
    //     0x91a03c: mov             x1, NULL
    // 0x91a040: r4 = 60
    //     0x91a040: movz            x4, #0x3c
    // 0x91a044: branchIfSmi(r0, 0x91a050)
    //     0x91a044: tbz             w0, #0, #0x91a050
    // 0x91a048: r4 = LoadClassIdInstr(r0)
    //     0x91a048: ldur            x4, [x0, #-1]
    //     0x91a04c: ubfx            x4, x4, #0xc, #0x14
    // 0x91a050: sub             x4, x4, #0x5e
    // 0x91a054: cmp             x4, #1
    // 0x91a058: b.ls            #0x91a06c
    // 0x91a05c: r8 = String?
    //     0x91a05c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91a060: r3 = Null
    //     0x91a060: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2c5d8] Null
    //     0x91a064: ldr             x3, [x3, #0x5d8]
    // 0x91a068: r0 = String?()
    //     0x91a068: bl              #0x600324  ; IsType_String?_Stub
    // 0x91a06c: r0 = Placemark()
    //     0x91a06c: bl              #0x91a10c  ; AllocatePlacemarkStub -> Placemark (size=0x34)
    // 0x91a070: mov             x1, x0
    // 0x91a074: ldur            x0, [fp, #-0x10]
    // 0x91a078: StoreField: r1->field_7 = r0
    //     0x91a078: stur            w0, [x1, #7]
    // 0x91a07c: ldur            x0, [fp, #-0x18]
    // 0x91a080: StoreField: r1->field_b = r0
    //     0x91a080: stur            w0, [x1, #0xb]
    // 0x91a084: ldur            x0, [fp, #-0x20]
    // 0x91a088: StoreField: r1->field_f = r0
    //     0x91a088: stur            w0, [x1, #0xf]
    // 0x91a08c: ldur            x0, [fp, #-0x28]
    // 0x91a090: StoreField: r1->field_13 = r0
    //     0x91a090: stur            w0, [x1, #0x13]
    // 0x91a094: ldur            x0, [fp, #-0x30]
    // 0x91a098: ArrayStore: r1[0] = r0  ; List_4
    //     0x91a098: stur            w0, [x1, #0x17]
    // 0x91a09c: ldur            x0, [fp, #-0x38]
    // 0x91a0a0: StoreField: r1->field_1b = r0
    //     0x91a0a0: stur            w0, [x1, #0x1b]
    // 0x91a0a4: ldur            x0, [fp, #-0x40]
    // 0x91a0a8: StoreField: r1->field_1f = r0
    //     0x91a0a8: stur            w0, [x1, #0x1f]
    // 0x91a0ac: ldur            x0, [fp, #-0x48]
    // 0x91a0b0: StoreField: r1->field_23 = r0
    //     0x91a0b0: stur            w0, [x1, #0x23]
    // 0x91a0b4: ldur            x0, [fp, #-0x50]
    // 0x91a0b8: StoreField: r1->field_27 = r0
    //     0x91a0b8: stur            w0, [x1, #0x27]
    // 0x91a0bc: ldur            x0, [fp, #-0x58]
    // 0x91a0c0: StoreField: r1->field_2b = r0
    //     0x91a0c0: stur            w0, [x1, #0x2b]
    // 0x91a0c4: ldur            x0, [fp, #-8]
    // 0x91a0c8: StoreField: r1->field_2f = r0
    //     0x91a0c8: stur            w0, [x1, #0x2f]
    // 0x91a0cc: mov             x0, x1
    // 0x91a0d0: LeaveFrame
    //     0x91a0d0: mov             SP, fp
    //     0x91a0d4: ldp             fp, lr, [SP], #0x10
    // 0x91a0d8: ret
    //     0x91a0d8: ret             
    // 0x91a0dc: r0 = ArgumentError()
    //     0x91a0dc: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x91a0e0: mov             x1, x0
    // 0x91a0e4: r0 = "The parameter \'message\' should not be null."
    //     0x91a0e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c4d8] "The parameter \'message\' should not be null."
    //     0x91a0e8: ldr             x0, [x0, #0x4d8]
    // 0x91a0ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x91a0ec: stur            w0, [x1, #0x17]
    // 0x91a0f0: r0 = false
    //     0x91a0f0: add             x0, NULL, #0x30  ; false
    // 0x91a0f4: StoreField: r1->field_b = r0
    //     0x91a0f4: stur            w0, [x1, #0xb]
    // 0x91a0f8: mov             x0, x1
    // 0x91a0fc: r0 = Throw()
    //     0x91a0fc: bl              #0xec04b8  ; ThrowStub
    // 0x91a100: brk             #0
    // 0x91a104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91a104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91a108: b               #0x919b04
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbee454, size: 0x2bc
    // 0xbee454: EnterFrame
    //     0xbee454: stp             fp, lr, [SP, #-0x10]!
    //     0xbee458: mov             fp, SP
    // 0xbee45c: AllocStack(0x20)
    //     0xbee45c: sub             SP, SP, #0x20
    // 0xbee460: CheckStackOverflow
    //     0xbee460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbee464: cmp             SP, x16
    //     0xbee468: b.ls            #0xbee708
    // 0xbee46c: ldr             x1, [fp, #0x10]
    // 0xbee470: LoadField: r0 = r1->field_1b
    //     0xbee470: ldur            w0, [x1, #0x1b]
    // 0xbee474: DecompressPointer r0
    //     0xbee474: add             x0, x0, HEAP, lsl #32
    // 0xbee478: r2 = LoadClassIdInstr(r0)
    //     0xbee478: ldur            x2, [x0, #-1]
    //     0xbee47c: ubfx            x2, x2, #0xc, #0x14
    // 0xbee480: str             x0, [SP]
    // 0xbee484: mov             x0, x2
    // 0xbee488: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee488: movz            x17, #0x64af
    //     0xbee48c: add             lr, x0, x17
    //     0xbee490: ldr             lr, [x21, lr, lsl #3]
    //     0xbee494: blr             lr
    // 0xbee498: mov             x2, x0
    // 0xbee49c: ldr             x1, [fp, #0x10]
    // 0xbee4a0: stur            x2, [fp, #-8]
    // 0xbee4a4: LoadField: r0 = r1->field_13
    //     0xbee4a4: ldur            w0, [x1, #0x13]
    // 0xbee4a8: DecompressPointer r0
    //     0xbee4a8: add             x0, x0, HEAP, lsl #32
    // 0xbee4ac: r3 = LoadClassIdInstr(r0)
    //     0xbee4ac: ldur            x3, [x0, #-1]
    //     0xbee4b0: ubfx            x3, x3, #0xc, #0x14
    // 0xbee4b4: str             x0, [SP]
    // 0xbee4b8: mov             x0, x3
    // 0xbee4bc: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee4bc: movz            x17, #0x64af
    //     0xbee4c0: add             lr, x0, x17
    //     0xbee4c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbee4c8: blr             lr
    // 0xbee4cc: mov             x1, x0
    // 0xbee4d0: ldur            x0, [fp, #-8]
    // 0xbee4d4: r2 = LoadInt32Instr(r0)
    //     0xbee4d4: sbfx            x2, x0, #1, #0x1f
    // 0xbee4d8: r0 = LoadInt32Instr(r1)
    //     0xbee4d8: sbfx            x0, x1, #1, #0x1f
    // 0xbee4dc: eor             x1, x2, x0
    // 0xbee4e0: ldr             x2, [fp, #0x10]
    // 0xbee4e4: stur            x1, [fp, #-0x10]
    // 0xbee4e8: LoadField: r0 = r2->field_f
    //     0xbee4e8: ldur            w0, [x2, #0xf]
    // 0xbee4ec: DecompressPointer r0
    //     0xbee4ec: add             x0, x0, HEAP, lsl #32
    // 0xbee4f0: r3 = LoadClassIdInstr(r0)
    //     0xbee4f0: ldur            x3, [x0, #-1]
    //     0xbee4f4: ubfx            x3, x3, #0xc, #0x14
    // 0xbee4f8: str             x0, [SP]
    // 0xbee4fc: mov             x0, x3
    // 0xbee500: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee500: movz            x17, #0x64af
    //     0xbee504: add             lr, x0, x17
    //     0xbee508: ldr             lr, [x21, lr, lsl #3]
    //     0xbee50c: blr             lr
    // 0xbee510: r1 = LoadInt32Instr(r0)
    //     0xbee510: sbfx            x1, x0, #1, #0x1f
    // 0xbee514: ldur            x0, [fp, #-0x10]
    // 0xbee518: eor             x2, x0, x1
    // 0xbee51c: ldr             x1, [fp, #0x10]
    // 0xbee520: stur            x2, [fp, #-0x18]
    // 0xbee524: LoadField: r0 = r1->field_23
    //     0xbee524: ldur            w0, [x1, #0x23]
    // 0xbee528: DecompressPointer r0
    //     0xbee528: add             x0, x0, HEAP, lsl #32
    // 0xbee52c: r3 = LoadClassIdInstr(r0)
    //     0xbee52c: ldur            x3, [x0, #-1]
    //     0xbee530: ubfx            x3, x3, #0xc, #0x14
    // 0xbee534: str             x0, [SP]
    // 0xbee538: mov             x0, x3
    // 0xbee53c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee53c: movz            x17, #0x64af
    //     0xbee540: add             lr, x0, x17
    //     0xbee544: ldr             lr, [x21, lr, lsl #3]
    //     0xbee548: blr             lr
    // 0xbee54c: r1 = LoadInt32Instr(r0)
    //     0xbee54c: sbfx            x1, x0, #1, #0x1f
    // 0xbee550: ldur            x0, [fp, #-0x18]
    // 0xbee554: eor             x2, x0, x1
    // 0xbee558: ldr             x1, [fp, #0x10]
    // 0xbee55c: stur            x2, [fp, #-0x10]
    // 0xbee560: LoadField: r0 = r1->field_7
    //     0xbee560: ldur            w0, [x1, #7]
    // 0xbee564: DecompressPointer r0
    //     0xbee564: add             x0, x0, HEAP, lsl #32
    // 0xbee568: r3 = LoadClassIdInstr(r0)
    //     0xbee568: ldur            x3, [x0, #-1]
    //     0xbee56c: ubfx            x3, x3, #0xc, #0x14
    // 0xbee570: str             x0, [SP]
    // 0xbee574: mov             x0, x3
    // 0xbee578: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee578: movz            x17, #0x64af
    //     0xbee57c: add             lr, x0, x17
    //     0xbee580: ldr             lr, [x21, lr, lsl #3]
    //     0xbee584: blr             lr
    // 0xbee588: r1 = LoadInt32Instr(r0)
    //     0xbee588: sbfx            x1, x0, #1, #0x1f
    // 0xbee58c: ldur            x0, [fp, #-0x10]
    // 0xbee590: eor             x2, x0, x1
    // 0xbee594: ldr             x1, [fp, #0x10]
    // 0xbee598: stur            x2, [fp, #-0x18]
    // 0xbee59c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbee59c: ldur            w0, [x1, #0x17]
    // 0xbee5a0: DecompressPointer r0
    //     0xbee5a0: add             x0, x0, HEAP, lsl #32
    // 0xbee5a4: r3 = LoadClassIdInstr(r0)
    //     0xbee5a4: ldur            x3, [x0, #-1]
    //     0xbee5a8: ubfx            x3, x3, #0xc, #0x14
    // 0xbee5ac: str             x0, [SP]
    // 0xbee5b0: mov             x0, x3
    // 0xbee5b4: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee5b4: movz            x17, #0x64af
    //     0xbee5b8: add             lr, x0, x17
    //     0xbee5bc: ldr             lr, [x21, lr, lsl #3]
    //     0xbee5c0: blr             lr
    // 0xbee5c4: r1 = LoadInt32Instr(r0)
    //     0xbee5c4: sbfx            x1, x0, #1, #0x1f
    // 0xbee5c8: ldur            x0, [fp, #-0x18]
    // 0xbee5cc: eor             x2, x0, x1
    // 0xbee5d0: ldr             x1, [fp, #0x10]
    // 0xbee5d4: stur            x2, [fp, #-0x10]
    // 0xbee5d8: LoadField: r0 = r1->field_b
    //     0xbee5d8: ldur            w0, [x1, #0xb]
    // 0xbee5dc: DecompressPointer r0
    //     0xbee5dc: add             x0, x0, HEAP, lsl #32
    // 0xbee5e0: r3 = LoadClassIdInstr(r0)
    //     0xbee5e0: ldur            x3, [x0, #-1]
    //     0xbee5e4: ubfx            x3, x3, #0xc, #0x14
    // 0xbee5e8: str             x0, [SP]
    // 0xbee5ec: mov             x0, x3
    // 0xbee5f0: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee5f0: movz            x17, #0x64af
    //     0xbee5f4: add             lr, x0, x17
    //     0xbee5f8: ldr             lr, [x21, lr, lsl #3]
    //     0xbee5fc: blr             lr
    // 0xbee600: r1 = LoadInt32Instr(r0)
    //     0xbee600: sbfx            x1, x0, #1, #0x1f
    // 0xbee604: ldur            x0, [fp, #-0x10]
    // 0xbee608: eor             x2, x0, x1
    // 0xbee60c: ldr             x1, [fp, #0x10]
    // 0xbee610: stur            x2, [fp, #-0x18]
    // 0xbee614: LoadField: r0 = r1->field_1f
    //     0xbee614: ldur            w0, [x1, #0x1f]
    // 0xbee618: DecompressPointer r0
    //     0xbee618: add             x0, x0, HEAP, lsl #32
    // 0xbee61c: r3 = LoadClassIdInstr(r0)
    //     0xbee61c: ldur            x3, [x0, #-1]
    //     0xbee620: ubfx            x3, x3, #0xc, #0x14
    // 0xbee624: str             x0, [SP]
    // 0xbee628: mov             x0, x3
    // 0xbee62c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee62c: movz            x17, #0x64af
    //     0xbee630: add             lr, x0, x17
    //     0xbee634: ldr             lr, [x21, lr, lsl #3]
    //     0xbee638: blr             lr
    // 0xbee63c: r1 = LoadInt32Instr(r0)
    //     0xbee63c: sbfx            x1, x0, #1, #0x1f
    // 0xbee640: ldur            x0, [fp, #-0x18]
    // 0xbee644: eor             x2, x0, x1
    // 0xbee648: ldr             x1, [fp, #0x10]
    // 0xbee64c: stur            x2, [fp, #-0x10]
    // 0xbee650: LoadField: r0 = r1->field_27
    //     0xbee650: ldur            w0, [x1, #0x27]
    // 0xbee654: DecompressPointer r0
    //     0xbee654: add             x0, x0, HEAP, lsl #32
    // 0xbee658: r3 = LoadClassIdInstr(r0)
    //     0xbee658: ldur            x3, [x0, #-1]
    //     0xbee65c: ubfx            x3, x3, #0xc, #0x14
    // 0xbee660: str             x0, [SP]
    // 0xbee664: mov             x0, x3
    // 0xbee668: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee668: movz            x17, #0x64af
    //     0xbee66c: add             lr, x0, x17
    //     0xbee670: ldr             lr, [x21, lr, lsl #3]
    //     0xbee674: blr             lr
    // 0xbee678: r1 = LoadInt32Instr(r0)
    //     0xbee678: sbfx            x1, x0, #1, #0x1f
    // 0xbee67c: ldur            x0, [fp, #-0x10]
    // 0xbee680: eor             x2, x0, x1
    // 0xbee684: ldr             x1, [fp, #0x10]
    // 0xbee688: stur            x2, [fp, #-0x18]
    // 0xbee68c: LoadField: r0 = r1->field_2f
    //     0xbee68c: ldur            w0, [x1, #0x2f]
    // 0xbee690: DecompressPointer r0
    //     0xbee690: add             x0, x0, HEAP, lsl #32
    // 0xbee694: r3 = LoadClassIdInstr(r0)
    //     0xbee694: ldur            x3, [x0, #-1]
    //     0xbee698: ubfx            x3, x3, #0xc, #0x14
    // 0xbee69c: str             x0, [SP]
    // 0xbee6a0: mov             x0, x3
    // 0xbee6a4: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee6a4: movz            x17, #0x64af
    //     0xbee6a8: add             lr, x0, x17
    //     0xbee6ac: ldr             lr, [x21, lr, lsl #3]
    //     0xbee6b0: blr             lr
    // 0xbee6b4: r1 = LoadInt32Instr(r0)
    //     0xbee6b4: sbfx            x1, x0, #1, #0x1f
    // 0xbee6b8: ldur            x0, [fp, #-0x18]
    // 0xbee6bc: eor             x2, x0, x1
    // 0xbee6c0: ldr             x0, [fp, #0x10]
    // 0xbee6c4: stur            x2, [fp, #-0x10]
    // 0xbee6c8: LoadField: r1 = r0->field_2b
    //     0xbee6c8: ldur            w1, [x0, #0x2b]
    // 0xbee6cc: DecompressPointer r1
    //     0xbee6cc: add             x1, x1, HEAP, lsl #32
    // 0xbee6d0: r0 = LoadClassIdInstr(r1)
    //     0xbee6d0: ldur            x0, [x1, #-1]
    //     0xbee6d4: ubfx            x0, x0, #0xc, #0x14
    // 0xbee6d8: str             x1, [SP]
    // 0xbee6dc: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbee6dc: movz            x17, #0x64af
    //     0xbee6e0: add             lr, x0, x17
    //     0xbee6e4: ldr             lr, [x21, lr, lsl #3]
    //     0xbee6e8: blr             lr
    // 0xbee6ec: r1 = LoadInt32Instr(r0)
    //     0xbee6ec: sbfx            x1, x0, #1, #0x1f
    // 0xbee6f0: ldur            x2, [fp, #-0x10]
    // 0xbee6f4: eor             x3, x2, x1
    // 0xbee6f8: lsl             x0, x3, #1
    // 0xbee6fc: LeaveFrame
    //     0xbee6fc: mov             SP, fp
    //     0xbee700: ldp             fp, lr, [SP], #0x10
    // 0xbee704: ret
    //     0xbee704: ret             
    // 0xbee708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbee708: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbee70c: b               #0xbee46c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc294a8, size: 0x14c
    // 0xc294a8: EnterFrame
    //     0xc294a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc294ac: mov             fp, SP
    // 0xc294b0: AllocStack(0x8)
    //     0xc294b0: sub             SP, SP, #8
    // 0xc294b4: CheckStackOverflow
    //     0xc294b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc294b8: cmp             SP, x16
    //     0xc294bc: b.ls            #0xc295ec
    // 0xc294c0: r1 = Null
    //     0xc294c0: mov             x1, NULL
    // 0xc294c4: r2 = 44
    //     0xc294c4: movz            x2, #0x2c
    // 0xc294c8: r0 = AllocateArray()
    //     0xc294c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc294cc: r16 = "      Name: "
    //     0xc294cc: add             x16, PP, #0x39, lsl #12  ; [pp+0x394f0] "      Name: "
    //     0xc294d0: ldr             x16, [x16, #0x4f0]
    // 0xc294d4: StoreField: r0->field_f = r16
    //     0xc294d4: stur            w16, [x0, #0xf]
    // 0xc294d8: ldr             x1, [fp, #0x10]
    // 0xc294dc: LoadField: r2 = r1->field_7
    //     0xc294dc: ldur            w2, [x1, #7]
    // 0xc294e0: DecompressPointer r2
    //     0xc294e0: add             x2, x2, HEAP, lsl #32
    // 0xc294e4: StoreField: r0->field_13 = r2
    //     0xc294e4: stur            w2, [x0, #0x13]
    // 0xc294e8: r16 = ", \n      Street: "
    //     0xc294e8: add             x16, PP, #0x39, lsl #12  ; [pp+0x394f8] ", \n      Street: "
    //     0xc294ec: ldr             x16, [x16, #0x4f8]
    // 0xc294f0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc294f0: stur            w16, [x0, #0x17]
    // 0xc294f4: LoadField: r2 = r1->field_b
    //     0xc294f4: ldur            w2, [x1, #0xb]
    // 0xc294f8: DecompressPointer r2
    //     0xc294f8: add             x2, x2, HEAP, lsl #32
    // 0xc294fc: StoreField: r0->field_1b = r2
    //     0xc294fc: stur            w2, [x0, #0x1b]
    // 0xc29500: r16 = ", \n      ISO Country Code: "
    //     0xc29500: add             x16, PP, #0x39, lsl #12  ; [pp+0x39500] ", \n      ISO Country Code: "
    //     0xc29504: ldr             x16, [x16, #0x500]
    // 0xc29508: StoreField: r0->field_1f = r16
    //     0xc29508: stur            w16, [x0, #0x1f]
    // 0xc2950c: LoadField: r2 = r1->field_f
    //     0xc2950c: ldur            w2, [x1, #0xf]
    // 0xc29510: DecompressPointer r2
    //     0xc29510: add             x2, x2, HEAP, lsl #32
    // 0xc29514: StoreField: r0->field_23 = r2
    //     0xc29514: stur            w2, [x0, #0x23]
    // 0xc29518: r16 = ", \n      Country: "
    //     0xc29518: add             x16, PP, #0x39, lsl #12  ; [pp+0x39508] ", \n      Country: "
    //     0xc2951c: ldr             x16, [x16, #0x508]
    // 0xc29520: StoreField: r0->field_27 = r16
    //     0xc29520: stur            w16, [x0, #0x27]
    // 0xc29524: LoadField: r2 = r1->field_13
    //     0xc29524: ldur            w2, [x1, #0x13]
    // 0xc29528: DecompressPointer r2
    //     0xc29528: add             x2, x2, HEAP, lsl #32
    // 0xc2952c: StoreField: r0->field_2b = r2
    //     0xc2952c: stur            w2, [x0, #0x2b]
    // 0xc29530: r16 = ", \n      Postal code: "
    //     0xc29530: add             x16, PP, #0x39, lsl #12  ; [pp+0x39510] ", \n      Postal code: "
    //     0xc29534: ldr             x16, [x16, #0x510]
    // 0xc29538: StoreField: r0->field_2f = r16
    //     0xc29538: stur            w16, [x0, #0x2f]
    // 0xc2953c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc2953c: ldur            w2, [x1, #0x17]
    // 0xc29540: DecompressPointer r2
    //     0xc29540: add             x2, x2, HEAP, lsl #32
    // 0xc29544: StoreField: r0->field_33 = r2
    //     0xc29544: stur            w2, [x0, #0x33]
    // 0xc29548: r16 = ", \n      Administrative area: "
    //     0xc29548: add             x16, PP, #0x39, lsl #12  ; [pp+0x39518] ", \n      Administrative area: "
    //     0xc2954c: ldr             x16, [x16, #0x518]
    // 0xc29550: StoreField: r0->field_37 = r16
    //     0xc29550: stur            w16, [x0, #0x37]
    // 0xc29554: LoadField: r2 = r1->field_1b
    //     0xc29554: ldur            w2, [x1, #0x1b]
    // 0xc29558: DecompressPointer r2
    //     0xc29558: add             x2, x2, HEAP, lsl #32
    // 0xc2955c: StoreField: r0->field_3b = r2
    //     0xc2955c: stur            w2, [x0, #0x3b]
    // 0xc29560: r16 = ", \n      Subadministrative area: "
    //     0xc29560: add             x16, PP, #0x39, lsl #12  ; [pp+0x39520] ", \n      Subadministrative area: "
    //     0xc29564: ldr             x16, [x16, #0x520]
    // 0xc29568: StoreField: r0->field_3f = r16
    //     0xc29568: stur            w16, [x0, #0x3f]
    // 0xc2956c: LoadField: r2 = r1->field_1f
    //     0xc2956c: ldur            w2, [x1, #0x1f]
    // 0xc29570: DecompressPointer r2
    //     0xc29570: add             x2, x2, HEAP, lsl #32
    // 0xc29574: StoreField: r0->field_43 = r2
    //     0xc29574: stur            w2, [x0, #0x43]
    // 0xc29578: r16 = ",\n      Locality: "
    //     0xc29578: add             x16, PP, #0x39, lsl #12  ; [pp+0x39528] ",\n      Locality: "
    //     0xc2957c: ldr             x16, [x16, #0x528]
    // 0xc29580: StoreField: r0->field_47 = r16
    //     0xc29580: stur            w16, [x0, #0x47]
    // 0xc29584: LoadField: r2 = r1->field_23
    //     0xc29584: ldur            w2, [x1, #0x23]
    // 0xc29588: DecompressPointer r2
    //     0xc29588: add             x2, x2, HEAP, lsl #32
    // 0xc2958c: StoreField: r0->field_4b = r2
    //     0xc2958c: stur            w2, [x0, #0x4b]
    // 0xc29590: r16 = ",\n      Sublocality: "
    //     0xc29590: add             x16, PP, #0x39, lsl #12  ; [pp+0x39530] ",\n      Sublocality: "
    //     0xc29594: ldr             x16, [x16, #0x530]
    // 0xc29598: StoreField: r0->field_4f = r16
    //     0xc29598: stur            w16, [x0, #0x4f]
    // 0xc2959c: LoadField: r2 = r1->field_27
    //     0xc2959c: ldur            w2, [x1, #0x27]
    // 0xc295a0: DecompressPointer r2
    //     0xc295a0: add             x2, x2, HEAP, lsl #32
    // 0xc295a4: StoreField: r0->field_53 = r2
    //     0xc295a4: stur            w2, [x0, #0x53]
    // 0xc295a8: r16 = ",\n      Thoroughfare: "
    //     0xc295a8: add             x16, PP, #0x39, lsl #12  ; [pp+0x39538] ",\n      Thoroughfare: "
    //     0xc295ac: ldr             x16, [x16, #0x538]
    // 0xc295b0: StoreField: r0->field_57 = r16
    //     0xc295b0: stur            w16, [x0, #0x57]
    // 0xc295b4: LoadField: r2 = r1->field_2b
    //     0xc295b4: ldur            w2, [x1, #0x2b]
    // 0xc295b8: DecompressPointer r2
    //     0xc295b8: add             x2, x2, HEAP, lsl #32
    // 0xc295bc: StoreField: r0->field_5b = r2
    //     0xc295bc: stur            w2, [x0, #0x5b]
    // 0xc295c0: r16 = ",\n      Subthoroughfare: "
    //     0xc295c0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39540] ",\n      Subthoroughfare: "
    //     0xc295c4: ldr             x16, [x16, #0x540]
    // 0xc295c8: StoreField: r0->field_5f = r16
    //     0xc295c8: stur            w16, [x0, #0x5f]
    // 0xc295cc: LoadField: r2 = r1->field_2f
    //     0xc295cc: ldur            w2, [x1, #0x2f]
    // 0xc295d0: DecompressPointer r2
    //     0xc295d0: add             x2, x2, HEAP, lsl #32
    // 0xc295d4: StoreField: r0->field_63 = r2
    //     0xc295d4: stur            w2, [x0, #0x63]
    // 0xc295d8: str             x0, [SP]
    // 0xc295dc: r0 = _interpolate()
    //     0xc295dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc295e0: LeaveFrame
    //     0xc295e0: mov             SP, fp
    //     0xc295e4: ldp             fp, lr, [SP], #0x10
    // 0xc295e8: ret
    //     0xc295e8: ret             
    // 0xc295ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc295ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc295f0: b               #0xc294c0
  }
  _ ==(/* No info */) {
    // ** addr: 0xd6ffe0, size: 0x2c8
    // 0xd6ffe0: EnterFrame
    //     0xd6ffe0: stp             fp, lr, [SP, #-0x10]!
    //     0xd6ffe4: mov             fp, SP
    // 0xd6ffe8: AllocStack(0x10)
    //     0xd6ffe8: sub             SP, SP, #0x10
    // 0xd6ffec: CheckStackOverflow
    //     0xd6ffec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6fff0: cmp             SP, x16
    //     0xd6fff4: b.ls            #0xd702a0
    // 0xd6fff8: ldr             x1, [fp, #0x10]
    // 0xd6fffc: cmp             w1, NULL
    // 0xd70000: b.ne            #0xd70014
    // 0xd70004: r0 = false
    //     0xd70004: add             x0, NULL, #0x30  ; false
    // 0xd70008: LeaveFrame
    //     0xd70008: mov             SP, fp
    //     0xd7000c: ldp             fp, lr, [SP], #0x10
    // 0xd70010: ret
    //     0xd70010: ret             
    // 0xd70014: r0 = 60
    //     0xd70014: movz            x0, #0x3c
    // 0xd70018: branchIfSmi(r1, 0xd70024)
    //     0xd70018: tbz             w1, #0, #0xd70024
    // 0xd7001c: r0 = LoadClassIdInstr(r1)
    //     0xd7001c: ldur            x0, [x1, #-1]
    //     0xd70020: ubfx            x0, x0, #0xc, #0x14
    // 0xd70024: cmp             x0, #0x8a7
    // 0xd70028: b.ne            #0xd70290
    // 0xd7002c: ldr             x2, [fp, #0x18]
    // 0xd70030: LoadField: r0 = r1->field_1b
    //     0xd70030: ldur            w0, [x1, #0x1b]
    // 0xd70034: DecompressPointer r0
    //     0xd70034: add             x0, x0, HEAP, lsl #32
    // 0xd70038: LoadField: r3 = r2->field_1b
    //     0xd70038: ldur            w3, [x2, #0x1b]
    // 0xd7003c: DecompressPointer r3
    //     0xd7003c: add             x3, x3, HEAP, lsl #32
    // 0xd70040: r4 = LoadClassIdInstr(r0)
    //     0xd70040: ldur            x4, [x0, #-1]
    //     0xd70044: ubfx            x4, x4, #0xc, #0x14
    // 0xd70048: stp             x3, x0, [SP]
    // 0xd7004c: mov             x0, x4
    // 0xd70050: mov             lr, x0
    // 0xd70054: ldr             lr, [x21, lr, lsl #3]
    // 0xd70058: blr             lr
    // 0xd7005c: tbnz            w0, #4, #0xd70290
    // 0xd70060: ldr             x2, [fp, #0x18]
    // 0xd70064: ldr             x1, [fp, #0x10]
    // 0xd70068: LoadField: r0 = r1->field_13
    //     0xd70068: ldur            w0, [x1, #0x13]
    // 0xd7006c: DecompressPointer r0
    //     0xd7006c: add             x0, x0, HEAP, lsl #32
    // 0xd70070: LoadField: r3 = r2->field_13
    //     0xd70070: ldur            w3, [x2, #0x13]
    // 0xd70074: DecompressPointer r3
    //     0xd70074: add             x3, x3, HEAP, lsl #32
    // 0xd70078: r4 = LoadClassIdInstr(r0)
    //     0xd70078: ldur            x4, [x0, #-1]
    //     0xd7007c: ubfx            x4, x4, #0xc, #0x14
    // 0xd70080: stp             x3, x0, [SP]
    // 0xd70084: mov             x0, x4
    // 0xd70088: mov             lr, x0
    // 0xd7008c: ldr             lr, [x21, lr, lsl #3]
    // 0xd70090: blr             lr
    // 0xd70094: tbnz            w0, #4, #0xd70290
    // 0xd70098: ldr             x2, [fp, #0x18]
    // 0xd7009c: ldr             x1, [fp, #0x10]
    // 0xd700a0: LoadField: r0 = r1->field_f
    //     0xd700a0: ldur            w0, [x1, #0xf]
    // 0xd700a4: DecompressPointer r0
    //     0xd700a4: add             x0, x0, HEAP, lsl #32
    // 0xd700a8: LoadField: r3 = r2->field_f
    //     0xd700a8: ldur            w3, [x2, #0xf]
    // 0xd700ac: DecompressPointer r3
    //     0xd700ac: add             x3, x3, HEAP, lsl #32
    // 0xd700b0: r4 = LoadClassIdInstr(r0)
    //     0xd700b0: ldur            x4, [x0, #-1]
    //     0xd700b4: ubfx            x4, x4, #0xc, #0x14
    // 0xd700b8: stp             x3, x0, [SP]
    // 0xd700bc: mov             x0, x4
    // 0xd700c0: mov             lr, x0
    // 0xd700c4: ldr             lr, [x21, lr, lsl #3]
    // 0xd700c8: blr             lr
    // 0xd700cc: tbnz            w0, #4, #0xd70290
    // 0xd700d0: ldr             x2, [fp, #0x18]
    // 0xd700d4: ldr             x1, [fp, #0x10]
    // 0xd700d8: LoadField: r0 = r1->field_23
    //     0xd700d8: ldur            w0, [x1, #0x23]
    // 0xd700dc: DecompressPointer r0
    //     0xd700dc: add             x0, x0, HEAP, lsl #32
    // 0xd700e0: LoadField: r3 = r2->field_23
    //     0xd700e0: ldur            w3, [x2, #0x23]
    // 0xd700e4: DecompressPointer r3
    //     0xd700e4: add             x3, x3, HEAP, lsl #32
    // 0xd700e8: r4 = LoadClassIdInstr(r0)
    //     0xd700e8: ldur            x4, [x0, #-1]
    //     0xd700ec: ubfx            x4, x4, #0xc, #0x14
    // 0xd700f0: stp             x3, x0, [SP]
    // 0xd700f4: mov             x0, x4
    // 0xd700f8: mov             lr, x0
    // 0xd700fc: ldr             lr, [x21, lr, lsl #3]
    // 0xd70100: blr             lr
    // 0xd70104: tbnz            w0, #4, #0xd70290
    // 0xd70108: ldr             x2, [fp, #0x18]
    // 0xd7010c: ldr             x1, [fp, #0x10]
    // 0xd70110: LoadField: r0 = r1->field_7
    //     0xd70110: ldur            w0, [x1, #7]
    // 0xd70114: DecompressPointer r0
    //     0xd70114: add             x0, x0, HEAP, lsl #32
    // 0xd70118: LoadField: r3 = r2->field_7
    //     0xd70118: ldur            w3, [x2, #7]
    // 0xd7011c: DecompressPointer r3
    //     0xd7011c: add             x3, x3, HEAP, lsl #32
    // 0xd70120: r4 = LoadClassIdInstr(r0)
    //     0xd70120: ldur            x4, [x0, #-1]
    //     0xd70124: ubfx            x4, x4, #0xc, #0x14
    // 0xd70128: stp             x3, x0, [SP]
    // 0xd7012c: mov             x0, x4
    // 0xd70130: mov             lr, x0
    // 0xd70134: ldr             lr, [x21, lr, lsl #3]
    // 0xd70138: blr             lr
    // 0xd7013c: tbnz            w0, #4, #0xd70290
    // 0xd70140: ldr             x2, [fp, #0x18]
    // 0xd70144: ldr             x1, [fp, #0x10]
    // 0xd70148: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd70148: ldur            w0, [x1, #0x17]
    // 0xd7014c: DecompressPointer r0
    //     0xd7014c: add             x0, x0, HEAP, lsl #32
    // 0xd70150: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd70150: ldur            w3, [x2, #0x17]
    // 0xd70154: DecompressPointer r3
    //     0xd70154: add             x3, x3, HEAP, lsl #32
    // 0xd70158: r4 = LoadClassIdInstr(r0)
    //     0xd70158: ldur            x4, [x0, #-1]
    //     0xd7015c: ubfx            x4, x4, #0xc, #0x14
    // 0xd70160: stp             x3, x0, [SP]
    // 0xd70164: mov             x0, x4
    // 0xd70168: mov             lr, x0
    // 0xd7016c: ldr             lr, [x21, lr, lsl #3]
    // 0xd70170: blr             lr
    // 0xd70174: tbnz            w0, #4, #0xd70290
    // 0xd70178: ldr             x2, [fp, #0x18]
    // 0xd7017c: ldr             x1, [fp, #0x10]
    // 0xd70180: LoadField: r0 = r1->field_b
    //     0xd70180: ldur            w0, [x1, #0xb]
    // 0xd70184: DecompressPointer r0
    //     0xd70184: add             x0, x0, HEAP, lsl #32
    // 0xd70188: LoadField: r3 = r2->field_b
    //     0xd70188: ldur            w3, [x2, #0xb]
    // 0xd7018c: DecompressPointer r3
    //     0xd7018c: add             x3, x3, HEAP, lsl #32
    // 0xd70190: r4 = LoadClassIdInstr(r0)
    //     0xd70190: ldur            x4, [x0, #-1]
    //     0xd70194: ubfx            x4, x4, #0xc, #0x14
    // 0xd70198: stp             x3, x0, [SP]
    // 0xd7019c: mov             x0, x4
    // 0xd701a0: mov             lr, x0
    // 0xd701a4: ldr             lr, [x21, lr, lsl #3]
    // 0xd701a8: blr             lr
    // 0xd701ac: tbnz            w0, #4, #0xd70290
    // 0xd701b0: ldr             x2, [fp, #0x18]
    // 0xd701b4: ldr             x1, [fp, #0x10]
    // 0xd701b8: LoadField: r0 = r1->field_1f
    //     0xd701b8: ldur            w0, [x1, #0x1f]
    // 0xd701bc: DecompressPointer r0
    //     0xd701bc: add             x0, x0, HEAP, lsl #32
    // 0xd701c0: LoadField: r3 = r2->field_1f
    //     0xd701c0: ldur            w3, [x2, #0x1f]
    // 0xd701c4: DecompressPointer r3
    //     0xd701c4: add             x3, x3, HEAP, lsl #32
    // 0xd701c8: r4 = LoadClassIdInstr(r0)
    //     0xd701c8: ldur            x4, [x0, #-1]
    //     0xd701cc: ubfx            x4, x4, #0xc, #0x14
    // 0xd701d0: stp             x3, x0, [SP]
    // 0xd701d4: mov             x0, x4
    // 0xd701d8: mov             lr, x0
    // 0xd701dc: ldr             lr, [x21, lr, lsl #3]
    // 0xd701e0: blr             lr
    // 0xd701e4: tbnz            w0, #4, #0xd70290
    // 0xd701e8: ldr             x2, [fp, #0x18]
    // 0xd701ec: ldr             x1, [fp, #0x10]
    // 0xd701f0: LoadField: r0 = r1->field_27
    //     0xd701f0: ldur            w0, [x1, #0x27]
    // 0xd701f4: DecompressPointer r0
    //     0xd701f4: add             x0, x0, HEAP, lsl #32
    // 0xd701f8: LoadField: r3 = r2->field_27
    //     0xd701f8: ldur            w3, [x2, #0x27]
    // 0xd701fc: DecompressPointer r3
    //     0xd701fc: add             x3, x3, HEAP, lsl #32
    // 0xd70200: r4 = LoadClassIdInstr(r0)
    //     0xd70200: ldur            x4, [x0, #-1]
    //     0xd70204: ubfx            x4, x4, #0xc, #0x14
    // 0xd70208: stp             x3, x0, [SP]
    // 0xd7020c: mov             x0, x4
    // 0xd70210: mov             lr, x0
    // 0xd70214: ldr             lr, [x21, lr, lsl #3]
    // 0xd70218: blr             lr
    // 0xd7021c: tbnz            w0, #4, #0xd70290
    // 0xd70220: ldr             x2, [fp, #0x18]
    // 0xd70224: ldr             x1, [fp, #0x10]
    // 0xd70228: LoadField: r0 = r1->field_2f
    //     0xd70228: ldur            w0, [x1, #0x2f]
    // 0xd7022c: DecompressPointer r0
    //     0xd7022c: add             x0, x0, HEAP, lsl #32
    // 0xd70230: LoadField: r3 = r2->field_2f
    //     0xd70230: ldur            w3, [x2, #0x2f]
    // 0xd70234: DecompressPointer r3
    //     0xd70234: add             x3, x3, HEAP, lsl #32
    // 0xd70238: r4 = LoadClassIdInstr(r0)
    //     0xd70238: ldur            x4, [x0, #-1]
    //     0xd7023c: ubfx            x4, x4, #0xc, #0x14
    // 0xd70240: stp             x3, x0, [SP]
    // 0xd70244: mov             x0, x4
    // 0xd70248: mov             lr, x0
    // 0xd7024c: ldr             lr, [x21, lr, lsl #3]
    // 0xd70250: blr             lr
    // 0xd70254: tbnz            w0, #4, #0xd70290
    // 0xd70258: ldr             x1, [fp, #0x18]
    // 0xd7025c: ldr             x0, [fp, #0x10]
    // 0xd70260: LoadField: r2 = r0->field_2b
    //     0xd70260: ldur            w2, [x0, #0x2b]
    // 0xd70264: DecompressPointer r2
    //     0xd70264: add             x2, x2, HEAP, lsl #32
    // 0xd70268: LoadField: r0 = r1->field_2b
    //     0xd70268: ldur            w0, [x1, #0x2b]
    // 0xd7026c: DecompressPointer r0
    //     0xd7026c: add             x0, x0, HEAP, lsl #32
    // 0xd70270: r1 = LoadClassIdInstr(r2)
    //     0xd70270: ldur            x1, [x2, #-1]
    //     0xd70274: ubfx            x1, x1, #0xc, #0x14
    // 0xd70278: stp             x0, x2, [SP]
    // 0xd7027c: mov             x0, x1
    // 0xd70280: mov             lr, x0
    // 0xd70284: ldr             lr, [x21, lr, lsl #3]
    // 0xd70288: blr             lr
    // 0xd7028c: b               #0xd70294
    // 0xd70290: r0 = false
    //     0xd70290: add             x0, NULL, #0x30  ; false
    // 0xd70294: LeaveFrame
    //     0xd70294: mov             SP, fp
    //     0xd70298: ldp             fp, lr, [SP], #0x10
    // 0xd7029c: ret
    //     0xd7029c: ret             
    // 0xd702a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd702a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd702a4: b               #0xd6fff8
  }
}
