// lib: , url: package:get/get_core/src/get_main.dart

// class id: 1049533, size: 0x8
class :: {

  static late final _GetImpl Get; // offset: 0x1338

  static _GetImpl Get() {
    // ** addr: 0x615ba8, size: 0x38
    // 0x615ba8: EnterFrame
    //     0x615ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x615bac: mov             fp, SP
    // 0x615bb0: r0 = _GetImpl()
    //     0x615bb0: bl              #0x615be0  ; Allocate_GetImplStub -> _GetImpl (size=0x14)
    // 0x615bb4: r1 = Instance_SmartManagement
    //     0x615bb4: add             x1, PP, #0xb, lsl #12  ; [pp+0xbda0] Obj!SmartManagement@e32aa1
    //     0x615bb8: ldr             x1, [x1, #0xda0]
    // 0x615bbc: StoreField: r0->field_7 = r1
    //     0x615bbc: stur            w1, [x0, #7]
    // 0x615bc0: r1 = false
    //     0x615bc0: add             x1, NULL, #0x30  ; false
    // 0x615bc4: StoreField: r0->field_b = r1
    //     0x615bc4: stur            w1, [x0, #0xb]
    // 0x615bc8: r1 = Closure: (String, {bool isError}) => void from Function 'defaultLogWriterCallback': static.
    //     0x615bc8: add             x1, PP, #0xb, lsl #12  ; [pp+0xbda8] Closure: (String, {bool isError}) => void from Function 'defaultLogWriterCallback': static. (0x7e54fb015bec)
    //     0x615bcc: ldr             x1, [x1, #0xda8]
    // 0x615bd0: StoreField: r0->field_f = r1
    //     0x615bd0: stur            w1, [x0, #0xf]
    // 0x615bd4: LeaveFrame
    //     0x615bd4: mov             SP, fp
    //     0x615bd8: ldp             fp, lr, [SP], #0x10
    // 0x615bdc: ret
    //     0x615bdc: ret             
  }
}

// class id: 2201, size: 0x14, field offset: 0x14
class _GetImpl extends GetInterface {
}
