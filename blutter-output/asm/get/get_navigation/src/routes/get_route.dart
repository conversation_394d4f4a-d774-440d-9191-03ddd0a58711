// lib: , url: package:get/get_navigation/src/routes/get_route.dart

// class id: 1049552, size: 0x8
class :: {
}

// class id: 1737, size: 0x10, field offset: 0x8
//   const constructor, 
class PathDecoded extends Object {

  _ ==(/* No info */) {
    // ** addr: 0xd75388, size: 0x70
    // 0xd75388: ldr             x1, [SP]
    // 0xd7538c: cmp             w1, NULL
    // 0xd75390: b.ne            #0xd7539c
    // 0xd75394: r0 = false
    //     0xd75394: add             x0, NULL, #0x30  ; false
    // 0xd75398: ret
    //     0xd75398: ret             
    // 0xd7539c: ldr             x2, [SP, #8]
    // 0xd753a0: cmp             w2, w1
    // 0xd753a4: b.ne            #0xd753b0
    // 0xd753a8: r0 = true
    //     0xd753a8: add             x0, NULL, #0x20  ; true
    // 0xd753ac: ret
    //     0xd753ac: ret             
    // 0xd753b0: r3 = 60
    //     0xd753b0: movz            x3, #0x3c
    // 0xd753b4: branchIfSmi(r1, 0xd753c0)
    //     0xd753b4: tbz             w1, #0, #0xd753c0
    // 0xd753b8: r3 = LoadClassIdInstr(r1)
    //     0xd753b8: ldur            x3, [x1, #-1]
    //     0xd753bc: ubfx            x3, x3, #0xc, #0x14
    // 0xd753c0: cmp             x3, #0x6c9
    // 0xd753c4: b.ne            #0xd753f0
    // 0xd753c8: LoadField: r3 = r1->field_7
    //     0xd753c8: ldur            w3, [x1, #7]
    // 0xd753cc: DecompressPointer r3
    //     0xd753cc: add             x3, x3, HEAP, lsl #32
    // 0xd753d0: LoadField: r1 = r2->field_7
    //     0xd753d0: ldur            w1, [x2, #7]
    // 0xd753d4: DecompressPointer r1
    //     0xd753d4: add             x1, x1, HEAP, lsl #32
    // 0xd753d8: cmp             w3, w1
    // 0xd753dc: r16 = true
    //     0xd753dc: add             x16, NULL, #0x20  ; true
    // 0xd753e0: r17 = false
    //     0xd753e0: add             x17, NULL, #0x30  ; false
    // 0xd753e4: csel            x2, x16, x17, eq
    // 0xd753e8: mov             x0, x2
    // 0xd753ec: b               #0xd753f4
    // 0xd753f0: r0 = false
    //     0xd753f0: add             x0, NULL, #0x30  ; false
    // 0xd753f4: ret
    //     0xd753f4: ret             
  }
}

// class id: 2643, size: 0x84, field offset: 0x24
class GetPage<X0> extends Page<X0> {

  _ copy(/* No info */) {
    // ** addr: 0x65bdbc, size: 0x164
    // 0x65bdbc: EnterFrame
    //     0x65bdbc: stp             fp, lr, [SP, #-0x10]!
    //     0x65bdc0: mov             fp, SP
    // 0x65bdc4: AllocStack(0x58)
    //     0x65bdc4: sub             SP, SP, #0x58
    // 0x65bdc8: SetupParameters({dynamic middlewares = Null /* r5 */, dynamic parameters = Null /* r0 */})
    //     0x65bdc8: ldur            w0, [x4, #0x13]
    //     0x65bdcc: ldur            w3, [x4, #0x1f]
    //     0x65bdd0: add             x3, x3, HEAP, lsl #32
    //     0x65bdd4: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1aa88] "middlewares"
    //     0x65bdd8: ldr             x16, [x16, #0xa88]
    //     0x65bddc: cmp             w3, w16
    //     0x65bde0: b.ne            #0x65be04
    //     0x65bde4: ldur            w3, [x4, #0x23]
    //     0x65bde8: add             x3, x3, HEAP, lsl #32
    //     0x65bdec: sub             w5, w0, w3
    //     0x65bdf0: add             x3, fp, w5, sxtw #2
    //     0x65bdf4: ldr             x3, [x3, #8]
    //     0x65bdf8: mov             x5, x3
    //     0x65bdfc: movz            x3, #0x1
    //     0x65be00: b               #0x65be0c
    //     0x65be04: mov             x5, NULL
    //     0x65be08: movz            x3, #0
    //     0x65be0c: lsl             x6, x3, #1
    //     0x65be10: lsl             w3, w6, #1
    //     0x65be14: add             w6, w3, #8
    //     0x65be18: add             x16, x4, w6, sxtw #1
    //     0x65be1c: ldur            w7, [x16, #0xf]
    //     0x65be20: add             x7, x7, HEAP, lsl #32
    //     0x65be24: add             x16, PP, #0xf, lsl #12  ; [pp+0xf918] "parameters"
    //     0x65be28: ldr             x16, [x16, #0x918]
    //     0x65be2c: cmp             w7, w16
    //     0x65be30: b.ne            #0x65be54
    //     0x65be34: add             w6, w3, #0xa
    //     0x65be38: add             x16, x4, w6, sxtw #1
    //     0x65be3c: ldur            w3, [x16, #0xf]
    //     0x65be40: add             x3, x3, HEAP, lsl #32
    //     0x65be44: sub             w4, w0, w3
    //     0x65be48: add             x0, fp, w4, sxtw #2
    //     0x65be4c: ldr             x0, [x0, #8]
    //     0x65be50: b               #0x65be58
    //     0x65be54: mov             x0, NULL
    // 0x65be58: CheckStackOverflow
    //     0x65be58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65be5c: cmp             SP, x16
    //     0x65be60: b.ls            #0x65bf18
    // 0x65be64: cmp             w2, NULL
    // 0x65be68: b.ne            #0x65be74
    // 0x65be6c: LoadField: r2 = r1->field_6b
    //     0x65be6c: ldur            w2, [x1, #0x6b]
    // 0x65be70: DecompressPointer r2
    //     0x65be70: add             x2, x2, HEAP, lsl #32
    // 0x65be74: stur            x2, [fp, #-0x30]
    // 0x65be78: LoadField: r3 = r1->field_23
    //     0x65be78: ldur            w3, [x1, #0x23]
    // 0x65be7c: DecompressPointer r3
    //     0x65be7c: add             x3, x3, HEAP, lsl #32
    // 0x65be80: stur            x3, [fp, #-0x28]
    // 0x65be84: cmp             w0, NULL
    // 0x65be88: b.ne            #0x65be94
    // 0x65be8c: LoadField: r0 = r1->field_2b
    //     0x65be8c: ldur            w0, [x1, #0x2b]
    // 0x65be90: DecompressPointer r0
    //     0x65be90: add             x0, x0, HEAP, lsl #32
    // 0x65be94: stur            x0, [fp, #-0x20]
    // 0x65be98: LoadField: r4 = r1->field_4f
    //     0x65be98: ldur            w4, [x1, #0x4f]
    // 0x65be9c: DecompressPointer r4
    //     0x65be9c: add             x4, x4, HEAP, lsl #32
    // 0x65bea0: stur            x4, [fp, #-0x18]
    // 0x65bea4: LoadField: r6 = r1->field_6f
    //     0x65bea4: ldur            w6, [x1, #0x6f]
    // 0x65bea8: DecompressPointer r6
    //     0x65bea8: add             x6, x6, HEAP, lsl #32
    // 0x65beac: stur            x6, [fp, #-0x10]
    // 0x65beb0: cmp             w5, NULL
    // 0x65beb4: b.ne            #0x65bec0
    // 0x65beb8: LoadField: r5 = r1->field_73
    //     0x65beb8: ldur            w5, [x1, #0x73]
    // 0x65bebc: DecompressPointer r5
    //     0x65bebc: add             x5, x5, HEAP, lsl #32
    // 0x65bec0: stur            x5, [fp, #-8]
    // 0x65bec4: LoadField: r7 = r1->field_f
    //     0x65bec4: ldur            w7, [x1, #0xf]
    // 0x65bec8: DecompressPointer r7
    //     0x65bec8: add             x7, x7, HEAP, lsl #32
    // 0x65becc: mov             x1, x7
    // 0x65bed0: r0 = GetPage()
    //     0x65bed0: bl              #0x65cdb0  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x65bed4: stur            x0, [fp, #-0x38]
    // 0x65bed8: ldur            x16, [fp, #-0x20]
    // 0x65bedc: ldur            lr, [fp, #-0x18]
    // 0x65bee0: stp             lr, x16, [SP, #0x10]
    // 0x65bee4: ldur            x16, [fp, #-0x10]
    // 0x65bee8: ldur            lr, [fp, #-8]
    // 0x65beec: stp             lr, x16, [SP]
    // 0x65bef0: mov             x1, x0
    // 0x65bef4: ldur            x2, [fp, #-0x30]
    // 0x65bef8: ldur            x3, [fp, #-0x28]
    // 0x65befc: r4 = const [0, 0x7, 0x4, 0x3, binding, 0x4, children, 0x5, middlewares, 0x6, parameters, 0x3, null]
    //     0x65befc: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1ab48] List(13) [0, 0x7, 0x4, 0x3, "binding", 0x4, "children", 0x5, "middlewares", 0x6, "parameters", 0x3, Null]
    //     0x65bf00: ldr             x4, [x4, #0xb48]
    // 0x65bf04: r0 = GetPage()
    //     0x65bf04: bl              #0x65bf20  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x65bf08: ldur            x0, [fp, #-0x38]
    // 0x65bf0c: LeaveFrame
    //     0x65bf0c: mov             SP, fp
    //     0x65bf10: ldp             fp, lr, [SP], #0x10
    // 0x65bf14: ret
    //     0x65bf14: ret             
    // 0x65bf18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65bf18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65bf1c: b               #0x65be64
  }
  _ GetPage(/* No info */) {
    // ** addr: 0x65bf20, size: 0x364
    // 0x65bf20: EnterFrame
    //     0x65bf20: stp             fp, lr, [SP, #-0x10]!
    //     0x65bf24: mov             fp, SP
    // 0x65bf28: AllocStack(0x18)
    //     0x65bf28: sub             SP, SP, #0x18
    // 0x65bf2c: SetupParameters(GetPage<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1 */, {dynamic binding = Null /* r6 */, dynamic children = const [] /* r7 */, dynamic middlewares = Null /* r8 */, dynamic parameters = Null /* r11 */})
    //     0x65bf2c: stur            x1, [fp, #-8]
    //     0x65bf30: mov             x16, x3
    //     0x65bf34: mov             x3, x1
    //     0x65bf38: mov             x1, x16
    //     0x65bf3c: stur            x2, [fp, #-0x10]
    //     0x65bf40: ldur            w0, [x4, #0x13]
    //     0x65bf44: ldur            w5, [x4, #0x1f]
    //     0x65bf48: add             x5, x5, HEAP, lsl #32
    //     0x65bf4c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ab50] "binding"
    //     0x65bf50: ldr             x16, [x16, #0xb50]
    //     0x65bf54: cmp             w5, w16
    //     0x65bf58: b.ne            #0x65bf7c
    //     0x65bf5c: ldur            w5, [x4, #0x23]
    //     0x65bf60: add             x5, x5, HEAP, lsl #32
    //     0x65bf64: sub             w6, w0, w5
    //     0x65bf68: add             x5, fp, w6, sxtw #2
    //     0x65bf6c: ldr             x5, [x5, #8]
    //     0x65bf70: mov             x6, x5
    //     0x65bf74: movz            x5, #0x1
    //     0x65bf78: b               #0x65bf84
    //     0x65bf7c: mov             x6, NULL
    //     0x65bf80: movz            x5, #0
    //     0x65bf84: lsl             x7, x5, #1
    //     0x65bf88: lsl             w8, w7, #1
    //     0x65bf8c: add             w9, w8, #8
    //     0x65bf90: add             x16, x4, w9, sxtw #1
    //     0x65bf94: ldur            w10, [x16, #0xf]
    //     0x65bf98: add             x10, x10, HEAP, lsl #32
    //     0x65bf9c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ab58] "children"
    //     0x65bfa0: ldr             x16, [x16, #0xb58]
    //     0x65bfa4: cmp             w10, w16
    //     0x65bfa8: b.ne            #0x65bfdc
    //     0x65bfac: add             w5, w8, #0xa
    //     0x65bfb0: add             x16, x4, w5, sxtw #1
    //     0x65bfb4: ldur            w8, [x16, #0xf]
    //     0x65bfb8: add             x8, x8, HEAP, lsl #32
    //     0x65bfbc: sub             w5, w0, w8
    //     0x65bfc0: add             x8, fp, w5, sxtw #2
    //     0x65bfc4: ldr             x8, [x8, #8]
    //     0x65bfc8: add             w5, w7, #2
    //     0x65bfcc: sbfx            x7, x5, #1, #0x1f
    //     0x65bfd0: mov             x5, x7
    //     0x65bfd4: mov             x7, x8
    //     0x65bfd8: b               #0x65bfe4
    //     0x65bfdc: add             x7, PP, #0x1a, lsl #12  ; [pp+0x1ab60] List<GetPage>(0)
    //     0x65bfe0: ldr             x7, [x7, #0xb60]
    //     0x65bfe4: lsl             x8, x5, #1
    //     0x65bfe8: lsl             w9, w8, #1
    //     0x65bfec: add             w10, w9, #8
    //     0x65bff0: add             x16, x4, w10, sxtw #1
    //     0x65bff4: ldur            w11, [x16, #0xf]
    //     0x65bff8: add             x11, x11, HEAP, lsl #32
    //     0x65bffc: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1aa88] "middlewares"
    //     0x65c000: ldr             x16, [x16, #0xa88]
    //     0x65c004: cmp             w11, w16
    //     0x65c008: b.ne            #0x65c03c
    //     0x65c00c: add             w5, w9, #0xa
    //     0x65c010: add             x16, x4, w5, sxtw #1
    //     0x65c014: ldur            w9, [x16, #0xf]
    //     0x65c018: add             x9, x9, HEAP, lsl #32
    //     0x65c01c: sub             w5, w0, w9
    //     0x65c020: add             x9, fp, w5, sxtw #2
    //     0x65c024: ldr             x9, [x9, #8]
    //     0x65c028: add             w5, w8, #2
    //     0x65c02c: sbfx            x8, x5, #1, #0x1f
    //     0x65c030: mov             x5, x8
    //     0x65c034: mov             x8, x9
    //     0x65c038: b               #0x65c040
    //     0x65c03c: mov             x8, NULL
    //     0x65c040: lsl             x9, x5, #1
    //     0x65c044: lsl             w5, w9, #1
    //     0x65c048: add             w9, w5, #8
    //     0x65c04c: add             x16, x4, w9, sxtw #1
    //     0x65c050: ldur            w10, [x16, #0xf]
    //     0x65c054: add             x10, x10, HEAP, lsl #32
    //     0x65c058: add             x16, PP, #0xf, lsl #12  ; [pp+0xf918] "parameters"
    //     0x65c05c: ldr             x16, [x16, #0x918]
    //     0x65c060: cmp             w10, w16
    //     0x65c064: b.ne            #0x65c08c
    //     0x65c068: add             w9, w5, #0xa
    //     0x65c06c: add             x16, x4, w9, sxtw #1
    //     0x65c070: ldur            w5, [x16, #0xf]
    //     0x65c074: add             x5, x5, HEAP, lsl #32
    //     0x65c078: sub             w4, w0, w5
    //     0x65c07c: add             x0, fp, w4, sxtw #2
    //     0x65c080: ldr             x0, [x0, #8]
    //     0x65c084: mov             x11, x0
    //     0x65c088: b               #0x65c090
    //     0x65c08c: mov             x11, NULL
    //     0x65c090: add             x10, NULL, #0x20  ; true
    //     0x65c094: ldr             x9, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    //     0x65c098: add             x5, PP, #0x1a, lsl #12  ; [pp+0x1aa70] List<Bindings>(0)
    //     0x65c09c: ldr             x5, [x5, #0xa70]
    //     0x65c0a0: add             x4, NULL, #0x30  ; false
    // 0x65c090: r10 = true
    // 0x65c094: r9 = Instance__Linear
    // 0x65c098: r5 = const []
    // 0x65c0a0: r4 = false
    // 0x65c0a4: CheckStackOverflow
    //     0x65c0a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65c0a8: cmp             SP, x16
    //     0x65c0ac: b.ls            #0x65c27c
    // 0x65c0b0: mov             x0, x2
    // 0x65c0b4: StoreField: r3->field_6b = r0
    //     0x65c0b4: stur            w0, [x3, #0x6b]
    //     0x65c0b8: ldurb           w16, [x3, #-1]
    //     0x65c0bc: ldurb           w17, [x0, #-1]
    //     0x65c0c0: and             x16, x17, x16, lsr #2
    //     0x65c0c4: tst             x16, HEAP, lsr #32
    //     0x65c0c8: b.eq            #0x65c0d0
    //     0x65c0cc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c0d0: mov             x0, x1
    // 0x65c0d4: StoreField: r3->field_23 = r0
    //     0x65c0d4: stur            w0, [x3, #0x23]
    //     0x65c0d8: ldurb           w16, [x3, #-1]
    //     0x65c0dc: ldurb           w17, [x0, #-1]
    //     0x65c0e0: and             x16, x17, x16, lsr #2
    //     0x65c0e4: tst             x16, HEAP, lsr #32
    //     0x65c0e8: b.eq            #0x65c0f0
    //     0x65c0ec: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c0f0: StoreField: r3->field_43 = r10
    //     0x65c0f0: stur            w10, [x3, #0x43]
    // 0x65c0f4: StoreField: r3->field_37 = r9
    //     0x65c0f4: stur            w9, [x3, #0x37]
    // 0x65c0f8: mov             x0, x11
    // 0x65c0fc: StoreField: r3->field_2b = r0
    //     0x65c0fc: stur            w0, [x3, #0x2b]
    //     0x65c100: ldurb           w16, [x3, #-1]
    //     0x65c104: ldurb           w17, [x0, #-1]
    //     0x65c108: and             x16, x17, x16, lsr #2
    //     0x65c10c: tst             x16, HEAP, lsr #32
    //     0x65c110: b.eq            #0x65c118
    //     0x65c114: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c118: StoreField: r3->field_47 = r10
    //     0x65c118: stur            w10, [x3, #0x47]
    // 0x65c11c: mov             x0, x6
    // 0x65c120: StoreField: r3->field_4f = r0
    //     0x65c120: stur            w0, [x3, #0x4f]
    //     0x65c124: ldurb           w16, [x3, #-1]
    //     0x65c128: ldurb           w17, [x0, #-1]
    //     0x65c12c: and             x16, x17, x16, lsr #2
    //     0x65c130: tst             x16, HEAP, lsr #32
    //     0x65c134: b.eq            #0x65c13c
    //     0x65c138: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c13c: StoreField: r3->field_53 = r5
    //     0x65c13c: stur            w5, [x3, #0x53]
    // 0x65c140: StoreField: r3->field_5f = r4
    //     0x65c140: stur            w4, [x3, #0x5f]
    // 0x65c144: mov             x0, x7
    // 0x65c148: StoreField: r3->field_6f = r0
    //     0x65c148: stur            w0, [x3, #0x6f]
    //     0x65c14c: ldurb           w16, [x3, #-1]
    //     0x65c150: ldurb           w17, [x0, #-1]
    //     0x65c154: and             x16, x17, x16, lsr #2
    //     0x65c158: tst             x16, HEAP, lsr #32
    //     0x65c15c: b.eq            #0x65c164
    //     0x65c160: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c164: mov             x0, x8
    // 0x65c168: StoreField: r3->field_73 = r0
    //     0x65c168: stur            w0, [x3, #0x73]
    //     0x65c16c: ldurb           w16, [x3, #-1]
    //     0x65c170: ldurb           w17, [x0, #-1]
    //     0x65c174: and             x16, x17, x16, lsr #2
    //     0x65c178: tst             x16, HEAP, lsr #32
    //     0x65c17c: b.eq            #0x65c184
    //     0x65c180: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x65c184: StoreField: r3->field_7f = r10
    //     0x65c184: stur            w10, [x3, #0x7f]
    // 0x65c188: StoreField: r3->field_63 = r10
    //     0x65c188: stur            w10, [x3, #0x63]
    // 0x65c18c: mov             x1, x2
    // 0x65c190: r0 = _nameToRegex()
    //     0x65c190: bl              #0x65c2ec  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::_nameToRegex
    // 0x65c194: ldur            x2, [fp, #-8]
    // 0x65c198: StoreField: r2->field_77 = r0
    //     0x65c198: stur            w0, [x2, #0x77]
    //     0x65c19c: ldurb           w16, [x2, #-1]
    //     0x65c1a0: ldurb           w17, [x0, #-1]
    //     0x65c1a4: and             x16, x17, x16, lsr #2
    //     0x65c1a8: tst             x16, HEAP, lsr #32
    //     0x65c1ac: b.eq            #0x65c1b4
    //     0x65c1b0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x65c1b4: r1 = <String>
    //     0x65c1b4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x65c1b8: r0 = ValueKey()
    //     0x65c1b8: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0x65c1bc: mov             x1, x0
    // 0x65c1c0: ldur            x0, [fp, #-0x10]
    // 0x65c1c4: stur            x1, [fp, #-0x18]
    // 0x65c1c8: StoreField: r1->field_b = r0
    //     0x65c1c8: stur            w0, [x1, #0xb]
    // 0x65c1cc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x65c1cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x65c1d0: ldr             x0, [x0, #0x2670]
    //     0x65c1d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x65c1d8: cmp             w0, w16
    //     0x65c1dc: b.ne            #0x65c1e8
    //     0x65c1e0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x65c1e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x65c1e8: r0 = GetNavigation.arguments()
    //     0x65c1e8: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x65c1ec: mov             x2, x0
    // 0x65c1f0: ldur            x0, [fp, #-0x18]
    // 0x65c1f4: ldur            x1, [fp, #-8]
    // 0x65c1f8: StoreField: r1->field_13 = r0
    //     0x65c1f8: stur            w0, [x1, #0x13]
    //     0x65c1fc: ldurb           w16, [x1, #-1]
    //     0x65c200: ldurb           w17, [x0, #-1]
    //     0x65c204: and             x16, x17, x16, lsr #2
    //     0x65c208: tst             x16, HEAP, lsr #32
    //     0x65c20c: b.eq            #0x65c214
    //     0x65c210: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65c214: r3 = true
    //     0x65c214: add             x3, NULL, #0x20  ; true
    // 0x65c218: StoreField: r1->field_1f = r3
    //     0x65c218: stur            w3, [x1, #0x1f]
    // 0x65c21c: r3 = Closure: (bool, Object?) => void from Function '_defaultPopInvokedHandler@302124995': static.
    //     0x65c21c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a918] Closure: (bool, Object?) => void from Function '_defaultPopInvokedHandler@302124995': static. (0x7e54fb8b8ce0)
    //     0x65c220: ldr             x3, [x3, #0x918]
    // 0x65c224: StoreField: r1->field_1b = r3
    //     0x65c224: stur            w3, [x1, #0x1b]
    // 0x65c228: ldur            x0, [fp, #-0x10]
    // 0x65c22c: StoreField: r1->field_7 = r0
    //     0x65c22c: stur            w0, [x1, #7]
    //     0x65c230: ldurb           w16, [x1, #-1]
    //     0x65c234: ldurb           w17, [x0, #-1]
    //     0x65c238: and             x16, x17, x16, lsr #2
    //     0x65c23c: tst             x16, HEAP, lsr #32
    //     0x65c240: b.eq            #0x65c248
    //     0x65c244: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65c248: mov             x0, x2
    // 0x65c24c: StoreField: r1->field_b = r0
    //     0x65c24c: stur            w0, [x1, #0xb]
    //     0x65c250: tbz             w0, #0, #0x65c26c
    //     0x65c254: ldurb           w16, [x1, #-1]
    //     0x65c258: ldurb           w17, [x0, #-1]
    //     0x65c25c: and             x16, x17, x16, lsr #2
    //     0x65c260: tst             x16, HEAP, lsr #32
    //     0x65c264: b.eq            #0x65c26c
    //     0x65c268: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x65c26c: r0 = Null
    //     0x65c26c: mov             x0, NULL
    // 0x65c270: LeaveFrame
    //     0x65c270: mov             SP, fp
    //     0x65c274: ldp             fp, lr, [SP], #0x10
    // 0x65c278: ret
    //     0x65c278: ret             
    // 0x65c27c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65c27c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65c280: b               #0x65c0b0
  }
  static _ _nameToRegex(/* No info */) {
    // ** addr: 0x65c2ec, size: 0x174
    // 0x65c2ec: EnterFrame
    //     0x65c2ec: stp             fp, lr, [SP, #-0x10]!
    //     0x65c2f0: mov             fp, SP
    // 0x65c2f4: AllocStack(0x48)
    //     0x65c2f4: sub             SP, SP, #0x48
    // 0x65c2f8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x65c2f8: mov             x0, x1
    //     0x65c2fc: stur            x1, [fp, #-8]
    // 0x65c300: CheckStackOverflow
    //     0x65c300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65c304: cmp             SP, x16
    //     0x65c308: b.ls            #0x65c458
    // 0x65c30c: r1 = <String?>
    //     0x65c30c: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x65c310: r2 = 0
    //     0x65c310: movz            x2, #0
    // 0x65c314: r0 = _GrowableList()
    //     0x65c314: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x65c318: stur            x0, [fp, #-0x10]
    // 0x65c31c: r1 = 1
    //     0x65c31c: movz            x1, #0x1
    // 0x65c320: r0 = AllocateContext()
    //     0x65c320: bl              #0xec126c  ; AllocateContextStub
    // 0x65c324: mov             x3, x0
    // 0x65c328: ldur            x0, [fp, #-0x10]
    // 0x65c32c: stur            x3, [fp, #-0x18]
    // 0x65c330: StoreField: r3->field_f = r0
    //     0x65c330: stur            w0, [x3, #0xf]
    // 0x65c334: r1 = Null
    //     0x65c334: mov             x1, NULL
    // 0x65c338: r2 = 4
    //     0x65c338: movz            x2, #0x4
    // 0x65c33c: r0 = AllocateArray()
    //     0x65c33c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x65c340: mov             x1, x0
    // 0x65c344: ldur            x0, [fp, #-8]
    // 0x65c348: StoreField: r1->field_f = r0
    //     0x65c348: stur            w0, [x1, #0xf]
    // 0x65c34c: r16 = "/\?"
    //     0x65c34c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ab68] "/\?"
    //     0x65c350: ldr             x16, [x16, #0xb68]
    // 0x65c354: StoreField: r1->field_13 = r16
    //     0x65c354: stur            w16, [x1, #0x13]
    // 0x65c358: str             x1, [SP]
    // 0x65c35c: r0 = _interpolate()
    //     0x65c35c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x65c360: stur            x0, [fp, #-8]
    // 0x65c364: r16 = "(\\.)\?:(\\w+)(\\\?)\?"
    //     0x65c364: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ab70] "(\\.)\?:(\\w+)(\\\?)\?"
    //     0x65c368: ldr             x16, [x16, #0xb70]
    // 0x65c36c: stp             x16, NULL, [SP, #0x20]
    // 0x65c370: r16 = false
    //     0x65c370: add             x16, NULL, #0x30  ; false
    // 0x65c374: r30 = true
    //     0x65c374: add             lr, NULL, #0x20  ; true
    // 0x65c378: stp             lr, x16, [SP, #0x10]
    // 0x65c37c: r16 = false
    //     0x65c37c: add             x16, NULL, #0x30  ; false
    // 0x65c380: r30 = false
    //     0x65c380: add             lr, NULL, #0x30  ; false
    // 0x65c384: stp             lr, x16, [SP]
    // 0x65c388: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x65c388: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x65c38c: r0 = _RegExp()
    //     0x65c38c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x65c390: ldur            x2, [fp, #-0x18]
    // 0x65c394: r1 = Function 'replace': static.
    //     0x65c394: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1ab78] AnonymousClosure: static (0x65cc20), in [package:get/get_navigation/src/routes/get_route.dart] GetPage::_nameToRegex (0x65c2ec)
    //     0x65c398: ldr             x1, [x1, #0xb78]
    // 0x65c39c: stur            x0, [fp, #-0x10]
    // 0x65c3a0: r0 = AllocateClosure()
    //     0x65c3a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x65c3a4: ldur            x1, [fp, #-8]
    // 0x65c3a8: ldur            x2, [fp, #-0x10]
    // 0x65c3ac: mov             x3, x0
    // 0x65c3b0: r0 = replaceAllMapped()
    //     0x65c3b0: bl              #0x65c46c  ; [dart:core] _StringBase::replaceAllMapped
    // 0x65c3b4: mov             x1, x0
    // 0x65c3b8: r2 = "//"
    //     0x65c3b8: ldr             x2, [PP, #0x3670]  ; [pp+0x3670] "//"
    // 0x65c3bc: r3 = "/"
    //     0x65c3bc: ldr             x3, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x65c3c0: r0 = replaceAll()
    //     0x65c3c0: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x65c3c4: r1 = Null
    //     0x65c3c4: mov             x1, NULL
    // 0x65c3c8: r2 = 6
    //     0x65c3c8: movz            x2, #0x6
    // 0x65c3cc: stur            x0, [fp, #-8]
    // 0x65c3d0: r0 = AllocateArray()
    //     0x65c3d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x65c3d4: r16 = "^"
    //     0x65c3d4: add             x16, PP, #0x18, lsl #12  ; [pp+0x18118] "^"
    //     0x65c3d8: ldr             x16, [x16, #0x118]
    // 0x65c3dc: StoreField: r0->field_f = r16
    //     0x65c3dc: stur            w16, [x0, #0xf]
    // 0x65c3e0: ldur            x1, [fp, #-8]
    // 0x65c3e4: StoreField: r0->field_13 = r1
    //     0x65c3e4: stur            w1, [x0, #0x13]
    // 0x65c3e8: r16 = "$"
    //     0x65c3e8: add             x16, PP, #0x18, lsl #12  ; [pp+0x18098] "$"
    //     0x65c3ec: ldr             x16, [x16, #0x98]
    // 0x65c3f0: ArrayStore: r0[0] = r16  ; List_4
    //     0x65c3f0: stur            w16, [x0, #0x17]
    // 0x65c3f4: str             x0, [SP]
    // 0x65c3f8: r0 = _interpolate()
    //     0x65c3f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x65c3fc: stp             x0, NULL, [SP, #0x20]
    // 0x65c400: r16 = false
    //     0x65c400: add             x16, NULL, #0x30  ; false
    // 0x65c404: r30 = true
    //     0x65c404: add             lr, NULL, #0x20  ; true
    // 0x65c408: stp             lr, x16, [SP, #0x10]
    // 0x65c40c: r16 = false
    //     0x65c40c: add             x16, NULL, #0x30  ; false
    // 0x65c410: r30 = false
    //     0x65c410: add             lr, NULL, #0x30  ; false
    // 0x65c414: stp             lr, x16, [SP]
    // 0x65c418: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x65c418: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x65c41c: r0 = _RegExp()
    //     0x65c41c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x65c420: mov             x1, x0
    // 0x65c424: ldur            x0, [fp, #-0x18]
    // 0x65c428: stur            x1, [fp, #-0x10]
    // 0x65c42c: LoadField: r2 = r0->field_f
    //     0x65c42c: ldur            w2, [x0, #0xf]
    // 0x65c430: DecompressPointer r2
    //     0x65c430: add             x2, x2, HEAP, lsl #32
    // 0x65c434: stur            x2, [fp, #-8]
    // 0x65c438: r0 = PathDecoded()
    //     0x65c438: bl              #0x65c460  ; AllocatePathDecodedStub -> PathDecoded (size=0x10)
    // 0x65c43c: ldur            x1, [fp, #-0x10]
    // 0x65c440: StoreField: r0->field_7 = r1
    //     0x65c440: stur            w1, [x0, #7]
    // 0x65c444: ldur            x1, [fp, #-8]
    // 0x65c448: StoreField: r0->field_b = r1
    //     0x65c448: stur            w1, [x0, #0xb]
    // 0x65c44c: LeaveFrame
    //     0x65c44c: mov             SP, fp
    //     0x65c450: ldp             fp, lr, [SP], #0x10
    // 0x65c454: ret
    //     0x65c454: ret             
    // 0x65c458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65c458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65c45c: b               #0x65c30c
  }
  [closure] static String replace(dynamic, Match) {
    // ** addr: 0x65cc20, size: 0x190
    // 0x65cc20: EnterFrame
    //     0x65cc20: stp             fp, lr, [SP, #-0x10]!
    //     0x65cc24: mov             fp, SP
    // 0x65cc28: AllocStack(0x28)
    //     0x65cc28: sub             SP, SP, #0x28
    // 0x65cc2c: SetupParameters()
    //     0x65cc2c: ldr             x0, [fp, #0x18]
    //     0x65cc30: ldur            w1, [x0, #0x17]
    //     0x65cc34: add             x1, x1, HEAP, lsl #32
    //     0x65cc38: stur            x1, [fp, #-8]
    // 0x65cc3c: CheckStackOverflow
    //     0x65cc3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x65cc40: cmp             SP, x16
    //     0x65cc44: b.ls            #0x65cda8
    // 0x65cc48: r0 = StringBuffer()
    //     0x65cc48: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x65cc4c: stur            x0, [fp, #-0x10]
    // 0x65cc50: r16 = "(\?:"
    //     0x65cc50: add             x16, PP, #0x10, lsl #12  ; [pp+0x109c0] "(\?:"
    //     0x65cc54: ldr             x16, [x16, #0x9c0]
    // 0x65cc58: str             x16, [SP]
    // 0x65cc5c: mov             x1, x0
    // 0x65cc60: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x65cc60: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x65cc64: r0 = StringBuffer()
    //     0x65cc64: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x65cc68: ldr             x3, [fp, #0x10]
    // 0x65cc6c: r0 = LoadClassIdInstr(r3)
    //     0x65cc6c: ldur            x0, [x3, #-1]
    //     0x65cc70: ubfx            x0, x0, #0xc, #0x14
    // 0x65cc74: mov             x1, x3
    // 0x65cc78: r2 = 2
    //     0x65cc78: movz            x2, #0x2
    // 0x65cc7c: r0 = GDT[cid_x0 + -0xfc6]()
    //     0x65cc7c: sub             lr, x0, #0xfc6
    //     0x65cc80: ldr             lr, [x21, lr, lsl #3]
    //     0x65cc84: blr             lr
    // 0x65cc88: cmp             w0, NULL
    // 0x65cc8c: b.eq            #0x65cc9c
    // 0x65cc90: ldur            x1, [fp, #-0x10]
    // 0x65cc94: r2 = "."
    //     0x65cc94: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x65cc98: r0 = write()
    //     0x65cc98: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x65cc9c: ldr             x0, [fp, #0x10]
    // 0x65cca0: ldur            x1, [fp, #-0x10]
    // 0x65cca4: r2 = "([\\w%+-._~!$&\'()*,;=:@]+))"
    //     0x65cca4: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ab80] "([\\w%+-._~!$&\'()*,;=:@]+))"
    //     0x65cca8: ldr             x2, [x2, #0xb80]
    // 0x65ccac: r0 = write()
    //     0x65ccac: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x65ccb0: ldr             x3, [fp, #0x10]
    // 0x65ccb4: r0 = LoadClassIdInstr(r3)
    //     0x65ccb4: ldur            x0, [x3, #-1]
    //     0x65ccb8: ubfx            x0, x0, #0xc, #0x14
    // 0x65ccbc: mov             x1, x3
    // 0x65ccc0: r2 = 6
    //     0x65ccc0: movz            x2, #0x6
    // 0x65ccc4: r0 = GDT[cid_x0 + -0xfc6]()
    //     0x65ccc4: sub             lr, x0, #0xfc6
    //     0x65ccc8: ldr             lr, [x21, lr, lsl #3]
    //     0x65cccc: blr             lr
    // 0x65ccd0: cmp             w0, NULL
    // 0x65ccd4: b.eq            #0x65cce4
    // 0x65ccd8: ldur            x1, [fp, #-0x10]
    // 0x65ccdc: r2 = "\?"
    //     0x65ccdc: ldr             x2, [PP, #0xf90]  ; [pp+0xf90] "\?"
    // 0x65cce0: r0 = write()
    //     0x65cce0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x65cce4: ldr             x1, [fp, #0x10]
    // 0x65cce8: ldur            x0, [fp, #-8]
    // 0x65ccec: LoadField: r3 = r0->field_f
    //     0x65ccec: ldur            w3, [x0, #0xf]
    // 0x65ccf0: DecompressPointer r3
    //     0x65ccf0: add             x3, x3, HEAP, lsl #32
    // 0x65ccf4: stur            x3, [fp, #-0x18]
    // 0x65ccf8: r0 = LoadClassIdInstr(r1)
    //     0x65ccf8: ldur            x0, [x1, #-1]
    //     0x65ccfc: ubfx            x0, x0, #0xc, #0x14
    // 0x65cd00: r2 = 4
    //     0x65cd00: movz            x2, #0x4
    // 0x65cd04: r0 = GDT[cid_x0 + -0xfc6]()
    //     0x65cd04: sub             lr, x0, #0xfc6
    //     0x65cd08: ldr             lr, [x21, lr, lsl #3]
    //     0x65cd0c: blr             lr
    // 0x65cd10: mov             x2, x0
    // 0x65cd14: ldur            x0, [fp, #-0x18]
    // 0x65cd18: stur            x2, [fp, #-8]
    // 0x65cd1c: LoadField: r1 = r0->field_b
    //     0x65cd1c: ldur            w1, [x0, #0xb]
    // 0x65cd20: LoadField: r3 = r0->field_f
    //     0x65cd20: ldur            w3, [x0, #0xf]
    // 0x65cd24: DecompressPointer r3
    //     0x65cd24: add             x3, x3, HEAP, lsl #32
    // 0x65cd28: LoadField: r4 = r3->field_b
    //     0x65cd28: ldur            w4, [x3, #0xb]
    // 0x65cd2c: r3 = LoadInt32Instr(r1)
    //     0x65cd2c: sbfx            x3, x1, #1, #0x1f
    // 0x65cd30: stur            x3, [fp, #-0x20]
    // 0x65cd34: r1 = LoadInt32Instr(r4)
    //     0x65cd34: sbfx            x1, x4, #1, #0x1f
    // 0x65cd38: cmp             x3, x1
    // 0x65cd3c: b.ne            #0x65cd48
    // 0x65cd40: mov             x1, x0
    // 0x65cd44: r0 = _growToNextCapacity()
    //     0x65cd44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x65cd48: ldur            x0, [fp, #-0x18]
    // 0x65cd4c: ldur            x2, [fp, #-0x20]
    // 0x65cd50: add             x1, x2, #1
    // 0x65cd54: lsl             x3, x1, #1
    // 0x65cd58: StoreField: r0->field_b = r3
    //     0x65cd58: stur            w3, [x0, #0xb]
    // 0x65cd5c: LoadField: r1 = r0->field_f
    //     0x65cd5c: ldur            w1, [x0, #0xf]
    // 0x65cd60: DecompressPointer r1
    //     0x65cd60: add             x1, x1, HEAP, lsl #32
    // 0x65cd64: ldur            x0, [fp, #-8]
    // 0x65cd68: ArrayStore: r1[r2] = r0  ; List_4
    //     0x65cd68: add             x25, x1, x2, lsl #2
    //     0x65cd6c: add             x25, x25, #0xf
    //     0x65cd70: str             w0, [x25]
    //     0x65cd74: tbz             w0, #0, #0x65cd90
    //     0x65cd78: ldurb           w16, [x1, #-1]
    //     0x65cd7c: ldurb           w17, [x0, #-1]
    //     0x65cd80: and             x16, x17, x16, lsr #2
    //     0x65cd84: tst             x16, HEAP, lsr #32
    //     0x65cd88: b.eq            #0x65cd90
    //     0x65cd8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x65cd90: ldur            x16, [fp, #-0x10]
    // 0x65cd94: str             x16, [SP]
    // 0x65cd98: r0 = toString()
    //     0x65cd98: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x65cd9c: LeaveFrame
    //     0x65cd9c: mov             SP, fp
    //     0x65cda0: ldp             fp, lr, [SP], #0x10
    // 0x65cda4: ret
    //     0x65cda4: ret             
    // 0x65cda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x65cda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x65cdac: b               #0x65cc48
  }
  const get _ arguments(/* No info */) {
    // ** addr: 0xdb931c, size: 0xc
    // 0xdb931c: LoadField: r0 = r1->field_67
    //     0xdb931c: ldur            w0, [x1, #0x67]
    // 0xdb9320: DecompressPointer r0
    //     0xdb9320: add             x0, x0, HEAP, lsl #32
    // 0xdb9324: ret
    //     0xdb9324: ret             
  }
  const get _ name(/* No info */) {
    // ** addr: 0xdb9328, size: 0xc
    // 0xdb9328: LoadField: r0 = r1->field_6b
    //     0xdb9328: ldur            w0, [x1, #0x6b]
    // 0xdb932c: DecompressPointer r0
    //     0xdb932c: add             x0, x0, HEAP, lsl #32
    // 0xdb9330: ret
    //     0xdb9330: ret             
  }
}
