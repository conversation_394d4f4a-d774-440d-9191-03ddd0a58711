// lib: , url: package:get/get_utils/src/extensions/dynamic_extensions.dart

// class id: 1049574, size: 0x8
class :: {

  static _ GetDynamicUtils.printError(/* No info */) {
    // ** addr: 0x9189ec, size: 0xd4
    // 0x9189ec: EnterFrame
    //     0x9189ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9189f0: mov             fp, SP
    // 0x9189f4: AllocStack(0x40)
    //     0x9189f4: sub             SP, SP, #0x40
    // 0x9189f8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9189f8: mov             x3, x1
    //     0x9189fc: mov             x0, x2
    //     0x918a00: stur            x1, [fp, #-8]
    //     0x918a04: stur            x2, [fp, #-0x10]
    // 0x918a08: CheckStackOverflow
    //     0x918a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x918a0c: cmp             SP, x16
    //     0x918a10: b.ls            #0x918ab8
    // 0x918a14: r1 = Null
    //     0x918a14: mov             x1, NULL
    // 0x918a18: r2 = 4
    //     0x918a18: movz            x2, #0x4
    // 0x918a1c: r0 = AllocateArray()
    //     0x918a1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x918a20: stur            x0, [fp, #-0x18]
    // 0x918a24: r16 = "Error: "
    //     0x918a24: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c4e8] "Error: "
    //     0x918a28: ldr             x16, [x16, #0x4e8]
    // 0x918a2c: StoreField: r0->field_f = r16
    //     0x918a2c: stur            w16, [x0, #0xf]
    // 0x918a30: ldur            x16, [fp, #-8]
    // 0x918a34: str             x16, [SP]
    // 0x918a38: r0 = runtimeType()
    //     0x918a38: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0x918a3c: ldur            x1, [fp, #-0x18]
    // 0x918a40: ArrayStore: r1[1] = r0  ; List_4
    //     0x918a40: add             x25, x1, #0x13
    //     0x918a44: str             w0, [x25]
    //     0x918a48: tbz             w0, #0, #0x918a64
    //     0x918a4c: ldurb           w16, [x1, #-1]
    //     0x918a50: ldurb           w17, [x0, #-1]
    //     0x918a54: and             x16, x17, x16, lsr #2
    //     0x918a58: tst             x16, HEAP, lsr #32
    //     0x918a5c: b.eq            #0x918a64
    //     0x918a60: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x918a64: ldur            x16, [fp, #-0x18]
    // 0x918a68: str             x16, [SP]
    // 0x918a6c: r0 = _interpolate()
    //     0x918a6c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x918a70: r16 = Closure: (String, dynamic, String, {bool isError}) => void from Function 'printFunction': static.
    //     0x918a70: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f680] Closure: (String, dynamic, String, {bool isError}) => void from Function 'printFunction': static. (0x7e54fb318ac0)
    //     0x918a74: ldr             x16, [x16, #0x680]
    // 0x918a78: stp             x0, x16, [SP, #0x18]
    // 0x918a7c: ldur            x16, [fp, #-8]
    // 0x918a80: ldur            lr, [fp, #-0x10]
    // 0x918a84: stp             lr, x16, [SP, #8]
    // 0x918a88: r16 = true
    //     0x918a88: add             x16, NULL, #0x20  ; true
    // 0x918a8c: str             x16, [SP]
    // 0x918a90: r4 = 0
    //     0x918a90: movz            x4, #0
    // 0x918a94: ldr             x0, [SP, #0x20]
    // 0x918a98: r16 = UnlinkedCall_0x5f3c08
    //     0x918a98: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f688] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x918a9c: add             x16, x16, #0x688
    // 0x918aa0: ldp             x5, lr, [x16]
    // 0x918aa4: blr             lr
    // 0x918aa8: r0 = Null
    //     0x918aa8: mov             x0, NULL
    // 0x918aac: LeaveFrame
    //     0x918aac: mov             SP, fp
    //     0x918ab0: ldp             fp, lr, [SP], #0x10
    // 0x918ab4: ret
    //     0x918ab4: ret             
    // 0x918ab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x918ab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x918abc: b               #0x918a14
  }
  static void GetDynamicUtils.printInfo(dynamic) {
    // ** addr: 0x918e60, size: 0x98
    // 0x918e60: EnterFrame
    //     0x918e60: stp             fp, lr, [SP, #-0x10]!
    //     0x918e64: mov             fp, SP
    // 0x918e68: AllocStack(0x28)
    //     0x918e68: sub             SP, SP, #0x28
    // 0x918e6c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x918e6c: mov             x0, x1
    //     0x918e70: stur            x1, [fp, #-8]
    // 0x918e74: CheckStackOverflow
    //     0x918e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x918e78: cmp             SP, x16
    //     0x918e7c: b.ls            #0x918ef0
    // 0x918e80: r1 = Null
    //     0x918e80: mov             x1, NULL
    // 0x918e84: r2 = 4
    //     0x918e84: movz            x2, #0x4
    // 0x918e88: r0 = AllocateArray()
    //     0x918e88: bl              #0xec22fc  ; AllocateArrayStub
    // 0x918e8c: r16 = "Info: "
    //     0x918e8c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f700] "Info: "
    //     0x918e90: ldr             x16, [x16, #0x700]
    // 0x918e94: StoreField: r0->field_f = r16
    //     0x918e94: stur            w16, [x0, #0xf]
    // 0x918e98: r16 = HomeController
    //     0x918e98: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f708] Type: HomeController
    //     0x918e9c: ldr             x16, [x16, #0x708]
    // 0x918ea0: StoreField: r0->field_13 = r16
    //     0x918ea0: stur            w16, [x0, #0x13]
    // 0x918ea4: str             x0, [SP]
    // 0x918ea8: r0 = _interpolate()
    //     0x918ea8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x918eac: r16 = Closure: (String, dynamic, String, {bool isError}) => void from Function 'printFunction': static.
    //     0x918eac: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f680] Closure: (String, dynamic, String, {bool isError}) => void from Function 'printFunction': static. (0x7e54fb318ac0)
    //     0x918eb0: ldr             x16, [x16, #0x680]
    // 0x918eb4: stp             x0, x16, [SP, #0x10]
    // 0x918eb8: ldur            x16, [fp, #-8]
    // 0x918ebc: r30 = "Update Started"
    //     0x918ebc: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f710] "Update Started"
    //     0x918ec0: ldr             lr, [lr, #0x710]
    // 0x918ec4: stp             lr, x16, [SP]
    // 0x918ec8: r4 = 0
    //     0x918ec8: movz            x4, #0
    // 0x918ecc: ldr             x0, [SP, #0x18]
    // 0x918ed0: r16 = UnlinkedCall_0x5f3c08
    //     0x918ed0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f718] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x918ed4: add             x16, x16, #0x718
    // 0x918ed8: ldp             x5, lr, [x16]
    // 0x918edc: blr             lr
    // 0x918ee0: r0 = Null
    //     0x918ee0: mov             x0, NULL
    // 0x918ee4: LeaveFrame
    //     0x918ee4: mov             SP, fp
    //     0x918ee8: ldp             fp, lr, [SP], #0x10
    // 0x918eec: ret
    //     0x918eec: ret             
    // 0x918ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x918ef0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x918ef4: b               #0x918e80
  }
}
