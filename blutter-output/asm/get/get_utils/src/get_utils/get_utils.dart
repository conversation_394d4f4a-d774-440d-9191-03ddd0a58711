// lib: , url: package:get/get_utils/src/get_utils/get_utils.dart

// class id: 1049580, size: 0x8
class :: {

  static _ _isEmpty(/* No info */) {
    // ** addr: 0xb6ca8c, size: 0x40
    // 0xb6ca8c: EnterFrame
    //     0xb6ca8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6ca90: mov             fp, SP
    // 0xb6ca94: CheckStackOverflow
    //     0xb6ca94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6ca98: cmp             SP, x16
    //     0xb6ca9c: b.ls            #0xb6cac4
    // 0xb6caa0: r0 = trim()
    //     0xb6caa0: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xb6caa4: LoadField: r1 = r0->field_7
    //     0xb6caa4: ldur            w1, [x0, #7]
    // 0xb6caa8: cbz             w1, #0xb6cab4
    // 0xb6caac: r0 = false
    //     0xb6caac: add             x0, NULL, #0x30  ; false
    // 0xb6cab0: b               #0xb6cab8
    // 0xb6cab4: r0 = true
    //     0xb6cab4: add             x0, NULL, #0x20  ; true
    // 0xb6cab8: LeaveFrame
    //     0xb6cab8: mov             SP, fp
    //     0xb6cabc: ldp             fp, lr, [SP], #0x10
    // 0xb6cac0: ret
    //     0xb6cac0: ret             
    // 0xb6cac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6cac4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6cac8: b               #0xb6caa0
  }
}

// class id: 1702, size: 0x8, field offset: 0x8
abstract class GetUtils extends Object {

  static _ hasMatch(/* No info */) {
    // ** addr: 0x91087c, size: 0x80
    // 0x91087c: EnterFrame
    //     0x91087c: stp             fp, lr, [SP, #-0x10]!
    //     0x910880: mov             fp, SP
    // 0x910884: AllocStack(0x38)
    //     0x910884: sub             SP, SP, #0x38
    // 0x910888: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x910888: stur            x1, [fp, #-8]
    // 0x91088c: CheckStackOverflow
    //     0x91088c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x910890: cmp             SP, x16
    //     0x910894: b.ls            #0x9108f4
    // 0x910898: r16 = "^((((H|h)(T|t)|(F|f))(T|t)(P|p)((S|s)\?))\\://)\?(www.|[a-zA-Z0-9].)[a-zA-Z0-9\\-\\.]+\\.[a-zA-Z]{2,6}(\\:[0-9]{1,5})*(/($|[a-zA-Z0-9\\.\\,\\;\\\?\\\'\\\\\\+&amp;%\\$#\\=~_\\-]+))*$"
    //     0x910898: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac50] "^((((H|h)(T|t)|(F|f))(T|t)(P|p)((S|s)\?))\\://)\?(www.|[a-zA-Z0-9].)[a-zA-Z0-9\\-\\.]+\\.[a-zA-Z]{2,6}(\\:[0-9]{1,5})*(/($|[a-zA-Z0-9\\.\\,\\;\\\?\\\'\\\\\\+&amp;%\\$#\\=~_\\-]+))*$"
    //     0x91089c: ldr             x16, [x16, #0xc50]
    // 0x9108a0: stp             x16, NULL, [SP, #0x20]
    // 0x9108a4: r16 = false
    //     0x9108a4: add             x16, NULL, #0x30  ; false
    // 0x9108a8: r30 = true
    //     0x9108a8: add             lr, NULL, #0x20  ; true
    // 0x9108ac: stp             lr, x16, [SP, #0x10]
    // 0x9108b0: r16 = false
    //     0x9108b0: add             x16, NULL, #0x30  ; false
    // 0x9108b4: r30 = false
    //     0x9108b4: add             lr, NULL, #0x30  ; false
    // 0x9108b8: stp             lr, x16, [SP]
    // 0x9108bc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x9108bc: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x9108c0: r0 = _RegExp()
    //     0x9108c0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x9108c4: ldur            x16, [fp, #-8]
    // 0x9108c8: stp             x16, x0, [SP, #8]
    // 0x9108cc: str             xzr, [SP]
    // 0x9108d0: r0 = _ExecuteMatch()
    //     0x9108d0: bl              #0x644494  ; [dart:core] _RegExp::_ExecuteMatch
    // 0x9108d4: cmp             w0, NULL
    // 0x9108d8: b.ne            #0x9108e4
    // 0x9108dc: r0 = false
    //     0x9108dc: add             x0, NULL, #0x30  ; false
    // 0x9108e0: b               #0x9108e8
    // 0x9108e4: r0 = true
    //     0x9108e4: add             x0, NULL, #0x20  ; true
    // 0x9108e8: LeaveFrame
    //     0x9108e8: mov             SP, fp
    //     0x9108ec: ldp             fp, lr, [SP], #0x10
    // 0x9108f0: ret
    //     0x9108f0: ret             
    // 0x9108f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9108f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9108f8: b               #0x910898
  }
  [closure] static void printFunction(dynamic, String, dynamic, String, {bool isError}) {
    // ** addr: 0x918ac0, size: 0x9c
    // 0x918ac0: EnterFrame
    //     0x918ac0: stp             fp, lr, [SP, #-0x10]!
    //     0x918ac4: mov             fp, SP
    // 0x918ac8: AllocStack(0x8)
    //     0x918ac8: sub             SP, SP, #8
    // 0x918acc: SetupParameters(dynamic _ /* r2 */, dynamic _ /* r3 */, dynamic _ /* r5 */, {dynamic isError = false /* r0 */})
    //     0x918acc: ldur            w0, [x4, #0x13]
    //     0x918ad0: sub             x1, x0, #8
    //     0x918ad4: add             x2, fp, w1, sxtw #2
    //     0x918ad8: ldr             x2, [x2, #0x20]
    //     0x918adc: add             x3, fp, w1, sxtw #2
    //     0x918ae0: ldr             x3, [x3, #0x18]
    //     0x918ae4: add             x5, fp, w1, sxtw #2
    //     0x918ae8: ldr             x5, [x5, #0x10]
    //     0x918aec: ldur            w1, [x4, #0x1f]
    //     0x918af0: add             x1, x1, HEAP, lsl #32
    //     0x918af4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdb0] "isError"
    //     0x918af8: ldr             x16, [x16, #0xdb0]
    //     0x918afc: cmp             w1, w16
    //     0x918b00: b.ne            #0x918b1c
    //     0x918b04: ldur            w1, [x4, #0x23]
    //     0x918b08: add             x1, x1, HEAP, lsl #32
    //     0x918b0c: sub             w4, w0, w1
    //     0x918b10: add             x0, fp, w4, sxtw #2
    //     0x918b14: ldr             x0, [x0, #8]
    //     0x918b18: b               #0x918b20
    //     0x918b1c: add             x0, NULL, #0x30  ; false
    // 0x918b20: CheckStackOverflow
    //     0x918b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x918b24: cmp             SP, x16
    //     0x918b28: b.ls            #0x918b54
    // 0x918b2c: str             x0, [SP]
    // 0x918b30: mov             x1, x2
    // 0x918b34: mov             x2, x3
    // 0x918b38: mov             x3, x5
    // 0x918b3c: r4 = const [0, 0x4, 0x1, 0x3, isError, 0x3, null]
    //     0x918b3c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f6e0] List(7) [0, 0x4, 0x1, 0x3, "isError", 0x3, Null]
    //     0x918b40: ldr             x4, [x4, #0x6e0]
    // 0x918b44: r0 = printFunction()
    //     0x918b44: bl              #0x918b5c  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::printFunction
    // 0x918b48: LeaveFrame
    //     0x918b48: mov             SP, fp
    //     0x918b4c: ldp             fp, lr, [SP], #0x10
    // 0x918b50: ret
    //     0x918b50: ret             
    // 0x918b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x918b54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x918b58: b               #0x918b2c
  }
  static _ printFunction(/* No info */) {
    // ** addr: 0x918b5c, size: 0xf4
    // 0x918b5c: EnterFrame
    //     0x918b5c: stp             fp, lr, [SP, #-0x10]!
    //     0x918b60: mov             fp, SP
    // 0x918b64: AllocStack(0x28)
    //     0x918b64: sub             SP, SP, #0x28
    // 0x918b68: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, {dynamic isError = false /* r0, fp-0x8 */})
    //     0x918b68: stur            x1, [fp, #-0x10]
    //     0x918b6c: stur            x2, [fp, #-0x18]
    //     0x918b70: stur            x3, [fp, #-0x20]
    //     0x918b74: ldur            w0, [x4, #0x13]
    //     0x918b78: ldur            w5, [x4, #0x1f]
    //     0x918b7c: add             x5, x5, HEAP, lsl #32
    //     0x918b80: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdb0] "isError"
    //     0x918b84: ldr             x16, [x16, #0xdb0]
    //     0x918b88: cmp             w5, w16
    //     0x918b8c: b.ne            #0x918ba8
    //     0x918b90: ldur            w5, [x4, #0x23]
    //     0x918b94: add             x5, x5, HEAP, lsl #32
    //     0x918b98: sub             w4, w0, w5
    //     0x918b9c: add             x0, fp, w4, sxtw #2
    //     0x918ba0: ldr             x0, [x0, #8]
    //     0x918ba4: b               #0x918bac
    //     0x918ba8: add             x0, NULL, #0x30  ; false
    //     0x918bac: stur            x0, [fp, #-8]
    // 0x918bb0: CheckStackOverflow
    //     0x918bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x918bb4: cmp             SP, x16
    //     0x918bb8: b.ls            #0x918c48
    // 0x918bbc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x918bbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x918bc0: ldr             x0, [x0, #0x2670]
    //     0x918bc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x918bc8: cmp             w0, w16
    //     0x918bcc: b.ne            #0x918bd8
    //     0x918bd0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x918bd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x918bd8: r1 = Null
    //     0x918bd8: mov             x1, NULL
    // 0x918bdc: r2 = 10
    //     0x918bdc: movz            x2, #0xa
    // 0x918be0: r0 = AllocateArray()
    //     0x918be0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x918be4: mov             x1, x0
    // 0x918be8: ldur            x0, [fp, #-0x10]
    // 0x918bec: StoreField: r1->field_f = r0
    //     0x918bec: stur            w0, [x1, #0xf]
    // 0x918bf0: r16 = " "
    //     0x918bf0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x918bf4: StoreField: r1->field_13 = r16
    //     0x918bf4: stur            w16, [x1, #0x13]
    // 0x918bf8: ldur            x0, [fp, #-0x18]
    // 0x918bfc: ArrayStore: r1[0] = r0  ; List_4
    //     0x918bfc: stur            w0, [x1, #0x17]
    // 0x918c00: r16 = " "
    //     0x918c00: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x918c04: StoreField: r1->field_1b = r16
    //     0x918c04: stur            w16, [x1, #0x1b]
    // 0x918c08: ldur            x0, [fp, #-0x20]
    // 0x918c0c: StoreField: r1->field_1f = r0
    //     0x918c0c: stur            w0, [x1, #0x1f]
    // 0x918c10: str             x1, [SP]
    // 0x918c14: r0 = _interpolate()
    //     0x918c14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x918c18: mov             x1, x0
    // 0x918c1c: r0 = trim()
    //     0x918c1c: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0x918c20: ldur            x16, [fp, #-8]
    // 0x918c24: str             x16, [SP]
    // 0x918c28: mov             x1, x0
    // 0x918c2c: r4 = const [0, 0x2, 0x1, 0x1, isError, 0x1, null]
    //     0x918c2c: add             x4, PP, #0xb, lsl #12  ; [pp+0xbdb8] List(7) [0, 0x2, 0x1, 0x1, "isError", 0x1, Null]
    //     0x918c30: ldr             x4, [x4, #0xdb8]
    // 0x918c34: r0 = defaultLogWriterCallback()
    //     0x918c34: bl              #0x615c70  ; [package:get/get_core/src/log.dart] ::defaultLogWriterCallback
    // 0x918c38: r0 = Null
    //     0x918c38: mov             x0, NULL
    // 0x918c3c: LeaveFrame
    //     0x918c3c: mov             SP, fp
    //     0x918c40: ldp             fp, lr, [SP], #0x10
    // 0x918c44: ret
    //     0x918c44: ret             
    // 0x918c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x918c48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x918c4c: b               #0x918bbc
  }
  static _ capitalize(/* No info */) {
    // ** addr: 0xb6c9f0, size: 0x9c
    // 0xb6c9f0: EnterFrame
    //     0xb6c9f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb6c9f4: mov             fp, SP
    // 0xb6c9f8: AllocStack(0x20)
    //     0xb6c9f8: sub             SP, SP, #0x20
    // 0xb6c9fc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb6c9fc: mov             x0, x1
    //     0xb6ca00: stur            x1, [fp, #-8]
    // 0xb6ca04: CheckStackOverflow
    //     0xb6ca04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6ca08: cmp             SP, x16
    //     0xb6ca0c: b.ls            #0xb6ca84
    // 0xb6ca10: mov             x1, x0
    // 0xb6ca14: r0 = _isEmpty()
    //     0xb6ca14: bl              #0xb6ca8c  ; [package:get/get_utils/src/get_utils/get_utils.dart] ::_isEmpty
    // 0xb6ca18: tbnz            w0, #4, #0xb6ca2c
    // 0xb6ca1c: ldur            x0, [fp, #-8]
    // 0xb6ca20: LeaveFrame
    //     0xb6ca20: mov             SP, fp
    //     0xb6ca24: ldp             fp, lr, [SP], #0x10
    // 0xb6ca28: ret
    //     0xb6ca28: ret             
    // 0xb6ca2c: ldur            x1, [fp, #-8]
    // 0xb6ca30: r0 = LoadClassIdInstr(r1)
    //     0xb6ca30: ldur            x0, [x1, #-1]
    //     0xb6ca34: ubfx            x0, x0, #0xc, #0x14
    // 0xb6ca38: r2 = " "
    //     0xb6ca38: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb6ca3c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb6ca3c: sub             lr, x0, #1, lsl #12
    //     0xb6ca40: ldr             lr, [x21, lr, lsl #3]
    //     0xb6ca44: blr             lr
    // 0xb6ca48: r16 = <String?>
    //     0xb6ca48: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb6ca4c: stp             x0, x16, [SP, #8]
    // 0xb6ca50: r16 = Closure: (String) => String? from Function 'capitalizeFirst': static.
    //     0xb6ca50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ed90] Closure: (String) => String? from Function 'capitalizeFirst': static. (0x7e54fb56cacc)
    //     0xb6ca54: ldr             x16, [x16, #0xd90]
    // 0xb6ca58: str             x16, [SP]
    // 0xb6ca5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb6ca5c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb6ca60: r0 = map()
    //     0xb6ca60: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb6ca64: r16 = " "
    //     0xb6ca64: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb6ca68: str             x16, [SP]
    // 0xb6ca6c: mov             x1, x0
    // 0xb6ca70: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb6ca70: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb6ca74: r0 = join()
    //     0xb6ca74: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xb6ca78: LeaveFrame
    //     0xb6ca78: mov             SP, fp
    //     0xb6ca7c: ldp             fp, lr, [SP], #0x10
    // 0xb6ca80: ret
    //     0xb6ca80: ret             
    // 0xb6ca84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6ca84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6ca88: b               #0xb6ca10
  }
  [closure] static String? capitalizeFirst(dynamic, String) {
    // ** addr: 0xb6cacc, size: 0x30
    // 0xb6cacc: EnterFrame
    //     0xb6cacc: stp             fp, lr, [SP, #-0x10]!
    //     0xb6cad0: mov             fp, SP
    // 0xb6cad4: CheckStackOverflow
    //     0xb6cad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6cad8: cmp             SP, x16
    //     0xb6cadc: b.ls            #0xb6caf4
    // 0xb6cae0: ldr             x1, [fp, #0x10]
    // 0xb6cae4: r0 = capitalizeFirst()
    //     0xb6cae4: bl              #0xb6cafc  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::capitalizeFirst
    // 0xb6cae8: LeaveFrame
    //     0xb6cae8: mov             SP, fp
    //     0xb6caec: ldp             fp, lr, [SP], #0x10
    // 0xb6caf0: ret
    //     0xb6caf0: ret             
    // 0xb6caf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6caf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6caf8: b               #0xb6cae0
  }
  static _ capitalizeFirst(/* No info */) {
    // ** addr: 0xb6cafc, size: 0xb4
    // 0xb6cafc: EnterFrame
    //     0xb6cafc: stp             fp, lr, [SP, #-0x10]!
    //     0xb6cb00: mov             fp, SP
    // 0xb6cb04: AllocStack(0x18)
    //     0xb6cb04: sub             SP, SP, #0x18
    // 0xb6cb08: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xb6cb08: mov             x0, x1
    //     0xb6cb0c: stur            x1, [fp, #-8]
    // 0xb6cb10: CheckStackOverflow
    //     0xb6cb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6cb14: cmp             SP, x16
    //     0xb6cb18: b.ls            #0xb6cba8
    // 0xb6cb1c: mov             x1, x0
    // 0xb6cb20: r0 = _isEmpty()
    //     0xb6cb20: bl              #0xb6ca8c  ; [package:get/get_utils/src/get_utils/get_utils.dart] ::_isEmpty
    // 0xb6cb24: tbnz            w0, #4, #0xb6cb38
    // 0xb6cb28: ldur            x0, [fp, #-8]
    // 0xb6cb2c: LeaveFrame
    //     0xb6cb2c: mov             SP, fp
    //     0xb6cb30: ldp             fp, lr, [SP], #0x10
    // 0xb6cb34: ret
    //     0xb6cb34: ret             
    // 0xb6cb38: ldur            x16, [fp, #-8]
    // 0xb6cb3c: stp             xzr, x16, [SP]
    // 0xb6cb40: r0 = []()
    //     0xb6cb40: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb6cb44: r1 = LoadClassIdInstr(r0)
    //     0xb6cb44: ldur            x1, [x0, #-1]
    //     0xb6cb48: ubfx            x1, x1, #0xc, #0x14
    // 0xb6cb4c: str             x0, [SP]
    // 0xb6cb50: mov             x0, x1
    // 0xb6cb54: r0 = GDT[cid_x0 + -0xff6]()
    //     0xb6cb54: sub             lr, x0, #0xff6
    //     0xb6cb58: ldr             lr, [x21, lr, lsl #3]
    //     0xb6cb5c: blr             lr
    // 0xb6cb60: ldur            x1, [fp, #-8]
    // 0xb6cb64: r2 = 1
    //     0xb6cb64: movz            x2, #0x1
    // 0xb6cb68: stur            x0, [fp, #-8]
    // 0xb6cb6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb6cb6c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb6cb70: r0 = substring()
    //     0xb6cb70: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xb6cb74: r1 = LoadClassIdInstr(r0)
    //     0xb6cb74: ldur            x1, [x0, #-1]
    //     0xb6cb78: ubfx            x1, x1, #0xc, #0x14
    // 0xb6cb7c: str             x0, [SP]
    // 0xb6cb80: mov             x0, x1
    // 0xb6cb84: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb6cb84: sub             lr, x0, #0xffe
    //     0xb6cb88: ldr             lr, [x21, lr, lsl #3]
    //     0xb6cb8c: blr             lr
    // 0xb6cb90: ldur            x16, [fp, #-8]
    // 0xb6cb94: stp             x0, x16, [SP]
    // 0xb6cb98: r0 = +()
    //     0xb6cb98: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xb6cb9c: LeaveFrame
    //     0xb6cb9c: mov             SP, fp
    //     0xb6cba0: ldp             fp, lr, [SP], #0x10
    // 0xb6cba4: ret
    //     0xb6cba4: ret             
    // 0xb6cba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6cba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6cbac: b               #0xb6cb1c
  }
}
