// lib: , url: package:hive/src/box/box_base_impl.dart

// class id: 1049604, size: 0x8
class :: {
}

// class id: 1626, size: 0x24, field offset: 0x8
abstract class BoxBaseImpl<X0> extends Object
    implements BoxBase<X0> {

  late Keystore<X0> keystore; // offset: 0x1c

  _ checkOpen(/* No info */) {
    // ** addr: 0x68f838, size: 0x44
    // 0x68f838: EnterFrame
    //     0x68f838: stp             fp, lr, [SP, #-0x10]!
    //     0x68f83c: mov             fp, SP
    // 0x68f840: LoadField: r0 = r1->field_1f
    //     0x68f840: ldur            w0, [x1, #0x1f]
    // 0x68f844: DecompressPointer r0
    //     0x68f844: add             x0, x0, HEAP, lsl #32
    // 0x68f848: tbnz            w0, #4, #0x68f85c
    // 0x68f84c: r0 = Null
    //     0x68f84c: mov             x0, NULL
    // 0x68f850: LeaveFrame
    //     0x68f850: mov             SP, fp
    //     0x68f854: ldp             fp, lr, [SP], #0x10
    // 0x68f858: ret
    //     0x68f858: ret             
    // 0x68f85c: r0 = HiveError()
    //     0x68f85c: bl              #0x68f4ec  ; AllocateHiveErrorStub -> HiveError (size=0x10)
    // 0x68f860: mov             x1, x0
    // 0x68f864: r0 = "Box has already been closed."
    //     0x68f864: add             x0, PP, #8, lsl #12  ; [pp+0x80d0] "Box has already been closed."
    //     0x68f868: ldr             x0, [x0, #0xd0]
    // 0x68f86c: StoreField: r1->field_b = r0
    //     0x68f86c: stur            w0, [x1, #0xb]
    // 0x68f870: mov             x0, x1
    // 0x68f874: r0 = Throw()
    //     0x68f874: bl              #0xec04b8  ; ThrowStub
    // 0x68f878: brk             #0
  }
  get _ length(/* No info */) {
    // ** addr: 0x7bd534, size: 0x68
    // 0x7bd534: EnterFrame
    //     0x7bd534: stp             fp, lr, [SP, #-0x10]!
    //     0x7bd538: mov             fp, SP
    // 0x7bd53c: AllocStack(0x8)
    //     0x7bd53c: sub             SP, SP, #8
    // 0x7bd540: SetupParameters(BoxBaseImpl<X0> this /* r1 => r0, fp-0x8 */)
    //     0x7bd540: mov             x0, x1
    //     0x7bd544: stur            x1, [fp, #-8]
    // 0x7bd548: CheckStackOverflow
    //     0x7bd548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bd54c: cmp             SP, x16
    //     0x7bd550: b.ls            #0x7bd58c
    // 0x7bd554: mov             x1, x0
    // 0x7bd558: r0 = checkOpen()
    //     0x7bd558: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7bd55c: ldur            x1, [fp, #-8]
    // 0x7bd560: LoadField: r2 = r1->field_1b
    //     0x7bd560: ldur            w2, [x1, #0x1b]
    // 0x7bd564: DecompressPointer r2
    //     0x7bd564: add             x2, x2, HEAP, lsl #32
    // 0x7bd568: r16 = Sentinel
    //     0x7bd568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7bd56c: cmp             w2, w16
    // 0x7bd570: b.eq            #0x7bd594
    // 0x7bd574: LoadField: r1 = r2->field_13
    //     0x7bd574: ldur            w1, [x2, #0x13]
    // 0x7bd578: DecompressPointer r1
    //     0x7bd578: add             x1, x1, HEAP, lsl #32
    // 0x7bd57c: LoadField: r0 = r1->field_1f
    //     0x7bd57c: ldur            x0, [x1, #0x1f]
    // 0x7bd580: LeaveFrame
    //     0x7bd580: mov             SP, fp
    //     0x7bd584: ldp             fp, lr, [SP], #0x10
    // 0x7bd588: ret
    //     0x7bd588: ret             
    // 0x7bd58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bd58c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bd590: b               #0x7bd554
    // 0x7bd594: r9 = keystore
    //     0x7bd594: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7bd598: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7bd598: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ isEmpty(/* No info */) {
    // ** addr: 0x7bd59c, size: 0x40
    // 0x7bd59c: EnterFrame
    //     0x7bd59c: stp             fp, lr, [SP, #-0x10]!
    //     0x7bd5a0: mov             fp, SP
    // 0x7bd5a4: CheckStackOverflow
    //     0x7bd5a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bd5a8: cmp             SP, x16
    //     0x7bd5ac: b.ls            #0x7bd5d4
    // 0x7bd5b0: r0 = length()
    //     0x7bd5b0: bl              #0x7bd534  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::length
    // 0x7bd5b4: cbz             x0, #0x7bd5c0
    // 0x7bd5b8: r1 = false
    //     0x7bd5b8: add             x1, NULL, #0x30  ; false
    // 0x7bd5bc: b               #0x7bd5c4
    // 0x7bd5c0: r1 = true
    //     0x7bd5c0: add             x1, NULL, #0x20  ; true
    // 0x7bd5c4: mov             x0, x1
    // 0x7bd5c8: LeaveFrame
    //     0x7bd5c8: mov             SP, fp
    //     0x7bd5cc: ldp             fp, lr, [SP], #0x10
    // 0x7bd5d0: ret
    //     0x7bd5d0: ret             
    // 0x7bd5d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bd5d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bd5d8: b               #0x7bd5b0
  }
  _ performCompactionIfNeeded(/* No info */) {
    // ** addr: 0x7bef80, size: 0xdc
    // 0x7bef80: EnterFrame
    //     0x7bef80: stp             fp, lr, [SP, #-0x10]!
    //     0x7bef84: mov             fp, SP
    // 0x7bef88: AllocStack(0x8)
    //     0x7bef88: sub             SP, SP, #8
    // 0x7bef8c: SetupParameters(BoxBaseImpl<X0> this /* r1 => r0, fp-0x8 */)
    //     0x7bef8c: mov             x0, x1
    //     0x7bef90: stur            x1, [fp, #-8]
    // 0x7bef94: CheckStackOverflow
    //     0x7bef94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bef98: cmp             SP, x16
    //     0x7bef9c: b.ls            #0x7bf04c
    // 0x7befa0: LoadField: r1 = r0->field_1b
    //     0x7befa0: ldur            w1, [x0, #0x1b]
    // 0x7befa4: DecompressPointer r1
    //     0x7befa4: add             x1, x1, HEAP, lsl #32
    // 0x7befa8: r16 = Sentinel
    //     0x7befa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7befac: cmp             w1, w16
    // 0x7befb0: b.eq            #0x7bf054
    // 0x7befb4: r0 = length()
    //     0x7befb4: bl              #0x7c1f18  ; [package:hive/src/box/keystore.dart] Keystore::length
    // 0x7befb8: ldur            x1, [fp, #-8]
    // 0x7befbc: LoadField: r2 = r1->field_1b
    //     0x7befbc: ldur            w2, [x1, #0x1b]
    // 0x7befc0: DecompressPointer r2
    //     0x7befc0: add             x2, x2, HEAP, lsl #32
    // 0x7befc4: LoadField: r3 = r2->field_1b
    //     0x7befc4: ldur            x3, [x2, #0x1b]
    // 0x7befc8: cmp             x3, #0x3c
    // 0x7befcc: b.le            #0x7beff8
    // 0x7befd0: d0 = 0.150000
    //     0x7befd0: ldr             d0, [PP, #0x5798]  ; [pp+0x5798] IMM: double(0.15) from 0x3fc3333333333333
    // 0x7befd4: scvtf           d1, x3
    // 0x7befd8: scvtf           d2, x0
    // 0x7befdc: fdiv            d3, d1, d2
    // 0x7befe0: fcmp            d3, d0
    // 0x7befe4: b.le            #0x7beff8
    // 0x7befe8: r0 = compact()
    //     0x7befe8: bl              #0x7bf05c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::compact
    // 0x7befec: LeaveFrame
    //     0x7befec: mov             SP, fp
    //     0x7beff0: ldp             fp, lr, [SP], #0x10
    // 0x7beff4: ret
    //     0x7beff4: ret             
    // 0x7beff8: r1 = <void?>
    //     0x7beff8: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7beffc: r0 = _Future()
    //     0x7beffc: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x7bf000: stur            x0, [fp, #-8]
    // 0x7bf004: StoreField: r0->field_b = rZR
    //     0x7bf004: stur            xzr, [x0, #0xb]
    // 0x7bf008: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x7bf008: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7bf00c: ldr             x0, [x0, #0x7a0]
    //     0x7bf010: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7bf014: cmp             w0, w16
    //     0x7bf018: b.ne            #0x7bf024
    //     0x7bf01c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x7bf020: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7bf024: mov             x1, x0
    // 0x7bf028: ldur            x0, [fp, #-8]
    // 0x7bf02c: StoreField: r0->field_13 = r1
    //     0x7bf02c: stur            w1, [x0, #0x13]
    // 0x7bf030: mov             x1, x0
    // 0x7bf034: r2 = Null
    //     0x7bf034: mov             x2, NULL
    // 0x7bf038: r0 = _asyncComplete()
    //     0x7bf038: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x7bf03c: ldur            x0, [fp, #-8]
    // 0x7bf040: LeaveFrame
    //     0x7bf040: mov             SP, fp
    //     0x7bf044: ldp             fp, lr, [SP], #0x10
    // 0x7bf048: ret
    //     0x7bf048: ret             
    // 0x7bf04c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bf04c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bf050: b               #0x7befa0
    // 0x7bf054: r9 = keystore
    //     0x7bf054: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7bf058: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7bf058: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ compact(/* No info */) async {
    // ** addr: 0x7bf05c, size: 0x1c4
    // 0x7bf05c: EnterFrame
    //     0x7bf05c: stp             fp, lr, [SP, #-0x10]!
    //     0x7bf060: mov             fp, SP
    // 0x7bf064: AllocStack(0x40)
    //     0x7bf064: sub             SP, SP, #0x40
    // 0x7bf068: SetupParameters(BoxBaseImpl<X0> this /* r1 => r1, fp-0x10 */)
    //     0x7bf068: stur            NULL, [fp, #-8]
    //     0x7bf06c: stur            x1, [fp, #-0x10]
    // 0x7bf070: CheckStackOverflow
    //     0x7bf070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bf074: cmp             SP, x16
    //     0x7bf078: b.ls            #0x7bf210
    // 0x7bf07c: InitAsync() -> Future<void?>
    //     0x7bf07c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7bf080: bl              #0x661298  ; InitAsyncStub
    // 0x7bf084: ldur            x1, [fp, #-0x10]
    // 0x7bf088: r0 = checkOpen()
    //     0x7bf088: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7bf08c: ldur            x0, [fp, #-0x10]
    // 0x7bf090: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7bf090: ldur            w2, [x0, #0x17]
    // 0x7bf094: DecompressPointer r2
    //     0x7bf094: add             x2, x2, HEAP, lsl #32
    // 0x7bf098: stur            x2, [fp, #-0x20]
    // 0x7bf09c: r3 = LoadClassIdInstr(r2)
    //     0x7bf09c: ldur            x3, [x2, #-1]
    //     0x7bf0a0: ubfx            x3, x3, #0xc, #0x14
    // 0x7bf0a4: stur            x3, [fp, #-0x18]
    // 0x7bf0a8: cmp             x3, #0x664
    // 0x7bf0ac: b.ne            #0x7bf0c0
    // 0x7bf0b0: LoadField: r1 = r2->field_3b
    //     0x7bf0b0: ldur            w1, [x2, #0x3b]
    // 0x7bf0b4: DecompressPointer r1
    //     0x7bf0b4: add             x1, x1, HEAP, lsl #32
    // 0x7bf0b8: tbz             w1, #4, #0x7bf0d4
    // 0x7bf0bc: b               #0x7bf0cc
    // 0x7bf0c0: LoadField: r1 = r2->field_13
    //     0x7bf0c0: ldur            w1, [x2, #0x13]
    // 0x7bf0c4: DecompressPointer r1
    //     0x7bf0c4: add             x1, x1, HEAP, lsl #32
    // 0x7bf0c8: tbz             w1, #4, #0x7bf0d4
    // 0x7bf0cc: r0 = Null
    //     0x7bf0cc: mov             x0, NULL
    // 0x7bf0d0: r0 = ReturnAsyncNotFuture()
    //     0x7bf0d0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7bf0d4: LoadField: r1 = r0->field_1b
    //     0x7bf0d4: ldur            w1, [x0, #0x1b]
    // 0x7bf0d8: DecompressPointer r1
    //     0x7bf0d8: add             x1, x1, HEAP, lsl #32
    // 0x7bf0dc: r16 = Sentinel
    //     0x7bf0dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7bf0e0: cmp             w1, w16
    // 0x7bf0e4: b.eq            #0x7bf218
    // 0x7bf0e8: LoadField: r4 = r1->field_1b
    //     0x7bf0e8: ldur            x4, [x1, #0x1b]
    // 0x7bf0ec: cbnz            x4, #0x7bf0f8
    // 0x7bf0f0: r0 = Null
    //     0x7bf0f0: mov             x0, NULL
    // 0x7bf0f4: r0 = ReturnAsyncNotFuture()
    //     0x7bf0f4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7bf0f8: r0 = frames()
    //     0x7bf0f8: bl              #0x7bf4e8  ; [package:hive/src/box/keystore.dart] Keystore::frames
    // 0x7bf0fc: mov             x1, x0
    // 0x7bf100: ldur            x0, [fp, #-0x18]
    // 0x7bf104: stur            x1, [fp, #-0x28]
    // 0x7bf108: cmp             x0, #0x664
    // 0x7bf10c: b.ne            #0x7bf1f0
    // 0x7bf110: ldur            x0, [fp, #-0x20]
    // 0x7bf114: r1 = 2
    //     0x7bf114: movz            x1, #0x2
    // 0x7bf118: r0 = AllocateContext()
    //     0x7bf118: bl              #0xec126c  ; AllocateContextStub
    // 0x7bf11c: mov             x1, x0
    // 0x7bf120: ldur            x0, [fp, #-0x20]
    // 0x7bf124: StoreField: r1->field_f = r0
    //     0x7bf124: stur            w0, [x1, #0xf]
    // 0x7bf128: ldur            x2, [fp, #-0x28]
    // 0x7bf12c: StoreField: r1->field_13 = r2
    //     0x7bf12c: stur            w2, [x1, #0x13]
    // 0x7bf130: LoadField: r2 = r0->field_37
    //     0x7bf130: ldur            w2, [x0, #0x37]
    // 0x7bf134: DecompressPointer r2
    //     0x7bf134: add             x2, x2, HEAP, lsl #32
    // 0x7bf138: tbnz            w2, #4, #0x7bf188
    // 0x7bf13c: r1 = <void?>
    //     0x7bf13c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7bf140: r0 = _Future()
    //     0x7bf140: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x7bf144: stur            x0, [fp, #-0x28]
    // 0x7bf148: StoreField: r0->field_b = rZR
    //     0x7bf148: stur            xzr, [x0, #0xb]
    // 0x7bf14c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x7bf14c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7bf150: ldr             x0, [x0, #0x7a0]
    //     0x7bf154: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7bf158: cmp             w0, w16
    //     0x7bf15c: b.ne            #0x7bf168
    //     0x7bf160: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x7bf164: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7bf168: mov             x1, x0
    // 0x7bf16c: ldur            x0, [fp, #-0x28]
    // 0x7bf170: StoreField: r0->field_13 = r1
    //     0x7bf170: stur            w1, [x0, #0x13]
    // 0x7bf174: mov             x1, x0
    // 0x7bf178: r2 = Null
    //     0x7bf178: mov             x2, NULL
    // 0x7bf17c: r0 = _asyncComplete()
    //     0x7bf17c: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x7bf180: ldur            x2, [fp, #-0x28]
    // 0x7bf184: b               #0x7bf1c8
    // 0x7bf188: r2 = true
    //     0x7bf188: add             x2, NULL, #0x20  ; true
    // 0x7bf18c: StoreField: r0->field_37 = r2
    //     0x7bf18c: stur            w2, [x0, #0x37]
    // 0x7bf190: LoadField: r3 = r0->field_1b
    //     0x7bf190: ldur            w3, [x0, #0x1b]
    // 0x7bf194: DecompressPointer r3
    //     0x7bf194: add             x3, x3, HEAP, lsl #32
    // 0x7bf198: mov             x2, x1
    // 0x7bf19c: stur            x3, [fp, #-0x28]
    // 0x7bf1a0: r1 = Function '<anonymous closure>':.
    //     0x7bf1a0: add             x1, PP, #8, lsl #12  ; [pp+0x8558] AnonymousClosure: (0x7bf520), of [package:hive/src/backend/vm/storage_backend_vm.dart] StorageBackendVm
    //     0x7bf1a4: ldr             x1, [x1, #0x558]
    // 0x7bf1a8: r0 = AllocateClosure()
    //     0x7bf1a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7bf1ac: r16 = <void?>
    //     0x7bf1ac: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x7bf1b0: ldur            lr, [fp, #-0x28]
    // 0x7bf1b4: stp             lr, x16, [SP, #8]
    // 0x7bf1b8: str             x0, [SP]
    // 0x7bf1bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bf1bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bf1c0: r0 = syncReadWrite()
    //     0x7bf1c0: bl              #0x7bf22c  ; [package:hive/src/backend/vm/read_write_sync.dart] ReadWriteSync::syncReadWrite
    // 0x7bf1c4: mov             x2, x0
    // 0x7bf1c8: ldur            x1, [fp, #-0x10]
    // 0x7bf1cc: mov             x0, x2
    // 0x7bf1d0: stur            x2, [fp, #-0x20]
    // 0x7bf1d4: r0 = Await()
    //     0x7bf1d4: bl              #0x661044  ; AwaitStub
    // 0x7bf1d8: ldur            x0, [fp, #-0x10]
    // 0x7bf1dc: LoadField: r1 = r0->field_1b
    //     0x7bf1dc: ldur            w1, [x0, #0x1b]
    // 0x7bf1e0: DecompressPointer r1
    //     0x7bf1e0: add             x1, x1, HEAP, lsl #32
    // 0x7bf1e4: r0 = resetDeletedEntries()
    //     0x7bf1e4: bl              #0x7bf220  ; [package:hive/src/box/keystore.dart] Keystore::resetDeletedEntries
    // 0x7bf1e8: r0 = Null
    //     0x7bf1e8: mov             x0, NULL
    // 0x7bf1ec: r0 = ReturnAsyncNotFuture()
    //     0x7bf1ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7bf1f0: r0 = UnsupportedError()
    //     0x7bf1f0: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x7bf1f4: mov             x1, x0
    // 0x7bf1f8: r0 = "This operation is unsupported for memory boxes."
    //     0x7bf1f8: add             x0, PP, #8, lsl #12  ; [pp+0x8560] "This operation is unsupported for memory boxes."
    //     0x7bf1fc: ldr             x0, [x0, #0x560]
    // 0x7bf200: StoreField: r1->field_b = r0
    //     0x7bf200: stur            w0, [x1, #0xb]
    // 0x7bf204: mov             x0, x1
    // 0x7bf208: r0 = Throw()
    //     0x7bf208: bl              #0xec04b8  ; ThrowStub
    // 0x7bf20c: brk             #0
    // 0x7bf210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bf210: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bf214: b               #0x7bf07c
    // 0x7bf218: r9 = keystore
    //     0x7bf218: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7bf21c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7bf21c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ put(/* No info */) {
    // ** addr: 0x819b0c, size: 0xe0
    // 0x819b0c: EnterFrame
    //     0x819b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x819b10: mov             fp, SP
    // 0x819b14: AllocStack(0x30)
    //     0x819b14: sub             SP, SP, #0x30
    // 0x819b18: SetupParameters(BoxBaseImpl<X0> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x819b18: mov             x5, x1
    //     0x819b1c: mov             x4, x2
    //     0x819b20: stur            x1, [fp, #-0x10]
    //     0x819b24: stur            x2, [fp, #-0x18]
    //     0x819b28: stur            x3, [fp, #-0x20]
    // 0x819b2c: CheckStackOverflow
    //     0x819b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x819b30: cmp             SP, x16
    //     0x819b34: b.ls            #0x819be4
    // 0x819b38: LoadField: r6 = r5->field_7
    //     0x819b38: ldur            w6, [x5, #7]
    // 0x819b3c: DecompressPointer r6
    //     0x819b3c: add             x6, x6, HEAP, lsl #32
    // 0x819b40: mov             x0, x3
    // 0x819b44: mov             x2, x6
    // 0x819b48: stur            x6, [fp, #-8]
    // 0x819b4c: r1 = Null
    //     0x819b4c: mov             x1, NULL
    // 0x819b50: cmp             w2, NULL
    // 0x819b54: b.eq            #0x819b74
    // 0x819b58: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x819b58: ldur            w4, [x2, #0x17]
    // 0x819b5c: DecompressPointer r4
    //     0x819b5c: add             x4, x4, HEAP, lsl #32
    // 0x819b60: r8 = X0
    //     0x819b60: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x819b64: LoadField: r9 = r4->field_7
    //     0x819b64: ldur            x9, [x4, #7]
    // 0x819b68: r3 = Null
    //     0x819b68: add             x3, PP, #8, lsl #12  ; [pp+0x82c0] Null
    //     0x819b6c: ldr             x3, [x3, #0x2c0]
    // 0x819b70: blr             x9
    // 0x819b74: ldur            x2, [fp, #-8]
    // 0x819b78: r1 = Null
    //     0x819b78: mov             x1, NULL
    // 0x819b7c: r3 = <dynamic, X0>
    //     0x819b7c: add             x3, PP, #8, lsl #12  ; [pp+0x82d0] TypeArguments: <dynamic, X0>
    //     0x819b80: ldr             x3, [x3, #0x2d0]
    // 0x819b84: r0 = Null
    //     0x819b84: mov             x0, NULL
    // 0x819b88: cmp             x2, x0
    // 0x819b8c: b.eq            #0x819b9c
    // 0x819b90: r30 = InstantiateTypeArgumentsStub
    //     0x819b90: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x819b94: LoadField: r30 = r30->field_7
    //     0x819b94: ldur            lr, [lr, #7]
    // 0x819b98: blr             lr
    // 0x819b9c: r1 = Null
    //     0x819b9c: mov             x1, NULL
    // 0x819ba0: r2 = 4
    //     0x819ba0: movz            x2, #0x4
    // 0x819ba4: stur            x0, [fp, #-8]
    // 0x819ba8: r0 = AllocateArray()
    //     0x819ba8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x819bac: mov             x1, x0
    // 0x819bb0: ldur            x0, [fp, #-0x18]
    // 0x819bb4: StoreField: r1->field_f = r0
    //     0x819bb4: stur            w0, [x1, #0xf]
    // 0x819bb8: ldur            x0, [fp, #-0x20]
    // 0x819bbc: StoreField: r1->field_13 = r0
    //     0x819bbc: stur            w0, [x1, #0x13]
    // 0x819bc0: ldur            x16, [fp, #-8]
    // 0x819bc4: stp             x1, x16, [SP]
    // 0x819bc8: r0 = Map._fromLiteral()
    //     0x819bc8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x819bcc: ldur            x1, [fp, #-0x10]
    // 0x819bd0: mov             x2, x0
    // 0x819bd4: r0 = putAll()
    //     0x819bd4: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x819bd8: LeaveFrame
    //     0x819bd8: mov             SP, fp
    //     0x819bdc: ldp             fp, lr, [SP], #0x10
    // 0x819be0: ret
    //     0x819be0: ret             
    // 0x819be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x819be4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x819be8: b               #0x819b38
  }
  _ close(/* No info */) async {
    // ** addr: 0x834878, size: 0x140
    // 0x834878: EnterFrame
    //     0x834878: stp             fp, lr, [SP, #-0x10]!
    //     0x83487c: mov             fp, SP
    // 0x834880: AllocStack(0x30)
    //     0x834880: sub             SP, SP, #0x30
    // 0x834884: SetupParameters(BoxBaseImpl<X0> this /* r1 => r1, fp-0x10 */)
    //     0x834884: stur            NULL, [fp, #-8]
    //     0x834888: stur            x1, [fp, #-0x10]
    // 0x83488c: CheckStackOverflow
    //     0x83488c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x834890: cmp             SP, x16
    //     0x834894: b.ls            #0x8349a8
    // 0x834898: InitAsync() -> Future<void?>
    //     0x834898: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x83489c: bl              #0x661298  ; InitAsyncStub
    // 0x8348a0: ldur            x0, [fp, #-0x10]
    // 0x8348a4: LoadField: r1 = r0->field_1f
    //     0x8348a4: ldur            w1, [x0, #0x1f]
    // 0x8348a8: DecompressPointer r1
    //     0x8348a8: add             x1, x1, HEAP, lsl #32
    // 0x8348ac: tbz             w1, #4, #0x8348b8
    // 0x8348b0: r0 = Null
    //     0x8348b0: mov             x0, NULL
    // 0x8348b4: r0 = ReturnAsyncNotFuture()
    //     0x8348b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8348b8: r1 = false
    //     0x8348b8: add             x1, NULL, #0x30  ; false
    // 0x8348bc: StoreField: r0->field_1f = r1
    //     0x8348bc: stur            w1, [x0, #0x1f]
    // 0x8348c0: LoadField: r1 = r0->field_1b
    //     0x8348c0: ldur            w1, [x0, #0x1b]
    // 0x8348c4: DecompressPointer r1
    //     0x8348c4: add             x1, x1, HEAP, lsl #32
    // 0x8348c8: r16 = Sentinel
    //     0x8348c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8348cc: cmp             w1, w16
    // 0x8348d0: b.eq            #0x8349b0
    // 0x8348d4: r0 = close()
    //     0x8348d4: bl              #0x834a24  ; [package:hive/src/box/keystore.dart] Keystore::close
    // 0x8348d8: mov             x1, x0
    // 0x8348dc: stur            x1, [fp, #-0x18]
    // 0x8348e0: r0 = Await()
    //     0x8348e0: bl              #0x661044  ; AwaitStub
    // 0x8348e4: ldur            x0, [fp, #-0x10]
    // 0x8348e8: LoadField: r1 = r0->field_f
    //     0x8348e8: ldur            w1, [x0, #0xf]
    // 0x8348ec: DecompressPointer r1
    //     0x8348ec: add             x1, x1, HEAP, lsl #32
    // 0x8348f0: LoadField: r2 = r0->field_b
    //     0x8348f0: ldur            w2, [x0, #0xb]
    // 0x8348f4: DecompressPointer r2
    //     0x8348f4: add             x2, x2, HEAP, lsl #32
    // 0x8348f8: r0 = unregisterBox()
    //     0x8348f8: bl              #0x8349b8  ; [package:hive/src/hive_impl.dart] HiveImpl::unregisterBox
    // 0x8348fc: ldur            x0, [fp, #-0x10]
    // 0x834900: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x834900: ldur            w2, [x0, #0x17]
    // 0x834904: DecompressPointer r2
    //     0x834904: add             x2, x2, HEAP, lsl #32
    // 0x834908: r0 = LoadClassIdInstr(r2)
    //     0x834908: ldur            x0, [x2, #-1]
    //     0x83490c: ubfx            x0, x0, #0xc, #0x14
    // 0x834910: cmp             x0, #0x664
    // 0x834914: b.ne            #0x83494c
    // 0x834918: LoadField: r0 = r2->field_1b
    //     0x834918: ldur            w0, [x2, #0x1b]
    // 0x83491c: DecompressPointer r0
    //     0x83491c: add             x0, x0, HEAP, lsl #32
    // 0x834920: stur            x0, [fp, #-0x10]
    // 0x834924: r1 = Function '_closeInternal@1331487889':.
    //     0x834924: ldr             x1, [PP, #0x7cb0]  ; [pp+0x7cb0] AnonymousClosure: (0x834a94), in [package:hive/src/backend/vm/storage_backend_vm.dart] StorageBackendVm::_closeInternal (0x834acc)
    // 0x834928: r0 = AllocateClosure()
    //     0x834928: bl              #0xec1630  ; AllocateClosureStub
    // 0x83492c: r16 = <void?>
    //     0x83492c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x834930: ldur            lr, [fp, #-0x10]
    // 0x834934: stp             lr, x16, [SP, #8]
    // 0x834938: str             x0, [SP]
    // 0x83493c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x83493c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x834940: r0 = syncReadWrite()
    //     0x834940: bl              #0x7bf22c  ; [package:hive/src/backend/vm/read_write_sync.dart] ReadWriteSync::syncReadWrite
    // 0x834944: mov             x1, x0
    // 0x834948: b               #0x834994
    // 0x83494c: r1 = <void?>
    //     0x83494c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x834950: r0 = _Future()
    //     0x834950: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x834954: stur            x0, [fp, #-0x10]
    // 0x834958: StoreField: r0->field_b = rZR
    //     0x834958: stur            xzr, [x0, #0xb]
    // 0x83495c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x83495c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x834960: ldr             x0, [x0, #0x7a0]
    //     0x834964: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x834968: cmp             w0, w16
    //     0x83496c: b.ne            #0x834978
    //     0x834970: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x834974: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x834978: mov             x1, x0
    // 0x83497c: ldur            x0, [fp, #-0x10]
    // 0x834980: StoreField: r0->field_13 = r1
    //     0x834980: stur            w1, [x0, #0x13]
    // 0x834984: mov             x1, x0
    // 0x834988: r2 = Null
    //     0x834988: mov             x2, NULL
    // 0x83498c: r0 = _asyncComplete()
    //     0x83498c: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x834990: ldur            x1, [fp, #-0x10]
    // 0x834994: mov             x0, x1
    // 0x834998: stur            x1, [fp, #-0x10]
    // 0x83499c: r0 = Await()
    //     0x83499c: bl              #0x661044  ; AwaitStub
    // 0x8349a0: r0 = Null
    //     0x8349a0: mov             x0, NULL
    // 0x8349a4: r0 = ReturnAsyncNotFuture()
    //     0x8349a4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8349a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8349a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8349ac: b               #0x834898
    // 0x8349b0: r9 = keystore
    //     0x8349b0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8349b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8349b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initialize(/* No info */) {
    // ** addr: 0x834bb4, size: 0xa4
    // 0x834bb4: EnterFrame
    //     0x834bb4: stp             fp, lr, [SP, #-0x10]!
    //     0x834bb8: mov             fp, SP
    // 0x834bbc: CheckStackOverflow
    //     0x834bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x834bc0: cmp             SP, x16
    //     0x834bc4: b.ls            #0x834c44
    // 0x834bc8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x834bc8: ldur            w0, [x1, #0x17]
    // 0x834bcc: DecompressPointer r0
    //     0x834bcc: add             x0, x0, HEAP, lsl #32
    // 0x834bd0: LoadField: r2 = r1->field_f
    //     0x834bd0: ldur            w2, [x1, #0xf]
    // 0x834bd4: DecompressPointer r2
    //     0x834bd4: add             x2, x2, HEAP, lsl #32
    // 0x834bd8: LoadField: r3 = r1->field_1b
    //     0x834bd8: ldur            w3, [x1, #0x1b]
    // 0x834bdc: DecompressPointer r3
    //     0x834bdc: add             x3, x3, HEAP, lsl #32
    // 0x834be0: r16 = Sentinel
    //     0x834be0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x834be4: cmp             w3, w16
    // 0x834be8: b.eq            #0x834c4c
    // 0x834bec: r1 = LoadClassIdInstr(r0)
    //     0x834bec: ldur            x1, [x0, #-1]
    //     0x834bf0: ubfx            x1, x1, #0xc, #0x14
    // 0x834bf4: cmp             x1, #0x665
    // 0x834bf8: b.eq            #0x834c2c
    // 0x834bfc: r1 = LoadClassIdInstr(r0)
    //     0x834bfc: ldur            x1, [x0, #-1]
    //     0x834c00: ubfx            x1, x1, #0xc, #0x14
    // 0x834c04: mov             x16, x0
    // 0x834c08: mov             x0, x1
    // 0x834c0c: mov             x1, x16
    // 0x834c10: r5 = false
    //     0x834c10: add             x5, NULL, #0x30  ; false
    // 0x834c14: r0 = GDT[cid_x0 + -0xff7]()
    //     0x834c14: sub             lr, x0, #0xff7
    //     0x834c18: ldr             lr, [x21, lr, lsl #3]
    //     0x834c1c: blr             lr
    // 0x834c20: LeaveFrame
    //     0x834c20: mov             SP, fp
    //     0x834c24: ldp             fp, lr, [SP], #0x10
    // 0x834c28: ret
    //     0x834c28: ret             
    // 0x834c2c: r0 = Null
    //     0x834c2c: mov             x0, NULL
    // 0x834c30: cmp             w0, NULL
    // 0x834c34: b.eq            #0x834c54
    // 0x834c38: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x834c38: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x834c3c: r0 = Throw()
    //     0x834c3c: bl              #0xec04b8  ; ThrowStub
    // 0x834c40: brk             #0
    // 0x834c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x834c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x834c48: b               #0x834bc8
    // 0x834c4c: r9 = keystore
    //     0x834c4c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x834c50: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x834c50: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x834c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x834c54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ BoxBaseImpl(/* No info */) {
    // ** addr: 0x834c58, size: 0x134
    // 0x834c58: EnterFrame
    //     0x834c58: stp             fp, lr, [SP, #-0x10]!
    //     0x834c5c: mov             fp, SP
    // 0x834c60: AllocStack(0x20)
    //     0x834c60: sub             SP, SP, #0x20
    // 0x834c64: r6 = Sentinel
    //     0x834c64: ldr             x6, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x834c68: r0 = true
    //     0x834c68: add             x0, NULL, #0x20  ; true
    // 0x834c6c: r4 = Closure: (int, int) => bool from Function 'defaultCompactionStrategy': static.
    //     0x834c6c: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] Closure: (int, int) => bool from Function 'defaultCompactionStrategy': static. (0x7e54fb23538c)
    // 0x834c70: stur            x1, [fp, #-0x10]
    // 0x834c74: mov             x16, x5
    // 0x834c78: mov             x5, x1
    // 0x834c7c: mov             x1, x16
    // 0x834c80: mov             x16, x3
    // 0x834c84: mov             x3, x2
    // 0x834c88: mov             x2, x16
    // 0x834c8c: CheckStackOverflow
    //     0x834c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x834c90: cmp             SP, x16
    //     0x834c94: b.ls            #0x834d84
    // 0x834c98: StoreField: r5->field_1b = r6
    //     0x834c98: stur            w6, [x5, #0x1b]
    // 0x834c9c: StoreField: r5->field_1f = r0
    //     0x834c9c: stur            w0, [x5, #0x1f]
    // 0x834ca0: mov             x0, x3
    // 0x834ca4: StoreField: r5->field_f = r0
    //     0x834ca4: stur            w0, [x5, #0xf]
    //     0x834ca8: ldurb           w16, [x5, #-1]
    //     0x834cac: ldurb           w17, [x0, #-1]
    //     0x834cb0: and             x16, x17, x16, lsr #2
    //     0x834cb4: tst             x16, HEAP, lsr #32
    //     0x834cb8: b.eq            #0x834cc0
    //     0x834cbc: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x834cc0: mov             x0, x2
    // 0x834cc4: StoreField: r5->field_b = r0
    //     0x834cc4: stur            w0, [x5, #0xb]
    //     0x834cc8: ldurb           w16, [x5, #-1]
    //     0x834ccc: ldurb           w17, [x0, #-1]
    //     0x834cd0: and             x16, x17, x16, lsr #2
    //     0x834cd4: tst             x16, HEAP, lsr #32
    //     0x834cd8: b.eq            #0x834ce0
    //     0x834cdc: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x834ce0: StoreField: r5->field_13 = r4
    //     0x834ce0: stur            w4, [x5, #0x13]
    // 0x834ce4: mov             x0, x1
    // 0x834ce8: ArrayStore: r5[0] = r0  ; List_4
    //     0x834ce8: stur            w0, [x5, #0x17]
    //     0x834cec: ldurb           w16, [x5, #-1]
    //     0x834cf0: ldurb           w17, [x0, #-1]
    //     0x834cf4: and             x16, x17, x16, lsr #2
    //     0x834cf8: tst             x16, HEAP, lsr #32
    //     0x834cfc: b.eq            #0x834d04
    //     0x834d00: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x834d04: LoadField: r0 = r5->field_7
    //     0x834d04: ldur            w0, [x5, #7]
    // 0x834d08: DecompressPointer r0
    //     0x834d08: add             x0, x0, HEAP, lsl #32
    // 0x834d0c: stur            x0, [fp, #-8]
    // 0x834d10: r1 = <BoxEvent>
    //     0x834d10: ldr             x1, [PP, #0x7e38]  ; [pp+0x7e38] TypeArguments: <BoxEvent>
    // 0x834d14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x834d14: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x834d18: r0 = StreamController.broadcast()
    //     0x834d18: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0x834d1c: stur            x0, [fp, #-0x18]
    // 0x834d20: r0 = ChangeNotifier()
    //     0x834d20: bl              #0x835220  ; AllocateChangeNotifierStub -> ChangeNotifier (size=0xc)
    // 0x834d24: mov             x2, x0
    // 0x834d28: ldur            x0, [fp, #-0x18]
    // 0x834d2c: stur            x2, [fp, #-0x20]
    // 0x834d30: StoreField: r2->field_7 = r0
    //     0x834d30: stur            w0, [x2, #7]
    // 0x834d34: ldur            x1, [fp, #-8]
    // 0x834d38: r0 = Keystore()
    //     0x834d38: bl              #0x835214  ; AllocateKeystoreStub -> Keystore<X0> (size=0x2c)
    // 0x834d3c: mov             x1, x0
    // 0x834d40: ldur            x2, [fp, #-0x10]
    // 0x834d44: ldur            x3, [fp, #-0x20]
    // 0x834d48: stur            x0, [fp, #-8]
    // 0x834d4c: r0 = Keystore()
    //     0x834d4c: bl              #0x834d8c  ; [package:hive/src/box/keystore.dart] Keystore::Keystore
    // 0x834d50: ldur            x0, [fp, #-8]
    // 0x834d54: ldur            x1, [fp, #-0x10]
    // 0x834d58: StoreField: r1->field_1b = r0
    //     0x834d58: stur            w0, [x1, #0x1b]
    //     0x834d5c: ldurb           w16, [x1, #-1]
    //     0x834d60: ldurb           w17, [x0, #-1]
    //     0x834d64: and             x16, x17, x16, lsr #2
    //     0x834d68: tst             x16, HEAP, lsr #32
    //     0x834d6c: b.eq            #0x834d74
    //     0x834d70: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x834d74: r0 = Null
    //     0x834d74: mov             x0, NULL
    // 0x834d78: LeaveFrame
    //     0x834d78: mov             SP, fp
    //     0x834d7c: ldp             fp, lr, [SP], #0x10
    // 0x834d80: ret
    //     0x834d80: ret             
    // 0x834d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x834d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x834d88: b               #0x834c98
  }
  _ watch(/* No info */) {
    // ** addr: 0x836368, size: 0xa0
    // 0x836368: EnterFrame
    //     0x836368: stp             fp, lr, [SP, #-0x10]!
    //     0x83636c: mov             fp, SP
    // 0x836370: AllocStack(0x10)
    //     0x836370: sub             SP, SP, #0x10
    // 0x836374: SetupParameters(BoxBaseImpl<X0> this /* r1 => r0, fp-0x10 */, {dynamic key = Null /* r2, fp-0x8 */})
    //     0x836374: mov             x0, x1
    //     0x836378: stur            x1, [fp, #-0x10]
    //     0x83637c: ldur            w1, [x4, #0x13]
    //     0x836380: ldur            w2, [x4, #0x1f]
    //     0x836384: add             x2, x2, HEAP, lsl #32
    //     0x836388: ldr             x16, [PP, #0xab8]  ; [pp+0xab8] "key"
    //     0x83638c: cmp             w2, w16
    //     0x836390: b.ne            #0x8363b0
    //     0x836394: ldur            w2, [x4, #0x23]
    //     0x836398: add             x2, x2, HEAP, lsl #32
    //     0x83639c: sub             w3, w1, w2
    //     0x8363a0: add             x1, fp, w3, sxtw #2
    //     0x8363a4: ldr             x1, [x1, #8]
    //     0x8363a8: mov             x2, x1
    //     0x8363ac: b               #0x8363b4
    //     0x8363b0: mov             x2, NULL
    //     0x8363b4: stur            x2, [fp, #-8]
    // 0x8363b8: CheckStackOverflow
    //     0x8363b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8363bc: cmp             SP, x16
    //     0x8363c0: b.ls            #0x8363f8
    // 0x8363c4: mov             x1, x0
    // 0x8363c8: r0 = checkOpen()
    //     0x8363c8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8363cc: ldur            x0, [fp, #-0x10]
    // 0x8363d0: LoadField: r1 = r0->field_1b
    //     0x8363d0: ldur            w1, [x0, #0x1b]
    // 0x8363d4: DecompressPointer r1
    //     0x8363d4: add             x1, x1, HEAP, lsl #32
    // 0x8363d8: r16 = Sentinel
    //     0x8363d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8363dc: cmp             w1, w16
    // 0x8363e0: b.eq            #0x836400
    // 0x8363e4: ldur            x2, [fp, #-8]
    // 0x8363e8: r0 = watch()
    //     0x8363e8: bl              #0x836408  ; [package:hive/src/box/keystore.dart] Keystore::watch
    // 0x8363ec: LeaveFrame
    //     0x8363ec: mov             SP, fp
    //     0x8363f0: ldp             fp, lr, [SP], #0x10
    // 0x8363f4: ret
    //     0x8363f4: ret             
    // 0x8363f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8363f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8363fc: b               #0x8363c4
    // 0x836400: r9 = keystore
    //     0x836400: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x836404: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x836404: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ isNotEmpty(/* No info */) {
    // ** addr: 0x8c0a88, size: 0x78
    // 0x8c0a88: EnterFrame
    //     0x8c0a88: stp             fp, lr, [SP, #-0x10]!
    //     0x8c0a8c: mov             fp, SP
    // 0x8c0a90: AllocStack(0x8)
    //     0x8c0a90: sub             SP, SP, #8
    // 0x8c0a94: SetupParameters(BoxBaseImpl<X0> this /* r1 => r0, fp-0x8 */)
    //     0x8c0a94: mov             x0, x1
    //     0x8c0a98: stur            x1, [fp, #-8]
    // 0x8c0a9c: CheckStackOverflow
    //     0x8c0a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0aa0: cmp             SP, x16
    //     0x8c0aa4: b.ls            #0x8c0af0
    // 0x8c0aa8: mov             x1, x0
    // 0x8c0aac: r0 = checkOpen()
    //     0x8c0aac: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c0ab0: ldur            x1, [fp, #-8]
    // 0x8c0ab4: LoadField: r2 = r1->field_1b
    //     0x8c0ab4: ldur            w2, [x1, #0x1b]
    // 0x8c0ab8: DecompressPointer r2
    //     0x8c0ab8: add             x2, x2, HEAP, lsl #32
    // 0x8c0abc: r16 = Sentinel
    //     0x8c0abc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c0ac0: cmp             w2, w16
    // 0x8c0ac4: b.eq            #0x8c0af8
    // 0x8c0ac8: LoadField: r1 = r2->field_13
    //     0x8c0ac8: ldur            w1, [x2, #0x13]
    // 0x8c0acc: DecompressPointer r1
    //     0x8c0acc: add             x1, x1, HEAP, lsl #32
    // 0x8c0ad0: LoadField: r2 = r1->field_1f
    //     0x8c0ad0: ldur            x2, [x1, #0x1f]
    // 0x8c0ad4: cmp             x2, #0
    // 0x8c0ad8: r16 = true
    //     0x8c0ad8: add             x16, NULL, #0x20  ; true
    // 0x8c0adc: r17 = false
    //     0x8c0adc: add             x17, NULL, #0x30  ; false
    // 0x8c0ae0: csel            x0, x16, x17, gt
    // 0x8c0ae4: LeaveFrame
    //     0x8c0ae4: mov             SP, fp
    //     0x8c0ae8: ldp             fp, lr, [SP], #0x10
    // 0x8c0aec: ret
    //     0x8c0aec: ret             
    // 0x8c0af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0af0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0af4: b               #0x8c0aa8
    // 0x8c0af8: r9 = keystore
    //     0x8c0af8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c0afc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c0afc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ addAll(/* No info */) async {
    // ** addr: 0x8c0b60, size: 0x250
    // 0x8c0b60: EnterFrame
    //     0x8c0b60: stp             fp, lr, [SP, #-0x10]!
    //     0x8c0b64: mov             fp, SP
    // 0x8c0b68: AllocStack(0x48)
    //     0x8c0b68: sub             SP, SP, #0x48
    // 0x8c0b6c: SetupParameters(BoxBaseImpl<X0> this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x8c0b6c: stur            NULL, [fp, #-8]
    //     0x8c0b70: mov             x4, x1
    //     0x8c0b74: mov             x3, x2
    //     0x8c0b78: stur            x1, [fp, #-0x18]
    //     0x8c0b7c: stur            x2, [fp, #-0x20]
    // 0x8c0b80: CheckStackOverflow
    //     0x8c0b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0b84: cmp             SP, x16
    //     0x8c0b88: b.ls            #0x8c0d98
    // 0x8c0b8c: LoadField: r5 = r4->field_7
    //     0x8c0b8c: ldur            w5, [x4, #7]
    // 0x8c0b90: DecompressPointer r5
    //     0x8c0b90: add             x5, x5, HEAP, lsl #32
    // 0x8c0b94: mov             x0, x3
    // 0x8c0b98: mov             x2, x5
    // 0x8c0b9c: stur            x5, [fp, #-0x10]
    // 0x8c0ba0: r1 = Null
    //     0x8c0ba0: mov             x1, NULL
    // 0x8c0ba4: r8 = Iterable<X0>
    //     0x8c0ba4: ldr             x8, [PP, #0x6e0]  ; [pp+0x6e0] Type: Iterable<X0>
    // 0x8c0ba8: LoadField: r9 = r8->field_7
    //     0x8c0ba8: ldur            x9, [x8, #7]
    // 0x8c0bac: r3 = Null
    //     0x8c0bac: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dae8] Null
    //     0x8c0bb0: ldr             x3, [x3, #0xae8]
    // 0x8c0bb4: blr             x9
    // 0x8c0bb8: InitAsync() -> Future<Iterable<int>>
    //     0x8c0bb8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2daf8] TypeArguments: <Iterable<int>>
    //     0x8c0bbc: ldr             x0, [x0, #0xaf8]
    //     0x8c0bc0: bl              #0x661298  ; InitAsyncStub
    // 0x8c0bc4: ldur            x2, [fp, #-0x10]
    // 0x8c0bc8: r1 = Null
    //     0x8c0bc8: mov             x1, NULL
    // 0x8c0bcc: r3 = <int, X0>
    //     0x8c0bcc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db00] TypeArguments: <int, X0>
    //     0x8c0bd0: ldr             x3, [x3, #0xb00]
    // 0x8c0bd4: r30 = InstantiateTypeArgumentsStub
    //     0x8c0bd4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8c0bd8: LoadField: r30 = r30->field_7
    //     0x8c0bd8: ldur            lr, [lr, #7]
    // 0x8c0bdc: blr             lr
    // 0x8c0be0: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0x8c0be4: stp             x16, x0, [SP]
    // 0x8c0be8: r0 = Map._fromLiteral()
    //     0x8c0be8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8c0bec: mov             x2, x0
    // 0x8c0bf0: ldur            x1, [fp, #-0x20]
    // 0x8c0bf4: stur            x2, [fp, #-0x10]
    // 0x8c0bf8: r0 = LoadClassIdInstr(r1)
    //     0x8c0bf8: ldur            x0, [x1, #-1]
    //     0x8c0bfc: ubfx            x0, x0, #0xc, #0x14
    // 0x8c0c00: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x8c0c00: movz            x17, #0xd35d
    //     0x8c0c04: add             lr, x0, x17
    //     0x8c0c08: ldr             lr, [x21, lr, lsl #3]
    //     0x8c0c0c: blr             lr
    // 0x8c0c10: mov             x3, x0
    // 0x8c0c14: ldur            x2, [fp, #-0x10]
    // 0x8c0c18: stur            x3, [fp, #-0x28]
    // 0x8c0c1c: LoadField: r4 = r2->field_7
    //     0x8c0c1c: ldur            w4, [x2, #7]
    // 0x8c0c20: DecompressPointer r4
    //     0x8c0c20: add             x4, x4, HEAP, lsl #32
    // 0x8c0c24: stur            x4, [fp, #-0x20]
    // 0x8c0c28: ldur            x5, [fp, #-0x18]
    // 0x8c0c2c: CheckStackOverflow
    //     0x8c0c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0c30: cmp             SP, x16
    //     0x8c0c34: b.ls            #0x8c0da0
    // 0x8c0c38: r0 = LoadClassIdInstr(r3)
    //     0x8c0c38: ldur            x0, [x3, #-1]
    //     0x8c0c3c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c0c40: mov             x1, x3
    // 0x8c0c44: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x8c0c44: movz            x17, #0x292d
    //     0x8c0c48: movk            x17, #0x1, lsl #16
    //     0x8c0c4c: add             lr, x0, x17
    //     0x8c0c50: ldr             lr, [x21, lr, lsl #3]
    //     0x8c0c54: blr             lr
    // 0x8c0c58: tbnz            w0, #4, #0x8c0d58
    // 0x8c0c5c: ldur            x3, [fp, #-0x18]
    // 0x8c0c60: ldur            x2, [fp, #-0x28]
    // 0x8c0c64: r0 = LoadClassIdInstr(r2)
    //     0x8c0c64: ldur            x0, [x2, #-1]
    //     0x8c0c68: ubfx            x0, x0, #0xc, #0x14
    // 0x8c0c6c: mov             x1, x2
    // 0x8c0c70: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x8c0c70: movz            x17, #0x384d
    //     0x8c0c74: movk            x17, #0x1, lsl #16
    //     0x8c0c78: add             lr, x0, x17
    //     0x8c0c7c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c0c80: blr             lr
    // 0x8c0c84: mov             x4, x0
    // 0x8c0c88: ldur            x3, [fp, #-0x18]
    // 0x8c0c8c: stur            x4, [fp, #-0x38]
    // 0x8c0c90: LoadField: r0 = r3->field_1b
    //     0x8c0c90: ldur            w0, [x3, #0x1b]
    // 0x8c0c94: DecompressPointer r0
    //     0x8c0c94: add             x0, x0, HEAP, lsl #32
    // 0x8c0c98: r16 = Sentinel
    //     0x8c0c98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c0c9c: cmp             w0, w16
    // 0x8c0ca0: b.eq            #0x8c0da8
    // 0x8c0ca4: LoadField: r1 = r0->field_23
    //     0x8c0ca4: ldur            x1, [x0, #0x23]
    // 0x8c0ca8: add             x2, x1, #1
    // 0x8c0cac: StoreField: r0->field_23 = r2
    //     0x8c0cac: stur            x2, [x0, #0x23]
    // 0x8c0cb0: r0 = BoxInt64Instr(r2)
    //     0x8c0cb0: sbfiz           x0, x2, #1, #0x1f
    //     0x8c0cb4: cmp             x2, x0, asr #1
    //     0x8c0cb8: b.eq            #0x8c0cc4
    //     0x8c0cbc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c0cc0: stur            x2, [x0, #7]
    // 0x8c0cc4: ldur            x2, [fp, #-0x20]
    // 0x8c0cc8: mov             x5, x0
    // 0x8c0ccc: r1 = Null
    //     0x8c0ccc: mov             x1, NULL
    // 0x8c0cd0: stur            x5, [fp, #-0x30]
    // 0x8c0cd4: cmp             w2, NULL
    // 0x8c0cd8: b.eq            #0x8c0cf8
    // 0x8c0cdc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8c0cdc: ldur            w4, [x2, #0x17]
    // 0x8c0ce0: DecompressPointer r4
    //     0x8c0ce0: add             x4, x4, HEAP, lsl #32
    // 0x8c0ce4: r8 = X0
    //     0x8c0ce4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8c0ce8: LoadField: r9 = r4->field_7
    //     0x8c0ce8: ldur            x9, [x4, #7]
    // 0x8c0cec: r3 = Null
    //     0x8c0cec: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db08] Null
    //     0x8c0cf0: ldr             x3, [x3, #0xb08]
    // 0x8c0cf4: blr             x9
    // 0x8c0cf8: ldur            x0, [fp, #-0x38]
    // 0x8c0cfc: ldur            x2, [fp, #-0x20]
    // 0x8c0d00: r1 = Null
    //     0x8c0d00: mov             x1, NULL
    // 0x8c0d04: cmp             w2, NULL
    // 0x8c0d08: b.eq            #0x8c0d28
    // 0x8c0d0c: LoadField: r4 = r2->field_1b
    //     0x8c0d0c: ldur            w4, [x2, #0x1b]
    // 0x8c0d10: DecompressPointer r4
    //     0x8c0d10: add             x4, x4, HEAP, lsl #32
    // 0x8c0d14: r8 = X1
    //     0x8c0d14: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x8c0d18: LoadField: r9 = r4->field_7
    //     0x8c0d18: ldur            x9, [x4, #7]
    // 0x8c0d1c: r3 = Null
    //     0x8c0d1c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db18] Null
    //     0x8c0d20: ldr             x3, [x3, #0xb18]
    // 0x8c0d24: blr             x9
    // 0x8c0d28: ldur            x1, [fp, #-0x10]
    // 0x8c0d2c: ldur            x2, [fp, #-0x30]
    // 0x8c0d30: r0 = _hashCode()
    //     0x8c0d30: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x8c0d34: ldur            x1, [fp, #-0x10]
    // 0x8c0d38: ldur            x2, [fp, #-0x30]
    // 0x8c0d3c: ldur            x3, [fp, #-0x38]
    // 0x8c0d40: mov             x5, x0
    // 0x8c0d44: r0 = _set()
    //     0x8c0d44: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x8c0d48: ldur            x2, [fp, #-0x10]
    // 0x8c0d4c: ldur            x3, [fp, #-0x28]
    // 0x8c0d50: ldur            x4, [fp, #-0x20]
    // 0x8c0d54: b               #0x8c0c28
    // 0x8c0d58: ldur            x0, [fp, #-0x10]
    // 0x8c0d5c: ldur            x1, [fp, #-0x18]
    // 0x8c0d60: mov             x2, x0
    // 0x8c0d64: r0 = putAll()
    //     0x8c0d64: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x8c0d68: mov             x1, x0
    // 0x8c0d6c: stur            x1, [fp, #-0x18]
    // 0x8c0d70: r0 = Await()
    //     0x8c0d70: bl              #0x661044  ; AwaitStub
    // 0x8c0d74: ldur            x1, [fp, #-0x20]
    // 0x8c0d78: r0 = _CompactIterable()
    //     0x8c0d78: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x8c0d7c: ldur            x1, [fp, #-0x10]
    // 0x8c0d80: StoreField: r0->field_b = r1
    //     0x8c0d80: stur            w1, [x0, #0xb]
    // 0x8c0d84: r1 = -2
    //     0x8c0d84: orr             x1, xzr, #0xfffffffffffffffe
    // 0x8c0d88: StoreField: r0->field_f = r1
    //     0x8c0d88: stur            x1, [x0, #0xf]
    // 0x8c0d8c: r1 = 2
    //     0x8c0d8c: movz            x1, #0x2
    // 0x8c0d90: ArrayStore: r0[0] = r1  ; List_8
    //     0x8c0d90: stur            x1, [x0, #0x17]
    // 0x8c0d94: r0 = ReturnAsyncNotFuture()
    //     0x8c0d94: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c0d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0d98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0d9c: b               #0x8c0b8c
    // 0x8c0da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0da4: b               #0x8c0c38
    // 0x8c0da8: r9 = keystore
    //     0x8c0da8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c0dac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c0dac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ clear(/* No info */) async {
    // ** addr: 0x8c0db0, size: 0x140
    // 0x8c0db0: EnterFrame
    //     0x8c0db0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c0db4: mov             fp, SP
    // 0x8c0db8: AllocStack(0x38)
    //     0x8c0db8: sub             SP, SP, #0x38
    // 0x8c0dbc: SetupParameters(BoxBaseImpl<X0> this /* r1 => r1, fp-0x10 */)
    //     0x8c0dbc: stur            NULL, [fp, #-8]
    //     0x8c0dc0: stur            x1, [fp, #-0x10]
    // 0x8c0dc4: CheckStackOverflow
    //     0x8c0dc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c0dc8: cmp             SP, x16
    //     0x8c0dcc: b.ls            #0x8c0ee0
    // 0x8c0dd0: InitAsync() -> Future<int>
    //     0x8c0dd0: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0x8c0dd4: bl              #0x661298  ; InitAsyncStub
    // 0x8c0dd8: ldur            x1, [fp, #-0x10]
    // 0x8c0ddc: r0 = checkOpen()
    //     0x8c0ddc: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c0de0: ldur            x0, [fp, #-0x10]
    // 0x8c0de4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8c0de4: ldur            w1, [x0, #0x17]
    // 0x8c0de8: DecompressPointer r1
    //     0x8c0de8: add             x1, x1, HEAP, lsl #32
    // 0x8c0dec: stur            x1, [fp, #-0x18]
    // 0x8c0df0: r2 = LoadClassIdInstr(r1)
    //     0x8c0df0: ldur            x2, [x1, #-1]
    //     0x8c0df4: ubfx            x2, x2, #0xc, #0x14
    // 0x8c0df8: cmp             x2, #0x664
    // 0x8c0dfc: b.ne            #0x8c0e50
    // 0x8c0e00: r1 = 1
    //     0x8c0e00: movz            x1, #0x1
    // 0x8c0e04: r0 = AllocateContext()
    //     0x8c0e04: bl              #0xec126c  ; AllocateContextStub
    // 0x8c0e08: mov             x1, x0
    // 0x8c0e0c: ldur            x0, [fp, #-0x18]
    // 0x8c0e10: StoreField: r1->field_f = r0
    //     0x8c0e10: stur            w0, [x1, #0xf]
    // 0x8c0e14: LoadField: r3 = r0->field_1b
    //     0x8c0e14: ldur            w3, [x0, #0x1b]
    // 0x8c0e18: DecompressPointer r3
    //     0x8c0e18: add             x3, x3, HEAP, lsl #32
    // 0x8c0e1c: mov             x2, x1
    // 0x8c0e20: stur            x3, [fp, #-0x20]
    // 0x8c0e24: r1 = Function '<anonymous closure>':.
    //     0x8c0e24: add             x1, PP, #0x17, lsl #12  ; [pp+0x17cf8] AnonymousClosure: (0x8c11d4), of [package:hive/src/backend/vm/storage_backend_vm.dart] StorageBackendVm
    //     0x8c0e28: ldr             x1, [x1, #0xcf8]
    // 0x8c0e2c: r0 = AllocateClosure()
    //     0x8c0e2c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c0e30: r16 = <void?>
    //     0x8c0e30: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8c0e34: ldur            lr, [fp, #-0x20]
    // 0x8c0e38: stp             lr, x16, [SP, #8]
    // 0x8c0e3c: str             x0, [SP]
    // 0x8c0e40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c0e40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c0e44: r0 = syncReadWrite()
    //     0x8c0e44: bl              #0x7bf22c  ; [package:hive/src/backend/vm/read_write_sync.dart] ReadWriteSync::syncReadWrite
    // 0x8c0e48: mov             x2, x0
    // 0x8c0e4c: b               #0x8c0e98
    // 0x8c0e50: r1 = <void?>
    //     0x8c0e50: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8c0e54: r0 = _Future()
    //     0x8c0e54: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8c0e58: stur            x0, [fp, #-0x18]
    // 0x8c0e5c: StoreField: r0->field_b = rZR
    //     0x8c0e5c: stur            xzr, [x0, #0xb]
    // 0x8c0e60: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x8c0e60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c0e64: ldr             x0, [x0, #0x7a0]
    //     0x8c0e68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c0e6c: cmp             w0, w16
    //     0x8c0e70: b.ne            #0x8c0e7c
    //     0x8c0e74: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x8c0e78: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8c0e7c: mov             x1, x0
    // 0x8c0e80: ldur            x0, [fp, #-0x18]
    // 0x8c0e84: StoreField: r0->field_13 = r1
    //     0x8c0e84: stur            w1, [x0, #0x13]
    // 0x8c0e88: mov             x1, x0
    // 0x8c0e8c: r2 = Null
    //     0x8c0e8c: mov             x2, NULL
    // 0x8c0e90: r0 = _asyncComplete()
    //     0x8c0e90: bl              #0x5f9868  ; [dart:async] _Future::_asyncComplete
    // 0x8c0e94: ldur            x2, [fp, #-0x18]
    // 0x8c0e98: ldur            x1, [fp, #-0x10]
    // 0x8c0e9c: mov             x0, x2
    // 0x8c0ea0: stur            x2, [fp, #-0x18]
    // 0x8c0ea4: r0 = Await()
    //     0x8c0ea4: bl              #0x661044  ; AwaitStub
    // 0x8c0ea8: ldur            x0, [fp, #-0x10]
    // 0x8c0eac: LoadField: r1 = r0->field_1b
    //     0x8c0eac: ldur            w1, [x0, #0x1b]
    // 0x8c0eb0: DecompressPointer r1
    //     0x8c0eb0: add             x1, x1, HEAP, lsl #32
    // 0x8c0eb4: r16 = Sentinel
    //     0x8c0eb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c0eb8: cmp             w1, w16
    // 0x8c0ebc: b.eq            #0x8c0ee8
    // 0x8c0ec0: r0 = clear()
    //     0x8c0ec0: bl              #0x8c0ef0  ; [package:hive/src/box/keystore.dart] Keystore::clear
    // 0x8c0ec4: mov             x2, x0
    // 0x8c0ec8: r0 = BoxInt64Instr(r2)
    //     0x8c0ec8: sbfiz           x0, x2, #1, #0x1f
    //     0x8c0ecc: cmp             x2, x0, asr #1
    //     0x8c0ed0: b.eq            #0x8c0edc
    //     0x8c0ed4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8c0ed8: stur            x2, [x0, #7]
    // 0x8c0edc: r0 = ReturnAsyncNotFuture()
    //     0x8c0edc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c0ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0ee0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0ee4: b               #0x8c0dd0
    // 0x8c0ee8: r9 = keystore
    //     0x8c0ee8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c0eec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c0eec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ delete(/* No info */) {
    // ** addr: 0xa424f8, size: 0x94
    // 0xa424f8: EnterFrame
    //     0xa424f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa424fc: mov             fp, SP
    // 0xa42500: AllocStack(0x18)
    //     0xa42500: sub             SP, SP, #0x18
    // 0xa42504: r3 = 2
    //     0xa42504: movz            x3, #0x2
    // 0xa42508: mov             x4, x1
    // 0xa4250c: stur            x1, [fp, #-0x10]
    // 0xa42510: CheckStackOverflow
    //     0xa42510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42514: cmp             SP, x16
    //     0xa42518: b.ls            #0xa42584
    // 0xa4251c: r0 = BoxInt64Instr(r2)
    //     0xa4251c: sbfiz           x0, x2, #1, #0x1f
    //     0xa42520: cmp             x2, x0, asr #1
    //     0xa42524: b.eq            #0xa42530
    //     0xa42528: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4252c: stur            x2, [x0, #7]
    // 0xa42530: mov             x2, x3
    // 0xa42534: r1 = Null
    //     0xa42534: mov             x1, NULL
    // 0xa42538: stur            x0, [fp, #-8]
    // 0xa4253c: r0 = AllocateArray()
    //     0xa4253c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa42540: mov             x2, x0
    // 0xa42544: ldur            x0, [fp, #-8]
    // 0xa42548: stur            x2, [fp, #-0x18]
    // 0xa4254c: StoreField: r2->field_f = r0
    //     0xa4254c: stur            w0, [x2, #0xf]
    // 0xa42550: r1 = Null
    //     0xa42550: mov             x1, NULL
    // 0xa42554: r0 = AllocateGrowableArray()
    //     0xa42554: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa42558: mov             x1, x0
    // 0xa4255c: ldur            x0, [fp, #-0x18]
    // 0xa42560: StoreField: r1->field_f = r0
    //     0xa42560: stur            w0, [x1, #0xf]
    // 0xa42564: r0 = 2
    //     0xa42564: movz            x0, #0x2
    // 0xa42568: StoreField: r1->field_b = r0
    //     0xa42568: stur            w0, [x1, #0xb]
    // 0xa4256c: mov             x2, x1
    // 0xa42570: ldur            x1, [fp, #-0x10]
    // 0xa42574: r0 = deleteAll()
    //     0xa42574: bl              #0xa4258c  ; [package:hive/src/box/box_impl.dart] BoxImpl::deleteAll
    // 0xa42578: LeaveFrame
    //     0xa42578: mov             SP, fp
    //     0xa4257c: ldp             fp, lr, [SP], #0x10
    // 0xa42580: ret
    //     0xa42580: ret             
    // 0xa42584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42588: b               #0xa4251c
  }
}
