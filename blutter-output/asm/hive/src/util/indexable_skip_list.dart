// lib: , url: package:hive/src/util/indexable_skip_list.dart

// class id: 1049623, size: 0x8
class :: {
}

// class id: 1591, size: 0x10, field offset: 0x8
abstract class _Iterator<X0, X1, X2> extends Object
    implements Iterator<X0> {

  _ moveNext(/* No info */) {
    // ** addr: 0x6fb01c, size: 0x84
    // 0x6fb01c: EnterFrame
    //     0x6fb01c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb020: mov             fp, SP
    // 0x6fb024: mov             x2, x1
    // 0x6fb028: LoadField: r3 = r2->field_b
    //     0x6fb028: ldur            w3, [x2, #0xb]
    // 0x6fb02c: DecompressPointer r3
    //     0x6fb02c: add             x3, x3, HEAP, lsl #32
    // 0x6fb030: cmp             w3, NULL
    // 0x6fb034: b.eq            #0x6fb098
    // 0x6fb038: LoadField: r4 = r3->field_13
    //     0x6fb038: ldur            w4, [x3, #0x13]
    // 0x6fb03c: DecompressPointer r4
    //     0x6fb03c: add             x4, x4, HEAP, lsl #32
    // 0x6fb040: LoadField: r3 = r4->field_b
    //     0x6fb040: ldur            w3, [x4, #0xb]
    // 0x6fb044: r0 = LoadInt32Instr(r3)
    //     0x6fb044: sbfx            x0, x3, #1, #0x1f
    // 0x6fb048: r1 = 0
    //     0x6fb048: movz            x1, #0
    // 0x6fb04c: cmp             x1, x0
    // 0x6fb050: b.hs            #0x6fb09c
    // 0x6fb054: LoadField: r1 = r4->field_f
    //     0x6fb054: ldur            w1, [x4, #0xf]
    // 0x6fb058: DecompressPointer r1
    //     0x6fb058: add             x1, x1, HEAP, lsl #32
    // 0x6fb05c: mov             x0, x1
    // 0x6fb060: StoreField: r2->field_b = r0
    //     0x6fb060: stur            w0, [x2, #0xb]
    //     0x6fb064: ldurb           w16, [x2, #-1]
    //     0x6fb068: ldurb           w17, [x0, #-1]
    //     0x6fb06c: and             x16, x17, x16, lsr #2
    //     0x6fb070: tst             x16, HEAP, lsr #32
    //     0x6fb074: b.eq            #0x6fb07c
    //     0x6fb078: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6fb07c: cmp             w1, NULL
    // 0x6fb080: r16 = true
    //     0x6fb080: add             x16, NULL, #0x20  ; true
    // 0x6fb084: r17 = false
    //     0x6fb084: add             x17, NULL, #0x30  ; false
    // 0x6fb088: csel            x0, x16, x17, ne
    // 0x6fb08c: LeaveFrame
    //     0x6fb08c: mov             SP, fp
    //     0x6fb090: ldp             fp, lr, [SP], #0x10
    // 0x6fb094: ret
    //     0x6fb094: ret             
    // 0x6fb098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6fb098: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6fb09c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fb09c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 1592, size: 0x10, field offset: 0x10
class _ValueIterator<C3X0, C3X1> extends _Iterator<C3X0, C3X1, dynamic> {

  get _ current(/* No info */) {
    // ** addr: 0x68e23c, size: 0x3c
    // 0x68e23c: EnterFrame
    //     0x68e23c: stp             fp, lr, [SP, #-0x10]!
    //     0x68e240: mov             fp, SP
    // 0x68e244: LoadField: r2 = r1->field_b
    //     0x68e244: ldur            w2, [x1, #0xb]
    // 0x68e248: DecompressPointer r2
    //     0x68e248: add             x2, x2, HEAP, lsl #32
    // 0x68e24c: cmp             w2, NULL
    // 0x68e250: b.eq            #0x68e270
    // 0x68e254: LoadField: r0 = r2->field_f
    //     0x68e254: ldur            w0, [x2, #0xf]
    // 0x68e258: DecompressPointer r0
    //     0x68e258: add             x0, x0, HEAP, lsl #32
    // 0x68e25c: cmp             w0, NULL
    // 0x68e260: b.eq            #0x68e274
    // 0x68e264: LeaveFrame
    //     0x68e264: mov             SP, fp
    //     0x68e268: ldp             fp, lr, [SP], #0x10
    // 0x68e26c: ret
    //     0x68e26c: ret             
    // 0x68e270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68e270: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68e274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68e274: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 1593, size: 0x1c, field offset: 0x8
class _Node<X0, X1> extends Object {
}

// class id: 1594, size: 0x28, field offset: 0x8
class IndexableSkipList<X0, X1> extends Object {

  _ _getNode(/* No info */) {
    // ** addr: 0x68f4f8, size: 0x1ac
    // 0x68f4f8: EnterFrame
    //     0x68f4f8: stp             fp, lr, [SP, #-0x10]!
    //     0x68f4fc: mov             fp, SP
    // 0x68f500: AllocStack(0x28)
    //     0x68f500: sub             SP, SP, #0x28
    // 0x68f504: SetupParameters(dynamic _ /* r2 => r3, fp-0x20 */)
    //     0x68f504: mov             x3, x2
    //     0x68f508: stur            x2, [fp, #-0x20]
    // 0x68f50c: CheckStackOverflow
    //     0x68f50c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f510: cmp             SP, x16
    //     0x68f514: b.ls            #0x68f67c
    // 0x68f518: LoadField: r0 = r1->field_b
    //     0x68f518: ldur            w0, [x1, #0xb]
    // 0x68f51c: DecompressPointer r0
    //     0x68f51c: add             x0, x0, HEAP, lsl #32
    // 0x68f520: ArrayLoad: r2 = r1[0]  ; List_8
    //     0x68f520: ldur            x2, [x1, #0x17]
    // 0x68f524: sub             x1, x2, #1
    // 0x68f528: mov             x2, x0
    // 0x68f52c: mov             x4, x1
    // 0x68f530: r0 = Null
    //     0x68f530: mov             x0, NULL
    // 0x68f534: stur            x4, [fp, #-0x18]
    // 0x68f538: stur            x0, [fp, #-0x28]
    // 0x68f53c: CheckStackOverflow
    //     0x68f53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f540: cmp             SP, x16
    //     0x68f544: b.ls            #0x68f684
    // 0x68f548: tbnz            x4, #0x3f, #0x68f620
    // 0x68f54c: LoadField: r5 = r2->field_13
    //     0x68f54c: ldur            w5, [x2, #0x13]
    // 0x68f550: DecompressPointer r5
    //     0x68f550: add             x5, x5, HEAP, lsl #32
    // 0x68f554: LoadField: r0 = r5->field_b
    //     0x68f554: ldur            w0, [x5, #0xb]
    // 0x68f558: r1 = LoadInt32Instr(r0)
    //     0x68f558: sbfx            x1, x0, #1, #0x1f
    // 0x68f55c: mov             x0, x1
    // 0x68f560: mov             x1, x4
    // 0x68f564: cmp             x1, x0
    // 0x68f568: b.hs            #0x68f68c
    // 0x68f56c: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0x68f56c: add             x16, x5, x4, lsl #2
    //     0x68f570: ldur            w0, [x16, #0xf]
    // 0x68f574: DecompressPointer r0
    //     0x68f574: add             x0, x0, HEAP, lsl #32
    // 0x68f578: mov             x5, x2
    // 0x68f57c: stur            x5, [fp, #-8]
    // 0x68f580: stur            x0, [fp, #-0x10]
    // 0x68f584: CheckStackOverflow
    //     0x68f584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f588: cmp             SP, x16
    //     0x68f58c: b.ls            #0x68f690
    // 0x68f590: cmp             w0, NULL
    // 0x68f594: b.eq            #0x68f604
    // 0x68f598: LoadField: r2 = r0->field_b
    //     0x68f598: ldur            w2, [x0, #0xb]
    // 0x68f59c: DecompressPointer r2
    //     0x68f59c: add             x2, x2, HEAP, lsl #32
    // 0x68f5a0: cmp             w2, NULL
    // 0x68f5a4: b.eq            #0x68f698
    // 0x68f5a8: mov             x1, x3
    // 0x68f5ac: r0 = defaultKeyComparator()
    //     0x68f5ac: bl              #0x68f6f0  ; [package:hive/src/box/default_key_comparator.dart] ::defaultKeyComparator
    // 0x68f5b0: cmp             x0, #0
    // 0x68f5b4: b.le            #0x68f5f8
    // 0x68f5b8: ldur            x2, [fp, #-0x18]
    // 0x68f5bc: ldur            x5, [fp, #-0x10]
    // 0x68f5c0: LoadField: r3 = r5->field_13
    //     0x68f5c0: ldur            w3, [x5, #0x13]
    // 0x68f5c4: DecompressPointer r3
    //     0x68f5c4: add             x3, x3, HEAP, lsl #32
    // 0x68f5c8: LoadField: r0 = r3->field_b
    //     0x68f5c8: ldur            w0, [x3, #0xb]
    // 0x68f5cc: r1 = LoadInt32Instr(r0)
    //     0x68f5cc: sbfx            x1, x0, #1, #0x1f
    // 0x68f5d0: mov             x0, x1
    // 0x68f5d4: mov             x1, x2
    // 0x68f5d8: cmp             x1, x0
    // 0x68f5dc: b.hs            #0x68f69c
    // 0x68f5e0: ArrayLoad: r0 = r3[r2]  ; Unknown_4
    //     0x68f5e0: add             x16, x3, x2, lsl #2
    //     0x68f5e4: ldur            w0, [x16, #0xf]
    // 0x68f5e8: DecompressPointer r0
    //     0x68f5e8: add             x0, x0, HEAP, lsl #32
    // 0x68f5ec: ldur            x3, [fp, #-0x20]
    // 0x68f5f0: mov             x4, x2
    // 0x68f5f4: b               #0x68f57c
    // 0x68f5f8: ldur            x2, [fp, #-0x18]
    // 0x68f5fc: ldur            x5, [fp, #-0x10]
    // 0x68f600: b               #0x68f60c
    // 0x68f604: mov             x2, x4
    // 0x68f608: mov             x5, x0
    // 0x68f60c: sub             x4, x2, #1
    // 0x68f610: ldur            x2, [fp, #-8]
    // 0x68f614: mov             x0, x5
    // 0x68f618: ldur            x3, [fp, #-0x20]
    // 0x68f61c: b               #0x68f534
    // 0x68f620: cmp             w0, NULL
    // 0x68f624: b.eq            #0x68f66c
    // 0x68f628: LoadField: r2 = r0->field_b
    //     0x68f628: ldur            w2, [x0, #0xb]
    // 0x68f62c: DecompressPointer r2
    //     0x68f62c: add             x2, x2, HEAP, lsl #32
    // 0x68f630: cmp             w2, NULL
    // 0x68f634: b.eq            #0x68f6a0
    // 0x68f638: ldur            x1, [fp, #-0x20]
    // 0x68f63c: r0 = defaultKeyComparator()
    //     0x68f63c: bl              #0x68f6f0  ; [package:hive/src/box/default_key_comparator.dart] ::defaultKeyComparator
    // 0x68f640: mov             x2, x0
    // 0x68f644: r0 = BoxInt64Instr(r2)
    //     0x68f644: sbfiz           x0, x2, #1, #0x1f
    //     0x68f648: cmp             x2, x0, asr #1
    //     0x68f64c: b.eq            #0x68f658
    //     0x68f650: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68f654: stur            x2, [x0, #7]
    // 0x68f658: cbnz            w0, #0x68f66c
    // 0x68f65c: ldur            x0, [fp, #-0x28]
    // 0x68f660: LeaveFrame
    //     0x68f660: mov             SP, fp
    //     0x68f664: ldp             fp, lr, [SP], #0x10
    // 0x68f668: ret
    //     0x68f668: ret             
    // 0x68f66c: r0 = Null
    //     0x68f66c: mov             x0, NULL
    // 0x68f670: LeaveFrame
    //     0x68f670: mov             SP, fp
    //     0x68f674: ldp             fp, lr, [SP], #0x10
    // 0x68f678: ret
    //     0x68f678: ret             
    // 0x68f67c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f67c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f680: b               #0x68f518
    // 0x68f684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f688: b               #0x68f548
    // 0x68f68c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f68c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f694: b               #0x68f590
    // 0x68f698: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f698: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68f69c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f69c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f6a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f6a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ values(/* No info */) {
    // ** addr: 0x7bcde4, size: 0x6c
    // 0x7bcde4: EnterFrame
    //     0x7bcde4: stp             fp, lr, [SP, #-0x10]!
    //     0x7bcde8: mov             fp, SP
    // 0x7bcdec: AllocStack(0x10)
    //     0x7bcdec: sub             SP, SP, #0x10
    // 0x7bcdf0: SetupParameters(IndexableSkipList<X0, X1> this /* r1 => r0, fp-0x8 */)
    //     0x7bcdf0: mov             x0, x1
    //     0x7bcdf4: stur            x1, [fp, #-8]
    // 0x7bcdf8: LoadField: r2 = r0->field_7
    //     0x7bcdf8: ldur            w2, [x0, #7]
    // 0x7bcdfc: DecompressPointer r2
    //     0x7bcdfc: add             x2, x2, HEAP, lsl #32
    // 0x7bce00: r1 = Null
    //     0x7bce00: mov             x1, NULL
    // 0x7bce04: r3 = <X1, X0, X1>
    //     0x7bce04: add             x3, PP, #8, lsl #12  ; [pp+0x85e0] TypeArguments: <X1, X0, X1>
    //     0x7bce08: ldr             x3, [x3, #0x5e0]
    // 0x7bce0c: r0 = Null
    //     0x7bce0c: mov             x0, NULL
    // 0x7bce10: cmp             x2, x0
    // 0x7bce14: b.eq            #0x7bce24
    // 0x7bce18: r30 = InstantiateTypeArgumentsStub
    //     0x7bce18: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7bce1c: LoadField: r30 = r30->field_7
    //     0x7bce1c: ldur            lr, [lr, #7]
    // 0x7bce20: blr             lr
    // 0x7bce24: mov             x1, x0
    // 0x7bce28: ldur            x0, [fp, #-8]
    // 0x7bce2c: LoadField: r2 = r0->field_b
    //     0x7bce2c: ldur            w2, [x0, #0xb]
    // 0x7bce30: DecompressPointer r2
    //     0x7bce30: add             x2, x2, HEAP, lsl #32
    // 0x7bce34: stur            x2, [fp, #-0x10]
    // 0x7bce38: r0 = _ValueIterable()
    //     0x7bce38: bl              #0x7bce50  ; Allocate_ValueIterableStub -> _ValueIterable<C1X0, C1X1> (size=0x10)
    // 0x7bce3c: ldur            x1, [fp, #-0x10]
    // 0x7bce40: StoreField: r0->field_b = r1
    //     0x7bce40: stur            w1, [x0, #0xb]
    // 0x7bce44: LeaveFrame
    //     0x7bce44: mov             SP, fp
    //     0x7bce48: ldp             fp, lr, [SP], #0x10
    // 0x7bce4c: ret
    //     0x7bce4c: ret             
  }
  _ delete(/* No info */) {
    // ** addr: 0x7be0dc, size: 0x440
    // 0x7be0dc: EnterFrame
    //     0x7be0dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7be0e0: mov             fp, SP
    // 0x7be0e4: AllocStack(0x68)
    //     0x7be0e4: sub             SP, SP, #0x68
    // 0x7be0e8: SetupParameters(IndexableSkipList<X0, X1> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7be0e8: mov             x3, x1
    //     0x7be0ec: mov             x0, x2
    //     0x7be0f0: stur            x1, [fp, #-8]
    //     0x7be0f4: stur            x2, [fp, #-0x10]
    // 0x7be0f8: CheckStackOverflow
    //     0x7be0f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be0fc: cmp             SP, x16
    //     0x7be100: b.ls            #0x7be4e8
    // 0x7be104: mov             x1, x3
    // 0x7be108: mov             x2, x0
    // 0x7be10c: r0 = _getNode()
    //     0x7be10c: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0x7be110: mov             x3, x0
    // 0x7be114: stur            x3, [fp, #-0x68]
    // 0x7be118: cmp             w3, NULL
    // 0x7be11c: b.ne            #0x7be130
    // 0x7be120: r0 = Null
    //     0x7be120: mov             x0, NULL
    // 0x7be124: LeaveFrame
    //     0x7be124: mov             SP, fp
    //     0x7be128: ldp             fp, lr, [SP], #0x10
    // 0x7be12c: ret
    //     0x7be12c: ret             
    // 0x7be130: ldur            x4, [fp, #-8]
    // 0x7be134: LoadField: r5 = r4->field_b
    //     0x7be134: ldur            w5, [x4, #0xb]
    // 0x7be138: DecompressPointer r5
    //     0x7be138: add             x5, x5, HEAP, lsl #32
    // 0x7be13c: stur            x5, [fp, #-0x60]
    // 0x7be140: ArrayLoad: r0 = r4[0]  ; List_8
    //     0x7be140: ldur            x0, [x4, #0x17]
    // 0x7be144: sub             x1, x0, #1
    // 0x7be148: LoadField: r6 = r3->field_13
    //     0x7be148: ldur            w6, [x3, #0x13]
    // 0x7be14c: DecompressPointer r6
    //     0x7be14c: add             x6, x6, HEAP, lsl #32
    // 0x7be150: stur            x6, [fp, #-0x58]
    // 0x7be154: LoadField: r0 = r6->field_b
    //     0x7be154: ldur            w0, [x6, #0xb]
    // 0x7be158: r7 = LoadInt32Instr(r0)
    //     0x7be158: sbfx            x7, x0, #1, #0x1f
    // 0x7be15c: stur            x7, [fp, #-0x50]
    // 0x7be160: sub             x8, x7, #1
    // 0x7be164: stur            x8, [fp, #-0x48]
    // 0x7be168: ArrayLoad: r9 = r3[0]  ; List_4
    //     0x7be168: ldur            w9, [x3, #0x17]
    // 0x7be16c: DecompressPointer r9
    //     0x7be16c: add             x9, x9, HEAP, lsl #32
    // 0x7be170: stur            x9, [fp, #-0x40]
    // 0x7be174: LoadField: r0 = r9->field_b
    //     0x7be174: ldur            w0, [x9, #0xb]
    // 0x7be178: r10 = LoadInt32Instr(r0)
    //     0x7be178: sbfx            x10, x0, #1, #0x1f
    // 0x7be17c: stur            x10, [fp, #-0x38]
    // 0x7be180: mov             x0, x5
    // 0x7be184: mov             x11, x1
    // 0x7be188: stur            x11, [fp, #-0x30]
    // 0x7be18c: CheckStackOverflow
    //     0x7be18c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be190: cmp             SP, x16
    //     0x7be194: b.ls            #0x7be4f0
    // 0x7be198: tbnz            x11, #0x3f, #0x7be460
    // 0x7be19c: mov             x12, x0
    // 0x7be1a0: stur            x12, [fp, #-0x28]
    // 0x7be1a4: CheckStackOverflow
    //     0x7be1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be1a8: cmp             SP, x16
    //     0x7be1ac: b.ls            #0x7be4f8
    // 0x7be1b0: LoadField: r13 = r12->field_13
    //     0x7be1b0: ldur            w13, [x12, #0x13]
    // 0x7be1b4: DecompressPointer r13
    //     0x7be1b4: add             x13, x13, HEAP, lsl #32
    // 0x7be1b8: stur            x13, [fp, #-0x20]
    // 0x7be1bc: LoadField: r0 = r13->field_b
    //     0x7be1bc: ldur            w0, [x13, #0xb]
    // 0x7be1c0: r1 = LoadInt32Instr(r0)
    //     0x7be1c0: sbfx            x1, x0, #1, #0x1f
    // 0x7be1c4: mov             x0, x1
    // 0x7be1c8: mov             x1, x11
    // 0x7be1cc: cmp             x1, x0
    // 0x7be1d0: b.hs            #0x7be500
    // 0x7be1d4: ArrayLoad: r0 = r13[r11]  ; Unknown_4
    //     0x7be1d4: add             x16, x13, x11, lsl #2
    //     0x7be1d8: ldur            w0, [x16, #0xf]
    // 0x7be1dc: DecompressPointer r0
    //     0x7be1dc: add             x0, x0, HEAP, lsl #32
    // 0x7be1e0: stur            x0, [fp, #-0x18]
    // 0x7be1e4: cmp             w0, NULL
    // 0x7be1e8: b.eq            #0x7be238
    // 0x7be1ec: LoadField: r2 = r0->field_b
    //     0x7be1ec: ldur            w2, [x0, #0xb]
    // 0x7be1f0: DecompressPointer r2
    //     0x7be1f0: add             x2, x2, HEAP, lsl #32
    // 0x7be1f4: cmp             w2, NULL
    // 0x7be1f8: b.eq            #0x7be504
    // 0x7be1fc: ldur            x1, [fp, #-0x10]
    // 0x7be200: r0 = defaultKeyComparator()
    //     0x7be200: bl              #0x68f6f0  ; [package:hive/src/box/default_key_comparator.dart] ::defaultKeyComparator
    // 0x7be204: cmp             x0, #0
    // 0x7be208: b.le            #0x7be238
    // 0x7be20c: ldur            x12, [fp, #-0x18]
    // 0x7be210: ldur            x4, [fp, #-8]
    // 0x7be214: ldur            x3, [fp, #-0x68]
    // 0x7be218: ldur            x5, [fp, #-0x60]
    // 0x7be21c: ldur            x11, [fp, #-0x30]
    // 0x7be220: ldur            x9, [fp, #-0x40]
    // 0x7be224: ldur            x6, [fp, #-0x58]
    // 0x7be228: ldur            x8, [fp, #-0x48]
    // 0x7be22c: ldur            x7, [fp, #-0x50]
    // 0x7be230: ldur            x10, [fp, #-0x38]
    // 0x7be234: b               #0x7be1a0
    // 0x7be238: ldur            x4, [fp, #-0x30]
    // 0x7be23c: ldur            x3, [fp, #-0x48]
    // 0x7be240: cmp             x4, x3
    // 0x7be244: b.le            #0x7be2e8
    // 0x7be248: ldur            x5, [fp, #-0x20]
    // 0x7be24c: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0x7be24c: add             x16, x5, x4, lsl #2
    //     0x7be250: ldur            w0, [x16, #0xf]
    // 0x7be254: DecompressPointer r0
    //     0x7be254: add             x0, x0, HEAP, lsl #32
    // 0x7be258: cmp             w0, NULL
    // 0x7be25c: b.eq            #0x7be2dc
    // 0x7be260: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7be260: ldur            w2, [x0, #0x17]
    // 0x7be264: DecompressPointer r2
    //     0x7be264: add             x2, x2, HEAP, lsl #32
    // 0x7be268: LoadField: r0 = r2->field_b
    //     0x7be268: ldur            w0, [x2, #0xb]
    // 0x7be26c: r1 = LoadInt32Instr(r0)
    //     0x7be26c: sbfx            x1, x0, #1, #0x1f
    // 0x7be270: mov             x0, x1
    // 0x7be274: mov             x1, x4
    // 0x7be278: cmp             x1, x0
    // 0x7be27c: b.hs            #0x7be508
    // 0x7be280: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0x7be280: add             x16, x2, x4, lsl #2
    //     0x7be284: ldur            w0, [x16, #0xf]
    // 0x7be288: DecompressPointer r0
    //     0x7be288: add             x0, x0, HEAP, lsl #32
    // 0x7be28c: r1 = LoadInt32Instr(r0)
    //     0x7be28c: sbfx            x1, x0, #1, #0x1f
    //     0x7be290: tbz             w0, #0, #0x7be298
    //     0x7be294: ldur            x1, [x0, #7]
    // 0x7be298: sub             x5, x1, #1
    // 0x7be29c: r0 = BoxInt64Instr(r5)
    //     0x7be29c: sbfiz           x0, x5, #1, #0x1f
    //     0x7be2a0: cmp             x5, x0, asr #1
    //     0x7be2a4: b.eq            #0x7be2b0
    //     0x7be2a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7be2ac: stur            x5, [x0, #7]
    // 0x7be2b0: mov             x1, x2
    // 0x7be2b4: ArrayStore: r1[r4] = r0  ; List_4
    //     0x7be2b4: add             x25, x1, x4, lsl #2
    //     0x7be2b8: add             x25, x25, #0xf
    //     0x7be2bc: str             w0, [x25]
    //     0x7be2c0: tbz             w0, #0, #0x7be2dc
    //     0x7be2c4: ldurb           w16, [x1, #-1]
    //     0x7be2c8: ldurb           w17, [x0, #-1]
    //     0x7be2cc: and             x16, x17, x16, lsr #2
    //     0x7be2d0: tst             x16, HEAP, lsr #32
    //     0x7be2d4: b.eq            #0x7be2dc
    //     0x7be2d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7be2dc: mov             x2, x4
    // 0x7be2e0: ldur            x4, [fp, #-0x40]
    // 0x7be2e4: b               #0x7be434
    // 0x7be2e8: ldur            x5, [fp, #-0x20]
    // 0x7be2ec: ldur            x6, [fp, #-0x58]
    // 0x7be2f0: ldur            x0, [fp, #-0x50]
    // 0x7be2f4: mov             x1, x4
    // 0x7be2f8: cmp             x1, x0
    // 0x7be2fc: b.hs            #0x7be50c
    // 0x7be300: ArrayLoad: r7 = r6[r4]  ; Unknown_4
    //     0x7be300: add             x16, x6, x4, lsl #2
    //     0x7be304: ldur            w7, [x16, #0xf]
    // 0x7be308: DecompressPointer r7
    //     0x7be308: add             x7, x7, HEAP, lsl #32
    // 0x7be30c: stur            x7, [fp, #-0x18]
    // 0x7be310: LoadField: r2 = r5->field_7
    //     0x7be310: ldur            w2, [x5, #7]
    // 0x7be314: DecompressPointer r2
    //     0x7be314: add             x2, x2, HEAP, lsl #32
    // 0x7be318: mov             x0, x7
    // 0x7be31c: r1 = Null
    //     0x7be31c: mov             x1, NULL
    // 0x7be320: cmp             w2, NULL
    // 0x7be324: b.eq            #0x7be344
    // 0x7be328: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7be328: ldur            w4, [x2, #0x17]
    // 0x7be32c: DecompressPointer r4
    //     0x7be32c: add             x4, x4, HEAP, lsl #32
    // 0x7be330: r8 = X0
    //     0x7be330: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7be334: LoadField: r9 = r4->field_7
    //     0x7be334: ldur            x9, [x4, #7]
    // 0x7be338: r3 = Null
    //     0x7be338: add             x3, PP, #8, lsl #12  ; [pp+0x84f0] Null
    //     0x7be33c: ldr             x3, [x3, #0x4f0]
    // 0x7be340: blr             x9
    // 0x7be344: ldur            x1, [fp, #-0x20]
    // 0x7be348: ldur            x0, [fp, #-0x18]
    // 0x7be34c: ldur            x2, [fp, #-0x30]
    // 0x7be350: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7be350: add             x25, x1, x2, lsl #2
    //     0x7be354: add             x25, x25, #0xf
    //     0x7be358: str             w0, [x25]
    //     0x7be35c: tbz             w0, #0, #0x7be378
    //     0x7be360: ldurb           w16, [x1, #-1]
    //     0x7be364: ldurb           w17, [x0, #-1]
    //     0x7be368: and             x16, x17, x16, lsr #2
    //     0x7be36c: tst             x16, HEAP, lsr #32
    //     0x7be370: b.eq            #0x7be378
    //     0x7be374: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7be378: ldur            x3, [fp, #-0x18]
    // 0x7be37c: cmp             w3, NULL
    // 0x7be380: b.eq            #0x7be430
    // 0x7be384: ldur            x4, [fp, #-0x40]
    // 0x7be388: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x7be388: ldur            w5, [x3, #0x17]
    // 0x7be38c: DecompressPointer r5
    //     0x7be38c: add             x5, x5, HEAP, lsl #32
    // 0x7be390: LoadField: r3 = r5->field_b
    //     0x7be390: ldur            w3, [x5, #0xb]
    // 0x7be394: r0 = LoadInt32Instr(r3)
    //     0x7be394: sbfx            x0, x3, #1, #0x1f
    // 0x7be398: mov             x1, x2
    // 0x7be39c: cmp             x1, x0
    // 0x7be3a0: b.hs            #0x7be510
    // 0x7be3a4: ArrayLoad: r3 = r5[r2]  ; Unknown_4
    //     0x7be3a4: add             x16, x5, x2, lsl #2
    //     0x7be3a8: ldur            w3, [x16, #0xf]
    // 0x7be3ac: DecompressPointer r3
    //     0x7be3ac: add             x3, x3, HEAP, lsl #32
    // 0x7be3b0: ldur            x0, [fp, #-0x38]
    // 0x7be3b4: mov             x1, x2
    // 0x7be3b8: cmp             x1, x0
    // 0x7be3bc: b.hs            #0x7be514
    // 0x7be3c0: ArrayLoad: r6 = r4[r2]  ; Unknown_4
    //     0x7be3c0: add             x16, x4, x2, lsl #2
    //     0x7be3c4: ldur            w6, [x16, #0xf]
    // 0x7be3c8: DecompressPointer r6
    //     0x7be3c8: add             x6, x6, HEAP, lsl #32
    // 0x7be3cc: r7 = LoadInt32Instr(r6)
    //     0x7be3cc: sbfx            x7, x6, #1, #0x1f
    //     0x7be3d0: tbz             w6, #0, #0x7be3d8
    //     0x7be3d4: ldur            x7, [x6, #7]
    // 0x7be3d8: sub             x6, x7, #1
    // 0x7be3dc: r7 = LoadInt32Instr(r3)
    //     0x7be3dc: sbfx            x7, x3, #1, #0x1f
    //     0x7be3e0: tbz             w3, #0, #0x7be3e8
    //     0x7be3e4: ldur            x7, [x3, #7]
    // 0x7be3e8: add             x3, x7, x6
    // 0x7be3ec: r0 = BoxInt64Instr(r3)
    //     0x7be3ec: sbfiz           x0, x3, #1, #0x1f
    //     0x7be3f0: cmp             x3, x0, asr #1
    //     0x7be3f4: b.eq            #0x7be400
    //     0x7be3f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7be3fc: stur            x3, [x0, #7]
    // 0x7be400: mov             x1, x5
    // 0x7be404: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7be404: add             x25, x1, x2, lsl #2
    //     0x7be408: add             x25, x25, #0xf
    //     0x7be40c: str             w0, [x25]
    //     0x7be410: tbz             w0, #0, #0x7be42c
    //     0x7be414: ldurb           w16, [x1, #-1]
    //     0x7be418: ldurb           w17, [x0, #-1]
    //     0x7be41c: and             x16, x17, x16, lsr #2
    //     0x7be420: tst             x16, HEAP, lsr #32
    //     0x7be424: b.eq            #0x7be42c
    //     0x7be428: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7be42c: b               #0x7be434
    // 0x7be430: ldur            x4, [fp, #-0x40]
    // 0x7be434: sub             x11, x2, #1
    // 0x7be438: ldur            x0, [fp, #-0x28]
    // 0x7be43c: mov             x9, x4
    // 0x7be440: ldur            x4, [fp, #-8]
    // 0x7be444: ldur            x3, [fp, #-0x68]
    // 0x7be448: ldur            x5, [fp, #-0x60]
    // 0x7be44c: ldur            x6, [fp, #-0x58]
    // 0x7be450: ldur            x8, [fp, #-0x48]
    // 0x7be454: ldur            x7, [fp, #-0x50]
    // 0x7be458: ldur            x10, [fp, #-0x38]
    // 0x7be45c: b               #0x7be188
    // 0x7be460: mov             x3, x4
    // 0x7be464: mov             x2, x6
    // 0x7be468: LoadField: r4 = r2->field_b
    //     0x7be468: ldur            w4, [x2, #0xb]
    // 0x7be46c: r2 = LoadInt32Instr(r4)
    //     0x7be46c: sbfx            x2, x4, #1, #0x1f
    // 0x7be470: sub             x4, x2, #1
    // 0x7be474: ArrayLoad: r2 = r3[0]  ; List_8
    //     0x7be474: ldur            x2, [x3, #0x17]
    // 0x7be478: sub             x5, x2, #1
    // 0x7be47c: cmp             x4, x5
    // 0x7be480: b.ne            #0x7be4c4
    // 0x7be484: cmp             x2, #1
    // 0x7be488: b.le            #0x7be4c4
    // 0x7be48c: ldur            x2, [fp, #-0x60]
    // 0x7be490: LoadField: r6 = r2->field_13
    //     0x7be490: ldur            w6, [x2, #0x13]
    // 0x7be494: DecompressPointer r6
    //     0x7be494: add             x6, x6, HEAP, lsl #32
    // 0x7be498: LoadField: r2 = r6->field_b
    //     0x7be498: ldur            w2, [x6, #0xb]
    // 0x7be49c: r0 = LoadInt32Instr(r2)
    //     0x7be49c: sbfx            x0, x2, #1, #0x1f
    // 0x7be4a0: mov             x1, x4
    // 0x7be4a4: cmp             x1, x0
    // 0x7be4a8: b.hs            #0x7be518
    // 0x7be4ac: ArrayLoad: r1 = r6[r4]  ; Unknown_4
    //     0x7be4ac: add             x16, x6, x4, lsl #2
    //     0x7be4b0: ldur            w1, [x16, #0xf]
    // 0x7be4b4: DecompressPointer r1
    //     0x7be4b4: add             x1, x1, HEAP, lsl #32
    // 0x7be4b8: cmp             w1, NULL
    // 0x7be4bc: b.ne            #0x7be4c4
    // 0x7be4c0: ArrayStore: r3[0] = r5  ; List_8
    //     0x7be4c0: stur            x5, [x3, #0x17]
    // 0x7be4c4: ldur            x1, [fp, #-0x68]
    // 0x7be4c8: LoadField: r2 = r3->field_1f
    //     0x7be4c8: ldur            x2, [x3, #0x1f]
    // 0x7be4cc: sub             x4, x2, #1
    // 0x7be4d0: StoreField: r3->field_1f = r4
    //     0x7be4d0: stur            x4, [x3, #0x1f]
    // 0x7be4d4: LoadField: r0 = r1->field_f
    //     0x7be4d4: ldur            w0, [x1, #0xf]
    // 0x7be4d8: DecompressPointer r0
    //     0x7be4d8: add             x0, x0, HEAP, lsl #32
    // 0x7be4dc: LeaveFrame
    //     0x7be4dc: mov             SP, fp
    //     0x7be4e0: ldp             fp, lr, [SP], #0x10
    // 0x7be4e4: ret
    //     0x7be4e4: ret             
    // 0x7be4e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7be4e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7be4ec: b               #0x7be104
    // 0x7be4f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7be4f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7be4f4: b               #0x7be198
    // 0x7be4f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7be4f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7be4fc: b               #0x7be1b0
    // 0x7be500: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be500: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7be504: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7be504: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7be508: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be508: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7be50c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be50c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7be510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be510: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7be514: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be514: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7be518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7be518: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x7be548, size: 0x824
    // 0x7be548: EnterFrame
    //     0x7be548: stp             fp, lr, [SP, #-0x10]!
    //     0x7be54c: mov             fp, SP
    // 0x7be550: AllocStack(0x70)
    //     0x7be550: sub             SP, SP, #0x70
    // 0x7be554: SetupParameters(IndexableSkipList<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x7be554: mov             x4, x1
    //     0x7be558: mov             x0, x3
    //     0x7be55c: stur            x3, [fp, #-0x18]
    //     0x7be560: mov             x3, x2
    //     0x7be564: stur            x1, [fp, #-8]
    //     0x7be568: stur            x2, [fp, #-0x10]
    // 0x7be56c: CheckStackOverflow
    //     0x7be56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be570: cmp             SP, x16
    //     0x7be574: b.ls            #0x7becf8
    // 0x7be578: mov             x1, x4
    // 0x7be57c: mov             x2, x3
    // 0x7be580: r0 = _getNode()
    //     0x7be580: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0x7be584: mov             x3, x0
    // 0x7be588: stur            x3, [fp, #-0x28]
    // 0x7be58c: cmp             w3, NULL
    // 0x7be590: b.eq            #0x7be610
    // 0x7be594: LoadField: r4 = r3->field_f
    //     0x7be594: ldur            w4, [x3, #0xf]
    // 0x7be598: DecompressPointer r4
    //     0x7be598: add             x4, x4, HEAP, lsl #32
    // 0x7be59c: stur            x4, [fp, #-0x20]
    // 0x7be5a0: LoadField: r2 = r3->field_7
    //     0x7be5a0: ldur            w2, [x3, #7]
    // 0x7be5a4: DecompressPointer r2
    //     0x7be5a4: add             x2, x2, HEAP, lsl #32
    // 0x7be5a8: ldur            x0, [fp, #-0x18]
    // 0x7be5ac: r1 = Null
    //     0x7be5ac: mov             x1, NULL
    // 0x7be5b0: cmp             w0, NULL
    // 0x7be5b4: b.eq            #0x7be5dc
    // 0x7be5b8: cmp             w2, NULL
    // 0x7be5bc: b.eq            #0x7be5dc
    // 0x7be5c0: LoadField: r4 = r2->field_1b
    //     0x7be5c0: ldur            w4, [x2, #0x1b]
    // 0x7be5c4: DecompressPointer r4
    //     0x7be5c4: add             x4, x4, HEAP, lsl #32
    // 0x7be5c8: r8 = X1?
    //     0x7be5c8: ldr             x8, [PP, #0x2518]  ; [pp+0x2518] TypeParameter: X1?
    // 0x7be5cc: LoadField: r9 = r4->field_7
    //     0x7be5cc: ldur            x9, [x4, #7]
    // 0x7be5d0: r3 = Null
    //     0x7be5d0: add             x3, PP, #8, lsl #12  ; [pp+0x8510] Null
    //     0x7be5d4: ldr             x3, [x3, #0x510]
    // 0x7be5d8: blr             x9
    // 0x7be5dc: ldur            x0, [fp, #-0x18]
    // 0x7be5e0: ldur            x1, [fp, #-0x28]
    // 0x7be5e4: StoreField: r1->field_f = r0
    //     0x7be5e4: stur            w0, [x1, #0xf]
    //     0x7be5e8: ldurb           w16, [x1, #-1]
    //     0x7be5ec: ldurb           w17, [x0, #-1]
    //     0x7be5f0: and             x16, x17, x16, lsr #2
    //     0x7be5f4: tst             x16, HEAP, lsr #32
    //     0x7be5f8: b.eq            #0x7be600
    //     0x7be5fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7be600: ldur            x0, [fp, #-0x20]
    // 0x7be604: LeaveFrame
    //     0x7be604: mov             SP, fp
    //     0x7be608: ldp             fp, lr, [SP], #0x10
    // 0x7be60c: ret
    //     0x7be60c: ret             
    // 0x7be610: ldur            x0, [fp, #-8]
    // 0x7be614: LoadField: r3 = r0->field_f
    //     0x7be614: ldur            w3, [x0, #0xf]
    // 0x7be618: DecompressPointer r3
    //     0x7be618: add             x3, x3, HEAP, lsl #32
    // 0x7be61c: stur            x3, [fp, #-0x20]
    // 0x7be620: r4 = 0
    //     0x7be620: movz            x4, #0
    // 0x7be624: stur            x4, [fp, #-0x30]
    // 0x7be628: CheckStackOverflow
    //     0x7be628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be62c: cmp             SP, x16
    //     0x7be630: b.ls            #0x7bed00
    // 0x7be634: mov             x1, x3
    // 0x7be638: r2 = 2
    //     0x7be638: movz            x2, #0x2
    // 0x7be63c: r0 = nextInt()
    //     0x7be63c: bl              #0x7bed78  ; [dart:math] _Random::nextInt
    // 0x7be640: cbnz            x0, #0x7be660
    // 0x7be644: ldur            x0, [fp, #-0x30]
    // 0x7be648: cmp             x0, #0xb
    // 0x7be64c: b.ge            #0x7be664
    // 0x7be650: add             x4, x0, #1
    // 0x7be654: ldur            x0, [fp, #-8]
    // 0x7be658: ldur            x3, [fp, #-0x20]
    // 0x7be65c: b               #0x7be624
    // 0x7be660: ldur            x0, [fp, #-0x30]
    // 0x7be664: ldur            x4, [fp, #-8]
    // 0x7be668: ArrayLoad: r1 = r4[0]  ; List_8
    //     0x7be668: ldur            x1, [x4, #0x17]
    // 0x7be66c: cmp             x0, x1
    // 0x7be670: b.lt            #0x7be684
    // 0x7be674: add             x0, x1, #1
    // 0x7be678: ArrayStore: r4[0] = r0  ; List_8
    //     0x7be678: stur            x0, [x4, #0x17]
    // 0x7be67c: mov             x5, x1
    // 0x7be680: b               #0x7be68c
    // 0x7be684: mov             x5, x0
    // 0x7be688: mov             x0, x1
    // 0x7be68c: stur            x5, [fp, #-0x30]
    // 0x7be690: stur            x0, [fp, #-0x38]
    // 0x7be694: LoadField: r6 = r4->field_7
    //     0x7be694: ldur            w6, [x4, #7]
    // 0x7be698: DecompressPointer r6
    //     0x7be698: add             x6, x6, HEAP, lsl #32
    // 0x7be69c: mov             x2, x6
    // 0x7be6a0: stur            x6, [fp, #-0x20]
    // 0x7be6a4: r1 = Null
    //     0x7be6a4: mov             x1, NULL
    // 0x7be6a8: r3 = <_Node<X0, X1>?>
    //     0x7be6a8: ldr             x3, [PP, #0x7e50]  ; [pp+0x7e50] TypeArguments: <_Node<X0, X1>?>
    // 0x7be6ac: r30 = InstantiateTypeArgumentsStub
    //     0x7be6ac: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7be6b0: LoadField: r30 = r30->field_7
    //     0x7be6b0: ldur            lr, [lr, #7]
    // 0x7be6b4: blr             lr
    // 0x7be6b8: mov             x4, x0
    // 0x7be6bc: ldur            x3, [fp, #-0x30]
    // 0x7be6c0: stur            x4, [fp, #-0x48]
    // 0x7be6c4: add             x5, x3, #1
    // 0x7be6c8: stur            x5, [fp, #-0x40]
    // 0x7be6cc: r0 = BoxInt64Instr(r5)
    //     0x7be6cc: sbfiz           x0, x5, #1, #0x1f
    //     0x7be6d0: cmp             x5, x0, asr #1
    //     0x7be6d4: b.eq            #0x7be6e0
    //     0x7be6d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7be6dc: stur            x5, [x0, #7]
    // 0x7be6e0: mov             x2, x0
    // 0x7be6e4: r1 = <int>
    //     0x7be6e4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7be6e8: stur            x0, [fp, #-0x28]
    // 0x7be6ec: r0 = AllocateArray()
    //     0x7be6ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7be6f0: stur            x0, [fp, #-0x50]
    // 0x7be6f4: ldur            x2, [fp, #-0x40]
    // 0x7be6f8: r1 = 0
    //     0x7be6f8: movz            x1, #0
    // 0x7be6fc: CheckStackOverflow
    //     0x7be6fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be700: cmp             SP, x16
    //     0x7be704: b.ls            #0x7bed08
    // 0x7be708: cmp             x1, x2
    // 0x7be70c: b.ge            #0x7be724
    // 0x7be710: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x7be710: add             x3, x0, x1, lsl #2
    //     0x7be714: stur            wzr, [x3, #0xf]
    // 0x7be718: add             x3, x1, #1
    // 0x7be71c: mov             x1, x3
    // 0x7be720: b               #0x7be6fc
    // 0x7be724: ldur            x3, [fp, #-8]
    // 0x7be728: ldur            x6, [fp, #-0x10]
    // 0x7be72c: ldur            x5, [fp, #-0x18]
    // 0x7be730: ldur            x4, [fp, #-0x38]
    // 0x7be734: ldur            x1, [fp, #-0x20]
    // 0x7be738: r0 = _Node()
    //     0x7be738: bl              #0x7bed6c  ; Allocate_NodeStub -> _Node<X0, X1> (size=0x1c)
    // 0x7be73c: mov             x3, x0
    // 0x7be740: ldur            x0, [fp, #-0x10]
    // 0x7be744: stur            x3, [fp, #-0x20]
    // 0x7be748: StoreField: r3->field_b = r0
    //     0x7be748: stur            w0, [x3, #0xb]
    // 0x7be74c: ldur            x1, [fp, #-0x18]
    // 0x7be750: StoreField: r3->field_f = r1
    //     0x7be750: stur            w1, [x3, #0xf]
    // 0x7be754: ldur            x1, [fp, #-0x48]
    // 0x7be758: ldur            x2, [fp, #-0x28]
    // 0x7be75c: r0 = AllocateArray()
    //     0x7be75c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7be760: mov             x4, x0
    // 0x7be764: ldur            x3, [fp, #-0x20]
    // 0x7be768: stur            x4, [fp, #-0x68]
    // 0x7be76c: StoreField: r3->field_13 = r4
    //     0x7be76c: stur            w4, [x3, #0x13]
    // 0x7be770: ldur            x5, [fp, #-0x50]
    // 0x7be774: ArrayStore: r3[0] = r5  ; List_4
    //     0x7be774: stur            w5, [x3, #0x17]
    // 0x7be778: ldur            x6, [fp, #-8]
    // 0x7be77c: LoadField: r0 = r6->field_b
    //     0x7be77c: ldur            w0, [x6, #0xb]
    // 0x7be780: DecompressPointer r0
    //     0x7be780: add             x0, x0, HEAP, lsl #32
    // 0x7be784: ldur            x1, [fp, #-0x38]
    // 0x7be788: sub             x2, x1, #1
    // 0x7be78c: mov             x8, x2
    // 0x7be790: ldur            x7, [fp, #-0x30]
    // 0x7be794: stur            x8, [fp, #-0x60]
    // 0x7be798: CheckStackOverflow
    //     0x7be798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be79c: cmp             SP, x16
    //     0x7be7a0: b.ls            #0x7bed10
    // 0x7be7a4: tbnz            x8, #0x3f, #0x7bebf0
    // 0x7be7a8: mov             x9, x0
    // 0x7be7ac: stur            x9, [fp, #-0x58]
    // 0x7be7b0: CheckStackOverflow
    //     0x7be7b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be7b4: cmp             SP, x16
    //     0x7be7b8: b.ls            #0x7bed18
    // 0x7be7bc: LoadField: r10 = r9->field_13
    //     0x7be7bc: ldur            w10, [x9, #0x13]
    // 0x7be7c0: DecompressPointer r10
    //     0x7be7c0: add             x10, x10, HEAP, lsl #32
    // 0x7be7c4: stur            x10, [fp, #-0x28]
    // 0x7be7c8: LoadField: r0 = r10->field_b
    //     0x7be7c8: ldur            w0, [x10, #0xb]
    // 0x7be7cc: r11 = LoadInt32Instr(r0)
    //     0x7be7cc: sbfx            x11, x0, #1, #0x1f
    // 0x7be7d0: mov             x0, x11
    // 0x7be7d4: mov             x1, x8
    // 0x7be7d8: stur            x11, [fp, #-0x38]
    // 0x7be7dc: cmp             x1, x0
    // 0x7be7e0: b.hs            #0x7bed20
    // 0x7be7e4: ArrayLoad: r0 = r10[r8]  ; Unknown_4
    //     0x7be7e4: add             x16, x10, x8, lsl #2
    //     0x7be7e8: ldur            w0, [x16, #0xf]
    // 0x7be7ec: DecompressPointer r0
    //     0x7be7ec: add             x0, x0, HEAP, lsl #32
    // 0x7be7f0: stur            x0, [fp, #-0x18]
    // 0x7be7f4: cmp             w0, NULL
    // 0x7be7f8: b.eq            #0x7be838
    // 0x7be7fc: LoadField: r2 = r0->field_b
    //     0x7be7fc: ldur            w2, [x0, #0xb]
    // 0x7be800: DecompressPointer r2
    //     0x7be800: add             x2, x2, HEAP, lsl #32
    // 0x7be804: cmp             w2, NULL
    // 0x7be808: b.eq            #0x7bed24
    // 0x7be80c: ldur            x1, [fp, #-0x10]
    // 0x7be810: r0 = defaultKeyComparator()
    //     0x7be810: bl              #0x68f6f0  ; [package:hive/src/box/default_key_comparator.dart] ::defaultKeyComparator
    // 0x7be814: tbnz            x0, #0x3f, #0x7be838
    // 0x7be818: ldur            x9, [fp, #-0x18]
    // 0x7be81c: ldur            x6, [fp, #-8]
    // 0x7be820: ldur            x7, [fp, #-0x30]
    // 0x7be824: ldur            x3, [fp, #-0x20]
    // 0x7be828: ldur            x8, [fp, #-0x60]
    // 0x7be82c: ldur            x4, [fp, #-0x68]
    // 0x7be830: ldur            x5, [fp, #-0x50]
    // 0x7be834: b               #0x7be7ac
    // 0x7be838: ldur            x3, [fp, #-0x30]
    // 0x7be83c: ldur            x4, [fp, #-0x60]
    // 0x7be840: cmp             x4, x3
    // 0x7be844: b.le            #0x7be8e4
    // 0x7be848: ldur            x5, [fp, #-0x28]
    // 0x7be84c: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0x7be84c: add             x16, x5, x4, lsl #2
    //     0x7be850: ldur            w0, [x16, #0xf]
    // 0x7be854: DecompressPointer r0
    //     0x7be854: add             x0, x0, HEAP, lsl #32
    // 0x7be858: cmp             w0, NULL
    // 0x7be85c: b.eq            #0x7be8dc
    // 0x7be860: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7be860: ldur            w2, [x0, #0x17]
    // 0x7be864: DecompressPointer r2
    //     0x7be864: add             x2, x2, HEAP, lsl #32
    // 0x7be868: LoadField: r0 = r2->field_b
    //     0x7be868: ldur            w0, [x2, #0xb]
    // 0x7be86c: r1 = LoadInt32Instr(r0)
    //     0x7be86c: sbfx            x1, x0, #1, #0x1f
    // 0x7be870: mov             x0, x1
    // 0x7be874: mov             x1, x4
    // 0x7be878: cmp             x1, x0
    // 0x7be87c: b.hs            #0x7bed28
    // 0x7be880: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0x7be880: add             x16, x2, x4, lsl #2
    //     0x7be884: ldur            w0, [x16, #0xf]
    // 0x7be888: DecompressPointer r0
    //     0x7be888: add             x0, x0, HEAP, lsl #32
    // 0x7be88c: r1 = LoadInt32Instr(r0)
    //     0x7be88c: sbfx            x1, x0, #1, #0x1f
    //     0x7be890: tbz             w0, #0, #0x7be898
    //     0x7be894: ldur            x1, [x0, #7]
    // 0x7be898: add             x5, x1, #1
    // 0x7be89c: r0 = BoxInt64Instr(r5)
    //     0x7be89c: sbfiz           x0, x5, #1, #0x1f
    //     0x7be8a0: cmp             x5, x0, asr #1
    //     0x7be8a4: b.eq            #0x7be8b0
    //     0x7be8a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7be8ac: stur            x5, [x0, #7]
    // 0x7be8b0: mov             x1, x2
    // 0x7be8b4: ArrayStore: r1[r4] = r0  ; List_4
    //     0x7be8b4: add             x25, x1, x4, lsl #2
    //     0x7be8b8: add             x25, x25, #0xf
    //     0x7be8bc: str             w0, [x25]
    //     0x7be8c0: tbz             w0, #0, #0x7be8dc
    //     0x7be8c4: ldurb           w16, [x1, #-1]
    //     0x7be8c8: ldurb           w17, [x0, #-1]
    //     0x7be8cc: and             x16, x17, x16, lsr #2
    //     0x7be8d0: tst             x16, HEAP, lsr #32
    //     0x7be8d4: b.eq            #0x7be8dc
    //     0x7be8d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7be8dc: mov             x2, x4
    // 0x7be8e0: b               #0x7bebd4
    // 0x7be8e4: ldur            x5, [fp, #-0x28]
    // 0x7be8e8: cbnz            x4, #0x7be918
    // 0x7be8ec: ldur            x6, [fp, #-0x50]
    // 0x7be8f0: ldur            x0, [fp, #-0x40]
    // 0x7be8f4: r1 = 0
    //     0x7be8f4: movz            x1, #0
    // 0x7be8f8: cmp             x1, x0
    // 0x7be8fc: b.hs            #0x7bed2c
    // 0x7be900: r16 = 2
    //     0x7be900: movz            x16, #0x2
    // 0x7be904: StoreField: r6->field_f = r16
    //     0x7be904: stur            w16, [x6, #0xf]
    // 0x7be908: mov             x16, x6
    // 0x7be90c: mov             x6, x5
    // 0x7be910: mov             x5, x16
    // 0x7be914: b               #0x7beb20
    // 0x7be918: ldur            x6, [fp, #-0x50]
    // 0x7be91c: sub             x7, x4, #1
    // 0x7be920: ldur            x0, [fp, #-0x38]
    // 0x7be924: mov             x1, x7
    // 0x7be928: stur            x7, [fp, #-0x70]
    // 0x7be92c: cmp             x1, x0
    // 0x7be930: b.hs            #0x7bed30
    // 0x7be934: ArrayLoad: r0 = r5[r7]  ; Unknown_4
    //     0x7be934: add             x16, x5, x7, lsl #2
    //     0x7be938: ldur            w0, [x16, #0xf]
    // 0x7be93c: DecompressPointer r0
    //     0x7be93c: add             x0, x0, HEAP, lsl #32
    // 0x7be940: r8 = 0
    //     0x7be940: movz            x8, #0
    // 0x7be944: stur            x8, [fp, #-0x38]
    // 0x7be948: stur            x0, [fp, #-0x18]
    // 0x7be94c: CheckStackOverflow
    //     0x7be94c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7be950: cmp             SP, x16
    //     0x7be954: b.ls            #0x7bed34
    // 0x7be958: cmp             w0, NULL
    // 0x7be95c: b.eq            #0x7bea10
    // 0x7be960: LoadField: r2 = r0->field_b
    //     0x7be960: ldur            w2, [x0, #0xb]
    // 0x7be964: DecompressPointer r2
    //     0x7be964: add             x2, x2, HEAP, lsl #32
    // 0x7be968: cmp             w2, NULL
    // 0x7be96c: b.eq            #0x7bed3c
    // 0x7be970: ldur            x1, [fp, #-0x10]
    // 0x7be974: r0 = defaultKeyComparator()
    //     0x7be974: bl              #0x68f6f0  ; [package:hive/src/box/default_key_comparator.dart] ::defaultKeyComparator
    // 0x7be978: tbnz            x0, #0x3f, #0x7bea08
    // 0x7be97c: ldur            x2, [fp, #-0x70]
    // 0x7be980: ldur            x4, [fp, #-0x38]
    // 0x7be984: ldur            x3, [fp, #-0x18]
    // 0x7be988: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x7be988: ldur            w5, [x3, #0x17]
    // 0x7be98c: DecompressPointer r5
    //     0x7be98c: add             x5, x5, HEAP, lsl #32
    // 0x7be990: LoadField: r0 = r5->field_b
    //     0x7be990: ldur            w0, [x5, #0xb]
    // 0x7be994: r1 = LoadInt32Instr(r0)
    //     0x7be994: sbfx            x1, x0, #1, #0x1f
    // 0x7be998: mov             x0, x1
    // 0x7be99c: mov             x1, x2
    // 0x7be9a0: cmp             x1, x0
    // 0x7be9a4: b.hs            #0x7bed40
    // 0x7be9a8: ArrayLoad: r0 = r5[r2]  ; Unknown_4
    //     0x7be9a8: add             x16, x5, x2, lsl #2
    //     0x7be9ac: ldur            w0, [x16, #0xf]
    // 0x7be9b0: DecompressPointer r0
    //     0x7be9b0: add             x0, x0, HEAP, lsl #32
    // 0x7be9b4: r1 = LoadInt32Instr(r0)
    //     0x7be9b4: sbfx            x1, x0, #1, #0x1f
    //     0x7be9b8: tbz             w0, #0, #0x7be9c0
    //     0x7be9bc: ldur            x1, [x0, #7]
    // 0x7be9c0: add             x8, x4, x1
    // 0x7be9c4: LoadField: r4 = r3->field_13
    //     0x7be9c4: ldur            w4, [x3, #0x13]
    // 0x7be9c8: DecompressPointer r4
    //     0x7be9c8: add             x4, x4, HEAP, lsl #32
    // 0x7be9cc: LoadField: r0 = r4->field_b
    //     0x7be9cc: ldur            w0, [x4, #0xb]
    // 0x7be9d0: r1 = LoadInt32Instr(r0)
    //     0x7be9d0: sbfx            x1, x0, #1, #0x1f
    // 0x7be9d4: mov             x0, x1
    // 0x7be9d8: mov             x1, x2
    // 0x7be9dc: cmp             x1, x0
    // 0x7be9e0: b.hs            #0x7bed44
    // 0x7be9e4: ArrayLoad: r0 = r4[r2]  ; Unknown_4
    //     0x7be9e4: add             x16, x4, x2, lsl #2
    //     0x7be9e8: ldur            w0, [x16, #0xf]
    // 0x7be9ec: DecompressPointer r0
    //     0x7be9ec: add             x0, x0, HEAP, lsl #32
    // 0x7be9f0: ldur            x3, [fp, #-0x30]
    // 0x7be9f4: ldur            x4, [fp, #-0x60]
    // 0x7be9f8: mov             x7, x2
    // 0x7be9fc: ldur            x5, [fp, #-0x28]
    // 0x7bea00: ldur            x6, [fp, #-0x50]
    // 0x7bea04: b               #0x7be944
    // 0x7bea08: ldur            x4, [fp, #-0x38]
    // 0x7bea0c: b               #0x7bea14
    // 0x7bea10: mov             x4, x8
    // 0x7bea14: ldur            x2, [fp, #-0x60]
    // 0x7bea18: ldur            x3, [fp, #-0x30]
    // 0x7bea1c: ldur            x5, [fp, #-0x50]
    // 0x7bea20: CheckStackOverflow
    //     0x7bea20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bea24: cmp             SP, x16
    //     0x7bea28: b.ls            #0x7bed48
    // 0x7bea2c: cmp             x2, x3
    // 0x7bea30: b.gt            #0x7beaac
    // 0x7bea34: ldur            x0, [fp, #-0x40]
    // 0x7bea38: mov             x1, x2
    // 0x7bea3c: cmp             x1, x0
    // 0x7bea40: b.hs            #0x7bed50
    // 0x7bea44: ArrayLoad: r0 = r5[r2]  ; Unknown_4
    //     0x7bea44: add             x16, x5, x2, lsl #2
    //     0x7bea48: ldur            w0, [x16, #0xf]
    // 0x7bea4c: DecompressPointer r0
    //     0x7bea4c: add             x0, x0, HEAP, lsl #32
    // 0x7bea50: r1 = LoadInt32Instr(r0)
    //     0x7bea50: sbfx            x1, x0, #1, #0x1f
    //     0x7bea54: tbz             w0, #0, #0x7bea5c
    //     0x7bea58: ldur            x1, [x0, #7]
    // 0x7bea5c: add             x6, x1, x4
    // 0x7bea60: r0 = BoxInt64Instr(r6)
    //     0x7bea60: sbfiz           x0, x6, #1, #0x1f
    //     0x7bea64: cmp             x6, x0, asr #1
    //     0x7bea68: b.eq            #0x7bea74
    //     0x7bea6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bea70: stur            x6, [x0, #7]
    // 0x7bea74: mov             x1, x5
    // 0x7bea78: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7bea78: add             x25, x1, x2, lsl #2
    //     0x7bea7c: add             x25, x25, #0xf
    //     0x7bea80: str             w0, [x25]
    //     0x7bea84: tbz             w0, #0, #0x7beaa0
    //     0x7bea88: ldurb           w16, [x1, #-1]
    //     0x7bea8c: ldurb           w17, [x0, #-1]
    //     0x7bea90: and             x16, x17, x16, lsr #2
    //     0x7bea94: tst             x16, HEAP, lsr #32
    //     0x7bea98: b.eq            #0x7beaa0
    //     0x7bea9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7beaa0: add             x0, x2, #1
    // 0x7beaa4: mov             x2, x0
    // 0x7beaa8: b               #0x7bea20
    // 0x7beaac: ldur            x4, [fp, #-0x60]
    // 0x7beab0: ldur            x0, [fp, #-0x40]
    // 0x7beab4: mov             x1, x4
    // 0x7beab8: cmp             x1, x0
    // 0x7beabc: b.hs            #0x7bed54
    // 0x7beac0: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0x7beac0: add             x16, x5, x4, lsl #2
    //     0x7beac4: ldur            w0, [x16, #0xf]
    // 0x7beac8: DecompressPointer r0
    //     0x7beac8: add             x0, x0, HEAP, lsl #32
    // 0x7beacc: r1 = LoadInt32Instr(r0)
    //     0x7beacc: sbfx            x1, x0, #1, #0x1f
    //     0x7bead0: tbz             w0, #0, #0x7bead8
    //     0x7bead4: ldur            x1, [x0, #7]
    // 0x7bead8: add             x2, x1, #1
    // 0x7beadc: r0 = BoxInt64Instr(r2)
    //     0x7beadc: sbfiz           x0, x2, #1, #0x1f
    //     0x7beae0: cmp             x2, x0, asr #1
    //     0x7beae4: b.eq            #0x7beaf0
    //     0x7beae8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7beaec: stur            x2, [x0, #7]
    // 0x7beaf0: mov             x1, x5
    // 0x7beaf4: ArrayStore: r1[r4] = r0  ; List_4
    //     0x7beaf4: add             x25, x1, x4, lsl #2
    //     0x7beaf8: add             x25, x25, #0xf
    //     0x7beafc: str             w0, [x25]
    //     0x7beb00: tbz             w0, #0, #0x7beb1c
    //     0x7beb04: ldurb           w16, [x1, #-1]
    //     0x7beb08: ldurb           w17, [x0, #-1]
    //     0x7beb0c: and             x16, x17, x16, lsr #2
    //     0x7beb10: tst             x16, HEAP, lsr #32
    //     0x7beb14: b.eq            #0x7beb1c
    //     0x7beb18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7beb1c: ldur            x6, [fp, #-0x28]
    // 0x7beb20: ArrayLoad: r7 = r6[r4]  ; Unknown_4
    //     0x7beb20: add             x16, x6, x4, lsl #2
    //     0x7beb24: ldur            w7, [x16, #0xf]
    // 0x7beb28: DecompressPointer r7
    //     0x7beb28: add             x7, x7, HEAP, lsl #32
    // 0x7beb2c: mov             x0, x7
    // 0x7beb30: ldur            x2, [fp, #-0x48]
    // 0x7beb34: stur            x7, [fp, #-0x18]
    // 0x7beb38: r1 = Null
    //     0x7beb38: mov             x1, NULL
    // 0x7beb3c: cmp             w2, NULL
    // 0x7beb40: b.eq            #0x7beb60
    // 0x7beb44: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7beb44: ldur            w4, [x2, #0x17]
    // 0x7beb48: DecompressPointer r4
    //     0x7beb48: add             x4, x4, HEAP, lsl #32
    // 0x7beb4c: r8 = X0
    //     0x7beb4c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7beb50: LoadField: r9 = r4->field_7
    //     0x7beb50: ldur            x9, [x4, #7]
    // 0x7beb54: r3 = Null
    //     0x7beb54: add             x3, PP, #8, lsl #12  ; [pp+0x8520] Null
    //     0x7beb58: ldr             x3, [x3, #0x520]
    // 0x7beb5c: blr             x9
    // 0x7beb60: ldur            x0, [fp, #-0x40]
    // 0x7beb64: ldur            x1, [fp, #-0x60]
    // 0x7beb68: cmp             x1, x0
    // 0x7beb6c: b.hs            #0x7bed58
    // 0x7beb70: ldur            x1, [fp, #-0x68]
    // 0x7beb74: ldur            x0, [fp, #-0x18]
    // 0x7beb78: ldur            x2, [fp, #-0x60]
    // 0x7beb7c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7beb7c: add             x25, x1, x2, lsl #2
    //     0x7beb80: add             x25, x25, #0xf
    //     0x7beb84: str             w0, [x25]
    //     0x7beb88: tbz             w0, #0, #0x7beba4
    //     0x7beb8c: ldurb           w16, [x1, #-1]
    //     0x7beb90: ldurb           w17, [x0, #-1]
    //     0x7beb94: and             x16, x17, x16, lsr #2
    //     0x7beb98: tst             x16, HEAP, lsr #32
    //     0x7beb9c: b.eq            #0x7beba4
    //     0x7beba0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7beba4: ldur            x1, [fp, #-0x28]
    // 0x7beba8: ldur            x0, [fp, #-0x20]
    // 0x7bebac: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7bebac: add             x25, x1, x2, lsl #2
    //     0x7bebb0: add             x25, x25, #0xf
    //     0x7bebb4: str             w0, [x25]
    //     0x7bebb8: tbz             w0, #0, #0x7bebd4
    //     0x7bebbc: ldurb           w16, [x1, #-1]
    //     0x7bebc0: ldurb           w17, [x0, #-1]
    //     0x7bebc4: and             x16, x17, x16, lsr #2
    //     0x7bebc8: tst             x16, HEAP, lsr #32
    //     0x7bebcc: b.eq            #0x7bebd4
    //     0x7bebd0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7bebd4: sub             x8, x2, #1
    // 0x7bebd8: ldur            x0, [fp, #-0x58]
    // 0x7bebdc: ldur            x6, [fp, #-8]
    // 0x7bebe0: ldur            x3, [fp, #-0x20]
    // 0x7bebe4: ldur            x4, [fp, #-0x68]
    // 0x7bebe8: ldur            x5, [fp, #-0x50]
    // 0x7bebec: b               #0x7be790
    // 0x7bebf0: ldur            x2, [fp, #-0x30]
    // 0x7bebf4: ldur            x4, [fp, #-0x68]
    // 0x7bebf8: ldur            x3, [fp, #-0x50]
    // 0x7bebfc: r5 = 1
    //     0x7bebfc: movz            x5, #0x1
    // 0x7bec00: CheckStackOverflow
    //     0x7bec00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bec04: cmp             SP, x16
    //     0x7bec08: b.ls            #0x7bed5c
    // 0x7bec0c: cmp             x5, x2
    // 0x7bec10: b.gt            #0x7becd8
    // 0x7bec14: ldur            x0, [fp, #-0x40]
    // 0x7bec18: mov             x1, x5
    // 0x7bec1c: cmp             x1, x0
    // 0x7bec20: b.hs            #0x7bed64
    // 0x7bec24: ArrayLoad: r6 = r4[r5]  ; Unknown_4
    //     0x7bec24: add             x16, x4, x5, lsl #2
    //     0x7bec28: ldur            w6, [x16, #0xf]
    // 0x7bec2c: DecompressPointer r6
    //     0x7bec2c: add             x6, x6, HEAP, lsl #32
    // 0x7bec30: cmp             w6, NULL
    // 0x7bec34: b.eq            #0x7beccc
    // 0x7bec38: ArrayLoad: r7 = r6[0]  ; List_4
    //     0x7bec38: ldur            w7, [x6, #0x17]
    // 0x7bec3c: DecompressPointer r7
    //     0x7bec3c: add             x7, x7, HEAP, lsl #32
    // 0x7bec40: LoadField: r6 = r7->field_b
    //     0x7bec40: ldur            w6, [x7, #0xb]
    // 0x7bec44: r0 = LoadInt32Instr(r6)
    //     0x7bec44: sbfx            x0, x6, #1, #0x1f
    // 0x7bec48: mov             x1, x5
    // 0x7bec4c: cmp             x1, x0
    // 0x7bec50: b.hs            #0x7bed68
    // 0x7bec54: ArrayLoad: r6 = r7[r5]  ; Unknown_4
    //     0x7bec54: add             x16, x7, x5, lsl #2
    //     0x7bec58: ldur            w6, [x16, #0xf]
    // 0x7bec5c: DecompressPointer r6
    //     0x7bec5c: add             x6, x6, HEAP, lsl #32
    // 0x7bec60: ArrayLoad: r8 = r3[r5]  ; Unknown_4
    //     0x7bec60: add             x16, x3, x5, lsl #2
    //     0x7bec64: ldur            w8, [x16, #0xf]
    // 0x7bec68: DecompressPointer r8
    //     0x7bec68: add             x8, x8, HEAP, lsl #32
    // 0x7bec6c: r9 = LoadInt32Instr(r8)
    //     0x7bec6c: sbfx            x9, x8, #1, #0x1f
    //     0x7bec70: tbz             w8, #0, #0x7bec78
    //     0x7bec74: ldur            x9, [x8, #7]
    // 0x7bec78: sub             x8, x9, #1
    // 0x7bec7c: r9 = LoadInt32Instr(r6)
    //     0x7bec7c: sbfx            x9, x6, #1, #0x1f
    //     0x7bec80: tbz             w6, #0, #0x7bec88
    //     0x7bec84: ldur            x9, [x6, #7]
    // 0x7bec88: sub             x6, x9, x8
    // 0x7bec8c: r0 = BoxInt64Instr(r6)
    //     0x7bec8c: sbfiz           x0, x6, #1, #0x1f
    //     0x7bec90: cmp             x6, x0, asr #1
    //     0x7bec94: b.eq            #0x7beca0
    //     0x7bec98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bec9c: stur            x6, [x0, #7]
    // 0x7beca0: mov             x1, x7
    // 0x7beca4: ArrayStore: r1[r5] = r0  ; List_4
    //     0x7beca4: add             x25, x1, x5, lsl #2
    //     0x7beca8: add             x25, x25, #0xf
    //     0x7becac: str             w0, [x25]
    //     0x7becb0: tbz             w0, #0, #0x7beccc
    //     0x7becb4: ldurb           w16, [x1, #-1]
    //     0x7becb8: ldurb           w17, [x0, #-1]
    //     0x7becbc: and             x16, x17, x16, lsr #2
    //     0x7becc0: tst             x16, HEAP, lsr #32
    //     0x7becc4: b.eq            #0x7beccc
    //     0x7becc8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7beccc: add             x0, x5, #1
    // 0x7becd0: mov             x5, x0
    // 0x7becd4: b               #0x7bec00
    // 0x7becd8: ldur            x1, [fp, #-8]
    // 0x7becdc: LoadField: r2 = r1->field_1f
    //     0x7becdc: ldur            x2, [x1, #0x1f]
    // 0x7bece0: add             x3, x2, #1
    // 0x7bece4: StoreField: r1->field_1f = r3
    //     0x7bece4: stur            x3, [x1, #0x1f]
    // 0x7bece8: r0 = Null
    //     0x7bece8: mov             x0, NULL
    // 0x7becec: LeaveFrame
    //     0x7becec: mov             SP, fp
    //     0x7becf0: ldp             fp, lr, [SP], #0x10
    // 0x7becf4: ret
    //     0x7becf4: ret             
    // 0x7becf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7becf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7becfc: b               #0x7be578
    // 0x7bed00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed04: b               #0x7be634
    // 0x7bed08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed0c: b               #0x7be708
    // 0x7bed10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed14: b               #0x7be7a4
    // 0x7bed18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed1c: b               #0x7be7bc
    // 0x7bed20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bed24: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bed28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed38: b               #0x7be958
    // 0x7bed3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7bed3c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7bed40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed4c: b               #0x7bea2c
    // 0x7bed50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed58: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bed5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bed60: b               #0x7bec0c
    // 0x7bed64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7bed68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7bed68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ IndexableSkipList(/* No info */) {
    // ** addr: 0x834eb0, size: 0x134
    // 0x834eb0: EnterFrame
    //     0x834eb0: stp             fp, lr, [SP, #-0x10]!
    //     0x834eb4: mov             fp, SP
    // 0x834eb8: AllocStack(0x20)
    //     0x834eb8: sub             SP, SP, #0x20
    // 0x834ebc: r0 = 1
    //     0x834ebc: movz            x0, #0x1
    // 0x834ec0: mov             x4, x1
    // 0x834ec4: stur            x1, [fp, #-0x10]
    // 0x834ec8: CheckStackOverflow
    //     0x834ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x834ecc: cmp             SP, x16
    //     0x834ed0: b.ls            #0x834fd4
    // 0x834ed4: ArrayStore: r4[0] = r0  ; List_8
    //     0x834ed4: stur            x0, [x4, #0x17]
    // 0x834ed8: StoreField: r4->field_1f = rZR
    //     0x834ed8: stur            xzr, [x4, #0x1f]
    // 0x834edc: LoadField: r0 = r4->field_7
    //     0x834edc: ldur            w0, [x4, #7]
    // 0x834ee0: DecompressPointer r0
    //     0x834ee0: add             x0, x0, HEAP, lsl #32
    // 0x834ee4: mov             x2, x0
    // 0x834ee8: stur            x0, [fp, #-8]
    // 0x834eec: r1 = Null
    //     0x834eec: mov             x1, NULL
    // 0x834ef0: r3 = <_Node<X0, X1>?>
    //     0x834ef0: ldr             x3, [PP, #0x7e50]  ; [pp+0x7e50] TypeArguments: <_Node<X0, X1>?>
    // 0x834ef4: r30 = InstantiateTypeArgumentsStub
    //     0x834ef4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x834ef8: LoadField: r30 = r30->field_7
    //     0x834ef8: ldur            lr, [lr, #7]
    // 0x834efc: blr             lr
    // 0x834f00: r1 = <int>
    //     0x834f00: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x834f04: r2 = 24
    //     0x834f04: movz            x2, #0x18
    // 0x834f08: stur            x0, [fp, #-0x18]
    // 0x834f0c: r0 = AllocateArray()
    //     0x834f0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x834f10: stur            x0, [fp, #-0x20]
    // 0x834f14: r1 = 0
    //     0x834f14: movz            x1, #0
    // 0x834f18: CheckStackOverflow
    //     0x834f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x834f1c: cmp             SP, x16
    //     0x834f20: b.ls            #0x834fdc
    // 0x834f24: cmp             x1, #0xc
    // 0x834f28: b.ge            #0x834f40
    // 0x834f2c: ArrayStore: r0[r1] = rZR  ; Unknown_4
    //     0x834f2c: add             x2, x0, x1, lsl #2
    //     0x834f30: stur            wzr, [x2, #0xf]
    // 0x834f34: add             x2, x1, #1
    // 0x834f38: mov             x1, x2
    // 0x834f3c: b               #0x834f18
    // 0x834f40: ldur            x2, [fp, #-0x10]
    // 0x834f44: ldur            x1, [fp, #-8]
    // 0x834f48: r0 = _Node()
    //     0x834f48: bl              #0x7bed6c  ; Allocate_NodeStub -> _Node<X0, X1> (size=0x1c)
    // 0x834f4c: ldur            x1, [fp, #-0x18]
    // 0x834f50: r2 = 24
    //     0x834f50: movz            x2, #0x18
    // 0x834f54: stur            x0, [fp, #-8]
    // 0x834f58: r0 = AllocateArray()
    //     0x834f58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x834f5c: mov             x1, x0
    // 0x834f60: ldur            x0, [fp, #-8]
    // 0x834f64: StoreField: r0->field_13 = r1
    //     0x834f64: stur            w1, [x0, #0x13]
    // 0x834f68: ldur            x1, [fp, #-0x20]
    // 0x834f6c: ArrayStore: r0[0] = r1  ; List_4
    //     0x834f6c: stur            w1, [x0, #0x17]
    // 0x834f70: ldur            x2, [fp, #-0x10]
    // 0x834f74: StoreField: r2->field_b = r0
    //     0x834f74: stur            w0, [x2, #0xb]
    //     0x834f78: ldurb           w16, [x2, #-1]
    //     0x834f7c: ldurb           w17, [x0, #-1]
    //     0x834f80: and             x16, x17, x16, lsr #2
    //     0x834f84: tst             x16, HEAP, lsr #32
    //     0x834f88: b.eq            #0x834f90
    //     0x834f8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x834f90: r0 = Closure: (dynamic, dynamic) => int from Function 'defaultKeyComparator': static.
    //     0x834f90: ldr             x0, [PP, #0x7e58]  ; [pp+0x7e58] Closure: (dynamic, dynamic) => int from Function 'defaultKeyComparator': static. (0x7e54fb08f6a4)
    // 0x834f94: StoreField: r2->field_13 = r0
    //     0x834f94: stur            w0, [x2, #0x13]
    // 0x834f98: r1 = Null
    //     0x834f98: mov             x1, NULL
    // 0x834f9c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x834f9c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x834fa0: r0 = Random()
    //     0x834fa0: bl              #0x834fe4  ; [dart:math] Random::Random
    // 0x834fa4: ldur            x1, [fp, #-0x10]
    // 0x834fa8: StoreField: r1->field_f = r0
    //     0x834fa8: stur            w0, [x1, #0xf]
    //     0x834fac: ldurb           w16, [x1, #-1]
    //     0x834fb0: ldurb           w17, [x0, #-1]
    //     0x834fb4: and             x16, x17, x16, lsr #2
    //     0x834fb8: tst             x16, HEAP, lsr #32
    //     0x834fbc: b.eq            #0x834fc4
    //     0x834fc0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x834fc4: r0 = Null
    //     0x834fc4: mov             x0, NULL
    // 0x834fc8: LeaveFrame
    //     0x834fc8: mov             SP, fp
    //     0x834fcc: ldp             fp, lr, [SP], #0x10
    // 0x834fd0: ret
    //     0x834fd0: ret             
    // 0x834fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x834fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x834fd8: b               #0x834ed4
    // 0x834fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x834fdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x834fe0: b               #0x834f24
  }
  _ clear(/* No info */) {
    // ** addr: 0x8c1148, size: 0x8c
    // 0x8c1148: EnterFrame
    //     0x8c1148: stp             fp, lr, [SP, #-0x10]!
    //     0x8c114c: mov             fp, SP
    // 0x8c1150: r2 = 1
    //     0x8c1150: movz            x2, #0x1
    // 0x8c1154: mov             x3, x1
    // 0x8c1158: ArrayStore: r3[0] = r2  ; List_8
    //     0x8c1158: stur            x2, [x3, #0x17]
    // 0x8c115c: LoadField: r4 = r3->field_b
    //     0x8c115c: ldur            w4, [x3, #0xb]
    // 0x8c1160: DecompressPointer r4
    //     0x8c1160: add             x4, x4, HEAP, lsl #32
    // 0x8c1164: LoadField: r5 = r4->field_13
    //     0x8c1164: ldur            w5, [x4, #0x13]
    // 0x8c1168: DecompressPointer r5
    //     0x8c1168: add             x5, x5, HEAP, lsl #32
    // 0x8c116c: LoadField: r4 = r5->field_b
    //     0x8c116c: ldur            w4, [x5, #0xb]
    // 0x8c1170: r6 = LoadInt32Instr(r4)
    //     0x8c1170: sbfx            x6, x4, #1, #0x1f
    // 0x8c1174: r4 = 0
    //     0x8c1174: movz            x4, #0
    // 0x8c1178: CheckStackOverflow
    //     0x8c1178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c117c: cmp             SP, x16
    //     0x8c1180: b.ls            #0x8c11c8
    // 0x8c1184: cmp             x4, #0xc
    // 0x8c1188: b.ge            #0x8c11b0
    // 0x8c118c: mov             x0, x6
    // 0x8c1190: mov             x1, x4
    // 0x8c1194: cmp             x1, x0
    // 0x8c1198: b.hs            #0x8c11d0
    // 0x8c119c: ArrayStore: r5[r4] = rNULL  ; Unknown_4
    //     0x8c119c: add             x1, x5, x4, lsl #2
    //     0x8c11a0: stur            NULL, [x1, #0xf]
    // 0x8c11a4: add             x0, x4, #1
    // 0x8c11a8: mov             x4, x0
    // 0x8c11ac: b               #0x8c1178
    // 0x8c11b0: ArrayStore: r3[0] = r2  ; List_8
    //     0x8c11b0: stur            x2, [x3, #0x17]
    // 0x8c11b4: StoreField: r3->field_1f = rZR
    //     0x8c11b4: stur            xzr, [x3, #0x1f]
    // 0x8c11b8: r0 = Null
    //     0x8c11b8: mov             x0, NULL
    // 0x8c11bc: LeaveFrame
    //     0x8c11bc: mov             SP, fp
    //     0x8c11c0: ldp             fp, lr, [SP], #0x10
    // 0x8c11c4: ret
    //     0x8c11c4: ret             
    // 0x8c11c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c11c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c11cc: b               #0x8c1184
    // 0x8c11d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8c11d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 7255, size: 0x10, field offset: 0xc
class _ValueIterable<C1X0, C1X1> extends Iterable<C1X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x888368, size: 0x6c
    // 0x888368: EnterFrame
    //     0x888368: stp             fp, lr, [SP, #-0x10]!
    //     0x88836c: mov             fp, SP
    // 0x888370: AllocStack(0x10)
    //     0x888370: sub             SP, SP, #0x10
    // 0x888374: SetupParameters(_ValueIterable<C1X0, C1X1> this /* r1 => r0, fp-0x8 */)
    //     0x888374: mov             x0, x1
    //     0x888378: stur            x1, [fp, #-8]
    // 0x88837c: LoadField: r2 = r0->field_7
    //     0x88837c: ldur            w2, [x0, #7]
    // 0x888380: DecompressPointer r2
    //     0x888380: add             x2, x2, HEAP, lsl #32
    // 0x888384: r1 = Null
    //     0x888384: mov             x1, NULL
    // 0x888388: r3 = <C1X0, C1X1, C1X1, C1X0, C1X1>
    //     0x888388: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b878] TypeArguments: <C1X0, C1X1, C1X1, C1X0, C1X1>
    //     0x88838c: ldr             x3, [x3, #0x878]
    // 0x888390: r0 = Null
    //     0x888390: mov             x0, NULL
    // 0x888394: cmp             x2, x0
    // 0x888398: b.eq            #0x8883a8
    // 0x88839c: r30 = InstantiateTypeArgumentsStub
    //     0x88839c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8883a0: LoadField: r30 = r30->field_7
    //     0x8883a0: ldur            lr, [lr, #7]
    // 0x8883a4: blr             lr
    // 0x8883a8: mov             x1, x0
    // 0x8883ac: ldur            x0, [fp, #-8]
    // 0x8883b0: LoadField: r2 = r0->field_b
    //     0x8883b0: ldur            w2, [x0, #0xb]
    // 0x8883b4: DecompressPointer r2
    //     0x8883b4: add             x2, x2, HEAP, lsl #32
    // 0x8883b8: stur            x2, [fp, #-0x10]
    // 0x8883bc: r0 = _ValueIterator()
    //     0x8883bc: bl              #0x8883d4  ; Allocate_ValueIteratorStub -> _ValueIterator<C3X0, C3X1> (size=0x10)
    // 0x8883c0: ldur            x1, [fp, #-0x10]
    // 0x8883c4: StoreField: r0->field_b = r1
    //     0x8883c4: stur            w1, [x0, #0xb]
    // 0x8883c8: LeaveFrame
    //     0x8883c8: mov             SP, fp
    //     0x8883cc: ldp             fp, lr, [SP], #0x10
    // 0x8883d0: ret
    //     0x8883d0: ret             
  }
}
