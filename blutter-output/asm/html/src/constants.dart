// lib: , url: package:html/src/constants.dart

// class id: 1049631, size: 0x8
class :: {

  static _ AsciiUpperToLower.toAsciiLowerCase(/* No info */) {
    // ** addr: 0x95701c, size: 0x74
    // 0x95701c: EnterFrame
    //     0x95701c: stp             fp, lr, [SP, #-0x10]!
    //     0x957020: mov             fp, SP
    // 0x957024: AllocStack(0x20)
    //     0x957024: sub             SP, SP, #0x20
    // 0x957028: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x957028: mov             x0, x1
    //     0x95702c: stur            x1, [fp, #-8]
    // 0x957030: CheckStackOverflow
    //     0x957030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x957034: cmp             SP, x16
    //     0x957038: b.ls            #0x957088
    // 0x95703c: r1 = <int>
    //     0x95703c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x957040: r0 = CodeUnits()
    //     0x957040: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x957044: mov             x1, x0
    // 0x957048: ldur            x0, [fp, #-8]
    // 0x95704c: StoreField: r1->field_b = r0
    //     0x95704c: stur            w0, [x1, #0xb]
    // 0x957050: r16 = <int>
    //     0x957050: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x957054: stp             x1, x16, [SP, #8]
    // 0x957058: r16 = Closure: (int) => int from Function 'AsciiUpperToLower|_asciiToLower': static.
    //     0x957058: add             x16, PP, #0x37, lsl #12  ; [pp+0x37380] Closure: (int) => int from Function 'AsciiUpperToLower|_asciiToLower': static. (0x7e54fb357090)
    //     0x95705c: ldr             x16, [x16, #0x380]
    // 0x957060: str             x16, [SP]
    // 0x957064: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x957064: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x957068: r0 = map()
    //     0x957068: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x95706c: mov             x1, x0
    // 0x957070: r2 = 0
    //     0x957070: movz            x2, #0
    // 0x957074: r3 = Null
    //     0x957074: mov             x3, NULL
    // 0x957078: r0 = createFromCharCodes()
    //     0x957078: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0x95707c: LeaveFrame
    //     0x95707c: mov             SP, fp
    //     0x957080: ldp             fp, lr, [SP], #0x10
    // 0x957084: ret
    //     0x957084: ret             
    // 0x957088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x957088: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95708c: b               #0x95703c
  }
  [closure] static int AsciiUpperToLower|_asciiToLower(dynamic, int) {
    // ** addr: 0x957090, size: 0x5c
    // 0x957090: ldr             x2, [SP]
    // 0x957094: r3 = LoadInt32Instr(r2)
    //     0x957094: sbfx            x3, x2, #1, #0x1f
    //     0x957098: tbz             w2, #0, #0x9570a0
    //     0x95709c: ldur            x3, [x2, #7]
    // 0x9570a0: cmp             x3, #0x41
    // 0x9570a4: b.lt            #0x9570c0
    // 0x9570a8: cmp             x3, #0x5a
    // 0x9570ac: b.gt            #0x9570c0
    // 0x9570b0: add             x2, x3, #0x61
    // 0x9570b4: sub             x4, x2, #0x41
    // 0x9570b8: mov             x2, x4
    // 0x9570bc: b               #0x9570c4
    // 0x9570c0: mov             x2, x3
    // 0x9570c4: r0 = BoxInt64Instr(r2)
    //     0x9570c4: sbfiz           x0, x2, #1, #0x1f
    //     0x9570c8: cmp             x2, x0, asr #1
    //     0x9570cc: b.eq            #0x9570e8
    //     0x9570d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9570d4: mov             fp, SP
    //     0x9570d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9570dc: mov             SP, fp
    //     0x9570e0: ldp             fp, lr, [SP], #0x10
    //     0x9570e4: stur            x2, [x0, #7]
    // 0x9570e8: ret
    //     0x9570e8: ret             
  }
  static _ isWhitespace(/* No info */) {
    // ** addr: 0x957abc, size: 0xb4
    // 0x957abc: mov             x2, x1
    // 0x957ac0: cmp             w2, NULL
    // 0x957ac4: b.ne            #0x957ad0
    // 0x957ac8: r0 = false
    //     0x957ac8: add             x0, NULL, #0x30  ; false
    // 0x957acc: ret
    //     0x957acc: ret             
    // 0x957ad0: LoadField: r3 = r2->field_7
    //     0x957ad0: ldur            w3, [x2, #7]
    // 0x957ad4: r0 = LoadInt32Instr(r3)
    //     0x957ad4: sbfx            x0, x3, #1, #0x1f
    // 0x957ad8: r1 = 0
    //     0x957ad8: movz            x1, #0
    // 0x957adc: cmp             x1, x0
    // 0x957ae0: b.hs            #0x957b64
    // 0x957ae4: r1 = LoadClassIdInstr(r2)
    //     0x957ae4: ldur            x1, [x2, #-1]
    //     0x957ae8: ubfx            x1, x1, #0xc, #0x14
    // 0x957aec: lsl             x1, x1, #1
    // 0x957af0: cmp             w1, #0xbc
    // 0x957af4: b.ne            #0x957b00
    // 0x957af8: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x957af8: ldrb            w1, [x2, #0xf]
    // 0x957afc: b               #0x957b04
    // 0x957b00: ldurh           w1, [x2, #0xf]
    // 0x957b04: cmp             x1, #0xc
    // 0x957b08: b.gt            #0x957b38
    // 0x957b0c: cmp             x1, #0xa
    // 0x957b10: b.gt            #0x957b2c
    // 0x957b14: cmp             x1, #9
    // 0x957b18: b.gt            #0x957b54
    // 0x957b1c: lsl             x2, x1, #1
    // 0x957b20: cmp             w2, #0x12
    // 0x957b24: b.ne            #0x957b5c
    // 0x957b28: b               #0x957b54
    // 0x957b2c: cmp             x1, #0xc
    // 0x957b30: b.lt            #0x957b5c
    // 0x957b34: b               #0x957b54
    // 0x957b38: cmp             x1, #0xd
    // 0x957b3c: b.le            #0x957b54
    // 0x957b40: cmp             x1, #0x20
    // 0x957b44: b.lt            #0x957b5c
    // 0x957b48: lsl             x2, x1, #1
    // 0x957b4c: cmp             w2, #0x40
    // 0x957b50: b.ne            #0x957b5c
    // 0x957b54: r0 = true
    //     0x957b54: add             x0, NULL, #0x20  ; true
    // 0x957b58: b               #0x957b60
    // 0x957b5c: r0 = false
    //     0x957b5c: add             x0, NULL, #0x30  ; false
    // 0x957b60: ret
    //     0x957b60: ret             
    // 0x957b64: EnterFrame
    //     0x957b64: stp             fp, lr, [SP, #-0x10]!
    //     0x957b68: mov             fp, SP
    // 0x957b6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x957b6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ isLetterOrDigit(/* No info */) {
    // ** addr: 0x95afa8, size: 0xf0
    // 0x95afa8: EnterFrame
    //     0x95afa8: stp             fp, lr, [SP, #-0x10]!
    //     0x95afac: mov             fp, SP
    // 0x95afb0: mov             x2, x1
    // 0x95afb4: cmp             w2, NULL
    // 0x95afb8: b.eq            #0x95b018
    // 0x95afbc: LoadField: r3 = r2->field_7
    //     0x95afbc: ldur            w3, [x2, #7]
    // 0x95afc0: r0 = LoadInt32Instr(r3)
    //     0x95afc0: sbfx            x0, x3, #1, #0x1f
    // 0x95afc4: r1 = 0
    //     0x95afc4: movz            x1, #0
    // 0x95afc8: cmp             x1, x0
    // 0x95afcc: b.hs            #0x95b090
    // 0x95afd0: r3 = LoadClassIdInstr(r2)
    //     0x95afd0: ldur            x3, [x2, #-1]
    //     0x95afd4: ubfx            x3, x3, #0xc, #0x14
    // 0x95afd8: lsl             x3, x3, #1
    // 0x95afdc: cmp             w3, #0xbc
    // 0x95afe0: b.ne            #0x95afec
    // 0x95afe4: ArrayLoad: r3 = r2[-8]  ; TypedUnsigned_1
    //     0x95afe4: ldrb            w3, [x2, #0xf]
    // 0x95afe8: b               #0x95aff0
    // 0x95afec: ldurh           w3, [x2, #0xf]
    // 0x95aff0: cmp             x3, #0x61
    // 0x95aff4: b.lt            #0x95b000
    // 0x95aff8: cmp             x3, #0x7a
    // 0x95affc: b.le            #0x95b010
    // 0x95b000: cmp             x3, #0x41
    // 0x95b004: b.lt            #0x95b018
    // 0x95b008: cmp             x3, #0x5a
    // 0x95b00c: b.gt            #0x95b018
    // 0x95b010: r0 = true
    //     0x95b010: add             x0, NULL, #0x20  ; true
    // 0x95b014: b               #0x95b084
    // 0x95b018: cmp             w2, NULL
    // 0x95b01c: b.ne            #0x95b028
    // 0x95b020: r1 = false
    //     0x95b020: add             x1, NULL, #0x30  ; false
    // 0x95b024: b               #0x95b080
    // 0x95b028: LoadField: r3 = r2->field_7
    //     0x95b028: ldur            w3, [x2, #7]
    // 0x95b02c: r0 = LoadInt32Instr(r3)
    //     0x95b02c: sbfx            x0, x3, #1, #0x1f
    // 0x95b030: r1 = 0
    //     0x95b030: movz            x1, #0
    // 0x95b034: cmp             x1, x0
    // 0x95b038: b.hs            #0x95b094
    // 0x95b03c: r1 = LoadClassIdInstr(r2)
    //     0x95b03c: ldur            x1, [x2, #-1]
    //     0x95b040: ubfx            x1, x1, #0xc, #0x14
    // 0x95b044: lsl             x1, x1, #1
    // 0x95b048: cmp             w1, #0xbc
    // 0x95b04c: b.ne            #0x95b058
    // 0x95b050: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x95b050: ldrb            w1, [x2, #0xf]
    // 0x95b054: b               #0x95b05c
    // 0x95b058: ldurh           w1, [x2, #0xf]
    // 0x95b05c: cmp             x1, #0x30
    // 0x95b060: b.lt            #0x95b07c
    // 0x95b064: cmp             x1, #0x3a
    // 0x95b068: r16 = true
    //     0x95b068: add             x16, NULL, #0x20  ; true
    // 0x95b06c: r17 = false
    //     0x95b06c: add             x17, NULL, #0x30  ; false
    // 0x95b070: csel            x2, x16, x17, lt
    // 0x95b074: mov             x1, x2
    // 0x95b078: b               #0x95b080
    // 0x95b07c: r1 = false
    //     0x95b07c: add             x1, NULL, #0x30  ; false
    // 0x95b080: mov             x0, x1
    // 0x95b084: LeaveFrame
    //     0x95b084: mov             SP, fp
    //     0x95b088: ldp             fp, lr, [SP], #0x10
    // 0x95b08c: ret
    //     0x95b08c: ret             
    // 0x95b090: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b090: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b094: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b094: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static bool isDigit(dynamic, String?) {
    // ** addr: 0x95b564, size: 0x80
    // 0x95b564: ldr             x2, [SP]
    // 0x95b568: cmp             w2, NULL
    // 0x95b56c: b.ne            #0x95b578
    // 0x95b570: r0 = false
    //     0x95b570: add             x0, NULL, #0x30  ; false
    // 0x95b574: b               #0x95b5d4
    // 0x95b578: LoadField: r3 = r2->field_7
    //     0x95b578: ldur            w3, [x2, #7]
    // 0x95b57c: r0 = LoadInt32Instr(r3)
    //     0x95b57c: sbfx            x0, x3, #1, #0x1f
    // 0x95b580: r1 = 0
    //     0x95b580: movz            x1, #0
    // 0x95b584: cmp             x1, x0
    // 0x95b588: b.hs            #0x95b5d8
    // 0x95b58c: r1 = LoadClassIdInstr(r2)
    //     0x95b58c: ldur            x1, [x2, #-1]
    //     0x95b590: ubfx            x1, x1, #0xc, #0x14
    // 0x95b594: lsl             x1, x1, #1
    // 0x95b598: cmp             w1, #0xbc
    // 0x95b59c: b.ne            #0x95b5a8
    // 0x95b5a0: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x95b5a0: ldrb            w1, [x2, #0xf]
    // 0x95b5a4: b               #0x95b5ac
    // 0x95b5a8: ldurh           w1, [x2, #0xf]
    // 0x95b5ac: cmp             x1, #0x30
    // 0x95b5b0: b.lt            #0x95b5cc
    // 0x95b5b4: cmp             x1, #0x3a
    // 0x95b5b8: r16 = true
    //     0x95b5b8: add             x16, NULL, #0x20  ; true
    // 0x95b5bc: r17 = false
    //     0x95b5bc: add             x17, NULL, #0x30  ; false
    // 0x95b5c0: csel            x2, x16, x17, lt
    // 0x95b5c4: mov             x1, x2
    // 0x95b5c8: b               #0x95b5d0
    // 0x95b5cc: r1 = false
    //     0x95b5cc: add             x1, NULL, #0x30  ; false
    // 0x95b5d0: mov             x0, x1
    // 0x95b5d4: ret
    //     0x95b5d4: ret             
    // 0x95b5d8: EnterFrame
    //     0x95b5d8: stp             fp, lr, [SP, #-0x10]!
    //     0x95b5dc: mov             fp, SP
    // 0x95b5e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static bool isHexDigit(dynamic, String?) {
    // ** addr: 0x95b5e4, size: 0x30
    // 0x95b5e4: EnterFrame
    //     0x95b5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x95b5e8: mov             fp, SP
    // 0x95b5ec: CheckStackOverflow
    //     0x95b5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95b5f0: cmp             SP, x16
    //     0x95b5f4: b.ls            #0x95b60c
    // 0x95b5f8: ldr             x1, [fp, #0x10]
    // 0x95b5fc: r0 = isHexDigit()
    //     0x95b5fc: bl              #0x95b614  ; [package:html/src/constants.dart] ::isHexDigit
    // 0x95b600: LeaveFrame
    //     0x95b600: mov             SP, fp
    //     0x95b604: ldp             fp, lr, [SP], #0x10
    // 0x95b608: ret
    //     0x95b608: ret             
    // 0x95b60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b60c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b610: b               #0x95b5f8
  }
  static _ isHexDigit(/* No info */) {
    // ** addr: 0x95b614, size: 0xf8
    // 0x95b614: mov             x2, x1
    // 0x95b618: cmp             w2, NULL
    // 0x95b61c: b.ne            #0x95b628
    // 0x95b620: r0 = false
    //     0x95b620: add             x0, NULL, #0x30  ; false
    // 0x95b624: ret
    //     0x95b624: ret             
    // 0x95b628: LoadField: r3 = r2->field_7
    //     0x95b628: ldur            w3, [x2, #7]
    // 0x95b62c: r0 = LoadInt32Instr(r3)
    //     0x95b62c: sbfx            x0, x3, #1, #0x1f
    // 0x95b630: r1 = 0
    //     0x95b630: movz            x1, #0
    // 0x95b634: cmp             x1, x0
    // 0x95b638: b.hs            #0x95b700
    // 0x95b63c: r1 = LoadClassIdInstr(r2)
    //     0x95b63c: ldur            x1, [x2, #-1]
    //     0x95b640: ubfx            x1, x1, #0xc, #0x14
    // 0x95b644: lsl             x1, x1, #1
    // 0x95b648: cmp             w1, #0xbc
    // 0x95b64c: b.ne            #0x95b658
    // 0x95b650: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x95b650: ldrb            w1, [x2, #0xf]
    // 0x95b654: b               #0x95b65c
    // 0x95b658: ldurh           w1, [x2, #0xf]
    // 0x95b65c: cmp             x1, #0x41
    // 0x95b660: b.gt            #0x95b6b0
    // 0x95b664: cmp             x1, #0x35
    // 0x95b668: b.gt            #0x95b694
    // 0x95b66c: cmp             x1, #0x32
    // 0x95b670: b.gt            #0x95b6f0
    // 0x95b674: cmp             x1, #0x31
    // 0x95b678: b.gt            #0x95b6f0
    // 0x95b67c: cmp             x1, #0x30
    // 0x95b680: b.gt            #0x95b6f0
    // 0x95b684: lsl             x2, x1, #1
    // 0x95b688: cmp             w2, #0x60
    // 0x95b68c: b.ne            #0x95b6f8
    // 0x95b690: b               #0x95b6f0
    // 0x95b694: cmp             x1, #0x38
    // 0x95b698: b.le            #0x95b6f0
    // 0x95b69c: cmp             x1, #0x39
    // 0x95b6a0: b.le            #0x95b6f0
    // 0x95b6a4: cmp             x1, #0x41
    // 0x95b6a8: b.lt            #0x95b6f8
    // 0x95b6ac: b               #0x95b6f0
    // 0x95b6b0: cmp             x1, #0x61
    // 0x95b6b4: b.gt            #0x95b6d4
    // 0x95b6b8: cmp             x1, #0x44
    // 0x95b6bc: b.le            #0x95b6f0
    // 0x95b6c0: cmp             x1, #0x46
    // 0x95b6c4: b.le            #0x95b6f0
    // 0x95b6c8: cmp             x1, #0x61
    // 0x95b6cc: b.lt            #0x95b6f8
    // 0x95b6d0: b               #0x95b6f0
    // 0x95b6d4: cmp             x1, #0x64
    // 0x95b6d8: b.le            #0x95b6f0
    // 0x95b6dc: cmp             x1, #0x65
    // 0x95b6e0: b.le            #0x95b6f0
    // 0x95b6e4: lsl             x2, x1, #1
    // 0x95b6e8: cmp             w2, #0xcc
    // 0x95b6ec: b.ne            #0x95b6f8
    // 0x95b6f0: r0 = true
    //     0x95b6f0: add             x0, NULL, #0x20  ; true
    // 0x95b6f4: ret
    //     0x95b6f4: ret             
    // 0x95b6f8: r0 = false
    //     0x95b6f8: add             x0, NULL, #0x30  ; false
    // 0x95b6fc: ret
    //     0x95b6fc: ret             
    // 0x95b700: EnterFrame
    //     0x95b700: stp             fp, lr, [SP, #-0x10]!
    //     0x95b704: mov             fp, SP
    // 0x95b708: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b708: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 1543, size: 0x8, field offset: 0x8
abstract class Namespaces extends Object {

  static _ getPrefix(/* No info */) {
    // ** addr: 0xc2b474, size: 0x138
    // 0xc2b474: EnterFrame
    //     0xc2b474: stp             fp, lr, [SP, #-0x10]!
    //     0xc2b478: mov             fp, SP
    // 0xc2b47c: AllocStack(0x18)
    //     0xc2b47c: sub             SP, SP, #0x18
    // 0xc2b480: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xc2b480: stur            x1, [fp, #-8]
    // 0xc2b484: CheckStackOverflow
    //     0xc2b484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2b488: cmp             SP, x16
    //     0xc2b48c: b.ls            #0xc2b5a4
    // 0xc2b490: r16 = "http://www.w3.org/1999/xhtml"
    //     0xc2b490: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f78] "http://www.w3.org/1999/xhtml"
    //     0xc2b494: ldr             x16, [x16, #0xf78]
    // 0xc2b498: stp             x1, x16, [SP]
    // 0xc2b49c: r0 = ==()
    //     0xc2b49c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b4a0: tbnz            w0, #4, #0xc2b4b8
    // 0xc2b4a4: r0 = "html"
    //     0xc2b4a4: add             x0, PP, #0x42, lsl #12  ; [pp+0x42b60] "html"
    //     0xc2b4a8: ldr             x0, [x0, #0xb60]
    // 0xc2b4ac: LeaveFrame
    //     0xc2b4ac: mov             SP, fp
    //     0xc2b4b0: ldp             fp, lr, [SP], #0x10
    // 0xc2b4b4: ret
    //     0xc2b4b4: ret             
    // 0xc2b4b8: r16 = "http://www.w3.org/1998/Math/MathML"
    //     0xc2b4b8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37358] "http://www.w3.org/1998/Math/MathML"
    //     0xc2b4bc: ldr             x16, [x16, #0x358]
    // 0xc2b4c0: ldur            lr, [fp, #-8]
    // 0xc2b4c4: stp             lr, x16, [SP]
    // 0xc2b4c8: r0 = ==()
    //     0xc2b4c8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b4cc: tbnz            w0, #4, #0xc2b4e4
    // 0xc2b4d0: r0 = "math"
    //     0xc2b4d0: add             x0, PP, #0x42, lsl #12  ; [pp+0x42c38] "math"
    //     0xc2b4d4: ldr             x0, [x0, #0xc38]
    // 0xc2b4d8: LeaveFrame
    //     0xc2b4d8: mov             SP, fp
    //     0xc2b4dc: ldp             fp, lr, [SP], #0x10
    // 0xc2b4e0: ret
    //     0xc2b4e0: ret             
    // 0xc2b4e4: r16 = "http://www.w3.org/2000/svg"
    //     0xc2b4e4: add             x16, PP, #0x42, lsl #12  ; [pp+0x42a40] "http://www.w3.org/2000/svg"
    //     0xc2b4e8: ldr             x16, [x16, #0xa40]
    // 0xc2b4ec: ldur            lr, [fp, #-8]
    // 0xc2b4f0: stp             lr, x16, [SP]
    // 0xc2b4f4: r0 = ==()
    //     0xc2b4f4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b4f8: tbnz            w0, #4, #0xc2b510
    // 0xc2b4fc: r0 = "svg"
    //     0xc2b4fc: add             x0, PP, #0x26, lsl #12  ; [pp+0x26470] "svg"
    //     0xc2b500: ldr             x0, [x0, #0x470]
    // 0xc2b504: LeaveFrame
    //     0xc2b504: mov             SP, fp
    //     0xc2b508: ldp             fp, lr, [SP], #0x10
    // 0xc2b50c: ret
    //     0xc2b50c: ret             
    // 0xc2b510: r16 = "http://www.w3.org/1999/xlink"
    //     0xc2b510: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "http://www.w3.org/1999/xlink"
    //     0xc2b514: ldr             x16, [x16, #0xaf8]
    // 0xc2b518: ldur            lr, [fp, #-8]
    // 0xc2b51c: stp             lr, x16, [SP]
    // 0xc2b520: r0 = ==()
    //     0xc2b520: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b524: tbnz            w0, #4, #0xc2b53c
    // 0xc2b528: r0 = "xlink"
    //     0xc2b528: add             x0, PP, #0x42, lsl #12  ; [pp+0x42c40] "xlink"
    //     0xc2b52c: ldr             x0, [x0, #0xc40]
    // 0xc2b530: LeaveFrame
    //     0xc2b530: mov             SP, fp
    //     0xc2b534: ldp             fp, lr, [SP], #0x10
    // 0xc2b538: ret
    //     0xc2b538: ret             
    // 0xc2b53c: r16 = "http://www.w3.org/XML/1998/namespace"
    //     0xc2b53c: add             x16, PP, #0x42, lsl #12  ; [pp+0x42c48] "http://www.w3.org/XML/1998/namespace"
    //     0xc2b540: ldr             x16, [x16, #0xc48]
    // 0xc2b544: ldur            lr, [fp, #-8]
    // 0xc2b548: stp             lr, x16, [SP]
    // 0xc2b54c: r0 = ==()
    //     0xc2b54c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b550: tbnz            w0, #4, #0xc2b568
    // 0xc2b554: r0 = "xml"
    //     0xc2b554: add             x0, PP, #0x42, lsl #12  ; [pp+0x42c50] "xml"
    //     0xc2b558: ldr             x0, [x0, #0xc50]
    // 0xc2b55c: LeaveFrame
    //     0xc2b55c: mov             SP, fp
    //     0xc2b560: ldp             fp, lr, [SP], #0x10
    // 0xc2b564: ret
    //     0xc2b564: ret             
    // 0xc2b568: r16 = "http://www.w3.org/2000/xmlns/"
    //     0xc2b568: add             x16, PP, #0x42, lsl #12  ; [pp+0x42c58] "http://www.w3.org/2000/xmlns/"
    //     0xc2b56c: ldr             x16, [x16, #0xc58]
    // 0xc2b570: ldur            lr, [fp, #-8]
    // 0xc2b574: stp             lr, x16, [SP]
    // 0xc2b578: r0 = ==()
    //     0xc2b578: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xc2b57c: tbnz            w0, #4, #0xc2b594
    // 0xc2b580: r0 = "xmlns"
    //     0xc2b580: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e3e8] "xmlns"
    //     0xc2b584: ldr             x0, [x0, #0x3e8]
    // 0xc2b588: LeaveFrame
    //     0xc2b588: mov             SP, fp
    //     0xc2b58c: ldp             fp, lr, [SP], #0x10
    // 0xc2b590: ret
    //     0xc2b590: ret             
    // 0xc2b594: r0 = Null
    //     0xc2b594: mov             x0, NULL
    // 0xc2b598: LeaveFrame
    //     0xc2b598: mov             SP, fp
    //     0xc2b59c: ldp             fp, lr, [SP], #0x10
    // 0xc2b5a0: ret
    //     0xc2b5a0: ret             
    // 0xc2b5a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2b5a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2b5a8: b               #0xc2b490
  }
}
