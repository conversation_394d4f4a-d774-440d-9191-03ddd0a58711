// lib: , url: package:image/src/formats/webp/vp8l_transform.dart

// class id: 1049760, size: 0x8
class :: {
}

// class id: 1376, size: 0xc, field offset: 0x8
class _VP8LMultipliers extends Object {

  _ transformColor(/* No info */) {
    // ** addr: 0xcb3964, size: 0x17c
    // 0xcb3964: EnterFrame
    //     0xcb3964: stp             fp, lr, [SP, #-0x10]!
    //     0xcb3968: mov             fp, SP
    // 0xcb396c: AllocStack(0x38)
    //     0xcb396c: sub             SP, SP, #0x38
    // 0xcb3970: r4 = 255
    //     0xcb3970: movz            x4, #0xff
    // 0xcb3974: mov             x6, x1
    // 0xcb3978: mov             x5, x2
    // 0xcb397c: stur            x1, [fp, #-0x30]
    // 0xcb3980: stur            x2, [fp, #-0x38]
    // 0xcb3984: CheckStackOverflow
    //     0xcb3984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3988: cmp             SP, x16
    //     0xcb398c: b.ls            #0xcb3acc
    // 0xcb3990: asr             x0, x5, #8
    // 0xcb3994: ubfx            x0, x0, #0, #0x20
    // 0xcb3998: and             x7, x0, x4
    // 0xcb399c: stur            x7, [fp, #-0x28]
    // 0xcb39a0: asr             x0, x5, #0x10
    // 0xcb39a4: ubfx            x0, x0, #0, #0x20
    // 0xcb39a8: and             x8, x0, x4
    // 0xcb39ac: stur            x8, [fp, #-0x20]
    // 0xcb39b0: mov             x0, x5
    // 0xcb39b4: ubfx            x0, x0, #0, #0x20
    // 0xcb39b8: and             x9, x0, x4
    // 0xcb39bc: stur            x9, [fp, #-0x18]
    // 0xcb39c0: LoadField: r10 = r6->field_7
    //     0xcb39c0: ldur            w10, [x6, #7]
    // 0xcb39c4: DecompressPointer r10
    //     0xcb39c4: add             x10, x10, HEAP, lsl #32
    // 0xcb39c8: stur            x10, [fp, #-0x10]
    // 0xcb39cc: LoadField: r0 = r10->field_13
    //     0xcb39cc: ldur            w0, [x10, #0x13]
    // 0xcb39d0: r11 = LoadInt32Instr(r0)
    //     0xcb39d0: sbfx            x11, x0, #1, #0x1f
    // 0xcb39d4: mov             x0, x11
    // 0xcb39d8: stur            x11, [fp, #-8]
    // 0xcb39dc: r1 = 0
    //     0xcb39dc: movz            x1, #0
    // 0xcb39e0: cmp             x1, x0
    // 0xcb39e4: b.hs            #0xcb3ad4
    // 0xcb39e8: ArrayLoad: r2 = r10[0]  ; List_1
    //     0xcb39e8: ldrb            w2, [x10, #0x17]
    // 0xcb39ec: mov             x3, x7
    // 0xcb39f0: ubfx            x3, x3, #0, #0x20
    // 0xcb39f4: mov             x1, x6
    // 0xcb39f8: r0 = colorTransformDelta()
    //     0xcb39f8: bl              #0xcb3ae0  ; [package:image/src/formats/webp/vp8l_transform.dart] _VP8LMultipliers::colorTransformDelta
    // 0xcb39fc: ubfx            x0, x0, #0, #0x20
    // 0xcb3a00: ldur            x1, [fp, #-0x20]
    // 0xcb3a04: add             w2, w1, w0
    // 0xcb3a08: r4 = 255
    //     0xcb3a08: movz            x4, #0xff
    // 0xcb3a0c: and             x5, x2, x4
    // 0xcb3a10: ldur            x0, [fp, #-8]
    // 0xcb3a14: stur            x5, [fp, #-0x20]
    // 0xcb3a18: r1 = 1
    //     0xcb3a18: movz            x1, #0x1
    // 0xcb3a1c: cmp             x1, x0
    // 0xcb3a20: b.hs            #0xcb3ad8
    // 0xcb3a24: ldur            x0, [fp, #-0x10]
    // 0xcb3a28: ArrayLoad: r2 = r0[1]  ; TypedUnsigned_1
    //     0xcb3a28: ldrb            w2, [x0, #0x18]
    // 0xcb3a2c: ldur            x1, [fp, #-0x28]
    // 0xcb3a30: ubfx            x1, x1, #0, #0x20
    // 0xcb3a34: mov             x3, x1
    // 0xcb3a38: ldur            x1, [fp, #-0x30]
    // 0xcb3a3c: r0 = colorTransformDelta()
    //     0xcb3a3c: bl              #0xcb3ae0  ; [package:image/src/formats/webp/vp8l_transform.dart] _VP8LMultipliers::colorTransformDelta
    // 0xcb3a40: ubfx            x0, x0, #0, #0x20
    // 0xcb3a44: ldur            x1, [fp, #-0x18]
    // 0xcb3a48: add             w4, w1, w0
    // 0xcb3a4c: ldur            x0, [fp, #-8]
    // 0xcb3a50: stur            x4, [fp, #-0x28]
    // 0xcb3a54: r1 = 2
    //     0xcb3a54: movz            x1, #0x2
    // 0xcb3a58: cmp             x1, x0
    // 0xcb3a5c: b.hs            #0xcb3adc
    // 0xcb3a60: ldur            x0, [fp, #-0x10]
    // 0xcb3a64: ArrayLoad: r2 = r0[2]  ; TypedUnsigned_1
    //     0xcb3a64: ldrb            w2, [x0, #0x19]
    // 0xcb3a68: ldur            x0, [fp, #-0x20]
    // 0xcb3a6c: ubfx            x0, x0, #0, #0x20
    // 0xcb3a70: ldur            x1, [fp, #-0x30]
    // 0xcb3a74: mov             x3, x0
    // 0xcb3a78: r0 = colorTransformDelta()
    //     0xcb3a78: bl              #0xcb3ae0  ; [package:image/src/formats/webp/vp8l_transform.dart] _VP8LMultipliers::colorTransformDelta
    // 0xcb3a7c: ubfx            x0, x0, #0, #0x20
    // 0xcb3a80: ldur            x1, [fp, #-0x28]
    // 0xcb3a84: add             w2, w1, w0
    // 0xcb3a88: r1 = 255
    //     0xcb3a88: movz            x1, #0xff
    // 0xcb3a8c: and             x3, x2, x1
    // 0xcb3a90: ldur            x1, [fp, #-0x38]
    // 0xcb3a94: ubfx            x1, x1, #0, #0x20
    // 0xcb3a98: r2 = 4278255360
    //     0xcb3a98: movz            x2, #0xff00
    //     0xcb3a9c: movk            x2, #0xff00, lsl #16
    // 0xcb3aa0: and             x4, x1, x2
    // 0xcb3aa4: ldur            x1, [fp, #-0x20]
    // 0xcb3aa8: lsl             w2, w1, #0x10
    // 0xcb3aac: ubfx            x4, x4, #0, #0x20
    // 0xcb3ab0: ubfx            x2, x2, #0, #0x20
    // 0xcb3ab4: orr             x1, x4, x2
    // 0xcb3ab8: ubfx            x3, x3, #0, #0x20
    // 0xcb3abc: orr             x0, x1, x3
    // 0xcb3ac0: LeaveFrame
    //     0xcb3ac0: mov             SP, fp
    //     0xcb3ac4: ldp             fp, lr, [SP], #0x10
    // 0xcb3ac8: ret
    //     0xcb3ac8: ret             
    // 0xcb3acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb3acc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3ad0: b               #0xcb3990
    // 0xcb3ad4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3ad4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3ad8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3ad8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3adc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ colorTransformDelta(/* No info */) {
    // ** addr: 0xcb3ae0, size: 0x6c
    // 0xcb3ae0: EnterFrame
    //     0xcb3ae0: stp             fp, lr, [SP, #-0x10]!
    //     0xcb3ae4: mov             fp, SP
    // 0xcb3ae8: AllocStack(0x8)
    //     0xcb3ae8: sub             SP, SP, #8
    // 0xcb3aec: SetupParameters(_VP8LMultipliers this /* r1 => r2 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r0, fp-0x8 */)
    //     0xcb3aec: mov             x16, x2
    //     0xcb3af0: mov             x2, x1
    //     0xcb3af4: mov             x1, x16
    //     0xcb3af8: mov             x0, x3
    //     0xcb3afc: stur            x3, [fp, #-8]
    // 0xcb3b00: CheckStackOverflow
    //     0xcb3b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3b04: cmp             SP, x16
    //     0xcb3b08: b.ls            #0xcb3b44
    // 0xcb3b0c: r0 = uint8ToInt8()
    //     0xcb3b0c: bl              #0xc993b0  ; [package:image/src/util/bit_utils.dart] ::uint8ToInt8
    // 0xcb3b10: ldur            x1, [fp, #-8]
    // 0xcb3b14: stur            x0, [fp, #-8]
    // 0xcb3b18: r0 = uint8ToInt8()
    //     0xcb3b18: bl              #0xc993b0  ; [package:image/src/util/bit_utils.dart] ::uint8ToInt8
    // 0xcb3b1c: mov             x1, x0
    // 0xcb3b20: ldur            x0, [fp, #-8]
    // 0xcb3b24: mul             x2, x0, x1
    // 0xcb3b28: mov             x1, x2
    // 0xcb3b2c: r0 = int32ToUint32()
    //     0xcb3b2c: bl              #0xcb3b4c  ; [package:image/src/util/bit_utils.dart] ::int32ToUint32
    // 0xcb3b30: asr             x1, x0, #5
    // 0xcb3b34: mov             x0, x1
    // 0xcb3b38: LeaveFrame
    //     0xcb3b38: mov             SP, fp
    //     0xcb3b3c: ldp             fp, lr, [SP], #0x10
    // 0xcb3b40: ret
    //     0xcb3b40: ret             
    // 0xcb3b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb3b44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3b48: b               #0xcb3b0c
  }
}

// class id: 1377, size: 0x28, field offset: 0x8
class VP8LTransform extends Object {

  static late final List<(dynamic, Uint32List, int, int) => int> _predictors; // offset: 0x1458

  _ colorIndexInverseTransformAlpha(/* No info */) {
    // ** addr: 0xcb2658, size: 0x488
    // 0xcb2658: EnterFrame
    //     0xcb2658: stp             fp, lr, [SP, #-0x10]!
    //     0xcb265c: mov             fp, SP
    // 0xcb2660: AllocStack(0x70)
    //     0xcb2660: sub             SP, SP, #0x70
    // 0xcb2664: r0 = 8
    //     0xcb2664: movz            x0, #0x8
    // 0xcb2668: stur            x3, [fp, #-0x40]
    // 0xcb266c: stur            x5, [fp, #-0x48]
    // 0xcb2670: stur            x6, [fp, #-0x50]
    // 0xcb2674: CheckStackOverflow
    //     0xcb2674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb2678: cmp             SP, x16
    //     0xcb267c: b.ls            #0xcb2a3c
    // 0xcb2680: LoadField: r4 = r1->field_1f
    //     0xcb2680: ldur            x4, [x1, #0x1f]
    // 0xcb2684: cmp             x4, #0x3f
    // 0xcb2688: b.hi            #0xcb2a44
    // 0xcb268c: asr             x7, x0, x4
    // 0xcb2690: stur            x7, [fp, #-0x38]
    // 0xcb2694: LoadField: r8 = r1->field_b
    //     0xcb2694: ldur            x8, [x1, #0xb]
    // 0xcb2698: stur            x8, [fp, #-0x30]
    // 0xcb269c: LoadField: r9 = r1->field_1b
    //     0xcb269c: ldur            w9, [x1, #0x1b]
    // 0xcb26a0: DecompressPointer r9
    //     0xcb26a0: add             x9, x9, HEAP, lsl #32
    // 0xcb26a4: stur            x9, [fp, #-0x28]
    // 0xcb26a8: cmp             x7, #8
    // 0xcb26ac: b.ge            #0xcb28b4
    // 0xcb26b0: r0 = 1
    //     0xcb26b0: movz            x0, #0x1
    // 0xcb26b4: cmp             x4, #0x3f
    // 0xcb26b8: b.hi            #0xcb2a78
    // 0xcb26bc: lsl             x1, x0, x4
    // 0xcb26c0: sub             x4, x1, #1
    // 0xcb26c4: stur            x4, [fp, #-0x20]
    // 0xcb26c8: lsl             x1, x0, x7
    // 0xcb26cc: sub             x10, x1, #1
    // 0xcb26d0: stur            x10, [fp, #-0x18]
    // 0xcb26d4: stur            x2, [fp, #-0x10]
    // 0xcb26d8: CheckStackOverflow
    //     0xcb26d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb26dc: cmp             SP, x16
    //     0xcb26e0: b.ls            #0xcb2ab0
    // 0xcb26e4: cmp             x2, x3
    // 0xcb26e8: b.ge            #0xcb2a2c
    // 0xcb26ec: r0 = 0
    //     0xcb26ec: movz            x0, #0
    // 0xcb26f0: r11 = 0
    //     0xcb26f0: movz            x11, #0
    // 0xcb26f4: stur            x11, [fp, #-8]
    // 0xcb26f8: CheckStackOverflow
    //     0xcb26f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb26fc: cmp             SP, x16
    //     0xcb2700: b.ls            #0xcb2ab8
    // 0xcb2704: cmp             x11, x8
    // 0xcb2708: b.ge            #0xcb2880
    // 0xcb270c: tst             x11, x4
    // 0xcb2710: b.ne            #0xcb277c
    // 0xcb2714: LoadField: r12 = r5->field_7
    //     0xcb2714: ldur            w12, [x5, #7]
    // 0xcb2718: DecompressPointer r12
    //     0xcb2718: add             x12, x12, HEAP, lsl #32
    // 0xcb271c: LoadField: r13 = r5->field_1b
    //     0xcb271c: ldur            x13, [x5, #0x1b]
    // 0xcb2720: r0 = BoxInt64Instr(r13)
    //     0xcb2720: sbfiz           x0, x13, #1, #0x1f
    //     0xcb2724: cmp             x13, x0, asr #1
    //     0xcb2728: b.eq            #0xcb2734
    //     0xcb272c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb2730: stur            x13, [x0, #7]
    // 0xcb2734: r1 = LoadClassIdInstr(r12)
    //     0xcb2734: ldur            x1, [x12, #-1]
    //     0xcb2738: ubfx            x1, x1, #0xc, #0x14
    // 0xcb273c: stp             x0, x12, [SP]
    // 0xcb2740: mov             x0, x1
    // 0xcb2744: r0 = GDT[cid_x0 + 0x13037]()
    //     0xcb2744: movz            x17, #0x3037
    //     0xcb2748: movk            x17, #0x1, lsl #16
    //     0xcb274c: add             lr, x0, x17
    //     0xcb2750: ldr             lr, [x21, lr, lsl #3]
    //     0xcb2754: blr             lr
    // 0xcb2758: ldur            x2, [fp, #-0x48]
    // 0xcb275c: LoadField: r1 = r2->field_1b
    //     0xcb275c: ldur            x1, [x2, #0x1b]
    // 0xcb2760: add             x3, x1, #1
    // 0xcb2764: StoreField: r2->field_1b = r3
    //     0xcb2764: stur            x3, [x2, #0x1b]
    // 0xcb2768: r1 = LoadInt32Instr(r0)
    //     0xcb2768: sbfx            x1, x0, #1, #0x1f
    //     0xcb276c: tbz             w0, #0, #0xcb2774
    //     0xcb2770: ldur            x1, [x0, #7]
    // 0xcb2774: mov             x8, x1
    // 0xcb2778: b               #0xcb2784
    // 0xcb277c: mov             x2, x5
    // 0xcb2780: mov             x8, x0
    // 0xcb2784: ldur            x3, [fp, #-0x50]
    // 0xcb2788: ldur            x4, [fp, #-0x38]
    // 0xcb278c: ldur            x5, [fp, #-0x28]
    // 0xcb2790: ldur            x7, [fp, #-8]
    // 0xcb2794: r6 = 255
    //     0xcb2794: movz            x6, #0xff
    // 0xcb2798: stur            x8, [fp, #-0x58]
    // 0xcb279c: cmp             w5, NULL
    // 0xcb27a0: b.eq            #0xcb2ac0
    // 0xcb27a4: ldur            x0, [fp, #-0x18]
    // 0xcb27a8: ubfx            x0, x0, #0, #0x20
    // 0xcb27ac: mov             x1, x8
    // 0xcb27b0: ubfx            x1, x1, #0, #0x20
    // 0xcb27b4: and             x9, x1, x0
    // 0xcb27b8: LoadField: r0 = r5->field_13
    //     0xcb27b8: ldur            w0, [x5, #0x13]
    // 0xcb27bc: r1 = LoadInt32Instr(r0)
    //     0xcb27bc: sbfx            x1, x0, #1, #0x1f
    // 0xcb27c0: ubfx            x9, x9, #0, #0x20
    // 0xcb27c4: mov             x0, x1
    // 0xcb27c8: mov             x1, x9
    // 0xcb27cc: cmp             x1, x0
    // 0xcb27d0: b.hs            #0xcb2ac4
    // 0xcb27d4: ArrayLoad: r0 = r5[r9]  ; List_4
    //     0xcb27d4: add             x16, x5, x9, lsl #2
    //     0xcb27d8: ldur            w0, [x16, #0x17]
    // 0xcb27dc: lsr             w1, w0, #8
    // 0xcb27e0: and             x0, x1, x6
    // 0xcb27e4: LoadField: r9 = r3->field_7
    //     0xcb27e4: ldur            w9, [x3, #7]
    // 0xcb27e8: DecompressPointer r9
    //     0xcb27e8: add             x9, x9, HEAP, lsl #32
    // 0xcb27ec: LoadField: r10 = r3->field_1b
    //     0xcb27ec: ldur            x10, [x3, #0x1b]
    // 0xcb27f0: lsl             w11, w0, #1
    // 0xcb27f4: r0 = BoxInt64Instr(r10)
    //     0xcb27f4: sbfiz           x0, x10, #1, #0x1f
    //     0xcb27f8: cmp             x10, x0, asr #1
    //     0xcb27fc: b.eq            #0xcb2808
    //     0xcb2800: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb2804: stur            x10, [x0, #7]
    // 0xcb2808: r1 = LoadClassIdInstr(r9)
    //     0xcb2808: ldur            x1, [x9, #-1]
    //     0xcb280c: ubfx            x1, x1, #0xc, #0x14
    // 0xcb2810: stp             x0, x9, [SP, #8]
    // 0xcb2814: str             x11, [SP]
    // 0xcb2818: mov             x0, x1
    // 0xcb281c: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xcb281c: movz            x17, #0x310f
    //     0xcb2820: movk            x17, #0x1, lsl #16
    //     0xcb2824: add             lr, x0, x17
    //     0xcb2828: ldr             lr, [x21, lr, lsl #3]
    //     0xcb282c: blr             lr
    // 0xcb2830: ldur            x3, [fp, #-0x50]
    // 0xcb2834: LoadField: r0 = r3->field_1b
    //     0xcb2834: ldur            x0, [x3, #0x1b]
    // 0xcb2838: add             x1, x0, #1
    // 0xcb283c: StoreField: r3->field_1b = r1
    //     0xcb283c: stur            x1, [x3, #0x1b]
    // 0xcb2840: ldur            x1, [fp, #-0x38]
    // 0xcb2844: ldur            x0, [fp, #-0x58]
    // 0xcb2848: asr             x2, x0, x1
    // 0xcb284c: ldur            x0, [fp, #-8]
    // 0xcb2850: add             x11, x0, #1
    // 0xcb2854: mov             x0, x2
    // 0xcb2858: mov             x6, x3
    // 0xcb285c: ldur            x3, [fp, #-0x40]
    // 0xcb2860: ldur            x5, [fp, #-0x48]
    // 0xcb2864: mov             x7, x1
    // 0xcb2868: ldur            x8, [fp, #-0x30]
    // 0xcb286c: ldur            x9, [fp, #-0x28]
    // 0xcb2870: ldur            x4, [fp, #-0x20]
    // 0xcb2874: ldur            x10, [fp, #-0x18]
    // 0xcb2878: ldur            x2, [fp, #-0x10]
    // 0xcb287c: b               #0xcb26f4
    // 0xcb2880: mov             x3, x6
    // 0xcb2884: mov             x1, x7
    // 0xcb2888: mov             x0, x2
    // 0xcb288c: add             x2, x0, #1
    // 0xcb2890: mov             x6, x3
    // 0xcb2894: ldur            x3, [fp, #-0x40]
    // 0xcb2898: ldur            x5, [fp, #-0x48]
    // 0xcb289c: mov             x7, x1
    // 0xcb28a0: ldur            x8, [fp, #-0x30]
    // 0xcb28a4: ldur            x9, [fp, #-0x28]
    // 0xcb28a8: ldur            x4, [fp, #-0x20]
    // 0xcb28ac: ldur            x10, [fp, #-0x18]
    // 0xcb28b0: b               #0xcb26d4
    // 0xcb28b4: mov             x3, x6
    // 0xcb28b8: mov             x7, x2
    // 0xcb28bc: ldur            x5, [fp, #-0x40]
    // 0xcb28c0: ldur            x2, [fp, #-0x48]
    // 0xcb28c4: ldur            x6, [fp, #-0x30]
    // 0xcb28c8: ldur            x4, [fp, #-0x28]
    // 0xcb28cc: stur            x7, [fp, #-0x10]
    // 0xcb28d0: CheckStackOverflow
    //     0xcb28d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb28d4: cmp             SP, x16
    //     0xcb28d8: b.ls            #0xcb2ac8
    // 0xcb28dc: cmp             x7, x5
    // 0xcb28e0: b.ge            #0xcb2a2c
    // 0xcb28e4: r8 = 0
    //     0xcb28e4: movz            x8, #0
    // 0xcb28e8: stur            x8, [fp, #-8]
    // 0xcb28ec: CheckStackOverflow
    //     0xcb28ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb28f0: cmp             SP, x16
    //     0xcb28f4: b.ls            #0xcb2ad0
    // 0xcb28f8: cmp             x8, x6
    // 0xcb28fc: b.ge            #0xcb2a18
    // 0xcb2900: LoadField: r9 = r2->field_7
    //     0xcb2900: ldur            w9, [x2, #7]
    // 0xcb2904: DecompressPointer r9
    //     0xcb2904: add             x9, x9, HEAP, lsl #32
    // 0xcb2908: LoadField: r10 = r2->field_1b
    //     0xcb2908: ldur            x10, [x2, #0x1b]
    // 0xcb290c: r0 = BoxInt64Instr(r10)
    //     0xcb290c: sbfiz           x0, x10, #1, #0x1f
    //     0xcb2910: cmp             x10, x0, asr #1
    //     0xcb2914: b.eq            #0xcb2920
    //     0xcb2918: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb291c: stur            x10, [x0, #7]
    // 0xcb2920: r1 = LoadClassIdInstr(r9)
    //     0xcb2920: ldur            x1, [x9, #-1]
    //     0xcb2924: ubfx            x1, x1, #0xc, #0x14
    // 0xcb2928: stp             x0, x9, [SP]
    // 0xcb292c: mov             x0, x1
    // 0xcb2930: r0 = GDT[cid_x0 + 0x13037]()
    //     0xcb2930: movz            x17, #0x3037
    //     0xcb2934: movk            x17, #0x1, lsl #16
    //     0xcb2938: add             lr, x0, x17
    //     0xcb293c: ldr             lr, [x21, lr, lsl #3]
    //     0xcb2940: blr             lr
    // 0xcb2944: ldur            x2, [fp, #-0x48]
    // 0xcb2948: LoadField: r1 = r2->field_1b
    //     0xcb2948: ldur            x1, [x2, #0x1b]
    // 0xcb294c: add             x3, x1, #1
    // 0xcb2950: StoreField: r2->field_1b = r3
    //     0xcb2950: stur            x3, [x2, #0x1b]
    // 0xcb2954: ldur            x3, [fp, #-0x28]
    // 0xcb2958: cmp             w3, NULL
    // 0xcb295c: b.eq            #0xcb2ad8
    // 0xcb2960: LoadField: r1 = r3->field_13
    //     0xcb2960: ldur            w1, [x3, #0x13]
    // 0xcb2964: r4 = LoadInt32Instr(r0)
    //     0xcb2964: sbfx            x4, x0, #1, #0x1f
    //     0xcb2968: tbz             w0, #0, #0xcb2970
    //     0xcb296c: ldur            x4, [x0, #7]
    // 0xcb2970: r0 = LoadInt32Instr(r1)
    //     0xcb2970: sbfx            x0, x1, #1, #0x1f
    // 0xcb2974: mov             x1, x4
    // 0xcb2978: cmp             x1, x0
    // 0xcb297c: b.hs            #0xcb2adc
    // 0xcb2980: ArrayLoad: r0 = r3[r4]  ; List_4
    //     0xcb2980: add             x16, x3, x4, lsl #2
    //     0xcb2984: ldur            w0, [x16, #0x17]
    // 0xcb2988: lsr             w1, w0, #8
    // 0xcb298c: r4 = 255
    //     0xcb298c: movz            x4, #0xff
    // 0xcb2990: and             x0, x1, x4
    // 0xcb2994: ldur            x5, [fp, #-0x50]
    // 0xcb2998: LoadField: r6 = r5->field_7
    //     0xcb2998: ldur            w6, [x5, #7]
    // 0xcb299c: DecompressPointer r6
    //     0xcb299c: add             x6, x6, HEAP, lsl #32
    // 0xcb29a0: LoadField: r7 = r5->field_1b
    //     0xcb29a0: ldur            x7, [x5, #0x1b]
    // 0xcb29a4: lsl             w8, w0, #1
    // 0xcb29a8: r0 = BoxInt64Instr(r7)
    //     0xcb29a8: sbfiz           x0, x7, #1, #0x1f
    //     0xcb29ac: cmp             x7, x0, asr #1
    //     0xcb29b0: b.eq            #0xcb29bc
    //     0xcb29b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb29b8: stur            x7, [x0, #7]
    // 0xcb29bc: r1 = LoadClassIdInstr(r6)
    //     0xcb29bc: ldur            x1, [x6, #-1]
    //     0xcb29c0: ubfx            x1, x1, #0xc, #0x14
    // 0xcb29c4: stp             x0, x6, [SP, #8]
    // 0xcb29c8: str             x8, [SP]
    // 0xcb29cc: mov             x0, x1
    // 0xcb29d0: r0 = GDT[cid_x0 + 0x1310f]()
    //     0xcb29d0: movz            x17, #0x310f
    //     0xcb29d4: movk            x17, #0x1, lsl #16
    //     0xcb29d8: add             lr, x0, x17
    //     0xcb29dc: ldr             lr, [x21, lr, lsl #3]
    //     0xcb29e0: blr             lr
    // 0xcb29e4: ldur            x1, [fp, #-0x50]
    // 0xcb29e8: LoadField: r2 = r1->field_1b
    //     0xcb29e8: ldur            x2, [x1, #0x1b]
    // 0xcb29ec: add             x3, x2, #1
    // 0xcb29f0: StoreField: r1->field_1b = r3
    //     0xcb29f0: stur            x3, [x1, #0x1b]
    // 0xcb29f4: ldur            x2, [fp, #-8]
    // 0xcb29f8: add             x8, x2, #1
    // 0xcb29fc: ldur            x5, [fp, #-0x40]
    // 0xcb2a00: ldur            x2, [fp, #-0x48]
    // 0xcb2a04: mov             x3, x1
    // 0xcb2a08: ldur            x6, [fp, #-0x30]
    // 0xcb2a0c: ldur            x4, [fp, #-0x28]
    // 0xcb2a10: ldur            x7, [fp, #-0x10]
    // 0xcb2a14: b               #0xcb28e8
    // 0xcb2a18: mov             x1, x3
    // 0xcb2a1c: mov             x2, x7
    // 0xcb2a20: add             x7, x2, #1
    // 0xcb2a24: mov             x3, x1
    // 0xcb2a28: b               #0xcb28bc
    // 0xcb2a2c: r0 = Null
    //     0xcb2a2c: mov             x0, NULL
    // 0xcb2a30: LeaveFrame
    //     0xcb2a30: mov             SP, fp
    //     0xcb2a34: ldp             fp, lr, [SP], #0x10
    // 0xcb2a38: ret
    //     0xcb2a38: ret             
    // 0xcb2a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb2a3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb2a40: b               #0xcb2680
    // 0xcb2a44: tbnz            x4, #0x3f, #0xcb2a50
    // 0xcb2a48: asr             x7, x0, #0x3f
    // 0xcb2a4c: b               #0xcb2690
    // 0xcb2a50: str             x4, [THR, #0x7a8]  ; THR::
    // 0xcb2a54: stp             x5, x6, [SP, #-0x10]!
    // 0xcb2a58: stp             x3, x4, [SP, #-0x10]!
    // 0xcb2a5c: stp             x1, x2, [SP, #-0x10]!
    // 0xcb2a60: SaveReg r0
    //     0xcb2a60: str             x0, [SP, #-8]!
    // 0xcb2a64: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb2a68: r4 = 0
    //     0xcb2a68: movz            x4, #0
    // 0xcb2a6c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb2a70: blr             lr
    // 0xcb2a74: brk             #0
    // 0xcb2a78: tbnz            x4, #0x3f, #0xcb2a84
    // 0xcb2a7c: mov             x1, xzr
    // 0xcb2a80: b               #0xcb26c0
    // 0xcb2a84: str             x4, [THR, #0x7a8]  ; THR::
    // 0xcb2a88: stp             x8, x9, [SP, #-0x10]!
    // 0xcb2a8c: stp             x6, x7, [SP, #-0x10]!
    // 0xcb2a90: stp             x4, x5, [SP, #-0x10]!
    // 0xcb2a94: stp             x2, x3, [SP, #-0x10]!
    // 0xcb2a98: SaveReg r0
    //     0xcb2a98: str             x0, [SP, #-8]!
    // 0xcb2a9c: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb2aa0: r4 = 0
    //     0xcb2aa0: movz            x4, #0
    // 0xcb2aa4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb2aa8: blr             lr
    // 0xcb2aac: brk             #0
    // 0xcb2ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb2ab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb2ab4: b               #0xcb26e4
    // 0xcb2ab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb2ab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb2abc: b               #0xcb2704
    // 0xcb2ac0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb2ac0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb2ac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb2ac4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb2ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb2ac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb2acc: b               #0xcb28dc
    // 0xcb2ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb2ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb2ad4: b               #0xcb28f8
    // 0xcb2ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb2ad8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb2adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb2adc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ inverseTransform(/* No info */) {
    // ** addr: 0xcb2ec0, size: 0x2dc
    // 0xcb2ec0: EnterFrame
    //     0xcb2ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xcb2ec4: mov             fp, SP
    // 0xcb2ec8: AllocStack(0x38)
    //     0xcb2ec8: sub             SP, SP, #0x38
    // 0xcb2ecc: SetupParameters(VP8LTransform this /* r1 => r9, fp-0x10 */, dynamic _ /* r2 => r8, fp-0x18 */, dynamic _ /* r3 => r7, fp-0x20 */, dynamic _ /* r5 => r4, fp-0x28 */, dynamic _ /* r7 => r0, fp-0x30 */)
    //     0xcb2ecc: mov             x9, x1
    //     0xcb2ed0: mov             x8, x2
    //     0xcb2ed4: mov             x0, x7
    //     0xcb2ed8: stur            x7, [fp, #-0x30]
    //     0xcb2edc: mov             x7, x3
    //     0xcb2ee0: mov             x4, x5
    //     0xcb2ee4: stur            x1, [fp, #-0x10]
    //     0xcb2ee8: stur            x2, [fp, #-0x18]
    //     0xcb2eec: stur            x3, [fp, #-0x20]
    //     0xcb2ef0: stur            x5, [fp, #-0x28]
    // 0xcb2ef4: CheckStackOverflow
    //     0xcb2ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb2ef8: cmp             SP, x16
    //     0xcb2efc: b.ls            #0xcb3124
    // 0xcb2f00: LoadField: r10 = r9->field_b
    //     0xcb2f00: ldur            x10, [x9, #0xb]
    // 0xcb2f04: stur            x10, [fp, #-8]
    // 0xcb2f08: LoadField: r1 = r9->field_7
    //     0xcb2f08: ldur            w1, [x9, #7]
    // 0xcb2f0c: DecompressPointer r1
    //     0xcb2f0c: add             x1, x1, HEAP, lsl #32
    // 0xcb2f10: LoadField: r2 = r1->field_7
    //     0xcb2f10: ldur            x2, [x1, #7]
    // 0xcb2f14: cmp             x2, #1
    // 0xcb2f18: b.gt            #0xcb2fec
    // 0xcb2f1c: cmp             x2, #0
    // 0xcb2f20: b.gt            #0xcb2fc0
    // 0xcb2f24: mov             x1, x9
    // 0xcb2f28: mov             x2, x8
    // 0xcb2f2c: mov             x3, x7
    // 0xcb2f30: mov             x5, x0
    // 0xcb2f34: ldr             x6, [fp, #0x10]
    // 0xcb2f38: r0 = predictorInverseTransform()
    //     0xcb2f38: bl              #0xcb3d38  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::predictorInverseTransform
    // 0xcb2f3c: ldur            x4, [fp, #-0x10]
    // 0xcb2f40: LoadField: r0 = r4->field_13
    //     0xcb2f40: ldur            x0, [x4, #0x13]
    // 0xcb2f44: ldur            x7, [fp, #-0x20]
    // 0xcb2f48: cmp             x7, x0
    // 0xcb2f4c: b.eq            #0xcb3114
    // 0xcb2f50: ldur            x9, [fp, #-0x18]
    // 0xcb2f54: ldur            x8, [fp, #-0x30]
    // 0xcb2f58: ldr             x10, [fp, #0x10]
    // 0xcb2f5c: ldur            x0, [fp, #-8]
    // 0xcb2f60: sub             x2, x10, x0
    // 0xcb2f64: add             x3, x2, x0
    // 0xcb2f68: sub             x1, x7, x9
    // 0xcb2f6c: sub             x4, x1, #1
    // 0xcb2f70: mul             x1, x4, x0
    // 0xcb2f74: add             x4, x10, x1
    // 0xcb2f78: r0 = BoxInt64Instr(r4)
    //     0xcb2f78: sbfiz           x0, x4, #1, #0x1f
    //     0xcb2f7c: cmp             x4, x0, asr #1
    //     0xcb2f80: b.eq            #0xcb2f8c
    //     0xcb2f84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb2f88: stur            x4, [x0, #7]
    // 0xcb2f8c: r1 = LoadClassIdInstr(r8)
    //     0xcb2f8c: ldur            x1, [x8, #-1]
    //     0xcb2f90: ubfx            x1, x1, #0xc, #0x14
    // 0xcb2f94: str             x0, [SP]
    // 0xcb2f98: mov             x0, x1
    // 0xcb2f9c: mov             x1, x8
    // 0xcb2fa0: ldur            x5, [fp, #-0x28]
    // 0xcb2fa4: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0xcb2fa4: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0xcb2fa8: r0 = GDT[cid_x0 + 0x1355f]()
    //     0xcb2fa8: movz            x17, #0x355f
    //     0xcb2fac: movk            x17, #0x1, lsl #16
    //     0xcb2fb0: add             lr, x0, x17
    //     0xcb2fb4: ldr             lr, [x21, lr, lsl #3]
    //     0xcb2fb8: blr             lr
    // 0xcb2fbc: b               #0xcb3114
    // 0xcb2fc0: mov             x4, x9
    // 0xcb2fc4: mov             x9, x8
    // 0xcb2fc8: mov             x8, x0
    // 0xcb2fcc: ldr             x10, [fp, #0x10]
    // 0xcb2fd0: mov             x1, x4
    // 0xcb2fd4: mov             x2, x9
    // 0xcb2fd8: mov             x3, x7
    // 0xcb2fdc: mov             x5, x8
    // 0xcb2fe0: mov             x6, x10
    // 0xcb2fe4: r0 = colorSpaceInverseTransform()
    //     0xcb2fe4: bl              #0xcb363c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::colorSpaceInverseTransform
    // 0xcb2fe8: b               #0xcb3114
    // 0xcb2fec: mov             x4, x9
    // 0xcb2ff0: mov             x9, x8
    // 0xcb2ff4: mov             x8, x0
    // 0xcb2ff8: mov             x0, x10
    // 0xcb2ffc: ldr             x10, [fp, #0x10]
    // 0xcb3000: cmp             x2, #2
    // 0xcb3004: b.gt            #0xcb3028
    // 0xcb3008: sub             x1, x7, x9
    // 0xcb300c: mul             x2, x1, x0
    // 0xcb3010: add             x5, x10, x2
    // 0xcb3014: mov             x1, x4
    // 0xcb3018: mov             x2, x8
    // 0xcb301c: mov             x3, x10
    // 0xcb3020: r0 = addGreenToBlueAndRed()
    //     0xcb3020: bl              #0xcb3544  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::addGreenToBlueAndRed
    // 0xcb3024: b               #0xcb3114
    // 0xcb3028: cmp             x6, x10
    // 0xcb302c: b.ne            #0xcb30f4
    // 0xcb3030: LoadField: r1 = r4->field_1f
    //     0xcb3030: ldur            x1, [x4, #0x1f]
    // 0xcb3034: cmp             x1, #0
    // 0xcb3038: b.le            #0xcb30ec
    // 0xcb303c: r2 = 1
    //     0xcb303c: movz            x2, #0x1
    // 0xcb3040: sub             x3, x7, x9
    // 0xcb3044: mul             x5, x3, x0
    // 0xcb3048: cmp             x1, #0x3f
    // 0xcb304c: b.hi            #0xcb312c
    // 0xcb3050: lsl             x6, x2, x1
    // 0xcb3054: add             x2, x0, x6
    // 0xcb3058: sub             x0, x2, #1
    // 0xcb305c: cmp             x1, #0x3f
    // 0xcb3060: b.hi            #0xcb3164
    // 0xcb3064: asr             x2, x0, x1
    // 0xcb3068: mul             x0, x3, x2
    // 0xcb306c: add             x1, x10, x5
    // 0xcb3070: sub             x6, x1, x0
    // 0xcb3074: stur            x6, [fp, #-8]
    // 0xcb3078: add             x3, x6, x0
    // 0xcb307c: r0 = BoxInt64Instr(r10)
    //     0xcb307c: sbfiz           x0, x10, #1, #0x1f
    //     0xcb3080: cmp             x10, x0, asr #1
    //     0xcb3084: b.eq            #0xcb3090
    //     0xcb3088: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb308c: stur            x10, [x0, #7]
    // 0xcb3090: r1 = LoadClassIdInstr(r8)
    //     0xcb3090: ldur            x1, [x8, #-1]
    //     0xcb3094: ubfx            x1, x1, #0xc, #0x14
    // 0xcb3098: str             x0, [SP]
    // 0xcb309c: mov             x0, x1
    // 0xcb30a0: mov             x1, x8
    // 0xcb30a4: mov             x2, x6
    // 0xcb30a8: ldur            x5, [fp, #-0x28]
    // 0xcb30ac: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0xcb30ac: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0xcb30b0: r0 = GDT[cid_x0 + 0x1355f]()
    //     0xcb30b0: movz            x17, #0x355f
    //     0xcb30b4: movk            x17, #0x1, lsl #16
    //     0xcb30b8: add             lr, x0, x17
    //     0xcb30bc: ldr             lr, [x21, lr, lsl #3]
    //     0xcb30c0: blr             lr
    // 0xcb30c4: ldr             x0, [fp, #0x10]
    // 0xcb30c8: str             x0, [SP]
    // 0xcb30cc: ldur            x1, [fp, #-0x10]
    // 0xcb30d0: ldur            x2, [fp, #-0x18]
    // 0xcb30d4: ldur            x3, [fp, #-0x20]
    // 0xcb30d8: ldur            x5, [fp, #-0x28]
    // 0xcb30dc: ldur            x6, [fp, #-8]
    // 0xcb30e0: ldur            x7, [fp, #-0x30]
    // 0xcb30e4: r0 = colorIndexInverseTransform()
    //     0xcb30e4: bl              #0xcb319c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::colorIndexInverseTransform
    // 0xcb30e8: b               #0xcb3114
    // 0xcb30ec: mov             x0, x10
    // 0xcb30f0: b               #0xcb30f8
    // 0xcb30f4: mov             x0, x10
    // 0xcb30f8: str             x0, [SP]
    // 0xcb30fc: ldur            x1, [fp, #-0x10]
    // 0xcb3100: ldur            x2, [fp, #-0x18]
    // 0xcb3104: ldur            x3, [fp, #-0x20]
    // 0xcb3108: ldur            x5, [fp, #-0x28]
    // 0xcb310c: ldur            x7, [fp, #-0x30]
    // 0xcb3110: r0 = colorIndexInverseTransform()
    //     0xcb3110: bl              #0xcb319c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::colorIndexInverseTransform
    // 0xcb3114: r0 = Null
    //     0xcb3114: mov             x0, NULL
    // 0xcb3118: LeaveFrame
    //     0xcb3118: mov             SP, fp
    //     0xcb311c: ldp             fp, lr, [SP], #0x10
    // 0xcb3120: ret
    //     0xcb3120: ret             
    // 0xcb3124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb3124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3128: b               #0xcb2f00
    // 0xcb312c: tbnz            x1, #0x3f, #0xcb3138
    // 0xcb3130: mov             x6, xzr
    // 0xcb3134: b               #0xcb3054
    // 0xcb3138: str             x1, [THR, #0x7a8]  ; THR::
    // 0xcb313c: stp             x9, x10, [SP, #-0x10]!
    // 0xcb3140: stp             x7, x8, [SP, #-0x10]!
    // 0xcb3144: stp             x4, x5, [SP, #-0x10]!
    // 0xcb3148: stp             x2, x3, [SP, #-0x10]!
    // 0xcb314c: stp             x0, x1, [SP, #-0x10]!
    // 0xcb3150: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb3154: r4 = 0
    //     0xcb3154: movz            x4, #0
    // 0xcb3158: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb315c: blr             lr
    // 0xcb3160: brk             #0
    // 0xcb3164: tbnz            x1, #0x3f, #0xcb3170
    // 0xcb3168: asr             x2, x0, #0x3f
    // 0xcb316c: b               #0xcb3068
    // 0xcb3170: str             x1, [THR, #0x7a8]  ; THR::
    // 0xcb3174: stp             x9, x10, [SP, #-0x10]!
    // 0xcb3178: stp             x7, x8, [SP, #-0x10]!
    // 0xcb317c: stp             x4, x5, [SP, #-0x10]!
    // 0xcb3180: stp             x1, x3, [SP, #-0x10]!
    // 0xcb3184: SaveReg r0
    //     0xcb3184: str             x0, [SP, #-8]!
    // 0xcb3188: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb318c: r4 = 0
    //     0xcb318c: movz            x4, #0
    // 0xcb3190: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb3194: blr             lr
    // 0xcb3198: brk             #0
  }
  _ colorIndexInverseTransform(/* No info */) {
    // ** addr: 0xcb319c, size: 0x3a8
    // 0xcb319c: EnterFrame
    //     0xcb319c: stp             fp, lr, [SP, #-0x10]!
    //     0xcb31a0: mov             fp, SP
    // 0xcb31a4: r4 = 8
    //     0xcb31a4: movz            x4, #0x8
    // 0xcb31a8: LoadField: r8 = r1->field_1f
    //     0xcb31a8: ldur            x8, [x1, #0x1f]
    // 0xcb31ac: cmp             x8, #0x3f
    // 0xcb31b0: b.hi            #0xcb3414
    // 0xcb31b4: asr             x9, x4, x8
    // 0xcb31b8: LoadField: r4 = r1->field_b
    //     0xcb31b8: ldur            x4, [x1, #0xb]
    // 0xcb31bc: LoadField: r10 = r1->field_1b
    //     0xcb31bc: ldur            w10, [x1, #0x1b]
    // 0xcb31c0: DecompressPointer r10
    //     0xcb31c0: add             x10, x10, HEAP, lsl #32
    // 0xcb31c4: cmp             x9, #8
    // 0xcb31c8: b.ge            #0xcb3314
    // 0xcb31cc: ldr             x12, [fp, #0x10]
    // 0xcb31d0: r11 = 1
    //     0xcb31d0: movz            x11, #0x1
    // 0xcb31d4: cmp             x8, #0x3f
    // 0xcb31d8: b.hi            #0xcb3448
    // 0xcb31dc: lsl             x13, x11, x8
    // 0xcb31e0: sub             x8, x13, #1
    // 0xcb31e4: lsl             x13, x11, x9
    // 0xcb31e8: sub             x11, x13, #1
    // 0xcb31ec: LoadField: r13 = r5->field_13
    //     0xcb31ec: ldur            w13, [x5, #0x13]
    // 0xcb31f0: r14 = LoadInt32Instr(r13)
    //     0xcb31f0: sbfx            x14, x13, #1, #0x1f
    // 0xcb31f4: mov             x23, x6
    // 0xcb31f8: mov             x20, x12
    // 0xcb31fc: mov             x19, x2
    // 0xcb3200: r13 = 255
    //     0xcb3200: movz            x13, #0xff
    // 0xcb3204: CheckStackOverflow
    //     0xcb3204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3208: cmp             SP, x16
    //     0xcb320c: b.ls            #0xcb3484
    // 0xcb3210: cmp             x19, x3
    // 0xcb3214: b.ge            #0xcb3404
    // 0xcb3218: mov             x6, x23
    // 0xcb321c: mov             x2, x20
    // 0xcb3220: r23 = 0
    //     0xcb3220: movz            x23, #0
    // 0xcb3224: r20 = 0
    //     0xcb3224: movz            x20, #0
    // 0xcb3228: CheckStackOverflow
    //     0xcb3228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb322c: cmp             SP, x16
    //     0xcb3230: b.ls            #0xcb348c
    // 0xcb3234: cmp             x20, x4
    // 0xcb3238: b.ge            #0xcb3300
    // 0xcb323c: tst             x20, x8
    // 0xcb3240: b.ne            #0xcb3278
    // 0xcb3244: add             x24, x6, #1
    // 0xcb3248: mov             x0, x14
    // 0xcb324c: mov             x1, x6
    // 0xcb3250: cmp             x1, x0
    // 0xcb3254: b.hs            #0xcb3494
    // 0xcb3258: LoadField: r25 = r5->field_7
    //     0xcb3258: ldur            x25, [x5, #7]
    // 0xcb325c: add             x16, x25, x6, lsl #2
    // 0xcb3260: ldr             w0, [x16]
    // 0xcb3264: lsr             w25, w0, #8
    // 0xcb3268: and             x0, x25, x13
    // 0xcb326c: ubfx            x0, x0, #0, #0x20
    // 0xcb3270: mov             x6, x24
    // 0xcb3274: mov             x23, x0
    // 0xcb3278: add             x12, x2, #1
    // 0xcb327c: cmp             w10, NULL
    // 0xcb3280: b.eq            #0xcb3498
    // 0xcb3284: mov             x24, x11
    // 0xcb3288: ubfx            x24, x24, #0, #0x20
    // 0xcb328c: mov             x25, x23
    // 0xcb3290: ubfx            x25, x25, #0, #0x20
    // 0xcb3294: and             x0, x25, x24
    // 0xcb3298: LoadField: r24 = r10->field_13
    //     0xcb3298: ldur            w24, [x10, #0x13]
    // 0xcb329c: r1 = LoadInt32Instr(r24)
    //     0xcb329c: sbfx            x1, x24, #1, #0x1f
    // 0xcb32a0: mov             x24, x0
    // 0xcb32a4: ubfx            x24, x24, #0, #0x20
    // 0xcb32a8: mov             x0, x1
    // 0xcb32ac: mov             x1, x24
    // 0xcb32b0: cmp             x1, x0
    // 0xcb32b4: b.hs            #0xcb349c
    // 0xcb32b8: ArrayLoad: r25 = r10[r24]  ; List_4
    //     0xcb32b8: add             x16, x10, x24, lsl #2
    //     0xcb32bc: ldur            w25, [x16, #0x17]
    // 0xcb32c0: ldurb           w16, [x7, #-1]
    // 0xcb32c4: tbnz            w16, #6, #0xcb34a0
    // 0xcb32c8: LoadField: r24 = r7->field_13
    //     0xcb32c8: ldur            w24, [x7, #0x13]
    // 0xcb32cc: r0 = LoadInt32Instr(r24)
    //     0xcb32cc: sbfx            x0, x24, #1, #0x1f
    // 0xcb32d0: mov             x1, x2
    // 0xcb32d4: cmp             x1, x0
    // 0xcb32d8: b.hs            #0xcb34e4
    // 0xcb32dc: LoadField: r24 = r7->field_7
    //     0xcb32dc: ldur            x24, [x7, #7]
    // 0xcb32e0: add             x0, x24, x2, lsl #2
    // 0xcb32e4: str             w25, [x0]
    // 0xcb32e8: asr             x0, x23, x9
    // 0xcb32ec: add             x1, x20, #1
    // 0xcb32f0: mov             x2, x12
    // 0xcb32f4: mov             x23, x0
    // 0xcb32f8: mov             x20, x1
    // 0xcb32fc: b               #0xcb3228
    // 0xcb3300: add             x0, x19, #1
    // 0xcb3304: mov             x23, x6
    // 0xcb3308: mov             x20, x2
    // 0xcb330c: mov             x19, x0
    // 0xcb3310: b               #0xcb3204
    // 0xcb3314: ldr             x12, [fp, #0x10]
    // 0xcb3318: r13 = 255
    //     0xcb3318: movz            x13, #0xff
    // 0xcb331c: LoadField: r8 = r5->field_13
    //     0xcb331c: ldur            w8, [x5, #0x13]
    // 0xcb3320: r9 = LoadInt32Instr(r8)
    //     0xcb3320: sbfx            x9, x8, #1, #0x1f
    // 0xcb3324: mov             x8, x6
    // 0xcb3328: mov             x6, x12
    // 0xcb332c: CheckStackOverflow
    //     0xcb332c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3330: cmp             SP, x16
    //     0xcb3334: b.ls            #0xcb34e8
    // 0xcb3338: cmp             x2, x3
    // 0xcb333c: b.ge            #0xcb3404
    // 0xcb3340: mov             x11, x8
    // 0xcb3344: mov             x8, x6
    // 0xcb3348: r6 = 0
    //     0xcb3348: movz            x6, #0
    // 0xcb334c: CheckStackOverflow
    //     0xcb334c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3350: cmp             SP, x16
    //     0xcb3354: b.ls            #0xcb34f0
    // 0xcb3358: cmp             x6, x4
    // 0xcb335c: b.ge            #0xcb33f0
    // 0xcb3360: add             x12, x8, #1
    // 0xcb3364: cmp             w10, NULL
    // 0xcb3368: b.eq            #0xcb34f8
    // 0xcb336c: add             x14, x11, #1
    // 0xcb3370: mov             x0, x9
    // 0xcb3374: mov             x1, x11
    // 0xcb3378: cmp             x1, x0
    // 0xcb337c: b.hs            #0xcb34fc
    // 0xcb3380: LoadField: r19 = r5->field_7
    //     0xcb3380: ldur            x19, [x5, #7]
    // 0xcb3384: add             x16, x19, x11, lsl #2
    // 0xcb3388: ldr             w20, [x16]
    // 0xcb338c: lsr             w19, w20, #8
    // 0xcb3390: and             x20, x19, x13
    // 0xcb3394: LoadField: r19 = r10->field_13
    //     0xcb3394: ldur            w19, [x10, #0x13]
    // 0xcb3398: r0 = LoadInt32Instr(r19)
    //     0xcb3398: sbfx            x0, x19, #1, #0x1f
    // 0xcb339c: ubfx            x20, x20, #0, #0x20
    // 0xcb33a0: mov             x1, x20
    // 0xcb33a4: cmp             x1, x0
    // 0xcb33a8: b.hs            #0xcb3500
    // 0xcb33ac: ArrayLoad: r19 = r10[r20]  ; List_4
    //     0xcb33ac: add             x16, x10, x20, lsl #2
    //     0xcb33b0: ldur            w19, [x16, #0x17]
    // 0xcb33b4: ldurb           w16, [x7, #-1]
    // 0xcb33b8: tbnz            w16, #6, #0xcb3504
    // 0xcb33bc: LoadField: r20 = r7->field_13
    //     0xcb33bc: ldur            w20, [x7, #0x13]
    // 0xcb33c0: r0 = LoadInt32Instr(r20)
    //     0xcb33c0: sbfx            x0, x20, #1, #0x1f
    // 0xcb33c4: mov             x1, x8
    // 0xcb33c8: cmp             x1, x0
    // 0xcb33cc: b.hs            #0xcb3540
    // 0xcb33d0: LoadField: r1 = r7->field_7
    //     0xcb33d0: ldur            x1, [x7, #7]
    // 0xcb33d4: add             x20, x1, x8, lsl #2
    // 0xcb33d8: str             w19, [x20]
    // 0xcb33dc: add             x0, x6, #1
    // 0xcb33e0: mov             x11, x14
    // 0xcb33e4: mov             x8, x12
    // 0xcb33e8: mov             x6, x0
    // 0xcb33ec: b               #0xcb334c
    // 0xcb33f0: add             x0, x2, #1
    // 0xcb33f4: mov             x6, x8
    // 0xcb33f8: mov             x8, x11
    // 0xcb33fc: mov             x2, x0
    // 0xcb3400: b               #0xcb332c
    // 0xcb3404: r0 = Null
    //     0xcb3404: mov             x0, NULL
    // 0xcb3408: LeaveFrame
    //     0xcb3408: mov             SP, fp
    //     0xcb340c: ldp             fp, lr, [SP], #0x10
    // 0xcb3410: ret
    //     0xcb3410: ret             
    // 0xcb3414: tbnz            x8, #0x3f, #0xcb3420
    // 0xcb3418: asr             x9, x4, #0x3f
    // 0xcb341c: b               #0xcb31b8
    // 0xcb3420: str             x8, [THR, #0x7a8]  ; THR::
    // 0xcb3424: stp             x7, x8, [SP, #-0x10]!
    // 0xcb3428: stp             x5, x6, [SP, #-0x10]!
    // 0xcb342c: stp             x3, x4, [SP, #-0x10]!
    // 0xcb3430: stp             x1, x2, [SP, #-0x10]!
    // 0xcb3434: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb3438: r4 = 0
    //     0xcb3438: movz            x4, #0
    // 0xcb343c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb3440: blr             lr
    // 0xcb3444: brk             #0
    // 0xcb3448: tbnz            x8, #0x3f, #0xcb3454
    // 0xcb344c: mov             x13, xzr
    // 0xcb3450: b               #0xcb31e0
    // 0xcb3454: str             x8, [THR, #0x7a8]  ; THR::
    // 0xcb3458: stp             x11, x12, [SP, #-0x10]!
    // 0xcb345c: stp             x9, x10, [SP, #-0x10]!
    // 0xcb3460: stp             x7, x8, [SP, #-0x10]!
    // 0xcb3464: stp             x5, x6, [SP, #-0x10]!
    // 0xcb3468: stp             x3, x4, [SP, #-0x10]!
    // 0xcb346c: SaveReg r2
    //     0xcb346c: str             x2, [SP, #-8]!
    // 0xcb3470: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb3474: r4 = 0
    //     0xcb3474: movz            x4, #0
    // 0xcb3478: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb347c: blr             lr
    // 0xcb3480: brk             #0
    // 0xcb3484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb3484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3488: b               #0xcb3210
    // 0xcb348c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb348c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3490: b               #0xcb3234
    // 0xcb3494: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3494: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3498: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb3498: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb349c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb349c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb34a0: stp             x23, x25, [SP, #-0x10]!
    // 0xcb34a4: stp             x19, x20, [SP, #-0x10]!
    // 0xcb34a8: stp             x13, x14, [SP, #-0x10]!
    // 0xcb34ac: stp             x11, x12, [SP, #-0x10]!
    // 0xcb34b0: stp             x9, x10, [SP, #-0x10]!
    // 0xcb34b4: stp             x7, x8, [SP, #-0x10]!
    // 0xcb34b8: stp             x5, x6, [SP, #-0x10]!
    // 0xcb34bc: stp             x3, x4, [SP, #-0x10]!
    // 0xcb34c0: SaveReg r2
    //     0xcb34c0: str             x2, [SP, #-8]!
    // 0xcb34c4: SaveReg r7
    //     0xcb34c4: str             x7, [SP, #-8]!
    // 0xcb34c8: r16 = 0
    //     0xcb34c8: movz            x16, #0
    // 0xcb34cc: SaveReg r16
    //     0xcb34cc: str             x16, [SP, #-8]!
    // 0xcb34d0: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb34d4: r4 = 2
    //     0xcb34d4: movz            x4, #0x2
    // 0xcb34d8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb34dc: blr             lr
    // 0xcb34e0: brk             #0
    // 0xcb34e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb34e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb34e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb34e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb34ec: b               #0xcb3338
    // 0xcb34f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb34f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb34f4: b               #0xcb3358
    // 0xcb34f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb34f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb34fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb34fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3500: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3500: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3504: stp             x14, x19, [SP, #-0x10]!
    // 0xcb3508: stp             x12, x13, [SP, #-0x10]!
    // 0xcb350c: stp             x9, x10, [SP, #-0x10]!
    // 0xcb3510: stp             x7, x8, [SP, #-0x10]!
    // 0xcb3514: stp             x5, x6, [SP, #-0x10]!
    // 0xcb3518: stp             x3, x4, [SP, #-0x10]!
    // 0xcb351c: SaveReg r2
    //     0xcb351c: str             x2, [SP, #-8]!
    // 0xcb3520: SaveReg r7
    //     0xcb3520: str             x7, [SP, #-8]!
    // 0xcb3524: r16 = 0
    //     0xcb3524: movz            x16, #0
    // 0xcb3528: SaveReg r16
    //     0xcb3528: str             x16, [SP, #-8]!
    // 0xcb352c: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb3530: r4 = 2
    //     0xcb3530: movz            x4, #0x2
    // 0xcb3534: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb3538: blr             lr
    // 0xcb353c: brk             #0
    // 0xcb3540: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3540: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ addGreenToBlueAndRed(/* No info */) {
    // ** addr: 0xcb3544, size: 0xf8
    // 0xcb3544: EnterFrame
    //     0xcb3544: stp             fp, lr, [SP, #-0x10]!
    //     0xcb3548: mov             fp, SP
    // 0xcb354c: LoadField: r4 = r2->field_13
    //     0xcb354c: ldur            w4, [x2, #0x13]
    // 0xcb3550: r6 = LoadInt32Instr(r4)
    //     0xcb3550: sbfx            x6, x4, #1, #0x1f
    // 0xcb3554: mov             x8, x3
    // 0xcb3558: r7 = 255
    //     0xcb3558: movz            x7, #0xff
    // 0xcb355c: r4 = 16711935
    //     0xcb355c: movz            x4, #0xff
    //     0xcb3560: movk            x4, #0xff, lsl #16
    // 0xcb3564: r3 = 4278255360
    //     0xcb3564: movz            x3, #0xff00
    //     0xcb3568: movk            x3, #0xff00, lsl #16
    // 0xcb356c: CheckStackOverflow
    //     0xcb356c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3570: cmp             SP, x16
    //     0xcb3574: b.ls            #0xcb35fc
    // 0xcb3578: cmp             x8, x5
    // 0xcb357c: b.ge            #0xcb35ec
    // 0xcb3580: mov             x0, x6
    // 0xcb3584: mov             x1, x8
    // 0xcb3588: cmp             x1, x0
    // 0xcb358c: b.hs            #0xcb3604
    // 0xcb3590: LoadField: r1 = r2->field_7
    //     0xcb3590: ldur            x1, [x2, #7]
    // 0xcb3594: add             x16, x1, x8, lsl #2
    // 0xcb3598: ldr             w9, [x16]
    // 0xcb359c: lsr             w1, w9, #8
    // 0xcb35a0: and             x10, x1, x7
    // 0xcb35a4: and             x1, x9, x4
    // 0xcb35a8: lsl             w11, w10, #0x10
    // 0xcb35ac: orr             x12, x11, x10
    // 0xcb35b0: add             w10, w1, w12
    // 0xcb35b4: and             x1, x10, x4
    // 0xcb35b8: add             x0, x8, #1
    // 0xcb35bc: and             x10, x9, x3
    // 0xcb35c0: ubfx            x1, x1, #0, #0x20
    // 0xcb35c4: ubfx            x10, x10, #0, #0x20
    // 0xcb35c8: orr             x9, x10, x1
    // 0xcb35cc: ldurb           w16, [x2, #-1]
    // 0xcb35d0: tbnz            w16, #6, #0xcb3608
    // 0xcb35d4: ubfx            x9, x9, #0, #0x20
    // 0xcb35d8: LoadField: r1 = r2->field_7
    //     0xcb35d8: ldur            x1, [x2, #7]
    // 0xcb35dc: add             x10, x1, x8, lsl #2
    // 0xcb35e0: str             w9, [x10]
    // 0xcb35e4: mov             x8, x0
    // 0xcb35e8: b               #0xcb356c
    // 0xcb35ec: r0 = Null
    //     0xcb35ec: mov             x0, NULL
    // 0xcb35f0: LeaveFrame
    //     0xcb35f0: mov             SP, fp
    //     0xcb35f4: ldp             fp, lr, [SP], #0x10
    // 0xcb35f8: ret
    //     0xcb35f8: ret             
    // 0xcb35fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb35fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3600: b               #0xcb3578
    // 0xcb3604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3604: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3608: stp             x8, x9, [SP, #-0x10]!
    // 0xcb360c: stp             x6, x7, [SP, #-0x10]!
    // 0xcb3610: stp             x4, x5, [SP, #-0x10]!
    // 0xcb3614: stp             x2, x3, [SP, #-0x10]!
    // 0xcb3618: SaveReg r0
    //     0xcb3618: str             x0, [SP, #-8]!
    // 0xcb361c: SaveReg r2
    //     0xcb361c: str             x2, [SP, #-8]!
    // 0xcb3620: r16 = 0
    //     0xcb3620: movz            x16, #0
    // 0xcb3624: SaveReg r16
    //     0xcb3624: str             x16, [SP, #-8]!
    // 0xcb3628: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb362c: r4 = 2
    //     0xcb362c: movz            x4, #0x2
    // 0xcb3630: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb3634: blr             lr
    // 0xcb3638: brk             #0
  }
  _ colorSpaceInverseTransform(/* No info */) {
    // ** addr: 0xcb363c, size: 0x328
    // 0xcb363c: EnterFrame
    //     0xcb363c: stp             fp, lr, [SP, #-0x10]!
    //     0xcb3640: mov             fp, SP
    // 0xcb3644: AllocStack(0x78)
    //     0xcb3644: sub             SP, SP, #0x78
    // 0xcb3648: r0 = 1
    //     0xcb3648: movz            x0, #0x1
    // 0xcb364c: mov             x7, x1
    // 0xcb3650: mov             x4, x2
    // 0xcb3654: stur            x1, [fp, #-0x18]
    // 0xcb3658: stur            x2, [fp, #-0x20]
    // 0xcb365c: stur            x3, [fp, #-0x28]
    // 0xcb3660: stur            x5, [fp, #-0x30]
    // 0xcb3664: stur            x6, [fp, #-0x38]
    // 0xcb3668: CheckStackOverflow
    //     0xcb3668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb366c: cmp             SP, x16
    //     0xcb3670: b.ls            #0xcb38bc
    // 0xcb3674: LoadField: r8 = r7->field_b
    //     0xcb3674: ldur            x8, [x7, #0xb]
    // 0xcb3678: stur            x8, [fp, #-0x10]
    // 0xcb367c: LoadField: r2 = r7->field_1f
    //     0xcb367c: ldur            x2, [x7, #0x1f]
    // 0xcb3680: cmp             x2, #0x3f
    // 0xcb3684: b.hi            #0xcb38c4
    // 0xcb3688: lsl             x1, x0, x2
    // 0xcb368c: sub             x0, x1, #1
    // 0xcb3690: mov             x1, x8
    // 0xcb3694: stur            x0, [fp, #-8]
    // 0xcb3698: r0 = _subSampleSize()
    //     0xcb3698: bl              #0xcb3cac  ; [package:image/src/formats/webp/vp8l.dart] VP8L::_subSampleSize
    // 0xcb369c: mov             x1, x0
    // 0xcb36a0: ldur            x0, [fp, #-0x18]
    // 0xcb36a4: stur            x1, [fp, #-0x50]
    // 0xcb36a8: LoadField: r2 = r0->field_1f
    //     0xcb36a8: ldur            x2, [x0, #0x1f]
    // 0xcb36ac: ldur            x3, [fp, #-0x20]
    // 0xcb36b0: cmp             x2, #0x3f
    // 0xcb36b4: b.hi            #0xcb38f8
    // 0xcb36b8: asr             x4, x3, x2
    // 0xcb36bc: mul             x2, x4, x1
    // 0xcb36c0: ldur            x4, [fp, #-0x30]
    // 0xcb36c4: LoadField: r5 = r4->field_13
    //     0xcb36c4: ldur            w5, [x4, #0x13]
    // 0xcb36c8: r6 = LoadInt32Instr(r5)
    //     0xcb36c8: sbfx            x6, x5, #1, #0x1f
    // 0xcb36cc: stur            x6, [fp, #-0x48]
    // 0xcb36d0: ldur            x9, [fp, #-0x38]
    // 0xcb36d4: mov             x8, x3
    // 0xcb36d8: mov             x7, x2
    // 0xcb36dc: ldur            x3, [fp, #-0x10]
    // 0xcb36e0: ldur            x5, [fp, #-8]
    // 0xcb36e4: ldur            x2, [fp, #-0x28]
    // 0xcb36e8: stur            x9, [fp, #-0x20]
    // 0xcb36ec: stur            x8, [fp, #-0x38]
    // 0xcb36f0: stur            x7, [fp, #-0x40]
    // 0xcb36f4: CheckStackOverflow
    //     0xcb36f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb36f8: cmp             SP, x16
    //     0xcb36fc: b.ls            #0xcb3924
    // 0xcb3700: cmp             x8, x2
    // 0xcb3704: b.ge            #0xcb38ac
    // 0xcb3708: r0 = _VP8LMultipliers()
    //     0xcb3708: bl              #0xcb3ca0  ; Allocate_VP8LMultipliersStub -> _VP8LMultipliers (size=0xc)
    // 0xcb370c: r4 = 6
    //     0xcb370c: movz            x4, #0x6
    // 0xcb3710: stur            x0, [fp, #-0x58]
    // 0xcb3714: r0 = AllocateUint8Array()
    //     0xcb3714: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xcb3718: mov             x4, x0
    // 0xcb371c: ldur            x3, [fp, #-0x58]
    // 0xcb3720: stur            x4, [fp, #-0x78]
    // 0xcb3724: StoreField: r3->field_7 = r4
    //     0xcb3724: stur            w4, [x3, #7]
    // 0xcb3728: ldur            x2, [fp, #-0x40]
    // 0xcb372c: ldur            x6, [fp, #-0x30]
    // 0xcb3730: r11 = 0
    //     0xcb3730: movz            x11, #0
    // 0xcb3734: ldur            x5, [fp, #-0x18]
    // 0xcb3738: ldur            x7, [fp, #-0x10]
    // 0xcb373c: ldur            x8, [fp, #-8]
    // 0xcb3740: ldur            x10, [fp, #-0x20]
    // 0xcb3744: r9 = 255
    //     0xcb3744: movz            x9, #0xff
    // 0xcb3748: stur            x11, [fp, #-0x70]
    // 0xcb374c: CheckStackOverflow
    //     0xcb374c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3750: cmp             SP, x16
    //     0xcb3754: b.ls            #0xcb392c
    // 0xcb3758: cmp             x11, x7
    // 0xcb375c: b.ge            #0xcb3848
    // 0xcb3760: tst             x11, x8
    // 0xcb3764: b.ne            #0xcb37d0
    // 0xcb3768: LoadField: r12 = r5->field_1b
    //     0xcb3768: ldur            w12, [x5, #0x1b]
    // 0xcb376c: DecompressPointer r12
    //     0xcb376c: add             x12, x12, HEAP, lsl #32
    // 0xcb3770: cmp             w12, NULL
    // 0xcb3774: b.eq            #0xcb3934
    // 0xcb3778: add             x13, x2, #1
    // 0xcb377c: LoadField: r0 = r12->field_13
    //     0xcb377c: ldur            w0, [x12, #0x13]
    // 0xcb3780: r1 = LoadInt32Instr(r0)
    //     0xcb3780: sbfx            x1, x0, #1, #0x1f
    // 0xcb3784: mov             x0, x1
    // 0xcb3788: mov             x1, x2
    // 0xcb378c: cmp             x1, x0
    // 0xcb3790: b.hs            #0xcb3938
    // 0xcb3794: ArrayLoad: r0 = r12[r2]  ; List_4
    //     0xcb3794: add             x16, x12, x2, lsl #2
    //     0xcb3798: ldur            w0, [x16, #0x17]
    // 0xcb379c: and             x1, x0, x9
    // 0xcb37a0: ubfx            x1, x1, #0, #0x20
    // 0xcb37a4: ArrayStore: r4[0] = r1  ; TypeUnknown_1
    //     0xcb37a4: strb            w1, [x4, #0x17]
    // 0xcb37a8: lsr             w1, w0, #8
    // 0xcb37ac: and             x2, x1, x9
    // 0xcb37b0: ubfx            x2, x2, #0, #0x20
    // 0xcb37b4: ArrayStore: r4[1] = r2  ; TypeUnknown_1
    //     0xcb37b4: strb            w2, [x4, #0x18]
    // 0xcb37b8: lsr             w1, w0, #0x10
    // 0xcb37bc: and             x0, x1, x9
    // 0xcb37c0: ubfx            x0, x0, #0, #0x20
    // 0xcb37c4: ArrayStore: r4[2] = r0  ; TypeUnknown_1
    //     0xcb37c4: strb            w0, [x4, #0x19]
    // 0xcb37c8: mov             x12, x13
    // 0xcb37cc: b               #0xcb37d4
    // 0xcb37d0: mov             x12, x2
    // 0xcb37d4: stur            x12, [fp, #-0x68]
    // 0xcb37d8: add             x13, x10, x11
    // 0xcb37dc: ldur            x0, [fp, #-0x48]
    // 0xcb37e0: mov             x1, x13
    // 0xcb37e4: stur            x13, [fp, #-0x60]
    // 0xcb37e8: cmp             x1, x0
    // 0xcb37ec: b.hs            #0xcb393c
    // 0xcb37f0: LoadField: r0 = r6->field_7
    //     0xcb37f0: ldur            x0, [x6, #7]
    // 0xcb37f4: add             x16, x0, x13, lsl #2
    // 0xcb37f8: ldr             w1, [x16]
    // 0xcb37fc: ubfx            x1, x1, #0, #0x20
    // 0xcb3800: mov             x2, x1
    // 0xcb3804: mov             x1, x3
    // 0xcb3808: r0 = transformColor()
    //     0xcb3808: bl              #0xcb3964  ; [package:image/src/formats/webp/vp8l_transform.dart] _VP8LMultipliers::transformColor
    // 0xcb380c: ldur            x1, [fp, #-0x30]
    // 0xcb3810: ldurb           w16, [x1, #-1]
    // 0xcb3814: tbnz            w16, #6, #0xcb3940
    // 0xcb3818: ubfx            x0, x0, #0, #0x20
    // 0xcb381c: LoadField: r2 = r1->field_7
    //     0xcb381c: ldur            x2, [x1, #7]
    // 0xcb3820: ldur            x3, [fp, #-0x60]
    // 0xcb3824: add             x4, x2, x3, lsl #2
    // 0xcb3828: str             w0, [x4]
    // 0xcb382c: ldur            x2, [fp, #-0x70]
    // 0xcb3830: add             x11, x2, #1
    // 0xcb3834: ldur            x2, [fp, #-0x68]
    // 0xcb3838: mov             x6, x1
    // 0xcb383c: ldur            x3, [fp, #-0x58]
    // 0xcb3840: ldur            x4, [fp, #-0x78]
    // 0xcb3844: b               #0xcb3734
    // 0xcb3848: mov             x1, x6
    // 0xcb384c: mov             x2, x7
    // 0xcb3850: mov             x3, x8
    // 0xcb3854: mov             x4, x10
    // 0xcb3858: ldur            x5, [fp, #-0x38]
    // 0xcb385c: add             x9, x4, x2
    // 0xcb3860: add             x8, x5, #1
    // 0xcb3864: tst             x8, x3
    // 0xcb3868: b.ne            #0xcb3880
    // 0xcb386c: ldur            x4, [fp, #-0x50]
    // 0xcb3870: ldur            x5, [fp, #-0x40]
    // 0xcb3874: add             x6, x5, x4
    // 0xcb3878: mov             x7, x6
    // 0xcb387c: b               #0xcb388c
    // 0xcb3880: ldur            x4, [fp, #-0x50]
    // 0xcb3884: ldur            x5, [fp, #-0x40]
    // 0xcb3888: mov             x7, x5
    // 0xcb388c: ldur            x0, [fp, #-0x18]
    // 0xcb3890: mov             x16, x4
    // 0xcb3894: mov             x4, x1
    // 0xcb3898: mov             x1, x16
    // 0xcb389c: mov             x5, x3
    // 0xcb38a0: mov             x3, x2
    // 0xcb38a4: ldur            x6, [fp, #-0x48]
    // 0xcb38a8: b               #0xcb36e4
    // 0xcb38ac: r0 = Null
    //     0xcb38ac: mov             x0, NULL
    // 0xcb38b0: LeaveFrame
    //     0xcb38b0: mov             SP, fp
    //     0xcb38b4: ldp             fp, lr, [SP], #0x10
    // 0xcb38b8: ret
    //     0xcb38b8: ret             
    // 0xcb38bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb38bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb38c0: b               #0xcb3674
    // 0xcb38c4: tbnz            x2, #0x3f, #0xcb38d0
    // 0xcb38c8: mov             x1, xzr
    // 0xcb38cc: b               #0xcb368c
    // 0xcb38d0: str             x2, [THR, #0x7a8]  ; THR::
    // 0xcb38d4: stp             x7, x8, [SP, #-0x10]!
    // 0xcb38d8: stp             x5, x6, [SP, #-0x10]!
    // 0xcb38dc: stp             x3, x4, [SP, #-0x10]!
    // 0xcb38e0: stp             x0, x2, [SP, #-0x10]!
    // 0xcb38e4: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb38e8: r4 = 0
    //     0xcb38e8: movz            x4, #0
    // 0xcb38ec: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb38f0: blr             lr
    // 0xcb38f4: brk             #0
    // 0xcb38f8: tbnz            x2, #0x3f, #0xcb3904
    // 0xcb38fc: asr             x4, x3, #0x3f
    // 0xcb3900: b               #0xcb36bc
    // 0xcb3904: str             x2, [THR, #0x7a8]  ; THR::
    // 0xcb3908: stp             x2, x3, [SP, #-0x10]!
    // 0xcb390c: stp             x0, x1, [SP, #-0x10]!
    // 0xcb3910: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb3914: r4 = 0
    //     0xcb3914: movz            x4, #0
    // 0xcb3918: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb391c: blr             lr
    // 0xcb3920: brk             #0
    // 0xcb3924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb3924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3928: b               #0xcb3700
    // 0xcb392c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb392c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb3930: b               #0xcb3758
    // 0xcb3934: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb3934: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb3938: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb3938: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb393c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb393c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb3940: stp             x0, x1, [SP, #-0x10]!
    // 0xcb3944: SaveReg r1
    //     0xcb3944: str             x1, [SP, #-8]!
    // 0xcb3948: r16 = 0
    //     0xcb3948: movz            x16, #0
    // 0xcb394c: SaveReg r16
    //     0xcb394c: str             x16, [SP, #-8]!
    // 0xcb3950: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb3954: r4 = 2
    //     0xcb3954: movz            x4, #0x2
    // 0xcb3958: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb395c: blr             lr
    // 0xcb3960: brk             #0
  }
  _ predictorInverseTransform(/* No info */) {
    // ** addr: 0xcb3d38, size: 0x7b0
    // 0xcb3d38: EnterFrame
    //     0xcb3d38: stp             fp, lr, [SP, #-0x10]!
    //     0xcb3d3c: mov             fp, SP
    // 0xcb3d40: AllocStack(0x98)
    //     0xcb3d40: sub             SP, SP, #0x98
    // 0xcb3d44: SetupParameters(VP8LTransform this /* r1 => r7, fp-0x18 */, dynamic _ /* r3 => r6, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r4, fp-0x30 */)
    //     0xcb3d44: mov             x7, x1
    //     0xcb3d48: mov             x4, x6
    //     0xcb3d4c: stur            x6, [fp, #-0x30]
    //     0xcb3d50: mov             x6, x3
    //     0xcb3d54: stur            x1, [fp, #-0x18]
    //     0xcb3d58: stur            x3, [fp, #-0x20]
    //     0xcb3d5c: stur            x5, [fp, #-0x28]
    // 0xcb3d60: CheckStackOverflow
    //     0xcb3d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3d64: cmp             SP, x16
    //     0xcb3d68: b.ls            #0xcb4350
    // 0xcb3d6c: LoadField: r8 = r7->field_b
    //     0xcb3d6c: ldur            x8, [x7, #0xb]
    // 0xcb3d70: stur            x8, [fp, #-0x10]
    // 0xcb3d74: cbnz            x2, #0xcb3ecc
    // 0xcb3d78: sub             x2, x4, #1
    // 0xcb3d7c: LoadField: r0 = r5->field_13
    //     0xcb3d7c: ldur            w0, [x5, #0x13]
    // 0xcb3d80: r9 = LoadInt32Instr(r0)
    //     0xcb3d80: sbfx            x9, x0, #1, #0x1f
    // 0xcb3d84: mov             x0, x9
    // 0xcb3d88: mov             x1, x2
    // 0xcb3d8c: stur            x9, [fp, #-8]
    // 0xcb3d90: cmp             x1, x0
    // 0xcb3d94: b.hs            #0xcb4358
    // 0xcb3d98: LoadField: r0 = r5->field_7
    //     0xcb3d98: ldur            x0, [x5, #7]
    // 0xcb3d9c: add             x16, x0, x2, lsl #2
    // 0xcb3da0: ldr             w1, [x16]
    // 0xcb3da4: lsl             w2, w1, #1
    // 0xcb3da8: tst             x1, #0xc0000000
    // 0xcb3dac: b.eq            #0xcb3ddc
    // 0xcb3db0: r2 = inline_Allocate_Mint()
    //     0xcb3db0: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xcb3db4: add             x2, x2, #0x10
    //     0xcb3db8: cmp             x0, x2
    //     0xcb3dbc: b.ls            #0xcb435c
    //     0xcb3dc0: str             x2, [THR, #0x50]  ; THR::top
    //     0xcb3dc4: sub             x2, x2, #0xf
    //     0xcb3dc8: movz            x0, #0xd15c
    //     0xcb3dcc: movk            x0, #0x3, lsl #16
    //     0xcb3dd0: stur            x0, [x2, #-1]
    // 0xcb3dd4: ubfx            x0, x1, #0, #0x20
    // 0xcb3dd8: StoreField: r2->field_7 = r0
    //     0xcb3dd8: stur            x0, [x2, #7]
    // 0xcb3ddc: mov             x1, x5
    // 0xcb3de0: r3 = 0
    //     0xcb3de0: movz            x3, #0
    // 0xcb3de4: r0 = _predictor0()
    //     0xcb3de4: bl              #0xcb45b0  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor0
    // 0xcb3de8: ldur            x1, [fp, #-0x28]
    // 0xcb3dec: ldur            x2, [fp, #-0x30]
    // 0xcb3df0: mov             x3, x0
    // 0xcb3df4: r0 = _addPixelsEq()
    //     0xcb3df4: bl              #0xcb44e8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_addPixelsEq
    // 0xcb3df8: ldur            x4, [fp, #-0x28]
    // 0xcb3dfc: ldur            x3, [fp, #-0x30]
    // 0xcb3e00: ldur            x5, [fp, #-0x10]
    // 0xcb3e04: r2 = 1
    //     0xcb3e04: movz            x2, #0x1
    // 0xcb3e08: r7 = 4278255360
    //     0xcb3e08: movz            x7, #0xff00
    //     0xcb3e0c: movk            x7, #0xff00, lsl #16
    // 0xcb3e10: r6 = 16711935
    //     0xcb3e10: movz            x6, #0xff
    //     0xcb3e14: movk            x6, #0xff, lsl #16
    // 0xcb3e18: CheckStackOverflow
    //     0xcb3e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3e1c: cmp             SP, x16
    //     0xcb3e20: b.ls            #0xcb4388
    // 0xcb3e24: cmp             x2, x5
    // 0xcb3e28: b.ge            #0xcb3ebc
    // 0xcb3e2c: add             x8, x3, x2
    // 0xcb3e30: sub             x9, x8, #1
    // 0xcb3e34: ldur            x0, [fp, #-8]
    // 0xcb3e38: mov             x1, x9
    // 0xcb3e3c: cmp             x1, x0
    // 0xcb3e40: b.hs            #0xcb4390
    // 0xcb3e44: LoadField: r0 = r4->field_7
    //     0xcb3e44: ldur            x0, [x4, #7]
    // 0xcb3e48: add             x16, x0, x9, lsl #2
    // 0xcb3e4c: ldr             w10, [x16]
    // 0xcb3e50: ldur            x0, [fp, #-8]
    // 0xcb3e54: mov             x1, x8
    // 0xcb3e58: cmp             x1, x0
    // 0xcb3e5c: b.hs            #0xcb4394
    // 0xcb3e60: LoadField: r0 = r4->field_7
    //     0xcb3e60: ldur            x0, [x4, #7]
    // 0xcb3e64: add             x16, x0, x8, lsl #2
    // 0xcb3e68: ldr             w1, [x16]
    // 0xcb3e6c: and             x0, x1, x7
    // 0xcb3e70: and             x9, x10, x7
    // 0xcb3e74: add             w11, w0, w9
    // 0xcb3e78: and             x0, x1, x6
    // 0xcb3e7c: and             x1, x10, x6
    // 0xcb3e80: add             w9, w0, w1
    // 0xcb3e84: and             x0, x11, x7
    // 0xcb3e88: and             x1, x9, x6
    // 0xcb3e8c: ubfx            x0, x0, #0, #0x20
    // 0xcb3e90: ubfx            x1, x1, #0, #0x20
    // 0xcb3e94: orr             x9, x0, x1
    // 0xcb3e98: ldurb           w16, [x4, #-1]
    // 0xcb3e9c: tbnz            w16, #6, #0xcb4398
    // 0xcb3ea0: ubfx            x9, x9, #0, #0x20
    // 0xcb3ea4: LoadField: r0 = r4->field_7
    //     0xcb3ea4: ldur            x0, [x4, #7]
    // 0xcb3ea8: add             x1, x0, x8, lsl #2
    // 0xcb3eac: str             w9, [x1]
    // 0xcb3eb0: add             x0, x2, #1
    // 0xcb3eb4: mov             x2, x0
    // 0xcb3eb8: b               #0xcb3e18
    // 0xcb3ebc: add             x0, x3, x5
    // 0xcb3ec0: mov             x3, x0
    // 0xcb3ec4: r8 = 1
    //     0xcb3ec4: movz            x8, #0x1
    // 0xcb3ec8: b               #0xcb3eec
    // 0xcb3ecc: mov             x3, x4
    // 0xcb3ed0: mov             x4, x5
    // 0xcb3ed4: mov             x5, x8
    // 0xcb3ed8: r7 = 4278255360
    //     0xcb3ed8: movz            x7, #0xff00
    //     0xcb3edc: movk            x7, #0xff00, lsl #16
    // 0xcb3ee0: r6 = 16711935
    //     0xcb3ee0: movz            x6, #0xff
    //     0xcb3ee4: movk            x6, #0xff, lsl #16
    // 0xcb3ee8: mov             x8, x2
    // 0xcb3eec: ldur            x0, [fp, #-0x18]
    // 0xcb3ef0: r1 = 1
    //     0xcb3ef0: movz            x1, #0x1
    // 0xcb3ef4: stur            x8, [fp, #-0x30]
    // 0xcb3ef8: stur            x3, [fp, #-0x38]
    // 0xcb3efc: LoadField: r2 = r0->field_1f
    //     0xcb3efc: ldur            x2, [x0, #0x1f]
    // 0xcb3f00: cmp             x2, #0x3f
    // 0xcb3f04: b.hi            #0xcb43c8
    // 0xcb3f08: lsl             x9, x1, x2
    // 0xcb3f0c: sub             x10, x9, #1
    // 0xcb3f10: mov             x1, x5
    // 0xcb3f14: stur            x10, [fp, #-8]
    // 0xcb3f18: r0 = _subSampleSize()
    //     0xcb3f18: bl              #0xcb3cac  ; [package:image/src/formats/webp/vp8l.dart] VP8L::_subSampleSize
    // 0xcb3f1c: mov             x3, x0
    // 0xcb3f20: ldur            x2, [fp, #-0x18]
    // 0xcb3f24: stur            x3, [fp, #-0x60]
    // 0xcb3f28: LoadField: r0 = r2->field_1f
    //     0xcb3f28: ldur            x0, [x2, #0x1f]
    // 0xcb3f2c: ldur            x1, [fp, #-0x30]
    // 0xcb3f30: cmp             x0, #0x3f
    // 0xcb3f34: b.hi            #0xcb4400
    // 0xcb3f38: asr             x4, x1, x0
    // 0xcb3f3c: mul             x0, x4, x3
    // 0xcb3f40: ldur            x4, [fp, #-0x28]
    // 0xcb3f44: LoadField: r5 = r4->field_13
    //     0xcb3f44: ldur            w5, [x4, #0x13]
    // 0xcb3f48: r6 = LoadInt32Instr(r5)
    //     0xcb3f48: sbfx            x6, x5, #1, #0x1f
    // 0xcb3f4c: stur            x6, [fp, #-0x58]
    // 0xcb3f50: ldur            x14, [fp, #-0x38]
    // 0xcb3f54: mov             x13, x1
    // 0xcb3f58: mov             x12, x0
    // 0xcb3f5c: ldur            x5, [fp, #-0x10]
    // 0xcb3f60: ldur            x9, [fp, #-8]
    // 0xcb3f64: r8 = 4278255360
    //     0xcb3f64: movz            x8, #0xff00
    //     0xcb3f68: movk            x8, #0xff00, lsl #16
    // 0xcb3f6c: r7 = 16711935
    //     0xcb3f6c: movz            x7, #0xff
    //     0xcb3f70: movk            x7, #0xff, lsl #16
    // 0xcb3f74: ldur            x10, [fp, #-0x20]
    // 0xcb3f78: r11 = 15
    //     0xcb3f78: movz            x11, #0xf
    // 0xcb3f7c: stur            x14, [fp, #-0x40]
    // 0xcb3f80: stur            x13, [fp, #-0x48]
    // 0xcb3f84: stur            x12, [fp, #-0x50]
    // 0xcb3f88: CheckStackOverflow
    //     0xcb3f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb3f8c: cmp             SP, x16
    //     0xcb3f90: b.ls            #0xcb442c
    // 0xcb3f94: cmp             x13, x10
    // 0xcb3f98: b.ge            #0xcb4340
    // 0xcb3f9c: sub             x1, x14, #1
    // 0xcb3fa0: mov             x0, x6
    // 0xcb3fa4: cmp             x1, x0
    // 0xcb3fa8: b.hs            #0xcb4434
    // 0xcb3fac: sub             x19, x14, x5
    // 0xcb3fb0: mov             x0, x6
    // 0xcb3fb4: mov             x1, x19
    // 0xcb3fb8: cmp             x1, x0
    // 0xcb3fbc: b.hs            #0xcb4438
    // 0xcb3fc0: LoadField: r0 = r4->field_7
    //     0xcb3fc0: ldur            x0, [x4, #7]
    // 0xcb3fc4: add             x16, x0, x19, lsl #2
    // 0xcb3fc8: ldr             w20, [x16]
    // 0xcb3fcc: mov             x0, x6
    // 0xcb3fd0: mov             x1, x14
    // 0xcb3fd4: cmp             x1, x0
    // 0xcb3fd8: b.hs            #0xcb443c
    // 0xcb3fdc: LoadField: r0 = r4->field_7
    //     0xcb3fdc: ldur            x0, [x4, #7]
    // 0xcb3fe0: add             x16, x0, x14, lsl #2
    // 0xcb3fe4: ldr             w1, [x16]
    // 0xcb3fe8: and             x0, x1, x8
    // 0xcb3fec: and             x19, x20, x8
    // 0xcb3ff0: add             w23, w0, w19
    // 0xcb3ff4: and             x0, x1, x7
    // 0xcb3ff8: and             x1, x20, x7
    // 0xcb3ffc: add             w19, w0, w1
    // 0xcb4000: and             x0, x23, x8
    // 0xcb4004: and             x1, x19, x7
    // 0xcb4008: ubfx            x0, x0, #0, #0x20
    // 0xcb400c: ubfx            x1, x1, #0, #0x20
    // 0xcb4010: orr             x19, x0, x1
    // 0xcb4014: ldurb           w16, [x4, #-1]
    // 0xcb4018: tbnz            w16, #6, #0xcb4440
    // 0xcb401c: ubfx            x19, x19, #0, #0x20
    // 0xcb4020: LoadField: r0 = r4->field_7
    //     0xcb4020: ldur            x0, [x4, #7]
    // 0xcb4024: add             x1, x0, x14, lsl #2
    // 0xcb4028: str             w19, [x1]
    // 0xcb402c: LoadField: r19 = r2->field_1b
    //     0xcb402c: ldur            w19, [x2, #0x1b]
    // 0xcb4030: DecompressPointer r19
    //     0xcb4030: add             x19, x19, HEAP, lsl #32
    // 0xcb4034: cmp             w19, NULL
    // 0xcb4038: b.eq            #0xcb447c
    // 0xcb403c: add             x20, x12, #1
    // 0xcb4040: stur            x20, [fp, #-0x38]
    // 0xcb4044: LoadField: r0 = r19->field_13
    //     0xcb4044: ldur            w0, [x19, #0x13]
    // 0xcb4048: r1 = LoadInt32Instr(r0)
    //     0xcb4048: sbfx            x1, x0, #1, #0x1f
    // 0xcb404c: mov             x0, x1
    // 0xcb4050: mov             x1, x12
    // 0xcb4054: cmp             x1, x0
    // 0xcb4058: b.hs            #0xcb4480
    // 0xcb405c: ArrayLoad: r0 = r19[r12]  ; List_4
    //     0xcb405c: add             x16, x19, x12, lsl #2
    //     0xcb4060: ldur            w0, [x16, #0x17]
    // 0xcb4064: lsr             w1, w0, #8
    // 0xcb4068: and             x0, x1, x11
    // 0xcb406c: stur            x0, [fp, #-0x30]
    // 0xcb4070: r0 = InitLateStaticField(0x1458) // [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictors
    //     0xcb4070: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xcb4074: ldr             x0, [x0, #0x28b0]
    //     0xcb4078: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xcb407c: cmp             w0, w16
    //     0xcb4080: b.ne            #0xcb4090
    //     0xcb4084: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4aca0] Field <VP8LTransform._predictors@1496369473>: static late final (offset: 0x1458)
    //     0xcb4088: ldr             x2, [x2, #0xca0]
    //     0xcb408c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xcb4090: mov             x2, x0
    // 0xcb4094: stur            x2, [fp, #-0x78]
    // 0xcb4098: LoadField: r0 = r2->field_b
    //     0xcb4098: ldur            w0, [x2, #0xb]
    // 0xcb409c: r1 = LoadInt32Instr(r0)
    //     0xcb409c: sbfx            x1, x0, #1, #0x1f
    // 0xcb40a0: ldur            x3, [fp, #-0x30]
    // 0xcb40a4: ubfx            x3, x3, #0, #0x20
    // 0xcb40a8: mov             x0, x1
    // 0xcb40ac: mov             x1, x3
    // 0xcb40b0: cmp             x1, x0
    // 0xcb40b4: b.hs            #0xcb4484
    // 0xcb40b8: LoadField: r0 = r2->field_f
    //     0xcb40b8: ldur            w0, [x2, #0xf]
    // 0xcb40bc: DecompressPointer r0
    //     0xcb40bc: add             x0, x0, HEAP, lsl #32
    // 0xcb40c0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xcb40c0: add             x16, x0, x3, lsl #2
    //     0xcb40c4: ldur            w1, [x16, #0xf]
    // 0xcb40c8: DecompressPointer r1
    //     0xcb40c8: add             x1, x1, HEAP, lsl #32
    // 0xcb40cc: ldur            x10, [fp, #-0x38]
    // 0xcb40d0: mov             x0, x1
    // 0xcb40d4: ldur            x4, [fp, #-0x28]
    // 0xcb40d8: r9 = 1
    //     0xcb40d8: movz            x9, #0x1
    // 0xcb40dc: ldur            x3, [fp, #-0x18]
    // 0xcb40e0: ldur            x5, [fp, #-0x10]
    // 0xcb40e4: ldur            x6, [fp, #-8]
    // 0xcb40e8: ldur            x8, [fp, #-0x40]
    // 0xcb40ec: r7 = 15
    //     0xcb40ec: movz            x7, #0xf
    // 0xcb40f0: stur            x9, [fp, #-0x70]
    // 0xcb40f4: CheckStackOverflow
    //     0xcb40f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb40f8: cmp             SP, x16
    //     0xcb40fc: b.ls            #0xcb4488
    // 0xcb4100: cmp             x9, x5
    // 0xcb4104: b.ge            #0xcb42c8
    // 0xcb4108: tst             x9, x6
    // 0xcb410c: b.ne            #0xcb418c
    // 0xcb4110: LoadField: r11 = r3->field_1b
    //     0xcb4110: ldur            w11, [x3, #0x1b]
    // 0xcb4114: DecompressPointer r11
    //     0xcb4114: add             x11, x11, HEAP, lsl #32
    // 0xcb4118: cmp             w11, NULL
    // 0xcb411c: b.eq            #0xcb4490
    // 0xcb4120: add             x12, x10, #1
    // 0xcb4124: LoadField: r0 = r11->field_13
    //     0xcb4124: ldur            w0, [x11, #0x13]
    // 0xcb4128: r1 = LoadInt32Instr(r0)
    //     0xcb4128: sbfx            x1, x0, #1, #0x1f
    // 0xcb412c: mov             x0, x1
    // 0xcb4130: mov             x1, x10
    // 0xcb4134: cmp             x1, x0
    // 0xcb4138: b.hs            #0xcb4494
    // 0xcb413c: ArrayLoad: r0 = r11[r10]  ; List_4
    //     0xcb413c: add             x16, x11, x10, lsl #2
    //     0xcb4140: ldur            w0, [x16, #0x17]
    // 0xcb4144: lsr             w1, w0, #8
    // 0xcb4148: and             x0, x1, x7
    // 0xcb414c: LoadField: r1 = r2->field_b
    //     0xcb414c: ldur            w1, [x2, #0xb]
    // 0xcb4150: r10 = LoadInt32Instr(r1)
    //     0xcb4150: sbfx            x10, x1, #1, #0x1f
    // 0xcb4154: mov             x11, x0
    // 0xcb4158: ubfx            x11, x11, #0, #0x20
    // 0xcb415c: mov             x0, x10
    // 0xcb4160: mov             x1, x11
    // 0xcb4164: cmp             x1, x0
    // 0xcb4168: b.hs            #0xcb4498
    // 0xcb416c: LoadField: r0 = r2->field_f
    //     0xcb416c: ldur            w0, [x2, #0xf]
    // 0xcb4170: DecompressPointer r0
    //     0xcb4170: add             x0, x0, HEAP, lsl #32
    // 0xcb4174: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xcb4174: add             x16, x0, x11, lsl #2
    //     0xcb4178: ldur            w1, [x16, #0xf]
    // 0xcb417c: DecompressPointer r1
    //     0xcb417c: add             x1, x1, HEAP, lsl #32
    // 0xcb4180: mov             x11, x12
    // 0xcb4184: mov             x10, x1
    // 0xcb4188: b               #0xcb4194
    // 0xcb418c: mov             x11, x10
    // 0xcb4190: mov             x10, x0
    // 0xcb4194: stur            x11, [fp, #-0x38]
    // 0xcb4198: stur            x10, [fp, #-0x68]
    // 0xcb419c: add             x12, x8, x9
    // 0xcb41a0: stur            x12, [fp, #-0x30]
    // 0xcb41a4: sub             x13, x12, #1
    // 0xcb41a8: ldur            x0, [fp, #-0x58]
    // 0xcb41ac: mov             x1, x13
    // 0xcb41b0: cmp             x1, x0
    // 0xcb41b4: b.hs            #0xcb449c
    // 0xcb41b8: LoadField: r0 = r4->field_7
    //     0xcb41b8: ldur            x0, [x4, #7]
    // 0xcb41bc: add             x16, x0, x13, lsl #2
    // 0xcb41c0: ldr             w1, [x16]
    // 0xcb41c4: sub             x13, x12, x5
    // 0xcb41c8: lsl             w14, w1, #1
    // 0xcb41cc: tst             x1, #0xc0000000
    // 0xcb41d0: b.eq            #0xcb4200
    // 0xcb41d4: r14 = inline_Allocate_Mint()
    //     0xcb41d4: ldp             x14, x0, [THR, #0x50]  ; THR::top
    //     0xcb41d8: add             x14, x14, #0x10
    //     0xcb41dc: cmp             x0, x14
    //     0xcb41e0: b.ls            #0xcb44a0
    //     0xcb41e4: str             x14, [THR, #0x50]  ; THR::top
    //     0xcb41e8: sub             x14, x14, #0xf
    //     0xcb41ec: movz            x0, #0xd15c
    //     0xcb41f0: movk            x0, #0x3, lsl #16
    //     0xcb41f4: stur            x0, [x14, #-1]
    // 0xcb41f8: ubfx            x0, x1, #0, #0x20
    // 0xcb41fc: StoreField: r14->field_7 = r0
    //     0xcb41fc: stur            x0, [x14, #7]
    // 0xcb4200: r0 = BoxInt64Instr(r13)
    //     0xcb4200: sbfiz           x0, x13, #1, #0x1f
    //     0xcb4204: cmp             x13, x0, asr #1
    //     0xcb4208: b.eq            #0xcb4214
    //     0xcb420c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb4210: stur            x13, [x0, #7]
    // 0xcb4214: stp             x4, x10, [SP, #0x10]
    // 0xcb4218: stp             x0, x14, [SP]
    // 0xcb421c: mov             x0, x10
    // 0xcb4220: ClosureCall
    //     0xcb4220: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xcb4224: ldur            x2, [x0, #0x1f]
    //     0xcb4228: blr             x2
    // 0xcb422c: mov             x2, x0
    // 0xcb4230: ldur            x0, [fp, #-0x58]
    // 0xcb4234: ldur            x1, [fp, #-0x30]
    // 0xcb4238: cmp             x1, x0
    // 0xcb423c: b.hs            #0xcb44e4
    // 0xcb4240: ldur            x1, [fp, #-0x28]
    // 0xcb4244: LoadField: r3 = r1->field_7
    //     0xcb4244: ldur            x3, [x1, #7]
    // 0xcb4248: ldur            x4, [fp, #-0x30]
    // 0xcb424c: add             x16, x3, x4, lsl #2
    // 0xcb4250: ldr             w5, [x16]
    // 0xcb4254: r3 = 4278255360
    //     0xcb4254: movz            x3, #0xff00
    //     0xcb4258: movk            x3, #0xff00, lsl #16
    // 0xcb425c: and             x6, x5, x3
    // 0xcb4260: r7 = LoadInt32Instr(r2)
    //     0xcb4260: sbfx            x7, x2, #1, #0x1f
    //     0xcb4264: tbz             w2, #0, #0xcb426c
    //     0xcb4268: ldur            x7, [x2, #7]
    // 0xcb426c: and             x2, x7, x3
    // 0xcb4270: add             w8, w6, w2
    // 0xcb4274: r2 = 16711935
    //     0xcb4274: movz            x2, #0xff
    //     0xcb4278: movk            x2, #0xff, lsl #16
    // 0xcb427c: and             x6, x5, x2
    // 0xcb4280: and             x5, x7, x2
    // 0xcb4284: add             w7, w6, w5
    // 0xcb4288: and             x5, x8, x3
    // 0xcb428c: and             x6, x7, x2
    // 0xcb4290: ubfx            x5, x5, #0, #0x20
    // 0xcb4294: ubfx            x6, x6, #0, #0x20
    // 0xcb4298: orr             x7, x5, x6
    // 0xcb429c: ubfx            x7, x7, #0, #0x20
    // 0xcb42a0: LoadField: r5 = r1->field_7
    //     0xcb42a0: ldur            x5, [x1, #7]
    // 0xcb42a4: add             x6, x5, x4, lsl #2
    // 0xcb42a8: str             w7, [x6]
    // 0xcb42ac: ldur            x4, [fp, #-0x70]
    // 0xcb42b0: add             x9, x4, #1
    // 0xcb42b4: ldur            x10, [fp, #-0x38]
    // 0xcb42b8: ldur            x0, [fp, #-0x68]
    // 0xcb42bc: mov             x4, x1
    // 0xcb42c0: ldur            x2, [fp, #-0x78]
    // 0xcb42c4: b               #0xcb40dc
    // 0xcb42c8: mov             x1, x4
    // 0xcb42cc: mov             x4, x5
    // 0xcb42d0: mov             x5, x6
    // 0xcb42d4: mov             x6, x8
    // 0xcb42d8: ldur            x7, [fp, #-0x48]
    // 0xcb42dc: r3 = 4278255360
    //     0xcb42dc: movz            x3, #0xff00
    //     0xcb42e0: movk            x3, #0xff00, lsl #16
    // 0xcb42e4: r2 = 16711935
    //     0xcb42e4: movz            x2, #0xff
    //     0xcb42e8: movk            x2, #0xff, lsl #16
    // 0xcb42ec: add             x14, x6, x4
    // 0xcb42f0: add             x13, x7, #1
    // 0xcb42f4: tst             x13, x5
    // 0xcb42f8: b.ne            #0xcb4310
    // 0xcb42fc: ldur            x6, [fp, #-0x60]
    // 0xcb4300: ldur            x7, [fp, #-0x50]
    // 0xcb4304: add             x8, x7, x6
    // 0xcb4308: mov             x12, x8
    // 0xcb430c: b               #0xcb431c
    // 0xcb4310: ldur            x6, [fp, #-0x60]
    // 0xcb4314: ldur            x7, [fp, #-0x50]
    // 0xcb4318: mov             x12, x7
    // 0xcb431c: mov             x7, x2
    // 0xcb4320: ldur            x2, [fp, #-0x18]
    // 0xcb4324: mov             x9, x5
    // 0xcb4328: mov             x5, x4
    // 0xcb432c: mov             x4, x1
    // 0xcb4330: mov             x8, x3
    // 0xcb4334: mov             x3, x6
    // 0xcb4338: ldur            x6, [fp, #-0x58]
    // 0xcb433c: b               #0xcb3f74
    // 0xcb4340: r0 = Null
    //     0xcb4340: mov             x0, NULL
    // 0xcb4344: LeaveFrame
    //     0xcb4344: mov             SP, fp
    //     0xcb4348: ldp             fp, lr, [SP], #0x10
    // 0xcb434c: ret
    //     0xcb434c: ret             
    // 0xcb4350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4354: b               #0xcb3d6c
    // 0xcb4358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4358: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb435c: stp             x8, x9, [SP, #-0x10]!
    // 0xcb4360: stp             x6, x7, [SP, #-0x10]!
    // 0xcb4364: stp             x4, x5, [SP, #-0x10]!
    // 0xcb4368: SaveReg r1
    //     0xcb4368: str             x1, [SP, #-8]!
    // 0xcb436c: r0 = AllocateMint()
    //     0xcb436c: bl              #0xec22a8  ; AllocateMintStub
    // 0xcb4370: mov             x2, x0
    // 0xcb4374: RestoreReg r1
    //     0xcb4374: ldr             x1, [SP], #8
    // 0xcb4378: ldp             x4, x5, [SP], #0x10
    // 0xcb437c: ldp             x6, x7, [SP], #0x10
    // 0xcb4380: ldp             x8, x9, [SP], #0x10
    // 0xcb4384: b               #0xcb3dd4
    // 0xcb4388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb438c: b               #0xcb3e24
    // 0xcb4390: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4390: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4394: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4394: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4398: stp             x8, x9, [SP, #-0x10]!
    // 0xcb439c: stp             x6, x7, [SP, #-0x10]!
    // 0xcb43a0: stp             x4, x5, [SP, #-0x10]!
    // 0xcb43a4: stp             x2, x3, [SP, #-0x10]!
    // 0xcb43a8: SaveReg r4
    //     0xcb43a8: str             x4, [SP, #-8]!
    // 0xcb43ac: r16 = 0
    //     0xcb43ac: movz            x16, #0
    // 0xcb43b0: SaveReg r16
    //     0xcb43b0: str             x16, [SP, #-8]!
    // 0xcb43b4: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb43b8: r4 = 2
    //     0xcb43b8: movz            x4, #0x2
    // 0xcb43bc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb43c0: blr             lr
    // 0xcb43c4: brk             #0
    // 0xcb43c8: tbnz            x2, #0x3f, #0xcb43d4
    // 0xcb43cc: mov             x9, xzr
    // 0xcb43d0: b               #0xcb3f0c
    // 0xcb43d4: str             x2, [THR, #0x7a8]  ; THR::
    // 0xcb43d8: stp             x7, x8, [SP, #-0x10]!
    // 0xcb43dc: stp             x5, x6, [SP, #-0x10]!
    // 0xcb43e0: stp             x3, x4, [SP, #-0x10]!
    // 0xcb43e4: stp             x1, x2, [SP, #-0x10]!
    // 0xcb43e8: SaveReg r0
    //     0xcb43e8: str             x0, [SP, #-8]!
    // 0xcb43ec: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb43f0: r4 = 0
    //     0xcb43f0: movz            x4, #0
    // 0xcb43f4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb43f8: blr             lr
    // 0xcb43fc: brk             #0
    // 0xcb4400: tbnz            x0, #0x3f, #0xcb440c
    // 0xcb4404: asr             x4, x1, #0x3f
    // 0xcb4408: b               #0xcb3f3c
    // 0xcb440c: str             x0, [THR, #0x7a8]  ; THR::
    // 0xcb4410: stp             x2, x3, [SP, #-0x10]!
    // 0xcb4414: stp             x0, x1, [SP, #-0x10]!
    // 0xcb4418: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0xcb441c: r4 = 0
    //     0xcb441c: movz            x4, #0
    // 0xcb4420: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb4424: blr             lr
    // 0xcb4428: brk             #0
    // 0xcb442c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb442c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4430: b               #0xcb3f94
    // 0xcb4434: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4434: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4438: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb443c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb443c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4440: stp             x14, x19, [SP, #-0x10]!
    // 0xcb4444: stp             x12, x13, [SP, #-0x10]!
    // 0xcb4448: stp             x10, x11, [SP, #-0x10]!
    // 0xcb444c: stp             x8, x9, [SP, #-0x10]!
    // 0xcb4450: stp             x6, x7, [SP, #-0x10]!
    // 0xcb4454: stp             x4, x5, [SP, #-0x10]!
    // 0xcb4458: stp             x2, x3, [SP, #-0x10]!
    // 0xcb445c: SaveReg r4
    //     0xcb445c: str             x4, [SP, #-8]!
    // 0xcb4460: r16 = 0
    //     0xcb4460: movz            x16, #0
    // 0xcb4464: SaveReg r16
    //     0xcb4464: str             x16, [SP, #-8]!
    // 0xcb4468: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb446c: r4 = 2
    //     0xcb446c: movz            x4, #0x2
    // 0xcb4470: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb4474: blr             lr
    // 0xcb4478: brk             #0
    // 0xcb447c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb447c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb4480: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4480: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4484: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb448c: b               #0xcb4100
    // 0xcb4490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xcb4490: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xcb4494: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4494: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4498: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4498: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb449c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb449c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb44a0: stp             x12, x13, [SP, #-0x10]!
    // 0xcb44a4: stp             x10, x11, [SP, #-0x10]!
    // 0xcb44a8: stp             x8, x9, [SP, #-0x10]!
    // 0xcb44ac: stp             x6, x7, [SP, #-0x10]!
    // 0xcb44b0: stp             x4, x5, [SP, #-0x10]!
    // 0xcb44b4: stp             x2, x3, [SP, #-0x10]!
    // 0xcb44b8: SaveReg r1
    //     0xcb44b8: str             x1, [SP, #-8]!
    // 0xcb44bc: r0 = AllocateMint()
    //     0xcb44bc: bl              #0xec22a8  ; AllocateMintStub
    // 0xcb44c0: mov             x14, x0
    // 0xcb44c4: RestoreReg r1
    //     0xcb44c4: ldr             x1, [SP], #8
    // 0xcb44c8: ldp             x2, x3, [SP], #0x10
    // 0xcb44cc: ldp             x4, x5, [SP], #0x10
    // 0xcb44d0: ldp             x6, x7, [SP], #0x10
    // 0xcb44d4: ldp             x8, x9, [SP], #0x10
    // 0xcb44d8: ldp             x10, x11, [SP], #0x10
    // 0xcb44dc: ldp             x12, x13, [SP], #0x10
    // 0xcb44e0: b               #0xcb41f8
    // 0xcb44e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb44e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _addPixelsEq(/* No info */) {
    // ** addr: 0xcb44e8, size: 0xc8
    // 0xcb44e8: EnterFrame
    //     0xcb44e8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb44ec: mov             fp, SP
    // 0xcb44f0: r5 = 4278255360
    //     0xcb44f0: movz            x5, #0xff00
    //     0xcb44f4: movk            x5, #0xff00, lsl #16
    // 0xcb44f8: r4 = 16711935
    //     0xcb44f8: movz            x4, #0xff
    //     0xcb44fc: movk            x4, #0xff, lsl #16
    // 0xcb4500: mov             x6, x1
    // 0xcb4504: LoadField: r7 = r6->field_13
    //     0xcb4504: ldur            w7, [x6, #0x13]
    // 0xcb4508: r0 = LoadInt32Instr(r7)
    //     0xcb4508: sbfx            x0, x7, #1, #0x1f
    // 0xcb450c: mov             x1, x2
    // 0xcb4510: cmp             x1, x0
    // 0xcb4514: b.hs            #0xcb4584
    // 0xcb4518: LoadField: r1 = r6->field_7
    //     0xcb4518: ldur            x1, [x6, #7]
    // 0xcb451c: add             x16, x1, x2, lsl #2
    // 0xcb4520: ldr             w7, [x16]
    // 0xcb4524: and             x1, x7, x5
    // 0xcb4528: mov             x8, x3
    // 0xcb452c: ubfx            x8, x8, #0, #0x20
    // 0xcb4530: and             x9, x8, x5
    // 0xcb4534: add             w8, w1, w9
    // 0xcb4538: and             x1, x7, x4
    // 0xcb453c: ubfx            x3, x3, #0, #0x20
    // 0xcb4540: and             x7, x3, x4
    // 0xcb4544: add             w3, w1, w7
    // 0xcb4548: and             x1, x8, x5
    // 0xcb454c: and             x5, x3, x4
    // 0xcb4550: ubfx            x1, x1, #0, #0x20
    // 0xcb4554: ubfx            x5, x5, #0, #0x20
    // 0xcb4558: orr             x3, x1, x5
    // 0xcb455c: ldurb           w16, [x6, #-1]
    // 0xcb4560: tbnz            w16, #6, #0xcb4588
    // 0xcb4564: ubfx            x3, x3, #0, #0x20
    // 0xcb4568: LoadField: r1 = r6->field_7
    //     0xcb4568: ldur            x1, [x6, #7]
    // 0xcb456c: add             x4, x1, x2, lsl #2
    // 0xcb4570: str             w3, [x4]
    // 0xcb4574: r0 = Null
    //     0xcb4574: mov             x0, NULL
    // 0xcb4578: LeaveFrame
    //     0xcb4578: mov             SP, fp
    //     0xcb457c: ldp             fp, lr, [SP], #0x10
    // 0xcb4580: ret
    //     0xcb4580: ret             
    // 0xcb4584: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4584: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4588: stp             x3, x6, [SP, #-0x10]!
    // 0xcb458c: SaveReg r2
    //     0xcb458c: str             x2, [SP, #-8]!
    // 0xcb4590: SaveReg r6
    //     0xcb4590: str             x6, [SP, #-8]!
    // 0xcb4594: r16 = 0
    //     0xcb4594: movz            x16, #0
    // 0xcb4598: SaveReg r16
    //     0xcb4598: str             x16, [SP, #-8]!
    // 0xcb459c: ldr             x5, [THR, #0x428]  ; THR::WriteError
    // 0xcb45a0: r4 = 2
    //     0xcb45a0: movz            x4, #0x2
    // 0xcb45a4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xcb45a8: blr             lr
    // 0xcb45ac: brk             #0
  }
  static _ _predictor0(/* No info */) {
    // ** addr: 0xcb45b0, size: 0x8
    // 0xcb45b0: r0 = 4278190080
    //     0xcb45b0: orr             x0, xzr, #0xff000000
    // 0xcb45b4: ret
    //     0xcb45b4: ret             
  }
  [closure] static int _predictor0(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb45b8, size: 0xc
    // 0xcb45b8: r0 = 4278190080
    //     0xcb45b8: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4ad20] 0xff000000
    //     0xcb45bc: ldr             x0, [x0, #0xd20]
    // 0xcb45c0: ret
    //     0xcb45c0: ret             
  }
  static List<(dynamic, Uint32List, int, int) => int> _predictors() {
    // ** addr: 0xcb45c4, size: 0x10c
    // 0xcb45c4: EnterFrame
    //     0xcb45c4: stp             fp, lr, [SP, #-0x10]!
    //     0xcb45c8: mov             fp, SP
    // 0xcb45cc: AllocStack(0x8)
    //     0xcb45cc: sub             SP, SP, #8
    // 0xcb45d0: r0 = 32
    //     0xcb45d0: movz            x0, #0x20
    // 0xcb45d4: mov             x2, x0
    // 0xcb45d8: r1 = <(dynamic this, Uint32List, int, int) => int>
    //     0xcb45d8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4aca8] TypeArguments: <(dynamic this, Uint32List, int, int) => int>
    //     0xcb45dc: ldr             x1, [x1, #0xca8]
    // 0xcb45e0: r0 = AllocateArray()
    //     0xcb45e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xcb45e4: stur            x0, [fp, #-8]
    // 0xcb45e8: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static.
    //     0xcb45e8: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acb0] Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static. (0x7e54fb6b45b8)
    //     0xcb45ec: ldr             x16, [x16, #0xcb0]
    // 0xcb45f0: StoreField: r0->field_f = r16
    //     0xcb45f0: stur            w16, [x0, #0xf]
    // 0xcb45f4: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor1@1496369473': static.
    //     0xcb45f4: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acb8] Closure: (Uint32List, int, int) => int from Function '_predictor1@1496369473': static. (0x7e54fb28bacc)
    //     0xcb45f8: ldr             x16, [x16, #0xcb8]
    // 0xcb45fc: StoreField: r0->field_13 = r16
    //     0xcb45fc: stur            w16, [x0, #0x13]
    // 0xcb4600: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor2@1496369473': static.
    //     0xcb4600: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acc0] Closure: (Uint32List, int, int) => int from Function '_predictor2@1496369473': static. (0x7e54fb6b55f4)
    //     0xcb4604: ldr             x16, [x16, #0xcc0]
    // 0xcb4608: ArrayStore: r0[0] = r16  ; List_4
    //     0xcb4608: stur            w16, [x0, #0x17]
    // 0xcb460c: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor3@1496369473': static.
    //     0xcb460c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acc8] Closure: (Uint32List, int, int) => int from Function '_predictor3@1496369473': static. (0x7e54fb6b5558)
    //     0xcb4610: ldr             x16, [x16, #0xcc8]
    // 0xcb4614: StoreField: r0->field_1b = r16
    //     0xcb4614: stur            w16, [x0, #0x1b]
    // 0xcb4618: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor4@1496369473': static.
    //     0xcb4618: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acd0] Closure: (Uint32List, int, int) => int from Function '_predictor4@1496369473': static. (0x7e54fb6b54bc)
    //     0xcb461c: ldr             x16, [x16, #0xcd0]
    // 0xcb4620: StoreField: r0->field_1f = r16
    //     0xcb4620: stur            w16, [x0, #0x1f]
    // 0xcb4624: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor5@1496369473': static.
    //     0xcb4624: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acd8] Closure: (Uint32List, int, int) => int from Function '_predictor5@1496369473': static. (0x7e54fb6b5358)
    //     0xcb4628: ldr             x16, [x16, #0xcd8]
    // 0xcb462c: StoreField: r0->field_23 = r16
    //     0xcb462c: stur            w16, [x0, #0x23]
    // 0xcb4630: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor6@1496369473': static.
    //     0xcb4630: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ace0] Closure: (Uint32List, int, int) => int from Function '_predictor6@1496369473': static. (0x7e54fb6b528c)
    //     0xcb4634: ldr             x16, [x16, #0xce0]
    // 0xcb4638: StoreField: r0->field_27 = r16
    //     0xcb4638: stur            w16, [x0, #0x27]
    // 0xcb463c: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor7@1496369473': static.
    //     0xcb463c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ace8] Closure: (Uint32List, int, int) => int from Function '_predictor7@1496369473': static. (0x7e54fb6b51c4)
    //     0xcb4640: ldr             x16, [x16, #0xce8]
    // 0xcb4644: StoreField: r0->field_2b = r16
    //     0xcb4644: stur            w16, [x0, #0x2b]
    // 0xcb4648: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor8@1496369473': static.
    //     0xcb4648: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acf0] Closure: (Uint32List, int, int) => int from Function '_predictor8@1496369473': static. (0x7e54fb6b50e0)
    //     0xcb464c: ldr             x16, [x16, #0xcf0]
    // 0xcb4650: StoreField: r0->field_2f = r16
    //     0xcb4650: stur            w16, [x0, #0x2f]
    // 0xcb4654: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor9@1496369473': static.
    //     0xcb4654: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acf8] Closure: (Uint32List, int, int) => int from Function '_predictor9@1496369473': static. (0x7e54fb6b4ffc)
    //     0xcb4658: ldr             x16, [x16, #0xcf8]
    // 0xcb465c: StoreField: r0->field_33 = r16
    //     0xcb465c: stur            w16, [x0, #0x33]
    // 0xcb4660: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor10@1496369473': static.
    //     0xcb4660: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ad00] Closure: (Uint32List, int, int) => int from Function '_predictor10@1496369473': static. (0x7e54fb6b4e48)
    //     0xcb4664: ldr             x16, [x16, #0xd00]
    // 0xcb4668: StoreField: r0->field_37 = r16
    //     0xcb4668: stur            w16, [x0, #0x37]
    // 0xcb466c: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor11@1496369473': static.
    //     0xcb466c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ad08] Closure: (Uint32List, int, int) => int from Function '_predictor11@1496369473': static. (0x7e54fb6b4ba8)
    //     0xcb4670: ldr             x16, [x16, #0xd08]
    // 0xcb4674: StoreField: r0->field_3b = r16
    //     0xcb4674: stur            w16, [x0, #0x3b]
    // 0xcb4678: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor12@1496369473': static.
    //     0xcb4678: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ad10] Closure: (Uint32List, int, int) => int from Function '_predictor12@1496369473': static. (0x7e54fb6b4958)
    //     0xcb467c: ldr             x16, [x16, #0xd10]
    // 0xcb4680: StoreField: r0->field_3f = r16
    //     0xcb4680: stur            w16, [x0, #0x3f]
    // 0xcb4684: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor13@1496369473': static.
    //     0xcb4684: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4ad18] Closure: (Uint32List, int, int) => int from Function '_predictor13@1496369473': static. (0x7e54fb6b46d0)
    //     0xcb4688: ldr             x16, [x16, #0xd18]
    // 0xcb468c: StoreField: r0->field_43 = r16
    //     0xcb468c: stur            w16, [x0, #0x43]
    // 0xcb4690: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static.
    //     0xcb4690: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acb0] Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static. (0x7e54fb6b45b8)
    //     0xcb4694: ldr             x16, [x16, #0xcb0]
    // 0xcb4698: StoreField: r0->field_47 = r16
    //     0xcb4698: stur            w16, [x0, #0x47]
    // 0xcb469c: r16 = Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static.
    //     0xcb469c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4acb0] Closure: (Uint32List, int, int) => int from Function '_predictor0@1496369473': static. (0x7e54fb6b45b8)
    //     0xcb46a0: ldr             x16, [x16, #0xcb0]
    // 0xcb46a4: StoreField: r0->field_4b = r16
    //     0xcb46a4: stur            w16, [x0, #0x4b]
    // 0xcb46a8: r1 = <(dynamic this, Uint32List, int, int) => int>
    //     0xcb46a8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4aca8] TypeArguments: <(dynamic this, Uint32List, int, int) => int>
    //     0xcb46ac: ldr             x1, [x1, #0xca8]
    // 0xcb46b0: r0 = AllocateGrowableArray()
    //     0xcb46b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xcb46b4: ldur            x1, [fp, #-8]
    // 0xcb46b8: StoreField: r0->field_f = r1
    //     0xcb46b8: stur            w1, [x0, #0xf]
    // 0xcb46bc: r1 = 32
    //     0xcb46bc: movz            x1, #0x20
    // 0xcb46c0: StoreField: r0->field_b = r1
    //     0xcb46c0: stur            w1, [x0, #0xb]
    // 0xcb46c4: LeaveFrame
    //     0xcb46c4: mov             SP, fp
    //     0xcb46c8: ldp             fp, lr, [SP], #0x10
    // 0xcb46cc: ret
    //     0xcb46cc: ret             
  }
  [closure] static int _predictor13(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb46d0, size: 0x50
    // 0xcb46d0: EnterFrame
    //     0xcb46d0: stp             fp, lr, [SP, #-0x10]!
    //     0xcb46d4: mov             fp, SP
    // 0xcb46d8: CheckStackOverflow
    //     0xcb46d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb46dc: cmp             SP, x16
    //     0xcb46e0: b.ls            #0xcb4718
    // 0xcb46e4: ldr             x1, [fp, #0x20]
    // 0xcb46e8: ldr             x2, [fp, #0x18]
    // 0xcb46ec: ldr             x3, [fp, #0x10]
    // 0xcb46f0: r0 = _predictor13()
    //     0xcb46f0: bl              #0xcb4720  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor13
    // 0xcb46f4: mov             x2, x0
    // 0xcb46f8: r0 = BoxInt64Instr(r2)
    //     0xcb46f8: sbfiz           x0, x2, #1, #0x1f
    //     0xcb46fc: cmp             x2, x0, asr #1
    //     0xcb4700: b.eq            #0xcb470c
    //     0xcb4704: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb4708: stur            x2, [x0, #7]
    // 0xcb470c: LeaveFrame
    //     0xcb470c: mov             SP, fp
    //     0xcb4710: ldp             fp, lr, [SP], #0x10
    // 0xcb4714: ret
    //     0xcb4714: ret             
    // 0xcb4718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4718: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb471c: b               #0xcb46e4
  }
  static _ _predictor13(/* No info */) {
    // ** addr: 0xcb4720, size: 0xa8
    // 0xcb4720: EnterFrame
    //     0xcb4720: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4724: mov             fp, SP
    // 0xcb4728: mov             x4, x1
    // 0xcb472c: CheckStackOverflow
    //     0xcb472c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4730: cmp             SP, x16
    //     0xcb4734: b.ls            #0xcb47b8
    // 0xcb4738: LoadField: r0 = r4->field_13
    //     0xcb4738: ldur            w0, [x4, #0x13]
    // 0xcb473c: r5 = LoadInt32Instr(r3)
    //     0xcb473c: sbfx            x5, x3, #1, #0x1f
    //     0xcb4740: tbz             w3, #0, #0xcb4748
    //     0xcb4744: ldur            x5, [x3, #7]
    // 0xcb4748: r3 = LoadInt32Instr(r0)
    //     0xcb4748: sbfx            x3, x0, #1, #0x1f
    // 0xcb474c: mov             x0, x3
    // 0xcb4750: mov             x1, x5
    // 0xcb4754: cmp             x1, x0
    // 0xcb4758: b.hs            #0xcb47c0
    // 0xcb475c: LoadField: r0 = r4->field_7
    //     0xcb475c: ldur            x0, [x4, #7]
    // 0xcb4760: add             x16, x0, x5, lsl #2
    // 0xcb4764: ldr             w6, [x16]
    // 0xcb4768: sub             x7, x5, #1
    // 0xcb476c: mov             x0, x3
    // 0xcb4770: mov             x1, x7
    // 0xcb4774: cmp             x1, x0
    // 0xcb4778: b.hs            #0xcb47c4
    // 0xcb477c: LoadField: r0 = r4->field_7
    //     0xcb477c: ldur            x0, [x4, #7]
    // 0xcb4780: add             x16, x0, x7, lsl #2
    // 0xcb4784: ldr             w1, [x16]
    // 0xcb4788: r0 = LoadInt32Instr(r2)
    //     0xcb4788: sbfx            x0, x2, #1, #0x1f
    //     0xcb478c: tbz             w2, #0, #0xcb4794
    //     0xcb4790: ldur            x0, [x2, #7]
    // 0xcb4794: ubfx            x6, x6, #0, #0x20
    // 0xcb4798: ubfx            x1, x1, #0, #0x20
    // 0xcb479c: mov             x3, x1
    // 0xcb47a0: mov             x1, x0
    // 0xcb47a4: mov             x2, x6
    // 0xcb47a8: r0 = _clampedAddSubtractHalf()
    //     0xcb47a8: bl              #0xcb47c8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_clampedAddSubtractHalf
    // 0xcb47ac: LeaveFrame
    //     0xcb47ac: mov             SP, fp
    //     0xcb47b0: ldp             fp, lr, [SP], #0x10
    // 0xcb47b4: ret
    //     0xcb47b4: ret             
    // 0xcb47b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb47b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb47bc: b               #0xcb4738
    // 0xcb47c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb47c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb47c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb47c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _clampedAddSubtractHalf(/* No info */) {
    // ** addr: 0xcb47c8, size: 0x14c
    // 0xcb47c8: EnterFrame
    //     0xcb47c8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb47cc: mov             fp, SP
    // 0xcb47d0: AllocStack(0x28)
    //     0xcb47d0: sub             SP, SP, #0x28
    // 0xcb47d4: r0 = 4278124286
    //     0xcb47d4: movz            x0, #0xfefe
    //     0xcb47d8: movk            x0, #0xfefe, lsl #16
    // 0xcb47dc: stur            x3, [fp, #-0x10]
    // 0xcb47e0: CheckStackOverflow
    //     0xcb47e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb47e4: cmp             SP, x16
    //     0xcb47e8: b.ls            #0xcb490c
    // 0xcb47ec: mov             x4, x1
    // 0xcb47f0: ubfx            x4, x4, #0, #0x20
    // 0xcb47f4: mov             x5, x2
    // 0xcb47f8: ubfx            x5, x5, #0, #0x20
    // 0xcb47fc: eor             x6, x4, x5
    // 0xcb4800: and             x4, x6, x0
    // 0xcb4804: ubfx            x4, x4, #0, #0x20
    // 0xcb4808: asr             x0, x4, #1
    // 0xcb480c: and             x4, x1, x2
    // 0xcb4810: add             x5, x0, x4
    // 0xcb4814: stur            x5, [fp, #-8]
    // 0xcb4818: asr             x1, x5, #0x18
    // 0xcb481c: asr             x2, x3, #0x18
    // 0xcb4820: r0 = _addSubtractComponentHalf()
    //     0xcb4820: bl              #0xcb4914  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_addSubtractComponentHalf
    // 0xcb4824: mov             x3, x0
    // 0xcb4828: ldur            x0, [fp, #-8]
    // 0xcb482c: stur            x3, [fp, #-0x18]
    // 0xcb4830: asr             x1, x0, #0x10
    // 0xcb4834: ubfx            x1, x1, #0, #0x20
    // 0xcb4838: r4 = 255
    //     0xcb4838: movz            x4, #0xff
    // 0xcb483c: and             x2, x1, x4
    // 0xcb4840: ldur            x5, [fp, #-0x10]
    // 0xcb4844: asr             x1, x5, #0x10
    // 0xcb4848: ubfx            x1, x1, #0, #0x20
    // 0xcb484c: and             x6, x1, x4
    // 0xcb4850: ubfx            x2, x2, #0, #0x20
    // 0xcb4854: ubfx            x6, x6, #0, #0x20
    // 0xcb4858: mov             x1, x2
    // 0xcb485c: mov             x2, x6
    // 0xcb4860: r0 = _addSubtractComponentHalf()
    //     0xcb4860: bl              #0xcb4914  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_addSubtractComponentHalf
    // 0xcb4864: mov             x3, x0
    // 0xcb4868: ldur            x0, [fp, #-8]
    // 0xcb486c: stur            x3, [fp, #-0x20]
    // 0xcb4870: asr             x1, x0, #8
    // 0xcb4874: ubfx            x1, x1, #0, #0x20
    // 0xcb4878: r4 = 255
    //     0xcb4878: movz            x4, #0xff
    // 0xcb487c: and             x2, x1, x4
    // 0xcb4880: ldur            x5, [fp, #-0x10]
    // 0xcb4884: asr             x1, x5, #8
    // 0xcb4888: ubfx            x1, x1, #0, #0x20
    // 0xcb488c: and             x6, x1, x4
    // 0xcb4890: ubfx            x2, x2, #0, #0x20
    // 0xcb4894: ubfx            x6, x6, #0, #0x20
    // 0xcb4898: mov             x1, x2
    // 0xcb489c: mov             x2, x6
    // 0xcb48a0: r0 = _addSubtractComponentHalf()
    //     0xcb48a0: bl              #0xcb4914  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_addSubtractComponentHalf
    // 0xcb48a4: ldur            x1, [fp, #-8]
    // 0xcb48a8: stur            x0, [fp, #-0x28]
    // 0xcb48ac: ubfx            x1, x1, #0, #0x20
    // 0xcb48b0: r2 = 255
    //     0xcb48b0: movz            x2, #0xff
    // 0xcb48b4: and             x3, x1, x2
    // 0xcb48b8: ldur            x1, [fp, #-0x10]
    // 0xcb48bc: ubfx            x1, x1, #0, #0x20
    // 0xcb48c0: and             x4, x1, x2
    // 0xcb48c4: ubfx            x3, x3, #0, #0x20
    // 0xcb48c8: ubfx            x4, x4, #0, #0x20
    // 0xcb48cc: mov             x1, x3
    // 0xcb48d0: mov             x2, x4
    // 0xcb48d4: r0 = _addSubtractComponentHalf()
    //     0xcb48d4: bl              #0xcb4914  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_addSubtractComponentHalf
    // 0xcb48d8: ldur            x1, [fp, #-0x18]
    // 0xcb48dc: lsl             x2, x1, #0x18
    // 0xcb48e0: ldur            x1, [fp, #-0x20]
    // 0xcb48e4: lsl             x3, x1, #0x10
    // 0xcb48e8: orr             x1, x2, x3
    // 0xcb48ec: ldur            x2, [fp, #-0x28]
    // 0xcb48f0: lsl             x3, x2, #8
    // 0xcb48f4: orr             x2, x1, x3
    // 0xcb48f8: orr             x1, x2, x0
    // 0xcb48fc: mov             x0, x1
    // 0xcb4900: LeaveFrame
    //     0xcb4900: mov             SP, fp
    //     0xcb4904: ldp             fp, lr, [SP], #0x10
    // 0xcb4908: ret
    //     0xcb4908: ret             
    // 0xcb490c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb490c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4910: b               #0xcb47ec
  }
  static _ _addSubtractComponentHalf(/* No info */) {
    // ** addr: 0xcb4914, size: 0x44
    // 0xcb4914: EnterFrame
    //     0xcb4914: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4918: mov             fp, SP
    // 0xcb491c: r3 = 2
    //     0xcb491c: movz            x3, #0x2
    // 0xcb4920: sub             x4, x1, x2
    // 0xcb4924: sdiv            x2, x4, x3
    // 0xcb4928: add             x3, x1, x2
    // 0xcb492c: tbz             x3, #0x3f, #0xcb4938
    // 0xcb4930: r0 = 0
    //     0xcb4930: movz            x0, #0
    // 0xcb4934: b               #0xcb494c
    // 0xcb4938: cmp             x3, #0xff
    // 0xcb493c: b.le            #0xcb4948
    // 0xcb4940: r0 = 255
    //     0xcb4940: movz            x0, #0xff
    // 0xcb4944: b               #0xcb494c
    // 0xcb4948: mov             x0, x3
    // 0xcb494c: LeaveFrame
    //     0xcb494c: mov             SP, fp
    //     0xcb4950: ldp             fp, lr, [SP], #0x10
    // 0xcb4954: ret
    //     0xcb4954: ret             
  }
  [closure] static int _predictor12(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb4958, size: 0x50
    // 0xcb4958: EnterFrame
    //     0xcb4958: stp             fp, lr, [SP, #-0x10]!
    //     0xcb495c: mov             fp, SP
    // 0xcb4960: CheckStackOverflow
    //     0xcb4960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4964: cmp             SP, x16
    //     0xcb4968: b.ls            #0xcb49a0
    // 0xcb496c: ldr             x1, [fp, #0x20]
    // 0xcb4970: ldr             x2, [fp, #0x18]
    // 0xcb4974: ldr             x3, [fp, #0x10]
    // 0xcb4978: r0 = _predictor12()
    //     0xcb4978: bl              #0xcb49a8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor12
    // 0xcb497c: mov             x2, x0
    // 0xcb4980: r0 = BoxInt64Instr(r2)
    //     0xcb4980: sbfiz           x0, x2, #1, #0x1f
    //     0xcb4984: cmp             x2, x0, asr #1
    //     0xcb4988: b.eq            #0xcb4994
    //     0xcb498c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb4990: stur            x2, [x0, #7]
    // 0xcb4994: LeaveFrame
    //     0xcb4994: mov             SP, fp
    //     0xcb4998: ldp             fp, lr, [SP], #0x10
    // 0xcb499c: ret
    //     0xcb499c: ret             
    // 0xcb49a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb49a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb49a4: b               #0xcb496c
  }
  static _ _predictor12(/* No info */) {
    // ** addr: 0xcb49a8, size: 0xa8
    // 0xcb49a8: EnterFrame
    //     0xcb49a8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb49ac: mov             fp, SP
    // 0xcb49b0: mov             x4, x1
    // 0xcb49b4: CheckStackOverflow
    //     0xcb49b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb49b8: cmp             SP, x16
    //     0xcb49bc: b.ls            #0xcb4a40
    // 0xcb49c0: LoadField: r0 = r4->field_13
    //     0xcb49c0: ldur            w0, [x4, #0x13]
    // 0xcb49c4: r5 = LoadInt32Instr(r3)
    //     0xcb49c4: sbfx            x5, x3, #1, #0x1f
    //     0xcb49c8: tbz             w3, #0, #0xcb49d0
    //     0xcb49cc: ldur            x5, [x3, #7]
    // 0xcb49d0: r3 = LoadInt32Instr(r0)
    //     0xcb49d0: sbfx            x3, x0, #1, #0x1f
    // 0xcb49d4: mov             x0, x3
    // 0xcb49d8: mov             x1, x5
    // 0xcb49dc: cmp             x1, x0
    // 0xcb49e0: b.hs            #0xcb4a48
    // 0xcb49e4: LoadField: r0 = r4->field_7
    //     0xcb49e4: ldur            x0, [x4, #7]
    // 0xcb49e8: add             x16, x0, x5, lsl #2
    // 0xcb49ec: ldr             w6, [x16]
    // 0xcb49f0: sub             x7, x5, #1
    // 0xcb49f4: mov             x0, x3
    // 0xcb49f8: mov             x1, x7
    // 0xcb49fc: cmp             x1, x0
    // 0xcb4a00: b.hs            #0xcb4a4c
    // 0xcb4a04: LoadField: r0 = r4->field_7
    //     0xcb4a04: ldur            x0, [x4, #7]
    // 0xcb4a08: add             x16, x0, x7, lsl #2
    // 0xcb4a0c: ldr             w1, [x16]
    // 0xcb4a10: r0 = LoadInt32Instr(r2)
    //     0xcb4a10: sbfx            x0, x2, #1, #0x1f
    //     0xcb4a14: tbz             w2, #0, #0xcb4a1c
    //     0xcb4a18: ldur            x0, [x2, #7]
    // 0xcb4a1c: ubfx            x6, x6, #0, #0x20
    // 0xcb4a20: ubfx            x1, x1, #0, #0x20
    // 0xcb4a24: mov             x3, x1
    // 0xcb4a28: mov             x1, x0
    // 0xcb4a2c: mov             x2, x6
    // 0xcb4a30: r0 = _clampedAddSubtractFull()
    //     0xcb4a30: bl              #0xcb4a50  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_clampedAddSubtractFull
    // 0xcb4a34: LeaveFrame
    //     0xcb4a34: mov             SP, fp
    //     0xcb4a38: ldp             fp, lr, [SP], #0x10
    // 0xcb4a3c: ret
    //     0xcb4a3c: ret             
    // 0xcb4a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4a40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4a44: b               #0xcb49c0
    // 0xcb4a48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4a48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4a4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4a4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _clampedAddSubtractFull(/* No info */) {
    // ** addr: 0xcb4a50, size: 0x158
    // 0xcb4a50: EnterFrame
    //     0xcb4a50: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4a54: mov             fp, SP
    // 0xcb4a58: asr             x4, x1, #0x18
    // 0xcb4a5c: asr             x5, x2, #0x18
    // 0xcb4a60: asr             x6, x3, #0x18
    // 0xcb4a64: add             x7, x4, x5
    // 0xcb4a68: sub             x4, x7, x6
    // 0xcb4a6c: tbz             x4, #0x3f, #0xcb4a78
    // 0xcb4a70: r5 = 0
    //     0xcb4a70: movz            x5, #0
    // 0xcb4a74: b               #0xcb4a8c
    // 0xcb4a78: cmp             x4, #0xff
    // 0xcb4a7c: b.le            #0xcb4a88
    // 0xcb4a80: r5 = 255
    //     0xcb4a80: movz            x5, #0xff
    // 0xcb4a84: b               #0xcb4a8c
    // 0xcb4a88: mov             x5, x4
    // 0xcb4a8c: r4 = 255
    //     0xcb4a8c: movz            x4, #0xff
    // 0xcb4a90: asr             x6, x1, #0x10
    // 0xcb4a94: ubfx            x6, x6, #0, #0x20
    // 0xcb4a98: and             x7, x6, x4
    // 0xcb4a9c: asr             x6, x2, #0x10
    // 0xcb4aa0: ubfx            x6, x6, #0, #0x20
    // 0xcb4aa4: and             x8, x6, x4
    // 0xcb4aa8: asr             x6, x3, #0x10
    // 0xcb4aac: ubfx            x6, x6, #0, #0x20
    // 0xcb4ab0: and             x9, x6, x4
    // 0xcb4ab4: ubfx            x7, x7, #0, #0x20
    // 0xcb4ab8: ubfx            x8, x8, #0, #0x20
    // 0xcb4abc: add             x6, x7, x8
    // 0xcb4ac0: ubfx            x9, x9, #0, #0x20
    // 0xcb4ac4: sub             x7, x6, x9
    // 0xcb4ac8: tbz             x7, #0x3f, #0xcb4ad4
    // 0xcb4acc: r6 = 0
    //     0xcb4acc: movz            x6, #0
    // 0xcb4ad0: b               #0xcb4ae8
    // 0xcb4ad4: cmp             x7, #0xff
    // 0xcb4ad8: b.le            #0xcb4ae4
    // 0xcb4adc: r6 = 255
    //     0xcb4adc: movz            x6, #0xff
    // 0xcb4ae0: b               #0xcb4ae8
    // 0xcb4ae4: mov             x6, x7
    // 0xcb4ae8: asr             x7, x1, #8
    // 0xcb4aec: ubfx            x7, x7, #0, #0x20
    // 0xcb4af0: and             x8, x7, x4
    // 0xcb4af4: asr             x7, x2, #8
    // 0xcb4af8: ubfx            x7, x7, #0, #0x20
    // 0xcb4afc: and             x9, x7, x4
    // 0xcb4b00: asr             x7, x3, #8
    // 0xcb4b04: ubfx            x7, x7, #0, #0x20
    // 0xcb4b08: and             x10, x7, x4
    // 0xcb4b0c: ubfx            x8, x8, #0, #0x20
    // 0xcb4b10: ubfx            x9, x9, #0, #0x20
    // 0xcb4b14: add             x7, x8, x9
    // 0xcb4b18: ubfx            x10, x10, #0, #0x20
    // 0xcb4b1c: sub             x8, x7, x10
    // 0xcb4b20: tbz             x8, #0x3f, #0xcb4b2c
    // 0xcb4b24: r7 = 0
    //     0xcb4b24: movz            x7, #0
    // 0xcb4b28: b               #0xcb4b40
    // 0xcb4b2c: cmp             x8, #0xff
    // 0xcb4b30: b.le            #0xcb4b3c
    // 0xcb4b34: r7 = 255
    //     0xcb4b34: movz            x7, #0xff
    // 0xcb4b38: b               #0xcb4b40
    // 0xcb4b3c: mov             x7, x8
    // 0xcb4b40: ubfx            x1, x1, #0, #0x20
    // 0xcb4b44: and             x8, x1, x4
    // 0xcb4b48: ubfx            x2, x2, #0, #0x20
    // 0xcb4b4c: and             x1, x2, x4
    // 0xcb4b50: ubfx            x3, x3, #0, #0x20
    // 0xcb4b54: and             x2, x3, x4
    // 0xcb4b58: ubfx            x8, x8, #0, #0x20
    // 0xcb4b5c: ubfx            x1, x1, #0, #0x20
    // 0xcb4b60: add             x3, x8, x1
    // 0xcb4b64: ubfx            x2, x2, #0, #0x20
    // 0xcb4b68: sub             x1, x3, x2
    // 0xcb4b6c: tbz             x1, #0x3f, #0xcb4b78
    // 0xcb4b70: r1 = 0
    //     0xcb4b70: movz            x1, #0
    // 0xcb4b74: b               #0xcb4b84
    // 0xcb4b78: cmp             x1, #0xff
    // 0xcb4b7c: b.le            #0xcb4b84
    // 0xcb4b80: r1 = 255
    //     0xcb4b80: movz            x1, #0xff
    // 0xcb4b84: lsl             x2, x5, #0x18
    // 0xcb4b88: lsl             x3, x6, #0x10
    // 0xcb4b8c: orr             x4, x2, x3
    // 0xcb4b90: lsl             x2, x7, #8
    // 0xcb4b94: orr             x3, x4, x2
    // 0xcb4b98: orr             x0, x3, x1
    // 0xcb4b9c: LeaveFrame
    //     0xcb4b9c: mov             SP, fp
    //     0xcb4ba0: ldp             fp, lr, [SP], #0x10
    // 0xcb4ba4: ret
    //     0xcb4ba4: ret             
  }
  [closure] static int _predictor11(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb4ba8, size: 0x50
    // 0xcb4ba8: EnterFrame
    //     0xcb4ba8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4bac: mov             fp, SP
    // 0xcb4bb0: CheckStackOverflow
    //     0xcb4bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4bb4: cmp             SP, x16
    //     0xcb4bb8: b.ls            #0xcb4bf0
    // 0xcb4bbc: ldr             x1, [fp, #0x20]
    // 0xcb4bc0: ldr             x2, [fp, #0x18]
    // 0xcb4bc4: ldr             x3, [fp, #0x10]
    // 0xcb4bc8: r0 = _predictor11()
    //     0xcb4bc8: bl              #0xcb4bf8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor11
    // 0xcb4bcc: mov             x2, x0
    // 0xcb4bd0: r0 = BoxInt64Instr(r2)
    //     0xcb4bd0: sbfiz           x0, x2, #1, #0x1f
    //     0xcb4bd4: cmp             x2, x0, asr #1
    //     0xcb4bd8: b.eq            #0xcb4be4
    //     0xcb4bdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb4be0: stur            x2, [x0, #7]
    // 0xcb4be4: LeaveFrame
    //     0xcb4be4: mov             SP, fp
    //     0xcb4be8: ldp             fp, lr, [SP], #0x10
    // 0xcb4bec: ret
    //     0xcb4bec: ret             
    // 0xcb4bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4bf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4bf4: b               #0xcb4bbc
  }
  static _ _predictor11(/* No info */) {
    // ** addr: 0xcb4bf8, size: 0xa8
    // 0xcb4bf8: EnterFrame
    //     0xcb4bf8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4bfc: mov             fp, SP
    // 0xcb4c00: mov             x4, x1
    // 0xcb4c04: CheckStackOverflow
    //     0xcb4c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4c08: cmp             SP, x16
    //     0xcb4c0c: b.ls            #0xcb4c90
    // 0xcb4c10: LoadField: r0 = r4->field_13
    //     0xcb4c10: ldur            w0, [x4, #0x13]
    // 0xcb4c14: r5 = LoadInt32Instr(r3)
    //     0xcb4c14: sbfx            x5, x3, #1, #0x1f
    //     0xcb4c18: tbz             w3, #0, #0xcb4c20
    //     0xcb4c1c: ldur            x5, [x3, #7]
    // 0xcb4c20: r3 = LoadInt32Instr(r0)
    //     0xcb4c20: sbfx            x3, x0, #1, #0x1f
    // 0xcb4c24: mov             x0, x3
    // 0xcb4c28: mov             x1, x5
    // 0xcb4c2c: cmp             x1, x0
    // 0xcb4c30: b.hs            #0xcb4c98
    // 0xcb4c34: LoadField: r0 = r4->field_7
    //     0xcb4c34: ldur            x0, [x4, #7]
    // 0xcb4c38: add             x16, x0, x5, lsl #2
    // 0xcb4c3c: ldr             w6, [x16]
    // 0xcb4c40: sub             x7, x5, #1
    // 0xcb4c44: mov             x0, x3
    // 0xcb4c48: mov             x1, x7
    // 0xcb4c4c: cmp             x1, x0
    // 0xcb4c50: b.hs            #0xcb4c9c
    // 0xcb4c54: LoadField: r0 = r4->field_7
    //     0xcb4c54: ldur            x0, [x4, #7]
    // 0xcb4c58: add             x16, x0, x7, lsl #2
    // 0xcb4c5c: ldr             w1, [x16]
    // 0xcb4c60: r0 = LoadInt32Instr(r2)
    //     0xcb4c60: sbfx            x0, x2, #1, #0x1f
    //     0xcb4c64: tbz             w2, #0, #0xcb4c6c
    //     0xcb4c68: ldur            x0, [x2, #7]
    // 0xcb4c6c: ubfx            x6, x6, #0, #0x20
    // 0xcb4c70: ubfx            x1, x1, #0, #0x20
    // 0xcb4c74: mov             x3, x1
    // 0xcb4c78: mov             x1, x6
    // 0xcb4c7c: mov             x2, x0
    // 0xcb4c80: r0 = _select()
    //     0xcb4c80: bl              #0xcb4ca0  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_select
    // 0xcb4c84: LeaveFrame
    //     0xcb4c84: mov             SP, fp
    //     0xcb4c88: ldp             fp, lr, [SP], #0x10
    // 0xcb4c8c: ret
    //     0xcb4c8c: ret             
    // 0xcb4c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4c94: b               #0xcb4c10
    // 0xcb4c98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4c98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4c9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4c9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _select(/* No info */) {
    // ** addr: 0xcb4ca0, size: 0x17c
    // 0xcb4ca0: EnterFrame
    //     0xcb4ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4ca4: mov             fp, SP
    // 0xcb4ca8: AllocStack(0x28)
    //     0xcb4ca8: sub             SP, SP, #0x28
    // 0xcb4cac: SetupParameters(dynamic _ /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xcb4cac: mov             x5, x1
    //     0xcb4cb0: mov             x4, x2
    //     0xcb4cb4: mov             x0, x3
    //     0xcb4cb8: stur            x1, [fp, #-8]
    //     0xcb4cbc: stur            x2, [fp, #-0x10]
    //     0xcb4cc0: stur            x3, [fp, #-0x18]
    // 0xcb4cc4: CheckStackOverflow
    //     0xcb4cc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4cc8: cmp             SP, x16
    //     0xcb4ccc: b.ls            #0xcb4e14
    // 0xcb4cd0: asr             x1, x5, #0x18
    // 0xcb4cd4: asr             x2, x4, #0x18
    // 0xcb4cd8: asr             x3, x0, #0x18
    // 0xcb4cdc: r0 = _sub3()
    //     0xcb4cdc: bl              #0xcb4e1c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_sub3
    // 0xcb4ce0: mov             x4, x0
    // 0xcb4ce4: ldur            x0, [fp, #-8]
    // 0xcb4ce8: stur            x4, [fp, #-0x20]
    // 0xcb4cec: asr             x1, x0, #0x10
    // 0xcb4cf0: ubfx            x1, x1, #0, #0x20
    // 0xcb4cf4: r5 = 255
    //     0xcb4cf4: movz            x5, #0xff
    // 0xcb4cf8: and             x2, x1, x5
    // 0xcb4cfc: ldur            x6, [fp, #-0x10]
    // 0xcb4d00: asr             x1, x6, #0x10
    // 0xcb4d04: ubfx            x1, x1, #0, #0x20
    // 0xcb4d08: and             x3, x1, x5
    // 0xcb4d0c: ldur            x7, [fp, #-0x18]
    // 0xcb4d10: asr             x1, x7, #0x10
    // 0xcb4d14: ubfx            x1, x1, #0, #0x20
    // 0xcb4d18: and             x8, x1, x5
    // 0xcb4d1c: ubfx            x2, x2, #0, #0x20
    // 0xcb4d20: ubfx            x3, x3, #0, #0x20
    // 0xcb4d24: ubfx            x8, x8, #0, #0x20
    // 0xcb4d28: mov             x1, x2
    // 0xcb4d2c: mov             x2, x3
    // 0xcb4d30: mov             x3, x8
    // 0xcb4d34: r0 = _sub3()
    //     0xcb4d34: bl              #0xcb4e1c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_sub3
    // 0xcb4d38: mov             x1, x0
    // 0xcb4d3c: ldur            x0, [fp, #-0x20]
    // 0xcb4d40: add             x4, x0, x1
    // 0xcb4d44: ldur            x0, [fp, #-8]
    // 0xcb4d48: stur            x4, [fp, #-0x28]
    // 0xcb4d4c: asr             x1, x0, #8
    // 0xcb4d50: ubfx            x1, x1, #0, #0x20
    // 0xcb4d54: r5 = 255
    //     0xcb4d54: movz            x5, #0xff
    // 0xcb4d58: and             x2, x1, x5
    // 0xcb4d5c: ldur            x6, [fp, #-0x10]
    // 0xcb4d60: asr             x1, x6, #8
    // 0xcb4d64: ubfx            x1, x1, #0, #0x20
    // 0xcb4d68: and             x3, x1, x5
    // 0xcb4d6c: ldur            x7, [fp, #-0x18]
    // 0xcb4d70: asr             x1, x7, #8
    // 0xcb4d74: ubfx            x1, x1, #0, #0x20
    // 0xcb4d78: and             x8, x1, x5
    // 0xcb4d7c: ubfx            x2, x2, #0, #0x20
    // 0xcb4d80: ubfx            x3, x3, #0, #0x20
    // 0xcb4d84: ubfx            x8, x8, #0, #0x20
    // 0xcb4d88: mov             x1, x2
    // 0xcb4d8c: mov             x2, x3
    // 0xcb4d90: mov             x3, x8
    // 0xcb4d94: r0 = _sub3()
    //     0xcb4d94: bl              #0xcb4e1c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_sub3
    // 0xcb4d98: mov             x1, x0
    // 0xcb4d9c: ldur            x0, [fp, #-0x28]
    // 0xcb4da0: add             x4, x0, x1
    // 0xcb4da4: stur            x4, [fp, #-0x20]
    // 0xcb4da8: ldur            x0, [fp, #-8]
    // 0xcb4dac: ubfx            x0, x0, #0, #0x20
    // 0xcb4db0: r1 = 255
    //     0xcb4db0: movz            x1, #0xff
    // 0xcb4db4: and             x2, x0, x1
    // 0xcb4db8: ldur            x0, [fp, #-0x10]
    // 0xcb4dbc: ubfx            x0, x0, #0, #0x20
    // 0xcb4dc0: and             x3, x0, x1
    // 0xcb4dc4: ldur            x0, [fp, #-0x18]
    // 0xcb4dc8: ubfx            x0, x0, #0, #0x20
    // 0xcb4dcc: and             x5, x0, x1
    // 0xcb4dd0: ubfx            x2, x2, #0, #0x20
    // 0xcb4dd4: ubfx            x3, x3, #0, #0x20
    // 0xcb4dd8: ubfx            x5, x5, #0, #0x20
    // 0xcb4ddc: mov             x1, x2
    // 0xcb4de0: mov             x2, x3
    // 0xcb4de4: mov             x3, x5
    // 0xcb4de8: r0 = _sub3()
    //     0xcb4de8: bl              #0xcb4e1c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_sub3
    // 0xcb4dec: ldur            x1, [fp, #-0x20]
    // 0xcb4df0: add             x2, x1, x0
    // 0xcb4df4: cmp             x2, #0
    // 0xcb4df8: b.gt            #0xcb4e04
    // 0xcb4dfc: ldur            x0, [fp, #-8]
    // 0xcb4e00: b               #0xcb4e08
    // 0xcb4e04: ldur            x0, [fp, #-0x10]
    // 0xcb4e08: LeaveFrame
    //     0xcb4e08: mov             SP, fp
    //     0xcb4e0c: ldp             fp, lr, [SP], #0x10
    // 0xcb4e10: ret
    //     0xcb4e10: ret             
    // 0xcb4e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4e14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4e18: b               #0xcb4cd0
  }
  static _ _sub3(/* No info */) {
    // ** addr: 0xcb4e1c, size: 0x2c
    // 0xcb4e1c: sub             x4, x2, x3
    // 0xcb4e20: sub             x2, x1, x3
    // 0xcb4e24: tbz             x4, #0x3f, #0xcb4e30
    // 0xcb4e28: neg             x1, x4
    // 0xcb4e2c: b               #0xcb4e34
    // 0xcb4e30: mov             x1, x4
    // 0xcb4e34: tbz             x2, #0x3f, #0xcb4e40
    // 0xcb4e38: neg             x3, x2
    // 0xcb4e3c: mov             x2, x3
    // 0xcb4e40: sub             x0, x1, x2
    // 0xcb4e44: ret
    //     0xcb4e44: ret             
  }
  [closure] static int _predictor10(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb4e48, size: 0x50
    // 0xcb4e48: EnterFrame
    //     0xcb4e48: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4e4c: mov             fp, SP
    // 0xcb4e50: CheckStackOverflow
    //     0xcb4e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4e54: cmp             SP, x16
    //     0xcb4e58: b.ls            #0xcb4e90
    // 0xcb4e5c: ldr             x1, [fp, #0x20]
    // 0xcb4e60: ldr             x2, [fp, #0x18]
    // 0xcb4e64: ldr             x3, [fp, #0x10]
    // 0xcb4e68: r0 = _predictor10()
    //     0xcb4e68: bl              #0xcb4e98  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor10
    // 0xcb4e6c: mov             x2, x0
    // 0xcb4e70: r0 = BoxInt64Instr(r2)
    //     0xcb4e70: sbfiz           x0, x2, #1, #0x1f
    //     0xcb4e74: cmp             x2, x0, asr #1
    //     0xcb4e78: b.eq            #0xcb4e84
    //     0xcb4e7c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb4e80: stur            x2, [x0, #7]
    // 0xcb4e84: LeaveFrame
    //     0xcb4e84: mov             SP, fp
    //     0xcb4e88: ldp             fp, lr, [SP], #0x10
    // 0xcb4e8c: ret
    //     0xcb4e8c: ret             
    // 0xcb4e90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4e90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4e94: b               #0xcb4e5c
  }
  static _ _predictor10(/* No info */) {
    // ** addr: 0xcb4e98, size: 0xd0
    // 0xcb4e98: EnterFrame
    //     0xcb4e98: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4e9c: mov             fp, SP
    // 0xcb4ea0: mov             x4, x1
    // 0xcb4ea4: CheckStackOverflow
    //     0xcb4ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb4ea8: cmp             SP, x16
    //     0xcb4eac: b.ls            #0xcb4f54
    // 0xcb4eb0: r5 = LoadInt32Instr(r3)
    //     0xcb4eb0: sbfx            x5, x3, #1, #0x1f
    //     0xcb4eb4: tbz             w3, #0, #0xcb4ebc
    //     0xcb4eb8: ldur            x5, [x3, #7]
    // 0xcb4ebc: sub             x3, x5, #1
    // 0xcb4ec0: LoadField: r0 = r4->field_13
    //     0xcb4ec0: ldur            w0, [x4, #0x13]
    // 0xcb4ec4: r6 = LoadInt32Instr(r0)
    //     0xcb4ec4: sbfx            x6, x0, #1, #0x1f
    // 0xcb4ec8: mov             x0, x6
    // 0xcb4ecc: mov             x1, x3
    // 0xcb4ed0: cmp             x1, x0
    // 0xcb4ed4: b.hs            #0xcb4f5c
    // 0xcb4ed8: LoadField: r0 = r4->field_7
    //     0xcb4ed8: ldur            x0, [x4, #7]
    // 0xcb4edc: add             x16, x0, x3, lsl #2
    // 0xcb4ee0: ldr             w7, [x16]
    // 0xcb4ee4: mov             x0, x6
    // 0xcb4ee8: mov             x1, x5
    // 0xcb4eec: cmp             x1, x0
    // 0xcb4ef0: b.hs            #0xcb4f60
    // 0xcb4ef4: LoadField: r0 = r4->field_7
    //     0xcb4ef4: ldur            x0, [x4, #7]
    // 0xcb4ef8: add             x16, x0, x5, lsl #2
    // 0xcb4efc: ldr             w3, [x16]
    // 0xcb4f00: add             x8, x5, #1
    // 0xcb4f04: mov             x0, x6
    // 0xcb4f08: mov             x1, x8
    // 0xcb4f0c: cmp             x1, x0
    // 0xcb4f10: b.hs            #0xcb4f64
    // 0xcb4f14: LoadField: r0 = r4->field_7
    //     0xcb4f14: ldur            x0, [x4, #7]
    // 0xcb4f18: add             x16, x0, x8, lsl #2
    // 0xcb4f1c: ldr             w1, [x16]
    // 0xcb4f20: r0 = LoadInt32Instr(r2)
    //     0xcb4f20: sbfx            x0, x2, #1, #0x1f
    //     0xcb4f24: tbz             w2, #0, #0xcb4f2c
    //     0xcb4f28: ldur            x0, [x2, #7]
    // 0xcb4f2c: ubfx            x7, x7, #0, #0x20
    // 0xcb4f30: ubfx            x3, x3, #0, #0x20
    // 0xcb4f34: ubfx            x1, x1, #0, #0x20
    // 0xcb4f38: mov             x5, x1
    // 0xcb4f3c: mov             x1, x0
    // 0xcb4f40: mov             x2, x7
    // 0xcb4f44: r0 = _average4()
    //     0xcb4f44: bl              #0xcb4f68  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_average4
    // 0xcb4f48: LeaveFrame
    //     0xcb4f48: mov             SP, fp
    //     0xcb4f4c: ldp             fp, lr, [SP], #0x10
    // 0xcb4f50: ret
    //     0xcb4f50: ret             
    // 0xcb4f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb4f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb4f58: b               #0xcb4eb0
    // 0xcb4f5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4f5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4f60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4f60: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb4f64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb4f64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _average4(/* No info */) {
    // ** addr: 0xcb4f68, size: 0x94
    // 0xcb4f68: EnterFrame
    //     0xcb4f68: stp             fp, lr, [SP, #-0x10]!
    //     0xcb4f6c: mov             fp, SP
    // 0xcb4f70: r4 = 4278124286
    //     0xcb4f70: movz            x4, #0xfefe
    //     0xcb4f74: movk            x4, #0xfefe, lsl #16
    // 0xcb4f78: mov             x6, x1
    // 0xcb4f7c: ubfx            x6, x6, #0, #0x20
    // 0xcb4f80: mov             x7, x2
    // 0xcb4f84: ubfx            x7, x7, #0, #0x20
    // 0xcb4f88: eor             x8, x6, x7
    // 0xcb4f8c: and             x6, x8, x4
    // 0xcb4f90: ubfx            x6, x6, #0, #0x20
    // 0xcb4f94: asr             x7, x6, #1
    // 0xcb4f98: and             x6, x1, x2
    // 0xcb4f9c: add             x1, x7, x6
    // 0xcb4fa0: mov             x2, x3
    // 0xcb4fa4: ubfx            x2, x2, #0, #0x20
    // 0xcb4fa8: mov             x6, x5
    // 0xcb4fac: ubfx            x6, x6, #0, #0x20
    // 0xcb4fb0: eor             x7, x2, x6
    // 0xcb4fb4: and             x2, x7, x4
    // 0xcb4fb8: ubfx            x2, x2, #0, #0x20
    // 0xcb4fbc: asr             x6, x2, #1
    // 0xcb4fc0: and             x2, x3, x5
    // 0xcb4fc4: add             x3, x6, x2
    // 0xcb4fc8: mov             x2, x1
    // 0xcb4fcc: ubfx            x2, x2, #0, #0x20
    // 0xcb4fd0: mov             x5, x3
    // 0xcb4fd4: ubfx            x5, x5, #0, #0x20
    // 0xcb4fd8: eor             x6, x2, x5
    // 0xcb4fdc: and             x2, x6, x4
    // 0xcb4fe0: ubfx            x2, x2, #0, #0x20
    // 0xcb4fe4: asr             x4, x2, #1
    // 0xcb4fe8: and             x2, x1, x3
    // 0xcb4fec: add             x0, x4, x2
    // 0xcb4ff0: LeaveFrame
    //     0xcb4ff0: mov             SP, fp
    //     0xcb4ff4: ldp             fp, lr, [SP], #0x10
    // 0xcb4ff8: ret
    //     0xcb4ff8: ret             
  }
  [closure] static int _predictor9(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb4ffc, size: 0x50
    // 0xcb4ffc: EnterFrame
    //     0xcb4ffc: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5000: mov             fp, SP
    // 0xcb5004: CheckStackOverflow
    //     0xcb5004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb5008: cmp             SP, x16
    //     0xcb500c: b.ls            #0xcb5044
    // 0xcb5010: ldr             x1, [fp, #0x20]
    // 0xcb5014: ldr             x2, [fp, #0x18]
    // 0xcb5018: ldr             x3, [fp, #0x10]
    // 0xcb501c: r0 = _predictor9()
    //     0xcb501c: bl              #0xcb504c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor9
    // 0xcb5020: mov             x2, x0
    // 0xcb5024: r0 = BoxInt64Instr(r2)
    //     0xcb5024: sbfiz           x0, x2, #1, #0x1f
    //     0xcb5028: cmp             x2, x0, asr #1
    //     0xcb502c: b.eq            #0xcb5038
    //     0xcb5030: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb5034: stur            x2, [x0, #7]
    // 0xcb5038: LeaveFrame
    //     0xcb5038: mov             SP, fp
    //     0xcb503c: ldp             fp, lr, [SP], #0x10
    // 0xcb5040: ret
    //     0xcb5040: ret             
    // 0xcb5044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb5044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb5048: b               #0xcb5010
  }
  static _ _predictor9(/* No info */) {
    // ** addr: 0xcb504c, size: 0x94
    // 0xcb504c: EnterFrame
    //     0xcb504c: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5050: mov             fp, SP
    // 0xcb5054: r4 = 4278124286
    //     0xcb5054: movz            x4, #0xfefe
    //     0xcb5058: movk            x4, #0xfefe, lsl #16
    // 0xcb505c: mov             x5, x1
    // 0xcb5060: LoadField: r2 = r5->field_13
    //     0xcb5060: ldur            w2, [x5, #0x13]
    // 0xcb5064: r6 = LoadInt32Instr(r3)
    //     0xcb5064: sbfx            x6, x3, #1, #0x1f
    //     0xcb5068: tbz             w3, #0, #0xcb5070
    //     0xcb506c: ldur            x6, [x3, #7]
    // 0xcb5070: r3 = LoadInt32Instr(r2)
    //     0xcb5070: sbfx            x3, x2, #1, #0x1f
    // 0xcb5074: mov             x0, x3
    // 0xcb5078: mov             x1, x6
    // 0xcb507c: cmp             x1, x0
    // 0xcb5080: b.hs            #0xcb50d8
    // 0xcb5084: LoadField: r2 = r5->field_7
    //     0xcb5084: ldur            x2, [x5, #7]
    // 0xcb5088: add             x16, x2, x6, lsl #2
    // 0xcb508c: ldr             w7, [x16]
    // 0xcb5090: add             x2, x6, #1
    // 0xcb5094: mov             x0, x3
    // 0xcb5098: mov             x1, x2
    // 0xcb509c: cmp             x1, x0
    // 0xcb50a0: b.hs            #0xcb50dc
    // 0xcb50a4: LoadField: r1 = r5->field_7
    //     0xcb50a4: ldur            x1, [x5, #7]
    // 0xcb50a8: add             x16, x1, x2, lsl #2
    // 0xcb50ac: ldr             w3, [x16]
    // 0xcb50b0: eor             x1, x7, x3
    // 0xcb50b4: and             x2, x1, x4
    // 0xcb50b8: ubfx            x2, x2, #0, #0x20
    // 0xcb50bc: asr             x1, x2, #1
    // 0xcb50c0: and             x2, x7, x3
    // 0xcb50c4: ubfx            x2, x2, #0, #0x20
    // 0xcb50c8: add             x0, x1, x2
    // 0xcb50cc: LeaveFrame
    //     0xcb50cc: mov             SP, fp
    //     0xcb50d0: ldp             fp, lr, [SP], #0x10
    // 0xcb50d4: ret
    //     0xcb50d4: ret             
    // 0xcb50d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb50d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb50dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb50dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor8(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb50e0, size: 0x50
    // 0xcb50e0: EnterFrame
    //     0xcb50e0: stp             fp, lr, [SP, #-0x10]!
    //     0xcb50e4: mov             fp, SP
    // 0xcb50e8: CheckStackOverflow
    //     0xcb50e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb50ec: cmp             SP, x16
    //     0xcb50f0: b.ls            #0xcb5128
    // 0xcb50f4: ldr             x1, [fp, #0x20]
    // 0xcb50f8: ldr             x2, [fp, #0x18]
    // 0xcb50fc: ldr             x3, [fp, #0x10]
    // 0xcb5100: r0 = _predictor8()
    //     0xcb5100: bl              #0xcb5130  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor8
    // 0xcb5104: mov             x2, x0
    // 0xcb5108: r0 = BoxInt64Instr(r2)
    //     0xcb5108: sbfiz           x0, x2, #1, #0x1f
    //     0xcb510c: cmp             x2, x0, asr #1
    //     0xcb5110: b.eq            #0xcb511c
    //     0xcb5114: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb5118: stur            x2, [x0, #7]
    // 0xcb511c: LeaveFrame
    //     0xcb511c: mov             SP, fp
    //     0xcb5120: ldp             fp, lr, [SP], #0x10
    // 0xcb5124: ret
    //     0xcb5124: ret             
    // 0xcb5128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb5128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb512c: b               #0xcb50f4
  }
  static _ _predictor8(/* No info */) {
    // ** addr: 0xcb5130, size: 0x94
    // 0xcb5130: EnterFrame
    //     0xcb5130: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5134: mov             fp, SP
    // 0xcb5138: r4 = 4278124286
    //     0xcb5138: movz            x4, #0xfefe
    //     0xcb513c: movk            x4, #0xfefe, lsl #16
    // 0xcb5140: mov             x5, x1
    // 0xcb5144: r2 = LoadInt32Instr(r3)
    //     0xcb5144: sbfx            x2, x3, #1, #0x1f
    //     0xcb5148: tbz             w3, #0, #0xcb5150
    //     0xcb514c: ldur            x2, [x3, #7]
    // 0xcb5150: sub             x3, x2, #1
    // 0xcb5154: LoadField: r6 = r5->field_13
    //     0xcb5154: ldur            w6, [x5, #0x13]
    // 0xcb5158: r7 = LoadInt32Instr(r6)
    //     0xcb5158: sbfx            x7, x6, #1, #0x1f
    // 0xcb515c: mov             x0, x7
    // 0xcb5160: mov             x1, x3
    // 0xcb5164: cmp             x1, x0
    // 0xcb5168: b.hs            #0xcb51bc
    // 0xcb516c: LoadField: r6 = r5->field_7
    //     0xcb516c: ldur            x6, [x5, #7]
    // 0xcb5170: add             x16, x6, x3, lsl #2
    // 0xcb5174: ldr             w8, [x16]
    // 0xcb5178: mov             x0, x7
    // 0xcb517c: mov             x1, x2
    // 0xcb5180: cmp             x1, x0
    // 0xcb5184: b.hs            #0xcb51c0
    // 0xcb5188: LoadField: r1 = r5->field_7
    //     0xcb5188: ldur            x1, [x5, #7]
    // 0xcb518c: add             x16, x1, x2, lsl #2
    // 0xcb5190: ldr             w3, [x16]
    // 0xcb5194: eor             x1, x8, x3
    // 0xcb5198: and             x2, x1, x4
    // 0xcb519c: ubfx            x2, x2, #0, #0x20
    // 0xcb51a0: asr             x1, x2, #1
    // 0xcb51a4: and             x2, x8, x3
    // 0xcb51a8: ubfx            x2, x2, #0, #0x20
    // 0xcb51ac: add             x0, x1, x2
    // 0xcb51b0: LeaveFrame
    //     0xcb51b0: mov             SP, fp
    //     0xcb51b4: ldp             fp, lr, [SP], #0x10
    // 0xcb51b8: ret
    //     0xcb51b8: ret             
    // 0xcb51bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb51bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb51c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb51c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor7(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb51c4, size: 0x50
    // 0xcb51c4: EnterFrame
    //     0xcb51c4: stp             fp, lr, [SP, #-0x10]!
    //     0xcb51c8: mov             fp, SP
    // 0xcb51cc: CheckStackOverflow
    //     0xcb51cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb51d0: cmp             SP, x16
    //     0xcb51d4: b.ls            #0xcb520c
    // 0xcb51d8: ldr             x1, [fp, #0x20]
    // 0xcb51dc: ldr             x2, [fp, #0x18]
    // 0xcb51e0: ldr             x3, [fp, #0x10]
    // 0xcb51e4: r0 = _predictor7()
    //     0xcb51e4: bl              #0xcb5214  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor7
    // 0xcb51e8: mov             x2, x0
    // 0xcb51ec: r0 = BoxInt64Instr(r2)
    //     0xcb51ec: sbfiz           x0, x2, #1, #0x1f
    //     0xcb51f0: cmp             x2, x0, asr #1
    //     0xcb51f4: b.eq            #0xcb5200
    //     0xcb51f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb51fc: stur            x2, [x0, #7]
    // 0xcb5200: LeaveFrame
    //     0xcb5200: mov             SP, fp
    //     0xcb5204: ldp             fp, lr, [SP], #0x10
    // 0xcb5208: ret
    //     0xcb5208: ret             
    // 0xcb520c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb520c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb5210: b               #0xcb51d8
  }
  static _ _predictor7(/* No info */) {
    // ** addr: 0xcb5214, size: 0x78
    // 0xcb5214: EnterFrame
    //     0xcb5214: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5218: mov             fp, SP
    // 0xcb521c: r4 = 4278124286
    //     0xcb521c: movz            x4, #0xfefe
    //     0xcb5220: movk            x4, #0xfefe, lsl #16
    // 0xcb5224: mov             x5, x1
    // 0xcb5228: LoadField: r6 = r5->field_13
    //     0xcb5228: ldur            w6, [x5, #0x13]
    // 0xcb522c: r7 = LoadInt32Instr(r3)
    //     0xcb522c: sbfx            x7, x3, #1, #0x1f
    //     0xcb5230: tbz             w3, #0, #0xcb5238
    //     0xcb5234: ldur            x7, [x3, #7]
    // 0xcb5238: r0 = LoadInt32Instr(r6)
    //     0xcb5238: sbfx            x0, x6, #1, #0x1f
    // 0xcb523c: mov             x1, x7
    // 0xcb5240: cmp             x1, x0
    // 0xcb5244: b.hs            #0xcb5288
    // 0xcb5248: LoadField: r1 = r5->field_7
    //     0xcb5248: ldur            x1, [x5, #7]
    // 0xcb524c: add             x16, x1, x7, lsl #2
    // 0xcb5250: ldr             w3, [x16]
    // 0xcb5254: r1 = LoadInt32Instr(r2)
    //     0xcb5254: sbfx            x1, x2, #1, #0x1f
    //     0xcb5258: tbz             w2, #0, #0xcb5260
    //     0xcb525c: ldur            x1, [x2, #7]
    // 0xcb5260: eor             x2, x1, x3
    // 0xcb5264: and             x5, x2, x4
    // 0xcb5268: ubfx            x5, x5, #0, #0x20
    // 0xcb526c: asr             x2, x5, #1
    // 0xcb5270: and             x4, x1, x3
    // 0xcb5274: ubfx            x4, x4, #0, #0x20
    // 0xcb5278: add             x0, x2, x4
    // 0xcb527c: LeaveFrame
    //     0xcb527c: mov             SP, fp
    //     0xcb5280: ldp             fp, lr, [SP], #0x10
    // 0xcb5284: ret
    //     0xcb5284: ret             
    // 0xcb5288: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb5288: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor6(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb528c, size: 0x50
    // 0xcb528c: EnterFrame
    //     0xcb528c: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5290: mov             fp, SP
    // 0xcb5294: CheckStackOverflow
    //     0xcb5294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb5298: cmp             SP, x16
    //     0xcb529c: b.ls            #0xcb52d4
    // 0xcb52a0: ldr             x1, [fp, #0x20]
    // 0xcb52a4: ldr             x2, [fp, #0x18]
    // 0xcb52a8: ldr             x3, [fp, #0x10]
    // 0xcb52ac: r0 = _predictor6()
    //     0xcb52ac: bl              #0xcb52dc  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor6
    // 0xcb52b0: mov             x2, x0
    // 0xcb52b4: r0 = BoxInt64Instr(r2)
    //     0xcb52b4: sbfiz           x0, x2, #1, #0x1f
    //     0xcb52b8: cmp             x2, x0, asr #1
    //     0xcb52bc: b.eq            #0xcb52c8
    //     0xcb52c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb52c4: stur            x2, [x0, #7]
    // 0xcb52c8: LeaveFrame
    //     0xcb52c8: mov             SP, fp
    //     0xcb52cc: ldp             fp, lr, [SP], #0x10
    // 0xcb52d0: ret
    //     0xcb52d0: ret             
    // 0xcb52d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb52d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb52d8: b               #0xcb52a0
  }
  static _ _predictor6(/* No info */) {
    // ** addr: 0xcb52dc, size: 0x7c
    // 0xcb52dc: EnterFrame
    //     0xcb52dc: stp             fp, lr, [SP, #-0x10]!
    //     0xcb52e0: mov             fp, SP
    // 0xcb52e4: r4 = 4278124286
    //     0xcb52e4: movz            x4, #0xfefe
    //     0xcb52e8: movk            x4, #0xfefe, lsl #16
    // 0xcb52ec: mov             x5, x1
    // 0xcb52f0: r6 = LoadInt32Instr(r3)
    //     0xcb52f0: sbfx            x6, x3, #1, #0x1f
    //     0xcb52f4: tbz             w3, #0, #0xcb52fc
    //     0xcb52f8: ldur            x6, [x3, #7]
    // 0xcb52fc: sub             x3, x6, #1
    // 0xcb5300: LoadField: r6 = r5->field_13
    //     0xcb5300: ldur            w6, [x5, #0x13]
    // 0xcb5304: r0 = LoadInt32Instr(r6)
    //     0xcb5304: sbfx            x0, x6, #1, #0x1f
    // 0xcb5308: mov             x1, x3
    // 0xcb530c: cmp             x1, x0
    // 0xcb5310: b.hs            #0xcb5354
    // 0xcb5314: LoadField: r1 = r5->field_7
    //     0xcb5314: ldur            x1, [x5, #7]
    // 0xcb5318: add             x16, x1, x3, lsl #2
    // 0xcb531c: ldr             w5, [x16]
    // 0xcb5320: r1 = LoadInt32Instr(r2)
    //     0xcb5320: sbfx            x1, x2, #1, #0x1f
    //     0xcb5324: tbz             w2, #0, #0xcb532c
    //     0xcb5328: ldur            x1, [x2, #7]
    // 0xcb532c: eor             x2, x1, x5
    // 0xcb5330: and             x3, x2, x4
    // 0xcb5334: ubfx            x3, x3, #0, #0x20
    // 0xcb5338: asr             x2, x3, #1
    // 0xcb533c: and             x3, x1, x5
    // 0xcb5340: ubfx            x3, x3, #0, #0x20
    // 0xcb5344: add             x0, x2, x3
    // 0xcb5348: LeaveFrame
    //     0xcb5348: mov             SP, fp
    //     0xcb534c: ldp             fp, lr, [SP], #0x10
    // 0xcb5350: ret
    //     0xcb5350: ret             
    // 0xcb5354: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb5354: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor5(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb5358, size: 0x50
    // 0xcb5358: EnterFrame
    //     0xcb5358: stp             fp, lr, [SP, #-0x10]!
    //     0xcb535c: mov             fp, SP
    // 0xcb5360: CheckStackOverflow
    //     0xcb5360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb5364: cmp             SP, x16
    //     0xcb5368: b.ls            #0xcb53a0
    // 0xcb536c: ldr             x1, [fp, #0x20]
    // 0xcb5370: ldr             x2, [fp, #0x18]
    // 0xcb5374: ldr             x3, [fp, #0x10]
    // 0xcb5378: r0 = _predictor5()
    //     0xcb5378: bl              #0xcb53a8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor5
    // 0xcb537c: mov             x2, x0
    // 0xcb5380: r0 = BoxInt64Instr(r2)
    //     0xcb5380: sbfiz           x0, x2, #1, #0x1f
    //     0xcb5384: cmp             x2, x0, asr #1
    //     0xcb5388: b.eq            #0xcb5394
    //     0xcb538c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb5390: stur            x2, [x0, #7]
    // 0xcb5394: LeaveFrame
    //     0xcb5394: mov             SP, fp
    //     0xcb5398: ldp             fp, lr, [SP], #0x10
    // 0xcb539c: ret
    //     0xcb539c: ret             
    // 0xcb53a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb53a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb53a4: b               #0xcb536c
  }
  static _ _predictor5(/* No info */) {
    // ** addr: 0xcb53a8, size: 0xa8
    // 0xcb53a8: EnterFrame
    //     0xcb53a8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb53ac: mov             fp, SP
    // 0xcb53b0: mov             x4, x1
    // 0xcb53b4: CheckStackOverflow
    //     0xcb53b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb53b8: cmp             SP, x16
    //     0xcb53bc: b.ls            #0xcb5440
    // 0xcb53c0: LoadField: r0 = r4->field_13
    //     0xcb53c0: ldur            w0, [x4, #0x13]
    // 0xcb53c4: r5 = LoadInt32Instr(r3)
    //     0xcb53c4: sbfx            x5, x3, #1, #0x1f
    //     0xcb53c8: tbz             w3, #0, #0xcb53d0
    //     0xcb53cc: ldur            x5, [x3, #7]
    // 0xcb53d0: r3 = LoadInt32Instr(r0)
    //     0xcb53d0: sbfx            x3, x0, #1, #0x1f
    // 0xcb53d4: mov             x0, x3
    // 0xcb53d8: mov             x1, x5
    // 0xcb53dc: cmp             x1, x0
    // 0xcb53e0: b.hs            #0xcb5448
    // 0xcb53e4: LoadField: r0 = r4->field_7
    //     0xcb53e4: ldur            x0, [x4, #7]
    // 0xcb53e8: add             x16, x0, x5, lsl #2
    // 0xcb53ec: ldr             w6, [x16]
    // 0xcb53f0: add             x7, x5, #1
    // 0xcb53f4: mov             x0, x3
    // 0xcb53f8: mov             x1, x7
    // 0xcb53fc: cmp             x1, x0
    // 0xcb5400: b.hs            #0xcb544c
    // 0xcb5404: LoadField: r0 = r4->field_7
    //     0xcb5404: ldur            x0, [x4, #7]
    // 0xcb5408: add             x16, x0, x7, lsl #2
    // 0xcb540c: ldr             w1, [x16]
    // 0xcb5410: r0 = LoadInt32Instr(r2)
    //     0xcb5410: sbfx            x0, x2, #1, #0x1f
    //     0xcb5414: tbz             w2, #0, #0xcb541c
    //     0xcb5418: ldur            x0, [x2, #7]
    // 0xcb541c: ubfx            x6, x6, #0, #0x20
    // 0xcb5420: ubfx            x1, x1, #0, #0x20
    // 0xcb5424: mov             x3, x1
    // 0xcb5428: mov             x1, x0
    // 0xcb542c: mov             x2, x6
    // 0xcb5430: r0 = _average3()
    //     0xcb5430: bl              #0xcb5450  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_average3
    // 0xcb5434: LeaveFrame
    //     0xcb5434: mov             SP, fp
    //     0xcb5438: ldp             fp, lr, [SP], #0x10
    // 0xcb543c: ret
    //     0xcb543c: ret             
    // 0xcb5440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb5440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb5444: b               #0xcb53c0
    // 0xcb5448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb5448: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xcb544c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb544c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _average3(/* No info */) {
    // ** addr: 0xcb5450, size: 0x6c
    // 0xcb5450: EnterFrame
    //     0xcb5450: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5454: mov             fp, SP
    // 0xcb5458: r4 = 4278124286
    //     0xcb5458: movz            x4, #0xfefe
    //     0xcb545c: movk            x4, #0xfefe, lsl #16
    // 0xcb5460: mov             x5, x1
    // 0xcb5464: ubfx            x5, x5, #0, #0x20
    // 0xcb5468: mov             x6, x3
    // 0xcb546c: ubfx            x6, x6, #0, #0x20
    // 0xcb5470: eor             x7, x5, x6
    // 0xcb5474: and             x5, x7, x4
    // 0xcb5478: ubfx            x5, x5, #0, #0x20
    // 0xcb547c: asr             x6, x5, #1
    // 0xcb5480: and             x5, x1, x3
    // 0xcb5484: add             x1, x6, x5
    // 0xcb5488: mov             x3, x2
    // 0xcb548c: ubfx            x3, x3, #0, #0x20
    // 0xcb5490: mov             x5, x1
    // 0xcb5494: ubfx            x5, x5, #0, #0x20
    // 0xcb5498: eor             x6, x5, x3
    // 0xcb549c: and             x3, x6, x4
    // 0xcb54a0: ubfx            x3, x3, #0, #0x20
    // 0xcb54a4: asr             x4, x3, #1
    // 0xcb54a8: and             x3, x1, x2
    // 0xcb54ac: add             x0, x4, x3
    // 0xcb54b0: LeaveFrame
    //     0xcb54b0: mov             SP, fp
    //     0xcb54b4: ldp             fp, lr, [SP], #0x10
    // 0xcb54b8: ret
    //     0xcb54b8: ret             
  }
  [closure] static int _predictor4(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb54bc, size: 0x50
    // 0xcb54bc: EnterFrame
    //     0xcb54bc: stp             fp, lr, [SP, #-0x10]!
    //     0xcb54c0: mov             fp, SP
    // 0xcb54c4: CheckStackOverflow
    //     0xcb54c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb54c8: cmp             SP, x16
    //     0xcb54cc: b.ls            #0xcb5504
    // 0xcb54d0: ldr             x1, [fp, #0x20]
    // 0xcb54d4: ldr             x2, [fp, #0x18]
    // 0xcb54d8: ldr             x3, [fp, #0x10]
    // 0xcb54dc: r0 = _predictor4()
    //     0xcb54dc: bl              #0xcb550c  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor4
    // 0xcb54e0: mov             x2, x0
    // 0xcb54e4: r0 = BoxInt64Instr(r2)
    //     0xcb54e4: sbfiz           x0, x2, #1, #0x1f
    //     0xcb54e8: cmp             x2, x0, asr #1
    //     0xcb54ec: b.eq            #0xcb54f8
    //     0xcb54f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb54f4: stur            x2, [x0, #7]
    // 0xcb54f8: LeaveFrame
    //     0xcb54f8: mov             SP, fp
    //     0xcb54fc: ldp             fp, lr, [SP], #0x10
    // 0xcb5500: ret
    //     0xcb5500: ret             
    // 0xcb5504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb5504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb5508: b               #0xcb54d0
  }
  static _ _predictor4(/* No info */) {
    // ** addr: 0xcb550c, size: 0x4c
    // 0xcb550c: mov             x4, x1
    // 0xcb5510: r2 = LoadInt32Instr(r3)
    //     0xcb5510: sbfx            x2, x3, #1, #0x1f
    //     0xcb5514: tbz             w3, #0, #0xcb551c
    //     0xcb5518: ldur            x2, [x3, #7]
    // 0xcb551c: sub             x3, x2, #1
    // 0xcb5520: LoadField: r2 = r4->field_13
    //     0xcb5520: ldur            w2, [x4, #0x13]
    // 0xcb5524: r0 = LoadInt32Instr(r2)
    //     0xcb5524: sbfx            x0, x2, #1, #0x1f
    // 0xcb5528: mov             x1, x3
    // 0xcb552c: cmp             x1, x0
    // 0xcb5530: b.hs            #0xcb554c
    // 0xcb5534: LoadField: r1 = r4->field_7
    //     0xcb5534: ldur            x1, [x4, #7]
    // 0xcb5538: add             x16, x1, x3, lsl #2
    // 0xcb553c: ldr             w2, [x16]
    // 0xcb5540: ubfx            x2, x2, #0, #0x20
    // 0xcb5544: mov             x0, x2
    // 0xcb5548: ret
    //     0xcb5548: ret             
    // 0xcb554c: EnterFrame
    //     0xcb554c: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5550: mov             fp, SP
    // 0xcb5554: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb5554: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor3(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb5558, size: 0x50
    // 0xcb5558: EnterFrame
    //     0xcb5558: stp             fp, lr, [SP, #-0x10]!
    //     0xcb555c: mov             fp, SP
    // 0xcb5560: CheckStackOverflow
    //     0xcb5560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb5564: cmp             SP, x16
    //     0xcb5568: b.ls            #0xcb55a0
    // 0xcb556c: ldr             x1, [fp, #0x20]
    // 0xcb5570: ldr             x2, [fp, #0x18]
    // 0xcb5574: ldr             x3, [fp, #0x10]
    // 0xcb5578: r0 = _predictor3()
    //     0xcb5578: bl              #0xcb55a8  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor3
    // 0xcb557c: mov             x2, x0
    // 0xcb5580: r0 = BoxInt64Instr(r2)
    //     0xcb5580: sbfiz           x0, x2, #1, #0x1f
    //     0xcb5584: cmp             x2, x0, asr #1
    //     0xcb5588: b.eq            #0xcb5594
    //     0xcb558c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb5590: stur            x2, [x0, #7]
    // 0xcb5594: LeaveFrame
    //     0xcb5594: mov             SP, fp
    //     0xcb5598: ldp             fp, lr, [SP], #0x10
    // 0xcb559c: ret
    //     0xcb559c: ret             
    // 0xcb55a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb55a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb55a4: b               #0xcb556c
  }
  static _ _predictor3(/* No info */) {
    // ** addr: 0xcb55a8, size: 0x4c
    // 0xcb55a8: mov             x4, x1
    // 0xcb55ac: r2 = LoadInt32Instr(r3)
    //     0xcb55ac: sbfx            x2, x3, #1, #0x1f
    //     0xcb55b0: tbz             w3, #0, #0xcb55b8
    //     0xcb55b4: ldur            x2, [x3, #7]
    // 0xcb55b8: add             x3, x2, #1
    // 0xcb55bc: LoadField: r2 = r4->field_13
    //     0xcb55bc: ldur            w2, [x4, #0x13]
    // 0xcb55c0: r0 = LoadInt32Instr(r2)
    //     0xcb55c0: sbfx            x0, x2, #1, #0x1f
    // 0xcb55c4: mov             x1, x3
    // 0xcb55c8: cmp             x1, x0
    // 0xcb55cc: b.hs            #0xcb55e8
    // 0xcb55d0: LoadField: r1 = r4->field_7
    //     0xcb55d0: ldur            x1, [x4, #7]
    // 0xcb55d4: add             x16, x1, x3, lsl #2
    // 0xcb55d8: ldr             w2, [x16]
    // 0xcb55dc: ubfx            x2, x2, #0, #0x20
    // 0xcb55e0: mov             x0, x2
    // 0xcb55e4: ret
    //     0xcb55e4: ret             
    // 0xcb55e8: EnterFrame
    //     0xcb55e8: stp             fp, lr, [SP, #-0x10]!
    //     0xcb55ec: mov             fp, SP
    // 0xcb55f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb55f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static int _predictor2(dynamic, Uint32List, int, int) {
    // ** addr: 0xcb55f4, size: 0x50
    // 0xcb55f4: EnterFrame
    //     0xcb55f4: stp             fp, lr, [SP, #-0x10]!
    //     0xcb55f8: mov             fp, SP
    // 0xcb55fc: CheckStackOverflow
    //     0xcb55fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcb5600: cmp             SP, x16
    //     0xcb5604: b.ls            #0xcb563c
    // 0xcb5608: ldr             x1, [fp, #0x20]
    // 0xcb560c: ldr             x2, [fp, #0x18]
    // 0xcb5610: ldr             x3, [fp, #0x10]
    // 0xcb5614: r0 = _predictor2()
    //     0xcb5614: bl              #0xcb5644  ; [package:image/src/formats/webp/vp8l_transform.dart] VP8LTransform::_predictor2
    // 0xcb5618: mov             x2, x0
    // 0xcb561c: r0 = BoxInt64Instr(r2)
    //     0xcb561c: sbfiz           x0, x2, #1, #0x1f
    //     0xcb5620: cmp             x2, x0, asr #1
    //     0xcb5624: b.eq            #0xcb5630
    //     0xcb5628: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xcb562c: stur            x2, [x0, #7]
    // 0xcb5630: LeaveFrame
    //     0xcb5630: mov             SP, fp
    //     0xcb5634: ldp             fp, lr, [SP], #0x10
    // 0xcb5638: ret
    //     0xcb5638: ret             
    // 0xcb563c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcb563c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcb5640: b               #0xcb5608
  }
  static _ _predictor2(/* No info */) {
    // ** addr: 0xcb5644, size: 0x48
    // 0xcb5644: mov             x4, x1
    // 0xcb5648: LoadField: r2 = r4->field_13
    //     0xcb5648: ldur            w2, [x4, #0x13]
    // 0xcb564c: r5 = LoadInt32Instr(r3)
    //     0xcb564c: sbfx            x5, x3, #1, #0x1f
    //     0xcb5650: tbz             w3, #0, #0xcb5658
    //     0xcb5654: ldur            x5, [x3, #7]
    // 0xcb5658: r0 = LoadInt32Instr(r2)
    //     0xcb5658: sbfx            x0, x2, #1, #0x1f
    // 0xcb565c: mov             x1, x5
    // 0xcb5660: cmp             x1, x0
    // 0xcb5664: b.hs            #0xcb5680
    // 0xcb5668: LoadField: r1 = r4->field_7
    //     0xcb5668: ldur            x1, [x4, #7]
    // 0xcb566c: add             x16, x1, x5, lsl #2
    // 0xcb5670: ldr             w2, [x16]
    // 0xcb5674: ubfx            x2, x2, #0, #0x20
    // 0xcb5678: mov             x0, x2
    // 0xcb567c: ret
    //     0xcb567c: ret             
    // 0xcb5680: EnterFrame
    //     0xcb5680: stp             fp, lr, [SP, #-0x10]!
    //     0xcb5684: mov             fp, SP
    // 0xcb5688: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcb5688: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 6872, size: 0x14, field offset: 0x14
enum VP8LImageTransformType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4c628, size: 0x64
    // 0xc4c628: EnterFrame
    //     0xc4c628: stp             fp, lr, [SP, #-0x10]!
    //     0xc4c62c: mov             fp, SP
    // 0xc4c630: AllocStack(0x10)
    //     0xc4c630: sub             SP, SP, #0x10
    // 0xc4c634: SetupParameters(VP8LImageTransformType this /* r1 => r0, fp-0x8 */)
    //     0xc4c634: mov             x0, x1
    //     0xc4c638: stur            x1, [fp, #-8]
    // 0xc4c63c: CheckStackOverflow
    //     0xc4c63c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4c640: cmp             SP, x16
    //     0xc4c644: b.ls            #0xc4c684
    // 0xc4c648: r1 = Null
    //     0xc4c648: mov             x1, NULL
    // 0xc4c64c: r2 = 4
    //     0xc4c64c: movz            x2, #0x4
    // 0xc4c650: r0 = AllocateArray()
    //     0xc4c650: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4c654: r16 = "VP8LImageTransformType."
    //     0xc4c654: add             x16, PP, #0x51, lsl #12  ; [pp+0x518e0] "VP8LImageTransformType."
    //     0xc4c658: ldr             x16, [x16, #0x8e0]
    // 0xc4c65c: StoreField: r0->field_f = r16
    //     0xc4c65c: stur            w16, [x0, #0xf]
    // 0xc4c660: ldur            x1, [fp, #-8]
    // 0xc4c664: LoadField: r2 = r1->field_f
    //     0xc4c664: ldur            w2, [x1, #0xf]
    // 0xc4c668: DecompressPointer r2
    //     0xc4c668: add             x2, x2, HEAP, lsl #32
    // 0xc4c66c: StoreField: r0->field_13 = r2
    //     0xc4c66c: stur            w2, [x0, #0x13]
    // 0xc4c670: str             x0, [SP]
    // 0xc4c674: r0 = _interpolate()
    //     0xc4c674: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4c678: LeaveFrame
    //     0xc4c678: mov             SP, fp
    //     0xc4c67c: ldp             fp, lr, [SP], #0x10
    // 0xc4c680: ret
    //     0xc4c680: ret             
    // 0xc4c684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4c684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4c688: b               #0xc4c648
  }
}
