// lib: date_format_internal, url: package:intl/src/date_format_internal.dart

// class id: 1049842, size: 0x8
class :: {

  static late dynamic _dateTimeSymbols; // offset: 0x1324
  static late dynamic dateTimePatterns; // offset: 0x1330

  static dynamic dateTimePatterns() {
    // ** addr: 0x81ce50, size: 0x58
    // 0x81ce50: EnterFrame
    //     0x81ce50: stp             fp, lr, [SP, #-0x10]!
    //     0x81ce54: mov             fp, SP
    // 0x81ce58: CheckStackOverflow
    //     0x81ce58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81ce5c: cmp             SP, x16
    //     0x81ce60: b.ls            #0x81cea0
    // 0x81ce64: r1 = <String>
    //     0x81ce64: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x81ce68: r2 = 0
    //     0x81ce68: movz            x2, #0
    // 0x81ce6c: r0 = _GrowableList()
    //     0x81ce6c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x81ce70: r1 = <Map<String, String>>
    //     0x81ce70: add             x1, PP, #8, lsl #12  ; [pp+0x8c38] TypeArguments: <Map<String, String>>
    //     0x81ce74: ldr             x1, [x1, #0xc38]
    // 0x81ce78: r0 = UninitializedLocaleData()
    //     0x81ce78: bl              #0x81d400  ; AllocateUninitializedLocaleDataStub -> UninitializedLocaleData<X0> (size=0x14)
    // 0x81ce7c: r1 = "initializeDateFormatting(<locale>)"
    //     0x81ce7c: add             x1, PP, #8, lsl #12  ; [pp+0x8b08] "initializeDateFormatting(<locale>)"
    //     0x81ce80: ldr             x1, [x1, #0xb08]
    // 0x81ce84: StoreField: r0->field_b = r1
    //     0x81ce84: stur            w1, [x0, #0xb]
    // 0x81ce88: r1 = _ConstMap len:44
    //     0x81ce88: add             x1, PP, #8, lsl #12  ; [pp+0x8c40] Map<String, String>(44)
    //     0x81ce8c: ldr             x1, [x1, #0xc40]
    // 0x81ce90: StoreField: r0->field_f = r1
    //     0x81ce90: stur            w1, [x0, #0xf]
    // 0x81ce94: LeaveFrame
    //     0x81ce94: mov             SP, fp
    //     0x81ce98: ldp             fp, lr, [SP], #0x10
    // 0x81ce9c: ret
    //     0x81ce9c: ret             
    // 0x81cea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81cea0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81cea4: b               #0x81ce64
  }
  static dynamic _dateTimeSymbols() {
    // ** addr: 0x81d938, size: 0x7c
    // 0x81d938: EnterFrame
    //     0x81d938: stp             fp, lr, [SP, #-0x10]!
    //     0x81d93c: mov             fp, SP
    // 0x81d940: AllocStack(0x8)
    //     0x81d940: sub             SP, SP, #8
    // 0x81d944: CheckStackOverflow
    //     0x81d944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81d948: cmp             SP, x16
    //     0x81d94c: b.ls            #0x81d9ac
    // 0x81d950: r0 = InitLateStaticField(0x1320) // [package:intl/date_symbols.dart] ::en_USSymbols
    //     0x81d950: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81d954: ldr             x0, [x0, #0x2640]
    //     0x81d958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81d95c: cmp             w0, w16
    //     0x81d960: b.ne            #0x81d970
    //     0x81d964: add             x2, PP, #8, lsl #12  ; [pp+0x8af8] Field <::.en_USSymbols>: static late final (offset: 0x1320)
    //     0x81d968: ldr             x2, [x2, #0xaf8]
    //     0x81d96c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81d970: r1 = <String>
    //     0x81d970: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x81d974: r2 = 0
    //     0x81d974: movz            x2, #0
    // 0x81d978: stur            x0, [fp, #-8]
    // 0x81d97c: r0 = _GrowableList()
    //     0x81d97c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x81d980: r1 = <DateSymbols>
    //     0x81d980: add             x1, PP, #8, lsl #12  ; [pp+0x8b00] TypeArguments: <DateSymbols>
    //     0x81d984: ldr             x1, [x1, #0xb00]
    // 0x81d988: r0 = UninitializedLocaleData()
    //     0x81d988: bl              #0x81d400  ; AllocateUninitializedLocaleDataStub -> UninitializedLocaleData<X0> (size=0x14)
    // 0x81d98c: r1 = "initializeDateFormatting(<locale>)"
    //     0x81d98c: add             x1, PP, #8, lsl #12  ; [pp+0x8b08] "initializeDateFormatting(<locale>)"
    //     0x81d990: ldr             x1, [x1, #0xb08]
    // 0x81d994: StoreField: r0->field_b = r1
    //     0x81d994: stur            w1, [x0, #0xb]
    // 0x81d998: ldur            x1, [fp, #-8]
    // 0x81d99c: StoreField: r0->field_f = r1
    //     0x81d99c: stur            w1, [x0, #0xf]
    // 0x81d9a0: LeaveFrame
    //     0x81d9a0: mov             SP, fp
    //     0x81d9a4: ldp             fp, lr, [SP], #0x10
    // 0x81d9a8: ret
    //     0x81d9a8: ret             
    // 0x81d9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81d9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81d9b0: b               #0x81d950
  }
  static void initializeDatePatterns() {
    // ** addr: 0x822068, size: 0x94
    // 0x822068: EnterFrame
    //     0x822068: stp             fp, lr, [SP, #-0x10]!
    //     0x82206c: mov             fp, SP
    // 0x822070: AllocStack(0x8)
    //     0x822070: sub             SP, SP, #8
    // 0x822074: CheckStackOverflow
    //     0x822074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x822078: cmp             SP, x16
    //     0x82207c: b.ls            #0x8220f4
    // 0x822080: r0 = InitLateStaticField(0x1330) // [package:intl/src/date_format_internal.dart] ::dateTimePatterns
    //     0x822080: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x822084: ldr             x0, [x0, #0x2660]
    //     0x822088: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x82208c: cmp             w0, w16
    //     0x822090: b.ne            #0x8220a0
    //     0x822094: add             x2, PP, #8, lsl #12  ; [pp+0x8be0] Field <::.dateTimePatterns>: static late (offset: 0x1330)
    //     0x822098: ldr             x2, [x2, #0xbe0]
    //     0x82209c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8220a0: r1 = 60
    //     0x8220a0: movz            x1, #0x3c
    // 0x8220a4: branchIfSmi(r0, 0x8220b0)
    //     0x8220a4: tbz             w0, #0, #0x8220b0
    // 0x8220a8: r1 = LoadClassIdInstr(r0)
    //     0x8220a8: ldur            x1, [x0, #-1]
    //     0x8220ac: ubfx            x1, x1, #0xc, #0x14
    // 0x8220b0: cmp             x1, #0x533
    // 0x8220b4: b.ne            #0x8220e4
    // 0x8220b8: r16 = Closure: () => Map<String, Map<String, String>> from Function 'dateTimePatternMap': static.
    //     0x8220b8: add             x16, PP, #9, lsl #12  ; [pp+0x9470] Closure: () => Map<String, Map<String, String>> from Function 'dateTimePatternMap': static. (0x7e54fb2220fc)
    //     0x8220bc: ldr             x16, [x16, #0x470]
    // 0x8220c0: str             x16, [SP]
    // 0x8220c4: r4 = 0
    //     0x8220c4: movz            x4, #0
    // 0x8220c8: ldr             x0, [SP]
    // 0x8220cc: r16 = UnlinkedCall_0x5f3c08
    //     0x8220cc: add             x16, PP, #9, lsl #12  ; [pp+0x9478] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8220d0: add             x16, x16, #0x478
    // 0x8220d4: ldp             x5, lr, [x16]
    // 0x8220d8: blr             lr
    // 0x8220dc: StoreStaticField(0x1330, r0)
    //     0x8220dc: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x8220e0: str             x0, [x1, #0x2660]
    // 0x8220e4: r0 = Null
    //     0x8220e4: mov             x0, NULL
    // 0x8220e8: LeaveFrame
    //     0x8220e8: mov             SP, fp
    //     0x8220ec: ldp             fp, lr, [SP], #0x10
    // 0x8220f0: ret
    //     0x8220f0: ret             
    // 0x8220f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8220f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8220f8: b               #0x822080
  }
  static void initializeDateSymbols() {
    // ** addr: 0x823298, size: 0xac
    // 0x823298: EnterFrame
    //     0x823298: stp             fp, lr, [SP, #-0x10]!
    //     0x82329c: mov             fp, SP
    // 0x8232a0: AllocStack(0x8)
    //     0x8232a0: sub             SP, SP, #8
    // 0x8232a4: CheckStackOverflow
    //     0x8232a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8232a8: cmp             SP, x16
    //     0x8232ac: b.ls            #0x82333c
    // 0x8232b0: r0 = InitLateStaticField(0x1324) // [package:intl/src/date_format_internal.dart] ::_dateTimeSymbols
    //     0x8232b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8232b4: ldr             x0, [x0, #0x2648]
    //     0x8232b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8232bc: cmp             w0, w16
    //     0x8232c0: b.ne            #0x8232d0
    //     0x8232c4: add             x2, PP, #8, lsl #12  ; [pp+0x8ae0] Field <::._dateTimeSymbols@1245168376>: static late (offset: 0x1324)
    //     0x8232c8: ldr             x2, [x2, #0xae0]
    //     0x8232cc: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8232d0: r1 = 60
    //     0x8232d0: movz            x1, #0x3c
    // 0x8232d4: branchIfSmi(r0, 0x8232e0)
    //     0x8232d4: tbz             w0, #0, #0x8232e0
    // 0x8232d8: r1 = LoadClassIdInstr(r0)
    //     0x8232d8: ldur            x1, [x0, #-1]
    //     0x8232dc: ubfx            x1, x1, #0xc, #0x14
    // 0x8232e0: cmp             x1, #0x533
    // 0x8232e4: b.ne            #0x82332c
    // 0x8232e8: r16 = Closure: () => Map<dynamic, dynamic> from Function 'dateTimeSymbolMap': static.
    //     0x8232e8: add             x16, PP, #9, lsl #12  ; [pp+0x9b70] Closure: () => Map<dynamic, dynamic> from Function 'dateTimeSymbolMap': static. (0x7e54fb223344)
    //     0x8232ec: ldr             x16, [x16, #0xb70]
    // 0x8232f0: str             x16, [SP]
    // 0x8232f4: r4 = 0
    //     0x8232f4: movz            x4, #0
    // 0x8232f8: ldr             x0, [SP]
    // 0x8232fc: r16 = UnlinkedCall_0x5f3c08
    //     0x8232fc: add             x16, PP, #9, lsl #12  ; [pp+0x9b78] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x823300: add             x16, x16, #0xb78
    // 0x823304: ldp             x5, lr, [x16]
    // 0x823308: blr             lr
    // 0x82330c: StoreStaticField(0x1324, r0)
    //     0x82330c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x823310: str             x0, [x1, #0x2648]
    // 0x823314: r0 = Null
    //     0x823314: mov             x0, NULL
    // 0x823318: StoreStaticField(0x1328, r0)
    //     0x823318: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x82331c: str             x0, [x1, #0x2650]
    // 0x823320: StoreStaticField(0x132c, r0)
    //     0x823320: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x823324: str             x0, [x1, #0x2658]
    // 0x823328: b               #0x823330
    // 0x82332c: r0 = Null
    //     0x82332c: mov             x0, NULL
    // 0x823330: LeaveFrame
    //     0x823330: mov             SP, fp
    //     0x823334: ldp             fp, lr, [SP], #0x10
    // 0x823338: ret
    //     0x823338: ret             
    // 0x82333c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82333c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x823340: b               #0x8232b0
  }
}
