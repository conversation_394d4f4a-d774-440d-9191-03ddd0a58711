// lib: , url: package:intl/src/intl/number_format.dart

// class id: 1049848, size: 0x8
class :: {

  static late final double _ln10; // offset: 0x149c

  static double _ln10() {
    // ** addr: 0x847294, size: 0xa8
    // 0x847294: EnterFrame
    //     0x847294: stp             fp, lr, [SP, #-0x10]!
    //     0x847298: mov             fp, SP
    // 0x84729c: AllocStack(0x10)
    //     0x84729c: sub             SP, SP, #0x10
    // 0x8472a0: CheckStackOverflow
    //     0x8472a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8472a4: cmp             SP, x16
    //     0x8472a8: b.ls            #0x847324
    // 0x8472ac: r16 = 20
    //     0x8472ac: movz            x16, #0x14
    // 0x8472b0: stp             x16, NULL, [SP]
    // 0x8472b4: r0 = _Double.fromInteger()
    //     0x8472b4: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8472b8: LoadField: d0 = r0->field_7
    //     0x8472b8: ldur            d0, [x0, #7]
    // 0x8472bc: stp             fp, lr, [SP, #-0x10]!
    // 0x8472c0: mov             fp, SP
    // 0x8472c4: CallRuntime_LibcLog(double) -> double
    //     0x8472c4: and             SP, SP, #0xfffffffffffffff0
    //     0x8472c8: mov             sp, SP
    //     0x8472cc: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8472d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8472d4: blr             x16
    //     0x8472d8: movz            x16, #0x8
    //     0x8472dc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8472e0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8472e4: sub             sp, x16, #1, lsl #12
    //     0x8472e8: mov             SP, fp
    //     0x8472ec: ldp             fp, lr, [SP], #0x10
    // 0x8472f0: r0 = inline_Allocate_Double()
    //     0x8472f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8472f4: add             x0, x0, #0x10
    //     0x8472f8: cmp             x1, x0
    //     0x8472fc: b.ls            #0x84732c
    //     0x847300: str             x0, [THR, #0x50]  ; THR::top
    //     0x847304: sub             x0, x0, #0xf
    //     0x847308: movz            x1, #0xe15c
    //     0x84730c: movk            x1, #0x3, lsl #16
    //     0x847310: stur            x1, [x0, #-1]
    // 0x847314: StoreField: r0->field_7 = d0
    //     0x847314: stur            d0, [x0, #7]
    // 0x847318: LeaveFrame
    //     0x847318: mov             SP, fp
    //     0x84731c: ldp             fp, lr, [SP], #0x10
    // 0x847320: ret
    //     0x847320: ret             
    // 0x847324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x847324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x847328: b               #0x8472ac
    // 0x84732c: SaveReg d0
    //     0x84732c: str             q0, [SP, #-0x10]!
    // 0x847330: r0 = AllocateDouble()
    //     0x847330: bl              #0xec2254  ; AllocateDoubleStub
    // 0x847334: RestoreReg d0
    //     0x847334: ldr             q0, [SP], #0x10
    // 0x847338: b               #0x847314
  }
}

// class id: 1335, size: 0x88, field offset: 0x8
class NumberFormat extends Object {

  static late final num _maxInt; // offset: 0x1494
  static late final int _maxDigits; // offset: 0x1498

  factory _ NumberFormat(/* No info */) {
    // ** addr: 0x84698c, size: 0x6c
    // 0x84698c: EnterFrame
    //     0x84698c: stp             fp, lr, [SP, #-0x10]!
    //     0x846990: mov             fp, SP
    // 0x846994: AllocStack(0x8)
    //     0x846994: sub             SP, SP, #8
    // 0x846998: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x846998: stur            x2, [fp, #-8]
    // 0x84699c: CheckStackOverflow
    //     0x84699c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8469a0: cmp             SP, x16
    //     0x8469a4: b.ls            #0x8469f0
    // 0x8469a8: r1 = 1
    //     0x8469a8: movz            x1, #0x1
    // 0x8469ac: r0 = AllocateContext()
    //     0x8469ac: bl              #0xec126c  ; AllocateContextStub
    // 0x8469b0: mov             x1, x0
    // 0x8469b4: ldur            x0, [fp, #-8]
    // 0x8469b8: StoreField: r1->field_f = r0
    //     0x8469b8: stur            w0, [x1, #0xf]
    // 0x8469bc: mov             x2, x1
    // 0x8469c0: r1 = Function '<anonymous closure>': static.
    //     0x8469c0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30050] AnonymousClosure: static (0x5fe1f0), in [dart:async] _Future::_propagateToListeners (0x5fa1e8)
    //     0x8469c4: ldr             x1, [x1, #0x50]
    // 0x8469c8: r0 = AllocateClosure()
    //     0x8469c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8469cc: mov             x3, x0
    // 0x8469d0: r1 = Null
    //     0x8469d0: mov             x1, NULL
    // 0x8469d4: r2 = "id_ID"
    //     0x8469d4: add             x2, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0x8469d8: ldr             x2, [x2, #0x200]
    // 0x8469dc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8469dc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8469e0: r0 = NumberFormat._forPattern()
    //     0x8469e0: bl              #0x846b60  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x8469e4: LeaveFrame
    //     0x8469e4: mov             SP, fp
    //     0x8469e8: ldp             fp, lr, [SP], #0x10
    // 0x8469ec: ret
    //     0x8469ec: ret             
    // 0x8469f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8469f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8469f4: b               #0x8469a8
  }
  factory _ NumberFormat._forPattern(/* No info */) {
    // ** addr: 0x846b60, size: 0x40c
    // 0x846b60: EnterFrame
    //     0x846b60: stp             fp, lr, [SP, #-0x10]!
    //     0x846b64: mov             fp, SP
    // 0x846b68: AllocStack(0x58)
    //     0x846b68: sub             SP, SP, #0x58
    // 0x846b6c: SetupParameters(dynamic _ /* r3 => r0, fp-0x28 */, {dynamic currencySymbol, dynamic decimalDigits = Null /* r7, fp-0x20 */, dynamic isForCurrency = false /* r3, fp-0x18 */, dynamic lookupSimpleCurrencySymbol = false /* r5, fp-0x10 */, dynamic name = Null /* r4, fp-0x8 */})
    //     0x846b6c: mov             x0, x3
    //     0x846b70: stur            x3, [fp, #-0x28]
    //     0x846b74: ldur            w1, [x4, #0x13]
    //     0x846b78: ldur            w2, [x4, #0x1f]
    //     0x846b7c: add             x2, x2, HEAP, lsl #32
    //     0x846b80: add             x16, PP, #0x28, lsl #12  ; [pp+0x28580] "currencySymbol"
    //     0x846b84: ldr             x16, [x16, #0x580]
    //     0x846b88: cmp             w2, w16
    //     0x846b8c: b.ne            #0x846b98
    //     0x846b90: movz            x2, #0x1
    //     0x846b94: b               #0x846b9c
    //     0x846b98: movz            x2, #0
    //     0x846b9c: lsl             x3, x2, #1
    //     0x846ba0: lsl             w5, w3, #1
    //     0x846ba4: add             w6, w5, #8
    //     0x846ba8: add             x16, x4, w6, sxtw #1
    //     0x846bac: ldur            w7, [x16, #0xf]
    //     0x846bb0: add             x7, x7, HEAP, lsl #32
    //     0x846bb4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28588] "decimalDigits"
    //     0x846bb8: ldr             x16, [x16, #0x588]
    //     0x846bbc: cmp             w7, w16
    //     0x846bc0: b.ne            #0x846bf4
    //     0x846bc4: add             w2, w5, #0xa
    //     0x846bc8: add             x16, x4, w2, sxtw #1
    //     0x846bcc: ldur            w5, [x16, #0xf]
    //     0x846bd0: add             x5, x5, HEAP, lsl #32
    //     0x846bd4: sub             w2, w1, w5
    //     0x846bd8: add             x5, fp, w2, sxtw #2
    //     0x846bdc: ldr             x5, [x5, #8]
    //     0x846be0: add             w2, w3, #2
    //     0x846be4: sbfx            x3, x2, #1, #0x1f
    //     0x846be8: mov             x7, x5
    //     0x846bec: mov             x2, x3
    //     0x846bf0: b               #0x846bf8
    //     0x846bf4: mov             x7, NULL
    //     0x846bf8: stur            x7, [fp, #-0x20]
    //     0x846bfc: lsl             x3, x2, #1
    //     0x846c00: lsl             w5, w3, #1
    //     0x846c04: add             w6, w5, #8
    //     0x846c08: add             x16, x4, w6, sxtw #1
    //     0x846c0c: ldur            w8, [x16, #0xf]
    //     0x846c10: add             x8, x8, HEAP, lsl #32
    //     0x846c14: add             x16, PP, #0x28, lsl #12  ; [pp+0x28590] "isForCurrency"
    //     0x846c18: ldr             x16, [x16, #0x590]
    //     0x846c1c: cmp             w8, w16
    //     0x846c20: b.ne            #0x846c54
    //     0x846c24: add             w2, w5, #0xa
    //     0x846c28: add             x16, x4, w2, sxtw #1
    //     0x846c2c: ldur            w5, [x16, #0xf]
    //     0x846c30: add             x5, x5, HEAP, lsl #32
    //     0x846c34: sub             w2, w1, w5
    //     0x846c38: add             x5, fp, w2, sxtw #2
    //     0x846c3c: ldr             x5, [x5, #8]
    //     0x846c40: add             w2, w3, #2
    //     0x846c44: sbfx            x3, x2, #1, #0x1f
    //     0x846c48: mov             x2, x3
    //     0x846c4c: mov             x3, x5
    //     0x846c50: b               #0x846c58
    //     0x846c54: add             x3, NULL, #0x30  ; false
    //     0x846c58: stur            x3, [fp, #-0x18]
    //     0x846c5c: lsl             x5, x2, #1
    //     0x846c60: lsl             w6, w5, #1
    //     0x846c64: add             w8, w6, #8
    //     0x846c68: add             x16, x4, w8, sxtw #1
    //     0x846c6c: ldur            w9, [x16, #0xf]
    //     0x846c70: add             x9, x9, HEAP, lsl #32
    //     0x846c74: add             x16, PP, #0x28, lsl #12  ; [pp+0x28598] "lookupSimpleCurrencySymbol"
    //     0x846c78: ldr             x16, [x16, #0x598]
    //     0x846c7c: cmp             w9, w16
    //     0x846c80: b.ne            #0x846cb4
    //     0x846c84: add             w2, w6, #0xa
    //     0x846c88: add             x16, x4, w2, sxtw #1
    //     0x846c8c: ldur            w6, [x16, #0xf]
    //     0x846c90: add             x6, x6, HEAP, lsl #32
    //     0x846c94: sub             w2, w1, w6
    //     0x846c98: add             x6, fp, w2, sxtw #2
    //     0x846c9c: ldr             x6, [x6, #8]
    //     0x846ca0: add             w2, w5, #2
    //     0x846ca4: sbfx            x5, x2, #1, #0x1f
    //     0x846ca8: mov             x2, x5
    //     0x846cac: mov             x5, x6
    //     0x846cb0: b               #0x846cb8
    //     0x846cb4: add             x5, NULL, #0x30  ; false
    //     0x846cb8: stur            x5, [fp, #-0x10]
    //     0x846cbc: lsl             x6, x2, #1
    //     0x846cc0: lsl             w2, w6, #1
    //     0x846cc4: add             w6, w2, #8
    //     0x846cc8: add             x16, x4, w6, sxtw #1
    //     0x846ccc: ldur            w8, [x16, #0xf]
    //     0x846cd0: add             x8, x8, HEAP, lsl #32
    //     0x846cd4: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    //     0x846cd8: cmp             w8, w16
    //     0x846cdc: b.ne            #0x846d04
    //     0x846ce0: add             w6, w2, #0xa
    //     0x846ce4: add             x16, x4, w6, sxtw #1
    //     0x846ce8: ldur            w2, [x16, #0xf]
    //     0x846cec: add             x2, x2, HEAP, lsl #32
    //     0x846cf0: sub             w4, w1, w2
    //     0x846cf4: add             x1, fp, w4, sxtw #2
    //     0x846cf8: ldr             x1, [x1, #8]
    //     0x846cfc: mov             x4, x1
    //     0x846d00: b               #0x846d08
    //     0x846d04: mov             x4, NULL
    //     0x846d08: stur            x4, [fp, #-8]
    // 0x846d0c: CheckStackOverflow
    //     0x846d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x846d10: cmp             SP, x16
    //     0x846d14: b.ls            #0x846f60
    // 0x846d18: r1 = "id_ID"
    //     0x846d18: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0x846d1c: ldr             x1, [x1, #0x200]
    // 0x846d20: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x846d20: add             x2, PP, #0x28, lsl #12  ; [pp+0x285a0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb250de0)
    //     0x846d24: ldr             x2, [x2, #0x5a0]
    // 0x846d28: r0 = verifiedLocale()
    //     0x846d28: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x846d2c: stur            x0, [fp, #-0x30]
    // 0x846d30: r0 = InitLateStaticField(0x14a0) // [package:intl/number_symbols_data.dart] ::numberFormatSymbols
    //     0x846d30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x846d34: ldr             x0, [x0, #0x2940]
    //     0x846d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x846d3c: cmp             w0, w16
    //     0x846d40: b.ne            #0x846d50
    //     0x846d44: add             x2, PP, #0x28, lsl #12  ; [pp+0x285a8] Field <::.numberFormatSymbols>: static late final (offset: 0x14a0)
    //     0x846d48: ldr             x2, [x2, #0x5a8]
    //     0x846d4c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x846d50: mov             x1, x0
    // 0x846d54: ldur            x2, [fp, #-0x30]
    // 0x846d58: stur            x0, [fp, #-0x38]
    // 0x846d5c: r0 = _getValueOrData()
    //     0x846d5c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x846d60: mov             x1, x0
    // 0x846d64: ldur            x0, [fp, #-0x38]
    // 0x846d68: LoadField: r2 = r0->field_f
    //     0x846d68: ldur            w2, [x0, #0xf]
    // 0x846d6c: DecompressPointer r2
    //     0x846d6c: add             x2, x2, HEAP, lsl #32
    // 0x846d70: cmp             w2, w1
    // 0x846d74: b.ne            #0x846d80
    // 0x846d78: r3 = Null
    //     0x846d78: mov             x3, NULL
    // 0x846d7c: b               #0x846d84
    // 0x846d80: mov             x3, x1
    // 0x846d84: mov             x0, x3
    // 0x846d88: stur            x3, [fp, #-0x38]
    // 0x846d8c: r2 = Null
    //     0x846d8c: mov             x2, NULL
    // 0x846d90: r1 = Null
    //     0x846d90: mov             x1, NULL
    // 0x846d94: r4 = 60
    //     0x846d94: movz            x4, #0x3c
    // 0x846d98: branchIfSmi(r0, 0x846da4)
    //     0x846d98: tbz             w0, #0, #0x846da4
    // 0x846d9c: r4 = LoadClassIdInstr(r0)
    //     0x846d9c: ldur            x4, [x0, #-1]
    //     0x846da0: ubfx            x4, x4, #0xc, #0x14
    // 0x846da4: cmp             x4, #0x53e
    // 0x846da8: b.eq            #0x846dc0
    // 0x846dac: r8 = NumberSymbols
    //     0x846dac: add             x8, PP, #0x28, lsl #12  ; [pp+0x285b0] Type: NumberSymbols
    //     0x846db0: ldr             x8, [x8, #0x5b0]
    // 0x846db4: r3 = Null
    //     0x846db4: add             x3, PP, #0x28, lsl #12  ; [pp+0x285b8] Null
    //     0x846db8: ldr             x3, [x3, #0x5b8]
    // 0x846dbc: r0 = NumberSymbols()
    //     0x846dbc: bl              #0x84a7e8  ; IsType_NumberSymbols_Stub
    // 0x846dc0: ldur            x2, [fp, #-0x38]
    // 0x846dc4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x846dc4: ldur            w3, [x2, #0x17]
    // 0x846dc8: DecompressPointer r3
    //     0x846dc8: add             x3, x3, HEAP, lsl #32
    // 0x846dcc: LoadField: r0 = r3->field_7
    //     0x846dcc: ldur            w0, [x3, #7]
    // 0x846dd0: r1 = LoadInt32Instr(r0)
    //     0x846dd0: sbfx            x1, x0, #1, #0x1f
    // 0x846dd4: mov             x0, x1
    // 0x846dd8: r1 = 0
    //     0x846dd8: movz            x1, #0
    // 0x846ddc: cmp             x1, x0
    // 0x846de0: b.hs            #0x846f68
    // 0x846de4: r0 = LoadClassIdInstr(r3)
    //     0x846de4: ldur            x0, [x3, #-1]
    //     0x846de8: ubfx            x0, x0, #0xc, #0x14
    // 0x846dec: lsl             x0, x0, #1
    // 0x846df0: cmp             w0, #0xbc
    // 0x846df4: b.ne            #0x846e04
    // 0x846df8: ArrayLoad: r0 = r3[-8]  ; TypedUnsigned_1
    //     0x846df8: ldrb            w0, [x3, #0xf]
    // 0x846dfc: mov             x1, x0
    // 0x846e00: b               #0x846e0c
    // 0x846e04: ldurh           w0, [x3, #0xf]
    // 0x846e08: mov             x1, x0
    // 0x846e0c: ldur            x0, [fp, #-8]
    // 0x846e10: stur            x1, [fp, #-0x40]
    // 0x846e14: r0 = InitLateStaticField(0x14a8) // [package:intl/src/intl/constants.dart] ::asciiZeroCodeUnit
    //     0x846e14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x846e18: ldr             x0, [x0, #0x2950]
    //     0x846e1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x846e20: cmp             w0, w16
    //     0x846e24: b.ne            #0x846e34
    //     0x846e28: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b830] Field <::.asciiZeroCodeUnit>: static late final (offset: 0x14a8)
    //     0x846e2c: ldr             x2, [x2, #0x830]
    //     0x846e30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x846e34: r1 = LoadInt32Instr(r0)
    //     0x846e34: sbfx            x1, x0, #1, #0x1f
    // 0x846e38: ldur            x0, [fp, #-0x40]
    // 0x846e3c: sub             x6, x0, x1
    // 0x846e40: ldur            x0, [fp, #-8]
    // 0x846e44: stur            x6, [fp, #-0x48]
    // 0x846e48: cmp             w0, NULL
    // 0x846e4c: b.ne            #0x846e64
    // 0x846e50: ldur            x1, [fp, #-0x38]
    // 0x846e54: LoadField: r0 = r1->field_37
    //     0x846e54: ldur            w0, [x1, #0x37]
    // 0x846e58: DecompressPointer r0
    //     0x846e58: add             x0, x0, HEAP, lsl #32
    // 0x846e5c: mov             x2, x0
    // 0x846e60: b               #0x846e6c
    // 0x846e64: ldur            x1, [fp, #-0x38]
    // 0x846e68: mov             x2, x0
    // 0x846e6c: ldur            x0, [fp, #-0x10]
    // 0x846e70: stur            x2, [fp, #-8]
    // 0x846e74: tbnz            w0, #4, #0x846ed0
    // 0x846e78: r0 = InitLateStaticField(0x14ac) // [package:intl/src/intl/constants.dart] ::simpleCurrencySymbols
    //     0x846e78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x846e7c: ldr             x0, [x0, #0x2958]
    //     0x846e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x846e84: cmp             w0, w16
    //     0x846e88: b.ne            #0x846e98
    //     0x846e8c: add             x2, PP, #0x28, lsl #12  ; [pp+0x285c8] Field <::.simpleCurrencySymbols>: static late final (offset: 0x14ac)
    //     0x846e90: ldr             x2, [x2, #0x5c8]
    //     0x846e94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x846e98: mov             x1, x0
    // 0x846e9c: ldur            x2, [fp, #-8]
    // 0x846ea0: stur            x0, [fp, #-0x10]
    // 0x846ea4: r0 = _getValueOrData()
    //     0x846ea4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x846ea8: mov             x1, x0
    // 0x846eac: ldur            x0, [fp, #-0x10]
    // 0x846eb0: LoadField: r2 = r0->field_f
    //     0x846eb0: ldur            w2, [x0, #0xf]
    // 0x846eb4: DecompressPointer r2
    //     0x846eb4: add             x2, x2, HEAP, lsl #32
    // 0x846eb8: cmp             w2, w1
    // 0x846ebc: b.ne            #0x846ec8
    // 0x846ec0: r0 = Null
    //     0x846ec0: mov             x0, NULL
    // 0x846ec4: b               #0x846ed4
    // 0x846ec8: mov             x0, x1
    // 0x846ecc: b               #0x846ed4
    // 0x846ed0: r0 = Null
    //     0x846ed0: mov             x0, NULL
    // 0x846ed4: cmp             w0, NULL
    // 0x846ed8: b.ne            #0x846ee4
    // 0x846edc: ldur            x5, [fp, #-8]
    // 0x846ee0: b               #0x846ee8
    // 0x846ee4: mov             x5, x0
    // 0x846ee8: stur            x5, [fp, #-0x10]
    // 0x846eec: ldur            x16, [fp, #-0x28]
    // 0x846ef0: ldur            lr, [fp, #-0x38]
    // 0x846ef4: stp             lr, x16, [SP]
    // 0x846ef8: ldur            x0, [fp, #-0x28]
    // 0x846efc: ClosureCall
    //     0x846efc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x846f00: ldur            x2, [x0, #0x1f]
    //     0x846f04: blr             x2
    // 0x846f08: ldur            x1, [fp, #-0x38]
    // 0x846f0c: mov             x2, x0
    // 0x846f10: ldur            x3, [fp, #-0x18]
    // 0x846f14: ldur            x5, [fp, #-0x10]
    // 0x846f18: ldur            x6, [fp, #-8]
    // 0x846f1c: ldur            x7, [fp, #-0x20]
    // 0x846f20: stur            x0, [fp, #-8]
    // 0x846f24: r0 = parse()
    //     0x846f24: bl              #0x847348  ; [package:intl/src/intl/number_format_parser.dart] NumberFormatParser::parse
    // 0x846f28: stur            x0, [fp, #-0x10]
    // 0x846f2c: r0 = NumberFormat()
    //     0x846f2c: bl              #0x84733c  ; AllocateNumberFormatStub -> NumberFormat (size=0x88)
    // 0x846f30: mov             x1, x0
    // 0x846f34: ldur            x2, [fp, #-0x30]
    // 0x846f38: ldur            x3, [fp, #-8]
    // 0x846f3c: ldur            x5, [fp, #-0x38]
    // 0x846f40: ldur            x6, [fp, #-0x48]
    // 0x846f44: ldur            x7, [fp, #-0x10]
    // 0x846f48: stur            x0, [fp, #-8]
    // 0x846f4c: r0 = NumberFormat._()
    //     0x846f4c: bl              #0x846f6c  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._
    // 0x846f50: ldur            x0, [fp, #-8]
    // 0x846f54: LeaveFrame
    //     0x846f54: mov             SP, fp
    //     0x846f58: ldp             fp, lr, [SP], #0x10
    // 0x846f5c: ret
    //     0x846f5c: ret             
    // 0x846f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x846f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x846f64: b               #0x846d18
    // 0x846f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x846f68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ NumberFormat._(/* No info */) {
    // ** addr: 0x846f6c, size: 0x328
    // 0x846f6c: EnterFrame
    //     0x846f6c: stp             fp, lr, [SP, #-0x10]!
    //     0x846f70: mov             fp, SP
    // 0x846f74: AllocStack(0x48)
    //     0x846f74: sub             SP, SP, #0x48
    // 0x846f78: r0 = false
    //     0x846f78: add             x0, NULL, #0x30  ; false
    // 0x846f7c: mov             x4, x1
    // 0x846f80: stur            x2, [fp, #-0x10]
    // 0x846f84: mov             x16, x3
    // 0x846f88: mov             x3, x2
    // 0x846f8c: mov             x2, x16
    // 0x846f90: stur            x1, [fp, #-8]
    // 0x846f94: mov             x1, x5
    // 0x846f98: stur            x2, [fp, #-0x18]
    // 0x846f9c: stur            x5, [fp, #-0x20]
    // 0x846fa0: stur            x6, [fp, #-0x28]
    // 0x846fa4: stur            x7, [fp, #-0x30]
    // 0x846fa8: CheckStackOverflow
    //     0x846fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x846fac: cmp             SP, x16
    //     0x846fb0: b.ls            #0x84726c
    // 0x846fb4: StoreField: r4->field_5b = r0
    //     0x846fb4: stur            w0, [x4, #0x5b]
    // 0x846fb8: r0 = StringBuffer()
    //     0x846fb8: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x846fbc: mov             x1, x0
    // 0x846fc0: stur            x0, [fp, #-0x38]
    // 0x846fc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x846fc4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x846fc8: r0 = StringBuffer()
    //     0x846fc8: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x846fcc: ldur            x0, [fp, #-0x38]
    // 0x846fd0: ldur            x2, [fp, #-8]
    // 0x846fd4: StoreField: r2->field_7b = r0
    //     0x846fd4: stur            w0, [x2, #0x7b]
    //     0x846fd8: ldurb           w16, [x2, #-1]
    //     0x846fdc: ldurb           w17, [x0, #-1]
    //     0x846fe0: and             x16, x17, x16, lsr #2
    //     0x846fe4: tst             x16, HEAP, lsr #32
    //     0x846fe8: b.eq            #0x846ff0
    //     0x846fec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x846ff0: ldur            x0, [fp, #-0x10]
    // 0x846ff4: StoreField: r2->field_73 = r0
    //     0x846ff4: stur            w0, [x2, #0x73]
    //     0x846ff8: ldurb           w16, [x2, #-1]
    //     0x846ffc: ldurb           w17, [x0, #-1]
    //     0x847000: and             x16, x17, x16, lsr #2
    //     0x847004: tst             x16, HEAP, lsr #32
    //     0x847008: b.eq            #0x847010
    //     0x84700c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x847010: ldur            x0, [fp, #-0x18]
    // 0x847014: StoreField: r2->field_6f = r0
    //     0x847014: stur            w0, [x2, #0x6f]
    //     0x847018: ldurb           w16, [x2, #-1]
    //     0x84701c: ldurb           w17, [x0, #-1]
    //     0x847020: and             x16, x17, x16, lsr #2
    //     0x847024: tst             x16, HEAP, lsr #32
    //     0x847028: b.eq            #0x847030
    //     0x84702c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x847030: ldur            x0, [fp, #-0x20]
    // 0x847034: StoreField: r2->field_77 = r0
    //     0x847034: stur            w0, [x2, #0x77]
    //     0x847038: ldurb           w16, [x2, #-1]
    //     0x84703c: ldurb           w17, [x0, #-1]
    //     0x847040: and             x16, x17, x16, lsr #2
    //     0x847044: tst             x16, HEAP, lsr #32
    //     0x847048: b.eq            #0x847050
    //     0x84704c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x847050: ldur            x0, [fp, #-0x28]
    // 0x847054: StoreField: r2->field_7f = r0
    //     0x847054: stur            x0, [x2, #0x7f]
    // 0x847058: ldur            x3, [fp, #-0x30]
    // 0x84705c: LoadField: r0 = r3->field_b
    //     0x84705c: ldur            w0, [x3, #0xb]
    // 0x847060: DecompressPointer r0
    //     0x847060: add             x0, x0, HEAP, lsl #32
    // 0x847064: StoreField: r2->field_b = r0
    //     0x847064: stur            w0, [x2, #0xb]
    //     0x847068: ldurb           w16, [x2, #-1]
    //     0x84706c: ldurb           w17, [x0, #-1]
    //     0x847070: and             x16, x17, x16, lsr #2
    //     0x847074: tst             x16, HEAP, lsr #32
    //     0x847078: b.eq            #0x847080
    //     0x84707c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x847080: LoadField: r0 = r3->field_7
    //     0x847080: ldur            w0, [x3, #7]
    // 0x847084: DecompressPointer r0
    //     0x847084: add             x0, x0, HEAP, lsl #32
    // 0x847088: StoreField: r2->field_7 = r0
    //     0x847088: stur            w0, [x2, #7]
    //     0x84708c: ldurb           w16, [x2, #-1]
    //     0x847090: ldurb           w17, [x0, #-1]
    //     0x847094: and             x16, x17, x16, lsr #2
    //     0x847098: tst             x16, HEAP, lsr #32
    //     0x84709c: b.eq            #0x8470a4
    //     0x8470a0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8470a4: LoadField: r0 = r3->field_13
    //     0x8470a4: ldur            w0, [x3, #0x13]
    // 0x8470a8: DecompressPointer r0
    //     0x8470a8: add             x0, x0, HEAP, lsl #32
    // 0x8470ac: StoreField: r2->field_13 = r0
    //     0x8470ac: stur            w0, [x2, #0x13]
    //     0x8470b0: ldurb           w16, [x2, #-1]
    //     0x8470b4: ldurb           w17, [x0, #-1]
    //     0x8470b8: and             x16, x17, x16, lsr #2
    //     0x8470bc: tst             x16, HEAP, lsr #32
    //     0x8470c0: b.eq            #0x8470c8
    //     0x8470c4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8470c8: LoadField: r0 = r3->field_f
    //     0x8470c8: ldur            w0, [x3, #0xf]
    // 0x8470cc: DecompressPointer r0
    //     0x8470cc: add             x0, x0, HEAP, lsl #32
    // 0x8470d0: StoreField: r2->field_f = r0
    //     0x8470d0: stur            w0, [x2, #0xf]
    //     0x8470d4: ldurb           w16, [x2, #-1]
    //     0x8470d8: ldurb           w17, [x0, #-1]
    //     0x8470dc: and             x16, x17, x16, lsr #2
    //     0x8470e0: tst             x16, HEAP, lsr #32
    //     0x8470e4: b.eq            #0x8470ec
    //     0x8470e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8470ec: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x8470ec: ldur            x4, [x3, #0x17]
    // 0x8470f0: StoreField: r2->field_5f = r4
    //     0x8470f0: stur            x4, [x2, #0x5f]
    // 0x8470f4: r0 = BoxInt64Instr(r4)
    //     0x8470f4: sbfiz           x0, x4, #1, #0x1f
    //     0x8470f8: cmp             x4, x0, asr #1
    //     0x8470fc: b.eq            #0x847108
    //     0x847100: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x847104: stur            x4, [x0, #7]
    // 0x847108: r1 = 60
    //     0x847108: movz            x1, #0x3c
    // 0x84710c: branchIfSmi(r0, 0x847118)
    //     0x84710c: tbz             w0, #0, #0x847118
    // 0x847110: r1 = LoadClassIdInstr(r0)
    //     0x847110: ldur            x1, [x0, #-1]
    //     0x847114: ubfx            x1, x1, #0xc, #0x14
    // 0x847118: str             x0, [SP]
    // 0x84711c: mov             x0, x1
    // 0x847120: r0 = GDT[cid_x0 + -0xffa]()
    //     0x847120: sub             lr, x0, #0xffa
    //     0x847124: ldr             lr, [x21, lr, lsl #3]
    //     0x847128: blr             lr
    // 0x84712c: LoadField: d0 = r0->field_7
    //     0x84712c: ldur            d0, [x0, #7]
    // 0x847130: stp             fp, lr, [SP, #-0x10]!
    // 0x847134: mov             fp, SP
    // 0x847138: CallRuntime_LibcLog(double) -> double
    //     0x847138: and             SP, SP, #0xfffffffffffffff0
    //     0x84713c: mov             sp, SP
    //     0x847140: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x847144: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x847148: blr             x16
    //     0x84714c: movz            x16, #0x8
    //     0x847150: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x847154: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x847158: sub             sp, x16, #1, lsl #12
    //     0x84715c: mov             SP, fp
    //     0x847160: ldp             fp, lr, [SP], #0x10
    // 0x847164: stur            d0, [fp, #-0x40]
    // 0x847168: r0 = InitLateStaticField(0x14b0) // [package:intl/src/intl/number_format_parser.dart] ::_ln10
    //     0x847168: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x84716c: ldr             x0, [x0, #0x2960]
    //     0x847170: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x847174: cmp             w0, w16
    //     0x847178: b.ne            #0x847188
    //     0x84717c: add             x2, PP, #0x28, lsl #12  ; [pp+0x285d0] Field <::._ln10@1565166373>: static late final (offset: 0x14b0)
    //     0x847180: ldr             x2, [x2, #0x5d0]
    //     0x847184: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x847188: LoadField: d0 = r0->field_7
    //     0x847188: ldur            d0, [x0, #7]
    // 0x84718c: ldur            d1, [fp, #-0x40]
    // 0x847190: fdiv            d2, d1, d0
    // 0x847194: mov             v0.16b, v2.16b
    // 0x847198: stp             fp, lr, [SP, #-0x10]!
    // 0x84719c: mov             fp, SP
    // 0x8471a0: CallRuntime_LibcRound(double) -> double
    //     0x8471a0: and             SP, SP, #0xfffffffffffffff0
    //     0x8471a4: mov             sp, SP
    //     0x8471a8: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x8471ac: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8471b0: blr             x16
    //     0x8471b4: movz            x16, #0x8
    //     0x8471b8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8471bc: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8471c0: sub             sp, x16, #1, lsl #12
    //     0x8471c4: mov             SP, fp
    //     0x8471c8: ldp             fp, lr, [SP], #0x10
    // 0x8471cc: fcmp            d0, d0
    // 0x8471d0: b.vs            #0x847274
    // 0x8471d4: fcvtzs          x1, d0
    // 0x8471d8: asr             x16, x1, #0x1e
    // 0x8471dc: cmp             x16, x1, asr #63
    // 0x8471e0: b.ne            #0x847274
    // 0x8471e4: lsl             x1, x1, #1
    // 0x8471e8: r2 = LoadInt32Instr(r1)
    //     0x8471e8: sbfx            x2, x1, #1, #0x1f
    //     0x8471ec: tbz             w1, #0, #0x8471f4
    //     0x8471f0: ldur            x2, [x1, #7]
    // 0x8471f4: ldur            x1, [fp, #-8]
    // 0x8471f8: StoreField: r1->field_67 = r2
    //     0x8471f8: stur            x2, [x1, #0x67]
    // 0x8471fc: ldur            x2, [fp, #-0x30]
    // 0x847200: LoadField: r3 = r2->field_5f
    //     0x847200: ldur            w3, [x2, #0x5f]
    // 0x847204: DecompressPointer r3
    //     0x847204: add             x3, x3, HEAP, lsl #32
    // 0x847208: StoreField: r1->field_2f = r3
    //     0x847208: stur            w3, [x1, #0x2f]
    // 0x84720c: LoadField: r3 = r2->field_1f
    //     0x84720c: ldur            x3, [x2, #0x1f]
    // 0x847210: StoreField: r1->field_53 = r3
    //     0x847210: stur            x3, [x1, #0x53]
    // 0x847214: LoadField: r3 = r2->field_27
    //     0x847214: ldur            x3, [x2, #0x27]
    // 0x847218: StoreField: r1->field_33 = r3
    //     0x847218: stur            x3, [x1, #0x33]
    // 0x84721c: LoadField: r3 = r2->field_2f
    //     0x84721c: ldur            x3, [x2, #0x2f]
    // 0x847220: StoreField: r1->field_3b = r3
    //     0x847220: stur            x3, [x1, #0x3b]
    // 0x847224: LoadField: r3 = r2->field_37
    //     0x847224: ldur            x3, [x2, #0x37]
    // 0x847228: StoreField: r1->field_43 = r3
    //     0x847228: stur            x3, [x1, #0x43]
    // 0x84722c: LoadField: r3 = r2->field_3f
    //     0x84722c: ldur            x3, [x2, #0x3f]
    // 0x847230: StoreField: r1->field_4b = r3
    //     0x847230: stur            x3, [x1, #0x4b]
    // 0x847234: LoadField: r3 = r2->field_47
    //     0x847234: ldur            x3, [x2, #0x47]
    // 0x847238: ArrayStore: r1[0] = r3  ; List_8
    //     0x847238: stur            x3, [x1, #0x17]
    // 0x84723c: LoadField: r3 = r2->field_4f
    //     0x84723c: ldur            x3, [x2, #0x4f]
    // 0x847240: StoreField: r1->field_1f = r3
    //     0x847240: stur            x3, [x1, #0x1f]
    // 0x847244: LoadField: r3 = r2->field_5b
    //     0x847244: ldur            w3, [x2, #0x5b]
    // 0x847248: DecompressPointer r3
    //     0x847248: add             x3, x3, HEAP, lsl #32
    // 0x84724c: StoreField: r1->field_2b = r3
    //     0x84724c: stur            w3, [x1, #0x2b]
    // 0x847250: LoadField: r3 = r2->field_57
    //     0x847250: ldur            w3, [x2, #0x57]
    // 0x847254: DecompressPointer r3
    //     0x847254: add             x3, x3, HEAP, lsl #32
    // 0x847258: StoreField: r1->field_27 = r3
    //     0x847258: stur            w3, [x1, #0x27]
    // 0x84725c: r0 = Null
    //     0x84725c: mov             x0, NULL
    // 0x847260: LeaveFrame
    //     0x847260: mov             SP, fp
    //     0x847264: ldp             fp, lr, [SP], #0x10
    // 0x847268: ret
    //     0x847268: ret             
    // 0x84726c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x84726c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x847270: b               #0x846fb4
    // 0x847274: SaveReg d0
    //     0x847274: str             q0, [SP, #-0x10]!
    // 0x847278: r0 = 74
    //     0x847278: movz            x0, #0x4a
    // 0x84727c: r30 = DoubleToIntegerStub
    //     0x84727c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x847280: LoadField: r30 = r30->field_7
    //     0x847280: ldur            lr, [lr, #7]
    // 0x847284: blr             lr
    // 0x847288: mov             x1, x0
    // 0x84728c: RestoreReg d0
    //     0x84728c: ldr             q0, [SP], #0x10
    // 0x847290: b               #0x8471e8
  }
  [closure] static bool localeExists(dynamic, String?) {
    // ** addr: 0x850de0, size: 0x30
    // 0x850de0: EnterFrame
    //     0x850de0: stp             fp, lr, [SP, #-0x10]!
    //     0x850de4: mov             fp, SP
    // 0x850de8: CheckStackOverflow
    //     0x850de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x850dec: cmp             SP, x16
    //     0x850df0: b.ls            #0x850e08
    // 0x850df4: ldr             x1, [fp, #0x10]
    // 0x850df8: r0 = localeExists()
    //     0x850df8: bl              #0x850e10  ; [package:intl/src/intl/number_format.dart] NumberFormat::localeExists
    // 0x850dfc: LeaveFrame
    //     0x850dfc: mov             SP, fp
    //     0x850e00: ldp             fp, lr, [SP], #0x10
    // 0x850e04: ret
    //     0x850e04: ret             
    // 0x850e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x850e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x850e0c: b               #0x850df4
  }
  static _ localeExists(/* No info */) {
    // ** addr: 0x850e10, size: 0x78
    // 0x850e10: EnterFrame
    //     0x850e10: stp             fp, lr, [SP, #-0x10]!
    //     0x850e14: mov             fp, SP
    // 0x850e18: AllocStack(0x8)
    //     0x850e18: sub             SP, SP, #8
    // 0x850e1c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x850e1c: mov             x2, x1
    //     0x850e20: stur            x1, [fp, #-8]
    // 0x850e24: CheckStackOverflow
    //     0x850e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x850e28: cmp             SP, x16
    //     0x850e2c: b.ls            #0x850e80
    // 0x850e30: cmp             w2, NULL
    // 0x850e34: b.ne            #0x850e48
    // 0x850e38: r0 = false
    //     0x850e38: add             x0, NULL, #0x30  ; false
    // 0x850e3c: LeaveFrame
    //     0x850e3c: mov             SP, fp
    //     0x850e40: ldp             fp, lr, [SP], #0x10
    // 0x850e44: ret
    //     0x850e44: ret             
    // 0x850e48: r0 = InitLateStaticField(0x14a0) // [package:intl/number_symbols_data.dart] ::numberFormatSymbols
    //     0x850e48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x850e4c: ldr             x0, [x0, #0x2940]
    //     0x850e50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x850e54: cmp             w0, w16
    //     0x850e58: b.ne            #0x850e68
    //     0x850e5c: add             x2, PP, #0x28, lsl #12  ; [pp+0x285a8] Field <::.numberFormatSymbols>: static late final (offset: 0x14a0)
    //     0x850e60: ldr             x2, [x2, #0x5a8]
    //     0x850e64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x850e68: mov             x1, x0
    // 0x850e6c: ldur            x2, [fp, #-8]
    // 0x850e70: r0 = containsKey()
    //     0x850e70: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x850e74: LeaveFrame
    //     0x850e74: mov             SP, fp
    //     0x850e78: ldp             fp, lr, [SP], #0x10
    // 0x850e7c: ret
    //     0x850e7c: ret             
    // 0x850e80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x850e80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x850e84: b               #0x850e30
  }
  _ format(/* No info */) {
    // ** addr: 0x8f5664, size: 0x15c
    // 0x8f5664: EnterFrame
    //     0x8f5664: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5668: mov             fp, SP
    // 0x8f566c: AllocStack(0x20)
    //     0x8f566c: sub             SP, SP, #0x20
    // 0x8f5670: SetupParameters(NumberFormat this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x8f5670: mov             x3, x1
    //     0x8f5674: mov             x0, x2
    //     0x8f5678: stur            x1, [fp, #-8]
    //     0x8f567c: stur            x2, [fp, #-0x10]
    // 0x8f5680: CheckStackOverflow
    //     0x8f5680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5684: cmp             SP, x16
    //     0x8f5688: b.ls            #0x8f57b8
    // 0x8f568c: mov             x1, x3
    // 0x8f5690: mov             x2, x0
    // 0x8f5694: r0 = _isNaN()
    //     0x8f5694: bl              #0x8f7d20  ; [package:intl/src/intl/number_format.dart] NumberFormat::_isNaN
    // 0x8f5698: tbnz            w0, #4, #0x8f56bc
    // 0x8f569c: ldur            x0, [fp, #-8]
    // 0x8f56a0: LoadField: r1 = r0->field_77
    //     0x8f56a0: ldur            w1, [x0, #0x77]
    // 0x8f56a4: DecompressPointer r1
    //     0x8f56a4: add             x1, x1, HEAP, lsl #32
    // 0x8f56a8: LoadField: r0 = r1->field_2f
    //     0x8f56a8: ldur            w0, [x1, #0x2f]
    // 0x8f56ac: DecompressPointer r0
    //     0x8f56ac: add             x0, x0, HEAP, lsl #32
    // 0x8f56b0: LeaveFrame
    //     0x8f56b0: mov             SP, fp
    //     0x8f56b4: ldp             fp, lr, [SP], #0x10
    // 0x8f56b8: ret
    //     0x8f56b8: ret             
    // 0x8f56bc: ldur            x0, [fp, #-8]
    // 0x8f56c0: mov             x1, x0
    // 0x8f56c4: ldur            x2, [fp, #-0x10]
    // 0x8f56c8: r0 = _isInfinite()
    //     0x8f56c8: bl              #0x8f7cb0  ; [package:intl/src/intl/number_format.dart] NumberFormat::_isInfinite
    // 0x8f56cc: tbnz            w0, #4, #0x8f5728
    // 0x8f56d0: ldur            x0, [fp, #-8]
    // 0x8f56d4: mov             x1, x0
    // 0x8f56d8: ldur            x2, [fp, #-0x10]
    // 0x8f56dc: r0 = _signPrefix()
    //     0x8f56dc: bl              #0x8f7c3c  ; [package:intl/src/intl/number_format.dart] NumberFormat::_signPrefix
    // 0x8f56e0: r1 = Null
    //     0x8f56e0: mov             x1, NULL
    // 0x8f56e4: r2 = 4
    //     0x8f56e4: movz            x2, #0x4
    // 0x8f56e8: stur            x0, [fp, #-0x18]
    // 0x8f56ec: r0 = AllocateArray()
    //     0x8f56ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8f56f0: mov             x1, x0
    // 0x8f56f4: ldur            x0, [fp, #-0x18]
    // 0x8f56f8: StoreField: r1->field_f = r0
    //     0x8f56f8: stur            w0, [x1, #0xf]
    // 0x8f56fc: ldur            x0, [fp, #-8]
    // 0x8f5700: LoadField: r2 = r0->field_77
    //     0x8f5700: ldur            w2, [x0, #0x77]
    // 0x8f5704: DecompressPointer r2
    //     0x8f5704: add             x2, x2, HEAP, lsl #32
    // 0x8f5708: LoadField: r0 = r2->field_2b
    //     0x8f5708: ldur            w0, [x2, #0x2b]
    // 0x8f570c: DecompressPointer r0
    //     0x8f570c: add             x0, x0, HEAP, lsl #32
    // 0x8f5710: StoreField: r1->field_13 = r0
    //     0x8f5710: stur            w0, [x1, #0x13]
    // 0x8f5714: str             x1, [SP]
    // 0x8f5718: r0 = _interpolate()
    //     0x8f5718: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8f571c: LeaveFrame
    //     0x8f571c: mov             SP, fp
    //     0x8f5720: ldp             fp, lr, [SP], #0x10
    // 0x8f5724: ret
    //     0x8f5724: ret             
    // 0x8f5728: ldur            x0, [fp, #-8]
    // 0x8f572c: mov             x1, x0
    // 0x8f5730: ldur            x2, [fp, #-0x10]
    // 0x8f5734: r0 = _signPrefix()
    //     0x8f5734: bl              #0x8f7c3c  ; [package:intl/src/intl/number_format.dart] NumberFormat::_signPrefix
    // 0x8f5738: ldur            x1, [fp, #-8]
    // 0x8f573c: mov             x2, x0
    // 0x8f5740: r0 = _add()
    //     0x8f5740: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f5744: ldur            x16, [fp, #-0x10]
    // 0x8f5748: str             x16, [SP]
    // 0x8f574c: r4 = 0
    //     0x8f574c: movz            x4, #0
    // 0x8f5750: ldr             x0, [SP]
    // 0x8f5754: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5754: add             x16, PP, #0x27, lsl #12  ; [pp+0x27158] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5758: ldp             x5, lr, [x16, #0x158]
    // 0x8f575c: blr             lr
    // 0x8f5760: ldur            x1, [fp, #-8]
    // 0x8f5764: mov             x2, x0
    // 0x8f5768: r0 = _formatNumber()
    //     0x8f5768: bl              #0x8f5848  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatNumber
    // 0x8f576c: ldur            x1, [fp, #-8]
    // 0x8f5770: ldur            x2, [fp, #-0x10]
    // 0x8f5774: r0 = _signSuffix()
    //     0x8f5774: bl              #0x8f57d8  ; [package:intl/src/intl/number_format.dart] NumberFormat::_signSuffix
    // 0x8f5778: ldur            x1, [fp, #-8]
    // 0x8f577c: mov             x2, x0
    // 0x8f5780: r0 = _add()
    //     0x8f5780: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f5784: ldur            x0, [fp, #-8]
    // 0x8f5788: LoadField: r1 = r0->field_7b
    //     0x8f5788: ldur            w1, [x0, #0x7b]
    // 0x8f578c: DecompressPointer r1
    //     0x8f578c: add             x1, x1, HEAP, lsl #32
    // 0x8f5790: stur            x1, [fp, #-0x10]
    // 0x8f5794: str             x1, [SP]
    // 0x8f5798: r0 = toString()
    //     0x8f5798: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x8f579c: ldur            x1, [fp, #-0x10]
    // 0x8f57a0: stur            x0, [fp, #-8]
    // 0x8f57a4: r0 = clear()
    //     0x8f57a4: bl              #0x8f57c0  ; [dart:core] StringBuffer::clear
    // 0x8f57a8: ldur            x0, [fp, #-8]
    // 0x8f57ac: LeaveFrame
    //     0x8f57ac: mov             SP, fp
    //     0x8f57b0: ldp             fp, lr, [SP], #0x10
    // 0x8f57b4: ret
    //     0x8f57b4: ret             
    // 0x8f57b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f57b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f57bc: b               #0x8f568c
  }
  _ _signSuffix(/* No info */) {
    // ** addr: 0x8f57d8, size: 0x70
    // 0x8f57d8: EnterFrame
    //     0x8f57d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f57dc: mov             fp, SP
    // 0x8f57e0: AllocStack(0x10)
    //     0x8f57e0: sub             SP, SP, #0x10
    // 0x8f57e4: SetupParameters(NumberFormat this /* r1 => r1, fp-0x8 */)
    //     0x8f57e4: stur            x1, [fp, #-8]
    // 0x8f57e8: CheckStackOverflow
    //     0x8f57e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f57ec: cmp             SP, x16
    //     0x8f57f0: b.ls            #0x8f5840
    // 0x8f57f4: str             x2, [SP]
    // 0x8f57f8: r4 = 0
    //     0x8f57f8: movz            x4, #0
    // 0x8f57fc: ldr             x0, [SP]
    // 0x8f5800: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5800: add             x16, PP, #0x27, lsl #12  ; [pp+0x27168] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5804: ldp             x5, lr, [x16, #0x168]
    // 0x8f5808: blr             lr
    // 0x8f580c: tbnz            w0, #4, #0x8f5824
    // 0x8f5810: ldur            x1, [fp, #-8]
    // 0x8f5814: LoadField: r2 = r1->field_f
    //     0x8f5814: ldur            w2, [x1, #0xf]
    // 0x8f5818: DecompressPointer r2
    //     0x8f5818: add             x2, x2, HEAP, lsl #32
    // 0x8f581c: mov             x0, x2
    // 0x8f5820: b               #0x8f5834
    // 0x8f5824: ldur            x1, [fp, #-8]
    // 0x8f5828: LoadField: r2 = r1->field_13
    //     0x8f5828: ldur            w2, [x1, #0x13]
    // 0x8f582c: DecompressPointer r2
    //     0x8f582c: add             x2, x2, HEAP, lsl #32
    // 0x8f5830: mov             x0, x2
    // 0x8f5834: LeaveFrame
    //     0x8f5834: mov             SP, fp
    //     0x8f5838: ldp             fp, lr, [SP], #0x10
    // 0x8f583c: ret
    //     0x8f583c: ret             
    // 0x8f5840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f5840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5844: b               #0x8f57f4
  }
  _ _formatNumber(/* No info */) {
    // ** addr: 0x8f5848, size: 0x9c
    // 0x8f5848: EnterFrame
    //     0x8f5848: stp             fp, lr, [SP, #-0x10]!
    //     0x8f584c: mov             fp, SP
    // 0x8f5850: AllocStack(0x10)
    //     0x8f5850: sub             SP, SP, #0x10
    // 0x8f5854: SetupParameters(NumberFormat this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8f5854: mov             x4, x1
    //     0x8f5858: mov             x3, x2
    //     0x8f585c: stur            x1, [fp, #-8]
    //     0x8f5860: stur            x2, [fp, #-0x10]
    // 0x8f5864: CheckStackOverflow
    //     0x8f5864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5868: cmp             SP, x16
    //     0x8f586c: b.ls            #0x8f58dc
    // 0x8f5870: LoadField: r0 = r4->field_2f
    //     0x8f5870: ldur            w0, [x4, #0x2f]
    // 0x8f5874: DecompressPointer r0
    //     0x8f5874: add             x0, x0, HEAP, lsl #32
    // 0x8f5878: tbnz            w0, #4, #0x8f58c0
    // 0x8f587c: r3 as num
    //     0x8f587c: mov             x0, x3
    //     0x8f5880: mov             x2, NULL
    //     0x8f5884: mov             x1, NULL
    //     0x8f5888: tbz             w0, #0, #0x8f58b0
    //     0x8f588c: ldur            x4, [x0, #-1]
    //     0x8f5890: ubfx            x4, x4, #0xc, #0x14
    //     0x8f5894: sub             x4, x4, #0x3c
    //     0x8f5898: cmp             x4, #2
    //     0x8f589c: b.ls            #0x8f58b0
    //     0x8f58a0: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    //     0x8f58a4: add             x3, PP, #0x27, lsl #12  ; [pp+0x27178] Null
    //     0x8f58a8: ldr             x3, [x3, #0x178]
    //     0x8f58ac: bl              #0xed4df4  ; IsType_num_Stub
    // 0x8f58b0: ldur            x1, [fp, #-8]
    // 0x8f58b4: ldur            x2, [fp, #-0x10]
    // 0x8f58b8: r0 = _formatExponential()
    //     0x8f58b8: bl              #0x8f730c  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatExponential
    // 0x8f58bc: b               #0x8f58cc
    // 0x8f58c0: ldur            x1, [fp, #-8]
    // 0x8f58c4: ldur            x2, [fp, #-0x10]
    // 0x8f58c8: r0 = _formatFixed()
    //     0x8f58c8: bl              #0x8f58e4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatFixed
    // 0x8f58cc: r0 = Null
    //     0x8f58cc: mov             x0, NULL
    // 0x8f58d0: LeaveFrame
    //     0x8f58d0: mov             SP, fp
    //     0x8f58d4: ldp             fp, lr, [SP], #0x10
    // 0x8f58d8: ret
    //     0x8f58d8: ret             
    // 0x8f58dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f58dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f58e0: b               #0x8f5870
  }
  _ _formatFixed(/* No info */) {
    // ** addr: 0x8f58e4, size: 0x6f0
    // 0x8f58e4: EnterFrame
    //     0x8f58e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f58e8: mov             fp, SP
    // 0x8f58ec: AllocStack(0x68)
    //     0x8f58ec: sub             SP, SP, #0x68
    // 0x8f58f0: SetupParameters(NumberFormat this /* r1 => r4, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */)
    //     0x8f58f0: mov             x4, x1
    //     0x8f58f4: mov             x3, x2
    //     0x8f58f8: stur            x1, [fp, #-0x20]
    //     0x8f58fc: stur            x2, [fp, #-0x28]
    // 0x8f5900: CheckStackOverflow
    //     0x8f5900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5904: cmp             SP, x16
    //     0x8f5908: b.ls            #0x8f5f60
    // 0x8f590c: LoadField: r5 = r4->field_43
    //     0x8f590c: ldur            x5, [x4, #0x43]
    // 0x8f5910: stur            x5, [fp, #-0x18]
    // 0x8f5914: r0 = BoxInt64Instr(r5)
    //     0x8f5914: sbfiz           x0, x5, #1, #0x1f
    //     0x8f5918: cmp             x5, x0, asr #1
    //     0x8f591c: b.eq            #0x8f5928
    //     0x8f5920: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f5924: stur            x5, [x0, #7]
    // 0x8f5928: stur            x0, [fp, #-0x10]
    // 0x8f592c: LoadField: r6 = r4->field_4b
    //     0x8f592c: ldur            x6, [x4, #0x4b]
    // 0x8f5930: mov             x1, x4
    // 0x8f5934: mov             x2, x3
    // 0x8f5938: stur            x6, [fp, #-8]
    // 0x8f593c: r0 = _isInfinite()
    //     0x8f593c: bl              #0x8f7cb0  ; [package:intl/src/intl/number_format.dart] NumberFormat::_isInfinite
    // 0x8f5940: tbnz            w0, #4, #0x8f5974
    // 0x8f5944: ldur            x16, [fp, #-0x28]
    // 0x8f5948: str             x16, [SP]
    // 0x8f594c: r4 = 0
    //     0x8f594c: movz            x4, #0
    // 0x8f5950: ldr             x0, [SP]
    // 0x8f5954: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5954: add             x16, PP, #0x27, lsl #12  ; [pp+0x27188] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5958: ldp             x5, lr, [x16, #0x188]
    // 0x8f595c: blr             lr
    // 0x8f5960: mov             x2, x0
    // 0x8f5964: r5 = 0
    //     0x8f5964: movz            x5, #0
    // 0x8f5968: r3 = 0
    //     0x8f5968: movz            x3, #0
    // 0x8f596c: r4 = 0
    //     0x8f596c: movz            x4, #0
    // 0x8f5970: b               #0x8f5d4c
    // 0x8f5974: ldur            x1, [fp, #-0x20]
    // 0x8f5978: ldur            x2, [fp, #-0x28]
    // 0x8f597c: r0 = _floor()
    //     0x8f597c: bl              #0x8f71c0  ; [package:intl/src/intl/number_format.dart] NumberFormat::_floor
    // 0x8f5980: stur            x0, [fp, #-0x30]
    // 0x8f5984: ldur            x16, [fp, #-0x28]
    // 0x8f5988: stp             x0, x16, [SP]
    // 0x8f598c: r4 = 0
    //     0x8f598c: movz            x4, #0
    // 0x8f5990: ldr             x0, [SP, #8]
    // 0x8f5994: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5994: add             x16, PP, #0x27, lsl #12  ; [pp+0x27198] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5998: ldp             x5, lr, [x16, #0x198]
    // 0x8f599c: blr             lr
    // 0x8f59a0: stur            x0, [fp, #-0x38]
    // 0x8f59a4: str             x0, [SP]
    // 0x8f59a8: r4 = 0
    //     0x8f59a8: movz            x4, #0
    // 0x8f59ac: ldr             x0, [SP]
    // 0x8f59b0: r5 = UnlinkedCall_0x5f3c08
    //     0x8f59b0: add             x16, PP, #0x27, lsl #12  ; [pp+0x271a8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f59b4: ldp             x5, lr, [x16, #0x1a8]
    // 0x8f59b8: blr             lr
    // 0x8f59bc: cbz             w0, #0x8f59cc
    // 0x8f59c0: ldur            x4, [fp, #-0x28]
    // 0x8f59c4: r3 = 0
    //     0x8f59c4: movz            x3, #0
    // 0x8f59c8: b               #0x8f59d4
    // 0x8f59cc: ldur            x4, [fp, #-0x30]
    // 0x8f59d0: ldur            x3, [fp, #-0x38]
    // 0x8f59d4: ldur            x2, [fp, #-0x18]
    // 0x8f59d8: stur            x4, [fp, #-0x28]
    // 0x8f59dc: stur            x3, [fp, #-0x30]
    // 0x8f59e0: tbnz            x2, #0x3f, #0x8f5a40
    // 0x8f59e4: mov             x0, x2
    // 0x8f59e8: r1 = 10
    //     0x8f59e8: movz            x1, #0xa
    // 0x8f59ec: r5 = 1
    //     0x8f59ec: movz            x5, #0x1
    // 0x8f59f0: CheckStackOverflow
    //     0x8f59f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f59f4: cmp             SP, x16
    //     0x8f59f8: b.ls            #0x8f5f68
    // 0x8f59fc: cbz             x0, #0x8f5a24
    // 0x8f5a00: branchIfSmi(r0, 0x8f5a0c)
    //     0x8f5a00: tbz             w0, #0, #0x8f5a0c
    // 0x8f5a04: mul             x6, x5, x1
    // 0x8f5a08: mov             x5, x6
    // 0x8f5a0c: asr             x6, x0, #1
    // 0x8f5a10: cbz             x6, #0x8f5a1c
    // 0x8f5a14: mul             x7, x1, x1
    // 0x8f5a18: mov             x1, x7
    // 0x8f5a1c: mov             x0, x6
    // 0x8f5a20: b               #0x8f59f0
    // 0x8f5a24: r0 = BoxInt64Instr(r5)
    //     0x8f5a24: sbfiz           x0, x5, #1, #0x1f
    //     0x8f5a28: cmp             x5, x0, asr #1
    //     0x8f5a2c: b.eq            #0x8f5a38
    //     0x8f5a30: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f5a34: stur            x5, [x0, #7]
    // 0x8f5a38: mov             x4, x0
    // 0x8f5a3c: b               #0x8f5b90
    // 0x8f5a40: ldur            x0, [fp, #-0x10]
    // 0x8f5a44: r16 = 20
    //     0x8f5a44: movz            x16, #0x14
    // 0x8f5a48: stp             x16, NULL, [SP]
    // 0x8f5a4c: r0 = _Double.fromInteger()
    //     0x8f5a4c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f5a50: mov             x1, x0
    // 0x8f5a54: ldur            x0, [fp, #-0x10]
    // 0x8f5a58: stur            x1, [fp, #-0x38]
    // 0x8f5a5c: r2 = 60
    //     0x8f5a5c: movz            x2, #0x3c
    // 0x8f5a60: branchIfSmi(r0, 0x8f5a6c)
    //     0x8f5a60: tbz             w0, #0, #0x8f5a6c
    // 0x8f5a64: r2 = LoadClassIdInstr(r0)
    //     0x8f5a64: ldur            x2, [x0, #-1]
    //     0x8f5a68: ubfx            x2, x2, #0xc, #0x14
    // 0x8f5a6c: str             x0, [SP]
    // 0x8f5a70: mov             x0, x2
    // 0x8f5a74: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f5a74: sub             lr, x0, #0xffa
    //     0x8f5a78: ldr             lr, [x21, lr, lsl #3]
    //     0x8f5a7c: blr             lr
    // 0x8f5a80: mov             x1, x0
    // 0x8f5a84: ldur            x0, [fp, #-0x38]
    // 0x8f5a88: LoadField: d0 = r0->field_7
    //     0x8f5a88: ldur            d0, [x0, #7]
    // 0x8f5a8c: LoadField: d1 = r1->field_7
    //     0x8f5a8c: ldur            d1, [x1, #7]
    // 0x8f5a90: d30 = 0.000000
    //     0x8f5a90: fmov            d30, d0
    // 0x8f5a94: d0 = 1.000000
    //     0x8f5a94: fmov            d0, #1.00000000
    // 0x8f5a98: fcmp            d1, #0.0
    // 0x8f5a9c: b.vs            #0x8f5ae0
    // 0x8f5aa0: b.eq            #0x8f5b64
    // 0x8f5aa4: fcmp            d1, d0
    // 0x8f5aa8: b.eq            #0x8f5ad0
    // 0x8f5aac: d31 = 2.000000
    //     0x8f5aac: fmov            d31, #2.00000000
    // 0x8f5ab0: fcmp            d1, d31
    // 0x8f5ab4: b.eq            #0x8f5ad8
    // 0x8f5ab8: d31 = 3.000000
    //     0x8f5ab8: fmov            d31, #3.00000000
    // 0x8f5abc: fcmp            d1, d31
    // 0x8f5ac0: b.ne            #0x8f5ae0
    // 0x8f5ac4: fmul            d0, d30, d30
    // 0x8f5ac8: fmul            d0, d0, d30
    // 0x8f5acc: b               #0x8f5b64
    // 0x8f5ad0: d0 = 0.000000
    //     0x8f5ad0: fmov            d0, d30
    // 0x8f5ad4: b               #0x8f5b64
    // 0x8f5ad8: fmul            d0, d30, d30
    // 0x8f5adc: b               #0x8f5b64
    // 0x8f5ae0: fcmp            d30, d0
    // 0x8f5ae4: b.vs            #0x8f5af4
    // 0x8f5ae8: b.eq            #0x8f5b64
    // 0x8f5aec: fcmp            d30, d1
    // 0x8f5af0: b.vc            #0x8f5afc
    // 0x8f5af4: d0 = nan
    //     0x8f5af4: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0x8f5af8: b               #0x8f5b64
    // 0x8f5afc: d0 = -inf
    //     0x8f5afc: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x8f5b00: fcmp            d30, d0
    // 0x8f5b04: b.eq            #0x8f5b2c
    // 0x8f5b08: d0 = 0.500000
    //     0x8f5b08: fmov            d0, #0.50000000
    // 0x8f5b0c: fcmp            d1, d0
    // 0x8f5b10: b.ne            #0x8f5b2c
    // 0x8f5b14: fcmp            d30, #0.0
    // 0x8f5b18: b.eq            #0x8f5b24
    // 0x8f5b1c: fsqrt           d0, d30
    // 0x8f5b20: b               #0x8f5b64
    // 0x8f5b24: d0 = 0.000000
    //     0x8f5b24: eor             v0.16b, v0.16b, v0.16b
    // 0x8f5b28: b               #0x8f5b64
    // 0x8f5b2c: d0 = 0.000000
    //     0x8f5b2c: fmov            d0, d30
    // 0x8f5b30: stp             fp, lr, [SP, #-0x10]!
    // 0x8f5b34: mov             fp, SP
    // 0x8f5b38: CallRuntime_LibcPow(double, double) -> double
    //     0x8f5b38: and             SP, SP, #0xfffffffffffffff0
    //     0x8f5b3c: mov             sp, SP
    //     0x8f5b40: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0x8f5b44: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f5b48: blr             x16
    //     0x8f5b4c: movz            x16, #0x8
    //     0x8f5b50: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f5b54: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f5b58: sub             sp, x16, #1, lsl #12
    //     0x8f5b5c: mov             SP, fp
    //     0x8f5b60: ldp             fp, lr, [SP], #0x10
    // 0x8f5b64: r0 = inline_Allocate_Double()
    //     0x8f5b64: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8f5b68: add             x0, x0, #0x10
    //     0x8f5b6c: cmp             x1, x0
    //     0x8f5b70: b.ls            #0x8f5f70
    //     0x8f5b74: str             x0, [THR, #0x50]  ; THR::top
    //     0x8f5b78: sub             x0, x0, #0xf
    //     0x8f5b7c: movz            x1, #0xe15c
    //     0x8f5b80: movk            x1, #0x3, lsl #16
    //     0x8f5b84: stur            x1, [x0, #-1]
    // 0x8f5b88: StoreField: r0->field_7 = d0
    //     0x8f5b88: stur            d0, [x0, #7]
    // 0x8f5b8c: mov             x4, x0
    // 0x8f5b90: ldur            x3, [fp, #-0x20]
    // 0x8f5b94: mov             x0, x4
    // 0x8f5b98: stur            x4, [fp, #-0x10]
    // 0x8f5b9c: r2 = Null
    //     0x8f5b9c: mov             x2, NULL
    // 0x8f5ba0: r1 = Null
    //     0x8f5ba0: mov             x1, NULL
    // 0x8f5ba4: branchIfSmi(r0, 0x8f5bcc)
    //     0x8f5ba4: tbz             w0, #0, #0x8f5bcc
    // 0x8f5ba8: r4 = LoadClassIdInstr(r0)
    //     0x8f5ba8: ldur            x4, [x0, #-1]
    //     0x8f5bac: ubfx            x4, x4, #0xc, #0x14
    // 0x8f5bb0: sub             x4, x4, #0x3c
    // 0x8f5bb4: cmp             x4, #1
    // 0x8f5bb8: b.ls            #0x8f5bcc
    // 0x8f5bbc: r8 = int
    //     0x8f5bbc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8f5bc0: r3 = Null
    //     0x8f5bc0: add             x3, PP, #0x27, lsl #12  ; [pp+0x271b8] Null
    //     0x8f5bc4: ldr             x3, [x3, #0x1b8]
    // 0x8f5bc8: r0 = int()
    //     0x8f5bc8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8f5bcc: ldur            x2, [fp, #-0x20]
    // 0x8f5bd0: LoadField: r0 = r2->field_5f
    //     0x8f5bd0: ldur            x0, [x2, #0x5f]
    // 0x8f5bd4: ldur            x1, [fp, #-0x10]
    // 0x8f5bd8: r3 = LoadInt32Instr(r1)
    //     0x8f5bd8: sbfx            x3, x1, #1, #0x1f
    //     0x8f5bdc: tbz             w1, #0, #0x8f5be4
    //     0x8f5be0: ldur            x3, [x1, #7]
    // 0x8f5be4: stur            x3, [fp, #-0x48]
    // 0x8f5be8: mul             x4, x3, x0
    // 0x8f5bec: stur            x4, [fp, #-0x40]
    // 0x8f5bf0: r0 = BoxInt64Instr(r4)
    //     0x8f5bf0: sbfiz           x0, x4, #1, #0x1f
    //     0x8f5bf4: cmp             x4, x0, asr #1
    //     0x8f5bf8: b.eq            #0x8f5c04
    //     0x8f5bfc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f5c00: stur            x4, [x0, #7]
    // 0x8f5c04: stur            x0, [fp, #-0x10]
    // 0x8f5c08: ldur            x16, [fp, #-0x30]
    // 0x8f5c0c: stp             x0, x16, [SP]
    // 0x8f5c10: r4 = 0
    //     0x8f5c10: movz            x4, #0
    // 0x8f5c14: ldr             x0, [SP, #8]
    // 0x8f5c18: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5c18: add             x16, PP, #0x27, lsl #12  ; [pp+0x271c8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5c1c: ldp             x5, lr, [x16, #0x1c8]
    // 0x8f5c20: blr             lr
    // 0x8f5c24: ldur            x1, [fp, #-0x20]
    // 0x8f5c28: mov             x2, x0
    // 0x8f5c2c: r0 = _round()
    //     0x8f5c2c: bl              #0x8f6f98  ; [package:intl/src/intl/number_format.dart] NumberFormat::_round
    // 0x8f5c30: str             x0, [SP]
    // 0x8f5c34: r4 = 0
    //     0x8f5c34: movz            x4, #0
    // 0x8f5c38: ldr             x0, [SP]
    // 0x8f5c3c: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5c3c: add             x16, PP, #0x27, lsl #12  ; [pp+0x271d8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5c40: ldp             x5, lr, [x16, #0x1d8]
    // 0x8f5c44: blr             lr
    // 0x8f5c48: r1 = LoadInt32Instr(r0)
    //     0x8f5c48: sbfx            x1, x0, #1, #0x1f
    //     0x8f5c4c: tbz             w0, #0, #0x8f5c54
    //     0x8f5c50: ldur            x1, [x0, #7]
    // 0x8f5c54: ldur            x0, [fp, #-0x40]
    // 0x8f5c58: stur            x1, [fp, #-0x50]
    // 0x8f5c5c: cmp             x1, x0
    // 0x8f5c60: b.lt            #0x8f5ca0
    // 0x8f5c64: ldur            x16, [fp, #-0x28]
    // 0x8f5c68: r30 = 2
    //     0x8f5c68: movz            lr, #0x2
    // 0x8f5c6c: stp             lr, x16, [SP]
    // 0x8f5c70: r4 = 0
    //     0x8f5c70: movz            x4, #0
    // 0x8f5c74: ldr             x0, [SP, #8]
    // 0x8f5c78: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5c78: add             x16, PP, #0x27, lsl #12  ; [pp+0x271e8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5c7c: ldp             x5, lr, [x16, #0x1e8]
    // 0x8f5c80: blr             lr
    // 0x8f5c84: mov             x2, x0
    // 0x8f5c88: ldur            x1, [fp, #-0x40]
    // 0x8f5c8c: ldur            x0, [fp, #-0x50]
    // 0x8f5c90: sub             x3, x0, x1
    // 0x8f5c94: mov             x1, x2
    // 0x8f5c98: mov             x2, x3
    // 0x8f5c9c: b               #0x8f5d20
    // 0x8f5ca0: mov             x0, x1
    // 0x8f5ca4: mov             x1, x0
    // 0x8f5ca8: r0 = numberOfIntegerDigits()
    //     0x8f5ca8: bl              #0x8f6c24  ; [package:intl/src/intl/number_format.dart] NumberFormat::numberOfIntegerDigits
    // 0x8f5cac: stur            x0, [fp, #-0x40]
    // 0x8f5cb0: ldur            x16, [fp, #-0x30]
    // 0x8f5cb4: ldur            lr, [fp, #-0x10]
    // 0x8f5cb8: stp             lr, x16, [SP]
    // 0x8f5cbc: r4 = 0
    //     0x8f5cbc: movz            x4, #0
    // 0x8f5cc0: ldr             x0, [SP, #8]
    // 0x8f5cc4: r5 = UnlinkedCall_0x5f3c08
    //     0x8f5cc4: add             x16, PP, #0x27, lsl #12  ; [pp+0x271f8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5cc8: ldp             x5, lr, [x16, #0x1f8]
    // 0x8f5ccc: blr             lr
    // 0x8f5cd0: ldur            x1, [fp, #-0x20]
    // 0x8f5cd4: mov             x2, x0
    // 0x8f5cd8: r0 = _floor()
    //     0x8f5cd8: bl              #0x8f71c0  ; [package:intl/src/intl/number_format.dart] NumberFormat::_floor
    // 0x8f5cdc: str             x0, [SP]
    // 0x8f5ce0: r4 = 0
    //     0x8f5ce0: movz            x4, #0
    // 0x8f5ce4: ldr             x0, [SP]
    // 0x8f5ce8: r16 = UnlinkedCall_0x5f3c08
    //     0x8f5ce8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27208] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f5cec: add             x16, x16, #0x208
    // 0x8f5cf0: ldp             x5, lr, [x16]
    // 0x8f5cf4: blr             lr
    // 0x8f5cf8: r1 = LoadInt32Instr(r0)
    //     0x8f5cf8: sbfx            x1, x0, #1, #0x1f
    //     0x8f5cfc: tbz             w0, #0, #0x8f5d04
    //     0x8f5d00: ldur            x1, [x0, #7]
    // 0x8f5d04: r0 = numberOfIntegerDigits()
    //     0x8f5d04: bl              #0x8f6c24  ; [package:intl/src/intl/number_format.dart] NumberFormat::numberOfIntegerDigits
    // 0x8f5d08: mov             x1, x0
    // 0x8f5d0c: ldur            x0, [fp, #-0x40]
    // 0x8f5d10: cmp             x0, x1
    // 0x8f5d14: b.gt            #0x8f5d18
    // 0x8f5d18: ldur            x2, [fp, #-0x50]
    // 0x8f5d1c: ldur            x1, [fp, #-0x28]
    // 0x8f5d20: ldur            x0, [fp, #-0x48]
    // 0x8f5d24: cbz             x0, #0x8f5f80
    // 0x8f5d28: sdiv            x3, x2, x0
    // 0x8f5d2c: cbz             x0, #0x8f5f9c
    // 0x8f5d30: sdiv            x5, x2, x0
    // 0x8f5d34: msub            x4, x5, x0, x2
    // 0x8f5d38: cmp             x4, xzr
    // 0x8f5d3c: b.lt            #0x8f5fb8
    // 0x8f5d40: mov             x5, x4
    // 0x8f5d44: mov             x4, x0
    // 0x8f5d48: mov             x2, x1
    // 0x8f5d4c: ldur            x0, [fp, #-0x18]
    // 0x8f5d50: ldur            x1, [fp, #-0x20]
    // 0x8f5d54: stur            x5, [fp, #-0x40]
    // 0x8f5d58: stur            x4, [fp, #-0x48]
    // 0x8f5d5c: r0 = _integerDigits()
    //     0x8f5d5c: bl              #0x8f6338  ; [package:intl/src/intl/number_format.dart] NumberFormat::_integerDigits
    // 0x8f5d60: stur            x0, [fp, #-0x30]
    // 0x8f5d64: LoadField: r3 = r0->field_7
    //     0x8f5d64: ldur            w3, [x0, #7]
    // 0x8f5d68: ldur            x1, [fp, #-0x18]
    // 0x8f5d6c: stur            x3, [fp, #-0x28]
    // 0x8f5d70: cmp             x1, #0
    // 0x8f5d74: b.le            #0x8f5dac
    // 0x8f5d78: ldur            x4, [fp, #-8]
    // 0x8f5d7c: cmp             x4, #0
    // 0x8f5d80: b.le            #0x8f5d90
    // 0x8f5d84: ldur            x5, [fp, #-0x40]
    // 0x8f5d88: r6 = true
    //     0x8f5d88: add             x6, NULL, #0x20  ; true
    // 0x8f5d8c: b               #0x8f5db8
    // 0x8f5d90: ldur            x5, [fp, #-0x40]
    // 0x8f5d94: cmp             x5, #0
    // 0x8f5d98: r16 = true
    //     0x8f5d98: add             x16, NULL, #0x20  ; true
    // 0x8f5d9c: r17 = false
    //     0x8f5d9c: add             x17, NULL, #0x30  ; false
    // 0x8f5da0: csel            x1, x16, x17, gt
    // 0x8f5da4: mov             x6, x1
    // 0x8f5da8: b               #0x8f5db8
    // 0x8f5dac: ldur            x5, [fp, #-0x40]
    // 0x8f5db0: ldur            x4, [fp, #-8]
    // 0x8f5db4: r6 = false
    //     0x8f5db4: add             x6, NULL, #0x30  ; false
    // 0x8f5db8: ldur            x1, [fp, #-0x20]
    // 0x8f5dbc: mov             x2, x0
    // 0x8f5dc0: stur            x6, [fp, #-0x10]
    // 0x8f5dc4: r0 = _hasIntegerDigits()
    //     0x8f5dc4: bl              #0x8f630c  ; [package:intl/src/intl/number_format.dart] NumberFormat::_hasIntegerDigits
    // 0x8f5dc8: tbnz            w0, #4, #0x8f5ecc
    // 0x8f5dcc: ldur            x3, [fp, #-0x20]
    // 0x8f5dd0: ldur            x0, [fp, #-0x30]
    // 0x8f5dd4: ldur            x1, [fp, #-0x28]
    // 0x8f5dd8: LoadField: r2 = r3->field_3b
    //     0x8f5dd8: ldur            x2, [x3, #0x3b]
    // 0x8f5ddc: r4 = LoadInt32Instr(r1)
    //     0x8f5ddc: sbfx            x4, x1, #1, #0x1f
    // 0x8f5de0: sub             x1, x2, x4
    // 0x8f5de4: mov             x2, x1
    // 0x8f5de8: r1 = "0"
    //     0x8f5de8: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8f5dec: r0 = *()
    //     0x8f5dec: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0x8f5df0: r1 = Null
    //     0x8f5df0: mov             x1, NULL
    // 0x8f5df4: r2 = 4
    //     0x8f5df4: movz            x2, #0x4
    // 0x8f5df8: stur            x0, [fp, #-0x28]
    // 0x8f5dfc: r0 = AllocateArray()
    //     0x8f5dfc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8f5e00: mov             x1, x0
    // 0x8f5e04: ldur            x0, [fp, #-0x28]
    // 0x8f5e08: StoreField: r1->field_f = r0
    //     0x8f5e08: stur            w0, [x1, #0xf]
    // 0x8f5e0c: ldur            x0, [fp, #-0x30]
    // 0x8f5e10: StoreField: r1->field_13 = r0
    //     0x8f5e10: stur            w0, [x1, #0x13]
    // 0x8f5e14: str             x1, [SP]
    // 0x8f5e18: r0 = _interpolate()
    //     0x8f5e18: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8f5e1c: stur            x0, [fp, #-0x38]
    // 0x8f5e20: LoadField: r1 = r0->field_7
    //     0x8f5e20: ldur            w1, [x0, #7]
    // 0x8f5e24: r3 = LoadInt32Instr(r1)
    //     0x8f5e24: sbfx            x3, x1, #1, #0x1f
    // 0x8f5e28: stur            x3, [fp, #-0x58]
    // 0x8f5e2c: r4 = LoadClassIdInstr(r0)
    //     0x8f5e2c: ldur            x4, [x0, #-1]
    //     0x8f5e30: ubfx            x4, x4, #0xc, #0x14
    // 0x8f5e34: lsl             x4, x4, #1
    // 0x8f5e38: ldur            x5, [fp, #-0x20]
    // 0x8f5e3c: stur            x4, [fp, #-0x30]
    // 0x8f5e40: LoadField: r6 = r5->field_7b
    //     0x8f5e40: ldur            w6, [x5, #0x7b]
    // 0x8f5e44: DecompressPointer r6
    //     0x8f5e44: add             x6, x6, HEAP, lsl #32
    // 0x8f5e48: stur            x6, [fp, #-0x28]
    // 0x8f5e4c: LoadField: r7 = r5->field_7f
    //     0x8f5e4c: ldur            x7, [x5, #0x7f]
    // 0x8f5e50: stur            x7, [fp, #-0x50]
    // 0x8f5e54: r8 = 0
    //     0x8f5e54: movz            x8, #0
    // 0x8f5e58: stur            x8, [fp, #-0x18]
    // 0x8f5e5c: CheckStackOverflow
    //     0x8f5e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5e60: cmp             SP, x16
    //     0x8f5e64: b.ls            #0x8f5fcc
    // 0x8f5e68: cmp             x8, x3
    // 0x8f5e6c: b.ge            #0x8f5edc
    // 0x8f5e70: cmp             w4, #0xbc
    // 0x8f5e74: b.ne            #0x8f5e84
    // 0x8f5e78: ArrayLoad: r1 = r0[r8]  ; TypedUnsigned_1
    //     0x8f5e78: add             x16, x0, x8
    //     0x8f5e7c: ldrb            w1, [x16, #0xf]
    // 0x8f5e80: b               #0x8f5e8c
    // 0x8f5e84: add             x16, x0, x8, lsl #1
    // 0x8f5e88: ldurh           w1, [x16, #0xf]
    // 0x8f5e8c: add             x2, x1, x7
    // 0x8f5e90: mov             x1, x6
    // 0x8f5e94: r0 = writeCharCode()
    //     0x8f5e94: bl              #0x603f20  ; [dart:core] StringBuffer::writeCharCode
    // 0x8f5e98: ldur            x1, [fp, #-0x20]
    // 0x8f5e9c: ldur            x2, [fp, #-0x58]
    // 0x8f5ea0: ldur            x3, [fp, #-0x18]
    // 0x8f5ea4: r0 = _group()
    //     0x8f5ea4: bl              #0x8f6224  ; [package:intl/src/intl/number_format.dart] NumberFormat::_group
    // 0x8f5ea8: ldur            x0, [fp, #-0x18]
    // 0x8f5eac: add             x8, x0, #1
    // 0x8f5eb0: ldur            x5, [fp, #-0x20]
    // 0x8f5eb4: ldur            x0, [fp, #-0x38]
    // 0x8f5eb8: ldur            x4, [fp, #-0x30]
    // 0x8f5ebc: ldur            x6, [fp, #-0x28]
    // 0x8f5ec0: ldur            x7, [fp, #-0x50]
    // 0x8f5ec4: ldur            x3, [fp, #-0x58]
    // 0x8f5ec8: b               #0x8f5e58
    // 0x8f5ecc: ldur            x2, [fp, #-0x10]
    // 0x8f5ed0: tbz             w2, #4, #0x8f5edc
    // 0x8f5ed4: ldur            x1, [fp, #-0x20]
    // 0x8f5ed8: r0 = _addZero()
    //     0x8f5ed8: bl              #0x8f61d4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_addZero
    // 0x8f5edc: ldur            x0, [fp, #-0x10]
    // 0x8f5ee0: ldur            x1, [fp, #-0x20]
    // 0x8f5ee4: mov             x2, x0
    // 0x8f5ee8: r0 = _decimalSeparator()
    //     0x8f5ee8: bl              #0x8f6184  ; [package:intl/src/intl/number_format.dart] NumberFormat::_decimalSeparator
    // 0x8f5eec: ldur            x0, [fp, #-0x10]
    // 0x8f5ef0: tbnz            w0, #4, #0x8f5f50
    // 0x8f5ef4: ldur            x0, [fp, #-0x40]
    // 0x8f5ef8: ldur            x1, [fp, #-0x48]
    // 0x8f5efc: add             x2, x0, x1
    // 0x8f5f00: r0 = BoxInt64Instr(r2)
    //     0x8f5f00: sbfiz           x0, x2, #1, #0x1f
    //     0x8f5f04: cmp             x2, x0, asr #1
    //     0x8f5f08: b.eq            #0x8f5f14
    //     0x8f5f0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f5f10: stur            x2, [x0, #7]
    // 0x8f5f14: r1 = 60
    //     0x8f5f14: movz            x1, #0x3c
    // 0x8f5f18: branchIfSmi(r0, 0x8f5f24)
    //     0x8f5f18: tbz             w0, #0, #0x8f5f24
    // 0x8f5f1c: r1 = LoadClassIdInstr(r0)
    //     0x8f5f1c: ldur            x1, [x0, #-1]
    //     0x8f5f20: ubfx            x1, x1, #0xc, #0x14
    // 0x8f5f24: str             x0, [SP]
    // 0x8f5f28: mov             x0, x1
    // 0x8f5f2c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8f5f2c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8f5f30: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8f5f30: movz            x17, #0x2b03
    //     0x8f5f34: add             lr, x0, x17
    //     0x8f5f38: ldr             lr, [x21, lr, lsl #3]
    //     0x8f5f3c: blr             lr
    // 0x8f5f40: ldur            x1, [fp, #-0x20]
    // 0x8f5f44: mov             x2, x0
    // 0x8f5f48: ldur            x3, [fp, #-8]
    // 0x8f5f4c: r0 = _formatFractionPart()
    //     0x8f5f4c: bl              #0x8f5fd4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatFractionPart
    // 0x8f5f50: r0 = Null
    //     0x8f5f50: mov             x0, NULL
    // 0x8f5f54: LeaveFrame
    //     0x8f5f54: mov             SP, fp
    //     0x8f5f58: ldp             fp, lr, [SP], #0x10
    // 0x8f5f5c: ret
    //     0x8f5f5c: ret             
    // 0x8f5f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f5f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5f64: b               #0x8f590c
    // 0x8f5f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f5f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5f6c: b               #0x8f59fc
    // 0x8f5f70: SaveReg d0
    //     0x8f5f70: str             q0, [SP, #-0x10]!
    // 0x8f5f74: r0 = AllocateDouble()
    //     0x8f5f74: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f5f78: RestoreReg d0
    //     0x8f5f78: ldr             q0, [SP], #0x10
    // 0x8f5f7c: b               #0x8f5b88
    // 0x8f5f80: stp             x1, x2, [SP, #-0x10]!
    // 0x8f5f84: SaveReg r0
    //     0x8f5f84: str             x0, [SP, #-8]!
    // 0x8f5f88: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x8f5f8c: r4 = 0
    //     0x8f5f8c: movz            x4, #0
    // 0x8f5f90: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8f5f94: blr             lr
    // 0x8f5f98: brk             #0
    // 0x8f5f9c: stp             x2, x3, [SP, #-0x10]!
    // 0x8f5fa0: stp             x0, x1, [SP, #-0x10]!
    // 0x8f5fa4: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x8f5fa8: r4 = 0
    //     0x8f5fa8: movz            x4, #0
    // 0x8f5fac: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8f5fb0: blr             lr
    // 0x8f5fb4: brk             #0
    // 0x8f5fb8: cmp             x0, xzr
    // 0x8f5fbc: sub             x5, x4, x0
    // 0x8f5fc0: add             x4, x4, x0
    // 0x8f5fc4: csel            x4, x5, x4, lt
    // 0x8f5fc8: b               #0x8f5d40
    // 0x8f5fcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f5fcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f5fd0: b               #0x8f5e68
  }
  _ _formatFractionPart(/* No info */) {
    // ** addr: 0x8f5fd4, size: 0x1b0
    // 0x8f5fd4: EnterFrame
    //     0x8f5fd4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f5fd8: mov             fp, SP
    // 0x8f5fdc: AllocStack(0x48)
    //     0x8f5fdc: sub             SP, SP, #0x48
    // 0x8f5fe0: SetupParameters(NumberFormat this /* r1 => r4, fp-0x38 */, dynamic _ /* r2 => r2, fp-0x40 */)
    //     0x8f5fe0: mov             x4, x1
    //     0x8f5fe4: stur            x1, [fp, #-0x38]
    //     0x8f5fe8: stur            x2, [fp, #-0x40]
    // 0x8f5fec: CheckStackOverflow
    //     0x8f5fec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f5ff0: cmp             SP, x16
    //     0x8f5ff4: b.ls            #0x8f6164
    // 0x8f5ff8: LoadField: r0 = r2->field_7
    //     0x8f5ff8: ldur            w0, [x2, #7]
    // 0x8f5ffc: r5 = LoadInt32Instr(r0)
    //     0x8f5ffc: sbfx            x5, x0, #1, #0x1f
    // 0x8f6000: stur            x5, [fp, #-0x30]
    // 0x8f6004: r6 = LoadClassIdInstr(r2)
    //     0x8f6004: ldur            x6, [x2, #-1]
    //     0x8f6008: ubfx            x6, x6, #0xc, #0x14
    // 0x8f600c: lsl             x6, x6, #1
    // 0x8f6010: stur            x6, [fp, #-0x28]
    // 0x8f6014: add             x7, x3, #1
    // 0x8f6018: stur            x7, [fp, #-0x20]
    // 0x8f601c: mov             x3, x5
    // 0x8f6020: stur            x3, [fp, #-0x18]
    // 0x8f6024: CheckStackOverflow
    //     0x8f6024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6028: cmp             SP, x16
    //     0x8f602c: b.ls            #0x8f616c
    // 0x8f6030: sub             x8, x3, #1
    // 0x8f6034: mov             x0, x5
    // 0x8f6038: mov             x1, x8
    // 0x8f603c: stur            x8, [fp, #-0x10]
    // 0x8f6040: cmp             x1, x0
    // 0x8f6044: b.hs            #0x8f6174
    // 0x8f6048: cmp             w6, #0xbc
    // 0x8f604c: b.ne            #0x8f605c
    // 0x8f6050: ArrayLoad: r0 = r2[r8]  ; TypedUnsigned_1
    //     0x8f6050: add             x16, x2, x8
    //     0x8f6054: ldrb            w0, [x16, #0xf]
    // 0x8f6058: b               #0x8f6064
    // 0x8f605c: add             x16, x2, x8, lsl #1
    // 0x8f6060: ldurh           w0, [x16, #0xf]
    // 0x8f6064: stur            x0, [fp, #-8]
    // 0x8f6068: r0 = InitLateStaticField(0x14a8) // [package:intl/src/intl/constants.dart] ::asciiZeroCodeUnit
    //     0x8f6068: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f606c: ldr             x0, [x0, #0x2950]
    //     0x8f6070: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f6074: cmp             w0, w16
    //     0x8f6078: b.ne            #0x8f6088
    //     0x8f607c: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b830] Field <::.asciiZeroCodeUnit>: static late final (offset: 0x14a8)
    //     0x8f6080: ldr             x2, [x2, #0x830]
    //     0x8f6084: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6088: r1 = LoadInt32Instr(r0)
    //     0x8f6088: sbfx            x1, x0, #1, #0x1f
    // 0x8f608c: ldur            x0, [fp, #-8]
    // 0x8f6090: cmp             x0, x1
    // 0x8f6094: b.ne            #0x8f60c4
    // 0x8f6098: ldur            x3, [fp, #-0x18]
    // 0x8f609c: ldur            x0, [fp, #-0x20]
    // 0x8f60a0: cmp             x3, x0
    // 0x8f60a4: b.le            #0x8f60c8
    // 0x8f60a8: ldur            x3, [fp, #-0x10]
    // 0x8f60ac: ldur            x4, [fp, #-0x38]
    // 0x8f60b0: ldur            x2, [fp, #-0x40]
    // 0x8f60b4: mov             x7, x0
    // 0x8f60b8: ldur            x6, [fp, #-0x28]
    // 0x8f60bc: ldur            x5, [fp, #-0x30]
    // 0x8f60c0: b               #0x8f6020
    // 0x8f60c4: ldur            x3, [fp, #-0x18]
    // 0x8f60c8: ldur            x0, [fp, #-0x38]
    // 0x8f60cc: LoadField: r4 = r0->field_7b
    //     0x8f60cc: ldur            w4, [x0, #0x7b]
    // 0x8f60d0: DecompressPointer r4
    //     0x8f60d0: add             x4, x4, HEAP, lsl #32
    // 0x8f60d4: stur            x4, [fp, #-0x48]
    // 0x8f60d8: LoadField: r5 = r0->field_7f
    //     0x8f60d8: ldur            x5, [x0, #0x7f]
    // 0x8f60dc: stur            x5, [fp, #-0x10]
    // 0x8f60e0: r8 = 1
    //     0x8f60e0: movz            x8, #0x1
    // 0x8f60e4: ldur            x6, [fp, #-0x40]
    // 0x8f60e8: ldur            x7, [fp, #-0x28]
    // 0x8f60ec: stur            x8, [fp, #-8]
    // 0x8f60f0: CheckStackOverflow
    //     0x8f60f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f60f4: cmp             SP, x16
    //     0x8f60f8: b.ls            #0x8f6178
    // 0x8f60fc: cmp             x8, x3
    // 0x8f6100: b.ge            #0x8f6154
    // 0x8f6104: ldur            x0, [fp, #-0x30]
    // 0x8f6108: mov             x1, x8
    // 0x8f610c: cmp             x1, x0
    // 0x8f6110: b.hs            #0x8f6180
    // 0x8f6114: cmp             w7, #0xbc
    // 0x8f6118: b.ne            #0x8f6128
    // 0x8f611c: ArrayLoad: r0 = r6[r8]  ; TypedUnsigned_1
    //     0x8f611c: add             x16, x6, x8
    //     0x8f6120: ldrb            w0, [x16, #0xf]
    // 0x8f6124: b               #0x8f6130
    // 0x8f6128: add             x16, x6, x8, lsl #1
    // 0x8f612c: ldurh           w0, [x16, #0xf]
    // 0x8f6130: add             x2, x0, x5
    // 0x8f6134: mov             x1, x4
    // 0x8f6138: r0 = writeCharCode()
    //     0x8f6138: bl              #0x603f20  ; [dart:core] StringBuffer::writeCharCode
    // 0x8f613c: ldur            x1, [fp, #-8]
    // 0x8f6140: add             x8, x1, #1
    // 0x8f6144: ldur            x3, [fp, #-0x18]
    // 0x8f6148: ldur            x4, [fp, #-0x48]
    // 0x8f614c: ldur            x5, [fp, #-0x10]
    // 0x8f6150: b               #0x8f60e4
    // 0x8f6154: r0 = Null
    //     0x8f6154: mov             x0, NULL
    // 0x8f6158: LeaveFrame
    //     0x8f6158: mov             SP, fp
    //     0x8f615c: ldp             fp, lr, [SP], #0x10
    // 0x8f6160: ret
    //     0x8f6160: ret             
    // 0x8f6164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6168: b               #0x8f5ff8
    // 0x8f616c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f616c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6170: b               #0x8f6030
    // 0x8f6174: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8f6174: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8f6178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6178: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f617c: b               #0x8f60fc
    // 0x8f6180: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8f6180: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _decimalSeparator(/* No info */) {
    // ** addr: 0x8f6184, size: 0x50
    // 0x8f6184: EnterFrame
    //     0x8f6184: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6188: mov             fp, SP
    // 0x8f618c: CheckStackOverflow
    //     0x8f618c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6190: cmp             SP, x16
    //     0x8f6194: b.ls            #0x8f61cc
    // 0x8f6198: LoadField: r0 = r1->field_27
    //     0x8f6198: ldur            w0, [x1, #0x27]
    // 0x8f619c: DecompressPointer r0
    //     0x8f619c: add             x0, x0, HEAP, lsl #32
    // 0x8f61a0: tbz             w0, #4, #0x8f61a8
    // 0x8f61a4: tbnz            w2, #4, #0x8f61bc
    // 0x8f61a8: LoadField: r0 = r1->field_77
    //     0x8f61a8: ldur            w0, [x1, #0x77]
    // 0x8f61ac: DecompressPointer r0
    //     0x8f61ac: add             x0, x0, HEAP, lsl #32
    // 0x8f61b0: LoadField: r2 = r0->field_b
    //     0x8f61b0: ldur            w2, [x0, #0xb]
    // 0x8f61b4: DecompressPointer r2
    //     0x8f61b4: add             x2, x2, HEAP, lsl #32
    // 0x8f61b8: r0 = _add()
    //     0x8f61b8: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f61bc: r0 = Null
    //     0x8f61bc: mov             x0, NULL
    // 0x8f61c0: LeaveFrame
    //     0x8f61c0: mov             SP, fp
    //     0x8f61c4: ldp             fp, lr, [SP], #0x10
    // 0x8f61c8: ret
    //     0x8f61c8: ret             
    // 0x8f61cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f61cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f61d0: b               #0x8f6198
  }
  _ _addZero(/* No info */) {
    // ** addr: 0x8f61d4, size: 0x50
    // 0x8f61d4: EnterFrame
    //     0x8f61d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f61d8: mov             fp, SP
    // 0x8f61dc: CheckStackOverflow
    //     0x8f61dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f61e0: cmp             SP, x16
    //     0x8f61e4: b.ls            #0x8f621c
    // 0x8f61e8: LoadField: r0 = r1->field_7b
    //     0x8f61e8: ldur            w0, [x1, #0x7b]
    // 0x8f61ec: DecompressPointer r0
    //     0x8f61ec: add             x0, x0, HEAP, lsl #32
    // 0x8f61f0: LoadField: r2 = r1->field_77
    //     0x8f61f0: ldur            w2, [x1, #0x77]
    // 0x8f61f4: DecompressPointer r2
    //     0x8f61f4: add             x2, x2, HEAP, lsl #32
    // 0x8f61f8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8f61f8: ldur            w1, [x2, #0x17]
    // 0x8f61fc: DecompressPointer r1
    //     0x8f61fc: add             x1, x1, HEAP, lsl #32
    // 0x8f6200: mov             x2, x1
    // 0x8f6204: mov             x1, x0
    // 0x8f6208: r0 = write()
    //     0x8f6208: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8f620c: r0 = Null
    //     0x8f620c: mov             x0, NULL
    // 0x8f6210: LeaveFrame
    //     0x8f6210: mov             SP, fp
    //     0x8f6214: ldp             fp, lr, [SP], #0x10
    // 0x8f6218: ret
    //     0x8f6218: ret             
    // 0x8f621c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f621c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6220: b               #0x8f61e8
  }
  _ _group(/* No info */) {
    // ** addr: 0x8f6224, size: 0xe8
    // 0x8f6224: EnterFrame
    //     0x8f6224: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6228: mov             fp, SP
    // 0x8f622c: CheckStackOverflow
    //     0x8f622c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6230: cmp             SP, x16
    //     0x8f6234: b.ls            #0x8f62d4
    // 0x8f6238: sub             x0, x2, x3
    // 0x8f623c: cmp             x0, #1
    // 0x8f6240: b.le            #0x8f6250
    // 0x8f6244: ArrayLoad: r2 = r1[0]  ; List_8
    //     0x8f6244: ldur            x2, [x1, #0x17]
    // 0x8f6248: cmp             x2, #0
    // 0x8f624c: b.gt            #0x8f6260
    // 0x8f6250: r0 = Null
    //     0x8f6250: mov             x0, NULL
    // 0x8f6254: LeaveFrame
    //     0x8f6254: mov             SP, fp
    //     0x8f6258: ldp             fp, lr, [SP], #0x10
    // 0x8f625c: ret
    //     0x8f625c: ret             
    // 0x8f6260: LoadField: r3 = r1->field_1f
    //     0x8f6260: ldur            x3, [x1, #0x1f]
    // 0x8f6264: add             x4, x3, #1
    // 0x8f6268: cmp             x0, x4
    // 0x8f626c: b.ne            #0x8f6288
    // 0x8f6270: LoadField: r0 = r1->field_77
    //     0x8f6270: ldur            w0, [x1, #0x77]
    // 0x8f6274: DecompressPointer r0
    //     0x8f6274: add             x0, x0, HEAP, lsl #32
    // 0x8f6278: LoadField: r2 = r0->field_f
    //     0x8f6278: ldur            w2, [x0, #0xf]
    // 0x8f627c: DecompressPointer r2
    //     0x8f627c: add             x2, x2, HEAP, lsl #32
    // 0x8f6280: r0 = _add()
    //     0x8f6280: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f6284: b               #0x8f62c4
    // 0x8f6288: cmp             x0, x3
    // 0x8f628c: b.le            #0x8f62c4
    // 0x8f6290: sub             x4, x0, x3
    // 0x8f6294: cbz             x2, #0x8f62dc
    // 0x8f6298: sdiv            x3, x4, x2
    // 0x8f629c: msub            x0, x3, x2, x4
    // 0x8f62a0: cmp             x0, xzr
    // 0x8f62a4: b.lt            #0x8f62f8
    // 0x8f62a8: cmp             x0, #1
    // 0x8f62ac: b.ne            #0x8f62c4
    // 0x8f62b0: LoadField: r0 = r1->field_77
    //     0x8f62b0: ldur            w0, [x1, #0x77]
    // 0x8f62b4: DecompressPointer r0
    //     0x8f62b4: add             x0, x0, HEAP, lsl #32
    // 0x8f62b8: LoadField: r2 = r0->field_f
    //     0x8f62b8: ldur            w2, [x0, #0xf]
    // 0x8f62bc: DecompressPointer r2
    //     0x8f62bc: add             x2, x2, HEAP, lsl #32
    // 0x8f62c0: r0 = _add()
    //     0x8f62c0: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f62c4: r0 = Null
    //     0x8f62c4: mov             x0, NULL
    // 0x8f62c8: LeaveFrame
    //     0x8f62c8: mov             SP, fp
    //     0x8f62cc: ldp             fp, lr, [SP], #0x10
    // 0x8f62d0: ret
    //     0x8f62d0: ret             
    // 0x8f62d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f62d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f62d8: b               #0x8f6238
    // 0x8f62dc: stp             x2, x4, [SP, #-0x10]!
    // 0x8f62e0: SaveReg r1
    //     0x8f62e0: str             x1, [SP, #-8]!
    // 0x8f62e4: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x8f62e8: r4 = 0
    //     0x8f62e8: movz            x4, #0
    // 0x8f62ec: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8f62f0: blr             lr
    // 0x8f62f4: brk             #0
    // 0x8f62f8: cmp             x2, xzr
    // 0x8f62fc: sub             x3, x0, x2
    // 0x8f6300: add             x0, x0, x2
    // 0x8f6304: csel            x0, x3, x0, lt
    // 0x8f6308: b               #0x8f62a8
  }
  _ _hasIntegerDigits(/* No info */) {
    // ** addr: 0x8f630c, size: 0x2c
    // 0x8f630c: LoadField: r3 = r2->field_7
    //     0x8f630c: ldur            w3, [x2, #7]
    // 0x8f6310: cbz             w3, #0x8f631c
    // 0x8f6314: r0 = true
    //     0x8f6314: add             x0, NULL, #0x20  ; true
    // 0x8f6318: b               #0x8f6334
    // 0x8f631c: LoadField: r2 = r1->field_3b
    //     0x8f631c: ldur            x2, [x1, #0x3b]
    // 0x8f6320: cmp             x2, #0
    // 0x8f6324: r16 = true
    //     0x8f6324: add             x16, NULL, #0x20  ; true
    // 0x8f6328: r17 = false
    //     0x8f6328: add             x17, NULL, #0x30  ; false
    // 0x8f632c: csel            x1, x16, x17, gt
    // 0x8f6330: mov             x0, x1
    // 0x8f6334: ret
    //     0x8f6334: ret             
  }
  _ _integerDigits(/* No info */) {
    // ** addr: 0x8f6338, size: 0x69c
    // 0x8f6338: EnterFrame
    //     0x8f6338: stp             fp, lr, [SP, #-0x10]!
    //     0x8f633c: mov             fp, SP
    // 0x8f6340: AllocStack(0x48)
    //     0x8f6340: sub             SP, SP, #0x48
    // 0x8f6344: SetupParameters(NumberFormat this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x8f6344: stur            x1, [fp, #-8]
    //     0x8f6348: stur            x2, [fp, #-0x10]
    //     0x8f634c: stur            x3, [fp, #-0x18]
    // 0x8f6350: CheckStackOverflow
    //     0x8f6350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6354: cmp             SP, x16
    //     0x8f6358: b.ls            #0x8f6968
    // 0x8f635c: r0 = 60
    //     0x8f635c: movz            x0, #0x3c
    // 0x8f6360: branchIfSmi(r2, 0x8f636c)
    //     0x8f6360: tbz             w2, #0, #0x8f636c
    // 0x8f6364: r0 = LoadClassIdInstr(r2)
    //     0x8f6364: ldur            x0, [x2, #-1]
    //     0x8f6368: ubfx            x0, x0, #0xc, #0x14
    // 0x8f636c: sub             x16, x0, #0x3c
    // 0x8f6370: cmp             x16, #2
    // 0x8f6374: b.hi            #0x8f6868
    // 0x8f6378: r0 = InitLateStaticField(0x1494) // [package:intl/src/intl/number_format.dart] NumberFormat::_maxInt
    //     0x8f6378: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f637c: ldr             x0, [x0, #0x2928]
    //     0x8f6380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f6384: cmp             w0, w16
    //     0x8f6388: b.ne            #0x8f6398
    //     0x8f638c: add             x2, PP, #0x27, lsl #12  ; [pp+0x27218] Field <NumberFormat._maxInt@1558441731>: static late final (offset: 0x1494)
    //     0x8f6390: ldr             x2, [x2, #0x218]
    //     0x8f6394: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6398: ldur            x1, [fp, #-0x10]
    // 0x8f639c: r2 = 60
    //     0x8f639c: movz            x2, #0x3c
    // 0x8f63a0: branchIfSmi(r1, 0x8f63ac)
    //     0x8f63a0: tbz             w1, #0, #0x8f63ac
    // 0x8f63a4: r2 = LoadClassIdInstr(r1)
    //     0x8f63a4: ldur            x2, [x1, #-1]
    //     0x8f63a8: ubfx            x2, x2, #0xc, #0x14
    // 0x8f63ac: stp             x0, x1, [SP]
    // 0x8f63b0: mov             x0, x2
    // 0x8f63b4: r0 = GDT[cid_x0 + -0xfe3]()
    //     0x8f63b4: sub             lr, x0, #0xfe3
    //     0x8f63b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8f63bc: blr             lr
    // 0x8f63c0: tbnz            w0, #4, #0x8f6860
    // 0x8f63c4: ldur            x1, [fp, #-0x10]
    // 0x8f63c8: r0 = 60
    //     0x8f63c8: movz            x0, #0x3c
    // 0x8f63cc: branchIfSmi(r1, 0x8f63d8)
    //     0x8f63cc: tbz             w1, #0, #0x8f63d8
    // 0x8f63d0: r0 = LoadClassIdInstr(r1)
    //     0x8f63d0: ldur            x0, [x1, #-1]
    //     0x8f63d4: ubfx            x0, x0, #0xc, #0x14
    // 0x8f63d8: str             x1, [SP]
    // 0x8f63dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f63dc: sub             lr, x0, #0xffa
    //     0x8f63e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8f63e4: blr             lr
    // 0x8f63e8: LoadField: d0 = r0->field_7
    //     0x8f63e8: ldur            d0, [x0, #7]
    // 0x8f63ec: stp             fp, lr, [SP, #-0x10]!
    // 0x8f63f0: mov             fp, SP
    // 0x8f63f4: CallRuntime_LibcLog(double) -> double
    //     0x8f63f4: and             SP, SP, #0xfffffffffffffff0
    //     0x8f63f8: mov             sp, SP
    //     0x8f63fc: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8f6400: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6404: blr             x16
    //     0x8f6408: movz            x16, #0x8
    //     0x8f640c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6410: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6414: sub             sp, x16, #1, lsl #12
    //     0x8f6418: mov             SP, fp
    //     0x8f641c: ldp             fp, lr, [SP], #0x10
    // 0x8f6420: stur            d0, [fp, #-0x38]
    // 0x8f6424: r0 = InitLateStaticField(0x149c) // [package:intl/src/intl/number_format.dart] ::_ln10
    //     0x8f6424: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f6428: ldr             x0, [x0, #0x2938]
    //     0x8f642c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f6430: cmp             w0, w16
    //     0x8f6434: b.ne            #0x8f6444
    //     0x8f6438: add             x2, PP, #0x27, lsl #12  ; [pp+0x27220] Field <::._ln10@1558441731>: static late final (offset: 0x149c)
    //     0x8f643c: ldr             x2, [x2, #0x220]
    //     0x8f6440: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6444: LoadField: d0 = r0->field_7
    //     0x8f6444: ldur            d0, [x0, #7]
    // 0x8f6448: ldur            d1, [fp, #-0x38]
    // 0x8f644c: fdiv            d2, d1, d0
    // 0x8f6450: fcmp            d2, d2
    // 0x8f6454: b.vs            #0x8f6970
    // 0x8f6458: fcvtps          x0, d2
    // 0x8f645c: asr             x16, x0, #0x1e
    // 0x8f6460: cmp             x16, x0, asr #63
    // 0x8f6464: b.ne            #0x8f6970
    // 0x8f6468: lsl             x0, x0, #1
    // 0x8f646c: stur            x0, [fp, #-0x20]
    // 0x8f6470: r0 = InitLateStaticField(0x1498) // [package:intl/src/intl/number_format.dart] NumberFormat::_maxDigits
    //     0x8f6470: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f6474: ldr             x0, [x0, #0x2930]
    //     0x8f6478: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f647c: cmp             w0, w16
    //     0x8f6480: b.ne            #0x8f6490
    //     0x8f6484: add             x2, PP, #0x27, lsl #12  ; [pp+0x27228] Field <NumberFormat._maxDigits@1558441731>: static late final (offset: 0x1498)
    //     0x8f6488: ldr             x2, [x2, #0x228]
    //     0x8f648c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6490: mov             x1, x0
    // 0x8f6494: ldur            x0, [fp, #-0x20]
    // 0x8f6498: r2 = LoadInt32Instr(r0)
    //     0x8f6498: sbfx            x2, x0, #1, #0x1f
    //     0x8f649c: tbz             w0, #0, #0x8f64a4
    //     0x8f64a0: ldur            x2, [x0, #7]
    // 0x8f64a4: r0 = LoadInt32Instr(r1)
    //     0x8f64a4: sbfx            x0, x1, #1, #0x1f
    //     0x8f64a8: tbz             w1, #0, #0x8f64b0
    //     0x8f64ac: ldur            x0, [x1, #7]
    // 0x8f64b0: sub             x3, x2, x0
    // 0x8f64b4: stur            x3, [fp, #-0x28]
    // 0x8f64b8: r0 = BoxInt64Instr(r3)
    //     0x8f64b8: sbfiz           x0, x3, #1, #0x1f
    //     0x8f64bc: cmp             x3, x0, asr #1
    //     0x8f64c0: b.eq            #0x8f64cc
    //     0x8f64c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f64c8: stur            x3, [x0, #7]
    // 0x8f64cc: mov             x2, x0
    // 0x8f64d0: stur            x2, [fp, #-0x20]
    // 0x8f64d4: tbnz            x3, #0x3f, #0x8f6534
    // 0x8f64d8: mov             x0, x3
    // 0x8f64dc: r1 = 10
    //     0x8f64dc: movz            x1, #0xa
    // 0x8f64e0: r4 = 1
    //     0x8f64e0: movz            x4, #0x1
    // 0x8f64e4: CheckStackOverflow
    //     0x8f64e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f64e8: cmp             SP, x16
    //     0x8f64ec: b.ls            #0x8f6990
    // 0x8f64f0: cbz             x0, #0x8f6518
    // 0x8f64f4: branchIfSmi(r0, 0x8f6500)
    //     0x8f64f4: tbz             w0, #0, #0x8f6500
    // 0x8f64f8: mul             x5, x4, x1
    // 0x8f64fc: mov             x4, x5
    // 0x8f6500: asr             x5, x0, #1
    // 0x8f6504: cbz             x5, #0x8f6510
    // 0x8f6508: mul             x6, x1, x1
    // 0x8f650c: mov             x1, x6
    // 0x8f6510: mov             x0, x5
    // 0x8f6514: b               #0x8f64e4
    // 0x8f6518: r0 = BoxInt64Instr(r4)
    //     0x8f6518: sbfiz           x0, x4, #1, #0x1f
    //     0x8f651c: cmp             x4, x0, asr #1
    //     0x8f6520: b.eq            #0x8f652c
    //     0x8f6524: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f6528: stur            x4, [x0, #7]
    // 0x8f652c: mov             x1, x0
    // 0x8f6530: b               #0x8f667c
    // 0x8f6534: r16 = 20
    //     0x8f6534: movz            x16, #0x14
    // 0x8f6538: stp             x16, NULL, [SP]
    // 0x8f653c: r0 = _Double.fromInteger()
    //     0x8f653c: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f6540: mov             x2, x0
    // 0x8f6544: ldur            x1, [fp, #-0x20]
    // 0x8f6548: stur            x2, [fp, #-0x30]
    // 0x8f654c: r0 = 60
    //     0x8f654c: movz            x0, #0x3c
    // 0x8f6550: branchIfSmi(r1, 0x8f655c)
    //     0x8f6550: tbz             w1, #0, #0x8f655c
    // 0x8f6554: r0 = LoadClassIdInstr(r1)
    //     0x8f6554: ldur            x0, [x1, #-1]
    //     0x8f6558: ubfx            x0, x0, #0xc, #0x14
    // 0x8f655c: str             x1, [SP]
    // 0x8f6560: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f6560: sub             lr, x0, #0xffa
    //     0x8f6564: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6568: blr             lr
    // 0x8f656c: mov             x1, x0
    // 0x8f6570: ldur            x0, [fp, #-0x30]
    // 0x8f6574: LoadField: d0 = r0->field_7
    //     0x8f6574: ldur            d0, [x0, #7]
    // 0x8f6578: LoadField: d1 = r1->field_7
    //     0x8f6578: ldur            d1, [x1, #7]
    // 0x8f657c: d30 = 0.000000
    //     0x8f657c: fmov            d30, d0
    // 0x8f6580: d0 = 1.000000
    //     0x8f6580: fmov            d0, #1.00000000
    // 0x8f6584: fcmp            d1, #0.0
    // 0x8f6588: b.vs            #0x8f65cc
    // 0x8f658c: b.eq            #0x8f6650
    // 0x8f6590: fcmp            d1, d0
    // 0x8f6594: b.eq            #0x8f65bc
    // 0x8f6598: d31 = 2.000000
    //     0x8f6598: fmov            d31, #2.00000000
    // 0x8f659c: fcmp            d1, d31
    // 0x8f65a0: b.eq            #0x8f65c4
    // 0x8f65a4: d31 = 3.000000
    //     0x8f65a4: fmov            d31, #3.00000000
    // 0x8f65a8: fcmp            d1, d31
    // 0x8f65ac: b.ne            #0x8f65cc
    // 0x8f65b0: fmul            d0, d30, d30
    // 0x8f65b4: fmul            d0, d0, d30
    // 0x8f65b8: b               #0x8f6650
    // 0x8f65bc: d0 = 0.000000
    //     0x8f65bc: fmov            d0, d30
    // 0x8f65c0: b               #0x8f6650
    // 0x8f65c4: fmul            d0, d30, d30
    // 0x8f65c8: b               #0x8f6650
    // 0x8f65cc: fcmp            d30, d0
    // 0x8f65d0: b.vs            #0x8f65e0
    // 0x8f65d4: b.eq            #0x8f6650
    // 0x8f65d8: fcmp            d30, d1
    // 0x8f65dc: b.vc            #0x8f65e8
    // 0x8f65e0: d0 = nan
    //     0x8f65e0: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0x8f65e4: b               #0x8f6650
    // 0x8f65e8: d0 = -inf
    //     0x8f65e8: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x8f65ec: fcmp            d30, d0
    // 0x8f65f0: b.eq            #0x8f6618
    // 0x8f65f4: d0 = 0.500000
    //     0x8f65f4: fmov            d0, #0.50000000
    // 0x8f65f8: fcmp            d1, d0
    // 0x8f65fc: b.ne            #0x8f6618
    // 0x8f6600: fcmp            d30, #0.0
    // 0x8f6604: b.eq            #0x8f6610
    // 0x8f6608: fsqrt           d0, d30
    // 0x8f660c: b               #0x8f6650
    // 0x8f6610: d0 = 0.000000
    //     0x8f6610: eor             v0.16b, v0.16b, v0.16b
    // 0x8f6614: b               #0x8f6650
    // 0x8f6618: d0 = 0.000000
    //     0x8f6618: fmov            d0, d30
    // 0x8f661c: stp             fp, lr, [SP, #-0x10]!
    // 0x8f6620: mov             fp, SP
    // 0x8f6624: CallRuntime_LibcPow(double, double) -> double
    //     0x8f6624: and             SP, SP, #0xfffffffffffffff0
    //     0x8f6628: mov             sp, SP
    //     0x8f662c: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0x8f6630: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6634: blr             x16
    //     0x8f6638: movz            x16, #0x8
    //     0x8f663c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6640: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6644: sub             sp, x16, #1, lsl #12
    //     0x8f6648: mov             SP, fp
    //     0x8f664c: ldp             fp, lr, [SP], #0x10
    // 0x8f6650: r0 = inline_Allocate_Double()
    //     0x8f6650: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8f6654: add             x0, x0, #0x10
    //     0x8f6658: cmp             x1, x0
    //     0x8f665c: b.ls            #0x8f6998
    //     0x8f6660: str             x0, [THR, #0x50]  ; THR::top
    //     0x8f6664: sub             x0, x0, #0xf
    //     0x8f6668: movz            x1, #0xe15c
    //     0x8f666c: movk            x1, #0x3, lsl #16
    //     0x8f6670: stur            x1, [x0, #-1]
    // 0x8f6674: StoreField: r0->field_7 = d0
    //     0x8f6674: stur            d0, [x0, #7]
    // 0x8f6678: mov             x1, x0
    // 0x8f667c: r0 = 60
    //     0x8f667c: movz            x0, #0x3c
    // 0x8f6680: branchIfSmi(r1, 0x8f668c)
    //     0x8f6680: tbz             w1, #0, #0x8f668c
    // 0x8f6684: r0 = LoadClassIdInstr(r1)
    //     0x8f6684: ldur            x0, [x1, #-1]
    //     0x8f6688: ubfx            x0, x0, #0xc, #0x14
    // 0x8f668c: r0 = GDT[cid_x0 + -0xfbe]()
    //     0x8f668c: sub             lr, x0, #0xfbe
    //     0x8f6690: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6694: blr             lr
    // 0x8f6698: mov             x2, x0
    // 0x8f669c: cbnz            x2, #0x8f67d4
    // 0x8f66a0: ldur            x0, [fp, #-0x20]
    // 0x8f66a4: r1 = 60
    //     0x8f66a4: movz            x1, #0x3c
    // 0x8f66a8: branchIfSmi(r0, 0x8f66b4)
    //     0x8f66a8: tbz             w0, #0, #0x8f66b4
    // 0x8f66ac: r1 = LoadClassIdInstr(r0)
    //     0x8f66ac: ldur            x1, [x0, #-1]
    //     0x8f66b0: ubfx            x1, x1, #0xc, #0x14
    // 0x8f66b4: str             x0, [SP]
    // 0x8f66b8: mov             x0, x1
    // 0x8f66bc: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f66bc: sub             lr, x0, #0xffa
    //     0x8f66c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8f66c4: blr             lr
    // 0x8f66c8: LoadField: d1 = r0->field_7
    //     0x8f66c8: ldur            d1, [x0, #7]
    // 0x8f66cc: d0 = 10.000000
    //     0x8f66cc: fmov            d0, #10.00000000
    // 0x8f66d0: d30 = 0.000000
    //     0x8f66d0: fmov            d30, d0
    // 0x8f66d4: d0 = 1.000000
    //     0x8f66d4: fmov            d0, #1.00000000
    // 0x8f66d8: fcmp            d1, #0.0
    // 0x8f66dc: b.vs            #0x8f6720
    // 0x8f66e0: b.eq            #0x8f67a4
    // 0x8f66e4: fcmp            d1, d0
    // 0x8f66e8: b.eq            #0x8f6710
    // 0x8f66ec: d31 = 2.000000
    //     0x8f66ec: fmov            d31, #2.00000000
    // 0x8f66f0: fcmp            d1, d31
    // 0x8f66f4: b.eq            #0x8f6718
    // 0x8f66f8: d31 = 3.000000
    //     0x8f66f8: fmov            d31, #3.00000000
    // 0x8f66fc: fcmp            d1, d31
    // 0x8f6700: b.ne            #0x8f6720
    // 0x8f6704: fmul            d0, d30, d30
    // 0x8f6708: fmul            d0, d0, d30
    // 0x8f670c: b               #0x8f67a4
    // 0x8f6710: d0 = 0.000000
    //     0x8f6710: fmov            d0, d30
    // 0x8f6714: b               #0x8f67a4
    // 0x8f6718: fmul            d0, d30, d30
    // 0x8f671c: b               #0x8f67a4
    // 0x8f6720: fcmp            d30, d0
    // 0x8f6724: b.vs            #0x8f6734
    // 0x8f6728: b.eq            #0x8f67a4
    // 0x8f672c: fcmp            d30, d1
    // 0x8f6730: b.vc            #0x8f673c
    // 0x8f6734: d0 = nan
    //     0x8f6734: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0x8f6738: b               #0x8f67a4
    // 0x8f673c: d0 = -inf
    //     0x8f673c: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x8f6740: fcmp            d30, d0
    // 0x8f6744: b.eq            #0x8f676c
    // 0x8f6748: d0 = 0.500000
    //     0x8f6748: fmov            d0, #0.50000000
    // 0x8f674c: fcmp            d1, d0
    // 0x8f6750: b.ne            #0x8f676c
    // 0x8f6754: fcmp            d30, #0.0
    // 0x8f6758: b.eq            #0x8f6764
    // 0x8f675c: fsqrt           d0, d30
    // 0x8f6760: b               #0x8f67a4
    // 0x8f6764: d0 = 0.000000
    //     0x8f6764: eor             v0.16b, v0.16b, v0.16b
    // 0x8f6768: b               #0x8f67a4
    // 0x8f676c: d0 = 0.000000
    //     0x8f676c: fmov            d0, d30
    // 0x8f6770: stp             fp, lr, [SP, #-0x10]!
    // 0x8f6774: mov             fp, SP
    // 0x8f6778: CallRuntime_LibcPow(double, double) -> double
    //     0x8f6778: and             SP, SP, #0xfffffffffffffff0
    //     0x8f677c: mov             sp, SP
    //     0x8f6780: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0x8f6784: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6788: blr             x16
    //     0x8f678c: movz            x16, #0x8
    //     0x8f6790: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6794: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6798: sub             sp, x16, #1, lsl #12
    //     0x8f679c: mov             SP, fp
    //     0x8f67a0: ldp             fp, lr, [SP], #0x10
    // 0x8f67a4: r0 = inline_Allocate_Double()
    //     0x8f67a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8f67a8: add             x0, x0, #0x10
    //     0x8f67ac: cmp             x1, x0
    //     0x8f67b0: b.ls            #0x8f69a8
    //     0x8f67b4: str             x0, [THR, #0x50]  ; THR::top
    //     0x8f67b8: sub             x0, x0, #0xf
    //     0x8f67bc: movz            x1, #0xe15c
    //     0x8f67c0: movk            x1, #0x3, lsl #16
    //     0x8f67c4: stur            x1, [x0, #-1]
    // 0x8f67c8: StoreField: r0->field_7 = d0
    //     0x8f67c8: stur            d0, [x0, #7]
    // 0x8f67cc: mov             x3, x0
    // 0x8f67d0: b               #0x8f67ec
    // 0x8f67d4: r0 = BoxInt64Instr(r2)
    //     0x8f67d4: sbfiz           x0, x2, #1, #0x1f
    //     0x8f67d8: cmp             x2, x0, asr #1
    //     0x8f67dc: b.eq            #0x8f67e8
    //     0x8f67e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f67e4: stur            x2, [x0, #7]
    // 0x8f67e8: mov             x3, x0
    // 0x8f67ec: ldur            x0, [fp, #-0x10]
    // 0x8f67f0: ldur            x2, [fp, #-0x28]
    // 0x8f67f4: stur            x3, [fp, #-0x20]
    // 0x8f67f8: r1 = "0"
    //     0x8f67f8: ldr             x1, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8f67fc: r0 = *()
    //     0x8f67fc: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0x8f6800: mov             x1, x0
    // 0x8f6804: ldur            x0, [fp, #-0x10]
    // 0x8f6808: stur            x1, [fp, #-0x30]
    // 0x8f680c: r2 = 60
    //     0x8f680c: movz            x2, #0x3c
    // 0x8f6810: branchIfSmi(r0, 0x8f681c)
    //     0x8f6810: tbz             w0, #0, #0x8f681c
    // 0x8f6814: r2 = LoadClassIdInstr(r0)
    //     0x8f6814: ldur            x2, [x0, #-1]
    //     0x8f6818: ubfx            x2, x2, #0xc, #0x14
    // 0x8f681c: ldur            x16, [fp, #-0x20]
    // 0x8f6820: stp             x16, x0, [SP]
    // 0x8f6824: mov             x0, x2
    // 0x8f6828: r0 = GDT[cid_x0 + -0xff7]()
    //     0x8f6828: sub             lr, x0, #0xff7
    //     0x8f682c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6830: blr             lr
    // 0x8f6834: LoadField: d0 = r0->field_7
    //     0x8f6834: ldur            d0, [x0, #7]
    // 0x8f6838: fcmp            d0, d0
    // 0x8f683c: b.vs            #0x8f69b8
    // 0x8f6840: fcvtzs          x0, d0
    // 0x8f6844: asr             x16, x0, #0x1e
    // 0x8f6848: cmp             x16, x0, asr #63
    // 0x8f684c: b.ne            #0x8f69b8
    // 0x8f6850: lsl             x0, x0, #1
    // 0x8f6854: mov             x4, x0
    // 0x8f6858: ldur            x3, [fp, #-0x30]
    // 0x8f685c: b               #0x8f6874
    // 0x8f6860: ldur            x0, [fp, #-0x10]
    // 0x8f6864: b               #0x8f686c
    // 0x8f6868: mov             x0, x2
    // 0x8f686c: mov             x4, x0
    // 0x8f6870: r3 = ""
    //     0x8f6870: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f6874: ldur            x2, [fp, #-0x18]
    // 0x8f6878: stur            x4, [fp, #-0x10]
    // 0x8f687c: stur            x3, [fp, #-0x20]
    // 0x8f6880: cbnz            x2, #0x8f688c
    // 0x8f6884: r0 = ""
    //     0x8f6884: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f6888: b               #0x8f68cc
    // 0x8f688c: r0 = BoxInt64Instr(r2)
    //     0x8f688c: sbfiz           x0, x2, #1, #0x1f
    //     0x8f6890: cmp             x2, x0, asr #1
    //     0x8f6894: b.eq            #0x8f68a0
    //     0x8f6898: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f689c: stur            x2, [x0, #7]
    // 0x8f68a0: r1 = 60
    //     0x8f68a0: movz            x1, #0x3c
    // 0x8f68a4: branchIfSmi(r0, 0x8f68b0)
    //     0x8f68a4: tbz             w0, #0, #0x8f68b0
    // 0x8f68a8: r1 = LoadClassIdInstr(r0)
    //     0x8f68a8: ldur            x1, [x0, #-1]
    //     0x8f68ac: ubfx            x1, x1, #0xc, #0x14
    // 0x8f68b0: str             x0, [SP]
    // 0x8f68b4: mov             x0, x1
    // 0x8f68b8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8f68b8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8f68bc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8f68bc: movz            x17, #0x2b03
    //     0x8f68c0: add             lr, x0, x17
    //     0x8f68c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8f68c8: blr             lr
    // 0x8f68cc: ldur            x1, [fp, #-8]
    // 0x8f68d0: ldur            x2, [fp, #-0x10]
    // 0x8f68d4: stur            x0, [fp, #-0x30]
    // 0x8f68d8: r0 = _mainIntegerDigits()
    //     0x8f68d8: bl              #0x8f69d4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_mainIntegerDigits
    // 0x8f68dc: mov             x4, x0
    // 0x8f68e0: stur            x4, [fp, #-0x10]
    // 0x8f68e4: LoadField: r0 = r4->field_7
    //     0x8f68e4: ldur            w0, [x4, #7]
    // 0x8f68e8: cbnz            w0, #0x8f68f8
    // 0x8f68ec: mov             x0, x4
    // 0x8f68f0: ldur            x4, [fp, #-0x30]
    // 0x8f68f4: b               #0x8f6924
    // 0x8f68f8: ldur            x0, [fp, #-8]
    // 0x8f68fc: ldur            x1, [fp, #-0x30]
    // 0x8f6900: LoadField: r2 = r0->field_67
    //     0x8f6900: ldur            x2, [x0, #0x67]
    // 0x8f6904: r0 = LoadClassIdInstr(r1)
    //     0x8f6904: ldur            x0, [x1, #-1]
    //     0x8f6908: ubfx            x0, x0, #0xc, #0x14
    // 0x8f690c: r3 = "0"
    //     0x8f690c: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8f6910: r0 = GDT[cid_x0 + -0xff8]()
    //     0x8f6910: sub             lr, x0, #0xff8
    //     0x8f6914: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6918: blr             lr
    // 0x8f691c: mov             x4, x0
    // 0x8f6920: ldur            x0, [fp, #-0x10]
    // 0x8f6924: ldur            x3, [fp, #-0x20]
    // 0x8f6928: stur            x4, [fp, #-8]
    // 0x8f692c: r1 = Null
    //     0x8f692c: mov             x1, NULL
    // 0x8f6930: r2 = 6
    //     0x8f6930: movz            x2, #0x6
    // 0x8f6934: r0 = AllocateArray()
    //     0x8f6934: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8f6938: mov             x1, x0
    // 0x8f693c: ldur            x0, [fp, #-0x10]
    // 0x8f6940: StoreField: r1->field_f = r0
    //     0x8f6940: stur            w0, [x1, #0xf]
    // 0x8f6944: ldur            x0, [fp, #-8]
    // 0x8f6948: StoreField: r1->field_13 = r0
    //     0x8f6948: stur            w0, [x1, #0x13]
    // 0x8f694c: ldur            x0, [fp, #-0x20]
    // 0x8f6950: ArrayStore: r1[0] = r0  ; List_4
    //     0x8f6950: stur            w0, [x1, #0x17]
    // 0x8f6954: str             x1, [SP]
    // 0x8f6958: r0 = _interpolate()
    //     0x8f6958: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8f695c: LeaveFrame
    //     0x8f695c: mov             SP, fp
    //     0x8f6960: ldp             fp, lr, [SP], #0x10
    // 0x8f6964: ret
    //     0x8f6964: ret             
    // 0x8f6968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6968: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f696c: b               #0x8f635c
    // 0x8f6970: SaveReg d2
    //     0x8f6970: str             q2, [SP, #-0x10]!
    // 0x8f6974: d0 = 0.000000
    //     0x8f6974: fmov            d0, d2
    // 0x8f6978: r0 = 64
    //     0x8f6978: movz            x0, #0x40
    // 0x8f697c: r30 = DoubleToIntegerStub
    //     0x8f697c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f6980: LoadField: r30 = r30->field_7
    //     0x8f6980: ldur            lr, [lr, #7]
    // 0x8f6984: blr             lr
    // 0x8f6988: RestoreReg d2
    //     0x8f6988: ldr             q2, [SP], #0x10
    // 0x8f698c: b               #0x8f646c
    // 0x8f6990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6994: b               #0x8f64f0
    // 0x8f6998: SaveReg d0
    //     0x8f6998: str             q0, [SP, #-0x10]!
    // 0x8f699c: r0 = AllocateDouble()
    //     0x8f699c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f69a0: RestoreReg d0
    //     0x8f69a0: ldr             q0, [SP], #0x10
    // 0x8f69a4: b               #0x8f6674
    // 0x8f69a8: SaveReg d0
    //     0x8f69a8: str             q0, [SP, #-0x10]!
    // 0x8f69ac: r0 = AllocateDouble()
    //     0x8f69ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f69b0: RestoreReg d0
    //     0x8f69b0: ldr             q0, [SP], #0x10
    // 0x8f69b4: b               #0x8f67c8
    // 0x8f69b8: SaveReg d0
    //     0x8f69b8: str             q0, [SP, #-0x10]!
    // 0x8f69bc: r0 = 74
    //     0x8f69bc: movz            x0, #0x4a
    // 0x8f69c0: r30 = DoubleToIntegerStub
    //     0x8f69c0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f69c4: LoadField: r30 = r30->field_7
    //     0x8f69c4: ldur            lr, [lr, #7]
    // 0x8f69c8: blr             lr
    // 0x8f69cc: RestoreReg d0
    //     0x8f69cc: ldr             q0, [SP], #0x10
    // 0x8f69d0: b               #0x8f6854
  }
  _ _mainIntegerDigits(/* No info */) {
    // ** addr: 0x8f69d4, size: 0xc4
    // 0x8f69d4: EnterFrame
    //     0x8f69d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f69d8: mov             fp, SP
    // 0x8f69dc: AllocStack(0x18)
    //     0x8f69dc: sub             SP, SP, #0x18
    // 0x8f69e0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8f69e0: stur            x2, [fp, #-8]
    // 0x8f69e4: CheckStackOverflow
    //     0x8f69e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f69e8: cmp             SP, x16
    //     0x8f69ec: b.ls            #0x8f6a90
    // 0x8f69f0: r0 = 60
    //     0x8f69f0: movz            x0, #0x3c
    // 0x8f69f4: branchIfSmi(r2, 0x8f6a00)
    //     0x8f69f4: tbz             w2, #0, #0x8f6a00
    // 0x8f69f8: r0 = LoadClassIdInstr(r2)
    //     0x8f69f8: ldur            x0, [x2, #-1]
    //     0x8f69fc: ubfx            x0, x0, #0xc, #0x14
    // 0x8f6a00: stp             xzr, x2, [SP]
    // 0x8f6a04: mov             lr, x0
    // 0x8f6a08: ldr             lr, [x21, lr, lsl #3]
    // 0x8f6a0c: blr             lr
    // 0x8f6a10: tbnz            w0, #4, #0x8f6a24
    // 0x8f6a14: r0 = ""
    //     0x8f6a14: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8f6a18: LeaveFrame
    //     0x8f6a18: mov             SP, fp
    //     0x8f6a1c: ldp             fp, lr, [SP], #0x10
    // 0x8f6a20: ret
    //     0x8f6a20: ret             
    // 0x8f6a24: ldur            x0, [fp, #-8]
    // 0x8f6a28: r1 = 60
    //     0x8f6a28: movz            x1, #0x3c
    // 0x8f6a2c: branchIfSmi(r0, 0x8f6a38)
    //     0x8f6a2c: tbz             w0, #0, #0x8f6a38
    // 0x8f6a30: r1 = LoadClassIdInstr(r0)
    //     0x8f6a30: ldur            x1, [x0, #-1]
    //     0x8f6a34: ubfx            x1, x1, #0xc, #0x14
    // 0x8f6a38: str             x0, [SP]
    // 0x8f6a3c: mov             x0, x1
    // 0x8f6a40: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8f6a40: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8f6a44: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8f6a44: movz            x17, #0x2b03
    //     0x8f6a48: add             lr, x0, x17
    //     0x8f6a4c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6a50: blr             lr
    // 0x8f6a54: mov             x1, x0
    // 0x8f6a58: r2 = "-"
    //     0x8f6a58: ldr             x2, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x8f6a5c: stur            x0, [fp, #-8]
    // 0x8f6a60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f6a60: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f6a64: r0 = startsWith()
    //     0x8f6a64: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x8f6a68: tbnz            w0, #4, #0x8f6a80
    // 0x8f6a6c: ldur            x1, [fp, #-8]
    // 0x8f6a70: r2 = 1
    //     0x8f6a70: movz            x2, #0x1
    // 0x8f6a74: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8f6a74: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8f6a78: r0 = substring()
    //     0x8f6a78: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x8f6a7c: b               #0x8f6a84
    // 0x8f6a80: ldur            x0, [fp, #-8]
    // 0x8f6a84: LeaveFrame
    //     0x8f6a84: mov             SP, fp
    //     0x8f6a88: ldp             fp, lr, [SP], #0x10
    // 0x8f6a8c: ret
    //     0x8f6a8c: ret             
    // 0x8f6a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6a90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6a94: b               #0x8f69f0
  }
  static int _maxDigits() {
    // ** addr: 0x8f6a98, size: 0x138
    // 0x8f6a98: EnterFrame
    //     0x8f6a98: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6a9c: mov             fp, SP
    // 0x8f6aa0: AllocStack(0x18)
    //     0x8f6aa0: sub             SP, SP, #0x18
    // 0x8f6aa4: CheckStackOverflow
    //     0x8f6aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6aa8: cmp             SP, x16
    //     0x8f6aac: b.ls            #0x8f6ba8
    // 0x8f6ab0: r0 = InitLateStaticField(0x1494) // [package:intl/src/intl/number_format.dart] NumberFormat::_maxInt
    //     0x8f6ab0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f6ab4: ldr             x0, [x0, #0x2928]
    //     0x8f6ab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f6abc: cmp             w0, w16
    //     0x8f6ac0: b.ne            #0x8f6ad0
    //     0x8f6ac4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27218] Field <NumberFormat._maxInt@1558441731>: static late final (offset: 0x1494)
    //     0x8f6ac8: ldr             x2, [x2, #0x218]
    //     0x8f6acc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6ad0: r1 = 60
    //     0x8f6ad0: movz            x1, #0x3c
    // 0x8f6ad4: branchIfSmi(r0, 0x8f6ae0)
    //     0x8f6ad4: tbz             w0, #0, #0x8f6ae0
    // 0x8f6ad8: r1 = LoadClassIdInstr(r0)
    //     0x8f6ad8: ldur            x1, [x0, #-1]
    //     0x8f6adc: ubfx            x1, x1, #0xc, #0x14
    // 0x8f6ae0: str             x0, [SP]
    // 0x8f6ae4: mov             x0, x1
    // 0x8f6ae8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f6ae8: sub             lr, x0, #0xffa
    //     0x8f6aec: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6af0: blr             lr
    // 0x8f6af4: LoadField: d0 = r0->field_7
    //     0x8f6af4: ldur            d0, [x0, #7]
    // 0x8f6af8: stp             fp, lr, [SP, #-0x10]!
    // 0x8f6afc: mov             fp, SP
    // 0x8f6b00: CallRuntime_LibcLog(double) -> double
    //     0x8f6b00: and             SP, SP, #0xfffffffffffffff0
    //     0x8f6b04: mov             sp, SP
    //     0x8f6b08: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8f6b0c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6b10: blr             x16
    //     0x8f6b14: movz            x16, #0x8
    //     0x8f6b18: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6b1c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6b20: sub             sp, x16, #1, lsl #12
    //     0x8f6b24: mov             SP, fp
    //     0x8f6b28: ldp             fp, lr, [SP], #0x10
    // 0x8f6b2c: stur            d0, [fp, #-8]
    // 0x8f6b30: r16 = 20
    //     0x8f6b30: movz            x16, #0x14
    // 0x8f6b34: stp             x16, NULL, [SP]
    // 0x8f6b38: r0 = _Double.fromInteger()
    //     0x8f6b38: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f6b3c: LoadField: d0 = r0->field_7
    //     0x8f6b3c: ldur            d0, [x0, #7]
    // 0x8f6b40: stp             fp, lr, [SP, #-0x10]!
    // 0x8f6b44: mov             fp, SP
    // 0x8f6b48: CallRuntime_LibcLog(double) -> double
    //     0x8f6b48: and             SP, SP, #0xfffffffffffffff0
    //     0x8f6b4c: mov             sp, SP
    //     0x8f6b50: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8f6b54: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6b58: blr             x16
    //     0x8f6b5c: movz            x16, #0x8
    //     0x8f6b60: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6b64: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6b68: sub             sp, x16, #1, lsl #12
    //     0x8f6b6c: mov             SP, fp
    //     0x8f6b70: ldp             fp, lr, [SP], #0x10
    // 0x8f6b74: mov             v1.16b, v0.16b
    // 0x8f6b78: ldur            d0, [fp, #-8]
    // 0x8f6b7c: fdiv            d2, d0, d1
    // 0x8f6b80: fcmp            d2, d2
    // 0x8f6b84: b.vs            #0x8f6bb0
    // 0x8f6b88: fcvtps          x0, d2
    // 0x8f6b8c: asr             x16, x0, #0x1e
    // 0x8f6b90: cmp             x16, x0, asr #63
    // 0x8f6b94: b.ne            #0x8f6bb0
    // 0x8f6b98: lsl             x0, x0, #1
    // 0x8f6b9c: LeaveFrame
    //     0x8f6b9c: mov             SP, fp
    //     0x8f6ba0: ldp             fp, lr, [SP], #0x10
    // 0x8f6ba4: ret
    //     0x8f6ba4: ret             
    // 0x8f6ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6ba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6bac: b               #0x8f6ab0
    // 0x8f6bb0: SaveReg d2
    //     0x8f6bb0: str             q2, [SP, #-0x10]!
    // 0x8f6bb4: d0 = 0.000000
    //     0x8f6bb4: fmov            d0, d2
    // 0x8f6bb8: r0 = 64
    //     0x8f6bb8: movz            x0, #0x40
    // 0x8f6bbc: r30 = DoubleToIntegerStub
    //     0x8f6bbc: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f6bc0: LoadField: r30 = r30->field_7
    //     0x8f6bc0: ldur            lr, [lr, #7]
    // 0x8f6bc4: blr             lr
    // 0x8f6bc8: RestoreReg d2
    //     0x8f6bc8: ldr             q2, [SP], #0x10
    // 0x8f6bcc: b               #0x8f6b9c
  }
  static num _maxInt() {
    // ** addr: 0x8f6bd0, size: 0x54
    // 0x8f6bd0: EnterFrame
    //     0x8f6bd0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6bd4: mov             fp, SP
    // 0x8f6bd8: d0 = 1000000000000000052504760255204420248704468581108159154915854115511802457988908195786371375080447864043704443832883878176942523235360430575644792184786706982848387200926575803737830233794788090059368953234970799945081119038967640880074652742780142494579258788820056842838115669472196386865459400540160.000000
    //     0x8f6bd8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27230] IMM: double(1e+300) from 0x7e37e43c8800759c
    //     0x8f6bdc: ldr             d0, [x17, #0x230]
    // 0x8f6be0: fcmp            d0, d0
    // 0x8f6be4: b.vs            #0x8f6c08
    // 0x8f6be8: fcvtms          x0, d0
    // 0x8f6bec: asr             x16, x0, #0x1e
    // 0x8f6bf0: cmp             x16, x0, asr #63
    // 0x8f6bf4: b.ne            #0x8f6c08
    // 0x8f6bf8: lsl             x0, x0, #1
    // 0x8f6bfc: LeaveFrame
    //     0x8f6bfc: mov             SP, fp
    //     0x8f6c00: ldp             fp, lr, [SP], #0x10
    // 0x8f6c04: ret
    //     0x8f6c04: ret             
    // 0x8f6c08: SaveReg d0
    //     0x8f6c08: str             q0, [SP, #-0x10]!
    // 0x8f6c0c: r0 = 68
    //     0x8f6c0c: movz            x0, #0x44
    // 0x8f6c10: r30 = DoubleToIntegerStub
    //     0x8f6c10: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f6c14: LoadField: r30 = r30->field_7
    //     0x8f6c14: ldur            lr, [lr, #7]
    // 0x8f6c18: blr             lr
    // 0x8f6c1c: RestoreReg d0
    //     0x8f6c1c: ldr             q0, [SP], #0x10
    // 0x8f6c20: b               #0x8f6bfc
  }
  static int numberOfIntegerDigits(dynamic) {
    // ** addr: 0x8f6c24, size: 0x374
    // 0x8f6c24: EnterFrame
    //     0x8f6c24: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6c28: mov             fp, SP
    // 0x8f6c2c: AllocStack(0x18)
    //     0x8f6c2c: sub             SP, SP, #0x18
    // 0x8f6c30: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0x8f6c30: mov             x2, x1
    // 0x8f6c34: CheckStackOverflow
    //     0x8f6c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6c38: cmp             SP, x16
    //     0x8f6c3c: b.ls            #0x8f6f6c
    // 0x8f6c40: r0 = BoxInt64Instr(r2)
    //     0x8f6c40: sbfiz           x0, x2, #1, #0x1f
    //     0x8f6c44: cmp             x2, x0, asr #1
    //     0x8f6c48: b.eq            #0x8f6c54
    //     0x8f6c4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f6c50: stur            x2, [x0, #7]
    // 0x8f6c54: stp             x0, NULL, [SP]
    // 0x8f6c58: r0 = _Double.fromInteger()
    //     0x8f6c58: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f6c5c: LoadField: d0 = r0->field_7
    //     0x8f6c5c: ldur            d0, [x0, #7]
    // 0x8f6c60: d1 = 0.000000
    //     0x8f6c60: eor             v1.16b, v1.16b, v1.16b
    // 0x8f6c64: fcmp            d0, d1
    // 0x8f6c68: b.ne            #0x8f6c74
    // 0x8f6c6c: d1 = 0.000000
    //     0x8f6c6c: eor             v1.16b, v1.16b, v1.16b
    // 0x8f6c70: b               #0x8f6c88
    // 0x8f6c74: fcmp            d1, d0
    // 0x8f6c78: b.le            #0x8f6c84
    // 0x8f6c7c: fneg            d1, d0
    // 0x8f6c80: mov             v0.16b, v1.16b
    // 0x8f6c84: mov             v1.16b, v0.16b
    // 0x8f6c88: d0 = 10.000000
    //     0x8f6c88: fmov            d0, #10.00000000
    // 0x8f6c8c: fcmp            d0, d1
    // 0x8f6c90: b.le            #0x8f6ca4
    // 0x8f6c94: r0 = 1
    //     0x8f6c94: movz            x0, #0x1
    // 0x8f6c98: LeaveFrame
    //     0x8f6c98: mov             SP, fp
    //     0x8f6c9c: ldp             fp, lr, [SP], #0x10
    // 0x8f6ca0: ret
    //     0x8f6ca0: ret             
    // 0x8f6ca4: d0 = 100.000000
    //     0x8f6ca4: ldr             d0, [PP, #0x5930]  ; [pp+0x5930] IMM: double(100) from 0x4059000000000000
    // 0x8f6ca8: fcmp            d0, d1
    // 0x8f6cac: b.le            #0x8f6cc0
    // 0x8f6cb0: r0 = 2
    //     0x8f6cb0: movz            x0, #0x2
    // 0x8f6cb4: LeaveFrame
    //     0x8f6cb4: mov             SP, fp
    //     0x8f6cb8: ldp             fp, lr, [SP], #0x10
    // 0x8f6cbc: ret
    //     0x8f6cbc: ret             
    // 0x8f6cc0: d0 = 1000.000000
    //     0x8f6cc0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x8f6cc4: ldr             d0, [x17, #0x238]
    // 0x8f6cc8: fcmp            d0, d1
    // 0x8f6ccc: b.le            #0x8f6ce0
    // 0x8f6cd0: r0 = 3
    //     0x8f6cd0: movz            x0, #0x3
    // 0x8f6cd4: LeaveFrame
    //     0x8f6cd4: mov             SP, fp
    //     0x8f6cd8: ldp             fp, lr, [SP], #0x10
    // 0x8f6cdc: ret
    //     0x8f6cdc: ret             
    // 0x8f6ce0: d0 = 10000.000000
    //     0x8f6ce0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27240] IMM: double(10000) from 0x40c3880000000000
    //     0x8f6ce4: ldr             d0, [x17, #0x240]
    // 0x8f6ce8: fcmp            d0, d1
    // 0x8f6cec: b.le            #0x8f6d00
    // 0x8f6cf0: r0 = 4
    //     0x8f6cf0: movz            x0, #0x4
    // 0x8f6cf4: LeaveFrame
    //     0x8f6cf4: mov             SP, fp
    //     0x8f6cf8: ldp             fp, lr, [SP], #0x10
    // 0x8f6cfc: ret
    //     0x8f6cfc: ret             
    // 0x8f6d00: d0 = 100000.000000
    //     0x8f6d00: add             x17, PP, #0x27, lsl #12  ; [pp+0x27248] IMM: double(1e+05) from 0x40f86a0000000000
    //     0x8f6d04: ldr             d0, [x17, #0x248]
    // 0x8f6d08: fcmp            d0, d1
    // 0x8f6d0c: b.le            #0x8f6d20
    // 0x8f6d10: r0 = 5
    //     0x8f6d10: movz            x0, #0x5
    // 0x8f6d14: LeaveFrame
    //     0x8f6d14: mov             SP, fp
    //     0x8f6d18: ldp             fp, lr, [SP], #0x10
    // 0x8f6d1c: ret
    //     0x8f6d1c: ret             
    // 0x8f6d20: d0 = 1000000.000000
    //     0x8f6d20: ldr             d0, [PP, #0x4ed0]  ; [pp+0x4ed0] IMM: double(1e+06) from 0x412e848000000000
    // 0x8f6d24: fcmp            d0, d1
    // 0x8f6d28: b.le            #0x8f6d3c
    // 0x8f6d2c: r0 = 6
    //     0x8f6d2c: movz            x0, #0x6
    // 0x8f6d30: LeaveFrame
    //     0x8f6d30: mov             SP, fp
    //     0x8f6d34: ldp             fp, lr, [SP], #0x10
    // 0x8f6d38: ret
    //     0x8f6d38: ret             
    // 0x8f6d3c: d0 = 10000000.000000
    //     0x8f6d3c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27250] IMM: double(1e+07) from 0x416312d000000000
    //     0x8f6d40: ldr             d0, [x17, #0x250]
    // 0x8f6d44: fcmp            d0, d1
    // 0x8f6d48: b.le            #0x8f6d5c
    // 0x8f6d4c: r0 = 7
    //     0x8f6d4c: movz            x0, #0x7
    // 0x8f6d50: LeaveFrame
    //     0x8f6d50: mov             SP, fp
    //     0x8f6d54: ldp             fp, lr, [SP], #0x10
    // 0x8f6d58: ret
    //     0x8f6d58: ret             
    // 0x8f6d5c: d0 = 100000000.000000
    //     0x8f6d5c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27258] IMM: double(1e+08) from 0x4197d78400000000
    //     0x8f6d60: ldr             d0, [x17, #0x258]
    // 0x8f6d64: fcmp            d0, d1
    // 0x8f6d68: b.le            #0x8f6d7c
    // 0x8f6d6c: r0 = 8
    //     0x8f6d6c: movz            x0, #0x8
    // 0x8f6d70: LeaveFrame
    //     0x8f6d70: mov             SP, fp
    //     0x8f6d74: ldp             fp, lr, [SP], #0x10
    // 0x8f6d78: ret
    //     0x8f6d78: ret             
    // 0x8f6d7c: d0 = 1000000000.000000
    //     0x8f6d7c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27260] IMM: double(1e+09) from 0x41cdcd6500000000
    //     0x8f6d80: ldr             d0, [x17, #0x260]
    // 0x8f6d84: fcmp            d0, d1
    // 0x8f6d88: b.le            #0x8f6d9c
    // 0x8f6d8c: r0 = 9
    //     0x8f6d8c: movz            x0, #0x9
    // 0x8f6d90: LeaveFrame
    //     0x8f6d90: mov             SP, fp
    //     0x8f6d94: ldp             fp, lr, [SP], #0x10
    // 0x8f6d98: ret
    //     0x8f6d98: ret             
    // 0x8f6d9c: d0 = 10000000000.000000
    //     0x8f6d9c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27268] IMM: double(1e+10) from 0x4202a05f20000000
    //     0x8f6da0: ldr             d0, [x17, #0x268]
    // 0x8f6da4: fcmp            d0, d1
    // 0x8f6da8: b.le            #0x8f6dbc
    // 0x8f6dac: r0 = 10
    //     0x8f6dac: movz            x0, #0xa
    // 0x8f6db0: LeaveFrame
    //     0x8f6db0: mov             SP, fp
    //     0x8f6db4: ldp             fp, lr, [SP], #0x10
    // 0x8f6db8: ret
    //     0x8f6db8: ret             
    // 0x8f6dbc: d0 = 100000000000.000000
    //     0x8f6dbc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27270] IMM: double(1e+11) from 0x42374876e8000000
    //     0x8f6dc0: ldr             d0, [x17, #0x270]
    // 0x8f6dc4: fcmp            d0, d1
    // 0x8f6dc8: b.le            #0x8f6ddc
    // 0x8f6dcc: r0 = 11
    //     0x8f6dcc: movz            x0, #0xb
    // 0x8f6dd0: LeaveFrame
    //     0x8f6dd0: mov             SP, fp
    //     0x8f6dd4: ldp             fp, lr, [SP], #0x10
    // 0x8f6dd8: ret
    //     0x8f6dd8: ret             
    // 0x8f6ddc: d0 = 1000000000000.000000
    //     0x8f6ddc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27278] IMM: double(1e+12) from 0x426d1a94a2000000
    //     0x8f6de0: ldr             d0, [x17, #0x278]
    // 0x8f6de4: fcmp            d0, d1
    // 0x8f6de8: b.le            #0x8f6dfc
    // 0x8f6dec: r0 = 12
    //     0x8f6dec: movz            x0, #0xc
    // 0x8f6df0: LeaveFrame
    //     0x8f6df0: mov             SP, fp
    //     0x8f6df4: ldp             fp, lr, [SP], #0x10
    // 0x8f6df8: ret
    //     0x8f6df8: ret             
    // 0x8f6dfc: d0 = 10000000000000.000000
    //     0x8f6dfc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27280] IMM: double(1e+13) from 0x42a2309ce5400000
    //     0x8f6e00: ldr             d0, [x17, #0x280]
    // 0x8f6e04: fcmp            d0, d1
    // 0x8f6e08: b.le            #0x8f6e1c
    // 0x8f6e0c: r0 = 13
    //     0x8f6e0c: movz            x0, #0xd
    // 0x8f6e10: LeaveFrame
    //     0x8f6e10: mov             SP, fp
    //     0x8f6e14: ldp             fp, lr, [SP], #0x10
    // 0x8f6e18: ret
    //     0x8f6e18: ret             
    // 0x8f6e1c: d0 = 100000000000000.000000
    //     0x8f6e1c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27288] IMM: double(1e+14) from 0x42d6bcc41e900000
    //     0x8f6e20: ldr             d0, [x17, #0x288]
    // 0x8f6e24: fcmp            d0, d1
    // 0x8f6e28: b.le            #0x8f6e3c
    // 0x8f6e2c: r0 = 14
    //     0x8f6e2c: movz            x0, #0xe
    // 0x8f6e30: LeaveFrame
    //     0x8f6e30: mov             SP, fp
    //     0x8f6e34: ldp             fp, lr, [SP], #0x10
    // 0x8f6e38: ret
    //     0x8f6e38: ret             
    // 0x8f6e3c: d0 = 1000000000000000.000000
    //     0x8f6e3c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27290] IMM: double(1e+15) from 0x430c6bf526340000
    //     0x8f6e40: ldr             d0, [x17, #0x290]
    // 0x8f6e44: fcmp            d0, d1
    // 0x8f6e48: b.le            #0x8f6e5c
    // 0x8f6e4c: r0 = 15
    //     0x8f6e4c: movz            x0, #0xf
    // 0x8f6e50: LeaveFrame
    //     0x8f6e50: mov             SP, fp
    //     0x8f6e54: ldp             fp, lr, [SP], #0x10
    // 0x8f6e58: ret
    //     0x8f6e58: ret             
    // 0x8f6e5c: d0 = 10000000000000000.000000
    //     0x8f6e5c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27298] IMM: double(1e+16) from 0x4341c37937e08000
    //     0x8f6e60: ldr             d0, [x17, #0x298]
    // 0x8f6e64: fcmp            d0, d1
    // 0x8f6e68: b.le            #0x8f6e7c
    // 0x8f6e6c: r0 = 16
    //     0x8f6e6c: movz            x0, #0x10
    // 0x8f6e70: LeaveFrame
    //     0x8f6e70: mov             SP, fp
    //     0x8f6e74: ldp             fp, lr, [SP], #0x10
    // 0x8f6e78: ret
    //     0x8f6e78: ret             
    // 0x8f6e7c: mov             v0.16b, v1.16b
    // 0x8f6e80: stp             fp, lr, [SP, #-0x10]!
    // 0x8f6e84: mov             fp, SP
    // 0x8f6e88: CallRuntime_LibcLog(double) -> double
    //     0x8f6e88: and             SP, SP, #0xfffffffffffffff0
    //     0x8f6e8c: mov             sp, SP
    //     0x8f6e90: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8f6e94: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6e98: blr             x16
    //     0x8f6e9c: movz            x16, #0x8
    //     0x8f6ea0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f6ea4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f6ea8: sub             sp, x16, #1, lsl #12
    //     0x8f6eac: mov             SP, fp
    //     0x8f6eb0: ldp             fp, lr, [SP], #0x10
    // 0x8f6eb4: stur            d0, [fp, #-8]
    // 0x8f6eb8: r0 = InitLateStaticField(0x149c) // [package:intl/src/intl/number_format.dart] ::_ln10
    //     0x8f6eb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f6ebc: ldr             x0, [x0, #0x2938]
    //     0x8f6ec0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f6ec4: cmp             w0, w16
    //     0x8f6ec8: b.ne            #0x8f6ed8
    //     0x8f6ecc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27220] Field <::._ln10@1558441731>: static late final (offset: 0x149c)
    //     0x8f6ed0: ldr             x2, [x2, #0x220]
    //     0x8f6ed4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f6ed8: LoadField: d0 = r0->field_7
    //     0x8f6ed8: ldur            d0, [x0, #7]
    // 0x8f6edc: ldur            d1, [fp, #-8]
    // 0x8f6ee0: fdiv            d2, d1, d0
    // 0x8f6ee4: fcmp            d2, d2
    // 0x8f6ee8: b.vs            #0x8f6f74
    // 0x8f6eec: fcvtps          x1, d2
    // 0x8f6ef0: asr             x16, x1, #0x1e
    // 0x8f6ef4: cmp             x16, x1, asr #63
    // 0x8f6ef8: b.ne            #0x8f6f74
    // 0x8f6efc: lsl             x1, x1, #1
    // 0x8f6f00: r2 = LoadInt32Instr(r1)
    //     0x8f6f00: sbfx            x2, x1, #1, #0x1f
    //     0x8f6f04: tbz             w1, #0, #0x8f6f0c
    //     0x8f6f08: ldur            x2, [x1, #7]
    // 0x8f6f0c: cmp             x2, #1
    // 0x8f6f10: b.ge            #0x8f6f1c
    // 0x8f6f14: r1 = 2
    //     0x8f6f14: movz            x1, #0x2
    // 0x8f6f18: b               #0x8f6f54
    // 0x8f6f1c: cmp             x2, #1
    // 0x8f6f20: b.gt            #0x8f6f54
    // 0x8f6f24: r2 = 60
    //     0x8f6f24: movz            x2, #0x3c
    // 0x8f6f28: branchIfSmi(r1, 0x8f6f34)
    //     0x8f6f28: tbz             w1, #0, #0x8f6f34
    // 0x8f6f2c: r2 = LoadClassIdInstr(r1)
    //     0x8f6f2c: ldur            x2, [x1, #-1]
    //     0x8f6f30: ubfx            x2, x2, #0xc, #0x14
    // 0x8f6f34: cmp             x2, #0x3e
    // 0x8f6f38: b.ne            #0x8f6f50
    // 0x8f6f3c: LoadField: d0 = r1->field_7
    //     0x8f6f3c: ldur            d0, [x1, #7]
    // 0x8f6f40: fcmp            d0, d0
    // 0x8f6f44: b.vs            #0x8f6f54
    // 0x8f6f48: r1 = 2
    //     0x8f6f48: movz            x1, #0x2
    // 0x8f6f4c: b               #0x8f6f54
    // 0x8f6f50: r1 = 2
    //     0x8f6f50: movz            x1, #0x2
    // 0x8f6f54: r0 = LoadInt32Instr(r1)
    //     0x8f6f54: sbfx            x0, x1, #1, #0x1f
    //     0x8f6f58: tbz             w1, #0, #0x8f6f60
    //     0x8f6f5c: ldur            x0, [x1, #7]
    // 0x8f6f60: LeaveFrame
    //     0x8f6f60: mov             SP, fp
    //     0x8f6f64: ldp             fp, lr, [SP], #0x10
    // 0x8f6f68: ret
    //     0x8f6f68: ret             
    // 0x8f6f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f6f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f6f70: b               #0x8f6c40
    // 0x8f6f74: SaveReg d2
    //     0x8f6f74: str             q2, [SP, #-0x10]!
    // 0x8f6f78: d0 = 0.000000
    //     0x8f6f78: fmov            d0, d2
    // 0x8f6f7c: r0 = 64
    //     0x8f6f7c: movz            x0, #0x40
    // 0x8f6f80: r30 = DoubleToIntegerStub
    //     0x8f6f80: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f6f84: LoadField: r30 = r30->field_7
    //     0x8f6f84: ldur            lr, [lr, #7]
    // 0x8f6f88: blr             lr
    // 0x8f6f8c: mov             x1, x0
    // 0x8f6f90: RestoreReg d2
    //     0x8f6f90: ldr             q2, [SP], #0x10
    // 0x8f6f94: b               #0x8f6f00
  }
  _ _round(/* No info */) {
    // ** addr: 0x8f6f98, size: 0x228
    // 0x8f6f98: EnterFrame
    //     0x8f6f98: stp             fp, lr, [SP, #-0x10]!
    //     0x8f6f9c: mov             fp, SP
    // 0x8f6fa0: AllocStack(0x20)
    //     0x8f6fa0: sub             SP, SP, #0x20
    // 0x8f6fa4: SetupParameters(NumberFormat this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x8f6fa4: mov             x0, x1
    //     0x8f6fa8: stur            x1, [fp, #-0x10]
    //     0x8f6fac: mov             x1, x2
    //     0x8f6fb0: stur            x2, [fp, #-8]
    // 0x8f6fb4: CheckStackOverflow
    //     0x8f6fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f6fb8: cmp             SP, x16
    //     0x8f6fbc: b.ls            #0x8f719c
    // 0x8f6fc0: r2 = 60
    //     0x8f6fc0: movz            x2, #0x3c
    // 0x8f6fc4: branchIfSmi(r1, 0x8f6fd0)
    //     0x8f6fc4: tbz             w1, #0, #0x8f6fd0
    // 0x8f6fc8: r2 = LoadClassIdInstr(r1)
    //     0x8f6fc8: ldur            x2, [x1, #-1]
    //     0x8f6fcc: ubfx            x2, x2, #0xc, #0x14
    // 0x8f6fd0: sub             x16, x2, #0x3c
    // 0x8f6fd4: cmp             x16, #2
    // 0x8f6fd8: b.hi            #0x8f7070
    // 0x8f6fdc: r0 = 60
    //     0x8f6fdc: movz            x0, #0x3c
    // 0x8f6fe0: branchIfSmi(r1, 0x8f6fec)
    //     0x8f6fe0: tbz             w1, #0, #0x8f6fec
    // 0x8f6fe4: r0 = LoadClassIdInstr(r1)
    //     0x8f6fe4: ldur            x0, [x1, #-1]
    //     0x8f6fe8: ubfx            x0, x0, #0xc, #0x14
    // 0x8f6fec: str             x1, [SP]
    // 0x8f6ff0: r0 = GDT[cid_x0 + -0xfb5]()
    //     0x8f6ff0: sub             lr, x0, #0xfb5
    //     0x8f6ff4: ldr             lr, [x21, lr, lsl #3]
    //     0x8f6ff8: blr             lr
    // 0x8f6ffc: tbnz            w0, #4, #0x8f702c
    // 0x8f7000: r0 = InitLateStaticField(0x1494) // [package:intl/src/intl/number_format.dart] NumberFormat::_maxInt
    //     0x8f7000: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f7004: ldr             x0, [x0, #0x2928]
    //     0x8f7008: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f700c: cmp             w0, w16
    //     0x8f7010: b.ne            #0x8f7020
    //     0x8f7014: add             x2, PP, #0x27, lsl #12  ; [pp+0x27218] Field <NumberFormat._maxInt@1558441731>: static late final (offset: 0x1494)
    //     0x8f7018: ldr             x2, [x2, #0x218]
    //     0x8f701c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f7020: LeaveFrame
    //     0x8f7020: mov             SP, fp
    //     0x8f7024: ldp             fp, lr, [SP], #0x10
    // 0x8f7028: ret
    //     0x8f7028: ret             
    // 0x8f702c: ldur            x1, [fp, #-8]
    // 0x8f7030: r0 = 60
    //     0x8f7030: movz            x0, #0x3c
    // 0x8f7034: branchIfSmi(r1, 0x8f7040)
    //     0x8f7034: tbz             w1, #0, #0x8f7040
    // 0x8f7038: r0 = LoadClassIdInstr(r1)
    //     0x8f7038: ldur            x0, [x1, #-1]
    //     0x8f703c: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7040: r0 = GDT[cid_x0 + -0xfbe]()
    //     0x8f7040: sub             lr, x0, #0xfbe
    //     0x8f7044: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7048: blr             lr
    // 0x8f704c: mov             x2, x0
    // 0x8f7050: r0 = BoxInt64Instr(r2)
    //     0x8f7050: sbfiz           x0, x2, #1, #0x1f
    //     0x8f7054: cmp             x2, x0, asr #1
    //     0x8f7058: b.eq            #0x8f7064
    //     0x8f705c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f7060: stur            x2, [x0, #7]
    // 0x8f7064: LeaveFrame
    //     0x8f7064: mov             SP, fp
    //     0x8f7068: ldp             fp, lr, [SP], #0x10
    // 0x8f706c: ret
    //     0x8f706c: ret             
    // 0x8f7070: r16 = 2
    //     0x8f7070: movz            x16, #0x2
    // 0x8f7074: stp             x16, x1, [SP]
    // 0x8f7078: r4 = 0
    //     0x8f7078: movz            x4, #0
    // 0x8f707c: ldr             x0, [SP, #8]
    // 0x8f7080: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7080: add             x16, PP, #0x27, lsl #12  ; [pp+0x272a0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7084: add             x16, x16, #0x2a0
    // 0x8f7088: ldp             x5, lr, [x16]
    // 0x8f708c: blr             lr
    // 0x8f7090: r1 = 60
    //     0x8f7090: movz            x1, #0x3c
    // 0x8f7094: branchIfSmi(r0, 0x8f70a0)
    //     0x8f7094: tbz             w0, #0, #0x8f70a0
    // 0x8f7098: r1 = LoadClassIdInstr(r0)
    //     0x8f7098: ldur            x1, [x0, #-1]
    //     0x8f709c: ubfx            x1, x1, #0xc, #0x14
    // 0x8f70a0: stp             xzr, x0, [SP]
    // 0x8f70a4: mov             x0, x1
    // 0x8f70a8: mov             lr, x0
    // 0x8f70ac: ldr             lr, [x21, lr, lsl #3]
    // 0x8f70b0: blr             lr
    // 0x8f70b4: tbnz            w0, #4, #0x8f70c8
    // 0x8f70b8: ldur            x0, [fp, #-8]
    // 0x8f70bc: LeaveFrame
    //     0x8f70bc: mov             SP, fp
    //     0x8f70c0: ldp             fp, lr, [SP], #0x10
    // 0x8f70c4: ret
    //     0x8f70c4: ret             
    // 0x8f70c8: ldur            x1, [fp, #-0x10]
    // 0x8f70cc: ldur            x2, [fp, #-8]
    // 0x8f70d0: r0 = _floor()
    //     0x8f70d0: bl              #0x8f71c0  ; [package:intl/src/intl/number_format.dart] NumberFormat::_floor
    // 0x8f70d4: ldur            x16, [fp, #-8]
    // 0x8f70d8: stp             x0, x16, [SP]
    // 0x8f70dc: r4 = 0
    //     0x8f70dc: movz            x4, #0
    // 0x8f70e0: ldr             x0, [SP, #8]
    // 0x8f70e4: r16 = UnlinkedCall_0x5f3c08
    //     0x8f70e4: add             x16, PP, #0x27, lsl #12  ; [pp+0x272b0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f70e8: add             x16, x16, #0x2b0
    // 0x8f70ec: ldp             x5, lr, [x16]
    // 0x8f70f0: blr             lr
    // 0x8f70f4: str             x0, [SP]
    // 0x8f70f8: r4 = 0
    //     0x8f70f8: movz            x4, #0
    // 0x8f70fc: ldr             x0, [SP]
    // 0x8f7100: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7100: add             x16, PP, #0x27, lsl #12  ; [pp+0x272c0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7104: add             x16, x16, #0x2c0
    // 0x8f7108: ldp             x5, lr, [x16]
    // 0x8f710c: blr             lr
    // 0x8f7110: LoadField: d0 = r0->field_7
    //     0x8f7110: ldur            d0, [x0, #7]
    // 0x8f7114: stp             fp, lr, [SP, #-0x10]!
    // 0x8f7118: mov             fp, SP
    // 0x8f711c: CallRuntime_LibcRound(double) -> double
    //     0x8f711c: and             SP, SP, #0xfffffffffffffff0
    //     0x8f7120: mov             sp, SP
    //     0x8f7124: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x8f7128: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f712c: blr             x16
    //     0x8f7130: movz            x16, #0x8
    //     0x8f7134: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f7138: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f713c: sub             sp, x16, #1, lsl #12
    //     0x8f7140: mov             SP, fp
    //     0x8f7144: ldp             fp, lr, [SP], #0x10
    // 0x8f7148: fcmp            d0, d0
    // 0x8f714c: b.vs            #0x8f71a4
    // 0x8f7150: fcvtzs          x0, d0
    // 0x8f7154: asr             x16, x0, #0x1e
    // 0x8f7158: cmp             x16, x0, asr #63
    // 0x8f715c: b.ne            #0x8f71a4
    // 0x8f7160: lsl             x0, x0, #1
    // 0x8f7164: cbnz            w0, #0x8f7170
    // 0x8f7168: ldur            x0, [fp, #-8]
    // 0x8f716c: b               #0x8f7190
    // 0x8f7170: ldur            x16, [fp, #-8]
    // 0x8f7174: stp             x0, x16, [SP]
    // 0x8f7178: r4 = 0
    //     0x8f7178: movz            x4, #0
    // 0x8f717c: ldr             x0, [SP, #8]
    // 0x8f7180: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7180: add             x16, PP, #0x27, lsl #12  ; [pp+0x272d0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7184: add             x16, x16, #0x2d0
    // 0x8f7188: ldp             x5, lr, [x16]
    // 0x8f718c: blr             lr
    // 0x8f7190: LeaveFrame
    //     0x8f7190: mov             SP, fp
    //     0x8f7194: ldp             fp, lr, [SP], #0x10
    // 0x8f7198: ret
    //     0x8f7198: ret             
    // 0x8f719c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f719c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f71a0: b               #0x8f6fc0
    // 0x8f71a4: SaveReg d0
    //     0x8f71a4: str             q0, [SP, #-0x10]!
    // 0x8f71a8: r0 = 74
    //     0x8f71a8: movz            x0, #0x4a
    // 0x8f71ac: r30 = DoubleToIntegerStub
    //     0x8f71ac: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f71b0: LoadField: r30 = r30->field_7
    //     0x8f71b0: ldur            lr, [lr, #7]
    // 0x8f71b4: blr             lr
    // 0x8f71b8: RestoreReg d0
    //     0x8f71b8: ldr             q0, [SP], #0x10
    // 0x8f71bc: b               #0x8f7164
  }
  _ _floor(/* No info */) {
    // ** addr: 0x8f71c0, size: 0x14c
    // 0x8f71c0: EnterFrame
    //     0x8f71c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f71c4: mov             fp, SP
    // 0x8f71c8: AllocStack(0x18)
    //     0x8f71c8: sub             SP, SP, #0x18
    // 0x8f71cc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8f71cc: stur            x2, [fp, #-8]
    // 0x8f71d0: CheckStackOverflow
    //     0x8f71d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f71d4: cmp             SP, x16
    //     0x8f71d8: b.ls            #0x8f7304
    // 0x8f71dc: str             x2, [SP]
    // 0x8f71e0: r4 = 0
    //     0x8f71e0: movz            x4, #0
    // 0x8f71e4: ldr             x0, [SP]
    // 0x8f71e8: r16 = UnlinkedCall_0x5f3c08
    //     0x8f71e8: add             x16, PP, #0x27, lsl #12  ; [pp+0x272e0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f71ec: add             x16, x16, #0x2e0
    // 0x8f71f0: ldp             x5, lr, [x16]
    // 0x8f71f4: blr             lr
    // 0x8f71f8: tbnz            w0, #4, #0x8f723c
    // 0x8f71fc: ldur            x16, [fp, #-8]
    // 0x8f7200: str             x16, [SP]
    // 0x8f7204: r4 = 0
    //     0x8f7204: movz            x4, #0
    // 0x8f7208: ldr             x0, [SP]
    // 0x8f720c: r16 = UnlinkedCall_0x5f3c08
    //     0x8f720c: add             x16, PP, #0x27, lsl #12  ; [pp+0x272f0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7210: add             x16, x16, #0x2f0
    // 0x8f7214: ldp             x5, lr, [x16]
    // 0x8f7218: blr             lr
    // 0x8f721c: str             x0, [SP]
    // 0x8f7220: r4 = 0
    //     0x8f7220: movz            x4, #0
    // 0x8f7224: ldr             x0, [SP]
    // 0x8f7228: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7228: add             x16, PP, #0x27, lsl #12  ; [pp+0x27300] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f722c: add             x16, x16, #0x300
    // 0x8f7230: ldp             x5, lr, [x16]
    // 0x8f7234: blr             lr
    // 0x8f7238: tbnz            w0, #4, #0x8f72b0
    // 0x8f723c: ldur            x0, [fp, #-8]
    // 0x8f7240: r1 = 60
    //     0x8f7240: movz            x1, #0x3c
    // 0x8f7244: branchIfSmi(r0, 0x8f7250)
    //     0x8f7244: tbz             w0, #0, #0x8f7250
    // 0x8f7248: r1 = LoadClassIdInstr(r0)
    //     0x8f7248: ldur            x1, [x0, #-1]
    //     0x8f724c: ubfx            x1, x1, #0xc, #0x14
    // 0x8f7250: sub             x16, x1, #0x3c
    // 0x8f7254: cmp             x16, #2
    // 0x8f7258: b.hi            #0x8f7284
    // 0x8f725c: r1 = 60
    //     0x8f725c: movz            x1, #0x3c
    // 0x8f7260: branchIfSmi(r0, 0x8f726c)
    //     0x8f7260: tbz             w0, #0, #0x8f726c
    // 0x8f7264: r1 = LoadClassIdInstr(r0)
    //     0x8f7264: ldur            x1, [x0, #-1]
    //     0x8f7268: ubfx            x1, x1, #0xc, #0x14
    // 0x8f726c: str             x0, [SP]
    // 0x8f7270: mov             x0, x1
    // 0x8f7274: r0 = GDT[cid_x0 + -0xfc9]()
    //     0x8f7274: sub             lr, x0, #0xfc9
    //     0x8f7278: ldr             lr, [x21, lr, lsl #3]
    //     0x8f727c: blr             lr
    // 0x8f7280: b               #0x8f72a4
    // 0x8f7284: r16 = 2
    //     0x8f7284: movz            x16, #0x2
    // 0x8f7288: stp             x16, x0, [SP]
    // 0x8f728c: r4 = 0
    //     0x8f728c: movz            x4, #0
    // 0x8f7290: ldr             x0, [SP, #8]
    // 0x8f7294: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7294: add             x16, PP, #0x27, lsl #12  ; [pp+0x27310] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7298: add             x16, x16, #0x310
    // 0x8f729c: ldp             x5, lr, [x16]
    // 0x8f72a0: blr             lr
    // 0x8f72a4: LeaveFrame
    //     0x8f72a4: mov             SP, fp
    //     0x8f72a8: ldp             fp, lr, [SP], #0x10
    // 0x8f72ac: ret
    //     0x8f72ac: ret             
    // 0x8f72b0: ldur            x0, [fp, #-8]
    // 0x8f72b4: r1 = Null
    //     0x8f72b4: mov             x1, NULL
    // 0x8f72b8: r2 = 4
    //     0x8f72b8: movz            x2, #0x4
    // 0x8f72bc: r0 = AllocateArray()
    //     0x8f72bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8f72c0: r16 = "Internal error: expected positive number, got "
    //     0x8f72c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27320] "Internal error: expected positive number, got "
    //     0x8f72c4: ldr             x16, [x16, #0x320]
    // 0x8f72c8: StoreField: r0->field_f = r16
    //     0x8f72c8: stur            w16, [x0, #0xf]
    // 0x8f72cc: ldur            x1, [fp, #-8]
    // 0x8f72d0: StoreField: r0->field_13 = r1
    //     0x8f72d0: stur            w1, [x0, #0x13]
    // 0x8f72d4: str             x0, [SP]
    // 0x8f72d8: r0 = _interpolate()
    //     0x8f72d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8f72dc: stur            x0, [fp, #-8]
    // 0x8f72e0: r0 = ArgumentError()
    //     0x8f72e0: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8f72e4: mov             x1, x0
    // 0x8f72e8: ldur            x0, [fp, #-8]
    // 0x8f72ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x8f72ec: stur            w0, [x1, #0x17]
    // 0x8f72f0: r0 = false
    //     0x8f72f0: add             x0, NULL, #0x30  ; false
    // 0x8f72f4: StoreField: r1->field_b = r0
    //     0x8f72f4: stur            w0, [x1, #0xb]
    // 0x8f72f8: mov             x0, x1
    // 0x8f72fc: r0 = Throw()
    //     0x8f72fc: bl              #0xec04b8  ; ThrowStub
    // 0x8f7300: brk             #0
    // 0x8f7304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7308: b               #0x8f71dc
  }
  _ _formatExponential(/* No info */) {
    // ** addr: 0x8f730c, size: 0x61c
    // 0x8f730c: EnterFrame
    //     0x8f730c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7310: mov             fp, SP
    // 0x8f7314: AllocStack(0x40)
    //     0x8f7314: sub             SP, SP, #0x40
    // 0x8f7318: SetupParameters(NumberFormat this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8f7318: stur            x1, [fp, #-8]
    //     0x8f731c: stur            x2, [fp, #-0x10]
    // 0x8f7320: CheckStackOverflow
    //     0x8f7320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7324: cmp             SP, x16
    //     0x8f7328: b.ls            #0x8f787c
    // 0x8f732c: r0 = 60
    //     0x8f732c: movz            x0, #0x3c
    // 0x8f7330: branchIfSmi(r2, 0x8f733c)
    //     0x8f7330: tbz             w2, #0, #0x8f733c
    // 0x8f7334: r0 = LoadClassIdInstr(r2)
    //     0x8f7334: ldur            x0, [x2, #-1]
    //     0x8f7338: ubfx            x0, x0, #0xc, #0x14
    // 0x8f733c: r16 = 0.000000
    //     0x8f733c: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0x8f7340: stp             x16, x2, [SP]
    // 0x8f7344: mov             lr, x0
    // 0x8f7348: ldr             lr, [x21, lr, lsl #3]
    // 0x8f734c: blr             lr
    // 0x8f7350: tbnz            w0, #4, #0x8f737c
    // 0x8f7354: ldur            x1, [fp, #-8]
    // 0x8f7358: ldur            x2, [fp, #-0x10]
    // 0x8f735c: r0 = _formatFixed()
    //     0x8f735c: bl              #0x8f58e4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatFixed
    // 0x8f7360: ldur            x1, [fp, #-8]
    // 0x8f7364: r2 = 0
    //     0x8f7364: movz            x2, #0
    // 0x8f7368: r0 = _formatExponent()
    //     0x8f7368: bl              #0x8f7928  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatExponent
    // 0x8f736c: r0 = Null
    //     0x8f736c: mov             x0, NULL
    // 0x8f7370: LeaveFrame
    //     0x8f7370: mov             SP, fp
    //     0x8f7374: ldp             fp, lr, [SP], #0x10
    // 0x8f7378: ret
    //     0x8f7378: ret             
    // 0x8f737c: ldur            x2, [fp, #-8]
    // 0x8f7380: ldur            x1, [fp, #-0x10]
    // 0x8f7384: r0 = 60
    //     0x8f7384: movz            x0, #0x3c
    // 0x8f7388: branchIfSmi(r1, 0x8f7394)
    //     0x8f7388: tbz             w1, #0, #0x8f7394
    // 0x8f738c: r0 = LoadClassIdInstr(r1)
    //     0x8f738c: ldur            x0, [x1, #-1]
    //     0x8f7390: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7394: str             x1, [SP]
    // 0x8f7398: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f7398: sub             lr, x0, #0xffa
    //     0x8f739c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f73a0: blr             lr
    // 0x8f73a4: LoadField: d0 = r0->field_7
    //     0x8f73a4: ldur            d0, [x0, #7]
    // 0x8f73a8: stp             fp, lr, [SP, #-0x10]!
    // 0x8f73ac: mov             fp, SP
    // 0x8f73b0: CallRuntime_LibcLog(double) -> double
    //     0x8f73b0: and             SP, SP, #0xfffffffffffffff0
    //     0x8f73b4: mov             sp, SP
    //     0x8f73b8: ldr             x16, [THR, #0x5e0]  ; THR::LibcLog
    //     0x8f73bc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f73c0: blr             x16
    //     0x8f73c4: movz            x16, #0x8
    //     0x8f73c8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f73cc: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f73d0: sub             sp, x16, #1, lsl #12
    //     0x8f73d4: mov             SP, fp
    //     0x8f73d8: ldp             fp, lr, [SP], #0x10
    // 0x8f73dc: stur            d0, [fp, #-0x30]
    // 0x8f73e0: r0 = InitLateStaticField(0x149c) // [package:intl/src/intl/number_format.dart] ::_ln10
    //     0x8f73e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f73e4: ldr             x0, [x0, #0x2938]
    //     0x8f73e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f73ec: cmp             w0, w16
    //     0x8f73f0: b.ne            #0x8f7400
    //     0x8f73f4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27220] Field <::._ln10@1558441731>: static late final (offset: 0x149c)
    //     0x8f73f8: ldr             x2, [x2, #0x220]
    //     0x8f73fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f7400: LoadField: d0 = r0->field_7
    //     0x8f7400: ldur            d0, [x0, #7]
    // 0x8f7404: ldur            d1, [fp, #-0x30]
    // 0x8f7408: fdiv            d2, d1, d0
    // 0x8f740c: fcmp            d2, d2
    // 0x8f7410: b.vs            #0x8f7884
    // 0x8f7414: fcvtms          x1, d2
    // 0x8f7418: asr             x16, x1, #0x1e
    // 0x8f741c: cmp             x16, x1, asr #63
    // 0x8f7420: b.ne            #0x8f7884
    // 0x8f7424: lsl             x1, x1, #1
    // 0x8f7428: stur            x1, [fp, #-0x18]
    // 0x8f742c: r0 = 60
    //     0x8f742c: movz            x0, #0x3c
    // 0x8f7430: branchIfSmi(r1, 0x8f743c)
    //     0x8f7430: tbz             w1, #0, #0x8f743c
    // 0x8f7434: r0 = LoadClassIdInstr(r1)
    //     0x8f7434: ldur            x0, [x1, #-1]
    //     0x8f7438: ubfx            x0, x0, #0xc, #0x14
    // 0x8f743c: str             x1, [SP]
    // 0x8f7440: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f7440: sub             lr, x0, #0xffa
    //     0x8f7444: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7448: blr             lr
    // 0x8f744c: LoadField: d1 = r0->field_7
    //     0x8f744c: ldur            d1, [x0, #7]
    // 0x8f7450: d0 = 10.000000
    //     0x8f7450: fmov            d0, #10.00000000
    // 0x8f7454: d30 = 0.000000
    //     0x8f7454: fmov            d30, d0
    // 0x8f7458: d0 = 1.000000
    //     0x8f7458: fmov            d0, #1.00000000
    // 0x8f745c: fcmp            d1, #0.0
    // 0x8f7460: b.vs            #0x8f74a4
    // 0x8f7464: b.eq            #0x8f7528
    // 0x8f7468: fcmp            d1, d0
    // 0x8f746c: b.eq            #0x8f7494
    // 0x8f7470: d31 = 2.000000
    //     0x8f7470: fmov            d31, #2.00000000
    // 0x8f7474: fcmp            d1, d31
    // 0x8f7478: b.eq            #0x8f749c
    // 0x8f747c: d31 = 3.000000
    //     0x8f747c: fmov            d31, #3.00000000
    // 0x8f7480: fcmp            d1, d31
    // 0x8f7484: b.ne            #0x8f74a4
    // 0x8f7488: fmul            d0, d30, d30
    // 0x8f748c: fmul            d0, d0, d30
    // 0x8f7490: b               #0x8f7528
    // 0x8f7494: d0 = 0.000000
    //     0x8f7494: fmov            d0, d30
    // 0x8f7498: b               #0x8f7528
    // 0x8f749c: fmul            d0, d30, d30
    // 0x8f74a0: b               #0x8f7528
    // 0x8f74a4: fcmp            d30, d0
    // 0x8f74a8: b.vs            #0x8f74b8
    // 0x8f74ac: b.eq            #0x8f7528
    // 0x8f74b0: fcmp            d30, d1
    // 0x8f74b4: b.vc            #0x8f74c0
    // 0x8f74b8: d0 = nan
    //     0x8f74b8: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0x8f74bc: b               #0x8f7528
    // 0x8f74c0: d0 = -inf
    //     0x8f74c0: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x8f74c4: fcmp            d30, d0
    // 0x8f74c8: b.eq            #0x8f74f0
    // 0x8f74cc: d0 = 0.500000
    //     0x8f74cc: fmov            d0, #0.50000000
    // 0x8f74d0: fcmp            d1, d0
    // 0x8f74d4: b.ne            #0x8f74f0
    // 0x8f74d8: fcmp            d30, #0.0
    // 0x8f74dc: b.eq            #0x8f74e8
    // 0x8f74e0: fsqrt           d0, d30
    // 0x8f74e4: b               #0x8f7528
    // 0x8f74e8: d0 = 0.000000
    //     0x8f74e8: eor             v0.16b, v0.16b, v0.16b
    // 0x8f74ec: b               #0x8f7528
    // 0x8f74f0: d0 = 0.000000
    //     0x8f74f0: fmov            d0, d30
    // 0x8f74f4: stp             fp, lr, [SP, #-0x10]!
    // 0x8f74f8: mov             fp, SP
    // 0x8f74fc: CallRuntime_LibcPow(double, double) -> double
    //     0x8f74fc: and             SP, SP, #0xfffffffffffffff0
    //     0x8f7500: mov             sp, SP
    //     0x8f7504: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0x8f7508: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f750c: blr             x16
    //     0x8f7510: movz            x16, #0x8
    //     0x8f7514: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f7518: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f751c: sub             sp, x16, #1, lsl #12
    //     0x8f7520: mov             SP, fp
    //     0x8f7524: ldp             fp, lr, [SP], #0x10
    // 0x8f7528: r0 = inline_Allocate_Double()
    //     0x8f7528: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8f752c: add             x0, x0, #0x10
    //     0x8f7530: cmp             x1, x0
    //     0x8f7534: b.ls            #0x8f78a8
    //     0x8f7538: str             x0, [THR, #0x50]  ; THR::top
    //     0x8f753c: sub             x0, x0, #0xf
    //     0x8f7540: movz            x1, #0xe15c
    //     0x8f7544: movk            x1, #0x3, lsl #16
    //     0x8f7548: stur            x1, [x0, #-1]
    // 0x8f754c: StoreField: r0->field_7 = d0
    //     0x8f754c: stur            d0, [x0, #7]
    // 0x8f7550: ldur            x1, [fp, #-0x10]
    // 0x8f7554: r2 = 60
    //     0x8f7554: movz            x2, #0x3c
    // 0x8f7558: branchIfSmi(r1, 0x8f7564)
    //     0x8f7558: tbz             w1, #0, #0x8f7564
    // 0x8f755c: r2 = LoadClassIdInstr(r1)
    //     0x8f755c: ldur            x2, [x1, #-1]
    //     0x8f7560: ubfx            x2, x2, #0xc, #0x14
    // 0x8f7564: stp             x0, x1, [SP]
    // 0x8f7568: mov             x0, x2
    // 0x8f756c: r0 = GDT[cid_x0 + -0xff7]()
    //     0x8f756c: sub             lr, x0, #0xff7
    //     0x8f7570: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7574: blr             lr
    // 0x8f7578: mov             x3, x0
    // 0x8f757c: ldur            x2, [fp, #-8]
    // 0x8f7580: stur            x3, [fp, #-0x28]
    // 0x8f7584: LoadField: r0 = r2->field_33
    //     0x8f7584: ldur            x0, [x2, #0x33]
    // 0x8f7588: cmp             x0, #1
    // 0x8f758c: b.le            #0x8f760c
    // 0x8f7590: LoadField: r1 = r2->field_3b
    //     0x8f7590: ldur            x1, [x2, #0x3b]
    // 0x8f7594: cmp             x0, x1
    // 0x8f7598: b.le            #0x8f7600
    // 0x8f759c: ldur            x1, [fp, #-0x18]
    // 0x8f75a0: r4 = LoadInt32Instr(r1)
    //     0x8f75a0: sbfx            x4, x1, #1, #0x1f
    //     0x8f75a4: tbz             w1, #0, #0x8f75ac
    //     0x8f75a8: ldur            x4, [x1, #7]
    // 0x8f75ac: LoadField: d0 = r3->field_7
    //     0x8f75ac: ldur            d0, [x3, #7]
    // 0x8f75b0: mov             x1, x4
    // 0x8f75b4: mov             v1.16b, v0.16b
    // 0x8f75b8: d0 = 10.000000
    //     0x8f75b8: fmov            d0, #10.00000000
    // 0x8f75bc: CheckStackOverflow
    //     0x8f75bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f75c0: cmp             SP, x16
    //     0x8f75c4: b.ls            #0x8f78b8
    // 0x8f75c8: cbz             x0, #0x8f78c0
    // 0x8f75cc: sdiv            x4, x1, x0
    // 0x8f75d0: msub            x3, x4, x0, x1
    // 0x8f75d4: cmp             x3, xzr
    // 0x8f75d8: b.lt            #0x8f78e0
    // 0x8f75dc: cbz             x3, #0x8f75f4
    // 0x8f75e0: fmul            d2, d1, d0
    // 0x8f75e4: sub             x3, x1, #1
    // 0x8f75e8: mov             x1, x3
    // 0x8f75ec: mov             v1.16b, v2.16b
    // 0x8f75f0: b               #0x8f75bc
    // 0x8f75f4: mov             x0, x1
    // 0x8f75f8: mov             v0.16b, v1.16b
    // 0x8f75fc: b               #0x8f782c
    // 0x8f7600: ldur            x1, [fp, #-0x18]
    // 0x8f7604: d0 = 10.000000
    //     0x8f7604: fmov            d0, #10.00000000
    // 0x8f7608: b               #0x8f7614
    // 0x8f760c: ldur            x1, [fp, #-0x18]
    // 0x8f7610: d0 = 10.000000
    //     0x8f7610: fmov            d0, #10.00000000
    // 0x8f7614: LoadField: r0 = r2->field_3b
    //     0x8f7614: ldur            x0, [x2, #0x3b]
    // 0x8f7618: cmp             x0, #1
    // 0x8f761c: b.ge            #0x8f7644
    // 0x8f7620: r0 = LoadInt32Instr(r1)
    //     0x8f7620: sbfx            x0, x1, #1, #0x1f
    //     0x8f7624: tbz             w1, #0, #0x8f762c
    //     0x8f7628: ldur            x0, [x1, #7]
    // 0x8f762c: add             x1, x0, #1
    // 0x8f7630: LoadField: d1 = r3->field_7
    //     0x8f7630: ldur            d1, [x3, #7]
    // 0x8f7634: fdiv            d2, d1, d0
    // 0x8f7638: mov             x0, x1
    // 0x8f763c: mov             v0.16b, v2.16b
    // 0x8f7640: b               #0x8f782c
    // 0x8f7644: sub             x4, x0, #1
    // 0x8f7648: r0 = LoadInt32Instr(r1)
    //     0x8f7648: sbfx            x0, x1, #1, #0x1f
    //     0x8f764c: tbz             w1, #0, #0x8f7654
    //     0x8f7650: ldur            x0, [x1, #7]
    // 0x8f7654: sub             x5, x0, x4
    // 0x8f7658: stur            x5, [fp, #-0x20]
    // 0x8f765c: r0 = BoxInt64Instr(r4)
    //     0x8f765c: sbfiz           x0, x4, #1, #0x1f
    //     0x8f7660: cmp             x4, x0, asr #1
    //     0x8f7664: b.eq            #0x8f7670
    //     0x8f7668: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f766c: stur            x4, [x0, #7]
    // 0x8f7670: stur            x0, [fp, #-0x10]
    // 0x8f7674: tbnz            x4, #0x3f, #0x8f76d0
    // 0x8f7678: mov             x0, x4
    // 0x8f767c: r1 = 10
    //     0x8f767c: movz            x1, #0xa
    // 0x8f7680: r4 = 1
    //     0x8f7680: movz            x4, #0x1
    // 0x8f7684: CheckStackOverflow
    //     0x8f7684: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7688: cmp             SP, x16
    //     0x8f768c: b.ls            #0x8f78f4
    // 0x8f7690: cbz             x0, #0x8f76b8
    // 0x8f7694: branchIfSmi(r0, 0x8f76a0)
    //     0x8f7694: tbz             w0, #0, #0x8f76a0
    // 0x8f7698: mul             x6, x4, x1
    // 0x8f769c: mov             x4, x6
    // 0x8f76a0: asr             x6, x0, #1
    // 0x8f76a4: cbz             x6, #0x8f76b0
    // 0x8f76a8: mul             x7, x1, x1
    // 0x8f76ac: mov             x1, x7
    // 0x8f76b0: mov             x0, x6
    // 0x8f76b4: b               #0x8f7684
    // 0x8f76b8: r0 = BoxInt64Instr(r4)
    //     0x8f76b8: sbfiz           x0, x4, #1, #0x1f
    //     0x8f76bc: cmp             x4, x0, asr #1
    //     0x8f76c0: b.eq            #0x8f76cc
    //     0x8f76c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f76c8: stur            x4, [x0, #7]
    // 0x8f76cc: b               #0x8f7818
    // 0x8f76d0: r16 = 20
    //     0x8f76d0: movz            x16, #0x14
    // 0x8f76d4: stp             x16, NULL, [SP]
    // 0x8f76d8: r0 = _Double.fromInteger()
    //     0x8f76d8: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x8f76dc: mov             x1, x0
    // 0x8f76e0: ldur            x0, [fp, #-0x10]
    // 0x8f76e4: stur            x1, [fp, #-0x18]
    // 0x8f76e8: r2 = 60
    //     0x8f76e8: movz            x2, #0x3c
    // 0x8f76ec: branchIfSmi(r0, 0x8f76f8)
    //     0x8f76ec: tbz             w0, #0, #0x8f76f8
    // 0x8f76f0: r2 = LoadClassIdInstr(r0)
    //     0x8f76f0: ldur            x2, [x0, #-1]
    //     0x8f76f4: ubfx            x2, x2, #0xc, #0x14
    // 0x8f76f8: str             x0, [SP]
    // 0x8f76fc: mov             x0, x2
    // 0x8f7700: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8f7700: sub             lr, x0, #0xffa
    //     0x8f7704: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7708: blr             lr
    // 0x8f770c: mov             x1, x0
    // 0x8f7710: ldur            x0, [fp, #-0x18]
    // 0x8f7714: LoadField: d0 = r0->field_7
    //     0x8f7714: ldur            d0, [x0, #7]
    // 0x8f7718: LoadField: d1 = r1->field_7
    //     0x8f7718: ldur            d1, [x1, #7]
    // 0x8f771c: d30 = 0.000000
    //     0x8f771c: fmov            d30, d0
    // 0x8f7720: d0 = 1.000000
    //     0x8f7720: fmov            d0, #1.00000000
    // 0x8f7724: fcmp            d1, #0.0
    // 0x8f7728: b.vs            #0x8f776c
    // 0x8f772c: b.eq            #0x8f77f0
    // 0x8f7730: fcmp            d1, d0
    // 0x8f7734: b.eq            #0x8f775c
    // 0x8f7738: d31 = 2.000000
    //     0x8f7738: fmov            d31, #2.00000000
    // 0x8f773c: fcmp            d1, d31
    // 0x8f7740: b.eq            #0x8f7764
    // 0x8f7744: d31 = 3.000000
    //     0x8f7744: fmov            d31, #3.00000000
    // 0x8f7748: fcmp            d1, d31
    // 0x8f774c: b.ne            #0x8f776c
    // 0x8f7750: fmul            d0, d30, d30
    // 0x8f7754: fmul            d0, d0, d30
    // 0x8f7758: b               #0x8f77f0
    // 0x8f775c: d0 = 0.000000
    //     0x8f775c: fmov            d0, d30
    // 0x8f7760: b               #0x8f77f0
    // 0x8f7764: fmul            d0, d30, d30
    // 0x8f7768: b               #0x8f77f0
    // 0x8f776c: fcmp            d30, d0
    // 0x8f7770: b.vs            #0x8f7780
    // 0x8f7774: b.eq            #0x8f77f0
    // 0x8f7778: fcmp            d30, d1
    // 0x8f777c: b.vc            #0x8f7788
    // 0x8f7780: d0 = nan
    //     0x8f7780: ldr             d0, [PP, #0x5950]  ; [pp+0x5950] IMM: double(nan) from 0x7ff8000000000000
    // 0x8f7784: b               #0x8f77f0
    // 0x8f7788: d0 = -inf
    //     0x8f7788: ldr             d0, [PP, #0x1370]  ; [pp+0x1370] IMM: double(-inf) from 0xfff0000000000000
    // 0x8f778c: fcmp            d30, d0
    // 0x8f7790: b.eq            #0x8f77b8
    // 0x8f7794: d0 = 0.500000
    //     0x8f7794: fmov            d0, #0.50000000
    // 0x8f7798: fcmp            d1, d0
    // 0x8f779c: b.ne            #0x8f77b8
    // 0x8f77a0: fcmp            d30, #0.0
    // 0x8f77a4: b.eq            #0x8f77b0
    // 0x8f77a8: fsqrt           d0, d30
    // 0x8f77ac: b               #0x8f77f0
    // 0x8f77b0: d0 = 0.000000
    //     0x8f77b0: eor             v0.16b, v0.16b, v0.16b
    // 0x8f77b4: b               #0x8f77f0
    // 0x8f77b8: d0 = 0.000000
    //     0x8f77b8: fmov            d0, d30
    // 0x8f77bc: stp             fp, lr, [SP, #-0x10]!
    // 0x8f77c0: mov             fp, SP
    // 0x8f77c4: CallRuntime_LibcPow(double, double) -> double
    //     0x8f77c4: and             SP, SP, #0xfffffffffffffff0
    //     0x8f77c8: mov             sp, SP
    //     0x8f77cc: ldr             x16, [THR, #0x568]  ; THR::LibcPow
    //     0x8f77d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f77d4: blr             x16
    //     0x8f77d8: movz            x16, #0x8
    //     0x8f77dc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8f77e0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8f77e4: sub             sp, x16, #1, lsl #12
    //     0x8f77e8: mov             SP, fp
    //     0x8f77ec: ldp             fp, lr, [SP], #0x10
    // 0x8f77f0: r0 = inline_Allocate_Double()
    //     0x8f77f0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8f77f4: add             x0, x0, #0x10
    //     0x8f77f8: cmp             x1, x0
    //     0x8f77fc: b.ls            #0x8f78fc
    //     0x8f7800: str             x0, [THR, #0x50]  ; THR::top
    //     0x8f7804: sub             x0, x0, #0xf
    //     0x8f7808: movz            x1, #0xe15c
    //     0x8f780c: movk            x1, #0x3, lsl #16
    //     0x8f7810: stur            x1, [x0, #-1]
    // 0x8f7814: StoreField: r0->field_7 = d0
    //     0x8f7814: stur            d0, [x0, #7]
    // 0x8f7818: ldur            x16, [fp, #-0x28]
    // 0x8f781c: stp             x0, x16, [SP]
    // 0x8f7820: r0 = *()
    //     0x8f7820: bl              #0xebfc6c  ; [dart:core] _Double::*
    // 0x8f7824: LoadField: d0 = r0->field_7
    //     0x8f7824: ldur            d0, [x0, #7]
    // 0x8f7828: ldur            x0, [fp, #-0x20]
    // 0x8f782c: stur            x0, [fp, #-0x20]
    // 0x8f7830: r2 = inline_Allocate_Double()
    //     0x8f7830: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0x8f7834: add             x2, x2, #0x10
    //     0x8f7838: cmp             x1, x2
    //     0x8f783c: b.ls            #0x8f790c
    //     0x8f7840: str             x2, [THR, #0x50]  ; THR::top
    //     0x8f7844: sub             x2, x2, #0xf
    //     0x8f7848: movz            x1, #0xe15c
    //     0x8f784c: movk            x1, #0x3, lsl #16
    //     0x8f7850: stur            x1, [x2, #-1]
    // 0x8f7854: StoreField: r2->field_7 = d0
    //     0x8f7854: stur            d0, [x2, #7]
    // 0x8f7858: ldur            x1, [fp, #-8]
    // 0x8f785c: r0 = _formatFixed()
    //     0x8f785c: bl              #0x8f58e4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatFixed
    // 0x8f7860: ldur            x1, [fp, #-8]
    // 0x8f7864: ldur            x2, [fp, #-0x20]
    // 0x8f7868: r0 = _formatExponent()
    //     0x8f7868: bl              #0x8f7928  ; [package:intl/src/intl/number_format.dart] NumberFormat::_formatExponent
    // 0x8f786c: r0 = Null
    //     0x8f786c: mov             x0, NULL
    // 0x8f7870: LeaveFrame
    //     0x8f7870: mov             SP, fp
    //     0x8f7874: ldp             fp, lr, [SP], #0x10
    // 0x8f7878: ret
    //     0x8f7878: ret             
    // 0x8f787c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f787c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7880: b               #0x8f732c
    // 0x8f7884: SaveReg d2
    //     0x8f7884: str             q2, [SP, #-0x10]!
    // 0x8f7888: d0 = 0.000000
    //     0x8f7888: fmov            d0, d2
    // 0x8f788c: r0 = 68
    //     0x8f788c: movz            x0, #0x44
    // 0x8f7890: r30 = DoubleToIntegerStub
    //     0x8f7890: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x8f7894: LoadField: r30 = r30->field_7
    //     0x8f7894: ldur            lr, [lr, #7]
    // 0x8f7898: blr             lr
    // 0x8f789c: mov             x1, x0
    // 0x8f78a0: RestoreReg d2
    //     0x8f78a0: ldr             q2, [SP], #0x10
    // 0x8f78a4: b               #0x8f7428
    // 0x8f78a8: SaveReg d0
    //     0x8f78a8: str             q0, [SP, #-0x10]!
    // 0x8f78ac: r0 = AllocateDouble()
    //     0x8f78ac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f78b0: RestoreReg d0
    //     0x8f78b0: ldr             q0, [SP], #0x10
    // 0x8f78b4: b               #0x8f754c
    // 0x8f78b8: r0 = StackOverflowSharedWithFPURegs()
    //     0x8f78b8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8f78bc: b               #0x8f75c8
    // 0x8f78c0: stp             q0, q1, [SP, #-0x20]!
    // 0x8f78c4: stp             x1, x2, [SP, #-0x10]!
    // 0x8f78c8: SaveReg r0
    //     0x8f78c8: str             x0, [SP, #-8]!
    // 0x8f78cc: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x8f78d0: r4 = 0
    //     0x8f78d0: movz            x4, #0
    // 0x8f78d4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x8f78d8: blr             lr
    // 0x8f78dc: brk             #0
    // 0x8f78e0: cmp             x0, xzr
    // 0x8f78e4: sub             x4, x3, x0
    // 0x8f78e8: add             x3, x3, x0
    // 0x8f78ec: csel            x3, x4, x3, lt
    // 0x8f78f0: b               #0x8f75dc
    // 0x8f78f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f78f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f78f8: b               #0x8f7690
    // 0x8f78fc: SaveReg d0
    //     0x8f78fc: str             q0, [SP, #-0x10]!
    // 0x8f7900: r0 = AllocateDouble()
    //     0x8f7900: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f7904: RestoreReg d0
    //     0x8f7904: ldr             q0, [SP], #0x10
    // 0x8f7908: b               #0x8f7814
    // 0x8f790c: SaveReg d0
    //     0x8f790c: str             q0, [SP, #-0x10]!
    // 0x8f7910: SaveReg r0
    //     0x8f7910: str             x0, [SP, #-8]!
    // 0x8f7914: r0 = AllocateDouble()
    //     0x8f7914: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8f7918: mov             x2, x0
    // 0x8f791c: RestoreReg r0
    //     0x8f791c: ldr             x0, [SP], #8
    // 0x8f7920: RestoreReg d0
    //     0x8f7920: ldr             q0, [SP], #0x10
    // 0x8f7924: b               #0x8f7854
  }
  _ _formatExponent(/* No info */) {
    // ** addr: 0x8f7928, size: 0x10c
    // 0x8f7928: EnterFrame
    //     0x8f7928: stp             fp, lr, [SP, #-0x10]!
    //     0x8f792c: mov             fp, SP
    // 0x8f7930: AllocStack(0x28)
    //     0x8f7930: sub             SP, SP, #0x28
    // 0x8f7934: SetupParameters(NumberFormat this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x8f7934: mov             x3, x1
    //     0x8f7938: mov             x0, x2
    //     0x8f793c: stur            x1, [fp, #-0x10]
    //     0x8f7940: stur            x2, [fp, #-0x18]
    // 0x8f7944: CheckStackOverflow
    //     0x8f7944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7948: cmp             SP, x16
    //     0x8f794c: b.ls            #0x8f7a2c
    // 0x8f7950: LoadField: r4 = r3->field_77
    //     0x8f7950: ldur            w4, [x3, #0x77]
    // 0x8f7954: DecompressPointer r4
    //     0x8f7954: add             x4, x4, HEAP, lsl #32
    // 0x8f7958: stur            x4, [fp, #-8]
    // 0x8f795c: LoadField: r2 = r4->field_23
    //     0x8f795c: ldur            w2, [x4, #0x23]
    // 0x8f7960: DecompressPointer r2
    //     0x8f7960: add             x2, x2, HEAP, lsl #32
    // 0x8f7964: mov             x1, x3
    // 0x8f7968: r0 = _add()
    //     0x8f7968: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f796c: ldur            x0, [fp, #-0x18]
    // 0x8f7970: tbz             x0, #0x3f, #0x8f7998
    // 0x8f7974: ldur            x1, [fp, #-8]
    // 0x8f7978: neg             x3, x0
    // 0x8f797c: stur            x3, [fp, #-0x20]
    // 0x8f7980: LoadField: r2 = r1->field_1f
    //     0x8f7980: ldur            w2, [x1, #0x1f]
    // 0x8f7984: DecompressPointer r2
    //     0x8f7984: add             x2, x2, HEAP, lsl #32
    // 0x8f7988: ldur            x1, [fp, #-0x10]
    // 0x8f798c: r0 = _add()
    //     0x8f798c: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f7990: ldur            x3, [fp, #-0x20]
    // 0x8f7994: b               #0x8f79c0
    // 0x8f7998: ldur            x3, [fp, #-0x10]
    // 0x8f799c: ldur            x1, [fp, #-8]
    // 0x8f79a0: LoadField: r2 = r3->field_2b
    //     0x8f79a0: ldur            w2, [x3, #0x2b]
    // 0x8f79a4: DecompressPointer r2
    //     0x8f79a4: add             x2, x2, HEAP, lsl #32
    // 0x8f79a8: tbnz            w2, #4, #0x8f79bc
    // 0x8f79ac: LoadField: r2 = r1->field_1b
    //     0x8f79ac: ldur            w2, [x1, #0x1b]
    // 0x8f79b0: DecompressPointer r2
    //     0x8f79b0: add             x2, x2, HEAP, lsl #32
    // 0x8f79b4: mov             x1, x3
    // 0x8f79b8: r0 = _add()
    //     0x8f79b8: bl              #0x8f7c00  ; [package:intl/src/intl/number_format.dart] NumberFormat::_add
    // 0x8f79bc: ldur            x3, [fp, #-0x18]
    // 0x8f79c0: ldur            x2, [fp, #-0x10]
    // 0x8f79c4: LoadField: r4 = r2->field_53
    //     0x8f79c4: ldur            x4, [x2, #0x53]
    // 0x8f79c8: stur            x4, [fp, #-0x18]
    // 0x8f79cc: r0 = BoxInt64Instr(r3)
    //     0x8f79cc: sbfiz           x0, x3, #1, #0x1f
    //     0x8f79d0: cmp             x3, x0, asr #1
    //     0x8f79d4: b.eq            #0x8f79e0
    //     0x8f79d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f79dc: stur            x3, [x0, #7]
    // 0x8f79e0: r1 = 60
    //     0x8f79e0: movz            x1, #0x3c
    // 0x8f79e4: branchIfSmi(r0, 0x8f79f0)
    //     0x8f79e4: tbz             w0, #0, #0x8f79f0
    // 0x8f79e8: r1 = LoadClassIdInstr(r0)
    //     0x8f79e8: ldur            x1, [x0, #-1]
    //     0x8f79ec: ubfx            x1, x1, #0xc, #0x14
    // 0x8f79f0: str             x0, [SP]
    // 0x8f79f4: mov             x0, x1
    // 0x8f79f8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8f79f8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8f79fc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8f79fc: movz            x17, #0x2b03
    //     0x8f7a00: add             lr, x0, x17
    //     0x8f7a04: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7a08: blr             lr
    // 0x8f7a0c: ldur            x1, [fp, #-0x10]
    // 0x8f7a10: ldur            x2, [fp, #-0x18]
    // 0x8f7a14: mov             x3, x0
    // 0x8f7a18: r0 = _pad()
    //     0x8f7a18: bl              #0x8f7a34  ; [package:intl/src/intl/number_format.dart] NumberFormat::_pad
    // 0x8f7a1c: r0 = Null
    //     0x8f7a1c: mov             x0, NULL
    // 0x8f7a20: LeaveFrame
    //     0x8f7a20: mov             SP, fp
    //     0x8f7a24: ldp             fp, lr, [SP], #0x10
    // 0x8f7a28: ret
    //     0x8f7a28: ret             
    // 0x8f7a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7a30: b               #0x8f7950
  }
  _ _pad(/* No info */) {
    // ** addr: 0x8f7a34, size: 0x80
    // 0x8f7a34: EnterFrame
    //     0x8f7a34: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7a38: mov             fp, SP
    // 0x8f7a3c: AllocStack(0x8)
    //     0x8f7a3c: sub             SP, SP, #8
    // 0x8f7a40: SetupParameters(NumberFormat this /* r1 => r0 */, dynamic _ /* r3 => r1 */)
    //     0x8f7a40: mov             x0, x1
    //     0x8f7a44: mov             x1, x3
    // 0x8f7a48: CheckStackOverflow
    //     0x8f7a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7a4c: cmp             SP, x16
    //     0x8f7a50: b.ls            #0x8f7aac
    // 0x8f7a54: LoadField: r3 = r0->field_7f
    //     0x8f7a54: ldur            x3, [x0, #0x7f]
    // 0x8f7a58: cbnz            x3, #0x8f7a90
    // 0x8f7a5c: LoadField: r4 = r0->field_7b
    //     0x8f7a5c: ldur            w4, [x0, #0x7b]
    // 0x8f7a60: DecompressPointer r4
    //     0x8f7a60: add             x4, x4, HEAP, lsl #32
    // 0x8f7a64: stur            x4, [fp, #-8]
    // 0x8f7a68: r0 = LoadClassIdInstr(r1)
    //     0x8f7a68: ldur            x0, [x1, #-1]
    //     0x8f7a6c: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7a70: r3 = "0"
    //     0x8f7a70: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8f7a74: r0 = GDT[cid_x0 + -0xff8]()
    //     0x8f7a74: sub             lr, x0, #0xff8
    //     0x8f7a78: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7a7c: blr             lr
    // 0x8f7a80: ldur            x1, [fp, #-8]
    // 0x8f7a84: mov             x2, x0
    // 0x8f7a88: r0 = write()
    //     0x8f7a88: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8f7a8c: b               #0x8f7a9c
    // 0x8f7a90: mov             x3, x1
    // 0x8f7a94: mov             x1, x0
    // 0x8f7a98: r0 = _slowPad()
    //     0x8f7a98: bl              #0x8f7ab4  ; [package:intl/src/intl/number_format.dart] NumberFormat::_slowPad
    // 0x8f7a9c: r0 = Null
    //     0x8f7a9c: mov             x0, NULL
    // 0x8f7aa0: LeaveFrame
    //     0x8f7aa0: mov             SP, fp
    //     0x8f7aa4: ldp             fp, lr, [SP], #0x10
    // 0x8f7aa8: ret
    //     0x8f7aa8: ret             
    // 0x8f7aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7ab0: b               #0x8f7a54
  }
  _ _slowPad(/* No info */) {
    // ** addr: 0x8f7ab4, size: 0x14c
    // 0x8f7ab4: EnterFrame
    //     0x8f7ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7ab8: mov             fp, SP
    // 0x8f7abc: AllocStack(0x38)
    //     0x8f7abc: sub             SP, SP, #0x38
    // 0x8f7ac0: SetupParameters(NumberFormat this /* r1 => r0, fp-0x30 */, dynamic _ /* r3 => r3, fp-0x38 */)
    //     0x8f7ac0: mov             x0, x1
    //     0x8f7ac4: stur            x1, [fp, #-0x30]
    //     0x8f7ac8: stur            x3, [fp, #-0x38]
    // 0x8f7acc: CheckStackOverflow
    //     0x8f7acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7ad0: cmp             SP, x16
    //     0x8f7ad4: b.ls            #0x8f7be8
    // 0x8f7ad8: LoadField: r1 = r3->field_7
    //     0x8f7ad8: ldur            w1, [x3, #7]
    // 0x8f7adc: r4 = LoadInt32Instr(r1)
    //     0x8f7adc: sbfx            x4, x1, #1, #0x1f
    // 0x8f7ae0: stur            x4, [fp, #-0x28]
    // 0x8f7ae4: sub             x5, x2, x4
    // 0x8f7ae8: stur            x5, [fp, #-0x20]
    // 0x8f7aec: LoadField: r1 = r0->field_77
    //     0x8f7aec: ldur            w1, [x0, #0x77]
    // 0x8f7af0: DecompressPointer r1
    //     0x8f7af0: add             x1, x1, HEAP, lsl #32
    // 0x8f7af4: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x8f7af4: ldur            w6, [x1, #0x17]
    // 0x8f7af8: DecompressPointer r6
    //     0x8f7af8: add             x6, x6, HEAP, lsl #32
    // 0x8f7afc: stur            x6, [fp, #-0x18]
    // 0x8f7b00: LoadField: r7 = r0->field_7b
    //     0x8f7b00: ldur            w7, [x0, #0x7b]
    // 0x8f7b04: DecompressPointer r7
    //     0x8f7b04: add             x7, x7, HEAP, lsl #32
    // 0x8f7b08: stur            x7, [fp, #-0x10]
    // 0x8f7b0c: r8 = 0
    //     0x8f7b0c: movz            x8, #0
    // 0x8f7b10: stur            x8, [fp, #-8]
    // 0x8f7b14: CheckStackOverflow
    //     0x8f7b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7b18: cmp             SP, x16
    //     0x8f7b1c: b.ls            #0x8f7bf0
    // 0x8f7b20: cmp             x8, x5
    // 0x8f7b24: b.ge            #0x8f7b58
    // 0x8f7b28: mov             x1, x7
    // 0x8f7b2c: mov             x2, x6
    // 0x8f7b30: r0 = write()
    //     0x8f7b30: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8f7b34: ldur            x0, [fp, #-8]
    // 0x8f7b38: add             x8, x0, #1
    // 0x8f7b3c: ldur            x0, [fp, #-0x30]
    // 0x8f7b40: ldur            x3, [fp, #-0x38]
    // 0x8f7b44: ldur            x5, [fp, #-0x20]
    // 0x8f7b48: ldur            x6, [fp, #-0x18]
    // 0x8f7b4c: ldur            x7, [fp, #-0x10]
    // 0x8f7b50: ldur            x4, [fp, #-0x28]
    // 0x8f7b54: b               #0x8f7b10
    // 0x8f7b58: mov             x1, x0
    // 0x8f7b5c: mov             x0, x3
    // 0x8f7b60: r3 = LoadClassIdInstr(r0)
    //     0x8f7b60: ldur            x3, [x0, #-1]
    //     0x8f7b64: ubfx            x3, x3, #0xc, #0x14
    // 0x8f7b68: lsl             x3, x3, #1
    // 0x8f7b6c: stur            x3, [fp, #-0x18]
    // 0x8f7b70: LoadField: r4 = r1->field_7f
    //     0x8f7b70: ldur            x4, [x1, #0x7f]
    // 0x8f7b74: stur            x4, [fp, #-0x20]
    // 0x8f7b78: r6 = 0
    //     0x8f7b78: movz            x6, #0
    // 0x8f7b7c: ldur            x5, [fp, #-0x28]
    // 0x8f7b80: stur            x6, [fp, #-8]
    // 0x8f7b84: CheckStackOverflow
    //     0x8f7b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7b88: cmp             SP, x16
    //     0x8f7b8c: b.ls            #0x8f7bf8
    // 0x8f7b90: cmp             x6, x5
    // 0x8f7b94: b.ge            #0x8f7bd8
    // 0x8f7b98: cmp             w3, #0xbc
    // 0x8f7b9c: b.ne            #0x8f7bac
    // 0x8f7ba0: ArrayLoad: r1 = r0[r6]  ; TypedUnsigned_1
    //     0x8f7ba0: add             x16, x0, x6
    //     0x8f7ba4: ldrb            w1, [x16, #0xf]
    // 0x8f7ba8: b               #0x8f7bb4
    // 0x8f7bac: add             x16, x0, x6, lsl #1
    // 0x8f7bb0: ldurh           w1, [x16, #0xf]
    // 0x8f7bb4: add             x2, x1, x4
    // 0x8f7bb8: ldur            x1, [fp, #-0x10]
    // 0x8f7bbc: r0 = writeCharCode()
    //     0x8f7bbc: bl              #0x603f20  ; [dart:core] StringBuffer::writeCharCode
    // 0x8f7bc0: ldur            x1, [fp, #-8]
    // 0x8f7bc4: add             x6, x1, #1
    // 0x8f7bc8: ldur            x0, [fp, #-0x38]
    // 0x8f7bcc: ldur            x3, [fp, #-0x18]
    // 0x8f7bd0: ldur            x4, [fp, #-0x20]
    // 0x8f7bd4: b               #0x8f7b7c
    // 0x8f7bd8: r0 = Null
    //     0x8f7bd8: mov             x0, NULL
    // 0x8f7bdc: LeaveFrame
    //     0x8f7bdc: mov             SP, fp
    //     0x8f7be0: ldp             fp, lr, [SP], #0x10
    // 0x8f7be4: ret
    //     0x8f7be4: ret             
    // 0x8f7be8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7be8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7bec: b               #0x8f7ad8
    // 0x8f7bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7bf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7bf4: b               #0x8f7b20
    // 0x8f7bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7bf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7bfc: b               #0x8f7b90
  }
  _ _add(/* No info */) {
    // ** addr: 0x8f7c00, size: 0x3c
    // 0x8f7c00: EnterFrame
    //     0x8f7c00: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7c04: mov             fp, SP
    // 0x8f7c08: CheckStackOverflow
    //     0x8f7c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7c0c: cmp             SP, x16
    //     0x8f7c10: b.ls            #0x8f7c34
    // 0x8f7c14: LoadField: r0 = r1->field_7b
    //     0x8f7c14: ldur            w0, [x1, #0x7b]
    // 0x8f7c18: DecompressPointer r0
    //     0x8f7c18: add             x0, x0, HEAP, lsl #32
    // 0x8f7c1c: mov             x1, x0
    // 0x8f7c20: r0 = write()
    //     0x8f7c20: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x8f7c24: r0 = Null
    //     0x8f7c24: mov             x0, NULL
    // 0x8f7c28: LeaveFrame
    //     0x8f7c28: mov             SP, fp
    //     0x8f7c2c: ldp             fp, lr, [SP], #0x10
    // 0x8f7c30: ret
    //     0x8f7c30: ret             
    // 0x8f7c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7c34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7c38: b               #0x8f7c14
  }
  _ _signPrefix(/* No info */) {
    // ** addr: 0x8f7c3c, size: 0x74
    // 0x8f7c3c: EnterFrame
    //     0x8f7c3c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7c40: mov             fp, SP
    // 0x8f7c44: AllocStack(0x10)
    //     0x8f7c44: sub             SP, SP, #0x10
    // 0x8f7c48: SetupParameters(NumberFormat this /* r1 => r1, fp-0x8 */)
    //     0x8f7c48: stur            x1, [fp, #-8]
    // 0x8f7c4c: CheckStackOverflow
    //     0x8f7c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7c50: cmp             SP, x16
    //     0x8f7c54: b.ls            #0x8f7ca8
    // 0x8f7c58: str             x2, [SP]
    // 0x8f7c5c: r4 = 0
    //     0x8f7c5c: movz            x4, #0
    // 0x8f7c60: ldr             x0, [SP]
    // 0x8f7c64: r16 = UnlinkedCall_0x5f3c08
    //     0x8f7c64: add             x16, PP, #0x27, lsl #12  ; [pp+0x27328] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8f7c68: add             x16, x16, #0x328
    // 0x8f7c6c: ldp             x5, lr, [x16]
    // 0x8f7c70: blr             lr
    // 0x8f7c74: tbnz            w0, #4, #0x8f7c8c
    // 0x8f7c78: ldur            x1, [fp, #-8]
    // 0x8f7c7c: LoadField: r2 = r1->field_7
    //     0x8f7c7c: ldur            w2, [x1, #7]
    // 0x8f7c80: DecompressPointer r2
    //     0x8f7c80: add             x2, x2, HEAP, lsl #32
    // 0x8f7c84: mov             x0, x2
    // 0x8f7c88: b               #0x8f7c9c
    // 0x8f7c8c: ldur            x1, [fp, #-8]
    // 0x8f7c90: LoadField: r2 = r1->field_b
    //     0x8f7c90: ldur            w2, [x1, #0xb]
    // 0x8f7c94: DecompressPointer r2
    //     0x8f7c94: add             x2, x2, HEAP, lsl #32
    // 0x8f7c98: mov             x0, x2
    // 0x8f7c9c: LeaveFrame
    //     0x8f7c9c: mov             SP, fp
    //     0x8f7ca0: ldp             fp, lr, [SP], #0x10
    // 0x8f7ca4: ret
    //     0x8f7ca4: ret             
    // 0x8f7ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7cac: b               #0x8f7c58
  }
  _ _isInfinite(/* No info */) {
    // ** addr: 0x8f7cb0, size: 0x70
    // 0x8f7cb0: EnterFrame
    //     0x8f7cb0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7cb4: mov             fp, SP
    // 0x8f7cb8: AllocStack(0x8)
    //     0x8f7cb8: sub             SP, SP, #8
    // 0x8f7cbc: CheckStackOverflow
    //     0x8f7cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7cc0: cmp             SP, x16
    //     0x8f7cc4: b.ls            #0x8f7d18
    // 0x8f7cc8: r0 = 60
    //     0x8f7cc8: movz            x0, #0x3c
    // 0x8f7ccc: branchIfSmi(r2, 0x8f7cd8)
    //     0x8f7ccc: tbz             w2, #0, #0x8f7cd8
    // 0x8f7cd0: r0 = LoadClassIdInstr(r2)
    //     0x8f7cd0: ldur            x0, [x2, #-1]
    //     0x8f7cd4: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7cd8: sub             x16, x0, #0x3c
    // 0x8f7cdc: cmp             x16, #2
    // 0x8f7ce0: b.hi            #0x8f7d08
    // 0x8f7ce4: r0 = 60
    //     0x8f7ce4: movz            x0, #0x3c
    // 0x8f7ce8: branchIfSmi(r2, 0x8f7cf4)
    //     0x8f7ce8: tbz             w2, #0, #0x8f7cf4
    // 0x8f7cec: r0 = LoadClassIdInstr(r2)
    //     0x8f7cec: ldur            x0, [x2, #-1]
    //     0x8f7cf0: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7cf4: str             x2, [SP]
    // 0x8f7cf8: r0 = GDT[cid_x0 + -0xfb5]()
    //     0x8f7cf8: sub             lr, x0, #0xfb5
    //     0x8f7cfc: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7d00: blr             lr
    // 0x8f7d04: b               #0x8f7d0c
    // 0x8f7d08: r0 = false
    //     0x8f7d08: add             x0, NULL, #0x30  ; false
    // 0x8f7d0c: LeaveFrame
    //     0x8f7d0c: mov             SP, fp
    //     0x8f7d10: ldp             fp, lr, [SP], #0x10
    // 0x8f7d14: ret
    //     0x8f7d14: ret             
    // 0x8f7d18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7d18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7d1c: b               #0x8f7cc8
  }
  _ _isNaN(/* No info */) {
    // ** addr: 0x8f7d20, size: 0x70
    // 0x8f7d20: EnterFrame
    //     0x8f7d20: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7d24: mov             fp, SP
    // 0x8f7d28: AllocStack(0x8)
    //     0x8f7d28: sub             SP, SP, #8
    // 0x8f7d2c: CheckStackOverflow
    //     0x8f7d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7d30: cmp             SP, x16
    //     0x8f7d34: b.ls            #0x8f7d88
    // 0x8f7d38: r0 = 60
    //     0x8f7d38: movz            x0, #0x3c
    // 0x8f7d3c: branchIfSmi(r2, 0x8f7d48)
    //     0x8f7d3c: tbz             w2, #0, #0x8f7d48
    // 0x8f7d40: r0 = LoadClassIdInstr(r2)
    //     0x8f7d40: ldur            x0, [x2, #-1]
    //     0x8f7d44: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7d48: sub             x16, x0, #0x3c
    // 0x8f7d4c: cmp             x16, #2
    // 0x8f7d50: b.hi            #0x8f7d78
    // 0x8f7d54: r0 = 60
    //     0x8f7d54: movz            x0, #0x3c
    // 0x8f7d58: branchIfSmi(r2, 0x8f7d64)
    //     0x8f7d58: tbz             w2, #0, #0x8f7d64
    // 0x8f7d5c: r0 = LoadClassIdInstr(r2)
    //     0x8f7d5c: ldur            x0, [x2, #-1]
    //     0x8f7d60: ubfx            x0, x0, #0xc, #0x14
    // 0x8f7d64: str             x2, [SP]
    // 0x8f7d68: r0 = GDT[cid_x0 + -0xfcc]()
    //     0x8f7d68: sub             lr, x0, #0xfcc
    //     0x8f7d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x8f7d70: blr             lr
    // 0x8f7d74: b               #0x8f7d7c
    // 0x8f7d78: r0 = false
    //     0x8f7d78: add             x0, NULL, #0x30  ; false
    // 0x8f7d7c: LeaveFrame
    //     0x8f7d7c: mov             SP, fp
    //     0x8f7d80: ldp             fp, lr, [SP], #0x10
    // 0x8f7d84: ret
    //     0x8f7d84: ret             
    // 0x8f7d88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7d88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7d8c: b               #0x8f7d38
  }
  factory _ NumberFormat.simpleCurrency(/* No info */) {
    // ** addr: 0xadf0e0, size: 0x7c
    // 0xadf0e0: EnterFrame
    //     0xadf0e0: stp             fp, lr, [SP, #-0x10]!
    //     0xadf0e4: mov             fp, SP
    // 0xadf0e8: AllocStack(0x30)
    //     0xadf0e8: sub             SP, SP, #0x30
    // 0xadf0ec: SetupParameters(dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xadf0ec: stur            x3, [fp, #-0x10]
    // 0xadf0f0: CheckStackOverflow
    //     0xadf0f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf0f4: cmp             SP, x16
    //     0xadf0f8: b.ls            #0xadf154
    // 0xadf0fc: lsl             x0, x2, #1
    // 0xadf100: stur            x0, [fp, #-8]
    // 0xadf104: r1 = Function '<anonymous closure>': static.
    //     0xadf104: add             x1, PP, #0x28, lsl #12  ; [pp+0x28570] AnonymousClosure: static (0xadf15c), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat.simpleCurrency (0xadf0e0)
    //     0xadf108: ldr             x1, [x1, #0x570]
    // 0xadf10c: r2 = Null
    //     0xadf10c: mov             x2, NULL
    // 0xadf110: r0 = AllocateClosure()
    //     0xadf110: bl              #0xec1630  ; AllocateClosureStub
    // 0xadf114: ldur            x16, [fp, #-0x10]
    // 0xadf118: ldur            lr, [fp, #-8]
    // 0xadf11c: stp             lr, x16, [SP, #0x10]
    // 0xadf120: r16 = true
    //     0xadf120: add             x16, NULL, #0x20  ; true
    // 0xadf124: r30 = true
    //     0xadf124: add             lr, NULL, #0x20  ; true
    // 0xadf128: stp             lr, x16, [SP]
    // 0xadf12c: mov             x3, x0
    // 0xadf130: r1 = Null
    //     0xadf130: mov             x1, NULL
    // 0xadf134: r2 = "id_ID"
    //     0xadf134: add             x2, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0xadf138: ldr             x2, [x2, #0x200]
    // 0xadf13c: r4 = const [0, 0x7, 0x4, 0x3, decimalDigits, 0x4, isForCurrency, 0x6, lookupSimpleCurrencySymbol, 0x5, name, 0x3, null]
    //     0xadf13c: add             x4, PP, #0x28, lsl #12  ; [pp+0x28578] List(13) [0, 0x7, 0x4, 0x3, "decimalDigits", 0x4, "isForCurrency", 0x6, "lookupSimpleCurrencySymbol", 0x5, "name", 0x3, Null]
    //     0xadf140: ldr             x4, [x4, #0x578]
    // 0xadf144: r0 = NumberFormat._forPattern()
    //     0xadf144: bl              #0x846b60  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xadf148: LeaveFrame
    //     0xadf148: mov             SP, fp
    //     0xadf14c: ldp             fp, lr, [SP], #0x10
    // 0xadf150: ret
    //     0xadf150: ret             
    // 0xadf154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf158: b               #0xadf0fc
  }
  [closure] static String <anonymous closure>(dynamic, NumberSymbols) {
    // ** addr: 0xadf15c, size: 0x10
    // 0xadf15c: ldr             x1, [SP]
    // 0xadf160: LoadField: r0 = r1->field_33
    //     0xadf160: ldur            w0, [x1, #0x33]
    // 0xadf164: DecompressPointer r0
    //     0xadf164: add             x0, x0, HEAP, lsl #32
    // 0xadf168: ret
    //     0xadf168: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc31b4c, size: 0x78
    // 0xc31b4c: EnterFrame
    //     0xc31b4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc31b50: mov             fp, SP
    // 0xc31b54: AllocStack(0x8)
    //     0xc31b54: sub             SP, SP, #8
    // 0xc31b58: CheckStackOverflow
    //     0xc31b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc31b5c: cmp             SP, x16
    //     0xc31b60: b.ls            #0xc31bbc
    // 0xc31b64: r1 = Null
    //     0xc31b64: mov             x1, NULL
    // 0xc31b68: r2 = 10
    //     0xc31b68: movz            x2, #0xa
    // 0xc31b6c: r0 = AllocateArray()
    //     0xc31b6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc31b70: r16 = "NumberFormat("
    //     0xc31b70: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ab0] "NumberFormat("
    //     0xc31b74: ldr             x16, [x16, #0xab0]
    // 0xc31b78: StoreField: r0->field_f = r16
    //     0xc31b78: stur            w16, [x0, #0xf]
    // 0xc31b7c: ldr             x1, [fp, #0x10]
    // 0xc31b80: LoadField: r2 = r1->field_73
    //     0xc31b80: ldur            w2, [x1, #0x73]
    // 0xc31b84: DecompressPointer r2
    //     0xc31b84: add             x2, x2, HEAP, lsl #32
    // 0xc31b88: StoreField: r0->field_13 = r2
    //     0xc31b88: stur            w2, [x0, #0x13]
    // 0xc31b8c: r16 = ", "
    //     0xc31b8c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc31b90: ArrayStore: r0[0] = r16  ; List_4
    //     0xc31b90: stur            w16, [x0, #0x17]
    // 0xc31b94: LoadField: r2 = r1->field_6f
    //     0xc31b94: ldur            w2, [x1, #0x6f]
    // 0xc31b98: DecompressPointer r2
    //     0xc31b98: add             x2, x2, HEAP, lsl #32
    // 0xc31b9c: StoreField: r0->field_1b = r2
    //     0xc31b9c: stur            w2, [x0, #0x1b]
    // 0xc31ba0: r16 = ")"
    //     0xc31ba0: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc31ba4: StoreField: r0->field_1f = r16
    //     0xc31ba4: stur            w16, [x0, #0x1f]
    // 0xc31ba8: str             x0, [SP]
    // 0xc31bac: r0 = _interpolate()
    //     0xc31bac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc31bb0: LeaveFrame
    //     0xc31bb0: mov             SP, fp
    //     0xc31bb4: ldp             fp, lr, [SP], #0x10
    // 0xc31bb8: ret
    //     0xc31bb8: ret             
    // 0xc31bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc31bbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc31bc0: b               #0xc31b64
  }
}
