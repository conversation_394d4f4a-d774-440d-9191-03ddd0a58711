// lib: , url: package:nuikit/src/widgets/expansion_tile/expansion_tile.dart

// class id: 1049954, size: 0x8
class :: {
}

// class id: 4127, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ExpansionTileState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x6f9a7c, size: 0x98
    // 0x6f9a7c: EnterFrame
    //     0x6f9a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f9a80: mov             fp, SP
    // 0x6f9a84: AllocStack(0x10)
    //     0x6f9a84: sub             SP, SP, #0x10
    // 0x6f9a88: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6f9a88: stur            x1, [fp, #-8]
    //     0x6f9a8c: stur            x2, [fp, #-0x10]
    // 0x6f9a90: CheckStackOverflow
    //     0x6f9a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f9a94: cmp             SP, x16
    //     0x6f9a98: b.ls            #0x6f9b08
    // 0x6f9a9c: r0 = Ticker()
    //     0x6f9a9c: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x6f9aa0: mov             x1, x0
    // 0x6f9aa4: r0 = false
    //     0x6f9aa4: add             x0, NULL, #0x30  ; false
    // 0x6f9aa8: StoreField: r1->field_b = r0
    //     0x6f9aa8: stur            w0, [x1, #0xb]
    // 0x6f9aac: ldur            x0, [fp, #-0x10]
    // 0x6f9ab0: StoreField: r1->field_13 = r0
    //     0x6f9ab0: stur            w0, [x1, #0x13]
    // 0x6f9ab4: mov             x0, x1
    // 0x6f9ab8: ldur            x2, [fp, #-8]
    // 0x6f9abc: StoreField: r2->field_13 = r0
    //     0x6f9abc: stur            w0, [x2, #0x13]
    //     0x6f9ac0: ldurb           w16, [x2, #-1]
    //     0x6f9ac4: ldurb           w17, [x0, #-1]
    //     0x6f9ac8: and             x16, x17, x16, lsr #2
    //     0x6f9acc: tst             x16, HEAP, lsr #32
    //     0x6f9ad0: b.eq            #0x6f9ad8
    //     0x6f9ad4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6f9ad8: mov             x1, x2
    // 0x6f9adc: r0 = _updateTickerModeNotifier()
    //     0x6f9adc: bl              #0x6f9b38  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x6f9ae0: ldur            x1, [fp, #-8]
    // 0x6f9ae4: r0 = _updateTicker()
    //     0x6f9ae4: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f9ae8: ldur            x1, [fp, #-8]
    // 0x6f9aec: LoadField: r0 = r1->field_13
    //     0x6f9aec: ldur            w0, [x1, #0x13]
    // 0x6f9af0: DecompressPointer r0
    //     0x6f9af0: add             x0, x0, HEAP, lsl #32
    // 0x6f9af4: cmp             w0, NULL
    // 0x6f9af8: b.eq            #0x6f9b10
    // 0x6f9afc: LeaveFrame
    //     0x6f9afc: mov             SP, fp
    //     0x6f9b00: ldp             fp, lr, [SP], #0x10
    // 0x6f9b04: ret
    //     0x6f9b04: ret             
    // 0x6f9b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f9b08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f9b0c: b               #0x6f9a9c
    // 0x6f9b10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f9b10: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x6f9b38, size: 0x124
    // 0x6f9b38: EnterFrame
    //     0x6f9b38: stp             fp, lr, [SP, #-0x10]!
    //     0x6f9b3c: mov             fp, SP
    // 0x6f9b40: AllocStack(0x18)
    //     0x6f9b40: sub             SP, SP, #0x18
    // 0x6f9b44: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x6f9b44: mov             x2, x1
    //     0x6f9b48: stur            x1, [fp, #-8]
    // 0x6f9b4c: CheckStackOverflow
    //     0x6f9b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f9b50: cmp             SP, x16
    //     0x6f9b54: b.ls            #0x6f9c50
    // 0x6f9b58: LoadField: r1 = r2->field_f
    //     0x6f9b58: ldur            w1, [x2, #0xf]
    // 0x6f9b5c: DecompressPointer r1
    //     0x6f9b5c: add             x1, x1, HEAP, lsl #32
    // 0x6f9b60: cmp             w1, NULL
    // 0x6f9b64: b.eq            #0x6f9c58
    // 0x6f9b68: r0 = getNotifier()
    //     0x6f9b68: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x6f9b6c: mov             x3, x0
    // 0x6f9b70: ldur            x0, [fp, #-8]
    // 0x6f9b74: stur            x3, [fp, #-0x18]
    // 0x6f9b78: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6f9b78: ldur            w4, [x0, #0x17]
    // 0x6f9b7c: DecompressPointer r4
    //     0x6f9b7c: add             x4, x4, HEAP, lsl #32
    // 0x6f9b80: stur            x4, [fp, #-0x10]
    // 0x6f9b84: cmp             w3, w4
    // 0x6f9b88: b.ne            #0x6f9b9c
    // 0x6f9b8c: r0 = Null
    //     0x6f9b8c: mov             x0, NULL
    // 0x6f9b90: LeaveFrame
    //     0x6f9b90: mov             SP, fp
    //     0x6f9b94: ldp             fp, lr, [SP], #0x10
    // 0x6f9b98: ret
    //     0x6f9b98: ret             
    // 0x6f9b9c: cmp             w4, NULL
    // 0x6f9ba0: b.eq            #0x6f9be4
    // 0x6f9ba4: mov             x2, x0
    // 0x6f9ba8: r1 = Function '_updateTicker@364311458':.
    //     0x6f9ba8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41568] AnonymousClosure: (0x6f9c5c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f9bac: ldr             x1, [x1, #0x568]
    // 0x6f9bb0: r0 = AllocateClosure()
    //     0x6f9bb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f9bb4: ldur            x1, [fp, #-0x10]
    // 0x6f9bb8: r2 = LoadClassIdInstr(r1)
    //     0x6f9bb8: ldur            x2, [x1, #-1]
    //     0x6f9bbc: ubfx            x2, x2, #0xc, #0x14
    // 0x6f9bc0: mov             x16, x0
    // 0x6f9bc4: mov             x0, x2
    // 0x6f9bc8: mov             x2, x16
    // 0x6f9bcc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0x6f9bcc: movz            x17, #0xbf5c
    //     0x6f9bd0: add             lr, x0, x17
    //     0x6f9bd4: ldr             lr, [x21, lr, lsl #3]
    //     0x6f9bd8: blr             lr
    // 0x6f9bdc: ldur            x0, [fp, #-8]
    // 0x6f9be0: ldur            x3, [fp, #-0x18]
    // 0x6f9be4: mov             x2, x0
    // 0x6f9be8: r1 = Function '_updateTicker@364311458':.
    //     0x6f9be8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41568] AnonymousClosure: (0x6f9c5c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0x6f9bec: ldr             x1, [x1, #0x568]
    // 0x6f9bf0: r0 = AllocateClosure()
    //     0x6f9bf0: bl              #0xec1630  ; AllocateClosureStub
    // 0x6f9bf4: ldur            x3, [fp, #-0x18]
    // 0x6f9bf8: r1 = LoadClassIdInstr(r3)
    //     0x6f9bf8: ldur            x1, [x3, #-1]
    //     0x6f9bfc: ubfx            x1, x1, #0xc, #0x14
    // 0x6f9c00: mov             x2, x0
    // 0x6f9c04: mov             x0, x1
    // 0x6f9c08: mov             x1, x3
    // 0x6f9c0c: r0 = GDT[cid_x0 + 0xc407]()
    //     0x6f9c0c: movz            x17, #0xc407
    //     0x6f9c10: add             lr, x0, x17
    //     0x6f9c14: ldr             lr, [x21, lr, lsl #3]
    //     0x6f9c18: blr             lr
    // 0x6f9c1c: ldur            x0, [fp, #-0x18]
    // 0x6f9c20: ldur            x1, [fp, #-8]
    // 0x6f9c24: ArrayStore: r1[0] = r0  ; List_4
    //     0x6f9c24: stur            w0, [x1, #0x17]
    //     0x6f9c28: ldurb           w16, [x1, #-1]
    //     0x6f9c2c: ldurb           w17, [x0, #-1]
    //     0x6f9c30: and             x16, x17, x16, lsr #2
    //     0x6f9c34: tst             x16, HEAP, lsr #32
    //     0x6f9c38: b.eq            #0x6f9c40
    //     0x6f9c3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6f9c40: r0 = Null
    //     0x6f9c40: mov             x0, NULL
    // 0x6f9c44: LeaveFrame
    //     0x6f9c44: mov             SP, fp
    //     0x6f9c48: ldp             fp, lr, [SP], #0x10
    // 0x6f9c4c: ret
    //     0x6f9c4c: ret             
    // 0x6f9c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f9c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f9c54: b               #0x6f9b58
    // 0x6f9c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6f9c58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x6f9c5c, size: 0x38
    // 0x6f9c5c: EnterFrame
    //     0x6f9c5c: stp             fp, lr, [SP, #-0x10]!
    //     0x6f9c60: mov             fp, SP
    // 0x6f9c64: ldr             x0, [fp, #0x10]
    // 0x6f9c68: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6f9c68: ldur            w1, [x0, #0x17]
    // 0x6f9c6c: DecompressPointer r1
    //     0x6f9c6c: add             x1, x1, HEAP, lsl #32
    // 0x6f9c70: CheckStackOverflow
    //     0x6f9c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6f9c74: cmp             SP, x16
    //     0x6f9c78: b.ls            #0x6f9c8c
    // 0x6f9c7c: r0 = _updateTicker()
    //     0x6f9c7c: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x6f9c80: LeaveFrame
    //     0x6f9c80: mov             SP, fp
    //     0x6f9c84: ldp             fp, lr, [SP], #0x10
    // 0x6f9c88: ret
    //     0x6f9c88: ret             
    // 0x6f9c8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6f9c8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6f9c90: b               #0x6f9c7c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa83454, size: 0x94
    // 0xa83454: EnterFrame
    //     0xa83454: stp             fp, lr, [SP, #-0x10]!
    //     0xa83458: mov             fp, SP
    // 0xa8345c: AllocStack(0x10)
    //     0xa8345c: sub             SP, SP, #0x10
    // 0xa83460: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83460: mov             x0, x1
    //     0xa83464: stur            x1, [fp, #-0x10]
    // 0xa83468: CheckStackOverflow
    //     0xa83468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8346c: cmp             SP, x16
    //     0xa83470: b.ls            #0xa834e0
    // 0xa83474: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa83474: ldur            w3, [x0, #0x17]
    // 0xa83478: DecompressPointer r3
    //     0xa83478: add             x3, x3, HEAP, lsl #32
    // 0xa8347c: stur            x3, [fp, #-8]
    // 0xa83480: cmp             w3, NULL
    // 0xa83484: b.ne            #0xa83490
    // 0xa83488: mov             x1, x0
    // 0xa8348c: b               #0xa834cc
    // 0xa83490: mov             x2, x0
    // 0xa83494: r1 = Function '_updateTicker@364311458':.
    //     0xa83494: add             x1, PP, #0x41, lsl #12  ; [pp+0x41568] AnonymousClosure: (0x6f9c5c), in [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker (0x6efafc)
    //     0xa83498: ldr             x1, [x1, #0x568]
    // 0xa8349c: r0 = AllocateClosure()
    //     0xa8349c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa834a0: ldur            x1, [fp, #-8]
    // 0xa834a4: r2 = LoadClassIdInstr(r1)
    //     0xa834a4: ldur            x2, [x1, #-1]
    //     0xa834a8: ubfx            x2, x2, #0xc, #0x14
    // 0xa834ac: mov             x16, x0
    // 0xa834b0: mov             x0, x2
    // 0xa834b4: mov             x2, x16
    // 0xa834b8: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa834b8: movz            x17, #0xbf5c
    //     0xa834bc: add             lr, x0, x17
    //     0xa834c0: ldr             lr, [x21, lr, lsl #3]
    //     0xa834c4: blr             lr
    // 0xa834c8: ldur            x1, [fp, #-0x10]
    // 0xa834cc: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa834cc: stur            NULL, [x1, #0x17]
    // 0xa834d0: r0 = Null
    //     0xa834d0: mov             x0, NULL
    // 0xa834d4: LeaveFrame
    //     0xa834d4: mov             SP, fp
    //     0xa834d8: ldp             fp, lr, [SP], #0x10
    // 0xa834dc: ret
    //     0xa834dc: ret             
    // 0xa834e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa834e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa834e4: b               #0xa83474
  }
  _ activate(/* No info */) {
    // ** addr: 0xa859f0, size: 0x48
    // 0xa859f0: EnterFrame
    //     0xa859f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa859f4: mov             fp, SP
    // 0xa859f8: AllocStack(0x8)
    //     0xa859f8: sub             SP, SP, #8
    // 0xa859fc: SetupParameters(__ExpansionTileState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0xa859fc: mov             x0, x1
    //     0xa85a00: stur            x1, [fp, #-8]
    // 0xa85a04: CheckStackOverflow
    //     0xa85a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85a08: cmp             SP, x16
    //     0xa85a0c: b.ls            #0xa85a30
    // 0xa85a10: mov             x1, x0
    // 0xa85a14: r0 = _updateTickerModeNotifier()
    //     0xa85a14: bl              #0x6f9b38  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85a18: ldur            x1, [fp, #-8]
    // 0xa85a1c: r0 = _updateTicker()
    //     0xa85a1c: bl              #0x6efafc  ; [package:flutter/src/cupertino/activity_indicator.dart] __CupertinoActivityIndicatorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0xa85a20: r0 = Null
    //     0xa85a20: mov             x0, NULL
    // 0xa85a24: LeaveFrame
    //     0xa85a24: mov             SP, fp
    //     0xa85a28: ldp             fp, lr, [SP], #0x10
    // 0xa85a2c: ret
    //     0xa85a2c: ret             
    // 0xa85a30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85a30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85a34: b               #0xa85a10
  }
}

// class id: 4128, size: 0x48, field offset: 0x1c
class _ExpansionTileState extends __ExpansionTileState&State&SingleTickerProviderStateMixin {

  late AnimationController _controller; // offset: 0x2c
  late Animation<Color?> _backgroundColor; // offset: 0x40
  late Animation<Color?> _iconColor; // offset: 0x3c
  late Animation<Color?> _headerColor; // offset: 0x38
  late Animation<double> _iconTurns; // offset: 0x30
  late Animation<double> _heightFactor; // offset: 0x34
  static late final Animatable<double> _easeInTween; // offset: 0x151c
  static late final Animatable<double> _halfTween; // offset: 0x1520
  static late final Animatable<double> _easeOutTween; // offset: 0x1518

  _ initState(/* No info */) {
    // ** addr: 0x964cfc, size: 0x300
    // 0x964cfc: EnterFrame
    //     0x964cfc: stp             fp, lr, [SP, #-0x10]!
    //     0x964d00: mov             fp, SP
    // 0x964d04: AllocStack(0x20)
    //     0x964d04: sub             SP, SP, #0x20
    // 0x964d08: SetupParameters(_ExpansionTileState this /* r1 => r2, fp-0x8 */)
    //     0x964d08: mov             x2, x1
    //     0x964d0c: stur            x1, [fp, #-8]
    // 0x964d10: CheckStackOverflow
    //     0x964d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x964d14: cmp             SP, x16
    //     0x964d18: b.ls            #0x964fe8
    // 0x964d1c: r1 = <double>
    //     0x964d1c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x964d20: r0 = AnimationController()
    //     0x964d20: bl              #0x67af60  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x964d24: stur            x0, [fp, #-0x10]
    // 0x964d28: r16 = Instance_Duration
    //     0x964d28: add             x16, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0x964d2c: ldr             x16, [x16, #0x368]
    // 0x964d30: str             x16, [SP]
    // 0x964d34: mov             x1, x0
    // 0x964d38: ldur            x2, [fp, #-8]
    // 0x964d3c: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x964d3c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25408] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x964d40: ldr             x4, [x4, #0x408]
    // 0x964d44: r0 = AnimationController()
    //     0x964d44: bl              #0x6b317c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x964d48: ldur            x0, [fp, #-0x10]
    // 0x964d4c: ldur            x1, [fp, #-8]
    // 0x964d50: StoreField: r1->field_2b = r0
    //     0x964d50: stur            w0, [x1, #0x2b]
    //     0x964d54: ldurb           w16, [x1, #-1]
    //     0x964d58: ldurb           w17, [x0, #-1]
    //     0x964d5c: and             x16, x17, x16, lsr #2
    //     0x964d60: tst             x16, HEAP, lsr #32
    //     0x964d64: b.eq            #0x964d6c
    //     0x964d68: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x964d6c: r0 = InitLateStaticField(0x151c) // [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_easeInTween
    //     0x964d6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x964d70: ldr             x0, [x0, #0x2a38]
    //     0x964d74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x964d78: cmp             w0, w16
    //     0x964d7c: b.ne            #0x964d8c
    //     0x964d80: add             x2, PP, #0x41, lsl #12  ; [pp+0x41640] Field <_ExpansionTileState@**********._easeInTween@**********>: static late final (offset: 0x151c)
    //     0x964d84: ldr             x2, [x2, #0x640]
    //     0x964d88: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x964d8c: mov             x1, x0
    // 0x964d90: ldur            x2, [fp, #-0x10]
    // 0x964d94: stur            x0, [fp, #-0x10]
    // 0x964d98: r0 = animate()
    //     0x964d98: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x964d9c: ldur            x1, [fp, #-8]
    // 0x964da0: StoreField: r1->field_33 = r0
    //     0x964da0: stur            w0, [x1, #0x33]
    //     0x964da4: ldurb           w16, [x1, #-1]
    //     0x964da8: ldurb           w17, [x0, #-1]
    //     0x964dac: and             x16, x17, x16, lsr #2
    //     0x964db0: tst             x16, HEAP, lsr #32
    //     0x964db4: b.eq            #0x964dbc
    //     0x964db8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x964dbc: LoadField: r2 = r1->field_2b
    //     0x964dbc: ldur            w2, [x1, #0x2b]
    // 0x964dc0: DecompressPointer r2
    //     0x964dc0: add             x2, x2, HEAP, lsl #32
    // 0x964dc4: stur            x2, [fp, #-0x18]
    // 0x964dc8: r0 = InitLateStaticField(0x1520) // [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_halfTween
    //     0x964dc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x964dcc: ldr             x0, [x0, #0x2a40]
    //     0x964dd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x964dd4: cmp             w0, w16
    //     0x964dd8: b.ne            #0x964de8
    //     0x964ddc: add             x2, PP, #0x41, lsl #12  ; [pp+0x41648] Field <_ExpansionTileState@**********._halfTween@**********>: static late final (offset: 0x1520)
    //     0x964de0: ldr             x2, [x2, #0x648]
    //     0x964de4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x964de8: mov             x1, x0
    // 0x964dec: ldur            x2, [fp, #-0x10]
    // 0x964df0: r0 = chain()
    //     0x964df0: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x964df4: mov             x1, x0
    // 0x964df8: ldur            x2, [fp, #-0x18]
    // 0x964dfc: r0 = animate()
    //     0x964dfc: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x964e00: ldur            x3, [fp, #-8]
    // 0x964e04: StoreField: r3->field_2f = r0
    //     0x964e04: stur            w0, [x3, #0x2f]
    //     0x964e08: ldurb           w16, [x3, #-1]
    //     0x964e0c: ldurb           w17, [x0, #-1]
    //     0x964e10: and             x16, x17, x16, lsr #2
    //     0x964e14: tst             x16, HEAP, lsr #32
    //     0x964e18: b.eq            #0x964e20
    //     0x964e1c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x964e20: LoadField: r0 = r3->field_2b
    //     0x964e20: ldur            w0, [x3, #0x2b]
    // 0x964e24: DecompressPointer r0
    //     0x964e24: add             x0, x0, HEAP, lsl #32
    // 0x964e28: stur            x0, [fp, #-0x18]
    // 0x964e2c: LoadField: r1 = r3->field_1f
    //     0x964e2c: ldur            w1, [x3, #0x1f]
    // 0x964e30: DecompressPointer r1
    //     0x964e30: add             x1, x1, HEAP, lsl #32
    // 0x964e34: ldur            x2, [fp, #-0x10]
    // 0x964e38: r0 = chain()
    //     0x964e38: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x964e3c: mov             x1, x0
    // 0x964e40: ldur            x2, [fp, #-0x18]
    // 0x964e44: r0 = animate()
    //     0x964e44: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x964e48: ldur            x3, [fp, #-8]
    // 0x964e4c: StoreField: r3->field_37 = r0
    //     0x964e4c: stur            w0, [x3, #0x37]
    //     0x964e50: ldurb           w16, [x3, #-1]
    //     0x964e54: ldurb           w17, [x0, #-1]
    //     0x964e58: and             x16, x17, x16, lsr #2
    //     0x964e5c: tst             x16, HEAP, lsr #32
    //     0x964e60: b.eq            #0x964e68
    //     0x964e64: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x964e68: LoadField: r0 = r3->field_2b
    //     0x964e68: ldur            w0, [x3, #0x2b]
    // 0x964e6c: DecompressPointer r0
    //     0x964e6c: add             x0, x0, HEAP, lsl #32
    // 0x964e70: stur            x0, [fp, #-0x18]
    // 0x964e74: LoadField: r1 = r3->field_23
    //     0x964e74: ldur            w1, [x3, #0x23]
    // 0x964e78: DecompressPointer r1
    //     0x964e78: add             x1, x1, HEAP, lsl #32
    // 0x964e7c: ldur            x2, [fp, #-0x10]
    // 0x964e80: r0 = chain()
    //     0x964e80: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x964e84: mov             x1, x0
    // 0x964e88: ldur            x2, [fp, #-0x18]
    // 0x964e8c: r0 = animate()
    //     0x964e8c: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x964e90: ldur            x1, [fp, #-8]
    // 0x964e94: StoreField: r1->field_3b = r0
    //     0x964e94: stur            w0, [x1, #0x3b]
    //     0x964e98: ldurb           w16, [x1, #-1]
    //     0x964e9c: ldurb           w17, [x0, #-1]
    //     0x964ea0: and             x16, x17, x16, lsr #2
    //     0x964ea4: tst             x16, HEAP, lsr #32
    //     0x964ea8: b.eq            #0x964eb0
    //     0x964eac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x964eb0: LoadField: r2 = r1->field_2b
    //     0x964eb0: ldur            w2, [x1, #0x2b]
    // 0x964eb4: DecompressPointer r2
    //     0x964eb4: add             x2, x2, HEAP, lsl #32
    // 0x964eb8: stur            x2, [fp, #-0x18]
    // 0x964ebc: LoadField: r0 = r1->field_27
    //     0x964ebc: ldur            w0, [x1, #0x27]
    // 0x964ec0: DecompressPointer r0
    //     0x964ec0: add             x0, x0, HEAP, lsl #32
    // 0x964ec4: stur            x0, [fp, #-0x10]
    // 0x964ec8: r0 = InitLateStaticField(0x1518) // [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_easeOutTween
    //     0x964ec8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x964ecc: ldr             x0, [x0, #0x2a30]
    //     0x964ed0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x964ed4: cmp             w0, w16
    //     0x964ed8: b.ne            #0x964ee8
    //     0x964edc: add             x2, PP, #0x41, lsl #12  ; [pp+0x41650] Field <_ExpansionTileState@**********._easeOutTween@**********>: static late final (offset: 0x1518)
    //     0x964ee0: ldr             x2, [x2, #0x650]
    //     0x964ee4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x964ee8: ldur            x1, [fp, #-0x10]
    // 0x964eec: mov             x2, x0
    // 0x964ef0: r0 = chain()
    //     0x964ef0: bl              #0x796a50  ; [package:flutter/src/animation/tween.dart] Animatable::chain
    // 0x964ef4: mov             x1, x0
    // 0x964ef8: ldur            x2, [fp, #-0x18]
    // 0x964efc: r0 = animate()
    //     0x964efc: bl              #0x7e3340  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0x964f00: ldur            x2, [fp, #-8]
    // 0x964f04: StoreField: r2->field_3f = r0
    //     0x964f04: stur            w0, [x2, #0x3f]
    //     0x964f08: ldurb           w16, [x2, #-1]
    //     0x964f0c: ldurb           w17, [x0, #-1]
    //     0x964f10: and             x16, x17, x16, lsr #2
    //     0x964f14: tst             x16, HEAP, lsr #32
    //     0x964f18: b.eq            #0x964f20
    //     0x964f1c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x964f20: LoadField: r1 = r2->field_f
    //     0x964f20: ldur            w1, [x2, #0xf]
    // 0x964f24: DecompressPointer r1
    //     0x964f24: add             x1, x1, HEAP, lsl #32
    // 0x964f28: cmp             w1, NULL
    // 0x964f2c: b.eq            #0x964ff0
    // 0x964f30: r0 = of()
    //     0x964f30: bl              #0x964ffc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::of
    // 0x964f34: mov             x1, x0
    // 0x964f38: ldur            x0, [fp, #-8]
    // 0x964f3c: LoadField: r2 = r0->field_f
    //     0x964f3c: ldur            w2, [x0, #0xf]
    // 0x964f40: DecompressPointer r2
    //     0x964f40: add             x2, x2, HEAP, lsl #32
    // 0x964f44: cmp             w2, NULL
    // 0x964f48: b.eq            #0x964ff4
    // 0x964f4c: r0 = readState()
    //     0x964f4c: bl              #0x933608  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::readState
    // 0x964f50: mov             x3, x0
    // 0x964f54: r2 = Null
    //     0x964f54: mov             x2, NULL
    // 0x964f58: r1 = Null
    //     0x964f58: mov             x1, NULL
    // 0x964f5c: stur            x3, [fp, #-0x10]
    // 0x964f60: r4 = 60
    //     0x964f60: movz            x4, #0x3c
    // 0x964f64: branchIfSmi(r0, 0x964f70)
    //     0x964f64: tbz             w0, #0, #0x964f70
    // 0x964f68: r4 = LoadClassIdInstr(r0)
    //     0x964f68: ldur            x4, [x0, #-1]
    //     0x964f6c: ubfx            x4, x4, #0xc, #0x14
    // 0x964f70: cmp             x4, #0x3f
    // 0x964f74: b.eq            #0x964f88
    // 0x964f78: r8 = bool?
    //     0x964f78: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x964f7c: r3 = Null
    //     0x964f7c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41658] Null
    //     0x964f80: ldr             x3, [x3, #0x658]
    // 0x964f84: r0 = bool?()
    //     0x964f84: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x964f88: ldur            x0, [fp, #-0x10]
    // 0x964f8c: cmp             w0, NULL
    // 0x964f90: b.ne            #0x964fb8
    // 0x964f94: ldur            x1, [fp, #-8]
    // 0x964f98: LoadField: r0 = r1->field_b
    //     0x964f98: ldur            w0, [x1, #0xb]
    // 0x964f9c: DecompressPointer r0
    //     0x964f9c: add             x0, x0, HEAP, lsl #32
    // 0x964fa0: cmp             w0, NULL
    // 0x964fa4: b.eq            #0x964ff8
    // 0x964fa8: LoadField: r2 = r0->field_2b
    //     0x964fa8: ldur            w2, [x0, #0x2b]
    // 0x964fac: DecompressPointer r2
    //     0x964fac: add             x2, x2, HEAP, lsl #32
    // 0x964fb0: mov             x0, x2
    // 0x964fb4: b               #0x964fbc
    // 0x964fb8: ldur            x1, [fp, #-8]
    // 0x964fbc: StoreField: r1->field_43 = r0
    //     0x964fbc: stur            w0, [x1, #0x43]
    // 0x964fc0: tbnz            w0, #4, #0x964fd8
    // 0x964fc4: LoadField: r0 = r1->field_2b
    //     0x964fc4: ldur            w0, [x1, #0x2b]
    // 0x964fc8: DecompressPointer r0
    //     0x964fc8: add             x0, x0, HEAP, lsl #32
    // 0x964fcc: mov             x1, x0
    // 0x964fd0: d0 = 1.000000
    //     0x964fd0: fmov            d0, #1.00000000
    // 0x964fd4: r0 = value=()
    //     0x964fd4: bl              #0x6567c4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::value=
    // 0x964fd8: r0 = Null
    //     0x964fd8: mov             x0, NULL
    // 0x964fdc: LeaveFrame
    //     0x964fdc: mov             SP, fp
    //     0x964fe0: ldp             fp, lr, [SP], #0x10
    // 0x964fe4: ret
    //     0x964fe4: ret             
    // 0x964fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x964fe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x964fec: b               #0x964d1c
    // 0x964ff0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x964ff0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x964ff4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x964ff4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x964ff8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x964ff8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9d1d78, size: 0x340
    // 0x9d1d78: EnterFrame
    //     0x9d1d78: stp             fp, lr, [SP, #-0x10]!
    //     0x9d1d7c: mov             fp, SP
    // 0x9d1d80: AllocStack(0x30)
    //     0x9d1d80: sub             SP, SP, #0x30
    // 0x9d1d84: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0x9d1d84: mov             x0, x1
    //     0x9d1d88: stur            x1, [fp, #-8]
    // 0x9d1d8c: CheckStackOverflow
    //     0x9d1d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d1d90: cmp             SP, x16
    //     0x9d1d94: b.ls            #0x9d20a4
    // 0x9d1d98: LoadField: r1 = r0->field_f
    //     0x9d1d98: ldur            w1, [x0, #0xf]
    // 0x9d1d9c: DecompressPointer r1
    //     0x9d1d9c: add             x1, x1, HEAP, lsl #32
    // 0x9d1da0: cmp             w1, NULL
    // 0x9d1da4: b.eq            #0x9d20ac
    // 0x9d1da8: r0 = of()
    //     0x9d1da8: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9d1dac: mov             x3, x0
    // 0x9d1db0: stur            x3, [fp, #-0x28]
    // 0x9d1db4: LoadField: r4 = r3->field_3f
    //     0x9d1db4: ldur            w4, [x3, #0x3f]
    // 0x9d1db8: DecompressPointer r4
    //     0x9d1db8: add             x4, x4, HEAP, lsl #32
    // 0x9d1dbc: ldur            x5, [fp, #-8]
    // 0x9d1dc0: stur            x4, [fp, #-0x20]
    // 0x9d1dc4: LoadField: r6 = r5->field_1b
    //     0x9d1dc4: ldur            w6, [x5, #0x1b]
    // 0x9d1dc8: DecompressPointer r6
    //     0x9d1dc8: add             x6, x6, HEAP, lsl #32
    // 0x9d1dcc: stur            x6, [fp, #-0x18]
    // 0x9d1dd0: LoadField: r7 = r3->field_4b
    //     0x9d1dd0: ldur            w7, [x3, #0x4b]
    // 0x9d1dd4: DecompressPointer r7
    //     0x9d1dd4: add             x7, x7, HEAP, lsl #32
    // 0x9d1dd8: stur            x7, [fp, #-0x10]
    // 0x9d1ddc: LoadField: r2 = r6->field_7
    //     0x9d1ddc: ldur            w2, [x6, #7]
    // 0x9d1de0: DecompressPointer r2
    //     0x9d1de0: add             x2, x2, HEAP, lsl #32
    // 0x9d1de4: mov             x0, x7
    // 0x9d1de8: r1 = Null
    //     0x9d1de8: mov             x1, NULL
    // 0x9d1dec: cmp             w0, NULL
    // 0x9d1df0: b.eq            #0x9d1e18
    // 0x9d1df4: cmp             w2, NULL
    // 0x9d1df8: b.eq            #0x9d1e18
    // 0x9d1dfc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d1dfc: ldur            w4, [x2, #0x17]
    // 0x9d1e00: DecompressPointer r4
    //     0x9d1e00: add             x4, x4, HEAP, lsl #32
    // 0x9d1e04: r8 = X0?
    //     0x9d1e04: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d1e08: LoadField: r9 = r4->field_7
    //     0x9d1e08: ldur            x9, [x4, #7]
    // 0x9d1e0c: r3 = Null
    //     0x9d1e0c: add             x3, PP, #0x41, lsl #12  ; [pp+0x415e0] Null
    //     0x9d1e10: ldr             x3, [x3, #0x5e0]
    // 0x9d1e14: blr             x9
    // 0x9d1e18: ldur            x0, [fp, #-0x10]
    // 0x9d1e1c: ldur            x1, [fp, #-0x18]
    // 0x9d1e20: StoreField: r1->field_f = r0
    //     0x9d1e20: stur            w0, [x1, #0xf]
    //     0x9d1e24: ldurb           w16, [x1, #-1]
    //     0x9d1e28: ldurb           w17, [x0, #-1]
    //     0x9d1e2c: and             x16, x17, x16, lsr #2
    //     0x9d1e30: tst             x16, HEAP, lsr #32
    //     0x9d1e34: b.eq            #0x9d1e3c
    //     0x9d1e38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d1e3c: ldur            x3, [fp, #-8]
    // 0x9d1e40: LoadField: r4 = r3->field_1f
    //     0x9d1e40: ldur            w4, [x3, #0x1f]
    // 0x9d1e44: DecompressPointer r4
    //     0x9d1e44: add             x4, x4, HEAP, lsl #32
    // 0x9d1e48: stur            x4, [fp, #-0x30]
    // 0x9d1e4c: LoadField: r0 = r3->field_b
    //     0x9d1e4c: ldur            w0, [x3, #0xb]
    // 0x9d1e50: DecompressPointer r0
    //     0x9d1e50: add             x0, x0, HEAP, lsl #32
    // 0x9d1e54: cmp             w0, NULL
    // 0x9d1e58: b.eq            #0x9d20b0
    // 0x9d1e5c: ldur            x5, [fp, #-0x28]
    // 0x9d1e60: LoadField: r0 = r5->field_8f
    //     0x9d1e60: ldur            w0, [x5, #0x8f]
    // 0x9d1e64: DecompressPointer r0
    //     0x9d1e64: add             x0, x0, HEAP, lsl #32
    // 0x9d1e68: LoadField: r1 = r0->field_23
    //     0x9d1e68: ldur            w1, [x0, #0x23]
    // 0x9d1e6c: DecompressPointer r1
    //     0x9d1e6c: add             x1, x1, HEAP, lsl #32
    // 0x9d1e70: cmp             w1, NULL
    // 0x9d1e74: b.eq            #0x9d20b4
    // 0x9d1e78: LoadField: r6 = r1->field_b
    //     0x9d1e78: ldur            w6, [x1, #0xb]
    // 0x9d1e7c: DecompressPointer r6
    //     0x9d1e7c: add             x6, x6, HEAP, lsl #32
    // 0x9d1e80: stur            x6, [fp, #-0x18]
    // 0x9d1e84: LoadField: r7 = r4->field_7
    //     0x9d1e84: ldur            w7, [x4, #7]
    // 0x9d1e88: DecompressPointer r7
    //     0x9d1e88: add             x7, x7, HEAP, lsl #32
    // 0x9d1e8c: mov             x0, x6
    // 0x9d1e90: mov             x2, x7
    // 0x9d1e94: stur            x7, [fp, #-0x10]
    // 0x9d1e98: r1 = Null
    //     0x9d1e98: mov             x1, NULL
    // 0x9d1e9c: cmp             w0, NULL
    // 0x9d1ea0: b.eq            #0x9d1ec8
    // 0x9d1ea4: cmp             w2, NULL
    // 0x9d1ea8: b.eq            #0x9d1ec8
    // 0x9d1eac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d1eac: ldur            w4, [x2, #0x17]
    // 0x9d1eb0: DecompressPointer r4
    //     0x9d1eb0: add             x4, x4, HEAP, lsl #32
    // 0x9d1eb4: r8 = X0?
    //     0x9d1eb4: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d1eb8: LoadField: r9 = r4->field_7
    //     0x9d1eb8: ldur            x9, [x4, #7]
    // 0x9d1ebc: r3 = Null
    //     0x9d1ebc: add             x3, PP, #0x41, lsl #12  ; [pp+0x415f0] Null
    //     0x9d1ec0: ldr             x3, [x3, #0x5f0]
    // 0x9d1ec4: blr             x9
    // 0x9d1ec8: ldur            x0, [fp, #-0x18]
    // 0x9d1ecc: ldur            x3, [fp, #-0x30]
    // 0x9d1ed0: StoreField: r3->field_b = r0
    //     0x9d1ed0: stur            w0, [x3, #0xb]
    //     0x9d1ed4: ldurb           w16, [x3, #-1]
    //     0x9d1ed8: ldurb           w17, [x0, #-1]
    //     0x9d1edc: and             x16, x17, x16, lsr #2
    //     0x9d1ee0: tst             x16, HEAP, lsr #32
    //     0x9d1ee4: b.eq            #0x9d1eec
    //     0x9d1ee8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9d1eec: ldur            x0, [fp, #-0x20]
    // 0x9d1ef0: LoadField: r4 = r0->field_2b
    //     0x9d1ef0: ldur            w4, [x0, #0x2b]
    // 0x9d1ef4: DecompressPointer r4
    //     0x9d1ef4: add             x4, x4, HEAP, lsl #32
    // 0x9d1ef8: mov             x0, x4
    // 0x9d1efc: ldur            x2, [fp, #-0x10]
    // 0x9d1f00: stur            x4, [fp, #-0x18]
    // 0x9d1f04: r1 = Null
    //     0x9d1f04: mov             x1, NULL
    // 0x9d1f08: cmp             w0, NULL
    // 0x9d1f0c: b.eq            #0x9d1f34
    // 0x9d1f10: cmp             w2, NULL
    // 0x9d1f14: b.eq            #0x9d1f34
    // 0x9d1f18: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d1f18: ldur            w4, [x2, #0x17]
    // 0x9d1f1c: DecompressPointer r4
    //     0x9d1f1c: add             x4, x4, HEAP, lsl #32
    // 0x9d1f20: r8 = X0?
    //     0x9d1f20: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d1f24: LoadField: r9 = r4->field_7
    //     0x9d1f24: ldur            x9, [x4, #7]
    // 0x9d1f28: r3 = Null
    //     0x9d1f28: add             x3, PP, #0x41, lsl #12  ; [pp+0x41600] Null
    //     0x9d1f2c: ldr             x3, [x3, #0x600]
    // 0x9d1f30: blr             x9
    // 0x9d1f34: ldur            x0, [fp, #-0x18]
    // 0x9d1f38: ldur            x1, [fp, #-0x30]
    // 0x9d1f3c: StoreField: r1->field_f = r0
    //     0x9d1f3c: stur            w0, [x1, #0xf]
    //     0x9d1f40: ldurb           w16, [x1, #-1]
    //     0x9d1f44: ldurb           w17, [x0, #-1]
    //     0x9d1f48: and             x16, x17, x16, lsr #2
    //     0x9d1f4c: tst             x16, HEAP, lsr #32
    //     0x9d1f50: b.eq            #0x9d1f58
    //     0x9d1f54: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d1f58: ldur            x3, [fp, #-8]
    // 0x9d1f5c: LoadField: r4 = r3->field_23
    //     0x9d1f5c: ldur            w4, [x3, #0x23]
    // 0x9d1f60: DecompressPointer r4
    //     0x9d1f60: add             x4, x4, HEAP, lsl #32
    // 0x9d1f64: ldur            x0, [fp, #-0x28]
    // 0x9d1f68: stur            x4, [fp, #-0x30]
    // 0x9d1f6c: LoadField: r5 = r0->field_7f
    //     0x9d1f6c: ldur            w5, [x0, #0x7f]
    // 0x9d1f70: DecompressPointer r5
    //     0x9d1f70: add             x5, x5, HEAP, lsl #32
    // 0x9d1f74: stur            x5, [fp, #-0x20]
    // 0x9d1f78: LoadField: r6 = r4->field_7
    //     0x9d1f78: ldur            w6, [x4, #7]
    // 0x9d1f7c: DecompressPointer r6
    //     0x9d1f7c: add             x6, x6, HEAP, lsl #32
    // 0x9d1f80: mov             x0, x5
    // 0x9d1f84: mov             x2, x6
    // 0x9d1f88: stur            x6, [fp, #-0x10]
    // 0x9d1f8c: r1 = Null
    //     0x9d1f8c: mov             x1, NULL
    // 0x9d1f90: cmp             w0, NULL
    // 0x9d1f94: b.eq            #0x9d1fbc
    // 0x9d1f98: cmp             w2, NULL
    // 0x9d1f9c: b.eq            #0x9d1fbc
    // 0x9d1fa0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d1fa0: ldur            w4, [x2, #0x17]
    // 0x9d1fa4: DecompressPointer r4
    //     0x9d1fa4: add             x4, x4, HEAP, lsl #32
    // 0x9d1fa8: r8 = X0?
    //     0x9d1fa8: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d1fac: LoadField: r9 = r4->field_7
    //     0x9d1fac: ldur            x9, [x4, #7]
    // 0x9d1fb0: r3 = Null
    //     0x9d1fb0: add             x3, PP, #0x41, lsl #12  ; [pp+0x41610] Null
    //     0x9d1fb4: ldr             x3, [x3, #0x610]
    // 0x9d1fb8: blr             x9
    // 0x9d1fbc: ldur            x0, [fp, #-0x20]
    // 0x9d1fc0: ldur            x3, [fp, #-0x30]
    // 0x9d1fc4: StoreField: r3->field_b = r0
    //     0x9d1fc4: stur            w0, [x3, #0xb]
    //     0x9d1fc8: ldurb           w16, [x3, #-1]
    //     0x9d1fcc: ldurb           w17, [x0, #-1]
    //     0x9d1fd0: and             x16, x17, x16, lsr #2
    //     0x9d1fd4: tst             x16, HEAP, lsr #32
    //     0x9d1fd8: b.eq            #0x9d1fe0
    //     0x9d1fdc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9d1fe0: ldur            x0, [fp, #-0x18]
    // 0x9d1fe4: ldur            x2, [fp, #-0x10]
    // 0x9d1fe8: r1 = Null
    //     0x9d1fe8: mov             x1, NULL
    // 0x9d1fec: cmp             w0, NULL
    // 0x9d1ff0: b.eq            #0x9d2018
    // 0x9d1ff4: cmp             w2, NULL
    // 0x9d1ff8: b.eq            #0x9d2018
    // 0x9d1ffc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d1ffc: ldur            w4, [x2, #0x17]
    // 0x9d2000: DecompressPointer r4
    //     0x9d2000: add             x4, x4, HEAP, lsl #32
    // 0x9d2004: r8 = X0?
    //     0x9d2004: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d2008: LoadField: r9 = r4->field_7
    //     0x9d2008: ldur            x9, [x4, #7]
    // 0x9d200c: r3 = Null
    //     0x9d200c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41620] Null
    //     0x9d2010: ldr             x3, [x3, #0x620]
    // 0x9d2014: blr             x9
    // 0x9d2018: ldur            x0, [fp, #-0x18]
    // 0x9d201c: ldur            x1, [fp, #-0x30]
    // 0x9d2020: StoreField: r1->field_f = r0
    //     0x9d2020: stur            w0, [x1, #0xf]
    //     0x9d2024: ldurb           w16, [x1, #-1]
    //     0x9d2028: ldurb           w17, [x0, #-1]
    //     0x9d202c: and             x16, x17, x16, lsr #2
    //     0x9d2030: tst             x16, HEAP, lsr #32
    //     0x9d2034: b.eq            #0x9d203c
    //     0x9d2038: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x9d203c: ldur            x0, [fp, #-8]
    // 0x9d2040: LoadField: r3 = r0->field_27
    //     0x9d2040: ldur            w3, [x0, #0x27]
    // 0x9d2044: DecompressPointer r3
    //     0x9d2044: add             x3, x3, HEAP, lsl #32
    // 0x9d2048: stur            x3, [fp, #-0x10]
    // 0x9d204c: LoadField: r2 = r3->field_7
    //     0x9d204c: ldur            w2, [x3, #7]
    // 0x9d2050: DecompressPointer r2
    //     0x9d2050: add             x2, x2, HEAP, lsl #32
    // 0x9d2054: r0 = Null
    //     0x9d2054: mov             x0, NULL
    // 0x9d2058: r1 = Null
    //     0x9d2058: mov             x1, NULL
    // 0x9d205c: cmp             w0, NULL
    // 0x9d2060: b.eq            #0x9d2088
    // 0x9d2064: cmp             w2, NULL
    // 0x9d2068: b.eq            #0x9d2088
    // 0x9d206c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9d206c: ldur            w4, [x2, #0x17]
    // 0x9d2070: DecompressPointer r4
    //     0x9d2070: add             x4, x4, HEAP, lsl #32
    // 0x9d2074: r8 = X0?
    //     0x9d2074: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x9d2078: LoadField: r9 = r4->field_7
    //     0x9d2078: ldur            x9, [x4, #7]
    // 0x9d207c: r3 = Null
    //     0x9d207c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41630] Null
    //     0x9d2080: ldr             x3, [x3, #0x630]
    // 0x9d2084: blr             x9
    // 0x9d2088: ldur            x1, [fp, #-0x10]
    // 0x9d208c: StoreField: r1->field_b = rNULL
    //     0x9d208c: stur            NULL, [x1, #0xb]
    // 0x9d2090: StoreField: r1->field_f = rNULL
    //     0x9d2090: stur            NULL, [x1, #0xf]
    // 0x9d2094: r0 = Null
    //     0x9d2094: mov             x0, NULL
    // 0x9d2098: LeaveFrame
    //     0x9d2098: mov             SP, fp
    //     0x9d209c: ldp             fp, lr, [SP], #0x10
    // 0x9d20a0: ret
    //     0x9d20a0: ret             
    // 0x9d20a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d20a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d20a8: b               #0x9d1d98
    // 0x9d20ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d20ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d20b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d20b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d20b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d20b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa31dec, size: 0x1f0
    // 0xa31dec: EnterFrame
    //     0xa31dec: stp             fp, lr, [SP, #-0x10]!
    //     0xa31df0: mov             fp, SP
    // 0xa31df4: AllocStack(0x30)
    //     0xa31df4: sub             SP, SP, #0x30
    // 0xa31df8: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x28 */)
    //     0xa31df8: mov             x0, x1
    //     0xa31dfc: stur            x1, [fp, #-0x28]
    // 0xa31e00: LoadField: r1 = r0->field_43
    //     0xa31e00: ldur            w1, [x0, #0x43]
    // 0xa31e04: DecompressPointer r1
    //     0xa31e04: add             x1, x1, HEAP, lsl #32
    // 0xa31e08: tbz             w1, #4, #0xa31e4c
    // 0xa31e0c: LoadField: r1 = r0->field_2b
    //     0xa31e0c: ldur            w1, [x0, #0x2b]
    // 0xa31e10: DecompressPointer r1
    //     0xa31e10: add             x1, x1, HEAP, lsl #32
    // 0xa31e14: r16 = Sentinel
    //     0xa31e14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa31e18: cmp             w1, w16
    // 0xa31e1c: b.eq            #0xa31fb4
    // 0xa31e20: LoadField: r2 = r1->field_43
    //     0xa31e20: ldur            w2, [x1, #0x43]
    // 0xa31e24: DecompressPointer r2
    //     0xa31e24: add             x2, x2, HEAP, lsl #32
    // 0xa31e28: r16 = Sentinel
    //     0xa31e28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa31e2c: cmp             w2, w16
    // 0xa31e30: b.eq            #0xa31fc0
    // 0xa31e34: r16 = Instance_AnimationStatus
    //     0xa31e34: ldr             x16, [PP, #0x4ea0]  ; [pp+0x4ea0] Obj!AnimationStatus@e372c1
    // 0xa31e38: cmp             w2, w16
    // 0xa31e3c: r16 = true
    //     0xa31e3c: add             x16, NULL, #0x20  ; true
    // 0xa31e40: r17 = false
    //     0xa31e40: add             x17, NULL, #0x30  ; false
    // 0xa31e44: csel            x1, x16, x17, eq
    // 0xa31e48: b               #0xa31e50
    // 0xa31e4c: r1 = false
    //     0xa31e4c: add             x1, NULL, #0x30  ; false
    // 0xa31e50: stur            x1, [fp, #-0x20]
    // 0xa31e54: tbnz            w1, #4, #0xa31e70
    // 0xa31e58: LoadField: r2 = r0->field_b
    //     0xa31e58: ldur            w2, [x0, #0xb]
    // 0xa31e5c: DecompressPointer r2
    //     0xa31e5c: add             x2, x2, HEAP, lsl #32
    // 0xa31e60: cmp             w2, NULL
    // 0xa31e64: b.eq            #0xa31fc8
    // 0xa31e68: r2 = true
    //     0xa31e68: add             x2, NULL, #0x20  ; true
    // 0xa31e6c: b               #0xa31e74
    // 0xa31e70: r2 = false
    //     0xa31e70: add             x2, NULL, #0x30  ; false
    // 0xa31e74: stur            x2, [fp, #-0x18]
    // 0xa31e78: eor             x3, x1, #0x10
    // 0xa31e7c: stur            x3, [fp, #-0x10]
    // 0xa31e80: LoadField: r4 = r0->field_b
    //     0xa31e80: ldur            w4, [x0, #0xb]
    // 0xa31e84: DecompressPointer r4
    //     0xa31e84: add             x4, x4, HEAP, lsl #32
    // 0xa31e88: cmp             w4, NULL
    // 0xa31e8c: b.eq            #0xa31fcc
    // 0xa31e90: LoadField: r5 = r4->field_1b
    //     0xa31e90: ldur            w5, [x4, #0x1b]
    // 0xa31e94: DecompressPointer r5
    //     0xa31e94: add             x5, x5, HEAP, lsl #32
    // 0xa31e98: stur            x5, [fp, #-8]
    // 0xa31e9c: r0 = Column()
    //     0xa31e9c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa31ea0: mov             x1, x0
    // 0xa31ea4: r0 = Instance_Axis
    //     0xa31ea4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa31ea8: stur            x1, [fp, #-0x30]
    // 0xa31eac: StoreField: r1->field_f = r0
    //     0xa31eac: stur            w0, [x1, #0xf]
    // 0xa31eb0: r0 = Instance_MainAxisAlignment
    //     0xa31eb0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa31eb4: ldr             x0, [x0, #0x730]
    // 0xa31eb8: StoreField: r1->field_13 = r0
    //     0xa31eb8: stur            w0, [x1, #0x13]
    // 0xa31ebc: r0 = Instance_MainAxisSize
    //     0xa31ebc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa31ec0: ldr             x0, [x0, #0x738]
    // 0xa31ec4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa31ec4: stur            w0, [x1, #0x17]
    // 0xa31ec8: r0 = Instance_CrossAxisAlignment
    //     0xa31ec8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa31ecc: ldr             x0, [x0, #0x740]
    // 0xa31ed0: StoreField: r1->field_1b = r0
    //     0xa31ed0: stur            w0, [x1, #0x1b]
    // 0xa31ed4: r0 = Instance_VerticalDirection
    //     0xa31ed4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa31ed8: ldr             x0, [x0, #0x748]
    // 0xa31edc: StoreField: r1->field_23 = r0
    //     0xa31edc: stur            w0, [x1, #0x23]
    // 0xa31ee0: r0 = Instance_Clip
    //     0xa31ee0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa31ee4: ldr             x0, [x0, #0x750]
    // 0xa31ee8: StoreField: r1->field_2b = r0
    //     0xa31ee8: stur            w0, [x1, #0x2b]
    // 0xa31eec: StoreField: r1->field_2f = rZR
    //     0xa31eec: stur            xzr, [x1, #0x2f]
    // 0xa31ef0: ldur            x0, [fp, #-8]
    // 0xa31ef4: StoreField: r1->field_b = r0
    //     0xa31ef4: stur            w0, [x1, #0xb]
    // 0xa31ef8: r0 = Padding()
    //     0xa31ef8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa31efc: mov             x1, x0
    // 0xa31f00: r0 = Instance_EdgeInsets
    //     0xa31f00: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa31f04: stur            x1, [fp, #-8]
    // 0xa31f08: StoreField: r1->field_f = r0
    //     0xa31f08: stur            w0, [x1, #0xf]
    // 0xa31f0c: ldur            x0, [fp, #-0x30]
    // 0xa31f10: StoreField: r1->field_b = r0
    //     0xa31f10: stur            w0, [x1, #0xb]
    // 0xa31f14: r0 = TickerMode()
    //     0xa31f14: bl              #0x9f02a4  ; AllocateTickerModeStub -> TickerMode (size=0x14)
    // 0xa31f18: mov             x1, x0
    // 0xa31f1c: ldur            x0, [fp, #-0x10]
    // 0xa31f20: stur            x1, [fp, #-0x30]
    // 0xa31f24: StoreField: r1->field_b = r0
    //     0xa31f24: stur            w0, [x1, #0xb]
    // 0xa31f28: ldur            x0, [fp, #-8]
    // 0xa31f2c: StoreField: r1->field_f = r0
    //     0xa31f2c: stur            w0, [x1, #0xf]
    // 0xa31f30: r0 = Offstage()
    //     0xa31f30: bl              #0x9f0298  ; AllocateOffstageStub -> Offstage (size=0x14)
    // 0xa31f34: mov             x1, x0
    // 0xa31f38: ldur            x0, [fp, #-0x20]
    // 0xa31f3c: StoreField: r1->field_f = r0
    //     0xa31f3c: stur            w0, [x1, #0xf]
    // 0xa31f40: ldur            x0, [fp, #-0x30]
    // 0xa31f44: StoreField: r1->field_b = r0
    //     0xa31f44: stur            w0, [x1, #0xb]
    // 0xa31f48: ldur            x2, [fp, #-0x28]
    // 0xa31f4c: LoadField: r0 = r2->field_2b
    //     0xa31f4c: ldur            w0, [x2, #0x2b]
    // 0xa31f50: DecompressPointer r0
    //     0xa31f50: add             x0, x0, HEAP, lsl #32
    // 0xa31f54: r16 = Sentinel
    //     0xa31f54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa31f58: cmp             w0, w16
    // 0xa31f5c: b.eq            #0xa31fd0
    // 0xa31f60: ldur            x3, [fp, #-0x18]
    // 0xa31f64: stur            x0, [fp, #-0x10]
    // 0xa31f68: tbnz            w3, #4, #0xa31f74
    // 0xa31f6c: r3 = Null
    //     0xa31f6c: mov             x3, NULL
    // 0xa31f70: b               #0xa31f78
    // 0xa31f74: mov             x3, x1
    // 0xa31f78: stur            x3, [fp, #-8]
    // 0xa31f7c: r1 = Function '_buildChildren@**********':.
    //     0xa31f7c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41570] AnonymousClosure: (0xa31fdc), in [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_buildChildren (0xa3201c)
    //     0xa31f80: ldr             x1, [x1, #0x570]
    // 0xa31f84: r0 = AllocateClosure()
    //     0xa31f84: bl              #0xec1630  ; AllocateClosureStub
    // 0xa31f88: stur            x0, [fp, #-0x18]
    // 0xa31f8c: r0 = AnimatedBuilder()
    //     0xa31f8c: bl              #0x7e5888  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0xa31f90: ldur            x1, [fp, #-0x18]
    // 0xa31f94: StoreField: r0->field_f = r1
    //     0xa31f94: stur            w1, [x0, #0xf]
    // 0xa31f98: ldur            x1, [fp, #-8]
    // 0xa31f9c: StoreField: r0->field_13 = r1
    //     0xa31f9c: stur            w1, [x0, #0x13]
    // 0xa31fa0: ldur            x1, [fp, #-0x10]
    // 0xa31fa4: StoreField: r0->field_b = r1
    //     0xa31fa4: stur            w1, [x0, #0xb]
    // 0xa31fa8: LeaveFrame
    //     0xa31fa8: mov             SP, fp
    //     0xa31fac: ldp             fp, lr, [SP], #0x10
    // 0xa31fb0: ret
    //     0xa31fb0: ret             
    // 0xa31fb4: r9 = _controller
    //     0xa31fb4: add             x9, PP, #0x41, lsl #12  ; [pp+0x41578] Field <_ExpansionTileState@**********._controller@**********>: late (offset: 0x2c)
    //     0xa31fb8: ldr             x9, [x9, #0x578]
    // 0xa31fbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa31fbc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa31fc0: r9 = _status
    //     0xa31fc0: ldr             x9, [PP, #0x4ed8]  ; [pp+0x4ed8] Field <AnimationController._status@441066280>: late (offset: 0x44)
    // 0xa31fc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa31fc4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa31fc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa31fc8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa31fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa31fcc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa31fd0: r9 = _controller
    //     0xa31fd0: add             x9, PP, #0x41, lsl #12  ; [pp+0x41578] Field <_ExpansionTileState@**********._controller@**********>: late (offset: 0x2c)
    //     0xa31fd4: ldr             x9, [x9, #0x578]
    // 0xa31fd8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa31fd8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _buildChildren(dynamic, BuildContext, Widget?) {
    // ** addr: 0xa31fdc, size: 0x40
    // 0xa31fdc: EnterFrame
    //     0xa31fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xa31fe0: mov             fp, SP
    // 0xa31fe4: ldr             x0, [fp, #0x20]
    // 0xa31fe8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa31fe8: ldur            w1, [x0, #0x17]
    // 0xa31fec: DecompressPointer r1
    //     0xa31fec: add             x1, x1, HEAP, lsl #32
    // 0xa31ff0: CheckStackOverflow
    //     0xa31ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa31ff4: cmp             SP, x16
    //     0xa31ff8: b.ls            #0xa32014
    // 0xa31ffc: ldr             x2, [fp, #0x18]
    // 0xa32000: ldr             x3, [fp, #0x10]
    // 0xa32004: r0 = _buildChildren()
    //     0xa32004: bl              #0xa3201c  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_buildChildren
    // 0xa32008: LeaveFrame
    //     0xa32008: mov             SP, fp
    //     0xa3200c: ldp             fp, lr, [SP], #0x10
    // 0xa32010: ret
    //     0xa32010: ret             
    // 0xa32014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa32014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa32018: b               #0xa31ffc
  }
  _ _buildChildren(/* No info */) {
    // ** addr: 0xa3201c, size: 0x3f4
    // 0xa3201c: EnterFrame
    //     0xa3201c: stp             fp, lr, [SP, #-0x10]!
    //     0xa32020: mov             fp, SP
    // 0xa32024: AllocStack(0x60)
    //     0xa32024: sub             SP, SP, #0x60
    // 0xa32028: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa32028: mov             x0, x1
    //     0xa3202c: stur            x1, [fp, #-8]
    //     0xa32030: stur            x3, [fp, #-0x10]
    // 0xa32034: CheckStackOverflow
    //     0xa32034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa32038: cmp             SP, x16
    //     0xa3203c: b.ls            #0xa323c4
    // 0xa32040: LoadField: r1 = r0->field_3f
    //     0xa32040: ldur            w1, [x0, #0x3f]
    // 0xa32044: DecompressPointer r1
    //     0xa32044: add             x1, x1, HEAP, lsl #32
    // 0xa32048: r16 = Sentinel
    //     0xa32048: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3204c: cmp             w1, w16
    // 0xa32050: b.eq            #0xa323cc
    // 0xa32054: LoadField: r2 = r1->field_f
    //     0xa32054: ldur            w2, [x1, #0xf]
    // 0xa32058: DecompressPointer r2
    //     0xa32058: add             x2, x2, HEAP, lsl #32
    // 0xa3205c: LoadField: r4 = r1->field_b
    //     0xa3205c: ldur            w4, [x1, #0xb]
    // 0xa32060: DecompressPointer r4
    //     0xa32060: add             x4, x4, HEAP, lsl #32
    // 0xa32064: mov             x1, x2
    // 0xa32068: mov             x2, x4
    // 0xa3206c: r0 = evaluate()
    //     0xa3206c: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xa32070: cmp             w0, NULL
    // 0xa32074: b.ne            #0xa3207c
    // 0xa32078: r0 = Instance_Color
    //     0xa32078: ldr             x0, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xa3207c: ldur            x2, [fp, #-8]
    // 0xa32080: stur            x0, [fp, #-0x18]
    // 0xa32084: r0 = BoxDecoration()
    //     0xa32084: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa32088: mov             x3, x0
    // 0xa3208c: ldur            x0, [fp, #-0x18]
    // 0xa32090: stur            x3, [fp, #-0x20]
    // 0xa32094: StoreField: r3->field_7 = r0
    //     0xa32094: stur            w0, [x3, #7]
    // 0xa32098: r0 = Instance_BoxShape
    //     0xa32098: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa3209c: ldr             x0, [x0, #0xca8]
    // 0xa320a0: StoreField: r3->field_23 = r0
    //     0xa320a0: stur            w0, [x3, #0x23]
    // 0xa320a4: ldur            x0, [fp, #-8]
    // 0xa320a8: LoadField: r1 = r0->field_3b
    //     0xa320a8: ldur            w1, [x0, #0x3b]
    // 0xa320ac: DecompressPointer r1
    //     0xa320ac: add             x1, x1, HEAP, lsl #32
    // 0xa320b0: r16 = Sentinel
    //     0xa320b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa320b4: cmp             w1, w16
    // 0xa320b8: b.eq            #0xa323d8
    // 0xa320bc: LoadField: r2 = r1->field_f
    //     0xa320bc: ldur            w2, [x1, #0xf]
    // 0xa320c0: DecompressPointer r2
    //     0xa320c0: add             x2, x2, HEAP, lsl #32
    // 0xa320c4: LoadField: r4 = r1->field_b
    //     0xa320c4: ldur            w4, [x1, #0xb]
    // 0xa320c8: DecompressPointer r4
    //     0xa320c8: add             x4, x4, HEAP, lsl #32
    // 0xa320cc: mov             x1, x2
    // 0xa320d0: mov             x2, x4
    // 0xa320d4: r0 = evaluate()
    //     0xa320d4: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xa320d8: mov             x3, x0
    // 0xa320dc: ldur            x0, [fp, #-8]
    // 0xa320e0: stur            x3, [fp, #-0x18]
    // 0xa320e4: LoadField: r1 = r0->field_37
    //     0xa320e4: ldur            w1, [x0, #0x37]
    // 0xa320e8: DecompressPointer r1
    //     0xa320e8: add             x1, x1, HEAP, lsl #32
    // 0xa320ec: r16 = Sentinel
    //     0xa320ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa320f0: cmp             w1, w16
    // 0xa320f4: b.eq            #0xa323e4
    // 0xa320f8: LoadField: r2 = r1->field_f
    //     0xa320f8: ldur            w2, [x1, #0xf]
    // 0xa320fc: DecompressPointer r2
    //     0xa320fc: add             x2, x2, HEAP, lsl #32
    // 0xa32100: LoadField: r4 = r1->field_b
    //     0xa32100: ldur            w4, [x1, #0xb]
    // 0xa32104: DecompressPointer r4
    //     0xa32104: add             x4, x4, HEAP, lsl #32
    // 0xa32108: mov             x1, x2
    // 0xa3210c: mov             x2, x4
    // 0xa32110: r0 = evaluate()
    //     0xa32110: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xa32114: ldur            x2, [fp, #-8]
    // 0xa32118: stur            x0, [fp, #-0x48]
    // 0xa3211c: LoadField: r1 = r2->field_b
    //     0xa3211c: ldur            w1, [x2, #0xb]
    // 0xa32120: DecompressPointer r1
    //     0xa32120: add             x1, x1, HEAP, lsl #32
    // 0xa32124: cmp             w1, NULL
    // 0xa32128: b.eq            #0xa323f0
    // 0xa3212c: LoadField: r3 = r1->field_53
    //     0xa3212c: ldur            w3, [x1, #0x53]
    // 0xa32130: DecompressPointer r3
    //     0xa32130: add             x3, x3, HEAP, lsl #32
    // 0xa32134: stur            x3, [fp, #-0x40]
    // 0xa32138: LoadField: r4 = r1->field_33
    //     0xa32138: ldur            w4, [x1, #0x33]
    // 0xa3213c: DecompressPointer r4
    //     0xa3213c: add             x4, x4, HEAP, lsl #32
    // 0xa32140: stur            x4, [fp, #-0x38]
    // 0xa32144: LoadField: r5 = r1->field_f
    //     0xa32144: ldur            w5, [x1, #0xf]
    // 0xa32148: DecompressPointer r5
    //     0xa32148: add             x5, x5, HEAP, lsl #32
    // 0xa3214c: stur            x5, [fp, #-0x30]
    // 0xa32150: LoadField: r1 = r2->field_2f
    //     0xa32150: ldur            w1, [x2, #0x2f]
    // 0xa32154: DecompressPointer r1
    //     0xa32154: add             x1, x1, HEAP, lsl #32
    // 0xa32158: r16 = Sentinel
    //     0xa32158: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3215c: cmp             w1, w16
    // 0xa32160: b.eq            #0xa323f4
    // 0xa32164: stur            x1, [fp, #-0x28]
    // 0xa32168: r0 = RotationTransition()
    //     0xa32168: bl              #0x9f0c10  ; AllocateRotationTransitionStub -> RotationTransition (size=0x20)
    // 0xa3216c: mov             x1, x0
    // 0xa32170: r0 = Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static.
    //     0xa32170: add             x0, PP, #0x41, lsl #12  ; [pp+0x41580] Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@367170175': static. (0x7e54fb3f0c1c)
    //     0xa32174: ldr             x0, [x0, #0x580]
    // 0xa32178: stur            x1, [fp, #-0x50]
    // 0xa3217c: StoreField: r1->field_f = r0
    //     0xa3217c: stur            w0, [x1, #0xf]
    // 0xa32180: r0 = Instance_Alignment
    //     0xa32180: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa32184: ldr             x0, [x0, #0x898]
    // 0xa32188: StoreField: r1->field_13 = r0
    //     0xa32188: stur            w0, [x1, #0x13]
    // 0xa3218c: r2 = Instance_Icon
    //     0xa3218c: add             x2, PP, #0x41, lsl #12  ; [pp+0x41588] Obj!Icon@e24071
    //     0xa32190: ldr             x2, [x2, #0x588]
    // 0xa32194: StoreField: r1->field_1b = r2
    //     0xa32194: stur            w2, [x1, #0x1b]
    // 0xa32198: ldur            x2, [fp, #-0x28]
    // 0xa3219c: StoreField: r1->field_b = r2
    //     0xa3219c: stur            w2, [x1, #0xb]
    // 0xa321a0: r0 = ListTile()
    //     0xa321a0: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xa321a4: mov             x3, x0
    // 0xa321a8: ldur            x0, [fp, #-0x30]
    // 0xa321ac: stur            x3, [fp, #-0x28]
    // 0xa321b0: StoreField: r3->field_f = r0
    //     0xa321b0: stur            w0, [x3, #0xf]
    // 0xa321b4: ldur            x0, [fp, #-0x50]
    // 0xa321b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xa321b8: stur            w0, [x3, #0x17]
    // 0xa321bc: r0 = false
    //     0xa321bc: add             x0, NULL, #0x30  ; false
    // 0xa321c0: StoreField: r3->field_1b = r0
    //     0xa321c0: stur            w0, [x3, #0x1b]
    // 0xa321c4: ldur            x1, [fp, #-0x38]
    // 0xa321c8: StoreField: r3->field_47 = r1
    //     0xa321c8: stur            w1, [x3, #0x47]
    // 0xa321cc: r4 = true
    //     0xa321cc: add             x4, NULL, #0x20  ; true
    // 0xa321d0: StoreField: r3->field_4b = r4
    //     0xa321d0: stur            w4, [x3, #0x4b]
    // 0xa321d4: ldur            x2, [fp, #-8]
    // 0xa321d8: r1 = Function '_handleTap@**********':.
    //     0xa321d8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41590] AnonymousClosure: (0xa32410), in [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_handleTap (0xa32448)
    //     0xa321dc: ldr             x1, [x1, #0x590]
    // 0xa321e0: r0 = AllocateClosure()
    //     0xa321e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa321e4: ldur            x1, [fp, #-0x28]
    // 0xa321e8: StoreField: r1->field_4f = r0
    //     0xa321e8: stur            w0, [x1, #0x4f]
    // 0xa321ec: r0 = false
    //     0xa321ec: add             x0, NULL, #0x30  ; false
    // 0xa321f0: StoreField: r1->field_5f = r0
    //     0xa321f0: stur            w0, [x1, #0x5f]
    // 0xa321f4: StoreField: r1->field_73 = r0
    //     0xa321f4: stur            w0, [x1, #0x73]
    // 0xa321f8: ldur            x0, [fp, #-0x40]
    // 0xa321fc: StoreField: r1->field_77 = r0
    //     0xa321fc: stur            w0, [x1, #0x77]
    // 0xa32200: r0 = true
    //     0xa32200: add             x0, NULL, #0x20  ; true
    // 0xa32204: StoreField: r1->field_97 = r0
    //     0xa32204: stur            w0, [x1, #0x97]
    // 0xa32208: ldur            x2, [fp, #-0x18]
    // 0xa3220c: ldur            x3, [fp, #-0x48]
    // 0xa32210: r0 = merge()
    //     0xa32210: bl              #0x9f0820  ; [package:flutter/src/material/list_tile_theme.dart] ListTileTheme::merge
    // 0xa32214: mov             x1, x0
    // 0xa32218: ldur            x0, [fp, #-8]
    // 0xa3221c: stur            x1, [fp, #-0x28]
    // 0xa32220: LoadField: r2 = r0->field_b
    //     0xa32220: ldur            w2, [x0, #0xb]
    // 0xa32224: DecompressPointer r2
    //     0xa32224: add             x2, x2, HEAP, lsl #32
    // 0xa32228: cmp             w2, NULL
    // 0xa3222c: b.eq            #0xa32400
    // 0xa32230: LoadField: r3 = r2->field_33
    //     0xa32230: ldur            w3, [x2, #0x33]
    // 0xa32234: DecompressPointer r3
    //     0xa32234: add             x3, x3, HEAP, lsl #32
    // 0xa32238: cmp             w3, NULL
    // 0xa3223c: b.ne            #0xa32244
    // 0xa32240: r3 = Instance_EdgeInsets
    //     0xa32240: ldr             x3, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa32244: ldur            x2, [fp, #-0x10]
    // 0xa32248: stur            x3, [fp, #-0x18]
    // 0xa3224c: r0 = Padding()
    //     0xa3224c: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa32250: mov             x3, x0
    // 0xa32254: ldur            x0, [fp, #-0x18]
    // 0xa32258: stur            x3, [fp, #-0x30]
    // 0xa3225c: StoreField: r3->field_f = r0
    //     0xa3225c: stur            w0, [x3, #0xf]
    // 0xa32260: r0 = Instance_Divider
    //     0xa32260: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xa32264: ldr             x0, [x0, #0xc28]
    // 0xa32268: StoreField: r3->field_b = r0
    //     0xa32268: stur            w0, [x3, #0xb]
    // 0xa3226c: ldur            x0, [fp, #-8]
    // 0xa32270: LoadField: r1 = r0->field_33
    //     0xa32270: ldur            w1, [x0, #0x33]
    // 0xa32274: DecompressPointer r1
    //     0xa32274: add             x1, x1, HEAP, lsl #32
    // 0xa32278: r16 = Sentinel
    //     0xa32278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa3227c: cmp             w1, w16
    // 0xa32280: b.eq            #0xa32404
    // 0xa32284: LoadField: r0 = r1->field_f
    //     0xa32284: ldur            w0, [x1, #0xf]
    // 0xa32288: DecompressPointer r0
    //     0xa32288: add             x0, x0, HEAP, lsl #32
    // 0xa3228c: LoadField: r2 = r1->field_b
    //     0xa3228c: ldur            w2, [x1, #0xb]
    // 0xa32290: DecompressPointer r2
    //     0xa32290: add             x2, x2, HEAP, lsl #32
    // 0xa32294: mov             x1, x0
    // 0xa32298: r0 = evaluate()
    //     0xa32298: bl              #0x6d4d30  ; [package:flutter/src/animation/tween.dart] Animatable::evaluate
    // 0xa3229c: stur            x0, [fp, #-8]
    // 0xa322a0: r0 = Align()
    //     0xa322a0: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa322a4: mov             x1, x0
    // 0xa322a8: r0 = Instance_Alignment
    //     0xa322a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa322ac: ldr             x0, [x0, #0x898]
    // 0xa322b0: stur            x1, [fp, #-0x18]
    // 0xa322b4: StoreField: r1->field_f = r0
    //     0xa322b4: stur            w0, [x1, #0xf]
    // 0xa322b8: ldur            x0, [fp, #-8]
    // 0xa322bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa322bc: stur            w0, [x1, #0x17]
    // 0xa322c0: ldur            x0, [fp, #-0x10]
    // 0xa322c4: StoreField: r1->field_b = r0
    //     0xa322c4: stur            w0, [x1, #0xb]
    // 0xa322c8: r0 = ClipRect()
    //     0xa322c8: bl              #0x9e6a74  ; AllocateClipRectStub -> ClipRect (size=0x18)
    // 0xa322cc: mov             x3, x0
    // 0xa322d0: r0 = Instance_Clip
    //     0xa322d0: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa322d4: ldr             x0, [x0, #0x7c0]
    // 0xa322d8: stur            x3, [fp, #-8]
    // 0xa322dc: StoreField: r3->field_13 = r0
    //     0xa322dc: stur            w0, [x3, #0x13]
    // 0xa322e0: ldur            x0, [fp, #-0x18]
    // 0xa322e4: StoreField: r3->field_b = r0
    //     0xa322e4: stur            w0, [x3, #0xb]
    // 0xa322e8: r1 = Null
    //     0xa322e8: mov             x1, NULL
    // 0xa322ec: r2 = 6
    //     0xa322ec: movz            x2, #0x6
    // 0xa322f0: r0 = AllocateArray()
    //     0xa322f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa322f4: mov             x2, x0
    // 0xa322f8: ldur            x0, [fp, #-0x28]
    // 0xa322fc: stur            x2, [fp, #-0x10]
    // 0xa32300: StoreField: r2->field_f = r0
    //     0xa32300: stur            w0, [x2, #0xf]
    // 0xa32304: ldur            x0, [fp, #-0x30]
    // 0xa32308: StoreField: r2->field_13 = r0
    //     0xa32308: stur            w0, [x2, #0x13]
    // 0xa3230c: ldur            x0, [fp, #-8]
    // 0xa32310: ArrayStore: r2[0] = r0  ; List_4
    //     0xa32310: stur            w0, [x2, #0x17]
    // 0xa32314: r1 = <Widget>
    //     0xa32314: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa32318: r0 = AllocateGrowableArray()
    //     0xa32318: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3231c: mov             x1, x0
    // 0xa32320: ldur            x0, [fp, #-0x10]
    // 0xa32324: stur            x1, [fp, #-8]
    // 0xa32328: StoreField: r1->field_f = r0
    //     0xa32328: stur            w0, [x1, #0xf]
    // 0xa3232c: r0 = 6
    //     0xa3232c: movz            x0, #0x6
    // 0xa32330: StoreField: r1->field_b = r0
    //     0xa32330: stur            w0, [x1, #0xb]
    // 0xa32334: r0 = Column()
    //     0xa32334: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa32338: mov             x1, x0
    // 0xa3233c: r0 = Instance_Axis
    //     0xa3233c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa32340: stur            x1, [fp, #-0x10]
    // 0xa32344: StoreField: r1->field_f = r0
    //     0xa32344: stur            w0, [x1, #0xf]
    // 0xa32348: r0 = Instance_MainAxisAlignment
    //     0xa32348: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa3234c: ldr             x0, [x0, #0x730]
    // 0xa32350: StoreField: r1->field_13 = r0
    //     0xa32350: stur            w0, [x1, #0x13]
    // 0xa32354: r0 = Instance_MainAxisSize
    //     0xa32354: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xa32358: ldr             x0, [x0, #0xe88]
    // 0xa3235c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3235c: stur            w0, [x1, #0x17]
    // 0xa32360: r0 = Instance_CrossAxisAlignment
    //     0xa32360: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa32364: ldr             x0, [x0, #0x740]
    // 0xa32368: StoreField: r1->field_1b = r0
    //     0xa32368: stur            w0, [x1, #0x1b]
    // 0xa3236c: r0 = Instance_VerticalDirection
    //     0xa3236c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa32370: ldr             x0, [x0, #0x748]
    // 0xa32374: StoreField: r1->field_23 = r0
    //     0xa32374: stur            w0, [x1, #0x23]
    // 0xa32378: r0 = Instance_Clip
    //     0xa32378: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa3237c: ldr             x0, [x0, #0x750]
    // 0xa32380: StoreField: r1->field_2b = r0
    //     0xa32380: stur            w0, [x1, #0x2b]
    // 0xa32384: StoreField: r1->field_2f = rZR
    //     0xa32384: stur            xzr, [x1, #0x2f]
    // 0xa32388: ldur            x0, [fp, #-8]
    // 0xa3238c: StoreField: r1->field_b = r0
    //     0xa3238c: stur            w0, [x1, #0xb]
    // 0xa32390: r0 = Container()
    //     0xa32390: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa32394: stur            x0, [fp, #-8]
    // 0xa32398: ldur            x16, [fp, #-0x20]
    // 0xa3239c: ldur            lr, [fp, #-0x10]
    // 0xa323a0: stp             lr, x16, [SP]
    // 0xa323a4: mov             x1, x0
    // 0xa323a8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa323a8: add             x4, PP, #0x29, lsl #12  ; [pp+0x29030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa323ac: ldr             x4, [x4, #0x30]
    // 0xa323b0: r0 = Container()
    //     0xa323b0: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa323b4: ldur            x0, [fp, #-8]
    // 0xa323b8: LeaveFrame
    //     0xa323b8: mov             SP, fp
    //     0xa323bc: ldp             fp, lr, [SP], #0x10
    // 0xa323c0: ret
    //     0xa323c0: ret             
    // 0xa323c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa323c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa323c8: b               #0xa32040
    // 0xa323cc: r9 = _backgroundColor
    //     0xa323cc: add             x9, PP, #0x41, lsl #12  ; [pp+0x41598] Field <_ExpansionTileState@**********._backgroundColor@**********>: late (offset: 0x40)
    //     0xa323d0: ldr             x9, [x9, #0x598]
    // 0xa323d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa323d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa323d8: r9 = _iconColor
    //     0xa323d8: add             x9, PP, #0x41, lsl #12  ; [pp+0x415a0] Field <_ExpansionTileState@**********._iconColor@**********>: late (offset: 0x3c)
    //     0xa323dc: ldr             x9, [x9, #0x5a0]
    // 0xa323e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa323e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa323e4: r9 = _headerColor
    //     0xa323e4: add             x9, PP, #0x41, lsl #12  ; [pp+0x415a8] Field <_ExpansionTileState@**********._headerColor@**********>: late (offset: 0x38)
    //     0xa323e8: ldr             x9, [x9, #0x5a8]
    // 0xa323ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa323ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa323f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa323f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa323f4: r9 = _iconTurns
    //     0xa323f4: add             x9, PP, #0x41, lsl #12  ; [pp+0x415b0] Field <_ExpansionTileState@**********._iconTurns@**********>: late (offset: 0x30)
    //     0xa323f8: ldr             x9, [x9, #0x5b0]
    // 0xa323fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa323fc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa32400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa32400: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa32404: r9 = _heightFactor
    //     0xa32404: add             x9, PP, #0x41, lsl #12  ; [pp+0x415b8] Field <_ExpansionTileState@**********._heightFactor@**********>: late (offset: 0x34)
    //     0xa32408: ldr             x9, [x9, #0x5b8]
    // 0xa3240c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa3240c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _handleTap(dynamic) {
    // ** addr: 0xa32410, size: 0x38
    // 0xa32410: EnterFrame
    //     0xa32410: stp             fp, lr, [SP, #-0x10]!
    //     0xa32414: mov             fp, SP
    // 0xa32418: ldr             x0, [fp, #0x10]
    // 0xa3241c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa3241c: ldur            w1, [x0, #0x17]
    // 0xa32420: DecompressPointer r1
    //     0xa32420: add             x1, x1, HEAP, lsl #32
    // 0xa32424: CheckStackOverflow
    //     0xa32424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa32428: cmp             SP, x16
    //     0xa3242c: b.ls            #0xa32440
    // 0xa32430: r0 = _handleTap()
    //     0xa32430: bl              #0xa32448  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_handleTap
    // 0xa32434: LeaveFrame
    //     0xa32434: mov             SP, fp
    //     0xa32438: ldp             fp, lr, [SP], #0x10
    // 0xa3243c: ret
    //     0xa3243c: ret             
    // 0xa32440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa32440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa32444: b               #0xa32430
  }
  _ _handleTap(/* No info */) {
    // ** addr: 0xa32448, size: 0xa8
    // 0xa32448: EnterFrame
    //     0xa32448: stp             fp, lr, [SP, #-0x10]!
    //     0xa3244c: mov             fp, SP
    // 0xa32450: AllocStack(0x18)
    //     0xa32450: sub             SP, SP, #0x18
    // 0xa32454: SetupParameters(_ExpansionTileState this /* r1 => r1, fp-0x8 */)
    //     0xa32454: stur            x1, [fp, #-8]
    // 0xa32458: CheckStackOverflow
    //     0xa32458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3245c: cmp             SP, x16
    //     0xa32460: b.ls            #0xa324e4
    // 0xa32464: r1 = 1
    //     0xa32464: movz            x1, #0x1
    // 0xa32468: r0 = AllocateContext()
    //     0xa32468: bl              #0xec126c  ; AllocateContextStub
    // 0xa3246c: mov             x1, x0
    // 0xa32470: ldur            x0, [fp, #-8]
    // 0xa32474: StoreField: r1->field_f = r0
    //     0xa32474: stur            w0, [x1, #0xf]
    // 0xa32478: mov             x2, x1
    // 0xa3247c: r1 = Function '<anonymous closure>':.
    //     0xa3247c: add             x1, PP, #0x41, lsl #12  ; [pp+0x415c0] AnonymousClosure: (0xa324f0), in [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_handleTap (0xa32448)
    //     0xa32480: ldr             x1, [x1, #0x5c0]
    // 0xa32484: r0 = AllocateClosure()
    //     0xa32484: bl              #0xec1630  ; AllocateClosureStub
    // 0xa32488: ldur            x1, [fp, #-8]
    // 0xa3248c: mov             x2, x0
    // 0xa32490: r0 = setState()
    //     0xa32490: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa32494: ldur            x0, [fp, #-8]
    // 0xa32498: LoadField: r1 = r0->field_b
    //     0xa32498: ldur            w1, [x0, #0xb]
    // 0xa3249c: DecompressPointer r1
    //     0xa3249c: add             x1, x1, HEAP, lsl #32
    // 0xa324a0: cmp             w1, NULL
    // 0xa324a4: b.eq            #0xa324ec
    // 0xa324a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa324a8: ldur            w2, [x1, #0x17]
    // 0xa324ac: DecompressPointer r2
    //     0xa324ac: add             x2, x2, HEAP, lsl #32
    // 0xa324b0: cmp             w2, NULL
    // 0xa324b4: b.eq            #0xa324d4
    // 0xa324b8: LoadField: r1 = r0->field_43
    //     0xa324b8: ldur            w1, [x0, #0x43]
    // 0xa324bc: DecompressPointer r1
    //     0xa324bc: add             x1, x1, HEAP, lsl #32
    // 0xa324c0: stp             x1, x2, [SP]
    // 0xa324c4: mov             x0, x2
    // 0xa324c8: ClosureCall
    //     0xa324c8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa324cc: ldur            x2, [x0, #0x1f]
    //     0xa324d0: blr             x2
    // 0xa324d4: r0 = Null
    //     0xa324d4: mov             x0, NULL
    // 0xa324d8: LeaveFrame
    //     0xa324d8: mov             SP, fp
    //     0xa324dc: ldp             fp, lr, [SP], #0x10
    // 0xa324e0: ret
    //     0xa324e0: ret             
    // 0xa324e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa324e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa324e8: b               #0xa32464
    // 0xa324ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa324ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa324f0, size: 0x138
    // 0xa324f0: EnterFrame
    //     0xa324f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa324f4: mov             fp, SP
    // 0xa324f8: AllocStack(0x28)
    //     0xa324f8: sub             SP, SP, #0x28
    // 0xa324fc: SetupParameters()
    //     0xa324fc: ldr             x0, [fp, #0x10]
    //     0xa32500: ldur            w2, [x0, #0x17]
    //     0xa32504: add             x2, x2, HEAP, lsl #32
    //     0xa32508: stur            x2, [fp, #-8]
    // 0xa3250c: CheckStackOverflow
    //     0xa3250c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa32510: cmp             SP, x16
    //     0xa32514: b.ls            #0xa32600
    // 0xa32518: LoadField: r0 = r2->field_f
    //     0xa32518: ldur            w0, [x2, #0xf]
    // 0xa3251c: DecompressPointer r0
    //     0xa3251c: add             x0, x0, HEAP, lsl #32
    // 0xa32520: LoadField: r1 = r0->field_43
    //     0xa32520: ldur            w1, [x0, #0x43]
    // 0xa32524: DecompressPointer r1
    //     0xa32524: add             x1, x1, HEAP, lsl #32
    // 0xa32528: eor             x3, x1, #0x10
    // 0xa3252c: StoreField: r0->field_43 = r3
    //     0xa3252c: stur            w3, [x0, #0x43]
    // 0xa32530: tbnz            w3, #4, #0xa32554
    // 0xa32534: LoadField: r1 = r0->field_2b
    //     0xa32534: ldur            w1, [x0, #0x2b]
    // 0xa32538: DecompressPointer r1
    //     0xa32538: add             x1, x1, HEAP, lsl #32
    // 0xa3253c: r16 = Sentinel
    //     0xa3253c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa32540: cmp             w1, w16
    // 0xa32544: b.eq            #0xa32608
    // 0xa32548: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa32548: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa3254c: r0 = forward()
    //     0xa3254c: bl              #0x656f90  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xa32550: b               #0xa3259c
    // 0xa32554: LoadField: r1 = r0->field_2b
    //     0xa32554: ldur            w1, [x0, #0x2b]
    // 0xa32558: DecompressPointer r1
    //     0xa32558: add             x1, x1, HEAP, lsl #32
    // 0xa3255c: r16 = Sentinel
    //     0xa3255c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa32560: cmp             w1, w16
    // 0xa32564: b.eq            #0xa32614
    // 0xa32568: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa32568: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa3256c: r0 = reverse()
    //     0xa3256c: bl              #0x6550d4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xa32570: ldur            x2, [fp, #-8]
    // 0xa32574: r1 = Function '<anonymous closure>':.
    //     0xa32574: add             x1, PP, #0x41, lsl #12  ; [pp+0x415c8] AnonymousClosure: (0xa32628), in [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_handleTap (0xa32448)
    //     0xa32578: ldr             x1, [x1, #0x5c8]
    // 0xa3257c: stur            x0, [fp, #-0x10]
    // 0xa32580: r0 = AllocateClosure()
    //     0xa32580: bl              #0xec1630  ; AllocateClosureStub
    // 0xa32584: r16 = <void?>
    //     0xa32584: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xa32588: ldur            lr, [fp, #-0x10]
    // 0xa3258c: stp             lr, x16, [SP, #8]
    // 0xa32590: str             x0, [SP]
    // 0xa32594: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa32594: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa32598: r0 = then()
    //     0xa32598: bl              #0xdb1ab8  ; [package:flutter/src/scheduler/ticker.dart] TickerFuture::then
    // 0xa3259c: ldur            x0, [fp, #-8]
    // 0xa325a0: LoadField: r1 = r0->field_f
    //     0xa325a0: ldur            w1, [x0, #0xf]
    // 0xa325a4: DecompressPointer r1
    //     0xa325a4: add             x1, x1, HEAP, lsl #32
    // 0xa325a8: LoadField: r2 = r1->field_f
    //     0xa325a8: ldur            w2, [x1, #0xf]
    // 0xa325ac: DecompressPointer r2
    //     0xa325ac: add             x2, x2, HEAP, lsl #32
    // 0xa325b0: cmp             w2, NULL
    // 0xa325b4: b.eq            #0xa32620
    // 0xa325b8: mov             x1, x2
    // 0xa325bc: r0 = of()
    //     0xa325bc: bl              #0x964ffc  ; [package:flutter/src/widgets/page_storage.dart] PageStorage::of
    // 0xa325c0: mov             x1, x0
    // 0xa325c4: ldur            x0, [fp, #-8]
    // 0xa325c8: LoadField: r2 = r0->field_f
    //     0xa325c8: ldur            w2, [x0, #0xf]
    // 0xa325cc: DecompressPointer r2
    //     0xa325cc: add             x2, x2, HEAP, lsl #32
    // 0xa325d0: LoadField: r0 = r2->field_f
    //     0xa325d0: ldur            w0, [x2, #0xf]
    // 0xa325d4: DecompressPointer r0
    //     0xa325d4: add             x0, x0, HEAP, lsl #32
    // 0xa325d8: cmp             w0, NULL
    // 0xa325dc: b.eq            #0xa32624
    // 0xa325e0: LoadField: r3 = r2->field_43
    //     0xa325e0: ldur            w3, [x2, #0x43]
    // 0xa325e4: DecompressPointer r3
    //     0xa325e4: add             x3, x3, HEAP, lsl #32
    // 0xa325e8: mov             x2, x0
    // 0xa325ec: r0 = writeState()
    //     0xa325ec: bl              #0x679b9c  ; [package:flutter/src/widgets/page_storage.dart] PageStorageBucket::writeState
    // 0xa325f0: r0 = Null
    //     0xa325f0: mov             x0, NULL
    // 0xa325f4: LeaveFrame
    //     0xa325f4: mov             SP, fp
    //     0xa325f8: ldp             fp, lr, [SP], #0x10
    // 0xa325fc: ret
    //     0xa325fc: ret             
    // 0xa32600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa32600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa32604: b               #0xa32518
    // 0xa32608: r9 = _controller
    //     0xa32608: add             x9, PP, #0x41, lsl #12  ; [pp+0x41578] Field <_ExpansionTileState@**********._controller@**********>: late (offset: 0x2c)
    //     0xa3260c: ldr             x9, [x9, #0x578]
    // 0xa32610: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa32610: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa32614: r9 = _controller
    //     0xa32614: add             x9, PP, #0x41, lsl #12  ; [pp+0x41578] Field <_ExpansionTileState@**********._controller@**********>: late (offset: 0x2c)
    //     0xa32618: ldr             x9, [x9, #0x578]
    // 0xa3261c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa3261c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa32620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa32620: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa32624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa32624: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0xa32628, size: 0x84
    // 0xa32628: EnterFrame
    //     0xa32628: stp             fp, lr, [SP, #-0x10]!
    //     0xa3262c: mov             fp, SP
    // 0xa32630: AllocStack(0x8)
    //     0xa32630: sub             SP, SP, #8
    // 0xa32634: SetupParameters()
    //     0xa32634: ldr             x0, [fp, #0x18]
    //     0xa32638: ldur            w1, [x0, #0x17]
    //     0xa3263c: add             x1, x1, HEAP, lsl #32
    // 0xa32640: CheckStackOverflow
    //     0xa32640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa32644: cmp             SP, x16
    //     0xa32648: b.ls            #0xa326a4
    // 0xa3264c: LoadField: r0 = r1->field_f
    //     0xa3264c: ldur            w0, [x1, #0xf]
    // 0xa32650: DecompressPointer r0
    //     0xa32650: add             x0, x0, HEAP, lsl #32
    // 0xa32654: stur            x0, [fp, #-8]
    // 0xa32658: LoadField: r1 = r0->field_f
    //     0xa32658: ldur            w1, [x0, #0xf]
    // 0xa3265c: DecompressPointer r1
    //     0xa3265c: add             x1, x1, HEAP, lsl #32
    // 0xa32660: cmp             w1, NULL
    // 0xa32664: b.ne            #0xa32678
    // 0xa32668: r0 = Null
    //     0xa32668: mov             x0, NULL
    // 0xa3266c: LeaveFrame
    //     0xa3266c: mov             SP, fp
    //     0xa32670: ldp             fp, lr, [SP], #0x10
    // 0xa32674: ret
    //     0xa32674: ret             
    // 0xa32678: r1 = Function '<anonymous closure>':.
    //     0xa32678: add             x1, PP, #0x41, lsl #12  ; [pp+0x415d0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa3267c: ldr             x1, [x1, #0x5d0]
    // 0xa32680: r2 = Null
    //     0xa32680: mov             x2, NULL
    // 0xa32684: r0 = AllocateClosure()
    //     0xa32684: bl              #0xec1630  ; AllocateClosureStub
    // 0xa32688: ldur            x1, [fp, #-8]
    // 0xa3268c: mov             x2, x0
    // 0xa32690: r0 = setState()
    //     0xa32690: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa32694: r0 = Null
    //     0xa32694: mov             x0, NULL
    // 0xa32698: LeaveFrame
    //     0xa32698: mov             SP, fp
    //     0xa3269c: ldp             fp, lr, [SP], #0x10
    // 0xa326a0: ret
    //     0xa326a0: ret             
    // 0xa326a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa326a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa326a8: b               #0xa3264c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa833f0, size: 0x64
    // 0xa833f0: EnterFrame
    //     0xa833f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa833f4: mov             fp, SP
    // 0xa833f8: AllocStack(0x8)
    //     0xa833f8: sub             SP, SP, #8
    // 0xa833fc: SetupParameters(_ExpansionTileState this /* r1 => r0, fp-0x8 */)
    //     0xa833fc: mov             x0, x1
    //     0xa83400: stur            x1, [fp, #-8]
    // 0xa83404: CheckStackOverflow
    //     0xa83404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83408: cmp             SP, x16
    //     0xa8340c: b.ls            #0xa83440
    // 0xa83410: LoadField: r1 = r0->field_2b
    //     0xa83410: ldur            w1, [x0, #0x2b]
    // 0xa83414: DecompressPointer r1
    //     0xa83414: add             x1, x1, HEAP, lsl #32
    // 0xa83418: r16 = Sentinel
    //     0xa83418: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8341c: cmp             w1, w16
    // 0xa83420: b.eq            #0xa83448
    // 0xa83424: r0 = dispose()
    //     0xa83424: bl              #0x7a05b4  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xa83428: ldur            x1, [fp, #-8]
    // 0xa8342c: r0 = dispose()
    //     0xa8342c: bl              #0xa83454  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] __ExpansionTileState&State&SingleTickerProviderStateMixin::dispose
    // 0xa83430: r0 = Null
    //     0xa83430: mov             x0, NULL
    // 0xa83434: LeaveFrame
    //     0xa83434: mov             SP, fp
    //     0xa83438: ldp             fp, lr, [SP], #0x10
    // 0xa8343c: ret
    //     0xa8343c: ret             
    // 0xa83440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83444: b               #0xa83410
    // 0xa83448: r9 = _controller
    //     0xa83448: add             x9, PP, #0x41, lsl #12  ; [pp+0x41578] Field <_ExpansionTileState@**********._controller@**********>: late (offset: 0x2c)
    //     0xa8344c: ldr             x9, [x9, #0x578]
    // 0xa83450: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83450: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _ExpansionTileState(/* No info */) {
    // ** addr: 0xa9432c, size: 0xf8
    // 0xa9432c: EnterFrame
    //     0xa9432c: stp             fp, lr, [SP, #-0x10]!
    //     0xa94330: mov             fp, SP
    // 0xa94334: AllocStack(0x8)
    //     0xa94334: sub             SP, SP, #8
    // 0xa94338: r2 = Sentinel
    //     0xa94338: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9433c: r0 = false
    //     0xa9433c: add             x0, NULL, #0x30  ; false
    // 0xa94340: mov             x3, x1
    // 0xa94344: stur            x1, [fp, #-8]
    // 0xa94348: StoreField: r3->field_2b = r2
    //     0xa94348: stur            w2, [x3, #0x2b]
    // 0xa9434c: StoreField: r3->field_2f = r2
    //     0xa9434c: stur            w2, [x3, #0x2f]
    // 0xa94350: StoreField: r3->field_33 = r2
    //     0xa94350: stur            w2, [x3, #0x33]
    // 0xa94354: StoreField: r3->field_37 = r2
    //     0xa94354: stur            w2, [x3, #0x37]
    // 0xa94358: StoreField: r3->field_3b = r2
    //     0xa94358: stur            w2, [x3, #0x3b]
    // 0xa9435c: StoreField: r3->field_3f = r2
    //     0xa9435c: stur            w2, [x3, #0x3f]
    // 0xa94360: StoreField: r3->field_43 = r0
    //     0xa94360: stur            w0, [x3, #0x43]
    // 0xa94364: r1 = <Color?>
    //     0xa94364: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa94368: ldr             x1, [x1, #0x98]
    // 0xa9436c: r0 = ColorTween()
    //     0xa9436c: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa94370: ldur            x2, [fp, #-8]
    // 0xa94374: StoreField: r2->field_1b = r0
    //     0xa94374: stur            w0, [x2, #0x1b]
    //     0xa94378: ldurb           w16, [x2, #-1]
    //     0xa9437c: ldurb           w17, [x0, #-1]
    //     0xa94380: and             x16, x17, x16, lsr #2
    //     0xa94384: tst             x16, HEAP, lsr #32
    //     0xa94388: b.eq            #0xa94390
    //     0xa9438c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa94390: r1 = <Color?>
    //     0xa94390: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa94394: ldr             x1, [x1, #0x98]
    // 0xa94398: r0 = ColorTween()
    //     0xa94398: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa9439c: ldur            x2, [fp, #-8]
    // 0xa943a0: StoreField: r2->field_1f = r0
    //     0xa943a0: stur            w0, [x2, #0x1f]
    //     0xa943a4: ldurb           w16, [x2, #-1]
    //     0xa943a8: ldurb           w17, [x0, #-1]
    //     0xa943ac: and             x16, x17, x16, lsr #2
    //     0xa943b0: tst             x16, HEAP, lsr #32
    //     0xa943b4: b.eq            #0xa943bc
    //     0xa943b8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa943bc: r1 = <Color?>
    //     0xa943bc: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa943c0: ldr             x1, [x1, #0x98]
    // 0xa943c4: r0 = ColorTween()
    //     0xa943c4: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa943c8: ldur            x2, [fp, #-8]
    // 0xa943cc: StoreField: r2->field_23 = r0
    //     0xa943cc: stur            w0, [x2, #0x23]
    //     0xa943d0: ldurb           w16, [x2, #-1]
    //     0xa943d4: ldurb           w17, [x0, #-1]
    //     0xa943d8: and             x16, x17, x16, lsr #2
    //     0xa943dc: tst             x16, HEAP, lsr #32
    //     0xa943e0: b.eq            #0xa943e8
    //     0xa943e4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa943e8: r1 = <Color?>
    //     0xa943e8: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xa943ec: ldr             x1, [x1, #0x98]
    // 0xa943f0: r0 = ColorTween()
    //     0xa943f0: bl              #0x7e3594  ; AllocateColorTweenStub -> ColorTween (size=0x14)
    // 0xa943f4: ldur            x1, [fp, #-8]
    // 0xa943f8: StoreField: r1->field_27 = r0
    //     0xa943f8: stur            w0, [x1, #0x27]
    //     0xa943fc: ldurb           w16, [x1, #-1]
    //     0xa94400: ldurb           w17, [x0, #-1]
    //     0xa94404: and             x16, x17, x16, lsr #2
    //     0xa94408: tst             x16, HEAP, lsr #32
    //     0xa9440c: b.eq            #0xa94414
    //     0xa94410: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa94414: r0 = Null
    //     0xa94414: mov             x0, NULL
    // 0xa94418: LeaveFrame
    //     0xa94418: mov             SP, fp
    //     0xa9441c: ldp             fp, lr, [SP], #0x10
    // 0xa94420: ret
    //     0xa94420: ret             
  }
}

// class id: 4721, size: 0x58, field offset: 0xc
//   const constructor, 
class NExpansionTile extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa942e4, size: 0x48
    // 0xa942e4: EnterFrame
    //     0xa942e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa942e8: mov             fp, SP
    // 0xa942ec: AllocStack(0x8)
    //     0xa942ec: sub             SP, SP, #8
    // 0xa942f0: CheckStackOverflow
    //     0xa942f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa942f4: cmp             SP, x16
    //     0xa942f8: b.ls            #0xa94324
    // 0xa942fc: r1 = <NExpansionTile>
    //     0xa942fc: add             x1, PP, #0x38, lsl #12  ; [pp+0x38368] TypeArguments: <NExpansionTile>
    //     0xa94300: ldr             x1, [x1, #0x368]
    // 0xa94304: r0 = _ExpansionTileState()
    //     0xa94304: bl              #0xa94424  ; Allocate_ExpansionTileStateStub -> _ExpansionTileState (size=0x48)
    // 0xa94308: mov             x1, x0
    // 0xa9430c: stur            x0, [fp, #-8]
    // 0xa94310: r0 = _ExpansionTileState()
    //     0xa94310: bl              #0xa9432c  ; [package:nuikit/src/widgets/expansion_tile/expansion_tile.dart] _ExpansionTileState::_ExpansionTileState
    // 0xa94314: ldur            x0, [fp, #-8]
    // 0xa94318: LeaveFrame
    //     0xa94318: mov             SP, fp
    //     0xa9431c: ldp             fp, lr, [SP], #0x10
    // 0xa94320: ret
    //     0xa94320: ret             
    // 0xa94324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa94324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa94328: b               #0xa942fc
  }
}
