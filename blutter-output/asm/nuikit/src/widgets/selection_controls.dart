// lib: , url: package:nuikit/src/widgets/selection_controls.dart

// class id: 1049979, size: 0x8
class :: {
}

// class id: 1167, size: 0x10, field offset: 0x8
//   const constructor, 
class _TextSelectionToolbarItemData extends Object {
}

// class id: 3720, size: 0x8, field offset: 0x8
class NTextSelectionControls extends MaterialTextSelectionControls {

  _ buildToolbar(/* No info */) {
    // ** addr: 0xc29af8, size: 0x1b0
    // 0xc29af8: EnterFrame
    //     0xc29af8: stp             fp, lr, [SP, #-0x10]!
    //     0xc29afc: mov             fp, SP
    // 0xc29b00: AllocStack(0x50)
    //     0xc29b00: sub             SP, SP, #0x50
    // 0xc29b04: SetupParameters(NTextSelectionControls this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r2, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */, dynamic _ /* d0 => d0, fp-0x50 */)
    //     0xc29b04: mov             x0, x2
    //     0xc29b08: stur            x2, [fp, #-0x10]
    //     0xc29b0c: mov             x2, x6
    //     0xc29b10: stur            x1, [fp, #-8]
    //     0xc29b14: stur            x3, [fp, #-0x18]
    //     0xc29b18: stur            x5, [fp, #-0x20]
    //     0xc29b1c: stur            x6, [fp, #-0x28]
    //     0xc29b20: stur            x7, [fp, #-0x30]
    //     0xc29b24: stur            d0, [fp, #-0x50]
    // 0xc29b28: CheckStackOverflow
    //     0xc29b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc29b2c: cmp             SP, x16
    //     0xc29b30: b.ls            #0xc29ca0
    // 0xc29b34: r1 = 2
    //     0xc29b34: movz            x1, #0x2
    // 0xc29b38: r0 = AllocateContext()
    //     0xc29b38: bl              #0xec126c  ; AllocateContextStub
    // 0xc29b3c: mov             x3, x0
    // 0xc29b40: ldur            x0, [fp, #-8]
    // 0xc29b44: stur            x3, [fp, #-0x38]
    // 0xc29b48: StoreField: r3->field_f = r0
    //     0xc29b48: stur            w0, [x3, #0xf]
    // 0xc29b4c: ldur            x2, [fp, #-0x28]
    // 0xc29b50: StoreField: r3->field_13 = r2
    //     0xc29b50: stur            w2, [x3, #0x13]
    // 0xc29b54: mov             x1, x0
    // 0xc29b58: r0 = canCut()
    //     0xc29b58: bl              #0xc2a8ec  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::canCut
    // 0xc29b5c: tbnz            w0, #4, #0xc29b78
    // 0xc29b60: ldur            x2, [fp, #-0x38]
    // 0xc29b64: r1 = Function '<anonymous closure>':.
    //     0xc29b64: add             x1, PP, #0x41, lsl #12  ; [pp+0x414f0] AnonymousClosure: (0xc2a06c), in [package:nuikit/src/widgets/selection_controls.dart] NTextSelectionControls::buildToolbar (0xc29af8)
    //     0xc29b68: ldr             x1, [x1, #0x4f0]
    // 0xc29b6c: r0 = AllocateClosure()
    //     0xc29b6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xc29b70: mov             x3, x0
    // 0xc29b74: b               #0xc29b7c
    // 0xc29b78: r3 = Null
    //     0xc29b78: mov             x3, NULL
    // 0xc29b7c: ldur            x0, [fp, #-0x38]
    // 0xc29b80: stur            x3, [fp, #-0x28]
    // 0xc29b84: LoadField: r2 = r0->field_13
    //     0xc29b84: ldur            w2, [x0, #0x13]
    // 0xc29b88: DecompressPointer r2
    //     0xc29b88: add             x2, x2, HEAP, lsl #32
    // 0xc29b8c: ldur            x1, [fp, #-8]
    // 0xc29b90: r0 = canCopy()
    //     0xc29b90: bl              #0xc2a97c  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::canCopy
    // 0xc29b94: tbnz            w0, #4, #0xc29bb0
    // 0xc29b98: ldur            x2, [fp, #-0x38]
    // 0xc29b9c: r1 = Function '<anonymous closure>':.
    //     0xc29b9c: add             x1, PP, #0x41, lsl #12  ; [pp+0x414f8] AnonymousClosure: (0xc29e58), in [package:nuikit/src/widgets/selection_controls.dart] NTextSelectionControls::buildToolbar (0xc29af8)
    //     0xc29ba0: ldr             x1, [x1, #0x4f8]
    // 0xc29ba4: r0 = AllocateClosure()
    //     0xc29ba4: bl              #0xec1630  ; AllocateClosureStub
    // 0xc29ba8: mov             x3, x0
    // 0xc29bac: b               #0xc29bb4
    // 0xc29bb0: r3 = Null
    //     0xc29bb0: mov             x3, NULL
    // 0xc29bb4: ldur            x0, [fp, #-0x38]
    // 0xc29bb8: stur            x3, [fp, #-0x40]
    // 0xc29bbc: LoadField: r2 = r0->field_13
    //     0xc29bbc: ldur            w2, [x0, #0x13]
    // 0xc29bc0: DecompressPointer r2
    //     0xc29bc0: add             x2, x2, HEAP, lsl #32
    // 0xc29bc4: ldur            x1, [fp, #-8]
    // 0xc29bc8: r0 = canPaste()
    //     0xc29bc8: bl              #0xd8391c  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::canPaste
    // 0xc29bcc: tbnz            w0, #4, #0xc29be8
    // 0xc29bd0: ldur            x2, [fp, #-0x38]
    // 0xc29bd4: r1 = Function '<anonymous closure>':.
    //     0xc29bd4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41500] AnonymousClosure: (0xc29e0c), in [package:nuikit/src/widgets/selection_controls.dart] NTextSelectionControls::buildToolbar (0xc29af8)
    //     0xc29bd8: ldr             x1, [x1, #0x500]
    // 0xc29bdc: r0 = AllocateClosure()
    //     0xc29bdc: bl              #0xec1630  ; AllocateClosureStub
    // 0xc29be0: mov             x3, x0
    // 0xc29be4: b               #0xc29bec
    // 0xc29be8: r3 = Null
    //     0xc29be8: mov             x3, NULL
    // 0xc29bec: ldur            x0, [fp, #-0x38]
    // 0xc29bf0: stur            x3, [fp, #-0x48]
    // 0xc29bf4: LoadField: r2 = r0->field_13
    //     0xc29bf4: ldur            w2, [x0, #0x13]
    // 0xc29bf8: DecompressPointer r2
    //     0xc29bf8: add             x2, x2, HEAP, lsl #32
    // 0xc29bfc: ldur            x1, [fp, #-8]
    // 0xc29c00: r0 = canSelectAll()
    //     0xc29c00: bl              #0xc29cb4  ; [package:flutter/src/material/text_selection.dart] MaterialTextSelectionControls::canSelectAll
    // 0xc29c04: tbnz            w0, #4, #0xc29c20
    // 0xc29c08: ldur            x2, [fp, #-0x38]
    // 0xc29c0c: r1 = Function '<anonymous closure>':.
    //     0xc29c0c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41508] AnonymousClosure: (0xc29d70), in [package:nuikit/src/widgets/selection_controls.dart] NTextSelectionControls::buildToolbar (0xc29af8)
    //     0xc29c10: ldr             x1, [x1, #0x508]
    // 0xc29c14: r0 = AllocateClosure()
    //     0xc29c14: bl              #0xec1630  ; AllocateClosureStub
    // 0xc29c18: mov             x7, x0
    // 0xc29c1c: b               #0xc29c24
    // 0xc29c20: r7 = Null
    //     0xc29c20: mov             x7, NULL
    // 0xc29c24: ldur            x6, [fp, #-0x10]
    // 0xc29c28: ldur            d0, [fp, #-0x50]
    // 0xc29c2c: ldur            x5, [fp, #-0x18]
    // 0xc29c30: ldur            x4, [fp, #-0x20]
    // 0xc29c34: ldur            x3, [fp, #-0x30]
    // 0xc29c38: ldur            x2, [fp, #-0x28]
    // 0xc29c3c: ldur            x1, [fp, #-0x40]
    // 0xc29c40: ldur            x0, [fp, #-0x48]
    // 0xc29c44: stur            x7, [fp, #-8]
    // 0xc29c48: r0 = _TextSelectionControlsToolbar()
    //     0xc29c48: bl              #0xc29ca8  ; Allocate_TextSelectionControlsToolbarStub -> _TextSelectionControlsToolbar (size=0x34)
    // 0xc29c4c: ldur            x1, [fp, #-0x30]
    // 0xc29c50: StoreField: r0->field_b = r1
    //     0xc29c50: stur            w1, [x0, #0xb]
    // 0xc29c54: ldur            x1, [fp, #-0x20]
    // 0xc29c58: StoreField: r0->field_f = r1
    //     0xc29c58: stur            w1, [x0, #0xf]
    // 0xc29c5c: ldur            x1, [fp, #-0x10]
    // 0xc29c60: StoreField: r0->field_13 = r1
    //     0xc29c60: stur            w1, [x0, #0x13]
    // 0xc29c64: ldur            x1, [fp, #-0x28]
    // 0xc29c68: ArrayStore: r0[0] = r1  ; List_4
    //     0xc29c68: stur            w1, [x0, #0x17]
    // 0xc29c6c: ldur            x1, [fp, #-0x40]
    // 0xc29c70: StoreField: r0->field_1b = r1
    //     0xc29c70: stur            w1, [x0, #0x1b]
    // 0xc29c74: ldur            x1, [fp, #-0x48]
    // 0xc29c78: StoreField: r0->field_1f = r1
    //     0xc29c78: stur            w1, [x0, #0x1f]
    // 0xc29c7c: ldur            x1, [fp, #-8]
    // 0xc29c80: StoreField: r0->field_23 = r1
    //     0xc29c80: stur            w1, [x0, #0x23]
    // 0xc29c84: ldur            x1, [fp, #-0x18]
    // 0xc29c88: StoreField: r0->field_27 = r1
    //     0xc29c88: stur            w1, [x0, #0x27]
    // 0xc29c8c: ldur            d0, [fp, #-0x50]
    // 0xc29c90: StoreField: r0->field_2b = d0
    //     0xc29c90: stur            d0, [x0, #0x2b]
    // 0xc29c94: LeaveFrame
    //     0xc29c94: mov             SP, fp
    //     0xc29c98: ldp             fp, lr, [SP], #0x10
    // 0xc29c9c: ret
    //     0xc29c9c: ret             
    // 0xc29ca0: r0 = StackOverflowSharedWithFPURegs()
    //     0xc29ca0: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc29ca4: b               #0xc29b34
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc29d70, size: 0x50
    // 0xc29d70: EnterFrame
    //     0xc29d70: stp             fp, lr, [SP, #-0x10]!
    //     0xc29d74: mov             fp, SP
    // 0xc29d78: ldr             x0, [fp, #0x10]
    // 0xc29d7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc29d7c: ldur            w1, [x0, #0x17]
    // 0xc29d80: DecompressPointer r1
    //     0xc29d80: add             x1, x1, HEAP, lsl #32
    // 0xc29d84: CheckStackOverflow
    //     0xc29d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc29d88: cmp             SP, x16
    //     0xc29d8c: b.ls            #0xc29db8
    // 0xc29d90: LoadField: r0 = r1->field_f
    //     0xc29d90: ldur            w0, [x1, #0xf]
    // 0xc29d94: DecompressPointer r0
    //     0xc29d94: add             x0, x0, HEAP, lsl #32
    // 0xc29d98: LoadField: r2 = r1->field_13
    //     0xc29d98: ldur            w2, [x1, #0x13]
    // 0xc29d9c: DecompressPointer r2
    //     0xc29d9c: add             x2, x2, HEAP, lsl #32
    // 0xc29da0: mov             x1, x0
    // 0xc29da4: r0 = handleSelectAll()
    //     0xc29da4: bl              #0xc29dc0  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::handleSelectAll
    // 0xc29da8: r0 = Null
    //     0xc29da8: mov             x0, NULL
    // 0xc29dac: LeaveFrame
    //     0xc29dac: mov             SP, fp
    //     0xc29db0: ldp             fp, lr, [SP], #0x10
    // 0xc29db4: ret
    //     0xc29db4: ret             
    // 0xc29db8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc29db8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc29dbc: b               #0xc29d90
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc29e0c, size: 0x4c
    // 0xc29e0c: EnterFrame
    //     0xc29e0c: stp             fp, lr, [SP, #-0x10]!
    //     0xc29e10: mov             fp, SP
    // 0xc29e14: ldr             x0, [fp, #0x10]
    // 0xc29e18: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc29e18: ldur            w1, [x0, #0x17]
    // 0xc29e1c: DecompressPointer r1
    //     0xc29e1c: add             x1, x1, HEAP, lsl #32
    // 0xc29e20: CheckStackOverflow
    //     0xc29e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc29e24: cmp             SP, x16
    //     0xc29e28: b.ls            #0xc29e50
    // 0xc29e2c: LoadField: r0 = r1->field_f
    //     0xc29e2c: ldur            w0, [x1, #0xf]
    // 0xc29e30: DecompressPointer r0
    //     0xc29e30: add             x0, x0, HEAP, lsl #32
    // 0xc29e34: LoadField: r2 = r1->field_13
    //     0xc29e34: ldur            w2, [x1, #0x13]
    // 0xc29e38: DecompressPointer r2
    //     0xc29e38: add             x2, x2, HEAP, lsl #32
    // 0xc29e3c: mov             x1, x0
    // 0xc29e40: r0 = handlePaste()
    //     0xc29e40: bl              #0xccef7c  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::handlePaste
    // 0xc29e44: LeaveFrame
    //     0xc29e44: mov             SP, fp
    //     0xc29e48: ldp             fp, lr, [SP], #0x10
    // 0xc29e4c: ret
    //     0xc29e4c: ret             
    // 0xc29e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc29e50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc29e54: b               #0xc29e2c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xc29e58, size: 0x134
    // 0xc29e58: EnterFrame
    //     0xc29e58: stp             fp, lr, [SP, #-0x10]!
    //     0xc29e5c: mov             fp, SP
    // 0xc29e60: AllocStack(0x20)
    //     0xc29e60: sub             SP, SP, #0x20
    // 0xc29e64: SetupParameters(NTextSelectionControls this /* r1 */)
    //     0xc29e64: stur            NULL, [fp, #-8]
    //     0xc29e68: movz            x0, #0
    //     0xc29e6c: add             x1, fp, w0, sxtw #2
    //     0xc29e70: ldr             x1, [x1, #0x10]
    //     0xc29e74: ldur            w2, [x1, #0x17]
    //     0xc29e78: add             x2, x2, HEAP, lsl #32
    //     0xc29e7c: stur            x2, [fp, #-0x10]
    // 0xc29e80: CheckStackOverflow
    //     0xc29e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc29e84: cmp             SP, x16
    //     0xc29e88: b.ls            #0xc29f84
    // 0xc29e8c: InitAsync() -> Future<void?>
    //     0xc29e8c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xc29e90: bl              #0x661298  ; InitAsyncStub
    // 0xc29e94: ldur            x0, [fp, #-0x10]
    // 0xc29e98: LoadField: r1 = r0->field_f
    //     0xc29e98: ldur            w1, [x0, #0xf]
    // 0xc29e9c: DecompressPointer r1
    //     0xc29e9c: add             x1, x1, HEAP, lsl #32
    // 0xc29ea0: LoadField: r2 = r0->field_13
    //     0xc29ea0: ldur            w2, [x0, #0x13]
    // 0xc29ea4: DecompressPointer r2
    //     0xc29ea4: add             x2, x2, HEAP, lsl #32
    // 0xc29ea8: r0 = handleCopy()
    //     0xc29ea8: bl              #0xc4337c  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::handleCopy
    // 0xc29eac: r0 = getData()
    //     0xc29eac: bl              #0xc29f8c  ; [package:flutter/src/services/clipboard.dart] Clipboard::getData
    // 0xc29eb0: mov             x1, x0
    // 0xc29eb4: stur            x1, [fp, #-0x18]
    // 0xc29eb8: r0 = Await()
    //     0xc29eb8: bl              #0x661044  ; AwaitStub
    // 0xc29ebc: cmp             w0, NULL
    // 0xc29ec0: b.ne            #0xc29ecc
    // 0xc29ec4: r0 = Null
    //     0xc29ec4: mov             x0, NULL
    // 0xc29ec8: b               #0xc29ed8
    // 0xc29ecc: LoadField: r1 = r0->field_7
    //     0xc29ecc: ldur            w1, [x0, #7]
    // 0xc29ed0: DecompressPointer r1
    //     0xc29ed0: add             x1, x1, HEAP, lsl #32
    // 0xc29ed4: mov             x0, x1
    // 0xc29ed8: cmp             w0, NULL
    // 0xc29edc: b.ne            #0xc29ee8
    // 0xc29ee0: r3 = ""
    //     0xc29ee0: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc29ee4: b               #0xc29eec
    // 0xc29ee8: mov             x3, x0
    // 0xc29eec: r0 = 10
    //     0xc29eec: movz            x0, #0xa
    // 0xc29ef0: mov             x2, x0
    // 0xc29ef4: stur            x3, [fp, #-0x10]
    // 0xc29ef8: r1 = Null
    //     0xc29ef8: mov             x1, NULL
    // 0xc29efc: r0 = AllocateArray()
    //     0xc29efc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc29f00: mov             x2, x0
    // 0xc29f04: ldur            x0, [fp, #-0x10]
    // 0xc29f08: stur            x2, [fp, #-0x18]
    // 0xc29f0c: StoreField: r2->field_f = r0
    //     0xc29f0c: stur            w0, [x2, #0xf]
    // 0xc29f10: r16 = ""
    //     0xc29f10: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc29f14: StoreField: r2->field_13 = r16
    //     0xc29f14: stur            w16, [x2, #0x13]
    // 0xc29f18: r16 = ""
    //     0xc29f18: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc29f1c: ArrayStore: r2[0] = r16  ; List_4
    //     0xc29f1c: stur            w16, [x2, #0x17]
    // 0xc29f20: r16 = "Download NU Online Super App, aplikasi keislaman terlengkap!"
    //     0xc29f20: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d3c0] "Download NU Online Super App, aplikasi keislaman terlengkap!"
    //     0xc29f24: ldr             x16, [x16, #0x3c0]
    // 0xc29f28: StoreField: r2->field_1b = r16
    //     0xc29f28: stur            w16, [x2, #0x1b]
    // 0xc29f2c: r16 = "https://nu.or.id/superapp (Android & iOS)"
    //     0xc29f2c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d3c8] "https://nu.or.id/superapp (Android & iOS)"
    //     0xc29f30: ldr             x16, [x16, #0x3c8]
    // 0xc29f34: StoreField: r2->field_1f = r16
    //     0xc29f34: stur            w16, [x2, #0x1f]
    // 0xc29f38: r1 = <String>
    //     0xc29f38: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc29f3c: r0 = AllocateGrowableArray()
    //     0xc29f3c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc29f40: mov             x1, x0
    // 0xc29f44: ldur            x0, [fp, #-0x18]
    // 0xc29f48: StoreField: r1->field_f = r0
    //     0xc29f48: stur            w0, [x1, #0xf]
    // 0xc29f4c: r0 = 10
    //     0xc29f4c: movz            x0, #0xa
    // 0xc29f50: StoreField: r1->field_b = r0
    //     0xc29f50: stur            w0, [x1, #0xb]
    // 0xc29f54: r16 = "\n"
    //     0xc29f54: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc29f58: str             x16, [SP]
    // 0xc29f5c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc29f5c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc29f60: r0 = join()
    //     0xc29f60: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xc29f64: stur            x0, [fp, #-0x10]
    // 0xc29f68: r0 = ClipboardData()
    //     0xc29f68: bl              #0xa053d8  ; AllocateClipboardDataStub -> ClipboardData (size=0xc)
    // 0xc29f6c: mov             x1, x0
    // 0xc29f70: ldur            x0, [fp, #-0x10]
    // 0xc29f74: StoreField: r1->field_7 = r0
    //     0xc29f74: stur            w0, [x1, #7]
    // 0xc29f78: r0 = setData()
    //     0xc29f78: bl              #0xa05344  ; [package:flutter/src/services/clipboard.dart] Clipboard::setData
    // 0xc29f7c: r0 = Null
    //     0xc29f7c: mov             x0, NULL
    // 0xc29f80: r0 = ReturnAsyncNotFuture()
    //     0xc29f80: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xc29f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc29f84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc29f88: b               #0xc29e8c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc2a06c, size: 0x50
    // 0xc2a06c: EnterFrame
    //     0xc2a06c: stp             fp, lr, [SP, #-0x10]!
    //     0xc2a070: mov             fp, SP
    // 0xc2a074: ldr             x0, [fp, #0x10]
    // 0xc2a078: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc2a078: ldur            w1, [x0, #0x17]
    // 0xc2a07c: DecompressPointer r1
    //     0xc2a07c: add             x1, x1, HEAP, lsl #32
    // 0xc2a080: CheckStackOverflow
    //     0xc2a080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2a084: cmp             SP, x16
    //     0xc2a088: b.ls            #0xc2a0b4
    // 0xc2a08c: LoadField: r0 = r1->field_f
    //     0xc2a08c: ldur            w0, [x1, #0xf]
    // 0xc2a090: DecompressPointer r0
    //     0xc2a090: add             x0, x0, HEAP, lsl #32
    // 0xc2a094: LoadField: r2 = r1->field_13
    //     0xc2a094: ldur            w2, [x1, #0x13]
    // 0xc2a098: DecompressPointer r2
    //     0xc2a098: add             x2, x2, HEAP, lsl #32
    // 0xc2a09c: mov             x1, x0
    // 0xc2a0a0: r0 = handleCut()
    //     0xc2a0a0: bl              #0xc32de0  ; [package:flutter/src/widgets/text_selection.dart] TextSelectionControls::handleCut
    // 0xc2a0a4: r0 = Null
    //     0xc2a0a4: mov             x0, NULL
    // 0xc2a0a8: LeaveFrame
    //     0xc2a0a8: mov             SP, fp
    //     0xc2a0ac: ldp             fp, lr, [SP], #0x10
    // 0xc2a0b0: ret
    //     0xc2a0b0: ret             
    // 0xc2a0b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2a0b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2a0b8: b               #0xc2a08c
  }
}

// class id: 4122, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __TextSelectionControlsToolbarState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ dispose(/* No info */) {
    // ** addr: 0xa8356c, size: 0x94
    // 0xa8356c: EnterFrame
    //     0xa8356c: stp             fp, lr, [SP, #-0x10]!
    //     0xa83570: mov             fp, SP
    // 0xa83574: AllocStack(0x10)
    //     0xa83574: sub             SP, SP, #0x10
    // 0xa83578: SetupParameters(__TextSelectionControlsToolbarState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xa83578: mov             x0, x1
    //     0xa8357c: stur            x1, [fp, #-0x10]
    // 0xa83580: CheckStackOverflow
    //     0xa83580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83584: cmp             SP, x16
    //     0xa83588: b.ls            #0xa835f8
    // 0xa8358c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa8358c: ldur            w3, [x0, #0x17]
    // 0xa83590: DecompressPointer r3
    //     0xa83590: add             x3, x3, HEAP, lsl #32
    // 0xa83594: stur            x3, [fp, #-8]
    // 0xa83598: cmp             w3, NULL
    // 0xa8359c: b.ne            #0xa835a8
    // 0xa835a0: mov             x1, x0
    // 0xa835a4: b               #0xa835e4
    // 0xa835a8: mov             x2, x0
    // 0xa835ac: r1 = Function '_updateTickers@364311458':.
    //     0xa835ac: add             x1, PP, #0x51, lsl #12  ; [pp+0x51770] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa835b0: ldr             x1, [x1, #0x770]
    // 0xa835b4: r0 = AllocateClosure()
    //     0xa835b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa835b8: ldur            x1, [fp, #-8]
    // 0xa835bc: r2 = LoadClassIdInstr(r1)
    //     0xa835bc: ldur            x2, [x1, #-1]
    //     0xa835c0: ubfx            x2, x2, #0xc, #0x14
    // 0xa835c4: mov             x16, x0
    // 0xa835c8: mov             x0, x2
    // 0xa835cc: mov             x2, x16
    // 0xa835d0: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa835d0: movz            x17, #0xbf5c
    //     0xa835d4: add             lr, x0, x17
    //     0xa835d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa835dc: blr             lr
    // 0xa835e0: ldur            x1, [fp, #-0x10]
    // 0xa835e4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xa835e4: stur            NULL, [x1, #0x17]
    // 0xa835e8: r0 = Null
    //     0xa835e8: mov             x0, NULL
    // 0xa835ec: LeaveFrame
    //     0xa835ec: mov             SP, fp
    //     0xa835f0: ldp             fp, lr, [SP], #0x10
    // 0xa835f4: ret
    //     0xa835f4: ret             
    // 0xa835f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa835f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa835fc: b               #0xa8358c
  }
  _ activate(/* No info */) {
    // ** addr: 0xa85a38, size: 0x30
    // 0xa85a38: EnterFrame
    //     0xa85a38: stp             fp, lr, [SP, #-0x10]!
    //     0xa85a3c: mov             fp, SP
    // 0xa85a40: CheckStackOverflow
    //     0xa85a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85a44: cmp             SP, x16
    //     0xa85a48: b.ls            #0xa85a60
    // 0xa85a4c: r0 = _updateTickerModeNotifier()
    //     0xa85a4c: bl              #0xa85a68  ; [package:nuikit/src/widgets/selection_controls.dart] __TextSelectionControlsToolbarState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0xa85a50: r0 = Null
    //     0xa85a50: mov             x0, NULL
    // 0xa85a54: LeaveFrame
    //     0xa85a54: mov             SP, fp
    //     0xa85a58: ldp             fp, lr, [SP], #0x10
    // 0xa85a5c: ret
    //     0xa85a5c: ret             
    // 0xa85a60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85a60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85a64: b               #0xa85a4c
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0xa85a68, size: 0x124
    // 0xa85a68: EnterFrame
    //     0xa85a68: stp             fp, lr, [SP, #-0x10]!
    //     0xa85a6c: mov             fp, SP
    // 0xa85a70: AllocStack(0x18)
    //     0xa85a70: sub             SP, SP, #0x18
    // 0xa85a74: SetupParameters(__TextSelectionControlsToolbarState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0xa85a74: mov             x2, x1
    //     0xa85a78: stur            x1, [fp, #-8]
    // 0xa85a7c: CheckStackOverflow
    //     0xa85a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85a80: cmp             SP, x16
    //     0xa85a84: b.ls            #0xa85b80
    // 0xa85a88: LoadField: r1 = r2->field_f
    //     0xa85a88: ldur            w1, [x2, #0xf]
    // 0xa85a8c: DecompressPointer r1
    //     0xa85a8c: add             x1, x1, HEAP, lsl #32
    // 0xa85a90: cmp             w1, NULL
    // 0xa85a94: b.eq            #0xa85b88
    // 0xa85a98: r0 = getNotifier()
    //     0xa85a98: bl              #0x6efd98  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0xa85a9c: mov             x3, x0
    // 0xa85aa0: ldur            x0, [fp, #-8]
    // 0xa85aa4: stur            x3, [fp, #-0x18]
    // 0xa85aa8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa85aa8: ldur            w4, [x0, #0x17]
    // 0xa85aac: DecompressPointer r4
    //     0xa85aac: add             x4, x4, HEAP, lsl #32
    // 0xa85ab0: stur            x4, [fp, #-0x10]
    // 0xa85ab4: cmp             w3, w4
    // 0xa85ab8: b.ne            #0xa85acc
    // 0xa85abc: r0 = Null
    //     0xa85abc: mov             x0, NULL
    // 0xa85ac0: LeaveFrame
    //     0xa85ac0: mov             SP, fp
    //     0xa85ac4: ldp             fp, lr, [SP], #0x10
    // 0xa85ac8: ret
    //     0xa85ac8: ret             
    // 0xa85acc: cmp             w4, NULL
    // 0xa85ad0: b.eq            #0xa85b14
    // 0xa85ad4: mov             x2, x0
    // 0xa85ad8: r1 = Function '_updateTickers@364311458':.
    //     0xa85ad8: add             x1, PP, #0x51, lsl #12  ; [pp+0x51770] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85adc: ldr             x1, [x1, #0x770]
    // 0xa85ae0: r0 = AllocateClosure()
    //     0xa85ae0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85ae4: ldur            x1, [fp, #-0x10]
    // 0xa85ae8: r2 = LoadClassIdInstr(r1)
    //     0xa85ae8: ldur            x2, [x1, #-1]
    //     0xa85aec: ubfx            x2, x2, #0xc, #0x14
    // 0xa85af0: mov             x16, x0
    // 0xa85af4: mov             x0, x2
    // 0xa85af8: mov             x2, x16
    // 0xa85afc: r0 = GDT[cid_x0 + 0xbf5c]()
    //     0xa85afc: movz            x17, #0xbf5c
    //     0xa85b00: add             lr, x0, x17
    //     0xa85b04: ldr             lr, [x21, lr, lsl #3]
    //     0xa85b08: blr             lr
    // 0xa85b0c: ldur            x0, [fp, #-8]
    // 0xa85b10: ldur            x3, [fp, #-0x18]
    // 0xa85b14: mov             x2, x0
    // 0xa85b18: r1 = Function '_updateTickers@364311458':.
    //     0xa85b18: add             x1, PP, #0x51, lsl #12  ; [pp+0x51770] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xa85b1c: ldr             x1, [x1, #0x770]
    // 0xa85b20: r0 = AllocateClosure()
    //     0xa85b20: bl              #0xec1630  ; AllocateClosureStub
    // 0xa85b24: ldur            x3, [fp, #-0x18]
    // 0xa85b28: r1 = LoadClassIdInstr(r3)
    //     0xa85b28: ldur            x1, [x3, #-1]
    //     0xa85b2c: ubfx            x1, x1, #0xc, #0x14
    // 0xa85b30: mov             x2, x0
    // 0xa85b34: mov             x0, x1
    // 0xa85b38: mov             x1, x3
    // 0xa85b3c: r0 = GDT[cid_x0 + 0xc407]()
    //     0xa85b3c: movz            x17, #0xc407
    //     0xa85b40: add             lr, x0, x17
    //     0xa85b44: ldr             lr, [x21, lr, lsl #3]
    //     0xa85b48: blr             lr
    // 0xa85b4c: ldur            x0, [fp, #-0x18]
    // 0xa85b50: ldur            x1, [fp, #-8]
    // 0xa85b54: ArrayStore: r1[0] = r0  ; List_4
    //     0xa85b54: stur            w0, [x1, #0x17]
    //     0xa85b58: ldurb           w16, [x1, #-1]
    //     0xa85b5c: ldurb           w17, [x0, #-1]
    //     0xa85b60: and             x16, x17, x16, lsr #2
    //     0xa85b64: tst             x16, HEAP, lsr #32
    //     0xa85b68: b.eq            #0xa85b70
    //     0xa85b6c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa85b70: r0 = Null
    //     0xa85b70: mov             x0, NULL
    // 0xa85b74: LeaveFrame
    //     0xa85b74: mov             SP, fp
    //     0xa85b78: ldp             fp, lr, [SP], #0x10
    // 0xa85b7c: ret
    //     0xa85b7c: ret             
    // 0xa85b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85b84: b               #0xa85a88
    // 0xa85b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85b88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4123, size: 0x1c, field offset: 0x1c
class _TextSelectionControlsToolbarState extends __TextSelectionControlsToolbarState&State&TickerProviderStateMixin {

  _ initState(/* No info */) {
    // ** addr: 0x96527c, size: 0x74
    // 0x96527c: EnterFrame
    //     0x96527c: stp             fp, lr, [SP, #-0x10]!
    //     0x965280: mov             fp, SP
    // 0x965284: AllocStack(0x8)
    //     0x965284: sub             SP, SP, #8
    // 0x965288: SetupParameters(_TextSelectionControlsToolbarState this /* r1 => r2 */)
    //     0x965288: mov             x2, x1
    // 0x96528c: CheckStackOverflow
    //     0x96528c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965290: cmp             SP, x16
    //     0x965294: b.ls            #0x9652e4
    // 0x965298: LoadField: r0 = r2->field_b
    //     0x965298: ldur            w0, [x2, #0xb]
    // 0x96529c: DecompressPointer r0
    //     0x96529c: add             x0, x0, HEAP, lsl #32
    // 0x9652a0: cmp             w0, NULL
    // 0x9652a4: b.eq            #0x9652ec
    // 0x9652a8: LoadField: r3 = r0->field_b
    //     0x9652a8: ldur            w3, [x0, #0xb]
    // 0x9652ac: DecompressPointer r3
    //     0x9652ac: add             x3, x3, HEAP, lsl #32
    // 0x9652b0: stur            x3, [fp, #-8]
    // 0x9652b4: cmp             w3, NULL
    // 0x9652b8: b.eq            #0x9652d4
    // 0x9652bc: r1 = Function '_onChangedClipboardStatus@**********':.
    //     0x9652bc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51738] AnonymousClosure: (0x965314), in [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::_onChangedClipboardStatus (0x96534c)
    //     0x9652c0: ldr             x1, [x1, #0x738]
    // 0x9652c4: r0 = AllocateClosure()
    //     0x9652c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9652c8: ldur            x1, [fp, #-8]
    // 0x9652cc: mov             x2, x0
    // 0x9652d0: r0 = addListener()
    //     0x9652d0: bl              #0xa7a494  ; [package:flutter/src/widgets/text_selection.dart] ClipboardStatusNotifier::addListener
    // 0x9652d4: r0 = Null
    //     0x9652d4: mov             x0, NULL
    // 0x9652d8: LeaveFrame
    //     0x9652d8: mov             SP, fp
    //     0x9652dc: ldp             fp, lr, [SP], #0x10
    // 0x9652e0: ret
    //     0x9652e0: ret             
    // 0x9652e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9652e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9652e8: b               #0x965298
    // 0x9652ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9652ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onChangedClipboardStatus(dynamic) {
    // ** addr: 0x965314, size: 0x38
    // 0x965314: EnterFrame
    //     0x965314: stp             fp, lr, [SP, #-0x10]!
    //     0x965318: mov             fp, SP
    // 0x96531c: ldr             x0, [fp, #0x10]
    // 0x965320: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x965320: ldur            w1, [x0, #0x17]
    // 0x965324: DecompressPointer r1
    //     0x965324: add             x1, x1, HEAP, lsl #32
    // 0x965328: CheckStackOverflow
    //     0x965328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96532c: cmp             SP, x16
    //     0x965330: b.ls            #0x965344
    // 0x965334: r0 = _onChangedClipboardStatus()
    //     0x965334: bl              #0x96534c  ; [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::_onChangedClipboardStatus
    // 0x965338: LeaveFrame
    //     0x965338: mov             SP, fp
    //     0x96533c: ldp             fp, lr, [SP], #0x10
    // 0x965340: ret
    //     0x965340: ret             
    // 0x965344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965348: b               #0x965334
  }
  _ _onChangedClipboardStatus(/* No info */) {
    // ** addr: 0x96534c, size: 0x54
    // 0x96534c: EnterFrame
    //     0x96534c: stp             fp, lr, [SP, #-0x10]!
    //     0x965350: mov             fp, SP
    // 0x965354: AllocStack(0x8)
    //     0x965354: sub             SP, SP, #8
    // 0x965358: SetupParameters(_TextSelectionControlsToolbarState this /* r1 => r0, fp-0x8 */)
    //     0x965358: mov             x0, x1
    //     0x96535c: stur            x1, [fp, #-8]
    // 0x965360: CheckStackOverflow
    //     0x965360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965364: cmp             SP, x16
    //     0x965368: b.ls            #0x965398
    // 0x96536c: r1 = Function '<anonymous closure>':.
    //     0x96536c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51740] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x965370: ldr             x1, [x1, #0x740]
    // 0x965374: r2 = Null
    //     0x965374: mov             x2, NULL
    // 0x965378: r0 = AllocateClosure()
    //     0x965378: bl              #0xec1630  ; AllocateClosureStub
    // 0x96537c: ldur            x1, [fp, #-8]
    // 0x965380: mov             x2, x0
    // 0x965384: r0 = setState()
    //     0x965384: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x965388: r0 = Null
    //     0x965388: mov             x0, NULL
    // 0x96538c: LeaveFrame
    //     0x96538c: mov             SP, fp
    //     0x965390: ldp             fp, lr, [SP], #0x10
    // 0x965394: ret
    //     0x965394: ret             
    // 0x965398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96539c: b               #0x96536c
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a0a68, size: 0x148
    // 0x9a0a68: EnterFrame
    //     0x9a0a68: stp             fp, lr, [SP, #-0x10]!
    //     0x9a0a6c: mov             fp, SP
    // 0x9a0a70: AllocStack(0x20)
    //     0x9a0a70: sub             SP, SP, #0x20
    // 0x9a0a74: SetupParameters(_TextSelectionControlsToolbarState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a0a74: mov             x4, x1
    //     0x9a0a78: mov             x3, x2
    //     0x9a0a7c: stur            x1, [fp, #-8]
    //     0x9a0a80: stur            x2, [fp, #-0x10]
    // 0x9a0a84: CheckStackOverflow
    //     0x9a0a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a0a88: cmp             SP, x16
    //     0x9a0a8c: b.ls            #0x9a0ba4
    // 0x9a0a90: mov             x0, x3
    // 0x9a0a94: r2 = Null
    //     0x9a0a94: mov             x2, NULL
    // 0x9a0a98: r1 = Null
    //     0x9a0a98: mov             x1, NULL
    // 0x9a0a9c: r4 = 60
    //     0x9a0a9c: movz            x4, #0x3c
    // 0x9a0aa0: branchIfSmi(r0, 0x9a0aac)
    //     0x9a0aa0: tbz             w0, #0, #0x9a0aac
    // 0x9a0aa4: r4 = LoadClassIdInstr(r0)
    //     0x9a0aa4: ldur            x4, [x0, #-1]
    //     0x9a0aa8: ubfx            x4, x4, #0xc, #0x14
    // 0x9a0aac: r17 = 4718
    //     0x9a0aac: movz            x17, #0x126e
    // 0x9a0ab0: cmp             x4, x17
    // 0x9a0ab4: b.eq            #0x9a0acc
    // 0x9a0ab8: r8 = _TextSelectionControlsToolbar
    //     0x9a0ab8: add             x8, PP, #0x51, lsl #12  ; [pp+0x51748] Type: _TextSelectionControlsToolbar
    //     0x9a0abc: ldr             x8, [x8, #0x748]
    // 0x9a0ac0: r3 = Null
    //     0x9a0ac0: add             x3, PP, #0x51, lsl #12  ; [pp+0x51750] Null
    //     0x9a0ac4: ldr             x3, [x3, #0x750]
    // 0x9a0ac8: r0 = _TextSelectionControlsToolbar()
    //     0x9a0ac8: bl              #0x9652f0  ; IsType__TextSelectionControlsToolbar_Stub
    // 0x9a0acc: ldur            x3, [fp, #-8]
    // 0x9a0ad0: LoadField: r2 = r3->field_7
    //     0x9a0ad0: ldur            w2, [x3, #7]
    // 0x9a0ad4: DecompressPointer r2
    //     0x9a0ad4: add             x2, x2, HEAP, lsl #32
    // 0x9a0ad8: ldur            x0, [fp, #-0x10]
    // 0x9a0adc: r1 = Null
    //     0x9a0adc: mov             x1, NULL
    // 0x9a0ae0: cmp             w2, NULL
    // 0x9a0ae4: b.eq            #0x9a0b08
    // 0x9a0ae8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a0ae8: ldur            w4, [x2, #0x17]
    // 0x9a0aec: DecompressPointer r4
    //     0x9a0aec: add             x4, x4, HEAP, lsl #32
    // 0x9a0af0: r8 = X0 bound StatefulWidget
    //     0x9a0af0: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a0af4: ldr             x8, [x8, #0x7f8]
    // 0x9a0af8: LoadField: r9 = r4->field_7
    //     0x9a0af8: ldur            x9, [x4, #7]
    // 0x9a0afc: r3 = Null
    //     0x9a0afc: add             x3, PP, #0x51, lsl #12  ; [pp+0x51760] Null
    //     0x9a0b00: ldr             x3, [x3, #0x760]
    // 0x9a0b04: blr             x9
    // 0x9a0b08: ldur            x0, [fp, #-8]
    // 0x9a0b0c: LoadField: r1 = r0->field_b
    //     0x9a0b0c: ldur            w1, [x0, #0xb]
    // 0x9a0b10: DecompressPointer r1
    //     0x9a0b10: add             x1, x1, HEAP, lsl #32
    // 0x9a0b14: cmp             w1, NULL
    // 0x9a0b18: b.eq            #0x9a0bac
    // 0x9a0b1c: LoadField: r3 = r1->field_b
    //     0x9a0b1c: ldur            w3, [x1, #0xb]
    // 0x9a0b20: DecompressPointer r3
    //     0x9a0b20: add             x3, x3, HEAP, lsl #32
    // 0x9a0b24: ldur            x1, [fp, #-0x10]
    // 0x9a0b28: stur            x3, [fp, #-0x20]
    // 0x9a0b2c: LoadField: r4 = r1->field_b
    //     0x9a0b2c: ldur            w4, [x1, #0xb]
    // 0x9a0b30: DecompressPointer r4
    //     0x9a0b30: add             x4, x4, HEAP, lsl #32
    // 0x9a0b34: stur            x4, [fp, #-0x18]
    // 0x9a0b38: cmp             w3, w4
    // 0x9a0b3c: b.eq            #0x9a0b94
    // 0x9a0b40: cmp             w3, NULL
    // 0x9a0b44: b.ne            #0x9a0b50
    // 0x9a0b48: mov             x0, x4
    // 0x9a0b4c: b               #0x9a0b70
    // 0x9a0b50: mov             x2, x0
    // 0x9a0b54: r1 = Function '_onChangedClipboardStatus@**********':.
    //     0x9a0b54: add             x1, PP, #0x51, lsl #12  ; [pp+0x51738] AnonymousClosure: (0x965314), in [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::_onChangedClipboardStatus (0x96534c)
    //     0x9a0b58: ldr             x1, [x1, #0x738]
    // 0x9a0b5c: r0 = AllocateClosure()
    //     0x9a0b5c: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a0b60: ldur            x1, [fp, #-0x20]
    // 0x9a0b64: mov             x2, x0
    // 0x9a0b68: r0 = addListener()
    //     0x9a0b68: bl              #0xa7a494  ; [package:flutter/src/widgets/text_selection.dart] ClipboardStatusNotifier::addListener
    // 0x9a0b6c: ldur            x0, [fp, #-0x18]
    // 0x9a0b70: cmp             w0, NULL
    // 0x9a0b74: b.eq            #0x9a0b94
    // 0x9a0b78: ldur            x2, [fp, #-8]
    // 0x9a0b7c: r1 = Function '_onChangedClipboardStatus@**********':.
    //     0x9a0b7c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51738] AnonymousClosure: (0x965314), in [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::_onChangedClipboardStatus (0x96534c)
    //     0x9a0b80: ldr             x1, [x1, #0x738]
    // 0x9a0b84: r0 = AllocateClosure()
    //     0x9a0b84: bl              #0xec1630  ; AllocateClosureStub
    // 0x9a0b88: ldur            x1, [fp, #-0x18]
    // 0x9a0b8c: mov             x2, x0
    // 0x9a0b90: r0 = removeListener()
    //     0x9a0b90: bl              #0xa8a478  ; [package:flutter/src/widgets/text_selection.dart] LiveTextInputStatusNotifier::removeListener
    // 0x9a0b94: r0 = Null
    //     0x9a0b94: mov             x0, NULL
    // 0x9a0b98: LeaveFrame
    //     0x9a0b98: mov             SP, fp
    //     0x9a0b9c: ldp             fp, lr, [SP], #0x10
    // 0x9a0ba0: ret
    //     0x9a0ba0: ret             
    // 0x9a0ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a0ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a0ba8: b               #0x9a0a90
    // 0x9a0bac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a0bac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa32cec, size: 0x820
    // 0xa32cec: EnterFrame
    //     0xa32cec: stp             fp, lr, [SP, #-0x10]!
    //     0xa32cf0: mov             fp, SP
    // 0xa32cf4: AllocStack(0x60)
    //     0xa32cf4: sub             SP, SP, #0x60
    // 0xa32cf8: SetupParameters(_TextSelectionControlsToolbarState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa32cf8: stur            x1, [fp, #-8]
    //     0xa32cfc: mov             x16, x2
    //     0xa32d00: mov             x2, x1
    //     0xa32d04: mov             x1, x16
    //     0xa32d08: stur            x1, [fp, #-0x10]
    // 0xa32d0c: CheckStackOverflow
    //     0xa32d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa32d10: cmp             SP, x16
    //     0xa32d14: b.ls            #0xa33474
    // 0xa32d18: LoadField: r0 = r2->field_b
    //     0xa32d18: ldur            w0, [x2, #0xb]
    // 0xa32d1c: DecompressPointer r0
    //     0xa32d1c: add             x0, x0, HEAP, lsl #32
    // 0xa32d20: cmp             w0, NULL
    // 0xa32d24: b.eq            #0xa3347c
    // 0xa32d28: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa32d28: ldur            w3, [x0, #0x17]
    // 0xa32d2c: DecompressPointer r3
    //     0xa32d2c: add             x3, x3, HEAP, lsl #32
    // 0xa32d30: cmp             w3, NULL
    // 0xa32d34: b.ne            #0xa32d78
    // 0xa32d38: LoadField: r3 = r0->field_1b
    //     0xa32d38: ldur            w3, [x0, #0x1b]
    // 0xa32d3c: DecompressPointer r3
    //     0xa32d3c: add             x3, x3, HEAP, lsl #32
    // 0xa32d40: cmp             w3, NULL
    // 0xa32d44: b.ne            #0xa32d78
    // 0xa32d48: LoadField: r3 = r0->field_1f
    //     0xa32d48: ldur            w3, [x0, #0x1f]
    // 0xa32d4c: DecompressPointer r3
    //     0xa32d4c: add             x3, x3, HEAP, lsl #32
    // 0xa32d50: cmp             w3, NULL
    // 0xa32d54: b.ne            #0xa32d78
    // 0xa32d58: LoadField: r3 = r0->field_23
    //     0xa32d58: ldur            w3, [x0, #0x23]
    // 0xa32d5c: DecompressPointer r3
    //     0xa32d5c: add             x3, x3, HEAP, lsl #32
    // 0xa32d60: cmp             w3, NULL
    // 0xa32d64: b.ne            #0xa32d78
    // 0xa32d68: r0 = Instance_SizedBox
    //     0xa32d68: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa32d6c: LeaveFrame
    //     0xa32d6c: mov             SP, fp
    //     0xa32d70: ldp             fp, lr, [SP], #0x10
    // 0xa32d74: ret
    //     0xa32d74: ret             
    // 0xa32d78: LoadField: r3 = r0->field_1f
    //     0xa32d78: ldur            w3, [x0, #0x1f]
    // 0xa32d7c: DecompressPointer r3
    //     0xa32d7c: add             x3, x3, HEAP, lsl #32
    // 0xa32d80: cmp             w3, NULL
    // 0xa32d84: b.eq            #0xa32dbc
    // 0xa32d88: LoadField: r3 = r0->field_b
    //     0xa32d88: ldur            w3, [x0, #0xb]
    // 0xa32d8c: DecompressPointer r3
    //     0xa32d8c: add             x3, x3, HEAP, lsl #32
    // 0xa32d90: cmp             w3, NULL
    // 0xa32d94: b.eq            #0xa32dbc
    // 0xa32d98: LoadField: r4 = r3->field_27
    //     0xa32d98: ldur            w4, [x3, #0x27]
    // 0xa32d9c: DecompressPointer r4
    //     0xa32d9c: add             x4, x4, HEAP, lsl #32
    // 0xa32da0: r16 = Instance_ClipboardStatus
    //     0xa32da0: ldr             x16, [PP, #0x4da8]  ; [pp+0x4da8] Obj!ClipboardStatus@e339a1
    // 0xa32da4: cmp             w4, w16
    // 0xa32da8: b.ne            #0xa32dbc
    // 0xa32dac: r0 = Instance_SizedBox
    //     0xa32dac: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa32db0: LeaveFrame
    //     0xa32db0: mov             SP, fp
    //     0xa32db4: ldp             fp, lr, [SP], #0x10
    // 0xa32db8: ret
    //     0xa32db8: ret             
    // 0xa32dbc: LoadField: r3 = r0->field_f
    //     0xa32dbc: ldur            w3, [x0, #0xf]
    // 0xa32dc0: DecompressPointer r3
    //     0xa32dc0: add             x3, x3, HEAP, lsl #32
    // 0xa32dc4: r0 = LoadClassIdInstr(r3)
    //     0xa32dc4: ldur            x0, [x3, #-1]
    //     0xa32dc8: ubfx            x0, x0, #0xc, #0x14
    // 0xa32dcc: stp             xzr, x3, [SP]
    // 0xa32dd0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa32dd0: movz            x17, #0x3037
    //     0xa32dd4: movk            x17, #0x1, lsl #16
    //     0xa32dd8: add             lr, x0, x17
    //     0xa32ddc: ldr             lr, [x21, lr, lsl #3]
    //     0xa32de0: blr             lr
    // 0xa32de4: mov             x2, x0
    // 0xa32de8: ldur            x1, [fp, #-8]
    // 0xa32dec: stur            x2, [fp, #-0x18]
    // 0xa32df0: LoadField: r0 = r1->field_b
    //     0xa32df0: ldur            w0, [x1, #0xb]
    // 0xa32df4: DecompressPointer r0
    //     0xa32df4: add             x0, x0, HEAP, lsl #32
    // 0xa32df8: cmp             w0, NULL
    // 0xa32dfc: b.eq            #0xa33480
    // 0xa32e00: LoadField: r3 = r0->field_f
    //     0xa32e00: ldur            w3, [x0, #0xf]
    // 0xa32e04: DecompressPointer r3
    //     0xa32e04: add             x3, x3, HEAP, lsl #32
    // 0xa32e08: r0 = LoadClassIdInstr(r3)
    //     0xa32e08: ldur            x0, [x3, #-1]
    //     0xa32e0c: ubfx            x0, x0, #0xc, #0x14
    // 0xa32e10: str             x3, [SP]
    // 0xa32e14: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa32e14: movz            x17, #0xc834
    //     0xa32e18: add             lr, x0, x17
    //     0xa32e1c: ldr             lr, [x21, lr, lsl #3]
    //     0xa32e20: blr             lr
    // 0xa32e24: r1 = LoadInt32Instr(r0)
    //     0xa32e24: sbfx            x1, x0, #1, #0x1f
    // 0xa32e28: cmp             x1, #1
    // 0xa32e2c: b.le            #0xa32e78
    // 0xa32e30: ldur            x1, [fp, #-8]
    // 0xa32e34: LoadField: r0 = r1->field_b
    //     0xa32e34: ldur            w0, [x1, #0xb]
    // 0xa32e38: DecompressPointer r0
    //     0xa32e38: add             x0, x0, HEAP, lsl #32
    // 0xa32e3c: cmp             w0, NULL
    // 0xa32e40: b.eq            #0xa33484
    // 0xa32e44: LoadField: r2 = r0->field_f
    //     0xa32e44: ldur            w2, [x0, #0xf]
    // 0xa32e48: DecompressPointer r2
    //     0xa32e48: add             x2, x2, HEAP, lsl #32
    // 0xa32e4c: r0 = LoadClassIdInstr(r2)
    //     0xa32e4c: ldur            x0, [x2, #-1]
    //     0xa32e50: ubfx            x0, x0, #0xc, #0x14
    // 0xa32e54: r16 = 2
    //     0xa32e54: movz            x16, #0x2
    // 0xa32e58: stp             x16, x2, [SP]
    // 0xa32e5c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa32e5c: movz            x17, #0x3037
    //     0xa32e60: movk            x17, #0x1, lsl #16
    //     0xa32e64: add             lr, x0, x17
    //     0xa32e68: ldr             lr, [x21, lr, lsl #3]
    //     0xa32e6c: blr             lr
    // 0xa32e70: mov             x2, x0
    // 0xa32e74: b               #0xa32eb8
    // 0xa32e78: ldur            x1, [fp, #-8]
    // 0xa32e7c: LoadField: r0 = r1->field_b
    //     0xa32e7c: ldur            w0, [x1, #0xb]
    // 0xa32e80: DecompressPointer r0
    //     0xa32e80: add             x0, x0, HEAP, lsl #32
    // 0xa32e84: cmp             w0, NULL
    // 0xa32e88: b.eq            #0xa33488
    // 0xa32e8c: LoadField: r2 = r0->field_f
    //     0xa32e8c: ldur            w2, [x0, #0xf]
    // 0xa32e90: DecompressPointer r2
    //     0xa32e90: add             x2, x2, HEAP, lsl #32
    // 0xa32e94: r0 = LoadClassIdInstr(r2)
    //     0xa32e94: ldur            x0, [x2, #-1]
    //     0xa32e98: ubfx            x0, x0, #0xc, #0x14
    // 0xa32e9c: stp             xzr, x2, [SP]
    // 0xa32ea0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa32ea0: movz            x17, #0x3037
    //     0xa32ea4: movk            x17, #0x1, lsl #16
    //     0xa32ea8: add             lr, x0, x17
    //     0xa32eac: ldr             lr, [x21, lr, lsl #3]
    //     0xa32eb0: blr             lr
    // 0xa32eb4: mov             x2, x0
    // 0xa32eb8: ldur            x1, [fp, #-8]
    // 0xa32ebc: ldur            x0, [fp, #-0x18]
    // 0xa32ec0: d0 = 0.000000
    //     0xa32ec0: eor             v0.16b, v0.16b, v0.16b
    // 0xa32ec4: stur            x2, [fp, #-0x20]
    // 0xa32ec8: LoadField: r3 = r0->field_7
    //     0xa32ec8: ldur            w3, [x0, #7]
    // 0xa32ecc: DecompressPointer r3
    //     0xa32ecc: add             x3, x3, HEAP, lsl #32
    // 0xa32ed0: LoadField: d1 = r3->field_f
    //     0xa32ed0: ldur            d1, [x3, #0xf]
    // 0xa32ed4: LoadField: r0 = r1->field_b
    //     0xa32ed4: ldur            w0, [x1, #0xb]
    // 0xa32ed8: DecompressPointer r0
    //     0xa32ed8: add             x0, x0, HEAP, lsl #32
    // 0xa32edc: cmp             w0, NULL
    // 0xa32ee0: b.eq            #0xa3348c
    // 0xa32ee4: LoadField: d2 = r0->field_2b
    //     0xa32ee4: ldur            d2, [x0, #0x2b]
    // 0xa32ee8: fsub            d3, d1, d2
    // 0xa32eec: fcmp            d3, d0
    // 0xa32ef0: b.le            #0xa32f20
    // 0xa32ef4: r3 = inline_Allocate_Double()
    //     0xa32ef4: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xa32ef8: add             x3, x3, #0x10
    //     0xa32efc: cmp             x4, x3
    //     0xa32f00: b.ls            #0xa33490
    //     0xa32f04: str             x3, [THR, #0x50]  ; THR::top
    //     0xa32f08: sub             x3, x3, #0xf
    //     0xa32f0c: movz            x4, #0xe15c
    //     0xa32f10: movk            x4, #0x3, lsl #16
    //     0xa32f14: stur            x4, [x3, #-1]
    // 0xa32f18: StoreField: r3->field_7 = d3
    //     0xa32f18: stur            d3, [x3, #7]
    // 0xa32f1c: b               #0xa32f78
    // 0xa32f20: fcmp            d0, d3
    // 0xa32f24: b.le            #0xa32f30
    // 0xa32f28: r3 = 0
    //     0xa32f28: movz            x3, #0
    // 0xa32f2c: b               #0xa32f78
    // 0xa32f30: fcmp            d3, #0.0
    // 0xa32f34: b.vs            #0xa32f50
    // 0xa32f38: b.ne            #0xa32f44
    // 0xa32f3c: r3 = 0.000000
    //     0xa32f3c: fmov            x3, d3
    // 0xa32f40: cmp             x3, #0
    // 0xa32f44: b.ge            #0xa32f50
    // 0xa32f48: r3 = 0
    //     0xa32f48: movz            x3, #0
    // 0xa32f4c: b               #0xa32f78
    // 0xa32f50: r3 = inline_Allocate_Double()
    //     0xa32f50: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xa32f54: add             x3, x3, #0x10
    //     0xa32f58: cmp             x4, x3
    //     0xa32f5c: b.ls            #0xa334b4
    //     0xa32f60: str             x3, [THR, #0x50]  ; THR::top
    //     0xa32f64: sub             x3, x3, #0xf
    //     0xa32f68: movz            x4, #0xe15c
    //     0xa32f6c: movk            x4, #0x3, lsl #16
    //     0xa32f70: stur            x4, [x3, #-1]
    // 0xa32f74: StoreField: r3->field_7 = d3
    //     0xa32f74: stur            d3, [x3, #7]
    // 0xa32f78: LoadField: r4 = r0->field_13
    //     0xa32f78: ldur            w4, [x0, #0x13]
    // 0xa32f7c: DecompressPointer r4
    //     0xa32f7c: add             x4, x4, HEAP, lsl #32
    // 0xa32f80: LoadField: d0 = r4->field_f
    //     0xa32f80: ldur            d0, [x4, #0xf]
    // 0xa32f84: r0 = inline_Allocate_Double()
    //     0xa32f84: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xa32f88: add             x0, x0, #0x10
    //     0xa32f8c: cmp             x4, x0
    //     0xa32f90: b.ls            #0xa334d8
    //     0xa32f94: str             x0, [THR, #0x50]  ; THR::top
    //     0xa32f98: sub             x0, x0, #0xf
    //     0xa32f9c: movz            x4, #0xe15c
    //     0xa32fa0: movk            x4, #0x3, lsl #16
    //     0xa32fa4: stur            x4, [x0, #-1]
    // 0xa32fa8: StoreField: r0->field_7 = d0
    //     0xa32fa8: stur            d0, [x0, #7]
    // 0xa32fac: r4 = 60
    //     0xa32fac: movz            x4, #0x3c
    // 0xa32fb0: branchIfSmi(r3, 0xa32fbc)
    //     0xa32fb0: tbz             w3, #0, #0xa32fbc
    // 0xa32fb4: r4 = LoadClassIdInstr(r3)
    //     0xa32fb4: ldur            x4, [x3, #-1]
    //     0xa32fb8: ubfx            x4, x4, #0xc, #0x14
    // 0xa32fbc: stp             x0, x3, [SP]
    // 0xa32fc0: mov             x0, x4
    // 0xa32fc4: r0 = GDT[cid_x0 + -0xff2]()
    //     0xa32fc4: sub             lr, x0, #0xff2
    //     0xa32fc8: ldr             lr, [x21, lr, lsl #3]
    //     0xa32fcc: blr             lr
    // 0xa32fd0: LoadField: d0 = r0->field_7
    //     0xa32fd0: ldur            d0, [x0, #7]
    // 0xa32fd4: d1 = 8.000000
    //     0xa32fd4: fmov            d1, #8.00000000
    // 0xa32fd8: fsub            d2, d0, d1
    // 0xa32fdc: ldur            x0, [fp, #-8]
    // 0xa32fe0: stur            d2, [fp, #-0x48]
    // 0xa32fe4: LoadField: r1 = r0->field_b
    //     0xa32fe4: ldur            w1, [x0, #0xb]
    // 0xa32fe8: DecompressPointer r1
    //     0xa32fe8: add             x1, x1, HEAP, lsl #32
    // 0xa32fec: cmp             w1, NULL
    // 0xa32ff0: b.eq            #0xa334f8
    // 0xa32ff4: LoadField: r2 = r1->field_13
    //     0xa32ff4: ldur            w2, [x1, #0x13]
    // 0xa32ff8: DecompressPointer r2
    //     0xa32ff8: add             x2, x2, HEAP, lsl #32
    // 0xa32ffc: stur            x2, [fp, #-0x18]
    // 0xa33000: LoadField: d0 = r2->field_7
    //     0xa33000: ldur            d0, [x2, #7]
    // 0xa33004: LoadField: r3 = r1->field_27
    //     0xa33004: ldur            w3, [x1, #0x27]
    // 0xa33008: DecompressPointer r3
    //     0xa33008: add             x3, x3, HEAP, lsl #32
    // 0xa3300c: LoadField: d1 = r3->field_7
    //     0xa3300c: ldur            d1, [x3, #7]
    // 0xa33010: fadd            d3, d0, d1
    // 0xa33014: stur            d3, [fp, #-0x40]
    // 0xa33018: r0 = Offset()
    //     0xa33018: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa3301c: ldur            d0, [fp, #-0x40]
    // 0xa33020: stur            x0, [fp, #-0x28]
    // 0xa33024: StoreField: r0->field_7 = d0
    //     0xa33024: stur            d0, [x0, #7]
    // 0xa33028: ldur            d1, [fp, #-0x48]
    // 0xa3302c: StoreField: r0->field_f = d1
    //     0xa3302c: stur            d1, [x0, #0xf]
    // 0xa33030: ldur            x1, [fp, #-0x18]
    // 0xa33034: LoadField: d1 = r1->field_f
    //     0xa33034: ldur            d1, [x1, #0xf]
    // 0xa33038: ldur            x1, [fp, #-0x20]
    // 0xa3303c: LoadField: r2 = r1->field_7
    //     0xa3303c: ldur            w2, [x1, #7]
    // 0xa33040: DecompressPointer r2
    //     0xa33040: add             x2, x2, HEAP, lsl #32
    // 0xa33044: LoadField: d2 = r2->field_f
    //     0xa33044: ldur            d2, [x2, #0xf]
    // 0xa33048: fadd            d3, d1, d2
    // 0xa3304c: d1 = 20.000000
    //     0xa3304c: fmov            d1, #20.00000000
    // 0xa33050: fadd            d2, d3, d1
    // 0xa33054: stur            d2, [fp, #-0x48]
    // 0xa33058: r0 = Offset()
    //     0xa33058: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xa3305c: ldur            d0, [fp, #-0x40]
    // 0xa33060: stur            x0, [fp, #-0x18]
    // 0xa33064: StoreField: r0->field_7 = d0
    //     0xa33064: stur            d0, [x0, #7]
    // 0xa33068: ldur            d0, [fp, #-0x48]
    // 0xa3306c: StoreField: r0->field_f = d0
    //     0xa3306c: stur            d0, [x0, #0xf]
    // 0xa33070: ldur            x1, [fp, #-0x10]
    // 0xa33074: r0 = of()
    //     0xa33074: bl              #0x9179e4  ; [package:flutter/src/material/material_localizations.dart] MaterialLocalizations::of
    // 0xa33078: r1 = <_TextSelectionToolbarItemData>
    //     0xa33078: add             x1, PP, #0x51, lsl #12  ; [pp+0x516f8] TypeArguments: <_TextSelectionToolbarItemData>
    //     0xa3307c: ldr             x1, [x1, #0x6f8]
    // 0xa33080: r2 = 0
    //     0xa33080: movz            x2, #0
    // 0xa33084: r0 = _GrowableList()
    //     0xa33084: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa33088: mov             x1, x0
    // 0xa3308c: ldur            x0, [fp, #-8]
    // 0xa33090: stur            x1, [fp, #-0x20]
    // 0xa33094: LoadField: r2 = r0->field_b
    //     0xa33094: ldur            w2, [x0, #0xb]
    // 0xa33098: DecompressPointer r2
    //     0xa33098: add             x2, x2, HEAP, lsl #32
    // 0xa3309c: cmp             w2, NULL
    // 0xa330a0: b.eq            #0xa334fc
    // 0xa330a4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa330a4: ldur            w3, [x2, #0x17]
    // 0xa330a8: DecompressPointer r3
    //     0xa330a8: add             x3, x3, HEAP, lsl #32
    // 0xa330ac: stur            x3, [fp, #-0x10]
    // 0xa330b0: cmp             w3, NULL
    // 0xa330b4: b.eq            #0xa33154
    // 0xa330b8: r0 = _TextSelectionToolbarItemData()
    //     0xa330b8: bl              #0xa33518  ; Allocate_TextSelectionToolbarItemDataStub -> _TextSelectionToolbarItemData (size=0x10)
    // 0xa330bc: mov             x2, x0
    // 0xa330c0: r0 = "Cut"
    //     0xa330c0: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ab0] "Cut"
    //     0xa330c4: ldr             x0, [x0, #0xab0]
    // 0xa330c8: stur            x2, [fp, #-0x38]
    // 0xa330cc: StoreField: r2->field_7 = r0
    //     0xa330cc: stur            w0, [x2, #7]
    // 0xa330d0: ldur            x0, [fp, #-0x10]
    // 0xa330d4: StoreField: r2->field_b = r0
    //     0xa330d4: stur            w0, [x2, #0xb]
    // 0xa330d8: ldur            x0, [fp, #-0x20]
    // 0xa330dc: LoadField: r1 = r0->field_b
    //     0xa330dc: ldur            w1, [x0, #0xb]
    // 0xa330e0: LoadField: r3 = r0->field_f
    //     0xa330e0: ldur            w3, [x0, #0xf]
    // 0xa330e4: DecompressPointer r3
    //     0xa330e4: add             x3, x3, HEAP, lsl #32
    // 0xa330e8: LoadField: r4 = r3->field_b
    //     0xa330e8: ldur            w4, [x3, #0xb]
    // 0xa330ec: r3 = LoadInt32Instr(r1)
    //     0xa330ec: sbfx            x3, x1, #1, #0x1f
    // 0xa330f0: stur            x3, [fp, #-0x30]
    // 0xa330f4: r1 = LoadInt32Instr(r4)
    //     0xa330f4: sbfx            x1, x4, #1, #0x1f
    // 0xa330f8: cmp             x3, x1
    // 0xa330fc: b.ne            #0xa33108
    // 0xa33100: mov             x1, x0
    // 0xa33104: r0 = _growToNextCapacity()
    //     0xa33104: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa33108: ldur            x2, [fp, #-0x20]
    // 0xa3310c: ldur            x3, [fp, #-0x30]
    // 0xa33110: add             x0, x3, #1
    // 0xa33114: lsl             x1, x0, #1
    // 0xa33118: StoreField: r2->field_b = r1
    //     0xa33118: stur            w1, [x2, #0xb]
    // 0xa3311c: LoadField: r1 = r2->field_f
    //     0xa3311c: ldur            w1, [x2, #0xf]
    // 0xa33120: DecompressPointer r1
    //     0xa33120: add             x1, x1, HEAP, lsl #32
    // 0xa33124: ldur            x0, [fp, #-0x38]
    // 0xa33128: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa33128: add             x25, x1, x3, lsl #2
    //     0xa3312c: add             x25, x25, #0xf
    //     0xa33130: str             w0, [x25]
    //     0xa33134: tbz             w0, #0, #0xa33150
    //     0xa33138: ldurb           w16, [x1, #-1]
    //     0xa3313c: ldurb           w17, [x0, #-1]
    //     0xa33140: and             x16, x17, x16, lsr #2
    //     0xa33144: tst             x16, HEAP, lsr #32
    //     0xa33148: b.eq            #0xa33150
    //     0xa3314c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa33150: b               #0xa33158
    // 0xa33154: mov             x2, x1
    // 0xa33158: ldur            x0, [fp, #-8]
    // 0xa3315c: LoadField: r1 = r0->field_b
    //     0xa3315c: ldur            w1, [x0, #0xb]
    // 0xa33160: DecompressPointer r1
    //     0xa33160: add             x1, x1, HEAP, lsl #32
    // 0xa33164: cmp             w1, NULL
    // 0xa33168: b.eq            #0xa33500
    // 0xa3316c: LoadField: r3 = r1->field_1b
    //     0xa3316c: ldur            w3, [x1, #0x1b]
    // 0xa33170: DecompressPointer r3
    //     0xa33170: add             x3, x3, HEAP, lsl #32
    // 0xa33174: stur            x3, [fp, #-0x10]
    // 0xa33178: cmp             w3, NULL
    // 0xa3317c: b.eq            #0xa33218
    // 0xa33180: r0 = _TextSelectionToolbarItemData()
    //     0xa33180: bl              #0xa33518  ; Allocate_TextSelectionToolbarItemDataStub -> _TextSelectionToolbarItemData (size=0x10)
    // 0xa33184: mov             x2, x0
    // 0xa33188: r0 = "Copy"
    //     0xa33188: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ab8] "Copy"
    //     0xa3318c: ldr             x0, [x0, #0xab8]
    // 0xa33190: stur            x2, [fp, #-0x38]
    // 0xa33194: StoreField: r2->field_7 = r0
    //     0xa33194: stur            w0, [x2, #7]
    // 0xa33198: ldur            x0, [fp, #-0x10]
    // 0xa3319c: StoreField: r2->field_b = r0
    //     0xa3319c: stur            w0, [x2, #0xb]
    // 0xa331a0: ldur            x0, [fp, #-0x20]
    // 0xa331a4: LoadField: r1 = r0->field_b
    //     0xa331a4: ldur            w1, [x0, #0xb]
    // 0xa331a8: LoadField: r3 = r0->field_f
    //     0xa331a8: ldur            w3, [x0, #0xf]
    // 0xa331ac: DecompressPointer r3
    //     0xa331ac: add             x3, x3, HEAP, lsl #32
    // 0xa331b0: LoadField: r4 = r3->field_b
    //     0xa331b0: ldur            w4, [x3, #0xb]
    // 0xa331b4: r3 = LoadInt32Instr(r1)
    //     0xa331b4: sbfx            x3, x1, #1, #0x1f
    // 0xa331b8: stur            x3, [fp, #-0x30]
    // 0xa331bc: r1 = LoadInt32Instr(r4)
    //     0xa331bc: sbfx            x1, x4, #1, #0x1f
    // 0xa331c0: cmp             x3, x1
    // 0xa331c4: b.ne            #0xa331d0
    // 0xa331c8: mov             x1, x0
    // 0xa331cc: r0 = _growToNextCapacity()
    //     0xa331cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa331d0: ldur            x2, [fp, #-0x20]
    // 0xa331d4: ldur            x3, [fp, #-0x30]
    // 0xa331d8: add             x0, x3, #1
    // 0xa331dc: lsl             x1, x0, #1
    // 0xa331e0: StoreField: r2->field_b = r1
    //     0xa331e0: stur            w1, [x2, #0xb]
    // 0xa331e4: LoadField: r1 = r2->field_f
    //     0xa331e4: ldur            w1, [x2, #0xf]
    // 0xa331e8: DecompressPointer r1
    //     0xa331e8: add             x1, x1, HEAP, lsl #32
    // 0xa331ec: ldur            x0, [fp, #-0x38]
    // 0xa331f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa331f0: add             x25, x1, x3, lsl #2
    //     0xa331f4: add             x25, x25, #0xf
    //     0xa331f8: str             w0, [x25]
    //     0xa331fc: tbz             w0, #0, #0xa33218
    //     0xa33200: ldurb           w16, [x1, #-1]
    //     0xa33204: ldurb           w17, [x0, #-1]
    //     0xa33208: and             x16, x17, x16, lsr #2
    //     0xa3320c: tst             x16, HEAP, lsr #32
    //     0xa33210: b.eq            #0xa33218
    //     0xa33214: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa33218: ldur            x0, [fp, #-8]
    // 0xa3321c: LoadField: r1 = r0->field_b
    //     0xa3321c: ldur            w1, [x0, #0xb]
    // 0xa33220: DecompressPointer r1
    //     0xa33220: add             x1, x1, HEAP, lsl #32
    // 0xa33224: cmp             w1, NULL
    // 0xa33228: b.eq            #0xa33504
    // 0xa3322c: LoadField: r3 = r1->field_1f
    //     0xa3322c: ldur            w3, [x1, #0x1f]
    // 0xa33230: DecompressPointer r3
    //     0xa33230: add             x3, x3, HEAP, lsl #32
    // 0xa33234: stur            x3, [fp, #-0x10]
    // 0xa33238: cmp             w3, NULL
    // 0xa3323c: b.eq            #0xa332fc
    // 0xa33240: LoadField: r4 = r1->field_b
    //     0xa33240: ldur            w4, [x1, #0xb]
    // 0xa33244: DecompressPointer r4
    //     0xa33244: add             x4, x4, HEAP, lsl #32
    // 0xa33248: cmp             w4, NULL
    // 0xa3324c: b.eq            #0xa332fc
    // 0xa33250: LoadField: r1 = r4->field_27
    //     0xa33250: ldur            w1, [x4, #0x27]
    // 0xa33254: DecompressPointer r1
    //     0xa33254: add             x1, x1, HEAP, lsl #32
    // 0xa33258: r16 = Instance_ClipboardStatus
    //     0xa33258: ldr             x16, [PP, #0x4d90]  ; [pp+0x4d90] Obj!ClipboardStatus@e339e1
    // 0xa3325c: cmp             w1, w16
    // 0xa33260: b.ne            #0xa332fc
    // 0xa33264: r0 = _TextSelectionToolbarItemData()
    //     0xa33264: bl              #0xa33518  ; Allocate_TextSelectionToolbarItemDataStub -> _TextSelectionToolbarItemData (size=0x10)
    // 0xa33268: mov             x2, x0
    // 0xa3326c: r0 = "Paste"
    //     0xa3326c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ac0] "Paste"
    //     0xa33270: ldr             x0, [x0, #0xac0]
    // 0xa33274: stur            x2, [fp, #-0x38]
    // 0xa33278: StoreField: r2->field_7 = r0
    //     0xa33278: stur            w0, [x2, #7]
    // 0xa3327c: ldur            x0, [fp, #-0x10]
    // 0xa33280: StoreField: r2->field_b = r0
    //     0xa33280: stur            w0, [x2, #0xb]
    // 0xa33284: ldur            x0, [fp, #-0x20]
    // 0xa33288: LoadField: r1 = r0->field_b
    //     0xa33288: ldur            w1, [x0, #0xb]
    // 0xa3328c: LoadField: r3 = r0->field_f
    //     0xa3328c: ldur            w3, [x0, #0xf]
    // 0xa33290: DecompressPointer r3
    //     0xa33290: add             x3, x3, HEAP, lsl #32
    // 0xa33294: LoadField: r4 = r3->field_b
    //     0xa33294: ldur            w4, [x3, #0xb]
    // 0xa33298: r3 = LoadInt32Instr(r1)
    //     0xa33298: sbfx            x3, x1, #1, #0x1f
    // 0xa3329c: stur            x3, [fp, #-0x30]
    // 0xa332a0: r1 = LoadInt32Instr(r4)
    //     0xa332a0: sbfx            x1, x4, #1, #0x1f
    // 0xa332a4: cmp             x3, x1
    // 0xa332a8: b.ne            #0xa332b4
    // 0xa332ac: mov             x1, x0
    // 0xa332b0: r0 = _growToNextCapacity()
    //     0xa332b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa332b4: ldur            x2, [fp, #-0x20]
    // 0xa332b8: ldur            x3, [fp, #-0x30]
    // 0xa332bc: add             x0, x3, #1
    // 0xa332c0: lsl             x1, x0, #1
    // 0xa332c4: StoreField: r2->field_b = r1
    //     0xa332c4: stur            w1, [x2, #0xb]
    // 0xa332c8: LoadField: r1 = r2->field_f
    //     0xa332c8: ldur            w1, [x2, #0xf]
    // 0xa332cc: DecompressPointer r1
    //     0xa332cc: add             x1, x1, HEAP, lsl #32
    // 0xa332d0: ldur            x0, [fp, #-0x38]
    // 0xa332d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa332d4: add             x25, x1, x3, lsl #2
    //     0xa332d8: add             x25, x25, #0xf
    //     0xa332dc: str             w0, [x25]
    //     0xa332e0: tbz             w0, #0, #0xa332fc
    //     0xa332e4: ldurb           w16, [x1, #-1]
    //     0xa332e8: ldurb           w17, [x0, #-1]
    //     0xa332ec: and             x16, x17, x16, lsr #2
    //     0xa332f0: tst             x16, HEAP, lsr #32
    //     0xa332f4: b.eq            #0xa332fc
    //     0xa332f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa332fc: ldur            x0, [fp, #-8]
    // 0xa33300: LoadField: r1 = r0->field_b
    //     0xa33300: ldur            w1, [x0, #0xb]
    // 0xa33304: DecompressPointer r1
    //     0xa33304: add             x1, x1, HEAP, lsl #32
    // 0xa33308: cmp             w1, NULL
    // 0xa3330c: b.eq            #0xa33508
    // 0xa33310: LoadField: r0 = r1->field_23
    //     0xa33310: ldur            w0, [x1, #0x23]
    // 0xa33314: DecompressPointer r0
    //     0xa33314: add             x0, x0, HEAP, lsl #32
    // 0xa33318: stur            x0, [fp, #-8]
    // 0xa3331c: cmp             w0, NULL
    // 0xa33320: b.eq            #0xa333bc
    // 0xa33324: r0 = _TextSelectionToolbarItemData()
    //     0xa33324: bl              #0xa33518  ; Allocate_TextSelectionToolbarItemDataStub -> _TextSelectionToolbarItemData (size=0x10)
    // 0xa33328: mov             x2, x0
    // 0xa3332c: r0 = "Select all"
    //     0xa3332c: add             x0, PP, #0x39, lsl #12  ; [pp+0x39ac8] "Select all"
    //     0xa33330: ldr             x0, [x0, #0xac8]
    // 0xa33334: stur            x2, [fp, #-0x10]
    // 0xa33338: StoreField: r2->field_7 = r0
    //     0xa33338: stur            w0, [x2, #7]
    // 0xa3333c: ldur            x0, [fp, #-8]
    // 0xa33340: StoreField: r2->field_b = r0
    //     0xa33340: stur            w0, [x2, #0xb]
    // 0xa33344: ldur            x0, [fp, #-0x20]
    // 0xa33348: LoadField: r1 = r0->field_b
    //     0xa33348: ldur            w1, [x0, #0xb]
    // 0xa3334c: LoadField: r3 = r0->field_f
    //     0xa3334c: ldur            w3, [x0, #0xf]
    // 0xa33350: DecompressPointer r3
    //     0xa33350: add             x3, x3, HEAP, lsl #32
    // 0xa33354: LoadField: r4 = r3->field_b
    //     0xa33354: ldur            w4, [x3, #0xb]
    // 0xa33358: r3 = LoadInt32Instr(r1)
    //     0xa33358: sbfx            x3, x1, #1, #0x1f
    // 0xa3335c: stur            x3, [fp, #-0x30]
    // 0xa33360: r1 = LoadInt32Instr(r4)
    //     0xa33360: sbfx            x1, x4, #1, #0x1f
    // 0xa33364: cmp             x3, x1
    // 0xa33368: b.ne            #0xa33374
    // 0xa3336c: mov             x1, x0
    // 0xa33370: r0 = _growToNextCapacity()
    //     0xa33370: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa33374: ldur            x2, [fp, #-0x20]
    // 0xa33378: ldur            x3, [fp, #-0x30]
    // 0xa3337c: add             x0, x3, #1
    // 0xa33380: lsl             x1, x0, #1
    // 0xa33384: StoreField: r2->field_b = r1
    //     0xa33384: stur            w1, [x2, #0xb]
    // 0xa33388: LoadField: r1 = r2->field_f
    //     0xa33388: ldur            w1, [x2, #0xf]
    // 0xa3338c: DecompressPointer r1
    //     0xa3338c: add             x1, x1, HEAP, lsl #32
    // 0xa33390: ldur            x0, [fp, #-0x10]
    // 0xa33394: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa33394: add             x25, x1, x3, lsl #2
    //     0xa33398: add             x25, x25, #0xf
    //     0xa3339c: str             w0, [x25]
    //     0xa333a0: tbz             w0, #0, #0xa333bc
    //     0xa333a4: ldurb           w16, [x1, #-1]
    //     0xa333a8: ldurb           w17, [x0, #-1]
    //     0xa333ac: and             x16, x17, x16, lsr #2
    //     0xa333b0: tst             x16, HEAP, lsr #32
    //     0xa333b4: b.eq            #0xa333bc
    //     0xa333b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa333bc: r1 = 1
    //     0xa333bc: movz            x1, #0x1
    // 0xa333c0: r0 = AllocateContext()
    //     0xa333c0: bl              #0xec126c  ; AllocateContextStub
    // 0xa333c4: ldur            x1, [fp, #-0x20]
    // 0xa333c8: stur            x0, [fp, #-8]
    // 0xa333cc: StoreField: r0->field_f = r1
    //     0xa333cc: stur            w1, [x0, #0xf]
    // 0xa333d0: LoadField: r2 = r1->field_b
    //     0xa333d0: ldur            w2, [x1, #0xb]
    // 0xa333d4: cbnz            w2, #0xa333e8
    // 0xa333d8: r0 = Instance_SizedBox
    //     0xa333d8: ldr             x0, [PP, #0x4c90]  ; [pp+0x4c90] Obj!SizedBox@e1df81
    // 0xa333dc: LeaveFrame
    //     0xa333dc: mov             SP, fp
    //     0xa333e0: ldp             fp, lr, [SP], #0x10
    // 0xa333e4: ret
    //     0xa333e4: ret             
    // 0xa333e8: ldur            x3, [fp, #-0x28]
    // 0xa333ec: ldur            x2, [fp, #-0x18]
    // 0xa333f0: r0 = asMap()
    //     0xa333f0: bl              #0x6dc898  ; [dart:collection] ListBase::asMap
    // 0xa333f4: mov             x1, x0
    // 0xa333f8: r0 = entries()
    //     0xa333f8: bl              #0x7f814c  ; [dart:collection] MapBase::entries
    // 0xa333fc: ldur            x2, [fp, #-8]
    // 0xa33400: r1 = Function '<anonymous closure>':.
    //     0xa33400: add             x1, PP, #0x51, lsl #12  ; [pp+0x51700] AnonymousClosure: (0xa33544), in [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::build (0xa32cec)
    //     0xa33404: ldr             x1, [x1, #0x700]
    // 0xa33408: stur            x0, [fp, #-8]
    // 0xa3340c: r0 = AllocateClosure()
    //     0xa3340c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa33410: r16 = <NTextSelectionToolbarTextButton>
    //     0xa33410: add             x16, PP, #0x51, lsl #12  ; [pp+0x51708] TypeArguments: <NTextSelectionToolbarTextButton>
    //     0xa33414: ldr             x16, [x16, #0x708]
    // 0xa33418: ldur            lr, [fp, #-8]
    // 0xa3341c: stp             lr, x16, [SP, #8]
    // 0xa33420: str             x0, [SP]
    // 0xa33424: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa33424: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa33428: r0 = map()
    //     0xa33428: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0xa3342c: LoadField: r1 = r0->field_7
    //     0xa3342c: ldur            w1, [x0, #7]
    // 0xa33430: DecompressPointer r1
    //     0xa33430: add             x1, x1, HEAP, lsl #32
    // 0xa33434: mov             x2, x0
    // 0xa33438: r0 = _GrowableList.of()
    //     0xa33438: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa3343c: stur            x0, [fp, #-8]
    // 0xa33440: r0 = TextSelectionToolbar()
    //     0xa33440: bl              #0xa3350c  ; AllocateTextSelectionToolbarStub -> TextSelectionToolbar (size=0x1c)
    // 0xa33444: ldur            x1, [fp, #-0x28]
    // 0xa33448: StoreField: r0->field_b = r1
    //     0xa33448: stur            w1, [x0, #0xb]
    // 0xa3344c: ldur            x1, [fp, #-0x18]
    // 0xa33450: StoreField: r0->field_f = r1
    //     0xa33450: stur            w1, [x0, #0xf]
    // 0xa33454: r1 = Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static.
    //     0xa33454: add             x1, PP, #0x39, lsl #12  ; [pp+0x39a48] Closure: (BuildContext, Widget) => Widget from Function '_defaultToolbarBuilder@614142888': static. (0x7e54fb40e098)
    //     0xa33458: ldr             x1, [x1, #0xa48]
    // 0xa3345c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa3345c: stur            w1, [x0, #0x17]
    // 0xa33460: ldur            x1, [fp, #-8]
    // 0xa33464: StoreField: r0->field_13 = r1
    //     0xa33464: stur            w1, [x0, #0x13]
    // 0xa33468: LeaveFrame
    //     0xa33468: mov             SP, fp
    //     0xa3346c: ldp             fp, lr, [SP], #0x10
    // 0xa33470: ret
    //     0xa33470: ret             
    // 0xa33474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa33474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa33478: b               #0xa32d18
    // 0xa3347c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3347c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33480: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33484: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33488: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3348c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa3348c: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa33490: SaveReg d3
    //     0xa33490: str             q3, [SP, #-0x10]!
    // 0xa33494: stp             x1, x2, [SP, #-0x10]!
    // 0xa33498: SaveReg r0
    //     0xa33498: str             x0, [SP, #-8]!
    // 0xa3349c: r0 = AllocateDouble()
    //     0xa3349c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa334a0: mov             x3, x0
    // 0xa334a4: RestoreReg r0
    //     0xa334a4: ldr             x0, [SP], #8
    // 0xa334a8: ldp             x1, x2, [SP], #0x10
    // 0xa334ac: RestoreReg d3
    //     0xa334ac: ldr             q3, [SP], #0x10
    // 0xa334b0: b               #0xa32f18
    // 0xa334b4: SaveReg d3
    //     0xa334b4: str             q3, [SP, #-0x10]!
    // 0xa334b8: stp             x1, x2, [SP, #-0x10]!
    // 0xa334bc: SaveReg r0
    //     0xa334bc: str             x0, [SP, #-8]!
    // 0xa334c0: r0 = AllocateDouble()
    //     0xa334c0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa334c4: mov             x3, x0
    // 0xa334c8: RestoreReg r0
    //     0xa334c8: ldr             x0, [SP], #8
    // 0xa334cc: ldp             x1, x2, [SP], #0x10
    // 0xa334d0: RestoreReg d3
    //     0xa334d0: ldr             q3, [SP], #0x10
    // 0xa334d4: b               #0xa32f74
    // 0xa334d8: SaveReg d0
    //     0xa334d8: str             q0, [SP, #-0x10]!
    // 0xa334dc: stp             x2, x3, [SP, #-0x10]!
    // 0xa334e0: SaveReg r1
    //     0xa334e0: str             x1, [SP, #-8]!
    // 0xa334e4: r0 = AllocateDouble()
    //     0xa334e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa334e8: RestoreReg r1
    //     0xa334e8: ldr             x1, [SP], #8
    // 0xa334ec: ldp             x2, x3, [SP], #0x10
    // 0xa334f0: RestoreReg d0
    //     0xa334f0: ldr             q0, [SP], #0x10
    // 0xa334f4: b               #0xa32fa8
    // 0xa334f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa334f8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa334fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa334fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33500: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33500: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33504: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33504: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa33508: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa33508: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] NTextSelectionToolbarTextButton <anonymous closure>(dynamic, MapEntry<int, _TextSelectionToolbarItemData>) {
    // ** addr: 0xa33544, size: 0xd0
    // 0xa33544: EnterFrame
    //     0xa33544: stp             fp, lr, [SP, #-0x10]!
    //     0xa33548: mov             fp, SP
    // 0xa3354c: AllocStack(0x20)
    //     0xa3354c: sub             SP, SP, #0x20
    // 0xa33550: SetupParameters()
    //     0xa33550: ldr             x0, [fp, #0x18]
    //     0xa33554: ldur            w1, [x0, #0x17]
    //     0xa33558: add             x1, x1, HEAP, lsl #32
    // 0xa3355c: CheckStackOverflow
    //     0xa3355c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa33560: cmp             SP, x16
    //     0xa33564: b.ls            #0xa33608
    // 0xa33568: ldr             x0, [fp, #0x10]
    // 0xa3356c: LoadField: r2 = r0->field_b
    //     0xa3356c: ldur            w2, [x0, #0xb]
    // 0xa33570: DecompressPointer r2
    //     0xa33570: add             x2, x2, HEAP, lsl #32
    // 0xa33574: LoadField: r3 = r1->field_f
    //     0xa33574: ldur            w3, [x1, #0xf]
    // 0xa33578: DecompressPointer r3
    //     0xa33578: add             x3, x3, HEAP, lsl #32
    // 0xa3357c: LoadField: r1 = r3->field_b
    //     0xa3357c: ldur            w1, [x3, #0xb]
    // 0xa33580: r3 = LoadInt32Instr(r1)
    //     0xa33580: sbfx            x3, x1, #1, #0x1f
    // 0xa33584: mov             x1, x2
    // 0xa33588: mov             x2, x3
    // 0xa3358c: r0 = getPadding()
    //     0xa3358c: bl              #0xa33620  ; [package:nuikit/src/widgets/selection_controls.dart] NTextSelectionToolbarTextButton::getPadding
    // 0xa33590: mov             x1, x0
    // 0xa33594: ldr             x0, [fp, #0x10]
    // 0xa33598: stur            x1, [fp, #-0x18]
    // 0xa3359c: LoadField: r2 = r0->field_f
    //     0xa3359c: ldur            w2, [x0, #0xf]
    // 0xa335a0: DecompressPointer r2
    //     0xa335a0: add             x2, x2, HEAP, lsl #32
    // 0xa335a4: cmp             w2, NULL
    // 0xa335a8: b.eq            #0xa33610
    // 0xa335ac: LoadField: r0 = r2->field_b
    //     0xa335ac: ldur            w0, [x2, #0xb]
    // 0xa335b0: DecompressPointer r0
    //     0xa335b0: add             x0, x0, HEAP, lsl #32
    // 0xa335b4: stur            x0, [fp, #-0x10]
    // 0xa335b8: LoadField: r3 = r2->field_7
    //     0xa335b8: ldur            w3, [x2, #7]
    // 0xa335bc: DecompressPointer r3
    //     0xa335bc: add             x3, x3, HEAP, lsl #32
    // 0xa335c0: stur            x3, [fp, #-8]
    // 0xa335c4: r0 = Text()
    //     0xa335c4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa335c8: mov             x1, x0
    // 0xa335cc: ldur            x0, [fp, #-8]
    // 0xa335d0: stur            x1, [fp, #-0x20]
    // 0xa335d4: StoreField: r1->field_b = r0
    //     0xa335d4: stur            w0, [x1, #0xb]
    // 0xa335d8: r0 = NTextSelectionToolbarTextButton()
    //     0xa335d8: bl              #0xa33614  ; AllocateNTextSelectionToolbarTextButtonStub -> NTextSelectionToolbarTextButton (size=0x28)
    // 0xa335dc: ldur            x1, [fp, #-0x20]
    // 0xa335e0: StoreField: r0->field_1b = r1
    //     0xa335e0: stur            w1, [x0, #0x1b]
    // 0xa335e4: ldur            x2, [fp, #-0x18]
    // 0xa335e8: StoreField: r0->field_23 = r2
    //     0xa335e8: stur            w2, [x0, #0x23]
    // 0xa335ec: ldur            x3, [fp, #-0x10]
    // 0xa335f0: StoreField: r0->field_1f = r3
    //     0xa335f0: stur            w3, [x0, #0x1f]
    // 0xa335f4: StoreField: r0->field_b = r1
    //     0xa335f4: stur            w1, [x0, #0xb]
    // 0xa335f8: StoreField: r0->field_13 = r2
    //     0xa335f8: stur            w2, [x0, #0x13]
    // 0xa335fc: LeaveFrame
    //     0xa335fc: mov             SP, fp
    //     0xa33600: ldp             fp, lr, [SP], #0x10
    // 0xa33604: ret
    //     0xa33604: ret             
    // 0xa33608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa33608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3360c: b               #0xa33568
    // 0xa33610: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa33610: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa834e8, size: 0x84
    // 0xa834e8: EnterFrame
    //     0xa834e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa834ec: mov             fp, SP
    // 0xa834f0: AllocStack(0x10)
    //     0xa834f0: sub             SP, SP, #0x10
    // 0xa834f4: SetupParameters(_TextSelectionControlsToolbarState this /* r1 => r0, fp-0x8 */)
    //     0xa834f4: mov             x0, x1
    //     0xa834f8: stur            x1, [fp, #-8]
    // 0xa834fc: CheckStackOverflow
    //     0xa834fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83500: cmp             SP, x16
    //     0xa83504: b.ls            #0xa83560
    // 0xa83508: mov             x1, x0
    // 0xa8350c: r0 = dispose()
    //     0xa8350c: bl              #0xa8356c  ; [package:nuikit/src/widgets/selection_controls.dart] __TextSelectionControlsToolbarState&State&TickerProviderStateMixin::dispose
    // 0xa83510: ldur            x2, [fp, #-8]
    // 0xa83514: LoadField: r0 = r2->field_b
    //     0xa83514: ldur            w0, [x2, #0xb]
    // 0xa83518: DecompressPointer r0
    //     0xa83518: add             x0, x0, HEAP, lsl #32
    // 0xa8351c: cmp             w0, NULL
    // 0xa83520: b.eq            #0xa83568
    // 0xa83524: LoadField: r3 = r0->field_b
    //     0xa83524: ldur            w3, [x0, #0xb]
    // 0xa83528: DecompressPointer r3
    //     0xa83528: add             x3, x3, HEAP, lsl #32
    // 0xa8352c: stur            x3, [fp, #-0x10]
    // 0xa83530: cmp             w3, NULL
    // 0xa83534: b.eq            #0xa83550
    // 0xa83538: r1 = Function '_onChangedClipboardStatus@**********':.
    //     0xa83538: add             x1, PP, #0x51, lsl #12  ; [pp+0x51738] AnonymousClosure: (0x965314), in [package:nuikit/src/widgets/selection_controls.dart] _TextSelectionControlsToolbarState::_onChangedClipboardStatus (0x96534c)
    //     0xa8353c: ldr             x1, [x1, #0x738]
    // 0xa83540: r0 = AllocateClosure()
    //     0xa83540: bl              #0xec1630  ; AllocateClosureStub
    // 0xa83544: ldur            x1, [fp, #-0x10]
    // 0xa83548: mov             x2, x0
    // 0xa8354c: r0 = removeListener()
    //     0xa8354c: bl              #0xa8a478  ; [package:flutter/src/widgets/text_selection.dart] LiveTextInputStatusNotifier::removeListener
    // 0xa83550: r0 = Null
    //     0xa83550: mov             x0, NULL
    // 0xa83554: LeaveFrame
    //     0xa83554: mov             SP, fp
    //     0xa83558: ldp             fp, lr, [SP], #0x10
    // 0xa8355c: ret
    //     0xa8355c: ret             
    // 0xa83560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83564: b               #0xa83508
    // 0xa83568: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa83568: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4718, size: 0x34, field offset: 0xc
//   const constructor, 
class _TextSelectionControlsToolbar extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa944a0, size: 0x24
    // 0xa944a0: EnterFrame
    //     0xa944a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa944a4: mov             fp, SP
    // 0xa944a8: mov             x0, x1
    // 0xa944ac: r1 = <_TextSelectionControlsToolbar>
    //     0xa944ac: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a578] TypeArguments: <_TextSelectionControlsToolbar>
    //     0xa944b0: ldr             x1, [x1, #0x578]
    // 0xa944b4: r0 = _TextSelectionControlsToolbarState()
    //     0xa944b4: bl              #0xa944c4  ; Allocate_TextSelectionControlsToolbarStateStub -> _TextSelectionControlsToolbarState (size=0x1c)
    // 0xa944b8: LeaveFrame
    //     0xa944b8: mov             SP, fp
    //     0xa944bc: ldp             fp, lr, [SP], #0x10
    // 0xa944c0: ret
    //     0xa944c0: ret             
  }
}

// class id: 5365, size: 0x28, field offset: 0x1c
//   const constructor, 
class NTextSelectionToolbarTextButton extends TextSelectionToolbarTextButton {

  static _ getPadding(/* No info */) {
    // ** addr: 0xa33620, size: 0xe4
    // 0xa33620: EnterFrame
    //     0xa33620: stp             fp, lr, [SP, #-0x10]!
    //     0xa33624: mov             fp, SP
    // 0xa33628: AllocStack(0x10)
    //     0xa33628: sub             SP, SP, #0x10
    // 0xa3362c: r0 = LoadInt32Instr(r1)
    //     0xa3362c: sbfx            x0, x1, #1, #0x1f
    //     0xa33630: tbz             w1, #0, #0xa33638
    //     0xa33634: ldur            x0, [x1, #7]
    // 0xa33638: cbnz            x0, #0xa3365c
    // 0xa3363c: cmp             x2, #1
    // 0xa33640: b.ne            #0xa33650
    // 0xa33644: r0 = Instance__TextSelectionToolbarItemPosition
    //     0xa33644: add             x0, PP, #0x51, lsl #12  ; [pp+0x51718] Obj!_TextSelectionToolbarItemPosition@e31181
    //     0xa33648: ldr             x0, [x0, #0x718]
    // 0xa3364c: b               #0xa3367c
    // 0xa33650: r0 = Instance__TextSelectionToolbarItemPosition
    //     0xa33650: add             x0, PP, #0x51, lsl #12  ; [pp+0x51720] Obj!_TextSelectionToolbarItemPosition@e31161
    //     0xa33654: ldr             x0, [x0, #0x720]
    // 0xa33658: b               #0xa3367c
    // 0xa3365c: sub             x1, x2, #1
    // 0xa33660: cmp             x0, x1
    // 0xa33664: b.ne            #0xa33674
    // 0xa33668: r0 = Instance__TextSelectionToolbarItemPosition
    //     0xa33668: add             x0, PP, #0x51, lsl #12  ; [pp+0x51728] Obj!_TextSelectionToolbarItemPosition@e31141
    //     0xa3366c: ldr             x0, [x0, #0x728]
    // 0xa33670: b               #0xa3367c
    // 0xa33674: r0 = Instance__TextSelectionToolbarItemPosition
    //     0xa33674: add             x0, PP, #0x51, lsl #12  ; [pp+0x51730] Obj!_TextSelectionToolbarItemPosition@e31121
    //     0xa33678: ldr             x0, [x0, #0x730]
    // 0xa3367c: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa3367c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51720] Obj!_TextSelectionToolbarItemPosition@e31161
    //     0xa33680: ldr             x16, [x16, #0x720]
    // 0xa33684: cmp             w0, w16
    // 0xa33688: b.eq            #0xa3369c
    // 0xa3368c: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa3368c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51718] Obj!_TextSelectionToolbarItemPosition@e31181
    //     0xa33690: ldr             x16, [x16, #0x718]
    // 0xa33694: cmp             w0, w16
    // 0xa33698: b.ne            #0xa336a4
    // 0xa3369c: d0 = 14.500000
    //     0xa3369c: fmov            d0, #14.50000000
    // 0xa336a0: b               #0xa336a8
    // 0xa336a4: d0 = 9.500000
    //     0xa336a4: fmov            d0, #9.50000000
    // 0xa336a8: stur            d0, [fp, #-0x10]
    // 0xa336ac: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa336ac: add             x16, PP, #0x51, lsl #12  ; [pp+0x51728] Obj!_TextSelectionToolbarItemPosition@e31141
    //     0xa336b0: ldr             x16, [x16, #0x728]
    // 0xa336b4: cmp             w0, w16
    // 0xa336b8: b.eq            #0xa336cc
    // 0xa336bc: r16 = Instance__TextSelectionToolbarItemPosition
    //     0xa336bc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51718] Obj!_TextSelectionToolbarItemPosition@e31181
    //     0xa336c0: ldr             x16, [x16, #0x718]
    // 0xa336c4: cmp             w0, w16
    // 0xa336c8: b.ne            #0xa336d4
    // 0xa336cc: d1 = 14.500000
    //     0xa336cc: fmov            d1, #14.50000000
    // 0xa336d0: b               #0xa336d8
    // 0xa336d4: d1 = 9.500000
    //     0xa336d4: fmov            d1, #9.50000000
    // 0xa336d8: stur            d1, [fp, #-8]
    // 0xa336dc: r0 = EdgeInsets()
    //     0xa336dc: bl              #0x65dd90  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa336e0: ldur            d0, [fp, #-0x10]
    // 0xa336e4: StoreField: r0->field_7 = d0
    //     0xa336e4: stur            d0, [x0, #7]
    // 0xa336e8: StoreField: r0->field_f = rZR
    //     0xa336e8: stur            xzr, [x0, #0xf]
    // 0xa336ec: ldur            d0, [fp, #-8]
    // 0xa336f0: ArrayStore: r0[0] = d0  ; List_8
    //     0xa336f0: stur            d0, [x0, #0x17]
    // 0xa336f4: StoreField: r0->field_1f = rZR
    //     0xa336f4: stur            xzr, [x0, #0x1f]
    // 0xa336f8: LeaveFrame
    //     0xa336f8: mov             SP, fp
    //     0xa336fc: ldp             fp, lr, [SP], #0x10
    // 0xa33700: ret
    //     0xa33700: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0xaa0e38, size: 0xd8
    // 0xaa0e38: EnterFrame
    //     0xaa0e38: stp             fp, lr, [SP, #-0x10]!
    //     0xaa0e3c: mov             fp, SP
    // 0xaa0e40: AllocStack(0x48)
    //     0xaa0e40: sub             SP, SP, #0x48
    // 0xaa0e44: SetupParameters(NTextSelectionToolbarTextButton this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xaa0e44: mov             x0, x1
    //     0xaa0e48: stur            x1, [fp, #-8]
    //     0xaa0e4c: mov             x1, x2
    // 0xaa0e50: CheckStackOverflow
    //     0xaa0e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa0e54: cmp             SP, x16
    //     0xaa0e58: b.ls            #0xaa0f08
    // 0xaa0e5c: r0 = of()
    //     0xaa0e5c: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa0e60: r1 = _ConstMap len:3
    //     0xaa0e60: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xaa0e64: ldr             x1, [x1, #0xbe8]
    // 0xaa0e68: r2 = 2
    //     0xaa0e68: movz            x2, #0x2
    // 0xaa0e6c: r0 = []()
    //     0xaa0e6c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaa0e70: mov             x1, x0
    // 0xaa0e74: ldur            x0, [fp, #-8]
    // 0xaa0e78: LoadField: r2 = r0->field_23
    //     0xaa0e78: ldur            w2, [x0, #0x23]
    // 0xaa0e7c: DecompressPointer r2
    //     0xaa0e7c: add             x2, x2, HEAP, lsl #32
    // 0xaa0e80: r16 = Instance_Color
    //     0xaa0e80: ldr             x16, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xaa0e84: stp             x1, x16, [SP, #0x18]
    // 0xaa0e88: r16 = Instance_RoundedRectangleBorder
    //     0xaa0e88: add             x16, PP, #0x39, lsl #12  ; [pp+0x39848] Obj!RoundedRectangleBorder@e146c1
    //     0xaa0e8c: ldr             x16, [x16, #0x848]
    // 0xaa0e90: r30 = Instance_Size
    //     0xaa0e90: add             lr, PP, #0x43, lsl #12  ; [pp+0x438a0] Obj!Size@e2c141
    //     0xaa0e94: ldr             lr, [lr, #0x8a0]
    // 0xaa0e98: stp             lr, x16, [SP, #8]
    // 0xaa0e9c: str             x2, [SP]
    // 0xaa0ea0: r4 = const [0, 0x5, 0x5, 0, backgroundColor, 0x1, foregroundColor, 0, minimumSize, 0x3, padding, 0x4, shape, 0x2, null]
    //     0xaa0ea0: add             x4, PP, #0x57, lsl #12  ; [pp+0x57fd8] List(15) [0, 0x5, 0x5, 0, "backgroundColor", 0x1, "foregroundColor", 0, "minimumSize", 0x3, "padding", 0x4, "shape", 0x2, Null]
    //     0xaa0ea4: ldr             x4, [x4, #0xfd8]
    // 0xaa0ea8: r0 = styleFrom()
    //     0xaa0ea8: bl              #0xa9bb70  ; [package:flutter/src/material/text_button.dart] TextButton::styleFrom
    // 0xaa0eac: mov             x1, x0
    // 0xaa0eb0: ldur            x0, [fp, #-8]
    // 0xaa0eb4: stur            x1, [fp, #-0x20]
    // 0xaa0eb8: LoadField: r2 = r0->field_1f
    //     0xaa0eb8: ldur            w2, [x0, #0x1f]
    // 0xaa0ebc: DecompressPointer r2
    //     0xaa0ebc: add             x2, x2, HEAP, lsl #32
    // 0xaa0ec0: stur            x2, [fp, #-0x18]
    // 0xaa0ec4: LoadField: r3 = r0->field_1b
    //     0xaa0ec4: ldur            w3, [x0, #0x1b]
    // 0xaa0ec8: DecompressPointer r3
    //     0xaa0ec8: add             x3, x3, HEAP, lsl #32
    // 0xaa0ecc: stur            x3, [fp, #-0x10]
    // 0xaa0ed0: r0 = TextButton()
    //     0xaa0ed0: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaa0ed4: ldur            x1, [fp, #-0x18]
    // 0xaa0ed8: StoreField: r0->field_b = r1
    //     0xaa0ed8: stur            w1, [x0, #0xb]
    // 0xaa0edc: ldur            x1, [fp, #-0x20]
    // 0xaa0ee0: StoreField: r0->field_1b = r1
    //     0xaa0ee0: stur            w1, [x0, #0x1b]
    // 0xaa0ee4: r1 = false
    //     0xaa0ee4: add             x1, NULL, #0x30  ; false
    // 0xaa0ee8: StoreField: r0->field_27 = r1
    //     0xaa0ee8: stur            w1, [x0, #0x27]
    // 0xaa0eec: r1 = true
    //     0xaa0eec: add             x1, NULL, #0x20  ; true
    // 0xaa0ef0: StoreField: r0->field_2f = r1
    //     0xaa0ef0: stur            w1, [x0, #0x2f]
    // 0xaa0ef4: ldur            x1, [fp, #-0x10]
    // 0xaa0ef8: StoreField: r0->field_37 = r1
    //     0xaa0ef8: stur            w1, [x0, #0x37]
    // 0xaa0efc: LeaveFrame
    //     0xaa0efc: mov             SP, fp
    //     0xaa0f00: ldp             fp, lr, [SP], #0x10
    // 0xaa0f04: ret
    //     0xaa0f04: ret             
    // 0xaa0f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa0f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa0f0c: b               #0xaa0e5c
  }
}

// class id: 6850, size: 0x14, field offset: 0x14
enum _TextSelectionToolbarItemPosition extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cd30, size: 0x64
    // 0xc4cd30: EnterFrame
    //     0xc4cd30: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cd34: mov             fp, SP
    // 0xc4cd38: AllocStack(0x10)
    //     0xc4cd38: sub             SP, SP, #0x10
    // 0xc4cd3c: SetupParameters(_TextSelectionToolbarItemPosition this /* r1 => r0, fp-0x8 */)
    //     0xc4cd3c: mov             x0, x1
    //     0xc4cd40: stur            x1, [fp, #-8]
    // 0xc4cd44: CheckStackOverflow
    //     0xc4cd44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4cd48: cmp             SP, x16
    //     0xc4cd4c: b.ls            #0xc4cd8c
    // 0xc4cd50: r1 = Null
    //     0xc4cd50: mov             x1, NULL
    // 0xc4cd54: r2 = 4
    //     0xc4cd54: movz            x2, #0x4
    // 0xc4cd58: r0 = AllocateArray()
    //     0xc4cd58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4cd5c: r16 = "_TextSelectionToolbarItemPosition."
    //     0xc4cd5c: add             x16, PP, #0x43, lsl #12  ; [pp+0x438b8] "_TextSelectionToolbarItemPosition."
    //     0xc4cd60: ldr             x16, [x16, #0x8b8]
    // 0xc4cd64: StoreField: r0->field_f = r16
    //     0xc4cd64: stur            w16, [x0, #0xf]
    // 0xc4cd68: ldur            x1, [fp, #-8]
    // 0xc4cd6c: LoadField: r2 = r1->field_f
    //     0xc4cd6c: ldur            w2, [x1, #0xf]
    // 0xc4cd70: DecompressPointer r2
    //     0xc4cd70: add             x2, x2, HEAP, lsl #32
    // 0xc4cd74: StoreField: r0->field_13 = r2
    //     0xc4cd74: stur            w2, [x0, #0x13]
    // 0xc4cd78: str             x0, [SP]
    // 0xc4cd7c: r0 = _interpolate()
    //     0xc4cd7c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4cd80: LeaveFrame
    //     0xc4cd80: mov             SP, fp
    //     0xc4cd84: ldp             fp, lr, [SP], #0x10
    // 0xc4cd88: ret
    //     0xc4cd88: ret             
    // 0xc4cd8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4cd8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cd90: b               #0xc4cd50
  }
}
