// lib: , url: package:nuikit/src/widgets/text_field/search_text_field.dart

// class id: 1049992, size: 0x8
class :: {
}

// class id: 5071, size: 0x14, field offset: 0xc
//   const constructor, 
class NSearchTextFieldDisabled extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb8293c, size: 0x41c
    // 0xb8293c: EnterFrame
    //     0xb8293c: stp             fp, lr, [SP, #-0x10]!
    //     0xb82940: mov             fp, SP
    // 0xb82944: AllocStack(0x58)
    //     0xb82944: sub             SP, SP, #0x58
    // 0xb82948: SetupParameters(NSearchTextFieldDisabled this /* r1 => r0, fp-0x10 */)
    //     0xb82948: mov             x0, x1
    //     0xb8294c: stur            x1, [fp, #-0x10]
    // 0xb82950: CheckStackOverflow
    //     0xb82950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb82954: cmp             SP, x16
    //     0xb82958: b.ls            #0xb82d34
    // 0xb8295c: LoadField: r3 = r0->field_b
    //     0xb8295c: ldur            w3, [x0, #0xb]
    // 0xb82960: DecompressPointer r3
    //     0xb82960: add             x3, x3, HEAP, lsl #32
    // 0xb82964: stur            x3, [fp, #-8]
    // 0xb82968: r1 = _ConstMap len:3
    //     0xb82968: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb8296c: ldr             x1, [x1, #0xbe8]
    // 0xb82970: r2 = 2
    //     0xb82970: movz            x2, #0x2
    // 0xb82974: r0 = []()
    //     0xb82974: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82978: r16 = <Color?>
    //     0xb82978: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb8297c: ldr             x16, [x16, #0x98]
    // 0xb82980: stp             x0, x16, [SP, #8]
    // 0xb82984: r16 = Instance_MaterialColor
    //     0xb82984: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb82988: ldr             x16, [x16, #0xe38]
    // 0xb8298c: str             x16, [SP]
    // 0xb82990: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82990: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb82994: r0 = mode()
    //     0xb82994: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82998: stur            x0, [fp, #-0x18]
    // 0xb8299c: r0 = Radius()
    //     0xb8299c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb829a0: d0 = 8.000000
    //     0xb829a0: fmov            d0, #8.00000000
    // 0xb829a4: stur            x0, [fp, #-0x20]
    // 0xb829a8: StoreField: r0->field_7 = d0
    //     0xb829a8: stur            d0, [x0, #7]
    // 0xb829ac: StoreField: r0->field_f = d0
    //     0xb829ac: stur            d0, [x0, #0xf]
    // 0xb829b0: r0 = BorderRadius()
    //     0xb829b0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb829b4: mov             x1, x0
    // 0xb829b8: ldur            x0, [fp, #-0x20]
    // 0xb829bc: stur            x1, [fp, #-0x28]
    // 0xb829c0: StoreField: r1->field_7 = r0
    //     0xb829c0: stur            w0, [x1, #7]
    // 0xb829c4: StoreField: r1->field_b = r0
    //     0xb829c4: stur            w0, [x1, #0xb]
    // 0xb829c8: StoreField: r1->field_f = r0
    //     0xb829c8: stur            w0, [x1, #0xf]
    // 0xb829cc: StoreField: r1->field_13 = r0
    //     0xb829cc: stur            w0, [x1, #0x13]
    // 0xb829d0: r0 = BoxDecoration()
    //     0xb829d0: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb829d4: mov             x3, x0
    // 0xb829d8: ldur            x0, [fp, #-0x18]
    // 0xb829dc: stur            x3, [fp, #-0x20]
    // 0xb829e0: StoreField: r3->field_7 = r0
    //     0xb829e0: stur            w0, [x3, #7]
    // 0xb829e4: ldur            x0, [fp, #-0x28]
    // 0xb829e8: StoreField: r3->field_13 = r0
    //     0xb829e8: stur            w0, [x3, #0x13]
    // 0xb829ec: r0 = Instance_BoxShape
    //     0xb829ec: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb829f0: ldr             x0, [x0, #0xca8]
    // 0xb829f4: StoreField: r3->field_23 = r0
    //     0xb829f4: stur            w0, [x3, #0x23]
    // 0xb829f8: r1 = _ConstMap len:6
    //     0xb829f8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb829fc: ldr             x1, [x1, #0xc20]
    // 0xb82a00: r2 = 6
    //     0xb82a00: movz            x2, #0x6
    // 0xb82a04: r0 = []()
    //     0xb82a04: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82a08: r1 = _ConstMap len:6
    //     0xb82a08: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82a0c: ldr             x1, [x1, #0xc20]
    // 0xb82a10: r2 = 8
    //     0xb82a10: movz            x2, #0x8
    // 0xb82a14: stur            x0, [fp, #-0x18]
    // 0xb82a18: r0 = []()
    //     0xb82a18: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82a1c: r16 = <Color?>
    //     0xb82a1c: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82a20: ldr             x16, [x16, #0x98]
    // 0xb82a24: stp             x0, x16, [SP, #8]
    // 0xb82a28: ldur            x16, [fp, #-0x18]
    // 0xb82a2c: str             x16, [SP]
    // 0xb82a30: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82a30: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb82a34: r0 = mode()
    //     0xb82a34: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82a38: stur            x0, [fp, #-0x18]
    // 0xb82a3c: r0 = Icon()
    //     0xb82a3c: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb82a40: mov             x1, x0
    // 0xb82a44: r0 = Instance_IconData
    //     0xb82a44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2ce48] Obj!IconData@e10231
    //     0xb82a48: ldr             x0, [x0, #0xe48]
    // 0xb82a4c: stur            x1, [fp, #-0x28]
    // 0xb82a50: StoreField: r1->field_b = r0
    //     0xb82a50: stur            w0, [x1, #0xb]
    // 0xb82a54: r0 = 18.000000
    //     0xb82a54: add             x0, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb82a58: ldr             x0, [x0, #0x958]
    // 0xb82a5c: StoreField: r1->field_f = r0
    //     0xb82a5c: stur            w0, [x1, #0xf]
    // 0xb82a60: ldur            x0, [fp, #-0x18]
    // 0xb82a64: StoreField: r1->field_23 = r0
    //     0xb82a64: stur            w0, [x1, #0x23]
    // 0xb82a68: ldur            x0, [fp, #-0x10]
    // 0xb82a6c: LoadField: r2 = r0->field_f
    //     0xb82a6c: ldur            w2, [x0, #0xf]
    // 0xb82a70: DecompressPointer r2
    //     0xb82a70: add             x2, x2, HEAP, lsl #32
    // 0xb82a74: stur            x2, [fp, #-0x18]
    // 0xb82a78: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb82a78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb82a7c: ldr             x0, [x0, #0x2670]
    //     0xb82a80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb82a84: cmp             w0, w16
    //     0xb82a88: b.ne            #0xb82a94
    //     0xb82a8c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb82a90: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb82a94: r0 = GetNavigation.textTheme()
    //     0xb82a94: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb82a98: LoadField: r1 = r0->field_2f
    //     0xb82a98: ldur            w1, [x0, #0x2f]
    // 0xb82a9c: DecompressPointer r1
    //     0xb82a9c: add             x1, x1, HEAP, lsl #32
    // 0xb82aa0: stur            x1, [fp, #-0x10]
    // 0xb82aa4: cmp             w1, NULL
    // 0xb82aa8: b.ne            #0xb82ab4
    // 0xb82aac: r3 = Null
    //     0xb82aac: mov             x3, NULL
    // 0xb82ab0: b               #0xb82b4c
    // 0xb82ab4: r0 = GetNavigation.textScaleFactor()
    //     0xb82ab4: bl              #0xb82868  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textScaleFactor
    // 0xb82ab8: mov             v1.16b, v0.16b
    // 0xb82abc: d0 = 16.000000
    //     0xb82abc: fmov            d0, #16.00000000
    // 0xb82ac0: fdiv            d2, d0, d1
    // 0xb82ac4: stur            d2, [fp, #-0x38]
    // 0xb82ac8: r1 = _ConstMap len:6
    //     0xb82ac8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82acc: ldr             x1, [x1, #0xc20]
    // 0xb82ad0: r2 = 6
    //     0xb82ad0: movz            x2, #0x6
    // 0xb82ad4: r0 = []()
    //     0xb82ad4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82ad8: r1 = _ConstMap len:6
    //     0xb82ad8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82adc: ldr             x1, [x1, #0xc20]
    // 0xb82ae0: r2 = 8
    //     0xb82ae0: movz            x2, #0x8
    // 0xb82ae4: stur            x0, [fp, #-0x30]
    // 0xb82ae8: r0 = []()
    //     0xb82ae8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82aec: r16 = <Color?>
    //     0xb82aec: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82af0: ldr             x16, [x16, #0x98]
    // 0xb82af4: stp             x0, x16, [SP, #8]
    // 0xb82af8: ldur            x16, [fp, #-0x30]
    // 0xb82afc: str             x16, [SP]
    // 0xb82b00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82b00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb82b04: r0 = mode()
    //     0xb82b04: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82b08: ldur            d0, [fp, #-0x38]
    // 0xb82b0c: r1 = inline_Allocate_Double()
    //     0xb82b0c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb82b10: add             x1, x1, #0x10
    //     0xb82b14: cmp             x2, x1
    //     0xb82b18: b.ls            #0xb82d3c
    //     0xb82b1c: str             x1, [THR, #0x50]  ; THR::top
    //     0xb82b20: sub             x1, x1, #0xf
    //     0xb82b24: movz            x2, #0xe15c
    //     0xb82b28: movk            x2, #0x3, lsl #16
    //     0xb82b2c: stur            x2, [x1, #-1]
    // 0xb82b30: StoreField: r1->field_7 = d0
    //     0xb82b30: stur            d0, [x1, #7]
    // 0xb82b34: stp             x0, x1, [SP]
    // 0xb82b38: ldur            x1, [fp, #-0x10]
    // 0xb82b3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb82b3c: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb82b40: ldr             x4, [x4, #0xaa0]
    // 0xb82b44: r0 = copyWith()
    //     0xb82b44: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb82b48: mov             x3, x0
    // 0xb82b4c: ldur            x2, [fp, #-8]
    // 0xb82b50: ldur            x0, [fp, #-0x28]
    // 0xb82b54: ldur            x1, [fp, #-0x18]
    // 0xb82b58: stur            x3, [fp, #-0x10]
    // 0xb82b5c: r0 = Text()
    //     0xb82b5c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb82b60: mov             x1, x0
    // 0xb82b64: ldur            x0, [fp, #-0x18]
    // 0xb82b68: stur            x1, [fp, #-0x30]
    // 0xb82b6c: StoreField: r1->field_b = r0
    //     0xb82b6c: stur            w0, [x1, #0xb]
    // 0xb82b70: ldur            x0, [fp, #-0x10]
    // 0xb82b74: StoreField: r1->field_13 = r0
    //     0xb82b74: stur            w0, [x1, #0x13]
    // 0xb82b78: r0 = Padding()
    //     0xb82b78: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82b7c: mov             x2, x0
    // 0xb82b80: r0 = Instance_EdgeInsets
    //     0xb82b80: add             x0, PP, #0x38, lsl #12  ; [pp+0x380b0] Obj!EdgeInsets@e13361
    //     0xb82b84: ldr             x0, [x0, #0xb0]
    // 0xb82b88: stur            x2, [fp, #-0x10]
    // 0xb82b8c: StoreField: r2->field_f = r0
    //     0xb82b8c: stur            w0, [x2, #0xf]
    // 0xb82b90: ldur            x0, [fp, #-0x30]
    // 0xb82b94: StoreField: r2->field_b = r0
    //     0xb82b94: stur            w0, [x2, #0xb]
    // 0xb82b98: r1 = <FlexParentData>
    //     0xb82b98: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb82b9c: ldr             x1, [x1, #0x720]
    // 0xb82ba0: r0 = Expanded()
    //     0xb82ba0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb82ba4: mov             x3, x0
    // 0xb82ba8: r0 = 1
    //     0xb82ba8: movz            x0, #0x1
    // 0xb82bac: stur            x3, [fp, #-0x18]
    // 0xb82bb0: StoreField: r3->field_13 = r0
    //     0xb82bb0: stur            x0, [x3, #0x13]
    // 0xb82bb4: r0 = Instance_FlexFit
    //     0xb82bb4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb82bb8: ldr             x0, [x0, #0x728]
    // 0xb82bbc: StoreField: r3->field_1b = r0
    //     0xb82bbc: stur            w0, [x3, #0x1b]
    // 0xb82bc0: ldur            x0, [fp, #-0x10]
    // 0xb82bc4: StoreField: r3->field_b = r0
    //     0xb82bc4: stur            w0, [x3, #0xb]
    // 0xb82bc8: r1 = Null
    //     0xb82bc8: mov             x1, NULL
    // 0xb82bcc: r2 = 8
    //     0xb82bcc: movz            x2, #0x8
    // 0xb82bd0: r0 = AllocateArray()
    //     0xb82bd0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb82bd4: mov             x2, x0
    // 0xb82bd8: ldur            x0, [fp, #-0x28]
    // 0xb82bdc: stur            x2, [fp, #-0x10]
    // 0xb82be0: StoreField: r2->field_f = r0
    //     0xb82be0: stur            w0, [x2, #0xf]
    // 0xb82be4: r16 = Instance_SizedBox
    //     0xb82be4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xb82be8: ldr             x16, [x16, #0x340]
    // 0xb82bec: StoreField: r2->field_13 = r16
    //     0xb82bec: stur            w16, [x2, #0x13]
    // 0xb82bf0: ldur            x0, [fp, #-0x18]
    // 0xb82bf4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb82bf4: stur            w0, [x2, #0x17]
    // 0xb82bf8: r16 = Instance_SizedBox
    //     0xb82bf8: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb82bfc: ldr             x16, [x16, #0x538]
    // 0xb82c00: StoreField: r2->field_1b = r16
    //     0xb82c00: stur            w16, [x2, #0x1b]
    // 0xb82c04: r1 = <Widget>
    //     0xb82c04: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb82c08: r0 = AllocateGrowableArray()
    //     0xb82c08: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb82c0c: mov             x1, x0
    // 0xb82c10: ldur            x0, [fp, #-0x10]
    // 0xb82c14: stur            x1, [fp, #-0x18]
    // 0xb82c18: StoreField: r1->field_f = r0
    //     0xb82c18: stur            w0, [x1, #0xf]
    // 0xb82c1c: r0 = 8
    //     0xb82c1c: movz            x0, #0x8
    // 0xb82c20: StoreField: r1->field_b = r0
    //     0xb82c20: stur            w0, [x1, #0xb]
    // 0xb82c24: r0 = Row()
    //     0xb82c24: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb82c28: mov             x1, x0
    // 0xb82c2c: r0 = Instance_Axis
    //     0xb82c2c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb82c30: stur            x1, [fp, #-0x10]
    // 0xb82c34: StoreField: r1->field_f = r0
    //     0xb82c34: stur            w0, [x1, #0xf]
    // 0xb82c38: r0 = Instance_MainAxisAlignment
    //     0xb82c38: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb82c3c: ldr             x0, [x0, #0xae8]
    // 0xb82c40: StoreField: r1->field_13 = r0
    //     0xb82c40: stur            w0, [x1, #0x13]
    // 0xb82c44: r0 = Instance_MainAxisSize
    //     0xb82c44: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb82c48: ldr             x0, [x0, #0x738]
    // 0xb82c4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb82c4c: stur            w0, [x1, #0x17]
    // 0xb82c50: r0 = Instance_CrossAxisAlignment
    //     0xb82c50: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb82c54: ldr             x0, [x0, #0x740]
    // 0xb82c58: StoreField: r1->field_1b = r0
    //     0xb82c58: stur            w0, [x1, #0x1b]
    // 0xb82c5c: r0 = Instance_VerticalDirection
    //     0xb82c5c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb82c60: ldr             x0, [x0, #0x748]
    // 0xb82c64: StoreField: r1->field_23 = r0
    //     0xb82c64: stur            w0, [x1, #0x23]
    // 0xb82c68: r0 = Instance_Clip
    //     0xb82c68: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb82c6c: ldr             x0, [x0, #0x750]
    // 0xb82c70: StoreField: r1->field_2b = r0
    //     0xb82c70: stur            w0, [x1, #0x2b]
    // 0xb82c74: StoreField: r1->field_2f = rZR
    //     0xb82c74: stur            xzr, [x1, #0x2f]
    // 0xb82c78: ldur            x0, [fp, #-0x18]
    // 0xb82c7c: StoreField: r1->field_b = r0
    //     0xb82c7c: stur            w0, [x1, #0xb]
    // 0xb82c80: r0 = Container()
    //     0xb82c80: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb82c84: stur            x0, [fp, #-0x18]
    // 0xb82c88: r16 = 44.000000
    //     0xb82c88: add             x16, PP, #0x38, lsl #12  ; [pp+0x380c8] 44
    //     0xb82c8c: ldr             x16, [x16, #0xc8]
    // 0xb82c90: r30 = Instance_EdgeInsets
    //     0xb82c90: add             lr, PP, #0x38, lsl #12  ; [pp+0x380d0] Obj!EdgeInsets@e13391
    //     0xb82c94: ldr             lr, [lr, #0xd0]
    // 0xb82c98: stp             lr, x16, [SP, #0x10]
    // 0xb82c9c: ldur            x16, [fp, #-0x20]
    // 0xb82ca0: ldur            lr, [fp, #-0x10]
    // 0xb82ca4: stp             lr, x16, [SP]
    // 0xb82ca8: mov             x1, x0
    // 0xb82cac: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, padding, 0x2, null]
    //     0xb82cac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33e40] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xb82cb0: ldr             x4, [x4, #0xe40]
    // 0xb82cb4: r0 = Container()
    //     0xb82cb4: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb82cb8: r0 = Padding()
    //     0xb82cb8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82cbc: mov             x1, x0
    // 0xb82cc0: r0 = Instance_EdgeInsets
    //     0xb82cc0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xb82cc4: ldr             x0, [x0, #0x768]
    // 0xb82cc8: stur            x1, [fp, #-0x10]
    // 0xb82ccc: StoreField: r1->field_f = r0
    //     0xb82ccc: stur            w0, [x1, #0xf]
    // 0xb82cd0: ldur            x0, [fp, #-0x18]
    // 0xb82cd4: StoreField: r1->field_b = r0
    //     0xb82cd4: stur            w0, [x1, #0xb]
    // 0xb82cd8: r0 = InkWell()
    //     0xb82cd8: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb82cdc: ldur            x1, [fp, #-0x10]
    // 0xb82ce0: StoreField: r0->field_b = r1
    //     0xb82ce0: stur            w1, [x0, #0xb]
    // 0xb82ce4: ldur            x1, [fp, #-8]
    // 0xb82ce8: StoreField: r0->field_f = r1
    //     0xb82ce8: stur            w1, [x0, #0xf]
    // 0xb82cec: r1 = true
    //     0xb82cec: add             x1, NULL, #0x20  ; true
    // 0xb82cf0: StoreField: r0->field_43 = r1
    //     0xb82cf0: stur            w1, [x0, #0x43]
    // 0xb82cf4: r2 = Instance_BoxShape
    //     0xb82cf4: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb82cf8: ldr             x2, [x2, #0xca8]
    // 0xb82cfc: StoreField: r0->field_47 = r2
    //     0xb82cfc: stur            w2, [x0, #0x47]
    // 0xb82d00: r2 = Instance_Color
    //     0xb82d00: ldr             x2, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xb82d04: StoreField: r0->field_5f = r2
    //     0xb82d04: stur            w2, [x0, #0x5f]
    // 0xb82d08: r2 = Instance__NoSplashFactory
    //     0xb82d08: add             x2, PP, #0x35, lsl #12  ; [pp+0x35170] Obj!_NoSplashFactory@e14771
    //     0xb82d0c: ldr             x2, [x2, #0x170]
    // 0xb82d10: StoreField: r0->field_6b = r2
    //     0xb82d10: stur            w2, [x0, #0x6b]
    // 0xb82d14: StoreField: r0->field_6f = r1
    //     0xb82d14: stur            w1, [x0, #0x6f]
    // 0xb82d18: r2 = false
    //     0xb82d18: add             x2, NULL, #0x30  ; false
    // 0xb82d1c: StoreField: r0->field_73 = r2
    //     0xb82d1c: stur            w2, [x0, #0x73]
    // 0xb82d20: StoreField: r0->field_83 = r1
    //     0xb82d20: stur            w1, [x0, #0x83]
    // 0xb82d24: StoreField: r0->field_7b = r2
    //     0xb82d24: stur            w2, [x0, #0x7b]
    // 0xb82d28: LeaveFrame
    //     0xb82d28: mov             SP, fp
    //     0xb82d2c: ldp             fp, lr, [SP], #0x10
    // 0xb82d30: ret
    //     0xb82d30: ret             
    // 0xb82d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb82d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb82d38: b               #0xb8295c
    // 0xb82d3c: SaveReg d0
    //     0xb82d3c: str             q0, [SP, #-0x10]!
    // 0xb82d40: SaveReg r0
    //     0xb82d40: str             x0, [SP, #-8]!
    // 0xb82d44: r0 = AllocateDouble()
    //     0xb82d44: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb82d48: mov             x1, x0
    // 0xb82d4c: RestoreReg r0
    //     0xb82d4c: ldr             x0, [SP], #8
    // 0xb82d50: RestoreReg d0
    //     0xb82d50: ldr             q0, [SP], #0x10
    // 0xb82d54: b               #0xb82b30
  }
}

// class id: 5072, size: 0x34, field offset: 0xc
//   const constructor, 
class NSearchTextField extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb820f8, size: 0x770
    // 0xb820f8: EnterFrame
    //     0xb820f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb820fc: mov             fp, SP
    // 0xb82100: AllocStack(0x98)
    //     0xb82100: sub             SP, SP, #0x98
    // 0xb82104: SetupParameters(NSearchTextField this /* r1 => r1, fp-0x8 */)
    //     0xb82104: stur            x1, [fp, #-8]
    // 0xb82108: CheckStackOverflow
    //     0xb82108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8210c: cmp             SP, x16
    //     0xb82110: b.ls            #0xb82828
    // 0xb82114: r1 = 1
    //     0xb82114: movz            x1, #0x1
    // 0xb82118: r0 = AllocateContext()
    //     0xb82118: bl              #0xec126c  ; AllocateContextStub
    // 0xb8211c: mov             x3, x0
    // 0xb82120: ldur            x0, [fp, #-8]
    // 0xb82124: stur            x3, [fp, #-0x18]
    // 0xb82128: StoreField: r3->field_f = r0
    //     0xb82128: stur            w0, [x3, #0xf]
    // 0xb8212c: LoadField: r1 = r0->field_2b
    //     0xb8212c: ldur            w1, [x0, #0x2b]
    // 0xb82130: DecompressPointer r1
    //     0xb82130: add             x1, x1, HEAP, lsl #32
    // 0xb82134: cmp             w1, NULL
    // 0xb82138: b.ne            #0xb82148
    // 0xb8213c: r4 = Instance_EdgeInsets
    //     0xb8213c: add             x4, PP, #0x29, lsl #12  ; [pp+0x296f0] Obj!EdgeInsets@e12791
    //     0xb82140: ldr             x4, [x4, #0x6f0]
    // 0xb82144: b               #0xb8214c
    // 0xb82148: mov             x4, x1
    // 0xb8214c: stur            x4, [fp, #-0x10]
    // 0xb82150: r1 = _ConstMap len:3
    //     0xb82150: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xb82154: ldr             x1, [x1, #0xbe8]
    // 0xb82158: r2 = 2
    //     0xb82158: movz            x2, #0x2
    // 0xb8215c: r0 = []()
    //     0xb8215c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82160: r16 = <Color?>
    //     0xb82160: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82164: ldr             x16, [x16, #0x98]
    // 0xb82168: stp             x0, x16, [SP, #8]
    // 0xb8216c: r16 = Instance_MaterialColor
    //     0xb8216c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb82170: ldr             x16, [x16, #0xe38]
    // 0xb82174: str             x16, [SP]
    // 0xb82178: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82178: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8217c: r0 = mode()
    //     0xb8217c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82180: stur            x0, [fp, #-0x20]
    // 0xb82184: r0 = Radius()
    //     0xb82184: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb82188: d0 = 8.000000
    //     0xb82188: fmov            d0, #8.00000000
    // 0xb8218c: stur            x0, [fp, #-0x28]
    // 0xb82190: StoreField: r0->field_7 = d0
    //     0xb82190: stur            d0, [x0, #7]
    // 0xb82194: StoreField: r0->field_f = d0
    //     0xb82194: stur            d0, [x0, #0xf]
    // 0xb82198: r0 = BorderRadius()
    //     0xb82198: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8219c: mov             x1, x0
    // 0xb821a0: ldur            x0, [fp, #-0x28]
    // 0xb821a4: stur            x1, [fp, #-0x30]
    // 0xb821a8: StoreField: r1->field_7 = r0
    //     0xb821a8: stur            w0, [x1, #7]
    // 0xb821ac: StoreField: r1->field_b = r0
    //     0xb821ac: stur            w0, [x1, #0xb]
    // 0xb821b0: StoreField: r1->field_f = r0
    //     0xb821b0: stur            w0, [x1, #0xf]
    // 0xb821b4: StoreField: r1->field_13 = r0
    //     0xb821b4: stur            w0, [x1, #0x13]
    // 0xb821b8: r0 = BoxDecoration()
    //     0xb821b8: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb821bc: mov             x3, x0
    // 0xb821c0: ldur            x0, [fp, #-0x20]
    // 0xb821c4: stur            x3, [fp, #-0x28]
    // 0xb821c8: StoreField: r3->field_7 = r0
    //     0xb821c8: stur            w0, [x3, #7]
    // 0xb821cc: ldur            x0, [fp, #-0x30]
    // 0xb821d0: StoreField: r3->field_13 = r0
    //     0xb821d0: stur            w0, [x3, #0x13]
    // 0xb821d4: r0 = Instance_BoxShape
    //     0xb821d4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb821d8: ldr             x0, [x0, #0xca8]
    // 0xb821dc: StoreField: r3->field_23 = r0
    //     0xb821dc: stur            w0, [x3, #0x23]
    // 0xb821e0: r1 = _ConstMap len:6
    //     0xb821e0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb821e4: ldr             x1, [x1, #0xc20]
    // 0xb821e8: r2 = 6
    //     0xb821e8: movz            x2, #0x6
    // 0xb821ec: r0 = []()
    //     0xb821ec: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb821f0: r1 = _ConstMap len:6
    //     0xb821f0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb821f4: ldr             x1, [x1, #0xc20]
    // 0xb821f8: r2 = 8
    //     0xb821f8: movz            x2, #0x8
    // 0xb821fc: stur            x0, [fp, #-0x20]
    // 0xb82200: r0 = []()
    //     0xb82200: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82204: r16 = <Color?>
    //     0xb82204: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82208: ldr             x16, [x16, #0x98]
    // 0xb8220c: stp             x0, x16, [SP, #8]
    // 0xb82210: ldur            x16, [fp, #-0x20]
    // 0xb82214: str             x16, [SP]
    // 0xb82218: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82218: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8221c: r0 = mode()
    //     0xb8221c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82220: stur            x0, [fp, #-0x20]
    // 0xb82224: r0 = Icon()
    //     0xb82224: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb82228: mov             x1, x0
    // 0xb8222c: r0 = Instance_IconData
    //     0xb8222c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2ce48] Obj!IconData@e10231
    //     0xb82230: ldr             x0, [x0, #0xe48]
    // 0xb82234: stur            x1, [fp, #-0x30]
    // 0xb82238: StoreField: r1->field_b = r0
    //     0xb82238: stur            w0, [x1, #0xb]
    // 0xb8223c: r0 = 18.000000
    //     0xb8223c: add             x0, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb82240: ldr             x0, [x0, #0x958]
    // 0xb82244: StoreField: r1->field_f = r0
    //     0xb82244: stur            w0, [x1, #0xf]
    // 0xb82248: ldur            x2, [fp, #-0x20]
    // 0xb8224c: StoreField: r1->field_23 = r2
    //     0xb8224c: stur            w2, [x1, #0x23]
    // 0xb82250: r0 = GestureDetector()
    //     0xb82250: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb82254: stur            x0, [fp, #-0x20]
    // 0xb82258: ldur            x16, [fp, #-0x30]
    // 0xb8225c: str             x16, [SP]
    // 0xb82260: mov             x1, x0
    // 0xb82264: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xb82264: add             x4, PP, #0x33, lsl #12  ; [pp+0x333d0] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xb82268: ldr             x4, [x4, #0x3d0]
    // 0xb8226c: r0 = GestureDetector()
    //     0xb8226c: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb82270: ldur            x0, [fp, #-8]
    // 0xb82274: LoadField: r1 = r0->field_1f
    //     0xb82274: ldur            w1, [x0, #0x1f]
    // 0xb82278: DecompressPointer r1
    //     0xb82278: add             x1, x1, HEAP, lsl #32
    // 0xb8227c: stur            x1, [fp, #-0x50]
    // 0xb82280: LoadField: r2 = r0->field_27
    //     0xb82280: ldur            w2, [x0, #0x27]
    // 0xb82284: DecompressPointer r2
    //     0xb82284: add             x2, x2, HEAP, lsl #32
    // 0xb82288: stur            x2, [fp, #-0x48]
    // 0xb8228c: LoadField: r3 = r0->field_f
    //     0xb8228c: ldur            w3, [x0, #0xf]
    // 0xb82290: DecompressPointer r3
    //     0xb82290: add             x3, x3, HEAP, lsl #32
    // 0xb82294: stur            x3, [fp, #-0x40]
    // 0xb82298: LoadField: r4 = r0->field_13
    //     0xb82298: ldur            w4, [x0, #0x13]
    // 0xb8229c: DecompressPointer r4
    //     0xb8229c: add             x4, x4, HEAP, lsl #32
    // 0xb822a0: stur            x4, [fp, #-0x38]
    // 0xb822a4: LoadField: r5 = r0->field_1b
    //     0xb822a4: ldur            w5, [x0, #0x1b]
    // 0xb822a8: DecompressPointer r5
    //     0xb822a8: add             x5, x5, HEAP, lsl #32
    // 0xb822ac: stur            x5, [fp, #-0x30]
    // 0xb822b0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb822b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb822b4: ldr             x0, [x0, #0x2670]
    //     0xb822b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb822bc: cmp             w0, w16
    //     0xb822c0: b.ne            #0xb822cc
    //     0xb822c4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb822c8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb822cc: r0 = GetNavigation.textTheme()
    //     0xb822cc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb822d0: LoadField: r1 = r0->field_2f
    //     0xb822d0: ldur            w1, [x0, #0x2f]
    // 0xb822d4: DecompressPointer r1
    //     0xb822d4: add             x1, x1, HEAP, lsl #32
    // 0xb822d8: stur            x1, [fp, #-0x58]
    // 0xb822dc: cmp             w1, NULL
    // 0xb822e0: b.ne            #0xb822ec
    // 0xb822e4: r1 = Null
    //     0xb822e4: mov             x1, NULL
    // 0xb822e8: b               #0xb82384
    // 0xb822ec: r0 = GetNavigation.textScaleFactor()
    //     0xb822ec: bl              #0xb82868  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textScaleFactor
    // 0xb822f0: mov             v1.16b, v0.16b
    // 0xb822f4: d0 = 16.000000
    //     0xb822f4: fmov            d0, #16.00000000
    // 0xb822f8: fdiv            d2, d0, d1
    // 0xb822fc: stur            d2, [fp, #-0x78]
    // 0xb82300: r1 = _ConstMap len:6
    //     0xb82300: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82304: ldr             x1, [x1, #0xc20]
    // 0xb82308: r2 = 6
    //     0xb82308: movz            x2, #0x6
    // 0xb8230c: r0 = []()
    //     0xb8230c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82310: r1 = _ConstMap len:6
    //     0xb82310: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82314: ldr             x1, [x1, #0xc20]
    // 0xb82318: r2 = 8
    //     0xb82318: movz            x2, #0x8
    // 0xb8231c: stur            x0, [fp, #-0x60]
    // 0xb82320: r0 = []()
    //     0xb82320: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82324: r16 = <Color?>
    //     0xb82324: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82328: ldr             x16, [x16, #0x98]
    // 0xb8232c: stp             x0, x16, [SP, #8]
    // 0xb82330: ldur            x16, [fp, #-0x60]
    // 0xb82334: str             x16, [SP]
    // 0xb82338: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82338: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8233c: r0 = mode()
    //     0xb8233c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82340: ldur            d0, [fp, #-0x78]
    // 0xb82344: r1 = inline_Allocate_Double()
    //     0xb82344: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb82348: add             x1, x1, #0x10
    //     0xb8234c: cmp             x2, x1
    //     0xb82350: b.ls            #0xb82830
    //     0xb82354: str             x1, [THR, #0x50]  ; THR::top
    //     0xb82358: sub             x1, x1, #0xf
    //     0xb8235c: movz            x2, #0xe15c
    //     0xb82360: movk            x2, #0x3, lsl #16
    //     0xb82364: stur            x2, [x1, #-1]
    // 0xb82368: StoreField: r1->field_7 = d0
    //     0xb82368: stur            d0, [x1, #7]
    // 0xb8236c: stp             x0, x1, [SP]
    // 0xb82370: ldur            x1, [fp, #-0x58]
    // 0xb82374: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb82374: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb82378: ldr             x4, [x4, #0xaa0]
    // 0xb8237c: r0 = copyWith()
    //     0xb8237c: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb82380: mov             x1, x0
    // 0xb82384: ldur            x0, [fp, #-8]
    // 0xb82388: stur            x1, [fp, #-0x60]
    // 0xb8238c: LoadField: r2 = r0->field_23
    //     0xb8238c: ldur            w2, [x0, #0x23]
    // 0xb82390: DecompressPointer r2
    //     0xb82390: add             x2, x2, HEAP, lsl #32
    // 0xb82394: stur            x2, [fp, #-0x58]
    // 0xb82398: r0 = InputDecoration()
    //     0xb82398: bl              #0x9876d4  ; AllocateInputDecorationStub -> InputDecoration (size=0xe0)
    // 0xb8239c: mov             x1, x0
    // 0xb823a0: ldur            x0, [fp, #-0x58]
    // 0xb823a4: stur            x1, [fp, #-0x68]
    // 0xb823a8: StoreField: r1->field_2f = r0
    //     0xb823a8: stur            w0, [x1, #0x2f]
    // 0xb823ac: ldur            x0, [fp, #-0x60]
    // 0xb823b0: StoreField: r1->field_33 = r0
    //     0xb823b0: stur            w0, [x1, #0x33]
    // 0xb823b4: r0 = true
    //     0xb823b4: add             x0, NULL, #0x20  ; true
    // 0xb823b8: StoreField: r1->field_43 = r0
    //     0xb823b8: stur            w0, [x1, #0x43]
    // 0xb823bc: StoreField: r1->field_5f = r0
    //     0xb823bc: stur            w0, [x1, #0x5f]
    // 0xb823c0: r2 = Instance__NoInputBorder
    //     0xb823c0: add             x2, PP, #0x38, lsl #12  ; [pp+0x380a8] Obj!_NoInputBorder@e14761
    //     0xb823c4: ldr             x2, [x2, #0xa8]
    // 0xb823c8: StoreField: r1->field_cb = r2
    //     0xb823c8: stur            w2, [x1, #0xcb]
    // 0xb823cc: StoreField: r1->field_cf = r0
    //     0xb823cc: stur            w0, [x1, #0xcf]
    // 0xb823d0: r0 = GetNavigation.textTheme()
    //     0xb823d0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb823d4: LoadField: r1 = r0->field_2f
    //     0xb823d4: ldur            w1, [x0, #0x2f]
    // 0xb823d8: DecompressPointer r1
    //     0xb823d8: add             x1, x1, HEAP, lsl #32
    // 0xb823dc: stur            x1, [fp, #-0x58]
    // 0xb823e0: cmp             w1, NULL
    // 0xb823e4: b.ne            #0xb823f0
    // 0xb823e8: r8 = Null
    //     0xb823e8: mov             x8, NULL
    // 0xb823ec: b               #0xb82450
    // 0xb823f0: ldur            x0, [fp, #-8]
    // 0xb823f4: r0 = GetNavigation.textScaleFactor()
    //     0xb823f4: bl              #0xb82868  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textScaleFactor
    // 0xb823f8: mov             v1.16b, v0.16b
    // 0xb823fc: d0 = 16.000000
    //     0xb823fc: fmov            d0, #16.00000000
    // 0xb82400: fdiv            d2, d0, d1
    // 0xb82404: ldur            x0, [fp, #-8]
    // 0xb82408: LoadField: r1 = r0->field_2f
    //     0xb82408: ldur            w1, [x0, #0x2f]
    // 0xb8240c: DecompressPointer r1
    //     0xb8240c: add             x1, x1, HEAP, lsl #32
    // 0xb82410: r2 = inline_Allocate_Double()
    //     0xb82410: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb82414: add             x2, x2, #0x10
    //     0xb82418: cmp             x3, x2
    //     0xb8241c: b.ls            #0xb8284c
    //     0xb82420: str             x2, [THR, #0x50]  ; THR::top
    //     0xb82424: sub             x2, x2, #0xf
    //     0xb82428: movz            x3, #0xe15c
    //     0xb8242c: movk            x3, #0x3, lsl #16
    //     0xb82430: stur            x3, [x2, #-1]
    // 0xb82434: StoreField: r2->field_7 = d2
    //     0xb82434: stur            d2, [x2, #7]
    // 0xb82438: stp             x1, x2, [SP]
    // 0xb8243c: ldur            x1, [fp, #-0x58]
    // 0xb82440: r4 = const [0, 0x3, 0x2, 0x1, fontFamily, 0x2, fontSize, 0x1, null]
    //     0xb82440: add             x4, PP, #0x2b, lsl #12  ; [pp+0x2b608] List(9) [0, 0x3, 0x2, 0x1, "fontFamily", 0x2, "fontSize", 0x1, Null]
    //     0xb82444: ldr             x4, [x4, #0x608]
    // 0xb82448: r0 = copyWith()
    //     0xb82448: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8244c: mov             x8, x0
    // 0xb82450: ldur            x0, [fp, #-8]
    // 0xb82454: ldur            x7, [fp, #-0x20]
    // 0xb82458: ldur            x2, [fp, #-0x50]
    // 0xb8245c: ldur            x3, [fp, #-0x48]
    // 0xb82460: ldur            x4, [fp, #-0x40]
    // 0xb82464: ldur            x5, [fp, #-0x38]
    // 0xb82468: ldur            x6, [fp, #-0x30]
    // 0xb8246c: ldur            x1, [fp, #-0x68]
    // 0xb82470: stur            x8, [fp, #-0x58]
    // 0xb82474: r0 = TextField()
    //     0xb82474: bl              #0xa3e024  ; AllocateTextFieldStub -> TextField (size=0x11c)
    // 0xb82478: mov             x1, x0
    // 0xb8247c: r0 = EditableText
    //     0xb8247c: ldr             x0, [PP, #0x6c48]  ; [pp+0x6c48] Type: EditableText
    // 0xb82480: stur            x1, [fp, #-0x60]
    // 0xb82484: StoreField: r1->field_f = r0
    //     0xb82484: stur            w0, [x1, #0xf]
    // 0xb82488: ldur            x0, [fp, #-0x40]
    // 0xb8248c: StoreField: r1->field_13 = r0
    //     0xb8248c: stur            w0, [x1, #0x13]
    // 0xb82490: ldur            x0, [fp, #-0x68]
    // 0xb82494: StoreField: r1->field_1b = r0
    //     0xb82494: stur            w0, [x1, #0x1b]
    // 0xb82498: r0 = Instance_TextInputAction
    //     0xb82498: ldr             x0, [PP, #0x7760]  ; [pp+0x7760] Obj!TextInputAction@e34c21
    // 0xb8249c: StoreField: r1->field_23 = r0
    //     0xb8249c: stur            w0, [x1, #0x23]
    // 0xb824a0: r0 = Instance_TextCapitalization
    //     0xb824a0: ldr             x0, [PP, #0x7210]  ; [pp+0x7210] Obj!TextCapitalization@e34ae1
    // 0xb824a4: StoreField: r1->field_27 = r0
    //     0xb824a4: stur            w0, [x1, #0x27]
    // 0xb824a8: ldur            x0, [fp, #-0x58]
    // 0xb824ac: StoreField: r1->field_2b = r0
    //     0xb824ac: stur            w0, [x1, #0x2b]
    // 0xb824b0: r0 = Instance_TextAlign
    //     0xb824b0: ldr             x0, [PP, #0x4930]  ; [pp+0x4930] Obj!TextAlign@e394a1
    // 0xb824b4: StoreField: r1->field_33 = r0
    //     0xb824b4: stur            w0, [x1, #0x33]
    // 0xb824b8: r0 = false
    //     0xb824b8: add             x0, NULL, #0x30  ; false
    // 0xb824bc: StoreField: r1->field_6f = r0
    //     0xb824bc: stur            w0, [x1, #0x6f]
    // 0xb824c0: ldur            x2, [fp, #-0x48]
    // 0xb824c4: StoreField: r1->field_3f = r2
    //     0xb824c4: stur            w2, [x1, #0x3f]
    // 0xb824c8: r2 = "•"
    //     0xb824c8: add             x2, PP, #0x27, lsl #12  ; [pp+0x274c8] "•"
    //     0xb824cc: ldr             x2, [x2, #0x4c8]
    // 0xb824d0: StoreField: r1->field_47 = r2
    //     0xb824d0: stur            w2, [x1, #0x47]
    // 0xb824d4: StoreField: r1->field_4b = r0
    //     0xb824d4: stur            w0, [x1, #0x4b]
    // 0xb824d8: StoreField: r1->field_4f = r0
    //     0xb824d8: stur            w0, [x1, #0x4f]
    // 0xb824dc: r2 = true
    //     0xb824dc: add             x2, NULL, #0x20  ; true
    // 0xb824e0: StoreField: r1->field_5b = r2
    //     0xb824e0: stur            w2, [x1, #0x5b]
    // 0xb824e4: r3 = 1
    //     0xb824e4: movz            x3, #0x1
    // 0xb824e8: StoreField: r1->field_5f = r3
    //     0xb824e8: stur            x3, [x1, #0x5f]
    // 0xb824ec: StoreField: r1->field_6b = r0
    //     0xb824ec: stur            w0, [x1, #0x6b]
    // 0xb824f0: ldur            x4, [fp, #-0x38]
    // 0xb824f4: StoreField: r1->field_83 = r4
    //     0xb824f4: stur            w4, [x1, #0x83]
    // 0xb824f8: ldur            x4, [fp, #-0x30]
    // 0xb824fc: StoreField: r1->field_8b = r4
    //     0xb824fc: stur            w4, [x1, #0x8b]
    // 0xb82500: d0 = 2.000000
    //     0xb82500: fmov            d0, #2.00000000
    // 0xb82504: StoreField: r1->field_9f = d0
    //     0xb82504: stur            d0, [x1, #0x9f]
    // 0xb82508: r4 = Instance_BoxHeightStyle
    //     0xb82508: ldr             x4, [PP, #0x4a00]  ; [pp+0x4a00] Obj!BoxHeightStyle@e39241
    // 0xb8250c: StoreField: r1->field_bb = r4
    //     0xb8250c: stur            w4, [x1, #0xbb]
    // 0xb82510: r4 = Instance_BoxWidthStyle
    //     0xb82510: ldr             x4, [PP, #0x4a78]  ; [pp+0x4a78] Obj!BoxWidthStyle@e39221
    // 0xb82514: StoreField: r1->field_bf = r4
    //     0xb82514: stur            w4, [x1, #0xbf]
    // 0xb82518: r4 = Instance_EdgeInsets
    //     0xb82518: ldr             x4, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xb8251c: StoreField: r1->field_c7 = r4
    //     0xb8251c: stur            w4, [x1, #0xc7]
    // 0xb82520: r4 = Instance_DragStartBehavior
    //     0xb82520: ldr             x4, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb82524: StoreField: r1->field_d3 = r4
    //     0xb82524: stur            w4, [x1, #0xd3]
    // 0xb82528: ldur            x4, [fp, #-0x50]
    // 0xb8252c: StoreField: r1->field_d7 = r4
    //     0xb8252c: stur            w4, [x1, #0xd7]
    // 0xb82530: StoreField: r1->field_db = r0
    //     0xb82530: stur            w0, [x1, #0xdb]
    // 0xb82534: r0 = const []
    //     0xb82534: ldr             x0, [PP, #0x7218]  ; [pp+0x7218] List<String>(0)
    // 0xb82538: StoreField: r1->field_f3 = r0
    //     0xb82538: stur            w0, [x1, #0xf3]
    // 0xb8253c: r0 = Instance_Clip
    //     0xb8253c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb82540: ldr             x0, [x0, #0x7c0]
    // 0xb82544: StoreField: r1->field_f7 = r0
    //     0xb82544: stur            w0, [x1, #0xf7]
    // 0xb82548: StoreField: r1->field_ff = r2
    //     0xb82548: stur            w2, [x1, #0xff]
    // 0xb8254c: r17 = 259
    //     0xb8254c: movz            x17, #0x103
    // 0xb82550: str             w2, [x1, x17]
    // 0xb82554: r0 = Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static.
    //     0xb82554: add             x0, PP, #0x27, lsl #12  ; [pp+0x274d8] Closure: (BuildContext, EditableTextState) => Widget from Function '_defaultContextMenuBuilder@610181401': static. (0x7e54fb43e0ec)
    //     0xb82558: ldr             x0, [x0, #0x4d8]
    // 0xb8255c: r17 = 267
    //     0xb8255c: movz            x17, #0x10b
    // 0xb82560: str             w0, [x1, x17]
    // 0xb82564: r17 = 271
    //     0xb82564: movz            x17, #0x10f
    // 0xb82568: str             w2, [x1, x17]
    // 0xb8256c: r0 = Instance_SmartDashesType
    //     0xb8256c: ldr             x0, [PP, #0x7220]  ; [pp+0x7220] Obj!SmartDashesType@e34cc1
    // 0xb82570: StoreField: r1->field_53 = r0
    //     0xb82570: stur            w0, [x1, #0x53]
    // 0xb82574: r0 = Instance_SmartQuotesType
    //     0xb82574: add             x0, PP, #0x27, lsl #12  ; [pp+0x274e0] Obj!SmartQuotesType@e34ca1
    //     0xb82578: ldr             x0, [x0, #0x4e0]
    // 0xb8257c: StoreField: r1->field_57 = r0
    //     0xb8257c: stur            w0, [x1, #0x57]
    // 0xb82580: r0 = Instance_TextInputType
    //     0xb82580: add             x0, PP, #0x27, lsl #12  ; [pp+0x274e8] Obj!TextInputType@e10db1
    //     0xb82584: ldr             x0, [x0, #0x4e8]
    // 0xb82588: StoreField: r1->field_1f = r0
    //     0xb82588: stur            w0, [x1, #0x1f]
    // 0xb8258c: StoreField: r1->field_cb = r2
    //     0xb8258c: stur            w2, [x1, #0xcb]
    // 0xb82590: r0 = Padding()
    //     0xb82590: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb82594: mov             x2, x0
    // 0xb82598: r0 = Instance_EdgeInsets
    //     0xb82598: add             x0, PP, #0x38, lsl #12  ; [pp+0x380b0] Obj!EdgeInsets@e13361
    //     0xb8259c: ldr             x0, [x0, #0xb0]
    // 0xb825a0: stur            x2, [fp, #-0x30]
    // 0xb825a4: StoreField: r2->field_f = r0
    //     0xb825a4: stur            w0, [x2, #0xf]
    // 0xb825a8: ldur            x0, [fp, #-0x60]
    // 0xb825ac: StoreField: r2->field_b = r0
    //     0xb825ac: stur            w0, [x2, #0xb]
    // 0xb825b0: r1 = <FlexParentData>
    //     0xb825b0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb825b4: ldr             x1, [x1, #0x720]
    // 0xb825b8: r0 = Expanded()
    //     0xb825b8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb825bc: mov             x3, x0
    // 0xb825c0: r0 = 1
    //     0xb825c0: movz            x0, #0x1
    // 0xb825c4: stur            x3, [fp, #-0x38]
    // 0xb825c8: StoreField: r3->field_13 = r0
    //     0xb825c8: stur            x0, [x3, #0x13]
    // 0xb825cc: r0 = Instance_FlexFit
    //     0xb825cc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb825d0: ldr             x0, [x0, #0x728]
    // 0xb825d4: StoreField: r3->field_1b = r0
    //     0xb825d4: stur            w0, [x3, #0x1b]
    // 0xb825d8: ldur            x0, [fp, #-0x30]
    // 0xb825dc: StoreField: r3->field_b = r0
    //     0xb825dc: stur            w0, [x3, #0xb]
    // 0xb825e0: r1 = Null
    //     0xb825e0: mov             x1, NULL
    // 0xb825e4: r2 = 8
    //     0xb825e4: movz            x2, #0x8
    // 0xb825e8: r0 = AllocateArray()
    //     0xb825e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb825ec: mov             x2, x0
    // 0xb825f0: ldur            x0, [fp, #-0x20]
    // 0xb825f4: stur            x2, [fp, #-0x30]
    // 0xb825f8: StoreField: r2->field_f = r0
    //     0xb825f8: stur            w0, [x2, #0xf]
    // 0xb825fc: r16 = Instance_SizedBox
    //     0xb825fc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xb82600: ldr             x16, [x16, #0x340]
    // 0xb82604: StoreField: r2->field_13 = r16
    //     0xb82604: stur            w16, [x2, #0x13]
    // 0xb82608: ldur            x0, [fp, #-0x38]
    // 0xb8260c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8260c: stur            w0, [x2, #0x17]
    // 0xb82610: r16 = Instance_SizedBox
    //     0xb82610: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xb82614: ldr             x16, [x16, #0x538]
    // 0xb82618: StoreField: r2->field_1b = r16
    //     0xb82618: stur            w16, [x2, #0x1b]
    // 0xb8261c: r1 = <Widget>
    //     0xb8261c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb82620: r0 = AllocateGrowableArray()
    //     0xb82620: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb82624: mov             x3, x0
    // 0xb82628: ldur            x0, [fp, #-0x30]
    // 0xb8262c: stur            x3, [fp, #-0x20]
    // 0xb82630: StoreField: r3->field_f = r0
    //     0xb82630: stur            w0, [x3, #0xf]
    // 0xb82634: r0 = 8
    //     0xb82634: movz            x0, #0x8
    // 0xb82638: StoreField: r3->field_b = r0
    //     0xb82638: stur            w0, [x3, #0xb]
    // 0xb8263c: ldur            x1, [fp, #-8]
    // 0xb82640: LoadField: r2 = r1->field_b
    //     0xb82640: ldur            w2, [x1, #0xb]
    // 0xb82644: DecompressPointer r2
    //     0xb82644: add             x2, x2, HEAP, lsl #32
    // 0xb82648: LoadField: r1 = r2->field_7
    //     0xb82648: ldur            w1, [x2, #7]
    // 0xb8264c: cbz             w1, #0xb8276c
    // 0xb82650: r1 = _ConstMap len:6
    //     0xb82650: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82654: ldr             x1, [x1, #0xc20]
    // 0xb82658: r2 = 6
    //     0xb82658: movz            x2, #0x6
    // 0xb8265c: r0 = []()
    //     0xb8265c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82660: r1 = _ConstMap len:6
    //     0xb82660: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb82664: ldr             x1, [x1, #0xc20]
    // 0xb82668: r2 = 8
    //     0xb82668: movz            x2, #0x8
    // 0xb8266c: stur            x0, [fp, #-8]
    // 0xb82670: r0 = []()
    //     0xb82670: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb82674: r16 = <Color?>
    //     0xb82674: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb82678: ldr             x16, [x16, #0x98]
    // 0xb8267c: stp             x0, x16, [SP, #8]
    // 0xb82680: ldur            x16, [fp, #-8]
    // 0xb82684: str             x16, [SP]
    // 0xb82688: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb82688: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8268c: r0 = mode()
    //     0xb8268c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb82690: stur            x0, [fp, #-8]
    // 0xb82694: r0 = Icon()
    //     0xb82694: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb82698: mov             x1, x0
    // 0xb8269c: r0 = Instance_IconData
    //     0xb8269c: add             x0, PP, #0x38, lsl #12  ; [pp+0x380b8] Obj!IconData@e10891
    //     0xb826a0: ldr             x0, [x0, #0xb8]
    // 0xb826a4: stur            x1, [fp, #-0x30]
    // 0xb826a8: StoreField: r1->field_b = r0
    //     0xb826a8: stur            w0, [x1, #0xb]
    // 0xb826ac: r0 = 18.000000
    //     0xb826ac: add             x0, PP, #0xb, lsl #12  ; [pp+0xb958] 18
    //     0xb826b0: ldr             x0, [x0, #0x958]
    // 0xb826b4: StoreField: r1->field_f = r0
    //     0xb826b4: stur            w0, [x1, #0xf]
    // 0xb826b8: ldur            x0, [fp, #-8]
    // 0xb826bc: StoreField: r1->field_23 = r0
    //     0xb826bc: stur            w0, [x1, #0x23]
    // 0xb826c0: r0 = GestureDetector()
    //     0xb826c0: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb826c4: ldur            x2, [fp, #-0x18]
    // 0xb826c8: r1 = Function '<anonymous closure>':.
    //     0xb826c8: add             x1, PP, #0x38, lsl #12  ; [pp+0x380c0] AnonymousClosure: (0xb828b8), in [package:nuikit/src/widgets/text_field/search_text_field.dart] NSearchTextField::build (0xb820f8)
    //     0xb826cc: ldr             x1, [x1, #0xc0]
    // 0xb826d0: stur            x0, [fp, #-8]
    // 0xb826d4: r0 = AllocateClosure()
    //     0xb826d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb826d8: ldur            x16, [fp, #-0x30]
    // 0xb826dc: stp             x16, x0, [SP]
    // 0xb826e0: ldur            x1, [fp, #-8]
    // 0xb826e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb826e4: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb826e8: ldr             x4, [x4, #0x7d0]
    // 0xb826ec: r0 = GestureDetector()
    //     0xb826ec: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb826f0: ldur            x0, [fp, #-0x20]
    // 0xb826f4: LoadField: r1 = r0->field_b
    //     0xb826f4: ldur            w1, [x0, #0xb]
    // 0xb826f8: LoadField: r2 = r0->field_f
    //     0xb826f8: ldur            w2, [x0, #0xf]
    // 0xb826fc: DecompressPointer r2
    //     0xb826fc: add             x2, x2, HEAP, lsl #32
    // 0xb82700: LoadField: r3 = r2->field_b
    //     0xb82700: ldur            w3, [x2, #0xb]
    // 0xb82704: r2 = LoadInt32Instr(r1)
    //     0xb82704: sbfx            x2, x1, #1, #0x1f
    // 0xb82708: stur            x2, [fp, #-0x70]
    // 0xb8270c: r1 = LoadInt32Instr(r3)
    //     0xb8270c: sbfx            x1, x3, #1, #0x1f
    // 0xb82710: cmp             x2, x1
    // 0xb82714: b.ne            #0xb82720
    // 0xb82718: mov             x1, x0
    // 0xb8271c: r0 = _growToNextCapacity()
    //     0xb8271c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb82720: ldur            x2, [fp, #-0x20]
    // 0xb82724: ldur            x3, [fp, #-0x70]
    // 0xb82728: add             x0, x3, #1
    // 0xb8272c: lsl             x1, x0, #1
    // 0xb82730: StoreField: r2->field_b = r1
    //     0xb82730: stur            w1, [x2, #0xb]
    // 0xb82734: LoadField: r1 = r2->field_f
    //     0xb82734: ldur            w1, [x2, #0xf]
    // 0xb82738: DecompressPointer r1
    //     0xb82738: add             x1, x1, HEAP, lsl #32
    // 0xb8273c: ldur            x0, [fp, #-8]
    // 0xb82740: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb82740: add             x25, x1, x3, lsl #2
    //     0xb82744: add             x25, x25, #0xf
    //     0xb82748: str             w0, [x25]
    //     0xb8274c: tbz             w0, #0, #0xb82768
    //     0xb82750: ldurb           w16, [x1, #-1]
    //     0xb82754: ldurb           w17, [x0, #-1]
    //     0xb82758: and             x16, x17, x16, lsr #2
    //     0xb8275c: tst             x16, HEAP, lsr #32
    //     0xb82760: b.eq            #0xb82768
    //     0xb82764: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb82768: b               #0xb82770
    // 0xb8276c: mov             x2, x3
    // 0xb82770: ldur            x0, [fp, #-0x10]
    // 0xb82774: r0 = Row()
    //     0xb82774: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb82778: mov             x1, x0
    // 0xb8277c: r0 = Instance_Axis
    //     0xb8277c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb82780: stur            x1, [fp, #-8]
    // 0xb82784: StoreField: r1->field_f = r0
    //     0xb82784: stur            w0, [x1, #0xf]
    // 0xb82788: r0 = Instance_MainAxisAlignment
    //     0xb82788: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xb8278c: ldr             x0, [x0, #0xae8]
    // 0xb82790: StoreField: r1->field_13 = r0
    //     0xb82790: stur            w0, [x1, #0x13]
    // 0xb82794: r0 = Instance_MainAxisSize
    //     0xb82794: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb82798: ldr             x0, [x0, #0x738]
    // 0xb8279c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8279c: stur            w0, [x1, #0x17]
    // 0xb827a0: r0 = Instance_CrossAxisAlignment
    //     0xb827a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb827a4: ldr             x0, [x0, #0x740]
    // 0xb827a8: StoreField: r1->field_1b = r0
    //     0xb827a8: stur            w0, [x1, #0x1b]
    // 0xb827ac: r0 = Instance_VerticalDirection
    //     0xb827ac: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb827b0: ldr             x0, [x0, #0x748]
    // 0xb827b4: StoreField: r1->field_23 = r0
    //     0xb827b4: stur            w0, [x1, #0x23]
    // 0xb827b8: r0 = Instance_Clip
    //     0xb827b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb827bc: ldr             x0, [x0, #0x750]
    // 0xb827c0: StoreField: r1->field_2b = r0
    //     0xb827c0: stur            w0, [x1, #0x2b]
    // 0xb827c4: StoreField: r1->field_2f = rZR
    //     0xb827c4: stur            xzr, [x1, #0x2f]
    // 0xb827c8: ldur            x0, [fp, #-0x20]
    // 0xb827cc: StoreField: r1->field_b = r0
    //     0xb827cc: stur            w0, [x1, #0xb]
    // 0xb827d0: r0 = Container()
    //     0xb827d0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb827d4: stur            x0, [fp, #-0x18]
    // 0xb827d8: r16 = 44.000000
    //     0xb827d8: add             x16, PP, #0x38, lsl #12  ; [pp+0x380c8] 44
    //     0xb827dc: ldr             x16, [x16, #0xc8]
    // 0xb827e0: r30 = Instance_EdgeInsets
    //     0xb827e0: add             lr, PP, #0x31, lsl #12  ; [pp+0x31b00] Obj!EdgeInsets@e131e1
    //     0xb827e4: ldr             lr, [lr, #0xb00]
    // 0xb827e8: stp             lr, x16, [SP, #0x10]
    // 0xb827ec: ldur            x16, [fp, #-0x28]
    // 0xb827f0: ldur            lr, [fp, #-8]
    // 0xb827f4: stp             lr, x16, [SP]
    // 0xb827f8: mov             x1, x0
    // 0xb827fc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, padding, 0x2, null]
    //     0xb827fc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33e40] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xb82800: ldr             x4, [x4, #0xe40]
    // 0xb82804: r0 = Container()
    //     0xb82804: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb82808: r0 = Padding()
    //     0xb82808: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8280c: ldur            x1, [fp, #-0x10]
    // 0xb82810: StoreField: r0->field_f = r1
    //     0xb82810: stur            w1, [x0, #0xf]
    // 0xb82814: ldur            x1, [fp, #-0x18]
    // 0xb82818: StoreField: r0->field_b = r1
    //     0xb82818: stur            w1, [x0, #0xb]
    // 0xb8281c: LeaveFrame
    //     0xb8281c: mov             SP, fp
    //     0xb82820: ldp             fp, lr, [SP], #0x10
    // 0xb82824: ret
    //     0xb82824: ret             
    // 0xb82828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb82828: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8282c: b               #0xb82114
    // 0xb82830: SaveReg d0
    //     0xb82830: str             q0, [SP, #-0x10]!
    // 0xb82834: SaveReg r0
    //     0xb82834: str             x0, [SP, #-8]!
    // 0xb82838: r0 = AllocateDouble()
    //     0xb82838: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb8283c: mov             x1, x0
    // 0xb82840: RestoreReg r0
    //     0xb82840: ldr             x0, [SP], #8
    // 0xb82844: RestoreReg d0
    //     0xb82844: ldr             q0, [SP], #0x10
    // 0xb82848: b               #0xb82368
    // 0xb8284c: SaveReg d2
    //     0xb8284c: str             q2, [SP, #-0x10]!
    // 0xb82850: stp             x0, x1, [SP, #-0x10]!
    // 0xb82854: r0 = AllocateDouble()
    //     0xb82854: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb82858: mov             x2, x0
    // 0xb8285c: ldp             x0, x1, [SP], #0x10
    // 0xb82860: RestoreReg d2
    //     0xb82860: ldr             q2, [SP], #0x10
    // 0xb82864: b               #0xb82434
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb828b8, size: 0x84
    // 0xb828b8: EnterFrame
    //     0xb828b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb828bc: mov             fp, SP
    // 0xb828c0: AllocStack(0x10)
    //     0xb828c0: sub             SP, SP, #0x10
    // 0xb828c4: SetupParameters()
    //     0xb828c4: ldr             x0, [fp, #0x10]
    //     0xb828c8: ldur            w3, [x0, #0x17]
    //     0xb828cc: add             x3, x3, HEAP, lsl #32
    //     0xb828d0: stur            x3, [fp, #-8]
    // 0xb828d4: CheckStackOverflow
    //     0xb828d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb828d8: cmp             SP, x16
    //     0xb828dc: b.ls            #0xb82934
    // 0xb828e0: LoadField: r0 = r3->field_f
    //     0xb828e0: ldur            w0, [x3, #0xf]
    // 0xb828e4: DecompressPointer r0
    //     0xb828e4: add             x0, x0, HEAP, lsl #32
    // 0xb828e8: LoadField: r1 = r0->field_f
    //     0xb828e8: ldur            w1, [x0, #0xf]
    // 0xb828ec: DecompressPointer r1
    //     0xb828ec: add             x1, x1, HEAP, lsl #32
    // 0xb828f0: r2 = ""
    //     0xb828f0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb828f4: r0 = text=()
    //     0xb828f4: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xb828f8: ldur            x0, [fp, #-8]
    // 0xb828fc: LoadField: r1 = r0->field_f
    //     0xb828fc: ldur            w1, [x0, #0xf]
    // 0xb82900: DecompressPointer r1
    //     0xb82900: add             x1, x1, HEAP, lsl #32
    // 0xb82904: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb82904: ldur            w0, [x1, #0x17]
    // 0xb82908: DecompressPointer r0
    //     0xb82908: add             x0, x0, HEAP, lsl #32
    // 0xb8290c: cmp             w0, NULL
    // 0xb82910: b.eq            #0xb82924
    // 0xb82914: str             x0, [SP]
    // 0xb82918: ClosureCall
    //     0xb82918: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xb8291c: ldur            x2, [x0, #0x1f]
    //     0xb82920: blr             x2
    // 0xb82924: r0 = Null
    //     0xb82924: mov             x0, NULL
    // 0xb82928: LeaveFrame
    //     0xb82928: mov             SP, fp
    //     0xb8292c: ldp             fp, lr, [SP], #0x10
    // 0xb82930: ret
    //     0xb82930: ret             
    // 0xb82934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb82934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb82938: b               #0xb828e0
  }
}
