// lib: , url: package:nuonline/app/data/enums/calendar_enum.dart

// class id: 1049994, size: 0x8
class :: {
}

// class id: 6849, size: 0x14, field offset: 0x14
enum EventCategory extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cd94, size: 0x64
    // 0xc4cd94: EnterFrame
    //     0xc4cd94: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cd98: mov             fp, SP
    // 0xc4cd9c: AllocStack(0x10)
    //     0xc4cd9c: sub             SP, SP, #0x10
    // 0xc4cda0: SetupParameters(EventCategory this /* r1 => r0, fp-0x8 */)
    //     0xc4cda0: mov             x0, x1
    //     0xc4cda4: stur            x1, [fp, #-8]
    // 0xc4cda8: CheckStackOverflow
    //     0xc4cda8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4cdac: cmp             SP, x16
    //     0xc4cdb0: b.ls            #0xc4cdf0
    // 0xc4cdb4: r1 = Null
    //     0xc4cdb4: mov             x1, NULL
    // 0xc4cdb8: r2 = 4
    //     0xc4cdb8: movz            x2, #0x4
    // 0xc4cdbc: r0 = AllocateArray()
    //     0xc4cdbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4cdc0: r16 = "EventCategory."
    //     0xc4cdc0: add             x16, PP, #0x21, lsl #12  ; [pp+0x219b8] "EventCategory."
    //     0xc4cdc4: ldr             x16, [x16, #0x9b8]
    // 0xc4cdc8: StoreField: r0->field_f = r16
    //     0xc4cdc8: stur            w16, [x0, #0xf]
    // 0xc4cdcc: ldur            x1, [fp, #-8]
    // 0xc4cdd0: LoadField: r2 = r1->field_f
    //     0xc4cdd0: ldur            w2, [x1, #0xf]
    // 0xc4cdd4: DecompressPointer r2
    //     0xc4cdd4: add             x2, x2, HEAP, lsl #32
    // 0xc4cdd8: StoreField: r0->field_13 = r2
    //     0xc4cdd8: stur            w2, [x0, #0x13]
    // 0xc4cddc: str             x0, [SP]
    // 0xc4cde0: r0 = _interpolate()
    //     0xc4cde0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4cde4: LeaveFrame
    //     0xc4cde4: mov             SP, fp
    //     0xc4cde8: ldp             fp, lr, [SP], #0x10
    // 0xc4cdec: ret
    //     0xc4cdec: ret             
    // 0xc4cdf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4cdf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cdf4: b               #0xc4cdb4
  }
}
