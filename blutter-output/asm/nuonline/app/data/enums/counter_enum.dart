// lib: , url: package:nuonline/app/data/enums/counter_enum.dart

// class id: 1049995, size: 0x8
class :: {

  static _ CounterTypeExtension.route(/* No info */) {
    // ** addr: 0x8efc3c, size: 0xb8
    // 0x8efc3c: EnterFrame
    //     0x8efc3c: stp             fp, lr, [SP, #-0x10]!
    //     0x8efc40: mov             fp, SP
    // 0x8efc44: LoadField: r2 = r1->field_7
    //     0x8efc44: ldur            x2, [x1, #7]
    // 0x8efc48: cmp             x2, #2
    // 0x8efc4c: b.gt            #0x8efc9c
    // 0x8efc50: cmp             x2, #1
    // 0x8efc54: b.gt            #0x8efc88
    // 0x8efc58: cmp             x2, #0
    // 0x8efc5c: b.gt            #0x8efc74
    // 0x8efc60: r0 = "doa-view"
    //     0x8efc60: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d3f0] "doa-view"
    //     0x8efc64: ldr             x0, [x0, #0x3f0]
    // 0x8efc68: LeaveFrame
    //     0x8efc68: mov             SP, fp
    //     0x8efc6c: ldp             fp, lr, [SP], #0x10
    // 0x8efc70: ret
    //     0x8efc70: ret             
    // 0x8efc74: r0 = "doa-category-view"
    //     0x8efc74: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d3f8] "doa-category-view"
    //     0x8efc78: ldr             x0, [x0, #0x3f8]
    // 0x8efc7c: LeaveFrame
    //     0x8efc7c: mov             SP, fp
    //     0x8efc80: ldp             fp, lr, [SP], #0x10
    // 0x8efc84: ret
    //     0x8efc84: ret             
    // 0x8efc88: r0 = "kalam-preview"
    //     0x8efc88: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d400] "kalam-preview"
    //     0x8efc8c: ldr             x0, [x0, #0x400]
    // 0x8efc90: LeaveFrame
    //     0x8efc90: mov             SP, fp
    //     0x8efc94: ldp             fp, lr, [SP], #0x10
    // 0x8efc98: ret
    //     0x8efc98: ret             
    // 0x8efc9c: cmp             x2, #3
    // 0x8efca0: b.gt            #0x8efcb8
    // 0x8efca4: r0 = "kalam-share"
    //     0x8efca4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d408] "kalam-share"
    //     0x8efca8: ldr             x0, [x0, #0x408]
    // 0x8efcac: LeaveFrame
    //     0x8efcac: mov             SP, fp
    //     0x8efcb0: ldp             fp, lr, [SP], #0x10
    // 0x8efcb4: ret
    //     0x8efcb4: ret             
    // 0x8efcb8: r0 = BoxInt64Instr(r2)
    //     0x8efcb8: sbfiz           x0, x2, #1, #0x1f
    //     0x8efcbc: cmp             x2, x0, asr #1
    //     0x8efcc0: b.eq            #0x8efccc
    //     0x8efcc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8efcc8: stur            x2, [x0, #7]
    // 0x8efccc: cmp             w0, #8
    // 0x8efcd0: b.ne            #0x8efce8
    // 0x8efcd4: r0 = "encyclopedia-view"
    //     0x8efcd4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d410] "encyclopedia-view"
    //     0x8efcd8: ldr             x0, [x0, #0x410]
    // 0x8efcdc: LeaveFrame
    //     0x8efcdc: mov             SP, fp
    //     0x8efce0: ldp             fp, lr, [SP], #0x10
    // 0x8efce4: ret
    //     0x8efce4: ret             
    // 0x8efce8: r0 = UnimplementedError()
    //     0x8efce8: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x8efcec: r0 = Throw()
    //     0x8efcec: bl              #0xec04b8  ; ThrowStub
    // 0x8efcf0: brk             #0
  }
}

// class id: 6848, size: 0x14, field offset: 0x14
enum CounterType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cdf8, size: 0x64
    // 0xc4cdf8: EnterFrame
    //     0xc4cdf8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cdfc: mov             fp, SP
    // 0xc4ce00: AllocStack(0x10)
    //     0xc4ce00: sub             SP, SP, #0x10
    // 0xc4ce04: SetupParameters(CounterType this /* r1 => r0, fp-0x8 */)
    //     0xc4ce04: mov             x0, x1
    //     0xc4ce08: stur            x1, [fp, #-8]
    // 0xc4ce0c: CheckStackOverflow
    //     0xc4ce0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ce10: cmp             SP, x16
    //     0xc4ce14: b.ls            #0xc4ce54
    // 0xc4ce18: r1 = Null
    //     0xc4ce18: mov             x1, NULL
    // 0xc4ce1c: r2 = 4
    //     0xc4ce1c: movz            x2, #0x4
    // 0xc4ce20: r0 = AllocateArray()
    //     0xc4ce20: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ce24: r16 = "CounterType."
    //     0xc4ce24: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f98] "CounterType."
    //     0xc4ce28: ldr             x16, [x16, #0xf98]
    // 0xc4ce2c: StoreField: r0->field_f = r16
    //     0xc4ce2c: stur            w16, [x0, #0xf]
    // 0xc4ce30: ldur            x1, [fp, #-8]
    // 0xc4ce34: LoadField: r2 = r1->field_f
    //     0xc4ce34: ldur            w2, [x1, #0xf]
    // 0xc4ce38: DecompressPointer r2
    //     0xc4ce38: add             x2, x2, HEAP, lsl #32
    // 0xc4ce3c: StoreField: r0->field_13 = r2
    //     0xc4ce3c: stur            w2, [x0, #0x13]
    // 0xc4ce40: str             x0, [SP]
    // 0xc4ce44: r0 = _interpolate()
    //     0xc4ce44: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ce48: LeaveFrame
    //     0xc4ce48: mov             SP, fp
    //     0xc4ce4c: ldp             fp, lr, [SP], #0x10
    // 0xc4ce50: ret
    //     0xc4ce50: ret             
    // 0xc4ce54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ce54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ce58: b               #0xc4ce18
  }
}
