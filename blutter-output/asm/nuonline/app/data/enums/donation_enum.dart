// lib: , url: package:nuonline/app/data/enums/donation_enum.dart

// class id: 1049996, size: 0x8
class :: {
}

// class id: 6847, size: 0x14, field offset: 0x14
enum DonationSupportPlacement extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ce5c, size: 0x64
    // 0xc4ce5c: EnterFrame
    //     0xc4ce5c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ce60: mov             fp, SP
    // 0xc4ce64: AllocStack(0x10)
    //     0xc4ce64: sub             SP, SP, #0x10
    // 0xc4ce68: SetupParameters(DonationSupportPlacement this /* r1 => r0, fp-0x8 */)
    //     0xc4ce68: mov             x0, x1
    //     0xc4ce6c: stur            x1, [fp, #-8]
    // 0xc4ce70: CheckStackOverflow
    //     0xc4ce70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ce74: cmp             SP, x16
    //     0xc4ce78: b.ls            #0xc4ceb8
    // 0xc4ce7c: r1 = Null
    //     0xc4ce7c: mov             x1, NULL
    // 0xc4ce80: r2 = 4
    //     0xc4ce80: movz            x2, #0x4
    // 0xc4ce84: r0 = AllocateArray()
    //     0xc4ce84: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ce88: r16 = "DonationSupportPlacement."
    //     0xc4ce88: add             x16, PP, #0x21, lsl #12  ; [pp+0x219b0] "DonationSupportPlacement."
    //     0xc4ce8c: ldr             x16, [x16, #0x9b0]
    // 0xc4ce90: StoreField: r0->field_f = r16
    //     0xc4ce90: stur            w16, [x0, #0xf]
    // 0xc4ce94: ldur            x1, [fp, #-8]
    // 0xc4ce98: LoadField: r2 = r1->field_f
    //     0xc4ce98: ldur            w2, [x1, #0xf]
    // 0xc4ce9c: DecompressPointer r2
    //     0xc4ce9c: add             x2, x2, HEAP, lsl #32
    // 0xc4cea0: StoreField: r0->field_13 = r2
    //     0xc4cea0: stur            w2, [x0, #0x13]
    // 0xc4cea4: str             x0, [SP]
    // 0xc4cea8: r0 = _interpolate()
    //     0xc4cea8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ceac: LeaveFrame
    //     0xc4ceac: mov             SP, fp
    //     0xc4ceb0: ldp             fp, lr, [SP], #0x10
    // 0xc4ceb4: ret
    //     0xc4ceb4: ret             
    // 0xc4ceb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ceb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cebc: b               #0xc4ce7c
  }
}
