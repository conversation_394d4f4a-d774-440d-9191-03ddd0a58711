// lib: , url: package:nuonline/app/data/enums/history_enum.dart

// class id: 1049997, size: 0x8
class :: {
}

// class id: 6846, size: 0x14, field offset: 0x14
enum HistoryStatus extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cec0, size: 0x64
    // 0xc4cec0: EnterFrame
    //     0xc4cec0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cec4: mov             fp, SP
    // 0xc4cec8: AllocStack(0x10)
    //     0xc4cec8: sub             SP, SP, #0x10
    // 0xc4cecc: SetupParameters(HistoryStatus this /* r1 => r0, fp-0x8 */)
    //     0xc4cecc: mov             x0, x1
    //     0xc4ced0: stur            x1, [fp, #-8]
    // 0xc4ced4: CheckStackOverflow
    //     0xc4ced4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ced8: cmp             SP, x16
    //     0xc4cedc: b.ls            #0xc4cf1c
    // 0xc4cee0: r1 = Null
    //     0xc4cee0: mov             x1, NULL
    // 0xc4cee4: r2 = 4
    //     0xc4cee4: movz            x2, #0x4
    // 0xc4cee8: r0 = AllocateArray()
    //     0xc4cee8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ceec: r16 = "HistoryStatus."
    //     0xc4ceec: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f90] "HistoryStatus."
    //     0xc4cef0: ldr             x16, [x16, #0xf90]
    // 0xc4cef4: StoreField: r0->field_f = r16
    //     0xc4cef4: stur            w16, [x0, #0xf]
    // 0xc4cef8: ldur            x1, [fp, #-8]
    // 0xc4cefc: LoadField: r2 = r1->field_f
    //     0xc4cefc: ldur            w2, [x1, #0xf]
    // 0xc4cf00: DecompressPointer r2
    //     0xc4cf00: add             x2, x2, HEAP, lsl #32
    // 0xc4cf04: StoreField: r0->field_13 = r2
    //     0xc4cf04: stur            w2, [x0, #0x13]
    // 0xc4cf08: str             x0, [SP]
    // 0xc4cf0c: r0 = _interpolate()
    //     0xc4cf0c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4cf10: LeaveFrame
    //     0xc4cf10: mov             SP, fp
    //     0xc4cf14: ldp             fp, lr, [SP], #0x10
    // 0xc4cf18: ret
    //     0xc4cf18: ret             
    // 0xc4cf1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4cf1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cf20: b               #0xc4cee0
  }
}
