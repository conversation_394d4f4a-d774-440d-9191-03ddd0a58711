// lib: , url: package:nuonline/app/data/enums/payment_enum.dart

// class id: 1049998, size: 0x8
class :: {

  static _ paymentMethodFromString(/* No info */) {
    // ** addr: 0x6ff544, size: 0xdc
    // 0x6ff544: EnterFrame
    //     0x6ff544: stp             fp, lr, [SP, #-0x10]!
    //     0x6ff548: mov             fp, SP
    // 0x6ff54c: AllocStack(0x18)
    //     0x6ff54c: sub             SP, SP, #0x18
    // 0x6ff550: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x6ff550: stur            x1, [fp, #-8]
    // 0x6ff554: CheckStackOverflow
    //     0x6ff554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ff558: cmp             SP, x16
    //     0x6ff55c: b.ls            #0x6ff618
    // 0x6ff560: r16 = "qris"
    //     0x6ff560: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b218] "qris"
    //     0x6ff564: ldr             x16, [x16, #0x218]
    // 0x6ff568: stp             x1, x16, [SP]
    // 0x6ff56c: r0 = ==()
    //     0x6ff56c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x6ff570: tbnz            w0, #4, #0x6ff588
    // 0x6ff574: r0 = Instance_PaymentMethod
    //     0x6ff574: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b220] Obj!PaymentMethod@e30de1
    //     0x6ff578: ldr             x0, [x0, #0x220]
    // 0x6ff57c: LeaveFrame
    //     0x6ff57c: mov             SP, fp
    //     0x6ff580: ldp             fp, lr, [SP], #0x10
    // 0x6ff584: ret
    //     0x6ff584: ret             
    // 0x6ff588: r16 = "va"
    //     0x6ff588: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b228] "va"
    //     0x6ff58c: ldr             x16, [x16, #0x228]
    // 0x6ff590: ldur            lr, [fp, #-8]
    // 0x6ff594: stp             lr, x16, [SP]
    // 0x6ff598: r0 = ==()
    //     0x6ff598: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x6ff59c: tbnz            w0, #4, #0x6ff5b4
    // 0x6ff5a0: r0 = Instance_PaymentMethod
    //     0x6ff5a0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b230] Obj!PaymentMethod@e30dc1
    //     0x6ff5a4: ldr             x0, [x0, #0x230]
    // 0x6ff5a8: LeaveFrame
    //     0x6ff5a8: mov             SP, fp
    //     0x6ff5ac: ldp             fp, lr, [SP], #0x10
    // 0x6ff5b0: ret
    //     0x6ff5b0: ret             
    // 0x6ff5b4: r16 = "bsi"
    //     0x6ff5b4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b238] "bsi"
    //     0x6ff5b8: ldr             x16, [x16, #0x238]
    // 0x6ff5bc: ldur            lr, [fp, #-8]
    // 0x6ff5c0: stp             lr, x16, [SP]
    // 0x6ff5c4: r0 = ==()
    //     0x6ff5c4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x6ff5c8: tbnz            w0, #4, #0x6ff5e0
    // 0x6ff5cc: r0 = Instance_PaymentMethod
    //     0x6ff5cc: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b240] Obj!PaymentMethod@e30da1
    //     0x6ff5d0: ldr             x0, [x0, #0x240]
    // 0x6ff5d4: LeaveFrame
    //     0x6ff5d4: mov             SP, fp
    //     0x6ff5d8: ldp             fp, lr, [SP], #0x10
    // 0x6ff5dc: ret
    //     0x6ff5dc: ret             
    // 0x6ff5e0: r16 = "bri"
    //     0x6ff5e0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b248] "bri"
    //     0x6ff5e4: ldr             x16, [x16, #0x248]
    // 0x6ff5e8: ldur            lr, [fp, #-8]
    // 0x6ff5ec: stp             lr, x16, [SP]
    // 0x6ff5f0: r0 = ==()
    //     0x6ff5f0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x6ff5f4: tbnz            w0, #4, #0x6ff60c
    // 0x6ff5f8: r0 = Instance_PaymentMethod
    //     0x6ff5f8: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b250] Obj!PaymentMethod@e30d81
    //     0x6ff5fc: ldr             x0, [x0, #0x250]
    // 0x6ff600: LeaveFrame
    //     0x6ff600: mov             SP, fp
    //     0x6ff604: ldp             fp, lr, [SP], #0x10
    // 0x6ff608: ret
    //     0x6ff608: ret             
    // 0x6ff60c: r0 = UnimplementedError()
    //     0x6ff60c: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x6ff610: r0 = Throw()
    //     0x6ff610: bl              #0xec04b8  ; ThrowStub
    // 0x6ff614: brk             #0
    // 0x6ff618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ff618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ff61c: b               #0x6ff560
  }
  static _ PaymentMethodExtension.instructions(/* No info */) {
    // ** addr: 0xa38bb4, size: 0xb68
    // 0xa38bb4: EnterFrame
    //     0xa38bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa38bb8: mov             fp, SP
    // 0xa38bbc: AllocStack(0x38)
    //     0xa38bbc: sub             SP, SP, #0x38
    // 0xa38bc0: LoadField: r0 = r1->field_7
    //     0xa38bc0: ldur            x0, [x1, #7]
    // 0xa38bc4: cmp             x0, #1
    // 0xa38bc8: b.gt            #0xa39144
    // 0xa38bcc: cmp             x0, #0
    // 0xa38bd0: b.gt            #0xa38cdc
    // 0xa38bd4: r0 = 18
    //     0xa38bd4: movz            x0, #0x12
    // 0xa38bd8: mov             x2, x0
    // 0xa38bdc: r1 = <String>
    //     0xa38bdc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38be0: r0 = AllocateArray()
    //     0xa38be0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38be4: stur            x0, [fp, #-8]
    // 0xa38be8: r16 = "Klik tombol \"Bayar\"."
    //     0xa38be8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30570] "Klik tombol \"Bayar\"."
    //     0xa38bec: ldr             x16, [x16, #0x570]
    // 0xa38bf0: StoreField: r0->field_f = r16
    //     0xa38bf0: stur            w16, [x0, #0xf]
    // 0xa38bf4: r16 = "Kemudian akan terlihat QR Code dari pembayaran menggunakan QRIS"
    //     0xa38bf4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30578] "Kemudian akan terlihat QR Code dari pembayaran menggunakan QRIS"
    //     0xa38bf8: ldr             x16, [x16, #0x578]
    // 0xa38bfc: StoreField: r0->field_13 = r16
    //     0xa38bfc: stur            w16, [x0, #0x13]
    // 0xa38c00: r16 = "Pilih tombol \"Download QRIS\" untuk menyimpan QR Code-nya"
    //     0xa38c00: add             x16, PP, #0x30, lsl #12  ; [pp+0x30580] "Pilih tombol \"Download QRIS\" untuk menyimpan QR Code-nya"
    //     0xa38c04: ldr             x16, [x16, #0x580]
    // 0xa38c08: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38c08: stur            w16, [x0, #0x17]
    // 0xa38c0c: r16 = "Buka Aplikasi Dompet Elektronik (e-wallet) yang dimiliki dan mendukung fasilitas QRIS"
    //     0xa38c0c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30588] "Buka Aplikasi Dompet Elektronik (e-wallet) yang dimiliki dan mendukung fasilitas QRIS"
    //     0xa38c10: ldr             x16, [x16, #0x588]
    // 0xa38c14: StoreField: r0->field_1b = r16
    //     0xa38c14: stur            w16, [x0, #0x1b]
    // 0xa38c18: r16 = "Buka menu \"Scan/Bayar\" kemudian pilih metode/tekan ikon \"Upload Gambar\". Kemudian pilih gambar QR Code dari QRIS yang sudah didownload sebelumnya"
    //     0xa38c18: add             x16, PP, #0x30, lsl #12  ; [pp+0x30590] "Buka menu \"Scan/Bayar\" kemudian pilih metode/tekan ikon \"Upload Gambar\". Kemudian pilih gambar QR Code dari QRIS yang sudah didownload sebelumnya"
    //     0xa38c1c: ldr             x16, [x16, #0x590]
    // 0xa38c20: StoreField: r0->field_1f = r16
    //     0xa38c20: stur            w16, [x0, #0x1f]
    // 0xa38c24: r16 = "Periksa kembali detail pembayaran anda"
    //     0xa38c24: add             x16, PP, #0x30, lsl #12  ; [pp+0x30598] "Periksa kembali detail pembayaran anda"
    //     0xa38c28: ldr             x16, [x16, #0x598]
    // 0xa38c2c: StoreField: r0->field_23 = r16
    //     0xa38c2c: stur            w16, [x0, #0x23]
    // 0xa38c30: r16 = "Jika sudah sesuai, konfirmasi dan bayar"
    //     0xa38c30: add             x16, PP, #0x30, lsl #12  ; [pp+0x305a0] "Jika sudah sesuai, konfirmasi dan bayar"
    //     0xa38c34: ldr             x16, [x16, #0x5a0]
    // 0xa38c38: StoreField: r0->field_27 = r16
    //     0xa38c38: stur            w16, [x0, #0x27]
    // 0xa38c3c: r16 = "Transaksi Berhasil"
    //     0xa38c3c: add             x16, PP, #0x30, lsl #12  ; [pp+0x305a8] "Transaksi Berhasil"
    //     0xa38c40: ldr             x16, [x16, #0x5a8]
    // 0xa38c44: StoreField: r0->field_2b = r16
    //     0xa38c44: stur            w16, [x0, #0x2b]
    // 0xa38c48: r16 = "Buka Aplikasi NU Online atau email yang terhubung dengan e-wallet Anda dan cek kembali transaksi."
    //     0xa38c48: add             x16, PP, #0x30, lsl #12  ; [pp+0x305b0] "Buka Aplikasi NU Online atau email yang terhubung dengan e-wallet Anda dan cek kembali transaksi."
    //     0xa38c4c: ldr             x16, [x16, #0x5b0]
    // 0xa38c50: StoreField: r0->field_2f = r16
    //     0xa38c50: stur            w16, [x0, #0x2f]
    // 0xa38c54: r1 = <String>
    //     0xa38c54: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38c58: r0 = AllocateGrowableArray()
    //     0xa38c58: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38c5c: mov             x1, x0
    // 0xa38c60: ldur            x0, [fp, #-8]
    // 0xa38c64: stur            x1, [fp, #-0x10]
    // 0xa38c68: StoreField: r1->field_f = r0
    //     0xa38c68: stur            w0, [x1, #0xf]
    // 0xa38c6c: r0 = 18
    //     0xa38c6c: movz            x0, #0x12
    // 0xa38c70: StoreField: r1->field_b = r0
    //     0xa38c70: stur            w0, [x1, #0xb]
    // 0xa38c74: r0 = Instruction()
    //     0xa38c74: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa38c78: mov             x3, x0
    // 0xa38c7c: r0 = ""
    //     0xa38c7c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa38c80: stur            x3, [fp, #-8]
    // 0xa38c84: StoreField: r3->field_7 = r0
    //     0xa38c84: stur            w0, [x3, #7]
    // 0xa38c88: ldur            x0, [fp, #-0x10]
    // 0xa38c8c: StoreField: r3->field_b = r0
    //     0xa38c8c: stur            w0, [x3, #0xb]
    // 0xa38c90: r1 = Null
    //     0xa38c90: mov             x1, NULL
    // 0xa38c94: r2 = 2
    //     0xa38c94: movz            x2, #0x2
    // 0xa38c98: r0 = AllocateArray()
    //     0xa38c98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38c9c: mov             x2, x0
    // 0xa38ca0: ldur            x0, [fp, #-8]
    // 0xa38ca4: stur            x2, [fp, #-0x10]
    // 0xa38ca8: StoreField: r2->field_f = r0
    //     0xa38ca8: stur            w0, [x2, #0xf]
    // 0xa38cac: r1 = <Instruction>
    //     0xa38cac: add             x1, PP, #0x30, lsl #12  ; [pp+0x305b8] TypeArguments: <Instruction>
    //     0xa38cb0: ldr             x1, [x1, #0x5b8]
    // 0xa38cb4: r0 = AllocateGrowableArray()
    //     0xa38cb4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38cb8: mov             x1, x0
    // 0xa38cbc: ldur            x0, [fp, #-0x10]
    // 0xa38cc0: StoreField: r1->field_f = r0
    //     0xa38cc0: stur            w0, [x1, #0xf]
    // 0xa38cc4: r0 = 2
    //     0xa38cc4: movz            x0, #0x2
    // 0xa38cc8: StoreField: r1->field_b = r0
    //     0xa38cc8: stur            w0, [x1, #0xb]
    // 0xa38ccc: mov             x0, x1
    // 0xa38cd0: LeaveFrame
    //     0xa38cd0: mov             SP, fp
    //     0xa38cd4: ldp             fp, lr, [SP], #0x10
    // 0xa38cd8: ret
    //     0xa38cd8: ret             
    // 0xa38cdc: r0 = 18
    //     0xa38cdc: movz            x0, #0x12
    // 0xa38ce0: mov             x2, x0
    // 0xa38ce4: r1 = <String>
    //     0xa38ce4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38ce8: r0 = AllocateArray()
    //     0xa38ce8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38cec: stur            x0, [fp, #-8]
    // 0xa38cf0: r16 = "Masukkan Kartu Anda."
    //     0xa38cf0: add             x16, PP, #0x30, lsl #12  ; [pp+0x305c0] "Masukkan Kartu Anda."
    //     0xa38cf4: ldr             x16, [x16, #0x5c0]
    // 0xa38cf8: StoreField: r0->field_f = r16
    //     0xa38cf8: stur            w16, [x0, #0xf]
    // 0xa38cfc: r16 = "Pilih Bahasa."
    //     0xa38cfc: add             x16, PP, #0x30, lsl #12  ; [pp+0x305c8] "Pilih Bahasa."
    //     0xa38d00: ldr             x16, [x16, #0x5c8]
    // 0xa38d04: StoreField: r0->field_13 = r16
    //     0xa38d04: stur            w16, [x0, #0x13]
    // 0xa38d08: r16 = "Masukkan PIN ATM Anda."
    //     0xa38d08: add             x16, PP, #0x30, lsl #12  ; [pp+0x305d0] "Masukkan PIN ATM Anda."
    //     0xa38d0c: ldr             x16, [x16, #0x5d0]
    // 0xa38d10: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38d10: stur            w16, [x0, #0x17]
    // 0xa38d14: r16 = "Kemudian, pilih \"Menu Lainnya\"."
    //     0xa38d14: add             x16, PP, #0x30, lsl #12  ; [pp+0x305d8] "Kemudian, pilih \"Menu Lainnya\"."
    //     0xa38d18: ldr             x16, [x16, #0x5d8]
    // 0xa38d1c: StoreField: r0->field_1b = r16
    //     0xa38d1c: stur            w16, [x0, #0x1b]
    // 0xa38d20: r16 = "Pilih \"Transfer\" dan pilih Jenis rekening yang akan Anda gunakan (Contoh: \"Dari Rekening Tabungan\")."
    //     0xa38d20: add             x16, PP, #0x30, lsl #12  ; [pp+0x305e0] "Pilih \"Transfer\" dan pilih Jenis rekening yang akan Anda gunakan (Contoh: \"Dari Rekening Tabungan\")."
    //     0xa38d24: ldr             x16, [x16, #0x5e0]
    // 0xa38d28: StoreField: r0->field_1f = r16
    //     0xa38d28: stur            w16, [x0, #0x1f]
    // 0xa38d2c: r16 = "Pilih \"Virtual Account Billing\". Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa38d2c: add             x16, PP, #0x30, lsl #12  ; [pp+0x305e8] "Pilih \"Virtual Account Billing\". Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa38d30: ldr             x16, [x16, #0x5e8]
    // 0xa38d34: StoreField: r0->field_23 = r16
    //     0xa38d34: stur            w16, [x0, #0x23]
    // 0xa38d38: r16 = "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38d38: add             x16, PP, #0x30, lsl #12  ; [pp+0x305f0] "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38d3c: ldr             x16, [x16, #0x5f0]
    // 0xa38d40: StoreField: r0->field_27 = r16
    //     0xa38d40: stur            w16, [x0, #0x27]
    // 0xa38d44: r16 = "Konfirmasi, apabila telah sesuai, lanjutkan transaksi."
    //     0xa38d44: add             x16, PP, #0x30, lsl #12  ; [pp+0x305f8] "Konfirmasi, apabila telah sesuai, lanjutkan transaksi."
    //     0xa38d48: ldr             x16, [x16, #0x5f8]
    // 0xa38d4c: StoreField: r0->field_2b = r16
    //     0xa38d4c: stur            w16, [x0, #0x2b]
    // 0xa38d50: r16 = "Transaksi Anda telah selesai."
    //     0xa38d50: add             x16, PP, #0x30, lsl #12  ; [pp+0x30600] "Transaksi Anda telah selesai."
    //     0xa38d54: ldr             x16, [x16, #0x600]
    // 0xa38d58: StoreField: r0->field_2f = r16
    //     0xa38d58: stur            w16, [x0, #0x2f]
    // 0xa38d5c: r1 = <String>
    //     0xa38d5c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38d60: r0 = AllocateGrowableArray()
    //     0xa38d60: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38d64: mov             x1, x0
    // 0xa38d68: ldur            x0, [fp, #-8]
    // 0xa38d6c: stur            x1, [fp, #-0x10]
    // 0xa38d70: StoreField: r1->field_f = r0
    //     0xa38d70: stur            w0, [x1, #0xf]
    // 0xa38d74: r3 = 18
    //     0xa38d74: movz            x3, #0x12
    // 0xa38d78: StoreField: r1->field_b = r3
    //     0xa38d78: stur            w3, [x1, #0xb]
    // 0xa38d7c: r0 = Instruction()
    //     0xa38d7c: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa38d80: mov             x3, x0
    // 0xa38d84: r0 = "Melalui ATM BNI"
    //     0xa38d84: add             x0, PP, #0x30, lsl #12  ; [pp+0x30608] "Melalui ATM BNI"
    //     0xa38d88: ldr             x0, [x0, #0x608]
    // 0xa38d8c: stur            x3, [fp, #-8]
    // 0xa38d90: StoreField: r3->field_7 = r0
    //     0xa38d90: stur            w0, [x3, #7]
    // 0xa38d94: ldur            x0, [fp, #-0x10]
    // 0xa38d98: StoreField: r3->field_b = r0
    //     0xa38d98: stur            w0, [x3, #0xb]
    // 0xa38d9c: r1 = Null
    //     0xa38d9c: mov             x1, NULL
    // 0xa38da0: r2 = 16
    //     0xa38da0: movz            x2, #0x10
    // 0xa38da4: r0 = AllocateArray()
    //     0xa38da4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38da8: stur            x0, [fp, #-0x10]
    // 0xa38dac: r16 = "Akses BNI Mobile Banking melalui handphone."
    //     0xa38dac: add             x16, PP, #0x30, lsl #12  ; [pp+0x30610] "Akses BNI Mobile Banking melalui handphone."
    //     0xa38db0: ldr             x16, [x16, #0x610]
    // 0xa38db4: StoreField: r0->field_f = r16
    //     0xa38db4: stur            w16, [x0, #0xf]
    // 0xa38db8: r16 = "Masukkan User ID dan password."
    //     0xa38db8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30618] "Masukkan User ID dan password."
    //     0xa38dbc: ldr             x16, [x16, #0x618]
    // 0xa38dc0: StoreField: r0->field_13 = r16
    //     0xa38dc0: stur            w16, [x0, #0x13]
    // 0xa38dc4: r16 = "Pilih menu \"Transfer\"."
    //     0xa38dc4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30620] "Pilih menu \"Transfer\"."
    //     0xa38dc8: ldr             x16, [x16, #0x620]
    // 0xa38dcc: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38dcc: stur            w16, [x0, #0x17]
    // 0xa38dd0: r16 = "Pilih menu \"Virtual Account Billing\", lalu pilih rekening debet."
    //     0xa38dd0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30628] "Pilih menu \"Virtual Account Billing\", lalu pilih rekening debet."
    //     0xa38dd4: ldr             x16, [x16, #0x628]
    // 0xa38dd8: StoreField: r0->field_1b = r16
    //     0xa38dd8: stur            w16, [x0, #0x1b]
    // 0xa38ddc: r16 = "Masukkan nomor Virtual Account Anda (Contoh: ****************) pada menu \"Input Baru\"."
    //     0xa38ddc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30630] "Masukkan nomor Virtual Account Anda (Contoh: ****************) pada menu \"Input Baru\"."
    //     0xa38de0: ldr             x16, [x16, #0x630]
    // 0xa38de4: StoreField: r0->field_1f = r16
    //     0xa38de4: stur            w16, [x0, #0x1f]
    // 0xa38de8: r16 = "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38de8: add             x16, PP, #0x30, lsl #12  ; [pp+0x305f0] "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38dec: ldr             x16, [x16, #0x5f0]
    // 0xa38df0: StoreField: r0->field_23 = r16
    //     0xa38df0: stur            w16, [x0, #0x23]
    // 0xa38df4: r16 = "Konfirmasi transaksi dan masukkan Password Transaksi."
    //     0xa38df4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30638] "Konfirmasi transaksi dan masukkan Password Transaksi."
    //     0xa38df8: ldr             x16, [x16, #0x638]
    // 0xa38dfc: StoreField: r0->field_27 = r16
    //     0xa38dfc: stur            w16, [x0, #0x27]
    // 0xa38e00: r16 = "Pembayaran Anda Telah Berhasil."
    //     0xa38e00: add             x16, PP, #0x30, lsl #12  ; [pp+0x30640] "Pembayaran Anda Telah Berhasil."
    //     0xa38e04: ldr             x16, [x16, #0x640]
    // 0xa38e08: StoreField: r0->field_2b = r16
    //     0xa38e08: stur            w16, [x0, #0x2b]
    // 0xa38e0c: r1 = <String>
    //     0xa38e0c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38e10: r0 = AllocateGrowableArray()
    //     0xa38e10: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38e14: mov             x1, x0
    // 0xa38e18: ldur            x0, [fp, #-0x10]
    // 0xa38e1c: stur            x1, [fp, #-0x18]
    // 0xa38e20: StoreField: r1->field_f = r0
    //     0xa38e20: stur            w0, [x1, #0xf]
    // 0xa38e24: r2 = 16
    //     0xa38e24: movz            x2, #0x10
    // 0xa38e28: StoreField: r1->field_b = r2
    //     0xa38e28: stur            w2, [x1, #0xb]
    // 0xa38e2c: r0 = Instruction()
    //     0xa38e2c: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa38e30: mov             x3, x0
    // 0xa38e34: r0 = "Melalui Mobile Banking BNI"
    //     0xa38e34: add             x0, PP, #0x30, lsl #12  ; [pp+0x30648] "Melalui Mobile Banking BNI"
    //     0xa38e38: ldr             x0, [x0, #0x648]
    // 0xa38e3c: stur            x3, [fp, #-0x10]
    // 0xa38e40: StoreField: r3->field_7 = r0
    //     0xa38e40: stur            w0, [x3, #7]
    // 0xa38e44: ldur            x0, [fp, #-0x18]
    // 0xa38e48: StoreField: r3->field_b = r0
    //     0xa38e48: stur            w0, [x3, #0xb]
    // 0xa38e4c: r1 = Null
    //     0xa38e4c: mov             x1, NULL
    // 0xa38e50: r2 = 16
    //     0xa38e50: movz            x2, #0x10
    // 0xa38e54: r0 = AllocateArray()
    //     0xa38e54: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38e58: stur            x0, [fp, #-0x18]
    // 0xa38e5c: r16 = "Akses ibank.bni.co.id kemudian klik \"Enter\"."
    //     0xa38e5c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30650] "Akses ibank.bni.co.id kemudian klik \"Enter\"."
    //     0xa38e60: ldr             x16, [x16, #0x650]
    // 0xa38e64: StoreField: r0->field_f = r16
    //     0xa38e64: stur            w16, [x0, #0xf]
    // 0xa38e68: r16 = "Masukkan User ID dan password."
    //     0xa38e68: add             x16, PP, #0x30, lsl #12  ; [pp+0x30618] "Masukkan User ID dan password."
    //     0xa38e6c: ldr             x16, [x16, #0x618]
    // 0xa38e70: StoreField: r0->field_13 = r16
    //     0xa38e70: stur            w16, [x0, #0x13]
    // 0xa38e74: r16 = "Klik menu \"Transfer\", lalu pilih \"Virtual Account Billing\"."
    //     0xa38e74: add             x16, PP, #0x30, lsl #12  ; [pp+0x30658] "Klik menu \"Transfer\", lalu pilih \"Virtual Account Billing\"."
    //     0xa38e78: ldr             x16, [x16, #0x658]
    // 0xa38e7c: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38e7c: stur            w16, [x0, #0x17]
    // 0xa38e80: r16 = "Kemudian, masukan nomor Virtual Account Anda (Contoh: ****************) yang akan dibayarkan."
    //     0xa38e80: add             x16, PP, #0x30, lsl #12  ; [pp+0x30660] "Kemudian, masukan nomor Virtual Account Anda (Contoh: ****************) yang akan dibayarkan."
    //     0xa38e84: ldr             x16, [x16, #0x660]
    // 0xa38e88: StoreField: r0->field_1b = r16
    //     0xa38e88: stur            w16, [x0, #0x1b]
    // 0xa38e8c: r16 = "Lalu pilih rekening debet yang akan digunakan. Kemudian tekan \"Lanjut\"."
    //     0xa38e8c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30668] "Lalu pilih rekening debet yang akan digunakan. Kemudian tekan \"Lanjut\"."
    //     0xa38e90: ldr             x16, [x16, #0x668]
    // 0xa38e94: StoreField: r0->field_1f = r16
    //     0xa38e94: stur            w16, [x0, #0x1f]
    // 0xa38e98: r16 = "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38e98: add             x16, PP, #0x30, lsl #12  ; [pp+0x305f0] "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa38e9c: ldr             x16, [x16, #0x5f0]
    // 0xa38ea0: StoreField: r0->field_23 = r16
    //     0xa38ea0: stur            w16, [x0, #0x23]
    // 0xa38ea4: r16 = "Masukkan Kode Otentikasi Token."
    //     0xa38ea4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30670] "Masukkan Kode Otentikasi Token."
    //     0xa38ea8: ldr             x16, [x16, #0x670]
    // 0xa38eac: StoreField: r0->field_27 = r16
    //     0xa38eac: stur            w16, [x0, #0x27]
    // 0xa38eb0: r16 = "Anda akan menerima notifikasi bahwa transaksi berhasil."
    //     0xa38eb0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30678] "Anda akan menerima notifikasi bahwa transaksi berhasil."
    //     0xa38eb4: ldr             x16, [x16, #0x678]
    // 0xa38eb8: StoreField: r0->field_2b = r16
    //     0xa38eb8: stur            w16, [x0, #0x2b]
    // 0xa38ebc: r1 = <String>
    //     0xa38ebc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38ec0: r0 = AllocateGrowableArray()
    //     0xa38ec0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38ec4: mov             x1, x0
    // 0xa38ec8: ldur            x0, [fp, #-0x18]
    // 0xa38ecc: stur            x1, [fp, #-0x20]
    // 0xa38ed0: StoreField: r1->field_f = r0
    //     0xa38ed0: stur            w0, [x1, #0xf]
    // 0xa38ed4: r2 = 16
    //     0xa38ed4: movz            x2, #0x10
    // 0xa38ed8: StoreField: r1->field_b = r2
    //     0xa38ed8: stur            w2, [x1, #0xb]
    // 0xa38edc: r0 = Instruction()
    //     0xa38edc: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa38ee0: mov             x3, x0
    // 0xa38ee4: r0 = "Melalui iBank Personal"
    //     0xa38ee4: add             x0, PP, #0x30, lsl #12  ; [pp+0x30680] "Melalui iBank Personal"
    //     0xa38ee8: ldr             x0, [x0, #0x680]
    // 0xa38eec: stur            x3, [fp, #-0x18]
    // 0xa38ef0: StoreField: r3->field_7 = r0
    //     0xa38ef0: stur            w0, [x3, #7]
    // 0xa38ef4: ldur            x0, [fp, #-0x20]
    // 0xa38ef8: StoreField: r3->field_b = r0
    //     0xa38ef8: stur            w0, [x3, #0xb]
    // 0xa38efc: r1 = Null
    //     0xa38efc: mov             x1, NULL
    // 0xa38f00: r2 = 16
    //     0xa38f00: movz            x2, #0x10
    // 0xa38f04: r0 = AllocateArray()
    //     0xa38f04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38f08: stur            x0, [fp, #-0x20]
    // 0xa38f0c: r16 = "Buka aplikasi SMS Banking BNI."
    //     0xa38f0c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30688] "Buka aplikasi SMS Banking BNI."
    //     0xa38f10: ldr             x16, [x16, #0x688]
    // 0xa38f14: StoreField: r0->field_f = r16
    //     0xa38f14: stur            w16, [x0, #0xf]
    // 0xa38f18: r16 = "Pilih menu \"Transfer\"."
    //     0xa38f18: add             x16, PP, #0x30, lsl #12  ; [pp+0x30620] "Pilih menu \"Transfer\"."
    //     0xa38f1c: ldr             x16, [x16, #0x620]
    // 0xa38f20: StoreField: r0->field_13 = r16
    //     0xa38f20: stur            w16, [x0, #0x13]
    // 0xa38f24: r16 = "Pilih menu \"Transfer rekening BNI\"."
    //     0xa38f24: add             x16, PP, #0x30, lsl #12  ; [pp+0x30690] "Pilih menu \"Transfer rekening BNI\"."
    //     0xa38f28: ldr             x16, [x16, #0x690]
    // 0xa38f2c: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38f2c: stur            w16, [x0, #0x17]
    // 0xa38f30: r16 = "Masukkan nomor rekening tujuan dengan 16 digit Nomor Virtual Account (Contoh: ****************)."
    //     0xa38f30: add             x16, PP, #0x30, lsl #12  ; [pp+0x30698] "Masukkan nomor rekening tujuan dengan 16 digit Nomor Virtual Account (Contoh: ****************)."
    //     0xa38f34: ldr             x16, [x16, #0x698]
    // 0xa38f38: StoreField: r0->field_1b = r16
    //     0xa38f38: stur            w16, [x0, #0x1b]
    // 0xa38f3c: r16 = "Masukkan nominal transfer sesuai tagihan. Nominal yang berbeda tidak dapat diproses."
    //     0xa38f3c: add             x16, PP, #0x30, lsl #12  ; [pp+0x306a0] "Masukkan nominal transfer sesuai tagihan. Nominal yang berbeda tidak dapat diproses."
    //     0xa38f40: ldr             x16, [x16, #0x6a0]
    // 0xa38f44: StoreField: r0->field_1f = r16
    //     0xa38f44: stur            w16, [x0, #0x1f]
    // 0xa38f48: r16 = "Pilih \"Proses\", kemudian \"Setuju\"."
    //     0xa38f48: add             x16, PP, #0x30, lsl #12  ; [pp+0x306a8] "Pilih \"Proses\", kemudian \"Setuju\"."
    //     0xa38f4c: ldr             x16, [x16, #0x6a8]
    // 0xa38f50: StoreField: r0->field_23 = r16
    //     0xa38f50: stur            w16, [x0, #0x23]
    // 0xa38f54: r16 = "Balas sms dengan mengetik pin sesuai dengan instruksi BNI. Anda akan menerima notif bahwa transaksi berhasil."
    //     0xa38f54: add             x16, PP, #0x30, lsl #12  ; [pp+0x306b0] "Balas sms dengan mengetik pin sesuai dengan instruksi BNI. Anda akan menerima notif bahwa transaksi berhasil."
    //     0xa38f58: ldr             x16, [x16, #0x6b0]
    // 0xa38f5c: StoreField: r0->field_27 = r16
    //     0xa38f5c: stur            w16, [x0, #0x27]
    // 0xa38f60: r16 = "Atau dapat juga langsung mengetik sms dengan format:\nTRF[SPASI]NomorVA[SPASI]NOMINAL\ndan kemudian kirim ke 3346.\nContoh: TRF **************** 50000"
    //     0xa38f60: add             x16, PP, #0x30, lsl #12  ; [pp+0x306b8] "Atau dapat juga langsung mengetik sms dengan format:\nTRF[SPASI]NomorVA[SPASI]NOMINAL\ndan kemudian kirim ke 3346.\nContoh: TRF **************** 50000"
    //     0xa38f64: ldr             x16, [x16, #0x6b8]
    // 0xa38f68: StoreField: r0->field_2b = r16
    //     0xa38f68: stur            w16, [x0, #0x2b]
    // 0xa38f6c: r1 = <String>
    //     0xa38f6c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38f70: r0 = AllocateGrowableArray()
    //     0xa38f70: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa38f74: mov             x1, x0
    // 0xa38f78: ldur            x0, [fp, #-0x20]
    // 0xa38f7c: stur            x1, [fp, #-0x28]
    // 0xa38f80: StoreField: r1->field_f = r0
    //     0xa38f80: stur            w0, [x1, #0xf]
    // 0xa38f84: r4 = 16
    //     0xa38f84: movz            x4, #0x10
    // 0xa38f88: StoreField: r1->field_b = r4
    //     0xa38f88: stur            w4, [x1, #0xb]
    // 0xa38f8c: r0 = Instruction()
    //     0xa38f8c: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa38f90: mov             x3, x0
    // 0xa38f94: r0 = "Melalui SMS Banking"
    //     0xa38f94: add             x0, PP, #0x30, lsl #12  ; [pp+0x306c0] "Melalui SMS Banking"
    //     0xa38f98: ldr             x0, [x0, #0x6c0]
    // 0xa38f9c: stur            x3, [fp, #-0x20]
    // 0xa38fa0: StoreField: r3->field_7 = r0
    //     0xa38fa0: stur            w0, [x3, #7]
    // 0xa38fa4: ldur            x0, [fp, #-0x28]
    // 0xa38fa8: StoreField: r3->field_b = r0
    //     0xa38fa8: stur            w0, [x3, #0xb]
    // 0xa38fac: r1 = Null
    //     0xa38fac: mov             x1, NULL
    // 0xa38fb0: r2 = 10
    //     0xa38fb0: movz            x2, #0xa
    // 0xa38fb4: r0 = AllocateArray()
    //     0xa38fb4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa38fb8: stur            x0, [fp, #-0x28]
    // 0xa38fbc: r16 = "Kunjungi Kantor Cabang/Outlet BNI terdekat."
    //     0xa38fbc: add             x16, PP, #0x30, lsl #12  ; [pp+0x306c8] "Kunjungi Kantor Cabang/Outlet BNI terdekat."
    //     0xa38fc0: ldr             x16, [x16, #0x6c8]
    // 0xa38fc4: StoreField: r0->field_f = r16
    //     0xa38fc4: stur            w16, [x0, #0xf]
    // 0xa38fc8: r16 = "Informasikan kepada Teller, bahwa Anda ingin melakukan pembayaran \"Virtual Account Billing\"."
    //     0xa38fc8: add             x16, PP, #0x30, lsl #12  ; [pp+0x306d0] "Informasikan kepada Teller, bahwa Anda ingin melakukan pembayaran \"Virtual Account Billing\"."
    //     0xa38fcc: ldr             x16, [x16, #0x6d0]
    // 0xa38fd0: StoreField: r0->field_13 = r16
    //     0xa38fd0: stur            w16, [x0, #0x13]
    // 0xa38fd4: r16 = "Serahkan nomor Virtual Account Anda kepada Teller."
    //     0xa38fd4: add             x16, PP, #0x30, lsl #12  ; [pp+0x306d8] "Serahkan nomor Virtual Account Anda kepada Teller."
    //     0xa38fd8: ldr             x16, [x16, #0x6d8]
    // 0xa38fdc: ArrayStore: r0[0] = r16  ; List_4
    //     0xa38fdc: stur            w16, [x0, #0x17]
    // 0xa38fe0: r16 = "Teller akan melakukan konfirmasi kepada Anda dan akan memproses Transaksi."
    //     0xa38fe0: add             x16, PP, #0x30, lsl #12  ; [pp+0x306e0] "Teller akan melakukan konfirmasi kepada Anda dan akan memproses Transaksi."
    //     0xa38fe4: ldr             x16, [x16, #0x6e0]
    // 0xa38fe8: StoreField: r0->field_1b = r16
    //     0xa38fe8: stur            w16, [x0, #0x1b]
    // 0xa38fec: r16 = "Apabila transaksi Sukses, Anda akan menerima bukti pembayaran dari Teller tersebut."
    //     0xa38fec: add             x16, PP, #0x30, lsl #12  ; [pp+0x306e8] "Apabila transaksi Sukses, Anda akan menerima bukti pembayaran dari Teller tersebut."
    //     0xa38ff0: ldr             x16, [x16, #0x6e8]
    // 0xa38ff4: StoreField: r0->field_1f = r16
    //     0xa38ff4: stur            w16, [x0, #0x1f]
    // 0xa38ff8: r1 = <String>
    //     0xa38ff8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa38ffc: r0 = AllocateGrowableArray()
    //     0xa38ffc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39000: mov             x1, x0
    // 0xa39004: ldur            x0, [fp, #-0x28]
    // 0xa39008: stur            x1, [fp, #-0x30]
    // 0xa3900c: StoreField: r1->field_f = r0
    //     0xa3900c: stur            w0, [x1, #0xf]
    // 0xa39010: r0 = 10
    //     0xa39010: movz            x0, #0xa
    // 0xa39014: StoreField: r1->field_b = r0
    //     0xa39014: stur            w0, [x1, #0xb]
    // 0xa39018: r0 = Instruction()
    //     0xa39018: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa3901c: mov             x3, x0
    // 0xa39020: r0 = "Melalui Cabang atau Outlet BNI (Teller)"
    //     0xa39020: add             x0, PP, #0x30, lsl #12  ; [pp+0x306f0] "Melalui Cabang atau Outlet BNI (Teller)"
    //     0xa39024: ldr             x0, [x0, #0x6f0]
    // 0xa39028: stur            x3, [fp, #-0x28]
    // 0xa3902c: StoreField: r3->field_7 = r0
    //     0xa3902c: stur            w0, [x3, #7]
    // 0xa39030: ldur            x0, [fp, #-0x30]
    // 0xa39034: StoreField: r3->field_b = r0
    //     0xa39034: stur            w0, [x3, #0xb]
    // 0xa39038: r1 = Null
    //     0xa39038: mov             x1, NULL
    // 0xa3903c: r2 = 12
    //     0xa3903c: movz            x2, #0xc
    // 0xa39040: r0 = AllocateArray()
    //     0xa39040: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa39044: stur            x0, [fp, #-0x30]
    // 0xa39048: r16 = "Kunjungi Agen46 terdekat (warung/took/kios dengan tulisan Agen46)."
    //     0xa39048: add             x16, PP, #0x30, lsl #12  ; [pp+0x306f8] "Kunjungi Agen46 terdekat (warung/took/kios dengan tulisan Agen46)."
    //     0xa3904c: ldr             x16, [x16, #0x6f8]
    // 0xa39050: StoreField: r0->field_f = r16
    //     0xa39050: stur            w16, [x0, #0xf]
    // 0xa39054: r16 = "Informasikan kepada Agen46, bahwa ingin melakukan pembayaran \"Virtual\"."
    //     0xa39054: add             x16, PP, #0x30, lsl #12  ; [pp+0x30700] "Informasikan kepada Agen46, bahwa ingin melakukan pembayaran \"Virtual\"."
    //     0xa39058: ldr             x16, [x16, #0x700]
    // 0xa3905c: StoreField: r0->field_13 = r16
    //     0xa3905c: stur            w16, [x0, #0x13]
    // 0xa39060: r16 = "Serahkan nomor Virtual Account Anda kepada Agen46."
    //     0xa39060: add             x16, PP, #0x30, lsl #12  ; [pp+0x30708] "Serahkan nomor Virtual Account Anda kepada Agen46."
    //     0xa39064: ldr             x16, [x16, #0x708]
    // 0xa39068: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39068: stur            w16, [x0, #0x17]
    // 0xa3906c: r16 = "Agen46 akan melakukan konfirmasi kepada Anda."
    //     0xa3906c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30710] "Agen46 akan melakukan konfirmasi kepada Anda."
    //     0xa39070: ldr             x16, [x16, #0x710]
    // 0xa39074: StoreField: r0->field_1b = r16
    //     0xa39074: stur            w16, [x0, #0x1b]
    // 0xa39078: r16 = "Selanjutnya, transaksi akan diproses."
    //     0xa39078: add             x16, PP, #0x30, lsl #12  ; [pp+0x30718] "Selanjutnya, transaksi akan diproses."
    //     0xa3907c: ldr             x16, [x16, #0x718]
    // 0xa39080: StoreField: r0->field_1f = r16
    //     0xa39080: stur            w16, [x0, #0x1f]
    // 0xa39084: r16 = "Apabila transaksi dinyatakan sukses, Anda akan menerima bukti pembayaran dari Agen46."
    //     0xa39084: add             x16, PP, #0x30, lsl #12  ; [pp+0x30720] "Apabila transaksi dinyatakan sukses, Anda akan menerima bukti pembayaran dari Agen46."
    //     0xa39088: ldr             x16, [x16, #0x720]
    // 0xa3908c: StoreField: r0->field_23 = r16
    //     0xa3908c: stur            w16, [x0, #0x23]
    // 0xa39090: r1 = <String>
    //     0xa39090: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39094: r0 = AllocateGrowableArray()
    //     0xa39094: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39098: mov             x1, x0
    // 0xa3909c: ldur            x0, [fp, #-0x30]
    // 0xa390a0: stur            x1, [fp, #-0x38]
    // 0xa390a4: StoreField: r1->field_f = r0
    //     0xa390a4: stur            w0, [x1, #0xf]
    // 0xa390a8: r2 = 12
    //     0xa390a8: movz            x2, #0xc
    // 0xa390ac: StoreField: r1->field_b = r2
    //     0xa390ac: stur            w2, [x1, #0xb]
    // 0xa390b0: r0 = Instruction()
    //     0xa390b0: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa390b4: mov             x3, x0
    // 0xa390b8: r0 = "Melalui Agen46"
    //     0xa390b8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30728] "Melalui Agen46"
    //     0xa390bc: ldr             x0, [x0, #0x728]
    // 0xa390c0: stur            x3, [fp, #-0x30]
    // 0xa390c4: StoreField: r3->field_7 = r0
    //     0xa390c4: stur            w0, [x3, #7]
    // 0xa390c8: ldur            x0, [fp, #-0x38]
    // 0xa390cc: StoreField: r3->field_b = r0
    //     0xa390cc: stur            w0, [x3, #0xb]
    // 0xa390d0: r1 = Null
    //     0xa390d0: mov             x1, NULL
    // 0xa390d4: r2 = 12
    //     0xa390d4: movz            x2, #0xc
    // 0xa390d8: r0 = AllocateArray()
    //     0xa390d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa390dc: mov             x2, x0
    // 0xa390e0: ldur            x0, [fp, #-8]
    // 0xa390e4: stur            x2, [fp, #-0x38]
    // 0xa390e8: StoreField: r2->field_f = r0
    //     0xa390e8: stur            w0, [x2, #0xf]
    // 0xa390ec: ldur            x0, [fp, #-0x10]
    // 0xa390f0: StoreField: r2->field_13 = r0
    //     0xa390f0: stur            w0, [x2, #0x13]
    // 0xa390f4: ldur            x0, [fp, #-0x18]
    // 0xa390f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa390f8: stur            w0, [x2, #0x17]
    // 0xa390fc: ldur            x0, [fp, #-0x20]
    // 0xa39100: StoreField: r2->field_1b = r0
    //     0xa39100: stur            w0, [x2, #0x1b]
    // 0xa39104: ldur            x0, [fp, #-0x28]
    // 0xa39108: StoreField: r2->field_1f = r0
    //     0xa39108: stur            w0, [x2, #0x1f]
    // 0xa3910c: ldur            x0, [fp, #-0x30]
    // 0xa39110: StoreField: r2->field_23 = r0
    //     0xa39110: stur            w0, [x2, #0x23]
    // 0xa39114: r1 = <Instruction>
    //     0xa39114: add             x1, PP, #0x30, lsl #12  ; [pp+0x305b8] TypeArguments: <Instruction>
    //     0xa39118: ldr             x1, [x1, #0x5b8]
    // 0xa3911c: r0 = AllocateGrowableArray()
    //     0xa3911c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39120: mov             x1, x0
    // 0xa39124: ldur            x0, [fp, #-0x38]
    // 0xa39128: StoreField: r1->field_f = r0
    //     0xa39128: stur            w0, [x1, #0xf]
    // 0xa3912c: r0 = 12
    //     0xa3912c: movz            x0, #0xc
    // 0xa39130: StoreField: r1->field_b = r0
    //     0xa39130: stur            w0, [x1, #0xb]
    // 0xa39134: mov             x0, x1
    // 0xa39138: LeaveFrame
    //     0xa39138: mov             SP, fp
    //     0xa3913c: ldp             fp, lr, [SP], #0x10
    // 0xa39140: ret
    //     0xa39140: ret             
    // 0xa39144: r4 = 16
    //     0xa39144: movz            x4, #0x10
    // 0xa39148: r3 = 18
    //     0xa39148: movz            x3, #0x12
    // 0xa3914c: cmp             x0, #2
    // 0xa39150: b.gt            #0xa393f0
    // 0xa39154: mov             x2, x3
    // 0xa39158: r1 = <String>
    //     0xa39158: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa3915c: r0 = AllocateArray()
    //     0xa3915c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa39160: stur            x0, [fp, #-8]
    // 0xa39164: r16 = "Masukkan Kartu Anda."
    //     0xa39164: add             x16, PP, #0x30, lsl #12  ; [pp+0x305c0] "Masukkan Kartu Anda."
    //     0xa39168: ldr             x16, [x16, #0x5c0]
    // 0xa3916c: StoreField: r0->field_f = r16
    //     0xa3916c: stur            w16, [x0, #0xf]
    // 0xa39170: r16 = "Pilih Bahasa."
    //     0xa39170: add             x16, PP, #0x30, lsl #12  ; [pp+0x305c8] "Pilih Bahasa."
    //     0xa39174: ldr             x16, [x16, #0x5c8]
    // 0xa39178: StoreField: r0->field_13 = r16
    //     0xa39178: stur            w16, [x0, #0x13]
    // 0xa3917c: r16 = "Masukkan PIN ATM Anda."
    //     0xa3917c: add             x16, PP, #0x30, lsl #12  ; [pp+0x305d0] "Masukkan PIN ATM Anda."
    //     0xa39180: ldr             x16, [x16, #0x5d0]
    // 0xa39184: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39184: stur            w16, [x0, #0x17]
    // 0xa39188: r16 = "Kemudian, pilih \"Menu Pembayaran\"."
    //     0xa39188: add             x16, PP, #0x30, lsl #12  ; [pp+0x30730] "Kemudian, pilih \"Menu Pembayaran\"."
    //     0xa3918c: ldr             x16, [x16, #0x730]
    // 0xa39190: StoreField: r0->field_1b = r16
    //     0xa39190: stur            w16, [x0, #0x1b]
    // 0xa39194: r16 = "Pilih \"Institusi\"."
    //     0xa39194: add             x16, PP, #0x30, lsl #12  ; [pp+0x30738] "Pilih \"Institusi\"."
    //     0xa39198: ldr             x16, [x16, #0x738]
    // 0xa3919c: StoreField: r0->field_1f = r16
    //     0xa3919c: stur            w16, [x0, #0x1f]
    // 0xa391a0: r16 = "Masukkan kode biller dan nomor pembayaran (VA) (Contoh: 3061168026080374)."
    //     0xa391a0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30740] "Masukkan kode biller dan nomor pembayaran (VA) (Contoh: 3061168026080374)."
    //     0xa391a4: ldr             x16, [x16, #0x740]
    // 0xa391a8: StoreField: r0->field_23 = r16
    //     0xa391a8: stur            w16, [x0, #0x23]
    // 0xa391ac: r16 = "Validasi data tagihan."
    //     0xa391ac: add             x16, PP, #0x30, lsl #12  ; [pp+0x30748] "Validasi data tagihan."
    //     0xa391b0: ldr             x16, [x16, #0x748]
    // 0xa391b4: StoreField: r0->field_27 = r16
    //     0xa391b4: stur            w16, [x0, #0x27]
    // 0xa391b8: r16 = "Tekan \"Benar\", apabila telah sesuai."
    //     0xa391b8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30750] "Tekan \"Benar\", apabila telah sesuai."
    //     0xa391bc: ldr             x16, [x16, #0x750]
    // 0xa391c0: StoreField: r0->field_2b = r16
    //     0xa391c0: stur            w16, [x0, #0x2b]
    // 0xa391c4: r16 = "Transaksi Anda telah selesai."
    //     0xa391c4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30600] "Transaksi Anda telah selesai."
    //     0xa391c8: ldr             x16, [x16, #0x600]
    // 0xa391cc: StoreField: r0->field_2f = r16
    //     0xa391cc: stur            w16, [x0, #0x2f]
    // 0xa391d0: r1 = <String>
    //     0xa391d0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa391d4: r0 = AllocateGrowableArray()
    //     0xa391d4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa391d8: mov             x1, x0
    // 0xa391dc: ldur            x0, [fp, #-8]
    // 0xa391e0: stur            x1, [fp, #-0x10]
    // 0xa391e4: StoreField: r1->field_f = r0
    //     0xa391e4: stur            w0, [x1, #0xf]
    // 0xa391e8: r2 = 18
    //     0xa391e8: movz            x2, #0x12
    // 0xa391ec: StoreField: r1->field_b = r2
    //     0xa391ec: stur            w2, [x1, #0xb]
    // 0xa391f0: r0 = Instruction()
    //     0xa391f0: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa391f4: mov             x3, x0
    // 0xa391f8: r0 = "Melalui ATM BSI"
    //     0xa391f8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30758] "Melalui ATM BSI"
    //     0xa391fc: ldr             x0, [x0, #0x758]
    // 0xa39200: stur            x3, [fp, #-8]
    // 0xa39204: StoreField: r3->field_7 = r0
    //     0xa39204: stur            w0, [x3, #7]
    // 0xa39208: ldur            x0, [fp, #-0x10]
    // 0xa3920c: StoreField: r3->field_b = r0
    //     0xa3920c: stur            w0, [x3, #0xb]
    // 0xa39210: r1 = <String>
    //     0xa39210: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39214: r2 = 18
    //     0xa39214: movz            x2, #0x12
    // 0xa39218: r0 = AllocateArray()
    //     0xa39218: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3921c: stur            x0, [fp, #-0x10]
    // 0xa39220: r16 = "Akses BSI Mobile melalui handphone."
    //     0xa39220: add             x16, PP, #0x30, lsl #12  ; [pp+0x30760] "Akses BSI Mobile melalui handphone."
    //     0xa39224: ldr             x16, [x16, #0x760]
    // 0xa39228: StoreField: r0->field_f = r16
    //     0xa39228: stur            w16, [x0, #0xf]
    // 0xa3922c: r16 = "Pilih menu \"Pembayaran\"."
    //     0xa3922c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30768] "Pilih menu \"Pembayaran\"."
    //     0xa39230: ldr             x16, [x16, #0x768]
    // 0xa39234: StoreField: r0->field_13 = r16
    //     0xa39234: stur            w16, [x0, #0x13]
    // 0xa39238: r16 = "Pilih \"Institusi\"."
    //     0xa39238: add             x16, PP, #0x30, lsl #12  ; [pp+0x30738] "Pilih \"Institusi\"."
    //     0xa3923c: ldr             x16, [x16, #0x738]
    // 0xa39240: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39240: stur            w16, [x0, #0x17]
    // 0xa39244: r16 = "Masukkan Kode/Nama Institusi : \"3061 - NU ONLINE DONASI / 3062 - NU ONLINE ZAKAT\"."
    //     0xa39244: add             x16, PP, #0x30, lsl #12  ; [pp+0x30770] "Masukkan Kode/Nama Institusi : \"3061 - NU ONLINE DONASI / 3062 - NU ONLINE ZAKAT\"."
    //     0xa39248: ldr             x16, [x16, #0x770]
    // 0xa3924c: StoreField: r0->field_1b = r16
    //     0xa3924c: stur            w16, [x0, #0x1b]
    // 0xa39250: r16 = "Masukkan Nomor Pembayaran (VA) (Contoh: 168026080374)."
    //     0xa39250: add             x16, PP, #0x30, lsl #12  ; [pp+0x30778] "Masukkan Nomor Pembayaran (VA) (Contoh: 168026080374)."
    //     0xa39254: ldr             x16, [x16, #0x778]
    // 0xa39258: StoreField: r0->field_1f = r16
    //     0xa39258: stur            w16, [x0, #0x1f]
    // 0xa3925c: r16 = "Tekan tombol \"Selanjutnya\"."
    //     0xa3925c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30780] "Tekan tombol \"Selanjutnya\"."
    //     0xa39260: ldr             x16, [x16, #0x780]
    // 0xa39264: StoreField: r0->field_23 = r16
    //     0xa39264: stur            w16, [x0, #0x23]
    // 0xa39268: r16 = "Masukan PIN."
    //     0xa39268: add             x16, PP, #0x30, lsl #12  ; [pp+0x30788] "Masukan PIN."
    //     0xa3926c: ldr             x16, [x16, #0x788]
    // 0xa39270: StoreField: r0->field_27 = r16
    //     0xa39270: stur            w16, [x0, #0x27]
    // 0xa39274: r16 = "Tekan tombol \"Submit\"."
    //     0xa39274: add             x16, PP, #0x30, lsl #12  ; [pp+0x30790] "Tekan tombol \"Submit\"."
    //     0xa39278: ldr             x16, [x16, #0x790]
    // 0xa3927c: StoreField: r0->field_2b = r16
    //     0xa3927c: stur            w16, [x0, #0x2b]
    // 0xa39280: r16 = "Transaksi Anda telah selesai."
    //     0xa39280: add             x16, PP, #0x30, lsl #12  ; [pp+0x30600] "Transaksi Anda telah selesai."
    //     0xa39284: ldr             x16, [x16, #0x600]
    // 0xa39288: StoreField: r0->field_2f = r16
    //     0xa39288: stur            w16, [x0, #0x2f]
    // 0xa3928c: r1 = <String>
    //     0xa3928c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39290: r0 = AllocateGrowableArray()
    //     0xa39290: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39294: mov             x1, x0
    // 0xa39298: ldur            x0, [fp, #-0x10]
    // 0xa3929c: stur            x1, [fp, #-0x18]
    // 0xa392a0: StoreField: r1->field_f = r0
    //     0xa392a0: stur            w0, [x1, #0xf]
    // 0xa392a4: r0 = 18
    //     0xa392a4: movz            x0, #0x12
    // 0xa392a8: StoreField: r1->field_b = r0
    //     0xa392a8: stur            w0, [x1, #0xb]
    // 0xa392ac: r0 = Instruction()
    //     0xa392ac: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa392b0: mov             x3, x0
    // 0xa392b4: r0 = "Melalui Mobile Banking BSI"
    //     0xa392b4: add             x0, PP, #0x30, lsl #12  ; [pp+0x30798] "Melalui Mobile Banking BSI"
    //     0xa392b8: ldr             x0, [x0, #0x798]
    // 0xa392bc: stur            x3, [fp, #-0x10]
    // 0xa392c0: StoreField: r3->field_7 = r0
    //     0xa392c0: stur            w0, [x3, #7]
    // 0xa392c4: ldur            x0, [fp, #-0x18]
    // 0xa392c8: StoreField: r3->field_b = r0
    //     0xa392c8: stur            w0, [x3, #0xb]
    // 0xa392cc: r1 = <String>
    //     0xa392cc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa392d0: r2 = 20
    //     0xa392d0: movz            x2, #0x14
    // 0xa392d4: r0 = AllocateArray()
    //     0xa392d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa392d8: stur            x0, [fp, #-0x18]
    // 0xa392dc: r16 = "Pilih menu \"Pembayaran\"."
    //     0xa392dc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30768] "Pilih menu \"Pembayaran\"."
    //     0xa392e0: ldr             x16, [x16, #0x768]
    // 0xa392e4: StoreField: r0->field_f = r16
    //     0xa392e4: stur            w16, [x0, #0xf]
    // 0xa392e8: r16 = "Pilih Nomor Rekening BSI Anda."
    //     0xa392e8: add             x16, PP, #0x30, lsl #12  ; [pp+0x307a0] "Pilih Nomor Rekening BSI Anda."
    //     0xa392ec: ldr             x16, [x16, #0x7a0]
    // 0xa392f0: StoreField: r0->field_13 = r16
    //     0xa392f0: stur            w16, [x0, #0x13]
    // 0xa392f4: r16 = "Pilih Jenis Pembayaran \"Institusi\"."
    //     0xa392f4: add             x16, PP, #0x30, lsl #12  ; [pp+0x307a8] "Pilih Jenis Pembayaran \"Institusi\"."
    //     0xa392f8: ldr             x16, [x16, #0x7a8]
    // 0xa392fc: ArrayStore: r0[0] = r16  ; List_4
    //     0xa392fc: stur            w16, [x0, #0x17]
    // 0xa39300: r16 = "Pilih Nama Lembaga \"3061 - NU ONLINE DONASI / 3062 - NU ONLINE ZAKAT\"."
    //     0xa39300: add             x16, PP, #0x30, lsl #12  ; [pp+0x307b0] "Pilih Nama Lembaga \"3061 - NU ONLINE DONASI / 3062 - NU ONLINE ZAKAT\"."
    //     0xa39304: ldr             x16, [x16, #0x7b0]
    // 0xa39308: StoreField: r0->field_1b = r16
    //     0xa39308: stur            w16, [x0, #0x1b]
    // 0xa3930c: r16 = "Masukkan Nomor Pembayaran (VA) (Contoh: 168026080374)."
    //     0xa3930c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30778] "Masukkan Nomor Pembayaran (VA) (Contoh: 168026080374)."
    //     0xa39310: ldr             x16, [x16, #0x778]
    // 0xa39314: StoreField: r0->field_1f = r16
    //     0xa39314: stur            w16, [x0, #0x1f]
    // 0xa39318: r16 = "Tekan tombol \"Verifikasi\"."
    //     0xa39318: add             x16, PP, #0x30, lsl #12  ; [pp+0x307b8] "Tekan tombol \"Verifikasi\"."
    //     0xa3931c: ldr             x16, [x16, #0x7b8]
    // 0xa39320: StoreField: r0->field_23 = r16
    //     0xa39320: stur            w16, [x0, #0x23]
    // 0xa39324: r16 = "Baca dengan seksama detail tagihan."
    //     0xa39324: add             x16, PP, #0x30, lsl #12  ; [pp+0x307c0] "Baca dengan seksama detail tagihan."
    //     0xa39328: ldr             x16, [x16, #0x7c0]
    // 0xa3932c: StoreField: r0->field_27 = r16
    //     0xa3932c: stur            w16, [x0, #0x27]
    // 0xa39330: r16 = "Masukkan TAN, PIN Otorisasi."
    //     0xa39330: add             x16, PP, #0x30, lsl #12  ; [pp+0x307c8] "Masukkan TAN, PIN Otorisasi."
    //     0xa39334: ldr             x16, [x16, #0x7c8]
    // 0xa39338: StoreField: r0->field_2b = r16
    //     0xa39338: stur            w16, [x0, #0x2b]
    // 0xa3933c: r16 = "Tekan tombol \"Submit\"."
    //     0xa3933c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30790] "Tekan tombol \"Submit\"."
    //     0xa39340: ldr             x16, [x16, #0x790]
    // 0xa39344: StoreField: r0->field_2f = r16
    //     0xa39344: stur            w16, [x0, #0x2f]
    // 0xa39348: r16 = "Transaksi Anda telah selesai."
    //     0xa39348: add             x16, PP, #0x30, lsl #12  ; [pp+0x30600] "Transaksi Anda telah selesai."
    //     0xa3934c: ldr             x16, [x16, #0x600]
    // 0xa39350: StoreField: r0->field_33 = r16
    //     0xa39350: stur            w16, [x0, #0x33]
    // 0xa39354: r1 = <String>
    //     0xa39354: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39358: r0 = AllocateGrowableArray()
    //     0xa39358: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3935c: mov             x1, x0
    // 0xa39360: ldur            x0, [fp, #-0x18]
    // 0xa39364: stur            x1, [fp, #-0x20]
    // 0xa39368: StoreField: r1->field_f = r0
    //     0xa39368: stur            w0, [x1, #0xf]
    // 0xa3936c: r0 = 20
    //     0xa3936c: movz            x0, #0x14
    // 0xa39370: StoreField: r1->field_b = r0
    //     0xa39370: stur            w0, [x1, #0xb]
    // 0xa39374: r0 = Instruction()
    //     0xa39374: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa39378: mov             x3, x0
    // 0xa3937c: r0 = "Melalui NetBanking BSI"
    //     0xa3937c: add             x0, PP, #0x30, lsl #12  ; [pp+0x307d0] "Melalui NetBanking BSI"
    //     0xa39380: ldr             x0, [x0, #0x7d0]
    // 0xa39384: stur            x3, [fp, #-0x18]
    // 0xa39388: StoreField: r3->field_7 = r0
    //     0xa39388: stur            w0, [x3, #7]
    // 0xa3938c: ldur            x0, [fp, #-0x20]
    // 0xa39390: StoreField: r3->field_b = r0
    //     0xa39390: stur            w0, [x3, #0xb]
    // 0xa39394: r1 = Null
    //     0xa39394: mov             x1, NULL
    // 0xa39398: r2 = 6
    //     0xa39398: movz            x2, #0x6
    // 0xa3939c: r0 = AllocateArray()
    //     0xa3939c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa393a0: mov             x2, x0
    // 0xa393a4: ldur            x0, [fp, #-8]
    // 0xa393a8: stur            x2, [fp, #-0x20]
    // 0xa393ac: StoreField: r2->field_f = r0
    //     0xa393ac: stur            w0, [x2, #0xf]
    // 0xa393b0: ldur            x0, [fp, #-0x10]
    // 0xa393b4: StoreField: r2->field_13 = r0
    //     0xa393b4: stur            w0, [x2, #0x13]
    // 0xa393b8: ldur            x0, [fp, #-0x18]
    // 0xa393bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa393bc: stur            w0, [x2, #0x17]
    // 0xa393c0: r1 = <Instruction>
    //     0xa393c0: add             x1, PP, #0x30, lsl #12  ; [pp+0x305b8] TypeArguments: <Instruction>
    //     0xa393c4: ldr             x1, [x1, #0x5b8]
    // 0xa393c8: r0 = AllocateGrowableArray()
    //     0xa393c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa393cc: mov             x1, x0
    // 0xa393d0: ldur            x0, [fp, #-0x20]
    // 0xa393d4: StoreField: r1->field_f = r0
    //     0xa393d4: stur            w0, [x1, #0xf]
    // 0xa393d8: r0 = 6
    //     0xa393d8: movz            x0, #0x6
    // 0xa393dc: StoreField: r1->field_b = r0
    //     0xa393dc: stur            w0, [x1, #0xb]
    // 0xa393e0: mov             x0, x1
    // 0xa393e4: LeaveFrame
    //     0xa393e4: mov             SP, fp
    //     0xa393e8: ldp             fp, lr, [SP], #0x10
    // 0xa393ec: ret
    //     0xa393ec: ret             
    // 0xa393f0: r0 = 20
    //     0xa393f0: movz            x0, #0x14
    // 0xa393f4: mov             x2, x0
    // 0xa393f8: r1 = <String>
    //     0xa393f8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa393fc: r0 = AllocateArray()
    //     0xa393fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa39400: stur            x0, [fp, #-8]
    // 0xa39404: r16 = "Lakukan pembayaran melalui ATM Bank BRI."
    //     0xa39404: add             x16, PP, #0x30, lsl #12  ; [pp+0x307d8] "Lakukan pembayaran melalui ATM Bank BRI."
    //     0xa39408: ldr             x16, [x16, #0x7d8]
    // 0xa3940c: StoreField: r0->field_f = r16
    //     0xa3940c: stur            w16, [x0, #0xf]
    // 0xa39410: r16 = "Pilih menu \"Transaksi Lain\""
    //     0xa39410: add             x16, PP, #0x30, lsl #12  ; [pp+0x307e0] "Pilih menu \"Transaksi Lain\""
    //     0xa39414: ldr             x16, [x16, #0x7e0]
    // 0xa39418: StoreField: r0->field_13 = r16
    //     0xa39418: stur            w16, [x0, #0x13]
    // 0xa3941c: r16 = "Pilih menu \"Pembayaran\"."
    //     0xa3941c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30768] "Pilih menu \"Pembayaran\"."
    //     0xa39420: ldr             x16, [x16, #0x768]
    // 0xa39424: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39424: stur            w16, [x0, #0x17]
    // 0xa39428: r16 = "Pilih menu \"Lainnya\"."
    //     0xa39428: add             x16, PP, #0x30, lsl #12  ; [pp+0x307e8] "Pilih menu \"Lainnya\"."
    //     0xa3942c: ldr             x16, [x16, #0x7e8]
    // 0xa39430: StoreField: r0->field_1b = r16
    //     0xa39430: stur            w16, [x0, #0x1b]
    // 0xa39434: r16 = "Pilih menu \"BRIVA\"."
    //     0xa39434: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f0] "Pilih menu \"BRIVA\"."
    //     0xa39438: ldr             x16, [x16, #0x7f0]
    // 0xa3943c: StoreField: r0->field_1f = r16
    //     0xa3943c: stur            w16, [x0, #0x1f]
    // 0xa39440: r16 = "Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa39440: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f8] "Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa39444: ldr             x16, [x16, #0x7f8]
    // 0xa39448: StoreField: r0->field_23 = r16
    //     0xa39448: stur            w16, [x0, #0x23]
    // 0xa3944c: r16 = "Pilih \"Benar\" untuk memproses pembayaran."
    //     0xa3944c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30800] "Pilih \"Benar\" untuk memproses pembayaran."
    //     0xa39450: ldr             x16, [x16, #0x800]
    // 0xa39454: StoreField: r0->field_27 = r16
    //     0xa39454: stur            w16, [x0, #0x27]
    // 0xa39458: r16 = "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa39458: add             x16, PP, #0x30, lsl #12  ; [pp+0x305f0] "Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi."
    //     0xa3945c: ldr             x16, [x16, #0x5f0]
    // 0xa39460: StoreField: r0->field_2b = r16
    //     0xa39460: stur            w16, [x0, #0x2b]
    // 0xa39464: r16 = "Pilih \"Ya\" untuk konfirmasi proses pembayaran."
    //     0xa39464: add             x16, PP, #0x30, lsl #12  ; [pp+0x30808] "Pilih \"Ya\" untuk konfirmasi proses pembayaran."
    //     0xa39468: ldr             x16, [x16, #0x808]
    // 0xa3946c: StoreField: r0->field_2f = r16
    //     0xa3946c: stur            w16, [x0, #0x2f]
    // 0xa39470: r16 = "Transaksi Anda telah selesai."
    //     0xa39470: add             x16, PP, #0x30, lsl #12  ; [pp+0x30600] "Transaksi Anda telah selesai."
    //     0xa39474: ldr             x16, [x16, #0x600]
    // 0xa39478: StoreField: r0->field_33 = r16
    //     0xa39478: stur            w16, [x0, #0x33]
    // 0xa3947c: r1 = <String>
    //     0xa3947c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39480: r0 = AllocateGrowableArray()
    //     0xa39480: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39484: mov             x1, x0
    // 0xa39488: ldur            x0, [fp, #-8]
    // 0xa3948c: stur            x1, [fp, #-0x10]
    // 0xa39490: StoreField: r1->field_f = r0
    //     0xa39490: stur            w0, [x1, #0xf]
    // 0xa39494: r0 = 20
    //     0xa39494: movz            x0, #0x14
    // 0xa39498: StoreField: r1->field_b = r0
    //     0xa39498: stur            w0, [x1, #0xb]
    // 0xa3949c: r0 = Instruction()
    //     0xa3949c: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa394a0: mov             x3, x0
    // 0xa394a4: r0 = "Melalui ATM BRI"
    //     0xa394a4: add             x0, PP, #0x30, lsl #12  ; [pp+0x30810] "Melalui ATM BRI"
    //     0xa394a8: ldr             x0, [x0, #0x810]
    // 0xa394ac: stur            x3, [fp, #-8]
    // 0xa394b0: StoreField: r3->field_7 = r0
    //     0xa394b0: stur            w0, [x3, #7]
    // 0xa394b4: ldur            x0, [fp, #-0x10]
    // 0xa394b8: StoreField: r3->field_b = r0
    //     0xa394b8: stur            w0, [x3, #0xb]
    // 0xa394bc: r1 = Null
    //     0xa394bc: mov             x1, NULL
    // 0xa394c0: r2 = 14
    //     0xa394c0: movz            x2, #0xe
    // 0xa394c4: r0 = AllocateArray()
    //     0xa394c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa394c8: stur            x0, [fp, #-0x10]
    // 0xa394cc: r16 = "Masuk ke aplikasi BRI Mobile dan pilih Mobile Banking BRI"
    //     0xa394cc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30818] "Masuk ke aplikasi BRI Mobile dan pilih Mobile Banking BRI"
    //     0xa394d0: ldr             x16, [x16, #0x818]
    // 0xa394d4: StoreField: r0->field_f = r16
    //     0xa394d4: stur            w16, [x0, #0xf]
    // 0xa394d8: r16 = "Pilih menu \"Info\"."
    //     0xa394d8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30820] "Pilih menu \"Info\"."
    //     0xa394dc: ldr             x16, [x16, #0x820]
    // 0xa394e0: StoreField: r0->field_13 = r16
    //     0xa394e0: stur            w16, [x0, #0x13]
    // 0xa394e4: r16 = "Pilih menu \"BRIVA\"."
    //     0xa394e4: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f0] "Pilih menu \"BRIVA\"."
    //     0xa394e8: ldr             x16, [x16, #0x7f0]
    // 0xa394ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xa394ec: stur            w16, [x0, #0x17]
    // 0xa394f0: r16 = "Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa394f0: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f8] "Masukkan nomor Virtual Account Anda (Contoh: ****************)."
    //     0xa394f4: ldr             x16, [x16, #0x7f8]
    // 0xa394f8: StoreField: r0->field_1b = r16
    //     0xa394f8: stur            w16, [x0, #0x1b]
    // 0xa394fc: r16 = "Masukkan PIN Mobile/SMS Banking BRI."
    //     0xa394fc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30828] "Masukkan PIN Mobile/SMS Banking BRI."
    //     0xa39500: ldr             x16, [x16, #0x828]
    // 0xa39504: StoreField: r0->field_1f = r16
    //     0xa39504: stur            w16, [x0, #0x1f]
    // 0xa39508: r16 = "Pembayaran Anda Telah Berhasil."
    //     0xa39508: add             x16, PP, #0x30, lsl #12  ; [pp+0x30640] "Pembayaran Anda Telah Berhasil."
    //     0xa3950c: ldr             x16, [x16, #0x640]
    // 0xa39510: StoreField: r0->field_23 = r16
    //     0xa39510: stur            w16, [x0, #0x23]
    // 0xa39514: r16 = "Anda akan mendapatkan notifikasi pembayaran melalui SMS"
    //     0xa39514: add             x16, PP, #0x30, lsl #12  ; [pp+0x30830] "Anda akan mendapatkan notifikasi pembayaran melalui SMS"
    //     0xa39518: ldr             x16, [x16, #0x830]
    // 0xa3951c: StoreField: r0->field_27 = r16
    //     0xa3951c: stur            w16, [x0, #0x27]
    // 0xa39520: r1 = <String>
    //     0xa39520: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39524: r0 = AllocateGrowableArray()
    //     0xa39524: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39528: mov             x1, x0
    // 0xa3952c: ldur            x0, [fp, #-0x10]
    // 0xa39530: stur            x1, [fp, #-0x18]
    // 0xa39534: StoreField: r1->field_f = r0
    //     0xa39534: stur            w0, [x1, #0xf]
    // 0xa39538: r0 = 14
    //     0xa39538: movz            x0, #0xe
    // 0xa3953c: StoreField: r1->field_b = r0
    //     0xa3953c: stur            w0, [x1, #0xb]
    // 0xa39540: r0 = Instruction()
    //     0xa39540: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa39544: mov             x3, x0
    // 0xa39548: r0 = "Melalui Mobile Banking BRI"
    //     0xa39548: add             x0, PP, #0x30, lsl #12  ; [pp+0x30838] "Melalui Mobile Banking BRI"
    //     0xa3954c: ldr             x0, [x0, #0x838]
    // 0xa39550: stur            x3, [fp, #-0x10]
    // 0xa39554: StoreField: r3->field_7 = r0
    //     0xa39554: stur            w0, [x3, #7]
    // 0xa39558: ldur            x0, [fp, #-0x18]
    // 0xa3955c: StoreField: r3->field_b = r0
    //     0xa3955c: stur            w0, [x3, #0xb]
    // 0xa39560: r1 = Null
    //     0xa39560: mov             x1, NULL
    // 0xa39564: r2 = 16
    //     0xa39564: movz            x2, #0x10
    // 0xa39568: r0 = AllocateArray()
    //     0xa39568: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3956c: stur            x0, [fp, #-0x18]
    // 0xa39570: r16 = "Login pada halaman Internet Banking BRI."
    //     0xa39570: add             x16, PP, #0x30, lsl #12  ; [pp+0x30840] "Login pada halaman Internet Banking BRI."
    //     0xa39574: ldr             x16, [x16, #0x840]
    // 0xa39578: StoreField: r0->field_f = r16
    //     0xa39578: stur            w16, [x0, #0xf]
    // 0xa3957c: r16 = "Pilih menu \"Pembayaran\"."
    //     0xa3957c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30768] "Pilih menu \"Pembayaran\"."
    //     0xa39580: ldr             x16, [x16, #0x768]
    // 0xa39584: StoreField: r0->field_13 = r16
    //     0xa39584: stur            w16, [x0, #0x13]
    // 0xa39588: r16 = "Pilih menu \"BRIVA\"."
    //     0xa39588: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f0] "Pilih menu \"BRIVA\"."
    //     0xa3958c: ldr             x16, [x16, #0x7f0]
    // 0xa39590: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39590: stur            w16, [x0, #0x17]
    // 0xa39594: r16 = "Masukkan nomor Virtual Account Anda (Contoh: ****************)"
    //     0xa39594: add             x16, PP, #0x30, lsl #12  ; [pp+0x30848] "Masukkan nomor Virtual Account Anda (Contoh: ****************)"
    //     0xa39598: ldr             x16, [x16, #0x848]
    // 0xa3959c: StoreField: r0->field_1b = r16
    //     0xa3959c: stur            w16, [x0, #0x1b]
    // 0xa395a0: r16 = "Masukkan password Internet Banking BRI."
    //     0xa395a0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30850] "Masukkan password Internet Banking BRI."
    //     0xa395a4: ldr             x16, [x16, #0x850]
    // 0xa395a8: StoreField: r0->field_1f = r16
    //     0xa395a8: stur            w16, [x0, #0x1f]
    // 0xa395ac: r16 = "Masukkan mToken Internet Banking BRI."
    //     0xa395ac: add             x16, PP, #0x30, lsl #12  ; [pp+0x30858] "Masukkan mToken Internet Banking BRI."
    //     0xa395b0: ldr             x16, [x16, #0x858]
    // 0xa395b4: StoreField: r0->field_23 = r16
    //     0xa395b4: stur            w16, [x0, #0x23]
    // 0xa395b8: r16 = "Anda akan mendapatkan notifikasi pembayaran."
    //     0xa395b8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30860] "Anda akan mendapatkan notifikasi pembayaran."
    //     0xa395bc: ldr             x16, [x16, #0x860]
    // 0xa395c0: StoreField: r0->field_27 = r16
    //     0xa395c0: stur            w16, [x0, #0x27]
    // 0xa395c4: r16 = "Pembayaran Anda Telah Berhasil."
    //     0xa395c4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30640] "Pembayaran Anda Telah Berhasil."
    //     0xa395c8: ldr             x16, [x16, #0x640]
    // 0xa395cc: StoreField: r0->field_2b = r16
    //     0xa395cc: stur            w16, [x0, #0x2b]
    // 0xa395d0: r1 = <String>
    //     0xa395d0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa395d4: r0 = AllocateGrowableArray()
    //     0xa395d4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa395d8: mov             x1, x0
    // 0xa395dc: ldur            x0, [fp, #-0x18]
    // 0xa395e0: stur            x1, [fp, #-0x20]
    // 0xa395e4: StoreField: r1->field_f = r0
    //     0xa395e4: stur            w0, [x1, #0xf]
    // 0xa395e8: r2 = 16
    //     0xa395e8: movz            x2, #0x10
    // 0xa395ec: StoreField: r1->field_b = r2
    //     0xa395ec: stur            w2, [x1, #0xb]
    // 0xa395f0: r0 = Instruction()
    //     0xa395f0: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa395f4: mov             x3, x0
    // 0xa395f8: r0 = "Melalui Internet Banking BRI."
    //     0xa395f8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30868] "Melalui Internet Banking BRI."
    //     0xa395fc: ldr             x0, [x0, #0x868]
    // 0xa39600: stur            x3, [fp, #-0x18]
    // 0xa39604: StoreField: r3->field_7 = r0
    //     0xa39604: stur            w0, [x3, #7]
    // 0xa39608: ldur            x0, [fp, #-0x20]
    // 0xa3960c: StoreField: r3->field_b = r0
    //     0xa3960c: stur            w0, [x3, #0xb]
    // 0xa39610: r1 = Null
    //     0xa39610: mov             x1, NULL
    // 0xa39614: r2 = 16
    //     0xa39614: movz            x2, #0x10
    // 0xa39618: r0 = AllocateArray()
    //     0xa39618: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa3961c: stur            x0, [fp, #-0x20]
    // 0xa39620: r16 = "Pilih menu \"Mini ATM\"."
    //     0xa39620: add             x16, PP, #0x30, lsl #12  ; [pp+0x30870] "Pilih menu \"Mini ATM\"."
    //     0xa39624: ldr             x16, [x16, #0x870]
    // 0xa39628: StoreField: r0->field_f = r16
    //     0xa39628: stur            w16, [x0, #0xf]
    // 0xa3962c: r16 = "Pilih menu \"Pembayaran\"."
    //     0xa3962c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30768] "Pilih menu \"Pembayaran\"."
    //     0xa39630: ldr             x16, [x16, #0x768]
    // 0xa39634: StoreField: r0->field_13 = r16
    //     0xa39634: stur            w16, [x0, #0x13]
    // 0xa39638: r16 = "Pilih menu \"BRIVA\"."
    //     0xa39638: add             x16, PP, #0x30, lsl #12  ; [pp+0x307f0] "Pilih menu \"BRIVA\"."
    //     0xa3963c: ldr             x16, [x16, #0x7f0]
    // 0xa39640: ArrayStore: r0[0] = r16  ; List_4
    //     0xa39640: stur            w16, [x0, #0x17]
    // 0xa39644: r16 = "Swipe kartu ATM"
    //     0xa39644: add             x16, PP, #0x30, lsl #12  ; [pp+0x30878] "Swipe kartu ATM"
    //     0xa39648: ldr             x16, [x16, #0x878]
    // 0xa3964c: StoreField: r0->field_1b = r16
    //     0xa3964c: stur            w16, [x0, #0x1b]
    // 0xa39650: r16 = "Masukkan nomor Virtual Account Anda (Contoh: ****************)"
    //     0xa39650: add             x16, PP, #0x30, lsl #12  ; [pp+0x30848] "Masukkan nomor Virtual Account Anda (Contoh: ****************)"
    //     0xa39654: ldr             x16, [x16, #0x848]
    // 0xa39658: StoreField: r0->field_1f = r16
    //     0xa39658: stur            w16, [x0, #0x1f]
    // 0xa3965c: r16 = "Masukkan pin ATM"
    //     0xa3965c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30880] "Masukkan pin ATM"
    //     0xa39660: ldr             x16, [x16, #0x880]
    // 0xa39664: StoreField: r0->field_23 = r16
    //     0xa39664: stur            w16, [x0, #0x23]
    // 0xa39668: r16 = "Klik \"Lanjut\" untuk memproses pembayaran."
    //     0xa39668: add             x16, PP, #0x30, lsl #12  ; [pp+0x30888] "Klik \"Lanjut\" untuk memproses pembayaran."
    //     0xa3966c: ldr             x16, [x16, #0x888]
    // 0xa39670: StoreField: r0->field_27 = r16
    //     0xa39670: stur            w16, [x0, #0x27]
    // 0xa39674: r16 = "Klik \"Ya\" untuk mencetak struk"
    //     0xa39674: add             x16, PP, #0x30, lsl #12  ; [pp+0x30890] "Klik \"Ya\" untuk mencetak struk"
    //     0xa39678: ldr             x16, [x16, #0x890]
    // 0xa3967c: StoreField: r0->field_2b = r16
    //     0xa3967c: stur            w16, [x0, #0x2b]
    // 0xa39680: r1 = <String>
    //     0xa39680: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa39684: r0 = AllocateGrowableArray()
    //     0xa39684: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39688: mov             x1, x0
    // 0xa3968c: ldur            x0, [fp, #-0x20]
    // 0xa39690: stur            x1, [fp, #-0x28]
    // 0xa39694: StoreField: r1->field_f = r0
    //     0xa39694: stur            w0, [x1, #0xf]
    // 0xa39698: r0 = 16
    //     0xa39698: movz            x0, #0x10
    // 0xa3969c: StoreField: r1->field_b = r0
    //     0xa3969c: stur            w0, [x1, #0xb]
    // 0xa396a0: r0 = Instruction()
    //     0xa396a0: bl              #0xa3971c  ; AllocateInstructionStub -> Instruction (size=0x10)
    // 0xa396a4: mov             x3, x0
    // 0xa396a8: r0 = "Melalui mini ATM BRI"
    //     0xa396a8: add             x0, PP, #0x30, lsl #12  ; [pp+0x30898] "Melalui mini ATM BRI"
    //     0xa396ac: ldr             x0, [x0, #0x898]
    // 0xa396b0: stur            x3, [fp, #-0x20]
    // 0xa396b4: StoreField: r3->field_7 = r0
    //     0xa396b4: stur            w0, [x3, #7]
    // 0xa396b8: ldur            x0, [fp, #-0x28]
    // 0xa396bc: StoreField: r3->field_b = r0
    //     0xa396bc: stur            w0, [x3, #0xb]
    // 0xa396c0: r1 = Null
    //     0xa396c0: mov             x1, NULL
    // 0xa396c4: r2 = 8
    //     0xa396c4: movz            x2, #0x8
    // 0xa396c8: r0 = AllocateArray()
    //     0xa396c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa396cc: mov             x2, x0
    // 0xa396d0: ldur            x0, [fp, #-8]
    // 0xa396d4: stur            x2, [fp, #-0x28]
    // 0xa396d8: StoreField: r2->field_f = r0
    //     0xa396d8: stur            w0, [x2, #0xf]
    // 0xa396dc: ldur            x0, [fp, #-0x10]
    // 0xa396e0: StoreField: r2->field_13 = r0
    //     0xa396e0: stur            w0, [x2, #0x13]
    // 0xa396e4: ldur            x0, [fp, #-0x18]
    // 0xa396e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xa396e8: stur            w0, [x2, #0x17]
    // 0xa396ec: ldur            x0, [fp, #-0x20]
    // 0xa396f0: StoreField: r2->field_1b = r0
    //     0xa396f0: stur            w0, [x2, #0x1b]
    // 0xa396f4: r1 = <Instruction>
    //     0xa396f4: add             x1, PP, #0x30, lsl #12  ; [pp+0x305b8] TypeArguments: <Instruction>
    //     0xa396f8: ldr             x1, [x1, #0x5b8]
    // 0xa396fc: r0 = AllocateGrowableArray()
    //     0xa396fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa39700: ldur            x1, [fp, #-0x28]
    // 0xa39704: StoreField: r0->field_f = r1
    //     0xa39704: stur            w1, [x0, #0xf]
    // 0xa39708: r1 = 8
    //     0xa39708: movz            x1, #0x8
    // 0xa3970c: StoreField: r0->field_b = r1
    //     0xa3970c: stur            w1, [x0, #0xb]
    // 0xa39710: LeaveFrame
    //     0xa39710: mov             SP, fp
    //     0xa39714: ldp             fp, lr, [SP], #0x10
    // 0xa39718: ret
    //     0xa39718: ret             
  }
}

// class id: 1164, size: 0x10, field offset: 0x8
class Instruction extends Object {
}

// class id: 1165, size: 0x18, field offset: 0x8
//   const constructor, 
class Merchant extends Object {

  _OneByteString field_c;
  _OneByteString field_10;
  _OneByteString field_14;
}

// class id: 6843, size: 0x14, field offset: 0x14
enum PaymentMethodGroup extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cfec, size: 0x64
    // 0xc4cfec: EnterFrame
    //     0xc4cfec: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cff0: mov             fp, SP
    // 0xc4cff4: AllocStack(0x10)
    //     0xc4cff4: sub             SP, SP, #0x10
    // 0xc4cff8: SetupParameters(PaymentMethodGroup this /* r1 => r0, fp-0x8 */)
    //     0xc4cff8: mov             x0, x1
    //     0xc4cffc: stur            x1, [fp, #-8]
    // 0xc4d000: CheckStackOverflow
    //     0xc4d000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d004: cmp             SP, x16
    //     0xc4d008: b.ls            #0xc4d048
    // 0xc4d00c: r1 = Null
    //     0xc4d00c: mov             x1, NULL
    // 0xc4d010: r2 = 4
    //     0xc4d010: movz            x2, #0x4
    // 0xc4d014: r0 = AllocateArray()
    //     0xc4d014: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d018: r16 = "PaymentMethodGroup."
    //     0xc4d018: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f80] "PaymentMethodGroup."
    //     0xc4d01c: ldr             x16, [x16, #0xf80]
    // 0xc4d020: StoreField: r0->field_f = r16
    //     0xc4d020: stur            w16, [x0, #0xf]
    // 0xc4d024: ldur            x1, [fp, #-8]
    // 0xc4d028: LoadField: r2 = r1->field_f
    //     0xc4d028: ldur            w2, [x1, #0xf]
    // 0xc4d02c: DecompressPointer r2
    //     0xc4d02c: add             x2, x2, HEAP, lsl #32
    // 0xc4d030: StoreField: r0->field_13 = r2
    //     0xc4d030: stur            w2, [x0, #0x13]
    // 0xc4d034: str             x0, [SP]
    // 0xc4d038: r0 = _interpolate()
    //     0xc4d038: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d03c: LeaveFrame
    //     0xc4d03c: mov             SP, fp
    //     0xc4d040: ldp             fp, lr, [SP], #0x10
    // 0xc4d044: ret
    //     0xc4d044: ret             
    // 0xc4d048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d04c: b               #0xc4d00c
  }
}

// class id: 6844, size: 0x18, field offset: 0x14
enum PaymentMethod extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  PaymentMethodGroup field_14;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cf88, size: 0x64
    // 0xc4cf88: EnterFrame
    //     0xc4cf88: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cf8c: mov             fp, SP
    // 0xc4cf90: AllocStack(0x10)
    //     0xc4cf90: sub             SP, SP, #0x10
    // 0xc4cf94: SetupParameters(PaymentMethod this /* r1 => r0, fp-0x8 */)
    //     0xc4cf94: mov             x0, x1
    //     0xc4cf98: stur            x1, [fp, #-8]
    // 0xc4cf9c: CheckStackOverflow
    //     0xc4cf9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4cfa0: cmp             SP, x16
    //     0xc4cfa4: b.ls            #0xc4cfe4
    // 0xc4cfa8: r1 = Null
    //     0xc4cfa8: mov             x1, NULL
    // 0xc4cfac: r2 = 4
    //     0xc4cfac: movz            x2, #0x4
    // 0xc4cfb0: r0 = AllocateArray()
    //     0xc4cfb0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4cfb4: r16 = "PaymentMethod."
    //     0xc4cfb4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f88] "PaymentMethod."
    //     0xc4cfb8: ldr             x16, [x16, #0xf88]
    // 0xc4cfbc: StoreField: r0->field_f = r16
    //     0xc4cfbc: stur            w16, [x0, #0xf]
    // 0xc4cfc0: ldur            x1, [fp, #-8]
    // 0xc4cfc4: LoadField: r2 = r1->field_f
    //     0xc4cfc4: ldur            w2, [x1, #0xf]
    // 0xc4cfc8: DecompressPointer r2
    //     0xc4cfc8: add             x2, x2, HEAP, lsl #32
    // 0xc4cfcc: StoreField: r0->field_13 = r2
    //     0xc4cfcc: stur            w2, [x0, #0x13]
    // 0xc4cfd0: str             x0, [SP]
    // 0xc4cfd4: r0 = _interpolate()
    //     0xc4cfd4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4cfd8: LeaveFrame
    //     0xc4cfd8: mov             SP, fp
    //     0xc4cfdc: ldp             fp, lr, [SP], #0x10
    // 0xc4cfe0: ret
    //     0xc4cfe0: ret             
    // 0xc4cfe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4cfe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cfe8: b               #0xc4cfa8
  }
}

// class id: 6845, size: 0x28, field offset: 0x14
enum PaymentType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _OneByteString field_14;
  _Mint field_18;
  _Mint field_20;

  const int dyn:get:id(PaymentType) {
    // ** addr: 0x72b9c0, size: 0x48
    // 0x72b9c0: ldr             x2, [SP]
    // 0x72b9c4: ArrayLoad: r3 = r2[0]  ; List_8
    //     0x72b9c4: ldur            x3, [x2, #0x17]
    // 0x72b9c8: r0 = BoxInt64Instr(r3)
    //     0x72b9c8: sbfiz           x0, x3, #1, #0x1f
    //     0x72b9cc: cmp             x3, x0, asr #1
    //     0x72b9d0: b.eq            #0x72b9ec
    //     0x72b9d4: stp             fp, lr, [SP, #-0x10]!
    //     0x72b9d8: mov             fp, SP
    //     0x72b9dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72b9e0: mov             SP, fp
    //     0x72b9e4: ldp             fp, lr, [SP], #0x10
    //     0x72b9e8: stur            x3, [x0, #7]
    // 0x72b9ec: ret
    //     0x72b9ec: ret             
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xc4cf24, size: 0x64
    // 0xc4cf24: EnterFrame
    //     0xc4cf24: stp             fp, lr, [SP, #-0x10]!
    //     0xc4cf28: mov             fp, SP
    // 0xc4cf2c: AllocStack(0x10)
    //     0xc4cf2c: sub             SP, SP, #0x10
    // 0xc4cf30: SetupParameters(PaymentType this /* r1 => r0, fp-0x8 */)
    //     0xc4cf30: mov             x0, x1
    //     0xc4cf34: stur            x1, [fp, #-8]
    // 0xc4cf38: CheckStackOverflow
    //     0xc4cf38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4cf3c: cmp             SP, x16
    //     0xc4cf40: b.ls            #0xc4cf80
    // 0xc4cf44: r1 = Null
    //     0xc4cf44: mov             x1, NULL
    // 0xc4cf48: r2 = 4
    //     0xc4cf48: movz            x2, #0x4
    // 0xc4cf4c: r0 = AllocateArray()
    //     0xc4cf4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4cf50: r16 = "PaymentType."
    //     0xc4cf50: add             x16, PP, #0x30, lsl #12  ; [pp+0x30c18] "PaymentType."
    //     0xc4cf54: ldr             x16, [x16, #0xc18]
    // 0xc4cf58: StoreField: r0->field_f = r16
    //     0xc4cf58: stur            w16, [x0, #0xf]
    // 0xc4cf5c: ldur            x1, [fp, #-8]
    // 0xc4cf60: LoadField: r2 = r1->field_f
    //     0xc4cf60: ldur            w2, [x1, #0xf]
    // 0xc4cf64: DecompressPointer r2
    //     0xc4cf64: add             x2, x2, HEAP, lsl #32
    // 0xc4cf68: StoreField: r0->field_13 = r2
    //     0xc4cf68: stur            w2, [x0, #0x13]
    // 0xc4cf6c: str             x0, [SP]
    // 0xc4cf70: r0 = _interpolate()
    //     0xc4cf70: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4cf74: LeaveFrame
    //     0xc4cf74: mov             SP, fp
    //     0xc4cf78: ldp             fp, lr, [SP], #0x10
    // 0xc4cf7c: ret
    //     0xc4cf7c: ret             
    // 0xc4cf80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4cf80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4cf84: b               #0xc4cf44
  }
}
