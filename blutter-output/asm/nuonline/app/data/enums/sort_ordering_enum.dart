// lib: , url: package:nuonline/app/data/enums/sort_ordering_enum.dart

// class id: 1049999, size: 0x8
class :: {

  static _ OrderByExtension.id(/* No info */) {
    // ** addr: 0x72b904, size: 0x7c
    // 0x72b904: EnterFrame
    //     0x72b904: stp             fp, lr, [SP, #-0x10]!
    //     0x72b908: mov             fp, SP
    // 0x72b90c: LoadField: r2 = r1->field_7
    //     0x72b90c: ldur            x2, [x1, #7]
    // 0x72b910: cmp             x2, #1
    // 0x72b914: b.gt            #0x72b944
    // 0x72b918: cmp             x2, #0
    // 0x72b91c: b.gt            #0x72b930
    // 0x72b920: r0 = "name"
    //     0x72b920: ldr             x0, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x72b924: LeaveFrame
    //     0x72b924: mov             SP, fp
    //     0x72b928: ldp             fp, lr, [SP], #0x10
    // 0x72b92c: ret
    //     0x72b92c: ret             
    // 0x72b930: r0 = "updated_at"
    //     0x72b930: add             x0, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x72b934: ldr             x0, [x0, #0xe88]
    // 0x72b938: LeaveFrame
    //     0x72b938: mov             SP, fp
    //     0x72b93c: ldp             fp, lr, [SP], #0x10
    // 0x72b940: ret
    //     0x72b940: ret             
    // 0x72b944: r0 = BoxInt64Instr(r2)
    //     0x72b944: sbfiz           x0, x2, #1, #0x1f
    //     0x72b948: cmp             x2, x0, asr #1
    //     0x72b94c: b.eq            #0x72b958
    //     0x72b950: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72b954: stur            x2, [x0, #7]
    // 0x72b958: cmp             w0, #4
    // 0x72b95c: b.ne            #0x72b974
    // 0x72b960: r0 = "amount"
    //     0x72b960: add             x0, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x72b964: ldr             x0, [x0, #0xe0]
    // 0x72b968: LeaveFrame
    //     0x72b968: mov             SP, fp
    //     0x72b96c: ldp             fp, lr, [SP], #0x10
    // 0x72b970: ret
    //     0x72b970: ret             
    // 0x72b974: r0 = UnimplementedError()
    //     0x72b974: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x72b978: r0 = Throw()
    //     0x72b978: bl              #0xec04b8  ; ThrowStub
    // 0x72b97c: brk             #0
  }
  static _ OrderByExtension.title(/* No info */) {
    // ** addr: 0xae6bb4, size: 0x80
    // 0xae6bb4: EnterFrame
    //     0xae6bb4: stp             fp, lr, [SP, #-0x10]!
    //     0xae6bb8: mov             fp, SP
    // 0xae6bbc: LoadField: r2 = r1->field_7
    //     0xae6bbc: ldur            x2, [x1, #7]
    // 0xae6bc0: cmp             x2, #1
    // 0xae6bc4: b.gt            #0xae6bf8
    // 0xae6bc8: r0 = BoxInt64Instr(r2)
    //     0xae6bc8: sbfiz           x0, x2, #1, #0x1f
    //     0xae6bcc: cmp             x2, x0, asr #1
    //     0xae6bd0: b.eq            #0xae6bdc
    //     0xae6bd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae6bd8: stur            x2, [x0, #7]
    // 0xae6bdc: cmp             w0, #2
    // 0xae6be0: b.ne            #0xae6c28
    // 0xae6be4: r0 = "Terbaru"
    //     0xae6be4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc10] "Terbaru"
    //     0xae6be8: ldr             x0, [x0, #0xc10]
    // 0xae6bec: LeaveFrame
    //     0xae6bec: mov             SP, fp
    //     0xae6bf0: ldp             fp, lr, [SP], #0x10
    // 0xae6bf4: ret
    //     0xae6bf4: ret             
    // 0xae6bf8: r0 = BoxInt64Instr(r2)
    //     0xae6bf8: sbfiz           x0, x2, #1, #0x1f
    //     0xae6bfc: cmp             x2, x0, asr #1
    //     0xae6c00: b.eq            #0xae6c0c
    //     0xae6c04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae6c08: stur            x2, [x0, #7]
    // 0xae6c0c: cmp             w0, #4
    // 0xae6c10: b.ne            #0xae6c28
    // 0xae6c14: r0 = "Terbanyak"
    //     0xae6c14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc28] "Terbanyak"
    //     0xae6c18: ldr             x0, [x0, #0xc28]
    // 0xae6c1c: LeaveFrame
    //     0xae6c1c: mov             SP, fp
    //     0xae6c20: ldp             fp, lr, [SP], #0x10
    // 0xae6c24: ret
    //     0xae6c24: ret             
    // 0xae6c28: r0 = UnimplementedError()
    //     0xae6c28: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xae6c2c: r0 = Throw()
    //     0xae6c2c: bl              #0xec04b8  ; ThrowStub
    // 0xae6c30: brk             #0
  }
}

// class id: 5603, size: 0x10, field offset: 0x8
//   const constructor, 
class SortOrdering extends Equatable {

  OrderBy field_8;
  SortBy field_c;

  _ copyWith(/* No info */) {
    // ** addr: 0xae6f70, size: 0x34
    // 0xae6f70: EnterFrame
    //     0xae6f70: stp             fp, lr, [SP, #-0x10]!
    //     0xae6f74: mov             fp, SP
    // 0xae6f78: AllocStack(0x8)
    //     0xae6f78: sub             SP, SP, #8
    // 0xae6f7c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xae6f7c: stur            x2, [fp, #-8]
    // 0xae6f80: r0 = SortOrdering()
    //     0xae6f80: bl              #0xae6fa4  ; AllocateSortOrderingStub -> SortOrdering (size=0x10)
    // 0xae6f84: ldur            x1, [fp, #-8]
    // 0xae6f88: StoreField: r0->field_7 = r1
    //     0xae6f88: stur            w1, [x0, #7]
    // 0xae6f8c: r1 = Instance_SortBy
    //     0xae6f8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fc58] Obj!SortBy@e30ce1
    //     0xae6f90: ldr             x1, [x1, #0xc58]
    // 0xae6f94: StoreField: r0->field_b = r1
    //     0xae6f94: stur            w1, [x0, #0xb]
    // 0xae6f98: LeaveFrame
    //     0xae6f98: mov             SP, fp
    //     0xae6f9c: ldp             fp, lr, [SP], #0x10
    // 0xae6fa0: ret
    //     0xae6fa0: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbc0c, size: 0x68
    // 0xbdbc0c: EnterFrame
    //     0xbdbc0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbc10: mov             fp, SP
    // 0xbdbc14: AllocStack(0x10)
    //     0xbdbc14: sub             SP, SP, #0x10
    // 0xbdbc18: r0 = 4
    //     0xbdbc18: movz            x0, #0x4
    // 0xbdbc1c: LoadField: r3 = r1->field_7
    //     0xbdbc1c: ldur            w3, [x1, #7]
    // 0xbdbc20: DecompressPointer r3
    //     0xbdbc20: add             x3, x3, HEAP, lsl #32
    // 0xbdbc24: mov             x2, x0
    // 0xbdbc28: stur            x3, [fp, #-8]
    // 0xbdbc2c: r1 = Null
    //     0xbdbc2c: mov             x1, NULL
    // 0xbdbc30: r0 = AllocateArray()
    //     0xbdbc30: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbc34: mov             x2, x0
    // 0xbdbc38: ldur            x0, [fp, #-8]
    // 0xbdbc3c: stur            x2, [fp, #-0x10]
    // 0xbdbc40: StoreField: r2->field_f = r0
    //     0xbdbc40: stur            w0, [x2, #0xf]
    // 0xbdbc44: r16 = Instance_SortBy
    //     0xbdbc44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc58] Obj!SortBy@e30ce1
    //     0xbdbc48: ldr             x16, [x16, #0xc58]
    // 0xbdbc4c: StoreField: r2->field_13 = r16
    //     0xbdbc4c: stur            w16, [x2, #0x13]
    // 0xbdbc50: r1 = <Object?>
    //     0xbdbc50: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbc54: r0 = AllocateGrowableArray()
    //     0xbdbc54: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbc58: ldur            x1, [fp, #-0x10]
    // 0xbdbc5c: StoreField: r0->field_f = r1
    //     0xbdbc5c: stur            w1, [x0, #0xf]
    // 0xbdbc60: r1 = 4
    //     0xbdbc60: movz            x1, #0x4
    // 0xbdbc64: StoreField: r0->field_b = r1
    //     0xbdbc64: stur            w1, [x0, #0xb]
    // 0xbdbc68: LeaveFrame
    //     0xbdbc68: mov             SP, fp
    //     0xbdbc6c: ldp             fp, lr, [SP], #0x10
    // 0xbdbc70: ret
    //     0xbdbc70: ret             
  }
}

// class id: 6841, size: 0x14, field offset: 0x14
enum SortBy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d0b4, size: 0x64
    // 0xc4d0b4: EnterFrame
    //     0xc4d0b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d0b8: mov             fp, SP
    // 0xc4d0bc: AllocStack(0x10)
    //     0xc4d0bc: sub             SP, SP, #0x10
    // 0xc4d0c0: SetupParameters(SortBy this /* r1 => r0, fp-0x8 */)
    //     0xc4d0c0: mov             x0, x1
    //     0xc4d0c4: stur            x1, [fp, #-8]
    // 0xc4d0c8: CheckStackOverflow
    //     0xc4d0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d0cc: cmp             SP, x16
    //     0xc4d0d0: b.ls            #0xc4d110
    // 0xc4d0d4: r1 = Null
    //     0xc4d0d4: mov             x1, NULL
    // 0xc4d0d8: r2 = 4
    //     0xc4d0d8: movz            x2, #0x4
    // 0xc4d0dc: r0 = AllocateArray()
    //     0xc4d0dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d0e0: r16 = "SortBy."
    //     0xc4d0e0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f78] "SortBy."
    //     0xc4d0e4: ldr             x16, [x16, #0xf78]
    // 0xc4d0e8: StoreField: r0->field_f = r16
    //     0xc4d0e8: stur            w16, [x0, #0xf]
    // 0xc4d0ec: ldur            x1, [fp, #-8]
    // 0xc4d0f0: LoadField: r2 = r1->field_f
    //     0xc4d0f0: ldur            w2, [x1, #0xf]
    // 0xc4d0f4: DecompressPointer r2
    //     0xc4d0f4: add             x2, x2, HEAP, lsl #32
    // 0xc4d0f8: StoreField: r0->field_13 = r2
    //     0xc4d0f8: stur            w2, [x0, #0x13]
    // 0xc4d0fc: str             x0, [SP]
    // 0xc4d100: r0 = _interpolate()
    //     0xc4d100: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d104: LeaveFrame
    //     0xc4d104: mov             SP, fp
    //     0xc4d108: ldp             fp, lr, [SP], #0x10
    // 0xc4d10c: ret
    //     0xc4d10c: ret             
    // 0xc4d110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d114: b               #0xc4d0d4
  }
}

// class id: 6842, size: 0x14, field offset: 0x14
enum OrderBy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d050, size: 0x64
    // 0xc4d050: EnterFrame
    //     0xc4d050: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d054: mov             fp, SP
    // 0xc4d058: AllocStack(0x10)
    //     0xc4d058: sub             SP, SP, #0x10
    // 0xc4d05c: SetupParameters(OrderBy this /* r1 => r0, fp-0x8 */)
    //     0xc4d05c: mov             x0, x1
    //     0xc4d060: stur            x1, [fp, #-8]
    // 0xc4d064: CheckStackOverflow
    //     0xc4d064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d068: cmp             SP, x16
    //     0xc4d06c: b.ls            #0xc4d0ac
    // 0xc4d070: r1 = Null
    //     0xc4d070: mov             x1, NULL
    // 0xc4d074: r2 = 4
    //     0xc4d074: movz            x2, #0x4
    // 0xc4d078: r0 = AllocateArray()
    //     0xc4d078: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d07c: r16 = "OrderBy."
    //     0xc4d07c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f70] "OrderBy."
    //     0xc4d080: ldr             x16, [x16, #0xf70]
    // 0xc4d084: StoreField: r0->field_f = r16
    //     0xc4d084: stur            w16, [x0, #0xf]
    // 0xc4d088: ldur            x1, [fp, #-8]
    // 0xc4d08c: LoadField: r2 = r1->field_f
    //     0xc4d08c: ldur            w2, [x1, #0xf]
    // 0xc4d090: DecompressPointer r2
    //     0xc4d090: add             x2, x2, HEAP, lsl #32
    // 0xc4d094: StoreField: r0->field_13 = r2
    //     0xc4d094: stur            w2, [x0, #0x13]
    // 0xc4d098: str             x0, [SP]
    // 0xc4d09c: r0 = _interpolate()
    //     0xc4d09c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d0a0: LeaveFrame
    //     0xc4d0a0: mov             SP, fp
    //     0xc4d0a4: ldp             fp, lr, [SP], #0x10
    // 0xc4d0a8: ret
    //     0xc4d0a8: ret             
    // 0xc4d0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d0b0: b               #0xc4d070
  }
}
