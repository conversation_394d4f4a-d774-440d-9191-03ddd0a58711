// lib: , url: package:nuonline/app/data/enums/tajweed_enum.dart

// class id: 1050000, size: 0x8
class :: {
}

// class id: 6840, size: 0x28, field offset: 0x14
enum TajweedType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _OneByteString field_14;
  _OneByteString field_18;
  _ImmutableList<int> field_1c;
  _OneByteString field_20;
  _OneByteString field_24;

  _ colorOf(/* No info */) {
    // ** addr: 0xb31024, size: 0x308
    // 0xb31024: EnterFrame
    //     0xb31024: stp             fp, lr, [SP, #-0x10]!
    //     0xb31028: mov             fp, SP
    // 0xb3102c: AllocStack(0x18)
    //     0xb3102c: sub             SP, SP, #0x18
    // 0xb31030: SetupParameters(TajweedType this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xb31030: mov             x0, x1
    //     0xb31034: stur            x1, [fp, #-8]
    //     0xb31038: mov             x1, x2
    // 0xb3103c: CheckStackOverflow
    //     0xb3103c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb31040: cmp             SP, x16
    //     0xb31044: b.ls            #0xb31324
    // 0xb31048: r0 = of()
    //     0xb31048: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3104c: r16 = <NTajweedColor>
    //     0xb3104c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ce0] TypeArguments: <NTajweedColor>
    //     0xb31050: ldr             x16, [x16, #0xce0]
    // 0xb31054: stp             x0, x16, [SP]
    // 0xb31058: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb31058: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3105c: r0 = extension()
    //     0xb3105c: bl              #0xb3132c  ; [package:flutter/src/material/theme_data.dart] ThemeData::extension
    // 0xb31060: mov             x3, x0
    // 0xb31064: ldur            x2, [fp, #-8]
    // 0xb31068: LoadField: r4 = r2->field_7
    //     0xb31068: ldur            x4, [x2, #7]
    // 0xb3106c: r0 = BoxInt64Instr(r4)
    //     0xb3106c: sbfiz           x0, x4, #1, #0x1f
    //     0xb31070: cmp             x4, x0, asr #1
    //     0xb31074: b.eq            #0xb31080
    //     0xb31078: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb3107c: stur            x4, [x0, #7]
    // 0xb31080: r1 = _Int32List
    //     0xb31080: add             x1, PP, #0x32, lsl #12  ; [pp+0x32f78] _Int32List(20) [0x78, 0x98, 0xb8, 0xd8, 0xf8, 0x118, 0x138, 0x158, 0x178, 0x198, 0x1b8, 0x1d8, 0x1f8, 0x218, 0x238, 0x258, 0x278, 0x298, 0x2b8, 0x2d8]
    //     0xb31084: ldr             x1, [x1, #0xf78]
    // 0xb31088: ArrayLoad: r1 = r1[r0]  ; TypedSigned_4
    //     0xb31088: add             x16, x1, w0, sxtw #1
    //     0xb3108c: ldursw          x1, [x16, #0x17]
    // 0xb31090: adr             x2, #0xb31024
    // 0xb31094: add             x2, x2, x1
    // 0xb31098: br              x2
    // 0xb3109c: cmp             w3, NULL
    // 0xb310a0: b.ne            #0xb310ac
    // 0xb310a4: r1 = Null
    //     0xb310a4: mov             x1, NULL
    // 0xb310a8: b               #0xb310b4
    // 0xb310ac: LoadField: r1 = r3->field_1f
    //     0xb310ac: ldur            w1, [x3, #0x1f]
    // 0xb310b0: DecompressPointer r1
    //     0xb310b0: add             x1, x1, HEAP, lsl #32
    // 0xb310b4: mov             x0, x1
    // 0xb310b8: b               #0xb31318
    // 0xb310bc: cmp             w3, NULL
    // 0xb310c0: b.ne            #0xb310cc
    // 0xb310c4: r1 = Null
    //     0xb310c4: mov             x1, NULL
    // 0xb310c8: b               #0xb310d4
    // 0xb310cc: LoadField: r1 = r3->field_b
    //     0xb310cc: ldur            w1, [x3, #0xb]
    // 0xb310d0: DecompressPointer r1
    //     0xb310d0: add             x1, x1, HEAP, lsl #32
    // 0xb310d4: mov             x0, x1
    // 0xb310d8: b               #0xb31318
    // 0xb310dc: cmp             w3, NULL
    // 0xb310e0: b.ne            #0xb310ec
    // 0xb310e4: r1 = Null
    //     0xb310e4: mov             x1, NULL
    // 0xb310e8: b               #0xb310f4
    // 0xb310ec: LoadField: r1 = r3->field_b
    //     0xb310ec: ldur            w1, [x3, #0xb]
    // 0xb310f0: DecompressPointer r1
    //     0xb310f0: add             x1, x1, HEAP, lsl #32
    // 0xb310f4: mov             x0, x1
    // 0xb310f8: b               #0xb31318
    // 0xb310fc: cmp             w3, NULL
    // 0xb31100: b.ne            #0xb3110c
    // 0xb31104: r1 = Null
    //     0xb31104: mov             x1, NULL
    // 0xb31108: b               #0xb31114
    // 0xb3110c: LoadField: r1 = r3->field_b
    //     0xb3110c: ldur            w1, [x3, #0xb]
    // 0xb31110: DecompressPointer r1
    //     0xb31110: add             x1, x1, HEAP, lsl #32
    // 0xb31114: mov             x0, x1
    // 0xb31118: b               #0xb31318
    // 0xb3111c: cmp             w3, NULL
    // 0xb31120: b.ne            #0xb3112c
    // 0xb31124: r1 = Null
    //     0xb31124: mov             x1, NULL
    // 0xb31128: b               #0xb31134
    // 0xb3112c: LoadField: r1 = r3->field_f
    //     0xb3112c: ldur            w1, [x3, #0xf]
    // 0xb31130: DecompressPointer r1
    //     0xb31130: add             x1, x1, HEAP, lsl #32
    // 0xb31134: mov             x0, x1
    // 0xb31138: b               #0xb31318
    // 0xb3113c: cmp             w3, NULL
    // 0xb31140: b.ne            #0xb3114c
    // 0xb31144: r1 = Null
    //     0xb31144: mov             x1, NULL
    // 0xb31148: b               #0xb31154
    // 0xb3114c: LoadField: r1 = r3->field_f
    //     0xb3114c: ldur            w1, [x3, #0xf]
    // 0xb31150: DecompressPointer r1
    //     0xb31150: add             x1, x1, HEAP, lsl #32
    // 0xb31154: mov             x0, x1
    // 0xb31158: b               #0xb31318
    // 0xb3115c: cmp             w3, NULL
    // 0xb31160: b.ne            #0xb3116c
    // 0xb31164: r1 = Null
    //     0xb31164: mov             x1, NULL
    // 0xb31168: b               #0xb31174
    // 0xb3116c: LoadField: r1 = r3->field_f
    //     0xb3116c: ldur            w1, [x3, #0xf]
    // 0xb31170: DecompressPointer r1
    //     0xb31170: add             x1, x1, HEAP, lsl #32
    // 0xb31174: mov             x0, x1
    // 0xb31178: b               #0xb31318
    // 0xb3117c: cmp             w3, NULL
    // 0xb31180: b.ne            #0xb3118c
    // 0xb31184: r1 = Null
    //     0xb31184: mov             x1, NULL
    // 0xb31188: b               #0xb31194
    // 0xb3118c: LoadField: r1 = r3->field_f
    //     0xb3118c: ldur            w1, [x3, #0xf]
    // 0xb31190: DecompressPointer r1
    //     0xb31190: add             x1, x1, HEAP, lsl #32
    // 0xb31194: mov             x0, x1
    // 0xb31198: b               #0xb31318
    // 0xb3119c: cmp             w3, NULL
    // 0xb311a0: b.ne            #0xb311ac
    // 0xb311a4: r1 = Null
    //     0xb311a4: mov             x1, NULL
    // 0xb311a8: b               #0xb311b4
    // 0xb311ac: LoadField: r1 = r3->field_13
    //     0xb311ac: ldur            w1, [x3, #0x13]
    // 0xb311b0: DecompressPointer r1
    //     0xb311b0: add             x1, x1, HEAP, lsl #32
    // 0xb311b4: mov             x0, x1
    // 0xb311b8: b               #0xb31318
    // 0xb311bc: cmp             w3, NULL
    // 0xb311c0: b.ne            #0xb311cc
    // 0xb311c4: r1 = Null
    //     0xb311c4: mov             x1, NULL
    // 0xb311c8: b               #0xb311d4
    // 0xb311cc: LoadField: r1 = r3->field_13
    //     0xb311cc: ldur            w1, [x3, #0x13]
    // 0xb311d0: DecompressPointer r1
    //     0xb311d0: add             x1, x1, HEAP, lsl #32
    // 0xb311d4: mov             x0, x1
    // 0xb311d8: b               #0xb31318
    // 0xb311dc: cmp             w3, NULL
    // 0xb311e0: b.ne            #0xb311ec
    // 0xb311e4: r1 = Null
    //     0xb311e4: mov             x1, NULL
    // 0xb311e8: b               #0xb311f4
    // 0xb311ec: LoadField: r1 = r3->field_1b
    //     0xb311ec: ldur            w1, [x3, #0x1b]
    // 0xb311f0: DecompressPointer r1
    //     0xb311f0: add             x1, x1, HEAP, lsl #32
    // 0xb311f4: mov             x0, x1
    // 0xb311f8: b               #0xb31318
    // 0xb311fc: cmp             w3, NULL
    // 0xb31200: b.ne            #0xb3120c
    // 0xb31204: r1 = Null
    //     0xb31204: mov             x1, NULL
    // 0xb31208: b               #0xb31214
    // 0xb3120c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb3120c: ldur            w1, [x3, #0x17]
    // 0xb31210: DecompressPointer r1
    //     0xb31210: add             x1, x1, HEAP, lsl #32
    // 0xb31214: mov             x0, x1
    // 0xb31218: b               #0xb31318
    // 0xb3121c: cmp             w3, NULL
    // 0xb31220: b.ne            #0xb3122c
    // 0xb31224: r1 = Null
    //     0xb31224: mov             x1, NULL
    // 0xb31228: b               #0xb31234
    // 0xb3122c: LoadField: r1 = r3->field_1b
    //     0xb3122c: ldur            w1, [x3, #0x1b]
    // 0xb31230: DecompressPointer r1
    //     0xb31230: add             x1, x1, HEAP, lsl #32
    // 0xb31234: mov             x0, x1
    // 0xb31238: b               #0xb31318
    // 0xb3123c: cmp             w3, NULL
    // 0xb31240: b.ne            #0xb3124c
    // 0xb31244: r1 = Null
    //     0xb31244: mov             x1, NULL
    // 0xb31248: b               #0xb31254
    // 0xb3124c: LoadField: r1 = r3->field_13
    //     0xb3124c: ldur            w1, [x3, #0x13]
    // 0xb31250: DecompressPointer r1
    //     0xb31250: add             x1, x1, HEAP, lsl #32
    // 0xb31254: mov             x0, x1
    // 0xb31258: b               #0xb31318
    // 0xb3125c: cmp             w3, NULL
    // 0xb31260: b.ne            #0xb3126c
    // 0xb31264: r1 = Null
    //     0xb31264: mov             x1, NULL
    // 0xb31268: b               #0xb31274
    // 0xb3126c: LoadField: r1 = r3->field_13
    //     0xb3126c: ldur            w1, [x3, #0x13]
    // 0xb31270: DecompressPointer r1
    //     0xb31270: add             x1, x1, HEAP, lsl #32
    // 0xb31274: mov             x0, x1
    // 0xb31278: b               #0xb31318
    // 0xb3127c: cmp             w3, NULL
    // 0xb31280: b.ne            #0xb3128c
    // 0xb31284: r1 = Null
    //     0xb31284: mov             x1, NULL
    // 0xb31288: b               #0xb31294
    // 0xb3128c: LoadField: r1 = r3->field_b
    //     0xb3128c: ldur            w1, [x3, #0xb]
    // 0xb31290: DecompressPointer r1
    //     0xb31290: add             x1, x1, HEAP, lsl #32
    // 0xb31294: mov             x0, x1
    // 0xb31298: b               #0xb31318
    // 0xb3129c: cmp             w3, NULL
    // 0xb312a0: b.ne            #0xb312ac
    // 0xb312a4: r1 = Null
    //     0xb312a4: mov             x1, NULL
    // 0xb312a8: b               #0xb312b4
    // 0xb312ac: LoadField: r1 = r3->field_b
    //     0xb312ac: ldur            w1, [x3, #0xb]
    // 0xb312b0: DecompressPointer r1
    //     0xb312b0: add             x1, x1, HEAP, lsl #32
    // 0xb312b4: mov             x0, x1
    // 0xb312b8: b               #0xb31318
    // 0xb312bc: cmp             w3, NULL
    // 0xb312c0: b.ne            #0xb312cc
    // 0xb312c4: r1 = Null
    //     0xb312c4: mov             x1, NULL
    // 0xb312c8: b               #0xb312d4
    // 0xb312cc: LoadField: r1 = r3->field_b
    //     0xb312cc: ldur            w1, [x3, #0xb]
    // 0xb312d0: DecompressPointer r1
    //     0xb312d0: add             x1, x1, HEAP, lsl #32
    // 0xb312d4: mov             x0, x1
    // 0xb312d8: b               #0xb31318
    // 0xb312dc: cmp             w3, NULL
    // 0xb312e0: b.ne            #0xb312ec
    // 0xb312e4: r1 = Null
    //     0xb312e4: mov             x1, NULL
    // 0xb312e8: b               #0xb312f4
    // 0xb312ec: LoadField: r1 = r3->field_b
    //     0xb312ec: ldur            w1, [x3, #0xb]
    // 0xb312f0: DecompressPointer r1
    //     0xb312f0: add             x1, x1, HEAP, lsl #32
    // 0xb312f4: mov             x0, x1
    // 0xb312f8: b               #0xb31318
    // 0xb312fc: cmp             w3, NULL
    // 0xb31300: b.ne            #0xb3130c
    // 0xb31304: r1 = Null
    //     0xb31304: mov             x1, NULL
    // 0xb31308: b               #0xb31314
    // 0xb3130c: LoadField: r1 = r3->field_b
    //     0xb3130c: ldur            w1, [x3, #0xb]
    // 0xb31310: DecompressPointer r1
    //     0xb31310: add             x1, x1, HEAP, lsl #32
    // 0xb31314: mov             x0, x1
    // 0xb31318: LeaveFrame
    //     0xb31318: mov             SP, fp
    //     0xb3131c: ldp             fp, lr, [SP], #0x10
    // 0xb31320: ret
    //     0xb31320: ret             
    // 0xb31324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb31324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb31328: b               #0xb31048
  }
  String _enumToString(TajweedType) {
    // ** addr: 0xc4d118, size: 0x64
    // 0xc4d118: EnterFrame
    //     0xc4d118: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d11c: mov             fp, SP
    // 0xc4d120: AllocStack(0x10)
    //     0xc4d120: sub             SP, SP, #0x10
    // 0xc4d124: SetupParameters(TajweedType this /* r1 => r0, fp-0x8 */)
    //     0xc4d124: mov             x0, x1
    //     0xc4d128: stur            x1, [fp, #-8]
    // 0xc4d12c: CheckStackOverflow
    //     0xc4d12c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d130: cmp             SP, x16
    //     0xc4d134: b.ls            #0xc4d174
    // 0xc4d138: r1 = Null
    //     0xc4d138: mov             x1, NULL
    // 0xc4d13c: r2 = 4
    //     0xc4d13c: movz            x2, #0x4
    // 0xc4d140: r0 = AllocateArray()
    //     0xc4d140: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d144: r16 = "TajweedType."
    //     0xc4d144: add             x16, PP, #0x37, lsl #12  ; [pp+0x37f68] "TajweedType."
    //     0xc4d148: ldr             x16, [x16, #0xf68]
    // 0xc4d14c: StoreField: r0->field_f = r16
    //     0xc4d14c: stur            w16, [x0, #0xf]
    // 0xc4d150: ldur            x1, [fp, #-8]
    // 0xc4d154: LoadField: r2 = r1->field_f
    //     0xc4d154: ldur            w2, [x1, #0xf]
    // 0xc4d158: DecompressPointer r2
    //     0xc4d158: add             x2, x2, HEAP, lsl #32
    // 0xc4d15c: StoreField: r0->field_13 = r2
    //     0xc4d15c: stur            w2, [x0, #0x13]
    // 0xc4d160: str             x0, [SP]
    // 0xc4d164: r0 = _interpolate()
    //     0xc4d164: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d168: LeaveFrame
    //     0xc4d168: mov             SP, fp
    //     0xc4d16c: ldp             fp, lr, [SP], #0x10
    // 0xc4d170: ret
    //     0xc4d170: ret             
    // 0xc4d174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d178: b               #0xc4d138
  }
}
