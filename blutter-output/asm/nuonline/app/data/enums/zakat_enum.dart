// lib: , url: package:nuonline/app/data/enums/zakat_enum.dart

// class id: 1050001, size: 0x8
class :: {

  static _ ZakatTypesExtension.id(/* No info */) {
    // ** addr: 0x72a57c, size: 0x164
    // 0x72a57c: EnterFrame
    //     0x72a57c: stp             fp, lr, [SP, #-0x10]!
    //     0x72a580: mov             fp, SP
    // 0x72a584: LoadField: r2 = r1->field_7
    //     0x72a584: ldur            x2, [x1, #7]
    // 0x72a588: cmp             x2, #6
    // 0x72a58c: b.gt            #0x72a630
    // 0x72a590: cmp             x2, #3
    // 0x72a594: b.gt            #0x72a5f0
    // 0x72a598: cmp             x2, #1
    // 0x72a59c: b.gt            #0x72a5c8
    // 0x72a5a0: cmp             x2, #0
    // 0x72a5a4: b.gt            #0x72a5b8
    // 0x72a5a8: r0 = 2
    //     0x72a5a8: movz            x0, #0x2
    // 0x72a5ac: LeaveFrame
    //     0x72a5ac: mov             SP, fp
    //     0x72a5b0: ldp             fp, lr, [SP], #0x10
    // 0x72a5b4: ret
    //     0x72a5b4: ret             
    // 0x72a5b8: r0 = 3
    //     0x72a5b8: movz            x0, #0x3
    // 0x72a5bc: LeaveFrame
    //     0x72a5bc: mov             SP, fp
    //     0x72a5c0: ldp             fp, lr, [SP], #0x10
    // 0x72a5c4: ret
    //     0x72a5c4: ret             
    // 0x72a5c8: cmp             x2, #2
    // 0x72a5cc: b.gt            #0x72a5e0
    // 0x72a5d0: r0 = 4
    //     0x72a5d0: movz            x0, #0x4
    // 0x72a5d4: LeaveFrame
    //     0x72a5d4: mov             SP, fp
    //     0x72a5d8: ldp             fp, lr, [SP], #0x10
    // 0x72a5dc: ret
    //     0x72a5dc: ret             
    // 0x72a5e0: r0 = 5
    //     0x72a5e0: movz            x0, #0x5
    // 0x72a5e4: LeaveFrame
    //     0x72a5e4: mov             SP, fp
    //     0x72a5e8: ldp             fp, lr, [SP], #0x10
    // 0x72a5ec: ret
    //     0x72a5ec: ret             
    // 0x72a5f0: cmp             x2, #5
    // 0x72a5f4: b.gt            #0x72a620
    // 0x72a5f8: cmp             x2, #4
    // 0x72a5fc: b.gt            #0x72a610
    // 0x72a600: r0 = 6
    //     0x72a600: movz            x0, #0x6
    // 0x72a604: LeaveFrame
    //     0x72a604: mov             SP, fp
    //     0x72a608: ldp             fp, lr, [SP], #0x10
    // 0x72a60c: ret
    //     0x72a60c: ret             
    // 0x72a610: r0 = 7
    //     0x72a610: movz            x0, #0x7
    // 0x72a614: LeaveFrame
    //     0x72a614: mov             SP, fp
    //     0x72a618: ldp             fp, lr, [SP], #0x10
    // 0x72a61c: ret
    //     0x72a61c: ret             
    // 0x72a620: r0 = 8
    //     0x72a620: movz            x0, #0x8
    // 0x72a624: LeaveFrame
    //     0x72a624: mov             SP, fp
    //     0x72a628: ldp             fp, lr, [SP], #0x10
    // 0x72a62c: ret
    //     0x72a62c: ret             
    // 0x72a630: cmp             x2, #9
    // 0x72a634: b.gt            #0x72a678
    // 0x72a638: cmp             x2, #8
    // 0x72a63c: b.gt            #0x72a668
    // 0x72a640: cmp             x2, #7
    // 0x72a644: b.gt            #0x72a658
    // 0x72a648: r0 = 9
    //     0x72a648: movz            x0, #0x9
    // 0x72a64c: LeaveFrame
    //     0x72a64c: mov             SP, fp
    //     0x72a650: ldp             fp, lr, [SP], #0x10
    // 0x72a654: ret
    //     0x72a654: ret             
    // 0x72a658: r0 = 10
    //     0x72a658: movz            x0, #0xa
    // 0x72a65c: LeaveFrame
    //     0x72a65c: mov             SP, fp
    //     0x72a660: ldp             fp, lr, [SP], #0x10
    // 0x72a664: ret
    //     0x72a664: ret             
    // 0x72a668: r0 = 11
    //     0x72a668: movz            x0, #0xb
    // 0x72a66c: LeaveFrame
    //     0x72a66c: mov             SP, fp
    //     0x72a670: ldp             fp, lr, [SP], #0x10
    // 0x72a674: ret
    //     0x72a674: ret             
    // 0x72a678: cmp             x2, #0xb
    // 0x72a67c: b.gt            #0x72a6a8
    // 0x72a680: cmp             x2, #0xa
    // 0x72a684: b.gt            #0x72a698
    // 0x72a688: r0 = 12
    //     0x72a688: movz            x0, #0xc
    // 0x72a68c: LeaveFrame
    //     0x72a68c: mov             SP, fp
    //     0x72a690: ldp             fp, lr, [SP], #0x10
    // 0x72a694: ret
    //     0x72a694: ret             
    // 0x72a698: r0 = 1
    //     0x72a698: movz            x0, #0x1
    // 0x72a69c: LeaveFrame
    //     0x72a69c: mov             SP, fp
    //     0x72a6a0: ldp             fp, lr, [SP], #0x10
    // 0x72a6a4: ret
    //     0x72a6a4: ret             
    // 0x72a6a8: r0 = BoxInt64Instr(r2)
    //     0x72a6a8: sbfiz           x0, x2, #1, #0x1f
    //     0x72a6ac: cmp             x2, x0, asr #1
    //     0x72a6b0: b.eq            #0x72a6bc
    //     0x72a6b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72a6b8: stur            x2, [x0, #7]
    // 0x72a6bc: cmp             w0, #0x18
    // 0x72a6c0: b.ne            #0x72a6d4
    // 0x72a6c4: r0 = 13
    //     0x72a6c4: movz            x0, #0xd
    // 0x72a6c8: LeaveFrame
    //     0x72a6c8: mov             SP, fp
    //     0x72a6cc: ldp             fp, lr, [SP], #0x10
    // 0x72a6d0: ret
    //     0x72a6d0: ret             
    // 0x72a6d4: r0 = UnimplementedError()
    //     0x72a6d4: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x72a6d8: r0 = Throw()
    //     0x72a6d8: bl              #0xec04b8  ; ThrowStub
    // 0x72a6dc: brk             #0
  }
  static _ ZakatTypesExtension.route(/* No info */) {
    // ** addr: 0xaf0bd0, size: 0x268
    // 0xaf0bd0: EnterFrame
    //     0xaf0bd0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0bd4: mov             fp, SP
    // 0xaf0bd8: AllocStack(0x8)
    //     0xaf0bd8: sub             SP, SP, #8
    // 0xaf0bdc: CheckStackOverflow
    //     0xaf0bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0be0: cmp             SP, x16
    //     0xaf0be4: b.ls            #0xaf0e30
    // 0xaf0be8: LoadField: r2 = r1->field_7
    //     0xaf0be8: ldur            x2, [x1, #7]
    // 0xaf0bec: cmp             x2, #5
    // 0xaf0bf0: b.gt            #0xaf0cf4
    // 0xaf0bf4: cmp             x2, #3
    // 0xaf0bf8: b.gt            #0xaf0c94
    // 0xaf0bfc: cmp             x2, #2
    // 0xaf0c00: b.gt            #0xaf0c50
    // 0xaf0c04: cmp             x2, #1
    // 0xaf0c08: b.gt            #0xaf0c3c
    // 0xaf0c0c: r0 = BoxInt64Instr(r2)
    //     0xaf0c0c: sbfiz           x0, x2, #1, #0x1f
    //     0xaf0c10: cmp             x2, x0, asr #1
    //     0xaf0c14: b.eq            #0xaf0c20
    //     0xaf0c18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf0c1c: stur            x2, [x0, #7]
    // 0xaf0c20: cmp             w0, #2
    // 0xaf0c24: b.ne            #0xaf0e24
    // 0xaf0c28: r0 = "/zakat"
    //     0xaf0c28: add             x0, PP, #0xf, lsl #12  ; [pp+0xfcf8] "/zakat"
    //     0xaf0c2c: ldr             x0, [x0, #0xcf8]
    // 0xaf0c30: LeaveFrame
    //     0xaf0c30: mov             SP, fp
    //     0xaf0c34: ldp             fp, lr, [SP], #0x10
    // 0xaf0c38: ret
    //     0xaf0c38: ret             
    // 0xaf0c3c: r0 = "/zakat/zakat-pertanian"
    //     0xaf0c3c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35238] "/zakat/zakat-pertanian"
    //     0xaf0c40: ldr             x0, [x0, #0x238]
    // 0xaf0c44: LeaveFrame
    //     0xaf0c44: mov             SP, fp
    //     0xaf0c48: ldp             fp, lr, [SP], #0x10
    // 0xaf0c4c: ret
    //     0xaf0c4c: ret             
    // 0xaf0c50: r1 = Null
    //     0xaf0c50: mov             x1, NULL
    // 0xaf0c54: r2 = 6
    //     0xaf0c54: movz            x2, #0x6
    // 0xaf0c58: r0 = AllocateArray()
    //     0xaf0c58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0c5c: r16 = "/zakat/zakat-perdagangan"
    //     0xaf0c5c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35240] "/zakat/zakat-perdagangan"
    //     0xaf0c60: ldr             x16, [x16, #0x240]
    // 0xaf0c64: StoreField: r0->field_f = r16
    //     0xaf0c64: stur            w16, [x0, #0xf]
    // 0xaf0c68: r16 = "\?zakat_type="
    //     0xaf0c68: add             x16, PP, #0x35, lsl #12  ; [pp+0x35248] "\?zakat_type="
    //     0xaf0c6c: ldr             x16, [x16, #0x248]
    // 0xaf0c70: StoreField: r0->field_13 = r16
    //     0xaf0c70: stur            w16, [x0, #0x13]
    // 0xaf0c74: r16 = Instance_ZakatType
    //     0xaf0c74: add             x16, PP, #0x35, lsl #12  ; [pp+0x35250] Obj!ZakatType@e30261
    //     0xaf0c78: ldr             x16, [x16, #0x250]
    // 0xaf0c7c: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf0c7c: stur            w16, [x0, #0x17]
    // 0xaf0c80: str             x0, [SP]
    // 0xaf0c84: r0 = _interpolate()
    //     0xaf0c84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf0c88: LeaveFrame
    //     0xaf0c88: mov             SP, fp
    //     0xaf0c8c: ldp             fp, lr, [SP], #0x10
    // 0xaf0c90: ret
    //     0xaf0c90: ret             
    // 0xaf0c94: cmp             x2, #4
    // 0xaf0c98: b.gt            #0xaf0cb0
    // 0xaf0c9c: r0 = "/zakat/zakat-emas"
    //     0xaf0c9c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35258] "/zakat/zakat-emas"
    //     0xaf0ca0: ldr             x0, [x0, #0x258]
    // 0xaf0ca4: LeaveFrame
    //     0xaf0ca4: mov             SP, fp
    //     0xaf0ca8: ldp             fp, lr, [SP], #0x10
    // 0xaf0cac: ret
    //     0xaf0cac: ret             
    // 0xaf0cb0: r1 = Null
    //     0xaf0cb0: mov             x1, NULL
    // 0xaf0cb4: r2 = 6
    //     0xaf0cb4: movz            x2, #0x6
    // 0xaf0cb8: r0 = AllocateArray()
    //     0xaf0cb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0cbc: r16 = "/zakat/zakat-perdagangan"
    //     0xaf0cbc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35240] "/zakat/zakat-perdagangan"
    //     0xaf0cc0: ldr             x16, [x16, #0x240]
    // 0xaf0cc4: StoreField: r0->field_f = r16
    //     0xaf0cc4: stur            w16, [x0, #0xf]
    // 0xaf0cc8: r16 = "\?zakat_type="
    //     0xaf0cc8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35248] "\?zakat_type="
    //     0xaf0ccc: ldr             x16, [x16, #0x248]
    // 0xaf0cd0: StoreField: r0->field_13 = r16
    //     0xaf0cd0: stur            w16, [x0, #0x13]
    // 0xaf0cd4: r16 = Instance_ZakatType
    //     0xaf0cd4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35260] Obj!ZakatType@e30221
    //     0xaf0cd8: ldr             x16, [x16, #0x260]
    // 0xaf0cdc: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf0cdc: stur            w16, [x0, #0x17]
    // 0xaf0ce0: str             x0, [SP]
    // 0xaf0ce4: r0 = _interpolate()
    //     0xaf0ce4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf0ce8: LeaveFrame
    //     0xaf0ce8: mov             SP, fp
    //     0xaf0cec: ldp             fp, lr, [SP], #0x10
    // 0xaf0cf0: ret
    //     0xaf0cf0: ret             
    // 0xaf0cf4: cmp             x2, #8
    // 0xaf0cf8: b.gt            #0xaf0dd8
    // 0xaf0cfc: cmp             x2, #7
    // 0xaf0d00: b.gt            #0xaf0d94
    // 0xaf0d04: cmp             x2, #6
    // 0xaf0d08: b.gt            #0xaf0d50
    // 0xaf0d0c: r1 = Null
    //     0xaf0d0c: mov             x1, NULL
    // 0xaf0d10: r2 = 6
    //     0xaf0d10: movz            x2, #0x6
    // 0xaf0d14: r0 = AllocateArray()
    //     0xaf0d14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0d18: r16 = "/zakat/zakat-perdagangan"
    //     0xaf0d18: add             x16, PP, #0x35, lsl #12  ; [pp+0x35240] "/zakat/zakat-perdagangan"
    //     0xaf0d1c: ldr             x16, [x16, #0x240]
    // 0xaf0d20: StoreField: r0->field_f = r16
    //     0xaf0d20: stur            w16, [x0, #0xf]
    // 0xaf0d24: r16 = "\?zakat_type="
    //     0xaf0d24: add             x16, PP, #0x35, lsl #12  ; [pp+0x35248] "\?zakat_type="
    //     0xaf0d28: ldr             x16, [x16, #0x248]
    // 0xaf0d2c: StoreField: r0->field_13 = r16
    //     0xaf0d2c: stur            w16, [x0, #0x13]
    // 0xaf0d30: r16 = Instance_ZakatType
    //     0xaf0d30: add             x16, PP, #0x35, lsl #12  ; [pp+0x35268] Obj!ZakatType@e30201
    //     0xaf0d34: ldr             x16, [x16, #0x268]
    // 0xaf0d38: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf0d38: stur            w16, [x0, #0x17]
    // 0xaf0d3c: str             x0, [SP]
    // 0xaf0d40: r0 = _interpolate()
    //     0xaf0d40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf0d44: LeaveFrame
    //     0xaf0d44: mov             SP, fp
    //     0xaf0d48: ldp             fp, lr, [SP], #0x10
    // 0xaf0d4c: ret
    //     0xaf0d4c: ret             
    // 0xaf0d50: r1 = Null
    //     0xaf0d50: mov             x1, NULL
    // 0xaf0d54: r2 = 6
    //     0xaf0d54: movz            x2, #0x6
    // 0xaf0d58: r0 = AllocateArray()
    //     0xaf0d58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0d5c: r16 = "/zakat/zakat-perdagangan"
    //     0xaf0d5c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35240] "/zakat/zakat-perdagangan"
    //     0xaf0d60: ldr             x16, [x16, #0x240]
    // 0xaf0d64: StoreField: r0->field_f = r16
    //     0xaf0d64: stur            w16, [x0, #0xf]
    // 0xaf0d68: r16 = "\?zakat_type="
    //     0xaf0d68: add             x16, PP, #0x35, lsl #12  ; [pp+0x35248] "\?zakat_type="
    //     0xaf0d6c: ldr             x16, [x16, #0x248]
    // 0xaf0d70: StoreField: r0->field_13 = r16
    //     0xaf0d70: stur            w16, [x0, #0x13]
    // 0xaf0d74: r16 = Instance_ZakatType
    //     0xaf0d74: add             x16, PP, #0x35, lsl #12  ; [pp+0x35270] Obj!ZakatType@e301e1
    //     0xaf0d78: ldr             x16, [x16, #0x270]
    // 0xaf0d7c: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf0d7c: stur            w16, [x0, #0x17]
    // 0xaf0d80: str             x0, [SP]
    // 0xaf0d84: r0 = _interpolate()
    //     0xaf0d84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf0d88: LeaveFrame
    //     0xaf0d88: mov             SP, fp
    //     0xaf0d8c: ldp             fp, lr, [SP], #0x10
    // 0xaf0d90: ret
    //     0xaf0d90: ret             
    // 0xaf0d94: r1 = Null
    //     0xaf0d94: mov             x1, NULL
    // 0xaf0d98: r2 = 6
    //     0xaf0d98: movz            x2, #0x6
    // 0xaf0d9c: r0 = AllocateArray()
    //     0xaf0d9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaf0da0: r16 = "/zakat/zakat-perdagangan"
    //     0xaf0da0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35240] "/zakat/zakat-perdagangan"
    //     0xaf0da4: ldr             x16, [x16, #0x240]
    // 0xaf0da8: StoreField: r0->field_f = r16
    //     0xaf0da8: stur            w16, [x0, #0xf]
    // 0xaf0dac: r16 = "\?zakat_type="
    //     0xaf0dac: add             x16, PP, #0x35, lsl #12  ; [pp+0x35248] "\?zakat_type="
    //     0xaf0db0: ldr             x16, [x16, #0x248]
    // 0xaf0db4: StoreField: r0->field_13 = r16
    //     0xaf0db4: stur            w16, [x0, #0x13]
    // 0xaf0db8: r16 = Instance_ZakatType
    //     0xaf0db8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35278] Obj!ZakatType@e301c1
    //     0xaf0dbc: ldr             x16, [x16, #0x278]
    // 0xaf0dc0: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf0dc0: stur            w16, [x0, #0x17]
    // 0xaf0dc4: str             x0, [SP]
    // 0xaf0dc8: r0 = _interpolate()
    //     0xaf0dc8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xaf0dcc: LeaveFrame
    //     0xaf0dcc: mov             SP, fp
    //     0xaf0dd0: ldp             fp, lr, [SP], #0x10
    // 0xaf0dd4: ret
    //     0xaf0dd4: ret             
    // 0xaf0dd8: cmp             x2, #9
    // 0xaf0ddc: b.gt            #0xaf0df4
    // 0xaf0de0: r0 = "/zakat/zakat-properti"
    //     0xaf0de0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35280] "/zakat/zakat-properti"
    //     0xaf0de4: ldr             x0, [x0, #0x280]
    // 0xaf0de8: LeaveFrame
    //     0xaf0de8: mov             SP, fp
    //     0xaf0dec: ldp             fp, lr, [SP], #0x10
    // 0xaf0df0: ret
    //     0xaf0df0: ret             
    // 0xaf0df4: r0 = BoxInt64Instr(r2)
    //     0xaf0df4: sbfiz           x0, x2, #1, #0x1f
    //     0xaf0df8: cmp             x2, x0, asr #1
    //     0xaf0dfc: b.eq            #0xaf0e08
    //     0xaf0e00: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf0e04: stur            x2, [x0, #7]
    // 0xaf0e08: cmp             w0, #0x14
    // 0xaf0e0c: b.ne            #0xaf0e24
    // 0xaf0e10: r0 = "/zakat/zakat-profesi"
    //     0xaf0e10: add             x0, PP, #0x35, lsl #12  ; [pp+0x35288] "/zakat/zakat-profesi"
    //     0xaf0e14: ldr             x0, [x0, #0x288]
    // 0xaf0e18: LeaveFrame
    //     0xaf0e18: mov             SP, fp
    //     0xaf0e1c: ldp             fp, lr, [SP], #0x10
    // 0xaf0e20: ret
    //     0xaf0e20: ret             
    // 0xaf0e24: r0 = UnimplementedError()
    //     0xaf0e24: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xaf0e28: r0 = Throw()
    //     0xaf0e28: bl              #0xec04b8  ; ThrowStub
    // 0xaf0e2c: brk             #0
    // 0xaf0e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf0e30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf0e34: b               #0xaf0be8
  }
  static _ ZakatTypesExtension.title(/* No info */) {
    // ** addr: 0xaf11b4, size: 0x198
    // 0xaf11b4: EnterFrame
    //     0xaf11b4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf11b8: mov             fp, SP
    // 0xaf11bc: LoadField: r2 = r1->field_7
    //     0xaf11bc: ldur            x2, [x1, #7]
    // 0xaf11c0: cmp             x2, #6
    // 0xaf11c4: b.gt            #0xaf1284
    // 0xaf11c8: cmp             x2, #3
    // 0xaf11cc: b.gt            #0xaf1238
    // 0xaf11d0: cmp             x2, #1
    // 0xaf11d4: b.gt            #0xaf1208
    // 0xaf11d8: cmp             x2, #0
    // 0xaf11dc: b.gt            #0xaf11f4
    // 0xaf11e0: r0 = "Zakat Fitrah"
    //     0xaf11e0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34818] "Zakat Fitrah"
    //     0xaf11e4: ldr             x0, [x0, #0x818]
    // 0xaf11e8: LeaveFrame
    //     0xaf11e8: mov             SP, fp
    //     0xaf11ec: ldp             fp, lr, [SP], #0x10
    // 0xaf11f0: ret
    //     0xaf11f0: ret             
    // 0xaf11f4: r0 = "Zakat Mal"
    //     0xaf11f4: add             x0, PP, #0x30, lsl #12  ; [pp+0x302f0] "Zakat Mal"
    //     0xaf11f8: ldr             x0, [x0, #0x2f0]
    // 0xaf11fc: LeaveFrame
    //     0xaf11fc: mov             SP, fp
    //     0xaf1200: ldp             fp, lr, [SP], #0x10
    // 0xaf1204: ret
    //     0xaf1204: ret             
    // 0xaf1208: cmp             x2, #2
    // 0xaf120c: b.gt            #0xaf1224
    // 0xaf1210: r0 = "Zakat Pertanian Tanaman Pangan"
    //     0xaf1210: add             x0, PP, #0x27, lsl #12  ; [pp+0x27890] "Zakat Pertanian Tanaman Pangan"
    //     0xaf1214: ldr             x0, [x0, #0x890]
    // 0xaf1218: LeaveFrame
    //     0xaf1218: mov             SP, fp
    //     0xaf121c: ldp             fp, lr, [SP], #0x10
    // 0xaf1220: ret
    //     0xaf1220: ret             
    // 0xaf1224: r0 = "Zakat Perdagangan"
    //     0xaf1224: add             x0, PP, #0x27, lsl #12  ; [pp+0x27898] "Zakat Perdagangan"
    //     0xaf1228: ldr             x0, [x0, #0x898]
    // 0xaf122c: LeaveFrame
    //     0xaf122c: mov             SP, fp
    //     0xaf1230: ldp             fp, lr, [SP], #0x10
    // 0xaf1234: ret
    //     0xaf1234: ret             
    // 0xaf1238: cmp             x2, #5
    // 0xaf123c: b.gt            #0xaf1270
    // 0xaf1240: cmp             x2, #4
    // 0xaf1244: b.gt            #0xaf125c
    // 0xaf1248: r0 = "Zakat Simpanan Emas, Perak, dan Perhiasan"
    //     0xaf1248: add             x0, PP, #0x27, lsl #12  ; [pp+0x278a0] "Zakat Simpanan Emas, Perak, dan Perhiasan"
    //     0xaf124c: ldr             x0, [x0, #0x8a0]
    // 0xaf1250: LeaveFrame
    //     0xaf1250: mov             SP, fp
    //     0xaf1254: ldp             fp, lr, [SP], #0x10
    // 0xaf1258: ret
    //     0xaf1258: ret             
    // 0xaf125c: r0 = "Zakat Tambak"
    //     0xaf125c: add             x0, PP, #0x27, lsl #12  ; [pp+0x278a8] "Zakat Tambak"
    //     0xaf1260: ldr             x0, [x0, #0x8a8]
    // 0xaf1264: LeaveFrame
    //     0xaf1264: mov             SP, fp
    //     0xaf1268: ldp             fp, lr, [SP], #0x10
    // 0xaf126c: ret
    //     0xaf126c: ret             
    // 0xaf1270: r0 = "Zakat Tanaman Produktif"
    //     0xaf1270: add             x0, PP, #0x27, lsl #12  ; [pp+0x278b0] "Zakat Tanaman Produktif"
    //     0xaf1274: ldr             x0, [x0, #0x8b0]
    // 0xaf1278: LeaveFrame
    //     0xaf1278: mov             SP, fp
    //     0xaf127c: ldp             fp, lr, [SP], #0x10
    // 0xaf1280: ret
    //     0xaf1280: ret             
    // 0xaf1284: cmp             x2, #9
    // 0xaf1288: b.gt            #0xaf12d8
    // 0xaf128c: cmp             x2, #8
    // 0xaf1290: b.gt            #0xaf12c4
    // 0xaf1294: cmp             x2, #7
    // 0xaf1298: b.gt            #0xaf12b0
    // 0xaf129c: r0 = "Zakat Peternakan"
    //     0xaf129c: add             x0, PP, #0x27, lsl #12  ; [pp+0x278b8] "Zakat Peternakan"
    //     0xaf12a0: ldr             x0, [x0, #0x8b8]
    // 0xaf12a4: LeaveFrame
    //     0xaf12a4: mov             SP, fp
    //     0xaf12a8: ldp             fp, lr, [SP], #0x10
    // 0xaf12ac: ret
    //     0xaf12ac: ret             
    // 0xaf12b0: r0 = "Zakat Perusahaan"
    //     0xaf12b0: add             x0, PP, #0x27, lsl #12  ; [pp+0x278c0] "Zakat Perusahaan"
    //     0xaf12b4: ldr             x0, [x0, #0x8c0]
    // 0xaf12b8: LeaveFrame
    //     0xaf12b8: mov             SP, fp
    //     0xaf12bc: ldp             fp, lr, [SP], #0x10
    // 0xaf12c0: ret
    //     0xaf12c0: ret             
    // 0xaf12c4: r0 = "Zakat Properti"
    //     0xaf12c4: add             x0, PP, #0x27, lsl #12  ; [pp+0x278d0] "Zakat Properti"
    //     0xaf12c8: ldr             x0, [x0, #0x8d0]
    // 0xaf12cc: LeaveFrame
    //     0xaf12cc: mov             SP, fp
    //     0xaf12d0: ldp             fp, lr, [SP], #0x10
    // 0xaf12d4: ret
    //     0xaf12d4: ret             
    // 0xaf12d8: cmp             x2, #0xb
    // 0xaf12dc: b.gt            #0xaf1310
    // 0xaf12e0: cmp             x2, #0xa
    // 0xaf12e4: b.gt            #0xaf12fc
    // 0xaf12e8: r0 = "Zakat Profesi"
    //     0xaf12e8: add             x0, PP, #0x27, lsl #12  ; [pp+0x278c8] "Zakat Profesi"
    //     0xaf12ec: ldr             x0, [x0, #0x8c8]
    // 0xaf12f0: LeaveFrame
    //     0xaf12f0: mov             SP, fp
    //     0xaf12f4: ldp             fp, lr, [SP], #0x10
    // 0xaf12f8: ret
    //     0xaf12f8: ret             
    // 0xaf12fc: r0 = "Infak Sedekah"
    //     0xaf12fc: add             x0, PP, #0x35, lsl #12  ; [pp+0x352f0] "Infak Sedekah"
    //     0xaf1300: ldr             x0, [x0, #0x2f0]
    // 0xaf1304: LeaveFrame
    //     0xaf1304: mov             SP, fp
    //     0xaf1308: ldp             fp, lr, [SP], #0x10
    // 0xaf130c: ret
    //     0xaf130c: ret             
    // 0xaf1310: r0 = BoxInt64Instr(r2)
    //     0xaf1310: sbfiz           x0, x2, #1, #0x1f
    //     0xaf1314: cmp             x2, x0, asr #1
    //     0xaf1318: b.eq            #0xaf1324
    //     0xaf131c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf1320: stur            x2, [x0, #7]
    // 0xaf1324: cmp             w0, #0x18
    // 0xaf1328: b.ne            #0xaf1340
    // 0xaf132c: r0 = "Dukung Pengembangan NU Online"
    //     0xaf132c: add             x0, PP, #0x35, lsl #12  ; [pp+0x352f8] "Dukung Pengembangan NU Online"
    //     0xaf1330: ldr             x0, [x0, #0x2f8]
    // 0xaf1334: LeaveFrame
    //     0xaf1334: mov             SP, fp
    //     0xaf1338: ldp             fp, lr, [SP], #0x10
    // 0xaf133c: ret
    //     0xaf133c: ret             
    // 0xaf1340: r0 = UnimplementedError()
    //     0xaf1340: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xaf1344: r0 = Throw()
    //     0xaf1344: bl              #0xec04b8  ; ThrowStub
    // 0xaf1348: brk             #0
  }
}

// class id: 5602, size: 0x14, field offset: 0x8
//   const constructor, 
class ZakatQuality extends Equatable {

  factory _ ZakatQuality.premium(/* No info */) {
    // ** addr: 0x7db25c, size: 0x8c
    // 0x7db25c: EnterFrame
    //     0x7db25c: stp             fp, lr, [SP, #-0x10]!
    //     0x7db260: mov             fp, SP
    // 0x7db264: AllocStack(0x8)
    //     0x7db264: sub             SP, SP, #8
    // 0x7db268: SetupParameters({dynamic price = Null /* r0 */})
    //     0x7db268: ldur            w0, [x4, #0x13]
    //     0x7db26c: ldur            w1, [x4, #0x1f]
    //     0x7db270: add             x1, x1, HEAP, lsl #32
    //     0x7db274: add             x16, PP, #0x27, lsl #12  ; [pp+0x273b8] "price"
    //     0x7db278: ldr             x16, [x16, #0x3b8]
    //     0x7db27c: cmp             w1, w16
    //     0x7db280: b.ne            #0x7db29c
    //     0x7db284: ldur            w1, [x4, #0x23]
    //     0x7db288: add             x1, x1, HEAP, lsl #32
    //     0x7db28c: sub             w2, w0, w1
    //     0x7db290: add             x0, fp, w2, sxtw #2
    //     0x7db294: ldr             x0, [x0, #8]
    //     0x7db298: b               #0x7db2a0
    //     0x7db29c: mov             x0, NULL
    // 0x7db2a0: cmp             w0, NULL
    // 0x7db2a4: b.ne            #0x7db2b0
    // 0x7db2a8: r0 = 45000
    //     0x7db2a8: movz            x0, #0xafc8
    // 0x7db2ac: b               #0x7db2c0
    // 0x7db2b0: r1 = LoadInt32Instr(r0)
    //     0x7db2b0: sbfx            x1, x0, #1, #0x1f
    //     0x7db2b4: tbz             w0, #0, #0x7db2bc
    //     0x7db2b8: ldur            x1, [x0, #7]
    // 0x7db2bc: mov             x0, x1
    // 0x7db2c0: stur            x0, [fp, #-8]
    // 0x7db2c4: r0 = ZakatQuality()
    //     0x7db2c4: bl              #0x7db3d0  ; AllocateZakatQualityStub -> ZakatQuality (size=0x14)
    // 0x7db2c8: r1 = "Kualitas Premium"
    //     0x7db2c8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30058] "Kualitas Premium"
    //     0x7db2cc: ldr             x1, [x1, #0x58]
    // 0x7db2d0: StoreField: r0->field_7 = r1
    //     0x7db2d0: stur            w1, [x0, #7]
    // 0x7db2d4: ldur            x1, [fp, #-8]
    // 0x7db2d8: StoreField: r0->field_b = r1
    //     0x7db2d8: stur            x1, [x0, #0xb]
    // 0x7db2dc: LeaveFrame
    //     0x7db2dc: mov             SP, fp
    //     0x7db2e0: ldp             fp, lr, [SP], #0x10
    // 0x7db2e4: ret
    //     0x7db2e4: ret             
  }
  factory _ ZakatQuality.medium(/* No info */) {
    // ** addr: 0x7db39c, size: 0x34
    // 0x7db39c: EnterFrame
    //     0x7db39c: stp             fp, lr, [SP, #-0x10]!
    //     0x7db3a0: mov             fp, SP
    // 0x7db3a4: AllocStack(0x8)
    //     0x7db3a4: sub             SP, SP, #8
    // 0x7db3a8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x7db3a8: stur            x2, [fp, #-8]
    // 0x7db3ac: r0 = ZakatQuality()
    //     0x7db3ac: bl              #0x7db3d0  ; AllocateZakatQualityStub -> ZakatQuality (size=0x14)
    // 0x7db3b0: r1 = "Kualitas Medium"
    //     0x7db3b0: add             x1, PP, #0x35, lsl #12  ; [pp+0x357a0] "Kualitas Medium"
    //     0x7db3b4: ldr             x1, [x1, #0x7a0]
    // 0x7db3b8: StoreField: r0->field_7 = r1
    //     0x7db3b8: stur            w1, [x0, #7]
    // 0x7db3bc: ldur            x1, [fp, #-8]
    // 0x7db3c0: StoreField: r0->field_b = r1
    //     0x7db3c0: stur            x1, [x0, #0xb]
    // 0x7db3c4: LeaveFrame
    //     0x7db3c4: mov             SP, fp
    //     0x7db3c8: ldp             fp, lr, [SP], #0x10
    // 0x7db3cc: ret
    //     0x7db3cc: ret             
  }
}

// class id: 6839, size: 0x14, field offset: 0x14
enum ZakatTypes extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d17c, size: 0x64
    // 0xc4d17c: EnterFrame
    //     0xc4d17c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d180: mov             fp, SP
    // 0xc4d184: AllocStack(0x10)
    //     0xc4d184: sub             SP, SP, #0x10
    // 0xc4d188: SetupParameters(ZakatTypes this /* r1 => r0, fp-0x8 */)
    //     0xc4d188: mov             x0, x1
    //     0xc4d18c: stur            x1, [fp, #-8]
    // 0xc4d190: CheckStackOverflow
    //     0xc4d190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d194: cmp             SP, x16
    //     0xc4d198: b.ls            #0xc4d1d8
    // 0xc4d19c: r1 = Null
    //     0xc4d19c: mov             x1, NULL
    // 0xc4d1a0: r2 = 4
    //     0xc4d1a0: movz            x2, #0x4
    // 0xc4d1a4: r0 = AllocateArray()
    //     0xc4d1a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d1a8: r16 = "ZakatTypes."
    //     0xc4d1a8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30c10] "ZakatTypes."
    //     0xc4d1ac: ldr             x16, [x16, #0xc10]
    // 0xc4d1b0: StoreField: r0->field_f = r16
    //     0xc4d1b0: stur            w16, [x0, #0xf]
    // 0xc4d1b4: ldur            x1, [fp, #-8]
    // 0xc4d1b8: LoadField: r2 = r1->field_f
    //     0xc4d1b8: ldur            w2, [x1, #0xf]
    // 0xc4d1bc: DecompressPointer r2
    //     0xc4d1bc: add             x2, x2, HEAP, lsl #32
    // 0xc4d1c0: StoreField: r0->field_13 = r2
    //     0xc4d1c0: stur            w2, [x0, #0x13]
    // 0xc4d1c4: str             x0, [SP]
    // 0xc4d1c8: r0 = _interpolate()
    //     0xc4d1c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d1cc: LeaveFrame
    //     0xc4d1cc: mov             SP, fp
    //     0xc4d1d0: ldp             fp, lr, [SP], #0x10
    // 0xc4d1d4: ret
    //     0xc4d1d4: ret             
    // 0xc4d1d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d1d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d1dc: b               #0xc4d19c
  }
}
