// lib: , url: package:nuonline/app/data/models/ad.dart

// class id: 1050002, size: 0x8
class :: {
}

// class id: 5601, size: 0x18, field offset: 0x8
//   const constructor, 
class Ad extends Equatable {

  factory Ad Ad.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb748, size: 0x170
    // 0xafb748: EnterFrame
    //     0xafb748: stp             fp, lr, [SP, #-0x10]!
    //     0xafb74c: mov             fp, SP
    // 0xafb750: AllocStack(0x20)
    //     0xafb750: sub             SP, SP, #0x20
    // 0xafb754: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xafb754: mov             x3, x2
    //     0xafb758: stur            x2, [fp, #-8]
    // 0xafb75c: CheckStackOverflow
    //     0xafb75c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb760: cmp             SP, x16
    //     0xafb764: b.ls            #0xafb8b0
    // 0xafb768: r0 = LoadClassIdInstr(r3)
    //     0xafb768: ldur            x0, [x3, #-1]
    //     0xafb76c: ubfx            x0, x0, #0xc, #0x14
    // 0xafb770: mov             x1, x3
    // 0xafb774: r2 = "id"
    //     0xafb774: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xafb778: ldr             x2, [x2, #0x740]
    // 0xafb77c: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb77c: sub             lr, x0, #0x114
    //     0xafb780: ldr             lr, [x21, lr, lsl #3]
    //     0xafb784: blr             lr
    // 0xafb788: mov             x3, x0
    // 0xafb78c: r2 = Null
    //     0xafb78c: mov             x2, NULL
    // 0xafb790: r1 = Null
    //     0xafb790: mov             x1, NULL
    // 0xafb794: stur            x3, [fp, #-0x10]
    // 0xafb798: branchIfSmi(r0, 0xafb7c0)
    //     0xafb798: tbz             w0, #0, #0xafb7c0
    // 0xafb79c: r4 = LoadClassIdInstr(r0)
    //     0xafb79c: ldur            x4, [x0, #-1]
    //     0xafb7a0: ubfx            x4, x4, #0xc, #0x14
    // 0xafb7a4: sub             x4, x4, #0x3c
    // 0xafb7a8: cmp             x4, #1
    // 0xafb7ac: b.ls            #0xafb7c0
    // 0xafb7b0: r8 = int
    //     0xafb7b0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xafb7b4: r3 = Null
    //     0xafb7b4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddb8] Null
    //     0xafb7b8: ldr             x3, [x3, #0xdb8]
    // 0xafb7bc: r0 = int()
    //     0xafb7bc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xafb7c0: ldur            x3, [fp, #-8]
    // 0xafb7c4: r0 = LoadClassIdInstr(r3)
    //     0xafb7c4: ldur            x0, [x3, #-1]
    //     0xafb7c8: ubfx            x0, x0, #0xc, #0x14
    // 0xafb7cc: mov             x1, x3
    // 0xafb7d0: r2 = "url"
    //     0xafb7d0: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0xafb7d4: ldr             x2, [x2, #0xd78]
    // 0xafb7d8: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb7d8: sub             lr, x0, #0x114
    //     0xafb7dc: ldr             lr, [x21, lr, lsl #3]
    //     0xafb7e0: blr             lr
    // 0xafb7e4: mov             x3, x0
    // 0xafb7e8: r2 = Null
    //     0xafb7e8: mov             x2, NULL
    // 0xafb7ec: r1 = Null
    //     0xafb7ec: mov             x1, NULL
    // 0xafb7f0: stur            x3, [fp, #-0x18]
    // 0xafb7f4: r4 = 60
    //     0xafb7f4: movz            x4, #0x3c
    // 0xafb7f8: branchIfSmi(r0, 0xafb804)
    //     0xafb7f8: tbz             w0, #0, #0xafb804
    // 0xafb7fc: r4 = LoadClassIdInstr(r0)
    //     0xafb7fc: ldur            x4, [x0, #-1]
    //     0xafb800: ubfx            x4, x4, #0xc, #0x14
    // 0xafb804: sub             x4, x4, #0x5e
    // 0xafb808: cmp             x4, #1
    // 0xafb80c: b.ls            #0xafb820
    // 0xafb810: r8 = String
    //     0xafb810: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xafb814: r3 = Null
    //     0xafb814: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddc8] Null
    //     0xafb818: ldr             x3, [x3, #0xdc8]
    // 0xafb81c: r0 = String()
    //     0xafb81c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xafb820: ldur            x1, [fp, #-8]
    // 0xafb824: r0 = LoadClassIdInstr(r1)
    //     0xafb824: ldur            x0, [x1, #-1]
    //     0xafb828: ubfx            x0, x0, #0xc, #0x14
    // 0xafb82c: r2 = "image"
    //     0xafb82c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xafb830: ldr             x2, [x2, #0x520]
    // 0xafb834: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb834: sub             lr, x0, #0x114
    //     0xafb838: ldr             lr, [x21, lr, lsl #3]
    //     0xafb83c: blr             lr
    // 0xafb840: mov             x3, x0
    // 0xafb844: r2 = Null
    //     0xafb844: mov             x2, NULL
    // 0xafb848: r1 = Null
    //     0xafb848: mov             x1, NULL
    // 0xafb84c: stur            x3, [fp, #-8]
    // 0xafb850: r8 = Map<String, dynamic>
    //     0xafb850: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xafb854: r3 = Null
    //     0xafb854: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddd8] Null
    //     0xafb858: ldr             x3, [x3, #0xdd8]
    // 0xafb85c: r0 = Map<String, dynamic>()
    //     0xafb85c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xafb860: ldur            x2, [fp, #-8]
    // 0xafb864: r1 = Null
    //     0xafb864: mov             x1, NULL
    // 0xafb868: r0 = Image.fromMap()
    //     0xafb868: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xafb86c: mov             x1, x0
    // 0xafb870: ldur            x0, [fp, #-0x10]
    // 0xafb874: stur            x1, [fp, #-8]
    // 0xafb878: r2 = LoadInt32Instr(r0)
    //     0xafb878: sbfx            x2, x0, #1, #0x1f
    //     0xafb87c: tbz             w0, #0, #0xafb884
    //     0xafb880: ldur            x2, [x0, #7]
    // 0xafb884: stur            x2, [fp, #-0x20]
    // 0xafb888: r0 = Ad()
    //     0xafb888: bl              #0xafb8dc  ; AllocateAdStub -> Ad (size=0x18)
    // 0xafb88c: ldur            x1, [fp, #-0x20]
    // 0xafb890: StoreField: r0->field_7 = r1
    //     0xafb890: stur            x1, [x0, #7]
    // 0xafb894: ldur            x1, [fp, #-0x18]
    // 0xafb898: StoreField: r0->field_f = r1
    //     0xafb898: stur            w1, [x0, #0xf]
    // 0xafb89c: ldur            x1, [fp, #-8]
    // 0xafb8a0: StoreField: r0->field_13 = r1
    //     0xafb8a0: stur            w1, [x0, #0x13]
    // 0xafb8a4: LeaveFrame
    //     0xafb8a4: mov             SP, fp
    //     0xafb8a8: ldp             fp, lr, [SP], #0x10
    // 0xafb8ac: ret
    //     0xafb8ac: ret             
    // 0xafb8b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb8b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb8b4: b               #0xafb768
  }
}
