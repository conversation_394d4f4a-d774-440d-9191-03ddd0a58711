// lib: , url: package:nuonline/app/data/models/ads_config.dart

// class id: 1050003, size: 0x8
class :: {
}

// class id: 1162, size: 0x14, field offset: 0x8
class AdsItems extends Object {

  Map<String, dynamic> to<PERSON>son(AdsItems) {
    // ** addr: 0xa366a8, size: 0xa0
    // 0xa366a8: EnterFrame
    //     0xa366a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa366ac: mov             fp, SP
    // 0xa366b0: AllocStack(0x20)
    //     0xa366b0: sub             SP, SP, #0x20
    // 0xa366b4: SetupParameters(AdsItems this /* r1 => r1, fp-0x8 */)
    //     0xa366b4: stur            x1, [fp, #-8]
    // 0xa366b8: CheckStackOverflow
    //     0xa366b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa366bc: cmp             SP, x16
    //     0xa366c0: b.ls            #0xa36740
    // 0xa366c4: r16 = <String, dynamic>
    //     0xa366c4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa366c8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa366cc: stp             lr, x16, [SP]
    // 0xa366d0: r0 = Map._fromLiteral()
    //     0xa366d0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa366d4: mov             x4, x0
    // 0xa366d8: ldur            x0, [fp, #-8]
    // 0xa366dc: stur            x4, [fp, #-0x10]
    // 0xa366e0: LoadField: r3 = r0->field_7
    //     0xa366e0: ldur            w3, [x0, #7]
    // 0xa366e4: DecompressPointer r3
    //     0xa366e4: add             x3, x3, HEAP, lsl #32
    // 0xa366e8: mov             x1, x4
    // 0xa366ec: r2 = "show"
    //     0xa366ec: add             x2, PP, #0x12, lsl #12  ; [pp+0x12480] "show"
    //     0xa366f0: ldr             x2, [x2, #0x480]
    // 0xa366f4: r0 = []=()
    //     0xa366f4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa366f8: ldur            x0, [fp, #-8]
    // 0xa366fc: LoadField: r3 = r0->field_b
    //     0xa366fc: ldur            w3, [x0, #0xb]
    // 0xa36700: DecompressPointer r3
    //     0xa36700: add             x3, x3, HEAP, lsl #32
    // 0xa36704: ldur            x1, [fp, #-0x10]
    // 0xa36708: r2 = "ad_unit"
    //     0xa36708: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e98] "ad_unit"
    //     0xa3670c: ldr             x2, [x2, #0xe98]
    // 0xa36710: r0 = []=()
    //     0xa36710: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36714: ldur            x0, [fp, #-8]
    // 0xa36718: LoadField: r3 = r0->field_f
    //     0xa36718: ldur            w3, [x0, #0xf]
    // 0xa3671c: DecompressPointer r3
    //     0xa3671c: add             x3, x3, HEAP, lsl #32
    // 0xa36720: ldur            x1, [fp, #-0x10]
    // 0xa36724: r2 = "ad_type"
    //     0xa36724: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e80] "ad_type"
    //     0xa36728: ldr             x2, [x2, #0xe80]
    // 0xa3672c: r0 = []=()
    //     0xa3672c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36730: ldur            x0, [fp, #-0x10]
    // 0xa36734: LeaveFrame
    //     0xa36734: mov             SP, fp
    //     0xa36738: ldp             fp, lr, [SP], #0x10
    // 0xa3673c: ret
    //     0xa3673c: ret             
    // 0xa36740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36740: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36744: b               #0xa366c4
  }
  Map<String, dynamic> toJson(AdsItems) {
    // ** addr: 0xa36760, size: 0x48
    // 0xa36760: EnterFrame
    //     0xa36760: stp             fp, lr, [SP, #-0x10]!
    //     0xa36764: mov             fp, SP
    // 0xa36768: CheckStackOverflow
    //     0xa36768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3676c: cmp             SP, x16
    //     0xa36770: b.ls            #0xa36788
    // 0xa36774: ldr             x1, [fp, #0x10]
    // 0xa36778: r0 = toJson()
    //     0xa36778: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa3677c: LeaveFrame
    //     0xa3677c: mov             SP, fp
    //     0xa36780: ldp             fp, lr, [SP], #0x10
    // 0xa36784: ret
    //     0xa36784: ret             
    // 0xa36788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3678c: b               #0xa36774
  }
  factory _ AdsItems.fromJson(/* No info */) {
    // ** addr: 0xa3679c, size: 0x164
    // 0xa3679c: EnterFrame
    //     0xa3679c: stp             fp, lr, [SP, #-0x10]!
    //     0xa367a0: mov             fp, SP
    // 0xa367a4: AllocStack(0x18)
    //     0xa367a4: sub             SP, SP, #0x18
    // 0xa367a8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xa367a8: mov             x3, x2
    //     0xa367ac: stur            x2, [fp, #-8]
    // 0xa367b0: CheckStackOverflow
    //     0xa367b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa367b4: cmp             SP, x16
    //     0xa367b8: b.ls            #0xa368f8
    // 0xa367bc: r0 = LoadClassIdInstr(r3)
    //     0xa367bc: ldur            x0, [x3, #-1]
    //     0xa367c0: ubfx            x0, x0, #0xc, #0x14
    // 0xa367c4: mov             x1, x3
    // 0xa367c8: r2 = "show"
    //     0xa367c8: add             x2, PP, #0x12, lsl #12  ; [pp+0x12480] "show"
    //     0xa367cc: ldr             x2, [x2, #0x480]
    // 0xa367d0: r0 = GDT[cid_x0 + -0x114]()
    //     0xa367d0: sub             lr, x0, #0x114
    //     0xa367d4: ldr             lr, [x21, lr, lsl #3]
    //     0xa367d8: blr             lr
    // 0xa367dc: mov             x3, x0
    // 0xa367e0: r2 = Null
    //     0xa367e0: mov             x2, NULL
    // 0xa367e4: r1 = Null
    //     0xa367e4: mov             x1, NULL
    // 0xa367e8: stur            x3, [fp, #-0x10]
    // 0xa367ec: r4 = 60
    //     0xa367ec: movz            x4, #0x3c
    // 0xa367f0: branchIfSmi(r0, 0xa367fc)
    //     0xa367f0: tbz             w0, #0, #0xa367fc
    // 0xa367f4: r4 = LoadClassIdInstr(r0)
    //     0xa367f4: ldur            x4, [x0, #-1]
    //     0xa367f8: ubfx            x4, x4, #0xc, #0x14
    // 0xa367fc: cmp             x4, #0x3f
    // 0xa36800: b.eq            #0xa36814
    // 0xa36804: r8 = bool
    //     0xa36804: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa36808: r3 = Null
    //     0xa36808: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e70] Null
    //     0xa3680c: ldr             x3, [x3, #0xe70]
    // 0xa36810: r0 = bool()
    //     0xa36810: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa36814: ldur            x3, [fp, #-8]
    // 0xa36818: r0 = LoadClassIdInstr(r3)
    //     0xa36818: ldur            x0, [x3, #-1]
    //     0xa3681c: ubfx            x0, x0, #0xc, #0x14
    // 0xa36820: mov             x1, x3
    // 0xa36824: r2 = "ad_type"
    //     0xa36824: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e80] "ad_type"
    //     0xa36828: ldr             x2, [x2, #0xe80]
    // 0xa3682c: r0 = GDT[cid_x0 + -0x114]()
    //     0xa3682c: sub             lr, x0, #0x114
    //     0xa36830: ldr             lr, [x21, lr, lsl #3]
    //     0xa36834: blr             lr
    // 0xa36838: mov             x3, x0
    // 0xa3683c: r2 = Null
    //     0xa3683c: mov             x2, NULL
    // 0xa36840: r1 = Null
    //     0xa36840: mov             x1, NULL
    // 0xa36844: stur            x3, [fp, #-0x18]
    // 0xa36848: r4 = 60
    //     0xa36848: movz            x4, #0x3c
    // 0xa3684c: branchIfSmi(r0, 0xa36858)
    //     0xa3684c: tbz             w0, #0, #0xa36858
    // 0xa36850: r4 = LoadClassIdInstr(r0)
    //     0xa36850: ldur            x4, [x0, #-1]
    //     0xa36854: ubfx            x4, x4, #0xc, #0x14
    // 0xa36858: sub             x4, x4, #0x5e
    // 0xa3685c: cmp             x4, #1
    // 0xa36860: b.ls            #0xa36874
    // 0xa36864: r8 = String
    //     0xa36864: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa36868: r3 = Null
    //     0xa36868: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e88] Null
    //     0xa3686c: ldr             x3, [x3, #0xe88]
    // 0xa36870: r0 = String()
    //     0xa36870: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa36874: ldur            x1, [fp, #-8]
    // 0xa36878: r0 = LoadClassIdInstr(r1)
    //     0xa36878: ldur            x0, [x1, #-1]
    //     0xa3687c: ubfx            x0, x0, #0xc, #0x14
    // 0xa36880: r2 = "ad_unit"
    //     0xa36880: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e98] "ad_unit"
    //     0xa36884: ldr             x2, [x2, #0xe98]
    // 0xa36888: r0 = GDT[cid_x0 + -0x114]()
    //     0xa36888: sub             lr, x0, #0x114
    //     0xa3688c: ldr             lr, [x21, lr, lsl #3]
    //     0xa36890: blr             lr
    // 0xa36894: mov             x3, x0
    // 0xa36898: r2 = Null
    //     0xa36898: mov             x2, NULL
    // 0xa3689c: r1 = Null
    //     0xa3689c: mov             x1, NULL
    // 0xa368a0: stur            x3, [fp, #-8]
    // 0xa368a4: r4 = 60
    //     0xa368a4: movz            x4, #0x3c
    // 0xa368a8: branchIfSmi(r0, 0xa368b4)
    //     0xa368a8: tbz             w0, #0, #0xa368b4
    // 0xa368ac: r4 = LoadClassIdInstr(r0)
    //     0xa368ac: ldur            x4, [x0, #-1]
    //     0xa368b0: ubfx            x4, x4, #0xc, #0x14
    // 0xa368b4: sub             x4, x4, #0x5e
    // 0xa368b8: cmp             x4, #1
    // 0xa368bc: b.ls            #0xa368d0
    // 0xa368c0: r8 = String?
    //     0xa368c0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa368c4: r3 = Null
    //     0xa368c4: add             x3, PP, #0x26, lsl #12  ; [pp+0x26ea0] Null
    //     0xa368c8: ldr             x3, [x3, #0xea0]
    // 0xa368cc: r0 = String?()
    //     0xa368cc: bl              #0x600324  ; IsType_String?_Stub
    // 0xa368d0: r0 = AdsItems()
    //     0xa368d0: bl              #0xa36900  ; AllocateAdsItemsStub -> AdsItems (size=0x14)
    // 0xa368d4: ldur            x1, [fp, #-0x10]
    // 0xa368d8: StoreField: r0->field_7 = r1
    //     0xa368d8: stur            w1, [x0, #7]
    // 0xa368dc: ldur            x1, [fp, #-0x18]
    // 0xa368e0: StoreField: r0->field_f = r1
    //     0xa368e0: stur            w1, [x0, #0xf]
    // 0xa368e4: ldur            x1, [fp, #-8]
    // 0xa368e8: StoreField: r0->field_b = r1
    //     0xa368e8: stur            w1, [x0, #0xb]
    // 0xa368ec: LeaveFrame
    //     0xa368ec: mov             SP, fp
    //     0xa368f0: ldp             fp, lr, [SP], #0x10
    // 0xa368f4: ret
    //     0xa368f4: ret             
    // 0xa368f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa368f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa368fc: b               #0xa367bc
  }
}

// class id: 1163, size: 0x4c, field offset: 0x8
class AdsConfig extends Object {

  factory _ AdsConfig.fromJson(/* No info */) {
    // ** addr: 0xa35c4c, size: 0x764
    // 0xa35c4c: EnterFrame
    //     0xa35c4c: stp             fp, lr, [SP, #-0x10]!
    //     0xa35c50: mov             fp, SP
    // 0xa35c54: AllocStack(0x88)
    //     0xa35c54: sub             SP, SP, #0x88
    // 0xa35c58: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xa35c58: mov             x0, x2
    //     0xa35c5c: stur            x2, [fp, #-8]
    // 0xa35c60: CheckStackOverflow
    //     0xa35c60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa35c64: cmp             SP, x16
    //     0xa35c68: b.ls            #0xa363a8
    // 0xa35c6c: mov             x1, x0
    // 0xa35c70: r2 = "home_native"
    //     0xa35c70: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cd8] "home_native"
    //     0xa35c74: ldr             x2, [x2, #0xcd8]
    // 0xa35c78: r0 = _getValueOrData()
    //     0xa35c78: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35c7c: ldur            x3, [fp, #-8]
    // 0xa35c80: LoadField: r1 = r3->field_f
    //     0xa35c80: ldur            w1, [x3, #0xf]
    // 0xa35c84: DecompressPointer r1
    //     0xa35c84: add             x1, x1, HEAP, lsl #32
    // 0xa35c88: cmp             w1, w0
    // 0xa35c8c: b.ne            #0xa35c98
    // 0xa35c90: r4 = Null
    //     0xa35c90: mov             x4, NULL
    // 0xa35c94: b               #0xa35c9c
    // 0xa35c98: mov             x4, x0
    // 0xa35c9c: mov             x0, x4
    // 0xa35ca0: stur            x4, [fp, #-0x10]
    // 0xa35ca4: r2 = Null
    //     0xa35ca4: mov             x2, NULL
    // 0xa35ca8: r1 = Null
    //     0xa35ca8: mov             x1, NULL
    // 0xa35cac: r8 = Map<String, dynamic>
    //     0xa35cac: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35cb0: r3 = Null
    //     0xa35cb0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26ce0] Null
    //     0xa35cb4: ldr             x3, [x3, #0xce0]
    // 0xa35cb8: r0 = Map<String, dynamic>()
    //     0xa35cb8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35cbc: ldur            x2, [fp, #-0x10]
    // 0xa35cc0: r1 = Null
    //     0xa35cc0: mov             x1, NULL
    // 0xa35cc4: r0 = AdsItems.fromJson()
    //     0xa35cc4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35cc8: ldur            x1, [fp, #-8]
    // 0xa35ccc: r2 = "article_native"
    //     0xa35ccc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cf0] "article_native"
    //     0xa35cd0: ldr             x2, [x2, #0xcf0]
    // 0xa35cd4: stur            x0, [fp, #-0x10]
    // 0xa35cd8: r0 = _getValueOrData()
    //     0xa35cd8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35cdc: ldur            x3, [fp, #-8]
    // 0xa35ce0: LoadField: r1 = r3->field_f
    //     0xa35ce0: ldur            w1, [x3, #0xf]
    // 0xa35ce4: DecompressPointer r1
    //     0xa35ce4: add             x1, x1, HEAP, lsl #32
    // 0xa35ce8: cmp             w1, w0
    // 0xa35cec: b.ne            #0xa35cf8
    // 0xa35cf0: r4 = Null
    //     0xa35cf0: mov             x4, NULL
    // 0xa35cf4: b               #0xa35cfc
    // 0xa35cf8: mov             x4, x0
    // 0xa35cfc: mov             x0, x4
    // 0xa35d00: stur            x4, [fp, #-0x18]
    // 0xa35d04: r2 = Null
    //     0xa35d04: mov             x2, NULL
    // 0xa35d08: r1 = Null
    //     0xa35d08: mov             x1, NULL
    // 0xa35d0c: r8 = Map<String, dynamic>
    //     0xa35d0c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35d10: r3 = Null
    //     0xa35d10: add             x3, PP, #0x26, lsl #12  ; [pp+0x26cf8] Null
    //     0xa35d14: ldr             x3, [x3, #0xcf8]
    // 0xa35d18: r0 = Map<String, dynamic>()
    //     0xa35d18: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35d1c: ldur            x2, [fp, #-0x18]
    // 0xa35d20: r1 = Null
    //     0xa35d20: mov             x1, NULL
    // 0xa35d24: r0 = AdsItems.fromJson()
    //     0xa35d24: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35d28: ldur            x1, [fp, #-8]
    // 0xa35d2c: r2 = "article_banner"
    //     0xa35d2c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d08] "article_banner"
    //     0xa35d30: ldr             x2, [x2, #0xd08]
    // 0xa35d34: stur            x0, [fp, #-0x18]
    // 0xa35d38: r0 = _getValueOrData()
    //     0xa35d38: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35d3c: ldur            x3, [fp, #-8]
    // 0xa35d40: LoadField: r1 = r3->field_f
    //     0xa35d40: ldur            w1, [x3, #0xf]
    // 0xa35d44: DecompressPointer r1
    //     0xa35d44: add             x1, x1, HEAP, lsl #32
    // 0xa35d48: cmp             w1, w0
    // 0xa35d4c: b.ne            #0xa35d58
    // 0xa35d50: r4 = Null
    //     0xa35d50: mov             x4, NULL
    // 0xa35d54: b               #0xa35d5c
    // 0xa35d58: mov             x4, x0
    // 0xa35d5c: mov             x0, x4
    // 0xa35d60: stur            x4, [fp, #-0x20]
    // 0xa35d64: r2 = Null
    //     0xa35d64: mov             x2, NULL
    // 0xa35d68: r1 = Null
    //     0xa35d68: mov             x1, NULL
    // 0xa35d6c: r8 = Map<String, dynamic>
    //     0xa35d6c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35d70: r3 = Null
    //     0xa35d70: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d10] Null
    //     0xa35d74: ldr             x3, [x3, #0xd10]
    // 0xa35d78: r0 = Map<String, dynamic>()
    //     0xa35d78: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35d7c: ldur            x2, [fp, #-0x20]
    // 0xa35d80: r1 = Null
    //     0xa35d80: mov             x1, NULL
    // 0xa35d84: r0 = AdsItems.fromJson()
    //     0xa35d84: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35d88: ldur            x1, [fp, #-8]
    // 0xa35d8c: r2 = "doa_native"
    //     0xa35d8c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d20] "doa_native"
    //     0xa35d90: ldr             x2, [x2, #0xd20]
    // 0xa35d94: stur            x0, [fp, #-0x20]
    // 0xa35d98: r0 = _getValueOrData()
    //     0xa35d98: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35d9c: ldur            x3, [fp, #-8]
    // 0xa35da0: LoadField: r1 = r3->field_f
    //     0xa35da0: ldur            w1, [x3, #0xf]
    // 0xa35da4: DecompressPointer r1
    //     0xa35da4: add             x1, x1, HEAP, lsl #32
    // 0xa35da8: cmp             w1, w0
    // 0xa35dac: b.ne            #0xa35db8
    // 0xa35db0: r4 = Null
    //     0xa35db0: mov             x4, NULL
    // 0xa35db4: b               #0xa35dbc
    // 0xa35db8: mov             x4, x0
    // 0xa35dbc: mov             x0, x4
    // 0xa35dc0: stur            x4, [fp, #-0x28]
    // 0xa35dc4: r2 = Null
    //     0xa35dc4: mov             x2, NULL
    // 0xa35dc8: r1 = Null
    //     0xa35dc8: mov             x1, NULL
    // 0xa35dcc: r8 = Map<String, dynamic>
    //     0xa35dcc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35dd0: r3 = Null
    //     0xa35dd0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d28] Null
    //     0xa35dd4: ldr             x3, [x3, #0xd28]
    // 0xa35dd8: r0 = Map<String, dynamic>()
    //     0xa35dd8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35ddc: ldur            x2, [fp, #-0x28]
    // 0xa35de0: r1 = Null
    //     0xa35de0: mov             x1, NULL
    // 0xa35de4: r0 = AdsItems.fromJson()
    //     0xa35de4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35de8: ldur            x1, [fp, #-8]
    // 0xa35dec: r2 = "doa_banner"
    //     0xa35dec: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d38] "doa_banner"
    //     0xa35df0: ldr             x2, [x2, #0xd38]
    // 0xa35df4: stur            x0, [fp, #-0x28]
    // 0xa35df8: r0 = _getValueOrData()
    //     0xa35df8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35dfc: ldur            x3, [fp, #-8]
    // 0xa35e00: LoadField: r1 = r3->field_f
    //     0xa35e00: ldur            w1, [x3, #0xf]
    // 0xa35e04: DecompressPointer r1
    //     0xa35e04: add             x1, x1, HEAP, lsl #32
    // 0xa35e08: cmp             w1, w0
    // 0xa35e0c: b.ne            #0xa35e18
    // 0xa35e10: r4 = Null
    //     0xa35e10: mov             x4, NULL
    // 0xa35e14: b               #0xa35e1c
    // 0xa35e18: mov             x4, x0
    // 0xa35e1c: mov             x0, x4
    // 0xa35e20: stur            x4, [fp, #-0x30]
    // 0xa35e24: r2 = Null
    //     0xa35e24: mov             x2, NULL
    // 0xa35e28: r1 = Null
    //     0xa35e28: mov             x1, NULL
    // 0xa35e2c: r8 = Map<String, dynamic>
    //     0xa35e2c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35e30: r3 = Null
    //     0xa35e30: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d40] Null
    //     0xa35e34: ldr             x3, [x3, #0xd40]
    // 0xa35e38: r0 = Map<String, dynamic>()
    //     0xa35e38: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35e3c: ldur            x2, [fp, #-0x30]
    // 0xa35e40: r1 = Null
    //     0xa35e40: mov             x1, NULL
    // 0xa35e44: r0 = AdsItems.fromJson()
    //     0xa35e44: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35e48: ldur            x1, [fp, #-8]
    // 0xa35e4c: r2 = "nupedia_native"
    //     0xa35e4c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d50] "nupedia_native"
    //     0xa35e50: ldr             x2, [x2, #0xd50]
    // 0xa35e54: stur            x0, [fp, #-0x30]
    // 0xa35e58: r0 = _getValueOrData()
    //     0xa35e58: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35e5c: ldur            x3, [fp, #-8]
    // 0xa35e60: LoadField: r1 = r3->field_f
    //     0xa35e60: ldur            w1, [x3, #0xf]
    // 0xa35e64: DecompressPointer r1
    //     0xa35e64: add             x1, x1, HEAP, lsl #32
    // 0xa35e68: cmp             w1, w0
    // 0xa35e6c: b.ne            #0xa35e78
    // 0xa35e70: r4 = Null
    //     0xa35e70: mov             x4, NULL
    // 0xa35e74: b               #0xa35e7c
    // 0xa35e78: mov             x4, x0
    // 0xa35e7c: mov             x0, x4
    // 0xa35e80: stur            x4, [fp, #-0x38]
    // 0xa35e84: r2 = Null
    //     0xa35e84: mov             x2, NULL
    // 0xa35e88: r1 = Null
    //     0xa35e88: mov             x1, NULL
    // 0xa35e8c: r8 = Map<String, dynamic>
    //     0xa35e8c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35e90: r3 = Null
    //     0xa35e90: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d58] Null
    //     0xa35e94: ldr             x3, [x3, #0xd58]
    // 0xa35e98: r0 = Map<String, dynamic>()
    //     0xa35e98: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35e9c: ldur            x2, [fp, #-0x38]
    // 0xa35ea0: r1 = Null
    //     0xa35ea0: mov             x1, NULL
    // 0xa35ea4: r0 = AdsItems.fromJson()
    //     0xa35ea4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35ea8: ldur            x1, [fp, #-8]
    // 0xa35eac: r2 = "nupedia_banner"
    //     0xa35eac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d68] "nupedia_banner"
    //     0xa35eb0: ldr             x2, [x2, #0xd68]
    // 0xa35eb4: stur            x0, [fp, #-0x38]
    // 0xa35eb8: r0 = _getValueOrData()
    //     0xa35eb8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35ebc: ldur            x3, [fp, #-8]
    // 0xa35ec0: LoadField: r1 = r3->field_f
    //     0xa35ec0: ldur            w1, [x3, #0xf]
    // 0xa35ec4: DecompressPointer r1
    //     0xa35ec4: add             x1, x1, HEAP, lsl #32
    // 0xa35ec8: cmp             w1, w0
    // 0xa35ecc: b.ne            #0xa35ed8
    // 0xa35ed0: r4 = Null
    //     0xa35ed0: mov             x4, NULL
    // 0xa35ed4: b               #0xa35edc
    // 0xa35ed8: mov             x4, x0
    // 0xa35edc: mov             x0, x4
    // 0xa35ee0: stur            x4, [fp, #-0x40]
    // 0xa35ee4: r2 = Null
    //     0xa35ee4: mov             x2, NULL
    // 0xa35ee8: r1 = Null
    //     0xa35ee8: mov             x1, NULL
    // 0xa35eec: r8 = Map<String, dynamic>
    //     0xa35eec: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35ef0: r3 = Null
    //     0xa35ef0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d70] Null
    //     0xa35ef4: ldr             x3, [x3, #0xd70]
    // 0xa35ef8: r0 = Map<String, dynamic>()
    //     0xa35ef8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35efc: ldur            x2, [fp, #-0x40]
    // 0xa35f00: r1 = Null
    //     0xa35f00: mov             x1, NULL
    // 0xa35f04: r0 = AdsItems.fromJson()
    //     0xa35f04: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35f08: ldur            x1, [fp, #-8]
    // 0xa35f0c: r2 = "topic_banner"
    //     0xa35f0c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d80] "topic_banner"
    //     0xa35f10: ldr             x2, [x2, #0xd80]
    // 0xa35f14: stur            x0, [fp, #-0x40]
    // 0xa35f18: r0 = _getValueOrData()
    //     0xa35f18: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35f1c: ldur            x3, [fp, #-8]
    // 0xa35f20: LoadField: r1 = r3->field_f
    //     0xa35f20: ldur            w1, [x3, #0xf]
    // 0xa35f24: DecompressPointer r1
    //     0xa35f24: add             x1, x1, HEAP, lsl #32
    // 0xa35f28: cmp             w1, w0
    // 0xa35f2c: b.ne            #0xa35f38
    // 0xa35f30: r4 = Null
    //     0xa35f30: mov             x4, NULL
    // 0xa35f34: b               #0xa35f3c
    // 0xa35f38: mov             x4, x0
    // 0xa35f3c: mov             x0, x4
    // 0xa35f40: stur            x4, [fp, #-0x48]
    // 0xa35f44: r2 = Null
    //     0xa35f44: mov             x2, NULL
    // 0xa35f48: r1 = Null
    //     0xa35f48: mov             x1, NULL
    // 0xa35f4c: r8 = Map<String, dynamic>
    //     0xa35f4c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35f50: r3 = Null
    //     0xa35f50: add             x3, PP, #0x26, lsl #12  ; [pp+0x26d88] Null
    //     0xa35f54: ldr             x3, [x3, #0xd88]
    // 0xa35f58: r0 = Map<String, dynamic>()
    //     0xa35f58: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35f5c: ldur            x2, [fp, #-0x48]
    // 0xa35f60: r1 = Null
    //     0xa35f60: mov             x1, NULL
    // 0xa35f64: r0 = AdsItems.fromJson()
    //     0xa35f64: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35f68: ldur            x1, [fp, #-8]
    // 0xa35f6c: r2 = "video_banner"
    //     0xa35f6c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d98] "video_banner"
    //     0xa35f70: ldr             x2, [x2, #0xd98]
    // 0xa35f74: stur            x0, [fp, #-0x48]
    // 0xa35f78: r0 = _getValueOrData()
    //     0xa35f78: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35f7c: ldur            x3, [fp, #-8]
    // 0xa35f80: LoadField: r1 = r3->field_f
    //     0xa35f80: ldur            w1, [x3, #0xf]
    // 0xa35f84: DecompressPointer r1
    //     0xa35f84: add             x1, x1, HEAP, lsl #32
    // 0xa35f88: cmp             w1, w0
    // 0xa35f8c: b.ne            #0xa35f98
    // 0xa35f90: r4 = Null
    //     0xa35f90: mov             x4, NULL
    // 0xa35f94: b               #0xa35f9c
    // 0xa35f98: mov             x4, x0
    // 0xa35f9c: mov             x0, x4
    // 0xa35fa0: stur            x4, [fp, #-0x50]
    // 0xa35fa4: r2 = Null
    //     0xa35fa4: mov             x2, NULL
    // 0xa35fa8: r1 = Null
    //     0xa35fa8: mov             x1, NULL
    // 0xa35fac: r8 = Map<String, dynamic>
    //     0xa35fac: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa35fb0: r3 = Null
    //     0xa35fb0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26da0] Null
    //     0xa35fb4: ldr             x3, [x3, #0xda0]
    // 0xa35fb8: r0 = Map<String, dynamic>()
    //     0xa35fb8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa35fbc: ldur            x2, [fp, #-0x50]
    // 0xa35fc0: r1 = Null
    //     0xa35fc0: mov             x1, NULL
    // 0xa35fc4: r0 = AdsItems.fromJson()
    //     0xa35fc4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa35fc8: ldur            x1, [fp, #-8]
    // 0xa35fcc: r2 = "video_interstitial"
    //     0xa35fcc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26db0] "video_interstitial"
    //     0xa35fd0: ldr             x2, [x2, #0xdb0]
    // 0xa35fd4: stur            x0, [fp, #-0x50]
    // 0xa35fd8: r0 = _getValueOrData()
    //     0xa35fd8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa35fdc: ldur            x3, [fp, #-8]
    // 0xa35fe0: LoadField: r1 = r3->field_f
    //     0xa35fe0: ldur            w1, [x3, #0xf]
    // 0xa35fe4: DecompressPointer r1
    //     0xa35fe4: add             x1, x1, HEAP, lsl #32
    // 0xa35fe8: cmp             w1, w0
    // 0xa35fec: b.ne            #0xa35ff8
    // 0xa35ff0: r4 = Null
    //     0xa35ff0: mov             x4, NULL
    // 0xa35ff4: b               #0xa35ffc
    // 0xa35ff8: mov             x4, x0
    // 0xa35ffc: mov             x0, x4
    // 0xa36000: stur            x4, [fp, #-0x58]
    // 0xa36004: r2 = Null
    //     0xa36004: mov             x2, NULL
    // 0xa36008: r1 = Null
    //     0xa36008: mov             x1, NULL
    // 0xa3600c: r8 = Map<String, dynamic>
    //     0xa3600c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa36010: r3 = Null
    //     0xa36010: add             x3, PP, #0x26, lsl #12  ; [pp+0x26db8] Null
    //     0xa36014: ldr             x3, [x3, #0xdb8]
    // 0xa36018: r0 = Map<String, dynamic>()
    //     0xa36018: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa3601c: ldur            x2, [fp, #-0x58]
    // 0xa36020: r1 = Null
    //     0xa36020: mov             x1, NULL
    // 0xa36024: r0 = AdsItems.fromJson()
    //     0xa36024: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa36028: ldur            x1, [fp, #-8]
    // 0xa3602c: r2 = "ziarah_banner"
    //     0xa3602c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26dc8] "ziarah_banner"
    //     0xa36030: ldr             x2, [x2, #0xdc8]
    // 0xa36034: stur            x0, [fp, #-0x58]
    // 0xa36038: r0 = _getValueOrData()
    //     0xa36038: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa3603c: ldur            x3, [fp, #-8]
    // 0xa36040: LoadField: r1 = r3->field_f
    //     0xa36040: ldur            w1, [x3, #0xf]
    // 0xa36044: DecompressPointer r1
    //     0xa36044: add             x1, x1, HEAP, lsl #32
    // 0xa36048: cmp             w1, w0
    // 0xa3604c: b.ne            #0xa36058
    // 0xa36050: r4 = Null
    //     0xa36050: mov             x4, NULL
    // 0xa36054: b               #0xa3605c
    // 0xa36058: mov             x4, x0
    // 0xa3605c: mov             x0, x4
    // 0xa36060: stur            x4, [fp, #-0x60]
    // 0xa36064: r2 = Null
    //     0xa36064: mov             x2, NULL
    // 0xa36068: r1 = Null
    //     0xa36068: mov             x1, NULL
    // 0xa3606c: r8 = Map<String, dynamic>
    //     0xa3606c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa36070: r3 = Null
    //     0xa36070: add             x3, PP, #0x26, lsl #12  ; [pp+0x26dd0] Null
    //     0xa36074: ldr             x3, [x3, #0xdd0]
    // 0xa36078: r0 = Map<String, dynamic>()
    //     0xa36078: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa3607c: ldur            x2, [fp, #-0x60]
    // 0xa36080: r1 = Null
    //     0xa36080: mov             x1, NULL
    // 0xa36084: r0 = AdsItems.fromJson()
    //     0xa36084: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa36088: ldur            x1, [fp, #-8]
    // 0xa3608c: r2 = "kalam_banner"
    //     0xa3608c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26de0] "kalam_banner"
    //     0xa36090: ldr             x2, [x2, #0xde0]
    // 0xa36094: stur            x0, [fp, #-0x60]
    // 0xa36098: r0 = _getValueOrData()
    //     0xa36098: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa3609c: ldur            x3, [fp, #-8]
    // 0xa360a0: LoadField: r1 = r3->field_f
    //     0xa360a0: ldur            w1, [x3, #0xf]
    // 0xa360a4: DecompressPointer r1
    //     0xa360a4: add             x1, x1, HEAP, lsl #32
    // 0xa360a8: cmp             w1, w0
    // 0xa360ac: b.ne            #0xa360b8
    // 0xa360b0: r4 = Null
    //     0xa360b0: mov             x4, NULL
    // 0xa360b4: b               #0xa360bc
    // 0xa360b8: mov             x4, x0
    // 0xa360bc: mov             x0, x4
    // 0xa360c0: stur            x4, [fp, #-0x68]
    // 0xa360c4: r2 = Null
    //     0xa360c4: mov             x2, NULL
    // 0xa360c8: r1 = Null
    //     0xa360c8: mov             x1, NULL
    // 0xa360cc: r8 = Map<String, dynamic>
    //     0xa360cc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa360d0: r3 = Null
    //     0xa360d0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26de8] Null
    //     0xa360d4: ldr             x3, [x3, #0xde8]
    // 0xa360d8: r0 = Map<String, dynamic>()
    //     0xa360d8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa360dc: ldur            x2, [fp, #-0x68]
    // 0xa360e0: r1 = Null
    //     0xa360e0: mov             x1, NULL
    // 0xa360e4: r0 = AdsItems.fromJson()
    //     0xa360e4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa360e8: ldur            x1, [fp, #-8]
    // 0xa360ec: r2 = "ramadhan_banner"
    //     0xa360ec: add             x2, PP, #0x26, lsl #12  ; [pp+0x26df8] "ramadhan_banner"
    //     0xa360f0: ldr             x2, [x2, #0xdf8]
    // 0xa360f4: stur            x0, [fp, #-0x68]
    // 0xa360f8: r0 = _getValueOrData()
    //     0xa360f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa360fc: ldur            x3, [fp, #-8]
    // 0xa36100: LoadField: r1 = r3->field_f
    //     0xa36100: ldur            w1, [x3, #0xf]
    // 0xa36104: DecompressPointer r1
    //     0xa36104: add             x1, x1, HEAP, lsl #32
    // 0xa36108: cmp             w1, w0
    // 0xa3610c: b.ne            #0xa36118
    // 0xa36110: r4 = Null
    //     0xa36110: mov             x4, NULL
    // 0xa36114: b               #0xa3611c
    // 0xa36118: mov             x4, x0
    // 0xa3611c: mov             x0, x4
    // 0xa36120: stur            x4, [fp, #-0x70]
    // 0xa36124: r2 = Null
    //     0xa36124: mov             x2, NULL
    // 0xa36128: r1 = Null
    //     0xa36128: mov             x1, NULL
    // 0xa3612c: r8 = Map<String, dynamic>
    //     0xa3612c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa36130: r3 = Null
    //     0xa36130: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e00] Null
    //     0xa36134: ldr             x3, [x3, #0xe00]
    // 0xa36138: r0 = Map<String, dynamic>()
    //     0xa36138: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa3613c: ldur            x2, [fp, #-0x70]
    // 0xa36140: r1 = Null
    //     0xa36140: mov             x1, NULL
    // 0xa36144: r0 = AdsItems.fromJson()
    //     0xa36144: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa36148: ldur            x1, [fp, #-8]
    // 0xa3614c: r2 = "ramadhan_native"
    //     0xa3614c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e10] "ramadhan_native"
    //     0xa36150: ldr             x2, [x2, #0xe10]
    // 0xa36154: stur            x0, [fp, #-0x70]
    // 0xa36158: r0 = _getValueOrData()
    //     0xa36158: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa3615c: ldur            x3, [fp, #-8]
    // 0xa36160: LoadField: r1 = r3->field_f
    //     0xa36160: ldur            w1, [x3, #0xf]
    // 0xa36164: DecompressPointer r1
    //     0xa36164: add             x1, x1, HEAP, lsl #32
    // 0xa36168: cmp             w1, w0
    // 0xa3616c: b.ne            #0xa36178
    // 0xa36170: r4 = Null
    //     0xa36170: mov             x4, NULL
    // 0xa36174: b               #0xa3617c
    // 0xa36178: mov             x4, x0
    // 0xa3617c: mov             x0, x4
    // 0xa36180: stur            x4, [fp, #-0x78]
    // 0xa36184: r2 = Null
    //     0xa36184: mov             x2, NULL
    // 0xa36188: r1 = Null
    //     0xa36188: mov             x1, NULL
    // 0xa3618c: r8 = Map<String, dynamic>
    //     0xa3618c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa36190: r3 = Null
    //     0xa36190: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e18] Null
    //     0xa36194: ldr             x3, [x3, #0xe18]
    // 0xa36198: r0 = Map<String, dynamic>()
    //     0xa36198: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa3619c: ldur            x2, [fp, #-0x78]
    // 0xa361a0: r1 = Null
    //     0xa361a0: mov             x1, NULL
    // 0xa361a4: r0 = AdsItems.fromJson()
    //     0xa361a4: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa361a8: ldur            x1, [fp, #-8]
    // 0xa361ac: r2 = "haji_banner"
    //     0xa361ac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e28] "haji_banner"
    //     0xa361b0: ldr             x2, [x2, #0xe28]
    // 0xa361b4: stur            x0, [fp, #-0x78]
    // 0xa361b8: r0 = _getValueOrData()
    //     0xa361b8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa361bc: ldur            x3, [fp, #-8]
    // 0xa361c0: LoadField: r1 = r3->field_f
    //     0xa361c0: ldur            w1, [x3, #0xf]
    // 0xa361c4: DecompressPointer r1
    //     0xa361c4: add             x1, x1, HEAP, lsl #32
    // 0xa361c8: cmp             w1, w0
    // 0xa361cc: b.ne            #0xa361d8
    // 0xa361d0: r4 = Null
    //     0xa361d0: mov             x4, NULL
    // 0xa361d4: b               #0xa361dc
    // 0xa361d8: mov             x4, x0
    // 0xa361dc: mov             x0, x4
    // 0xa361e0: stur            x4, [fp, #-0x80]
    // 0xa361e4: r2 = Null
    //     0xa361e4: mov             x2, NULL
    // 0xa361e8: r1 = Null
    //     0xa361e8: mov             x1, NULL
    // 0xa361ec: r8 = Map<String, dynamic>
    //     0xa361ec: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa361f0: r3 = Null
    //     0xa361f0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e30] Null
    //     0xa361f4: ldr             x3, [x3, #0xe30]
    // 0xa361f8: r0 = Map<String, dynamic>()
    //     0xa361f8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa361fc: ldur            x2, [fp, #-0x80]
    // 0xa36200: r1 = Null
    //     0xa36200: mov             x1, NULL
    // 0xa36204: r0 = AdsItems.fromJson()
    //     0xa36204: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa36208: ldur            x1, [fp, #-8]
    // 0xa3620c: r2 = "tutorial_banner"
    //     0xa3620c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e40] "tutorial_banner"
    //     0xa36210: ldr             x2, [x2, #0xe40]
    // 0xa36214: stur            x0, [fp, #-0x80]
    // 0xa36218: r0 = _getValueOrData()
    //     0xa36218: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa3621c: ldur            x3, [fp, #-8]
    // 0xa36220: LoadField: r1 = r3->field_f
    //     0xa36220: ldur            w1, [x3, #0xf]
    // 0xa36224: DecompressPointer r1
    //     0xa36224: add             x1, x1, HEAP, lsl #32
    // 0xa36228: cmp             w1, w0
    // 0xa3622c: b.ne            #0xa36238
    // 0xa36230: r4 = Null
    //     0xa36230: mov             x4, NULL
    // 0xa36234: b               #0xa3623c
    // 0xa36238: mov             x4, x0
    // 0xa3623c: mov             x0, x4
    // 0xa36240: stur            x4, [fp, #-0x88]
    // 0xa36244: r2 = Null
    //     0xa36244: mov             x2, NULL
    // 0xa36248: r1 = Null
    //     0xa36248: mov             x1, NULL
    // 0xa3624c: r8 = Map<String, dynamic>
    //     0xa3624c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa36250: r3 = Null
    //     0xa36250: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e48] Null
    //     0xa36254: ldr             x3, [x3, #0xe48]
    // 0xa36258: r0 = Map<String, dynamic>()
    //     0xa36258: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa3625c: ldur            x2, [fp, #-0x88]
    // 0xa36260: r1 = Null
    //     0xa36260: mov             x1, NULL
    // 0xa36264: r0 = AdsItems.fromJson()
    //     0xa36264: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa36268: ldur            x1, [fp, #-8]
    // 0xa3626c: r2 = "zakat_banner"
    //     0xa3626c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e58] "zakat_banner"
    //     0xa36270: ldr             x2, [x2, #0xe58]
    // 0xa36274: stur            x0, [fp, #-0x88]
    // 0xa36278: r0 = _getValueOrData()
    //     0xa36278: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa3627c: mov             x1, x0
    // 0xa36280: ldur            x0, [fp, #-8]
    // 0xa36284: LoadField: r2 = r0->field_f
    //     0xa36284: ldur            w2, [x0, #0xf]
    // 0xa36288: DecompressPointer r2
    //     0xa36288: add             x2, x2, HEAP, lsl #32
    // 0xa3628c: cmp             w2, w1
    // 0xa36290: b.ne            #0xa3629c
    // 0xa36294: r25 = Null
    //     0xa36294: mov             x25, NULL
    // 0xa36298: b               #0xa362a0
    // 0xa3629c: mov             x25, x1
    // 0xa362a0: ldur            x24, [fp, #-0x10]
    // 0xa362a4: ldur            x23, [fp, #-0x18]
    // 0xa362a8: ldur            x20, [fp, #-0x20]
    // 0xa362ac: ldur            x19, [fp, #-0x28]
    // 0xa362b0: ldur            x14, [fp, #-0x30]
    // 0xa362b4: ldur            x13, [fp, #-0x38]
    // 0xa362b8: ldur            x12, [fp, #-0x40]
    // 0xa362bc: ldur            x11, [fp, #-0x48]
    // 0xa362c0: ldur            x10, [fp, #-0x50]
    // 0xa362c4: ldur            x9, [fp, #-0x58]
    // 0xa362c8: ldur            x8, [fp, #-0x60]
    // 0xa362cc: ldur            x7, [fp, #-0x68]
    // 0xa362d0: ldur            x6, [fp, #-0x70]
    // 0xa362d4: ldur            x5, [fp, #-0x78]
    // 0xa362d8: ldur            x4, [fp, #-0x80]
    // 0xa362dc: ldur            x3, [fp, #-0x88]
    // 0xa362e0: mov             x0, x25
    // 0xa362e4: stur            x25, [fp, #-8]
    // 0xa362e8: r2 = Null
    //     0xa362e8: mov             x2, NULL
    // 0xa362ec: r1 = Null
    //     0xa362ec: mov             x1, NULL
    // 0xa362f0: r8 = Map<String, dynamic>
    //     0xa362f0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xa362f4: r3 = Null
    //     0xa362f4: add             x3, PP, #0x26, lsl #12  ; [pp+0x26e60] Null
    //     0xa362f8: ldr             x3, [x3, #0xe60]
    // 0xa362fc: r0 = Map<String, dynamic>()
    //     0xa362fc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xa36300: ldur            x2, [fp, #-8]
    // 0xa36304: r1 = Null
    //     0xa36304: mov             x1, NULL
    // 0xa36308: r0 = AdsItems.fromJson()
    //     0xa36308: bl              #0xa3679c  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::AdsItems.fromJson
    // 0xa3630c: stur            x0, [fp, #-8]
    // 0xa36310: r0 = AdsConfig()
    //     0xa36310: bl              #0xa36790  ; AllocateAdsConfigStub -> AdsConfig (size=0x4c)
    // 0xa36314: ldur            x1, [fp, #-0x10]
    // 0xa36318: StoreField: r0->field_7 = r1
    //     0xa36318: stur            w1, [x0, #7]
    // 0xa3631c: ldur            x1, [fp, #-0x18]
    // 0xa36320: StoreField: r0->field_b = r1
    //     0xa36320: stur            w1, [x0, #0xb]
    // 0xa36324: ldur            x1, [fp, #-0x20]
    // 0xa36328: StoreField: r0->field_f = r1
    //     0xa36328: stur            w1, [x0, #0xf]
    // 0xa3632c: ldur            x1, [fp, #-0x28]
    // 0xa36330: StoreField: r0->field_13 = r1
    //     0xa36330: stur            w1, [x0, #0x13]
    // 0xa36334: ldur            x1, [fp, #-0x30]
    // 0xa36338: ArrayStore: r0[0] = r1  ; List_4
    //     0xa36338: stur            w1, [x0, #0x17]
    // 0xa3633c: ldur            x1, [fp, #-0x38]
    // 0xa36340: StoreField: r0->field_1b = r1
    //     0xa36340: stur            w1, [x0, #0x1b]
    // 0xa36344: ldur            x1, [fp, #-0x40]
    // 0xa36348: StoreField: r0->field_1f = r1
    //     0xa36348: stur            w1, [x0, #0x1f]
    // 0xa3634c: ldur            x1, [fp, #-0x48]
    // 0xa36350: StoreField: r0->field_23 = r1
    //     0xa36350: stur            w1, [x0, #0x23]
    // 0xa36354: ldur            x1, [fp, #-0x50]
    // 0xa36358: StoreField: r0->field_27 = r1
    //     0xa36358: stur            w1, [x0, #0x27]
    // 0xa3635c: ldur            x1, [fp, #-0x58]
    // 0xa36360: StoreField: r0->field_2b = r1
    //     0xa36360: stur            w1, [x0, #0x2b]
    // 0xa36364: ldur            x1, [fp, #-0x60]
    // 0xa36368: StoreField: r0->field_2f = r1
    //     0xa36368: stur            w1, [x0, #0x2f]
    // 0xa3636c: ldur            x1, [fp, #-0x68]
    // 0xa36370: StoreField: r0->field_33 = r1
    //     0xa36370: stur            w1, [x0, #0x33]
    // 0xa36374: ldur            x1, [fp, #-0x70]
    // 0xa36378: StoreField: r0->field_37 = r1
    //     0xa36378: stur            w1, [x0, #0x37]
    // 0xa3637c: ldur            x1, [fp, #-0x78]
    // 0xa36380: StoreField: r0->field_3b = r1
    //     0xa36380: stur            w1, [x0, #0x3b]
    // 0xa36384: ldur            x1, [fp, #-0x80]
    // 0xa36388: StoreField: r0->field_3f = r1
    //     0xa36388: stur            w1, [x0, #0x3f]
    // 0xa3638c: ldur            x1, [fp, #-0x88]
    // 0xa36390: StoreField: r0->field_43 = r1
    //     0xa36390: stur            w1, [x0, #0x43]
    // 0xa36394: ldur            x1, [fp, #-8]
    // 0xa36398: StoreField: r0->field_47 = r1
    //     0xa36398: stur            w1, [x0, #0x47]
    // 0xa3639c: LeaveFrame
    //     0xa3639c: mov             SP, fp
    //     0xa363a0: ldp             fp, lr, [SP], #0x10
    // 0xa363a4: ret
    //     0xa363a4: ret             
    // 0xa363a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa363a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa363ac: b               #0xa35c6c
  }
  Map<String, dynamic> toJson(AdsConfig) {
    // ** addr: 0xa363c8, size: 0x48
    // 0xa363c8: EnterFrame
    //     0xa363c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa363cc: mov             fp, SP
    // 0xa363d0: CheckStackOverflow
    //     0xa363d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa363d4: cmp             SP, x16
    //     0xa363d8: b.ls            #0xa363f0
    // 0xa363dc: ldr             x1, [fp, #0x10]
    // 0xa363e0: r0 = toJson()
    //     0xa363e0: bl              #0xa363f8  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::toJson
    // 0xa363e4: LeaveFrame
    //     0xa363e4: mov             SP, fp
    //     0xa363e8: ldp             fp, lr, [SP], #0x10
    // 0xa363ec: ret
    //     0xa363ec: ret             
    // 0xa363f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa363f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa363f4: b               #0xa363dc
  }
  Map<String, dynamic> toJson(AdsConfig) {
    // ** addr: 0xa363f8, size: 0x2b0
    // 0xa363f8: EnterFrame
    //     0xa363f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa363fc: mov             fp, SP
    // 0xa36400: AllocStack(0x20)
    //     0xa36400: sub             SP, SP, #0x20
    // 0xa36404: SetupParameters(AdsConfig this /* r1 => r1, fp-0x8 */)
    //     0xa36404: stur            x1, [fp, #-8]
    // 0xa36408: CheckStackOverflow
    //     0xa36408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3640c: cmp             SP, x16
    //     0xa36410: b.ls            #0xa366a0
    // 0xa36414: r16 = <String, dynamic>
    //     0xa36414: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa36418: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa3641c: stp             lr, x16, [SP]
    // 0xa36420: r0 = Map._fromLiteral()
    //     0xa36420: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa36424: mov             x2, x0
    // 0xa36428: ldur            x0, [fp, #-8]
    // 0xa3642c: stur            x2, [fp, #-0x10]
    // 0xa36430: LoadField: r1 = r0->field_7
    //     0xa36430: ldur            w1, [x0, #7]
    // 0xa36434: DecompressPointer r1
    //     0xa36434: add             x1, x1, HEAP, lsl #32
    // 0xa36438: r0 = toJson()
    //     0xa36438: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa3643c: ldur            x1, [fp, #-0x10]
    // 0xa36440: mov             x3, x0
    // 0xa36444: r2 = "home_native"
    //     0xa36444: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cd8] "home_native"
    //     0xa36448: ldr             x2, [x2, #0xcd8]
    // 0xa3644c: r0 = []=()
    //     0xa3644c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36450: ldur            x0, [fp, #-8]
    // 0xa36454: LoadField: r1 = r0->field_b
    //     0xa36454: ldur            w1, [x0, #0xb]
    // 0xa36458: DecompressPointer r1
    //     0xa36458: add             x1, x1, HEAP, lsl #32
    // 0xa3645c: r0 = toJson()
    //     0xa3645c: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36460: ldur            x1, [fp, #-0x10]
    // 0xa36464: mov             x3, x0
    // 0xa36468: r2 = "article_native"
    //     0xa36468: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cf0] "article_native"
    //     0xa3646c: ldr             x2, [x2, #0xcf0]
    // 0xa36470: r0 = []=()
    //     0xa36470: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36474: ldur            x0, [fp, #-8]
    // 0xa36478: LoadField: r1 = r0->field_f
    //     0xa36478: ldur            w1, [x0, #0xf]
    // 0xa3647c: DecompressPointer r1
    //     0xa3647c: add             x1, x1, HEAP, lsl #32
    // 0xa36480: r0 = toJson()
    //     0xa36480: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36484: ldur            x1, [fp, #-0x10]
    // 0xa36488: mov             x3, x0
    // 0xa3648c: r2 = "article_banner"
    //     0xa3648c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d08] "article_banner"
    //     0xa36490: ldr             x2, [x2, #0xd08]
    // 0xa36494: r0 = []=()
    //     0xa36494: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36498: ldur            x0, [fp, #-8]
    // 0xa3649c: LoadField: r1 = r0->field_13
    //     0xa3649c: ldur            w1, [x0, #0x13]
    // 0xa364a0: DecompressPointer r1
    //     0xa364a0: add             x1, x1, HEAP, lsl #32
    // 0xa364a4: r0 = toJson()
    //     0xa364a4: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa364a8: ldur            x1, [fp, #-0x10]
    // 0xa364ac: mov             x3, x0
    // 0xa364b0: r2 = "doa_native"
    //     0xa364b0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d20] "doa_native"
    //     0xa364b4: ldr             x2, [x2, #0xd20]
    // 0xa364b8: r0 = []=()
    //     0xa364b8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa364bc: ldur            x0, [fp, #-8]
    // 0xa364c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa364c0: ldur            w1, [x0, #0x17]
    // 0xa364c4: DecompressPointer r1
    //     0xa364c4: add             x1, x1, HEAP, lsl #32
    // 0xa364c8: r0 = toJson()
    //     0xa364c8: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa364cc: ldur            x1, [fp, #-0x10]
    // 0xa364d0: mov             x3, x0
    // 0xa364d4: r2 = "doa_banner"
    //     0xa364d4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d38] "doa_banner"
    //     0xa364d8: ldr             x2, [x2, #0xd38]
    // 0xa364dc: r0 = []=()
    //     0xa364dc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa364e0: ldur            x0, [fp, #-8]
    // 0xa364e4: LoadField: r1 = r0->field_1b
    //     0xa364e4: ldur            w1, [x0, #0x1b]
    // 0xa364e8: DecompressPointer r1
    //     0xa364e8: add             x1, x1, HEAP, lsl #32
    // 0xa364ec: r0 = toJson()
    //     0xa364ec: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa364f0: ldur            x1, [fp, #-0x10]
    // 0xa364f4: mov             x3, x0
    // 0xa364f8: r2 = "nupedia_native"
    //     0xa364f8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d50] "nupedia_native"
    //     0xa364fc: ldr             x2, [x2, #0xd50]
    // 0xa36500: r0 = []=()
    //     0xa36500: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36504: ldur            x0, [fp, #-8]
    // 0xa36508: LoadField: r1 = r0->field_1f
    //     0xa36508: ldur            w1, [x0, #0x1f]
    // 0xa3650c: DecompressPointer r1
    //     0xa3650c: add             x1, x1, HEAP, lsl #32
    // 0xa36510: r0 = toJson()
    //     0xa36510: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36514: ldur            x1, [fp, #-0x10]
    // 0xa36518: mov             x3, x0
    // 0xa3651c: r2 = "nupedia_banner"
    //     0xa3651c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d68] "nupedia_banner"
    //     0xa36520: ldr             x2, [x2, #0xd68]
    // 0xa36524: r0 = []=()
    //     0xa36524: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36528: ldur            x0, [fp, #-8]
    // 0xa3652c: LoadField: r1 = r0->field_23
    //     0xa3652c: ldur            w1, [x0, #0x23]
    // 0xa36530: DecompressPointer r1
    //     0xa36530: add             x1, x1, HEAP, lsl #32
    // 0xa36534: r0 = toJson()
    //     0xa36534: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36538: ldur            x1, [fp, #-0x10]
    // 0xa3653c: mov             x3, x0
    // 0xa36540: r2 = "topic_banner"
    //     0xa36540: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d80] "topic_banner"
    //     0xa36544: ldr             x2, [x2, #0xd80]
    // 0xa36548: r0 = []=()
    //     0xa36548: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3654c: ldur            x0, [fp, #-8]
    // 0xa36550: LoadField: r1 = r0->field_27
    //     0xa36550: ldur            w1, [x0, #0x27]
    // 0xa36554: DecompressPointer r1
    //     0xa36554: add             x1, x1, HEAP, lsl #32
    // 0xa36558: r0 = toJson()
    //     0xa36558: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa3655c: ldur            x1, [fp, #-0x10]
    // 0xa36560: mov             x3, x0
    // 0xa36564: r2 = "video_banner"
    //     0xa36564: add             x2, PP, #0x26, lsl #12  ; [pp+0x26d98] "video_banner"
    //     0xa36568: ldr             x2, [x2, #0xd98]
    // 0xa3656c: r0 = []=()
    //     0xa3656c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36570: ldur            x0, [fp, #-8]
    // 0xa36574: LoadField: r1 = r0->field_2b
    //     0xa36574: ldur            w1, [x0, #0x2b]
    // 0xa36578: DecompressPointer r1
    //     0xa36578: add             x1, x1, HEAP, lsl #32
    // 0xa3657c: r0 = toJson()
    //     0xa3657c: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36580: ldur            x1, [fp, #-0x10]
    // 0xa36584: mov             x3, x0
    // 0xa36588: r2 = "video_interstitial"
    //     0xa36588: add             x2, PP, #0x26, lsl #12  ; [pp+0x26db0] "video_interstitial"
    //     0xa3658c: ldr             x2, [x2, #0xdb0]
    // 0xa36590: r0 = []=()
    //     0xa36590: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36594: ldur            x0, [fp, #-8]
    // 0xa36598: LoadField: r1 = r0->field_2f
    //     0xa36598: ldur            w1, [x0, #0x2f]
    // 0xa3659c: DecompressPointer r1
    //     0xa3659c: add             x1, x1, HEAP, lsl #32
    // 0xa365a0: r0 = toJson()
    //     0xa365a0: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa365a4: ldur            x1, [fp, #-0x10]
    // 0xa365a8: mov             x3, x0
    // 0xa365ac: r2 = "ziarah_banner"
    //     0xa365ac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26dc8] "ziarah_banner"
    //     0xa365b0: ldr             x2, [x2, #0xdc8]
    // 0xa365b4: r0 = []=()
    //     0xa365b4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa365b8: ldur            x0, [fp, #-8]
    // 0xa365bc: LoadField: r1 = r0->field_33
    //     0xa365bc: ldur            w1, [x0, #0x33]
    // 0xa365c0: DecompressPointer r1
    //     0xa365c0: add             x1, x1, HEAP, lsl #32
    // 0xa365c4: r0 = toJson()
    //     0xa365c4: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa365c8: ldur            x1, [fp, #-0x10]
    // 0xa365cc: mov             x3, x0
    // 0xa365d0: r2 = "kalam_banner"
    //     0xa365d0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26de0] "kalam_banner"
    //     0xa365d4: ldr             x2, [x2, #0xde0]
    // 0xa365d8: r0 = []=()
    //     0xa365d8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa365dc: ldur            x0, [fp, #-8]
    // 0xa365e0: LoadField: r1 = r0->field_37
    //     0xa365e0: ldur            w1, [x0, #0x37]
    // 0xa365e4: DecompressPointer r1
    //     0xa365e4: add             x1, x1, HEAP, lsl #32
    // 0xa365e8: r0 = toJson()
    //     0xa365e8: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa365ec: ldur            x1, [fp, #-0x10]
    // 0xa365f0: mov             x3, x0
    // 0xa365f4: r2 = "ramadhan_banner"
    //     0xa365f4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26df8] "ramadhan_banner"
    //     0xa365f8: ldr             x2, [x2, #0xdf8]
    // 0xa365fc: r0 = []=()
    //     0xa365fc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36600: ldur            x0, [fp, #-8]
    // 0xa36604: LoadField: r1 = r0->field_3b
    //     0xa36604: ldur            w1, [x0, #0x3b]
    // 0xa36608: DecompressPointer r1
    //     0xa36608: add             x1, x1, HEAP, lsl #32
    // 0xa3660c: r0 = toJson()
    //     0xa3660c: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36610: ldur            x1, [fp, #-0x10]
    // 0xa36614: mov             x3, x0
    // 0xa36618: r2 = "ramadhan_native"
    //     0xa36618: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e10] "ramadhan_native"
    //     0xa3661c: ldr             x2, [x2, #0xe10]
    // 0xa36620: r0 = []=()
    //     0xa36620: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36624: ldur            x0, [fp, #-8]
    // 0xa36628: LoadField: r1 = r0->field_3f
    //     0xa36628: ldur            w1, [x0, #0x3f]
    // 0xa3662c: DecompressPointer r1
    //     0xa3662c: add             x1, x1, HEAP, lsl #32
    // 0xa36630: r0 = toJson()
    //     0xa36630: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36634: ldur            x1, [fp, #-0x10]
    // 0xa36638: mov             x3, x0
    // 0xa3663c: r2 = "haji_banner"
    //     0xa3663c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e28] "haji_banner"
    //     0xa36640: ldr             x2, [x2, #0xe28]
    // 0xa36644: r0 = []=()
    //     0xa36644: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36648: ldur            x0, [fp, #-8]
    // 0xa3664c: LoadField: r1 = r0->field_43
    //     0xa3664c: ldur            w1, [x0, #0x43]
    // 0xa36650: DecompressPointer r1
    //     0xa36650: add             x1, x1, HEAP, lsl #32
    // 0xa36654: r0 = toJson()
    //     0xa36654: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa36658: ldur            x1, [fp, #-0x10]
    // 0xa3665c: mov             x3, x0
    // 0xa36660: r2 = "tutorial_banner"
    //     0xa36660: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e40] "tutorial_banner"
    //     0xa36664: ldr             x2, [x2, #0xe40]
    // 0xa36668: r0 = []=()
    //     0xa36668: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa3666c: ldur            x0, [fp, #-8]
    // 0xa36670: LoadField: r1 = r0->field_47
    //     0xa36670: ldur            w1, [x0, #0x47]
    // 0xa36674: DecompressPointer r1
    //     0xa36674: add             x1, x1, HEAP, lsl #32
    // 0xa36678: r0 = toJson()
    //     0xa36678: bl              #0xa366a8  ; [package:nuonline/app/data/models/ads_config.dart] AdsItems::toJson
    // 0xa3667c: ldur            x1, [fp, #-0x10]
    // 0xa36680: mov             x3, x0
    // 0xa36684: r2 = "zakat_banner"
    //     0xa36684: add             x2, PP, #0x26, lsl #12  ; [pp+0x26e58] "zakat_banner"
    //     0xa36688: ldr             x2, [x2, #0xe58]
    // 0xa3668c: r0 = []=()
    //     0xa3668c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xa36690: ldur            x0, [fp, #-0x10]
    // 0xa36694: LeaveFrame
    //     0xa36694: mov             SP, fp
    //     0xa36698: ldp             fp, lr, [SP], #0x10
    // 0xa3669c: ret
    //     0xa3669c: ret             
    // 0xa366a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa366a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa366a4: b               #0xa36414
  }
}
