// lib: , url: package:nuonline/app/data/models/area.dart

// class id: 1050004, size: 0x8
class :: {
}

// class id: 5600, size: 0x1c, field offset: 0x8
//   const constructor, 
class Area extends Equatable {

  _OneByteString field_c;

  _ copyWith(/* No info */) {
    // ** addr: 0x7daf8c, size: 0x78
    // 0x7daf8c: EnterFrame
    //     0x7daf8c: stp             fp, lr, [SP, #-0x10]!
    //     0x7daf90: mov             fp, SP
    // 0x7daf94: AllocStack(0x28)
    //     0x7daf94: sub             SP, SP, #0x28
    // 0x7daf98: SetupParameters(dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x7daf98: stur            x2, [fp, #-0x28]
    // 0x7daf9c: LoadField: r0 = r1->field_7
    //     0x7daf9c: ldur            w0, [x1, #7]
    // 0x7dafa0: DecompressPointer r0
    //     0x7dafa0: add             x0, x0, HEAP, lsl #32
    // 0x7dafa4: stur            x0, [fp, #-0x20]
    // 0x7dafa8: LoadField: r3 = r1->field_f
    //     0x7dafa8: ldur            w3, [x1, #0xf]
    // 0x7dafac: DecompressPointer r3
    //     0x7dafac: add             x3, x3, HEAP, lsl #32
    // 0x7dafb0: stur            x3, [fp, #-0x18]
    // 0x7dafb4: LoadField: r4 = r1->field_13
    //     0x7dafb4: ldur            w4, [x1, #0x13]
    // 0x7dafb8: DecompressPointer r4
    //     0x7dafb8: add             x4, x4, HEAP, lsl #32
    // 0x7dafbc: stur            x4, [fp, #-0x10]
    // 0x7dafc0: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x7dafc0: ldur            w5, [x1, #0x17]
    // 0x7dafc4: DecompressPointer r5
    //     0x7dafc4: add             x5, x5, HEAP, lsl #32
    // 0x7dafc8: stur            x5, [fp, #-8]
    // 0x7dafcc: r0 = Area()
    //     0x7dafcc: bl              #0x7db004  ; AllocateAreaStub -> Area (size=0x1c)
    // 0x7dafd0: ldur            x1, [fp, #-0x20]
    // 0x7dafd4: StoreField: r0->field_7 = r1
    //     0x7dafd4: stur            w1, [x0, #7]
    // 0x7dafd8: ldur            x1, [fp, #-0x28]
    // 0x7dafdc: StoreField: r0->field_b = r1
    //     0x7dafdc: stur            w1, [x0, #0xb]
    // 0x7dafe0: ldur            x1, [fp, #-0x18]
    // 0x7dafe4: StoreField: r0->field_f = r1
    //     0x7dafe4: stur            w1, [x0, #0xf]
    // 0x7dafe8: ldur            x1, [fp, #-0x10]
    // 0x7dafec: StoreField: r0->field_13 = r1
    //     0x7dafec: stur            w1, [x0, #0x13]
    // 0x7daff0: ldur            x1, [fp, #-8]
    // 0x7daff4: ArrayStore: r0[0] = r1  ; List_4
    //     0x7daff4: stur            w1, [x0, #0x17]
    // 0x7daff8: LeaveFrame
    //     0x7daff8: mov             SP, fp
    //     0x7daffc: ldp             fp, lr, [SP], #0x10
    // 0x7db000: ret
    //     0x7db000: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbc74, size: 0x70
    // 0xbdbc74: EnterFrame
    //     0xbdbc74: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbc78: mov             fp, SP
    // 0xbdbc7c: AllocStack(0x18)
    //     0xbdbc7c: sub             SP, SP, #0x18
    // 0xbdbc80: r0 = 4
    //     0xbdbc80: movz            x0, #0x4
    // 0xbdbc84: LoadField: r3 = r1->field_7
    //     0xbdbc84: ldur            w3, [x1, #7]
    // 0xbdbc88: DecompressPointer r3
    //     0xbdbc88: add             x3, x3, HEAP, lsl #32
    // 0xbdbc8c: stur            x3, [fp, #-0x10]
    // 0xbdbc90: LoadField: r4 = r1->field_b
    //     0xbdbc90: ldur            w4, [x1, #0xb]
    // 0xbdbc94: DecompressPointer r4
    //     0xbdbc94: add             x4, x4, HEAP, lsl #32
    // 0xbdbc98: mov             x2, x0
    // 0xbdbc9c: stur            x4, [fp, #-8]
    // 0xbdbca0: r1 = Null
    //     0xbdbca0: mov             x1, NULL
    // 0xbdbca4: r0 = AllocateArray()
    //     0xbdbca4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbca8: mov             x2, x0
    // 0xbdbcac: ldur            x0, [fp, #-0x10]
    // 0xbdbcb0: stur            x2, [fp, #-0x18]
    // 0xbdbcb4: StoreField: r2->field_f = r0
    //     0xbdbcb4: stur            w0, [x2, #0xf]
    // 0xbdbcb8: ldur            x0, [fp, #-8]
    // 0xbdbcbc: StoreField: r2->field_13 = r0
    //     0xbdbcbc: stur            w0, [x2, #0x13]
    // 0xbdbcc0: r1 = <Object?>
    //     0xbdbcc0: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbcc4: r0 = AllocateGrowableArray()
    //     0xbdbcc4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbcc8: ldur            x1, [fp, #-0x18]
    // 0xbdbccc: StoreField: r0->field_f = r1
    //     0xbdbccc: stur            w1, [x0, #0xf]
    // 0xbdbcd0: r1 = 4
    //     0xbdbcd0: movz            x1, #0x4
    // 0xbdbcd4: StoreField: r0->field_b = r1
    //     0xbdbcd4: stur            w1, [x0, #0xb]
    // 0xbdbcd8: LeaveFrame
    //     0xbdbcd8: mov             SP, fp
    //     0xbdbcdc: ldp             fp, lr, [SP], #0x10
    // 0xbdbce0: ret
    //     0xbdbce0: ret             
  }
  factory _ Area.fromMap(/* No info */) {
    // ** addr: 0xe7c5b4, size: 0x2a4
    // 0xe7c5b4: EnterFrame
    //     0xe7c5b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c5b8: mov             fp, SP
    // 0xe7c5bc: AllocStack(0x28)
    //     0xe7c5bc: sub             SP, SP, #0x28
    // 0xe7c5c0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe7c5c0: mov             x3, x2
    //     0xe7c5c4: stur            x2, [fp, #-8]
    // 0xe7c5c8: CheckStackOverflow
    //     0xe7c5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c5cc: cmp             SP, x16
    //     0xe7c5d0: b.ls            #0xe7c850
    // 0xe7c5d4: r0 = LoadClassIdInstr(r3)
    //     0xe7c5d4: ldur            x0, [x3, #-1]
    //     0xe7c5d8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c5dc: mov             x1, x3
    // 0xe7c5e0: r2 = "id"
    //     0xe7c5e0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7c5e4: ldr             x2, [x2, #0x740]
    // 0xe7c5e8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c5e8: sub             lr, x0, #0x114
    //     0xe7c5ec: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c5f0: blr             lr
    // 0xe7c5f4: mov             x3, x0
    // 0xe7c5f8: r2 = Null
    //     0xe7c5f8: mov             x2, NULL
    // 0xe7c5fc: r1 = Null
    //     0xe7c5fc: mov             x1, NULL
    // 0xe7c600: stur            x3, [fp, #-0x10]
    // 0xe7c604: branchIfSmi(r0, 0xe7c62c)
    //     0xe7c604: tbz             w0, #0, #0xe7c62c
    // 0xe7c608: r4 = LoadClassIdInstr(r0)
    //     0xe7c608: ldur            x4, [x0, #-1]
    //     0xe7c60c: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c610: sub             x4, x4, #0x3c
    // 0xe7c614: cmp             x4, #1
    // 0xe7c618: b.ls            #0xe7c62c
    // 0xe7c61c: r8 = int?
    //     0xe7c61c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7c620: r3 = Null
    //     0xe7c620: add             x3, PP, #0x37, lsl #12  ; [pp+0x37de0] Null
    //     0xe7c624: ldr             x3, [x3, #0xde0]
    // 0xe7c628: r0 = int?()
    //     0xe7c628: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7c62c: ldur            x3, [fp, #-8]
    // 0xe7c630: r0 = LoadClassIdInstr(r3)
    //     0xe7c630: ldur            x0, [x3, #-1]
    //     0xe7c634: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c638: mov             x1, x3
    // 0xe7c63c: r2 = "name"
    //     0xe7c63c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7c640: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c640: sub             lr, x0, #0x114
    //     0xe7c644: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c648: blr             lr
    // 0xe7c64c: mov             x3, x0
    // 0xe7c650: r2 = Null
    //     0xe7c650: mov             x2, NULL
    // 0xe7c654: r1 = Null
    //     0xe7c654: mov             x1, NULL
    // 0xe7c658: stur            x3, [fp, #-0x18]
    // 0xe7c65c: r4 = 60
    //     0xe7c65c: movz            x4, #0x3c
    // 0xe7c660: branchIfSmi(r0, 0xe7c66c)
    //     0xe7c660: tbz             w0, #0, #0xe7c66c
    // 0xe7c664: r4 = LoadClassIdInstr(r0)
    //     0xe7c664: ldur            x4, [x0, #-1]
    //     0xe7c668: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c66c: sub             x4, x4, #0x5e
    // 0xe7c670: cmp             x4, #1
    // 0xe7c674: b.ls            #0xe7c688
    // 0xe7c678: r8 = String
    //     0xe7c678: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7c67c: r3 = Null
    //     0xe7c67c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37df0] Null
    //     0xe7c680: ldr             x3, [x3, #0xdf0]
    // 0xe7c684: r0 = String()
    //     0xe7c684: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7c688: ldur            x1, [fp, #-0x18]
    // 0xe7c68c: r0 = StringExtension.toKab()
    //     0xe7c68c: bl              #0xb6c940  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.toKab
    // 0xe7c690: mov             x1, x0
    // 0xe7c694: r0 = StringExtension.toCapitalize()
    //     0xe7c694: bl              #0x8fb4e4  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.toCapitalize
    // 0xe7c698: mov             x1, x0
    // 0xe7c69c: r2 = "Dki "
    //     0xe7c69c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed70] "Dki "
    //     0xe7c6a0: ldr             x2, [x2, #0xd70]
    // 0xe7c6a4: r3 = "DKI "
    //     0xe7c6a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ed78] "DKI "
    //     0xe7c6a8: ldr             x3, [x3, #0xd78]
    // 0xe7c6ac: r0 = replaceAll()
    //     0xe7c6ac: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xe7c6b0: mov             x1, x0
    // 0xe7c6b4: r2 = "Di "
    //     0xe7c6b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed80] "Di "
    //     0xe7c6b8: ldr             x2, [x2, #0xd80]
    // 0xe7c6bc: r3 = "DI "
    //     0xe7c6bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ed88] "DI "
    //     0xe7c6c0: ldr             x3, [x3, #0xd88]
    // 0xe7c6c4: r0 = replaceAll()
    //     0xe7c6c4: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xe7c6c8: mov             x4, x0
    // 0xe7c6cc: ldur            x3, [fp, #-8]
    // 0xe7c6d0: stur            x4, [fp, #-0x18]
    // 0xe7c6d4: r0 = LoadClassIdInstr(r3)
    //     0xe7c6d4: ldur            x0, [x3, #-1]
    //     0xe7c6d8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c6dc: mov             x1, x3
    // 0xe7c6e0: r2 = "parent_id"
    //     0xe7c6e0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19cf8] "parent_id"
    //     0xe7c6e4: ldr             x2, [x2, #0xcf8]
    // 0xe7c6e8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c6e8: sub             lr, x0, #0x114
    //     0xe7c6ec: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c6f0: blr             lr
    // 0xe7c6f4: mov             x3, x0
    // 0xe7c6f8: r2 = Null
    //     0xe7c6f8: mov             x2, NULL
    // 0xe7c6fc: r1 = Null
    //     0xe7c6fc: mov             x1, NULL
    // 0xe7c700: stur            x3, [fp, #-0x20]
    // 0xe7c704: branchIfSmi(r0, 0xe7c72c)
    //     0xe7c704: tbz             w0, #0, #0xe7c72c
    // 0xe7c708: r4 = LoadClassIdInstr(r0)
    //     0xe7c708: ldur            x4, [x0, #-1]
    //     0xe7c70c: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c710: sub             x4, x4, #0x3c
    // 0xe7c714: cmp             x4, #1
    // 0xe7c718: b.ls            #0xe7c72c
    // 0xe7c71c: r8 = int?
    //     0xe7c71c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7c720: r3 = Null
    //     0xe7c720: add             x3, PP, #0x37, lsl #12  ; [pp+0x37e00] Null
    //     0xe7c724: ldr             x3, [x3, #0xe00]
    // 0xe7c728: r0 = int?()
    //     0xe7c728: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7c72c: ldur            x3, [fp, #-8]
    // 0xe7c730: r0 = LoadClassIdInstr(r3)
    //     0xe7c730: ldur            x0, [x3, #-1]
    //     0xe7c734: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c738: mov             x1, x3
    // 0xe7c73c: r2 = "parent"
    //     0xe7c73c: ldr             x2, [PP, #0x4488]  ; [pp+0x4488] "parent"
    // 0xe7c740: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c740: sub             lr, x0, #0x114
    //     0xe7c744: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c748: blr             lr
    // 0xe7c74c: cmp             w0, NULL
    // 0xe7c750: b.eq            #0xe7c7a8
    // 0xe7c754: ldur            x3, [fp, #-8]
    // 0xe7c758: r0 = LoadClassIdInstr(r3)
    //     0xe7c758: ldur            x0, [x3, #-1]
    //     0xe7c75c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c760: mov             x1, x3
    // 0xe7c764: r2 = "parent"
    //     0xe7c764: ldr             x2, [PP, #0x4488]  ; [pp+0x4488] "parent"
    // 0xe7c768: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c768: sub             lr, x0, #0x114
    //     0xe7c76c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c770: blr             lr
    // 0xe7c774: mov             x3, x0
    // 0xe7c778: r2 = Null
    //     0xe7c778: mov             x2, NULL
    // 0xe7c77c: r1 = Null
    //     0xe7c77c: mov             x1, NULL
    // 0xe7c780: stur            x3, [fp, #-0x28]
    // 0xe7c784: r8 = Map<String, dynamic>
    //     0xe7c784: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7c788: r3 = Null
    //     0xe7c788: add             x3, PP, #0x37, lsl #12  ; [pp+0x37e10] Null
    //     0xe7c78c: ldr             x3, [x3, #0xe10]
    // 0xe7c790: r0 = Map<String, dynamic>()
    //     0xe7c790: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7c794: ldur            x2, [fp, #-0x28]
    // 0xe7c798: r1 = Null
    //     0xe7c798: mov             x1, NULL
    // 0xe7c79c: r0 = Area.fromMap()
    //     0xe7c79c: bl              #0xe7c5b4  ; [package:nuonline/app/data/models/area.dart] Area::Area.fromMap
    // 0xe7c7a0: mov             x6, x0
    // 0xe7c7a4: b               #0xe7c7ac
    // 0xe7c7a8: r6 = Null
    //     0xe7c7a8: mov             x6, NULL
    // 0xe7c7ac: ldur            x1, [fp, #-8]
    // 0xe7c7b0: ldur            x5, [fp, #-0x10]
    // 0xe7c7b4: ldur            x4, [fp, #-0x18]
    // 0xe7c7b8: ldur            x3, [fp, #-0x20]
    // 0xe7c7bc: stur            x6, [fp, #-0x28]
    // 0xe7c7c0: r0 = LoadClassIdInstr(r1)
    //     0xe7c7c0: ldur            x0, [x1, #-1]
    //     0xe7c7c4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c7c8: r2 = "group"
    //     0xe7c7c8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dd8] "group"
    //     0xe7c7cc: ldr             x2, [x2, #0xdd8]
    // 0xe7c7d0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c7d0: sub             lr, x0, #0x114
    //     0xe7c7d4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c7d8: blr             lr
    // 0xe7c7dc: mov             x3, x0
    // 0xe7c7e0: r2 = Null
    //     0xe7c7e0: mov             x2, NULL
    // 0xe7c7e4: r1 = Null
    //     0xe7c7e4: mov             x1, NULL
    // 0xe7c7e8: stur            x3, [fp, #-8]
    // 0xe7c7ec: r4 = 60
    //     0xe7c7ec: movz            x4, #0x3c
    // 0xe7c7f0: branchIfSmi(r0, 0xe7c7fc)
    //     0xe7c7f0: tbz             w0, #0, #0xe7c7fc
    // 0xe7c7f4: r4 = LoadClassIdInstr(r0)
    //     0xe7c7f4: ldur            x4, [x0, #-1]
    //     0xe7c7f8: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c7fc: sub             x4, x4, #0x5e
    // 0xe7c800: cmp             x4, #1
    // 0xe7c804: b.ls            #0xe7c818
    // 0xe7c808: r8 = String?
    //     0xe7c808: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xe7c80c: r3 = Null
    //     0xe7c80c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37e20] Null
    //     0xe7c810: ldr             x3, [x3, #0xe20]
    // 0xe7c814: r0 = String?()
    //     0xe7c814: bl              #0x600324  ; IsType_String?_Stub
    // 0xe7c818: r0 = Area()
    //     0xe7c818: bl              #0x7db004  ; AllocateAreaStub -> Area (size=0x1c)
    // 0xe7c81c: ldur            x1, [fp, #-0x10]
    // 0xe7c820: StoreField: r0->field_7 = r1
    //     0xe7c820: stur            w1, [x0, #7]
    // 0xe7c824: ldur            x1, [fp, #-0x18]
    // 0xe7c828: StoreField: r0->field_b = r1
    //     0xe7c828: stur            w1, [x0, #0xb]
    // 0xe7c82c: ldur            x1, [fp, #-0x20]
    // 0xe7c830: StoreField: r0->field_f = r1
    //     0xe7c830: stur            w1, [x0, #0xf]
    // 0xe7c834: ldur            x1, [fp, #-0x28]
    // 0xe7c838: StoreField: r0->field_13 = r1
    //     0xe7c838: stur            w1, [x0, #0x13]
    // 0xe7c83c: ldur            x1, [fp, #-8]
    // 0xe7c840: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7c840: stur            w1, [x0, #0x17]
    // 0xe7c844: LeaveFrame
    //     0xe7c844: mov             SP, fp
    //     0xe7c848: ldp             fp, lr, [SP], #0x10
    // 0xe7c84c: ret
    //     0xe7c84c: ret             
    // 0xe7c850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c854: b               #0xe7c5d4
  }
  _ toMap(/* No info */) {
    // ** addr: 0xe7d724, size: 0x130
    // 0xe7d724: EnterFrame
    //     0xe7d724: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d728: mov             fp, SP
    // 0xe7d72c: AllocStack(0x20)
    //     0xe7d72c: sub             SP, SP, #0x20
    // 0xe7d730: SetupParameters(Area this /* r1 => r0, fp-0x8 */)
    //     0xe7d730: mov             x0, x1
    //     0xe7d734: stur            x1, [fp, #-8]
    // 0xe7d738: CheckStackOverflow
    //     0xe7d738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d73c: cmp             SP, x16
    //     0xe7d740: b.ls            #0xe7d84c
    // 0xe7d744: r1 = Null
    //     0xe7d744: mov             x1, NULL
    // 0xe7d748: r2 = 20
    //     0xe7d748: movz            x2, #0x14
    // 0xe7d74c: r0 = AllocateArray()
    //     0xe7d74c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7d750: stur            x0, [fp, #-0x10]
    // 0xe7d754: r16 = "id"
    //     0xe7d754: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7d758: ldr             x16, [x16, #0x740]
    // 0xe7d75c: StoreField: r0->field_f = r16
    //     0xe7d75c: stur            w16, [x0, #0xf]
    // 0xe7d760: ldur            x2, [fp, #-8]
    // 0xe7d764: LoadField: r1 = r2->field_7
    //     0xe7d764: ldur            w1, [x2, #7]
    // 0xe7d768: DecompressPointer r1
    //     0xe7d768: add             x1, x1, HEAP, lsl #32
    // 0xe7d76c: StoreField: r0->field_13 = r1
    //     0xe7d76c: stur            w1, [x0, #0x13]
    // 0xe7d770: r16 = "name"
    //     0xe7d770: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7d774: ArrayStore: r0[0] = r16  ; List_4
    //     0xe7d774: stur            w16, [x0, #0x17]
    // 0xe7d778: LoadField: r1 = r2->field_b
    //     0xe7d778: ldur            w1, [x2, #0xb]
    // 0xe7d77c: DecompressPointer r1
    //     0xe7d77c: add             x1, x1, HEAP, lsl #32
    // 0xe7d780: StoreField: r0->field_1b = r1
    //     0xe7d780: stur            w1, [x0, #0x1b]
    // 0xe7d784: r16 = "parent_id"
    //     0xe7d784: add             x16, PP, #0x19, lsl #12  ; [pp+0x19cf8] "parent_id"
    //     0xe7d788: ldr             x16, [x16, #0xcf8]
    // 0xe7d78c: StoreField: r0->field_1f = r16
    //     0xe7d78c: stur            w16, [x0, #0x1f]
    // 0xe7d790: LoadField: r1 = r2->field_f
    //     0xe7d790: ldur            w1, [x2, #0xf]
    // 0xe7d794: DecompressPointer r1
    //     0xe7d794: add             x1, x1, HEAP, lsl #32
    // 0xe7d798: StoreField: r0->field_23 = r1
    //     0xe7d798: stur            w1, [x0, #0x23]
    // 0xe7d79c: r16 = "parent"
    //     0xe7d79c: ldr             x16, [PP, #0x4488]  ; [pp+0x4488] "parent"
    // 0xe7d7a0: StoreField: r0->field_27 = r16
    //     0xe7d7a0: stur            w16, [x0, #0x27]
    // 0xe7d7a4: LoadField: r1 = r2->field_13
    //     0xe7d7a4: ldur            w1, [x2, #0x13]
    // 0xe7d7a8: DecompressPointer r1
    //     0xe7d7a8: add             x1, x1, HEAP, lsl #32
    // 0xe7d7ac: cmp             w1, NULL
    // 0xe7d7b0: b.ne            #0xe7d7c4
    // 0xe7d7b4: mov             x3, x2
    // 0xe7d7b8: mov             x2, x0
    // 0xe7d7bc: r0 = Null
    //     0xe7d7bc: mov             x0, NULL
    // 0xe7d7c0: b               #0xe7d7d0
    // 0xe7d7c4: r0 = toMap()
    //     0xe7d7c4: bl              #0xe7d724  ; [package:nuonline/app/data/models/area.dart] Area::toMap
    // 0xe7d7c8: ldur            x3, [fp, #-8]
    // 0xe7d7cc: ldur            x2, [fp, #-0x10]
    // 0xe7d7d0: mov             x1, x2
    // 0xe7d7d4: ArrayStore: r1[7] = r0  ; List_4
    //     0xe7d7d4: add             x25, x1, #0x2b
    //     0xe7d7d8: str             w0, [x25]
    //     0xe7d7dc: tbz             w0, #0, #0xe7d7f8
    //     0xe7d7e0: ldurb           w16, [x1, #-1]
    //     0xe7d7e4: ldurb           w17, [x0, #-1]
    //     0xe7d7e8: and             x16, x17, x16, lsr #2
    //     0xe7d7ec: tst             x16, HEAP, lsr #32
    //     0xe7d7f0: b.eq            #0xe7d7f8
    //     0xe7d7f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7d7f8: r16 = "group"
    //     0xe7d7f8: add             x16, PP, #0x19, lsl #12  ; [pp+0x19dd8] "group"
    //     0xe7d7fc: ldr             x16, [x16, #0xdd8]
    // 0xe7d800: StoreField: r2->field_2f = r16
    //     0xe7d800: stur            w16, [x2, #0x2f]
    // 0xe7d804: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xe7d804: ldur            w0, [x3, #0x17]
    // 0xe7d808: DecompressPointer r0
    //     0xe7d808: add             x0, x0, HEAP, lsl #32
    // 0xe7d80c: mov             x1, x2
    // 0xe7d810: ArrayStore: r1[9] = r0  ; List_4
    //     0xe7d810: add             x25, x1, #0x33
    //     0xe7d814: str             w0, [x25]
    //     0xe7d818: tbz             w0, #0, #0xe7d834
    //     0xe7d81c: ldurb           w16, [x1, #-1]
    //     0xe7d820: ldurb           w17, [x0, #-1]
    //     0xe7d824: and             x16, x17, x16, lsr #2
    //     0xe7d828: tst             x16, HEAP, lsr #32
    //     0xe7d82c: b.eq            #0xe7d834
    //     0xe7d830: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7d834: r16 = <String, dynamic>
    //     0xe7d834: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe7d838: stp             x2, x16, [SP]
    // 0xe7d83c: r0 = Map._fromLiteral()
    //     0xe7d83c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe7d840: LeaveFrame
    //     0xe7d840: mov             SP, fp
    //     0xe7d844: ldp             fp, lr, [SP], #0x10
    // 0xe7d848: ret
    //     0xe7d848: ret             
    // 0xe7d84c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d84c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d850: b               #0xe7d744
  }
}
