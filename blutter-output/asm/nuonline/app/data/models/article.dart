// lib: , url: package:nuonline/app/data/models/article.dart

// class id: 1050005, size: 0x8
class :: {
}

// class id: 1670, size: 0x14, field offset: 0xc
class ArticleInsertionAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5ebfc, size: 0x3ac
    // 0xa5ebfc: EnterFrame
    //     0xa5ebfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa5ec00: mov             fp, SP
    // 0xa5ec04: AllocStack(0x50)
    //     0xa5ec04: sub             SP, SP, #0x50
    // 0xa5ec08: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5ec08: stur            x2, [fp, #-0x20]
    // 0xa5ec0c: CheckStackOverflow
    //     0xa5ec0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5ec10: cmp             SP, x16
    //     0xa5ec14: b.ls            #0xa5ef90
    // 0xa5ec18: LoadField: r3 = r2->field_23
    //     0xa5ec18: ldur            x3, [x2, #0x23]
    // 0xa5ec1c: add             x0, x3, #1
    // 0xa5ec20: LoadField: r1 = r2->field_1b
    //     0xa5ec20: ldur            x1, [x2, #0x1b]
    // 0xa5ec24: cmp             x0, x1
    // 0xa5ec28: b.gt            #0xa5ef34
    // 0xa5ec2c: LoadField: r4 = r2->field_7
    //     0xa5ec2c: ldur            w4, [x2, #7]
    // 0xa5ec30: DecompressPointer r4
    //     0xa5ec30: add             x4, x4, HEAP, lsl #32
    // 0xa5ec34: stur            x4, [fp, #-0x18]
    // 0xa5ec38: StoreField: r2->field_23 = r0
    //     0xa5ec38: stur            x0, [x2, #0x23]
    // 0xa5ec3c: LoadField: r0 = r4->field_13
    //     0xa5ec3c: ldur            w0, [x4, #0x13]
    // 0xa5ec40: r5 = LoadInt32Instr(r0)
    //     0xa5ec40: sbfx            x5, x0, #1, #0x1f
    // 0xa5ec44: mov             x0, x5
    // 0xa5ec48: mov             x1, x3
    // 0xa5ec4c: stur            x5, [fp, #-0x10]
    // 0xa5ec50: cmp             x1, x0
    // 0xa5ec54: b.hs            #0xa5ef98
    // 0xa5ec58: LoadField: r0 = r4->field_7
    //     0xa5ec58: ldur            x0, [x4, #7]
    // 0xa5ec5c: ldrb            w1, [x0, x3]
    // 0xa5ec60: stur            x1, [fp, #-8]
    // 0xa5ec64: r16 = <int, dynamic>
    //     0xa5ec64: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5ec68: ldr             x16, [x16, #0xac0]
    // 0xa5ec6c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5ec70: stp             lr, x16, [SP]
    // 0xa5ec74: r0 = Map._fromLiteral()
    //     0xa5ec74: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5ec78: mov             x2, x0
    // 0xa5ec7c: stur            x2, [fp, #-0x38]
    // 0xa5ec80: r6 = 0
    //     0xa5ec80: movz            x6, #0
    // 0xa5ec84: ldur            x3, [fp, #-0x20]
    // 0xa5ec88: ldur            x4, [fp, #-0x18]
    // 0xa5ec8c: ldur            x5, [fp, #-8]
    // 0xa5ec90: stur            x6, [fp, #-0x30]
    // 0xa5ec94: CheckStackOverflow
    //     0xa5ec94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5ec98: cmp             SP, x16
    //     0xa5ec9c: b.ls            #0xa5ef9c
    // 0xa5eca0: cmp             x6, x5
    // 0xa5eca4: b.ge            #0xa5ed30
    // 0xa5eca8: LoadField: r7 = r3->field_23
    //     0xa5eca8: ldur            x7, [x3, #0x23]
    // 0xa5ecac: add             x0, x7, #1
    // 0xa5ecb0: LoadField: r1 = r3->field_1b
    //     0xa5ecb0: ldur            x1, [x3, #0x1b]
    // 0xa5ecb4: cmp             x0, x1
    // 0xa5ecb8: b.gt            #0xa5ef5c
    // 0xa5ecbc: StoreField: r3->field_23 = r0
    //     0xa5ecbc: stur            x0, [x3, #0x23]
    // 0xa5ecc0: ldur            x0, [fp, #-0x10]
    // 0xa5ecc4: mov             x1, x7
    // 0xa5ecc8: cmp             x1, x0
    // 0xa5eccc: b.hs            #0xa5efa4
    // 0xa5ecd0: LoadField: r0 = r4->field_7
    //     0xa5ecd0: ldur            x0, [x4, #7]
    // 0xa5ecd4: ldrb            w8, [x0, x7]
    // 0xa5ecd8: mov             x1, x3
    // 0xa5ecdc: stur            x8, [fp, #-0x28]
    // 0xa5ece0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5ece0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5ece4: r0 = read()
    //     0xa5ece4: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5ece8: mov             x1, x0
    // 0xa5ecec: ldur            x0, [fp, #-0x28]
    // 0xa5ecf0: lsl             x2, x0, #1
    // 0xa5ecf4: r16 = LoadInt32Instr(r2)
    //     0xa5ecf4: sbfx            x16, x2, #1, #0x1f
    // 0xa5ecf8: r17 = 11601
    //     0xa5ecf8: movz            x17, #0x2d51
    // 0xa5ecfc: mul             x0, x16, x17
    // 0xa5ed00: umulh           x16, x16, x17
    // 0xa5ed04: eor             x0, x0, x16
    // 0xa5ed08: r0 = 0
    //     0xa5ed08: eor             x0, x0, x0, lsr #32
    // 0xa5ed0c: ubfiz           x0, x0, #1, #0x1e
    // 0xa5ed10: r5 = LoadInt32Instr(r0)
    //     0xa5ed10: sbfx            x5, x0, #1, #0x1f
    // 0xa5ed14: mov             x3, x1
    // 0xa5ed18: ldur            x1, [fp, #-0x38]
    // 0xa5ed1c: r0 = _set()
    //     0xa5ed1c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5ed20: ldur            x0, [fp, #-0x30]
    // 0xa5ed24: add             x6, x0, #1
    // 0xa5ed28: ldur            x2, [fp, #-0x38]
    // 0xa5ed2c: b               #0xa5ec84
    // 0xa5ed30: mov             x0, x2
    // 0xa5ed34: mov             x1, x0
    // 0xa5ed38: r2 = 0
    //     0xa5ed38: movz            x2, #0
    // 0xa5ed3c: r0 = _getValueOrData()
    //     0xa5ed3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5ed40: ldur            x3, [fp, #-0x38]
    // 0xa5ed44: LoadField: r1 = r3->field_f
    //     0xa5ed44: ldur            w1, [x3, #0xf]
    // 0xa5ed48: DecompressPointer r1
    //     0xa5ed48: add             x1, x1, HEAP, lsl #32
    // 0xa5ed4c: cmp             w1, w0
    // 0xa5ed50: b.ne            #0xa5ed5c
    // 0xa5ed54: r4 = Null
    //     0xa5ed54: mov             x4, NULL
    // 0xa5ed58: b               #0xa5ed60
    // 0xa5ed5c: mov             x4, x0
    // 0xa5ed60: mov             x0, x4
    // 0xa5ed64: stur            x4, [fp, #-0x18]
    // 0xa5ed68: r2 = Null
    //     0xa5ed68: mov             x2, NULL
    // 0xa5ed6c: r1 = Null
    //     0xa5ed6c: mov             x1, NULL
    // 0xa5ed70: branchIfSmi(r0, 0xa5ed98)
    //     0xa5ed70: tbz             w0, #0, #0xa5ed98
    // 0xa5ed74: r4 = LoadClassIdInstr(r0)
    //     0xa5ed74: ldur            x4, [x0, #-1]
    //     0xa5ed78: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ed7c: sub             x4, x4, #0x3c
    // 0xa5ed80: cmp             x4, #1
    // 0xa5ed84: b.ls            #0xa5ed98
    // 0xa5ed88: r8 = int
    //     0xa5ed88: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5ed8c: r3 = Null
    //     0xa5ed8c: add             x3, PP, #0x21, lsl #12  ; [pp+0x218c0] Null
    //     0xa5ed90: ldr             x3, [x3, #0x8c0]
    // 0xa5ed94: r0 = int()
    //     0xa5ed94: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5ed98: ldur            x1, [fp, #-0x38]
    // 0xa5ed9c: r2 = 2
    //     0xa5ed9c: movz            x2, #0x2
    // 0xa5eda0: r0 = _getValueOrData()
    //     0xa5eda0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5eda4: ldur            x3, [fp, #-0x38]
    // 0xa5eda8: LoadField: r1 = r3->field_f
    //     0xa5eda8: ldur            w1, [x3, #0xf]
    // 0xa5edac: DecompressPointer r1
    //     0xa5edac: add             x1, x1, HEAP, lsl #32
    // 0xa5edb0: cmp             w1, w0
    // 0xa5edb4: b.ne            #0xa5edc0
    // 0xa5edb8: r4 = Null
    //     0xa5edb8: mov             x4, NULL
    // 0xa5edbc: b               #0xa5edc4
    // 0xa5edc0: mov             x4, x0
    // 0xa5edc4: mov             x0, x4
    // 0xa5edc8: stur            x4, [fp, #-0x20]
    // 0xa5edcc: r2 = Null
    //     0xa5edcc: mov             x2, NULL
    // 0xa5edd0: r1 = Null
    //     0xa5edd0: mov             x1, NULL
    // 0xa5edd4: r4 = 60
    //     0xa5edd4: movz            x4, #0x3c
    // 0xa5edd8: branchIfSmi(r0, 0xa5ede4)
    //     0xa5edd8: tbz             w0, #0, #0xa5ede4
    // 0xa5eddc: r4 = LoadClassIdInstr(r0)
    //     0xa5eddc: ldur            x4, [x0, #-1]
    //     0xa5ede0: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ede4: sub             x4, x4, #0x5e
    // 0xa5ede8: cmp             x4, #1
    // 0xa5edec: b.ls            #0xa5ee00
    // 0xa5edf0: r8 = String
    //     0xa5edf0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5edf4: r3 = Null
    //     0xa5edf4: add             x3, PP, #0x21, lsl #12  ; [pp+0x218d0] Null
    //     0xa5edf8: ldr             x3, [x3, #0x8d0]
    // 0xa5edfc: r0 = String()
    //     0xa5edfc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5ee00: ldur            x1, [fp, #-0x38]
    // 0xa5ee04: r2 = 4
    //     0xa5ee04: movz            x2, #0x4
    // 0xa5ee08: r0 = _getValueOrData()
    //     0xa5ee08: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5ee0c: ldur            x3, [fp, #-0x38]
    // 0xa5ee10: LoadField: r1 = r3->field_f
    //     0xa5ee10: ldur            w1, [x3, #0xf]
    // 0xa5ee14: DecompressPointer r1
    //     0xa5ee14: add             x1, x1, HEAP, lsl #32
    // 0xa5ee18: cmp             w1, w0
    // 0xa5ee1c: b.ne            #0xa5ee28
    // 0xa5ee20: r4 = Null
    //     0xa5ee20: mov             x4, NULL
    // 0xa5ee24: b               #0xa5ee2c
    // 0xa5ee28: mov             x4, x0
    // 0xa5ee2c: mov             x0, x4
    // 0xa5ee30: stur            x4, [fp, #-0x40]
    // 0xa5ee34: r2 = Null
    //     0xa5ee34: mov             x2, NULL
    // 0xa5ee38: r1 = Null
    //     0xa5ee38: mov             x1, NULL
    // 0xa5ee3c: r4 = 60
    //     0xa5ee3c: movz            x4, #0x3c
    // 0xa5ee40: branchIfSmi(r0, 0xa5ee4c)
    //     0xa5ee40: tbz             w0, #0, #0xa5ee4c
    // 0xa5ee44: r4 = LoadClassIdInstr(r0)
    //     0xa5ee44: ldur            x4, [x0, #-1]
    //     0xa5ee48: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ee4c: sub             x4, x4, #0x5e
    // 0xa5ee50: cmp             x4, #1
    // 0xa5ee54: b.ls            #0xa5ee68
    // 0xa5ee58: r8 = String
    //     0xa5ee58: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5ee5c: r3 = Null
    //     0xa5ee5c: add             x3, PP, #0x21, lsl #12  ; [pp+0x218e0] Null
    //     0xa5ee60: ldr             x3, [x3, #0x8e0]
    // 0xa5ee64: r0 = String()
    //     0xa5ee64: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5ee68: ldur            x1, [fp, #-0x38]
    // 0xa5ee6c: r2 = 6
    //     0xa5ee6c: movz            x2, #0x6
    // 0xa5ee70: r0 = _getValueOrData()
    //     0xa5ee70: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5ee74: mov             x1, x0
    // 0xa5ee78: ldur            x0, [fp, #-0x38]
    // 0xa5ee7c: LoadField: r2 = r0->field_f
    //     0xa5ee7c: ldur            w2, [x0, #0xf]
    // 0xa5ee80: DecompressPointer r2
    //     0xa5ee80: add             x2, x2, HEAP, lsl #32
    // 0xa5ee84: cmp             w2, w1
    // 0xa5ee88: b.ne            #0xa5ee94
    // 0xa5ee8c: r6 = Null
    //     0xa5ee8c: mov             x6, NULL
    // 0xa5ee90: b               #0xa5ee98
    // 0xa5ee94: mov             x6, x1
    // 0xa5ee98: ldur            x5, [fp, #-0x18]
    // 0xa5ee9c: ldur            x4, [fp, #-0x20]
    // 0xa5eea0: ldur            x3, [fp, #-0x40]
    // 0xa5eea4: mov             x0, x6
    // 0xa5eea8: stur            x6, [fp, #-0x38]
    // 0xa5eeac: r2 = Null
    //     0xa5eeac: mov             x2, NULL
    // 0xa5eeb0: r1 = Null
    //     0xa5eeb0: mov             x1, NULL
    // 0xa5eeb4: branchIfSmi(r0, 0xa5eedc)
    //     0xa5eeb4: tbz             w0, #0, #0xa5eedc
    // 0xa5eeb8: r4 = LoadClassIdInstr(r0)
    //     0xa5eeb8: ldur            x4, [x0, #-1]
    //     0xa5eebc: ubfx            x4, x4, #0xc, #0x14
    // 0xa5eec0: sub             x4, x4, #0x3c
    // 0xa5eec4: cmp             x4, #1
    // 0xa5eec8: b.ls            #0xa5eedc
    // 0xa5eecc: r8 = int
    //     0xa5eecc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5eed0: r3 = Null
    //     0xa5eed0: add             x3, PP, #0x21, lsl #12  ; [pp+0x218f0] Null
    //     0xa5eed4: ldr             x3, [x3, #0x8f0]
    // 0xa5eed8: r0 = int()
    //     0xa5eed8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5eedc: ldur            x0, [fp, #-0x18]
    // 0xa5eee0: r1 = LoadInt32Instr(r0)
    //     0xa5eee0: sbfx            x1, x0, #1, #0x1f
    //     0xa5eee4: tbz             w0, #0, #0xa5eeec
    //     0xa5eee8: ldur            x1, [x0, #7]
    // 0xa5eeec: stur            x1, [fp, #-8]
    // 0xa5eef0: r0 = ArticleInsertion()
    //     0xa5eef0: bl              #0x8eca48  ; AllocateArticleInsertionStub -> ArticleInsertion (size=0x20)
    // 0xa5eef4: mov             x1, x0
    // 0xa5eef8: ldur            x0, [fp, #-8]
    // 0xa5eefc: StoreField: r1->field_7 = r0
    //     0xa5eefc: stur            x0, [x1, #7]
    // 0xa5ef00: ldur            x0, [fp, #-0x20]
    // 0xa5ef04: StoreField: r1->field_f = r0
    //     0xa5ef04: stur            w0, [x1, #0xf]
    // 0xa5ef08: ldur            x0, [fp, #-0x40]
    // 0xa5ef0c: StoreField: r1->field_13 = r0
    //     0xa5ef0c: stur            w0, [x1, #0x13]
    // 0xa5ef10: ldur            x0, [fp, #-0x38]
    // 0xa5ef14: r2 = LoadInt32Instr(r0)
    //     0xa5ef14: sbfx            x2, x0, #1, #0x1f
    //     0xa5ef18: tbz             w0, #0, #0xa5ef20
    //     0xa5ef1c: ldur            x2, [x0, #7]
    // 0xa5ef20: ArrayStore: r1[0] = r2  ; List_8
    //     0xa5ef20: stur            x2, [x1, #0x17]
    // 0xa5ef24: mov             x0, x1
    // 0xa5ef28: LeaveFrame
    //     0xa5ef28: mov             SP, fp
    //     0xa5ef2c: ldp             fp, lr, [SP], #0x10
    // 0xa5ef30: ret
    //     0xa5ef30: ret             
    // 0xa5ef34: r0 = RangeError()
    //     0xa5ef34: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5ef38: mov             x1, x0
    // 0xa5ef3c: r0 = "Not enough bytes available."
    //     0xa5ef3c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5ef40: ldr             x0, [x0, #0x8a8]
    // 0xa5ef44: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5ef44: stur            w0, [x1, #0x17]
    // 0xa5ef48: r2 = false
    //     0xa5ef48: add             x2, NULL, #0x30  ; false
    // 0xa5ef4c: StoreField: r1->field_b = r2
    //     0xa5ef4c: stur            w2, [x1, #0xb]
    // 0xa5ef50: mov             x0, x1
    // 0xa5ef54: r0 = Throw()
    //     0xa5ef54: bl              #0xec04b8  ; ThrowStub
    // 0xa5ef58: brk             #0
    // 0xa5ef5c: r0 = "Not enough bytes available."
    //     0xa5ef5c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5ef60: ldr             x0, [x0, #0x8a8]
    // 0xa5ef64: r2 = false
    //     0xa5ef64: add             x2, NULL, #0x30  ; false
    // 0xa5ef68: r0 = RangeError()
    //     0xa5ef68: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5ef6c: mov             x1, x0
    // 0xa5ef70: r0 = "Not enough bytes available."
    //     0xa5ef70: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5ef74: ldr             x0, [x0, #0x8a8]
    // 0xa5ef78: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5ef78: stur            w0, [x1, #0x17]
    // 0xa5ef7c: r0 = false
    //     0xa5ef7c: add             x0, NULL, #0x30  ; false
    // 0xa5ef80: StoreField: r1->field_b = r0
    //     0xa5ef80: stur            w0, [x1, #0xb]
    // 0xa5ef84: mov             x0, x1
    // 0xa5ef88: r0 = Throw()
    //     0xa5ef88: bl              #0xec04b8  ; ThrowStub
    // 0xa5ef8c: brk             #0
    // 0xa5ef90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ef90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5ef94: b               #0xa5ec18
    // 0xa5ef98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5ef98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5ef9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ef9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5efa0: b               #0xa5eca0
    // 0xa5efa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5efa4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd0368, size: 0x32c
    // 0xbd0368: EnterFrame
    //     0xbd0368: stp             fp, lr, [SP, #-0x10]!
    //     0xbd036c: mov             fp, SP
    // 0xbd0370: AllocStack(0x28)
    //     0xbd0370: sub             SP, SP, #0x28
    // 0xbd0374: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd0374: mov             x4, x2
    //     0xbd0378: stur            x2, [fp, #-8]
    //     0xbd037c: stur            x3, [fp, #-0x10]
    // 0xbd0380: CheckStackOverflow
    //     0xbd0380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd0384: cmp             SP, x16
    //     0xbd0388: b.ls            #0xbd0678
    // 0xbd038c: mov             x0, x3
    // 0xbd0390: r2 = Null
    //     0xbd0390: mov             x2, NULL
    // 0xbd0394: r1 = Null
    //     0xbd0394: mov             x1, NULL
    // 0xbd0398: r4 = 60
    //     0xbd0398: movz            x4, #0x3c
    // 0xbd039c: branchIfSmi(r0, 0xbd03a8)
    //     0xbd039c: tbz             w0, #0, #0xbd03a8
    // 0xbd03a0: r4 = LoadClassIdInstr(r0)
    //     0xbd03a0: ldur            x4, [x0, #-1]
    //     0xbd03a4: ubfx            x4, x4, #0xc, #0x14
    // 0xbd03a8: r17 = 5597
    //     0xbd03a8: movz            x17, #0x15dd
    // 0xbd03ac: cmp             x4, x17
    // 0xbd03b0: b.eq            #0xbd03c8
    // 0xbd03b4: r8 = ArticleInsertion
    //     0xbd03b4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b520] Type: ArticleInsertion
    //     0xbd03b8: ldr             x8, [x8, #0x520]
    // 0xbd03bc: r3 = Null
    //     0xbd03bc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b528] Null
    //     0xbd03c0: ldr             x3, [x3, #0x528]
    // 0xbd03c4: r0 = ArticleInsertion()
    //     0xbd03c4: bl              #0x835fe0  ; IsType_ArticleInsertion_Stub
    // 0xbd03c8: ldur            x0, [fp, #-8]
    // 0xbd03cc: LoadField: r1 = r0->field_b
    //     0xbd03cc: ldur            w1, [x0, #0xb]
    // 0xbd03d0: DecompressPointer r1
    //     0xbd03d0: add             x1, x1, HEAP, lsl #32
    // 0xbd03d4: LoadField: r2 = r1->field_13
    //     0xbd03d4: ldur            w2, [x1, #0x13]
    // 0xbd03d8: LoadField: r1 = r0->field_13
    //     0xbd03d8: ldur            x1, [x0, #0x13]
    // 0xbd03dc: r3 = LoadInt32Instr(r2)
    //     0xbd03dc: sbfx            x3, x2, #1, #0x1f
    // 0xbd03e0: sub             x2, x3, x1
    // 0xbd03e4: cmp             x2, #1
    // 0xbd03e8: b.ge            #0xbd03f8
    // 0xbd03ec: mov             x1, x0
    // 0xbd03f0: r2 = 1
    //     0xbd03f0: movz            x2, #0x1
    // 0xbd03f4: r0 = _increaseBufferSize()
    //     0xbd03f4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd03f8: ldur            x3, [fp, #-8]
    // 0xbd03fc: r2 = 4
    //     0xbd03fc: movz            x2, #0x4
    // 0xbd0400: LoadField: r4 = r3->field_b
    //     0xbd0400: ldur            w4, [x3, #0xb]
    // 0xbd0404: DecompressPointer r4
    //     0xbd0404: add             x4, x4, HEAP, lsl #32
    // 0xbd0408: LoadField: r5 = r3->field_13
    //     0xbd0408: ldur            x5, [x3, #0x13]
    // 0xbd040c: add             x6, x5, #1
    // 0xbd0410: StoreField: r3->field_13 = r6
    //     0xbd0410: stur            x6, [x3, #0x13]
    // 0xbd0414: LoadField: r0 = r4->field_13
    //     0xbd0414: ldur            w0, [x4, #0x13]
    // 0xbd0418: r7 = LoadInt32Instr(r0)
    //     0xbd0418: sbfx            x7, x0, #1, #0x1f
    // 0xbd041c: mov             x0, x7
    // 0xbd0420: mov             x1, x5
    // 0xbd0424: cmp             x1, x0
    // 0xbd0428: b.hs            #0xbd0680
    // 0xbd042c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd042c: add             x0, x4, x5
    //     0xbd0430: strb            w2, [x0, #0x17]
    // 0xbd0434: sub             x0, x7, x6
    // 0xbd0438: cmp             x0, #1
    // 0xbd043c: b.ge            #0xbd044c
    // 0xbd0440: mov             x1, x3
    // 0xbd0444: r2 = 1
    //     0xbd0444: movz            x2, #0x1
    // 0xbd0448: r0 = _increaseBufferSize()
    //     0xbd0448: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd044c: ldur            x2, [fp, #-8]
    // 0xbd0450: ldur            x3, [fp, #-0x10]
    // 0xbd0454: LoadField: r4 = r2->field_b
    //     0xbd0454: ldur            w4, [x2, #0xb]
    // 0xbd0458: DecompressPointer r4
    //     0xbd0458: add             x4, x4, HEAP, lsl #32
    // 0xbd045c: LoadField: r5 = r2->field_13
    //     0xbd045c: ldur            x5, [x2, #0x13]
    // 0xbd0460: add             x0, x5, #1
    // 0xbd0464: StoreField: r2->field_13 = r0
    //     0xbd0464: stur            x0, [x2, #0x13]
    // 0xbd0468: LoadField: r0 = r4->field_13
    //     0xbd0468: ldur            w0, [x4, #0x13]
    // 0xbd046c: r1 = LoadInt32Instr(r0)
    //     0xbd046c: sbfx            x1, x0, #1, #0x1f
    // 0xbd0470: mov             x0, x1
    // 0xbd0474: mov             x1, x5
    // 0xbd0478: cmp             x1, x0
    // 0xbd047c: b.hs            #0xbd0684
    // 0xbd0480: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd0480: add             x0, x4, x5
    //     0xbd0484: strb            wzr, [x0, #0x17]
    // 0xbd0488: LoadField: r4 = r3->field_7
    //     0xbd0488: ldur            x4, [x3, #7]
    // 0xbd048c: r0 = BoxInt64Instr(r4)
    //     0xbd048c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd0490: cmp             x4, x0, asr #1
    //     0xbd0494: b.eq            #0xbd04a0
    //     0xbd0498: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd049c: stur            x4, [x0, #7]
    // 0xbd04a0: r16 = <int>
    //     0xbd04a0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd04a4: stp             x2, x16, [SP, #8]
    // 0xbd04a8: str             x0, [SP]
    // 0xbd04ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd04ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd04b0: r0 = write()
    //     0xbd04b0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd04b4: ldur            x0, [fp, #-8]
    // 0xbd04b8: LoadField: r1 = r0->field_b
    //     0xbd04b8: ldur            w1, [x0, #0xb]
    // 0xbd04bc: DecompressPointer r1
    //     0xbd04bc: add             x1, x1, HEAP, lsl #32
    // 0xbd04c0: LoadField: r2 = r1->field_13
    //     0xbd04c0: ldur            w2, [x1, #0x13]
    // 0xbd04c4: LoadField: r1 = r0->field_13
    //     0xbd04c4: ldur            x1, [x0, #0x13]
    // 0xbd04c8: r3 = LoadInt32Instr(r2)
    //     0xbd04c8: sbfx            x3, x2, #1, #0x1f
    // 0xbd04cc: sub             x2, x3, x1
    // 0xbd04d0: cmp             x2, #1
    // 0xbd04d4: b.ge            #0xbd04e4
    // 0xbd04d8: mov             x1, x0
    // 0xbd04dc: r2 = 1
    //     0xbd04dc: movz            x2, #0x1
    // 0xbd04e0: r0 = _increaseBufferSize()
    //     0xbd04e0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd04e4: ldur            x2, [fp, #-8]
    // 0xbd04e8: ldur            x3, [fp, #-0x10]
    // 0xbd04ec: r4 = 1
    //     0xbd04ec: movz            x4, #0x1
    // 0xbd04f0: LoadField: r5 = r2->field_b
    //     0xbd04f0: ldur            w5, [x2, #0xb]
    // 0xbd04f4: DecompressPointer r5
    //     0xbd04f4: add             x5, x5, HEAP, lsl #32
    // 0xbd04f8: LoadField: r6 = r2->field_13
    //     0xbd04f8: ldur            x6, [x2, #0x13]
    // 0xbd04fc: add             x0, x6, #1
    // 0xbd0500: StoreField: r2->field_13 = r0
    //     0xbd0500: stur            x0, [x2, #0x13]
    // 0xbd0504: LoadField: r0 = r5->field_13
    //     0xbd0504: ldur            w0, [x5, #0x13]
    // 0xbd0508: r1 = LoadInt32Instr(r0)
    //     0xbd0508: sbfx            x1, x0, #1, #0x1f
    // 0xbd050c: mov             x0, x1
    // 0xbd0510: mov             x1, x6
    // 0xbd0514: cmp             x1, x0
    // 0xbd0518: b.hs            #0xbd0688
    // 0xbd051c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd051c: add             x0, x5, x6
    //     0xbd0520: strb            w4, [x0, #0x17]
    // 0xbd0524: LoadField: r0 = r3->field_f
    //     0xbd0524: ldur            w0, [x3, #0xf]
    // 0xbd0528: DecompressPointer r0
    //     0xbd0528: add             x0, x0, HEAP, lsl #32
    // 0xbd052c: r16 = <String>
    //     0xbd052c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd0530: stp             x2, x16, [SP, #8]
    // 0xbd0534: str             x0, [SP]
    // 0xbd0538: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0538: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd053c: r0 = write()
    //     0xbd053c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0540: ldur            x0, [fp, #-8]
    // 0xbd0544: LoadField: r1 = r0->field_b
    //     0xbd0544: ldur            w1, [x0, #0xb]
    // 0xbd0548: DecompressPointer r1
    //     0xbd0548: add             x1, x1, HEAP, lsl #32
    // 0xbd054c: LoadField: r2 = r1->field_13
    //     0xbd054c: ldur            w2, [x1, #0x13]
    // 0xbd0550: LoadField: r1 = r0->field_13
    //     0xbd0550: ldur            x1, [x0, #0x13]
    // 0xbd0554: r3 = LoadInt32Instr(r2)
    //     0xbd0554: sbfx            x3, x2, #1, #0x1f
    // 0xbd0558: sub             x2, x3, x1
    // 0xbd055c: cmp             x2, #1
    // 0xbd0560: b.ge            #0xbd0570
    // 0xbd0564: mov             x1, x0
    // 0xbd0568: r2 = 1
    //     0xbd0568: movz            x2, #0x1
    // 0xbd056c: r0 = _increaseBufferSize()
    //     0xbd056c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0570: ldur            x2, [fp, #-8]
    // 0xbd0574: ldur            x3, [fp, #-0x10]
    // 0xbd0578: r4 = 2
    //     0xbd0578: movz            x4, #0x2
    // 0xbd057c: LoadField: r5 = r2->field_b
    //     0xbd057c: ldur            w5, [x2, #0xb]
    // 0xbd0580: DecompressPointer r5
    //     0xbd0580: add             x5, x5, HEAP, lsl #32
    // 0xbd0584: LoadField: r6 = r2->field_13
    //     0xbd0584: ldur            x6, [x2, #0x13]
    // 0xbd0588: add             x0, x6, #1
    // 0xbd058c: StoreField: r2->field_13 = r0
    //     0xbd058c: stur            x0, [x2, #0x13]
    // 0xbd0590: LoadField: r0 = r5->field_13
    //     0xbd0590: ldur            w0, [x5, #0x13]
    // 0xbd0594: r1 = LoadInt32Instr(r0)
    //     0xbd0594: sbfx            x1, x0, #1, #0x1f
    // 0xbd0598: mov             x0, x1
    // 0xbd059c: mov             x1, x6
    // 0xbd05a0: cmp             x1, x0
    // 0xbd05a4: b.hs            #0xbd068c
    // 0xbd05a8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd05a8: add             x0, x5, x6
    //     0xbd05ac: strb            w4, [x0, #0x17]
    // 0xbd05b0: LoadField: r0 = r3->field_13
    //     0xbd05b0: ldur            w0, [x3, #0x13]
    // 0xbd05b4: DecompressPointer r0
    //     0xbd05b4: add             x0, x0, HEAP, lsl #32
    // 0xbd05b8: r16 = <String>
    //     0xbd05b8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd05bc: stp             x2, x16, [SP, #8]
    // 0xbd05c0: str             x0, [SP]
    // 0xbd05c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd05c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd05c8: r0 = write()
    //     0xbd05c8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd05cc: ldur            x0, [fp, #-8]
    // 0xbd05d0: LoadField: r1 = r0->field_b
    //     0xbd05d0: ldur            w1, [x0, #0xb]
    // 0xbd05d4: DecompressPointer r1
    //     0xbd05d4: add             x1, x1, HEAP, lsl #32
    // 0xbd05d8: LoadField: r2 = r1->field_13
    //     0xbd05d8: ldur            w2, [x1, #0x13]
    // 0xbd05dc: LoadField: r1 = r0->field_13
    //     0xbd05dc: ldur            x1, [x0, #0x13]
    // 0xbd05e0: r3 = LoadInt32Instr(r2)
    //     0xbd05e0: sbfx            x3, x2, #1, #0x1f
    // 0xbd05e4: sub             x2, x3, x1
    // 0xbd05e8: cmp             x2, #1
    // 0xbd05ec: b.ge            #0xbd05fc
    // 0xbd05f0: mov             x1, x0
    // 0xbd05f4: r2 = 1
    //     0xbd05f4: movz            x2, #0x1
    // 0xbd05f8: r0 = _increaseBufferSize()
    //     0xbd05f8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd05fc: ldur            x2, [fp, #-8]
    // 0xbd0600: ldur            x3, [fp, #-0x10]
    // 0xbd0604: r4 = 3
    //     0xbd0604: movz            x4, #0x3
    // 0xbd0608: LoadField: r5 = r2->field_b
    //     0xbd0608: ldur            w5, [x2, #0xb]
    // 0xbd060c: DecompressPointer r5
    //     0xbd060c: add             x5, x5, HEAP, lsl #32
    // 0xbd0610: LoadField: r6 = r2->field_13
    //     0xbd0610: ldur            x6, [x2, #0x13]
    // 0xbd0614: add             x0, x6, #1
    // 0xbd0618: StoreField: r2->field_13 = r0
    //     0xbd0618: stur            x0, [x2, #0x13]
    // 0xbd061c: LoadField: r0 = r5->field_13
    //     0xbd061c: ldur            w0, [x5, #0x13]
    // 0xbd0620: r1 = LoadInt32Instr(r0)
    //     0xbd0620: sbfx            x1, x0, #1, #0x1f
    // 0xbd0624: mov             x0, x1
    // 0xbd0628: mov             x1, x6
    // 0xbd062c: cmp             x1, x0
    // 0xbd0630: b.hs            #0xbd0690
    // 0xbd0634: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0634: add             x0, x5, x6
    //     0xbd0638: strb            w4, [x0, #0x17]
    // 0xbd063c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd063c: ldur            x4, [x3, #0x17]
    // 0xbd0640: r0 = BoxInt64Instr(r4)
    //     0xbd0640: sbfiz           x0, x4, #1, #0x1f
    //     0xbd0644: cmp             x4, x0, asr #1
    //     0xbd0648: b.eq            #0xbd0654
    //     0xbd064c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd0650: stur            x4, [x0, #7]
    // 0xbd0654: r16 = <int>
    //     0xbd0654: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd0658: stp             x2, x16, [SP, #8]
    // 0xbd065c: str             x0, [SP]
    // 0xbd0660: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0660: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0664: r0 = write()
    //     0xbd0664: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0668: r0 = Null
    //     0xbd0668: mov             x0, NULL
    // 0xbd066c: LeaveFrame
    //     0xbd066c: mov             SP, fp
    //     0xbd0670: ldp             fp, lr, [SP], #0x10
    // 0xbd0674: ret
    //     0xbd0674: ret             
    // 0xbd0678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd0678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd067c: b               #0xbd038c
    // 0xbd0680: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0680: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0684: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0684: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0688: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0688: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd068c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd068c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0690: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0690: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0000, size: 0x24
    // 0xbf0000: r1 = 92
    //     0xbf0000: movz            x1, #0x5c
    // 0xbf0004: r16 = LoadInt32Instr(r1)
    //     0xbf0004: sbfx            x16, x1, #1, #0x1f
    // 0xbf0008: r17 = 11601
    //     0xbf0008: movz            x17, #0x2d51
    // 0xbf000c: mul             x0, x16, x17
    // 0xbf0010: umulh           x16, x16, x17
    // 0xbf0014: eor             x0, x0, x16
    // 0xbf0018: r0 = 0
    //     0xbf0018: eor             x0, x0, x0, lsr #32
    // 0xbf001c: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0020: ret
    //     0xbf0020: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76074, size: 0x9c
    // 0xd76074: EnterFrame
    //     0xd76074: stp             fp, lr, [SP, #-0x10]!
    //     0xd76078: mov             fp, SP
    // 0xd7607c: AllocStack(0x10)
    //     0xd7607c: sub             SP, SP, #0x10
    // 0xd76080: CheckStackOverflow
    //     0xd76080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76084: cmp             SP, x16
    //     0xd76088: b.ls            #0xd76108
    // 0xd7608c: ldr             x0, [fp, #0x10]
    // 0xd76090: cmp             w0, NULL
    // 0xd76094: b.ne            #0xd760a8
    // 0xd76098: r0 = false
    //     0xd76098: add             x0, NULL, #0x30  ; false
    // 0xd7609c: LeaveFrame
    //     0xd7609c: mov             SP, fp
    //     0xd760a0: ldp             fp, lr, [SP], #0x10
    // 0xd760a4: ret
    //     0xd760a4: ret             
    // 0xd760a8: ldr             x1, [fp, #0x18]
    // 0xd760ac: cmp             w1, w0
    // 0xd760b0: b.ne            #0xd760bc
    // 0xd760b4: r0 = true
    //     0xd760b4: add             x0, NULL, #0x20  ; true
    // 0xd760b8: b               #0xd760fc
    // 0xd760bc: r1 = 60
    //     0xd760bc: movz            x1, #0x3c
    // 0xd760c0: branchIfSmi(r0, 0xd760cc)
    //     0xd760c0: tbz             w0, #0, #0xd760cc
    // 0xd760c4: r1 = LoadClassIdInstr(r0)
    //     0xd760c4: ldur            x1, [x0, #-1]
    //     0xd760c8: ubfx            x1, x1, #0xc, #0x14
    // 0xd760cc: cmp             x1, #0x686
    // 0xd760d0: b.ne            #0xd760f8
    // 0xd760d4: r16 = ArticleInsertionAdapter
    //     0xd760d4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b518] Type: ArticleInsertionAdapter
    //     0xd760d8: ldr             x16, [x16, #0x518]
    // 0xd760dc: r30 = ArticleInsertionAdapter
    //     0xd760dc: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b518] Type: ArticleInsertionAdapter
    //     0xd760e0: ldr             lr, [lr, #0x518]
    // 0xd760e4: stp             lr, x16, [SP]
    // 0xd760e8: r0 = ==()
    //     0xd760e8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd760ec: tbnz            w0, #4, #0xd760f8
    // 0xd760f0: r0 = true
    //     0xd760f0: add             x0, NULL, #0x20  ; true
    // 0xd760f4: b               #0xd760fc
    // 0xd760f8: r0 = false
    //     0xd760f8: add             x0, NULL, #0x30  ; false
    // 0xd760fc: LeaveFrame
    //     0xd760fc: mov             SP, fp
    //     0xd76100: ldp             fp, lr, [SP], #0x10
    // 0xd76104: ret
    //     0xd76104: ret             
    // 0xd76108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7610c: b               #0xd7608c
  }
}

// class id: 1671, size: 0x14, field offset: 0xc
class ArticleDetailAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5e14c, size: 0xab0
    // 0xa5e14c: EnterFrame
    //     0xa5e14c: stp             fp, lr, [SP, #-0x10]!
    //     0xa5e150: mov             fp, SP
    // 0xa5e154: AllocStack(0xb0)
    //     0xa5e154: sub             SP, SP, #0xb0
    // 0xa5e158: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5e158: stur            x2, [fp, #-0x20]
    // 0xa5e15c: CheckStackOverflow
    //     0xa5e15c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5e160: cmp             SP, x16
    //     0xa5e164: b.ls            #0xa5ebe4
    // 0xa5e168: LoadField: r3 = r2->field_23
    //     0xa5e168: ldur            x3, [x2, #0x23]
    // 0xa5e16c: add             x0, x3, #1
    // 0xa5e170: LoadField: r1 = r2->field_1b
    //     0xa5e170: ldur            x1, [x2, #0x1b]
    // 0xa5e174: cmp             x0, x1
    // 0xa5e178: b.gt            #0xa5eb88
    // 0xa5e17c: LoadField: r4 = r2->field_7
    //     0xa5e17c: ldur            w4, [x2, #7]
    // 0xa5e180: DecompressPointer r4
    //     0xa5e180: add             x4, x4, HEAP, lsl #32
    // 0xa5e184: stur            x4, [fp, #-0x18]
    // 0xa5e188: StoreField: r2->field_23 = r0
    //     0xa5e188: stur            x0, [x2, #0x23]
    // 0xa5e18c: LoadField: r0 = r4->field_13
    //     0xa5e18c: ldur            w0, [x4, #0x13]
    // 0xa5e190: r5 = LoadInt32Instr(r0)
    //     0xa5e190: sbfx            x5, x0, #1, #0x1f
    // 0xa5e194: mov             x0, x5
    // 0xa5e198: mov             x1, x3
    // 0xa5e19c: stur            x5, [fp, #-0x10]
    // 0xa5e1a0: cmp             x1, x0
    // 0xa5e1a4: b.hs            #0xa5ebec
    // 0xa5e1a8: LoadField: r0 = r4->field_7
    //     0xa5e1a8: ldur            x0, [x4, #7]
    // 0xa5e1ac: ldrb            w1, [x0, x3]
    // 0xa5e1b0: stur            x1, [fp, #-8]
    // 0xa5e1b4: r16 = <int, dynamic>
    //     0xa5e1b4: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5e1b8: ldr             x16, [x16, #0xac0]
    // 0xa5e1bc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5e1c0: stp             lr, x16, [SP]
    // 0xa5e1c4: r0 = Map._fromLiteral()
    //     0xa5e1c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5e1c8: mov             x2, x0
    // 0xa5e1cc: stur            x2, [fp, #-0x38]
    // 0xa5e1d0: r6 = 0
    //     0xa5e1d0: movz            x6, #0
    // 0xa5e1d4: ldur            x3, [fp, #-0x20]
    // 0xa5e1d8: ldur            x4, [fp, #-0x18]
    // 0xa5e1dc: ldur            x5, [fp, #-8]
    // 0xa5e1e0: stur            x6, [fp, #-0x30]
    // 0xa5e1e4: CheckStackOverflow
    //     0xa5e1e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5e1e8: cmp             SP, x16
    //     0xa5e1ec: b.ls            #0xa5ebf0
    // 0xa5e1f0: cmp             x6, x5
    // 0xa5e1f4: b.ge            #0xa5e280
    // 0xa5e1f8: LoadField: r7 = r3->field_23
    //     0xa5e1f8: ldur            x7, [x3, #0x23]
    // 0xa5e1fc: add             x0, x7, #1
    // 0xa5e200: LoadField: r1 = r3->field_1b
    //     0xa5e200: ldur            x1, [x3, #0x1b]
    // 0xa5e204: cmp             x0, x1
    // 0xa5e208: b.gt            #0xa5ebb0
    // 0xa5e20c: StoreField: r3->field_23 = r0
    //     0xa5e20c: stur            x0, [x3, #0x23]
    // 0xa5e210: ldur            x0, [fp, #-0x10]
    // 0xa5e214: mov             x1, x7
    // 0xa5e218: cmp             x1, x0
    // 0xa5e21c: b.hs            #0xa5ebf8
    // 0xa5e220: LoadField: r0 = r4->field_7
    //     0xa5e220: ldur            x0, [x4, #7]
    // 0xa5e224: ldrb            w8, [x0, x7]
    // 0xa5e228: mov             x1, x3
    // 0xa5e22c: stur            x8, [fp, #-0x28]
    // 0xa5e230: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5e230: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5e234: r0 = read()
    //     0xa5e234: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5e238: mov             x1, x0
    // 0xa5e23c: ldur            x0, [fp, #-0x28]
    // 0xa5e240: lsl             x2, x0, #1
    // 0xa5e244: r16 = LoadInt32Instr(r2)
    //     0xa5e244: sbfx            x16, x2, #1, #0x1f
    // 0xa5e248: r17 = 11601
    //     0xa5e248: movz            x17, #0x2d51
    // 0xa5e24c: mul             x0, x16, x17
    // 0xa5e250: umulh           x16, x16, x17
    // 0xa5e254: eor             x0, x0, x16
    // 0xa5e258: r0 = 0
    //     0xa5e258: eor             x0, x0, x0, lsr #32
    // 0xa5e25c: ubfiz           x0, x0, #1, #0x1e
    // 0xa5e260: r5 = LoadInt32Instr(r0)
    //     0xa5e260: sbfx            x5, x0, #1, #0x1f
    // 0xa5e264: mov             x3, x1
    // 0xa5e268: ldur            x1, [fp, #-0x38]
    // 0xa5e26c: r0 = _set()
    //     0xa5e26c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5e270: ldur            x0, [fp, #-0x30]
    // 0xa5e274: add             x6, x0, #1
    // 0xa5e278: ldur            x2, [fp, #-0x38]
    // 0xa5e27c: b               #0xa5e1d4
    // 0xa5e280: mov             x0, x2
    // 0xa5e284: mov             x1, x0
    // 0xa5e288: r2 = 0
    //     0xa5e288: movz            x2, #0
    // 0xa5e28c: r0 = _getValueOrData()
    //     0xa5e28c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e290: ldur            x3, [fp, #-0x38]
    // 0xa5e294: LoadField: r1 = r3->field_f
    //     0xa5e294: ldur            w1, [x3, #0xf]
    // 0xa5e298: DecompressPointer r1
    //     0xa5e298: add             x1, x1, HEAP, lsl #32
    // 0xa5e29c: cmp             w1, w0
    // 0xa5e2a0: b.ne            #0xa5e2ac
    // 0xa5e2a4: r4 = Null
    //     0xa5e2a4: mov             x4, NULL
    // 0xa5e2a8: b               #0xa5e2b0
    // 0xa5e2ac: mov             x4, x0
    // 0xa5e2b0: mov             x0, x4
    // 0xa5e2b4: stur            x4, [fp, #-0x18]
    // 0xa5e2b8: r2 = Null
    //     0xa5e2b8: mov             x2, NULL
    // 0xa5e2bc: r1 = Null
    //     0xa5e2bc: mov             x1, NULL
    // 0xa5e2c0: branchIfSmi(r0, 0xa5e2e8)
    //     0xa5e2c0: tbz             w0, #0, #0xa5e2e8
    // 0xa5e2c4: r4 = LoadClassIdInstr(r0)
    //     0xa5e2c4: ldur            x4, [x0, #-1]
    //     0xa5e2c8: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e2cc: sub             x4, x4, #0x3c
    // 0xa5e2d0: cmp             x4, #1
    // 0xa5e2d4: b.ls            #0xa5e2e8
    // 0xa5e2d8: r8 = int
    //     0xa5e2d8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5e2dc: r3 = Null
    //     0xa5e2dc: add             x3, PP, #0x21, lsl #12  ; [pp+0x217b8] Null
    //     0xa5e2e0: ldr             x3, [x3, #0x7b8]
    // 0xa5e2e4: r0 = int()
    //     0xa5e2e4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5e2e8: ldur            x1, [fp, #-0x38]
    // 0xa5e2ec: r2 = 2
    //     0xa5e2ec: movz            x2, #0x2
    // 0xa5e2f0: r0 = _getValueOrData()
    //     0xa5e2f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e2f4: ldur            x3, [fp, #-0x38]
    // 0xa5e2f8: LoadField: r1 = r3->field_f
    //     0xa5e2f8: ldur            w1, [x3, #0xf]
    // 0xa5e2fc: DecompressPointer r1
    //     0xa5e2fc: add             x1, x1, HEAP, lsl #32
    // 0xa5e300: cmp             w1, w0
    // 0xa5e304: b.ne            #0xa5e310
    // 0xa5e308: r4 = Null
    //     0xa5e308: mov             x4, NULL
    // 0xa5e30c: b               #0xa5e314
    // 0xa5e310: mov             x4, x0
    // 0xa5e314: mov             x0, x4
    // 0xa5e318: stur            x4, [fp, #-0x20]
    // 0xa5e31c: r2 = Null
    //     0xa5e31c: mov             x2, NULL
    // 0xa5e320: r1 = Null
    //     0xa5e320: mov             x1, NULL
    // 0xa5e324: r4 = 60
    //     0xa5e324: movz            x4, #0x3c
    // 0xa5e328: branchIfSmi(r0, 0xa5e334)
    //     0xa5e328: tbz             w0, #0, #0xa5e334
    // 0xa5e32c: r4 = LoadClassIdInstr(r0)
    //     0xa5e32c: ldur            x4, [x0, #-1]
    //     0xa5e330: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e334: sub             x4, x4, #0x5e
    // 0xa5e338: cmp             x4, #1
    // 0xa5e33c: b.ls            #0xa5e350
    // 0xa5e340: r8 = String
    //     0xa5e340: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5e344: r3 = Null
    //     0xa5e344: add             x3, PP, #0x21, lsl #12  ; [pp+0x217c8] Null
    //     0xa5e348: ldr             x3, [x3, #0x7c8]
    // 0xa5e34c: r0 = String()
    //     0xa5e34c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5e350: ldur            x1, [fp, #-0x38]
    // 0xa5e354: r2 = 6
    //     0xa5e354: movz            x2, #0x6
    // 0xa5e358: r0 = _getValueOrData()
    //     0xa5e358: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e35c: ldur            x3, [fp, #-0x38]
    // 0xa5e360: LoadField: r1 = r3->field_f
    //     0xa5e360: ldur            w1, [x3, #0xf]
    // 0xa5e364: DecompressPointer r1
    //     0xa5e364: add             x1, x1, HEAP, lsl #32
    // 0xa5e368: cmp             w1, w0
    // 0xa5e36c: b.ne            #0xa5e378
    // 0xa5e370: r4 = Null
    //     0xa5e370: mov             x4, NULL
    // 0xa5e374: b               #0xa5e37c
    // 0xa5e378: mov             x4, x0
    // 0xa5e37c: mov             x0, x4
    // 0xa5e380: stur            x4, [fp, #-0x40]
    // 0xa5e384: r2 = Null
    //     0xa5e384: mov             x2, NULL
    // 0xa5e388: r1 = Null
    //     0xa5e388: mov             x1, NULL
    // 0xa5e38c: r4 = 60
    //     0xa5e38c: movz            x4, #0x3c
    // 0xa5e390: branchIfSmi(r0, 0xa5e39c)
    //     0xa5e390: tbz             w0, #0, #0xa5e39c
    // 0xa5e394: r4 = LoadClassIdInstr(r0)
    //     0xa5e394: ldur            x4, [x0, #-1]
    //     0xa5e398: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e39c: r17 = 5582
    //     0xa5e39c: movz            x17, #0x15ce
    // 0xa5e3a0: cmp             x4, x17
    // 0xa5e3a4: b.eq            #0xa5e3bc
    // 0xa5e3a8: r8 = Image
    //     0xa5e3a8: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b560] Type: Image
    //     0xa5e3ac: ldr             x8, [x8, #0x560]
    // 0xa5e3b0: r3 = Null
    //     0xa5e3b0: add             x3, PP, #0x21, lsl #12  ; [pp+0x217d8] Null
    //     0xa5e3b4: ldr             x3, [x3, #0x7d8]
    // 0xa5e3b8: r0 = Image()
    //     0xa5e3b8: bl              #0x72c934  ; IsType_Image_Stub
    // 0xa5e3bc: ldur            x1, [fp, #-0x38]
    // 0xa5e3c0: r2 = 8
    //     0xa5e3c0: movz            x2, #0x8
    // 0xa5e3c4: r0 = _getValueOrData()
    //     0xa5e3c4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e3c8: ldur            x3, [fp, #-0x38]
    // 0xa5e3cc: LoadField: r1 = r3->field_f
    //     0xa5e3cc: ldur            w1, [x3, #0xf]
    // 0xa5e3d0: DecompressPointer r1
    //     0xa5e3d0: add             x1, x1, HEAP, lsl #32
    // 0xa5e3d4: cmp             w1, w0
    // 0xa5e3d8: b.ne            #0xa5e3e4
    // 0xa5e3dc: r4 = Null
    //     0xa5e3dc: mov             x4, NULL
    // 0xa5e3e0: b               #0xa5e3e8
    // 0xa5e3e4: mov             x4, x0
    // 0xa5e3e8: mov             x0, x4
    // 0xa5e3ec: stur            x4, [fp, #-0x48]
    // 0xa5e3f0: r2 = Null
    //     0xa5e3f0: mov             x2, NULL
    // 0xa5e3f4: r1 = Null
    //     0xa5e3f4: mov             x1, NULL
    // 0xa5e3f8: r4 = 60
    //     0xa5e3f8: movz            x4, #0x3c
    // 0xa5e3fc: branchIfSmi(r0, 0xa5e408)
    //     0xa5e3fc: tbz             w0, #0, #0xa5e408
    // 0xa5e400: r4 = LoadClassIdInstr(r0)
    //     0xa5e400: ldur            x4, [x0, #-1]
    //     0xa5e404: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e408: cmp             x4, #0x649
    // 0xa5e40c: b.eq            #0xa5e424
    // 0xa5e410: r8 = Category
    //     0xa5e410: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b498] Type: Category
    //     0xa5e414: ldr             x8, [x8, #0x498]
    // 0xa5e418: r3 = Null
    //     0xa5e418: add             x3, PP, #0x21, lsl #12  ; [pp+0x217e8] Null
    //     0xa5e41c: ldr             x3, [x3, #0x7e8]
    // 0xa5e420: r0 = Category()
    //     0xa5e420: bl              #0x80d594  ; IsType_Category_Stub
    // 0xa5e424: ldur            x1, [fp, #-0x38]
    // 0xa5e428: r2 = 10
    //     0xa5e428: movz            x2, #0xa
    // 0xa5e42c: r0 = _getValueOrData()
    //     0xa5e42c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e430: ldur            x3, [fp, #-0x38]
    // 0xa5e434: LoadField: r1 = r3->field_f
    //     0xa5e434: ldur            w1, [x3, #0xf]
    // 0xa5e438: DecompressPointer r1
    //     0xa5e438: add             x1, x1, HEAP, lsl #32
    // 0xa5e43c: cmp             w1, w0
    // 0xa5e440: b.ne            #0xa5e44c
    // 0xa5e444: r4 = Null
    //     0xa5e444: mov             x4, NULL
    // 0xa5e448: b               #0xa5e450
    // 0xa5e44c: mov             x4, x0
    // 0xa5e450: mov             x0, x4
    // 0xa5e454: stur            x4, [fp, #-0x50]
    // 0xa5e458: r2 = Null
    //     0xa5e458: mov             x2, NULL
    // 0xa5e45c: r1 = Null
    //     0xa5e45c: mov             x1, NULL
    // 0xa5e460: r4 = 60
    //     0xa5e460: movz            x4, #0x3c
    // 0xa5e464: branchIfSmi(r0, 0xa5e470)
    //     0xa5e464: tbz             w0, #0, #0xa5e470
    // 0xa5e468: r4 = LoadClassIdInstr(r0)
    //     0xa5e468: ldur            x4, [x0, #-1]
    //     0xa5e46c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e470: sub             x4, x4, #0x5e
    // 0xa5e474: cmp             x4, #1
    // 0xa5e478: b.ls            #0xa5e48c
    // 0xa5e47c: r8 = String
    //     0xa5e47c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5e480: r3 = Null
    //     0xa5e480: add             x3, PP, #0x21, lsl #12  ; [pp+0x217f8] Null
    //     0xa5e484: ldr             x3, [x3, #0x7f8]
    // 0xa5e488: r0 = String()
    //     0xa5e488: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5e48c: ldur            x1, [fp, #-0x38]
    // 0xa5e490: r2 = 12
    //     0xa5e490: movz            x2, #0xc
    // 0xa5e494: r0 = _getValueOrData()
    //     0xa5e494: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e498: ldur            x3, [fp, #-0x38]
    // 0xa5e49c: LoadField: r1 = r3->field_f
    //     0xa5e49c: ldur            w1, [x3, #0xf]
    // 0xa5e4a0: DecompressPointer r1
    //     0xa5e4a0: add             x1, x1, HEAP, lsl #32
    // 0xa5e4a4: cmp             w1, w0
    // 0xa5e4a8: b.ne            #0xa5e4b4
    // 0xa5e4ac: r4 = Null
    //     0xa5e4ac: mov             x4, NULL
    // 0xa5e4b0: b               #0xa5e4b8
    // 0xa5e4b4: mov             x4, x0
    // 0xa5e4b8: mov             x0, x4
    // 0xa5e4bc: stur            x4, [fp, #-0x58]
    // 0xa5e4c0: r2 = Null
    //     0xa5e4c0: mov             x2, NULL
    // 0xa5e4c4: r1 = Null
    //     0xa5e4c4: mov             x1, NULL
    // 0xa5e4c8: r4 = 60
    //     0xa5e4c8: movz            x4, #0x3c
    // 0xa5e4cc: branchIfSmi(r0, 0xa5e4d8)
    //     0xa5e4cc: tbz             w0, #0, #0xa5e4d8
    // 0xa5e4d0: r4 = LoadClassIdInstr(r0)
    //     0xa5e4d0: ldur            x4, [x0, #-1]
    //     0xa5e4d4: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e4d8: sub             x4, x4, #0x5e
    // 0xa5e4dc: cmp             x4, #1
    // 0xa5e4e0: b.ls            #0xa5e4f4
    // 0xa5e4e4: r8 = String
    //     0xa5e4e4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5e4e8: r3 = Null
    //     0xa5e4e8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21808] Null
    //     0xa5e4ec: ldr             x3, [x3, #0x808]
    // 0xa5e4f0: r0 = String()
    //     0xa5e4f0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5e4f4: ldur            x1, [fp, #-0x38]
    // 0xa5e4f8: r2 = 16
    //     0xa5e4f8: movz            x2, #0x10
    // 0xa5e4fc: r0 = _getValueOrData()
    //     0xa5e4fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e500: ldur            x3, [fp, #-0x38]
    // 0xa5e504: LoadField: r1 = r3->field_f
    //     0xa5e504: ldur            w1, [x3, #0xf]
    // 0xa5e508: DecompressPointer r1
    //     0xa5e508: add             x1, x1, HEAP, lsl #32
    // 0xa5e50c: cmp             w1, w0
    // 0xa5e510: b.ne            #0xa5e51c
    // 0xa5e514: r4 = Null
    //     0xa5e514: mov             x4, NULL
    // 0xa5e518: b               #0xa5e520
    // 0xa5e51c: mov             x4, x0
    // 0xa5e520: mov             x0, x4
    // 0xa5e524: stur            x4, [fp, #-0x60]
    // 0xa5e528: r2 = Null
    //     0xa5e528: mov             x2, NULL
    // 0xa5e52c: r1 = Null
    //     0xa5e52c: mov             x1, NULL
    // 0xa5e530: r4 = 60
    //     0xa5e530: movz            x4, #0x3c
    // 0xa5e534: branchIfSmi(r0, 0xa5e540)
    //     0xa5e534: tbz             w0, #0, #0xa5e540
    // 0xa5e538: r4 = LoadClassIdInstr(r0)
    //     0xa5e538: ldur            x4, [x0, #-1]
    //     0xa5e53c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e540: sub             x4, x4, #0x5e
    // 0xa5e544: cmp             x4, #1
    // 0xa5e548: b.ls            #0xa5e55c
    // 0xa5e54c: r8 = String
    //     0xa5e54c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5e550: r3 = Null
    //     0xa5e550: add             x3, PP, #0x21, lsl #12  ; [pp+0x21818] Null
    //     0xa5e554: ldr             x3, [x3, #0x818]
    // 0xa5e558: r0 = String()
    //     0xa5e558: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5e55c: ldur            x1, [fp, #-0x38]
    // 0xa5e560: r2 = 4
    //     0xa5e560: movz            x2, #0x4
    // 0xa5e564: r0 = _getValueOrData()
    //     0xa5e564: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e568: ldur            x3, [fp, #-0x38]
    // 0xa5e56c: LoadField: r1 = r3->field_f
    //     0xa5e56c: ldur            w1, [x3, #0xf]
    // 0xa5e570: DecompressPointer r1
    //     0xa5e570: add             x1, x1, HEAP, lsl #32
    // 0xa5e574: cmp             w1, w0
    // 0xa5e578: b.ne            #0xa5e584
    // 0xa5e57c: r4 = Null
    //     0xa5e57c: mov             x4, NULL
    // 0xa5e580: b               #0xa5e588
    // 0xa5e584: mov             x4, x0
    // 0xa5e588: mov             x0, x4
    // 0xa5e58c: stur            x4, [fp, #-0x68]
    // 0xa5e590: r2 = Null
    //     0xa5e590: mov             x2, NULL
    // 0xa5e594: r1 = Null
    //     0xa5e594: mov             x1, NULL
    // 0xa5e598: r4 = 60
    //     0xa5e598: movz            x4, #0x3c
    // 0xa5e59c: branchIfSmi(r0, 0xa5e5a8)
    //     0xa5e59c: tbz             w0, #0, #0xa5e5a8
    // 0xa5e5a0: r4 = LoadClassIdInstr(r0)
    //     0xa5e5a0: ldur            x4, [x0, #-1]
    //     0xa5e5a4: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e5a8: sub             x4, x4, #0x5e
    // 0xa5e5ac: cmp             x4, #1
    // 0xa5e5b0: b.ls            #0xa5e5c4
    // 0xa5e5b4: r8 = String?
    //     0xa5e5b4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5e5b8: r3 = Null
    //     0xa5e5b8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21828] Null
    //     0xa5e5bc: ldr             x3, [x3, #0x828]
    // 0xa5e5c0: r0 = String?()
    //     0xa5e5c0: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5e5c4: ldur            x1, [fp, #-0x38]
    // 0xa5e5c8: r2 = 20
    //     0xa5e5c8: movz            x2, #0x14
    // 0xa5e5cc: r0 = _getValueOrData()
    //     0xa5e5cc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e5d0: ldur            x3, [fp, #-0x38]
    // 0xa5e5d4: LoadField: r1 = r3->field_f
    //     0xa5e5d4: ldur            w1, [x3, #0xf]
    // 0xa5e5d8: DecompressPointer r1
    //     0xa5e5d8: add             x1, x1, HEAP, lsl #32
    // 0xa5e5dc: cmp             w1, w0
    // 0xa5e5e0: b.ne            #0xa5e5ec
    // 0xa5e5e4: r4 = Null
    //     0xa5e5e4: mov             x4, NULL
    // 0xa5e5e8: b               #0xa5e5f0
    // 0xa5e5ec: mov             x4, x0
    // 0xa5e5f0: mov             x0, x4
    // 0xa5e5f4: stur            x4, [fp, #-0x70]
    // 0xa5e5f8: r2 = Null
    //     0xa5e5f8: mov             x2, NULL
    // 0xa5e5fc: r1 = Null
    //     0xa5e5fc: mov             x1, NULL
    // 0xa5e600: r4 = 60
    //     0xa5e600: movz            x4, #0x3c
    // 0xa5e604: branchIfSmi(r0, 0xa5e610)
    //     0xa5e604: tbz             w0, #0, #0xa5e610
    // 0xa5e608: r4 = LoadClassIdInstr(r0)
    //     0xa5e608: ldur            x4, [x0, #-1]
    //     0xa5e60c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e610: r17 = 5596
    //     0xa5e610: movz            x17, #0x15dc
    // 0xa5e614: cmp             x4, x17
    // 0xa5e618: b.eq            #0xa5e630
    // 0xa5e61c: r8 = Author?
    //     0xa5e61c: add             x8, PP, #0x21, lsl #12  ; [pp+0x21838] Type: Author?
    //     0xa5e620: ldr             x8, [x8, #0x838]
    // 0xa5e624: r3 = Null
    //     0xa5e624: add             x3, PP, #0x21, lsl #12  ; [pp+0x21840] Null
    //     0xa5e628: ldr             x3, [x3, #0x840]
    // 0xa5e62c: r0 = Author?()
    //     0xa5e62c: bl              #0xa36e2c  ; IsType_Author?_Stub
    // 0xa5e630: ldur            x1, [fp, #-0x38]
    // 0xa5e634: r2 = 14
    //     0xa5e634: movz            x2, #0xe
    // 0xa5e638: r0 = _getValueOrData()
    //     0xa5e638: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e63c: ldur            x3, [fp, #-0x38]
    // 0xa5e640: LoadField: r1 = r3->field_f
    //     0xa5e640: ldur            w1, [x3, #0xf]
    // 0xa5e644: DecompressPointer r1
    //     0xa5e644: add             x1, x1, HEAP, lsl #32
    // 0xa5e648: cmp             w1, w0
    // 0xa5e64c: b.ne            #0xa5e658
    // 0xa5e650: r4 = Null
    //     0xa5e650: mov             x4, NULL
    // 0xa5e654: b               #0xa5e65c
    // 0xa5e658: mov             x4, x0
    // 0xa5e65c: mov             x0, x4
    // 0xa5e660: stur            x4, [fp, #-0x78]
    // 0xa5e664: r2 = Null
    //     0xa5e664: mov             x2, NULL
    // 0xa5e668: r1 = Null
    //     0xa5e668: mov             x1, NULL
    // 0xa5e66c: r4 = 60
    //     0xa5e66c: movz            x4, #0x3c
    // 0xa5e670: branchIfSmi(r0, 0xa5e67c)
    //     0xa5e670: tbz             w0, #0, #0xa5e67c
    // 0xa5e674: r4 = LoadClassIdInstr(r0)
    //     0xa5e674: ldur            x4, [x0, #-1]
    //     0xa5e678: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e67c: sub             x4, x4, #0x5a
    // 0xa5e680: cmp             x4, #2
    // 0xa5e684: b.ls            #0xa5e698
    // 0xa5e688: r8 = List
    //     0xa5e688: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xa5e68c: r3 = Null
    //     0xa5e68c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21850] Null
    //     0xa5e690: ldr             x3, [x3, #0x850]
    // 0xa5e694: r0 = List()
    //     0xa5e694: bl              #0xed6b40  ; IsType_List_Stub
    // 0xa5e698: ldur            x0, [fp, #-0x78]
    // 0xa5e69c: r1 = LoadClassIdInstr(r0)
    //     0xa5e69c: ldur            x1, [x0, #-1]
    //     0xa5e6a0: ubfx            x1, x1, #0xc, #0x14
    // 0xa5e6a4: r16 = <Tag>
    //     0xa5e6a4: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0xa5e6a8: stp             x0, x16, [SP]
    // 0xa5e6ac: mov             x0, x1
    // 0xa5e6b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5e6b0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa5e6b4: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa5e6b4: movz            x17, #0xf30c
    //     0xa5e6b8: add             lr, x0, x17
    //     0xa5e6bc: ldr             lr, [x21, lr, lsl #3]
    //     0xa5e6c0: blr             lr
    // 0xa5e6c4: ldur            x1, [fp, #-0x38]
    // 0xa5e6c8: r2 = 18
    //     0xa5e6c8: movz            x2, #0x12
    // 0xa5e6cc: stur            x0, [fp, #-0x78]
    // 0xa5e6d0: r0 = _getValueOrData()
    //     0xa5e6d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e6d4: ldur            x3, [fp, #-0x38]
    // 0xa5e6d8: LoadField: r1 = r3->field_f
    //     0xa5e6d8: ldur            w1, [x3, #0xf]
    // 0xa5e6dc: DecompressPointer r1
    //     0xa5e6dc: add             x1, x1, HEAP, lsl #32
    // 0xa5e6e0: cmp             w1, w0
    // 0xa5e6e4: b.ne            #0xa5e6f0
    // 0xa5e6e8: r4 = Null
    //     0xa5e6e8: mov             x4, NULL
    // 0xa5e6ec: b               #0xa5e6f4
    // 0xa5e6f0: mov             x4, x0
    // 0xa5e6f4: mov             x0, x4
    // 0xa5e6f8: stur            x4, [fp, #-0x80]
    // 0xa5e6fc: r2 = Null
    //     0xa5e6fc: mov             x2, NULL
    // 0xa5e700: r1 = Null
    //     0xa5e700: mov             x1, NULL
    // 0xa5e704: r4 = 60
    //     0xa5e704: movz            x4, #0x3c
    // 0xa5e708: branchIfSmi(r0, 0xa5e714)
    //     0xa5e708: tbz             w0, #0, #0xa5e714
    // 0xa5e70c: r4 = LoadClassIdInstr(r0)
    //     0xa5e70c: ldur            x4, [x0, #-1]
    //     0xa5e710: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e714: sub             x4, x4, #0x5a
    // 0xa5e718: cmp             x4, #2
    // 0xa5e71c: b.ls            #0xa5e730
    // 0xa5e720: r8 = List
    //     0xa5e720: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xa5e724: r3 = Null
    //     0xa5e724: add             x3, PP, #0x21, lsl #12  ; [pp+0x21860] Null
    //     0xa5e728: ldr             x3, [x3, #0x860]
    // 0xa5e72c: r0 = List()
    //     0xa5e72c: bl              #0xed6b40  ; IsType_List_Stub
    // 0xa5e730: ldur            x0, [fp, #-0x80]
    // 0xa5e734: r1 = LoadClassIdInstr(r0)
    //     0xa5e734: ldur            x1, [x0, #-1]
    //     0xa5e738: ubfx            x1, x1, #0xc, #0x14
    // 0xa5e73c: r16 = <Article>
    //     0xa5e73c: ldr             x16, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0xa5e740: stp             x0, x16, [SP]
    // 0xa5e744: mov             x0, x1
    // 0xa5e748: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5e748: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa5e74c: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa5e74c: movz            x17, #0xf30c
    //     0xa5e750: add             lr, x0, x17
    //     0xa5e754: ldr             lr, [x21, lr, lsl #3]
    //     0xa5e758: blr             lr
    // 0xa5e75c: ldur            x1, [fp, #-0x38]
    // 0xa5e760: r2 = 22
    //     0xa5e760: movz            x2, #0x16
    // 0xa5e764: stur            x0, [fp, #-0x80]
    // 0xa5e768: r0 = _getValueOrData()
    //     0xa5e768: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e76c: ldur            x3, [fp, #-0x38]
    // 0xa5e770: LoadField: r1 = r3->field_f
    //     0xa5e770: ldur            w1, [x3, #0xf]
    // 0xa5e774: DecompressPointer r1
    //     0xa5e774: add             x1, x1, HEAP, lsl #32
    // 0xa5e778: cmp             w1, w0
    // 0xa5e77c: b.ne            #0xa5e788
    // 0xa5e780: r4 = Null
    //     0xa5e780: mov             x4, NULL
    // 0xa5e784: b               #0xa5e78c
    // 0xa5e788: mov             x4, x0
    // 0xa5e78c: mov             x0, x4
    // 0xa5e790: stur            x4, [fp, #-0x88]
    // 0xa5e794: r2 = Null
    //     0xa5e794: mov             x2, NULL
    // 0xa5e798: r1 = Null
    //     0xa5e798: mov             x1, NULL
    // 0xa5e79c: r4 = 60
    //     0xa5e79c: movz            x4, #0x3c
    // 0xa5e7a0: branchIfSmi(r0, 0xa5e7ac)
    //     0xa5e7a0: tbz             w0, #0, #0xa5e7ac
    // 0xa5e7a4: r4 = LoadClassIdInstr(r0)
    //     0xa5e7a4: ldur            x4, [x0, #-1]
    //     0xa5e7a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e7ac: sub             x4, x4, #0x5e
    // 0xa5e7b0: cmp             x4, #1
    // 0xa5e7b4: b.ls            #0xa5e7c8
    // 0xa5e7b8: r8 = String?
    //     0xa5e7b8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5e7bc: r3 = Null
    //     0xa5e7bc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21870] Null
    //     0xa5e7c0: ldr             x3, [x3, #0x870]
    // 0xa5e7c4: r0 = String?()
    //     0xa5e7c4: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5e7c8: ldur            x1, [fp, #-0x38]
    // 0xa5e7cc: r2 = 24
    //     0xa5e7cc: movz            x2, #0x18
    // 0xa5e7d0: r0 = _getValueOrData()
    //     0xa5e7d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e7d4: mov             x1, x0
    // 0xa5e7d8: ldur            x0, [fp, #-0x38]
    // 0xa5e7dc: LoadField: r2 = r0->field_f
    //     0xa5e7dc: ldur            w2, [x0, #0xf]
    // 0xa5e7e0: DecompressPointer r2
    //     0xa5e7e0: add             x2, x2, HEAP, lsl #32
    // 0xa5e7e4: cmp             w2, w1
    // 0xa5e7e8: b.eq            #0xa5e7f4
    // 0xa5e7ec: cmp             w1, NULL
    // 0xa5e7f0: b.ne            #0xa5e808
    // 0xa5e7f4: r1 = <ArticleInsertion>
    //     0xa5e7f4: ldr             x1, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0xa5e7f8: r2 = 0
    //     0xa5e7f8: movz            x2, #0
    // 0xa5e7fc: r0 = _GrowableList()
    //     0xa5e7fc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa5e800: mov             x3, x0
    // 0xa5e804: b               #0xa5e8a0
    // 0xa5e808: mov             x1, x0
    // 0xa5e80c: r2 = 24
    //     0xa5e80c: movz            x2, #0x18
    // 0xa5e810: r0 = _getValueOrData()
    //     0xa5e810: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e814: ldur            x3, [fp, #-0x38]
    // 0xa5e818: LoadField: r1 = r3->field_f
    //     0xa5e818: ldur            w1, [x3, #0xf]
    // 0xa5e81c: DecompressPointer r1
    //     0xa5e81c: add             x1, x1, HEAP, lsl #32
    // 0xa5e820: cmp             w1, w0
    // 0xa5e824: b.ne            #0xa5e830
    // 0xa5e828: r4 = Null
    //     0xa5e828: mov             x4, NULL
    // 0xa5e82c: b               #0xa5e834
    // 0xa5e830: mov             x4, x0
    // 0xa5e834: mov             x0, x4
    // 0xa5e838: stur            x4, [fp, #-0x90]
    // 0xa5e83c: r2 = Null
    //     0xa5e83c: mov             x2, NULL
    // 0xa5e840: r1 = Null
    //     0xa5e840: mov             x1, NULL
    // 0xa5e844: r4 = 60
    //     0xa5e844: movz            x4, #0x3c
    // 0xa5e848: branchIfSmi(r0, 0xa5e854)
    //     0xa5e848: tbz             w0, #0, #0xa5e854
    // 0xa5e84c: r4 = LoadClassIdInstr(r0)
    //     0xa5e84c: ldur            x4, [x0, #-1]
    //     0xa5e850: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e854: sub             x4, x4, #0x5a
    // 0xa5e858: cmp             x4, #2
    // 0xa5e85c: b.ls            #0xa5e870
    // 0xa5e860: r8 = List
    //     0xa5e860: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xa5e864: r3 = Null
    //     0xa5e864: add             x3, PP, #0x21, lsl #12  ; [pp+0x21880] Null
    //     0xa5e868: ldr             x3, [x3, #0x880]
    // 0xa5e86c: r0 = List()
    //     0xa5e86c: bl              #0xed6b40  ; IsType_List_Stub
    // 0xa5e870: ldur            x0, [fp, #-0x90]
    // 0xa5e874: r1 = LoadClassIdInstr(r0)
    //     0xa5e874: ldur            x1, [x0, #-1]
    //     0xa5e878: ubfx            x1, x1, #0xc, #0x14
    // 0xa5e87c: r16 = <ArticleInsertion>
    //     0xa5e87c: ldr             x16, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0xa5e880: stp             x0, x16, [SP]
    // 0xa5e884: mov             x0, x1
    // 0xa5e888: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5e888: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa5e88c: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa5e88c: movz            x17, #0xf30c
    //     0xa5e890: add             lr, x0, x17
    //     0xa5e894: ldr             lr, [x21, lr, lsl #3]
    //     0xa5e898: blr             lr
    // 0xa5e89c: mov             x3, x0
    // 0xa5e8a0: ldur            x0, [fp, #-0x38]
    // 0xa5e8a4: mov             x1, x0
    // 0xa5e8a8: stur            x3, [fp, #-0x90]
    // 0xa5e8ac: r2 = 26
    //     0xa5e8ac: movz            x2, #0x1a
    // 0xa5e8b0: r0 = _getValueOrData()
    //     0xa5e8b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e8b4: mov             x1, x0
    // 0xa5e8b8: ldur            x0, [fp, #-0x38]
    // 0xa5e8bc: LoadField: r2 = r0->field_f
    //     0xa5e8bc: ldur            w2, [x0, #0xf]
    // 0xa5e8c0: DecompressPointer r2
    //     0xa5e8c0: add             x2, x2, HEAP, lsl #32
    // 0xa5e8c4: cmp             w2, w1
    // 0xa5e8c8: b.eq            #0xa5e8d4
    // 0xa5e8cc: cmp             w1, NULL
    // 0xa5e8d0: b.ne            #0xa5e8e8
    // 0xa5e8d4: r1 = <ArticleInsertion>
    //     0xa5e8d4: ldr             x1, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0xa5e8d8: r2 = 0
    //     0xa5e8d8: movz            x2, #0
    // 0xa5e8dc: r0 = _GrowableList()
    //     0xa5e8dc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa5e8e0: mov             x3, x0
    // 0xa5e8e4: b               #0xa5e980
    // 0xa5e8e8: mov             x1, x0
    // 0xa5e8ec: r2 = 26
    //     0xa5e8ec: movz            x2, #0x1a
    // 0xa5e8f0: r0 = _getValueOrData()
    //     0xa5e8f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e8f4: ldur            x3, [fp, #-0x38]
    // 0xa5e8f8: LoadField: r1 = r3->field_f
    //     0xa5e8f8: ldur            w1, [x3, #0xf]
    // 0xa5e8fc: DecompressPointer r1
    //     0xa5e8fc: add             x1, x1, HEAP, lsl #32
    // 0xa5e900: cmp             w1, w0
    // 0xa5e904: b.ne            #0xa5e910
    // 0xa5e908: r4 = Null
    //     0xa5e908: mov             x4, NULL
    // 0xa5e90c: b               #0xa5e914
    // 0xa5e910: mov             x4, x0
    // 0xa5e914: mov             x0, x4
    // 0xa5e918: stur            x4, [fp, #-0x98]
    // 0xa5e91c: r2 = Null
    //     0xa5e91c: mov             x2, NULL
    // 0xa5e920: r1 = Null
    //     0xa5e920: mov             x1, NULL
    // 0xa5e924: r4 = 60
    //     0xa5e924: movz            x4, #0x3c
    // 0xa5e928: branchIfSmi(r0, 0xa5e934)
    //     0xa5e928: tbz             w0, #0, #0xa5e934
    // 0xa5e92c: r4 = LoadClassIdInstr(r0)
    //     0xa5e92c: ldur            x4, [x0, #-1]
    //     0xa5e930: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e934: sub             x4, x4, #0x5a
    // 0xa5e938: cmp             x4, #2
    // 0xa5e93c: b.ls            #0xa5e950
    // 0xa5e940: r8 = List
    //     0xa5e940: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xa5e944: r3 = Null
    //     0xa5e944: add             x3, PP, #0x21, lsl #12  ; [pp+0x21890] Null
    //     0xa5e948: ldr             x3, [x3, #0x890]
    // 0xa5e94c: r0 = List()
    //     0xa5e94c: bl              #0xed6b40  ; IsType_List_Stub
    // 0xa5e950: ldur            x0, [fp, #-0x98]
    // 0xa5e954: r1 = LoadClassIdInstr(r0)
    //     0xa5e954: ldur            x1, [x0, #-1]
    //     0xa5e958: ubfx            x1, x1, #0xc, #0x14
    // 0xa5e95c: r16 = <ArticleInsertion>
    //     0xa5e95c: ldr             x16, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0xa5e960: stp             x0, x16, [SP]
    // 0xa5e964: mov             x0, x1
    // 0xa5e968: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5e968: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa5e96c: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa5e96c: movz            x17, #0xf30c
    //     0xa5e970: add             lr, x0, x17
    //     0xa5e974: ldr             lr, [x21, lr, lsl #3]
    //     0xa5e978: blr             lr
    // 0xa5e97c: mov             x3, x0
    // 0xa5e980: ldur            x0, [fp, #-0x38]
    // 0xa5e984: mov             x1, x0
    // 0xa5e988: stur            x3, [fp, #-0x98]
    // 0xa5e98c: r2 = 28
    //     0xa5e98c: movz            x2, #0x1c
    // 0xa5e990: r0 = _getValueOrData()
    //     0xa5e990: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e994: ldur            x3, [fp, #-0x38]
    // 0xa5e998: LoadField: r1 = r3->field_f
    //     0xa5e998: ldur            w1, [x3, #0xf]
    // 0xa5e99c: DecompressPointer r1
    //     0xa5e99c: add             x1, x1, HEAP, lsl #32
    // 0xa5e9a0: cmp             w1, w0
    // 0xa5e9a4: b.ne            #0xa5e9b0
    // 0xa5e9a8: r4 = Null
    //     0xa5e9a8: mov             x4, NULL
    // 0xa5e9ac: b               #0xa5e9b4
    // 0xa5e9b0: mov             x4, x0
    // 0xa5e9b4: mov             x0, x4
    // 0xa5e9b8: stur            x4, [fp, #-0xa0]
    // 0xa5e9bc: r2 = Null
    //     0xa5e9bc: mov             x2, NULL
    // 0xa5e9c0: r1 = Null
    //     0xa5e9c0: mov             x1, NULL
    // 0xa5e9c4: r4 = 60
    //     0xa5e9c4: movz            x4, #0x3c
    // 0xa5e9c8: branchIfSmi(r0, 0xa5e9d4)
    //     0xa5e9c8: tbz             w0, #0, #0xa5e9d4
    // 0xa5e9cc: r4 = LoadClassIdInstr(r0)
    //     0xa5e9cc: ldur            x4, [x0, #-1]
    //     0xa5e9d0: ubfx            x4, x4, #0xc, #0x14
    // 0xa5e9d4: sub             x4, x4, #0x5e
    // 0xa5e9d8: cmp             x4, #1
    // 0xa5e9dc: b.ls            #0xa5e9f0
    // 0xa5e9e0: r8 = String?
    //     0xa5e9e0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5e9e4: r3 = Null
    //     0xa5e9e4: add             x3, PP, #0x21, lsl #12  ; [pp+0x218a0] Null
    //     0xa5e9e8: ldr             x3, [x3, #0x8a0]
    // 0xa5e9ec: r0 = String?()
    //     0xa5e9ec: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5e9f0: ldur            x1, [fp, #-0x38]
    // 0xa5e9f4: r2 = 30
    //     0xa5e9f4: movz            x2, #0x1e
    // 0xa5e9f8: r0 = _getValueOrData()
    //     0xa5e9f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5e9fc: mov             x1, x0
    // 0xa5ea00: ldur            x0, [fp, #-0x38]
    // 0xa5ea04: LoadField: r2 = r0->field_f
    //     0xa5ea04: ldur            w2, [x0, #0xf]
    // 0xa5ea08: DecompressPointer r2
    //     0xa5ea08: add             x2, x2, HEAP, lsl #32
    // 0xa5ea0c: cmp             w2, w1
    // 0xa5ea10: b.ne            #0xa5ea1c
    // 0xa5ea14: r3 = Null
    //     0xa5ea14: mov             x3, NULL
    // 0xa5ea18: b               #0xa5ea20
    // 0xa5ea1c: mov             x3, x1
    // 0xa5ea20: mov             x0, x3
    // 0xa5ea24: stur            x3, [fp, #-0x38]
    // 0xa5ea28: r2 = Null
    //     0xa5ea28: mov             x2, NULL
    // 0xa5ea2c: r1 = Null
    //     0xa5ea2c: mov             x1, NULL
    // 0xa5ea30: r4 = 60
    //     0xa5ea30: movz            x4, #0x3c
    // 0xa5ea34: branchIfSmi(r0, 0xa5ea40)
    //     0xa5ea34: tbz             w0, #0, #0xa5ea40
    // 0xa5ea38: r4 = LoadClassIdInstr(r0)
    //     0xa5ea38: ldur            x4, [x0, #-1]
    //     0xa5ea3c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ea40: sub             x4, x4, #0x5a
    // 0xa5ea44: cmp             x4, #2
    // 0xa5ea48: b.ls            #0xa5ea60
    // 0xa5ea4c: r8 = List?
    //     0xa5ea4c: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xa5ea50: ldr             x8, [x8, #0x140]
    // 0xa5ea54: r3 = Null
    //     0xa5ea54: add             x3, PP, #0x21, lsl #12  ; [pp+0x218b0] Null
    //     0xa5ea58: ldr             x3, [x3, #0x8b0]
    // 0xa5ea5c: r0 = List?()
    //     0xa5ea5c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xa5ea60: ldur            x0, [fp, #-0x38]
    // 0xa5ea64: cmp             w0, NULL
    // 0xa5ea68: b.ne            #0xa5ea74
    // 0xa5ea6c: r19 = Null
    //     0xa5ea6c: mov             x19, NULL
    // 0xa5ea70: b               #0xa5eaa0
    // 0xa5ea74: r1 = LoadClassIdInstr(r0)
    //     0xa5ea74: ldur            x1, [x0, #-1]
    //     0xa5ea78: ubfx            x1, x1, #0xc, #0x14
    // 0xa5ea7c: r16 = <Author>
    //     0xa5ea7c: ldr             x16, [PP, #0x7b70]  ; [pp+0x7b70] TypeArguments: <Author>
    // 0xa5ea80: stp             x0, x16, [SP]
    // 0xa5ea84: mov             x0, x1
    // 0xa5ea88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5ea88: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa5ea8c: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa5ea8c: movz            x17, #0xf30c
    //     0xa5ea90: add             lr, x0, x17
    //     0xa5ea94: ldr             lr, [x21, lr, lsl #3]
    //     0xa5ea98: blr             lr
    // 0xa5ea9c: mov             x19, x0
    // 0xa5eaa0: ldur            x5, [fp, #-0x78]
    // 0xa5eaa4: ldur            x4, [fp, #-0x80]
    // 0xa5eaa8: ldur            x2, [fp, #-0x90]
    // 0xa5eaac: ldur            x1, [fp, #-0x98]
    // 0xa5eab0: ldur            x0, [fp, #-0xa0]
    // 0xa5eab4: ldur            x14, [fp, #-0x18]
    // 0xa5eab8: ldur            x13, [fp, #-0x20]
    // 0xa5eabc: ldur            x12, [fp, #-0x40]
    // 0xa5eac0: ldur            x11, [fp, #-0x48]
    // 0xa5eac4: ldur            x10, [fp, #-0x50]
    // 0xa5eac8: ldur            x9, [fp, #-0x58]
    // 0xa5eacc: ldur            x8, [fp, #-0x60]
    // 0xa5ead0: ldur            x7, [fp, #-0x68]
    // 0xa5ead4: ldur            x6, [fp, #-0x70]
    // 0xa5ead8: ldur            x3, [fp, #-0x88]
    // 0xa5eadc: stur            x19, [fp, #-0x38]
    // 0xa5eae0: r20 = LoadInt32Instr(r14)
    //     0xa5eae0: sbfx            x20, x14, #1, #0x1f
    //     0xa5eae4: tbz             w14, #0, #0xa5eaec
    //     0xa5eae8: ldur            x20, [x14, #7]
    // 0xa5eaec: stur            x20, [fp, #-8]
    // 0xa5eaf0: r0 = ArticleDetail()
    //     0xa5eaf0: bl              #0x8ec568  ; AllocateArticleDetailStub -> ArticleDetail (size=0x4c)
    // 0xa5eaf4: mov             x1, x0
    // 0xa5eaf8: ldur            x0, [fp, #-8]
    // 0xa5eafc: StoreField: r1->field_7 = r0
    //     0xa5eafc: stur            x0, [x1, #7]
    // 0xa5eb00: ldur            x0, [fp, #-0x20]
    // 0xa5eb04: StoreField: r1->field_f = r0
    //     0xa5eb04: stur            w0, [x1, #0xf]
    // 0xa5eb08: ldur            x0, [fp, #-0x40]
    // 0xa5eb0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5eb0c: stur            w0, [x1, #0x17]
    // 0xa5eb10: ldur            x0, [fp, #-0x48]
    // 0xa5eb14: StoreField: r1->field_1b = r0
    //     0xa5eb14: stur            w0, [x1, #0x1b]
    // 0xa5eb18: ldur            x0, [fp, #-0x50]
    // 0xa5eb1c: StoreField: r1->field_1f = r0
    //     0xa5eb1c: stur            w0, [x1, #0x1f]
    // 0xa5eb20: ldur            x0, [fp, #-0x58]
    // 0xa5eb24: StoreField: r1->field_23 = r0
    //     0xa5eb24: stur            w0, [x1, #0x23]
    // 0xa5eb28: ldur            x0, [fp, #-0x60]
    // 0xa5eb2c: StoreField: r1->field_2b = r0
    //     0xa5eb2c: stur            w0, [x1, #0x2b]
    // 0xa5eb30: ldur            x0, [fp, #-0x68]
    // 0xa5eb34: StoreField: r1->field_13 = r0
    //     0xa5eb34: stur            w0, [x1, #0x13]
    // 0xa5eb38: ldur            x0, [fp, #-0x70]
    // 0xa5eb3c: StoreField: r1->field_33 = r0
    //     0xa5eb3c: stur            w0, [x1, #0x33]
    // 0xa5eb40: ldur            x0, [fp, #-0x78]
    // 0xa5eb44: StoreField: r1->field_27 = r0
    //     0xa5eb44: stur            w0, [x1, #0x27]
    // 0xa5eb48: ldur            x0, [fp, #-0x80]
    // 0xa5eb4c: StoreField: r1->field_2f = r0
    //     0xa5eb4c: stur            w0, [x1, #0x2f]
    // 0xa5eb50: ldur            x0, [fp, #-0x88]
    // 0xa5eb54: StoreField: r1->field_37 = r0
    //     0xa5eb54: stur            w0, [x1, #0x37]
    // 0xa5eb58: ldur            x0, [fp, #-0x90]
    // 0xa5eb5c: StoreField: r1->field_3b = r0
    //     0xa5eb5c: stur            w0, [x1, #0x3b]
    // 0xa5eb60: ldur            x0, [fp, #-0x98]
    // 0xa5eb64: StoreField: r1->field_3f = r0
    //     0xa5eb64: stur            w0, [x1, #0x3f]
    // 0xa5eb68: ldur            x0, [fp, #-0xa0]
    // 0xa5eb6c: StoreField: r1->field_43 = r0
    //     0xa5eb6c: stur            w0, [x1, #0x43]
    // 0xa5eb70: ldur            x0, [fp, #-0x38]
    // 0xa5eb74: StoreField: r1->field_47 = r0
    //     0xa5eb74: stur            w0, [x1, #0x47]
    // 0xa5eb78: mov             x0, x1
    // 0xa5eb7c: LeaveFrame
    //     0xa5eb7c: mov             SP, fp
    //     0xa5eb80: ldp             fp, lr, [SP], #0x10
    // 0xa5eb84: ret
    //     0xa5eb84: ret             
    // 0xa5eb88: r0 = RangeError()
    //     0xa5eb88: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5eb8c: mov             x1, x0
    // 0xa5eb90: r0 = "Not enough bytes available."
    //     0xa5eb90: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5eb94: ldr             x0, [x0, #0x8a8]
    // 0xa5eb98: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5eb98: stur            w0, [x1, #0x17]
    // 0xa5eb9c: r2 = false
    //     0xa5eb9c: add             x2, NULL, #0x30  ; false
    // 0xa5eba0: StoreField: r1->field_b = r2
    //     0xa5eba0: stur            w2, [x1, #0xb]
    // 0xa5eba4: mov             x0, x1
    // 0xa5eba8: r0 = Throw()
    //     0xa5eba8: bl              #0xec04b8  ; ThrowStub
    // 0xa5ebac: brk             #0
    // 0xa5ebb0: r0 = "Not enough bytes available."
    //     0xa5ebb0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5ebb4: ldr             x0, [x0, #0x8a8]
    // 0xa5ebb8: r2 = false
    //     0xa5ebb8: add             x2, NULL, #0x30  ; false
    // 0xa5ebbc: r0 = RangeError()
    //     0xa5ebbc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5ebc0: mov             x1, x0
    // 0xa5ebc4: r0 = "Not enough bytes available."
    //     0xa5ebc4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5ebc8: ldr             x0, [x0, #0x8a8]
    // 0xa5ebcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5ebcc: stur            w0, [x1, #0x17]
    // 0xa5ebd0: r0 = false
    //     0xa5ebd0: add             x0, NULL, #0x30  ; false
    // 0xa5ebd4: StoreField: r1->field_b = r0
    //     0xa5ebd4: stur            w0, [x1, #0xb]
    // 0xa5ebd8: mov             x0, x1
    // 0xa5ebdc: r0 = Throw()
    //     0xa5ebdc: bl              #0xec04b8  ; ThrowStub
    // 0xa5ebe0: brk             #0
    // 0xa5ebe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ebe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5ebe8: b               #0xa5e168
    // 0xa5ebec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5ebec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5ebf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ebf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5ebf4: b               #0xa5e1f0
    // 0xa5ebf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5ebf8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbcf974, size: 0x9f4
    // 0xbcf974: EnterFrame
    //     0xbcf974: stp             fp, lr, [SP, #-0x10]!
    //     0xbcf978: mov             fp, SP
    // 0xbcf97c: AllocStack(0x28)
    //     0xbcf97c: sub             SP, SP, #0x28
    // 0xbcf980: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbcf980: mov             x4, x2
    //     0xbcf984: stur            x2, [fp, #-8]
    //     0xbcf988: stur            x3, [fp, #-0x10]
    // 0xbcf98c: CheckStackOverflow
    //     0xbcf98c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbcf990: cmp             SP, x16
    //     0xbcf994: b.ls            #0xbd031c
    // 0xbcf998: mov             x0, x3
    // 0xbcf99c: r2 = Null
    //     0xbcf99c: mov             x2, NULL
    // 0xbcf9a0: r1 = Null
    //     0xbcf9a0: mov             x1, NULL
    // 0xbcf9a4: r4 = 60
    //     0xbcf9a4: movz            x4, #0x3c
    // 0xbcf9a8: branchIfSmi(r0, 0xbcf9b4)
    //     0xbcf9a8: tbz             w0, #0, #0xbcf9b4
    // 0xbcf9ac: r4 = LoadClassIdInstr(r0)
    //     0xbcf9ac: ldur            x4, [x0, #-1]
    //     0xbcf9b0: ubfx            x4, x4, #0xc, #0x14
    // 0xbcf9b4: r17 = 5598
    //     0xbcf9b4: movz            x17, #0x15de
    // 0xbcf9b8: cmp             x4, x17
    // 0xbcf9bc: b.eq            #0xbcf9d4
    // 0xbcf9c0: r8 = ArticleDetail
    //     0xbcf9c0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b4d8] Type: ArticleDetail
    //     0xbcf9c4: ldr             x8, [x8, #0x4d8]
    // 0xbcf9c8: r3 = Null
    //     0xbcf9c8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4e0] Null
    //     0xbcf9cc: ldr             x3, [x3, #0x4e0]
    // 0xbcf9d0: r0 = ArticleDetail()
    //     0xbcf9d0: bl              #0x6fb53c  ; IsType_ArticleDetail_Stub
    // 0xbcf9d4: ldur            x0, [fp, #-8]
    // 0xbcf9d8: LoadField: r1 = r0->field_b
    //     0xbcf9d8: ldur            w1, [x0, #0xb]
    // 0xbcf9dc: DecompressPointer r1
    //     0xbcf9dc: add             x1, x1, HEAP, lsl #32
    // 0xbcf9e0: LoadField: r2 = r1->field_13
    //     0xbcf9e0: ldur            w2, [x1, #0x13]
    // 0xbcf9e4: LoadField: r1 = r0->field_13
    //     0xbcf9e4: ldur            x1, [x0, #0x13]
    // 0xbcf9e8: r3 = LoadInt32Instr(r2)
    //     0xbcf9e8: sbfx            x3, x2, #1, #0x1f
    // 0xbcf9ec: sub             x2, x3, x1
    // 0xbcf9f0: cmp             x2, #1
    // 0xbcf9f4: b.ge            #0xbcfa04
    // 0xbcf9f8: mov             x1, x0
    // 0xbcf9fc: r2 = 1
    //     0xbcf9fc: movz            x2, #0x1
    // 0xbcfa00: r0 = _increaseBufferSize()
    //     0xbcfa00: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfa04: ldur            x3, [fp, #-8]
    // 0xbcfa08: r2 = 16
    //     0xbcfa08: movz            x2, #0x10
    // 0xbcfa0c: LoadField: r4 = r3->field_b
    //     0xbcfa0c: ldur            w4, [x3, #0xb]
    // 0xbcfa10: DecompressPointer r4
    //     0xbcfa10: add             x4, x4, HEAP, lsl #32
    // 0xbcfa14: LoadField: r5 = r3->field_13
    //     0xbcfa14: ldur            x5, [x3, #0x13]
    // 0xbcfa18: add             x6, x5, #1
    // 0xbcfa1c: StoreField: r3->field_13 = r6
    //     0xbcfa1c: stur            x6, [x3, #0x13]
    // 0xbcfa20: LoadField: r0 = r4->field_13
    //     0xbcfa20: ldur            w0, [x4, #0x13]
    // 0xbcfa24: r7 = LoadInt32Instr(r0)
    //     0xbcfa24: sbfx            x7, x0, #1, #0x1f
    // 0xbcfa28: mov             x0, x7
    // 0xbcfa2c: mov             x1, x5
    // 0xbcfa30: cmp             x1, x0
    // 0xbcfa34: b.hs            #0xbd0324
    // 0xbcfa38: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbcfa38: add             x0, x4, x5
    //     0xbcfa3c: strb            w2, [x0, #0x17]
    // 0xbcfa40: sub             x0, x7, x6
    // 0xbcfa44: cmp             x0, #1
    // 0xbcfa48: b.ge            #0xbcfa58
    // 0xbcfa4c: mov             x1, x3
    // 0xbcfa50: r2 = 1
    //     0xbcfa50: movz            x2, #0x1
    // 0xbcfa54: r0 = _increaseBufferSize()
    //     0xbcfa54: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfa58: ldur            x2, [fp, #-8]
    // 0xbcfa5c: ldur            x3, [fp, #-0x10]
    // 0xbcfa60: LoadField: r4 = r2->field_b
    //     0xbcfa60: ldur            w4, [x2, #0xb]
    // 0xbcfa64: DecompressPointer r4
    //     0xbcfa64: add             x4, x4, HEAP, lsl #32
    // 0xbcfa68: LoadField: r5 = r2->field_13
    //     0xbcfa68: ldur            x5, [x2, #0x13]
    // 0xbcfa6c: add             x0, x5, #1
    // 0xbcfa70: StoreField: r2->field_13 = r0
    //     0xbcfa70: stur            x0, [x2, #0x13]
    // 0xbcfa74: LoadField: r0 = r4->field_13
    //     0xbcfa74: ldur            w0, [x4, #0x13]
    // 0xbcfa78: r1 = LoadInt32Instr(r0)
    //     0xbcfa78: sbfx            x1, x0, #1, #0x1f
    // 0xbcfa7c: mov             x0, x1
    // 0xbcfa80: mov             x1, x5
    // 0xbcfa84: cmp             x1, x0
    // 0xbcfa88: b.hs            #0xbd0328
    // 0xbcfa8c: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbcfa8c: add             x0, x4, x5
    //     0xbcfa90: strb            wzr, [x0, #0x17]
    // 0xbcfa94: LoadField: r4 = r3->field_7
    //     0xbcfa94: ldur            x4, [x3, #7]
    // 0xbcfa98: r0 = BoxInt64Instr(r4)
    //     0xbcfa98: sbfiz           x0, x4, #1, #0x1f
    //     0xbcfa9c: cmp             x4, x0, asr #1
    //     0xbcfaa0: b.eq            #0xbcfaac
    //     0xbcfaa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbcfaa8: stur            x4, [x0, #7]
    // 0xbcfaac: r16 = <int>
    //     0xbcfaac: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbcfab0: stp             x2, x16, [SP, #8]
    // 0xbcfab4: str             x0, [SP]
    // 0xbcfab8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfab8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfabc: r0 = write()
    //     0xbcfabc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfac0: ldur            x0, [fp, #-8]
    // 0xbcfac4: LoadField: r1 = r0->field_b
    //     0xbcfac4: ldur            w1, [x0, #0xb]
    // 0xbcfac8: DecompressPointer r1
    //     0xbcfac8: add             x1, x1, HEAP, lsl #32
    // 0xbcfacc: LoadField: r2 = r1->field_13
    //     0xbcfacc: ldur            w2, [x1, #0x13]
    // 0xbcfad0: LoadField: r1 = r0->field_13
    //     0xbcfad0: ldur            x1, [x0, #0x13]
    // 0xbcfad4: r3 = LoadInt32Instr(r2)
    //     0xbcfad4: sbfx            x3, x2, #1, #0x1f
    // 0xbcfad8: sub             x2, x3, x1
    // 0xbcfadc: cmp             x2, #1
    // 0xbcfae0: b.ge            #0xbcfaf0
    // 0xbcfae4: mov             x1, x0
    // 0xbcfae8: r2 = 1
    //     0xbcfae8: movz            x2, #0x1
    // 0xbcfaec: r0 = _increaseBufferSize()
    //     0xbcfaec: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfaf0: ldur            x2, [fp, #-8]
    // 0xbcfaf4: ldur            x3, [fp, #-0x10]
    // 0xbcfaf8: r4 = 1
    //     0xbcfaf8: movz            x4, #0x1
    // 0xbcfafc: LoadField: r5 = r2->field_b
    //     0xbcfafc: ldur            w5, [x2, #0xb]
    // 0xbcfb00: DecompressPointer r5
    //     0xbcfb00: add             x5, x5, HEAP, lsl #32
    // 0xbcfb04: LoadField: r6 = r2->field_13
    //     0xbcfb04: ldur            x6, [x2, #0x13]
    // 0xbcfb08: add             x0, x6, #1
    // 0xbcfb0c: StoreField: r2->field_13 = r0
    //     0xbcfb0c: stur            x0, [x2, #0x13]
    // 0xbcfb10: LoadField: r0 = r5->field_13
    //     0xbcfb10: ldur            w0, [x5, #0x13]
    // 0xbcfb14: r1 = LoadInt32Instr(r0)
    //     0xbcfb14: sbfx            x1, x0, #1, #0x1f
    // 0xbcfb18: mov             x0, x1
    // 0xbcfb1c: mov             x1, x6
    // 0xbcfb20: cmp             x1, x0
    // 0xbcfb24: b.hs            #0xbd032c
    // 0xbcfb28: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfb28: add             x0, x5, x6
    //     0xbcfb2c: strb            w4, [x0, #0x17]
    // 0xbcfb30: LoadField: r0 = r3->field_f
    //     0xbcfb30: ldur            w0, [x3, #0xf]
    // 0xbcfb34: DecompressPointer r0
    //     0xbcfb34: add             x0, x0, HEAP, lsl #32
    // 0xbcfb38: r16 = <String>
    //     0xbcfb38: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcfb3c: stp             x2, x16, [SP, #8]
    // 0xbcfb40: str             x0, [SP]
    // 0xbcfb44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfb44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfb48: r0 = write()
    //     0xbcfb48: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfb4c: ldur            x0, [fp, #-8]
    // 0xbcfb50: LoadField: r1 = r0->field_b
    //     0xbcfb50: ldur            w1, [x0, #0xb]
    // 0xbcfb54: DecompressPointer r1
    //     0xbcfb54: add             x1, x1, HEAP, lsl #32
    // 0xbcfb58: LoadField: r2 = r1->field_13
    //     0xbcfb58: ldur            w2, [x1, #0x13]
    // 0xbcfb5c: LoadField: r1 = r0->field_13
    //     0xbcfb5c: ldur            x1, [x0, #0x13]
    // 0xbcfb60: r3 = LoadInt32Instr(r2)
    //     0xbcfb60: sbfx            x3, x2, #1, #0x1f
    // 0xbcfb64: sub             x2, x3, x1
    // 0xbcfb68: cmp             x2, #1
    // 0xbcfb6c: b.ge            #0xbcfb7c
    // 0xbcfb70: mov             x1, x0
    // 0xbcfb74: r2 = 1
    //     0xbcfb74: movz            x2, #0x1
    // 0xbcfb78: r0 = _increaseBufferSize()
    //     0xbcfb78: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfb7c: ldur            x2, [fp, #-8]
    // 0xbcfb80: ldur            x3, [fp, #-0x10]
    // 0xbcfb84: r4 = 2
    //     0xbcfb84: movz            x4, #0x2
    // 0xbcfb88: LoadField: r5 = r2->field_b
    //     0xbcfb88: ldur            w5, [x2, #0xb]
    // 0xbcfb8c: DecompressPointer r5
    //     0xbcfb8c: add             x5, x5, HEAP, lsl #32
    // 0xbcfb90: LoadField: r6 = r2->field_13
    //     0xbcfb90: ldur            x6, [x2, #0x13]
    // 0xbcfb94: add             x0, x6, #1
    // 0xbcfb98: StoreField: r2->field_13 = r0
    //     0xbcfb98: stur            x0, [x2, #0x13]
    // 0xbcfb9c: LoadField: r0 = r5->field_13
    //     0xbcfb9c: ldur            w0, [x5, #0x13]
    // 0xbcfba0: r1 = LoadInt32Instr(r0)
    //     0xbcfba0: sbfx            x1, x0, #1, #0x1f
    // 0xbcfba4: mov             x0, x1
    // 0xbcfba8: mov             x1, x6
    // 0xbcfbac: cmp             x1, x0
    // 0xbcfbb0: b.hs            #0xbd0330
    // 0xbcfbb4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfbb4: add             x0, x5, x6
    //     0xbcfbb8: strb            w4, [x0, #0x17]
    // 0xbcfbbc: LoadField: r0 = r3->field_13
    //     0xbcfbbc: ldur            w0, [x3, #0x13]
    // 0xbcfbc0: DecompressPointer r0
    //     0xbcfbc0: add             x0, x0, HEAP, lsl #32
    // 0xbcfbc4: r16 = <String?>
    //     0xbcfbc4: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbcfbc8: stp             x2, x16, [SP, #8]
    // 0xbcfbcc: str             x0, [SP]
    // 0xbcfbd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfbd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfbd4: r0 = write()
    //     0xbcfbd4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfbd8: ldur            x0, [fp, #-8]
    // 0xbcfbdc: LoadField: r1 = r0->field_b
    //     0xbcfbdc: ldur            w1, [x0, #0xb]
    // 0xbcfbe0: DecompressPointer r1
    //     0xbcfbe0: add             x1, x1, HEAP, lsl #32
    // 0xbcfbe4: LoadField: r2 = r1->field_13
    //     0xbcfbe4: ldur            w2, [x1, #0x13]
    // 0xbcfbe8: LoadField: r1 = r0->field_13
    //     0xbcfbe8: ldur            x1, [x0, #0x13]
    // 0xbcfbec: r3 = LoadInt32Instr(r2)
    //     0xbcfbec: sbfx            x3, x2, #1, #0x1f
    // 0xbcfbf0: sub             x2, x3, x1
    // 0xbcfbf4: cmp             x2, #1
    // 0xbcfbf8: b.ge            #0xbcfc08
    // 0xbcfbfc: mov             x1, x0
    // 0xbcfc00: r2 = 1
    //     0xbcfc00: movz            x2, #0x1
    // 0xbcfc04: r0 = _increaseBufferSize()
    //     0xbcfc04: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfc08: ldur            x2, [fp, #-8]
    // 0xbcfc0c: ldur            x3, [fp, #-0x10]
    // 0xbcfc10: r4 = 3
    //     0xbcfc10: movz            x4, #0x3
    // 0xbcfc14: LoadField: r5 = r2->field_b
    //     0xbcfc14: ldur            w5, [x2, #0xb]
    // 0xbcfc18: DecompressPointer r5
    //     0xbcfc18: add             x5, x5, HEAP, lsl #32
    // 0xbcfc1c: LoadField: r6 = r2->field_13
    //     0xbcfc1c: ldur            x6, [x2, #0x13]
    // 0xbcfc20: add             x0, x6, #1
    // 0xbcfc24: StoreField: r2->field_13 = r0
    //     0xbcfc24: stur            x0, [x2, #0x13]
    // 0xbcfc28: LoadField: r0 = r5->field_13
    //     0xbcfc28: ldur            w0, [x5, #0x13]
    // 0xbcfc2c: r1 = LoadInt32Instr(r0)
    //     0xbcfc2c: sbfx            x1, x0, #1, #0x1f
    // 0xbcfc30: mov             x0, x1
    // 0xbcfc34: mov             x1, x6
    // 0xbcfc38: cmp             x1, x0
    // 0xbcfc3c: b.hs            #0xbd0334
    // 0xbcfc40: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfc40: add             x0, x5, x6
    //     0xbcfc44: strb            w4, [x0, #0x17]
    // 0xbcfc48: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbcfc48: ldur            w0, [x3, #0x17]
    // 0xbcfc4c: DecompressPointer r0
    //     0xbcfc4c: add             x0, x0, HEAP, lsl #32
    // 0xbcfc50: r16 = <Image>
    //     0xbcfc50: ldr             x16, [PP, #0x7b60]  ; [pp+0x7b60] TypeArguments: <Image>
    // 0xbcfc54: stp             x2, x16, [SP, #8]
    // 0xbcfc58: str             x0, [SP]
    // 0xbcfc5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfc5c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfc60: r0 = write()
    //     0xbcfc60: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfc64: ldur            x0, [fp, #-8]
    // 0xbcfc68: LoadField: r1 = r0->field_b
    //     0xbcfc68: ldur            w1, [x0, #0xb]
    // 0xbcfc6c: DecompressPointer r1
    //     0xbcfc6c: add             x1, x1, HEAP, lsl #32
    // 0xbcfc70: LoadField: r2 = r1->field_13
    //     0xbcfc70: ldur            w2, [x1, #0x13]
    // 0xbcfc74: LoadField: r1 = r0->field_13
    //     0xbcfc74: ldur            x1, [x0, #0x13]
    // 0xbcfc78: r3 = LoadInt32Instr(r2)
    //     0xbcfc78: sbfx            x3, x2, #1, #0x1f
    // 0xbcfc7c: sub             x2, x3, x1
    // 0xbcfc80: cmp             x2, #1
    // 0xbcfc84: b.ge            #0xbcfc94
    // 0xbcfc88: mov             x1, x0
    // 0xbcfc8c: r2 = 1
    //     0xbcfc8c: movz            x2, #0x1
    // 0xbcfc90: r0 = _increaseBufferSize()
    //     0xbcfc90: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfc94: ldur            x2, [fp, #-8]
    // 0xbcfc98: ldur            x3, [fp, #-0x10]
    // 0xbcfc9c: r4 = 4
    //     0xbcfc9c: movz            x4, #0x4
    // 0xbcfca0: LoadField: r5 = r2->field_b
    //     0xbcfca0: ldur            w5, [x2, #0xb]
    // 0xbcfca4: DecompressPointer r5
    //     0xbcfca4: add             x5, x5, HEAP, lsl #32
    // 0xbcfca8: LoadField: r6 = r2->field_13
    //     0xbcfca8: ldur            x6, [x2, #0x13]
    // 0xbcfcac: add             x0, x6, #1
    // 0xbcfcb0: StoreField: r2->field_13 = r0
    //     0xbcfcb0: stur            x0, [x2, #0x13]
    // 0xbcfcb4: LoadField: r0 = r5->field_13
    //     0xbcfcb4: ldur            w0, [x5, #0x13]
    // 0xbcfcb8: r1 = LoadInt32Instr(r0)
    //     0xbcfcb8: sbfx            x1, x0, #1, #0x1f
    // 0xbcfcbc: mov             x0, x1
    // 0xbcfcc0: mov             x1, x6
    // 0xbcfcc4: cmp             x1, x0
    // 0xbcfcc8: b.hs            #0xbd0338
    // 0xbcfccc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfccc: add             x0, x5, x6
    //     0xbcfcd0: strb            w4, [x0, #0x17]
    // 0xbcfcd4: LoadField: r0 = r3->field_1b
    //     0xbcfcd4: ldur            w0, [x3, #0x1b]
    // 0xbcfcd8: DecompressPointer r0
    //     0xbcfcd8: add             x0, x0, HEAP, lsl #32
    // 0xbcfcdc: r16 = <Category>
    //     0xbcfcdc: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0xbcfce0: stp             x2, x16, [SP, #8]
    // 0xbcfce4: str             x0, [SP]
    // 0xbcfce8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfce8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfcec: r0 = write()
    //     0xbcfcec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfcf0: ldur            x0, [fp, #-8]
    // 0xbcfcf4: LoadField: r1 = r0->field_b
    //     0xbcfcf4: ldur            w1, [x0, #0xb]
    // 0xbcfcf8: DecompressPointer r1
    //     0xbcfcf8: add             x1, x1, HEAP, lsl #32
    // 0xbcfcfc: LoadField: r2 = r1->field_13
    //     0xbcfcfc: ldur            w2, [x1, #0x13]
    // 0xbcfd00: LoadField: r1 = r0->field_13
    //     0xbcfd00: ldur            x1, [x0, #0x13]
    // 0xbcfd04: r3 = LoadInt32Instr(r2)
    //     0xbcfd04: sbfx            x3, x2, #1, #0x1f
    // 0xbcfd08: sub             x2, x3, x1
    // 0xbcfd0c: cmp             x2, #1
    // 0xbcfd10: b.ge            #0xbcfd20
    // 0xbcfd14: mov             x1, x0
    // 0xbcfd18: r2 = 1
    //     0xbcfd18: movz            x2, #0x1
    // 0xbcfd1c: r0 = _increaseBufferSize()
    //     0xbcfd1c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfd20: ldur            x2, [fp, #-8]
    // 0xbcfd24: ldur            x3, [fp, #-0x10]
    // 0xbcfd28: r4 = 5
    //     0xbcfd28: movz            x4, #0x5
    // 0xbcfd2c: LoadField: r5 = r2->field_b
    //     0xbcfd2c: ldur            w5, [x2, #0xb]
    // 0xbcfd30: DecompressPointer r5
    //     0xbcfd30: add             x5, x5, HEAP, lsl #32
    // 0xbcfd34: LoadField: r6 = r2->field_13
    //     0xbcfd34: ldur            x6, [x2, #0x13]
    // 0xbcfd38: add             x0, x6, #1
    // 0xbcfd3c: StoreField: r2->field_13 = r0
    //     0xbcfd3c: stur            x0, [x2, #0x13]
    // 0xbcfd40: LoadField: r0 = r5->field_13
    //     0xbcfd40: ldur            w0, [x5, #0x13]
    // 0xbcfd44: r1 = LoadInt32Instr(r0)
    //     0xbcfd44: sbfx            x1, x0, #1, #0x1f
    // 0xbcfd48: mov             x0, x1
    // 0xbcfd4c: mov             x1, x6
    // 0xbcfd50: cmp             x1, x0
    // 0xbcfd54: b.hs            #0xbd033c
    // 0xbcfd58: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfd58: add             x0, x5, x6
    //     0xbcfd5c: strb            w4, [x0, #0x17]
    // 0xbcfd60: LoadField: r0 = r3->field_1f
    //     0xbcfd60: ldur            w0, [x3, #0x1f]
    // 0xbcfd64: DecompressPointer r0
    //     0xbcfd64: add             x0, x0, HEAP, lsl #32
    // 0xbcfd68: r16 = <String>
    //     0xbcfd68: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcfd6c: stp             x2, x16, [SP, #8]
    // 0xbcfd70: str             x0, [SP]
    // 0xbcfd74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfd74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfd78: r0 = write()
    //     0xbcfd78: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfd7c: ldur            x0, [fp, #-8]
    // 0xbcfd80: LoadField: r1 = r0->field_b
    //     0xbcfd80: ldur            w1, [x0, #0xb]
    // 0xbcfd84: DecompressPointer r1
    //     0xbcfd84: add             x1, x1, HEAP, lsl #32
    // 0xbcfd88: LoadField: r2 = r1->field_13
    //     0xbcfd88: ldur            w2, [x1, #0x13]
    // 0xbcfd8c: LoadField: r1 = r0->field_13
    //     0xbcfd8c: ldur            x1, [x0, #0x13]
    // 0xbcfd90: r3 = LoadInt32Instr(r2)
    //     0xbcfd90: sbfx            x3, x2, #1, #0x1f
    // 0xbcfd94: sub             x2, x3, x1
    // 0xbcfd98: cmp             x2, #1
    // 0xbcfd9c: b.ge            #0xbcfdac
    // 0xbcfda0: mov             x1, x0
    // 0xbcfda4: r2 = 1
    //     0xbcfda4: movz            x2, #0x1
    // 0xbcfda8: r0 = _increaseBufferSize()
    //     0xbcfda8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfdac: ldur            x2, [fp, #-8]
    // 0xbcfdb0: ldur            x3, [fp, #-0x10]
    // 0xbcfdb4: r4 = 6
    //     0xbcfdb4: movz            x4, #0x6
    // 0xbcfdb8: LoadField: r5 = r2->field_b
    //     0xbcfdb8: ldur            w5, [x2, #0xb]
    // 0xbcfdbc: DecompressPointer r5
    //     0xbcfdbc: add             x5, x5, HEAP, lsl #32
    // 0xbcfdc0: LoadField: r6 = r2->field_13
    //     0xbcfdc0: ldur            x6, [x2, #0x13]
    // 0xbcfdc4: add             x0, x6, #1
    // 0xbcfdc8: StoreField: r2->field_13 = r0
    //     0xbcfdc8: stur            x0, [x2, #0x13]
    // 0xbcfdcc: LoadField: r0 = r5->field_13
    //     0xbcfdcc: ldur            w0, [x5, #0x13]
    // 0xbcfdd0: r1 = LoadInt32Instr(r0)
    //     0xbcfdd0: sbfx            x1, x0, #1, #0x1f
    // 0xbcfdd4: mov             x0, x1
    // 0xbcfdd8: mov             x1, x6
    // 0xbcfddc: cmp             x1, x0
    // 0xbcfde0: b.hs            #0xbd0340
    // 0xbcfde4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfde4: add             x0, x5, x6
    //     0xbcfde8: strb            w4, [x0, #0x17]
    // 0xbcfdec: LoadField: r0 = r3->field_23
    //     0xbcfdec: ldur            w0, [x3, #0x23]
    // 0xbcfdf0: DecompressPointer r0
    //     0xbcfdf0: add             x0, x0, HEAP, lsl #32
    // 0xbcfdf4: r16 = <String>
    //     0xbcfdf4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcfdf8: stp             x2, x16, [SP, #8]
    // 0xbcfdfc: str             x0, [SP]
    // 0xbcfe00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfe00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfe04: r0 = write()
    //     0xbcfe04: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfe08: ldur            x0, [fp, #-8]
    // 0xbcfe0c: LoadField: r1 = r0->field_b
    //     0xbcfe0c: ldur            w1, [x0, #0xb]
    // 0xbcfe10: DecompressPointer r1
    //     0xbcfe10: add             x1, x1, HEAP, lsl #32
    // 0xbcfe14: LoadField: r2 = r1->field_13
    //     0xbcfe14: ldur            w2, [x1, #0x13]
    // 0xbcfe18: LoadField: r1 = r0->field_13
    //     0xbcfe18: ldur            x1, [x0, #0x13]
    // 0xbcfe1c: r3 = LoadInt32Instr(r2)
    //     0xbcfe1c: sbfx            x3, x2, #1, #0x1f
    // 0xbcfe20: sub             x2, x3, x1
    // 0xbcfe24: cmp             x2, #1
    // 0xbcfe28: b.ge            #0xbcfe38
    // 0xbcfe2c: mov             x1, x0
    // 0xbcfe30: r2 = 1
    //     0xbcfe30: movz            x2, #0x1
    // 0xbcfe34: r0 = _increaseBufferSize()
    //     0xbcfe34: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfe38: ldur            x2, [fp, #-8]
    // 0xbcfe3c: ldur            x3, [fp, #-0x10]
    // 0xbcfe40: r4 = 7
    //     0xbcfe40: movz            x4, #0x7
    // 0xbcfe44: LoadField: r5 = r2->field_b
    //     0xbcfe44: ldur            w5, [x2, #0xb]
    // 0xbcfe48: DecompressPointer r5
    //     0xbcfe48: add             x5, x5, HEAP, lsl #32
    // 0xbcfe4c: LoadField: r6 = r2->field_13
    //     0xbcfe4c: ldur            x6, [x2, #0x13]
    // 0xbcfe50: add             x0, x6, #1
    // 0xbcfe54: StoreField: r2->field_13 = r0
    //     0xbcfe54: stur            x0, [x2, #0x13]
    // 0xbcfe58: LoadField: r0 = r5->field_13
    //     0xbcfe58: ldur            w0, [x5, #0x13]
    // 0xbcfe5c: r1 = LoadInt32Instr(r0)
    //     0xbcfe5c: sbfx            x1, x0, #1, #0x1f
    // 0xbcfe60: mov             x0, x1
    // 0xbcfe64: mov             x1, x6
    // 0xbcfe68: cmp             x1, x0
    // 0xbcfe6c: b.hs            #0xbd0344
    // 0xbcfe70: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcfe70: add             x0, x5, x6
    //     0xbcfe74: strb            w4, [x0, #0x17]
    // 0xbcfe78: LoadField: r0 = r3->field_27
    //     0xbcfe78: ldur            w0, [x3, #0x27]
    // 0xbcfe7c: DecompressPointer r0
    //     0xbcfe7c: add             x0, x0, HEAP, lsl #32
    // 0xbcfe80: r16 = <List<Tag>>
    //     0xbcfe80: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b4f0] TypeArguments: <List<Tag>>
    //     0xbcfe84: ldr             x16, [x16, #0x4f0]
    // 0xbcfe88: stp             x2, x16, [SP, #8]
    // 0xbcfe8c: str             x0, [SP]
    // 0xbcfe90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcfe90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcfe94: r0 = write()
    //     0xbcfe94: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcfe98: ldur            x0, [fp, #-8]
    // 0xbcfe9c: LoadField: r1 = r0->field_b
    //     0xbcfe9c: ldur            w1, [x0, #0xb]
    // 0xbcfea0: DecompressPointer r1
    //     0xbcfea0: add             x1, x1, HEAP, lsl #32
    // 0xbcfea4: LoadField: r2 = r1->field_13
    //     0xbcfea4: ldur            w2, [x1, #0x13]
    // 0xbcfea8: LoadField: r1 = r0->field_13
    //     0xbcfea8: ldur            x1, [x0, #0x13]
    // 0xbcfeac: r3 = LoadInt32Instr(r2)
    //     0xbcfeac: sbfx            x3, x2, #1, #0x1f
    // 0xbcfeb0: sub             x2, x3, x1
    // 0xbcfeb4: cmp             x2, #1
    // 0xbcfeb8: b.ge            #0xbcfec8
    // 0xbcfebc: mov             x1, x0
    // 0xbcfec0: r2 = 1
    //     0xbcfec0: movz            x2, #0x1
    // 0xbcfec4: r0 = _increaseBufferSize()
    //     0xbcfec4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcfec8: ldur            x2, [fp, #-8]
    // 0xbcfecc: ldur            x3, [fp, #-0x10]
    // 0xbcfed0: r4 = 8
    //     0xbcfed0: movz            x4, #0x8
    // 0xbcfed4: LoadField: r5 = r2->field_b
    //     0xbcfed4: ldur            w5, [x2, #0xb]
    // 0xbcfed8: DecompressPointer r5
    //     0xbcfed8: add             x5, x5, HEAP, lsl #32
    // 0xbcfedc: LoadField: r6 = r2->field_13
    //     0xbcfedc: ldur            x6, [x2, #0x13]
    // 0xbcfee0: add             x0, x6, #1
    // 0xbcfee4: StoreField: r2->field_13 = r0
    //     0xbcfee4: stur            x0, [x2, #0x13]
    // 0xbcfee8: LoadField: r0 = r5->field_13
    //     0xbcfee8: ldur            w0, [x5, #0x13]
    // 0xbcfeec: r1 = LoadInt32Instr(r0)
    //     0xbcfeec: sbfx            x1, x0, #1, #0x1f
    // 0xbcfef0: mov             x0, x1
    // 0xbcfef4: mov             x1, x6
    // 0xbcfef8: cmp             x1, x0
    // 0xbcfefc: b.hs            #0xbd0348
    // 0xbcff00: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcff00: add             x0, x5, x6
    //     0xbcff04: strb            w4, [x0, #0x17]
    // 0xbcff08: LoadField: r0 = r3->field_2b
    //     0xbcff08: ldur            w0, [x3, #0x2b]
    // 0xbcff0c: DecompressPointer r0
    //     0xbcff0c: add             x0, x0, HEAP, lsl #32
    // 0xbcff10: r16 = <String>
    //     0xbcff10: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcff14: stp             x2, x16, [SP, #8]
    // 0xbcff18: str             x0, [SP]
    // 0xbcff1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcff1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcff20: r0 = write()
    //     0xbcff20: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcff24: ldur            x0, [fp, #-8]
    // 0xbcff28: LoadField: r1 = r0->field_b
    //     0xbcff28: ldur            w1, [x0, #0xb]
    // 0xbcff2c: DecompressPointer r1
    //     0xbcff2c: add             x1, x1, HEAP, lsl #32
    // 0xbcff30: LoadField: r2 = r1->field_13
    //     0xbcff30: ldur            w2, [x1, #0x13]
    // 0xbcff34: LoadField: r1 = r0->field_13
    //     0xbcff34: ldur            x1, [x0, #0x13]
    // 0xbcff38: r3 = LoadInt32Instr(r2)
    //     0xbcff38: sbfx            x3, x2, #1, #0x1f
    // 0xbcff3c: sub             x2, x3, x1
    // 0xbcff40: cmp             x2, #1
    // 0xbcff44: b.ge            #0xbcff54
    // 0xbcff48: mov             x1, x0
    // 0xbcff4c: r2 = 1
    //     0xbcff4c: movz            x2, #0x1
    // 0xbcff50: r0 = _increaseBufferSize()
    //     0xbcff50: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcff54: ldur            x2, [fp, #-8]
    // 0xbcff58: ldur            x3, [fp, #-0x10]
    // 0xbcff5c: r4 = 9
    //     0xbcff5c: movz            x4, #0x9
    // 0xbcff60: LoadField: r5 = r2->field_b
    //     0xbcff60: ldur            w5, [x2, #0xb]
    // 0xbcff64: DecompressPointer r5
    //     0xbcff64: add             x5, x5, HEAP, lsl #32
    // 0xbcff68: LoadField: r6 = r2->field_13
    //     0xbcff68: ldur            x6, [x2, #0x13]
    // 0xbcff6c: add             x0, x6, #1
    // 0xbcff70: StoreField: r2->field_13 = r0
    //     0xbcff70: stur            x0, [x2, #0x13]
    // 0xbcff74: LoadField: r0 = r5->field_13
    //     0xbcff74: ldur            w0, [x5, #0x13]
    // 0xbcff78: r1 = LoadInt32Instr(r0)
    //     0xbcff78: sbfx            x1, x0, #1, #0x1f
    // 0xbcff7c: mov             x0, x1
    // 0xbcff80: mov             x1, x6
    // 0xbcff84: cmp             x1, x0
    // 0xbcff88: b.hs            #0xbd034c
    // 0xbcff8c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcff8c: add             x0, x5, x6
    //     0xbcff90: strb            w4, [x0, #0x17]
    // 0xbcff94: LoadField: r0 = r3->field_2f
    //     0xbcff94: ldur            w0, [x3, #0x2f]
    // 0xbcff98: DecompressPointer r0
    //     0xbcff98: add             x0, x0, HEAP, lsl #32
    // 0xbcff9c: r16 = <List<Article>>
    //     0xbcff9c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xbcffa0: ldr             x16, [x16, #0x4f8]
    // 0xbcffa4: stp             x2, x16, [SP, #8]
    // 0xbcffa8: str             x0, [SP]
    // 0xbcffac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcffac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcffb0: r0 = write()
    //     0xbcffb0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcffb4: ldur            x0, [fp, #-8]
    // 0xbcffb8: LoadField: r1 = r0->field_b
    //     0xbcffb8: ldur            w1, [x0, #0xb]
    // 0xbcffbc: DecompressPointer r1
    //     0xbcffbc: add             x1, x1, HEAP, lsl #32
    // 0xbcffc0: LoadField: r2 = r1->field_13
    //     0xbcffc0: ldur            w2, [x1, #0x13]
    // 0xbcffc4: LoadField: r1 = r0->field_13
    //     0xbcffc4: ldur            x1, [x0, #0x13]
    // 0xbcffc8: r3 = LoadInt32Instr(r2)
    //     0xbcffc8: sbfx            x3, x2, #1, #0x1f
    // 0xbcffcc: sub             x2, x3, x1
    // 0xbcffd0: cmp             x2, #1
    // 0xbcffd4: b.ge            #0xbcffe4
    // 0xbcffd8: mov             x1, x0
    // 0xbcffdc: r2 = 1
    //     0xbcffdc: movz            x2, #0x1
    // 0xbcffe0: r0 = _increaseBufferSize()
    //     0xbcffe0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcffe4: ldur            x2, [fp, #-8]
    // 0xbcffe8: ldur            x3, [fp, #-0x10]
    // 0xbcffec: r4 = 10
    //     0xbcffec: movz            x4, #0xa
    // 0xbcfff0: LoadField: r5 = r2->field_b
    //     0xbcfff0: ldur            w5, [x2, #0xb]
    // 0xbcfff4: DecompressPointer r5
    //     0xbcfff4: add             x5, x5, HEAP, lsl #32
    // 0xbcfff8: LoadField: r6 = r2->field_13
    //     0xbcfff8: ldur            x6, [x2, #0x13]
    // 0xbcfffc: add             x0, x6, #1
    // 0xbd0000: StoreField: r2->field_13 = r0
    //     0xbd0000: stur            x0, [x2, #0x13]
    // 0xbd0004: LoadField: r0 = r5->field_13
    //     0xbd0004: ldur            w0, [x5, #0x13]
    // 0xbd0008: r1 = LoadInt32Instr(r0)
    //     0xbd0008: sbfx            x1, x0, #1, #0x1f
    // 0xbd000c: mov             x0, x1
    // 0xbd0010: mov             x1, x6
    // 0xbd0014: cmp             x1, x0
    // 0xbd0018: b.hs            #0xbd0350
    // 0xbd001c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd001c: add             x0, x5, x6
    //     0xbd0020: strb            w4, [x0, #0x17]
    // 0xbd0024: LoadField: r0 = r3->field_33
    //     0xbd0024: ldur            w0, [x3, #0x33]
    // 0xbd0028: DecompressPointer r0
    //     0xbd0028: add             x0, x0, HEAP, lsl #32
    // 0xbd002c: r16 = <Author?>
    //     0xbd002c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b500] TypeArguments: <Author?>
    //     0xbd0030: ldr             x16, [x16, #0x500]
    // 0xbd0034: stp             x2, x16, [SP, #8]
    // 0xbd0038: str             x0, [SP]
    // 0xbd003c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd003c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0040: r0 = write()
    //     0xbd0040: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0044: ldur            x0, [fp, #-8]
    // 0xbd0048: LoadField: r1 = r0->field_b
    //     0xbd0048: ldur            w1, [x0, #0xb]
    // 0xbd004c: DecompressPointer r1
    //     0xbd004c: add             x1, x1, HEAP, lsl #32
    // 0xbd0050: LoadField: r2 = r1->field_13
    //     0xbd0050: ldur            w2, [x1, #0x13]
    // 0xbd0054: LoadField: r1 = r0->field_13
    //     0xbd0054: ldur            x1, [x0, #0x13]
    // 0xbd0058: r3 = LoadInt32Instr(r2)
    //     0xbd0058: sbfx            x3, x2, #1, #0x1f
    // 0xbd005c: sub             x2, x3, x1
    // 0xbd0060: cmp             x2, #1
    // 0xbd0064: b.ge            #0xbd0074
    // 0xbd0068: mov             x1, x0
    // 0xbd006c: r2 = 1
    //     0xbd006c: movz            x2, #0x1
    // 0xbd0070: r0 = _increaseBufferSize()
    //     0xbd0070: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0074: ldur            x2, [fp, #-8]
    // 0xbd0078: ldur            x3, [fp, #-0x10]
    // 0xbd007c: r4 = 11
    //     0xbd007c: movz            x4, #0xb
    // 0xbd0080: LoadField: r5 = r2->field_b
    //     0xbd0080: ldur            w5, [x2, #0xb]
    // 0xbd0084: DecompressPointer r5
    //     0xbd0084: add             x5, x5, HEAP, lsl #32
    // 0xbd0088: LoadField: r6 = r2->field_13
    //     0xbd0088: ldur            x6, [x2, #0x13]
    // 0xbd008c: add             x0, x6, #1
    // 0xbd0090: StoreField: r2->field_13 = r0
    //     0xbd0090: stur            x0, [x2, #0x13]
    // 0xbd0094: LoadField: r0 = r5->field_13
    //     0xbd0094: ldur            w0, [x5, #0x13]
    // 0xbd0098: r1 = LoadInt32Instr(r0)
    //     0xbd0098: sbfx            x1, x0, #1, #0x1f
    // 0xbd009c: mov             x0, x1
    // 0xbd00a0: mov             x1, x6
    // 0xbd00a4: cmp             x1, x0
    // 0xbd00a8: b.hs            #0xbd0354
    // 0xbd00ac: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd00ac: add             x0, x5, x6
    //     0xbd00b0: strb            w4, [x0, #0x17]
    // 0xbd00b4: LoadField: r0 = r3->field_37
    //     0xbd00b4: ldur            w0, [x3, #0x37]
    // 0xbd00b8: DecompressPointer r0
    //     0xbd00b8: add             x0, x0, HEAP, lsl #32
    // 0xbd00bc: r16 = <String?>
    //     0xbd00bc: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd00c0: stp             x2, x16, [SP, #8]
    // 0xbd00c4: str             x0, [SP]
    // 0xbd00c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd00c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd00cc: r0 = write()
    //     0xbd00cc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd00d0: ldur            x0, [fp, #-8]
    // 0xbd00d4: LoadField: r1 = r0->field_b
    //     0xbd00d4: ldur            w1, [x0, #0xb]
    // 0xbd00d8: DecompressPointer r1
    //     0xbd00d8: add             x1, x1, HEAP, lsl #32
    // 0xbd00dc: LoadField: r2 = r1->field_13
    //     0xbd00dc: ldur            w2, [x1, #0x13]
    // 0xbd00e0: LoadField: r1 = r0->field_13
    //     0xbd00e0: ldur            x1, [x0, #0x13]
    // 0xbd00e4: r3 = LoadInt32Instr(r2)
    //     0xbd00e4: sbfx            x3, x2, #1, #0x1f
    // 0xbd00e8: sub             x2, x3, x1
    // 0xbd00ec: cmp             x2, #1
    // 0xbd00f0: b.ge            #0xbd0100
    // 0xbd00f4: mov             x1, x0
    // 0xbd00f8: r2 = 1
    //     0xbd00f8: movz            x2, #0x1
    // 0xbd00fc: r0 = _increaseBufferSize()
    //     0xbd00fc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0100: ldur            x2, [fp, #-8]
    // 0xbd0104: ldur            x3, [fp, #-0x10]
    // 0xbd0108: r4 = 12
    //     0xbd0108: movz            x4, #0xc
    // 0xbd010c: LoadField: r5 = r2->field_b
    //     0xbd010c: ldur            w5, [x2, #0xb]
    // 0xbd0110: DecompressPointer r5
    //     0xbd0110: add             x5, x5, HEAP, lsl #32
    // 0xbd0114: LoadField: r6 = r2->field_13
    //     0xbd0114: ldur            x6, [x2, #0x13]
    // 0xbd0118: add             x0, x6, #1
    // 0xbd011c: StoreField: r2->field_13 = r0
    //     0xbd011c: stur            x0, [x2, #0x13]
    // 0xbd0120: LoadField: r0 = r5->field_13
    //     0xbd0120: ldur            w0, [x5, #0x13]
    // 0xbd0124: r1 = LoadInt32Instr(r0)
    //     0xbd0124: sbfx            x1, x0, #1, #0x1f
    // 0xbd0128: mov             x0, x1
    // 0xbd012c: mov             x1, x6
    // 0xbd0130: cmp             x1, x0
    // 0xbd0134: b.hs            #0xbd0358
    // 0xbd0138: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0138: add             x0, x5, x6
    //     0xbd013c: strb            w4, [x0, #0x17]
    // 0xbd0140: LoadField: r0 = r3->field_3b
    //     0xbd0140: ldur            w0, [x3, #0x3b]
    // 0xbd0144: DecompressPointer r0
    //     0xbd0144: add             x0, x0, HEAP, lsl #32
    // 0xbd0148: r16 = <List<ArticleInsertion>>
    //     0xbd0148: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b508] TypeArguments: <List<ArticleInsertion>>
    //     0xbd014c: ldr             x16, [x16, #0x508]
    // 0xbd0150: stp             x2, x16, [SP, #8]
    // 0xbd0154: str             x0, [SP]
    // 0xbd0158: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0158: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd015c: r0 = write()
    //     0xbd015c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0160: ldur            x0, [fp, #-8]
    // 0xbd0164: LoadField: r1 = r0->field_b
    //     0xbd0164: ldur            w1, [x0, #0xb]
    // 0xbd0168: DecompressPointer r1
    //     0xbd0168: add             x1, x1, HEAP, lsl #32
    // 0xbd016c: LoadField: r2 = r1->field_13
    //     0xbd016c: ldur            w2, [x1, #0x13]
    // 0xbd0170: LoadField: r1 = r0->field_13
    //     0xbd0170: ldur            x1, [x0, #0x13]
    // 0xbd0174: r3 = LoadInt32Instr(r2)
    //     0xbd0174: sbfx            x3, x2, #1, #0x1f
    // 0xbd0178: sub             x2, x3, x1
    // 0xbd017c: cmp             x2, #1
    // 0xbd0180: b.ge            #0xbd0190
    // 0xbd0184: mov             x1, x0
    // 0xbd0188: r2 = 1
    //     0xbd0188: movz            x2, #0x1
    // 0xbd018c: r0 = _increaseBufferSize()
    //     0xbd018c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0190: ldur            x2, [fp, #-8]
    // 0xbd0194: ldur            x3, [fp, #-0x10]
    // 0xbd0198: r4 = 13
    //     0xbd0198: movz            x4, #0xd
    // 0xbd019c: LoadField: r5 = r2->field_b
    //     0xbd019c: ldur            w5, [x2, #0xb]
    // 0xbd01a0: DecompressPointer r5
    //     0xbd01a0: add             x5, x5, HEAP, lsl #32
    // 0xbd01a4: LoadField: r6 = r2->field_13
    //     0xbd01a4: ldur            x6, [x2, #0x13]
    // 0xbd01a8: add             x0, x6, #1
    // 0xbd01ac: StoreField: r2->field_13 = r0
    //     0xbd01ac: stur            x0, [x2, #0x13]
    // 0xbd01b0: LoadField: r0 = r5->field_13
    //     0xbd01b0: ldur            w0, [x5, #0x13]
    // 0xbd01b4: r1 = LoadInt32Instr(r0)
    //     0xbd01b4: sbfx            x1, x0, #1, #0x1f
    // 0xbd01b8: mov             x0, x1
    // 0xbd01bc: mov             x1, x6
    // 0xbd01c0: cmp             x1, x0
    // 0xbd01c4: b.hs            #0xbd035c
    // 0xbd01c8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd01c8: add             x0, x5, x6
    //     0xbd01cc: strb            w4, [x0, #0x17]
    // 0xbd01d0: LoadField: r0 = r3->field_3f
    //     0xbd01d0: ldur            w0, [x3, #0x3f]
    // 0xbd01d4: DecompressPointer r0
    //     0xbd01d4: add             x0, x0, HEAP, lsl #32
    // 0xbd01d8: r16 = <List<ArticleInsertion>>
    //     0xbd01d8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b508] TypeArguments: <List<ArticleInsertion>>
    //     0xbd01dc: ldr             x16, [x16, #0x508]
    // 0xbd01e0: stp             x2, x16, [SP, #8]
    // 0xbd01e4: str             x0, [SP]
    // 0xbd01e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd01e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd01ec: r0 = write()
    //     0xbd01ec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd01f0: ldur            x0, [fp, #-8]
    // 0xbd01f4: LoadField: r1 = r0->field_b
    //     0xbd01f4: ldur            w1, [x0, #0xb]
    // 0xbd01f8: DecompressPointer r1
    //     0xbd01f8: add             x1, x1, HEAP, lsl #32
    // 0xbd01fc: LoadField: r2 = r1->field_13
    //     0xbd01fc: ldur            w2, [x1, #0x13]
    // 0xbd0200: LoadField: r1 = r0->field_13
    //     0xbd0200: ldur            x1, [x0, #0x13]
    // 0xbd0204: r3 = LoadInt32Instr(r2)
    //     0xbd0204: sbfx            x3, x2, #1, #0x1f
    // 0xbd0208: sub             x2, x3, x1
    // 0xbd020c: cmp             x2, #1
    // 0xbd0210: b.ge            #0xbd0220
    // 0xbd0214: mov             x1, x0
    // 0xbd0218: r2 = 1
    //     0xbd0218: movz            x2, #0x1
    // 0xbd021c: r0 = _increaseBufferSize()
    //     0xbd021c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0220: ldur            x2, [fp, #-8]
    // 0xbd0224: ldur            x3, [fp, #-0x10]
    // 0xbd0228: r4 = 14
    //     0xbd0228: movz            x4, #0xe
    // 0xbd022c: LoadField: r5 = r2->field_b
    //     0xbd022c: ldur            w5, [x2, #0xb]
    // 0xbd0230: DecompressPointer r5
    //     0xbd0230: add             x5, x5, HEAP, lsl #32
    // 0xbd0234: LoadField: r6 = r2->field_13
    //     0xbd0234: ldur            x6, [x2, #0x13]
    // 0xbd0238: add             x0, x6, #1
    // 0xbd023c: StoreField: r2->field_13 = r0
    //     0xbd023c: stur            x0, [x2, #0x13]
    // 0xbd0240: LoadField: r0 = r5->field_13
    //     0xbd0240: ldur            w0, [x5, #0x13]
    // 0xbd0244: r1 = LoadInt32Instr(r0)
    //     0xbd0244: sbfx            x1, x0, #1, #0x1f
    // 0xbd0248: mov             x0, x1
    // 0xbd024c: mov             x1, x6
    // 0xbd0250: cmp             x1, x0
    // 0xbd0254: b.hs            #0xbd0360
    // 0xbd0258: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0258: add             x0, x5, x6
    //     0xbd025c: strb            w4, [x0, #0x17]
    // 0xbd0260: LoadField: r0 = r3->field_43
    //     0xbd0260: ldur            w0, [x3, #0x43]
    // 0xbd0264: DecompressPointer r0
    //     0xbd0264: add             x0, x0, HEAP, lsl #32
    // 0xbd0268: r16 = <String?>
    //     0xbd0268: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd026c: stp             x2, x16, [SP, #8]
    // 0xbd0270: str             x0, [SP]
    // 0xbd0274: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0274: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0278: r0 = write()
    //     0xbd0278: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd027c: ldur            x0, [fp, #-8]
    // 0xbd0280: LoadField: r1 = r0->field_b
    //     0xbd0280: ldur            w1, [x0, #0xb]
    // 0xbd0284: DecompressPointer r1
    //     0xbd0284: add             x1, x1, HEAP, lsl #32
    // 0xbd0288: LoadField: r2 = r1->field_13
    //     0xbd0288: ldur            w2, [x1, #0x13]
    // 0xbd028c: LoadField: r1 = r0->field_13
    //     0xbd028c: ldur            x1, [x0, #0x13]
    // 0xbd0290: r3 = LoadInt32Instr(r2)
    //     0xbd0290: sbfx            x3, x2, #1, #0x1f
    // 0xbd0294: sub             x2, x3, x1
    // 0xbd0298: cmp             x2, #1
    // 0xbd029c: b.ge            #0xbd02ac
    // 0xbd02a0: mov             x1, x0
    // 0xbd02a4: r2 = 1
    //     0xbd02a4: movz            x2, #0x1
    // 0xbd02a8: r0 = _increaseBufferSize()
    //     0xbd02a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd02ac: ldur            x2, [fp, #-8]
    // 0xbd02b0: ldur            x3, [fp, #-0x10]
    // 0xbd02b4: r4 = 15
    //     0xbd02b4: movz            x4, #0xf
    // 0xbd02b8: LoadField: r5 = r2->field_b
    //     0xbd02b8: ldur            w5, [x2, #0xb]
    // 0xbd02bc: DecompressPointer r5
    //     0xbd02bc: add             x5, x5, HEAP, lsl #32
    // 0xbd02c0: LoadField: r6 = r2->field_13
    //     0xbd02c0: ldur            x6, [x2, #0x13]
    // 0xbd02c4: add             x0, x6, #1
    // 0xbd02c8: StoreField: r2->field_13 = r0
    //     0xbd02c8: stur            x0, [x2, #0x13]
    // 0xbd02cc: LoadField: r0 = r5->field_13
    //     0xbd02cc: ldur            w0, [x5, #0x13]
    // 0xbd02d0: r1 = LoadInt32Instr(r0)
    //     0xbd02d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd02d4: mov             x0, x1
    // 0xbd02d8: mov             x1, x6
    // 0xbd02dc: cmp             x1, x0
    // 0xbd02e0: b.hs            #0xbd0364
    // 0xbd02e4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd02e4: add             x0, x5, x6
    //     0xbd02e8: strb            w4, [x0, #0x17]
    // 0xbd02ec: LoadField: r0 = r3->field_47
    //     0xbd02ec: ldur            w0, [x3, #0x47]
    // 0xbd02f0: DecompressPointer r0
    //     0xbd02f0: add             x0, x0, HEAP, lsl #32
    // 0xbd02f4: r16 = <List<Author>?>
    //     0xbd02f4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b510] TypeArguments: <List<Author>?>
    //     0xbd02f8: ldr             x16, [x16, #0x510]
    // 0xbd02fc: stp             x2, x16, [SP, #8]
    // 0xbd0300: str             x0, [SP]
    // 0xbd0304: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0304: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0308: r0 = write()
    //     0xbd0308: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd030c: r0 = Null
    //     0xbd030c: mov             x0, NULL
    // 0xbd0310: LeaveFrame
    //     0xbd0310: mov             SP, fp
    //     0xbd0314: ldp             fp, lr, [SP], #0x10
    // 0xbd0318: ret
    //     0xbd0318: ret             
    // 0xbd031c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd031c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd0320: b               #0xbcf998
    // 0xbd0324: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0324: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0328: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0328: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd032c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd032c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0330: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0330: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0334: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0334: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0338: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0338: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd033c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd033c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0340: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0340: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0344: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0344: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0348: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0348: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd034c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd034c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0350: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0350: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0354: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0354: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0358: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd035c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd035c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0360: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0364: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbeffdc, size: 0x24
    // 0xbeffdc: r1 = 90
    //     0xbeffdc: movz            x1, #0x5a
    // 0xbeffe0: r16 = LoadInt32Instr(r1)
    //     0xbeffe0: sbfx            x16, x1, #1, #0x1f
    // 0xbeffe4: r17 = 11601
    //     0xbeffe4: movz            x17, #0x2d51
    // 0xbeffe8: mul             x0, x16, x17
    // 0xbeffec: umulh           x16, x16, x17
    // 0xbefff0: eor             x0, x0, x16
    // 0xbefff4: r0 = 0
    //     0xbefff4: eor             x0, x0, x0, lsr #32
    // 0xbefff8: ubfiz           x0, x0, #1, #0x1e
    // 0xbefffc: ret
    //     0xbefffc: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd75fd8, size: 0x9c
    // 0xd75fd8: EnterFrame
    //     0xd75fd8: stp             fp, lr, [SP, #-0x10]!
    //     0xd75fdc: mov             fp, SP
    // 0xd75fe0: AllocStack(0x10)
    //     0xd75fe0: sub             SP, SP, #0x10
    // 0xd75fe4: CheckStackOverflow
    //     0xd75fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd75fe8: cmp             SP, x16
    //     0xd75fec: b.ls            #0xd7606c
    // 0xd75ff0: ldr             x0, [fp, #0x10]
    // 0xd75ff4: cmp             w0, NULL
    // 0xd75ff8: b.ne            #0xd7600c
    // 0xd75ffc: r0 = false
    //     0xd75ffc: add             x0, NULL, #0x30  ; false
    // 0xd76000: LeaveFrame
    //     0xd76000: mov             SP, fp
    //     0xd76004: ldp             fp, lr, [SP], #0x10
    // 0xd76008: ret
    //     0xd76008: ret             
    // 0xd7600c: ldr             x1, [fp, #0x18]
    // 0xd76010: cmp             w1, w0
    // 0xd76014: b.ne            #0xd76020
    // 0xd76018: r0 = true
    //     0xd76018: add             x0, NULL, #0x20  ; true
    // 0xd7601c: b               #0xd76060
    // 0xd76020: r1 = 60
    //     0xd76020: movz            x1, #0x3c
    // 0xd76024: branchIfSmi(r0, 0xd76030)
    //     0xd76024: tbz             w0, #0, #0xd76030
    // 0xd76028: r1 = LoadClassIdInstr(r0)
    //     0xd76028: ldur            x1, [x0, #-1]
    //     0xd7602c: ubfx            x1, x1, #0xc, #0x14
    // 0xd76030: cmp             x1, #0x687
    // 0xd76034: b.ne            #0xd7605c
    // 0xd76038: r16 = ArticleDetailAdapter
    //     0xd76038: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b4d0] Type: ArticleDetailAdapter
    //     0xd7603c: ldr             x16, [x16, #0x4d0]
    // 0xd76040: r30 = ArticleDetailAdapter
    //     0xd76040: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b4d0] Type: ArticleDetailAdapter
    //     0xd76044: ldr             lr, [lr, #0x4d0]
    // 0xd76048: stp             lr, x16, [SP]
    // 0xd7604c: r0 = ==()
    //     0xd7604c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76050: tbnz            w0, #4, #0xd7605c
    // 0xd76054: r0 = true
    //     0xd76054: add             x0, NULL, #0x20  ; true
    // 0xd76058: b               #0xd76060
    // 0xd7605c: r0 = false
    //     0xd7605c: add             x0, NULL, #0x30  ; false
    // 0xd76060: LeaveFrame
    //     0xd76060: mov             SP, fp
    //     0xd76064: ldp             fp, lr, [SP], #0x10
    // 0xd76068: ret
    //     0xd76068: ret             
    // 0xd7606c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7606c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76070: b               #0xd75ff0
  }
}

// class id: 1672, size: 0x14, field offset: 0xc
class ArticleAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5be70, size: 0x504
    // 0xa5be70: EnterFrame
    //     0xa5be70: stp             fp, lr, [SP, #-0x10]!
    //     0xa5be74: mov             fp, SP
    // 0xa5be78: AllocStack(0x68)
    //     0xa5be78: sub             SP, SP, #0x68
    // 0xa5be7c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5be7c: stur            x2, [fp, #-0x20]
    // 0xa5be80: CheckStackOverflow
    //     0xa5be80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5be84: cmp             SP, x16
    //     0xa5be88: b.ls            #0xa5c35c
    // 0xa5be8c: LoadField: r3 = r2->field_23
    //     0xa5be8c: ldur            x3, [x2, #0x23]
    // 0xa5be90: add             x0, x3, #1
    // 0xa5be94: LoadField: r1 = r2->field_1b
    //     0xa5be94: ldur            x1, [x2, #0x1b]
    // 0xa5be98: cmp             x0, x1
    // 0xa5be9c: b.gt            #0xa5c300
    // 0xa5bea0: LoadField: r4 = r2->field_7
    //     0xa5bea0: ldur            w4, [x2, #7]
    // 0xa5bea4: DecompressPointer r4
    //     0xa5bea4: add             x4, x4, HEAP, lsl #32
    // 0xa5bea8: stur            x4, [fp, #-0x18]
    // 0xa5beac: StoreField: r2->field_23 = r0
    //     0xa5beac: stur            x0, [x2, #0x23]
    // 0xa5beb0: LoadField: r0 = r4->field_13
    //     0xa5beb0: ldur            w0, [x4, #0x13]
    // 0xa5beb4: r5 = LoadInt32Instr(r0)
    //     0xa5beb4: sbfx            x5, x0, #1, #0x1f
    // 0xa5beb8: mov             x0, x5
    // 0xa5bebc: mov             x1, x3
    // 0xa5bec0: stur            x5, [fp, #-0x10]
    // 0xa5bec4: cmp             x1, x0
    // 0xa5bec8: b.hs            #0xa5c364
    // 0xa5becc: LoadField: r0 = r4->field_7
    //     0xa5becc: ldur            x0, [x4, #7]
    // 0xa5bed0: ldrb            w1, [x0, x3]
    // 0xa5bed4: stur            x1, [fp, #-8]
    // 0xa5bed8: r16 = <int, dynamic>
    //     0xa5bed8: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5bedc: ldr             x16, [x16, #0xac0]
    // 0xa5bee0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5bee4: stp             lr, x16, [SP]
    // 0xa5bee8: r0 = Map._fromLiteral()
    //     0xa5bee8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5beec: mov             x2, x0
    // 0xa5bef0: stur            x2, [fp, #-0x38]
    // 0xa5bef4: r6 = 0
    //     0xa5bef4: movz            x6, #0
    // 0xa5bef8: ldur            x3, [fp, #-0x20]
    // 0xa5befc: ldur            x4, [fp, #-0x18]
    // 0xa5bf00: ldur            x5, [fp, #-8]
    // 0xa5bf04: stur            x6, [fp, #-0x30]
    // 0xa5bf08: CheckStackOverflow
    //     0xa5bf08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5bf0c: cmp             SP, x16
    //     0xa5bf10: b.ls            #0xa5c368
    // 0xa5bf14: cmp             x6, x5
    // 0xa5bf18: b.ge            #0xa5bfa4
    // 0xa5bf1c: LoadField: r7 = r3->field_23
    //     0xa5bf1c: ldur            x7, [x3, #0x23]
    // 0xa5bf20: add             x0, x7, #1
    // 0xa5bf24: LoadField: r1 = r3->field_1b
    //     0xa5bf24: ldur            x1, [x3, #0x1b]
    // 0xa5bf28: cmp             x0, x1
    // 0xa5bf2c: b.gt            #0xa5c328
    // 0xa5bf30: StoreField: r3->field_23 = r0
    //     0xa5bf30: stur            x0, [x3, #0x23]
    // 0xa5bf34: ldur            x0, [fp, #-0x10]
    // 0xa5bf38: mov             x1, x7
    // 0xa5bf3c: cmp             x1, x0
    // 0xa5bf40: b.hs            #0xa5c370
    // 0xa5bf44: LoadField: r0 = r4->field_7
    //     0xa5bf44: ldur            x0, [x4, #7]
    // 0xa5bf48: ldrb            w8, [x0, x7]
    // 0xa5bf4c: mov             x1, x3
    // 0xa5bf50: stur            x8, [fp, #-0x28]
    // 0xa5bf54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5bf54: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5bf58: r0 = read()
    //     0xa5bf58: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5bf5c: mov             x1, x0
    // 0xa5bf60: ldur            x0, [fp, #-0x28]
    // 0xa5bf64: lsl             x2, x0, #1
    // 0xa5bf68: r16 = LoadInt32Instr(r2)
    //     0xa5bf68: sbfx            x16, x2, #1, #0x1f
    // 0xa5bf6c: r17 = 11601
    //     0xa5bf6c: movz            x17, #0x2d51
    // 0xa5bf70: mul             x0, x16, x17
    // 0xa5bf74: umulh           x16, x16, x17
    // 0xa5bf78: eor             x0, x0, x16
    // 0xa5bf7c: r0 = 0
    //     0xa5bf7c: eor             x0, x0, x0, lsr #32
    // 0xa5bf80: ubfiz           x0, x0, #1, #0x1e
    // 0xa5bf84: r5 = LoadInt32Instr(r0)
    //     0xa5bf84: sbfx            x5, x0, #1, #0x1f
    // 0xa5bf88: mov             x3, x1
    // 0xa5bf8c: ldur            x1, [fp, #-0x38]
    // 0xa5bf90: r0 = _set()
    //     0xa5bf90: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5bf94: ldur            x0, [fp, #-0x30]
    // 0xa5bf98: add             x6, x0, #1
    // 0xa5bf9c: ldur            x2, [fp, #-0x38]
    // 0xa5bfa0: b               #0xa5bef8
    // 0xa5bfa4: mov             x0, x2
    // 0xa5bfa8: mov             x1, x0
    // 0xa5bfac: r2 = 0
    //     0xa5bfac: movz            x2, #0
    // 0xa5bfb0: r0 = _getValueOrData()
    //     0xa5bfb0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5bfb4: ldur            x3, [fp, #-0x38]
    // 0xa5bfb8: LoadField: r1 = r3->field_f
    //     0xa5bfb8: ldur            w1, [x3, #0xf]
    // 0xa5bfbc: DecompressPointer r1
    //     0xa5bfbc: add             x1, x1, HEAP, lsl #32
    // 0xa5bfc0: cmp             w1, w0
    // 0xa5bfc4: b.ne            #0xa5bfd0
    // 0xa5bfc8: r4 = Null
    //     0xa5bfc8: mov             x4, NULL
    // 0xa5bfcc: b               #0xa5bfd4
    // 0xa5bfd0: mov             x4, x0
    // 0xa5bfd4: mov             x0, x4
    // 0xa5bfd8: stur            x4, [fp, #-0x18]
    // 0xa5bfdc: r2 = Null
    //     0xa5bfdc: mov             x2, NULL
    // 0xa5bfe0: r1 = Null
    //     0xa5bfe0: mov             x1, NULL
    // 0xa5bfe4: branchIfSmi(r0, 0xa5c00c)
    //     0xa5bfe4: tbz             w0, #0, #0xa5c00c
    // 0xa5bfe8: r4 = LoadClassIdInstr(r0)
    //     0xa5bfe8: ldur            x4, [x0, #-1]
    //     0xa5bfec: ubfx            x4, x4, #0xc, #0x14
    // 0xa5bff0: sub             x4, x4, #0x3c
    // 0xa5bff4: cmp             x4, #1
    // 0xa5bff8: b.ls            #0xa5c00c
    // 0xa5bffc: r8 = int
    //     0xa5bffc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5c000: r3 = Null
    //     0xa5c000: add             x3, PP, #0x21, lsl #12  ; [pp+0x21900] Null
    //     0xa5c004: ldr             x3, [x3, #0x900]
    // 0xa5c008: r0 = int()
    //     0xa5c008: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5c00c: ldur            x1, [fp, #-0x38]
    // 0xa5c010: r2 = 2
    //     0xa5c010: movz            x2, #0x2
    // 0xa5c014: r0 = _getValueOrData()
    //     0xa5c014: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c018: ldur            x3, [fp, #-0x38]
    // 0xa5c01c: LoadField: r1 = r3->field_f
    //     0xa5c01c: ldur            w1, [x3, #0xf]
    // 0xa5c020: DecompressPointer r1
    //     0xa5c020: add             x1, x1, HEAP, lsl #32
    // 0xa5c024: cmp             w1, w0
    // 0xa5c028: b.ne            #0xa5c034
    // 0xa5c02c: r4 = Null
    //     0xa5c02c: mov             x4, NULL
    // 0xa5c030: b               #0xa5c038
    // 0xa5c034: mov             x4, x0
    // 0xa5c038: mov             x0, x4
    // 0xa5c03c: stur            x4, [fp, #-0x20]
    // 0xa5c040: r2 = Null
    //     0xa5c040: mov             x2, NULL
    // 0xa5c044: r1 = Null
    //     0xa5c044: mov             x1, NULL
    // 0xa5c048: r4 = 60
    //     0xa5c048: movz            x4, #0x3c
    // 0xa5c04c: branchIfSmi(r0, 0xa5c058)
    //     0xa5c04c: tbz             w0, #0, #0xa5c058
    // 0xa5c050: r4 = LoadClassIdInstr(r0)
    //     0xa5c050: ldur            x4, [x0, #-1]
    //     0xa5c054: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c058: sub             x4, x4, #0x5e
    // 0xa5c05c: cmp             x4, #1
    // 0xa5c060: b.ls            #0xa5c074
    // 0xa5c064: r8 = String
    //     0xa5c064: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5c068: r3 = Null
    //     0xa5c068: add             x3, PP, #0x21, lsl #12  ; [pp+0x21910] Null
    //     0xa5c06c: ldr             x3, [x3, #0x910]
    // 0xa5c070: r0 = String()
    //     0xa5c070: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5c074: ldur            x1, [fp, #-0x38]
    // 0xa5c078: r2 = 6
    //     0xa5c078: movz            x2, #0x6
    // 0xa5c07c: r0 = _getValueOrData()
    //     0xa5c07c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c080: ldur            x3, [fp, #-0x38]
    // 0xa5c084: LoadField: r1 = r3->field_f
    //     0xa5c084: ldur            w1, [x3, #0xf]
    // 0xa5c088: DecompressPointer r1
    //     0xa5c088: add             x1, x1, HEAP, lsl #32
    // 0xa5c08c: cmp             w1, w0
    // 0xa5c090: b.ne            #0xa5c09c
    // 0xa5c094: r4 = Null
    //     0xa5c094: mov             x4, NULL
    // 0xa5c098: b               #0xa5c0a0
    // 0xa5c09c: mov             x4, x0
    // 0xa5c0a0: mov             x0, x4
    // 0xa5c0a4: stur            x4, [fp, #-0x40]
    // 0xa5c0a8: r2 = Null
    //     0xa5c0a8: mov             x2, NULL
    // 0xa5c0ac: r1 = Null
    //     0xa5c0ac: mov             x1, NULL
    // 0xa5c0b0: r4 = 60
    //     0xa5c0b0: movz            x4, #0x3c
    // 0xa5c0b4: branchIfSmi(r0, 0xa5c0c0)
    //     0xa5c0b4: tbz             w0, #0, #0xa5c0c0
    // 0xa5c0b8: r4 = LoadClassIdInstr(r0)
    //     0xa5c0b8: ldur            x4, [x0, #-1]
    //     0xa5c0bc: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c0c0: r17 = 5582
    //     0xa5c0c0: movz            x17, #0x15ce
    // 0xa5c0c4: cmp             x4, x17
    // 0xa5c0c8: b.eq            #0xa5c0e0
    // 0xa5c0cc: r8 = Image
    //     0xa5c0cc: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b560] Type: Image
    //     0xa5c0d0: ldr             x8, [x8, #0x560]
    // 0xa5c0d4: r3 = Null
    //     0xa5c0d4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21920] Null
    //     0xa5c0d8: ldr             x3, [x3, #0x920]
    // 0xa5c0dc: r0 = Image()
    //     0xa5c0dc: bl              #0x72c934  ; IsType_Image_Stub
    // 0xa5c0e0: ldur            x1, [fp, #-0x38]
    // 0xa5c0e4: r2 = 8
    //     0xa5c0e4: movz            x2, #0x8
    // 0xa5c0e8: r0 = _getValueOrData()
    //     0xa5c0e8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c0ec: ldur            x3, [fp, #-0x38]
    // 0xa5c0f0: LoadField: r1 = r3->field_f
    //     0xa5c0f0: ldur            w1, [x3, #0xf]
    // 0xa5c0f4: DecompressPointer r1
    //     0xa5c0f4: add             x1, x1, HEAP, lsl #32
    // 0xa5c0f8: cmp             w1, w0
    // 0xa5c0fc: b.ne            #0xa5c108
    // 0xa5c100: r4 = Null
    //     0xa5c100: mov             x4, NULL
    // 0xa5c104: b               #0xa5c10c
    // 0xa5c108: mov             x4, x0
    // 0xa5c10c: mov             x0, x4
    // 0xa5c110: stur            x4, [fp, #-0x48]
    // 0xa5c114: r2 = Null
    //     0xa5c114: mov             x2, NULL
    // 0xa5c118: r1 = Null
    //     0xa5c118: mov             x1, NULL
    // 0xa5c11c: r4 = 60
    //     0xa5c11c: movz            x4, #0x3c
    // 0xa5c120: branchIfSmi(r0, 0xa5c12c)
    //     0xa5c120: tbz             w0, #0, #0xa5c12c
    // 0xa5c124: r4 = LoadClassIdInstr(r0)
    //     0xa5c124: ldur            x4, [x0, #-1]
    //     0xa5c128: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c12c: cmp             x4, #0x649
    // 0xa5c130: b.eq            #0xa5c148
    // 0xa5c134: r8 = Category
    //     0xa5c134: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b498] Type: Category
    //     0xa5c138: ldr             x8, [x8, #0x498]
    // 0xa5c13c: r3 = Null
    //     0xa5c13c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21930] Null
    //     0xa5c140: ldr             x3, [x3, #0x930]
    // 0xa5c144: r0 = Category()
    //     0xa5c144: bl              #0x80d594  ; IsType_Category_Stub
    // 0xa5c148: ldur            x1, [fp, #-0x38]
    // 0xa5c14c: r2 = 10
    //     0xa5c14c: movz            x2, #0xa
    // 0xa5c150: r0 = _getValueOrData()
    //     0xa5c150: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c154: ldur            x3, [fp, #-0x38]
    // 0xa5c158: LoadField: r1 = r3->field_f
    //     0xa5c158: ldur            w1, [x3, #0xf]
    // 0xa5c15c: DecompressPointer r1
    //     0xa5c15c: add             x1, x1, HEAP, lsl #32
    // 0xa5c160: cmp             w1, w0
    // 0xa5c164: b.ne            #0xa5c170
    // 0xa5c168: r4 = Null
    //     0xa5c168: mov             x4, NULL
    // 0xa5c16c: b               #0xa5c174
    // 0xa5c170: mov             x4, x0
    // 0xa5c174: mov             x0, x4
    // 0xa5c178: stur            x4, [fp, #-0x50]
    // 0xa5c17c: r2 = Null
    //     0xa5c17c: mov             x2, NULL
    // 0xa5c180: r1 = Null
    //     0xa5c180: mov             x1, NULL
    // 0xa5c184: r4 = 60
    //     0xa5c184: movz            x4, #0x3c
    // 0xa5c188: branchIfSmi(r0, 0xa5c194)
    //     0xa5c188: tbz             w0, #0, #0xa5c194
    // 0xa5c18c: r4 = LoadClassIdInstr(r0)
    //     0xa5c18c: ldur            x4, [x0, #-1]
    //     0xa5c190: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c194: sub             x4, x4, #0x5e
    // 0xa5c198: cmp             x4, #1
    // 0xa5c19c: b.ls            #0xa5c1b0
    // 0xa5c1a0: r8 = String
    //     0xa5c1a0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5c1a4: r3 = Null
    //     0xa5c1a4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21940] Null
    //     0xa5c1a8: ldr             x3, [x3, #0x940]
    // 0xa5c1ac: r0 = String()
    //     0xa5c1ac: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5c1b0: ldur            x1, [fp, #-0x38]
    // 0xa5c1b4: r2 = 4
    //     0xa5c1b4: movz            x2, #0x4
    // 0xa5c1b8: r0 = _getValueOrData()
    //     0xa5c1b8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c1bc: ldur            x3, [fp, #-0x38]
    // 0xa5c1c0: LoadField: r1 = r3->field_f
    //     0xa5c1c0: ldur            w1, [x3, #0xf]
    // 0xa5c1c4: DecompressPointer r1
    //     0xa5c1c4: add             x1, x1, HEAP, lsl #32
    // 0xa5c1c8: cmp             w1, w0
    // 0xa5c1cc: b.ne            #0xa5c1d8
    // 0xa5c1d0: r4 = Null
    //     0xa5c1d0: mov             x4, NULL
    // 0xa5c1d4: b               #0xa5c1dc
    // 0xa5c1d8: mov             x4, x0
    // 0xa5c1dc: mov             x0, x4
    // 0xa5c1e0: stur            x4, [fp, #-0x58]
    // 0xa5c1e4: r2 = Null
    //     0xa5c1e4: mov             x2, NULL
    // 0xa5c1e8: r1 = Null
    //     0xa5c1e8: mov             x1, NULL
    // 0xa5c1ec: r4 = 60
    //     0xa5c1ec: movz            x4, #0x3c
    // 0xa5c1f0: branchIfSmi(r0, 0xa5c1fc)
    //     0xa5c1f0: tbz             w0, #0, #0xa5c1fc
    // 0xa5c1f4: r4 = LoadClassIdInstr(r0)
    //     0xa5c1f4: ldur            x4, [x0, #-1]
    //     0xa5c1f8: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c1fc: sub             x4, x4, #0x5e
    // 0xa5c200: cmp             x4, #1
    // 0xa5c204: b.ls            #0xa5c218
    // 0xa5c208: r8 = String?
    //     0xa5c208: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5c20c: r3 = Null
    //     0xa5c20c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21950] Null
    //     0xa5c210: ldr             x3, [x3, #0x950]
    // 0xa5c214: r0 = String?()
    //     0xa5c214: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5c218: ldur            x1, [fp, #-0x38]
    // 0xa5c21c: r2 = 12
    //     0xa5c21c: movz            x2, #0xc
    // 0xa5c220: r0 = _getValueOrData()
    //     0xa5c220: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5c224: mov             x1, x0
    // 0xa5c228: ldur            x0, [fp, #-0x38]
    // 0xa5c22c: LoadField: r2 = r0->field_f
    //     0xa5c22c: ldur            w2, [x0, #0xf]
    // 0xa5c230: DecompressPointer r2
    //     0xa5c230: add             x2, x2, HEAP, lsl #32
    // 0xa5c234: cmp             w2, w1
    // 0xa5c238: b.ne            #0xa5c244
    // 0xa5c23c: r9 = Null
    //     0xa5c23c: mov             x9, NULL
    // 0xa5c240: b               #0xa5c248
    // 0xa5c244: mov             x9, x1
    // 0xa5c248: ldur            x8, [fp, #-0x18]
    // 0xa5c24c: ldur            x7, [fp, #-0x20]
    // 0xa5c250: ldur            x6, [fp, #-0x40]
    // 0xa5c254: ldur            x5, [fp, #-0x48]
    // 0xa5c258: ldur            x4, [fp, #-0x50]
    // 0xa5c25c: ldur            x3, [fp, #-0x58]
    // 0xa5c260: mov             x0, x9
    // 0xa5c264: stur            x9, [fp, #-0x38]
    // 0xa5c268: r2 = Null
    //     0xa5c268: mov             x2, NULL
    // 0xa5c26c: r1 = Null
    //     0xa5c26c: mov             x1, NULL
    // 0xa5c270: r4 = 60
    //     0xa5c270: movz            x4, #0x3c
    // 0xa5c274: branchIfSmi(r0, 0xa5c280)
    //     0xa5c274: tbz             w0, #0, #0xa5c280
    // 0xa5c278: r4 = LoadClassIdInstr(r0)
    //     0xa5c278: ldur            x4, [x0, #-1]
    //     0xa5c27c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5c280: sub             x4, x4, #0x5e
    // 0xa5c284: cmp             x4, #1
    // 0xa5c288: b.ls            #0xa5c29c
    // 0xa5c28c: r8 = String?
    //     0xa5c28c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5c290: r3 = Null
    //     0xa5c290: add             x3, PP, #0x21, lsl #12  ; [pp+0x21960] Null
    //     0xa5c294: ldr             x3, [x3, #0x960]
    // 0xa5c298: r0 = String?()
    //     0xa5c298: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5c29c: ldur            x0, [fp, #-0x18]
    // 0xa5c2a0: r1 = LoadInt32Instr(r0)
    //     0xa5c2a0: sbfx            x1, x0, #1, #0x1f
    //     0xa5c2a4: tbz             w0, #0, #0xa5c2ac
    //     0xa5c2a8: ldur            x1, [x0, #7]
    // 0xa5c2ac: stur            x1, [fp, #-8]
    // 0xa5c2b0: r0 = Article()
    //     0xa5c2b0: bl              #0x8ecdfc  ; AllocateArticleStub -> Article (size=0x28)
    // 0xa5c2b4: mov             x1, x0
    // 0xa5c2b8: ldur            x0, [fp, #-8]
    // 0xa5c2bc: StoreField: r1->field_7 = r0
    //     0xa5c2bc: stur            x0, [x1, #7]
    // 0xa5c2c0: ldur            x0, [fp, #-0x20]
    // 0xa5c2c4: StoreField: r1->field_f = r0
    //     0xa5c2c4: stur            w0, [x1, #0xf]
    // 0xa5c2c8: ldur            x0, [fp, #-0x40]
    // 0xa5c2cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5c2cc: stur            w0, [x1, #0x17]
    // 0xa5c2d0: ldur            x0, [fp, #-0x48]
    // 0xa5c2d4: StoreField: r1->field_1b = r0
    //     0xa5c2d4: stur            w0, [x1, #0x1b]
    // 0xa5c2d8: ldur            x0, [fp, #-0x50]
    // 0xa5c2dc: StoreField: r1->field_1f = r0
    //     0xa5c2dc: stur            w0, [x1, #0x1f]
    // 0xa5c2e0: ldur            x0, [fp, #-0x58]
    // 0xa5c2e4: StoreField: r1->field_13 = r0
    //     0xa5c2e4: stur            w0, [x1, #0x13]
    // 0xa5c2e8: ldur            x0, [fp, #-0x38]
    // 0xa5c2ec: StoreField: r1->field_23 = r0
    //     0xa5c2ec: stur            w0, [x1, #0x23]
    // 0xa5c2f0: mov             x0, x1
    // 0xa5c2f4: LeaveFrame
    //     0xa5c2f4: mov             SP, fp
    //     0xa5c2f8: ldp             fp, lr, [SP], #0x10
    // 0xa5c2fc: ret
    //     0xa5c2fc: ret             
    // 0xa5c300: r0 = RangeError()
    //     0xa5c300: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5c304: mov             x1, x0
    // 0xa5c308: r0 = "Not enough bytes available."
    //     0xa5c308: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5c30c: ldr             x0, [x0, #0x8a8]
    // 0xa5c310: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5c310: stur            w0, [x1, #0x17]
    // 0xa5c314: r2 = false
    //     0xa5c314: add             x2, NULL, #0x30  ; false
    // 0xa5c318: StoreField: r1->field_b = r2
    //     0xa5c318: stur            w2, [x1, #0xb]
    // 0xa5c31c: mov             x0, x1
    // 0xa5c320: r0 = Throw()
    //     0xa5c320: bl              #0xec04b8  ; ThrowStub
    // 0xa5c324: brk             #0
    // 0xa5c328: r0 = "Not enough bytes available."
    //     0xa5c328: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5c32c: ldr             x0, [x0, #0x8a8]
    // 0xa5c330: r2 = false
    //     0xa5c330: add             x2, NULL, #0x30  ; false
    // 0xa5c334: r0 = RangeError()
    //     0xa5c334: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5c338: mov             x1, x0
    // 0xa5c33c: r0 = "Not enough bytes available."
    //     0xa5c33c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5c340: ldr             x0, [x0, #0x8a8]
    // 0xa5c344: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5c344: stur            w0, [x1, #0x17]
    // 0xa5c348: r0 = false
    //     0xa5c348: add             x0, NULL, #0x30  ; false
    // 0xa5c34c: StoreField: r1->field_b = r0
    //     0xa5c34c: stur            w0, [x1, #0xb]
    // 0xa5c350: mov             x0, x1
    // 0xa5c354: r0 = Throw()
    //     0xa5c354: bl              #0xec04b8  ; ThrowStub
    // 0xa5c358: brk             #0
    // 0xa5c35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5c35c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5c360: b               #0xa5be8c
    // 0xa5c364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5c364: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5c368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5c368: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5c36c: b               #0xa5bf14
    // 0xa5c370: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5c370: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbcf4a8, size: 0x4cc
    // 0xbcf4a8: EnterFrame
    //     0xbcf4a8: stp             fp, lr, [SP, #-0x10]!
    //     0xbcf4ac: mov             fp, SP
    // 0xbcf4b0: AllocStack(0x28)
    //     0xbcf4b0: sub             SP, SP, #0x28
    // 0xbcf4b4: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbcf4b4: mov             x4, x2
    //     0xbcf4b8: stur            x2, [fp, #-8]
    //     0xbcf4bc: stur            x3, [fp, #-0x10]
    // 0xbcf4c0: CheckStackOverflow
    //     0xbcf4c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbcf4c4: cmp             SP, x16
    //     0xbcf4c8: b.ls            #0xbcf94c
    // 0xbcf4cc: mov             x0, x3
    // 0xbcf4d0: r2 = Null
    //     0xbcf4d0: mov             x2, NULL
    // 0xbcf4d4: r1 = Null
    //     0xbcf4d4: mov             x1, NULL
    // 0xbcf4d8: r4 = 60
    //     0xbcf4d8: movz            x4, #0x3c
    // 0xbcf4dc: branchIfSmi(r0, 0xbcf4e8)
    //     0xbcf4dc: tbz             w0, #0, #0xbcf4e8
    // 0xbcf4e0: r4 = LoadClassIdInstr(r0)
    //     0xbcf4e0: ldur            x4, [x0, #-1]
    //     0xbcf4e4: ubfx            x4, x4, #0xc, #0x14
    // 0xbcf4e8: r17 = 5599
    //     0xbcf4e8: movz            x17, #0x15df
    // 0xbcf4ec: cmp             x4, x17
    // 0xbcf4f0: b.eq            #0xbcf508
    // 0xbcf4f4: r8 = Article
    //     0xbcf4f4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b540] Type: Article
    //     0xbcf4f8: ldr             x8, [x8, #0x540]
    // 0xbcf4fc: r3 = Null
    //     0xbcf4fc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b548] Null
    //     0xbcf500: ldr             x3, [x3, #0x548]
    // 0xbcf504: r0 = Article()
    //     0xbcf504: bl              #0x6fb0e8  ; IsType_Article_Stub
    // 0xbcf508: ldur            x0, [fp, #-8]
    // 0xbcf50c: LoadField: r1 = r0->field_b
    //     0xbcf50c: ldur            w1, [x0, #0xb]
    // 0xbcf510: DecompressPointer r1
    //     0xbcf510: add             x1, x1, HEAP, lsl #32
    // 0xbcf514: LoadField: r2 = r1->field_13
    //     0xbcf514: ldur            w2, [x1, #0x13]
    // 0xbcf518: LoadField: r1 = r0->field_13
    //     0xbcf518: ldur            x1, [x0, #0x13]
    // 0xbcf51c: r3 = LoadInt32Instr(r2)
    //     0xbcf51c: sbfx            x3, x2, #1, #0x1f
    // 0xbcf520: sub             x2, x3, x1
    // 0xbcf524: cmp             x2, #1
    // 0xbcf528: b.ge            #0xbcf538
    // 0xbcf52c: mov             x1, x0
    // 0xbcf530: r2 = 1
    //     0xbcf530: movz            x2, #0x1
    // 0xbcf534: r0 = _increaseBufferSize()
    //     0xbcf534: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf538: ldur            x3, [fp, #-8]
    // 0xbcf53c: r2 = 7
    //     0xbcf53c: movz            x2, #0x7
    // 0xbcf540: LoadField: r4 = r3->field_b
    //     0xbcf540: ldur            w4, [x3, #0xb]
    // 0xbcf544: DecompressPointer r4
    //     0xbcf544: add             x4, x4, HEAP, lsl #32
    // 0xbcf548: LoadField: r5 = r3->field_13
    //     0xbcf548: ldur            x5, [x3, #0x13]
    // 0xbcf54c: add             x6, x5, #1
    // 0xbcf550: StoreField: r3->field_13 = r6
    //     0xbcf550: stur            x6, [x3, #0x13]
    // 0xbcf554: LoadField: r0 = r4->field_13
    //     0xbcf554: ldur            w0, [x4, #0x13]
    // 0xbcf558: r7 = LoadInt32Instr(r0)
    //     0xbcf558: sbfx            x7, x0, #1, #0x1f
    // 0xbcf55c: mov             x0, x7
    // 0xbcf560: mov             x1, x5
    // 0xbcf564: cmp             x1, x0
    // 0xbcf568: b.hs            #0xbcf954
    // 0xbcf56c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbcf56c: add             x0, x4, x5
    //     0xbcf570: strb            w2, [x0, #0x17]
    // 0xbcf574: sub             x0, x7, x6
    // 0xbcf578: cmp             x0, #1
    // 0xbcf57c: b.ge            #0xbcf58c
    // 0xbcf580: mov             x1, x3
    // 0xbcf584: r2 = 1
    //     0xbcf584: movz            x2, #0x1
    // 0xbcf588: r0 = _increaseBufferSize()
    //     0xbcf588: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf58c: ldur            x2, [fp, #-8]
    // 0xbcf590: ldur            x3, [fp, #-0x10]
    // 0xbcf594: LoadField: r4 = r2->field_b
    //     0xbcf594: ldur            w4, [x2, #0xb]
    // 0xbcf598: DecompressPointer r4
    //     0xbcf598: add             x4, x4, HEAP, lsl #32
    // 0xbcf59c: LoadField: r5 = r2->field_13
    //     0xbcf59c: ldur            x5, [x2, #0x13]
    // 0xbcf5a0: add             x0, x5, #1
    // 0xbcf5a4: StoreField: r2->field_13 = r0
    //     0xbcf5a4: stur            x0, [x2, #0x13]
    // 0xbcf5a8: LoadField: r0 = r4->field_13
    //     0xbcf5a8: ldur            w0, [x4, #0x13]
    // 0xbcf5ac: r1 = LoadInt32Instr(r0)
    //     0xbcf5ac: sbfx            x1, x0, #1, #0x1f
    // 0xbcf5b0: mov             x0, x1
    // 0xbcf5b4: mov             x1, x5
    // 0xbcf5b8: cmp             x1, x0
    // 0xbcf5bc: b.hs            #0xbcf958
    // 0xbcf5c0: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbcf5c0: add             x0, x4, x5
    //     0xbcf5c4: strb            wzr, [x0, #0x17]
    // 0xbcf5c8: LoadField: r4 = r3->field_7
    //     0xbcf5c8: ldur            x4, [x3, #7]
    // 0xbcf5cc: r0 = BoxInt64Instr(r4)
    //     0xbcf5cc: sbfiz           x0, x4, #1, #0x1f
    //     0xbcf5d0: cmp             x4, x0, asr #1
    //     0xbcf5d4: b.eq            #0xbcf5e0
    //     0xbcf5d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbcf5dc: stur            x4, [x0, #7]
    // 0xbcf5e0: r16 = <int>
    //     0xbcf5e0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbcf5e4: stp             x2, x16, [SP, #8]
    // 0xbcf5e8: str             x0, [SP]
    // 0xbcf5ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf5ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf5f0: r0 = write()
    //     0xbcf5f0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf5f4: ldur            x0, [fp, #-8]
    // 0xbcf5f8: LoadField: r1 = r0->field_b
    //     0xbcf5f8: ldur            w1, [x0, #0xb]
    // 0xbcf5fc: DecompressPointer r1
    //     0xbcf5fc: add             x1, x1, HEAP, lsl #32
    // 0xbcf600: LoadField: r2 = r1->field_13
    //     0xbcf600: ldur            w2, [x1, #0x13]
    // 0xbcf604: LoadField: r1 = r0->field_13
    //     0xbcf604: ldur            x1, [x0, #0x13]
    // 0xbcf608: r3 = LoadInt32Instr(r2)
    //     0xbcf608: sbfx            x3, x2, #1, #0x1f
    // 0xbcf60c: sub             x2, x3, x1
    // 0xbcf610: cmp             x2, #1
    // 0xbcf614: b.ge            #0xbcf624
    // 0xbcf618: mov             x1, x0
    // 0xbcf61c: r2 = 1
    //     0xbcf61c: movz            x2, #0x1
    // 0xbcf620: r0 = _increaseBufferSize()
    //     0xbcf620: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf624: ldur            x2, [fp, #-8]
    // 0xbcf628: ldur            x3, [fp, #-0x10]
    // 0xbcf62c: r4 = 1
    //     0xbcf62c: movz            x4, #0x1
    // 0xbcf630: LoadField: r5 = r2->field_b
    //     0xbcf630: ldur            w5, [x2, #0xb]
    // 0xbcf634: DecompressPointer r5
    //     0xbcf634: add             x5, x5, HEAP, lsl #32
    // 0xbcf638: LoadField: r6 = r2->field_13
    //     0xbcf638: ldur            x6, [x2, #0x13]
    // 0xbcf63c: add             x0, x6, #1
    // 0xbcf640: StoreField: r2->field_13 = r0
    //     0xbcf640: stur            x0, [x2, #0x13]
    // 0xbcf644: LoadField: r0 = r5->field_13
    //     0xbcf644: ldur            w0, [x5, #0x13]
    // 0xbcf648: r1 = LoadInt32Instr(r0)
    //     0xbcf648: sbfx            x1, x0, #1, #0x1f
    // 0xbcf64c: mov             x0, x1
    // 0xbcf650: mov             x1, x6
    // 0xbcf654: cmp             x1, x0
    // 0xbcf658: b.hs            #0xbcf95c
    // 0xbcf65c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf65c: add             x0, x5, x6
    //     0xbcf660: strb            w4, [x0, #0x17]
    // 0xbcf664: LoadField: r0 = r3->field_f
    //     0xbcf664: ldur            w0, [x3, #0xf]
    // 0xbcf668: DecompressPointer r0
    //     0xbcf668: add             x0, x0, HEAP, lsl #32
    // 0xbcf66c: r16 = <String>
    //     0xbcf66c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcf670: stp             x2, x16, [SP, #8]
    // 0xbcf674: str             x0, [SP]
    // 0xbcf678: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf678: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf67c: r0 = write()
    //     0xbcf67c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf680: ldur            x0, [fp, #-8]
    // 0xbcf684: LoadField: r1 = r0->field_b
    //     0xbcf684: ldur            w1, [x0, #0xb]
    // 0xbcf688: DecompressPointer r1
    //     0xbcf688: add             x1, x1, HEAP, lsl #32
    // 0xbcf68c: LoadField: r2 = r1->field_13
    //     0xbcf68c: ldur            w2, [x1, #0x13]
    // 0xbcf690: LoadField: r1 = r0->field_13
    //     0xbcf690: ldur            x1, [x0, #0x13]
    // 0xbcf694: r3 = LoadInt32Instr(r2)
    //     0xbcf694: sbfx            x3, x2, #1, #0x1f
    // 0xbcf698: sub             x2, x3, x1
    // 0xbcf69c: cmp             x2, #1
    // 0xbcf6a0: b.ge            #0xbcf6b0
    // 0xbcf6a4: mov             x1, x0
    // 0xbcf6a8: r2 = 1
    //     0xbcf6a8: movz            x2, #0x1
    // 0xbcf6ac: r0 = _increaseBufferSize()
    //     0xbcf6ac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf6b0: ldur            x2, [fp, #-8]
    // 0xbcf6b4: ldur            x3, [fp, #-0x10]
    // 0xbcf6b8: r4 = 2
    //     0xbcf6b8: movz            x4, #0x2
    // 0xbcf6bc: LoadField: r5 = r2->field_b
    //     0xbcf6bc: ldur            w5, [x2, #0xb]
    // 0xbcf6c0: DecompressPointer r5
    //     0xbcf6c0: add             x5, x5, HEAP, lsl #32
    // 0xbcf6c4: LoadField: r6 = r2->field_13
    //     0xbcf6c4: ldur            x6, [x2, #0x13]
    // 0xbcf6c8: add             x0, x6, #1
    // 0xbcf6cc: StoreField: r2->field_13 = r0
    //     0xbcf6cc: stur            x0, [x2, #0x13]
    // 0xbcf6d0: LoadField: r0 = r5->field_13
    //     0xbcf6d0: ldur            w0, [x5, #0x13]
    // 0xbcf6d4: r1 = LoadInt32Instr(r0)
    //     0xbcf6d4: sbfx            x1, x0, #1, #0x1f
    // 0xbcf6d8: mov             x0, x1
    // 0xbcf6dc: mov             x1, x6
    // 0xbcf6e0: cmp             x1, x0
    // 0xbcf6e4: b.hs            #0xbcf960
    // 0xbcf6e8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf6e8: add             x0, x5, x6
    //     0xbcf6ec: strb            w4, [x0, #0x17]
    // 0xbcf6f0: LoadField: r0 = r3->field_13
    //     0xbcf6f0: ldur            w0, [x3, #0x13]
    // 0xbcf6f4: DecompressPointer r0
    //     0xbcf6f4: add             x0, x0, HEAP, lsl #32
    // 0xbcf6f8: r16 = <String?>
    //     0xbcf6f8: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbcf6fc: stp             x2, x16, [SP, #8]
    // 0xbcf700: str             x0, [SP]
    // 0xbcf704: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf704: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf708: r0 = write()
    //     0xbcf708: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf70c: ldur            x0, [fp, #-8]
    // 0xbcf710: LoadField: r1 = r0->field_b
    //     0xbcf710: ldur            w1, [x0, #0xb]
    // 0xbcf714: DecompressPointer r1
    //     0xbcf714: add             x1, x1, HEAP, lsl #32
    // 0xbcf718: LoadField: r2 = r1->field_13
    //     0xbcf718: ldur            w2, [x1, #0x13]
    // 0xbcf71c: LoadField: r1 = r0->field_13
    //     0xbcf71c: ldur            x1, [x0, #0x13]
    // 0xbcf720: r3 = LoadInt32Instr(r2)
    //     0xbcf720: sbfx            x3, x2, #1, #0x1f
    // 0xbcf724: sub             x2, x3, x1
    // 0xbcf728: cmp             x2, #1
    // 0xbcf72c: b.ge            #0xbcf73c
    // 0xbcf730: mov             x1, x0
    // 0xbcf734: r2 = 1
    //     0xbcf734: movz            x2, #0x1
    // 0xbcf738: r0 = _increaseBufferSize()
    //     0xbcf738: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf73c: ldur            x2, [fp, #-8]
    // 0xbcf740: ldur            x3, [fp, #-0x10]
    // 0xbcf744: r4 = 3
    //     0xbcf744: movz            x4, #0x3
    // 0xbcf748: LoadField: r5 = r2->field_b
    //     0xbcf748: ldur            w5, [x2, #0xb]
    // 0xbcf74c: DecompressPointer r5
    //     0xbcf74c: add             x5, x5, HEAP, lsl #32
    // 0xbcf750: LoadField: r6 = r2->field_13
    //     0xbcf750: ldur            x6, [x2, #0x13]
    // 0xbcf754: add             x0, x6, #1
    // 0xbcf758: StoreField: r2->field_13 = r0
    //     0xbcf758: stur            x0, [x2, #0x13]
    // 0xbcf75c: LoadField: r0 = r5->field_13
    //     0xbcf75c: ldur            w0, [x5, #0x13]
    // 0xbcf760: r1 = LoadInt32Instr(r0)
    //     0xbcf760: sbfx            x1, x0, #1, #0x1f
    // 0xbcf764: mov             x0, x1
    // 0xbcf768: mov             x1, x6
    // 0xbcf76c: cmp             x1, x0
    // 0xbcf770: b.hs            #0xbcf964
    // 0xbcf774: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf774: add             x0, x5, x6
    //     0xbcf778: strb            w4, [x0, #0x17]
    // 0xbcf77c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbcf77c: ldur            w0, [x3, #0x17]
    // 0xbcf780: DecompressPointer r0
    //     0xbcf780: add             x0, x0, HEAP, lsl #32
    // 0xbcf784: r16 = <Image>
    //     0xbcf784: ldr             x16, [PP, #0x7b60]  ; [pp+0x7b60] TypeArguments: <Image>
    // 0xbcf788: stp             x2, x16, [SP, #8]
    // 0xbcf78c: str             x0, [SP]
    // 0xbcf790: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf790: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf794: r0 = write()
    //     0xbcf794: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf798: ldur            x0, [fp, #-8]
    // 0xbcf79c: LoadField: r1 = r0->field_b
    //     0xbcf79c: ldur            w1, [x0, #0xb]
    // 0xbcf7a0: DecompressPointer r1
    //     0xbcf7a0: add             x1, x1, HEAP, lsl #32
    // 0xbcf7a4: LoadField: r2 = r1->field_13
    //     0xbcf7a4: ldur            w2, [x1, #0x13]
    // 0xbcf7a8: LoadField: r1 = r0->field_13
    //     0xbcf7a8: ldur            x1, [x0, #0x13]
    // 0xbcf7ac: r3 = LoadInt32Instr(r2)
    //     0xbcf7ac: sbfx            x3, x2, #1, #0x1f
    // 0xbcf7b0: sub             x2, x3, x1
    // 0xbcf7b4: cmp             x2, #1
    // 0xbcf7b8: b.ge            #0xbcf7c8
    // 0xbcf7bc: mov             x1, x0
    // 0xbcf7c0: r2 = 1
    //     0xbcf7c0: movz            x2, #0x1
    // 0xbcf7c4: r0 = _increaseBufferSize()
    //     0xbcf7c4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf7c8: ldur            x2, [fp, #-8]
    // 0xbcf7cc: ldur            x3, [fp, #-0x10]
    // 0xbcf7d0: r4 = 4
    //     0xbcf7d0: movz            x4, #0x4
    // 0xbcf7d4: LoadField: r5 = r2->field_b
    //     0xbcf7d4: ldur            w5, [x2, #0xb]
    // 0xbcf7d8: DecompressPointer r5
    //     0xbcf7d8: add             x5, x5, HEAP, lsl #32
    // 0xbcf7dc: LoadField: r6 = r2->field_13
    //     0xbcf7dc: ldur            x6, [x2, #0x13]
    // 0xbcf7e0: add             x0, x6, #1
    // 0xbcf7e4: StoreField: r2->field_13 = r0
    //     0xbcf7e4: stur            x0, [x2, #0x13]
    // 0xbcf7e8: LoadField: r0 = r5->field_13
    //     0xbcf7e8: ldur            w0, [x5, #0x13]
    // 0xbcf7ec: r1 = LoadInt32Instr(r0)
    //     0xbcf7ec: sbfx            x1, x0, #1, #0x1f
    // 0xbcf7f0: mov             x0, x1
    // 0xbcf7f4: mov             x1, x6
    // 0xbcf7f8: cmp             x1, x0
    // 0xbcf7fc: b.hs            #0xbcf968
    // 0xbcf800: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf800: add             x0, x5, x6
    //     0xbcf804: strb            w4, [x0, #0x17]
    // 0xbcf808: LoadField: r0 = r3->field_1b
    //     0xbcf808: ldur            w0, [x3, #0x1b]
    // 0xbcf80c: DecompressPointer r0
    //     0xbcf80c: add             x0, x0, HEAP, lsl #32
    // 0xbcf810: r16 = <Category>
    //     0xbcf810: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0xbcf814: stp             x2, x16, [SP, #8]
    // 0xbcf818: str             x0, [SP]
    // 0xbcf81c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf81c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf820: r0 = write()
    //     0xbcf820: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf824: ldur            x0, [fp, #-8]
    // 0xbcf828: LoadField: r1 = r0->field_b
    //     0xbcf828: ldur            w1, [x0, #0xb]
    // 0xbcf82c: DecompressPointer r1
    //     0xbcf82c: add             x1, x1, HEAP, lsl #32
    // 0xbcf830: LoadField: r2 = r1->field_13
    //     0xbcf830: ldur            w2, [x1, #0x13]
    // 0xbcf834: LoadField: r1 = r0->field_13
    //     0xbcf834: ldur            x1, [x0, #0x13]
    // 0xbcf838: r3 = LoadInt32Instr(r2)
    //     0xbcf838: sbfx            x3, x2, #1, #0x1f
    // 0xbcf83c: sub             x2, x3, x1
    // 0xbcf840: cmp             x2, #1
    // 0xbcf844: b.ge            #0xbcf854
    // 0xbcf848: mov             x1, x0
    // 0xbcf84c: r2 = 1
    //     0xbcf84c: movz            x2, #0x1
    // 0xbcf850: r0 = _increaseBufferSize()
    //     0xbcf850: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf854: ldur            x2, [fp, #-8]
    // 0xbcf858: ldur            x3, [fp, #-0x10]
    // 0xbcf85c: r4 = 5
    //     0xbcf85c: movz            x4, #0x5
    // 0xbcf860: LoadField: r5 = r2->field_b
    //     0xbcf860: ldur            w5, [x2, #0xb]
    // 0xbcf864: DecompressPointer r5
    //     0xbcf864: add             x5, x5, HEAP, lsl #32
    // 0xbcf868: LoadField: r6 = r2->field_13
    //     0xbcf868: ldur            x6, [x2, #0x13]
    // 0xbcf86c: add             x0, x6, #1
    // 0xbcf870: StoreField: r2->field_13 = r0
    //     0xbcf870: stur            x0, [x2, #0x13]
    // 0xbcf874: LoadField: r0 = r5->field_13
    //     0xbcf874: ldur            w0, [x5, #0x13]
    // 0xbcf878: r1 = LoadInt32Instr(r0)
    //     0xbcf878: sbfx            x1, x0, #1, #0x1f
    // 0xbcf87c: mov             x0, x1
    // 0xbcf880: mov             x1, x6
    // 0xbcf884: cmp             x1, x0
    // 0xbcf888: b.hs            #0xbcf96c
    // 0xbcf88c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf88c: add             x0, x5, x6
    //     0xbcf890: strb            w4, [x0, #0x17]
    // 0xbcf894: LoadField: r0 = r3->field_1f
    //     0xbcf894: ldur            w0, [x3, #0x1f]
    // 0xbcf898: DecompressPointer r0
    //     0xbcf898: add             x0, x0, HEAP, lsl #32
    // 0xbcf89c: r16 = <String>
    //     0xbcf89c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbcf8a0: stp             x2, x16, [SP, #8]
    // 0xbcf8a4: str             x0, [SP]
    // 0xbcf8a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf8a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf8ac: r0 = write()
    //     0xbcf8ac: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf8b0: ldur            x0, [fp, #-8]
    // 0xbcf8b4: LoadField: r1 = r0->field_b
    //     0xbcf8b4: ldur            w1, [x0, #0xb]
    // 0xbcf8b8: DecompressPointer r1
    //     0xbcf8b8: add             x1, x1, HEAP, lsl #32
    // 0xbcf8bc: LoadField: r2 = r1->field_13
    //     0xbcf8bc: ldur            w2, [x1, #0x13]
    // 0xbcf8c0: LoadField: r1 = r0->field_13
    //     0xbcf8c0: ldur            x1, [x0, #0x13]
    // 0xbcf8c4: r3 = LoadInt32Instr(r2)
    //     0xbcf8c4: sbfx            x3, x2, #1, #0x1f
    // 0xbcf8c8: sub             x2, x3, x1
    // 0xbcf8cc: cmp             x2, #1
    // 0xbcf8d0: b.ge            #0xbcf8e0
    // 0xbcf8d4: mov             x1, x0
    // 0xbcf8d8: r2 = 1
    //     0xbcf8d8: movz            x2, #0x1
    // 0xbcf8dc: r0 = _increaseBufferSize()
    //     0xbcf8dc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbcf8e0: ldur            x2, [fp, #-8]
    // 0xbcf8e4: ldur            x3, [fp, #-0x10]
    // 0xbcf8e8: r4 = 6
    //     0xbcf8e8: movz            x4, #0x6
    // 0xbcf8ec: LoadField: r5 = r2->field_b
    //     0xbcf8ec: ldur            w5, [x2, #0xb]
    // 0xbcf8f0: DecompressPointer r5
    //     0xbcf8f0: add             x5, x5, HEAP, lsl #32
    // 0xbcf8f4: LoadField: r6 = r2->field_13
    //     0xbcf8f4: ldur            x6, [x2, #0x13]
    // 0xbcf8f8: add             x0, x6, #1
    // 0xbcf8fc: StoreField: r2->field_13 = r0
    //     0xbcf8fc: stur            x0, [x2, #0x13]
    // 0xbcf900: LoadField: r0 = r5->field_13
    //     0xbcf900: ldur            w0, [x5, #0x13]
    // 0xbcf904: r1 = LoadInt32Instr(r0)
    //     0xbcf904: sbfx            x1, x0, #1, #0x1f
    // 0xbcf908: mov             x0, x1
    // 0xbcf90c: mov             x1, x6
    // 0xbcf910: cmp             x1, x0
    // 0xbcf914: b.hs            #0xbcf970
    // 0xbcf918: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbcf918: add             x0, x5, x6
    //     0xbcf91c: strb            w4, [x0, #0x17]
    // 0xbcf920: LoadField: r0 = r3->field_23
    //     0xbcf920: ldur            w0, [x3, #0x23]
    // 0xbcf924: DecompressPointer r0
    //     0xbcf924: add             x0, x0, HEAP, lsl #32
    // 0xbcf928: r16 = <String?>
    //     0xbcf928: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbcf92c: stp             x2, x16, [SP, #8]
    // 0xbcf930: str             x0, [SP]
    // 0xbcf934: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbcf934: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbcf938: r0 = write()
    //     0xbcf938: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbcf93c: r0 = Null
    //     0xbcf93c: mov             x0, NULL
    // 0xbcf940: LeaveFrame
    //     0xbcf940: mov             SP, fp
    //     0xbcf944: ldp             fp, lr, [SP], #0x10
    // 0xbcf948: ret
    //     0xbcf948: ret             
    // 0xbcf94c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbcf94c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbcf950: b               #0xbcf4cc
    // 0xbcf954: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf954: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf958: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf958: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf95c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf95c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf960: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf960: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf964: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf964: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf968: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf968: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf96c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf96c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbcf970: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbcf970: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbeffb8, size: 0x24
    // 0xbeffb8: r1 = 88
    //     0xbeffb8: movz            x1, #0x58
    // 0xbeffbc: r16 = LoadInt32Instr(r1)
    //     0xbeffbc: sbfx            x16, x1, #1, #0x1f
    // 0xbeffc0: r17 = 11601
    //     0xbeffc0: movz            x17, #0x2d51
    // 0xbeffc4: mul             x0, x16, x17
    // 0xbeffc8: umulh           x16, x16, x17
    // 0xbeffcc: eor             x0, x0, x16
    // 0xbeffd0: r0 = 0
    //     0xbeffd0: eor             x0, x0, x0, lsr #32
    // 0xbeffd4: ubfiz           x0, x0, #1, #0x1e
    // 0xbeffd8: ret
    //     0xbeffd8: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd75f3c, size: 0x9c
    // 0xd75f3c: EnterFrame
    //     0xd75f3c: stp             fp, lr, [SP, #-0x10]!
    //     0xd75f40: mov             fp, SP
    // 0xd75f44: AllocStack(0x10)
    //     0xd75f44: sub             SP, SP, #0x10
    // 0xd75f48: CheckStackOverflow
    //     0xd75f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd75f4c: cmp             SP, x16
    //     0xd75f50: b.ls            #0xd75fd0
    // 0xd75f54: ldr             x0, [fp, #0x10]
    // 0xd75f58: cmp             w0, NULL
    // 0xd75f5c: b.ne            #0xd75f70
    // 0xd75f60: r0 = false
    //     0xd75f60: add             x0, NULL, #0x30  ; false
    // 0xd75f64: LeaveFrame
    //     0xd75f64: mov             SP, fp
    //     0xd75f68: ldp             fp, lr, [SP], #0x10
    // 0xd75f6c: ret
    //     0xd75f6c: ret             
    // 0xd75f70: ldr             x1, [fp, #0x18]
    // 0xd75f74: cmp             w1, w0
    // 0xd75f78: b.ne            #0xd75f84
    // 0xd75f7c: r0 = true
    //     0xd75f7c: add             x0, NULL, #0x20  ; true
    // 0xd75f80: b               #0xd75fc4
    // 0xd75f84: r1 = 60
    //     0xd75f84: movz            x1, #0x3c
    // 0xd75f88: branchIfSmi(r0, 0xd75f94)
    //     0xd75f88: tbz             w0, #0, #0xd75f94
    // 0xd75f8c: r1 = LoadClassIdInstr(r0)
    //     0xd75f8c: ldur            x1, [x0, #-1]
    //     0xd75f90: ubfx            x1, x1, #0xc, #0x14
    // 0xd75f94: cmp             x1, #0x688
    // 0xd75f98: b.ne            #0xd75fc0
    // 0xd75f9c: r16 = ArticleAdapter
    //     0xd75f9c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b538] Type: ArticleAdapter
    //     0xd75fa0: ldr             x16, [x16, #0x538]
    // 0xd75fa4: r30 = ArticleAdapter
    //     0xd75fa4: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b538] Type: ArticleAdapter
    //     0xd75fa8: ldr             lr, [lr, #0x538]
    // 0xd75fac: stp             lr, x16, [SP]
    // 0xd75fb0: r0 = ==()
    //     0xd75fb0: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd75fb4: tbnz            w0, #4, #0xd75fc0
    // 0xd75fb8: r0 = true
    //     0xd75fb8: add             x0, NULL, #0x20  ; true
    // 0xd75fbc: b               #0xd75fc4
    // 0xd75fc0: r0 = false
    //     0xd75fc0: add             x0, NULL, #0x30  ; false
    // 0xd75fc4: LeaveFrame
    //     0xd75fc4: mov             SP, fp
    //     0xd75fc8: ldp             fp, lr, [SP], #0x10
    // 0xd75fcc: ret
    //     0xd75fcc: ret             
    // 0xd75fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd75fd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd75fd4: b               #0xd75f54
  }
}

// class id: 5597, size: 0x20, field offset: 0x8
//   const constructor, 
class ArticleInsertion extends Equatable {

  factory _ ArticleInsertion.fromMap(/* No info */) {
    // ** addr: 0x8ec860, size: 0x1e8
    // 0x8ec860: EnterFrame
    //     0x8ec860: stp             fp, lr, [SP, #-0x10]!
    //     0x8ec864: mov             fp, SP
    // 0x8ec868: AllocStack(0x28)
    //     0x8ec868: sub             SP, SP, #0x28
    // 0x8ec86c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ec86c: mov             x3, x2
    //     0x8ec870: stur            x2, [fp, #-8]
    // 0x8ec874: CheckStackOverflow
    //     0x8ec874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ec878: cmp             SP, x16
    //     0x8ec87c: b.ls            #0x8eca40
    // 0x8ec880: r0 = LoadClassIdInstr(r3)
    //     0x8ec880: ldur            x0, [x3, #-1]
    //     0x8ec884: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec888: mov             x1, x3
    // 0x8ec88c: r2 = "id"
    //     0x8ec88c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ec890: ldr             x2, [x2, #0x740]
    // 0x8ec894: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec894: sub             lr, x0, #0x114
    //     0x8ec898: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec89c: blr             lr
    // 0x8ec8a0: mov             x3, x0
    // 0x8ec8a4: r2 = Null
    //     0x8ec8a4: mov             x2, NULL
    // 0x8ec8a8: r1 = Null
    //     0x8ec8a8: mov             x1, NULL
    // 0x8ec8ac: stur            x3, [fp, #-0x10]
    // 0x8ec8b0: branchIfSmi(r0, 0x8ec8d8)
    //     0x8ec8b0: tbz             w0, #0, #0x8ec8d8
    // 0x8ec8b4: r4 = LoadClassIdInstr(r0)
    //     0x8ec8b4: ldur            x4, [x0, #-1]
    //     0x8ec8b8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec8bc: sub             x4, x4, #0x3c
    // 0x8ec8c0: cmp             x4, #1
    // 0x8ec8c4: b.ls            #0x8ec8d8
    // 0x8ec8c8: r8 = int
    //     0x8ec8c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ec8cc: r3 = Null
    //     0x8ec8cc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e78] Null
    //     0x8ec8d0: ldr             x3, [x3, #0xe78]
    // 0x8ec8d4: r0 = int()
    //     0x8ec8d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ec8d8: ldur            x3, [fp, #-8]
    // 0x8ec8dc: r0 = LoadClassIdInstr(r3)
    //     0x8ec8dc: ldur            x0, [x3, #-1]
    //     0x8ec8e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec8e4: mov             x1, x3
    // 0x8ec8e8: r2 = "title"
    //     0x8ec8e8: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8ec8ec: ldr             x2, [x2, #0x748]
    // 0x8ec8f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec8f0: sub             lr, x0, #0x114
    //     0x8ec8f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec8f8: blr             lr
    // 0x8ec8fc: mov             x3, x0
    // 0x8ec900: r2 = Null
    //     0x8ec900: mov             x2, NULL
    // 0x8ec904: r1 = Null
    //     0x8ec904: mov             x1, NULL
    // 0x8ec908: stur            x3, [fp, #-0x18]
    // 0x8ec90c: r4 = 60
    //     0x8ec90c: movz            x4, #0x3c
    // 0x8ec910: branchIfSmi(r0, 0x8ec91c)
    //     0x8ec910: tbz             w0, #0, #0x8ec91c
    // 0x8ec914: r4 = LoadClassIdInstr(r0)
    //     0x8ec914: ldur            x4, [x0, #-1]
    //     0x8ec918: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec91c: sub             x4, x4, #0x5e
    // 0x8ec920: cmp             x4, #1
    // 0x8ec924: b.ls            #0x8ec938
    // 0x8ec928: r8 = String
    //     0x8ec928: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ec92c: r3 = Null
    //     0x8ec92c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e88] Null
    //     0x8ec930: ldr             x3, [x3, #0xe88]
    // 0x8ec934: r0 = String()
    //     0x8ec934: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ec938: ldur            x3, [fp, #-8]
    // 0x8ec93c: r0 = LoadClassIdInstr(r3)
    //     0x8ec93c: ldur            x0, [x3, #-1]
    //     0x8ec940: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec944: mov             x1, x3
    // 0x8ec948: r2 = "url"
    //     0x8ec948: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x8ec94c: ldr             x2, [x2, #0xd78]
    // 0x8ec950: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec950: sub             lr, x0, #0x114
    //     0x8ec954: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec958: blr             lr
    // 0x8ec95c: mov             x3, x0
    // 0x8ec960: r2 = Null
    //     0x8ec960: mov             x2, NULL
    // 0x8ec964: r1 = Null
    //     0x8ec964: mov             x1, NULL
    // 0x8ec968: stur            x3, [fp, #-0x20]
    // 0x8ec96c: r4 = 60
    //     0x8ec96c: movz            x4, #0x3c
    // 0x8ec970: branchIfSmi(r0, 0x8ec97c)
    //     0x8ec970: tbz             w0, #0, #0x8ec97c
    // 0x8ec974: r4 = LoadClassIdInstr(r0)
    //     0x8ec974: ldur            x4, [x0, #-1]
    //     0x8ec978: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec97c: sub             x4, x4, #0x5e
    // 0x8ec980: cmp             x4, #1
    // 0x8ec984: b.ls            #0x8ec998
    // 0x8ec988: r8 = String
    //     0x8ec988: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ec98c: r3 = Null
    //     0x8ec98c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e98] Null
    //     0x8ec990: ldr             x3, [x3, #0xe98]
    // 0x8ec994: r0 = String()
    //     0x8ec994: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ec998: ldur            x1, [fp, #-8]
    // 0x8ec99c: r0 = LoadClassIdInstr(r1)
    //     0x8ec99c: ldur            x0, [x1, #-1]
    //     0x8ec9a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec9a4: r2 = "paragraph"
    //     0x8ec9a4: add             x2, PP, #0x40, lsl #12  ; [pp+0x40ea8] "paragraph"
    //     0x8ec9a8: ldr             x2, [x2, #0xea8]
    // 0x8ec9ac: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec9ac: sub             lr, x0, #0x114
    //     0x8ec9b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec9b4: blr             lr
    // 0x8ec9b8: mov             x3, x0
    // 0x8ec9bc: r2 = Null
    //     0x8ec9bc: mov             x2, NULL
    // 0x8ec9c0: r1 = Null
    //     0x8ec9c0: mov             x1, NULL
    // 0x8ec9c4: stur            x3, [fp, #-8]
    // 0x8ec9c8: branchIfSmi(r0, 0x8ec9f0)
    //     0x8ec9c8: tbz             w0, #0, #0x8ec9f0
    // 0x8ec9cc: r4 = LoadClassIdInstr(r0)
    //     0x8ec9cc: ldur            x4, [x0, #-1]
    //     0x8ec9d0: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec9d4: sub             x4, x4, #0x3c
    // 0x8ec9d8: cmp             x4, #1
    // 0x8ec9dc: b.ls            #0x8ec9f0
    // 0x8ec9e0: r8 = int
    //     0x8ec9e0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ec9e4: r3 = Null
    //     0x8ec9e4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40eb0] Null
    //     0x8ec9e8: ldr             x3, [x3, #0xeb0]
    // 0x8ec9ec: r0 = int()
    //     0x8ec9ec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ec9f0: ldur            x0, [fp, #-0x10]
    // 0x8ec9f4: r1 = LoadInt32Instr(r0)
    //     0x8ec9f4: sbfx            x1, x0, #1, #0x1f
    //     0x8ec9f8: tbz             w0, #0, #0x8eca00
    //     0x8ec9fc: ldur            x1, [x0, #7]
    // 0x8eca00: stur            x1, [fp, #-0x28]
    // 0x8eca04: r0 = ArticleInsertion()
    //     0x8eca04: bl              #0x8eca48  ; AllocateArticleInsertionStub -> ArticleInsertion (size=0x20)
    // 0x8eca08: ldur            x1, [fp, #-0x28]
    // 0x8eca0c: StoreField: r0->field_7 = r1
    //     0x8eca0c: stur            x1, [x0, #7]
    // 0x8eca10: ldur            x1, [fp, #-0x18]
    // 0x8eca14: StoreField: r0->field_f = r1
    //     0x8eca14: stur            w1, [x0, #0xf]
    // 0x8eca18: ldur            x1, [fp, #-0x20]
    // 0x8eca1c: StoreField: r0->field_13 = r1
    //     0x8eca1c: stur            w1, [x0, #0x13]
    // 0x8eca20: ldur            x1, [fp, #-8]
    // 0x8eca24: r2 = LoadInt32Instr(r1)
    //     0x8eca24: sbfx            x2, x1, #1, #0x1f
    //     0x8eca28: tbz             w1, #0, #0x8eca30
    //     0x8eca2c: ldur            x2, [x1, #7]
    // 0x8eca30: ArrayStore: r0[0] = r2  ; List_8
    //     0x8eca30: stur            x2, [x0, #0x17]
    // 0x8eca34: LeaveFrame
    //     0x8eca34: mov             SP, fp
    //     0x8eca38: ldp             fp, lr, [SP], #0x10
    // 0x8eca3c: ret
    //     0x8eca3c: ret             
    // 0x8eca40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eca40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eca44: b               #0x8ec880
  }
}

// class id: 5598, size: 0x4c, field offset: 0x8
//   const constructor, 
class ArticleDetail extends Equatable {

  factory _ ArticleDetail.fromMap(/* No info */) {
    // ** addr: 0x8ebb40, size: 0xa28
    // 0x8ebb40: EnterFrame
    //     0x8ebb40: stp             fp, lr, [SP, #-0x10]!
    //     0x8ebb44: mov             fp, SP
    // 0x8ebb48: AllocStack(0xa0)
    //     0x8ebb48: sub             SP, SP, #0xa0
    // 0x8ebb4c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ebb4c: mov             x3, x2
    //     0x8ebb50: stur            x2, [fp, #-8]
    // 0x8ebb54: CheckStackOverflow
    //     0x8ebb54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ebb58: cmp             SP, x16
    //     0x8ebb5c: b.ls            #0x8ec560
    // 0x8ebb60: r0 = LoadClassIdInstr(r3)
    //     0x8ebb60: ldur            x0, [x3, #-1]
    //     0x8ebb64: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebb68: mov             x1, x3
    // 0x8ebb6c: r2 = "id"
    //     0x8ebb6c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ebb70: ldr             x2, [x2, #0x740]
    // 0x8ebb74: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebb74: sub             lr, x0, #0x114
    //     0x8ebb78: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebb7c: blr             lr
    // 0x8ebb80: mov             x3, x0
    // 0x8ebb84: r2 = Null
    //     0x8ebb84: mov             x2, NULL
    // 0x8ebb88: r1 = Null
    //     0x8ebb88: mov             x1, NULL
    // 0x8ebb8c: stur            x3, [fp, #-0x10]
    // 0x8ebb90: branchIfSmi(r0, 0x8ebbb8)
    //     0x8ebb90: tbz             w0, #0, #0x8ebbb8
    // 0x8ebb94: r4 = LoadClassIdInstr(r0)
    //     0x8ebb94: ldur            x4, [x0, #-1]
    //     0x8ebb98: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebb9c: sub             x4, x4, #0x3c
    // 0x8ebba0: cmp             x4, #1
    // 0x8ebba4: b.ls            #0x8ebbb8
    // 0x8ebba8: r8 = int
    //     0x8ebba8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ebbac: r3 = Null
    //     0x8ebbac: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d18] Null
    //     0x8ebbb0: ldr             x3, [x3, #0xd18]
    // 0x8ebbb4: r0 = int()
    //     0x8ebbb4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ebbb8: ldur            x3, [fp, #-8]
    // 0x8ebbbc: r0 = LoadClassIdInstr(r3)
    //     0x8ebbbc: ldur            x0, [x3, #-1]
    //     0x8ebbc0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebbc4: mov             x1, x3
    // 0x8ebbc8: r2 = "title"
    //     0x8ebbc8: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8ebbcc: ldr             x2, [x2, #0x748]
    // 0x8ebbd0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebbd0: sub             lr, x0, #0x114
    //     0x8ebbd4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebbd8: blr             lr
    // 0x8ebbdc: mov             x3, x0
    // 0x8ebbe0: r2 = Null
    //     0x8ebbe0: mov             x2, NULL
    // 0x8ebbe4: r1 = Null
    //     0x8ebbe4: mov             x1, NULL
    // 0x8ebbe8: stur            x3, [fp, #-0x18]
    // 0x8ebbec: r4 = 60
    //     0x8ebbec: movz            x4, #0x3c
    // 0x8ebbf0: branchIfSmi(r0, 0x8ebbfc)
    //     0x8ebbf0: tbz             w0, #0, #0x8ebbfc
    // 0x8ebbf4: r4 = LoadClassIdInstr(r0)
    //     0x8ebbf4: ldur            x4, [x0, #-1]
    //     0x8ebbf8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebbfc: sub             x4, x4, #0x5e
    // 0x8ebc00: cmp             x4, #1
    // 0x8ebc04: b.ls            #0x8ebc18
    // 0x8ebc08: r8 = String
    //     0x8ebc08: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ebc0c: r3 = Null
    //     0x8ebc0c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d28] Null
    //     0x8ebc10: ldr             x3, [x3, #0xd28]
    // 0x8ebc14: r0 = String()
    //     0x8ebc14: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ebc18: ldur            x3, [fp, #-8]
    // 0x8ebc1c: r0 = LoadClassIdInstr(r3)
    //     0x8ebc1c: ldur            x0, [x3, #-1]
    //     0x8ebc20: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebc24: mov             x1, x3
    // 0x8ebc28: r2 = "slug"
    //     0x8ebc28: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x8ebc2c: ldr             x2, [x2, #0x9a8]
    // 0x8ebc30: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebc30: sub             lr, x0, #0x114
    //     0x8ebc34: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebc38: blr             lr
    // 0x8ebc3c: mov             x3, x0
    // 0x8ebc40: r2 = Null
    //     0x8ebc40: mov             x2, NULL
    // 0x8ebc44: r1 = Null
    //     0x8ebc44: mov             x1, NULL
    // 0x8ebc48: stur            x3, [fp, #-0x20]
    // 0x8ebc4c: r4 = 60
    //     0x8ebc4c: movz            x4, #0x3c
    // 0x8ebc50: branchIfSmi(r0, 0x8ebc5c)
    //     0x8ebc50: tbz             w0, #0, #0x8ebc5c
    // 0x8ebc54: r4 = LoadClassIdInstr(r0)
    //     0x8ebc54: ldur            x4, [x0, #-1]
    //     0x8ebc58: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebc5c: sub             x4, x4, #0x5e
    // 0x8ebc60: cmp             x4, #1
    // 0x8ebc64: b.ls            #0x8ebc78
    // 0x8ebc68: r8 = String?
    //     0x8ebc68: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ebc6c: r3 = Null
    //     0x8ebc6c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d38] Null
    //     0x8ebc70: ldr             x3, [x3, #0xd38]
    // 0x8ebc74: r0 = String?()
    //     0x8ebc74: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ebc78: ldur            x3, [fp, #-8]
    // 0x8ebc7c: r0 = LoadClassIdInstr(r3)
    //     0x8ebc7c: ldur            x0, [x3, #-1]
    //     0x8ebc80: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebc84: mov             x1, x3
    // 0x8ebc88: r2 = "image"
    //     0x8ebc88: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x8ebc8c: ldr             x2, [x2, #0x520]
    // 0x8ebc90: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebc90: sub             lr, x0, #0x114
    //     0x8ebc94: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebc98: blr             lr
    // 0x8ebc9c: mov             x3, x0
    // 0x8ebca0: r2 = Null
    //     0x8ebca0: mov             x2, NULL
    // 0x8ebca4: r1 = Null
    //     0x8ebca4: mov             x1, NULL
    // 0x8ebca8: stur            x3, [fp, #-0x28]
    // 0x8ebcac: r8 = Map<String, dynamic>
    //     0x8ebcac: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ebcb0: r3 = Null
    //     0x8ebcb0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d48] Null
    //     0x8ebcb4: ldr             x3, [x3, #0xd48]
    // 0x8ebcb8: r0 = Map<String, dynamic>()
    //     0x8ebcb8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ebcbc: ldur            x2, [fp, #-0x28]
    // 0x8ebcc0: r1 = Null
    //     0x8ebcc0: mov             x1, NULL
    // 0x8ebcc4: r0 = Image.fromMap()
    //     0x8ebcc4: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x8ebcc8: mov             x4, x0
    // 0x8ebccc: ldur            x3, [fp, #-8]
    // 0x8ebcd0: stur            x4, [fp, #-0x28]
    // 0x8ebcd4: r0 = LoadClassIdInstr(r3)
    //     0x8ebcd4: ldur            x0, [x3, #-1]
    //     0x8ebcd8: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebcdc: mov             x1, x3
    // 0x8ebce0: r2 = "category"
    //     0x8ebce0: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x8ebce4: ldr             x2, [x2, #0x960]
    // 0x8ebce8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebce8: sub             lr, x0, #0x114
    //     0x8ebcec: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebcf0: blr             lr
    // 0x8ebcf4: mov             x3, x0
    // 0x8ebcf8: r2 = Null
    //     0x8ebcf8: mov             x2, NULL
    // 0x8ebcfc: r1 = Null
    //     0x8ebcfc: mov             x1, NULL
    // 0x8ebd00: stur            x3, [fp, #-0x30]
    // 0x8ebd04: r8 = Map<String, dynamic>
    //     0x8ebd04: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ebd08: r3 = Null
    //     0x8ebd08: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d58] Null
    //     0x8ebd0c: ldr             x3, [x3, #0xd58]
    // 0x8ebd10: r0 = Map<String, dynamic>()
    //     0x8ebd10: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ebd14: ldur            x2, [fp, #-0x30]
    // 0x8ebd18: r1 = Null
    //     0x8ebd18: mov             x1, NULL
    // 0x8ebd1c: r0 = Category.fromMap()
    //     0x8ebd1c: bl              #0x8aa92c  ; [package:nuonline/app/data/models/category.dart] Category::Category.fromMap
    // 0x8ebd20: mov             x4, x0
    // 0x8ebd24: ldur            x3, [fp, #-8]
    // 0x8ebd28: stur            x4, [fp, #-0x30]
    // 0x8ebd2c: r0 = LoadClassIdInstr(r3)
    //     0x8ebd2c: ldur            x0, [x3, #-1]
    //     0x8ebd30: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebd34: mov             x1, x3
    // 0x8ebd38: r2 = "published_at"
    //     0x8ebd38: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d958] "published_at"
    //     0x8ebd3c: ldr             x2, [x2, #0x958]
    // 0x8ebd40: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebd40: sub             lr, x0, #0x114
    //     0x8ebd44: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebd48: blr             lr
    // 0x8ebd4c: mov             x3, x0
    // 0x8ebd50: r2 = Null
    //     0x8ebd50: mov             x2, NULL
    // 0x8ebd54: r1 = Null
    //     0x8ebd54: mov             x1, NULL
    // 0x8ebd58: stur            x3, [fp, #-0x38]
    // 0x8ebd5c: r4 = 60
    //     0x8ebd5c: movz            x4, #0x3c
    // 0x8ebd60: branchIfSmi(r0, 0x8ebd6c)
    //     0x8ebd60: tbz             w0, #0, #0x8ebd6c
    // 0x8ebd64: r4 = LoadClassIdInstr(r0)
    //     0x8ebd64: ldur            x4, [x0, #-1]
    //     0x8ebd68: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebd6c: sub             x4, x4, #0x5e
    // 0x8ebd70: cmp             x4, #1
    // 0x8ebd74: b.ls            #0x8ebd88
    // 0x8ebd78: r8 = String
    //     0x8ebd78: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ebd7c: r3 = Null
    //     0x8ebd7c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d68] Null
    //     0x8ebd80: ldr             x3, [x3, #0xd68]
    // 0x8ebd84: r0 = String()
    //     0x8ebd84: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ebd88: ldur            x3, [fp, #-8]
    // 0x8ebd8c: r0 = LoadClassIdInstr(r3)
    //     0x8ebd8c: ldur            x0, [x3, #-1]
    //     0x8ebd90: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebd94: mov             x1, x3
    // 0x8ebd98: r2 = "url"
    //     0x8ebd98: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x8ebd9c: ldr             x2, [x2, #0xd78]
    // 0x8ebda0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebda0: sub             lr, x0, #0x114
    //     0x8ebda4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebda8: blr             lr
    // 0x8ebdac: mov             x3, x0
    // 0x8ebdb0: r2 = Null
    //     0x8ebdb0: mov             x2, NULL
    // 0x8ebdb4: r1 = Null
    //     0x8ebdb4: mov             x1, NULL
    // 0x8ebdb8: stur            x3, [fp, #-0x40]
    // 0x8ebdbc: r4 = 60
    //     0x8ebdbc: movz            x4, #0x3c
    // 0x8ebdc0: branchIfSmi(r0, 0x8ebdcc)
    //     0x8ebdc0: tbz             w0, #0, #0x8ebdcc
    // 0x8ebdc4: r4 = LoadClassIdInstr(r0)
    //     0x8ebdc4: ldur            x4, [x0, #-1]
    //     0x8ebdc8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebdcc: sub             x4, x4, #0x5e
    // 0x8ebdd0: cmp             x4, #1
    // 0x8ebdd4: b.ls            #0x8ebde8
    // 0x8ebdd8: r8 = String
    //     0x8ebdd8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ebddc: r3 = Null
    //     0x8ebddc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d78] Null
    //     0x8ebde0: ldr             x3, [x3, #0xd78]
    // 0x8ebde4: r0 = String()
    //     0x8ebde4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ebde8: ldur            x3, [fp, #-8]
    // 0x8ebdec: r0 = LoadClassIdInstr(r3)
    //     0x8ebdec: ldur            x0, [x3, #-1]
    //     0x8ebdf0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebdf4: mov             x1, x3
    // 0x8ebdf8: r2 = "tags"
    //     0x8ebdf8: add             x2, PP, #0x40, lsl #12  ; [pp+0x40d88] "tags"
    //     0x8ebdfc: ldr             x2, [x2, #0xd88]
    // 0x8ebe00: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebe00: sub             lr, x0, #0x114
    //     0x8ebe04: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebe08: blr             lr
    // 0x8ebe0c: mov             x3, x0
    // 0x8ebe10: r2 = Null
    //     0x8ebe10: mov             x2, NULL
    // 0x8ebe14: r1 = Null
    //     0x8ebe14: mov             x1, NULL
    // 0x8ebe18: stur            x3, [fp, #-0x48]
    // 0x8ebe1c: r4 = 60
    //     0x8ebe1c: movz            x4, #0x3c
    // 0x8ebe20: branchIfSmi(r0, 0x8ebe2c)
    //     0x8ebe20: tbz             w0, #0, #0x8ebe2c
    // 0x8ebe24: r4 = LoadClassIdInstr(r0)
    //     0x8ebe24: ldur            x4, [x0, #-1]
    //     0x8ebe28: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebe2c: sub             x4, x4, #0x5a
    // 0x8ebe30: cmp             x4, #2
    // 0x8ebe34: b.ls            #0x8ebe48
    // 0x8ebe38: r8 = List
    //     0x8ebe38: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0x8ebe3c: r3 = Null
    //     0x8ebe3c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d90] Null
    //     0x8ebe40: ldr             x3, [x3, #0xd90]
    // 0x8ebe44: r0 = List()
    //     0x8ebe44: bl              #0xed6b40  ; IsType_List_Stub
    // 0x8ebe48: r1 = Function '<anonymous closure>': static.
    //     0x8ebe48: add             x1, PP, #0x40, lsl #12  ; [pp+0x40da0] AnonymousClosure: static (0x8ece08), in [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap (0x8ebb40)
    //     0x8ebe4c: ldr             x1, [x1, #0xda0]
    // 0x8ebe50: r2 = Null
    //     0x8ebe50: mov             x2, NULL
    // 0x8ebe54: r0 = AllocateClosure()
    //     0x8ebe54: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ebe58: mov             x1, x0
    // 0x8ebe5c: ldur            x0, [fp, #-0x48]
    // 0x8ebe60: r2 = LoadClassIdInstr(r0)
    //     0x8ebe60: ldur            x2, [x0, #-1]
    //     0x8ebe64: ubfx            x2, x2, #0xc, #0x14
    // 0x8ebe68: r16 = <Tag>
    //     0x8ebe68: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0x8ebe6c: stp             x0, x16, [SP, #8]
    // 0x8ebe70: str             x1, [SP]
    // 0x8ebe74: mov             x0, x2
    // 0x8ebe78: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ebe78: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ebe7c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ebe7c: movz            x17, #0xf28c
    //     0x8ebe80: add             lr, x0, x17
    //     0x8ebe84: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebe88: blr             lr
    // 0x8ebe8c: r1 = LoadClassIdInstr(r0)
    //     0x8ebe8c: ldur            x1, [x0, #-1]
    //     0x8ebe90: ubfx            x1, x1, #0xc, #0x14
    // 0x8ebe94: mov             x16, x0
    // 0x8ebe98: mov             x0, x1
    // 0x8ebe9c: mov             x1, x16
    // 0x8ebea0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ebea0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ebea4: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ebea4: movz            x17, #0xd889
    //     0x8ebea8: add             lr, x0, x17
    //     0x8ebeac: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebeb0: blr             lr
    // 0x8ebeb4: mov             x4, x0
    // 0x8ebeb8: ldur            x3, [fp, #-8]
    // 0x8ebebc: stur            x4, [fp, #-0x48]
    // 0x8ebec0: r0 = LoadClassIdInstr(r3)
    //     0x8ebec0: ldur            x0, [x3, #-1]
    //     0x8ebec4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebec8: mov             x1, x3
    // 0x8ebecc: r2 = "content"
    //     0x8ebecc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x8ebed0: ldr             x2, [x2, #0xf58]
    // 0x8ebed4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebed4: sub             lr, x0, #0x114
    //     0x8ebed8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebedc: blr             lr
    // 0x8ebee0: mov             x3, x0
    // 0x8ebee4: r2 = Null
    //     0x8ebee4: mov             x2, NULL
    // 0x8ebee8: r1 = Null
    //     0x8ebee8: mov             x1, NULL
    // 0x8ebeec: stur            x3, [fp, #-0x50]
    // 0x8ebef0: r4 = 60
    //     0x8ebef0: movz            x4, #0x3c
    // 0x8ebef4: branchIfSmi(r0, 0x8ebf00)
    //     0x8ebef4: tbz             w0, #0, #0x8ebf00
    // 0x8ebef8: r4 = LoadClassIdInstr(r0)
    //     0x8ebef8: ldur            x4, [x0, #-1]
    //     0x8ebefc: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebf00: sub             x4, x4, #0x5e
    // 0x8ebf04: cmp             x4, #1
    // 0x8ebf08: b.ls            #0x8ebf1c
    // 0x8ebf0c: r8 = String
    //     0x8ebf0c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ebf10: r3 = Null
    //     0x8ebf10: add             x3, PP, #0x40, lsl #12  ; [pp+0x40da8] Null
    //     0x8ebf14: ldr             x3, [x3, #0xda8]
    // 0x8ebf18: r0 = String()
    //     0x8ebf18: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ebf1c: ldur            x3, [fp, #-8]
    // 0x8ebf20: r0 = LoadClassIdInstr(r3)
    //     0x8ebf20: ldur            x0, [x3, #-1]
    //     0x8ebf24: ubfx            x0, x0, #0xc, #0x14
    // 0x8ebf28: mov             x1, x3
    // 0x8ebf2c: r2 = "related"
    //     0x8ebf2c: add             x2, PP, #0x32, lsl #12  ; [pp+0x320e8] "related"
    //     0x8ebf30: ldr             x2, [x2, #0xe8]
    // 0x8ebf34: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ebf34: sub             lr, x0, #0x114
    //     0x8ebf38: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebf3c: blr             lr
    // 0x8ebf40: mov             x3, x0
    // 0x8ebf44: r2 = Null
    //     0x8ebf44: mov             x2, NULL
    // 0x8ebf48: r1 = Null
    //     0x8ebf48: mov             x1, NULL
    // 0x8ebf4c: stur            x3, [fp, #-0x58]
    // 0x8ebf50: r4 = 60
    //     0x8ebf50: movz            x4, #0x3c
    // 0x8ebf54: branchIfSmi(r0, 0x8ebf60)
    //     0x8ebf54: tbz             w0, #0, #0x8ebf60
    // 0x8ebf58: r4 = LoadClassIdInstr(r0)
    //     0x8ebf58: ldur            x4, [x0, #-1]
    //     0x8ebf5c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ebf60: sub             x4, x4, #0x5a
    // 0x8ebf64: cmp             x4, #2
    // 0x8ebf68: b.ls            #0x8ebf80
    // 0x8ebf6c: r8 = List?
    //     0x8ebf6c: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8ebf70: ldr             x8, [x8, #0x140]
    // 0x8ebf74: r3 = Null
    //     0x8ebf74: add             x3, PP, #0x40, lsl #12  ; [pp+0x40db8] Null
    //     0x8ebf78: ldr             x3, [x3, #0xdb8]
    // 0x8ebf7c: r0 = List?()
    //     0x8ebf7c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8ebf80: ldur            x0, [fp, #-0x58]
    // 0x8ebf84: cmp             w0, NULL
    // 0x8ebf88: b.ne            #0x8ebfa0
    // 0x8ebf8c: r1 = Null
    //     0x8ebf8c: mov             x1, NULL
    // 0x8ebf90: r2 = 0
    //     0x8ebf90: movz            x2, #0
    // 0x8ebf94: r0 = _GrowableList()
    //     0x8ebf94: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ebf98: mov             x3, x0
    // 0x8ebf9c: b               #0x8ebfa4
    // 0x8ebfa0: mov             x3, x0
    // 0x8ebfa4: ldur            x0, [fp, #-8]
    // 0x8ebfa8: stur            x3, [fp, #-0x58]
    // 0x8ebfac: r1 = Function '<anonymous closure>': static.
    //     0x8ebfac: add             x1, PP, #0x40, lsl #12  ; [pp+0x40dc8] AnonymousClosure: static (0x8ecaa4), in [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap (0x8ebb40)
    //     0x8ebfb0: ldr             x1, [x1, #0xdc8]
    // 0x8ebfb4: r2 = Null
    //     0x8ebfb4: mov             x2, NULL
    // 0x8ebfb8: r0 = AllocateClosure()
    //     0x8ebfb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ebfbc: mov             x1, x0
    // 0x8ebfc0: ldur            x0, [fp, #-0x58]
    // 0x8ebfc4: r2 = LoadClassIdInstr(r0)
    //     0x8ebfc4: ldur            x2, [x0, #-1]
    //     0x8ebfc8: ubfx            x2, x2, #0xc, #0x14
    // 0x8ebfcc: r16 = <Article>
    //     0x8ebfcc: ldr             x16, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0x8ebfd0: stp             x0, x16, [SP, #8]
    // 0x8ebfd4: str             x1, [SP]
    // 0x8ebfd8: mov             x0, x2
    // 0x8ebfdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ebfdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ebfe0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ebfe0: movz            x17, #0xf28c
    //     0x8ebfe4: add             lr, x0, x17
    //     0x8ebfe8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ebfec: blr             lr
    // 0x8ebff0: r1 = LoadClassIdInstr(r0)
    //     0x8ebff0: ldur            x1, [x0, #-1]
    //     0x8ebff4: ubfx            x1, x1, #0xc, #0x14
    // 0x8ebff8: mov             x16, x0
    // 0x8ebffc: mov             x0, x1
    // 0x8ec000: mov             x1, x16
    // 0x8ec004: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ec004: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ec008: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ec008: movz            x17, #0xd889
    //     0x8ec00c: add             lr, x0, x17
    //     0x8ec010: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec014: blr             lr
    // 0x8ec018: mov             x4, x0
    // 0x8ec01c: ldur            x3, [fp, #-8]
    // 0x8ec020: stur            x4, [fp, #-0x58]
    // 0x8ec024: r0 = LoadClassIdInstr(r3)
    //     0x8ec024: ldur            x0, [x3, #-1]
    //     0x8ec028: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec02c: mov             x1, x3
    // 0x8ec030: r2 = "author"
    //     0x8ec030: add             x2, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x8ec034: ldr             x2, [x2, #0xbf8]
    // 0x8ec038: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec038: sub             lr, x0, #0x114
    //     0x8ec03c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec040: blr             lr
    // 0x8ec044: cmp             w0, NULL
    // 0x8ec048: b.ne            #0x8ec054
    // 0x8ec04c: r4 = Null
    //     0x8ec04c: mov             x4, NULL
    // 0x8ec050: b               #0x8ec0a8
    // 0x8ec054: ldur            x3, [fp, #-8]
    // 0x8ec058: r0 = LoadClassIdInstr(r3)
    //     0x8ec058: ldur            x0, [x3, #-1]
    //     0x8ec05c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec060: mov             x1, x3
    // 0x8ec064: r2 = "author"
    //     0x8ec064: add             x2, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x8ec068: ldr             x2, [x2, #0xbf8]
    // 0x8ec06c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec06c: sub             lr, x0, #0x114
    //     0x8ec070: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec074: blr             lr
    // 0x8ec078: mov             x3, x0
    // 0x8ec07c: r2 = Null
    //     0x8ec07c: mov             x2, NULL
    // 0x8ec080: r1 = Null
    //     0x8ec080: mov             x1, NULL
    // 0x8ec084: stur            x3, [fp, #-0x60]
    // 0x8ec088: r8 = Map<String, dynamic>
    //     0x8ec088: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ec08c: r3 = Null
    //     0x8ec08c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40dd0] Null
    //     0x8ec090: ldr             x3, [x3, #0xdd0]
    // 0x8ec094: r0 = Map<String, dynamic>()
    //     0x8ec094: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ec098: ldur            x2, [fp, #-0x60]
    // 0x8ec09c: r1 = Null
    //     0x8ec09c: mov             x1, NULL
    // 0x8ec0a0: r0 = Author.fromMap()
    //     0x8ec0a0: bl              #0x8ec574  ; [package:nuonline/app/data/models/author.dart] Author::Author.fromMap
    // 0x8ec0a4: mov             x4, x0
    // 0x8ec0a8: ldur            x3, [fp, #-8]
    // 0x8ec0ac: stur            x4, [fp, #-0x60]
    // 0x8ec0b0: r0 = LoadClassIdInstr(r3)
    //     0x8ec0b0: ldur            x0, [x3, #-1]
    //     0x8ec0b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec0b8: mov             x1, x3
    // 0x8ec0bc: r2 = "prefix"
    //     0x8ec0bc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x8ec0c0: ldr             x2, [x2, #0x970]
    // 0x8ec0c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec0c4: sub             lr, x0, #0x114
    //     0x8ec0c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec0cc: blr             lr
    // 0x8ec0d0: mov             x3, x0
    // 0x8ec0d4: r2 = Null
    //     0x8ec0d4: mov             x2, NULL
    // 0x8ec0d8: r1 = Null
    //     0x8ec0d8: mov             x1, NULL
    // 0x8ec0dc: stur            x3, [fp, #-0x68]
    // 0x8ec0e0: r4 = 60
    //     0x8ec0e0: movz            x4, #0x3c
    // 0x8ec0e4: branchIfSmi(r0, 0x8ec0f0)
    //     0x8ec0e4: tbz             w0, #0, #0x8ec0f0
    // 0x8ec0e8: r4 = LoadClassIdInstr(r0)
    //     0x8ec0e8: ldur            x4, [x0, #-1]
    //     0x8ec0ec: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec0f0: sub             x4, x4, #0x5e
    // 0x8ec0f4: cmp             x4, #1
    // 0x8ec0f8: b.ls            #0x8ec10c
    // 0x8ec0fc: r8 = String?
    //     0x8ec0fc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ec100: r3 = Null
    //     0x8ec100: add             x3, PP, #0x40, lsl #12  ; [pp+0x40de0] Null
    //     0x8ec104: ldr             x3, [x3, #0xde0]
    // 0x8ec108: r0 = String?()
    //     0x8ec108: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ec10c: ldur            x3, [fp, #-8]
    // 0x8ec110: r0 = LoadClassIdInstr(r3)
    //     0x8ec110: ldur            x0, [x3, #-1]
    //     0x8ec114: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec118: mov             x1, x3
    // 0x8ec11c: r2 = "insert"
    //     0x8ec11c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40df0] "insert"
    //     0x8ec120: ldr             x2, [x2, #0xdf0]
    // 0x8ec124: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec124: sub             lr, x0, #0x114
    //     0x8ec128: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec12c: blr             lr
    // 0x8ec130: mov             x3, x0
    // 0x8ec134: r2 = Null
    //     0x8ec134: mov             x2, NULL
    // 0x8ec138: r1 = Null
    //     0x8ec138: mov             x1, NULL
    // 0x8ec13c: stur            x3, [fp, #-0x70]
    // 0x8ec140: r4 = 60
    //     0x8ec140: movz            x4, #0x3c
    // 0x8ec144: branchIfSmi(r0, 0x8ec150)
    //     0x8ec144: tbz             w0, #0, #0x8ec150
    // 0x8ec148: r4 = LoadClassIdInstr(r0)
    //     0x8ec148: ldur            x4, [x0, #-1]
    //     0x8ec14c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec150: sub             x4, x4, #0x5a
    // 0x8ec154: cmp             x4, #2
    // 0x8ec158: b.ls            #0x8ec170
    // 0x8ec15c: r8 = List?
    //     0x8ec15c: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8ec160: ldr             x8, [x8, #0x140]
    // 0x8ec164: r3 = Null
    //     0x8ec164: add             x3, PP, #0x40, lsl #12  ; [pp+0x40df8] Null
    //     0x8ec168: ldr             x3, [x3, #0xdf8]
    // 0x8ec16c: r0 = List?()
    //     0x8ec16c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8ec170: ldur            x0, [fp, #-0x70]
    // 0x8ec174: cmp             w0, NULL
    // 0x8ec178: b.ne            #0x8ec190
    // 0x8ec17c: r1 = Null
    //     0x8ec17c: mov             x1, NULL
    // 0x8ec180: r2 = 0
    //     0x8ec180: movz            x2, #0
    // 0x8ec184: r0 = _GrowableList()
    //     0x8ec184: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ec188: mov             x3, x0
    // 0x8ec18c: b               #0x8ec194
    // 0x8ec190: mov             x3, x0
    // 0x8ec194: ldur            x0, [fp, #-8]
    // 0x8ec198: stur            x3, [fp, #-0x70]
    // 0x8ec19c: r1 = Function '<anonymous closure>': static.
    //     0x8ec19c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e08] AnonymousClosure: static (0x8eca54), in [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap (0x8ebb40)
    //     0x8ec1a0: ldr             x1, [x1, #0xe08]
    // 0x8ec1a4: r2 = Null
    //     0x8ec1a4: mov             x2, NULL
    // 0x8ec1a8: r0 = AllocateClosure()
    //     0x8ec1a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ec1ac: mov             x1, x0
    // 0x8ec1b0: ldur            x0, [fp, #-0x70]
    // 0x8ec1b4: r2 = LoadClassIdInstr(r0)
    //     0x8ec1b4: ldur            x2, [x0, #-1]
    //     0x8ec1b8: ubfx            x2, x2, #0xc, #0x14
    // 0x8ec1bc: r16 = <ArticleInsertion>
    //     0x8ec1bc: ldr             x16, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0x8ec1c0: stp             x0, x16, [SP, #8]
    // 0x8ec1c4: str             x1, [SP]
    // 0x8ec1c8: mov             x0, x2
    // 0x8ec1cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ec1cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ec1d0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ec1d0: movz            x17, #0xf28c
    //     0x8ec1d4: add             lr, x0, x17
    //     0x8ec1d8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec1dc: blr             lr
    // 0x8ec1e0: r1 = LoadClassIdInstr(r0)
    //     0x8ec1e0: ldur            x1, [x0, #-1]
    //     0x8ec1e4: ubfx            x1, x1, #0xc, #0x14
    // 0x8ec1e8: mov             x16, x0
    // 0x8ec1ec: mov             x0, x1
    // 0x8ec1f0: mov             x1, x16
    // 0x8ec1f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ec1f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ec1f8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ec1f8: movz            x17, #0xd889
    //     0x8ec1fc: add             lr, x0, x17
    //     0x8ec200: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec204: blr             lr
    // 0x8ec208: mov             x4, x0
    // 0x8ec20c: ldur            x3, [fp, #-8]
    // 0x8ec210: stur            x4, [fp, #-0x70]
    // 0x8ec214: r0 = LoadClassIdInstr(r3)
    //     0x8ec214: ldur            x0, [x3, #-1]
    //     0x8ec218: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec21c: mov             x1, x3
    // 0x8ec220: r2 = "insertTopic"
    //     0x8ec220: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fb0] "insertTopic"
    //     0x8ec224: ldr             x2, [x2, #0xfb0]
    // 0x8ec228: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec228: sub             lr, x0, #0x114
    //     0x8ec22c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec230: blr             lr
    // 0x8ec234: mov             x3, x0
    // 0x8ec238: r2 = Null
    //     0x8ec238: mov             x2, NULL
    // 0x8ec23c: r1 = Null
    //     0x8ec23c: mov             x1, NULL
    // 0x8ec240: stur            x3, [fp, #-0x78]
    // 0x8ec244: r4 = 60
    //     0x8ec244: movz            x4, #0x3c
    // 0x8ec248: branchIfSmi(r0, 0x8ec254)
    //     0x8ec248: tbz             w0, #0, #0x8ec254
    // 0x8ec24c: r4 = LoadClassIdInstr(r0)
    //     0x8ec24c: ldur            x4, [x0, #-1]
    //     0x8ec250: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec254: sub             x4, x4, #0x5a
    // 0x8ec258: cmp             x4, #2
    // 0x8ec25c: b.ls            #0x8ec274
    // 0x8ec260: r8 = List?
    //     0x8ec260: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8ec264: ldr             x8, [x8, #0x140]
    // 0x8ec268: r3 = Null
    //     0x8ec268: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e10] Null
    //     0x8ec26c: ldr             x3, [x3, #0xe10]
    // 0x8ec270: r0 = List?()
    //     0x8ec270: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8ec274: ldur            x0, [fp, #-0x78]
    // 0x8ec278: cmp             w0, NULL
    // 0x8ec27c: b.ne            #0x8ec294
    // 0x8ec280: r1 = Null
    //     0x8ec280: mov             x1, NULL
    // 0x8ec284: r2 = 0
    //     0x8ec284: movz            x2, #0
    // 0x8ec288: r0 = _GrowableList()
    //     0x8ec288: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ec28c: mov             x3, x0
    // 0x8ec290: b               #0x8ec298
    // 0x8ec294: mov             x3, x0
    // 0x8ec298: ldur            x0, [fp, #-8]
    // 0x8ec29c: stur            x3, [fp, #-0x78]
    // 0x8ec2a0: r1 = Function '<anonymous closure>': static.
    //     0x8ec2a0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e20] AnonymousClosure: static (0x8ec810), in [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap (0x8ebb40)
    //     0x8ec2a4: ldr             x1, [x1, #0xe20]
    // 0x8ec2a8: r2 = Null
    //     0x8ec2a8: mov             x2, NULL
    // 0x8ec2ac: r0 = AllocateClosure()
    //     0x8ec2ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ec2b0: mov             x1, x0
    // 0x8ec2b4: ldur            x0, [fp, #-0x78]
    // 0x8ec2b8: r2 = LoadClassIdInstr(r0)
    //     0x8ec2b8: ldur            x2, [x0, #-1]
    //     0x8ec2bc: ubfx            x2, x2, #0xc, #0x14
    // 0x8ec2c0: r16 = <ArticleInsertion>
    //     0x8ec2c0: ldr             x16, [PP, #0x7b88]  ; [pp+0x7b88] TypeArguments: <ArticleInsertion>
    // 0x8ec2c4: stp             x0, x16, [SP, #8]
    // 0x8ec2c8: str             x1, [SP]
    // 0x8ec2cc: mov             x0, x2
    // 0x8ec2d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ec2d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ec2d4: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ec2d4: movz            x17, #0xf28c
    //     0x8ec2d8: add             lr, x0, x17
    //     0x8ec2dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec2e0: blr             lr
    // 0x8ec2e4: r1 = LoadClassIdInstr(r0)
    //     0x8ec2e4: ldur            x1, [x0, #-1]
    //     0x8ec2e8: ubfx            x1, x1, #0xc, #0x14
    // 0x8ec2ec: mov             x16, x0
    // 0x8ec2f0: mov             x0, x1
    // 0x8ec2f4: mov             x1, x16
    // 0x8ec2f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ec2f8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ec2fc: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ec2fc: movz            x17, #0xd889
    //     0x8ec300: add             lr, x0, x17
    //     0x8ec304: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec308: blr             lr
    // 0x8ec30c: mov             x4, x0
    // 0x8ec310: ldur            x3, [fp, #-8]
    // 0x8ec314: stur            x4, [fp, #-0x78]
    // 0x8ec318: r0 = LoadClassIdInstr(r3)
    //     0x8ec318: ldur            x0, [x3, #-1]
    //     0x8ec31c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec320: mov             x1, x3
    // 0x8ec324: r2 = "youtube_id"
    //     0x8ec324: add             x2, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x8ec328: ldr             x2, [x2, #0x3f0]
    // 0x8ec32c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec32c: sub             lr, x0, #0x114
    //     0x8ec330: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec334: blr             lr
    // 0x8ec338: mov             x3, x0
    // 0x8ec33c: r2 = Null
    //     0x8ec33c: mov             x2, NULL
    // 0x8ec340: r1 = Null
    //     0x8ec340: mov             x1, NULL
    // 0x8ec344: stur            x3, [fp, #-0x80]
    // 0x8ec348: r4 = 60
    //     0x8ec348: movz            x4, #0x3c
    // 0x8ec34c: branchIfSmi(r0, 0x8ec358)
    //     0x8ec34c: tbz             w0, #0, #0x8ec358
    // 0x8ec350: r4 = LoadClassIdInstr(r0)
    //     0x8ec350: ldur            x4, [x0, #-1]
    //     0x8ec354: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec358: sub             x4, x4, #0x5e
    // 0x8ec35c: cmp             x4, #1
    // 0x8ec360: b.ls            #0x8ec374
    // 0x8ec364: r8 = String?
    //     0x8ec364: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ec368: r3 = Null
    //     0x8ec368: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e28] Null
    //     0x8ec36c: ldr             x3, [x3, #0xe28]
    // 0x8ec370: r0 = String?()
    //     0x8ec370: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ec374: ldur            x3, [fp, #-8]
    // 0x8ec378: r0 = LoadClassIdInstr(r3)
    //     0x8ec378: ldur            x0, [x3, #-1]
    //     0x8ec37c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec380: mov             x1, x3
    // 0x8ec384: r2 = "authors"
    //     0x8ec384: add             x2, PP, #0x40, lsl #12  ; [pp+0x40e38] "authors"
    //     0x8ec388: ldr             x2, [x2, #0xe38]
    // 0x8ec38c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec38c: sub             lr, x0, #0x114
    //     0x8ec390: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec394: blr             lr
    // 0x8ec398: cmp             w0, NULL
    // 0x8ec39c: b.ne            #0x8ec3b4
    // 0x8ec3a0: r1 = <Author>
    //     0x8ec3a0: ldr             x1, [PP, #0x7b70]  ; [pp+0x7b70] TypeArguments: <Author>
    // 0x8ec3a4: r2 = 0
    //     0x8ec3a4: movz            x2, #0
    // 0x8ec3a8: r0 = _GrowableList()
    //     0x8ec3a8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ec3ac: mov             x19, x0
    // 0x8ec3b0: b               #0x8ec480
    // 0x8ec3b4: ldur            x1, [fp, #-8]
    // 0x8ec3b8: r0 = LoadClassIdInstr(r1)
    //     0x8ec3b8: ldur            x0, [x1, #-1]
    //     0x8ec3bc: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec3c0: r2 = "authors"
    //     0x8ec3c0: add             x2, PP, #0x40, lsl #12  ; [pp+0x40e38] "authors"
    //     0x8ec3c4: ldr             x2, [x2, #0xe38]
    // 0x8ec3c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec3c8: sub             lr, x0, #0x114
    //     0x8ec3cc: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec3d0: blr             lr
    // 0x8ec3d4: mov             x3, x0
    // 0x8ec3d8: r2 = Null
    //     0x8ec3d8: mov             x2, NULL
    // 0x8ec3dc: r1 = Null
    //     0x8ec3dc: mov             x1, NULL
    // 0x8ec3e0: stur            x3, [fp, #-8]
    // 0x8ec3e4: r4 = 60
    //     0x8ec3e4: movz            x4, #0x3c
    // 0x8ec3e8: branchIfSmi(r0, 0x8ec3f4)
    //     0x8ec3e8: tbz             w0, #0, #0x8ec3f4
    // 0x8ec3ec: r4 = LoadClassIdInstr(r0)
    //     0x8ec3ec: ldur            x4, [x0, #-1]
    //     0x8ec3f0: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec3f4: sub             x4, x4, #0x5a
    // 0x8ec3f8: cmp             x4, #2
    // 0x8ec3fc: b.ls            #0x8ec410
    // 0x8ec400: r8 = List
    //     0x8ec400: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0x8ec404: r3 = Null
    //     0x8ec404: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e40] Null
    //     0x8ec408: ldr             x3, [x3, #0xe40]
    // 0x8ec40c: r0 = List()
    //     0x8ec40c: bl              #0xed6b40  ; IsType_List_Stub
    // 0x8ec410: r1 = Function '<anonymous closure>': static.
    //     0x8ec410: add             x1, PP, #0x40, lsl #12  ; [pp+0x40e50] AnonymousClosure: static (0x8ec7c0), in [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap (0x8ebb40)
    //     0x8ec414: ldr             x1, [x1, #0xe50]
    // 0x8ec418: r2 = Null
    //     0x8ec418: mov             x2, NULL
    // 0x8ec41c: r0 = AllocateClosure()
    //     0x8ec41c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ec420: mov             x1, x0
    // 0x8ec424: ldur            x0, [fp, #-8]
    // 0x8ec428: r2 = LoadClassIdInstr(r0)
    //     0x8ec428: ldur            x2, [x0, #-1]
    //     0x8ec42c: ubfx            x2, x2, #0xc, #0x14
    // 0x8ec430: r16 = <Author>
    //     0x8ec430: ldr             x16, [PP, #0x7b70]  ; [pp+0x7b70] TypeArguments: <Author>
    // 0x8ec434: stp             x0, x16, [SP, #8]
    // 0x8ec438: str             x1, [SP]
    // 0x8ec43c: mov             x0, x2
    // 0x8ec440: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ec440: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ec444: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ec444: movz            x17, #0xf28c
    //     0x8ec448: add             lr, x0, x17
    //     0x8ec44c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec450: blr             lr
    // 0x8ec454: r1 = LoadClassIdInstr(r0)
    //     0x8ec454: ldur            x1, [x0, #-1]
    //     0x8ec458: ubfx            x1, x1, #0xc, #0x14
    // 0x8ec45c: mov             x16, x0
    // 0x8ec460: mov             x0, x1
    // 0x8ec464: mov             x1, x16
    // 0x8ec468: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ec468: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ec46c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ec46c: movz            x17, #0xd889
    //     0x8ec470: add             lr, x0, x17
    //     0x8ec474: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec478: blr             lr
    // 0x8ec47c: mov             x19, x0
    // 0x8ec480: ldur            x14, [fp, #-0x10]
    // 0x8ec484: ldur            x13, [fp, #-0x18]
    // 0x8ec488: ldur            x12, [fp, #-0x20]
    // 0x8ec48c: ldur            x11, [fp, #-0x28]
    // 0x8ec490: ldur            x10, [fp, #-0x30]
    // 0x8ec494: ldur            x9, [fp, #-0x38]
    // 0x8ec498: ldur            x8, [fp, #-0x40]
    // 0x8ec49c: ldur            x7, [fp, #-0x48]
    // 0x8ec4a0: ldur            x6, [fp, #-0x50]
    // 0x8ec4a4: ldur            x5, [fp, #-0x58]
    // 0x8ec4a8: ldur            x4, [fp, #-0x60]
    // 0x8ec4ac: ldur            x3, [fp, #-0x68]
    // 0x8ec4b0: ldur            x2, [fp, #-0x70]
    // 0x8ec4b4: ldur            x1, [fp, #-0x78]
    // 0x8ec4b8: ldur            x0, [fp, #-0x80]
    // 0x8ec4bc: stur            x19, [fp, #-8]
    // 0x8ec4c0: r20 = LoadInt32Instr(r14)
    //     0x8ec4c0: sbfx            x20, x14, #1, #0x1f
    //     0x8ec4c4: tbz             w14, #0, #0x8ec4cc
    //     0x8ec4c8: ldur            x20, [x14, #7]
    // 0x8ec4cc: stur            x20, [fp, #-0x88]
    // 0x8ec4d0: r0 = ArticleDetail()
    //     0x8ec4d0: bl              #0x8ec568  ; AllocateArticleDetailStub -> ArticleDetail (size=0x4c)
    // 0x8ec4d4: ldur            x1, [fp, #-0x88]
    // 0x8ec4d8: StoreField: r0->field_7 = r1
    //     0x8ec4d8: stur            x1, [x0, #7]
    // 0x8ec4dc: ldur            x1, [fp, #-0x18]
    // 0x8ec4e0: StoreField: r0->field_f = r1
    //     0x8ec4e0: stur            w1, [x0, #0xf]
    // 0x8ec4e4: ldur            x1, [fp, #-0x28]
    // 0x8ec4e8: ArrayStore: r0[0] = r1  ; List_4
    //     0x8ec4e8: stur            w1, [x0, #0x17]
    // 0x8ec4ec: ldur            x1, [fp, #-0x30]
    // 0x8ec4f0: StoreField: r0->field_1b = r1
    //     0x8ec4f0: stur            w1, [x0, #0x1b]
    // 0x8ec4f4: ldur            x1, [fp, #-0x38]
    // 0x8ec4f8: StoreField: r0->field_1f = r1
    //     0x8ec4f8: stur            w1, [x0, #0x1f]
    // 0x8ec4fc: ldur            x1, [fp, #-0x40]
    // 0x8ec500: StoreField: r0->field_23 = r1
    //     0x8ec500: stur            w1, [x0, #0x23]
    // 0x8ec504: ldur            x1, [fp, #-0x50]
    // 0x8ec508: StoreField: r0->field_2b = r1
    //     0x8ec508: stur            w1, [x0, #0x2b]
    // 0x8ec50c: ldur            x1, [fp, #-0x20]
    // 0x8ec510: StoreField: r0->field_13 = r1
    //     0x8ec510: stur            w1, [x0, #0x13]
    // 0x8ec514: ldur            x1, [fp, #-0x60]
    // 0x8ec518: StoreField: r0->field_33 = r1
    //     0x8ec518: stur            w1, [x0, #0x33]
    // 0x8ec51c: ldur            x1, [fp, #-0x48]
    // 0x8ec520: StoreField: r0->field_27 = r1
    //     0x8ec520: stur            w1, [x0, #0x27]
    // 0x8ec524: ldur            x1, [fp, #-0x58]
    // 0x8ec528: StoreField: r0->field_2f = r1
    //     0x8ec528: stur            w1, [x0, #0x2f]
    // 0x8ec52c: ldur            x1, [fp, #-0x68]
    // 0x8ec530: StoreField: r0->field_37 = r1
    //     0x8ec530: stur            w1, [x0, #0x37]
    // 0x8ec534: ldur            x1, [fp, #-0x70]
    // 0x8ec538: StoreField: r0->field_3b = r1
    //     0x8ec538: stur            w1, [x0, #0x3b]
    // 0x8ec53c: ldur            x1, [fp, #-0x78]
    // 0x8ec540: StoreField: r0->field_3f = r1
    //     0x8ec540: stur            w1, [x0, #0x3f]
    // 0x8ec544: ldur            x1, [fp, #-0x80]
    // 0x8ec548: StoreField: r0->field_43 = r1
    //     0x8ec548: stur            w1, [x0, #0x43]
    // 0x8ec54c: ldur            x1, [fp, #-8]
    // 0x8ec550: StoreField: r0->field_47 = r1
    //     0x8ec550: stur            w1, [x0, #0x47]
    // 0x8ec554: LeaveFrame
    //     0x8ec554: mov             SP, fp
    //     0x8ec558: ldp             fp, lr, [SP], #0x10
    // 0x8ec55c: ret
    //     0x8ec55c: ret             
    // 0x8ec560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ec560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ec564: b               #0x8ebb60
  }
  [closure] static Author <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ec7c0, size: 0x50
    // 0x8ec7c0: EnterFrame
    //     0x8ec7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ec7c4: mov             fp, SP
    // 0x8ec7c8: CheckStackOverflow
    //     0x8ec7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ec7cc: cmp             SP, x16
    //     0x8ec7d0: b.ls            #0x8ec808
    // 0x8ec7d4: ldr             x0, [fp, #0x10]
    // 0x8ec7d8: r2 = Null
    //     0x8ec7d8: mov             x2, NULL
    // 0x8ec7dc: r1 = Null
    //     0x8ec7dc: mov             x1, NULL
    // 0x8ec7e0: r8 = Map<String, dynamic>
    //     0x8ec7e0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ec7e4: r3 = Null
    //     0x8ec7e4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e58] Null
    //     0x8ec7e8: ldr             x3, [x3, #0xe58]
    // 0x8ec7ec: r0 = Map<String, dynamic>()
    //     0x8ec7ec: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ec7f0: ldr             x2, [fp, #0x10]
    // 0x8ec7f4: r1 = Null
    //     0x8ec7f4: mov             x1, NULL
    // 0x8ec7f8: r0 = Author.fromMap()
    //     0x8ec7f8: bl              #0x8ec574  ; [package:nuonline/app/data/models/author.dart] Author::Author.fromMap
    // 0x8ec7fc: LeaveFrame
    //     0x8ec7fc: mov             SP, fp
    //     0x8ec800: ldp             fp, lr, [SP], #0x10
    // 0x8ec804: ret
    //     0x8ec804: ret             
    // 0x8ec808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ec808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ec80c: b               #0x8ec7d4
  }
  [closure] static ArticleInsertion <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ec810, size: 0x50
    // 0x8ec810: EnterFrame
    //     0x8ec810: stp             fp, lr, [SP, #-0x10]!
    //     0x8ec814: mov             fp, SP
    // 0x8ec818: CheckStackOverflow
    //     0x8ec818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ec81c: cmp             SP, x16
    //     0x8ec820: b.ls            #0x8ec858
    // 0x8ec824: ldr             x0, [fp, #0x10]
    // 0x8ec828: r2 = Null
    //     0x8ec828: mov             x2, NULL
    // 0x8ec82c: r1 = Null
    //     0x8ec82c: mov             x1, NULL
    // 0x8ec830: r8 = Map<String, dynamic>
    //     0x8ec830: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ec834: r3 = Null
    //     0x8ec834: add             x3, PP, #0x40, lsl #12  ; [pp+0x40e68] Null
    //     0x8ec838: ldr             x3, [x3, #0xe68]
    // 0x8ec83c: r0 = Map<String, dynamic>()
    //     0x8ec83c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ec840: ldr             x2, [fp, #0x10]
    // 0x8ec844: r1 = Null
    //     0x8ec844: mov             x1, NULL
    // 0x8ec848: r0 = ArticleInsertion.fromMap()
    //     0x8ec848: bl              #0x8ec860  ; [package:nuonline/app/data/models/article.dart] ArticleInsertion::ArticleInsertion.fromMap
    // 0x8ec84c: LeaveFrame
    //     0x8ec84c: mov             SP, fp
    //     0x8ec850: ldp             fp, lr, [SP], #0x10
    // 0x8ec854: ret
    //     0x8ec854: ret             
    // 0x8ec858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ec858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ec85c: b               #0x8ec824
  }
  [closure] static ArticleInsertion <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8eca54, size: 0x50
    // 0x8eca54: EnterFrame
    //     0x8eca54: stp             fp, lr, [SP, #-0x10]!
    //     0x8eca58: mov             fp, SP
    // 0x8eca5c: CheckStackOverflow
    //     0x8eca5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eca60: cmp             SP, x16
    //     0x8eca64: b.ls            #0x8eca9c
    // 0x8eca68: ldr             x0, [fp, #0x10]
    // 0x8eca6c: r2 = Null
    //     0x8eca6c: mov             x2, NULL
    // 0x8eca70: r1 = Null
    //     0x8eca70: mov             x1, NULL
    // 0x8eca74: r8 = Map<String, dynamic>
    //     0x8eca74: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8eca78: r3 = Null
    //     0x8eca78: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ec0] Null
    //     0x8eca7c: ldr             x3, [x3, #0xec0]
    // 0x8eca80: r0 = Map<String, dynamic>()
    //     0x8eca80: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8eca84: ldr             x2, [fp, #0x10]
    // 0x8eca88: r1 = Null
    //     0x8eca88: mov             x1, NULL
    // 0x8eca8c: r0 = ArticleInsertion.fromMap()
    //     0x8eca8c: bl              #0x8ec860  ; [package:nuonline/app/data/models/article.dart] ArticleInsertion::ArticleInsertion.fromMap
    // 0x8eca90: LeaveFrame
    //     0x8eca90: mov             SP, fp
    //     0x8eca94: ldp             fp, lr, [SP], #0x10
    // 0x8eca98: ret
    //     0x8eca98: ret             
    // 0x8eca9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eca9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecaa0: b               #0x8eca68
  }
  [closure] static Article <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ecaa4, size: 0x50
    // 0x8ecaa4: EnterFrame
    //     0x8ecaa4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecaa8: mov             fp, SP
    // 0x8ecaac: CheckStackOverflow
    //     0x8ecaac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecab0: cmp             SP, x16
    //     0x8ecab4: b.ls            #0x8ecaec
    // 0x8ecab8: ldr             x0, [fp, #0x10]
    // 0x8ecabc: r2 = Null
    //     0x8ecabc: mov             x2, NULL
    // 0x8ecac0: r1 = Null
    //     0x8ecac0: mov             x1, NULL
    // 0x8ecac4: r8 = Map<String, dynamic>
    //     0x8ecac4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ecac8: r3 = Null
    //     0x8ecac8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ed0] Null
    //     0x8ecacc: ldr             x3, [x3, #0xed0]
    // 0x8ecad0: r0 = Map<String, dynamic>()
    //     0x8ecad0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ecad4: ldr             x2, [fp, #0x10]
    // 0x8ecad8: r1 = Null
    //     0x8ecad8: mov             x1, NULL
    // 0x8ecadc: r0 = Article.fromMap()
    //     0x8ecadc: bl              #0x8ecaf4  ; [package:nuonline/app/data/models/article.dart] Article::Article.fromMap
    // 0x8ecae0: LeaveFrame
    //     0x8ecae0: mov             SP, fp
    //     0x8ecae4: ldp             fp, lr, [SP], #0x10
    // 0x8ecae8: ret
    //     0x8ecae8: ret             
    // 0x8ecaec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecaec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecaf0: b               #0x8ecab8
  }
  [closure] static Tag <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ece08, size: 0x50
    // 0x8ece08: EnterFrame
    //     0x8ece08: stp             fp, lr, [SP, #-0x10]!
    //     0x8ece0c: mov             fp, SP
    // 0x8ece10: CheckStackOverflow
    //     0x8ece10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ece14: cmp             SP, x16
    //     0x8ece18: b.ls            #0x8ece50
    // 0x8ece1c: ldr             x0, [fp, #0x10]
    // 0x8ece20: r2 = Null
    //     0x8ece20: mov             x2, NULL
    // 0x8ece24: r1 = Null
    //     0x8ece24: mov             x1, NULL
    // 0x8ece28: r8 = Map<String, dynamic>
    //     0x8ece28: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ece2c: r3 = Null
    //     0x8ece2c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ee0] Null
    //     0x8ece30: ldr             x3, [x3, #0xee0]
    // 0x8ece34: r0 = Map<String, dynamic>()
    //     0x8ece34: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ece38: ldr             x2, [fp, #0x10]
    // 0x8ece3c: r1 = Null
    //     0x8ece3c: mov             x1, NULL
    // 0x8ece40: r0 = Tag.fromMap()
    //     0x8ece40: bl              #0x8a9bd4  ; [package:nuonline/app/data/models/tag.dart] Tag::Tag.fromMap
    // 0x8ece44: LeaveFrame
    //     0x8ece44: mov             SP, fp
    //     0x8ece48: ldp             fp, lr, [SP], #0x10
    // 0x8ece4c: ret
    //     0x8ece4c: ret             
    // 0x8ece50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ece50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ece54: b               #0x8ece1c
  }
  get _ publishedAt(/* No info */) {
    // ** addr: 0xa35938, size: 0x78
    // 0xa35938: EnterFrame
    //     0xa35938: stp             fp, lr, [SP, #-0x10]!
    //     0xa3593c: mov             fp, SP
    // 0xa35940: AllocStack(0x8)
    //     0xa35940: sub             SP, SP, #8
    // 0xa35944: CheckStackOverflow
    //     0xa35944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa35948: cmp             SP, x16
    //     0xa3594c: b.ls            #0xa359a8
    // 0xa35950: LoadField: r0 = r1->field_1f
    //     0xa35950: ldur            w0, [x1, #0x1f]
    // 0xa35954: DecompressPointer r0
    //     0xa35954: add             x0, x0, HEAP, lsl #32
    // 0xa35958: mov             x1, x0
    // 0xa3595c: stur            x0, [fp, #-8]
    // 0xa35960: r0 = tryParse()
    //     0xa35960: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0xa35964: cmp             w0, NULL
    // 0xa35968: b.ne            #0xa35974
    // 0xa3596c: r1 = Null
    //     0xa3596c: mov             x1, NULL
    // 0xa35970: b               #0xa35988
    // 0xa35974: mov             x1, x0
    // 0xa35978: r0 = toLocal()
    //     0xa35978: bl              #0x82c4e0  ; [dart:core] DateTime::toLocal
    // 0xa3597c: mov             x1, x0
    // 0xa35980: r0 = DateTimeExtensions.humanizeWithTime()
    //     0xa35980: bl              #0xa359b0  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanizeWithTime
    // 0xa35984: mov             x1, x0
    // 0xa35988: cmp             w1, NULL
    // 0xa3598c: b.ne            #0xa35998
    // 0xa35990: ldur            x0, [fp, #-8]
    // 0xa35994: b               #0xa3599c
    // 0xa35998: mov             x0, x1
    // 0xa3599c: LeaveFrame
    //     0xa3599c: mov             SP, fp
    //     0xa359a0: ldp             fp, lr, [SP], #0x10
    // 0xa359a4: ret
    //     0xa359a4: ret             
    // 0xa359a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa359a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa359ac: b               #0xa35950
  }
  _ toArticle(/* No info */) {
    // ** addr: 0xacfca8, size: 0xa4
    // 0xacfca8: EnterFrame
    //     0xacfca8: stp             fp, lr, [SP, #-0x10]!
    //     0xacfcac: mov             fp, SP
    // 0xacfcb0: AllocStack(0x38)
    //     0xacfcb0: sub             SP, SP, #0x38
    // 0xacfcb4: LoadField: r0 = r1->field_7
    //     0xacfcb4: ldur            x0, [x1, #7]
    // 0xacfcb8: stur            x0, [fp, #-0x38]
    // 0xacfcbc: LoadField: r2 = r1->field_f
    //     0xacfcbc: ldur            w2, [x1, #0xf]
    // 0xacfcc0: DecompressPointer r2
    //     0xacfcc0: add             x2, x2, HEAP, lsl #32
    // 0xacfcc4: stur            x2, [fp, #-0x30]
    // 0xacfcc8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xacfcc8: ldur            w3, [x1, #0x17]
    // 0xacfccc: DecompressPointer r3
    //     0xacfccc: add             x3, x3, HEAP, lsl #32
    // 0xacfcd0: stur            x3, [fp, #-0x28]
    // 0xacfcd4: LoadField: r4 = r1->field_1b
    //     0xacfcd4: ldur            w4, [x1, #0x1b]
    // 0xacfcd8: DecompressPointer r4
    //     0xacfcd8: add             x4, x4, HEAP, lsl #32
    // 0xacfcdc: stur            x4, [fp, #-0x20]
    // 0xacfce0: LoadField: r5 = r1->field_1f
    //     0xacfce0: ldur            w5, [x1, #0x1f]
    // 0xacfce4: DecompressPointer r5
    //     0xacfce4: add             x5, x5, HEAP, lsl #32
    // 0xacfce8: stur            x5, [fp, #-0x18]
    // 0xacfcec: LoadField: r6 = r1->field_37
    //     0xacfcec: ldur            w6, [x1, #0x37]
    // 0xacfcf0: DecompressPointer r6
    //     0xacfcf0: add             x6, x6, HEAP, lsl #32
    // 0xacfcf4: stur            x6, [fp, #-0x10]
    // 0xacfcf8: LoadField: r7 = r1->field_13
    //     0xacfcf8: ldur            w7, [x1, #0x13]
    // 0xacfcfc: DecompressPointer r7
    //     0xacfcfc: add             x7, x7, HEAP, lsl #32
    // 0xacfd00: stur            x7, [fp, #-8]
    // 0xacfd04: r0 = Article()
    //     0xacfd04: bl              #0x8ecdfc  ; AllocateArticleStub -> Article (size=0x28)
    // 0xacfd08: ldur            x1, [fp, #-0x38]
    // 0xacfd0c: StoreField: r0->field_7 = r1
    //     0xacfd0c: stur            x1, [x0, #7]
    // 0xacfd10: ldur            x1, [fp, #-0x30]
    // 0xacfd14: StoreField: r0->field_f = r1
    //     0xacfd14: stur            w1, [x0, #0xf]
    // 0xacfd18: ldur            x1, [fp, #-0x28]
    // 0xacfd1c: ArrayStore: r0[0] = r1  ; List_4
    //     0xacfd1c: stur            w1, [x0, #0x17]
    // 0xacfd20: ldur            x1, [fp, #-0x20]
    // 0xacfd24: StoreField: r0->field_1b = r1
    //     0xacfd24: stur            w1, [x0, #0x1b]
    // 0xacfd28: ldur            x1, [fp, #-0x18]
    // 0xacfd2c: StoreField: r0->field_1f = r1
    //     0xacfd2c: stur            w1, [x0, #0x1f]
    // 0xacfd30: ldur            x1, [fp, #-8]
    // 0xacfd34: StoreField: r0->field_13 = r1
    //     0xacfd34: stur            w1, [x0, #0x13]
    // 0xacfd38: ldur            x1, [fp, #-0x10]
    // 0xacfd3c: StoreField: r0->field_23 = r1
    //     0xacfd3c: stur            w1, [x0, #0x23]
    // 0xacfd40: LeaveFrame
    //     0xacfd40: mov             SP, fp
    //     0xacfd44: ldp             fp, lr, [SP], #0x10
    // 0xacfd48: ret
    //     0xacfd48: ret             
  }
  get _ shareableText(/* No info */) {
    // ** addr: 0xbc2c38, size: 0x84
    // 0xbc2c38: EnterFrame
    //     0xbc2c38: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2c3c: mov             fp, SP
    // 0xbc2c40: AllocStack(0x18)
    //     0xbc2c40: sub             SP, SP, #0x18
    // 0xbc2c44: SetupParameters(ArticleDetail this /* r1 => r0, fp-0x10 */)
    //     0xbc2c44: mov             x0, x1
    //     0xbc2c48: stur            x1, [fp, #-0x10]
    // 0xbc2c4c: CheckStackOverflow
    //     0xbc2c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2c50: cmp             SP, x16
    //     0xbc2c54: b.ls            #0xbc2cb4
    // 0xbc2c58: LoadField: r3 = r0->field_f
    //     0xbc2c58: ldur            w3, [x0, #0xf]
    // 0xbc2c5c: DecompressPointer r3
    //     0xbc2c5c: add             x3, x3, HEAP, lsl #32
    // 0xbc2c60: stur            x3, [fp, #-8]
    // 0xbc2c64: r1 = Null
    //     0xbc2c64: mov             x1, NULL
    // 0xbc2c68: r2 = 8
    //     0xbc2c68: movz            x2, #0x8
    // 0xbc2c6c: r0 = AllocateArray()
    //     0xbc2c6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2c70: mov             x1, x0
    // 0xbc2c74: ldur            x0, [fp, #-8]
    // 0xbc2c78: StoreField: r1->field_f = r0
    //     0xbc2c78: stur            w0, [x1, #0xf]
    // 0xbc2c7c: r16 = "\n\n"
    //     0xbc2c7c: ldr             x16, [PP, #0x3360]  ; [pp+0x3360] "\n\n"
    // 0xbc2c80: StoreField: r1->field_13 = r16
    //     0xbc2c80: stur            w16, [x1, #0x13]
    // 0xbc2c84: ldur            x0, [fp, #-0x10]
    // 0xbc2c88: LoadField: r2 = r0->field_23
    //     0xbc2c88: ldur            w2, [x0, #0x23]
    // 0xbc2c8c: DecompressPointer r2
    //     0xbc2c8c: add             x2, x2, HEAP, lsl #32
    // 0xbc2c90: ArrayStore: r1[0] = r2  ; List_4
    //     0xbc2c90: stur            w2, [x1, #0x17]
    // 0xbc2c94: r16 = "\n___\nDownload NU Online Super App, aplikasi keislaman terlengkap!\nhttps://nu.or.id/superapp (Android/iOS)"
    //     0xbc2c94: add             x16, PP, #0x29, lsl #12  ; [pp+0x29600] "\n___\nDownload NU Online Super App, aplikasi keislaman terlengkap!\nhttps://nu.or.id/superapp (Android/iOS)"
    //     0xbc2c98: ldr             x16, [x16, #0x600]
    // 0xbc2c9c: StoreField: r1->field_1b = r16
    //     0xbc2c9c: stur            w16, [x1, #0x1b]
    // 0xbc2ca0: str             x1, [SP]
    // 0xbc2ca4: r0 = _interpolate()
    //     0xbc2ca4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2ca8: LeaveFrame
    //     0xbc2ca8: mov             SP, fp
    //     0xbc2cac: ldp             fp, lr, [SP], #0x10
    // 0xbc2cb0: ret
    //     0xbc2cb0: ret             
    // 0xbc2cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2cb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2cb8: b               #0xbc2c58
  }
}

// class id: 5599, size: 0x28, field offset: 0x8
//   const constructor, 
class Article extends Equatable {

  factory Article Article.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0x8ecaf4, size: 0x308
    // 0x8ecaf4: EnterFrame
    //     0x8ecaf4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecaf8: mov             fp, SP
    // 0x8ecafc: AllocStack(0x40)
    //     0x8ecafc: sub             SP, SP, #0x40
    // 0x8ecb00: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ecb00: mov             x3, x2
    //     0x8ecb04: stur            x2, [fp, #-8]
    // 0x8ecb08: CheckStackOverflow
    //     0x8ecb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecb0c: cmp             SP, x16
    //     0x8ecb10: b.ls            #0x8ecdf4
    // 0x8ecb14: r0 = LoadClassIdInstr(r3)
    //     0x8ecb14: ldur            x0, [x3, #-1]
    //     0x8ecb18: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecb1c: mov             x1, x3
    // 0x8ecb20: r2 = "id"
    //     0x8ecb20: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ecb24: ldr             x2, [x2, #0x740]
    // 0x8ecb28: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecb28: sub             lr, x0, #0x114
    //     0x8ecb2c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecb30: blr             lr
    // 0x8ecb34: mov             x3, x0
    // 0x8ecb38: r2 = Null
    //     0x8ecb38: mov             x2, NULL
    // 0x8ecb3c: r1 = Null
    //     0x8ecb3c: mov             x1, NULL
    // 0x8ecb40: stur            x3, [fp, #-0x10]
    // 0x8ecb44: branchIfSmi(r0, 0x8ecb6c)
    //     0x8ecb44: tbz             w0, #0, #0x8ecb6c
    // 0x8ecb48: r4 = LoadClassIdInstr(r0)
    //     0x8ecb48: ldur            x4, [x0, #-1]
    //     0x8ecb4c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ecb50: sub             x4, x4, #0x3c
    // 0x8ecb54: cmp             x4, #1
    // 0x8ecb58: b.ls            #0x8ecb6c
    // 0x8ecb5c: r8 = int
    //     0x8ecb5c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ecb60: r3 = Null
    //     0x8ecb60: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d908] Null
    //     0x8ecb64: ldr             x3, [x3, #0x908]
    // 0x8ecb68: r0 = int()
    //     0x8ecb68: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ecb6c: ldur            x3, [fp, #-8]
    // 0x8ecb70: r0 = LoadClassIdInstr(r3)
    //     0x8ecb70: ldur            x0, [x3, #-1]
    //     0x8ecb74: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecb78: mov             x1, x3
    // 0x8ecb7c: r2 = "title"
    //     0x8ecb7c: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8ecb80: ldr             x2, [x2, #0x748]
    // 0x8ecb84: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecb84: sub             lr, x0, #0x114
    //     0x8ecb88: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecb8c: blr             lr
    // 0x8ecb90: mov             x3, x0
    // 0x8ecb94: r2 = Null
    //     0x8ecb94: mov             x2, NULL
    // 0x8ecb98: r1 = Null
    //     0x8ecb98: mov             x1, NULL
    // 0x8ecb9c: stur            x3, [fp, #-0x18]
    // 0x8ecba0: r4 = 60
    //     0x8ecba0: movz            x4, #0x3c
    // 0x8ecba4: branchIfSmi(r0, 0x8ecbb0)
    //     0x8ecba4: tbz             w0, #0, #0x8ecbb0
    // 0x8ecba8: r4 = LoadClassIdInstr(r0)
    //     0x8ecba8: ldur            x4, [x0, #-1]
    //     0x8ecbac: ubfx            x4, x4, #0xc, #0x14
    // 0x8ecbb0: sub             x4, x4, #0x5e
    // 0x8ecbb4: cmp             x4, #1
    // 0x8ecbb8: b.ls            #0x8ecbcc
    // 0x8ecbbc: r8 = String
    //     0x8ecbbc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ecbc0: r3 = Null
    //     0x8ecbc0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d918] Null
    //     0x8ecbc4: ldr             x3, [x3, #0x918]
    // 0x8ecbc8: r0 = String()
    //     0x8ecbc8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ecbcc: ldur            x3, [fp, #-8]
    // 0x8ecbd0: r0 = LoadClassIdInstr(r3)
    //     0x8ecbd0: ldur            x0, [x3, #-1]
    //     0x8ecbd4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecbd8: mov             x1, x3
    // 0x8ecbdc: r2 = "slug"
    //     0x8ecbdc: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x8ecbe0: ldr             x2, [x2, #0x9a8]
    // 0x8ecbe4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecbe4: sub             lr, x0, #0x114
    //     0x8ecbe8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecbec: blr             lr
    // 0x8ecbf0: mov             x3, x0
    // 0x8ecbf4: r2 = Null
    //     0x8ecbf4: mov             x2, NULL
    // 0x8ecbf8: r1 = Null
    //     0x8ecbf8: mov             x1, NULL
    // 0x8ecbfc: stur            x3, [fp, #-0x20]
    // 0x8ecc00: r4 = 60
    //     0x8ecc00: movz            x4, #0x3c
    // 0x8ecc04: branchIfSmi(r0, 0x8ecc10)
    //     0x8ecc04: tbz             w0, #0, #0x8ecc10
    // 0x8ecc08: r4 = LoadClassIdInstr(r0)
    //     0x8ecc08: ldur            x4, [x0, #-1]
    //     0x8ecc0c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ecc10: sub             x4, x4, #0x5e
    // 0x8ecc14: cmp             x4, #1
    // 0x8ecc18: b.ls            #0x8ecc2c
    // 0x8ecc1c: r8 = String?
    //     0x8ecc1c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ecc20: r3 = Null
    //     0x8ecc20: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d928] Null
    //     0x8ecc24: ldr             x3, [x3, #0x928]
    // 0x8ecc28: r0 = String?()
    //     0x8ecc28: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ecc2c: ldur            x3, [fp, #-8]
    // 0x8ecc30: r0 = LoadClassIdInstr(r3)
    //     0x8ecc30: ldur            x0, [x3, #-1]
    //     0x8ecc34: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecc38: mov             x1, x3
    // 0x8ecc3c: r2 = "image"
    //     0x8ecc3c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x8ecc40: ldr             x2, [x2, #0x520]
    // 0x8ecc44: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecc44: sub             lr, x0, #0x114
    //     0x8ecc48: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecc4c: blr             lr
    // 0x8ecc50: mov             x3, x0
    // 0x8ecc54: r2 = Null
    //     0x8ecc54: mov             x2, NULL
    // 0x8ecc58: r1 = Null
    //     0x8ecc58: mov             x1, NULL
    // 0x8ecc5c: stur            x3, [fp, #-0x28]
    // 0x8ecc60: r8 = Map<String, dynamic>
    //     0x8ecc60: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ecc64: r3 = Null
    //     0x8ecc64: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d938] Null
    //     0x8ecc68: ldr             x3, [x3, #0x938]
    // 0x8ecc6c: r0 = Map<String, dynamic>()
    //     0x8ecc6c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ecc70: ldur            x2, [fp, #-0x28]
    // 0x8ecc74: r1 = Null
    //     0x8ecc74: mov             x1, NULL
    // 0x8ecc78: r0 = Image.fromMap()
    //     0x8ecc78: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x8ecc7c: mov             x4, x0
    // 0x8ecc80: ldur            x3, [fp, #-8]
    // 0x8ecc84: stur            x4, [fp, #-0x28]
    // 0x8ecc88: r0 = LoadClassIdInstr(r3)
    //     0x8ecc88: ldur            x0, [x3, #-1]
    //     0x8ecc8c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecc90: mov             x1, x3
    // 0x8ecc94: r2 = "category"
    //     0x8ecc94: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x8ecc98: ldr             x2, [x2, #0x960]
    // 0x8ecc9c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecc9c: sub             lr, x0, #0x114
    //     0x8ecca0: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecca4: blr             lr
    // 0x8ecca8: mov             x3, x0
    // 0x8eccac: r2 = Null
    //     0x8eccac: mov             x2, NULL
    // 0x8eccb0: r1 = Null
    //     0x8eccb0: mov             x1, NULL
    // 0x8eccb4: stur            x3, [fp, #-0x30]
    // 0x8eccb8: r8 = Map<String, dynamic>
    //     0x8eccb8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8eccbc: r3 = Null
    //     0x8eccbc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d948] Null
    //     0x8eccc0: ldr             x3, [x3, #0x948]
    // 0x8eccc4: r0 = Map<String, dynamic>()
    //     0x8eccc4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8eccc8: ldur            x2, [fp, #-0x30]
    // 0x8ecccc: r1 = Null
    //     0x8ecccc: mov             x1, NULL
    // 0x8eccd0: r0 = Category.fromMap()
    //     0x8eccd0: bl              #0x8aa92c  ; [package:nuonline/app/data/models/category.dart] Category::Category.fromMap
    // 0x8eccd4: mov             x4, x0
    // 0x8eccd8: ldur            x3, [fp, #-8]
    // 0x8eccdc: stur            x4, [fp, #-0x30]
    // 0x8ecce0: r0 = LoadClassIdInstr(r3)
    //     0x8ecce0: ldur            x0, [x3, #-1]
    //     0x8ecce4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecce8: mov             x1, x3
    // 0x8eccec: r2 = "published_at"
    //     0x8eccec: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d958] "published_at"
    //     0x8eccf0: ldr             x2, [x2, #0x958]
    // 0x8eccf4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8eccf4: sub             lr, x0, #0x114
    //     0x8eccf8: ldr             lr, [x21, lr, lsl #3]
    //     0x8eccfc: blr             lr
    // 0x8ecd00: mov             x3, x0
    // 0x8ecd04: r2 = Null
    //     0x8ecd04: mov             x2, NULL
    // 0x8ecd08: r1 = Null
    //     0x8ecd08: mov             x1, NULL
    // 0x8ecd0c: stur            x3, [fp, #-0x38]
    // 0x8ecd10: r4 = 60
    //     0x8ecd10: movz            x4, #0x3c
    // 0x8ecd14: branchIfSmi(r0, 0x8ecd20)
    //     0x8ecd14: tbz             w0, #0, #0x8ecd20
    // 0x8ecd18: r4 = LoadClassIdInstr(r0)
    //     0x8ecd18: ldur            x4, [x0, #-1]
    //     0x8ecd1c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ecd20: sub             x4, x4, #0x5e
    // 0x8ecd24: cmp             x4, #1
    // 0x8ecd28: b.ls            #0x8ecd3c
    // 0x8ecd2c: r8 = String
    //     0x8ecd2c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ecd30: r3 = Null
    //     0x8ecd30: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d960] Null
    //     0x8ecd34: ldr             x3, [x3, #0x960]
    // 0x8ecd38: r0 = String()
    //     0x8ecd38: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ecd3c: ldur            x1, [fp, #-8]
    // 0x8ecd40: r0 = LoadClassIdInstr(r1)
    //     0x8ecd40: ldur            x0, [x1, #-1]
    //     0x8ecd44: ubfx            x0, x0, #0xc, #0x14
    // 0x8ecd48: r2 = "prefix"
    //     0x8ecd48: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x8ecd4c: ldr             x2, [x2, #0x970]
    // 0x8ecd50: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ecd50: sub             lr, x0, #0x114
    //     0x8ecd54: ldr             lr, [x21, lr, lsl #3]
    //     0x8ecd58: blr             lr
    // 0x8ecd5c: mov             x3, x0
    // 0x8ecd60: r2 = Null
    //     0x8ecd60: mov             x2, NULL
    // 0x8ecd64: r1 = Null
    //     0x8ecd64: mov             x1, NULL
    // 0x8ecd68: stur            x3, [fp, #-8]
    // 0x8ecd6c: r4 = 60
    //     0x8ecd6c: movz            x4, #0x3c
    // 0x8ecd70: branchIfSmi(r0, 0x8ecd7c)
    //     0x8ecd70: tbz             w0, #0, #0x8ecd7c
    // 0x8ecd74: r4 = LoadClassIdInstr(r0)
    //     0x8ecd74: ldur            x4, [x0, #-1]
    //     0x8ecd78: ubfx            x4, x4, #0xc, #0x14
    // 0x8ecd7c: sub             x4, x4, #0x5e
    // 0x8ecd80: cmp             x4, #1
    // 0x8ecd84: b.ls            #0x8ecd98
    // 0x8ecd88: r8 = String?
    //     0x8ecd88: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ecd8c: r3 = Null
    //     0x8ecd8c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d978] Null
    //     0x8ecd90: ldr             x3, [x3, #0x978]
    // 0x8ecd94: r0 = String?()
    //     0x8ecd94: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ecd98: ldur            x0, [fp, #-0x10]
    // 0x8ecd9c: r1 = LoadInt32Instr(r0)
    //     0x8ecd9c: sbfx            x1, x0, #1, #0x1f
    //     0x8ecda0: tbz             w0, #0, #0x8ecda8
    //     0x8ecda4: ldur            x1, [x0, #7]
    // 0x8ecda8: stur            x1, [fp, #-0x40]
    // 0x8ecdac: r0 = Article()
    //     0x8ecdac: bl              #0x8ecdfc  ; AllocateArticleStub -> Article (size=0x28)
    // 0x8ecdb0: ldur            x1, [fp, #-0x40]
    // 0x8ecdb4: StoreField: r0->field_7 = r1
    //     0x8ecdb4: stur            x1, [x0, #7]
    // 0x8ecdb8: ldur            x1, [fp, #-0x18]
    // 0x8ecdbc: StoreField: r0->field_f = r1
    //     0x8ecdbc: stur            w1, [x0, #0xf]
    // 0x8ecdc0: ldur            x1, [fp, #-0x28]
    // 0x8ecdc4: ArrayStore: r0[0] = r1  ; List_4
    //     0x8ecdc4: stur            w1, [x0, #0x17]
    // 0x8ecdc8: ldur            x1, [fp, #-0x30]
    // 0x8ecdcc: StoreField: r0->field_1b = r1
    //     0x8ecdcc: stur            w1, [x0, #0x1b]
    // 0x8ecdd0: ldur            x1, [fp, #-0x38]
    // 0x8ecdd4: StoreField: r0->field_1f = r1
    //     0x8ecdd4: stur            w1, [x0, #0x1f]
    // 0x8ecdd8: ldur            x1, [fp, #-0x20]
    // 0x8ecddc: StoreField: r0->field_13 = r1
    //     0x8ecddc: stur            w1, [x0, #0x13]
    // 0x8ecde0: ldur            x1, [fp, #-8]
    // 0x8ecde4: StoreField: r0->field_23 = r1
    //     0x8ecde4: stur            w1, [x0, #0x23]
    // 0x8ecde8: LeaveFrame
    //     0x8ecde8: mov             SP, fp
    //     0x8ecdec: ldp             fp, lr, [SP], #0x10
    // 0x8ecdf0: ret
    //     0x8ecdf0: ret             
    // 0x8ecdf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecdf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecdf8: b               #0x8ecb14
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x8ff3bc, size: 0x180
    // 0x8ff3bc: EnterFrame
    //     0x8ff3bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff3c0: mov             fp, SP
    // 0x8ff3c4: AllocStack(0x20)
    //     0x8ff3c4: sub             SP, SP, #0x20
    // 0x8ff3c8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8ff3c8: mov             x3, x1
    //     0x8ff3cc: stur            x1, [fp, #-8]
    // 0x8ff3d0: CheckStackOverflow
    //     0x8ff3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff3d4: cmp             SP, x16
    //     0x8ff3d8: b.ls            #0x8ff534
    // 0x8ff3dc: mov             x0, x3
    // 0x8ff3e0: r2 = Null
    //     0x8ff3e0: mov             x2, NULL
    // 0x8ff3e4: r1 = Null
    //     0x8ff3e4: mov             x1, NULL
    // 0x8ff3e8: cmp             w0, NULL
    // 0x8ff3ec: b.eq            #0x8ff490
    // 0x8ff3f0: branchIfSmi(r0, 0x8ff490)
    //     0x8ff3f0: tbz             w0, #0, #0x8ff490
    // 0x8ff3f4: r3 = LoadClassIdInstr(r0)
    //     0x8ff3f4: ldur            x3, [x0, #-1]
    //     0x8ff3f8: ubfx            x3, x3, #0xc, #0x14
    // 0x8ff3fc: r17 = 6718
    //     0x8ff3fc: movz            x17, #0x1a3e
    // 0x8ff400: cmp             x3, x17
    // 0x8ff404: b.eq            #0x8ff498
    // 0x8ff408: sub             x3, x3, #0x5a
    // 0x8ff40c: cmp             x3, #2
    // 0x8ff410: b.ls            #0x8ff498
    // 0x8ff414: r4 = LoadClassIdInstr(r0)
    //     0x8ff414: ldur            x4, [x0, #-1]
    //     0x8ff418: ubfx            x4, x4, #0xc, #0x14
    // 0x8ff41c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8ff420: ldr             x3, [x3, #0x18]
    // 0x8ff424: ldr             x3, [x3, x4, lsl #3]
    // 0x8ff428: LoadField: r3 = r3->field_2b
    //     0x8ff428: ldur            w3, [x3, #0x2b]
    // 0x8ff42c: DecompressPointer r3
    //     0x8ff42c: add             x3, x3, HEAP, lsl #32
    // 0x8ff430: cmp             w3, NULL
    // 0x8ff434: b.eq            #0x8ff490
    // 0x8ff438: LoadField: r3 = r3->field_f
    //     0x8ff438: ldur            w3, [x3, #0xf]
    // 0x8ff43c: lsr             x3, x3, #3
    // 0x8ff440: r17 = 6718
    //     0x8ff440: movz            x17, #0x1a3e
    // 0x8ff444: cmp             x3, x17
    // 0x8ff448: b.eq            #0x8ff498
    // 0x8ff44c: r3 = SubtypeTestCache
    //     0x8ff44c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d8d8] SubtypeTestCache
    //     0x8ff450: ldr             x3, [x3, #0x8d8]
    // 0x8ff454: r30 = Subtype1TestCacheStub
    //     0x8ff454: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8ff458: LoadField: r30 = r30->field_7
    //     0x8ff458: ldur            lr, [lr, #7]
    // 0x8ff45c: blr             lr
    // 0x8ff460: cmp             w7, NULL
    // 0x8ff464: b.eq            #0x8ff470
    // 0x8ff468: tbnz            w7, #4, #0x8ff490
    // 0x8ff46c: b               #0x8ff498
    // 0x8ff470: r8 = List
    //     0x8ff470: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d8e0] Type: List
    //     0x8ff474: ldr             x8, [x8, #0x8e0]
    // 0x8ff478: r3 = SubtypeTestCache
    //     0x8ff478: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d8e8] SubtypeTestCache
    //     0x8ff47c: ldr             x3, [x3, #0x8e8]
    // 0x8ff480: r30 = InstanceOfStub
    //     0x8ff480: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8ff484: LoadField: r30 = r30->field_7
    //     0x8ff484: ldur            lr, [lr, #7]
    // 0x8ff488: blr             lr
    // 0x8ff48c: b               #0x8ff49c
    // 0x8ff490: r0 = false
    //     0x8ff490: add             x0, NULL, #0x30  ; false
    // 0x8ff494: b               #0x8ff49c
    // 0x8ff498: r0 = true
    //     0x8ff498: add             x0, NULL, #0x20  ; true
    // 0x8ff49c: tbnz            w0, #4, #0x8ff51c
    // 0x8ff4a0: ldur            x0, [fp, #-8]
    // 0x8ff4a4: r1 = Function '<anonymous closure>': static.
    //     0x8ff4a4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d8f0] AnonymousClosure: static (0x8ff53c), in [package:nuonline/app/data/models/article.dart] Article::fromResponse (0x8ff3bc)
    //     0x8ff4a8: ldr             x1, [x1, #0x8f0]
    // 0x8ff4ac: r2 = Null
    //     0x8ff4ac: mov             x2, NULL
    // 0x8ff4b0: r0 = AllocateClosure()
    //     0x8ff4b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ff4b4: mov             x1, x0
    // 0x8ff4b8: ldur            x0, [fp, #-8]
    // 0x8ff4bc: r2 = LoadClassIdInstr(r0)
    //     0x8ff4bc: ldur            x2, [x0, #-1]
    //     0x8ff4c0: ubfx            x2, x2, #0xc, #0x14
    // 0x8ff4c4: r16 = <Article>
    //     0x8ff4c4: ldr             x16, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0x8ff4c8: stp             x0, x16, [SP, #8]
    // 0x8ff4cc: str             x1, [SP]
    // 0x8ff4d0: mov             x0, x2
    // 0x8ff4d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ff4d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ff4d8: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ff4d8: movz            x17, #0xf28c
    //     0x8ff4dc: add             lr, x0, x17
    //     0x8ff4e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8ff4e4: blr             lr
    // 0x8ff4e8: r1 = LoadClassIdInstr(r0)
    //     0x8ff4e8: ldur            x1, [x0, #-1]
    //     0x8ff4ec: ubfx            x1, x1, #0xc, #0x14
    // 0x8ff4f0: mov             x16, x0
    // 0x8ff4f4: mov             x0, x1
    // 0x8ff4f8: mov             x1, x16
    // 0x8ff4fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ff4fc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ff500: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ff500: movz            x17, #0xd889
    //     0x8ff504: add             lr, x0, x17
    //     0x8ff508: ldr             lr, [x21, lr, lsl #3]
    //     0x8ff50c: blr             lr
    // 0x8ff510: LeaveFrame
    //     0x8ff510: mov             SP, fp
    //     0x8ff514: ldp             fp, lr, [SP], #0x10
    // 0x8ff518: ret
    //     0x8ff518: ret             
    // 0x8ff51c: r1 = <Article>
    //     0x8ff51c: ldr             x1, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0x8ff520: r2 = 0
    //     0x8ff520: movz            x2, #0
    // 0x8ff524: r0 = _GrowableList()
    //     0x8ff524: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ff528: LeaveFrame
    //     0x8ff528: mov             SP, fp
    //     0x8ff52c: ldp             fp, lr, [SP], #0x10
    // 0x8ff530: ret
    //     0x8ff530: ret             
    // 0x8ff534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff534: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff538: b               #0x8ff3dc
  }
  [closure] static Article <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ff53c, size: 0x50
    // 0x8ff53c: EnterFrame
    //     0x8ff53c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff540: mov             fp, SP
    // 0x8ff544: CheckStackOverflow
    //     0x8ff544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff548: cmp             SP, x16
    //     0x8ff54c: b.ls            #0x8ff584
    // 0x8ff550: ldr             x0, [fp, #0x10]
    // 0x8ff554: r2 = Null
    //     0x8ff554: mov             x2, NULL
    // 0x8ff558: r1 = Null
    //     0x8ff558: mov             x1, NULL
    // 0x8ff55c: r8 = Map<String, dynamic>
    //     0x8ff55c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ff560: r3 = Null
    //     0x8ff560: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d8f8] Null
    //     0x8ff564: ldr             x3, [x3, #0x8f8]
    // 0x8ff568: r0 = Map<String, dynamic>()
    //     0x8ff568: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ff56c: ldr             x2, [fp, #0x10]
    // 0x8ff570: r1 = Null
    //     0x8ff570: mov             x1, NULL
    // 0x8ff574: r0 = Article.fromMap()
    //     0x8ff574: bl              #0x8ecaf4  ; [package:nuonline/app/data/models/article.dart] Article::Article.fromMap
    // 0x8ff578: LeaveFrame
    //     0x8ff578: mov             SP, fp
    //     0x8ff57c: ldp             fp, lr, [SP], #0x10
    // 0x8ff580: ret
    //     0x8ff580: ret             
    // 0x8ff584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff588: b               #0x8ff550
  }
  get _ publishedAt(/* No info */) {
    // ** addr: 0xb585c0, size: 0x78
    // 0xb585c0: EnterFrame
    //     0xb585c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb585c4: mov             fp, SP
    // 0xb585c8: AllocStack(0x8)
    //     0xb585c8: sub             SP, SP, #8
    // 0xb585cc: CheckStackOverflow
    //     0xb585cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb585d0: cmp             SP, x16
    //     0xb585d4: b.ls            #0xb58630
    // 0xb585d8: LoadField: r0 = r1->field_1f
    //     0xb585d8: ldur            w0, [x1, #0x1f]
    // 0xb585dc: DecompressPointer r0
    //     0xb585dc: add             x0, x0, HEAP, lsl #32
    // 0xb585e0: mov             x1, x0
    // 0xb585e4: stur            x0, [fp, #-8]
    // 0xb585e8: r0 = tryParse()
    //     0xb585e8: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0xb585ec: cmp             w0, NULL
    // 0xb585f0: b.ne            #0xb585fc
    // 0xb585f4: r1 = Null
    //     0xb585f4: mov             x1, NULL
    // 0xb585f8: b               #0xb58610
    // 0xb585fc: mov             x1, x0
    // 0xb58600: r0 = toLocal()
    //     0xb58600: bl              #0x82c4e0  ; [dart:core] DateTime::toLocal
    // 0xb58604: mov             x1, x0
    // 0xb58608: r0 = DateTimeExtensions.humanize()
    //     0xb58608: bl              #0xb58638  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanize
    // 0xb5860c: mov             x1, x0
    // 0xb58610: cmp             w1, NULL
    // 0xb58614: b.ne            #0xb58620
    // 0xb58618: ldur            x0, [fp, #-8]
    // 0xb5861c: b               #0xb58624
    // 0xb58620: mov             x0, x1
    // 0xb58624: LeaveFrame
    //     0xb58624: mov             SP, fp
    //     0xb58628: ldp             fp, lr, [SP], #0x10
    // 0xb5862c: ret
    //     0xb5862c: ret             
    // 0xb58630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58634: b               #0xb585d8
  }
}
