// lib: , url: package:nuonline/app/data/models/author.dart

// class id: 1050006, size: 0x8
class :: {
}

// class id: 1669, size: 0x14, field offset: 0xc
class AuthorAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5efa8, size: 0x418
    // 0xa5efa8: EnterFrame
    //     0xa5efa8: stp             fp, lr, [SP, #-0x10]!
    //     0xa5efac: mov             fp, SP
    // 0xa5efb0: AllocStack(0x58)
    //     0xa5efb0: sub             SP, SP, #0x58
    // 0xa5efb4: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5efb4: stur            x2, [fp, #-0x20]
    // 0xa5efb8: CheckStackOverflow
    //     0xa5efb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5efbc: cmp             SP, x16
    //     0xa5efc0: b.ls            #0xa5f3a8
    // 0xa5efc4: LoadField: r3 = r2->field_23
    //     0xa5efc4: ldur            x3, [x2, #0x23]
    // 0xa5efc8: add             x0, x3, #1
    // 0xa5efcc: LoadField: r1 = r2->field_1b
    //     0xa5efcc: ldur            x1, [x2, #0x1b]
    // 0xa5efd0: cmp             x0, x1
    // 0xa5efd4: b.gt            #0xa5f34c
    // 0xa5efd8: LoadField: r4 = r2->field_7
    //     0xa5efd8: ldur            w4, [x2, #7]
    // 0xa5efdc: DecompressPointer r4
    //     0xa5efdc: add             x4, x4, HEAP, lsl #32
    // 0xa5efe0: stur            x4, [fp, #-0x18]
    // 0xa5efe4: StoreField: r2->field_23 = r0
    //     0xa5efe4: stur            x0, [x2, #0x23]
    // 0xa5efe8: LoadField: r0 = r4->field_13
    //     0xa5efe8: ldur            w0, [x4, #0x13]
    // 0xa5efec: r5 = LoadInt32Instr(r0)
    //     0xa5efec: sbfx            x5, x0, #1, #0x1f
    // 0xa5eff0: mov             x0, x5
    // 0xa5eff4: mov             x1, x3
    // 0xa5eff8: stur            x5, [fp, #-0x10]
    // 0xa5effc: cmp             x1, x0
    // 0xa5f000: b.hs            #0xa5f3b0
    // 0xa5f004: LoadField: r0 = r4->field_7
    //     0xa5f004: ldur            x0, [x4, #7]
    // 0xa5f008: ldrb            w1, [x0, x3]
    // 0xa5f00c: stur            x1, [fp, #-8]
    // 0xa5f010: r16 = <int, dynamic>
    //     0xa5f010: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5f014: ldr             x16, [x16, #0xac0]
    // 0xa5f018: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5f01c: stp             lr, x16, [SP]
    // 0xa5f020: r0 = Map._fromLiteral()
    //     0xa5f020: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5f024: mov             x2, x0
    // 0xa5f028: stur            x2, [fp, #-0x38]
    // 0xa5f02c: r6 = 0
    //     0xa5f02c: movz            x6, #0
    // 0xa5f030: ldur            x3, [fp, #-0x20]
    // 0xa5f034: ldur            x4, [fp, #-0x18]
    // 0xa5f038: ldur            x5, [fp, #-8]
    // 0xa5f03c: stur            x6, [fp, #-0x30]
    // 0xa5f040: CheckStackOverflow
    //     0xa5f040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5f044: cmp             SP, x16
    //     0xa5f048: b.ls            #0xa5f3b4
    // 0xa5f04c: cmp             x6, x5
    // 0xa5f050: b.ge            #0xa5f0dc
    // 0xa5f054: LoadField: r7 = r3->field_23
    //     0xa5f054: ldur            x7, [x3, #0x23]
    // 0xa5f058: add             x0, x7, #1
    // 0xa5f05c: LoadField: r1 = r3->field_1b
    //     0xa5f05c: ldur            x1, [x3, #0x1b]
    // 0xa5f060: cmp             x0, x1
    // 0xa5f064: b.gt            #0xa5f374
    // 0xa5f068: StoreField: r3->field_23 = r0
    //     0xa5f068: stur            x0, [x3, #0x23]
    // 0xa5f06c: ldur            x0, [fp, #-0x10]
    // 0xa5f070: mov             x1, x7
    // 0xa5f074: cmp             x1, x0
    // 0xa5f078: b.hs            #0xa5f3bc
    // 0xa5f07c: LoadField: r0 = r4->field_7
    //     0xa5f07c: ldur            x0, [x4, #7]
    // 0xa5f080: ldrb            w8, [x0, x7]
    // 0xa5f084: mov             x1, x3
    // 0xa5f088: stur            x8, [fp, #-0x28]
    // 0xa5f08c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5f08c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5f090: r0 = read()
    //     0xa5f090: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5f094: mov             x1, x0
    // 0xa5f098: ldur            x0, [fp, #-0x28]
    // 0xa5f09c: lsl             x2, x0, #1
    // 0xa5f0a0: r16 = LoadInt32Instr(r2)
    //     0xa5f0a0: sbfx            x16, x2, #1, #0x1f
    // 0xa5f0a4: r17 = 11601
    //     0xa5f0a4: movz            x17, #0x2d51
    // 0xa5f0a8: mul             x0, x16, x17
    // 0xa5f0ac: umulh           x16, x16, x17
    // 0xa5f0b0: eor             x0, x0, x16
    // 0xa5f0b4: r0 = 0
    //     0xa5f0b4: eor             x0, x0, x0, lsr #32
    // 0xa5f0b8: ubfiz           x0, x0, #1, #0x1e
    // 0xa5f0bc: r5 = LoadInt32Instr(r0)
    //     0xa5f0bc: sbfx            x5, x0, #1, #0x1f
    // 0xa5f0c0: mov             x3, x1
    // 0xa5f0c4: ldur            x1, [fp, #-0x38]
    // 0xa5f0c8: r0 = _set()
    //     0xa5f0c8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5f0cc: ldur            x0, [fp, #-0x30]
    // 0xa5f0d0: add             x6, x0, #1
    // 0xa5f0d4: ldur            x2, [fp, #-0x38]
    // 0xa5f0d8: b               #0xa5f030
    // 0xa5f0dc: mov             x0, x2
    // 0xa5f0e0: mov             x1, x0
    // 0xa5f0e4: r2 = 0
    //     0xa5f0e4: movz            x2, #0
    // 0xa5f0e8: r0 = _getValueOrData()
    //     0xa5f0e8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f0ec: ldur            x3, [fp, #-0x38]
    // 0xa5f0f0: LoadField: r1 = r3->field_f
    //     0xa5f0f0: ldur            w1, [x3, #0xf]
    // 0xa5f0f4: DecompressPointer r1
    //     0xa5f0f4: add             x1, x1, HEAP, lsl #32
    // 0xa5f0f8: cmp             w1, w0
    // 0xa5f0fc: b.ne            #0xa5f108
    // 0xa5f100: r4 = Null
    //     0xa5f100: mov             x4, NULL
    // 0xa5f104: b               #0xa5f10c
    // 0xa5f108: mov             x4, x0
    // 0xa5f10c: mov             x0, x4
    // 0xa5f110: stur            x4, [fp, #-0x18]
    // 0xa5f114: r2 = Null
    //     0xa5f114: mov             x2, NULL
    // 0xa5f118: r1 = Null
    //     0xa5f118: mov             x1, NULL
    // 0xa5f11c: branchIfSmi(r0, 0xa5f144)
    //     0xa5f11c: tbz             w0, #0, #0xa5f144
    // 0xa5f120: r4 = LoadClassIdInstr(r0)
    //     0xa5f120: ldur            x4, [x0, #-1]
    //     0xa5f124: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f128: sub             x4, x4, #0x3c
    // 0xa5f12c: cmp             x4, #1
    // 0xa5f130: b.ls            #0xa5f144
    // 0xa5f134: r8 = int
    //     0xa5f134: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5f138: r3 = Null
    //     0xa5f138: add             x3, PP, #0x21, lsl #12  ; [pp+0x21768] Null
    //     0xa5f13c: ldr             x3, [x3, #0x768]
    // 0xa5f140: r0 = int()
    //     0xa5f140: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5f144: ldur            x1, [fp, #-0x38]
    // 0xa5f148: r2 = 2
    //     0xa5f148: movz            x2, #0x2
    // 0xa5f14c: r0 = _getValueOrData()
    //     0xa5f14c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f150: ldur            x3, [fp, #-0x38]
    // 0xa5f154: LoadField: r1 = r3->field_f
    //     0xa5f154: ldur            w1, [x3, #0xf]
    // 0xa5f158: DecompressPointer r1
    //     0xa5f158: add             x1, x1, HEAP, lsl #32
    // 0xa5f15c: cmp             w1, w0
    // 0xa5f160: b.ne            #0xa5f16c
    // 0xa5f164: r4 = Null
    //     0xa5f164: mov             x4, NULL
    // 0xa5f168: b               #0xa5f170
    // 0xa5f16c: mov             x4, x0
    // 0xa5f170: mov             x0, x4
    // 0xa5f174: stur            x4, [fp, #-0x20]
    // 0xa5f178: r2 = Null
    //     0xa5f178: mov             x2, NULL
    // 0xa5f17c: r1 = Null
    //     0xa5f17c: mov             x1, NULL
    // 0xa5f180: r4 = 60
    //     0xa5f180: movz            x4, #0x3c
    // 0xa5f184: branchIfSmi(r0, 0xa5f190)
    //     0xa5f184: tbz             w0, #0, #0xa5f190
    // 0xa5f188: r4 = LoadClassIdInstr(r0)
    //     0xa5f188: ldur            x4, [x0, #-1]
    //     0xa5f18c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f190: sub             x4, x4, #0x5e
    // 0xa5f194: cmp             x4, #1
    // 0xa5f198: b.ls            #0xa5f1ac
    // 0xa5f19c: r8 = String
    //     0xa5f19c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5f1a0: r3 = Null
    //     0xa5f1a0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21778] Null
    //     0xa5f1a4: ldr             x3, [x3, #0x778]
    // 0xa5f1a8: r0 = String()
    //     0xa5f1a8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5f1ac: ldur            x1, [fp, #-0x38]
    // 0xa5f1b0: r2 = 4
    //     0xa5f1b0: movz            x2, #0x4
    // 0xa5f1b4: r0 = _getValueOrData()
    //     0xa5f1b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f1b8: ldur            x3, [fp, #-0x38]
    // 0xa5f1bc: LoadField: r1 = r3->field_f
    //     0xa5f1bc: ldur            w1, [x3, #0xf]
    // 0xa5f1c0: DecompressPointer r1
    //     0xa5f1c0: add             x1, x1, HEAP, lsl #32
    // 0xa5f1c4: cmp             w1, w0
    // 0xa5f1c8: b.ne            #0xa5f1d4
    // 0xa5f1cc: r4 = Null
    //     0xa5f1cc: mov             x4, NULL
    // 0xa5f1d0: b               #0xa5f1d8
    // 0xa5f1d4: mov             x4, x0
    // 0xa5f1d8: mov             x0, x4
    // 0xa5f1dc: stur            x4, [fp, #-0x40]
    // 0xa5f1e0: r2 = Null
    //     0xa5f1e0: mov             x2, NULL
    // 0xa5f1e4: r1 = Null
    //     0xa5f1e4: mov             x1, NULL
    // 0xa5f1e8: r4 = 60
    //     0xa5f1e8: movz            x4, #0x3c
    // 0xa5f1ec: branchIfSmi(r0, 0xa5f1f8)
    //     0xa5f1ec: tbz             w0, #0, #0xa5f1f8
    // 0xa5f1f0: r4 = LoadClassIdInstr(r0)
    //     0xa5f1f0: ldur            x4, [x0, #-1]
    //     0xa5f1f4: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f1f8: sub             x4, x4, #0x5e
    // 0xa5f1fc: cmp             x4, #1
    // 0xa5f200: b.ls            #0xa5f214
    // 0xa5f204: r8 = String
    //     0xa5f204: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5f208: r3 = Null
    //     0xa5f208: add             x3, PP, #0x21, lsl #12  ; [pp+0x21788] Null
    //     0xa5f20c: ldr             x3, [x3, #0x788]
    // 0xa5f210: r0 = String()
    //     0xa5f210: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5f214: ldur            x1, [fp, #-0x38]
    // 0xa5f218: r2 = 6
    //     0xa5f218: movz            x2, #0x6
    // 0xa5f21c: r0 = _getValueOrData()
    //     0xa5f21c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f220: ldur            x3, [fp, #-0x38]
    // 0xa5f224: LoadField: r1 = r3->field_f
    //     0xa5f224: ldur            w1, [x3, #0xf]
    // 0xa5f228: DecompressPointer r1
    //     0xa5f228: add             x1, x1, HEAP, lsl #32
    // 0xa5f22c: cmp             w1, w0
    // 0xa5f230: b.ne            #0xa5f23c
    // 0xa5f234: r4 = Null
    //     0xa5f234: mov             x4, NULL
    // 0xa5f238: b               #0xa5f240
    // 0xa5f23c: mov             x4, x0
    // 0xa5f240: mov             x0, x4
    // 0xa5f244: stur            x4, [fp, #-0x48]
    // 0xa5f248: r2 = Null
    //     0xa5f248: mov             x2, NULL
    // 0xa5f24c: r1 = Null
    //     0xa5f24c: mov             x1, NULL
    // 0xa5f250: r4 = 60
    //     0xa5f250: movz            x4, #0x3c
    // 0xa5f254: branchIfSmi(r0, 0xa5f260)
    //     0xa5f254: tbz             w0, #0, #0xa5f260
    // 0xa5f258: r4 = LoadClassIdInstr(r0)
    //     0xa5f258: ldur            x4, [x0, #-1]
    //     0xa5f25c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f260: sub             x4, x4, #0x5e
    // 0xa5f264: cmp             x4, #1
    // 0xa5f268: b.ls            #0xa5f27c
    // 0xa5f26c: r8 = String?
    //     0xa5f26c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5f270: r3 = Null
    //     0xa5f270: add             x3, PP, #0x21, lsl #12  ; [pp+0x21798] Null
    //     0xa5f274: ldr             x3, [x3, #0x798]
    // 0xa5f278: r0 = String?()
    //     0xa5f278: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5f27c: ldur            x1, [fp, #-0x38]
    // 0xa5f280: r2 = 8
    //     0xa5f280: movz            x2, #0x8
    // 0xa5f284: r0 = _getValueOrData()
    //     0xa5f284: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f288: mov             x1, x0
    // 0xa5f28c: ldur            x0, [fp, #-0x38]
    // 0xa5f290: LoadField: r2 = r0->field_f
    //     0xa5f290: ldur            w2, [x0, #0xf]
    // 0xa5f294: DecompressPointer r2
    //     0xa5f294: add             x2, x2, HEAP, lsl #32
    // 0xa5f298: cmp             w2, w1
    // 0xa5f29c: b.ne            #0xa5f2a8
    // 0xa5f2a0: r7 = Null
    //     0xa5f2a0: mov             x7, NULL
    // 0xa5f2a4: b               #0xa5f2ac
    // 0xa5f2a8: mov             x7, x1
    // 0xa5f2ac: ldur            x6, [fp, #-0x18]
    // 0xa5f2b0: ldur            x5, [fp, #-0x20]
    // 0xa5f2b4: ldur            x4, [fp, #-0x40]
    // 0xa5f2b8: ldur            x3, [fp, #-0x48]
    // 0xa5f2bc: mov             x0, x7
    // 0xa5f2c0: stur            x7, [fp, #-0x38]
    // 0xa5f2c4: r2 = Null
    //     0xa5f2c4: mov             x2, NULL
    // 0xa5f2c8: r1 = Null
    //     0xa5f2c8: mov             x1, NULL
    // 0xa5f2cc: r4 = 60
    //     0xa5f2cc: movz            x4, #0x3c
    // 0xa5f2d0: branchIfSmi(r0, 0xa5f2dc)
    //     0xa5f2d0: tbz             w0, #0, #0xa5f2dc
    // 0xa5f2d4: r4 = LoadClassIdInstr(r0)
    //     0xa5f2d4: ldur            x4, [x0, #-1]
    //     0xa5f2d8: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f2dc: sub             x4, x4, #0x5e
    // 0xa5f2e0: cmp             x4, #1
    // 0xa5f2e4: b.ls            #0xa5f2f8
    // 0xa5f2e8: r8 = String?
    //     0xa5f2e8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5f2ec: r3 = Null
    //     0xa5f2ec: add             x3, PP, #0x21, lsl #12  ; [pp+0x217a8] Null
    //     0xa5f2f0: ldr             x3, [x3, #0x7a8]
    // 0xa5f2f4: r0 = String?()
    //     0xa5f2f4: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5f2f8: ldur            x0, [fp, #-0x18]
    // 0xa5f2fc: r1 = LoadInt32Instr(r0)
    //     0xa5f2fc: sbfx            x1, x0, #1, #0x1f
    //     0xa5f300: tbz             w0, #0, #0xa5f308
    //     0xa5f304: ldur            x1, [x0, #7]
    // 0xa5f308: stur            x1, [fp, #-8]
    // 0xa5f30c: r0 = Author()
    //     0xa5f30c: bl              #0x8ec7b4  ; AllocateAuthorStub -> Author (size=0x20)
    // 0xa5f310: mov             x1, x0
    // 0xa5f314: ldur            x0, [fp, #-8]
    // 0xa5f318: StoreField: r1->field_7 = r0
    //     0xa5f318: stur            x0, [x1, #7]
    // 0xa5f31c: ldur            x0, [fp, #-0x20]
    // 0xa5f320: StoreField: r1->field_f = r0
    //     0xa5f320: stur            w0, [x1, #0xf]
    // 0xa5f324: ldur            x0, [fp, #-0x40]
    // 0xa5f328: StoreField: r1->field_13 = r0
    //     0xa5f328: stur            w0, [x1, #0x13]
    // 0xa5f32c: ldur            x0, [fp, #-0x48]
    // 0xa5f330: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5f330: stur            w0, [x1, #0x17]
    // 0xa5f334: ldur            x0, [fp, #-0x38]
    // 0xa5f338: StoreField: r1->field_1b = r0
    //     0xa5f338: stur            w0, [x1, #0x1b]
    // 0xa5f33c: mov             x0, x1
    // 0xa5f340: LeaveFrame
    //     0xa5f340: mov             SP, fp
    //     0xa5f344: ldp             fp, lr, [SP], #0x10
    // 0xa5f348: ret
    //     0xa5f348: ret             
    // 0xa5f34c: r0 = RangeError()
    //     0xa5f34c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5f350: mov             x1, x0
    // 0xa5f354: r0 = "Not enough bytes available."
    //     0xa5f354: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f358: ldr             x0, [x0, #0x8a8]
    // 0xa5f35c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5f35c: stur            w0, [x1, #0x17]
    // 0xa5f360: r2 = false
    //     0xa5f360: add             x2, NULL, #0x30  ; false
    // 0xa5f364: StoreField: r1->field_b = r2
    //     0xa5f364: stur            w2, [x1, #0xb]
    // 0xa5f368: mov             x0, x1
    // 0xa5f36c: r0 = Throw()
    //     0xa5f36c: bl              #0xec04b8  ; ThrowStub
    // 0xa5f370: brk             #0
    // 0xa5f374: r0 = "Not enough bytes available."
    //     0xa5f374: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f378: ldr             x0, [x0, #0x8a8]
    // 0xa5f37c: r2 = false
    //     0xa5f37c: add             x2, NULL, #0x30  ; false
    // 0xa5f380: r0 = RangeError()
    //     0xa5f380: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5f384: mov             x1, x0
    // 0xa5f388: r0 = "Not enough bytes available."
    //     0xa5f388: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f38c: ldr             x0, [x0, #0x8a8]
    // 0xa5f390: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5f390: stur            w0, [x1, #0x17]
    // 0xa5f394: r0 = false
    //     0xa5f394: add             x0, NULL, #0x30  ; false
    // 0xa5f398: StoreField: r1->field_b = r0
    //     0xa5f398: stur            w0, [x1, #0xb]
    // 0xa5f39c: mov             x0, x1
    // 0xa5f3a0: r0 = Throw()
    //     0xa5f3a0: bl              #0xec04b8  ; ThrowStub
    // 0xa5f3a4: brk             #0
    // 0xa5f3a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5f3a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5f3ac: b               #0xa5efc4
    // 0xa5f3b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5f3b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5f3b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5f3b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5f3b8: b               #0xa5f04c
    // 0xa5f3bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5f3bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd0694, size: 0x3ac
    // 0xbd0694: EnterFrame
    //     0xbd0694: stp             fp, lr, [SP, #-0x10]!
    //     0xbd0698: mov             fp, SP
    // 0xbd069c: AllocStack(0x28)
    //     0xbd069c: sub             SP, SP, #0x28
    // 0xbd06a0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd06a0: mov             x4, x2
    //     0xbd06a4: stur            x2, [fp, #-8]
    //     0xbd06a8: stur            x3, [fp, #-0x10]
    // 0xbd06ac: CheckStackOverflow
    //     0xbd06ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd06b0: cmp             SP, x16
    //     0xbd06b4: b.ls            #0xbd0a20
    // 0xbd06b8: mov             x0, x3
    // 0xbd06bc: r2 = Null
    //     0xbd06bc: mov             x2, NULL
    // 0xbd06c0: r1 = Null
    //     0xbd06c0: mov             x1, NULL
    // 0xbd06c4: r4 = 60
    //     0xbd06c4: movz            x4, #0x3c
    // 0xbd06c8: branchIfSmi(r0, 0xbd06d4)
    //     0xbd06c8: tbz             w0, #0, #0xbd06d4
    // 0xbd06cc: r4 = LoadClassIdInstr(r0)
    //     0xbd06cc: ldur            x4, [x0, #-1]
    //     0xbd06d0: ubfx            x4, x4, #0xc, #0x14
    // 0xbd06d4: r17 = 5596
    //     0xbd06d4: movz            x17, #0x15dc
    // 0xbd06d8: cmp             x4, x17
    // 0xbd06dc: b.eq            #0xbd06f4
    // 0xbd06e0: r8 = Author
    //     0xbd06e0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b4b8] Type: Author
    //     0xbd06e4: ldr             x8, [x8, #0x4b8]
    // 0xbd06e8: r3 = Null
    //     0xbd06e8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4c0] Null
    //     0xbd06ec: ldr             x3, [x3, #0x4c0]
    // 0xbd06f0: r0 = Author()
    //     0xbd06f0: bl              #0x80cd68  ; IsType_Author_Stub
    // 0xbd06f4: ldur            x0, [fp, #-8]
    // 0xbd06f8: LoadField: r1 = r0->field_b
    //     0xbd06f8: ldur            w1, [x0, #0xb]
    // 0xbd06fc: DecompressPointer r1
    //     0xbd06fc: add             x1, x1, HEAP, lsl #32
    // 0xbd0700: LoadField: r2 = r1->field_13
    //     0xbd0700: ldur            w2, [x1, #0x13]
    // 0xbd0704: LoadField: r1 = r0->field_13
    //     0xbd0704: ldur            x1, [x0, #0x13]
    // 0xbd0708: r3 = LoadInt32Instr(r2)
    //     0xbd0708: sbfx            x3, x2, #1, #0x1f
    // 0xbd070c: sub             x2, x3, x1
    // 0xbd0710: cmp             x2, #1
    // 0xbd0714: b.ge            #0xbd0724
    // 0xbd0718: mov             x1, x0
    // 0xbd071c: r2 = 1
    //     0xbd071c: movz            x2, #0x1
    // 0xbd0720: r0 = _increaseBufferSize()
    //     0xbd0720: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0724: ldur            x3, [fp, #-8]
    // 0xbd0728: r2 = 5
    //     0xbd0728: movz            x2, #0x5
    // 0xbd072c: LoadField: r4 = r3->field_b
    //     0xbd072c: ldur            w4, [x3, #0xb]
    // 0xbd0730: DecompressPointer r4
    //     0xbd0730: add             x4, x4, HEAP, lsl #32
    // 0xbd0734: LoadField: r5 = r3->field_13
    //     0xbd0734: ldur            x5, [x3, #0x13]
    // 0xbd0738: add             x6, x5, #1
    // 0xbd073c: StoreField: r3->field_13 = r6
    //     0xbd073c: stur            x6, [x3, #0x13]
    // 0xbd0740: LoadField: r0 = r4->field_13
    //     0xbd0740: ldur            w0, [x4, #0x13]
    // 0xbd0744: r7 = LoadInt32Instr(r0)
    //     0xbd0744: sbfx            x7, x0, #1, #0x1f
    // 0xbd0748: mov             x0, x7
    // 0xbd074c: mov             x1, x5
    // 0xbd0750: cmp             x1, x0
    // 0xbd0754: b.hs            #0xbd0a28
    // 0xbd0758: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd0758: add             x0, x4, x5
    //     0xbd075c: strb            w2, [x0, #0x17]
    // 0xbd0760: sub             x0, x7, x6
    // 0xbd0764: cmp             x0, #1
    // 0xbd0768: b.ge            #0xbd0778
    // 0xbd076c: mov             x1, x3
    // 0xbd0770: r2 = 1
    //     0xbd0770: movz            x2, #0x1
    // 0xbd0774: r0 = _increaseBufferSize()
    //     0xbd0774: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0778: ldur            x2, [fp, #-8]
    // 0xbd077c: ldur            x3, [fp, #-0x10]
    // 0xbd0780: LoadField: r4 = r2->field_b
    //     0xbd0780: ldur            w4, [x2, #0xb]
    // 0xbd0784: DecompressPointer r4
    //     0xbd0784: add             x4, x4, HEAP, lsl #32
    // 0xbd0788: LoadField: r5 = r2->field_13
    //     0xbd0788: ldur            x5, [x2, #0x13]
    // 0xbd078c: add             x0, x5, #1
    // 0xbd0790: StoreField: r2->field_13 = r0
    //     0xbd0790: stur            x0, [x2, #0x13]
    // 0xbd0794: LoadField: r0 = r4->field_13
    //     0xbd0794: ldur            w0, [x4, #0x13]
    // 0xbd0798: r1 = LoadInt32Instr(r0)
    //     0xbd0798: sbfx            x1, x0, #1, #0x1f
    // 0xbd079c: mov             x0, x1
    // 0xbd07a0: mov             x1, x5
    // 0xbd07a4: cmp             x1, x0
    // 0xbd07a8: b.hs            #0xbd0a2c
    // 0xbd07ac: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd07ac: add             x0, x4, x5
    //     0xbd07b0: strb            wzr, [x0, #0x17]
    // 0xbd07b4: LoadField: r4 = r3->field_7
    //     0xbd07b4: ldur            x4, [x3, #7]
    // 0xbd07b8: r0 = BoxInt64Instr(r4)
    //     0xbd07b8: sbfiz           x0, x4, #1, #0x1f
    //     0xbd07bc: cmp             x4, x0, asr #1
    //     0xbd07c0: b.eq            #0xbd07cc
    //     0xbd07c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd07c8: stur            x4, [x0, #7]
    // 0xbd07cc: r16 = <int>
    //     0xbd07cc: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd07d0: stp             x2, x16, [SP, #8]
    // 0xbd07d4: str             x0, [SP]
    // 0xbd07d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd07d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd07dc: r0 = write()
    //     0xbd07dc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd07e0: ldur            x0, [fp, #-8]
    // 0xbd07e4: LoadField: r1 = r0->field_b
    //     0xbd07e4: ldur            w1, [x0, #0xb]
    // 0xbd07e8: DecompressPointer r1
    //     0xbd07e8: add             x1, x1, HEAP, lsl #32
    // 0xbd07ec: LoadField: r2 = r1->field_13
    //     0xbd07ec: ldur            w2, [x1, #0x13]
    // 0xbd07f0: LoadField: r1 = r0->field_13
    //     0xbd07f0: ldur            x1, [x0, #0x13]
    // 0xbd07f4: r3 = LoadInt32Instr(r2)
    //     0xbd07f4: sbfx            x3, x2, #1, #0x1f
    // 0xbd07f8: sub             x2, x3, x1
    // 0xbd07fc: cmp             x2, #1
    // 0xbd0800: b.ge            #0xbd0810
    // 0xbd0804: mov             x1, x0
    // 0xbd0808: r2 = 1
    //     0xbd0808: movz            x2, #0x1
    // 0xbd080c: r0 = _increaseBufferSize()
    //     0xbd080c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0810: ldur            x2, [fp, #-8]
    // 0xbd0814: ldur            x3, [fp, #-0x10]
    // 0xbd0818: r4 = 1
    //     0xbd0818: movz            x4, #0x1
    // 0xbd081c: LoadField: r5 = r2->field_b
    //     0xbd081c: ldur            w5, [x2, #0xb]
    // 0xbd0820: DecompressPointer r5
    //     0xbd0820: add             x5, x5, HEAP, lsl #32
    // 0xbd0824: LoadField: r6 = r2->field_13
    //     0xbd0824: ldur            x6, [x2, #0x13]
    // 0xbd0828: add             x0, x6, #1
    // 0xbd082c: StoreField: r2->field_13 = r0
    //     0xbd082c: stur            x0, [x2, #0x13]
    // 0xbd0830: LoadField: r0 = r5->field_13
    //     0xbd0830: ldur            w0, [x5, #0x13]
    // 0xbd0834: r1 = LoadInt32Instr(r0)
    //     0xbd0834: sbfx            x1, x0, #1, #0x1f
    // 0xbd0838: mov             x0, x1
    // 0xbd083c: mov             x1, x6
    // 0xbd0840: cmp             x1, x0
    // 0xbd0844: b.hs            #0xbd0a30
    // 0xbd0848: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0848: add             x0, x5, x6
    //     0xbd084c: strb            w4, [x0, #0x17]
    // 0xbd0850: LoadField: r0 = r3->field_f
    //     0xbd0850: ldur            w0, [x3, #0xf]
    // 0xbd0854: DecompressPointer r0
    //     0xbd0854: add             x0, x0, HEAP, lsl #32
    // 0xbd0858: r16 = <String>
    //     0xbd0858: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd085c: stp             x2, x16, [SP, #8]
    // 0xbd0860: str             x0, [SP]
    // 0xbd0864: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0864: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0868: r0 = write()
    //     0xbd0868: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd086c: ldur            x0, [fp, #-8]
    // 0xbd0870: LoadField: r1 = r0->field_b
    //     0xbd0870: ldur            w1, [x0, #0xb]
    // 0xbd0874: DecompressPointer r1
    //     0xbd0874: add             x1, x1, HEAP, lsl #32
    // 0xbd0878: LoadField: r2 = r1->field_13
    //     0xbd0878: ldur            w2, [x1, #0x13]
    // 0xbd087c: LoadField: r1 = r0->field_13
    //     0xbd087c: ldur            x1, [x0, #0x13]
    // 0xbd0880: r3 = LoadInt32Instr(r2)
    //     0xbd0880: sbfx            x3, x2, #1, #0x1f
    // 0xbd0884: sub             x2, x3, x1
    // 0xbd0888: cmp             x2, #1
    // 0xbd088c: b.ge            #0xbd089c
    // 0xbd0890: mov             x1, x0
    // 0xbd0894: r2 = 1
    //     0xbd0894: movz            x2, #0x1
    // 0xbd0898: r0 = _increaseBufferSize()
    //     0xbd0898: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd089c: ldur            x2, [fp, #-8]
    // 0xbd08a0: ldur            x3, [fp, #-0x10]
    // 0xbd08a4: r4 = 2
    //     0xbd08a4: movz            x4, #0x2
    // 0xbd08a8: LoadField: r5 = r2->field_b
    //     0xbd08a8: ldur            w5, [x2, #0xb]
    // 0xbd08ac: DecompressPointer r5
    //     0xbd08ac: add             x5, x5, HEAP, lsl #32
    // 0xbd08b0: LoadField: r6 = r2->field_13
    //     0xbd08b0: ldur            x6, [x2, #0x13]
    // 0xbd08b4: add             x0, x6, #1
    // 0xbd08b8: StoreField: r2->field_13 = r0
    //     0xbd08b8: stur            x0, [x2, #0x13]
    // 0xbd08bc: LoadField: r0 = r5->field_13
    //     0xbd08bc: ldur            w0, [x5, #0x13]
    // 0xbd08c0: r1 = LoadInt32Instr(r0)
    //     0xbd08c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd08c4: mov             x0, x1
    // 0xbd08c8: mov             x1, x6
    // 0xbd08cc: cmp             x1, x0
    // 0xbd08d0: b.hs            #0xbd0a34
    // 0xbd08d4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd08d4: add             x0, x5, x6
    //     0xbd08d8: strb            w4, [x0, #0x17]
    // 0xbd08dc: LoadField: r0 = r3->field_13
    //     0xbd08dc: ldur            w0, [x3, #0x13]
    // 0xbd08e0: DecompressPointer r0
    //     0xbd08e0: add             x0, x0, HEAP, lsl #32
    // 0xbd08e4: r16 = <String>
    //     0xbd08e4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd08e8: stp             x2, x16, [SP, #8]
    // 0xbd08ec: str             x0, [SP]
    // 0xbd08f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd08f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd08f4: r0 = write()
    //     0xbd08f4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd08f8: ldur            x0, [fp, #-8]
    // 0xbd08fc: LoadField: r1 = r0->field_b
    //     0xbd08fc: ldur            w1, [x0, #0xb]
    // 0xbd0900: DecompressPointer r1
    //     0xbd0900: add             x1, x1, HEAP, lsl #32
    // 0xbd0904: LoadField: r2 = r1->field_13
    //     0xbd0904: ldur            w2, [x1, #0x13]
    // 0xbd0908: LoadField: r1 = r0->field_13
    //     0xbd0908: ldur            x1, [x0, #0x13]
    // 0xbd090c: r3 = LoadInt32Instr(r2)
    //     0xbd090c: sbfx            x3, x2, #1, #0x1f
    // 0xbd0910: sub             x2, x3, x1
    // 0xbd0914: cmp             x2, #1
    // 0xbd0918: b.ge            #0xbd0928
    // 0xbd091c: mov             x1, x0
    // 0xbd0920: r2 = 1
    //     0xbd0920: movz            x2, #0x1
    // 0xbd0924: r0 = _increaseBufferSize()
    //     0xbd0924: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0928: ldur            x2, [fp, #-8]
    // 0xbd092c: ldur            x3, [fp, #-0x10]
    // 0xbd0930: r4 = 3
    //     0xbd0930: movz            x4, #0x3
    // 0xbd0934: LoadField: r5 = r2->field_b
    //     0xbd0934: ldur            w5, [x2, #0xb]
    // 0xbd0938: DecompressPointer r5
    //     0xbd0938: add             x5, x5, HEAP, lsl #32
    // 0xbd093c: LoadField: r6 = r2->field_13
    //     0xbd093c: ldur            x6, [x2, #0x13]
    // 0xbd0940: add             x0, x6, #1
    // 0xbd0944: StoreField: r2->field_13 = r0
    //     0xbd0944: stur            x0, [x2, #0x13]
    // 0xbd0948: LoadField: r0 = r5->field_13
    //     0xbd0948: ldur            w0, [x5, #0x13]
    // 0xbd094c: r1 = LoadInt32Instr(r0)
    //     0xbd094c: sbfx            x1, x0, #1, #0x1f
    // 0xbd0950: mov             x0, x1
    // 0xbd0954: mov             x1, x6
    // 0xbd0958: cmp             x1, x0
    // 0xbd095c: b.hs            #0xbd0a38
    // 0xbd0960: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0960: add             x0, x5, x6
    //     0xbd0964: strb            w4, [x0, #0x17]
    // 0xbd0968: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd0968: ldur            w0, [x3, #0x17]
    // 0xbd096c: DecompressPointer r0
    //     0xbd096c: add             x0, x0, HEAP, lsl #32
    // 0xbd0970: r16 = <String?>
    //     0xbd0970: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd0974: stp             x2, x16, [SP, #8]
    // 0xbd0978: str             x0, [SP]
    // 0xbd097c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd097c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0980: r0 = write()
    //     0xbd0980: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0984: ldur            x0, [fp, #-8]
    // 0xbd0988: LoadField: r1 = r0->field_b
    //     0xbd0988: ldur            w1, [x0, #0xb]
    // 0xbd098c: DecompressPointer r1
    //     0xbd098c: add             x1, x1, HEAP, lsl #32
    // 0xbd0990: LoadField: r2 = r1->field_13
    //     0xbd0990: ldur            w2, [x1, #0x13]
    // 0xbd0994: LoadField: r1 = r0->field_13
    //     0xbd0994: ldur            x1, [x0, #0x13]
    // 0xbd0998: r3 = LoadInt32Instr(r2)
    //     0xbd0998: sbfx            x3, x2, #1, #0x1f
    // 0xbd099c: sub             x2, x3, x1
    // 0xbd09a0: cmp             x2, #1
    // 0xbd09a4: b.ge            #0xbd09b4
    // 0xbd09a8: mov             x1, x0
    // 0xbd09ac: r2 = 1
    //     0xbd09ac: movz            x2, #0x1
    // 0xbd09b0: r0 = _increaseBufferSize()
    //     0xbd09b0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd09b4: ldur            x2, [fp, #-8]
    // 0xbd09b8: ldur            x3, [fp, #-0x10]
    // 0xbd09bc: r4 = 4
    //     0xbd09bc: movz            x4, #0x4
    // 0xbd09c0: LoadField: r5 = r2->field_b
    //     0xbd09c0: ldur            w5, [x2, #0xb]
    // 0xbd09c4: DecompressPointer r5
    //     0xbd09c4: add             x5, x5, HEAP, lsl #32
    // 0xbd09c8: LoadField: r6 = r2->field_13
    //     0xbd09c8: ldur            x6, [x2, #0x13]
    // 0xbd09cc: add             x0, x6, #1
    // 0xbd09d0: StoreField: r2->field_13 = r0
    //     0xbd09d0: stur            x0, [x2, #0x13]
    // 0xbd09d4: LoadField: r0 = r5->field_13
    //     0xbd09d4: ldur            w0, [x5, #0x13]
    // 0xbd09d8: r1 = LoadInt32Instr(r0)
    //     0xbd09d8: sbfx            x1, x0, #1, #0x1f
    // 0xbd09dc: mov             x0, x1
    // 0xbd09e0: mov             x1, x6
    // 0xbd09e4: cmp             x1, x0
    // 0xbd09e8: b.hs            #0xbd0a3c
    // 0xbd09ec: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd09ec: add             x0, x5, x6
    //     0xbd09f0: strb            w4, [x0, #0x17]
    // 0xbd09f4: LoadField: r0 = r3->field_1b
    //     0xbd09f4: ldur            w0, [x3, #0x1b]
    // 0xbd09f8: DecompressPointer r0
    //     0xbd09f8: add             x0, x0, HEAP, lsl #32
    // 0xbd09fc: r16 = <String?>
    //     0xbd09fc: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd0a00: stp             x2, x16, [SP, #8]
    // 0xbd0a04: str             x0, [SP]
    // 0xbd0a08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0a08: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0a0c: r0 = write()
    //     0xbd0a0c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0a10: r0 = Null
    //     0xbd0a10: mov             x0, NULL
    // 0xbd0a14: LeaveFrame
    //     0xbd0a14: mov             SP, fp
    //     0xbd0a18: ldp             fp, lr, [SP], #0x10
    // 0xbd0a1c: ret
    //     0xbd0a1c: ret             
    // 0xbd0a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd0a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd0a24: b               #0xbd06b8
    // 0xbd0a28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0a2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0a30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0a34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0a38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0a3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0a3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0024, size: 0x24
    // 0xbf0024: r1 = 82
    //     0xbf0024: movz            x1, #0x52
    // 0xbf0028: r16 = LoadInt32Instr(r1)
    //     0xbf0028: sbfx            x16, x1, #1, #0x1f
    // 0xbf002c: r17 = 11601
    //     0xbf002c: movz            x17, #0x2d51
    // 0xbf0030: mul             x0, x16, x17
    // 0xbf0034: umulh           x16, x16, x17
    // 0xbf0038: eor             x0, x0, x16
    // 0xbf003c: r0 = 0
    //     0xbf003c: eor             x0, x0, x0, lsr #32
    // 0xbf0040: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0044: ret
    //     0xbf0044: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76110, size: 0x9c
    // 0xd76110: EnterFrame
    //     0xd76110: stp             fp, lr, [SP, #-0x10]!
    //     0xd76114: mov             fp, SP
    // 0xd76118: AllocStack(0x10)
    //     0xd76118: sub             SP, SP, #0x10
    // 0xd7611c: CheckStackOverflow
    //     0xd7611c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76120: cmp             SP, x16
    //     0xd76124: b.ls            #0xd761a4
    // 0xd76128: ldr             x0, [fp, #0x10]
    // 0xd7612c: cmp             w0, NULL
    // 0xd76130: b.ne            #0xd76144
    // 0xd76134: r0 = false
    //     0xd76134: add             x0, NULL, #0x30  ; false
    // 0xd76138: LeaveFrame
    //     0xd76138: mov             SP, fp
    //     0xd7613c: ldp             fp, lr, [SP], #0x10
    // 0xd76140: ret
    //     0xd76140: ret             
    // 0xd76144: ldr             x1, [fp, #0x18]
    // 0xd76148: cmp             w1, w0
    // 0xd7614c: b.ne            #0xd76158
    // 0xd76150: r0 = true
    //     0xd76150: add             x0, NULL, #0x20  ; true
    // 0xd76154: b               #0xd76198
    // 0xd76158: r1 = 60
    //     0xd76158: movz            x1, #0x3c
    // 0xd7615c: branchIfSmi(r0, 0xd76168)
    //     0xd7615c: tbz             w0, #0, #0xd76168
    // 0xd76160: r1 = LoadClassIdInstr(r0)
    //     0xd76160: ldur            x1, [x0, #-1]
    //     0xd76164: ubfx            x1, x1, #0xc, #0x14
    // 0xd76168: cmp             x1, #0x685
    // 0xd7616c: b.ne            #0xd76194
    // 0xd76170: r16 = AuthorAdapter
    //     0xd76170: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b4b0] Type: AuthorAdapter
    //     0xd76174: ldr             x16, [x16, #0x4b0]
    // 0xd76178: r30 = AuthorAdapter
    //     0xd76178: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b4b0] Type: AuthorAdapter
    //     0xd7617c: ldr             lr, [lr, #0x4b0]
    // 0xd76180: stp             lr, x16, [SP]
    // 0xd76184: r0 = ==()
    //     0xd76184: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76188: tbnz            w0, #4, #0xd76194
    // 0xd7618c: r0 = true
    //     0xd7618c: add             x0, NULL, #0x20  ; true
    // 0xd76190: b               #0xd76198
    // 0xd76194: r0 = false
    //     0xd76194: add             x0, NULL, #0x30  ; false
    // 0xd76198: LeaveFrame
    //     0xd76198: mov             SP, fp
    //     0xd7619c: ldp             fp, lr, [SP], #0x10
    // 0xd761a0: ret
    //     0xd761a0: ret             
    // 0xd761a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd761a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd761a8: b               #0xd76128
  }
}

// class id: 5596, size: 0x20, field offset: 0x8
//   const constructor, 
class Author extends Equatable {

  factory _ Author.fromMap(/* No info */) {
    // ** addr: 0x8ec574, size: 0x240
    // 0x8ec574: EnterFrame
    //     0x8ec574: stp             fp, lr, [SP, #-0x10]!
    //     0x8ec578: mov             fp, SP
    // 0x8ec57c: AllocStack(0x30)
    //     0x8ec57c: sub             SP, SP, #0x30
    // 0x8ec580: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ec580: mov             x3, x2
    //     0x8ec584: stur            x2, [fp, #-8]
    // 0x8ec588: CheckStackOverflow
    //     0x8ec588: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ec58c: cmp             SP, x16
    //     0x8ec590: b.ls            #0x8ec7ac
    // 0x8ec594: r0 = LoadClassIdInstr(r3)
    //     0x8ec594: ldur            x0, [x3, #-1]
    //     0x8ec598: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec59c: mov             x1, x3
    // 0x8ec5a0: r2 = "id"
    //     0x8ec5a0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ec5a4: ldr             x2, [x2, #0x740]
    // 0x8ec5a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec5a8: sub             lr, x0, #0x114
    //     0x8ec5ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec5b0: blr             lr
    // 0x8ec5b4: mov             x3, x0
    // 0x8ec5b8: r2 = Null
    //     0x8ec5b8: mov             x2, NULL
    // 0x8ec5bc: r1 = Null
    //     0x8ec5bc: mov             x1, NULL
    // 0x8ec5c0: stur            x3, [fp, #-0x10]
    // 0x8ec5c4: branchIfSmi(r0, 0x8ec5ec)
    //     0x8ec5c4: tbz             w0, #0, #0x8ec5ec
    // 0x8ec5c8: r4 = LoadClassIdInstr(r0)
    //     0x8ec5c8: ldur            x4, [x0, #-1]
    //     0x8ec5cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec5d0: sub             x4, x4, #0x3c
    // 0x8ec5d4: cmp             x4, #1
    // 0x8ec5d8: b.ls            #0x8ec5ec
    // 0x8ec5dc: r8 = int
    //     0x8ec5dc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ec5e0: r3 = Null
    //     0x8ec5e0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ef0] Null
    //     0x8ec5e4: ldr             x3, [x3, #0xef0]
    // 0x8ec5e8: r0 = int()
    //     0x8ec5e8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ec5ec: ldur            x3, [fp, #-8]
    // 0x8ec5f0: r0 = LoadClassIdInstr(r3)
    //     0x8ec5f0: ldur            x0, [x3, #-1]
    //     0x8ec5f4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec5f8: mov             x1, x3
    // 0x8ec5fc: r2 = "name"
    //     0x8ec5fc: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8ec600: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec600: sub             lr, x0, #0x114
    //     0x8ec604: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec608: blr             lr
    // 0x8ec60c: mov             x3, x0
    // 0x8ec610: r2 = Null
    //     0x8ec610: mov             x2, NULL
    // 0x8ec614: r1 = Null
    //     0x8ec614: mov             x1, NULL
    // 0x8ec618: stur            x3, [fp, #-0x18]
    // 0x8ec61c: r4 = 60
    //     0x8ec61c: movz            x4, #0x3c
    // 0x8ec620: branchIfSmi(r0, 0x8ec62c)
    //     0x8ec620: tbz             w0, #0, #0x8ec62c
    // 0x8ec624: r4 = LoadClassIdInstr(r0)
    //     0x8ec624: ldur            x4, [x0, #-1]
    //     0x8ec628: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec62c: sub             x4, x4, #0x5e
    // 0x8ec630: cmp             x4, #1
    // 0x8ec634: b.ls            #0x8ec648
    // 0x8ec638: r8 = String
    //     0x8ec638: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ec63c: r3 = Null
    //     0x8ec63c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40f00] Null
    //     0x8ec640: ldr             x3, [x3, #0xf00]
    // 0x8ec644: r0 = String()
    //     0x8ec644: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ec648: ldur            x3, [fp, #-8]
    // 0x8ec64c: r0 = LoadClassIdInstr(r3)
    //     0x8ec64c: ldur            x0, [x3, #-1]
    //     0x8ec650: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec654: mov             x1, x3
    // 0x8ec658: r2 = "avatar"
    //     0x8ec658: add             x2, PP, #0x40, lsl #12  ; [pp+0x40f10] "avatar"
    //     0x8ec65c: ldr             x2, [x2, #0xf10]
    // 0x8ec660: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec660: sub             lr, x0, #0x114
    //     0x8ec664: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec668: blr             lr
    // 0x8ec66c: mov             x3, x0
    // 0x8ec670: r2 = Null
    //     0x8ec670: mov             x2, NULL
    // 0x8ec674: r1 = Null
    //     0x8ec674: mov             x1, NULL
    // 0x8ec678: stur            x3, [fp, #-0x20]
    // 0x8ec67c: r4 = 60
    //     0x8ec67c: movz            x4, #0x3c
    // 0x8ec680: branchIfSmi(r0, 0x8ec68c)
    //     0x8ec680: tbz             w0, #0, #0x8ec68c
    // 0x8ec684: r4 = LoadClassIdInstr(r0)
    //     0x8ec684: ldur            x4, [x0, #-1]
    //     0x8ec688: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec68c: sub             x4, x4, #0x5e
    // 0x8ec690: cmp             x4, #1
    // 0x8ec694: b.ls            #0x8ec6a8
    // 0x8ec698: r8 = String
    //     0x8ec698: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ec69c: r3 = Null
    //     0x8ec69c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40f18] Null
    //     0x8ec6a0: ldr             x3, [x3, #0xf18]
    // 0x8ec6a4: r0 = String()
    //     0x8ec6a4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ec6a8: ldur            x3, [fp, #-8]
    // 0x8ec6ac: r0 = LoadClassIdInstr(r3)
    //     0x8ec6ac: ldur            x0, [x3, #-1]
    //     0x8ec6b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec6b4: mov             x1, x3
    // 0x8ec6b8: r2 = "description"
    //     0x8ec6b8: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x8ec6bc: ldr             x2, [x2, #0xd28]
    // 0x8ec6c0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec6c0: sub             lr, x0, #0x114
    //     0x8ec6c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec6c8: blr             lr
    // 0x8ec6cc: mov             x3, x0
    // 0x8ec6d0: r2 = Null
    //     0x8ec6d0: mov             x2, NULL
    // 0x8ec6d4: r1 = Null
    //     0x8ec6d4: mov             x1, NULL
    // 0x8ec6d8: stur            x3, [fp, #-0x28]
    // 0x8ec6dc: r4 = 60
    //     0x8ec6dc: movz            x4, #0x3c
    // 0x8ec6e0: branchIfSmi(r0, 0x8ec6ec)
    //     0x8ec6e0: tbz             w0, #0, #0x8ec6ec
    // 0x8ec6e4: r4 = LoadClassIdInstr(r0)
    //     0x8ec6e4: ldur            x4, [x0, #-1]
    //     0x8ec6e8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec6ec: sub             x4, x4, #0x5e
    // 0x8ec6f0: cmp             x4, #1
    // 0x8ec6f4: b.ls            #0x8ec708
    // 0x8ec6f8: r8 = String?
    //     0x8ec6f8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ec6fc: r3 = Null
    //     0x8ec6fc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40f28] Null
    //     0x8ec700: ldr             x3, [x3, #0xf28]
    // 0x8ec704: r0 = String?()
    //     0x8ec704: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ec708: ldur            x1, [fp, #-8]
    // 0x8ec70c: r0 = LoadClassIdInstr(r1)
    //     0x8ec70c: ldur            x0, [x1, #-1]
    //     0x8ec710: ubfx            x0, x0, #0xc, #0x14
    // 0x8ec714: r2 = "type"
    //     0x8ec714: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x8ec718: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ec718: sub             lr, x0, #0x114
    //     0x8ec71c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ec720: blr             lr
    // 0x8ec724: mov             x3, x0
    // 0x8ec728: r2 = Null
    //     0x8ec728: mov             x2, NULL
    // 0x8ec72c: r1 = Null
    //     0x8ec72c: mov             x1, NULL
    // 0x8ec730: stur            x3, [fp, #-8]
    // 0x8ec734: r4 = 60
    //     0x8ec734: movz            x4, #0x3c
    // 0x8ec738: branchIfSmi(r0, 0x8ec744)
    //     0x8ec738: tbz             w0, #0, #0x8ec744
    // 0x8ec73c: r4 = LoadClassIdInstr(r0)
    //     0x8ec73c: ldur            x4, [x0, #-1]
    //     0x8ec740: ubfx            x4, x4, #0xc, #0x14
    // 0x8ec744: sub             x4, x4, #0x5e
    // 0x8ec748: cmp             x4, #1
    // 0x8ec74c: b.ls            #0x8ec760
    // 0x8ec750: r8 = String?
    //     0x8ec750: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ec754: r3 = Null
    //     0x8ec754: add             x3, PP, #0x40, lsl #12  ; [pp+0x40f38] Null
    //     0x8ec758: ldr             x3, [x3, #0xf38]
    // 0x8ec75c: r0 = String?()
    //     0x8ec75c: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ec760: ldur            x0, [fp, #-0x10]
    // 0x8ec764: r1 = LoadInt32Instr(r0)
    //     0x8ec764: sbfx            x1, x0, #1, #0x1f
    //     0x8ec768: tbz             w0, #0, #0x8ec770
    //     0x8ec76c: ldur            x1, [x0, #7]
    // 0x8ec770: stur            x1, [fp, #-0x30]
    // 0x8ec774: r0 = Author()
    //     0x8ec774: bl              #0x8ec7b4  ; AllocateAuthorStub -> Author (size=0x20)
    // 0x8ec778: ldur            x1, [fp, #-0x30]
    // 0x8ec77c: StoreField: r0->field_7 = r1
    //     0x8ec77c: stur            x1, [x0, #7]
    // 0x8ec780: ldur            x1, [fp, #-0x18]
    // 0x8ec784: StoreField: r0->field_f = r1
    //     0x8ec784: stur            w1, [x0, #0xf]
    // 0x8ec788: ldur            x1, [fp, #-0x20]
    // 0x8ec78c: StoreField: r0->field_13 = r1
    //     0x8ec78c: stur            w1, [x0, #0x13]
    // 0x8ec790: ldur            x1, [fp, #-0x28]
    // 0x8ec794: ArrayStore: r0[0] = r1  ; List_4
    //     0x8ec794: stur            w1, [x0, #0x17]
    // 0x8ec798: ldur            x1, [fp, #-8]
    // 0x8ec79c: StoreField: r0->field_1b = r1
    //     0x8ec79c: stur            w1, [x0, #0x1b]
    // 0x8ec7a0: LeaveFrame
    //     0x8ec7a0: mov             SP, fp
    //     0x8ec7a4: ldp             fp, lr, [SP], #0x10
    // 0x8ec7a8: ret
    //     0x8ec7a8: ret             
    // 0x8ec7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ec7ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ec7b0: b               #0x8ec594
  }
}
