// lib: , url: package:nuonline/app/data/models/banner.dart

// class id: 1050007, size: 0x8
class :: {
}

// class id: 1160, size: 0x14, field offset: 0x8
class BannerOptions extends Object {

  factory _ BannerOptions.fromMap(/* No info */) {
    // ** addr: 0xafb32c, size: 0x1a4
    // 0xafb32c: EnterFrame
    //     0xafb32c: stp             fp, lr, [SP, #-0x10]!
    //     0xafb330: mov             fp, SP
    // 0xafb334: AllocStack(0x18)
    //     0xafb334: sub             SP, SP, #0x18
    // 0xafb338: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xafb338: mov             x3, x2
    //     0xafb33c: stur            x2, [fp, #-8]
    // 0xafb340: CheckStackOverflow
    //     0xafb340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb344: cmp             SP, x16
    //     0xafb348: b.ls            #0xafb4c8
    // 0xafb34c: cmp             w3, NULL
    // 0xafb350: b.ne            #0xafb374
    // 0xafb354: r0 = BannerOptions()
    //     0xafb354: bl              #0xafb4d0  ; AllocateBannerOptionsStub -> BannerOptions (size=0x14)
    // 0xafb358: mov             x1, x0
    // 0xafb35c: r0 = true
    //     0xafb35c: add             x0, NULL, #0x20  ; true
    // 0xafb360: StoreField: r1->field_b = r0
    //     0xafb360: stur            w0, [x1, #0xb]
    // 0xafb364: mov             x0, x1
    // 0xafb368: LeaveFrame
    //     0xafb368: mov             SP, fp
    //     0xafb36c: ldp             fp, lr, [SP], #0x10
    // 0xafb370: ret
    //     0xafb370: ret             
    // 0xafb374: r0 = LoadClassIdInstr(r3)
    //     0xafb374: ldur            x0, [x3, #-1]
    //     0xafb378: ubfx            x0, x0, #0xc, #0x14
    // 0xafb37c: mov             x1, x3
    // 0xafb380: r2 = "aspect_ratio"
    //     0xafb380: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dd30] "aspect_ratio"
    //     0xafb384: ldr             x2, [x2, #0xd30]
    // 0xafb388: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb388: sub             lr, x0, #0x114
    //     0xafb38c: ldr             lr, [x21, lr, lsl #3]
    //     0xafb390: blr             lr
    // 0xafb394: mov             x3, x0
    // 0xafb398: r2 = Null
    //     0xafb398: mov             x2, NULL
    // 0xafb39c: r1 = Null
    //     0xafb39c: mov             x1, NULL
    // 0xafb3a0: stur            x3, [fp, #-0x10]
    // 0xafb3a4: r4 = 60
    //     0xafb3a4: movz            x4, #0x3c
    // 0xafb3a8: branchIfSmi(r0, 0xafb3b4)
    //     0xafb3a8: tbz             w0, #0, #0xafb3b4
    // 0xafb3ac: r4 = LoadClassIdInstr(r0)
    //     0xafb3ac: ldur            x4, [x0, #-1]
    //     0xafb3b0: ubfx            x4, x4, #0xc, #0x14
    // 0xafb3b4: cmp             x4, #0x3e
    // 0xafb3b8: b.eq            #0xafb3cc
    // 0xafb3bc: r8 = double?
    //     0xafb3bc: ldr             x8, [PP, #0x12d0]  ; [pp+0x12d0] Type: double?
    // 0xafb3c0: r3 = Null
    //     0xafb3c0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd38] Null
    //     0xafb3c4: ldr             x3, [x3, #0xd38]
    // 0xafb3c8: r0 = double?()
    //     0xafb3c8: bl              #0xed4434  ; IsType_double?_Stub
    // 0xafb3cc: ldur            x3, [fp, #-8]
    // 0xafb3d0: r0 = LoadClassIdInstr(r3)
    //     0xafb3d0: ldur            x0, [x3, #-1]
    //     0xafb3d4: ubfx            x0, x0, #0xc, #0x14
    // 0xafb3d8: mov             x1, x3
    // 0xafb3dc: r2 = "auto_play"
    //     0xafb3dc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dd48] "auto_play"
    //     0xafb3e0: ldr             x2, [x2, #0xd48]
    // 0xafb3e4: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb3e4: sub             lr, x0, #0x114
    //     0xafb3e8: ldr             lr, [x21, lr, lsl #3]
    //     0xafb3ec: blr             lr
    // 0xafb3f0: mov             x3, x0
    // 0xafb3f4: r2 = Null
    //     0xafb3f4: mov             x2, NULL
    // 0xafb3f8: r1 = Null
    //     0xafb3f8: mov             x1, NULL
    // 0xafb3fc: stur            x3, [fp, #-0x18]
    // 0xafb400: r4 = 60
    //     0xafb400: movz            x4, #0x3c
    // 0xafb404: branchIfSmi(r0, 0xafb410)
    //     0xafb404: tbz             w0, #0, #0xafb410
    // 0xafb408: r4 = LoadClassIdInstr(r0)
    //     0xafb408: ldur            x4, [x0, #-1]
    //     0xafb40c: ubfx            x4, x4, #0xc, #0x14
    // 0xafb410: cmp             x4, #0x3f
    // 0xafb414: b.eq            #0xafb428
    // 0xafb418: r8 = bool?
    //     0xafb418: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xafb41c: r3 = Null
    //     0xafb41c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd50] Null
    //     0xafb420: ldr             x3, [x3, #0xd50]
    // 0xafb424: r0 = bool?()
    //     0xafb424: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xafb428: ldur            x0, [fp, #-0x18]
    // 0xafb42c: cmp             w0, NULL
    // 0xafb430: b.ne            #0xafb43c
    // 0xafb434: r4 = true
    //     0xafb434: add             x4, NULL, #0x20  ; true
    // 0xafb438: b               #0xafb440
    // 0xafb43c: mov             x4, x0
    // 0xafb440: ldur            x1, [fp, #-8]
    // 0xafb444: ldur            x3, [fp, #-0x10]
    // 0xafb448: stur            x4, [fp, #-0x18]
    // 0xafb44c: r0 = LoadClassIdInstr(r1)
    //     0xafb44c: ldur            x0, [x1, #-1]
    //     0xafb450: ubfx            x0, x0, #0xc, #0x14
    // 0xafb454: r2 = "auto_play_duration"
    //     0xafb454: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dd60] "auto_play_duration"
    //     0xafb458: ldr             x2, [x2, #0xd60]
    // 0xafb45c: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb45c: sub             lr, x0, #0x114
    //     0xafb460: ldr             lr, [x21, lr, lsl #3]
    //     0xafb464: blr             lr
    // 0xafb468: mov             x3, x0
    // 0xafb46c: r2 = Null
    //     0xafb46c: mov             x2, NULL
    // 0xafb470: r1 = Null
    //     0xafb470: mov             x1, NULL
    // 0xafb474: stur            x3, [fp, #-8]
    // 0xafb478: branchIfSmi(r0, 0xafb4a0)
    //     0xafb478: tbz             w0, #0, #0xafb4a0
    // 0xafb47c: r4 = LoadClassIdInstr(r0)
    //     0xafb47c: ldur            x4, [x0, #-1]
    //     0xafb480: ubfx            x4, x4, #0xc, #0x14
    // 0xafb484: sub             x4, x4, #0x3c
    // 0xafb488: cmp             x4, #1
    // 0xafb48c: b.ls            #0xafb4a0
    // 0xafb490: r8 = int?
    //     0xafb490: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xafb494: r3 = Null
    //     0xafb494: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd68] Null
    //     0xafb498: ldr             x3, [x3, #0xd68]
    // 0xafb49c: r0 = int?()
    //     0xafb49c: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xafb4a0: r0 = BannerOptions()
    //     0xafb4a0: bl              #0xafb4d0  ; AllocateBannerOptionsStub -> BannerOptions (size=0x14)
    // 0xafb4a4: ldur            x1, [fp, #-0x10]
    // 0xafb4a8: StoreField: r0->field_7 = r1
    //     0xafb4a8: stur            w1, [x0, #7]
    // 0xafb4ac: ldur            x1, [fp, #-0x18]
    // 0xafb4b0: StoreField: r0->field_b = r1
    //     0xafb4b0: stur            w1, [x0, #0xb]
    // 0xafb4b4: ldur            x1, [fp, #-8]
    // 0xafb4b8: StoreField: r0->field_f = r1
    //     0xafb4b8: stur            w1, [x0, #0xf]
    // 0xafb4bc: LeaveFrame
    //     0xafb4bc: mov             SP, fp
    //     0xafb4c0: ldp             fp, lr, [SP], #0x10
    // 0xafb4c4: ret
    //     0xafb4c4: ret             
    // 0xafb4c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb4c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb4cc: b               #0xafb34c
  }
}

// class id: 1161, size: 0x1c, field offset: 0x8
class Banner extends Object {

  factory Banner Banner.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafb510, size: 0x1cc
    // 0xafb510: EnterFrame
    //     0xafb510: stp             fp, lr, [SP, #-0x10]!
    //     0xafb514: mov             fp, SP
    // 0xafb518: AllocStack(0x20)
    //     0xafb518: sub             SP, SP, #0x20
    // 0xafb51c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xafb51c: mov             x3, x2
    //     0xafb520: stur            x2, [fp, #-8]
    // 0xafb524: CheckStackOverflow
    //     0xafb524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafb528: cmp             SP, x16
    //     0xafb52c: b.ls            #0xafb6d4
    // 0xafb530: r0 = LoadClassIdInstr(r3)
    //     0xafb530: ldur            x0, [x3, #-1]
    //     0xafb534: ubfx            x0, x0, #0xc, #0x14
    // 0xafb538: mov             x1, x3
    // 0xafb53c: r2 = "order"
    //     0xafb53c: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0xafb540: ldr             x2, [x2, #0xb78]
    // 0xafb544: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb544: sub             lr, x0, #0x114
    //     0xafb548: ldr             lr, [x21, lr, lsl #3]
    //     0xafb54c: blr             lr
    // 0xafb550: mov             x3, x0
    // 0xafb554: r2 = Null
    //     0xafb554: mov             x2, NULL
    // 0xafb558: r1 = Null
    //     0xafb558: mov             x1, NULL
    // 0xafb55c: stur            x3, [fp, #-0x10]
    // 0xafb560: branchIfSmi(r0, 0xafb588)
    //     0xafb560: tbz             w0, #0, #0xafb588
    // 0xafb564: r4 = LoadClassIdInstr(r0)
    //     0xafb564: ldur            x4, [x0, #-1]
    //     0xafb568: ubfx            x4, x4, #0xc, #0x14
    // 0xafb56c: sub             x4, x4, #0x3c
    // 0xafb570: cmp             x4, #1
    // 0xafb574: b.ls            #0xafb588
    // 0xafb578: r8 = int?
    //     0xafb578: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xafb57c: r3 = Null
    //     0xafb57c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd78] Null
    //     0xafb580: ldr             x3, [x3, #0xd78]
    // 0xafb584: r0 = int?()
    //     0xafb584: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xafb588: ldur            x0, [fp, #-0x10]
    // 0xafb58c: cmp             w0, NULL
    // 0xafb590: b.ne            #0xafb59c
    // 0xafb594: r4 = 0
    //     0xafb594: movz            x4, #0
    // 0xafb598: b               #0xafb5ac
    // 0xafb59c: r1 = LoadInt32Instr(r0)
    //     0xafb59c: sbfx            x1, x0, #1, #0x1f
    //     0xafb5a0: tbz             w0, #0, #0xafb5a8
    //     0xafb5a4: ldur            x1, [x0, #7]
    // 0xafb5a8: mov             x4, x1
    // 0xafb5ac: ldur            x3, [fp, #-8]
    // 0xafb5b0: stur            x4, [fp, #-0x18]
    // 0xafb5b4: r0 = LoadClassIdInstr(r3)
    //     0xafb5b4: ldur            x0, [x3, #-1]
    //     0xafb5b8: ubfx            x0, x0, #0xc, #0x14
    // 0xafb5bc: mov             x1, x3
    // 0xafb5c0: r2 = "route"
    //     0xafb5c0: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xafb5c4: ldr             x2, [x2, #0x3a0]
    // 0xafb5c8: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb5c8: sub             lr, x0, #0x114
    //     0xafb5cc: ldr             lr, [x21, lr, lsl #3]
    //     0xafb5d0: blr             lr
    // 0xafb5d4: mov             x3, x0
    // 0xafb5d8: r2 = Null
    //     0xafb5d8: mov             x2, NULL
    // 0xafb5dc: r1 = Null
    //     0xafb5dc: mov             x1, NULL
    // 0xafb5e0: stur            x3, [fp, #-0x10]
    // 0xafb5e4: r4 = 60
    //     0xafb5e4: movz            x4, #0x3c
    // 0xafb5e8: branchIfSmi(r0, 0xafb5f4)
    //     0xafb5e8: tbz             w0, #0, #0xafb5f4
    // 0xafb5ec: r4 = LoadClassIdInstr(r0)
    //     0xafb5ec: ldur            x4, [x0, #-1]
    //     0xafb5f0: ubfx            x4, x4, #0xc, #0x14
    // 0xafb5f4: sub             x4, x4, #0x5e
    // 0xafb5f8: cmp             x4, #1
    // 0xafb5fc: b.ls            #0xafb610
    // 0xafb600: r8 = String?
    //     0xafb600: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xafb604: r3 = Null
    //     0xafb604: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd88] Null
    //     0xafb608: ldr             x3, [x3, #0xd88]
    // 0xafb60c: r0 = String?()
    //     0xafb60c: bl              #0x600324  ; IsType_String?_Stub
    // 0xafb610: ldur            x3, [fp, #-8]
    // 0xafb614: r0 = LoadClassIdInstr(r3)
    //     0xafb614: ldur            x0, [x3, #-1]
    //     0xafb618: ubfx            x0, x0, #0xc, #0x14
    // 0xafb61c: mov             x1, x3
    // 0xafb620: r2 = "params"
    //     0xafb620: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0xafb624: ldr             x2, [x2, #0x3b8]
    // 0xafb628: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb628: sub             lr, x0, #0x114
    //     0xafb62c: ldr             lr, [x21, lr, lsl #3]
    //     0xafb630: blr             lr
    // 0xafb634: mov             x3, x0
    // 0xafb638: r2 = Null
    //     0xafb638: mov             x2, NULL
    // 0xafb63c: r1 = Null
    //     0xafb63c: mov             x1, NULL
    // 0xafb640: stur            x3, [fp, #-0x20]
    // 0xafb644: r8 = Map<String, dynamic>?
    //     0xafb644: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0xafb648: r3 = Null
    //     0xafb648: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd98] Null
    //     0xafb64c: ldr             x3, [x3, #0xd98]
    // 0xafb650: r0 = Map<String, dynamic>?()
    //     0xafb650: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0xafb654: ldur            x1, [fp, #-8]
    // 0xafb658: r0 = LoadClassIdInstr(r1)
    //     0xafb658: ldur            x0, [x1, #-1]
    //     0xafb65c: ubfx            x0, x0, #0xc, #0x14
    // 0xafb660: r2 = "image"
    //     0xafb660: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xafb664: ldr             x2, [x2, #0x520]
    // 0xafb668: r0 = GDT[cid_x0 + -0x114]()
    //     0xafb668: sub             lr, x0, #0x114
    //     0xafb66c: ldr             lr, [x21, lr, lsl #3]
    //     0xafb670: blr             lr
    // 0xafb674: mov             x3, x0
    // 0xafb678: r2 = Null
    //     0xafb678: mov             x2, NULL
    // 0xafb67c: r1 = Null
    //     0xafb67c: mov             x1, NULL
    // 0xafb680: stur            x3, [fp, #-8]
    // 0xafb684: r8 = Map<String, dynamic>
    //     0xafb684: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xafb688: r3 = Null
    //     0xafb688: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dda8] Null
    //     0xafb68c: ldr             x3, [x3, #0xda8]
    // 0xafb690: r0 = Map<String, dynamic>()
    //     0xafb690: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xafb694: ldur            x2, [fp, #-8]
    // 0xafb698: r1 = Null
    //     0xafb698: mov             x1, NULL
    // 0xafb69c: r0 = Image.fromMap()
    //     0xafb69c: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xafb6a0: stur            x0, [fp, #-8]
    // 0xafb6a4: r0 = Banner()
    //     0xafb6a4: bl              #0xafb6dc  ; AllocateBannerStub -> Banner (size=0x1c)
    // 0xafb6a8: ldur            x1, [fp, #-0x18]
    // 0xafb6ac: StoreField: r0->field_7 = r1
    //     0xafb6ac: stur            x1, [x0, #7]
    // 0xafb6b0: ldur            x1, [fp, #-0x10]
    // 0xafb6b4: StoreField: r0->field_f = r1
    //     0xafb6b4: stur            w1, [x0, #0xf]
    // 0xafb6b8: ldur            x1, [fp, #-0x20]
    // 0xafb6bc: StoreField: r0->field_13 = r1
    //     0xafb6bc: stur            w1, [x0, #0x13]
    // 0xafb6c0: ldur            x1, [fp, #-8]
    // 0xafb6c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xafb6c4: stur            w1, [x0, #0x17]
    // 0xafb6c8: LeaveFrame
    //     0xafb6c8: mov             SP, fp
    //     0xafb6cc: ldp             fp, lr, [SP], #0x10
    // 0xafb6d0: ret
    //     0xafb6d0: ret             
    // 0xafb6d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafb6d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafb6d8: b               #0xafb530
  }
}
