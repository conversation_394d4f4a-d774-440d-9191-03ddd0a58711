// lib: , url: package:nuonline/app/data/models/calendar.dart

// class id: 1050008, size: 0x8
class :: {
}

// class id: 1159, size: 0x30, field offset: 0x8
class HijriResponse extends Object {

  factory _ HijriResponse.fromJson(/* No info */) {
    // ** addr: 0x8ee8c0, size: 0x2e8
    // 0x8ee8c0: EnterFrame
    //     0x8ee8c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ee8c4: mov             fp, SP
    // 0x8ee8c8: AllocStack(0x38)
    //     0x8ee8c8: sub             SP, SP, #0x38
    // 0x8ee8cc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ee8cc: mov             x3, x2
    //     0x8ee8d0: stur            x2, [fp, #-8]
    // 0x8ee8d4: CheckStackOverflow
    //     0x8ee8d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ee8d8: cmp             SP, x16
    //     0x8ee8dc: b.ls            #0x8eeba0
    // 0x8ee8e0: r0 = LoadClassIdInstr(r3)
    //     0x8ee8e0: ldur            x0, [x3, #-1]
    //     0x8ee8e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee8e8: mov             x1, x3
    // 0x8ee8ec: r2 = "id"
    //     0x8ee8ec: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ee8f0: ldr             x2, [x2, #0x740]
    // 0x8ee8f4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ee8f4: sub             lr, x0, #0x114
    //     0x8ee8f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ee8fc: blr             lr
    // 0x8ee900: mov             x3, x0
    // 0x8ee904: r2 = Null
    //     0x8ee904: mov             x2, NULL
    // 0x8ee908: r1 = Null
    //     0x8ee908: mov             x1, NULL
    // 0x8ee90c: stur            x3, [fp, #-0x10]
    // 0x8ee910: branchIfSmi(r0, 0x8ee938)
    //     0x8ee910: tbz             w0, #0, #0x8ee938
    // 0x8ee914: r4 = LoadClassIdInstr(r0)
    //     0x8ee914: ldur            x4, [x0, #-1]
    //     0x8ee918: ubfx            x4, x4, #0xc, #0x14
    // 0x8ee91c: sub             x4, x4, #0x3c
    // 0x8ee920: cmp             x4, #1
    // 0x8ee924: b.ls            #0x8ee938
    // 0x8ee928: r8 = int
    //     0x8ee928: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ee92c: r3 = Null
    //     0x8ee92c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40758] Null
    //     0x8ee930: ldr             x3, [x3, #0x758]
    // 0x8ee934: r0 = int()
    //     0x8ee934: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ee938: ldur            x3, [fp, #-8]
    // 0x8ee93c: r0 = LoadClassIdInstr(r3)
    //     0x8ee93c: ldur            x0, [x3, #-1]
    //     0x8ee940: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee944: mov             x1, x3
    // 0x8ee948: r2 = "year"
    //     0x8ee948: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x8ee94c: ldr             x2, [x2, #0x310]
    // 0x8ee950: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ee950: sub             lr, x0, #0x114
    //     0x8ee954: ldr             lr, [x21, lr, lsl #3]
    //     0x8ee958: blr             lr
    // 0x8ee95c: mov             x3, x0
    // 0x8ee960: r2 = Null
    //     0x8ee960: mov             x2, NULL
    // 0x8ee964: r1 = Null
    //     0x8ee964: mov             x1, NULL
    // 0x8ee968: stur            x3, [fp, #-0x18]
    // 0x8ee96c: branchIfSmi(r0, 0x8ee994)
    //     0x8ee96c: tbz             w0, #0, #0x8ee994
    // 0x8ee970: r4 = LoadClassIdInstr(r0)
    //     0x8ee970: ldur            x4, [x0, #-1]
    //     0x8ee974: ubfx            x4, x4, #0xc, #0x14
    // 0x8ee978: sub             x4, x4, #0x3c
    // 0x8ee97c: cmp             x4, #1
    // 0x8ee980: b.ls            #0x8ee994
    // 0x8ee984: r8 = int
    //     0x8ee984: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ee988: r3 = Null
    //     0x8ee988: add             x3, PP, #0x40, lsl #12  ; [pp+0x40768] Null
    //     0x8ee98c: ldr             x3, [x3, #0x768]
    // 0x8ee990: r0 = int()
    //     0x8ee990: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ee994: ldur            x3, [fp, #-8]
    // 0x8ee998: r0 = LoadClassIdInstr(r3)
    //     0x8ee998: ldur            x0, [x3, #-1]
    //     0x8ee99c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee9a0: mov             x1, x3
    // 0x8ee9a4: r2 = "month"
    //     0x8ee9a4: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x8ee9a8: ldr             x2, [x2, #0x328]
    // 0x8ee9ac: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ee9ac: sub             lr, x0, #0x114
    //     0x8ee9b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8ee9b4: blr             lr
    // 0x8ee9b8: mov             x3, x0
    // 0x8ee9bc: r2 = Null
    //     0x8ee9bc: mov             x2, NULL
    // 0x8ee9c0: r1 = Null
    //     0x8ee9c0: mov             x1, NULL
    // 0x8ee9c4: stur            x3, [fp, #-0x20]
    // 0x8ee9c8: branchIfSmi(r0, 0x8ee9f0)
    //     0x8ee9c8: tbz             w0, #0, #0x8ee9f0
    // 0x8ee9cc: r4 = LoadClassIdInstr(r0)
    //     0x8ee9cc: ldur            x4, [x0, #-1]
    //     0x8ee9d0: ubfx            x4, x4, #0xc, #0x14
    // 0x8ee9d4: sub             x4, x4, #0x3c
    // 0x8ee9d8: cmp             x4, #1
    // 0x8ee9dc: b.ls            #0x8ee9f0
    // 0x8ee9e0: r8 = int
    //     0x8ee9e0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ee9e4: r3 = Null
    //     0x8ee9e4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40778] Null
    //     0x8ee9e8: ldr             x3, [x3, #0x778]
    // 0x8ee9ec: r0 = int()
    //     0x8ee9ec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ee9f0: ldur            x3, [fp, #-8]
    // 0x8ee9f4: r0 = LoadClassIdInstr(r3)
    //     0x8ee9f4: ldur            x0, [x3, #-1]
    //     0x8ee9f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee9fc: mov             x1, x3
    // 0x8eea00: r2 = "number_of_days"
    //     0x8eea00: add             x2, PP, #0x40, lsl #12  ; [pp+0x40788] "number_of_days"
    //     0x8eea04: ldr             x2, [x2, #0x788]
    // 0x8eea08: r0 = GDT[cid_x0 + -0x114]()
    //     0x8eea08: sub             lr, x0, #0x114
    //     0x8eea0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8eea10: blr             lr
    // 0x8eea14: mov             x3, x0
    // 0x8eea18: r2 = Null
    //     0x8eea18: mov             x2, NULL
    // 0x8eea1c: r1 = Null
    //     0x8eea1c: mov             x1, NULL
    // 0x8eea20: stur            x3, [fp, #-0x28]
    // 0x8eea24: branchIfSmi(r0, 0x8eea4c)
    //     0x8eea24: tbz             w0, #0, #0x8eea4c
    // 0x8eea28: r4 = LoadClassIdInstr(r0)
    //     0x8eea28: ldur            x4, [x0, #-1]
    //     0x8eea2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8eea30: sub             x4, x4, #0x3c
    // 0x8eea34: cmp             x4, #1
    // 0x8eea38: b.ls            #0x8eea4c
    // 0x8eea3c: r8 = int
    //     0x8eea3c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8eea40: r3 = Null
    //     0x8eea40: add             x3, PP, #0x40, lsl #12  ; [pp+0x40790] Null
    //     0x8eea44: ldr             x3, [x3, #0x790]
    // 0x8eea48: r0 = int()
    //     0x8eea48: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8eea4c: ldur            x3, [fp, #-8]
    // 0x8eea50: r0 = LoadClassIdInstr(r3)
    //     0x8eea50: ldur            x0, [x3, #-1]
    //     0x8eea54: ubfx            x0, x0, #0xc, #0x14
    // 0x8eea58: mov             x1, x3
    // 0x8eea5c: r2 = "started_at"
    //     0x8eea5c: add             x2, PP, #0x40, lsl #12  ; [pp+0x407a0] "started_at"
    //     0x8eea60: ldr             x2, [x2, #0x7a0]
    // 0x8eea64: r0 = GDT[cid_x0 + -0x114]()
    //     0x8eea64: sub             lr, x0, #0x114
    //     0x8eea68: ldr             lr, [x21, lr, lsl #3]
    //     0x8eea6c: blr             lr
    // 0x8eea70: mov             x3, x0
    // 0x8eea74: r2 = Null
    //     0x8eea74: mov             x2, NULL
    // 0x8eea78: r1 = Null
    //     0x8eea78: mov             x1, NULL
    // 0x8eea7c: stur            x3, [fp, #-0x30]
    // 0x8eea80: r4 = 60
    //     0x8eea80: movz            x4, #0x3c
    // 0x8eea84: branchIfSmi(r0, 0x8eea90)
    //     0x8eea84: tbz             w0, #0, #0x8eea90
    // 0x8eea88: r4 = LoadClassIdInstr(r0)
    //     0x8eea88: ldur            x4, [x0, #-1]
    //     0x8eea8c: ubfx            x4, x4, #0xc, #0x14
    // 0x8eea90: sub             x4, x4, #0x5e
    // 0x8eea94: cmp             x4, #1
    // 0x8eea98: b.ls            #0x8eeaac
    // 0x8eea9c: r8 = String
    //     0x8eea9c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8eeaa0: r3 = Null
    //     0x8eeaa0: add             x3, PP, #0x40, lsl #12  ; [pp+0x407a8] Null
    //     0x8eeaa4: ldr             x3, [x3, #0x7a8]
    // 0x8eeaa8: r0 = String()
    //     0x8eeaa8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8eeaac: ldur            x1, [fp, #-0x30]
    // 0x8eeab0: r0 = parse()
    //     0x8eeab0: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x8eeab4: mov             x3, x0
    // 0x8eeab8: ldur            x1, [fp, #-8]
    // 0x8eeabc: stur            x3, [fp, #-0x30]
    // 0x8eeac0: r0 = LoadClassIdInstr(r1)
    //     0x8eeac0: ldur            x0, [x1, #-1]
    //     0x8eeac4: ubfx            x0, x0, #0xc, #0x14
    // 0x8eeac8: r2 = "ended_at"
    //     0x8eeac8: add             x2, PP, #0x40, lsl #12  ; [pp+0x407b8] "ended_at"
    //     0x8eeacc: ldr             x2, [x2, #0x7b8]
    // 0x8eead0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8eead0: sub             lr, x0, #0x114
    //     0x8eead4: ldr             lr, [x21, lr, lsl #3]
    //     0x8eead8: blr             lr
    // 0x8eeadc: mov             x3, x0
    // 0x8eeae0: r2 = Null
    //     0x8eeae0: mov             x2, NULL
    // 0x8eeae4: r1 = Null
    //     0x8eeae4: mov             x1, NULL
    // 0x8eeae8: stur            x3, [fp, #-8]
    // 0x8eeaec: r4 = 60
    //     0x8eeaec: movz            x4, #0x3c
    // 0x8eeaf0: branchIfSmi(r0, 0x8eeafc)
    //     0x8eeaf0: tbz             w0, #0, #0x8eeafc
    // 0x8eeaf4: r4 = LoadClassIdInstr(r0)
    //     0x8eeaf4: ldur            x4, [x0, #-1]
    //     0x8eeaf8: ubfx            x4, x4, #0xc, #0x14
    // 0x8eeafc: sub             x4, x4, #0x5e
    // 0x8eeb00: cmp             x4, #1
    // 0x8eeb04: b.ls            #0x8eeb18
    // 0x8eeb08: r8 = String
    //     0x8eeb08: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8eeb0c: r3 = Null
    //     0x8eeb0c: add             x3, PP, #0x40, lsl #12  ; [pp+0x407c0] Null
    //     0x8eeb10: ldr             x3, [x3, #0x7c0]
    // 0x8eeb14: r0 = String()
    //     0x8eeb14: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8eeb18: ldur            x1, [fp, #-8]
    // 0x8eeb1c: r0 = parse()
    //     0x8eeb1c: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x8eeb20: mov             x1, x0
    // 0x8eeb24: ldur            x0, [fp, #-0x10]
    // 0x8eeb28: stur            x1, [fp, #-8]
    // 0x8eeb2c: r2 = LoadInt32Instr(r0)
    //     0x8eeb2c: sbfx            x2, x0, #1, #0x1f
    //     0x8eeb30: tbz             w0, #0, #0x8eeb38
    //     0x8eeb34: ldur            x2, [x0, #7]
    // 0x8eeb38: stur            x2, [fp, #-0x38]
    // 0x8eeb3c: r0 = HijriResponse()
    //     0x8eeb3c: bl              #0x8eed54  ; AllocateHijriResponseStub -> HijriResponse (size=0x30)
    // 0x8eeb40: ldur            x1, [fp, #-0x38]
    // 0x8eeb44: StoreField: r0->field_7 = r1
    //     0x8eeb44: stur            x1, [x0, #7]
    // 0x8eeb48: ldur            x1, [fp, #-0x18]
    // 0x8eeb4c: r2 = LoadInt32Instr(r1)
    //     0x8eeb4c: sbfx            x2, x1, #1, #0x1f
    //     0x8eeb50: tbz             w1, #0, #0x8eeb58
    //     0x8eeb54: ldur            x2, [x1, #7]
    // 0x8eeb58: StoreField: r0->field_f = r2
    //     0x8eeb58: stur            x2, [x0, #0xf]
    // 0x8eeb5c: ldur            x1, [fp, #-0x20]
    // 0x8eeb60: r2 = LoadInt32Instr(r1)
    //     0x8eeb60: sbfx            x2, x1, #1, #0x1f
    //     0x8eeb64: tbz             w1, #0, #0x8eeb6c
    //     0x8eeb68: ldur            x2, [x1, #7]
    // 0x8eeb6c: ArrayStore: r0[0] = r2  ; List_8
    //     0x8eeb6c: stur            x2, [x0, #0x17]
    // 0x8eeb70: ldur            x1, [fp, #-0x28]
    // 0x8eeb74: r2 = LoadInt32Instr(r1)
    //     0x8eeb74: sbfx            x2, x1, #1, #0x1f
    //     0x8eeb78: tbz             w1, #0, #0x8eeb80
    //     0x8eeb7c: ldur            x2, [x1, #7]
    // 0x8eeb80: StoreField: r0->field_1f = r2
    //     0x8eeb80: stur            x2, [x0, #0x1f]
    // 0x8eeb84: ldur            x1, [fp, #-0x30]
    // 0x8eeb88: StoreField: r0->field_27 = r1
    //     0x8eeb88: stur            w1, [x0, #0x27]
    // 0x8eeb8c: ldur            x1, [fp, #-8]
    // 0x8eeb90: StoreField: r0->field_2b = r1
    //     0x8eeb90: stur            w1, [x0, #0x2b]
    // 0x8eeb94: LeaveFrame
    //     0x8eeb94: mov             SP, fp
    //     0x8eeb98: ldp             fp, lr, [SP], #0x10
    // 0x8eeb9c: ret
    //     0x8eeb9c: ret             
    // 0x8eeba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eeba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eeba4: b               #0x8ee8e0
  }
  Map<String, dynamic> toJson(HijriResponse) {
    // ** addr: 0x8eebc0, size: 0x48
    // 0x8eebc0: EnterFrame
    //     0x8eebc0: stp             fp, lr, [SP, #-0x10]!
    //     0x8eebc4: mov             fp, SP
    // 0x8eebc8: CheckStackOverflow
    //     0x8eebc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eebcc: cmp             SP, x16
    //     0x8eebd0: b.ls            #0x8eebe8
    // 0x8eebd4: ldr             x1, [fp, #0x10]
    // 0x8eebd8: r0 = toJson()
    //     0x8eebd8: bl              #0x8eebf0  ; [package:nuonline/app/data/models/calendar.dart] HijriResponse::toJson
    // 0x8eebdc: LeaveFrame
    //     0x8eebdc: mov             SP, fp
    //     0x8eebe0: ldp             fp, lr, [SP], #0x10
    // 0x8eebe4: ret
    //     0x8eebe4: ret             
    // 0x8eebe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eebe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eebec: b               #0x8eebd4
  }
  Map<String, dynamic> toJson(HijriResponse) {
    // ** addr: 0x8eebf0, size: 0x144
    // 0x8eebf0: EnterFrame
    //     0x8eebf0: stp             fp, lr, [SP, #-0x10]!
    //     0x8eebf4: mov             fp, SP
    // 0x8eebf8: AllocStack(0x20)
    //     0x8eebf8: sub             SP, SP, #0x20
    // 0x8eebfc: SetupParameters(HijriResponse this /* r1 => r1, fp-0x8 */)
    //     0x8eebfc: stur            x1, [fp, #-8]
    // 0x8eec00: CheckStackOverflow
    //     0x8eec00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eec04: cmp             SP, x16
    //     0x8eec08: b.ls            #0x8eed2c
    // 0x8eec0c: r16 = <String, dynamic>
    //     0x8eec0c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8eec10: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8eec14: stp             lr, x16, [SP]
    // 0x8eec18: r0 = Map._fromLiteral()
    //     0x8eec18: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8eec1c: mov             x5, x0
    // 0x8eec20: ldur            x4, [fp, #-8]
    // 0x8eec24: stur            x5, [fp, #-0x10]
    // 0x8eec28: LoadField: r2 = r4->field_7
    //     0x8eec28: ldur            x2, [x4, #7]
    // 0x8eec2c: r0 = BoxInt64Instr(r2)
    //     0x8eec2c: sbfiz           x0, x2, #1, #0x1f
    //     0x8eec30: cmp             x2, x0, asr #1
    //     0x8eec34: b.eq            #0x8eec40
    //     0x8eec38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eec3c: stur            x2, [x0, #7]
    // 0x8eec40: mov             x1, x5
    // 0x8eec44: mov             x3, x0
    // 0x8eec48: r2 = "id"
    //     0x8eec48: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8eec4c: ldr             x2, [x2, #0x740]
    // 0x8eec50: r0 = []=()
    //     0x8eec50: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eec54: ldur            x4, [fp, #-8]
    // 0x8eec58: LoadField: r2 = r4->field_f
    //     0x8eec58: ldur            x2, [x4, #0xf]
    // 0x8eec5c: r0 = BoxInt64Instr(r2)
    //     0x8eec5c: sbfiz           x0, x2, #1, #0x1f
    //     0x8eec60: cmp             x2, x0, asr #1
    //     0x8eec64: b.eq            #0x8eec70
    //     0x8eec68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eec6c: stur            x2, [x0, #7]
    // 0x8eec70: ldur            x1, [fp, #-0x10]
    // 0x8eec74: mov             x3, x0
    // 0x8eec78: r2 = "year"
    //     0x8eec78: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x8eec7c: ldr             x2, [x2, #0x310]
    // 0x8eec80: r0 = []=()
    //     0x8eec80: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eec84: ldur            x4, [fp, #-8]
    // 0x8eec88: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x8eec88: ldur            x2, [x4, #0x17]
    // 0x8eec8c: r0 = BoxInt64Instr(r2)
    //     0x8eec8c: sbfiz           x0, x2, #1, #0x1f
    //     0x8eec90: cmp             x2, x0, asr #1
    //     0x8eec94: b.eq            #0x8eeca0
    //     0x8eec98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eec9c: stur            x2, [x0, #7]
    // 0x8eeca0: ldur            x1, [fp, #-0x10]
    // 0x8eeca4: mov             x3, x0
    // 0x8eeca8: r2 = "month"
    //     0x8eeca8: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x8eecac: ldr             x2, [x2, #0x328]
    // 0x8eecb0: r0 = []=()
    //     0x8eecb0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eecb4: ldur            x4, [fp, #-8]
    // 0x8eecb8: LoadField: r2 = r4->field_1f
    //     0x8eecb8: ldur            x2, [x4, #0x1f]
    // 0x8eecbc: r0 = BoxInt64Instr(r2)
    //     0x8eecbc: sbfiz           x0, x2, #1, #0x1f
    //     0x8eecc0: cmp             x2, x0, asr #1
    //     0x8eecc4: b.eq            #0x8eecd0
    //     0x8eecc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eeccc: stur            x2, [x0, #7]
    // 0x8eecd0: ldur            x1, [fp, #-0x10]
    // 0x8eecd4: mov             x3, x0
    // 0x8eecd8: r2 = "number_of_days"
    //     0x8eecd8: add             x2, PP, #0x40, lsl #12  ; [pp+0x40788] "number_of_days"
    //     0x8eecdc: ldr             x2, [x2, #0x788]
    // 0x8eece0: r0 = []=()
    //     0x8eece0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eece4: ldur            x0, [fp, #-8]
    // 0x8eece8: LoadField: r3 = r0->field_27
    //     0x8eece8: ldur            w3, [x0, #0x27]
    // 0x8eecec: DecompressPointer r3
    //     0x8eecec: add             x3, x3, HEAP, lsl #32
    // 0x8eecf0: ldur            x1, [fp, #-0x10]
    // 0x8eecf4: r2 = "started_at"
    //     0x8eecf4: add             x2, PP, #0x40, lsl #12  ; [pp+0x407a0] "started_at"
    //     0x8eecf8: ldr             x2, [x2, #0x7a0]
    // 0x8eecfc: r0 = []=()
    //     0x8eecfc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eed00: ldur            x0, [fp, #-8]
    // 0x8eed04: LoadField: r3 = r0->field_2b
    //     0x8eed04: ldur            w3, [x0, #0x2b]
    // 0x8eed08: DecompressPointer r3
    //     0x8eed08: add             x3, x3, HEAP, lsl #32
    // 0x8eed0c: ldur            x1, [fp, #-0x10]
    // 0x8eed10: r2 = "ended_at"
    //     0x8eed10: add             x2, PP, #0x40, lsl #12  ; [pp+0x407b8] "ended_at"
    //     0x8eed14: ldr             x2, [x2, #0x7b8]
    // 0x8eed18: r0 = []=()
    //     0x8eed18: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8eed1c: ldur            x0, [fp, #-0x10]
    // 0x8eed20: LeaveFrame
    //     0x8eed20: mov             SP, fp
    //     0x8eed24: ldp             fp, lr, [SP], #0x10
    // 0x8eed28: ret
    //     0x8eed28: ret             
    // 0x8eed2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eed2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eed30: b               #0x8eec0c
  }
}

// class id: 1668, size: 0x14, field offset: 0xc
class HijriDateAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5f3c0, size: 0x3b4
    // 0xa5f3c0: EnterFrame
    //     0xa5f3c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa5f3c4: mov             fp, SP
    // 0xa5f3c8: AllocStack(0x50)
    //     0xa5f3c8: sub             SP, SP, #0x50
    // 0xa5f3cc: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5f3cc: stur            x2, [fp, #-0x20]
    // 0xa5f3d0: CheckStackOverflow
    //     0xa5f3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5f3d4: cmp             SP, x16
    //     0xa5f3d8: b.ls            #0xa5f75c
    // 0xa5f3dc: LoadField: r3 = r2->field_23
    //     0xa5f3dc: ldur            x3, [x2, #0x23]
    // 0xa5f3e0: add             x0, x3, #1
    // 0xa5f3e4: LoadField: r1 = r2->field_1b
    //     0xa5f3e4: ldur            x1, [x2, #0x1b]
    // 0xa5f3e8: cmp             x0, x1
    // 0xa5f3ec: b.gt            #0xa5f700
    // 0xa5f3f0: LoadField: r4 = r2->field_7
    //     0xa5f3f0: ldur            w4, [x2, #7]
    // 0xa5f3f4: DecompressPointer r4
    //     0xa5f3f4: add             x4, x4, HEAP, lsl #32
    // 0xa5f3f8: stur            x4, [fp, #-0x18]
    // 0xa5f3fc: StoreField: r2->field_23 = r0
    //     0xa5f3fc: stur            x0, [x2, #0x23]
    // 0xa5f400: LoadField: r0 = r4->field_13
    //     0xa5f400: ldur            w0, [x4, #0x13]
    // 0xa5f404: r5 = LoadInt32Instr(r0)
    //     0xa5f404: sbfx            x5, x0, #1, #0x1f
    // 0xa5f408: mov             x0, x5
    // 0xa5f40c: mov             x1, x3
    // 0xa5f410: stur            x5, [fp, #-0x10]
    // 0xa5f414: cmp             x1, x0
    // 0xa5f418: b.hs            #0xa5f764
    // 0xa5f41c: LoadField: r0 = r4->field_7
    //     0xa5f41c: ldur            x0, [x4, #7]
    // 0xa5f420: ldrb            w1, [x0, x3]
    // 0xa5f424: stur            x1, [fp, #-8]
    // 0xa5f428: r16 = <int, dynamic>
    //     0xa5f428: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5f42c: ldr             x16, [x16, #0xac0]
    // 0xa5f430: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5f434: stp             lr, x16, [SP]
    // 0xa5f438: r0 = Map._fromLiteral()
    //     0xa5f438: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5f43c: mov             x2, x0
    // 0xa5f440: stur            x2, [fp, #-0x38]
    // 0xa5f444: r6 = 0
    //     0xa5f444: movz            x6, #0
    // 0xa5f448: ldur            x3, [fp, #-0x20]
    // 0xa5f44c: ldur            x4, [fp, #-0x18]
    // 0xa5f450: ldur            x5, [fp, #-8]
    // 0xa5f454: stur            x6, [fp, #-0x30]
    // 0xa5f458: CheckStackOverflow
    //     0xa5f458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5f45c: cmp             SP, x16
    //     0xa5f460: b.ls            #0xa5f768
    // 0xa5f464: cmp             x6, x5
    // 0xa5f468: b.ge            #0xa5f4f4
    // 0xa5f46c: LoadField: r7 = r3->field_23
    //     0xa5f46c: ldur            x7, [x3, #0x23]
    // 0xa5f470: add             x0, x7, #1
    // 0xa5f474: LoadField: r1 = r3->field_1b
    //     0xa5f474: ldur            x1, [x3, #0x1b]
    // 0xa5f478: cmp             x0, x1
    // 0xa5f47c: b.gt            #0xa5f728
    // 0xa5f480: StoreField: r3->field_23 = r0
    //     0xa5f480: stur            x0, [x3, #0x23]
    // 0xa5f484: ldur            x0, [fp, #-0x10]
    // 0xa5f488: mov             x1, x7
    // 0xa5f48c: cmp             x1, x0
    // 0xa5f490: b.hs            #0xa5f770
    // 0xa5f494: LoadField: r0 = r4->field_7
    //     0xa5f494: ldur            x0, [x4, #7]
    // 0xa5f498: ldrb            w8, [x0, x7]
    // 0xa5f49c: mov             x1, x3
    // 0xa5f4a0: stur            x8, [fp, #-0x28]
    // 0xa5f4a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5f4a4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5f4a8: r0 = read()
    //     0xa5f4a8: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5f4ac: mov             x1, x0
    // 0xa5f4b0: ldur            x0, [fp, #-0x28]
    // 0xa5f4b4: lsl             x2, x0, #1
    // 0xa5f4b8: r16 = LoadInt32Instr(r2)
    //     0xa5f4b8: sbfx            x16, x2, #1, #0x1f
    // 0xa5f4bc: r17 = 11601
    //     0xa5f4bc: movz            x17, #0x2d51
    // 0xa5f4c0: mul             x0, x16, x17
    // 0xa5f4c4: umulh           x16, x16, x17
    // 0xa5f4c8: eor             x0, x0, x16
    // 0xa5f4cc: r0 = 0
    //     0xa5f4cc: eor             x0, x0, x0, lsr #32
    // 0xa5f4d0: ubfiz           x0, x0, #1, #0x1e
    // 0xa5f4d4: r5 = LoadInt32Instr(r0)
    //     0xa5f4d4: sbfx            x5, x0, #1, #0x1f
    // 0xa5f4d8: mov             x3, x1
    // 0xa5f4dc: ldur            x1, [fp, #-0x38]
    // 0xa5f4e0: r0 = _set()
    //     0xa5f4e0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5f4e4: ldur            x0, [fp, #-0x30]
    // 0xa5f4e8: add             x6, x0, #1
    // 0xa5f4ec: ldur            x2, [fp, #-0x38]
    // 0xa5f4f0: b               #0xa5f448
    // 0xa5f4f4: mov             x0, x2
    // 0xa5f4f8: mov             x1, x0
    // 0xa5f4fc: r2 = 0
    //     0xa5f4fc: movz            x2, #0
    // 0xa5f500: r0 = _getValueOrData()
    //     0xa5f500: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f504: ldur            x3, [fp, #-0x38]
    // 0xa5f508: LoadField: r1 = r3->field_f
    //     0xa5f508: ldur            w1, [x3, #0xf]
    // 0xa5f50c: DecompressPointer r1
    //     0xa5f50c: add             x1, x1, HEAP, lsl #32
    // 0xa5f510: cmp             w1, w0
    // 0xa5f514: b.ne            #0xa5f520
    // 0xa5f518: r4 = Null
    //     0xa5f518: mov             x4, NULL
    // 0xa5f51c: b               #0xa5f524
    // 0xa5f520: mov             x4, x0
    // 0xa5f524: mov             x0, x4
    // 0xa5f528: stur            x4, [fp, #-0x18]
    // 0xa5f52c: r2 = Null
    //     0xa5f52c: mov             x2, NULL
    // 0xa5f530: r1 = Null
    //     0xa5f530: mov             x1, NULL
    // 0xa5f534: branchIfSmi(r0, 0xa5f55c)
    //     0xa5f534: tbz             w0, #0, #0xa5f55c
    // 0xa5f538: r4 = LoadClassIdInstr(r0)
    //     0xa5f538: ldur            x4, [x0, #-1]
    //     0xa5f53c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f540: sub             x4, x4, #0x3c
    // 0xa5f544: cmp             x4, #1
    // 0xa5f548: b.ls            #0xa5f55c
    // 0xa5f54c: r8 = int
    //     0xa5f54c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5f550: r3 = Null
    //     0xa5f550: add             x3, PP, #0x21, lsl #12  ; [pp+0x216b8] Null
    //     0xa5f554: ldr             x3, [x3, #0x6b8]
    // 0xa5f558: r0 = int()
    //     0xa5f558: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5f55c: ldur            x1, [fp, #-0x38]
    // 0xa5f560: r2 = 2
    //     0xa5f560: movz            x2, #0x2
    // 0xa5f564: r0 = _getValueOrData()
    //     0xa5f564: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f568: ldur            x3, [fp, #-0x38]
    // 0xa5f56c: LoadField: r1 = r3->field_f
    //     0xa5f56c: ldur            w1, [x3, #0xf]
    // 0xa5f570: DecompressPointer r1
    //     0xa5f570: add             x1, x1, HEAP, lsl #32
    // 0xa5f574: cmp             w1, w0
    // 0xa5f578: b.ne            #0xa5f584
    // 0xa5f57c: r4 = Null
    //     0xa5f57c: mov             x4, NULL
    // 0xa5f580: b               #0xa5f588
    // 0xa5f584: mov             x4, x0
    // 0xa5f588: mov             x0, x4
    // 0xa5f58c: stur            x4, [fp, #-0x20]
    // 0xa5f590: r2 = Null
    //     0xa5f590: mov             x2, NULL
    // 0xa5f594: r1 = Null
    //     0xa5f594: mov             x1, NULL
    // 0xa5f598: branchIfSmi(r0, 0xa5f5c0)
    //     0xa5f598: tbz             w0, #0, #0xa5f5c0
    // 0xa5f59c: r4 = LoadClassIdInstr(r0)
    //     0xa5f59c: ldur            x4, [x0, #-1]
    //     0xa5f5a0: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f5a4: sub             x4, x4, #0x3c
    // 0xa5f5a8: cmp             x4, #1
    // 0xa5f5ac: b.ls            #0xa5f5c0
    // 0xa5f5b0: r8 = int
    //     0xa5f5b0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5f5b4: r3 = Null
    //     0xa5f5b4: add             x3, PP, #0x21, lsl #12  ; [pp+0x216c8] Null
    //     0xa5f5b8: ldr             x3, [x3, #0x6c8]
    // 0xa5f5bc: r0 = int()
    //     0xa5f5bc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5f5c0: ldur            x1, [fp, #-0x38]
    // 0xa5f5c4: r2 = 4
    //     0xa5f5c4: movz            x2, #0x4
    // 0xa5f5c8: r0 = _getValueOrData()
    //     0xa5f5c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f5cc: ldur            x3, [fp, #-0x38]
    // 0xa5f5d0: LoadField: r1 = r3->field_f
    //     0xa5f5d0: ldur            w1, [x3, #0xf]
    // 0xa5f5d4: DecompressPointer r1
    //     0xa5f5d4: add             x1, x1, HEAP, lsl #32
    // 0xa5f5d8: cmp             w1, w0
    // 0xa5f5dc: b.ne            #0xa5f5e8
    // 0xa5f5e0: r4 = Null
    //     0xa5f5e0: mov             x4, NULL
    // 0xa5f5e4: b               #0xa5f5ec
    // 0xa5f5e8: mov             x4, x0
    // 0xa5f5ec: mov             x0, x4
    // 0xa5f5f0: stur            x4, [fp, #-0x40]
    // 0xa5f5f4: r2 = Null
    //     0xa5f5f4: mov             x2, NULL
    // 0xa5f5f8: r1 = Null
    //     0xa5f5f8: mov             x1, NULL
    // 0xa5f5fc: branchIfSmi(r0, 0xa5f624)
    //     0xa5f5fc: tbz             w0, #0, #0xa5f624
    // 0xa5f600: r4 = LoadClassIdInstr(r0)
    //     0xa5f600: ldur            x4, [x0, #-1]
    //     0xa5f604: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f608: sub             x4, x4, #0x3c
    // 0xa5f60c: cmp             x4, #1
    // 0xa5f610: b.ls            #0xa5f624
    // 0xa5f614: r8 = int
    //     0xa5f614: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5f618: r3 = Null
    //     0xa5f618: add             x3, PP, #0x21, lsl #12  ; [pp+0x216d8] Null
    //     0xa5f61c: ldr             x3, [x3, #0x6d8]
    // 0xa5f620: r0 = int()
    //     0xa5f620: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5f624: ldur            x1, [fp, #-0x38]
    // 0xa5f628: r2 = 6
    //     0xa5f628: movz            x2, #0x6
    // 0xa5f62c: r0 = _getValueOrData()
    //     0xa5f62c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f630: mov             x1, x0
    // 0xa5f634: ldur            x0, [fp, #-0x38]
    // 0xa5f638: LoadField: r2 = r0->field_f
    //     0xa5f638: ldur            w2, [x0, #0xf]
    // 0xa5f63c: DecompressPointer r2
    //     0xa5f63c: add             x2, x2, HEAP, lsl #32
    // 0xa5f640: cmp             w2, w1
    // 0xa5f644: b.ne            #0xa5f650
    // 0xa5f648: r6 = Null
    //     0xa5f648: mov             x6, NULL
    // 0xa5f64c: b               #0xa5f654
    // 0xa5f650: mov             x6, x1
    // 0xa5f654: ldur            x5, [fp, #-0x18]
    // 0xa5f658: ldur            x4, [fp, #-0x20]
    // 0xa5f65c: ldur            x3, [fp, #-0x40]
    // 0xa5f660: mov             x0, x6
    // 0xa5f664: stur            x6, [fp, #-0x38]
    // 0xa5f668: r2 = Null
    //     0xa5f668: mov             x2, NULL
    // 0xa5f66c: r1 = Null
    //     0xa5f66c: mov             x1, NULL
    // 0xa5f670: r4 = 60
    //     0xa5f670: movz            x4, #0x3c
    // 0xa5f674: branchIfSmi(r0, 0xa5f680)
    //     0xa5f674: tbz             w0, #0, #0xa5f680
    // 0xa5f678: r4 = LoadClassIdInstr(r0)
    //     0xa5f678: ldur            x4, [x0, #-1]
    //     0xa5f67c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f680: sub             x4, x4, #0x5e
    // 0xa5f684: cmp             x4, #1
    // 0xa5f688: b.ls            #0xa5f69c
    // 0xa5f68c: r8 = String
    //     0xa5f68c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5f690: r3 = Null
    //     0xa5f690: add             x3, PP, #0x21, lsl #12  ; [pp+0x216e8] Null
    //     0xa5f694: ldr             x3, [x3, #0x6e8]
    // 0xa5f698: r0 = String()
    //     0xa5f698: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5f69c: ldur            x0, [fp, #-0x18]
    // 0xa5f6a0: r1 = LoadInt32Instr(r0)
    //     0xa5f6a0: sbfx            x1, x0, #1, #0x1f
    //     0xa5f6a4: tbz             w0, #0, #0xa5f6ac
    //     0xa5f6a8: ldur            x1, [x0, #7]
    // 0xa5f6ac: stur            x1, [fp, #-8]
    // 0xa5f6b0: r0 = HijriDate()
    //     0xa5f6b0: bl              #0x8ee738  ; AllocateHijriDateStub -> HijriDate (size=0x24)
    // 0xa5f6b4: mov             x1, x0
    // 0xa5f6b8: ldur            x0, [fp, #-8]
    // 0xa5f6bc: StoreField: r1->field_7 = r0
    //     0xa5f6bc: stur            x0, [x1, #7]
    // 0xa5f6c0: ldur            x0, [fp, #-0x20]
    // 0xa5f6c4: r2 = LoadInt32Instr(r0)
    //     0xa5f6c4: sbfx            x2, x0, #1, #0x1f
    //     0xa5f6c8: tbz             w0, #0, #0xa5f6d0
    //     0xa5f6cc: ldur            x2, [x0, #7]
    // 0xa5f6d0: StoreField: r1->field_f = r2
    //     0xa5f6d0: stur            x2, [x1, #0xf]
    // 0xa5f6d4: ldur            x0, [fp, #-0x40]
    // 0xa5f6d8: r2 = LoadInt32Instr(r0)
    //     0xa5f6d8: sbfx            x2, x0, #1, #0x1f
    //     0xa5f6dc: tbz             w0, #0, #0xa5f6e4
    //     0xa5f6e0: ldur            x2, [x0, #7]
    // 0xa5f6e4: ArrayStore: r1[0] = r2  ; List_8
    //     0xa5f6e4: stur            x2, [x1, #0x17]
    // 0xa5f6e8: ldur            x0, [fp, #-0x38]
    // 0xa5f6ec: StoreField: r1->field_1f = r0
    //     0xa5f6ec: stur            w0, [x1, #0x1f]
    // 0xa5f6f0: mov             x0, x1
    // 0xa5f6f4: LeaveFrame
    //     0xa5f6f4: mov             SP, fp
    //     0xa5f6f8: ldp             fp, lr, [SP], #0x10
    // 0xa5f6fc: ret
    //     0xa5f6fc: ret             
    // 0xa5f700: r0 = RangeError()
    //     0xa5f700: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5f704: mov             x1, x0
    // 0xa5f708: r0 = "Not enough bytes available."
    //     0xa5f708: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f70c: ldr             x0, [x0, #0x8a8]
    // 0xa5f710: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5f710: stur            w0, [x1, #0x17]
    // 0xa5f714: r2 = false
    //     0xa5f714: add             x2, NULL, #0x30  ; false
    // 0xa5f718: StoreField: r1->field_b = r2
    //     0xa5f718: stur            w2, [x1, #0xb]
    // 0xa5f71c: mov             x0, x1
    // 0xa5f720: r0 = Throw()
    //     0xa5f720: bl              #0xec04b8  ; ThrowStub
    // 0xa5f724: brk             #0
    // 0xa5f728: r0 = "Not enough bytes available."
    //     0xa5f728: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f72c: ldr             x0, [x0, #0x8a8]
    // 0xa5f730: r2 = false
    //     0xa5f730: add             x2, NULL, #0x30  ; false
    // 0xa5f734: r0 = RangeError()
    //     0xa5f734: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5f738: mov             x1, x0
    // 0xa5f73c: r0 = "Not enough bytes available."
    //     0xa5f73c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5f740: ldr             x0, [x0, #0x8a8]
    // 0xa5f744: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5f744: stur            w0, [x1, #0x17]
    // 0xa5f748: r0 = false
    //     0xa5f748: add             x0, NULL, #0x30  ; false
    // 0xa5f74c: StoreField: r1->field_b = r0
    //     0xa5f74c: stur            w0, [x1, #0xb]
    // 0xa5f750: mov             x0, x1
    // 0xa5f754: r0 = Throw()
    //     0xa5f754: bl              #0xec04b8  ; ThrowStub
    // 0xa5f758: brk             #0
    // 0xa5f75c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5f75c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5f760: b               #0xa5f3dc
    // 0xa5f764: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5f764: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5f768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5f768: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5f76c: b               #0xa5f464
    // 0xa5f770: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5f770: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd0a40, size: 0x33c
    // 0xbd0a40: EnterFrame
    //     0xbd0a40: stp             fp, lr, [SP, #-0x10]!
    //     0xbd0a44: mov             fp, SP
    // 0xbd0a48: AllocStack(0x28)
    //     0xbd0a48: sub             SP, SP, #0x28
    // 0xbd0a4c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd0a4c: mov             x4, x2
    //     0xbd0a50: stur            x2, [fp, #-8]
    //     0xbd0a54: stur            x3, [fp, #-0x10]
    // 0xbd0a58: CheckStackOverflow
    //     0xbd0a58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd0a5c: cmp             SP, x16
    //     0xbd0a60: b.ls            #0xbd0d60
    // 0xbd0a64: mov             x0, x3
    // 0xbd0a68: r2 = Null
    //     0xbd0a68: mov             x2, NULL
    // 0xbd0a6c: r1 = Null
    //     0xbd0a6c: mov             x1, NULL
    // 0xbd0a70: r4 = 60
    //     0xbd0a70: movz            x4, #0x3c
    // 0xbd0a74: branchIfSmi(r0, 0xbd0a80)
    //     0xbd0a74: tbz             w0, #0, #0xbd0a80
    // 0xbd0a78: r4 = LoadClassIdInstr(r0)
    //     0xbd0a78: ldur            x4, [x0, #-1]
    //     0xbd0a7c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd0a80: r17 = 5595
    //     0xbd0a80: movz            x17, #0x15db
    // 0xbd0a84: cmp             x4, x17
    // 0xbd0a88: b.eq            #0xbd0aa0
    // 0xbd0a8c: r8 = HijriDate
    //     0xbd0a8c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b458] Type: HijriDate
    //     0xbd0a90: ldr             x8, [x8, #0x458]
    // 0xbd0a94: r3 = Null
    //     0xbd0a94: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b460] Null
    //     0xbd0a98: ldr             x3, [x3, #0x460]
    // 0xbd0a9c: r0 = HijriDate()
    //     0xbd0a9c: bl              #0x81541c  ; IsType_HijriDate_Stub
    // 0xbd0aa0: ldur            x0, [fp, #-8]
    // 0xbd0aa4: LoadField: r1 = r0->field_b
    //     0xbd0aa4: ldur            w1, [x0, #0xb]
    // 0xbd0aa8: DecompressPointer r1
    //     0xbd0aa8: add             x1, x1, HEAP, lsl #32
    // 0xbd0aac: LoadField: r2 = r1->field_13
    //     0xbd0aac: ldur            w2, [x1, #0x13]
    // 0xbd0ab0: LoadField: r1 = r0->field_13
    //     0xbd0ab0: ldur            x1, [x0, #0x13]
    // 0xbd0ab4: r3 = LoadInt32Instr(r2)
    //     0xbd0ab4: sbfx            x3, x2, #1, #0x1f
    // 0xbd0ab8: sub             x2, x3, x1
    // 0xbd0abc: cmp             x2, #1
    // 0xbd0ac0: b.ge            #0xbd0ad0
    // 0xbd0ac4: mov             x1, x0
    // 0xbd0ac8: r2 = 1
    //     0xbd0ac8: movz            x2, #0x1
    // 0xbd0acc: r0 = _increaseBufferSize()
    //     0xbd0acc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0ad0: ldur            x3, [fp, #-8]
    // 0xbd0ad4: r2 = 4
    //     0xbd0ad4: movz            x2, #0x4
    // 0xbd0ad8: LoadField: r4 = r3->field_b
    //     0xbd0ad8: ldur            w4, [x3, #0xb]
    // 0xbd0adc: DecompressPointer r4
    //     0xbd0adc: add             x4, x4, HEAP, lsl #32
    // 0xbd0ae0: LoadField: r5 = r3->field_13
    //     0xbd0ae0: ldur            x5, [x3, #0x13]
    // 0xbd0ae4: add             x6, x5, #1
    // 0xbd0ae8: StoreField: r3->field_13 = r6
    //     0xbd0ae8: stur            x6, [x3, #0x13]
    // 0xbd0aec: LoadField: r0 = r4->field_13
    //     0xbd0aec: ldur            w0, [x4, #0x13]
    // 0xbd0af0: r7 = LoadInt32Instr(r0)
    //     0xbd0af0: sbfx            x7, x0, #1, #0x1f
    // 0xbd0af4: mov             x0, x7
    // 0xbd0af8: mov             x1, x5
    // 0xbd0afc: cmp             x1, x0
    // 0xbd0b00: b.hs            #0xbd0d68
    // 0xbd0b04: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd0b04: add             x0, x4, x5
    //     0xbd0b08: strb            w2, [x0, #0x17]
    // 0xbd0b0c: sub             x0, x7, x6
    // 0xbd0b10: cmp             x0, #1
    // 0xbd0b14: b.ge            #0xbd0b24
    // 0xbd0b18: mov             x1, x3
    // 0xbd0b1c: r2 = 1
    //     0xbd0b1c: movz            x2, #0x1
    // 0xbd0b20: r0 = _increaseBufferSize()
    //     0xbd0b20: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0b24: ldur            x2, [fp, #-8]
    // 0xbd0b28: ldur            x3, [fp, #-0x10]
    // 0xbd0b2c: LoadField: r4 = r2->field_b
    //     0xbd0b2c: ldur            w4, [x2, #0xb]
    // 0xbd0b30: DecompressPointer r4
    //     0xbd0b30: add             x4, x4, HEAP, lsl #32
    // 0xbd0b34: LoadField: r5 = r2->field_13
    //     0xbd0b34: ldur            x5, [x2, #0x13]
    // 0xbd0b38: add             x0, x5, #1
    // 0xbd0b3c: StoreField: r2->field_13 = r0
    //     0xbd0b3c: stur            x0, [x2, #0x13]
    // 0xbd0b40: LoadField: r0 = r4->field_13
    //     0xbd0b40: ldur            w0, [x4, #0x13]
    // 0xbd0b44: r1 = LoadInt32Instr(r0)
    //     0xbd0b44: sbfx            x1, x0, #1, #0x1f
    // 0xbd0b48: mov             x0, x1
    // 0xbd0b4c: mov             x1, x5
    // 0xbd0b50: cmp             x1, x0
    // 0xbd0b54: b.hs            #0xbd0d6c
    // 0xbd0b58: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd0b58: add             x0, x4, x5
    //     0xbd0b5c: strb            wzr, [x0, #0x17]
    // 0xbd0b60: LoadField: r4 = r3->field_7
    //     0xbd0b60: ldur            x4, [x3, #7]
    // 0xbd0b64: r0 = BoxInt64Instr(r4)
    //     0xbd0b64: sbfiz           x0, x4, #1, #0x1f
    //     0xbd0b68: cmp             x4, x0, asr #1
    //     0xbd0b6c: b.eq            #0xbd0b78
    //     0xbd0b70: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd0b74: stur            x4, [x0, #7]
    // 0xbd0b78: r16 = <int>
    //     0xbd0b78: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd0b7c: stp             x2, x16, [SP, #8]
    // 0xbd0b80: str             x0, [SP]
    // 0xbd0b84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0b84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0b88: r0 = write()
    //     0xbd0b88: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0b8c: ldur            x0, [fp, #-8]
    // 0xbd0b90: LoadField: r1 = r0->field_b
    //     0xbd0b90: ldur            w1, [x0, #0xb]
    // 0xbd0b94: DecompressPointer r1
    //     0xbd0b94: add             x1, x1, HEAP, lsl #32
    // 0xbd0b98: LoadField: r2 = r1->field_13
    //     0xbd0b98: ldur            w2, [x1, #0x13]
    // 0xbd0b9c: LoadField: r1 = r0->field_13
    //     0xbd0b9c: ldur            x1, [x0, #0x13]
    // 0xbd0ba0: r3 = LoadInt32Instr(r2)
    //     0xbd0ba0: sbfx            x3, x2, #1, #0x1f
    // 0xbd0ba4: sub             x2, x3, x1
    // 0xbd0ba8: cmp             x2, #1
    // 0xbd0bac: b.ge            #0xbd0bbc
    // 0xbd0bb0: mov             x1, x0
    // 0xbd0bb4: r2 = 1
    //     0xbd0bb4: movz            x2, #0x1
    // 0xbd0bb8: r0 = _increaseBufferSize()
    //     0xbd0bb8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0bbc: ldur            x2, [fp, #-8]
    // 0xbd0bc0: ldur            x3, [fp, #-0x10]
    // 0xbd0bc4: r4 = 1
    //     0xbd0bc4: movz            x4, #0x1
    // 0xbd0bc8: LoadField: r5 = r2->field_b
    //     0xbd0bc8: ldur            w5, [x2, #0xb]
    // 0xbd0bcc: DecompressPointer r5
    //     0xbd0bcc: add             x5, x5, HEAP, lsl #32
    // 0xbd0bd0: LoadField: r6 = r2->field_13
    //     0xbd0bd0: ldur            x6, [x2, #0x13]
    // 0xbd0bd4: add             x0, x6, #1
    // 0xbd0bd8: StoreField: r2->field_13 = r0
    //     0xbd0bd8: stur            x0, [x2, #0x13]
    // 0xbd0bdc: LoadField: r0 = r5->field_13
    //     0xbd0bdc: ldur            w0, [x5, #0x13]
    // 0xbd0be0: r1 = LoadInt32Instr(r0)
    //     0xbd0be0: sbfx            x1, x0, #1, #0x1f
    // 0xbd0be4: mov             x0, x1
    // 0xbd0be8: mov             x1, x6
    // 0xbd0bec: cmp             x1, x0
    // 0xbd0bf0: b.hs            #0xbd0d70
    // 0xbd0bf4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0bf4: add             x0, x5, x6
    //     0xbd0bf8: strb            w4, [x0, #0x17]
    // 0xbd0bfc: LoadField: r5 = r3->field_f
    //     0xbd0bfc: ldur            x5, [x3, #0xf]
    // 0xbd0c00: r0 = BoxInt64Instr(r5)
    //     0xbd0c00: sbfiz           x0, x5, #1, #0x1f
    //     0xbd0c04: cmp             x5, x0, asr #1
    //     0xbd0c08: b.eq            #0xbd0c14
    //     0xbd0c0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd0c10: stur            x5, [x0, #7]
    // 0xbd0c14: r16 = <int>
    //     0xbd0c14: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd0c18: stp             x2, x16, [SP, #8]
    // 0xbd0c1c: str             x0, [SP]
    // 0xbd0c20: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0c20: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0c24: r0 = write()
    //     0xbd0c24: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0c28: ldur            x0, [fp, #-8]
    // 0xbd0c2c: LoadField: r1 = r0->field_b
    //     0xbd0c2c: ldur            w1, [x0, #0xb]
    // 0xbd0c30: DecompressPointer r1
    //     0xbd0c30: add             x1, x1, HEAP, lsl #32
    // 0xbd0c34: LoadField: r2 = r1->field_13
    //     0xbd0c34: ldur            w2, [x1, #0x13]
    // 0xbd0c38: LoadField: r1 = r0->field_13
    //     0xbd0c38: ldur            x1, [x0, #0x13]
    // 0xbd0c3c: r3 = LoadInt32Instr(r2)
    //     0xbd0c3c: sbfx            x3, x2, #1, #0x1f
    // 0xbd0c40: sub             x2, x3, x1
    // 0xbd0c44: cmp             x2, #1
    // 0xbd0c48: b.ge            #0xbd0c58
    // 0xbd0c4c: mov             x1, x0
    // 0xbd0c50: r2 = 1
    //     0xbd0c50: movz            x2, #0x1
    // 0xbd0c54: r0 = _increaseBufferSize()
    //     0xbd0c54: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0c58: ldur            x2, [fp, #-8]
    // 0xbd0c5c: ldur            x3, [fp, #-0x10]
    // 0xbd0c60: r4 = 2
    //     0xbd0c60: movz            x4, #0x2
    // 0xbd0c64: LoadField: r5 = r2->field_b
    //     0xbd0c64: ldur            w5, [x2, #0xb]
    // 0xbd0c68: DecompressPointer r5
    //     0xbd0c68: add             x5, x5, HEAP, lsl #32
    // 0xbd0c6c: LoadField: r6 = r2->field_13
    //     0xbd0c6c: ldur            x6, [x2, #0x13]
    // 0xbd0c70: add             x0, x6, #1
    // 0xbd0c74: StoreField: r2->field_13 = r0
    //     0xbd0c74: stur            x0, [x2, #0x13]
    // 0xbd0c78: LoadField: r0 = r5->field_13
    //     0xbd0c78: ldur            w0, [x5, #0x13]
    // 0xbd0c7c: r1 = LoadInt32Instr(r0)
    //     0xbd0c7c: sbfx            x1, x0, #1, #0x1f
    // 0xbd0c80: mov             x0, x1
    // 0xbd0c84: mov             x1, x6
    // 0xbd0c88: cmp             x1, x0
    // 0xbd0c8c: b.hs            #0xbd0d74
    // 0xbd0c90: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0c90: add             x0, x5, x6
    //     0xbd0c94: strb            w4, [x0, #0x17]
    // 0xbd0c98: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd0c98: ldur            x4, [x3, #0x17]
    // 0xbd0c9c: r0 = BoxInt64Instr(r4)
    //     0xbd0c9c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd0ca0: cmp             x4, x0, asr #1
    //     0xbd0ca4: b.eq            #0xbd0cb0
    //     0xbd0ca8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd0cac: stur            x4, [x0, #7]
    // 0xbd0cb0: r16 = <int>
    //     0xbd0cb0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd0cb4: stp             x2, x16, [SP, #8]
    // 0xbd0cb8: str             x0, [SP]
    // 0xbd0cbc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0cbc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0cc0: r0 = write()
    //     0xbd0cc0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0cc4: ldur            x0, [fp, #-8]
    // 0xbd0cc8: LoadField: r1 = r0->field_b
    //     0xbd0cc8: ldur            w1, [x0, #0xb]
    // 0xbd0ccc: DecompressPointer r1
    //     0xbd0ccc: add             x1, x1, HEAP, lsl #32
    // 0xbd0cd0: LoadField: r2 = r1->field_13
    //     0xbd0cd0: ldur            w2, [x1, #0x13]
    // 0xbd0cd4: LoadField: r1 = r0->field_13
    //     0xbd0cd4: ldur            x1, [x0, #0x13]
    // 0xbd0cd8: r3 = LoadInt32Instr(r2)
    //     0xbd0cd8: sbfx            x3, x2, #1, #0x1f
    // 0xbd0cdc: sub             x2, x3, x1
    // 0xbd0ce0: cmp             x2, #1
    // 0xbd0ce4: b.ge            #0xbd0cf4
    // 0xbd0ce8: mov             x1, x0
    // 0xbd0cec: r2 = 1
    //     0xbd0cec: movz            x2, #0x1
    // 0xbd0cf0: r0 = _increaseBufferSize()
    //     0xbd0cf0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0cf4: ldur            x2, [fp, #-8]
    // 0xbd0cf8: ldur            x3, [fp, #-0x10]
    // 0xbd0cfc: r4 = 3
    //     0xbd0cfc: movz            x4, #0x3
    // 0xbd0d00: LoadField: r5 = r2->field_b
    //     0xbd0d00: ldur            w5, [x2, #0xb]
    // 0xbd0d04: DecompressPointer r5
    //     0xbd0d04: add             x5, x5, HEAP, lsl #32
    // 0xbd0d08: LoadField: r6 = r2->field_13
    //     0xbd0d08: ldur            x6, [x2, #0x13]
    // 0xbd0d0c: add             x0, x6, #1
    // 0xbd0d10: StoreField: r2->field_13 = r0
    //     0xbd0d10: stur            x0, [x2, #0x13]
    // 0xbd0d14: LoadField: r0 = r5->field_13
    //     0xbd0d14: ldur            w0, [x5, #0x13]
    // 0xbd0d18: r1 = LoadInt32Instr(r0)
    //     0xbd0d18: sbfx            x1, x0, #1, #0x1f
    // 0xbd0d1c: mov             x0, x1
    // 0xbd0d20: mov             x1, x6
    // 0xbd0d24: cmp             x1, x0
    // 0xbd0d28: b.hs            #0xbd0d78
    // 0xbd0d2c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0d2c: add             x0, x5, x6
    //     0xbd0d30: strb            w4, [x0, #0x17]
    // 0xbd0d34: LoadField: r0 = r3->field_1f
    //     0xbd0d34: ldur            w0, [x3, #0x1f]
    // 0xbd0d38: DecompressPointer r0
    //     0xbd0d38: add             x0, x0, HEAP, lsl #32
    // 0xbd0d3c: r16 = <String>
    //     0xbd0d3c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd0d40: stp             x2, x16, [SP, #8]
    // 0xbd0d44: str             x0, [SP]
    // 0xbd0d48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0d48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0d4c: r0 = write()
    //     0xbd0d4c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0d50: r0 = Null
    //     0xbd0d50: mov             x0, NULL
    // 0xbd0d54: LeaveFrame
    //     0xbd0d54: mov             SP, fp
    //     0xbd0d58: ldp             fp, lr, [SP], #0x10
    // 0xbd0d5c: ret
    //     0xbd0d5c: ret             
    // 0xbd0d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd0d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd0d64: b               #0xbd0a64
    // 0xbd0d68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0d68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0d6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0d6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0d70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0d70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0d74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0d74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd0d78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd0d78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0048, size: 0x24
    // 0xbf0048: r1 = 200
    //     0xbf0048: movz            x1, #0xc8
    // 0xbf004c: r16 = LoadInt32Instr(r1)
    //     0xbf004c: sbfx            x16, x1, #1, #0x1f
    // 0xbf0050: r17 = 11601
    //     0xbf0050: movz            x17, #0x2d51
    // 0xbf0054: mul             x0, x16, x17
    // 0xbf0058: umulh           x16, x16, x17
    // 0xbf005c: eor             x0, x0, x16
    // 0xbf0060: r0 = 0
    //     0xbf0060: eor             x0, x0, x0, lsr #32
    // 0xbf0064: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0068: ret
    //     0xbf0068: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd761ac, size: 0x9c
    // 0xd761ac: EnterFrame
    //     0xd761ac: stp             fp, lr, [SP, #-0x10]!
    //     0xd761b0: mov             fp, SP
    // 0xd761b4: AllocStack(0x10)
    //     0xd761b4: sub             SP, SP, #0x10
    // 0xd761b8: CheckStackOverflow
    //     0xd761b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd761bc: cmp             SP, x16
    //     0xd761c0: b.ls            #0xd76240
    // 0xd761c4: ldr             x0, [fp, #0x10]
    // 0xd761c8: cmp             w0, NULL
    // 0xd761cc: b.ne            #0xd761e0
    // 0xd761d0: r0 = false
    //     0xd761d0: add             x0, NULL, #0x30  ; false
    // 0xd761d4: LeaveFrame
    //     0xd761d4: mov             SP, fp
    //     0xd761d8: ldp             fp, lr, [SP], #0x10
    // 0xd761dc: ret
    //     0xd761dc: ret             
    // 0xd761e0: ldr             x1, [fp, #0x18]
    // 0xd761e4: cmp             w1, w0
    // 0xd761e8: b.ne            #0xd761f4
    // 0xd761ec: r0 = true
    //     0xd761ec: add             x0, NULL, #0x20  ; true
    // 0xd761f0: b               #0xd76234
    // 0xd761f4: r1 = 60
    //     0xd761f4: movz            x1, #0x3c
    // 0xd761f8: branchIfSmi(r0, 0xd76204)
    //     0xd761f8: tbz             w0, #0, #0xd76204
    // 0xd761fc: r1 = LoadClassIdInstr(r0)
    //     0xd761fc: ldur            x1, [x0, #-1]
    //     0xd76200: ubfx            x1, x1, #0xc, #0x14
    // 0xd76204: cmp             x1, #0x684
    // 0xd76208: b.ne            #0xd76230
    // 0xd7620c: r16 = HijriDateAdapter
    //     0xd7620c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b450] Type: HijriDateAdapter
    //     0xd76210: ldr             x16, [x16, #0x450]
    // 0xd76214: r30 = HijriDateAdapter
    //     0xd76214: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b450] Type: HijriDateAdapter
    //     0xd76218: ldr             lr, [lr, #0x450]
    // 0xd7621c: stp             lr, x16, [SP]
    // 0xd76220: r0 = ==()
    //     0xd76220: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76224: tbnz            w0, #4, #0xd76230
    // 0xd76228: r0 = true
    //     0xd76228: add             x0, NULL, #0x20  ; true
    // 0xd7622c: b               #0xd76234
    // 0xd76230: r0 = false
    //     0xd76230: add             x0, NULL, #0x30  ; false
    // 0xd76234: LeaveFrame
    //     0xd76234: mov             SP, fp
    //     0xd76238: ldp             fp, lr, [SP], #0x10
    // 0xd7623c: ret
    //     0xd7623c: ret             
    // 0xd76240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76244: b               #0xd761c4
  }
}

// class id: 5595, size: 0x24, field offset: 0x8
//   const constructor, 
class HijriDate extends Equatable {

  _ toMap(/* No info */) {
    // ** addr: 0x815350, size: 0xcc
    // 0x815350: EnterFrame
    //     0x815350: stp             fp, lr, [SP, #-0x10]!
    //     0x815354: mov             fp, SP
    // 0x815358: AllocStack(0x18)
    //     0x815358: sub             SP, SP, #0x18
    // 0x81535c: SetupParameters(HijriDate this /* r1 => r0, fp-0x8 */)
    //     0x81535c: mov             x0, x1
    //     0x815360: stur            x1, [fp, #-8]
    // 0x815364: CheckStackOverflow
    //     0x815364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x815368: cmp             SP, x16
    //     0x81536c: b.ls            #0x815414
    // 0x815370: r1 = Null
    //     0x815370: mov             x1, NULL
    // 0x815374: r2 = 12
    //     0x815374: movz            x2, #0xc
    // 0x815378: r0 = AllocateArray()
    //     0x815378: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81537c: mov             x2, x0
    // 0x815380: r16 = "day"
    //     0x815380: add             x16, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0x815384: ldr             x16, [x16, #0x340]
    // 0x815388: StoreField: r2->field_f = r16
    //     0x815388: stur            w16, [x2, #0xf]
    // 0x81538c: ldur            x3, [fp, #-8]
    // 0x815390: LoadField: r4 = r3->field_7
    //     0x815390: ldur            x4, [x3, #7]
    // 0x815394: r0 = BoxInt64Instr(r4)
    //     0x815394: sbfiz           x0, x4, #1, #0x1f
    //     0x815398: cmp             x4, x0, asr #1
    //     0x81539c: b.eq            #0x8153a8
    //     0x8153a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8153a4: stur            x4, [x0, #7]
    // 0x8153a8: StoreField: r2->field_13 = r0
    //     0x8153a8: stur            w0, [x2, #0x13]
    // 0x8153ac: r16 = "month"
    //     0x8153ac: add             x16, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x8153b0: ldr             x16, [x16, #0x328]
    // 0x8153b4: ArrayStore: r2[0] = r16  ; List_4
    //     0x8153b4: stur            w16, [x2, #0x17]
    // 0x8153b8: LoadField: r4 = r3->field_f
    //     0x8153b8: ldur            x4, [x3, #0xf]
    // 0x8153bc: r0 = BoxInt64Instr(r4)
    //     0x8153bc: sbfiz           x0, x4, #1, #0x1f
    //     0x8153c0: cmp             x4, x0, asr #1
    //     0x8153c4: b.eq            #0x8153d0
    //     0x8153c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8153cc: stur            x4, [x0, #7]
    // 0x8153d0: StoreField: r2->field_1b = r0
    //     0x8153d0: stur            w0, [x2, #0x1b]
    // 0x8153d4: r16 = "year"
    //     0x8153d4: add             x16, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x8153d8: ldr             x16, [x16, #0x310]
    // 0x8153dc: StoreField: r2->field_1f = r16
    //     0x8153dc: stur            w16, [x2, #0x1f]
    // 0x8153e0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x8153e0: ldur            x4, [x3, #0x17]
    // 0x8153e4: r0 = BoxInt64Instr(r4)
    //     0x8153e4: sbfiz           x0, x4, #1, #0x1f
    //     0x8153e8: cmp             x4, x0, asr #1
    //     0x8153ec: b.eq            #0x8153f8
    //     0x8153f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8153f4: stur            x4, [x0, #7]
    // 0x8153f8: StoreField: r2->field_23 = r0
    //     0x8153f8: stur            w0, [x2, #0x23]
    // 0x8153fc: r16 = <String, int>
    //     0x8153fc: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0x815400: stp             x2, x16, [SP]
    // 0x815404: r0 = Map._fromLiteral()
    //     0x815404: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x815408: LeaveFrame
    //     0x815408: mov             SP, fp
    //     0x81540c: ldp             fp, lr, [SP], #0x10
    // 0x815410: ret
    //     0x815410: ret             
    // 0x815414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x815414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x815418: b               #0x815370
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbce4, size: 0x5c
    // 0xbdbce4: EnterFrame
    //     0xbdbce4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbce8: mov             fp, SP
    // 0xbdbcec: AllocStack(0x10)
    //     0xbdbcec: sub             SP, SP, #0x10
    // 0xbdbcf0: r0 = 2
    //     0xbdbcf0: movz            x0, #0x2
    // 0xbdbcf4: LoadField: r3 = r1->field_1f
    //     0xbdbcf4: ldur            w3, [x1, #0x1f]
    // 0xbdbcf8: DecompressPointer r3
    //     0xbdbcf8: add             x3, x3, HEAP, lsl #32
    // 0xbdbcfc: mov             x2, x0
    // 0xbdbd00: stur            x3, [fp, #-8]
    // 0xbdbd04: r1 = Null
    //     0xbdbd04: mov             x1, NULL
    // 0xbdbd08: r0 = AllocateArray()
    //     0xbdbd08: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbd0c: mov             x2, x0
    // 0xbdbd10: ldur            x0, [fp, #-8]
    // 0xbdbd14: stur            x2, [fp, #-0x10]
    // 0xbdbd18: StoreField: r2->field_f = r0
    //     0xbdbd18: stur            w0, [x2, #0xf]
    // 0xbdbd1c: r1 = <Object?>
    //     0xbdbd1c: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbd20: r0 = AllocateGrowableArray()
    //     0xbdbd20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbd24: ldur            x1, [fp, #-0x10]
    // 0xbdbd28: StoreField: r0->field_f = r1
    //     0xbdbd28: stur            w1, [x0, #0xf]
    // 0xbdbd2c: r1 = 2
    //     0xbdbd2c: movz            x1, #0x2
    // 0xbdbd30: StoreField: r0->field_b = r1
    //     0xbdbd30: stur            w1, [x0, #0xb]
    // 0xbdbd34: LeaveFrame
    //     0xbdbd34: mov             SP, fp
    //     0xbdbd38: ldp             fp, lr, [SP], #0x10
    // 0xbdbd3c: ret
    //     0xbdbd3c: ret             
  }
  factory _ HijriDate.fromMap(/* No info */) {
    // ** addr: 0xdcaaa0, size: 0x234
    // 0xdcaaa0: EnterFrame
    //     0xdcaaa0: stp             fp, lr, [SP, #-0x10]!
    //     0xdcaaa4: mov             fp, SP
    // 0xdcaaa8: AllocStack(0x28)
    //     0xdcaaa8: sub             SP, SP, #0x28
    // 0xdcaaac: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xdcaaac: mov             x0, x2
    //     0xdcaab0: stur            x2, [fp, #-8]
    // 0xdcaab4: CheckStackOverflow
    //     0xdcaab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdcaab8: cmp             SP, x16
    //     0xdcaabc: b.ls            #0xdcaccc
    // 0xdcaac0: mov             x1, x0
    // 0xdcaac4: r2 = "day"
    //     0xdcaac4: add             x2, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0xdcaac8: ldr             x2, [x2, #0x340]
    // 0xdcaacc: r0 = _getValueOrData()
    //     0xdcaacc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdcaad0: ldur            x3, [fp, #-8]
    // 0xdcaad4: LoadField: r1 = r3->field_f
    //     0xdcaad4: ldur            w1, [x3, #0xf]
    // 0xdcaad8: DecompressPointer r1
    //     0xdcaad8: add             x1, x1, HEAP, lsl #32
    // 0xdcaadc: cmp             w1, w0
    // 0xdcaae0: b.ne            #0xdcaaec
    // 0xdcaae4: r4 = Null
    //     0xdcaae4: mov             x4, NULL
    // 0xdcaae8: b               #0xdcaaf0
    // 0xdcaaec: mov             x4, x0
    // 0xdcaaf0: mov             x0, x4
    // 0xdcaaf4: stur            x4, [fp, #-0x10]
    // 0xdcaaf8: r2 = Null
    //     0xdcaaf8: mov             x2, NULL
    // 0xdcaafc: r1 = Null
    //     0xdcaafc: mov             x1, NULL
    // 0xdcab00: branchIfSmi(r0, 0xdcab28)
    //     0xdcab00: tbz             w0, #0, #0xdcab28
    // 0xdcab04: r4 = LoadClassIdInstr(r0)
    //     0xdcab04: ldur            x4, [x0, #-1]
    //     0xdcab08: ubfx            x4, x4, #0xc, #0x14
    // 0xdcab0c: sub             x4, x4, #0x3c
    // 0xdcab10: cmp             x4, #1
    // 0xdcab14: b.ls            #0xdcab28
    // 0xdcab18: r8 = int
    //     0xdcab18: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdcab1c: r3 = Null
    //     0xdcab1c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12808] Null
    //     0xdcab20: ldr             x3, [x3, #0x808]
    // 0xdcab24: r0 = int()
    //     0xdcab24: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdcab28: ldur            x1, [fp, #-8]
    // 0xdcab2c: r2 = "month"
    //     0xdcab2c: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0xdcab30: ldr             x2, [x2, #0x328]
    // 0xdcab34: r0 = _getValueOrData()
    //     0xdcab34: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdcab38: ldur            x3, [fp, #-8]
    // 0xdcab3c: LoadField: r1 = r3->field_f
    //     0xdcab3c: ldur            w1, [x3, #0xf]
    // 0xdcab40: DecompressPointer r1
    //     0xdcab40: add             x1, x1, HEAP, lsl #32
    // 0xdcab44: cmp             w1, w0
    // 0xdcab48: b.ne            #0xdcab54
    // 0xdcab4c: r4 = Null
    //     0xdcab4c: mov             x4, NULL
    // 0xdcab50: b               #0xdcab58
    // 0xdcab54: mov             x4, x0
    // 0xdcab58: mov             x0, x4
    // 0xdcab5c: stur            x4, [fp, #-0x18]
    // 0xdcab60: r2 = Null
    //     0xdcab60: mov             x2, NULL
    // 0xdcab64: r1 = Null
    //     0xdcab64: mov             x1, NULL
    // 0xdcab68: branchIfSmi(r0, 0xdcab90)
    //     0xdcab68: tbz             w0, #0, #0xdcab90
    // 0xdcab6c: r4 = LoadClassIdInstr(r0)
    //     0xdcab6c: ldur            x4, [x0, #-1]
    //     0xdcab70: ubfx            x4, x4, #0xc, #0x14
    // 0xdcab74: sub             x4, x4, #0x3c
    // 0xdcab78: cmp             x4, #1
    // 0xdcab7c: b.ls            #0xdcab90
    // 0xdcab80: r8 = int
    //     0xdcab80: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdcab84: r3 = Null
    //     0xdcab84: add             x3, PP, #0x12, lsl #12  ; [pp+0x12818] Null
    //     0xdcab88: ldr             x3, [x3, #0x818]
    // 0xdcab8c: r0 = int()
    //     0xdcab8c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdcab90: ldur            x1, [fp, #-8]
    // 0xdcab94: r2 = "year"
    //     0xdcab94: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0xdcab98: ldr             x2, [x2, #0x310]
    // 0xdcab9c: r0 = _getValueOrData()
    //     0xdcab9c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdcaba0: ldur            x3, [fp, #-8]
    // 0xdcaba4: LoadField: r1 = r3->field_f
    //     0xdcaba4: ldur            w1, [x3, #0xf]
    // 0xdcaba8: DecompressPointer r1
    //     0xdcaba8: add             x1, x1, HEAP, lsl #32
    // 0xdcabac: cmp             w1, w0
    // 0xdcabb0: b.ne            #0xdcabbc
    // 0xdcabb4: r4 = Null
    //     0xdcabb4: mov             x4, NULL
    // 0xdcabb8: b               #0xdcabc0
    // 0xdcabbc: mov             x4, x0
    // 0xdcabc0: mov             x0, x4
    // 0xdcabc4: stur            x4, [fp, #-0x20]
    // 0xdcabc8: r2 = Null
    //     0xdcabc8: mov             x2, NULL
    // 0xdcabcc: r1 = Null
    //     0xdcabcc: mov             x1, NULL
    // 0xdcabd0: branchIfSmi(r0, 0xdcabf8)
    //     0xdcabd0: tbz             w0, #0, #0xdcabf8
    // 0xdcabd4: r4 = LoadClassIdInstr(r0)
    //     0xdcabd4: ldur            x4, [x0, #-1]
    //     0xdcabd8: ubfx            x4, x4, #0xc, #0x14
    // 0xdcabdc: sub             x4, x4, #0x3c
    // 0xdcabe0: cmp             x4, #1
    // 0xdcabe4: b.ls            #0xdcabf8
    // 0xdcabe8: r8 = int
    //     0xdcabe8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdcabec: r3 = Null
    //     0xdcabec: add             x3, PP, #0x12, lsl #12  ; [pp+0x12828] Null
    //     0xdcabf0: ldr             x3, [x3, #0x828]
    // 0xdcabf4: r0 = int()
    //     0xdcabf4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdcabf8: ldur            x1, [fp, #-8]
    // 0xdcabfc: r2 = "key"
    //     0xdcabfc: ldr             x2, [PP, #0xab8]  ; [pp+0xab8] "key"
    // 0xdcac00: r0 = _getValueOrData()
    //     0xdcac00: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdcac04: mov             x1, x0
    // 0xdcac08: ldur            x0, [fp, #-8]
    // 0xdcac0c: LoadField: r2 = r0->field_f
    //     0xdcac0c: ldur            w2, [x0, #0xf]
    // 0xdcac10: DecompressPointer r2
    //     0xdcac10: add             x2, x2, HEAP, lsl #32
    // 0xdcac14: cmp             w2, w1
    // 0xdcac18: b.ne            #0xdcac24
    // 0xdcac1c: r6 = Null
    //     0xdcac1c: mov             x6, NULL
    // 0xdcac20: b               #0xdcac28
    // 0xdcac24: mov             x6, x1
    // 0xdcac28: ldur            x5, [fp, #-0x10]
    // 0xdcac2c: ldur            x4, [fp, #-0x18]
    // 0xdcac30: ldur            x3, [fp, #-0x20]
    // 0xdcac34: mov             x0, x6
    // 0xdcac38: stur            x6, [fp, #-8]
    // 0xdcac3c: r2 = Null
    //     0xdcac3c: mov             x2, NULL
    // 0xdcac40: r1 = Null
    //     0xdcac40: mov             x1, NULL
    // 0xdcac44: r4 = 60
    //     0xdcac44: movz            x4, #0x3c
    // 0xdcac48: branchIfSmi(r0, 0xdcac54)
    //     0xdcac48: tbz             w0, #0, #0xdcac54
    // 0xdcac4c: r4 = LoadClassIdInstr(r0)
    //     0xdcac4c: ldur            x4, [x0, #-1]
    //     0xdcac50: ubfx            x4, x4, #0xc, #0x14
    // 0xdcac54: sub             x4, x4, #0x5e
    // 0xdcac58: cmp             x4, #1
    // 0xdcac5c: b.ls            #0xdcac70
    // 0xdcac60: r8 = String
    //     0xdcac60: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdcac64: r3 = Null
    //     0xdcac64: add             x3, PP, #0x12, lsl #12  ; [pp+0x12838] Null
    //     0xdcac68: ldr             x3, [x3, #0x838]
    // 0xdcac6c: r0 = String()
    //     0xdcac6c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdcac70: ldur            x0, [fp, #-0x10]
    // 0xdcac74: r1 = LoadInt32Instr(r0)
    //     0xdcac74: sbfx            x1, x0, #1, #0x1f
    //     0xdcac78: tbz             w0, #0, #0xdcac80
    //     0xdcac7c: ldur            x1, [x0, #7]
    // 0xdcac80: stur            x1, [fp, #-0x28]
    // 0xdcac84: r0 = HijriDate()
    //     0xdcac84: bl              #0x8ee738  ; AllocateHijriDateStub -> HijriDate (size=0x24)
    // 0xdcac88: ldur            x1, [fp, #-0x28]
    // 0xdcac8c: StoreField: r0->field_7 = r1
    //     0xdcac8c: stur            x1, [x0, #7]
    // 0xdcac90: ldur            x1, [fp, #-0x18]
    // 0xdcac94: r2 = LoadInt32Instr(r1)
    //     0xdcac94: sbfx            x2, x1, #1, #0x1f
    //     0xdcac98: tbz             w1, #0, #0xdcaca0
    //     0xdcac9c: ldur            x2, [x1, #7]
    // 0xdcaca0: StoreField: r0->field_f = r2
    //     0xdcaca0: stur            x2, [x0, #0xf]
    // 0xdcaca4: ldur            x1, [fp, #-0x20]
    // 0xdcaca8: r2 = LoadInt32Instr(r1)
    //     0xdcaca8: sbfx            x2, x1, #1, #0x1f
    //     0xdcacac: tbz             w1, #0, #0xdcacb4
    //     0xdcacb0: ldur            x2, [x1, #7]
    // 0xdcacb4: ArrayStore: r0[0] = r2  ; List_8
    //     0xdcacb4: stur            x2, [x0, #0x17]
    // 0xdcacb8: ldur            x1, [fp, #-8]
    // 0xdcacbc: StoreField: r0->field_1f = r1
    //     0xdcacbc: stur            w1, [x0, #0x1f]
    // 0xdcacc0: LeaveFrame
    //     0xdcacc0: mov             SP, fp
    //     0xdcacc4: ldp             fp, lr, [SP], #0x10
    // 0xdcacc8: ret
    //     0xdcacc8: ret             
    // 0xdcaccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdcaccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdcacd0: b               #0xdcaac0
  }
}
