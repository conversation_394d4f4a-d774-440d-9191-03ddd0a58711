// lib: , url: package:nuonline/app/data/models/campaign.dart

// class id: 1050009, size: 0x8
class :: {
}

// class id: 1156, size: 0x14, field offset: 0x8
class CampaignCategory extends Object {

  factory _ CampaignCategory.fromMap(/* No info */) {
    // ** addr: 0x7e7adc, size: 0x1bc
    // 0x7e7adc: EnterFrame
    //     0x7e7adc: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7ae0: mov             fp, SP
    // 0x7e7ae4: AllocStack(0x20)
    //     0x7e7ae4: sub             SP, SP, #0x20
    // 0x7e7ae8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e7ae8: mov             x3, x2
    //     0x7e7aec: stur            x2, [fp, #-8]
    // 0x7e7af0: CheckStackOverflow
    //     0x7e7af0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7af4: cmp             SP, x16
    //     0x7e7af8: b.ls            #0x7e7c90
    // 0x7e7afc: r0 = LoadClassIdInstr(r3)
    //     0x7e7afc: ldur            x0, [x3, #-1]
    //     0x7e7b00: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7b04: mov             x1, x3
    // 0x7e7b08: r2 = "id"
    //     0x7e7b08: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7e7b0c: ldr             x2, [x2, #0x740]
    // 0x7e7b10: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7b10: sub             lr, x0, #0x114
    //     0x7e7b14: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7b18: blr             lr
    // 0x7e7b1c: mov             x3, x0
    // 0x7e7b20: r2 = Null
    //     0x7e7b20: mov             x2, NULL
    // 0x7e7b24: r1 = Null
    //     0x7e7b24: mov             x1, NULL
    // 0x7e7b28: stur            x3, [fp, #-0x10]
    // 0x7e7b2c: branchIfSmi(r0, 0x7e7b54)
    //     0x7e7b2c: tbz             w0, #0, #0x7e7b54
    // 0x7e7b30: r4 = LoadClassIdInstr(r0)
    //     0x7e7b30: ldur            x4, [x0, #-1]
    //     0x7e7b34: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7b38: sub             x4, x4, #0x3c
    // 0x7e7b3c: cmp             x4, #1
    // 0x7e7b40: b.ls            #0x7e7b54
    // 0x7e7b44: r8 = int
    //     0x7e7b44: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7e7b48: r3 = Null
    //     0x7e7b48: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c98] Null
    //     0x7e7b4c: ldr             x3, [x3, #0xc98]
    // 0x7e7b50: r0 = int()
    //     0x7e7b50: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7e7b54: ldur            x3, [fp, #-8]
    // 0x7e7b58: r0 = LoadClassIdInstr(r3)
    //     0x7e7b58: ldur            x0, [x3, #-1]
    //     0x7e7b5c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7b60: mov             x1, x3
    // 0x7e7b64: r2 = "name"
    //     0x7e7b64: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7e7b68: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7b68: sub             lr, x0, #0x114
    //     0x7e7b6c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7b70: blr             lr
    // 0x7e7b74: mov             x3, x0
    // 0x7e7b78: r2 = Null
    //     0x7e7b78: mov             x2, NULL
    // 0x7e7b7c: r1 = Null
    //     0x7e7b7c: mov             x1, NULL
    // 0x7e7b80: stur            x3, [fp, #-0x18]
    // 0x7e7b84: r4 = 60
    //     0x7e7b84: movz            x4, #0x3c
    // 0x7e7b88: branchIfSmi(r0, 0x7e7b94)
    //     0x7e7b88: tbz             w0, #0, #0x7e7b94
    // 0x7e7b8c: r4 = LoadClassIdInstr(r0)
    //     0x7e7b8c: ldur            x4, [x0, #-1]
    //     0x7e7b90: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7b94: sub             x4, x4, #0x5e
    // 0x7e7b98: cmp             x4, #1
    // 0x7e7b9c: b.ls            #0x7e7bb0
    // 0x7e7ba0: r8 = String
    //     0x7e7ba0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e7ba4: r3 = Null
    //     0x7e7ba4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ca8] Null
    //     0x7e7ba8: ldr             x3, [x3, #0xca8]
    // 0x7e7bac: r0 = String()
    //     0x7e7bac: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e7bb0: ldur            x3, [fp, #-8]
    // 0x7e7bb4: r0 = LoadClassIdInstr(r3)
    //     0x7e7bb4: ldur            x0, [x3, #-1]
    //     0x7e7bb8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7bbc: mov             x1, x3
    // 0x7e7bc0: r2 = "prefix"
    //     0x7e7bc0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x7e7bc4: ldr             x2, [x2, #0x970]
    // 0x7e7bc8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7bc8: sub             lr, x0, #0x114
    //     0x7e7bcc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7bd0: blr             lr
    // 0x7e7bd4: r2 = Null
    //     0x7e7bd4: mov             x2, NULL
    // 0x7e7bd8: r1 = Null
    //     0x7e7bd8: mov             x1, NULL
    // 0x7e7bdc: r4 = 60
    //     0x7e7bdc: movz            x4, #0x3c
    // 0x7e7be0: branchIfSmi(r0, 0x7e7bec)
    //     0x7e7be0: tbz             w0, #0, #0x7e7bec
    // 0x7e7be4: r4 = LoadClassIdInstr(r0)
    //     0x7e7be4: ldur            x4, [x0, #-1]
    //     0x7e7be8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7bec: sub             x4, x4, #0x5e
    // 0x7e7bf0: cmp             x4, #1
    // 0x7e7bf4: b.ls            #0x7e7c08
    // 0x7e7bf8: r8 = String?
    //     0x7e7bf8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e7bfc: r3 = Null
    //     0x7e7bfc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32cb8] Null
    //     0x7e7c00: ldr             x3, [x3, #0xcb8]
    // 0x7e7c04: r0 = String?()
    //     0x7e7c04: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7c08: ldur            x1, [fp, #-8]
    // 0x7e7c0c: r0 = LoadClassIdInstr(r1)
    //     0x7e7c0c: ldur            x0, [x1, #-1]
    //     0x7e7c10: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7c14: r2 = "slug"
    //     0x7e7c14: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x7e7c18: ldr             x2, [x2, #0x9a8]
    // 0x7e7c1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7c1c: sub             lr, x0, #0x114
    //     0x7e7c20: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7c24: blr             lr
    // 0x7e7c28: r2 = Null
    //     0x7e7c28: mov             x2, NULL
    // 0x7e7c2c: r1 = Null
    //     0x7e7c2c: mov             x1, NULL
    // 0x7e7c30: r4 = 60
    //     0x7e7c30: movz            x4, #0x3c
    // 0x7e7c34: branchIfSmi(r0, 0x7e7c40)
    //     0x7e7c34: tbz             w0, #0, #0x7e7c40
    // 0x7e7c38: r4 = LoadClassIdInstr(r0)
    //     0x7e7c38: ldur            x4, [x0, #-1]
    //     0x7e7c3c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7c40: sub             x4, x4, #0x5e
    // 0x7e7c44: cmp             x4, #1
    // 0x7e7c48: b.ls            #0x7e7c5c
    // 0x7e7c4c: r8 = String?
    //     0x7e7c4c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e7c50: r3 = Null
    //     0x7e7c50: add             x3, PP, #0x32, lsl #12  ; [pp+0x32cc8] Null
    //     0x7e7c54: ldr             x3, [x3, #0xcc8]
    // 0x7e7c58: r0 = String?()
    //     0x7e7c58: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7c5c: ldur            x0, [fp, #-0x10]
    // 0x7e7c60: r1 = LoadInt32Instr(r0)
    //     0x7e7c60: sbfx            x1, x0, #1, #0x1f
    //     0x7e7c64: tbz             w0, #0, #0x7e7c6c
    //     0x7e7c68: ldur            x1, [x0, #7]
    // 0x7e7c6c: stur            x1, [fp, #-0x20]
    // 0x7e7c70: r0 = CampaignCategory()
    //     0x7e7c70: bl              #0x7e7c98  ; AllocateCampaignCategoryStub -> CampaignCategory (size=0x14)
    // 0x7e7c74: ldur            x1, [fp, #-0x20]
    // 0x7e7c78: StoreField: r0->field_7 = r1
    //     0x7e7c78: stur            x1, [x0, #7]
    // 0x7e7c7c: ldur            x1, [fp, #-0x18]
    // 0x7e7c80: StoreField: r0->field_f = r1
    //     0x7e7c80: stur            w1, [x0, #0xf]
    // 0x7e7c84: LeaveFrame
    //     0x7e7c84: mov             SP, fp
    //     0x7e7c88: ldp             fp, lr, [SP], #0x10
    // 0x7e7c8c: ret
    //     0x7e7c8c: ret             
    // 0x7e7c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7c94: b               #0x7e7afc
  }
}

// class id: 1157, size: 0x30, field offset: 0x8
class CampaignOrganization extends Object {

  factory _ CampaignOrganization.fromMap(/* No info */) {
    // ** addr: 0x7e7504, size: 0x5cc
    // 0x7e7504: EnterFrame
    //     0x7e7504: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7508: mov             fp, SP
    // 0x7e750c: AllocStack(0x60)
    //     0x7e750c: sub             SP, SP, #0x60
    // 0x7e7510: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e7510: mov             x3, x2
    //     0x7e7514: stur            x2, [fp, #-8]
    // 0x7e7518: CheckStackOverflow
    //     0x7e7518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e751c: cmp             SP, x16
    //     0x7e7520: b.ls            #0x7e7ac8
    // 0x7e7524: r0 = LoadClassIdInstr(r3)
    //     0x7e7524: ldur            x0, [x3, #-1]
    //     0x7e7528: ubfx            x0, x0, #0xc, #0x14
    // 0x7e752c: mov             x1, x3
    // 0x7e7530: r2 = "id"
    //     0x7e7530: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7e7534: ldr             x2, [x2, #0x740]
    // 0x7e7538: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7538: sub             lr, x0, #0x114
    //     0x7e753c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7540: blr             lr
    // 0x7e7544: mov             x3, x0
    // 0x7e7548: r2 = Null
    //     0x7e7548: mov             x2, NULL
    // 0x7e754c: r1 = Null
    //     0x7e754c: mov             x1, NULL
    // 0x7e7550: stur            x3, [fp, #-0x10]
    // 0x7e7554: branchIfSmi(r0, 0x7e757c)
    //     0x7e7554: tbz             w0, #0, #0x7e757c
    // 0x7e7558: r4 = LoadClassIdInstr(r0)
    //     0x7e7558: ldur            x4, [x0, #-1]
    //     0x7e755c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7560: sub             x4, x4, #0x3c
    // 0x7e7564: cmp             x4, #1
    // 0x7e7568: b.ls            #0x7e757c
    // 0x7e756c: r8 = int
    //     0x7e756c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7e7570: r3 = Null
    //     0x7e7570: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b70] Null
    //     0x7e7574: ldr             x3, [x3, #0xb70]
    // 0x7e7578: r0 = int()
    //     0x7e7578: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7e757c: ldur            x3, [fp, #-8]
    // 0x7e7580: r0 = LoadClassIdInstr(r3)
    //     0x7e7580: ldur            x0, [x3, #-1]
    //     0x7e7584: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7588: mov             x1, x3
    // 0x7e758c: r2 = "name"
    //     0x7e758c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7e7590: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7590: sub             lr, x0, #0x114
    //     0x7e7594: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7598: blr             lr
    // 0x7e759c: mov             x3, x0
    // 0x7e75a0: r2 = Null
    //     0x7e75a0: mov             x2, NULL
    // 0x7e75a4: r1 = Null
    //     0x7e75a4: mov             x1, NULL
    // 0x7e75a8: stur            x3, [fp, #-0x18]
    // 0x7e75ac: r4 = 60
    //     0x7e75ac: movz            x4, #0x3c
    // 0x7e75b0: branchIfSmi(r0, 0x7e75bc)
    //     0x7e75b0: tbz             w0, #0, #0x7e75bc
    // 0x7e75b4: r4 = LoadClassIdInstr(r0)
    //     0x7e75b4: ldur            x4, [x0, #-1]
    //     0x7e75b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e75bc: sub             x4, x4, #0x5e
    // 0x7e75c0: cmp             x4, #1
    // 0x7e75c4: b.ls            #0x7e75d8
    // 0x7e75c8: r8 = String
    //     0x7e75c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e75cc: r3 = Null
    //     0x7e75cc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b80] Null
    //     0x7e75d0: ldr             x3, [x3, #0xb80]
    // 0x7e75d4: r0 = String()
    //     0x7e75d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e75d8: ldur            x3, [fp, #-8]
    // 0x7e75dc: r0 = LoadClassIdInstr(r3)
    //     0x7e75dc: ldur            x0, [x3, #-1]
    //     0x7e75e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e75e4: mov             x1, x3
    // 0x7e75e8: r2 = "logo_url"
    //     0x7e75e8: add             x2, PP, #0x32, lsl #12  ; [pp+0x32b90] "logo_url"
    //     0x7e75ec: ldr             x2, [x2, #0xb90]
    // 0x7e75f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e75f0: sub             lr, x0, #0x114
    //     0x7e75f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e75f8: blr             lr
    // 0x7e75fc: mov             x3, x0
    // 0x7e7600: r2 = Null
    //     0x7e7600: mov             x2, NULL
    // 0x7e7604: r1 = Null
    //     0x7e7604: mov             x1, NULL
    // 0x7e7608: stur            x3, [fp, #-0x20]
    // 0x7e760c: r4 = 60
    //     0x7e760c: movz            x4, #0x3c
    // 0x7e7610: branchIfSmi(r0, 0x7e761c)
    //     0x7e7610: tbz             w0, #0, #0x7e761c
    // 0x7e7614: r4 = LoadClassIdInstr(r0)
    //     0x7e7614: ldur            x4, [x0, #-1]
    //     0x7e7618: ubfx            x4, x4, #0xc, #0x14
    // 0x7e761c: sub             x4, x4, #0x5e
    // 0x7e7620: cmp             x4, #1
    // 0x7e7624: b.ls            #0x7e7638
    // 0x7e7628: r8 = String
    //     0x7e7628: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e762c: r3 = Null
    //     0x7e762c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b98] Null
    //     0x7e7630: ldr             x3, [x3, #0xb98]
    // 0x7e7634: r0 = String()
    //     0x7e7634: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e7638: ldur            x3, [fp, #-8]
    // 0x7e763c: r0 = LoadClassIdInstr(r3)
    //     0x7e763c: ldur            x0, [x3, #-1]
    //     0x7e7640: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7644: mov             x1, x3
    // 0x7e7648: r2 = "short_description"
    //     0x7e7648: add             x2, PP, #0x32, lsl #12  ; [pp+0x329f0] "short_description"
    //     0x7e764c: ldr             x2, [x2, #0x9f0]
    // 0x7e7650: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7650: sub             lr, x0, #0x114
    //     0x7e7654: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7658: blr             lr
    // 0x7e765c: r2 = Null
    //     0x7e765c: mov             x2, NULL
    // 0x7e7660: r1 = Null
    //     0x7e7660: mov             x1, NULL
    // 0x7e7664: r4 = 60
    //     0x7e7664: movz            x4, #0x3c
    // 0x7e7668: branchIfSmi(r0, 0x7e7674)
    //     0x7e7668: tbz             w0, #0, #0x7e7674
    // 0x7e766c: r4 = LoadClassIdInstr(r0)
    //     0x7e766c: ldur            x4, [x0, #-1]
    //     0x7e7670: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7674: sub             x4, x4, #0x5e
    // 0x7e7678: cmp             x4, #1
    // 0x7e767c: b.ls            #0x7e7690
    // 0x7e7680: r8 = String?
    //     0x7e7680: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e7684: r3 = Null
    //     0x7e7684: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ba8] Null
    //     0x7e7688: ldr             x3, [x3, #0xba8]
    // 0x7e768c: r0 = String?()
    //     0x7e768c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7690: ldur            x3, [fp, #-8]
    // 0x7e7694: r0 = LoadClassIdInstr(r3)
    //     0x7e7694: ldur            x0, [x3, #-1]
    //     0x7e7698: ubfx            x0, x0, #0xc, #0x14
    // 0x7e769c: mov             x1, x3
    // 0x7e76a0: r2 = "description"
    //     0x7e76a0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x7e76a4: ldr             x2, [x2, #0xd28]
    // 0x7e76a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e76a8: sub             lr, x0, #0x114
    //     0x7e76ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7e76b0: blr             lr
    // 0x7e76b4: r2 = Null
    //     0x7e76b4: mov             x2, NULL
    // 0x7e76b8: r1 = Null
    //     0x7e76b8: mov             x1, NULL
    // 0x7e76bc: r4 = 60
    //     0x7e76bc: movz            x4, #0x3c
    // 0x7e76c0: branchIfSmi(r0, 0x7e76cc)
    //     0x7e76c0: tbz             w0, #0, #0x7e76cc
    // 0x7e76c4: r4 = LoadClassIdInstr(r0)
    //     0x7e76c4: ldur            x4, [x0, #-1]
    //     0x7e76c8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e76cc: sub             x4, x4, #0x5e
    // 0x7e76d0: cmp             x4, #1
    // 0x7e76d4: b.ls            #0x7e76e8
    // 0x7e76d8: r8 = String?
    //     0x7e76d8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e76dc: r3 = Null
    //     0x7e76dc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32bb8] Null
    //     0x7e76e0: ldr             x3, [x3, #0xbb8]
    // 0x7e76e4: r0 = String?()
    //     0x7e76e4: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e76e8: ldur            x3, [fp, #-8]
    // 0x7e76ec: r0 = LoadClassIdInstr(r3)
    //     0x7e76ec: ldur            x0, [x3, #-1]
    //     0x7e76f0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e76f4: mov             x1, x3
    // 0x7e76f8: r2 = "address"
    //     0x7e76f8: add             x2, PP, #0xc, lsl #12  ; [pp+0xc828] "address"
    //     0x7e76fc: ldr             x2, [x2, #0x828]
    // 0x7e7700: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7700: sub             lr, x0, #0x114
    //     0x7e7704: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7708: blr             lr
    // 0x7e770c: mov             x3, x0
    // 0x7e7710: r2 = Null
    //     0x7e7710: mov             x2, NULL
    // 0x7e7714: r1 = Null
    //     0x7e7714: mov             x1, NULL
    // 0x7e7718: stur            x3, [fp, #-0x28]
    // 0x7e771c: r4 = 60
    //     0x7e771c: movz            x4, #0x3c
    // 0x7e7720: branchIfSmi(r0, 0x7e772c)
    //     0x7e7720: tbz             w0, #0, #0x7e772c
    // 0x7e7724: r4 = LoadClassIdInstr(r0)
    //     0x7e7724: ldur            x4, [x0, #-1]
    //     0x7e7728: ubfx            x4, x4, #0xc, #0x14
    // 0x7e772c: sub             x4, x4, #0x5e
    // 0x7e7730: cmp             x4, #1
    // 0x7e7734: b.ls            #0x7e7748
    // 0x7e7738: r8 = String?
    //     0x7e7738: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e773c: r3 = Null
    //     0x7e773c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32bc8] Null
    //     0x7e7740: ldr             x3, [x3, #0xbc8]
    // 0x7e7744: r0 = String?()
    //     0x7e7744: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7748: ldur            x3, [fp, #-8]
    // 0x7e774c: r0 = LoadClassIdInstr(r3)
    //     0x7e774c: ldur            x0, [x3, #-1]
    //     0x7e7750: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7754: mov             x1, x3
    // 0x7e7758: r2 = "province_id"
    //     0x7e7758: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x7e775c: ldr             x2, [x2, #0x100]
    // 0x7e7760: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7760: sub             lr, x0, #0x114
    //     0x7e7764: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7768: blr             lr
    // 0x7e776c: mov             x3, x0
    // 0x7e7770: r2 = Null
    //     0x7e7770: mov             x2, NULL
    // 0x7e7774: r1 = Null
    //     0x7e7774: mov             x1, NULL
    // 0x7e7778: stur            x3, [fp, #-0x30]
    // 0x7e777c: branchIfSmi(r0, 0x7e77a4)
    //     0x7e777c: tbz             w0, #0, #0x7e77a4
    // 0x7e7780: r4 = LoadClassIdInstr(r0)
    //     0x7e7780: ldur            x4, [x0, #-1]
    //     0x7e7784: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7788: sub             x4, x4, #0x3c
    // 0x7e778c: cmp             x4, #1
    // 0x7e7790: b.ls            #0x7e77a4
    // 0x7e7794: r8 = int?
    //     0x7e7794: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e7798: r3 = Null
    //     0x7e7798: add             x3, PP, #0x32, lsl #12  ; [pp+0x32bd8] Null
    //     0x7e779c: ldr             x3, [x3, #0xbd8]
    // 0x7e77a0: r0 = int?()
    //     0x7e77a0: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e77a4: ldur            x3, [fp, #-8]
    // 0x7e77a8: r0 = LoadClassIdInstr(r3)
    //     0x7e77a8: ldur            x0, [x3, #-1]
    //     0x7e77ac: ubfx            x0, x0, #0xc, #0x14
    // 0x7e77b0: mov             x1, x3
    // 0x7e77b4: r2 = "regency_id"
    //     0x7e77b4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x7e77b8: ldr             x2, [x2, #0x118]
    // 0x7e77bc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e77bc: sub             lr, x0, #0x114
    //     0x7e77c0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e77c4: blr             lr
    // 0x7e77c8: mov             x3, x0
    // 0x7e77cc: r2 = Null
    //     0x7e77cc: mov             x2, NULL
    // 0x7e77d0: r1 = Null
    //     0x7e77d0: mov             x1, NULL
    // 0x7e77d4: stur            x3, [fp, #-0x38]
    // 0x7e77d8: branchIfSmi(r0, 0x7e7800)
    //     0x7e77d8: tbz             w0, #0, #0x7e7800
    // 0x7e77dc: r4 = LoadClassIdInstr(r0)
    //     0x7e77dc: ldur            x4, [x0, #-1]
    //     0x7e77e0: ubfx            x4, x4, #0xc, #0x14
    // 0x7e77e4: sub             x4, x4, #0x3c
    // 0x7e77e8: cmp             x4, #1
    // 0x7e77ec: b.ls            #0x7e7800
    // 0x7e77f0: r8 = int?
    //     0x7e77f0: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e77f4: r3 = Null
    //     0x7e77f4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32be8] Null
    //     0x7e77f8: ldr             x3, [x3, #0xbe8]
    // 0x7e77fc: r0 = int?()
    //     0x7e77fc: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e7800: ldur            x3, [fp, #-8]
    // 0x7e7804: r0 = LoadClassIdInstr(r3)
    //     0x7e7804: ldur            x0, [x3, #-1]
    //     0x7e7808: ubfx            x0, x0, #0xc, #0x14
    // 0x7e780c: mov             x1, x3
    // 0x7e7810: r2 = "district_id"
    //     0x7e7810: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x7e7814: ldr             x2, [x2, #0xfe8]
    // 0x7e7818: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7818: sub             lr, x0, #0x114
    //     0x7e781c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7820: blr             lr
    // 0x7e7824: mov             x3, x0
    // 0x7e7828: r2 = Null
    //     0x7e7828: mov             x2, NULL
    // 0x7e782c: r1 = Null
    //     0x7e782c: mov             x1, NULL
    // 0x7e7830: stur            x3, [fp, #-0x40]
    // 0x7e7834: branchIfSmi(r0, 0x7e785c)
    //     0x7e7834: tbz             w0, #0, #0x7e785c
    // 0x7e7838: r4 = LoadClassIdInstr(r0)
    //     0x7e7838: ldur            x4, [x0, #-1]
    //     0x7e783c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7840: sub             x4, x4, #0x3c
    // 0x7e7844: cmp             x4, #1
    // 0x7e7848: b.ls            #0x7e785c
    // 0x7e784c: r8 = int?
    //     0x7e784c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e7850: r3 = Null
    //     0x7e7850: add             x3, PP, #0x32, lsl #12  ; [pp+0x32bf8] Null
    //     0x7e7854: ldr             x3, [x3, #0xbf8]
    // 0x7e7858: r0 = int?()
    //     0x7e7858: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e785c: ldur            x3, [fp, #-8]
    // 0x7e7860: r0 = LoadClassIdInstr(r3)
    //     0x7e7860: ldur            x0, [x3, #-1]
    //     0x7e7864: ubfx            x0, x0, #0xc, #0x14
    // 0x7e7868: mov             x1, x3
    // 0x7e786c: r2 = "locality_id"
    //     0x7e786c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe70] "locality_id"
    //     0x7e7870: ldr             x2, [x2, #0xe70]
    // 0x7e7874: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7874: sub             lr, x0, #0x114
    //     0x7e7878: ldr             lr, [x21, lr, lsl #3]
    //     0x7e787c: blr             lr
    // 0x7e7880: r2 = Null
    //     0x7e7880: mov             x2, NULL
    // 0x7e7884: r1 = Null
    //     0x7e7884: mov             x1, NULL
    // 0x7e7888: branchIfSmi(r0, 0x7e78b0)
    //     0x7e7888: tbz             w0, #0, #0x7e78b0
    // 0x7e788c: r4 = LoadClassIdInstr(r0)
    //     0x7e788c: ldur            x4, [x0, #-1]
    //     0x7e7890: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7894: sub             x4, x4, #0x3c
    // 0x7e7898: cmp             x4, #1
    // 0x7e789c: b.ls            #0x7e78b0
    // 0x7e78a0: r8 = int?
    //     0x7e78a0: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e78a4: r3 = Null
    //     0x7e78a4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c08] Null
    //     0x7e78a8: ldr             x3, [x3, #0xc08]
    // 0x7e78ac: r0 = int?()
    //     0x7e78ac: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e78b0: ldur            x3, [fp, #-8]
    // 0x7e78b4: r0 = LoadClassIdInstr(r3)
    //     0x7e78b4: ldur            x0, [x3, #-1]
    //     0x7e78b8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e78bc: mov             x1, x3
    // 0x7e78c0: r2 = "level"
    //     0x7e78c0: add             x2, PP, #0x32, lsl #12  ; [pp+0x32c18] "level"
    //     0x7e78c4: ldr             x2, [x2, #0xc18]
    // 0x7e78c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e78c8: sub             lr, x0, #0x114
    //     0x7e78cc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e78d0: blr             lr
    // 0x7e78d4: mov             x3, x0
    // 0x7e78d8: r2 = Null
    //     0x7e78d8: mov             x2, NULL
    // 0x7e78dc: r1 = Null
    //     0x7e78dc: mov             x1, NULL
    // 0x7e78e0: stur            x3, [fp, #-0x48]
    // 0x7e78e4: r4 = 60
    //     0x7e78e4: movz            x4, #0x3c
    // 0x7e78e8: branchIfSmi(r0, 0x7e78f4)
    //     0x7e78e8: tbz             w0, #0, #0x7e78f4
    // 0x7e78ec: r4 = LoadClassIdInstr(r0)
    //     0x7e78ec: ldur            x4, [x0, #-1]
    //     0x7e78f0: ubfx            x4, x4, #0xc, #0x14
    // 0x7e78f4: sub             x4, x4, #0x5e
    // 0x7e78f8: cmp             x4, #1
    // 0x7e78fc: b.ls            #0x7e7910
    // 0x7e7900: r8 = String?
    //     0x7e7900: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e7904: r3 = Null
    //     0x7e7904: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c20] Null
    //     0x7e7908: ldr             x3, [x3, #0xc20]
    // 0x7e790c: r0 = String?()
    //     0x7e790c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7910: r16 = "NATIONAL"
    //     0x7e7910: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c30] "NATIONAL"
    //     0x7e7914: ldr             x16, [x16, #0xc30]
    // 0x7e7918: ldur            lr, [fp, #-0x48]
    // 0x7e791c: stp             lr, x16, [SP]
    // 0x7e7920: r0 = ==()
    //     0x7e7920: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e7924: tbnz            w0, #4, #0x7e7934
    // 0x7e7928: r3 = Instance_CampaignOrganizationLevel
    //     0x7e7928: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!CampaignOrganizationLevel@e30761
    //     0x7e792c: ldr             x3, [x3, #0xc38]
    // 0x7e7930: b               #0x7e79c8
    // 0x7e7934: r16 = "PROVINCE"
    //     0x7e7934: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] "PROVINCE"
    //     0x7e7938: ldr             x16, [x16, #0xc40]
    // 0x7e793c: ldur            lr, [fp, #-0x48]
    // 0x7e7940: stp             lr, x16, [SP]
    // 0x7e7944: r0 = ==()
    //     0x7e7944: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e7948: tbnz            w0, #4, #0x7e7958
    // 0x7e794c: r3 = Instance_CampaignOrganizationLevel
    //     0x7e794c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c48] Obj!CampaignOrganizationLevel@e30741
    //     0x7e7950: ldr             x3, [x3, #0xc48]
    // 0x7e7954: b               #0x7e79c8
    // 0x7e7958: r16 = "REGENCY"
    //     0x7e7958: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c50] "REGENCY"
    //     0x7e795c: ldr             x16, [x16, #0xc50]
    // 0x7e7960: ldur            lr, [fp, #-0x48]
    // 0x7e7964: stp             lr, x16, [SP]
    // 0x7e7968: r0 = ==()
    //     0x7e7968: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e796c: tbnz            w0, #4, #0x7e797c
    // 0x7e7970: r3 = Instance_CampaignOrganizationLevel
    //     0x7e7970: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c58] Obj!CampaignOrganizationLevel@e30721
    //     0x7e7974: ldr             x3, [x3, #0xc58]
    // 0x7e7978: b               #0x7e79c8
    // 0x7e797c: r16 = "DISTRICT"
    //     0x7e797c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c60] "DISTRICT"
    //     0x7e7980: ldr             x16, [x16, #0xc60]
    // 0x7e7984: ldur            lr, [fp, #-0x48]
    // 0x7e7988: stp             lr, x16, [SP]
    // 0x7e798c: r0 = ==()
    //     0x7e798c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e7990: tbnz            w0, #4, #0x7e79a0
    // 0x7e7994: r3 = Instance_CampaignOrganizationLevel
    //     0x7e7994: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CampaignOrganizationLevel@e30701
    //     0x7e7998: ldr             x3, [x3, #0xc68]
    // 0x7e799c: b               #0x7e79c8
    // 0x7e79a0: r16 = "LOCALITY"
    //     0x7e79a0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] "LOCALITY"
    //     0x7e79a4: ldr             x16, [x16, #0xc70]
    // 0x7e79a8: ldur            lr, [fp, #-0x48]
    // 0x7e79ac: stp             lr, x16, [SP]
    // 0x7e79b0: r0 = ==()
    //     0x7e79b0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e79b4: tbnz            w0, #4, #0x7e79c4
    // 0x7e79b8: r3 = Instance_CampaignOrganizationLevel
    //     0x7e79b8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c78] Obj!CampaignOrganizationLevel@e306e1
    //     0x7e79bc: ldr             x3, [x3, #0xc78]
    // 0x7e79c0: b               #0x7e79c8
    // 0x7e79c4: r3 = Null
    //     0x7e79c4: mov             x3, NULL
    // 0x7e79c8: ldur            x1, [fp, #-8]
    // 0x7e79cc: stur            x3, [fp, #-0x48]
    // 0x7e79d0: r0 = LoadClassIdInstr(r1)
    //     0x7e79d0: ldur            x0, [x1, #-1]
    //     0x7e79d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e79d8: r2 = "donor_origin_required"
    //     0x7e79d8: add             x2, PP, #0x32, lsl #12  ; [pp+0x32c80] "donor_origin_required"
    //     0x7e79dc: ldr             x2, [x2, #0xc80]
    // 0x7e79e0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e79e0: sub             lr, x0, #0x114
    //     0x7e79e4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e79e8: blr             lr
    // 0x7e79ec: mov             x3, x0
    // 0x7e79f0: r2 = Null
    //     0x7e79f0: mov             x2, NULL
    // 0x7e79f4: r1 = Null
    //     0x7e79f4: mov             x1, NULL
    // 0x7e79f8: stur            x3, [fp, #-8]
    // 0x7e79fc: r4 = 60
    //     0x7e79fc: movz            x4, #0x3c
    // 0x7e7a00: branchIfSmi(r0, 0x7e7a0c)
    //     0x7e7a00: tbz             w0, #0, #0x7e7a0c
    // 0x7e7a04: r4 = LoadClassIdInstr(r0)
    //     0x7e7a04: ldur            x4, [x0, #-1]
    //     0x7e7a08: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7a0c: cmp             x4, #0x3f
    // 0x7e7a10: b.eq            #0x7e7a24
    // 0x7e7a14: r8 = bool?
    //     0x7e7a14: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7e7a18: r3 = Null
    //     0x7e7a18: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c88] Null
    //     0x7e7a1c: ldr             x3, [x3, #0xc88]
    // 0x7e7a20: r0 = bool?()
    //     0x7e7a20: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7e7a24: ldur            x0, [fp, #-8]
    // 0x7e7a28: cmp             w0, NULL
    // 0x7e7a2c: b.ne            #0x7e7a38
    // 0x7e7a30: r8 = false
    //     0x7e7a30: add             x8, NULL, #0x30  ; false
    // 0x7e7a34: b               #0x7e7a3c
    // 0x7e7a38: mov             x8, x0
    // 0x7e7a3c: ldur            x7, [fp, #-0x10]
    // 0x7e7a40: ldur            x6, [fp, #-0x18]
    // 0x7e7a44: ldur            x5, [fp, #-0x20]
    // 0x7e7a48: ldur            x4, [fp, #-0x28]
    // 0x7e7a4c: ldur            x3, [fp, #-0x30]
    // 0x7e7a50: ldur            x2, [fp, #-0x38]
    // 0x7e7a54: ldur            x1, [fp, #-0x40]
    // 0x7e7a58: ldur            x0, [fp, #-0x48]
    // 0x7e7a5c: stur            x8, [fp, #-8]
    // 0x7e7a60: r9 = LoadInt32Instr(r7)
    //     0x7e7a60: sbfx            x9, x7, #1, #0x1f
    //     0x7e7a64: tbz             w7, #0, #0x7e7a6c
    //     0x7e7a68: ldur            x9, [x7, #7]
    // 0x7e7a6c: stur            x9, [fp, #-0x50]
    // 0x7e7a70: r0 = CampaignOrganization()
    //     0x7e7a70: bl              #0x7e7ad0  ; AllocateCampaignOrganizationStub -> CampaignOrganization (size=0x30)
    // 0x7e7a74: ldur            x1, [fp, #-0x50]
    // 0x7e7a78: StoreField: r0->field_7 = r1
    //     0x7e7a78: stur            x1, [x0, #7]
    // 0x7e7a7c: ldur            x1, [fp, #-0x18]
    // 0x7e7a80: StoreField: r0->field_f = r1
    //     0x7e7a80: stur            w1, [x0, #0xf]
    // 0x7e7a84: ldur            x1, [fp, #-0x20]
    // 0x7e7a88: StoreField: r0->field_13 = r1
    //     0x7e7a88: stur            w1, [x0, #0x13]
    // 0x7e7a8c: ldur            x1, [fp, #-0x28]
    // 0x7e7a90: ArrayStore: r0[0] = r1  ; List_4
    //     0x7e7a90: stur            w1, [x0, #0x17]
    // 0x7e7a94: ldur            x1, [fp, #-0x30]
    // 0x7e7a98: StoreField: r0->field_1b = r1
    //     0x7e7a98: stur            w1, [x0, #0x1b]
    // 0x7e7a9c: ldur            x1, [fp, #-0x38]
    // 0x7e7aa0: StoreField: r0->field_1f = r1
    //     0x7e7aa0: stur            w1, [x0, #0x1f]
    // 0x7e7aa4: ldur            x1, [fp, #-0x40]
    // 0x7e7aa8: StoreField: r0->field_23 = r1
    //     0x7e7aa8: stur            w1, [x0, #0x23]
    // 0x7e7aac: ldur            x1, [fp, #-0x48]
    // 0x7e7ab0: StoreField: r0->field_27 = r1
    //     0x7e7ab0: stur            w1, [x0, #0x27]
    // 0x7e7ab4: ldur            x1, [fp, #-8]
    // 0x7e7ab8: StoreField: r0->field_2b = r1
    //     0x7e7ab8: stur            w1, [x0, #0x2b]
    // 0x7e7abc: LeaveFrame
    //     0x7e7abc: mov             SP, fp
    //     0x7e7ac0: ldp             fp, lr, [SP], #0x10
    // 0x7e7ac4: ret
    //     0x7e7ac4: ret             
    // 0x7e7ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7ac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7acc: b               #0x7e7524
  }
}

// class id: 1158, size: 0x44, field offset: 0x8
class Campaign extends Object {

  factory _ Campaign.fromMap(/* No info */) {
    // ** addr: 0x7e65c8, size: 0x8d8
    // 0x7e65c8: EnterFrame
    //     0x7e65c8: stp             fp, lr, [SP, #-0x10]!
    //     0x7e65cc: mov             fp, SP
    // 0x7e65d0: AllocStack(0x78)
    //     0x7e65d0: sub             SP, SP, #0x78
    // 0x7e65d4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e65d4: mov             x3, x2
    //     0x7e65d8: stur            x2, [fp, #-8]
    // 0x7e65dc: CheckStackOverflow
    //     0x7e65dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e65e0: cmp             SP, x16
    //     0x7e65e4: b.ls            #0x7e6e98
    // 0x7e65e8: r0 = LoadClassIdInstr(r3)
    //     0x7e65e8: ldur            x0, [x3, #-1]
    //     0x7e65ec: ubfx            x0, x0, #0xc, #0x14
    // 0x7e65f0: mov             x1, x3
    // 0x7e65f4: r2 = "id"
    //     0x7e65f4: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7e65f8: ldr             x2, [x2, #0x740]
    // 0x7e65fc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e65fc: sub             lr, x0, #0x114
    //     0x7e6600: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6604: blr             lr
    // 0x7e6608: mov             x3, x0
    // 0x7e660c: r2 = Null
    //     0x7e660c: mov             x2, NULL
    // 0x7e6610: r1 = Null
    //     0x7e6610: mov             x1, NULL
    // 0x7e6614: stur            x3, [fp, #-0x10]
    // 0x7e6618: branchIfSmi(r0, 0x7e6640)
    //     0x7e6618: tbz             w0, #0, #0x7e6640
    // 0x7e661c: r4 = LoadClassIdInstr(r0)
    //     0x7e661c: ldur            x4, [x0, #-1]
    //     0x7e6620: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6624: sub             x4, x4, #0x3c
    // 0x7e6628: cmp             x4, #1
    // 0x7e662c: b.ls            #0x7e6640
    // 0x7e6630: r8 = int
    //     0x7e6630: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7e6634: r3 = Null
    //     0x7e6634: add             x3, PP, #0x32, lsl #12  ; [pp+0x329d0] Null
    //     0x7e6638: ldr             x3, [x3, #0x9d0]
    // 0x7e663c: r0 = int()
    //     0x7e663c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7e6640: ldur            x3, [fp, #-8]
    // 0x7e6644: r0 = LoadClassIdInstr(r3)
    //     0x7e6644: ldur            x0, [x3, #-1]
    //     0x7e6648: ubfx            x0, x0, #0xc, #0x14
    // 0x7e664c: mov             x1, x3
    // 0x7e6650: r2 = "name"
    //     0x7e6650: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7e6654: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6654: sub             lr, x0, #0x114
    //     0x7e6658: ldr             lr, [x21, lr, lsl #3]
    //     0x7e665c: blr             lr
    // 0x7e6660: mov             x3, x0
    // 0x7e6664: r2 = Null
    //     0x7e6664: mov             x2, NULL
    // 0x7e6668: r1 = Null
    //     0x7e6668: mov             x1, NULL
    // 0x7e666c: stur            x3, [fp, #-0x18]
    // 0x7e6670: r4 = 60
    //     0x7e6670: movz            x4, #0x3c
    // 0x7e6674: branchIfSmi(r0, 0x7e6680)
    //     0x7e6674: tbz             w0, #0, #0x7e6680
    // 0x7e6678: r4 = LoadClassIdInstr(r0)
    //     0x7e6678: ldur            x4, [x0, #-1]
    //     0x7e667c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6680: sub             x4, x4, #0x5e
    // 0x7e6684: cmp             x4, #1
    // 0x7e6688: b.ls            #0x7e669c
    // 0x7e668c: r8 = String
    //     0x7e668c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e6690: r3 = Null
    //     0x7e6690: add             x3, PP, #0x32, lsl #12  ; [pp+0x329e0] Null
    //     0x7e6694: ldr             x3, [x3, #0x9e0]
    // 0x7e6698: r0 = String()
    //     0x7e6698: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e669c: ldur            x3, [fp, #-8]
    // 0x7e66a0: r0 = LoadClassIdInstr(r3)
    //     0x7e66a0: ldur            x0, [x3, #-1]
    //     0x7e66a4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e66a8: mov             x1, x3
    // 0x7e66ac: r2 = "short_description"
    //     0x7e66ac: add             x2, PP, #0x32, lsl #12  ; [pp+0x329f0] "short_description"
    //     0x7e66b0: ldr             x2, [x2, #0x9f0]
    // 0x7e66b4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e66b4: sub             lr, x0, #0x114
    //     0x7e66b8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e66bc: blr             lr
    // 0x7e66c0: mov             x3, x0
    // 0x7e66c4: r2 = Null
    //     0x7e66c4: mov             x2, NULL
    // 0x7e66c8: r1 = Null
    //     0x7e66c8: mov             x1, NULL
    // 0x7e66cc: stur            x3, [fp, #-0x20]
    // 0x7e66d0: r4 = 60
    //     0x7e66d0: movz            x4, #0x3c
    // 0x7e66d4: branchIfSmi(r0, 0x7e66e0)
    //     0x7e66d4: tbz             w0, #0, #0x7e66e0
    // 0x7e66d8: r4 = LoadClassIdInstr(r0)
    //     0x7e66d8: ldur            x4, [x0, #-1]
    //     0x7e66dc: ubfx            x4, x4, #0xc, #0x14
    // 0x7e66e0: sub             x4, x4, #0x5e
    // 0x7e66e4: cmp             x4, #1
    // 0x7e66e8: b.ls            #0x7e66fc
    // 0x7e66ec: r8 = String?
    //     0x7e66ec: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e66f0: r3 = Null
    //     0x7e66f0: add             x3, PP, #0x32, lsl #12  ; [pp+0x329f8] Null
    //     0x7e66f4: ldr             x3, [x3, #0x9f8]
    // 0x7e66f8: r0 = String?()
    //     0x7e66f8: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e66fc: ldur            x3, [fp, #-8]
    // 0x7e6700: r0 = LoadClassIdInstr(r3)
    //     0x7e6700: ldur            x0, [x3, #-1]
    //     0x7e6704: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6708: mov             x1, x3
    // 0x7e670c: r2 = "description"
    //     0x7e670c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x7e6710: ldr             x2, [x2, #0xd28]
    // 0x7e6714: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6714: sub             lr, x0, #0x114
    //     0x7e6718: ldr             lr, [x21, lr, lsl #3]
    //     0x7e671c: blr             lr
    // 0x7e6720: mov             x3, x0
    // 0x7e6724: r2 = Null
    //     0x7e6724: mov             x2, NULL
    // 0x7e6728: r1 = Null
    //     0x7e6728: mov             x1, NULL
    // 0x7e672c: stur            x3, [fp, #-0x28]
    // 0x7e6730: r4 = 60
    //     0x7e6730: movz            x4, #0x3c
    // 0x7e6734: branchIfSmi(r0, 0x7e6740)
    //     0x7e6734: tbz             w0, #0, #0x7e6740
    // 0x7e6738: r4 = LoadClassIdInstr(r0)
    //     0x7e6738: ldur            x4, [x0, #-1]
    //     0x7e673c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6740: sub             x4, x4, #0x5e
    // 0x7e6744: cmp             x4, #1
    // 0x7e6748: b.ls            #0x7e675c
    // 0x7e674c: r8 = String?
    //     0x7e674c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e6750: r3 = Null
    //     0x7e6750: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a08] Null
    //     0x7e6754: ldr             x3, [x3, #0xa08]
    // 0x7e6758: r0 = String?()
    //     0x7e6758: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e675c: ldur            x0, [fp, #-0x28]
    // 0x7e6760: cmp             w0, NULL
    // 0x7e6764: b.ne            #0x7e6770
    // 0x7e6768: r4 = ""
    //     0x7e6768: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e676c: b               #0x7e6774
    // 0x7e6770: mov             x4, x0
    // 0x7e6774: ldur            x3, [fp, #-8]
    // 0x7e6778: stur            x4, [fp, #-0x28]
    // 0x7e677c: r0 = LoadClassIdInstr(r3)
    //     0x7e677c: ldur            x0, [x3, #-1]
    //     0x7e6780: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6784: mov             x1, x3
    // 0x7e6788: r2 = "thumbnail_url"
    //     0x7e6788: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dbd0] "thumbnail_url"
    //     0x7e678c: ldr             x2, [x2, #0xbd0]
    // 0x7e6790: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6790: sub             lr, x0, #0x114
    //     0x7e6794: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6798: blr             lr
    // 0x7e679c: mov             x3, x0
    // 0x7e67a0: r2 = Null
    //     0x7e67a0: mov             x2, NULL
    // 0x7e67a4: r1 = Null
    //     0x7e67a4: mov             x1, NULL
    // 0x7e67a8: stur            x3, [fp, #-0x30]
    // 0x7e67ac: r4 = 60
    //     0x7e67ac: movz            x4, #0x3c
    // 0x7e67b0: branchIfSmi(r0, 0x7e67bc)
    //     0x7e67b0: tbz             w0, #0, #0x7e67bc
    // 0x7e67b4: r4 = LoadClassIdInstr(r0)
    //     0x7e67b4: ldur            x4, [x0, #-1]
    //     0x7e67b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e67bc: sub             x4, x4, #0x5e
    // 0x7e67c0: cmp             x4, #1
    // 0x7e67c4: b.ls            #0x7e67d8
    // 0x7e67c8: r8 = String
    //     0x7e67c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e67cc: r3 = Null
    //     0x7e67cc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a18] Null
    //     0x7e67d0: ldr             x3, [x3, #0xa18]
    // 0x7e67d4: r0 = String()
    //     0x7e67d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e67d8: ldur            x3, [fp, #-8]
    // 0x7e67dc: r0 = LoadClassIdInstr(r3)
    //     0x7e67dc: ldur            x0, [x3, #-1]
    //     0x7e67e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e67e4: mov             x1, x3
    // 0x7e67e8: r2 = "start_date"
    //     0x7e67e8: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a28] "start_date"
    //     0x7e67ec: ldr             x2, [x2, #0xa28]
    // 0x7e67f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e67f0: sub             lr, x0, #0x114
    //     0x7e67f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e67f8: blr             lr
    // 0x7e67fc: mov             x3, x0
    // 0x7e6800: r2 = Null
    //     0x7e6800: mov             x2, NULL
    // 0x7e6804: r1 = Null
    //     0x7e6804: mov             x1, NULL
    // 0x7e6808: stur            x3, [fp, #-0x38]
    // 0x7e680c: r4 = 60
    //     0x7e680c: movz            x4, #0x3c
    // 0x7e6810: branchIfSmi(r0, 0x7e681c)
    //     0x7e6810: tbz             w0, #0, #0x7e681c
    // 0x7e6814: r4 = LoadClassIdInstr(r0)
    //     0x7e6814: ldur            x4, [x0, #-1]
    //     0x7e6818: ubfx            x4, x4, #0xc, #0x14
    // 0x7e681c: sub             x4, x4, #0x5e
    // 0x7e6820: cmp             x4, #1
    // 0x7e6824: b.ls            #0x7e6838
    // 0x7e6828: r8 = String?
    //     0x7e6828: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e682c: r3 = Null
    //     0x7e682c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a30] Null
    //     0x7e6830: ldr             x3, [x3, #0xa30]
    // 0x7e6834: r0 = String?()
    //     0x7e6834: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e6838: ldur            x0, [fp, #-0x38]
    // 0x7e683c: cmp             w0, NULL
    // 0x7e6840: b.ne            #0x7e6880
    // 0x7e6844: r0 = DateTime()
    //     0x7e6844: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x7e6848: mov             x1, x0
    // 0x7e684c: r0 = false
    //     0x7e684c: add             x0, NULL, #0x30  ; false
    // 0x7e6850: stur            x1, [fp, #-0x40]
    // 0x7e6854: StoreField: r1->field_13 = r0
    //     0x7e6854: stur            w0, [x1, #0x13]
    // 0x7e6858: r0 = _getCurrentMicros()
    //     0x7e6858: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x7e685c: r1 = LoadInt32Instr(r0)
    //     0x7e685c: sbfx            x1, x0, #1, #0x1f
    //     0x7e6860: tbz             w0, #0, #0x7e6868
    //     0x7e6864: ldur            x1, [x0, #7]
    // 0x7e6868: ldur            x0, [fp, #-0x40]
    // 0x7e686c: StoreField: r0->field_7 = r1
    //     0x7e686c: stur            x1, [x0, #7]
    // 0x7e6870: str             x0, [SP]
    // 0x7e6874: r0 = toString()
    //     0x7e6874: bl              #0xbffa50  ; [dart:core] DateTime::toString
    // 0x7e6878: mov             x1, x0
    // 0x7e687c: b               #0x7e6884
    // 0x7e6880: mov             x1, x0
    // 0x7e6884: ldur            x0, [fp, #-8]
    // 0x7e6888: r0 = parse()
    //     0x7e6888: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7e688c: ldur            x3, [fp, #-8]
    // 0x7e6890: r0 = LoadClassIdInstr(r3)
    //     0x7e6890: ldur            x0, [x3, #-1]
    //     0x7e6894: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6898: mov             x1, x3
    // 0x7e689c: r2 = "end_date"
    //     0x7e689c: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a40] "end_date"
    //     0x7e68a0: ldr             x2, [x2, #0xa40]
    // 0x7e68a4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e68a4: sub             lr, x0, #0x114
    //     0x7e68a8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e68ac: blr             lr
    // 0x7e68b0: mov             x3, x0
    // 0x7e68b4: r2 = Null
    //     0x7e68b4: mov             x2, NULL
    // 0x7e68b8: r1 = Null
    //     0x7e68b8: mov             x1, NULL
    // 0x7e68bc: stur            x3, [fp, #-0x38]
    // 0x7e68c0: r4 = 60
    //     0x7e68c0: movz            x4, #0x3c
    // 0x7e68c4: branchIfSmi(r0, 0x7e68d0)
    //     0x7e68c4: tbz             w0, #0, #0x7e68d0
    // 0x7e68c8: r4 = LoadClassIdInstr(r0)
    //     0x7e68c8: ldur            x4, [x0, #-1]
    //     0x7e68cc: ubfx            x4, x4, #0xc, #0x14
    // 0x7e68d0: sub             x4, x4, #0x5e
    // 0x7e68d4: cmp             x4, #1
    // 0x7e68d8: b.ls            #0x7e68ec
    // 0x7e68dc: r8 = String?
    //     0x7e68dc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e68e0: r3 = Null
    //     0x7e68e0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a48] Null
    //     0x7e68e4: ldr             x3, [x3, #0xa48]
    // 0x7e68e8: r0 = String?()
    //     0x7e68e8: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e68ec: ldur            x0, [fp, #-0x38]
    // 0x7e68f0: cmp             w0, NULL
    // 0x7e68f4: b.ne            #0x7e6934
    // 0x7e68f8: r0 = DateTime()
    //     0x7e68f8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x7e68fc: mov             x1, x0
    // 0x7e6900: r0 = false
    //     0x7e6900: add             x0, NULL, #0x30  ; false
    // 0x7e6904: stur            x1, [fp, #-0x40]
    // 0x7e6908: StoreField: r1->field_13 = r0
    //     0x7e6908: stur            w0, [x1, #0x13]
    // 0x7e690c: r0 = _getCurrentMicros()
    //     0x7e690c: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x7e6910: r1 = LoadInt32Instr(r0)
    //     0x7e6910: sbfx            x1, x0, #1, #0x1f
    //     0x7e6914: tbz             w0, #0, #0x7e691c
    //     0x7e6918: ldur            x1, [x0, #7]
    // 0x7e691c: ldur            x0, [fp, #-0x40]
    // 0x7e6920: StoreField: r0->field_7 = r1
    //     0x7e6920: stur            x1, [x0, #7]
    // 0x7e6924: str             x0, [SP]
    // 0x7e6928: r0 = toString()
    //     0x7e6928: bl              #0xbffa50  ; [dart:core] DateTime::toString
    // 0x7e692c: mov             x1, x0
    // 0x7e6930: b               #0x7e6938
    // 0x7e6934: mov             x1, x0
    // 0x7e6938: ldur            x0, [fp, #-8]
    // 0x7e693c: r0 = parse()
    //     0x7e693c: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7e6940: mov             x4, x0
    // 0x7e6944: ldur            x3, [fp, #-8]
    // 0x7e6948: stur            x4, [fp, #-0x38]
    // 0x7e694c: r0 = LoadClassIdInstr(r3)
    //     0x7e694c: ldur            x0, [x3, #-1]
    //     0x7e6950: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6954: mov             x1, x3
    // 0x7e6958: r2 = "target"
    //     0x7e6958: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9a8] "target"
    //     0x7e695c: ldr             x2, [x2, #0x9a8]
    // 0x7e6960: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6960: sub             lr, x0, #0x114
    //     0x7e6964: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6968: blr             lr
    // 0x7e696c: mov             x3, x0
    // 0x7e6970: r2 = Null
    //     0x7e6970: mov             x2, NULL
    // 0x7e6974: r1 = Null
    //     0x7e6974: mov             x1, NULL
    // 0x7e6978: stur            x3, [fp, #-0x40]
    // 0x7e697c: branchIfSmi(r0, 0x7e69a8)
    //     0x7e697c: tbz             w0, #0, #0x7e69a8
    // 0x7e6980: r4 = LoadClassIdInstr(r0)
    //     0x7e6980: ldur            x4, [x0, #-1]
    //     0x7e6984: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6988: sub             x4, x4, #0x3c
    // 0x7e698c: cmp             x4, #2
    // 0x7e6990: b.ls            #0x7e69a8
    // 0x7e6994: r8 = num?
    //     0x7e6994: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7e6998: ldr             x8, [x8, #0x4f0]
    // 0x7e699c: r3 = Null
    //     0x7e699c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a58] Null
    //     0x7e69a0: ldr             x3, [x3, #0xa58]
    // 0x7e69a4: r0 = DefaultNullableTypeTest()
    //     0x7e69a4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7e69a8: ldur            x0, [fp, #-0x40]
    // 0x7e69ac: cmp             w0, NULL
    // 0x7e69b0: b.ne            #0x7e69bc
    // 0x7e69b4: r4 = 0
    //     0x7e69b4: movz            x4, #0
    // 0x7e69b8: b               #0x7e69c0
    // 0x7e69bc: mov             x4, x0
    // 0x7e69c0: ldur            x3, [fp, #-8]
    // 0x7e69c4: stur            x4, [fp, #-0x40]
    // 0x7e69c8: r0 = LoadClassIdInstr(r3)
    //     0x7e69c8: ldur            x0, [x3, #-1]
    //     0x7e69cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7e69d0: mov             x1, x3
    // 0x7e69d4: r2 = "total_amount"
    //     0x7e69d4: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a68] "total_amount"
    //     0x7e69d8: ldr             x2, [x2, #0xa68]
    // 0x7e69dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e69dc: sub             lr, x0, #0x114
    //     0x7e69e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e69e4: blr             lr
    // 0x7e69e8: mov             x3, x0
    // 0x7e69ec: r2 = Null
    //     0x7e69ec: mov             x2, NULL
    // 0x7e69f0: r1 = Null
    //     0x7e69f0: mov             x1, NULL
    // 0x7e69f4: stur            x3, [fp, #-0x48]
    // 0x7e69f8: branchIfSmi(r0, 0x7e6a24)
    //     0x7e69f8: tbz             w0, #0, #0x7e6a24
    // 0x7e69fc: r4 = LoadClassIdInstr(r0)
    //     0x7e69fc: ldur            x4, [x0, #-1]
    //     0x7e6a00: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6a04: sub             x4, x4, #0x3c
    // 0x7e6a08: cmp             x4, #2
    // 0x7e6a0c: b.ls            #0x7e6a24
    // 0x7e6a10: r8 = num?
    //     0x7e6a10: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7e6a14: ldr             x8, [x8, #0x4f0]
    // 0x7e6a18: r3 = Null
    //     0x7e6a18: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a70] Null
    //     0x7e6a1c: ldr             x3, [x3, #0xa70]
    // 0x7e6a20: r0 = DefaultNullableTypeTest()
    //     0x7e6a20: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7e6a24: ldur            x0, [fp, #-0x48]
    // 0x7e6a28: cmp             w0, NULL
    // 0x7e6a2c: b.ne            #0x7e6a38
    // 0x7e6a30: r4 = 0
    //     0x7e6a30: movz            x4, #0
    // 0x7e6a34: b               #0x7e6a3c
    // 0x7e6a38: mov             x4, x0
    // 0x7e6a3c: ldur            x3, [fp, #-8]
    // 0x7e6a40: stur            x4, [fp, #-0x48]
    // 0x7e6a44: r0 = LoadClassIdInstr(r3)
    //     0x7e6a44: ldur            x0, [x3, #-1]
    //     0x7e6a48: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6a4c: mov             x1, x3
    // 0x7e6a50: r2 = "total_donation"
    //     0x7e6a50: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a80] "total_donation"
    //     0x7e6a54: ldr             x2, [x2, #0xa80]
    // 0x7e6a58: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6a58: sub             lr, x0, #0x114
    //     0x7e6a5c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6a60: blr             lr
    // 0x7e6a64: mov             x3, x0
    // 0x7e6a68: r2 = Null
    //     0x7e6a68: mov             x2, NULL
    // 0x7e6a6c: r1 = Null
    //     0x7e6a6c: mov             x1, NULL
    // 0x7e6a70: stur            x3, [fp, #-0x50]
    // 0x7e6a74: branchIfSmi(r0, 0x7e6a9c)
    //     0x7e6a74: tbz             w0, #0, #0x7e6a9c
    // 0x7e6a78: r4 = LoadClassIdInstr(r0)
    //     0x7e6a78: ldur            x4, [x0, #-1]
    //     0x7e6a7c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6a80: sub             x4, x4, #0x3c
    // 0x7e6a84: cmp             x4, #1
    // 0x7e6a88: b.ls            #0x7e6a9c
    // 0x7e6a8c: r8 = int?
    //     0x7e6a8c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e6a90: r3 = Null
    //     0x7e6a90: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a88] Null
    //     0x7e6a94: ldr             x3, [x3, #0xa88]
    // 0x7e6a98: r0 = int?()
    //     0x7e6a98: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e6a9c: ldur            x0, [fp, #-0x50]
    // 0x7e6aa0: cmp             w0, NULL
    // 0x7e6aa4: b.ne            #0x7e6ab0
    // 0x7e6aa8: r4 = 0
    //     0x7e6aa8: movz            x4, #0
    // 0x7e6aac: b               #0x7e6ac0
    // 0x7e6ab0: r1 = LoadInt32Instr(r0)
    //     0x7e6ab0: sbfx            x1, x0, #1, #0x1f
    //     0x7e6ab4: tbz             w0, #0, #0x7e6abc
    //     0x7e6ab8: ldur            x1, [x0, #7]
    // 0x7e6abc: mov             x4, x1
    // 0x7e6ac0: ldur            x3, [fp, #-8]
    // 0x7e6ac4: stur            x4, [fp, #-0x58]
    // 0x7e6ac8: r0 = LoadClassIdInstr(r3)
    //     0x7e6ac8: ldur            x0, [x3, #-1]
    //     0x7e6acc: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6ad0: mov             x1, x3
    // 0x7e6ad4: r2 = "category"
    //     0x7e6ad4: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x7e6ad8: ldr             x2, [x2, #0x960]
    // 0x7e6adc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6adc: sub             lr, x0, #0x114
    //     0x7e6ae0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6ae4: blr             lr
    // 0x7e6ae8: mov             x3, x0
    // 0x7e6aec: r2 = Null
    //     0x7e6aec: mov             x2, NULL
    // 0x7e6af0: r1 = Null
    //     0x7e6af0: mov             x1, NULL
    // 0x7e6af4: stur            x3, [fp, #-0x50]
    // 0x7e6af8: r8 = Map<String, dynamic>
    //     0x7e6af8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e6afc: r3 = Null
    //     0x7e6afc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32a98] Null
    //     0x7e6b00: ldr             x3, [x3, #0xa98]
    // 0x7e6b04: r0 = Map<String, dynamic>()
    //     0x7e6b04: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e6b08: ldur            x2, [fp, #-0x50]
    // 0x7e6b0c: r1 = Null
    //     0x7e6b0c: mov             x1, NULL
    // 0x7e6b10: r0 = CampaignCategory.fromMap()
    //     0x7e6b10: bl              #0x7e7adc  ; [package:nuonline/app/data/models/campaign.dart] CampaignCategory::CampaignCategory.fromMap
    // 0x7e6b14: mov             x4, x0
    // 0x7e6b18: ldur            x3, [fp, #-8]
    // 0x7e6b1c: stur            x4, [fp, #-0x50]
    // 0x7e6b20: r0 = LoadClassIdInstr(r3)
    //     0x7e6b20: ldur            x0, [x3, #-1]
    //     0x7e6b24: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6b28: mov             x1, x3
    // 0x7e6b2c: r2 = "sub_category"
    //     0x7e6b2c: add             x2, PP, #0x32, lsl #12  ; [pp+0x32aa8] "sub_category"
    //     0x7e6b30: ldr             x2, [x2, #0xaa8]
    // 0x7e6b34: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6b34: sub             lr, x0, #0x114
    //     0x7e6b38: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6b3c: blr             lr
    // 0x7e6b40: r2 = Null
    //     0x7e6b40: mov             x2, NULL
    // 0x7e6b44: r1 = Null
    //     0x7e6b44: mov             x1, NULL
    // 0x7e6b48: cmp             w0, NULL
    // 0x7e6b4c: b.eq            #0x7e6be4
    // 0x7e6b50: branchIfSmi(r0, 0x7e6be4)
    //     0x7e6b50: tbz             w0, #0, #0x7e6be4
    // 0x7e6b54: r3 = LoadClassIdInstr(r0)
    //     0x7e6b54: ldur            x3, [x0, #-1]
    //     0x7e6b58: ubfx            x3, x3, #0xc, #0x14
    // 0x7e6b5c: r17 = 6717
    //     0x7e6b5c: movz            x17, #0x1a3d
    // 0x7e6b60: cmp             x3, x17
    // 0x7e6b64: b.eq            #0x7e6bec
    // 0x7e6b68: r4 = LoadClassIdInstr(r0)
    //     0x7e6b68: ldur            x4, [x0, #-1]
    //     0x7e6b6c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6b70: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x7e6b74: ldr             x3, [x3, #0x18]
    // 0x7e6b78: ldr             x3, [x3, x4, lsl #3]
    // 0x7e6b7c: LoadField: r3 = r3->field_2b
    //     0x7e6b7c: ldur            w3, [x3, #0x2b]
    // 0x7e6b80: DecompressPointer r3
    //     0x7e6b80: add             x3, x3, HEAP, lsl #32
    // 0x7e6b84: cmp             w3, NULL
    // 0x7e6b88: b.eq            #0x7e6be4
    // 0x7e6b8c: LoadField: r3 = r3->field_f
    //     0x7e6b8c: ldur            w3, [x3, #0xf]
    // 0x7e6b90: lsr             x3, x3, #3
    // 0x7e6b94: r17 = 6717
    //     0x7e6b94: movz            x17, #0x1a3d
    // 0x7e6b98: cmp             x3, x17
    // 0x7e6b9c: b.eq            #0x7e6bec
    // 0x7e6ba0: r3 = SubtypeTestCache
    //     0x7e6ba0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ab0] SubtypeTestCache
    //     0x7e6ba4: ldr             x3, [x3, #0xab0]
    // 0x7e6ba8: r30 = Subtype1TestCacheStub
    //     0x7e6ba8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x7e6bac: LoadField: r30 = r30->field_7
    //     0x7e6bac: ldur            lr, [lr, #7]
    // 0x7e6bb0: blr             lr
    // 0x7e6bb4: cmp             w7, NULL
    // 0x7e6bb8: b.eq            #0x7e6bc4
    // 0x7e6bbc: tbnz            w7, #4, #0x7e6be4
    // 0x7e6bc0: b               #0x7e6bec
    // 0x7e6bc4: r8 = Map
    //     0x7e6bc4: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ab8] Type: Map
    //     0x7e6bc8: ldr             x8, [x8, #0xab8]
    // 0x7e6bcc: r3 = SubtypeTestCache
    //     0x7e6bcc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ac0] SubtypeTestCache
    //     0x7e6bd0: ldr             x3, [x3, #0xac0]
    // 0x7e6bd4: r30 = InstanceOfStub
    //     0x7e6bd4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7e6bd8: LoadField: r30 = r30->field_7
    //     0x7e6bd8: ldur            lr, [lr, #7]
    // 0x7e6bdc: blr             lr
    // 0x7e6be0: b               #0x7e6bf0
    // 0x7e6be4: r0 = false
    //     0x7e6be4: add             x0, NULL, #0x30  ; false
    // 0x7e6be8: b               #0x7e6bf0
    // 0x7e6bec: r0 = true
    //     0x7e6bec: add             x0, NULL, #0x20  ; true
    // 0x7e6bf0: tbnz            w0, #4, #0x7e6c4c
    // 0x7e6bf4: ldur            x3, [fp, #-8]
    // 0x7e6bf8: r0 = LoadClassIdInstr(r3)
    //     0x7e6bf8: ldur            x0, [x3, #-1]
    //     0x7e6bfc: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6c00: mov             x1, x3
    // 0x7e6c04: r2 = "sub_category"
    //     0x7e6c04: add             x2, PP, #0x32, lsl #12  ; [pp+0x32aa8] "sub_category"
    //     0x7e6c08: ldr             x2, [x2, #0xaa8]
    // 0x7e6c0c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6c0c: sub             lr, x0, #0x114
    //     0x7e6c10: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6c14: blr             lr
    // 0x7e6c18: mov             x3, x0
    // 0x7e6c1c: r2 = Null
    //     0x7e6c1c: mov             x2, NULL
    // 0x7e6c20: r1 = Null
    //     0x7e6c20: mov             x1, NULL
    // 0x7e6c24: stur            x3, [fp, #-0x60]
    // 0x7e6c28: r8 = Map<String, dynamic>
    //     0x7e6c28: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e6c2c: r3 = Null
    //     0x7e6c2c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ac8] Null
    //     0x7e6c30: ldr             x3, [x3, #0xac8]
    // 0x7e6c34: r0 = Map<String, dynamic>()
    //     0x7e6c34: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e6c38: ldur            x2, [fp, #-0x60]
    // 0x7e6c3c: r1 = Null
    //     0x7e6c3c: mov             x1, NULL
    // 0x7e6c40: r0 = CampaignCategory.fromMap()
    //     0x7e6c40: bl              #0x7e7adc  ; [package:nuonline/app/data/models/campaign.dart] CampaignCategory::CampaignCategory.fromMap
    // 0x7e6c44: mov             x4, x0
    // 0x7e6c48: b               #0x7e6c50
    // 0x7e6c4c: r4 = Null
    //     0x7e6c4c: mov             x4, NULL
    // 0x7e6c50: ldur            x3, [fp, #-8]
    // 0x7e6c54: stur            x4, [fp, #-0x60]
    // 0x7e6c58: r0 = LoadClassIdInstr(r3)
    //     0x7e6c58: ldur            x0, [x3, #-1]
    //     0x7e6c5c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6c60: mov             x1, x3
    // 0x7e6c64: r2 = "organization"
    //     0x7e6c64: add             x2, PP, #0x32, lsl #12  ; [pp+0x32ad8] "organization"
    //     0x7e6c68: ldr             x2, [x2, #0xad8]
    // 0x7e6c6c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6c6c: sub             lr, x0, #0x114
    //     0x7e6c70: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6c74: blr             lr
    // 0x7e6c78: mov             x3, x0
    // 0x7e6c7c: r2 = Null
    //     0x7e6c7c: mov             x2, NULL
    // 0x7e6c80: r1 = Null
    //     0x7e6c80: mov             x1, NULL
    // 0x7e6c84: stur            x3, [fp, #-0x68]
    // 0x7e6c88: r8 = Map<String, dynamic>
    //     0x7e6c88: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e6c8c: r3 = Null
    //     0x7e6c8c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ae0] Null
    //     0x7e6c90: ldr             x3, [x3, #0xae0]
    // 0x7e6c94: r0 = Map<String, dynamic>()
    //     0x7e6c94: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e6c98: ldur            x2, [fp, #-0x68]
    // 0x7e6c9c: r1 = Null
    //     0x7e6c9c: mov             x1, NULL
    // 0x7e6ca0: r0 = CampaignOrganization.fromMap()
    //     0x7e6ca0: bl              #0x7e7504  ; [package:nuonline/app/data/models/campaign.dart] CampaignOrganization::CampaignOrganization.fromMap
    // 0x7e6ca4: mov             x4, x0
    // 0x7e6ca8: ldur            x3, [fp, #-8]
    // 0x7e6cac: stur            x4, [fp, #-0x68]
    // 0x7e6cb0: r0 = LoadClassIdInstr(r3)
    //     0x7e6cb0: ldur            x0, [x3, #-1]
    //     0x7e6cb4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6cb8: mov             x1, x3
    // 0x7e6cbc: r2 = "metadata"
    //     0x7e6cbc: add             x2, PP, #0x32, lsl #12  ; [pp+0x32af0] "metadata"
    //     0x7e6cc0: ldr             x2, [x2, #0xaf0]
    // 0x7e6cc4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6cc4: sub             lr, x0, #0x114
    //     0x7e6cc8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6ccc: blr             lr
    // 0x7e6cd0: r2 = Null
    //     0x7e6cd0: mov             x2, NULL
    // 0x7e6cd4: r1 = Null
    //     0x7e6cd4: mov             x1, NULL
    // 0x7e6cd8: cmp             w0, NULL
    // 0x7e6cdc: b.eq            #0x7e6d74
    // 0x7e6ce0: branchIfSmi(r0, 0x7e6d74)
    //     0x7e6ce0: tbz             w0, #0, #0x7e6d74
    // 0x7e6ce4: r3 = LoadClassIdInstr(r0)
    //     0x7e6ce4: ldur            x3, [x0, #-1]
    //     0x7e6ce8: ubfx            x3, x3, #0xc, #0x14
    // 0x7e6cec: r17 = 6717
    //     0x7e6cec: movz            x17, #0x1a3d
    // 0x7e6cf0: cmp             x3, x17
    // 0x7e6cf4: b.eq            #0x7e6d7c
    // 0x7e6cf8: r4 = LoadClassIdInstr(r0)
    //     0x7e6cf8: ldur            x4, [x0, #-1]
    //     0x7e6cfc: ubfx            x4, x4, #0xc, #0x14
    // 0x7e6d00: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x7e6d04: ldr             x3, [x3, #0x18]
    // 0x7e6d08: ldr             x3, [x3, x4, lsl #3]
    // 0x7e6d0c: LoadField: r3 = r3->field_2b
    //     0x7e6d0c: ldur            w3, [x3, #0x2b]
    // 0x7e6d10: DecompressPointer r3
    //     0x7e6d10: add             x3, x3, HEAP, lsl #32
    // 0x7e6d14: cmp             w3, NULL
    // 0x7e6d18: b.eq            #0x7e6d74
    // 0x7e6d1c: LoadField: r3 = r3->field_f
    //     0x7e6d1c: ldur            w3, [x3, #0xf]
    // 0x7e6d20: lsr             x3, x3, #3
    // 0x7e6d24: r17 = 6717
    //     0x7e6d24: movz            x17, #0x1a3d
    // 0x7e6d28: cmp             x3, x17
    // 0x7e6d2c: b.eq            #0x7e6d7c
    // 0x7e6d30: r3 = SubtypeTestCache
    //     0x7e6d30: add             x3, PP, #0x32, lsl #12  ; [pp+0x32af8] SubtypeTestCache
    //     0x7e6d34: ldr             x3, [x3, #0xaf8]
    // 0x7e6d38: r30 = Subtype1TestCacheStub
    //     0x7e6d38: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x7e6d3c: LoadField: r30 = r30->field_7
    //     0x7e6d3c: ldur            lr, [lr, #7]
    // 0x7e6d40: blr             lr
    // 0x7e6d44: cmp             w7, NULL
    // 0x7e6d48: b.eq            #0x7e6d54
    // 0x7e6d4c: tbnz            w7, #4, #0x7e6d74
    // 0x7e6d50: b               #0x7e6d7c
    // 0x7e6d54: r8 = Map
    //     0x7e6d54: add             x8, PP, #0x32, lsl #12  ; [pp+0x32b00] Type: Map
    //     0x7e6d58: ldr             x8, [x8, #0xb00]
    // 0x7e6d5c: r3 = SubtypeTestCache
    //     0x7e6d5c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b08] SubtypeTestCache
    //     0x7e6d60: ldr             x3, [x3, #0xb08]
    // 0x7e6d64: r30 = InstanceOfStub
    //     0x7e6d64: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7e6d68: LoadField: r30 = r30->field_7
    //     0x7e6d68: ldur            lr, [lr, #7]
    // 0x7e6d6c: blr             lr
    // 0x7e6d70: b               #0x7e6d80
    // 0x7e6d74: r0 = false
    //     0x7e6d74: add             x0, NULL, #0x30  ; false
    // 0x7e6d78: b               #0x7e6d80
    // 0x7e6d7c: r0 = true
    //     0x7e6d7c: add             x0, NULL, #0x20  ; true
    // 0x7e6d80: tbnz            w0, #4, #0x7e6dd4
    // 0x7e6d84: ldur            x1, [fp, #-8]
    // 0x7e6d88: r0 = LoadClassIdInstr(r1)
    //     0x7e6d88: ldur            x0, [x1, #-1]
    //     0x7e6d8c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6d90: r2 = "metadata"
    //     0x7e6d90: add             x2, PP, #0x32, lsl #12  ; [pp+0x32af0] "metadata"
    //     0x7e6d94: ldr             x2, [x2, #0xaf0]
    // 0x7e6d98: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6d98: sub             lr, x0, #0x114
    //     0x7e6d9c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6da0: blr             lr
    // 0x7e6da4: mov             x3, x0
    // 0x7e6da8: r2 = Null
    //     0x7e6da8: mov             x2, NULL
    // 0x7e6dac: r1 = Null
    //     0x7e6dac: mov             x1, NULL
    // 0x7e6db0: stur            x3, [fp, #-8]
    // 0x7e6db4: r8 = Map<String, dynamic>
    //     0x7e6db4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e6db8: r3 = Null
    //     0x7e6db8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b10] Null
    //     0x7e6dbc: ldr             x3, [x3, #0xb10]
    // 0x7e6dc0: r0 = Map<String, dynamic>()
    //     0x7e6dc0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e6dc4: ldur            x1, [fp, #-8]
    // 0x7e6dc8: r0 = _$CampaignMetadataFromJson()
    //     0x7e6dc8: bl              #0x7e6eac  ; [package:nuonline/app/data/models/campaign_metadata.dart] ::_$CampaignMetadataFromJson
    // 0x7e6dcc: mov             x12, x0
    // 0x7e6dd0: b               #0x7e6ddc
    // 0x7e6dd4: r12 = Instance__$CampaignMetadataUnknownImpl
    //     0x7e6dd4: add             x12, PP, #0x32, lsl #12  ; [pp+0x32b20] Obj!_$CampaignMetadataUnknownImpl@e0e641
    //     0x7e6dd8: ldr             x12, [x12, #0xb20]
    // 0x7e6ddc: ldur            x11, [fp, #-0x10]
    // 0x7e6de0: ldur            x10, [fp, #-0x18]
    // 0x7e6de4: ldur            x9, [fp, #-0x20]
    // 0x7e6de8: ldur            x8, [fp, #-0x28]
    // 0x7e6dec: ldur            x7, [fp, #-0x30]
    // 0x7e6df0: ldur            x6, [fp, #-0x38]
    // 0x7e6df4: ldur            x5, [fp, #-0x40]
    // 0x7e6df8: ldur            x4, [fp, #-0x48]
    // 0x7e6dfc: ldur            x3, [fp, #-0x58]
    // 0x7e6e00: ldur            x2, [fp, #-0x50]
    // 0x7e6e04: ldur            x1, [fp, #-0x60]
    // 0x7e6e08: ldur            x0, [fp, #-0x68]
    // 0x7e6e0c: stur            x12, [fp, #-8]
    // 0x7e6e10: r13 = LoadInt32Instr(r11)
    //     0x7e6e10: sbfx            x13, x11, #1, #0x1f
    //     0x7e6e14: tbz             w11, #0, #0x7e6e1c
    //     0x7e6e18: ldur            x13, [x11, #7]
    // 0x7e6e1c: stur            x13, [fp, #-0x70]
    // 0x7e6e20: r0 = Campaign()
    //     0x7e6e20: bl              #0x7e6ea0  ; AllocateCampaignStub -> Campaign (size=0x44)
    // 0x7e6e24: ldur            x1, [fp, #-0x70]
    // 0x7e6e28: StoreField: r0->field_7 = r1
    //     0x7e6e28: stur            x1, [x0, #7]
    // 0x7e6e2c: ldur            x1, [fp, #-0x18]
    // 0x7e6e30: StoreField: r0->field_f = r1
    //     0x7e6e30: stur            w1, [x0, #0xf]
    // 0x7e6e34: ldur            x1, [fp, #-0x20]
    // 0x7e6e38: StoreField: r0->field_13 = r1
    //     0x7e6e38: stur            w1, [x0, #0x13]
    // 0x7e6e3c: ldur            x1, [fp, #-0x28]
    // 0x7e6e40: ArrayStore: r0[0] = r1  ; List_4
    //     0x7e6e40: stur            w1, [x0, #0x17]
    // 0x7e6e44: ldur            x1, [fp, #-0x30]
    // 0x7e6e48: StoreField: r0->field_1b = r1
    //     0x7e6e48: stur            w1, [x0, #0x1b]
    // 0x7e6e4c: ldur            x1, [fp, #-0x38]
    // 0x7e6e50: StoreField: r0->field_1f = r1
    //     0x7e6e50: stur            w1, [x0, #0x1f]
    // 0x7e6e54: ldur            x1, [fp, #-0x40]
    // 0x7e6e58: StoreField: r0->field_23 = r1
    //     0x7e6e58: stur            w1, [x0, #0x23]
    // 0x7e6e5c: ldur            x1, [fp, #-0x48]
    // 0x7e6e60: StoreField: r0->field_27 = r1
    //     0x7e6e60: stur            w1, [x0, #0x27]
    // 0x7e6e64: ldur            x1, [fp, #-0x58]
    // 0x7e6e68: StoreField: r0->field_2b = r1
    //     0x7e6e68: stur            x1, [x0, #0x2b]
    // 0x7e6e6c: ldur            x1, [fp, #-0x50]
    // 0x7e6e70: StoreField: r0->field_33 = r1
    //     0x7e6e70: stur            w1, [x0, #0x33]
    // 0x7e6e74: ldur            x1, [fp, #-0x60]
    // 0x7e6e78: StoreField: r0->field_37 = r1
    //     0x7e6e78: stur            w1, [x0, #0x37]
    // 0x7e6e7c: ldur            x1, [fp, #-0x68]
    // 0x7e6e80: StoreField: r0->field_3b = r1
    //     0x7e6e80: stur            w1, [x0, #0x3b]
    // 0x7e6e84: ldur            x1, [fp, #-8]
    // 0x7e6e88: StoreField: r0->field_3f = r1
    //     0x7e6e88: stur            w1, [x0, #0x3f]
    // 0x7e6e8c: LeaveFrame
    //     0x7e6e8c: mov             SP, fp
    //     0x7e6e90: ldp             fp, lr, [SP], #0x10
    // 0x7e6e94: ret
    //     0x7e6e94: ret             
    // 0x7e6e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e6e98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e6e9c: b               #0x7e65e8
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x7e7e34, size: 0x188
    // 0x7e7e34: EnterFrame
    //     0x7e7e34: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7e38: mov             fp, SP
    // 0x7e7e3c: AllocStack(0x20)
    //     0x7e7e3c: sub             SP, SP, #0x20
    // 0x7e7e40: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7e7e40: mov             x3, x1
    //     0x7e7e44: stur            x1, [fp, #-8]
    // 0x7e7e48: CheckStackOverflow
    //     0x7e7e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7e4c: cmp             SP, x16
    //     0x7e7e50: b.ls            #0x7e7fb4
    // 0x7e7e54: mov             x0, x3
    // 0x7e7e58: r2 = Null
    //     0x7e7e58: mov             x2, NULL
    // 0x7e7e5c: r1 = Null
    //     0x7e7e5c: mov             x1, NULL
    // 0x7e7e60: cmp             w0, NULL
    // 0x7e7e64: b.eq            #0x7e7f08
    // 0x7e7e68: branchIfSmi(r0, 0x7e7f08)
    //     0x7e7e68: tbz             w0, #0, #0x7e7f08
    // 0x7e7e6c: r3 = LoadClassIdInstr(r0)
    //     0x7e7e6c: ldur            x3, [x0, #-1]
    //     0x7e7e70: ubfx            x3, x3, #0xc, #0x14
    // 0x7e7e74: r17 = 6718
    //     0x7e7e74: movz            x17, #0x1a3e
    // 0x7e7e78: cmp             x3, x17
    // 0x7e7e7c: b.eq            #0x7e7f10
    // 0x7e7e80: sub             x3, x3, #0x5a
    // 0x7e7e84: cmp             x3, #2
    // 0x7e7e88: b.ls            #0x7e7f10
    // 0x7e7e8c: r4 = LoadClassIdInstr(r0)
    //     0x7e7e8c: ldur            x4, [x0, #-1]
    //     0x7e7e90: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7e94: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x7e7e98: ldr             x3, [x3, #0x18]
    // 0x7e7e9c: ldr             x3, [x3, x4, lsl #3]
    // 0x7e7ea0: LoadField: r3 = r3->field_2b
    //     0x7e7ea0: ldur            w3, [x3, #0x2b]
    // 0x7e7ea4: DecompressPointer r3
    //     0x7e7ea4: add             x3, x3, HEAP, lsl #32
    // 0x7e7ea8: cmp             w3, NULL
    // 0x7e7eac: b.eq            #0x7e7f08
    // 0x7e7eb0: LoadField: r3 = r3->field_f
    //     0x7e7eb0: ldur            w3, [x3, #0xf]
    // 0x7e7eb4: lsr             x3, x3, #3
    // 0x7e7eb8: r17 = 6718
    //     0x7e7eb8: movz            x17, #0x1a3e
    // 0x7e7ebc: cmp             x3, x17
    // 0x7e7ec0: b.eq            #0x7e7f10
    // 0x7e7ec4: r3 = SubtypeTestCache
    //     0x7e7ec4: add             x3, PP, #0x32, lsl #12  ; [pp+0x329a0] SubtypeTestCache
    //     0x7e7ec8: ldr             x3, [x3, #0x9a0]
    // 0x7e7ecc: r30 = Subtype1TestCacheStub
    //     0x7e7ecc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x7e7ed0: LoadField: r30 = r30->field_7
    //     0x7e7ed0: ldur            lr, [lr, #7]
    // 0x7e7ed4: blr             lr
    // 0x7e7ed8: cmp             w7, NULL
    // 0x7e7edc: b.eq            #0x7e7ee8
    // 0x7e7ee0: tbnz            w7, #4, #0x7e7f08
    // 0x7e7ee4: b               #0x7e7f10
    // 0x7e7ee8: r8 = List
    //     0x7e7ee8: add             x8, PP, #0x32, lsl #12  ; [pp+0x329a8] Type: List
    //     0x7e7eec: ldr             x8, [x8, #0x9a8]
    // 0x7e7ef0: r3 = SubtypeTestCache
    //     0x7e7ef0: add             x3, PP, #0x32, lsl #12  ; [pp+0x329b0] SubtypeTestCache
    //     0x7e7ef4: ldr             x3, [x3, #0x9b0]
    // 0x7e7ef8: r30 = InstanceOfStub
    //     0x7e7ef8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7e7efc: LoadField: r30 = r30->field_7
    //     0x7e7efc: ldur            lr, [lr, #7]
    // 0x7e7f00: blr             lr
    // 0x7e7f04: b               #0x7e7f14
    // 0x7e7f08: r0 = false
    //     0x7e7f08: add             x0, NULL, #0x30  ; false
    // 0x7e7f0c: b               #0x7e7f14
    // 0x7e7f10: r0 = true
    //     0x7e7f10: add             x0, NULL, #0x20  ; true
    // 0x7e7f14: tbnz            w0, #4, #0x7e7f98
    // 0x7e7f18: ldur            x0, [fp, #-8]
    // 0x7e7f1c: r1 = Function '<anonymous closure>': static.
    //     0x7e7f1c: add             x1, PP, #0x32, lsl #12  ; [pp+0x329b8] AnonymousClosure: static (0x7e7fbc), in [package:nuonline/app/data/models/campaign.dart] Campaign::fromResponse (0x7e7e34)
    //     0x7e7f20: ldr             x1, [x1, #0x9b8]
    // 0x7e7f24: r2 = Null
    //     0x7e7f24: mov             x2, NULL
    // 0x7e7f28: r0 = AllocateClosure()
    //     0x7e7f28: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e7f2c: mov             x1, x0
    // 0x7e7f30: ldur            x0, [fp, #-8]
    // 0x7e7f34: r2 = LoadClassIdInstr(r0)
    //     0x7e7f34: ldur            x2, [x0, #-1]
    //     0x7e7f38: ubfx            x2, x2, #0xc, #0x14
    // 0x7e7f3c: r16 = <Campaign>
    //     0x7e7f3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0x7e7f40: ldr             x16, [x16, #0x598]
    // 0x7e7f44: stp             x0, x16, [SP, #8]
    // 0x7e7f48: str             x1, [SP]
    // 0x7e7f4c: mov             x0, x2
    // 0x7e7f50: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e7f50: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e7f54: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7e7f54: movz            x17, #0xf28c
    //     0x7e7f58: add             lr, x0, x17
    //     0x7e7f5c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7f60: blr             lr
    // 0x7e7f64: r1 = LoadClassIdInstr(r0)
    //     0x7e7f64: ldur            x1, [x0, #-1]
    //     0x7e7f68: ubfx            x1, x1, #0xc, #0x14
    // 0x7e7f6c: mov             x16, x0
    // 0x7e7f70: mov             x0, x1
    // 0x7e7f74: mov             x1, x16
    // 0x7e7f78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7e7f78: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7e7f7c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7e7f7c: movz            x17, #0xd889
    //     0x7e7f80: add             lr, x0, x17
    //     0x7e7f84: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7f88: blr             lr
    // 0x7e7f8c: LeaveFrame
    //     0x7e7f8c: mov             SP, fp
    //     0x7e7f90: ldp             fp, lr, [SP], #0x10
    // 0x7e7f94: ret
    //     0x7e7f94: ret             
    // 0x7e7f98: r1 = <Campaign>
    //     0x7e7f98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0x7e7f9c: ldr             x1, [x1, #0x598]
    // 0x7e7fa0: r2 = 0
    //     0x7e7fa0: movz            x2, #0
    // 0x7e7fa4: r0 = _GrowableList()
    //     0x7e7fa4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e7fa8: LeaveFrame
    //     0x7e7fa8: mov             SP, fp
    //     0x7e7fac: ldp             fp, lr, [SP], #0x10
    // 0x7e7fb0: ret
    //     0x7e7fb0: ret             
    // 0x7e7fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7fb8: b               #0x7e7e54
  }
  [closure] static Campaign <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7e7fbc, size: 0x50
    // 0x7e7fbc: EnterFrame
    //     0x7e7fbc: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7fc0: mov             fp, SP
    // 0x7e7fc4: CheckStackOverflow
    //     0x7e7fc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7fc8: cmp             SP, x16
    //     0x7e7fcc: b.ls            #0x7e8004
    // 0x7e7fd0: ldr             x0, [fp, #0x10]
    // 0x7e7fd4: r2 = Null
    //     0x7e7fd4: mov             x2, NULL
    // 0x7e7fd8: r1 = Null
    //     0x7e7fd8: mov             x1, NULL
    // 0x7e7fdc: r8 = Map<String, dynamic>
    //     0x7e7fdc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e7fe0: r3 = Null
    //     0x7e7fe0: add             x3, PP, #0x32, lsl #12  ; [pp+0x329c0] Null
    //     0x7e7fe4: ldr             x3, [x3, #0x9c0]
    // 0x7e7fe8: r0 = Map<String, dynamic>()
    //     0x7e7fe8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e7fec: ldr             x2, [fp, #0x10]
    // 0x7e7ff0: r1 = Null
    //     0x7e7ff0: mov             x1, NULL
    // 0x7e7ff4: r0 = Campaign.fromMap()
    //     0x7e7ff4: bl              #0x7e65c8  ; [package:nuonline/app/data/models/campaign.dart] Campaign::Campaign.fromMap
    // 0x7e7ff8: LeaveFrame
    //     0x7e7ff8: mov             SP, fp
    //     0x7e7ffc: ldp             fp, lr, [SP], #0x10
    // 0x7e8000: ret
    //     0x7e8000: ret             
    // 0x7e8004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8008: b               #0x7e7fd0
  }
  get _ isEnded(/* No info */) {
    // ** addr: 0x91eb50, size: 0x68
    // 0x91eb50: EnterFrame
    //     0x91eb50: stp             fp, lr, [SP, #-0x10]!
    //     0x91eb54: mov             fp, SP
    // 0x91eb58: AllocStack(0x8)
    //     0x91eb58: sub             SP, SP, #8
    // 0x91eb5c: CheckStackOverflow
    //     0x91eb5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91eb60: cmp             SP, x16
    //     0x91eb64: b.ls            #0x91ebb0
    // 0x91eb68: LoadField: r0 = r1->field_1f
    //     0x91eb68: ldur            w0, [x1, #0x1f]
    // 0x91eb6c: DecompressPointer r0
    //     0x91eb6c: add             x0, x0, HEAP, lsl #32
    // 0x91eb70: stur            x0, [fp, #-8]
    // 0x91eb74: r0 = systemTime()
    //     0x91eb74: bl              #0x81b86c  ; [package:clock/clock.dart] ::systemTime
    // 0x91eb78: ldur            x1, [fp, #-8]
    // 0x91eb7c: mov             x2, x0
    // 0x91eb80: r0 = difference()
    //     0x91eb80: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0x91eb84: LoadField: r1 = r0->field_7
    //     0x91eb84: ldur            x1, [x0, #7]
    // 0x91eb88: r2 = 86400000000
    //     0x91eb88: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x91eb8c: ldr             x2, [x2, #0x268]
    // 0x91eb90: sdiv            x3, x1, x2
    // 0x91eb94: cmp             x3, #0
    // 0x91eb98: r16 = true
    //     0x91eb98: add             x16, NULL, #0x20  ; true
    // 0x91eb9c: r17 = false
    //     0x91eb9c: add             x17, NULL, #0x30  ; false
    // 0x91eba0: csel            x0, x16, x17, le
    // 0x91eba4: LeaveFrame
    //     0x91eba4: mov             SP, fp
    //     0x91eba8: ldp             fp, lr, [SP], #0x10
    // 0x91ebac: ret
    //     0x91ebac: ret             
    // 0x91ebb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ebb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ebb4: b               #0x91eb68
  }
  get _ progress(/* No info */) {
    // ** addr: 0xadee8c, size: 0x64
    // 0xadee8c: EnterFrame
    //     0xadee8c: stp             fp, lr, [SP, #-0x10]!
    //     0xadee90: mov             fp, SP
    // 0xadee94: AllocStack(0x10)
    //     0xadee94: sub             SP, SP, #0x10
    // 0xadee98: CheckStackOverflow
    //     0xadee98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadee9c: cmp             SP, x16
    //     0xadeea0: b.ls            #0xadeee8
    // 0xadeea4: LoadField: r0 = r1->field_27
    //     0xadeea4: ldur            w0, [x1, #0x27]
    // 0xadeea8: DecompressPointer r0
    //     0xadeea8: add             x0, x0, HEAP, lsl #32
    // 0xadeeac: LoadField: r2 = r1->field_23
    //     0xadeeac: ldur            w2, [x1, #0x23]
    // 0xadeeb0: DecompressPointer r2
    //     0xadeeb0: add             x2, x2, HEAP, lsl #32
    // 0xadeeb4: r1 = 60
    //     0xadeeb4: movz            x1, #0x3c
    // 0xadeeb8: branchIfSmi(r0, 0xadeec4)
    //     0xadeeb8: tbz             w0, #0, #0xadeec4
    // 0xadeebc: r1 = LoadClassIdInstr(r0)
    //     0xadeebc: ldur            x1, [x0, #-1]
    //     0xadeec0: ubfx            x1, x1, #0xc, #0x14
    // 0xadeec4: stp             x2, x0, [SP]
    // 0xadeec8: mov             x0, x1
    // 0xadeecc: r0 = GDT[cid_x0 + -0xff7]()
    //     0xadeecc: sub             lr, x0, #0xff7
    //     0xadeed0: ldr             lr, [x21, lr, lsl #3]
    //     0xadeed4: blr             lr
    // 0xadeed8: LoadField: d0 = r0->field_7
    //     0xadeed8: ldur            d0, [x0, #7]
    // 0xadeedc: LeaveFrame
    //     0xadeedc: mov             SP, fp
    //     0xadeee0: ldp             fp, lr, [SP], #0x10
    // 0xadeee4: ret
    //     0xadeee4: ret             
    // 0xadeee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadeee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadeeec: b               #0xadeea4
  }
  get _ remainingDay(/* No info */) {
    // ** addr: 0xadeef0, size: 0xdc
    // 0xadeef0: EnterFrame
    //     0xadeef0: stp             fp, lr, [SP, #-0x10]!
    //     0xadeef4: mov             fp, SP
    // 0xadeef8: AllocStack(0x18)
    //     0xadeef8: sub             SP, SP, #0x18
    // 0xadeefc: CheckStackOverflow
    //     0xadeefc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadef00: cmp             SP, x16
    //     0xadef04: b.ls            #0xadefc4
    // 0xadef08: LoadField: r0 = r1->field_1f
    //     0xadef08: ldur            w0, [x1, #0x1f]
    // 0xadef0c: DecompressPointer r0
    //     0xadef0c: add             x0, x0, HEAP, lsl #32
    // 0xadef10: stur            x0, [fp, #-8]
    // 0xadef14: r0 = DateTime()
    //     0xadef14: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xadef18: mov             x1, x0
    // 0xadef1c: r0 = false
    //     0xadef1c: add             x0, NULL, #0x30  ; false
    // 0xadef20: stur            x1, [fp, #-0x10]
    // 0xadef24: StoreField: r1->field_13 = r0
    //     0xadef24: stur            w0, [x1, #0x13]
    // 0xadef28: r0 = _getCurrentMicros()
    //     0xadef28: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xadef2c: r1 = LoadInt32Instr(r0)
    //     0xadef2c: sbfx            x1, x0, #1, #0x1f
    //     0xadef30: tbz             w0, #0, #0xadef38
    //     0xadef34: ldur            x1, [x0, #7]
    // 0xadef38: ldur            x2, [fp, #-0x10]
    // 0xadef3c: StoreField: r2->field_7 = r1
    //     0xadef3c: stur            x1, [x2, #7]
    // 0xadef40: ldur            x1, [fp, #-8]
    // 0xadef44: r0 = difference()
    //     0xadef44: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xadef48: LoadField: r1 = r0->field_7
    //     0xadef48: ldur            x1, [x0, #7]
    // 0xadef4c: r0 = 86400000000
    //     0xadef4c: add             x0, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xadef50: ldr             x0, [x0, #0x268]
    // 0xadef54: sdiv            x2, x1, x0
    // 0xadef58: cmp             x2, #0
    // 0xadef5c: b.gt            #0xadef74
    // 0xadef60: r0 = "Berakhir"
    //     0xadef60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f700] "Berakhir"
    //     0xadef64: ldr             x0, [x0, #0x700]
    // 0xadef68: LeaveFrame
    //     0xadef68: mov             SP, fp
    //     0xadef6c: ldp             fp, lr, [SP], #0x10
    // 0xadef70: ret
    //     0xadef70: ret             
    // 0xadef74: r0 = BoxInt64Instr(r2)
    //     0xadef74: sbfiz           x0, x2, #1, #0x1f
    //     0xadef78: cmp             x2, x0, asr #1
    //     0xadef7c: b.eq            #0xadef88
    //     0xadef80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadef84: stur            x2, [x0, #7]
    // 0xadef88: r1 = Null
    //     0xadef88: mov             x1, NULL
    // 0xadef8c: r2 = 4
    //     0xadef8c: movz            x2, #0x4
    // 0xadef90: stur            x0, [fp, #-8]
    // 0xadef94: r0 = AllocateArray()
    //     0xadef94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadef98: mov             x1, x0
    // 0xadef9c: ldur            x0, [fp, #-8]
    // 0xadefa0: StoreField: r1->field_f = r0
    //     0xadefa0: stur            w0, [x1, #0xf]
    // 0xadefa4: r16 = " hari lagi"
    //     0xadefa4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f708] " hari lagi"
    //     0xadefa8: ldr             x16, [x16, #0x708]
    // 0xadefac: StoreField: r1->field_13 = r16
    //     0xadefac: stur            w16, [x1, #0x13]
    // 0xadefb0: str             x1, [SP]
    // 0xadefb4: r0 = _interpolate()
    //     0xadefb4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xadefb8: LeaveFrame
    //     0xadefb8: mov             SP, fp
    //     0xadefbc: ldp             fp, lr, [SP], #0x10
    // 0xadefc0: ret
    //     0xadefc0: ret             
    // 0xadefc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadefc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadefc8: b               #0xadef08
  }
  get _ targetAmount(/* No info */) {
    // ** addr: 0xadefcc, size: 0x9c
    // 0xadefcc: EnterFrame
    //     0xadefcc: stp             fp, lr, [SP, #-0x10]!
    //     0xadefd0: mov             fp, SP
    // 0xadefd4: AllocStack(0x18)
    //     0xadefd4: sub             SP, SP, #0x18
    // 0xadefd8: SetupParameters(Campaign this /* r1 => r0, fp-0x8 */)
    //     0xadefd8: mov             x0, x1
    //     0xadefdc: stur            x1, [fp, #-8]
    // 0xadefe0: CheckStackOverflow
    //     0xadefe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadefe4: cmp             SP, x16
    //     0xadefe8: b.ls            #0xadf060
    // 0xadefec: r1 = Null
    //     0xadefec: mov             x1, NULL
    // 0xadeff0: r2 = 4
    //     0xadeff0: movz            x2, #0x4
    // 0xadeff4: r0 = AllocateArray()
    //     0xadeff4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadeff8: stur            x0, [fp, #-0x10]
    // 0xadeffc: r16 = "Rp"
    //     0xadeffc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xadf000: ldr             x16, [x16, #0xf90]
    // 0xadf004: StoreField: r0->field_f = r16
    //     0xadf004: stur            w16, [x0, #0xf]
    // 0xadf008: ldur            x1, [fp, #-8]
    // 0xadf00c: LoadField: r2 = r1->field_23
    //     0xadf00c: ldur            w2, [x1, #0x23]
    // 0xadf010: DecompressPointer r2
    //     0xadf010: add             x2, x2, HEAP, lsl #32
    // 0xadf014: mov             x1, x2
    // 0xadf018: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf018: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf01c: r0 = NumExtension.idr()
    //     0xadf01c: bl              #0xadf068  ; [package:nuonline/common/extensions/int_extension.dart] ::NumExtension.idr
    // 0xadf020: ldur            x1, [fp, #-0x10]
    // 0xadf024: ArrayStore: r1[1] = r0  ; List_4
    //     0xadf024: add             x25, x1, #0x13
    //     0xadf028: str             w0, [x25]
    //     0xadf02c: tbz             w0, #0, #0xadf048
    //     0xadf030: ldurb           w16, [x1, #-1]
    //     0xadf034: ldurb           w17, [x0, #-1]
    //     0xadf038: and             x16, x17, x16, lsr #2
    //     0xadf03c: tst             x16, HEAP, lsr #32
    //     0xadf040: b.eq            #0xadf048
    //     0xadf044: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadf048: ldur            x16, [fp, #-0x10]
    // 0xadf04c: str             x16, [SP]
    // 0xadf050: r0 = _interpolate()
    //     0xadf050: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xadf054: LeaveFrame
    //     0xadf054: mov             SP, fp
    //     0xadf058: ldp             fp, lr, [SP], #0x10
    // 0xadf05c: ret
    //     0xadf05c: ret             
    // 0xadf060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf064: b               #0xadefec
  }
  get _ amountRaised(/* No info */) {
    // ** addr: 0xadf16c, size: 0x9c
    // 0xadf16c: EnterFrame
    //     0xadf16c: stp             fp, lr, [SP, #-0x10]!
    //     0xadf170: mov             fp, SP
    // 0xadf174: AllocStack(0x18)
    //     0xadf174: sub             SP, SP, #0x18
    // 0xadf178: SetupParameters(Campaign this /* r1 => r0, fp-0x8 */)
    //     0xadf178: mov             x0, x1
    //     0xadf17c: stur            x1, [fp, #-8]
    // 0xadf180: CheckStackOverflow
    //     0xadf180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf184: cmp             SP, x16
    //     0xadf188: b.ls            #0xadf200
    // 0xadf18c: r1 = Null
    //     0xadf18c: mov             x1, NULL
    // 0xadf190: r2 = 4
    //     0xadf190: movz            x2, #0x4
    // 0xadf194: r0 = AllocateArray()
    //     0xadf194: bl              #0xec22fc  ; AllocateArrayStub
    // 0xadf198: stur            x0, [fp, #-0x10]
    // 0xadf19c: r16 = "Rp"
    //     0xadf19c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26f90] "Rp"
    //     0xadf1a0: ldr             x16, [x16, #0xf90]
    // 0xadf1a4: StoreField: r0->field_f = r16
    //     0xadf1a4: stur            w16, [x0, #0xf]
    // 0xadf1a8: ldur            x1, [fp, #-8]
    // 0xadf1ac: LoadField: r2 = r1->field_27
    //     0xadf1ac: ldur            w2, [x1, #0x27]
    // 0xadf1b0: DecompressPointer r2
    //     0xadf1b0: add             x2, x2, HEAP, lsl #32
    // 0xadf1b4: mov             x1, x2
    // 0xadf1b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf1b8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf1bc: r0 = NumExtension.idr()
    //     0xadf1bc: bl              #0xadf068  ; [package:nuonline/common/extensions/int_extension.dart] ::NumExtension.idr
    // 0xadf1c0: ldur            x1, [fp, #-0x10]
    // 0xadf1c4: ArrayStore: r1[1] = r0  ; List_4
    //     0xadf1c4: add             x25, x1, #0x13
    //     0xadf1c8: str             w0, [x25]
    //     0xadf1cc: tbz             w0, #0, #0xadf1e8
    //     0xadf1d0: ldurb           w16, [x1, #-1]
    //     0xadf1d4: ldurb           w17, [x0, #-1]
    //     0xadf1d8: and             x16, x17, x16, lsr #2
    //     0xadf1dc: tst             x16, HEAP, lsr #32
    //     0xadf1e0: b.eq            #0xadf1e8
    //     0xadf1e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xadf1e8: ldur            x16, [fp, #-0x10]
    // 0xadf1ec: str             x16, [SP]
    // 0xadf1f0: r0 = _interpolate()
    //     0xadf1f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xadf1f4: LeaveFrame
    //     0xadf1f4: mov             SP, fp
    //     0xadf1f8: ldp             fp, lr, [SP], #0x10
    // 0xadf1fc: ret
    //     0xadf1fc: ret             
    // 0xadf200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf204: b               #0xadf18c
  }
}

// class id: 6838, size: 0x14, field offset: 0x14
enum CampaignOrganizationLevel extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d1e0, size: 0x64
    // 0xc4d1e0: EnterFrame
    //     0xc4d1e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d1e4: mov             fp, SP
    // 0xc4d1e8: AllocStack(0x10)
    //     0xc4d1e8: sub             SP, SP, #0x10
    // 0xc4d1ec: SetupParameters(CampaignOrganizationLevel this /* r1 => r0, fp-0x8 */)
    //     0xc4d1ec: mov             x0, x1
    //     0xc4d1f0: stur            x1, [fp, #-8]
    // 0xc4d1f4: CheckStackOverflow
    //     0xc4d1f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d1f8: cmp             SP, x16
    //     0xc4d1fc: b.ls            #0xc4d23c
    // 0xc4d200: r1 = Null
    //     0xc4d200: mov             x1, NULL
    // 0xc4d204: r2 = 4
    //     0xc4d204: movz            x2, #0x4
    // 0xc4d208: r0 = AllocateArray()
    //     0xc4d208: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d20c: r16 = "CampaignOrganizationLevel."
    //     0xc4d20c: add             x16, PP, #0x41, lsl #12  ; [pp+0x413d0] "CampaignOrganizationLevel."
    //     0xc4d210: ldr             x16, [x16, #0x3d0]
    // 0xc4d214: StoreField: r0->field_f = r16
    //     0xc4d214: stur            w16, [x0, #0xf]
    // 0xc4d218: ldur            x1, [fp, #-8]
    // 0xc4d21c: LoadField: r2 = r1->field_f
    //     0xc4d21c: ldur            w2, [x1, #0xf]
    // 0xc4d220: DecompressPointer r2
    //     0xc4d220: add             x2, x2, HEAP, lsl #32
    // 0xc4d224: StoreField: r0->field_13 = r2
    //     0xc4d224: stur            w2, [x0, #0x13]
    // 0xc4d228: str             x0, [SP]
    // 0xc4d22c: r0 = _interpolate()
    //     0xc4d22c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d230: LeaveFrame
    //     0xc4d230: mov             SP, fp
    //     0xc4d234: ldp             fp, lr, [SP], #0x10
    // 0xc4d238: ret
    //     0xc4d238: ret             
    // 0xc4d23c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d23c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d240: b               #0xc4d200
  }
}
