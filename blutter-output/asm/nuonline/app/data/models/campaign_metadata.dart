// lib: , url: package:nuonline/app/data/models/campaign_metadata.dart

// class id: 1050010, size: 0x8
class :: {

  static _ _$CampaignMetadataFromJson(/* No info */) {
    // ** addr: 0x7e6eac, size: 0x80
    // 0x7e6eac: EnterFrame
    //     0x7e6eac: stp             fp, lr, [SP, #-0x10]!
    //     0x7e6eb0: mov             fp, SP
    // 0x7e6eb4: AllocStack(0x18)
    //     0x7e6eb4: sub             SP, SP, #0x18
    // 0x7e6eb8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7e6eb8: mov             x3, x1
    //     0x7e6ebc: stur            x1, [fp, #-8]
    // 0x7e6ec0: CheckStackOverflow
    //     0x7e6ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e6ec4: cmp             SP, x16
    //     0x7e6ec8: b.ls            #0x7e6f24
    // 0x7e6ecc: r0 = LoadClassIdInstr(r3)
    //     0x7e6ecc: ldur            x0, [x3, #-1]
    //     0x7e6ed0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e6ed4: mov             x1, x3
    // 0x7e6ed8: r2 = "type"
    //     0x7e6ed8: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7e6edc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e6edc: sub             lr, x0, #0x114
    //     0x7e6ee0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e6ee4: blr             lr
    // 0x7e6ee8: r16 = "qurban"
    //     0x7e6ee8: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ae88] "qurban"
    //     0x7e6eec: ldr             x16, [x16, #0xe88]
    // 0x7e6ef0: stp             x0, x16, [SP]
    // 0x7e6ef4: r0 = ==()
    //     0x7e6ef4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x7e6ef8: tbnz            w0, #4, #0x7e6f10
    // 0x7e6efc: ldur            x1, [fp, #-8]
    // 0x7e6f00: r0 = _$$CampaignMetadataQurbanImplFromJson()
    //     0x7e6f00: bl              #0x7e7384  ; [package:nuonline/app/data/models/campaign_metadata.dart] ::_$$CampaignMetadataQurbanImplFromJson
    // 0x7e6f04: LeaveFrame
    //     0x7e6f04: mov             SP, fp
    //     0x7e6f08: ldp             fp, lr, [SP], #0x10
    // 0x7e6f0c: ret
    //     0x7e6f0c: ret             
    // 0x7e6f10: ldur            x1, [fp, #-8]
    // 0x7e6f14: r0 = _$$CampaignMetadataUnknownImplFromJson()
    //     0x7e6f14: bl              #0x7e72d4  ; [package:nuonline/app/data/models/campaign_metadata.dart] ::_$$CampaignMetadataUnknownImplFromJson
    // 0x7e6f18: LeaveFrame
    //     0x7e6f18: mov             SP, fp
    //     0x7e6f1c: ldp             fp, lr, [SP], #0x10
    // 0x7e6f20: ret
    //     0x7e6f20: ret             
    // 0x7e6f24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e6f24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e6f28: b               #0x7e6ecc
  }
  static _ _$$CampaignMetadataUnknownImplToJson(/* No info */) {
    // ** addr: 0x7e7084, size: 0x64
    // 0x7e7084: EnterFrame
    //     0x7e7084: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7088: mov             fp, SP
    // 0x7e708c: AllocStack(0x18)
    //     0x7e708c: sub             SP, SP, #0x18
    // 0x7e7090: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7e7090: mov             x0, x1
    //     0x7e7094: stur            x1, [fp, #-8]
    // 0x7e7098: CheckStackOverflow
    //     0x7e7098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e709c: cmp             SP, x16
    //     0x7e70a0: b.ls            #0x7e70e0
    // 0x7e70a4: r1 = Null
    //     0x7e70a4: mov             x1, NULL
    // 0x7e70a8: r2 = 4
    //     0x7e70a8: movz            x2, #0x4
    // 0x7e70ac: r0 = AllocateArray()
    //     0x7e70ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e70b0: r16 = "type"
    //     0x7e70b0: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7e70b4: StoreField: r0->field_f = r16
    //     0x7e70b4: stur            w16, [x0, #0xf]
    // 0x7e70b8: ldur            x1, [fp, #-8]
    // 0x7e70bc: LoadField: r2 = r1->field_7
    //     0x7e70bc: ldur            w2, [x1, #7]
    // 0x7e70c0: DecompressPointer r2
    //     0x7e70c0: add             x2, x2, HEAP, lsl #32
    // 0x7e70c4: StoreField: r0->field_13 = r2
    //     0x7e70c4: stur            w2, [x0, #0x13]
    // 0x7e70c8: r16 = <String, dynamic>
    //     0x7e70c8: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7e70cc: stp             x0, x16, [SP]
    // 0x7e70d0: r0 = Map._fromLiteral()
    //     0x7e70d0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e70d4: LeaveFrame
    //     0x7e70d4: mov             SP, fp
    //     0x7e70d8: ldp             fp, lr, [SP], #0x10
    // 0x7e70dc: ret
    //     0x7e70dc: ret             
    // 0x7e70e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e70e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e70e4: b               #0x7e70a4
  }
  static _ _$$CampaignMetadataQurbanImplToJson(/* No info */) {
    // ** addr: 0x7e7240, size: 0x94
    // 0x7e7240: EnterFrame
    //     0x7e7240: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7244: mov             fp, SP
    // 0x7e7248: AllocStack(0x18)
    //     0x7e7248: sub             SP, SP, #0x18
    // 0x7e724c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7e724c: mov             x0, x1
    //     0x7e7250: stur            x1, [fp, #-8]
    // 0x7e7254: CheckStackOverflow
    //     0x7e7254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7258: cmp             SP, x16
    //     0x7e725c: b.ls            #0x7e72cc
    // 0x7e7260: r1 = Null
    //     0x7e7260: mov             x1, NULL
    // 0x7e7264: r2 = 12
    //     0x7e7264: movz            x2, #0xc
    // 0x7e7268: r0 = AllocateArray()
    //     0x7e7268: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e726c: r16 = "weight"
    //     0x7e726c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32b38] "weight"
    //     0x7e7270: ldr             x16, [x16, #0xb38]
    // 0x7e7274: StoreField: r0->field_f = r16
    //     0x7e7274: stur            w16, [x0, #0xf]
    // 0x7e7278: ldur            x1, [fp, #-8]
    // 0x7e727c: LoadField: r2 = r1->field_7
    //     0x7e727c: ldur            w2, [x1, #7]
    // 0x7e7280: DecompressPointer r2
    //     0x7e7280: add             x2, x2, HEAP, lsl #32
    // 0x7e7284: StoreField: r0->field_13 = r2
    //     0x7e7284: stur            w2, [x0, #0x13]
    // 0x7e7288: r16 = "price"
    //     0x7e7288: add             x16, PP, #0x27, lsl #12  ; [pp+0x273b8] "price"
    //     0x7e728c: ldr             x16, [x16, #0x3b8]
    // 0x7e7290: ArrayStore: r0[0] = r16  ; List_4
    //     0x7e7290: stur            w16, [x0, #0x17]
    // 0x7e7294: LoadField: r2 = r1->field_b
    //     0x7e7294: ldur            w2, [x1, #0xb]
    // 0x7e7298: DecompressPointer r2
    //     0x7e7298: add             x2, x2, HEAP, lsl #32
    // 0x7e729c: StoreField: r0->field_1b = r2
    //     0x7e729c: stur            w2, [x0, #0x1b]
    // 0x7e72a0: r16 = "type"
    //     0x7e72a0: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7e72a4: StoreField: r0->field_1f = r16
    //     0x7e72a4: stur            w16, [x0, #0x1f]
    // 0x7e72a8: LoadField: r2 = r1->field_f
    //     0x7e72a8: ldur            w2, [x1, #0xf]
    // 0x7e72ac: DecompressPointer r2
    //     0x7e72ac: add             x2, x2, HEAP, lsl #32
    // 0x7e72b0: StoreField: r0->field_23 = r2
    //     0x7e72b0: stur            w2, [x0, #0x23]
    // 0x7e72b4: r16 = <String, dynamic>
    //     0x7e72b4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7e72b8: stp             x0, x16, [SP]
    // 0x7e72bc: r0 = Map._fromLiteral()
    //     0x7e72bc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e72c0: LeaveFrame
    //     0x7e72c0: mov             SP, fp
    //     0x7e72c4: ldp             fp, lr, [SP], #0x10
    // 0x7e72c8: ret
    //     0x7e72c8: ret             
    // 0x7e72cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e72cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e72d0: b               #0x7e7260
  }
  static _ _$$CampaignMetadataUnknownImplFromJson(/* No info */) {
    // ** addr: 0x7e72d4, size: 0xa4
    // 0x7e72d4: EnterFrame
    //     0x7e72d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7e72d8: mov             fp, SP
    // 0x7e72dc: AllocStack(0x8)
    //     0x7e72dc: sub             SP, SP, #8
    // 0x7e72e0: CheckStackOverflow
    //     0x7e72e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e72e4: cmp             SP, x16
    //     0x7e72e8: b.ls            #0x7e7370
    // 0x7e72ec: r0 = LoadClassIdInstr(r1)
    //     0x7e72ec: ldur            x0, [x1, #-1]
    //     0x7e72f0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e72f4: r2 = "type"
    //     0x7e72f4: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7e72f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e72f8: sub             lr, x0, #0x114
    //     0x7e72fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7300: blr             lr
    // 0x7e7304: mov             x3, x0
    // 0x7e7308: r2 = Null
    //     0x7e7308: mov             x2, NULL
    // 0x7e730c: r1 = Null
    //     0x7e730c: mov             x1, NULL
    // 0x7e7310: stur            x3, [fp, #-8]
    // 0x7e7314: r4 = 60
    //     0x7e7314: movz            x4, #0x3c
    // 0x7e7318: branchIfSmi(r0, 0x7e7324)
    //     0x7e7318: tbz             w0, #0, #0x7e7324
    // 0x7e731c: r4 = LoadClassIdInstr(r0)
    //     0x7e731c: ldur            x4, [x0, #-1]
    //     0x7e7320: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7324: sub             x4, x4, #0x5e
    // 0x7e7328: cmp             x4, #1
    // 0x7e732c: b.ls            #0x7e7340
    // 0x7e7330: r8 = String?
    //     0x7e7330: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e7334: r3 = Null
    //     0x7e7334: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b28] Null
    //     0x7e7338: ldr             x3, [x3, #0xb28]
    // 0x7e733c: r0 = String?()
    //     0x7e733c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7340: ldur            x0, [fp, #-8]
    // 0x7e7344: cmp             w0, NULL
    // 0x7e7348: b.ne            #0x7e7354
    // 0x7e734c: r0 = "unknown"
    //     0x7e734c: add             x0, PP, #0xd, lsl #12  ; [pp+0xd490] "unknown"
    //     0x7e7350: ldr             x0, [x0, #0x490]
    // 0x7e7354: stur            x0, [fp, #-8]
    // 0x7e7358: r0 = _$CampaignMetadataUnknownImpl()
    //     0x7e7358: bl              #0x7e7378  ; Allocate_$CampaignMetadataUnknownImplStub -> _$CampaignMetadataUnknownImpl (size=0xc)
    // 0x7e735c: ldur            x1, [fp, #-8]
    // 0x7e7360: StoreField: r0->field_7 = r1
    //     0x7e7360: stur            w1, [x0, #7]
    // 0x7e7364: LeaveFrame
    //     0x7e7364: mov             SP, fp
    //     0x7e7368: ldp             fp, lr, [SP], #0x10
    // 0x7e736c: ret
    //     0x7e736c: ret             
    // 0x7e7370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7370: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7374: b               #0x7e72ec
  }
  static _ _$$CampaignMetadataQurbanImplFromJson(/* No info */) {
    // ** addr: 0x7e7384, size: 0x174
    // 0x7e7384: EnterFrame
    //     0x7e7384: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7388: mov             fp, SP
    // 0x7e738c: AllocStack(0x18)
    //     0x7e738c: sub             SP, SP, #0x18
    // 0x7e7390: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7e7390: mov             x3, x1
    //     0x7e7394: stur            x1, [fp, #-8]
    // 0x7e7398: CheckStackOverflow
    //     0x7e7398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e739c: cmp             SP, x16
    //     0x7e73a0: b.ls            #0x7e74f0
    // 0x7e73a4: r0 = LoadClassIdInstr(r3)
    //     0x7e73a4: ldur            x0, [x3, #-1]
    //     0x7e73a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e73ac: mov             x1, x3
    // 0x7e73b0: r2 = "weight"
    //     0x7e73b0: add             x2, PP, #0x32, lsl #12  ; [pp+0x32b38] "weight"
    //     0x7e73b4: ldr             x2, [x2, #0xb38]
    // 0x7e73b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e73b8: sub             lr, x0, #0x114
    //     0x7e73bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e73c0: blr             lr
    // 0x7e73c4: mov             x3, x0
    // 0x7e73c8: r2 = Null
    //     0x7e73c8: mov             x2, NULL
    // 0x7e73cc: r1 = Null
    //     0x7e73cc: mov             x1, NULL
    // 0x7e73d0: stur            x3, [fp, #-0x10]
    // 0x7e73d4: r4 = 60
    //     0x7e73d4: movz            x4, #0x3c
    // 0x7e73d8: branchIfSmi(r0, 0x7e73e4)
    //     0x7e73d8: tbz             w0, #0, #0x7e73e4
    // 0x7e73dc: r4 = LoadClassIdInstr(r0)
    //     0x7e73dc: ldur            x4, [x0, #-1]
    //     0x7e73e0: ubfx            x4, x4, #0xc, #0x14
    // 0x7e73e4: sub             x4, x4, #0x5e
    // 0x7e73e8: cmp             x4, #1
    // 0x7e73ec: b.ls            #0x7e7400
    // 0x7e73f0: r8 = String?
    //     0x7e73f0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e73f4: r3 = Null
    //     0x7e73f4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b40] Null
    //     0x7e73f8: ldr             x3, [x3, #0xb40]
    // 0x7e73fc: r0 = String?()
    //     0x7e73fc: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e7400: ldur            x3, [fp, #-8]
    // 0x7e7404: r0 = LoadClassIdInstr(r3)
    //     0x7e7404: ldur            x0, [x3, #-1]
    //     0x7e7408: ubfx            x0, x0, #0xc, #0x14
    // 0x7e740c: mov             x1, x3
    // 0x7e7410: r2 = "price"
    //     0x7e7410: add             x2, PP, #0x27, lsl #12  ; [pp+0x273b8] "price"
    //     0x7e7414: ldr             x2, [x2, #0x3b8]
    // 0x7e7418: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7418: sub             lr, x0, #0x114
    //     0x7e741c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7420: blr             lr
    // 0x7e7424: mov             x3, x0
    // 0x7e7428: r2 = Null
    //     0x7e7428: mov             x2, NULL
    // 0x7e742c: r1 = Null
    //     0x7e742c: mov             x1, NULL
    // 0x7e7430: stur            x3, [fp, #-0x18]
    // 0x7e7434: branchIfSmi(r0, 0x7e7460)
    //     0x7e7434: tbz             w0, #0, #0x7e7460
    // 0x7e7438: r4 = LoadClassIdInstr(r0)
    //     0x7e7438: ldur            x4, [x0, #-1]
    //     0x7e743c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e7440: sub             x4, x4, #0x3c
    // 0x7e7444: cmp             x4, #2
    // 0x7e7448: b.ls            #0x7e7460
    // 0x7e744c: r8 = num?
    //     0x7e744c: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7e7450: ldr             x8, [x8, #0x4f0]
    // 0x7e7454: r3 = Null
    //     0x7e7454: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b50] Null
    //     0x7e7458: ldr             x3, [x3, #0xb50]
    // 0x7e745c: r0 = DefaultNullableTypeTest()
    //     0x7e745c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7e7460: ldur            x1, [fp, #-8]
    // 0x7e7464: r0 = LoadClassIdInstr(r1)
    //     0x7e7464: ldur            x0, [x1, #-1]
    //     0x7e7468: ubfx            x0, x0, #0xc, #0x14
    // 0x7e746c: r2 = "type"
    //     0x7e746c: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7e7470: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e7470: sub             lr, x0, #0x114
    //     0x7e7474: ldr             lr, [x21, lr, lsl #3]
    //     0x7e7478: blr             lr
    // 0x7e747c: mov             x3, x0
    // 0x7e7480: r2 = Null
    //     0x7e7480: mov             x2, NULL
    // 0x7e7484: r1 = Null
    //     0x7e7484: mov             x1, NULL
    // 0x7e7488: stur            x3, [fp, #-8]
    // 0x7e748c: r4 = 60
    //     0x7e748c: movz            x4, #0x3c
    // 0x7e7490: branchIfSmi(r0, 0x7e749c)
    //     0x7e7490: tbz             w0, #0, #0x7e749c
    // 0x7e7494: r4 = LoadClassIdInstr(r0)
    //     0x7e7494: ldur            x4, [x0, #-1]
    //     0x7e7498: ubfx            x4, x4, #0xc, #0x14
    // 0x7e749c: sub             x4, x4, #0x5e
    // 0x7e74a0: cmp             x4, #1
    // 0x7e74a4: b.ls            #0x7e74b8
    // 0x7e74a8: r8 = String?
    //     0x7e74a8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e74ac: r3 = Null
    //     0x7e74ac: add             x3, PP, #0x32, lsl #12  ; [pp+0x32b60] Null
    //     0x7e74b0: ldr             x3, [x3, #0xb60]
    // 0x7e74b4: r0 = String?()
    //     0x7e74b4: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e74b8: r0 = _$CampaignMetadataQurbanImpl()
    //     0x7e74b8: bl              #0x7e74f8  ; Allocate_$CampaignMetadataQurbanImplStub -> _$CampaignMetadataQurbanImpl (size=0x14)
    // 0x7e74bc: ldur            x1, [fp, #-0x10]
    // 0x7e74c0: StoreField: r0->field_7 = r1
    //     0x7e74c0: stur            w1, [x0, #7]
    // 0x7e74c4: ldur            x1, [fp, #-0x18]
    // 0x7e74c8: StoreField: r0->field_b = r1
    //     0x7e74c8: stur            w1, [x0, #0xb]
    // 0x7e74cc: ldur            x1, [fp, #-8]
    // 0x7e74d0: cmp             w1, NULL
    // 0x7e74d4: b.ne            #0x7e74e0
    // 0x7e74d8: r1 = "qurban"
    //     0x7e74d8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ae88] "qurban"
    //     0x7e74dc: ldr             x1, [x1, #0xe88]
    // 0x7e74e0: StoreField: r0->field_f = r1
    //     0x7e74e0: stur            w1, [x0, #0xf]
    // 0x7e74e4: LeaveFrame
    //     0x7e74e4: mov             SP, fp
    //     0x7e74e8: ldp             fp, lr, [SP], #0x10
    // 0x7e74ec: ret
    //     0x7e74ec: ret             
    // 0x7e74f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e74f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e74f4: b               #0x7e73a4
  }
}

// class id: 1149, size: 0x8, field offset: 0x8
abstract class CampaignMetadataUnknown extends Object
    implements CampaignMetadata {
}

// class id: 1150, size: 0xc, field offset: 0x8
//   const constructor, 
class _$CampaignMetadataUnknownImpl extends Object
    implements CampaignMetadataUnknown {

  _OneByteString field_8;

  Y0 map<Y0>(_$CampaignMetadataUnknownImpl, {required (dynamic, CampaignMetadataQurban) => Y0 qurban, required (dynamic, CampaignMetadataUnknown) => Y0 unknown}) {
    // ** addr: 0x7e6f2c, size: 0x110
    // 0x7e6f2c: EnterFrame
    //     0x7e6f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e6f30: mov             fp, SP
    // 0x7e6f34: AllocStack(0x40)
    //     0x7e6f34: sub             SP, SP, #0x40
    // 0x7e6f38: SetupParameters(_$CampaignMetadataUnknownImpl this /* r3, fp-0x20 */, {dynamic required /* r5, fp-0x18 */, dynamic required /* r6, fp-0x10 */})
    //     0x7e6f38: ldur            w0, [x4, #0x13]
    //     0x7e6f3c: sub             x1, x0, #2
    //     0x7e6f40: add             x3, fp, w1, sxtw #2
    //     0x7e6f44: ldr             x3, [x3, #0x10]
    //     0x7e6f48: stur            x3, [fp, #-0x20]
    //     0x7e6f4c: ldur            w1, [x4, #0x23]
    //     0x7e6f50: add             x1, x1, HEAP, lsl #32
    //     0x7e6f54: sub             w2, w0, w1
    //     0x7e6f58: add             x5, fp, w2, sxtw #2
    //     0x7e6f5c: ldr             x5, [x5, #8]
    //     0x7e6f60: stur            x5, [fp, #-0x18]
    //     0x7e6f64: ldur            w1, [x4, #0x2b]
    //     0x7e6f68: add             x1, x1, HEAP, lsl #32
    //     0x7e6f6c: sub             w2, w0, w1
    //     0x7e6f70: add             x6, fp, w2, sxtw #2
    //     0x7e6f74: ldr             x6, [x6, #8]
    //     0x7e6f78: stur            x6, [fp, #-0x10]
    //     0x7e6f7c: ldur            w0, [x4, #0xf]
    //     0x7e6f80: cbnz            w0, #0x7e6f8c
    //     0x7e6f84: mov             x1, NULL
    //     0x7e6f88: b               #0x7e6f9c
    //     0x7e6f8c: ldur            w1, [x4, #0x17]
    //     0x7e6f90: add             x2, fp, w1, sxtw #2
    //     0x7e6f94: ldr             x2, [x2, #0x10]
    //     0x7e6f98: mov             x1, x2
    // 0x7e6f9c: CheckStackOverflow
    //     0x7e6f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e6fa0: cmp             SP, x16
    //     0x7e6fa4: b.ls            #0x7e7034
    // 0x7e6fa8: cbnz            w0, #0x7e6fb4
    // 0x7e6fac: r4 = <Object?>
    //     0x7e6fac: ldr             x4, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x7e6fb0: b               #0x7e6fb8
    // 0x7e6fb4: mov             x4, x1
    // 0x7e6fb8: mov             x0, x5
    // 0x7e6fbc: mov             x1, x4
    // 0x7e6fc0: stur            x4, [fp, #-8]
    // 0x7e6fc4: r2 = Null
    //     0x7e6fc4: mov             x2, NULL
    // 0x7e6fc8: r8 = (dynamic this, CampaignMetadataQurban) => Y0
    //     0x7e6fc8: add             x8, PP, #0x41, lsl #12  ; [pp+0x41348] FunctionType: (dynamic this, CampaignMetadataQurban) => Y0
    //     0x7e6fcc: ldr             x8, [x8, #0x348]
    // 0x7e6fd0: LoadField: r9 = r8->field_7
    //     0x7e6fd0: ldur            x9, [x8, #7]
    // 0x7e6fd4: r3 = Null
    //     0x7e6fd4: add             x3, PP, #0x41, lsl #12  ; [pp+0x41350] Null
    //     0x7e6fd8: ldr             x3, [x3, #0x350]
    // 0x7e6fdc: blr             x9
    // 0x7e6fe0: ldur            x0, [fp, #-0x10]
    // 0x7e6fe4: ldur            x1, [fp, #-8]
    // 0x7e6fe8: r2 = Null
    //     0x7e6fe8: mov             x2, NULL
    // 0x7e6fec: r8 = (dynamic this, CampaignMetadataUnknown) => Y0
    //     0x7e6fec: add             x8, PP, #0x41, lsl #12  ; [pp+0x41360] FunctionType: (dynamic this, CampaignMetadataUnknown) => Y0
    //     0x7e6ff0: ldr             x8, [x8, #0x360]
    // 0x7e6ff4: LoadField: r9 = r8->field_7
    //     0x7e6ff4: ldur            x9, [x8, #7]
    // 0x7e6ff8: r3 = Null
    //     0x7e6ff8: add             x3, PP, #0x41, lsl #12  ; [pp+0x41368] Null
    //     0x7e6ffc: ldr             x3, [x3, #0x368]
    // 0x7e7000: blr             x9
    // 0x7e7004: ldur            x16, [fp, #-8]
    // 0x7e7008: ldur            lr, [fp, #-0x20]
    // 0x7e700c: stp             lr, x16, [SP, #0x10]
    // 0x7e7010: ldur            x16, [fp, #-0x18]
    // 0x7e7014: ldur            lr, [fp, #-0x10]
    // 0x7e7018: stp             lr, x16, [SP]
    // 0x7e701c: r4 = const [0x1, 0x3, 0x3, 0x1, qurban, 0x1, unknown, 0x2, null]
    //     0x7e701c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32928] List(9) [0x1, 0x3, 0x3, 0x1, "qurban", 0x1, "unknown", 0x2, Null]
    //     0x7e7020: ldr             x4, [x4, #0x928]
    // 0x7e7024: r0 = map()
    //     0x7e7024: bl              #0xe77764  ; [package:nuonline/app/data/models/campaign_metadata.dart] _$CampaignMetadataUnknownImpl::map
    // 0x7e7028: LeaveFrame
    //     0x7e7028: mov             SP, fp
    //     0x7e702c: ldp             fp, lr, [SP], #0x10
    // 0x7e7030: ret
    //     0x7e7030: ret             
    // 0x7e7034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7038: b               #0x7e6fa8
  }
  Map<String, dynamic> toJson(_$CampaignMetadataUnknownImpl) {
    // ** addr: 0x7e7054, size: 0x48
    // 0x7e7054: EnterFrame
    //     0x7e7054: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7058: mov             fp, SP
    // 0x7e705c: CheckStackOverflow
    //     0x7e705c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7060: cmp             SP, x16
    //     0x7e7064: b.ls            #0x7e707c
    // 0x7e7068: ldr             x1, [fp, #0x10]
    // 0x7e706c: r0 = _$$CampaignMetadataUnknownImplToJson()
    //     0x7e706c: bl              #0x7e7084  ; [package:nuonline/app/data/models/campaign_metadata.dart] ::_$$CampaignMetadataUnknownImplToJson
    // 0x7e7070: LeaveFrame
    //     0x7e7070: mov             SP, fp
    //     0x7e7074: ldp             fp, lr, [SP], #0x10
    // 0x7e7078: ret
    //     0x7e7078: ret             
    // 0x7e707c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e707c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7080: b               #0x7e7068
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0ef4, size: 0x3c
    // 0xbf0ef4: EnterFrame
    //     0xbf0ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf0ef8: mov             fp, SP
    // 0xbf0efc: AllocStack(0x8)
    //     0xbf0efc: sub             SP, SP, #8
    // 0xbf0f00: CheckStackOverflow
    //     0xbf0f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0f04: cmp             SP, x16
    //     0xbf0f08: b.ls            #0xbf0f28
    // 0xbf0f0c: r16 = _$CampaignMetadataUnknownImpl
    //     0xbf0f0c: add             x16, PP, #0x41, lsl #12  ; [pp+0x41378] Type: _$CampaignMetadataUnknownImpl
    //     0xbf0f10: ldr             x16, [x16, #0x378]
    // 0xbf0f14: str             x16, [SP]
    // 0xbf0f18: r0 = hashCode()
    //     0xbf0f18: bl              #0xbf4e5c  ; [dart:core] _AbstractType::hashCode
    // 0xbf0f1c: LeaveFrame
    //     0xbf0f1c: mov             SP, fp
    //     0xbf0f20: ldp             fp, lr, [SP], #0x10
    // 0xbf0f24: ret
    //     0xbf0f24: ret             
    // 0xbf0f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf0f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0f2c: b               #0xbf0f0c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc32960, size: 0xc
    // 0xc32960: r0 = "CampaignMetadata.unknown()"
    //     0xc32960: add             x0, PP, #0x41, lsl #12  ; [pp+0x41380] "CampaignMetadata.unknown()"
    //     0xc32964: ldr             x0, [x0, #0x380]
    // 0xc32968: ret
    //     0xc32968: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7ae9c, size: 0xbc
    // 0xd7ae9c: EnterFrame
    //     0xd7ae9c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7aea0: mov             fp, SP
    // 0xd7aea4: AllocStack(0x10)
    //     0xd7aea4: sub             SP, SP, #0x10
    // 0xd7aea8: CheckStackOverflow
    //     0xd7aea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7aeac: cmp             SP, x16
    //     0xd7aeb0: b.ls            #0xd7af50
    // 0xd7aeb4: ldr             x0, [fp, #0x10]
    // 0xd7aeb8: cmp             w0, NULL
    // 0xd7aebc: b.ne            #0xd7aed0
    // 0xd7aec0: r0 = false
    //     0xd7aec0: add             x0, NULL, #0x30  ; false
    // 0xd7aec4: LeaveFrame
    //     0xd7aec4: mov             SP, fp
    //     0xd7aec8: ldp             fp, lr, [SP], #0x10
    // 0xd7aecc: ret
    //     0xd7aecc: ret             
    // 0xd7aed0: ldr             x1, [fp, #0x18]
    // 0xd7aed4: cmp             w1, w0
    // 0xd7aed8: b.ne            #0xd7aee4
    // 0xd7aedc: r0 = true
    //     0xd7aedc: add             x0, NULL, #0x20  ; true
    // 0xd7aee0: b               #0xd7af44
    // 0xd7aee4: str             x0, [SP]
    // 0xd7aee8: r0 = runtimeType()
    //     0xd7aee8: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7aeec: r1 = LoadClassIdInstr(r0)
    //     0xd7aeec: ldur            x1, [x0, #-1]
    //     0xd7aef0: ubfx            x1, x1, #0xc, #0x14
    // 0xd7aef4: r16 = _$CampaignMetadataUnknownImpl
    //     0xd7aef4: add             x16, PP, #0x41, lsl #12  ; [pp+0x41378] Type: _$CampaignMetadataUnknownImpl
    //     0xd7aef8: ldr             x16, [x16, #0x378]
    // 0xd7aefc: stp             x16, x0, [SP]
    // 0xd7af00: mov             x0, x1
    // 0xd7af04: mov             lr, x0
    // 0xd7af08: ldr             lr, [x21, lr, lsl #3]
    // 0xd7af0c: blr             lr
    // 0xd7af10: tbnz            w0, #4, #0xd7af40
    // 0xd7af14: ldr             x1, [fp, #0x10]
    // 0xd7af18: r2 = 60
    //     0xd7af18: movz            x2, #0x3c
    // 0xd7af1c: branchIfSmi(r1, 0xd7af28)
    //     0xd7af1c: tbz             w1, #0, #0xd7af28
    // 0xd7af20: r2 = LoadClassIdInstr(r1)
    //     0xd7af20: ldur            x2, [x1, #-1]
    //     0xd7af24: ubfx            x2, x2, #0xc, #0x14
    // 0xd7af28: cmp             x2, #0x47e
    // 0xd7af2c: r16 = true
    //     0xd7af2c: add             x16, NULL, #0x20  ; true
    // 0xd7af30: r17 = false
    //     0xd7af30: add             x17, NULL, #0x30  ; false
    // 0xd7af34: csel            x1, x16, x17, eq
    // 0xd7af38: mov             x0, x1
    // 0xd7af3c: b               #0xd7af44
    // 0xd7af40: r0 = false
    //     0xd7af40: add             x0, NULL, #0x30  ; false
    // 0xd7af44: LeaveFrame
    //     0xd7af44: mov             SP, fp
    //     0xd7af48: ldp             fp, lr, [SP], #0x10
    // 0xd7af4c: ret
    //     0xd7af4c: ret             
    // 0xd7af50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7af50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7af54: b               #0xd7aeb4
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xe7771c, size: 0x48
    // 0xe7771c: EnterFrame
    //     0xe7771c: stp             fp, lr, [SP, #-0x10]!
    //     0xe77720: mov             fp, SP
    // 0xe77724: AllocStack(0x8)
    //     0xe77724: sub             SP, SP, #8
    // 0xe77728: CheckStackOverflow
    //     0xe77728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7772c: cmp             SP, x16
    //     0xe77730: b.ls            #0xe7775c
    // 0xe77734: ldr             x16, [fp, #0x18]
    // 0xe77738: str             x16, [SP]
    // 0xe7773c: ldr             x0, [fp, #0x18]
    // 0xe77740: ClosureCall
    //     0xe77740: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xe77744: ldur            x2, [x0, #0x1f]
    //     0xe77748: blr             x2
    // 0xe7774c: r0 = ""
    //     0xe7774c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe77750: LeaveFrame
    //     0xe77750: mov             SP, fp
    //     0xe77754: ldp             fp, lr, [SP], #0x10
    // 0xe77758: ret
    //     0xe77758: ret             
    // 0xe7775c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7775c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77760: b               #0xe77734
  }
  Y0 map<Y0>(_$CampaignMetadataUnknownImpl, {required (dynamic, CampaignMetadataQurban) => Y0 qurban, required (dynamic, CampaignMetadataUnknown) => Y0 unknown}) {
    // ** addr: 0xe77764, size: 0x60
    // 0xe77764: EnterFrame
    //     0xe77764: stp             fp, lr, [SP, #-0x10]!
    //     0xe77768: mov             fp, SP
    // 0xe7776c: AllocStack(0x10)
    //     0xe7776c: sub             SP, SP, #0x10
    // 0xe77770: SetupParameters(_$CampaignMetadataUnknownImpl this /* r2 */, {dynamic required, dynamic required /* r0 */})
    //     0xe77770: ldur            w0, [x4, #0x13]
    //     0xe77774: sub             x1, x0, #2
    //     0xe77778: add             x2, fp, w1, sxtw #2
    //     0xe7777c: ldr             x2, [x2, #0x10]
    //     0xe77780: ldur            w1, [x4, #0x2b]
    //     0xe77784: add             x1, x1, HEAP, lsl #32
    //     0xe77788: sub             w3, w0, w1
    //     0xe7778c: add             x0, fp, w3, sxtw #2
    //     0xe77790: ldr             x0, [x0, #8]
    // 0xe77794: CheckStackOverflow
    //     0xe77794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77798: cmp             SP, x16
    //     0xe7779c: b.ls            #0xe777bc
    // 0xe777a0: stp             x2, x0, [SP]
    // 0xe777a4: ClosureCall
    //     0xe777a4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe777a8: ldur            x2, [x0, #0x1f]
    //     0xe777ac: blr             x2
    // 0xe777b0: LeaveFrame
    //     0xe777b0: mov             SP, fp
    //     0xe777b4: ldp             fp, lr, [SP], #0x10
    // 0xe777b8: ret
    //     0xe777b8: ret             
    // 0xe777bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe777bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe777c0: b               #0xe777a0
  }
}

// class id: 1151, size: 0x8, field offset: 0x8
abstract class CampaignMetadataQurban extends Object
    implements CampaignMetadata {
}

// class id: 1152, size: 0x14, field offset: 0x8
//   const constructor, 
class _$CampaignMetadataQurbanImpl extends Object
    implements CampaignMetadataQurban {

  Y0 map<Y0>(_$CampaignMetadataQurbanImpl, {required (dynamic, CampaignMetadataQurban) => Y0 qurban, required (dynamic, CampaignMetadataUnknown) => Y0 unknown}) {
    // ** addr: 0x7e70e8, size: 0x110
    // 0x7e70e8: EnterFrame
    //     0x7e70e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7e70ec: mov             fp, SP
    // 0x7e70f0: AllocStack(0x40)
    //     0x7e70f0: sub             SP, SP, #0x40
    // 0x7e70f4: SetupParameters(_$CampaignMetadataQurbanImpl this /* r3, fp-0x20 */, {dynamic required /* r5, fp-0x18 */, dynamic required /* r6, fp-0x10 */})
    //     0x7e70f4: ldur            w0, [x4, #0x13]
    //     0x7e70f8: sub             x1, x0, #2
    //     0x7e70fc: add             x3, fp, w1, sxtw #2
    //     0x7e7100: ldr             x3, [x3, #0x10]
    //     0x7e7104: stur            x3, [fp, #-0x20]
    //     0x7e7108: ldur            w1, [x4, #0x23]
    //     0x7e710c: add             x1, x1, HEAP, lsl #32
    //     0x7e7110: sub             w2, w0, w1
    //     0x7e7114: add             x5, fp, w2, sxtw #2
    //     0x7e7118: ldr             x5, [x5, #8]
    //     0x7e711c: stur            x5, [fp, #-0x18]
    //     0x7e7120: ldur            w1, [x4, #0x2b]
    //     0x7e7124: add             x1, x1, HEAP, lsl #32
    //     0x7e7128: sub             w2, w0, w1
    //     0x7e712c: add             x6, fp, w2, sxtw #2
    //     0x7e7130: ldr             x6, [x6, #8]
    //     0x7e7134: stur            x6, [fp, #-0x10]
    //     0x7e7138: ldur            w0, [x4, #0xf]
    //     0x7e713c: cbnz            w0, #0x7e7148
    //     0x7e7140: mov             x1, NULL
    //     0x7e7144: b               #0x7e7158
    //     0x7e7148: ldur            w1, [x4, #0x17]
    //     0x7e714c: add             x2, fp, w1, sxtw #2
    //     0x7e7150: ldr             x2, [x2, #0x10]
    //     0x7e7154: mov             x1, x2
    // 0x7e7158: CheckStackOverflow
    //     0x7e7158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e715c: cmp             SP, x16
    //     0x7e7160: b.ls            #0x7e71f0
    // 0x7e7164: cbnz            w0, #0x7e7170
    // 0x7e7168: r4 = <Object?>
    //     0x7e7168: ldr             x4, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x7e716c: b               #0x7e7174
    // 0x7e7170: mov             x4, x1
    // 0x7e7174: mov             x0, x5
    // 0x7e7178: mov             x1, x4
    // 0x7e717c: stur            x4, [fp, #-8]
    // 0x7e7180: r2 = Null
    //     0x7e7180: mov             x2, NULL
    // 0x7e7184: r8 = (dynamic this, CampaignMetadataQurban) => Y0
    //     0x7e7184: add             x8, PP, #0x41, lsl #12  ; [pp+0x41388] FunctionType: (dynamic this, CampaignMetadataQurban) => Y0
    //     0x7e7188: ldr             x8, [x8, #0x388]
    // 0x7e718c: LoadField: r9 = r8->field_7
    //     0x7e718c: ldur            x9, [x8, #7]
    // 0x7e7190: r3 = Null
    //     0x7e7190: add             x3, PP, #0x41, lsl #12  ; [pp+0x41390] Null
    //     0x7e7194: ldr             x3, [x3, #0x390]
    // 0x7e7198: blr             x9
    // 0x7e719c: ldur            x0, [fp, #-0x10]
    // 0x7e71a0: ldur            x1, [fp, #-8]
    // 0x7e71a4: r2 = Null
    //     0x7e71a4: mov             x2, NULL
    // 0x7e71a8: r8 = (dynamic this, CampaignMetadataUnknown) => Y0
    //     0x7e71a8: add             x8, PP, #0x41, lsl #12  ; [pp+0x413a0] FunctionType: (dynamic this, CampaignMetadataUnknown) => Y0
    //     0x7e71ac: ldr             x8, [x8, #0x3a0]
    // 0x7e71b0: LoadField: r9 = r8->field_7
    //     0x7e71b0: ldur            x9, [x8, #7]
    // 0x7e71b4: r3 = Null
    //     0x7e71b4: add             x3, PP, #0x41, lsl #12  ; [pp+0x413a8] Null
    //     0x7e71b8: ldr             x3, [x3, #0x3a8]
    // 0x7e71bc: blr             x9
    // 0x7e71c0: ldur            x16, [fp, #-8]
    // 0x7e71c4: ldur            lr, [fp, #-0x20]
    // 0x7e71c8: stp             lr, x16, [SP, #0x10]
    // 0x7e71cc: ldur            x16, [fp, #-0x18]
    // 0x7e71d0: ldur            lr, [fp, #-0x10]
    // 0x7e71d4: stp             lr, x16, [SP]
    // 0x7e71d8: r4 = const [0x1, 0x3, 0x3, 0x1, qurban, 0x1, unknown, 0x2, null]
    //     0x7e71d8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32928] List(9) [0x1, 0x3, 0x3, 0x1, "qurban", 0x1, "unknown", 0x2, Null]
    //     0x7e71dc: ldr             x4, [x4, #0x928]
    // 0x7e71e0: r0 = map()
    //     0x7e71e0: bl              #0xe776bc  ; [package:nuonline/app/data/models/campaign_metadata.dart] _$CampaignMetadataQurbanImpl::map
    // 0x7e71e4: LeaveFrame
    //     0x7e71e4: mov             SP, fp
    //     0x7e71e8: ldp             fp, lr, [SP], #0x10
    // 0x7e71ec: ret
    //     0x7e71ec: ret             
    // 0x7e71f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e71f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e71f4: b               #0x7e7164
  }
  Map<String, dynamic> toJson(_$CampaignMetadataQurbanImpl) {
    // ** addr: 0x7e7210, size: 0x48
    // 0x7e7210: EnterFrame
    //     0x7e7210: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7214: mov             fp, SP
    // 0x7e7218: CheckStackOverflow
    //     0x7e7218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e721c: cmp             SP, x16
    //     0x7e7220: b.ls            #0x7e7238
    // 0x7e7224: ldr             x1, [fp, #0x10]
    // 0x7e7228: r0 = _$$CampaignMetadataQurbanImplToJson()
    //     0x7e7228: bl              #0x7e7240  ; [package:nuonline/app/data/models/campaign_metadata.dart] ::_$$CampaignMetadataQurbanImplToJson
    // 0x7e722c: LeaveFrame
    //     0x7e722c: mov             SP, fp
    //     0x7e7230: ldp             fp, lr, [SP], #0x10
    // 0x7e7234: ret
    //     0x7e7234: ret             
    // 0x7e7238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e723c: b               #0x7e7224
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0e88, size: 0x6c
    // 0xbf0e88: EnterFrame
    //     0xbf0e88: stp             fp, lr, [SP, #-0x10]!
    //     0xbf0e8c: mov             fp, SP
    // 0xbf0e90: AllocStack(0x8)
    //     0xbf0e90: sub             SP, SP, #8
    // 0xbf0e94: CheckStackOverflow
    //     0xbf0e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0e98: cmp             SP, x16
    //     0xbf0e9c: b.ls            #0xbf0eec
    // 0xbf0ea0: ldr             x0, [fp, #0x10]
    // 0xbf0ea4: LoadField: r2 = r0->field_7
    //     0xbf0ea4: ldur            w2, [x0, #7]
    // 0xbf0ea8: DecompressPointer r2
    //     0xbf0ea8: add             x2, x2, HEAP, lsl #32
    // 0xbf0eac: LoadField: r1 = r0->field_b
    //     0xbf0eac: ldur            w1, [x0, #0xb]
    // 0xbf0eb0: DecompressPointer r1
    //     0xbf0eb0: add             x1, x1, HEAP, lsl #32
    // 0xbf0eb4: str             x1, [SP]
    // 0xbf0eb8: r1 = _$CampaignMetadataQurbanImpl
    //     0xbf0eb8: add             x1, PP, #0x41, lsl #12  ; [pp+0x413b8] Type: _$CampaignMetadataQurbanImpl
    //     0xbf0ebc: ldr             x1, [x1, #0x3b8]
    // 0xbf0ec0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf0ec0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf0ec4: r0 = hash()
    //     0xbf0ec4: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf0ec8: mov             x2, x0
    // 0xbf0ecc: r0 = BoxInt64Instr(r2)
    //     0xbf0ecc: sbfiz           x0, x2, #1, #0x1f
    //     0xbf0ed0: cmp             x2, x0, asr #1
    //     0xbf0ed4: b.eq            #0xbf0ee0
    //     0xbf0ed8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf0edc: stur            x2, [x0, #7]
    // 0xbf0ee0: LeaveFrame
    //     0xbf0ee0: mov             SP, fp
    //     0xbf0ee4: ldp             fp, lr, [SP], #0x10
    // 0xbf0ee8: ret
    //     0xbf0ee8: ret             
    // 0xbf0eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf0eec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0ef0: b               #0xbf0ea0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc328e4, size: 0x7c
    // 0xc328e4: EnterFrame
    //     0xc328e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc328e8: mov             fp, SP
    // 0xc328ec: AllocStack(0x8)
    //     0xc328ec: sub             SP, SP, #8
    // 0xc328f0: CheckStackOverflow
    //     0xc328f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc328f4: cmp             SP, x16
    //     0xc328f8: b.ls            #0xc32958
    // 0xc328fc: r1 = Null
    //     0xc328fc: mov             x1, NULL
    // 0xc32900: r2 = 10
    //     0xc32900: movz            x2, #0xa
    // 0xc32904: r0 = AllocateArray()
    //     0xc32904: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc32908: r16 = "CampaignMetadata.qurban(weight: "
    //     0xc32908: add             x16, PP, #0x41, lsl #12  ; [pp+0x413c0] "CampaignMetadata.qurban(weight: "
    //     0xc3290c: ldr             x16, [x16, #0x3c0]
    // 0xc32910: StoreField: r0->field_f = r16
    //     0xc32910: stur            w16, [x0, #0xf]
    // 0xc32914: ldr             x1, [fp, #0x10]
    // 0xc32918: LoadField: r2 = r1->field_7
    //     0xc32918: ldur            w2, [x1, #7]
    // 0xc3291c: DecompressPointer r2
    //     0xc3291c: add             x2, x2, HEAP, lsl #32
    // 0xc32920: StoreField: r0->field_13 = r2
    //     0xc32920: stur            w2, [x0, #0x13]
    // 0xc32924: r16 = ", price: "
    //     0xc32924: add             x16, PP, #0x41, lsl #12  ; [pp+0x413c8] ", price: "
    //     0xc32928: ldr             x16, [x16, #0x3c8]
    // 0xc3292c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc3292c: stur            w16, [x0, #0x17]
    // 0xc32930: LoadField: r2 = r1->field_b
    //     0xc32930: ldur            w2, [x1, #0xb]
    // 0xc32934: DecompressPointer r2
    //     0xc32934: add             x2, x2, HEAP, lsl #32
    // 0xc32938: StoreField: r0->field_1b = r2
    //     0xc32938: stur            w2, [x0, #0x1b]
    // 0xc3293c: r16 = ")"
    //     0xc3293c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc32940: StoreField: r0->field_1f = r16
    //     0xc32940: stur            w16, [x0, #0x1f]
    // 0xc32944: str             x0, [SP]
    // 0xc32948: r0 = _interpolate()
    //     0xc32948: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3294c: LeaveFrame
    //     0xc3294c: mov             SP, fp
    //     0xc32950: ldp             fp, lr, [SP], #0x10
    // 0xc32954: ret
    //     0xc32954: ret             
    // 0xc32958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc32958: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3295c: b               #0xc328fc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7ad14, size: 0x164
    // 0xd7ad14: EnterFrame
    //     0xd7ad14: stp             fp, lr, [SP, #-0x10]!
    //     0xd7ad18: mov             fp, SP
    // 0xd7ad1c: AllocStack(0x20)
    //     0xd7ad1c: sub             SP, SP, #0x20
    // 0xd7ad20: CheckStackOverflow
    //     0xd7ad20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7ad24: cmp             SP, x16
    //     0xd7ad28: b.ls            #0xd7ae70
    // 0xd7ad2c: ldr             x0, [fp, #0x10]
    // 0xd7ad30: cmp             w0, NULL
    // 0xd7ad34: b.ne            #0xd7ad48
    // 0xd7ad38: r0 = false
    //     0xd7ad38: add             x0, NULL, #0x30  ; false
    // 0xd7ad3c: LeaveFrame
    //     0xd7ad3c: mov             SP, fp
    //     0xd7ad40: ldp             fp, lr, [SP], #0x10
    // 0xd7ad44: ret
    //     0xd7ad44: ret             
    // 0xd7ad48: ldr             x1, [fp, #0x18]
    // 0xd7ad4c: cmp             w1, w0
    // 0xd7ad50: b.eq            #0xd7ae28
    // 0xd7ad54: str             x0, [SP]
    // 0xd7ad58: r0 = runtimeType()
    //     0xd7ad58: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7ad5c: r1 = LoadClassIdInstr(r0)
    //     0xd7ad5c: ldur            x1, [x0, #-1]
    //     0xd7ad60: ubfx            x1, x1, #0xc, #0x14
    // 0xd7ad64: r16 = _$CampaignMetadataQurbanImpl
    //     0xd7ad64: add             x16, PP, #0x41, lsl #12  ; [pp+0x413b8] Type: _$CampaignMetadataQurbanImpl
    //     0xd7ad68: ldr             x16, [x16, #0x3b8]
    // 0xd7ad6c: stp             x16, x0, [SP]
    // 0xd7ad70: mov             x0, x1
    // 0xd7ad74: mov             lr, x0
    // 0xd7ad78: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ad7c: blr             lr
    // 0xd7ad80: tbnz            w0, #4, #0xd7ae60
    // 0xd7ad84: ldr             x1, [fp, #0x10]
    // 0xd7ad88: r0 = 60
    //     0xd7ad88: movz            x0, #0x3c
    // 0xd7ad8c: branchIfSmi(r1, 0xd7ad98)
    //     0xd7ad8c: tbz             w1, #0, #0xd7ad98
    // 0xd7ad90: r0 = LoadClassIdInstr(r1)
    //     0xd7ad90: ldur            x0, [x1, #-1]
    //     0xd7ad94: ubfx            x0, x0, #0xc, #0x14
    // 0xd7ad98: cmp             x0, #0x480
    // 0xd7ad9c: b.ne            #0xd7ae60
    // 0xd7ada0: ldr             x2, [fp, #0x18]
    // 0xd7ada4: LoadField: r0 = r1->field_7
    //     0xd7ada4: ldur            w0, [x1, #7]
    // 0xd7ada8: DecompressPointer r0
    //     0xd7ada8: add             x0, x0, HEAP, lsl #32
    // 0xd7adac: LoadField: r3 = r2->field_7
    //     0xd7adac: ldur            w3, [x2, #7]
    // 0xd7adb0: DecompressPointer r3
    //     0xd7adb0: add             x3, x3, HEAP, lsl #32
    // 0xd7adb4: cmp             w0, w3
    // 0xd7adb8: b.ne            #0xd7adc8
    // 0xd7adbc: mov             x0, x1
    // 0xd7adc0: mov             x1, x2
    // 0xd7adc4: b               #0xd7adf0
    // 0xd7adc8: r4 = LoadClassIdInstr(r0)
    //     0xd7adc8: ldur            x4, [x0, #-1]
    //     0xd7adcc: ubfx            x4, x4, #0xc, #0x14
    // 0xd7add0: stp             x3, x0, [SP]
    // 0xd7add4: mov             x0, x4
    // 0xd7add8: mov             lr, x0
    // 0xd7addc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ade0: blr             lr
    // 0xd7ade4: tbnz            w0, #4, #0xd7ae60
    // 0xd7ade8: ldr             x1, [fp, #0x18]
    // 0xd7adec: ldr             x0, [fp, #0x10]
    // 0xd7adf0: LoadField: r2 = r0->field_b
    //     0xd7adf0: ldur            w2, [x0, #0xb]
    // 0xd7adf4: DecompressPointer r2
    //     0xd7adf4: add             x2, x2, HEAP, lsl #32
    // 0xd7adf8: stur            x2, [fp, #-0x10]
    // 0xd7adfc: LoadField: r3 = r1->field_b
    //     0xd7adfc: ldur            w3, [x1, #0xb]
    // 0xd7ae00: DecompressPointer r3
    //     0xd7ae00: add             x3, x3, HEAP, lsl #32
    // 0xd7ae04: mov             x0, x2
    // 0xd7ae08: mov             x1, x3
    // 0xd7ae0c: stur            x3, [fp, #-8]
    // 0xd7ae10: stp             x1, x0, [SP, #-0x10]!
    // 0xd7ae14: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd7ae14: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd7ae18: LoadField: r30 = r30->field_7
    //     0xd7ae18: ldur            lr, [lr, #7]
    // 0xd7ae1c: blr             lr
    // 0xd7ae20: ldp             x1, x0, [SP], #0x10
    // 0xd7ae24: b.ne            #0xd7ae30
    // 0xd7ae28: r0 = true
    //     0xd7ae28: add             x0, NULL, #0x20  ; true
    // 0xd7ae2c: b               #0xd7ae64
    // 0xd7ae30: ldur            x0, [fp, #-0x10]
    // 0xd7ae34: r1 = 60
    //     0xd7ae34: movz            x1, #0x3c
    // 0xd7ae38: branchIfSmi(r0, 0xd7ae44)
    //     0xd7ae38: tbz             w0, #0, #0xd7ae44
    // 0xd7ae3c: r1 = LoadClassIdInstr(r0)
    //     0xd7ae3c: ldur            x1, [x0, #-1]
    //     0xd7ae40: ubfx            x1, x1, #0xc, #0x14
    // 0xd7ae44: ldur            x16, [fp, #-8]
    // 0xd7ae48: stp             x16, x0, [SP]
    // 0xd7ae4c: mov             x0, x1
    // 0xd7ae50: mov             lr, x0
    // 0xd7ae54: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ae58: blr             lr
    // 0xd7ae5c: b               #0xd7ae64
    // 0xd7ae60: r0 = false
    //     0xd7ae60: add             x0, NULL, #0x30  ; false
    // 0xd7ae64: LeaveFrame
    //     0xd7ae64: mov             SP, fp
    //     0xd7ae68: ldp             fp, lr, [SP], #0x10
    // 0xd7ae6c: ret
    //     0xd7ae6c: ret             
    // 0xd7ae70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7ae70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7ae74: b               #0xd7ad2c
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xe77674, size: 0x48
    // 0xe77674: EnterFrame
    //     0xe77674: stp             fp, lr, [SP, #-0x10]!
    //     0xe77678: mov             fp, SP
    // 0xe7767c: AllocStack(0x10)
    //     0xe7767c: sub             SP, SP, #0x10
    // 0xe77680: CheckStackOverflow
    //     0xe77680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77684: cmp             SP, x16
    //     0xe77688: b.ls            #0xe776b4
    // 0xe7768c: ldr             x16, [fp, #0x10]
    // 0xe77690: ldr             lr, [fp, #0x20]
    // 0xe77694: stp             lr, x16, [SP]
    // 0xe77698: ldr             x0, [fp, #0x10]
    // 0xe7769c: ClosureCall
    //     0xe7769c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe776a0: ldur            x2, [x0, #0x1f]
    //     0xe776a4: blr             x2
    // 0xe776a8: LeaveFrame
    //     0xe776a8: mov             SP, fp
    //     0xe776ac: ldp             fp, lr, [SP], #0x10
    // 0xe776b0: ret
    //     0xe776b0: ret             
    // 0xe776b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe776b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe776b8: b               #0xe7768c
  }
  Y0 map<Y0>(_$CampaignMetadataQurbanImpl, {required (dynamic, CampaignMetadataQurban) => Y0 qurban, required (dynamic, CampaignMetadataUnknown) => Y0 unknown}) {
    // ** addr: 0xe776bc, size: 0x60
    // 0xe776bc: EnterFrame
    //     0xe776bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe776c0: mov             fp, SP
    // 0xe776c4: AllocStack(0x10)
    //     0xe776c4: sub             SP, SP, #0x10
    // 0xe776c8: SetupParameters(_$CampaignMetadataQurbanImpl this /* r2 */, {dynamic required /* r0 */})
    //     0xe776c8: ldur            w0, [x4, #0x13]
    //     0xe776cc: sub             x1, x0, #2
    //     0xe776d0: add             x2, fp, w1, sxtw #2
    //     0xe776d4: ldr             x2, [x2, #0x10]
    //     0xe776d8: ldur            w1, [x4, #0x23]
    //     0xe776dc: add             x1, x1, HEAP, lsl #32
    //     0xe776e0: sub             w3, w0, w1
    //     0xe776e4: add             x0, fp, w3, sxtw #2
    //     0xe776e8: ldr             x0, [x0, #8]
    // 0xe776ec: CheckStackOverflow
    //     0xe776ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe776f0: cmp             SP, x16
    //     0xe776f4: b.ls            #0xe77714
    // 0xe776f8: stp             x2, x0, [SP]
    // 0xe776fc: ClosureCall
    //     0xe776fc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe77700: ldur            x2, [x0, #0x1f]
    //     0xe77704: blr             x2
    // 0xe77708: LeaveFrame
    //     0xe77708: mov             SP, fp
    //     0xe7770c: ldp             fp, lr, [SP], #0x10
    // 0xe77710: ret
    //     0xe77710: ret             
    // 0xe77714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77718: b               #0xe776f8
  }
}

// class id: 1153, size: 0x8, field offset: 0x8
abstract class _$CampaignMetadata extends Object {
}

// class id: 1154, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _CampaignMetadata&Object&_$CampaignMetadata extends Object
     with _$CampaignMetadata {
}

// class id: 1155, size: 0x8, field offset: 0x8
abstract class CampaignMetadata extends _CampaignMetadata&Object&_$CampaignMetadata {
}
