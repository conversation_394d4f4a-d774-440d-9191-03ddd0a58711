// lib: , url: package:nuonline/app/data/models/category.dart

// class id: 1050011, size: 0x8
class :: {
}

// class id: 1609, size: 0x30, field offset: 0x14
class Category extends HiveObject {

  int dyn:get:id(Category) {
    // ** addr: 0x80d564, size: 0x48
    // 0x80d564: ldr             x2, [SP]
    // 0x80d568: LoadField: r3 = r2->field_13
    //     0x80d568: ldur            x3, [x2, #0x13]
    // 0x80d56c: r0 = BoxInt64Instr(r3)
    //     0x80d56c: sbfiz           x0, x3, #1, #0x1f
    //     0x80d570: cmp             x3, x0, asr #1
    //     0x80d574: b.eq            #0x80d590
    //     0x80d578: stp             fp, lr, [SP, #-0x10]!
    //     0x80d57c: mov             fp, SP
    //     0x80d580: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80d584: mov             SP, fp
    //     0x80d588: ldp             fp, lr, [SP], #0x10
    //     0x80d58c: stur            x3, [x0, #7]
    // 0x80d590: ret
    //     0x80d590: ret             
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x8aa75c, size: 0x180
    // 0x8aa75c: EnterFrame
    //     0x8aa75c: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa760: mov             fp, SP
    // 0x8aa764: AllocStack(0x20)
    //     0x8aa764: sub             SP, SP, #0x20
    // 0x8aa768: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8aa768: mov             x3, x1
    //     0x8aa76c: stur            x1, [fp, #-8]
    // 0x8aa770: CheckStackOverflow
    //     0x8aa770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa774: cmp             SP, x16
    //     0x8aa778: b.ls            #0x8aa8d4
    // 0x8aa77c: mov             x0, x3
    // 0x8aa780: r2 = Null
    //     0x8aa780: mov             x2, NULL
    // 0x8aa784: r1 = Null
    //     0x8aa784: mov             x1, NULL
    // 0x8aa788: cmp             w0, NULL
    // 0x8aa78c: b.eq            #0x8aa830
    // 0x8aa790: branchIfSmi(r0, 0x8aa830)
    //     0x8aa790: tbz             w0, #0, #0x8aa830
    // 0x8aa794: r3 = LoadClassIdInstr(r0)
    //     0x8aa794: ldur            x3, [x0, #-1]
    //     0x8aa798: ubfx            x3, x3, #0xc, #0x14
    // 0x8aa79c: r17 = 6718
    //     0x8aa79c: movz            x17, #0x1a3e
    // 0x8aa7a0: cmp             x3, x17
    // 0x8aa7a4: b.eq            #0x8aa838
    // 0x8aa7a8: sub             x3, x3, #0x5a
    // 0x8aa7ac: cmp             x3, #2
    // 0x8aa7b0: b.ls            #0x8aa838
    // 0x8aa7b4: r4 = LoadClassIdInstr(r0)
    //     0x8aa7b4: ldur            x4, [x0, #-1]
    //     0x8aa7b8: ubfx            x4, x4, #0xc, #0x14
    // 0x8aa7bc: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8aa7c0: ldr             x3, [x3, #0x18]
    // 0x8aa7c4: ldr             x3, [x3, x4, lsl #3]
    // 0x8aa7c8: LoadField: r3 = r3->field_2b
    //     0x8aa7c8: ldur            w3, [x3, #0x2b]
    // 0x8aa7cc: DecompressPointer r3
    //     0x8aa7cc: add             x3, x3, HEAP, lsl #32
    // 0x8aa7d0: cmp             w3, NULL
    // 0x8aa7d4: b.eq            #0x8aa830
    // 0x8aa7d8: LoadField: r3 = r3->field_f
    //     0x8aa7d8: ldur            w3, [x3, #0xf]
    // 0x8aa7dc: lsr             x3, x3, #3
    // 0x8aa7e0: r17 = 6718
    //     0x8aa7e0: movz            x17, #0x1a3e
    // 0x8aa7e4: cmp             x3, x17
    // 0x8aa7e8: b.eq            #0x8aa838
    // 0x8aa7ec: r3 = SubtypeTestCache
    //     0x8aa7ec: add             x3, PP, #0x36, lsl #12  ; [pp+0x363f0] SubtypeTestCache
    //     0x8aa7f0: ldr             x3, [x3, #0x3f0]
    // 0x8aa7f4: r30 = Subtype1TestCacheStub
    //     0x8aa7f4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8aa7f8: LoadField: r30 = r30->field_7
    //     0x8aa7f8: ldur            lr, [lr, #7]
    // 0x8aa7fc: blr             lr
    // 0x8aa800: cmp             w7, NULL
    // 0x8aa804: b.eq            #0x8aa810
    // 0x8aa808: tbnz            w7, #4, #0x8aa830
    // 0x8aa80c: b               #0x8aa838
    // 0x8aa810: r8 = List
    //     0x8aa810: add             x8, PP, #0x36, lsl #12  ; [pp+0x363f8] Type: List
    //     0x8aa814: ldr             x8, [x8, #0x3f8]
    // 0x8aa818: r3 = SubtypeTestCache
    //     0x8aa818: add             x3, PP, #0x36, lsl #12  ; [pp+0x36400] SubtypeTestCache
    //     0x8aa81c: ldr             x3, [x3, #0x400]
    // 0x8aa820: r30 = InstanceOfStub
    //     0x8aa820: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8aa824: LoadField: r30 = r30->field_7
    //     0x8aa824: ldur            lr, [lr, #7]
    // 0x8aa828: blr             lr
    // 0x8aa82c: b               #0x8aa83c
    // 0x8aa830: r0 = false
    //     0x8aa830: add             x0, NULL, #0x30  ; false
    // 0x8aa834: b               #0x8aa83c
    // 0x8aa838: r0 = true
    //     0x8aa838: add             x0, NULL, #0x20  ; true
    // 0x8aa83c: tbnz            w0, #4, #0x8aa8bc
    // 0x8aa840: ldur            x0, [fp, #-8]
    // 0x8aa844: r1 = Function '<anonymous closure>': static.
    //     0x8aa844: add             x1, PP, #0x36, lsl #12  ; [pp+0x36408] AnonymousClosure: static (0x8aa8dc), in [package:nuonline/app/data/models/category.dart] Category::fromResponse (0x8aa75c)
    //     0x8aa848: ldr             x1, [x1, #0x408]
    // 0x8aa84c: r2 = Null
    //     0x8aa84c: mov             x2, NULL
    // 0x8aa850: r0 = AllocateClosure()
    //     0x8aa850: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aa854: mov             x1, x0
    // 0x8aa858: ldur            x0, [fp, #-8]
    // 0x8aa85c: r2 = LoadClassIdInstr(r0)
    //     0x8aa85c: ldur            x2, [x0, #-1]
    //     0x8aa860: ubfx            x2, x2, #0xc, #0x14
    // 0x8aa864: r16 = <Category>
    //     0x8aa864: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aa868: stp             x0, x16, [SP, #8]
    // 0x8aa86c: str             x1, [SP]
    // 0x8aa870: mov             x0, x2
    // 0x8aa874: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa874: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa878: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8aa878: movz            x17, #0xf28c
    //     0x8aa87c: add             lr, x0, x17
    //     0x8aa880: ldr             lr, [x21, lr, lsl #3]
    //     0x8aa884: blr             lr
    // 0x8aa888: r1 = LoadClassIdInstr(r0)
    //     0x8aa888: ldur            x1, [x0, #-1]
    //     0x8aa88c: ubfx            x1, x1, #0xc, #0x14
    // 0x8aa890: mov             x16, x0
    // 0x8aa894: mov             x0, x1
    // 0x8aa898: mov             x1, x16
    // 0x8aa89c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8aa89c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8aa8a0: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8aa8a0: movz            x17, #0xd889
    //     0x8aa8a4: add             lr, x0, x17
    //     0x8aa8a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8aa8ac: blr             lr
    // 0x8aa8b0: LeaveFrame
    //     0x8aa8b0: mov             SP, fp
    //     0x8aa8b4: ldp             fp, lr, [SP], #0x10
    // 0x8aa8b8: ret
    //     0x8aa8b8: ret             
    // 0x8aa8bc: r1 = <Category>
    //     0x8aa8bc: ldr             x1, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aa8c0: r2 = 0
    //     0x8aa8c0: movz            x2, #0
    // 0x8aa8c4: r0 = _GrowableList()
    //     0x8aa8c4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8aa8c8: LeaveFrame
    //     0x8aa8c8: mov             SP, fp
    //     0x8aa8cc: ldp             fp, lr, [SP], #0x10
    // 0x8aa8d0: ret
    //     0x8aa8d0: ret             
    // 0x8aa8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa8d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa8d8: b               #0x8aa77c
  }
  [closure] static Category <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8aa8dc, size: 0x50
    // 0x8aa8dc: EnterFrame
    //     0x8aa8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa8e0: mov             fp, SP
    // 0x8aa8e4: CheckStackOverflow
    //     0x8aa8e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa8e8: cmp             SP, x16
    //     0x8aa8ec: b.ls            #0x8aa924
    // 0x8aa8f0: ldr             x0, [fp, #0x10]
    // 0x8aa8f4: r2 = Null
    //     0x8aa8f4: mov             x2, NULL
    // 0x8aa8f8: r1 = Null
    //     0x8aa8f8: mov             x1, NULL
    // 0x8aa8fc: r8 = Map<String, dynamic>
    //     0x8aa8fc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8aa900: r3 = Null
    //     0x8aa900: add             x3, PP, #0x36, lsl #12  ; [pp+0x36410] Null
    //     0x8aa904: ldr             x3, [x3, #0x410]
    // 0x8aa908: r0 = Map<String, dynamic>()
    //     0x8aa908: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8aa90c: ldr             x2, [fp, #0x10]
    // 0x8aa910: r1 = Null
    //     0x8aa910: mov             x1, NULL
    // 0x8aa914: r0 = Category.fromMap()
    //     0x8aa914: bl              #0x8aa92c  ; [package:nuonline/app/data/models/category.dart] Category::Category.fromMap
    // 0x8aa918: LeaveFrame
    //     0x8aa918: mov             SP, fp
    //     0x8aa91c: ldp             fp, lr, [SP], #0x10
    // 0x8aa920: ret
    //     0x8aa920: ret             
    // 0x8aa924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa924: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa928: b               #0x8aa8f0
  }
  factory _ Category.fromMap(/* No info */) {
    // ** addr: 0x8aa92c, size: 0x350
    // 0x8aa92c: EnterFrame
    //     0x8aa92c: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa930: mov             fp, SP
    // 0x8aa934: AllocStack(0x50)
    //     0x8aa934: sub             SP, SP, #0x50
    // 0x8aa938: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8aa938: mov             x3, x2
    //     0x8aa93c: stur            x2, [fp, #-8]
    // 0x8aa940: CheckStackOverflow
    //     0x8aa940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa944: cmp             SP, x16
    //     0x8aa948: b.ls            #0x8aac74
    // 0x8aa94c: r0 = LoadClassIdInstr(r3)
    //     0x8aa94c: ldur            x0, [x3, #-1]
    //     0x8aa950: ubfx            x0, x0, #0xc, #0x14
    // 0x8aa954: mov             x1, x3
    // 0x8aa958: r2 = "id"
    //     0x8aa958: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8aa95c: ldr             x2, [x2, #0x740]
    // 0x8aa960: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aa960: sub             lr, x0, #0x114
    //     0x8aa964: ldr             lr, [x21, lr, lsl #3]
    //     0x8aa968: blr             lr
    // 0x8aa96c: mov             x3, x0
    // 0x8aa970: r2 = Null
    //     0x8aa970: mov             x2, NULL
    // 0x8aa974: r1 = Null
    //     0x8aa974: mov             x1, NULL
    // 0x8aa978: stur            x3, [fp, #-0x10]
    // 0x8aa97c: branchIfSmi(r0, 0x8aa9a4)
    //     0x8aa97c: tbz             w0, #0, #0x8aa9a4
    // 0x8aa980: r4 = LoadClassIdInstr(r0)
    //     0x8aa980: ldur            x4, [x0, #-1]
    //     0x8aa984: ubfx            x4, x4, #0xc, #0x14
    // 0x8aa988: sub             x4, x4, #0x3c
    // 0x8aa98c: cmp             x4, #1
    // 0x8aa990: b.ls            #0x8aa9a4
    // 0x8aa994: r8 = int
    //     0x8aa994: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8aa998: r3 = Null
    //     0x8aa998: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d988] Null
    //     0x8aa99c: ldr             x3, [x3, #0x988]
    // 0x8aa9a0: r0 = int()
    //     0x8aa9a0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8aa9a4: ldur            x3, [fp, #-8]
    // 0x8aa9a8: r0 = LoadClassIdInstr(r3)
    //     0x8aa9a8: ldur            x0, [x3, #-1]
    //     0x8aa9ac: ubfx            x0, x0, #0xc, #0x14
    // 0x8aa9b0: mov             x1, x3
    // 0x8aa9b4: r2 = "name"
    //     0x8aa9b4: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8aa9b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aa9b8: sub             lr, x0, #0x114
    //     0x8aa9bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8aa9c0: blr             lr
    // 0x8aa9c4: mov             x3, x0
    // 0x8aa9c8: r2 = Null
    //     0x8aa9c8: mov             x2, NULL
    // 0x8aa9cc: r1 = Null
    //     0x8aa9cc: mov             x1, NULL
    // 0x8aa9d0: stur            x3, [fp, #-0x18]
    // 0x8aa9d4: r4 = 60
    //     0x8aa9d4: movz            x4, #0x3c
    // 0x8aa9d8: branchIfSmi(r0, 0x8aa9e4)
    //     0x8aa9d8: tbz             w0, #0, #0x8aa9e4
    // 0x8aa9dc: r4 = LoadClassIdInstr(r0)
    //     0x8aa9dc: ldur            x4, [x0, #-1]
    //     0x8aa9e0: ubfx            x4, x4, #0xc, #0x14
    // 0x8aa9e4: sub             x4, x4, #0x5e
    // 0x8aa9e8: cmp             x4, #1
    // 0x8aa9ec: b.ls            #0x8aaa00
    // 0x8aa9f0: r8 = String
    //     0x8aa9f0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8aa9f4: r3 = Null
    //     0x8aa9f4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d998] Null
    //     0x8aa9f8: ldr             x3, [x3, #0x998]
    // 0x8aa9fc: r0 = String()
    //     0x8aa9fc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8aaa00: ldur            x3, [fp, #-8]
    // 0x8aaa04: r0 = LoadClassIdInstr(r3)
    //     0x8aaa04: ldur            x0, [x3, #-1]
    //     0x8aaa08: ubfx            x0, x0, #0xc, #0x14
    // 0x8aaa0c: mov             x1, x3
    // 0x8aaa10: r2 = "islamic"
    //     0x8aaa10: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0x8aaa14: ldr             x2, [x2, #0xca0]
    // 0x8aaa18: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aaa18: sub             lr, x0, #0x114
    //     0x8aaa1c: ldr             lr, [x21, lr, lsl #3]
    //     0x8aaa20: blr             lr
    // 0x8aaa24: mov             x3, x0
    // 0x8aaa28: r2 = Null
    //     0x8aaa28: mov             x2, NULL
    // 0x8aaa2c: r1 = Null
    //     0x8aaa2c: mov             x1, NULL
    // 0x8aaa30: stur            x3, [fp, #-0x20]
    // 0x8aaa34: r4 = 60
    //     0x8aaa34: movz            x4, #0x3c
    // 0x8aaa38: branchIfSmi(r0, 0x8aaa44)
    //     0x8aaa38: tbz             w0, #0, #0x8aaa44
    // 0x8aaa3c: r4 = LoadClassIdInstr(r0)
    //     0x8aaa3c: ldur            x4, [x0, #-1]
    //     0x8aaa40: ubfx            x4, x4, #0xc, #0x14
    // 0x8aaa44: cmp             x4, #0x3f
    // 0x8aaa48: b.eq            #0x8aaa5c
    // 0x8aaa4c: r8 = bool?
    //     0x8aaa4c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x8aaa50: r3 = Null
    //     0x8aaa50: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d9a8] Null
    //     0x8aaa54: ldr             x3, [x3, #0x9a8]
    // 0x8aaa58: r0 = bool?()
    //     0x8aaa58: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x8aaa5c: ldur            x0, [fp, #-0x20]
    // 0x8aaa60: cmp             w0, NULL
    // 0x8aaa64: b.ne            #0x8aaa70
    // 0x8aaa68: r4 = false
    //     0x8aaa68: add             x4, NULL, #0x30  ; false
    // 0x8aaa6c: b               #0x8aaa74
    // 0x8aaa70: mov             x4, x0
    // 0x8aaa74: ldur            x3, [fp, #-8]
    // 0x8aaa78: stur            x4, [fp, #-0x20]
    // 0x8aaa7c: r0 = LoadClassIdInstr(r3)
    //     0x8aaa7c: ldur            x0, [x3, #-1]
    //     0x8aaa80: ubfx            x0, x0, #0xc, #0x14
    // 0x8aaa84: mov             x1, x3
    // 0x8aaa88: r2 = "selected"
    //     0x8aaa88: add             x2, PP, #0x22, lsl #12  ; [pp+0x222f0] "selected"
    //     0x8aaa8c: ldr             x2, [x2, #0x2f0]
    // 0x8aaa90: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aaa90: sub             lr, x0, #0x114
    //     0x8aaa94: ldr             lr, [x21, lr, lsl #3]
    //     0x8aaa98: blr             lr
    // 0x8aaa9c: mov             x3, x0
    // 0x8aaaa0: r2 = Null
    //     0x8aaaa0: mov             x2, NULL
    // 0x8aaaa4: r1 = Null
    //     0x8aaaa4: mov             x1, NULL
    // 0x8aaaa8: stur            x3, [fp, #-0x28]
    // 0x8aaaac: r4 = 60
    //     0x8aaaac: movz            x4, #0x3c
    // 0x8aaab0: branchIfSmi(r0, 0x8aaabc)
    //     0x8aaab0: tbz             w0, #0, #0x8aaabc
    // 0x8aaab4: r4 = LoadClassIdInstr(r0)
    //     0x8aaab4: ldur            x4, [x0, #-1]
    //     0x8aaab8: ubfx            x4, x4, #0xc, #0x14
    // 0x8aaabc: cmp             x4, #0x3f
    // 0x8aaac0: b.eq            #0x8aaad4
    // 0x8aaac4: r8 = bool?
    //     0x8aaac4: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x8aaac8: r3 = Null
    //     0x8aaac8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d9b8] Null
    //     0x8aaacc: ldr             x3, [x3, #0x9b8]
    // 0x8aaad0: r0 = bool?()
    //     0x8aaad0: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x8aaad4: ldur            x0, [fp, #-0x28]
    // 0x8aaad8: cmp             w0, NULL
    // 0x8aaadc: b.ne            #0x8aaae8
    // 0x8aaae0: r4 = false
    //     0x8aaae0: add             x4, NULL, #0x30  ; false
    // 0x8aaae4: b               #0x8aaaec
    // 0x8aaae8: mov             x4, x0
    // 0x8aaaec: ldur            x3, [fp, #-8]
    // 0x8aaaf0: stur            x4, [fp, #-0x28]
    // 0x8aaaf4: r0 = LoadClassIdInstr(r3)
    //     0x8aaaf4: ldur            x0, [x3, #-1]
    //     0x8aaaf8: ubfx            x0, x0, #0xc, #0x14
    // 0x8aaafc: mov             x1, x3
    // 0x8aab00: r2 = "order"
    //     0x8aab00: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x8aab04: ldr             x2, [x2, #0xb78]
    // 0x8aab08: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aab08: sub             lr, x0, #0x114
    //     0x8aab0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8aab10: blr             lr
    // 0x8aab14: mov             x3, x0
    // 0x8aab18: r2 = Null
    //     0x8aab18: mov             x2, NULL
    // 0x8aab1c: r1 = Null
    //     0x8aab1c: mov             x1, NULL
    // 0x8aab20: stur            x3, [fp, #-0x30]
    // 0x8aab24: branchIfSmi(r0, 0x8aab4c)
    //     0x8aab24: tbz             w0, #0, #0x8aab4c
    // 0x8aab28: r4 = LoadClassIdInstr(r0)
    //     0x8aab28: ldur            x4, [x0, #-1]
    //     0x8aab2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8aab30: sub             x4, x4, #0x3c
    // 0x8aab34: cmp             x4, #1
    // 0x8aab38: b.ls            #0x8aab4c
    // 0x8aab3c: r8 = int?
    //     0x8aab3c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x8aab40: r3 = Null
    //     0x8aab40: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d9c8] Null
    //     0x8aab44: ldr             x3, [x3, #0x9c8]
    // 0x8aab48: r0 = int?()
    //     0x8aab48: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x8aab4c: ldur            x0, [fp, #-0x30]
    // 0x8aab50: cmp             w0, NULL
    // 0x8aab54: b.ne            #0x8aabc8
    // 0x8aab58: ldur            x1, [fp, #-8]
    // 0x8aab5c: r0 = LoadClassIdInstr(r1)
    //     0x8aab5c: ldur            x0, [x1, #-1]
    //     0x8aab60: ubfx            x0, x0, #0xc, #0x14
    // 0x8aab64: r2 = "id"
    //     0x8aab64: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8aab68: ldr             x2, [x2, #0x740]
    // 0x8aab6c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aab6c: sub             lr, x0, #0x114
    //     0x8aab70: ldr             lr, [x21, lr, lsl #3]
    //     0x8aab74: blr             lr
    // 0x8aab78: mov             x3, x0
    // 0x8aab7c: r2 = Null
    //     0x8aab7c: mov             x2, NULL
    // 0x8aab80: r1 = Null
    //     0x8aab80: mov             x1, NULL
    // 0x8aab84: stur            x3, [fp, #-8]
    // 0x8aab88: branchIfSmi(r0, 0x8aabb0)
    //     0x8aab88: tbz             w0, #0, #0x8aabb0
    // 0x8aab8c: r4 = LoadClassIdInstr(r0)
    //     0x8aab8c: ldur            x4, [x0, #-1]
    //     0x8aab90: ubfx            x4, x4, #0xc, #0x14
    // 0x8aab94: sub             x4, x4, #0x3c
    // 0x8aab98: cmp             x4, #1
    // 0x8aab9c: b.ls            #0x8aabb0
    // 0x8aaba0: r8 = int
    //     0x8aaba0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8aaba4: r3 = Null
    //     0x8aaba4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d9d8] Null
    //     0x8aaba8: ldr             x3, [x3, #0x9d8]
    // 0x8aabac: r0 = int()
    //     0x8aabac: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8aabb0: ldur            x0, [fp, #-8]
    // 0x8aabb4: r1 = LoadInt32Instr(r0)
    //     0x8aabb4: sbfx            x1, x0, #1, #0x1f
    //     0x8aabb8: tbz             w0, #0, #0x8aabc0
    //     0x8aabbc: ldur            x1, [x0, #7]
    // 0x8aabc0: mov             x4, x1
    // 0x8aabc4: b               #0x8aabd8
    // 0x8aabc8: r1 = LoadInt32Instr(r0)
    //     0x8aabc8: sbfx            x1, x0, #1, #0x1f
    //     0x8aabcc: tbz             w0, #0, #0x8aabd4
    //     0x8aabd0: ldur            x1, [x0, #7]
    // 0x8aabd4: mov             x4, x1
    // 0x8aabd8: ldur            x3, [fp, #-0x10]
    // 0x8aabdc: ldur            x2, [fp, #-0x18]
    // 0x8aabe0: ldur            x1, [fp, #-0x20]
    // 0x8aabe4: ldur            x0, [fp, #-0x28]
    // 0x8aabe8: stur            x4, [fp, #-0x40]
    // 0x8aabec: r5 = LoadInt32Instr(r3)
    //     0x8aabec: sbfx            x5, x3, #1, #0x1f
    //     0x8aabf0: tbz             w3, #0, #0x8aabf8
    //     0x8aabf4: ldur            x5, [x3, #7]
    // 0x8aabf8: stur            x5, [fp, #-0x38]
    // 0x8aabfc: r0 = Category()
    //     0x8aabfc: bl              #0x8aac7c  ; AllocateCategoryStub -> Category (size=0x30)
    // 0x8aac00: mov             x1, x0
    // 0x8aac04: ldur            x0, [fp, #-0x38]
    // 0x8aac08: stur            x1, [fp, #-8]
    // 0x8aac0c: StoreField: r1->field_13 = r0
    //     0x8aac0c: stur            x0, [x1, #0x13]
    // 0x8aac10: ldur            x0, [fp, #-0x18]
    // 0x8aac14: StoreField: r1->field_1b = r0
    //     0x8aac14: stur            w0, [x1, #0x1b]
    // 0x8aac18: ldur            x0, [fp, #-0x20]
    // 0x8aac1c: StoreField: r1->field_1f = r0
    //     0x8aac1c: stur            w0, [x1, #0x1f]
    // 0x8aac20: ldur            x0, [fp, #-0x40]
    // 0x8aac24: StoreField: r1->field_27 = r0
    //     0x8aac24: stur            x0, [x1, #0x27]
    // 0x8aac28: ldur            x0, [fp, #-0x28]
    // 0x8aac2c: StoreField: r1->field_23 = r0
    //     0x8aac2c: stur            w0, [x1, #0x23]
    // 0x8aac30: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8aac30: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8aac34: ldr             x16, [x16, #0x9f8]
    // 0x8aac38: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8aac3c: stp             lr, x16, [SP]
    // 0x8aac40: r0 = Map._fromLiteral()
    //     0x8aac40: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8aac44: ldur            x1, [fp, #-8]
    // 0x8aac48: StoreField: r1->field_f = r0
    //     0x8aac48: stur            w0, [x1, #0xf]
    //     0x8aac4c: ldurb           w16, [x1, #-1]
    //     0x8aac50: ldurb           w17, [x0, #-1]
    //     0x8aac54: and             x16, x17, x16, lsr #2
    //     0x8aac58: tst             x16, HEAP, lsr #32
    //     0x8aac5c: b.eq            #0x8aac64
    //     0x8aac60: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8aac64: mov             x0, x1
    // 0x8aac68: LeaveFrame
    //     0x8aac68: mov             SP, fp
    //     0x8aac6c: ldp             fp, lr, [SP], #0x10
    // 0x8aac70: ret
    //     0x8aac70: ret             
    // 0x8aac74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aac74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aac78: b               #0x8aa94c
  }
  _ copyWith(/* No info */) {
    // ** addr: 0x8ab0f0, size: 0xe0
    // 0x8ab0f0: EnterFrame
    //     0x8ab0f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab0f4: mov             fp, SP
    // 0x8ab0f8: AllocStack(0x40)
    //     0x8ab0f8: sub             SP, SP, #0x40
    // 0x8ab0fc: SetupParameters(dynamic _ /* r3 => r3, fp-0x28 */)
    //     0x8ab0fc: stur            x3, [fp, #-0x28]
    // 0x8ab100: CheckStackOverflow
    //     0x8ab100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab104: cmp             SP, x16
    //     0x8ab108: b.ls            #0x8ab1c8
    // 0x8ab10c: LoadField: r0 = r1->field_13
    //     0x8ab10c: ldur            x0, [x1, #0x13]
    // 0x8ab110: stur            x0, [fp, #-0x20]
    // 0x8ab114: LoadField: r4 = r1->field_1b
    //     0x8ab114: ldur            w4, [x1, #0x1b]
    // 0x8ab118: DecompressPointer r4
    //     0x8ab118: add             x4, x4, HEAP, lsl #32
    // 0x8ab11c: stur            x4, [fp, #-0x18]
    // 0x8ab120: LoadField: r5 = r1->field_1f
    //     0x8ab120: ldur            w5, [x1, #0x1f]
    // 0x8ab124: DecompressPointer r5
    //     0x8ab124: add             x5, x5, HEAP, lsl #32
    // 0x8ab128: stur            x5, [fp, #-0x10]
    // 0x8ab12c: cmp             w2, NULL
    // 0x8ab130: b.ne            #0x8ab140
    // 0x8ab134: LoadField: r2 = r1->field_27
    //     0x8ab134: ldur            x2, [x1, #0x27]
    // 0x8ab138: mov             x1, x2
    // 0x8ab13c: b               #0x8ab14c
    // 0x8ab140: r1 = LoadInt32Instr(r2)
    //     0x8ab140: sbfx            x1, x2, #1, #0x1f
    //     0x8ab144: tbz             w2, #0, #0x8ab14c
    //     0x8ab148: ldur            x1, [x2, #7]
    // 0x8ab14c: stur            x1, [fp, #-8]
    // 0x8ab150: r0 = Category()
    //     0x8ab150: bl              #0x8aac7c  ; AllocateCategoryStub -> Category (size=0x30)
    // 0x8ab154: mov             x1, x0
    // 0x8ab158: ldur            x0, [fp, #-0x20]
    // 0x8ab15c: stur            x1, [fp, #-0x30]
    // 0x8ab160: StoreField: r1->field_13 = r0
    //     0x8ab160: stur            x0, [x1, #0x13]
    // 0x8ab164: ldur            x0, [fp, #-0x18]
    // 0x8ab168: StoreField: r1->field_1b = r0
    //     0x8ab168: stur            w0, [x1, #0x1b]
    // 0x8ab16c: ldur            x0, [fp, #-0x10]
    // 0x8ab170: StoreField: r1->field_1f = r0
    //     0x8ab170: stur            w0, [x1, #0x1f]
    // 0x8ab174: ldur            x0, [fp, #-8]
    // 0x8ab178: StoreField: r1->field_27 = r0
    //     0x8ab178: stur            x0, [x1, #0x27]
    // 0x8ab17c: ldur            x0, [fp, #-0x28]
    // 0x8ab180: StoreField: r1->field_23 = r0
    //     0x8ab180: stur            w0, [x1, #0x23]
    // 0x8ab184: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8ab184: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8ab188: ldr             x16, [x16, #0x9f8]
    // 0x8ab18c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8ab190: stp             lr, x16, [SP]
    // 0x8ab194: r0 = Map._fromLiteral()
    //     0x8ab194: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8ab198: ldur            x1, [fp, #-0x30]
    // 0x8ab19c: StoreField: r1->field_f = r0
    //     0x8ab19c: stur            w0, [x1, #0xf]
    //     0x8ab1a0: ldurb           w16, [x1, #-1]
    //     0x8ab1a4: ldurb           w17, [x0, #-1]
    //     0x8ab1a8: and             x16, x17, x16, lsr #2
    //     0x8ab1ac: tst             x16, HEAP, lsr #32
    //     0x8ab1b0: b.eq            #0x8ab1b8
    //     0x8ab1b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ab1b8: mov             x0, x1
    // 0x8ab1bc: LeaveFrame
    //     0x8ab1bc: mov             SP, fp
    //     0x8ab1c0: ldp             fp, lr, [SP], #0x10
    // 0x8ab1c4: ret
    //     0x8ab1c4: ret             
    // 0x8ab1c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab1c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab1cc: b               #0x8ab10c
  }
}

// class id: 1667, size: 0x14, field offset: 0xc
class CategoryAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5f774, size: 0x450
    // 0xa5f774: EnterFrame
    //     0xa5f774: stp             fp, lr, [SP, #-0x10]!
    //     0xa5f778: mov             fp, SP
    // 0xa5f77c: AllocStack(0x58)
    //     0xa5f77c: sub             SP, SP, #0x58
    // 0xa5f780: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5f780: stur            x2, [fp, #-0x20]
    // 0xa5f784: CheckStackOverflow
    //     0xa5f784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5f788: cmp             SP, x16
    //     0xa5f78c: b.ls            #0xa5fbac
    // 0xa5f790: LoadField: r3 = r2->field_23
    //     0xa5f790: ldur            x3, [x2, #0x23]
    // 0xa5f794: add             x0, x3, #1
    // 0xa5f798: LoadField: r1 = r2->field_1b
    //     0xa5f798: ldur            x1, [x2, #0x1b]
    // 0xa5f79c: cmp             x0, x1
    // 0xa5f7a0: b.gt            #0xa5fb50
    // 0xa5f7a4: LoadField: r4 = r2->field_7
    //     0xa5f7a4: ldur            w4, [x2, #7]
    // 0xa5f7a8: DecompressPointer r4
    //     0xa5f7a8: add             x4, x4, HEAP, lsl #32
    // 0xa5f7ac: stur            x4, [fp, #-0x18]
    // 0xa5f7b0: StoreField: r2->field_23 = r0
    //     0xa5f7b0: stur            x0, [x2, #0x23]
    // 0xa5f7b4: LoadField: r0 = r4->field_13
    //     0xa5f7b4: ldur            w0, [x4, #0x13]
    // 0xa5f7b8: r5 = LoadInt32Instr(r0)
    //     0xa5f7b8: sbfx            x5, x0, #1, #0x1f
    // 0xa5f7bc: mov             x0, x5
    // 0xa5f7c0: mov             x1, x3
    // 0xa5f7c4: stur            x5, [fp, #-0x10]
    // 0xa5f7c8: cmp             x1, x0
    // 0xa5f7cc: b.hs            #0xa5fbb4
    // 0xa5f7d0: LoadField: r0 = r4->field_7
    //     0xa5f7d0: ldur            x0, [x4, #7]
    // 0xa5f7d4: ldrb            w1, [x0, x3]
    // 0xa5f7d8: stur            x1, [fp, #-8]
    // 0xa5f7dc: r16 = <int, dynamic>
    //     0xa5f7dc: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5f7e0: ldr             x16, [x16, #0xac0]
    // 0xa5f7e4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5f7e8: stp             lr, x16, [SP]
    // 0xa5f7ec: r0 = Map._fromLiteral()
    //     0xa5f7ec: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5f7f0: mov             x2, x0
    // 0xa5f7f4: stur            x2, [fp, #-0x38]
    // 0xa5f7f8: r6 = 0
    //     0xa5f7f8: movz            x6, #0
    // 0xa5f7fc: ldur            x3, [fp, #-0x20]
    // 0xa5f800: ldur            x4, [fp, #-0x18]
    // 0xa5f804: ldur            x5, [fp, #-8]
    // 0xa5f808: stur            x6, [fp, #-0x30]
    // 0xa5f80c: CheckStackOverflow
    //     0xa5f80c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5f810: cmp             SP, x16
    //     0xa5f814: b.ls            #0xa5fbb8
    // 0xa5f818: cmp             x6, x5
    // 0xa5f81c: b.ge            #0xa5f8a8
    // 0xa5f820: LoadField: r7 = r3->field_23
    //     0xa5f820: ldur            x7, [x3, #0x23]
    // 0xa5f824: add             x0, x7, #1
    // 0xa5f828: LoadField: r1 = r3->field_1b
    //     0xa5f828: ldur            x1, [x3, #0x1b]
    // 0xa5f82c: cmp             x0, x1
    // 0xa5f830: b.gt            #0xa5fb78
    // 0xa5f834: StoreField: r3->field_23 = r0
    //     0xa5f834: stur            x0, [x3, #0x23]
    // 0xa5f838: ldur            x0, [fp, #-0x10]
    // 0xa5f83c: mov             x1, x7
    // 0xa5f840: cmp             x1, x0
    // 0xa5f844: b.hs            #0xa5fbc0
    // 0xa5f848: LoadField: r0 = r4->field_7
    //     0xa5f848: ldur            x0, [x4, #7]
    // 0xa5f84c: ldrb            w8, [x0, x7]
    // 0xa5f850: mov             x1, x3
    // 0xa5f854: stur            x8, [fp, #-0x28]
    // 0xa5f858: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5f858: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5f85c: r0 = read()
    //     0xa5f85c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5f860: mov             x1, x0
    // 0xa5f864: ldur            x0, [fp, #-0x28]
    // 0xa5f868: lsl             x2, x0, #1
    // 0xa5f86c: r16 = LoadInt32Instr(r2)
    //     0xa5f86c: sbfx            x16, x2, #1, #0x1f
    // 0xa5f870: r17 = 11601
    //     0xa5f870: movz            x17, #0x2d51
    // 0xa5f874: mul             x0, x16, x17
    // 0xa5f878: umulh           x16, x16, x17
    // 0xa5f87c: eor             x0, x0, x16
    // 0xa5f880: r0 = 0
    //     0xa5f880: eor             x0, x0, x0, lsr #32
    // 0xa5f884: ubfiz           x0, x0, #1, #0x1e
    // 0xa5f888: r5 = LoadInt32Instr(r0)
    //     0xa5f888: sbfx            x5, x0, #1, #0x1f
    // 0xa5f88c: mov             x3, x1
    // 0xa5f890: ldur            x1, [fp, #-0x38]
    // 0xa5f894: r0 = _set()
    //     0xa5f894: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5f898: ldur            x0, [fp, #-0x30]
    // 0xa5f89c: add             x6, x0, #1
    // 0xa5f8a0: ldur            x2, [fp, #-0x38]
    // 0xa5f8a4: b               #0xa5f7fc
    // 0xa5f8a8: mov             x0, x2
    // 0xa5f8ac: mov             x1, x0
    // 0xa5f8b0: r2 = 0
    //     0xa5f8b0: movz            x2, #0
    // 0xa5f8b4: r0 = _getValueOrData()
    //     0xa5f8b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f8b8: ldur            x3, [fp, #-0x38]
    // 0xa5f8bc: LoadField: r1 = r3->field_f
    //     0xa5f8bc: ldur            w1, [x3, #0xf]
    // 0xa5f8c0: DecompressPointer r1
    //     0xa5f8c0: add             x1, x1, HEAP, lsl #32
    // 0xa5f8c4: cmp             w1, w0
    // 0xa5f8c8: b.ne            #0xa5f8d4
    // 0xa5f8cc: r4 = Null
    //     0xa5f8cc: mov             x4, NULL
    // 0xa5f8d0: b               #0xa5f8d8
    // 0xa5f8d4: mov             x4, x0
    // 0xa5f8d8: mov             x0, x4
    // 0xa5f8dc: stur            x4, [fp, #-0x18]
    // 0xa5f8e0: r2 = Null
    //     0xa5f8e0: mov             x2, NULL
    // 0xa5f8e4: r1 = Null
    //     0xa5f8e4: mov             x1, NULL
    // 0xa5f8e8: branchIfSmi(r0, 0xa5f910)
    //     0xa5f8e8: tbz             w0, #0, #0xa5f910
    // 0xa5f8ec: r4 = LoadClassIdInstr(r0)
    //     0xa5f8ec: ldur            x4, [x0, #-1]
    //     0xa5f8f0: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f8f4: sub             x4, x4, #0x3c
    // 0xa5f8f8: cmp             x4, #1
    // 0xa5f8fc: b.ls            #0xa5f910
    // 0xa5f900: r8 = int
    //     0xa5f900: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5f904: r3 = Null
    //     0xa5f904: add             x3, PP, #0x21, lsl #12  ; [pp+0x21718] Null
    //     0xa5f908: ldr             x3, [x3, #0x718]
    // 0xa5f90c: r0 = int()
    //     0xa5f90c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5f910: ldur            x1, [fp, #-0x38]
    // 0xa5f914: r2 = 2
    //     0xa5f914: movz            x2, #0x2
    // 0xa5f918: r0 = _getValueOrData()
    //     0xa5f918: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f91c: ldur            x3, [fp, #-0x38]
    // 0xa5f920: LoadField: r1 = r3->field_f
    //     0xa5f920: ldur            w1, [x3, #0xf]
    // 0xa5f924: DecompressPointer r1
    //     0xa5f924: add             x1, x1, HEAP, lsl #32
    // 0xa5f928: cmp             w1, w0
    // 0xa5f92c: b.ne            #0xa5f938
    // 0xa5f930: r4 = Null
    //     0xa5f930: mov             x4, NULL
    // 0xa5f934: b               #0xa5f93c
    // 0xa5f938: mov             x4, x0
    // 0xa5f93c: mov             x0, x4
    // 0xa5f940: stur            x4, [fp, #-0x20]
    // 0xa5f944: r2 = Null
    //     0xa5f944: mov             x2, NULL
    // 0xa5f948: r1 = Null
    //     0xa5f948: mov             x1, NULL
    // 0xa5f94c: r4 = 60
    //     0xa5f94c: movz            x4, #0x3c
    // 0xa5f950: branchIfSmi(r0, 0xa5f95c)
    //     0xa5f950: tbz             w0, #0, #0xa5f95c
    // 0xa5f954: r4 = LoadClassIdInstr(r0)
    //     0xa5f954: ldur            x4, [x0, #-1]
    //     0xa5f958: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f95c: sub             x4, x4, #0x5e
    // 0xa5f960: cmp             x4, #1
    // 0xa5f964: b.ls            #0xa5f978
    // 0xa5f968: r8 = String
    //     0xa5f968: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5f96c: r3 = Null
    //     0xa5f96c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21728] Null
    //     0xa5f970: ldr             x3, [x3, #0x728]
    // 0xa5f974: r0 = String()
    //     0xa5f974: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5f978: ldur            x1, [fp, #-0x38]
    // 0xa5f97c: r2 = 4
    //     0xa5f97c: movz            x2, #0x4
    // 0xa5f980: r0 = _getValueOrData()
    //     0xa5f980: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f984: ldur            x3, [fp, #-0x38]
    // 0xa5f988: LoadField: r1 = r3->field_f
    //     0xa5f988: ldur            w1, [x3, #0xf]
    // 0xa5f98c: DecompressPointer r1
    //     0xa5f98c: add             x1, x1, HEAP, lsl #32
    // 0xa5f990: cmp             w1, w0
    // 0xa5f994: b.ne            #0xa5f9a0
    // 0xa5f998: r4 = Null
    //     0xa5f998: mov             x4, NULL
    // 0xa5f99c: b               #0xa5f9a4
    // 0xa5f9a0: mov             x4, x0
    // 0xa5f9a4: mov             x0, x4
    // 0xa5f9a8: stur            x4, [fp, #-0x40]
    // 0xa5f9ac: r2 = Null
    //     0xa5f9ac: mov             x2, NULL
    // 0xa5f9b0: r1 = Null
    //     0xa5f9b0: mov             x1, NULL
    // 0xa5f9b4: r4 = 60
    //     0xa5f9b4: movz            x4, #0x3c
    // 0xa5f9b8: branchIfSmi(r0, 0xa5f9c4)
    //     0xa5f9b8: tbz             w0, #0, #0xa5f9c4
    // 0xa5f9bc: r4 = LoadClassIdInstr(r0)
    //     0xa5f9bc: ldur            x4, [x0, #-1]
    //     0xa5f9c0: ubfx            x4, x4, #0xc, #0x14
    // 0xa5f9c4: cmp             x4, #0x3f
    // 0xa5f9c8: b.eq            #0xa5f9dc
    // 0xa5f9cc: r8 = bool
    //     0xa5f9cc: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa5f9d0: r3 = Null
    //     0xa5f9d0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21738] Null
    //     0xa5f9d4: ldr             x3, [x3, #0x738]
    // 0xa5f9d8: r0 = bool()
    //     0xa5f9d8: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa5f9dc: ldur            x1, [fp, #-0x38]
    // 0xa5f9e0: r2 = 8
    //     0xa5f9e0: movz            x2, #0x8
    // 0xa5f9e4: r0 = _getValueOrData()
    //     0xa5f9e4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5f9e8: ldur            x3, [fp, #-0x38]
    // 0xa5f9ec: LoadField: r1 = r3->field_f
    //     0xa5f9ec: ldur            w1, [x3, #0xf]
    // 0xa5f9f0: DecompressPointer r1
    //     0xa5f9f0: add             x1, x1, HEAP, lsl #32
    // 0xa5f9f4: cmp             w1, w0
    // 0xa5f9f8: b.ne            #0xa5fa04
    // 0xa5f9fc: r4 = Null
    //     0xa5f9fc: mov             x4, NULL
    // 0xa5fa00: b               #0xa5fa08
    // 0xa5fa04: mov             x4, x0
    // 0xa5fa08: mov             x0, x4
    // 0xa5fa0c: stur            x4, [fp, #-0x48]
    // 0xa5fa10: r2 = Null
    //     0xa5fa10: mov             x2, NULL
    // 0xa5fa14: r1 = Null
    //     0xa5fa14: mov             x1, NULL
    // 0xa5fa18: branchIfSmi(r0, 0xa5fa40)
    //     0xa5fa18: tbz             w0, #0, #0xa5fa40
    // 0xa5fa1c: r4 = LoadClassIdInstr(r0)
    //     0xa5fa1c: ldur            x4, [x0, #-1]
    //     0xa5fa20: ubfx            x4, x4, #0xc, #0x14
    // 0xa5fa24: sub             x4, x4, #0x3c
    // 0xa5fa28: cmp             x4, #1
    // 0xa5fa2c: b.ls            #0xa5fa40
    // 0xa5fa30: r8 = int
    //     0xa5fa30: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa5fa34: r3 = Null
    //     0xa5fa34: add             x3, PP, #0x21, lsl #12  ; [pp+0x21748] Null
    //     0xa5fa38: ldr             x3, [x3, #0x748]
    // 0xa5fa3c: r0 = int()
    //     0xa5fa3c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa5fa40: ldur            x1, [fp, #-0x38]
    // 0xa5fa44: r2 = 6
    //     0xa5fa44: movz            x2, #0x6
    // 0xa5fa48: r0 = _getValueOrData()
    //     0xa5fa48: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fa4c: mov             x1, x0
    // 0xa5fa50: ldur            x0, [fp, #-0x38]
    // 0xa5fa54: LoadField: r2 = r0->field_f
    //     0xa5fa54: ldur            w2, [x0, #0xf]
    // 0xa5fa58: DecompressPointer r2
    //     0xa5fa58: add             x2, x2, HEAP, lsl #32
    // 0xa5fa5c: cmp             w2, w1
    // 0xa5fa60: b.ne            #0xa5fa6c
    // 0xa5fa64: r7 = Null
    //     0xa5fa64: mov             x7, NULL
    // 0xa5fa68: b               #0xa5fa70
    // 0xa5fa6c: mov             x7, x1
    // 0xa5fa70: ldur            x6, [fp, #-0x18]
    // 0xa5fa74: ldur            x5, [fp, #-0x20]
    // 0xa5fa78: ldur            x4, [fp, #-0x40]
    // 0xa5fa7c: ldur            x3, [fp, #-0x48]
    // 0xa5fa80: mov             x0, x7
    // 0xa5fa84: stur            x7, [fp, #-0x38]
    // 0xa5fa88: r2 = Null
    //     0xa5fa88: mov             x2, NULL
    // 0xa5fa8c: r1 = Null
    //     0xa5fa8c: mov             x1, NULL
    // 0xa5fa90: r4 = 60
    //     0xa5fa90: movz            x4, #0x3c
    // 0xa5fa94: branchIfSmi(r0, 0xa5faa0)
    //     0xa5fa94: tbz             w0, #0, #0xa5faa0
    // 0xa5fa98: r4 = LoadClassIdInstr(r0)
    //     0xa5fa98: ldur            x4, [x0, #-1]
    //     0xa5fa9c: ubfx            x4, x4, #0xc, #0x14
    // 0xa5faa0: cmp             x4, #0x3f
    // 0xa5faa4: b.eq            #0xa5fab8
    // 0xa5faa8: r8 = bool
    //     0xa5faa8: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa5faac: r3 = Null
    //     0xa5faac: add             x3, PP, #0x21, lsl #12  ; [pp+0x21758] Null
    //     0xa5fab0: ldr             x3, [x3, #0x758]
    // 0xa5fab4: r0 = bool()
    //     0xa5fab4: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa5fab8: ldur            x0, [fp, #-0x18]
    // 0xa5fabc: r1 = LoadInt32Instr(r0)
    //     0xa5fabc: sbfx            x1, x0, #1, #0x1f
    //     0xa5fac0: tbz             w0, #0, #0xa5fac8
    //     0xa5fac4: ldur            x1, [x0, #7]
    // 0xa5fac8: stur            x1, [fp, #-8]
    // 0xa5facc: r0 = Category()
    //     0xa5facc: bl              #0x8aac7c  ; AllocateCategoryStub -> Category (size=0x30)
    // 0xa5fad0: mov             x1, x0
    // 0xa5fad4: ldur            x0, [fp, #-8]
    // 0xa5fad8: stur            x1, [fp, #-0x18]
    // 0xa5fadc: StoreField: r1->field_13 = r0
    //     0xa5fadc: stur            x0, [x1, #0x13]
    // 0xa5fae0: ldur            x0, [fp, #-0x20]
    // 0xa5fae4: StoreField: r1->field_1b = r0
    //     0xa5fae4: stur            w0, [x1, #0x1b]
    // 0xa5fae8: ldur            x0, [fp, #-0x40]
    // 0xa5faec: StoreField: r1->field_1f = r0
    //     0xa5faec: stur            w0, [x1, #0x1f]
    // 0xa5faf0: ldur            x0, [fp, #-0x48]
    // 0xa5faf4: r2 = LoadInt32Instr(r0)
    //     0xa5faf4: sbfx            x2, x0, #1, #0x1f
    //     0xa5faf8: tbz             w0, #0, #0xa5fb00
    //     0xa5fafc: ldur            x2, [x0, #7]
    // 0xa5fb00: StoreField: r1->field_27 = r2
    //     0xa5fb00: stur            x2, [x1, #0x27]
    // 0xa5fb04: ldur            x0, [fp, #-0x38]
    // 0xa5fb08: StoreField: r1->field_23 = r0
    //     0xa5fb08: stur            w0, [x1, #0x23]
    // 0xa5fb0c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa5fb0c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa5fb10: ldr             x16, [x16, #0x9f8]
    // 0xa5fb14: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5fb18: stp             lr, x16, [SP]
    // 0xa5fb1c: r0 = Map._fromLiteral()
    //     0xa5fb1c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5fb20: ldur            x1, [fp, #-0x18]
    // 0xa5fb24: StoreField: r1->field_f = r0
    //     0xa5fb24: stur            w0, [x1, #0xf]
    //     0xa5fb28: ldurb           w16, [x1, #-1]
    //     0xa5fb2c: ldurb           w17, [x0, #-1]
    //     0xa5fb30: and             x16, x17, x16, lsr #2
    //     0xa5fb34: tst             x16, HEAP, lsr #32
    //     0xa5fb38: b.eq            #0xa5fb40
    //     0xa5fb3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa5fb40: mov             x0, x1
    // 0xa5fb44: LeaveFrame
    //     0xa5fb44: mov             SP, fp
    //     0xa5fb48: ldp             fp, lr, [SP], #0x10
    // 0xa5fb4c: ret
    //     0xa5fb4c: ret             
    // 0xa5fb50: r0 = RangeError()
    //     0xa5fb50: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5fb54: mov             x1, x0
    // 0xa5fb58: r0 = "Not enough bytes available."
    //     0xa5fb58: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5fb5c: ldr             x0, [x0, #0x8a8]
    // 0xa5fb60: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5fb60: stur            w0, [x1, #0x17]
    // 0xa5fb64: r2 = false
    //     0xa5fb64: add             x2, NULL, #0x30  ; false
    // 0xa5fb68: StoreField: r1->field_b = r2
    //     0xa5fb68: stur            w2, [x1, #0xb]
    // 0xa5fb6c: mov             x0, x1
    // 0xa5fb70: r0 = Throw()
    //     0xa5fb70: bl              #0xec04b8  ; ThrowStub
    // 0xa5fb74: brk             #0
    // 0xa5fb78: r0 = "Not enough bytes available."
    //     0xa5fb78: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5fb7c: ldr             x0, [x0, #0x8a8]
    // 0xa5fb80: r2 = false
    //     0xa5fb80: add             x2, NULL, #0x30  ; false
    // 0xa5fb84: r0 = RangeError()
    //     0xa5fb84: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa5fb88: mov             x1, x0
    // 0xa5fb8c: r0 = "Not enough bytes available."
    //     0xa5fb8c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa5fb90: ldr             x0, [x0, #0x8a8]
    // 0xa5fb94: ArrayStore: r1[0] = r0  ; List_4
    //     0xa5fb94: stur            w0, [x1, #0x17]
    // 0xa5fb98: r0 = false
    //     0xa5fb98: add             x0, NULL, #0x30  ; false
    // 0xa5fb9c: StoreField: r1->field_b = r0
    //     0xa5fb9c: stur            w0, [x1, #0xb]
    // 0xa5fba0: mov             x0, x1
    // 0xa5fba4: r0 = Throw()
    //     0xa5fba4: bl              #0xec04b8  ; ThrowStub
    // 0xa5fba8: brk             #0
    // 0xa5fbac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5fbac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5fbb0: b               #0xa5f790
    // 0xa5fbb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5fbb4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa5fbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5fbb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5fbbc: b               #0xa5f818
    // 0xa5fbc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa5fbc0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd0d7c, size: 0x3b8
    // 0xbd0d7c: EnterFrame
    //     0xbd0d7c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd0d80: mov             fp, SP
    // 0xbd0d84: AllocStack(0x28)
    //     0xbd0d84: sub             SP, SP, #0x28
    // 0xbd0d88: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd0d88: mov             x4, x2
    //     0xbd0d8c: stur            x2, [fp, #-8]
    //     0xbd0d90: stur            x3, [fp, #-0x10]
    // 0xbd0d94: CheckStackOverflow
    //     0xbd0d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd0d98: cmp             SP, x16
    //     0xbd0d9c: b.ls            #0xbd1114
    // 0xbd0da0: mov             x0, x3
    // 0xbd0da4: r2 = Null
    //     0xbd0da4: mov             x2, NULL
    // 0xbd0da8: r1 = Null
    //     0xbd0da8: mov             x1, NULL
    // 0xbd0dac: r4 = 60
    //     0xbd0dac: movz            x4, #0x3c
    // 0xbd0db0: branchIfSmi(r0, 0xbd0dbc)
    //     0xbd0db0: tbz             w0, #0, #0xbd0dbc
    // 0xbd0db4: r4 = LoadClassIdInstr(r0)
    //     0xbd0db4: ldur            x4, [x0, #-1]
    //     0xbd0db8: ubfx            x4, x4, #0xc, #0x14
    // 0xbd0dbc: cmp             x4, #0x649
    // 0xbd0dc0: b.eq            #0xbd0dd8
    // 0xbd0dc4: r8 = Category
    //     0xbd0dc4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b498] Type: Category
    //     0xbd0dc8: ldr             x8, [x8, #0x498]
    // 0xbd0dcc: r3 = Null
    //     0xbd0dcc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4a0] Null
    //     0xbd0dd0: ldr             x3, [x3, #0x4a0]
    // 0xbd0dd4: r0 = Category()
    //     0xbd0dd4: bl              #0x80d594  ; IsType_Category_Stub
    // 0xbd0dd8: ldur            x0, [fp, #-8]
    // 0xbd0ddc: LoadField: r1 = r0->field_b
    //     0xbd0ddc: ldur            w1, [x0, #0xb]
    // 0xbd0de0: DecompressPointer r1
    //     0xbd0de0: add             x1, x1, HEAP, lsl #32
    // 0xbd0de4: LoadField: r2 = r1->field_13
    //     0xbd0de4: ldur            w2, [x1, #0x13]
    // 0xbd0de8: LoadField: r1 = r0->field_13
    //     0xbd0de8: ldur            x1, [x0, #0x13]
    // 0xbd0dec: r3 = LoadInt32Instr(r2)
    //     0xbd0dec: sbfx            x3, x2, #1, #0x1f
    // 0xbd0df0: sub             x2, x3, x1
    // 0xbd0df4: cmp             x2, #1
    // 0xbd0df8: b.ge            #0xbd0e08
    // 0xbd0dfc: mov             x1, x0
    // 0xbd0e00: r2 = 1
    //     0xbd0e00: movz            x2, #0x1
    // 0xbd0e04: r0 = _increaseBufferSize()
    //     0xbd0e04: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0e08: ldur            x3, [fp, #-8]
    // 0xbd0e0c: r2 = 5
    //     0xbd0e0c: movz            x2, #0x5
    // 0xbd0e10: LoadField: r4 = r3->field_b
    //     0xbd0e10: ldur            w4, [x3, #0xb]
    // 0xbd0e14: DecompressPointer r4
    //     0xbd0e14: add             x4, x4, HEAP, lsl #32
    // 0xbd0e18: LoadField: r5 = r3->field_13
    //     0xbd0e18: ldur            x5, [x3, #0x13]
    // 0xbd0e1c: add             x6, x5, #1
    // 0xbd0e20: StoreField: r3->field_13 = r6
    //     0xbd0e20: stur            x6, [x3, #0x13]
    // 0xbd0e24: LoadField: r0 = r4->field_13
    //     0xbd0e24: ldur            w0, [x4, #0x13]
    // 0xbd0e28: r7 = LoadInt32Instr(r0)
    //     0xbd0e28: sbfx            x7, x0, #1, #0x1f
    // 0xbd0e2c: mov             x0, x7
    // 0xbd0e30: mov             x1, x5
    // 0xbd0e34: cmp             x1, x0
    // 0xbd0e38: b.hs            #0xbd111c
    // 0xbd0e3c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd0e3c: add             x0, x4, x5
    //     0xbd0e40: strb            w2, [x0, #0x17]
    // 0xbd0e44: sub             x0, x7, x6
    // 0xbd0e48: cmp             x0, #1
    // 0xbd0e4c: b.ge            #0xbd0e5c
    // 0xbd0e50: mov             x1, x3
    // 0xbd0e54: r2 = 1
    //     0xbd0e54: movz            x2, #0x1
    // 0xbd0e58: r0 = _increaseBufferSize()
    //     0xbd0e58: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0e5c: ldur            x2, [fp, #-8]
    // 0xbd0e60: ldur            x3, [fp, #-0x10]
    // 0xbd0e64: LoadField: r4 = r2->field_b
    //     0xbd0e64: ldur            w4, [x2, #0xb]
    // 0xbd0e68: DecompressPointer r4
    //     0xbd0e68: add             x4, x4, HEAP, lsl #32
    // 0xbd0e6c: LoadField: r5 = r2->field_13
    //     0xbd0e6c: ldur            x5, [x2, #0x13]
    // 0xbd0e70: add             x0, x5, #1
    // 0xbd0e74: StoreField: r2->field_13 = r0
    //     0xbd0e74: stur            x0, [x2, #0x13]
    // 0xbd0e78: LoadField: r0 = r4->field_13
    //     0xbd0e78: ldur            w0, [x4, #0x13]
    // 0xbd0e7c: r1 = LoadInt32Instr(r0)
    //     0xbd0e7c: sbfx            x1, x0, #1, #0x1f
    // 0xbd0e80: mov             x0, x1
    // 0xbd0e84: mov             x1, x5
    // 0xbd0e88: cmp             x1, x0
    // 0xbd0e8c: b.hs            #0xbd1120
    // 0xbd0e90: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd0e90: add             x0, x4, x5
    //     0xbd0e94: strb            wzr, [x0, #0x17]
    // 0xbd0e98: LoadField: r4 = r3->field_13
    //     0xbd0e98: ldur            x4, [x3, #0x13]
    // 0xbd0e9c: r0 = BoxInt64Instr(r4)
    //     0xbd0e9c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd0ea0: cmp             x4, x0, asr #1
    //     0xbd0ea4: b.eq            #0xbd0eb0
    //     0xbd0ea8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd0eac: stur            x4, [x0, #7]
    // 0xbd0eb0: r16 = <int>
    //     0xbd0eb0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd0eb4: stp             x2, x16, [SP, #8]
    // 0xbd0eb8: str             x0, [SP]
    // 0xbd0ebc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0ebc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0ec0: r0 = write()
    //     0xbd0ec0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0ec4: ldur            x0, [fp, #-8]
    // 0xbd0ec8: LoadField: r1 = r0->field_b
    //     0xbd0ec8: ldur            w1, [x0, #0xb]
    // 0xbd0ecc: DecompressPointer r1
    //     0xbd0ecc: add             x1, x1, HEAP, lsl #32
    // 0xbd0ed0: LoadField: r2 = r1->field_13
    //     0xbd0ed0: ldur            w2, [x1, #0x13]
    // 0xbd0ed4: LoadField: r1 = r0->field_13
    //     0xbd0ed4: ldur            x1, [x0, #0x13]
    // 0xbd0ed8: r3 = LoadInt32Instr(r2)
    //     0xbd0ed8: sbfx            x3, x2, #1, #0x1f
    // 0xbd0edc: sub             x2, x3, x1
    // 0xbd0ee0: cmp             x2, #1
    // 0xbd0ee4: b.ge            #0xbd0ef4
    // 0xbd0ee8: mov             x1, x0
    // 0xbd0eec: r2 = 1
    //     0xbd0eec: movz            x2, #0x1
    // 0xbd0ef0: r0 = _increaseBufferSize()
    //     0xbd0ef0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0ef4: ldur            x2, [fp, #-8]
    // 0xbd0ef8: ldur            x3, [fp, #-0x10]
    // 0xbd0efc: r4 = 1
    //     0xbd0efc: movz            x4, #0x1
    // 0xbd0f00: LoadField: r5 = r2->field_b
    //     0xbd0f00: ldur            w5, [x2, #0xb]
    // 0xbd0f04: DecompressPointer r5
    //     0xbd0f04: add             x5, x5, HEAP, lsl #32
    // 0xbd0f08: LoadField: r6 = r2->field_13
    //     0xbd0f08: ldur            x6, [x2, #0x13]
    // 0xbd0f0c: add             x0, x6, #1
    // 0xbd0f10: StoreField: r2->field_13 = r0
    //     0xbd0f10: stur            x0, [x2, #0x13]
    // 0xbd0f14: LoadField: r0 = r5->field_13
    //     0xbd0f14: ldur            w0, [x5, #0x13]
    // 0xbd0f18: r1 = LoadInt32Instr(r0)
    //     0xbd0f18: sbfx            x1, x0, #1, #0x1f
    // 0xbd0f1c: mov             x0, x1
    // 0xbd0f20: mov             x1, x6
    // 0xbd0f24: cmp             x1, x0
    // 0xbd0f28: b.hs            #0xbd1124
    // 0xbd0f2c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0f2c: add             x0, x5, x6
    //     0xbd0f30: strb            w4, [x0, #0x17]
    // 0xbd0f34: LoadField: r0 = r3->field_1b
    //     0xbd0f34: ldur            w0, [x3, #0x1b]
    // 0xbd0f38: DecompressPointer r0
    //     0xbd0f38: add             x0, x0, HEAP, lsl #32
    // 0xbd0f3c: r16 = <String>
    //     0xbd0f3c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd0f40: stp             x2, x16, [SP, #8]
    // 0xbd0f44: str             x0, [SP]
    // 0xbd0f48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0f48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0f4c: r0 = write()
    //     0xbd0f4c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0f50: ldur            x0, [fp, #-8]
    // 0xbd0f54: LoadField: r1 = r0->field_b
    //     0xbd0f54: ldur            w1, [x0, #0xb]
    // 0xbd0f58: DecompressPointer r1
    //     0xbd0f58: add             x1, x1, HEAP, lsl #32
    // 0xbd0f5c: LoadField: r2 = r1->field_13
    //     0xbd0f5c: ldur            w2, [x1, #0x13]
    // 0xbd0f60: LoadField: r1 = r0->field_13
    //     0xbd0f60: ldur            x1, [x0, #0x13]
    // 0xbd0f64: r3 = LoadInt32Instr(r2)
    //     0xbd0f64: sbfx            x3, x2, #1, #0x1f
    // 0xbd0f68: sub             x2, x3, x1
    // 0xbd0f6c: cmp             x2, #1
    // 0xbd0f70: b.ge            #0xbd0f80
    // 0xbd0f74: mov             x1, x0
    // 0xbd0f78: r2 = 1
    //     0xbd0f78: movz            x2, #0x1
    // 0xbd0f7c: r0 = _increaseBufferSize()
    //     0xbd0f7c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd0f80: ldur            x2, [fp, #-8]
    // 0xbd0f84: ldur            x3, [fp, #-0x10]
    // 0xbd0f88: r4 = 2
    //     0xbd0f88: movz            x4, #0x2
    // 0xbd0f8c: LoadField: r5 = r2->field_b
    //     0xbd0f8c: ldur            w5, [x2, #0xb]
    // 0xbd0f90: DecompressPointer r5
    //     0xbd0f90: add             x5, x5, HEAP, lsl #32
    // 0xbd0f94: LoadField: r6 = r2->field_13
    //     0xbd0f94: ldur            x6, [x2, #0x13]
    // 0xbd0f98: add             x0, x6, #1
    // 0xbd0f9c: StoreField: r2->field_13 = r0
    //     0xbd0f9c: stur            x0, [x2, #0x13]
    // 0xbd0fa0: LoadField: r0 = r5->field_13
    //     0xbd0fa0: ldur            w0, [x5, #0x13]
    // 0xbd0fa4: r1 = LoadInt32Instr(r0)
    //     0xbd0fa4: sbfx            x1, x0, #1, #0x1f
    // 0xbd0fa8: mov             x0, x1
    // 0xbd0fac: mov             x1, x6
    // 0xbd0fb0: cmp             x1, x0
    // 0xbd0fb4: b.hs            #0xbd1128
    // 0xbd0fb8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd0fb8: add             x0, x5, x6
    //     0xbd0fbc: strb            w4, [x0, #0x17]
    // 0xbd0fc0: LoadField: r0 = r3->field_1f
    //     0xbd0fc0: ldur            w0, [x3, #0x1f]
    // 0xbd0fc4: DecompressPointer r0
    //     0xbd0fc4: add             x0, x0, HEAP, lsl #32
    // 0xbd0fc8: r16 = <bool>
    //     0xbd0fc8: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd0fcc: stp             x2, x16, [SP, #8]
    // 0xbd0fd0: str             x0, [SP]
    // 0xbd0fd4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd0fd4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd0fd8: r0 = write()
    //     0xbd0fd8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd0fdc: ldur            x0, [fp, #-8]
    // 0xbd0fe0: LoadField: r1 = r0->field_b
    //     0xbd0fe0: ldur            w1, [x0, #0xb]
    // 0xbd0fe4: DecompressPointer r1
    //     0xbd0fe4: add             x1, x1, HEAP, lsl #32
    // 0xbd0fe8: LoadField: r2 = r1->field_13
    //     0xbd0fe8: ldur            w2, [x1, #0x13]
    // 0xbd0fec: LoadField: r1 = r0->field_13
    //     0xbd0fec: ldur            x1, [x0, #0x13]
    // 0xbd0ff0: r3 = LoadInt32Instr(r2)
    //     0xbd0ff0: sbfx            x3, x2, #1, #0x1f
    // 0xbd0ff4: sub             x2, x3, x1
    // 0xbd0ff8: cmp             x2, #1
    // 0xbd0ffc: b.ge            #0xbd100c
    // 0xbd1000: mov             x1, x0
    // 0xbd1004: r2 = 1
    //     0xbd1004: movz            x2, #0x1
    // 0xbd1008: r0 = _increaseBufferSize()
    //     0xbd1008: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd100c: ldur            x2, [fp, #-8]
    // 0xbd1010: ldur            x3, [fp, #-0x10]
    // 0xbd1014: r4 = 3
    //     0xbd1014: movz            x4, #0x3
    // 0xbd1018: LoadField: r5 = r2->field_b
    //     0xbd1018: ldur            w5, [x2, #0xb]
    // 0xbd101c: DecompressPointer r5
    //     0xbd101c: add             x5, x5, HEAP, lsl #32
    // 0xbd1020: LoadField: r6 = r2->field_13
    //     0xbd1020: ldur            x6, [x2, #0x13]
    // 0xbd1024: add             x0, x6, #1
    // 0xbd1028: StoreField: r2->field_13 = r0
    //     0xbd1028: stur            x0, [x2, #0x13]
    // 0xbd102c: LoadField: r0 = r5->field_13
    //     0xbd102c: ldur            w0, [x5, #0x13]
    // 0xbd1030: r1 = LoadInt32Instr(r0)
    //     0xbd1030: sbfx            x1, x0, #1, #0x1f
    // 0xbd1034: mov             x0, x1
    // 0xbd1038: mov             x1, x6
    // 0xbd103c: cmp             x1, x0
    // 0xbd1040: b.hs            #0xbd112c
    // 0xbd1044: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1044: add             x0, x5, x6
    //     0xbd1048: strb            w4, [x0, #0x17]
    // 0xbd104c: LoadField: r0 = r3->field_23
    //     0xbd104c: ldur            w0, [x3, #0x23]
    // 0xbd1050: DecompressPointer r0
    //     0xbd1050: add             x0, x0, HEAP, lsl #32
    // 0xbd1054: r16 = <bool>
    //     0xbd1054: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd1058: stp             x2, x16, [SP, #8]
    // 0xbd105c: str             x0, [SP]
    // 0xbd1060: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1060: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1064: r0 = write()
    //     0xbd1064: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1068: ldur            x0, [fp, #-8]
    // 0xbd106c: LoadField: r1 = r0->field_b
    //     0xbd106c: ldur            w1, [x0, #0xb]
    // 0xbd1070: DecompressPointer r1
    //     0xbd1070: add             x1, x1, HEAP, lsl #32
    // 0xbd1074: LoadField: r2 = r1->field_13
    //     0xbd1074: ldur            w2, [x1, #0x13]
    // 0xbd1078: LoadField: r1 = r0->field_13
    //     0xbd1078: ldur            x1, [x0, #0x13]
    // 0xbd107c: r3 = LoadInt32Instr(r2)
    //     0xbd107c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1080: sub             x2, x3, x1
    // 0xbd1084: cmp             x2, #1
    // 0xbd1088: b.ge            #0xbd1098
    // 0xbd108c: mov             x1, x0
    // 0xbd1090: r2 = 1
    //     0xbd1090: movz            x2, #0x1
    // 0xbd1094: r0 = _increaseBufferSize()
    //     0xbd1094: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1098: ldur            x2, [fp, #-8]
    // 0xbd109c: ldur            x3, [fp, #-0x10]
    // 0xbd10a0: r4 = 4
    //     0xbd10a0: movz            x4, #0x4
    // 0xbd10a4: LoadField: r5 = r2->field_b
    //     0xbd10a4: ldur            w5, [x2, #0xb]
    // 0xbd10a8: DecompressPointer r5
    //     0xbd10a8: add             x5, x5, HEAP, lsl #32
    // 0xbd10ac: LoadField: r6 = r2->field_13
    //     0xbd10ac: ldur            x6, [x2, #0x13]
    // 0xbd10b0: add             x0, x6, #1
    // 0xbd10b4: StoreField: r2->field_13 = r0
    //     0xbd10b4: stur            x0, [x2, #0x13]
    // 0xbd10b8: LoadField: r0 = r5->field_13
    //     0xbd10b8: ldur            w0, [x5, #0x13]
    // 0xbd10bc: r1 = LoadInt32Instr(r0)
    //     0xbd10bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd10c0: mov             x0, x1
    // 0xbd10c4: mov             x1, x6
    // 0xbd10c8: cmp             x1, x0
    // 0xbd10cc: b.hs            #0xbd1130
    // 0xbd10d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd10d0: add             x0, x5, x6
    //     0xbd10d4: strb            w4, [x0, #0x17]
    // 0xbd10d8: LoadField: r4 = r3->field_27
    //     0xbd10d8: ldur            x4, [x3, #0x27]
    // 0xbd10dc: r0 = BoxInt64Instr(r4)
    //     0xbd10dc: sbfiz           x0, x4, #1, #0x1f
    //     0xbd10e0: cmp             x4, x0, asr #1
    //     0xbd10e4: b.eq            #0xbd10f0
    //     0xbd10e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd10ec: stur            x4, [x0, #7]
    // 0xbd10f0: r16 = <int>
    //     0xbd10f0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd10f4: stp             x2, x16, [SP, #8]
    // 0xbd10f8: str             x0, [SP]
    // 0xbd10fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd10fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1100: r0 = write()
    //     0xbd1100: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1104: r0 = Null
    //     0xbd1104: mov             x0, NULL
    // 0xbd1108: LeaveFrame
    //     0xbd1108: mov             SP, fp
    //     0xbd110c: ldp             fp, lr, [SP], #0x10
    // 0xbd1110: ret
    //     0xbd1110: ret             
    // 0xbd1114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd1114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd1118: b               #0xbd0da0
    // 0xbd111c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd111c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1120: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1120: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1124: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1124: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1128: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd112c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd112c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1130: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1130: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf006c, size: 0x24
    // 0xbf006c: r1 = 80
    //     0xbf006c: movz            x1, #0x50
    // 0xbf0070: r16 = LoadInt32Instr(r1)
    //     0xbf0070: sbfx            x16, x1, #1, #0x1f
    // 0xbf0074: r17 = 11601
    //     0xbf0074: movz            x17, #0x2d51
    // 0xbf0078: mul             x0, x16, x17
    // 0xbf007c: umulh           x16, x16, x17
    // 0xbf0080: eor             x0, x0, x16
    // 0xbf0084: r0 = 0
    //     0xbf0084: eor             x0, x0, x0, lsr #32
    // 0xbf0088: ubfiz           x0, x0, #1, #0x1e
    // 0xbf008c: ret
    //     0xbf008c: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76248, size: 0x9c
    // 0xd76248: EnterFrame
    //     0xd76248: stp             fp, lr, [SP, #-0x10]!
    //     0xd7624c: mov             fp, SP
    // 0xd76250: AllocStack(0x10)
    //     0xd76250: sub             SP, SP, #0x10
    // 0xd76254: CheckStackOverflow
    //     0xd76254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76258: cmp             SP, x16
    //     0xd7625c: b.ls            #0xd762dc
    // 0xd76260: ldr             x0, [fp, #0x10]
    // 0xd76264: cmp             w0, NULL
    // 0xd76268: b.ne            #0xd7627c
    // 0xd7626c: r0 = false
    //     0xd7626c: add             x0, NULL, #0x30  ; false
    // 0xd76270: LeaveFrame
    //     0xd76270: mov             SP, fp
    //     0xd76274: ldp             fp, lr, [SP], #0x10
    // 0xd76278: ret
    //     0xd76278: ret             
    // 0xd7627c: ldr             x1, [fp, #0x18]
    // 0xd76280: cmp             w1, w0
    // 0xd76284: b.ne            #0xd76290
    // 0xd76288: r0 = true
    //     0xd76288: add             x0, NULL, #0x20  ; true
    // 0xd7628c: b               #0xd762d0
    // 0xd76290: r1 = 60
    //     0xd76290: movz            x1, #0x3c
    // 0xd76294: branchIfSmi(r0, 0xd762a0)
    //     0xd76294: tbz             w0, #0, #0xd762a0
    // 0xd76298: r1 = LoadClassIdInstr(r0)
    //     0xd76298: ldur            x1, [x0, #-1]
    //     0xd7629c: ubfx            x1, x1, #0xc, #0x14
    // 0xd762a0: cmp             x1, #0x683
    // 0xd762a4: b.ne            #0xd762cc
    // 0xd762a8: r16 = CategoryAdapter
    //     0xd762a8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b490] Type: CategoryAdapter
    //     0xd762ac: ldr             x16, [x16, #0x490]
    // 0xd762b0: r30 = CategoryAdapter
    //     0xd762b0: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b490] Type: CategoryAdapter
    //     0xd762b4: ldr             lr, [lr, #0x490]
    // 0xd762b8: stp             lr, x16, [SP]
    // 0xd762bc: r0 = ==()
    //     0xd762bc: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd762c0: tbnz            w0, #4, #0xd762cc
    // 0xd762c4: r0 = true
    //     0xd762c4: add             x0, NULL, #0x20  ; true
    // 0xd762c8: b               #0xd762d0
    // 0xd762cc: r0 = false
    //     0xd762cc: add             x0, NULL, #0x30  ; false
    // 0xd762d0: LeaveFrame
    //     0xd762d0: mov             SP, fp
    //     0xd762d4: ldp             fp, lr, [SP], #0x10
    // 0xd762d8: ret
    //     0xd762d8: ret             
    // 0xd762dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd762dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd762e0: b               #0xd76260
  }
}
