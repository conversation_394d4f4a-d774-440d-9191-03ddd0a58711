// lib: , url: package:nuonline/app/data/models/countdown.dart

// class id: 1050012, size: 0x8
class :: {
}

// class id: 1608, size: 0x44, field offset: 0x14
class Countdown extends HiveObject {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x91f9b4, size: 0x180
    // 0x91f9b4: EnterFrame
    //     0x91f9b4: stp             fp, lr, [SP, #-0x10]!
    //     0x91f9b8: mov             fp, SP
    // 0x91f9bc: AllocStack(0x20)
    //     0x91f9bc: sub             SP, SP, #0x20
    // 0x91f9c0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x91f9c0: mov             x3, x1
    //     0x91f9c4: stur            x1, [fp, #-8]
    // 0x91f9c8: CheckStackOverflow
    //     0x91f9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91f9cc: cmp             SP, x16
    //     0x91f9d0: b.ls            #0x91fb2c
    // 0x91f9d4: mov             x0, x3
    // 0x91f9d8: r2 = Null
    //     0x91f9d8: mov             x2, NULL
    // 0x91f9dc: r1 = Null
    //     0x91f9dc: mov             x1, NULL
    // 0x91f9e0: cmp             w0, NULL
    // 0x91f9e4: b.eq            #0x91fa88
    // 0x91f9e8: branchIfSmi(r0, 0x91fa88)
    //     0x91f9e8: tbz             w0, #0, #0x91fa88
    // 0x91f9ec: r3 = LoadClassIdInstr(r0)
    //     0x91f9ec: ldur            x3, [x0, #-1]
    //     0x91f9f0: ubfx            x3, x3, #0xc, #0x14
    // 0x91f9f4: r17 = 6718
    //     0x91f9f4: movz            x17, #0x1a3e
    // 0x91f9f8: cmp             x3, x17
    // 0x91f9fc: b.eq            #0x91fa90
    // 0x91fa00: sub             x3, x3, #0x5a
    // 0x91fa04: cmp             x3, #2
    // 0x91fa08: b.ls            #0x91fa90
    // 0x91fa0c: r4 = LoadClassIdInstr(r0)
    //     0x91fa0c: ldur            x4, [x0, #-1]
    //     0x91fa10: ubfx            x4, x4, #0xc, #0x14
    // 0x91fa14: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x91fa18: ldr             x3, [x3, #0x18]
    // 0x91fa1c: ldr             x3, [x3, x4, lsl #3]
    // 0x91fa20: LoadField: r3 = r3->field_2b
    //     0x91fa20: ldur            w3, [x3, #0x2b]
    // 0x91fa24: DecompressPointer r3
    //     0x91fa24: add             x3, x3, HEAP, lsl #32
    // 0x91fa28: cmp             w3, NULL
    // 0x91fa2c: b.eq            #0x91fa88
    // 0x91fa30: LoadField: r3 = r3->field_f
    //     0x91fa30: ldur            w3, [x3, #0xf]
    // 0x91fa34: lsr             x3, x3, #3
    // 0x91fa38: r17 = 6718
    //     0x91fa38: movz            x17, #0x1a3e
    // 0x91fa3c: cmp             x3, x17
    // 0x91fa40: b.eq            #0x91fa90
    // 0x91fa44: r3 = SubtypeTestCache
    //     0x91fa44: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb50] SubtypeTestCache
    //     0x91fa48: ldr             x3, [x3, #0xb50]
    // 0x91fa4c: r30 = Subtype1TestCacheStub
    //     0x91fa4c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x91fa50: LoadField: r30 = r30->field_7
    //     0x91fa50: ldur            lr, [lr, #7]
    // 0x91fa54: blr             lr
    // 0x91fa58: cmp             w7, NULL
    // 0x91fa5c: b.eq            #0x91fa68
    // 0x91fa60: tbnz            w7, #4, #0x91fa88
    // 0x91fa64: b               #0x91fa90
    // 0x91fa68: r8 = List
    //     0x91fa68: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3fb58] Type: List
    //     0x91fa6c: ldr             x8, [x8, #0xb58]
    // 0x91fa70: r3 = SubtypeTestCache
    //     0x91fa70: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb60] SubtypeTestCache
    //     0x91fa74: ldr             x3, [x3, #0xb60]
    // 0x91fa78: r30 = InstanceOfStub
    //     0x91fa78: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x91fa7c: LoadField: r30 = r30->field_7
    //     0x91fa7c: ldur            lr, [lr, #7]
    // 0x91fa80: blr             lr
    // 0x91fa84: b               #0x91fa94
    // 0x91fa88: r0 = false
    //     0x91fa88: add             x0, NULL, #0x30  ; false
    // 0x91fa8c: b               #0x91fa94
    // 0x91fa90: r0 = true
    //     0x91fa90: add             x0, NULL, #0x20  ; true
    // 0x91fa94: tbnz            w0, #4, #0x91fb14
    // 0x91fa98: ldur            x0, [fp, #-8]
    // 0x91fa9c: r1 = Function '<anonymous closure>': static.
    //     0x91fa9c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fb68] AnonymousClosure: static (0x91fb34), in [package:nuonline/app/data/models/countdown.dart] Countdown::fromResponse (0x91f9b4)
    //     0x91faa0: ldr             x1, [x1, #0xb68]
    // 0x91faa4: r2 = Null
    //     0x91faa4: mov             x2, NULL
    // 0x91faa8: r0 = AllocateClosure()
    //     0x91faa8: bl              #0xec1630  ; AllocateClosureStub
    // 0x91faac: mov             x1, x0
    // 0x91fab0: ldur            x0, [fp, #-8]
    // 0x91fab4: r2 = LoadClassIdInstr(r0)
    //     0x91fab4: ldur            x2, [x0, #-1]
    //     0x91fab8: ubfx            x2, x2, #0xc, #0x14
    // 0x91fabc: r16 = <Countdown>
    //     0x91fabc: ldr             x16, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91fac0: stp             x0, x16, [SP, #8]
    // 0x91fac4: str             x1, [SP]
    // 0x91fac8: mov             x0, x2
    // 0x91facc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91facc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91fad0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x91fad0: movz            x17, #0xf28c
    //     0x91fad4: add             lr, x0, x17
    //     0x91fad8: ldr             lr, [x21, lr, lsl #3]
    //     0x91fadc: blr             lr
    // 0x91fae0: r1 = LoadClassIdInstr(r0)
    //     0x91fae0: ldur            x1, [x0, #-1]
    //     0x91fae4: ubfx            x1, x1, #0xc, #0x14
    // 0x91fae8: mov             x16, x0
    // 0x91faec: mov             x0, x1
    // 0x91faf0: mov             x1, x16
    // 0x91faf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91faf4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91faf8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x91faf8: movz            x17, #0xd889
    //     0x91fafc: add             lr, x0, x17
    //     0x91fb00: ldr             lr, [x21, lr, lsl #3]
    //     0x91fb04: blr             lr
    // 0x91fb08: LeaveFrame
    //     0x91fb08: mov             SP, fp
    //     0x91fb0c: ldp             fp, lr, [SP], #0x10
    // 0x91fb10: ret
    //     0x91fb10: ret             
    // 0x91fb14: r1 = <Countdown>
    //     0x91fb14: ldr             x1, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91fb18: r2 = 0
    //     0x91fb18: movz            x2, #0
    // 0x91fb1c: r0 = _GrowableList()
    //     0x91fb1c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x91fb20: LeaveFrame
    //     0x91fb20: mov             SP, fp
    //     0x91fb24: ldp             fp, lr, [SP], #0x10
    // 0x91fb28: ret
    //     0x91fb28: ret             
    // 0x91fb2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91fb2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91fb30: b               #0x91f9d4
  }
  [closure] static Countdown <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x91fb34, size: 0x50
    // 0x91fb34: EnterFrame
    //     0x91fb34: stp             fp, lr, [SP, #-0x10]!
    //     0x91fb38: mov             fp, SP
    // 0x91fb3c: CheckStackOverflow
    //     0x91fb3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91fb40: cmp             SP, x16
    //     0x91fb44: b.ls            #0x91fb7c
    // 0x91fb48: ldr             x0, [fp, #0x10]
    // 0x91fb4c: r2 = Null
    //     0x91fb4c: mov             x2, NULL
    // 0x91fb50: r1 = Null
    //     0x91fb50: mov             x1, NULL
    // 0x91fb54: r8 = Map<String, dynamic>
    //     0x91fb54: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x91fb58: r3 = Null
    //     0x91fb58: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb70] Null
    //     0x91fb5c: ldr             x3, [x3, #0xb70]
    // 0x91fb60: r0 = Map<String, dynamic>()
    //     0x91fb60: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x91fb64: ldr             x2, [fp, #0x10]
    // 0x91fb68: r1 = Null
    //     0x91fb68: mov             x1, NULL
    // 0x91fb6c: r0 = Countdown.fromMap()
    //     0x91fb6c: bl              #0x91fb84  ; [package:nuonline/app/data/models/countdown.dart] Countdown::Countdown.fromMap
    // 0x91fb70: LeaveFrame
    //     0x91fb70: mov             SP, fp
    //     0x91fb74: ldp             fp, lr, [SP], #0x10
    // 0x91fb78: ret
    //     0x91fb78: ret             
    // 0x91fb7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91fb7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91fb80: b               #0x91fb48
  }
  factory _ Countdown.fromMap(/* No info */) {
    // ** addr: 0x91fb84, size: 0x4ec
    // 0x91fb84: EnterFrame
    //     0x91fb84: stp             fp, lr, [SP, #-0x10]!
    //     0x91fb88: mov             fp, SP
    // 0x91fb8c: AllocStack(0x68)
    //     0x91fb8c: sub             SP, SP, #0x68
    // 0x91fb90: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x91fb90: mov             x3, x2
    //     0x91fb94: stur            x2, [fp, #-8]
    // 0x91fb98: CheckStackOverflow
    //     0x91fb98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91fb9c: cmp             SP, x16
    //     0x91fba0: b.ls            #0x920068
    // 0x91fba4: r0 = LoadClassIdInstr(r3)
    //     0x91fba4: ldur            x0, [x3, #-1]
    //     0x91fba8: ubfx            x0, x0, #0xc, #0x14
    // 0x91fbac: mov             x1, x3
    // 0x91fbb0: r2 = "route"
    //     0x91fbb0: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x91fbb4: ldr             x2, [x2, #0x3a0]
    // 0x91fbb8: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fbb8: sub             lr, x0, #0x114
    //     0x91fbbc: ldr             lr, [x21, lr, lsl #3]
    //     0x91fbc0: blr             lr
    // 0x91fbc4: mov             x3, x0
    // 0x91fbc8: r2 = Null
    //     0x91fbc8: mov             x2, NULL
    // 0x91fbcc: r1 = Null
    //     0x91fbcc: mov             x1, NULL
    // 0x91fbd0: stur            x3, [fp, #-0x10]
    // 0x91fbd4: r4 = 60
    //     0x91fbd4: movz            x4, #0x3c
    // 0x91fbd8: branchIfSmi(r0, 0x91fbe4)
    //     0x91fbd8: tbz             w0, #0, #0x91fbe4
    // 0x91fbdc: r4 = LoadClassIdInstr(r0)
    //     0x91fbdc: ldur            x4, [x0, #-1]
    //     0x91fbe0: ubfx            x4, x4, #0xc, #0x14
    // 0x91fbe4: sub             x4, x4, #0x5e
    // 0x91fbe8: cmp             x4, #1
    // 0x91fbec: b.ls            #0x91fc00
    // 0x91fbf0: r8 = String?
    //     0x91fbf0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91fbf4: r3 = Null
    //     0x91fbf4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb80] Null
    //     0x91fbf8: ldr             x3, [x3, #0xb80]
    // 0x91fbfc: r0 = String?()
    //     0x91fbfc: bl              #0x600324  ; IsType_String?_Stub
    // 0x91fc00: ldur            x3, [fp, #-8]
    // 0x91fc04: r0 = LoadClassIdInstr(r3)
    //     0x91fc04: ldur            x0, [x3, #-1]
    //     0x91fc08: ubfx            x0, x0, #0xc, #0x14
    // 0x91fc0c: mov             x1, x3
    // 0x91fc10: r2 = "params"
    //     0x91fc10: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x91fc14: ldr             x2, [x2, #0x3b8]
    // 0x91fc18: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fc18: sub             lr, x0, #0x114
    //     0x91fc1c: ldr             lr, [x21, lr, lsl #3]
    //     0x91fc20: blr             lr
    // 0x91fc24: mov             x3, x0
    // 0x91fc28: r2 = Null
    //     0x91fc28: mov             x2, NULL
    // 0x91fc2c: r1 = Null
    //     0x91fc2c: mov             x1, NULL
    // 0x91fc30: stur            x3, [fp, #-0x18]
    // 0x91fc34: r8 = Map<String, dynamic>?
    //     0x91fc34: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0x91fc38: r3 = Null
    //     0x91fc38: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb90] Null
    //     0x91fc3c: ldr             x3, [x3, #0xb90]
    // 0x91fc40: r0 = Map<String, dynamic>?()
    //     0x91fc40: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0x91fc44: ldur            x3, [fp, #-8]
    // 0x91fc48: r0 = LoadClassIdInstr(r3)
    //     0x91fc48: ldur            x0, [x3, #-1]
    //     0x91fc4c: ubfx            x0, x0, #0xc, #0x14
    // 0x91fc50: mov             x1, x3
    // 0x91fc54: r2 = "image_url"
    //     0x91fc54: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fba0] "image_url"
    //     0x91fc58: ldr             x2, [x2, #0xba0]
    // 0x91fc5c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fc5c: sub             lr, x0, #0x114
    //     0x91fc60: ldr             lr, [x21, lr, lsl #3]
    //     0x91fc64: blr             lr
    // 0x91fc68: mov             x3, x0
    // 0x91fc6c: r2 = Null
    //     0x91fc6c: mov             x2, NULL
    // 0x91fc70: r1 = Null
    //     0x91fc70: mov             x1, NULL
    // 0x91fc74: stur            x3, [fp, #-0x20]
    // 0x91fc78: r4 = 60
    //     0x91fc78: movz            x4, #0x3c
    // 0x91fc7c: branchIfSmi(r0, 0x91fc88)
    //     0x91fc7c: tbz             w0, #0, #0x91fc88
    // 0x91fc80: r4 = LoadClassIdInstr(r0)
    //     0x91fc80: ldur            x4, [x0, #-1]
    //     0x91fc84: ubfx            x4, x4, #0xc, #0x14
    // 0x91fc88: sub             x4, x4, #0x5e
    // 0x91fc8c: cmp             x4, #1
    // 0x91fc90: b.ls            #0x91fca4
    // 0x91fc94: r8 = String
    //     0x91fc94: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91fc98: r3 = Null
    //     0x91fc98: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fba8] Null
    //     0x91fc9c: ldr             x3, [x3, #0xba8]
    // 0x91fca0: r0 = String()
    //     0x91fca0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91fca4: ldur            x3, [fp, #-8]
    // 0x91fca8: r0 = LoadClassIdInstr(r3)
    //     0x91fca8: ldur            x0, [x3, #-1]
    //     0x91fcac: ubfx            x0, x0, #0xc, #0x14
    // 0x91fcb0: mov             x1, x3
    // 0x91fcb4: r2 = "height"
    //     0x91fcb4: ldr             x2, [PP, #0x4778]  ; [pp+0x4778] "height"
    // 0x91fcb8: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fcb8: sub             lr, x0, #0x114
    //     0x91fcbc: ldr             lr, [x21, lr, lsl #3]
    //     0x91fcc0: blr             lr
    // 0x91fcc4: mov             x3, x0
    // 0x91fcc8: r2 = Null
    //     0x91fcc8: mov             x2, NULL
    // 0x91fccc: r1 = Null
    //     0x91fccc: mov             x1, NULL
    // 0x91fcd0: stur            x3, [fp, #-0x28]
    // 0x91fcd4: branchIfSmi(r0, 0x91fcfc)
    //     0x91fcd4: tbz             w0, #0, #0x91fcfc
    // 0x91fcd8: r4 = LoadClassIdInstr(r0)
    //     0x91fcd8: ldur            x4, [x0, #-1]
    //     0x91fcdc: ubfx            x4, x4, #0xc, #0x14
    // 0x91fce0: sub             x4, x4, #0x3c
    // 0x91fce4: cmp             x4, #1
    // 0x91fce8: b.ls            #0x91fcfc
    // 0x91fcec: r8 = int
    //     0x91fcec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x91fcf0: r3 = Null
    //     0x91fcf0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fbb8] Null
    //     0x91fcf4: ldr             x3, [x3, #0xbb8]
    // 0x91fcf8: r0 = int()
    //     0x91fcf8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91fcfc: ldur            x16, [fp, #-0x28]
    // 0x91fd00: stp             x16, NULL, [SP]
    // 0x91fd04: r0 = _Double.fromInteger()
    //     0x91fd04: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x91fd08: mov             x4, x0
    // 0x91fd0c: ldur            x3, [fp, #-8]
    // 0x91fd10: stur            x4, [fp, #-0x28]
    // 0x91fd14: r0 = LoadClassIdInstr(r3)
    //     0x91fd14: ldur            x0, [x3, #-1]
    //     0x91fd18: ubfx            x0, x0, #0xc, #0x14
    // 0x91fd1c: mov             x1, x3
    // 0x91fd20: r2 = "width"
    //     0x91fd20: ldr             x2, [PP, #0x7198]  ; [pp+0x7198] "width"
    // 0x91fd24: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fd24: sub             lr, x0, #0x114
    //     0x91fd28: ldr             lr, [x21, lr, lsl #3]
    //     0x91fd2c: blr             lr
    // 0x91fd30: mov             x3, x0
    // 0x91fd34: r2 = Null
    //     0x91fd34: mov             x2, NULL
    // 0x91fd38: r1 = Null
    //     0x91fd38: mov             x1, NULL
    // 0x91fd3c: stur            x3, [fp, #-0x30]
    // 0x91fd40: branchIfSmi(r0, 0x91fd68)
    //     0x91fd40: tbz             w0, #0, #0x91fd68
    // 0x91fd44: r4 = LoadClassIdInstr(r0)
    //     0x91fd44: ldur            x4, [x0, #-1]
    //     0x91fd48: ubfx            x4, x4, #0xc, #0x14
    // 0x91fd4c: sub             x4, x4, #0x3c
    // 0x91fd50: cmp             x4, #1
    // 0x91fd54: b.ls            #0x91fd68
    // 0x91fd58: r8 = int
    //     0x91fd58: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x91fd5c: r3 = Null
    //     0x91fd5c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fbc8] Null
    //     0x91fd60: ldr             x3, [x3, #0xbc8]
    // 0x91fd64: r0 = int()
    //     0x91fd64: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91fd68: ldur            x16, [fp, #-0x30]
    // 0x91fd6c: stp             x16, NULL, [SP]
    // 0x91fd70: r0 = _Double.fromInteger()
    //     0x91fd70: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x91fd74: mov             x4, x0
    // 0x91fd78: ldur            x3, [fp, #-8]
    // 0x91fd7c: stur            x4, [fp, #-0x30]
    // 0x91fd80: r0 = LoadClassIdInstr(r3)
    //     0x91fd80: ldur            x0, [x3, #-1]
    //     0x91fd84: ubfx            x0, x0, #0xc, #0x14
    // 0x91fd88: mov             x1, x3
    // 0x91fd8c: r2 = "event_at"
    //     0x91fd8c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fbd8] "event_at"
    //     0x91fd90: ldr             x2, [x2, #0xbd8]
    // 0x91fd94: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fd94: sub             lr, x0, #0x114
    //     0x91fd98: ldr             lr, [x21, lr, lsl #3]
    //     0x91fd9c: blr             lr
    // 0x91fda0: cmp             w0, NULL
    // 0x91fda4: b.eq            #0x91fe18
    // 0x91fda8: ldur            x3, [fp, #-8]
    // 0x91fdac: r0 = LoadClassIdInstr(r3)
    //     0x91fdac: ldur            x0, [x3, #-1]
    //     0x91fdb0: ubfx            x0, x0, #0xc, #0x14
    // 0x91fdb4: mov             x1, x3
    // 0x91fdb8: r2 = "event_at"
    //     0x91fdb8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fbd8] "event_at"
    //     0x91fdbc: ldr             x2, [x2, #0xbd8]
    // 0x91fdc0: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fdc0: sub             lr, x0, #0x114
    //     0x91fdc4: ldr             lr, [x21, lr, lsl #3]
    //     0x91fdc8: blr             lr
    // 0x91fdcc: mov             x3, x0
    // 0x91fdd0: r2 = Null
    //     0x91fdd0: mov             x2, NULL
    // 0x91fdd4: r1 = Null
    //     0x91fdd4: mov             x1, NULL
    // 0x91fdd8: stur            x3, [fp, #-0x38]
    // 0x91fddc: r4 = 60
    //     0x91fddc: movz            x4, #0x3c
    // 0x91fde0: branchIfSmi(r0, 0x91fdec)
    //     0x91fde0: tbz             w0, #0, #0x91fdec
    // 0x91fde4: r4 = LoadClassIdInstr(r0)
    //     0x91fde4: ldur            x4, [x0, #-1]
    //     0x91fde8: ubfx            x4, x4, #0xc, #0x14
    // 0x91fdec: sub             x4, x4, #0x5e
    // 0x91fdf0: cmp             x4, #1
    // 0x91fdf4: b.ls            #0x91fe08
    // 0x91fdf8: r8 = String
    //     0x91fdf8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91fdfc: r3 = Null
    //     0x91fdfc: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fbe0] Null
    //     0x91fe00: ldr             x3, [x3, #0xbe0]
    // 0x91fe04: r0 = String()
    //     0x91fe04: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91fe08: ldur            x1, [fp, #-0x38]
    // 0x91fe0c: r0 = tryParse()
    //     0x91fe0c: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x91fe10: mov             x9, x0
    // 0x91fe14: b               #0x91fe1c
    // 0x91fe18: r9 = Null
    //     0x91fe18: mov             x9, NULL
    // 0x91fe1c: ldur            x3, [fp, #-8]
    // 0x91fe20: ldur            x8, [fp, #-0x10]
    // 0x91fe24: ldur            x7, [fp, #-0x18]
    // 0x91fe28: ldur            x6, [fp, #-0x20]
    // 0x91fe2c: ldur            x5, [fp, #-0x28]
    // 0x91fe30: ldur            x4, [fp, #-0x30]
    // 0x91fe34: stur            x9, [fp, #-0x38]
    // 0x91fe38: r0 = LoadClassIdInstr(r3)
    //     0x91fe38: ldur            x0, [x3, #-1]
    //     0x91fe3c: ubfx            x0, x0, #0xc, #0x14
    // 0x91fe40: mov             x1, x3
    // 0x91fe44: r2 = "published_at"
    //     0x91fe44: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d958] "published_at"
    //     0x91fe48: ldr             x2, [x2, #0x958]
    // 0x91fe4c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91fe4c: sub             lr, x0, #0x114
    //     0x91fe50: ldr             lr, [x21, lr, lsl #3]
    //     0x91fe54: blr             lr
    // 0x91fe58: mov             x3, x0
    // 0x91fe5c: r2 = Null
    //     0x91fe5c: mov             x2, NULL
    // 0x91fe60: r1 = Null
    //     0x91fe60: mov             x1, NULL
    // 0x91fe64: stur            x3, [fp, #-0x40]
    // 0x91fe68: r4 = 60
    //     0x91fe68: movz            x4, #0x3c
    // 0x91fe6c: branchIfSmi(r0, 0x91fe78)
    //     0x91fe6c: tbz             w0, #0, #0x91fe78
    // 0x91fe70: r4 = LoadClassIdInstr(r0)
    //     0x91fe70: ldur            x4, [x0, #-1]
    //     0x91fe74: ubfx            x4, x4, #0xc, #0x14
    // 0x91fe78: sub             x4, x4, #0x5e
    // 0x91fe7c: cmp             x4, #1
    // 0x91fe80: b.ls            #0x91fe94
    // 0x91fe84: r8 = String
    //     0x91fe84: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91fe88: r3 = Null
    //     0x91fe88: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fbf0] Null
    //     0x91fe8c: ldr             x3, [x3, #0xbf0]
    // 0x91fe90: r0 = String()
    //     0x91fe90: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91fe94: ldur            x1, [fp, #-0x40]
    // 0x91fe98: r0 = parse()
    //     0x91fe98: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x91fe9c: mov             x4, x0
    // 0x91fea0: ldur            x3, [fp, #-8]
    // 0x91fea4: stur            x4, [fp, #-0x40]
    // 0x91fea8: r0 = LoadClassIdInstr(r3)
    //     0x91fea8: ldur            x0, [x3, #-1]
    //     0x91feac: ubfx            x0, x0, #0xc, #0x14
    // 0x91feb0: mov             x1, x3
    // 0x91feb4: r2 = "title"
    //     0x91feb4: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x91feb8: ldr             x2, [x2, #0x748]
    // 0x91febc: r0 = GDT[cid_x0 + -0x114]()
    //     0x91febc: sub             lr, x0, #0x114
    //     0x91fec0: ldr             lr, [x21, lr, lsl #3]
    //     0x91fec4: blr             lr
    // 0x91fec8: mov             x3, x0
    // 0x91fecc: r2 = Null
    //     0x91fecc: mov             x2, NULL
    // 0x91fed0: r1 = Null
    //     0x91fed0: mov             x1, NULL
    // 0x91fed4: stur            x3, [fp, #-0x48]
    // 0x91fed8: r4 = 60
    //     0x91fed8: movz            x4, #0x3c
    // 0x91fedc: branchIfSmi(r0, 0x91fee8)
    //     0x91fedc: tbz             w0, #0, #0x91fee8
    // 0x91fee0: r4 = LoadClassIdInstr(r0)
    //     0x91fee0: ldur            x4, [x0, #-1]
    //     0x91fee4: ubfx            x4, x4, #0xc, #0x14
    // 0x91fee8: sub             x4, x4, #0x5e
    // 0x91feec: cmp             x4, #1
    // 0x91fef0: b.ls            #0x91ff04
    // 0x91fef4: r8 = String
    //     0x91fef4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91fef8: r3 = Null
    //     0x91fef8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fc00] Null
    //     0x91fefc: ldr             x3, [x3, #0xc00]
    // 0x91ff00: r0 = String()
    //     0x91ff00: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91ff04: ldur            x3, [fp, #-8]
    // 0x91ff08: r0 = LoadClassIdInstr(r3)
    //     0x91ff08: ldur            x0, [x3, #-1]
    //     0x91ff0c: ubfx            x0, x0, #0xc, #0x14
    // 0x91ff10: mov             x1, x3
    // 0x91ff14: r2 = "prefix"
    //     0x91ff14: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x91ff18: ldr             x2, [x2, #0x970]
    // 0x91ff1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91ff1c: sub             lr, x0, #0x114
    //     0x91ff20: ldr             lr, [x21, lr, lsl #3]
    //     0x91ff24: blr             lr
    // 0x91ff28: mov             x3, x0
    // 0x91ff2c: r2 = Null
    //     0x91ff2c: mov             x2, NULL
    // 0x91ff30: r1 = Null
    //     0x91ff30: mov             x1, NULL
    // 0x91ff34: stur            x3, [fp, #-0x50]
    // 0x91ff38: r4 = 60
    //     0x91ff38: movz            x4, #0x3c
    // 0x91ff3c: branchIfSmi(r0, 0x91ff48)
    //     0x91ff3c: tbz             w0, #0, #0x91ff48
    // 0x91ff40: r4 = LoadClassIdInstr(r0)
    //     0x91ff40: ldur            x4, [x0, #-1]
    //     0x91ff44: ubfx            x4, x4, #0xc, #0x14
    // 0x91ff48: sub             x4, x4, #0x5e
    // 0x91ff4c: cmp             x4, #1
    // 0x91ff50: b.ls            #0x91ff64
    // 0x91ff54: r8 = String?
    //     0x91ff54: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91ff58: r3 = Null
    //     0x91ff58: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fc10] Null
    //     0x91ff5c: ldr             x3, [x3, #0xc10]
    // 0x91ff60: r0 = String?()
    //     0x91ff60: bl              #0x600324  ; IsType_String?_Stub
    // 0x91ff64: ldur            x1, [fp, #-8]
    // 0x91ff68: r0 = LoadClassIdInstr(r1)
    //     0x91ff68: ldur            x0, [x1, #-1]
    //     0x91ff6c: ubfx            x0, x0, #0xc, #0x14
    // 0x91ff70: r2 = "subtitle"
    //     0x91ff70: add             x2, PP, #0xc, lsl #12  ; [pp+0xc568] "subtitle"
    //     0x91ff74: ldr             x2, [x2, #0x568]
    // 0x91ff78: r0 = GDT[cid_x0 + -0x114]()
    //     0x91ff78: sub             lr, x0, #0x114
    //     0x91ff7c: ldr             lr, [x21, lr, lsl #3]
    //     0x91ff80: blr             lr
    // 0x91ff84: mov             x3, x0
    // 0x91ff88: r2 = Null
    //     0x91ff88: mov             x2, NULL
    // 0x91ff8c: r1 = Null
    //     0x91ff8c: mov             x1, NULL
    // 0x91ff90: stur            x3, [fp, #-8]
    // 0x91ff94: r4 = 60
    //     0x91ff94: movz            x4, #0x3c
    // 0x91ff98: branchIfSmi(r0, 0x91ffa4)
    //     0x91ff98: tbz             w0, #0, #0x91ffa4
    // 0x91ff9c: r4 = LoadClassIdInstr(r0)
    //     0x91ff9c: ldur            x4, [x0, #-1]
    //     0x91ffa0: ubfx            x4, x4, #0xc, #0x14
    // 0x91ffa4: sub             x4, x4, #0x5e
    // 0x91ffa8: cmp             x4, #1
    // 0x91ffac: b.ls            #0x91ffc0
    // 0x91ffb0: r8 = String?
    //     0x91ffb0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91ffb4: r3 = Null
    //     0x91ffb4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fc20] Null
    //     0x91ffb8: ldr             x3, [x3, #0xc20]
    // 0x91ffbc: r0 = String?()
    //     0x91ffbc: bl              #0x600324  ; IsType_String?_Stub
    // 0x91ffc0: r0 = Countdown()
    //     0x91ffc0: bl              #0x920070  ; AllocateCountdownStub -> Countdown (size=0x44)
    // 0x91ffc4: mov             x1, x0
    // 0x91ffc8: ldur            x0, [fp, #-0x10]
    // 0x91ffcc: stur            x1, [fp, #-0x58]
    // 0x91ffd0: StoreField: r1->field_13 = r0
    //     0x91ffd0: stur            w0, [x1, #0x13]
    // 0x91ffd4: ldur            x0, [fp, #-0x18]
    // 0x91ffd8: ArrayStore: r1[0] = r0  ; List_4
    //     0x91ffd8: stur            w0, [x1, #0x17]
    // 0x91ffdc: ldur            x0, [fp, #-0x50]
    // 0x91ffe0: StoreField: r1->field_3b = r0
    //     0x91ffe0: stur            w0, [x1, #0x3b]
    // 0x91ffe4: ldur            x0, [fp, #-8]
    // 0x91ffe8: StoreField: r1->field_3f = r0
    //     0x91ffe8: stur            w0, [x1, #0x3f]
    // 0x91ffec: ldur            x0, [fp, #-0x48]
    // 0x91fff0: StoreField: r1->field_37 = r0
    //     0x91fff0: stur            w0, [x1, #0x37]
    // 0x91fff4: ldur            x0, [fp, #-0x28]
    // 0x91fff8: LoadField: d0 = r0->field_7
    //     0x91fff8: ldur            d0, [x0, #7]
    // 0x91fffc: StoreField: r1->field_1f = d0
    //     0x91fffc: stur            d0, [x1, #0x1f]
    // 0x920000: ldur            x0, [fp, #-0x30]
    // 0x920004: LoadField: d0 = r0->field_7
    //     0x920004: ldur            d0, [x0, #7]
    // 0x920008: StoreField: r1->field_27 = d0
    //     0x920008: stur            d0, [x1, #0x27]
    // 0x92000c: ldur            x0, [fp, #-0x20]
    // 0x920010: StoreField: r1->field_1b = r0
    //     0x920010: stur            w0, [x1, #0x1b]
    // 0x920014: ldur            x0, [fp, #-0x40]
    // 0x920018: StoreField: r1->field_2f = r0
    //     0x920018: stur            w0, [x1, #0x2f]
    // 0x92001c: ldur            x0, [fp, #-0x38]
    // 0x920020: StoreField: r1->field_33 = r0
    //     0x920020: stur            w0, [x1, #0x33]
    // 0x920024: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x920024: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x920028: ldr             x16, [x16, #0x9f8]
    // 0x92002c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x920030: stp             lr, x16, [SP]
    // 0x920034: r0 = Map._fromLiteral()
    //     0x920034: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x920038: ldur            x1, [fp, #-0x58]
    // 0x92003c: StoreField: r1->field_f = r0
    //     0x92003c: stur            w0, [x1, #0xf]
    //     0x920040: ldurb           w16, [x1, #-1]
    //     0x920044: ldurb           w17, [x0, #-1]
    //     0x920048: and             x16, x17, x16, lsr #2
    //     0x92004c: tst             x16, HEAP, lsr #32
    //     0x920050: b.eq            #0x920058
    //     0x920054: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x920058: mov             x0, x1
    // 0x92005c: LeaveFrame
    //     0x92005c: mov             SP, fp
    //     0x920060: ldp             fp, lr, [SP], #0x10
    // 0x920064: ret
    //     0x920064: ret             
    // 0x920068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92006c: b               #0x91fba4
  }
}

// class id: 1666, size: 0x14, field offset: 0xc
class CountdownAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa5fbc4, size: 0x6d0
    // 0xa5fbc4: EnterFrame
    //     0xa5fbc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa5fbc8: mov             fp, SP
    // 0xa5fbcc: AllocStack(0x88)
    //     0xa5fbcc: sub             SP, SP, #0x88
    // 0xa5fbd0: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa5fbd0: stur            x2, [fp, #-0x20]
    // 0xa5fbd4: CheckStackOverflow
    //     0xa5fbd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5fbd8: cmp             SP, x16
    //     0xa5fbdc: b.ls            #0xa6027c
    // 0xa5fbe0: LoadField: r3 = r2->field_23
    //     0xa5fbe0: ldur            x3, [x2, #0x23]
    // 0xa5fbe4: add             x0, x3, #1
    // 0xa5fbe8: LoadField: r1 = r2->field_1b
    //     0xa5fbe8: ldur            x1, [x2, #0x1b]
    // 0xa5fbec: cmp             x0, x1
    // 0xa5fbf0: b.gt            #0xa60220
    // 0xa5fbf4: LoadField: r4 = r2->field_7
    //     0xa5fbf4: ldur            w4, [x2, #7]
    // 0xa5fbf8: DecompressPointer r4
    //     0xa5fbf8: add             x4, x4, HEAP, lsl #32
    // 0xa5fbfc: stur            x4, [fp, #-0x18]
    // 0xa5fc00: StoreField: r2->field_23 = r0
    //     0xa5fc00: stur            x0, [x2, #0x23]
    // 0xa5fc04: LoadField: r0 = r4->field_13
    //     0xa5fc04: ldur            w0, [x4, #0x13]
    // 0xa5fc08: r5 = LoadInt32Instr(r0)
    //     0xa5fc08: sbfx            x5, x0, #1, #0x1f
    // 0xa5fc0c: mov             x0, x5
    // 0xa5fc10: mov             x1, x3
    // 0xa5fc14: stur            x5, [fp, #-0x10]
    // 0xa5fc18: cmp             x1, x0
    // 0xa5fc1c: b.hs            #0xa60284
    // 0xa5fc20: LoadField: r0 = r4->field_7
    //     0xa5fc20: ldur            x0, [x4, #7]
    // 0xa5fc24: ldrb            w1, [x0, x3]
    // 0xa5fc28: stur            x1, [fp, #-8]
    // 0xa5fc2c: r16 = <int, dynamic>
    //     0xa5fc2c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa5fc30: ldr             x16, [x16, #0xac0]
    // 0xa5fc34: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa5fc38: stp             lr, x16, [SP]
    // 0xa5fc3c: r0 = Map._fromLiteral()
    //     0xa5fc3c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa5fc40: mov             x2, x0
    // 0xa5fc44: stur            x2, [fp, #-0x38]
    // 0xa5fc48: r6 = 0
    //     0xa5fc48: movz            x6, #0
    // 0xa5fc4c: ldur            x3, [fp, #-0x20]
    // 0xa5fc50: ldur            x4, [fp, #-0x18]
    // 0xa5fc54: ldur            x5, [fp, #-8]
    // 0xa5fc58: stur            x6, [fp, #-0x30]
    // 0xa5fc5c: CheckStackOverflow
    //     0xa5fc5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5fc60: cmp             SP, x16
    //     0xa5fc64: b.ls            #0xa60288
    // 0xa5fc68: cmp             x6, x5
    // 0xa5fc6c: b.ge            #0xa5fcf8
    // 0xa5fc70: LoadField: r7 = r3->field_23
    //     0xa5fc70: ldur            x7, [x3, #0x23]
    // 0xa5fc74: add             x0, x7, #1
    // 0xa5fc78: LoadField: r1 = r3->field_1b
    //     0xa5fc78: ldur            x1, [x3, #0x1b]
    // 0xa5fc7c: cmp             x0, x1
    // 0xa5fc80: b.gt            #0xa60248
    // 0xa5fc84: StoreField: r3->field_23 = r0
    //     0xa5fc84: stur            x0, [x3, #0x23]
    // 0xa5fc88: ldur            x0, [fp, #-0x10]
    // 0xa5fc8c: mov             x1, x7
    // 0xa5fc90: cmp             x1, x0
    // 0xa5fc94: b.hs            #0xa60290
    // 0xa5fc98: LoadField: r0 = r4->field_7
    //     0xa5fc98: ldur            x0, [x4, #7]
    // 0xa5fc9c: ldrb            w8, [x0, x7]
    // 0xa5fca0: mov             x1, x3
    // 0xa5fca4: stur            x8, [fp, #-0x28]
    // 0xa5fca8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5fca8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa5fcac: r0 = read()
    //     0xa5fcac: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa5fcb0: mov             x1, x0
    // 0xa5fcb4: ldur            x0, [fp, #-0x28]
    // 0xa5fcb8: lsl             x2, x0, #1
    // 0xa5fcbc: r16 = LoadInt32Instr(r2)
    //     0xa5fcbc: sbfx            x16, x2, #1, #0x1f
    // 0xa5fcc0: r17 = 11601
    //     0xa5fcc0: movz            x17, #0x2d51
    // 0xa5fcc4: mul             x0, x16, x17
    // 0xa5fcc8: umulh           x16, x16, x17
    // 0xa5fccc: eor             x0, x0, x16
    // 0xa5fcd0: r0 = 0
    //     0xa5fcd0: eor             x0, x0, x0, lsr #32
    // 0xa5fcd4: ubfiz           x0, x0, #1, #0x1e
    // 0xa5fcd8: r5 = LoadInt32Instr(r0)
    //     0xa5fcd8: sbfx            x5, x0, #1, #0x1f
    // 0xa5fcdc: mov             x3, x1
    // 0xa5fce0: ldur            x1, [fp, #-0x38]
    // 0xa5fce4: r0 = _set()
    //     0xa5fce4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa5fce8: ldur            x0, [fp, #-0x30]
    // 0xa5fcec: add             x6, x0, #1
    // 0xa5fcf0: ldur            x2, [fp, #-0x38]
    // 0xa5fcf4: b               #0xa5fc4c
    // 0xa5fcf8: mov             x0, x2
    // 0xa5fcfc: mov             x1, x0
    // 0xa5fd00: r2 = 0
    //     0xa5fd00: movz            x2, #0
    // 0xa5fd04: r0 = _getValueOrData()
    //     0xa5fd04: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fd08: ldur            x3, [fp, #-0x38]
    // 0xa5fd0c: LoadField: r1 = r3->field_f
    //     0xa5fd0c: ldur            w1, [x3, #0xf]
    // 0xa5fd10: DecompressPointer r1
    //     0xa5fd10: add             x1, x1, HEAP, lsl #32
    // 0xa5fd14: cmp             w1, w0
    // 0xa5fd18: b.ne            #0xa5fd24
    // 0xa5fd1c: r4 = Null
    //     0xa5fd1c: mov             x4, NULL
    // 0xa5fd20: b               #0xa5fd28
    // 0xa5fd24: mov             x4, x0
    // 0xa5fd28: mov             x0, x4
    // 0xa5fd2c: stur            x4, [fp, #-0x18]
    // 0xa5fd30: r2 = Null
    //     0xa5fd30: mov             x2, NULL
    // 0xa5fd34: r1 = Null
    //     0xa5fd34: mov             x1, NULL
    // 0xa5fd38: r4 = 60
    //     0xa5fd38: movz            x4, #0x3c
    // 0xa5fd3c: branchIfSmi(r0, 0xa5fd48)
    //     0xa5fd3c: tbz             w0, #0, #0xa5fd48
    // 0xa5fd40: r4 = LoadClassIdInstr(r0)
    //     0xa5fd40: ldur            x4, [x0, #-1]
    //     0xa5fd44: ubfx            x4, x4, #0xc, #0x14
    // 0xa5fd48: sub             x4, x4, #0x5e
    // 0xa5fd4c: cmp             x4, #1
    // 0xa5fd50: b.ls            #0xa5fd64
    // 0xa5fd54: r8 = String?
    //     0xa5fd54: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5fd58: r3 = Null
    //     0xa5fd58: add             x3, PP, #0x21, lsl #12  ; [pp+0x21618] Null
    //     0xa5fd5c: ldr             x3, [x3, #0x618]
    // 0xa5fd60: r0 = String?()
    //     0xa5fd60: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5fd64: ldur            x1, [fp, #-0x38]
    // 0xa5fd68: r2 = 2
    //     0xa5fd68: movz            x2, #0x2
    // 0xa5fd6c: r0 = _getValueOrData()
    //     0xa5fd6c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fd70: ldur            x3, [fp, #-0x38]
    // 0xa5fd74: LoadField: r1 = r3->field_f
    //     0xa5fd74: ldur            w1, [x3, #0xf]
    // 0xa5fd78: DecompressPointer r1
    //     0xa5fd78: add             x1, x1, HEAP, lsl #32
    // 0xa5fd7c: cmp             w1, w0
    // 0xa5fd80: b.ne            #0xa5fd8c
    // 0xa5fd84: r4 = Null
    //     0xa5fd84: mov             x4, NULL
    // 0xa5fd88: b               #0xa5fd90
    // 0xa5fd8c: mov             x4, x0
    // 0xa5fd90: mov             x0, x4
    // 0xa5fd94: stur            x4, [fp, #-0x20]
    // 0xa5fd98: r2 = Null
    //     0xa5fd98: mov             x2, NULL
    // 0xa5fd9c: r1 = Null
    //     0xa5fd9c: mov             x1, NULL
    // 0xa5fda0: r8 = Map?
    //     0xa5fda0: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0xa5fda4: ldr             x8, [x8, #0x590]
    // 0xa5fda8: r3 = Null
    //     0xa5fda8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21628] Null
    //     0xa5fdac: ldr             x3, [x3, #0x628]
    // 0xa5fdb0: r0 = Map?()
    //     0xa5fdb0: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0xa5fdb4: ldur            x0, [fp, #-0x20]
    // 0xa5fdb8: cmp             w0, NULL
    // 0xa5fdbc: b.ne            #0xa5fdc8
    // 0xa5fdc0: r3 = Null
    //     0xa5fdc0: mov             x3, NULL
    // 0xa5fdc4: b               #0xa5fdf0
    // 0xa5fdc8: r1 = LoadClassIdInstr(r0)
    //     0xa5fdc8: ldur            x1, [x0, #-1]
    //     0xa5fdcc: ubfx            x1, x1, #0xc, #0x14
    // 0xa5fdd0: r16 = <String, dynamic>
    //     0xa5fdd0: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa5fdd4: stp             x0, x16, [SP]
    // 0xa5fdd8: mov             x0, x1
    // 0xa5fddc: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa5fddc: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa5fde0: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa5fde0: add             lr, x0, #0x5f4
    //     0xa5fde4: ldr             lr, [x21, lr, lsl #3]
    //     0xa5fde8: blr             lr
    // 0xa5fdec: mov             x3, x0
    // 0xa5fdf0: ldur            x0, [fp, #-0x38]
    // 0xa5fdf4: mov             x1, x0
    // 0xa5fdf8: stur            x3, [fp, #-0x20]
    // 0xa5fdfc: r2 = 16
    //     0xa5fdfc: movz            x2, #0x10
    // 0xa5fe00: r0 = _getValueOrData()
    //     0xa5fe00: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fe04: ldur            x3, [fp, #-0x38]
    // 0xa5fe08: LoadField: r1 = r3->field_f
    //     0xa5fe08: ldur            w1, [x3, #0xf]
    // 0xa5fe0c: DecompressPointer r1
    //     0xa5fe0c: add             x1, x1, HEAP, lsl #32
    // 0xa5fe10: cmp             w1, w0
    // 0xa5fe14: b.ne            #0xa5fe20
    // 0xa5fe18: r4 = Null
    //     0xa5fe18: mov             x4, NULL
    // 0xa5fe1c: b               #0xa5fe24
    // 0xa5fe20: mov             x4, x0
    // 0xa5fe24: mov             x0, x4
    // 0xa5fe28: stur            x4, [fp, #-0x40]
    // 0xa5fe2c: r2 = Null
    //     0xa5fe2c: mov             x2, NULL
    // 0xa5fe30: r1 = Null
    //     0xa5fe30: mov             x1, NULL
    // 0xa5fe34: r4 = 60
    //     0xa5fe34: movz            x4, #0x3c
    // 0xa5fe38: branchIfSmi(r0, 0xa5fe44)
    //     0xa5fe38: tbz             w0, #0, #0xa5fe44
    // 0xa5fe3c: r4 = LoadClassIdInstr(r0)
    //     0xa5fe3c: ldur            x4, [x0, #-1]
    //     0xa5fe40: ubfx            x4, x4, #0xc, #0x14
    // 0xa5fe44: sub             x4, x4, #0x5e
    // 0xa5fe48: cmp             x4, #1
    // 0xa5fe4c: b.ls            #0xa5fe60
    // 0xa5fe50: r8 = String?
    //     0xa5fe50: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5fe54: r3 = Null
    //     0xa5fe54: add             x3, PP, #0x21, lsl #12  ; [pp+0x21638] Null
    //     0xa5fe58: ldr             x3, [x3, #0x638]
    // 0xa5fe5c: r0 = String?()
    //     0xa5fe5c: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5fe60: ldur            x1, [fp, #-0x38]
    // 0xa5fe64: r2 = 18
    //     0xa5fe64: movz            x2, #0x12
    // 0xa5fe68: r0 = _getValueOrData()
    //     0xa5fe68: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fe6c: ldur            x3, [fp, #-0x38]
    // 0xa5fe70: LoadField: r1 = r3->field_f
    //     0xa5fe70: ldur            w1, [x3, #0xf]
    // 0xa5fe74: DecompressPointer r1
    //     0xa5fe74: add             x1, x1, HEAP, lsl #32
    // 0xa5fe78: cmp             w1, w0
    // 0xa5fe7c: b.ne            #0xa5fe88
    // 0xa5fe80: r4 = Null
    //     0xa5fe80: mov             x4, NULL
    // 0xa5fe84: b               #0xa5fe8c
    // 0xa5fe88: mov             x4, x0
    // 0xa5fe8c: mov             x0, x4
    // 0xa5fe90: stur            x4, [fp, #-0x48]
    // 0xa5fe94: r2 = Null
    //     0xa5fe94: mov             x2, NULL
    // 0xa5fe98: r1 = Null
    //     0xa5fe98: mov             x1, NULL
    // 0xa5fe9c: r4 = 60
    //     0xa5fe9c: movz            x4, #0x3c
    // 0xa5fea0: branchIfSmi(r0, 0xa5feac)
    //     0xa5fea0: tbz             w0, #0, #0xa5feac
    // 0xa5fea4: r4 = LoadClassIdInstr(r0)
    //     0xa5fea4: ldur            x4, [x0, #-1]
    //     0xa5fea8: ubfx            x4, x4, #0xc, #0x14
    // 0xa5feac: sub             x4, x4, #0x5e
    // 0xa5feb0: cmp             x4, #1
    // 0xa5feb4: b.ls            #0xa5fec8
    // 0xa5feb8: r8 = String?
    //     0xa5feb8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa5febc: r3 = Null
    //     0xa5febc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21648] Null
    //     0xa5fec0: ldr             x3, [x3, #0x648]
    // 0xa5fec4: r0 = String?()
    //     0xa5fec4: bl              #0x600324  ; IsType_String?_Stub
    // 0xa5fec8: ldur            x1, [fp, #-0x38]
    // 0xa5fecc: r2 = 14
    //     0xa5fecc: movz            x2, #0xe
    // 0xa5fed0: r0 = _getValueOrData()
    //     0xa5fed0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5fed4: ldur            x3, [fp, #-0x38]
    // 0xa5fed8: LoadField: r1 = r3->field_f
    //     0xa5fed8: ldur            w1, [x3, #0xf]
    // 0xa5fedc: DecompressPointer r1
    //     0xa5fedc: add             x1, x1, HEAP, lsl #32
    // 0xa5fee0: cmp             w1, w0
    // 0xa5fee4: b.ne            #0xa5fef0
    // 0xa5fee8: r4 = Null
    //     0xa5fee8: mov             x4, NULL
    // 0xa5feec: b               #0xa5fef4
    // 0xa5fef0: mov             x4, x0
    // 0xa5fef4: mov             x0, x4
    // 0xa5fef8: stur            x4, [fp, #-0x50]
    // 0xa5fefc: r2 = Null
    //     0xa5fefc: mov             x2, NULL
    // 0xa5ff00: r1 = Null
    //     0xa5ff00: mov             x1, NULL
    // 0xa5ff04: r4 = 60
    //     0xa5ff04: movz            x4, #0x3c
    // 0xa5ff08: branchIfSmi(r0, 0xa5ff14)
    //     0xa5ff08: tbz             w0, #0, #0xa5ff14
    // 0xa5ff0c: r4 = LoadClassIdInstr(r0)
    //     0xa5ff0c: ldur            x4, [x0, #-1]
    //     0xa5ff10: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ff14: sub             x4, x4, #0x5e
    // 0xa5ff18: cmp             x4, #1
    // 0xa5ff1c: b.ls            #0xa5ff30
    // 0xa5ff20: r8 = String
    //     0xa5ff20: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa5ff24: r3 = Null
    //     0xa5ff24: add             x3, PP, #0x21, lsl #12  ; [pp+0x21658] Null
    //     0xa5ff28: ldr             x3, [x3, #0x658]
    // 0xa5ff2c: r0 = String()
    //     0xa5ff2c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa5ff30: ldur            x1, [fp, #-0x38]
    // 0xa5ff34: r2 = 6
    //     0xa5ff34: movz            x2, #0x6
    // 0xa5ff38: r0 = _getValueOrData()
    //     0xa5ff38: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5ff3c: ldur            x3, [fp, #-0x38]
    // 0xa5ff40: LoadField: r1 = r3->field_f
    //     0xa5ff40: ldur            w1, [x3, #0xf]
    // 0xa5ff44: DecompressPointer r1
    //     0xa5ff44: add             x1, x1, HEAP, lsl #32
    // 0xa5ff48: cmp             w1, w0
    // 0xa5ff4c: b.ne            #0xa5ff58
    // 0xa5ff50: r4 = Null
    //     0xa5ff50: mov             x4, NULL
    // 0xa5ff54: b               #0xa5ff5c
    // 0xa5ff58: mov             x4, x0
    // 0xa5ff5c: mov             x0, x4
    // 0xa5ff60: stur            x4, [fp, #-0x58]
    // 0xa5ff64: r2 = Null
    //     0xa5ff64: mov             x2, NULL
    // 0xa5ff68: r1 = Null
    //     0xa5ff68: mov             x1, NULL
    // 0xa5ff6c: r4 = 60
    //     0xa5ff6c: movz            x4, #0x3c
    // 0xa5ff70: branchIfSmi(r0, 0xa5ff7c)
    //     0xa5ff70: tbz             w0, #0, #0xa5ff7c
    // 0xa5ff74: r4 = LoadClassIdInstr(r0)
    //     0xa5ff74: ldur            x4, [x0, #-1]
    //     0xa5ff78: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ff7c: cmp             x4, #0x3e
    // 0xa5ff80: b.eq            #0xa5ff94
    // 0xa5ff84: r8 = double
    //     0xa5ff84: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa5ff88: r3 = Null
    //     0xa5ff88: add             x3, PP, #0x21, lsl #12  ; [pp+0x21668] Null
    //     0xa5ff8c: ldr             x3, [x3, #0x668]
    // 0xa5ff90: r0 = double()
    //     0xa5ff90: bl              #0xed4460  ; IsType_double_Stub
    // 0xa5ff94: ldur            x1, [fp, #-0x38]
    // 0xa5ff98: r2 = 8
    //     0xa5ff98: movz            x2, #0x8
    // 0xa5ff9c: r0 = _getValueOrData()
    //     0xa5ff9c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5ffa0: ldur            x3, [fp, #-0x38]
    // 0xa5ffa4: LoadField: r1 = r3->field_f
    //     0xa5ffa4: ldur            w1, [x3, #0xf]
    // 0xa5ffa8: DecompressPointer r1
    //     0xa5ffa8: add             x1, x1, HEAP, lsl #32
    // 0xa5ffac: cmp             w1, w0
    // 0xa5ffb0: b.ne            #0xa5ffbc
    // 0xa5ffb4: r4 = Null
    //     0xa5ffb4: mov             x4, NULL
    // 0xa5ffb8: b               #0xa5ffc0
    // 0xa5ffbc: mov             x4, x0
    // 0xa5ffc0: mov             x0, x4
    // 0xa5ffc4: stur            x4, [fp, #-0x60]
    // 0xa5ffc8: r2 = Null
    //     0xa5ffc8: mov             x2, NULL
    // 0xa5ffcc: r1 = Null
    //     0xa5ffcc: mov             x1, NULL
    // 0xa5ffd0: r4 = 60
    //     0xa5ffd0: movz            x4, #0x3c
    // 0xa5ffd4: branchIfSmi(r0, 0xa5ffe0)
    //     0xa5ffd4: tbz             w0, #0, #0xa5ffe0
    // 0xa5ffd8: r4 = LoadClassIdInstr(r0)
    //     0xa5ffd8: ldur            x4, [x0, #-1]
    //     0xa5ffdc: ubfx            x4, x4, #0xc, #0x14
    // 0xa5ffe0: cmp             x4, #0x3e
    // 0xa5ffe4: b.eq            #0xa5fff8
    // 0xa5ffe8: r8 = double
    //     0xa5ffe8: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa5ffec: r3 = Null
    //     0xa5ffec: add             x3, PP, #0x21, lsl #12  ; [pp+0x21678] Null
    //     0xa5fff0: ldr             x3, [x3, #0x678]
    // 0xa5fff4: r0 = double()
    //     0xa5fff4: bl              #0xed4460  ; IsType_double_Stub
    // 0xa5fff8: ldur            x1, [fp, #-0x38]
    // 0xa5fffc: r2 = 4
    //     0xa5fffc: movz            x2, #0x4
    // 0xa60000: r0 = _getValueOrData()
    //     0xa60000: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60004: ldur            x3, [fp, #-0x38]
    // 0xa60008: LoadField: r1 = r3->field_f
    //     0xa60008: ldur            w1, [x3, #0xf]
    // 0xa6000c: DecompressPointer r1
    //     0xa6000c: add             x1, x1, HEAP, lsl #32
    // 0xa60010: cmp             w1, w0
    // 0xa60014: b.ne            #0xa60020
    // 0xa60018: r4 = Null
    //     0xa60018: mov             x4, NULL
    // 0xa6001c: b               #0xa60024
    // 0xa60020: mov             x4, x0
    // 0xa60024: mov             x0, x4
    // 0xa60028: stur            x4, [fp, #-0x68]
    // 0xa6002c: r2 = Null
    //     0xa6002c: mov             x2, NULL
    // 0xa60030: r1 = Null
    //     0xa60030: mov             x1, NULL
    // 0xa60034: r4 = 60
    //     0xa60034: movz            x4, #0x3c
    // 0xa60038: branchIfSmi(r0, 0xa60044)
    //     0xa60038: tbz             w0, #0, #0xa60044
    // 0xa6003c: r4 = LoadClassIdInstr(r0)
    //     0xa6003c: ldur            x4, [x0, #-1]
    //     0xa60040: ubfx            x4, x4, #0xc, #0x14
    // 0xa60044: sub             x4, x4, #0x5e
    // 0xa60048: cmp             x4, #1
    // 0xa6004c: b.ls            #0xa60060
    // 0xa60050: r8 = String
    //     0xa60050: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60054: r3 = Null
    //     0xa60054: add             x3, PP, #0x21, lsl #12  ; [pp+0x21688] Null
    //     0xa60058: ldr             x3, [x3, #0x688]
    // 0xa6005c: r0 = String()
    //     0xa6005c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60060: ldur            x1, [fp, #-0x38]
    // 0xa60064: r2 = 10
    //     0xa60064: movz            x2, #0xa
    // 0xa60068: r0 = _getValueOrData()
    //     0xa60068: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6006c: ldur            x3, [fp, #-0x38]
    // 0xa60070: LoadField: r1 = r3->field_f
    //     0xa60070: ldur            w1, [x3, #0xf]
    // 0xa60074: DecompressPointer r1
    //     0xa60074: add             x1, x1, HEAP, lsl #32
    // 0xa60078: cmp             w1, w0
    // 0xa6007c: b.ne            #0xa60088
    // 0xa60080: r4 = Null
    //     0xa60080: mov             x4, NULL
    // 0xa60084: b               #0xa6008c
    // 0xa60088: mov             x4, x0
    // 0xa6008c: mov             x0, x4
    // 0xa60090: stur            x4, [fp, #-0x70]
    // 0xa60094: r2 = Null
    //     0xa60094: mov             x2, NULL
    // 0xa60098: r1 = Null
    //     0xa60098: mov             x1, NULL
    // 0xa6009c: r4 = 60
    //     0xa6009c: movz            x4, #0x3c
    // 0xa600a0: branchIfSmi(r0, 0xa600ac)
    //     0xa600a0: tbz             w0, #0, #0xa600ac
    // 0xa600a4: r4 = LoadClassIdInstr(r0)
    //     0xa600a4: ldur            x4, [x0, #-1]
    //     0xa600a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa600ac: cmp             x4, #0x1ae
    // 0xa600b0: b.eq            #0xa600d8
    // 0xa600b4: r17 = -7188
    //     0xa600b4: movn            x17, #0x1c13
    // 0xa600b8: add             x4, x4, x17
    // 0xa600bc: cmp             x4, #1
    // 0xa600c0: b.ls            #0xa600d8
    // 0xa600c4: r8 = DateTime
    //     0xa600c4: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a290] Type: DateTime
    //     0xa600c8: ldr             x8, [x8, #0x290]
    // 0xa600cc: r3 = Null
    //     0xa600cc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21698] Null
    //     0xa600d0: ldr             x3, [x3, #0x698]
    // 0xa600d4: r0 = DateTime()
    //     0xa600d4: bl              #0x615e38  ; IsType_DateTime_Stub
    // 0xa600d8: ldur            x1, [fp, #-0x38]
    // 0xa600dc: r2 = 12
    //     0xa600dc: movz            x2, #0xc
    // 0xa600e0: r0 = _getValueOrData()
    //     0xa600e0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa600e4: mov             x1, x0
    // 0xa600e8: ldur            x0, [fp, #-0x38]
    // 0xa600ec: LoadField: r2 = r0->field_f
    //     0xa600ec: ldur            w2, [x0, #0xf]
    // 0xa600f0: DecompressPointer r2
    //     0xa600f0: add             x2, x2, HEAP, lsl #32
    // 0xa600f4: cmp             w2, w1
    // 0xa600f8: b.ne            #0xa60104
    // 0xa600fc: r12 = Null
    //     0xa600fc: mov             x12, NULL
    // 0xa60100: b               #0xa60108
    // 0xa60104: mov             x12, x1
    // 0xa60108: ldur            x10, [fp, #-0x20]
    // 0xa6010c: ldur            x9, [fp, #-0x40]
    // 0xa60110: ldur            x8, [fp, #-0x48]
    // 0xa60114: ldur            x7, [fp, #-0x50]
    // 0xa60118: ldur            x6, [fp, #-0x58]
    // 0xa6011c: ldur            x5, [fp, #-0x60]
    // 0xa60120: ldur            x4, [fp, #-0x68]
    // 0xa60124: ldur            x3, [fp, #-0x70]
    // 0xa60128: ldur            x11, [fp, #-0x18]
    // 0xa6012c: mov             x0, x12
    // 0xa60130: stur            x12, [fp, #-0x38]
    // 0xa60134: r2 = Null
    //     0xa60134: mov             x2, NULL
    // 0xa60138: r1 = Null
    //     0xa60138: mov             x1, NULL
    // 0xa6013c: r4 = 60
    //     0xa6013c: movz            x4, #0x3c
    // 0xa60140: branchIfSmi(r0, 0xa6014c)
    //     0xa60140: tbz             w0, #0, #0xa6014c
    // 0xa60144: r4 = LoadClassIdInstr(r0)
    //     0xa60144: ldur            x4, [x0, #-1]
    //     0xa60148: ubfx            x4, x4, #0xc, #0x14
    // 0xa6014c: cmp             x4, #0x1ae
    // 0xa60150: b.eq            #0xa60178
    // 0xa60154: r17 = -7188
    //     0xa60154: movn            x17, #0x1c13
    // 0xa60158: add             x4, x4, x17
    // 0xa6015c: cmp             x4, #1
    // 0xa60160: b.ls            #0xa60178
    // 0xa60164: r8 = DateTime?
    //     0xa60164: add             x8, PP, #0x21, lsl #12  ; [pp+0x21018] Type: DateTime?
    //     0xa60168: ldr             x8, [x8, #0x18]
    // 0xa6016c: r3 = Null
    //     0xa6016c: add             x3, PP, #0x21, lsl #12  ; [pp+0x216a8] Null
    //     0xa60170: ldr             x3, [x3, #0x6a8]
    // 0xa60174: r0 = DefaultNullableTypeTest()
    //     0xa60174: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0xa60178: r0 = Countdown()
    //     0xa60178: bl              #0x920070  ; AllocateCountdownStub -> Countdown (size=0x44)
    // 0xa6017c: mov             x1, x0
    // 0xa60180: ldur            x0, [fp, #-0x18]
    // 0xa60184: stur            x1, [fp, #-0x78]
    // 0xa60188: StoreField: r1->field_13 = r0
    //     0xa60188: stur            w0, [x1, #0x13]
    // 0xa6018c: ldur            x0, [fp, #-0x20]
    // 0xa60190: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60190: stur            w0, [x1, #0x17]
    // 0xa60194: ldur            x0, [fp, #-0x40]
    // 0xa60198: StoreField: r1->field_3b = r0
    //     0xa60198: stur            w0, [x1, #0x3b]
    // 0xa6019c: ldur            x0, [fp, #-0x48]
    // 0xa601a0: StoreField: r1->field_3f = r0
    //     0xa601a0: stur            w0, [x1, #0x3f]
    // 0xa601a4: ldur            x0, [fp, #-0x50]
    // 0xa601a8: StoreField: r1->field_37 = r0
    //     0xa601a8: stur            w0, [x1, #0x37]
    // 0xa601ac: ldur            x0, [fp, #-0x58]
    // 0xa601b0: LoadField: d0 = r0->field_7
    //     0xa601b0: ldur            d0, [x0, #7]
    // 0xa601b4: StoreField: r1->field_1f = d0
    //     0xa601b4: stur            d0, [x1, #0x1f]
    // 0xa601b8: ldur            x0, [fp, #-0x60]
    // 0xa601bc: LoadField: d0 = r0->field_7
    //     0xa601bc: ldur            d0, [x0, #7]
    // 0xa601c0: StoreField: r1->field_27 = d0
    //     0xa601c0: stur            d0, [x1, #0x27]
    // 0xa601c4: ldur            x0, [fp, #-0x68]
    // 0xa601c8: StoreField: r1->field_1b = r0
    //     0xa601c8: stur            w0, [x1, #0x1b]
    // 0xa601cc: ldur            x0, [fp, #-0x70]
    // 0xa601d0: StoreField: r1->field_2f = r0
    //     0xa601d0: stur            w0, [x1, #0x2f]
    // 0xa601d4: ldur            x0, [fp, #-0x38]
    // 0xa601d8: StoreField: r1->field_33 = r0
    //     0xa601d8: stur            w0, [x1, #0x33]
    // 0xa601dc: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa601dc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa601e0: ldr             x16, [x16, #0x9f8]
    // 0xa601e4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa601e8: stp             lr, x16, [SP]
    // 0xa601ec: r0 = Map._fromLiteral()
    //     0xa601ec: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa601f0: ldur            x1, [fp, #-0x78]
    // 0xa601f4: StoreField: r1->field_f = r0
    //     0xa601f4: stur            w0, [x1, #0xf]
    //     0xa601f8: ldurb           w16, [x1, #-1]
    //     0xa601fc: ldurb           w17, [x0, #-1]
    //     0xa60200: and             x16, x17, x16, lsr #2
    //     0xa60204: tst             x16, HEAP, lsr #32
    //     0xa60208: b.eq            #0xa60210
    //     0xa6020c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa60210: mov             x0, x1
    // 0xa60214: LeaveFrame
    //     0xa60214: mov             SP, fp
    //     0xa60218: ldp             fp, lr, [SP], #0x10
    // 0xa6021c: ret
    //     0xa6021c: ret             
    // 0xa60220: r0 = RangeError()
    //     0xa60220: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa60224: mov             x1, x0
    // 0xa60228: r0 = "Not enough bytes available."
    //     0xa60228: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6022c: ldr             x0, [x0, #0x8a8]
    // 0xa60230: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60230: stur            w0, [x1, #0x17]
    // 0xa60234: r2 = false
    //     0xa60234: add             x2, NULL, #0x30  ; false
    // 0xa60238: StoreField: r1->field_b = r2
    //     0xa60238: stur            w2, [x1, #0xb]
    // 0xa6023c: mov             x0, x1
    // 0xa60240: r0 = Throw()
    //     0xa60240: bl              #0xec04b8  ; ThrowStub
    // 0xa60244: brk             #0
    // 0xa60248: r0 = "Not enough bytes available."
    //     0xa60248: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6024c: ldr             x0, [x0, #0x8a8]
    // 0xa60250: r2 = false
    //     0xa60250: add             x2, NULL, #0x30  ; false
    // 0xa60254: r0 = RangeError()
    //     0xa60254: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa60258: mov             x1, x0
    // 0xa6025c: r0 = "Not enough bytes available."
    //     0xa6025c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60260: ldr             x0, [x0, #0x8a8]
    // 0xa60264: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60264: stur            w0, [x1, #0x17]
    // 0xa60268: r0 = false
    //     0xa60268: add             x0, NULL, #0x30  ; false
    // 0xa6026c: StoreField: r1->field_b = r0
    //     0xa6026c: stur            w0, [x1, #0xb]
    // 0xa60270: mov             x0, x1
    // 0xa60274: r0 = Throw()
    //     0xa60274: bl              #0xec04b8  ; ThrowStub
    // 0xa60278: brk             #0
    // 0xa6027c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6027c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60280: b               #0xa5fbe0
    // 0xa60284: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60284: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa60288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60288: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6028c: b               #0xa5fc68
    // 0xa60290: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60290: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd1134, size: 0x6ec
    // 0xbd1134: EnterFrame
    //     0xbd1134: stp             fp, lr, [SP, #-0x10]!
    //     0xbd1138: mov             fp, SP
    // 0xbd113c: AllocStack(0x28)
    //     0xbd113c: sub             SP, SP, #0x28
    // 0xbd1140: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd1140: mov             x4, x2
    //     0xbd1144: stur            x2, [fp, #-8]
    //     0xbd1148: stur            x3, [fp, #-0x10]
    // 0xbd114c: CheckStackOverflow
    //     0xbd114c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd1150: cmp             SP, x16
    //     0xbd1154: b.ls            #0xbd17bc
    // 0xbd1158: mov             x0, x3
    // 0xbd115c: r2 = Null
    //     0xbd115c: mov             x2, NULL
    // 0xbd1160: r1 = Null
    //     0xbd1160: mov             x1, NULL
    // 0xbd1164: r4 = 60
    //     0xbd1164: movz            x4, #0x3c
    // 0xbd1168: branchIfSmi(r0, 0xbd1174)
    //     0xbd1168: tbz             w0, #0, #0xbd1174
    // 0xbd116c: r4 = LoadClassIdInstr(r0)
    //     0xbd116c: ldur            x4, [x0, #-1]
    //     0xbd1170: ubfx            x4, x4, #0xc, #0x14
    // 0xbd1174: cmp             x4, #0x648
    // 0xbd1178: b.eq            #0xbd1190
    // 0xbd117c: r8 = Countdown
    //     0xbd117c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b438] Type: Countdown
    //     0xbd1180: ldr             x8, [x8, #0x438]
    // 0xbd1184: r3 = Null
    //     0xbd1184: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b440] Null
    //     0xbd1188: ldr             x3, [x3, #0x440]
    // 0xbd118c: r0 = Countdown()
    //     0xbd118c: bl              #0x836004  ; IsType_Countdown_Stub
    // 0xbd1190: ldur            x0, [fp, #-8]
    // 0xbd1194: LoadField: r1 = r0->field_b
    //     0xbd1194: ldur            w1, [x0, #0xb]
    // 0xbd1198: DecompressPointer r1
    //     0xbd1198: add             x1, x1, HEAP, lsl #32
    // 0xbd119c: LoadField: r2 = r1->field_13
    //     0xbd119c: ldur            w2, [x1, #0x13]
    // 0xbd11a0: LoadField: r1 = r0->field_13
    //     0xbd11a0: ldur            x1, [x0, #0x13]
    // 0xbd11a4: r3 = LoadInt32Instr(r2)
    //     0xbd11a4: sbfx            x3, x2, #1, #0x1f
    // 0xbd11a8: sub             x2, x3, x1
    // 0xbd11ac: cmp             x2, #1
    // 0xbd11b0: b.ge            #0xbd11c0
    // 0xbd11b4: mov             x1, x0
    // 0xbd11b8: r2 = 1
    //     0xbd11b8: movz            x2, #0x1
    // 0xbd11bc: r0 = _increaseBufferSize()
    //     0xbd11bc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd11c0: ldur            x3, [fp, #-8]
    // 0xbd11c4: r2 = 10
    //     0xbd11c4: movz            x2, #0xa
    // 0xbd11c8: LoadField: r4 = r3->field_b
    //     0xbd11c8: ldur            w4, [x3, #0xb]
    // 0xbd11cc: DecompressPointer r4
    //     0xbd11cc: add             x4, x4, HEAP, lsl #32
    // 0xbd11d0: LoadField: r5 = r3->field_13
    //     0xbd11d0: ldur            x5, [x3, #0x13]
    // 0xbd11d4: add             x6, x5, #1
    // 0xbd11d8: StoreField: r3->field_13 = r6
    //     0xbd11d8: stur            x6, [x3, #0x13]
    // 0xbd11dc: LoadField: r0 = r4->field_13
    //     0xbd11dc: ldur            w0, [x4, #0x13]
    // 0xbd11e0: r7 = LoadInt32Instr(r0)
    //     0xbd11e0: sbfx            x7, x0, #1, #0x1f
    // 0xbd11e4: mov             x0, x7
    // 0xbd11e8: mov             x1, x5
    // 0xbd11ec: cmp             x1, x0
    // 0xbd11f0: b.hs            #0xbd17c4
    // 0xbd11f4: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd11f4: add             x0, x4, x5
    //     0xbd11f8: strb            w2, [x0, #0x17]
    // 0xbd11fc: sub             x0, x7, x6
    // 0xbd1200: cmp             x0, #1
    // 0xbd1204: b.ge            #0xbd1214
    // 0xbd1208: mov             x1, x3
    // 0xbd120c: r2 = 1
    //     0xbd120c: movz            x2, #0x1
    // 0xbd1210: r0 = _increaseBufferSize()
    //     0xbd1210: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1214: ldur            x2, [fp, #-8]
    // 0xbd1218: ldur            x3, [fp, #-0x10]
    // 0xbd121c: LoadField: r4 = r2->field_b
    //     0xbd121c: ldur            w4, [x2, #0xb]
    // 0xbd1220: DecompressPointer r4
    //     0xbd1220: add             x4, x4, HEAP, lsl #32
    // 0xbd1224: LoadField: r5 = r2->field_13
    //     0xbd1224: ldur            x5, [x2, #0x13]
    // 0xbd1228: add             x0, x5, #1
    // 0xbd122c: StoreField: r2->field_13 = r0
    //     0xbd122c: stur            x0, [x2, #0x13]
    // 0xbd1230: LoadField: r0 = r4->field_13
    //     0xbd1230: ldur            w0, [x4, #0x13]
    // 0xbd1234: r1 = LoadInt32Instr(r0)
    //     0xbd1234: sbfx            x1, x0, #1, #0x1f
    // 0xbd1238: mov             x0, x1
    // 0xbd123c: mov             x1, x5
    // 0xbd1240: cmp             x1, x0
    // 0xbd1244: b.hs            #0xbd17c8
    // 0xbd1248: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd1248: add             x0, x4, x5
    //     0xbd124c: strb            wzr, [x0, #0x17]
    // 0xbd1250: LoadField: r0 = r3->field_13
    //     0xbd1250: ldur            w0, [x3, #0x13]
    // 0xbd1254: DecompressPointer r0
    //     0xbd1254: add             x0, x0, HEAP, lsl #32
    // 0xbd1258: r16 = <String?>
    //     0xbd1258: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd125c: stp             x2, x16, [SP, #8]
    // 0xbd1260: str             x0, [SP]
    // 0xbd1264: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1264: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1268: r0 = write()
    //     0xbd1268: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd126c: ldur            x0, [fp, #-8]
    // 0xbd1270: LoadField: r1 = r0->field_b
    //     0xbd1270: ldur            w1, [x0, #0xb]
    // 0xbd1274: DecompressPointer r1
    //     0xbd1274: add             x1, x1, HEAP, lsl #32
    // 0xbd1278: LoadField: r2 = r1->field_13
    //     0xbd1278: ldur            w2, [x1, #0x13]
    // 0xbd127c: LoadField: r1 = r0->field_13
    //     0xbd127c: ldur            x1, [x0, #0x13]
    // 0xbd1280: r3 = LoadInt32Instr(r2)
    //     0xbd1280: sbfx            x3, x2, #1, #0x1f
    // 0xbd1284: sub             x2, x3, x1
    // 0xbd1288: cmp             x2, #1
    // 0xbd128c: b.ge            #0xbd129c
    // 0xbd1290: mov             x1, x0
    // 0xbd1294: r2 = 1
    //     0xbd1294: movz            x2, #0x1
    // 0xbd1298: r0 = _increaseBufferSize()
    //     0xbd1298: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd129c: ldur            x2, [fp, #-8]
    // 0xbd12a0: ldur            x3, [fp, #-0x10]
    // 0xbd12a4: r4 = 1
    //     0xbd12a4: movz            x4, #0x1
    // 0xbd12a8: LoadField: r5 = r2->field_b
    //     0xbd12a8: ldur            w5, [x2, #0xb]
    // 0xbd12ac: DecompressPointer r5
    //     0xbd12ac: add             x5, x5, HEAP, lsl #32
    // 0xbd12b0: LoadField: r6 = r2->field_13
    //     0xbd12b0: ldur            x6, [x2, #0x13]
    // 0xbd12b4: add             x0, x6, #1
    // 0xbd12b8: StoreField: r2->field_13 = r0
    //     0xbd12b8: stur            x0, [x2, #0x13]
    // 0xbd12bc: LoadField: r0 = r5->field_13
    //     0xbd12bc: ldur            w0, [x5, #0x13]
    // 0xbd12c0: r1 = LoadInt32Instr(r0)
    //     0xbd12c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd12c4: mov             x0, x1
    // 0xbd12c8: mov             x1, x6
    // 0xbd12cc: cmp             x1, x0
    // 0xbd12d0: b.hs            #0xbd17cc
    // 0xbd12d4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd12d4: add             x0, x5, x6
    //     0xbd12d8: strb            w4, [x0, #0x17]
    // 0xbd12dc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd12dc: ldur            w0, [x3, #0x17]
    // 0xbd12e0: DecompressPointer r0
    //     0xbd12e0: add             x0, x0, HEAP, lsl #32
    // 0xbd12e4: r16 = <Map<String, dynamic>?>
    //     0xbd12e4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b180] TypeArguments: <Map<String, dynamic>?>
    //     0xbd12e8: ldr             x16, [x16, #0x180]
    // 0xbd12ec: stp             x2, x16, [SP, #8]
    // 0xbd12f0: str             x0, [SP]
    // 0xbd12f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd12f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd12f8: r0 = write()
    //     0xbd12f8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd12fc: ldur            x0, [fp, #-8]
    // 0xbd1300: LoadField: r1 = r0->field_b
    //     0xbd1300: ldur            w1, [x0, #0xb]
    // 0xbd1304: DecompressPointer r1
    //     0xbd1304: add             x1, x1, HEAP, lsl #32
    // 0xbd1308: LoadField: r2 = r1->field_13
    //     0xbd1308: ldur            w2, [x1, #0x13]
    // 0xbd130c: LoadField: r1 = r0->field_13
    //     0xbd130c: ldur            x1, [x0, #0x13]
    // 0xbd1310: r3 = LoadInt32Instr(r2)
    //     0xbd1310: sbfx            x3, x2, #1, #0x1f
    // 0xbd1314: sub             x2, x3, x1
    // 0xbd1318: cmp             x2, #1
    // 0xbd131c: b.ge            #0xbd132c
    // 0xbd1320: mov             x1, x0
    // 0xbd1324: r2 = 1
    //     0xbd1324: movz            x2, #0x1
    // 0xbd1328: r0 = _increaseBufferSize()
    //     0xbd1328: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd132c: ldur            x2, [fp, #-8]
    // 0xbd1330: ldur            x3, [fp, #-0x10]
    // 0xbd1334: r4 = 2
    //     0xbd1334: movz            x4, #0x2
    // 0xbd1338: LoadField: r5 = r2->field_b
    //     0xbd1338: ldur            w5, [x2, #0xb]
    // 0xbd133c: DecompressPointer r5
    //     0xbd133c: add             x5, x5, HEAP, lsl #32
    // 0xbd1340: LoadField: r6 = r2->field_13
    //     0xbd1340: ldur            x6, [x2, #0x13]
    // 0xbd1344: add             x0, x6, #1
    // 0xbd1348: StoreField: r2->field_13 = r0
    //     0xbd1348: stur            x0, [x2, #0x13]
    // 0xbd134c: LoadField: r0 = r5->field_13
    //     0xbd134c: ldur            w0, [x5, #0x13]
    // 0xbd1350: r1 = LoadInt32Instr(r0)
    //     0xbd1350: sbfx            x1, x0, #1, #0x1f
    // 0xbd1354: mov             x0, x1
    // 0xbd1358: mov             x1, x6
    // 0xbd135c: cmp             x1, x0
    // 0xbd1360: b.hs            #0xbd17d0
    // 0xbd1364: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1364: add             x0, x5, x6
    //     0xbd1368: strb            w4, [x0, #0x17]
    // 0xbd136c: LoadField: r0 = r3->field_1b
    //     0xbd136c: ldur            w0, [x3, #0x1b]
    // 0xbd1370: DecompressPointer r0
    //     0xbd1370: add             x0, x0, HEAP, lsl #32
    // 0xbd1374: r16 = <String>
    //     0xbd1374: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd1378: stp             x2, x16, [SP, #8]
    // 0xbd137c: str             x0, [SP]
    // 0xbd1380: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1380: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1384: r0 = write()
    //     0xbd1384: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1388: ldur            x0, [fp, #-8]
    // 0xbd138c: LoadField: r1 = r0->field_b
    //     0xbd138c: ldur            w1, [x0, #0xb]
    // 0xbd1390: DecompressPointer r1
    //     0xbd1390: add             x1, x1, HEAP, lsl #32
    // 0xbd1394: LoadField: r2 = r1->field_13
    //     0xbd1394: ldur            w2, [x1, #0x13]
    // 0xbd1398: LoadField: r1 = r0->field_13
    //     0xbd1398: ldur            x1, [x0, #0x13]
    // 0xbd139c: r3 = LoadInt32Instr(r2)
    //     0xbd139c: sbfx            x3, x2, #1, #0x1f
    // 0xbd13a0: sub             x2, x3, x1
    // 0xbd13a4: cmp             x2, #1
    // 0xbd13a8: b.ge            #0xbd13b8
    // 0xbd13ac: mov             x1, x0
    // 0xbd13b0: r2 = 1
    //     0xbd13b0: movz            x2, #0x1
    // 0xbd13b4: r0 = _increaseBufferSize()
    //     0xbd13b4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd13b8: ldur            x2, [fp, #-8]
    // 0xbd13bc: ldur            x3, [fp, #-0x10]
    // 0xbd13c0: r4 = 3
    //     0xbd13c0: movz            x4, #0x3
    // 0xbd13c4: LoadField: r5 = r2->field_b
    //     0xbd13c4: ldur            w5, [x2, #0xb]
    // 0xbd13c8: DecompressPointer r5
    //     0xbd13c8: add             x5, x5, HEAP, lsl #32
    // 0xbd13cc: LoadField: r6 = r2->field_13
    //     0xbd13cc: ldur            x6, [x2, #0x13]
    // 0xbd13d0: add             x0, x6, #1
    // 0xbd13d4: StoreField: r2->field_13 = r0
    //     0xbd13d4: stur            x0, [x2, #0x13]
    // 0xbd13d8: LoadField: r0 = r5->field_13
    //     0xbd13d8: ldur            w0, [x5, #0x13]
    // 0xbd13dc: r1 = LoadInt32Instr(r0)
    //     0xbd13dc: sbfx            x1, x0, #1, #0x1f
    // 0xbd13e0: mov             x0, x1
    // 0xbd13e4: mov             x1, x6
    // 0xbd13e8: cmp             x1, x0
    // 0xbd13ec: b.hs            #0xbd17d4
    // 0xbd13f0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd13f0: add             x0, x5, x6
    //     0xbd13f4: strb            w4, [x0, #0x17]
    // 0xbd13f8: LoadField: d0 = r3->field_1f
    //     0xbd13f8: ldur            d0, [x3, #0x1f]
    // 0xbd13fc: r0 = inline_Allocate_Double()
    //     0xbd13fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd1400: add             x0, x0, #0x10
    //     0xbd1404: cmp             x1, x0
    //     0xbd1408: b.ls            #0xbd17d8
    //     0xbd140c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd1410: sub             x0, x0, #0xf
    //     0xbd1414: movz            x1, #0xe15c
    //     0xbd1418: movk            x1, #0x3, lsl #16
    //     0xbd141c: stur            x1, [x0, #-1]
    // 0xbd1420: StoreField: r0->field_7 = d0
    //     0xbd1420: stur            d0, [x0, #7]
    // 0xbd1424: r16 = <double>
    //     0xbd1424: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd1428: stp             x2, x16, [SP, #8]
    // 0xbd142c: str             x0, [SP]
    // 0xbd1430: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1430: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1434: r0 = write()
    //     0xbd1434: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1438: ldur            x0, [fp, #-8]
    // 0xbd143c: LoadField: r1 = r0->field_b
    //     0xbd143c: ldur            w1, [x0, #0xb]
    // 0xbd1440: DecompressPointer r1
    //     0xbd1440: add             x1, x1, HEAP, lsl #32
    // 0xbd1444: LoadField: r2 = r1->field_13
    //     0xbd1444: ldur            w2, [x1, #0x13]
    // 0xbd1448: LoadField: r1 = r0->field_13
    //     0xbd1448: ldur            x1, [x0, #0x13]
    // 0xbd144c: r3 = LoadInt32Instr(r2)
    //     0xbd144c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1450: sub             x2, x3, x1
    // 0xbd1454: cmp             x2, #1
    // 0xbd1458: b.ge            #0xbd1468
    // 0xbd145c: mov             x1, x0
    // 0xbd1460: r2 = 1
    //     0xbd1460: movz            x2, #0x1
    // 0xbd1464: r0 = _increaseBufferSize()
    //     0xbd1464: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1468: ldur            x2, [fp, #-8]
    // 0xbd146c: ldur            x3, [fp, #-0x10]
    // 0xbd1470: r4 = 4
    //     0xbd1470: movz            x4, #0x4
    // 0xbd1474: LoadField: r5 = r2->field_b
    //     0xbd1474: ldur            w5, [x2, #0xb]
    // 0xbd1478: DecompressPointer r5
    //     0xbd1478: add             x5, x5, HEAP, lsl #32
    // 0xbd147c: LoadField: r6 = r2->field_13
    //     0xbd147c: ldur            x6, [x2, #0x13]
    // 0xbd1480: add             x0, x6, #1
    // 0xbd1484: StoreField: r2->field_13 = r0
    //     0xbd1484: stur            x0, [x2, #0x13]
    // 0xbd1488: LoadField: r0 = r5->field_13
    //     0xbd1488: ldur            w0, [x5, #0x13]
    // 0xbd148c: r1 = LoadInt32Instr(r0)
    //     0xbd148c: sbfx            x1, x0, #1, #0x1f
    // 0xbd1490: mov             x0, x1
    // 0xbd1494: mov             x1, x6
    // 0xbd1498: cmp             x1, x0
    // 0xbd149c: b.hs            #0xbd17f0
    // 0xbd14a0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd14a0: add             x0, x5, x6
    //     0xbd14a4: strb            w4, [x0, #0x17]
    // 0xbd14a8: LoadField: d0 = r3->field_27
    //     0xbd14a8: ldur            d0, [x3, #0x27]
    // 0xbd14ac: r0 = inline_Allocate_Double()
    //     0xbd14ac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd14b0: add             x0, x0, #0x10
    //     0xbd14b4: cmp             x1, x0
    //     0xbd14b8: b.ls            #0xbd17f4
    //     0xbd14bc: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd14c0: sub             x0, x0, #0xf
    //     0xbd14c4: movz            x1, #0xe15c
    //     0xbd14c8: movk            x1, #0x3, lsl #16
    //     0xbd14cc: stur            x1, [x0, #-1]
    // 0xbd14d0: StoreField: r0->field_7 = d0
    //     0xbd14d0: stur            d0, [x0, #7]
    // 0xbd14d4: r16 = <double>
    //     0xbd14d4: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd14d8: stp             x2, x16, [SP, #8]
    // 0xbd14dc: str             x0, [SP]
    // 0xbd14e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd14e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd14e4: r0 = write()
    //     0xbd14e4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd14e8: ldur            x0, [fp, #-8]
    // 0xbd14ec: LoadField: r1 = r0->field_b
    //     0xbd14ec: ldur            w1, [x0, #0xb]
    // 0xbd14f0: DecompressPointer r1
    //     0xbd14f0: add             x1, x1, HEAP, lsl #32
    // 0xbd14f4: LoadField: r2 = r1->field_13
    //     0xbd14f4: ldur            w2, [x1, #0x13]
    // 0xbd14f8: LoadField: r1 = r0->field_13
    //     0xbd14f8: ldur            x1, [x0, #0x13]
    // 0xbd14fc: r3 = LoadInt32Instr(r2)
    //     0xbd14fc: sbfx            x3, x2, #1, #0x1f
    // 0xbd1500: sub             x2, x3, x1
    // 0xbd1504: cmp             x2, #1
    // 0xbd1508: b.ge            #0xbd1518
    // 0xbd150c: mov             x1, x0
    // 0xbd1510: r2 = 1
    //     0xbd1510: movz            x2, #0x1
    // 0xbd1514: r0 = _increaseBufferSize()
    //     0xbd1514: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1518: ldur            x2, [fp, #-8]
    // 0xbd151c: ldur            x3, [fp, #-0x10]
    // 0xbd1520: r4 = 5
    //     0xbd1520: movz            x4, #0x5
    // 0xbd1524: LoadField: r5 = r2->field_b
    //     0xbd1524: ldur            w5, [x2, #0xb]
    // 0xbd1528: DecompressPointer r5
    //     0xbd1528: add             x5, x5, HEAP, lsl #32
    // 0xbd152c: LoadField: r6 = r2->field_13
    //     0xbd152c: ldur            x6, [x2, #0x13]
    // 0xbd1530: add             x0, x6, #1
    // 0xbd1534: StoreField: r2->field_13 = r0
    //     0xbd1534: stur            x0, [x2, #0x13]
    // 0xbd1538: LoadField: r0 = r5->field_13
    //     0xbd1538: ldur            w0, [x5, #0x13]
    // 0xbd153c: r1 = LoadInt32Instr(r0)
    //     0xbd153c: sbfx            x1, x0, #1, #0x1f
    // 0xbd1540: mov             x0, x1
    // 0xbd1544: mov             x1, x6
    // 0xbd1548: cmp             x1, x0
    // 0xbd154c: b.hs            #0xbd180c
    // 0xbd1550: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1550: add             x0, x5, x6
    //     0xbd1554: strb            w4, [x0, #0x17]
    // 0xbd1558: LoadField: r0 = r3->field_2f
    //     0xbd1558: ldur            w0, [x3, #0x2f]
    // 0xbd155c: DecompressPointer r0
    //     0xbd155c: add             x0, x0, HEAP, lsl #32
    // 0xbd1560: r16 = <DateTime>
    //     0xbd1560: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0xbd1564: ldr             x16, [x16, #0xdd8]
    // 0xbd1568: stp             x2, x16, [SP, #8]
    // 0xbd156c: str             x0, [SP]
    // 0xbd1570: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1570: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1574: r0 = write()
    //     0xbd1574: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1578: ldur            x0, [fp, #-8]
    // 0xbd157c: LoadField: r1 = r0->field_b
    //     0xbd157c: ldur            w1, [x0, #0xb]
    // 0xbd1580: DecompressPointer r1
    //     0xbd1580: add             x1, x1, HEAP, lsl #32
    // 0xbd1584: LoadField: r2 = r1->field_13
    //     0xbd1584: ldur            w2, [x1, #0x13]
    // 0xbd1588: LoadField: r1 = r0->field_13
    //     0xbd1588: ldur            x1, [x0, #0x13]
    // 0xbd158c: r3 = LoadInt32Instr(r2)
    //     0xbd158c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1590: sub             x2, x3, x1
    // 0xbd1594: cmp             x2, #1
    // 0xbd1598: b.ge            #0xbd15a8
    // 0xbd159c: mov             x1, x0
    // 0xbd15a0: r2 = 1
    //     0xbd15a0: movz            x2, #0x1
    // 0xbd15a4: r0 = _increaseBufferSize()
    //     0xbd15a4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd15a8: ldur            x2, [fp, #-8]
    // 0xbd15ac: ldur            x3, [fp, #-0x10]
    // 0xbd15b0: r4 = 6
    //     0xbd15b0: movz            x4, #0x6
    // 0xbd15b4: LoadField: r5 = r2->field_b
    //     0xbd15b4: ldur            w5, [x2, #0xb]
    // 0xbd15b8: DecompressPointer r5
    //     0xbd15b8: add             x5, x5, HEAP, lsl #32
    // 0xbd15bc: LoadField: r6 = r2->field_13
    //     0xbd15bc: ldur            x6, [x2, #0x13]
    // 0xbd15c0: add             x0, x6, #1
    // 0xbd15c4: StoreField: r2->field_13 = r0
    //     0xbd15c4: stur            x0, [x2, #0x13]
    // 0xbd15c8: LoadField: r0 = r5->field_13
    //     0xbd15c8: ldur            w0, [x5, #0x13]
    // 0xbd15cc: r1 = LoadInt32Instr(r0)
    //     0xbd15cc: sbfx            x1, x0, #1, #0x1f
    // 0xbd15d0: mov             x0, x1
    // 0xbd15d4: mov             x1, x6
    // 0xbd15d8: cmp             x1, x0
    // 0xbd15dc: b.hs            #0xbd1810
    // 0xbd15e0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd15e0: add             x0, x5, x6
    //     0xbd15e4: strb            w4, [x0, #0x17]
    // 0xbd15e8: LoadField: r0 = r3->field_33
    //     0xbd15e8: ldur            w0, [x3, #0x33]
    // 0xbd15ec: DecompressPointer r0
    //     0xbd15ec: add             x0, x0, HEAP, lsl #32
    // 0xbd15f0: r16 = <DateTime?>
    //     0xbd15f0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b278] TypeArguments: <DateTime?>
    //     0xbd15f4: ldr             x16, [x16, #0x278]
    // 0xbd15f8: stp             x2, x16, [SP, #8]
    // 0xbd15fc: str             x0, [SP]
    // 0xbd1600: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1600: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1604: r0 = write()
    //     0xbd1604: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1608: ldur            x0, [fp, #-8]
    // 0xbd160c: LoadField: r1 = r0->field_b
    //     0xbd160c: ldur            w1, [x0, #0xb]
    // 0xbd1610: DecompressPointer r1
    //     0xbd1610: add             x1, x1, HEAP, lsl #32
    // 0xbd1614: LoadField: r2 = r1->field_13
    //     0xbd1614: ldur            w2, [x1, #0x13]
    // 0xbd1618: LoadField: r1 = r0->field_13
    //     0xbd1618: ldur            x1, [x0, #0x13]
    // 0xbd161c: r3 = LoadInt32Instr(r2)
    //     0xbd161c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1620: sub             x2, x3, x1
    // 0xbd1624: cmp             x2, #1
    // 0xbd1628: b.ge            #0xbd1638
    // 0xbd162c: mov             x1, x0
    // 0xbd1630: r2 = 1
    //     0xbd1630: movz            x2, #0x1
    // 0xbd1634: r0 = _increaseBufferSize()
    //     0xbd1634: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1638: ldur            x2, [fp, #-8]
    // 0xbd163c: ldur            x3, [fp, #-0x10]
    // 0xbd1640: r4 = 7
    //     0xbd1640: movz            x4, #0x7
    // 0xbd1644: LoadField: r5 = r2->field_b
    //     0xbd1644: ldur            w5, [x2, #0xb]
    // 0xbd1648: DecompressPointer r5
    //     0xbd1648: add             x5, x5, HEAP, lsl #32
    // 0xbd164c: LoadField: r6 = r2->field_13
    //     0xbd164c: ldur            x6, [x2, #0x13]
    // 0xbd1650: add             x0, x6, #1
    // 0xbd1654: StoreField: r2->field_13 = r0
    //     0xbd1654: stur            x0, [x2, #0x13]
    // 0xbd1658: LoadField: r0 = r5->field_13
    //     0xbd1658: ldur            w0, [x5, #0x13]
    // 0xbd165c: r1 = LoadInt32Instr(r0)
    //     0xbd165c: sbfx            x1, x0, #1, #0x1f
    // 0xbd1660: mov             x0, x1
    // 0xbd1664: mov             x1, x6
    // 0xbd1668: cmp             x1, x0
    // 0xbd166c: b.hs            #0xbd1814
    // 0xbd1670: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1670: add             x0, x5, x6
    //     0xbd1674: strb            w4, [x0, #0x17]
    // 0xbd1678: LoadField: r0 = r3->field_37
    //     0xbd1678: ldur            w0, [x3, #0x37]
    // 0xbd167c: DecompressPointer r0
    //     0xbd167c: add             x0, x0, HEAP, lsl #32
    // 0xbd1680: r16 = <String>
    //     0xbd1680: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd1684: stp             x2, x16, [SP, #8]
    // 0xbd1688: str             x0, [SP]
    // 0xbd168c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd168c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1690: r0 = write()
    //     0xbd1690: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1694: ldur            x0, [fp, #-8]
    // 0xbd1698: LoadField: r1 = r0->field_b
    //     0xbd1698: ldur            w1, [x0, #0xb]
    // 0xbd169c: DecompressPointer r1
    //     0xbd169c: add             x1, x1, HEAP, lsl #32
    // 0xbd16a0: LoadField: r2 = r1->field_13
    //     0xbd16a0: ldur            w2, [x1, #0x13]
    // 0xbd16a4: LoadField: r1 = r0->field_13
    //     0xbd16a4: ldur            x1, [x0, #0x13]
    // 0xbd16a8: r3 = LoadInt32Instr(r2)
    //     0xbd16a8: sbfx            x3, x2, #1, #0x1f
    // 0xbd16ac: sub             x2, x3, x1
    // 0xbd16b0: cmp             x2, #1
    // 0xbd16b4: b.ge            #0xbd16c4
    // 0xbd16b8: mov             x1, x0
    // 0xbd16bc: r2 = 1
    //     0xbd16bc: movz            x2, #0x1
    // 0xbd16c0: r0 = _increaseBufferSize()
    //     0xbd16c0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd16c4: ldur            x2, [fp, #-8]
    // 0xbd16c8: ldur            x3, [fp, #-0x10]
    // 0xbd16cc: r4 = 8
    //     0xbd16cc: movz            x4, #0x8
    // 0xbd16d0: LoadField: r5 = r2->field_b
    //     0xbd16d0: ldur            w5, [x2, #0xb]
    // 0xbd16d4: DecompressPointer r5
    //     0xbd16d4: add             x5, x5, HEAP, lsl #32
    // 0xbd16d8: LoadField: r6 = r2->field_13
    //     0xbd16d8: ldur            x6, [x2, #0x13]
    // 0xbd16dc: add             x0, x6, #1
    // 0xbd16e0: StoreField: r2->field_13 = r0
    //     0xbd16e0: stur            x0, [x2, #0x13]
    // 0xbd16e4: LoadField: r0 = r5->field_13
    //     0xbd16e4: ldur            w0, [x5, #0x13]
    // 0xbd16e8: r1 = LoadInt32Instr(r0)
    //     0xbd16e8: sbfx            x1, x0, #1, #0x1f
    // 0xbd16ec: mov             x0, x1
    // 0xbd16f0: mov             x1, x6
    // 0xbd16f4: cmp             x1, x0
    // 0xbd16f8: b.hs            #0xbd1818
    // 0xbd16fc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd16fc: add             x0, x5, x6
    //     0xbd1700: strb            w4, [x0, #0x17]
    // 0xbd1704: LoadField: r0 = r3->field_3b
    //     0xbd1704: ldur            w0, [x3, #0x3b]
    // 0xbd1708: DecompressPointer r0
    //     0xbd1708: add             x0, x0, HEAP, lsl #32
    // 0xbd170c: r16 = <String?>
    //     0xbd170c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd1710: stp             x2, x16, [SP, #8]
    // 0xbd1714: str             x0, [SP]
    // 0xbd1718: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1718: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd171c: r0 = write()
    //     0xbd171c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1720: ldur            x0, [fp, #-8]
    // 0xbd1724: LoadField: r1 = r0->field_b
    //     0xbd1724: ldur            w1, [x0, #0xb]
    // 0xbd1728: DecompressPointer r1
    //     0xbd1728: add             x1, x1, HEAP, lsl #32
    // 0xbd172c: LoadField: r2 = r1->field_13
    //     0xbd172c: ldur            w2, [x1, #0x13]
    // 0xbd1730: LoadField: r1 = r0->field_13
    //     0xbd1730: ldur            x1, [x0, #0x13]
    // 0xbd1734: r3 = LoadInt32Instr(r2)
    //     0xbd1734: sbfx            x3, x2, #1, #0x1f
    // 0xbd1738: sub             x2, x3, x1
    // 0xbd173c: cmp             x2, #1
    // 0xbd1740: b.ge            #0xbd1750
    // 0xbd1744: mov             x1, x0
    // 0xbd1748: r2 = 1
    //     0xbd1748: movz            x2, #0x1
    // 0xbd174c: r0 = _increaseBufferSize()
    //     0xbd174c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1750: ldur            x2, [fp, #-8]
    // 0xbd1754: ldur            x3, [fp, #-0x10]
    // 0xbd1758: r4 = 9
    //     0xbd1758: movz            x4, #0x9
    // 0xbd175c: LoadField: r5 = r2->field_b
    //     0xbd175c: ldur            w5, [x2, #0xb]
    // 0xbd1760: DecompressPointer r5
    //     0xbd1760: add             x5, x5, HEAP, lsl #32
    // 0xbd1764: LoadField: r6 = r2->field_13
    //     0xbd1764: ldur            x6, [x2, #0x13]
    // 0xbd1768: add             x0, x6, #1
    // 0xbd176c: StoreField: r2->field_13 = r0
    //     0xbd176c: stur            x0, [x2, #0x13]
    // 0xbd1770: LoadField: r0 = r5->field_13
    //     0xbd1770: ldur            w0, [x5, #0x13]
    // 0xbd1774: r1 = LoadInt32Instr(r0)
    //     0xbd1774: sbfx            x1, x0, #1, #0x1f
    // 0xbd1778: mov             x0, x1
    // 0xbd177c: mov             x1, x6
    // 0xbd1780: cmp             x1, x0
    // 0xbd1784: b.hs            #0xbd181c
    // 0xbd1788: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1788: add             x0, x5, x6
    //     0xbd178c: strb            w4, [x0, #0x17]
    // 0xbd1790: LoadField: r0 = r3->field_3f
    //     0xbd1790: ldur            w0, [x3, #0x3f]
    // 0xbd1794: DecompressPointer r0
    //     0xbd1794: add             x0, x0, HEAP, lsl #32
    // 0xbd1798: r16 = <String?>
    //     0xbd1798: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd179c: stp             x2, x16, [SP, #8]
    // 0xbd17a0: str             x0, [SP]
    // 0xbd17a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd17a4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd17a8: r0 = write()
    //     0xbd17a8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd17ac: r0 = Null
    //     0xbd17ac: mov             x0, NULL
    // 0xbd17b0: LeaveFrame
    //     0xbd17b0: mov             SP, fp
    //     0xbd17b4: ldp             fp, lr, [SP], #0x10
    // 0xbd17b8: ret
    //     0xbd17b8: ret             
    // 0xbd17bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd17bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd17c0: b               #0xbd1158
    // 0xbd17c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17d8: SaveReg d0
    //     0xbd17d8: str             q0, [SP, #-0x10]!
    // 0xbd17dc: stp             x2, x3, [SP, #-0x10]!
    // 0xbd17e0: r0 = AllocateDouble()
    //     0xbd17e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd17e4: ldp             x2, x3, [SP], #0x10
    // 0xbd17e8: RestoreReg d0
    //     0xbd17e8: ldr             q0, [SP], #0x10
    // 0xbd17ec: b               #0xbd1420
    // 0xbd17f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd17f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd17f4: SaveReg d0
    //     0xbd17f4: str             q0, [SP, #-0x10]!
    // 0xbd17f8: stp             x2, x3, [SP, #-0x10]!
    // 0xbd17fc: r0 = AllocateDouble()
    //     0xbd17fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd1800: ldp             x2, x3, [SP], #0x10
    // 0xbd1804: RestoreReg d0
    //     0xbd1804: ldr             q0, [SP], #0x10
    // 0xbd1808: b               #0xbd14d0
    // 0xbd180c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd180c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1810: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1814: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1818: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1818: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd181c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd181c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0090, size: 0x24
    // 0xbf0090: r1 = 64
    //     0xbf0090: movz            x1, #0x40
    // 0xbf0094: r16 = LoadInt32Instr(r1)
    //     0xbf0094: sbfx            x16, x1, #1, #0x1f
    // 0xbf0098: r17 = 11601
    //     0xbf0098: movz            x17, #0x2d51
    // 0xbf009c: mul             x0, x16, x17
    // 0xbf00a0: umulh           x16, x16, x17
    // 0xbf00a4: eor             x0, x0, x16
    // 0xbf00a8: r0 = 0
    //     0xbf00a8: eor             x0, x0, x0, lsr #32
    // 0xbf00ac: ubfiz           x0, x0, #1, #0x1e
    // 0xbf00b0: ret
    //     0xbf00b0: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd762e4, size: 0x9c
    // 0xd762e4: EnterFrame
    //     0xd762e4: stp             fp, lr, [SP, #-0x10]!
    //     0xd762e8: mov             fp, SP
    // 0xd762ec: AllocStack(0x10)
    //     0xd762ec: sub             SP, SP, #0x10
    // 0xd762f0: CheckStackOverflow
    //     0xd762f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd762f4: cmp             SP, x16
    //     0xd762f8: b.ls            #0xd76378
    // 0xd762fc: ldr             x0, [fp, #0x10]
    // 0xd76300: cmp             w0, NULL
    // 0xd76304: b.ne            #0xd76318
    // 0xd76308: r0 = false
    //     0xd76308: add             x0, NULL, #0x30  ; false
    // 0xd7630c: LeaveFrame
    //     0xd7630c: mov             SP, fp
    //     0xd76310: ldp             fp, lr, [SP], #0x10
    // 0xd76314: ret
    //     0xd76314: ret             
    // 0xd76318: ldr             x1, [fp, #0x18]
    // 0xd7631c: cmp             w1, w0
    // 0xd76320: b.ne            #0xd7632c
    // 0xd76324: r0 = true
    //     0xd76324: add             x0, NULL, #0x20  ; true
    // 0xd76328: b               #0xd7636c
    // 0xd7632c: r1 = 60
    //     0xd7632c: movz            x1, #0x3c
    // 0xd76330: branchIfSmi(r0, 0xd7633c)
    //     0xd76330: tbz             w0, #0, #0xd7633c
    // 0xd76334: r1 = LoadClassIdInstr(r0)
    //     0xd76334: ldur            x1, [x0, #-1]
    //     0xd76338: ubfx            x1, x1, #0xc, #0x14
    // 0xd7633c: cmp             x1, #0x682
    // 0xd76340: b.ne            #0xd76368
    // 0xd76344: r16 = CountdownAdapter
    //     0xd76344: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b430] Type: CountdownAdapter
    //     0xd76348: ldr             x16, [x16, #0x430]
    // 0xd7634c: r30 = CountdownAdapter
    //     0xd7634c: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b430] Type: CountdownAdapter
    //     0xd76350: ldr             lr, [lr, #0x430]
    // 0xd76354: stp             lr, x16, [SP]
    // 0xd76358: r0 = ==()
    //     0xd76358: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7635c: tbnz            w0, #4, #0xd76368
    // 0xd76360: r0 = true
    //     0xd76360: add             x0, NULL, #0x20  ; true
    // 0xd76364: b               #0xd7636c
    // 0xd76368: r0 = false
    //     0xd76368: add             x0, NULL, #0x30  ; false
    // 0xd7636c: LeaveFrame
    //     0xd7636c: mov             SP, fp
    //     0xd76370: ldp             fp, lr, [SP], #0x10
    // 0xd76374: ret
    //     0xd76374: ret             
    // 0xd76378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7637c: b               #0xd762fc
  }
}
