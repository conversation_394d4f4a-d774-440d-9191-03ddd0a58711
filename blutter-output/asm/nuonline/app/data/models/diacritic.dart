// lib: , url: package:nuonline/app/data/models/diacritic.dart

// class id: 1050013, size: 0x8
class :: {
}

// class id: 1148, size: 0x24, field offset: 0x8
//   const constructor, 
class Diacritic extends Object {

  factory _ Diacritic.fromMap(/* No info */) {
    // ** addr: 0xe31da0, size: 0x1ec
    // 0xe31da0: EnterFrame
    //     0xe31da0: stp             fp, lr, [SP, #-0x10]!
    //     0xe31da4: mov             fp, SP
    // 0xe31da8: AllocStack(0x28)
    //     0xe31da8: sub             SP, SP, #0x28
    // 0xe31dac: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe31dac: mov             x3, x2
    //     0xe31db0: stur            x2, [fp, #-8]
    // 0xe31db4: CheckStackOverflow
    //     0xe31db4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe31db8: cmp             SP, x16
    //     0xe31dbc: b.ls            #0xe31f84
    // 0xe31dc0: r0 = LoadClassIdInstr(r3)
    //     0xe31dc0: ldur            x0, [x3, #-1]
    //     0xe31dc4: ubfx            x0, x0, #0xc, #0x14
    // 0xe31dc8: mov             x1, x3
    // 0xe31dcc: r2 = "id"
    //     0xe31dcc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe31dd0: ldr             x2, [x2, #0x740]
    // 0xe31dd4: r0 = GDT[cid_x0 + -0x114]()
    //     0xe31dd4: sub             lr, x0, #0x114
    //     0xe31dd8: ldr             lr, [x21, lr, lsl #3]
    //     0xe31ddc: blr             lr
    // 0xe31de0: mov             x3, x0
    // 0xe31de4: r2 = Null
    //     0xe31de4: mov             x2, NULL
    // 0xe31de8: r1 = Null
    //     0xe31de8: mov             x1, NULL
    // 0xe31dec: stur            x3, [fp, #-0x10]
    // 0xe31df0: branchIfSmi(r0, 0xe31e18)
    //     0xe31df0: tbz             w0, #0, #0xe31e18
    // 0xe31df4: r4 = LoadClassIdInstr(r0)
    //     0xe31df4: ldur            x4, [x0, #-1]
    //     0xe31df8: ubfx            x4, x4, #0xc, #0x14
    // 0xe31dfc: sub             x4, x4, #0x3c
    // 0xe31e00: cmp             x4, #1
    // 0xe31e04: b.ls            #0xe31e18
    // 0xe31e08: r8 = int
    //     0xe31e08: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe31e0c: r3 = Null
    //     0xe31e0c: add             x3, PP, #0x17, lsl #12  ; [pp+0x17d80] Null
    //     0xe31e10: ldr             x3, [x3, #0xd80]
    // 0xe31e14: r0 = int()
    //     0xe31e14: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe31e18: ldur            x3, [fp, #-8]
    // 0xe31e1c: r0 = LoadClassIdInstr(r3)
    //     0xe31e1c: ldur            x0, [x3, #-1]
    //     0xe31e20: ubfx            x0, x0, #0xc, #0x14
    // 0xe31e24: mov             x1, x3
    // 0xe31e28: r2 = "surah_id"
    //     0xe31e28: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0xe31e2c: ldr             x2, [x2, #0x340]
    // 0xe31e30: r0 = GDT[cid_x0 + -0x114]()
    //     0xe31e30: sub             lr, x0, #0x114
    //     0xe31e34: ldr             lr, [x21, lr, lsl #3]
    //     0xe31e38: blr             lr
    // 0xe31e3c: mov             x3, x0
    // 0xe31e40: r2 = Null
    //     0xe31e40: mov             x2, NULL
    // 0xe31e44: r1 = Null
    //     0xe31e44: mov             x1, NULL
    // 0xe31e48: stur            x3, [fp, #-0x18]
    // 0xe31e4c: branchIfSmi(r0, 0xe31e74)
    //     0xe31e4c: tbz             w0, #0, #0xe31e74
    // 0xe31e50: r4 = LoadClassIdInstr(r0)
    //     0xe31e50: ldur            x4, [x0, #-1]
    //     0xe31e54: ubfx            x4, x4, #0xc, #0x14
    // 0xe31e58: sub             x4, x4, #0x3c
    // 0xe31e5c: cmp             x4, #1
    // 0xe31e60: b.ls            #0xe31e74
    // 0xe31e64: r8 = int
    //     0xe31e64: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe31e68: r3 = Null
    //     0xe31e68: add             x3, PP, #0x17, lsl #12  ; [pp+0x17d90] Null
    //     0xe31e6c: ldr             x3, [x3, #0xd90]
    // 0xe31e70: r0 = int()
    //     0xe31e70: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe31e74: ldur            x3, [fp, #-8]
    // 0xe31e78: r0 = LoadClassIdInstr(r3)
    //     0xe31e78: ldur            x0, [x3, #-1]
    //     0xe31e7c: ubfx            x0, x0, #0xc, #0x14
    // 0xe31e80: mov             x1, x3
    // 0xe31e84: r2 = "number"
    //     0xe31e84: add             x2, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0xe31e88: ldr             x2, [x2, #0x998]
    // 0xe31e8c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe31e8c: sub             lr, x0, #0x114
    //     0xe31e90: ldr             lr, [x21, lr, lsl #3]
    //     0xe31e94: blr             lr
    // 0xe31e98: mov             x3, x0
    // 0xe31e9c: r2 = Null
    //     0xe31e9c: mov             x2, NULL
    // 0xe31ea0: r1 = Null
    //     0xe31ea0: mov             x1, NULL
    // 0xe31ea4: stur            x3, [fp, #-0x20]
    // 0xe31ea8: branchIfSmi(r0, 0xe31ed0)
    //     0xe31ea8: tbz             w0, #0, #0xe31ed0
    // 0xe31eac: r4 = LoadClassIdInstr(r0)
    //     0xe31eac: ldur            x4, [x0, #-1]
    //     0xe31eb0: ubfx            x4, x4, #0xc, #0x14
    // 0xe31eb4: sub             x4, x4, #0x3c
    // 0xe31eb8: cmp             x4, #1
    // 0xe31ebc: b.ls            #0xe31ed0
    // 0xe31ec0: r8 = int
    //     0xe31ec0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe31ec4: r3 = Null
    //     0xe31ec4: add             x3, PP, #0x17, lsl #12  ; [pp+0x17da0] Null
    //     0xe31ec8: ldr             x3, [x3, #0xda0]
    // 0xe31ecc: r0 = int()
    //     0xe31ecc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe31ed0: ldur            x1, [fp, #-8]
    // 0xe31ed4: r0 = LoadClassIdInstr(r1)
    //     0xe31ed4: ldur            x0, [x1, #-1]
    //     0xe31ed8: ubfx            x0, x0, #0xc, #0x14
    // 0xe31edc: r2 = "text"
    //     0xe31edc: ldr             x2, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0xe31ee0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe31ee0: sub             lr, x0, #0x114
    //     0xe31ee4: ldr             lr, [x21, lr, lsl #3]
    //     0xe31ee8: blr             lr
    // 0xe31eec: mov             x3, x0
    // 0xe31ef0: r2 = Null
    //     0xe31ef0: mov             x2, NULL
    // 0xe31ef4: r1 = Null
    //     0xe31ef4: mov             x1, NULL
    // 0xe31ef8: stur            x3, [fp, #-8]
    // 0xe31efc: r4 = 60
    //     0xe31efc: movz            x4, #0x3c
    // 0xe31f00: branchIfSmi(r0, 0xe31f0c)
    //     0xe31f00: tbz             w0, #0, #0xe31f0c
    // 0xe31f04: r4 = LoadClassIdInstr(r0)
    //     0xe31f04: ldur            x4, [x0, #-1]
    //     0xe31f08: ubfx            x4, x4, #0xc, #0x14
    // 0xe31f0c: sub             x4, x4, #0x5e
    // 0xe31f10: cmp             x4, #1
    // 0xe31f14: b.ls            #0xe31f28
    // 0xe31f18: r8 = String
    //     0xe31f18: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe31f1c: r3 = Null
    //     0xe31f1c: add             x3, PP, #0x17, lsl #12  ; [pp+0x17db0] Null
    //     0xe31f20: ldr             x3, [x3, #0xdb0]
    // 0xe31f24: r0 = String()
    //     0xe31f24: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe31f28: ldur            x0, [fp, #-0x10]
    // 0xe31f2c: r1 = LoadInt32Instr(r0)
    //     0xe31f2c: sbfx            x1, x0, #1, #0x1f
    //     0xe31f30: tbz             w0, #0, #0xe31f38
    //     0xe31f34: ldur            x1, [x0, #7]
    // 0xe31f38: stur            x1, [fp, #-0x28]
    // 0xe31f3c: r0 = Diacritic()
    //     0xe31f3c: bl              #0xa60648  ; AllocateDiacriticStub -> Diacritic (size=0x24)
    // 0xe31f40: ldur            x1, [fp, #-0x28]
    // 0xe31f44: StoreField: r0->field_7 = r1
    //     0xe31f44: stur            x1, [x0, #7]
    // 0xe31f48: ldur            x1, [fp, #-0x18]
    // 0xe31f4c: r2 = LoadInt32Instr(r1)
    //     0xe31f4c: sbfx            x2, x1, #1, #0x1f
    //     0xe31f50: tbz             w1, #0, #0xe31f58
    //     0xe31f54: ldur            x2, [x1, #7]
    // 0xe31f58: StoreField: r0->field_f = r2
    //     0xe31f58: stur            x2, [x0, #0xf]
    // 0xe31f5c: ldur            x1, [fp, #-0x20]
    // 0xe31f60: r2 = LoadInt32Instr(r1)
    //     0xe31f60: sbfx            x2, x1, #1, #0x1f
    //     0xe31f64: tbz             w1, #0, #0xe31f6c
    //     0xe31f68: ldur            x2, [x1, #7]
    // 0xe31f6c: ArrayStore: r0[0] = r2  ; List_8
    //     0xe31f6c: stur            x2, [x0, #0x17]
    // 0xe31f70: ldur            x1, [fp, #-8]
    // 0xe31f74: StoreField: r0->field_1f = r1
    //     0xe31f74: stur            w1, [x0, #0x1f]
    // 0xe31f78: LeaveFrame
    //     0xe31f78: mov             SP, fp
    //     0xe31f7c: ldp             fp, lr, [SP], #0x10
    // 0xe31f80: ret
    //     0xe31f80: ret             
    // 0xe31f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe31f84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe31f88: b               #0xe31dc0
  }
}

// class id: 1665, size: 0x14, field offset: 0xc
class DiacriticAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa60294, size: 0x3b4
    // 0xa60294: EnterFrame
    //     0xa60294: stp             fp, lr, [SP, #-0x10]!
    //     0xa60298: mov             fp, SP
    // 0xa6029c: AllocStack(0x50)
    //     0xa6029c: sub             SP, SP, #0x50
    // 0xa602a0: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa602a0: stur            x2, [fp, #-0x20]
    // 0xa602a4: CheckStackOverflow
    //     0xa602a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa602a8: cmp             SP, x16
    //     0xa602ac: b.ls            #0xa60630
    // 0xa602b0: LoadField: r3 = r2->field_23
    //     0xa602b0: ldur            x3, [x2, #0x23]
    // 0xa602b4: add             x0, x3, #1
    // 0xa602b8: LoadField: r1 = r2->field_1b
    //     0xa602b8: ldur            x1, [x2, #0x1b]
    // 0xa602bc: cmp             x0, x1
    // 0xa602c0: b.gt            #0xa605d4
    // 0xa602c4: LoadField: r4 = r2->field_7
    //     0xa602c4: ldur            w4, [x2, #7]
    // 0xa602c8: DecompressPointer r4
    //     0xa602c8: add             x4, x4, HEAP, lsl #32
    // 0xa602cc: stur            x4, [fp, #-0x18]
    // 0xa602d0: StoreField: r2->field_23 = r0
    //     0xa602d0: stur            x0, [x2, #0x23]
    // 0xa602d4: LoadField: r0 = r4->field_13
    //     0xa602d4: ldur            w0, [x4, #0x13]
    // 0xa602d8: r5 = LoadInt32Instr(r0)
    //     0xa602d8: sbfx            x5, x0, #1, #0x1f
    // 0xa602dc: mov             x0, x5
    // 0xa602e0: mov             x1, x3
    // 0xa602e4: stur            x5, [fp, #-0x10]
    // 0xa602e8: cmp             x1, x0
    // 0xa602ec: b.hs            #0xa60638
    // 0xa602f0: LoadField: r0 = r4->field_7
    //     0xa602f0: ldur            x0, [x4, #7]
    // 0xa602f4: ldrb            w1, [x0, x3]
    // 0xa602f8: stur            x1, [fp, #-8]
    // 0xa602fc: r16 = <int, dynamic>
    //     0xa602fc: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa60300: ldr             x16, [x16, #0xac0]
    // 0xa60304: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa60308: stp             lr, x16, [SP]
    // 0xa6030c: r0 = Map._fromLiteral()
    //     0xa6030c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa60310: mov             x2, x0
    // 0xa60314: stur            x2, [fp, #-0x38]
    // 0xa60318: r6 = 0
    //     0xa60318: movz            x6, #0
    // 0xa6031c: ldur            x3, [fp, #-0x20]
    // 0xa60320: ldur            x4, [fp, #-0x18]
    // 0xa60324: ldur            x5, [fp, #-8]
    // 0xa60328: stur            x6, [fp, #-0x30]
    // 0xa6032c: CheckStackOverflow
    //     0xa6032c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa60330: cmp             SP, x16
    //     0xa60334: b.ls            #0xa6063c
    // 0xa60338: cmp             x6, x5
    // 0xa6033c: b.ge            #0xa603c8
    // 0xa60340: LoadField: r7 = r3->field_23
    //     0xa60340: ldur            x7, [x3, #0x23]
    // 0xa60344: add             x0, x7, #1
    // 0xa60348: LoadField: r1 = r3->field_1b
    //     0xa60348: ldur            x1, [x3, #0x1b]
    // 0xa6034c: cmp             x0, x1
    // 0xa60350: b.gt            #0xa605fc
    // 0xa60354: StoreField: r3->field_23 = r0
    //     0xa60354: stur            x0, [x3, #0x23]
    // 0xa60358: ldur            x0, [fp, #-0x10]
    // 0xa6035c: mov             x1, x7
    // 0xa60360: cmp             x1, x0
    // 0xa60364: b.hs            #0xa60644
    // 0xa60368: LoadField: r0 = r4->field_7
    //     0xa60368: ldur            x0, [x4, #7]
    // 0xa6036c: ldrb            w8, [x0, x7]
    // 0xa60370: mov             x1, x3
    // 0xa60374: stur            x8, [fp, #-0x28]
    // 0xa60378: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa60378: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa6037c: r0 = read()
    //     0xa6037c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa60380: mov             x1, x0
    // 0xa60384: ldur            x0, [fp, #-0x28]
    // 0xa60388: lsl             x2, x0, #1
    // 0xa6038c: r16 = LoadInt32Instr(r2)
    //     0xa6038c: sbfx            x16, x2, #1, #0x1f
    // 0xa60390: r17 = 11601
    //     0xa60390: movz            x17, #0x2d51
    // 0xa60394: mul             x0, x16, x17
    // 0xa60398: umulh           x16, x16, x17
    // 0xa6039c: eor             x0, x0, x16
    // 0xa603a0: r0 = 0
    //     0xa603a0: eor             x0, x0, x0, lsr #32
    // 0xa603a4: ubfiz           x0, x0, #1, #0x1e
    // 0xa603a8: r5 = LoadInt32Instr(r0)
    //     0xa603a8: sbfx            x5, x0, #1, #0x1f
    // 0xa603ac: mov             x3, x1
    // 0xa603b0: ldur            x1, [fp, #-0x38]
    // 0xa603b4: r0 = _set()
    //     0xa603b4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa603b8: ldur            x0, [fp, #-0x30]
    // 0xa603bc: add             x6, x0, #1
    // 0xa603c0: ldur            x2, [fp, #-0x38]
    // 0xa603c4: b               #0xa6031c
    // 0xa603c8: mov             x0, x2
    // 0xa603cc: mov             x1, x0
    // 0xa603d0: r2 = 0
    //     0xa603d0: movz            x2, #0
    // 0xa603d4: r0 = _getValueOrData()
    //     0xa603d4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa603d8: ldur            x3, [fp, #-0x38]
    // 0xa603dc: LoadField: r1 = r3->field_f
    //     0xa603dc: ldur            w1, [x3, #0xf]
    // 0xa603e0: DecompressPointer r1
    //     0xa603e0: add             x1, x1, HEAP, lsl #32
    // 0xa603e4: cmp             w1, w0
    // 0xa603e8: b.ne            #0xa603f4
    // 0xa603ec: r4 = Null
    //     0xa603ec: mov             x4, NULL
    // 0xa603f0: b               #0xa603f8
    // 0xa603f4: mov             x4, x0
    // 0xa603f8: mov             x0, x4
    // 0xa603fc: stur            x4, [fp, #-0x18]
    // 0xa60400: r2 = Null
    //     0xa60400: mov             x2, NULL
    // 0xa60404: r1 = Null
    //     0xa60404: mov             x1, NULL
    // 0xa60408: branchIfSmi(r0, 0xa60430)
    //     0xa60408: tbz             w0, #0, #0xa60430
    // 0xa6040c: r4 = LoadClassIdInstr(r0)
    //     0xa6040c: ldur            x4, [x0, #-1]
    //     0xa60410: ubfx            x4, x4, #0xc, #0x14
    // 0xa60414: sub             x4, x4, #0x3c
    // 0xa60418: cmp             x4, #1
    // 0xa6041c: b.ls            #0xa60430
    // 0xa60420: r8 = int
    //     0xa60420: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60424: r3 = Null
    //     0xa60424: add             x3, PP, #0x21, lsl #12  ; [pp+0x215d8] Null
    //     0xa60428: ldr             x3, [x3, #0x5d8]
    // 0xa6042c: r0 = int()
    //     0xa6042c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60430: ldur            x1, [fp, #-0x38]
    // 0xa60434: r2 = 2
    //     0xa60434: movz            x2, #0x2
    // 0xa60438: r0 = _getValueOrData()
    //     0xa60438: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6043c: ldur            x3, [fp, #-0x38]
    // 0xa60440: LoadField: r1 = r3->field_f
    //     0xa60440: ldur            w1, [x3, #0xf]
    // 0xa60444: DecompressPointer r1
    //     0xa60444: add             x1, x1, HEAP, lsl #32
    // 0xa60448: cmp             w1, w0
    // 0xa6044c: b.ne            #0xa60458
    // 0xa60450: r4 = Null
    //     0xa60450: mov             x4, NULL
    // 0xa60454: b               #0xa6045c
    // 0xa60458: mov             x4, x0
    // 0xa6045c: mov             x0, x4
    // 0xa60460: stur            x4, [fp, #-0x20]
    // 0xa60464: r2 = Null
    //     0xa60464: mov             x2, NULL
    // 0xa60468: r1 = Null
    //     0xa60468: mov             x1, NULL
    // 0xa6046c: branchIfSmi(r0, 0xa60494)
    //     0xa6046c: tbz             w0, #0, #0xa60494
    // 0xa60470: r4 = LoadClassIdInstr(r0)
    //     0xa60470: ldur            x4, [x0, #-1]
    //     0xa60474: ubfx            x4, x4, #0xc, #0x14
    // 0xa60478: sub             x4, x4, #0x3c
    // 0xa6047c: cmp             x4, #1
    // 0xa60480: b.ls            #0xa60494
    // 0xa60484: r8 = int
    //     0xa60484: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60488: r3 = Null
    //     0xa60488: add             x3, PP, #0x21, lsl #12  ; [pp+0x215e8] Null
    //     0xa6048c: ldr             x3, [x3, #0x5e8]
    // 0xa60490: r0 = int()
    //     0xa60490: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60494: ldur            x1, [fp, #-0x38]
    // 0xa60498: r2 = 4
    //     0xa60498: movz            x2, #0x4
    // 0xa6049c: r0 = _getValueOrData()
    //     0xa6049c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa604a0: ldur            x3, [fp, #-0x38]
    // 0xa604a4: LoadField: r1 = r3->field_f
    //     0xa604a4: ldur            w1, [x3, #0xf]
    // 0xa604a8: DecompressPointer r1
    //     0xa604a8: add             x1, x1, HEAP, lsl #32
    // 0xa604ac: cmp             w1, w0
    // 0xa604b0: b.ne            #0xa604bc
    // 0xa604b4: r4 = Null
    //     0xa604b4: mov             x4, NULL
    // 0xa604b8: b               #0xa604c0
    // 0xa604bc: mov             x4, x0
    // 0xa604c0: mov             x0, x4
    // 0xa604c4: stur            x4, [fp, #-0x40]
    // 0xa604c8: r2 = Null
    //     0xa604c8: mov             x2, NULL
    // 0xa604cc: r1 = Null
    //     0xa604cc: mov             x1, NULL
    // 0xa604d0: branchIfSmi(r0, 0xa604f8)
    //     0xa604d0: tbz             w0, #0, #0xa604f8
    // 0xa604d4: r4 = LoadClassIdInstr(r0)
    //     0xa604d4: ldur            x4, [x0, #-1]
    //     0xa604d8: ubfx            x4, x4, #0xc, #0x14
    // 0xa604dc: sub             x4, x4, #0x3c
    // 0xa604e0: cmp             x4, #1
    // 0xa604e4: b.ls            #0xa604f8
    // 0xa604e8: r8 = int
    //     0xa604e8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa604ec: r3 = Null
    //     0xa604ec: add             x3, PP, #0x21, lsl #12  ; [pp+0x215f8] Null
    //     0xa604f0: ldr             x3, [x3, #0x5f8]
    // 0xa604f4: r0 = int()
    //     0xa604f4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa604f8: ldur            x1, [fp, #-0x38]
    // 0xa604fc: r2 = 6
    //     0xa604fc: movz            x2, #0x6
    // 0xa60500: r0 = _getValueOrData()
    //     0xa60500: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60504: mov             x1, x0
    // 0xa60508: ldur            x0, [fp, #-0x38]
    // 0xa6050c: LoadField: r2 = r0->field_f
    //     0xa6050c: ldur            w2, [x0, #0xf]
    // 0xa60510: DecompressPointer r2
    //     0xa60510: add             x2, x2, HEAP, lsl #32
    // 0xa60514: cmp             w2, w1
    // 0xa60518: b.ne            #0xa60524
    // 0xa6051c: r6 = Null
    //     0xa6051c: mov             x6, NULL
    // 0xa60520: b               #0xa60528
    // 0xa60524: mov             x6, x1
    // 0xa60528: ldur            x5, [fp, #-0x18]
    // 0xa6052c: ldur            x4, [fp, #-0x20]
    // 0xa60530: ldur            x3, [fp, #-0x40]
    // 0xa60534: mov             x0, x6
    // 0xa60538: stur            x6, [fp, #-0x38]
    // 0xa6053c: r2 = Null
    //     0xa6053c: mov             x2, NULL
    // 0xa60540: r1 = Null
    //     0xa60540: mov             x1, NULL
    // 0xa60544: r4 = 60
    //     0xa60544: movz            x4, #0x3c
    // 0xa60548: branchIfSmi(r0, 0xa60554)
    //     0xa60548: tbz             w0, #0, #0xa60554
    // 0xa6054c: r4 = LoadClassIdInstr(r0)
    //     0xa6054c: ldur            x4, [x0, #-1]
    //     0xa60550: ubfx            x4, x4, #0xc, #0x14
    // 0xa60554: sub             x4, x4, #0x5e
    // 0xa60558: cmp             x4, #1
    // 0xa6055c: b.ls            #0xa60570
    // 0xa60560: r8 = String
    //     0xa60560: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60564: r3 = Null
    //     0xa60564: add             x3, PP, #0x21, lsl #12  ; [pp+0x21608] Null
    //     0xa60568: ldr             x3, [x3, #0x608]
    // 0xa6056c: r0 = String()
    //     0xa6056c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60570: ldur            x0, [fp, #-0x18]
    // 0xa60574: r1 = LoadInt32Instr(r0)
    //     0xa60574: sbfx            x1, x0, #1, #0x1f
    //     0xa60578: tbz             w0, #0, #0xa60580
    //     0xa6057c: ldur            x1, [x0, #7]
    // 0xa60580: stur            x1, [fp, #-8]
    // 0xa60584: r0 = Diacritic()
    //     0xa60584: bl              #0xa60648  ; AllocateDiacriticStub -> Diacritic (size=0x24)
    // 0xa60588: mov             x1, x0
    // 0xa6058c: ldur            x0, [fp, #-8]
    // 0xa60590: StoreField: r1->field_7 = r0
    //     0xa60590: stur            x0, [x1, #7]
    // 0xa60594: ldur            x0, [fp, #-0x20]
    // 0xa60598: r2 = LoadInt32Instr(r0)
    //     0xa60598: sbfx            x2, x0, #1, #0x1f
    //     0xa6059c: tbz             w0, #0, #0xa605a4
    //     0xa605a0: ldur            x2, [x0, #7]
    // 0xa605a4: StoreField: r1->field_f = r2
    //     0xa605a4: stur            x2, [x1, #0xf]
    // 0xa605a8: ldur            x0, [fp, #-0x40]
    // 0xa605ac: r2 = LoadInt32Instr(r0)
    //     0xa605ac: sbfx            x2, x0, #1, #0x1f
    //     0xa605b0: tbz             w0, #0, #0xa605b8
    //     0xa605b4: ldur            x2, [x0, #7]
    // 0xa605b8: ArrayStore: r1[0] = r2  ; List_8
    //     0xa605b8: stur            x2, [x1, #0x17]
    // 0xa605bc: ldur            x0, [fp, #-0x38]
    // 0xa605c0: StoreField: r1->field_1f = r0
    //     0xa605c0: stur            w0, [x1, #0x1f]
    // 0xa605c4: mov             x0, x1
    // 0xa605c8: LeaveFrame
    //     0xa605c8: mov             SP, fp
    //     0xa605cc: ldp             fp, lr, [SP], #0x10
    // 0xa605d0: ret
    //     0xa605d0: ret             
    // 0xa605d4: r0 = RangeError()
    //     0xa605d4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa605d8: mov             x1, x0
    // 0xa605dc: r0 = "Not enough bytes available."
    //     0xa605dc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa605e0: ldr             x0, [x0, #0x8a8]
    // 0xa605e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa605e4: stur            w0, [x1, #0x17]
    // 0xa605e8: r2 = false
    //     0xa605e8: add             x2, NULL, #0x30  ; false
    // 0xa605ec: StoreField: r1->field_b = r2
    //     0xa605ec: stur            w2, [x1, #0xb]
    // 0xa605f0: mov             x0, x1
    // 0xa605f4: r0 = Throw()
    //     0xa605f4: bl              #0xec04b8  ; ThrowStub
    // 0xa605f8: brk             #0
    // 0xa605fc: r0 = "Not enough bytes available."
    //     0xa605fc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60600: ldr             x0, [x0, #0x8a8]
    // 0xa60604: r2 = false
    //     0xa60604: add             x2, NULL, #0x30  ; false
    // 0xa60608: r0 = RangeError()
    //     0xa60608: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa6060c: mov             x1, x0
    // 0xa60610: r0 = "Not enough bytes available."
    //     0xa60610: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60614: ldr             x0, [x0, #0x8a8]
    // 0xa60618: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60618: stur            w0, [x1, #0x17]
    // 0xa6061c: r0 = false
    //     0xa6061c: add             x0, NULL, #0x30  ; false
    // 0xa60620: StoreField: r1->field_b = r0
    //     0xa60620: stur            w0, [x1, #0xb]
    // 0xa60624: mov             x0, x1
    // 0xa60628: r0 = Throw()
    //     0xa60628: bl              #0xec04b8  ; ThrowStub
    // 0xa6062c: brk             #0
    // 0xa60630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60634: b               #0xa602b0
    // 0xa60638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60638: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa6063c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6063c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60640: b               #0xa60338
    // 0xa60644: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60644: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd1820, size: 0x338
    // 0xbd1820: EnterFrame
    //     0xbd1820: stp             fp, lr, [SP, #-0x10]!
    //     0xbd1824: mov             fp, SP
    // 0xbd1828: AllocStack(0x28)
    //     0xbd1828: sub             SP, SP, #0x28
    // 0xbd182c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd182c: mov             x4, x2
    //     0xbd1830: stur            x2, [fp, #-8]
    //     0xbd1834: stur            x3, [fp, #-0x10]
    // 0xbd1838: CheckStackOverflow
    //     0xbd1838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd183c: cmp             SP, x16
    //     0xbd1840: b.ls            #0xbd1b3c
    // 0xbd1844: mov             x0, x3
    // 0xbd1848: r2 = Null
    //     0xbd1848: mov             x2, NULL
    // 0xbd184c: r1 = Null
    //     0xbd184c: mov             x1, NULL
    // 0xbd1850: r4 = 60
    //     0xbd1850: movz            x4, #0x3c
    // 0xbd1854: branchIfSmi(r0, 0xbd1860)
    //     0xbd1854: tbz             w0, #0, #0xbd1860
    // 0xbd1858: r4 = LoadClassIdInstr(r0)
    //     0xbd1858: ldur            x4, [x0, #-1]
    //     0xbd185c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd1860: cmp             x4, #0x47c
    // 0xbd1864: b.eq            #0xbd187c
    // 0xbd1868: r8 = Diacritic
    //     0xbd1868: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b418] Type: Diacritic
    //     0xbd186c: ldr             x8, [x8, #0x418]
    // 0xbd1870: r3 = Null
    //     0xbd1870: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b420] Null
    //     0xbd1874: ldr             x3, [x3, #0x420]
    // 0xbd1878: r0 = Diacritic()
    //     0xbd1878: bl              #0x6fbbb4  ; IsType_Diacritic_Stub
    // 0xbd187c: ldur            x0, [fp, #-8]
    // 0xbd1880: LoadField: r1 = r0->field_b
    //     0xbd1880: ldur            w1, [x0, #0xb]
    // 0xbd1884: DecompressPointer r1
    //     0xbd1884: add             x1, x1, HEAP, lsl #32
    // 0xbd1888: LoadField: r2 = r1->field_13
    //     0xbd1888: ldur            w2, [x1, #0x13]
    // 0xbd188c: LoadField: r1 = r0->field_13
    //     0xbd188c: ldur            x1, [x0, #0x13]
    // 0xbd1890: r3 = LoadInt32Instr(r2)
    //     0xbd1890: sbfx            x3, x2, #1, #0x1f
    // 0xbd1894: sub             x2, x3, x1
    // 0xbd1898: cmp             x2, #1
    // 0xbd189c: b.ge            #0xbd18ac
    // 0xbd18a0: mov             x1, x0
    // 0xbd18a4: r2 = 1
    //     0xbd18a4: movz            x2, #0x1
    // 0xbd18a8: r0 = _increaseBufferSize()
    //     0xbd18a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd18ac: ldur            x3, [fp, #-8]
    // 0xbd18b0: r2 = 4
    //     0xbd18b0: movz            x2, #0x4
    // 0xbd18b4: LoadField: r4 = r3->field_b
    //     0xbd18b4: ldur            w4, [x3, #0xb]
    // 0xbd18b8: DecompressPointer r4
    //     0xbd18b8: add             x4, x4, HEAP, lsl #32
    // 0xbd18bc: LoadField: r5 = r3->field_13
    //     0xbd18bc: ldur            x5, [x3, #0x13]
    // 0xbd18c0: add             x6, x5, #1
    // 0xbd18c4: StoreField: r3->field_13 = r6
    //     0xbd18c4: stur            x6, [x3, #0x13]
    // 0xbd18c8: LoadField: r0 = r4->field_13
    //     0xbd18c8: ldur            w0, [x4, #0x13]
    // 0xbd18cc: r7 = LoadInt32Instr(r0)
    //     0xbd18cc: sbfx            x7, x0, #1, #0x1f
    // 0xbd18d0: mov             x0, x7
    // 0xbd18d4: mov             x1, x5
    // 0xbd18d8: cmp             x1, x0
    // 0xbd18dc: b.hs            #0xbd1b44
    // 0xbd18e0: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd18e0: add             x0, x4, x5
    //     0xbd18e4: strb            w2, [x0, #0x17]
    // 0xbd18e8: sub             x0, x7, x6
    // 0xbd18ec: cmp             x0, #1
    // 0xbd18f0: b.ge            #0xbd1900
    // 0xbd18f4: mov             x1, x3
    // 0xbd18f8: r2 = 1
    //     0xbd18f8: movz            x2, #0x1
    // 0xbd18fc: r0 = _increaseBufferSize()
    //     0xbd18fc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1900: ldur            x2, [fp, #-8]
    // 0xbd1904: ldur            x3, [fp, #-0x10]
    // 0xbd1908: LoadField: r4 = r2->field_b
    //     0xbd1908: ldur            w4, [x2, #0xb]
    // 0xbd190c: DecompressPointer r4
    //     0xbd190c: add             x4, x4, HEAP, lsl #32
    // 0xbd1910: LoadField: r5 = r2->field_13
    //     0xbd1910: ldur            x5, [x2, #0x13]
    // 0xbd1914: add             x0, x5, #1
    // 0xbd1918: StoreField: r2->field_13 = r0
    //     0xbd1918: stur            x0, [x2, #0x13]
    // 0xbd191c: LoadField: r0 = r4->field_13
    //     0xbd191c: ldur            w0, [x4, #0x13]
    // 0xbd1920: r1 = LoadInt32Instr(r0)
    //     0xbd1920: sbfx            x1, x0, #1, #0x1f
    // 0xbd1924: mov             x0, x1
    // 0xbd1928: mov             x1, x5
    // 0xbd192c: cmp             x1, x0
    // 0xbd1930: b.hs            #0xbd1b48
    // 0xbd1934: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd1934: add             x0, x4, x5
    //     0xbd1938: strb            wzr, [x0, #0x17]
    // 0xbd193c: LoadField: r4 = r3->field_7
    //     0xbd193c: ldur            x4, [x3, #7]
    // 0xbd1940: r0 = BoxInt64Instr(r4)
    //     0xbd1940: sbfiz           x0, x4, #1, #0x1f
    //     0xbd1944: cmp             x4, x0, asr #1
    //     0xbd1948: b.eq            #0xbd1954
    //     0xbd194c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd1950: stur            x4, [x0, #7]
    // 0xbd1954: r16 = <int>
    //     0xbd1954: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd1958: stp             x2, x16, [SP, #8]
    // 0xbd195c: str             x0, [SP]
    // 0xbd1960: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1960: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1964: r0 = write()
    //     0xbd1964: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1968: ldur            x0, [fp, #-8]
    // 0xbd196c: LoadField: r1 = r0->field_b
    //     0xbd196c: ldur            w1, [x0, #0xb]
    // 0xbd1970: DecompressPointer r1
    //     0xbd1970: add             x1, x1, HEAP, lsl #32
    // 0xbd1974: LoadField: r2 = r1->field_13
    //     0xbd1974: ldur            w2, [x1, #0x13]
    // 0xbd1978: LoadField: r1 = r0->field_13
    //     0xbd1978: ldur            x1, [x0, #0x13]
    // 0xbd197c: r3 = LoadInt32Instr(r2)
    //     0xbd197c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1980: sub             x2, x3, x1
    // 0xbd1984: cmp             x2, #1
    // 0xbd1988: b.ge            #0xbd1998
    // 0xbd198c: mov             x1, x0
    // 0xbd1990: r2 = 1
    //     0xbd1990: movz            x2, #0x1
    // 0xbd1994: r0 = _increaseBufferSize()
    //     0xbd1994: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1998: ldur            x2, [fp, #-8]
    // 0xbd199c: ldur            x3, [fp, #-0x10]
    // 0xbd19a0: r4 = 1
    //     0xbd19a0: movz            x4, #0x1
    // 0xbd19a4: LoadField: r5 = r2->field_b
    //     0xbd19a4: ldur            w5, [x2, #0xb]
    // 0xbd19a8: DecompressPointer r5
    //     0xbd19a8: add             x5, x5, HEAP, lsl #32
    // 0xbd19ac: LoadField: r6 = r2->field_13
    //     0xbd19ac: ldur            x6, [x2, #0x13]
    // 0xbd19b0: add             x0, x6, #1
    // 0xbd19b4: StoreField: r2->field_13 = r0
    //     0xbd19b4: stur            x0, [x2, #0x13]
    // 0xbd19b8: LoadField: r0 = r5->field_13
    //     0xbd19b8: ldur            w0, [x5, #0x13]
    // 0xbd19bc: r1 = LoadInt32Instr(r0)
    //     0xbd19bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd19c0: mov             x0, x1
    // 0xbd19c4: mov             x1, x6
    // 0xbd19c8: cmp             x1, x0
    // 0xbd19cc: b.hs            #0xbd1b4c
    // 0xbd19d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd19d0: add             x0, x5, x6
    //     0xbd19d4: strb            w4, [x0, #0x17]
    // 0xbd19d8: LoadField: r5 = r3->field_f
    //     0xbd19d8: ldur            x5, [x3, #0xf]
    // 0xbd19dc: r0 = BoxInt64Instr(r5)
    //     0xbd19dc: sbfiz           x0, x5, #1, #0x1f
    //     0xbd19e0: cmp             x5, x0, asr #1
    //     0xbd19e4: b.eq            #0xbd19f0
    //     0xbd19e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd19ec: stur            x5, [x0, #7]
    // 0xbd19f0: r16 = <int>
    //     0xbd19f0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd19f4: stp             x2, x16, [SP, #8]
    // 0xbd19f8: str             x0, [SP]
    // 0xbd19fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd19fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1a00: r0 = write()
    //     0xbd1a00: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1a04: ldur            x0, [fp, #-8]
    // 0xbd1a08: LoadField: r1 = r0->field_b
    //     0xbd1a08: ldur            w1, [x0, #0xb]
    // 0xbd1a0c: DecompressPointer r1
    //     0xbd1a0c: add             x1, x1, HEAP, lsl #32
    // 0xbd1a10: LoadField: r2 = r1->field_13
    //     0xbd1a10: ldur            w2, [x1, #0x13]
    // 0xbd1a14: LoadField: r1 = r0->field_13
    //     0xbd1a14: ldur            x1, [x0, #0x13]
    // 0xbd1a18: r3 = LoadInt32Instr(r2)
    //     0xbd1a18: sbfx            x3, x2, #1, #0x1f
    // 0xbd1a1c: sub             x2, x3, x1
    // 0xbd1a20: cmp             x2, #1
    // 0xbd1a24: b.ge            #0xbd1a34
    // 0xbd1a28: mov             x1, x0
    // 0xbd1a2c: r2 = 1
    //     0xbd1a2c: movz            x2, #0x1
    // 0xbd1a30: r0 = _increaseBufferSize()
    //     0xbd1a30: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1a34: ldur            x2, [fp, #-8]
    // 0xbd1a38: ldur            x3, [fp, #-0x10]
    // 0xbd1a3c: r4 = 2
    //     0xbd1a3c: movz            x4, #0x2
    // 0xbd1a40: LoadField: r5 = r2->field_b
    //     0xbd1a40: ldur            w5, [x2, #0xb]
    // 0xbd1a44: DecompressPointer r5
    //     0xbd1a44: add             x5, x5, HEAP, lsl #32
    // 0xbd1a48: LoadField: r6 = r2->field_13
    //     0xbd1a48: ldur            x6, [x2, #0x13]
    // 0xbd1a4c: add             x0, x6, #1
    // 0xbd1a50: StoreField: r2->field_13 = r0
    //     0xbd1a50: stur            x0, [x2, #0x13]
    // 0xbd1a54: LoadField: r0 = r5->field_13
    //     0xbd1a54: ldur            w0, [x5, #0x13]
    // 0xbd1a58: r1 = LoadInt32Instr(r0)
    //     0xbd1a58: sbfx            x1, x0, #1, #0x1f
    // 0xbd1a5c: mov             x0, x1
    // 0xbd1a60: mov             x1, x6
    // 0xbd1a64: cmp             x1, x0
    // 0xbd1a68: b.hs            #0xbd1b50
    // 0xbd1a6c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1a6c: add             x0, x5, x6
    //     0xbd1a70: strb            w4, [x0, #0x17]
    // 0xbd1a74: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd1a74: ldur            x4, [x3, #0x17]
    // 0xbd1a78: r0 = BoxInt64Instr(r4)
    //     0xbd1a78: sbfiz           x0, x4, #1, #0x1f
    //     0xbd1a7c: cmp             x4, x0, asr #1
    //     0xbd1a80: b.eq            #0xbd1a8c
    //     0xbd1a84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd1a88: stur            x4, [x0, #7]
    // 0xbd1a8c: r16 = <int>
    //     0xbd1a8c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd1a90: stp             x2, x16, [SP, #8]
    // 0xbd1a94: str             x0, [SP]
    // 0xbd1a98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1a98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1a9c: r0 = write()
    //     0xbd1a9c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1aa0: ldur            x0, [fp, #-8]
    // 0xbd1aa4: LoadField: r1 = r0->field_b
    //     0xbd1aa4: ldur            w1, [x0, #0xb]
    // 0xbd1aa8: DecompressPointer r1
    //     0xbd1aa8: add             x1, x1, HEAP, lsl #32
    // 0xbd1aac: LoadField: r2 = r1->field_13
    //     0xbd1aac: ldur            w2, [x1, #0x13]
    // 0xbd1ab0: LoadField: r1 = r0->field_13
    //     0xbd1ab0: ldur            x1, [x0, #0x13]
    // 0xbd1ab4: r3 = LoadInt32Instr(r2)
    //     0xbd1ab4: sbfx            x3, x2, #1, #0x1f
    // 0xbd1ab8: sub             x2, x3, x1
    // 0xbd1abc: cmp             x2, #1
    // 0xbd1ac0: b.ge            #0xbd1ad0
    // 0xbd1ac4: mov             x1, x0
    // 0xbd1ac8: r2 = 1
    //     0xbd1ac8: movz            x2, #0x1
    // 0xbd1acc: r0 = _increaseBufferSize()
    //     0xbd1acc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1ad0: ldur            x2, [fp, #-8]
    // 0xbd1ad4: ldur            x3, [fp, #-0x10]
    // 0xbd1ad8: r4 = 3
    //     0xbd1ad8: movz            x4, #0x3
    // 0xbd1adc: LoadField: r5 = r2->field_b
    //     0xbd1adc: ldur            w5, [x2, #0xb]
    // 0xbd1ae0: DecompressPointer r5
    //     0xbd1ae0: add             x5, x5, HEAP, lsl #32
    // 0xbd1ae4: LoadField: r6 = r2->field_13
    //     0xbd1ae4: ldur            x6, [x2, #0x13]
    // 0xbd1ae8: add             x0, x6, #1
    // 0xbd1aec: StoreField: r2->field_13 = r0
    //     0xbd1aec: stur            x0, [x2, #0x13]
    // 0xbd1af0: LoadField: r0 = r5->field_13
    //     0xbd1af0: ldur            w0, [x5, #0x13]
    // 0xbd1af4: r1 = LoadInt32Instr(r0)
    //     0xbd1af4: sbfx            x1, x0, #1, #0x1f
    // 0xbd1af8: mov             x0, x1
    // 0xbd1afc: mov             x1, x6
    // 0xbd1b00: cmp             x1, x0
    // 0xbd1b04: b.hs            #0xbd1b54
    // 0xbd1b08: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1b08: add             x0, x5, x6
    //     0xbd1b0c: strb            w4, [x0, #0x17]
    // 0xbd1b10: LoadField: r0 = r3->field_1f
    //     0xbd1b10: ldur            w0, [x3, #0x1f]
    // 0xbd1b14: DecompressPointer r0
    //     0xbd1b14: add             x0, x0, HEAP, lsl #32
    // 0xbd1b18: r16 = <String>
    //     0xbd1b18: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd1b1c: stp             x2, x16, [SP, #8]
    // 0xbd1b20: str             x0, [SP]
    // 0xbd1b24: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1b24: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1b28: r0 = write()
    //     0xbd1b28: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1b2c: r0 = Null
    //     0xbd1b2c: mov             x0, NULL
    // 0xbd1b30: LeaveFrame
    //     0xbd1b30: mov             SP, fp
    //     0xbd1b34: ldp             fp, lr, [SP], #0x10
    // 0xbd1b38: ret
    //     0xbd1b38: ret             
    // 0xbd1b3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd1b3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd1b40: b               #0xbd1844
    // 0xbd1b44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1b44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1b48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1b48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1b4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1b4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1b50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1b50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd1b54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd1b54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf00b4, size: 0x24
    // 0xbf00b4: r1 = 56
    //     0xbf00b4: movz            x1, #0x38
    // 0xbf00b8: r16 = LoadInt32Instr(r1)
    //     0xbf00b8: sbfx            x16, x1, #1, #0x1f
    // 0xbf00bc: r17 = 11601
    //     0xbf00bc: movz            x17, #0x2d51
    // 0xbf00c0: mul             x0, x16, x17
    // 0xbf00c4: umulh           x16, x16, x17
    // 0xbf00c8: eor             x0, x0, x16
    // 0xbf00cc: r0 = 0
    //     0xbf00cc: eor             x0, x0, x0, lsr #32
    // 0xbf00d0: ubfiz           x0, x0, #1, #0x1e
    // 0xbf00d4: ret
    //     0xbf00d4: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76380, size: 0x9c
    // 0xd76380: EnterFrame
    //     0xd76380: stp             fp, lr, [SP, #-0x10]!
    //     0xd76384: mov             fp, SP
    // 0xd76388: AllocStack(0x10)
    //     0xd76388: sub             SP, SP, #0x10
    // 0xd7638c: CheckStackOverflow
    //     0xd7638c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76390: cmp             SP, x16
    //     0xd76394: b.ls            #0xd76414
    // 0xd76398: ldr             x0, [fp, #0x10]
    // 0xd7639c: cmp             w0, NULL
    // 0xd763a0: b.ne            #0xd763b4
    // 0xd763a4: r0 = false
    //     0xd763a4: add             x0, NULL, #0x30  ; false
    // 0xd763a8: LeaveFrame
    //     0xd763a8: mov             SP, fp
    //     0xd763ac: ldp             fp, lr, [SP], #0x10
    // 0xd763b0: ret
    //     0xd763b0: ret             
    // 0xd763b4: ldr             x1, [fp, #0x18]
    // 0xd763b8: cmp             w1, w0
    // 0xd763bc: b.ne            #0xd763c8
    // 0xd763c0: r0 = true
    //     0xd763c0: add             x0, NULL, #0x20  ; true
    // 0xd763c4: b               #0xd76408
    // 0xd763c8: r1 = 60
    //     0xd763c8: movz            x1, #0x3c
    // 0xd763cc: branchIfSmi(r0, 0xd763d8)
    //     0xd763cc: tbz             w0, #0, #0xd763d8
    // 0xd763d0: r1 = LoadClassIdInstr(r0)
    //     0xd763d0: ldur            x1, [x0, #-1]
    //     0xd763d4: ubfx            x1, x1, #0xc, #0x14
    // 0xd763d8: cmp             x1, #0x681
    // 0xd763dc: b.ne            #0xd76404
    // 0xd763e0: r16 = DiacriticAdapter
    //     0xd763e0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b410] Type: DiacriticAdapter
    //     0xd763e4: ldr             x16, [x16, #0x410]
    // 0xd763e8: r30 = DiacriticAdapter
    //     0xd763e8: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b410] Type: DiacriticAdapter
    //     0xd763ec: ldr             lr, [lr, #0x410]
    // 0xd763f0: stp             lr, x16, [SP]
    // 0xd763f4: r0 = ==()
    //     0xd763f4: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd763f8: tbnz            w0, #4, #0xd76404
    // 0xd763fc: r0 = true
    //     0xd763fc: add             x0, NULL, #0x20  ; true
    // 0xd76400: b               #0xd76408
    // 0xd76404: r0 = false
    //     0xd76404: add             x0, NULL, #0x30  ; false
    // 0xd76408: LeaveFrame
    //     0xd76408: mov             SP, fp
    //     0xd7640c: ldp             fp, lr, [SP], #0x10
    // 0xd76410: ret
    //     0xd76410: ret             
    // 0xd76414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76418: b               #0xd76398
  }
}
