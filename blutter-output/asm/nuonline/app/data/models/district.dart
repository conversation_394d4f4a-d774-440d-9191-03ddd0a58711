// lib: , url: package:nuonline/app/data/models/district.dart

// class id: 1050014, size: 0x8
class :: {
}

// class id: 1147, size: 0x14, field offset: 0x8
class District extends Object {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x8fe44c, size: 0x188
    // 0x8fe44c: EnterFrame
    //     0x8fe44c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe450: mov             fp, SP
    // 0x8fe454: AllocStack(0x20)
    //     0x8fe454: sub             SP, SP, #0x20
    // 0x8fe458: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8fe458: mov             x3, x1
    //     0x8fe45c: stur            x1, [fp, #-8]
    // 0x8fe460: CheckStackOverflow
    //     0x8fe460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fe464: cmp             SP, x16
    //     0x8fe468: b.ls            #0x8fe5cc
    // 0x8fe46c: mov             x0, x3
    // 0x8fe470: r2 = Null
    //     0x8fe470: mov             x2, NULL
    // 0x8fe474: r1 = Null
    //     0x8fe474: mov             x1, NULL
    // 0x8fe478: cmp             w0, NULL
    // 0x8fe47c: b.eq            #0x8fe520
    // 0x8fe480: branchIfSmi(r0, 0x8fe520)
    //     0x8fe480: tbz             w0, #0, #0x8fe520
    // 0x8fe484: r3 = LoadClassIdInstr(r0)
    //     0x8fe484: ldur            x3, [x0, #-1]
    //     0x8fe488: ubfx            x3, x3, #0xc, #0x14
    // 0x8fe48c: r17 = 6718
    //     0x8fe48c: movz            x17, #0x1a3e
    // 0x8fe490: cmp             x3, x17
    // 0x8fe494: b.eq            #0x8fe528
    // 0x8fe498: sub             x3, x3, #0x5a
    // 0x8fe49c: cmp             x3, #2
    // 0x8fe4a0: b.ls            #0x8fe528
    // 0x8fe4a4: r4 = LoadClassIdInstr(r0)
    //     0x8fe4a4: ldur            x4, [x0, #-1]
    //     0x8fe4a8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe4ac: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8fe4b0: ldr             x3, [x3, #0x18]
    // 0x8fe4b4: ldr             x3, [x3, x4, lsl #3]
    // 0x8fe4b8: LoadField: r3 = r3->field_2b
    //     0x8fe4b8: ldur            w3, [x3, #0x2b]
    // 0x8fe4bc: DecompressPointer r3
    //     0x8fe4bc: add             x3, x3, HEAP, lsl #32
    // 0x8fe4c0: cmp             w3, NULL
    // 0x8fe4c4: b.eq            #0x8fe520
    // 0x8fe4c8: LoadField: r3 = r3->field_f
    //     0x8fe4c8: ldur            w3, [x3, #0xf]
    // 0x8fe4cc: lsr             x3, x3, #3
    // 0x8fe4d0: r17 = 6718
    //     0x8fe4d0: movz            x17, #0x1a3e
    // 0x8fe4d4: cmp             x3, x17
    // 0x8fe4d8: b.eq            #0x8fe528
    // 0x8fe4dc: r3 = SubtypeTestCache
    //     0x8fe4dc: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f0c8] SubtypeTestCache
    //     0x8fe4e0: ldr             x3, [x3, #0xc8]
    // 0x8fe4e4: r30 = Subtype1TestCacheStub
    //     0x8fe4e4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8fe4e8: LoadField: r30 = r30->field_7
    //     0x8fe4e8: ldur            lr, [lr, #7]
    // 0x8fe4ec: blr             lr
    // 0x8fe4f0: cmp             w7, NULL
    // 0x8fe4f4: b.eq            #0x8fe500
    // 0x8fe4f8: tbnz            w7, #4, #0x8fe520
    // 0x8fe4fc: b               #0x8fe528
    // 0x8fe500: r8 = List
    //     0x8fe500: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3f0d0] Type: List
    //     0x8fe504: ldr             x8, [x8, #0xd0]
    // 0x8fe508: r3 = SubtypeTestCache
    //     0x8fe508: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f0d8] SubtypeTestCache
    //     0x8fe50c: ldr             x3, [x3, #0xd8]
    // 0x8fe510: r30 = InstanceOfStub
    //     0x8fe510: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8fe514: LoadField: r30 = r30->field_7
    //     0x8fe514: ldur            lr, [lr, #7]
    // 0x8fe518: blr             lr
    // 0x8fe51c: b               #0x8fe52c
    // 0x8fe520: r0 = false
    //     0x8fe520: add             x0, NULL, #0x30  ; false
    // 0x8fe524: b               #0x8fe52c
    // 0x8fe528: r0 = true
    //     0x8fe528: add             x0, NULL, #0x20  ; true
    // 0x8fe52c: tbnz            w0, #4, #0x8fe5b0
    // 0x8fe530: ldur            x0, [fp, #-8]
    // 0x8fe534: r1 = Function '<anonymous closure>': static.
    //     0x8fe534: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f0e0] AnonymousClosure: static (0x8fe5d4), in [package:nuonline/app/data/models/district.dart] District::fromResponse (0x8fe44c)
    //     0x8fe538: ldr             x1, [x1, #0xe0]
    // 0x8fe53c: r2 = Null
    //     0x8fe53c: mov             x2, NULL
    // 0x8fe540: r0 = AllocateClosure()
    //     0x8fe540: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fe544: mov             x1, x0
    // 0x8fe548: ldur            x0, [fp, #-8]
    // 0x8fe54c: r2 = LoadClassIdInstr(r0)
    //     0x8fe54c: ldur            x2, [x0, #-1]
    //     0x8fe550: ubfx            x2, x2, #0xc, #0x14
    // 0x8fe554: r16 = <District>
    //     0x8fe554: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ad8] TypeArguments: <District>
    //     0x8fe558: ldr             x16, [x16, #0xad8]
    // 0x8fe55c: stp             x0, x16, [SP, #8]
    // 0x8fe560: str             x1, [SP]
    // 0x8fe564: mov             x0, x2
    // 0x8fe568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fe568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fe56c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fe56c: movz            x17, #0xf28c
    //     0x8fe570: add             lr, x0, x17
    //     0x8fe574: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe578: blr             lr
    // 0x8fe57c: r1 = LoadClassIdInstr(r0)
    //     0x8fe57c: ldur            x1, [x0, #-1]
    //     0x8fe580: ubfx            x1, x1, #0xc, #0x14
    // 0x8fe584: mov             x16, x0
    // 0x8fe588: mov             x0, x1
    // 0x8fe58c: mov             x1, x16
    // 0x8fe590: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fe590: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fe594: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fe594: movz            x17, #0xd889
    //     0x8fe598: add             lr, x0, x17
    //     0x8fe59c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe5a0: blr             lr
    // 0x8fe5a4: LeaveFrame
    //     0x8fe5a4: mov             SP, fp
    //     0x8fe5a8: ldp             fp, lr, [SP], #0x10
    // 0x8fe5ac: ret
    //     0x8fe5ac: ret             
    // 0x8fe5b0: r1 = <District>
    //     0x8fe5b0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ad8] TypeArguments: <District>
    //     0x8fe5b4: ldr             x1, [x1, #0xad8]
    // 0x8fe5b8: r2 = 0
    //     0x8fe5b8: movz            x2, #0
    // 0x8fe5bc: r0 = _GrowableList()
    //     0x8fe5bc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fe5c0: LeaveFrame
    //     0x8fe5c0: mov             SP, fp
    //     0x8fe5c4: ldp             fp, lr, [SP], #0x10
    // 0x8fe5c8: ret
    //     0x8fe5c8: ret             
    // 0x8fe5cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fe5cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fe5d0: b               #0x8fe46c
  }
  [closure] static District <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fe5d4, size: 0x50
    // 0x8fe5d4: EnterFrame
    //     0x8fe5d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe5d8: mov             fp, SP
    // 0x8fe5dc: CheckStackOverflow
    //     0x8fe5dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fe5e0: cmp             SP, x16
    //     0x8fe5e4: b.ls            #0x8fe61c
    // 0x8fe5e8: ldr             x0, [fp, #0x10]
    // 0x8fe5ec: r2 = Null
    //     0x8fe5ec: mov             x2, NULL
    // 0x8fe5f0: r1 = Null
    //     0x8fe5f0: mov             x1, NULL
    // 0x8fe5f4: r8 = Map<String, dynamic>
    //     0x8fe5f4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fe5f8: r3 = Null
    //     0x8fe5f8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f0e8] Null
    //     0x8fe5fc: ldr             x3, [x3, #0xe8]
    // 0x8fe600: r0 = Map<String, dynamic>()
    //     0x8fe600: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fe604: ldr             x2, [fp, #0x10]
    // 0x8fe608: r1 = Null
    //     0x8fe608: mov             x1, NULL
    // 0x8fe60c: r0 = District.fromMap()
    //     0x8fe60c: bl              #0x8fe624  ; [package:nuonline/app/data/models/district.dart] District::District.fromMap
    // 0x8fe610: LeaveFrame
    //     0x8fe610: mov             SP, fp
    //     0x8fe614: ldp             fp, lr, [SP], #0x10
    // 0x8fe618: ret
    //     0x8fe618: ret             
    // 0x8fe61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fe61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fe620: b               #0x8fe5e8
  }
  factory _ District.fromMap(/* No info */) {
    // ** addr: 0x8fe624, size: 0x1b4
    // 0x8fe624: EnterFrame
    //     0x8fe624: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe628: mov             fp, SP
    // 0x8fe62c: AllocStack(0x20)
    //     0x8fe62c: sub             SP, SP, #0x20
    // 0x8fe630: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8fe630: mov             x3, x2
    //     0x8fe634: stur            x2, [fp, #-8]
    // 0x8fe638: CheckStackOverflow
    //     0x8fe638: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fe63c: cmp             SP, x16
    //     0x8fe640: b.ls            #0x8fe7d0
    // 0x8fe644: r0 = LoadClassIdInstr(r3)
    //     0x8fe644: ldur            x0, [x3, #-1]
    //     0x8fe648: ubfx            x0, x0, #0xc, #0x14
    // 0x8fe64c: mov             x1, x3
    // 0x8fe650: r2 = "id"
    //     0x8fe650: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fe654: ldr             x2, [x2, #0x740]
    // 0x8fe658: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fe658: sub             lr, x0, #0x114
    //     0x8fe65c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe660: blr             lr
    // 0x8fe664: mov             x3, x0
    // 0x8fe668: r2 = Null
    //     0x8fe668: mov             x2, NULL
    // 0x8fe66c: r1 = Null
    //     0x8fe66c: mov             x1, NULL
    // 0x8fe670: stur            x3, [fp, #-0x10]
    // 0x8fe674: branchIfSmi(r0, 0x8fe69c)
    //     0x8fe674: tbz             w0, #0, #0x8fe69c
    // 0x8fe678: r4 = LoadClassIdInstr(r0)
    //     0x8fe678: ldur            x4, [x0, #-1]
    //     0x8fe67c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe680: sub             x4, x4, #0x3c
    // 0x8fe684: cmp             x4, #1
    // 0x8fe688: b.ls            #0x8fe69c
    // 0x8fe68c: r8 = int
    //     0x8fe68c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fe690: r3 = Null
    //     0x8fe690: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f0f8] Null
    //     0x8fe694: ldr             x3, [x3, #0xf8]
    // 0x8fe698: r0 = int()
    //     0x8fe698: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fe69c: ldur            x3, [fp, #-8]
    // 0x8fe6a0: r0 = LoadClassIdInstr(r3)
    //     0x8fe6a0: ldur            x0, [x3, #-1]
    //     0x8fe6a4: ubfx            x0, x0, #0xc, #0x14
    // 0x8fe6a8: mov             x1, x3
    // 0x8fe6ac: r2 = "name"
    //     0x8fe6ac: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8fe6b0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fe6b0: sub             lr, x0, #0x114
    //     0x8fe6b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe6b8: blr             lr
    // 0x8fe6bc: mov             x3, x0
    // 0x8fe6c0: r2 = Null
    //     0x8fe6c0: mov             x2, NULL
    // 0x8fe6c4: r1 = Null
    //     0x8fe6c4: mov             x1, NULL
    // 0x8fe6c8: stur            x3, [fp, #-0x18]
    // 0x8fe6cc: r4 = 60
    //     0x8fe6cc: movz            x4, #0x3c
    // 0x8fe6d0: branchIfSmi(r0, 0x8fe6dc)
    //     0x8fe6d0: tbz             w0, #0, #0x8fe6dc
    // 0x8fe6d4: r4 = LoadClassIdInstr(r0)
    //     0x8fe6d4: ldur            x4, [x0, #-1]
    //     0x8fe6d8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe6dc: sub             x4, x4, #0x5e
    // 0x8fe6e0: cmp             x4, #1
    // 0x8fe6e4: b.ls            #0x8fe6f8
    // 0x8fe6e8: r8 = String
    //     0x8fe6e8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fe6ec: r3 = Null
    //     0x8fe6ec: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f108] Null
    //     0x8fe6f0: ldr             x3, [x3, #0x108]
    // 0x8fe6f4: r0 = String()
    //     0x8fe6f4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fe6f8: ldur            x3, [fp, #-8]
    // 0x8fe6fc: r0 = LoadClassIdInstr(r3)
    //     0x8fe6fc: ldur            x0, [x3, #-1]
    //     0x8fe700: ubfx            x0, x0, #0xc, #0x14
    // 0x8fe704: mov             x1, x3
    // 0x8fe708: r2 = "province_id"
    //     0x8fe708: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x8fe70c: ldr             x2, [x2, #0x100]
    // 0x8fe710: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fe710: sub             lr, x0, #0x114
    //     0x8fe714: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe718: blr             lr
    // 0x8fe71c: r2 = Null
    //     0x8fe71c: mov             x2, NULL
    // 0x8fe720: r1 = Null
    //     0x8fe720: mov             x1, NULL
    // 0x8fe724: branchIfSmi(r0, 0x8fe74c)
    //     0x8fe724: tbz             w0, #0, #0x8fe74c
    // 0x8fe728: r4 = LoadClassIdInstr(r0)
    //     0x8fe728: ldur            x4, [x0, #-1]
    //     0x8fe72c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe730: sub             x4, x4, #0x3c
    // 0x8fe734: cmp             x4, #1
    // 0x8fe738: b.ls            #0x8fe74c
    // 0x8fe73c: r8 = int
    //     0x8fe73c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fe740: r3 = Null
    //     0x8fe740: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f118] Null
    //     0x8fe744: ldr             x3, [x3, #0x118]
    // 0x8fe748: r0 = int()
    //     0x8fe748: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fe74c: ldur            x1, [fp, #-8]
    // 0x8fe750: r0 = LoadClassIdInstr(r1)
    //     0x8fe750: ldur            x0, [x1, #-1]
    //     0x8fe754: ubfx            x0, x0, #0xc, #0x14
    // 0x8fe758: r2 = "regency_id"
    //     0x8fe758: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x8fe75c: ldr             x2, [x2, #0x118]
    // 0x8fe760: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fe760: sub             lr, x0, #0x114
    //     0x8fe764: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe768: blr             lr
    // 0x8fe76c: r2 = Null
    //     0x8fe76c: mov             x2, NULL
    // 0x8fe770: r1 = Null
    //     0x8fe770: mov             x1, NULL
    // 0x8fe774: branchIfSmi(r0, 0x8fe79c)
    //     0x8fe774: tbz             w0, #0, #0x8fe79c
    // 0x8fe778: r4 = LoadClassIdInstr(r0)
    //     0x8fe778: ldur            x4, [x0, #-1]
    //     0x8fe77c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe780: sub             x4, x4, #0x3c
    // 0x8fe784: cmp             x4, #1
    // 0x8fe788: b.ls            #0x8fe79c
    // 0x8fe78c: r8 = int
    //     0x8fe78c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fe790: r3 = Null
    //     0x8fe790: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f128] Null
    //     0x8fe794: ldr             x3, [x3, #0x128]
    // 0x8fe798: r0 = int()
    //     0x8fe798: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fe79c: ldur            x0, [fp, #-0x10]
    // 0x8fe7a0: r1 = LoadInt32Instr(r0)
    //     0x8fe7a0: sbfx            x1, x0, #1, #0x1f
    //     0x8fe7a4: tbz             w0, #0, #0x8fe7ac
    //     0x8fe7a8: ldur            x1, [x0, #7]
    // 0x8fe7ac: stur            x1, [fp, #-0x20]
    // 0x8fe7b0: r0 = District()
    //     0x8fe7b0: bl              #0x8fe7d8  ; AllocateDistrictStub -> District (size=0x14)
    // 0x8fe7b4: ldur            x1, [fp, #-0x20]
    // 0x8fe7b8: StoreField: r0->field_7 = r1
    //     0x8fe7b8: stur            x1, [x0, #7]
    // 0x8fe7bc: ldur            x1, [fp, #-0x18]
    // 0x8fe7c0: StoreField: r0->field_f = r1
    //     0x8fe7c0: stur            w1, [x0, #0xf]
    // 0x8fe7c4: LeaveFrame
    //     0x8fe7c4: mov             SP, fp
    //     0x8fe7c8: ldp             fp, lr, [SP], #0x10
    // 0x8fe7cc: ret
    //     0x8fe7cc: ret             
    // 0x8fe7d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fe7d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fe7d4: b               #0x8fe644
  }
}
