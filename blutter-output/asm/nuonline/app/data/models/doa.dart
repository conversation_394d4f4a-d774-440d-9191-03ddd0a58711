// lib: , url: package:nuonline/app/data/models/doa.dart

// class id: 1050015, size: 0x8
class :: {
}

// class id: 1662, size: 0x14, field offset: 0xc
class DoaSubCategoryAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa610f8, size: 0x57c
    // 0xa610f8: EnterFrame
    //     0xa610f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa610fc: mov             fp, SP
    // 0xa61100: AllocStack(0x68)
    //     0xa61100: sub             SP, SP, #0x68
    // 0xa61104: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa61104: stur            x2, [fp, #-0x20]
    // 0xa61108: CheckStackOverflow
    //     0xa61108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6110c: cmp             SP, x16
    //     0xa61110: b.ls            #0xa6165c
    // 0xa61114: LoadField: r3 = r2->field_23
    //     0xa61114: ldur            x3, [x2, #0x23]
    // 0xa61118: add             x0, x3, #1
    // 0xa6111c: LoadField: r1 = r2->field_1b
    //     0xa6111c: ldur            x1, [x2, #0x1b]
    // 0xa61120: cmp             x0, x1
    // 0xa61124: b.gt            #0xa61600
    // 0xa61128: LoadField: r4 = r2->field_7
    //     0xa61128: ldur            w4, [x2, #7]
    // 0xa6112c: DecompressPointer r4
    //     0xa6112c: add             x4, x4, HEAP, lsl #32
    // 0xa61130: stur            x4, [fp, #-0x18]
    // 0xa61134: StoreField: r2->field_23 = r0
    //     0xa61134: stur            x0, [x2, #0x23]
    // 0xa61138: LoadField: r0 = r4->field_13
    //     0xa61138: ldur            w0, [x4, #0x13]
    // 0xa6113c: r5 = LoadInt32Instr(r0)
    //     0xa6113c: sbfx            x5, x0, #1, #0x1f
    // 0xa61140: mov             x0, x5
    // 0xa61144: mov             x1, x3
    // 0xa61148: stur            x5, [fp, #-0x10]
    // 0xa6114c: cmp             x1, x0
    // 0xa61150: b.hs            #0xa61664
    // 0xa61154: LoadField: r0 = r4->field_7
    //     0xa61154: ldur            x0, [x4, #7]
    // 0xa61158: ldrb            w1, [x0, x3]
    // 0xa6115c: stur            x1, [fp, #-8]
    // 0xa61160: r16 = <int, dynamic>
    //     0xa61160: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa61164: ldr             x16, [x16, #0xac0]
    // 0xa61168: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6116c: stp             lr, x16, [SP]
    // 0xa61170: r0 = Map._fromLiteral()
    //     0xa61170: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa61174: mov             x2, x0
    // 0xa61178: stur            x2, [fp, #-0x38]
    // 0xa6117c: r6 = 0
    //     0xa6117c: movz            x6, #0
    // 0xa61180: ldur            x3, [fp, #-0x20]
    // 0xa61184: ldur            x4, [fp, #-0x18]
    // 0xa61188: ldur            x5, [fp, #-8]
    // 0xa6118c: stur            x6, [fp, #-0x30]
    // 0xa61190: CheckStackOverflow
    //     0xa61190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61194: cmp             SP, x16
    //     0xa61198: b.ls            #0xa61668
    // 0xa6119c: cmp             x6, x5
    // 0xa611a0: b.ge            #0xa6122c
    // 0xa611a4: LoadField: r7 = r3->field_23
    //     0xa611a4: ldur            x7, [x3, #0x23]
    // 0xa611a8: add             x0, x7, #1
    // 0xa611ac: LoadField: r1 = r3->field_1b
    //     0xa611ac: ldur            x1, [x3, #0x1b]
    // 0xa611b0: cmp             x0, x1
    // 0xa611b4: b.gt            #0xa61628
    // 0xa611b8: StoreField: r3->field_23 = r0
    //     0xa611b8: stur            x0, [x3, #0x23]
    // 0xa611bc: ldur            x0, [fp, #-0x10]
    // 0xa611c0: mov             x1, x7
    // 0xa611c4: cmp             x1, x0
    // 0xa611c8: b.hs            #0xa61670
    // 0xa611cc: LoadField: r0 = r4->field_7
    //     0xa611cc: ldur            x0, [x4, #7]
    // 0xa611d0: ldrb            w8, [x0, x7]
    // 0xa611d4: mov             x1, x3
    // 0xa611d8: stur            x8, [fp, #-0x28]
    // 0xa611dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa611dc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa611e0: r0 = read()
    //     0xa611e0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa611e4: mov             x1, x0
    // 0xa611e8: ldur            x0, [fp, #-0x28]
    // 0xa611ec: lsl             x2, x0, #1
    // 0xa611f0: r16 = LoadInt32Instr(r2)
    //     0xa611f0: sbfx            x16, x2, #1, #0x1f
    // 0xa611f4: r17 = 11601
    //     0xa611f4: movz            x17, #0x2d51
    // 0xa611f8: mul             x0, x16, x17
    // 0xa611fc: umulh           x16, x16, x17
    // 0xa61200: eor             x0, x0, x16
    // 0xa61204: r0 = 0
    //     0xa61204: eor             x0, x0, x0, lsr #32
    // 0xa61208: ubfiz           x0, x0, #1, #0x1e
    // 0xa6120c: r5 = LoadInt32Instr(r0)
    //     0xa6120c: sbfx            x5, x0, #1, #0x1f
    // 0xa61210: mov             x3, x1
    // 0xa61214: ldur            x1, [fp, #-0x38]
    // 0xa61218: r0 = _set()
    //     0xa61218: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa6121c: ldur            x0, [fp, #-0x30]
    // 0xa61220: add             x6, x0, #1
    // 0xa61224: ldur            x2, [fp, #-0x38]
    // 0xa61228: b               #0xa61180
    // 0xa6122c: mov             x0, x2
    // 0xa61230: mov             x1, x0
    // 0xa61234: r2 = 0
    //     0xa61234: movz            x2, #0
    // 0xa61238: r0 = _getValueOrData()
    //     0xa61238: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6123c: ldur            x3, [fp, #-0x38]
    // 0xa61240: LoadField: r1 = r3->field_f
    //     0xa61240: ldur            w1, [x3, #0xf]
    // 0xa61244: DecompressPointer r1
    //     0xa61244: add             x1, x1, HEAP, lsl #32
    // 0xa61248: cmp             w1, w0
    // 0xa6124c: b.ne            #0xa61258
    // 0xa61250: r4 = Null
    //     0xa61250: mov             x4, NULL
    // 0xa61254: b               #0xa6125c
    // 0xa61258: mov             x4, x0
    // 0xa6125c: mov             x0, x4
    // 0xa61260: stur            x4, [fp, #-0x18]
    // 0xa61264: r2 = Null
    //     0xa61264: mov             x2, NULL
    // 0xa61268: r1 = Null
    //     0xa61268: mov             x1, NULL
    // 0xa6126c: branchIfSmi(r0, 0xa61294)
    //     0xa6126c: tbz             w0, #0, #0xa61294
    // 0xa61270: r4 = LoadClassIdInstr(r0)
    //     0xa61270: ldur            x4, [x0, #-1]
    //     0xa61274: ubfx            x4, x4, #0xc, #0x14
    // 0xa61278: sub             x4, x4, #0x3c
    // 0xa6127c: cmp             x4, #1
    // 0xa61280: b.ls            #0xa61294
    // 0xa61284: r8 = int
    //     0xa61284: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa61288: r3 = Null
    //     0xa61288: add             x3, PP, #0x21, lsl #12  ; [pp+0x21488] Null
    //     0xa6128c: ldr             x3, [x3, #0x488]
    // 0xa61290: r0 = int()
    //     0xa61290: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa61294: ldur            x1, [fp, #-0x38]
    // 0xa61298: r2 = 2
    //     0xa61298: movz            x2, #0x2
    // 0xa6129c: r0 = _getValueOrData()
    //     0xa6129c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa612a0: ldur            x3, [fp, #-0x38]
    // 0xa612a4: LoadField: r1 = r3->field_f
    //     0xa612a4: ldur            w1, [x3, #0xf]
    // 0xa612a8: DecompressPointer r1
    //     0xa612a8: add             x1, x1, HEAP, lsl #32
    // 0xa612ac: cmp             w1, w0
    // 0xa612b0: b.ne            #0xa612bc
    // 0xa612b4: r4 = Null
    //     0xa612b4: mov             x4, NULL
    // 0xa612b8: b               #0xa612c0
    // 0xa612bc: mov             x4, x0
    // 0xa612c0: mov             x0, x4
    // 0xa612c4: stur            x4, [fp, #-0x20]
    // 0xa612c8: r2 = Null
    //     0xa612c8: mov             x2, NULL
    // 0xa612cc: r1 = Null
    //     0xa612cc: mov             x1, NULL
    // 0xa612d0: r4 = 60
    //     0xa612d0: movz            x4, #0x3c
    // 0xa612d4: branchIfSmi(r0, 0xa612e0)
    //     0xa612d4: tbz             w0, #0, #0xa612e0
    // 0xa612d8: r4 = LoadClassIdInstr(r0)
    //     0xa612d8: ldur            x4, [x0, #-1]
    //     0xa612dc: ubfx            x4, x4, #0xc, #0x14
    // 0xa612e0: sub             x4, x4, #0x5e
    // 0xa612e4: cmp             x4, #1
    // 0xa612e8: b.ls            #0xa612fc
    // 0xa612ec: r8 = String
    //     0xa612ec: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa612f0: r3 = Null
    //     0xa612f0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21498] Null
    //     0xa612f4: ldr             x3, [x3, #0x498]
    // 0xa612f8: r0 = String()
    //     0xa612f8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa612fc: ldur            x1, [fp, #-0x38]
    // 0xa61300: r2 = 4
    //     0xa61300: movz            x2, #0x4
    // 0xa61304: r0 = _getValueOrData()
    //     0xa61304: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61308: ldur            x3, [fp, #-0x38]
    // 0xa6130c: LoadField: r1 = r3->field_f
    //     0xa6130c: ldur            w1, [x3, #0xf]
    // 0xa61310: DecompressPointer r1
    //     0xa61310: add             x1, x1, HEAP, lsl #32
    // 0xa61314: cmp             w1, w0
    // 0xa61318: b.ne            #0xa61324
    // 0xa6131c: r4 = Null
    //     0xa6131c: mov             x4, NULL
    // 0xa61320: b               #0xa61328
    // 0xa61324: mov             x4, x0
    // 0xa61328: mov             x0, x4
    // 0xa6132c: stur            x4, [fp, #-0x40]
    // 0xa61330: r2 = Null
    //     0xa61330: mov             x2, NULL
    // 0xa61334: r1 = Null
    //     0xa61334: mov             x1, NULL
    // 0xa61338: branchIfSmi(r0, 0xa61360)
    //     0xa61338: tbz             w0, #0, #0xa61360
    // 0xa6133c: r4 = LoadClassIdInstr(r0)
    //     0xa6133c: ldur            x4, [x0, #-1]
    //     0xa61340: ubfx            x4, x4, #0xc, #0x14
    // 0xa61344: sub             x4, x4, #0x3c
    // 0xa61348: cmp             x4, #1
    // 0xa6134c: b.ls            #0xa61360
    // 0xa61350: r8 = int
    //     0xa61350: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa61354: r3 = Null
    //     0xa61354: add             x3, PP, #0x21, lsl #12  ; [pp+0x214a8] Null
    //     0xa61358: ldr             x3, [x3, #0x4a8]
    // 0xa6135c: r0 = int()
    //     0xa6135c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa61360: ldur            x1, [fp, #-0x38]
    // 0xa61364: r2 = 6
    //     0xa61364: movz            x2, #0x6
    // 0xa61368: r0 = _getValueOrData()
    //     0xa61368: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6136c: ldur            x3, [fp, #-0x38]
    // 0xa61370: LoadField: r1 = r3->field_f
    //     0xa61370: ldur            w1, [x3, #0xf]
    // 0xa61374: DecompressPointer r1
    //     0xa61374: add             x1, x1, HEAP, lsl #32
    // 0xa61378: cmp             w1, w0
    // 0xa6137c: b.ne            #0xa61388
    // 0xa61380: r4 = Null
    //     0xa61380: mov             x4, NULL
    // 0xa61384: b               #0xa6138c
    // 0xa61388: mov             x4, x0
    // 0xa6138c: mov             x0, x4
    // 0xa61390: stur            x4, [fp, #-0x48]
    // 0xa61394: r2 = Null
    //     0xa61394: mov             x2, NULL
    // 0xa61398: r1 = Null
    //     0xa61398: mov             x1, NULL
    // 0xa6139c: r4 = 60
    //     0xa6139c: movz            x4, #0x3c
    // 0xa613a0: branchIfSmi(r0, 0xa613ac)
    //     0xa613a0: tbz             w0, #0, #0xa613ac
    // 0xa613a4: r4 = LoadClassIdInstr(r0)
    //     0xa613a4: ldur            x4, [x0, #-1]
    //     0xa613a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa613ac: sub             x4, x4, #0x5e
    // 0xa613b0: cmp             x4, #1
    // 0xa613b4: b.ls            #0xa613c8
    // 0xa613b8: r8 = String?
    //     0xa613b8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa613bc: r3 = Null
    //     0xa613bc: add             x3, PP, #0x21, lsl #12  ; [pp+0x214b8] Null
    //     0xa613c0: ldr             x3, [x3, #0x4b8]
    // 0xa613c4: r0 = String?()
    //     0xa613c4: bl              #0x600324  ; IsType_String?_Stub
    // 0xa613c8: ldur            x1, [fp, #-0x38]
    // 0xa613cc: r2 = 8
    //     0xa613cc: movz            x2, #0x8
    // 0xa613d0: r0 = _getValueOrData()
    //     0xa613d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa613d4: ldur            x3, [fp, #-0x38]
    // 0xa613d8: LoadField: r1 = r3->field_f
    //     0xa613d8: ldur            w1, [x3, #0xf]
    // 0xa613dc: DecompressPointer r1
    //     0xa613dc: add             x1, x1, HEAP, lsl #32
    // 0xa613e0: cmp             w1, w0
    // 0xa613e4: b.ne            #0xa613f0
    // 0xa613e8: r4 = Null
    //     0xa613e8: mov             x4, NULL
    // 0xa613ec: b               #0xa613f4
    // 0xa613f0: mov             x4, x0
    // 0xa613f4: mov             x0, x4
    // 0xa613f8: stur            x4, [fp, #-0x50]
    // 0xa613fc: r2 = Null
    //     0xa613fc: mov             x2, NULL
    // 0xa61400: r1 = Null
    //     0xa61400: mov             x1, NULL
    // 0xa61404: branchIfSmi(r0, 0xa6142c)
    //     0xa61404: tbz             w0, #0, #0xa6142c
    // 0xa61408: r4 = LoadClassIdInstr(r0)
    //     0xa61408: ldur            x4, [x0, #-1]
    //     0xa6140c: ubfx            x4, x4, #0xc, #0x14
    // 0xa61410: sub             x4, x4, #0x3c
    // 0xa61414: cmp             x4, #1
    // 0xa61418: b.ls            #0xa6142c
    // 0xa6141c: r8 = int?
    //     0xa6141c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xa61420: r3 = Null
    //     0xa61420: add             x3, PP, #0x21, lsl #12  ; [pp+0x214c8] Null
    //     0xa61424: ldr             x3, [x3, #0x4c8]
    // 0xa61428: r0 = int?()
    //     0xa61428: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xa6142c: ldur            x1, [fp, #-0x38]
    // 0xa61430: r2 = 10
    //     0xa61430: movz            x2, #0xa
    // 0xa61434: r0 = _getValueOrData()
    //     0xa61434: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61438: mov             x1, x0
    // 0xa6143c: ldur            x0, [fp, #-0x38]
    // 0xa61440: LoadField: r2 = r0->field_f
    //     0xa61440: ldur            w2, [x0, #0xf]
    // 0xa61444: DecompressPointer r2
    //     0xa61444: add             x2, x2, HEAP, lsl #32
    // 0xa61448: cmp             w2, w1
    // 0xa6144c: b.eq            #0xa61458
    // 0xa61450: cmp             w1, NULL
    // 0xa61454: b.ne            #0xa61460
    // 0xa61458: r3 = ""
    //     0xa61458: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa6145c: b               #0xa614d0
    // 0xa61460: mov             x1, x0
    // 0xa61464: r2 = 10
    //     0xa61464: movz            x2, #0xa
    // 0xa61468: r0 = _getValueOrData()
    //     0xa61468: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6146c: ldur            x3, [fp, #-0x38]
    // 0xa61470: LoadField: r1 = r3->field_f
    //     0xa61470: ldur            w1, [x3, #0xf]
    // 0xa61474: DecompressPointer r1
    //     0xa61474: add             x1, x1, HEAP, lsl #32
    // 0xa61478: cmp             w1, w0
    // 0xa6147c: b.ne            #0xa61488
    // 0xa61480: r4 = Null
    //     0xa61480: mov             x4, NULL
    // 0xa61484: b               #0xa6148c
    // 0xa61488: mov             x4, x0
    // 0xa6148c: mov             x0, x4
    // 0xa61490: stur            x4, [fp, #-0x58]
    // 0xa61494: r2 = Null
    //     0xa61494: mov             x2, NULL
    // 0xa61498: r1 = Null
    //     0xa61498: mov             x1, NULL
    // 0xa6149c: r4 = 60
    //     0xa6149c: movz            x4, #0x3c
    // 0xa614a0: branchIfSmi(r0, 0xa614ac)
    //     0xa614a0: tbz             w0, #0, #0xa614ac
    // 0xa614a4: r4 = LoadClassIdInstr(r0)
    //     0xa614a4: ldur            x4, [x0, #-1]
    //     0xa614a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa614ac: sub             x4, x4, #0x5e
    // 0xa614b0: cmp             x4, #1
    // 0xa614b4: b.ls            #0xa614c8
    // 0xa614b8: r8 = String
    //     0xa614b8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa614bc: r3 = Null
    //     0xa614bc: add             x3, PP, #0x21, lsl #12  ; [pp+0x214d8] Null
    //     0xa614c0: ldr             x3, [x3, #0x4d8]
    // 0xa614c4: r0 = String()
    //     0xa614c4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa614c8: ldur            x3, [fp, #-0x58]
    // 0xa614cc: ldur            x0, [fp, #-0x38]
    // 0xa614d0: mov             x1, x0
    // 0xa614d4: stur            x3, [fp, #-0x58]
    // 0xa614d8: r2 = 12
    //     0xa614d8: movz            x2, #0xc
    // 0xa614dc: r0 = _getValueOrData()
    //     0xa614dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa614e0: mov             x1, x0
    // 0xa614e4: ldur            x0, [fp, #-0x38]
    // 0xa614e8: LoadField: r2 = r0->field_f
    //     0xa614e8: ldur            w2, [x0, #0xf]
    // 0xa614ec: DecompressPointer r2
    //     0xa614ec: add             x2, x2, HEAP, lsl #32
    // 0xa614f0: cmp             w2, w1
    // 0xa614f4: b.eq            #0xa61500
    // 0xa614f8: cmp             w1, NULL
    // 0xa614fc: b.ne            #0xa61508
    // 0xa61500: r6 = ""
    //     0xa61500: ldr             x6, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa61504: b               #0xa61578
    // 0xa61508: mov             x1, x0
    // 0xa6150c: r2 = 12
    //     0xa6150c: movz            x2, #0xc
    // 0xa61510: r0 = _getValueOrData()
    //     0xa61510: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61514: mov             x1, x0
    // 0xa61518: ldur            x0, [fp, #-0x38]
    // 0xa6151c: LoadField: r2 = r0->field_f
    //     0xa6151c: ldur            w2, [x0, #0xf]
    // 0xa61520: DecompressPointer r2
    //     0xa61520: add             x2, x2, HEAP, lsl #32
    // 0xa61524: cmp             w2, w1
    // 0xa61528: b.ne            #0xa61534
    // 0xa6152c: r3 = Null
    //     0xa6152c: mov             x3, NULL
    // 0xa61530: b               #0xa61538
    // 0xa61534: mov             x3, x1
    // 0xa61538: mov             x0, x3
    // 0xa6153c: stur            x3, [fp, #-0x38]
    // 0xa61540: r2 = Null
    //     0xa61540: mov             x2, NULL
    // 0xa61544: r1 = Null
    //     0xa61544: mov             x1, NULL
    // 0xa61548: r4 = 60
    //     0xa61548: movz            x4, #0x3c
    // 0xa6154c: branchIfSmi(r0, 0xa61558)
    //     0xa6154c: tbz             w0, #0, #0xa61558
    // 0xa61550: r4 = LoadClassIdInstr(r0)
    //     0xa61550: ldur            x4, [x0, #-1]
    //     0xa61554: ubfx            x4, x4, #0xc, #0x14
    // 0xa61558: sub             x4, x4, #0x5e
    // 0xa6155c: cmp             x4, #1
    // 0xa61560: b.ls            #0xa61574
    // 0xa61564: r8 = String
    //     0xa61564: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa61568: r3 = Null
    //     0xa61568: add             x3, PP, #0x21, lsl #12  ; [pp+0x214e8] Null
    //     0xa6156c: ldr             x3, [x3, #0x4e8]
    // 0xa61570: r0 = String()
    //     0xa61570: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa61574: ldur            x6, [fp, #-0x38]
    // 0xa61578: ldur            x0, [fp, #-0x58]
    // 0xa6157c: ldur            x5, [fp, #-0x18]
    // 0xa61580: ldur            x4, [fp, #-0x20]
    // 0xa61584: ldur            x3, [fp, #-0x40]
    // 0xa61588: ldur            x2, [fp, #-0x48]
    // 0xa6158c: ldur            x1, [fp, #-0x50]
    // 0xa61590: stur            x6, [fp, #-0x38]
    // 0xa61594: r7 = LoadInt32Instr(r5)
    //     0xa61594: sbfx            x7, x5, #1, #0x1f
    //     0xa61598: tbz             w5, #0, #0xa615a0
    //     0xa6159c: ldur            x7, [x5, #7]
    // 0xa615a0: stur            x7, [fp, #-8]
    // 0xa615a4: r0 = DoaSubCategory()
    //     0xa615a4: bl              #0xa61674  ; AllocateDoaSubCategoryStub -> DoaSubCategory (size=0x2c)
    // 0xa615a8: mov             x1, x0
    // 0xa615ac: ldur            x0, [fp, #-8]
    // 0xa615b0: StoreField: r1->field_7 = r0
    //     0xa615b0: stur            x0, [x1, #7]
    // 0xa615b4: ldur            x0, [fp, #-0x20]
    // 0xa615b8: StoreField: r1->field_f = r0
    //     0xa615b8: stur            w0, [x1, #0xf]
    // 0xa615bc: ldur            x0, [fp, #-0x40]
    // 0xa615c0: r2 = LoadInt32Instr(r0)
    //     0xa615c0: sbfx            x2, x0, #1, #0x1f
    //     0xa615c4: tbz             w0, #0, #0xa615cc
    //     0xa615c8: ldur            x2, [x0, #7]
    // 0xa615cc: StoreField: r1->field_13 = r2
    //     0xa615cc: stur            x2, [x1, #0x13]
    // 0xa615d0: ldur            x0, [fp, #-0x48]
    // 0xa615d4: StoreField: r1->field_1b = r0
    //     0xa615d4: stur            w0, [x1, #0x1b]
    // 0xa615d8: ldur            x0, [fp, #-0x50]
    // 0xa615dc: StoreField: r1->field_1f = r0
    //     0xa615dc: stur            w0, [x1, #0x1f]
    // 0xa615e0: ldur            x0, [fp, #-0x58]
    // 0xa615e4: StoreField: r1->field_23 = r0
    //     0xa615e4: stur            w0, [x1, #0x23]
    // 0xa615e8: ldur            x0, [fp, #-0x38]
    // 0xa615ec: StoreField: r1->field_27 = r0
    //     0xa615ec: stur            w0, [x1, #0x27]
    // 0xa615f0: mov             x0, x1
    // 0xa615f4: LeaveFrame
    //     0xa615f4: mov             SP, fp
    //     0xa615f8: ldp             fp, lr, [SP], #0x10
    // 0xa615fc: ret
    //     0xa615fc: ret             
    // 0xa61600: r0 = RangeError()
    //     0xa61600: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61604: mov             x1, x0
    // 0xa61608: r0 = "Not enough bytes available."
    //     0xa61608: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6160c: ldr             x0, [x0, #0x8a8]
    // 0xa61610: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61610: stur            w0, [x1, #0x17]
    // 0xa61614: r2 = false
    //     0xa61614: add             x2, NULL, #0x30  ; false
    // 0xa61618: StoreField: r1->field_b = r2
    //     0xa61618: stur            w2, [x1, #0xb]
    // 0xa6161c: mov             x0, x1
    // 0xa61620: r0 = Throw()
    //     0xa61620: bl              #0xec04b8  ; ThrowStub
    // 0xa61624: brk             #0
    // 0xa61628: r0 = "Not enough bytes available."
    //     0xa61628: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6162c: ldr             x0, [x0, #0x8a8]
    // 0xa61630: r2 = false
    //     0xa61630: add             x2, NULL, #0x30  ; false
    // 0xa61634: r0 = RangeError()
    //     0xa61634: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61638: mov             x1, x0
    // 0xa6163c: r0 = "Not enough bytes available."
    //     0xa6163c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61640: ldr             x0, [x0, #0x8a8]
    // 0xa61644: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61644: stur            w0, [x1, #0x17]
    // 0xa61648: r0 = false
    //     0xa61648: add             x0, NULL, #0x30  ; false
    // 0xa6164c: StoreField: r1->field_b = r0
    //     0xa6164c: stur            w0, [x1, #0xb]
    // 0xa61650: mov             x0, x1
    // 0xa61654: r0 = Throw()
    //     0xa61654: bl              #0xec04b8  ; ThrowStub
    // 0xa61658: brk             #0
    // 0xa6165c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6165c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa61660: b               #0xa61114
    // 0xa61664: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61664: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa61668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61668: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6166c: b               #0xa6119c
    // 0xa61670: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61670: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd2530, size: 0x4dc
    // 0xbd2530: EnterFrame
    //     0xbd2530: stp             fp, lr, [SP, #-0x10]!
    //     0xbd2534: mov             fp, SP
    // 0xbd2538: AllocStack(0x28)
    //     0xbd2538: sub             SP, SP, #0x28
    // 0xbd253c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd253c: mov             x4, x2
    //     0xbd2540: stur            x2, [fp, #-8]
    //     0xbd2544: stur            x3, [fp, #-0x10]
    // 0xbd2548: CheckStackOverflow
    //     0xbd2548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd254c: cmp             SP, x16
    //     0xbd2550: b.ls            #0xbd29e4
    // 0xbd2554: mov             x0, x3
    // 0xbd2558: r2 = Null
    //     0xbd2558: mov             x2, NULL
    // 0xbd255c: r1 = Null
    //     0xbd255c: mov             x1, NULL
    // 0xbd2560: r4 = 60
    //     0xbd2560: movz            x4, #0x3c
    // 0xbd2564: branchIfSmi(r0, 0xbd2570)
    //     0xbd2564: tbz             w0, #0, #0xbd2570
    // 0xbd2568: r4 = LoadClassIdInstr(r0)
    //     0xbd2568: ldur            x4, [x0, #-1]
    //     0xbd256c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd2570: r17 = 5592
    //     0xbd2570: movz            x17, #0x15d8
    // 0xbd2574: cmp             x4, x17
    // 0xbd2578: b.eq            #0xbd2590
    // 0xbd257c: r8 = DoaSubCategory
    //     0xbd257c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b3b8] Type: DoaSubCategory
    //     0xbd2580: ldr             x8, [x8, #0x3b8]
    // 0xbd2584: r3 = Null
    //     0xbd2584: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3c0] Null
    //     0xbd2588: ldr             x3, [x3, #0x3c0]
    // 0xbd258c: r0 = DoaSubCategory()
    //     0xbd258c: bl              #0x6fb10c  ; IsType_DoaSubCategory_Stub
    // 0xbd2590: ldur            x0, [fp, #-8]
    // 0xbd2594: LoadField: r1 = r0->field_b
    //     0xbd2594: ldur            w1, [x0, #0xb]
    // 0xbd2598: DecompressPointer r1
    //     0xbd2598: add             x1, x1, HEAP, lsl #32
    // 0xbd259c: LoadField: r2 = r1->field_13
    //     0xbd259c: ldur            w2, [x1, #0x13]
    // 0xbd25a0: LoadField: r1 = r0->field_13
    //     0xbd25a0: ldur            x1, [x0, #0x13]
    // 0xbd25a4: r3 = LoadInt32Instr(r2)
    //     0xbd25a4: sbfx            x3, x2, #1, #0x1f
    // 0xbd25a8: sub             x2, x3, x1
    // 0xbd25ac: cmp             x2, #1
    // 0xbd25b0: b.ge            #0xbd25c0
    // 0xbd25b4: mov             x1, x0
    // 0xbd25b8: r2 = 1
    //     0xbd25b8: movz            x2, #0x1
    // 0xbd25bc: r0 = _increaseBufferSize()
    //     0xbd25bc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd25c0: ldur            x3, [fp, #-8]
    // 0xbd25c4: r2 = 7
    //     0xbd25c4: movz            x2, #0x7
    // 0xbd25c8: LoadField: r4 = r3->field_b
    //     0xbd25c8: ldur            w4, [x3, #0xb]
    // 0xbd25cc: DecompressPointer r4
    //     0xbd25cc: add             x4, x4, HEAP, lsl #32
    // 0xbd25d0: LoadField: r5 = r3->field_13
    //     0xbd25d0: ldur            x5, [x3, #0x13]
    // 0xbd25d4: add             x6, x5, #1
    // 0xbd25d8: StoreField: r3->field_13 = r6
    //     0xbd25d8: stur            x6, [x3, #0x13]
    // 0xbd25dc: LoadField: r0 = r4->field_13
    //     0xbd25dc: ldur            w0, [x4, #0x13]
    // 0xbd25e0: r7 = LoadInt32Instr(r0)
    //     0xbd25e0: sbfx            x7, x0, #1, #0x1f
    // 0xbd25e4: mov             x0, x7
    // 0xbd25e8: mov             x1, x5
    // 0xbd25ec: cmp             x1, x0
    // 0xbd25f0: b.hs            #0xbd29ec
    // 0xbd25f4: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd25f4: add             x0, x4, x5
    //     0xbd25f8: strb            w2, [x0, #0x17]
    // 0xbd25fc: sub             x0, x7, x6
    // 0xbd2600: cmp             x0, #1
    // 0xbd2604: b.ge            #0xbd2614
    // 0xbd2608: mov             x1, x3
    // 0xbd260c: r2 = 1
    //     0xbd260c: movz            x2, #0x1
    // 0xbd2610: r0 = _increaseBufferSize()
    //     0xbd2610: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2614: ldur            x2, [fp, #-8]
    // 0xbd2618: ldur            x3, [fp, #-0x10]
    // 0xbd261c: LoadField: r4 = r2->field_b
    //     0xbd261c: ldur            w4, [x2, #0xb]
    // 0xbd2620: DecompressPointer r4
    //     0xbd2620: add             x4, x4, HEAP, lsl #32
    // 0xbd2624: LoadField: r5 = r2->field_13
    //     0xbd2624: ldur            x5, [x2, #0x13]
    // 0xbd2628: add             x0, x5, #1
    // 0xbd262c: StoreField: r2->field_13 = r0
    //     0xbd262c: stur            x0, [x2, #0x13]
    // 0xbd2630: LoadField: r0 = r4->field_13
    //     0xbd2630: ldur            w0, [x4, #0x13]
    // 0xbd2634: r1 = LoadInt32Instr(r0)
    //     0xbd2634: sbfx            x1, x0, #1, #0x1f
    // 0xbd2638: mov             x0, x1
    // 0xbd263c: mov             x1, x5
    // 0xbd2640: cmp             x1, x0
    // 0xbd2644: b.hs            #0xbd29f0
    // 0xbd2648: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd2648: add             x0, x4, x5
    //     0xbd264c: strb            wzr, [x0, #0x17]
    // 0xbd2650: LoadField: r4 = r3->field_7
    //     0xbd2650: ldur            x4, [x3, #7]
    // 0xbd2654: r0 = BoxInt64Instr(r4)
    //     0xbd2654: sbfiz           x0, x4, #1, #0x1f
    //     0xbd2658: cmp             x4, x0, asr #1
    //     0xbd265c: b.eq            #0xbd2668
    //     0xbd2660: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd2664: stur            x4, [x0, #7]
    // 0xbd2668: r16 = <int>
    //     0xbd2668: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd266c: stp             x2, x16, [SP, #8]
    // 0xbd2670: str             x0, [SP]
    // 0xbd2674: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2674: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2678: r0 = write()
    //     0xbd2678: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd267c: ldur            x0, [fp, #-8]
    // 0xbd2680: LoadField: r1 = r0->field_b
    //     0xbd2680: ldur            w1, [x0, #0xb]
    // 0xbd2684: DecompressPointer r1
    //     0xbd2684: add             x1, x1, HEAP, lsl #32
    // 0xbd2688: LoadField: r2 = r1->field_13
    //     0xbd2688: ldur            w2, [x1, #0x13]
    // 0xbd268c: LoadField: r1 = r0->field_13
    //     0xbd268c: ldur            x1, [x0, #0x13]
    // 0xbd2690: r3 = LoadInt32Instr(r2)
    //     0xbd2690: sbfx            x3, x2, #1, #0x1f
    // 0xbd2694: sub             x2, x3, x1
    // 0xbd2698: cmp             x2, #1
    // 0xbd269c: b.ge            #0xbd26ac
    // 0xbd26a0: mov             x1, x0
    // 0xbd26a4: r2 = 1
    //     0xbd26a4: movz            x2, #0x1
    // 0xbd26a8: r0 = _increaseBufferSize()
    //     0xbd26a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd26ac: ldur            x2, [fp, #-8]
    // 0xbd26b0: ldur            x3, [fp, #-0x10]
    // 0xbd26b4: r4 = 1
    //     0xbd26b4: movz            x4, #0x1
    // 0xbd26b8: LoadField: r5 = r2->field_b
    //     0xbd26b8: ldur            w5, [x2, #0xb]
    // 0xbd26bc: DecompressPointer r5
    //     0xbd26bc: add             x5, x5, HEAP, lsl #32
    // 0xbd26c0: LoadField: r6 = r2->field_13
    //     0xbd26c0: ldur            x6, [x2, #0x13]
    // 0xbd26c4: add             x0, x6, #1
    // 0xbd26c8: StoreField: r2->field_13 = r0
    //     0xbd26c8: stur            x0, [x2, #0x13]
    // 0xbd26cc: LoadField: r0 = r5->field_13
    //     0xbd26cc: ldur            w0, [x5, #0x13]
    // 0xbd26d0: r1 = LoadInt32Instr(r0)
    //     0xbd26d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd26d4: mov             x0, x1
    // 0xbd26d8: mov             x1, x6
    // 0xbd26dc: cmp             x1, x0
    // 0xbd26e0: b.hs            #0xbd29f4
    // 0xbd26e4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd26e4: add             x0, x5, x6
    //     0xbd26e8: strb            w4, [x0, #0x17]
    // 0xbd26ec: LoadField: r0 = r3->field_f
    //     0xbd26ec: ldur            w0, [x3, #0xf]
    // 0xbd26f0: DecompressPointer r0
    //     0xbd26f0: add             x0, x0, HEAP, lsl #32
    // 0xbd26f4: r16 = <String>
    //     0xbd26f4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd26f8: stp             x2, x16, [SP, #8]
    // 0xbd26fc: str             x0, [SP]
    // 0xbd2700: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2700: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2704: r0 = write()
    //     0xbd2704: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2708: ldur            x0, [fp, #-8]
    // 0xbd270c: LoadField: r1 = r0->field_b
    //     0xbd270c: ldur            w1, [x0, #0xb]
    // 0xbd2710: DecompressPointer r1
    //     0xbd2710: add             x1, x1, HEAP, lsl #32
    // 0xbd2714: LoadField: r2 = r1->field_13
    //     0xbd2714: ldur            w2, [x1, #0x13]
    // 0xbd2718: LoadField: r1 = r0->field_13
    //     0xbd2718: ldur            x1, [x0, #0x13]
    // 0xbd271c: r3 = LoadInt32Instr(r2)
    //     0xbd271c: sbfx            x3, x2, #1, #0x1f
    // 0xbd2720: sub             x2, x3, x1
    // 0xbd2724: cmp             x2, #1
    // 0xbd2728: b.ge            #0xbd2738
    // 0xbd272c: mov             x1, x0
    // 0xbd2730: r2 = 1
    //     0xbd2730: movz            x2, #0x1
    // 0xbd2734: r0 = _increaseBufferSize()
    //     0xbd2734: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2738: ldur            x2, [fp, #-8]
    // 0xbd273c: ldur            x3, [fp, #-0x10]
    // 0xbd2740: r4 = 2
    //     0xbd2740: movz            x4, #0x2
    // 0xbd2744: LoadField: r5 = r2->field_b
    //     0xbd2744: ldur            w5, [x2, #0xb]
    // 0xbd2748: DecompressPointer r5
    //     0xbd2748: add             x5, x5, HEAP, lsl #32
    // 0xbd274c: LoadField: r6 = r2->field_13
    //     0xbd274c: ldur            x6, [x2, #0x13]
    // 0xbd2750: add             x0, x6, #1
    // 0xbd2754: StoreField: r2->field_13 = r0
    //     0xbd2754: stur            x0, [x2, #0x13]
    // 0xbd2758: LoadField: r0 = r5->field_13
    //     0xbd2758: ldur            w0, [x5, #0x13]
    // 0xbd275c: r1 = LoadInt32Instr(r0)
    //     0xbd275c: sbfx            x1, x0, #1, #0x1f
    // 0xbd2760: mov             x0, x1
    // 0xbd2764: mov             x1, x6
    // 0xbd2768: cmp             x1, x0
    // 0xbd276c: b.hs            #0xbd29f8
    // 0xbd2770: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2770: add             x0, x5, x6
    //     0xbd2774: strb            w4, [x0, #0x17]
    // 0xbd2778: LoadField: r4 = r3->field_13
    //     0xbd2778: ldur            x4, [x3, #0x13]
    // 0xbd277c: r0 = BoxInt64Instr(r4)
    //     0xbd277c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd2780: cmp             x4, x0, asr #1
    //     0xbd2784: b.eq            #0xbd2790
    //     0xbd2788: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd278c: stur            x4, [x0, #7]
    // 0xbd2790: r16 = <int>
    //     0xbd2790: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd2794: stp             x2, x16, [SP, #8]
    // 0xbd2798: str             x0, [SP]
    // 0xbd279c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd279c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd27a0: r0 = write()
    //     0xbd27a0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd27a4: ldur            x0, [fp, #-8]
    // 0xbd27a8: LoadField: r1 = r0->field_b
    //     0xbd27a8: ldur            w1, [x0, #0xb]
    // 0xbd27ac: DecompressPointer r1
    //     0xbd27ac: add             x1, x1, HEAP, lsl #32
    // 0xbd27b0: LoadField: r2 = r1->field_13
    //     0xbd27b0: ldur            w2, [x1, #0x13]
    // 0xbd27b4: LoadField: r1 = r0->field_13
    //     0xbd27b4: ldur            x1, [x0, #0x13]
    // 0xbd27b8: r3 = LoadInt32Instr(r2)
    //     0xbd27b8: sbfx            x3, x2, #1, #0x1f
    // 0xbd27bc: sub             x2, x3, x1
    // 0xbd27c0: cmp             x2, #1
    // 0xbd27c4: b.ge            #0xbd27d4
    // 0xbd27c8: mov             x1, x0
    // 0xbd27cc: r2 = 1
    //     0xbd27cc: movz            x2, #0x1
    // 0xbd27d0: r0 = _increaseBufferSize()
    //     0xbd27d0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd27d4: ldur            x2, [fp, #-8]
    // 0xbd27d8: ldur            x3, [fp, #-0x10]
    // 0xbd27dc: r4 = 3
    //     0xbd27dc: movz            x4, #0x3
    // 0xbd27e0: LoadField: r5 = r2->field_b
    //     0xbd27e0: ldur            w5, [x2, #0xb]
    // 0xbd27e4: DecompressPointer r5
    //     0xbd27e4: add             x5, x5, HEAP, lsl #32
    // 0xbd27e8: LoadField: r6 = r2->field_13
    //     0xbd27e8: ldur            x6, [x2, #0x13]
    // 0xbd27ec: add             x0, x6, #1
    // 0xbd27f0: StoreField: r2->field_13 = r0
    //     0xbd27f0: stur            x0, [x2, #0x13]
    // 0xbd27f4: LoadField: r0 = r5->field_13
    //     0xbd27f4: ldur            w0, [x5, #0x13]
    // 0xbd27f8: r1 = LoadInt32Instr(r0)
    //     0xbd27f8: sbfx            x1, x0, #1, #0x1f
    // 0xbd27fc: mov             x0, x1
    // 0xbd2800: mov             x1, x6
    // 0xbd2804: cmp             x1, x0
    // 0xbd2808: b.hs            #0xbd29fc
    // 0xbd280c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd280c: add             x0, x5, x6
    //     0xbd2810: strb            w4, [x0, #0x17]
    // 0xbd2814: LoadField: r0 = r3->field_1b
    //     0xbd2814: ldur            w0, [x3, #0x1b]
    // 0xbd2818: DecompressPointer r0
    //     0xbd2818: add             x0, x0, HEAP, lsl #32
    // 0xbd281c: r16 = <String?>
    //     0xbd281c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd2820: stp             x2, x16, [SP, #8]
    // 0xbd2824: str             x0, [SP]
    // 0xbd2828: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2828: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd282c: r0 = write()
    //     0xbd282c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2830: ldur            x0, [fp, #-8]
    // 0xbd2834: LoadField: r1 = r0->field_b
    //     0xbd2834: ldur            w1, [x0, #0xb]
    // 0xbd2838: DecompressPointer r1
    //     0xbd2838: add             x1, x1, HEAP, lsl #32
    // 0xbd283c: LoadField: r2 = r1->field_13
    //     0xbd283c: ldur            w2, [x1, #0x13]
    // 0xbd2840: LoadField: r1 = r0->field_13
    //     0xbd2840: ldur            x1, [x0, #0x13]
    // 0xbd2844: r3 = LoadInt32Instr(r2)
    //     0xbd2844: sbfx            x3, x2, #1, #0x1f
    // 0xbd2848: sub             x2, x3, x1
    // 0xbd284c: cmp             x2, #1
    // 0xbd2850: b.ge            #0xbd2860
    // 0xbd2854: mov             x1, x0
    // 0xbd2858: r2 = 1
    //     0xbd2858: movz            x2, #0x1
    // 0xbd285c: r0 = _increaseBufferSize()
    //     0xbd285c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2860: ldur            x2, [fp, #-8]
    // 0xbd2864: ldur            x3, [fp, #-0x10]
    // 0xbd2868: r4 = 4
    //     0xbd2868: movz            x4, #0x4
    // 0xbd286c: LoadField: r5 = r2->field_b
    //     0xbd286c: ldur            w5, [x2, #0xb]
    // 0xbd2870: DecompressPointer r5
    //     0xbd2870: add             x5, x5, HEAP, lsl #32
    // 0xbd2874: LoadField: r6 = r2->field_13
    //     0xbd2874: ldur            x6, [x2, #0x13]
    // 0xbd2878: add             x0, x6, #1
    // 0xbd287c: StoreField: r2->field_13 = r0
    //     0xbd287c: stur            x0, [x2, #0x13]
    // 0xbd2880: LoadField: r0 = r5->field_13
    //     0xbd2880: ldur            w0, [x5, #0x13]
    // 0xbd2884: r1 = LoadInt32Instr(r0)
    //     0xbd2884: sbfx            x1, x0, #1, #0x1f
    // 0xbd2888: mov             x0, x1
    // 0xbd288c: mov             x1, x6
    // 0xbd2890: cmp             x1, x0
    // 0xbd2894: b.hs            #0xbd2a00
    // 0xbd2898: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2898: add             x0, x5, x6
    //     0xbd289c: strb            w4, [x0, #0x17]
    // 0xbd28a0: LoadField: r0 = r3->field_1f
    //     0xbd28a0: ldur            w0, [x3, #0x1f]
    // 0xbd28a4: DecompressPointer r0
    //     0xbd28a4: add             x0, x0, HEAP, lsl #32
    // 0xbd28a8: r16 = <int?>
    //     0xbd28a8: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbd28ac: stp             x2, x16, [SP, #8]
    // 0xbd28b0: str             x0, [SP]
    // 0xbd28b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd28b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd28b8: r0 = write()
    //     0xbd28b8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd28bc: ldur            x0, [fp, #-8]
    // 0xbd28c0: LoadField: r1 = r0->field_b
    //     0xbd28c0: ldur            w1, [x0, #0xb]
    // 0xbd28c4: DecompressPointer r1
    //     0xbd28c4: add             x1, x1, HEAP, lsl #32
    // 0xbd28c8: LoadField: r2 = r1->field_13
    //     0xbd28c8: ldur            w2, [x1, #0x13]
    // 0xbd28cc: LoadField: r1 = r0->field_13
    //     0xbd28cc: ldur            x1, [x0, #0x13]
    // 0xbd28d0: r3 = LoadInt32Instr(r2)
    //     0xbd28d0: sbfx            x3, x2, #1, #0x1f
    // 0xbd28d4: sub             x2, x3, x1
    // 0xbd28d8: cmp             x2, #1
    // 0xbd28dc: b.ge            #0xbd28ec
    // 0xbd28e0: mov             x1, x0
    // 0xbd28e4: r2 = 1
    //     0xbd28e4: movz            x2, #0x1
    // 0xbd28e8: r0 = _increaseBufferSize()
    //     0xbd28e8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd28ec: ldur            x2, [fp, #-8]
    // 0xbd28f0: ldur            x3, [fp, #-0x10]
    // 0xbd28f4: r4 = 5
    //     0xbd28f4: movz            x4, #0x5
    // 0xbd28f8: LoadField: r5 = r2->field_b
    //     0xbd28f8: ldur            w5, [x2, #0xb]
    // 0xbd28fc: DecompressPointer r5
    //     0xbd28fc: add             x5, x5, HEAP, lsl #32
    // 0xbd2900: LoadField: r6 = r2->field_13
    //     0xbd2900: ldur            x6, [x2, #0x13]
    // 0xbd2904: add             x0, x6, #1
    // 0xbd2908: StoreField: r2->field_13 = r0
    //     0xbd2908: stur            x0, [x2, #0x13]
    // 0xbd290c: LoadField: r0 = r5->field_13
    //     0xbd290c: ldur            w0, [x5, #0x13]
    // 0xbd2910: r1 = LoadInt32Instr(r0)
    //     0xbd2910: sbfx            x1, x0, #1, #0x1f
    // 0xbd2914: mov             x0, x1
    // 0xbd2918: mov             x1, x6
    // 0xbd291c: cmp             x1, x0
    // 0xbd2920: b.hs            #0xbd2a04
    // 0xbd2924: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2924: add             x0, x5, x6
    //     0xbd2928: strb            w4, [x0, #0x17]
    // 0xbd292c: LoadField: r0 = r3->field_23
    //     0xbd292c: ldur            w0, [x3, #0x23]
    // 0xbd2930: DecompressPointer r0
    //     0xbd2930: add             x0, x0, HEAP, lsl #32
    // 0xbd2934: r16 = <String>
    //     0xbd2934: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd2938: stp             x2, x16, [SP, #8]
    // 0xbd293c: str             x0, [SP]
    // 0xbd2940: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2940: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2944: r0 = write()
    //     0xbd2944: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2948: ldur            x0, [fp, #-8]
    // 0xbd294c: LoadField: r1 = r0->field_b
    //     0xbd294c: ldur            w1, [x0, #0xb]
    // 0xbd2950: DecompressPointer r1
    //     0xbd2950: add             x1, x1, HEAP, lsl #32
    // 0xbd2954: LoadField: r2 = r1->field_13
    //     0xbd2954: ldur            w2, [x1, #0x13]
    // 0xbd2958: LoadField: r1 = r0->field_13
    //     0xbd2958: ldur            x1, [x0, #0x13]
    // 0xbd295c: r3 = LoadInt32Instr(r2)
    //     0xbd295c: sbfx            x3, x2, #1, #0x1f
    // 0xbd2960: sub             x2, x3, x1
    // 0xbd2964: cmp             x2, #1
    // 0xbd2968: b.ge            #0xbd2978
    // 0xbd296c: mov             x1, x0
    // 0xbd2970: r2 = 1
    //     0xbd2970: movz            x2, #0x1
    // 0xbd2974: r0 = _increaseBufferSize()
    //     0xbd2974: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2978: ldur            x2, [fp, #-8]
    // 0xbd297c: ldur            x3, [fp, #-0x10]
    // 0xbd2980: r4 = 6
    //     0xbd2980: movz            x4, #0x6
    // 0xbd2984: LoadField: r5 = r2->field_b
    //     0xbd2984: ldur            w5, [x2, #0xb]
    // 0xbd2988: DecompressPointer r5
    //     0xbd2988: add             x5, x5, HEAP, lsl #32
    // 0xbd298c: LoadField: r6 = r2->field_13
    //     0xbd298c: ldur            x6, [x2, #0x13]
    // 0xbd2990: add             x0, x6, #1
    // 0xbd2994: StoreField: r2->field_13 = r0
    //     0xbd2994: stur            x0, [x2, #0x13]
    // 0xbd2998: LoadField: r0 = r5->field_13
    //     0xbd2998: ldur            w0, [x5, #0x13]
    // 0xbd299c: r1 = LoadInt32Instr(r0)
    //     0xbd299c: sbfx            x1, x0, #1, #0x1f
    // 0xbd29a0: mov             x0, x1
    // 0xbd29a4: mov             x1, x6
    // 0xbd29a8: cmp             x1, x0
    // 0xbd29ac: b.hs            #0xbd2a08
    // 0xbd29b0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd29b0: add             x0, x5, x6
    //     0xbd29b4: strb            w4, [x0, #0x17]
    // 0xbd29b8: LoadField: r0 = r3->field_27
    //     0xbd29b8: ldur            w0, [x3, #0x27]
    // 0xbd29bc: DecompressPointer r0
    //     0xbd29bc: add             x0, x0, HEAP, lsl #32
    // 0xbd29c0: r16 = <String>
    //     0xbd29c0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd29c4: stp             x2, x16, [SP, #8]
    // 0xbd29c8: str             x0, [SP]
    // 0xbd29cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd29cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd29d0: r0 = write()
    //     0xbd29d0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd29d4: r0 = Null
    //     0xbd29d4: mov             x0, NULL
    // 0xbd29d8: LeaveFrame
    //     0xbd29d8: mov             SP, fp
    //     0xbd29dc: ldp             fp, lr, [SP], #0x10
    // 0xbd29e0: ret
    //     0xbd29e0: ret             
    // 0xbd29e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd29e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd29e8: b               #0xbd2554
    // 0xbd29ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd29ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd29f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd29f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd29f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd29f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd29f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd29f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd29fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd29fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2a00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2a00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2a04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2a04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2a08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2a08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0120, size: 0x24
    // 0xbf0120: r1 = 124
    //     0xbf0120: movz            x1, #0x7c
    // 0xbf0124: r16 = LoadInt32Instr(r1)
    //     0xbf0124: sbfx            x16, x1, #1, #0x1f
    // 0xbf0128: r17 = 11601
    //     0xbf0128: movz            x17, #0x2d51
    // 0xbf012c: mul             x0, x16, x17
    // 0xbf0130: umulh           x16, x16, x17
    // 0xbf0134: eor             x0, x0, x16
    // 0xbf0138: r0 = 0
    //     0xbf0138: eor             x0, x0, x0, lsr #32
    // 0xbf013c: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0140: ret
    //     0xbf0140: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76554, size: 0x9c
    // 0xd76554: EnterFrame
    //     0xd76554: stp             fp, lr, [SP, #-0x10]!
    //     0xd76558: mov             fp, SP
    // 0xd7655c: AllocStack(0x10)
    //     0xd7655c: sub             SP, SP, #0x10
    // 0xd76560: CheckStackOverflow
    //     0xd76560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76564: cmp             SP, x16
    //     0xd76568: b.ls            #0xd765e8
    // 0xd7656c: ldr             x0, [fp, #0x10]
    // 0xd76570: cmp             w0, NULL
    // 0xd76574: b.ne            #0xd76588
    // 0xd76578: r0 = false
    //     0xd76578: add             x0, NULL, #0x30  ; false
    // 0xd7657c: LeaveFrame
    //     0xd7657c: mov             SP, fp
    //     0xd76580: ldp             fp, lr, [SP], #0x10
    // 0xd76584: ret
    //     0xd76584: ret             
    // 0xd76588: ldr             x1, [fp, #0x18]
    // 0xd7658c: cmp             w1, w0
    // 0xd76590: b.ne            #0xd7659c
    // 0xd76594: r0 = true
    //     0xd76594: add             x0, NULL, #0x20  ; true
    // 0xd76598: b               #0xd765dc
    // 0xd7659c: r1 = 60
    //     0xd7659c: movz            x1, #0x3c
    // 0xd765a0: branchIfSmi(r0, 0xd765ac)
    //     0xd765a0: tbz             w0, #0, #0xd765ac
    // 0xd765a4: r1 = LoadClassIdInstr(r0)
    //     0xd765a4: ldur            x1, [x0, #-1]
    //     0xd765a8: ubfx            x1, x1, #0xc, #0x14
    // 0xd765ac: cmp             x1, #0x67e
    // 0xd765b0: b.ne            #0xd765d8
    // 0xd765b4: r16 = DoaSubCategoryAdapter
    //     0xd765b4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b3b0] Type: DoaSubCategoryAdapter
    //     0xd765b8: ldr             x16, [x16, #0x3b0]
    // 0xd765bc: r30 = DoaSubCategoryAdapter
    //     0xd765bc: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b3b0] Type: DoaSubCategoryAdapter
    //     0xd765c0: ldr             lr, [lr, #0x3b0]
    // 0xd765c4: stp             lr, x16, [SP]
    // 0xd765c8: r0 = ==()
    //     0xd765c8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd765cc: tbnz            w0, #4, #0xd765d8
    // 0xd765d0: r0 = true
    //     0xd765d0: add             x0, NULL, #0x20  ; true
    // 0xd765d4: b               #0xd765dc
    // 0xd765d8: r0 = false
    //     0xd765d8: add             x0, NULL, #0x30  ; false
    // 0xd765dc: LeaveFrame
    //     0xd765dc: mov             SP, fp
    //     0xd765e0: ldp             fp, lr, [SP], #0x10
    // 0xd765e4: ret
    //     0xd765e4: ret             
    // 0xd765e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd765e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd765ec: b               #0xd7656c
  }
}

// class id: 1663, size: 0x14, field offset: 0xc
class DoaCategoryAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa60c24, size: 0x4c8
    // 0xa60c24: EnterFrame
    //     0xa60c24: stp             fp, lr, [SP, #-0x10]!
    //     0xa60c28: mov             fp, SP
    // 0xa60c2c: AllocStack(0x60)
    //     0xa60c2c: sub             SP, SP, #0x60
    // 0xa60c30: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa60c30: stur            x2, [fp, #-0x20]
    // 0xa60c34: CheckStackOverflow
    //     0xa60c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa60c38: cmp             SP, x16
    //     0xa60c3c: b.ls            #0xa610d4
    // 0xa60c40: LoadField: r3 = r2->field_23
    //     0xa60c40: ldur            x3, [x2, #0x23]
    // 0xa60c44: add             x0, x3, #1
    // 0xa60c48: LoadField: r1 = r2->field_1b
    //     0xa60c48: ldur            x1, [x2, #0x1b]
    // 0xa60c4c: cmp             x0, x1
    // 0xa60c50: b.gt            #0xa61078
    // 0xa60c54: LoadField: r4 = r2->field_7
    //     0xa60c54: ldur            w4, [x2, #7]
    // 0xa60c58: DecompressPointer r4
    //     0xa60c58: add             x4, x4, HEAP, lsl #32
    // 0xa60c5c: stur            x4, [fp, #-0x18]
    // 0xa60c60: StoreField: r2->field_23 = r0
    //     0xa60c60: stur            x0, [x2, #0x23]
    // 0xa60c64: LoadField: r0 = r4->field_13
    //     0xa60c64: ldur            w0, [x4, #0x13]
    // 0xa60c68: r5 = LoadInt32Instr(r0)
    //     0xa60c68: sbfx            x5, x0, #1, #0x1f
    // 0xa60c6c: mov             x0, x5
    // 0xa60c70: mov             x1, x3
    // 0xa60c74: stur            x5, [fp, #-0x10]
    // 0xa60c78: cmp             x1, x0
    // 0xa60c7c: b.hs            #0xa610dc
    // 0xa60c80: LoadField: r0 = r4->field_7
    //     0xa60c80: ldur            x0, [x4, #7]
    // 0xa60c84: ldrb            w1, [x0, x3]
    // 0xa60c88: stur            x1, [fp, #-8]
    // 0xa60c8c: r16 = <int, dynamic>
    //     0xa60c8c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa60c90: ldr             x16, [x16, #0xac0]
    // 0xa60c94: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa60c98: stp             lr, x16, [SP]
    // 0xa60c9c: r0 = Map._fromLiteral()
    //     0xa60c9c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa60ca0: mov             x2, x0
    // 0xa60ca4: stur            x2, [fp, #-0x38]
    // 0xa60ca8: r6 = 0
    //     0xa60ca8: movz            x6, #0
    // 0xa60cac: ldur            x3, [fp, #-0x20]
    // 0xa60cb0: ldur            x4, [fp, #-0x18]
    // 0xa60cb4: ldur            x5, [fp, #-8]
    // 0xa60cb8: stur            x6, [fp, #-0x30]
    // 0xa60cbc: CheckStackOverflow
    //     0xa60cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa60cc0: cmp             SP, x16
    //     0xa60cc4: b.ls            #0xa610e0
    // 0xa60cc8: cmp             x6, x5
    // 0xa60ccc: b.ge            #0xa60d58
    // 0xa60cd0: LoadField: r7 = r3->field_23
    //     0xa60cd0: ldur            x7, [x3, #0x23]
    // 0xa60cd4: add             x0, x7, #1
    // 0xa60cd8: LoadField: r1 = r3->field_1b
    //     0xa60cd8: ldur            x1, [x3, #0x1b]
    // 0xa60cdc: cmp             x0, x1
    // 0xa60ce0: b.gt            #0xa610a0
    // 0xa60ce4: StoreField: r3->field_23 = r0
    //     0xa60ce4: stur            x0, [x3, #0x23]
    // 0xa60ce8: ldur            x0, [fp, #-0x10]
    // 0xa60cec: mov             x1, x7
    // 0xa60cf0: cmp             x1, x0
    // 0xa60cf4: b.hs            #0xa610e8
    // 0xa60cf8: LoadField: r0 = r4->field_7
    //     0xa60cf8: ldur            x0, [x4, #7]
    // 0xa60cfc: ldrb            w8, [x0, x7]
    // 0xa60d00: mov             x1, x3
    // 0xa60d04: stur            x8, [fp, #-0x28]
    // 0xa60d08: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa60d08: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa60d0c: r0 = read()
    //     0xa60d0c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa60d10: mov             x1, x0
    // 0xa60d14: ldur            x0, [fp, #-0x28]
    // 0xa60d18: lsl             x2, x0, #1
    // 0xa60d1c: r16 = LoadInt32Instr(r2)
    //     0xa60d1c: sbfx            x16, x2, #1, #0x1f
    // 0xa60d20: r17 = 11601
    //     0xa60d20: movz            x17, #0x2d51
    // 0xa60d24: mul             x0, x16, x17
    // 0xa60d28: umulh           x16, x16, x17
    // 0xa60d2c: eor             x0, x0, x16
    // 0xa60d30: r0 = 0
    //     0xa60d30: eor             x0, x0, x0, lsr #32
    // 0xa60d34: ubfiz           x0, x0, #1, #0x1e
    // 0xa60d38: r5 = LoadInt32Instr(r0)
    //     0xa60d38: sbfx            x5, x0, #1, #0x1f
    // 0xa60d3c: mov             x3, x1
    // 0xa60d40: ldur            x1, [fp, #-0x38]
    // 0xa60d44: r0 = _set()
    //     0xa60d44: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa60d48: ldur            x0, [fp, #-0x30]
    // 0xa60d4c: add             x6, x0, #1
    // 0xa60d50: ldur            x2, [fp, #-0x38]
    // 0xa60d54: b               #0xa60cac
    // 0xa60d58: mov             x0, x2
    // 0xa60d5c: mov             x1, x0
    // 0xa60d60: r2 = 0
    //     0xa60d60: movz            x2, #0
    // 0xa60d64: r0 = _getValueOrData()
    //     0xa60d64: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60d68: ldur            x3, [fp, #-0x38]
    // 0xa60d6c: LoadField: r1 = r3->field_f
    //     0xa60d6c: ldur            w1, [x3, #0xf]
    // 0xa60d70: DecompressPointer r1
    //     0xa60d70: add             x1, x1, HEAP, lsl #32
    // 0xa60d74: cmp             w1, w0
    // 0xa60d78: b.ne            #0xa60d84
    // 0xa60d7c: r4 = Null
    //     0xa60d7c: mov             x4, NULL
    // 0xa60d80: b               #0xa60d88
    // 0xa60d84: mov             x4, x0
    // 0xa60d88: mov             x0, x4
    // 0xa60d8c: stur            x4, [fp, #-0x18]
    // 0xa60d90: r2 = Null
    //     0xa60d90: mov             x2, NULL
    // 0xa60d94: r1 = Null
    //     0xa60d94: mov             x1, NULL
    // 0xa60d98: branchIfSmi(r0, 0xa60dc0)
    //     0xa60d98: tbz             w0, #0, #0xa60dc0
    // 0xa60d9c: r4 = LoadClassIdInstr(r0)
    //     0xa60d9c: ldur            x4, [x0, #-1]
    //     0xa60da0: ubfx            x4, x4, #0xc, #0x14
    // 0xa60da4: sub             x4, x4, #0x3c
    // 0xa60da8: cmp             x4, #1
    // 0xa60dac: b.ls            #0xa60dc0
    // 0xa60db0: r8 = int
    //     0xa60db0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60db4: r3 = Null
    //     0xa60db4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21578] Null
    //     0xa60db8: ldr             x3, [x3, #0x578]
    // 0xa60dbc: r0 = int()
    //     0xa60dbc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60dc0: ldur            x1, [fp, #-0x38]
    // 0xa60dc4: r2 = 2
    //     0xa60dc4: movz            x2, #0x2
    // 0xa60dc8: r0 = _getValueOrData()
    //     0xa60dc8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60dcc: ldur            x3, [fp, #-0x38]
    // 0xa60dd0: LoadField: r1 = r3->field_f
    //     0xa60dd0: ldur            w1, [x3, #0xf]
    // 0xa60dd4: DecompressPointer r1
    //     0xa60dd4: add             x1, x1, HEAP, lsl #32
    // 0xa60dd8: cmp             w1, w0
    // 0xa60ddc: b.ne            #0xa60de8
    // 0xa60de0: r4 = Null
    //     0xa60de0: mov             x4, NULL
    // 0xa60de4: b               #0xa60dec
    // 0xa60de8: mov             x4, x0
    // 0xa60dec: mov             x0, x4
    // 0xa60df0: stur            x4, [fp, #-0x20]
    // 0xa60df4: r2 = Null
    //     0xa60df4: mov             x2, NULL
    // 0xa60df8: r1 = Null
    //     0xa60df8: mov             x1, NULL
    // 0xa60dfc: r4 = 60
    //     0xa60dfc: movz            x4, #0x3c
    // 0xa60e00: branchIfSmi(r0, 0xa60e0c)
    //     0xa60e00: tbz             w0, #0, #0xa60e0c
    // 0xa60e04: r4 = LoadClassIdInstr(r0)
    //     0xa60e04: ldur            x4, [x0, #-1]
    //     0xa60e08: ubfx            x4, x4, #0xc, #0x14
    // 0xa60e0c: sub             x4, x4, #0x5e
    // 0xa60e10: cmp             x4, #1
    // 0xa60e14: b.ls            #0xa60e28
    // 0xa60e18: r8 = String
    //     0xa60e18: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60e1c: r3 = Null
    //     0xa60e1c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21588] Null
    //     0xa60e20: ldr             x3, [x3, #0x588]
    // 0xa60e24: r0 = String()
    //     0xa60e24: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60e28: ldur            x1, [fp, #-0x38]
    // 0xa60e2c: r2 = 6
    //     0xa60e2c: movz            x2, #0x6
    // 0xa60e30: r0 = _getValueOrData()
    //     0xa60e30: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60e34: ldur            x3, [fp, #-0x38]
    // 0xa60e38: LoadField: r1 = r3->field_f
    //     0xa60e38: ldur            w1, [x3, #0xf]
    // 0xa60e3c: DecompressPointer r1
    //     0xa60e3c: add             x1, x1, HEAP, lsl #32
    // 0xa60e40: cmp             w1, w0
    // 0xa60e44: b.ne            #0xa60e50
    // 0xa60e48: r4 = Null
    //     0xa60e48: mov             x4, NULL
    // 0xa60e4c: b               #0xa60e54
    // 0xa60e50: mov             x4, x0
    // 0xa60e54: mov             x0, x4
    // 0xa60e58: stur            x4, [fp, #-0x40]
    // 0xa60e5c: r2 = Null
    //     0xa60e5c: mov             x2, NULL
    // 0xa60e60: r1 = Null
    //     0xa60e60: mov             x1, NULL
    // 0xa60e64: r4 = 60
    //     0xa60e64: movz            x4, #0x3c
    // 0xa60e68: branchIfSmi(r0, 0xa60e74)
    //     0xa60e68: tbz             w0, #0, #0xa60e74
    // 0xa60e6c: r4 = LoadClassIdInstr(r0)
    //     0xa60e6c: ldur            x4, [x0, #-1]
    //     0xa60e70: ubfx            x4, x4, #0xc, #0x14
    // 0xa60e74: sub             x4, x4, #0x5e
    // 0xa60e78: cmp             x4, #1
    // 0xa60e7c: b.ls            #0xa60e90
    // 0xa60e80: r8 = String
    //     0xa60e80: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60e84: r3 = Null
    //     0xa60e84: add             x3, PP, #0x21, lsl #12  ; [pp+0x21598] Null
    //     0xa60e88: ldr             x3, [x3, #0x598]
    // 0xa60e8c: r0 = String()
    //     0xa60e8c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60e90: ldur            x1, [fp, #-0x38]
    // 0xa60e94: r2 = 4
    //     0xa60e94: movz            x2, #0x4
    // 0xa60e98: r0 = _getValueOrData()
    //     0xa60e98: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60e9c: ldur            x3, [fp, #-0x38]
    // 0xa60ea0: LoadField: r1 = r3->field_f
    //     0xa60ea0: ldur            w1, [x3, #0xf]
    // 0xa60ea4: DecompressPointer r1
    //     0xa60ea4: add             x1, x1, HEAP, lsl #32
    // 0xa60ea8: cmp             w1, w0
    // 0xa60eac: b.ne            #0xa60eb8
    // 0xa60eb0: r4 = Null
    //     0xa60eb0: mov             x4, NULL
    // 0xa60eb4: b               #0xa60ebc
    // 0xa60eb8: mov             x4, x0
    // 0xa60ebc: mov             x0, x4
    // 0xa60ec0: stur            x4, [fp, #-0x48]
    // 0xa60ec4: r2 = Null
    //     0xa60ec4: mov             x2, NULL
    // 0xa60ec8: r1 = Null
    //     0xa60ec8: mov             x1, NULL
    // 0xa60ecc: branchIfSmi(r0, 0xa60ef4)
    //     0xa60ecc: tbz             w0, #0, #0xa60ef4
    // 0xa60ed0: r4 = LoadClassIdInstr(r0)
    //     0xa60ed0: ldur            x4, [x0, #-1]
    //     0xa60ed4: ubfx            x4, x4, #0xc, #0x14
    // 0xa60ed8: sub             x4, x4, #0x3c
    // 0xa60edc: cmp             x4, #1
    // 0xa60ee0: b.ls            #0xa60ef4
    // 0xa60ee4: r8 = int
    //     0xa60ee4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60ee8: r3 = Null
    //     0xa60ee8: add             x3, PP, #0x21, lsl #12  ; [pp+0x215a8] Null
    //     0xa60eec: ldr             x3, [x3, #0x5a8]
    // 0xa60ef0: r0 = int()
    //     0xa60ef0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60ef4: ldur            x1, [fp, #-0x38]
    // 0xa60ef8: r2 = 8
    //     0xa60ef8: movz            x2, #0x8
    // 0xa60efc: r0 = _getValueOrData()
    //     0xa60efc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60f00: ldur            x3, [fp, #-0x38]
    // 0xa60f04: LoadField: r1 = r3->field_f
    //     0xa60f04: ldur            w1, [x3, #0xf]
    // 0xa60f08: DecompressPointer r1
    //     0xa60f08: add             x1, x1, HEAP, lsl #32
    // 0xa60f0c: cmp             w1, w0
    // 0xa60f10: b.ne            #0xa60f1c
    // 0xa60f14: r4 = Null
    //     0xa60f14: mov             x4, NULL
    // 0xa60f18: b               #0xa60f20
    // 0xa60f1c: mov             x4, x0
    // 0xa60f20: mov             x0, x4
    // 0xa60f24: stur            x4, [fp, #-0x50]
    // 0xa60f28: r2 = Null
    //     0xa60f28: mov             x2, NULL
    // 0xa60f2c: r1 = Null
    //     0xa60f2c: mov             x1, NULL
    // 0xa60f30: branchIfSmi(r0, 0xa60f58)
    //     0xa60f30: tbz             w0, #0, #0xa60f58
    // 0xa60f34: r4 = LoadClassIdInstr(r0)
    //     0xa60f34: ldur            x4, [x0, #-1]
    //     0xa60f38: ubfx            x4, x4, #0xc, #0x14
    // 0xa60f3c: sub             x4, x4, #0x3c
    // 0xa60f40: cmp             x4, #1
    // 0xa60f44: b.ls            #0xa60f58
    // 0xa60f48: r8 = int?
    //     0xa60f48: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xa60f4c: r3 = Null
    //     0xa60f4c: add             x3, PP, #0x21, lsl #12  ; [pp+0x215b8] Null
    //     0xa60f50: ldr             x3, [x3, #0x5b8]
    // 0xa60f54: r0 = int?()
    //     0xa60f54: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xa60f58: ldur            x1, [fp, #-0x38]
    // 0xa60f5c: r2 = 10
    //     0xa60f5c: movz            x2, #0xa
    // 0xa60f60: r0 = _getValueOrData()
    //     0xa60f60: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60f64: mov             x1, x0
    // 0xa60f68: ldur            x0, [fp, #-0x38]
    // 0xa60f6c: LoadField: r2 = r0->field_f
    //     0xa60f6c: ldur            w2, [x0, #0xf]
    // 0xa60f70: DecompressPointer r2
    //     0xa60f70: add             x2, x2, HEAP, lsl #32
    // 0xa60f74: cmp             w2, w1
    // 0xa60f78: b.eq            #0xa60f84
    // 0xa60f7c: cmp             w1, NULL
    // 0xa60f80: b.ne            #0xa60f8c
    // 0xa60f84: r5 = ""
    //     0xa60f84: ldr             x5, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa60f88: b               #0xa60ffc
    // 0xa60f8c: mov             x1, x0
    // 0xa60f90: r2 = 10
    //     0xa60f90: movz            x2, #0xa
    // 0xa60f94: r0 = _getValueOrData()
    //     0xa60f94: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60f98: mov             x1, x0
    // 0xa60f9c: ldur            x0, [fp, #-0x38]
    // 0xa60fa0: LoadField: r2 = r0->field_f
    //     0xa60fa0: ldur            w2, [x0, #0xf]
    // 0xa60fa4: DecompressPointer r2
    //     0xa60fa4: add             x2, x2, HEAP, lsl #32
    // 0xa60fa8: cmp             w2, w1
    // 0xa60fac: b.ne            #0xa60fb8
    // 0xa60fb0: r3 = Null
    //     0xa60fb0: mov             x3, NULL
    // 0xa60fb4: b               #0xa60fbc
    // 0xa60fb8: mov             x3, x1
    // 0xa60fbc: mov             x0, x3
    // 0xa60fc0: stur            x3, [fp, #-0x38]
    // 0xa60fc4: r2 = Null
    //     0xa60fc4: mov             x2, NULL
    // 0xa60fc8: r1 = Null
    //     0xa60fc8: mov             x1, NULL
    // 0xa60fcc: r4 = 60
    //     0xa60fcc: movz            x4, #0x3c
    // 0xa60fd0: branchIfSmi(r0, 0xa60fdc)
    //     0xa60fd0: tbz             w0, #0, #0xa60fdc
    // 0xa60fd4: r4 = LoadClassIdInstr(r0)
    //     0xa60fd4: ldur            x4, [x0, #-1]
    //     0xa60fd8: ubfx            x4, x4, #0xc, #0x14
    // 0xa60fdc: sub             x4, x4, #0x5e
    // 0xa60fe0: cmp             x4, #1
    // 0xa60fe4: b.ls            #0xa60ff8
    // 0xa60fe8: r8 = String
    //     0xa60fe8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60fec: r3 = Null
    //     0xa60fec: add             x3, PP, #0x21, lsl #12  ; [pp+0x215c8] Null
    //     0xa60ff0: ldr             x3, [x3, #0x5c8]
    // 0xa60ff4: r0 = String()
    //     0xa60ff4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60ff8: ldur            x5, [fp, #-0x38]
    // 0xa60ffc: ldur            x4, [fp, #-0x18]
    // 0xa61000: ldur            x3, [fp, #-0x20]
    // 0xa61004: ldur            x2, [fp, #-0x40]
    // 0xa61008: ldur            x1, [fp, #-0x48]
    // 0xa6100c: ldur            x0, [fp, #-0x50]
    // 0xa61010: stur            x5, [fp, #-0x38]
    // 0xa61014: r6 = LoadInt32Instr(r4)
    //     0xa61014: sbfx            x6, x4, #1, #0x1f
    //     0xa61018: tbz             w4, #0, #0xa61020
    //     0xa6101c: ldur            x6, [x4, #7]
    // 0xa61020: stur            x6, [fp, #-8]
    // 0xa61024: r0 = DoaCategory()
    //     0xa61024: bl              #0xa610ec  ; AllocateDoaCategoryStub -> DoaCategory (size=0x28)
    // 0xa61028: mov             x1, x0
    // 0xa6102c: ldur            x0, [fp, #-8]
    // 0xa61030: StoreField: r1->field_7 = r0
    //     0xa61030: stur            x0, [x1, #7]
    // 0xa61034: ldur            x0, [fp, #-0x20]
    // 0xa61038: StoreField: r1->field_f = r0
    //     0xa61038: stur            w0, [x1, #0xf]
    // 0xa6103c: ldur            x0, [fp, #-0x40]
    // 0xa61040: StoreField: r1->field_1b = r0
    //     0xa61040: stur            w0, [x1, #0x1b]
    // 0xa61044: ldur            x0, [fp, #-0x48]
    // 0xa61048: r2 = LoadInt32Instr(r0)
    //     0xa61048: sbfx            x2, x0, #1, #0x1f
    //     0xa6104c: tbz             w0, #0, #0xa61054
    //     0xa61050: ldur            x2, [x0, #7]
    // 0xa61054: StoreField: r1->field_13 = r2
    //     0xa61054: stur            x2, [x1, #0x13]
    // 0xa61058: ldur            x0, [fp, #-0x50]
    // 0xa6105c: StoreField: r1->field_1f = r0
    //     0xa6105c: stur            w0, [x1, #0x1f]
    // 0xa61060: ldur            x0, [fp, #-0x38]
    // 0xa61064: StoreField: r1->field_23 = r0
    //     0xa61064: stur            w0, [x1, #0x23]
    // 0xa61068: mov             x0, x1
    // 0xa6106c: LeaveFrame
    //     0xa6106c: mov             SP, fp
    //     0xa61070: ldp             fp, lr, [SP], #0x10
    // 0xa61074: ret
    //     0xa61074: ret             
    // 0xa61078: r0 = RangeError()
    //     0xa61078: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa6107c: mov             x1, x0
    // 0xa61080: r0 = "Not enough bytes available."
    //     0xa61080: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61084: ldr             x0, [x0, #0x8a8]
    // 0xa61088: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61088: stur            w0, [x1, #0x17]
    // 0xa6108c: r2 = false
    //     0xa6108c: add             x2, NULL, #0x30  ; false
    // 0xa61090: StoreField: r1->field_b = r2
    //     0xa61090: stur            w2, [x1, #0xb]
    // 0xa61094: mov             x0, x1
    // 0xa61098: r0 = Throw()
    //     0xa61098: bl              #0xec04b8  ; ThrowStub
    // 0xa6109c: brk             #0
    // 0xa610a0: r0 = "Not enough bytes available."
    //     0xa610a0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa610a4: ldr             x0, [x0, #0x8a8]
    // 0xa610a8: r2 = false
    //     0xa610a8: add             x2, NULL, #0x30  ; false
    // 0xa610ac: r0 = RangeError()
    //     0xa610ac: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa610b0: mov             x1, x0
    // 0xa610b4: r0 = "Not enough bytes available."
    //     0xa610b4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa610b8: ldr             x0, [x0, #0x8a8]
    // 0xa610bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa610bc: stur            w0, [x1, #0x17]
    // 0xa610c0: r0 = false
    //     0xa610c0: add             x0, NULL, #0x30  ; false
    // 0xa610c4: StoreField: r1->field_b = r0
    //     0xa610c4: stur            w0, [x1, #0xb]
    // 0xa610c8: mov             x0, x1
    // 0xa610cc: r0 = Throw()
    //     0xa610cc: bl              #0xec04b8  ; ThrowStub
    // 0xa610d0: brk             #0
    // 0xa610d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa610d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa610d8: b               #0xa60c40
    // 0xa610dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa610dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa610e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa610e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa610e4: b               #0xa60cc8
    // 0xa610e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa610e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd20e4, size: 0x44c
    // 0xbd20e4: EnterFrame
    //     0xbd20e4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd20e8: mov             fp, SP
    // 0xbd20ec: AllocStack(0x28)
    //     0xbd20ec: sub             SP, SP, #0x28
    // 0xbd20f0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd20f0: mov             x4, x2
    //     0xbd20f4: stur            x2, [fp, #-8]
    //     0xbd20f8: stur            x3, [fp, #-0x10]
    // 0xbd20fc: CheckStackOverflow
    //     0xbd20fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd2100: cmp             SP, x16
    //     0xbd2104: b.ls            #0xbd250c
    // 0xbd2108: mov             x0, x3
    // 0xbd210c: r2 = Null
    //     0xbd210c: mov             x2, NULL
    // 0xbd2110: r1 = Null
    //     0xbd2110: mov             x1, NULL
    // 0xbd2114: r4 = 60
    //     0xbd2114: movz            x4, #0x3c
    // 0xbd2118: branchIfSmi(r0, 0xbd2124)
    //     0xbd2118: tbz             w0, #0, #0xbd2124
    // 0xbd211c: r4 = LoadClassIdInstr(r0)
    //     0xbd211c: ldur            x4, [x0, #-1]
    //     0xbd2120: ubfx            x4, x4, #0xc, #0x14
    // 0xbd2124: r17 = 5593
    //     0xbd2124: movz            x17, #0x15d9
    // 0xbd2128: cmp             x4, x17
    // 0xbd212c: b.eq            #0xbd2144
    // 0xbd2130: r8 = DoaCategory
    //     0xbd2130: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b3f8] Type: DoaCategory
    //     0xbd2134: ldr             x8, [x8, #0x3f8]
    // 0xbd2138: r3 = Null
    //     0xbd2138: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b400] Null
    //     0xbd213c: ldr             x3, [x3, #0x400]
    // 0xbd2140: r0 = DoaCategory()
    //     0xbd2140: bl              #0x6fb3ac  ; IsType_DoaCategory_Stub
    // 0xbd2144: ldur            x0, [fp, #-8]
    // 0xbd2148: LoadField: r1 = r0->field_b
    //     0xbd2148: ldur            w1, [x0, #0xb]
    // 0xbd214c: DecompressPointer r1
    //     0xbd214c: add             x1, x1, HEAP, lsl #32
    // 0xbd2150: LoadField: r2 = r1->field_13
    //     0xbd2150: ldur            w2, [x1, #0x13]
    // 0xbd2154: LoadField: r1 = r0->field_13
    //     0xbd2154: ldur            x1, [x0, #0x13]
    // 0xbd2158: r3 = LoadInt32Instr(r2)
    //     0xbd2158: sbfx            x3, x2, #1, #0x1f
    // 0xbd215c: sub             x2, x3, x1
    // 0xbd2160: cmp             x2, #1
    // 0xbd2164: b.ge            #0xbd2174
    // 0xbd2168: mov             x1, x0
    // 0xbd216c: r2 = 1
    //     0xbd216c: movz            x2, #0x1
    // 0xbd2170: r0 = _increaseBufferSize()
    //     0xbd2170: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2174: ldur            x3, [fp, #-8]
    // 0xbd2178: r2 = 6
    //     0xbd2178: movz            x2, #0x6
    // 0xbd217c: LoadField: r4 = r3->field_b
    //     0xbd217c: ldur            w4, [x3, #0xb]
    // 0xbd2180: DecompressPointer r4
    //     0xbd2180: add             x4, x4, HEAP, lsl #32
    // 0xbd2184: LoadField: r5 = r3->field_13
    //     0xbd2184: ldur            x5, [x3, #0x13]
    // 0xbd2188: add             x6, x5, #1
    // 0xbd218c: StoreField: r3->field_13 = r6
    //     0xbd218c: stur            x6, [x3, #0x13]
    // 0xbd2190: LoadField: r0 = r4->field_13
    //     0xbd2190: ldur            w0, [x4, #0x13]
    // 0xbd2194: r7 = LoadInt32Instr(r0)
    //     0xbd2194: sbfx            x7, x0, #1, #0x1f
    // 0xbd2198: mov             x0, x7
    // 0xbd219c: mov             x1, x5
    // 0xbd21a0: cmp             x1, x0
    // 0xbd21a4: b.hs            #0xbd2514
    // 0xbd21a8: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd21a8: add             x0, x4, x5
    //     0xbd21ac: strb            w2, [x0, #0x17]
    // 0xbd21b0: sub             x0, x7, x6
    // 0xbd21b4: cmp             x0, #1
    // 0xbd21b8: b.ge            #0xbd21c8
    // 0xbd21bc: mov             x1, x3
    // 0xbd21c0: r2 = 1
    //     0xbd21c0: movz            x2, #0x1
    // 0xbd21c4: r0 = _increaseBufferSize()
    //     0xbd21c4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd21c8: ldur            x2, [fp, #-8]
    // 0xbd21cc: ldur            x3, [fp, #-0x10]
    // 0xbd21d0: LoadField: r4 = r2->field_b
    //     0xbd21d0: ldur            w4, [x2, #0xb]
    // 0xbd21d4: DecompressPointer r4
    //     0xbd21d4: add             x4, x4, HEAP, lsl #32
    // 0xbd21d8: LoadField: r5 = r2->field_13
    //     0xbd21d8: ldur            x5, [x2, #0x13]
    // 0xbd21dc: add             x0, x5, #1
    // 0xbd21e0: StoreField: r2->field_13 = r0
    //     0xbd21e0: stur            x0, [x2, #0x13]
    // 0xbd21e4: LoadField: r0 = r4->field_13
    //     0xbd21e4: ldur            w0, [x4, #0x13]
    // 0xbd21e8: r1 = LoadInt32Instr(r0)
    //     0xbd21e8: sbfx            x1, x0, #1, #0x1f
    // 0xbd21ec: mov             x0, x1
    // 0xbd21f0: mov             x1, x5
    // 0xbd21f4: cmp             x1, x0
    // 0xbd21f8: b.hs            #0xbd2518
    // 0xbd21fc: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd21fc: add             x0, x4, x5
    //     0xbd2200: strb            wzr, [x0, #0x17]
    // 0xbd2204: LoadField: r4 = r3->field_7
    //     0xbd2204: ldur            x4, [x3, #7]
    // 0xbd2208: r0 = BoxInt64Instr(r4)
    //     0xbd2208: sbfiz           x0, x4, #1, #0x1f
    //     0xbd220c: cmp             x4, x0, asr #1
    //     0xbd2210: b.eq            #0xbd221c
    //     0xbd2214: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd2218: stur            x4, [x0, #7]
    // 0xbd221c: r16 = <int>
    //     0xbd221c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd2220: stp             x2, x16, [SP, #8]
    // 0xbd2224: str             x0, [SP]
    // 0xbd2228: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2228: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd222c: r0 = write()
    //     0xbd222c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2230: ldur            x0, [fp, #-8]
    // 0xbd2234: LoadField: r1 = r0->field_b
    //     0xbd2234: ldur            w1, [x0, #0xb]
    // 0xbd2238: DecompressPointer r1
    //     0xbd2238: add             x1, x1, HEAP, lsl #32
    // 0xbd223c: LoadField: r2 = r1->field_13
    //     0xbd223c: ldur            w2, [x1, #0x13]
    // 0xbd2240: LoadField: r1 = r0->field_13
    //     0xbd2240: ldur            x1, [x0, #0x13]
    // 0xbd2244: r3 = LoadInt32Instr(r2)
    //     0xbd2244: sbfx            x3, x2, #1, #0x1f
    // 0xbd2248: sub             x2, x3, x1
    // 0xbd224c: cmp             x2, #1
    // 0xbd2250: b.ge            #0xbd2260
    // 0xbd2254: mov             x1, x0
    // 0xbd2258: r2 = 1
    //     0xbd2258: movz            x2, #0x1
    // 0xbd225c: r0 = _increaseBufferSize()
    //     0xbd225c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2260: ldur            x2, [fp, #-8]
    // 0xbd2264: ldur            x3, [fp, #-0x10]
    // 0xbd2268: r4 = 1
    //     0xbd2268: movz            x4, #0x1
    // 0xbd226c: LoadField: r5 = r2->field_b
    //     0xbd226c: ldur            w5, [x2, #0xb]
    // 0xbd2270: DecompressPointer r5
    //     0xbd2270: add             x5, x5, HEAP, lsl #32
    // 0xbd2274: LoadField: r6 = r2->field_13
    //     0xbd2274: ldur            x6, [x2, #0x13]
    // 0xbd2278: add             x0, x6, #1
    // 0xbd227c: StoreField: r2->field_13 = r0
    //     0xbd227c: stur            x0, [x2, #0x13]
    // 0xbd2280: LoadField: r0 = r5->field_13
    //     0xbd2280: ldur            w0, [x5, #0x13]
    // 0xbd2284: r1 = LoadInt32Instr(r0)
    //     0xbd2284: sbfx            x1, x0, #1, #0x1f
    // 0xbd2288: mov             x0, x1
    // 0xbd228c: mov             x1, x6
    // 0xbd2290: cmp             x1, x0
    // 0xbd2294: b.hs            #0xbd251c
    // 0xbd2298: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2298: add             x0, x5, x6
    //     0xbd229c: strb            w4, [x0, #0x17]
    // 0xbd22a0: LoadField: r0 = r3->field_f
    //     0xbd22a0: ldur            w0, [x3, #0xf]
    // 0xbd22a4: DecompressPointer r0
    //     0xbd22a4: add             x0, x0, HEAP, lsl #32
    // 0xbd22a8: r16 = <String>
    //     0xbd22a8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd22ac: stp             x2, x16, [SP, #8]
    // 0xbd22b0: str             x0, [SP]
    // 0xbd22b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd22b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd22b8: r0 = write()
    //     0xbd22b8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd22bc: ldur            x0, [fp, #-8]
    // 0xbd22c0: LoadField: r1 = r0->field_b
    //     0xbd22c0: ldur            w1, [x0, #0xb]
    // 0xbd22c4: DecompressPointer r1
    //     0xbd22c4: add             x1, x1, HEAP, lsl #32
    // 0xbd22c8: LoadField: r2 = r1->field_13
    //     0xbd22c8: ldur            w2, [x1, #0x13]
    // 0xbd22cc: LoadField: r1 = r0->field_13
    //     0xbd22cc: ldur            x1, [x0, #0x13]
    // 0xbd22d0: r3 = LoadInt32Instr(r2)
    //     0xbd22d0: sbfx            x3, x2, #1, #0x1f
    // 0xbd22d4: sub             x2, x3, x1
    // 0xbd22d8: cmp             x2, #1
    // 0xbd22dc: b.ge            #0xbd22ec
    // 0xbd22e0: mov             x1, x0
    // 0xbd22e4: r2 = 1
    //     0xbd22e4: movz            x2, #0x1
    // 0xbd22e8: r0 = _increaseBufferSize()
    //     0xbd22e8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd22ec: ldur            x2, [fp, #-8]
    // 0xbd22f0: ldur            x3, [fp, #-0x10]
    // 0xbd22f4: r4 = 2
    //     0xbd22f4: movz            x4, #0x2
    // 0xbd22f8: LoadField: r5 = r2->field_b
    //     0xbd22f8: ldur            w5, [x2, #0xb]
    // 0xbd22fc: DecompressPointer r5
    //     0xbd22fc: add             x5, x5, HEAP, lsl #32
    // 0xbd2300: LoadField: r6 = r2->field_13
    //     0xbd2300: ldur            x6, [x2, #0x13]
    // 0xbd2304: add             x0, x6, #1
    // 0xbd2308: StoreField: r2->field_13 = r0
    //     0xbd2308: stur            x0, [x2, #0x13]
    // 0xbd230c: LoadField: r0 = r5->field_13
    //     0xbd230c: ldur            w0, [x5, #0x13]
    // 0xbd2310: r1 = LoadInt32Instr(r0)
    //     0xbd2310: sbfx            x1, x0, #1, #0x1f
    // 0xbd2314: mov             x0, x1
    // 0xbd2318: mov             x1, x6
    // 0xbd231c: cmp             x1, x0
    // 0xbd2320: b.hs            #0xbd2520
    // 0xbd2324: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2324: add             x0, x5, x6
    //     0xbd2328: strb            w4, [x0, #0x17]
    // 0xbd232c: LoadField: r4 = r3->field_13
    //     0xbd232c: ldur            x4, [x3, #0x13]
    // 0xbd2330: r0 = BoxInt64Instr(r4)
    //     0xbd2330: sbfiz           x0, x4, #1, #0x1f
    //     0xbd2334: cmp             x4, x0, asr #1
    //     0xbd2338: b.eq            #0xbd2344
    //     0xbd233c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd2340: stur            x4, [x0, #7]
    // 0xbd2344: r16 = <int>
    //     0xbd2344: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd2348: stp             x2, x16, [SP, #8]
    // 0xbd234c: str             x0, [SP]
    // 0xbd2350: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2350: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2354: r0 = write()
    //     0xbd2354: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2358: ldur            x0, [fp, #-8]
    // 0xbd235c: LoadField: r1 = r0->field_b
    //     0xbd235c: ldur            w1, [x0, #0xb]
    // 0xbd2360: DecompressPointer r1
    //     0xbd2360: add             x1, x1, HEAP, lsl #32
    // 0xbd2364: LoadField: r2 = r1->field_13
    //     0xbd2364: ldur            w2, [x1, #0x13]
    // 0xbd2368: LoadField: r1 = r0->field_13
    //     0xbd2368: ldur            x1, [x0, #0x13]
    // 0xbd236c: r3 = LoadInt32Instr(r2)
    //     0xbd236c: sbfx            x3, x2, #1, #0x1f
    // 0xbd2370: sub             x2, x3, x1
    // 0xbd2374: cmp             x2, #1
    // 0xbd2378: b.ge            #0xbd2388
    // 0xbd237c: mov             x1, x0
    // 0xbd2380: r2 = 1
    //     0xbd2380: movz            x2, #0x1
    // 0xbd2384: r0 = _increaseBufferSize()
    //     0xbd2384: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2388: ldur            x2, [fp, #-8]
    // 0xbd238c: ldur            x3, [fp, #-0x10]
    // 0xbd2390: r4 = 3
    //     0xbd2390: movz            x4, #0x3
    // 0xbd2394: LoadField: r5 = r2->field_b
    //     0xbd2394: ldur            w5, [x2, #0xb]
    // 0xbd2398: DecompressPointer r5
    //     0xbd2398: add             x5, x5, HEAP, lsl #32
    // 0xbd239c: LoadField: r6 = r2->field_13
    //     0xbd239c: ldur            x6, [x2, #0x13]
    // 0xbd23a0: add             x0, x6, #1
    // 0xbd23a4: StoreField: r2->field_13 = r0
    //     0xbd23a4: stur            x0, [x2, #0x13]
    // 0xbd23a8: LoadField: r0 = r5->field_13
    //     0xbd23a8: ldur            w0, [x5, #0x13]
    // 0xbd23ac: r1 = LoadInt32Instr(r0)
    //     0xbd23ac: sbfx            x1, x0, #1, #0x1f
    // 0xbd23b0: mov             x0, x1
    // 0xbd23b4: mov             x1, x6
    // 0xbd23b8: cmp             x1, x0
    // 0xbd23bc: b.hs            #0xbd2524
    // 0xbd23c0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd23c0: add             x0, x5, x6
    //     0xbd23c4: strb            w4, [x0, #0x17]
    // 0xbd23c8: LoadField: r0 = r3->field_1b
    //     0xbd23c8: ldur            w0, [x3, #0x1b]
    // 0xbd23cc: DecompressPointer r0
    //     0xbd23cc: add             x0, x0, HEAP, lsl #32
    // 0xbd23d0: r16 = <String>
    //     0xbd23d0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd23d4: stp             x2, x16, [SP, #8]
    // 0xbd23d8: str             x0, [SP]
    // 0xbd23dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd23dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd23e0: r0 = write()
    //     0xbd23e0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd23e4: ldur            x0, [fp, #-8]
    // 0xbd23e8: LoadField: r1 = r0->field_b
    //     0xbd23e8: ldur            w1, [x0, #0xb]
    // 0xbd23ec: DecompressPointer r1
    //     0xbd23ec: add             x1, x1, HEAP, lsl #32
    // 0xbd23f0: LoadField: r2 = r1->field_13
    //     0xbd23f0: ldur            w2, [x1, #0x13]
    // 0xbd23f4: LoadField: r1 = r0->field_13
    //     0xbd23f4: ldur            x1, [x0, #0x13]
    // 0xbd23f8: r3 = LoadInt32Instr(r2)
    //     0xbd23f8: sbfx            x3, x2, #1, #0x1f
    // 0xbd23fc: sub             x2, x3, x1
    // 0xbd2400: cmp             x2, #1
    // 0xbd2404: b.ge            #0xbd2414
    // 0xbd2408: mov             x1, x0
    // 0xbd240c: r2 = 1
    //     0xbd240c: movz            x2, #0x1
    // 0xbd2410: r0 = _increaseBufferSize()
    //     0xbd2410: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2414: ldur            x2, [fp, #-8]
    // 0xbd2418: ldur            x3, [fp, #-0x10]
    // 0xbd241c: r4 = 4
    //     0xbd241c: movz            x4, #0x4
    // 0xbd2420: LoadField: r5 = r2->field_b
    //     0xbd2420: ldur            w5, [x2, #0xb]
    // 0xbd2424: DecompressPointer r5
    //     0xbd2424: add             x5, x5, HEAP, lsl #32
    // 0xbd2428: LoadField: r6 = r2->field_13
    //     0xbd2428: ldur            x6, [x2, #0x13]
    // 0xbd242c: add             x0, x6, #1
    // 0xbd2430: StoreField: r2->field_13 = r0
    //     0xbd2430: stur            x0, [x2, #0x13]
    // 0xbd2434: LoadField: r0 = r5->field_13
    //     0xbd2434: ldur            w0, [x5, #0x13]
    // 0xbd2438: r1 = LoadInt32Instr(r0)
    //     0xbd2438: sbfx            x1, x0, #1, #0x1f
    // 0xbd243c: mov             x0, x1
    // 0xbd2440: mov             x1, x6
    // 0xbd2444: cmp             x1, x0
    // 0xbd2448: b.hs            #0xbd2528
    // 0xbd244c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd244c: add             x0, x5, x6
    //     0xbd2450: strb            w4, [x0, #0x17]
    // 0xbd2454: LoadField: r0 = r3->field_1f
    //     0xbd2454: ldur            w0, [x3, #0x1f]
    // 0xbd2458: DecompressPointer r0
    //     0xbd2458: add             x0, x0, HEAP, lsl #32
    // 0xbd245c: r16 = <int?>
    //     0xbd245c: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbd2460: stp             x2, x16, [SP, #8]
    // 0xbd2464: str             x0, [SP]
    // 0xbd2468: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2468: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd246c: r0 = write()
    //     0xbd246c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2470: ldur            x0, [fp, #-8]
    // 0xbd2474: LoadField: r1 = r0->field_b
    //     0xbd2474: ldur            w1, [x0, #0xb]
    // 0xbd2478: DecompressPointer r1
    //     0xbd2478: add             x1, x1, HEAP, lsl #32
    // 0xbd247c: LoadField: r2 = r1->field_13
    //     0xbd247c: ldur            w2, [x1, #0x13]
    // 0xbd2480: LoadField: r1 = r0->field_13
    //     0xbd2480: ldur            x1, [x0, #0x13]
    // 0xbd2484: r3 = LoadInt32Instr(r2)
    //     0xbd2484: sbfx            x3, x2, #1, #0x1f
    // 0xbd2488: sub             x2, x3, x1
    // 0xbd248c: cmp             x2, #1
    // 0xbd2490: b.ge            #0xbd24a0
    // 0xbd2494: mov             x1, x0
    // 0xbd2498: r2 = 1
    //     0xbd2498: movz            x2, #0x1
    // 0xbd249c: r0 = _increaseBufferSize()
    //     0xbd249c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd24a0: ldur            x2, [fp, #-8]
    // 0xbd24a4: ldur            x3, [fp, #-0x10]
    // 0xbd24a8: r4 = 5
    //     0xbd24a8: movz            x4, #0x5
    // 0xbd24ac: LoadField: r5 = r2->field_b
    //     0xbd24ac: ldur            w5, [x2, #0xb]
    // 0xbd24b0: DecompressPointer r5
    //     0xbd24b0: add             x5, x5, HEAP, lsl #32
    // 0xbd24b4: LoadField: r6 = r2->field_13
    //     0xbd24b4: ldur            x6, [x2, #0x13]
    // 0xbd24b8: add             x0, x6, #1
    // 0xbd24bc: StoreField: r2->field_13 = r0
    //     0xbd24bc: stur            x0, [x2, #0x13]
    // 0xbd24c0: LoadField: r0 = r5->field_13
    //     0xbd24c0: ldur            w0, [x5, #0x13]
    // 0xbd24c4: r1 = LoadInt32Instr(r0)
    //     0xbd24c4: sbfx            x1, x0, #1, #0x1f
    // 0xbd24c8: mov             x0, x1
    // 0xbd24cc: mov             x1, x6
    // 0xbd24d0: cmp             x1, x0
    // 0xbd24d4: b.hs            #0xbd252c
    // 0xbd24d8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd24d8: add             x0, x5, x6
    //     0xbd24dc: strb            w4, [x0, #0x17]
    // 0xbd24e0: LoadField: r0 = r3->field_23
    //     0xbd24e0: ldur            w0, [x3, #0x23]
    // 0xbd24e4: DecompressPointer r0
    //     0xbd24e4: add             x0, x0, HEAP, lsl #32
    // 0xbd24e8: r16 = <String>
    //     0xbd24e8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd24ec: stp             x2, x16, [SP, #8]
    // 0xbd24f0: str             x0, [SP]
    // 0xbd24f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd24f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd24f8: r0 = write()
    //     0xbd24f8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd24fc: r0 = Null
    //     0xbd24fc: mov             x0, NULL
    // 0xbd2500: LeaveFrame
    //     0xbd2500: mov             SP, fp
    //     0xbd2504: ldp             fp, lr, [SP], #0x10
    // 0xbd2508: ret
    //     0xbd2508: ret             
    // 0xbd250c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd250c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd2510: b               #0xbd2108
    // 0xbd2514: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2514: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2518: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd251c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd251c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2520: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2520: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2524: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2524: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2528: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2528: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd252c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd252c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf00fc, size: 0x24
    // 0xbf00fc: r1 = 122
    //     0xbf00fc: movz            x1, #0x7a
    // 0xbf0100: r16 = LoadInt32Instr(r1)
    //     0xbf0100: sbfx            x16, x1, #1, #0x1f
    // 0xbf0104: r17 = 11601
    //     0xbf0104: movz            x17, #0x2d51
    // 0xbf0108: mul             x0, x16, x17
    // 0xbf010c: umulh           x16, x16, x17
    // 0xbf0110: eor             x0, x0, x16
    // 0xbf0114: r0 = 0
    //     0xbf0114: eor             x0, x0, x0, lsr #32
    // 0xbf0118: ubfiz           x0, x0, #1, #0x1e
    // 0xbf011c: ret
    //     0xbf011c: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd764b8, size: 0x9c
    // 0xd764b8: EnterFrame
    //     0xd764b8: stp             fp, lr, [SP, #-0x10]!
    //     0xd764bc: mov             fp, SP
    // 0xd764c0: AllocStack(0x10)
    //     0xd764c0: sub             SP, SP, #0x10
    // 0xd764c4: CheckStackOverflow
    //     0xd764c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd764c8: cmp             SP, x16
    //     0xd764cc: b.ls            #0xd7654c
    // 0xd764d0: ldr             x0, [fp, #0x10]
    // 0xd764d4: cmp             w0, NULL
    // 0xd764d8: b.ne            #0xd764ec
    // 0xd764dc: r0 = false
    //     0xd764dc: add             x0, NULL, #0x30  ; false
    // 0xd764e0: LeaveFrame
    //     0xd764e0: mov             SP, fp
    //     0xd764e4: ldp             fp, lr, [SP], #0x10
    // 0xd764e8: ret
    //     0xd764e8: ret             
    // 0xd764ec: ldr             x1, [fp, #0x18]
    // 0xd764f0: cmp             w1, w0
    // 0xd764f4: b.ne            #0xd76500
    // 0xd764f8: r0 = true
    //     0xd764f8: add             x0, NULL, #0x20  ; true
    // 0xd764fc: b               #0xd76540
    // 0xd76500: r1 = 60
    //     0xd76500: movz            x1, #0x3c
    // 0xd76504: branchIfSmi(r0, 0xd76510)
    //     0xd76504: tbz             w0, #0, #0xd76510
    // 0xd76508: r1 = LoadClassIdInstr(r0)
    //     0xd76508: ldur            x1, [x0, #-1]
    //     0xd7650c: ubfx            x1, x1, #0xc, #0x14
    // 0xd76510: cmp             x1, #0x67f
    // 0xd76514: b.ne            #0xd7653c
    // 0xd76518: r16 = DoaCategoryAdapter
    //     0xd76518: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b3f0] Type: DoaCategoryAdapter
    //     0xd7651c: ldr             x16, [x16, #0x3f0]
    // 0xd76520: r30 = DoaCategoryAdapter
    //     0xd76520: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b3f0] Type: DoaCategoryAdapter
    //     0xd76524: ldr             lr, [lr, #0x3f0]
    // 0xd76528: stp             lr, x16, [SP]
    // 0xd7652c: r0 = ==()
    //     0xd7652c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76530: tbnz            w0, #4, #0xd7653c
    // 0xd76534: r0 = true
    //     0xd76534: add             x0, NULL, #0x20  ; true
    // 0xd76538: b               #0xd76540
    // 0xd7653c: r0 = false
    //     0xd7653c: add             x0, NULL, #0x30  ; false
    // 0xd76540: LeaveFrame
    //     0xd76540: mov             SP, fp
    //     0xd76544: ldp             fp, lr, [SP], #0x10
    // 0xd76548: ret
    //     0xd76548: ret             
    // 0xd7654c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7654c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76550: b               #0xd764d0
  }
}

// class id: 1664, size: 0x14, field offset: 0xc
class DoaAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa60654, size: 0x5c4
    // 0xa60654: EnterFrame
    //     0xa60654: stp             fp, lr, [SP, #-0x10]!
    //     0xa60658: mov             fp, SP
    // 0xa6065c: AllocStack(0x70)
    //     0xa6065c: sub             SP, SP, #0x70
    // 0xa60660: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa60660: stur            x2, [fp, #-0x20]
    // 0xa60664: CheckStackOverflow
    //     0xa60664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa60668: cmp             SP, x16
    //     0xa6066c: b.ls            #0xa60c00
    // 0xa60670: LoadField: r3 = r2->field_23
    //     0xa60670: ldur            x3, [x2, #0x23]
    // 0xa60674: add             x0, x3, #1
    // 0xa60678: LoadField: r1 = r2->field_1b
    //     0xa60678: ldur            x1, [x2, #0x1b]
    // 0xa6067c: cmp             x0, x1
    // 0xa60680: b.gt            #0xa60ba4
    // 0xa60684: LoadField: r4 = r2->field_7
    //     0xa60684: ldur            w4, [x2, #7]
    // 0xa60688: DecompressPointer r4
    //     0xa60688: add             x4, x4, HEAP, lsl #32
    // 0xa6068c: stur            x4, [fp, #-0x18]
    // 0xa60690: StoreField: r2->field_23 = r0
    //     0xa60690: stur            x0, [x2, #0x23]
    // 0xa60694: LoadField: r0 = r4->field_13
    //     0xa60694: ldur            w0, [x4, #0x13]
    // 0xa60698: r5 = LoadInt32Instr(r0)
    //     0xa60698: sbfx            x5, x0, #1, #0x1f
    // 0xa6069c: mov             x0, x5
    // 0xa606a0: mov             x1, x3
    // 0xa606a4: stur            x5, [fp, #-0x10]
    // 0xa606a8: cmp             x1, x0
    // 0xa606ac: b.hs            #0xa60c08
    // 0xa606b0: LoadField: r0 = r4->field_7
    //     0xa606b0: ldur            x0, [x4, #7]
    // 0xa606b4: ldrb            w1, [x0, x3]
    // 0xa606b8: stur            x1, [fp, #-8]
    // 0xa606bc: r16 = <int, dynamic>
    //     0xa606bc: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa606c0: ldr             x16, [x16, #0xac0]
    // 0xa606c4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa606c8: stp             lr, x16, [SP]
    // 0xa606cc: r0 = Map._fromLiteral()
    //     0xa606cc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa606d0: mov             x2, x0
    // 0xa606d4: stur            x2, [fp, #-0x38]
    // 0xa606d8: r6 = 0
    //     0xa606d8: movz            x6, #0
    // 0xa606dc: ldur            x3, [fp, #-0x20]
    // 0xa606e0: ldur            x4, [fp, #-0x18]
    // 0xa606e4: ldur            x5, [fp, #-8]
    // 0xa606e8: stur            x6, [fp, #-0x30]
    // 0xa606ec: CheckStackOverflow
    //     0xa606ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa606f0: cmp             SP, x16
    //     0xa606f4: b.ls            #0xa60c0c
    // 0xa606f8: cmp             x6, x5
    // 0xa606fc: b.ge            #0xa60788
    // 0xa60700: LoadField: r7 = r3->field_23
    //     0xa60700: ldur            x7, [x3, #0x23]
    // 0xa60704: add             x0, x7, #1
    // 0xa60708: LoadField: r1 = r3->field_1b
    //     0xa60708: ldur            x1, [x3, #0x1b]
    // 0xa6070c: cmp             x0, x1
    // 0xa60710: b.gt            #0xa60bcc
    // 0xa60714: StoreField: r3->field_23 = r0
    //     0xa60714: stur            x0, [x3, #0x23]
    // 0xa60718: ldur            x0, [fp, #-0x10]
    // 0xa6071c: mov             x1, x7
    // 0xa60720: cmp             x1, x0
    // 0xa60724: b.hs            #0xa60c14
    // 0xa60728: LoadField: r0 = r4->field_7
    //     0xa60728: ldur            x0, [x4, #7]
    // 0xa6072c: ldrb            w8, [x0, x7]
    // 0xa60730: mov             x1, x3
    // 0xa60734: stur            x8, [fp, #-0x28]
    // 0xa60738: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa60738: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa6073c: r0 = read()
    //     0xa6073c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa60740: mov             x1, x0
    // 0xa60744: ldur            x0, [fp, #-0x28]
    // 0xa60748: lsl             x2, x0, #1
    // 0xa6074c: r16 = LoadInt32Instr(r2)
    //     0xa6074c: sbfx            x16, x2, #1, #0x1f
    // 0xa60750: r17 = 11601
    //     0xa60750: movz            x17, #0x2d51
    // 0xa60754: mul             x0, x16, x17
    // 0xa60758: umulh           x16, x16, x17
    // 0xa6075c: eor             x0, x0, x16
    // 0xa60760: r0 = 0
    //     0xa60760: eor             x0, x0, x0, lsr #32
    // 0xa60764: ubfiz           x0, x0, #1, #0x1e
    // 0xa60768: r5 = LoadInt32Instr(r0)
    //     0xa60768: sbfx            x5, x0, #1, #0x1f
    // 0xa6076c: mov             x3, x1
    // 0xa60770: ldur            x1, [fp, #-0x38]
    // 0xa60774: r0 = _set()
    //     0xa60774: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa60778: ldur            x0, [fp, #-0x30]
    // 0xa6077c: add             x6, x0, #1
    // 0xa60780: ldur            x2, [fp, #-0x38]
    // 0xa60784: b               #0xa606dc
    // 0xa60788: mov             x0, x2
    // 0xa6078c: mov             x1, x0
    // 0xa60790: r2 = 0
    //     0xa60790: movz            x2, #0
    // 0xa60794: r0 = _getValueOrData()
    //     0xa60794: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60798: ldur            x3, [fp, #-0x38]
    // 0xa6079c: LoadField: r1 = r3->field_f
    //     0xa6079c: ldur            w1, [x3, #0xf]
    // 0xa607a0: DecompressPointer r1
    //     0xa607a0: add             x1, x1, HEAP, lsl #32
    // 0xa607a4: cmp             w1, w0
    // 0xa607a8: b.ne            #0xa607b4
    // 0xa607ac: r4 = Null
    //     0xa607ac: mov             x4, NULL
    // 0xa607b0: b               #0xa607b8
    // 0xa607b4: mov             x4, x0
    // 0xa607b8: mov             x0, x4
    // 0xa607bc: stur            x4, [fp, #-0x18]
    // 0xa607c0: r2 = Null
    //     0xa607c0: mov             x2, NULL
    // 0xa607c4: r1 = Null
    //     0xa607c4: mov             x1, NULL
    // 0xa607c8: branchIfSmi(r0, 0xa607f0)
    //     0xa607c8: tbz             w0, #0, #0xa607f0
    // 0xa607cc: r4 = LoadClassIdInstr(r0)
    //     0xa607cc: ldur            x4, [x0, #-1]
    //     0xa607d0: ubfx            x4, x4, #0xc, #0x14
    // 0xa607d4: sub             x4, x4, #0x3c
    // 0xa607d8: cmp             x4, #1
    // 0xa607dc: b.ls            #0xa607f0
    // 0xa607e0: r8 = int
    //     0xa607e0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa607e4: r3 = Null
    //     0xa607e4: add             x3, PP, #0x21, lsl #12  ; [pp+0x214f8] Null
    //     0xa607e8: ldr             x3, [x3, #0x4f8]
    // 0xa607ec: r0 = int()
    //     0xa607ec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa607f0: ldur            x1, [fp, #-0x38]
    // 0xa607f4: r2 = 2
    //     0xa607f4: movz            x2, #0x2
    // 0xa607f8: r0 = _getValueOrData()
    //     0xa607f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa607fc: ldur            x3, [fp, #-0x38]
    // 0xa60800: LoadField: r1 = r3->field_f
    //     0xa60800: ldur            w1, [x3, #0xf]
    // 0xa60804: DecompressPointer r1
    //     0xa60804: add             x1, x1, HEAP, lsl #32
    // 0xa60808: cmp             w1, w0
    // 0xa6080c: b.ne            #0xa60818
    // 0xa60810: r4 = Null
    //     0xa60810: mov             x4, NULL
    // 0xa60814: b               #0xa6081c
    // 0xa60818: mov             x4, x0
    // 0xa6081c: mov             x0, x4
    // 0xa60820: stur            x4, [fp, #-0x20]
    // 0xa60824: r2 = Null
    //     0xa60824: mov             x2, NULL
    // 0xa60828: r1 = Null
    //     0xa60828: mov             x1, NULL
    // 0xa6082c: branchIfSmi(r0, 0xa60854)
    //     0xa6082c: tbz             w0, #0, #0xa60854
    // 0xa60830: r4 = LoadClassIdInstr(r0)
    //     0xa60830: ldur            x4, [x0, #-1]
    //     0xa60834: ubfx            x4, x4, #0xc, #0x14
    // 0xa60838: sub             x4, x4, #0x3c
    // 0xa6083c: cmp             x4, #1
    // 0xa60840: b.ls            #0xa60854
    // 0xa60844: r8 = int
    //     0xa60844: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60848: r3 = Null
    //     0xa60848: add             x3, PP, #0x21, lsl #12  ; [pp+0x21508] Null
    //     0xa6084c: ldr             x3, [x3, #0x508]
    // 0xa60850: r0 = int()
    //     0xa60850: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60854: ldur            x1, [fp, #-0x38]
    // 0xa60858: r2 = 4
    //     0xa60858: movz            x2, #0x4
    // 0xa6085c: r0 = _getValueOrData()
    //     0xa6085c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60860: ldur            x3, [fp, #-0x38]
    // 0xa60864: LoadField: r1 = r3->field_f
    //     0xa60864: ldur            w1, [x3, #0xf]
    // 0xa60868: DecompressPointer r1
    //     0xa60868: add             x1, x1, HEAP, lsl #32
    // 0xa6086c: cmp             w1, w0
    // 0xa60870: b.ne            #0xa6087c
    // 0xa60874: r4 = Null
    //     0xa60874: mov             x4, NULL
    // 0xa60878: b               #0xa60880
    // 0xa6087c: mov             x4, x0
    // 0xa60880: mov             x0, x4
    // 0xa60884: stur            x4, [fp, #-0x40]
    // 0xa60888: r2 = Null
    //     0xa60888: mov             x2, NULL
    // 0xa6088c: r1 = Null
    //     0xa6088c: mov             x1, NULL
    // 0xa60890: branchIfSmi(r0, 0xa608b8)
    //     0xa60890: tbz             w0, #0, #0xa608b8
    // 0xa60894: r4 = LoadClassIdInstr(r0)
    //     0xa60894: ldur            x4, [x0, #-1]
    //     0xa60898: ubfx            x4, x4, #0xc, #0x14
    // 0xa6089c: sub             x4, x4, #0x3c
    // 0xa608a0: cmp             x4, #1
    // 0xa608a4: b.ls            #0xa608b8
    // 0xa608a8: r8 = int
    //     0xa608a8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa608ac: r3 = Null
    //     0xa608ac: add             x3, PP, #0x21, lsl #12  ; [pp+0x21518] Null
    //     0xa608b0: ldr             x3, [x3, #0x518]
    // 0xa608b4: r0 = int()
    //     0xa608b4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa608b8: ldur            x1, [fp, #-0x38]
    // 0xa608bc: r2 = 6
    //     0xa608bc: movz            x2, #0x6
    // 0xa608c0: r0 = _getValueOrData()
    //     0xa608c0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa608c4: ldur            x3, [fp, #-0x38]
    // 0xa608c8: LoadField: r1 = r3->field_f
    //     0xa608c8: ldur            w1, [x3, #0xf]
    // 0xa608cc: DecompressPointer r1
    //     0xa608cc: add             x1, x1, HEAP, lsl #32
    // 0xa608d0: cmp             w1, w0
    // 0xa608d4: b.ne            #0xa608e0
    // 0xa608d8: r4 = Null
    //     0xa608d8: mov             x4, NULL
    // 0xa608dc: b               #0xa608e4
    // 0xa608e0: mov             x4, x0
    // 0xa608e4: mov             x0, x4
    // 0xa608e8: stur            x4, [fp, #-0x48]
    // 0xa608ec: r2 = Null
    //     0xa608ec: mov             x2, NULL
    // 0xa608f0: r1 = Null
    //     0xa608f0: mov             x1, NULL
    // 0xa608f4: r4 = 60
    //     0xa608f4: movz            x4, #0x3c
    // 0xa608f8: branchIfSmi(r0, 0xa60904)
    //     0xa608f8: tbz             w0, #0, #0xa60904
    // 0xa608fc: r4 = LoadClassIdInstr(r0)
    //     0xa608fc: ldur            x4, [x0, #-1]
    //     0xa60900: ubfx            x4, x4, #0xc, #0x14
    // 0xa60904: sub             x4, x4, #0x5e
    // 0xa60908: cmp             x4, #1
    // 0xa6090c: b.ls            #0xa60920
    // 0xa60910: r8 = String
    //     0xa60910: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60914: r3 = Null
    //     0xa60914: add             x3, PP, #0x21, lsl #12  ; [pp+0x21528] Null
    //     0xa60918: ldr             x3, [x3, #0x528]
    // 0xa6091c: r0 = String()
    //     0xa6091c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60920: ldur            x1, [fp, #-0x38]
    // 0xa60924: r2 = 12
    //     0xa60924: movz            x2, #0xc
    // 0xa60928: r0 = _getValueOrData()
    //     0xa60928: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6092c: ldur            x3, [fp, #-0x38]
    // 0xa60930: LoadField: r1 = r3->field_f
    //     0xa60930: ldur            w1, [x3, #0xf]
    // 0xa60934: DecompressPointer r1
    //     0xa60934: add             x1, x1, HEAP, lsl #32
    // 0xa60938: cmp             w1, w0
    // 0xa6093c: b.ne            #0xa60948
    // 0xa60940: r4 = Null
    //     0xa60940: mov             x4, NULL
    // 0xa60944: b               #0xa6094c
    // 0xa60948: mov             x4, x0
    // 0xa6094c: mov             x0, x4
    // 0xa60950: stur            x4, [fp, #-0x50]
    // 0xa60954: r2 = Null
    //     0xa60954: mov             x2, NULL
    // 0xa60958: r1 = Null
    //     0xa60958: mov             x1, NULL
    // 0xa6095c: branchIfSmi(r0, 0xa60984)
    //     0xa6095c: tbz             w0, #0, #0xa60984
    // 0xa60960: r4 = LoadClassIdInstr(r0)
    //     0xa60960: ldur            x4, [x0, #-1]
    //     0xa60964: ubfx            x4, x4, #0xc, #0x14
    // 0xa60968: sub             x4, x4, #0x3c
    // 0xa6096c: cmp             x4, #1
    // 0xa60970: b.ls            #0xa60984
    // 0xa60974: r8 = int
    //     0xa60974: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa60978: r3 = Null
    //     0xa60978: add             x3, PP, #0x21, lsl #12  ; [pp+0x21538] Null
    //     0xa6097c: ldr             x3, [x3, #0x538]
    // 0xa60980: r0 = int()
    //     0xa60980: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa60984: ldur            x1, [fp, #-0x38]
    // 0xa60988: r2 = 8
    //     0xa60988: movz            x2, #0x8
    // 0xa6098c: r0 = _getValueOrData()
    //     0xa6098c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60990: ldur            x3, [fp, #-0x38]
    // 0xa60994: LoadField: r1 = r3->field_f
    //     0xa60994: ldur            w1, [x3, #0xf]
    // 0xa60998: DecompressPointer r1
    //     0xa60998: add             x1, x1, HEAP, lsl #32
    // 0xa6099c: cmp             w1, w0
    // 0xa609a0: b.ne            #0xa609ac
    // 0xa609a4: r4 = Null
    //     0xa609a4: mov             x4, NULL
    // 0xa609a8: b               #0xa609b0
    // 0xa609ac: mov             x4, x0
    // 0xa609b0: mov             x0, x4
    // 0xa609b4: stur            x4, [fp, #-0x58]
    // 0xa609b8: r2 = Null
    //     0xa609b8: mov             x2, NULL
    // 0xa609bc: r1 = Null
    //     0xa609bc: mov             x1, NULL
    // 0xa609c0: r4 = 60
    //     0xa609c0: movz            x4, #0x3c
    // 0xa609c4: branchIfSmi(r0, 0xa609d0)
    //     0xa609c4: tbz             w0, #0, #0xa609d0
    // 0xa609c8: r4 = LoadClassIdInstr(r0)
    //     0xa609c8: ldur            x4, [x0, #-1]
    //     0xa609cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa609d0: sub             x4, x4, #0x5e
    // 0xa609d4: cmp             x4, #1
    // 0xa609d8: b.ls            #0xa609ec
    // 0xa609dc: r8 = String?
    //     0xa609dc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa609e0: r3 = Null
    //     0xa609e0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21548] Null
    //     0xa609e4: ldr             x3, [x3, #0x548]
    // 0xa609e8: r0 = String?()
    //     0xa609e8: bl              #0x600324  ; IsType_String?_Stub
    // 0xa609ec: ldur            x1, [fp, #-0x38]
    // 0xa609f0: r2 = 10
    //     0xa609f0: movz            x2, #0xa
    // 0xa609f4: r0 = _getValueOrData()
    //     0xa609f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa609f8: ldur            x3, [fp, #-0x38]
    // 0xa609fc: LoadField: r1 = r3->field_f
    //     0xa609fc: ldur            w1, [x3, #0xf]
    // 0xa60a00: DecompressPointer r1
    //     0xa60a00: add             x1, x1, HEAP, lsl #32
    // 0xa60a04: cmp             w1, w0
    // 0xa60a08: b.ne            #0xa60a14
    // 0xa60a0c: r4 = Null
    //     0xa60a0c: mov             x4, NULL
    // 0xa60a10: b               #0xa60a18
    // 0xa60a14: mov             x4, x0
    // 0xa60a18: mov             x0, x4
    // 0xa60a1c: stur            x4, [fp, #-0x60]
    // 0xa60a20: r2 = Null
    //     0xa60a20: mov             x2, NULL
    // 0xa60a24: r1 = Null
    //     0xa60a24: mov             x1, NULL
    // 0xa60a28: r4 = 60
    //     0xa60a28: movz            x4, #0x3c
    // 0xa60a2c: branchIfSmi(r0, 0xa60a38)
    //     0xa60a2c: tbz             w0, #0, #0xa60a38
    // 0xa60a30: r4 = LoadClassIdInstr(r0)
    //     0xa60a30: ldur            x4, [x0, #-1]
    //     0xa60a34: ubfx            x4, x4, #0xc, #0x14
    // 0xa60a38: sub             x4, x4, #0x5e
    // 0xa60a3c: cmp             x4, #1
    // 0xa60a40: b.ls            #0xa60a54
    // 0xa60a44: r8 = String?
    //     0xa60a44: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa60a48: r3 = Null
    //     0xa60a48: add             x3, PP, #0x21, lsl #12  ; [pp+0x21558] Null
    //     0xa60a4c: ldr             x3, [x3, #0x558]
    // 0xa60a50: r0 = String?()
    //     0xa60a50: bl              #0x600324  ; IsType_String?_Stub
    // 0xa60a54: ldur            x1, [fp, #-0x38]
    // 0xa60a58: r2 = 14
    //     0xa60a58: movz            x2, #0xe
    // 0xa60a5c: r0 = _getValueOrData()
    //     0xa60a5c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60a60: mov             x1, x0
    // 0xa60a64: ldur            x0, [fp, #-0x38]
    // 0xa60a68: LoadField: r2 = r0->field_f
    //     0xa60a68: ldur            w2, [x0, #0xf]
    // 0xa60a6c: DecompressPointer r2
    //     0xa60a6c: add             x2, x2, HEAP, lsl #32
    // 0xa60a70: cmp             w2, w1
    // 0xa60a74: b.eq            #0xa60a80
    // 0xa60a78: cmp             w1, NULL
    // 0xa60a7c: b.ne            #0xa60a88
    // 0xa60a80: r7 = ""
    //     0xa60a80: ldr             x7, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa60a84: b               #0xa60af8
    // 0xa60a88: mov             x1, x0
    // 0xa60a8c: r2 = 14
    //     0xa60a8c: movz            x2, #0xe
    // 0xa60a90: r0 = _getValueOrData()
    //     0xa60a90: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa60a94: mov             x1, x0
    // 0xa60a98: ldur            x0, [fp, #-0x38]
    // 0xa60a9c: LoadField: r2 = r0->field_f
    //     0xa60a9c: ldur            w2, [x0, #0xf]
    // 0xa60aa0: DecompressPointer r2
    //     0xa60aa0: add             x2, x2, HEAP, lsl #32
    // 0xa60aa4: cmp             w2, w1
    // 0xa60aa8: b.ne            #0xa60ab4
    // 0xa60aac: r3 = Null
    //     0xa60aac: mov             x3, NULL
    // 0xa60ab0: b               #0xa60ab8
    // 0xa60ab4: mov             x3, x1
    // 0xa60ab8: mov             x0, x3
    // 0xa60abc: stur            x3, [fp, #-0x38]
    // 0xa60ac0: r2 = Null
    //     0xa60ac0: mov             x2, NULL
    // 0xa60ac4: r1 = Null
    //     0xa60ac4: mov             x1, NULL
    // 0xa60ac8: r4 = 60
    //     0xa60ac8: movz            x4, #0x3c
    // 0xa60acc: branchIfSmi(r0, 0xa60ad8)
    //     0xa60acc: tbz             w0, #0, #0xa60ad8
    // 0xa60ad0: r4 = LoadClassIdInstr(r0)
    //     0xa60ad0: ldur            x4, [x0, #-1]
    //     0xa60ad4: ubfx            x4, x4, #0xc, #0x14
    // 0xa60ad8: sub             x4, x4, #0x5e
    // 0xa60adc: cmp             x4, #1
    // 0xa60ae0: b.ls            #0xa60af4
    // 0xa60ae4: r8 = String
    //     0xa60ae4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa60ae8: r3 = Null
    //     0xa60ae8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21568] Null
    //     0xa60aec: ldr             x3, [x3, #0x568]
    // 0xa60af0: r0 = String()
    //     0xa60af0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa60af4: ldur            x7, [fp, #-0x38]
    // 0xa60af8: ldur            x6, [fp, #-0x18]
    // 0xa60afc: ldur            x5, [fp, #-0x20]
    // 0xa60b00: ldur            x4, [fp, #-0x40]
    // 0xa60b04: ldur            x3, [fp, #-0x48]
    // 0xa60b08: ldur            x2, [fp, #-0x50]
    // 0xa60b0c: ldur            x1, [fp, #-0x58]
    // 0xa60b10: ldur            x0, [fp, #-0x60]
    // 0xa60b14: stur            x7, [fp, #-0x38]
    // 0xa60b18: r8 = LoadInt32Instr(r6)
    //     0xa60b18: sbfx            x8, x6, #1, #0x1f
    //     0xa60b1c: tbz             w6, #0, #0xa60b24
    //     0xa60b20: ldur            x8, [x6, #7]
    // 0xa60b24: stur            x8, [fp, #-8]
    // 0xa60b28: r0 = Doa()
    //     0xa60b28: bl              #0xa60c18  ; AllocateDoaStub -> Doa (size=0x38)
    // 0xa60b2c: mov             x1, x0
    // 0xa60b30: ldur            x0, [fp, #-8]
    // 0xa60b34: StoreField: r1->field_7 = r0
    //     0xa60b34: stur            x0, [x1, #7]
    // 0xa60b38: ldur            x0, [fp, #-0x20]
    // 0xa60b3c: r2 = LoadInt32Instr(r0)
    //     0xa60b3c: sbfx            x2, x0, #1, #0x1f
    //     0xa60b40: tbz             w0, #0, #0xa60b48
    //     0xa60b44: ldur            x2, [x0, #7]
    // 0xa60b48: StoreField: r1->field_f = r2
    //     0xa60b48: stur            x2, [x1, #0xf]
    // 0xa60b4c: ldur            x0, [fp, #-0x40]
    // 0xa60b50: r2 = LoadInt32Instr(r0)
    //     0xa60b50: sbfx            x2, x0, #1, #0x1f
    //     0xa60b54: tbz             w0, #0, #0xa60b5c
    //     0xa60b58: ldur            x2, [x0, #7]
    // 0xa60b5c: ArrayStore: r1[0] = r2  ; List_8
    //     0xa60b5c: stur            x2, [x1, #0x17]
    // 0xa60b60: ldur            x0, [fp, #-0x48]
    // 0xa60b64: StoreField: r1->field_1f = r0
    //     0xa60b64: stur            w0, [x1, #0x1f]
    // 0xa60b68: ldur            x0, [fp, #-0x50]
    // 0xa60b6c: r2 = LoadInt32Instr(r0)
    //     0xa60b6c: sbfx            x2, x0, #1, #0x1f
    //     0xa60b70: tbz             w0, #0, #0xa60b78
    //     0xa60b74: ldur            x2, [x0, #7]
    // 0xa60b78: StoreField: r1->field_2b = r2
    //     0xa60b78: stur            x2, [x1, #0x2b]
    // 0xa60b7c: ldur            x0, [fp, #-0x58]
    // 0xa60b80: StoreField: r1->field_23 = r0
    //     0xa60b80: stur            w0, [x1, #0x23]
    // 0xa60b84: ldur            x0, [fp, #-0x60]
    // 0xa60b88: StoreField: r1->field_27 = r0
    //     0xa60b88: stur            w0, [x1, #0x27]
    // 0xa60b8c: ldur            x0, [fp, #-0x38]
    // 0xa60b90: StoreField: r1->field_33 = r0
    //     0xa60b90: stur            w0, [x1, #0x33]
    // 0xa60b94: mov             x0, x1
    // 0xa60b98: LeaveFrame
    //     0xa60b98: mov             SP, fp
    //     0xa60b9c: ldp             fp, lr, [SP], #0x10
    // 0xa60ba0: ret
    //     0xa60ba0: ret             
    // 0xa60ba4: r0 = RangeError()
    //     0xa60ba4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa60ba8: mov             x1, x0
    // 0xa60bac: r0 = "Not enough bytes available."
    //     0xa60bac: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60bb0: ldr             x0, [x0, #0x8a8]
    // 0xa60bb4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60bb4: stur            w0, [x1, #0x17]
    // 0xa60bb8: r2 = false
    //     0xa60bb8: add             x2, NULL, #0x30  ; false
    // 0xa60bbc: StoreField: r1->field_b = r2
    //     0xa60bbc: stur            w2, [x1, #0xb]
    // 0xa60bc0: mov             x0, x1
    // 0xa60bc4: r0 = Throw()
    //     0xa60bc4: bl              #0xec04b8  ; ThrowStub
    // 0xa60bc8: brk             #0
    // 0xa60bcc: r0 = "Not enough bytes available."
    //     0xa60bcc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60bd0: ldr             x0, [x0, #0x8a8]
    // 0xa60bd4: r2 = false
    //     0xa60bd4: add             x2, NULL, #0x30  ; false
    // 0xa60bd8: r0 = RangeError()
    //     0xa60bd8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa60bdc: mov             x1, x0
    // 0xa60be0: r0 = "Not enough bytes available."
    //     0xa60be0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa60be4: ldr             x0, [x0, #0x8a8]
    // 0xa60be8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa60be8: stur            w0, [x1, #0x17]
    // 0xa60bec: r0 = false
    //     0xa60bec: add             x0, NULL, #0x30  ; false
    // 0xa60bf0: StoreField: r1->field_b = r0
    //     0xa60bf0: stur            w0, [x1, #0xb]
    // 0xa60bf4: mov             x0, x1
    // 0xa60bf8: r0 = Throw()
    //     0xa60bf8: bl              #0xec04b8  ; ThrowStub
    // 0xa60bfc: brk             #0
    // 0xa60c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60c00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60c04: b               #0xa60670
    // 0xa60c08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60c08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa60c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60c0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60c10: b               #0xa606f8
    // 0xa60c14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60c14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd1b58, size: 0x58c
    // 0xbd1b58: EnterFrame
    //     0xbd1b58: stp             fp, lr, [SP, #-0x10]!
    //     0xbd1b5c: mov             fp, SP
    // 0xbd1b60: AllocStack(0x28)
    //     0xbd1b60: sub             SP, SP, #0x28
    // 0xbd1b64: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd1b64: mov             x4, x2
    //     0xbd1b68: stur            x2, [fp, #-8]
    //     0xbd1b6c: stur            x3, [fp, #-0x10]
    // 0xbd1b70: CheckStackOverflow
    //     0xbd1b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd1b74: cmp             SP, x16
    //     0xbd1b78: b.ls            #0xbd20b8
    // 0xbd1b7c: mov             x0, x3
    // 0xbd1b80: r2 = Null
    //     0xbd1b80: mov             x2, NULL
    // 0xbd1b84: r1 = Null
    //     0xbd1b84: mov             x1, NULL
    // 0xbd1b88: r4 = 60
    //     0xbd1b88: movz            x4, #0x3c
    // 0xbd1b8c: branchIfSmi(r0, 0xbd1b98)
    //     0xbd1b8c: tbz             w0, #0, #0xbd1b98
    // 0xbd1b90: r4 = LoadClassIdInstr(r0)
    //     0xbd1b90: ldur            x4, [x0, #-1]
    //     0xbd1b94: ubfx            x4, x4, #0xc, #0x14
    // 0xbd1b98: r17 = 5594
    //     0xbd1b98: movz            x17, #0x15da
    // 0xbd1b9c: cmp             x4, x17
    // 0xbd1ba0: b.eq            #0xbd1bb8
    // 0xbd1ba4: r8 = Doa
    //     0xbd1ba4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b3d8] Type: Doa
    //     0xbd1ba8: ldr             x8, [x8, #0x3d8]
    // 0xbd1bac: r3 = Null
    //     0xbd1bac: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3e0] Null
    //     0xbd1bb0: ldr             x3, [x3, #0x3e0]
    // 0xbd1bb4: r0 = Doa()
    //     0xbd1bb4: bl              #0x80f7b4  ; IsType_Doa_Stub
    // 0xbd1bb8: ldur            x0, [fp, #-8]
    // 0xbd1bbc: LoadField: r1 = r0->field_b
    //     0xbd1bbc: ldur            w1, [x0, #0xb]
    // 0xbd1bc0: DecompressPointer r1
    //     0xbd1bc0: add             x1, x1, HEAP, lsl #32
    // 0xbd1bc4: LoadField: r2 = r1->field_13
    //     0xbd1bc4: ldur            w2, [x1, #0x13]
    // 0xbd1bc8: LoadField: r1 = r0->field_13
    //     0xbd1bc8: ldur            x1, [x0, #0x13]
    // 0xbd1bcc: r3 = LoadInt32Instr(r2)
    //     0xbd1bcc: sbfx            x3, x2, #1, #0x1f
    // 0xbd1bd0: sub             x2, x3, x1
    // 0xbd1bd4: cmp             x2, #1
    // 0xbd1bd8: b.ge            #0xbd1be8
    // 0xbd1bdc: mov             x1, x0
    // 0xbd1be0: r2 = 1
    //     0xbd1be0: movz            x2, #0x1
    // 0xbd1be4: r0 = _increaseBufferSize()
    //     0xbd1be4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1be8: ldur            x3, [fp, #-8]
    // 0xbd1bec: r2 = 8
    //     0xbd1bec: movz            x2, #0x8
    // 0xbd1bf0: LoadField: r4 = r3->field_b
    //     0xbd1bf0: ldur            w4, [x3, #0xb]
    // 0xbd1bf4: DecompressPointer r4
    //     0xbd1bf4: add             x4, x4, HEAP, lsl #32
    // 0xbd1bf8: LoadField: r5 = r3->field_13
    //     0xbd1bf8: ldur            x5, [x3, #0x13]
    // 0xbd1bfc: add             x6, x5, #1
    // 0xbd1c00: StoreField: r3->field_13 = r6
    //     0xbd1c00: stur            x6, [x3, #0x13]
    // 0xbd1c04: LoadField: r0 = r4->field_13
    //     0xbd1c04: ldur            w0, [x4, #0x13]
    // 0xbd1c08: r7 = LoadInt32Instr(r0)
    //     0xbd1c08: sbfx            x7, x0, #1, #0x1f
    // 0xbd1c0c: mov             x0, x7
    // 0xbd1c10: mov             x1, x5
    // 0xbd1c14: cmp             x1, x0
    // 0xbd1c18: b.hs            #0xbd20c0
    // 0xbd1c1c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd1c1c: add             x0, x4, x5
    //     0xbd1c20: strb            w2, [x0, #0x17]
    // 0xbd1c24: sub             x0, x7, x6
    // 0xbd1c28: cmp             x0, #1
    // 0xbd1c2c: b.ge            #0xbd1c3c
    // 0xbd1c30: mov             x1, x3
    // 0xbd1c34: r2 = 1
    //     0xbd1c34: movz            x2, #0x1
    // 0xbd1c38: r0 = _increaseBufferSize()
    //     0xbd1c38: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1c3c: ldur            x2, [fp, #-8]
    // 0xbd1c40: ldur            x3, [fp, #-0x10]
    // 0xbd1c44: LoadField: r4 = r2->field_b
    //     0xbd1c44: ldur            w4, [x2, #0xb]
    // 0xbd1c48: DecompressPointer r4
    //     0xbd1c48: add             x4, x4, HEAP, lsl #32
    // 0xbd1c4c: LoadField: r5 = r2->field_13
    //     0xbd1c4c: ldur            x5, [x2, #0x13]
    // 0xbd1c50: add             x0, x5, #1
    // 0xbd1c54: StoreField: r2->field_13 = r0
    //     0xbd1c54: stur            x0, [x2, #0x13]
    // 0xbd1c58: LoadField: r0 = r4->field_13
    //     0xbd1c58: ldur            w0, [x4, #0x13]
    // 0xbd1c5c: r1 = LoadInt32Instr(r0)
    //     0xbd1c5c: sbfx            x1, x0, #1, #0x1f
    // 0xbd1c60: mov             x0, x1
    // 0xbd1c64: mov             x1, x5
    // 0xbd1c68: cmp             x1, x0
    // 0xbd1c6c: b.hs            #0xbd20c4
    // 0xbd1c70: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd1c70: add             x0, x4, x5
    //     0xbd1c74: strb            wzr, [x0, #0x17]
    // 0xbd1c78: LoadField: r4 = r3->field_7
    //     0xbd1c78: ldur            x4, [x3, #7]
    // 0xbd1c7c: r0 = BoxInt64Instr(r4)
    //     0xbd1c7c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd1c80: cmp             x4, x0, asr #1
    //     0xbd1c84: b.eq            #0xbd1c90
    //     0xbd1c88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd1c8c: stur            x4, [x0, #7]
    // 0xbd1c90: r16 = <int>
    //     0xbd1c90: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd1c94: stp             x2, x16, [SP, #8]
    // 0xbd1c98: str             x0, [SP]
    // 0xbd1c9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1c9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1ca0: r0 = write()
    //     0xbd1ca0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1ca4: ldur            x0, [fp, #-8]
    // 0xbd1ca8: LoadField: r1 = r0->field_b
    //     0xbd1ca8: ldur            w1, [x0, #0xb]
    // 0xbd1cac: DecompressPointer r1
    //     0xbd1cac: add             x1, x1, HEAP, lsl #32
    // 0xbd1cb0: LoadField: r2 = r1->field_13
    //     0xbd1cb0: ldur            w2, [x1, #0x13]
    // 0xbd1cb4: LoadField: r1 = r0->field_13
    //     0xbd1cb4: ldur            x1, [x0, #0x13]
    // 0xbd1cb8: r3 = LoadInt32Instr(r2)
    //     0xbd1cb8: sbfx            x3, x2, #1, #0x1f
    // 0xbd1cbc: sub             x2, x3, x1
    // 0xbd1cc0: cmp             x2, #1
    // 0xbd1cc4: b.ge            #0xbd1cd4
    // 0xbd1cc8: mov             x1, x0
    // 0xbd1ccc: r2 = 1
    //     0xbd1ccc: movz            x2, #0x1
    // 0xbd1cd0: r0 = _increaseBufferSize()
    //     0xbd1cd0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1cd4: ldur            x2, [fp, #-8]
    // 0xbd1cd8: ldur            x3, [fp, #-0x10]
    // 0xbd1cdc: r4 = 1
    //     0xbd1cdc: movz            x4, #0x1
    // 0xbd1ce0: LoadField: r5 = r2->field_b
    //     0xbd1ce0: ldur            w5, [x2, #0xb]
    // 0xbd1ce4: DecompressPointer r5
    //     0xbd1ce4: add             x5, x5, HEAP, lsl #32
    // 0xbd1ce8: LoadField: r6 = r2->field_13
    //     0xbd1ce8: ldur            x6, [x2, #0x13]
    // 0xbd1cec: add             x0, x6, #1
    // 0xbd1cf0: StoreField: r2->field_13 = r0
    //     0xbd1cf0: stur            x0, [x2, #0x13]
    // 0xbd1cf4: LoadField: r0 = r5->field_13
    //     0xbd1cf4: ldur            w0, [x5, #0x13]
    // 0xbd1cf8: r1 = LoadInt32Instr(r0)
    //     0xbd1cf8: sbfx            x1, x0, #1, #0x1f
    // 0xbd1cfc: mov             x0, x1
    // 0xbd1d00: mov             x1, x6
    // 0xbd1d04: cmp             x1, x0
    // 0xbd1d08: b.hs            #0xbd20c8
    // 0xbd1d0c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1d0c: add             x0, x5, x6
    //     0xbd1d10: strb            w4, [x0, #0x17]
    // 0xbd1d14: LoadField: r5 = r3->field_f
    //     0xbd1d14: ldur            x5, [x3, #0xf]
    // 0xbd1d18: r0 = BoxInt64Instr(r5)
    //     0xbd1d18: sbfiz           x0, x5, #1, #0x1f
    //     0xbd1d1c: cmp             x5, x0, asr #1
    //     0xbd1d20: b.eq            #0xbd1d2c
    //     0xbd1d24: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd1d28: stur            x5, [x0, #7]
    // 0xbd1d2c: r16 = <int>
    //     0xbd1d2c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd1d30: stp             x2, x16, [SP, #8]
    // 0xbd1d34: str             x0, [SP]
    // 0xbd1d38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1d38: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1d3c: r0 = write()
    //     0xbd1d3c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1d40: ldur            x0, [fp, #-8]
    // 0xbd1d44: LoadField: r1 = r0->field_b
    //     0xbd1d44: ldur            w1, [x0, #0xb]
    // 0xbd1d48: DecompressPointer r1
    //     0xbd1d48: add             x1, x1, HEAP, lsl #32
    // 0xbd1d4c: LoadField: r2 = r1->field_13
    //     0xbd1d4c: ldur            w2, [x1, #0x13]
    // 0xbd1d50: LoadField: r1 = r0->field_13
    //     0xbd1d50: ldur            x1, [x0, #0x13]
    // 0xbd1d54: r3 = LoadInt32Instr(r2)
    //     0xbd1d54: sbfx            x3, x2, #1, #0x1f
    // 0xbd1d58: sub             x2, x3, x1
    // 0xbd1d5c: cmp             x2, #1
    // 0xbd1d60: b.ge            #0xbd1d70
    // 0xbd1d64: mov             x1, x0
    // 0xbd1d68: r2 = 1
    //     0xbd1d68: movz            x2, #0x1
    // 0xbd1d6c: r0 = _increaseBufferSize()
    //     0xbd1d6c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1d70: ldur            x2, [fp, #-8]
    // 0xbd1d74: ldur            x3, [fp, #-0x10]
    // 0xbd1d78: r4 = 2
    //     0xbd1d78: movz            x4, #0x2
    // 0xbd1d7c: LoadField: r5 = r2->field_b
    //     0xbd1d7c: ldur            w5, [x2, #0xb]
    // 0xbd1d80: DecompressPointer r5
    //     0xbd1d80: add             x5, x5, HEAP, lsl #32
    // 0xbd1d84: LoadField: r6 = r2->field_13
    //     0xbd1d84: ldur            x6, [x2, #0x13]
    // 0xbd1d88: add             x0, x6, #1
    // 0xbd1d8c: StoreField: r2->field_13 = r0
    //     0xbd1d8c: stur            x0, [x2, #0x13]
    // 0xbd1d90: LoadField: r0 = r5->field_13
    //     0xbd1d90: ldur            w0, [x5, #0x13]
    // 0xbd1d94: r1 = LoadInt32Instr(r0)
    //     0xbd1d94: sbfx            x1, x0, #1, #0x1f
    // 0xbd1d98: mov             x0, x1
    // 0xbd1d9c: mov             x1, x6
    // 0xbd1da0: cmp             x1, x0
    // 0xbd1da4: b.hs            #0xbd20cc
    // 0xbd1da8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1da8: add             x0, x5, x6
    //     0xbd1dac: strb            w4, [x0, #0x17]
    // 0xbd1db0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd1db0: ldur            x4, [x3, #0x17]
    // 0xbd1db4: r0 = BoxInt64Instr(r4)
    //     0xbd1db4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd1db8: cmp             x4, x0, asr #1
    //     0xbd1dbc: b.eq            #0xbd1dc8
    //     0xbd1dc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd1dc4: stur            x4, [x0, #7]
    // 0xbd1dc8: r16 = <int>
    //     0xbd1dc8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd1dcc: stp             x2, x16, [SP, #8]
    // 0xbd1dd0: str             x0, [SP]
    // 0xbd1dd4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1dd4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1dd8: r0 = write()
    //     0xbd1dd8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1ddc: ldur            x0, [fp, #-8]
    // 0xbd1de0: LoadField: r1 = r0->field_b
    //     0xbd1de0: ldur            w1, [x0, #0xb]
    // 0xbd1de4: DecompressPointer r1
    //     0xbd1de4: add             x1, x1, HEAP, lsl #32
    // 0xbd1de8: LoadField: r2 = r1->field_13
    //     0xbd1de8: ldur            w2, [x1, #0x13]
    // 0xbd1dec: LoadField: r1 = r0->field_13
    //     0xbd1dec: ldur            x1, [x0, #0x13]
    // 0xbd1df0: r3 = LoadInt32Instr(r2)
    //     0xbd1df0: sbfx            x3, x2, #1, #0x1f
    // 0xbd1df4: sub             x2, x3, x1
    // 0xbd1df8: cmp             x2, #1
    // 0xbd1dfc: b.ge            #0xbd1e0c
    // 0xbd1e00: mov             x1, x0
    // 0xbd1e04: r2 = 1
    //     0xbd1e04: movz            x2, #0x1
    // 0xbd1e08: r0 = _increaseBufferSize()
    //     0xbd1e08: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1e0c: ldur            x2, [fp, #-8]
    // 0xbd1e10: ldur            x3, [fp, #-0x10]
    // 0xbd1e14: r4 = 3
    //     0xbd1e14: movz            x4, #0x3
    // 0xbd1e18: LoadField: r5 = r2->field_b
    //     0xbd1e18: ldur            w5, [x2, #0xb]
    // 0xbd1e1c: DecompressPointer r5
    //     0xbd1e1c: add             x5, x5, HEAP, lsl #32
    // 0xbd1e20: LoadField: r6 = r2->field_13
    //     0xbd1e20: ldur            x6, [x2, #0x13]
    // 0xbd1e24: add             x0, x6, #1
    // 0xbd1e28: StoreField: r2->field_13 = r0
    //     0xbd1e28: stur            x0, [x2, #0x13]
    // 0xbd1e2c: LoadField: r0 = r5->field_13
    //     0xbd1e2c: ldur            w0, [x5, #0x13]
    // 0xbd1e30: r1 = LoadInt32Instr(r0)
    //     0xbd1e30: sbfx            x1, x0, #1, #0x1f
    // 0xbd1e34: mov             x0, x1
    // 0xbd1e38: mov             x1, x6
    // 0xbd1e3c: cmp             x1, x0
    // 0xbd1e40: b.hs            #0xbd20d0
    // 0xbd1e44: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1e44: add             x0, x5, x6
    //     0xbd1e48: strb            w4, [x0, #0x17]
    // 0xbd1e4c: LoadField: r0 = r3->field_1f
    //     0xbd1e4c: ldur            w0, [x3, #0x1f]
    // 0xbd1e50: DecompressPointer r0
    //     0xbd1e50: add             x0, x0, HEAP, lsl #32
    // 0xbd1e54: r16 = <String>
    //     0xbd1e54: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd1e58: stp             x2, x16, [SP, #8]
    // 0xbd1e5c: str             x0, [SP]
    // 0xbd1e60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1e60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1e64: r0 = write()
    //     0xbd1e64: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1e68: ldur            x0, [fp, #-8]
    // 0xbd1e6c: LoadField: r1 = r0->field_b
    //     0xbd1e6c: ldur            w1, [x0, #0xb]
    // 0xbd1e70: DecompressPointer r1
    //     0xbd1e70: add             x1, x1, HEAP, lsl #32
    // 0xbd1e74: LoadField: r2 = r1->field_13
    //     0xbd1e74: ldur            w2, [x1, #0x13]
    // 0xbd1e78: LoadField: r1 = r0->field_13
    //     0xbd1e78: ldur            x1, [x0, #0x13]
    // 0xbd1e7c: r3 = LoadInt32Instr(r2)
    //     0xbd1e7c: sbfx            x3, x2, #1, #0x1f
    // 0xbd1e80: sub             x2, x3, x1
    // 0xbd1e84: cmp             x2, #1
    // 0xbd1e88: b.ge            #0xbd1e98
    // 0xbd1e8c: mov             x1, x0
    // 0xbd1e90: r2 = 1
    //     0xbd1e90: movz            x2, #0x1
    // 0xbd1e94: r0 = _increaseBufferSize()
    //     0xbd1e94: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1e98: ldur            x2, [fp, #-8]
    // 0xbd1e9c: ldur            x3, [fp, #-0x10]
    // 0xbd1ea0: r4 = 4
    //     0xbd1ea0: movz            x4, #0x4
    // 0xbd1ea4: LoadField: r5 = r2->field_b
    //     0xbd1ea4: ldur            w5, [x2, #0xb]
    // 0xbd1ea8: DecompressPointer r5
    //     0xbd1ea8: add             x5, x5, HEAP, lsl #32
    // 0xbd1eac: LoadField: r6 = r2->field_13
    //     0xbd1eac: ldur            x6, [x2, #0x13]
    // 0xbd1eb0: add             x0, x6, #1
    // 0xbd1eb4: StoreField: r2->field_13 = r0
    //     0xbd1eb4: stur            x0, [x2, #0x13]
    // 0xbd1eb8: LoadField: r0 = r5->field_13
    //     0xbd1eb8: ldur            w0, [x5, #0x13]
    // 0xbd1ebc: r1 = LoadInt32Instr(r0)
    //     0xbd1ebc: sbfx            x1, x0, #1, #0x1f
    // 0xbd1ec0: mov             x0, x1
    // 0xbd1ec4: mov             x1, x6
    // 0xbd1ec8: cmp             x1, x0
    // 0xbd1ecc: b.hs            #0xbd20d4
    // 0xbd1ed0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1ed0: add             x0, x5, x6
    //     0xbd1ed4: strb            w4, [x0, #0x17]
    // 0xbd1ed8: LoadField: r0 = r3->field_23
    //     0xbd1ed8: ldur            w0, [x3, #0x23]
    // 0xbd1edc: DecompressPointer r0
    //     0xbd1edc: add             x0, x0, HEAP, lsl #32
    // 0xbd1ee0: r16 = <String?>
    //     0xbd1ee0: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd1ee4: stp             x2, x16, [SP, #8]
    // 0xbd1ee8: str             x0, [SP]
    // 0xbd1eec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1eec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1ef0: r0 = write()
    //     0xbd1ef0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1ef4: ldur            x0, [fp, #-8]
    // 0xbd1ef8: LoadField: r1 = r0->field_b
    //     0xbd1ef8: ldur            w1, [x0, #0xb]
    // 0xbd1efc: DecompressPointer r1
    //     0xbd1efc: add             x1, x1, HEAP, lsl #32
    // 0xbd1f00: LoadField: r2 = r1->field_13
    //     0xbd1f00: ldur            w2, [x1, #0x13]
    // 0xbd1f04: LoadField: r1 = r0->field_13
    //     0xbd1f04: ldur            x1, [x0, #0x13]
    // 0xbd1f08: r3 = LoadInt32Instr(r2)
    //     0xbd1f08: sbfx            x3, x2, #1, #0x1f
    // 0xbd1f0c: sub             x2, x3, x1
    // 0xbd1f10: cmp             x2, #1
    // 0xbd1f14: b.ge            #0xbd1f24
    // 0xbd1f18: mov             x1, x0
    // 0xbd1f1c: r2 = 1
    //     0xbd1f1c: movz            x2, #0x1
    // 0xbd1f20: r0 = _increaseBufferSize()
    //     0xbd1f20: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1f24: ldur            x2, [fp, #-8]
    // 0xbd1f28: ldur            x3, [fp, #-0x10]
    // 0xbd1f2c: r4 = 5
    //     0xbd1f2c: movz            x4, #0x5
    // 0xbd1f30: LoadField: r5 = r2->field_b
    //     0xbd1f30: ldur            w5, [x2, #0xb]
    // 0xbd1f34: DecompressPointer r5
    //     0xbd1f34: add             x5, x5, HEAP, lsl #32
    // 0xbd1f38: LoadField: r6 = r2->field_13
    //     0xbd1f38: ldur            x6, [x2, #0x13]
    // 0xbd1f3c: add             x0, x6, #1
    // 0xbd1f40: StoreField: r2->field_13 = r0
    //     0xbd1f40: stur            x0, [x2, #0x13]
    // 0xbd1f44: LoadField: r0 = r5->field_13
    //     0xbd1f44: ldur            w0, [x5, #0x13]
    // 0xbd1f48: r1 = LoadInt32Instr(r0)
    //     0xbd1f48: sbfx            x1, x0, #1, #0x1f
    // 0xbd1f4c: mov             x0, x1
    // 0xbd1f50: mov             x1, x6
    // 0xbd1f54: cmp             x1, x0
    // 0xbd1f58: b.hs            #0xbd20d8
    // 0xbd1f5c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1f5c: add             x0, x5, x6
    //     0xbd1f60: strb            w4, [x0, #0x17]
    // 0xbd1f64: LoadField: r0 = r3->field_27
    //     0xbd1f64: ldur            w0, [x3, #0x27]
    // 0xbd1f68: DecompressPointer r0
    //     0xbd1f68: add             x0, x0, HEAP, lsl #32
    // 0xbd1f6c: r16 = <String?>
    //     0xbd1f6c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd1f70: stp             x2, x16, [SP, #8]
    // 0xbd1f74: str             x0, [SP]
    // 0xbd1f78: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd1f78: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd1f7c: r0 = write()
    //     0xbd1f7c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd1f80: ldur            x0, [fp, #-8]
    // 0xbd1f84: LoadField: r1 = r0->field_b
    //     0xbd1f84: ldur            w1, [x0, #0xb]
    // 0xbd1f88: DecompressPointer r1
    //     0xbd1f88: add             x1, x1, HEAP, lsl #32
    // 0xbd1f8c: LoadField: r2 = r1->field_13
    //     0xbd1f8c: ldur            w2, [x1, #0x13]
    // 0xbd1f90: LoadField: r1 = r0->field_13
    //     0xbd1f90: ldur            x1, [x0, #0x13]
    // 0xbd1f94: r3 = LoadInt32Instr(r2)
    //     0xbd1f94: sbfx            x3, x2, #1, #0x1f
    // 0xbd1f98: sub             x2, x3, x1
    // 0xbd1f9c: cmp             x2, #1
    // 0xbd1fa0: b.ge            #0xbd1fb0
    // 0xbd1fa4: mov             x1, x0
    // 0xbd1fa8: r2 = 1
    //     0xbd1fa8: movz            x2, #0x1
    // 0xbd1fac: r0 = _increaseBufferSize()
    //     0xbd1fac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd1fb0: ldur            x2, [fp, #-8]
    // 0xbd1fb4: ldur            x3, [fp, #-0x10]
    // 0xbd1fb8: r4 = 6
    //     0xbd1fb8: movz            x4, #0x6
    // 0xbd1fbc: LoadField: r5 = r2->field_b
    //     0xbd1fbc: ldur            w5, [x2, #0xb]
    // 0xbd1fc0: DecompressPointer r5
    //     0xbd1fc0: add             x5, x5, HEAP, lsl #32
    // 0xbd1fc4: LoadField: r6 = r2->field_13
    //     0xbd1fc4: ldur            x6, [x2, #0x13]
    // 0xbd1fc8: add             x0, x6, #1
    // 0xbd1fcc: StoreField: r2->field_13 = r0
    //     0xbd1fcc: stur            x0, [x2, #0x13]
    // 0xbd1fd0: LoadField: r0 = r5->field_13
    //     0xbd1fd0: ldur            w0, [x5, #0x13]
    // 0xbd1fd4: r1 = LoadInt32Instr(r0)
    //     0xbd1fd4: sbfx            x1, x0, #1, #0x1f
    // 0xbd1fd8: mov             x0, x1
    // 0xbd1fdc: mov             x1, x6
    // 0xbd1fe0: cmp             x1, x0
    // 0xbd1fe4: b.hs            #0xbd20dc
    // 0xbd1fe8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd1fe8: add             x0, x5, x6
    //     0xbd1fec: strb            w4, [x0, #0x17]
    // 0xbd1ff0: LoadField: r4 = r3->field_2b
    //     0xbd1ff0: ldur            x4, [x3, #0x2b]
    // 0xbd1ff4: r0 = BoxInt64Instr(r4)
    //     0xbd1ff4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd1ff8: cmp             x4, x0, asr #1
    //     0xbd1ffc: b.eq            #0xbd2008
    //     0xbd2000: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd2004: stur            x4, [x0, #7]
    // 0xbd2008: r16 = <int>
    //     0xbd2008: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd200c: stp             x2, x16, [SP, #8]
    // 0xbd2010: str             x0, [SP]
    // 0xbd2014: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2014: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2018: r0 = write()
    //     0xbd2018: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd201c: ldur            x0, [fp, #-8]
    // 0xbd2020: LoadField: r1 = r0->field_b
    //     0xbd2020: ldur            w1, [x0, #0xb]
    // 0xbd2024: DecompressPointer r1
    //     0xbd2024: add             x1, x1, HEAP, lsl #32
    // 0xbd2028: LoadField: r2 = r1->field_13
    //     0xbd2028: ldur            w2, [x1, #0x13]
    // 0xbd202c: LoadField: r1 = r0->field_13
    //     0xbd202c: ldur            x1, [x0, #0x13]
    // 0xbd2030: r3 = LoadInt32Instr(r2)
    //     0xbd2030: sbfx            x3, x2, #1, #0x1f
    // 0xbd2034: sub             x2, x3, x1
    // 0xbd2038: cmp             x2, #1
    // 0xbd203c: b.ge            #0xbd204c
    // 0xbd2040: mov             x1, x0
    // 0xbd2044: r2 = 1
    //     0xbd2044: movz            x2, #0x1
    // 0xbd2048: r0 = _increaseBufferSize()
    //     0xbd2048: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd204c: ldur            x2, [fp, #-8]
    // 0xbd2050: ldur            x3, [fp, #-0x10]
    // 0xbd2054: r4 = 7
    //     0xbd2054: movz            x4, #0x7
    // 0xbd2058: LoadField: r5 = r2->field_b
    //     0xbd2058: ldur            w5, [x2, #0xb]
    // 0xbd205c: DecompressPointer r5
    //     0xbd205c: add             x5, x5, HEAP, lsl #32
    // 0xbd2060: LoadField: r6 = r2->field_13
    //     0xbd2060: ldur            x6, [x2, #0x13]
    // 0xbd2064: add             x0, x6, #1
    // 0xbd2068: StoreField: r2->field_13 = r0
    //     0xbd2068: stur            x0, [x2, #0x13]
    // 0xbd206c: LoadField: r0 = r5->field_13
    //     0xbd206c: ldur            w0, [x5, #0x13]
    // 0xbd2070: r1 = LoadInt32Instr(r0)
    //     0xbd2070: sbfx            x1, x0, #1, #0x1f
    // 0xbd2074: mov             x0, x1
    // 0xbd2078: mov             x1, x6
    // 0xbd207c: cmp             x1, x0
    // 0xbd2080: b.hs            #0xbd20e0
    // 0xbd2084: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2084: add             x0, x5, x6
    //     0xbd2088: strb            w4, [x0, #0x17]
    // 0xbd208c: LoadField: r0 = r3->field_33
    //     0xbd208c: ldur            w0, [x3, #0x33]
    // 0xbd2090: DecompressPointer r0
    //     0xbd2090: add             x0, x0, HEAP, lsl #32
    // 0xbd2094: r16 = <String>
    //     0xbd2094: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd2098: stp             x2, x16, [SP, #8]
    // 0xbd209c: str             x0, [SP]
    // 0xbd20a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd20a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd20a4: r0 = write()
    //     0xbd20a4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd20a8: r0 = Null
    //     0xbd20a8: mov             x0, NULL
    // 0xbd20ac: LeaveFrame
    //     0xbd20ac: mov             SP, fp
    //     0xbd20b0: ldp             fp, lr, [SP], #0x10
    // 0xbd20b4: ret
    //     0xbd20b4: ret             
    // 0xbd20b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd20b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd20bc: b               #0xbd1b7c
    // 0xbd20c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd20e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd20e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf00d8, size: 0x24
    // 0xbf00d8: r1 = 120
    //     0xbf00d8: movz            x1, #0x78
    // 0xbf00dc: r16 = LoadInt32Instr(r1)
    //     0xbf00dc: sbfx            x16, x1, #1, #0x1f
    // 0xbf00e0: r17 = 11601
    //     0xbf00e0: movz            x17, #0x2d51
    // 0xbf00e4: mul             x0, x16, x17
    // 0xbf00e8: umulh           x16, x16, x17
    // 0xbf00ec: eor             x0, x0, x16
    // 0xbf00f0: r0 = 0
    //     0xbf00f0: eor             x0, x0, x0, lsr #32
    // 0xbf00f4: ubfiz           x0, x0, #1, #0x1e
    // 0xbf00f8: ret
    //     0xbf00f8: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7641c, size: 0x9c
    // 0xd7641c: EnterFrame
    //     0xd7641c: stp             fp, lr, [SP, #-0x10]!
    //     0xd76420: mov             fp, SP
    // 0xd76424: AllocStack(0x10)
    //     0xd76424: sub             SP, SP, #0x10
    // 0xd76428: CheckStackOverflow
    //     0xd76428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7642c: cmp             SP, x16
    //     0xd76430: b.ls            #0xd764b0
    // 0xd76434: ldr             x0, [fp, #0x10]
    // 0xd76438: cmp             w0, NULL
    // 0xd7643c: b.ne            #0xd76450
    // 0xd76440: r0 = false
    //     0xd76440: add             x0, NULL, #0x30  ; false
    // 0xd76444: LeaveFrame
    //     0xd76444: mov             SP, fp
    //     0xd76448: ldp             fp, lr, [SP], #0x10
    // 0xd7644c: ret
    //     0xd7644c: ret             
    // 0xd76450: ldr             x1, [fp, #0x18]
    // 0xd76454: cmp             w1, w0
    // 0xd76458: b.ne            #0xd76464
    // 0xd7645c: r0 = true
    //     0xd7645c: add             x0, NULL, #0x20  ; true
    // 0xd76460: b               #0xd764a4
    // 0xd76464: r1 = 60
    //     0xd76464: movz            x1, #0x3c
    // 0xd76468: branchIfSmi(r0, 0xd76474)
    //     0xd76468: tbz             w0, #0, #0xd76474
    // 0xd7646c: r1 = LoadClassIdInstr(r0)
    //     0xd7646c: ldur            x1, [x0, #-1]
    //     0xd76470: ubfx            x1, x1, #0xc, #0x14
    // 0xd76474: cmp             x1, #0x680
    // 0xd76478: b.ne            #0xd764a0
    // 0xd7647c: r16 = DoaAdapter
    //     0xd7647c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b3d0] Type: DoaAdapter
    //     0xd76480: ldr             x16, [x16, #0x3d0]
    // 0xd76484: r30 = DoaAdapter
    //     0xd76484: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b3d0] Type: DoaAdapter
    //     0xd76488: ldr             lr, [lr, #0x3d0]
    // 0xd7648c: stp             lr, x16, [SP]
    // 0xd76490: r0 = ==()
    //     0xd76490: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76494: tbnz            w0, #4, #0xd764a0
    // 0xd76498: r0 = true
    //     0xd76498: add             x0, NULL, #0x20  ; true
    // 0xd7649c: b               #0xd764a4
    // 0xd764a0: r0 = false
    //     0xd764a0: add             x0, NULL, #0x30  ; false
    // 0xd764a4: LeaveFrame
    //     0xd764a4: mov             SP, fp
    //     0xd764a8: ldp             fp, lr, [SP], #0x10
    // 0xd764ac: ret
    //     0xd764ac: ret             
    // 0xd764b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd764b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd764b4: b               #0xd76434
  }
}

// class id: 5591, size: 0x28, field offset: 0x8
//   const constructor, 
class DoaRelated extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0xe779fc, size: 0x188
    // 0xe779fc: EnterFrame
    //     0xe779fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe77a00: mov             fp, SP
    // 0xe77a04: AllocStack(0x20)
    //     0xe77a04: sub             SP, SP, #0x20
    // 0xe77a08: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe77a08: mov             x3, x1
    //     0xe77a0c: stur            x1, [fp, #-8]
    // 0xe77a10: CheckStackOverflow
    //     0xe77a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77a14: cmp             SP, x16
    //     0xe77a18: b.ls            #0xe77b7c
    // 0xe77a1c: mov             x0, x3
    // 0xe77a20: r2 = Null
    //     0xe77a20: mov             x2, NULL
    // 0xe77a24: r1 = Null
    //     0xe77a24: mov             x1, NULL
    // 0xe77a28: cmp             w0, NULL
    // 0xe77a2c: b.eq            #0xe77ad0
    // 0xe77a30: branchIfSmi(r0, 0xe77ad0)
    //     0xe77a30: tbz             w0, #0, #0xe77ad0
    // 0xe77a34: r3 = LoadClassIdInstr(r0)
    //     0xe77a34: ldur            x3, [x0, #-1]
    //     0xe77a38: ubfx            x3, x3, #0xc, #0x14
    // 0xe77a3c: r17 = 6718
    //     0xe77a3c: movz            x17, #0x1a3e
    // 0xe77a40: cmp             x3, x17
    // 0xe77a44: b.eq            #0xe77ad8
    // 0xe77a48: sub             x3, x3, #0x5a
    // 0xe77a4c: cmp             x3, #2
    // 0xe77a50: b.ls            #0xe77ad8
    // 0xe77a54: r4 = LoadClassIdInstr(r0)
    //     0xe77a54: ldur            x4, [x0, #-1]
    //     0xe77a58: ubfx            x4, x4, #0xc, #0x14
    // 0xe77a5c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe77a60: ldr             x3, [x3, #0x18]
    // 0xe77a64: ldr             x3, [x3, x4, lsl #3]
    // 0xe77a68: LoadField: r3 = r3->field_2b
    //     0xe77a68: ldur            w3, [x3, #0x2b]
    // 0xe77a6c: DecompressPointer r3
    //     0xe77a6c: add             x3, x3, HEAP, lsl #32
    // 0xe77a70: cmp             w3, NULL
    // 0xe77a74: b.eq            #0xe77ad0
    // 0xe77a78: LoadField: r3 = r3->field_f
    //     0xe77a78: ldur            w3, [x3, #0xf]
    // 0xe77a7c: lsr             x3, x3, #3
    // 0xe77a80: r17 = 6718
    //     0xe77a80: movz            x17, #0x1a3e
    // 0xe77a84: cmp             x3, x17
    // 0xe77a88: b.eq            #0xe77ad8
    // 0xe77a8c: r3 = SubtypeTestCache
    //     0xe77a8c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a240] SubtypeTestCache
    //     0xe77a90: ldr             x3, [x3, #0x240]
    // 0xe77a94: r30 = Subtype1TestCacheStub
    //     0xe77a94: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe77a98: LoadField: r30 = r30->field_7
    //     0xe77a98: ldur            lr, [lr, #7]
    // 0xe77a9c: blr             lr
    // 0xe77aa0: cmp             w7, NULL
    // 0xe77aa4: b.eq            #0xe77ab0
    // 0xe77aa8: tbnz            w7, #4, #0xe77ad0
    // 0xe77aac: b               #0xe77ad8
    // 0xe77ab0: r8 = List
    //     0xe77ab0: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a248] Type: List
    //     0xe77ab4: ldr             x8, [x8, #0x248]
    // 0xe77ab8: r3 = SubtypeTestCache
    //     0xe77ab8: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a250] SubtypeTestCache
    //     0xe77abc: ldr             x3, [x3, #0x250]
    // 0xe77ac0: r30 = InstanceOfStub
    //     0xe77ac0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe77ac4: LoadField: r30 = r30->field_7
    //     0xe77ac4: ldur            lr, [lr, #7]
    // 0xe77ac8: blr             lr
    // 0xe77acc: b               #0xe77adc
    // 0xe77ad0: r0 = false
    //     0xe77ad0: add             x0, NULL, #0x30  ; false
    // 0xe77ad4: b               #0xe77adc
    // 0xe77ad8: r0 = true
    //     0xe77ad8: add             x0, NULL, #0x20  ; true
    // 0xe77adc: tbnz            w0, #4, #0xe77b60
    // 0xe77ae0: ldur            x0, [fp, #-8]
    // 0xe77ae4: r1 = Function '<anonymous closure>': static.
    //     0xe77ae4: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a258] AnonymousClosure: static (0xe77b84), in [package:nuonline/app/data/models/doa.dart] DoaRelated::fromResponse (0xe779fc)
    //     0xe77ae8: ldr             x1, [x1, #0x258]
    // 0xe77aec: r2 = Null
    //     0xe77aec: mov             x2, NULL
    // 0xe77af0: r0 = AllocateClosure()
    //     0xe77af0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe77af4: mov             x1, x0
    // 0xe77af8: ldur            x0, [fp, #-8]
    // 0xe77afc: r2 = LoadClassIdInstr(r0)
    //     0xe77afc: ldur            x2, [x0, #-1]
    //     0xe77b00: ubfx            x2, x2, #0xc, #0x14
    // 0xe77b04: r16 = <DoaRelated>
    //     0xe77b04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] TypeArguments: <DoaRelated>
    //     0xe77b08: ldr             x16, [x16, #0x8e8]
    // 0xe77b0c: stp             x0, x16, [SP, #8]
    // 0xe77b10: str             x1, [SP]
    // 0xe77b14: mov             x0, x2
    // 0xe77b18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe77b18: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77b1c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe77b1c: movz            x17, #0xf28c
    //     0xe77b20: add             lr, x0, x17
    //     0xe77b24: ldr             lr, [x21, lr, lsl #3]
    //     0xe77b28: blr             lr
    // 0xe77b2c: r1 = LoadClassIdInstr(r0)
    //     0xe77b2c: ldur            x1, [x0, #-1]
    //     0xe77b30: ubfx            x1, x1, #0xc, #0x14
    // 0xe77b34: mov             x16, x0
    // 0xe77b38: mov             x0, x1
    // 0xe77b3c: mov             x1, x16
    // 0xe77b40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe77b40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe77b44: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe77b44: movz            x17, #0xd889
    //     0xe77b48: add             lr, x0, x17
    //     0xe77b4c: ldr             lr, [x21, lr, lsl #3]
    //     0xe77b50: blr             lr
    // 0xe77b54: LeaveFrame
    //     0xe77b54: mov             SP, fp
    //     0xe77b58: ldp             fp, lr, [SP], #0x10
    // 0xe77b5c: ret
    //     0xe77b5c: ret             
    // 0xe77b60: r1 = <DoaRelated>
    //     0xe77b60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] TypeArguments: <DoaRelated>
    //     0xe77b64: ldr             x1, [x1, #0x8e8]
    // 0xe77b68: r2 = 0
    //     0xe77b68: movz            x2, #0
    // 0xe77b6c: r0 = _GrowableList()
    //     0xe77b6c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe77b70: LeaveFrame
    //     0xe77b70: mov             SP, fp
    //     0xe77b74: ldp             fp, lr, [SP], #0x10
    // 0xe77b78: ret
    //     0xe77b78: ret             
    // 0xe77b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77b80: b               #0xe77a1c
  }
  [closure] static DoaRelated <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe77b84, size: 0x50
    // 0xe77b84: EnterFrame
    //     0xe77b84: stp             fp, lr, [SP, #-0x10]!
    //     0xe77b88: mov             fp, SP
    // 0xe77b8c: CheckStackOverflow
    //     0xe77b8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77b90: cmp             SP, x16
    //     0xe77b94: b.ls            #0xe77bcc
    // 0xe77b98: ldr             x0, [fp, #0x10]
    // 0xe77b9c: r2 = Null
    //     0xe77b9c: mov             x2, NULL
    // 0xe77ba0: r1 = Null
    //     0xe77ba0: mov             x1, NULL
    // 0xe77ba4: r8 = Map<String, dynamic>
    //     0xe77ba4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe77ba8: r3 = Null
    //     0xe77ba8: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a260] Null
    //     0xe77bac: ldr             x3, [x3, #0x260]
    // 0xe77bb0: r0 = Map<String, dynamic>()
    //     0xe77bb0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe77bb4: ldr             x2, [fp, #0x10]
    // 0xe77bb8: r1 = Null
    //     0xe77bb8: mov             x1, NULL
    // 0xe77bbc: r0 = DoaRelated.fromMap()
    //     0xe77bbc: bl              #0xe77bd4  ; [package:nuonline/app/data/models/doa.dart] DoaRelated::DoaRelated.fromMap
    // 0xe77bc0: LeaveFrame
    //     0xe77bc0: mov             SP, fp
    //     0xe77bc4: ldp             fp, lr, [SP], #0x10
    // 0xe77bc8: ret
    //     0xe77bc8: ret             
    // 0xe77bcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77bcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77bd0: b               #0xe77b98
  }
  factory _ DoaRelated.fromMap(/* No info */) {
    // ** addr: 0xe77bd4, size: 0x308
    // 0xe77bd4: EnterFrame
    //     0xe77bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe77bd8: mov             fp, SP
    // 0xe77bdc: AllocStack(0x40)
    //     0xe77bdc: sub             SP, SP, #0x40
    // 0xe77be0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe77be0: mov             x3, x2
    //     0xe77be4: stur            x2, [fp, #-8]
    // 0xe77be8: CheckStackOverflow
    //     0xe77be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77bec: cmp             SP, x16
    //     0xe77bf0: b.ls            #0xe77ed4
    // 0xe77bf4: r0 = LoadClassIdInstr(r3)
    //     0xe77bf4: ldur            x0, [x3, #-1]
    //     0xe77bf8: ubfx            x0, x0, #0xc, #0x14
    // 0xe77bfc: mov             x1, x3
    // 0xe77c00: r2 = "id"
    //     0xe77c00: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe77c04: ldr             x2, [x2, #0x740]
    // 0xe77c08: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77c08: sub             lr, x0, #0x114
    //     0xe77c0c: ldr             lr, [x21, lr, lsl #3]
    //     0xe77c10: blr             lr
    // 0xe77c14: mov             x3, x0
    // 0xe77c18: r2 = Null
    //     0xe77c18: mov             x2, NULL
    // 0xe77c1c: r1 = Null
    //     0xe77c1c: mov             x1, NULL
    // 0xe77c20: stur            x3, [fp, #-0x10]
    // 0xe77c24: branchIfSmi(r0, 0xe77c4c)
    //     0xe77c24: tbz             w0, #0, #0xe77c4c
    // 0xe77c28: r4 = LoadClassIdInstr(r0)
    //     0xe77c28: ldur            x4, [x0, #-1]
    //     0xe77c2c: ubfx            x4, x4, #0xc, #0x14
    // 0xe77c30: sub             x4, x4, #0x3c
    // 0xe77c34: cmp             x4, #1
    // 0xe77c38: b.ls            #0xe77c4c
    // 0xe77c3c: r8 = int
    //     0xe77c3c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe77c40: r3 = Null
    //     0xe77c40: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a270] Null
    //     0xe77c44: ldr             x3, [x3, #0x270]
    // 0xe77c48: r0 = int()
    //     0xe77c48: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe77c4c: ldur            x3, [fp, #-8]
    // 0xe77c50: r0 = LoadClassIdInstr(r3)
    //     0xe77c50: ldur            x0, [x3, #-1]
    //     0xe77c54: ubfx            x0, x0, #0xc, #0x14
    // 0xe77c58: mov             x1, x3
    // 0xe77c5c: r2 = "post_id"
    //     0xe77c5c: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a280] "post_id"
    //     0xe77c60: ldr             x2, [x2, #0x280]
    // 0xe77c64: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77c64: sub             lr, x0, #0x114
    //     0xe77c68: ldr             lr, [x21, lr, lsl #3]
    //     0xe77c6c: blr             lr
    // 0xe77c70: mov             x3, x0
    // 0xe77c74: r2 = Null
    //     0xe77c74: mov             x2, NULL
    // 0xe77c78: r1 = Null
    //     0xe77c78: mov             x1, NULL
    // 0xe77c7c: stur            x3, [fp, #-0x18]
    // 0xe77c80: r4 = 60
    //     0xe77c80: movz            x4, #0x3c
    // 0xe77c84: branchIfSmi(r0, 0xe77c90)
    //     0xe77c84: tbz             w0, #0, #0xe77c90
    // 0xe77c88: r4 = LoadClassIdInstr(r0)
    //     0xe77c88: ldur            x4, [x0, #-1]
    //     0xe77c8c: ubfx            x4, x4, #0xc, #0x14
    // 0xe77c90: sub             x4, x4, #0x5e
    // 0xe77c94: cmp             x4, #1
    // 0xe77c98: b.ls            #0xe77cac
    // 0xe77c9c: r8 = String
    //     0xe77c9c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe77ca0: r3 = Null
    //     0xe77ca0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a288] Null
    //     0xe77ca4: ldr             x3, [x3, #0x288]
    // 0xe77ca8: r0 = String()
    //     0xe77ca8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe77cac: ldur            x3, [fp, #-8]
    // 0xe77cb0: r0 = LoadClassIdInstr(r3)
    //     0xe77cb0: ldur            x0, [x3, #-1]
    //     0xe77cb4: ubfx            x0, x0, #0xc, #0x14
    // 0xe77cb8: mov             x1, x3
    // 0xe77cbc: r2 = "title"
    //     0xe77cbc: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xe77cc0: ldr             x2, [x2, #0x748]
    // 0xe77cc4: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77cc4: sub             lr, x0, #0x114
    //     0xe77cc8: ldr             lr, [x21, lr, lsl #3]
    //     0xe77ccc: blr             lr
    // 0xe77cd0: mov             x3, x0
    // 0xe77cd4: r2 = Null
    //     0xe77cd4: mov             x2, NULL
    // 0xe77cd8: r1 = Null
    //     0xe77cd8: mov             x1, NULL
    // 0xe77cdc: stur            x3, [fp, #-0x20]
    // 0xe77ce0: r4 = 60
    //     0xe77ce0: movz            x4, #0x3c
    // 0xe77ce4: branchIfSmi(r0, 0xe77cf0)
    //     0xe77ce4: tbz             w0, #0, #0xe77cf0
    // 0xe77ce8: r4 = LoadClassIdInstr(r0)
    //     0xe77ce8: ldur            x4, [x0, #-1]
    //     0xe77cec: ubfx            x4, x4, #0xc, #0x14
    // 0xe77cf0: sub             x4, x4, #0x5e
    // 0xe77cf4: cmp             x4, #1
    // 0xe77cf8: b.ls            #0xe77d0c
    // 0xe77cfc: r8 = String
    //     0xe77cfc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe77d00: r3 = Null
    //     0xe77d00: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a298] Null
    //     0xe77d04: ldr             x3, [x3, #0x298]
    // 0xe77d08: r0 = String()
    //     0xe77d08: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe77d0c: ldur            x3, [fp, #-8]
    // 0xe77d10: r0 = LoadClassIdInstr(r3)
    //     0xe77d10: ldur            x0, [x3, #-1]
    //     0xe77d14: ubfx            x0, x0, #0xc, #0x14
    // 0xe77d18: mov             x1, x3
    // 0xe77d1c: r2 = "category"
    //     0xe77d1c: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xe77d20: ldr             x2, [x2, #0x960]
    // 0xe77d24: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77d24: sub             lr, x0, #0x114
    //     0xe77d28: ldr             lr, [x21, lr, lsl #3]
    //     0xe77d2c: blr             lr
    // 0xe77d30: mov             x3, x0
    // 0xe77d34: r2 = Null
    //     0xe77d34: mov             x2, NULL
    // 0xe77d38: r1 = Null
    //     0xe77d38: mov             x1, NULL
    // 0xe77d3c: stur            x3, [fp, #-0x28]
    // 0xe77d40: r4 = 60
    //     0xe77d40: movz            x4, #0x3c
    // 0xe77d44: branchIfSmi(r0, 0xe77d50)
    //     0xe77d44: tbz             w0, #0, #0xe77d50
    // 0xe77d48: r4 = LoadClassIdInstr(r0)
    //     0xe77d48: ldur            x4, [x0, #-1]
    //     0xe77d4c: ubfx            x4, x4, #0xc, #0x14
    // 0xe77d50: sub             x4, x4, #0x5e
    // 0xe77d54: cmp             x4, #1
    // 0xe77d58: b.ls            #0xe77d6c
    // 0xe77d5c: r8 = String
    //     0xe77d5c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe77d60: r3 = Null
    //     0xe77d60: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a2a8] Null
    //     0xe77d64: ldr             x3, [x3, #0x2a8]
    // 0xe77d68: r0 = String()
    //     0xe77d68: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe77d6c: ldur            x3, [fp, #-8]
    // 0xe77d70: r0 = LoadClassIdInstr(r3)
    //     0xe77d70: ldur            x0, [x3, #-1]
    //     0xe77d74: ubfx            x0, x0, #0xc, #0x14
    // 0xe77d78: mov             x1, x3
    // 0xe77d7c: r2 = "image"
    //     0xe77d7c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xe77d80: ldr             x2, [x2, #0x520]
    // 0xe77d84: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77d84: sub             lr, x0, #0x114
    //     0xe77d88: ldr             lr, [x21, lr, lsl #3]
    //     0xe77d8c: blr             lr
    // 0xe77d90: mov             x3, x0
    // 0xe77d94: r2 = Null
    //     0xe77d94: mov             x2, NULL
    // 0xe77d98: r1 = Null
    //     0xe77d98: mov             x1, NULL
    // 0xe77d9c: stur            x3, [fp, #-0x30]
    // 0xe77da0: r4 = 60
    //     0xe77da0: movz            x4, #0x3c
    // 0xe77da4: branchIfSmi(r0, 0xe77db0)
    //     0xe77da4: tbz             w0, #0, #0xe77db0
    // 0xe77da8: r4 = LoadClassIdInstr(r0)
    //     0xe77da8: ldur            x4, [x0, #-1]
    //     0xe77dac: ubfx            x4, x4, #0xc, #0x14
    // 0xe77db0: sub             x4, x4, #0x5e
    // 0xe77db4: cmp             x4, #1
    // 0xe77db8: b.ls            #0xe77dcc
    // 0xe77dbc: r8 = String
    //     0xe77dbc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe77dc0: r3 = Null
    //     0xe77dc0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a2b8] Null
    //     0xe77dc4: ldr             x3, [x3, #0x2b8]
    // 0xe77dc8: r0 = String()
    //     0xe77dc8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe77dcc: ldur            x3, [fp, #-8]
    // 0xe77dd0: r0 = LoadClassIdInstr(r3)
    //     0xe77dd0: ldur            x0, [x3, #-1]
    //     0xe77dd4: ubfx            x0, x0, #0xc, #0x14
    // 0xe77dd8: mov             x1, x3
    // 0xe77ddc: r2 = "type"
    //     0xe77ddc: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0xe77de0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77de0: sub             lr, x0, #0x114
    //     0xe77de4: ldr             lr, [x21, lr, lsl #3]
    //     0xe77de8: blr             lr
    // 0xe77dec: mov             x3, x0
    // 0xe77df0: r2 = Null
    //     0xe77df0: mov             x2, NULL
    // 0xe77df4: r1 = Null
    //     0xe77df4: mov             x1, NULL
    // 0xe77df8: stur            x3, [fp, #-0x38]
    // 0xe77dfc: branchIfSmi(r0, 0xe77e24)
    //     0xe77dfc: tbz             w0, #0, #0xe77e24
    // 0xe77e00: r4 = LoadClassIdInstr(r0)
    //     0xe77e00: ldur            x4, [x0, #-1]
    //     0xe77e04: ubfx            x4, x4, #0xc, #0x14
    // 0xe77e08: sub             x4, x4, #0x3c
    // 0xe77e0c: cmp             x4, #1
    // 0xe77e10: b.ls            #0xe77e24
    // 0xe77e14: r8 = int
    //     0xe77e14: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe77e18: r3 = Null
    //     0xe77e18: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a2c8] Null
    //     0xe77e1c: ldr             x3, [x3, #0x2c8]
    // 0xe77e20: r0 = int()
    //     0xe77e20: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe77e24: ldur            x1, [fp, #-8]
    // 0xe77e28: r0 = LoadClassIdInstr(r1)
    //     0xe77e28: ldur            x0, [x1, #-1]
    //     0xe77e2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe77e30: r2 = "published_at"
    //     0xe77e30: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d958] "published_at"
    //     0xe77e34: ldr             x2, [x2, #0x958]
    // 0xe77e38: r0 = GDT[cid_x0 + -0x114]()
    //     0xe77e38: sub             lr, x0, #0x114
    //     0xe77e3c: ldr             lr, [x21, lr, lsl #3]
    //     0xe77e40: blr             lr
    // 0xe77e44: r2 = Null
    //     0xe77e44: mov             x2, NULL
    // 0xe77e48: r1 = Null
    //     0xe77e48: mov             x1, NULL
    // 0xe77e4c: branchIfSmi(r0, 0xe77e74)
    //     0xe77e4c: tbz             w0, #0, #0xe77e74
    // 0xe77e50: r4 = LoadClassIdInstr(r0)
    //     0xe77e50: ldur            x4, [x0, #-1]
    //     0xe77e54: ubfx            x4, x4, #0xc, #0x14
    // 0xe77e58: sub             x4, x4, #0x3c
    // 0xe77e5c: cmp             x4, #1
    // 0xe77e60: b.ls            #0xe77e74
    // 0xe77e64: r8 = int
    //     0xe77e64: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe77e68: r3 = Null
    //     0xe77e68: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a2d8] Null
    //     0xe77e6c: ldr             x3, [x3, #0x2d8]
    // 0xe77e70: r0 = int()
    //     0xe77e70: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe77e74: ldur            x0, [fp, #-0x10]
    // 0xe77e78: r1 = LoadInt32Instr(r0)
    //     0xe77e78: sbfx            x1, x0, #1, #0x1f
    //     0xe77e7c: tbz             w0, #0, #0xe77e84
    //     0xe77e80: ldur            x1, [x0, #7]
    // 0xe77e84: stur            x1, [fp, #-0x40]
    // 0xe77e88: r0 = DoaRelated()
    //     0xe77e88: bl              #0xe77edc  ; AllocateDoaRelatedStub -> DoaRelated (size=0x28)
    // 0xe77e8c: ldur            x1, [fp, #-0x40]
    // 0xe77e90: StoreField: r0->field_7 = r1
    //     0xe77e90: stur            x1, [x0, #7]
    // 0xe77e94: ldur            x1, [fp, #-0x18]
    // 0xe77e98: StoreField: r0->field_f = r1
    //     0xe77e98: stur            w1, [x0, #0xf]
    // 0xe77e9c: ldur            x1, [fp, #-0x20]
    // 0xe77ea0: StoreField: r0->field_13 = r1
    //     0xe77ea0: stur            w1, [x0, #0x13]
    // 0xe77ea4: ldur            x1, [fp, #-0x28]
    // 0xe77ea8: ArrayStore: r0[0] = r1  ; List_4
    //     0xe77ea8: stur            w1, [x0, #0x17]
    // 0xe77eac: ldur            x1, [fp, #-0x30]
    // 0xe77eb0: StoreField: r0->field_1b = r1
    //     0xe77eb0: stur            w1, [x0, #0x1b]
    // 0xe77eb4: ldur            x1, [fp, #-0x38]
    // 0xe77eb8: r2 = LoadInt32Instr(r1)
    //     0xe77eb8: sbfx            x2, x1, #1, #0x1f
    //     0xe77ebc: tbz             w1, #0, #0xe77ec4
    //     0xe77ec0: ldur            x2, [x1, #7]
    // 0xe77ec4: StoreField: r0->field_1f = r2
    //     0xe77ec4: stur            x2, [x0, #0x1f]
    // 0xe77ec8: LeaveFrame
    //     0xe77ec8: mov             SP, fp
    //     0xe77ecc: ldp             fp, lr, [SP], #0x10
    // 0xe77ed0: ret
    //     0xe77ed0: ret             
    // 0xe77ed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77ed4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77ed8: b               #0xe77bf4
  }
}

// class id: 5592, size: 0x2c, field offset: 0x8
//   const constructor, 
class DoaSubCategory extends Equatable {

  get _ hasInfo(/* No info */) {
    // ** addr: 0xbf67f8, size: 0x30
    // 0xbf67f8: LoadField: r2 = r1->field_1b
    //     0xbf67f8: ldur            w2, [x1, #0x1b]
    // 0xbf67fc: DecompressPointer r2
    //     0xbf67fc: add             x2, x2, HEAP, lsl #32
    // 0xbf6800: cmp             w2, NULL
    // 0xbf6804: b.ne            #0xbf6810
    // 0xbf6808: r0 = false
    //     0xbf6808: add             x0, NULL, #0x30  ; false
    // 0xbf680c: ret
    //     0xbf680c: ret             
    // 0xbf6810: LoadField: r1 = r2->field_7
    //     0xbf6810: ldur            w1, [x2, #7]
    // 0xbf6814: cbnz            w1, #0xbf6820
    // 0xbf6818: r0 = false
    //     0xbf6818: add             x0, NULL, #0x30  ; false
    // 0xbf681c: b               #0xbf6824
    // 0xbf6820: r0 = true
    //     0xbf6820: add             x0, NULL, #0x20  ; true
    // 0xbf6824: ret
    //     0xbf6824: ret             
  }
  factory _ DoaSubCategory.fromMap(/* No info */) {
    // ** addr: 0xdc84ac, size: 0x318
    // 0xdc84ac: EnterFrame
    //     0xdc84ac: stp             fp, lr, [SP, #-0x10]!
    //     0xdc84b0: mov             fp, SP
    // 0xdc84b4: AllocStack(0x40)
    //     0xdc84b4: sub             SP, SP, #0x40
    // 0xdc84b8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xdc84b8: mov             x3, x2
    //     0xdc84bc: stur            x2, [fp, #-8]
    // 0xdc84c0: CheckStackOverflow
    //     0xdc84c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc84c4: cmp             SP, x16
    //     0xdc84c8: b.ls            #0xdc87bc
    // 0xdc84cc: r0 = LoadClassIdInstr(r3)
    //     0xdc84cc: ldur            x0, [x3, #-1]
    //     0xdc84d0: ubfx            x0, x0, #0xc, #0x14
    // 0xdc84d4: mov             x1, x3
    // 0xdc84d8: r2 = "id"
    //     0xdc84d8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc84dc: ldr             x2, [x2, #0x740]
    // 0xdc84e0: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc84e0: sub             lr, x0, #0x114
    //     0xdc84e4: ldr             lr, [x21, lr, lsl #3]
    //     0xdc84e8: blr             lr
    // 0xdc84ec: mov             x3, x0
    // 0xdc84f0: r2 = Null
    //     0xdc84f0: mov             x2, NULL
    // 0xdc84f4: r1 = Null
    //     0xdc84f4: mov             x1, NULL
    // 0xdc84f8: stur            x3, [fp, #-0x10]
    // 0xdc84fc: branchIfSmi(r0, 0xdc8524)
    //     0xdc84fc: tbz             w0, #0, #0xdc8524
    // 0xdc8500: r4 = LoadClassIdInstr(r0)
    //     0xdc8500: ldur            x4, [x0, #-1]
    //     0xdc8504: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8508: sub             x4, x4, #0x3c
    // 0xdc850c: cmp             x4, #1
    // 0xdc8510: b.ls            #0xdc8524
    // 0xdc8514: r8 = int
    //     0xdc8514: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc8518: r3 = Null
    //     0xdc8518: add             x3, PP, #0x19, lsl #12  ; [pp+0x19cd8] Null
    //     0xdc851c: ldr             x3, [x3, #0xcd8]
    // 0xdc8520: r0 = int()
    //     0xdc8520: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc8524: ldur            x3, [fp, #-8]
    // 0xdc8528: r0 = LoadClassIdInstr(r3)
    //     0xdc8528: ldur            x0, [x3, #-1]
    //     0xdc852c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8530: mov             x1, x3
    // 0xdc8534: r2 = "name"
    //     0xdc8534: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xdc8538: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8538: sub             lr, x0, #0x114
    //     0xdc853c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8540: blr             lr
    // 0xdc8544: mov             x3, x0
    // 0xdc8548: r2 = Null
    //     0xdc8548: mov             x2, NULL
    // 0xdc854c: r1 = Null
    //     0xdc854c: mov             x1, NULL
    // 0xdc8550: stur            x3, [fp, #-0x18]
    // 0xdc8554: r4 = 60
    //     0xdc8554: movz            x4, #0x3c
    // 0xdc8558: branchIfSmi(r0, 0xdc8564)
    //     0xdc8558: tbz             w0, #0, #0xdc8564
    // 0xdc855c: r4 = LoadClassIdInstr(r0)
    //     0xdc855c: ldur            x4, [x0, #-1]
    //     0xdc8560: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8564: sub             x4, x4, #0x5e
    // 0xdc8568: cmp             x4, #1
    // 0xdc856c: b.ls            #0xdc8580
    // 0xdc8570: r8 = String
    //     0xdc8570: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8574: r3 = Null
    //     0xdc8574: add             x3, PP, #0x19, lsl #12  ; [pp+0x19ce8] Null
    //     0xdc8578: ldr             x3, [x3, #0xce8]
    // 0xdc857c: r0 = String()
    //     0xdc857c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc8580: ldur            x3, [fp, #-8]
    // 0xdc8584: r0 = LoadClassIdInstr(r3)
    //     0xdc8584: ldur            x0, [x3, #-1]
    //     0xdc8588: ubfx            x0, x0, #0xc, #0x14
    // 0xdc858c: mov             x1, x3
    // 0xdc8590: r2 = "parent_id"
    //     0xdc8590: add             x2, PP, #0x19, lsl #12  ; [pp+0x19cf8] "parent_id"
    //     0xdc8594: ldr             x2, [x2, #0xcf8]
    // 0xdc8598: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8598: sub             lr, x0, #0x114
    //     0xdc859c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc85a0: blr             lr
    // 0xdc85a4: mov             x3, x0
    // 0xdc85a8: r2 = Null
    //     0xdc85a8: mov             x2, NULL
    // 0xdc85ac: r1 = Null
    //     0xdc85ac: mov             x1, NULL
    // 0xdc85b0: stur            x3, [fp, #-0x20]
    // 0xdc85b4: branchIfSmi(r0, 0xdc85dc)
    //     0xdc85b4: tbz             w0, #0, #0xdc85dc
    // 0xdc85b8: r4 = LoadClassIdInstr(r0)
    //     0xdc85b8: ldur            x4, [x0, #-1]
    //     0xdc85bc: ubfx            x4, x4, #0xc, #0x14
    // 0xdc85c0: sub             x4, x4, #0x3c
    // 0xdc85c4: cmp             x4, #1
    // 0xdc85c8: b.ls            #0xdc85dc
    // 0xdc85cc: r8 = int
    //     0xdc85cc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc85d0: r3 = Null
    //     0xdc85d0: add             x3, PP, #0x19, lsl #12  ; [pp+0x19d00] Null
    //     0xdc85d4: ldr             x3, [x3, #0xd00]
    // 0xdc85d8: r0 = int()
    //     0xdc85d8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc85dc: ldur            x3, [fp, #-8]
    // 0xdc85e0: r0 = LoadClassIdInstr(r3)
    //     0xdc85e0: ldur            x0, [x3, #-1]
    //     0xdc85e4: ubfx            x0, x0, #0xc, #0x14
    // 0xdc85e8: mov             x1, x3
    // 0xdc85ec: r2 = "parent_name"
    //     0xdc85ec: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d10] "parent_name"
    //     0xdc85f0: ldr             x2, [x2, #0xd10]
    // 0xdc85f4: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc85f4: sub             lr, x0, #0x114
    //     0xdc85f8: ldr             lr, [x21, lr, lsl #3]
    //     0xdc85fc: blr             lr
    // 0xdc8600: mov             x3, x0
    // 0xdc8604: r2 = Null
    //     0xdc8604: mov             x2, NULL
    // 0xdc8608: r1 = Null
    //     0xdc8608: mov             x1, NULL
    // 0xdc860c: stur            x3, [fp, #-0x28]
    // 0xdc8610: r4 = 60
    //     0xdc8610: movz            x4, #0x3c
    // 0xdc8614: branchIfSmi(r0, 0xdc8620)
    //     0xdc8614: tbz             w0, #0, #0xdc8620
    // 0xdc8618: r4 = LoadClassIdInstr(r0)
    //     0xdc8618: ldur            x4, [x0, #-1]
    //     0xdc861c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8620: sub             x4, x4, #0x5e
    // 0xdc8624: cmp             x4, #1
    // 0xdc8628: b.ls            #0xdc863c
    // 0xdc862c: r8 = String
    //     0xdc862c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8630: r3 = Null
    //     0xdc8630: add             x3, PP, #0x19, lsl #12  ; [pp+0x19d18] Null
    //     0xdc8634: ldr             x3, [x3, #0xd18]
    // 0xdc8638: r0 = String()
    //     0xdc8638: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc863c: ldur            x3, [fp, #-8]
    // 0xdc8640: r0 = LoadClassIdInstr(r3)
    //     0xdc8640: ldur            x0, [x3, #-1]
    //     0xdc8644: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8648: mov             x1, x3
    // 0xdc864c: r2 = "description"
    //     0xdc864c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0xdc8650: ldr             x2, [x2, #0xd28]
    // 0xdc8654: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8654: sub             lr, x0, #0x114
    //     0xdc8658: ldr             lr, [x21, lr, lsl #3]
    //     0xdc865c: blr             lr
    // 0xdc8660: mov             x3, x0
    // 0xdc8664: r2 = Null
    //     0xdc8664: mov             x2, NULL
    // 0xdc8668: r1 = Null
    //     0xdc8668: mov             x1, NULL
    // 0xdc866c: stur            x3, [fp, #-0x30]
    // 0xdc8670: r4 = 60
    //     0xdc8670: movz            x4, #0x3c
    // 0xdc8674: branchIfSmi(r0, 0xdc8680)
    //     0xdc8674: tbz             w0, #0, #0xdc8680
    // 0xdc8678: r4 = LoadClassIdInstr(r0)
    //     0xdc8678: ldur            x4, [x0, #-1]
    //     0xdc867c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8680: sub             x4, x4, #0x5e
    // 0xdc8684: cmp             x4, #1
    // 0xdc8688: b.ls            #0xdc869c
    // 0xdc868c: r8 = String?
    //     0xdc868c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xdc8690: r3 = Null
    //     0xdc8690: add             x3, PP, #0x19, lsl #12  ; [pp+0x19d30] Null
    //     0xdc8694: ldr             x3, [x3, #0xd30]
    // 0xdc8698: r0 = String?()
    //     0xdc8698: bl              #0x600324  ; IsType_String?_Stub
    // 0xdc869c: ldur            x3, [fp, #-8]
    // 0xdc86a0: r0 = LoadClassIdInstr(r3)
    //     0xdc86a0: ldur            x0, [x3, #-1]
    //     0xdc86a4: ubfx            x0, x0, #0xc, #0x14
    // 0xdc86a8: mov             x1, x3
    // 0xdc86ac: r2 = "order"
    //     0xdc86ac: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0xdc86b0: ldr             x2, [x2, #0xb78]
    // 0xdc86b4: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc86b4: sub             lr, x0, #0x114
    //     0xdc86b8: ldr             lr, [x21, lr, lsl #3]
    //     0xdc86bc: blr             lr
    // 0xdc86c0: mov             x3, x0
    // 0xdc86c4: r2 = Null
    //     0xdc86c4: mov             x2, NULL
    // 0xdc86c8: r1 = Null
    //     0xdc86c8: mov             x1, NULL
    // 0xdc86cc: stur            x3, [fp, #-0x38]
    // 0xdc86d0: branchIfSmi(r0, 0xdc86f8)
    //     0xdc86d0: tbz             w0, #0, #0xdc86f8
    // 0xdc86d4: r4 = LoadClassIdInstr(r0)
    //     0xdc86d4: ldur            x4, [x0, #-1]
    //     0xdc86d8: ubfx            x4, x4, #0xc, #0x14
    // 0xdc86dc: sub             x4, x4, #0x3c
    // 0xdc86e0: cmp             x4, #1
    // 0xdc86e4: b.ls            #0xdc86f8
    // 0xdc86e8: r8 = int?
    //     0xdc86e8: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xdc86ec: r3 = Null
    //     0xdc86ec: add             x3, PP, #0x19, lsl #12  ; [pp+0x19d40] Null
    //     0xdc86f0: ldr             x3, [x3, #0xd40]
    // 0xdc86f4: r0 = int?()
    //     0xdc86f4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xdc86f8: ldur            x1, [fp, #-8]
    // 0xdc86fc: r0 = LoadClassIdInstr(r1)
    //     0xdc86fc: ldur            x0, [x1, #-1]
    //     0xdc8700: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8704: r2 = "updated_at"
    //     0xdc8704: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0xdc8708: ldr             x2, [x2, #0xe88]
    // 0xdc870c: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc870c: sub             lr, x0, #0x114
    //     0xdc8710: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8714: blr             lr
    // 0xdc8718: mov             x3, x0
    // 0xdc871c: r2 = Null
    //     0xdc871c: mov             x2, NULL
    // 0xdc8720: r1 = Null
    //     0xdc8720: mov             x1, NULL
    // 0xdc8724: stur            x3, [fp, #-8]
    // 0xdc8728: r4 = 60
    //     0xdc8728: movz            x4, #0x3c
    // 0xdc872c: branchIfSmi(r0, 0xdc8738)
    //     0xdc872c: tbz             w0, #0, #0xdc8738
    // 0xdc8730: r4 = LoadClassIdInstr(r0)
    //     0xdc8730: ldur            x4, [x0, #-1]
    //     0xdc8734: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8738: sub             x4, x4, #0x5e
    // 0xdc873c: cmp             x4, #1
    // 0xdc8740: b.ls            #0xdc8754
    // 0xdc8744: r8 = String
    //     0xdc8744: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8748: r3 = Null
    //     0xdc8748: add             x3, PP, #0x19, lsl #12  ; [pp+0x19d50] Null
    //     0xdc874c: ldr             x3, [x3, #0xd50]
    // 0xdc8750: r0 = String()
    //     0xdc8750: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc8754: ldur            x0, [fp, #-0x10]
    // 0xdc8758: r1 = LoadInt32Instr(r0)
    //     0xdc8758: sbfx            x1, x0, #1, #0x1f
    //     0xdc875c: tbz             w0, #0, #0xdc8764
    //     0xdc8760: ldur            x1, [x0, #7]
    // 0xdc8764: stur            x1, [fp, #-0x40]
    // 0xdc8768: r0 = DoaSubCategory()
    //     0xdc8768: bl              #0xa61674  ; AllocateDoaSubCategoryStub -> DoaSubCategory (size=0x2c)
    // 0xdc876c: ldur            x1, [fp, #-0x40]
    // 0xdc8770: StoreField: r0->field_7 = r1
    //     0xdc8770: stur            x1, [x0, #7]
    // 0xdc8774: ldur            x1, [fp, #-0x18]
    // 0xdc8778: StoreField: r0->field_f = r1
    //     0xdc8778: stur            w1, [x0, #0xf]
    // 0xdc877c: ldur            x1, [fp, #-0x20]
    // 0xdc8780: r2 = LoadInt32Instr(r1)
    //     0xdc8780: sbfx            x2, x1, #1, #0x1f
    //     0xdc8784: tbz             w1, #0, #0xdc878c
    //     0xdc8788: ldur            x2, [x1, #7]
    // 0xdc878c: StoreField: r0->field_13 = r2
    //     0xdc878c: stur            x2, [x0, #0x13]
    // 0xdc8790: ldur            x1, [fp, #-0x30]
    // 0xdc8794: StoreField: r0->field_1b = r1
    //     0xdc8794: stur            w1, [x0, #0x1b]
    // 0xdc8798: ldur            x1, [fp, #-0x38]
    // 0xdc879c: StoreField: r0->field_1f = r1
    //     0xdc879c: stur            w1, [x0, #0x1f]
    // 0xdc87a0: ldur            x1, [fp, #-0x28]
    // 0xdc87a4: StoreField: r0->field_23 = r1
    //     0xdc87a4: stur            w1, [x0, #0x23]
    // 0xdc87a8: ldur            x1, [fp, #-8]
    // 0xdc87ac: StoreField: r0->field_27 = r1
    //     0xdc87ac: stur            w1, [x0, #0x27]
    // 0xdc87b0: LeaveFrame
    //     0xdc87b0: mov             SP, fp
    //     0xdc87b4: ldp             fp, lr, [SP], #0x10
    // 0xdc87b8: ret
    //     0xdc87b8: ret             
    // 0xdc87bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc87bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc87c0: b               #0xdc84cc
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe7a074, size: 0x180
    // 0xe7a074: EnterFrame
    //     0xe7a074: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a078: mov             fp, SP
    // 0xe7a07c: AllocStack(0x20)
    //     0xe7a07c: sub             SP, SP, #0x20
    // 0xe7a080: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe7a080: mov             x3, x1
    //     0xe7a084: stur            x1, [fp, #-8]
    // 0xe7a088: CheckStackOverflow
    //     0xe7a088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a08c: cmp             SP, x16
    //     0xe7a090: b.ls            #0xe7a1ec
    // 0xe7a094: mov             x0, x3
    // 0xe7a098: r2 = Null
    //     0xe7a098: mov             x2, NULL
    // 0xe7a09c: r1 = Null
    //     0xe7a09c: mov             x1, NULL
    // 0xe7a0a0: cmp             w0, NULL
    // 0xe7a0a4: b.eq            #0xe7a148
    // 0xe7a0a8: branchIfSmi(r0, 0xe7a148)
    //     0xe7a0a8: tbz             w0, #0, #0xe7a148
    // 0xe7a0ac: r3 = LoadClassIdInstr(r0)
    //     0xe7a0ac: ldur            x3, [x0, #-1]
    //     0xe7a0b0: ubfx            x3, x3, #0xc, #0x14
    // 0xe7a0b4: r17 = 6718
    //     0xe7a0b4: movz            x17, #0x1a3e
    // 0xe7a0b8: cmp             x3, x17
    // 0xe7a0bc: b.eq            #0xe7a150
    // 0xe7a0c0: sub             x3, x3, #0x5a
    // 0xe7a0c4: cmp             x3, #2
    // 0xe7a0c8: b.ls            #0xe7a150
    // 0xe7a0cc: r4 = LoadClassIdInstr(r0)
    //     0xe7a0cc: ldur            x4, [x0, #-1]
    //     0xe7a0d0: ubfx            x4, x4, #0xc, #0x14
    // 0xe7a0d4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe7a0d8: ldr             x3, [x3, #0x18]
    // 0xe7a0dc: ldr             x3, [x3, x4, lsl #3]
    // 0xe7a0e0: LoadField: r3 = r3->field_2b
    //     0xe7a0e0: ldur            w3, [x3, #0x2b]
    // 0xe7a0e4: DecompressPointer r3
    //     0xe7a0e4: add             x3, x3, HEAP, lsl #32
    // 0xe7a0e8: cmp             w3, NULL
    // 0xe7a0ec: b.eq            #0xe7a148
    // 0xe7a0f0: LoadField: r3 = r3->field_f
    //     0xe7a0f0: ldur            w3, [x3, #0xf]
    // 0xe7a0f4: lsr             x3, x3, #3
    // 0xe7a0f8: r17 = 6718
    //     0xe7a0f8: movz            x17, #0x1a3e
    // 0xe7a0fc: cmp             x3, x17
    // 0xe7a100: b.eq            #0xe7a150
    // 0xe7a104: r3 = SubtypeTestCache
    //     0xe7a104: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a1f8] SubtypeTestCache
    //     0xe7a108: ldr             x3, [x3, #0x1f8]
    // 0xe7a10c: r30 = Subtype1TestCacheStub
    //     0xe7a10c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe7a110: LoadField: r30 = r30->field_7
    //     0xe7a110: ldur            lr, [lr, #7]
    // 0xe7a114: blr             lr
    // 0xe7a118: cmp             w7, NULL
    // 0xe7a11c: b.eq            #0xe7a128
    // 0xe7a120: tbnz            w7, #4, #0xe7a148
    // 0xe7a124: b               #0xe7a150
    // 0xe7a128: r8 = List
    //     0xe7a128: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a200] Type: List
    //     0xe7a12c: ldr             x8, [x8, #0x200]
    // 0xe7a130: r3 = SubtypeTestCache
    //     0xe7a130: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a208] SubtypeTestCache
    //     0xe7a134: ldr             x3, [x3, #0x208]
    // 0xe7a138: r30 = InstanceOfStub
    //     0xe7a138: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe7a13c: LoadField: r30 = r30->field_7
    //     0xe7a13c: ldur            lr, [lr, #7]
    // 0xe7a140: blr             lr
    // 0xe7a144: b               #0xe7a154
    // 0xe7a148: r0 = false
    //     0xe7a148: add             x0, NULL, #0x30  ; false
    // 0xe7a14c: b               #0xe7a154
    // 0xe7a150: r0 = true
    //     0xe7a150: add             x0, NULL, #0x20  ; true
    // 0xe7a154: tbnz            w0, #4, #0xe7a1d4
    // 0xe7a158: ldur            x0, [fp, #-8]
    // 0xe7a15c: r1 = Function '<anonymous closure>': static.
    //     0xe7a15c: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a210] AnonymousClosure: static (0xe7a1f4), in [package:nuonline/app/data/models/doa.dart] DoaSubCategory::fromResponse (0xe7a074)
    //     0xe7a160: ldr             x1, [x1, #0x210]
    // 0xe7a164: r2 = Null
    //     0xe7a164: mov             x2, NULL
    // 0xe7a168: r0 = AllocateClosure()
    //     0xe7a168: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a16c: mov             x1, x0
    // 0xe7a170: ldur            x0, [fp, #-8]
    // 0xe7a174: r2 = LoadClassIdInstr(r0)
    //     0xe7a174: ldur            x2, [x0, #-1]
    //     0xe7a178: ubfx            x2, x2, #0xc, #0x14
    // 0xe7a17c: r16 = <DoaSubCategory>
    //     0xe7a17c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a180: stp             x0, x16, [SP, #8]
    // 0xe7a184: str             x1, [SP]
    // 0xe7a188: mov             x0, x2
    // 0xe7a18c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a18c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a190: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7a190: movz            x17, #0xf28c
    //     0xe7a194: add             lr, x0, x17
    //     0xe7a198: ldr             lr, [x21, lr, lsl #3]
    //     0xe7a19c: blr             lr
    // 0xe7a1a0: r1 = LoadClassIdInstr(r0)
    //     0xe7a1a0: ldur            x1, [x0, #-1]
    //     0xe7a1a4: ubfx            x1, x1, #0xc, #0x14
    // 0xe7a1a8: mov             x16, x0
    // 0xe7a1ac: mov             x0, x1
    // 0xe7a1b0: mov             x1, x16
    // 0xe7a1b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7a1b4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7a1b8: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7a1b8: movz            x17, #0xd889
    //     0xe7a1bc: add             lr, x0, x17
    //     0xe7a1c0: ldr             lr, [x21, lr, lsl #3]
    //     0xe7a1c4: blr             lr
    // 0xe7a1c8: LeaveFrame
    //     0xe7a1c8: mov             SP, fp
    //     0xe7a1cc: ldp             fp, lr, [SP], #0x10
    // 0xe7a1d0: ret
    //     0xe7a1d0: ret             
    // 0xe7a1d4: r1 = <DoaSubCategory>
    //     0xe7a1d4: ldr             x1, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a1d8: r2 = 0
    //     0xe7a1d8: movz            x2, #0
    // 0xe7a1dc: r0 = _GrowableList()
    //     0xe7a1dc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7a1e0: LeaveFrame
    //     0xe7a1e0: mov             SP, fp
    //     0xe7a1e4: ldp             fp, lr, [SP], #0x10
    // 0xe7a1e8: ret
    //     0xe7a1e8: ret             
    // 0xe7a1ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a1ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a1f0: b               #0xe7a094
  }
  [closure] static DoaSubCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7a1f4, size: 0x50
    // 0xe7a1f4: EnterFrame
    //     0xe7a1f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a1f8: mov             fp, SP
    // 0xe7a1fc: CheckStackOverflow
    //     0xe7a1fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a200: cmp             SP, x16
    //     0xe7a204: b.ls            #0xe7a23c
    // 0xe7a208: ldr             x0, [fp, #0x10]
    // 0xe7a20c: r2 = Null
    //     0xe7a20c: mov             x2, NULL
    // 0xe7a210: r1 = Null
    //     0xe7a210: mov             x1, NULL
    // 0xe7a214: r8 = Map<String, dynamic>
    //     0xe7a214: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7a218: r3 = Null
    //     0xe7a218: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a218] Null
    //     0xe7a21c: ldr             x3, [x3, #0x218]
    // 0xe7a220: r0 = Map<String, dynamic>()
    //     0xe7a220: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7a224: ldr             x2, [fp, #0x10]
    // 0xe7a228: r1 = Null
    //     0xe7a228: mov             x1, NULL
    // 0xe7a22c: r0 = DoaSubCategory.fromMap()
    //     0xe7a22c: bl              #0xdc84ac  ; [package:nuonline/app/data/models/doa.dart] DoaSubCategory::DoaSubCategory.fromMap
    // 0xe7a230: LeaveFrame
    //     0xe7a230: mov             SP, fp
    //     0xe7a234: ldp             fp, lr, [SP], #0x10
    // 0xe7a238: ret
    //     0xe7a238: ret             
    // 0xe7a23c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a23c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a240: b               #0xe7a208
  }
  get _ searchable(/* No info */) {
    // ** addr: 0xe7a690, size: 0x98
    // 0xe7a690: EnterFrame
    //     0xe7a690: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a694: mov             fp, SP
    // 0xe7a698: AllocStack(0x20)
    //     0xe7a698: sub             SP, SP, #0x20
    // 0xe7a69c: r0 = 4
    //     0xe7a69c: movz            x0, #0x4
    // 0xe7a6a0: CheckStackOverflow
    //     0xe7a6a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a6a4: cmp             SP, x16
    //     0xe7a6a8: b.ls            #0xe7a720
    // 0xe7a6ac: LoadField: r3 = r1->field_23
    //     0xe7a6ac: ldur            w3, [x1, #0x23]
    // 0xe7a6b0: DecompressPointer r3
    //     0xe7a6b0: add             x3, x3, HEAP, lsl #32
    // 0xe7a6b4: stur            x3, [fp, #-0x10]
    // 0xe7a6b8: LoadField: r4 = r1->field_f
    //     0xe7a6b8: ldur            w4, [x1, #0xf]
    // 0xe7a6bc: DecompressPointer r4
    //     0xe7a6bc: add             x4, x4, HEAP, lsl #32
    // 0xe7a6c0: mov             x2, x0
    // 0xe7a6c4: stur            x4, [fp, #-8]
    // 0xe7a6c8: r1 = Null
    //     0xe7a6c8: mov             x1, NULL
    // 0xe7a6cc: r0 = AllocateArray()
    //     0xe7a6cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7a6d0: mov             x2, x0
    // 0xe7a6d4: ldur            x0, [fp, #-0x10]
    // 0xe7a6d8: stur            x2, [fp, #-0x18]
    // 0xe7a6dc: StoreField: r2->field_f = r0
    //     0xe7a6dc: stur            w0, [x2, #0xf]
    // 0xe7a6e0: ldur            x0, [fp, #-8]
    // 0xe7a6e4: StoreField: r2->field_13 = r0
    //     0xe7a6e4: stur            w0, [x2, #0x13]
    // 0xe7a6e8: r1 = <String>
    //     0xe7a6e8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe7a6ec: r0 = AllocateGrowableArray()
    //     0xe7a6ec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xe7a6f0: mov             x1, x0
    // 0xe7a6f4: ldur            x0, [fp, #-0x18]
    // 0xe7a6f8: StoreField: r1->field_f = r0
    //     0xe7a6f8: stur            w0, [x1, #0xf]
    // 0xe7a6fc: r0 = 4
    //     0xe7a6fc: movz            x0, #0x4
    // 0xe7a700: StoreField: r1->field_b = r0
    //     0xe7a700: stur            w0, [x1, #0xb]
    // 0xe7a704: r16 = " "
    //     0xe7a704: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xe7a708: str             x16, [SP]
    // 0xe7a70c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xe7a70c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xe7a710: r0 = join()
    //     0xe7a710: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xe7a714: LeaveFrame
    //     0xe7a714: mov             SP, fp
    //     0xe7a718: ldp             fp, lr, [SP], #0x10
    // 0xe7a71c: ret
    //     0xe7a71c: ret             
    // 0xe7a720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a724: b               #0xe7a6ac
  }
}

// class id: 5593, size: 0x28, field offset: 0x8
//   const constructor, 
class DoaCategory extends Equatable {

  factory _ DoaCategory.fromMap(/* No info */) {
    // ** addr: 0xdc8a8c, size: 0x2b0
    // 0xdc8a8c: EnterFrame
    //     0xdc8a8c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc8a90: mov             fp, SP
    // 0xdc8a94: AllocStack(0x38)
    //     0xdc8a94: sub             SP, SP, #0x38
    // 0xdc8a98: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xdc8a98: mov             x3, x2
    //     0xdc8a9c: stur            x2, [fp, #-8]
    // 0xdc8aa0: CheckStackOverflow
    //     0xdc8aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc8aa4: cmp             SP, x16
    //     0xdc8aa8: b.ls            #0xdc8d34
    // 0xdc8aac: r0 = LoadClassIdInstr(r3)
    //     0xdc8aac: ldur            x0, [x3, #-1]
    //     0xdc8ab0: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8ab4: mov             x1, x3
    // 0xdc8ab8: r2 = "id"
    //     0xdc8ab8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc8abc: ldr             x2, [x2, #0x740]
    // 0xdc8ac0: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8ac0: sub             lr, x0, #0x114
    //     0xdc8ac4: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8ac8: blr             lr
    // 0xdc8acc: mov             x3, x0
    // 0xdc8ad0: r2 = Null
    //     0xdc8ad0: mov             x2, NULL
    // 0xdc8ad4: r1 = Null
    //     0xdc8ad4: mov             x1, NULL
    // 0xdc8ad8: stur            x3, [fp, #-0x10]
    // 0xdc8adc: branchIfSmi(r0, 0xdc8b04)
    //     0xdc8adc: tbz             w0, #0, #0xdc8b04
    // 0xdc8ae0: r4 = LoadClassIdInstr(r0)
    //     0xdc8ae0: ldur            x4, [x0, #-1]
    //     0xdc8ae4: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8ae8: sub             x4, x4, #0x3c
    // 0xdc8aec: cmp             x4, #1
    // 0xdc8af0: b.ls            #0xdc8b04
    // 0xdc8af4: r8 = int
    //     0xdc8af4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc8af8: r3 = Null
    //     0xdc8af8: add             x3, PP, #0x19, lsl #12  ; [pp+0x19da0] Null
    //     0xdc8afc: ldr             x3, [x3, #0xda0]
    // 0xdc8b00: r0 = int()
    //     0xdc8b00: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc8b04: ldur            x3, [fp, #-8]
    // 0xdc8b08: r0 = LoadClassIdInstr(r3)
    //     0xdc8b08: ldur            x0, [x3, #-1]
    //     0xdc8b0c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8b10: mov             x1, x3
    // 0xdc8b14: r2 = "name"
    //     0xdc8b14: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xdc8b18: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8b18: sub             lr, x0, #0x114
    //     0xdc8b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8b20: blr             lr
    // 0xdc8b24: mov             x3, x0
    // 0xdc8b28: r2 = Null
    //     0xdc8b28: mov             x2, NULL
    // 0xdc8b2c: r1 = Null
    //     0xdc8b2c: mov             x1, NULL
    // 0xdc8b30: stur            x3, [fp, #-0x18]
    // 0xdc8b34: r4 = 60
    //     0xdc8b34: movz            x4, #0x3c
    // 0xdc8b38: branchIfSmi(r0, 0xdc8b44)
    //     0xdc8b38: tbz             w0, #0, #0xdc8b44
    // 0xdc8b3c: r4 = LoadClassIdInstr(r0)
    //     0xdc8b3c: ldur            x4, [x0, #-1]
    //     0xdc8b40: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8b44: sub             x4, x4, #0x5e
    // 0xdc8b48: cmp             x4, #1
    // 0xdc8b4c: b.ls            #0xdc8b60
    // 0xdc8b50: r8 = String
    //     0xdc8b50: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8b54: r3 = Null
    //     0xdc8b54: add             x3, PP, #0x19, lsl #12  ; [pp+0x19db0] Null
    //     0xdc8b58: ldr             x3, [x3, #0xdb0]
    // 0xdc8b5c: r0 = String()
    //     0xdc8b5c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc8b60: ldur            x3, [fp, #-8]
    // 0xdc8b64: r0 = LoadClassIdInstr(r3)
    //     0xdc8b64: ldur            x0, [x3, #-1]
    //     0xdc8b68: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8b6c: mov             x1, x3
    // 0xdc8b70: r2 = "total"
    //     0xdc8b70: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0xdc8b74: ldr             x2, [x2, #0xdc0]
    // 0xdc8b78: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8b78: sub             lr, x0, #0x114
    //     0xdc8b7c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8b80: blr             lr
    // 0xdc8b84: mov             x3, x0
    // 0xdc8b88: r2 = Null
    //     0xdc8b88: mov             x2, NULL
    // 0xdc8b8c: r1 = Null
    //     0xdc8b8c: mov             x1, NULL
    // 0xdc8b90: stur            x3, [fp, #-0x20]
    // 0xdc8b94: branchIfSmi(r0, 0xdc8bbc)
    //     0xdc8b94: tbz             w0, #0, #0xdc8bbc
    // 0xdc8b98: r4 = LoadClassIdInstr(r0)
    //     0xdc8b98: ldur            x4, [x0, #-1]
    //     0xdc8b9c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8ba0: sub             x4, x4, #0x3c
    // 0xdc8ba4: cmp             x4, #1
    // 0xdc8ba8: b.ls            #0xdc8bbc
    // 0xdc8bac: r8 = int
    //     0xdc8bac: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc8bb0: r3 = Null
    //     0xdc8bb0: add             x3, PP, #0x19, lsl #12  ; [pp+0x19dc8] Null
    //     0xdc8bb4: ldr             x3, [x3, #0xdc8]
    // 0xdc8bb8: r0 = int()
    //     0xdc8bb8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc8bbc: ldur            x3, [fp, #-8]
    // 0xdc8bc0: r0 = LoadClassIdInstr(r3)
    //     0xdc8bc0: ldur            x0, [x3, #-1]
    //     0xdc8bc4: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8bc8: mov             x1, x3
    // 0xdc8bcc: r2 = "group"
    //     0xdc8bcc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dd8] "group"
    //     0xdc8bd0: ldr             x2, [x2, #0xdd8]
    // 0xdc8bd4: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8bd4: sub             lr, x0, #0x114
    //     0xdc8bd8: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8bdc: blr             lr
    // 0xdc8be0: mov             x3, x0
    // 0xdc8be4: r2 = Null
    //     0xdc8be4: mov             x2, NULL
    // 0xdc8be8: r1 = Null
    //     0xdc8be8: mov             x1, NULL
    // 0xdc8bec: stur            x3, [fp, #-0x28]
    // 0xdc8bf0: r4 = 60
    //     0xdc8bf0: movz            x4, #0x3c
    // 0xdc8bf4: branchIfSmi(r0, 0xdc8c00)
    //     0xdc8bf4: tbz             w0, #0, #0xdc8c00
    // 0xdc8bf8: r4 = LoadClassIdInstr(r0)
    //     0xdc8bf8: ldur            x4, [x0, #-1]
    //     0xdc8bfc: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8c00: sub             x4, x4, #0x5e
    // 0xdc8c04: cmp             x4, #1
    // 0xdc8c08: b.ls            #0xdc8c1c
    // 0xdc8c0c: r8 = String
    //     0xdc8c0c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8c10: r3 = Null
    //     0xdc8c10: add             x3, PP, #0x19, lsl #12  ; [pp+0x19de0] Null
    //     0xdc8c14: ldr             x3, [x3, #0xde0]
    // 0xdc8c18: r0 = String()
    //     0xdc8c18: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc8c1c: ldur            x3, [fp, #-8]
    // 0xdc8c20: r0 = LoadClassIdInstr(r3)
    //     0xdc8c20: ldur            x0, [x3, #-1]
    //     0xdc8c24: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8c28: mov             x1, x3
    // 0xdc8c2c: r2 = "order"
    //     0xdc8c2c: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0xdc8c30: ldr             x2, [x2, #0xb78]
    // 0xdc8c34: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8c34: sub             lr, x0, #0x114
    //     0xdc8c38: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8c3c: blr             lr
    // 0xdc8c40: mov             x3, x0
    // 0xdc8c44: r2 = Null
    //     0xdc8c44: mov             x2, NULL
    // 0xdc8c48: r1 = Null
    //     0xdc8c48: mov             x1, NULL
    // 0xdc8c4c: stur            x3, [fp, #-0x30]
    // 0xdc8c50: branchIfSmi(r0, 0xdc8c78)
    //     0xdc8c50: tbz             w0, #0, #0xdc8c78
    // 0xdc8c54: r4 = LoadClassIdInstr(r0)
    //     0xdc8c54: ldur            x4, [x0, #-1]
    //     0xdc8c58: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8c5c: sub             x4, x4, #0x3c
    // 0xdc8c60: cmp             x4, #1
    // 0xdc8c64: b.ls            #0xdc8c78
    // 0xdc8c68: r8 = int?
    //     0xdc8c68: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xdc8c6c: r3 = Null
    //     0xdc8c6c: add             x3, PP, #0x19, lsl #12  ; [pp+0x19df0] Null
    //     0xdc8c70: ldr             x3, [x3, #0xdf0]
    // 0xdc8c74: r0 = int?()
    //     0xdc8c74: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xdc8c78: ldur            x1, [fp, #-8]
    // 0xdc8c7c: r0 = LoadClassIdInstr(r1)
    //     0xdc8c7c: ldur            x0, [x1, #-1]
    //     0xdc8c80: ubfx            x0, x0, #0xc, #0x14
    // 0xdc8c84: r2 = "updated_at"
    //     0xdc8c84: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0xdc8c88: ldr             x2, [x2, #0xe88]
    // 0xdc8c8c: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc8c8c: sub             lr, x0, #0x114
    //     0xdc8c90: ldr             lr, [x21, lr, lsl #3]
    //     0xdc8c94: blr             lr
    // 0xdc8c98: mov             x3, x0
    // 0xdc8c9c: r2 = Null
    //     0xdc8c9c: mov             x2, NULL
    // 0xdc8ca0: r1 = Null
    //     0xdc8ca0: mov             x1, NULL
    // 0xdc8ca4: stur            x3, [fp, #-8]
    // 0xdc8ca8: r4 = 60
    //     0xdc8ca8: movz            x4, #0x3c
    // 0xdc8cac: branchIfSmi(r0, 0xdc8cb8)
    //     0xdc8cac: tbz             w0, #0, #0xdc8cb8
    // 0xdc8cb0: r4 = LoadClassIdInstr(r0)
    //     0xdc8cb0: ldur            x4, [x0, #-1]
    //     0xdc8cb4: ubfx            x4, x4, #0xc, #0x14
    // 0xdc8cb8: sub             x4, x4, #0x5e
    // 0xdc8cbc: cmp             x4, #1
    // 0xdc8cc0: b.ls            #0xdc8cd4
    // 0xdc8cc4: r8 = String
    //     0xdc8cc4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc8cc8: r3 = Null
    //     0xdc8cc8: add             x3, PP, #0x19, lsl #12  ; [pp+0x19e00] Null
    //     0xdc8ccc: ldr             x3, [x3, #0xe00]
    // 0xdc8cd0: r0 = String()
    //     0xdc8cd0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc8cd4: ldur            x0, [fp, #-0x10]
    // 0xdc8cd8: r1 = LoadInt32Instr(r0)
    //     0xdc8cd8: sbfx            x1, x0, #1, #0x1f
    //     0xdc8cdc: tbz             w0, #0, #0xdc8ce4
    //     0xdc8ce0: ldur            x1, [x0, #7]
    // 0xdc8ce4: stur            x1, [fp, #-0x38]
    // 0xdc8ce8: r0 = DoaCategory()
    //     0xdc8ce8: bl              #0xa610ec  ; AllocateDoaCategoryStub -> DoaCategory (size=0x28)
    // 0xdc8cec: ldur            x1, [fp, #-0x38]
    // 0xdc8cf0: StoreField: r0->field_7 = r1
    //     0xdc8cf0: stur            x1, [x0, #7]
    // 0xdc8cf4: ldur            x1, [fp, #-0x18]
    // 0xdc8cf8: StoreField: r0->field_f = r1
    //     0xdc8cf8: stur            w1, [x0, #0xf]
    // 0xdc8cfc: ldur            x1, [fp, #-0x28]
    // 0xdc8d00: StoreField: r0->field_1b = r1
    //     0xdc8d00: stur            w1, [x0, #0x1b]
    // 0xdc8d04: ldur            x1, [fp, #-0x20]
    // 0xdc8d08: r2 = LoadInt32Instr(r1)
    //     0xdc8d08: sbfx            x2, x1, #1, #0x1f
    //     0xdc8d0c: tbz             w1, #0, #0xdc8d14
    //     0xdc8d10: ldur            x2, [x1, #7]
    // 0xdc8d14: StoreField: r0->field_13 = r2
    //     0xdc8d14: stur            x2, [x0, #0x13]
    // 0xdc8d18: ldur            x1, [fp, #-0x30]
    // 0xdc8d1c: StoreField: r0->field_1f = r1
    //     0xdc8d1c: stur            w1, [x0, #0x1f]
    // 0xdc8d20: ldur            x1, [fp, #-8]
    // 0xdc8d24: StoreField: r0->field_23 = r1
    //     0xdc8d24: stur            w1, [x0, #0x23]
    // 0xdc8d28: LeaveFrame
    //     0xdc8d28: mov             SP, fp
    //     0xdc8d2c: ldp             fp, lr, [SP], #0x10
    // 0xdc8d30: ret
    //     0xdc8d30: ret             
    // 0xdc8d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc8d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc8d38: b               #0xdc8aac
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe79d80, size: 0x180
    // 0xe79d80: EnterFrame
    //     0xe79d80: stp             fp, lr, [SP, #-0x10]!
    //     0xe79d84: mov             fp, SP
    // 0xe79d88: AllocStack(0x20)
    //     0xe79d88: sub             SP, SP, #0x20
    // 0xe79d8c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe79d8c: mov             x3, x1
    //     0xe79d90: stur            x1, [fp, #-8]
    // 0xe79d94: CheckStackOverflow
    //     0xe79d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79d98: cmp             SP, x16
    //     0xe79d9c: b.ls            #0xe79ef8
    // 0xe79da0: mov             x0, x3
    // 0xe79da4: r2 = Null
    //     0xe79da4: mov             x2, NULL
    // 0xe79da8: r1 = Null
    //     0xe79da8: mov             x1, NULL
    // 0xe79dac: cmp             w0, NULL
    // 0xe79db0: b.eq            #0xe79e54
    // 0xe79db4: branchIfSmi(r0, 0xe79e54)
    //     0xe79db4: tbz             w0, #0, #0xe79e54
    // 0xe79db8: r3 = LoadClassIdInstr(r0)
    //     0xe79db8: ldur            x3, [x0, #-1]
    //     0xe79dbc: ubfx            x3, x3, #0xc, #0x14
    // 0xe79dc0: r17 = 6718
    //     0xe79dc0: movz            x17, #0x1a3e
    // 0xe79dc4: cmp             x3, x17
    // 0xe79dc8: b.eq            #0xe79e5c
    // 0xe79dcc: sub             x3, x3, #0x5a
    // 0xe79dd0: cmp             x3, #2
    // 0xe79dd4: b.ls            #0xe79e5c
    // 0xe79dd8: r4 = LoadClassIdInstr(r0)
    //     0xe79dd8: ldur            x4, [x0, #-1]
    //     0xe79ddc: ubfx            x4, x4, #0xc, #0x14
    // 0xe79de0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe79de4: ldr             x3, [x3, #0x18]
    // 0xe79de8: ldr             x3, [x3, x4, lsl #3]
    // 0xe79dec: LoadField: r3 = r3->field_2b
    //     0xe79dec: ldur            w3, [x3, #0x2b]
    // 0xe79df0: DecompressPointer r3
    //     0xe79df0: add             x3, x3, HEAP, lsl #32
    // 0xe79df4: cmp             w3, NULL
    // 0xe79df8: b.eq            #0xe79e54
    // 0xe79dfc: LoadField: r3 = r3->field_f
    //     0xe79dfc: ldur            w3, [x3, #0xf]
    // 0xe79e00: lsr             x3, x3, #3
    // 0xe79e04: r17 = 6718
    //     0xe79e04: movz            x17, #0x1a3e
    // 0xe79e08: cmp             x3, x17
    // 0xe79e0c: b.eq            #0xe79e5c
    // 0xe79e10: r3 = SubtypeTestCache
    //     0xe79e10: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a2f8] SubtypeTestCache
    //     0xe79e14: ldr             x3, [x3, #0x2f8]
    // 0xe79e18: r30 = Subtype1TestCacheStub
    //     0xe79e18: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe79e1c: LoadField: r30 = r30->field_7
    //     0xe79e1c: ldur            lr, [lr, #7]
    // 0xe79e20: blr             lr
    // 0xe79e24: cmp             w7, NULL
    // 0xe79e28: b.eq            #0xe79e34
    // 0xe79e2c: tbnz            w7, #4, #0xe79e54
    // 0xe79e30: b               #0xe79e5c
    // 0xe79e34: r8 = List
    //     0xe79e34: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a300] Type: List
    //     0xe79e38: ldr             x8, [x8, #0x300]
    // 0xe79e3c: r3 = SubtypeTestCache
    //     0xe79e3c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a308] SubtypeTestCache
    //     0xe79e40: ldr             x3, [x3, #0x308]
    // 0xe79e44: r30 = InstanceOfStub
    //     0xe79e44: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe79e48: LoadField: r30 = r30->field_7
    //     0xe79e48: ldur            lr, [lr, #7]
    // 0xe79e4c: blr             lr
    // 0xe79e50: b               #0xe79e60
    // 0xe79e54: r0 = false
    //     0xe79e54: add             x0, NULL, #0x30  ; false
    // 0xe79e58: b               #0xe79e60
    // 0xe79e5c: r0 = true
    //     0xe79e5c: add             x0, NULL, #0x20  ; true
    // 0xe79e60: tbnz            w0, #4, #0xe79ee0
    // 0xe79e64: ldur            x0, [fp, #-8]
    // 0xe79e68: r1 = Function '<anonymous closure>': static.
    //     0xe79e68: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a310] AnonymousClosure: static (0xe79f00), in [package:nuonline/app/data/models/doa.dart] DoaCategory::fromResponse (0xe79d80)
    //     0xe79e6c: ldr             x1, [x1, #0x310]
    // 0xe79e70: r2 = Null
    //     0xe79e70: mov             x2, NULL
    // 0xe79e74: r0 = AllocateClosure()
    //     0xe79e74: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79e78: mov             x1, x0
    // 0xe79e7c: ldur            x0, [fp, #-8]
    // 0xe79e80: r2 = LoadClassIdInstr(r0)
    //     0xe79e80: ldur            x2, [x0, #-1]
    //     0xe79e84: ubfx            x2, x2, #0xc, #0x14
    // 0xe79e88: r16 = <DoaCategory>
    //     0xe79e88: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79e8c: stp             x0, x16, [SP, #8]
    // 0xe79e90: str             x1, [SP]
    // 0xe79e94: mov             x0, x2
    // 0xe79e98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79e98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79e9c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe79e9c: movz            x17, #0xf28c
    //     0xe79ea0: add             lr, x0, x17
    //     0xe79ea4: ldr             lr, [x21, lr, lsl #3]
    //     0xe79ea8: blr             lr
    // 0xe79eac: r1 = LoadClassIdInstr(r0)
    //     0xe79eac: ldur            x1, [x0, #-1]
    //     0xe79eb0: ubfx            x1, x1, #0xc, #0x14
    // 0xe79eb4: mov             x16, x0
    // 0xe79eb8: mov             x0, x1
    // 0xe79ebc: mov             x1, x16
    // 0xe79ec0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe79ec0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe79ec4: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe79ec4: movz            x17, #0xd889
    //     0xe79ec8: add             lr, x0, x17
    //     0xe79ecc: ldr             lr, [x21, lr, lsl #3]
    //     0xe79ed0: blr             lr
    // 0xe79ed4: LeaveFrame
    //     0xe79ed4: mov             SP, fp
    //     0xe79ed8: ldp             fp, lr, [SP], #0x10
    // 0xe79edc: ret
    //     0xe79edc: ret             
    // 0xe79ee0: r1 = <DoaCategory>
    //     0xe79ee0: ldr             x1, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79ee4: r2 = 0
    //     0xe79ee4: movz            x2, #0
    // 0xe79ee8: r0 = _GrowableList()
    //     0xe79ee8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe79eec: LeaveFrame
    //     0xe79eec: mov             SP, fp
    //     0xe79ef0: ldp             fp, lr, [SP], #0x10
    // 0xe79ef4: ret
    //     0xe79ef4: ret             
    // 0xe79ef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79ef8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79efc: b               #0xe79da0
  }
  [closure] static DoaCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe79f00, size: 0x50
    // 0xe79f00: EnterFrame
    //     0xe79f00: stp             fp, lr, [SP, #-0x10]!
    //     0xe79f04: mov             fp, SP
    // 0xe79f08: CheckStackOverflow
    //     0xe79f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79f0c: cmp             SP, x16
    //     0xe79f10: b.ls            #0xe79f48
    // 0xe79f14: ldr             x0, [fp, #0x10]
    // 0xe79f18: r2 = Null
    //     0xe79f18: mov             x2, NULL
    // 0xe79f1c: r1 = Null
    //     0xe79f1c: mov             x1, NULL
    // 0xe79f20: r8 = Map<String, dynamic>
    //     0xe79f20: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe79f24: r3 = Null
    //     0xe79f24: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a318] Null
    //     0xe79f28: ldr             x3, [x3, #0x318]
    // 0xe79f2c: r0 = Map<String, dynamic>()
    //     0xe79f2c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe79f30: ldr             x2, [fp, #0x10]
    // 0xe79f34: r1 = Null
    //     0xe79f34: mov             x1, NULL
    // 0xe79f38: r0 = DoaCategory.fromMap()
    //     0xe79f38: bl              #0xdc8a8c  ; [package:nuonline/app/data/models/doa.dart] DoaCategory::DoaCategory.fromMap
    // 0xe79f3c: LeaveFrame
    //     0xe79f3c: mov             SP, fp
    //     0xe79f40: ldp             fp, lr, [SP], #0x10
    // 0xe79f44: ret
    //     0xe79f44: ret             
    // 0xe79f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79f4c: b               #0xe79f14
  }
}

// class id: 5594, size: 0x38, field offset: 0x8
//   const constructor, 
class Doa extends Equatable {

  factory Doa Doa.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0xdc7ae4, size: 0x394
    // 0xdc7ae4: EnterFrame
    //     0xdc7ae4: stp             fp, lr, [SP, #-0x10]!
    //     0xdc7ae8: mov             fp, SP
    // 0xdc7aec: AllocStack(0x48)
    //     0xdc7aec: sub             SP, SP, #0x48
    // 0xdc7af0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xdc7af0: mov             x3, x2
    //     0xdc7af4: stur            x2, [fp, #-8]
    // 0xdc7af8: CheckStackOverflow
    //     0xdc7af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc7afc: cmp             SP, x16
    //     0xdc7b00: b.ls            #0xdc7e70
    // 0xdc7b04: r0 = LoadClassIdInstr(r3)
    //     0xdc7b04: ldur            x0, [x3, #-1]
    //     0xdc7b08: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7b0c: mov             x1, x3
    // 0xdc7b10: r2 = "id"
    //     0xdc7b10: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc7b14: ldr             x2, [x2, #0x740]
    // 0xdc7b18: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7b18: sub             lr, x0, #0x114
    //     0xdc7b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7b20: blr             lr
    // 0xdc7b24: mov             x3, x0
    // 0xdc7b28: r2 = Null
    //     0xdc7b28: mov             x2, NULL
    // 0xdc7b2c: r1 = Null
    //     0xdc7b2c: mov             x1, NULL
    // 0xdc7b30: stur            x3, [fp, #-0x10]
    // 0xdc7b34: branchIfSmi(r0, 0xdc7b5c)
    //     0xdc7b34: tbz             w0, #0, #0xdc7b5c
    // 0xdc7b38: r4 = LoadClassIdInstr(r0)
    //     0xdc7b38: ldur            x4, [x0, #-1]
    //     0xdc7b3c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7b40: sub             x4, x4, #0x3c
    // 0xdc7b44: cmp             x4, #1
    // 0xdc7b48: b.ls            #0xdc7b5c
    // 0xdc7b4c: r8 = int
    //     0xdc7b4c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc7b50: r3 = Null
    //     0xdc7b50: add             x3, PP, #0x17, lsl #12  ; [pp+0x17df8] Null
    //     0xdc7b54: ldr             x3, [x3, #0xdf8]
    // 0xdc7b58: r0 = int()
    //     0xdc7b58: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc7b5c: ldur            x3, [fp, #-8]
    // 0xdc7b60: r0 = LoadClassIdInstr(r3)
    //     0xdc7b60: ldur            x0, [x3, #-1]
    //     0xdc7b64: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7b68: mov             x1, x3
    // 0xdc7b6c: r2 = "type"
    //     0xdc7b6c: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0xdc7b70: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7b70: sub             lr, x0, #0x114
    //     0xdc7b74: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7b78: blr             lr
    // 0xdc7b7c: mov             x3, x0
    // 0xdc7b80: r2 = Null
    //     0xdc7b80: mov             x2, NULL
    // 0xdc7b84: r1 = Null
    //     0xdc7b84: mov             x1, NULL
    // 0xdc7b88: stur            x3, [fp, #-0x18]
    // 0xdc7b8c: branchIfSmi(r0, 0xdc7bb4)
    //     0xdc7b8c: tbz             w0, #0, #0xdc7bb4
    // 0xdc7b90: r4 = LoadClassIdInstr(r0)
    //     0xdc7b90: ldur            x4, [x0, #-1]
    //     0xdc7b94: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7b98: sub             x4, x4, #0x3c
    // 0xdc7b9c: cmp             x4, #1
    // 0xdc7ba0: b.ls            #0xdc7bb4
    // 0xdc7ba4: r8 = int
    //     0xdc7ba4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc7ba8: r3 = Null
    //     0xdc7ba8: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e08] Null
    //     0xdc7bac: ldr             x3, [x3, #0xe08]
    // 0xdc7bb0: r0 = int()
    //     0xdc7bb0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc7bb4: ldur            x3, [fp, #-8]
    // 0xdc7bb8: r0 = LoadClassIdInstr(r3)
    //     0xdc7bb8: ldur            x0, [x3, #-1]
    //     0xdc7bbc: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7bc0: mov             x1, x3
    // 0xdc7bc4: r2 = "order"
    //     0xdc7bc4: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0xdc7bc8: ldr             x2, [x2, #0xb78]
    // 0xdc7bcc: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7bcc: sub             lr, x0, #0x114
    //     0xdc7bd0: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7bd4: blr             lr
    // 0xdc7bd8: mov             x3, x0
    // 0xdc7bdc: r2 = Null
    //     0xdc7bdc: mov             x2, NULL
    // 0xdc7be0: r1 = Null
    //     0xdc7be0: mov             x1, NULL
    // 0xdc7be4: stur            x3, [fp, #-0x20]
    // 0xdc7be8: branchIfSmi(r0, 0xdc7c10)
    //     0xdc7be8: tbz             w0, #0, #0xdc7c10
    // 0xdc7bec: r4 = LoadClassIdInstr(r0)
    //     0xdc7bec: ldur            x4, [x0, #-1]
    //     0xdc7bf0: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7bf4: sub             x4, x4, #0x3c
    // 0xdc7bf8: cmp             x4, #1
    // 0xdc7bfc: b.ls            #0xdc7c10
    // 0xdc7c00: r8 = int
    //     0xdc7c00: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc7c04: r3 = Null
    //     0xdc7c04: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e18] Null
    //     0xdc7c08: ldr             x3, [x3, #0xe18]
    // 0xdc7c0c: r0 = int()
    //     0xdc7c0c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc7c10: ldur            x3, [fp, #-8]
    // 0xdc7c14: r0 = LoadClassIdInstr(r3)
    //     0xdc7c14: ldur            x0, [x3, #-1]
    //     0xdc7c18: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7c1c: mov             x1, x3
    // 0xdc7c20: r2 = "arabic"
    //     0xdc7c20: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e28] "arabic"
    //     0xdc7c24: ldr             x2, [x2, #0xe28]
    // 0xdc7c28: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7c28: sub             lr, x0, #0x114
    //     0xdc7c2c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7c30: blr             lr
    // 0xdc7c34: mov             x3, x0
    // 0xdc7c38: r2 = Null
    //     0xdc7c38: mov             x2, NULL
    // 0xdc7c3c: r1 = Null
    //     0xdc7c3c: mov             x1, NULL
    // 0xdc7c40: stur            x3, [fp, #-0x28]
    // 0xdc7c44: r4 = 60
    //     0xdc7c44: movz            x4, #0x3c
    // 0xdc7c48: branchIfSmi(r0, 0xdc7c54)
    //     0xdc7c48: tbz             w0, #0, #0xdc7c54
    // 0xdc7c4c: r4 = LoadClassIdInstr(r0)
    //     0xdc7c4c: ldur            x4, [x0, #-1]
    //     0xdc7c50: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7c54: sub             x4, x4, #0x5e
    // 0xdc7c58: cmp             x4, #1
    // 0xdc7c5c: b.ls            #0xdc7c70
    // 0xdc7c60: r8 = String
    //     0xdc7c60: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc7c64: r3 = Null
    //     0xdc7c64: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e30] Null
    //     0xdc7c68: ldr             x3, [x3, #0xe30]
    // 0xdc7c6c: r0 = String()
    //     0xdc7c6c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc7c70: ldur            x3, [fp, #-8]
    // 0xdc7c74: r0 = LoadClassIdInstr(r3)
    //     0xdc7c74: ldur            x0, [x3, #-1]
    //     0xdc7c78: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7c7c: mov             x1, x3
    // 0xdc7c80: r2 = "translate"
    //     0xdc7c80: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e40] "translate"
    //     0xdc7c84: ldr             x2, [x2, #0xe40]
    // 0xdc7c88: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7c88: sub             lr, x0, #0x114
    //     0xdc7c8c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7c90: blr             lr
    // 0xdc7c94: mov             x3, x0
    // 0xdc7c98: r2 = Null
    //     0xdc7c98: mov             x2, NULL
    // 0xdc7c9c: r1 = Null
    //     0xdc7c9c: mov             x1, NULL
    // 0xdc7ca0: stur            x3, [fp, #-0x30]
    // 0xdc7ca4: r4 = 60
    //     0xdc7ca4: movz            x4, #0x3c
    // 0xdc7ca8: branchIfSmi(r0, 0xdc7cb4)
    //     0xdc7ca8: tbz             w0, #0, #0xdc7cb4
    // 0xdc7cac: r4 = LoadClassIdInstr(r0)
    //     0xdc7cac: ldur            x4, [x0, #-1]
    //     0xdc7cb0: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7cb4: sub             x4, x4, #0x5e
    // 0xdc7cb8: cmp             x4, #1
    // 0xdc7cbc: b.ls            #0xdc7cd0
    // 0xdc7cc0: r8 = String?
    //     0xdc7cc0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xdc7cc4: r3 = Null
    //     0xdc7cc4: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e48] Null
    //     0xdc7cc8: ldr             x3, [x3, #0xe48]
    // 0xdc7ccc: r0 = String?()
    //     0xdc7ccc: bl              #0x600324  ; IsType_String?_Stub
    // 0xdc7cd0: ldur            x3, [fp, #-8]
    // 0xdc7cd4: r0 = LoadClassIdInstr(r3)
    //     0xdc7cd4: ldur            x0, [x3, #-1]
    //     0xdc7cd8: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7cdc: mov             x1, x3
    // 0xdc7ce0: r2 = "transliteration"
    //     0xdc7ce0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0xdc7ce4: ldr             x2, [x2, #0xe58]
    // 0xdc7ce8: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7ce8: sub             lr, x0, #0x114
    //     0xdc7cec: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7cf0: blr             lr
    // 0xdc7cf4: mov             x3, x0
    // 0xdc7cf8: r2 = Null
    //     0xdc7cf8: mov             x2, NULL
    // 0xdc7cfc: r1 = Null
    //     0xdc7cfc: mov             x1, NULL
    // 0xdc7d00: stur            x3, [fp, #-0x38]
    // 0xdc7d04: r4 = 60
    //     0xdc7d04: movz            x4, #0x3c
    // 0xdc7d08: branchIfSmi(r0, 0xdc7d14)
    //     0xdc7d08: tbz             w0, #0, #0xdc7d14
    // 0xdc7d0c: r4 = LoadClassIdInstr(r0)
    //     0xdc7d0c: ldur            x4, [x0, #-1]
    //     0xdc7d10: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7d14: sub             x4, x4, #0x5e
    // 0xdc7d18: cmp             x4, #1
    // 0xdc7d1c: b.ls            #0xdc7d30
    // 0xdc7d20: r8 = String?
    //     0xdc7d20: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xdc7d24: r3 = Null
    //     0xdc7d24: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e60] Null
    //     0xdc7d28: ldr             x3, [x3, #0xe60]
    // 0xdc7d2c: r0 = String?()
    //     0xdc7d2c: bl              #0x600324  ; IsType_String?_Stub
    // 0xdc7d30: ldur            x3, [fp, #-8]
    // 0xdc7d34: r0 = LoadClassIdInstr(r3)
    //     0xdc7d34: ldur            x0, [x3, #-1]
    //     0xdc7d38: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7d3c: mov             x1, x3
    // 0xdc7d40: r2 = "category_id"
    //     0xdc7d40: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0xdc7d44: ldr             x2, [x2, #0xe70]
    // 0xdc7d48: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7d48: sub             lr, x0, #0x114
    //     0xdc7d4c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7d50: blr             lr
    // 0xdc7d54: mov             x3, x0
    // 0xdc7d58: r2 = Null
    //     0xdc7d58: mov             x2, NULL
    // 0xdc7d5c: r1 = Null
    //     0xdc7d5c: mov             x1, NULL
    // 0xdc7d60: stur            x3, [fp, #-0x40]
    // 0xdc7d64: branchIfSmi(r0, 0xdc7d8c)
    //     0xdc7d64: tbz             w0, #0, #0xdc7d8c
    // 0xdc7d68: r4 = LoadClassIdInstr(r0)
    //     0xdc7d68: ldur            x4, [x0, #-1]
    //     0xdc7d6c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7d70: sub             x4, x4, #0x3c
    // 0xdc7d74: cmp             x4, #1
    // 0xdc7d78: b.ls            #0xdc7d8c
    // 0xdc7d7c: r8 = int
    //     0xdc7d7c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc7d80: r3 = Null
    //     0xdc7d80: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e78] Null
    //     0xdc7d84: ldr             x3, [x3, #0xe78]
    // 0xdc7d88: r0 = int()
    //     0xdc7d88: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc7d8c: ldur            x1, [fp, #-8]
    // 0xdc7d90: r0 = LoadClassIdInstr(r1)
    //     0xdc7d90: ldur            x0, [x1, #-1]
    //     0xdc7d94: ubfx            x0, x0, #0xc, #0x14
    // 0xdc7d98: r2 = "updated_at"
    //     0xdc7d98: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0xdc7d9c: ldr             x2, [x2, #0xe88]
    // 0xdc7da0: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc7da0: sub             lr, x0, #0x114
    //     0xdc7da4: ldr             lr, [x21, lr, lsl #3]
    //     0xdc7da8: blr             lr
    // 0xdc7dac: mov             x3, x0
    // 0xdc7db0: r2 = Null
    //     0xdc7db0: mov             x2, NULL
    // 0xdc7db4: r1 = Null
    //     0xdc7db4: mov             x1, NULL
    // 0xdc7db8: stur            x3, [fp, #-8]
    // 0xdc7dbc: r4 = 60
    //     0xdc7dbc: movz            x4, #0x3c
    // 0xdc7dc0: branchIfSmi(r0, 0xdc7dcc)
    //     0xdc7dc0: tbz             w0, #0, #0xdc7dcc
    // 0xdc7dc4: r4 = LoadClassIdInstr(r0)
    //     0xdc7dc4: ldur            x4, [x0, #-1]
    //     0xdc7dc8: ubfx            x4, x4, #0xc, #0x14
    // 0xdc7dcc: sub             x4, x4, #0x5e
    // 0xdc7dd0: cmp             x4, #1
    // 0xdc7dd4: b.ls            #0xdc7de8
    // 0xdc7dd8: r8 = String
    //     0xdc7dd8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc7ddc: r3 = Null
    //     0xdc7ddc: add             x3, PP, #0x17, lsl #12  ; [pp+0x17e90] Null
    //     0xdc7de0: ldr             x3, [x3, #0xe90]
    // 0xdc7de4: r0 = String()
    //     0xdc7de4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc7de8: ldur            x0, [fp, #-0x10]
    // 0xdc7dec: r1 = LoadInt32Instr(r0)
    //     0xdc7dec: sbfx            x1, x0, #1, #0x1f
    //     0xdc7df0: tbz             w0, #0, #0xdc7df8
    //     0xdc7df4: ldur            x1, [x0, #7]
    // 0xdc7df8: stur            x1, [fp, #-0x48]
    // 0xdc7dfc: r0 = Doa()
    //     0xdc7dfc: bl              #0xa60c18  ; AllocateDoaStub -> Doa (size=0x38)
    // 0xdc7e00: ldur            x1, [fp, #-0x48]
    // 0xdc7e04: StoreField: r0->field_7 = r1
    //     0xdc7e04: stur            x1, [x0, #7]
    // 0xdc7e08: ldur            x1, [fp, #-0x18]
    // 0xdc7e0c: r2 = LoadInt32Instr(r1)
    //     0xdc7e0c: sbfx            x2, x1, #1, #0x1f
    //     0xdc7e10: tbz             w1, #0, #0xdc7e18
    //     0xdc7e14: ldur            x2, [x1, #7]
    // 0xdc7e18: StoreField: r0->field_f = r2
    //     0xdc7e18: stur            x2, [x0, #0xf]
    // 0xdc7e1c: ldur            x1, [fp, #-0x20]
    // 0xdc7e20: r2 = LoadInt32Instr(r1)
    //     0xdc7e20: sbfx            x2, x1, #1, #0x1f
    //     0xdc7e24: tbz             w1, #0, #0xdc7e2c
    //     0xdc7e28: ldur            x2, [x1, #7]
    // 0xdc7e2c: ArrayStore: r0[0] = r2  ; List_8
    //     0xdc7e2c: stur            x2, [x0, #0x17]
    // 0xdc7e30: ldur            x1, [fp, #-0x28]
    // 0xdc7e34: StoreField: r0->field_1f = r1
    //     0xdc7e34: stur            w1, [x0, #0x1f]
    // 0xdc7e38: ldur            x1, [fp, #-0x40]
    // 0xdc7e3c: r2 = LoadInt32Instr(r1)
    //     0xdc7e3c: sbfx            x2, x1, #1, #0x1f
    //     0xdc7e40: tbz             w1, #0, #0xdc7e48
    //     0xdc7e44: ldur            x2, [x1, #7]
    // 0xdc7e48: StoreField: r0->field_2b = r2
    //     0xdc7e48: stur            x2, [x0, #0x2b]
    // 0xdc7e4c: ldur            x1, [fp, #-0x30]
    // 0xdc7e50: StoreField: r0->field_23 = r1
    //     0xdc7e50: stur            w1, [x0, #0x23]
    // 0xdc7e54: ldur            x1, [fp, #-0x38]
    // 0xdc7e58: StoreField: r0->field_27 = r1
    //     0xdc7e58: stur            w1, [x0, #0x27]
    // 0xdc7e5c: ldur            x1, [fp, #-8]
    // 0xdc7e60: StoreField: r0->field_33 = r1
    //     0xdc7e60: stur            w1, [x0, #0x33]
    // 0xdc7e64: LeaveFrame
    //     0xdc7e64: mov             SP, fp
    //     0xdc7e68: ldp             fp, lr, [SP], #0x10
    // 0xdc7e6c: ret
    //     0xdc7e6c: ret             
    // 0xdc7e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc7e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc7e74: b               #0xdc7b04
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe774a4, size: 0x180
    // 0xe774a4: EnterFrame
    //     0xe774a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe774a8: mov             fp, SP
    // 0xe774ac: AllocStack(0x20)
    //     0xe774ac: sub             SP, SP, #0x20
    // 0xe774b0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe774b0: mov             x3, x1
    //     0xe774b4: stur            x1, [fp, #-8]
    // 0xe774b8: CheckStackOverflow
    //     0xe774b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe774bc: cmp             SP, x16
    //     0xe774c0: b.ls            #0xe7761c
    // 0xe774c4: mov             x0, x3
    // 0xe774c8: r2 = Null
    //     0xe774c8: mov             x2, NULL
    // 0xe774cc: r1 = Null
    //     0xe774cc: mov             x1, NULL
    // 0xe774d0: cmp             w0, NULL
    // 0xe774d4: b.eq            #0xe77578
    // 0xe774d8: branchIfSmi(r0, 0xe77578)
    //     0xe774d8: tbz             w0, #0, #0xe77578
    // 0xe774dc: r3 = LoadClassIdInstr(r0)
    //     0xe774dc: ldur            x3, [x0, #-1]
    //     0xe774e0: ubfx            x3, x3, #0xc, #0x14
    // 0xe774e4: r17 = 6718
    //     0xe774e4: movz            x17, #0x1a3e
    // 0xe774e8: cmp             x3, x17
    // 0xe774ec: b.eq            #0xe77580
    // 0xe774f0: sub             x3, x3, #0x5a
    // 0xe774f4: cmp             x3, #2
    // 0xe774f8: b.ls            #0xe77580
    // 0xe774fc: r4 = LoadClassIdInstr(r0)
    //     0xe774fc: ldur            x4, [x0, #-1]
    //     0xe77500: ubfx            x4, x4, #0xc, #0x14
    // 0xe77504: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe77508: ldr             x3, [x3, #0x18]
    // 0xe7750c: ldr             x3, [x3, x4, lsl #3]
    // 0xe77510: LoadField: r3 = r3->field_2b
    //     0xe77510: ldur            w3, [x3, #0x2b]
    // 0xe77514: DecompressPointer r3
    //     0xe77514: add             x3, x3, HEAP, lsl #32
    // 0xe77518: cmp             w3, NULL
    // 0xe7751c: b.eq            #0xe77578
    // 0xe77520: LoadField: r3 = r3->field_f
    //     0xe77520: ldur            w3, [x3, #0xf]
    // 0xe77524: lsr             x3, x3, #3
    // 0xe77528: r17 = 6718
    //     0xe77528: movz            x17, #0x1a3e
    // 0xe7752c: cmp             x3, x17
    // 0xe77530: b.eq            #0xe77580
    // 0xe77534: r3 = SubtypeTestCache
    //     0xe77534: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a338] SubtypeTestCache
    //     0xe77538: ldr             x3, [x3, #0x338]
    // 0xe7753c: r30 = Subtype1TestCacheStub
    //     0xe7753c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe77540: LoadField: r30 = r30->field_7
    //     0xe77540: ldur            lr, [lr, #7]
    // 0xe77544: blr             lr
    // 0xe77548: cmp             w7, NULL
    // 0xe7754c: b.eq            #0xe77558
    // 0xe77550: tbnz            w7, #4, #0xe77578
    // 0xe77554: b               #0xe77580
    // 0xe77558: r8 = List
    //     0xe77558: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a340] Type: List
    //     0xe7755c: ldr             x8, [x8, #0x340]
    // 0xe77560: r3 = SubtypeTestCache
    //     0xe77560: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a348] SubtypeTestCache
    //     0xe77564: ldr             x3, [x3, #0x348]
    // 0xe77568: r30 = InstanceOfStub
    //     0xe77568: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe7756c: LoadField: r30 = r30->field_7
    //     0xe7756c: ldur            lr, [lr, #7]
    // 0xe77570: blr             lr
    // 0xe77574: b               #0xe77584
    // 0xe77578: r0 = false
    //     0xe77578: add             x0, NULL, #0x30  ; false
    // 0xe7757c: b               #0xe77584
    // 0xe77580: r0 = true
    //     0xe77580: add             x0, NULL, #0x20  ; true
    // 0xe77584: tbnz            w0, #4, #0xe77604
    // 0xe77588: ldur            x0, [fp, #-8]
    // 0xe7758c: r1 = Function '<anonymous closure>': static.
    //     0xe7758c: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a350] AnonymousClosure: static (0xe77624), in [package:nuonline/app/data/models/doa.dart] Doa::fromResponse (0xe774a4)
    //     0xe77590: ldr             x1, [x1, #0x350]
    // 0xe77594: r2 = Null
    //     0xe77594: mov             x2, NULL
    // 0xe77598: r0 = AllocateClosure()
    //     0xe77598: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7759c: mov             x1, x0
    // 0xe775a0: ldur            x0, [fp, #-8]
    // 0xe775a4: r2 = LoadClassIdInstr(r0)
    //     0xe775a4: ldur            x2, [x0, #-1]
    //     0xe775a8: ubfx            x2, x2, #0xc, #0x14
    // 0xe775ac: r16 = <Doa>
    //     0xe775ac: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe775b0: stp             x0, x16, [SP, #8]
    // 0xe775b4: str             x1, [SP]
    // 0xe775b8: mov             x0, x2
    // 0xe775bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe775bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe775c0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe775c0: movz            x17, #0xf28c
    //     0xe775c4: add             lr, x0, x17
    //     0xe775c8: ldr             lr, [x21, lr, lsl #3]
    //     0xe775cc: blr             lr
    // 0xe775d0: r1 = LoadClassIdInstr(r0)
    //     0xe775d0: ldur            x1, [x0, #-1]
    //     0xe775d4: ubfx            x1, x1, #0xc, #0x14
    // 0xe775d8: mov             x16, x0
    // 0xe775dc: mov             x0, x1
    // 0xe775e0: mov             x1, x16
    // 0xe775e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe775e4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe775e8: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe775e8: movz            x17, #0xd889
    //     0xe775ec: add             lr, x0, x17
    //     0xe775f0: ldr             lr, [x21, lr, lsl #3]
    //     0xe775f4: blr             lr
    // 0xe775f8: LeaveFrame
    //     0xe775f8: mov             SP, fp
    //     0xe775fc: ldp             fp, lr, [SP], #0x10
    // 0xe77600: ret
    //     0xe77600: ret             
    // 0xe77604: r1 = <Doa>
    //     0xe77604: ldr             x1, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77608: r2 = 0
    //     0xe77608: movz            x2, #0
    // 0xe7760c: r0 = _GrowableList()
    //     0xe7760c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe77610: LeaveFrame
    //     0xe77610: mov             SP, fp
    //     0xe77614: ldp             fp, lr, [SP], #0x10
    // 0xe77618: ret
    //     0xe77618: ret             
    // 0xe7761c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7761c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77620: b               #0xe774c4
  }
  [closure] static Doa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe77624, size: 0x50
    // 0xe77624: EnterFrame
    //     0xe77624: stp             fp, lr, [SP, #-0x10]!
    //     0xe77628: mov             fp, SP
    // 0xe7762c: CheckStackOverflow
    //     0xe7762c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77630: cmp             SP, x16
    //     0xe77634: b.ls            #0xe7766c
    // 0xe77638: ldr             x0, [fp, #0x10]
    // 0xe7763c: r2 = Null
    //     0xe7763c: mov             x2, NULL
    // 0xe77640: r1 = Null
    //     0xe77640: mov             x1, NULL
    // 0xe77644: r8 = Map<String, dynamic>
    //     0xe77644: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe77648: r3 = Null
    //     0xe77648: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a358] Null
    //     0xe7764c: ldr             x3, [x3, #0x358]
    // 0xe77650: r0 = Map<String, dynamic>()
    //     0xe77650: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe77654: ldr             x2, [fp, #0x10]
    // 0xe77658: r1 = Null
    //     0xe77658: mov             x1, NULL
    // 0xe7765c: r0 = Doa.fromMap()
    //     0xe7765c: bl              #0xdc7ae4  ; [package:nuonline/app/data/models/doa.dart] Doa::Doa.fromMap
    // 0xe77660: LeaveFrame
    //     0xe77660: mov             SP, fp
    //     0xe77664: ldp             fp, lr, [SP], #0x10
    // 0xe77668: ret
    //     0xe77668: ret             
    // 0xe7766c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7766c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77670: b               #0xe77638
  }
}
