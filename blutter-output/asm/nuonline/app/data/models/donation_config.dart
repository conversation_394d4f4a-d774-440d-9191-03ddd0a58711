// lib: , url: package:nuonline/app/data/models/donation_config.dart

// class id: 1050016, size: 0x8
class :: {
}

// class id: 1146, size: 0x38, field offset: 0x8
class DonationConfig extends Object {

  factory _ DonationConfig.fromJson(/* No info */) {
    // ** addr: 0x91707c, size: 0x3a0
    // 0x91707c: EnterFrame
    //     0x91707c: stp             fp, lr, [SP, #-0x10]!
    //     0x917080: mov             fp, SP
    // 0x917084: AllocStack(0x38)
    //     0x917084: sub             SP, SP, #0x38
    // 0x917088: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x917088: mov             x0, x2
    //     0x91708c: stur            x2, [fp, #-8]
    // 0x917090: CheckStackOverflow
    //     0x917090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x917094: cmp             SP, x16
    //     0x917098: b.ls            #0x917414
    // 0x91709c: mov             x1, x0
    // 0x9170a0: r2 = "force_show"
    //     0x9170a0: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f5c8] "force_show"
    //     0x9170a4: ldr             x2, [x2, #0x5c8]
    // 0x9170a8: r0 = _getValueOrData()
    //     0x9170a8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9170ac: ldur            x3, [fp, #-8]
    // 0x9170b0: LoadField: r1 = r3->field_f
    //     0x9170b0: ldur            w1, [x3, #0xf]
    // 0x9170b4: DecompressPointer r1
    //     0x9170b4: add             x1, x1, HEAP, lsl #32
    // 0x9170b8: cmp             w1, w0
    // 0x9170bc: b.ne            #0x9170c8
    // 0x9170c0: r4 = Null
    //     0x9170c0: mov             x4, NULL
    // 0x9170c4: b               #0x9170cc
    // 0x9170c8: mov             x4, x0
    // 0x9170cc: mov             x0, x4
    // 0x9170d0: stur            x4, [fp, #-0x10]
    // 0x9170d4: r2 = Null
    //     0x9170d4: mov             x2, NULL
    // 0x9170d8: r1 = Null
    //     0x9170d8: mov             x1, NULL
    // 0x9170dc: r4 = 60
    //     0x9170dc: movz            x4, #0x3c
    // 0x9170e0: branchIfSmi(r0, 0x9170ec)
    //     0x9170e0: tbz             w0, #0, #0x9170ec
    // 0x9170e4: r4 = LoadClassIdInstr(r0)
    //     0x9170e4: ldur            x4, [x0, #-1]
    //     0x9170e8: ubfx            x4, x4, #0xc, #0x14
    // 0x9170ec: cmp             x4, #0x3f
    // 0x9170f0: b.eq            #0x917104
    // 0x9170f4: r8 = bool
    //     0x9170f4: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x9170f8: r3 = Null
    //     0x9170f8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f5d0] Null
    //     0x9170fc: ldr             x3, [x3, #0x5d0]
    // 0x917100: r0 = bool()
    //     0x917100: bl              #0xed4390  ; IsType_bool_Stub
    // 0x917104: ldur            x1, [fp, #-8]
    // 0x917108: r2 = "max_count_show"
    //     0x917108: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f5e0] "max_count_show"
    //     0x91710c: ldr             x2, [x2, #0x5e0]
    // 0x917110: r0 = _getValueOrData()
    //     0x917110: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x917114: ldur            x3, [fp, #-8]
    // 0x917118: LoadField: r1 = r3->field_f
    //     0x917118: ldur            w1, [x3, #0xf]
    // 0x91711c: DecompressPointer r1
    //     0x91711c: add             x1, x1, HEAP, lsl #32
    // 0x917120: cmp             w1, w0
    // 0x917124: b.ne            #0x917130
    // 0x917128: r4 = Null
    //     0x917128: mov             x4, NULL
    // 0x91712c: b               #0x917134
    // 0x917130: mov             x4, x0
    // 0x917134: mov             x0, x4
    // 0x917138: stur            x4, [fp, #-0x18]
    // 0x91713c: r2 = Null
    //     0x91713c: mov             x2, NULL
    // 0x917140: r1 = Null
    //     0x917140: mov             x1, NULL
    // 0x917144: branchIfSmi(r0, 0x91716c)
    //     0x917144: tbz             w0, #0, #0x91716c
    // 0x917148: r4 = LoadClassIdInstr(r0)
    //     0x917148: ldur            x4, [x0, #-1]
    //     0x91714c: ubfx            x4, x4, #0xc, #0x14
    // 0x917150: sub             x4, x4, #0x3c
    // 0x917154: cmp             x4, #1
    // 0x917158: b.ls            #0x91716c
    // 0x91715c: r8 = int
    //     0x91715c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x917160: r3 = Null
    //     0x917160: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f5e8] Null
    //     0x917164: ldr             x3, [x3, #0x5e8]
    // 0x917168: r0 = int()
    //     0x917168: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91716c: ldur            x1, [fp, #-8]
    // 0x917170: r2 = "duration_show"
    //     0x917170: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f5f8] "duration_show"
    //     0x917174: ldr             x2, [x2, #0x5f8]
    // 0x917178: r0 = _getValueOrData()
    //     0x917178: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91717c: ldur            x3, [fp, #-8]
    // 0x917180: LoadField: r1 = r3->field_f
    //     0x917180: ldur            w1, [x3, #0xf]
    // 0x917184: DecompressPointer r1
    //     0x917184: add             x1, x1, HEAP, lsl #32
    // 0x917188: cmp             w1, w0
    // 0x91718c: b.ne            #0x917198
    // 0x917190: r4 = Null
    //     0x917190: mov             x4, NULL
    // 0x917194: b               #0x91719c
    // 0x917198: mov             x4, x0
    // 0x91719c: mov             x0, x4
    // 0x9171a0: stur            x4, [fp, #-0x20]
    // 0x9171a4: r2 = Null
    //     0x9171a4: mov             x2, NULL
    // 0x9171a8: r1 = Null
    //     0x9171a8: mov             x1, NULL
    // 0x9171ac: branchIfSmi(r0, 0x9171d4)
    //     0x9171ac: tbz             w0, #0, #0x9171d4
    // 0x9171b0: r4 = LoadClassIdInstr(r0)
    //     0x9171b0: ldur            x4, [x0, #-1]
    //     0x9171b4: ubfx            x4, x4, #0xc, #0x14
    // 0x9171b8: sub             x4, x4, #0x3c
    // 0x9171bc: cmp             x4, #1
    // 0x9171c0: b.ls            #0x9171d4
    // 0x9171c4: r8 = int
    //     0x9171c4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x9171c8: r3 = Null
    //     0x9171c8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f600] Null
    //     0x9171cc: ldr             x3, [x3, #0x600]
    // 0x9171d0: r0 = int()
    //     0x9171d0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x9171d4: ldur            x1, [fp, #-8]
    // 0x9171d8: r2 = "day_force_show"
    //     0x9171d8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f610] "day_force_show"
    //     0x9171dc: ldr             x2, [x2, #0x610]
    // 0x9171e0: r0 = _getValueOrData()
    //     0x9171e0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9171e4: ldur            x3, [fp, #-8]
    // 0x9171e8: LoadField: r1 = r3->field_f
    //     0x9171e8: ldur            w1, [x3, #0xf]
    // 0x9171ec: DecompressPointer r1
    //     0x9171ec: add             x1, x1, HEAP, lsl #32
    // 0x9171f0: cmp             w1, w0
    // 0x9171f4: b.ne            #0x917200
    // 0x9171f8: r4 = Null
    //     0x9171f8: mov             x4, NULL
    // 0x9171fc: b               #0x917204
    // 0x917200: mov             x4, x0
    // 0x917204: mov             x0, x4
    // 0x917208: stur            x4, [fp, #-0x28]
    // 0x91720c: r2 = Null
    //     0x91720c: mov             x2, NULL
    // 0x917210: r1 = Null
    //     0x917210: mov             x1, NULL
    // 0x917214: r4 = 60
    //     0x917214: movz            x4, #0x3c
    // 0x917218: branchIfSmi(r0, 0x917224)
    //     0x917218: tbz             w0, #0, #0x917224
    // 0x91721c: r4 = LoadClassIdInstr(r0)
    //     0x91721c: ldur            x4, [x0, #-1]
    //     0x917220: ubfx            x4, x4, #0xc, #0x14
    // 0x917224: cmp             x4, #0x3f
    // 0x917228: b.eq            #0x91723c
    // 0x91722c: r8 = bool
    //     0x91722c: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x917230: r3 = Null
    //     0x917230: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f618] Null
    //     0x917234: ldr             x3, [x3, #0x618]
    // 0x917238: r0 = bool()
    //     0x917238: bl              #0xed4390  ; IsType_bool_Stub
    // 0x91723c: ldur            x1, [fp, #-8]
    // 0x917240: r2 = "day_show"
    //     0x917240: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f628] "day_show"
    //     0x917244: ldr             x2, [x2, #0x628]
    // 0x917248: r0 = _getValueOrData()
    //     0x917248: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91724c: ldur            x3, [fp, #-8]
    // 0x917250: LoadField: r1 = r3->field_f
    //     0x917250: ldur            w1, [x3, #0xf]
    // 0x917254: DecompressPointer r1
    //     0x917254: add             x1, x1, HEAP, lsl #32
    // 0x917258: cmp             w1, w0
    // 0x91725c: b.ne            #0x917268
    // 0x917260: r4 = Null
    //     0x917260: mov             x4, NULL
    // 0x917264: b               #0x91726c
    // 0x917268: mov             x4, x0
    // 0x91726c: mov             x0, x4
    // 0x917270: stur            x4, [fp, #-0x30]
    // 0x917274: r2 = Null
    //     0x917274: mov             x2, NULL
    // 0x917278: r1 = Null
    //     0x917278: mov             x1, NULL
    // 0x91727c: branchIfSmi(r0, 0x9172a4)
    //     0x91727c: tbz             w0, #0, #0x9172a4
    // 0x917280: r4 = LoadClassIdInstr(r0)
    //     0x917280: ldur            x4, [x0, #-1]
    //     0x917284: ubfx            x4, x4, #0xc, #0x14
    // 0x917288: sub             x4, x4, #0x3c
    // 0x91728c: cmp             x4, #1
    // 0x917290: b.ls            #0x9172a4
    // 0x917294: r8 = int
    //     0x917294: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x917298: r3 = Null
    //     0x917298: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f630] Null
    //     0x91729c: ldr             x3, [x3, #0x630]
    // 0x9172a0: r0 = int()
    //     0x9172a0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x9172a4: ldur            x1, [fp, #-8]
    // 0x9172a8: r2 = "hour_start_show"
    //     0x9172a8: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f640] "hour_start_show"
    //     0x9172ac: ldr             x2, [x2, #0x640]
    // 0x9172b0: r0 = _getValueOrData()
    //     0x9172b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9172b4: ldur            x3, [fp, #-8]
    // 0x9172b8: LoadField: r1 = r3->field_f
    //     0x9172b8: ldur            w1, [x3, #0xf]
    // 0x9172bc: DecompressPointer r1
    //     0x9172bc: add             x1, x1, HEAP, lsl #32
    // 0x9172c0: cmp             w1, w0
    // 0x9172c4: b.ne            #0x9172d0
    // 0x9172c8: r4 = Null
    //     0x9172c8: mov             x4, NULL
    // 0x9172cc: b               #0x9172d4
    // 0x9172d0: mov             x4, x0
    // 0x9172d4: mov             x0, x4
    // 0x9172d8: stur            x4, [fp, #-0x38]
    // 0x9172dc: r2 = Null
    //     0x9172dc: mov             x2, NULL
    // 0x9172e0: r1 = Null
    //     0x9172e0: mov             x1, NULL
    // 0x9172e4: branchIfSmi(r0, 0x91730c)
    //     0x9172e4: tbz             w0, #0, #0x91730c
    // 0x9172e8: r4 = LoadClassIdInstr(r0)
    //     0x9172e8: ldur            x4, [x0, #-1]
    //     0x9172ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9172f0: sub             x4, x4, #0x3c
    // 0x9172f4: cmp             x4, #1
    // 0x9172f8: b.ls            #0x91730c
    // 0x9172fc: r8 = int
    //     0x9172fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x917300: r3 = Null
    //     0x917300: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f648] Null
    //     0x917304: ldr             x3, [x3, #0x648]
    // 0x917308: r0 = int()
    //     0x917308: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91730c: ldur            x1, [fp, #-8]
    // 0x917310: r2 = "hour_end_show"
    //     0x917310: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f658] "hour_end_show"
    //     0x917314: ldr             x2, [x2, #0x658]
    // 0x917318: r0 = _getValueOrData()
    //     0x917318: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91731c: mov             x1, x0
    // 0x917320: ldur            x0, [fp, #-8]
    // 0x917324: LoadField: r2 = r0->field_f
    //     0x917324: ldur            w2, [x0, #0xf]
    // 0x917328: DecompressPointer r2
    //     0x917328: add             x2, x2, HEAP, lsl #32
    // 0x91732c: cmp             w2, w1
    // 0x917330: b.ne            #0x91733c
    // 0x917334: r9 = Null
    //     0x917334: mov             x9, NULL
    // 0x917338: b               #0x917340
    // 0x91733c: mov             x9, x1
    // 0x917340: ldur            x8, [fp, #-0x10]
    // 0x917344: ldur            x7, [fp, #-0x18]
    // 0x917348: ldur            x6, [fp, #-0x20]
    // 0x91734c: ldur            x5, [fp, #-0x28]
    // 0x917350: ldur            x4, [fp, #-0x30]
    // 0x917354: ldur            x3, [fp, #-0x38]
    // 0x917358: mov             x0, x9
    // 0x91735c: stur            x9, [fp, #-8]
    // 0x917360: r2 = Null
    //     0x917360: mov             x2, NULL
    // 0x917364: r1 = Null
    //     0x917364: mov             x1, NULL
    // 0x917368: branchIfSmi(r0, 0x917390)
    //     0x917368: tbz             w0, #0, #0x917390
    // 0x91736c: r4 = LoadClassIdInstr(r0)
    //     0x91736c: ldur            x4, [x0, #-1]
    //     0x917370: ubfx            x4, x4, #0xc, #0x14
    // 0x917374: sub             x4, x4, #0x3c
    // 0x917378: cmp             x4, #1
    // 0x91737c: b.ls            #0x917390
    // 0x917380: r8 = int
    //     0x917380: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x917384: r3 = Null
    //     0x917384: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f660] Null
    //     0x917388: ldr             x3, [x3, #0x660]
    // 0x91738c: r0 = int()
    //     0x91738c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x917390: r0 = DonationConfig()
    //     0x917390: bl              #0x91741c  ; AllocateDonationConfigStub -> DonationConfig (size=0x38)
    // 0x917394: ldur            x1, [fp, #-0x10]
    // 0x917398: StoreField: r0->field_7 = r1
    //     0x917398: stur            w1, [x0, #7]
    // 0x91739c: ldur            x1, [fp, #-0x18]
    // 0x9173a0: r2 = LoadInt32Instr(r1)
    //     0x9173a0: sbfx            x2, x1, #1, #0x1f
    //     0x9173a4: tbz             w1, #0, #0x9173ac
    //     0x9173a8: ldur            x2, [x1, #7]
    // 0x9173ac: StoreField: r0->field_b = r2
    //     0x9173ac: stur            x2, [x0, #0xb]
    // 0x9173b0: ldur            x1, [fp, #-0x20]
    // 0x9173b4: r2 = LoadInt32Instr(r1)
    //     0x9173b4: sbfx            x2, x1, #1, #0x1f
    //     0x9173b8: tbz             w1, #0, #0x9173c0
    //     0x9173bc: ldur            x2, [x1, #7]
    // 0x9173c0: StoreField: r0->field_13 = r2
    //     0x9173c0: stur            x2, [x0, #0x13]
    // 0x9173c4: ldur            x1, [fp, #-0x28]
    // 0x9173c8: StoreField: r0->field_1b = r1
    //     0x9173c8: stur            w1, [x0, #0x1b]
    // 0x9173cc: ldur            x1, [fp, #-0x30]
    // 0x9173d0: r2 = LoadInt32Instr(r1)
    //     0x9173d0: sbfx            x2, x1, #1, #0x1f
    //     0x9173d4: tbz             w1, #0, #0x9173dc
    //     0x9173d8: ldur            x2, [x1, #7]
    // 0x9173dc: StoreField: r0->field_1f = r2
    //     0x9173dc: stur            x2, [x0, #0x1f]
    // 0x9173e0: ldur            x1, [fp, #-0x38]
    // 0x9173e4: r2 = LoadInt32Instr(r1)
    //     0x9173e4: sbfx            x2, x1, #1, #0x1f
    //     0x9173e8: tbz             w1, #0, #0x9173f0
    //     0x9173ec: ldur            x2, [x1, #7]
    // 0x9173f0: StoreField: r0->field_27 = r2
    //     0x9173f0: stur            x2, [x0, #0x27]
    // 0x9173f4: ldur            x1, [fp, #-8]
    // 0x9173f8: r2 = LoadInt32Instr(r1)
    //     0x9173f8: sbfx            x2, x1, #1, #0x1f
    //     0x9173fc: tbz             w1, #0, #0x917404
    //     0x917400: ldur            x2, [x1, #7]
    // 0x917404: StoreField: r0->field_2f = r2
    //     0x917404: stur            x2, [x0, #0x2f]
    // 0x917408: LeaveFrame
    //     0x917408: mov             SP, fp
    //     0x91740c: ldp             fp, lr, [SP], #0x10
    // 0x917410: ret
    //     0x917410: ret             
    // 0x917414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x917414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x917418: b               #0x91709c
  }
}
