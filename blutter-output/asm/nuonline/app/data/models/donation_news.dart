// lib: , url: package:nuonline/app/data/models/donation_news.dart

// class id: 1050017, size: 0x8
class :: {
}

// class id: 1145, size: 0x14, field offset: 0x8
class DonationNewsContent extends Object {

  factory _ DonationNewsContent.fromMap(/* No info */) {
    // ** addr: 0x7ebf5c, size: 0x128
    // 0x7ebf5c: EnterFrame
    //     0x7ebf5c: stp             fp, lr, [SP, #-0x10]!
    //     0x7ebf60: mov             fp, SP
    // 0x7ebf64: AllocStack(0x18)
    //     0x7ebf64: sub             SP, SP, #0x18
    // 0x7ebf68: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7ebf68: mov             x3, x2
    //     0x7ebf6c: stur            x2, [fp, #-8]
    // 0x7ebf70: CheckStackOverflow
    //     0x7ebf70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebf74: cmp             SP, x16
    //     0x7ebf78: b.ls            #0x7ec07c
    // 0x7ebf7c: r0 = LoadClassIdInstr(r3)
    //     0x7ebf7c: ldur            x0, [x3, #-1]
    //     0x7ebf80: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebf84: mov             x1, x3
    // 0x7ebf88: r2 = "type"
    //     0x7ebf88: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x7ebf8c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebf8c: sub             lr, x0, #0x114
    //     0x7ebf90: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebf94: blr             lr
    // 0x7ebf98: mov             x3, x0
    // 0x7ebf9c: r2 = Null
    //     0x7ebf9c: mov             x2, NULL
    // 0x7ebfa0: r1 = Null
    //     0x7ebfa0: mov             x1, NULL
    // 0x7ebfa4: stur            x3, [fp, #-0x10]
    // 0x7ebfa8: r4 = 60
    //     0x7ebfa8: movz            x4, #0x3c
    // 0x7ebfac: branchIfSmi(r0, 0x7ebfb8)
    //     0x7ebfac: tbz             w0, #0, #0x7ebfb8
    // 0x7ebfb0: r4 = LoadClassIdInstr(r0)
    //     0x7ebfb0: ldur            x4, [x0, #-1]
    //     0x7ebfb4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebfb8: sub             x4, x4, #0x5e
    // 0x7ebfbc: cmp             x4, #1
    // 0x7ebfc0: b.ls            #0x7ebfd4
    // 0x7ebfc4: r8 = String
    //     0x7ebfc4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ebfc8: r3 = Null
    //     0x7ebfc8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35948] Null
    //     0x7ebfcc: ldr             x3, [x3, #0x948]
    // 0x7ebfd0: r0 = String()
    //     0x7ebfd0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ebfd4: ldur            x3, [fp, #-8]
    // 0x7ebfd8: r0 = LoadClassIdInstr(r3)
    //     0x7ebfd8: ldur            x0, [x3, #-1]
    //     0x7ebfdc: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebfe0: mov             x1, x3
    // 0x7ebfe4: r2 = "title"
    //     0x7ebfe4: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x7ebfe8: ldr             x2, [x2, #0x748]
    // 0x7ebfec: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebfec: sub             lr, x0, #0x114
    //     0x7ebff0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebff4: blr             lr
    // 0x7ebff8: mov             x3, x0
    // 0x7ebffc: r2 = Null
    //     0x7ebffc: mov             x2, NULL
    // 0x7ec000: r1 = Null
    //     0x7ec000: mov             x1, NULL
    // 0x7ec004: stur            x3, [fp, #-0x18]
    // 0x7ec008: r4 = 60
    //     0x7ec008: movz            x4, #0x3c
    // 0x7ec00c: branchIfSmi(r0, 0x7ec018)
    //     0x7ec00c: tbz             w0, #0, #0x7ec018
    // 0x7ec010: r4 = LoadClassIdInstr(r0)
    //     0x7ec010: ldur            x4, [x0, #-1]
    //     0x7ec014: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec018: sub             x4, x4, #0x5e
    // 0x7ec01c: cmp             x4, #1
    // 0x7ec020: b.ls            #0x7ec034
    // 0x7ec024: r8 = String?
    //     0x7ec024: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7ec028: r3 = Null
    //     0x7ec028: add             x3, PP, #0x35, lsl #12  ; [pp+0x35958] Null
    //     0x7ec02c: ldr             x3, [x3, #0x958]
    // 0x7ec030: r0 = String?()
    //     0x7ec030: bl              #0x600324  ; IsType_String?_Stub
    // 0x7ec034: ldur            x1, [fp, #-8]
    // 0x7ec038: r0 = LoadClassIdInstr(r1)
    //     0x7ec038: ldur            x0, [x1, #-1]
    //     0x7ec03c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec040: r2 = "value"
    //     0x7ec040: ldr             x2, [PP, #0x4dc0]  ; [pp+0x4dc0] "value"
    // 0x7ec044: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ec044: sub             lr, x0, #0x114
    //     0x7ec048: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec04c: blr             lr
    // 0x7ec050: stur            x0, [fp, #-8]
    // 0x7ec054: r0 = DonationNewsContent()
    //     0x7ec054: bl              #0x7ec084  ; AllocateDonationNewsContentStub -> DonationNewsContent (size=0x14)
    // 0x7ec058: ldur            x1, [fp, #-0x10]
    // 0x7ec05c: StoreField: r0->field_7 = r1
    //     0x7ec05c: stur            w1, [x0, #7]
    // 0x7ec060: ldur            x1, [fp, #-0x18]
    // 0x7ec064: StoreField: r0->field_b = r1
    //     0x7ec064: stur            w1, [x0, #0xb]
    // 0x7ec068: ldur            x1, [fp, #-8]
    // 0x7ec06c: StoreField: r0->field_f = r1
    //     0x7ec06c: stur            w1, [x0, #0xf]
    // 0x7ec070: LeaveFrame
    //     0x7ec070: mov             SP, fp
    //     0x7ec074: ldp             fp, lr, [SP], #0x10
    // 0x7ec078: ret
    //     0x7ec078: ret             
    // 0x7ec07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec07c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec080: b               #0x7ebf7c
  }
}

// class id: 5590, size: 0x1c, field offset: 0x8
//   const constructor, 
class DonationNews extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x7eb9cc, size: 0x188
    // 0x7eb9cc: EnterFrame
    //     0x7eb9cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb9d0: mov             fp, SP
    // 0x7eb9d4: AllocStack(0x20)
    //     0x7eb9d4: sub             SP, SP, #0x20
    // 0x7eb9d8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7eb9d8: mov             x3, x1
    //     0x7eb9dc: stur            x1, [fp, #-8]
    // 0x7eb9e0: CheckStackOverflow
    //     0x7eb9e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb9e4: cmp             SP, x16
    //     0x7eb9e8: b.ls            #0x7ebb4c
    // 0x7eb9ec: mov             x0, x3
    // 0x7eb9f0: r2 = Null
    //     0x7eb9f0: mov             x2, NULL
    // 0x7eb9f4: r1 = Null
    //     0x7eb9f4: mov             x1, NULL
    // 0x7eb9f8: cmp             w0, NULL
    // 0x7eb9fc: b.eq            #0x7ebaa0
    // 0x7eba00: branchIfSmi(r0, 0x7ebaa0)
    //     0x7eba00: tbz             w0, #0, #0x7ebaa0
    // 0x7eba04: r3 = LoadClassIdInstr(r0)
    //     0x7eba04: ldur            x3, [x0, #-1]
    //     0x7eba08: ubfx            x3, x3, #0xc, #0x14
    // 0x7eba0c: r17 = 6718
    //     0x7eba0c: movz            x17, #0x1a3e
    // 0x7eba10: cmp             x3, x17
    // 0x7eba14: b.eq            #0x7ebaa8
    // 0x7eba18: sub             x3, x3, #0x5a
    // 0x7eba1c: cmp             x3, #2
    // 0x7eba20: b.ls            #0x7ebaa8
    // 0x7eba24: r4 = LoadClassIdInstr(r0)
    //     0x7eba24: ldur            x4, [x0, #-1]
    //     0x7eba28: ubfx            x4, x4, #0xc, #0x14
    // 0x7eba2c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x7eba30: ldr             x3, [x3, #0x18]
    // 0x7eba34: ldr             x3, [x3, x4, lsl #3]
    // 0x7eba38: LoadField: r3 = r3->field_2b
    //     0x7eba38: ldur            w3, [x3, #0x2b]
    // 0x7eba3c: DecompressPointer r3
    //     0x7eba3c: add             x3, x3, HEAP, lsl #32
    // 0x7eba40: cmp             w3, NULL
    // 0x7eba44: b.eq            #0x7ebaa0
    // 0x7eba48: LoadField: r3 = r3->field_f
    //     0x7eba48: ldur            w3, [x3, #0xf]
    // 0x7eba4c: lsr             x3, x3, #3
    // 0x7eba50: r17 = 6718
    //     0x7eba50: movz            x17, #0x1a3e
    // 0x7eba54: cmp             x3, x17
    // 0x7eba58: b.eq            #0x7ebaa8
    // 0x7eba5c: r3 = SubtypeTestCache
    //     0x7eba5c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35898] SubtypeTestCache
    //     0x7eba60: ldr             x3, [x3, #0x898]
    // 0x7eba64: r30 = Subtype1TestCacheStub
    //     0x7eba64: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x7eba68: LoadField: r30 = r30->field_7
    //     0x7eba68: ldur            lr, [lr, #7]
    // 0x7eba6c: blr             lr
    // 0x7eba70: cmp             w7, NULL
    // 0x7eba74: b.eq            #0x7eba80
    // 0x7eba78: tbnz            w7, #4, #0x7ebaa0
    // 0x7eba7c: b               #0x7ebaa8
    // 0x7eba80: r8 = List
    //     0x7eba80: add             x8, PP, #0x35, lsl #12  ; [pp+0x358a0] Type: List
    //     0x7eba84: ldr             x8, [x8, #0x8a0]
    // 0x7eba88: r3 = SubtypeTestCache
    //     0x7eba88: add             x3, PP, #0x35, lsl #12  ; [pp+0x358a8] SubtypeTestCache
    //     0x7eba8c: ldr             x3, [x3, #0x8a8]
    // 0x7eba90: r30 = InstanceOfStub
    //     0x7eba90: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x7eba94: LoadField: r30 = r30->field_7
    //     0x7eba94: ldur            lr, [lr, #7]
    // 0x7eba98: blr             lr
    // 0x7eba9c: b               #0x7ebaac
    // 0x7ebaa0: r0 = false
    //     0x7ebaa0: add             x0, NULL, #0x30  ; false
    // 0x7ebaa4: b               #0x7ebaac
    // 0x7ebaa8: r0 = true
    //     0x7ebaa8: add             x0, NULL, #0x20  ; true
    // 0x7ebaac: tbnz            w0, #4, #0x7ebb30
    // 0x7ebab0: ldur            x0, [fp, #-8]
    // 0x7ebab4: r1 = Function '<anonymous closure>': static.
    //     0x7ebab4: add             x1, PP, #0x35, lsl #12  ; [pp+0x358b0] AnonymousClosure: static (0x7ebb54), in [package:nuonline/app/data/models/donation_news.dart] DonationNews::fromResponse (0x7eb9cc)
    //     0x7ebab8: ldr             x1, [x1, #0x8b0]
    // 0x7ebabc: r2 = Null
    //     0x7ebabc: mov             x2, NULL
    // 0x7ebac0: r0 = AllocateClosure()
    //     0x7ebac0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ebac4: mov             x1, x0
    // 0x7ebac8: ldur            x0, [fp, #-8]
    // 0x7ebacc: r2 = LoadClassIdInstr(r0)
    //     0x7ebacc: ldur            x2, [x0, #-1]
    //     0x7ebad0: ubfx            x2, x2, #0xc, #0x14
    // 0x7ebad4: r16 = <DonationNews>
    //     0x7ebad4: add             x16, PP, #0x30, lsl #12  ; [pp+0x304f8] TypeArguments: <DonationNews>
    //     0x7ebad8: ldr             x16, [x16, #0x4f8]
    // 0x7ebadc: stp             x0, x16, [SP, #8]
    // 0x7ebae0: str             x1, [SP]
    // 0x7ebae4: mov             x0, x2
    // 0x7ebae8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ebae8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ebaec: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7ebaec: movz            x17, #0xf28c
    //     0x7ebaf0: add             lr, x0, x17
    //     0x7ebaf4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebaf8: blr             lr
    // 0x7ebafc: r1 = LoadClassIdInstr(r0)
    //     0x7ebafc: ldur            x1, [x0, #-1]
    //     0x7ebb00: ubfx            x1, x1, #0xc, #0x14
    // 0x7ebb04: mov             x16, x0
    // 0x7ebb08: mov             x0, x1
    // 0x7ebb0c: mov             x1, x16
    // 0x7ebb10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ebb10: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ebb14: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7ebb14: movz            x17, #0xd889
    //     0x7ebb18: add             lr, x0, x17
    //     0x7ebb1c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebb20: blr             lr
    // 0x7ebb24: LeaveFrame
    //     0x7ebb24: mov             SP, fp
    //     0x7ebb28: ldp             fp, lr, [SP], #0x10
    // 0x7ebb2c: ret
    //     0x7ebb2c: ret             
    // 0x7ebb30: r1 = <DonationNews>
    //     0x7ebb30: add             x1, PP, #0x30, lsl #12  ; [pp+0x304f8] TypeArguments: <DonationNews>
    //     0x7ebb34: ldr             x1, [x1, #0x4f8]
    // 0x7ebb38: r2 = 0
    //     0x7ebb38: movz            x2, #0
    // 0x7ebb3c: r0 = _GrowableList()
    //     0x7ebb3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7ebb40: LeaveFrame
    //     0x7ebb40: mov             SP, fp
    //     0x7ebb44: ldp             fp, lr, [SP], #0x10
    // 0x7ebb48: ret
    //     0x7ebb48: ret             
    // 0x7ebb4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebb4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebb50: b               #0x7eb9ec
  }
  [closure] static DonationNews <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7ebb54, size: 0x50
    // 0x7ebb54: EnterFrame
    //     0x7ebb54: stp             fp, lr, [SP, #-0x10]!
    //     0x7ebb58: mov             fp, SP
    // 0x7ebb5c: CheckStackOverflow
    //     0x7ebb5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebb60: cmp             SP, x16
    //     0x7ebb64: b.ls            #0x7ebb9c
    // 0x7ebb68: ldr             x0, [fp, #0x10]
    // 0x7ebb6c: r2 = Null
    //     0x7ebb6c: mov             x2, NULL
    // 0x7ebb70: r1 = Null
    //     0x7ebb70: mov             x1, NULL
    // 0x7ebb74: r8 = Map<String, dynamic>
    //     0x7ebb74: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7ebb78: r3 = Null
    //     0x7ebb78: add             x3, PP, #0x35, lsl #12  ; [pp+0x358b8] Null
    //     0x7ebb7c: ldr             x3, [x3, #0x8b8]
    // 0x7ebb80: r0 = Map<String, dynamic>()
    //     0x7ebb80: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7ebb84: ldr             x2, [fp, #0x10]
    // 0x7ebb88: r1 = Null
    //     0x7ebb88: mov             x1, NULL
    // 0x7ebb8c: r0 = DonationNews.fromMap()
    //     0x7ebb8c: bl              #0x7ebba4  ; [package:nuonline/app/data/models/donation_news.dart] DonationNews::DonationNews.fromMap
    // 0x7ebb90: LeaveFrame
    //     0x7ebb90: mov             SP, fp
    //     0x7ebb94: ldp             fp, lr, [SP], #0x10
    // 0x7ebb98: ret
    //     0x7ebb98: ret             
    // 0x7ebb9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebb9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebba0: b               #0x7ebb68
  }
  factory _ DonationNews.fromMap(/* No info */) {
    // ** addr: 0x7ebba4, size: 0x35c
    // 0x7ebba4: EnterFrame
    //     0x7ebba4: stp             fp, lr, [SP, #-0x10]!
    //     0x7ebba8: mov             fp, SP
    // 0x7ebbac: AllocStack(0x48)
    //     0x7ebbac: sub             SP, SP, #0x48
    // 0x7ebbb0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7ebbb0: mov             x3, x2
    //     0x7ebbb4: stur            x2, [fp, #-8]
    // 0x7ebbb8: CheckStackOverflow
    //     0x7ebbb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebbbc: cmp             SP, x16
    //     0x7ebbc0: b.ls            #0x7ebef8
    // 0x7ebbc4: r0 = LoadClassIdInstr(r3)
    //     0x7ebbc4: ldur            x0, [x3, #-1]
    //     0x7ebbc8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebbcc: mov             x1, x3
    // 0x7ebbd0: r2 = "id"
    //     0x7ebbd0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7ebbd4: ldr             x2, [x2, #0x740]
    // 0x7ebbd8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebbd8: sub             lr, x0, #0x114
    //     0x7ebbdc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebbe0: blr             lr
    // 0x7ebbe4: mov             x3, x0
    // 0x7ebbe8: r2 = Null
    //     0x7ebbe8: mov             x2, NULL
    // 0x7ebbec: r1 = Null
    //     0x7ebbec: mov             x1, NULL
    // 0x7ebbf0: stur            x3, [fp, #-0x10]
    // 0x7ebbf4: branchIfSmi(r0, 0x7ebc1c)
    //     0x7ebbf4: tbz             w0, #0, #0x7ebc1c
    // 0x7ebbf8: r4 = LoadClassIdInstr(r0)
    //     0x7ebbf8: ldur            x4, [x0, #-1]
    //     0x7ebbfc: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebc00: sub             x4, x4, #0x3c
    // 0x7ebc04: cmp             x4, #1
    // 0x7ebc08: b.ls            #0x7ebc1c
    // 0x7ebc0c: r8 = int
    //     0x7ebc0c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7ebc10: r3 = Null
    //     0x7ebc10: add             x3, PP, #0x35, lsl #12  ; [pp+0x358c8] Null
    //     0x7ebc14: ldr             x3, [x3, #0x8c8]
    // 0x7ebc18: r0 = int()
    //     0x7ebc18: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7ebc1c: ldur            x3, [fp, #-8]
    // 0x7ebc20: r0 = LoadClassIdInstr(r3)
    //     0x7ebc20: ldur            x0, [x3, #-1]
    //     0x7ebc24: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebc28: mov             x1, x3
    // 0x7ebc2c: r2 = "donation_id"
    //     0x7ebc2c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b078] "donation_id"
    //     0x7ebc30: ldr             x2, [x2, #0x78]
    // 0x7ebc34: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebc34: sub             lr, x0, #0x114
    //     0x7ebc38: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebc3c: blr             lr
    // 0x7ebc40: r2 = Null
    //     0x7ebc40: mov             x2, NULL
    // 0x7ebc44: r1 = Null
    //     0x7ebc44: mov             x1, NULL
    // 0x7ebc48: branchIfSmi(r0, 0x7ebc70)
    //     0x7ebc48: tbz             w0, #0, #0x7ebc70
    // 0x7ebc4c: r4 = LoadClassIdInstr(r0)
    //     0x7ebc4c: ldur            x4, [x0, #-1]
    //     0x7ebc50: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebc54: sub             x4, x4, #0x3c
    // 0x7ebc58: cmp             x4, #1
    // 0x7ebc5c: b.ls            #0x7ebc70
    // 0x7ebc60: r8 = int
    //     0x7ebc60: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7ebc64: r3 = Null
    //     0x7ebc64: add             x3, PP, #0x35, lsl #12  ; [pp+0x358d8] Null
    //     0x7ebc68: ldr             x3, [x3, #0x8d8]
    // 0x7ebc6c: r0 = int()
    //     0x7ebc6c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7ebc70: ldur            x3, [fp, #-8]
    // 0x7ebc74: r0 = LoadClassIdInstr(r3)
    //     0x7ebc74: ldur            x0, [x3, #-1]
    //     0x7ebc78: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebc7c: mov             x1, x3
    // 0x7ebc80: r2 = "title"
    //     0x7ebc80: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x7ebc84: ldr             x2, [x2, #0x748]
    // 0x7ebc88: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebc88: sub             lr, x0, #0x114
    //     0x7ebc8c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebc90: blr             lr
    // 0x7ebc94: mov             x3, x0
    // 0x7ebc98: r2 = Null
    //     0x7ebc98: mov             x2, NULL
    // 0x7ebc9c: r1 = Null
    //     0x7ebc9c: mov             x1, NULL
    // 0x7ebca0: stur            x3, [fp, #-0x18]
    // 0x7ebca4: r4 = 60
    //     0x7ebca4: movz            x4, #0x3c
    // 0x7ebca8: branchIfSmi(r0, 0x7ebcb4)
    //     0x7ebca8: tbz             w0, #0, #0x7ebcb4
    // 0x7ebcac: r4 = LoadClassIdInstr(r0)
    //     0x7ebcac: ldur            x4, [x0, #-1]
    //     0x7ebcb0: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebcb4: sub             x4, x4, #0x5e
    // 0x7ebcb8: cmp             x4, #1
    // 0x7ebcbc: b.ls            #0x7ebcd0
    // 0x7ebcc0: r8 = String
    //     0x7ebcc0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ebcc4: r3 = Null
    //     0x7ebcc4: add             x3, PP, #0x35, lsl #12  ; [pp+0x358e8] Null
    //     0x7ebcc8: ldr             x3, [x3, #0x8e8]
    // 0x7ebccc: r0 = String()
    //     0x7ebccc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ebcd0: ldur            x3, [fp, #-8]
    // 0x7ebcd4: r0 = LoadClassIdInstr(r3)
    //     0x7ebcd4: ldur            x0, [x3, #-1]
    //     0x7ebcd8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebcdc: mov             x1, x3
    // 0x7ebce0: r2 = "content"
    //     0x7ebce0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x7ebce4: ldr             x2, [x2, #0xf58]
    // 0x7ebce8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebce8: sub             lr, x0, #0x114
    //     0x7ebcec: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebcf0: blr             lr
    // 0x7ebcf4: mov             x3, x0
    // 0x7ebcf8: r2 = Null
    //     0x7ebcf8: mov             x2, NULL
    // 0x7ebcfc: r1 = Null
    //     0x7ebcfc: mov             x1, NULL
    // 0x7ebd00: stur            x3, [fp, #-0x20]
    // 0x7ebd04: r4 = 60
    //     0x7ebd04: movz            x4, #0x3c
    // 0x7ebd08: branchIfSmi(r0, 0x7ebd14)
    //     0x7ebd08: tbz             w0, #0, #0x7ebd14
    // 0x7ebd0c: r4 = LoadClassIdInstr(r0)
    //     0x7ebd0c: ldur            x4, [x0, #-1]
    //     0x7ebd10: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebd14: sub             x4, x4, #0x5a
    // 0x7ebd18: cmp             x4, #2
    // 0x7ebd1c: b.ls            #0x7ebd34
    // 0x7ebd20: r8 = List?
    //     0x7ebd20: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7ebd24: ldr             x8, [x8, #0x140]
    // 0x7ebd28: r3 = Null
    //     0x7ebd28: add             x3, PP, #0x35, lsl #12  ; [pp+0x358f8] Null
    //     0x7ebd2c: ldr             x3, [x3, #0x8f8]
    // 0x7ebd30: r0 = List?()
    //     0x7ebd30: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7ebd34: ldur            x0, [fp, #-0x20]
    // 0x7ebd38: cmp             w0, NULL
    // 0x7ebd3c: b.ne            #0x7ebd54
    // 0x7ebd40: r1 = Null
    //     0x7ebd40: mov             x1, NULL
    // 0x7ebd44: r2 = 0
    //     0x7ebd44: movz            x2, #0
    // 0x7ebd48: r0 = _GrowableList()
    //     0x7ebd48: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7ebd4c: mov             x5, x0
    // 0x7ebd50: b               #0x7ebd58
    // 0x7ebd54: mov             x5, x0
    // 0x7ebd58: ldur            x0, [fp, #-8]
    // 0x7ebd5c: ldur            x4, [fp, #-0x10]
    // 0x7ebd60: ldur            x3, [fp, #-0x18]
    // 0x7ebd64: stur            x5, [fp, #-0x20]
    // 0x7ebd68: r1 = Function '<anonymous closure>': static.
    //     0x7ebd68: add             x1, PP, #0x35, lsl #12  ; [pp+0x35908] AnonymousClosure: static (0x7ebf0c), in [package:nuonline/app/data/models/donation_news.dart] DonationNews::DonationNews.fromMap (0x7ebba4)
    //     0x7ebd6c: ldr             x1, [x1, #0x908]
    // 0x7ebd70: r2 = Null
    //     0x7ebd70: mov             x2, NULL
    // 0x7ebd74: r0 = AllocateClosure()
    //     0x7ebd74: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ebd78: mov             x1, x0
    // 0x7ebd7c: ldur            x0, [fp, #-0x20]
    // 0x7ebd80: r2 = LoadClassIdInstr(r0)
    //     0x7ebd80: ldur            x2, [x0, #-1]
    //     0x7ebd84: ubfx            x2, x2, #0xc, #0x14
    // 0x7ebd88: r16 = <DonationNewsContent>
    //     0x7ebd88: add             x16, PP, #0x35, lsl #12  ; [pp+0x35910] TypeArguments: <DonationNewsContent>
    //     0x7ebd8c: ldr             x16, [x16, #0x910]
    // 0x7ebd90: stp             x0, x16, [SP, #8]
    // 0x7ebd94: str             x1, [SP]
    // 0x7ebd98: mov             x0, x2
    // 0x7ebd9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ebd9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ebda0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7ebda0: movz            x17, #0xf28c
    //     0x7ebda4: add             lr, x0, x17
    //     0x7ebda8: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebdac: blr             lr
    // 0x7ebdb0: r1 = LoadClassIdInstr(r0)
    //     0x7ebdb0: ldur            x1, [x0, #-1]
    //     0x7ebdb4: ubfx            x1, x1, #0xc, #0x14
    // 0x7ebdb8: mov             x16, x0
    // 0x7ebdbc: mov             x0, x1
    // 0x7ebdc0: mov             x1, x16
    // 0x7ebdc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ebdc4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ebdc8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7ebdc8: movz            x17, #0xd889
    //     0x7ebdcc: add             lr, x0, x17
    //     0x7ebdd0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebdd4: blr             lr
    // 0x7ebdd8: mov             x4, x0
    // 0x7ebddc: ldur            x3, [fp, #-8]
    // 0x7ebde0: stur            x4, [fp, #-0x20]
    // 0x7ebde4: r0 = LoadClassIdInstr(r3)
    //     0x7ebde4: ldur            x0, [x3, #-1]
    //     0x7ebde8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebdec: mov             x1, x3
    // 0x7ebdf0: r2 = "created_at"
    //     0x7ebdf0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1d0] "created_at"
    //     0x7ebdf4: ldr             x2, [x2, #0x1d0]
    // 0x7ebdf8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebdf8: sub             lr, x0, #0x114
    //     0x7ebdfc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebe00: blr             lr
    // 0x7ebe04: mov             x3, x0
    // 0x7ebe08: r2 = Null
    //     0x7ebe08: mov             x2, NULL
    // 0x7ebe0c: r1 = Null
    //     0x7ebe0c: mov             x1, NULL
    // 0x7ebe10: stur            x3, [fp, #-0x28]
    // 0x7ebe14: r4 = 60
    //     0x7ebe14: movz            x4, #0x3c
    // 0x7ebe18: branchIfSmi(r0, 0x7ebe24)
    //     0x7ebe18: tbz             w0, #0, #0x7ebe24
    // 0x7ebe1c: r4 = LoadClassIdInstr(r0)
    //     0x7ebe1c: ldur            x4, [x0, #-1]
    //     0x7ebe20: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebe24: sub             x4, x4, #0x5e
    // 0x7ebe28: cmp             x4, #1
    // 0x7ebe2c: b.ls            #0x7ebe40
    // 0x7ebe30: r8 = String
    //     0x7ebe30: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ebe34: r3 = Null
    //     0x7ebe34: add             x3, PP, #0x35, lsl #12  ; [pp+0x35918] Null
    //     0x7ebe38: ldr             x3, [x3, #0x918]
    // 0x7ebe3c: r0 = String()
    //     0x7ebe3c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ebe40: ldur            x1, [fp, #-0x28]
    // 0x7ebe44: r0 = parse()
    //     0x7ebe44: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7ebe48: mov             x3, x0
    // 0x7ebe4c: ldur            x1, [fp, #-8]
    // 0x7ebe50: stur            x3, [fp, #-0x28]
    // 0x7ebe54: r0 = LoadClassIdInstr(r1)
    //     0x7ebe54: ldur            x0, [x1, #-1]
    //     0x7ebe58: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebe5c: r2 = "updated_at"
    //     0x7ebe5c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x7ebe60: ldr             x2, [x2, #0xe88]
    // 0x7ebe64: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ebe64: sub             lr, x0, #0x114
    //     0x7ebe68: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebe6c: blr             lr
    // 0x7ebe70: mov             x3, x0
    // 0x7ebe74: r2 = Null
    //     0x7ebe74: mov             x2, NULL
    // 0x7ebe78: r1 = Null
    //     0x7ebe78: mov             x1, NULL
    // 0x7ebe7c: stur            x3, [fp, #-8]
    // 0x7ebe80: r4 = 60
    //     0x7ebe80: movz            x4, #0x3c
    // 0x7ebe84: branchIfSmi(r0, 0x7ebe90)
    //     0x7ebe84: tbz             w0, #0, #0x7ebe90
    // 0x7ebe88: r4 = LoadClassIdInstr(r0)
    //     0x7ebe88: ldur            x4, [x0, #-1]
    //     0x7ebe8c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebe90: sub             x4, x4, #0x5e
    // 0x7ebe94: cmp             x4, #1
    // 0x7ebe98: b.ls            #0x7ebeac
    // 0x7ebe9c: r8 = String
    //     0x7ebe9c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ebea0: r3 = Null
    //     0x7ebea0: add             x3, PP, #0x35, lsl #12  ; [pp+0x35928] Null
    //     0x7ebea4: ldr             x3, [x3, #0x928]
    // 0x7ebea8: r0 = String()
    //     0x7ebea8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ebeac: ldur            x1, [fp, #-8]
    // 0x7ebeb0: r0 = parse()
    //     0x7ebeb0: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7ebeb4: ldur            x0, [fp, #-0x10]
    // 0x7ebeb8: r1 = LoadInt32Instr(r0)
    //     0x7ebeb8: sbfx            x1, x0, #1, #0x1f
    //     0x7ebebc: tbz             w0, #0, #0x7ebec4
    //     0x7ebec0: ldur            x1, [x0, #7]
    // 0x7ebec4: stur            x1, [fp, #-0x30]
    // 0x7ebec8: r0 = DonationNews()
    //     0x7ebec8: bl              #0x7ebf00  ; AllocateDonationNewsStub -> DonationNews (size=0x1c)
    // 0x7ebecc: ldur            x1, [fp, #-0x30]
    // 0x7ebed0: StoreField: r0->field_7 = r1
    //     0x7ebed0: stur            x1, [x0, #7]
    // 0x7ebed4: ldur            x1, [fp, #-0x18]
    // 0x7ebed8: StoreField: r0->field_f = r1
    //     0x7ebed8: stur            w1, [x0, #0xf]
    // 0x7ebedc: ldur            x1, [fp, #-0x28]
    // 0x7ebee0: ArrayStore: r0[0] = r1  ; List_4
    //     0x7ebee0: stur            w1, [x0, #0x17]
    // 0x7ebee4: ldur            x1, [fp, #-0x20]
    // 0x7ebee8: StoreField: r0->field_13 = r1
    //     0x7ebee8: stur            w1, [x0, #0x13]
    // 0x7ebeec: LeaveFrame
    //     0x7ebeec: mov             SP, fp
    //     0x7ebef0: ldp             fp, lr, [SP], #0x10
    // 0x7ebef4: ret
    //     0x7ebef4: ret             
    // 0x7ebef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebef8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebefc: b               #0x7ebbc4
  }
  [closure] static DonationNewsContent <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7ebf0c, size: 0x50
    // 0x7ebf0c: EnterFrame
    //     0x7ebf0c: stp             fp, lr, [SP, #-0x10]!
    //     0x7ebf10: mov             fp, SP
    // 0x7ebf14: CheckStackOverflow
    //     0x7ebf14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebf18: cmp             SP, x16
    //     0x7ebf1c: b.ls            #0x7ebf54
    // 0x7ebf20: ldr             x0, [fp, #0x10]
    // 0x7ebf24: r2 = Null
    //     0x7ebf24: mov             x2, NULL
    // 0x7ebf28: r1 = Null
    //     0x7ebf28: mov             x1, NULL
    // 0x7ebf2c: r8 = Map<String, dynamic>
    //     0x7ebf2c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7ebf30: r3 = Null
    //     0x7ebf30: add             x3, PP, #0x35, lsl #12  ; [pp+0x35938] Null
    //     0x7ebf34: ldr             x3, [x3, #0x938]
    // 0x7ebf38: r0 = Map<String, dynamic>()
    //     0x7ebf38: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7ebf3c: ldr             x2, [fp, #0x10]
    // 0x7ebf40: r1 = Null
    //     0x7ebf40: mov             x1, NULL
    // 0x7ebf44: r0 = DonationNewsContent.fromMap()
    //     0x7ebf44: bl              #0x7ebf5c  ; [package:nuonline/app/data/models/donation_news.dart] DonationNewsContent::DonationNewsContent.fromMap
    // 0x7ebf48: LeaveFrame
    //     0x7ebf48: mov             SP, fp
    //     0x7ebf4c: ldp             fp, lr, [SP], #0x10
    // 0x7ebf50: ret
    //     0x7ebf50: ret             
    // 0x7ebf54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebf54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebf58: b               #0x7ebf20
  }
}
