// lib: , url: package:nuonline/app/data/models/donor.dart

// class id: 1050018, size: 0x8
class :: {
}

// class id: 1661, size: 0x14, field offset: 0xc
class DonorAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa61680, size: 0x320
    // 0xa61680: EnterFrame
    //     0xa61680: stp             fp, lr, [SP, #-0x10]!
    //     0xa61684: mov             fp, SP
    // 0xa61688: AllocStack(0x48)
    //     0xa61688: sub             SP, SP, #0x48
    // 0xa6168c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa6168c: stur            x2, [fp, #-0x20]
    // 0xa61690: CheckStackOverflow
    //     0xa61690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61694: cmp             SP, x16
    //     0xa61698: b.ls            #0xa61988
    // 0xa6169c: LoadField: r3 = r2->field_23
    //     0xa6169c: ldur            x3, [x2, #0x23]
    // 0xa616a0: add             x0, x3, #1
    // 0xa616a4: LoadField: r1 = r2->field_1b
    //     0xa616a4: ldur            x1, [x2, #0x1b]
    // 0xa616a8: cmp             x0, x1
    // 0xa616ac: b.gt            #0xa6192c
    // 0xa616b0: LoadField: r4 = r2->field_7
    //     0xa616b0: ldur            w4, [x2, #7]
    // 0xa616b4: DecompressPointer r4
    //     0xa616b4: add             x4, x4, HEAP, lsl #32
    // 0xa616b8: stur            x4, [fp, #-0x18]
    // 0xa616bc: StoreField: r2->field_23 = r0
    //     0xa616bc: stur            x0, [x2, #0x23]
    // 0xa616c0: LoadField: r0 = r4->field_13
    //     0xa616c0: ldur            w0, [x4, #0x13]
    // 0xa616c4: r5 = LoadInt32Instr(r0)
    //     0xa616c4: sbfx            x5, x0, #1, #0x1f
    // 0xa616c8: mov             x0, x5
    // 0xa616cc: mov             x1, x3
    // 0xa616d0: stur            x5, [fp, #-0x10]
    // 0xa616d4: cmp             x1, x0
    // 0xa616d8: b.hs            #0xa61990
    // 0xa616dc: LoadField: r0 = r4->field_7
    //     0xa616dc: ldur            x0, [x4, #7]
    // 0xa616e0: ldrb            w1, [x0, x3]
    // 0xa616e4: stur            x1, [fp, #-8]
    // 0xa616e8: r16 = <int, dynamic>
    //     0xa616e8: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa616ec: ldr             x16, [x16, #0xac0]
    // 0xa616f0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa616f4: stp             lr, x16, [SP]
    // 0xa616f8: r0 = Map._fromLiteral()
    //     0xa616f8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa616fc: mov             x2, x0
    // 0xa61700: stur            x2, [fp, #-0x38]
    // 0xa61704: r6 = 0
    //     0xa61704: movz            x6, #0
    // 0xa61708: ldur            x3, [fp, #-0x20]
    // 0xa6170c: ldur            x4, [fp, #-0x18]
    // 0xa61710: ldur            x5, [fp, #-8]
    // 0xa61714: stur            x6, [fp, #-0x30]
    // 0xa61718: CheckStackOverflow
    //     0xa61718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6171c: cmp             SP, x16
    //     0xa61720: b.ls            #0xa61994
    // 0xa61724: cmp             x6, x5
    // 0xa61728: b.ge            #0xa617b4
    // 0xa6172c: LoadField: r7 = r3->field_23
    //     0xa6172c: ldur            x7, [x3, #0x23]
    // 0xa61730: add             x0, x7, #1
    // 0xa61734: LoadField: r1 = r3->field_1b
    //     0xa61734: ldur            x1, [x3, #0x1b]
    // 0xa61738: cmp             x0, x1
    // 0xa6173c: b.gt            #0xa61954
    // 0xa61740: StoreField: r3->field_23 = r0
    //     0xa61740: stur            x0, [x3, #0x23]
    // 0xa61744: ldur            x0, [fp, #-0x10]
    // 0xa61748: mov             x1, x7
    // 0xa6174c: cmp             x1, x0
    // 0xa61750: b.hs            #0xa6199c
    // 0xa61754: LoadField: r0 = r4->field_7
    //     0xa61754: ldur            x0, [x4, #7]
    // 0xa61758: ldrb            w8, [x0, x7]
    // 0xa6175c: mov             x1, x3
    // 0xa61760: stur            x8, [fp, #-0x28]
    // 0xa61764: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa61764: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa61768: r0 = read()
    //     0xa61768: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa6176c: mov             x1, x0
    // 0xa61770: ldur            x0, [fp, #-0x28]
    // 0xa61774: lsl             x2, x0, #1
    // 0xa61778: r16 = LoadInt32Instr(r2)
    //     0xa61778: sbfx            x16, x2, #1, #0x1f
    // 0xa6177c: r17 = 11601
    //     0xa6177c: movz            x17, #0x2d51
    // 0xa61780: mul             x0, x16, x17
    // 0xa61784: umulh           x16, x16, x17
    // 0xa61788: eor             x0, x0, x16
    // 0xa6178c: r0 = 0
    //     0xa6178c: eor             x0, x0, x0, lsr #32
    // 0xa61790: ubfiz           x0, x0, #1, #0x1e
    // 0xa61794: r5 = LoadInt32Instr(r0)
    //     0xa61794: sbfx            x5, x0, #1, #0x1f
    // 0xa61798: mov             x3, x1
    // 0xa6179c: ldur            x1, [fp, #-0x38]
    // 0xa617a0: r0 = _set()
    //     0xa617a0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa617a4: ldur            x0, [fp, #-0x30]
    // 0xa617a8: add             x6, x0, #1
    // 0xa617ac: ldur            x2, [fp, #-0x38]
    // 0xa617b0: b               #0xa61708
    // 0xa617b4: mov             x0, x2
    // 0xa617b8: mov             x1, x0
    // 0xa617bc: r2 = 0
    //     0xa617bc: movz            x2, #0
    // 0xa617c0: r0 = _getValueOrData()
    //     0xa617c0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa617c4: ldur            x3, [fp, #-0x38]
    // 0xa617c8: LoadField: r1 = r3->field_f
    //     0xa617c8: ldur            w1, [x3, #0xf]
    // 0xa617cc: DecompressPointer r1
    //     0xa617cc: add             x1, x1, HEAP, lsl #32
    // 0xa617d0: cmp             w1, w0
    // 0xa617d4: b.ne            #0xa617e0
    // 0xa617d8: r4 = Null
    //     0xa617d8: mov             x4, NULL
    // 0xa617dc: b               #0xa617e4
    // 0xa617e0: mov             x4, x0
    // 0xa617e4: mov             x0, x4
    // 0xa617e8: stur            x4, [fp, #-0x18]
    // 0xa617ec: r2 = Null
    //     0xa617ec: mov             x2, NULL
    // 0xa617f0: r1 = Null
    //     0xa617f0: mov             x1, NULL
    // 0xa617f4: r4 = 60
    //     0xa617f4: movz            x4, #0x3c
    // 0xa617f8: branchIfSmi(r0, 0xa61804)
    //     0xa617f8: tbz             w0, #0, #0xa61804
    // 0xa617fc: r4 = LoadClassIdInstr(r0)
    //     0xa617fc: ldur            x4, [x0, #-1]
    //     0xa61800: ubfx            x4, x4, #0xc, #0x14
    // 0xa61804: sub             x4, x4, #0x5e
    // 0xa61808: cmp             x4, #1
    // 0xa6180c: b.ls            #0xa61820
    // 0xa61810: r8 = String
    //     0xa61810: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa61814: r3 = Null
    //     0xa61814: add             x3, PP, #0x21, lsl #12  ; [pp+0x21458] Null
    //     0xa61818: ldr             x3, [x3, #0x458]
    // 0xa6181c: r0 = String()
    //     0xa6181c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa61820: ldur            x1, [fp, #-0x38]
    // 0xa61824: r2 = 2
    //     0xa61824: movz            x2, #0x2
    // 0xa61828: r0 = _getValueOrData()
    //     0xa61828: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6182c: ldur            x3, [fp, #-0x38]
    // 0xa61830: LoadField: r1 = r3->field_f
    //     0xa61830: ldur            w1, [x3, #0xf]
    // 0xa61834: DecompressPointer r1
    //     0xa61834: add             x1, x1, HEAP, lsl #32
    // 0xa61838: cmp             w1, w0
    // 0xa6183c: b.ne            #0xa61848
    // 0xa61840: r4 = Null
    //     0xa61840: mov             x4, NULL
    // 0xa61844: b               #0xa6184c
    // 0xa61848: mov             x4, x0
    // 0xa6184c: mov             x0, x4
    // 0xa61850: stur            x4, [fp, #-0x20]
    // 0xa61854: r2 = Null
    //     0xa61854: mov             x2, NULL
    // 0xa61858: r1 = Null
    //     0xa61858: mov             x1, NULL
    // 0xa6185c: r4 = 60
    //     0xa6185c: movz            x4, #0x3c
    // 0xa61860: branchIfSmi(r0, 0xa6186c)
    //     0xa61860: tbz             w0, #0, #0xa6186c
    // 0xa61864: r4 = LoadClassIdInstr(r0)
    //     0xa61864: ldur            x4, [x0, #-1]
    //     0xa61868: ubfx            x4, x4, #0xc, #0x14
    // 0xa6186c: sub             x4, x4, #0x5e
    // 0xa61870: cmp             x4, #1
    // 0xa61874: b.ls            #0xa61888
    // 0xa61878: r8 = String?
    //     0xa61878: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa6187c: r3 = Null
    //     0xa6187c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21468] Null
    //     0xa61880: ldr             x3, [x3, #0x468]
    // 0xa61884: r0 = String?()
    //     0xa61884: bl              #0x600324  ; IsType_String?_Stub
    // 0xa61888: ldur            x1, [fp, #-0x38]
    // 0xa6188c: r2 = 4
    //     0xa6188c: movz            x2, #0x4
    // 0xa61890: r0 = _getValueOrData()
    //     0xa61890: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61894: mov             x1, x0
    // 0xa61898: ldur            x0, [fp, #-0x38]
    // 0xa6189c: LoadField: r2 = r0->field_f
    //     0xa6189c: ldur            w2, [x0, #0xf]
    // 0xa618a0: DecompressPointer r2
    //     0xa618a0: add             x2, x2, HEAP, lsl #32
    // 0xa618a4: cmp             w2, w1
    // 0xa618a8: b.ne            #0xa618b4
    // 0xa618ac: r5 = Null
    //     0xa618ac: mov             x5, NULL
    // 0xa618b0: b               #0xa618b8
    // 0xa618b4: mov             x5, x1
    // 0xa618b8: ldur            x4, [fp, #-0x18]
    // 0xa618bc: ldur            x3, [fp, #-0x20]
    // 0xa618c0: mov             x0, x5
    // 0xa618c4: stur            x5, [fp, #-0x38]
    // 0xa618c8: r2 = Null
    //     0xa618c8: mov             x2, NULL
    // 0xa618cc: r1 = Null
    //     0xa618cc: mov             x1, NULL
    // 0xa618d0: r4 = 60
    //     0xa618d0: movz            x4, #0x3c
    // 0xa618d4: branchIfSmi(r0, 0xa618e0)
    //     0xa618d4: tbz             w0, #0, #0xa618e0
    // 0xa618d8: r4 = LoadClassIdInstr(r0)
    //     0xa618d8: ldur            x4, [x0, #-1]
    //     0xa618dc: ubfx            x4, x4, #0xc, #0x14
    // 0xa618e0: sub             x4, x4, #0x5e
    // 0xa618e4: cmp             x4, #1
    // 0xa618e8: b.ls            #0xa618fc
    // 0xa618ec: r8 = String?
    //     0xa618ec: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa618f0: r3 = Null
    //     0xa618f0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21478] Null
    //     0xa618f4: ldr             x3, [x3, #0x478]
    // 0xa618f8: r0 = String?()
    //     0xa618f8: bl              #0x600324  ; IsType_String?_Stub
    // 0xa618fc: r0 = Donor()
    //     0xa618fc: bl              #0xa619a0  ; AllocateDonorStub -> Donor (size=0x14)
    // 0xa61900: mov             x1, x0
    // 0xa61904: ldur            x0, [fp, #-0x18]
    // 0xa61908: StoreField: r1->field_7 = r0
    //     0xa61908: stur            w0, [x1, #7]
    // 0xa6190c: ldur            x0, [fp, #-0x20]
    // 0xa61910: StoreField: r1->field_b = r0
    //     0xa61910: stur            w0, [x1, #0xb]
    // 0xa61914: ldur            x0, [fp, #-0x38]
    // 0xa61918: StoreField: r1->field_f = r0
    //     0xa61918: stur            w0, [x1, #0xf]
    // 0xa6191c: mov             x0, x1
    // 0xa61920: LeaveFrame
    //     0xa61920: mov             SP, fp
    //     0xa61924: ldp             fp, lr, [SP], #0x10
    // 0xa61928: ret
    //     0xa61928: ret             
    // 0xa6192c: r0 = RangeError()
    //     0xa6192c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61930: mov             x1, x0
    // 0xa61934: r0 = "Not enough bytes available."
    //     0xa61934: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61938: ldr             x0, [x0, #0x8a8]
    // 0xa6193c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa6193c: stur            w0, [x1, #0x17]
    // 0xa61940: r2 = false
    //     0xa61940: add             x2, NULL, #0x30  ; false
    // 0xa61944: StoreField: r1->field_b = r2
    //     0xa61944: stur            w2, [x1, #0xb]
    // 0xa61948: mov             x0, x1
    // 0xa6194c: r0 = Throw()
    //     0xa6194c: bl              #0xec04b8  ; ThrowStub
    // 0xa61950: brk             #0
    // 0xa61954: r0 = "Not enough bytes available."
    //     0xa61954: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61958: ldr             x0, [x0, #0x8a8]
    // 0xa6195c: r2 = false
    //     0xa6195c: add             x2, NULL, #0x30  ; false
    // 0xa61960: r0 = RangeError()
    //     0xa61960: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61964: mov             x1, x0
    // 0xa61968: r0 = "Not enough bytes available."
    //     0xa61968: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6196c: ldr             x0, [x0, #0x8a8]
    // 0xa61970: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61970: stur            w0, [x1, #0x17]
    // 0xa61974: r0 = false
    //     0xa61974: add             x0, NULL, #0x30  ; false
    // 0xa61978: StoreField: r1->field_b = r0
    //     0xa61978: stur            w0, [x1, #0xb]
    // 0xa6197c: mov             x0, x1
    // 0xa61980: r0 = Throw()
    //     0xa61980: bl              #0xec04b8  ; ThrowStub
    // 0xa61984: brk             #0
    // 0xa61988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6198c: b               #0xa6169c
    // 0xa61990: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61990: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa61994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa61998: b               #0xa61724
    // 0xa6199c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa6199c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd2a0c, size: 0x27c
    // 0xbd2a0c: EnterFrame
    //     0xbd2a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd2a10: mov             fp, SP
    // 0xbd2a14: AllocStack(0x28)
    //     0xbd2a14: sub             SP, SP, #0x28
    // 0xbd2a18: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd2a18: mov             x4, x2
    //     0xbd2a1c: stur            x2, [fp, #-8]
    //     0xbd2a20: stur            x3, [fp, #-0x10]
    // 0xbd2a24: CheckStackOverflow
    //     0xbd2a24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd2a28: cmp             SP, x16
    //     0xbd2a2c: b.ls            #0xbd2c70
    // 0xbd2a30: mov             x0, x3
    // 0xbd2a34: r2 = Null
    //     0xbd2a34: mov             x2, NULL
    // 0xbd2a38: r1 = Null
    //     0xbd2a38: mov             x1, NULL
    // 0xbd2a3c: r4 = 60
    //     0xbd2a3c: movz            x4, #0x3c
    // 0xbd2a40: branchIfSmi(r0, 0xbd2a4c)
    //     0xbd2a40: tbz             w0, #0, #0xbd2a4c
    // 0xbd2a44: r4 = LoadClassIdInstr(r0)
    //     0xbd2a44: ldur            x4, [x0, #-1]
    //     0xbd2a48: ubfx            x4, x4, #0xc, #0x14
    // 0xbd2a4c: r17 = 5589
    //     0xbd2a4c: movz            x17, #0x15d5
    // 0xbd2a50: cmp             x4, x17
    // 0xbd2a54: b.eq            #0xbd2a6c
    // 0xbd2a58: r8 = Donor
    //     0xbd2a58: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b398] Type: Donor
    //     0xbd2a5c: ldr             x8, [x8, #0x398]
    // 0xbd2a60: r3 = Null
    //     0xbd2a60: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3a0] Null
    //     0xbd2a64: ldr             x3, [x3, #0x3a0]
    // 0xbd2a68: r0 = Donor()
    //     0xbd2a68: bl              #0x8f84ac  ; IsType_Donor_Stub
    // 0xbd2a6c: ldur            x0, [fp, #-8]
    // 0xbd2a70: LoadField: r1 = r0->field_b
    //     0xbd2a70: ldur            w1, [x0, #0xb]
    // 0xbd2a74: DecompressPointer r1
    //     0xbd2a74: add             x1, x1, HEAP, lsl #32
    // 0xbd2a78: LoadField: r2 = r1->field_13
    //     0xbd2a78: ldur            w2, [x1, #0x13]
    // 0xbd2a7c: LoadField: r1 = r0->field_13
    //     0xbd2a7c: ldur            x1, [x0, #0x13]
    // 0xbd2a80: r3 = LoadInt32Instr(r2)
    //     0xbd2a80: sbfx            x3, x2, #1, #0x1f
    // 0xbd2a84: sub             x2, x3, x1
    // 0xbd2a88: cmp             x2, #1
    // 0xbd2a8c: b.ge            #0xbd2a9c
    // 0xbd2a90: mov             x1, x0
    // 0xbd2a94: r2 = 1
    //     0xbd2a94: movz            x2, #0x1
    // 0xbd2a98: r0 = _increaseBufferSize()
    //     0xbd2a98: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2a9c: ldur            x3, [fp, #-8]
    // 0xbd2aa0: r2 = 3
    //     0xbd2aa0: movz            x2, #0x3
    // 0xbd2aa4: LoadField: r4 = r3->field_b
    //     0xbd2aa4: ldur            w4, [x3, #0xb]
    // 0xbd2aa8: DecompressPointer r4
    //     0xbd2aa8: add             x4, x4, HEAP, lsl #32
    // 0xbd2aac: LoadField: r5 = r3->field_13
    //     0xbd2aac: ldur            x5, [x3, #0x13]
    // 0xbd2ab0: add             x6, x5, #1
    // 0xbd2ab4: StoreField: r3->field_13 = r6
    //     0xbd2ab4: stur            x6, [x3, #0x13]
    // 0xbd2ab8: LoadField: r0 = r4->field_13
    //     0xbd2ab8: ldur            w0, [x4, #0x13]
    // 0xbd2abc: r7 = LoadInt32Instr(r0)
    //     0xbd2abc: sbfx            x7, x0, #1, #0x1f
    // 0xbd2ac0: mov             x0, x7
    // 0xbd2ac4: mov             x1, x5
    // 0xbd2ac8: cmp             x1, x0
    // 0xbd2acc: b.hs            #0xbd2c78
    // 0xbd2ad0: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd2ad0: add             x0, x4, x5
    //     0xbd2ad4: strb            w2, [x0, #0x17]
    // 0xbd2ad8: sub             x0, x7, x6
    // 0xbd2adc: cmp             x0, #1
    // 0xbd2ae0: b.ge            #0xbd2af0
    // 0xbd2ae4: mov             x1, x3
    // 0xbd2ae8: r2 = 1
    //     0xbd2ae8: movz            x2, #0x1
    // 0xbd2aec: r0 = _increaseBufferSize()
    //     0xbd2aec: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2af0: ldur            x2, [fp, #-8]
    // 0xbd2af4: ldur            x3, [fp, #-0x10]
    // 0xbd2af8: LoadField: r4 = r2->field_b
    //     0xbd2af8: ldur            w4, [x2, #0xb]
    // 0xbd2afc: DecompressPointer r4
    //     0xbd2afc: add             x4, x4, HEAP, lsl #32
    // 0xbd2b00: LoadField: r5 = r2->field_13
    //     0xbd2b00: ldur            x5, [x2, #0x13]
    // 0xbd2b04: add             x0, x5, #1
    // 0xbd2b08: StoreField: r2->field_13 = r0
    //     0xbd2b08: stur            x0, [x2, #0x13]
    // 0xbd2b0c: LoadField: r0 = r4->field_13
    //     0xbd2b0c: ldur            w0, [x4, #0x13]
    // 0xbd2b10: r1 = LoadInt32Instr(r0)
    //     0xbd2b10: sbfx            x1, x0, #1, #0x1f
    // 0xbd2b14: mov             x0, x1
    // 0xbd2b18: mov             x1, x5
    // 0xbd2b1c: cmp             x1, x0
    // 0xbd2b20: b.hs            #0xbd2c7c
    // 0xbd2b24: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd2b24: add             x0, x4, x5
    //     0xbd2b28: strb            wzr, [x0, #0x17]
    // 0xbd2b2c: LoadField: r0 = r3->field_7
    //     0xbd2b2c: ldur            w0, [x3, #7]
    // 0xbd2b30: DecompressPointer r0
    //     0xbd2b30: add             x0, x0, HEAP, lsl #32
    // 0xbd2b34: r16 = <String>
    //     0xbd2b34: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd2b38: stp             x2, x16, [SP, #8]
    // 0xbd2b3c: str             x0, [SP]
    // 0xbd2b40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2b40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2b44: r0 = write()
    //     0xbd2b44: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2b48: ldur            x0, [fp, #-8]
    // 0xbd2b4c: LoadField: r1 = r0->field_b
    //     0xbd2b4c: ldur            w1, [x0, #0xb]
    // 0xbd2b50: DecompressPointer r1
    //     0xbd2b50: add             x1, x1, HEAP, lsl #32
    // 0xbd2b54: LoadField: r2 = r1->field_13
    //     0xbd2b54: ldur            w2, [x1, #0x13]
    // 0xbd2b58: LoadField: r1 = r0->field_13
    //     0xbd2b58: ldur            x1, [x0, #0x13]
    // 0xbd2b5c: r3 = LoadInt32Instr(r2)
    //     0xbd2b5c: sbfx            x3, x2, #1, #0x1f
    // 0xbd2b60: sub             x2, x3, x1
    // 0xbd2b64: cmp             x2, #1
    // 0xbd2b68: b.ge            #0xbd2b78
    // 0xbd2b6c: mov             x1, x0
    // 0xbd2b70: r2 = 1
    //     0xbd2b70: movz            x2, #0x1
    // 0xbd2b74: r0 = _increaseBufferSize()
    //     0xbd2b74: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2b78: ldur            x2, [fp, #-8]
    // 0xbd2b7c: ldur            x3, [fp, #-0x10]
    // 0xbd2b80: r4 = 1
    //     0xbd2b80: movz            x4, #0x1
    // 0xbd2b84: LoadField: r5 = r2->field_b
    //     0xbd2b84: ldur            w5, [x2, #0xb]
    // 0xbd2b88: DecompressPointer r5
    //     0xbd2b88: add             x5, x5, HEAP, lsl #32
    // 0xbd2b8c: LoadField: r6 = r2->field_13
    //     0xbd2b8c: ldur            x6, [x2, #0x13]
    // 0xbd2b90: add             x0, x6, #1
    // 0xbd2b94: StoreField: r2->field_13 = r0
    //     0xbd2b94: stur            x0, [x2, #0x13]
    // 0xbd2b98: LoadField: r0 = r5->field_13
    //     0xbd2b98: ldur            w0, [x5, #0x13]
    // 0xbd2b9c: r1 = LoadInt32Instr(r0)
    //     0xbd2b9c: sbfx            x1, x0, #1, #0x1f
    // 0xbd2ba0: mov             x0, x1
    // 0xbd2ba4: mov             x1, x6
    // 0xbd2ba8: cmp             x1, x0
    // 0xbd2bac: b.hs            #0xbd2c80
    // 0xbd2bb0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2bb0: add             x0, x5, x6
    //     0xbd2bb4: strb            w4, [x0, #0x17]
    // 0xbd2bb8: LoadField: r0 = r3->field_b
    //     0xbd2bb8: ldur            w0, [x3, #0xb]
    // 0xbd2bbc: DecompressPointer r0
    //     0xbd2bbc: add             x0, x0, HEAP, lsl #32
    // 0xbd2bc0: r16 = <String?>
    //     0xbd2bc0: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd2bc4: stp             x2, x16, [SP, #8]
    // 0xbd2bc8: str             x0, [SP]
    // 0xbd2bcc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2bcc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2bd0: r0 = write()
    //     0xbd2bd0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2bd4: ldur            x0, [fp, #-8]
    // 0xbd2bd8: LoadField: r1 = r0->field_b
    //     0xbd2bd8: ldur            w1, [x0, #0xb]
    // 0xbd2bdc: DecompressPointer r1
    //     0xbd2bdc: add             x1, x1, HEAP, lsl #32
    // 0xbd2be0: LoadField: r2 = r1->field_13
    //     0xbd2be0: ldur            w2, [x1, #0x13]
    // 0xbd2be4: LoadField: r1 = r0->field_13
    //     0xbd2be4: ldur            x1, [x0, #0x13]
    // 0xbd2be8: r3 = LoadInt32Instr(r2)
    //     0xbd2be8: sbfx            x3, x2, #1, #0x1f
    // 0xbd2bec: sub             x2, x3, x1
    // 0xbd2bf0: cmp             x2, #1
    // 0xbd2bf4: b.ge            #0xbd2c04
    // 0xbd2bf8: mov             x1, x0
    // 0xbd2bfc: r2 = 1
    //     0xbd2bfc: movz            x2, #0x1
    // 0xbd2c00: r0 = _increaseBufferSize()
    //     0xbd2c00: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2c04: ldur            x2, [fp, #-8]
    // 0xbd2c08: ldur            x3, [fp, #-0x10]
    // 0xbd2c0c: r4 = 2
    //     0xbd2c0c: movz            x4, #0x2
    // 0xbd2c10: LoadField: r5 = r2->field_b
    //     0xbd2c10: ldur            w5, [x2, #0xb]
    // 0xbd2c14: DecompressPointer r5
    //     0xbd2c14: add             x5, x5, HEAP, lsl #32
    // 0xbd2c18: LoadField: r6 = r2->field_13
    //     0xbd2c18: ldur            x6, [x2, #0x13]
    // 0xbd2c1c: add             x0, x6, #1
    // 0xbd2c20: StoreField: r2->field_13 = r0
    //     0xbd2c20: stur            x0, [x2, #0x13]
    // 0xbd2c24: LoadField: r0 = r5->field_13
    //     0xbd2c24: ldur            w0, [x5, #0x13]
    // 0xbd2c28: r1 = LoadInt32Instr(r0)
    //     0xbd2c28: sbfx            x1, x0, #1, #0x1f
    // 0xbd2c2c: mov             x0, x1
    // 0xbd2c30: mov             x1, x6
    // 0xbd2c34: cmp             x1, x0
    // 0xbd2c38: b.hs            #0xbd2c84
    // 0xbd2c3c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2c3c: add             x0, x5, x6
    //     0xbd2c40: strb            w4, [x0, #0x17]
    // 0xbd2c44: LoadField: r0 = r3->field_f
    //     0xbd2c44: ldur            w0, [x3, #0xf]
    // 0xbd2c48: DecompressPointer r0
    //     0xbd2c48: add             x0, x0, HEAP, lsl #32
    // 0xbd2c4c: r16 = <String?>
    //     0xbd2c4c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd2c50: stp             x2, x16, [SP, #8]
    // 0xbd2c54: str             x0, [SP]
    // 0xbd2c58: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2c58: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2c5c: r0 = write()
    //     0xbd2c5c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2c60: r0 = Null
    //     0xbd2c60: mov             x0, NULL
    // 0xbd2c64: LeaveFrame
    //     0xbd2c64: mov             SP, fp
    //     0xbd2c68: ldp             fp, lr, [SP], #0x10
    // 0xbd2c6c: ret
    //     0xbd2c6c: ret             
    // 0xbd2c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd2c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd2c74: b               #0xbd2a30
    // 0xbd2c78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2c78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2c7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2c7c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2c80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2c80: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd2c84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd2c84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0144, size: 0x24
    // 0xbf0144: r1 = 180
    //     0xbf0144: movz            x1, #0xb4
    // 0xbf0148: r16 = LoadInt32Instr(r1)
    //     0xbf0148: sbfx            x16, x1, #1, #0x1f
    // 0xbf014c: r17 = 11601
    //     0xbf014c: movz            x17, #0x2d51
    // 0xbf0150: mul             x0, x16, x17
    // 0xbf0154: umulh           x16, x16, x17
    // 0xbf0158: eor             x0, x0, x16
    // 0xbf015c: r0 = 0
    //     0xbf015c: eor             x0, x0, x0, lsr #32
    // 0xbf0160: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0164: ret
    //     0xbf0164: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd765f0, size: 0x9c
    // 0xd765f0: EnterFrame
    //     0xd765f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd765f4: mov             fp, SP
    // 0xd765f8: AllocStack(0x10)
    //     0xd765f8: sub             SP, SP, #0x10
    // 0xd765fc: CheckStackOverflow
    //     0xd765fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76600: cmp             SP, x16
    //     0xd76604: b.ls            #0xd76684
    // 0xd76608: ldr             x0, [fp, #0x10]
    // 0xd7660c: cmp             w0, NULL
    // 0xd76610: b.ne            #0xd76624
    // 0xd76614: r0 = false
    //     0xd76614: add             x0, NULL, #0x30  ; false
    // 0xd76618: LeaveFrame
    //     0xd76618: mov             SP, fp
    //     0xd7661c: ldp             fp, lr, [SP], #0x10
    // 0xd76620: ret
    //     0xd76620: ret             
    // 0xd76624: ldr             x1, [fp, #0x18]
    // 0xd76628: cmp             w1, w0
    // 0xd7662c: b.ne            #0xd76638
    // 0xd76630: r0 = true
    //     0xd76630: add             x0, NULL, #0x20  ; true
    // 0xd76634: b               #0xd76678
    // 0xd76638: r1 = 60
    //     0xd76638: movz            x1, #0x3c
    // 0xd7663c: branchIfSmi(r0, 0xd76648)
    //     0xd7663c: tbz             w0, #0, #0xd76648
    // 0xd76640: r1 = LoadClassIdInstr(r0)
    //     0xd76640: ldur            x1, [x0, #-1]
    //     0xd76644: ubfx            x1, x1, #0xc, #0x14
    // 0xd76648: cmp             x1, #0x67d
    // 0xd7664c: b.ne            #0xd76674
    // 0xd76650: r16 = DonorAdapter
    //     0xd76650: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b390] Type: DonorAdapter
    //     0xd76654: ldr             x16, [x16, #0x390]
    // 0xd76658: r30 = DonorAdapter
    //     0xd76658: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b390] Type: DonorAdapter
    //     0xd7665c: ldr             lr, [lr, #0x390]
    // 0xd76660: stp             lr, x16, [SP]
    // 0xd76664: r0 = ==()
    //     0xd76664: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76668: tbnz            w0, #4, #0xd76674
    // 0xd7666c: r0 = true
    //     0xd7666c: add             x0, NULL, #0x20  ; true
    // 0xd76670: b               #0xd76678
    // 0xd76674: r0 = false
    //     0xd76674: add             x0, NULL, #0x30  ; false
    // 0xd76678: LeaveFrame
    //     0xd76678: mov             SP, fp
    //     0xd7667c: ldp             fp, lr, [SP], #0x10
    // 0xd76680: ret
    //     0xd76680: ret             
    // 0xd76684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76688: b               #0xd76608
  }
}

// class id: 5589, size: 0x14, field offset: 0x8
//   const constructor, 
class Donor extends Equatable {
}
