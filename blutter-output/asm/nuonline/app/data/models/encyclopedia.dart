// lib: , url: package:nuonline/app/data/models/encyclopedia.dart

// class id: 1050019, size: 0x8
class :: {
}

// class id: 1659, size: 0x14, field offset: 0xc
class EncyclopediaCategoryAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa61e84, size: 0x3e4
    // 0xa61e84: EnterFrame
    //     0xa61e84: stp             fp, lr, [SP, #-0x10]!
    //     0xa61e88: mov             fp, SP
    // 0xa61e8c: AllocStack(0x50)
    //     0xa61e8c: sub             SP, SP, #0x50
    // 0xa61e90: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa61e90: stur            x2, [fp, #-0x20]
    // 0xa61e94: CheckStackOverflow
    //     0xa61e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61e98: cmp             SP, x16
    //     0xa61e9c: b.ls            #0xa62250
    // 0xa61ea0: LoadField: r3 = r2->field_23
    //     0xa61ea0: ldur            x3, [x2, #0x23]
    // 0xa61ea4: add             x0, x3, #1
    // 0xa61ea8: LoadField: r1 = r2->field_1b
    //     0xa61ea8: ldur            x1, [x2, #0x1b]
    // 0xa61eac: cmp             x0, x1
    // 0xa61eb0: b.gt            #0xa621f4
    // 0xa61eb4: LoadField: r4 = r2->field_7
    //     0xa61eb4: ldur            w4, [x2, #7]
    // 0xa61eb8: DecompressPointer r4
    //     0xa61eb8: add             x4, x4, HEAP, lsl #32
    // 0xa61ebc: stur            x4, [fp, #-0x18]
    // 0xa61ec0: StoreField: r2->field_23 = r0
    //     0xa61ec0: stur            x0, [x2, #0x23]
    // 0xa61ec4: LoadField: r0 = r4->field_13
    //     0xa61ec4: ldur            w0, [x4, #0x13]
    // 0xa61ec8: r5 = LoadInt32Instr(r0)
    //     0xa61ec8: sbfx            x5, x0, #1, #0x1f
    // 0xa61ecc: mov             x0, x5
    // 0xa61ed0: mov             x1, x3
    // 0xa61ed4: stur            x5, [fp, #-0x10]
    // 0xa61ed8: cmp             x1, x0
    // 0xa61edc: b.hs            #0xa62258
    // 0xa61ee0: LoadField: r0 = r4->field_7
    //     0xa61ee0: ldur            x0, [x4, #7]
    // 0xa61ee4: ldrb            w1, [x0, x3]
    // 0xa61ee8: stur            x1, [fp, #-8]
    // 0xa61eec: r16 = <int, dynamic>
    //     0xa61eec: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa61ef0: ldr             x16, [x16, #0xac0]
    // 0xa61ef4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa61ef8: stp             lr, x16, [SP]
    // 0xa61efc: r0 = Map._fromLiteral()
    //     0xa61efc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa61f00: mov             x2, x0
    // 0xa61f04: stur            x2, [fp, #-0x38]
    // 0xa61f08: r6 = 0
    //     0xa61f08: movz            x6, #0
    // 0xa61f0c: ldur            x3, [fp, #-0x20]
    // 0xa61f10: ldur            x4, [fp, #-0x18]
    // 0xa61f14: ldur            x5, [fp, #-8]
    // 0xa61f18: stur            x6, [fp, #-0x30]
    // 0xa61f1c: CheckStackOverflow
    //     0xa61f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61f20: cmp             SP, x16
    //     0xa61f24: b.ls            #0xa6225c
    // 0xa61f28: cmp             x6, x5
    // 0xa61f2c: b.ge            #0xa61fb8
    // 0xa61f30: LoadField: r7 = r3->field_23
    //     0xa61f30: ldur            x7, [x3, #0x23]
    // 0xa61f34: add             x0, x7, #1
    // 0xa61f38: LoadField: r1 = r3->field_1b
    //     0xa61f38: ldur            x1, [x3, #0x1b]
    // 0xa61f3c: cmp             x0, x1
    // 0xa61f40: b.gt            #0xa6221c
    // 0xa61f44: StoreField: r3->field_23 = r0
    //     0xa61f44: stur            x0, [x3, #0x23]
    // 0xa61f48: ldur            x0, [fp, #-0x10]
    // 0xa61f4c: mov             x1, x7
    // 0xa61f50: cmp             x1, x0
    // 0xa61f54: b.hs            #0xa62264
    // 0xa61f58: LoadField: r0 = r4->field_7
    //     0xa61f58: ldur            x0, [x4, #7]
    // 0xa61f5c: ldrb            w8, [x0, x7]
    // 0xa61f60: mov             x1, x3
    // 0xa61f64: stur            x8, [fp, #-0x28]
    // 0xa61f68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa61f68: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa61f6c: r0 = read()
    //     0xa61f6c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa61f70: mov             x1, x0
    // 0xa61f74: ldur            x0, [fp, #-0x28]
    // 0xa61f78: lsl             x2, x0, #1
    // 0xa61f7c: r16 = LoadInt32Instr(r2)
    //     0xa61f7c: sbfx            x16, x2, #1, #0x1f
    // 0xa61f80: r17 = 11601
    //     0xa61f80: movz            x17, #0x2d51
    // 0xa61f84: mul             x0, x16, x17
    // 0xa61f88: umulh           x16, x16, x17
    // 0xa61f8c: eor             x0, x0, x16
    // 0xa61f90: r0 = 0
    //     0xa61f90: eor             x0, x0, x0, lsr #32
    // 0xa61f94: ubfiz           x0, x0, #1, #0x1e
    // 0xa61f98: r5 = LoadInt32Instr(r0)
    //     0xa61f98: sbfx            x5, x0, #1, #0x1f
    // 0xa61f9c: mov             x3, x1
    // 0xa61fa0: ldur            x1, [fp, #-0x38]
    // 0xa61fa4: r0 = _set()
    //     0xa61fa4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa61fa8: ldur            x0, [fp, #-0x30]
    // 0xa61fac: add             x6, x0, #1
    // 0xa61fb0: ldur            x2, [fp, #-0x38]
    // 0xa61fb4: b               #0xa61f0c
    // 0xa61fb8: mov             x0, x2
    // 0xa61fbc: mov             x1, x0
    // 0xa61fc0: r2 = 0
    //     0xa61fc0: movz            x2, #0
    // 0xa61fc4: r0 = _getValueOrData()
    //     0xa61fc4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61fc8: ldur            x3, [fp, #-0x38]
    // 0xa61fcc: LoadField: r1 = r3->field_f
    //     0xa61fcc: ldur            w1, [x3, #0xf]
    // 0xa61fd0: DecompressPointer r1
    //     0xa61fd0: add             x1, x1, HEAP, lsl #32
    // 0xa61fd4: cmp             w1, w0
    // 0xa61fd8: b.ne            #0xa61fe4
    // 0xa61fdc: r4 = Null
    //     0xa61fdc: mov             x4, NULL
    // 0xa61fe0: b               #0xa61fe8
    // 0xa61fe4: mov             x4, x0
    // 0xa61fe8: mov             x0, x4
    // 0xa61fec: stur            x4, [fp, #-0x18]
    // 0xa61ff0: r2 = Null
    //     0xa61ff0: mov             x2, NULL
    // 0xa61ff4: r1 = Null
    //     0xa61ff4: mov             x1, NULL
    // 0xa61ff8: branchIfSmi(r0, 0xa62020)
    //     0xa61ff8: tbz             w0, #0, #0xa62020
    // 0xa61ffc: r4 = LoadClassIdInstr(r0)
    //     0xa61ffc: ldur            x4, [x0, #-1]
    //     0xa62000: ubfx            x4, x4, #0xc, #0x14
    // 0xa62004: sub             x4, x4, #0x3c
    // 0xa62008: cmp             x4, #1
    // 0xa6200c: b.ls            #0xa62020
    // 0xa62010: r8 = int
    //     0xa62010: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa62014: r3 = Null
    //     0xa62014: add             x3, PP, #0x21, lsl #12  ; [pp+0x21418] Null
    //     0xa62018: ldr             x3, [x3, #0x418]
    // 0xa6201c: r0 = int()
    //     0xa6201c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa62020: ldur            x1, [fp, #-0x38]
    // 0xa62024: r2 = 2
    //     0xa62024: movz            x2, #0x2
    // 0xa62028: r0 = _getValueOrData()
    //     0xa62028: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6202c: ldur            x3, [fp, #-0x38]
    // 0xa62030: LoadField: r1 = r3->field_f
    //     0xa62030: ldur            w1, [x3, #0xf]
    // 0xa62034: DecompressPointer r1
    //     0xa62034: add             x1, x1, HEAP, lsl #32
    // 0xa62038: cmp             w1, w0
    // 0xa6203c: b.ne            #0xa62048
    // 0xa62040: r4 = Null
    //     0xa62040: mov             x4, NULL
    // 0xa62044: b               #0xa6204c
    // 0xa62048: mov             x4, x0
    // 0xa6204c: mov             x0, x4
    // 0xa62050: stur            x4, [fp, #-0x20]
    // 0xa62054: r2 = Null
    //     0xa62054: mov             x2, NULL
    // 0xa62058: r1 = Null
    //     0xa62058: mov             x1, NULL
    // 0xa6205c: r4 = 60
    //     0xa6205c: movz            x4, #0x3c
    // 0xa62060: branchIfSmi(r0, 0xa6206c)
    //     0xa62060: tbz             w0, #0, #0xa6206c
    // 0xa62064: r4 = LoadClassIdInstr(r0)
    //     0xa62064: ldur            x4, [x0, #-1]
    //     0xa62068: ubfx            x4, x4, #0xc, #0x14
    // 0xa6206c: sub             x4, x4, #0x5e
    // 0xa62070: cmp             x4, #1
    // 0xa62074: b.ls            #0xa62088
    // 0xa62078: r8 = String
    //     0xa62078: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa6207c: r3 = Null
    //     0xa6207c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21428] Null
    //     0xa62080: ldr             x3, [x3, #0x428]
    // 0xa62084: r0 = String()
    //     0xa62084: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62088: ldur            x1, [fp, #-0x38]
    // 0xa6208c: r2 = 4
    //     0xa6208c: movz            x2, #0x4
    // 0xa62090: r0 = _getValueOrData()
    //     0xa62090: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62094: ldur            x3, [fp, #-0x38]
    // 0xa62098: LoadField: r1 = r3->field_f
    //     0xa62098: ldur            w1, [x3, #0xf]
    // 0xa6209c: DecompressPointer r1
    //     0xa6209c: add             x1, x1, HEAP, lsl #32
    // 0xa620a0: cmp             w1, w0
    // 0xa620a4: b.ne            #0xa620b0
    // 0xa620a8: r4 = Null
    //     0xa620a8: mov             x4, NULL
    // 0xa620ac: b               #0xa620b4
    // 0xa620b0: mov             x4, x0
    // 0xa620b4: mov             x0, x4
    // 0xa620b8: stur            x4, [fp, #-0x40]
    // 0xa620bc: r2 = Null
    //     0xa620bc: mov             x2, NULL
    // 0xa620c0: r1 = Null
    //     0xa620c0: mov             x1, NULL
    // 0xa620c4: branchIfSmi(r0, 0xa620ec)
    //     0xa620c4: tbz             w0, #0, #0xa620ec
    // 0xa620c8: r4 = LoadClassIdInstr(r0)
    //     0xa620c8: ldur            x4, [x0, #-1]
    //     0xa620cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa620d0: sub             x4, x4, #0x3c
    // 0xa620d4: cmp             x4, #1
    // 0xa620d8: b.ls            #0xa620ec
    // 0xa620dc: r8 = int
    //     0xa620dc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa620e0: r3 = Null
    //     0xa620e0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21438] Null
    //     0xa620e4: ldr             x3, [x3, #0x438]
    // 0xa620e8: r0 = int()
    //     0xa620e8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa620ec: ldur            x1, [fp, #-0x38]
    // 0xa620f0: r2 = 6
    //     0xa620f0: movz            x2, #0x6
    // 0xa620f4: r0 = _getValueOrData()
    //     0xa620f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa620f8: mov             x1, x0
    // 0xa620fc: ldur            x0, [fp, #-0x38]
    // 0xa62100: LoadField: r2 = r0->field_f
    //     0xa62100: ldur            w2, [x0, #0xf]
    // 0xa62104: DecompressPointer r2
    //     0xa62104: add             x2, x2, HEAP, lsl #32
    // 0xa62108: cmp             w2, w1
    // 0xa6210c: b.eq            #0xa62118
    // 0xa62110: cmp             w1, NULL
    // 0xa62114: b.ne            #0xa62120
    // 0xa62118: r3 = ""
    //     0xa62118: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa6211c: b               #0xa62190
    // 0xa62120: mov             x1, x0
    // 0xa62124: r2 = 6
    //     0xa62124: movz            x2, #0x6
    // 0xa62128: r0 = _getValueOrData()
    //     0xa62128: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6212c: mov             x1, x0
    // 0xa62130: ldur            x0, [fp, #-0x38]
    // 0xa62134: LoadField: r2 = r0->field_f
    //     0xa62134: ldur            w2, [x0, #0xf]
    // 0xa62138: DecompressPointer r2
    //     0xa62138: add             x2, x2, HEAP, lsl #32
    // 0xa6213c: cmp             w2, w1
    // 0xa62140: b.ne            #0xa6214c
    // 0xa62144: r3 = Null
    //     0xa62144: mov             x3, NULL
    // 0xa62148: b               #0xa62150
    // 0xa6214c: mov             x3, x1
    // 0xa62150: mov             x0, x3
    // 0xa62154: stur            x3, [fp, #-0x38]
    // 0xa62158: r2 = Null
    //     0xa62158: mov             x2, NULL
    // 0xa6215c: r1 = Null
    //     0xa6215c: mov             x1, NULL
    // 0xa62160: r4 = 60
    //     0xa62160: movz            x4, #0x3c
    // 0xa62164: branchIfSmi(r0, 0xa62170)
    //     0xa62164: tbz             w0, #0, #0xa62170
    // 0xa62168: r4 = LoadClassIdInstr(r0)
    //     0xa62168: ldur            x4, [x0, #-1]
    //     0xa6216c: ubfx            x4, x4, #0xc, #0x14
    // 0xa62170: sub             x4, x4, #0x5e
    // 0xa62174: cmp             x4, #1
    // 0xa62178: b.ls            #0xa6218c
    // 0xa6217c: r8 = String
    //     0xa6217c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62180: r3 = Null
    //     0xa62180: add             x3, PP, #0x21, lsl #12  ; [pp+0x21448] Null
    //     0xa62184: ldr             x3, [x3, #0x448]
    // 0xa62188: r0 = String()
    //     0xa62188: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa6218c: ldur            x3, [fp, #-0x38]
    // 0xa62190: ldur            x2, [fp, #-0x18]
    // 0xa62194: ldur            x1, [fp, #-0x20]
    // 0xa62198: ldur            x0, [fp, #-0x40]
    // 0xa6219c: stur            x3, [fp, #-0x38]
    // 0xa621a0: r4 = LoadInt32Instr(r2)
    //     0xa621a0: sbfx            x4, x2, #1, #0x1f
    //     0xa621a4: tbz             w2, #0, #0xa621ac
    //     0xa621a8: ldur            x4, [x2, #7]
    // 0xa621ac: stur            x4, [fp, #-8]
    // 0xa621b0: r0 = EncyclopediaCategory()
    //     0xa621b0: bl              #0xa62268  ; AllocateEncyclopediaCategoryStub -> EncyclopediaCategory (size=0x20)
    // 0xa621b4: mov             x1, x0
    // 0xa621b8: ldur            x0, [fp, #-8]
    // 0xa621bc: StoreField: r1->field_7 = r0
    //     0xa621bc: stur            x0, [x1, #7]
    // 0xa621c0: ldur            x0, [fp, #-0x20]
    // 0xa621c4: StoreField: r1->field_f = r0
    //     0xa621c4: stur            w0, [x1, #0xf]
    // 0xa621c8: ldur            x0, [fp, #-0x40]
    // 0xa621cc: r2 = LoadInt32Instr(r0)
    //     0xa621cc: sbfx            x2, x0, #1, #0x1f
    //     0xa621d0: tbz             w0, #0, #0xa621d8
    //     0xa621d4: ldur            x2, [x0, #7]
    // 0xa621d8: StoreField: r1->field_13 = r2
    //     0xa621d8: stur            x2, [x1, #0x13]
    // 0xa621dc: ldur            x0, [fp, #-0x38]
    // 0xa621e0: StoreField: r1->field_1b = r0
    //     0xa621e0: stur            w0, [x1, #0x1b]
    // 0xa621e4: mov             x0, x1
    // 0xa621e8: LeaveFrame
    //     0xa621e8: mov             SP, fp
    //     0xa621ec: ldp             fp, lr, [SP], #0x10
    // 0xa621f0: ret
    //     0xa621f0: ret             
    // 0xa621f4: r0 = RangeError()
    //     0xa621f4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa621f8: mov             x1, x0
    // 0xa621fc: r0 = "Not enough bytes available."
    //     0xa621fc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa62200: ldr             x0, [x0, #0x8a8]
    // 0xa62204: ArrayStore: r1[0] = r0  ; List_4
    //     0xa62204: stur            w0, [x1, #0x17]
    // 0xa62208: r2 = false
    //     0xa62208: add             x2, NULL, #0x30  ; false
    // 0xa6220c: StoreField: r1->field_b = r2
    //     0xa6220c: stur            w2, [x1, #0xb]
    // 0xa62210: mov             x0, x1
    // 0xa62214: r0 = Throw()
    //     0xa62214: bl              #0xec04b8  ; ThrowStub
    // 0xa62218: brk             #0
    // 0xa6221c: r0 = "Not enough bytes available."
    //     0xa6221c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa62220: ldr             x0, [x0, #0x8a8]
    // 0xa62224: r2 = false
    //     0xa62224: add             x2, NULL, #0x30  ; false
    // 0xa62228: r0 = RangeError()
    //     0xa62228: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa6222c: mov             x1, x0
    // 0xa62230: r0 = "Not enough bytes available."
    //     0xa62230: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa62234: ldr             x0, [x0, #0x8a8]
    // 0xa62238: ArrayStore: r1[0] = r0  ; List_4
    //     0xa62238: stur            w0, [x1, #0x17]
    // 0xa6223c: r0 = false
    //     0xa6223c: add             x0, NULL, #0x30  ; false
    // 0xa62240: StoreField: r1->field_b = r0
    //     0xa62240: stur            w0, [x1, #0xb]
    // 0xa62244: mov             x0, x1
    // 0xa62248: r0 = Throw()
    //     0xa62248: bl              #0xec04b8  ; ThrowStub
    // 0xa6224c: brk             #0
    // 0xa62250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa62250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62254: b               #0xa61ea0
    // 0xa62258: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa62258: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa6225c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6225c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62260: b               #0xa61f28
    // 0xa62264: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa62264: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd30c4, size: 0x32c
    // 0xbd30c4: EnterFrame
    //     0xbd30c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd30c8: mov             fp, SP
    // 0xbd30cc: AllocStack(0x28)
    //     0xbd30cc: sub             SP, SP, #0x28
    // 0xbd30d0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd30d0: mov             x4, x2
    //     0xbd30d4: stur            x2, [fp, #-8]
    //     0xbd30d8: stur            x3, [fp, #-0x10]
    // 0xbd30dc: CheckStackOverflow
    //     0xbd30dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd30e0: cmp             SP, x16
    //     0xbd30e4: b.ls            #0xbd33d4
    // 0xbd30e8: mov             x0, x3
    // 0xbd30ec: r2 = Null
    //     0xbd30ec: mov             x2, NULL
    // 0xbd30f0: r1 = Null
    //     0xbd30f0: mov             x1, NULL
    // 0xbd30f4: r4 = 60
    //     0xbd30f4: movz            x4, #0x3c
    // 0xbd30f8: branchIfSmi(r0, 0xbd3104)
    //     0xbd30f8: tbz             w0, #0, #0xbd3104
    // 0xbd30fc: r4 = LoadClassIdInstr(r0)
    //     0xbd30fc: ldur            x4, [x0, #-1]
    //     0xbd3100: ubfx            x4, x4, #0xc, #0x14
    // 0xbd3104: r17 = 5587
    //     0xbd3104: movz            x17, #0x15d3
    // 0xbd3108: cmp             x4, x17
    // 0xbd310c: b.eq            #0xbd3124
    // 0xbd3110: r8 = EncyclopediaCategory
    //     0xbd3110: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b378] Type: EncyclopediaCategory
    //     0xbd3114: ldr             x8, [x8, #0x378]
    // 0xbd3118: r3 = Null
    //     0xbd3118: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b380] Null
    //     0xbd311c: ldr             x3, [x3, #0x380]
    // 0xbd3120: r0 = EncyclopediaCategory()
    //     0xbd3120: bl              #0x8121b0  ; IsType_EncyclopediaCategory_Stub
    // 0xbd3124: ldur            x0, [fp, #-8]
    // 0xbd3128: LoadField: r1 = r0->field_b
    //     0xbd3128: ldur            w1, [x0, #0xb]
    // 0xbd312c: DecompressPointer r1
    //     0xbd312c: add             x1, x1, HEAP, lsl #32
    // 0xbd3130: LoadField: r2 = r1->field_13
    //     0xbd3130: ldur            w2, [x1, #0x13]
    // 0xbd3134: LoadField: r1 = r0->field_13
    //     0xbd3134: ldur            x1, [x0, #0x13]
    // 0xbd3138: r3 = LoadInt32Instr(r2)
    //     0xbd3138: sbfx            x3, x2, #1, #0x1f
    // 0xbd313c: sub             x2, x3, x1
    // 0xbd3140: cmp             x2, #1
    // 0xbd3144: b.ge            #0xbd3154
    // 0xbd3148: mov             x1, x0
    // 0xbd314c: r2 = 1
    //     0xbd314c: movz            x2, #0x1
    // 0xbd3150: r0 = _increaseBufferSize()
    //     0xbd3150: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3154: ldur            x3, [fp, #-8]
    // 0xbd3158: r2 = 4
    //     0xbd3158: movz            x2, #0x4
    // 0xbd315c: LoadField: r4 = r3->field_b
    //     0xbd315c: ldur            w4, [x3, #0xb]
    // 0xbd3160: DecompressPointer r4
    //     0xbd3160: add             x4, x4, HEAP, lsl #32
    // 0xbd3164: LoadField: r5 = r3->field_13
    //     0xbd3164: ldur            x5, [x3, #0x13]
    // 0xbd3168: add             x6, x5, #1
    // 0xbd316c: StoreField: r3->field_13 = r6
    //     0xbd316c: stur            x6, [x3, #0x13]
    // 0xbd3170: LoadField: r0 = r4->field_13
    //     0xbd3170: ldur            w0, [x4, #0x13]
    // 0xbd3174: r7 = LoadInt32Instr(r0)
    //     0xbd3174: sbfx            x7, x0, #1, #0x1f
    // 0xbd3178: mov             x0, x7
    // 0xbd317c: mov             x1, x5
    // 0xbd3180: cmp             x1, x0
    // 0xbd3184: b.hs            #0xbd33dc
    // 0xbd3188: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd3188: add             x0, x4, x5
    //     0xbd318c: strb            w2, [x0, #0x17]
    // 0xbd3190: sub             x0, x7, x6
    // 0xbd3194: cmp             x0, #1
    // 0xbd3198: b.ge            #0xbd31a8
    // 0xbd319c: mov             x1, x3
    // 0xbd31a0: r2 = 1
    //     0xbd31a0: movz            x2, #0x1
    // 0xbd31a4: r0 = _increaseBufferSize()
    //     0xbd31a4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd31a8: ldur            x2, [fp, #-8]
    // 0xbd31ac: ldur            x3, [fp, #-0x10]
    // 0xbd31b0: LoadField: r4 = r2->field_b
    //     0xbd31b0: ldur            w4, [x2, #0xb]
    // 0xbd31b4: DecompressPointer r4
    //     0xbd31b4: add             x4, x4, HEAP, lsl #32
    // 0xbd31b8: LoadField: r5 = r2->field_13
    //     0xbd31b8: ldur            x5, [x2, #0x13]
    // 0xbd31bc: add             x0, x5, #1
    // 0xbd31c0: StoreField: r2->field_13 = r0
    //     0xbd31c0: stur            x0, [x2, #0x13]
    // 0xbd31c4: LoadField: r0 = r4->field_13
    //     0xbd31c4: ldur            w0, [x4, #0x13]
    // 0xbd31c8: r1 = LoadInt32Instr(r0)
    //     0xbd31c8: sbfx            x1, x0, #1, #0x1f
    // 0xbd31cc: mov             x0, x1
    // 0xbd31d0: mov             x1, x5
    // 0xbd31d4: cmp             x1, x0
    // 0xbd31d8: b.hs            #0xbd33e0
    // 0xbd31dc: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd31dc: add             x0, x4, x5
    //     0xbd31e0: strb            wzr, [x0, #0x17]
    // 0xbd31e4: LoadField: r4 = r3->field_7
    //     0xbd31e4: ldur            x4, [x3, #7]
    // 0xbd31e8: r0 = BoxInt64Instr(r4)
    //     0xbd31e8: sbfiz           x0, x4, #1, #0x1f
    //     0xbd31ec: cmp             x4, x0, asr #1
    //     0xbd31f0: b.eq            #0xbd31fc
    //     0xbd31f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd31f8: stur            x4, [x0, #7]
    // 0xbd31fc: r16 = <int>
    //     0xbd31fc: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd3200: stp             x2, x16, [SP, #8]
    // 0xbd3204: str             x0, [SP]
    // 0xbd3208: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3208: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd320c: r0 = write()
    //     0xbd320c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3210: ldur            x0, [fp, #-8]
    // 0xbd3214: LoadField: r1 = r0->field_b
    //     0xbd3214: ldur            w1, [x0, #0xb]
    // 0xbd3218: DecompressPointer r1
    //     0xbd3218: add             x1, x1, HEAP, lsl #32
    // 0xbd321c: LoadField: r2 = r1->field_13
    //     0xbd321c: ldur            w2, [x1, #0x13]
    // 0xbd3220: LoadField: r1 = r0->field_13
    //     0xbd3220: ldur            x1, [x0, #0x13]
    // 0xbd3224: r3 = LoadInt32Instr(r2)
    //     0xbd3224: sbfx            x3, x2, #1, #0x1f
    // 0xbd3228: sub             x2, x3, x1
    // 0xbd322c: cmp             x2, #1
    // 0xbd3230: b.ge            #0xbd3240
    // 0xbd3234: mov             x1, x0
    // 0xbd3238: r2 = 1
    //     0xbd3238: movz            x2, #0x1
    // 0xbd323c: r0 = _increaseBufferSize()
    //     0xbd323c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3240: ldur            x2, [fp, #-8]
    // 0xbd3244: ldur            x3, [fp, #-0x10]
    // 0xbd3248: r4 = 1
    //     0xbd3248: movz            x4, #0x1
    // 0xbd324c: LoadField: r5 = r2->field_b
    //     0xbd324c: ldur            w5, [x2, #0xb]
    // 0xbd3250: DecompressPointer r5
    //     0xbd3250: add             x5, x5, HEAP, lsl #32
    // 0xbd3254: LoadField: r6 = r2->field_13
    //     0xbd3254: ldur            x6, [x2, #0x13]
    // 0xbd3258: add             x0, x6, #1
    // 0xbd325c: StoreField: r2->field_13 = r0
    //     0xbd325c: stur            x0, [x2, #0x13]
    // 0xbd3260: LoadField: r0 = r5->field_13
    //     0xbd3260: ldur            w0, [x5, #0x13]
    // 0xbd3264: r1 = LoadInt32Instr(r0)
    //     0xbd3264: sbfx            x1, x0, #1, #0x1f
    // 0xbd3268: mov             x0, x1
    // 0xbd326c: mov             x1, x6
    // 0xbd3270: cmp             x1, x0
    // 0xbd3274: b.hs            #0xbd33e4
    // 0xbd3278: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3278: add             x0, x5, x6
    //     0xbd327c: strb            w4, [x0, #0x17]
    // 0xbd3280: LoadField: r0 = r3->field_f
    //     0xbd3280: ldur            w0, [x3, #0xf]
    // 0xbd3284: DecompressPointer r0
    //     0xbd3284: add             x0, x0, HEAP, lsl #32
    // 0xbd3288: r16 = <String>
    //     0xbd3288: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd328c: stp             x2, x16, [SP, #8]
    // 0xbd3290: str             x0, [SP]
    // 0xbd3294: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3294: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3298: r0 = write()
    //     0xbd3298: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd329c: ldur            x0, [fp, #-8]
    // 0xbd32a0: LoadField: r1 = r0->field_b
    //     0xbd32a0: ldur            w1, [x0, #0xb]
    // 0xbd32a4: DecompressPointer r1
    //     0xbd32a4: add             x1, x1, HEAP, lsl #32
    // 0xbd32a8: LoadField: r2 = r1->field_13
    //     0xbd32a8: ldur            w2, [x1, #0x13]
    // 0xbd32ac: LoadField: r1 = r0->field_13
    //     0xbd32ac: ldur            x1, [x0, #0x13]
    // 0xbd32b0: r3 = LoadInt32Instr(r2)
    //     0xbd32b0: sbfx            x3, x2, #1, #0x1f
    // 0xbd32b4: sub             x2, x3, x1
    // 0xbd32b8: cmp             x2, #1
    // 0xbd32bc: b.ge            #0xbd32cc
    // 0xbd32c0: mov             x1, x0
    // 0xbd32c4: r2 = 1
    //     0xbd32c4: movz            x2, #0x1
    // 0xbd32c8: r0 = _increaseBufferSize()
    //     0xbd32c8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd32cc: ldur            x2, [fp, #-8]
    // 0xbd32d0: ldur            x3, [fp, #-0x10]
    // 0xbd32d4: r4 = 2
    //     0xbd32d4: movz            x4, #0x2
    // 0xbd32d8: LoadField: r5 = r2->field_b
    //     0xbd32d8: ldur            w5, [x2, #0xb]
    // 0xbd32dc: DecompressPointer r5
    //     0xbd32dc: add             x5, x5, HEAP, lsl #32
    // 0xbd32e0: LoadField: r6 = r2->field_13
    //     0xbd32e0: ldur            x6, [x2, #0x13]
    // 0xbd32e4: add             x0, x6, #1
    // 0xbd32e8: StoreField: r2->field_13 = r0
    //     0xbd32e8: stur            x0, [x2, #0x13]
    // 0xbd32ec: LoadField: r0 = r5->field_13
    //     0xbd32ec: ldur            w0, [x5, #0x13]
    // 0xbd32f0: r1 = LoadInt32Instr(r0)
    //     0xbd32f0: sbfx            x1, x0, #1, #0x1f
    // 0xbd32f4: mov             x0, x1
    // 0xbd32f8: mov             x1, x6
    // 0xbd32fc: cmp             x1, x0
    // 0xbd3300: b.hs            #0xbd33e8
    // 0xbd3304: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3304: add             x0, x5, x6
    //     0xbd3308: strb            w4, [x0, #0x17]
    // 0xbd330c: LoadField: r4 = r3->field_13
    //     0xbd330c: ldur            x4, [x3, #0x13]
    // 0xbd3310: r0 = BoxInt64Instr(r4)
    //     0xbd3310: sbfiz           x0, x4, #1, #0x1f
    //     0xbd3314: cmp             x4, x0, asr #1
    //     0xbd3318: b.eq            #0xbd3324
    //     0xbd331c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd3320: stur            x4, [x0, #7]
    // 0xbd3324: r16 = <int>
    //     0xbd3324: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd3328: stp             x2, x16, [SP, #8]
    // 0xbd332c: str             x0, [SP]
    // 0xbd3330: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3330: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3334: r0 = write()
    //     0xbd3334: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3338: ldur            x0, [fp, #-8]
    // 0xbd333c: LoadField: r1 = r0->field_b
    //     0xbd333c: ldur            w1, [x0, #0xb]
    // 0xbd3340: DecompressPointer r1
    //     0xbd3340: add             x1, x1, HEAP, lsl #32
    // 0xbd3344: LoadField: r2 = r1->field_13
    //     0xbd3344: ldur            w2, [x1, #0x13]
    // 0xbd3348: LoadField: r1 = r0->field_13
    //     0xbd3348: ldur            x1, [x0, #0x13]
    // 0xbd334c: r3 = LoadInt32Instr(r2)
    //     0xbd334c: sbfx            x3, x2, #1, #0x1f
    // 0xbd3350: sub             x2, x3, x1
    // 0xbd3354: cmp             x2, #1
    // 0xbd3358: b.ge            #0xbd3368
    // 0xbd335c: mov             x1, x0
    // 0xbd3360: r2 = 1
    //     0xbd3360: movz            x2, #0x1
    // 0xbd3364: r0 = _increaseBufferSize()
    //     0xbd3364: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3368: ldur            x2, [fp, #-8]
    // 0xbd336c: ldur            x3, [fp, #-0x10]
    // 0xbd3370: r4 = 3
    //     0xbd3370: movz            x4, #0x3
    // 0xbd3374: LoadField: r5 = r2->field_b
    //     0xbd3374: ldur            w5, [x2, #0xb]
    // 0xbd3378: DecompressPointer r5
    //     0xbd3378: add             x5, x5, HEAP, lsl #32
    // 0xbd337c: LoadField: r6 = r2->field_13
    //     0xbd337c: ldur            x6, [x2, #0x13]
    // 0xbd3380: add             x0, x6, #1
    // 0xbd3384: StoreField: r2->field_13 = r0
    //     0xbd3384: stur            x0, [x2, #0x13]
    // 0xbd3388: LoadField: r0 = r5->field_13
    //     0xbd3388: ldur            w0, [x5, #0x13]
    // 0xbd338c: r1 = LoadInt32Instr(r0)
    //     0xbd338c: sbfx            x1, x0, #1, #0x1f
    // 0xbd3390: mov             x0, x1
    // 0xbd3394: mov             x1, x6
    // 0xbd3398: cmp             x1, x0
    // 0xbd339c: b.hs            #0xbd33ec
    // 0xbd33a0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd33a0: add             x0, x5, x6
    //     0xbd33a4: strb            w4, [x0, #0x17]
    // 0xbd33a8: LoadField: r0 = r3->field_1b
    //     0xbd33a8: ldur            w0, [x3, #0x1b]
    // 0xbd33ac: DecompressPointer r0
    //     0xbd33ac: add             x0, x0, HEAP, lsl #32
    // 0xbd33b0: r16 = <String>
    //     0xbd33b0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd33b4: stp             x2, x16, [SP, #8]
    // 0xbd33b8: str             x0, [SP]
    // 0xbd33bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd33bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd33c0: r0 = write()
    //     0xbd33c0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd33c4: r0 = Null
    //     0xbd33c4: mov             x0, NULL
    // 0xbd33c8: LeaveFrame
    //     0xbd33c8: mov             SP, fp
    //     0xbd33cc: ldp             fp, lr, [SP], #0x10
    // 0xbd33d0: ret
    //     0xbd33d0: ret             
    // 0xbd33d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd33d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd33d8: b               #0xbd30e8
    // 0xbd33dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd33dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd33e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd33e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd33e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd33e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd33e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd33e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd33ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd33ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf018c, size: 0x24
    // 0xbf018c: r1 = 102
    //     0xbf018c: movz            x1, #0x66
    // 0xbf0190: r16 = LoadInt32Instr(r1)
    //     0xbf0190: sbfx            x16, x1, #1, #0x1f
    // 0xbf0194: r17 = 11601
    //     0xbf0194: movz            x17, #0x2d51
    // 0xbf0198: mul             x0, x16, x17
    // 0xbf019c: umulh           x16, x16, x17
    // 0xbf01a0: eor             x0, x0, x16
    // 0xbf01a4: r0 = 0
    //     0xbf01a4: eor             x0, x0, x0, lsr #32
    // 0xbf01a8: ubfiz           x0, x0, #1, #0x1e
    // 0xbf01ac: ret
    //     0xbf01ac: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76728, size: 0x9c
    // 0xd76728: EnterFrame
    //     0xd76728: stp             fp, lr, [SP, #-0x10]!
    //     0xd7672c: mov             fp, SP
    // 0xd76730: AllocStack(0x10)
    //     0xd76730: sub             SP, SP, #0x10
    // 0xd76734: CheckStackOverflow
    //     0xd76734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76738: cmp             SP, x16
    //     0xd7673c: b.ls            #0xd767bc
    // 0xd76740: ldr             x0, [fp, #0x10]
    // 0xd76744: cmp             w0, NULL
    // 0xd76748: b.ne            #0xd7675c
    // 0xd7674c: r0 = false
    //     0xd7674c: add             x0, NULL, #0x30  ; false
    // 0xd76750: LeaveFrame
    //     0xd76750: mov             SP, fp
    //     0xd76754: ldp             fp, lr, [SP], #0x10
    // 0xd76758: ret
    //     0xd76758: ret             
    // 0xd7675c: ldr             x1, [fp, #0x18]
    // 0xd76760: cmp             w1, w0
    // 0xd76764: b.ne            #0xd76770
    // 0xd76768: r0 = true
    //     0xd76768: add             x0, NULL, #0x20  ; true
    // 0xd7676c: b               #0xd767b0
    // 0xd76770: r1 = 60
    //     0xd76770: movz            x1, #0x3c
    // 0xd76774: branchIfSmi(r0, 0xd76780)
    //     0xd76774: tbz             w0, #0, #0xd76780
    // 0xd76778: r1 = LoadClassIdInstr(r0)
    //     0xd76778: ldur            x1, [x0, #-1]
    //     0xd7677c: ubfx            x1, x1, #0xc, #0x14
    // 0xd76780: cmp             x1, #0x67b
    // 0xd76784: b.ne            #0xd767ac
    // 0xd76788: r16 = EncyclopediaCategoryAdapter
    //     0xd76788: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b370] Type: EncyclopediaCategoryAdapter
    //     0xd7678c: ldr             x16, [x16, #0x370]
    // 0xd76790: r30 = EncyclopediaCategoryAdapter
    //     0xd76790: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b370] Type: EncyclopediaCategoryAdapter
    //     0xd76794: ldr             lr, [lr, #0x370]
    // 0xd76798: stp             lr, x16, [SP]
    // 0xd7679c: r0 = ==()
    //     0xd7679c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd767a0: tbnz            w0, #4, #0xd767ac
    // 0xd767a4: r0 = true
    //     0xd767a4: add             x0, NULL, #0x20  ; true
    // 0xd767a8: b               #0xd767b0
    // 0xd767ac: r0 = false
    //     0xd767ac: add             x0, NULL, #0x30  ; false
    // 0xd767b0: LeaveFrame
    //     0xd767b0: mov             SP, fp
    //     0xd767b4: ldp             fp, lr, [SP], #0x10
    // 0xd767b8: ret
    //     0xd767b8: ret             
    // 0xd767bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd767bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd767c0: b               #0xd76740
  }
}

// class id: 1660, size: 0x14, field offset: 0xc
class EncyclopediaAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa619ac, size: 0x4cc
    // 0xa619ac: EnterFrame
    //     0xa619ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa619b0: mov             fp, SP
    // 0xa619b4: AllocStack(0x60)
    //     0xa619b4: sub             SP, SP, #0x60
    // 0xa619b8: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa619b8: stur            x2, [fp, #-0x20]
    // 0xa619bc: CheckStackOverflow
    //     0xa619bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa619c0: cmp             SP, x16
    //     0xa619c4: b.ls            #0xa61e60
    // 0xa619c8: LoadField: r3 = r2->field_23
    //     0xa619c8: ldur            x3, [x2, #0x23]
    // 0xa619cc: add             x0, x3, #1
    // 0xa619d0: LoadField: r1 = r2->field_1b
    //     0xa619d0: ldur            x1, [x2, #0x1b]
    // 0xa619d4: cmp             x0, x1
    // 0xa619d8: b.gt            #0xa61e04
    // 0xa619dc: LoadField: r4 = r2->field_7
    //     0xa619dc: ldur            w4, [x2, #7]
    // 0xa619e0: DecompressPointer r4
    //     0xa619e0: add             x4, x4, HEAP, lsl #32
    // 0xa619e4: stur            x4, [fp, #-0x18]
    // 0xa619e8: StoreField: r2->field_23 = r0
    //     0xa619e8: stur            x0, [x2, #0x23]
    // 0xa619ec: LoadField: r0 = r4->field_13
    //     0xa619ec: ldur            w0, [x4, #0x13]
    // 0xa619f0: r5 = LoadInt32Instr(r0)
    //     0xa619f0: sbfx            x5, x0, #1, #0x1f
    // 0xa619f4: mov             x0, x5
    // 0xa619f8: mov             x1, x3
    // 0xa619fc: stur            x5, [fp, #-0x10]
    // 0xa61a00: cmp             x1, x0
    // 0xa61a04: b.hs            #0xa61e68
    // 0xa61a08: LoadField: r0 = r4->field_7
    //     0xa61a08: ldur            x0, [x4, #7]
    // 0xa61a0c: ldrb            w1, [x0, x3]
    // 0xa61a10: stur            x1, [fp, #-8]
    // 0xa61a14: r16 = <int, dynamic>
    //     0xa61a14: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa61a18: ldr             x16, [x16, #0xac0]
    // 0xa61a1c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa61a20: stp             lr, x16, [SP]
    // 0xa61a24: r0 = Map._fromLiteral()
    //     0xa61a24: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa61a28: mov             x2, x0
    // 0xa61a2c: stur            x2, [fp, #-0x38]
    // 0xa61a30: r6 = 0
    //     0xa61a30: movz            x6, #0
    // 0xa61a34: ldur            x3, [fp, #-0x20]
    // 0xa61a38: ldur            x4, [fp, #-0x18]
    // 0xa61a3c: ldur            x5, [fp, #-8]
    // 0xa61a40: stur            x6, [fp, #-0x30]
    // 0xa61a44: CheckStackOverflow
    //     0xa61a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61a48: cmp             SP, x16
    //     0xa61a4c: b.ls            #0xa61e6c
    // 0xa61a50: cmp             x6, x5
    // 0xa61a54: b.ge            #0xa61ae0
    // 0xa61a58: LoadField: r7 = r3->field_23
    //     0xa61a58: ldur            x7, [x3, #0x23]
    // 0xa61a5c: add             x0, x7, #1
    // 0xa61a60: LoadField: r1 = r3->field_1b
    //     0xa61a60: ldur            x1, [x3, #0x1b]
    // 0xa61a64: cmp             x0, x1
    // 0xa61a68: b.gt            #0xa61e2c
    // 0xa61a6c: StoreField: r3->field_23 = r0
    //     0xa61a6c: stur            x0, [x3, #0x23]
    // 0xa61a70: ldur            x0, [fp, #-0x10]
    // 0xa61a74: mov             x1, x7
    // 0xa61a78: cmp             x1, x0
    // 0xa61a7c: b.hs            #0xa61e74
    // 0xa61a80: LoadField: r0 = r4->field_7
    //     0xa61a80: ldur            x0, [x4, #7]
    // 0xa61a84: ldrb            w8, [x0, x7]
    // 0xa61a88: mov             x1, x3
    // 0xa61a8c: stur            x8, [fp, #-0x28]
    // 0xa61a90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa61a90: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa61a94: r0 = read()
    //     0xa61a94: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa61a98: mov             x1, x0
    // 0xa61a9c: ldur            x0, [fp, #-0x28]
    // 0xa61aa0: lsl             x2, x0, #1
    // 0xa61aa4: r16 = LoadInt32Instr(r2)
    //     0xa61aa4: sbfx            x16, x2, #1, #0x1f
    // 0xa61aa8: r17 = 11601
    //     0xa61aa8: movz            x17, #0x2d51
    // 0xa61aac: mul             x0, x16, x17
    // 0xa61ab0: umulh           x16, x16, x17
    // 0xa61ab4: eor             x0, x0, x16
    // 0xa61ab8: r0 = 0
    //     0xa61ab8: eor             x0, x0, x0, lsr #32
    // 0xa61abc: ubfiz           x0, x0, #1, #0x1e
    // 0xa61ac0: r5 = LoadInt32Instr(r0)
    //     0xa61ac0: sbfx            x5, x0, #1, #0x1f
    // 0xa61ac4: mov             x3, x1
    // 0xa61ac8: ldur            x1, [fp, #-0x38]
    // 0xa61acc: r0 = _set()
    //     0xa61acc: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa61ad0: ldur            x0, [fp, #-0x30]
    // 0xa61ad4: add             x6, x0, #1
    // 0xa61ad8: ldur            x2, [fp, #-0x38]
    // 0xa61adc: b               #0xa61a34
    // 0xa61ae0: mov             x0, x2
    // 0xa61ae4: mov             x1, x0
    // 0xa61ae8: r2 = 0
    //     0xa61ae8: movz            x2, #0
    // 0xa61aec: r0 = _getValueOrData()
    //     0xa61aec: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61af0: ldur            x3, [fp, #-0x38]
    // 0xa61af4: LoadField: r1 = r3->field_f
    //     0xa61af4: ldur            w1, [x3, #0xf]
    // 0xa61af8: DecompressPointer r1
    //     0xa61af8: add             x1, x1, HEAP, lsl #32
    // 0xa61afc: cmp             w1, w0
    // 0xa61b00: b.ne            #0xa61b0c
    // 0xa61b04: r4 = Null
    //     0xa61b04: mov             x4, NULL
    // 0xa61b08: b               #0xa61b10
    // 0xa61b0c: mov             x4, x0
    // 0xa61b10: mov             x0, x4
    // 0xa61b14: stur            x4, [fp, #-0x18]
    // 0xa61b18: r2 = Null
    //     0xa61b18: mov             x2, NULL
    // 0xa61b1c: r1 = Null
    //     0xa61b1c: mov             x1, NULL
    // 0xa61b20: branchIfSmi(r0, 0xa61b48)
    //     0xa61b20: tbz             w0, #0, #0xa61b48
    // 0xa61b24: r4 = LoadClassIdInstr(r0)
    //     0xa61b24: ldur            x4, [x0, #-1]
    //     0xa61b28: ubfx            x4, x4, #0xc, #0x14
    // 0xa61b2c: sub             x4, x4, #0x3c
    // 0xa61b30: cmp             x4, #1
    // 0xa61b34: b.ls            #0xa61b48
    // 0xa61b38: r8 = int
    //     0xa61b38: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa61b3c: r3 = Null
    //     0xa61b3c: add             x3, PP, #0x21, lsl #12  ; [pp+0x213b8] Null
    //     0xa61b40: ldr             x3, [x3, #0x3b8]
    // 0xa61b44: r0 = int()
    //     0xa61b44: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa61b48: ldur            x1, [fp, #-0x38]
    // 0xa61b4c: r2 = 2
    //     0xa61b4c: movz            x2, #0x2
    // 0xa61b50: r0 = _getValueOrData()
    //     0xa61b50: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61b54: ldur            x3, [fp, #-0x38]
    // 0xa61b58: LoadField: r1 = r3->field_f
    //     0xa61b58: ldur            w1, [x3, #0xf]
    // 0xa61b5c: DecompressPointer r1
    //     0xa61b5c: add             x1, x1, HEAP, lsl #32
    // 0xa61b60: cmp             w1, w0
    // 0xa61b64: b.ne            #0xa61b70
    // 0xa61b68: r4 = Null
    //     0xa61b68: mov             x4, NULL
    // 0xa61b6c: b               #0xa61b74
    // 0xa61b70: mov             x4, x0
    // 0xa61b74: mov             x0, x4
    // 0xa61b78: stur            x4, [fp, #-0x20]
    // 0xa61b7c: r2 = Null
    //     0xa61b7c: mov             x2, NULL
    // 0xa61b80: r1 = Null
    //     0xa61b80: mov             x1, NULL
    // 0xa61b84: r4 = 60
    //     0xa61b84: movz            x4, #0x3c
    // 0xa61b88: branchIfSmi(r0, 0xa61b94)
    //     0xa61b88: tbz             w0, #0, #0xa61b94
    // 0xa61b8c: r4 = LoadClassIdInstr(r0)
    //     0xa61b8c: ldur            x4, [x0, #-1]
    //     0xa61b90: ubfx            x4, x4, #0xc, #0x14
    // 0xa61b94: sub             x4, x4, #0x5e
    // 0xa61b98: cmp             x4, #1
    // 0xa61b9c: b.ls            #0xa61bb0
    // 0xa61ba0: r8 = String
    //     0xa61ba0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa61ba4: r3 = Null
    //     0xa61ba4: add             x3, PP, #0x21, lsl #12  ; [pp+0x213c8] Null
    //     0xa61ba8: ldr             x3, [x3, #0x3c8]
    // 0xa61bac: r0 = String()
    //     0xa61bac: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa61bb0: ldur            x1, [fp, #-0x38]
    // 0xa61bb4: r2 = 4
    //     0xa61bb4: movz            x2, #0x4
    // 0xa61bb8: r0 = _getValueOrData()
    //     0xa61bb8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61bbc: ldur            x3, [fp, #-0x38]
    // 0xa61bc0: LoadField: r1 = r3->field_f
    //     0xa61bc0: ldur            w1, [x3, #0xf]
    // 0xa61bc4: DecompressPointer r1
    //     0xa61bc4: add             x1, x1, HEAP, lsl #32
    // 0xa61bc8: cmp             w1, w0
    // 0xa61bcc: b.ne            #0xa61bd8
    // 0xa61bd0: r4 = Null
    //     0xa61bd0: mov             x4, NULL
    // 0xa61bd4: b               #0xa61bdc
    // 0xa61bd8: mov             x4, x0
    // 0xa61bdc: mov             x0, x4
    // 0xa61be0: stur            x4, [fp, #-0x40]
    // 0xa61be4: r2 = Null
    //     0xa61be4: mov             x2, NULL
    // 0xa61be8: r1 = Null
    //     0xa61be8: mov             x1, NULL
    // 0xa61bec: r4 = 60
    //     0xa61bec: movz            x4, #0x3c
    // 0xa61bf0: branchIfSmi(r0, 0xa61bfc)
    //     0xa61bf0: tbz             w0, #0, #0xa61bfc
    // 0xa61bf4: r4 = LoadClassIdInstr(r0)
    //     0xa61bf4: ldur            x4, [x0, #-1]
    //     0xa61bf8: ubfx            x4, x4, #0xc, #0x14
    // 0xa61bfc: r17 = 5587
    //     0xa61bfc: movz            x17, #0x15d3
    // 0xa61c00: cmp             x4, x17
    // 0xa61c04: b.eq            #0xa61c1c
    // 0xa61c08: r8 = EncyclopediaCategory
    //     0xa61c08: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b378] Type: EncyclopediaCategory
    //     0xa61c0c: ldr             x8, [x8, #0x378]
    // 0xa61c10: r3 = Null
    //     0xa61c10: add             x3, PP, #0x21, lsl #12  ; [pp+0x213d8] Null
    //     0xa61c14: ldr             x3, [x3, #0x3d8]
    // 0xa61c18: r0 = EncyclopediaCategory()
    //     0xa61c18: bl              #0x8121b0  ; IsType_EncyclopediaCategory_Stub
    // 0xa61c1c: ldur            x1, [fp, #-0x38]
    // 0xa61c20: r2 = 6
    //     0xa61c20: movz            x2, #0x6
    // 0xa61c24: r0 = _getValueOrData()
    //     0xa61c24: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61c28: ldur            x3, [fp, #-0x38]
    // 0xa61c2c: LoadField: r1 = r3->field_f
    //     0xa61c2c: ldur            w1, [x3, #0xf]
    // 0xa61c30: DecompressPointer r1
    //     0xa61c30: add             x1, x1, HEAP, lsl #32
    // 0xa61c34: cmp             w1, w0
    // 0xa61c38: b.ne            #0xa61c44
    // 0xa61c3c: r4 = Null
    //     0xa61c3c: mov             x4, NULL
    // 0xa61c40: b               #0xa61c48
    // 0xa61c44: mov             x4, x0
    // 0xa61c48: mov             x0, x4
    // 0xa61c4c: stur            x4, [fp, #-0x48]
    // 0xa61c50: r2 = Null
    //     0xa61c50: mov             x2, NULL
    // 0xa61c54: r1 = Null
    //     0xa61c54: mov             x1, NULL
    // 0xa61c58: r4 = 60
    //     0xa61c58: movz            x4, #0x3c
    // 0xa61c5c: branchIfSmi(r0, 0xa61c68)
    //     0xa61c5c: tbz             w0, #0, #0xa61c68
    // 0xa61c60: r4 = LoadClassIdInstr(r0)
    //     0xa61c60: ldur            x4, [x0, #-1]
    //     0xa61c64: ubfx            x4, x4, #0xc, #0x14
    // 0xa61c68: r17 = 5582
    //     0xa61c68: movz            x17, #0x15ce
    // 0xa61c6c: cmp             x4, x17
    // 0xa61c70: b.eq            #0xa61c88
    // 0xa61c74: r8 = Image
    //     0xa61c74: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b560] Type: Image
    //     0xa61c78: ldr             x8, [x8, #0x560]
    // 0xa61c7c: r3 = Null
    //     0xa61c7c: add             x3, PP, #0x21, lsl #12  ; [pp+0x213e8] Null
    //     0xa61c80: ldr             x3, [x3, #0x3e8]
    // 0xa61c84: r0 = Image()
    //     0xa61c84: bl              #0x72c934  ; IsType_Image_Stub
    // 0xa61c88: ldur            x1, [fp, #-0x38]
    // 0xa61c8c: r2 = 8
    //     0xa61c8c: movz            x2, #0x8
    // 0xa61c90: r0 = _getValueOrData()
    //     0xa61c90: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61c94: ldur            x3, [fp, #-0x38]
    // 0xa61c98: LoadField: r1 = r3->field_f
    //     0xa61c98: ldur            w1, [x3, #0xf]
    // 0xa61c9c: DecompressPointer r1
    //     0xa61c9c: add             x1, x1, HEAP, lsl #32
    // 0xa61ca0: cmp             w1, w0
    // 0xa61ca4: b.ne            #0xa61cb0
    // 0xa61ca8: r4 = Null
    //     0xa61ca8: mov             x4, NULL
    // 0xa61cac: b               #0xa61cb4
    // 0xa61cb0: mov             x4, x0
    // 0xa61cb4: mov             x0, x4
    // 0xa61cb8: stur            x4, [fp, #-0x50]
    // 0xa61cbc: r2 = Null
    //     0xa61cbc: mov             x2, NULL
    // 0xa61cc0: r1 = Null
    //     0xa61cc0: mov             x1, NULL
    // 0xa61cc4: r4 = 60
    //     0xa61cc4: movz            x4, #0x3c
    // 0xa61cc8: branchIfSmi(r0, 0xa61cd4)
    //     0xa61cc8: tbz             w0, #0, #0xa61cd4
    // 0xa61ccc: r4 = LoadClassIdInstr(r0)
    //     0xa61ccc: ldur            x4, [x0, #-1]
    //     0xa61cd0: ubfx            x4, x4, #0xc, #0x14
    // 0xa61cd4: sub             x4, x4, #0x5e
    // 0xa61cd8: cmp             x4, #1
    // 0xa61cdc: b.ls            #0xa61cf0
    // 0xa61ce0: r8 = String
    //     0xa61ce0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa61ce4: r3 = Null
    //     0xa61ce4: add             x3, PP, #0x21, lsl #12  ; [pp+0x213f8] Null
    //     0xa61ce8: ldr             x3, [x3, #0x3f8]
    // 0xa61cec: r0 = String()
    //     0xa61cec: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa61cf0: ldur            x1, [fp, #-0x38]
    // 0xa61cf4: r2 = 10
    //     0xa61cf4: movz            x2, #0xa
    // 0xa61cf8: r0 = _getValueOrData()
    //     0xa61cf8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61cfc: mov             x1, x0
    // 0xa61d00: ldur            x0, [fp, #-0x38]
    // 0xa61d04: LoadField: r2 = r0->field_f
    //     0xa61d04: ldur            w2, [x0, #0xf]
    // 0xa61d08: DecompressPointer r2
    //     0xa61d08: add             x2, x2, HEAP, lsl #32
    // 0xa61d0c: cmp             w2, w1
    // 0xa61d10: b.eq            #0xa61d1c
    // 0xa61d14: cmp             w1, NULL
    // 0xa61d18: b.ne            #0xa61d24
    // 0xa61d1c: r5 = ""
    //     0xa61d1c: ldr             x5, [PP, #0x288]  ; [pp+0x288] ""
    // 0xa61d20: b               #0xa61d94
    // 0xa61d24: mov             x1, x0
    // 0xa61d28: r2 = 10
    //     0xa61d28: movz            x2, #0xa
    // 0xa61d2c: r0 = _getValueOrData()
    //     0xa61d2c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa61d30: mov             x1, x0
    // 0xa61d34: ldur            x0, [fp, #-0x38]
    // 0xa61d38: LoadField: r2 = r0->field_f
    //     0xa61d38: ldur            w2, [x0, #0xf]
    // 0xa61d3c: DecompressPointer r2
    //     0xa61d3c: add             x2, x2, HEAP, lsl #32
    // 0xa61d40: cmp             w2, w1
    // 0xa61d44: b.ne            #0xa61d50
    // 0xa61d48: r3 = Null
    //     0xa61d48: mov             x3, NULL
    // 0xa61d4c: b               #0xa61d54
    // 0xa61d50: mov             x3, x1
    // 0xa61d54: mov             x0, x3
    // 0xa61d58: stur            x3, [fp, #-0x38]
    // 0xa61d5c: r2 = Null
    //     0xa61d5c: mov             x2, NULL
    // 0xa61d60: r1 = Null
    //     0xa61d60: mov             x1, NULL
    // 0xa61d64: r4 = 60
    //     0xa61d64: movz            x4, #0x3c
    // 0xa61d68: branchIfSmi(r0, 0xa61d74)
    //     0xa61d68: tbz             w0, #0, #0xa61d74
    // 0xa61d6c: r4 = LoadClassIdInstr(r0)
    //     0xa61d6c: ldur            x4, [x0, #-1]
    //     0xa61d70: ubfx            x4, x4, #0xc, #0x14
    // 0xa61d74: sub             x4, x4, #0x5e
    // 0xa61d78: cmp             x4, #1
    // 0xa61d7c: b.ls            #0xa61d90
    // 0xa61d80: r8 = String
    //     0xa61d80: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa61d84: r3 = Null
    //     0xa61d84: add             x3, PP, #0x21, lsl #12  ; [pp+0x21408] Null
    //     0xa61d88: ldr             x3, [x3, #0x408]
    // 0xa61d8c: r0 = String()
    //     0xa61d8c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa61d90: ldur            x5, [fp, #-0x38]
    // 0xa61d94: ldur            x4, [fp, #-0x18]
    // 0xa61d98: ldur            x3, [fp, #-0x20]
    // 0xa61d9c: ldur            x2, [fp, #-0x40]
    // 0xa61da0: ldur            x1, [fp, #-0x48]
    // 0xa61da4: ldur            x0, [fp, #-0x50]
    // 0xa61da8: stur            x5, [fp, #-0x38]
    // 0xa61dac: r6 = LoadInt32Instr(r4)
    //     0xa61dac: sbfx            x6, x4, #1, #0x1f
    //     0xa61db0: tbz             w4, #0, #0xa61db8
    //     0xa61db4: ldur            x6, [x4, #7]
    // 0xa61db8: stur            x6, [fp, #-8]
    // 0xa61dbc: r0 = Encyclopedia()
    //     0xa61dbc: bl              #0xa61e78  ; AllocateEncyclopediaStub -> Encyclopedia (size=0x24)
    // 0xa61dc0: mov             x1, x0
    // 0xa61dc4: ldur            x0, [fp, #-8]
    // 0xa61dc8: StoreField: r1->field_7 = r0
    //     0xa61dc8: stur            x0, [x1, #7]
    // 0xa61dcc: ldur            x0, [fp, #-0x20]
    // 0xa61dd0: StoreField: r1->field_f = r0
    //     0xa61dd0: stur            w0, [x1, #0xf]
    // 0xa61dd4: ldur            x0, [fp, #-0x40]
    // 0xa61dd8: StoreField: r1->field_13 = r0
    //     0xa61dd8: stur            w0, [x1, #0x13]
    // 0xa61ddc: ldur            x0, [fp, #-0x48]
    // 0xa61de0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61de0: stur            w0, [x1, #0x17]
    // 0xa61de4: ldur            x0, [fp, #-0x50]
    // 0xa61de8: StoreField: r1->field_1b = r0
    //     0xa61de8: stur            w0, [x1, #0x1b]
    // 0xa61dec: ldur            x0, [fp, #-0x38]
    // 0xa61df0: StoreField: r1->field_1f = r0
    //     0xa61df0: stur            w0, [x1, #0x1f]
    // 0xa61df4: mov             x0, x1
    // 0xa61df8: LeaveFrame
    //     0xa61df8: mov             SP, fp
    //     0xa61dfc: ldp             fp, lr, [SP], #0x10
    // 0xa61e00: ret
    //     0xa61e00: ret             
    // 0xa61e04: r0 = RangeError()
    //     0xa61e04: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61e08: mov             x1, x0
    // 0xa61e0c: r0 = "Not enough bytes available."
    //     0xa61e0c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61e10: ldr             x0, [x0, #0x8a8]
    // 0xa61e14: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61e14: stur            w0, [x1, #0x17]
    // 0xa61e18: r2 = false
    //     0xa61e18: add             x2, NULL, #0x30  ; false
    // 0xa61e1c: StoreField: r1->field_b = r2
    //     0xa61e1c: stur            w2, [x1, #0xb]
    // 0xa61e20: mov             x0, x1
    // 0xa61e24: r0 = Throw()
    //     0xa61e24: bl              #0xec04b8  ; ThrowStub
    // 0xa61e28: brk             #0
    // 0xa61e2c: r0 = "Not enough bytes available."
    //     0xa61e2c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61e30: ldr             x0, [x0, #0x8a8]
    // 0xa61e34: r2 = false
    //     0xa61e34: add             x2, NULL, #0x30  ; false
    // 0xa61e38: r0 = RangeError()
    //     0xa61e38: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa61e3c: mov             x1, x0
    // 0xa61e40: r0 = "Not enough bytes available."
    //     0xa61e40: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa61e44: ldr             x0, [x0, #0x8a8]
    // 0xa61e48: ArrayStore: r1[0] = r0  ; List_4
    //     0xa61e48: stur            w0, [x1, #0x17]
    // 0xa61e4c: r0 = false
    //     0xa61e4c: add             x0, NULL, #0x30  ; false
    // 0xa61e50: StoreField: r1->field_b = r0
    //     0xa61e50: stur            w0, [x1, #0xb]
    // 0xa61e54: mov             x0, x1
    // 0xa61e58: r0 = Throw()
    //     0xa61e58: bl              #0xec04b8  ; ThrowStub
    // 0xa61e5c: brk             #0
    // 0xa61e60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61e60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa61e64: b               #0xa619c8
    // 0xa61e68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61e68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa61e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa61e70: b               #0xa61a50
    // 0xa61e74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61e74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd2c88, size: 0x43c
    // 0xbd2c88: EnterFrame
    //     0xbd2c88: stp             fp, lr, [SP, #-0x10]!
    //     0xbd2c8c: mov             fp, SP
    // 0xbd2c90: AllocStack(0x28)
    //     0xbd2c90: sub             SP, SP, #0x28
    // 0xbd2c94: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd2c94: mov             x4, x2
    //     0xbd2c98: stur            x2, [fp, #-8]
    //     0xbd2c9c: stur            x3, [fp, #-0x10]
    // 0xbd2ca0: CheckStackOverflow
    //     0xbd2ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd2ca4: cmp             SP, x16
    //     0xbd2ca8: b.ls            #0xbd30a0
    // 0xbd2cac: mov             x0, x3
    // 0xbd2cb0: r2 = Null
    //     0xbd2cb0: mov             x2, NULL
    // 0xbd2cb4: r1 = Null
    //     0xbd2cb4: mov             x1, NULL
    // 0xbd2cb8: r4 = 60
    //     0xbd2cb8: movz            x4, #0x3c
    // 0xbd2cbc: branchIfSmi(r0, 0xbd2cc8)
    //     0xbd2cbc: tbz             w0, #0, #0xbd2cc8
    // 0xbd2cc0: r4 = LoadClassIdInstr(r0)
    //     0xbd2cc0: ldur            x4, [x0, #-1]
    //     0xbd2cc4: ubfx            x4, x4, #0xc, #0x14
    // 0xbd2cc8: r17 = 5588
    //     0xbd2cc8: movz            x17, #0x15d4
    // 0xbd2ccc: cmp             x4, x17
    // 0xbd2cd0: b.eq            #0xbd2ce8
    // 0xbd2cd4: r8 = Encyclopedia
    //     0xbd2cd4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b358] Type: Encyclopedia
    //     0xbd2cd8: ldr             x8, [x8, #0x358]
    // 0xbd2cdc: r3 = Null
    //     0xbd2cdc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b360] Null
    //     0xbd2ce0: ldr             x3, [x3, #0x360]
    // 0xbd2ce4: r0 = Encyclopedia()
    //     0xbd2ce4: bl              #0x6fb580  ; IsType_Encyclopedia_Stub
    // 0xbd2ce8: ldur            x0, [fp, #-8]
    // 0xbd2cec: LoadField: r1 = r0->field_b
    //     0xbd2cec: ldur            w1, [x0, #0xb]
    // 0xbd2cf0: DecompressPointer r1
    //     0xbd2cf0: add             x1, x1, HEAP, lsl #32
    // 0xbd2cf4: LoadField: r2 = r1->field_13
    //     0xbd2cf4: ldur            w2, [x1, #0x13]
    // 0xbd2cf8: LoadField: r1 = r0->field_13
    //     0xbd2cf8: ldur            x1, [x0, #0x13]
    // 0xbd2cfc: r3 = LoadInt32Instr(r2)
    //     0xbd2cfc: sbfx            x3, x2, #1, #0x1f
    // 0xbd2d00: sub             x2, x3, x1
    // 0xbd2d04: cmp             x2, #1
    // 0xbd2d08: b.ge            #0xbd2d18
    // 0xbd2d0c: mov             x1, x0
    // 0xbd2d10: r2 = 1
    //     0xbd2d10: movz            x2, #0x1
    // 0xbd2d14: r0 = _increaseBufferSize()
    //     0xbd2d14: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2d18: ldur            x3, [fp, #-8]
    // 0xbd2d1c: r2 = 6
    //     0xbd2d1c: movz            x2, #0x6
    // 0xbd2d20: LoadField: r4 = r3->field_b
    //     0xbd2d20: ldur            w4, [x3, #0xb]
    // 0xbd2d24: DecompressPointer r4
    //     0xbd2d24: add             x4, x4, HEAP, lsl #32
    // 0xbd2d28: LoadField: r5 = r3->field_13
    //     0xbd2d28: ldur            x5, [x3, #0x13]
    // 0xbd2d2c: add             x6, x5, #1
    // 0xbd2d30: StoreField: r3->field_13 = r6
    //     0xbd2d30: stur            x6, [x3, #0x13]
    // 0xbd2d34: LoadField: r0 = r4->field_13
    //     0xbd2d34: ldur            w0, [x4, #0x13]
    // 0xbd2d38: r7 = LoadInt32Instr(r0)
    //     0xbd2d38: sbfx            x7, x0, #1, #0x1f
    // 0xbd2d3c: mov             x0, x7
    // 0xbd2d40: mov             x1, x5
    // 0xbd2d44: cmp             x1, x0
    // 0xbd2d48: b.hs            #0xbd30a8
    // 0xbd2d4c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd2d4c: add             x0, x4, x5
    //     0xbd2d50: strb            w2, [x0, #0x17]
    // 0xbd2d54: sub             x0, x7, x6
    // 0xbd2d58: cmp             x0, #1
    // 0xbd2d5c: b.ge            #0xbd2d6c
    // 0xbd2d60: mov             x1, x3
    // 0xbd2d64: r2 = 1
    //     0xbd2d64: movz            x2, #0x1
    // 0xbd2d68: r0 = _increaseBufferSize()
    //     0xbd2d68: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2d6c: ldur            x2, [fp, #-8]
    // 0xbd2d70: ldur            x3, [fp, #-0x10]
    // 0xbd2d74: LoadField: r4 = r2->field_b
    //     0xbd2d74: ldur            w4, [x2, #0xb]
    // 0xbd2d78: DecompressPointer r4
    //     0xbd2d78: add             x4, x4, HEAP, lsl #32
    // 0xbd2d7c: LoadField: r5 = r2->field_13
    //     0xbd2d7c: ldur            x5, [x2, #0x13]
    // 0xbd2d80: add             x0, x5, #1
    // 0xbd2d84: StoreField: r2->field_13 = r0
    //     0xbd2d84: stur            x0, [x2, #0x13]
    // 0xbd2d88: LoadField: r0 = r4->field_13
    //     0xbd2d88: ldur            w0, [x4, #0x13]
    // 0xbd2d8c: r1 = LoadInt32Instr(r0)
    //     0xbd2d8c: sbfx            x1, x0, #1, #0x1f
    // 0xbd2d90: mov             x0, x1
    // 0xbd2d94: mov             x1, x5
    // 0xbd2d98: cmp             x1, x0
    // 0xbd2d9c: b.hs            #0xbd30ac
    // 0xbd2da0: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd2da0: add             x0, x4, x5
    //     0xbd2da4: strb            wzr, [x0, #0x17]
    // 0xbd2da8: LoadField: r4 = r3->field_7
    //     0xbd2da8: ldur            x4, [x3, #7]
    // 0xbd2dac: r0 = BoxInt64Instr(r4)
    //     0xbd2dac: sbfiz           x0, x4, #1, #0x1f
    //     0xbd2db0: cmp             x4, x0, asr #1
    //     0xbd2db4: b.eq            #0xbd2dc0
    //     0xbd2db8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd2dbc: stur            x4, [x0, #7]
    // 0xbd2dc0: r16 = <int>
    //     0xbd2dc0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd2dc4: stp             x2, x16, [SP, #8]
    // 0xbd2dc8: str             x0, [SP]
    // 0xbd2dcc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2dcc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2dd0: r0 = write()
    //     0xbd2dd0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2dd4: ldur            x0, [fp, #-8]
    // 0xbd2dd8: LoadField: r1 = r0->field_b
    //     0xbd2dd8: ldur            w1, [x0, #0xb]
    // 0xbd2ddc: DecompressPointer r1
    //     0xbd2ddc: add             x1, x1, HEAP, lsl #32
    // 0xbd2de0: LoadField: r2 = r1->field_13
    //     0xbd2de0: ldur            w2, [x1, #0x13]
    // 0xbd2de4: LoadField: r1 = r0->field_13
    //     0xbd2de4: ldur            x1, [x0, #0x13]
    // 0xbd2de8: r3 = LoadInt32Instr(r2)
    //     0xbd2de8: sbfx            x3, x2, #1, #0x1f
    // 0xbd2dec: sub             x2, x3, x1
    // 0xbd2df0: cmp             x2, #1
    // 0xbd2df4: b.ge            #0xbd2e04
    // 0xbd2df8: mov             x1, x0
    // 0xbd2dfc: r2 = 1
    //     0xbd2dfc: movz            x2, #0x1
    // 0xbd2e00: r0 = _increaseBufferSize()
    //     0xbd2e00: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2e04: ldur            x2, [fp, #-8]
    // 0xbd2e08: ldur            x3, [fp, #-0x10]
    // 0xbd2e0c: r4 = 1
    //     0xbd2e0c: movz            x4, #0x1
    // 0xbd2e10: LoadField: r5 = r2->field_b
    //     0xbd2e10: ldur            w5, [x2, #0xb]
    // 0xbd2e14: DecompressPointer r5
    //     0xbd2e14: add             x5, x5, HEAP, lsl #32
    // 0xbd2e18: LoadField: r6 = r2->field_13
    //     0xbd2e18: ldur            x6, [x2, #0x13]
    // 0xbd2e1c: add             x0, x6, #1
    // 0xbd2e20: StoreField: r2->field_13 = r0
    //     0xbd2e20: stur            x0, [x2, #0x13]
    // 0xbd2e24: LoadField: r0 = r5->field_13
    //     0xbd2e24: ldur            w0, [x5, #0x13]
    // 0xbd2e28: r1 = LoadInt32Instr(r0)
    //     0xbd2e28: sbfx            x1, x0, #1, #0x1f
    // 0xbd2e2c: mov             x0, x1
    // 0xbd2e30: mov             x1, x6
    // 0xbd2e34: cmp             x1, x0
    // 0xbd2e38: b.hs            #0xbd30b0
    // 0xbd2e3c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2e3c: add             x0, x5, x6
    //     0xbd2e40: strb            w4, [x0, #0x17]
    // 0xbd2e44: LoadField: r0 = r3->field_f
    //     0xbd2e44: ldur            w0, [x3, #0xf]
    // 0xbd2e48: DecompressPointer r0
    //     0xbd2e48: add             x0, x0, HEAP, lsl #32
    // 0xbd2e4c: r16 = <String>
    //     0xbd2e4c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd2e50: stp             x2, x16, [SP, #8]
    // 0xbd2e54: str             x0, [SP]
    // 0xbd2e58: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2e58: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2e5c: r0 = write()
    //     0xbd2e5c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2e60: ldur            x0, [fp, #-8]
    // 0xbd2e64: LoadField: r1 = r0->field_b
    //     0xbd2e64: ldur            w1, [x0, #0xb]
    // 0xbd2e68: DecompressPointer r1
    //     0xbd2e68: add             x1, x1, HEAP, lsl #32
    // 0xbd2e6c: LoadField: r2 = r1->field_13
    //     0xbd2e6c: ldur            w2, [x1, #0x13]
    // 0xbd2e70: LoadField: r1 = r0->field_13
    //     0xbd2e70: ldur            x1, [x0, #0x13]
    // 0xbd2e74: r3 = LoadInt32Instr(r2)
    //     0xbd2e74: sbfx            x3, x2, #1, #0x1f
    // 0xbd2e78: sub             x2, x3, x1
    // 0xbd2e7c: cmp             x2, #1
    // 0xbd2e80: b.ge            #0xbd2e90
    // 0xbd2e84: mov             x1, x0
    // 0xbd2e88: r2 = 1
    //     0xbd2e88: movz            x2, #0x1
    // 0xbd2e8c: r0 = _increaseBufferSize()
    //     0xbd2e8c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2e90: ldur            x2, [fp, #-8]
    // 0xbd2e94: ldur            x3, [fp, #-0x10]
    // 0xbd2e98: r4 = 2
    //     0xbd2e98: movz            x4, #0x2
    // 0xbd2e9c: LoadField: r5 = r2->field_b
    //     0xbd2e9c: ldur            w5, [x2, #0xb]
    // 0xbd2ea0: DecompressPointer r5
    //     0xbd2ea0: add             x5, x5, HEAP, lsl #32
    // 0xbd2ea4: LoadField: r6 = r2->field_13
    //     0xbd2ea4: ldur            x6, [x2, #0x13]
    // 0xbd2ea8: add             x0, x6, #1
    // 0xbd2eac: StoreField: r2->field_13 = r0
    //     0xbd2eac: stur            x0, [x2, #0x13]
    // 0xbd2eb0: LoadField: r0 = r5->field_13
    //     0xbd2eb0: ldur            w0, [x5, #0x13]
    // 0xbd2eb4: r1 = LoadInt32Instr(r0)
    //     0xbd2eb4: sbfx            x1, x0, #1, #0x1f
    // 0xbd2eb8: mov             x0, x1
    // 0xbd2ebc: mov             x1, x6
    // 0xbd2ec0: cmp             x1, x0
    // 0xbd2ec4: b.hs            #0xbd30b4
    // 0xbd2ec8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2ec8: add             x0, x5, x6
    //     0xbd2ecc: strb            w4, [x0, #0x17]
    // 0xbd2ed0: LoadField: r0 = r3->field_13
    //     0xbd2ed0: ldur            w0, [x3, #0x13]
    // 0xbd2ed4: DecompressPointer r0
    //     0xbd2ed4: add             x0, x0, HEAP, lsl #32
    // 0xbd2ed8: r16 = <EncyclopediaCategory>
    //     0xbd2ed8: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xbd2edc: stp             x2, x16, [SP, #8]
    // 0xbd2ee0: str             x0, [SP]
    // 0xbd2ee4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2ee4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2ee8: r0 = write()
    //     0xbd2ee8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2eec: ldur            x0, [fp, #-8]
    // 0xbd2ef0: LoadField: r1 = r0->field_b
    //     0xbd2ef0: ldur            w1, [x0, #0xb]
    // 0xbd2ef4: DecompressPointer r1
    //     0xbd2ef4: add             x1, x1, HEAP, lsl #32
    // 0xbd2ef8: LoadField: r2 = r1->field_13
    //     0xbd2ef8: ldur            w2, [x1, #0x13]
    // 0xbd2efc: LoadField: r1 = r0->field_13
    //     0xbd2efc: ldur            x1, [x0, #0x13]
    // 0xbd2f00: r3 = LoadInt32Instr(r2)
    //     0xbd2f00: sbfx            x3, x2, #1, #0x1f
    // 0xbd2f04: sub             x2, x3, x1
    // 0xbd2f08: cmp             x2, #1
    // 0xbd2f0c: b.ge            #0xbd2f1c
    // 0xbd2f10: mov             x1, x0
    // 0xbd2f14: r2 = 1
    //     0xbd2f14: movz            x2, #0x1
    // 0xbd2f18: r0 = _increaseBufferSize()
    //     0xbd2f18: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2f1c: ldur            x2, [fp, #-8]
    // 0xbd2f20: ldur            x3, [fp, #-0x10]
    // 0xbd2f24: r4 = 3
    //     0xbd2f24: movz            x4, #0x3
    // 0xbd2f28: LoadField: r5 = r2->field_b
    //     0xbd2f28: ldur            w5, [x2, #0xb]
    // 0xbd2f2c: DecompressPointer r5
    //     0xbd2f2c: add             x5, x5, HEAP, lsl #32
    // 0xbd2f30: LoadField: r6 = r2->field_13
    //     0xbd2f30: ldur            x6, [x2, #0x13]
    // 0xbd2f34: add             x0, x6, #1
    // 0xbd2f38: StoreField: r2->field_13 = r0
    //     0xbd2f38: stur            x0, [x2, #0x13]
    // 0xbd2f3c: LoadField: r0 = r5->field_13
    //     0xbd2f3c: ldur            w0, [x5, #0x13]
    // 0xbd2f40: r1 = LoadInt32Instr(r0)
    //     0xbd2f40: sbfx            x1, x0, #1, #0x1f
    // 0xbd2f44: mov             x0, x1
    // 0xbd2f48: mov             x1, x6
    // 0xbd2f4c: cmp             x1, x0
    // 0xbd2f50: b.hs            #0xbd30b8
    // 0xbd2f54: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2f54: add             x0, x5, x6
    //     0xbd2f58: strb            w4, [x0, #0x17]
    // 0xbd2f5c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd2f5c: ldur            w0, [x3, #0x17]
    // 0xbd2f60: DecompressPointer r0
    //     0xbd2f60: add             x0, x0, HEAP, lsl #32
    // 0xbd2f64: r16 = <Image>
    //     0xbd2f64: ldr             x16, [PP, #0x7b60]  ; [pp+0x7b60] TypeArguments: <Image>
    // 0xbd2f68: stp             x2, x16, [SP, #8]
    // 0xbd2f6c: str             x0, [SP]
    // 0xbd2f70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2f70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd2f74: r0 = write()
    //     0xbd2f74: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd2f78: ldur            x0, [fp, #-8]
    // 0xbd2f7c: LoadField: r1 = r0->field_b
    //     0xbd2f7c: ldur            w1, [x0, #0xb]
    // 0xbd2f80: DecompressPointer r1
    //     0xbd2f80: add             x1, x1, HEAP, lsl #32
    // 0xbd2f84: LoadField: r2 = r1->field_13
    //     0xbd2f84: ldur            w2, [x1, #0x13]
    // 0xbd2f88: LoadField: r1 = r0->field_13
    //     0xbd2f88: ldur            x1, [x0, #0x13]
    // 0xbd2f8c: r3 = LoadInt32Instr(r2)
    //     0xbd2f8c: sbfx            x3, x2, #1, #0x1f
    // 0xbd2f90: sub             x2, x3, x1
    // 0xbd2f94: cmp             x2, #1
    // 0xbd2f98: b.ge            #0xbd2fa8
    // 0xbd2f9c: mov             x1, x0
    // 0xbd2fa0: r2 = 1
    //     0xbd2fa0: movz            x2, #0x1
    // 0xbd2fa4: r0 = _increaseBufferSize()
    //     0xbd2fa4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd2fa8: ldur            x2, [fp, #-8]
    // 0xbd2fac: ldur            x3, [fp, #-0x10]
    // 0xbd2fb0: r4 = 4
    //     0xbd2fb0: movz            x4, #0x4
    // 0xbd2fb4: LoadField: r5 = r2->field_b
    //     0xbd2fb4: ldur            w5, [x2, #0xb]
    // 0xbd2fb8: DecompressPointer r5
    //     0xbd2fb8: add             x5, x5, HEAP, lsl #32
    // 0xbd2fbc: LoadField: r6 = r2->field_13
    //     0xbd2fbc: ldur            x6, [x2, #0x13]
    // 0xbd2fc0: add             x0, x6, #1
    // 0xbd2fc4: StoreField: r2->field_13 = r0
    //     0xbd2fc4: stur            x0, [x2, #0x13]
    // 0xbd2fc8: LoadField: r0 = r5->field_13
    //     0xbd2fc8: ldur            w0, [x5, #0x13]
    // 0xbd2fcc: r1 = LoadInt32Instr(r0)
    //     0xbd2fcc: sbfx            x1, x0, #1, #0x1f
    // 0xbd2fd0: mov             x0, x1
    // 0xbd2fd4: mov             x1, x6
    // 0xbd2fd8: cmp             x1, x0
    // 0xbd2fdc: b.hs            #0xbd30bc
    // 0xbd2fe0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd2fe0: add             x0, x5, x6
    //     0xbd2fe4: strb            w4, [x0, #0x17]
    // 0xbd2fe8: LoadField: r0 = r3->field_1b
    //     0xbd2fe8: ldur            w0, [x3, #0x1b]
    // 0xbd2fec: DecompressPointer r0
    //     0xbd2fec: add             x0, x0, HEAP, lsl #32
    // 0xbd2ff0: r16 = <String>
    //     0xbd2ff0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd2ff4: stp             x2, x16, [SP, #8]
    // 0xbd2ff8: str             x0, [SP]
    // 0xbd2ffc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd2ffc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3000: r0 = write()
    //     0xbd3000: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3004: ldur            x0, [fp, #-8]
    // 0xbd3008: LoadField: r1 = r0->field_b
    //     0xbd3008: ldur            w1, [x0, #0xb]
    // 0xbd300c: DecompressPointer r1
    //     0xbd300c: add             x1, x1, HEAP, lsl #32
    // 0xbd3010: LoadField: r2 = r1->field_13
    //     0xbd3010: ldur            w2, [x1, #0x13]
    // 0xbd3014: LoadField: r1 = r0->field_13
    //     0xbd3014: ldur            x1, [x0, #0x13]
    // 0xbd3018: r3 = LoadInt32Instr(r2)
    //     0xbd3018: sbfx            x3, x2, #1, #0x1f
    // 0xbd301c: sub             x2, x3, x1
    // 0xbd3020: cmp             x2, #1
    // 0xbd3024: b.ge            #0xbd3034
    // 0xbd3028: mov             x1, x0
    // 0xbd302c: r2 = 1
    //     0xbd302c: movz            x2, #0x1
    // 0xbd3030: r0 = _increaseBufferSize()
    //     0xbd3030: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3034: ldur            x2, [fp, #-8]
    // 0xbd3038: ldur            x3, [fp, #-0x10]
    // 0xbd303c: r4 = 5
    //     0xbd303c: movz            x4, #0x5
    // 0xbd3040: LoadField: r5 = r2->field_b
    //     0xbd3040: ldur            w5, [x2, #0xb]
    // 0xbd3044: DecompressPointer r5
    //     0xbd3044: add             x5, x5, HEAP, lsl #32
    // 0xbd3048: LoadField: r6 = r2->field_13
    //     0xbd3048: ldur            x6, [x2, #0x13]
    // 0xbd304c: add             x0, x6, #1
    // 0xbd3050: StoreField: r2->field_13 = r0
    //     0xbd3050: stur            x0, [x2, #0x13]
    // 0xbd3054: LoadField: r0 = r5->field_13
    //     0xbd3054: ldur            w0, [x5, #0x13]
    // 0xbd3058: r1 = LoadInt32Instr(r0)
    //     0xbd3058: sbfx            x1, x0, #1, #0x1f
    // 0xbd305c: mov             x0, x1
    // 0xbd3060: mov             x1, x6
    // 0xbd3064: cmp             x1, x0
    // 0xbd3068: b.hs            #0xbd30c0
    // 0xbd306c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd306c: add             x0, x5, x6
    //     0xbd3070: strb            w4, [x0, #0x17]
    // 0xbd3074: LoadField: r0 = r3->field_1f
    //     0xbd3074: ldur            w0, [x3, #0x1f]
    // 0xbd3078: DecompressPointer r0
    //     0xbd3078: add             x0, x0, HEAP, lsl #32
    // 0xbd307c: r16 = <String>
    //     0xbd307c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3080: stp             x2, x16, [SP, #8]
    // 0xbd3084: str             x0, [SP]
    // 0xbd3088: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3088: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd308c: r0 = write()
    //     0xbd308c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3090: r0 = Null
    //     0xbd3090: mov             x0, NULL
    // 0xbd3094: LeaveFrame
    //     0xbd3094: mov             SP, fp
    //     0xbd3098: ldp             fp, lr, [SP], #0x10
    // 0xbd309c: ret
    //     0xbd309c: ret             
    // 0xbd30a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd30a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd30a4: b               #0xbd2cac
    // 0xbd30a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd30c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd30c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0168, size: 0x24
    // 0xbf0168: r1 = 100
    //     0xbf0168: movz            x1, #0x64
    // 0xbf016c: r16 = LoadInt32Instr(r1)
    //     0xbf016c: sbfx            x16, x1, #1, #0x1f
    // 0xbf0170: r17 = 11601
    //     0xbf0170: movz            x17, #0x2d51
    // 0xbf0174: mul             x0, x16, x17
    // 0xbf0178: umulh           x16, x16, x17
    // 0xbf017c: eor             x0, x0, x16
    // 0xbf0180: r0 = 0
    //     0xbf0180: eor             x0, x0, x0, lsr #32
    // 0xbf0184: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0188: ret
    //     0xbf0188: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7668c, size: 0x9c
    // 0xd7668c: EnterFrame
    //     0xd7668c: stp             fp, lr, [SP, #-0x10]!
    //     0xd76690: mov             fp, SP
    // 0xd76694: AllocStack(0x10)
    //     0xd76694: sub             SP, SP, #0x10
    // 0xd76698: CheckStackOverflow
    //     0xd76698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7669c: cmp             SP, x16
    //     0xd766a0: b.ls            #0xd76720
    // 0xd766a4: ldr             x0, [fp, #0x10]
    // 0xd766a8: cmp             w0, NULL
    // 0xd766ac: b.ne            #0xd766c0
    // 0xd766b0: r0 = false
    //     0xd766b0: add             x0, NULL, #0x30  ; false
    // 0xd766b4: LeaveFrame
    //     0xd766b4: mov             SP, fp
    //     0xd766b8: ldp             fp, lr, [SP], #0x10
    // 0xd766bc: ret
    //     0xd766bc: ret             
    // 0xd766c0: ldr             x1, [fp, #0x18]
    // 0xd766c4: cmp             w1, w0
    // 0xd766c8: b.ne            #0xd766d4
    // 0xd766cc: r0 = true
    //     0xd766cc: add             x0, NULL, #0x20  ; true
    // 0xd766d0: b               #0xd76714
    // 0xd766d4: r1 = 60
    //     0xd766d4: movz            x1, #0x3c
    // 0xd766d8: branchIfSmi(r0, 0xd766e4)
    //     0xd766d8: tbz             w0, #0, #0xd766e4
    // 0xd766dc: r1 = LoadClassIdInstr(r0)
    //     0xd766dc: ldur            x1, [x0, #-1]
    //     0xd766e0: ubfx            x1, x1, #0xc, #0x14
    // 0xd766e4: cmp             x1, #0x67c
    // 0xd766e8: b.ne            #0xd76710
    // 0xd766ec: r16 = EncyclopediaAdapter
    //     0xd766ec: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b350] Type: EncyclopediaAdapter
    //     0xd766f0: ldr             x16, [x16, #0x350]
    // 0xd766f4: r30 = EncyclopediaAdapter
    //     0xd766f4: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b350] Type: EncyclopediaAdapter
    //     0xd766f8: ldr             lr, [lr, #0x350]
    // 0xd766fc: stp             lr, x16, [SP]
    // 0xd76700: r0 = ==()
    //     0xd76700: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76704: tbnz            w0, #4, #0xd76710
    // 0xd76708: r0 = true
    //     0xd76708: add             x0, NULL, #0x20  ; true
    // 0xd7670c: b               #0xd76714
    // 0xd76710: r0 = false
    //     0xd76710: add             x0, NULL, #0x30  ; false
    // 0xd76714: LeaveFrame
    //     0xd76714: mov             SP, fp
    //     0xd76718: ldp             fp, lr, [SP], #0x10
    // 0xd7671c: ret
    //     0xd7671c: ret             
    // 0xd76720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76724: b               #0xd766a4
  }
}

// class id: 5587, size: 0x20, field offset: 0x8
//   const constructor, 
class EncyclopediaCategory extends Equatable {

  factory _ EncyclopediaCategory.fromMap(/* No info */) {
    // ** addr: 0xdc92cc, size: 0x208
    // 0xdc92cc: EnterFrame
    //     0xdc92cc: stp             fp, lr, [SP, #-0x10]!
    //     0xdc92d0: mov             fp, SP
    // 0xdc92d4: AllocStack(0x30)
    //     0xdc92d4: sub             SP, SP, #0x30
    // 0xdc92d8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xdc92d8: mov             x3, x2
    //     0xdc92dc: stur            x2, [fp, #-8]
    // 0xdc92e0: CheckStackOverflow
    //     0xdc92e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc92e4: cmp             SP, x16
    //     0xdc92e8: b.ls            #0xdc94cc
    // 0xdc92ec: r0 = LoadClassIdInstr(r3)
    //     0xdc92ec: ldur            x0, [x3, #-1]
    //     0xdc92f0: ubfx            x0, x0, #0xc, #0x14
    // 0xdc92f4: mov             x1, x3
    // 0xdc92f8: r2 = "id"
    //     0xdc92f8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc92fc: ldr             x2, [x2, #0x740]
    // 0xdc9300: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9300: sub             lr, x0, #0x114
    //     0xdc9304: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9308: blr             lr
    // 0xdc930c: mov             x3, x0
    // 0xdc9310: r2 = Null
    //     0xdc9310: mov             x2, NULL
    // 0xdc9314: r1 = Null
    //     0xdc9314: mov             x1, NULL
    // 0xdc9318: stur            x3, [fp, #-0x10]
    // 0xdc931c: branchIfSmi(r0, 0xdc9344)
    //     0xdc931c: tbz             w0, #0, #0xdc9344
    // 0xdc9320: r4 = LoadClassIdInstr(r0)
    //     0xdc9320: ldur            x4, [x0, #-1]
    //     0xdc9324: ubfx            x4, x4, #0xc, #0x14
    // 0xdc9328: sub             x4, x4, #0x3c
    // 0xdc932c: cmp             x4, #1
    // 0xdc9330: b.ls            #0xdc9344
    // 0xdc9334: r8 = int
    //     0xdc9334: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc9338: r3 = Null
    //     0xdc9338: add             x3, PP, #0x19, lsl #12  ; [pp+0x19fe0] Null
    //     0xdc933c: ldr             x3, [x3, #0xfe0]
    // 0xdc9340: r0 = int()
    //     0xdc9340: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc9344: ldur            x3, [fp, #-8]
    // 0xdc9348: r0 = LoadClassIdInstr(r3)
    //     0xdc9348: ldur            x0, [x3, #-1]
    //     0xdc934c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc9350: mov             x1, x3
    // 0xdc9354: r2 = "name"
    //     0xdc9354: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xdc9358: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9358: sub             lr, x0, #0x114
    //     0xdc935c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9360: blr             lr
    // 0xdc9364: mov             x3, x0
    // 0xdc9368: r2 = Null
    //     0xdc9368: mov             x2, NULL
    // 0xdc936c: r1 = Null
    //     0xdc936c: mov             x1, NULL
    // 0xdc9370: stur            x3, [fp, #-0x18]
    // 0xdc9374: r4 = 60
    //     0xdc9374: movz            x4, #0x3c
    // 0xdc9378: branchIfSmi(r0, 0xdc9384)
    //     0xdc9378: tbz             w0, #0, #0xdc9384
    // 0xdc937c: r4 = LoadClassIdInstr(r0)
    //     0xdc937c: ldur            x4, [x0, #-1]
    //     0xdc9380: ubfx            x4, x4, #0xc, #0x14
    // 0xdc9384: sub             x4, x4, #0x5e
    // 0xdc9388: cmp             x4, #1
    // 0xdc938c: b.ls            #0xdc93a0
    // 0xdc9390: r8 = String
    //     0xdc9390: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc9394: r3 = Null
    //     0xdc9394: add             x3, PP, #0x19, lsl #12  ; [pp+0x19ff0] Null
    //     0xdc9398: ldr             x3, [x3, #0xff0]
    // 0xdc939c: r0 = String()
    //     0xdc939c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc93a0: ldur            x3, [fp, #-8]
    // 0xdc93a4: r0 = LoadClassIdInstr(r3)
    //     0xdc93a4: ldur            x0, [x3, #-1]
    //     0xdc93a8: ubfx            x0, x0, #0xc, #0x14
    // 0xdc93ac: mov             x1, x3
    // 0xdc93b0: r2 = "total"
    //     0xdc93b0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0xdc93b4: ldr             x2, [x2, #0xdc0]
    // 0xdc93b8: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc93b8: sub             lr, x0, #0x114
    //     0xdc93bc: ldr             lr, [x21, lr, lsl #3]
    //     0xdc93c0: blr             lr
    // 0xdc93c4: mov             x3, x0
    // 0xdc93c8: r2 = Null
    //     0xdc93c8: mov             x2, NULL
    // 0xdc93cc: r1 = Null
    //     0xdc93cc: mov             x1, NULL
    // 0xdc93d0: stur            x3, [fp, #-0x20]
    // 0xdc93d4: branchIfSmi(r0, 0xdc93fc)
    //     0xdc93d4: tbz             w0, #0, #0xdc93fc
    // 0xdc93d8: r4 = LoadClassIdInstr(r0)
    //     0xdc93d8: ldur            x4, [x0, #-1]
    //     0xdc93dc: ubfx            x4, x4, #0xc, #0x14
    // 0xdc93e0: sub             x4, x4, #0x3c
    // 0xdc93e4: cmp             x4, #1
    // 0xdc93e8: b.ls            #0xdc93fc
    // 0xdc93ec: r8 = int?
    //     0xdc93ec: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xdc93f0: r3 = Null
    //     0xdc93f0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a000] Null
    //     0xdc93f4: ldr             x3, [x3]
    // 0xdc93f8: r0 = int?()
    //     0xdc93f8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xdc93fc: ldur            x0, [fp, #-0x20]
    // 0xdc9400: cmp             w0, NULL
    // 0xdc9404: b.ne            #0xdc9410
    // 0xdc9408: r5 = 0
    //     0xdc9408: movz            x5, #0
    // 0xdc940c: b               #0xdc9420
    // 0xdc9410: r1 = LoadInt32Instr(r0)
    //     0xdc9410: sbfx            x1, x0, #1, #0x1f
    //     0xdc9414: tbz             w0, #0, #0xdc941c
    //     0xdc9418: ldur            x1, [x0, #7]
    // 0xdc941c: mov             x5, x1
    // 0xdc9420: ldur            x1, [fp, #-8]
    // 0xdc9424: ldur            x4, [fp, #-0x10]
    // 0xdc9428: ldur            x3, [fp, #-0x18]
    // 0xdc942c: stur            x5, [fp, #-0x28]
    // 0xdc9430: r0 = LoadClassIdInstr(r1)
    //     0xdc9430: ldur            x0, [x1, #-1]
    //     0xdc9434: ubfx            x0, x0, #0xc, #0x14
    // 0xdc9438: r2 = "updated_at"
    //     0xdc9438: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0xdc943c: ldr             x2, [x2, #0xe88]
    // 0xdc9440: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9440: sub             lr, x0, #0x114
    //     0xdc9444: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9448: blr             lr
    // 0xdc944c: mov             x3, x0
    // 0xdc9450: r2 = Null
    //     0xdc9450: mov             x2, NULL
    // 0xdc9454: r1 = Null
    //     0xdc9454: mov             x1, NULL
    // 0xdc9458: stur            x3, [fp, #-8]
    // 0xdc945c: r4 = 60
    //     0xdc945c: movz            x4, #0x3c
    // 0xdc9460: branchIfSmi(r0, 0xdc946c)
    //     0xdc9460: tbz             w0, #0, #0xdc946c
    // 0xdc9464: r4 = LoadClassIdInstr(r0)
    //     0xdc9464: ldur            x4, [x0, #-1]
    //     0xdc9468: ubfx            x4, x4, #0xc, #0x14
    // 0xdc946c: sub             x4, x4, #0x5e
    // 0xdc9470: cmp             x4, #1
    // 0xdc9474: b.ls            #0xdc9488
    // 0xdc9478: r8 = String
    //     0xdc9478: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc947c: r3 = Null
    //     0xdc947c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a010] Null
    //     0xdc9480: ldr             x3, [x3, #0x10]
    // 0xdc9484: r0 = String()
    //     0xdc9484: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc9488: ldur            x0, [fp, #-0x10]
    // 0xdc948c: r1 = LoadInt32Instr(r0)
    //     0xdc948c: sbfx            x1, x0, #1, #0x1f
    //     0xdc9490: tbz             w0, #0, #0xdc9498
    //     0xdc9494: ldur            x1, [x0, #7]
    // 0xdc9498: stur            x1, [fp, #-0x30]
    // 0xdc949c: r0 = EncyclopediaCategory()
    //     0xdc949c: bl              #0xa62268  ; AllocateEncyclopediaCategoryStub -> EncyclopediaCategory (size=0x20)
    // 0xdc94a0: ldur            x1, [fp, #-0x30]
    // 0xdc94a4: StoreField: r0->field_7 = r1
    //     0xdc94a4: stur            x1, [x0, #7]
    // 0xdc94a8: ldur            x1, [fp, #-0x18]
    // 0xdc94ac: StoreField: r0->field_f = r1
    //     0xdc94ac: stur            w1, [x0, #0xf]
    // 0xdc94b0: ldur            x1, [fp, #-0x28]
    // 0xdc94b4: StoreField: r0->field_13 = r1
    //     0xdc94b4: stur            x1, [x0, #0x13]
    // 0xdc94b8: ldur            x1, [fp, #-8]
    // 0xdc94bc: StoreField: r0->field_1b = r1
    //     0xdc94bc: stur            w1, [x0, #0x1b]
    // 0xdc94c0: LeaveFrame
    //     0xdc94c0: mov             SP, fp
    //     0xdc94c4: ldp             fp, lr, [SP], #0x10
    // 0xdc94c8: ret
    //     0xdc94c8: ret             
    // 0xdc94cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc94cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc94d0: b               #0xdc92ec
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe79438, size: 0x180
    // 0xe79438: EnterFrame
    //     0xe79438: stp             fp, lr, [SP, #-0x10]!
    //     0xe7943c: mov             fp, SP
    // 0xe79440: AllocStack(0x20)
    //     0xe79440: sub             SP, SP, #0x20
    // 0xe79444: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe79444: mov             x3, x1
    //     0xe79448: stur            x1, [fp, #-8]
    // 0xe7944c: CheckStackOverflow
    //     0xe7944c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79450: cmp             SP, x16
    //     0xe79454: b.ls            #0xe795b0
    // 0xe79458: mov             x0, x3
    // 0xe7945c: r2 = Null
    //     0xe7945c: mov             x2, NULL
    // 0xe79460: r1 = Null
    //     0xe79460: mov             x1, NULL
    // 0xe79464: cmp             w0, NULL
    // 0xe79468: b.eq            #0xe7950c
    // 0xe7946c: branchIfSmi(r0, 0xe7950c)
    //     0xe7946c: tbz             w0, #0, #0xe7950c
    // 0xe79470: r3 = LoadClassIdInstr(r0)
    //     0xe79470: ldur            x3, [x0, #-1]
    //     0xe79474: ubfx            x3, x3, #0xc, #0x14
    // 0xe79478: r17 = 6718
    //     0xe79478: movz            x17, #0x1a3e
    // 0xe7947c: cmp             x3, x17
    // 0xe79480: b.eq            #0xe79514
    // 0xe79484: sub             x3, x3, #0x5a
    // 0xe79488: cmp             x3, #2
    // 0xe7948c: b.ls            #0xe79514
    // 0xe79490: r4 = LoadClassIdInstr(r0)
    //     0xe79490: ldur            x4, [x0, #-1]
    //     0xe79494: ubfx            x4, x4, #0xc, #0x14
    // 0xe79498: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe7949c: ldr             x3, [x3, #0x18]
    // 0xe794a0: ldr             x3, [x3, x4, lsl #3]
    // 0xe794a4: LoadField: r3 = r3->field_2b
    //     0xe794a4: ldur            w3, [x3, #0x2b]
    // 0xe794a8: DecompressPointer r3
    //     0xe794a8: add             x3, x3, HEAP, lsl #32
    // 0xe794ac: cmp             w3, NULL
    // 0xe794b0: b.eq            #0xe7950c
    // 0xe794b4: LoadField: r3 = r3->field_f
    //     0xe794b4: ldur            w3, [x3, #0xf]
    // 0xe794b8: lsr             x3, x3, #3
    // 0xe794bc: r17 = 6718
    //     0xe794bc: movz            x17, #0x1a3e
    // 0xe794c0: cmp             x3, x17
    // 0xe794c4: b.eq            #0xe79514
    // 0xe794c8: r3 = SubtypeTestCache
    //     0xe794c8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51608] SubtypeTestCache
    //     0xe794cc: ldr             x3, [x3, #0x608]
    // 0xe794d0: r30 = Subtype1TestCacheStub
    //     0xe794d0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe794d4: LoadField: r30 = r30->field_7
    //     0xe794d4: ldur            lr, [lr, #7]
    // 0xe794d8: blr             lr
    // 0xe794dc: cmp             w7, NULL
    // 0xe794e0: b.eq            #0xe794ec
    // 0xe794e4: tbnz            w7, #4, #0xe7950c
    // 0xe794e8: b               #0xe79514
    // 0xe794ec: r8 = List
    //     0xe794ec: add             x8, PP, #0x51, lsl #12  ; [pp+0x51610] Type: List
    //     0xe794f0: ldr             x8, [x8, #0x610]
    // 0xe794f4: r3 = SubtypeTestCache
    //     0xe794f4: add             x3, PP, #0x51, lsl #12  ; [pp+0x51618] SubtypeTestCache
    //     0xe794f8: ldr             x3, [x3, #0x618]
    // 0xe794fc: r30 = InstanceOfStub
    //     0xe794fc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe79500: LoadField: r30 = r30->field_7
    //     0xe79500: ldur            lr, [lr, #7]
    // 0xe79504: blr             lr
    // 0xe79508: b               #0xe79518
    // 0xe7950c: r0 = false
    //     0xe7950c: add             x0, NULL, #0x30  ; false
    // 0xe79510: b               #0xe79518
    // 0xe79514: r0 = true
    //     0xe79514: add             x0, NULL, #0x20  ; true
    // 0xe79518: tbnz            w0, #4, #0xe79598
    // 0xe7951c: ldur            x0, [fp, #-8]
    // 0xe79520: r1 = Function '<anonymous closure>': static.
    //     0xe79520: add             x1, PP, #0x51, lsl #12  ; [pp+0x51620] AnonymousClosure: static (0xe795b8), in [package:nuonline/app/data/models/encyclopedia.dart] EncyclopediaCategory::fromResponse (0xe79438)
    //     0xe79524: ldr             x1, [x1, #0x620]
    // 0xe79528: r2 = Null
    //     0xe79528: mov             x2, NULL
    // 0xe7952c: r0 = AllocateClosure()
    //     0xe7952c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79530: mov             x1, x0
    // 0xe79534: ldur            x0, [fp, #-8]
    // 0xe79538: r2 = LoadClassIdInstr(r0)
    //     0xe79538: ldur            x2, [x0, #-1]
    //     0xe7953c: ubfx            x2, x2, #0xc, #0x14
    // 0xe79540: r16 = <EncyclopediaCategory>
    //     0xe79540: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe79544: stp             x0, x16, [SP, #8]
    // 0xe79548: str             x1, [SP]
    // 0xe7954c: mov             x0, x2
    // 0xe79550: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79550: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79554: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe79554: movz            x17, #0xf28c
    //     0xe79558: add             lr, x0, x17
    //     0xe7955c: ldr             lr, [x21, lr, lsl #3]
    //     0xe79560: blr             lr
    // 0xe79564: r1 = LoadClassIdInstr(r0)
    //     0xe79564: ldur            x1, [x0, #-1]
    //     0xe79568: ubfx            x1, x1, #0xc, #0x14
    // 0xe7956c: mov             x16, x0
    // 0xe79570: mov             x0, x1
    // 0xe79574: mov             x1, x16
    // 0xe79578: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe79578: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7957c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7957c: movz            x17, #0xd889
    //     0xe79580: add             lr, x0, x17
    //     0xe79584: ldr             lr, [x21, lr, lsl #3]
    //     0xe79588: blr             lr
    // 0xe7958c: LeaveFrame
    //     0xe7958c: mov             SP, fp
    //     0xe79590: ldp             fp, lr, [SP], #0x10
    // 0xe79594: ret
    //     0xe79594: ret             
    // 0xe79598: r1 = <EncyclopediaCategory>
    //     0xe79598: ldr             x1, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe7959c: r2 = 0
    //     0xe7959c: movz            x2, #0
    // 0xe795a0: r0 = _GrowableList()
    //     0xe795a0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe795a4: LeaveFrame
    //     0xe795a4: mov             SP, fp
    //     0xe795a8: ldp             fp, lr, [SP], #0x10
    // 0xe795ac: ret
    //     0xe795ac: ret             
    // 0xe795b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe795b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe795b4: b               #0xe79458
  }
  [closure] static EncyclopediaCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe795b8, size: 0x50
    // 0xe795b8: EnterFrame
    //     0xe795b8: stp             fp, lr, [SP, #-0x10]!
    //     0xe795bc: mov             fp, SP
    // 0xe795c0: CheckStackOverflow
    //     0xe795c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe795c4: cmp             SP, x16
    //     0xe795c8: b.ls            #0xe79600
    // 0xe795cc: ldr             x0, [fp, #0x10]
    // 0xe795d0: r2 = Null
    //     0xe795d0: mov             x2, NULL
    // 0xe795d4: r1 = Null
    //     0xe795d4: mov             x1, NULL
    // 0xe795d8: r8 = Map<String, dynamic>
    //     0xe795d8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe795dc: r3 = Null
    //     0xe795dc: add             x3, PP, #0x51, lsl #12  ; [pp+0x51628] Null
    //     0xe795e0: ldr             x3, [x3, #0x628]
    // 0xe795e4: r0 = Map<String, dynamic>()
    //     0xe795e4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe795e8: ldr             x2, [fp, #0x10]
    // 0xe795ec: r1 = Null
    //     0xe795ec: mov             x1, NULL
    // 0xe795f0: r0 = EncyclopediaCategory.fromMap()
    //     0xe795f0: bl              #0xdc92cc  ; [package:nuonline/app/data/models/encyclopedia.dart] EncyclopediaCategory::EncyclopediaCategory.fromMap
    // 0xe795f4: LeaveFrame
    //     0xe795f4: mov             SP, fp
    //     0xe795f8: ldp             fp, lr, [SP], #0x10
    // 0xe795fc: ret
    //     0xe795fc: ret             
    // 0xe79600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79604: b               #0xe795cc
  }
}

// class id: 5588, size: 0x24, field offset: 0x8
//   const constructor, 
class Encyclopedia extends Equatable {

  factory _ Encyclopedia.fromMap(/* No info */) {
    // ** addr: 0xdc8fe8, size: 0x2e4
    // 0xdc8fe8: EnterFrame
    //     0xdc8fe8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc8fec: mov             fp, SP
    // 0xdc8ff0: AllocStack(0x38)
    //     0xdc8ff0: sub             SP, SP, #0x38
    // 0xdc8ff4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xdc8ff4: mov             x3, x2
    //     0xdc8ff8: stur            x2, [fp, #-8]
    // 0xdc8ffc: CheckStackOverflow
    //     0xdc8ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc9000: cmp             SP, x16
    //     0xdc9004: b.ls            #0xdc92c4
    // 0xdc9008: r0 = LoadClassIdInstr(r3)
    //     0xdc9008: ldur            x0, [x3, #-1]
    //     0xdc900c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc9010: mov             x1, x3
    // 0xdc9014: r2 = "id"
    //     0xdc9014: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc9018: ldr             x2, [x2, #0x740]
    // 0xdc901c: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc901c: sub             lr, x0, #0x114
    //     0xdc9020: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9024: blr             lr
    // 0xdc9028: mov             x3, x0
    // 0xdc902c: r2 = Null
    //     0xdc902c: mov             x2, NULL
    // 0xdc9030: r1 = Null
    //     0xdc9030: mov             x1, NULL
    // 0xdc9034: stur            x3, [fp, #-0x10]
    // 0xdc9038: branchIfSmi(r0, 0xdc9060)
    //     0xdc9038: tbz             w0, #0, #0xdc9060
    // 0xdc903c: r4 = LoadClassIdInstr(r0)
    //     0xdc903c: ldur            x4, [x0, #-1]
    //     0xdc9040: ubfx            x4, x4, #0xc, #0x14
    // 0xdc9044: sub             x4, x4, #0x3c
    // 0xdc9048: cmp             x4, #1
    // 0xdc904c: b.ls            #0xdc9060
    // 0xdc9050: r8 = int
    //     0xdc9050: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xdc9054: r3 = Null
    //     0xdc9054: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f10] Null
    //     0xdc9058: ldr             x3, [x3, #0xf10]
    // 0xdc905c: r0 = int()
    //     0xdc905c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xdc9060: ldur            x3, [fp, #-8]
    // 0xdc9064: r0 = LoadClassIdInstr(r3)
    //     0xdc9064: ldur            x0, [x3, #-1]
    //     0xdc9068: ubfx            x0, x0, #0xc, #0x14
    // 0xdc906c: mov             x1, x3
    // 0xdc9070: r2 = "title"
    //     0xdc9070: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xdc9074: ldr             x2, [x2, #0x748]
    // 0xdc9078: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9078: sub             lr, x0, #0x114
    //     0xdc907c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9080: blr             lr
    // 0xdc9084: mov             x3, x0
    // 0xdc9088: r2 = Null
    //     0xdc9088: mov             x2, NULL
    // 0xdc908c: r1 = Null
    //     0xdc908c: mov             x1, NULL
    // 0xdc9090: stur            x3, [fp, #-0x18]
    // 0xdc9094: r4 = 60
    //     0xdc9094: movz            x4, #0x3c
    // 0xdc9098: branchIfSmi(r0, 0xdc90a4)
    //     0xdc9098: tbz             w0, #0, #0xdc90a4
    // 0xdc909c: r4 = LoadClassIdInstr(r0)
    //     0xdc909c: ldur            x4, [x0, #-1]
    //     0xdc90a0: ubfx            x4, x4, #0xc, #0x14
    // 0xdc90a4: sub             x4, x4, #0x5e
    // 0xdc90a8: cmp             x4, #1
    // 0xdc90ac: b.ls            #0xdc90c0
    // 0xdc90b0: r8 = String
    //     0xdc90b0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc90b4: r3 = Null
    //     0xdc90b4: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f20] Null
    //     0xdc90b8: ldr             x3, [x3, #0xf20]
    // 0xdc90bc: r0 = String()
    //     0xdc90bc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc90c0: ldur            x3, [fp, #-8]
    // 0xdc90c4: r0 = LoadClassIdInstr(r3)
    //     0xdc90c4: ldur            x0, [x3, #-1]
    //     0xdc90c8: ubfx            x0, x0, #0xc, #0x14
    // 0xdc90cc: mov             x1, x3
    // 0xdc90d0: r2 = "category"
    //     0xdc90d0: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xdc90d4: ldr             x2, [x2, #0x960]
    // 0xdc90d8: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc90d8: sub             lr, x0, #0x114
    //     0xdc90dc: ldr             lr, [x21, lr, lsl #3]
    //     0xdc90e0: blr             lr
    // 0xdc90e4: mov             x3, x0
    // 0xdc90e8: r2 = Null
    //     0xdc90e8: mov             x2, NULL
    // 0xdc90ec: r1 = Null
    //     0xdc90ec: mov             x1, NULL
    // 0xdc90f0: stur            x3, [fp, #-0x20]
    // 0xdc90f4: r8 = Map<String, dynamic>
    //     0xdc90f4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xdc90f8: r3 = Null
    //     0xdc90f8: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f30] Null
    //     0xdc90fc: ldr             x3, [x3, #0xf30]
    // 0xdc9100: r0 = Map<String, dynamic>()
    //     0xdc9100: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xdc9104: ldur            x2, [fp, #-0x20]
    // 0xdc9108: r1 = Null
    //     0xdc9108: mov             x1, NULL
    // 0xdc910c: r0 = EncyclopediaCategory.fromMap()
    //     0xdc910c: bl              #0xdc92cc  ; [package:nuonline/app/data/models/encyclopedia.dart] EncyclopediaCategory::EncyclopediaCategory.fromMap
    // 0xdc9110: mov             x4, x0
    // 0xdc9114: ldur            x3, [fp, #-8]
    // 0xdc9118: stur            x4, [fp, #-0x20]
    // 0xdc911c: r0 = LoadClassIdInstr(r3)
    //     0xdc911c: ldur            x0, [x3, #-1]
    //     0xdc9120: ubfx            x0, x0, #0xc, #0x14
    // 0xdc9124: mov             x1, x3
    // 0xdc9128: r2 = "image"
    //     0xdc9128: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xdc912c: ldr             x2, [x2, #0x520]
    // 0xdc9130: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9130: sub             lr, x0, #0x114
    //     0xdc9134: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9138: blr             lr
    // 0xdc913c: cmp             w0, NULL
    // 0xdc9140: b.ne            #0xdc9150
    // 0xdc9144: r7 = Instance_Image
    //     0xdc9144: add             x7, PP, #0x19, lsl #12  ; [pp+0x19f40] Obj!Image@e25c41
    //     0xdc9148: ldr             x7, [x7, #0xf40]
    // 0xdc914c: b               #0xdc91a4
    // 0xdc9150: ldur            x3, [fp, #-8]
    // 0xdc9154: r0 = LoadClassIdInstr(r3)
    //     0xdc9154: ldur            x0, [x3, #-1]
    //     0xdc9158: ubfx            x0, x0, #0xc, #0x14
    // 0xdc915c: mov             x1, x3
    // 0xdc9160: r2 = "image"
    //     0xdc9160: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xdc9164: ldr             x2, [x2, #0x520]
    // 0xdc9168: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9168: sub             lr, x0, #0x114
    //     0xdc916c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9170: blr             lr
    // 0xdc9174: mov             x3, x0
    // 0xdc9178: r2 = Null
    //     0xdc9178: mov             x2, NULL
    // 0xdc917c: r1 = Null
    //     0xdc917c: mov             x1, NULL
    // 0xdc9180: stur            x3, [fp, #-0x28]
    // 0xdc9184: r8 = Map<String, dynamic>
    //     0xdc9184: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xdc9188: r3 = Null
    //     0xdc9188: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f48] Null
    //     0xdc918c: ldr             x3, [x3, #0xf48]
    // 0xdc9190: r0 = Map<String, dynamic>()
    //     0xdc9190: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xdc9194: ldur            x2, [fp, #-0x28]
    // 0xdc9198: r1 = Null
    //     0xdc9198: mov             x1, NULL
    // 0xdc919c: r0 = Image.fromMap()
    //     0xdc919c: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xdc91a0: mov             x7, x0
    // 0xdc91a4: ldur            x3, [fp, #-8]
    // 0xdc91a8: ldur            x6, [fp, #-0x10]
    // 0xdc91ac: ldur            x5, [fp, #-0x18]
    // 0xdc91b0: ldur            x4, [fp, #-0x20]
    // 0xdc91b4: stur            x7, [fp, #-0x28]
    // 0xdc91b8: r0 = LoadClassIdInstr(r3)
    //     0xdc91b8: ldur            x0, [x3, #-1]
    //     0xdc91bc: ubfx            x0, x0, #0xc, #0x14
    // 0xdc91c0: mov             x1, x3
    // 0xdc91c4: r2 = "content"
    //     0xdc91c4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0xdc91c8: ldr             x2, [x2, #0xf58]
    // 0xdc91cc: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc91cc: sub             lr, x0, #0x114
    //     0xdc91d0: ldr             lr, [x21, lr, lsl #3]
    //     0xdc91d4: blr             lr
    // 0xdc91d8: mov             x3, x0
    // 0xdc91dc: r2 = Null
    //     0xdc91dc: mov             x2, NULL
    // 0xdc91e0: r1 = Null
    //     0xdc91e0: mov             x1, NULL
    // 0xdc91e4: stur            x3, [fp, #-0x30]
    // 0xdc91e8: r4 = 60
    //     0xdc91e8: movz            x4, #0x3c
    // 0xdc91ec: branchIfSmi(r0, 0xdc91f8)
    //     0xdc91ec: tbz             w0, #0, #0xdc91f8
    // 0xdc91f0: r4 = LoadClassIdInstr(r0)
    //     0xdc91f0: ldur            x4, [x0, #-1]
    //     0xdc91f4: ubfx            x4, x4, #0xc, #0x14
    // 0xdc91f8: sub             x4, x4, #0x5e
    // 0xdc91fc: cmp             x4, #1
    // 0xdc9200: b.ls            #0xdc9214
    // 0xdc9204: r8 = String
    //     0xdc9204: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc9208: r3 = Null
    //     0xdc9208: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f60] Null
    //     0xdc920c: ldr             x3, [x3, #0xf60]
    // 0xdc9210: r0 = String()
    //     0xdc9210: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc9214: ldur            x1, [fp, #-8]
    // 0xdc9218: r0 = LoadClassIdInstr(r1)
    //     0xdc9218: ldur            x0, [x1, #-1]
    //     0xdc921c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc9220: r2 = "updated_at"
    //     0xdc9220: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0xdc9224: ldr             x2, [x2, #0xe88]
    // 0xdc9228: r0 = GDT[cid_x0 + -0x114]()
    //     0xdc9228: sub             lr, x0, #0x114
    //     0xdc922c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc9230: blr             lr
    // 0xdc9234: mov             x3, x0
    // 0xdc9238: r2 = Null
    //     0xdc9238: mov             x2, NULL
    // 0xdc923c: r1 = Null
    //     0xdc923c: mov             x1, NULL
    // 0xdc9240: stur            x3, [fp, #-8]
    // 0xdc9244: r4 = 60
    //     0xdc9244: movz            x4, #0x3c
    // 0xdc9248: branchIfSmi(r0, 0xdc9254)
    //     0xdc9248: tbz             w0, #0, #0xdc9254
    // 0xdc924c: r4 = LoadClassIdInstr(r0)
    //     0xdc924c: ldur            x4, [x0, #-1]
    //     0xdc9250: ubfx            x4, x4, #0xc, #0x14
    // 0xdc9254: sub             x4, x4, #0x5e
    // 0xdc9258: cmp             x4, #1
    // 0xdc925c: b.ls            #0xdc9270
    // 0xdc9260: r8 = String
    //     0xdc9260: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc9264: r3 = Null
    //     0xdc9264: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f70] Null
    //     0xdc9268: ldr             x3, [x3, #0xf70]
    // 0xdc926c: r0 = String()
    //     0xdc926c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc9270: ldur            x0, [fp, #-0x10]
    // 0xdc9274: r1 = LoadInt32Instr(r0)
    //     0xdc9274: sbfx            x1, x0, #1, #0x1f
    //     0xdc9278: tbz             w0, #0, #0xdc9280
    //     0xdc927c: ldur            x1, [x0, #7]
    // 0xdc9280: stur            x1, [fp, #-0x38]
    // 0xdc9284: r0 = Encyclopedia()
    //     0xdc9284: bl              #0xa61e78  ; AllocateEncyclopediaStub -> Encyclopedia (size=0x24)
    // 0xdc9288: ldur            x1, [fp, #-0x38]
    // 0xdc928c: StoreField: r0->field_7 = r1
    //     0xdc928c: stur            x1, [x0, #7]
    // 0xdc9290: ldur            x1, [fp, #-0x18]
    // 0xdc9294: StoreField: r0->field_f = r1
    //     0xdc9294: stur            w1, [x0, #0xf]
    // 0xdc9298: ldur            x1, [fp, #-0x20]
    // 0xdc929c: StoreField: r0->field_13 = r1
    //     0xdc929c: stur            w1, [x0, #0x13]
    // 0xdc92a0: ldur            x1, [fp, #-0x28]
    // 0xdc92a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xdc92a4: stur            w1, [x0, #0x17]
    // 0xdc92a8: ldur            x1, [fp, #-0x30]
    // 0xdc92ac: StoreField: r0->field_1b = r1
    //     0xdc92ac: stur            w1, [x0, #0x1b]
    // 0xdc92b0: ldur            x1, [fp, #-8]
    // 0xdc92b4: StoreField: r0->field_1f = r1
    //     0xdc92b4: stur            w1, [x0, #0x1f]
    // 0xdc92b8: LeaveFrame
    //     0xdc92b8: mov             SP, fp
    //     0xdc92bc: ldp             fp, lr, [SP], #0x10
    // 0xdc92c0: ret
    //     0xdc92c0: ret             
    // 0xdc92c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc92c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc92c8: b               #0xdc9008
  }
  get _ prefix(/* No info */) {
    // ** addr: 0xe78bf4, size: 0x58
    // 0xe78bf4: EnterFrame
    //     0xe78bf4: stp             fp, lr, [SP, #-0x10]!
    //     0xe78bf8: mov             fp, SP
    // 0xe78bfc: AllocStack(0x10)
    //     0xe78bfc: sub             SP, SP, #0x10
    // 0xe78c00: CheckStackOverflow
    //     0xe78c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78c04: cmp             SP, x16
    //     0xe78c08: b.ls            #0xe78c44
    // 0xe78c0c: LoadField: r0 = r1->field_f
    //     0xe78c0c: ldur            w0, [x1, #0xf]
    // 0xe78c10: DecompressPointer r0
    //     0xe78c10: add             x0, x0, HEAP, lsl #32
    // 0xe78c14: stp             xzr, x0, [SP]
    // 0xe78c18: r0 = []()
    //     0xe78c18: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xe78c1c: r1 = LoadClassIdInstr(r0)
    //     0xe78c1c: ldur            x1, [x0, #-1]
    //     0xe78c20: ubfx            x1, x1, #0xc, #0x14
    // 0xe78c24: str             x0, [SP]
    // 0xe78c28: mov             x0, x1
    // 0xe78c2c: r0 = GDT[cid_x0 + -0xff6]()
    //     0xe78c2c: sub             lr, x0, #0xff6
    //     0xe78c30: ldr             lr, [x21, lr, lsl #3]
    //     0xe78c34: blr             lr
    // 0xe78c38: LeaveFrame
    //     0xe78c38: mov             SP, fp
    //     0xe78c3c: ldp             fp, lr, [SP], #0x10
    // 0xe78c40: ret
    //     0xe78c40: ret             
    // 0xe78c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78c48: b               #0xe78c0c
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe791e0, size: 0x34
    // 0xe791e0: EnterFrame
    //     0xe791e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe791e4: mov             fp, SP
    // 0xe791e8: CheckStackOverflow
    //     0xe791e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe791ec: cmp             SP, x16
    //     0xe791f0: b.ls            #0xe7920c
    // 0xe791f4: r1 = <Encyclopedia>
    //     0xe791f4: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe791f8: r2 = 0
    //     0xe791f8: movz            x2, #0
    // 0xe791fc: r0 = _GrowableList()
    //     0xe791fc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe79200: LeaveFrame
    //     0xe79200: mov             SP, fp
    //     0xe79204: ldp             fp, lr, [SP], #0x10
    // 0xe79208: ret
    //     0xe79208: ret             
    // 0xe7920c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7920c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79210: b               #0xe791f4
  }
}
