// lib: , url: package:nuonline/app/data/models/event.dart

// class id: 1050020, size: 0x8
class :: {
}

// class id: 5586, size: 0x1c, field offset: 0x8
//   const constructor, 
class Event extends Equatable {

  get _ props(/* No info */) {
    // ** addr: 0xbdbd40, size: 0x84
    // 0xbdbd40: EnterFrame
    //     0xbdbd40: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbd44: mov             fp, SP
    // 0xbdbd48: AllocStack(0x20)
    //     0xbdbd48: sub             SP, SP, #0x20
    // 0xbdbd4c: r0 = 6
    //     0xbdbd4c: movz            x0, #0x6
    // 0xbdbd50: LoadField: r3 = r1->field_7
    //     0xbdbd50: ldur            w3, [x1, #7]
    // 0xbdbd54: DecompressPointer r3
    //     0xbdbd54: add             x3, x3, HEAP, lsl #32
    // 0xbdbd58: stur            x3, [fp, #-0x18]
    // 0xbdbd5c: LoadField: r4 = r1->field_b
    //     0xbdbd5c: ldur            w4, [x1, #0xb]
    // 0xbdbd60: DecompressPointer r4
    //     0xbdbd60: add             x4, x4, HEAP, lsl #32
    // 0xbdbd64: stur            x4, [fp, #-0x10]
    // 0xbdbd68: LoadField: r5 = r1->field_f
    //     0xbdbd68: ldur            w5, [x1, #0xf]
    // 0xbdbd6c: DecompressPointer r5
    //     0xbdbd6c: add             x5, x5, HEAP, lsl #32
    // 0xbdbd70: mov             x2, x0
    // 0xbdbd74: stur            x5, [fp, #-8]
    // 0xbdbd78: r1 = Null
    //     0xbdbd78: mov             x1, NULL
    // 0xbdbd7c: r0 = AllocateArray()
    //     0xbdbd7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbd80: mov             x2, x0
    // 0xbdbd84: ldur            x0, [fp, #-0x18]
    // 0xbdbd88: stur            x2, [fp, #-0x20]
    // 0xbdbd8c: StoreField: r2->field_f = r0
    //     0xbdbd8c: stur            w0, [x2, #0xf]
    // 0xbdbd90: ldur            x0, [fp, #-0x10]
    // 0xbdbd94: StoreField: r2->field_13 = r0
    //     0xbdbd94: stur            w0, [x2, #0x13]
    // 0xbdbd98: ldur            x0, [fp, #-8]
    // 0xbdbd9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdbd9c: stur            w0, [x2, #0x17]
    // 0xbdbda0: r1 = <Object?>
    //     0xbdbda0: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbda4: r0 = AllocateGrowableArray()
    //     0xbdbda4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbda8: ldur            x1, [fp, #-0x20]
    // 0xbdbdac: StoreField: r0->field_f = r1
    //     0xbdbdac: stur            w1, [x0, #0xf]
    // 0xbdbdb0: r1 = 6
    //     0xbdbdb0: movz            x1, #0x6
    // 0xbdbdb4: StoreField: r0->field_b = r1
    //     0xbdbdb4: stur            w1, [x0, #0xb]
    // 0xbdbdb8: LeaveFrame
    //     0xbdbdb8: mov             SP, fp
    //     0xbdbdbc: ldp             fp, lr, [SP], #0x10
    // 0xbdbdc0: ret
    //     0xbdbdc0: ret             
  }
}
