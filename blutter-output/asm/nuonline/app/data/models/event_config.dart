// lib: , url: package:nuonline/app/data/models/event_config.dart

// class id: 1050021, size: 0x8
class :: {
}

// class id: 5585, size: 0x2c, field offset: 0x8
//   const constructor, 
class EventConfig extends Equatable {

  factory _ EventConfig.fromMap(/* No info */) {
    // ** addr: 0x821670, size: 0x480
    // 0x821670: EnterFrame
    //     0x821670: stp             fp, lr, [SP, #-0x10]!
    //     0x821674: mov             fp, SP
    // 0x821678: AllocStack(0x48)
    //     0x821678: sub             SP, SP, #0x48
    // 0x82167c: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x82167c: mov             x0, x1
    //     0x821680: mov             x1, x2
    //     0x821684: stur            x2, [fp, #-8]
    // 0x821688: CheckStackOverflow
    //     0x821688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82168c: cmp             SP, x16
    //     0x821690: b.ls            #0x821ae8
    // 0x821694: r1 = 1
    //     0x821694: movz            x1, #0x1
    // 0x821698: r0 = AllocateContext()
    //     0x821698: bl              #0xec126c  ; AllocateContextStub
    // 0x82169c: mov             x3, x0
    // 0x8216a0: ldur            x1, [fp, #-8]
    // 0x8216a4: stur            x3, [fp, #-0x10]
    // 0x8216a8: StoreField: r3->field_f = r1
    //     0x8216a8: stur            w1, [x3, #0xf]
    // 0x8216ac: r0 = LoadClassIdInstr(r1)
    //     0x8216ac: ldur            x0, [x1, #-1]
    //     0x8216b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8216b4: r2 = "year"
    //     0x8216b4: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x8216b8: ldr             x2, [x2, #0x310]
    // 0x8216bc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8216bc: sub             lr, x0, #0x114
    //     0x8216c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8216c4: blr             lr
    // 0x8216c8: cmp             w0, NULL
    // 0x8216cc: b.eq            #0x821738
    // 0x8216d0: ldur            x3, [fp, #-0x10]
    // 0x8216d4: LoadField: r1 = r3->field_f
    //     0x8216d4: ldur            w1, [x3, #0xf]
    // 0x8216d8: DecompressPointer r1
    //     0x8216d8: add             x1, x1, HEAP, lsl #32
    // 0x8216dc: r0 = LoadClassIdInstr(r1)
    //     0x8216dc: ldur            x0, [x1, #-1]
    //     0x8216e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8216e4: r2 = "year"
    //     0x8216e4: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x8216e8: ldr             x2, [x2, #0x310]
    // 0x8216ec: r0 = GDT[cid_x0 + -0x114]()
    //     0x8216ec: sub             lr, x0, #0x114
    //     0x8216f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8216f4: blr             lr
    // 0x8216f8: mov             x3, x0
    // 0x8216fc: r2 = Null
    //     0x8216fc: mov             x2, NULL
    // 0x821700: r1 = Null
    //     0x821700: mov             x1, NULL
    // 0x821704: stur            x3, [fp, #-8]
    // 0x821708: branchIfSmi(r0, 0x821730)
    //     0x821708: tbz             w0, #0, #0x821730
    // 0x82170c: r4 = LoadClassIdInstr(r0)
    //     0x82170c: ldur            x4, [x0, #-1]
    //     0x821710: ubfx            x4, x4, #0xc, #0x14
    // 0x821714: sub             x4, x4, #0x3c
    // 0x821718: cmp             x4, #1
    // 0x82171c: b.ls            #0x821730
    // 0x821720: r8 = int
    //     0x821720: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x821724: r3 = Null
    //     0x821724: add             x3, PP, #9, lsl #12  ; [pp+0x9318] Null
    //     0x821728: ldr             x3, [x3, #0x318]
    // 0x82172c: r0 = int()
    //     0x82172c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x821730: ldur            x4, [fp, #-8]
    // 0x821734: b               #0x82173c
    // 0x821738: r4 = Null
    //     0x821738: mov             x4, NULL
    // 0x82173c: ldur            x3, [fp, #-0x10]
    // 0x821740: stur            x4, [fp, #-8]
    // 0x821744: LoadField: r1 = r3->field_f
    //     0x821744: ldur            w1, [x3, #0xf]
    // 0x821748: DecompressPointer r1
    //     0x821748: add             x1, x1, HEAP, lsl #32
    // 0x82174c: r0 = LoadClassIdInstr(r1)
    //     0x82174c: ldur            x0, [x1, #-1]
    //     0x821750: ubfx            x0, x0, #0xc, #0x14
    // 0x821754: r2 = "month"
    //     0x821754: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x821758: ldr             x2, [x2, #0x328]
    // 0x82175c: r0 = GDT[cid_x0 + -0x114]()
    //     0x82175c: sub             lr, x0, #0x114
    //     0x821760: ldr             lr, [x21, lr, lsl #3]
    //     0x821764: blr             lr
    // 0x821768: cmp             w0, NULL
    // 0x82176c: b.eq            #0x8217d8
    // 0x821770: ldur            x3, [fp, #-0x10]
    // 0x821774: LoadField: r1 = r3->field_f
    //     0x821774: ldur            w1, [x3, #0xf]
    // 0x821778: DecompressPointer r1
    //     0x821778: add             x1, x1, HEAP, lsl #32
    // 0x82177c: r0 = LoadClassIdInstr(r1)
    //     0x82177c: ldur            x0, [x1, #-1]
    //     0x821780: ubfx            x0, x0, #0xc, #0x14
    // 0x821784: r2 = "month"
    //     0x821784: add             x2, PP, #9, lsl #12  ; [pp+0x9328] "month"
    //     0x821788: ldr             x2, [x2, #0x328]
    // 0x82178c: r0 = GDT[cid_x0 + -0x114]()
    //     0x82178c: sub             lr, x0, #0x114
    //     0x821790: ldr             lr, [x21, lr, lsl #3]
    //     0x821794: blr             lr
    // 0x821798: mov             x3, x0
    // 0x82179c: r2 = Null
    //     0x82179c: mov             x2, NULL
    // 0x8217a0: r1 = Null
    //     0x8217a0: mov             x1, NULL
    // 0x8217a4: stur            x3, [fp, #-0x18]
    // 0x8217a8: branchIfSmi(r0, 0x8217d0)
    //     0x8217a8: tbz             w0, #0, #0x8217d0
    // 0x8217ac: r4 = LoadClassIdInstr(r0)
    //     0x8217ac: ldur            x4, [x0, #-1]
    //     0x8217b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8217b4: sub             x4, x4, #0x3c
    // 0x8217b8: cmp             x4, #1
    // 0x8217bc: b.ls            #0x8217d0
    // 0x8217c0: r8 = int
    //     0x8217c0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8217c4: r3 = Null
    //     0x8217c4: add             x3, PP, #9, lsl #12  ; [pp+0x9330] Null
    //     0x8217c8: ldr             x3, [x3, #0x330]
    // 0x8217cc: r0 = int()
    //     0x8217cc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8217d0: ldur            x4, [fp, #-0x18]
    // 0x8217d4: b               #0x8217dc
    // 0x8217d8: r4 = Null
    //     0x8217d8: mov             x4, NULL
    // 0x8217dc: ldur            x3, [fp, #-0x10]
    // 0x8217e0: stur            x4, [fp, #-0x18]
    // 0x8217e4: LoadField: r1 = r3->field_f
    //     0x8217e4: ldur            w1, [x3, #0xf]
    // 0x8217e8: DecompressPointer r1
    //     0x8217e8: add             x1, x1, HEAP, lsl #32
    // 0x8217ec: r0 = LoadClassIdInstr(r1)
    //     0x8217ec: ldur            x0, [x1, #-1]
    //     0x8217f0: ubfx            x0, x0, #0xc, #0x14
    // 0x8217f4: r2 = "day"
    //     0x8217f4: add             x2, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0x8217f8: ldr             x2, [x2, #0x340]
    // 0x8217fc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8217fc: sub             lr, x0, #0x114
    //     0x821800: ldr             lr, [x21, lr, lsl #3]
    //     0x821804: blr             lr
    // 0x821808: cmp             w0, NULL
    // 0x82180c: b.eq            #0x821878
    // 0x821810: ldur            x3, [fp, #-0x10]
    // 0x821814: LoadField: r1 = r3->field_f
    //     0x821814: ldur            w1, [x3, #0xf]
    // 0x821818: DecompressPointer r1
    //     0x821818: add             x1, x1, HEAP, lsl #32
    // 0x82181c: r0 = LoadClassIdInstr(r1)
    //     0x82181c: ldur            x0, [x1, #-1]
    //     0x821820: ubfx            x0, x0, #0xc, #0x14
    // 0x821824: r2 = "day"
    //     0x821824: add             x2, PP, #9, lsl #12  ; [pp+0x9340] "day"
    //     0x821828: ldr             x2, [x2, #0x340]
    // 0x82182c: r0 = GDT[cid_x0 + -0x114]()
    //     0x82182c: sub             lr, x0, #0x114
    //     0x821830: ldr             lr, [x21, lr, lsl #3]
    //     0x821834: blr             lr
    // 0x821838: mov             x3, x0
    // 0x82183c: r2 = Null
    //     0x82183c: mov             x2, NULL
    // 0x821840: r1 = Null
    //     0x821840: mov             x1, NULL
    // 0x821844: stur            x3, [fp, #-0x20]
    // 0x821848: branchIfSmi(r0, 0x821870)
    //     0x821848: tbz             w0, #0, #0x821870
    // 0x82184c: r4 = LoadClassIdInstr(r0)
    //     0x82184c: ldur            x4, [x0, #-1]
    //     0x821850: ubfx            x4, x4, #0xc, #0x14
    // 0x821854: sub             x4, x4, #0x3c
    // 0x821858: cmp             x4, #1
    // 0x82185c: b.ls            #0x821870
    // 0x821860: r8 = int
    //     0x821860: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x821864: r3 = Null
    //     0x821864: add             x3, PP, #9, lsl #12  ; [pp+0x9348] Null
    //     0x821868: ldr             x3, [x3, #0x348]
    // 0x82186c: r0 = int()
    //     0x82186c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x821870: ldur            x4, [fp, #-0x20]
    // 0x821874: b               #0x82187c
    // 0x821878: r4 = Null
    //     0x821878: mov             x4, NULL
    // 0x82187c: ldur            x3, [fp, #-0x10]
    // 0x821880: stur            x4, [fp, #-0x20]
    // 0x821884: LoadField: r1 = r3->field_f
    //     0x821884: ldur            w1, [x3, #0xf]
    // 0x821888: DecompressPointer r1
    //     0x821888: add             x1, x1, HEAP, lsl #32
    // 0x82188c: r0 = LoadClassIdInstr(r1)
    //     0x82188c: ldur            x0, [x1, #-1]
    //     0x821890: ubfx            x0, x0, #0xc, #0x14
    // 0x821894: r2 = "weekday"
    //     0x821894: add             x2, PP, #9, lsl #12  ; [pp+0x9358] "weekday"
    //     0x821898: ldr             x2, [x2, #0x358]
    // 0x82189c: r0 = GDT[cid_x0 + -0x114]()
    //     0x82189c: sub             lr, x0, #0x114
    //     0x8218a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8218a4: blr             lr
    // 0x8218a8: cmp             w0, NULL
    // 0x8218ac: b.eq            #0x821918
    // 0x8218b0: ldur            x3, [fp, #-0x10]
    // 0x8218b4: LoadField: r1 = r3->field_f
    //     0x8218b4: ldur            w1, [x3, #0xf]
    // 0x8218b8: DecompressPointer r1
    //     0x8218b8: add             x1, x1, HEAP, lsl #32
    // 0x8218bc: r0 = LoadClassIdInstr(r1)
    //     0x8218bc: ldur            x0, [x1, #-1]
    //     0x8218c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8218c4: r2 = "weekday"
    //     0x8218c4: add             x2, PP, #9, lsl #12  ; [pp+0x9358] "weekday"
    //     0x8218c8: ldr             x2, [x2, #0x358]
    // 0x8218cc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8218cc: sub             lr, x0, #0x114
    //     0x8218d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8218d4: blr             lr
    // 0x8218d8: mov             x3, x0
    // 0x8218dc: r2 = Null
    //     0x8218dc: mov             x2, NULL
    // 0x8218e0: r1 = Null
    //     0x8218e0: mov             x1, NULL
    // 0x8218e4: stur            x3, [fp, #-0x28]
    // 0x8218e8: branchIfSmi(r0, 0x821910)
    //     0x8218e8: tbz             w0, #0, #0x821910
    // 0x8218ec: r4 = LoadClassIdInstr(r0)
    //     0x8218ec: ldur            x4, [x0, #-1]
    //     0x8218f0: ubfx            x4, x4, #0xc, #0x14
    // 0x8218f4: sub             x4, x4, #0x3c
    // 0x8218f8: cmp             x4, #1
    // 0x8218fc: b.ls            #0x821910
    // 0x821900: r8 = int
    //     0x821900: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x821904: r3 = Null
    //     0x821904: add             x3, PP, #9, lsl #12  ; [pp+0x9360] Null
    //     0x821908: ldr             x3, [x3, #0x360]
    // 0x82190c: r0 = int()
    //     0x82190c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x821910: ldur            x7, [fp, #-0x28]
    // 0x821914: b               #0x82191c
    // 0x821918: r7 = Null
    //     0x821918: mov             x7, NULL
    // 0x82191c: ldur            x3, [fp, #-0x10]
    // 0x821920: ldur            x6, [fp, #-8]
    // 0x821924: ldur            x5, [fp, #-0x18]
    // 0x821928: ldur            x4, [fp, #-0x20]
    // 0x82192c: stur            x7, [fp, #-0x28]
    // 0x821930: LoadField: r1 = r3->field_f
    //     0x821930: ldur            w1, [x3, #0xf]
    // 0x821934: DecompressPointer r1
    //     0x821934: add             x1, x1, HEAP, lsl #32
    // 0x821938: r0 = LoadClassIdInstr(r1)
    //     0x821938: ldur            x0, [x1, #-1]
    //     0x82193c: ubfx            x0, x0, #0xc, #0x14
    // 0x821940: r2 = "title"
    //     0x821940: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x821944: ldr             x2, [x2, #0x748]
    // 0x821948: r0 = GDT[cid_x0 + -0x114]()
    //     0x821948: sub             lr, x0, #0x114
    //     0x82194c: ldr             lr, [x21, lr, lsl #3]
    //     0x821950: blr             lr
    // 0x821954: mov             x3, x0
    // 0x821958: r2 = Null
    //     0x821958: mov             x2, NULL
    // 0x82195c: r1 = Null
    //     0x82195c: mov             x1, NULL
    // 0x821960: stur            x3, [fp, #-0x30]
    // 0x821964: r4 = 60
    //     0x821964: movz            x4, #0x3c
    // 0x821968: branchIfSmi(r0, 0x821974)
    //     0x821968: tbz             w0, #0, #0x821974
    // 0x82196c: r4 = LoadClassIdInstr(r0)
    //     0x82196c: ldur            x4, [x0, #-1]
    //     0x821970: ubfx            x4, x4, #0xc, #0x14
    // 0x821974: sub             x4, x4, #0x5e
    // 0x821978: cmp             x4, #1
    // 0x82197c: b.ls            #0x821990
    // 0x821980: r8 = String
    //     0x821980: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x821984: r3 = Null
    //     0x821984: add             x3, PP, #9, lsl #12  ; [pp+0x9370] Null
    //     0x821988: ldr             x3, [x3, #0x370]
    // 0x82198c: r0 = String()
    //     0x82198c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x821990: ldur            x2, [fp, #-0x10]
    // 0x821994: r1 = Function '<anonymous closure>': static.
    //     0x821994: add             x1, PP, #9, lsl #12  ; [pp+0x9380] AnonymousClosure: static (0x821b80), in [package:nuonline/app/data/models/event_config.dart] EventConfig::EventConfig.fromMap (0x821670)
    //     0x821998: ldr             x1, [x1, #0x380]
    // 0x82199c: r0 = AllocateClosure()
    //     0x82199c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8219a0: mov             x2, x0
    // 0x8219a4: r1 = const [Instance of 'EventCategory', Instance of 'EventCategory', Instance of 'EventCategory', Instance of 'EventCategory']
    //     0x8219a4: add             x1, PP, #9, lsl #12  ; [pp+0x9388] List<EventCategory>(4)
    //     0x8219a8: ldr             x1, [x1, #0x388]
    // 0x8219ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8219ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8219b0: r0 = firstWhere()
    //     0x8219b0: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x8219b4: ldur            x2, [fp, #-0x10]
    // 0x8219b8: r1 = Function '<anonymous closure>': static.
    //     0x8219b8: add             x1, PP, #9, lsl #12  ; [pp+0x9390] AnonymousClosure: static (0x821afc), in [package:nuonline/app/data/models/event_config.dart] EventConfig::EventConfig.fromMap (0x821670)
    //     0x8219bc: ldr             x1, [x1, #0x390]
    // 0x8219c0: stur            x0, [fp, #-0x38]
    // 0x8219c4: r0 = AllocateClosure()
    //     0x8219c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8219c8: mov             x2, x0
    // 0x8219cc: r1 = const [Instance of 'EventType', Instance of 'EventType', Instance of 'EventType', Instance of 'EventType', Instance of 'EventType', Instance of 'EventType', Instance of 'EventType']
    //     0x8219cc: add             x1, PP, #9, lsl #12  ; [pp+0x9398] List<EventType>(7)
    //     0x8219d0: ldr             x1, [x1, #0x398]
    // 0x8219d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8219d4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8219d8: r0 = firstWhere()
    //     0x8219d8: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x8219dc: mov             x4, x0
    // 0x8219e0: ldur            x3, [fp, #-0x10]
    // 0x8219e4: stur            x4, [fp, #-0x40]
    // 0x8219e8: LoadField: r1 = r3->field_f
    //     0x8219e8: ldur            w1, [x3, #0xf]
    // 0x8219ec: DecompressPointer r1
    //     0x8219ec: add             x1, x1, HEAP, lsl #32
    // 0x8219f0: r0 = LoadClassIdInstr(r1)
    //     0x8219f0: ldur            x0, [x1, #-1]
    //     0x8219f4: ubfx            x0, x0, #0xc, #0x14
    // 0x8219f8: r2 = "route"
    //     0x8219f8: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x8219fc: ldr             x2, [x2, #0x3a0]
    // 0x821a00: r0 = GDT[cid_x0 + -0x114]()
    //     0x821a00: sub             lr, x0, #0x114
    //     0x821a04: ldr             lr, [x21, lr, lsl #3]
    //     0x821a08: blr             lr
    // 0x821a0c: mov             x3, x0
    // 0x821a10: r2 = Null
    //     0x821a10: mov             x2, NULL
    // 0x821a14: r1 = Null
    //     0x821a14: mov             x1, NULL
    // 0x821a18: stur            x3, [fp, #-0x48]
    // 0x821a1c: r4 = 60
    //     0x821a1c: movz            x4, #0x3c
    // 0x821a20: branchIfSmi(r0, 0x821a2c)
    //     0x821a20: tbz             w0, #0, #0x821a2c
    // 0x821a24: r4 = LoadClassIdInstr(r0)
    //     0x821a24: ldur            x4, [x0, #-1]
    //     0x821a28: ubfx            x4, x4, #0xc, #0x14
    // 0x821a2c: sub             x4, x4, #0x5e
    // 0x821a30: cmp             x4, #1
    // 0x821a34: b.ls            #0x821a48
    // 0x821a38: r8 = String?
    //     0x821a38: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x821a3c: r3 = Null
    //     0x821a3c: add             x3, PP, #9, lsl #12  ; [pp+0x93a8] Null
    //     0x821a40: ldr             x3, [x3, #0x3a8]
    // 0x821a44: r0 = String?()
    //     0x821a44: bl              #0x600324  ; IsType_String?_Stub
    // 0x821a48: ldur            x0, [fp, #-0x10]
    // 0x821a4c: LoadField: r1 = r0->field_f
    //     0x821a4c: ldur            w1, [x0, #0xf]
    // 0x821a50: DecompressPointer r1
    //     0x821a50: add             x1, x1, HEAP, lsl #32
    // 0x821a54: r0 = LoadClassIdInstr(r1)
    //     0x821a54: ldur            x0, [x1, #-1]
    //     0x821a58: ubfx            x0, x0, #0xc, #0x14
    // 0x821a5c: r2 = "params"
    //     0x821a5c: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x821a60: ldr             x2, [x2, #0x3b8]
    // 0x821a64: r0 = GDT[cid_x0 + -0x114]()
    //     0x821a64: sub             lr, x0, #0x114
    //     0x821a68: ldr             lr, [x21, lr, lsl #3]
    //     0x821a6c: blr             lr
    // 0x821a70: mov             x3, x0
    // 0x821a74: r2 = Null
    //     0x821a74: mov             x2, NULL
    // 0x821a78: r1 = Null
    //     0x821a78: mov             x1, NULL
    // 0x821a7c: stur            x3, [fp, #-0x10]
    // 0x821a80: r8 = Map<String, dynamic>?
    //     0x821a80: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0x821a84: r3 = Null
    //     0x821a84: add             x3, PP, #9, lsl #12  ; [pp+0x93c0] Null
    //     0x821a88: ldr             x3, [x3, #0x3c0]
    // 0x821a8c: r0 = Map<String, dynamic>?()
    //     0x821a8c: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0x821a90: r0 = EventConfig()
    //     0x821a90: bl              #0x821af0  ; AllocateEventConfigStub -> EventConfig (size=0x2c)
    // 0x821a94: ldur            x1, [fp, #-8]
    // 0x821a98: StoreField: r0->field_7 = r1
    //     0x821a98: stur            w1, [x0, #7]
    // 0x821a9c: ldur            x1, [fp, #-0x18]
    // 0x821aa0: StoreField: r0->field_b = r1
    //     0x821aa0: stur            w1, [x0, #0xb]
    // 0x821aa4: ldur            x1, [fp, #-0x20]
    // 0x821aa8: StoreField: r0->field_f = r1
    //     0x821aa8: stur            w1, [x0, #0xf]
    // 0x821aac: ldur            x1, [fp, #-0x28]
    // 0x821ab0: StoreField: r0->field_13 = r1
    //     0x821ab0: stur            w1, [x0, #0x13]
    // 0x821ab4: ldur            x1, [fp, #-0x30]
    // 0x821ab8: ArrayStore: r0[0] = r1  ; List_4
    //     0x821ab8: stur            w1, [x0, #0x17]
    // 0x821abc: ldur            x1, [fp, #-0x40]
    // 0x821ac0: StoreField: r0->field_1b = r1
    //     0x821ac0: stur            w1, [x0, #0x1b]
    // 0x821ac4: ldur            x1, [fp, #-0x38]
    // 0x821ac8: StoreField: r0->field_1f = r1
    //     0x821ac8: stur            w1, [x0, #0x1f]
    // 0x821acc: ldur            x1, [fp, #-0x48]
    // 0x821ad0: StoreField: r0->field_23 = r1
    //     0x821ad0: stur            w1, [x0, #0x23]
    // 0x821ad4: ldur            x1, [fp, #-0x10]
    // 0x821ad8: StoreField: r0->field_27 = r1
    //     0x821ad8: stur            w1, [x0, #0x27]
    // 0x821adc: LeaveFrame
    //     0x821adc: mov             SP, fp
    //     0x821ae0: ldp             fp, lr, [SP], #0x10
    // 0x821ae4: ret
    //     0x821ae4: ret             
    // 0x821ae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x821ae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x821aec: b               #0x821694
  }
  [closure] static bool <anonymous closure>(dynamic, EventType) {
    // ** addr: 0x821afc, size: 0x84
    // 0x821afc: EnterFrame
    //     0x821afc: stp             fp, lr, [SP, #-0x10]!
    //     0x821b00: mov             fp, SP
    // 0x821b04: AllocStack(0x18)
    //     0x821b04: sub             SP, SP, #0x18
    // 0x821b08: SetupParameters()
    //     0x821b08: ldr             x0, [fp, #0x18]
    //     0x821b0c: ldur            w1, [x0, #0x17]
    //     0x821b10: add             x1, x1, HEAP, lsl #32
    // 0x821b14: CheckStackOverflow
    //     0x821b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x821b18: cmp             SP, x16
    //     0x821b1c: b.ls            #0x821b78
    // 0x821b20: ldr             x0, [fp, #0x10]
    // 0x821b24: LoadField: r3 = r0->field_f
    //     0x821b24: ldur            w3, [x0, #0xf]
    // 0x821b28: DecompressPointer r3
    //     0x821b28: add             x3, x3, HEAP, lsl #32
    // 0x821b2c: stur            x3, [fp, #-8]
    // 0x821b30: LoadField: r0 = r1->field_f
    //     0x821b30: ldur            w0, [x1, #0xf]
    // 0x821b34: DecompressPointer r0
    //     0x821b34: add             x0, x0, HEAP, lsl #32
    // 0x821b38: r1 = LoadClassIdInstr(r0)
    //     0x821b38: ldur            x1, [x0, #-1]
    //     0x821b3c: ubfx            x1, x1, #0xc, #0x14
    // 0x821b40: mov             x16, x0
    // 0x821b44: mov             x0, x1
    // 0x821b48: mov             x1, x16
    // 0x821b4c: r2 = "eventType"
    //     0x821b4c: add             x2, PP, #9, lsl #12  ; [pp+0x93d0] "eventType"
    //     0x821b50: ldr             x2, [x2, #0x3d0]
    // 0x821b54: r0 = GDT[cid_x0 + -0x114]()
    //     0x821b54: sub             lr, x0, #0x114
    //     0x821b58: ldr             lr, [x21, lr, lsl #3]
    //     0x821b5c: blr             lr
    // 0x821b60: ldur            x16, [fp, #-8]
    // 0x821b64: stp             x0, x16, [SP]
    // 0x821b68: r0 = ==()
    //     0x821b68: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x821b6c: LeaveFrame
    //     0x821b6c: mov             SP, fp
    //     0x821b70: ldp             fp, lr, [SP], #0x10
    // 0x821b74: ret
    //     0x821b74: ret             
    // 0x821b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x821b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x821b7c: b               #0x821b20
  }
  [closure] static bool <anonymous closure>(dynamic, EventCategory) {
    // ** addr: 0x821b80, size: 0x84
    // 0x821b80: EnterFrame
    //     0x821b80: stp             fp, lr, [SP, #-0x10]!
    //     0x821b84: mov             fp, SP
    // 0x821b88: AllocStack(0x18)
    //     0x821b88: sub             SP, SP, #0x18
    // 0x821b8c: SetupParameters()
    //     0x821b8c: ldr             x0, [fp, #0x18]
    //     0x821b90: ldur            w1, [x0, #0x17]
    //     0x821b94: add             x1, x1, HEAP, lsl #32
    // 0x821b98: CheckStackOverflow
    //     0x821b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x821b9c: cmp             SP, x16
    //     0x821ba0: b.ls            #0x821bfc
    // 0x821ba4: ldr             x0, [fp, #0x10]
    // 0x821ba8: LoadField: r3 = r0->field_f
    //     0x821ba8: ldur            w3, [x0, #0xf]
    // 0x821bac: DecompressPointer r3
    //     0x821bac: add             x3, x3, HEAP, lsl #32
    // 0x821bb0: stur            x3, [fp, #-8]
    // 0x821bb4: LoadField: r0 = r1->field_f
    //     0x821bb4: ldur            w0, [x1, #0xf]
    // 0x821bb8: DecompressPointer r0
    //     0x821bb8: add             x0, x0, HEAP, lsl #32
    // 0x821bbc: r1 = LoadClassIdInstr(r0)
    //     0x821bbc: ldur            x1, [x0, #-1]
    //     0x821bc0: ubfx            x1, x1, #0xc, #0x14
    // 0x821bc4: mov             x16, x0
    // 0x821bc8: mov             x0, x1
    // 0x821bcc: mov             x1, x16
    // 0x821bd0: r2 = "eventCategory"
    //     0x821bd0: add             x2, PP, #9, lsl #12  ; [pp+0x93d8] "eventCategory"
    //     0x821bd4: ldr             x2, [x2, #0x3d8]
    // 0x821bd8: r0 = GDT[cid_x0 + -0x114]()
    //     0x821bd8: sub             lr, x0, #0x114
    //     0x821bdc: ldr             lr, [x21, lr, lsl #3]
    //     0x821be0: blr             lr
    // 0x821be4: ldur            x16, [fp, #-8]
    // 0x821be8: stp             x0, x16, [SP]
    // 0x821bec: r0 = ==()
    //     0x821bec: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x821bf0: LeaveFrame
    //     0x821bf0: mov             SP, fp
    //     0x821bf4: ldp             fp, lr, [SP], #0x10
    // 0x821bf8: ret
    //     0x821bf8: ret             
    // 0x821bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x821bfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x821c00: b               #0x821ba4
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbdc4, size: 0x84
    // 0xbdbdc4: EnterFrame
    //     0xbdbdc4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbdc8: mov             fp, SP
    // 0xbdbdcc: AllocStack(0x20)
    //     0xbdbdcc: sub             SP, SP, #0x20
    // 0xbdbdd0: r0 = 6
    //     0xbdbdd0: movz            x0, #0x6
    // 0xbdbdd4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbdbdd4: ldur            w3, [x1, #0x17]
    // 0xbdbdd8: DecompressPointer r3
    //     0xbdbdd8: add             x3, x3, HEAP, lsl #32
    // 0xbdbddc: stur            x3, [fp, #-0x18]
    // 0xbdbde0: LoadField: r4 = r1->field_1b
    //     0xbdbde0: ldur            w4, [x1, #0x1b]
    // 0xbdbde4: DecompressPointer r4
    //     0xbdbde4: add             x4, x4, HEAP, lsl #32
    // 0xbdbde8: stur            x4, [fp, #-0x10]
    // 0xbdbdec: LoadField: r5 = r1->field_1f
    //     0xbdbdec: ldur            w5, [x1, #0x1f]
    // 0xbdbdf0: DecompressPointer r5
    //     0xbdbdf0: add             x5, x5, HEAP, lsl #32
    // 0xbdbdf4: mov             x2, x0
    // 0xbdbdf8: stur            x5, [fp, #-8]
    // 0xbdbdfc: r1 = Null
    //     0xbdbdfc: mov             x1, NULL
    // 0xbdbe00: r0 = AllocateArray()
    //     0xbdbe00: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbe04: mov             x2, x0
    // 0xbdbe08: ldur            x0, [fp, #-0x18]
    // 0xbdbe0c: stur            x2, [fp, #-0x20]
    // 0xbdbe10: StoreField: r2->field_f = r0
    //     0xbdbe10: stur            w0, [x2, #0xf]
    // 0xbdbe14: ldur            x0, [fp, #-0x10]
    // 0xbdbe18: StoreField: r2->field_13 = r0
    //     0xbdbe18: stur            w0, [x2, #0x13]
    // 0xbdbe1c: ldur            x0, [fp, #-8]
    // 0xbdbe20: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdbe20: stur            w0, [x2, #0x17]
    // 0xbdbe24: r1 = <Object?>
    //     0xbdbe24: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbe28: r0 = AllocateGrowableArray()
    //     0xbdbe28: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbe2c: ldur            x1, [fp, #-0x20]
    // 0xbdbe30: StoreField: r0->field_f = r1
    //     0xbdbe30: stur            w1, [x0, #0xf]
    // 0xbdbe34: r1 = 6
    //     0xbdbe34: movz            x1, #0x6
    // 0xbdbe38: StoreField: r0->field_b = r1
    //     0xbdbe38: stur            w1, [x0, #0xb]
    // 0xbdbe3c: LeaveFrame
    //     0xbdbe3c: mov             SP, fp
    //     0xbdbe40: ldp             fp, lr, [SP], #0x10
    // 0xbdbe44: ret
    //     0xbdbe44: ret             
  }
}

// class id: 6837, size: 0x14, field offset: 0x14
enum EventType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d244, size: 0x64
    // 0xc4d244: EnterFrame
    //     0xc4d244: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d248: mov             fp, SP
    // 0xc4d24c: AllocStack(0x10)
    //     0xc4d24c: sub             SP, SP, #0x10
    // 0xc4d250: SetupParameters(EventType this /* r1 => r0, fp-0x8 */)
    //     0xc4d250: mov             x0, x1
    //     0xc4d254: stur            x1, [fp, #-8]
    // 0xc4d258: CheckStackOverflow
    //     0xc4d258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d25c: cmp             SP, x16
    //     0xc4d260: b.ls            #0xc4d2a0
    // 0xc4d264: r1 = Null
    //     0xc4d264: mov             x1, NULL
    // 0xc4d268: r2 = 4
    //     0xc4d268: movz            x2, #0x4
    // 0xc4d26c: r0 = AllocateArray()
    //     0xc4d26c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d270: r16 = "EventType."
    //     0xc4d270: add             x16, PP, #0x21, lsl #12  ; [pp+0x213b0] "EventType."
    //     0xc4d274: ldr             x16, [x16, #0x3b0]
    // 0xc4d278: StoreField: r0->field_f = r16
    //     0xc4d278: stur            w16, [x0, #0xf]
    // 0xc4d27c: ldur            x1, [fp, #-8]
    // 0xc4d280: LoadField: r2 = r1->field_f
    //     0xc4d280: ldur            w2, [x1, #0xf]
    // 0xc4d284: DecompressPointer r2
    //     0xc4d284: add             x2, x2, HEAP, lsl #32
    // 0xc4d288: StoreField: r0->field_13 = r2
    //     0xc4d288: stur            w2, [x0, #0x13]
    // 0xc4d28c: str             x0, [SP]
    // 0xc4d290: r0 = _interpolate()
    //     0xc4d290: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d294: LeaveFrame
    //     0xc4d294: mov             SP, fp
    //     0xc4d298: ldp             fp, lr, [SP], #0x10
    // 0xc4d29c: ret
    //     0xc4d29c: ret             
    // 0xc4d2a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d2a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d2a4: b               #0xc4d264
  }
}
