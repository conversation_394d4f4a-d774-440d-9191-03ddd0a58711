// lib: , url: package:nuonline/app/data/models/faq.dart

// class id: 1050022, size: 0x8
class :: {
}

// class id: 1143, size: 0x24, field offset: 0x8
class Faq extends Object {

  factory _ Faq.fromMap(/* No info */) {
    // ** addr: 0x7e86bc, size: 0x4a8
    // 0x7e86bc: EnterFrame
    //     0x7e86bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7e86c0: mov             fp, SP
    // 0x7e86c4: AllocStack(0x48)
    //     0x7e86c4: sub             SP, SP, #0x48
    // 0x7e86c8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e86c8: mov             x3, x2
    //     0x7e86cc: stur            x2, [fp, #-8]
    // 0x7e86d0: CheckStackOverflow
    //     0x7e86d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e86d4: cmp             SP, x16
    //     0x7e86d8: b.ls            #0x7e8b5c
    // 0x7e86dc: r0 = LoadClassIdInstr(r3)
    //     0x7e86dc: ldur            x0, [x3, #-1]
    //     0x7e86e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e86e4: mov             x1, x3
    // 0x7e86e8: r2 = "id"
    //     0x7e86e8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7e86ec: ldr             x2, [x2, #0x740]
    // 0x7e86f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e86f0: sub             lr, x0, #0x114
    //     0x7e86f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e86f8: blr             lr
    // 0x7e86fc: mov             x3, x0
    // 0x7e8700: r2 = Null
    //     0x7e8700: mov             x2, NULL
    // 0x7e8704: r1 = Null
    //     0x7e8704: mov             x1, NULL
    // 0x7e8708: stur            x3, [fp, #-0x10]
    // 0x7e870c: branchIfSmi(r0, 0x7e8734)
    //     0x7e870c: tbz             w0, #0, #0x7e8734
    // 0x7e8710: r4 = LoadClassIdInstr(r0)
    //     0x7e8710: ldur            x4, [x0, #-1]
    //     0x7e8714: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8718: sub             x4, x4, #0x3c
    // 0x7e871c: cmp             x4, #1
    // 0x7e8720: b.ls            #0x7e8734
    // 0x7e8724: r8 = int?
    //     0x7e8724: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e8728: r3 = Null
    //     0x7e8728: add             x3, PP, #0x34, lsl #12  ; [pp+0x348c0] Null
    //     0x7e872c: ldr             x3, [x3, #0x8c0]
    // 0x7e8730: r0 = int?()
    //     0x7e8730: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e8734: ldur            x0, [fp, #-0x10]
    // 0x7e8738: cmp             w0, NULL
    // 0x7e873c: b.ne            #0x7e8748
    // 0x7e8740: r4 = 0
    //     0x7e8740: movz            x4, #0
    // 0x7e8744: b               #0x7e8758
    // 0x7e8748: r1 = LoadInt32Instr(r0)
    //     0x7e8748: sbfx            x1, x0, #1, #0x1f
    //     0x7e874c: tbz             w0, #0, #0x7e8754
    //     0x7e8750: ldur            x1, [x0, #7]
    // 0x7e8754: mov             x4, x1
    // 0x7e8758: ldur            x3, [fp, #-8]
    // 0x7e875c: stur            x4, [fp, #-0x18]
    // 0x7e8760: r0 = LoadClassIdInstr(r3)
    //     0x7e8760: ldur            x0, [x3, #-1]
    //     0x7e8764: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8768: mov             x1, x3
    // 0x7e876c: r2 = "slug"
    //     0x7e876c: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x7e8770: ldr             x2, [x2, #0x9a8]
    // 0x7e8774: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8774: sub             lr, x0, #0x114
    //     0x7e8778: ldr             lr, [x21, lr, lsl #3]
    //     0x7e877c: blr             lr
    // 0x7e8780: r2 = Null
    //     0x7e8780: mov             x2, NULL
    // 0x7e8784: r1 = Null
    //     0x7e8784: mov             x1, NULL
    // 0x7e8788: r4 = 60
    //     0x7e8788: movz            x4, #0x3c
    // 0x7e878c: branchIfSmi(r0, 0x7e8798)
    //     0x7e878c: tbz             w0, #0, #0x7e8798
    // 0x7e8790: r4 = LoadClassIdInstr(r0)
    //     0x7e8790: ldur            x4, [x0, #-1]
    //     0x7e8794: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8798: sub             x4, x4, #0x5e
    // 0x7e879c: cmp             x4, #1
    // 0x7e87a0: b.ls            #0x7e87b4
    // 0x7e87a4: r8 = String?
    //     0x7e87a4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e87a8: r3 = Null
    //     0x7e87a8: add             x3, PP, #0x34, lsl #12  ; [pp+0x348d0] Null
    //     0x7e87ac: ldr             x3, [x3, #0x8d0]
    // 0x7e87b0: r0 = String?()
    //     0x7e87b0: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e87b4: ldur            x3, [fp, #-8]
    // 0x7e87b8: r0 = LoadClassIdInstr(r3)
    //     0x7e87b8: ldur            x0, [x3, #-1]
    //     0x7e87bc: ubfx            x0, x0, #0xc, #0x14
    // 0x7e87c0: mov             x1, x3
    // 0x7e87c4: r2 = "title"
    //     0x7e87c4: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x7e87c8: ldr             x2, [x2, #0x748]
    // 0x7e87cc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e87cc: sub             lr, x0, #0x114
    //     0x7e87d0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e87d4: blr             lr
    // 0x7e87d8: mov             x3, x0
    // 0x7e87dc: r2 = Null
    //     0x7e87dc: mov             x2, NULL
    // 0x7e87e0: r1 = Null
    //     0x7e87e0: mov             x1, NULL
    // 0x7e87e4: stur            x3, [fp, #-0x10]
    // 0x7e87e8: r4 = 60
    //     0x7e87e8: movz            x4, #0x3c
    // 0x7e87ec: branchIfSmi(r0, 0x7e87f8)
    //     0x7e87ec: tbz             w0, #0, #0x7e87f8
    // 0x7e87f0: r4 = LoadClassIdInstr(r0)
    //     0x7e87f0: ldur            x4, [x0, #-1]
    //     0x7e87f4: ubfx            x4, x4, #0xc, #0x14
    // 0x7e87f8: sub             x4, x4, #0x5e
    // 0x7e87fc: cmp             x4, #1
    // 0x7e8800: b.ls            #0x7e8814
    // 0x7e8804: r8 = String?
    //     0x7e8804: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e8808: r3 = Null
    //     0x7e8808: add             x3, PP, #0x34, lsl #12  ; [pp+0x348e0] Null
    //     0x7e880c: ldr             x3, [x3, #0x8e0]
    // 0x7e8810: r0 = String?()
    //     0x7e8810: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e8814: ldur            x0, [fp, #-0x10]
    // 0x7e8818: cmp             w0, NULL
    // 0x7e881c: b.ne            #0x7e8828
    // 0x7e8820: r4 = ""
    //     0x7e8820: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e8824: b               #0x7e882c
    // 0x7e8828: mov             x4, x0
    // 0x7e882c: ldur            x3, [fp, #-8]
    // 0x7e8830: stur            x4, [fp, #-0x10]
    // 0x7e8834: r0 = LoadClassIdInstr(r3)
    //     0x7e8834: ldur            x0, [x3, #-1]
    //     0x7e8838: ubfx            x0, x0, #0xc, #0x14
    // 0x7e883c: mov             x1, x3
    // 0x7e8840: r2 = "content"
    //     0x7e8840: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x7e8844: ldr             x2, [x2, #0xf58]
    // 0x7e8848: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8848: sub             lr, x0, #0x114
    //     0x7e884c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8850: blr             lr
    // 0x7e8854: mov             x3, x0
    // 0x7e8858: r2 = Null
    //     0x7e8858: mov             x2, NULL
    // 0x7e885c: r1 = Null
    //     0x7e885c: mov             x1, NULL
    // 0x7e8860: stur            x3, [fp, #-0x20]
    // 0x7e8864: r4 = 60
    //     0x7e8864: movz            x4, #0x3c
    // 0x7e8868: branchIfSmi(r0, 0x7e8874)
    //     0x7e8868: tbz             w0, #0, #0x7e8874
    // 0x7e886c: r4 = LoadClassIdInstr(r0)
    //     0x7e886c: ldur            x4, [x0, #-1]
    //     0x7e8870: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8874: sub             x4, x4, #0x5e
    // 0x7e8878: cmp             x4, #1
    // 0x7e887c: b.ls            #0x7e8890
    // 0x7e8880: r8 = String?
    //     0x7e8880: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e8884: r3 = Null
    //     0x7e8884: add             x3, PP, #0x34, lsl #12  ; [pp+0x348f0] Null
    //     0x7e8888: ldr             x3, [x3, #0x8f0]
    // 0x7e888c: r0 = String?()
    //     0x7e888c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e8890: ldur            x0, [fp, #-0x20]
    // 0x7e8894: cmp             w0, NULL
    // 0x7e8898: b.ne            #0x7e88a4
    // 0x7e889c: r4 = ""
    //     0x7e889c: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e88a0: b               #0x7e88a8
    // 0x7e88a4: mov             x4, x0
    // 0x7e88a8: ldur            x3, [fp, #-8]
    // 0x7e88ac: stur            x4, [fp, #-0x20]
    // 0x7e88b0: r0 = LoadClassIdInstr(r3)
    //     0x7e88b0: ldur            x0, [x3, #-1]
    //     0x7e88b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e88b8: mov             x1, x3
    // 0x7e88bc: r2 = "get_category"
    //     0x7e88bc: add             x2, PP, #0x34, lsl #12  ; [pp+0x34900] "get_category"
    //     0x7e88c0: ldr             x2, [x2, #0x900]
    // 0x7e88c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e88c4: sub             lr, x0, #0x114
    //     0x7e88c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e88cc: blr             lr
    // 0x7e88d0: mov             x3, x0
    // 0x7e88d4: r2 = Null
    //     0x7e88d4: mov             x2, NULL
    // 0x7e88d8: r1 = Null
    //     0x7e88d8: mov             x1, NULL
    // 0x7e88dc: stur            x3, [fp, #-0x28]
    // 0x7e88e0: r8 = Map<String, dynamic>
    //     0x7e88e0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e88e4: r3 = Null
    //     0x7e88e4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34908] Null
    //     0x7e88e8: ldr             x3, [x3, #0x908]
    // 0x7e88ec: r0 = Map<String, dynamic>()
    //     0x7e88ec: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e88f0: ldur            x2, [fp, #-0x28]
    // 0x7e88f4: r1 = Null
    //     0x7e88f4: mov             x1, NULL
    // 0x7e88f8: r0 = FaqCategory.fromMap()
    //     0x7e88f8: bl              #0x7e8b70  ; [package:nuonline/app/data/models/faq.dart] FaqCategory::FaqCategory.fromMap
    // 0x7e88fc: mov             x4, x0
    // 0x7e8900: ldur            x3, [fp, #-8]
    // 0x7e8904: stur            x4, [fp, #-0x28]
    // 0x7e8908: r0 = LoadClassIdInstr(r3)
    //     0x7e8908: ldur            x0, [x3, #-1]
    //     0x7e890c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8910: mov             x1, x3
    // 0x7e8914: r2 = "articles"
    //     0x7e8914: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0x7e8918: ldr             x2, [x2, #0x8b8]
    // 0x7e891c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e891c: sub             lr, x0, #0x114
    //     0x7e8920: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8924: blr             lr
    // 0x7e8928: mov             x3, x0
    // 0x7e892c: r2 = Null
    //     0x7e892c: mov             x2, NULL
    // 0x7e8930: r1 = Null
    //     0x7e8930: mov             x1, NULL
    // 0x7e8934: stur            x3, [fp, #-0x30]
    // 0x7e8938: r4 = 60
    //     0x7e8938: movz            x4, #0x3c
    // 0x7e893c: branchIfSmi(r0, 0x7e8948)
    //     0x7e893c: tbz             w0, #0, #0x7e8948
    // 0x7e8940: r4 = LoadClassIdInstr(r0)
    //     0x7e8940: ldur            x4, [x0, #-1]
    //     0x7e8944: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8948: sub             x4, x4, #0x5a
    // 0x7e894c: cmp             x4, #2
    // 0x7e8950: b.ls            #0x7e8968
    // 0x7e8954: r8 = List?
    //     0x7e8954: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7e8958: ldr             x8, [x8, #0x140]
    // 0x7e895c: r3 = Null
    //     0x7e895c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34918] Null
    //     0x7e8960: ldr             x3, [x3, #0x918]
    // 0x7e8964: r0 = List?()
    //     0x7e8964: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7e8968: ldur            x0, [fp, #-0x30]
    // 0x7e896c: cmp             w0, NULL
    // 0x7e8970: b.ne            #0x7e8988
    // 0x7e8974: r1 = Null
    //     0x7e8974: mov             x1, NULL
    // 0x7e8978: r2 = 0
    //     0x7e8978: movz            x2, #0
    // 0x7e897c: r0 = _GrowableList()
    //     0x7e897c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e8980: mov             x3, x0
    // 0x7e8984: b               #0x7e898c
    // 0x7e8988: mov             x3, x0
    // 0x7e898c: ldur            x0, [fp, #-8]
    // 0x7e8990: stur            x3, [fp, #-0x30]
    // 0x7e8994: r1 = Function '<anonymous closure>': static.
    //     0x7e8994: add             x1, PP, #0x34, lsl #12  ; [pp+0x34928] AnonymousClosure: static (0x7e8d9c), in [package:nuonline/app/data/models/faq.dart] Faq::Faq.fromMap (0x7e86bc)
    //     0x7e8998: ldr             x1, [x1, #0x928]
    // 0x7e899c: r2 = Null
    //     0x7e899c: mov             x2, NULL
    // 0x7e89a0: r0 = AllocateClosure()
    //     0x7e89a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e89a4: mov             x1, x0
    // 0x7e89a8: ldur            x0, [fp, #-0x30]
    // 0x7e89ac: r2 = LoadClassIdInstr(r0)
    //     0x7e89ac: ldur            x2, [x0, #-1]
    //     0x7e89b0: ubfx            x2, x2, #0xc, #0x14
    // 0x7e89b4: r16 = <RelatedContent<int>>
    //     0x7e89b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e808] TypeArguments: <RelatedContent<int>>
    //     0x7e89b8: ldr             x16, [x16, #0x808]
    // 0x7e89bc: stp             x0, x16, [SP, #8]
    // 0x7e89c0: str             x1, [SP]
    // 0x7e89c4: mov             x0, x2
    // 0x7e89c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e89c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e89cc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7e89cc: movz            x17, #0xf28c
    //     0x7e89d0: add             lr, x0, x17
    //     0x7e89d4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e89d8: blr             lr
    // 0x7e89dc: r1 = LoadClassIdInstr(r0)
    //     0x7e89dc: ldur            x1, [x0, #-1]
    //     0x7e89e0: ubfx            x1, x1, #0xc, #0x14
    // 0x7e89e4: mov             x16, x0
    // 0x7e89e8: mov             x0, x1
    // 0x7e89ec: mov             x1, x16
    // 0x7e89f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7e89f0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7e89f4: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7e89f4: movz            x17, #0xd889
    //     0x7e89f8: add             lr, x0, x17
    //     0x7e89fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8a00: blr             lr
    // 0x7e8a04: mov             x3, x0
    // 0x7e8a08: ldur            x1, [fp, #-8]
    // 0x7e8a0c: stur            x3, [fp, #-0x30]
    // 0x7e8a10: r0 = LoadClassIdInstr(r1)
    //     0x7e8a10: ldur            x0, [x1, #-1]
    //     0x7e8a14: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8a18: r2 = "videos"
    //     0x7e8a18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0x7e8a1c: ldr             x2, [x2, #0x8d8]
    // 0x7e8a20: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8a20: sub             lr, x0, #0x114
    //     0x7e8a24: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8a28: blr             lr
    // 0x7e8a2c: mov             x3, x0
    // 0x7e8a30: r2 = Null
    //     0x7e8a30: mov             x2, NULL
    // 0x7e8a34: r1 = Null
    //     0x7e8a34: mov             x1, NULL
    // 0x7e8a38: stur            x3, [fp, #-8]
    // 0x7e8a3c: r4 = 60
    //     0x7e8a3c: movz            x4, #0x3c
    // 0x7e8a40: branchIfSmi(r0, 0x7e8a4c)
    //     0x7e8a40: tbz             w0, #0, #0x7e8a4c
    // 0x7e8a44: r4 = LoadClassIdInstr(r0)
    //     0x7e8a44: ldur            x4, [x0, #-1]
    //     0x7e8a48: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8a4c: sub             x4, x4, #0x5a
    // 0x7e8a50: cmp             x4, #2
    // 0x7e8a54: b.ls            #0x7e8a6c
    // 0x7e8a58: r8 = List?
    //     0x7e8a58: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7e8a5c: ldr             x8, [x8, #0x140]
    // 0x7e8a60: r3 = Null
    //     0x7e8a60: add             x3, PP, #0x34, lsl #12  ; [pp+0x34930] Null
    //     0x7e8a64: ldr             x3, [x3, #0x930]
    // 0x7e8a68: r0 = List?()
    //     0x7e8a68: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7e8a6c: ldur            x0, [fp, #-8]
    // 0x7e8a70: cmp             w0, NULL
    // 0x7e8a74: b.ne            #0x7e8a8c
    // 0x7e8a78: r1 = Null
    //     0x7e8a78: mov             x1, NULL
    // 0x7e8a7c: r2 = 0
    //     0x7e8a7c: movz            x2, #0
    // 0x7e8a80: r0 = _GrowableList()
    //     0x7e8a80: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e8a84: mov             x7, x0
    // 0x7e8a88: b               #0x7e8a90
    // 0x7e8a8c: mov             x7, x0
    // 0x7e8a90: ldur            x6, [fp, #-0x18]
    // 0x7e8a94: ldur            x5, [fp, #-0x10]
    // 0x7e8a98: ldur            x4, [fp, #-0x20]
    // 0x7e8a9c: ldur            x3, [fp, #-0x28]
    // 0x7e8aa0: ldur            x0, [fp, #-0x30]
    // 0x7e8aa4: stur            x7, [fp, #-8]
    // 0x7e8aa8: r1 = Function '<anonymous closure>': static.
    //     0x7e8aa8: add             x1, PP, #0x34, lsl #12  ; [pp+0x34940] AnonymousClosure: static (0x7e8d44), in [package:nuonline/app/data/models/faq.dart] Faq::Faq.fromMap (0x7e86bc)
    //     0x7e8aac: ldr             x1, [x1, #0x940]
    // 0x7e8ab0: r2 = Null
    //     0x7e8ab0: mov             x2, NULL
    // 0x7e8ab4: r0 = AllocateClosure()
    //     0x7e8ab4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e8ab8: mov             x1, x0
    // 0x7e8abc: ldur            x0, [fp, #-8]
    // 0x7e8ac0: r2 = LoadClassIdInstr(r0)
    //     0x7e8ac0: ldur            x2, [x0, #-1]
    //     0x7e8ac4: ubfx            x2, x2, #0xc, #0x14
    // 0x7e8ac8: r16 = <RelatedContent<String>>
    //     0x7e8ac8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e810] TypeArguments: <RelatedContent<String>>
    //     0x7e8acc: ldr             x16, [x16, #0x810]
    // 0x7e8ad0: stp             x0, x16, [SP, #8]
    // 0x7e8ad4: str             x1, [SP]
    // 0x7e8ad8: mov             x0, x2
    // 0x7e8adc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e8adc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e8ae0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7e8ae0: movz            x17, #0xf28c
    //     0x7e8ae4: add             lr, x0, x17
    //     0x7e8ae8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8aec: blr             lr
    // 0x7e8af0: r1 = LoadClassIdInstr(r0)
    //     0x7e8af0: ldur            x1, [x0, #-1]
    //     0x7e8af4: ubfx            x1, x1, #0xc, #0x14
    // 0x7e8af8: mov             x16, x0
    // 0x7e8afc: mov             x0, x1
    // 0x7e8b00: mov             x1, x16
    // 0x7e8b04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7e8b04: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7e8b08: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7e8b08: movz            x17, #0xd889
    //     0x7e8b0c: add             lr, x0, x17
    //     0x7e8b10: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8b14: blr             lr
    // 0x7e8b18: stur            x0, [fp, #-8]
    // 0x7e8b1c: r0 = Faq()
    //     0x7e8b1c: bl              #0x7e8b64  ; AllocateFaqStub -> Faq (size=0x24)
    // 0x7e8b20: ldur            x1, [fp, #-0x18]
    // 0x7e8b24: StoreField: r0->field_7 = r1
    //     0x7e8b24: stur            x1, [x0, #7]
    // 0x7e8b28: ldur            x1, [fp, #-0x10]
    // 0x7e8b2c: StoreField: r0->field_f = r1
    //     0x7e8b2c: stur            w1, [x0, #0xf]
    // 0x7e8b30: ldur            x1, [fp, #-0x20]
    // 0x7e8b34: StoreField: r0->field_13 = r1
    //     0x7e8b34: stur            w1, [x0, #0x13]
    // 0x7e8b38: ldur            x1, [fp, #-0x28]
    // 0x7e8b3c: ArrayStore: r0[0] = r1  ; List_4
    //     0x7e8b3c: stur            w1, [x0, #0x17]
    // 0x7e8b40: ldur            x1, [fp, #-0x30]
    // 0x7e8b44: StoreField: r0->field_1b = r1
    //     0x7e8b44: stur            w1, [x0, #0x1b]
    // 0x7e8b48: ldur            x1, [fp, #-8]
    // 0x7e8b4c: StoreField: r0->field_1f = r1
    //     0x7e8b4c: stur            w1, [x0, #0x1f]
    // 0x7e8b50: LeaveFrame
    //     0x7e8b50: mov             SP, fp
    //     0x7e8b54: ldp             fp, lr, [SP], #0x10
    // 0x7e8b58: ret
    //     0x7e8b58: ret             
    // 0x7e8b5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8b5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8b60: b               #0x7e86dc
  }
  [closure] static RelatedContent<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7e8d44, size: 0x58
    // 0x7e8d44: EnterFrame
    //     0x7e8d44: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8d48: mov             fp, SP
    // 0x7e8d4c: CheckStackOverflow
    //     0x7e8d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8d50: cmp             SP, x16
    //     0x7e8d54: b.ls            #0x7e8d94
    // 0x7e8d58: ldr             x0, [fp, #0x10]
    // 0x7e8d5c: r2 = Null
    //     0x7e8d5c: mov             x2, NULL
    // 0x7e8d60: r1 = Null
    //     0x7e8d60: mov             x1, NULL
    // 0x7e8d64: r8 = Map<String, dynamic>
    //     0x7e8d64: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e8d68: r3 = Null
    //     0x7e8d68: add             x3, PP, #0x34, lsl #12  ; [pp+0x34948] Null
    //     0x7e8d6c: ldr             x3, [x3, #0x948]
    // 0x7e8d70: r0 = Map<String, dynamic>()
    //     0x7e8d70: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e8d74: ldr             x2, [fp, #0x10]
    // 0x7e8d78: r1 = <String>
    //     0x7e8d78: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x7e8d7c: r3 = "youtube_id"
    //     0x7e8d7c: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x7e8d80: ldr             x3, [x3, #0x3f0]
    // 0x7e8d84: r0 = RelatedContent.fromMap()
    //     0x7e8d84: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x7e8d88: LeaveFrame
    //     0x7e8d88: mov             SP, fp
    //     0x7e8d8c: ldp             fp, lr, [SP], #0x10
    // 0x7e8d90: ret
    //     0x7e8d90: ret             
    // 0x7e8d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8d94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8d98: b               #0x7e8d58
  }
  [closure] static RelatedContent<int> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7e8d9c, size: 0x58
    // 0x7e8d9c: EnterFrame
    //     0x7e8d9c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8da0: mov             fp, SP
    // 0x7e8da4: CheckStackOverflow
    //     0x7e8da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8da8: cmp             SP, x16
    //     0x7e8dac: b.ls            #0x7e8dec
    // 0x7e8db0: ldr             x0, [fp, #0x10]
    // 0x7e8db4: r2 = Null
    //     0x7e8db4: mov             x2, NULL
    // 0x7e8db8: r1 = Null
    //     0x7e8db8: mov             x1, NULL
    // 0x7e8dbc: r8 = Map<String, dynamic>
    //     0x7e8dbc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e8dc0: r3 = Null
    //     0x7e8dc0: add             x3, PP, #0x34, lsl #12  ; [pp+0x34958] Null
    //     0x7e8dc4: ldr             x3, [x3, #0x958]
    // 0x7e8dc8: r0 = Map<String, dynamic>()
    //     0x7e8dc8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e8dcc: ldr             x2, [fp, #0x10]
    // 0x7e8dd0: r1 = <int>
    //     0x7e8dd0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x7e8dd4: r3 = "article_id"
    //     0x7e8dd4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0x7e8dd8: ldr             x3, [x3, #0x958]
    // 0x7e8ddc: r0 = RelatedContent.fromMap()
    //     0x7e8ddc: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x7e8de0: LeaveFrame
    //     0x7e8de0: mov             SP, fp
    //     0x7e8de4: ldp             fp, lr, [SP], #0x10
    // 0x7e8de8: ret
    //     0x7e8de8: ret             
    // 0x7e8dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8dec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8df0: b               #0x7e8db0
  }
}

// class id: 1144, size: 0x18, field offset: 0x8
class FaqCategory extends Object {

  factory _ FaqCategory.fromMap(/* No info */) {
    // ** addr: 0x7e8b70, size: 0x1c8
    // 0x7e8b70: EnterFrame
    //     0x7e8b70: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8b74: mov             fp, SP
    // 0x7e8b78: AllocStack(0x18)
    //     0x7e8b78: sub             SP, SP, #0x18
    // 0x7e8b7c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e8b7c: mov             x3, x2
    //     0x7e8b80: stur            x2, [fp, #-8]
    // 0x7e8b84: CheckStackOverflow
    //     0x7e8b84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8b88: cmp             SP, x16
    //     0x7e8b8c: b.ls            #0x7e8d30
    // 0x7e8b90: r0 = LoadClassIdInstr(r3)
    //     0x7e8b90: ldur            x0, [x3, #-1]
    //     0x7e8b94: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8b98: mov             x1, x3
    // 0x7e8b9c: r2 = "id"
    //     0x7e8b9c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7e8ba0: ldr             x2, [x2, #0x740]
    // 0x7e8ba4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8ba4: sub             lr, x0, #0x114
    //     0x7e8ba8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8bac: blr             lr
    // 0x7e8bb0: mov             x3, x0
    // 0x7e8bb4: r2 = Null
    //     0x7e8bb4: mov             x2, NULL
    // 0x7e8bb8: r1 = Null
    //     0x7e8bb8: mov             x1, NULL
    // 0x7e8bbc: stur            x3, [fp, #-0x10]
    // 0x7e8bc0: branchIfSmi(r0, 0x7e8be8)
    //     0x7e8bc0: tbz             w0, #0, #0x7e8be8
    // 0x7e8bc4: r4 = LoadClassIdInstr(r0)
    //     0x7e8bc4: ldur            x4, [x0, #-1]
    //     0x7e8bc8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8bcc: sub             x4, x4, #0x3c
    // 0x7e8bd0: cmp             x4, #1
    // 0x7e8bd4: b.ls            #0x7e8be8
    // 0x7e8bd8: r8 = int?
    //     0x7e8bd8: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e8bdc: r3 = Null
    //     0x7e8bdc: add             x3, PP, #0x34, lsl #12  ; [pp+0x34968] Null
    //     0x7e8be0: ldr             x3, [x3, #0x968]
    // 0x7e8be4: r0 = int?()
    //     0x7e8be4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e8be8: ldur            x0, [fp, #-0x10]
    // 0x7e8bec: cmp             w0, NULL
    // 0x7e8bf0: b.ne            #0x7e8bfc
    // 0x7e8bf4: r4 = 0
    //     0x7e8bf4: movz            x4, #0
    // 0x7e8bf8: b               #0x7e8c0c
    // 0x7e8bfc: r1 = LoadInt32Instr(r0)
    //     0x7e8bfc: sbfx            x1, x0, #1, #0x1f
    //     0x7e8c00: tbz             w0, #0, #0x7e8c08
    //     0x7e8c04: ldur            x1, [x0, #7]
    // 0x7e8c08: mov             x4, x1
    // 0x7e8c0c: ldur            x3, [fp, #-8]
    // 0x7e8c10: stur            x4, [fp, #-0x18]
    // 0x7e8c14: r0 = LoadClassIdInstr(r3)
    //     0x7e8c14: ldur            x0, [x3, #-1]
    //     0x7e8c18: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8c1c: mov             x1, x3
    // 0x7e8c20: r2 = "name"
    //     0x7e8c20: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x7e8c24: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8c24: sub             lr, x0, #0x114
    //     0x7e8c28: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8c2c: blr             lr
    // 0x7e8c30: mov             x3, x0
    // 0x7e8c34: r2 = Null
    //     0x7e8c34: mov             x2, NULL
    // 0x7e8c38: r1 = Null
    //     0x7e8c38: mov             x1, NULL
    // 0x7e8c3c: stur            x3, [fp, #-0x10]
    // 0x7e8c40: r4 = 60
    //     0x7e8c40: movz            x4, #0x3c
    // 0x7e8c44: branchIfSmi(r0, 0x7e8c50)
    //     0x7e8c44: tbz             w0, #0, #0x7e8c50
    // 0x7e8c48: r4 = LoadClassIdInstr(r0)
    //     0x7e8c48: ldur            x4, [x0, #-1]
    //     0x7e8c4c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8c50: sub             x4, x4, #0x5e
    // 0x7e8c54: cmp             x4, #1
    // 0x7e8c58: b.ls            #0x7e8c6c
    // 0x7e8c5c: r8 = String?
    //     0x7e8c5c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e8c60: r3 = Null
    //     0x7e8c60: add             x3, PP, #0x34, lsl #12  ; [pp+0x34978] Null
    //     0x7e8c64: ldr             x3, [x3, #0x978]
    // 0x7e8c68: r0 = String?()
    //     0x7e8c68: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e8c6c: ldur            x0, [fp, #-0x10]
    // 0x7e8c70: cmp             w0, NULL
    // 0x7e8c74: b.ne            #0x7e8c80
    // 0x7e8c78: r3 = ""
    //     0x7e8c78: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e8c7c: b               #0x7e8c84
    // 0x7e8c80: mov             x3, x0
    // 0x7e8c84: ldur            x1, [fp, #-8]
    // 0x7e8c88: stur            x3, [fp, #-0x10]
    // 0x7e8c8c: r0 = LoadClassIdInstr(r1)
    //     0x7e8c8c: ldur            x0, [x1, #-1]
    //     0x7e8c90: ubfx            x0, x0, #0xc, #0x14
    // 0x7e8c94: r2 = "slug"
    //     0x7e8c94: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x7e8c98: ldr             x2, [x2, #0x9a8]
    // 0x7e8c9c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e8c9c: sub             lr, x0, #0x114
    //     0x7e8ca0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8ca4: blr             lr
    // 0x7e8ca8: mov             x3, x0
    // 0x7e8cac: r2 = Null
    //     0x7e8cac: mov             x2, NULL
    // 0x7e8cb0: r1 = Null
    //     0x7e8cb0: mov             x1, NULL
    // 0x7e8cb4: stur            x3, [fp, #-8]
    // 0x7e8cb8: r4 = 60
    //     0x7e8cb8: movz            x4, #0x3c
    // 0x7e8cbc: branchIfSmi(r0, 0x7e8cc8)
    //     0x7e8cbc: tbz             w0, #0, #0x7e8cc8
    // 0x7e8cc0: r4 = LoadClassIdInstr(r0)
    //     0x7e8cc0: ldur            x4, [x0, #-1]
    //     0x7e8cc4: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8cc8: sub             x4, x4, #0x5e
    // 0x7e8ccc: cmp             x4, #1
    // 0x7e8cd0: b.ls            #0x7e8ce4
    // 0x7e8cd4: r8 = String?
    //     0x7e8cd4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e8cd8: r3 = Null
    //     0x7e8cd8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34988] Null
    //     0x7e8cdc: ldr             x3, [x3, #0x988]
    // 0x7e8ce0: r0 = String?()
    //     0x7e8ce0: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e8ce4: ldur            x0, [fp, #-8]
    // 0x7e8ce8: cmp             w0, NULL
    // 0x7e8cec: b.ne            #0x7e8cf8
    // 0x7e8cf0: r2 = ""
    //     0x7e8cf0: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e8cf4: b               #0x7e8cfc
    // 0x7e8cf8: mov             x2, x0
    // 0x7e8cfc: ldur            x1, [fp, #-0x18]
    // 0x7e8d00: ldur            x0, [fp, #-0x10]
    // 0x7e8d04: stur            x2, [fp, #-8]
    // 0x7e8d08: r0 = FaqCategory()
    //     0x7e8d08: bl              #0x7e8d38  ; AllocateFaqCategoryStub -> FaqCategory (size=0x18)
    // 0x7e8d0c: ldur            x1, [fp, #-0x18]
    // 0x7e8d10: StoreField: r0->field_7 = r1
    //     0x7e8d10: stur            x1, [x0, #7]
    // 0x7e8d14: ldur            x1, [fp, #-0x10]
    // 0x7e8d18: StoreField: r0->field_f = r1
    //     0x7e8d18: stur            w1, [x0, #0xf]
    // 0x7e8d1c: ldur            x1, [fp, #-8]
    // 0x7e8d20: StoreField: r0->field_13 = r1
    //     0x7e8d20: stur            w1, [x0, #0x13]
    // 0x7e8d24: LeaveFrame
    //     0x7e8d24: mov             SP, fp
    //     0x7e8d28: ldp             fp, lr, [SP], #0x10
    // 0x7e8d2c: ret
    //     0x7e8d2c: ret             
    // 0x7e8d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e8d30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e8d34: b               #0x7e8b90
  }
}
