// lib: , url: package:nuonline/app/data/models/haji.dart

// class id: 1050023, size: 0x8
class :: {
}

// class id: 5584, size: 0x2c, field offset: 0x8
//   const constructor, 
class Haji extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x8fc1ac, size: 0x1b4
    // 0x8fc1ac: EnterFrame
    //     0x8fc1ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc1b0: mov             fp, SP
    // 0x8fc1b4: AllocStack(0x20)
    //     0x8fc1b4: sub             SP, SP, #0x20
    // 0x8fc1b8: CheckStackOverflow
    //     0x8fc1b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc1bc: cmp             SP, x16
    //     0x8fc1c0: b.ls            #0x8fc358
    // 0x8fc1c4: LoadField: r3 = r1->field_b
    //     0x8fc1c4: ldur            w3, [x1, #0xb]
    // 0x8fc1c8: DecompressPointer r3
    //     0x8fc1c8: add             x3, x3, HEAP, lsl #32
    // 0x8fc1cc: mov             x0, x3
    // 0x8fc1d0: stur            x3, [fp, #-8]
    // 0x8fc1d4: r2 = Null
    //     0x8fc1d4: mov             x2, NULL
    // 0x8fc1d8: r1 = Null
    //     0x8fc1d8: mov             x1, NULL
    // 0x8fc1dc: cmp             w0, NULL
    // 0x8fc1e0: b.eq            #0x8fc284
    // 0x8fc1e4: branchIfSmi(r0, 0x8fc284)
    //     0x8fc1e4: tbz             w0, #0, #0x8fc284
    // 0x8fc1e8: r3 = LoadClassIdInstr(r0)
    //     0x8fc1e8: ldur            x3, [x0, #-1]
    //     0x8fc1ec: ubfx            x3, x3, #0xc, #0x14
    // 0x8fc1f0: r17 = 6718
    //     0x8fc1f0: movz            x17, #0x1a3e
    // 0x8fc1f4: cmp             x3, x17
    // 0x8fc1f8: b.eq            #0x8fc28c
    // 0x8fc1fc: sub             x3, x3, #0x5a
    // 0x8fc200: cmp             x3, #2
    // 0x8fc204: b.ls            #0x8fc28c
    // 0x8fc208: r4 = LoadClassIdInstr(r0)
    //     0x8fc208: ldur            x4, [x0, #-1]
    //     0x8fc20c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc210: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8fc214: ldr             x3, [x3, #0x18]
    // 0x8fc218: ldr             x3, [x3, x4, lsl #3]
    // 0x8fc21c: LoadField: r3 = r3->field_2b
    //     0x8fc21c: ldur            w3, [x3, #0x2b]
    // 0x8fc220: DecompressPointer r3
    //     0x8fc220: add             x3, x3, HEAP, lsl #32
    // 0x8fc224: cmp             w3, NULL
    // 0x8fc228: b.eq            #0x8fc284
    // 0x8fc22c: LoadField: r3 = r3->field_f
    //     0x8fc22c: ldur            w3, [x3, #0xf]
    // 0x8fc230: lsr             x3, x3, #3
    // 0x8fc234: r17 = 6718
    //     0x8fc234: movz            x17, #0x1a3e
    // 0x8fc238: cmp             x3, x17
    // 0x8fc23c: b.eq            #0x8fc28c
    // 0x8fc240: r3 = SubtypeTestCache
    //     0x8fc240: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fdb0] SubtypeTestCache
    //     0x8fc244: ldr             x3, [x3, #0xdb0]
    // 0x8fc248: r30 = Subtype1TestCacheStub
    //     0x8fc248: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8fc24c: LoadField: r30 = r30->field_7
    //     0x8fc24c: ldur            lr, [lr, #7]
    // 0x8fc250: blr             lr
    // 0x8fc254: cmp             w7, NULL
    // 0x8fc258: b.eq            #0x8fc264
    // 0x8fc25c: tbnz            w7, #4, #0x8fc284
    // 0x8fc260: b               #0x8fc28c
    // 0x8fc264: r8 = List
    //     0x8fc264: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3fdb8] Type: List
    //     0x8fc268: ldr             x8, [x8, #0xdb8]
    // 0x8fc26c: r3 = SubtypeTestCache
    //     0x8fc26c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] SubtypeTestCache
    //     0x8fc270: ldr             x3, [x3, #0xdc0]
    // 0x8fc274: r30 = InstanceOfStub
    //     0x8fc274: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8fc278: LoadField: r30 = r30->field_7
    //     0x8fc278: ldur            lr, [lr, #7]
    // 0x8fc27c: blr             lr
    // 0x8fc280: b               #0x8fc290
    // 0x8fc284: r0 = false
    //     0x8fc284: add             x0, NULL, #0x30  ; false
    // 0x8fc288: b               #0x8fc290
    // 0x8fc28c: r0 = true
    //     0x8fc28c: add             x0, NULL, #0x20  ; true
    // 0x8fc290: tbnz            w0, #4, #0x8fc33c
    // 0x8fc294: ldur            x0, [fp, #-8]
    // 0x8fc298: r1 = Function '<anonymous closure>': static.
    //     0x8fc298: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fdc8] AnonymousClosure: static (0x8fc3d8), in [package:nuonline/app/data/models/haji.dart] Haji::fromResponse (0x8fc1ac)
    //     0x8fc29c: ldr             x1, [x1, #0xdc8]
    // 0x8fc2a0: r2 = Null
    //     0x8fc2a0: mov             x2, NULL
    // 0x8fc2a4: r0 = AllocateClosure()
    //     0x8fc2a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fc2a8: mov             x1, x0
    // 0x8fc2ac: ldur            x0, [fp, #-8]
    // 0x8fc2b0: r2 = LoadClassIdInstr(r0)
    //     0x8fc2b0: ldur            x2, [x0, #-1]
    //     0x8fc2b4: ubfx            x2, x2, #0xc, #0x14
    // 0x8fc2b8: r16 = <Haji>
    //     0x8fc2b8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fdd0] TypeArguments: <Haji>
    //     0x8fc2bc: ldr             x16, [x16, #0xdd0]
    // 0x8fc2c0: stp             x0, x16, [SP, #8]
    // 0x8fc2c4: str             x1, [SP]
    // 0x8fc2c8: mov             x0, x2
    // 0x8fc2cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc2cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc2d0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fc2d0: movz            x17, #0xf28c
    //     0x8fc2d4: add             lr, x0, x17
    //     0x8fc2d8: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc2dc: blr             lr
    // 0x8fc2e0: r1 = LoadClassIdInstr(r0)
    //     0x8fc2e0: ldur            x1, [x0, #-1]
    //     0x8fc2e4: ubfx            x1, x1, #0xc, #0x14
    // 0x8fc2e8: mov             x16, x0
    // 0x8fc2ec: mov             x0, x1
    // 0x8fc2f0: mov             x1, x16
    // 0x8fc2f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fc2f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fc2f8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fc2f8: movz            x17, #0xd889
    //     0x8fc2fc: add             lr, x0, x17
    //     0x8fc300: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc304: blr             lr
    // 0x8fc308: r1 = Function '<anonymous closure>': static.
    //     0x8fc308: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fdd8] AnonymousClosure: static (0x8fc360), in [package:nuonline/app/data/models/haji.dart] Haji::fromResponse (0x8fc1ac)
    //     0x8fc30c: ldr             x1, [x1, #0xdd8]
    // 0x8fc310: r2 = Null
    //     0x8fc310: mov             x2, NULL
    // 0x8fc314: stur            x0, [fp, #-8]
    // 0x8fc318: r0 = AllocateClosure()
    //     0x8fc318: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fc31c: str             x0, [SP]
    // 0x8fc320: ldur            x1, [fp, #-8]
    // 0x8fc324: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8fc324: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8fc328: r0 = sort()
    //     0x8fc328: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x8fc32c: ldur            x0, [fp, #-8]
    // 0x8fc330: LeaveFrame
    //     0x8fc330: mov             SP, fp
    //     0x8fc334: ldp             fp, lr, [SP], #0x10
    // 0x8fc338: ret
    //     0x8fc338: ret             
    // 0x8fc33c: r1 = <Haji>
    //     0x8fc33c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fdd0] TypeArguments: <Haji>
    //     0x8fc340: ldr             x1, [x1, #0xdd0]
    // 0x8fc344: r2 = 0
    //     0x8fc344: movz            x2, #0
    // 0x8fc348: r0 = _GrowableList()
    //     0x8fc348: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fc34c: LeaveFrame
    //     0x8fc34c: mov             SP, fp
    //     0x8fc350: ldp             fp, lr, [SP], #0x10
    // 0x8fc354: ret
    //     0x8fc354: ret             
    // 0x8fc358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc35c: b               #0x8fc1c4
  }
  [closure] static int <anonymous closure>(dynamic, Haji, Haji) {
    // ** addr: 0x8fc360, size: 0x78
    // 0x8fc360: EnterFrame
    //     0x8fc360: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc364: mov             fp, SP
    // 0x8fc368: CheckStackOverflow
    //     0x8fc368: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc36c: cmp             SP, x16
    //     0x8fc370: b.ls            #0x8fc3d0
    // 0x8fc374: ldr             x0, [fp, #0x18]
    // 0x8fc378: LoadField: r2 = r0->field_f
    //     0x8fc378: ldur            x2, [x0, #0xf]
    // 0x8fc37c: ldr             x0, [fp, #0x10]
    // 0x8fc380: LoadField: r3 = r0->field_f
    //     0x8fc380: ldur            x3, [x0, #0xf]
    // 0x8fc384: r0 = BoxInt64Instr(r2)
    //     0x8fc384: sbfiz           x0, x2, #1, #0x1f
    //     0x8fc388: cmp             x2, x0, asr #1
    //     0x8fc38c: b.eq            #0x8fc398
    //     0x8fc390: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fc394: stur            x2, [x0, #7]
    // 0x8fc398: mov             x2, x0
    // 0x8fc39c: r0 = BoxInt64Instr(r3)
    //     0x8fc39c: sbfiz           x0, x3, #1, #0x1f
    //     0x8fc3a0: cmp             x3, x0, asr #1
    //     0x8fc3a4: b.eq            #0x8fc3b0
    //     0x8fc3a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fc3ac: stur            x3, [x0, #7]
    // 0x8fc3b0: mov             x1, x2
    // 0x8fc3b4: mov             x2, x0
    // 0x8fc3b8: r0 = compareTo()
    //     0x8fc3b8: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0x8fc3bc: lsl             x1, x0, #1
    // 0x8fc3c0: mov             x0, x1
    // 0x8fc3c4: LeaveFrame
    //     0x8fc3c4: mov             SP, fp
    //     0x8fc3c8: ldp             fp, lr, [SP], #0x10
    // 0x8fc3cc: ret
    //     0x8fc3cc: ret             
    // 0x8fc3d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc3d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc3d4: b               #0x8fc374
  }
  [closure] static Haji <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fc3d8, size: 0x50
    // 0x8fc3d8: EnterFrame
    //     0x8fc3d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc3dc: mov             fp, SP
    // 0x8fc3e0: CheckStackOverflow
    //     0x8fc3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc3e4: cmp             SP, x16
    //     0x8fc3e8: b.ls            #0x8fc420
    // 0x8fc3ec: ldr             x0, [fp, #0x10]
    // 0x8fc3f0: r2 = Null
    //     0x8fc3f0: mov             x2, NULL
    // 0x8fc3f4: r1 = Null
    //     0x8fc3f4: mov             x1, NULL
    // 0x8fc3f8: r8 = Map<String, dynamic>
    //     0x8fc3f8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fc3fc: r3 = Null
    //     0x8fc3fc: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fde0] Null
    //     0x8fc400: ldr             x3, [x3, #0xde0]
    // 0x8fc404: r0 = Map<String, dynamic>()
    //     0x8fc404: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fc408: ldr             x2, [fp, #0x10]
    // 0x8fc40c: r1 = Null
    //     0x8fc40c: mov             x1, NULL
    // 0x8fc410: r0 = Haji.fromMap()
    //     0x8fc410: bl              #0x8fc428  ; [package:nuonline/app/data/models/haji.dart] Haji::Haji.fromMap
    // 0x8fc414: LeaveFrame
    //     0x8fc414: mov             SP, fp
    //     0x8fc418: ldp             fp, lr, [SP], #0x10
    // 0x8fc41c: ret
    //     0x8fc41c: ret             
    // 0x8fc420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc424: b               #0x8fc3ec
  }
  factory _ Haji.fromMap(/* No info */) {
    // ** addr: 0x8fc428, size: 0x4dc
    // 0x8fc428: EnterFrame
    //     0x8fc428: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc42c: mov             fp, SP
    // 0x8fc430: AllocStack(0x58)
    //     0x8fc430: sub             SP, SP, #0x58
    // 0x8fc434: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8fc434: mov             x3, x2
    //     0x8fc438: stur            x2, [fp, #-8]
    // 0x8fc43c: CheckStackOverflow
    //     0x8fc43c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc440: cmp             SP, x16
    //     0x8fc444: b.ls            #0x8fc8fc
    // 0x8fc448: r0 = LoadClassIdInstr(r3)
    //     0x8fc448: ldur            x0, [x3, #-1]
    //     0x8fc44c: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc450: mov             x1, x3
    // 0x8fc454: r2 = "id"
    //     0x8fc454: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fc458: ldr             x2, [x2, #0x740]
    // 0x8fc45c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc45c: sub             lr, x0, #0x114
    //     0x8fc460: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc464: blr             lr
    // 0x8fc468: mov             x3, x0
    // 0x8fc46c: r2 = Null
    //     0x8fc46c: mov             x2, NULL
    // 0x8fc470: r1 = Null
    //     0x8fc470: mov             x1, NULL
    // 0x8fc474: stur            x3, [fp, #-0x10]
    // 0x8fc478: branchIfSmi(r0, 0x8fc4a0)
    //     0x8fc478: tbz             w0, #0, #0x8fc4a0
    // 0x8fc47c: r4 = LoadClassIdInstr(r0)
    //     0x8fc47c: ldur            x4, [x0, #-1]
    //     0x8fc480: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc484: sub             x4, x4, #0x3c
    // 0x8fc488: cmp             x4, #1
    // 0x8fc48c: b.ls            #0x8fc4a0
    // 0x8fc490: r8 = int
    //     0x8fc490: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fc494: r3 = Null
    //     0x8fc494: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fdf0] Null
    //     0x8fc498: ldr             x3, [x3, #0xdf0]
    // 0x8fc49c: r0 = int()
    //     0x8fc49c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fc4a0: ldur            x3, [fp, #-8]
    // 0x8fc4a4: r0 = LoadClassIdInstr(r3)
    //     0x8fc4a4: ldur            x0, [x3, #-1]
    //     0x8fc4a8: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc4ac: mov             x1, x3
    // 0x8fc4b0: r2 = "order"
    //     0x8fc4b0: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x8fc4b4: ldr             x2, [x2, #0xb78]
    // 0x8fc4b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc4b8: sub             lr, x0, #0x114
    //     0x8fc4bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc4c0: blr             lr
    // 0x8fc4c4: mov             x3, x0
    // 0x8fc4c8: r2 = Null
    //     0x8fc4c8: mov             x2, NULL
    // 0x8fc4cc: r1 = Null
    //     0x8fc4cc: mov             x1, NULL
    // 0x8fc4d0: stur            x3, [fp, #-0x18]
    // 0x8fc4d4: branchIfSmi(r0, 0x8fc4fc)
    //     0x8fc4d4: tbz             w0, #0, #0x8fc4fc
    // 0x8fc4d8: r4 = LoadClassIdInstr(r0)
    //     0x8fc4d8: ldur            x4, [x0, #-1]
    //     0x8fc4dc: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc4e0: sub             x4, x4, #0x3c
    // 0x8fc4e4: cmp             x4, #1
    // 0x8fc4e8: b.ls            #0x8fc4fc
    // 0x8fc4ec: r8 = int
    //     0x8fc4ec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fc4f0: r3 = Null
    //     0x8fc4f0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe00] Null
    //     0x8fc4f4: ldr             x3, [x3, #0xe00]
    // 0x8fc4f8: r0 = int()
    //     0x8fc4f8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fc4fc: ldur            x3, [fp, #-8]
    // 0x8fc500: r0 = LoadClassIdInstr(r3)
    //     0x8fc500: ldur            x0, [x3, #-1]
    //     0x8fc504: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc508: mov             x1, x3
    // 0x8fc50c: r2 = "description"
    //     0x8fc50c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x8fc510: ldr             x2, [x2, #0xd28]
    // 0x8fc514: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc514: sub             lr, x0, #0x114
    //     0x8fc518: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc51c: blr             lr
    // 0x8fc520: mov             x3, x0
    // 0x8fc524: r2 = Null
    //     0x8fc524: mov             x2, NULL
    // 0x8fc528: r1 = Null
    //     0x8fc528: mov             x1, NULL
    // 0x8fc52c: stur            x3, [fp, #-0x20]
    // 0x8fc530: r4 = 60
    //     0x8fc530: movz            x4, #0x3c
    // 0x8fc534: branchIfSmi(r0, 0x8fc540)
    //     0x8fc534: tbz             w0, #0, #0x8fc540
    // 0x8fc538: r4 = LoadClassIdInstr(r0)
    //     0x8fc538: ldur            x4, [x0, #-1]
    //     0x8fc53c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc540: sub             x4, x4, #0x5e
    // 0x8fc544: cmp             x4, #1
    // 0x8fc548: b.ls            #0x8fc55c
    // 0x8fc54c: r8 = String
    //     0x8fc54c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fc550: r3 = Null
    //     0x8fc550: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe10] Null
    //     0x8fc554: ldr             x3, [x3, #0xe10]
    // 0x8fc558: r0 = String()
    //     0x8fc558: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fc55c: ldur            x3, [fp, #-8]
    // 0x8fc560: r0 = LoadClassIdInstr(r3)
    //     0x8fc560: ldur            x0, [x3, #-1]
    //     0x8fc564: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc568: mov             x1, x3
    // 0x8fc56c: r2 = "schedule"
    //     0x8fc56c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] "schedule"
    //     0x8fc570: ldr             x2, [x2, #0xa18]
    // 0x8fc574: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc574: sub             lr, x0, #0x114
    //     0x8fc578: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc57c: blr             lr
    // 0x8fc580: mov             x3, x0
    // 0x8fc584: r2 = Null
    //     0x8fc584: mov             x2, NULL
    // 0x8fc588: r1 = Null
    //     0x8fc588: mov             x1, NULL
    // 0x8fc58c: stur            x3, [fp, #-0x28]
    // 0x8fc590: r4 = 60
    //     0x8fc590: movz            x4, #0x3c
    // 0x8fc594: branchIfSmi(r0, 0x8fc5a0)
    //     0x8fc594: tbz             w0, #0, #0x8fc5a0
    // 0x8fc598: r4 = LoadClassIdInstr(r0)
    //     0x8fc598: ldur            x4, [x0, #-1]
    //     0x8fc59c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc5a0: sub             x4, x4, #0x5e
    // 0x8fc5a4: cmp             x4, #1
    // 0x8fc5a8: b.ls            #0x8fc5bc
    // 0x8fc5ac: r8 = String
    //     0x8fc5ac: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fc5b0: r3 = Null
    //     0x8fc5b0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe20] Null
    //     0x8fc5b4: ldr             x3, [x3, #0xe20]
    // 0x8fc5b8: r0 = String()
    //     0x8fc5b8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fc5bc: ldur            x3, [fp, #-8]
    // 0x8fc5c0: r0 = LoadClassIdInstr(r3)
    //     0x8fc5c0: ldur            x0, [x3, #-1]
    //     0x8fc5c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc5c8: mov             x1, x3
    // 0x8fc5cc: r2 = "content"
    //     0x8fc5cc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x8fc5d0: ldr             x2, [x2, #0xf58]
    // 0x8fc5d4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc5d4: sub             lr, x0, #0x114
    //     0x8fc5d8: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc5dc: blr             lr
    // 0x8fc5e0: mov             x3, x0
    // 0x8fc5e4: r2 = Null
    //     0x8fc5e4: mov             x2, NULL
    // 0x8fc5e8: r1 = Null
    //     0x8fc5e8: mov             x1, NULL
    // 0x8fc5ec: stur            x3, [fp, #-0x30]
    // 0x8fc5f0: r4 = 60
    //     0x8fc5f0: movz            x4, #0x3c
    // 0x8fc5f4: branchIfSmi(r0, 0x8fc600)
    //     0x8fc5f4: tbz             w0, #0, #0x8fc600
    // 0x8fc5f8: r4 = LoadClassIdInstr(r0)
    //     0x8fc5f8: ldur            x4, [x0, #-1]
    //     0x8fc5fc: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc600: sub             x4, x4, #0x5e
    // 0x8fc604: cmp             x4, #1
    // 0x8fc608: b.ls            #0x8fc61c
    // 0x8fc60c: r8 = String
    //     0x8fc60c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fc610: r3 = Null
    //     0x8fc610: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe30] Null
    //     0x8fc614: ldr             x3, [x3, #0xe30]
    // 0x8fc618: r0 = String()
    //     0x8fc618: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fc61c: ldur            x3, [fp, #-8]
    // 0x8fc620: r0 = LoadClassIdInstr(r3)
    //     0x8fc620: ldur            x0, [x3, #-1]
    //     0x8fc624: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc628: mov             x1, x3
    // 0x8fc62c: r2 = "updated_at"
    //     0x8fc62c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x8fc630: ldr             x2, [x2, #0xe88]
    // 0x8fc634: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc634: sub             lr, x0, #0x114
    //     0x8fc638: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc63c: blr             lr
    // 0x8fc640: r2 = Null
    //     0x8fc640: mov             x2, NULL
    // 0x8fc644: r1 = Null
    //     0x8fc644: mov             x1, NULL
    // 0x8fc648: r4 = 60
    //     0x8fc648: movz            x4, #0x3c
    // 0x8fc64c: branchIfSmi(r0, 0x8fc658)
    //     0x8fc64c: tbz             w0, #0, #0x8fc658
    // 0x8fc650: r4 = LoadClassIdInstr(r0)
    //     0x8fc650: ldur            x4, [x0, #-1]
    //     0x8fc654: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc658: sub             x4, x4, #0x5e
    // 0x8fc65c: cmp             x4, #1
    // 0x8fc660: b.ls            #0x8fc674
    // 0x8fc664: r8 = String
    //     0x8fc664: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fc668: r3 = Null
    //     0x8fc668: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe40] Null
    //     0x8fc66c: ldr             x3, [x3, #0xe40]
    // 0x8fc670: r0 = String()
    //     0x8fc670: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fc674: ldur            x3, [fp, #-8]
    // 0x8fc678: r0 = LoadClassIdInstr(r3)
    //     0x8fc678: ldur            x0, [x3, #-1]
    //     0x8fc67c: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc680: mov             x1, x3
    // 0x8fc684: r2 = "articles"
    //     0x8fc684: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0x8fc688: ldr             x2, [x2, #0x8b8]
    // 0x8fc68c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc68c: sub             lr, x0, #0x114
    //     0x8fc690: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc694: blr             lr
    // 0x8fc698: mov             x3, x0
    // 0x8fc69c: r2 = Null
    //     0x8fc69c: mov             x2, NULL
    // 0x8fc6a0: r1 = Null
    //     0x8fc6a0: mov             x1, NULL
    // 0x8fc6a4: stur            x3, [fp, #-0x38]
    // 0x8fc6a8: r4 = 60
    //     0x8fc6a8: movz            x4, #0x3c
    // 0x8fc6ac: branchIfSmi(r0, 0x8fc6b8)
    //     0x8fc6ac: tbz             w0, #0, #0x8fc6b8
    // 0x8fc6b0: r4 = LoadClassIdInstr(r0)
    //     0x8fc6b0: ldur            x4, [x0, #-1]
    //     0x8fc6b4: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc6b8: sub             x4, x4, #0x5a
    // 0x8fc6bc: cmp             x4, #2
    // 0x8fc6c0: b.ls            #0x8fc6d8
    // 0x8fc6c4: r8 = List?
    //     0x8fc6c4: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8fc6c8: ldr             x8, [x8, #0x140]
    // 0x8fc6cc: r3 = Null
    //     0x8fc6cc: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe50] Null
    //     0x8fc6d0: ldr             x3, [x3, #0xe50]
    // 0x8fc6d4: r0 = List?()
    //     0x8fc6d4: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8fc6d8: ldur            x0, [fp, #-0x38]
    // 0x8fc6dc: cmp             w0, NULL
    // 0x8fc6e0: b.ne            #0x8fc6f8
    // 0x8fc6e4: r1 = Null
    //     0x8fc6e4: mov             x1, NULL
    // 0x8fc6e8: r2 = 0
    //     0x8fc6e8: movz            x2, #0
    // 0x8fc6ec: r0 = _GrowableList()
    //     0x8fc6ec: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fc6f0: mov             x3, x0
    // 0x8fc6f4: b               #0x8fc6fc
    // 0x8fc6f8: mov             x3, x0
    // 0x8fc6fc: ldur            x0, [fp, #-8]
    // 0x8fc700: stur            x3, [fp, #-0x38]
    // 0x8fc704: r1 = Function '<anonymous closure>': static.
    //     0x8fc704: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe60] AnonymousClosure: static (0x8fc968), in [package:nuonline/app/data/models/haji.dart] Haji::Haji.fromMap (0x8fc428)
    //     0x8fc708: ldr             x1, [x1, #0xe60]
    // 0x8fc70c: r2 = Null
    //     0x8fc70c: mov             x2, NULL
    // 0x8fc710: r0 = AllocateClosure()
    //     0x8fc710: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fc714: mov             x1, x0
    // 0x8fc718: ldur            x0, [fp, #-0x38]
    // 0x8fc71c: r2 = LoadClassIdInstr(r0)
    //     0x8fc71c: ldur            x2, [x0, #-1]
    //     0x8fc720: ubfx            x2, x2, #0xc, #0x14
    // 0x8fc724: r16 = <RelatedContent<int>>
    //     0x8fc724: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e808] TypeArguments: <RelatedContent<int>>
    //     0x8fc728: ldr             x16, [x16, #0x808]
    // 0x8fc72c: stp             x0, x16, [SP, #8]
    // 0x8fc730: str             x1, [SP]
    // 0x8fc734: mov             x0, x2
    // 0x8fc738: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc738: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc73c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fc73c: movz            x17, #0xf28c
    //     0x8fc740: add             lr, x0, x17
    //     0x8fc744: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc748: blr             lr
    // 0x8fc74c: r1 = LoadClassIdInstr(r0)
    //     0x8fc74c: ldur            x1, [x0, #-1]
    //     0x8fc750: ubfx            x1, x1, #0xc, #0x14
    // 0x8fc754: mov             x16, x0
    // 0x8fc758: mov             x0, x1
    // 0x8fc75c: mov             x1, x16
    // 0x8fc760: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fc760: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fc764: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fc764: movz            x17, #0xd889
    //     0x8fc768: add             lr, x0, x17
    //     0x8fc76c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc770: blr             lr
    // 0x8fc774: mov             x3, x0
    // 0x8fc778: ldur            x1, [fp, #-8]
    // 0x8fc77c: stur            x3, [fp, #-0x38]
    // 0x8fc780: r0 = LoadClassIdInstr(r1)
    //     0x8fc780: ldur            x0, [x1, #-1]
    //     0x8fc784: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc788: r2 = "videos"
    //     0x8fc788: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0x8fc78c: ldr             x2, [x2, #0x8d8]
    // 0x8fc790: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fc790: sub             lr, x0, #0x114
    //     0x8fc794: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc798: blr             lr
    // 0x8fc79c: mov             x3, x0
    // 0x8fc7a0: r2 = Null
    //     0x8fc7a0: mov             x2, NULL
    // 0x8fc7a4: r1 = Null
    //     0x8fc7a4: mov             x1, NULL
    // 0x8fc7a8: stur            x3, [fp, #-8]
    // 0x8fc7ac: r4 = 60
    //     0x8fc7ac: movz            x4, #0x3c
    // 0x8fc7b0: branchIfSmi(r0, 0x8fc7bc)
    //     0x8fc7b0: tbz             w0, #0, #0x8fc7bc
    // 0x8fc7b4: r4 = LoadClassIdInstr(r0)
    //     0x8fc7b4: ldur            x4, [x0, #-1]
    //     0x8fc7b8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc7bc: sub             x4, x4, #0x5a
    // 0x8fc7c0: cmp             x4, #2
    // 0x8fc7c4: b.ls            #0x8fc7dc
    // 0x8fc7c8: r8 = List?
    //     0x8fc7c8: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8fc7cc: ldr             x8, [x8, #0x140]
    // 0x8fc7d0: r3 = Null
    //     0x8fc7d0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe68] Null
    //     0x8fc7d4: ldr             x3, [x3, #0xe68]
    // 0x8fc7d8: r0 = List?()
    //     0x8fc7d8: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8fc7dc: ldur            x0, [fp, #-8]
    // 0x8fc7e0: cmp             w0, NULL
    // 0x8fc7e4: b.ne            #0x8fc7fc
    // 0x8fc7e8: r1 = Null
    //     0x8fc7e8: mov             x1, NULL
    // 0x8fc7ec: r2 = 0
    //     0x8fc7ec: movz            x2, #0
    // 0x8fc7f0: r0 = _GrowableList()
    //     0x8fc7f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fc7f4: mov             x8, x0
    // 0x8fc7f8: b               #0x8fc800
    // 0x8fc7fc: mov             x8, x0
    // 0x8fc800: ldur            x7, [fp, #-0x10]
    // 0x8fc804: ldur            x6, [fp, #-0x18]
    // 0x8fc808: ldur            x5, [fp, #-0x20]
    // 0x8fc80c: ldur            x4, [fp, #-0x28]
    // 0x8fc810: ldur            x3, [fp, #-0x30]
    // 0x8fc814: ldur            x0, [fp, #-0x38]
    // 0x8fc818: stur            x8, [fp, #-8]
    // 0x8fc81c: r1 = Function '<anonymous closure>': static.
    //     0x8fc81c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe78] AnonymousClosure: static (0x8fc910), in [package:nuonline/app/data/models/haji.dart] Haji::Haji.fromMap (0x8fc428)
    //     0x8fc820: ldr             x1, [x1, #0xe78]
    // 0x8fc824: r2 = Null
    //     0x8fc824: mov             x2, NULL
    // 0x8fc828: r0 = AllocateClosure()
    //     0x8fc828: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fc82c: mov             x1, x0
    // 0x8fc830: ldur            x0, [fp, #-8]
    // 0x8fc834: r2 = LoadClassIdInstr(r0)
    //     0x8fc834: ldur            x2, [x0, #-1]
    //     0x8fc838: ubfx            x2, x2, #0xc, #0x14
    // 0x8fc83c: r16 = <RelatedContent<String>>
    //     0x8fc83c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e810] TypeArguments: <RelatedContent<String>>
    //     0x8fc840: ldr             x16, [x16, #0x810]
    // 0x8fc844: stp             x0, x16, [SP, #8]
    // 0x8fc848: str             x1, [SP]
    // 0x8fc84c: mov             x0, x2
    // 0x8fc850: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc850: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc854: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fc854: movz            x17, #0xf28c
    //     0x8fc858: add             lr, x0, x17
    //     0x8fc85c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc860: blr             lr
    // 0x8fc864: r1 = LoadClassIdInstr(r0)
    //     0x8fc864: ldur            x1, [x0, #-1]
    //     0x8fc868: ubfx            x1, x1, #0xc, #0x14
    // 0x8fc86c: mov             x16, x0
    // 0x8fc870: mov             x0, x1
    // 0x8fc874: mov             x1, x16
    // 0x8fc878: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fc878: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fc87c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fc87c: movz            x17, #0xd889
    //     0x8fc880: add             lr, x0, x17
    //     0x8fc884: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc888: blr             lr
    // 0x8fc88c: mov             x1, x0
    // 0x8fc890: ldur            x0, [fp, #-0x10]
    // 0x8fc894: stur            x1, [fp, #-8]
    // 0x8fc898: r2 = LoadInt32Instr(r0)
    //     0x8fc898: sbfx            x2, x0, #1, #0x1f
    //     0x8fc89c: tbz             w0, #0, #0x8fc8a4
    //     0x8fc8a0: ldur            x2, [x0, #7]
    // 0x8fc8a4: stur            x2, [fp, #-0x40]
    // 0x8fc8a8: r0 = Haji()
    //     0x8fc8a8: bl              #0x8fc904  ; AllocateHajiStub -> Haji (size=0x2c)
    // 0x8fc8ac: ldur            x1, [fp, #-0x40]
    // 0x8fc8b0: StoreField: r0->field_7 = r1
    //     0x8fc8b0: stur            x1, [x0, #7]
    // 0x8fc8b4: ldur            x1, [fp, #-0x18]
    // 0x8fc8b8: r2 = LoadInt32Instr(r1)
    //     0x8fc8b8: sbfx            x2, x1, #1, #0x1f
    //     0x8fc8bc: tbz             w1, #0, #0x8fc8c4
    //     0x8fc8c0: ldur            x2, [x1, #7]
    // 0x8fc8c4: StoreField: r0->field_f = r2
    //     0x8fc8c4: stur            x2, [x0, #0xf]
    // 0x8fc8c8: ldur            x1, [fp, #-0x20]
    // 0x8fc8cc: ArrayStore: r0[0] = r1  ; List_4
    //     0x8fc8cc: stur            w1, [x0, #0x17]
    // 0x8fc8d0: ldur            x1, [fp, #-0x28]
    // 0x8fc8d4: StoreField: r0->field_1b = r1
    //     0x8fc8d4: stur            w1, [x0, #0x1b]
    // 0x8fc8d8: ldur            x1, [fp, #-0x30]
    // 0x8fc8dc: StoreField: r0->field_1f = r1
    //     0x8fc8dc: stur            w1, [x0, #0x1f]
    // 0x8fc8e0: ldur            x1, [fp, #-0x38]
    // 0x8fc8e4: StoreField: r0->field_23 = r1
    //     0x8fc8e4: stur            w1, [x0, #0x23]
    // 0x8fc8e8: ldur            x1, [fp, #-8]
    // 0x8fc8ec: StoreField: r0->field_27 = r1
    //     0x8fc8ec: stur            w1, [x0, #0x27]
    // 0x8fc8f0: LeaveFrame
    //     0x8fc8f0: mov             SP, fp
    //     0x8fc8f4: ldp             fp, lr, [SP], #0x10
    // 0x8fc8f8: ret
    //     0x8fc8f8: ret             
    // 0x8fc8fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc8fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc900: b               #0x8fc448
  }
  [closure] static RelatedContent<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fc910, size: 0x58
    // 0x8fc910: EnterFrame
    //     0x8fc910: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc914: mov             fp, SP
    // 0x8fc918: CheckStackOverflow
    //     0x8fc918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc91c: cmp             SP, x16
    //     0x8fc920: b.ls            #0x8fc960
    // 0x8fc924: ldr             x0, [fp, #0x10]
    // 0x8fc928: r2 = Null
    //     0x8fc928: mov             x2, NULL
    // 0x8fc92c: r1 = Null
    //     0x8fc92c: mov             x1, NULL
    // 0x8fc930: r8 = Map<String, dynamic>
    //     0x8fc930: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fc934: r3 = Null
    //     0x8fc934: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe80] Null
    //     0x8fc938: ldr             x3, [x3, #0xe80]
    // 0x8fc93c: r0 = Map<String, dynamic>()
    //     0x8fc93c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fc940: ldr             x2, [fp, #0x10]
    // 0x8fc944: r1 = <String>
    //     0x8fc944: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8fc948: r3 = "youtube_id"
    //     0x8fc948: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x8fc94c: ldr             x3, [x3, #0x3f0]
    // 0x8fc950: r0 = RelatedContent.fromMap()
    //     0x8fc950: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x8fc954: LeaveFrame
    //     0x8fc954: mov             SP, fp
    //     0x8fc958: ldp             fp, lr, [SP], #0x10
    // 0x8fc95c: ret
    //     0x8fc95c: ret             
    // 0x8fc960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc964: b               #0x8fc924
  }
  [closure] static RelatedContent<int> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fc968, size: 0x58
    // 0x8fc968: EnterFrame
    //     0x8fc968: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc96c: mov             fp, SP
    // 0x8fc970: CheckStackOverflow
    //     0x8fc970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc974: cmp             SP, x16
    //     0x8fc978: b.ls            #0x8fc9b8
    // 0x8fc97c: ldr             x0, [fp, #0x10]
    // 0x8fc980: r2 = Null
    //     0x8fc980: mov             x2, NULL
    // 0x8fc984: r1 = Null
    //     0x8fc984: mov             x1, NULL
    // 0x8fc988: r8 = Map<String, dynamic>
    //     0x8fc988: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fc98c: r3 = Null
    //     0x8fc98c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fe90] Null
    //     0x8fc990: ldr             x3, [x3, #0xe90]
    // 0x8fc994: r0 = Map<String, dynamic>()
    //     0x8fc994: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fc998: ldr             x2, [fp, #0x10]
    // 0x8fc99c: r1 = <int>
    //     0x8fc99c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8fc9a0: r3 = "article_id"
    //     0x8fc9a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0x8fc9a4: ldr             x3, [x3, #0x958]
    // 0x8fc9a8: r0 = RelatedContent.fromMap()
    //     0x8fc9a8: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x8fc9ac: LeaveFrame
    //     0x8fc9ac: mov             SP, fp
    //     0x8fc9b0: ldp             fp, lr, [SP], #0x10
    // 0x8fc9b4: ret
    //     0x8fc9b4: ret             
    // 0x8fc9b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc9b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc9bc: b               #0x8fc97c
  }
}
