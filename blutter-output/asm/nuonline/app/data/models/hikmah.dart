// lib: , url: package:nuonline/app/data/models/hikmah.dart

// class id: 1050024, size: 0x8
class :: {
}

// class id: 5583, size: 0x24, field offset: 0x8
//   const constructor, 
class Hikmah extends Equatable {

  factory _ Hikmah.fromMap(/* No info */) {
    // ** addr: 0x72c134, size: 0x4f0
    // 0x72c134: EnterFrame
    //     0x72c134: stp             fp, lr, [SP, #-0x10]!
    //     0x72c138: mov             fp, SP
    // 0x72c13c: AllocStack(0x50)
    //     0x72c13c: sub             SP, SP, #0x50
    // 0x72c140: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x72c140: mov             x3, x2
    //     0x72c144: stur            x2, [fp, #-8]
    // 0x72c148: CheckStackOverflow
    //     0x72c148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c14c: cmp             SP, x16
    //     0x72c150: b.ls            #0x72c61c
    // 0x72c154: r0 = LoadClassIdInstr(r3)
    //     0x72c154: ldur            x0, [x3, #-1]
    //     0x72c158: ubfx            x0, x0, #0xc, #0x14
    // 0x72c15c: mov             x1, x3
    // 0x72c160: r2 = "id"
    //     0x72c160: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x72c164: ldr             x2, [x2, #0x740]
    // 0x72c168: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c168: sub             lr, x0, #0x114
    //     0x72c16c: ldr             lr, [x21, lr, lsl #3]
    //     0x72c170: blr             lr
    // 0x72c174: mov             x3, x0
    // 0x72c178: r2 = Null
    //     0x72c178: mov             x2, NULL
    // 0x72c17c: r1 = Null
    //     0x72c17c: mov             x1, NULL
    // 0x72c180: stur            x3, [fp, #-0x10]
    // 0x72c184: branchIfSmi(r0, 0x72c1ac)
    //     0x72c184: tbz             w0, #0, #0x72c1ac
    // 0x72c188: r4 = LoadClassIdInstr(r0)
    //     0x72c188: ldur            x4, [x0, #-1]
    //     0x72c18c: ubfx            x4, x4, #0xc, #0x14
    // 0x72c190: sub             x4, x4, #0x3c
    // 0x72c194: cmp             x4, #1
    // 0x72c198: b.ls            #0x72c1ac
    // 0x72c19c: r8 = int
    //     0x72c19c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x72c1a0: r3 = Null
    //     0x72c1a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eac8] Null
    //     0x72c1a4: ldr             x3, [x3, #0xac8]
    // 0x72c1a8: r0 = int()
    //     0x72c1a8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x72c1ac: ldur            x3, [fp, #-8]
    // 0x72c1b0: r0 = LoadClassIdInstr(r3)
    //     0x72c1b0: ldur            x0, [x3, #-1]
    //     0x72c1b4: ubfx            x0, x0, #0xc, #0x14
    // 0x72c1b8: mov             x1, x3
    // 0x72c1bc: r2 = "title"
    //     0x72c1bc: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x72c1c0: ldr             x2, [x2, #0x748]
    // 0x72c1c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c1c4: sub             lr, x0, #0x114
    //     0x72c1c8: ldr             lr, [x21, lr, lsl #3]
    //     0x72c1cc: blr             lr
    // 0x72c1d0: mov             x3, x0
    // 0x72c1d4: r2 = Null
    //     0x72c1d4: mov             x2, NULL
    // 0x72c1d8: r1 = Null
    //     0x72c1d8: mov             x1, NULL
    // 0x72c1dc: stur            x3, [fp, #-0x18]
    // 0x72c1e0: r4 = 60
    //     0x72c1e0: movz            x4, #0x3c
    // 0x72c1e4: branchIfSmi(r0, 0x72c1f0)
    //     0x72c1e4: tbz             w0, #0, #0x72c1f0
    // 0x72c1e8: r4 = LoadClassIdInstr(r0)
    //     0x72c1e8: ldur            x4, [x0, #-1]
    //     0x72c1ec: ubfx            x4, x4, #0xc, #0x14
    // 0x72c1f0: sub             x4, x4, #0x5e
    // 0x72c1f4: cmp             x4, #1
    // 0x72c1f8: b.ls            #0x72c20c
    // 0x72c1fc: r8 = String
    //     0x72c1fc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c200: r3 = Null
    //     0x72c200: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ead8] Null
    //     0x72c204: ldr             x3, [x3, #0xad8]
    // 0x72c208: r0 = String()
    //     0x72c208: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c20c: ldur            x3, [fp, #-8]
    // 0x72c210: r0 = LoadClassIdInstr(r3)
    //     0x72c210: ldur            x0, [x3, #-1]
    //     0x72c214: ubfx            x0, x0, #0xc, #0x14
    // 0x72c218: mov             x1, x3
    // 0x72c21c: r2 = "slug"
    //     0x72c21c: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x72c220: ldr             x2, [x2, #0x9a8]
    // 0x72c224: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c224: sub             lr, x0, #0x114
    //     0x72c228: ldr             lr, [x21, lr, lsl #3]
    //     0x72c22c: blr             lr
    // 0x72c230: r2 = Null
    //     0x72c230: mov             x2, NULL
    // 0x72c234: r1 = Null
    //     0x72c234: mov             x1, NULL
    // 0x72c238: r4 = 60
    //     0x72c238: movz            x4, #0x3c
    // 0x72c23c: branchIfSmi(r0, 0x72c248)
    //     0x72c23c: tbz             w0, #0, #0x72c248
    // 0x72c240: r4 = LoadClassIdInstr(r0)
    //     0x72c240: ldur            x4, [x0, #-1]
    //     0x72c244: ubfx            x4, x4, #0xc, #0x14
    // 0x72c248: sub             x4, x4, #0x5e
    // 0x72c24c: cmp             x4, #1
    // 0x72c250: b.ls            #0x72c264
    // 0x72c254: r8 = String
    //     0x72c254: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c258: r3 = Null
    //     0x72c258: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eae8] Null
    //     0x72c25c: ldr             x3, [x3, #0xae8]
    // 0x72c260: r0 = String()
    //     0x72c260: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c264: ldur            x3, [fp, #-8]
    // 0x72c268: r0 = LoadClassIdInstr(r3)
    //     0x72c268: ldur            x0, [x3, #-1]
    //     0x72c26c: ubfx            x0, x0, #0xc, #0x14
    // 0x72c270: mov             x1, x3
    // 0x72c274: r2 = "image"
    //     0x72c274: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x72c278: ldr             x2, [x2, #0x520]
    // 0x72c27c: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c27c: sub             lr, x0, #0x114
    //     0x72c280: ldr             lr, [x21, lr, lsl #3]
    //     0x72c284: blr             lr
    // 0x72c288: cmp             w0, NULL
    // 0x72c28c: b.ne            #0x72c29c
    // 0x72c290: r4 = Instance_Image
    //     0x72c290: add             x4, PP, #0x19, lsl #12  ; [pp+0x19f40] Obj!Image@e25c41
    //     0x72c294: ldr             x4, [x4, #0xf40]
    // 0x72c298: b               #0x72c2f0
    // 0x72c29c: ldur            x3, [fp, #-8]
    // 0x72c2a0: r0 = LoadClassIdInstr(r3)
    //     0x72c2a0: ldur            x0, [x3, #-1]
    //     0x72c2a4: ubfx            x0, x0, #0xc, #0x14
    // 0x72c2a8: mov             x1, x3
    // 0x72c2ac: r2 = "image"
    //     0x72c2ac: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x72c2b0: ldr             x2, [x2, #0x520]
    // 0x72c2b4: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c2b4: sub             lr, x0, #0x114
    //     0x72c2b8: ldr             lr, [x21, lr, lsl #3]
    //     0x72c2bc: blr             lr
    // 0x72c2c0: mov             x3, x0
    // 0x72c2c4: r2 = Null
    //     0x72c2c4: mov             x2, NULL
    // 0x72c2c8: r1 = Null
    //     0x72c2c8: mov             x1, NULL
    // 0x72c2cc: stur            x3, [fp, #-0x20]
    // 0x72c2d0: r8 = Map<String, dynamic>
    //     0x72c2d0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72c2d4: r3 = Null
    //     0x72c2d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Null
    //     0x72c2d8: ldr             x3, [x3, #0xaf8]
    // 0x72c2dc: r0 = Map<String, dynamic>()
    //     0x72c2dc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72c2e0: ldur            x2, [fp, #-0x20]
    // 0x72c2e4: r1 = Null
    //     0x72c2e4: mov             x1, NULL
    // 0x72c2e8: r0 = Image.fromMap()
    //     0x72c2e8: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x72c2ec: mov             x4, x0
    // 0x72c2f0: ldur            x3, [fp, #-8]
    // 0x72c2f4: stur            x4, [fp, #-0x20]
    // 0x72c2f8: r0 = LoadClassIdInstr(r3)
    //     0x72c2f8: ldur            x0, [x3, #-1]
    //     0x72c2fc: ubfx            x0, x0, #0xc, #0x14
    // 0x72c300: mov             x1, x3
    // 0x72c304: r2 = "content"
    //     0x72c304: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x72c308: ldr             x2, [x2, #0xf58]
    // 0x72c30c: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c30c: sub             lr, x0, #0x114
    //     0x72c310: ldr             lr, [x21, lr, lsl #3]
    //     0x72c314: blr             lr
    // 0x72c318: mov             x3, x0
    // 0x72c31c: r2 = Null
    //     0x72c31c: mov             x2, NULL
    // 0x72c320: r1 = Null
    //     0x72c320: mov             x1, NULL
    // 0x72c324: stur            x3, [fp, #-0x28]
    // 0x72c328: r4 = 60
    //     0x72c328: movz            x4, #0x3c
    // 0x72c32c: branchIfSmi(r0, 0x72c338)
    //     0x72c32c: tbz             w0, #0, #0x72c338
    // 0x72c330: r4 = LoadClassIdInstr(r0)
    //     0x72c330: ldur            x4, [x0, #-1]
    //     0x72c334: ubfx            x4, x4, #0xc, #0x14
    // 0x72c338: sub             x4, x4, #0x5e
    // 0x72c33c: cmp             x4, #1
    // 0x72c340: b.ls            #0x72c354
    // 0x72c344: r8 = String
    //     0x72c344: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c348: r3 = Null
    //     0x72c348: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb08] Null
    //     0x72c34c: ldr             x3, [x3, #0xb08]
    // 0x72c350: r0 = String()
    //     0x72c350: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c354: ldur            x3, [fp, #-8]
    // 0x72c358: r0 = LoadClassIdInstr(r3)
    //     0x72c358: ldur            x0, [x3, #-1]
    //     0x72c35c: ubfx            x0, x0, #0xc, #0x14
    // 0x72c360: mov             x1, x3
    // 0x72c364: r2 = "updated_at"
    //     0x72c364: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x72c368: ldr             x2, [x2, #0xe88]
    // 0x72c36c: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c36c: sub             lr, x0, #0x114
    //     0x72c370: ldr             lr, [x21, lr, lsl #3]
    //     0x72c374: blr             lr
    // 0x72c378: r2 = Null
    //     0x72c378: mov             x2, NULL
    // 0x72c37c: r1 = Null
    //     0x72c37c: mov             x1, NULL
    // 0x72c380: r4 = 60
    //     0x72c380: movz            x4, #0x3c
    // 0x72c384: branchIfSmi(r0, 0x72c390)
    //     0x72c384: tbz             w0, #0, #0x72c390
    // 0x72c388: r4 = LoadClassIdInstr(r0)
    //     0x72c388: ldur            x4, [x0, #-1]
    //     0x72c38c: ubfx            x4, x4, #0xc, #0x14
    // 0x72c390: sub             x4, x4, #0x5e
    // 0x72c394: cmp             x4, #1
    // 0x72c398: b.ls            #0x72c3ac
    // 0x72c39c: r8 = String
    //     0x72c39c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c3a0: r3 = Null
    //     0x72c3a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb18] Null
    //     0x72c3a4: ldr             x3, [x3, #0xb18]
    // 0x72c3a8: r0 = String()
    //     0x72c3a8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c3ac: ldur            x3, [fp, #-8]
    // 0x72c3b0: r0 = LoadClassIdInstr(r3)
    //     0x72c3b0: ldur            x0, [x3, #-1]
    //     0x72c3b4: ubfx            x0, x0, #0xc, #0x14
    // 0x72c3b8: mov             x1, x3
    // 0x72c3bc: r2 = "articles"
    //     0x72c3bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0x72c3c0: ldr             x2, [x2, #0x8b8]
    // 0x72c3c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c3c4: sub             lr, x0, #0x114
    //     0x72c3c8: ldr             lr, [x21, lr, lsl #3]
    //     0x72c3cc: blr             lr
    // 0x72c3d0: mov             x3, x0
    // 0x72c3d4: r2 = Null
    //     0x72c3d4: mov             x2, NULL
    // 0x72c3d8: r1 = Null
    //     0x72c3d8: mov             x1, NULL
    // 0x72c3dc: stur            x3, [fp, #-0x30]
    // 0x72c3e0: r4 = 60
    //     0x72c3e0: movz            x4, #0x3c
    // 0x72c3e4: branchIfSmi(r0, 0x72c3f0)
    //     0x72c3e4: tbz             w0, #0, #0x72c3f0
    // 0x72c3e8: r4 = LoadClassIdInstr(r0)
    //     0x72c3e8: ldur            x4, [x0, #-1]
    //     0x72c3ec: ubfx            x4, x4, #0xc, #0x14
    // 0x72c3f0: sub             x4, x4, #0x5a
    // 0x72c3f4: cmp             x4, #2
    // 0x72c3f8: b.ls            #0x72c410
    // 0x72c3fc: r8 = List?
    //     0x72c3fc: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x72c400: ldr             x8, [x8, #0x140]
    // 0x72c404: r3 = Null
    //     0x72c404: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb28] Null
    //     0x72c408: ldr             x3, [x3, #0xb28]
    // 0x72c40c: r0 = List?()
    //     0x72c40c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x72c410: ldur            x0, [fp, #-0x30]
    // 0x72c414: cmp             w0, NULL
    // 0x72c418: b.ne            #0x72c430
    // 0x72c41c: r1 = Null
    //     0x72c41c: mov             x1, NULL
    // 0x72c420: r2 = 0
    //     0x72c420: movz            x2, #0
    // 0x72c424: r0 = _GrowableList()
    //     0x72c424: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x72c428: mov             x3, x0
    // 0x72c42c: b               #0x72c434
    // 0x72c430: mov             x3, x0
    // 0x72c434: ldur            x0, [fp, #-8]
    // 0x72c438: stur            x3, [fp, #-0x30]
    // 0x72c43c: r1 = Function '<anonymous closure>': static.
    //     0x72c43c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb38] AnonymousClosure: static (0x72cc74), in [package:nuonline/app/data/models/hikmah.dart] Hikmah::Hikmah.fromMap (0x72c134)
    //     0x72c440: ldr             x1, [x1, #0xb38]
    // 0x72c444: r2 = Null
    //     0x72c444: mov             x2, NULL
    // 0x72c448: r0 = AllocateClosure()
    //     0x72c448: bl              #0xec1630  ; AllocateClosureStub
    // 0x72c44c: mov             x1, x0
    // 0x72c450: ldur            x0, [fp, #-0x30]
    // 0x72c454: r2 = LoadClassIdInstr(r0)
    //     0x72c454: ldur            x2, [x0, #-1]
    //     0x72c458: ubfx            x2, x2, #0xc, #0x14
    // 0x72c45c: r16 = <RelatedContent<int>>
    //     0x72c45c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e808] TypeArguments: <RelatedContent<int>>
    //     0x72c460: ldr             x16, [x16, #0x808]
    // 0x72c464: stp             x0, x16, [SP, #8]
    // 0x72c468: str             x1, [SP]
    // 0x72c46c: mov             x0, x2
    // 0x72c470: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72c470: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72c474: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x72c474: movz            x17, #0xf28c
    //     0x72c478: add             lr, x0, x17
    //     0x72c47c: ldr             lr, [x21, lr, lsl #3]
    //     0x72c480: blr             lr
    // 0x72c484: r1 = LoadClassIdInstr(r0)
    //     0x72c484: ldur            x1, [x0, #-1]
    //     0x72c488: ubfx            x1, x1, #0xc, #0x14
    // 0x72c48c: mov             x16, x0
    // 0x72c490: mov             x0, x1
    // 0x72c494: mov             x1, x16
    // 0x72c498: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x72c498: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x72c49c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x72c49c: movz            x17, #0xd889
    //     0x72c4a0: add             lr, x0, x17
    //     0x72c4a4: ldr             lr, [x21, lr, lsl #3]
    //     0x72c4a8: blr             lr
    // 0x72c4ac: mov             x3, x0
    // 0x72c4b0: ldur            x1, [fp, #-8]
    // 0x72c4b4: stur            x3, [fp, #-0x30]
    // 0x72c4b8: r0 = LoadClassIdInstr(r1)
    //     0x72c4b8: ldur            x0, [x1, #-1]
    //     0x72c4bc: ubfx            x0, x0, #0xc, #0x14
    // 0x72c4c0: r2 = "videos"
    //     0x72c4c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0x72c4c4: ldr             x2, [x2, #0x8d8]
    // 0x72c4c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c4c8: sub             lr, x0, #0x114
    //     0x72c4cc: ldr             lr, [x21, lr, lsl #3]
    //     0x72c4d0: blr             lr
    // 0x72c4d4: mov             x3, x0
    // 0x72c4d8: r2 = Null
    //     0x72c4d8: mov             x2, NULL
    // 0x72c4dc: r1 = Null
    //     0x72c4dc: mov             x1, NULL
    // 0x72c4e0: stur            x3, [fp, #-8]
    // 0x72c4e4: r4 = 60
    //     0x72c4e4: movz            x4, #0x3c
    // 0x72c4e8: branchIfSmi(r0, 0x72c4f4)
    //     0x72c4e8: tbz             w0, #0, #0x72c4f4
    // 0x72c4ec: r4 = LoadClassIdInstr(r0)
    //     0x72c4ec: ldur            x4, [x0, #-1]
    //     0x72c4f0: ubfx            x4, x4, #0xc, #0x14
    // 0x72c4f4: sub             x4, x4, #0x5a
    // 0x72c4f8: cmp             x4, #2
    // 0x72c4fc: b.ls            #0x72c514
    // 0x72c500: r8 = List?
    //     0x72c500: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x72c504: ldr             x8, [x8, #0x140]
    // 0x72c508: r3 = Null
    //     0x72c508: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb40] Null
    //     0x72c50c: ldr             x3, [x3, #0xb40]
    // 0x72c510: r0 = List?()
    //     0x72c510: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x72c514: ldur            x0, [fp, #-8]
    // 0x72c518: cmp             w0, NULL
    // 0x72c51c: b.ne            #0x72c534
    // 0x72c520: r1 = Null
    //     0x72c520: mov             x1, NULL
    // 0x72c524: r2 = 0
    //     0x72c524: movz            x2, #0
    // 0x72c528: r0 = _GrowableList()
    //     0x72c528: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x72c52c: mov             x7, x0
    // 0x72c530: b               #0x72c538
    // 0x72c534: mov             x7, x0
    // 0x72c538: ldur            x6, [fp, #-0x10]
    // 0x72c53c: ldur            x5, [fp, #-0x18]
    // 0x72c540: ldur            x4, [fp, #-0x20]
    // 0x72c544: ldur            x3, [fp, #-0x28]
    // 0x72c548: ldur            x0, [fp, #-0x30]
    // 0x72c54c: stur            x7, [fp, #-8]
    // 0x72c550: r1 = Function '<anonymous closure>': static.
    //     0x72c550: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb50] AnonymousClosure: static (0x72c964), in [package:nuonline/app/data/models/hikmah.dart] Hikmah::Hikmah.fromMap (0x72c134)
    //     0x72c554: ldr             x1, [x1, #0xb50]
    // 0x72c558: r2 = Null
    //     0x72c558: mov             x2, NULL
    // 0x72c55c: r0 = AllocateClosure()
    //     0x72c55c: bl              #0xec1630  ; AllocateClosureStub
    // 0x72c560: mov             x1, x0
    // 0x72c564: ldur            x0, [fp, #-8]
    // 0x72c568: r2 = LoadClassIdInstr(r0)
    //     0x72c568: ldur            x2, [x0, #-1]
    //     0x72c56c: ubfx            x2, x2, #0xc, #0x14
    // 0x72c570: r16 = <RelatedContent<String>>
    //     0x72c570: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e810] TypeArguments: <RelatedContent<String>>
    //     0x72c574: ldr             x16, [x16, #0x810]
    // 0x72c578: stp             x0, x16, [SP, #8]
    // 0x72c57c: str             x1, [SP]
    // 0x72c580: mov             x0, x2
    // 0x72c584: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72c584: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72c588: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x72c588: movz            x17, #0xf28c
    //     0x72c58c: add             lr, x0, x17
    //     0x72c590: ldr             lr, [x21, lr, lsl #3]
    //     0x72c594: blr             lr
    // 0x72c598: r1 = LoadClassIdInstr(r0)
    //     0x72c598: ldur            x1, [x0, #-1]
    //     0x72c59c: ubfx            x1, x1, #0xc, #0x14
    // 0x72c5a0: mov             x16, x0
    // 0x72c5a4: mov             x0, x1
    // 0x72c5a8: mov             x1, x16
    // 0x72c5ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x72c5ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x72c5b0: r0 = GDT[cid_x0 + 0xd889]()
    //     0x72c5b0: movz            x17, #0xd889
    //     0x72c5b4: add             lr, x0, x17
    //     0x72c5b8: ldr             lr, [x21, lr, lsl #3]
    //     0x72c5bc: blr             lr
    // 0x72c5c0: mov             x1, x0
    // 0x72c5c4: ldur            x0, [fp, #-0x10]
    // 0x72c5c8: stur            x1, [fp, #-8]
    // 0x72c5cc: r2 = LoadInt32Instr(r0)
    //     0x72c5cc: sbfx            x2, x0, #1, #0x1f
    //     0x72c5d0: tbz             w0, #0, #0x72c5d8
    //     0x72c5d4: ldur            x2, [x0, #7]
    // 0x72c5d8: stur            x2, [fp, #-0x38]
    // 0x72c5dc: r0 = Hikmah()
    //     0x72c5dc: bl              #0x72c624  ; AllocateHikmahStub -> Hikmah (size=0x24)
    // 0x72c5e0: ldur            x1, [fp, #-0x38]
    // 0x72c5e4: StoreField: r0->field_7 = r1
    //     0x72c5e4: stur            x1, [x0, #7]
    // 0x72c5e8: ldur            x1, [fp, #-0x18]
    // 0x72c5ec: StoreField: r0->field_f = r1
    //     0x72c5ec: stur            w1, [x0, #0xf]
    // 0x72c5f0: ldur            x1, [fp, #-0x20]
    // 0x72c5f4: StoreField: r0->field_13 = r1
    //     0x72c5f4: stur            w1, [x0, #0x13]
    // 0x72c5f8: ldur            x1, [fp, #-0x28]
    // 0x72c5fc: ArrayStore: r0[0] = r1  ; List_4
    //     0x72c5fc: stur            w1, [x0, #0x17]
    // 0x72c600: ldur            x1, [fp, #-0x30]
    // 0x72c604: StoreField: r0->field_1b = r1
    //     0x72c604: stur            w1, [x0, #0x1b]
    // 0x72c608: ldur            x1, [fp, #-8]
    // 0x72c60c: StoreField: r0->field_1f = r1
    //     0x72c60c: stur            w1, [x0, #0x1f]
    // 0x72c610: LeaveFrame
    //     0x72c610: mov             SP, fp
    //     0x72c614: ldp             fp, lr, [SP], #0x10
    // 0x72c618: ret
    //     0x72c618: ret             
    // 0x72c61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c620: b               #0x72c154
  }
  [closure] static RelatedContent<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x72c964, size: 0x58
    // 0x72c964: EnterFrame
    //     0x72c964: stp             fp, lr, [SP, #-0x10]!
    //     0x72c968: mov             fp, SP
    // 0x72c96c: CheckStackOverflow
    //     0x72c96c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c970: cmp             SP, x16
    //     0x72c974: b.ls            #0x72c9b4
    // 0x72c978: ldr             x0, [fp, #0x10]
    // 0x72c97c: r2 = Null
    //     0x72c97c: mov             x2, NULL
    // 0x72c980: r1 = Null
    //     0x72c980: mov             x1, NULL
    // 0x72c984: r8 = Map<String, dynamic>
    //     0x72c984: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72c988: r3 = Null
    //     0x72c988: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb58] Null
    //     0x72c98c: ldr             x3, [x3, #0xb58]
    // 0x72c990: r0 = Map<String, dynamic>()
    //     0x72c990: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72c994: ldr             x2, [fp, #0x10]
    // 0x72c998: r1 = <String>
    //     0x72c998: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x72c99c: r3 = "youtube_id"
    //     0x72c99c: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x72c9a0: ldr             x3, [x3, #0x3f0]
    // 0x72c9a4: r0 = RelatedContent.fromMap()
    //     0x72c9a4: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x72c9a8: LeaveFrame
    //     0x72c9a8: mov             SP, fp
    //     0x72c9ac: ldp             fp, lr, [SP], #0x10
    // 0x72c9b0: ret
    //     0x72c9b0: ret             
    // 0x72c9b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c9b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c9b8: b               #0x72c978
  }
  [closure] static RelatedContent<int> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x72cc74, size: 0x58
    // 0x72cc74: EnterFrame
    //     0x72cc74: stp             fp, lr, [SP, #-0x10]!
    //     0x72cc78: mov             fp, SP
    // 0x72cc7c: CheckStackOverflow
    //     0x72cc7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72cc80: cmp             SP, x16
    //     0x72cc84: b.ls            #0x72ccc4
    // 0x72cc88: ldr             x0, [fp, #0x10]
    // 0x72cc8c: r2 = Null
    //     0x72cc8c: mov             x2, NULL
    // 0x72cc90: r1 = Null
    //     0x72cc90: mov             x1, NULL
    // 0x72cc94: r8 = Map<String, dynamic>
    //     0x72cc94: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72cc98: r3 = Null
    //     0x72cc98: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb68] Null
    //     0x72cc9c: ldr             x3, [x3, #0xb68]
    // 0x72cca0: r0 = Map<String, dynamic>()
    //     0x72cca0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72cca4: ldr             x2, [fp, #0x10]
    // 0x72cca8: r1 = <int>
    //     0x72cca8: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x72ccac: r3 = "article_id"
    //     0x72ccac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0x72ccb0: ldr             x3, [x3, #0x958]
    // 0x72ccb4: r0 = RelatedContent.fromMap()
    //     0x72ccb4: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x72ccb8: LeaveFrame
    //     0x72ccb8: mov             SP, fp
    //     0x72ccbc: ldp             fp, lr, [SP], #0x10
    // 0x72ccc0: ret
    //     0x72ccc0: ret             
    // 0x72ccc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72ccc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72ccc8: b               #0x72cc88
  }
}
