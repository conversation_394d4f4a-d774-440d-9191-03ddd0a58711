// lib: , url: package:nuonline/app/data/models/home_update_config.dart

// class id: 1050025, size: 0x8
class :: {
}

// class id: 1142, size: 0x14, field offset: 0x8
class HomeUpdateConfig extends Object {

  factory _ HomeUpdateConfig.fromMap(/* No info */) {
    // ** addr: 0x91b684, size: 0x1f0
    // 0x91b684: EnterFrame
    //     0x91b684: stp             fp, lr, [SP, #-0x10]!
    //     0x91b688: mov             fp, SP
    // 0x91b68c: AllocStack(0x18)
    //     0x91b68c: sub             SP, SP, #0x18
    // 0x91b690: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x91b690: mov             x0, x2
    //     0x91b694: stur            x2, [fp, #-8]
    // 0x91b698: CheckStackOverflow
    //     0x91b698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b69c: cmp             SP, x16
    //     0x91b6a0: b.ls            #0x91b86c
    // 0x91b6a4: mov             x1, x0
    // 0x91b6a8: r2 = "summary"
    //     0x91b6a8: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb58] "summary"
    //     0x91b6ac: ldr             x2, [x2, #0xb58]
    // 0x91b6b0: r0 = _getValueOrData()
    //     0x91b6b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91b6b4: ldur            x3, [fp, #-8]
    // 0x91b6b8: LoadField: r1 = r3->field_f
    //     0x91b6b8: ldur            w1, [x3, #0xf]
    // 0x91b6bc: DecompressPointer r1
    //     0x91b6bc: add             x1, x1, HEAP, lsl #32
    // 0x91b6c0: cmp             w1, w0
    // 0x91b6c4: b.ne            #0x91b6d0
    // 0x91b6c8: r4 = Null
    //     0x91b6c8: mov             x4, NULL
    // 0x91b6cc: b               #0x91b6d4
    // 0x91b6d0: mov             x4, x0
    // 0x91b6d4: mov             x0, x4
    // 0x91b6d8: stur            x4, [fp, #-0x10]
    // 0x91b6dc: r2 = Null
    //     0x91b6dc: mov             x2, NULL
    // 0x91b6e0: r1 = Null
    //     0x91b6e0: mov             x1, NULL
    // 0x91b6e4: r4 = 60
    //     0x91b6e4: movz            x4, #0x3c
    // 0x91b6e8: branchIfSmi(r0, 0x91b6f4)
    //     0x91b6e8: tbz             w0, #0, #0x91b6f4
    // 0x91b6ec: r4 = LoadClassIdInstr(r0)
    //     0x91b6ec: ldur            x4, [x0, #-1]
    //     0x91b6f0: ubfx            x4, x4, #0xc, #0x14
    // 0x91b6f4: cmp             x4, #0x3f
    // 0x91b6f8: b.eq            #0x91b70c
    // 0x91b6fc: r8 = bool?
    //     0x91b6fc: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x91b700: r3 = Null
    //     0x91b700: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc18] Null
    //     0x91b704: ldr             x3, [x3, #0xc18]
    // 0x91b708: r0 = bool?()
    //     0x91b708: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x91b70c: ldur            x0, [fp, #-0x10]
    // 0x91b710: cmp             w0, NULL
    // 0x91b714: b.ne            #0x91b720
    // 0x91b718: r3 = false
    //     0x91b718: add             x3, NULL, #0x30  ; false
    // 0x91b71c: b               #0x91b724
    // 0x91b720: mov             x3, x0
    // 0x91b724: ldur            x0, [fp, #-8]
    // 0x91b728: mov             x1, x0
    // 0x91b72c: stur            x3, [fp, #-0x10]
    // 0x91b730: r2 = "doa"
    //     0x91b730: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb60] "doa"
    //     0x91b734: ldr             x2, [x2, #0xb60]
    // 0x91b738: r0 = _getValueOrData()
    //     0x91b738: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91b73c: ldur            x3, [fp, #-8]
    // 0x91b740: LoadField: r1 = r3->field_f
    //     0x91b740: ldur            w1, [x3, #0xf]
    // 0x91b744: DecompressPointer r1
    //     0x91b744: add             x1, x1, HEAP, lsl #32
    // 0x91b748: cmp             w1, w0
    // 0x91b74c: b.ne            #0x91b758
    // 0x91b750: r4 = Null
    //     0x91b750: mov             x4, NULL
    // 0x91b754: b               #0x91b75c
    // 0x91b758: mov             x4, x0
    // 0x91b75c: mov             x0, x4
    // 0x91b760: stur            x4, [fp, #-0x18]
    // 0x91b764: r2 = Null
    //     0x91b764: mov             x2, NULL
    // 0x91b768: r1 = Null
    //     0x91b768: mov             x1, NULL
    // 0x91b76c: r4 = 60
    //     0x91b76c: movz            x4, #0x3c
    // 0x91b770: branchIfSmi(r0, 0x91b77c)
    //     0x91b770: tbz             w0, #0, #0x91b77c
    // 0x91b774: r4 = LoadClassIdInstr(r0)
    //     0x91b774: ldur            x4, [x0, #-1]
    //     0x91b778: ubfx            x4, x4, #0xc, #0x14
    // 0x91b77c: cmp             x4, #0x3f
    // 0x91b780: b.eq            #0x91b794
    // 0x91b784: r8 = bool?
    //     0x91b784: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x91b788: r3 = Null
    //     0x91b788: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc28] Null
    //     0x91b78c: ldr             x3, [x3, #0xc28]
    // 0x91b790: r0 = bool?()
    //     0x91b790: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x91b794: ldur            x0, [fp, #-0x18]
    // 0x91b798: cmp             w0, NULL
    // 0x91b79c: b.ne            #0x91b7a8
    // 0x91b7a0: r3 = false
    //     0x91b7a0: add             x3, NULL, #0x30  ; false
    // 0x91b7a4: b               #0x91b7ac
    // 0x91b7a8: mov             x3, x0
    // 0x91b7ac: ldur            x0, [fp, #-8]
    // 0x91b7b0: mov             x1, x0
    // 0x91b7b4: stur            x3, [fp, #-0x18]
    // 0x91b7b8: r2 = "countdown"
    //     0x91b7b8: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb68] "countdown"
    //     0x91b7bc: ldr             x2, [x2, #0xb68]
    // 0x91b7c0: r0 = _getValueOrData()
    //     0x91b7c0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91b7c4: mov             x1, x0
    // 0x91b7c8: ldur            x0, [fp, #-8]
    // 0x91b7cc: LoadField: r2 = r0->field_f
    //     0x91b7cc: ldur            w2, [x0, #0xf]
    // 0x91b7d0: DecompressPointer r2
    //     0x91b7d0: add             x2, x2, HEAP, lsl #32
    // 0x91b7d4: cmp             w2, w1
    // 0x91b7d8: b.ne            #0x91b7e4
    // 0x91b7dc: r3 = Null
    //     0x91b7dc: mov             x3, NULL
    // 0x91b7e0: b               #0x91b7e8
    // 0x91b7e4: mov             x3, x1
    // 0x91b7e8: mov             x0, x3
    // 0x91b7ec: stur            x3, [fp, #-8]
    // 0x91b7f0: r2 = Null
    //     0x91b7f0: mov             x2, NULL
    // 0x91b7f4: r1 = Null
    //     0x91b7f4: mov             x1, NULL
    // 0x91b7f8: r4 = 60
    //     0x91b7f8: movz            x4, #0x3c
    // 0x91b7fc: branchIfSmi(r0, 0x91b808)
    //     0x91b7fc: tbz             w0, #0, #0x91b808
    // 0x91b800: r4 = LoadClassIdInstr(r0)
    //     0x91b800: ldur            x4, [x0, #-1]
    //     0x91b804: ubfx            x4, x4, #0xc, #0x14
    // 0x91b808: cmp             x4, #0x3f
    // 0x91b80c: b.eq            #0x91b820
    // 0x91b810: r8 = bool?
    //     0x91b810: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x91b814: r3 = Null
    //     0x91b814: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dc38] Null
    //     0x91b818: ldr             x3, [x3, #0xc38]
    // 0x91b81c: r0 = bool?()
    //     0x91b81c: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x91b820: ldur            x0, [fp, #-8]
    // 0x91b824: cmp             w0, NULL
    // 0x91b828: b.ne            #0x91b834
    // 0x91b82c: r2 = false
    //     0x91b82c: add             x2, NULL, #0x30  ; false
    // 0x91b830: b               #0x91b838
    // 0x91b834: mov             x2, x0
    // 0x91b838: ldur            x1, [fp, #-0x10]
    // 0x91b83c: ldur            x0, [fp, #-0x18]
    // 0x91b840: stur            x2, [fp, #-8]
    // 0x91b844: r0 = HomeUpdateConfig()
    //     0x91b844: bl              #0x91b874  ; AllocateHomeUpdateConfigStub -> HomeUpdateConfig (size=0x14)
    // 0x91b848: ldur            x1, [fp, #-0x10]
    // 0x91b84c: StoreField: r0->field_7 = r1
    //     0x91b84c: stur            w1, [x0, #7]
    // 0x91b850: ldur            x1, [fp, #-0x18]
    // 0x91b854: StoreField: r0->field_b = r1
    //     0x91b854: stur            w1, [x0, #0xb]
    // 0x91b858: ldur            x1, [fp, #-8]
    // 0x91b85c: StoreField: r0->field_f = r1
    //     0x91b85c: stur            w1, [x0, #0xf]
    // 0x91b860: LeaveFrame
    //     0x91b860: mov             SP, fp
    //     0x91b864: ldp             fp, lr, [SP], #0x10
    // 0x91b868: ret
    //     0x91b868: ret             
    // 0x91b86c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b86c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b870: b               #0x91b6a4
  }
}
