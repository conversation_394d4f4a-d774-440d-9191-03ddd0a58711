// lib: , url: package:nuonline/app/data/models/image.dart

// class id: 1050026, size: 0x8
class :: {
}

// class id: 1658, size: 0x14, field offset: 0xc
class ImageAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa62274, size: 0x394
    // 0xa62274: EnterFrame
    //     0xa62274: stp             fp, lr, [SP, #-0x10]!
    //     0xa62278: mov             fp, SP
    // 0xa6227c: AllocStack(0x50)
    //     0xa6227c: sub             SP, SP, #0x50
    // 0xa62280: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa62280: stur            x2, [fp, #-0x20]
    // 0xa62284: CheckStackOverflow
    //     0xa62284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62288: cmp             SP, x16
    //     0xa6228c: b.ls            #0xa625f0
    // 0xa62290: LoadField: r3 = r2->field_23
    //     0xa62290: ldur            x3, [x2, #0x23]
    // 0xa62294: add             x0, x3, #1
    // 0xa62298: LoadField: r1 = r2->field_1b
    //     0xa62298: ldur            x1, [x2, #0x1b]
    // 0xa6229c: cmp             x0, x1
    // 0xa622a0: b.gt            #0xa62594
    // 0xa622a4: LoadField: r4 = r2->field_7
    //     0xa622a4: ldur            w4, [x2, #7]
    // 0xa622a8: DecompressPointer r4
    //     0xa622a8: add             x4, x4, HEAP, lsl #32
    // 0xa622ac: stur            x4, [fp, #-0x18]
    // 0xa622b0: StoreField: r2->field_23 = r0
    //     0xa622b0: stur            x0, [x2, #0x23]
    // 0xa622b4: LoadField: r0 = r4->field_13
    //     0xa622b4: ldur            w0, [x4, #0x13]
    // 0xa622b8: r5 = LoadInt32Instr(r0)
    //     0xa622b8: sbfx            x5, x0, #1, #0x1f
    // 0xa622bc: mov             x0, x5
    // 0xa622c0: mov             x1, x3
    // 0xa622c4: stur            x5, [fp, #-0x10]
    // 0xa622c8: cmp             x1, x0
    // 0xa622cc: b.hs            #0xa625f8
    // 0xa622d0: LoadField: r0 = r4->field_7
    //     0xa622d0: ldur            x0, [x4, #7]
    // 0xa622d4: ldrb            w1, [x0, x3]
    // 0xa622d8: stur            x1, [fp, #-8]
    // 0xa622dc: r16 = <int, dynamic>
    //     0xa622dc: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa622e0: ldr             x16, [x16, #0xac0]
    // 0xa622e4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa622e8: stp             lr, x16, [SP]
    // 0xa622ec: r0 = Map._fromLiteral()
    //     0xa622ec: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa622f0: mov             x2, x0
    // 0xa622f4: stur            x2, [fp, #-0x38]
    // 0xa622f8: r6 = 0
    //     0xa622f8: movz            x6, #0
    // 0xa622fc: ldur            x3, [fp, #-0x20]
    // 0xa62300: ldur            x4, [fp, #-0x18]
    // 0xa62304: ldur            x5, [fp, #-8]
    // 0xa62308: stur            x6, [fp, #-0x30]
    // 0xa6230c: CheckStackOverflow
    //     0xa6230c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62310: cmp             SP, x16
    //     0xa62314: b.ls            #0xa625fc
    // 0xa62318: cmp             x6, x5
    // 0xa6231c: b.ge            #0xa623a8
    // 0xa62320: LoadField: r7 = r3->field_23
    //     0xa62320: ldur            x7, [x3, #0x23]
    // 0xa62324: add             x0, x7, #1
    // 0xa62328: LoadField: r1 = r3->field_1b
    //     0xa62328: ldur            x1, [x3, #0x1b]
    // 0xa6232c: cmp             x0, x1
    // 0xa62330: b.gt            #0xa625bc
    // 0xa62334: StoreField: r3->field_23 = r0
    //     0xa62334: stur            x0, [x3, #0x23]
    // 0xa62338: ldur            x0, [fp, #-0x10]
    // 0xa6233c: mov             x1, x7
    // 0xa62340: cmp             x1, x0
    // 0xa62344: b.hs            #0xa62604
    // 0xa62348: LoadField: r0 = r4->field_7
    //     0xa62348: ldur            x0, [x4, #7]
    // 0xa6234c: ldrb            w8, [x0, x7]
    // 0xa62350: mov             x1, x3
    // 0xa62354: stur            x8, [fp, #-0x28]
    // 0xa62358: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa62358: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa6235c: r0 = read()
    //     0xa6235c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa62360: mov             x1, x0
    // 0xa62364: ldur            x0, [fp, #-0x28]
    // 0xa62368: lsl             x2, x0, #1
    // 0xa6236c: r16 = LoadInt32Instr(r2)
    //     0xa6236c: sbfx            x16, x2, #1, #0x1f
    // 0xa62370: r17 = 11601
    //     0xa62370: movz            x17, #0x2d51
    // 0xa62374: mul             x0, x16, x17
    // 0xa62378: umulh           x16, x16, x17
    // 0xa6237c: eor             x0, x0, x16
    // 0xa62380: r0 = 0
    //     0xa62380: eor             x0, x0, x0, lsr #32
    // 0xa62384: ubfiz           x0, x0, #1, #0x1e
    // 0xa62388: r5 = LoadInt32Instr(r0)
    //     0xa62388: sbfx            x5, x0, #1, #0x1f
    // 0xa6238c: mov             x3, x1
    // 0xa62390: ldur            x1, [fp, #-0x38]
    // 0xa62394: r0 = _set()
    //     0xa62394: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa62398: ldur            x0, [fp, #-0x30]
    // 0xa6239c: add             x6, x0, #1
    // 0xa623a0: ldur            x2, [fp, #-0x38]
    // 0xa623a4: b               #0xa622fc
    // 0xa623a8: mov             x0, x2
    // 0xa623ac: mov             x1, x0
    // 0xa623b0: r2 = 0
    //     0xa623b0: movz            x2, #0
    // 0xa623b4: r0 = _getValueOrData()
    //     0xa623b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa623b8: ldur            x3, [fp, #-0x38]
    // 0xa623bc: LoadField: r1 = r3->field_f
    //     0xa623bc: ldur            w1, [x3, #0xf]
    // 0xa623c0: DecompressPointer r1
    //     0xa623c0: add             x1, x1, HEAP, lsl #32
    // 0xa623c4: cmp             w1, w0
    // 0xa623c8: b.ne            #0xa623d4
    // 0xa623cc: r4 = Null
    //     0xa623cc: mov             x4, NULL
    // 0xa623d0: b               #0xa623d8
    // 0xa623d4: mov             x4, x0
    // 0xa623d8: mov             x0, x4
    // 0xa623dc: stur            x4, [fp, #-0x18]
    // 0xa623e0: r2 = Null
    //     0xa623e0: mov             x2, NULL
    // 0xa623e4: r1 = Null
    //     0xa623e4: mov             x1, NULL
    // 0xa623e8: r4 = 60
    //     0xa623e8: movz            x4, #0x3c
    // 0xa623ec: branchIfSmi(r0, 0xa623f8)
    //     0xa623ec: tbz             w0, #0, #0xa623f8
    // 0xa623f0: r4 = LoadClassIdInstr(r0)
    //     0xa623f0: ldur            x4, [x0, #-1]
    //     0xa623f4: ubfx            x4, x4, #0xc, #0x14
    // 0xa623f8: sub             x4, x4, #0x5e
    // 0xa623fc: cmp             x4, #1
    // 0xa62400: b.ls            #0xa62414
    // 0xa62404: r8 = String
    //     0xa62404: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62408: r3 = Null
    //     0xa62408: add             x3, PP, #0x21, lsl #12  ; [pp+0x21970] Null
    //     0xa6240c: ldr             x3, [x3, #0x970]
    // 0xa62410: r0 = String()
    //     0xa62410: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62414: ldur            x1, [fp, #-0x38]
    // 0xa62418: r2 = 2
    //     0xa62418: movz            x2, #0x2
    // 0xa6241c: r0 = _getValueOrData()
    //     0xa6241c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62420: ldur            x3, [fp, #-0x38]
    // 0xa62424: LoadField: r1 = r3->field_f
    //     0xa62424: ldur            w1, [x3, #0xf]
    // 0xa62428: DecompressPointer r1
    //     0xa62428: add             x1, x1, HEAP, lsl #32
    // 0xa6242c: cmp             w1, w0
    // 0xa62430: b.ne            #0xa6243c
    // 0xa62434: r4 = Null
    //     0xa62434: mov             x4, NULL
    // 0xa62438: b               #0xa62440
    // 0xa6243c: mov             x4, x0
    // 0xa62440: mov             x0, x4
    // 0xa62444: stur            x4, [fp, #-0x20]
    // 0xa62448: r2 = Null
    //     0xa62448: mov             x2, NULL
    // 0xa6244c: r1 = Null
    //     0xa6244c: mov             x1, NULL
    // 0xa62450: r4 = 60
    //     0xa62450: movz            x4, #0x3c
    // 0xa62454: branchIfSmi(r0, 0xa62460)
    //     0xa62454: tbz             w0, #0, #0xa62460
    // 0xa62458: r4 = LoadClassIdInstr(r0)
    //     0xa62458: ldur            x4, [x0, #-1]
    //     0xa6245c: ubfx            x4, x4, #0xc, #0x14
    // 0xa62460: sub             x4, x4, #0x5e
    // 0xa62464: cmp             x4, #1
    // 0xa62468: b.ls            #0xa6247c
    // 0xa6246c: r8 = String
    //     0xa6246c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62470: r3 = Null
    //     0xa62470: add             x3, PP, #0x21, lsl #12  ; [pp+0x21980] Null
    //     0xa62474: ldr             x3, [x3, #0x980]
    // 0xa62478: r0 = String()
    //     0xa62478: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa6247c: ldur            x1, [fp, #-0x38]
    // 0xa62480: r2 = 4
    //     0xa62480: movz            x2, #0x4
    // 0xa62484: r0 = _getValueOrData()
    //     0xa62484: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62488: ldur            x3, [fp, #-0x38]
    // 0xa6248c: LoadField: r1 = r3->field_f
    //     0xa6248c: ldur            w1, [x3, #0xf]
    // 0xa62490: DecompressPointer r1
    //     0xa62490: add             x1, x1, HEAP, lsl #32
    // 0xa62494: cmp             w1, w0
    // 0xa62498: b.ne            #0xa624a4
    // 0xa6249c: r4 = Null
    //     0xa6249c: mov             x4, NULL
    // 0xa624a0: b               #0xa624a8
    // 0xa624a4: mov             x4, x0
    // 0xa624a8: mov             x0, x4
    // 0xa624ac: stur            x4, [fp, #-0x40]
    // 0xa624b0: r2 = Null
    //     0xa624b0: mov             x2, NULL
    // 0xa624b4: r1 = Null
    //     0xa624b4: mov             x1, NULL
    // 0xa624b8: r4 = 60
    //     0xa624b8: movz            x4, #0x3c
    // 0xa624bc: branchIfSmi(r0, 0xa624c8)
    //     0xa624bc: tbz             w0, #0, #0xa624c8
    // 0xa624c0: r4 = LoadClassIdInstr(r0)
    //     0xa624c0: ldur            x4, [x0, #-1]
    //     0xa624c4: ubfx            x4, x4, #0xc, #0x14
    // 0xa624c8: sub             x4, x4, #0x5e
    // 0xa624cc: cmp             x4, #1
    // 0xa624d0: b.ls            #0xa624e4
    // 0xa624d4: r8 = String
    //     0xa624d4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa624d8: r3 = Null
    //     0xa624d8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21990] Null
    //     0xa624dc: ldr             x3, [x3, #0x990]
    // 0xa624e0: r0 = String()
    //     0xa624e0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa624e4: ldur            x1, [fp, #-0x38]
    // 0xa624e8: r2 = 6
    //     0xa624e8: movz            x2, #0x6
    // 0xa624ec: r0 = _getValueOrData()
    //     0xa624ec: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa624f0: mov             x1, x0
    // 0xa624f4: ldur            x0, [fp, #-0x38]
    // 0xa624f8: LoadField: r2 = r0->field_f
    //     0xa624f8: ldur            w2, [x0, #0xf]
    // 0xa624fc: DecompressPointer r2
    //     0xa624fc: add             x2, x2, HEAP, lsl #32
    // 0xa62500: cmp             w2, w1
    // 0xa62504: b.ne            #0xa62510
    // 0xa62508: r6 = Null
    //     0xa62508: mov             x6, NULL
    // 0xa6250c: b               #0xa62514
    // 0xa62510: mov             x6, x1
    // 0xa62514: ldur            x5, [fp, #-0x18]
    // 0xa62518: ldur            x4, [fp, #-0x20]
    // 0xa6251c: ldur            x3, [fp, #-0x40]
    // 0xa62520: mov             x0, x6
    // 0xa62524: stur            x6, [fp, #-0x38]
    // 0xa62528: r2 = Null
    //     0xa62528: mov             x2, NULL
    // 0xa6252c: r1 = Null
    //     0xa6252c: mov             x1, NULL
    // 0xa62530: r4 = 60
    //     0xa62530: movz            x4, #0x3c
    // 0xa62534: branchIfSmi(r0, 0xa62540)
    //     0xa62534: tbz             w0, #0, #0xa62540
    // 0xa62538: r4 = LoadClassIdInstr(r0)
    //     0xa62538: ldur            x4, [x0, #-1]
    //     0xa6253c: ubfx            x4, x4, #0xc, #0x14
    // 0xa62540: sub             x4, x4, #0x5e
    // 0xa62544: cmp             x4, #1
    // 0xa62548: b.ls            #0xa6255c
    // 0xa6254c: r8 = String?
    //     0xa6254c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa62550: r3 = Null
    //     0xa62550: add             x3, PP, #0x21, lsl #12  ; [pp+0x219a0] Null
    //     0xa62554: ldr             x3, [x3, #0x9a0]
    // 0xa62558: r0 = String?()
    //     0xa62558: bl              #0x600324  ; IsType_String?_Stub
    // 0xa6255c: r0 = Image()
    //     0xa6255c: bl              #0x72c958  ; AllocateImageStub -> Image (size=0x18)
    // 0xa62560: mov             x1, x0
    // 0xa62564: ldur            x0, [fp, #-0x18]
    // 0xa62568: StoreField: r1->field_7 = r0
    //     0xa62568: stur            w0, [x1, #7]
    // 0xa6256c: ldur            x0, [fp, #-0x20]
    // 0xa62570: StoreField: r1->field_b = r0
    //     0xa62570: stur            w0, [x1, #0xb]
    // 0xa62574: ldur            x0, [fp, #-0x40]
    // 0xa62578: StoreField: r1->field_f = r0
    //     0xa62578: stur            w0, [x1, #0xf]
    // 0xa6257c: ldur            x0, [fp, #-0x38]
    // 0xa62580: StoreField: r1->field_13 = r0
    //     0xa62580: stur            w0, [x1, #0x13]
    // 0xa62584: mov             x0, x1
    // 0xa62588: LeaveFrame
    //     0xa62588: mov             SP, fp
    //     0xa6258c: ldp             fp, lr, [SP], #0x10
    // 0xa62590: ret
    //     0xa62590: ret             
    // 0xa62594: r0 = RangeError()
    //     0xa62594: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa62598: mov             x1, x0
    // 0xa6259c: r0 = "Not enough bytes available."
    //     0xa6259c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa625a0: ldr             x0, [x0, #0x8a8]
    // 0xa625a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa625a4: stur            w0, [x1, #0x17]
    // 0xa625a8: r2 = false
    //     0xa625a8: add             x2, NULL, #0x30  ; false
    // 0xa625ac: StoreField: r1->field_b = r2
    //     0xa625ac: stur            w2, [x1, #0xb]
    // 0xa625b0: mov             x0, x1
    // 0xa625b4: r0 = Throw()
    //     0xa625b4: bl              #0xec04b8  ; ThrowStub
    // 0xa625b8: brk             #0
    // 0xa625bc: r0 = "Not enough bytes available."
    //     0xa625bc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa625c0: ldr             x0, [x0, #0x8a8]
    // 0xa625c4: r2 = false
    //     0xa625c4: add             x2, NULL, #0x30  ; false
    // 0xa625c8: r0 = RangeError()
    //     0xa625c8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa625cc: mov             x1, x0
    // 0xa625d0: r0 = "Not enough bytes available."
    //     0xa625d0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa625d4: ldr             x0, [x0, #0x8a8]
    // 0xa625d8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa625d8: stur            w0, [x1, #0x17]
    // 0xa625dc: r0 = false
    //     0xa625dc: add             x0, NULL, #0x30  ; false
    // 0xa625e0: StoreField: r1->field_b = r0
    //     0xa625e0: stur            w0, [x1, #0xb]
    // 0xa625e4: mov             x0, x1
    // 0xa625e8: r0 = Throw()
    //     0xa625e8: bl              #0xec04b8  ; ThrowStub
    // 0xa625ec: brk             #0
    // 0xa625f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa625f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa625f4: b               #0xa62290
    // 0xa625f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa625f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa625fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa625fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62600: b               #0xa62318
    // 0xa62604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa62604: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd33f0, size: 0x30c
    // 0xbd33f0: EnterFrame
    //     0xbd33f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd33f4: mov             fp, SP
    // 0xbd33f8: AllocStack(0x28)
    //     0xbd33f8: sub             SP, SP, #0x28
    // 0xbd33fc: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd33fc: mov             x4, x2
    //     0xbd3400: stur            x2, [fp, #-8]
    //     0xbd3404: stur            x3, [fp, #-0x10]
    // 0xbd3408: CheckStackOverflow
    //     0xbd3408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd340c: cmp             SP, x16
    //     0xbd3410: b.ls            #0xbd36e0
    // 0xbd3414: mov             x0, x3
    // 0xbd3418: r2 = Null
    //     0xbd3418: mov             x2, NULL
    // 0xbd341c: r1 = Null
    //     0xbd341c: mov             x1, NULL
    // 0xbd3420: r4 = 60
    //     0xbd3420: movz            x4, #0x3c
    // 0xbd3424: branchIfSmi(r0, 0xbd3430)
    //     0xbd3424: tbz             w0, #0, #0xbd3430
    // 0xbd3428: r4 = LoadClassIdInstr(r0)
    //     0xbd3428: ldur            x4, [x0, #-1]
    //     0xbd342c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd3430: r17 = 5582
    //     0xbd3430: movz            x17, #0x15ce
    // 0xbd3434: cmp             x4, x17
    // 0xbd3438: b.eq            #0xbd3450
    // 0xbd343c: r8 = Image
    //     0xbd343c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b560] Type: Image
    //     0xbd3440: ldr             x8, [x8, #0x560]
    // 0xbd3444: r3 = Null
    //     0xbd3444: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b568] Null
    //     0xbd3448: ldr             x3, [x3, #0x568]
    // 0xbd344c: r0 = Image()
    //     0xbd344c: bl              #0x72c934  ; IsType_Image_Stub
    // 0xbd3450: ldur            x0, [fp, #-8]
    // 0xbd3454: LoadField: r1 = r0->field_b
    //     0xbd3454: ldur            w1, [x0, #0xb]
    // 0xbd3458: DecompressPointer r1
    //     0xbd3458: add             x1, x1, HEAP, lsl #32
    // 0xbd345c: LoadField: r2 = r1->field_13
    //     0xbd345c: ldur            w2, [x1, #0x13]
    // 0xbd3460: LoadField: r1 = r0->field_13
    //     0xbd3460: ldur            x1, [x0, #0x13]
    // 0xbd3464: r3 = LoadInt32Instr(r2)
    //     0xbd3464: sbfx            x3, x2, #1, #0x1f
    // 0xbd3468: sub             x2, x3, x1
    // 0xbd346c: cmp             x2, #1
    // 0xbd3470: b.ge            #0xbd3480
    // 0xbd3474: mov             x1, x0
    // 0xbd3478: r2 = 1
    //     0xbd3478: movz            x2, #0x1
    // 0xbd347c: r0 = _increaseBufferSize()
    //     0xbd347c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3480: ldur            x3, [fp, #-8]
    // 0xbd3484: r2 = 4
    //     0xbd3484: movz            x2, #0x4
    // 0xbd3488: LoadField: r4 = r3->field_b
    //     0xbd3488: ldur            w4, [x3, #0xb]
    // 0xbd348c: DecompressPointer r4
    //     0xbd348c: add             x4, x4, HEAP, lsl #32
    // 0xbd3490: LoadField: r5 = r3->field_13
    //     0xbd3490: ldur            x5, [x3, #0x13]
    // 0xbd3494: add             x6, x5, #1
    // 0xbd3498: StoreField: r3->field_13 = r6
    //     0xbd3498: stur            x6, [x3, #0x13]
    // 0xbd349c: LoadField: r0 = r4->field_13
    //     0xbd349c: ldur            w0, [x4, #0x13]
    // 0xbd34a0: r7 = LoadInt32Instr(r0)
    //     0xbd34a0: sbfx            x7, x0, #1, #0x1f
    // 0xbd34a4: mov             x0, x7
    // 0xbd34a8: mov             x1, x5
    // 0xbd34ac: cmp             x1, x0
    // 0xbd34b0: b.hs            #0xbd36e8
    // 0xbd34b4: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd34b4: add             x0, x4, x5
    //     0xbd34b8: strb            w2, [x0, #0x17]
    // 0xbd34bc: sub             x0, x7, x6
    // 0xbd34c0: cmp             x0, #1
    // 0xbd34c4: b.ge            #0xbd34d4
    // 0xbd34c8: mov             x1, x3
    // 0xbd34cc: r2 = 1
    //     0xbd34cc: movz            x2, #0x1
    // 0xbd34d0: r0 = _increaseBufferSize()
    //     0xbd34d0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd34d4: ldur            x2, [fp, #-8]
    // 0xbd34d8: ldur            x3, [fp, #-0x10]
    // 0xbd34dc: LoadField: r4 = r2->field_b
    //     0xbd34dc: ldur            w4, [x2, #0xb]
    // 0xbd34e0: DecompressPointer r4
    //     0xbd34e0: add             x4, x4, HEAP, lsl #32
    // 0xbd34e4: LoadField: r5 = r2->field_13
    //     0xbd34e4: ldur            x5, [x2, #0x13]
    // 0xbd34e8: add             x0, x5, #1
    // 0xbd34ec: StoreField: r2->field_13 = r0
    //     0xbd34ec: stur            x0, [x2, #0x13]
    // 0xbd34f0: LoadField: r0 = r4->field_13
    //     0xbd34f0: ldur            w0, [x4, #0x13]
    // 0xbd34f4: r1 = LoadInt32Instr(r0)
    //     0xbd34f4: sbfx            x1, x0, #1, #0x1f
    // 0xbd34f8: mov             x0, x1
    // 0xbd34fc: mov             x1, x5
    // 0xbd3500: cmp             x1, x0
    // 0xbd3504: b.hs            #0xbd36ec
    // 0xbd3508: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd3508: add             x0, x4, x5
    //     0xbd350c: strb            wzr, [x0, #0x17]
    // 0xbd3510: LoadField: r0 = r3->field_7
    //     0xbd3510: ldur            w0, [x3, #7]
    // 0xbd3514: DecompressPointer r0
    //     0xbd3514: add             x0, x0, HEAP, lsl #32
    // 0xbd3518: r16 = <String>
    //     0xbd3518: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd351c: stp             x2, x16, [SP, #8]
    // 0xbd3520: str             x0, [SP]
    // 0xbd3524: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3524: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3528: r0 = write()
    //     0xbd3528: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd352c: ldur            x0, [fp, #-8]
    // 0xbd3530: LoadField: r1 = r0->field_b
    //     0xbd3530: ldur            w1, [x0, #0xb]
    // 0xbd3534: DecompressPointer r1
    //     0xbd3534: add             x1, x1, HEAP, lsl #32
    // 0xbd3538: LoadField: r2 = r1->field_13
    //     0xbd3538: ldur            w2, [x1, #0x13]
    // 0xbd353c: LoadField: r1 = r0->field_13
    //     0xbd353c: ldur            x1, [x0, #0x13]
    // 0xbd3540: r3 = LoadInt32Instr(r2)
    //     0xbd3540: sbfx            x3, x2, #1, #0x1f
    // 0xbd3544: sub             x2, x3, x1
    // 0xbd3548: cmp             x2, #1
    // 0xbd354c: b.ge            #0xbd355c
    // 0xbd3550: mov             x1, x0
    // 0xbd3554: r2 = 1
    //     0xbd3554: movz            x2, #0x1
    // 0xbd3558: r0 = _increaseBufferSize()
    //     0xbd3558: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd355c: ldur            x2, [fp, #-8]
    // 0xbd3560: ldur            x3, [fp, #-0x10]
    // 0xbd3564: r4 = 1
    //     0xbd3564: movz            x4, #0x1
    // 0xbd3568: LoadField: r5 = r2->field_b
    //     0xbd3568: ldur            w5, [x2, #0xb]
    // 0xbd356c: DecompressPointer r5
    //     0xbd356c: add             x5, x5, HEAP, lsl #32
    // 0xbd3570: LoadField: r6 = r2->field_13
    //     0xbd3570: ldur            x6, [x2, #0x13]
    // 0xbd3574: add             x0, x6, #1
    // 0xbd3578: StoreField: r2->field_13 = r0
    //     0xbd3578: stur            x0, [x2, #0x13]
    // 0xbd357c: LoadField: r0 = r5->field_13
    //     0xbd357c: ldur            w0, [x5, #0x13]
    // 0xbd3580: r1 = LoadInt32Instr(r0)
    //     0xbd3580: sbfx            x1, x0, #1, #0x1f
    // 0xbd3584: mov             x0, x1
    // 0xbd3588: mov             x1, x6
    // 0xbd358c: cmp             x1, x0
    // 0xbd3590: b.hs            #0xbd36f0
    // 0xbd3594: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3594: add             x0, x5, x6
    //     0xbd3598: strb            w4, [x0, #0x17]
    // 0xbd359c: LoadField: r0 = r3->field_b
    //     0xbd359c: ldur            w0, [x3, #0xb]
    // 0xbd35a0: DecompressPointer r0
    //     0xbd35a0: add             x0, x0, HEAP, lsl #32
    // 0xbd35a4: r16 = <String>
    //     0xbd35a4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd35a8: stp             x2, x16, [SP, #8]
    // 0xbd35ac: str             x0, [SP]
    // 0xbd35b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd35b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd35b4: r0 = write()
    //     0xbd35b4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd35b8: ldur            x0, [fp, #-8]
    // 0xbd35bc: LoadField: r1 = r0->field_b
    //     0xbd35bc: ldur            w1, [x0, #0xb]
    // 0xbd35c0: DecompressPointer r1
    //     0xbd35c0: add             x1, x1, HEAP, lsl #32
    // 0xbd35c4: LoadField: r2 = r1->field_13
    //     0xbd35c4: ldur            w2, [x1, #0x13]
    // 0xbd35c8: LoadField: r1 = r0->field_13
    //     0xbd35c8: ldur            x1, [x0, #0x13]
    // 0xbd35cc: r3 = LoadInt32Instr(r2)
    //     0xbd35cc: sbfx            x3, x2, #1, #0x1f
    // 0xbd35d0: sub             x2, x3, x1
    // 0xbd35d4: cmp             x2, #1
    // 0xbd35d8: b.ge            #0xbd35e8
    // 0xbd35dc: mov             x1, x0
    // 0xbd35e0: r2 = 1
    //     0xbd35e0: movz            x2, #0x1
    // 0xbd35e4: r0 = _increaseBufferSize()
    //     0xbd35e4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd35e8: ldur            x2, [fp, #-8]
    // 0xbd35ec: ldur            x3, [fp, #-0x10]
    // 0xbd35f0: r4 = 2
    //     0xbd35f0: movz            x4, #0x2
    // 0xbd35f4: LoadField: r5 = r2->field_b
    //     0xbd35f4: ldur            w5, [x2, #0xb]
    // 0xbd35f8: DecompressPointer r5
    //     0xbd35f8: add             x5, x5, HEAP, lsl #32
    // 0xbd35fc: LoadField: r6 = r2->field_13
    //     0xbd35fc: ldur            x6, [x2, #0x13]
    // 0xbd3600: add             x0, x6, #1
    // 0xbd3604: StoreField: r2->field_13 = r0
    //     0xbd3604: stur            x0, [x2, #0x13]
    // 0xbd3608: LoadField: r0 = r5->field_13
    //     0xbd3608: ldur            w0, [x5, #0x13]
    // 0xbd360c: r1 = LoadInt32Instr(r0)
    //     0xbd360c: sbfx            x1, x0, #1, #0x1f
    // 0xbd3610: mov             x0, x1
    // 0xbd3614: mov             x1, x6
    // 0xbd3618: cmp             x1, x0
    // 0xbd361c: b.hs            #0xbd36f4
    // 0xbd3620: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3620: add             x0, x5, x6
    //     0xbd3624: strb            w4, [x0, #0x17]
    // 0xbd3628: LoadField: r0 = r3->field_f
    //     0xbd3628: ldur            w0, [x3, #0xf]
    // 0xbd362c: DecompressPointer r0
    //     0xbd362c: add             x0, x0, HEAP, lsl #32
    // 0xbd3630: r16 = <String>
    //     0xbd3630: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3634: stp             x2, x16, [SP, #8]
    // 0xbd3638: str             x0, [SP]
    // 0xbd363c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd363c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3640: r0 = write()
    //     0xbd3640: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3644: ldur            x0, [fp, #-8]
    // 0xbd3648: LoadField: r1 = r0->field_b
    //     0xbd3648: ldur            w1, [x0, #0xb]
    // 0xbd364c: DecompressPointer r1
    //     0xbd364c: add             x1, x1, HEAP, lsl #32
    // 0xbd3650: LoadField: r2 = r1->field_13
    //     0xbd3650: ldur            w2, [x1, #0x13]
    // 0xbd3654: LoadField: r1 = r0->field_13
    //     0xbd3654: ldur            x1, [x0, #0x13]
    // 0xbd3658: r3 = LoadInt32Instr(r2)
    //     0xbd3658: sbfx            x3, x2, #1, #0x1f
    // 0xbd365c: sub             x2, x3, x1
    // 0xbd3660: cmp             x2, #1
    // 0xbd3664: b.ge            #0xbd3674
    // 0xbd3668: mov             x1, x0
    // 0xbd366c: r2 = 1
    //     0xbd366c: movz            x2, #0x1
    // 0xbd3670: r0 = _increaseBufferSize()
    //     0xbd3670: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3674: ldur            x2, [fp, #-8]
    // 0xbd3678: ldur            x3, [fp, #-0x10]
    // 0xbd367c: r4 = 3
    //     0xbd367c: movz            x4, #0x3
    // 0xbd3680: LoadField: r5 = r2->field_b
    //     0xbd3680: ldur            w5, [x2, #0xb]
    // 0xbd3684: DecompressPointer r5
    //     0xbd3684: add             x5, x5, HEAP, lsl #32
    // 0xbd3688: LoadField: r6 = r2->field_13
    //     0xbd3688: ldur            x6, [x2, #0x13]
    // 0xbd368c: add             x0, x6, #1
    // 0xbd3690: StoreField: r2->field_13 = r0
    //     0xbd3690: stur            x0, [x2, #0x13]
    // 0xbd3694: LoadField: r0 = r5->field_13
    //     0xbd3694: ldur            w0, [x5, #0x13]
    // 0xbd3698: r1 = LoadInt32Instr(r0)
    //     0xbd3698: sbfx            x1, x0, #1, #0x1f
    // 0xbd369c: mov             x0, x1
    // 0xbd36a0: mov             x1, x6
    // 0xbd36a4: cmp             x1, x0
    // 0xbd36a8: b.hs            #0xbd36f8
    // 0xbd36ac: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd36ac: add             x0, x5, x6
    //     0xbd36b0: strb            w4, [x0, #0x17]
    // 0xbd36b4: LoadField: r0 = r3->field_13
    //     0xbd36b4: ldur            w0, [x3, #0x13]
    // 0xbd36b8: DecompressPointer r0
    //     0xbd36b8: add             x0, x0, HEAP, lsl #32
    // 0xbd36bc: r16 = <String?>
    //     0xbd36bc: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd36c0: stp             x2, x16, [SP, #8]
    // 0xbd36c4: str             x0, [SP]
    // 0xbd36c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd36c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd36cc: r0 = write()
    //     0xbd36cc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd36d0: r0 = Null
    //     0xbd36d0: mov             x0, NULL
    // 0xbd36d4: LeaveFrame
    //     0xbd36d4: mov             SP, fp
    //     0xbd36d8: ldp             fp, lr, [SP], #0x10
    // 0xbd36dc: ret
    //     0xbd36dc: ret             
    // 0xbd36e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd36e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd36e4: b               #0xbd3414
    // 0xbd36e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd36e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd36ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd36ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd36f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd36f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd36f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd36f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd36f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd36f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf01b0, size: 0x24
    // 0xbf01b0: r1 = 84
    //     0xbf01b0: movz            x1, #0x54
    // 0xbf01b4: r16 = LoadInt32Instr(r1)
    //     0xbf01b4: sbfx            x16, x1, #1, #0x1f
    // 0xbf01b8: r17 = 11601
    //     0xbf01b8: movz            x17, #0x2d51
    // 0xbf01bc: mul             x0, x16, x17
    // 0xbf01c0: umulh           x16, x16, x17
    // 0xbf01c4: eor             x0, x0, x16
    // 0xbf01c8: r0 = 0
    //     0xbf01c8: eor             x0, x0, x0, lsr #32
    // 0xbf01cc: ubfiz           x0, x0, #1, #0x1e
    // 0xbf01d0: ret
    //     0xbf01d0: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd767c4, size: 0x9c
    // 0xd767c4: EnterFrame
    //     0xd767c4: stp             fp, lr, [SP, #-0x10]!
    //     0xd767c8: mov             fp, SP
    // 0xd767cc: AllocStack(0x10)
    //     0xd767cc: sub             SP, SP, #0x10
    // 0xd767d0: CheckStackOverflow
    //     0xd767d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd767d4: cmp             SP, x16
    //     0xd767d8: b.ls            #0xd76858
    // 0xd767dc: ldr             x0, [fp, #0x10]
    // 0xd767e0: cmp             w0, NULL
    // 0xd767e4: b.ne            #0xd767f8
    // 0xd767e8: r0 = false
    //     0xd767e8: add             x0, NULL, #0x30  ; false
    // 0xd767ec: LeaveFrame
    //     0xd767ec: mov             SP, fp
    //     0xd767f0: ldp             fp, lr, [SP], #0x10
    // 0xd767f4: ret
    //     0xd767f4: ret             
    // 0xd767f8: ldr             x1, [fp, #0x18]
    // 0xd767fc: cmp             w1, w0
    // 0xd76800: b.ne            #0xd7680c
    // 0xd76804: r0 = true
    //     0xd76804: add             x0, NULL, #0x20  ; true
    // 0xd76808: b               #0xd7684c
    // 0xd7680c: r1 = 60
    //     0xd7680c: movz            x1, #0x3c
    // 0xd76810: branchIfSmi(r0, 0xd7681c)
    //     0xd76810: tbz             w0, #0, #0xd7681c
    // 0xd76814: r1 = LoadClassIdInstr(r0)
    //     0xd76814: ldur            x1, [x0, #-1]
    //     0xd76818: ubfx            x1, x1, #0xc, #0x14
    // 0xd7681c: cmp             x1, #0x67a
    // 0xd76820: b.ne            #0xd76848
    // 0xd76824: r16 = ImageAdapter
    //     0xd76824: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b558] Type: ImageAdapter
    //     0xd76828: ldr             x16, [x16, #0x558]
    // 0xd7682c: r30 = ImageAdapter
    //     0xd7682c: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b558] Type: ImageAdapter
    //     0xd76830: ldr             lr, [lr, #0x558]
    // 0xd76834: stp             lr, x16, [SP]
    // 0xd76838: r0 = ==()
    //     0xd76838: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7683c: tbnz            w0, #4, #0xd76848
    // 0xd76840: r0 = true
    //     0xd76840: add             x0, NULL, #0x20  ; true
    // 0xd76844: b               #0xd7684c
    // 0xd76848: r0 = false
    //     0xd76848: add             x0, NULL, #0x30  ; false
    // 0xd7684c: LeaveFrame
    //     0xd7684c: mov             SP, fp
    //     0xd76850: ldp             fp, lr, [SP], #0x10
    // 0xd76854: ret
    //     0xd76854: ret             
    // 0xd76858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7685c: b               #0xd767dc
  }
}

// class id: 5582, size: 0x18, field offset: 0x8
//   const constructor, 
class Image extends Equatable {

  _OneByteString field_8;
  _OneByteString field_c;
  _OneByteString field_10;

  factory _ Image.fromMap(/* No info */) {
    // ** addr: 0x72c630, size: 0x1d0
    // 0x72c630: EnterFrame
    //     0x72c630: stp             fp, lr, [SP, #-0x10]!
    //     0x72c634: mov             fp, SP
    // 0x72c638: AllocStack(0x20)
    //     0x72c638: sub             SP, SP, #0x20
    // 0x72c63c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x72c63c: mov             x3, x2
    //     0x72c640: stur            x2, [fp, #-8]
    // 0x72c644: CheckStackOverflow
    //     0x72c644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c648: cmp             SP, x16
    //     0x72c64c: b.ls            #0x72c7f8
    // 0x72c650: r0 = LoadClassIdInstr(r3)
    //     0x72c650: ldur            x0, [x3, #-1]
    //     0x72c654: ubfx            x0, x0, #0xc, #0x14
    // 0x72c658: mov             x1, x3
    // 0x72c65c: r2 = "thumbnail"
    //     0x72c65c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f80] "thumbnail"
    //     0x72c660: ldr             x2, [x2, #0xf80]
    // 0x72c664: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c664: sub             lr, x0, #0x114
    //     0x72c668: ldr             lr, [x21, lr, lsl #3]
    //     0x72c66c: blr             lr
    // 0x72c670: mov             x3, x0
    // 0x72c674: r2 = Null
    //     0x72c674: mov             x2, NULL
    // 0x72c678: r1 = Null
    //     0x72c678: mov             x1, NULL
    // 0x72c67c: stur            x3, [fp, #-0x10]
    // 0x72c680: r4 = 60
    //     0x72c680: movz            x4, #0x3c
    // 0x72c684: branchIfSmi(r0, 0x72c690)
    //     0x72c684: tbz             w0, #0, #0x72c690
    // 0x72c688: r4 = LoadClassIdInstr(r0)
    //     0x72c688: ldur            x4, [x0, #-1]
    //     0x72c68c: ubfx            x4, x4, #0xc, #0x14
    // 0x72c690: sub             x4, x4, #0x5e
    // 0x72c694: cmp             x4, #1
    // 0x72c698: b.ls            #0x72c6ac
    // 0x72c69c: r8 = String
    //     0x72c69c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c6a0: r3 = Null
    //     0x72c6a0: add             x3, PP, #0x19, lsl #12  ; [pp+0x19f88] Null
    //     0x72c6a4: ldr             x3, [x3, #0xf88]
    // 0x72c6a8: r0 = String()
    //     0x72c6a8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c6ac: ldur            x3, [fp, #-8]
    // 0x72c6b0: r0 = LoadClassIdInstr(r3)
    //     0x72c6b0: ldur            x0, [x3, #-1]
    //     0x72c6b4: ubfx            x0, x0, #0xc, #0x14
    // 0x72c6b8: mov             x1, x3
    // 0x72c6bc: r2 = "medium"
    //     0x72c6bc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f98] "medium"
    //     0x72c6c0: ldr             x2, [x2, #0xf98]
    // 0x72c6c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c6c4: sub             lr, x0, #0x114
    //     0x72c6c8: ldr             lr, [x21, lr, lsl #3]
    //     0x72c6cc: blr             lr
    // 0x72c6d0: mov             x3, x0
    // 0x72c6d4: r2 = Null
    //     0x72c6d4: mov             x2, NULL
    // 0x72c6d8: r1 = Null
    //     0x72c6d8: mov             x1, NULL
    // 0x72c6dc: stur            x3, [fp, #-0x18]
    // 0x72c6e0: r4 = 60
    //     0x72c6e0: movz            x4, #0x3c
    // 0x72c6e4: branchIfSmi(r0, 0x72c6f0)
    //     0x72c6e4: tbz             w0, #0, #0x72c6f0
    // 0x72c6e8: r4 = LoadClassIdInstr(r0)
    //     0x72c6e8: ldur            x4, [x0, #-1]
    //     0x72c6ec: ubfx            x4, x4, #0xc, #0x14
    // 0x72c6f0: sub             x4, x4, #0x5e
    // 0x72c6f4: cmp             x4, #1
    // 0x72c6f8: b.ls            #0x72c70c
    // 0x72c6fc: r8 = String
    //     0x72c6fc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c700: r3 = Null
    //     0x72c700: add             x3, PP, #0x19, lsl #12  ; [pp+0x19fa0] Null
    //     0x72c704: ldr             x3, [x3, #0xfa0]
    // 0x72c708: r0 = String()
    //     0x72c708: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c70c: ldur            x3, [fp, #-8]
    // 0x72c710: r0 = LoadClassIdInstr(r3)
    //     0x72c710: ldur            x0, [x3, #-1]
    //     0x72c714: ubfx            x0, x0, #0xc, #0x14
    // 0x72c718: mov             x1, x3
    // 0x72c71c: r2 = "full"
    //     0x72c71c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19fb0] "full"
    //     0x72c720: ldr             x2, [x2, #0xfb0]
    // 0x72c724: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c724: sub             lr, x0, #0x114
    //     0x72c728: ldr             lr, [x21, lr, lsl #3]
    //     0x72c72c: blr             lr
    // 0x72c730: mov             x3, x0
    // 0x72c734: r2 = Null
    //     0x72c734: mov             x2, NULL
    // 0x72c738: r1 = Null
    //     0x72c738: mov             x1, NULL
    // 0x72c73c: stur            x3, [fp, #-0x20]
    // 0x72c740: r4 = 60
    //     0x72c740: movz            x4, #0x3c
    // 0x72c744: branchIfSmi(r0, 0x72c750)
    //     0x72c744: tbz             w0, #0, #0x72c750
    // 0x72c748: r4 = LoadClassIdInstr(r0)
    //     0x72c748: ldur            x4, [x0, #-1]
    //     0x72c74c: ubfx            x4, x4, #0xc, #0x14
    // 0x72c750: sub             x4, x4, #0x5e
    // 0x72c754: cmp             x4, #1
    // 0x72c758: b.ls            #0x72c76c
    // 0x72c75c: r8 = String
    //     0x72c75c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72c760: r3 = Null
    //     0x72c760: add             x3, PP, #0x19, lsl #12  ; [pp+0x19fb8] Null
    //     0x72c764: ldr             x3, [x3, #0xfb8]
    // 0x72c768: r0 = String()
    //     0x72c768: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72c76c: ldur            x1, [fp, #-8]
    // 0x72c770: r0 = LoadClassIdInstr(r1)
    //     0x72c770: ldur            x0, [x1, #-1]
    //     0x72c774: ubfx            x0, x0, #0xc, #0x14
    // 0x72c778: r2 = "caption"
    //     0x72c778: add             x2, PP, #0x19, lsl #12  ; [pp+0x19fc8] "caption"
    //     0x72c77c: ldr             x2, [x2, #0xfc8]
    // 0x72c780: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c780: sub             lr, x0, #0x114
    //     0x72c784: ldr             lr, [x21, lr, lsl #3]
    //     0x72c788: blr             lr
    // 0x72c78c: mov             x3, x0
    // 0x72c790: r2 = Null
    //     0x72c790: mov             x2, NULL
    // 0x72c794: r1 = Null
    //     0x72c794: mov             x1, NULL
    // 0x72c798: stur            x3, [fp, #-8]
    // 0x72c79c: r4 = 60
    //     0x72c79c: movz            x4, #0x3c
    // 0x72c7a0: branchIfSmi(r0, 0x72c7ac)
    //     0x72c7a0: tbz             w0, #0, #0x72c7ac
    // 0x72c7a4: r4 = LoadClassIdInstr(r0)
    //     0x72c7a4: ldur            x4, [x0, #-1]
    //     0x72c7a8: ubfx            x4, x4, #0xc, #0x14
    // 0x72c7ac: sub             x4, x4, #0x5e
    // 0x72c7b0: cmp             x4, #1
    // 0x72c7b4: b.ls            #0x72c7c8
    // 0x72c7b8: r8 = String?
    //     0x72c7b8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x72c7bc: r3 = Null
    //     0x72c7bc: add             x3, PP, #0x19, lsl #12  ; [pp+0x19fd0] Null
    //     0x72c7c0: ldr             x3, [x3, #0xfd0]
    // 0x72c7c4: r0 = String?()
    //     0x72c7c4: bl              #0x600324  ; IsType_String?_Stub
    // 0x72c7c8: r0 = Image()
    //     0x72c7c8: bl              #0x72c958  ; AllocateImageStub -> Image (size=0x18)
    // 0x72c7cc: ldur            x1, [fp, #-0x10]
    // 0x72c7d0: StoreField: r0->field_7 = r1
    //     0x72c7d0: stur            w1, [x0, #7]
    // 0x72c7d4: ldur            x1, [fp, #-0x18]
    // 0x72c7d8: StoreField: r0->field_b = r1
    //     0x72c7d8: stur            w1, [x0, #0xb]
    // 0x72c7dc: ldur            x1, [fp, #-0x20]
    // 0x72c7e0: StoreField: r0->field_f = r1
    //     0x72c7e0: stur            w1, [x0, #0xf]
    // 0x72c7e4: ldur            x1, [fp, #-8]
    // 0x72c7e8: StoreField: r0->field_13 = r1
    //     0x72c7e8: stur            w1, [x0, #0x13]
    // 0x72c7ec: LeaveFrame
    //     0x72c7ec: mov             SP, fp
    //     0x72c7f0: ldp             fp, lr, [SP], #0x10
    // 0x72c7f4: ret
    //     0x72c7f4: ret             
    // 0x72c7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c7f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c7fc: b               #0x72c650
  }
  String toJson(Image) {
    // ** addr: 0x72c818, size: 0x48
    // 0x72c818: EnterFrame
    //     0x72c818: stp             fp, lr, [SP, #-0x10]!
    //     0x72c81c: mov             fp, SP
    // 0x72c820: CheckStackOverflow
    //     0x72c820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c824: cmp             SP, x16
    //     0x72c828: b.ls            #0x72c840
    // 0x72c82c: ldr             x1, [fp, #0x10]
    // 0x72c830: r0 = toJson()
    //     0x72c830: bl              #0x72c848  ; [package:nuonline/app/data/models/image.dart] Image::toJson
    // 0x72c834: LeaveFrame
    //     0x72c834: mov             SP, fp
    //     0x72c838: ldp             fp, lr, [SP], #0x10
    // 0x72c83c: ret
    //     0x72c83c: ret             
    // 0x72c840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c844: b               #0x72c82c
  }
  String toJson(Image) {
    // ** addr: 0x72c848, size: 0x3c
    // 0x72c848: EnterFrame
    //     0x72c848: stp             fp, lr, [SP, #-0x10]!
    //     0x72c84c: mov             fp, SP
    // 0x72c850: CheckStackOverflow
    //     0x72c850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c854: cmp             SP, x16
    //     0x72c858: b.ls            #0x72c87c
    // 0x72c85c: r0 = toMap()
    //     0x72c85c: bl              #0x72c884  ; [package:nuonline/app/data/models/image.dart] Image::toMap
    // 0x72c860: mov             x2, x0
    // 0x72c864: r1 = Instance_JsonCodec
    //     0x72c864: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x72c868: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x72c868: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x72c86c: r0 = encode()
    //     0x72c86c: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x72c870: LeaveFrame
    //     0x72c870: mov             SP, fp
    //     0x72c874: ldp             fp, lr, [SP], #0x10
    // 0x72c878: ret
    //     0x72c878: ret             
    // 0x72c87c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c87c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c880: b               #0x72c85c
  }
  _ toMap(/* No info */) {
    // ** addr: 0x72c884, size: 0xb0
    // 0x72c884: EnterFrame
    //     0x72c884: stp             fp, lr, [SP, #-0x10]!
    //     0x72c888: mov             fp, SP
    // 0x72c88c: AllocStack(0x18)
    //     0x72c88c: sub             SP, SP, #0x18
    // 0x72c890: SetupParameters(Image this /* r1 => r0, fp-0x8 */)
    //     0x72c890: mov             x0, x1
    //     0x72c894: stur            x1, [fp, #-8]
    // 0x72c898: CheckStackOverflow
    //     0x72c898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c89c: cmp             SP, x16
    //     0x72c8a0: b.ls            #0x72c92c
    // 0x72c8a4: r1 = Null
    //     0x72c8a4: mov             x1, NULL
    // 0x72c8a8: r2 = 16
    //     0x72c8a8: movz            x2, #0x10
    // 0x72c8ac: r0 = AllocateArray()
    //     0x72c8ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72c8b0: r16 = "thumbnail"
    //     0x72c8b0: add             x16, PP, #0x19, lsl #12  ; [pp+0x19f80] "thumbnail"
    //     0x72c8b4: ldr             x16, [x16, #0xf80]
    // 0x72c8b8: StoreField: r0->field_f = r16
    //     0x72c8b8: stur            w16, [x0, #0xf]
    // 0x72c8bc: ldur            x1, [fp, #-8]
    // 0x72c8c0: LoadField: r2 = r1->field_7
    //     0x72c8c0: ldur            w2, [x1, #7]
    // 0x72c8c4: DecompressPointer r2
    //     0x72c8c4: add             x2, x2, HEAP, lsl #32
    // 0x72c8c8: StoreField: r0->field_13 = r2
    //     0x72c8c8: stur            w2, [x0, #0x13]
    // 0x72c8cc: r16 = "medium"
    //     0x72c8cc: add             x16, PP, #0x19, lsl #12  ; [pp+0x19f98] "medium"
    //     0x72c8d0: ldr             x16, [x16, #0xf98]
    // 0x72c8d4: ArrayStore: r0[0] = r16  ; List_4
    //     0x72c8d4: stur            w16, [x0, #0x17]
    // 0x72c8d8: LoadField: r2 = r1->field_b
    //     0x72c8d8: ldur            w2, [x1, #0xb]
    // 0x72c8dc: DecompressPointer r2
    //     0x72c8dc: add             x2, x2, HEAP, lsl #32
    // 0x72c8e0: StoreField: r0->field_1b = r2
    //     0x72c8e0: stur            w2, [x0, #0x1b]
    // 0x72c8e4: r16 = "full"
    //     0x72c8e4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19fb0] "full"
    //     0x72c8e8: ldr             x16, [x16, #0xfb0]
    // 0x72c8ec: StoreField: r0->field_1f = r16
    //     0x72c8ec: stur            w16, [x0, #0x1f]
    // 0x72c8f0: LoadField: r2 = r1->field_f
    //     0x72c8f0: ldur            w2, [x1, #0xf]
    // 0x72c8f4: DecompressPointer r2
    //     0x72c8f4: add             x2, x2, HEAP, lsl #32
    // 0x72c8f8: StoreField: r0->field_23 = r2
    //     0x72c8f8: stur            w2, [x0, #0x23]
    // 0x72c8fc: r16 = "caption"
    //     0x72c8fc: add             x16, PP, #0x19, lsl #12  ; [pp+0x19fc8] "caption"
    //     0x72c900: ldr             x16, [x16, #0xfc8]
    // 0x72c904: StoreField: r0->field_27 = r16
    //     0x72c904: stur            w16, [x0, #0x27]
    // 0x72c908: LoadField: r2 = r1->field_13
    //     0x72c908: ldur            w2, [x1, #0x13]
    // 0x72c90c: DecompressPointer r2
    //     0x72c90c: add             x2, x2, HEAP, lsl #32
    // 0x72c910: StoreField: r0->field_2b = r2
    //     0x72c910: stur            w2, [x0, #0x2b]
    // 0x72c914: r16 = <String, dynamic>
    //     0x72c914: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x72c918: stp             x0, x16, [SP]
    // 0x72c91c: r0 = Map._fromLiteral()
    //     0x72c91c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x72c920: LeaveFrame
    //     0x72c920: mov             SP, fp
    //     0x72c924: ldp             fp, lr, [SP], #0x10
    // 0x72c928: ret
    //     0x72c928: ret             
    // 0x72c92c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c92c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c930: b               #0x72c8a4
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbe48, size: 0x98
    // 0xbdbe48: EnterFrame
    //     0xbdbe48: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbe4c: mov             fp, SP
    // 0xbdbe50: AllocStack(0x28)
    //     0xbdbe50: sub             SP, SP, #0x28
    // 0xbdbe54: r0 = 8
    //     0xbdbe54: movz            x0, #0x8
    // 0xbdbe58: LoadField: r3 = r1->field_7
    //     0xbdbe58: ldur            w3, [x1, #7]
    // 0xbdbe5c: DecompressPointer r3
    //     0xbdbe5c: add             x3, x3, HEAP, lsl #32
    // 0xbdbe60: stur            x3, [fp, #-0x20]
    // 0xbdbe64: LoadField: r4 = r1->field_b
    //     0xbdbe64: ldur            w4, [x1, #0xb]
    // 0xbdbe68: DecompressPointer r4
    //     0xbdbe68: add             x4, x4, HEAP, lsl #32
    // 0xbdbe6c: stur            x4, [fp, #-0x18]
    // 0xbdbe70: LoadField: r5 = r1->field_f
    //     0xbdbe70: ldur            w5, [x1, #0xf]
    // 0xbdbe74: DecompressPointer r5
    //     0xbdbe74: add             x5, x5, HEAP, lsl #32
    // 0xbdbe78: stur            x5, [fp, #-0x10]
    // 0xbdbe7c: LoadField: r6 = r1->field_13
    //     0xbdbe7c: ldur            w6, [x1, #0x13]
    // 0xbdbe80: DecompressPointer r6
    //     0xbdbe80: add             x6, x6, HEAP, lsl #32
    // 0xbdbe84: mov             x2, x0
    // 0xbdbe88: stur            x6, [fp, #-8]
    // 0xbdbe8c: r1 = Null
    //     0xbdbe8c: mov             x1, NULL
    // 0xbdbe90: r0 = AllocateArray()
    //     0xbdbe90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbe94: mov             x2, x0
    // 0xbdbe98: ldur            x0, [fp, #-0x20]
    // 0xbdbe9c: stur            x2, [fp, #-0x28]
    // 0xbdbea0: StoreField: r2->field_f = r0
    //     0xbdbea0: stur            w0, [x2, #0xf]
    // 0xbdbea4: ldur            x0, [fp, #-0x18]
    // 0xbdbea8: StoreField: r2->field_13 = r0
    //     0xbdbea8: stur            w0, [x2, #0x13]
    // 0xbdbeac: ldur            x0, [fp, #-0x10]
    // 0xbdbeb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdbeb0: stur            w0, [x2, #0x17]
    // 0xbdbeb4: ldur            x0, [fp, #-8]
    // 0xbdbeb8: StoreField: r2->field_1b = r0
    //     0xbdbeb8: stur            w0, [x2, #0x1b]
    // 0xbdbebc: r1 = <Object?>
    //     0xbdbebc: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbec0: r0 = AllocateGrowableArray()
    //     0xbdbec0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbec4: ldur            x1, [fp, #-0x28]
    // 0xbdbec8: StoreField: r0->field_f = r1
    //     0xbdbec8: stur            w1, [x0, #0xf]
    // 0xbdbecc: r1 = 8
    //     0xbdbecc: movz            x1, #0x8
    // 0xbdbed0: StoreField: r0->field_b = r1
    //     0xbdbed0: stur            w1, [x0, #0xb]
    // 0xbdbed4: LeaveFrame
    //     0xbdbed4: mov             SP, fp
    //     0xbdbed8: ldp             fp, lr, [SP], #0x10
    // 0xbdbedc: ret
    //     0xbdbedc: ret             
  }
}
