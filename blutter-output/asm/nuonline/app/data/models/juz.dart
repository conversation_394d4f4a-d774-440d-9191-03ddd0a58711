// lib: , url: package:nuonline/app/data/models/juz.dart

// class id: 1050027, size: 0x8
class :: {
}

// class id: 1607, size: 0x30, field offset: 0x14
class <PERSON><PERSON> extends HiveObject {

  String toJson(Juz) {
    // ** addr: 0x83c08c, size: 0x48
    // 0x83c08c: EnterFrame
    //     0x83c08c: stp             fp, lr, [SP, #-0x10]!
    //     0x83c090: mov             fp, SP
    // 0x83c094: CheckStackOverflow
    //     0x83c094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83c098: cmp             SP, x16
    //     0x83c09c: b.ls            #0x83c0b4
    // 0x83c0a0: ldr             x1, [fp, #0x10]
    // 0x83c0a4: r0 = to<PERSON>son()
    //     0x83c0a4: bl              #0x83c0bc  ; [package:nuonline/app/data/models/juz.dart] Juz::toJson
    // 0x83c0a8: LeaveFrame
    //     0x83c0a8: mov             SP, fp
    //     0x83c0ac: ldp             fp, lr, [SP], #0x10
    // 0x83c0b0: ret
    //     0x83c0b0: ret             
    // 0x83c0b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83c0b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83c0b8: b               #0x83c0a0
  }
  String toJson(Juz) {
    // ** addr: 0x83c0bc, size: 0x3c
    // 0x83c0bc: EnterFrame
    //     0x83c0bc: stp             fp, lr, [SP, #-0x10]!
    //     0x83c0c0: mov             fp, SP
    // 0x83c0c4: CheckStackOverflow
    //     0x83c0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83c0c8: cmp             SP, x16
    //     0x83c0cc: b.ls            #0x83c0f0
    // 0x83c0d0: r0 = toMap()
    //     0x83c0d0: bl              #0x83c0f8  ; [package:nuonline/app/data/models/juz.dart] Juz::toMap
    // 0x83c0d4: mov             x2, x0
    // 0x83c0d8: r1 = Instance_JsonCodec
    //     0x83c0d8: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x83c0dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x83c0dc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83c0e0: r0 = encode()
    //     0x83c0e0: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x83c0e4: LeaveFrame
    //     0x83c0e4: mov             SP, fp
    //     0x83c0e8: ldp             fp, lr, [SP], #0x10
    // 0x83c0ec: ret
    //     0x83c0ec: ret             
    // 0x83c0f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83c0f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83c0f4: b               #0x83c0d0
  }
  _ toMap(/* No info */) {
    // ** addr: 0x83c0f8, size: 0x19c
    // 0x83c0f8: EnterFrame
    //     0x83c0f8: stp             fp, lr, [SP, #-0x10]!
    //     0x83c0fc: mov             fp, SP
    // 0x83c100: AllocStack(0x18)
    //     0x83c100: sub             SP, SP, #0x18
    // 0x83c104: SetupParameters(Juz this /* r1 => r0, fp-0x8 */)
    //     0x83c104: mov             x0, x1
    //     0x83c108: stur            x1, [fp, #-8]
    // 0x83c10c: CheckStackOverflow
    //     0x83c10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83c110: cmp             SP, x16
    //     0x83c114: b.ls            #0x83c28c
    // 0x83c118: r1 = Null
    //     0x83c118: mov             x1, NULL
    // 0x83c11c: r2 = 20
    //     0x83c11c: movz            x2, #0x14
    // 0x83c120: r0 = AllocateArray()
    //     0x83c120: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83c124: mov             x2, x0
    // 0x83c128: r16 = "id"
    //     0x83c128: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x83c12c: ldr             x16, [x16, #0x740]
    // 0x83c130: StoreField: r2->field_f = r16
    //     0x83c130: stur            w16, [x2, #0xf]
    // 0x83c134: ldur            x3, [fp, #-8]
    // 0x83c138: LoadField: r4 = r3->field_13
    //     0x83c138: ldur            x4, [x3, #0x13]
    // 0x83c13c: r0 = BoxInt64Instr(r4)
    //     0x83c13c: sbfiz           x0, x4, #1, #0x1f
    //     0x83c140: cmp             x4, x0, asr #1
    //     0x83c144: b.eq            #0x83c150
    //     0x83c148: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83c14c: stur            x4, [x0, #7]
    // 0x83c150: mov             x1, x2
    // 0x83c154: ArrayStore: r1[1] = r0  ; List_4
    //     0x83c154: add             x25, x1, #0x13
    //     0x83c158: str             w0, [x25]
    //     0x83c15c: tbz             w0, #0, #0x83c178
    //     0x83c160: ldurb           w16, [x1, #-1]
    //     0x83c164: ldurb           w17, [x0, #-1]
    //     0x83c168: and             x16, x17, x16, lsr #2
    //     0x83c16c: tst             x16, HEAP, lsr #32
    //     0x83c170: b.eq            #0x83c178
    //     0x83c174: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83c178: r16 = "surahId"
    //     0x83c178: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f8] "surahId"
    //     0x83c17c: ldr             x16, [x16, #0xf8]
    // 0x83c180: ArrayStore: r2[0] = r16  ; List_4
    //     0x83c180: stur            w16, [x2, #0x17]
    // 0x83c184: LoadField: r4 = r3->field_1b
    //     0x83c184: ldur            x4, [x3, #0x1b]
    // 0x83c188: r0 = BoxInt64Instr(r4)
    //     0x83c188: sbfiz           x0, x4, #1, #0x1f
    //     0x83c18c: cmp             x4, x0, asr #1
    //     0x83c190: b.eq            #0x83c19c
    //     0x83c194: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83c198: stur            x4, [x0, #7]
    // 0x83c19c: mov             x1, x2
    // 0x83c1a0: ArrayStore: r1[3] = r0  ; List_4
    //     0x83c1a0: add             x25, x1, #0x1b
    //     0x83c1a4: str             w0, [x25]
    //     0x83c1a8: tbz             w0, #0, #0x83c1c4
    //     0x83c1ac: ldurb           w16, [x1, #-1]
    //     0x83c1b0: ldurb           w17, [x0, #-1]
    //     0x83c1b4: and             x16, x17, x16, lsr #2
    //     0x83c1b8: tst             x16, HEAP, lsr #32
    //     0x83c1bc: b.eq            #0x83c1c4
    //     0x83c1c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83c1c4: r16 = "startText"
    //     0x83c1c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] "startText"
    //     0x83c1c8: ldr             x16, [x16, #0x650]
    // 0x83c1cc: StoreField: r2->field_1f = r16
    //     0x83c1cc: stur            w16, [x2, #0x1f]
    // 0x83c1d0: LoadField: r0 = r3->field_23
    //     0x83c1d0: ldur            w0, [x3, #0x23]
    // 0x83c1d4: DecompressPointer r0
    //     0x83c1d4: add             x0, x0, HEAP, lsl #32
    // 0x83c1d8: mov             x1, x2
    // 0x83c1dc: ArrayStore: r1[5] = r0  ; List_4
    //     0x83c1dc: add             x25, x1, #0x23
    //     0x83c1e0: str             w0, [x25]
    //     0x83c1e4: tbz             w0, #0, #0x83c200
    //     0x83c1e8: ldurb           w16, [x1, #-1]
    //     0x83c1ec: ldurb           w17, [x0, #-1]
    //     0x83c1f0: and             x16, x17, x16, lsr #2
    //     0x83c1f4: tst             x16, HEAP, lsr #32
    //     0x83c1f8: b.eq            #0x83c200
    //     0x83c1fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83c200: r16 = "endText"
    //     0x83c200: add             x16, PP, #0x23, lsl #12  ; [pp+0x23658] "endText"
    //     0x83c204: ldr             x16, [x16, #0x658]
    // 0x83c208: StoreField: r2->field_27 = r16
    //     0x83c208: stur            w16, [x2, #0x27]
    // 0x83c20c: LoadField: r0 = r3->field_27
    //     0x83c20c: ldur            w0, [x3, #0x27]
    // 0x83c210: DecompressPointer r0
    //     0x83c210: add             x0, x0, HEAP, lsl #32
    // 0x83c214: mov             x1, x2
    // 0x83c218: ArrayStore: r1[7] = r0  ; List_4
    //     0x83c218: add             x25, x1, #0x2b
    //     0x83c21c: str             w0, [x25]
    //     0x83c220: tbz             w0, #0, #0x83c23c
    //     0x83c224: ldurb           w16, [x1, #-1]
    //     0x83c228: ldurb           w17, [x0, #-1]
    //     0x83c22c: and             x16, x17, x16, lsr #2
    //     0x83c230: tst             x16, HEAP, lsr #32
    //     0x83c234: b.eq            #0x83c23c
    //     0x83c238: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83c23c: r16 = "text"
    //     0x83c23c: ldr             x16, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0x83c240: StoreField: r2->field_2f = r16
    //     0x83c240: stur            w16, [x2, #0x2f]
    // 0x83c244: LoadField: r0 = r3->field_2b
    //     0x83c244: ldur            w0, [x3, #0x2b]
    // 0x83c248: DecompressPointer r0
    //     0x83c248: add             x0, x0, HEAP, lsl #32
    // 0x83c24c: mov             x1, x2
    // 0x83c250: ArrayStore: r1[9] = r0  ; List_4
    //     0x83c250: add             x25, x1, #0x33
    //     0x83c254: str             w0, [x25]
    //     0x83c258: tbz             w0, #0, #0x83c274
    //     0x83c25c: ldurb           w16, [x1, #-1]
    //     0x83c260: ldurb           w17, [x0, #-1]
    //     0x83c264: and             x16, x17, x16, lsr #2
    //     0x83c268: tst             x16, HEAP, lsr #32
    //     0x83c26c: b.eq            #0x83c274
    //     0x83c270: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83c274: r16 = <String, dynamic>
    //     0x83c274: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x83c278: stp             x2, x16, [SP]
    // 0x83c27c: r0 = Map._fromLiteral()
    //     0x83c27c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83c280: LeaveFrame
    //     0x83c280: mov             SP, fp
    //     0x83c284: ldp             fp, lr, [SP], #0x10
    // 0x83c288: ret
    //     0x83c288: ret             
    // 0x83c28c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83c28c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83c290: b               #0x83c118
  }
  factory _ Juz.fromMap(/* No info */) {
    // ** addr: 0x922abc, size: 0x28c
    // 0x922abc: EnterFrame
    //     0x922abc: stp             fp, lr, [SP, #-0x10]!
    //     0x922ac0: mov             fp, SP
    // 0x922ac4: AllocStack(0x40)
    //     0x922ac4: sub             SP, SP, #0x40
    // 0x922ac8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x922ac8: mov             x3, x2
    //     0x922acc: stur            x2, [fp, #-8]
    // 0x922ad0: CheckStackOverflow
    //     0x922ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922ad4: cmp             SP, x16
    //     0x922ad8: b.ls            #0x922d40
    // 0x922adc: r0 = LoadClassIdInstr(r3)
    //     0x922adc: ldur            x0, [x3, #-1]
    //     0x922ae0: ubfx            x0, x0, #0xc, #0x14
    // 0x922ae4: mov             x1, x3
    // 0x922ae8: r2 = "id"
    //     0x922ae8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x922aec: ldr             x2, [x2, #0x740]
    // 0x922af0: r0 = GDT[cid_x0 + -0x114]()
    //     0x922af0: sub             lr, x0, #0x114
    //     0x922af4: ldr             lr, [x21, lr, lsl #3]
    //     0x922af8: blr             lr
    // 0x922afc: mov             x3, x0
    // 0x922b00: r2 = Null
    //     0x922b00: mov             x2, NULL
    // 0x922b04: r1 = Null
    //     0x922b04: mov             x1, NULL
    // 0x922b08: stur            x3, [fp, #-0x10]
    // 0x922b0c: branchIfSmi(r0, 0x922b34)
    //     0x922b0c: tbz             w0, #0, #0x922b34
    // 0x922b10: r4 = LoadClassIdInstr(r0)
    //     0x922b10: ldur            x4, [x0, #-1]
    //     0x922b14: ubfx            x4, x4, #0xc, #0x14
    // 0x922b18: sub             x4, x4, #0x3c
    // 0x922b1c: cmp             x4, #1
    // 0x922b20: b.ls            #0x922b34
    // 0x922b24: r8 = int
    //     0x922b24: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x922b28: r3 = Null
    //     0x922b28: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc30] Null
    //     0x922b2c: ldr             x3, [x3, #0xc30]
    // 0x922b30: r0 = int()
    //     0x922b30: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x922b34: ldur            x3, [fp, #-8]
    // 0x922b38: r0 = LoadClassIdInstr(r3)
    //     0x922b38: ldur            x0, [x3, #-1]
    //     0x922b3c: ubfx            x0, x0, #0xc, #0x14
    // 0x922b40: mov             x1, x3
    // 0x922b44: r2 = "surah_id"
    //     0x922b44: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x922b48: ldr             x2, [x2, #0x340]
    // 0x922b4c: r0 = GDT[cid_x0 + -0x114]()
    //     0x922b4c: sub             lr, x0, #0x114
    //     0x922b50: ldr             lr, [x21, lr, lsl #3]
    //     0x922b54: blr             lr
    // 0x922b58: mov             x3, x0
    // 0x922b5c: r2 = Null
    //     0x922b5c: mov             x2, NULL
    // 0x922b60: r1 = Null
    //     0x922b60: mov             x1, NULL
    // 0x922b64: stur            x3, [fp, #-0x18]
    // 0x922b68: branchIfSmi(r0, 0x922b90)
    //     0x922b68: tbz             w0, #0, #0x922b90
    // 0x922b6c: r4 = LoadClassIdInstr(r0)
    //     0x922b6c: ldur            x4, [x0, #-1]
    //     0x922b70: ubfx            x4, x4, #0xc, #0x14
    // 0x922b74: sub             x4, x4, #0x3c
    // 0x922b78: cmp             x4, #1
    // 0x922b7c: b.ls            #0x922b90
    // 0x922b80: r8 = int
    //     0x922b80: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x922b84: r3 = Null
    //     0x922b84: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc40] Null
    //     0x922b88: ldr             x3, [x3, #0xc40]
    // 0x922b8c: r0 = int()
    //     0x922b8c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x922b90: ldur            x3, [fp, #-8]
    // 0x922b94: r0 = LoadClassIdInstr(r3)
    //     0x922b94: ldur            x0, [x3, #-1]
    //     0x922b98: ubfx            x0, x0, #0xc, #0x14
    // 0x922b9c: mov             x1, x3
    // 0x922ba0: r2 = "start_text"
    //     0x922ba0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dc50] "start_text"
    //     0x922ba4: ldr             x2, [x2, #0xc50]
    // 0x922ba8: r0 = GDT[cid_x0 + -0x114]()
    //     0x922ba8: sub             lr, x0, #0x114
    //     0x922bac: ldr             lr, [x21, lr, lsl #3]
    //     0x922bb0: blr             lr
    // 0x922bb4: mov             x3, x0
    // 0x922bb8: r2 = Null
    //     0x922bb8: mov             x2, NULL
    // 0x922bbc: r1 = Null
    //     0x922bbc: mov             x1, NULL
    // 0x922bc0: stur            x3, [fp, #-0x20]
    // 0x922bc4: r4 = 60
    //     0x922bc4: movz            x4, #0x3c
    // 0x922bc8: branchIfSmi(r0, 0x922bd4)
    //     0x922bc8: tbz             w0, #0, #0x922bd4
    // 0x922bcc: r4 = LoadClassIdInstr(r0)
    //     0x922bcc: ldur            x4, [x0, #-1]
    //     0x922bd0: ubfx            x4, x4, #0xc, #0x14
    // 0x922bd4: sub             x4, x4, #0x5e
    // 0x922bd8: cmp             x4, #1
    // 0x922bdc: b.ls            #0x922bf0
    // 0x922be0: r8 = String
    //     0x922be0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x922be4: r3 = Null
    //     0x922be4: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc58] Null
    //     0x922be8: ldr             x3, [x3, #0xc58]
    // 0x922bec: r0 = String()
    //     0x922bec: bl              #0xed43b0  ; IsType_String_Stub
    // 0x922bf0: ldur            x3, [fp, #-8]
    // 0x922bf4: r0 = LoadClassIdInstr(r3)
    //     0x922bf4: ldur            x0, [x3, #-1]
    //     0x922bf8: ubfx            x0, x0, #0xc, #0x14
    // 0x922bfc: mov             x1, x3
    // 0x922c00: r2 = "end_text"
    //     0x922c00: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dc68] "end_text"
    //     0x922c04: ldr             x2, [x2, #0xc68]
    // 0x922c08: r0 = GDT[cid_x0 + -0x114]()
    //     0x922c08: sub             lr, x0, #0x114
    //     0x922c0c: ldr             lr, [x21, lr, lsl #3]
    //     0x922c10: blr             lr
    // 0x922c14: mov             x3, x0
    // 0x922c18: r2 = Null
    //     0x922c18: mov             x2, NULL
    // 0x922c1c: r1 = Null
    //     0x922c1c: mov             x1, NULL
    // 0x922c20: stur            x3, [fp, #-0x28]
    // 0x922c24: r4 = 60
    //     0x922c24: movz            x4, #0x3c
    // 0x922c28: branchIfSmi(r0, 0x922c34)
    //     0x922c28: tbz             w0, #0, #0x922c34
    // 0x922c2c: r4 = LoadClassIdInstr(r0)
    //     0x922c2c: ldur            x4, [x0, #-1]
    //     0x922c30: ubfx            x4, x4, #0xc, #0x14
    // 0x922c34: sub             x4, x4, #0x5e
    // 0x922c38: cmp             x4, #1
    // 0x922c3c: b.ls            #0x922c50
    // 0x922c40: r8 = String
    //     0x922c40: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x922c44: r3 = Null
    //     0x922c44: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc70] Null
    //     0x922c48: ldr             x3, [x3, #0xc70]
    // 0x922c4c: r0 = String()
    //     0x922c4c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x922c50: ldur            x1, [fp, #-8]
    // 0x922c54: r0 = LoadClassIdInstr(r1)
    //     0x922c54: ldur            x0, [x1, #-1]
    //     0x922c58: ubfx            x0, x0, #0xc, #0x14
    // 0x922c5c: r2 = "text"
    //     0x922c5c: ldr             x2, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0x922c60: r0 = GDT[cid_x0 + -0x114]()
    //     0x922c60: sub             lr, x0, #0x114
    //     0x922c64: ldr             lr, [x21, lr, lsl #3]
    //     0x922c68: blr             lr
    // 0x922c6c: mov             x3, x0
    // 0x922c70: r2 = Null
    //     0x922c70: mov             x2, NULL
    // 0x922c74: r1 = Null
    //     0x922c74: mov             x1, NULL
    // 0x922c78: stur            x3, [fp, #-8]
    // 0x922c7c: r4 = 60
    //     0x922c7c: movz            x4, #0x3c
    // 0x922c80: branchIfSmi(r0, 0x922c8c)
    //     0x922c80: tbz             w0, #0, #0x922c8c
    // 0x922c84: r4 = LoadClassIdInstr(r0)
    //     0x922c84: ldur            x4, [x0, #-1]
    //     0x922c88: ubfx            x4, x4, #0xc, #0x14
    // 0x922c8c: sub             x4, x4, #0x5e
    // 0x922c90: cmp             x4, #1
    // 0x922c94: b.ls            #0x922ca8
    // 0x922c98: r8 = String
    //     0x922c98: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x922c9c: r3 = Null
    //     0x922c9c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc80] Null
    //     0x922ca0: ldr             x3, [x3, #0xc80]
    // 0x922ca4: r0 = String()
    //     0x922ca4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x922ca8: ldur            x0, [fp, #-0x10]
    // 0x922cac: r1 = LoadInt32Instr(r0)
    //     0x922cac: sbfx            x1, x0, #1, #0x1f
    //     0x922cb0: tbz             w0, #0, #0x922cb8
    //     0x922cb4: ldur            x1, [x0, #7]
    // 0x922cb8: stur            x1, [fp, #-0x30]
    // 0x922cbc: r0 = Juz()
    //     0x922cbc: bl              #0x922d48  ; AllocateJuzStub -> Juz (size=0x30)
    // 0x922cc0: mov             x1, x0
    // 0x922cc4: ldur            x0, [fp, #-0x30]
    // 0x922cc8: stur            x1, [fp, #-0x10]
    // 0x922ccc: StoreField: r1->field_13 = r0
    //     0x922ccc: stur            x0, [x1, #0x13]
    // 0x922cd0: ldur            x0, [fp, #-0x18]
    // 0x922cd4: r2 = LoadInt32Instr(r0)
    //     0x922cd4: sbfx            x2, x0, #1, #0x1f
    //     0x922cd8: tbz             w0, #0, #0x922ce0
    //     0x922cdc: ldur            x2, [x0, #7]
    // 0x922ce0: StoreField: r1->field_1b = r2
    //     0x922ce0: stur            x2, [x1, #0x1b]
    // 0x922ce4: ldur            x0, [fp, #-0x20]
    // 0x922ce8: StoreField: r1->field_23 = r0
    //     0x922ce8: stur            w0, [x1, #0x23]
    // 0x922cec: ldur            x0, [fp, #-0x28]
    // 0x922cf0: StoreField: r1->field_27 = r0
    //     0x922cf0: stur            w0, [x1, #0x27]
    // 0x922cf4: ldur            x0, [fp, #-8]
    // 0x922cf8: StoreField: r1->field_2b = r0
    //     0x922cf8: stur            w0, [x1, #0x2b]
    // 0x922cfc: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x922cfc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x922d00: ldr             x16, [x16, #0x9f8]
    // 0x922d04: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x922d08: stp             lr, x16, [SP]
    // 0x922d0c: r0 = Map._fromLiteral()
    //     0x922d0c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x922d10: ldur            x1, [fp, #-0x10]
    // 0x922d14: StoreField: r1->field_f = r0
    //     0x922d14: stur            w0, [x1, #0xf]
    //     0x922d18: ldurb           w16, [x1, #-1]
    //     0x922d1c: ldurb           w17, [x0, #-1]
    //     0x922d20: and             x16, x17, x16, lsr #2
    //     0x922d24: tst             x16, HEAP, lsr #32
    //     0x922d28: b.eq            #0x922d30
    //     0x922d2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x922d30: mov             x0, x1
    // 0x922d34: LeaveFrame
    //     0x922d34: mov             SP, fp
    //     0x922d38: ldp             fp, lr, [SP], #0x10
    // 0x922d3c: ret
    //     0x922d3c: ret             
    // 0x922d40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922d40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922d44: b               #0x922adc
  }
}

// class id: 1657, size: 0x14, field offset: 0xc
class JuzAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa62608, size: 0x458
    // 0xa62608: EnterFrame
    //     0xa62608: stp             fp, lr, [SP, #-0x10]!
    //     0xa6260c: mov             fp, SP
    // 0xa62610: AllocStack(0x58)
    //     0xa62610: sub             SP, SP, #0x58
    // 0xa62614: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa62614: stur            x2, [fp, #-0x20]
    // 0xa62618: CheckStackOverflow
    //     0xa62618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6261c: cmp             SP, x16
    //     0xa62620: b.ls            #0xa62a48
    // 0xa62624: LoadField: r3 = r2->field_23
    //     0xa62624: ldur            x3, [x2, #0x23]
    // 0xa62628: add             x0, x3, #1
    // 0xa6262c: LoadField: r1 = r2->field_1b
    //     0xa6262c: ldur            x1, [x2, #0x1b]
    // 0xa62630: cmp             x0, x1
    // 0xa62634: b.gt            #0xa629ec
    // 0xa62638: LoadField: r4 = r2->field_7
    //     0xa62638: ldur            w4, [x2, #7]
    // 0xa6263c: DecompressPointer r4
    //     0xa6263c: add             x4, x4, HEAP, lsl #32
    // 0xa62640: stur            x4, [fp, #-0x18]
    // 0xa62644: StoreField: r2->field_23 = r0
    //     0xa62644: stur            x0, [x2, #0x23]
    // 0xa62648: LoadField: r0 = r4->field_13
    //     0xa62648: ldur            w0, [x4, #0x13]
    // 0xa6264c: r5 = LoadInt32Instr(r0)
    //     0xa6264c: sbfx            x5, x0, #1, #0x1f
    // 0xa62650: mov             x0, x5
    // 0xa62654: mov             x1, x3
    // 0xa62658: stur            x5, [fp, #-0x10]
    // 0xa6265c: cmp             x1, x0
    // 0xa62660: b.hs            #0xa62a50
    // 0xa62664: LoadField: r0 = r4->field_7
    //     0xa62664: ldur            x0, [x4, #7]
    // 0xa62668: ldrb            w1, [x0, x3]
    // 0xa6266c: stur            x1, [fp, #-8]
    // 0xa62670: r16 = <int, dynamic>
    //     0xa62670: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa62674: ldr             x16, [x16, #0xac0]
    // 0xa62678: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6267c: stp             lr, x16, [SP]
    // 0xa62680: r0 = Map._fromLiteral()
    //     0xa62680: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa62684: mov             x2, x0
    // 0xa62688: stur            x2, [fp, #-0x38]
    // 0xa6268c: r6 = 0
    //     0xa6268c: movz            x6, #0
    // 0xa62690: ldur            x3, [fp, #-0x20]
    // 0xa62694: ldur            x4, [fp, #-0x18]
    // 0xa62698: ldur            x5, [fp, #-8]
    // 0xa6269c: stur            x6, [fp, #-0x30]
    // 0xa626a0: CheckStackOverflow
    //     0xa626a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa626a4: cmp             SP, x16
    //     0xa626a8: b.ls            #0xa62a54
    // 0xa626ac: cmp             x6, x5
    // 0xa626b0: b.ge            #0xa6273c
    // 0xa626b4: LoadField: r7 = r3->field_23
    //     0xa626b4: ldur            x7, [x3, #0x23]
    // 0xa626b8: add             x0, x7, #1
    // 0xa626bc: LoadField: r1 = r3->field_1b
    //     0xa626bc: ldur            x1, [x3, #0x1b]
    // 0xa626c0: cmp             x0, x1
    // 0xa626c4: b.gt            #0xa62a14
    // 0xa626c8: StoreField: r3->field_23 = r0
    //     0xa626c8: stur            x0, [x3, #0x23]
    // 0xa626cc: ldur            x0, [fp, #-0x10]
    // 0xa626d0: mov             x1, x7
    // 0xa626d4: cmp             x1, x0
    // 0xa626d8: b.hs            #0xa62a5c
    // 0xa626dc: LoadField: r0 = r4->field_7
    //     0xa626dc: ldur            x0, [x4, #7]
    // 0xa626e0: ldrb            w8, [x0, x7]
    // 0xa626e4: mov             x1, x3
    // 0xa626e8: stur            x8, [fp, #-0x28]
    // 0xa626ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa626ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa626f0: r0 = read()
    //     0xa626f0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa626f4: mov             x1, x0
    // 0xa626f8: ldur            x0, [fp, #-0x28]
    // 0xa626fc: lsl             x2, x0, #1
    // 0xa62700: r16 = LoadInt32Instr(r2)
    //     0xa62700: sbfx            x16, x2, #1, #0x1f
    // 0xa62704: r17 = 11601
    //     0xa62704: movz            x17, #0x2d51
    // 0xa62708: mul             x0, x16, x17
    // 0xa6270c: umulh           x16, x16, x17
    // 0xa62710: eor             x0, x0, x16
    // 0xa62714: r0 = 0
    //     0xa62714: eor             x0, x0, x0, lsr #32
    // 0xa62718: ubfiz           x0, x0, #1, #0x1e
    // 0xa6271c: r5 = LoadInt32Instr(r0)
    //     0xa6271c: sbfx            x5, x0, #1, #0x1f
    // 0xa62720: mov             x3, x1
    // 0xa62724: ldur            x1, [fp, #-0x38]
    // 0xa62728: r0 = _set()
    //     0xa62728: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa6272c: ldur            x0, [fp, #-0x30]
    // 0xa62730: add             x6, x0, #1
    // 0xa62734: ldur            x2, [fp, #-0x38]
    // 0xa62738: b               #0xa62690
    // 0xa6273c: mov             x0, x2
    // 0xa62740: mov             x1, x0
    // 0xa62744: r2 = 0
    //     0xa62744: movz            x2, #0
    // 0xa62748: r0 = _getValueOrData()
    //     0xa62748: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6274c: ldur            x3, [fp, #-0x38]
    // 0xa62750: LoadField: r1 = r3->field_f
    //     0xa62750: ldur            w1, [x3, #0xf]
    // 0xa62754: DecompressPointer r1
    //     0xa62754: add             x1, x1, HEAP, lsl #32
    // 0xa62758: cmp             w1, w0
    // 0xa6275c: b.ne            #0xa62768
    // 0xa62760: r4 = Null
    //     0xa62760: mov             x4, NULL
    // 0xa62764: b               #0xa6276c
    // 0xa62768: mov             x4, x0
    // 0xa6276c: mov             x0, x4
    // 0xa62770: stur            x4, [fp, #-0x18]
    // 0xa62774: r2 = Null
    //     0xa62774: mov             x2, NULL
    // 0xa62778: r1 = Null
    //     0xa62778: mov             x1, NULL
    // 0xa6277c: branchIfSmi(r0, 0xa627a4)
    //     0xa6277c: tbz             w0, #0, #0xa627a4
    // 0xa62780: r4 = LoadClassIdInstr(r0)
    //     0xa62780: ldur            x4, [x0, #-1]
    //     0xa62784: ubfx            x4, x4, #0xc, #0x14
    // 0xa62788: sub             x4, x4, #0x3c
    // 0xa6278c: cmp             x4, #1
    // 0xa62790: b.ls            #0xa627a4
    // 0xa62794: r8 = int
    //     0xa62794: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa62798: r3 = Null
    //     0xa62798: add             x3, PP, #0x21, lsl #12  ; [pp+0x21360] Null
    //     0xa6279c: ldr             x3, [x3, #0x360]
    // 0xa627a0: r0 = int()
    //     0xa627a0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa627a4: ldur            x1, [fp, #-0x38]
    // 0xa627a8: r2 = 2
    //     0xa627a8: movz            x2, #0x2
    // 0xa627ac: r0 = _getValueOrData()
    //     0xa627ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa627b0: ldur            x3, [fp, #-0x38]
    // 0xa627b4: LoadField: r1 = r3->field_f
    //     0xa627b4: ldur            w1, [x3, #0xf]
    // 0xa627b8: DecompressPointer r1
    //     0xa627b8: add             x1, x1, HEAP, lsl #32
    // 0xa627bc: cmp             w1, w0
    // 0xa627c0: b.ne            #0xa627cc
    // 0xa627c4: r4 = Null
    //     0xa627c4: mov             x4, NULL
    // 0xa627c8: b               #0xa627d0
    // 0xa627cc: mov             x4, x0
    // 0xa627d0: mov             x0, x4
    // 0xa627d4: stur            x4, [fp, #-0x20]
    // 0xa627d8: r2 = Null
    //     0xa627d8: mov             x2, NULL
    // 0xa627dc: r1 = Null
    //     0xa627dc: mov             x1, NULL
    // 0xa627e0: branchIfSmi(r0, 0xa62808)
    //     0xa627e0: tbz             w0, #0, #0xa62808
    // 0xa627e4: r4 = LoadClassIdInstr(r0)
    //     0xa627e4: ldur            x4, [x0, #-1]
    //     0xa627e8: ubfx            x4, x4, #0xc, #0x14
    // 0xa627ec: sub             x4, x4, #0x3c
    // 0xa627f0: cmp             x4, #1
    // 0xa627f4: b.ls            #0xa62808
    // 0xa627f8: r8 = int
    //     0xa627f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa627fc: r3 = Null
    //     0xa627fc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21370] Null
    //     0xa62800: ldr             x3, [x3, #0x370]
    // 0xa62804: r0 = int()
    //     0xa62804: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa62808: ldur            x1, [fp, #-0x38]
    // 0xa6280c: r2 = 4
    //     0xa6280c: movz            x2, #0x4
    // 0xa62810: r0 = _getValueOrData()
    //     0xa62810: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62814: ldur            x3, [fp, #-0x38]
    // 0xa62818: LoadField: r1 = r3->field_f
    //     0xa62818: ldur            w1, [x3, #0xf]
    // 0xa6281c: DecompressPointer r1
    //     0xa6281c: add             x1, x1, HEAP, lsl #32
    // 0xa62820: cmp             w1, w0
    // 0xa62824: b.ne            #0xa62830
    // 0xa62828: r4 = Null
    //     0xa62828: mov             x4, NULL
    // 0xa6282c: b               #0xa62834
    // 0xa62830: mov             x4, x0
    // 0xa62834: mov             x0, x4
    // 0xa62838: stur            x4, [fp, #-0x40]
    // 0xa6283c: r2 = Null
    //     0xa6283c: mov             x2, NULL
    // 0xa62840: r1 = Null
    //     0xa62840: mov             x1, NULL
    // 0xa62844: r4 = 60
    //     0xa62844: movz            x4, #0x3c
    // 0xa62848: branchIfSmi(r0, 0xa62854)
    //     0xa62848: tbz             w0, #0, #0xa62854
    // 0xa6284c: r4 = LoadClassIdInstr(r0)
    //     0xa6284c: ldur            x4, [x0, #-1]
    //     0xa62850: ubfx            x4, x4, #0xc, #0x14
    // 0xa62854: sub             x4, x4, #0x5e
    // 0xa62858: cmp             x4, #1
    // 0xa6285c: b.ls            #0xa62870
    // 0xa62860: r8 = String
    //     0xa62860: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62864: r3 = Null
    //     0xa62864: add             x3, PP, #0x21, lsl #12  ; [pp+0x21380] Null
    //     0xa62868: ldr             x3, [x3, #0x380]
    // 0xa6286c: r0 = String()
    //     0xa6286c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62870: ldur            x1, [fp, #-0x38]
    // 0xa62874: r2 = 6
    //     0xa62874: movz            x2, #0x6
    // 0xa62878: r0 = _getValueOrData()
    //     0xa62878: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6287c: ldur            x3, [fp, #-0x38]
    // 0xa62880: LoadField: r1 = r3->field_f
    //     0xa62880: ldur            w1, [x3, #0xf]
    // 0xa62884: DecompressPointer r1
    //     0xa62884: add             x1, x1, HEAP, lsl #32
    // 0xa62888: cmp             w1, w0
    // 0xa6288c: b.ne            #0xa62898
    // 0xa62890: r4 = Null
    //     0xa62890: mov             x4, NULL
    // 0xa62894: b               #0xa6289c
    // 0xa62898: mov             x4, x0
    // 0xa6289c: mov             x0, x4
    // 0xa628a0: stur            x4, [fp, #-0x48]
    // 0xa628a4: r2 = Null
    //     0xa628a4: mov             x2, NULL
    // 0xa628a8: r1 = Null
    //     0xa628a8: mov             x1, NULL
    // 0xa628ac: r4 = 60
    //     0xa628ac: movz            x4, #0x3c
    // 0xa628b0: branchIfSmi(r0, 0xa628bc)
    //     0xa628b0: tbz             w0, #0, #0xa628bc
    // 0xa628b4: r4 = LoadClassIdInstr(r0)
    //     0xa628b4: ldur            x4, [x0, #-1]
    //     0xa628b8: ubfx            x4, x4, #0xc, #0x14
    // 0xa628bc: sub             x4, x4, #0x5e
    // 0xa628c0: cmp             x4, #1
    // 0xa628c4: b.ls            #0xa628d8
    // 0xa628c8: r8 = String
    //     0xa628c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa628cc: r3 = Null
    //     0xa628cc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21390] Null
    //     0xa628d0: ldr             x3, [x3, #0x390]
    // 0xa628d4: r0 = String()
    //     0xa628d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa628d8: ldur            x1, [fp, #-0x38]
    // 0xa628dc: r2 = 8
    //     0xa628dc: movz            x2, #0x8
    // 0xa628e0: r0 = _getValueOrData()
    //     0xa628e0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa628e4: mov             x1, x0
    // 0xa628e8: ldur            x0, [fp, #-0x38]
    // 0xa628ec: LoadField: r2 = r0->field_f
    //     0xa628ec: ldur            w2, [x0, #0xf]
    // 0xa628f0: DecompressPointer r2
    //     0xa628f0: add             x2, x2, HEAP, lsl #32
    // 0xa628f4: cmp             w2, w1
    // 0xa628f8: b.ne            #0xa62904
    // 0xa628fc: r7 = Null
    //     0xa628fc: mov             x7, NULL
    // 0xa62900: b               #0xa62908
    // 0xa62904: mov             x7, x1
    // 0xa62908: ldur            x6, [fp, #-0x18]
    // 0xa6290c: ldur            x5, [fp, #-0x20]
    // 0xa62910: ldur            x4, [fp, #-0x40]
    // 0xa62914: ldur            x3, [fp, #-0x48]
    // 0xa62918: mov             x0, x7
    // 0xa6291c: stur            x7, [fp, #-0x38]
    // 0xa62920: r2 = Null
    //     0xa62920: mov             x2, NULL
    // 0xa62924: r1 = Null
    //     0xa62924: mov             x1, NULL
    // 0xa62928: r4 = 60
    //     0xa62928: movz            x4, #0x3c
    // 0xa6292c: branchIfSmi(r0, 0xa62938)
    //     0xa6292c: tbz             w0, #0, #0xa62938
    // 0xa62930: r4 = LoadClassIdInstr(r0)
    //     0xa62930: ldur            x4, [x0, #-1]
    //     0xa62934: ubfx            x4, x4, #0xc, #0x14
    // 0xa62938: sub             x4, x4, #0x5e
    // 0xa6293c: cmp             x4, #1
    // 0xa62940: b.ls            #0xa62954
    // 0xa62944: r8 = String
    //     0xa62944: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62948: r3 = Null
    //     0xa62948: add             x3, PP, #0x21, lsl #12  ; [pp+0x213a0] Null
    //     0xa6294c: ldr             x3, [x3, #0x3a0]
    // 0xa62950: r0 = String()
    //     0xa62950: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62954: ldur            x0, [fp, #-0x18]
    // 0xa62958: r1 = LoadInt32Instr(r0)
    //     0xa62958: sbfx            x1, x0, #1, #0x1f
    //     0xa6295c: tbz             w0, #0, #0xa62964
    //     0xa62960: ldur            x1, [x0, #7]
    // 0xa62964: stur            x1, [fp, #-8]
    // 0xa62968: r0 = Juz()
    //     0xa62968: bl              #0x922d48  ; AllocateJuzStub -> Juz (size=0x30)
    // 0xa6296c: mov             x1, x0
    // 0xa62970: ldur            x0, [fp, #-8]
    // 0xa62974: stur            x1, [fp, #-0x18]
    // 0xa62978: StoreField: r1->field_13 = r0
    //     0xa62978: stur            x0, [x1, #0x13]
    // 0xa6297c: ldur            x0, [fp, #-0x20]
    // 0xa62980: r2 = LoadInt32Instr(r0)
    //     0xa62980: sbfx            x2, x0, #1, #0x1f
    //     0xa62984: tbz             w0, #0, #0xa6298c
    //     0xa62988: ldur            x2, [x0, #7]
    // 0xa6298c: StoreField: r1->field_1b = r2
    //     0xa6298c: stur            x2, [x1, #0x1b]
    // 0xa62990: ldur            x0, [fp, #-0x40]
    // 0xa62994: StoreField: r1->field_23 = r0
    //     0xa62994: stur            w0, [x1, #0x23]
    // 0xa62998: ldur            x0, [fp, #-0x48]
    // 0xa6299c: StoreField: r1->field_27 = r0
    //     0xa6299c: stur            w0, [x1, #0x27]
    // 0xa629a0: ldur            x0, [fp, #-0x38]
    // 0xa629a4: StoreField: r1->field_2b = r0
    //     0xa629a4: stur            w0, [x1, #0x2b]
    // 0xa629a8: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa629a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa629ac: ldr             x16, [x16, #0x9f8]
    // 0xa629b0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa629b4: stp             lr, x16, [SP]
    // 0xa629b8: r0 = Map._fromLiteral()
    //     0xa629b8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa629bc: ldur            x1, [fp, #-0x18]
    // 0xa629c0: StoreField: r1->field_f = r0
    //     0xa629c0: stur            w0, [x1, #0xf]
    //     0xa629c4: ldurb           w16, [x1, #-1]
    //     0xa629c8: ldurb           w17, [x0, #-1]
    //     0xa629cc: and             x16, x17, x16, lsr #2
    //     0xa629d0: tst             x16, HEAP, lsr #32
    //     0xa629d4: b.eq            #0xa629dc
    //     0xa629d8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa629dc: mov             x0, x1
    // 0xa629e0: LeaveFrame
    //     0xa629e0: mov             SP, fp
    //     0xa629e4: ldp             fp, lr, [SP], #0x10
    // 0xa629e8: ret
    //     0xa629e8: ret             
    // 0xa629ec: r0 = RangeError()
    //     0xa629ec: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa629f0: mov             x1, x0
    // 0xa629f4: r0 = "Not enough bytes available."
    //     0xa629f4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa629f8: ldr             x0, [x0, #0x8a8]
    // 0xa629fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa629fc: stur            w0, [x1, #0x17]
    // 0xa62a00: r2 = false
    //     0xa62a00: add             x2, NULL, #0x30  ; false
    // 0xa62a04: StoreField: r1->field_b = r2
    //     0xa62a04: stur            w2, [x1, #0xb]
    // 0xa62a08: mov             x0, x1
    // 0xa62a0c: r0 = Throw()
    //     0xa62a0c: bl              #0xec04b8  ; ThrowStub
    // 0xa62a10: brk             #0
    // 0xa62a14: r0 = "Not enough bytes available."
    //     0xa62a14: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa62a18: ldr             x0, [x0, #0x8a8]
    // 0xa62a1c: r2 = false
    //     0xa62a1c: add             x2, NULL, #0x30  ; false
    // 0xa62a20: r0 = RangeError()
    //     0xa62a20: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa62a24: mov             x1, x0
    // 0xa62a28: r0 = "Not enough bytes available."
    //     0xa62a28: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa62a2c: ldr             x0, [x0, #0x8a8]
    // 0xa62a30: ArrayStore: r1[0] = r0  ; List_4
    //     0xa62a30: stur            w0, [x1, #0x17]
    // 0xa62a34: r0 = false
    //     0xa62a34: add             x0, NULL, #0x30  ; false
    // 0xa62a38: StoreField: r1->field_b = r0
    //     0xa62a38: stur            w0, [x1, #0xb]
    // 0xa62a3c: mov             x0, x1
    // 0xa62a40: r0 = Throw()
    //     0xa62a40: bl              #0xec04b8  ; ThrowStub
    // 0xa62a44: brk             #0
    // 0xa62a48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa62a48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62a4c: b               #0xa62624
    // 0xa62a50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa62a50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa62a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa62a54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62a58: b               #0xa626ac
    // 0xa62a5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa62a5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd36fc, size: 0x3b8
    // 0xbd36fc: EnterFrame
    //     0xbd36fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbd3700: mov             fp, SP
    // 0xbd3704: AllocStack(0x28)
    //     0xbd3704: sub             SP, SP, #0x28
    // 0xbd3708: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd3708: mov             x4, x2
    //     0xbd370c: stur            x2, [fp, #-8]
    //     0xbd3710: stur            x3, [fp, #-0x10]
    // 0xbd3714: CheckStackOverflow
    //     0xbd3714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd3718: cmp             SP, x16
    //     0xbd371c: b.ls            #0xbd3a94
    // 0xbd3720: mov             x0, x3
    // 0xbd3724: r2 = Null
    //     0xbd3724: mov             x2, NULL
    // 0xbd3728: r1 = Null
    //     0xbd3728: mov             x1, NULL
    // 0xbd372c: r4 = 60
    //     0xbd372c: movz            x4, #0x3c
    // 0xbd3730: branchIfSmi(r0, 0xbd373c)
    //     0xbd3730: tbz             w0, #0, #0xbd373c
    // 0xbd3734: r4 = LoadClassIdInstr(r0)
    //     0xbd3734: ldur            x4, [x0, #-1]
    //     0xbd3738: ubfx            x4, x4, #0xc, #0x14
    // 0xbd373c: cmp             x4, #0x647
    // 0xbd3740: b.eq            #0xbd3758
    // 0xbd3744: r8 = Juz
    //     0xbd3744: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b338] Type: Juz
    //     0xbd3748: ldr             x8, [x8, #0x338]
    // 0xbd374c: r3 = Null
    //     0xbd374c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b340] Null
    //     0xbd3750: ldr             x3, [x3, #0x340]
    // 0xbd3754: r0 = Juz()
    //     0xbd3754: bl              #0x83c294  ; IsType_Juz_Stub
    // 0xbd3758: ldur            x0, [fp, #-8]
    // 0xbd375c: LoadField: r1 = r0->field_b
    //     0xbd375c: ldur            w1, [x0, #0xb]
    // 0xbd3760: DecompressPointer r1
    //     0xbd3760: add             x1, x1, HEAP, lsl #32
    // 0xbd3764: LoadField: r2 = r1->field_13
    //     0xbd3764: ldur            w2, [x1, #0x13]
    // 0xbd3768: LoadField: r1 = r0->field_13
    //     0xbd3768: ldur            x1, [x0, #0x13]
    // 0xbd376c: r3 = LoadInt32Instr(r2)
    //     0xbd376c: sbfx            x3, x2, #1, #0x1f
    // 0xbd3770: sub             x2, x3, x1
    // 0xbd3774: cmp             x2, #1
    // 0xbd3778: b.ge            #0xbd3788
    // 0xbd377c: mov             x1, x0
    // 0xbd3780: r2 = 1
    //     0xbd3780: movz            x2, #0x1
    // 0xbd3784: r0 = _increaseBufferSize()
    //     0xbd3784: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3788: ldur            x3, [fp, #-8]
    // 0xbd378c: r2 = 5
    //     0xbd378c: movz            x2, #0x5
    // 0xbd3790: LoadField: r4 = r3->field_b
    //     0xbd3790: ldur            w4, [x3, #0xb]
    // 0xbd3794: DecompressPointer r4
    //     0xbd3794: add             x4, x4, HEAP, lsl #32
    // 0xbd3798: LoadField: r5 = r3->field_13
    //     0xbd3798: ldur            x5, [x3, #0x13]
    // 0xbd379c: add             x6, x5, #1
    // 0xbd37a0: StoreField: r3->field_13 = r6
    //     0xbd37a0: stur            x6, [x3, #0x13]
    // 0xbd37a4: LoadField: r0 = r4->field_13
    //     0xbd37a4: ldur            w0, [x4, #0x13]
    // 0xbd37a8: r7 = LoadInt32Instr(r0)
    //     0xbd37a8: sbfx            x7, x0, #1, #0x1f
    // 0xbd37ac: mov             x0, x7
    // 0xbd37b0: mov             x1, x5
    // 0xbd37b4: cmp             x1, x0
    // 0xbd37b8: b.hs            #0xbd3a9c
    // 0xbd37bc: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd37bc: add             x0, x4, x5
    //     0xbd37c0: strb            w2, [x0, #0x17]
    // 0xbd37c4: sub             x0, x7, x6
    // 0xbd37c8: cmp             x0, #1
    // 0xbd37cc: b.ge            #0xbd37dc
    // 0xbd37d0: mov             x1, x3
    // 0xbd37d4: r2 = 1
    //     0xbd37d4: movz            x2, #0x1
    // 0xbd37d8: r0 = _increaseBufferSize()
    //     0xbd37d8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd37dc: ldur            x2, [fp, #-8]
    // 0xbd37e0: ldur            x3, [fp, #-0x10]
    // 0xbd37e4: LoadField: r4 = r2->field_b
    //     0xbd37e4: ldur            w4, [x2, #0xb]
    // 0xbd37e8: DecompressPointer r4
    //     0xbd37e8: add             x4, x4, HEAP, lsl #32
    // 0xbd37ec: LoadField: r5 = r2->field_13
    //     0xbd37ec: ldur            x5, [x2, #0x13]
    // 0xbd37f0: add             x0, x5, #1
    // 0xbd37f4: StoreField: r2->field_13 = r0
    //     0xbd37f4: stur            x0, [x2, #0x13]
    // 0xbd37f8: LoadField: r0 = r4->field_13
    //     0xbd37f8: ldur            w0, [x4, #0x13]
    // 0xbd37fc: r1 = LoadInt32Instr(r0)
    //     0xbd37fc: sbfx            x1, x0, #1, #0x1f
    // 0xbd3800: mov             x0, x1
    // 0xbd3804: mov             x1, x5
    // 0xbd3808: cmp             x1, x0
    // 0xbd380c: b.hs            #0xbd3aa0
    // 0xbd3810: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd3810: add             x0, x4, x5
    //     0xbd3814: strb            wzr, [x0, #0x17]
    // 0xbd3818: LoadField: r4 = r3->field_13
    //     0xbd3818: ldur            x4, [x3, #0x13]
    // 0xbd381c: r0 = BoxInt64Instr(r4)
    //     0xbd381c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd3820: cmp             x4, x0, asr #1
    //     0xbd3824: b.eq            #0xbd3830
    //     0xbd3828: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd382c: stur            x4, [x0, #7]
    // 0xbd3830: r16 = <int>
    //     0xbd3830: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd3834: stp             x2, x16, [SP, #8]
    // 0xbd3838: str             x0, [SP]
    // 0xbd383c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd383c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3840: r0 = write()
    //     0xbd3840: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3844: ldur            x0, [fp, #-8]
    // 0xbd3848: LoadField: r1 = r0->field_b
    //     0xbd3848: ldur            w1, [x0, #0xb]
    // 0xbd384c: DecompressPointer r1
    //     0xbd384c: add             x1, x1, HEAP, lsl #32
    // 0xbd3850: LoadField: r2 = r1->field_13
    //     0xbd3850: ldur            w2, [x1, #0x13]
    // 0xbd3854: LoadField: r1 = r0->field_13
    //     0xbd3854: ldur            x1, [x0, #0x13]
    // 0xbd3858: r3 = LoadInt32Instr(r2)
    //     0xbd3858: sbfx            x3, x2, #1, #0x1f
    // 0xbd385c: sub             x2, x3, x1
    // 0xbd3860: cmp             x2, #1
    // 0xbd3864: b.ge            #0xbd3874
    // 0xbd3868: mov             x1, x0
    // 0xbd386c: r2 = 1
    //     0xbd386c: movz            x2, #0x1
    // 0xbd3870: r0 = _increaseBufferSize()
    //     0xbd3870: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3874: ldur            x2, [fp, #-8]
    // 0xbd3878: ldur            x3, [fp, #-0x10]
    // 0xbd387c: r4 = 1
    //     0xbd387c: movz            x4, #0x1
    // 0xbd3880: LoadField: r5 = r2->field_b
    //     0xbd3880: ldur            w5, [x2, #0xb]
    // 0xbd3884: DecompressPointer r5
    //     0xbd3884: add             x5, x5, HEAP, lsl #32
    // 0xbd3888: LoadField: r6 = r2->field_13
    //     0xbd3888: ldur            x6, [x2, #0x13]
    // 0xbd388c: add             x0, x6, #1
    // 0xbd3890: StoreField: r2->field_13 = r0
    //     0xbd3890: stur            x0, [x2, #0x13]
    // 0xbd3894: LoadField: r0 = r5->field_13
    //     0xbd3894: ldur            w0, [x5, #0x13]
    // 0xbd3898: r1 = LoadInt32Instr(r0)
    //     0xbd3898: sbfx            x1, x0, #1, #0x1f
    // 0xbd389c: mov             x0, x1
    // 0xbd38a0: mov             x1, x6
    // 0xbd38a4: cmp             x1, x0
    // 0xbd38a8: b.hs            #0xbd3aa4
    // 0xbd38ac: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd38ac: add             x0, x5, x6
    //     0xbd38b0: strb            w4, [x0, #0x17]
    // 0xbd38b4: LoadField: r5 = r3->field_1b
    //     0xbd38b4: ldur            x5, [x3, #0x1b]
    // 0xbd38b8: r0 = BoxInt64Instr(r5)
    //     0xbd38b8: sbfiz           x0, x5, #1, #0x1f
    //     0xbd38bc: cmp             x5, x0, asr #1
    //     0xbd38c0: b.eq            #0xbd38cc
    //     0xbd38c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd38c8: stur            x5, [x0, #7]
    // 0xbd38cc: r16 = <int>
    //     0xbd38cc: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd38d0: stp             x2, x16, [SP, #8]
    // 0xbd38d4: str             x0, [SP]
    // 0xbd38d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd38d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd38dc: r0 = write()
    //     0xbd38dc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd38e0: ldur            x0, [fp, #-8]
    // 0xbd38e4: LoadField: r1 = r0->field_b
    //     0xbd38e4: ldur            w1, [x0, #0xb]
    // 0xbd38e8: DecompressPointer r1
    //     0xbd38e8: add             x1, x1, HEAP, lsl #32
    // 0xbd38ec: LoadField: r2 = r1->field_13
    //     0xbd38ec: ldur            w2, [x1, #0x13]
    // 0xbd38f0: LoadField: r1 = r0->field_13
    //     0xbd38f0: ldur            x1, [x0, #0x13]
    // 0xbd38f4: r3 = LoadInt32Instr(r2)
    //     0xbd38f4: sbfx            x3, x2, #1, #0x1f
    // 0xbd38f8: sub             x2, x3, x1
    // 0xbd38fc: cmp             x2, #1
    // 0xbd3900: b.ge            #0xbd3910
    // 0xbd3904: mov             x1, x0
    // 0xbd3908: r2 = 1
    //     0xbd3908: movz            x2, #0x1
    // 0xbd390c: r0 = _increaseBufferSize()
    //     0xbd390c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3910: ldur            x2, [fp, #-8]
    // 0xbd3914: ldur            x3, [fp, #-0x10]
    // 0xbd3918: r4 = 2
    //     0xbd3918: movz            x4, #0x2
    // 0xbd391c: LoadField: r5 = r2->field_b
    //     0xbd391c: ldur            w5, [x2, #0xb]
    // 0xbd3920: DecompressPointer r5
    //     0xbd3920: add             x5, x5, HEAP, lsl #32
    // 0xbd3924: LoadField: r6 = r2->field_13
    //     0xbd3924: ldur            x6, [x2, #0x13]
    // 0xbd3928: add             x0, x6, #1
    // 0xbd392c: StoreField: r2->field_13 = r0
    //     0xbd392c: stur            x0, [x2, #0x13]
    // 0xbd3930: LoadField: r0 = r5->field_13
    //     0xbd3930: ldur            w0, [x5, #0x13]
    // 0xbd3934: r1 = LoadInt32Instr(r0)
    //     0xbd3934: sbfx            x1, x0, #1, #0x1f
    // 0xbd3938: mov             x0, x1
    // 0xbd393c: mov             x1, x6
    // 0xbd3940: cmp             x1, x0
    // 0xbd3944: b.hs            #0xbd3aa8
    // 0xbd3948: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3948: add             x0, x5, x6
    //     0xbd394c: strb            w4, [x0, #0x17]
    // 0xbd3950: LoadField: r0 = r3->field_23
    //     0xbd3950: ldur            w0, [x3, #0x23]
    // 0xbd3954: DecompressPointer r0
    //     0xbd3954: add             x0, x0, HEAP, lsl #32
    // 0xbd3958: r16 = <String>
    //     0xbd3958: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd395c: stp             x2, x16, [SP, #8]
    // 0xbd3960: str             x0, [SP]
    // 0xbd3964: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3964: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3968: r0 = write()
    //     0xbd3968: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd396c: ldur            x0, [fp, #-8]
    // 0xbd3970: LoadField: r1 = r0->field_b
    //     0xbd3970: ldur            w1, [x0, #0xb]
    // 0xbd3974: DecompressPointer r1
    //     0xbd3974: add             x1, x1, HEAP, lsl #32
    // 0xbd3978: LoadField: r2 = r1->field_13
    //     0xbd3978: ldur            w2, [x1, #0x13]
    // 0xbd397c: LoadField: r1 = r0->field_13
    //     0xbd397c: ldur            x1, [x0, #0x13]
    // 0xbd3980: r3 = LoadInt32Instr(r2)
    //     0xbd3980: sbfx            x3, x2, #1, #0x1f
    // 0xbd3984: sub             x2, x3, x1
    // 0xbd3988: cmp             x2, #1
    // 0xbd398c: b.ge            #0xbd399c
    // 0xbd3990: mov             x1, x0
    // 0xbd3994: r2 = 1
    //     0xbd3994: movz            x2, #0x1
    // 0xbd3998: r0 = _increaseBufferSize()
    //     0xbd3998: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd399c: ldur            x2, [fp, #-8]
    // 0xbd39a0: ldur            x3, [fp, #-0x10]
    // 0xbd39a4: r4 = 3
    //     0xbd39a4: movz            x4, #0x3
    // 0xbd39a8: LoadField: r5 = r2->field_b
    //     0xbd39a8: ldur            w5, [x2, #0xb]
    // 0xbd39ac: DecompressPointer r5
    //     0xbd39ac: add             x5, x5, HEAP, lsl #32
    // 0xbd39b0: LoadField: r6 = r2->field_13
    //     0xbd39b0: ldur            x6, [x2, #0x13]
    // 0xbd39b4: add             x0, x6, #1
    // 0xbd39b8: StoreField: r2->field_13 = r0
    //     0xbd39b8: stur            x0, [x2, #0x13]
    // 0xbd39bc: LoadField: r0 = r5->field_13
    //     0xbd39bc: ldur            w0, [x5, #0x13]
    // 0xbd39c0: r1 = LoadInt32Instr(r0)
    //     0xbd39c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd39c4: mov             x0, x1
    // 0xbd39c8: mov             x1, x6
    // 0xbd39cc: cmp             x1, x0
    // 0xbd39d0: b.hs            #0xbd3aac
    // 0xbd39d4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd39d4: add             x0, x5, x6
    //     0xbd39d8: strb            w4, [x0, #0x17]
    // 0xbd39dc: LoadField: r0 = r3->field_27
    //     0xbd39dc: ldur            w0, [x3, #0x27]
    // 0xbd39e0: DecompressPointer r0
    //     0xbd39e0: add             x0, x0, HEAP, lsl #32
    // 0xbd39e4: r16 = <String>
    //     0xbd39e4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd39e8: stp             x2, x16, [SP, #8]
    // 0xbd39ec: str             x0, [SP]
    // 0xbd39f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd39f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd39f4: r0 = write()
    //     0xbd39f4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd39f8: ldur            x0, [fp, #-8]
    // 0xbd39fc: LoadField: r1 = r0->field_b
    //     0xbd39fc: ldur            w1, [x0, #0xb]
    // 0xbd3a00: DecompressPointer r1
    //     0xbd3a00: add             x1, x1, HEAP, lsl #32
    // 0xbd3a04: LoadField: r2 = r1->field_13
    //     0xbd3a04: ldur            w2, [x1, #0x13]
    // 0xbd3a08: LoadField: r1 = r0->field_13
    //     0xbd3a08: ldur            x1, [x0, #0x13]
    // 0xbd3a0c: r3 = LoadInt32Instr(r2)
    //     0xbd3a0c: sbfx            x3, x2, #1, #0x1f
    // 0xbd3a10: sub             x2, x3, x1
    // 0xbd3a14: cmp             x2, #1
    // 0xbd3a18: b.ge            #0xbd3a28
    // 0xbd3a1c: mov             x1, x0
    // 0xbd3a20: r2 = 1
    //     0xbd3a20: movz            x2, #0x1
    // 0xbd3a24: r0 = _increaseBufferSize()
    //     0xbd3a24: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3a28: ldur            x2, [fp, #-8]
    // 0xbd3a2c: ldur            x3, [fp, #-0x10]
    // 0xbd3a30: r4 = 4
    //     0xbd3a30: movz            x4, #0x4
    // 0xbd3a34: LoadField: r5 = r2->field_b
    //     0xbd3a34: ldur            w5, [x2, #0xb]
    // 0xbd3a38: DecompressPointer r5
    //     0xbd3a38: add             x5, x5, HEAP, lsl #32
    // 0xbd3a3c: LoadField: r6 = r2->field_13
    //     0xbd3a3c: ldur            x6, [x2, #0x13]
    // 0xbd3a40: add             x0, x6, #1
    // 0xbd3a44: StoreField: r2->field_13 = r0
    //     0xbd3a44: stur            x0, [x2, #0x13]
    // 0xbd3a48: LoadField: r0 = r5->field_13
    //     0xbd3a48: ldur            w0, [x5, #0x13]
    // 0xbd3a4c: r1 = LoadInt32Instr(r0)
    //     0xbd3a4c: sbfx            x1, x0, #1, #0x1f
    // 0xbd3a50: mov             x0, x1
    // 0xbd3a54: mov             x1, x6
    // 0xbd3a58: cmp             x1, x0
    // 0xbd3a5c: b.hs            #0xbd3ab0
    // 0xbd3a60: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3a60: add             x0, x5, x6
    //     0xbd3a64: strb            w4, [x0, #0x17]
    // 0xbd3a68: LoadField: r0 = r3->field_2b
    //     0xbd3a68: ldur            w0, [x3, #0x2b]
    // 0xbd3a6c: DecompressPointer r0
    //     0xbd3a6c: add             x0, x0, HEAP, lsl #32
    // 0xbd3a70: r16 = <String>
    //     0xbd3a70: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3a74: stp             x2, x16, [SP, #8]
    // 0xbd3a78: str             x0, [SP]
    // 0xbd3a7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3a7c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3a80: r0 = write()
    //     0xbd3a80: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3a84: r0 = Null
    //     0xbd3a84: mov             x0, NULL
    // 0xbd3a88: LeaveFrame
    //     0xbd3a88: mov             SP, fp
    //     0xbd3a8c: ldp             fp, lr, [SP], #0x10
    // 0xbd3a90: ret
    //     0xbd3a90: ret             
    // 0xbd3a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd3a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd3a98: b               #0xbd3720
    // 0xbd3a9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3a9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd3aa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3aa0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd3aa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3aa4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd3aa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3aa8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd3aac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3aac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd3ab0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd3ab0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf01d4, size: 0x24
    // 0xbf01d4: r1 = 46
    //     0xbf01d4: movz            x1, #0x2e
    // 0xbf01d8: r16 = LoadInt32Instr(r1)
    //     0xbf01d8: sbfx            x16, x1, #1, #0x1f
    // 0xbf01dc: r17 = 11601
    //     0xbf01dc: movz            x17, #0x2d51
    // 0xbf01e0: mul             x0, x16, x17
    // 0xbf01e4: umulh           x16, x16, x17
    // 0xbf01e8: eor             x0, x0, x16
    // 0xbf01ec: r0 = 0
    //     0xbf01ec: eor             x0, x0, x0, lsr #32
    // 0xbf01f0: ubfiz           x0, x0, #1, #0x1e
    // 0xbf01f4: ret
    //     0xbf01f4: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76860, size: 0x9c
    // 0xd76860: EnterFrame
    //     0xd76860: stp             fp, lr, [SP, #-0x10]!
    //     0xd76864: mov             fp, SP
    // 0xd76868: AllocStack(0x10)
    //     0xd76868: sub             SP, SP, #0x10
    // 0xd7686c: CheckStackOverflow
    //     0xd7686c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76870: cmp             SP, x16
    //     0xd76874: b.ls            #0xd768f4
    // 0xd76878: ldr             x0, [fp, #0x10]
    // 0xd7687c: cmp             w0, NULL
    // 0xd76880: b.ne            #0xd76894
    // 0xd76884: r0 = false
    //     0xd76884: add             x0, NULL, #0x30  ; false
    // 0xd76888: LeaveFrame
    //     0xd76888: mov             SP, fp
    //     0xd7688c: ldp             fp, lr, [SP], #0x10
    // 0xd76890: ret
    //     0xd76890: ret             
    // 0xd76894: ldr             x1, [fp, #0x18]
    // 0xd76898: cmp             w1, w0
    // 0xd7689c: b.ne            #0xd768a8
    // 0xd768a0: r0 = true
    //     0xd768a0: add             x0, NULL, #0x20  ; true
    // 0xd768a4: b               #0xd768e8
    // 0xd768a8: r1 = 60
    //     0xd768a8: movz            x1, #0x3c
    // 0xd768ac: branchIfSmi(r0, 0xd768b8)
    //     0xd768ac: tbz             w0, #0, #0xd768b8
    // 0xd768b0: r1 = LoadClassIdInstr(r0)
    //     0xd768b0: ldur            x1, [x0, #-1]
    //     0xd768b4: ubfx            x1, x1, #0xc, #0x14
    // 0xd768b8: cmp             x1, #0x679
    // 0xd768bc: b.ne            #0xd768e4
    // 0xd768c0: r16 = JuzAdapter
    //     0xd768c0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b330] Type: JuzAdapter
    //     0xd768c4: ldr             x16, [x16, #0x330]
    // 0xd768c8: r30 = JuzAdapter
    //     0xd768c8: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b330] Type: JuzAdapter
    //     0xd768cc: ldr             lr, [lr, #0x330]
    // 0xd768d0: stp             lr, x16, [SP]
    // 0xd768d4: r0 = ==()
    //     0xd768d4: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd768d8: tbnz            w0, #4, #0xd768e4
    // 0xd768dc: r0 = true
    //     0xd768dc: add             x0, NULL, #0x20  ; true
    // 0xd768e0: b               #0xd768e8
    // 0xd768e4: r0 = false
    //     0xd768e4: add             x0, NULL, #0x30  ; false
    // 0xd768e8: LeaveFrame
    //     0xd768e8: mov             SP, fp
    //     0xd768ec: ldp             fp, lr, [SP], #0x10
    // 0xd768f0: ret
    //     0xd768f0: ret             
    // 0xd768f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd768f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd768f8: b               #0xd76878
  }
}
