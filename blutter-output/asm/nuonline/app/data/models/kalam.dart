// lib: , url: package:nuonline/app/data/models/kalam.dart

// class id: 1050028, size: 0x8
class :: {
}

// class id: 5580, size: 0x18, field offset: 0x8
//   const constructor, 
class KalamCategory extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x920438, size: 0x188
    // 0x920438: EnterFrame
    //     0x920438: stp             fp, lr, [SP, #-0x10]!
    //     0x92043c: mov             fp, SP
    // 0x920440: AllocStack(0x20)
    //     0x920440: sub             SP, SP, #0x20
    // 0x920444: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x920444: mov             x3, x1
    //     0x920448: stur            x1, [fp, #-8]
    // 0x92044c: CheckStackOverflow
    //     0x92044c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920450: cmp             SP, x16
    //     0x920454: b.ls            #0x9205b8
    // 0x920458: mov             x0, x3
    // 0x92045c: r2 = Null
    //     0x92045c: mov             x2, NULL
    // 0x920460: r1 = Null
    //     0x920460: mov             x1, NULL
    // 0x920464: cmp             w0, NULL
    // 0x920468: b.eq            #0x92050c
    // 0x92046c: branchIfSmi(r0, 0x92050c)
    //     0x92046c: tbz             w0, #0, #0x92050c
    // 0x920470: r3 = LoadClassIdInstr(r0)
    //     0x920470: ldur            x3, [x0, #-1]
    //     0x920474: ubfx            x3, x3, #0xc, #0x14
    // 0x920478: r17 = 6718
    //     0x920478: movz            x17, #0x1a3e
    // 0x92047c: cmp             x3, x17
    // 0x920480: b.eq            #0x920514
    // 0x920484: sub             x3, x3, #0x5a
    // 0x920488: cmp             x3, #2
    // 0x92048c: b.ls            #0x920514
    // 0x920490: r4 = LoadClassIdInstr(r0)
    //     0x920490: ldur            x4, [x0, #-1]
    //     0x920494: ubfx            x4, x4, #0xc, #0x14
    // 0x920498: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x92049c: ldr             x3, [x3, #0x18]
    // 0x9204a0: ldr             x3, [x3, x4, lsl #3]
    // 0x9204a4: LoadField: r3 = r3->field_2b
    //     0x9204a4: ldur            w3, [x3, #0x2b]
    // 0x9204a8: DecompressPointer r3
    //     0x9204a8: add             x3, x3, HEAP, lsl #32
    // 0x9204ac: cmp             w3, NULL
    // 0x9204b0: b.eq            #0x92050c
    // 0x9204b4: LoadField: r3 = r3->field_f
    //     0x9204b4: ldur            w3, [x3, #0xf]
    // 0x9204b8: lsr             x3, x3, #3
    // 0x9204bc: r17 = 6718
    //     0x9204bc: movz            x17, #0x1a3e
    // 0x9204c0: cmp             x3, x17
    // 0x9204c4: b.eq            #0x920514
    // 0x9204c8: r3 = SubtypeTestCache
    //     0x9204c8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f1d8] SubtypeTestCache
    //     0x9204cc: ldr             x3, [x3, #0x1d8]
    // 0x9204d0: r30 = Subtype1TestCacheStub
    //     0x9204d0: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x9204d4: LoadField: r30 = r30->field_7
    //     0x9204d4: ldur            lr, [lr, #7]
    // 0x9204d8: blr             lr
    // 0x9204dc: cmp             w7, NULL
    // 0x9204e0: b.eq            #0x9204ec
    // 0x9204e4: tbnz            w7, #4, #0x92050c
    // 0x9204e8: b               #0x920514
    // 0x9204ec: r8 = List
    //     0x9204ec: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3f1e0] Type: List
    //     0x9204f0: ldr             x8, [x8, #0x1e0]
    // 0x9204f4: r3 = SubtypeTestCache
    //     0x9204f4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f1e8] SubtypeTestCache
    //     0x9204f8: ldr             x3, [x3, #0x1e8]
    // 0x9204fc: r30 = InstanceOfStub
    //     0x9204fc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x920500: LoadField: r30 = r30->field_7
    //     0x920500: ldur            lr, [lr, #7]
    // 0x920504: blr             lr
    // 0x920508: b               #0x920518
    // 0x92050c: r0 = false
    //     0x92050c: add             x0, NULL, #0x30  ; false
    // 0x920510: b               #0x920518
    // 0x920514: r0 = true
    //     0x920514: add             x0, NULL, #0x20  ; true
    // 0x920518: tbnz            w0, #4, #0x92059c
    // 0x92051c: ldur            x0, [fp, #-8]
    // 0x920520: r1 = Function '<anonymous closure>': static.
    //     0x920520: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f1f0] AnonymousClosure: static (0x9205c0), in [package:nuonline/app/data/models/kalam.dart] KalamCategory::fromResponse (0x920438)
    //     0x920524: ldr             x1, [x1, #0x1f0]
    // 0x920528: r2 = Null
    //     0x920528: mov             x2, NULL
    // 0x92052c: r0 = AllocateClosure()
    //     0x92052c: bl              #0xec1630  ; AllocateClosureStub
    // 0x920530: mov             x1, x0
    // 0x920534: ldur            x0, [fp, #-8]
    // 0x920538: r2 = LoadClassIdInstr(r0)
    //     0x920538: ldur            x2, [x0, #-1]
    //     0x92053c: ubfx            x2, x2, #0xc, #0x14
    // 0x920540: r16 = <KalamCategory>
    //     0x920540: add             x16, PP, #0x33, lsl #12  ; [pp+0x33c68] TypeArguments: <KalamCategory>
    //     0x920544: ldr             x16, [x16, #0xc68]
    // 0x920548: stp             x0, x16, [SP, #8]
    // 0x92054c: str             x1, [SP]
    // 0x920550: mov             x0, x2
    // 0x920554: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x920554: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x920558: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x920558: movz            x17, #0xf28c
    //     0x92055c: add             lr, x0, x17
    //     0x920560: ldr             lr, [x21, lr, lsl #3]
    //     0x920564: blr             lr
    // 0x920568: r1 = LoadClassIdInstr(r0)
    //     0x920568: ldur            x1, [x0, #-1]
    //     0x92056c: ubfx            x1, x1, #0xc, #0x14
    // 0x920570: mov             x16, x0
    // 0x920574: mov             x0, x1
    // 0x920578: mov             x1, x16
    // 0x92057c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92057c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x920580: r0 = GDT[cid_x0 + 0xd889]()
    //     0x920580: movz            x17, #0xd889
    //     0x920584: add             lr, x0, x17
    //     0x920588: ldr             lr, [x21, lr, lsl #3]
    //     0x92058c: blr             lr
    // 0x920590: LeaveFrame
    //     0x920590: mov             SP, fp
    //     0x920594: ldp             fp, lr, [SP], #0x10
    // 0x920598: ret
    //     0x920598: ret             
    // 0x92059c: r1 = <KalamCategory>
    //     0x92059c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33c68] TypeArguments: <KalamCategory>
    //     0x9205a0: ldr             x1, [x1, #0xc68]
    // 0x9205a4: r2 = 0
    //     0x9205a4: movz            x2, #0
    // 0x9205a8: r0 = _GrowableList()
    //     0x9205a8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x9205ac: LeaveFrame
    //     0x9205ac: mov             SP, fp
    //     0x9205b0: ldp             fp, lr, [SP], #0x10
    // 0x9205b4: ret
    //     0x9205b4: ret             
    // 0x9205b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9205b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9205bc: b               #0x920458
  }
  [closure] static KalamCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x9205c0, size: 0x50
    // 0x9205c0: EnterFrame
    //     0x9205c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9205c4: mov             fp, SP
    // 0x9205c8: CheckStackOverflow
    //     0x9205c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9205cc: cmp             SP, x16
    //     0x9205d0: b.ls            #0x920608
    // 0x9205d4: ldr             x0, [fp, #0x10]
    // 0x9205d8: r2 = Null
    //     0x9205d8: mov             x2, NULL
    // 0x9205dc: r1 = Null
    //     0x9205dc: mov             x1, NULL
    // 0x9205e0: r8 = Map<String, dynamic>
    //     0x9205e0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x9205e4: r3 = Null
    //     0x9205e4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f1f8] Null
    //     0x9205e8: ldr             x3, [x3, #0x1f8]
    // 0x9205ec: r0 = Map<String, dynamic>()
    //     0x9205ec: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9205f0: ldr             x2, [fp, #0x10]
    // 0x9205f4: r1 = Null
    //     0x9205f4: mov             x1, NULL
    // 0x9205f8: r0 = KalamCategory.fromMap()
    //     0x9205f8: bl              #0x920610  ; [package:nuonline/app/data/models/kalam.dart] KalamCategory::KalamCategory.fromMap
    // 0x9205fc: LeaveFrame
    //     0x9205fc: mov             SP, fp
    //     0x920600: ldp             fp, lr, [SP], #0x10
    // 0x920604: ret
    //     0x920604: ret             
    // 0x920608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92060c: b               #0x9205d4
  }
  factory _ KalamCategory.fromMap(/* No info */) {
    // ** addr: 0x920610, size: 0x214
    // 0x920610: EnterFrame
    //     0x920610: stp             fp, lr, [SP, #-0x10]!
    //     0x920614: mov             fp, SP
    // 0x920618: AllocStack(0x38)
    //     0x920618: sub             SP, SP, #0x38
    // 0x92061c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x92061c: mov             x3, x2
    //     0x920620: stur            x2, [fp, #-8]
    // 0x920624: CheckStackOverflow
    //     0x920624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920628: cmp             SP, x16
    //     0x92062c: b.ls            #0x92081c
    // 0x920630: r0 = LoadClassIdInstr(r3)
    //     0x920630: ldur            x0, [x3, #-1]
    //     0x920634: ubfx            x0, x0, #0xc, #0x14
    // 0x920638: mov             x1, x3
    // 0x92063c: r2 = "id"
    //     0x92063c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x920640: ldr             x2, [x2, #0x740]
    // 0x920644: r0 = GDT[cid_x0 + -0x114]()
    //     0x920644: sub             lr, x0, #0x114
    //     0x920648: ldr             lr, [x21, lr, lsl #3]
    //     0x92064c: blr             lr
    // 0x920650: mov             x3, x0
    // 0x920654: r2 = Null
    //     0x920654: mov             x2, NULL
    // 0x920658: r1 = Null
    //     0x920658: mov             x1, NULL
    // 0x92065c: stur            x3, [fp, #-0x10]
    // 0x920660: branchIfSmi(r0, 0x920688)
    //     0x920660: tbz             w0, #0, #0x920688
    // 0x920664: r4 = LoadClassIdInstr(r0)
    //     0x920664: ldur            x4, [x0, #-1]
    //     0x920668: ubfx            x4, x4, #0xc, #0x14
    // 0x92066c: sub             x4, x4, #0x3c
    // 0x920670: cmp             x4, #1
    // 0x920674: b.ls            #0x920688
    // 0x920678: r8 = int
    //     0x920678: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x92067c: r3 = Null
    //     0x92067c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f208] Null
    //     0x920680: ldr             x3, [x3, #0x208]
    // 0x920684: r0 = int()
    //     0x920684: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x920688: ldur            x3, [fp, #-8]
    // 0x92068c: r0 = LoadClassIdInstr(r3)
    //     0x92068c: ldur            x0, [x3, #-1]
    //     0x920690: ubfx            x0, x0, #0xc, #0x14
    // 0x920694: mov             x1, x3
    // 0x920698: r2 = "name"
    //     0x920698: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x92069c: r0 = GDT[cid_x0 + -0x114]()
    //     0x92069c: sub             lr, x0, #0x114
    //     0x9206a0: ldr             lr, [x21, lr, lsl #3]
    //     0x9206a4: blr             lr
    // 0x9206a8: mov             x3, x0
    // 0x9206ac: r2 = Null
    //     0x9206ac: mov             x2, NULL
    // 0x9206b0: r1 = Null
    //     0x9206b0: mov             x1, NULL
    // 0x9206b4: stur            x3, [fp, #-0x18]
    // 0x9206b8: r4 = 60
    //     0x9206b8: movz            x4, #0x3c
    // 0x9206bc: branchIfSmi(r0, 0x9206c8)
    //     0x9206bc: tbz             w0, #0, #0x9206c8
    // 0x9206c0: r4 = LoadClassIdInstr(r0)
    //     0x9206c0: ldur            x4, [x0, #-1]
    //     0x9206c4: ubfx            x4, x4, #0xc, #0x14
    // 0x9206c8: sub             x4, x4, #0x5e
    // 0x9206cc: cmp             x4, #1
    // 0x9206d0: b.ls            #0x9206e4
    // 0x9206d4: r8 = String
    //     0x9206d4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9206d8: r3 = Null
    //     0x9206d8: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f218] Null
    //     0x9206dc: ldr             x3, [x3, #0x218]
    // 0x9206e0: r0 = String()
    //     0x9206e0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9206e4: ldur            x1, [fp, #-8]
    // 0x9206e8: r0 = LoadClassIdInstr(r1)
    //     0x9206e8: ldur            x0, [x1, #-1]
    //     0x9206ec: ubfx            x0, x0, #0xc, #0x14
    // 0x9206f0: r2 = "banners"
    //     0x9206f0: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f228] "banners"
    //     0x9206f4: ldr             x2, [x2, #0x228]
    // 0x9206f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9206f8: sub             lr, x0, #0x114
    //     0x9206fc: ldr             lr, [x21, lr, lsl #3]
    //     0x920700: blr             lr
    // 0x920704: mov             x3, x0
    // 0x920708: r2 = Null
    //     0x920708: mov             x2, NULL
    // 0x92070c: r1 = Null
    //     0x92070c: mov             x1, NULL
    // 0x920710: stur            x3, [fp, #-8]
    // 0x920714: r4 = 60
    //     0x920714: movz            x4, #0x3c
    // 0x920718: branchIfSmi(r0, 0x920724)
    //     0x920718: tbz             w0, #0, #0x920724
    // 0x92071c: r4 = LoadClassIdInstr(r0)
    //     0x92071c: ldur            x4, [x0, #-1]
    //     0x920720: ubfx            x4, x4, #0xc, #0x14
    // 0x920724: sub             x4, x4, #0x5a
    // 0x920728: cmp             x4, #2
    // 0x92072c: b.ls            #0x920740
    // 0x920730: r8 = List
    //     0x920730: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0x920734: r3 = Null
    //     0x920734: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f230] Null
    //     0x920738: ldr             x3, [x3, #0x230]
    // 0x92073c: r0 = List()
    //     0x92073c: bl              #0xed6b40  ; IsType_List_Stub
    // 0x920740: r1 = Function '<anonymous closure>': static.
    //     0x920740: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f240] AnonymousClosure: static (0x920830), in [package:nuonline/app/data/models/kalam.dart] KalamCategory::KalamCategory.fromMap (0x920610)
    //     0x920744: ldr             x1, [x1, #0x240]
    // 0x920748: r2 = Null
    //     0x920748: mov             x2, NULL
    // 0x92074c: r0 = AllocateClosure()
    //     0x92074c: bl              #0xec1630  ; AllocateClosureStub
    // 0x920750: mov             x1, x0
    // 0x920754: ldur            x0, [fp, #-8]
    // 0x920758: r2 = LoadClassIdInstr(r0)
    //     0x920758: ldur            x2, [x0, #-1]
    //     0x92075c: ubfx            x2, x2, #0xc, #0x14
    // 0x920760: r16 = <Kalam>
    //     0x920760: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d478] TypeArguments: <Kalam>
    //     0x920764: ldr             x16, [x16, #0x478]
    // 0x920768: stp             x0, x16, [SP, #8]
    // 0x92076c: str             x1, [SP]
    // 0x920770: mov             x0, x2
    // 0x920774: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x920774: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x920778: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x920778: movz            x17, #0xf28c
    //     0x92077c: add             lr, x0, x17
    //     0x920780: ldr             lr, [x21, lr, lsl #3]
    //     0x920784: blr             lr
    // 0x920788: r1 = LoadClassIdInstr(r0)
    //     0x920788: ldur            x1, [x0, #-1]
    //     0x92078c: ubfx            x1, x1, #0xc, #0x14
    // 0x920790: mov             x16, x0
    // 0x920794: mov             x0, x1
    // 0x920798: mov             x1, x16
    // 0x92079c: r2 = 5
    //     0x92079c: movz            x2, #0x5
    // 0x9207a0: r0 = GDT[cid_x0 + 0xf4bf]()
    //     0x9207a0: movz            x17, #0xf4bf
    //     0x9207a4: add             lr, x0, x17
    //     0x9207a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9207ac: blr             lr
    // 0x9207b0: r1 = LoadClassIdInstr(r0)
    //     0x9207b0: ldur            x1, [x0, #-1]
    //     0x9207b4: ubfx            x1, x1, #0xc, #0x14
    // 0x9207b8: mov             x16, x0
    // 0x9207bc: mov             x0, x1
    // 0x9207c0: mov             x1, x16
    // 0x9207c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9207c4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9207c8: r0 = GDT[cid_x0 + 0xd889]()
    //     0x9207c8: movz            x17, #0xd889
    //     0x9207cc: add             lr, x0, x17
    //     0x9207d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9207d4: blr             lr
    // 0x9207d8: mov             x1, x0
    // 0x9207dc: ldur            x0, [fp, #-0x10]
    // 0x9207e0: stur            x1, [fp, #-8]
    // 0x9207e4: r2 = LoadInt32Instr(r0)
    //     0x9207e4: sbfx            x2, x0, #1, #0x1f
    //     0x9207e8: tbz             w0, #0, #0x9207f0
    //     0x9207ec: ldur            x2, [x0, #7]
    // 0x9207f0: stur            x2, [fp, #-0x20]
    // 0x9207f4: r0 = KalamCategory()
    //     0x9207f4: bl              #0x920824  ; AllocateKalamCategoryStub -> KalamCategory (size=0x18)
    // 0x9207f8: ldur            x1, [fp, #-0x20]
    // 0x9207fc: StoreField: r0->field_7 = r1
    //     0x9207fc: stur            x1, [x0, #7]
    // 0x920800: ldur            x1, [fp, #-0x18]
    // 0x920804: StoreField: r0->field_f = r1
    //     0x920804: stur            w1, [x0, #0xf]
    // 0x920808: ldur            x1, [fp, #-8]
    // 0x92080c: StoreField: r0->field_13 = r1
    //     0x92080c: stur            w1, [x0, #0x13]
    // 0x920810: LeaveFrame
    //     0x920810: mov             SP, fp
    //     0x920814: ldp             fp, lr, [SP], #0x10
    // 0x920818: ret
    //     0x920818: ret             
    // 0x92081c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92081c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920820: b               #0x920630
  }
  [closure] static Kalam <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x920830, size: 0x50
    // 0x920830: EnterFrame
    //     0x920830: stp             fp, lr, [SP, #-0x10]!
    //     0x920834: mov             fp, SP
    // 0x920838: CheckStackOverflow
    //     0x920838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92083c: cmp             SP, x16
    //     0x920840: b.ls            #0x920878
    // 0x920844: ldr             x0, [fp, #0x10]
    // 0x920848: r2 = Null
    //     0x920848: mov             x2, NULL
    // 0x92084c: r1 = Null
    //     0x92084c: mov             x1, NULL
    // 0x920850: r8 = Map<String, dynamic>
    //     0x920850: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x920854: r3 = Null
    //     0x920854: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f248] Null
    //     0x920858: ldr             x3, [x3, #0x248]
    // 0x92085c: r0 = Map<String, dynamic>()
    //     0x92085c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x920860: ldr             x2, [fp, #0x10]
    // 0x920864: r1 = Null
    //     0x920864: mov             x1, NULL
    // 0x920868: r0 = Kalam.fromMap()
    //     0x920868: bl              #0x8fde5c  ; [package:nuonline/app/data/models/kalam.dart] Kalam::Kalam.fromMap
    // 0x92086c: LeaveFrame
    //     0x92086c: mov             SP, fp
    //     0x920870: ldp             fp, lr, [SP], #0x10
    // 0x920874: ret
    //     0x920874: ret             
    // 0x920878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92087c: b               #0x920844
  }
}

// class id: 5581, size: 0x1c, field offset: 0x8
//   const constructor, 
class Kalam extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x8fdc84, size: 0x188
    // 0x8fdc84: EnterFrame
    //     0x8fdc84: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdc88: mov             fp, SP
    // 0x8fdc8c: AllocStack(0x20)
    //     0x8fdc8c: sub             SP, SP, #0x20
    // 0x8fdc90: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8fdc90: mov             x3, x1
    //     0x8fdc94: stur            x1, [fp, #-8]
    // 0x8fdc98: CheckStackOverflow
    //     0x8fdc98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fdc9c: cmp             SP, x16
    //     0x8fdca0: b.ls            #0x8fde04
    // 0x8fdca4: mov             x0, x3
    // 0x8fdca8: r2 = Null
    //     0x8fdca8: mov             x2, NULL
    // 0x8fdcac: r1 = Null
    //     0x8fdcac: mov             x1, NULL
    // 0x8fdcb0: cmp             w0, NULL
    // 0x8fdcb4: b.eq            #0x8fdd58
    // 0x8fdcb8: branchIfSmi(r0, 0x8fdd58)
    //     0x8fdcb8: tbz             w0, #0, #0x8fdd58
    // 0x8fdcbc: r3 = LoadClassIdInstr(r0)
    //     0x8fdcbc: ldur            x3, [x0, #-1]
    //     0x8fdcc0: ubfx            x3, x3, #0xc, #0x14
    // 0x8fdcc4: r17 = 6718
    //     0x8fdcc4: movz            x17, #0x1a3e
    // 0x8fdcc8: cmp             x3, x17
    // 0x8fdccc: b.eq            #0x8fdd60
    // 0x8fdcd0: sub             x3, x3, #0x5a
    // 0x8fdcd4: cmp             x3, #2
    // 0x8fdcd8: b.ls            #0x8fdd60
    // 0x8fdcdc: r4 = LoadClassIdInstr(r0)
    //     0x8fdcdc: ldur            x4, [x0, #-1]
    //     0x8fdce0: ubfx            x4, x4, #0xc, #0x14
    // 0x8fdce4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8fdce8: ldr             x3, [x3, #0x18]
    // 0x8fdcec: ldr             x3, [x3, x4, lsl #3]
    // 0x8fdcf0: LoadField: r3 = r3->field_2b
    //     0x8fdcf0: ldur            w3, [x3, #0x2b]
    // 0x8fdcf4: DecompressPointer r3
    //     0x8fdcf4: add             x3, x3, HEAP, lsl #32
    // 0x8fdcf8: cmp             w3, NULL
    // 0x8fdcfc: b.eq            #0x8fdd58
    // 0x8fdd00: LoadField: r3 = r3->field_f
    //     0x8fdd00: ldur            w3, [x3, #0xf]
    // 0x8fdd04: lsr             x3, x3, #3
    // 0x8fdd08: r17 = 6718
    //     0x8fdd08: movz            x17, #0x1a3e
    // 0x8fdd0c: cmp             x3, x17
    // 0x8fdd10: b.eq            #0x8fdd60
    // 0x8fdd14: r3 = SubtypeTestCache
    //     0x8fdd14: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d458] SubtypeTestCache
    //     0x8fdd18: ldr             x3, [x3, #0x458]
    // 0x8fdd1c: r30 = Subtype1TestCacheStub
    //     0x8fdd1c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8fdd20: LoadField: r30 = r30->field_7
    //     0x8fdd20: ldur            lr, [lr, #7]
    // 0x8fdd24: blr             lr
    // 0x8fdd28: cmp             w7, NULL
    // 0x8fdd2c: b.eq            #0x8fdd38
    // 0x8fdd30: tbnz            w7, #4, #0x8fdd58
    // 0x8fdd34: b               #0x8fdd60
    // 0x8fdd38: r8 = List
    //     0x8fdd38: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d460] Type: List
    //     0x8fdd3c: ldr             x8, [x8, #0x460]
    // 0x8fdd40: r3 = SubtypeTestCache
    //     0x8fdd40: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d468] SubtypeTestCache
    //     0x8fdd44: ldr             x3, [x3, #0x468]
    // 0x8fdd48: r30 = InstanceOfStub
    //     0x8fdd48: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8fdd4c: LoadField: r30 = r30->field_7
    //     0x8fdd4c: ldur            lr, [lr, #7]
    // 0x8fdd50: blr             lr
    // 0x8fdd54: b               #0x8fdd64
    // 0x8fdd58: r0 = false
    //     0x8fdd58: add             x0, NULL, #0x30  ; false
    // 0x8fdd5c: b               #0x8fdd64
    // 0x8fdd60: r0 = true
    //     0x8fdd60: add             x0, NULL, #0x20  ; true
    // 0x8fdd64: tbnz            w0, #4, #0x8fdde8
    // 0x8fdd68: ldur            x0, [fp, #-8]
    // 0x8fdd6c: r1 = Function '<anonymous closure>': static.
    //     0x8fdd6c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d470] AnonymousClosure: static (0x8fde0c), in [package:nuonline/app/data/models/kalam.dart] Kalam::fromResponse (0x8fdc84)
    //     0x8fdd70: ldr             x1, [x1, #0x470]
    // 0x8fdd74: r2 = Null
    //     0x8fdd74: mov             x2, NULL
    // 0x8fdd78: r0 = AllocateClosure()
    //     0x8fdd78: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fdd7c: mov             x1, x0
    // 0x8fdd80: ldur            x0, [fp, #-8]
    // 0x8fdd84: r2 = LoadClassIdInstr(r0)
    //     0x8fdd84: ldur            x2, [x0, #-1]
    //     0x8fdd88: ubfx            x2, x2, #0xc, #0x14
    // 0x8fdd8c: r16 = <Kalam>
    //     0x8fdd8c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d478] TypeArguments: <Kalam>
    //     0x8fdd90: ldr             x16, [x16, #0x478]
    // 0x8fdd94: stp             x0, x16, [SP, #8]
    // 0x8fdd98: str             x1, [SP]
    // 0x8fdd9c: mov             x0, x2
    // 0x8fdda0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fdda0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fdda4: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fdda4: movz            x17, #0xf28c
    //     0x8fdda8: add             lr, x0, x17
    //     0x8fddac: ldr             lr, [x21, lr, lsl #3]
    //     0x8fddb0: blr             lr
    // 0x8fddb4: r1 = LoadClassIdInstr(r0)
    //     0x8fddb4: ldur            x1, [x0, #-1]
    //     0x8fddb8: ubfx            x1, x1, #0xc, #0x14
    // 0x8fddbc: mov             x16, x0
    // 0x8fddc0: mov             x0, x1
    // 0x8fddc4: mov             x1, x16
    // 0x8fddc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fddc8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fddcc: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fddcc: movz            x17, #0xd889
    //     0x8fddd0: add             lr, x0, x17
    //     0x8fddd4: ldr             lr, [x21, lr, lsl #3]
    //     0x8fddd8: blr             lr
    // 0x8fdddc: LeaveFrame
    //     0x8fdddc: mov             SP, fp
    //     0x8fdde0: ldp             fp, lr, [SP], #0x10
    // 0x8fdde4: ret
    //     0x8fdde4: ret             
    // 0x8fdde8: r1 = <Kalam>
    //     0x8fdde8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d478] TypeArguments: <Kalam>
    //     0x8fddec: ldr             x1, [x1, #0x478]
    // 0x8fddf0: r2 = 0
    //     0x8fddf0: movz            x2, #0
    // 0x8fddf4: r0 = _GrowableList()
    //     0x8fddf4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fddf8: LeaveFrame
    //     0x8fddf8: mov             SP, fp
    //     0x8fddfc: ldp             fp, lr, [SP], #0x10
    // 0x8fde00: ret
    //     0x8fde00: ret             
    // 0x8fde04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fde04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fde08: b               #0x8fdca4
  }
  [closure] static Kalam <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fde0c, size: 0x50
    // 0x8fde0c: EnterFrame
    //     0x8fde0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fde10: mov             fp, SP
    // 0x8fde14: CheckStackOverflow
    //     0x8fde14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fde18: cmp             SP, x16
    //     0x8fde1c: b.ls            #0x8fde54
    // 0x8fde20: ldr             x0, [fp, #0x10]
    // 0x8fde24: r2 = Null
    //     0x8fde24: mov             x2, NULL
    // 0x8fde28: r1 = Null
    //     0x8fde28: mov             x1, NULL
    // 0x8fde2c: r8 = Map<String, dynamic>
    //     0x8fde2c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fde30: r3 = Null
    //     0x8fde30: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d480] Null
    //     0x8fde34: ldr             x3, [x3, #0x480]
    // 0x8fde38: r0 = Map<String, dynamic>()
    //     0x8fde38: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fde3c: ldr             x2, [fp, #0x10]
    // 0x8fde40: r1 = Null
    //     0x8fde40: mov             x1, NULL
    // 0x8fde44: r0 = Kalam.fromMap()
    //     0x8fde44: bl              #0x8fde5c  ; [package:nuonline/app/data/models/kalam.dart] Kalam::Kalam.fromMap
    // 0x8fde48: LeaveFrame
    //     0x8fde48: mov             SP, fp
    //     0x8fde4c: ldp             fp, lr, [SP], #0x10
    // 0x8fde50: ret
    //     0x8fde50: ret             
    // 0x8fde54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fde54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fde58: b               #0x8fde20
  }
  factory Kalam Kalam.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0x8fde5c, size: 0x264
    // 0x8fde5c: EnterFrame
    //     0x8fde5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fde60: mov             fp, SP
    // 0x8fde64: AllocStack(0x28)
    //     0x8fde64: sub             SP, SP, #0x28
    // 0x8fde68: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8fde68: mov             x3, x2
    //     0x8fde6c: stur            x2, [fp, #-8]
    // 0x8fde70: CheckStackOverflow
    //     0x8fde70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fde74: cmp             SP, x16
    //     0x8fde78: b.ls            #0x8fe0b8
    // 0x8fde7c: r0 = LoadClassIdInstr(r3)
    //     0x8fde7c: ldur            x0, [x3, #-1]
    //     0x8fde80: ubfx            x0, x0, #0xc, #0x14
    // 0x8fde84: mov             x1, x3
    // 0x8fde88: r2 = "id"
    //     0x8fde88: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fde8c: ldr             x2, [x2, #0x740]
    // 0x8fde90: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fde90: sub             lr, x0, #0x114
    //     0x8fde94: ldr             lr, [x21, lr, lsl #3]
    //     0x8fde98: blr             lr
    // 0x8fde9c: mov             x3, x0
    // 0x8fdea0: r2 = Null
    //     0x8fdea0: mov             x2, NULL
    // 0x8fdea4: r1 = Null
    //     0x8fdea4: mov             x1, NULL
    // 0x8fdea8: stur            x3, [fp, #-0x10]
    // 0x8fdeac: branchIfSmi(r0, 0x8fded4)
    //     0x8fdeac: tbz             w0, #0, #0x8fded4
    // 0x8fdeb0: r4 = LoadClassIdInstr(r0)
    //     0x8fdeb0: ldur            x4, [x0, #-1]
    //     0x8fdeb4: ubfx            x4, x4, #0xc, #0x14
    // 0x8fdeb8: sub             x4, x4, #0x3c
    // 0x8fdebc: cmp             x4, #1
    // 0x8fdec0: b.ls            #0x8fded4
    // 0x8fdec4: r8 = int
    //     0x8fdec4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fdec8: r3 = Null
    //     0x8fdec8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d490] Null
    //     0x8fdecc: ldr             x3, [x3, #0x490]
    // 0x8fded0: r0 = int()
    //     0x8fded0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fded4: ldur            x3, [fp, #-8]
    // 0x8fded8: r0 = LoadClassIdInstr(r3)
    //     0x8fded8: ldur            x0, [x3, #-1]
    //     0x8fdedc: ubfx            x0, x0, #0xc, #0x14
    // 0x8fdee0: mov             x1, x3
    // 0x8fdee4: r2 = "title"
    //     0x8fdee4: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8fdee8: ldr             x2, [x2, #0x748]
    // 0x8fdeec: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fdeec: sub             lr, x0, #0x114
    //     0x8fdef0: ldr             lr, [x21, lr, lsl #3]
    //     0x8fdef4: blr             lr
    // 0x8fdef8: mov             x3, x0
    // 0x8fdefc: r2 = Null
    //     0x8fdefc: mov             x2, NULL
    // 0x8fdf00: r1 = Null
    //     0x8fdf00: mov             x1, NULL
    // 0x8fdf04: stur            x3, [fp, #-0x18]
    // 0x8fdf08: r4 = 60
    //     0x8fdf08: movz            x4, #0x3c
    // 0x8fdf0c: branchIfSmi(r0, 0x8fdf18)
    //     0x8fdf0c: tbz             w0, #0, #0x8fdf18
    // 0x8fdf10: r4 = LoadClassIdInstr(r0)
    //     0x8fdf10: ldur            x4, [x0, #-1]
    //     0x8fdf14: ubfx            x4, x4, #0xc, #0x14
    // 0x8fdf18: sub             x4, x4, #0x5e
    // 0x8fdf1c: cmp             x4, #1
    // 0x8fdf20: b.ls            #0x8fdf34
    // 0x8fdf24: r8 = String
    //     0x8fdf24: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fdf28: r3 = Null
    //     0x8fdf28: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d4a0] Null
    //     0x8fdf2c: ldr             x3, [x3, #0x4a0]
    // 0x8fdf30: r0 = String()
    //     0x8fdf30: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fdf34: ldur            x3, [fp, #-8]
    // 0x8fdf38: r0 = LoadClassIdInstr(r3)
    //     0x8fdf38: ldur            x0, [x3, #-1]
    //     0x8fdf3c: ubfx            x0, x0, #0xc, #0x14
    // 0x8fdf40: mov             x1, x3
    // 0x8fdf44: r2 = "images"
    //     0x8fdf44: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d4b0] "images"
    //     0x8fdf48: ldr             x2, [x2, #0x4b0]
    // 0x8fdf4c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fdf4c: sub             lr, x0, #0x114
    //     0x8fdf50: ldr             lr, [x21, lr, lsl #3]
    //     0x8fdf54: blr             lr
    // 0x8fdf58: cmp             w0, NULL
    // 0x8fdf5c: b.eq            #0x8fdfb8
    // 0x8fdf60: ldur            x3, [fp, #-8]
    // 0x8fdf64: r0 = LoadClassIdInstr(r3)
    //     0x8fdf64: ldur            x0, [x3, #-1]
    //     0x8fdf68: ubfx            x0, x0, #0xc, #0x14
    // 0x8fdf6c: mov             x1, x3
    // 0x8fdf70: r2 = "images"
    //     0x8fdf70: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d4b0] "images"
    //     0x8fdf74: ldr             x2, [x2, #0x4b0]
    // 0x8fdf78: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fdf78: sub             lr, x0, #0x114
    //     0x8fdf7c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fdf80: blr             lr
    // 0x8fdf84: mov             x3, x0
    // 0x8fdf88: r2 = Null
    //     0x8fdf88: mov             x2, NULL
    // 0x8fdf8c: r1 = Null
    //     0x8fdf8c: mov             x1, NULL
    // 0x8fdf90: stur            x3, [fp, #-0x20]
    // 0x8fdf94: r8 = Map<String, dynamic>
    //     0x8fdf94: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fdf98: r3 = Null
    //     0x8fdf98: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d4b8] Null
    //     0x8fdf9c: ldr             x3, [x3, #0x4b8]
    // 0x8fdfa0: r0 = Map<String, dynamic>()
    //     0x8fdfa0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fdfa4: ldur            x2, [fp, #-0x20]
    // 0x8fdfa8: r1 = Null
    //     0x8fdfa8: mov             x1, NULL
    // 0x8fdfac: r0 = Image.fromMap()
    //     0x8fdfac: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x8fdfb0: mov             x5, x0
    // 0x8fdfb4: b               #0x8fe00c
    // 0x8fdfb8: ldur            x3, [fp, #-8]
    // 0x8fdfbc: r0 = LoadClassIdInstr(r3)
    //     0x8fdfbc: ldur            x0, [x3, #-1]
    //     0x8fdfc0: ubfx            x0, x0, #0xc, #0x14
    // 0x8fdfc4: mov             x1, x3
    // 0x8fdfc8: r2 = "image"
    //     0x8fdfc8: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x8fdfcc: ldr             x2, [x2, #0x520]
    // 0x8fdfd0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fdfd0: sub             lr, x0, #0x114
    //     0x8fdfd4: ldr             lr, [x21, lr, lsl #3]
    //     0x8fdfd8: blr             lr
    // 0x8fdfdc: mov             x3, x0
    // 0x8fdfe0: r2 = Null
    //     0x8fdfe0: mov             x2, NULL
    // 0x8fdfe4: r1 = Null
    //     0x8fdfe4: mov             x1, NULL
    // 0x8fdfe8: stur            x3, [fp, #-0x20]
    // 0x8fdfec: r8 = Map<String, dynamic>
    //     0x8fdfec: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fdff0: r3 = Null
    //     0x8fdff0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d4c8] Null
    //     0x8fdff4: ldr             x3, [x3, #0x4c8]
    // 0x8fdff8: r0 = Map<String, dynamic>()
    //     0x8fdff8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fdffc: ldur            x2, [fp, #-0x20]
    // 0x8fe000: r1 = Null
    //     0x8fe000: mov             x1, NULL
    // 0x8fe004: r0 = Image.fromMap()
    //     0x8fe004: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x8fe008: mov             x5, x0
    // 0x8fe00c: ldur            x1, [fp, #-8]
    // 0x8fe010: ldur            x4, [fp, #-0x10]
    // 0x8fe014: ldur            x3, [fp, #-0x18]
    // 0x8fe018: stur            x5, [fp, #-0x20]
    // 0x8fe01c: r0 = LoadClassIdInstr(r1)
    //     0x8fe01c: ldur            x0, [x1, #-1]
    //     0x8fe020: ubfx            x0, x0, #0xc, #0x14
    // 0x8fe024: r2 = "url"
    //     0x8fe024: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x8fe028: ldr             x2, [x2, #0xd78]
    // 0x8fe02c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fe02c: sub             lr, x0, #0x114
    //     0x8fe030: ldr             lr, [x21, lr, lsl #3]
    //     0x8fe034: blr             lr
    // 0x8fe038: mov             x3, x0
    // 0x8fe03c: r2 = Null
    //     0x8fe03c: mov             x2, NULL
    // 0x8fe040: r1 = Null
    //     0x8fe040: mov             x1, NULL
    // 0x8fe044: stur            x3, [fp, #-8]
    // 0x8fe048: r4 = 60
    //     0x8fe048: movz            x4, #0x3c
    // 0x8fe04c: branchIfSmi(r0, 0x8fe058)
    //     0x8fe04c: tbz             w0, #0, #0x8fe058
    // 0x8fe050: r4 = LoadClassIdInstr(r0)
    //     0x8fe050: ldur            x4, [x0, #-1]
    //     0x8fe054: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe058: sub             x4, x4, #0x5e
    // 0x8fe05c: cmp             x4, #1
    // 0x8fe060: b.ls            #0x8fe074
    // 0x8fe064: r8 = String?
    //     0x8fe064: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8fe068: r3 = Null
    //     0x8fe068: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d4d8] Null
    //     0x8fe06c: ldr             x3, [x3, #0x4d8]
    // 0x8fe070: r0 = String?()
    //     0x8fe070: bl              #0x600324  ; IsType_String?_Stub
    // 0x8fe074: ldur            x0, [fp, #-0x10]
    // 0x8fe078: r1 = LoadInt32Instr(r0)
    //     0x8fe078: sbfx            x1, x0, #1, #0x1f
    //     0x8fe07c: tbz             w0, #0, #0x8fe084
    //     0x8fe080: ldur            x1, [x0, #7]
    // 0x8fe084: stur            x1, [fp, #-0x28]
    // 0x8fe088: r0 = Kalam()
    //     0x8fe088: bl              #0x8fe0c0  ; AllocateKalamStub -> Kalam (size=0x1c)
    // 0x8fe08c: ldur            x1, [fp, #-0x28]
    // 0x8fe090: StoreField: r0->field_7 = r1
    //     0x8fe090: stur            x1, [x0, #7]
    // 0x8fe094: ldur            x1, [fp, #-0x18]
    // 0x8fe098: StoreField: r0->field_f = r1
    //     0x8fe098: stur            w1, [x0, #0xf]
    // 0x8fe09c: ldur            x1, [fp, #-0x20]
    // 0x8fe0a0: StoreField: r0->field_13 = r1
    //     0x8fe0a0: stur            w1, [x0, #0x13]
    // 0x8fe0a4: ldur            x1, [fp, #-8]
    // 0x8fe0a8: ArrayStore: r0[0] = r1  ; List_4
    //     0x8fe0a8: stur            w1, [x0, #0x17]
    // 0x8fe0ac: LeaveFrame
    //     0x8fe0ac: mov             SP, fp
    //     0x8fe0b0: ldp             fp, lr, [SP], #0x10
    // 0x8fe0b4: ret
    //     0x8fe0b4: ret             
    // 0x8fe0b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fe0b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fe0bc: b               #0x8fde7c
  }
}
