// lib: , url: package:nuonline/app/data/models/locality.dart

// class id: 1050029, size: 0x8
class :: {
}

// class id: 1141, size: 0x5c, field offset: 0x8
class Locality extends Object {

  String toJson(Locality) {
    // ** addr: 0x867a34, size: 0x48
    // 0x867a34: EnterFrame
    //     0x867a34: stp             fp, lr, [SP, #-0x10]!
    //     0x867a38: mov             fp, SP
    // 0x867a3c: CheckStackOverflow
    //     0x867a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867a40: cmp             SP, x16
    //     0x867a44: b.ls            #0x867a5c
    // 0x867a48: ldr             x1, [fp, #0x10]
    // 0x867a4c: r0 = toJson()
    //     0x867a4c: bl              #0x867a64  ; [package:nuonline/app/data/models/locality.dart] Locality::toJson
    // 0x867a50: LeaveFrame
    //     0x867a50: mov             SP, fp
    //     0x867a54: ldp             fp, lr, [SP], #0x10
    // 0x867a58: ret
    //     0x867a58: ret             
    // 0x867a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867a60: b               #0x867a48
  }
  String toJson(Locality) {
    // ** addr: 0x867a64, size: 0x3c
    // 0x867a64: EnterFrame
    //     0x867a64: stp             fp, lr, [SP, #-0x10]!
    //     0x867a68: mov             fp, SP
    // 0x867a6c: CheckStackOverflow
    //     0x867a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867a70: cmp             SP, x16
    //     0x867a74: b.ls            #0x867a98
    // 0x867a78: r0 = toMap()
    //     0x867a78: bl              #0x867aa0  ; [package:nuonline/app/data/models/locality.dart] Locality::toMap
    // 0x867a7c: mov             x2, x0
    // 0x867a80: r1 = Instance_JsonCodec
    //     0x867a80: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x867a84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x867a84: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x867a88: r0 = encode()
    //     0x867a88: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x867a8c: LeaveFrame
    //     0x867a8c: mov             SP, fp
    //     0x867a90: ldp             fp, lr, [SP], #0x10
    // 0x867a94: ret
    //     0x867a94: ret             
    // 0x867a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867a9c: b               #0x867a78
  }
  _ toMap(/* No info */) {
    // ** addr: 0x867aa0, size: 0x434
    // 0x867aa0: EnterFrame
    //     0x867aa0: stp             fp, lr, [SP, #-0x10]!
    //     0x867aa4: mov             fp, SP
    // 0x867aa8: AllocStack(0x18)
    //     0x867aa8: sub             SP, SP, #0x18
    // 0x867aac: SetupParameters(Locality this /* r1 => r0, fp-0x8 */)
    //     0x867aac: mov             x0, x1
    //     0x867ab0: stur            x1, [fp, #-8]
    // 0x867ab4: CheckStackOverflow
    //     0x867ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867ab8: cmp             SP, x16
    //     0x867abc: b.ls            #0x867e9c
    // 0x867ac0: r1 = Null
    //     0x867ac0: mov             x1, NULL
    // 0x867ac4: r2 = 52
    //     0x867ac4: movz            x2, #0x34
    // 0x867ac8: r0 = AllocateArray()
    //     0x867ac8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x867acc: mov             x2, x0
    // 0x867ad0: r16 = "id"
    //     0x867ad0: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x867ad4: ldr             x16, [x16, #0x740]
    // 0x867ad8: StoreField: r2->field_f = r16
    //     0x867ad8: stur            w16, [x2, #0xf]
    // 0x867adc: ldur            x3, [fp, #-8]
    // 0x867ae0: LoadField: r4 = r3->field_7
    //     0x867ae0: ldur            x4, [x3, #7]
    // 0x867ae4: r0 = BoxInt64Instr(r4)
    //     0x867ae4: sbfiz           x0, x4, #1, #0x1f
    //     0x867ae8: cmp             x4, x0, asr #1
    //     0x867aec: b.eq            #0x867af8
    //     0x867af0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867af4: stur            x4, [x0, #7]
    // 0x867af8: mov             x1, x2
    // 0x867afc: ArrayStore: r1[1] = r0  ; List_4
    //     0x867afc: add             x25, x1, #0x13
    //     0x867b00: str             w0, [x25]
    //     0x867b04: tbz             w0, #0, #0x867b20
    //     0x867b08: ldurb           w16, [x1, #-1]
    //     0x867b0c: ldurb           w17, [x0, #-1]
    //     0x867b10: and             x16, x17, x16, lsr #2
    //     0x867b14: tst             x16, HEAP, lsr #32
    //     0x867b18: b.eq            #0x867b20
    //     0x867b1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867b20: r16 = "name"
    //     0x867b20: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x867b24: ArrayStore: r2[0] = r16  ; List_4
    //     0x867b24: stur            w16, [x2, #0x17]
    // 0x867b28: LoadField: r0 = r3->field_f
    //     0x867b28: ldur            w0, [x3, #0xf]
    // 0x867b2c: DecompressPointer r0
    //     0x867b2c: add             x0, x0, HEAP, lsl #32
    // 0x867b30: mov             x1, x2
    // 0x867b34: ArrayStore: r1[3] = r0  ; List_4
    //     0x867b34: add             x25, x1, #0x1b
    //     0x867b38: str             w0, [x25]
    //     0x867b3c: tbz             w0, #0, #0x867b58
    //     0x867b40: ldurb           w16, [x1, #-1]
    //     0x867b44: ldurb           w17, [x0, #-1]
    //     0x867b48: and             x16, x17, x16, lsr #2
    //     0x867b4c: tst             x16, HEAP, lsr #32
    //     0x867b50: b.eq            #0x867b58
    //     0x867b54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867b58: r16 = "province_id"
    //     0x867b58: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x867b5c: ldr             x16, [x16, #0x100]
    // 0x867b60: StoreField: r2->field_1f = r16
    //     0x867b60: stur            w16, [x2, #0x1f]
    // 0x867b64: LoadField: r4 = r3->field_13
    //     0x867b64: ldur            x4, [x3, #0x13]
    // 0x867b68: r0 = BoxInt64Instr(r4)
    //     0x867b68: sbfiz           x0, x4, #1, #0x1f
    //     0x867b6c: cmp             x4, x0, asr #1
    //     0x867b70: b.eq            #0x867b7c
    //     0x867b74: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867b78: stur            x4, [x0, #7]
    // 0x867b7c: mov             x1, x2
    // 0x867b80: ArrayStore: r1[5] = r0  ; List_4
    //     0x867b80: add             x25, x1, #0x23
    //     0x867b84: str             w0, [x25]
    //     0x867b88: tbz             w0, #0, #0x867ba4
    //     0x867b8c: ldurb           w16, [x1, #-1]
    //     0x867b90: ldurb           w17, [x0, #-1]
    //     0x867b94: and             x16, x17, x16, lsr #2
    //     0x867b98: tst             x16, HEAP, lsr #32
    //     0x867b9c: b.eq            #0x867ba4
    //     0x867ba0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867ba4: r16 = "regency_id"
    //     0x867ba4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x867ba8: ldr             x16, [x16, #0x118]
    // 0x867bac: StoreField: r2->field_27 = r16
    //     0x867bac: stur            w16, [x2, #0x27]
    // 0x867bb0: LoadField: r4 = r3->field_1b
    //     0x867bb0: ldur            x4, [x3, #0x1b]
    // 0x867bb4: r0 = BoxInt64Instr(r4)
    //     0x867bb4: sbfiz           x0, x4, #1, #0x1f
    //     0x867bb8: cmp             x4, x0, asr #1
    //     0x867bbc: b.eq            #0x867bc8
    //     0x867bc0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867bc4: stur            x4, [x0, #7]
    // 0x867bc8: mov             x1, x2
    // 0x867bcc: ArrayStore: r1[7] = r0  ; List_4
    //     0x867bcc: add             x25, x1, #0x2b
    //     0x867bd0: str             w0, [x25]
    //     0x867bd4: tbz             w0, #0, #0x867bf0
    //     0x867bd8: ldurb           w16, [x1, #-1]
    //     0x867bdc: ldurb           w17, [x0, #-1]
    //     0x867be0: and             x16, x17, x16, lsr #2
    //     0x867be4: tst             x16, HEAP, lsr #32
    //     0x867be8: b.eq            #0x867bf0
    //     0x867bec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867bf0: r16 = "district_id"
    //     0x867bf0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x867bf4: ldr             x16, [x16, #0xfe8]
    // 0x867bf8: StoreField: r2->field_2f = r16
    //     0x867bf8: stur            w16, [x2, #0x2f]
    // 0x867bfc: LoadField: r4 = r3->field_23
    //     0x867bfc: ldur            x4, [x3, #0x23]
    // 0x867c00: r0 = BoxInt64Instr(r4)
    //     0x867c00: sbfiz           x0, x4, #1, #0x1f
    //     0x867c04: cmp             x4, x0, asr #1
    //     0x867c08: b.eq            #0x867c14
    //     0x867c0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867c10: stur            x4, [x0, #7]
    // 0x867c14: mov             x1, x2
    // 0x867c18: ArrayStore: r1[9] = r0  ; List_4
    //     0x867c18: add             x25, x1, #0x33
    //     0x867c1c: str             w0, [x25]
    //     0x867c20: tbz             w0, #0, #0x867c3c
    //     0x867c24: ldurb           w16, [x1, #-1]
    //     0x867c28: ldurb           w17, [x0, #-1]
    //     0x867c2c: and             x16, x17, x16, lsr #2
    //     0x867c30: tst             x16, HEAP, lsr #32
    //     0x867c34: b.eq            #0x867c3c
    //     0x867c38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867c3c: r16 = "postcode"
    //     0x867c3c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d140] "postcode"
    //     0x867c40: ldr             x16, [x16, #0x140]
    // 0x867c44: StoreField: r2->field_37 = r16
    //     0x867c44: stur            w16, [x2, #0x37]
    // 0x867c48: LoadField: r0 = r3->field_2b
    //     0x867c48: ldur            w0, [x3, #0x2b]
    // 0x867c4c: DecompressPointer r0
    //     0x867c4c: add             x0, x0, HEAP, lsl #32
    // 0x867c50: mov             x1, x2
    // 0x867c54: ArrayStore: r1[11] = r0  ; List_4
    //     0x867c54: add             x25, x1, #0x3b
    //     0x867c58: str             w0, [x25]
    //     0x867c5c: tbz             w0, #0, #0x867c78
    //     0x867c60: ldurb           w16, [x1, #-1]
    //     0x867c64: ldurb           w17, [x0, #-1]
    //     0x867c68: and             x16, x17, x16, lsr #2
    //     0x867c6c: tst             x16, HEAP, lsr #32
    //     0x867c70: b.eq            #0x867c78
    //     0x867c74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867c78: r16 = "latitude"
    //     0x867c78: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c470] "latitude"
    //     0x867c7c: ldr             x16, [x16, #0x470]
    // 0x867c80: StoreField: r2->field_3f = r16
    //     0x867c80: stur            w16, [x2, #0x3f]
    // 0x867c84: LoadField: d0 = r3->field_2f
    //     0x867c84: ldur            d0, [x3, #0x2f]
    // 0x867c88: r0 = inline_Allocate_Double()
    //     0x867c88: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x867c8c: add             x0, x0, #0x10
    //     0x867c90: cmp             x1, x0
    //     0x867c94: b.ls            #0x867ea4
    //     0x867c98: str             x0, [THR, #0x50]  ; THR::top
    //     0x867c9c: sub             x0, x0, #0xf
    //     0x867ca0: movz            x1, #0xe15c
    //     0x867ca4: movk            x1, #0x3, lsl #16
    //     0x867ca8: stur            x1, [x0, #-1]
    // 0x867cac: StoreField: r0->field_7 = d0
    //     0x867cac: stur            d0, [x0, #7]
    // 0x867cb0: mov             x1, x2
    // 0x867cb4: ArrayStore: r1[13] = r0  ; List_4
    //     0x867cb4: add             x25, x1, #0x43
    //     0x867cb8: str             w0, [x25]
    //     0x867cbc: tbz             w0, #0, #0x867cd8
    //     0x867cc0: ldurb           w16, [x1, #-1]
    //     0x867cc4: ldurb           w17, [x0, #-1]
    //     0x867cc8: and             x16, x17, x16, lsr #2
    //     0x867ccc: tst             x16, HEAP, lsr #32
    //     0x867cd0: b.eq            #0x867cd8
    //     0x867cd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867cd8: r16 = "longitude"
    //     0x867cd8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c478] "longitude"
    //     0x867cdc: ldr             x16, [x16, #0x478]
    // 0x867ce0: StoreField: r2->field_47 = r16
    //     0x867ce0: stur            w16, [x2, #0x47]
    // 0x867ce4: LoadField: d0 = r3->field_37
    //     0x867ce4: ldur            d0, [x3, #0x37]
    // 0x867ce8: r0 = inline_Allocate_Double()
    //     0x867ce8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x867cec: add             x0, x0, #0x10
    //     0x867cf0: cmp             x1, x0
    //     0x867cf4: b.ls            #0x867ebc
    //     0x867cf8: str             x0, [THR, #0x50]  ; THR::top
    //     0x867cfc: sub             x0, x0, #0xf
    //     0x867d00: movz            x1, #0xe15c
    //     0x867d04: movk            x1, #0x3, lsl #16
    //     0x867d08: stur            x1, [x0, #-1]
    // 0x867d0c: StoreField: r0->field_7 = d0
    //     0x867d0c: stur            d0, [x0, #7]
    // 0x867d10: mov             x1, x2
    // 0x867d14: ArrayStore: r1[15] = r0  ; List_4
    //     0x867d14: add             x25, x1, #0x4b
    //     0x867d18: str             w0, [x25]
    //     0x867d1c: tbz             w0, #0, #0x867d38
    //     0x867d20: ldurb           w16, [x1, #-1]
    //     0x867d24: ldurb           w17, [x0, #-1]
    //     0x867d28: and             x16, x17, x16, lsr #2
    //     0x867d2c: tst             x16, HEAP, lsr #32
    //     0x867d30: b.eq            #0x867d38
    //     0x867d34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867d38: r16 = "altitude"
    //     0x867d38: add             x16, PP, #0xf, lsl #12  ; [pp+0xf548] "altitude"
    //     0x867d3c: ldr             x16, [x16, #0x548]
    // 0x867d40: StoreField: r2->field_4f = r16
    //     0x867d40: stur            w16, [x2, #0x4f]
    // 0x867d44: LoadField: r4 = r3->field_3f
    //     0x867d44: ldur            x4, [x3, #0x3f]
    // 0x867d48: r0 = BoxInt64Instr(r4)
    //     0x867d48: sbfiz           x0, x4, #1, #0x1f
    //     0x867d4c: cmp             x4, x0, asr #1
    //     0x867d50: b.eq            #0x867d5c
    //     0x867d54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867d58: stur            x4, [x0, #7]
    // 0x867d5c: mov             x1, x2
    // 0x867d60: ArrayStore: r1[17] = r0  ; List_4
    //     0x867d60: add             x25, x1, #0x53
    //     0x867d64: str             w0, [x25]
    //     0x867d68: tbz             w0, #0, #0x867d84
    //     0x867d6c: ldurb           w16, [x1, #-1]
    //     0x867d70: ldurb           w17, [x0, #-1]
    //     0x867d74: and             x16, x17, x16, lsr #2
    //     0x867d78: tst             x16, HEAP, lsr #32
    //     0x867d7c: b.eq            #0x867d84
    //     0x867d80: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867d84: r16 = "hasc"
    //     0x867d84: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d158] "hasc"
    //     0x867d88: ldr             x16, [x16, #0x158]
    // 0x867d8c: StoreField: r2->field_57 = r16
    //     0x867d8c: stur            w16, [x2, #0x57]
    // 0x867d90: LoadField: r0 = r3->field_47
    //     0x867d90: ldur            w0, [x3, #0x47]
    // 0x867d94: DecompressPointer r0
    //     0x867d94: add             x0, x0, HEAP, lsl #32
    // 0x867d98: mov             x1, x2
    // 0x867d9c: ArrayStore: r1[19] = r0  ; List_4
    //     0x867d9c: add             x25, x1, #0x5b
    //     0x867da0: str             w0, [x25]
    //     0x867da4: tbz             w0, #0, #0x867dc0
    //     0x867da8: ldurb           w16, [x1, #-1]
    //     0x867dac: ldurb           w17, [x0, #-1]
    //     0x867db0: and             x16, x17, x16, lsr #2
    //     0x867db4: tst             x16, HEAP, lsr #32
    //     0x867db8: b.eq            #0x867dc0
    //     0x867dbc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867dc0: r16 = "timezone"
    //     0x867dc0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d170] "timezone"
    //     0x867dc4: ldr             x16, [x16, #0x170]
    // 0x867dc8: StoreField: r2->field_5f = r16
    //     0x867dc8: stur            w16, [x2, #0x5f]
    // 0x867dcc: LoadField: r0 = r3->field_4b
    //     0x867dcc: ldur            w0, [x3, #0x4b]
    // 0x867dd0: DecompressPointer r0
    //     0x867dd0: add             x0, x0, HEAP, lsl #32
    // 0x867dd4: mov             x1, x2
    // 0x867dd8: ArrayStore: r1[21] = r0  ; List_4
    //     0x867dd8: add             x25, x1, #0x63
    //     0x867ddc: str             w0, [x25]
    //     0x867de0: tbz             w0, #0, #0x867dfc
    //     0x867de4: ldurb           w16, [x1, #-1]
    //     0x867de8: ldurb           w17, [x0, #-1]
    //     0x867dec: and             x16, x17, x16, lsr #2
    //     0x867df0: tst             x16, HEAP, lsr #32
    //     0x867df4: b.eq            #0x867dfc
    //     0x867df8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867dfc: r16 = "utc"
    //     0x867dfc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d0d8] "utc"
    //     0x867e00: ldr             x16, [x16, #0xd8]
    // 0x867e04: StoreField: r2->field_67 = r16
    //     0x867e04: stur            w16, [x2, #0x67]
    // 0x867e08: LoadField: r4 = r3->field_4f
    //     0x867e08: ldur            x4, [x3, #0x4f]
    // 0x867e0c: r0 = BoxInt64Instr(r4)
    //     0x867e0c: sbfiz           x0, x4, #1, #0x1f
    //     0x867e10: cmp             x4, x0, asr #1
    //     0x867e14: b.eq            #0x867e20
    //     0x867e18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x867e1c: stur            x4, [x0, #7]
    // 0x867e20: mov             x1, x2
    // 0x867e24: ArrayStore: r1[23] = r0  ; List_4
    //     0x867e24: add             x25, x1, #0x6b
    //     0x867e28: str             w0, [x25]
    //     0x867e2c: tbz             w0, #0, #0x867e48
    //     0x867e30: ldurb           w16, [x1, #-1]
    //     0x867e34: ldurb           w17, [x0, #-1]
    //     0x867e38: and             x16, x17, x16, lsr #2
    //     0x867e3c: tst             x16, HEAP, lsr #32
    //     0x867e40: b.eq            #0x867e48
    //     0x867e44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867e48: r16 = "address"
    //     0x867e48: add             x16, PP, #0xc, lsl #12  ; [pp+0xc828] "address"
    //     0x867e4c: ldr             x16, [x16, #0x828]
    // 0x867e50: StoreField: r2->field_6f = r16
    //     0x867e50: stur            w16, [x2, #0x6f]
    // 0x867e54: LoadField: r0 = r3->field_57
    //     0x867e54: ldur            w0, [x3, #0x57]
    // 0x867e58: DecompressPointer r0
    //     0x867e58: add             x0, x0, HEAP, lsl #32
    // 0x867e5c: mov             x1, x2
    // 0x867e60: ArrayStore: r1[25] = r0  ; List_4
    //     0x867e60: add             x25, x1, #0x73
    //     0x867e64: str             w0, [x25]
    //     0x867e68: tbz             w0, #0, #0x867e84
    //     0x867e6c: ldurb           w16, [x1, #-1]
    //     0x867e70: ldurb           w17, [x0, #-1]
    //     0x867e74: and             x16, x17, x16, lsr #2
    //     0x867e78: tst             x16, HEAP, lsr #32
    //     0x867e7c: b.eq            #0x867e84
    //     0x867e80: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x867e84: r16 = <String, dynamic>
    //     0x867e84: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x867e88: stp             x2, x16, [SP]
    // 0x867e8c: r0 = Map._fromLiteral()
    //     0x867e8c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x867e90: LeaveFrame
    //     0x867e90: mov             SP, fp
    //     0x867e94: ldp             fp, lr, [SP], #0x10
    // 0x867e98: ret
    //     0x867e98: ret             
    // 0x867e9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867e9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867ea0: b               #0x867ac0
    // 0x867ea4: SaveReg d0
    //     0x867ea4: str             q0, [SP, #-0x10]!
    // 0x867ea8: stp             x2, x3, [SP, #-0x10]!
    // 0x867eac: r0 = AllocateDouble()
    //     0x867eac: bl              #0xec2254  ; AllocateDoubleStub
    // 0x867eb0: ldp             x2, x3, [SP], #0x10
    // 0x867eb4: RestoreReg d0
    //     0x867eb4: ldr             q0, [SP], #0x10
    // 0x867eb8: b               #0x867cac
    // 0x867ebc: SaveReg d0
    //     0x867ebc: str             q0, [SP, #-0x10]!
    // 0x867ec0: stp             x2, x3, [SP, #-0x10]!
    // 0x867ec4: r0 = AllocateDouble()
    //     0x867ec4: bl              #0xec2254  ; AllocateDoubleStub
    // 0x867ec8: ldp             x2, x3, [SP], #0x10
    // 0x867ecc: RestoreReg d0
    //     0x867ecc: ldr             q0, [SP], #0x10
    // 0x867ed0: b               #0x867d0c
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x920c44, size: 0x188
    // 0x920c44: EnterFrame
    //     0x920c44: stp             fp, lr, [SP, #-0x10]!
    //     0x920c48: mov             fp, SP
    // 0x920c4c: AllocStack(0x20)
    //     0x920c4c: sub             SP, SP, #0x20
    // 0x920c50: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x920c50: mov             x3, x1
    //     0x920c54: stur            x1, [fp, #-8]
    // 0x920c58: CheckStackOverflow
    //     0x920c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920c5c: cmp             SP, x16
    //     0x920c60: b.ls            #0x920dc4
    // 0x920c64: mov             x0, x3
    // 0x920c68: r2 = Null
    //     0x920c68: mov             x2, NULL
    // 0x920c6c: r1 = Null
    //     0x920c6c: mov             x1, NULL
    // 0x920c70: cmp             w0, NULL
    // 0x920c74: b.eq            #0x920d18
    // 0x920c78: branchIfSmi(r0, 0x920d18)
    //     0x920c78: tbz             w0, #0, #0x920d18
    // 0x920c7c: r3 = LoadClassIdInstr(r0)
    //     0x920c7c: ldur            x3, [x0, #-1]
    //     0x920c80: ubfx            x3, x3, #0xc, #0x14
    // 0x920c84: r17 = 6718
    //     0x920c84: movz            x17, #0x1a3e
    // 0x920c88: cmp             x3, x17
    // 0x920c8c: b.eq            #0x920d20
    // 0x920c90: sub             x3, x3, #0x5a
    // 0x920c94: cmp             x3, #2
    // 0x920c98: b.ls            #0x920d20
    // 0x920c9c: r4 = LoadClassIdInstr(r0)
    //     0x920c9c: ldur            x4, [x0, #-1]
    //     0x920ca0: ubfx            x4, x4, #0xc, #0x14
    // 0x920ca4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x920ca8: ldr             x3, [x3, #0x18]
    // 0x920cac: ldr             x3, [x3, x4, lsl #3]
    // 0x920cb0: LoadField: r3 = r3->field_2b
    //     0x920cb0: ldur            w3, [x3, #0x2b]
    // 0x920cb4: DecompressPointer r3
    //     0x920cb4: add             x3, x3, HEAP, lsl #32
    // 0x920cb8: cmp             w3, NULL
    // 0x920cbc: b.eq            #0x920d18
    // 0x920cc0: LoadField: r3 = r3->field_f
    //     0x920cc0: ldur            w3, [x3, #0xf]
    // 0x920cc4: lsr             x3, x3, #3
    // 0x920cc8: r17 = 6718
    //     0x920cc8: movz            x17, #0x1a3e
    // 0x920ccc: cmp             x3, x17
    // 0x920cd0: b.eq            #0x920d20
    // 0x920cd4: r3 = SubtypeTestCache
    //     0x920cd4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d0a8] SubtypeTestCache
    //     0x920cd8: ldr             x3, [x3, #0xa8]
    // 0x920cdc: r30 = Subtype1TestCacheStub
    //     0x920cdc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x920ce0: LoadField: r30 = r30->field_7
    //     0x920ce0: ldur            lr, [lr, #7]
    // 0x920ce4: blr             lr
    // 0x920ce8: cmp             w7, NULL
    // 0x920cec: b.eq            #0x920cf8
    // 0x920cf0: tbnz            w7, #4, #0x920d18
    // 0x920cf4: b               #0x920d20
    // 0x920cf8: r8 = List
    //     0x920cf8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d0b0] Type: List
    //     0x920cfc: ldr             x8, [x8, #0xb0]
    // 0x920d00: r3 = SubtypeTestCache
    //     0x920d00: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d0b8] SubtypeTestCache
    //     0x920d04: ldr             x3, [x3, #0xb8]
    // 0x920d08: r30 = InstanceOfStub
    //     0x920d08: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x920d0c: LoadField: r30 = r30->field_7
    //     0x920d0c: ldur            lr, [lr, #7]
    // 0x920d10: blr             lr
    // 0x920d14: b               #0x920d24
    // 0x920d18: r0 = false
    //     0x920d18: add             x0, NULL, #0x30  ; false
    // 0x920d1c: b               #0x920d24
    // 0x920d20: r0 = true
    //     0x920d20: add             x0, NULL, #0x20  ; true
    // 0x920d24: tbnz            w0, #4, #0x920da8
    // 0x920d28: ldur            x0, [fp, #-8]
    // 0x920d2c: r1 = Function '<anonymous closure>': static.
    //     0x920d2c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d0c0] AnonymousClosure: static (0x920dcc), in [package:nuonline/app/data/models/locality.dart] Locality::fromResponse (0x920c44)
    //     0x920d30: ldr             x1, [x1, #0xc0]
    // 0x920d34: r2 = Null
    //     0x920d34: mov             x2, NULL
    // 0x920d38: r0 = AllocateClosure()
    //     0x920d38: bl              #0xec1630  ; AllocateClosureStub
    // 0x920d3c: mov             x1, x0
    // 0x920d40: ldur            x0, [fp, #-8]
    // 0x920d44: r2 = LoadClassIdInstr(r0)
    //     0x920d44: ldur            x2, [x0, #-1]
    //     0x920d48: ubfx            x2, x2, #0xc, #0x14
    // 0x920d4c: r16 = <Locality>
    //     0x920d4c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cf98] TypeArguments: <Locality>
    //     0x920d50: ldr             x16, [x16, #0xf98]
    // 0x920d54: stp             x0, x16, [SP, #8]
    // 0x920d58: str             x1, [SP]
    // 0x920d5c: mov             x0, x2
    // 0x920d60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x920d60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x920d64: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x920d64: movz            x17, #0xf28c
    //     0x920d68: add             lr, x0, x17
    //     0x920d6c: ldr             lr, [x21, lr, lsl #3]
    //     0x920d70: blr             lr
    // 0x920d74: r1 = LoadClassIdInstr(r0)
    //     0x920d74: ldur            x1, [x0, #-1]
    //     0x920d78: ubfx            x1, x1, #0xc, #0x14
    // 0x920d7c: mov             x16, x0
    // 0x920d80: mov             x0, x1
    // 0x920d84: mov             x1, x16
    // 0x920d88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x920d88: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x920d8c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x920d8c: movz            x17, #0xd889
    //     0x920d90: add             lr, x0, x17
    //     0x920d94: ldr             lr, [x21, lr, lsl #3]
    //     0x920d98: blr             lr
    // 0x920d9c: LeaveFrame
    //     0x920d9c: mov             SP, fp
    //     0x920da0: ldp             fp, lr, [SP], #0x10
    // 0x920da4: ret
    //     0x920da4: ret             
    // 0x920da8: r1 = <Locality>
    //     0x920da8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cf98] TypeArguments: <Locality>
    //     0x920dac: ldr             x1, [x1, #0xf98]
    // 0x920db0: r2 = 0
    //     0x920db0: movz            x2, #0
    // 0x920db4: r0 = _GrowableList()
    //     0x920db4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x920db8: LeaveFrame
    //     0x920db8: mov             SP, fp
    //     0x920dbc: ldp             fp, lr, [SP], #0x10
    // 0x920dc0: ret
    //     0x920dc0: ret             
    // 0x920dc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920dc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920dc8: b               #0x920c64
  }
  [closure] static Locality <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x920dcc, size: 0x50
    // 0x920dcc: EnterFrame
    //     0x920dcc: stp             fp, lr, [SP, #-0x10]!
    //     0x920dd0: mov             fp, SP
    // 0x920dd4: CheckStackOverflow
    //     0x920dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920dd8: cmp             SP, x16
    //     0x920ddc: b.ls            #0x920e14
    // 0x920de0: ldr             x0, [fp, #0x10]
    // 0x920de4: r2 = Null
    //     0x920de4: mov             x2, NULL
    // 0x920de8: r1 = Null
    //     0x920de8: mov             x1, NULL
    // 0x920dec: r8 = Map<String, dynamic>
    //     0x920dec: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x920df0: r3 = Null
    //     0x920df0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d0c8] Null
    //     0x920df4: ldr             x3, [x3, #0xc8]
    // 0x920df8: r0 = Map<String, dynamic>()
    //     0x920df8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x920dfc: ldr             x2, [fp, #0x10]
    // 0x920e00: r1 = Null
    //     0x920e00: mov             x1, NULL
    // 0x920e04: r0 = Locality.fromMap()
    //     0x920e04: bl              #0x920e1c  ; [package:nuonline/app/data/models/locality.dart] Locality::Locality.fromMap
    // 0x920e08: LeaveFrame
    //     0x920e08: mov             SP, fp
    //     0x920e0c: ldp             fp, lr, [SP], #0x10
    // 0x920e10: ret
    //     0x920e10: ret             
    // 0x920e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920e14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920e18: b               #0x920de0
  }
  factory _ Locality.fromMap(/* No info */) {
    // ** addr: 0x920e1c, size: 0x63c
    // 0x920e1c: EnterFrame
    //     0x920e1c: stp             fp, lr, [SP, #-0x10]!
    //     0x920e20: mov             fp, SP
    // 0x920e24: AllocStack(0x88)
    //     0x920e24: sub             SP, SP, #0x88
    // 0x920e28: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x920e28: mov             x3, x2
    //     0x920e2c: stur            x2, [fp, #-8]
    // 0x920e30: CheckStackOverflow
    //     0x920e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920e34: cmp             SP, x16
    //     0x920e38: b.ls            #0x921450
    // 0x920e3c: r0 = LoadClassIdInstr(r3)
    //     0x920e3c: ldur            x0, [x3, #-1]
    //     0x920e40: ubfx            x0, x0, #0xc, #0x14
    // 0x920e44: mov             x1, x3
    // 0x920e48: r2 = "utc"
    //     0x920e48: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d0d8] "utc"
    //     0x920e4c: ldr             x2, [x2, #0xd8]
    // 0x920e50: r0 = GDT[cid_x0 + -0x114]()
    //     0x920e50: sub             lr, x0, #0x114
    //     0x920e54: ldr             lr, [x21, lr, lsl #3]
    //     0x920e58: blr             lr
    // 0x920e5c: mov             x4, x0
    // 0x920e60: ldur            x3, [fp, #-8]
    // 0x920e64: stur            x4, [fp, #-0x10]
    // 0x920e68: r0 = LoadClassIdInstr(r3)
    //     0x920e68: ldur            x0, [x3, #-1]
    //     0x920e6c: ubfx            x0, x0, #0xc, #0x14
    // 0x920e70: mov             x1, x3
    // 0x920e74: r2 = "latitude"
    //     0x920e74: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c470] "latitude"
    //     0x920e78: ldr             x2, [x2, #0x470]
    // 0x920e7c: r0 = GDT[cid_x0 + -0x114]()
    //     0x920e7c: sub             lr, x0, #0x114
    //     0x920e80: ldr             lr, [x21, lr, lsl #3]
    //     0x920e84: blr             lr
    // 0x920e88: mov             x4, x0
    // 0x920e8c: ldur            x3, [fp, #-8]
    // 0x920e90: stur            x4, [fp, #-0x18]
    // 0x920e94: r0 = LoadClassIdInstr(r3)
    //     0x920e94: ldur            x0, [x3, #-1]
    //     0x920e98: ubfx            x0, x0, #0xc, #0x14
    // 0x920e9c: mov             x1, x3
    // 0x920ea0: r2 = "longitude"
    //     0x920ea0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c478] "longitude"
    //     0x920ea4: ldr             x2, [x2, #0x478]
    // 0x920ea8: r0 = GDT[cid_x0 + -0x114]()
    //     0x920ea8: sub             lr, x0, #0x114
    //     0x920eac: ldr             lr, [x21, lr, lsl #3]
    //     0x920eb0: blr             lr
    // 0x920eb4: ldur            x1, [fp, #-0x10]
    // 0x920eb8: stur            x0, [fp, #-0x20]
    // 0x920ebc: r2 = 60
    //     0x920ebc: movz            x2, #0x3c
    // 0x920ec0: branchIfSmi(r1, 0x920ecc)
    //     0x920ec0: tbz             w1, #0, #0x920ecc
    // 0x920ec4: r2 = LoadClassIdInstr(r1)
    //     0x920ec4: ldur            x2, [x1, #-1]
    //     0x920ec8: ubfx            x2, x2, #0xc, #0x14
    // 0x920ecc: sub             x16, x2, #0x3c
    // 0x920ed0: cmp             x16, #1
    // 0x920ed4: b.hi            #0x920ee8
    // 0x920ed8: r3 = LoadInt32Instr(r1)
    //     0x920ed8: sbfx            x3, x1, #1, #0x1f
    //     0x920edc: tbz             w1, #0, #0x920ee4
    //     0x920ee0: ldur            x3, [x1, #7]
    // 0x920ee4: b               #0x920eec
    // 0x920ee8: r3 = 7
    //     0x920ee8: movz            x3, #0x7
    // 0x920eec: sub             x16, x2, #0x5e
    // 0x920ef0: cmp             x16, #1
    // 0x920ef4: b.hi            #0x920f40
    // 0x920ef8: r16 = 6
    //     0x920ef8: movz            x16, #0x6
    // 0x920efc: str             x16, [SP]
    // 0x920f00: r2 = 2
    //     0x920f00: movz            x2, #0x2
    // 0x920f04: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x920f04: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x920f08: r0 = substring()
    //     0x920f08: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x920f0c: mov             x1, x0
    // 0x920f10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x920f10: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x920f14: r0 = tryParse()
    //     0x920f14: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0x920f18: cmp             w0, NULL
    // 0x920f1c: b.ne            #0x920f28
    // 0x920f20: r0 = 7
    //     0x920f20: movz            x0, #0x7
    // 0x920f24: b               #0x920f38
    // 0x920f28: r1 = LoadInt32Instr(r0)
    //     0x920f28: sbfx            x1, x0, #1, #0x1f
    //     0x920f2c: tbz             w0, #0, #0x920f34
    //     0x920f30: ldur            x1, [x0, #7]
    // 0x920f34: mov             x0, x1
    // 0x920f38: mov             x1, x0
    // 0x920f3c: b               #0x920f44
    // 0x920f40: mov             x1, x3
    // 0x920f44: ldur            x0, [fp, #-0x18]
    // 0x920f48: stur            x1, [fp, #-0x30]
    // 0x920f4c: r2 = 60
    //     0x920f4c: movz            x2, #0x3c
    // 0x920f50: branchIfSmi(r0, 0x920f5c)
    //     0x920f50: tbz             w0, #0, #0x920f5c
    // 0x920f54: r2 = LoadClassIdInstr(r0)
    //     0x920f54: ldur            x2, [x0, #-1]
    //     0x920f58: ubfx            x2, x2, #0xc, #0x14
    // 0x920f5c: stur            x2, [fp, #-0x28]
    // 0x920f60: sub             x16, x2, #0x3c
    // 0x920f64: cmp             x16, #1
    // 0x920f68: b.hi            #0x920f7c
    // 0x920f6c: stp             x0, NULL, [SP]
    // 0x920f70: r0 = _Double.fromInteger()
    //     0x920f70: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x920f74: LoadField: d0 = r0->field_7
    //     0x920f74: ldur            d0, [x0, #7]
    // 0x920f78: b               #0x920f80
    // 0x920f7c: d0 = 0.000000
    //     0x920f7c: eor             v0.16b, v0.16b, v0.16b
    // 0x920f80: ldur            x0, [fp, #-0x20]
    // 0x920f84: stur            d0, [fp, #-0x70]
    // 0x920f88: r1 = 60
    //     0x920f88: movz            x1, #0x3c
    // 0x920f8c: branchIfSmi(r0, 0x920f98)
    //     0x920f8c: tbz             w0, #0, #0x920f98
    // 0x920f90: r1 = LoadClassIdInstr(r0)
    //     0x920f90: ldur            x1, [x0, #-1]
    //     0x920f94: ubfx            x1, x1, #0xc, #0x14
    // 0x920f98: stur            x1, [fp, #-0x38]
    // 0x920f9c: sub             x16, x1, #0x3c
    // 0x920fa0: cmp             x16, #1
    // 0x920fa4: b.hi            #0x920fb8
    // 0x920fa8: stp             x0, NULL, [SP]
    // 0x920fac: r0 = _Double.fromInteger()
    //     0x920fac: bl              #0x61d174  ; [dart:core] _Double::_Double.fromInteger
    // 0x920fb0: LoadField: d0 = r0->field_7
    //     0x920fb0: ldur            d0, [x0, #7]
    // 0x920fb4: b               #0x920fbc
    // 0x920fb8: d0 = 0.000000
    //     0x920fb8: eor             v0.16b, v0.16b, v0.16b
    // 0x920fbc: ldur            x0, [fp, #-0x28]
    // 0x920fc0: cmp             x0, #0x3e
    // 0x920fc4: b.ne            #0x920fd4
    // 0x920fc8: ldur            x0, [fp, #-0x18]
    // 0x920fcc: LoadField: d1 = r0->field_7
    //     0x920fcc: ldur            d1, [x0, #7]
    // 0x920fd0: b               #0x920fd8
    // 0x920fd4: ldur            d1, [fp, #-0x70]
    // 0x920fd8: ldur            x0, [fp, #-0x38]
    // 0x920fdc: stur            d1, [fp, #-0x78]
    // 0x920fe0: cmp             x0, #0x3e
    // 0x920fe4: b.ne            #0x920ff0
    // 0x920fe8: ldur            x0, [fp, #-0x20]
    // 0x920fec: LoadField: d0 = r0->field_7
    //     0x920fec: ldur            d0, [x0, #7]
    // 0x920ff0: ldur            x4, [fp, #-8]
    // 0x920ff4: ldur            x3, [fp, #-0x30]
    // 0x920ff8: stur            d0, [fp, #-0x70]
    // 0x920ffc: r0 = LoadClassIdInstr(r4)
    //     0x920ffc: ldur            x0, [x4, #-1]
    //     0x921000: ubfx            x0, x0, #0xc, #0x14
    // 0x921004: mov             x1, x4
    // 0x921008: r2 = "id"
    //     0x921008: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x92100c: ldr             x2, [x2, #0x740]
    // 0x921010: r0 = GDT[cid_x0 + -0x114]()
    //     0x921010: sub             lr, x0, #0x114
    //     0x921014: ldr             lr, [x21, lr, lsl #3]
    //     0x921018: blr             lr
    // 0x92101c: mov             x3, x0
    // 0x921020: r2 = Null
    //     0x921020: mov             x2, NULL
    // 0x921024: r1 = Null
    //     0x921024: mov             x1, NULL
    // 0x921028: stur            x3, [fp, #-0x10]
    // 0x92102c: branchIfSmi(r0, 0x921054)
    //     0x92102c: tbz             w0, #0, #0x921054
    // 0x921030: r4 = LoadClassIdInstr(r0)
    //     0x921030: ldur            x4, [x0, #-1]
    //     0x921034: ubfx            x4, x4, #0xc, #0x14
    // 0x921038: sub             x4, x4, #0x3c
    // 0x92103c: cmp             x4, #1
    // 0x921040: b.ls            #0x921054
    // 0x921044: r8 = int
    //     0x921044: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x921048: r3 = Null
    //     0x921048: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d0e0] Null
    //     0x92104c: ldr             x3, [x3, #0xe0]
    // 0x921050: r0 = int()
    //     0x921050: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x921054: ldur            x3, [fp, #-8]
    // 0x921058: r0 = LoadClassIdInstr(r3)
    //     0x921058: ldur            x0, [x3, #-1]
    //     0x92105c: ubfx            x0, x0, #0xc, #0x14
    // 0x921060: mov             x1, x3
    // 0x921064: r2 = "name"
    //     0x921064: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x921068: r0 = GDT[cid_x0 + -0x114]()
    //     0x921068: sub             lr, x0, #0x114
    //     0x92106c: ldr             lr, [x21, lr, lsl #3]
    //     0x921070: blr             lr
    // 0x921074: mov             x3, x0
    // 0x921078: r2 = Null
    //     0x921078: mov             x2, NULL
    // 0x92107c: r1 = Null
    //     0x92107c: mov             x1, NULL
    // 0x921080: stur            x3, [fp, #-0x18]
    // 0x921084: r4 = 60
    //     0x921084: movz            x4, #0x3c
    // 0x921088: branchIfSmi(r0, 0x921094)
    //     0x921088: tbz             w0, #0, #0x921094
    // 0x92108c: r4 = LoadClassIdInstr(r0)
    //     0x92108c: ldur            x4, [x0, #-1]
    //     0x921090: ubfx            x4, x4, #0xc, #0x14
    // 0x921094: sub             x4, x4, #0x5e
    // 0x921098: cmp             x4, #1
    // 0x92109c: b.ls            #0x9210b0
    // 0x9210a0: r8 = String
    //     0x9210a0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9210a4: r3 = Null
    //     0x9210a4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d0f0] Null
    //     0x9210a8: ldr             x3, [x3, #0xf0]
    // 0x9210ac: r0 = String()
    //     0x9210ac: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9210b0: ldur            x3, [fp, #-8]
    // 0x9210b4: r0 = LoadClassIdInstr(r3)
    //     0x9210b4: ldur            x0, [x3, #-1]
    //     0x9210b8: ubfx            x0, x0, #0xc, #0x14
    // 0x9210bc: mov             x1, x3
    // 0x9210c0: r2 = "province_id"
    //     0x9210c0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x9210c4: ldr             x2, [x2, #0x100]
    // 0x9210c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9210c8: sub             lr, x0, #0x114
    //     0x9210cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9210d0: blr             lr
    // 0x9210d4: mov             x3, x0
    // 0x9210d8: r2 = Null
    //     0x9210d8: mov             x2, NULL
    // 0x9210dc: r1 = Null
    //     0x9210dc: mov             x1, NULL
    // 0x9210e0: stur            x3, [fp, #-0x20]
    // 0x9210e4: branchIfSmi(r0, 0x92110c)
    //     0x9210e4: tbz             w0, #0, #0x92110c
    // 0x9210e8: r4 = LoadClassIdInstr(r0)
    //     0x9210e8: ldur            x4, [x0, #-1]
    //     0x9210ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9210f0: sub             x4, x4, #0x3c
    // 0x9210f4: cmp             x4, #1
    // 0x9210f8: b.ls            #0x92110c
    // 0x9210fc: r8 = int
    //     0x9210fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x921100: r3 = Null
    //     0x921100: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d108] Null
    //     0x921104: ldr             x3, [x3, #0x108]
    // 0x921108: r0 = int()
    //     0x921108: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x92110c: ldur            x3, [fp, #-8]
    // 0x921110: r0 = LoadClassIdInstr(r3)
    //     0x921110: ldur            x0, [x3, #-1]
    //     0x921114: ubfx            x0, x0, #0xc, #0x14
    // 0x921118: mov             x1, x3
    // 0x92111c: r2 = "regency_id"
    //     0x92111c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x921120: ldr             x2, [x2, #0x118]
    // 0x921124: r0 = GDT[cid_x0 + -0x114]()
    //     0x921124: sub             lr, x0, #0x114
    //     0x921128: ldr             lr, [x21, lr, lsl #3]
    //     0x92112c: blr             lr
    // 0x921130: mov             x3, x0
    // 0x921134: r2 = Null
    //     0x921134: mov             x2, NULL
    // 0x921138: r1 = Null
    //     0x921138: mov             x1, NULL
    // 0x92113c: stur            x3, [fp, #-0x40]
    // 0x921140: branchIfSmi(r0, 0x921168)
    //     0x921140: tbz             w0, #0, #0x921168
    // 0x921144: r4 = LoadClassIdInstr(r0)
    //     0x921144: ldur            x4, [x0, #-1]
    //     0x921148: ubfx            x4, x4, #0xc, #0x14
    // 0x92114c: sub             x4, x4, #0x3c
    // 0x921150: cmp             x4, #1
    // 0x921154: b.ls            #0x921168
    // 0x921158: r8 = int
    //     0x921158: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x92115c: r3 = Null
    //     0x92115c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d120] Null
    //     0x921160: ldr             x3, [x3, #0x120]
    // 0x921164: r0 = int()
    //     0x921164: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x921168: ldur            x3, [fp, #-8]
    // 0x92116c: r0 = LoadClassIdInstr(r3)
    //     0x92116c: ldur            x0, [x3, #-1]
    //     0x921170: ubfx            x0, x0, #0xc, #0x14
    // 0x921174: mov             x1, x3
    // 0x921178: r2 = "district_id"
    //     0x921178: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x92117c: ldr             x2, [x2, #0xfe8]
    // 0x921180: r0 = GDT[cid_x0 + -0x114]()
    //     0x921180: sub             lr, x0, #0x114
    //     0x921184: ldr             lr, [x21, lr, lsl #3]
    //     0x921188: blr             lr
    // 0x92118c: mov             x3, x0
    // 0x921190: r2 = Null
    //     0x921190: mov             x2, NULL
    // 0x921194: r1 = Null
    //     0x921194: mov             x1, NULL
    // 0x921198: stur            x3, [fp, #-0x48]
    // 0x92119c: branchIfSmi(r0, 0x9211c4)
    //     0x92119c: tbz             w0, #0, #0x9211c4
    // 0x9211a0: r4 = LoadClassIdInstr(r0)
    //     0x9211a0: ldur            x4, [x0, #-1]
    //     0x9211a4: ubfx            x4, x4, #0xc, #0x14
    // 0x9211a8: sub             x4, x4, #0x3c
    // 0x9211ac: cmp             x4, #1
    // 0x9211b0: b.ls            #0x9211c4
    // 0x9211b4: r8 = int
    //     0x9211b4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x9211b8: r3 = Null
    //     0x9211b8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d130] Null
    //     0x9211bc: ldr             x3, [x3, #0x130]
    // 0x9211c0: r0 = int()
    //     0x9211c0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x9211c4: ldur            x3, [fp, #-8]
    // 0x9211c8: r0 = LoadClassIdInstr(r3)
    //     0x9211c8: ldur            x0, [x3, #-1]
    //     0x9211cc: ubfx            x0, x0, #0xc, #0x14
    // 0x9211d0: mov             x1, x3
    // 0x9211d4: r2 = "postcode"
    //     0x9211d4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d140] "postcode"
    //     0x9211d8: ldr             x2, [x2, #0x140]
    // 0x9211dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x9211dc: sub             lr, x0, #0x114
    //     0x9211e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9211e4: blr             lr
    // 0x9211e8: r1 = 60
    //     0x9211e8: movz            x1, #0x3c
    // 0x9211ec: branchIfSmi(r0, 0x9211f8)
    //     0x9211ec: tbz             w0, #0, #0x9211f8
    // 0x9211f0: r1 = LoadClassIdInstr(r0)
    //     0x9211f0: ldur            x1, [x0, #-1]
    //     0x9211f4: ubfx            x1, x1, #0xc, #0x14
    // 0x9211f8: str             x0, [SP]
    // 0x9211fc: mov             x0, x1
    // 0x921200: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x921200: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x921204: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x921204: movz            x17, #0x2b03
    //     0x921208: add             lr, x0, x17
    //     0x92120c: ldr             lr, [x21, lr, lsl #3]
    //     0x921210: blr             lr
    // 0x921214: mov             x4, x0
    // 0x921218: ldur            x3, [fp, #-8]
    // 0x92121c: stur            x4, [fp, #-0x50]
    // 0x921220: r0 = LoadClassIdInstr(r3)
    //     0x921220: ldur            x0, [x3, #-1]
    //     0x921224: ubfx            x0, x0, #0xc, #0x14
    // 0x921228: mov             x1, x3
    // 0x92122c: r2 = "altitude"
    //     0x92122c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf548] "altitude"
    //     0x921230: ldr             x2, [x2, #0x548]
    // 0x921234: r0 = GDT[cid_x0 + -0x114]()
    //     0x921234: sub             lr, x0, #0x114
    //     0x921238: ldr             lr, [x21, lr, lsl #3]
    //     0x92123c: blr             lr
    // 0x921240: mov             x3, x0
    // 0x921244: r2 = Null
    //     0x921244: mov             x2, NULL
    // 0x921248: r1 = Null
    //     0x921248: mov             x1, NULL
    // 0x92124c: stur            x3, [fp, #-0x58]
    // 0x921250: branchIfSmi(r0, 0x921278)
    //     0x921250: tbz             w0, #0, #0x921278
    // 0x921254: r4 = LoadClassIdInstr(r0)
    //     0x921254: ldur            x4, [x0, #-1]
    //     0x921258: ubfx            x4, x4, #0xc, #0x14
    // 0x92125c: sub             x4, x4, #0x3c
    // 0x921260: cmp             x4, #1
    // 0x921264: b.ls            #0x921278
    // 0x921268: r8 = int
    //     0x921268: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x92126c: r3 = Null
    //     0x92126c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d148] Null
    //     0x921270: ldr             x3, [x3, #0x148]
    // 0x921274: r0 = int()
    //     0x921274: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x921278: ldur            x3, [fp, #-8]
    // 0x92127c: r0 = LoadClassIdInstr(r3)
    //     0x92127c: ldur            x0, [x3, #-1]
    //     0x921280: ubfx            x0, x0, #0xc, #0x14
    // 0x921284: mov             x1, x3
    // 0x921288: r2 = "hasc"
    //     0x921288: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d158] "hasc"
    //     0x92128c: ldr             x2, [x2, #0x158]
    // 0x921290: r0 = GDT[cid_x0 + -0x114]()
    //     0x921290: sub             lr, x0, #0x114
    //     0x921294: ldr             lr, [x21, lr, lsl #3]
    //     0x921298: blr             lr
    // 0x92129c: mov             x3, x0
    // 0x9212a0: r2 = Null
    //     0x9212a0: mov             x2, NULL
    // 0x9212a4: r1 = Null
    //     0x9212a4: mov             x1, NULL
    // 0x9212a8: stur            x3, [fp, #-0x60]
    // 0x9212ac: r4 = 60
    //     0x9212ac: movz            x4, #0x3c
    // 0x9212b0: branchIfSmi(r0, 0x9212bc)
    //     0x9212b0: tbz             w0, #0, #0x9212bc
    // 0x9212b4: r4 = LoadClassIdInstr(r0)
    //     0x9212b4: ldur            x4, [x0, #-1]
    //     0x9212b8: ubfx            x4, x4, #0xc, #0x14
    // 0x9212bc: sub             x4, x4, #0x5e
    // 0x9212c0: cmp             x4, #1
    // 0x9212c4: b.ls            #0x9212d8
    // 0x9212c8: r8 = String
    //     0x9212c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9212cc: r3 = Null
    //     0x9212cc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d160] Null
    //     0x9212d0: ldr             x3, [x3, #0x160]
    // 0x9212d4: r0 = String()
    //     0x9212d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9212d8: ldur            x3, [fp, #-8]
    // 0x9212dc: r0 = LoadClassIdInstr(r3)
    //     0x9212dc: ldur            x0, [x3, #-1]
    //     0x9212e0: ubfx            x0, x0, #0xc, #0x14
    // 0x9212e4: mov             x1, x3
    // 0x9212e8: r2 = "timezone"
    //     0x9212e8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d170] "timezone"
    //     0x9212ec: ldr             x2, [x2, #0x170]
    // 0x9212f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x9212f0: sub             lr, x0, #0x114
    //     0x9212f4: ldr             lr, [x21, lr, lsl #3]
    //     0x9212f8: blr             lr
    // 0x9212fc: mov             x3, x0
    // 0x921300: r2 = Null
    //     0x921300: mov             x2, NULL
    // 0x921304: r1 = Null
    //     0x921304: mov             x1, NULL
    // 0x921308: stur            x3, [fp, #-0x68]
    // 0x92130c: r4 = 60
    //     0x92130c: movz            x4, #0x3c
    // 0x921310: branchIfSmi(r0, 0x92131c)
    //     0x921310: tbz             w0, #0, #0x92131c
    // 0x921314: r4 = LoadClassIdInstr(r0)
    //     0x921314: ldur            x4, [x0, #-1]
    //     0x921318: ubfx            x4, x4, #0xc, #0x14
    // 0x92131c: sub             x4, x4, #0x5e
    // 0x921320: cmp             x4, #1
    // 0x921324: b.ls            #0x921338
    // 0x921328: r8 = String
    //     0x921328: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x92132c: r3 = Null
    //     0x92132c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d178] Null
    //     0x921330: ldr             x3, [x3, #0x178]
    // 0x921334: r0 = String()
    //     0x921334: bl              #0xed43b0  ; IsType_String_Stub
    // 0x921338: ldur            x1, [fp, #-8]
    // 0x92133c: r0 = LoadClassIdInstr(r1)
    //     0x92133c: ldur            x0, [x1, #-1]
    //     0x921340: ubfx            x0, x0, #0xc, #0x14
    // 0x921344: r2 = "address"
    //     0x921344: add             x2, PP, #0xc, lsl #12  ; [pp+0xc828] "address"
    //     0x921348: ldr             x2, [x2, #0x828]
    // 0x92134c: r0 = GDT[cid_x0 + -0x114]()
    //     0x92134c: sub             lr, x0, #0x114
    //     0x921350: ldr             lr, [x21, lr, lsl #3]
    //     0x921354: blr             lr
    // 0x921358: mov             x3, x0
    // 0x92135c: r2 = Null
    //     0x92135c: mov             x2, NULL
    // 0x921360: r1 = Null
    //     0x921360: mov             x1, NULL
    // 0x921364: stur            x3, [fp, #-8]
    // 0x921368: r4 = 60
    //     0x921368: movz            x4, #0x3c
    // 0x92136c: branchIfSmi(r0, 0x921378)
    //     0x92136c: tbz             w0, #0, #0x921378
    // 0x921370: r4 = LoadClassIdInstr(r0)
    //     0x921370: ldur            x4, [x0, #-1]
    //     0x921374: ubfx            x4, x4, #0xc, #0x14
    // 0x921378: sub             x4, x4, #0x5e
    // 0x92137c: cmp             x4, #1
    // 0x921380: b.ls            #0x921394
    // 0x921384: r8 = String
    //     0x921384: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x921388: r3 = Null
    //     0x921388: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d188] Null
    //     0x92138c: ldr             x3, [x3, #0x188]
    // 0x921390: r0 = String()
    //     0x921390: bl              #0xed43b0  ; IsType_String_Stub
    // 0x921394: ldur            x0, [fp, #-0x10]
    // 0x921398: r1 = LoadInt32Instr(r0)
    //     0x921398: sbfx            x1, x0, #1, #0x1f
    //     0x92139c: tbz             w0, #0, #0x9213a4
    //     0x9213a0: ldur            x1, [x0, #7]
    // 0x9213a4: stur            x1, [fp, #-0x28]
    // 0x9213a8: r0 = Locality()
    //     0x9213a8: bl              #0x921458  ; AllocateLocalityStub -> Locality (size=0x5c)
    // 0x9213ac: ldur            x1, [fp, #-0x28]
    // 0x9213b0: StoreField: r0->field_7 = r1
    //     0x9213b0: stur            x1, [x0, #7]
    // 0x9213b4: ldur            x1, [fp, #-0x18]
    // 0x9213b8: StoreField: r0->field_f = r1
    //     0x9213b8: stur            w1, [x0, #0xf]
    // 0x9213bc: ldur            x1, [fp, #-0x20]
    // 0x9213c0: r2 = LoadInt32Instr(r1)
    //     0x9213c0: sbfx            x2, x1, #1, #0x1f
    //     0x9213c4: tbz             w1, #0, #0x9213cc
    //     0x9213c8: ldur            x2, [x1, #7]
    // 0x9213cc: StoreField: r0->field_13 = r2
    //     0x9213cc: stur            x2, [x0, #0x13]
    // 0x9213d0: ldur            x1, [fp, #-0x40]
    // 0x9213d4: r2 = LoadInt32Instr(r1)
    //     0x9213d4: sbfx            x2, x1, #1, #0x1f
    //     0x9213d8: tbz             w1, #0, #0x9213e0
    //     0x9213dc: ldur            x2, [x1, #7]
    // 0x9213e0: StoreField: r0->field_1b = r2
    //     0x9213e0: stur            x2, [x0, #0x1b]
    // 0x9213e4: ldur            x1, [fp, #-0x48]
    // 0x9213e8: r2 = LoadInt32Instr(r1)
    //     0x9213e8: sbfx            x2, x1, #1, #0x1f
    //     0x9213ec: tbz             w1, #0, #0x9213f4
    //     0x9213f0: ldur            x2, [x1, #7]
    // 0x9213f4: StoreField: r0->field_23 = r2
    //     0x9213f4: stur            x2, [x0, #0x23]
    // 0x9213f8: ldur            x1, [fp, #-0x50]
    // 0x9213fc: StoreField: r0->field_2b = r1
    //     0x9213fc: stur            w1, [x0, #0x2b]
    // 0x921400: ldur            d0, [fp, #-0x78]
    // 0x921404: StoreField: r0->field_2f = d0
    //     0x921404: stur            d0, [x0, #0x2f]
    // 0x921408: ldur            d0, [fp, #-0x70]
    // 0x92140c: StoreField: r0->field_37 = d0
    //     0x92140c: stur            d0, [x0, #0x37]
    // 0x921410: ldur            x1, [fp, #-0x58]
    // 0x921414: r2 = LoadInt32Instr(r1)
    //     0x921414: sbfx            x2, x1, #1, #0x1f
    //     0x921418: tbz             w1, #0, #0x921420
    //     0x92141c: ldur            x2, [x1, #7]
    // 0x921420: StoreField: r0->field_3f = r2
    //     0x921420: stur            x2, [x0, #0x3f]
    // 0x921424: ldur            x1, [fp, #-0x60]
    // 0x921428: StoreField: r0->field_47 = r1
    //     0x921428: stur            w1, [x0, #0x47]
    // 0x92142c: ldur            x1, [fp, #-0x68]
    // 0x921430: StoreField: r0->field_4b = r1
    //     0x921430: stur            w1, [x0, #0x4b]
    // 0x921434: ldur            x1, [fp, #-0x30]
    // 0x921438: StoreField: r0->field_4f = r1
    //     0x921438: stur            x1, [x0, #0x4f]
    // 0x92143c: ldur            x1, [fp, #-8]
    // 0x921440: StoreField: r0->field_57 = r1
    //     0x921440: stur            w1, [x0, #0x57]
    // 0x921444: LeaveFrame
    //     0x921444: mov             SP, fp
    //     0x921448: ldp             fp, lr, [SP], #0x10
    // 0x92144c: ret
    //     0x92144c: ret             
    // 0x921450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x921450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x921454: b               #0x920e3c
  }
  String locality(Locality) {
    // ** addr: 0xb035cc, size: 0x110
    // 0xb035cc: EnterFrame
    //     0xb035cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb035d0: mov             fp, SP
    // 0xb035d4: AllocStack(0x8)
    //     0xb035d4: sub             SP, SP, #8
    // 0xb035d8: SetupParameters(Locality this /* r1 => r3, fp-0x8 */)
    //     0xb035d8: mov             x3, x1
    //     0xb035dc: stur            x1, [fp, #-8]
    // 0xb035e0: CheckStackOverflow
    //     0xb035e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb035e4: cmp             SP, x16
    //     0xb035e8: b.ls            #0xb036cc
    // 0xb035ec: LoadField: r1 = r3->field_57
    //     0xb035ec: ldur            w1, [x3, #0x57]
    // 0xb035f0: DecompressPointer r1
    //     0xb035f0: add             x1, x1, HEAP, lsl #32
    // 0xb035f4: r0 = LoadClassIdInstr(r1)
    //     0xb035f4: ldur            x0, [x1, #-1]
    //     0xb035f8: ubfx            x0, x0, #0xc, #0x14
    // 0xb035fc: r2 = ", "
    //     0xb035fc: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03600: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03600: sub             lr, x0, #1, lsl #12
    //     0xb03604: ldr             lr, [x21, lr, lsl #3]
    //     0xb03608: blr             lr
    // 0xb0360c: LoadField: r1 = r0->field_b
    //     0xb0360c: ldur            w1, [x0, #0xb]
    // 0xb03610: cbz             w1, #0xb036bc
    // 0xb03614: ldur            x3, [fp, #-8]
    // 0xb03618: LoadField: r1 = r3->field_57
    //     0xb03618: ldur            w1, [x3, #0x57]
    // 0xb0361c: DecompressPointer r1
    //     0xb0361c: add             x1, x1, HEAP, lsl #32
    // 0xb03620: r0 = LoadClassIdInstr(r1)
    //     0xb03620: ldur            x0, [x1, #-1]
    //     0xb03624: ubfx            x0, x0, #0xc, #0x14
    // 0xb03628: r2 = ", "
    //     0xb03628: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb0362c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb0362c: sub             lr, x0, #1, lsl #12
    //     0xb03630: ldr             lr, [x21, lr, lsl #3]
    //     0xb03634: blr             lr
    // 0xb03638: mov             x2, x0
    // 0xb0363c: LoadField: r0 = r2->field_b
    //     0xb0363c: ldur            w0, [x2, #0xb]
    // 0xb03640: r1 = LoadInt32Instr(r0)
    //     0xb03640: sbfx            x1, x0, #1, #0x1f
    // 0xb03644: mov             x0, x1
    // 0xb03648: r1 = 0
    //     0xb03648: movz            x1, #0
    // 0xb0364c: cmp             x1, x0
    // 0xb03650: b.hs            #0xb036d4
    // 0xb03654: LoadField: r0 = r2->field_f
    //     0xb03654: ldur            w0, [x2, #0xf]
    // 0xb03658: DecompressPointer r0
    //     0xb03658: add             x0, x0, HEAP, lsl #32
    // 0xb0365c: LoadField: r1 = r0->field_f
    //     0xb0365c: ldur            w1, [x0, #0xf]
    // 0xb03660: DecompressPointer r1
    //     0xb03660: add             x1, x1, HEAP, lsl #32
    // 0xb03664: LoadField: r0 = r1->field_7
    //     0xb03664: ldur            w0, [x1, #7]
    // 0xb03668: cbz             w0, #0xb036a4
    // 0xb0366c: ldur            x1, [fp, #-8]
    // 0xb03670: r0 = addresses()
    //     0xb03670: bl              #0xb036dc  ; [package:nuonline/app/data/models/locality.dart] Locality::addresses
    // 0xb03674: mov             x2, x0
    // 0xb03678: LoadField: r3 = r2->field_b
    //     0xb03678: ldur            w3, [x2, #0xb]
    // 0xb0367c: r0 = LoadInt32Instr(r3)
    //     0xb0367c: sbfx            x0, x3, #1, #0x1f
    // 0xb03680: r1 = 0
    //     0xb03680: movz            x1, #0
    // 0xb03684: cmp             x1, x0
    // 0xb03688: b.hs            #0xb036d8
    // 0xb0368c: LoadField: r1 = r2->field_f
    //     0xb0368c: ldur            w1, [x2, #0xf]
    // 0xb03690: DecompressPointer r1
    //     0xb03690: add             x1, x1, HEAP, lsl #32
    // 0xb03694: LoadField: r2 = r1->field_f
    //     0xb03694: ldur            w2, [x1, #0xf]
    // 0xb03698: DecompressPointer r2
    //     0xb03698: add             x2, x2, HEAP, lsl #32
    // 0xb0369c: mov             x1, x2
    // 0xb036a0: b               #0xb036b4
    // 0xb036a4: ldur            x1, [fp, #-8]
    // 0xb036a8: LoadField: r2 = r1->field_f
    //     0xb036a8: ldur            w2, [x1, #0xf]
    // 0xb036ac: DecompressPointer r2
    //     0xb036ac: add             x2, x2, HEAP, lsl #32
    // 0xb036b0: mov             x1, x2
    // 0xb036b4: mov             x0, x1
    // 0xb036b8: b               #0xb036c0
    // 0xb036bc: r0 = ""
    //     0xb036bc: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb036c0: LeaveFrame
    //     0xb036c0: mov             SP, fp
    //     0xb036c4: ldp             fp, lr, [SP], #0x10
    // 0xb036c8: ret
    //     0xb036c8: ret             
    // 0xb036cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb036cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb036d0: b               #0xb035ec
    // 0xb036d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb036d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb036d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb036d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ addresses(/* No info */) {
    // ** addr: 0xb036dc, size: 0x54
    // 0xb036dc: EnterFrame
    //     0xb036dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb036e0: mov             fp, SP
    // 0xb036e4: CheckStackOverflow
    //     0xb036e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb036e8: cmp             SP, x16
    //     0xb036ec: b.ls            #0xb03728
    // 0xb036f0: LoadField: r0 = r1->field_57
    //     0xb036f0: ldur            w0, [x1, #0x57]
    // 0xb036f4: DecompressPointer r0
    //     0xb036f4: add             x0, x0, HEAP, lsl #32
    // 0xb036f8: r1 = LoadClassIdInstr(r0)
    //     0xb036f8: ldur            x1, [x0, #-1]
    //     0xb036fc: ubfx            x1, x1, #0xc, #0x14
    // 0xb03700: mov             x16, x0
    // 0xb03704: mov             x0, x1
    // 0xb03708: mov             x1, x16
    // 0xb0370c: r2 = ", "
    //     0xb0370c: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03710: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03710: sub             lr, x0, #1, lsl #12
    //     0xb03714: ldr             lr, [x21, lr, lsl #3]
    //     0xb03718: blr             lr
    // 0xb0371c: LeaveFrame
    //     0xb0371c: mov             SP, fp
    //     0xb03720: ldp             fp, lr, [SP], #0x10
    // 0xb03724: ret
    //     0xb03724: ret             
    // 0xb03728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb03728: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0372c: b               #0xb036f0
  }
  String district(Locality) {
    // ** addr: 0xb03730, size: 0xc0
    // 0xb03730: EnterFrame
    //     0xb03730: stp             fp, lr, [SP, #-0x10]!
    //     0xb03734: mov             fp, SP
    // 0xb03738: AllocStack(0x8)
    //     0xb03738: sub             SP, SP, #8
    // 0xb0373c: SetupParameters(Locality this /* r1 => r3, fp-0x8 */)
    //     0xb0373c: mov             x3, x1
    //     0xb03740: stur            x1, [fp, #-8]
    // 0xb03744: CheckStackOverflow
    //     0xb03744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03748: cmp             SP, x16
    //     0xb0374c: b.ls            #0xb037e4
    // 0xb03750: LoadField: r1 = r3->field_57
    //     0xb03750: ldur            w1, [x3, #0x57]
    // 0xb03754: DecompressPointer r1
    //     0xb03754: add             x1, x1, HEAP, lsl #32
    // 0xb03758: r0 = LoadClassIdInstr(r1)
    //     0xb03758: ldur            x0, [x1, #-1]
    //     0xb0375c: ubfx            x0, x0, #0xc, #0x14
    // 0xb03760: r2 = ", "
    //     0xb03760: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03764: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03764: sub             lr, x0, #1, lsl #12
    //     0xb03768: ldr             lr, [x21, lr, lsl #3]
    //     0xb0376c: blr             lr
    // 0xb03770: LoadField: r1 = r0->field_b
    //     0xb03770: ldur            w1, [x0, #0xb]
    // 0xb03774: r0 = LoadInt32Instr(r1)
    //     0xb03774: sbfx            x0, x1, #1, #0x1f
    // 0xb03778: cmp             x0, #2
    // 0xb0377c: b.lt            #0xb037d4
    // 0xb03780: ldur            x0, [fp, #-8]
    // 0xb03784: LoadField: r1 = r0->field_57
    //     0xb03784: ldur            w1, [x0, #0x57]
    // 0xb03788: DecompressPointer r1
    //     0xb03788: add             x1, x1, HEAP, lsl #32
    // 0xb0378c: r0 = LoadClassIdInstr(r1)
    //     0xb0378c: ldur            x0, [x1, #-1]
    //     0xb03790: ubfx            x0, x0, #0xc, #0x14
    // 0xb03794: r2 = ", "
    //     0xb03794: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03798: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03798: sub             lr, x0, #1, lsl #12
    //     0xb0379c: ldr             lr, [x21, lr, lsl #3]
    //     0xb037a0: blr             lr
    // 0xb037a4: mov             x2, x0
    // 0xb037a8: LoadField: r3 = r2->field_b
    //     0xb037a8: ldur            w3, [x2, #0xb]
    // 0xb037ac: r0 = LoadInt32Instr(r3)
    //     0xb037ac: sbfx            x0, x3, #1, #0x1f
    // 0xb037b0: r1 = 1
    //     0xb037b0: movz            x1, #0x1
    // 0xb037b4: cmp             x1, x0
    // 0xb037b8: b.hs            #0xb037ec
    // 0xb037bc: LoadField: r1 = r2->field_f
    //     0xb037bc: ldur            w1, [x2, #0xf]
    // 0xb037c0: DecompressPointer r1
    //     0xb037c0: add             x1, x1, HEAP, lsl #32
    // 0xb037c4: LoadField: r2 = r1->field_13
    //     0xb037c4: ldur            w2, [x1, #0x13]
    // 0xb037c8: DecompressPointer r2
    //     0xb037c8: add             x2, x2, HEAP, lsl #32
    // 0xb037cc: mov             x0, x2
    // 0xb037d0: b               #0xb037d8
    // 0xb037d4: r0 = ""
    //     0xb037d4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb037d8: LeaveFrame
    //     0xb037d8: mov             SP, fp
    //     0xb037dc: ldp             fp, lr, [SP], #0x10
    // 0xb037e0: ret
    //     0xb037e0: ret             
    // 0xb037e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb037e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb037e8: b               #0xb03750
    // 0xb037ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb037ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  String regency(Locality) {
    // ** addr: 0xb037f0, size: 0xc0
    // 0xb037f0: EnterFrame
    //     0xb037f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb037f4: mov             fp, SP
    // 0xb037f8: AllocStack(0x8)
    //     0xb037f8: sub             SP, SP, #8
    // 0xb037fc: SetupParameters(Locality this /* r1 => r3, fp-0x8 */)
    //     0xb037fc: mov             x3, x1
    //     0xb03800: stur            x1, [fp, #-8]
    // 0xb03804: CheckStackOverflow
    //     0xb03804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03808: cmp             SP, x16
    //     0xb0380c: b.ls            #0xb038a4
    // 0xb03810: LoadField: r1 = r3->field_57
    //     0xb03810: ldur            w1, [x3, #0x57]
    // 0xb03814: DecompressPointer r1
    //     0xb03814: add             x1, x1, HEAP, lsl #32
    // 0xb03818: r0 = LoadClassIdInstr(r1)
    //     0xb03818: ldur            x0, [x1, #-1]
    //     0xb0381c: ubfx            x0, x0, #0xc, #0x14
    // 0xb03820: r2 = ", "
    //     0xb03820: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03824: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03824: sub             lr, x0, #1, lsl #12
    //     0xb03828: ldr             lr, [x21, lr, lsl #3]
    //     0xb0382c: blr             lr
    // 0xb03830: LoadField: r1 = r0->field_b
    //     0xb03830: ldur            w1, [x0, #0xb]
    // 0xb03834: r0 = LoadInt32Instr(r1)
    //     0xb03834: sbfx            x0, x1, #1, #0x1f
    // 0xb03838: cmp             x0, #3
    // 0xb0383c: b.lt            #0xb03894
    // 0xb03840: ldur            x0, [fp, #-8]
    // 0xb03844: LoadField: r1 = r0->field_57
    //     0xb03844: ldur            w1, [x0, #0x57]
    // 0xb03848: DecompressPointer r1
    //     0xb03848: add             x1, x1, HEAP, lsl #32
    // 0xb0384c: r0 = LoadClassIdInstr(r1)
    //     0xb0384c: ldur            x0, [x1, #-1]
    //     0xb03850: ubfx            x0, x0, #0xc, #0x14
    // 0xb03854: r2 = ", "
    //     0xb03854: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03858: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03858: sub             lr, x0, #1, lsl #12
    //     0xb0385c: ldr             lr, [x21, lr, lsl #3]
    //     0xb03860: blr             lr
    // 0xb03864: mov             x2, x0
    // 0xb03868: LoadField: r3 = r2->field_b
    //     0xb03868: ldur            w3, [x2, #0xb]
    // 0xb0386c: r0 = LoadInt32Instr(r3)
    //     0xb0386c: sbfx            x0, x3, #1, #0x1f
    // 0xb03870: r1 = 2
    //     0xb03870: movz            x1, #0x2
    // 0xb03874: cmp             x1, x0
    // 0xb03878: b.hs            #0xb038ac
    // 0xb0387c: LoadField: r1 = r2->field_f
    //     0xb0387c: ldur            w1, [x2, #0xf]
    // 0xb03880: DecompressPointer r1
    //     0xb03880: add             x1, x1, HEAP, lsl #32
    // 0xb03884: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb03884: ldur            w2, [x1, #0x17]
    // 0xb03888: DecompressPointer r2
    //     0xb03888: add             x2, x2, HEAP, lsl #32
    // 0xb0388c: mov             x0, x2
    // 0xb03890: b               #0xb03898
    // 0xb03894: r0 = ""
    //     0xb03894: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb03898: LeaveFrame
    //     0xb03898: mov             SP, fp
    //     0xb0389c: ldp             fp, lr, [SP], #0x10
    // 0xb038a0: ret
    //     0xb038a0: ret             
    // 0xb038a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb038a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb038a8: b               #0xb03810
    // 0xb038ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb038ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  String province(Locality) {
    // ** addr: 0xb038b0, size: 0xbc
    // 0xb038b0: EnterFrame
    //     0xb038b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb038b4: mov             fp, SP
    // 0xb038b8: AllocStack(0x8)
    //     0xb038b8: sub             SP, SP, #8
    // 0xb038bc: SetupParameters(Locality this /* r1 => r3, fp-0x8 */)
    //     0xb038bc: mov             x3, x1
    //     0xb038c0: stur            x1, [fp, #-8]
    // 0xb038c4: CheckStackOverflow
    //     0xb038c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb038c8: cmp             SP, x16
    //     0xb038cc: b.ls            #0xb03960
    // 0xb038d0: LoadField: r1 = r3->field_57
    //     0xb038d0: ldur            w1, [x3, #0x57]
    // 0xb038d4: DecompressPointer r1
    //     0xb038d4: add             x1, x1, HEAP, lsl #32
    // 0xb038d8: r0 = LoadClassIdInstr(r1)
    //     0xb038d8: ldur            x0, [x1, #-1]
    //     0xb038dc: ubfx            x0, x0, #0xc, #0x14
    // 0xb038e0: r2 = ", "
    //     0xb038e0: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb038e4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb038e4: sub             lr, x0, #1, lsl #12
    //     0xb038e8: ldr             lr, [x21, lr, lsl #3]
    //     0xb038ec: blr             lr
    // 0xb038f0: LoadField: r1 = r0->field_b
    //     0xb038f0: ldur            w1, [x0, #0xb]
    // 0xb038f4: cmp             w1, #8
    // 0xb038f8: b.ne            #0xb03950
    // 0xb038fc: ldur            x0, [fp, #-8]
    // 0xb03900: LoadField: r1 = r0->field_57
    //     0xb03900: ldur            w1, [x0, #0x57]
    // 0xb03904: DecompressPointer r1
    //     0xb03904: add             x1, x1, HEAP, lsl #32
    // 0xb03908: r0 = LoadClassIdInstr(r1)
    //     0xb03908: ldur            x0, [x1, #-1]
    //     0xb0390c: ubfx            x0, x0, #0xc, #0x14
    // 0xb03910: r2 = ", "
    //     0xb03910: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb03914: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb03914: sub             lr, x0, #1, lsl #12
    //     0xb03918: ldr             lr, [x21, lr, lsl #3]
    //     0xb0391c: blr             lr
    // 0xb03920: mov             x2, x0
    // 0xb03924: LoadField: r3 = r2->field_b
    //     0xb03924: ldur            w3, [x2, #0xb]
    // 0xb03928: r0 = LoadInt32Instr(r3)
    //     0xb03928: sbfx            x0, x3, #1, #0x1f
    // 0xb0392c: r1 = 3
    //     0xb0392c: movz            x1, #0x3
    // 0xb03930: cmp             x1, x0
    // 0xb03934: b.hs            #0xb03968
    // 0xb03938: LoadField: r1 = r2->field_f
    //     0xb03938: ldur            w1, [x2, #0xf]
    // 0xb0393c: DecompressPointer r1
    //     0xb0393c: add             x1, x1, HEAP, lsl #32
    // 0xb03940: LoadField: r2 = r1->field_1b
    //     0xb03940: ldur            w2, [x1, #0x1b]
    // 0xb03944: DecompressPointer r2
    //     0xb03944: add             x2, x2, HEAP, lsl #32
    // 0xb03948: mov             x0, x2
    // 0xb0394c: b               #0xb03954
    // 0xb03950: r0 = ""
    //     0xb03950: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb03954: LeaveFrame
    //     0xb03954: mov             SP, fp
    //     0xb03958: ldp             fp, lr, [SP], #0x10
    // 0xb0395c: ret
    //     0xb0395c: ret             
    // 0xb03960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb03960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb03964: b               #0xb038d0
    // 0xb03968: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb03968: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
