// lib: , url: package:nuonline/app/data/models/location.dart

// class id: 1050030, size: 0x8
class :: {
}

// class id: 1606, size: 0x48, field offset: 0x14
class Location extends HiveObject {

  get _ address(/* No info */) {
    // ** addr: 0x81e19c, size: 0x128
    // 0x81e19c: EnterFrame
    //     0x81e19c: stp             fp, lr, [SP, #-0x10]!
    //     0x81e1a0: mov             fp, SP
    // 0x81e1a4: AllocStack(0x40)
    //     0x81e1a4: sub             SP, SP, #0x40
    // 0x81e1a8: r0 = 8
    //     0x81e1a8: movz            x0, #0x8
    // 0x81e1ac: CheckStackOverflow
    //     0x81e1ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81e1b0: cmp             SP, x16
    //     0x81e1b4: b.ls            #0x81e2bc
    // 0x81e1b8: LoadField: r3 = r1->field_43
    //     0x81e1b8: ldur            w3, [x1, #0x43]
    // 0x81e1bc: DecompressPointer r3
    //     0x81e1bc: add             x3, x3, HEAP, lsl #32
    // 0x81e1c0: stur            x3, [fp, #-0x20]
    // 0x81e1c4: LoadField: r4 = r1->field_3f
    //     0x81e1c4: ldur            w4, [x1, #0x3f]
    // 0x81e1c8: DecompressPointer r4
    //     0x81e1c8: add             x4, x4, HEAP, lsl #32
    // 0x81e1cc: stur            x4, [fp, #-0x18]
    // 0x81e1d0: LoadField: r5 = r1->field_1b
    //     0x81e1d0: ldur            w5, [x1, #0x1b]
    // 0x81e1d4: DecompressPointer r5
    //     0x81e1d4: add             x5, x5, HEAP, lsl #32
    // 0x81e1d8: stur            x5, [fp, #-0x10]
    // 0x81e1dc: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x81e1dc: ldur            w6, [x1, #0x17]
    // 0x81e1e0: DecompressPointer r6
    //     0x81e1e0: add             x6, x6, HEAP, lsl #32
    // 0x81e1e4: mov             x2, x0
    // 0x81e1e8: stur            x6, [fp, #-8]
    // 0x81e1ec: r1 = Null
    //     0x81e1ec: mov             x1, NULL
    // 0x81e1f0: r0 = AllocateArray()
    //     0x81e1f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81e1f4: mov             x2, x0
    // 0x81e1f8: ldur            x0, [fp, #-0x20]
    // 0x81e1fc: stur            x2, [fp, #-0x28]
    // 0x81e200: StoreField: r2->field_f = r0
    //     0x81e200: stur            w0, [x2, #0xf]
    // 0x81e204: ldur            x0, [fp, #-0x18]
    // 0x81e208: StoreField: r2->field_13 = r0
    //     0x81e208: stur            w0, [x2, #0x13]
    // 0x81e20c: ldur            x0, [fp, #-0x10]
    // 0x81e210: ArrayStore: r2[0] = r0  ; List_4
    //     0x81e210: stur            w0, [x2, #0x17]
    // 0x81e214: ldur            x0, [fp, #-8]
    // 0x81e218: StoreField: r2->field_1b = r0
    //     0x81e218: stur            w0, [x2, #0x1b]
    // 0x81e21c: r1 = <String?>
    //     0x81e21c: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x81e220: r0 = AllocateGrowableArray()
    //     0x81e220: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x81e224: mov             x3, x0
    // 0x81e228: ldur            x0, [fp, #-0x28]
    // 0x81e22c: stur            x3, [fp, #-8]
    // 0x81e230: StoreField: r3->field_f = r0
    //     0x81e230: stur            w0, [x3, #0xf]
    // 0x81e234: r0 = 8
    //     0x81e234: movz            x0, #0x8
    // 0x81e238: StoreField: r3->field_b = r0
    //     0x81e238: stur            w0, [x3, #0xb]
    // 0x81e23c: r1 = Function '<anonymous closure>':.
    //     0x81e23c: add             x1, PP, #8, lsl #12  ; [pp+0x8dd8] AnonymousClosure: (0x81e334), in [package:nuonline/app/data/models/location.dart] Location::address (0x81e19c)
    //     0x81e240: ldr             x1, [x1, #0xdd8]
    // 0x81e244: r2 = Null
    //     0x81e244: mov             x2, NULL
    // 0x81e248: r0 = AllocateClosure()
    //     0x81e248: bl              #0xec1630  ; AllocateClosureStub
    // 0x81e24c: r16 = <String?>
    //     0x81e24c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0x81e250: ldur            lr, [fp, #-8]
    // 0x81e254: stp             lr, x16, [SP, #8]
    // 0x81e258: str             x0, [SP]
    // 0x81e25c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x81e25c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x81e260: r0 = ListExtension.filter()
    //     0x81e260: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0x81e264: mov             x1, x0
    // 0x81e268: r2 = 3
    //     0x81e268: movz            x2, #0x3
    // 0x81e26c: r0 = take()
    //     0x81e26c: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0x81e270: r16 = ", "
    //     0x81e270: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x81e274: str             x16, [SP]
    // 0x81e278: mov             x1, x0
    // 0x81e27c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x81e27c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x81e280: r0 = join()
    //     0x81e280: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0x81e284: mov             x1, x0
    // 0x81e288: r2 = "Kecamatan"
    //     0x81e288: add             x2, PP, #8, lsl #12  ; [pp+0x8de0] "Kecamatan"
    //     0x81e28c: ldr             x2, [x2, #0xde0]
    // 0x81e290: r3 = "Kec."
    //     0x81e290: add             x3, PP, #8, lsl #12  ; [pp+0x8de8] "Kec."
    //     0x81e294: ldr             x3, [x3, #0xde8]
    // 0x81e298: r0 = replaceAll()
    //     0x81e298: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x81e29c: mov             x1, x0
    // 0x81e2a0: r2 = "Kota Administrasi "
    //     0x81e2a0: add             x2, PP, #8, lsl #12  ; [pp+0x8df0] "Kota Administrasi "
    //     0x81e2a4: ldr             x2, [x2, #0xdf0]
    // 0x81e2a8: r3 = ""
    //     0x81e2a8: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x81e2ac: r0 = replaceAll()
    //     0x81e2ac: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x81e2b0: LeaveFrame
    //     0x81e2b0: mov             SP, fp
    //     0x81e2b4: ldp             fp, lr, [SP], #0x10
    // 0x81e2b8: ret
    //     0x81e2b8: ret             
    // 0x81e2bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81e2bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81e2c0: b               #0x81e1b8
  }
  [closure] bool <anonymous closure>(dynamic, String?) {
    // ** addr: 0x81e334, size: 0x30
    // 0x81e334: ldr             x1, [SP]
    // 0x81e338: cmp             w1, NULL
    // 0x81e33c: b.eq            #0x81e35c
    // 0x81e340: LoadField: r2 = r1->field_7
    //     0x81e340: ldur            w2, [x1, #7]
    // 0x81e344: cbnz            w2, #0x81e350
    // 0x81e348: r1 = false
    //     0x81e348: add             x1, NULL, #0x30  ; false
    // 0x81e34c: b               #0x81e354
    // 0x81e350: r1 = true
    //     0x81e350: add             x1, NULL, #0x20  ; true
    // 0x81e354: mov             x0, x1
    // 0x81e358: b               #0x81e360
    // 0x81e35c: r0 = false
    //     0x81e35c: add             x0, NULL, #0x30  ; false
    // 0x81e360: ret
    //     0x81e360: ret             
  }
  _ Location(/* No info */) {
    // ** addr: 0x8ff888, size: 0x2dc
    // 0x8ff888: EnterFrame
    //     0x8ff888: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff88c: mov             fp, SP
    // 0x8ff890: AllocStack(0x18)
    //     0x8ff890: sub             SP, SP, #0x18
    // 0x8ff894: SetupParameters(Location this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r5 */, dynamic _ /* r5 => r3 */, dynamic _ /* r6 => r2 */, dynamic _ /* r7 => r1 */, {dynamic altitude = Null /* r9 */, dynamic district = Null /* r10 */, dynamic isFromGPS = false /* r11 */, dynamic locality = Null /* r4 */})
    //     0x8ff894: mov             x0, x2
    //     0x8ff898: mov             x2, x6
    //     0x8ff89c: mov             x6, x1
    //     0x8ff8a0: mov             x16, x5
    //     0x8ff8a4: mov             x5, x3
    //     0x8ff8a8: mov             x3, x16
    //     0x8ff8ac: stur            x1, [fp, #-8]
    //     0x8ff8b0: mov             x1, x7
    //     0x8ff8b4: ldur            w7, [x4, #0x13]
    //     0x8ff8b8: ldur            w8, [x4, #0x1f]
    //     0x8ff8bc: add             x8, x8, HEAP, lsl #32
    //     0x8ff8c0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf548] "altitude"
    //     0x8ff8c4: ldr             x16, [x16, #0x548]
    //     0x8ff8c8: cmp             w8, w16
    //     0x8ff8cc: b.ne            #0x8ff8f0
    //     0x8ff8d0: ldur            w8, [x4, #0x23]
    //     0x8ff8d4: add             x8, x8, HEAP, lsl #32
    //     0x8ff8d8: sub             w9, w7, w8
    //     0x8ff8dc: add             x8, fp, w9, sxtw #2
    //     0x8ff8e0: ldr             x8, [x8, #8]
    //     0x8ff8e4: mov             x9, x8
    //     0x8ff8e8: movz            x8, #0x1
    //     0x8ff8ec: b               #0x8ff8f8
    //     0x8ff8f0: mov             x9, NULL
    //     0x8ff8f4: movz            x8, #0
    //     0x8ff8f8: lsl             x10, x8, #1
    //     0x8ff8fc: lsl             w11, w10, #1
    //     0x8ff900: add             w12, w11, #8
    //     0x8ff904: add             x16, x4, w12, sxtw #1
    //     0x8ff908: ldur            w13, [x16, #0xf]
    //     0x8ff90c: add             x13, x13, HEAP, lsl #32
    //     0x8ff910: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c430] "district"
    //     0x8ff914: ldr             x16, [x16, #0x430]
    //     0x8ff918: cmp             w13, w16
    //     0x8ff91c: b.ne            #0x8ff950
    //     0x8ff920: add             w8, w11, #0xa
    //     0x8ff924: add             x16, x4, w8, sxtw #1
    //     0x8ff928: ldur            w11, [x16, #0xf]
    //     0x8ff92c: add             x11, x11, HEAP, lsl #32
    //     0x8ff930: sub             w8, w7, w11
    //     0x8ff934: add             x11, fp, w8, sxtw #2
    //     0x8ff938: ldr             x11, [x11, #8]
    //     0x8ff93c: add             w8, w10, #2
    //     0x8ff940: sbfx            x10, x8, #1, #0x1f
    //     0x8ff944: mov             x8, x10
    //     0x8ff948: mov             x10, x11
    //     0x8ff94c: b               #0x8ff954
    //     0x8ff950: mov             x10, NULL
    //     0x8ff954: lsl             x11, x8, #1
    //     0x8ff958: lsl             w12, w11, #1
    //     0x8ff95c: add             w13, w12, #8
    //     0x8ff960: add             x16, x4, w13, sxtw #1
    //     0x8ff964: ldur            w14, [x16, #0xf]
    //     0x8ff968: add             x14, x14, HEAP, lsl #32
    //     0x8ff96c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c438] "isFromGPS"
    //     0x8ff970: ldr             x16, [x16, #0x438]
    //     0x8ff974: cmp             w14, w16
    //     0x8ff978: b.ne            #0x8ff9ac
    //     0x8ff97c: add             w8, w12, #0xa
    //     0x8ff980: add             x16, x4, w8, sxtw #1
    //     0x8ff984: ldur            w12, [x16, #0xf]
    //     0x8ff988: add             x12, x12, HEAP, lsl #32
    //     0x8ff98c: sub             w8, w7, w12
    //     0x8ff990: add             x12, fp, w8, sxtw #2
    //     0x8ff994: ldr             x12, [x12, #8]
    //     0x8ff998: add             w8, w11, #2
    //     0x8ff99c: sbfx            x11, x8, #1, #0x1f
    //     0x8ff9a0: mov             x8, x11
    //     0x8ff9a4: mov             x11, x12
    //     0x8ff9a8: b               #0x8ff9b0
    //     0x8ff9ac: add             x11, NULL, #0x30  ; false
    //     0x8ff9b0: lsl             x12, x8, #1
    //     0x8ff9b4: lsl             w8, w12, #1
    //     0x8ff9b8: add             w12, w8, #8
    //     0x8ff9bc: add             x16, x4, w12, sxtw #1
    //     0x8ff9c0: ldur            w13, [x16, #0xf]
    //     0x8ff9c4: add             x13, x13, HEAP, lsl #32
    //     0x8ff9c8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c440] "locality"
    //     0x8ff9cc: ldr             x16, [x16, #0x440]
    //     0x8ff9d0: cmp             w13, w16
    //     0x8ff9d4: b.ne            #0x8ff9fc
    //     0x8ff9d8: add             w12, w8, #0xa
    //     0x8ff9dc: add             x16, x4, w12, sxtw #1
    //     0x8ff9e0: ldur            w8, [x16, #0xf]
    //     0x8ff9e4: add             x8, x8, HEAP, lsl #32
    //     0x8ff9e8: sub             w4, w7, w8
    //     0x8ff9ec: add             x7, fp, w4, sxtw #2
    //     0x8ff9f0: ldr             x7, [x7, #8]
    //     0x8ff9f4: mov             x4, x7
    //     0x8ff9f8: b               #0x8ffa00
    //     0x8ff9fc: mov             x4, NULL
    // 0x8ffa00: CheckStackOverflow
    //     0x8ffa00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffa04: cmp             SP, x16
    //     0x8ffa08: b.ls            #0x8ffb5c
    // 0x8ffa0c: StoreField: r6->field_13 = r0
    //     0x8ffa0c: stur            w0, [x6, #0x13]
    //     0x8ffa10: ldurb           w16, [x6, #-1]
    //     0x8ffa14: ldurb           w17, [x0, #-1]
    //     0x8ffa18: and             x16, x17, x16, lsr #2
    //     0x8ffa1c: tst             x16, HEAP, lsr #32
    //     0x8ffa20: b.eq            #0x8ffa28
    //     0x8ffa24: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffa28: mov             x0, x3
    // 0x8ffa2c: ArrayStore: r6[0] = r0  ; List_4
    //     0x8ffa2c: stur            w0, [x6, #0x17]
    //     0x8ffa30: ldurb           w16, [x6, #-1]
    //     0x8ffa34: ldurb           w17, [x0, #-1]
    //     0x8ffa38: and             x16, x17, x16, lsr #2
    //     0x8ffa3c: tst             x16, HEAP, lsr #32
    //     0x8ffa40: b.eq            #0x8ffa48
    //     0x8ffa44: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffa48: mov             x0, x2
    // 0x8ffa4c: StoreField: r6->field_1b = r0
    //     0x8ffa4c: stur            w0, [x6, #0x1b]
    //     0x8ffa50: ldurb           w16, [x6, #-1]
    //     0x8ffa54: ldurb           w17, [x0, #-1]
    //     0x8ffa58: and             x16, x17, x16, lsr #2
    //     0x8ffa5c: tst             x16, HEAP, lsr #32
    //     0x8ffa60: b.eq            #0x8ffa68
    //     0x8ffa64: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffa68: StoreField: r6->field_1f = d1
    //     0x8ffa68: stur            d1, [x6, #0x1f]
    // 0x8ffa6c: StoreField: r6->field_27 = d0
    //     0x8ffa6c: stur            d0, [x6, #0x27]
    // 0x8ffa70: mov             x0, x1
    // 0x8ffa74: StoreField: r6->field_33 = r0
    //     0x8ffa74: stur            w0, [x6, #0x33]
    //     0x8ffa78: ldurb           w16, [x6, #-1]
    //     0x8ffa7c: ldurb           w17, [x0, #-1]
    //     0x8ffa80: and             x16, x17, x16, lsr #2
    //     0x8ffa84: tst             x16, HEAP, lsr #32
    //     0x8ffa88: b.eq            #0x8ffa90
    //     0x8ffa8c: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffa90: mov             x0, x10
    // 0x8ffa94: StoreField: r6->field_3f = r0
    //     0x8ffa94: stur            w0, [x6, #0x3f]
    //     0x8ffa98: ldurb           w16, [x6, #-1]
    //     0x8ffa9c: ldurb           w17, [x0, #-1]
    //     0x8ffaa0: and             x16, x17, x16, lsr #2
    //     0x8ffaa4: tst             x16, HEAP, lsr #32
    //     0x8ffaa8: b.eq            #0x8ffab0
    //     0x8ffaac: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffab0: mov             x0, x4
    // 0x8ffab4: StoreField: r6->field_43 = r0
    //     0x8ffab4: stur            w0, [x6, #0x43]
    //     0x8ffab8: ldurb           w16, [x6, #-1]
    //     0x8ffabc: ldurb           w17, [x0, #-1]
    //     0x8ffac0: and             x16, x17, x16, lsr #2
    //     0x8ffac4: tst             x16, HEAP, lsr #32
    //     0x8ffac8: b.eq            #0x8ffad0
    //     0x8ffacc: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffad0: mov             x0, x5
    // 0x8ffad4: StoreField: r6->field_3b = r0
    //     0x8ffad4: stur            w0, [x6, #0x3b]
    //     0x8ffad8: ldurb           w16, [x6, #-1]
    //     0x8ffadc: ldurb           w17, [x0, #-1]
    //     0x8ffae0: and             x16, x17, x16, lsr #2
    //     0x8ffae4: tst             x16, HEAP, lsr #32
    //     0x8ffae8: b.eq            #0x8ffaf0
    //     0x8ffaec: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffaf0: mov             x0, x9
    // 0x8ffaf4: StoreField: r6->field_2f = r0
    //     0x8ffaf4: stur            w0, [x6, #0x2f]
    //     0x8ffaf8: tbz             w0, #0, #0x8ffb14
    //     0x8ffafc: ldurb           w16, [x6, #-1]
    //     0x8ffb00: ldurb           w17, [x0, #-1]
    //     0x8ffb04: and             x16, x17, x16, lsr #2
    //     0x8ffb08: tst             x16, HEAP, lsr #32
    //     0x8ffb0c: b.eq            #0x8ffb14
    //     0x8ffb10: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8ffb14: StoreField: r6->field_37 = r11
    //     0x8ffb14: stur            w11, [x6, #0x37]
    // 0x8ffb18: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8ffb18: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8ffb1c: ldr             x16, [x16, #0x9f8]
    // 0x8ffb20: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8ffb24: stp             lr, x16, [SP]
    // 0x8ffb28: r0 = Map._fromLiteral()
    //     0x8ffb28: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8ffb2c: ldur            x1, [fp, #-8]
    // 0x8ffb30: StoreField: r1->field_f = r0
    //     0x8ffb30: stur            w0, [x1, #0xf]
    //     0x8ffb34: ldurb           w16, [x1, #-1]
    //     0x8ffb38: ldurb           w17, [x0, #-1]
    //     0x8ffb3c: and             x16, x17, x16, lsr #2
    //     0x8ffb40: tst             x16, HEAP, lsr #32
    //     0x8ffb44: b.eq            #0x8ffb4c
    //     0x8ffb48: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ffb4c: r0 = Null
    //     0x8ffb4c: mov             x0, NULL
    // 0x8ffb50: LeaveFrame
    //     0x8ffb50: mov             SP, fp
    //     0x8ffb54: ldp             fp, lr, [SP], #0x10
    // 0x8ffb58: ret
    //     0x8ffb58: ret             
    // 0x8ffb5c: r0 = StackOverflowSharedWithFPURegs()
    //     0x8ffb5c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8ffb60: b               #0x8ffa0c
  }
  factory _ Location.fromLocality(/* No info */) {
    // ** addr: 0xb03408, size: 0x1c4
    // 0xb03408: EnterFrame
    //     0xb03408: stp             fp, lr, [SP, #-0x10]!
    //     0xb0340c: mov             fp, SP
    // 0xb03410: AllocStack(0x58)
    //     0xb03410: sub             SP, SP, #0x58
    // 0xb03414: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb03414: mov             x0, x1
    //     0xb03418: mov             x1, x2
    //     0xb0341c: stur            x2, [fp, #-0x10]
    // 0xb03420: CheckStackOverflow
    //     0xb03420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03424: cmp             SP, x16
    //     0xb03428: b.ls            #0xb035c4
    // 0xb0342c: LoadField: r0 = r1->field_4b
    //     0xb0342c: ldur            w0, [x1, #0x4b]
    // 0xb03430: DecompressPointer r0
    //     0xb03430: add             x0, x0, HEAP, lsl #32
    // 0xb03434: stur            x0, [fp, #-8]
    // 0xb03438: r16 = "Asia/Jakarta"
    //     0xb03438: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b210] "Asia/Jakarta"
    //     0xb0343c: ldr             x16, [x16, #0x210]
    // 0xb03440: stp             x0, x16, [SP]
    // 0xb03444: r0 = ==()
    //     0xb03444: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb03448: tbnz            w0, #4, #0xb03458
    // 0xb0344c: r2 = "WIB"
    //     0xb0344c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cf38] "WIB"
    //     0xb03450: ldr             x2, [x2, #0xf38]
    // 0xb03454: b               #0xb034a8
    // 0xb03458: r16 = "Asia/Makasar"
    //     0xb03458: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cf40] "Asia/Makasar"
    //     0xb0345c: ldr             x16, [x16, #0xf40]
    // 0xb03460: ldur            lr, [fp, #-8]
    // 0xb03464: stp             lr, x16, [SP]
    // 0xb03468: r0 = ==()
    //     0xb03468: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb0346c: tbnz            w0, #4, #0xb0347c
    // 0xb03470: r2 = "WITA"
    //     0xb03470: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cf48] "WITA"
    //     0xb03474: ldr             x2, [x2, #0xf48]
    // 0xb03478: b               #0xb034a8
    // 0xb0347c: r16 = "Asia/Jayapura"
    //     0xb0347c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cf50] "Asia/Jayapura"
    //     0xb03480: ldr             x16, [x16, #0xf50]
    // 0xb03484: ldur            lr, [fp, #-8]
    // 0xb03488: stp             lr, x16, [SP]
    // 0xb0348c: r0 = ==()
    //     0xb0348c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb03490: tbnz            w0, #4, #0xb034a0
    // 0xb03494: r0 = "WIT"
    //     0xb03494: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cf58] "WIT"
    //     0xb03498: ldr             x0, [x0, #0xf58]
    // 0xb0349c: b               #0xb034a4
    // 0xb034a0: ldur            x0, [fp, #-8]
    // 0xb034a4: mov             x2, x0
    // 0xb034a8: ldur            x0, [fp, #-0x10]
    // 0xb034ac: mov             x1, x0
    // 0xb034b0: stur            x2, [fp, #-8]
    // 0xb034b4: r0 = province()
    //     0xb034b4: bl              #0xb038b0  ; [package:nuonline/app/data/models/locality.dart] Locality::province
    // 0xb034b8: ldur            x1, [fp, #-0x10]
    // 0xb034bc: stur            x0, [fp, #-0x18]
    // 0xb034c0: r0 = regency()
    //     0xb034c0: bl              #0xb037f0  ; [package:nuonline/app/data/models/locality.dart] Locality::regency
    // 0xb034c4: ldur            x1, [fp, #-0x10]
    // 0xb034c8: stur            x0, [fp, #-0x20]
    // 0xb034cc: r0 = district()
    //     0xb034cc: bl              #0xb03730  ; [package:nuonline/app/data/models/locality.dart] Locality::district
    // 0xb034d0: ldur            x1, [fp, #-0x10]
    // 0xb034d4: stur            x0, [fp, #-0x28]
    // 0xb034d8: r0 = locality()
    //     0xb034d8: bl              #0xb035cc  ; [package:nuonline/app/data/models/locality.dart] Locality::locality
    // 0xb034dc: mov             x1, x0
    // 0xb034e0: ldur            x0, [fp, #-0x10]
    // 0xb034e4: stur            x1, [fp, #-0x38]
    // 0xb034e8: LoadField: d0 = r0->field_37
    //     0xb034e8: ldur            d0, [x0, #0x37]
    // 0xb034ec: stur            d0, [fp, #-0x48]
    // 0xb034f0: LoadField: d1 = r0->field_2f
    //     0xb034f0: ldur            d1, [x0, #0x2f]
    // 0xb034f4: stur            d1, [fp, #-0x40]
    // 0xb034f8: LoadField: r2 = r0->field_3f
    //     0xb034f8: ldur            x2, [x0, #0x3f]
    // 0xb034fc: stur            x2, [fp, #-0x30]
    // 0xb03500: r0 = Location()
    //     0xb03500: bl              #0x8ffb64  ; AllocateLocationStub -> Location (size=0x48)
    // 0xb03504: mov             x2, x0
    // 0xb03508: r0 = "ID"
    //     0xb03508: add             x0, PP, #8, lsl #12  ; [pp+0x8f40] "ID"
    //     0xb0350c: ldr             x0, [x0, #0xf40]
    // 0xb03510: stur            x2, [fp, #-0x10]
    // 0xb03514: StoreField: r2->field_13 = r0
    //     0xb03514: stur            w0, [x2, #0x13]
    // 0xb03518: ldur            x0, [fp, #-0x18]
    // 0xb0351c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb0351c: stur            w0, [x2, #0x17]
    // 0xb03520: ldur            x0, [fp, #-0x20]
    // 0xb03524: StoreField: r2->field_1b = r0
    //     0xb03524: stur            w0, [x2, #0x1b]
    // 0xb03528: ldur            d0, [fp, #-0x48]
    // 0xb0352c: StoreField: r2->field_1f = d0
    //     0xb0352c: stur            d0, [x2, #0x1f]
    // 0xb03530: ldur            d0, [fp, #-0x40]
    // 0xb03534: StoreField: r2->field_27 = d0
    //     0xb03534: stur            d0, [x2, #0x27]
    // 0xb03538: ldur            x0, [fp, #-8]
    // 0xb0353c: StoreField: r2->field_33 = r0
    //     0xb0353c: stur            w0, [x2, #0x33]
    // 0xb03540: ldur            x0, [fp, #-0x28]
    // 0xb03544: StoreField: r2->field_3f = r0
    //     0xb03544: stur            w0, [x2, #0x3f]
    // 0xb03548: ldur            x0, [fp, #-0x38]
    // 0xb0354c: StoreField: r2->field_43 = r0
    //     0xb0354c: stur            w0, [x2, #0x43]
    // 0xb03550: r0 = "Indonesia"
    //     0xb03550: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cf60] "Indonesia"
    //     0xb03554: ldr             x0, [x0, #0xf60]
    // 0xb03558: StoreField: r2->field_3b = r0
    //     0xb03558: stur            w0, [x2, #0x3b]
    // 0xb0355c: ldur            x3, [fp, #-0x30]
    // 0xb03560: r0 = BoxInt64Instr(r3)
    //     0xb03560: sbfiz           x0, x3, #1, #0x1f
    //     0xb03564: cmp             x3, x0, asr #1
    //     0xb03568: b.eq            #0xb03574
    //     0xb0356c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb03570: stur            x3, [x0, #7]
    // 0xb03574: StoreField: r2->field_2f = r0
    //     0xb03574: stur            w0, [x2, #0x2f]
    // 0xb03578: r0 = false
    //     0xb03578: add             x0, NULL, #0x30  ; false
    // 0xb0357c: StoreField: r2->field_37 = r0
    //     0xb0357c: stur            w0, [x2, #0x37]
    // 0xb03580: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xb03580: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xb03584: ldr             x16, [x16, #0x9f8]
    // 0xb03588: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xb0358c: stp             lr, x16, [SP]
    // 0xb03590: r0 = Map._fromLiteral()
    //     0xb03590: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb03594: ldur            x1, [fp, #-0x10]
    // 0xb03598: StoreField: r1->field_f = r0
    //     0xb03598: stur            w0, [x1, #0xf]
    //     0xb0359c: ldurb           w16, [x1, #-1]
    //     0xb035a0: ldurb           w17, [x0, #-1]
    //     0xb035a4: and             x16, x17, x16, lsr #2
    //     0xb035a8: tst             x16, HEAP, lsr #32
    //     0xb035ac: b.eq            #0xb035b4
    //     0xb035b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb035b4: mov             x0, x1
    // 0xb035b8: LeaveFrame
    //     0xb035b8: mov             SP, fp
    //     0xb035bc: ldp             fp, lr, [SP], #0x10
    // 0xb035c0: ret
    //     0xb035c0: ret             
    // 0xb035c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb035c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb035c8: b               #0xb0342c
  }
  _ getDMS(/* No info */) {
    // ** addr: 0xb1144c, size: 0x10c
    // 0xb1144c: EnterFrame
    //     0xb1144c: stp             fp, lr, [SP, #-0x10]!
    //     0xb11450: mov             fp, SP
    // 0xb11454: AllocStack(0x28)
    //     0xb11454: sub             SP, SP, #0x28
    // 0xb11458: SetupParameters(Location this /* r1 => r0, fp-0x8 */)
    //     0xb11458: mov             x0, x1
    //     0xb1145c: stur            x1, [fp, #-8]
    // 0xb11460: CheckStackOverflow
    //     0xb11460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb11464: cmp             SP, x16
    //     0xb11468: b.ls            #0xb11550
    // 0xb1146c: LoadField: d0 = r0->field_27
    //     0xb1146c: ldur            d0, [x0, #0x27]
    // 0xb11470: mov             x1, x0
    // 0xb11474: r0 = toDMS()
    //     0xb11474: bl              #0xb11558  ; [package:nuonline/app/data/models/location.dart] Location::toDMS
    // 0xb11478: mov             x2, x0
    // 0xb1147c: ldur            x0, [fp, #-8]
    // 0xb11480: stur            x2, [fp, #-0x18]
    // 0xb11484: LoadField: d0 = r0->field_27
    //     0xb11484: ldur            d0, [x0, #0x27]
    // 0xb11488: d1 = 0.000000
    //     0xb11488: eor             v1.16b, v1.16b, v1.16b
    // 0xb1148c: fcmp            d0, d1
    // 0xb11490: b.lt            #0xb114a0
    // 0xb11494: r3 = "LU"
    //     0xb11494: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2aa30] "LU"
    //     0xb11498: ldr             x3, [x3, #0xa30]
    // 0xb1149c: b               #0xb114a8
    // 0xb114a0: r3 = "LS"
    //     0xb114a0: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2aa38] "LS"
    //     0xb114a4: ldr             x3, [x3, #0xa38]
    // 0xb114a8: stur            x3, [fp, #-0x10]
    // 0xb114ac: LoadField: d0 = r0->field_1f
    //     0xb114ac: ldur            d0, [x0, #0x1f]
    // 0xb114b0: mov             x1, x0
    // 0xb114b4: r0 = toDMS()
    //     0xb114b4: bl              #0xb11558  ; [package:nuonline/app/data/models/location.dart] Location::toDMS
    // 0xb114b8: mov             x3, x0
    // 0xb114bc: ldur            x0, [fp, #-8]
    // 0xb114c0: stur            x3, [fp, #-0x20]
    // 0xb114c4: LoadField: d0 = r0->field_1f
    //     0xb114c4: ldur            d0, [x0, #0x1f]
    // 0xb114c8: d1 = 0.000000
    //     0xb114c8: eor             v1.16b, v1.16b, v1.16b
    // 0xb114cc: fcmp            d0, d1
    // 0xb114d0: b.lt            #0xb114e0
    // 0xb114d4: r5 = "BT"
    //     0xb114d4: add             x5, PP, #0x2a, lsl #12  ; [pp+0x2aa40] "BT"
    //     0xb114d8: ldr             x5, [x5, #0xa40]
    // 0xb114dc: b               #0xb114e8
    // 0xb114e0: r5 = "BB"
    //     0xb114e0: add             x5, PP, #0xf, lsl #12  ; [pp+0xf3c8] "BB"
    //     0xb114e4: ldr             x5, [x5, #0x3c8]
    // 0xb114e8: ldur            x0, [fp, #-0x18]
    // 0xb114ec: ldur            x4, [fp, #-0x10]
    // 0xb114f0: stur            x5, [fp, #-8]
    // 0xb114f4: r1 = Null
    //     0xb114f4: mov             x1, NULL
    // 0xb114f8: r2 = 14
    //     0xb114f8: movz            x2, #0xe
    // 0xb114fc: r0 = AllocateArray()
    //     0xb114fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb11500: mov             x1, x0
    // 0xb11504: ldur            x0, [fp, #-0x18]
    // 0xb11508: StoreField: r1->field_f = r0
    //     0xb11508: stur            w0, [x1, #0xf]
    // 0xb1150c: r16 = " "
    //     0xb1150c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb11510: StoreField: r1->field_13 = r16
    //     0xb11510: stur            w16, [x1, #0x13]
    // 0xb11514: ldur            x0, [fp, #-0x10]
    // 0xb11518: ArrayStore: r1[0] = r0  ; List_4
    //     0xb11518: stur            w0, [x1, #0x17]
    // 0xb1151c: r16 = "  "
    //     0xb1151c: ldr             x16, [PP, #0x830]  ; [pp+0x830] "  "
    // 0xb11520: StoreField: r1->field_1b = r16
    //     0xb11520: stur            w16, [x1, #0x1b]
    // 0xb11524: ldur            x0, [fp, #-0x20]
    // 0xb11528: StoreField: r1->field_1f = r0
    //     0xb11528: stur            w0, [x1, #0x1f]
    // 0xb1152c: r16 = " "
    //     0xb1152c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb11530: StoreField: r1->field_23 = r16
    //     0xb11530: stur            w16, [x1, #0x23]
    // 0xb11534: ldur            x0, [fp, #-8]
    // 0xb11538: StoreField: r1->field_27 = r0
    //     0xb11538: stur            w0, [x1, #0x27]
    // 0xb1153c: str             x1, [SP]
    // 0xb11540: r0 = _interpolate()
    //     0xb11540: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb11544: LeaveFrame
    //     0xb11544: mov             SP, fp
    //     0xb11548: ldp             fp, lr, [SP], #0x10
    // 0xb1154c: ret
    //     0xb1154c: ret             
    // 0xb11550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11550: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11554: b               #0xb1146c
  }
  _ toDMS(/* No info */) {
    // ** addr: 0xb11558, size: 0x1ac
    // 0xb11558: EnterFrame
    //     0xb11558: stp             fp, lr, [SP, #-0x10]!
    //     0xb1155c: mov             fp, SP
    // 0xb11560: AllocStack(0x20)
    //     0xb11560: sub             SP, SP, #0x20
    // 0xb11564: d1 = 0.000000
    //     0xb11564: eor             v1.16b, v1.16b, v1.16b
    // 0xb11568: CheckStackOverflow
    //     0xb11568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1156c: cmp             SP, x16
    //     0xb11570: b.ls            #0xb11684
    // 0xb11574: fcmp            d0, d1
    // 0xb11578: b.ne            #0xb11584
    // 0xb1157c: d1 = 0.000000
    //     0xb1157c: eor             v1.16b, v1.16b, v1.16b
    // 0xb11580: b               #0xb11598
    // 0xb11584: fcmp            d1, d0
    // 0xb11588: b.le            #0xb11594
    // 0xb1158c: fneg            d1, d0
    // 0xb11590: mov             v0.16b, v1.16b
    // 0xb11594: mov             v1.16b, v0.16b
    // 0xb11598: d0 = 60.000000
    //     0xb11598: ldr             d0, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0xb1159c: fcmp            d1, d1
    // 0xb115a0: b.vs            #0xb1168c
    // 0xb115a4: fcvtms          x0, d1
    // 0xb115a8: asr             x16, x0, #0x1e
    // 0xb115ac: cmp             x16, x0, asr #63
    // 0xb115b0: b.ne            #0xb1168c
    // 0xb115b4: lsl             x0, x0, #1
    // 0xb115b8: stur            x0, [fp, #-0x18]
    // 0xb115bc: r1 = LoadInt32Instr(r0)
    //     0xb115bc: sbfx            x1, x0, #1, #0x1f
    //     0xb115c0: tbz             w0, #0, #0xb115c8
    //     0xb115c4: ldur            x1, [x0, #7]
    // 0xb115c8: scvtf           d2, x1
    // 0xb115cc: fsub            d3, d1, d2
    // 0xb115d0: fmul            d1, d3, d0
    // 0xb115d4: fcmp            d1, d1
    // 0xb115d8: b.vs            #0xb116ac
    // 0xb115dc: fcvtms          x3, d1
    // 0xb115e0: asr             x16, x3, #0x1e
    // 0xb115e4: cmp             x16, x3, asr #63
    // 0xb115e8: b.ne            #0xb116ac
    // 0xb115ec: lsl             x3, x3, #1
    // 0xb115f0: stur            x3, [fp, #-0x10]
    // 0xb115f4: r1 = LoadInt32Instr(r3)
    //     0xb115f4: sbfx            x1, x3, #1, #0x1f
    //     0xb115f8: tbz             w3, #0, #0xb11600
    //     0xb115fc: ldur            x1, [x3, #7]
    // 0xb11600: scvtf           d2, x1
    // 0xb11604: fsub            d3, d1, d2
    // 0xb11608: fmul            d1, d3, d0
    // 0xb1160c: fcmp            d1, d1
    // 0xb11610: b.vs            #0xb116d8
    // 0xb11614: fcvtms          x4, d1
    // 0xb11618: asr             x16, x4, #0x1e
    // 0xb1161c: cmp             x16, x4, asr #63
    // 0xb11620: b.ne            #0xb116d8
    // 0xb11624: lsl             x4, x4, #1
    // 0xb11628: stur            x4, [fp, #-8]
    // 0xb1162c: r1 = Null
    //     0xb1162c: mov             x1, NULL
    // 0xb11630: r2 = 12
    //     0xb11630: movz            x2, #0xc
    // 0xb11634: r0 = AllocateArray()
    //     0xb11634: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb11638: mov             x1, x0
    // 0xb1163c: ldur            x0, [fp, #-0x18]
    // 0xb11640: StoreField: r1->field_f = r0
    //     0xb11640: stur            w0, [x1, #0xf]
    // 0xb11644: r16 = "°"
    //     0xb11644: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2aa48] "°"
    //     0xb11648: ldr             x16, [x16, #0xa48]
    // 0xb1164c: StoreField: r1->field_13 = r16
    //     0xb1164c: stur            w16, [x1, #0x13]
    // 0xb11650: ldur            x0, [fp, #-0x10]
    // 0xb11654: ArrayStore: r1[0] = r0  ; List_4
    //     0xb11654: stur            w0, [x1, #0x17]
    // 0xb11658: r16 = "\'"
    //     0xb11658: ldr             x16, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xb1165c: StoreField: r1->field_1b = r16
    //     0xb1165c: stur            w16, [x1, #0x1b]
    // 0xb11660: ldur            x0, [fp, #-8]
    // 0xb11664: StoreField: r1->field_1f = r0
    //     0xb11664: stur            w0, [x1, #0x1f]
    // 0xb11668: r16 = "\""
    //     0xb11668: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0xb1166c: StoreField: r1->field_23 = r16
    //     0xb1166c: stur            w16, [x1, #0x23]
    // 0xb11670: str             x1, [SP]
    // 0xb11674: r0 = _interpolate()
    //     0xb11674: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb11678: LeaveFrame
    //     0xb11678: mov             SP, fp
    //     0xb1167c: ldp             fp, lr, [SP], #0x10
    // 0xb11680: ret
    //     0xb11680: ret             
    // 0xb11684: r0 = StackOverflowSharedWithFPURegs()
    //     0xb11684: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb11688: b               #0xb11574
    // 0xb1168c: stp             q0, q1, [SP, #-0x20]!
    // 0xb11690: d0 = 0.000000
    //     0xb11690: fmov            d0, d1
    // 0xb11694: r0 = 68
    //     0xb11694: movz            x0, #0x44
    // 0xb11698: r30 = DoubleToIntegerStub
    //     0xb11698: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb1169c: LoadField: r30 = r30->field_7
    //     0xb1169c: ldur            lr, [lr, #7]
    // 0xb116a0: blr             lr
    // 0xb116a4: ldp             q0, q1, [SP], #0x20
    // 0xb116a8: b               #0xb115b8
    // 0xb116ac: stp             q0, q1, [SP, #-0x20]!
    // 0xb116b0: SaveReg r0
    //     0xb116b0: str             x0, [SP, #-8]!
    // 0xb116b4: d0 = 0.000000
    //     0xb116b4: fmov            d0, d1
    // 0xb116b8: r0 = 68
    //     0xb116b8: movz            x0, #0x44
    // 0xb116bc: r30 = DoubleToIntegerStub
    //     0xb116bc: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb116c0: LoadField: r30 = r30->field_7
    //     0xb116c0: ldur            lr, [lr, #7]
    // 0xb116c4: blr             lr
    // 0xb116c8: mov             x3, x0
    // 0xb116cc: RestoreReg r0
    //     0xb116cc: ldr             x0, [SP], #8
    // 0xb116d0: ldp             q0, q1, [SP], #0x20
    // 0xb116d4: b               #0xb115f0
    // 0xb116d8: SaveReg d1
    //     0xb116d8: str             q1, [SP, #-0x10]!
    // 0xb116dc: stp             x0, x3, [SP, #-0x10]!
    // 0xb116e0: d0 = 0.000000
    //     0xb116e0: fmov            d0, d1
    // 0xb116e4: r0 = 68
    //     0xb116e4: movz            x0, #0x44
    // 0xb116e8: r30 = DoubleToIntegerStub
    //     0xb116e8: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb116ec: LoadField: r30 = r30->field_7
    //     0xb116ec: ldur            lr, [lr, #7]
    // 0xb116f0: blr             lr
    // 0xb116f4: mov             x4, x0
    // 0xb116f8: ldp             x0, x3, [SP], #0x10
    // 0xb116fc: RestoreReg d1
    //     0xb116fc: ldr             q1, [SP], #0x10
    // 0xb11700: b               #0xb11628
  }
  get _ fullAddress(/* No info */) {
    // ** addr: 0xb13ce0, size: 0x138
    // 0xb13ce0: EnterFrame
    //     0xb13ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xb13ce4: mov             fp, SP
    // 0xb13ce8: AllocStack(0x48)
    //     0xb13ce8: sub             SP, SP, #0x48
    // 0xb13cec: CheckStackOverflow
    //     0xb13cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb13cf0: cmp             SP, x16
    //     0xb13cf4: b.ls            #0xb13e10
    // 0xb13cf8: LoadField: r0 = r1->field_43
    //     0xb13cf8: ldur            w0, [x1, #0x43]
    // 0xb13cfc: DecompressPointer r0
    //     0xb13cfc: add             x0, x0, HEAP, lsl #32
    // 0xb13d00: stur            x0, [fp, #-0x28]
    // 0xb13d04: LoadField: r3 = r1->field_3f
    //     0xb13d04: ldur            w3, [x1, #0x3f]
    // 0xb13d08: DecompressPointer r3
    //     0xb13d08: add             x3, x3, HEAP, lsl #32
    // 0xb13d0c: stur            x3, [fp, #-0x20]
    // 0xb13d10: LoadField: r4 = r1->field_1b
    //     0xb13d10: ldur            w4, [x1, #0x1b]
    // 0xb13d14: DecompressPointer r4
    //     0xb13d14: add             x4, x4, HEAP, lsl #32
    // 0xb13d18: stur            x4, [fp, #-0x18]
    // 0xb13d1c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb13d1c: ldur            w5, [x1, #0x17]
    // 0xb13d20: DecompressPointer r5
    //     0xb13d20: add             x5, x5, HEAP, lsl #32
    // 0xb13d24: stur            x5, [fp, #-0x10]
    // 0xb13d28: LoadField: r2 = r1->field_3b
    //     0xb13d28: ldur            w2, [x1, #0x3b]
    // 0xb13d2c: DecompressPointer r2
    //     0xb13d2c: add             x2, x2, HEAP, lsl #32
    // 0xb13d30: cmp             w2, NULL
    // 0xb13d34: b.ne            #0xb13d48
    // 0xb13d38: LoadField: r2 = r1->field_13
    //     0xb13d38: ldur            w2, [x1, #0x13]
    // 0xb13d3c: DecompressPointer r2
    //     0xb13d3c: add             x2, x2, HEAP, lsl #32
    // 0xb13d40: mov             x7, x2
    // 0xb13d44: b               #0xb13d4c
    // 0xb13d48: mov             x7, x2
    // 0xb13d4c: r6 = 10
    //     0xb13d4c: movz            x6, #0xa
    // 0xb13d50: mov             x2, x6
    // 0xb13d54: stur            x7, [fp, #-8]
    // 0xb13d58: r1 = Null
    //     0xb13d58: mov             x1, NULL
    // 0xb13d5c: r0 = AllocateArray()
    //     0xb13d5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb13d60: mov             x2, x0
    // 0xb13d64: ldur            x0, [fp, #-0x28]
    // 0xb13d68: stur            x2, [fp, #-0x30]
    // 0xb13d6c: StoreField: r2->field_f = r0
    //     0xb13d6c: stur            w0, [x2, #0xf]
    // 0xb13d70: ldur            x0, [fp, #-0x20]
    // 0xb13d74: StoreField: r2->field_13 = r0
    //     0xb13d74: stur            w0, [x2, #0x13]
    // 0xb13d78: ldur            x0, [fp, #-0x18]
    // 0xb13d7c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb13d7c: stur            w0, [x2, #0x17]
    // 0xb13d80: ldur            x0, [fp, #-0x10]
    // 0xb13d84: StoreField: r2->field_1b = r0
    //     0xb13d84: stur            w0, [x2, #0x1b]
    // 0xb13d88: ldur            x0, [fp, #-8]
    // 0xb13d8c: StoreField: r2->field_1f = r0
    //     0xb13d8c: stur            w0, [x2, #0x1f]
    // 0xb13d90: r1 = <String?>
    //     0xb13d90: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb13d94: r0 = AllocateGrowableArray()
    //     0xb13d94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb13d98: mov             x3, x0
    // 0xb13d9c: ldur            x0, [fp, #-0x30]
    // 0xb13da0: stur            x3, [fp, #-8]
    // 0xb13da4: StoreField: r3->field_f = r0
    //     0xb13da4: stur            w0, [x3, #0xf]
    // 0xb13da8: r0 = 10
    //     0xb13da8: movz            x0, #0xa
    // 0xb13dac: StoreField: r3->field_b = r0
    //     0xb13dac: stur            w0, [x3, #0xb]
    // 0xb13db0: r1 = Function '<anonymous closure>':.
    //     0xb13db0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e328] AnonymousClosure: (0x81e334), in [package:nuonline/app/data/models/location.dart] Location::address (0x81e19c)
    //     0xb13db4: ldr             x1, [x1, #0x328]
    // 0xb13db8: r2 = Null
    //     0xb13db8: mov             x2, NULL
    // 0xb13dbc: r0 = AllocateClosure()
    //     0xb13dbc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb13dc0: r16 = <String?>
    //     0xb13dc0: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb13dc4: ldur            lr, [fp, #-8]
    // 0xb13dc8: stp             lr, x16, [SP, #8]
    // 0xb13dcc: str             x0, [SP]
    // 0xb13dd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb13dd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb13dd4: r0 = ListExtension.filter()
    //     0xb13dd4: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xb13dd8: r16 = ", "
    //     0xb13dd8: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb13ddc: str             x16, [SP]
    // 0xb13de0: mov             x1, x0
    // 0xb13de4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb13de4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb13de8: r0 = join()
    //     0xb13de8: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb13dec: mov             x1, x0
    // 0xb13df0: r2 = "Kab."
    //     0xb13df0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e330] "Kab."
    //     0xb13df4: ldr             x2, [x2, #0x330]
    // 0xb13df8: r3 = "Kabupaten"
    //     0xb13df8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e338] "Kabupaten"
    //     0xb13dfc: ldr             x3, [x3, #0x338]
    // 0xb13e00: r0 = replaceAll()
    //     0xb13e00: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb13e04: LeaveFrame
    //     0xb13e04: mov             SP, fp
    //     0xb13e08: ldp             fp, lr, [SP], #0x10
    // 0xb13e0c: ret
    //     0xb13e0c: ret             
    // 0xb13e10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb13e10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb13e14: b               #0xb13cf8
  }
}

// class id: 1656, size: 0x14, field offset: 0xc
class LocationAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa62a60, size: 0x6f0
    // 0xa62a60: EnterFrame
    //     0xa62a60: stp             fp, lr, [SP, #-0x10]!
    //     0xa62a64: mov             fp, SP
    // 0xa62a68: AllocStack(0x90)
    //     0xa62a68: sub             SP, SP, #0x90
    // 0xa62a6c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa62a6c: stur            x2, [fp, #-0x20]
    // 0xa62a70: CheckStackOverflow
    //     0xa62a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62a74: cmp             SP, x16
    //     0xa62a78: b.ls            #0xa63138
    // 0xa62a7c: LoadField: r3 = r2->field_23
    //     0xa62a7c: ldur            x3, [x2, #0x23]
    // 0xa62a80: add             x0, x3, #1
    // 0xa62a84: LoadField: r1 = r2->field_1b
    //     0xa62a84: ldur            x1, [x2, #0x1b]
    // 0xa62a88: cmp             x0, x1
    // 0xa62a8c: b.gt            #0xa630dc
    // 0xa62a90: LoadField: r4 = r2->field_7
    //     0xa62a90: ldur            w4, [x2, #7]
    // 0xa62a94: DecompressPointer r4
    //     0xa62a94: add             x4, x4, HEAP, lsl #32
    // 0xa62a98: stur            x4, [fp, #-0x18]
    // 0xa62a9c: StoreField: r2->field_23 = r0
    //     0xa62a9c: stur            x0, [x2, #0x23]
    // 0xa62aa0: LoadField: r0 = r4->field_13
    //     0xa62aa0: ldur            w0, [x4, #0x13]
    // 0xa62aa4: r5 = LoadInt32Instr(r0)
    //     0xa62aa4: sbfx            x5, x0, #1, #0x1f
    // 0xa62aa8: mov             x0, x5
    // 0xa62aac: mov             x1, x3
    // 0xa62ab0: stur            x5, [fp, #-0x10]
    // 0xa62ab4: cmp             x1, x0
    // 0xa62ab8: b.hs            #0xa63140
    // 0xa62abc: LoadField: r0 = r4->field_7
    //     0xa62abc: ldur            x0, [x4, #7]
    // 0xa62ac0: ldrb            w1, [x0, x3]
    // 0xa62ac4: stur            x1, [fp, #-8]
    // 0xa62ac8: r16 = <int, dynamic>
    //     0xa62ac8: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa62acc: ldr             x16, [x16, #0xac0]
    // 0xa62ad0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa62ad4: stp             lr, x16, [SP]
    // 0xa62ad8: r0 = Map._fromLiteral()
    //     0xa62ad8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa62adc: mov             x2, x0
    // 0xa62ae0: stur            x2, [fp, #-0x38]
    // 0xa62ae4: r6 = 0
    //     0xa62ae4: movz            x6, #0
    // 0xa62ae8: ldur            x3, [fp, #-0x20]
    // 0xa62aec: ldur            x4, [fp, #-0x18]
    // 0xa62af0: ldur            x5, [fp, #-8]
    // 0xa62af4: stur            x6, [fp, #-0x30]
    // 0xa62af8: CheckStackOverflow
    //     0xa62af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62afc: cmp             SP, x16
    //     0xa62b00: b.ls            #0xa63144
    // 0xa62b04: cmp             x6, x5
    // 0xa62b08: b.ge            #0xa62b94
    // 0xa62b0c: LoadField: r7 = r3->field_23
    //     0xa62b0c: ldur            x7, [x3, #0x23]
    // 0xa62b10: add             x0, x7, #1
    // 0xa62b14: LoadField: r1 = r3->field_1b
    //     0xa62b14: ldur            x1, [x3, #0x1b]
    // 0xa62b18: cmp             x0, x1
    // 0xa62b1c: b.gt            #0xa63104
    // 0xa62b20: StoreField: r3->field_23 = r0
    //     0xa62b20: stur            x0, [x3, #0x23]
    // 0xa62b24: ldur            x0, [fp, #-0x10]
    // 0xa62b28: mov             x1, x7
    // 0xa62b2c: cmp             x1, x0
    // 0xa62b30: b.hs            #0xa6314c
    // 0xa62b34: LoadField: r0 = r4->field_7
    //     0xa62b34: ldur            x0, [x4, #7]
    // 0xa62b38: ldrb            w8, [x0, x7]
    // 0xa62b3c: mov             x1, x3
    // 0xa62b40: stur            x8, [fp, #-0x28]
    // 0xa62b44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa62b44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa62b48: r0 = read()
    //     0xa62b48: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa62b4c: mov             x1, x0
    // 0xa62b50: ldur            x0, [fp, #-0x28]
    // 0xa62b54: lsl             x2, x0, #1
    // 0xa62b58: r16 = LoadInt32Instr(r2)
    //     0xa62b58: sbfx            x16, x2, #1, #0x1f
    // 0xa62b5c: r17 = 11601
    //     0xa62b5c: movz            x17, #0x2d51
    // 0xa62b60: mul             x0, x16, x17
    // 0xa62b64: umulh           x16, x16, x17
    // 0xa62b68: eor             x0, x0, x16
    // 0xa62b6c: r0 = 0
    //     0xa62b6c: eor             x0, x0, x0, lsr #32
    // 0xa62b70: ubfiz           x0, x0, #1, #0x1e
    // 0xa62b74: r5 = LoadInt32Instr(r0)
    //     0xa62b74: sbfx            x5, x0, #1, #0x1f
    // 0xa62b78: mov             x3, x1
    // 0xa62b7c: ldur            x1, [fp, #-0x38]
    // 0xa62b80: r0 = _set()
    //     0xa62b80: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa62b84: ldur            x0, [fp, #-0x30]
    // 0xa62b88: add             x6, x0, #1
    // 0xa62b8c: ldur            x2, [fp, #-0x38]
    // 0xa62b90: b               #0xa62ae8
    // 0xa62b94: mov             x0, x2
    // 0xa62b98: mov             x1, x0
    // 0xa62b9c: r2 = 0
    //     0xa62b9c: movz            x2, #0
    // 0xa62ba0: r0 = _getValueOrData()
    //     0xa62ba0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62ba4: ldur            x3, [fp, #-0x38]
    // 0xa62ba8: LoadField: r1 = r3->field_f
    //     0xa62ba8: ldur            w1, [x3, #0xf]
    // 0xa62bac: DecompressPointer r1
    //     0xa62bac: add             x1, x1, HEAP, lsl #32
    // 0xa62bb0: cmp             w1, w0
    // 0xa62bb4: b.ne            #0xa62bc0
    // 0xa62bb8: r4 = Null
    //     0xa62bb8: mov             x4, NULL
    // 0xa62bbc: b               #0xa62bc4
    // 0xa62bc0: mov             x4, x0
    // 0xa62bc4: mov             x0, x4
    // 0xa62bc8: stur            x4, [fp, #-0x18]
    // 0xa62bcc: r2 = Null
    //     0xa62bcc: mov             x2, NULL
    // 0xa62bd0: r1 = Null
    //     0xa62bd0: mov             x1, NULL
    // 0xa62bd4: r4 = 60
    //     0xa62bd4: movz            x4, #0x3c
    // 0xa62bd8: branchIfSmi(r0, 0xa62be4)
    //     0xa62bd8: tbz             w0, #0, #0xa62be4
    // 0xa62bdc: r4 = LoadClassIdInstr(r0)
    //     0xa62bdc: ldur            x4, [x0, #-1]
    //     0xa62be0: ubfx            x4, x4, #0xc, #0x14
    // 0xa62be4: sub             x4, x4, #0x5e
    // 0xa62be8: cmp             x4, #1
    // 0xa62bec: b.ls            #0xa62c00
    // 0xa62bf0: r8 = String
    //     0xa62bf0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62bf4: r3 = Null
    //     0xa62bf4: add             x3, PP, #0x21, lsl #12  ; [pp+0x212b0] Null
    //     0xa62bf8: ldr             x3, [x3, #0x2b0]
    // 0xa62bfc: r0 = String()
    //     0xa62bfc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62c00: ldur            x1, [fp, #-0x38]
    // 0xa62c04: r2 = 2
    //     0xa62c04: movz            x2, #0x2
    // 0xa62c08: r0 = _getValueOrData()
    //     0xa62c08: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62c0c: ldur            x3, [fp, #-0x38]
    // 0xa62c10: LoadField: r1 = r3->field_f
    //     0xa62c10: ldur            w1, [x3, #0xf]
    // 0xa62c14: DecompressPointer r1
    //     0xa62c14: add             x1, x1, HEAP, lsl #32
    // 0xa62c18: cmp             w1, w0
    // 0xa62c1c: b.ne            #0xa62c28
    // 0xa62c20: r4 = Null
    //     0xa62c20: mov             x4, NULL
    // 0xa62c24: b               #0xa62c2c
    // 0xa62c28: mov             x4, x0
    // 0xa62c2c: mov             x0, x4
    // 0xa62c30: stur            x4, [fp, #-0x20]
    // 0xa62c34: r2 = Null
    //     0xa62c34: mov             x2, NULL
    // 0xa62c38: r1 = Null
    //     0xa62c38: mov             x1, NULL
    // 0xa62c3c: r4 = 60
    //     0xa62c3c: movz            x4, #0x3c
    // 0xa62c40: branchIfSmi(r0, 0xa62c4c)
    //     0xa62c40: tbz             w0, #0, #0xa62c4c
    // 0xa62c44: r4 = LoadClassIdInstr(r0)
    //     0xa62c44: ldur            x4, [x0, #-1]
    //     0xa62c48: ubfx            x4, x4, #0xc, #0x14
    // 0xa62c4c: sub             x4, x4, #0x5e
    // 0xa62c50: cmp             x4, #1
    // 0xa62c54: b.ls            #0xa62c68
    // 0xa62c58: r8 = String
    //     0xa62c58: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62c5c: r3 = Null
    //     0xa62c5c: add             x3, PP, #0x21, lsl #12  ; [pp+0x212c0] Null
    //     0xa62c60: ldr             x3, [x3, #0x2c0]
    // 0xa62c64: r0 = String()
    //     0xa62c64: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62c68: ldur            x1, [fp, #-0x38]
    // 0xa62c6c: r2 = 4
    //     0xa62c6c: movz            x2, #0x4
    // 0xa62c70: r0 = _getValueOrData()
    //     0xa62c70: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62c74: ldur            x3, [fp, #-0x38]
    // 0xa62c78: LoadField: r1 = r3->field_f
    //     0xa62c78: ldur            w1, [x3, #0xf]
    // 0xa62c7c: DecompressPointer r1
    //     0xa62c7c: add             x1, x1, HEAP, lsl #32
    // 0xa62c80: cmp             w1, w0
    // 0xa62c84: b.ne            #0xa62c90
    // 0xa62c88: r4 = Null
    //     0xa62c88: mov             x4, NULL
    // 0xa62c8c: b               #0xa62c94
    // 0xa62c90: mov             x4, x0
    // 0xa62c94: mov             x0, x4
    // 0xa62c98: stur            x4, [fp, #-0x40]
    // 0xa62c9c: r2 = Null
    //     0xa62c9c: mov             x2, NULL
    // 0xa62ca0: r1 = Null
    //     0xa62ca0: mov             x1, NULL
    // 0xa62ca4: r4 = 60
    //     0xa62ca4: movz            x4, #0x3c
    // 0xa62ca8: branchIfSmi(r0, 0xa62cb4)
    //     0xa62ca8: tbz             w0, #0, #0xa62cb4
    // 0xa62cac: r4 = LoadClassIdInstr(r0)
    //     0xa62cac: ldur            x4, [x0, #-1]
    //     0xa62cb0: ubfx            x4, x4, #0xc, #0x14
    // 0xa62cb4: sub             x4, x4, #0x5e
    // 0xa62cb8: cmp             x4, #1
    // 0xa62cbc: b.ls            #0xa62cd0
    // 0xa62cc0: r8 = String
    //     0xa62cc0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62cc4: r3 = Null
    //     0xa62cc4: add             x3, PP, #0x21, lsl #12  ; [pp+0x212d0] Null
    //     0xa62cc8: ldr             x3, [x3, #0x2d0]
    // 0xa62ccc: r0 = String()
    //     0xa62ccc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62cd0: ldur            x1, [fp, #-0x38]
    // 0xa62cd4: r2 = 6
    //     0xa62cd4: movz            x2, #0x6
    // 0xa62cd8: r0 = _getValueOrData()
    //     0xa62cd8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62cdc: ldur            x3, [fp, #-0x38]
    // 0xa62ce0: LoadField: r1 = r3->field_f
    //     0xa62ce0: ldur            w1, [x3, #0xf]
    // 0xa62ce4: DecompressPointer r1
    //     0xa62ce4: add             x1, x1, HEAP, lsl #32
    // 0xa62ce8: cmp             w1, w0
    // 0xa62cec: b.ne            #0xa62cf8
    // 0xa62cf0: r4 = Null
    //     0xa62cf0: mov             x4, NULL
    // 0xa62cf4: b               #0xa62cfc
    // 0xa62cf8: mov             x4, x0
    // 0xa62cfc: mov             x0, x4
    // 0xa62d00: stur            x4, [fp, #-0x48]
    // 0xa62d04: r2 = Null
    //     0xa62d04: mov             x2, NULL
    // 0xa62d08: r1 = Null
    //     0xa62d08: mov             x1, NULL
    // 0xa62d0c: r4 = 60
    //     0xa62d0c: movz            x4, #0x3c
    // 0xa62d10: branchIfSmi(r0, 0xa62d1c)
    //     0xa62d10: tbz             w0, #0, #0xa62d1c
    // 0xa62d14: r4 = LoadClassIdInstr(r0)
    //     0xa62d14: ldur            x4, [x0, #-1]
    //     0xa62d18: ubfx            x4, x4, #0xc, #0x14
    // 0xa62d1c: cmp             x4, #0x3e
    // 0xa62d20: b.eq            #0xa62d34
    // 0xa62d24: r8 = double
    //     0xa62d24: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa62d28: r3 = Null
    //     0xa62d28: add             x3, PP, #0x21, lsl #12  ; [pp+0x212e0] Null
    //     0xa62d2c: ldr             x3, [x3, #0x2e0]
    // 0xa62d30: r0 = double()
    //     0xa62d30: bl              #0xed4460  ; IsType_double_Stub
    // 0xa62d34: ldur            x1, [fp, #-0x38]
    // 0xa62d38: r2 = 8
    //     0xa62d38: movz            x2, #0x8
    // 0xa62d3c: r0 = _getValueOrData()
    //     0xa62d3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62d40: ldur            x3, [fp, #-0x38]
    // 0xa62d44: LoadField: r1 = r3->field_f
    //     0xa62d44: ldur            w1, [x3, #0xf]
    // 0xa62d48: DecompressPointer r1
    //     0xa62d48: add             x1, x1, HEAP, lsl #32
    // 0xa62d4c: cmp             w1, w0
    // 0xa62d50: b.ne            #0xa62d5c
    // 0xa62d54: r4 = Null
    //     0xa62d54: mov             x4, NULL
    // 0xa62d58: b               #0xa62d60
    // 0xa62d5c: mov             x4, x0
    // 0xa62d60: mov             x0, x4
    // 0xa62d64: stur            x4, [fp, #-0x50]
    // 0xa62d68: r2 = Null
    //     0xa62d68: mov             x2, NULL
    // 0xa62d6c: r1 = Null
    //     0xa62d6c: mov             x1, NULL
    // 0xa62d70: r4 = 60
    //     0xa62d70: movz            x4, #0x3c
    // 0xa62d74: branchIfSmi(r0, 0xa62d80)
    //     0xa62d74: tbz             w0, #0, #0xa62d80
    // 0xa62d78: r4 = LoadClassIdInstr(r0)
    //     0xa62d78: ldur            x4, [x0, #-1]
    //     0xa62d7c: ubfx            x4, x4, #0xc, #0x14
    // 0xa62d80: cmp             x4, #0x3e
    // 0xa62d84: b.eq            #0xa62d98
    // 0xa62d88: r8 = double
    //     0xa62d88: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa62d8c: r3 = Null
    //     0xa62d8c: add             x3, PP, #0x21, lsl #12  ; [pp+0x212f0] Null
    //     0xa62d90: ldr             x3, [x3, #0x2f0]
    // 0xa62d94: r0 = double()
    //     0xa62d94: bl              #0xed4460  ; IsType_double_Stub
    // 0xa62d98: ldur            x1, [fp, #-0x38]
    // 0xa62d9c: r2 = 12
    //     0xa62d9c: movz            x2, #0xc
    // 0xa62da0: r0 = _getValueOrData()
    //     0xa62da0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62da4: ldur            x3, [fp, #-0x38]
    // 0xa62da8: LoadField: r1 = r3->field_f
    //     0xa62da8: ldur            w1, [x3, #0xf]
    // 0xa62dac: DecompressPointer r1
    //     0xa62dac: add             x1, x1, HEAP, lsl #32
    // 0xa62db0: cmp             w1, w0
    // 0xa62db4: b.ne            #0xa62dc0
    // 0xa62db8: r4 = Null
    //     0xa62db8: mov             x4, NULL
    // 0xa62dbc: b               #0xa62dc4
    // 0xa62dc0: mov             x4, x0
    // 0xa62dc4: mov             x0, x4
    // 0xa62dc8: stur            x4, [fp, #-0x58]
    // 0xa62dcc: r2 = Null
    //     0xa62dcc: mov             x2, NULL
    // 0xa62dd0: r1 = Null
    //     0xa62dd0: mov             x1, NULL
    // 0xa62dd4: r4 = 60
    //     0xa62dd4: movz            x4, #0x3c
    // 0xa62dd8: branchIfSmi(r0, 0xa62de4)
    //     0xa62dd8: tbz             w0, #0, #0xa62de4
    // 0xa62ddc: r4 = LoadClassIdInstr(r0)
    //     0xa62ddc: ldur            x4, [x0, #-1]
    //     0xa62de0: ubfx            x4, x4, #0xc, #0x14
    // 0xa62de4: sub             x4, x4, #0x5e
    // 0xa62de8: cmp             x4, #1
    // 0xa62dec: b.ls            #0xa62e00
    // 0xa62df0: r8 = String
    //     0xa62df0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa62df4: r3 = Null
    //     0xa62df4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21300] Null
    //     0xa62df8: ldr             x3, [x3, #0x300]
    // 0xa62dfc: r0 = String()
    //     0xa62dfc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa62e00: ldur            x1, [fp, #-0x38]
    // 0xa62e04: r2 = 18
    //     0xa62e04: movz            x2, #0x12
    // 0xa62e08: r0 = _getValueOrData()
    //     0xa62e08: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62e0c: ldur            x3, [fp, #-0x38]
    // 0xa62e10: LoadField: r1 = r3->field_f
    //     0xa62e10: ldur            w1, [x3, #0xf]
    // 0xa62e14: DecompressPointer r1
    //     0xa62e14: add             x1, x1, HEAP, lsl #32
    // 0xa62e18: cmp             w1, w0
    // 0xa62e1c: b.ne            #0xa62e28
    // 0xa62e20: r4 = Null
    //     0xa62e20: mov             x4, NULL
    // 0xa62e24: b               #0xa62e2c
    // 0xa62e28: mov             x4, x0
    // 0xa62e2c: mov             x0, x4
    // 0xa62e30: stur            x4, [fp, #-0x60]
    // 0xa62e34: r2 = Null
    //     0xa62e34: mov             x2, NULL
    // 0xa62e38: r1 = Null
    //     0xa62e38: mov             x1, NULL
    // 0xa62e3c: r4 = 60
    //     0xa62e3c: movz            x4, #0x3c
    // 0xa62e40: branchIfSmi(r0, 0xa62e4c)
    //     0xa62e40: tbz             w0, #0, #0xa62e4c
    // 0xa62e44: r4 = LoadClassIdInstr(r0)
    //     0xa62e44: ldur            x4, [x0, #-1]
    //     0xa62e48: ubfx            x4, x4, #0xc, #0x14
    // 0xa62e4c: sub             x4, x4, #0x5e
    // 0xa62e50: cmp             x4, #1
    // 0xa62e54: b.ls            #0xa62e68
    // 0xa62e58: r8 = String?
    //     0xa62e58: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa62e5c: r3 = Null
    //     0xa62e5c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21310] Null
    //     0xa62e60: ldr             x3, [x3, #0x310]
    // 0xa62e64: r0 = String?()
    //     0xa62e64: bl              #0x600324  ; IsType_String?_Stub
    // 0xa62e68: ldur            x1, [fp, #-0x38]
    // 0xa62e6c: r2 = 20
    //     0xa62e6c: movz            x2, #0x14
    // 0xa62e70: r0 = _getValueOrData()
    //     0xa62e70: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62e74: ldur            x3, [fp, #-0x38]
    // 0xa62e78: LoadField: r1 = r3->field_f
    //     0xa62e78: ldur            w1, [x3, #0xf]
    // 0xa62e7c: DecompressPointer r1
    //     0xa62e7c: add             x1, x1, HEAP, lsl #32
    // 0xa62e80: cmp             w1, w0
    // 0xa62e84: b.ne            #0xa62e90
    // 0xa62e88: r4 = Null
    //     0xa62e88: mov             x4, NULL
    // 0xa62e8c: b               #0xa62e94
    // 0xa62e90: mov             x4, x0
    // 0xa62e94: mov             x0, x4
    // 0xa62e98: stur            x4, [fp, #-0x68]
    // 0xa62e9c: r2 = Null
    //     0xa62e9c: mov             x2, NULL
    // 0xa62ea0: r1 = Null
    //     0xa62ea0: mov             x1, NULL
    // 0xa62ea4: r4 = 60
    //     0xa62ea4: movz            x4, #0x3c
    // 0xa62ea8: branchIfSmi(r0, 0xa62eb4)
    //     0xa62ea8: tbz             w0, #0, #0xa62eb4
    // 0xa62eac: r4 = LoadClassIdInstr(r0)
    //     0xa62eac: ldur            x4, [x0, #-1]
    //     0xa62eb0: ubfx            x4, x4, #0xc, #0x14
    // 0xa62eb4: sub             x4, x4, #0x5e
    // 0xa62eb8: cmp             x4, #1
    // 0xa62ebc: b.ls            #0xa62ed0
    // 0xa62ec0: r8 = String?
    //     0xa62ec0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa62ec4: r3 = Null
    //     0xa62ec4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21320] Null
    //     0xa62ec8: ldr             x3, [x3, #0x320]
    // 0xa62ecc: r0 = String?()
    //     0xa62ecc: bl              #0x600324  ; IsType_String?_Stub
    // 0xa62ed0: ldur            x1, [fp, #-0x38]
    // 0xa62ed4: r2 = 16
    //     0xa62ed4: movz            x2, #0x10
    // 0xa62ed8: r0 = _getValueOrData()
    //     0xa62ed8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62edc: ldur            x3, [fp, #-0x38]
    // 0xa62ee0: LoadField: r1 = r3->field_f
    //     0xa62ee0: ldur            w1, [x3, #0xf]
    // 0xa62ee4: DecompressPointer r1
    //     0xa62ee4: add             x1, x1, HEAP, lsl #32
    // 0xa62ee8: cmp             w1, w0
    // 0xa62eec: b.ne            #0xa62ef8
    // 0xa62ef0: r4 = Null
    //     0xa62ef0: mov             x4, NULL
    // 0xa62ef4: b               #0xa62efc
    // 0xa62ef8: mov             x4, x0
    // 0xa62efc: mov             x0, x4
    // 0xa62f00: stur            x4, [fp, #-0x70]
    // 0xa62f04: r2 = Null
    //     0xa62f04: mov             x2, NULL
    // 0xa62f08: r1 = Null
    //     0xa62f08: mov             x1, NULL
    // 0xa62f0c: r4 = 60
    //     0xa62f0c: movz            x4, #0x3c
    // 0xa62f10: branchIfSmi(r0, 0xa62f1c)
    //     0xa62f10: tbz             w0, #0, #0xa62f1c
    // 0xa62f14: r4 = LoadClassIdInstr(r0)
    //     0xa62f14: ldur            x4, [x0, #-1]
    //     0xa62f18: ubfx            x4, x4, #0xc, #0x14
    // 0xa62f1c: sub             x4, x4, #0x5e
    // 0xa62f20: cmp             x4, #1
    // 0xa62f24: b.ls            #0xa62f38
    // 0xa62f28: r8 = String?
    //     0xa62f28: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa62f2c: r3 = Null
    //     0xa62f2c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21330] Null
    //     0xa62f30: ldr             x3, [x3, #0x330]
    // 0xa62f34: r0 = String?()
    //     0xa62f34: bl              #0x600324  ; IsType_String?_Stub
    // 0xa62f38: ldur            x1, [fp, #-0x38]
    // 0xa62f3c: r2 = 10
    //     0xa62f3c: movz            x2, #0xa
    // 0xa62f40: r0 = _getValueOrData()
    //     0xa62f40: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62f44: ldur            x3, [fp, #-0x38]
    // 0xa62f48: LoadField: r1 = r3->field_f
    //     0xa62f48: ldur            w1, [x3, #0xf]
    // 0xa62f4c: DecompressPointer r1
    //     0xa62f4c: add             x1, x1, HEAP, lsl #32
    // 0xa62f50: cmp             w1, w0
    // 0xa62f54: b.ne            #0xa62f60
    // 0xa62f58: r4 = Null
    //     0xa62f58: mov             x4, NULL
    // 0xa62f5c: b               #0xa62f64
    // 0xa62f60: mov             x4, x0
    // 0xa62f64: mov             x0, x4
    // 0xa62f68: stur            x4, [fp, #-0x78]
    // 0xa62f6c: r2 = Null
    //     0xa62f6c: mov             x2, NULL
    // 0xa62f70: r1 = Null
    //     0xa62f70: mov             x1, NULL
    // 0xa62f74: branchIfSmi(r0, 0xa62f9c)
    //     0xa62f74: tbz             w0, #0, #0xa62f9c
    // 0xa62f78: r4 = LoadClassIdInstr(r0)
    //     0xa62f78: ldur            x4, [x0, #-1]
    //     0xa62f7c: ubfx            x4, x4, #0xc, #0x14
    // 0xa62f80: sub             x4, x4, #0x3c
    // 0xa62f84: cmp             x4, #1
    // 0xa62f88: b.ls            #0xa62f9c
    // 0xa62f8c: r8 = int?
    //     0xa62f8c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xa62f90: r3 = Null
    //     0xa62f90: add             x3, PP, #0x21, lsl #12  ; [pp+0x21340] Null
    //     0xa62f94: ldr             x3, [x3, #0x340]
    // 0xa62f98: r0 = int?()
    //     0xa62f98: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xa62f9c: ldur            x1, [fp, #-0x38]
    // 0xa62fa0: r2 = 14
    //     0xa62fa0: movz            x2, #0xe
    // 0xa62fa4: r0 = _getValueOrData()
    //     0xa62fa4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa62fa8: mov             x1, x0
    // 0xa62fac: ldur            x0, [fp, #-0x38]
    // 0xa62fb0: LoadField: r2 = r0->field_f
    //     0xa62fb0: ldur            w2, [x0, #0xf]
    // 0xa62fb4: DecompressPointer r2
    //     0xa62fb4: add             x2, x2, HEAP, lsl #32
    // 0xa62fb8: cmp             w2, w1
    // 0xa62fbc: b.ne            #0xa62fc8
    // 0xa62fc0: r13 = Null
    //     0xa62fc0: mov             x13, NULL
    // 0xa62fc4: b               #0xa62fcc
    // 0xa62fc8: mov             x13, x1
    // 0xa62fcc: ldur            x12, [fp, #-0x18]
    // 0xa62fd0: ldur            x11, [fp, #-0x20]
    // 0xa62fd4: ldur            x10, [fp, #-0x40]
    // 0xa62fd8: ldur            x9, [fp, #-0x48]
    // 0xa62fdc: ldur            x8, [fp, #-0x50]
    // 0xa62fe0: ldur            x7, [fp, #-0x58]
    // 0xa62fe4: ldur            x6, [fp, #-0x60]
    // 0xa62fe8: ldur            x5, [fp, #-0x68]
    // 0xa62fec: ldur            x4, [fp, #-0x70]
    // 0xa62ff0: ldur            x3, [fp, #-0x78]
    // 0xa62ff4: mov             x0, x13
    // 0xa62ff8: stur            x13, [fp, #-0x38]
    // 0xa62ffc: r2 = Null
    //     0xa62ffc: mov             x2, NULL
    // 0xa63000: r1 = Null
    //     0xa63000: mov             x1, NULL
    // 0xa63004: r4 = 60
    //     0xa63004: movz            x4, #0x3c
    // 0xa63008: branchIfSmi(r0, 0xa63014)
    //     0xa63008: tbz             w0, #0, #0xa63014
    // 0xa6300c: r4 = LoadClassIdInstr(r0)
    //     0xa6300c: ldur            x4, [x0, #-1]
    //     0xa63010: ubfx            x4, x4, #0xc, #0x14
    // 0xa63014: cmp             x4, #0x3f
    // 0xa63018: b.eq            #0xa6302c
    // 0xa6301c: r8 = bool
    //     0xa6301c: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa63020: r3 = Null
    //     0xa63020: add             x3, PP, #0x21, lsl #12  ; [pp+0x21350] Null
    //     0xa63024: ldr             x3, [x3, #0x350]
    // 0xa63028: r0 = bool()
    //     0xa63028: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa6302c: r0 = Location()
    //     0xa6302c: bl              #0x8ffb64  ; AllocateLocationStub -> Location (size=0x48)
    // 0xa63030: mov             x1, x0
    // 0xa63034: ldur            x0, [fp, #-0x18]
    // 0xa63038: stur            x1, [fp, #-0x80]
    // 0xa6303c: StoreField: r1->field_13 = r0
    //     0xa6303c: stur            w0, [x1, #0x13]
    // 0xa63040: ldur            x0, [fp, #-0x20]
    // 0xa63044: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63044: stur            w0, [x1, #0x17]
    // 0xa63048: ldur            x0, [fp, #-0x40]
    // 0xa6304c: StoreField: r1->field_1b = r0
    //     0xa6304c: stur            w0, [x1, #0x1b]
    // 0xa63050: ldur            x0, [fp, #-0x48]
    // 0xa63054: LoadField: d0 = r0->field_7
    //     0xa63054: ldur            d0, [x0, #7]
    // 0xa63058: StoreField: r1->field_1f = d0
    //     0xa63058: stur            d0, [x1, #0x1f]
    // 0xa6305c: ldur            x0, [fp, #-0x50]
    // 0xa63060: LoadField: d0 = r0->field_7
    //     0xa63060: ldur            d0, [x0, #7]
    // 0xa63064: StoreField: r1->field_27 = d0
    //     0xa63064: stur            d0, [x1, #0x27]
    // 0xa63068: ldur            x0, [fp, #-0x58]
    // 0xa6306c: StoreField: r1->field_33 = r0
    //     0xa6306c: stur            w0, [x1, #0x33]
    // 0xa63070: ldur            x0, [fp, #-0x60]
    // 0xa63074: StoreField: r1->field_3f = r0
    //     0xa63074: stur            w0, [x1, #0x3f]
    // 0xa63078: ldur            x0, [fp, #-0x68]
    // 0xa6307c: StoreField: r1->field_43 = r0
    //     0xa6307c: stur            w0, [x1, #0x43]
    // 0xa63080: ldur            x0, [fp, #-0x70]
    // 0xa63084: StoreField: r1->field_3b = r0
    //     0xa63084: stur            w0, [x1, #0x3b]
    // 0xa63088: ldur            x0, [fp, #-0x78]
    // 0xa6308c: StoreField: r1->field_2f = r0
    //     0xa6308c: stur            w0, [x1, #0x2f]
    // 0xa63090: ldur            x0, [fp, #-0x38]
    // 0xa63094: StoreField: r1->field_37 = r0
    //     0xa63094: stur            w0, [x1, #0x37]
    // 0xa63098: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa63098: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa6309c: ldr             x16, [x16, #0x9f8]
    // 0xa630a0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa630a4: stp             lr, x16, [SP]
    // 0xa630a8: r0 = Map._fromLiteral()
    //     0xa630a8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa630ac: ldur            x1, [fp, #-0x80]
    // 0xa630b0: StoreField: r1->field_f = r0
    //     0xa630b0: stur            w0, [x1, #0xf]
    //     0xa630b4: ldurb           w16, [x1, #-1]
    //     0xa630b8: ldurb           w17, [x0, #-1]
    //     0xa630bc: and             x16, x17, x16, lsr #2
    //     0xa630c0: tst             x16, HEAP, lsr #32
    //     0xa630c4: b.eq            #0xa630cc
    //     0xa630c8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa630cc: mov             x0, x1
    // 0xa630d0: LeaveFrame
    //     0xa630d0: mov             SP, fp
    //     0xa630d4: ldp             fp, lr, [SP], #0x10
    // 0xa630d8: ret
    //     0xa630d8: ret             
    // 0xa630dc: r0 = RangeError()
    //     0xa630dc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa630e0: mov             x1, x0
    // 0xa630e4: r0 = "Not enough bytes available."
    //     0xa630e4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa630e8: ldr             x0, [x0, #0x8a8]
    // 0xa630ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xa630ec: stur            w0, [x1, #0x17]
    // 0xa630f0: r2 = false
    //     0xa630f0: add             x2, NULL, #0x30  ; false
    // 0xa630f4: StoreField: r1->field_b = r2
    //     0xa630f4: stur            w2, [x1, #0xb]
    // 0xa630f8: mov             x0, x1
    // 0xa630fc: r0 = Throw()
    //     0xa630fc: bl              #0xec04b8  ; ThrowStub
    // 0xa63100: brk             #0
    // 0xa63104: r0 = "Not enough bytes available."
    //     0xa63104: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa63108: ldr             x0, [x0, #0x8a8]
    // 0xa6310c: r2 = false
    //     0xa6310c: add             x2, NULL, #0x30  ; false
    // 0xa63110: r0 = RangeError()
    //     0xa63110: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa63114: mov             x1, x0
    // 0xa63118: r0 = "Not enough bytes available."
    //     0xa63118: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6311c: ldr             x0, [x0, #0x8a8]
    // 0xa63120: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63120: stur            w0, [x1, #0x17]
    // 0xa63124: r0 = false
    //     0xa63124: add             x0, NULL, #0x30  ; false
    // 0xa63128: StoreField: r1->field_b = r0
    //     0xa63128: stur            w0, [x1, #0xb]
    // 0xa6312c: mov             x0, x1
    // 0xa63130: r0 = Throw()
    //     0xa63130: bl              #0xec04b8  ; ThrowStub
    // 0xa63134: brk             #0
    // 0xa63138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa63138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6313c: b               #0xa62a7c
    // 0xa63140: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa63140: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa63144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa63144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa63148: b               #0xa62b04
    // 0xa6314c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa6314c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd3ab4, size: 0x770
    // 0xbd3ab4: EnterFrame
    //     0xbd3ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd3ab8: mov             fp, SP
    // 0xbd3abc: AllocStack(0x28)
    //     0xbd3abc: sub             SP, SP, #0x28
    // 0xbd3ac0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd3ac0: mov             x4, x2
    //     0xbd3ac4: stur            x2, [fp, #-8]
    //     0xbd3ac8: stur            x3, [fp, #-0x10]
    // 0xbd3acc: CheckStackOverflow
    //     0xbd3acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd3ad0: cmp             SP, x16
    //     0xbd3ad4: b.ls            #0xbd41bc
    // 0xbd3ad8: mov             x0, x3
    // 0xbd3adc: r2 = Null
    //     0xbd3adc: mov             x2, NULL
    // 0xbd3ae0: r1 = Null
    //     0xbd3ae0: mov             x1, NULL
    // 0xbd3ae4: r4 = 60
    //     0xbd3ae4: movz            x4, #0x3c
    // 0xbd3ae8: branchIfSmi(r0, 0xbd3af4)
    //     0xbd3ae8: tbz             w0, #0, #0xbd3af4
    // 0xbd3aec: r4 = LoadClassIdInstr(r0)
    //     0xbd3aec: ldur            x4, [x0, #-1]
    //     0xbd3af0: ubfx            x4, x4, #0xc, #0x14
    // 0xbd3af4: cmp             x4, #0x646
    // 0xbd3af8: b.eq            #0xbd3b10
    // 0xbd3afc: r8 = Location
    //     0xbd3afc: add             x8, PP, #8, lsl #12  ; [pp+0x8120] Type: Location
    //     0xbd3b00: ldr             x8, [x8, #0x120]
    // 0xbd3b04: r3 = Null
    //     0xbd3b04: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b320] Null
    //     0xbd3b08: ldr             x3, [x3, #0x320]
    // 0xbd3b0c: r0 = Location()
    //     0xbd3b0c: bl              #0x8149ac  ; IsType_Location_Stub
    // 0xbd3b10: ldur            x0, [fp, #-8]
    // 0xbd3b14: LoadField: r1 = r0->field_b
    //     0xbd3b14: ldur            w1, [x0, #0xb]
    // 0xbd3b18: DecompressPointer r1
    //     0xbd3b18: add             x1, x1, HEAP, lsl #32
    // 0xbd3b1c: LoadField: r2 = r1->field_13
    //     0xbd3b1c: ldur            w2, [x1, #0x13]
    // 0xbd3b20: LoadField: r1 = r0->field_13
    //     0xbd3b20: ldur            x1, [x0, #0x13]
    // 0xbd3b24: r3 = LoadInt32Instr(r2)
    //     0xbd3b24: sbfx            x3, x2, #1, #0x1f
    // 0xbd3b28: sub             x2, x3, x1
    // 0xbd3b2c: cmp             x2, #1
    // 0xbd3b30: b.ge            #0xbd3b40
    // 0xbd3b34: mov             x1, x0
    // 0xbd3b38: r2 = 1
    //     0xbd3b38: movz            x2, #0x1
    // 0xbd3b3c: r0 = _increaseBufferSize()
    //     0xbd3b3c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3b40: ldur            x3, [fp, #-8]
    // 0xbd3b44: r2 = 11
    //     0xbd3b44: movz            x2, #0xb
    // 0xbd3b48: LoadField: r4 = r3->field_b
    //     0xbd3b48: ldur            w4, [x3, #0xb]
    // 0xbd3b4c: DecompressPointer r4
    //     0xbd3b4c: add             x4, x4, HEAP, lsl #32
    // 0xbd3b50: LoadField: r5 = r3->field_13
    //     0xbd3b50: ldur            x5, [x3, #0x13]
    // 0xbd3b54: add             x6, x5, #1
    // 0xbd3b58: StoreField: r3->field_13 = r6
    //     0xbd3b58: stur            x6, [x3, #0x13]
    // 0xbd3b5c: LoadField: r0 = r4->field_13
    //     0xbd3b5c: ldur            w0, [x4, #0x13]
    // 0xbd3b60: r7 = LoadInt32Instr(r0)
    //     0xbd3b60: sbfx            x7, x0, #1, #0x1f
    // 0xbd3b64: mov             x0, x7
    // 0xbd3b68: mov             x1, x5
    // 0xbd3b6c: cmp             x1, x0
    // 0xbd3b70: b.hs            #0xbd41c4
    // 0xbd3b74: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd3b74: add             x0, x4, x5
    //     0xbd3b78: strb            w2, [x0, #0x17]
    // 0xbd3b7c: sub             x0, x7, x6
    // 0xbd3b80: cmp             x0, #1
    // 0xbd3b84: b.ge            #0xbd3b94
    // 0xbd3b88: mov             x1, x3
    // 0xbd3b8c: r2 = 1
    //     0xbd3b8c: movz            x2, #0x1
    // 0xbd3b90: r0 = _increaseBufferSize()
    //     0xbd3b90: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3b94: ldur            x2, [fp, #-8]
    // 0xbd3b98: ldur            x3, [fp, #-0x10]
    // 0xbd3b9c: LoadField: r4 = r2->field_b
    //     0xbd3b9c: ldur            w4, [x2, #0xb]
    // 0xbd3ba0: DecompressPointer r4
    //     0xbd3ba0: add             x4, x4, HEAP, lsl #32
    // 0xbd3ba4: LoadField: r5 = r2->field_13
    //     0xbd3ba4: ldur            x5, [x2, #0x13]
    // 0xbd3ba8: add             x0, x5, #1
    // 0xbd3bac: StoreField: r2->field_13 = r0
    //     0xbd3bac: stur            x0, [x2, #0x13]
    // 0xbd3bb0: LoadField: r0 = r4->field_13
    //     0xbd3bb0: ldur            w0, [x4, #0x13]
    // 0xbd3bb4: r1 = LoadInt32Instr(r0)
    //     0xbd3bb4: sbfx            x1, x0, #1, #0x1f
    // 0xbd3bb8: mov             x0, x1
    // 0xbd3bbc: mov             x1, x5
    // 0xbd3bc0: cmp             x1, x0
    // 0xbd3bc4: b.hs            #0xbd41c8
    // 0xbd3bc8: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd3bc8: add             x0, x4, x5
    //     0xbd3bcc: strb            wzr, [x0, #0x17]
    // 0xbd3bd0: LoadField: r0 = r3->field_13
    //     0xbd3bd0: ldur            w0, [x3, #0x13]
    // 0xbd3bd4: DecompressPointer r0
    //     0xbd3bd4: add             x0, x0, HEAP, lsl #32
    // 0xbd3bd8: r16 = <String>
    //     0xbd3bd8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3bdc: stp             x2, x16, [SP, #8]
    // 0xbd3be0: str             x0, [SP]
    // 0xbd3be4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3be4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3be8: r0 = write()
    //     0xbd3be8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3bec: ldur            x0, [fp, #-8]
    // 0xbd3bf0: LoadField: r1 = r0->field_b
    //     0xbd3bf0: ldur            w1, [x0, #0xb]
    // 0xbd3bf4: DecompressPointer r1
    //     0xbd3bf4: add             x1, x1, HEAP, lsl #32
    // 0xbd3bf8: LoadField: r2 = r1->field_13
    //     0xbd3bf8: ldur            w2, [x1, #0x13]
    // 0xbd3bfc: LoadField: r1 = r0->field_13
    //     0xbd3bfc: ldur            x1, [x0, #0x13]
    // 0xbd3c00: r3 = LoadInt32Instr(r2)
    //     0xbd3c00: sbfx            x3, x2, #1, #0x1f
    // 0xbd3c04: sub             x2, x3, x1
    // 0xbd3c08: cmp             x2, #1
    // 0xbd3c0c: b.ge            #0xbd3c1c
    // 0xbd3c10: mov             x1, x0
    // 0xbd3c14: r2 = 1
    //     0xbd3c14: movz            x2, #0x1
    // 0xbd3c18: r0 = _increaseBufferSize()
    //     0xbd3c18: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3c1c: ldur            x2, [fp, #-8]
    // 0xbd3c20: ldur            x3, [fp, #-0x10]
    // 0xbd3c24: r4 = 1
    //     0xbd3c24: movz            x4, #0x1
    // 0xbd3c28: LoadField: r5 = r2->field_b
    //     0xbd3c28: ldur            w5, [x2, #0xb]
    // 0xbd3c2c: DecompressPointer r5
    //     0xbd3c2c: add             x5, x5, HEAP, lsl #32
    // 0xbd3c30: LoadField: r6 = r2->field_13
    //     0xbd3c30: ldur            x6, [x2, #0x13]
    // 0xbd3c34: add             x0, x6, #1
    // 0xbd3c38: StoreField: r2->field_13 = r0
    //     0xbd3c38: stur            x0, [x2, #0x13]
    // 0xbd3c3c: LoadField: r0 = r5->field_13
    //     0xbd3c3c: ldur            w0, [x5, #0x13]
    // 0xbd3c40: r1 = LoadInt32Instr(r0)
    //     0xbd3c40: sbfx            x1, x0, #1, #0x1f
    // 0xbd3c44: mov             x0, x1
    // 0xbd3c48: mov             x1, x6
    // 0xbd3c4c: cmp             x1, x0
    // 0xbd3c50: b.hs            #0xbd41cc
    // 0xbd3c54: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3c54: add             x0, x5, x6
    //     0xbd3c58: strb            w4, [x0, #0x17]
    // 0xbd3c5c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd3c5c: ldur            w0, [x3, #0x17]
    // 0xbd3c60: DecompressPointer r0
    //     0xbd3c60: add             x0, x0, HEAP, lsl #32
    // 0xbd3c64: r16 = <String>
    //     0xbd3c64: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3c68: stp             x2, x16, [SP, #8]
    // 0xbd3c6c: str             x0, [SP]
    // 0xbd3c70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3c70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3c74: r0 = write()
    //     0xbd3c74: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3c78: ldur            x0, [fp, #-8]
    // 0xbd3c7c: LoadField: r1 = r0->field_b
    //     0xbd3c7c: ldur            w1, [x0, #0xb]
    // 0xbd3c80: DecompressPointer r1
    //     0xbd3c80: add             x1, x1, HEAP, lsl #32
    // 0xbd3c84: LoadField: r2 = r1->field_13
    //     0xbd3c84: ldur            w2, [x1, #0x13]
    // 0xbd3c88: LoadField: r1 = r0->field_13
    //     0xbd3c88: ldur            x1, [x0, #0x13]
    // 0xbd3c8c: r3 = LoadInt32Instr(r2)
    //     0xbd3c8c: sbfx            x3, x2, #1, #0x1f
    // 0xbd3c90: sub             x2, x3, x1
    // 0xbd3c94: cmp             x2, #1
    // 0xbd3c98: b.ge            #0xbd3ca8
    // 0xbd3c9c: mov             x1, x0
    // 0xbd3ca0: r2 = 1
    //     0xbd3ca0: movz            x2, #0x1
    // 0xbd3ca4: r0 = _increaseBufferSize()
    //     0xbd3ca4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3ca8: ldur            x2, [fp, #-8]
    // 0xbd3cac: ldur            x3, [fp, #-0x10]
    // 0xbd3cb0: r4 = 2
    //     0xbd3cb0: movz            x4, #0x2
    // 0xbd3cb4: LoadField: r5 = r2->field_b
    //     0xbd3cb4: ldur            w5, [x2, #0xb]
    // 0xbd3cb8: DecompressPointer r5
    //     0xbd3cb8: add             x5, x5, HEAP, lsl #32
    // 0xbd3cbc: LoadField: r6 = r2->field_13
    //     0xbd3cbc: ldur            x6, [x2, #0x13]
    // 0xbd3cc0: add             x0, x6, #1
    // 0xbd3cc4: StoreField: r2->field_13 = r0
    //     0xbd3cc4: stur            x0, [x2, #0x13]
    // 0xbd3cc8: LoadField: r0 = r5->field_13
    //     0xbd3cc8: ldur            w0, [x5, #0x13]
    // 0xbd3ccc: r1 = LoadInt32Instr(r0)
    //     0xbd3ccc: sbfx            x1, x0, #1, #0x1f
    // 0xbd3cd0: mov             x0, x1
    // 0xbd3cd4: mov             x1, x6
    // 0xbd3cd8: cmp             x1, x0
    // 0xbd3cdc: b.hs            #0xbd41d0
    // 0xbd3ce0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3ce0: add             x0, x5, x6
    //     0xbd3ce4: strb            w4, [x0, #0x17]
    // 0xbd3ce8: LoadField: r0 = r3->field_1b
    //     0xbd3ce8: ldur            w0, [x3, #0x1b]
    // 0xbd3cec: DecompressPointer r0
    //     0xbd3cec: add             x0, x0, HEAP, lsl #32
    // 0xbd3cf0: r16 = <String>
    //     0xbd3cf0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3cf4: stp             x2, x16, [SP, #8]
    // 0xbd3cf8: str             x0, [SP]
    // 0xbd3cfc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3cfc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3d00: r0 = write()
    //     0xbd3d00: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3d04: ldur            x0, [fp, #-8]
    // 0xbd3d08: LoadField: r1 = r0->field_b
    //     0xbd3d08: ldur            w1, [x0, #0xb]
    // 0xbd3d0c: DecompressPointer r1
    //     0xbd3d0c: add             x1, x1, HEAP, lsl #32
    // 0xbd3d10: LoadField: r2 = r1->field_13
    //     0xbd3d10: ldur            w2, [x1, #0x13]
    // 0xbd3d14: LoadField: r1 = r0->field_13
    //     0xbd3d14: ldur            x1, [x0, #0x13]
    // 0xbd3d18: r3 = LoadInt32Instr(r2)
    //     0xbd3d18: sbfx            x3, x2, #1, #0x1f
    // 0xbd3d1c: sub             x2, x3, x1
    // 0xbd3d20: cmp             x2, #1
    // 0xbd3d24: b.ge            #0xbd3d34
    // 0xbd3d28: mov             x1, x0
    // 0xbd3d2c: r2 = 1
    //     0xbd3d2c: movz            x2, #0x1
    // 0xbd3d30: r0 = _increaseBufferSize()
    //     0xbd3d30: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3d34: ldur            x2, [fp, #-8]
    // 0xbd3d38: ldur            x3, [fp, #-0x10]
    // 0xbd3d3c: r4 = 3
    //     0xbd3d3c: movz            x4, #0x3
    // 0xbd3d40: LoadField: r5 = r2->field_b
    //     0xbd3d40: ldur            w5, [x2, #0xb]
    // 0xbd3d44: DecompressPointer r5
    //     0xbd3d44: add             x5, x5, HEAP, lsl #32
    // 0xbd3d48: LoadField: r6 = r2->field_13
    //     0xbd3d48: ldur            x6, [x2, #0x13]
    // 0xbd3d4c: add             x0, x6, #1
    // 0xbd3d50: StoreField: r2->field_13 = r0
    //     0xbd3d50: stur            x0, [x2, #0x13]
    // 0xbd3d54: LoadField: r0 = r5->field_13
    //     0xbd3d54: ldur            w0, [x5, #0x13]
    // 0xbd3d58: r1 = LoadInt32Instr(r0)
    //     0xbd3d58: sbfx            x1, x0, #1, #0x1f
    // 0xbd3d5c: mov             x0, x1
    // 0xbd3d60: mov             x1, x6
    // 0xbd3d64: cmp             x1, x0
    // 0xbd3d68: b.hs            #0xbd41d4
    // 0xbd3d6c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3d6c: add             x0, x5, x6
    //     0xbd3d70: strb            w4, [x0, #0x17]
    // 0xbd3d74: LoadField: d0 = r3->field_1f
    //     0xbd3d74: ldur            d0, [x3, #0x1f]
    // 0xbd3d78: r0 = inline_Allocate_Double()
    //     0xbd3d78: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd3d7c: add             x0, x0, #0x10
    //     0xbd3d80: cmp             x1, x0
    //     0xbd3d84: b.ls            #0xbd41d8
    //     0xbd3d88: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd3d8c: sub             x0, x0, #0xf
    //     0xbd3d90: movz            x1, #0xe15c
    //     0xbd3d94: movk            x1, #0x3, lsl #16
    //     0xbd3d98: stur            x1, [x0, #-1]
    // 0xbd3d9c: StoreField: r0->field_7 = d0
    //     0xbd3d9c: stur            d0, [x0, #7]
    // 0xbd3da0: r16 = <double>
    //     0xbd3da0: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd3da4: stp             x2, x16, [SP, #8]
    // 0xbd3da8: str             x0, [SP]
    // 0xbd3dac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3dac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3db0: r0 = write()
    //     0xbd3db0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3db4: ldur            x0, [fp, #-8]
    // 0xbd3db8: LoadField: r1 = r0->field_b
    //     0xbd3db8: ldur            w1, [x0, #0xb]
    // 0xbd3dbc: DecompressPointer r1
    //     0xbd3dbc: add             x1, x1, HEAP, lsl #32
    // 0xbd3dc0: LoadField: r2 = r1->field_13
    //     0xbd3dc0: ldur            w2, [x1, #0x13]
    // 0xbd3dc4: LoadField: r1 = r0->field_13
    //     0xbd3dc4: ldur            x1, [x0, #0x13]
    // 0xbd3dc8: r3 = LoadInt32Instr(r2)
    //     0xbd3dc8: sbfx            x3, x2, #1, #0x1f
    // 0xbd3dcc: sub             x2, x3, x1
    // 0xbd3dd0: cmp             x2, #1
    // 0xbd3dd4: b.ge            #0xbd3de4
    // 0xbd3dd8: mov             x1, x0
    // 0xbd3ddc: r2 = 1
    //     0xbd3ddc: movz            x2, #0x1
    // 0xbd3de0: r0 = _increaseBufferSize()
    //     0xbd3de0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3de4: ldur            x2, [fp, #-8]
    // 0xbd3de8: ldur            x3, [fp, #-0x10]
    // 0xbd3dec: r4 = 4
    //     0xbd3dec: movz            x4, #0x4
    // 0xbd3df0: LoadField: r5 = r2->field_b
    //     0xbd3df0: ldur            w5, [x2, #0xb]
    // 0xbd3df4: DecompressPointer r5
    //     0xbd3df4: add             x5, x5, HEAP, lsl #32
    // 0xbd3df8: LoadField: r6 = r2->field_13
    //     0xbd3df8: ldur            x6, [x2, #0x13]
    // 0xbd3dfc: add             x0, x6, #1
    // 0xbd3e00: StoreField: r2->field_13 = r0
    //     0xbd3e00: stur            x0, [x2, #0x13]
    // 0xbd3e04: LoadField: r0 = r5->field_13
    //     0xbd3e04: ldur            w0, [x5, #0x13]
    // 0xbd3e08: r1 = LoadInt32Instr(r0)
    //     0xbd3e08: sbfx            x1, x0, #1, #0x1f
    // 0xbd3e0c: mov             x0, x1
    // 0xbd3e10: mov             x1, x6
    // 0xbd3e14: cmp             x1, x0
    // 0xbd3e18: b.hs            #0xbd41f0
    // 0xbd3e1c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3e1c: add             x0, x5, x6
    //     0xbd3e20: strb            w4, [x0, #0x17]
    // 0xbd3e24: LoadField: d0 = r3->field_27
    //     0xbd3e24: ldur            d0, [x3, #0x27]
    // 0xbd3e28: r0 = inline_Allocate_Double()
    //     0xbd3e28: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd3e2c: add             x0, x0, #0x10
    //     0xbd3e30: cmp             x1, x0
    //     0xbd3e34: b.ls            #0xbd41f4
    //     0xbd3e38: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd3e3c: sub             x0, x0, #0xf
    //     0xbd3e40: movz            x1, #0xe15c
    //     0xbd3e44: movk            x1, #0x3, lsl #16
    //     0xbd3e48: stur            x1, [x0, #-1]
    // 0xbd3e4c: StoreField: r0->field_7 = d0
    //     0xbd3e4c: stur            d0, [x0, #7]
    // 0xbd3e50: r16 = <double>
    //     0xbd3e50: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd3e54: stp             x2, x16, [SP, #8]
    // 0xbd3e58: str             x0, [SP]
    // 0xbd3e5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3e5c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3e60: r0 = write()
    //     0xbd3e60: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3e64: ldur            x0, [fp, #-8]
    // 0xbd3e68: LoadField: r1 = r0->field_b
    //     0xbd3e68: ldur            w1, [x0, #0xb]
    // 0xbd3e6c: DecompressPointer r1
    //     0xbd3e6c: add             x1, x1, HEAP, lsl #32
    // 0xbd3e70: LoadField: r2 = r1->field_13
    //     0xbd3e70: ldur            w2, [x1, #0x13]
    // 0xbd3e74: LoadField: r1 = r0->field_13
    //     0xbd3e74: ldur            x1, [x0, #0x13]
    // 0xbd3e78: r3 = LoadInt32Instr(r2)
    //     0xbd3e78: sbfx            x3, x2, #1, #0x1f
    // 0xbd3e7c: sub             x2, x3, x1
    // 0xbd3e80: cmp             x2, #1
    // 0xbd3e84: b.ge            #0xbd3e94
    // 0xbd3e88: mov             x1, x0
    // 0xbd3e8c: r2 = 1
    //     0xbd3e8c: movz            x2, #0x1
    // 0xbd3e90: r0 = _increaseBufferSize()
    //     0xbd3e90: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3e94: ldur            x2, [fp, #-8]
    // 0xbd3e98: ldur            x3, [fp, #-0x10]
    // 0xbd3e9c: r4 = 5
    //     0xbd3e9c: movz            x4, #0x5
    // 0xbd3ea0: LoadField: r5 = r2->field_b
    //     0xbd3ea0: ldur            w5, [x2, #0xb]
    // 0xbd3ea4: DecompressPointer r5
    //     0xbd3ea4: add             x5, x5, HEAP, lsl #32
    // 0xbd3ea8: LoadField: r6 = r2->field_13
    //     0xbd3ea8: ldur            x6, [x2, #0x13]
    // 0xbd3eac: add             x0, x6, #1
    // 0xbd3eb0: StoreField: r2->field_13 = r0
    //     0xbd3eb0: stur            x0, [x2, #0x13]
    // 0xbd3eb4: LoadField: r0 = r5->field_13
    //     0xbd3eb4: ldur            w0, [x5, #0x13]
    // 0xbd3eb8: r1 = LoadInt32Instr(r0)
    //     0xbd3eb8: sbfx            x1, x0, #1, #0x1f
    // 0xbd3ebc: mov             x0, x1
    // 0xbd3ec0: mov             x1, x6
    // 0xbd3ec4: cmp             x1, x0
    // 0xbd3ec8: b.hs            #0xbd420c
    // 0xbd3ecc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3ecc: add             x0, x5, x6
    //     0xbd3ed0: strb            w4, [x0, #0x17]
    // 0xbd3ed4: LoadField: r0 = r3->field_2f
    //     0xbd3ed4: ldur            w0, [x3, #0x2f]
    // 0xbd3ed8: DecompressPointer r0
    //     0xbd3ed8: add             x0, x0, HEAP, lsl #32
    // 0xbd3edc: r16 = <int?>
    //     0xbd3edc: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xbd3ee0: stp             x2, x16, [SP, #8]
    // 0xbd3ee4: str             x0, [SP]
    // 0xbd3ee8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3ee8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3eec: r0 = write()
    //     0xbd3eec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3ef0: ldur            x0, [fp, #-8]
    // 0xbd3ef4: LoadField: r1 = r0->field_b
    //     0xbd3ef4: ldur            w1, [x0, #0xb]
    // 0xbd3ef8: DecompressPointer r1
    //     0xbd3ef8: add             x1, x1, HEAP, lsl #32
    // 0xbd3efc: LoadField: r2 = r1->field_13
    //     0xbd3efc: ldur            w2, [x1, #0x13]
    // 0xbd3f00: LoadField: r1 = r0->field_13
    //     0xbd3f00: ldur            x1, [x0, #0x13]
    // 0xbd3f04: r3 = LoadInt32Instr(r2)
    //     0xbd3f04: sbfx            x3, x2, #1, #0x1f
    // 0xbd3f08: sub             x2, x3, x1
    // 0xbd3f0c: cmp             x2, #1
    // 0xbd3f10: b.ge            #0xbd3f20
    // 0xbd3f14: mov             x1, x0
    // 0xbd3f18: r2 = 1
    //     0xbd3f18: movz            x2, #0x1
    // 0xbd3f1c: r0 = _increaseBufferSize()
    //     0xbd3f1c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3f20: ldur            x2, [fp, #-8]
    // 0xbd3f24: ldur            x3, [fp, #-0x10]
    // 0xbd3f28: r4 = 6
    //     0xbd3f28: movz            x4, #0x6
    // 0xbd3f2c: LoadField: r5 = r2->field_b
    //     0xbd3f2c: ldur            w5, [x2, #0xb]
    // 0xbd3f30: DecompressPointer r5
    //     0xbd3f30: add             x5, x5, HEAP, lsl #32
    // 0xbd3f34: LoadField: r6 = r2->field_13
    //     0xbd3f34: ldur            x6, [x2, #0x13]
    // 0xbd3f38: add             x0, x6, #1
    // 0xbd3f3c: StoreField: r2->field_13 = r0
    //     0xbd3f3c: stur            x0, [x2, #0x13]
    // 0xbd3f40: LoadField: r0 = r5->field_13
    //     0xbd3f40: ldur            w0, [x5, #0x13]
    // 0xbd3f44: r1 = LoadInt32Instr(r0)
    //     0xbd3f44: sbfx            x1, x0, #1, #0x1f
    // 0xbd3f48: mov             x0, x1
    // 0xbd3f4c: mov             x1, x6
    // 0xbd3f50: cmp             x1, x0
    // 0xbd3f54: b.hs            #0xbd4210
    // 0xbd3f58: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3f58: add             x0, x5, x6
    //     0xbd3f5c: strb            w4, [x0, #0x17]
    // 0xbd3f60: LoadField: r0 = r3->field_33
    //     0xbd3f60: ldur            w0, [x3, #0x33]
    // 0xbd3f64: DecompressPointer r0
    //     0xbd3f64: add             x0, x0, HEAP, lsl #32
    // 0xbd3f68: r16 = <String>
    //     0xbd3f68: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd3f6c: stp             x2, x16, [SP, #8]
    // 0xbd3f70: str             x0, [SP]
    // 0xbd3f74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd3f74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd3f78: r0 = write()
    //     0xbd3f78: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd3f7c: ldur            x0, [fp, #-8]
    // 0xbd3f80: LoadField: r1 = r0->field_b
    //     0xbd3f80: ldur            w1, [x0, #0xb]
    // 0xbd3f84: DecompressPointer r1
    //     0xbd3f84: add             x1, x1, HEAP, lsl #32
    // 0xbd3f88: LoadField: r2 = r1->field_13
    //     0xbd3f88: ldur            w2, [x1, #0x13]
    // 0xbd3f8c: LoadField: r1 = r0->field_13
    //     0xbd3f8c: ldur            x1, [x0, #0x13]
    // 0xbd3f90: r3 = LoadInt32Instr(r2)
    //     0xbd3f90: sbfx            x3, x2, #1, #0x1f
    // 0xbd3f94: sub             x2, x3, x1
    // 0xbd3f98: cmp             x2, #1
    // 0xbd3f9c: b.ge            #0xbd3fac
    // 0xbd3fa0: mov             x1, x0
    // 0xbd3fa4: r2 = 1
    //     0xbd3fa4: movz            x2, #0x1
    // 0xbd3fa8: r0 = _increaseBufferSize()
    //     0xbd3fa8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd3fac: ldur            x2, [fp, #-8]
    // 0xbd3fb0: ldur            x3, [fp, #-0x10]
    // 0xbd3fb4: r4 = 7
    //     0xbd3fb4: movz            x4, #0x7
    // 0xbd3fb8: LoadField: r5 = r2->field_b
    //     0xbd3fb8: ldur            w5, [x2, #0xb]
    // 0xbd3fbc: DecompressPointer r5
    //     0xbd3fbc: add             x5, x5, HEAP, lsl #32
    // 0xbd3fc0: LoadField: r6 = r2->field_13
    //     0xbd3fc0: ldur            x6, [x2, #0x13]
    // 0xbd3fc4: add             x0, x6, #1
    // 0xbd3fc8: StoreField: r2->field_13 = r0
    //     0xbd3fc8: stur            x0, [x2, #0x13]
    // 0xbd3fcc: LoadField: r0 = r5->field_13
    //     0xbd3fcc: ldur            w0, [x5, #0x13]
    // 0xbd3fd0: r1 = LoadInt32Instr(r0)
    //     0xbd3fd0: sbfx            x1, x0, #1, #0x1f
    // 0xbd3fd4: mov             x0, x1
    // 0xbd3fd8: mov             x1, x6
    // 0xbd3fdc: cmp             x1, x0
    // 0xbd3fe0: b.hs            #0xbd4214
    // 0xbd3fe4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd3fe4: add             x0, x5, x6
    //     0xbd3fe8: strb            w4, [x0, #0x17]
    // 0xbd3fec: LoadField: r0 = r3->field_37
    //     0xbd3fec: ldur            w0, [x3, #0x37]
    // 0xbd3ff0: DecompressPointer r0
    //     0xbd3ff0: add             x0, x0, HEAP, lsl #32
    // 0xbd3ff4: r16 = <bool>
    //     0xbd3ff4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd3ff8: stp             x2, x16, [SP, #8]
    // 0xbd3ffc: str             x0, [SP]
    // 0xbd4000: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4000: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4004: r0 = write()
    //     0xbd4004: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4008: ldur            x0, [fp, #-8]
    // 0xbd400c: LoadField: r1 = r0->field_b
    //     0xbd400c: ldur            w1, [x0, #0xb]
    // 0xbd4010: DecompressPointer r1
    //     0xbd4010: add             x1, x1, HEAP, lsl #32
    // 0xbd4014: LoadField: r2 = r1->field_13
    //     0xbd4014: ldur            w2, [x1, #0x13]
    // 0xbd4018: LoadField: r1 = r0->field_13
    //     0xbd4018: ldur            x1, [x0, #0x13]
    // 0xbd401c: r3 = LoadInt32Instr(r2)
    //     0xbd401c: sbfx            x3, x2, #1, #0x1f
    // 0xbd4020: sub             x2, x3, x1
    // 0xbd4024: cmp             x2, #1
    // 0xbd4028: b.ge            #0xbd4038
    // 0xbd402c: mov             x1, x0
    // 0xbd4030: r2 = 1
    //     0xbd4030: movz            x2, #0x1
    // 0xbd4034: r0 = _increaseBufferSize()
    //     0xbd4034: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4038: ldur            x2, [fp, #-8]
    // 0xbd403c: ldur            x3, [fp, #-0x10]
    // 0xbd4040: r4 = 8
    //     0xbd4040: movz            x4, #0x8
    // 0xbd4044: LoadField: r5 = r2->field_b
    //     0xbd4044: ldur            w5, [x2, #0xb]
    // 0xbd4048: DecompressPointer r5
    //     0xbd4048: add             x5, x5, HEAP, lsl #32
    // 0xbd404c: LoadField: r6 = r2->field_13
    //     0xbd404c: ldur            x6, [x2, #0x13]
    // 0xbd4050: add             x0, x6, #1
    // 0xbd4054: StoreField: r2->field_13 = r0
    //     0xbd4054: stur            x0, [x2, #0x13]
    // 0xbd4058: LoadField: r0 = r5->field_13
    //     0xbd4058: ldur            w0, [x5, #0x13]
    // 0xbd405c: r1 = LoadInt32Instr(r0)
    //     0xbd405c: sbfx            x1, x0, #1, #0x1f
    // 0xbd4060: mov             x0, x1
    // 0xbd4064: mov             x1, x6
    // 0xbd4068: cmp             x1, x0
    // 0xbd406c: b.hs            #0xbd4218
    // 0xbd4070: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4070: add             x0, x5, x6
    //     0xbd4074: strb            w4, [x0, #0x17]
    // 0xbd4078: LoadField: r0 = r3->field_3b
    //     0xbd4078: ldur            w0, [x3, #0x3b]
    // 0xbd407c: DecompressPointer r0
    //     0xbd407c: add             x0, x0, HEAP, lsl #32
    // 0xbd4080: r16 = <String?>
    //     0xbd4080: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd4084: stp             x2, x16, [SP, #8]
    // 0xbd4088: str             x0, [SP]
    // 0xbd408c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd408c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4090: r0 = write()
    //     0xbd4090: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4094: ldur            x0, [fp, #-8]
    // 0xbd4098: LoadField: r1 = r0->field_b
    //     0xbd4098: ldur            w1, [x0, #0xb]
    // 0xbd409c: DecompressPointer r1
    //     0xbd409c: add             x1, x1, HEAP, lsl #32
    // 0xbd40a0: LoadField: r2 = r1->field_13
    //     0xbd40a0: ldur            w2, [x1, #0x13]
    // 0xbd40a4: LoadField: r1 = r0->field_13
    //     0xbd40a4: ldur            x1, [x0, #0x13]
    // 0xbd40a8: r3 = LoadInt32Instr(r2)
    //     0xbd40a8: sbfx            x3, x2, #1, #0x1f
    // 0xbd40ac: sub             x2, x3, x1
    // 0xbd40b0: cmp             x2, #1
    // 0xbd40b4: b.ge            #0xbd40c4
    // 0xbd40b8: mov             x1, x0
    // 0xbd40bc: r2 = 1
    //     0xbd40bc: movz            x2, #0x1
    // 0xbd40c0: r0 = _increaseBufferSize()
    //     0xbd40c0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd40c4: ldur            x2, [fp, #-8]
    // 0xbd40c8: ldur            x3, [fp, #-0x10]
    // 0xbd40cc: r4 = 9
    //     0xbd40cc: movz            x4, #0x9
    // 0xbd40d0: LoadField: r5 = r2->field_b
    //     0xbd40d0: ldur            w5, [x2, #0xb]
    // 0xbd40d4: DecompressPointer r5
    //     0xbd40d4: add             x5, x5, HEAP, lsl #32
    // 0xbd40d8: LoadField: r6 = r2->field_13
    //     0xbd40d8: ldur            x6, [x2, #0x13]
    // 0xbd40dc: add             x0, x6, #1
    // 0xbd40e0: StoreField: r2->field_13 = r0
    //     0xbd40e0: stur            x0, [x2, #0x13]
    // 0xbd40e4: LoadField: r0 = r5->field_13
    //     0xbd40e4: ldur            w0, [x5, #0x13]
    // 0xbd40e8: r1 = LoadInt32Instr(r0)
    //     0xbd40e8: sbfx            x1, x0, #1, #0x1f
    // 0xbd40ec: mov             x0, x1
    // 0xbd40f0: mov             x1, x6
    // 0xbd40f4: cmp             x1, x0
    // 0xbd40f8: b.hs            #0xbd421c
    // 0xbd40fc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd40fc: add             x0, x5, x6
    //     0xbd4100: strb            w4, [x0, #0x17]
    // 0xbd4104: LoadField: r0 = r3->field_3f
    //     0xbd4104: ldur            w0, [x3, #0x3f]
    // 0xbd4108: DecompressPointer r0
    //     0xbd4108: add             x0, x0, HEAP, lsl #32
    // 0xbd410c: r16 = <String?>
    //     0xbd410c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd4110: stp             x2, x16, [SP, #8]
    // 0xbd4114: str             x0, [SP]
    // 0xbd4118: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4118: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd411c: r0 = write()
    //     0xbd411c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4120: ldur            x0, [fp, #-8]
    // 0xbd4124: LoadField: r1 = r0->field_b
    //     0xbd4124: ldur            w1, [x0, #0xb]
    // 0xbd4128: DecompressPointer r1
    //     0xbd4128: add             x1, x1, HEAP, lsl #32
    // 0xbd412c: LoadField: r2 = r1->field_13
    //     0xbd412c: ldur            w2, [x1, #0x13]
    // 0xbd4130: LoadField: r1 = r0->field_13
    //     0xbd4130: ldur            x1, [x0, #0x13]
    // 0xbd4134: r3 = LoadInt32Instr(r2)
    //     0xbd4134: sbfx            x3, x2, #1, #0x1f
    // 0xbd4138: sub             x2, x3, x1
    // 0xbd413c: cmp             x2, #1
    // 0xbd4140: b.ge            #0xbd4150
    // 0xbd4144: mov             x1, x0
    // 0xbd4148: r2 = 1
    //     0xbd4148: movz            x2, #0x1
    // 0xbd414c: r0 = _increaseBufferSize()
    //     0xbd414c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4150: ldur            x2, [fp, #-8]
    // 0xbd4154: ldur            x3, [fp, #-0x10]
    // 0xbd4158: r4 = 10
    //     0xbd4158: movz            x4, #0xa
    // 0xbd415c: LoadField: r5 = r2->field_b
    //     0xbd415c: ldur            w5, [x2, #0xb]
    // 0xbd4160: DecompressPointer r5
    //     0xbd4160: add             x5, x5, HEAP, lsl #32
    // 0xbd4164: LoadField: r6 = r2->field_13
    //     0xbd4164: ldur            x6, [x2, #0x13]
    // 0xbd4168: add             x0, x6, #1
    // 0xbd416c: StoreField: r2->field_13 = r0
    //     0xbd416c: stur            x0, [x2, #0x13]
    // 0xbd4170: LoadField: r0 = r5->field_13
    //     0xbd4170: ldur            w0, [x5, #0x13]
    // 0xbd4174: r1 = LoadInt32Instr(r0)
    //     0xbd4174: sbfx            x1, x0, #1, #0x1f
    // 0xbd4178: mov             x0, x1
    // 0xbd417c: mov             x1, x6
    // 0xbd4180: cmp             x1, x0
    // 0xbd4184: b.hs            #0xbd4220
    // 0xbd4188: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4188: add             x0, x5, x6
    //     0xbd418c: strb            w4, [x0, #0x17]
    // 0xbd4190: LoadField: r0 = r3->field_43
    //     0xbd4190: ldur            w0, [x3, #0x43]
    // 0xbd4194: DecompressPointer r0
    //     0xbd4194: add             x0, x0, HEAP, lsl #32
    // 0xbd4198: r16 = <String?>
    //     0xbd4198: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd419c: stp             x2, x16, [SP, #8]
    // 0xbd41a0: str             x0, [SP]
    // 0xbd41a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd41a4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd41a8: r0 = write()
    //     0xbd41a8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd41ac: r0 = Null
    //     0xbd41ac: mov             x0, NULL
    // 0xbd41b0: LeaveFrame
    //     0xbd41b0: mov             SP, fp
    //     0xbd41b4: ldp             fp, lr, [SP], #0x10
    // 0xbd41b8: ret
    //     0xbd41b8: ret             
    // 0xbd41bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd41bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd41c0: b               #0xbd3ad8
    // 0xbd41c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41d8: SaveReg d0
    //     0xbd41d8: str             q0, [SP, #-0x10]!
    // 0xbd41dc: stp             x2, x3, [SP, #-0x10]!
    // 0xbd41e0: r0 = AllocateDouble()
    //     0xbd41e0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd41e4: ldp             x2, x3, [SP], #0x10
    // 0xbd41e8: RestoreReg d0
    //     0xbd41e8: ldr             q0, [SP], #0x10
    // 0xbd41ec: b               #0xbd3d9c
    // 0xbd41f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd41f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd41f4: SaveReg d0
    //     0xbd41f4: str             q0, [SP, #-0x10]!
    // 0xbd41f8: stp             x2, x3, [SP, #-0x10]!
    // 0xbd41fc: r0 = AllocateDouble()
    //     0xbd41fc: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd4200: ldp             x2, x3, [SP], #0x10
    // 0xbd4204: RestoreReg d0
    //     0xbd4204: ldr             q0, [SP], #0x10
    // 0xbd4208: b               #0xbd3e4c
    // 0xbd420c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd420c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd4210: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd4210: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd4214: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd4214: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd4218: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd4218: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd421c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd421c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd4220: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd4220: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf01f8, size: 0x24
    // 0xbf01f8: r1 = 6
    //     0xbf01f8: movz            x1, #0x6
    // 0xbf01fc: r16 = LoadInt32Instr(r1)
    //     0xbf01fc: sbfx            x16, x1, #1, #0x1f
    // 0xbf0200: r17 = 11601
    //     0xbf0200: movz            x17, #0x2d51
    // 0xbf0204: mul             x0, x16, x17
    // 0xbf0208: umulh           x16, x16, x17
    // 0xbf020c: eor             x0, x0, x16
    // 0xbf0210: r0 = 0
    //     0xbf0210: eor             x0, x0, x0, lsr #32
    // 0xbf0214: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0218: ret
    //     0xbf0218: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd768fc, size: 0x9c
    // 0xd768fc: EnterFrame
    //     0xd768fc: stp             fp, lr, [SP, #-0x10]!
    //     0xd76900: mov             fp, SP
    // 0xd76904: AllocStack(0x10)
    //     0xd76904: sub             SP, SP, #0x10
    // 0xd76908: CheckStackOverflow
    //     0xd76908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7690c: cmp             SP, x16
    //     0xd76910: b.ls            #0xd76990
    // 0xd76914: ldr             x0, [fp, #0x10]
    // 0xd76918: cmp             w0, NULL
    // 0xd7691c: b.ne            #0xd76930
    // 0xd76920: r0 = false
    //     0xd76920: add             x0, NULL, #0x30  ; false
    // 0xd76924: LeaveFrame
    //     0xd76924: mov             SP, fp
    //     0xd76928: ldp             fp, lr, [SP], #0x10
    // 0xd7692c: ret
    //     0xd7692c: ret             
    // 0xd76930: ldr             x1, [fp, #0x18]
    // 0xd76934: cmp             w1, w0
    // 0xd76938: b.ne            #0xd76944
    // 0xd7693c: r0 = true
    //     0xd7693c: add             x0, NULL, #0x20  ; true
    // 0xd76940: b               #0xd76984
    // 0xd76944: r1 = 60
    //     0xd76944: movz            x1, #0x3c
    // 0xd76948: branchIfSmi(r0, 0xd76954)
    //     0xd76948: tbz             w0, #0, #0xd76954
    // 0xd7694c: r1 = LoadClassIdInstr(r0)
    //     0xd7694c: ldur            x1, [x0, #-1]
    //     0xd76950: ubfx            x1, x1, #0xc, #0x14
    // 0xd76954: cmp             x1, #0x678
    // 0xd76958: b.ne            #0xd76980
    // 0xd7695c: r16 = LocationAdapter
    //     0xd7695c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b318] Type: LocationAdapter
    //     0xd76960: ldr             x16, [x16, #0x318]
    // 0xd76964: r30 = LocationAdapter
    //     0xd76964: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b318] Type: LocationAdapter
    //     0xd76968: ldr             lr, [lr, #0x318]
    // 0xd7696c: stp             lr, x16, [SP]
    // 0xd76970: r0 = ==()
    //     0xd76970: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76974: tbnz            w0, #4, #0xd76980
    // 0xd76978: r0 = true
    //     0xd76978: add             x0, NULL, #0x20  ; true
    // 0xd7697c: b               #0xd76984
    // 0xd76980: r0 = false
    //     0xd76980: add             x0, NULL, #0x30  ; false
    // 0xd76984: LeaveFrame
    //     0xd76984: mov             SP, fp
    //     0xd76988: ldp             fp, lr, [SP], #0x10
    // 0xd7698c: ret
    //     0xd7698c: ret             
    // 0xd76990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76990: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76994: b               #0xd76914
  }
}
