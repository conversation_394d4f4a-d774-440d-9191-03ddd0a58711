// lib: , url: package:nuonline/app/data/models/manasik.dart

// class id: 1050031, size: 0x8
class :: {
}

// class id: 5579, size: 0x1c, field offset: 0x8
//   const constructor, 
class Manasik extends Equatable {

  factory _ Manasik.fromMap(/* No info */) {
    // ** addr: 0x8fcd68, size: 0x514
    // 0x8fcd68: EnterFrame
    //     0x8fcd68: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcd6c: mov             fp, SP
    // 0x8fcd70: AllocStack(0x40)
    //     0x8fcd70: sub             SP, SP, #0x40
    // 0x8fcd74: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x8fcd74: mov             x0, x2
    //     0x8fcd78: stur            x2, [fp, #-8]
    // 0x8fcd7c: CheckStackOverflow
    //     0x8fcd7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcd80: cmp             SP, x16
    //     0x8fcd84: b.ls            #0x8fd274
    // 0x8fcd88: mov             x1, x0
    // 0x8fcd8c: r2 = "id"
    //     0x8fcd8c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fcd90: ldr             x2, [x2, #0x740]
    // 0x8fcd94: r0 = _getValueOrData()
    //     0x8fcd94: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fcd98: ldur            x3, [fp, #-8]
    // 0x8fcd9c: LoadField: r1 = r3->field_f
    //     0x8fcd9c: ldur            w1, [x3, #0xf]
    // 0x8fcda0: DecompressPointer r1
    //     0x8fcda0: add             x1, x1, HEAP, lsl #32
    // 0x8fcda4: cmp             w1, w0
    // 0x8fcda8: b.ne            #0x8fcdb4
    // 0x8fcdac: r4 = Null
    //     0x8fcdac: mov             x4, NULL
    // 0x8fcdb0: b               #0x8fcdb8
    // 0x8fcdb4: mov             x4, x0
    // 0x8fcdb8: mov             x0, x4
    // 0x8fcdbc: stur            x4, [fp, #-0x10]
    // 0x8fcdc0: r2 = Null
    //     0x8fcdc0: mov             x2, NULL
    // 0x8fcdc4: r1 = Null
    //     0x8fcdc4: mov             x1, NULL
    // 0x8fcdc8: branchIfSmi(r0, 0x8fcdf0)
    //     0x8fcdc8: tbz             w0, #0, #0x8fcdf0
    // 0x8fcdcc: r4 = LoadClassIdInstr(r0)
    //     0x8fcdcc: ldur            x4, [x0, #-1]
    //     0x8fcdd0: ubfx            x4, x4, #0xc, #0x14
    // 0x8fcdd4: sub             x4, x4, #0x3c
    // 0x8fcdd8: cmp             x4, #1
    // 0x8fcddc: b.ls            #0x8fcdf0
    // 0x8fcde0: r8 = int
    //     0x8fcde0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fcde4: r3 = Null
    //     0x8fcde4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e858] Null
    //     0x8fcde8: ldr             x3, [x3, #0x858]
    // 0x8fcdec: r0 = int()
    //     0x8fcdec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fcdf0: ldur            x1, [fp, #-8]
    // 0x8fcdf4: r2 = "title"
    //     0x8fcdf4: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8fcdf8: ldr             x2, [x2, #0x748]
    // 0x8fcdfc: r0 = _getValueOrData()
    //     0x8fcdfc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fce00: ldur            x3, [fp, #-8]
    // 0x8fce04: LoadField: r1 = r3->field_f
    //     0x8fce04: ldur            w1, [x3, #0xf]
    // 0x8fce08: DecompressPointer r1
    //     0x8fce08: add             x1, x1, HEAP, lsl #32
    // 0x8fce0c: cmp             w1, w0
    // 0x8fce10: b.ne            #0x8fce18
    // 0x8fce14: r0 = Null
    //     0x8fce14: mov             x0, NULL
    // 0x8fce18: r2 = Null
    //     0x8fce18: mov             x2, NULL
    // 0x8fce1c: r1 = Null
    //     0x8fce1c: mov             x1, NULL
    // 0x8fce20: r4 = 60
    //     0x8fce20: movz            x4, #0x3c
    // 0x8fce24: branchIfSmi(r0, 0x8fce30)
    //     0x8fce24: tbz             w0, #0, #0x8fce30
    // 0x8fce28: r4 = LoadClassIdInstr(r0)
    //     0x8fce28: ldur            x4, [x0, #-1]
    //     0x8fce2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fce30: sub             x4, x4, #0x5e
    // 0x8fce34: cmp             x4, #1
    // 0x8fce38: b.ls            #0x8fce4c
    // 0x8fce3c: r8 = String
    //     0x8fce3c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fce40: r3 = Null
    //     0x8fce40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e868] Null
    //     0x8fce44: ldr             x3, [x3, #0x868]
    // 0x8fce48: r0 = String()
    //     0x8fce48: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fce4c: ldur            x1, [fp, #-8]
    // 0x8fce50: r2 = "slug"
    //     0x8fce50: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x8fce54: ldr             x2, [x2, #0x9a8]
    // 0x8fce58: r0 = _getValueOrData()
    //     0x8fce58: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fce5c: ldur            x3, [fp, #-8]
    // 0x8fce60: LoadField: r1 = r3->field_f
    //     0x8fce60: ldur            w1, [x3, #0xf]
    // 0x8fce64: DecompressPointer r1
    //     0x8fce64: add             x1, x1, HEAP, lsl #32
    // 0x8fce68: cmp             w1, w0
    // 0x8fce6c: b.ne            #0x8fce74
    // 0x8fce70: r0 = Null
    //     0x8fce70: mov             x0, NULL
    // 0x8fce74: r2 = Null
    //     0x8fce74: mov             x2, NULL
    // 0x8fce78: r1 = Null
    //     0x8fce78: mov             x1, NULL
    // 0x8fce7c: r4 = 60
    //     0x8fce7c: movz            x4, #0x3c
    // 0x8fce80: branchIfSmi(r0, 0x8fce8c)
    //     0x8fce80: tbz             w0, #0, #0x8fce8c
    // 0x8fce84: r4 = LoadClassIdInstr(r0)
    //     0x8fce84: ldur            x4, [x0, #-1]
    //     0x8fce88: ubfx            x4, x4, #0xc, #0x14
    // 0x8fce8c: sub             x4, x4, #0x5e
    // 0x8fce90: cmp             x4, #1
    // 0x8fce94: b.ls            #0x8fcea8
    // 0x8fce98: r8 = String
    //     0x8fce98: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fce9c: r3 = Null
    //     0x8fce9c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e878] Null
    //     0x8fcea0: ldr             x3, [x3, #0x878]
    // 0x8fcea4: r0 = String()
    //     0x8fcea4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fcea8: ldur            x1, [fp, #-8]
    // 0x8fceac: r2 = "image"
    //     0x8fceac: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x8fceb0: ldr             x2, [x2, #0x520]
    // 0x8fceb4: r0 = _getValueOrData()
    //     0x8fceb4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fceb8: mov             x1, x0
    // 0x8fcebc: ldur            x0, [fp, #-8]
    // 0x8fcec0: LoadField: r2 = r0->field_f
    //     0x8fcec0: ldur            w2, [x0, #0xf]
    // 0x8fcec4: DecompressPointer r2
    //     0x8fcec4: add             x2, x2, HEAP, lsl #32
    // 0x8fcec8: cmp             w2, w1
    // 0x8fcecc: b.eq            #0x8fcf38
    // 0x8fced0: cmp             w1, NULL
    // 0x8fced4: b.eq            #0x8fcf38
    // 0x8fced8: mov             x1, x0
    // 0x8fcedc: r2 = "image"
    //     0x8fcedc: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x8fcee0: ldr             x2, [x2, #0x520]
    // 0x8fcee4: r0 = _getValueOrData()
    //     0x8fcee4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fcee8: ldur            x3, [fp, #-8]
    // 0x8fceec: LoadField: r1 = r3->field_f
    //     0x8fceec: ldur            w1, [x3, #0xf]
    // 0x8fcef0: DecompressPointer r1
    //     0x8fcef0: add             x1, x1, HEAP, lsl #32
    // 0x8fcef4: cmp             w1, w0
    // 0x8fcef8: b.ne            #0x8fcf04
    // 0x8fcefc: r4 = Null
    //     0x8fcefc: mov             x4, NULL
    // 0x8fcf00: b               #0x8fcf08
    // 0x8fcf04: mov             x4, x0
    // 0x8fcf08: mov             x0, x4
    // 0x8fcf0c: stur            x4, [fp, #-0x18]
    // 0x8fcf10: r2 = Null
    //     0x8fcf10: mov             x2, NULL
    // 0x8fcf14: r1 = Null
    //     0x8fcf14: mov             x1, NULL
    // 0x8fcf18: r8 = Map<String, dynamic>
    //     0x8fcf18: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fcf1c: r3 = Null
    //     0x8fcf1c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e888] Null
    //     0x8fcf20: ldr             x3, [x3, #0x888]
    // 0x8fcf24: r0 = Map<String, dynamic>()
    //     0x8fcf24: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fcf28: ldur            x2, [fp, #-0x18]
    // 0x8fcf2c: r1 = Null
    //     0x8fcf2c: mov             x1, NULL
    // 0x8fcf30: r0 = Image.fromMap()
    //     0x8fcf30: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x8fcf34: ldur            x0, [fp, #-8]
    // 0x8fcf38: mov             x1, x0
    // 0x8fcf3c: r2 = "content"
    //     0x8fcf3c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x8fcf40: ldr             x2, [x2, #0xf58]
    // 0x8fcf44: r0 = _getValueOrData()
    //     0x8fcf44: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fcf48: ldur            x3, [fp, #-8]
    // 0x8fcf4c: LoadField: r1 = r3->field_f
    //     0x8fcf4c: ldur            w1, [x3, #0xf]
    // 0x8fcf50: DecompressPointer r1
    //     0x8fcf50: add             x1, x1, HEAP, lsl #32
    // 0x8fcf54: cmp             w1, w0
    // 0x8fcf58: b.ne            #0x8fcf64
    // 0x8fcf5c: r4 = Null
    //     0x8fcf5c: mov             x4, NULL
    // 0x8fcf60: b               #0x8fcf68
    // 0x8fcf64: mov             x4, x0
    // 0x8fcf68: mov             x0, x4
    // 0x8fcf6c: stur            x4, [fp, #-0x18]
    // 0x8fcf70: r2 = Null
    //     0x8fcf70: mov             x2, NULL
    // 0x8fcf74: r1 = Null
    //     0x8fcf74: mov             x1, NULL
    // 0x8fcf78: r4 = 60
    //     0x8fcf78: movz            x4, #0x3c
    // 0x8fcf7c: branchIfSmi(r0, 0x8fcf88)
    //     0x8fcf7c: tbz             w0, #0, #0x8fcf88
    // 0x8fcf80: r4 = LoadClassIdInstr(r0)
    //     0x8fcf80: ldur            x4, [x0, #-1]
    //     0x8fcf84: ubfx            x4, x4, #0xc, #0x14
    // 0x8fcf88: sub             x4, x4, #0x5e
    // 0x8fcf8c: cmp             x4, #1
    // 0x8fcf90: b.ls            #0x8fcfa4
    // 0x8fcf94: r8 = String
    //     0x8fcf94: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fcf98: r3 = Null
    //     0x8fcf98: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e898] Null
    //     0x8fcf9c: ldr             x3, [x3, #0x898]
    // 0x8fcfa0: r0 = String()
    //     0x8fcfa0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fcfa4: ldur            x1, [fp, #-8]
    // 0x8fcfa8: r2 = "updated_at"
    //     0x8fcfa8: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x8fcfac: ldr             x2, [x2, #0xe88]
    // 0x8fcfb0: r0 = _getValueOrData()
    //     0x8fcfb0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fcfb4: ldur            x3, [fp, #-8]
    // 0x8fcfb8: LoadField: r1 = r3->field_f
    //     0x8fcfb8: ldur            w1, [x3, #0xf]
    // 0x8fcfbc: DecompressPointer r1
    //     0x8fcfbc: add             x1, x1, HEAP, lsl #32
    // 0x8fcfc0: cmp             w1, w0
    // 0x8fcfc4: b.ne            #0x8fcfcc
    // 0x8fcfc8: r0 = Null
    //     0x8fcfc8: mov             x0, NULL
    // 0x8fcfcc: r2 = Null
    //     0x8fcfcc: mov             x2, NULL
    // 0x8fcfd0: r1 = Null
    //     0x8fcfd0: mov             x1, NULL
    // 0x8fcfd4: r4 = 60
    //     0x8fcfd4: movz            x4, #0x3c
    // 0x8fcfd8: branchIfSmi(r0, 0x8fcfe4)
    //     0x8fcfd8: tbz             w0, #0, #0x8fcfe4
    // 0x8fcfdc: r4 = LoadClassIdInstr(r0)
    //     0x8fcfdc: ldur            x4, [x0, #-1]
    //     0x8fcfe0: ubfx            x4, x4, #0xc, #0x14
    // 0x8fcfe4: sub             x4, x4, #0x5e
    // 0x8fcfe8: cmp             x4, #1
    // 0x8fcfec: b.ls            #0x8fd000
    // 0x8fcff0: r8 = String
    //     0x8fcff0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fcff4: r3 = Null
    //     0x8fcff4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e8a8] Null
    //     0x8fcff8: ldr             x3, [x3, #0x8a8]
    // 0x8fcffc: r0 = String()
    //     0x8fcffc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fd000: ldur            x1, [fp, #-8]
    // 0x8fd004: r2 = "articles"
    //     0x8fd004: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0x8fd008: ldr             x2, [x2, #0x8b8]
    // 0x8fd00c: r0 = _getValueOrData()
    //     0x8fd00c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fd010: ldur            x3, [fp, #-8]
    // 0x8fd014: LoadField: r1 = r3->field_f
    //     0x8fd014: ldur            w1, [x3, #0xf]
    // 0x8fd018: DecompressPointer r1
    //     0x8fd018: add             x1, x1, HEAP, lsl #32
    // 0x8fd01c: cmp             w1, w0
    // 0x8fd020: b.ne            #0x8fd02c
    // 0x8fd024: r4 = Null
    //     0x8fd024: mov             x4, NULL
    // 0x8fd028: b               #0x8fd030
    // 0x8fd02c: mov             x4, x0
    // 0x8fd030: mov             x0, x4
    // 0x8fd034: stur            x4, [fp, #-0x20]
    // 0x8fd038: r2 = Null
    //     0x8fd038: mov             x2, NULL
    // 0x8fd03c: r1 = Null
    //     0x8fd03c: mov             x1, NULL
    // 0x8fd040: r4 = 60
    //     0x8fd040: movz            x4, #0x3c
    // 0x8fd044: branchIfSmi(r0, 0x8fd050)
    //     0x8fd044: tbz             w0, #0, #0x8fd050
    // 0x8fd048: r4 = LoadClassIdInstr(r0)
    //     0x8fd048: ldur            x4, [x0, #-1]
    //     0x8fd04c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd050: sub             x4, x4, #0x5a
    // 0x8fd054: cmp             x4, #2
    // 0x8fd058: b.ls            #0x8fd070
    // 0x8fd05c: r8 = List?
    //     0x8fd05c: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8fd060: ldr             x8, [x8, #0x140]
    // 0x8fd064: r3 = Null
    //     0x8fd064: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e8c0] Null
    //     0x8fd068: ldr             x3, [x3, #0x8c0]
    // 0x8fd06c: r0 = List?()
    //     0x8fd06c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8fd070: ldur            x0, [fp, #-0x20]
    // 0x8fd074: cmp             w0, NULL
    // 0x8fd078: b.ne            #0x8fd090
    // 0x8fd07c: r1 = Null
    //     0x8fd07c: mov             x1, NULL
    // 0x8fd080: r2 = 0
    //     0x8fd080: movz            x2, #0
    // 0x8fd084: r0 = _GrowableList()
    //     0x8fd084: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fd088: mov             x3, x0
    // 0x8fd08c: b               #0x8fd094
    // 0x8fd090: mov             x3, x0
    // 0x8fd094: ldur            x0, [fp, #-8]
    // 0x8fd098: stur            x3, [fp, #-0x20]
    // 0x8fd09c: r1 = Function '<anonymous closure>': static.
    //     0x8fd09c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e8d0] AnonymousClosure: static (0x8fd2e0), in [package:nuonline/app/data/models/manasik.dart] Manasik::Manasik.fromMap (0x8fcd68)
    //     0x8fd0a0: ldr             x1, [x1, #0x8d0]
    // 0x8fd0a4: r2 = Null
    //     0x8fd0a4: mov             x2, NULL
    // 0x8fd0a8: r0 = AllocateClosure()
    //     0x8fd0a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fd0ac: mov             x1, x0
    // 0x8fd0b0: ldur            x0, [fp, #-0x20]
    // 0x8fd0b4: r2 = LoadClassIdInstr(r0)
    //     0x8fd0b4: ldur            x2, [x0, #-1]
    //     0x8fd0b8: ubfx            x2, x2, #0xc, #0x14
    // 0x8fd0bc: r16 = <RelatedContent<int>>
    //     0x8fd0bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e808] TypeArguments: <RelatedContent<int>>
    //     0x8fd0c0: ldr             x16, [x16, #0x808]
    // 0x8fd0c4: stp             x0, x16, [SP, #8]
    // 0x8fd0c8: str             x1, [SP]
    // 0x8fd0cc: mov             x0, x2
    // 0x8fd0d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fd0d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fd0d4: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fd0d4: movz            x17, #0xf28c
    //     0x8fd0d8: add             lr, x0, x17
    //     0x8fd0dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd0e0: blr             lr
    // 0x8fd0e4: r1 = LoadClassIdInstr(r0)
    //     0x8fd0e4: ldur            x1, [x0, #-1]
    //     0x8fd0e8: ubfx            x1, x1, #0xc, #0x14
    // 0x8fd0ec: mov             x16, x0
    // 0x8fd0f0: mov             x0, x1
    // 0x8fd0f4: mov             x1, x16
    // 0x8fd0f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fd0f8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fd0fc: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fd0fc: movz            x17, #0xd889
    //     0x8fd100: add             lr, x0, x17
    //     0x8fd104: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd108: blr             lr
    // 0x8fd10c: ldur            x1, [fp, #-8]
    // 0x8fd110: r2 = "videos"
    //     0x8fd110: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0x8fd114: ldr             x2, [x2, #0x8d8]
    // 0x8fd118: stur            x0, [fp, #-0x20]
    // 0x8fd11c: r0 = _getValueOrData()
    //     0x8fd11c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fd120: mov             x1, x0
    // 0x8fd124: ldur            x0, [fp, #-8]
    // 0x8fd128: LoadField: r2 = r0->field_f
    //     0x8fd128: ldur            w2, [x0, #0xf]
    // 0x8fd12c: DecompressPointer r2
    //     0x8fd12c: add             x2, x2, HEAP, lsl #32
    // 0x8fd130: cmp             w2, w1
    // 0x8fd134: b.ne            #0x8fd140
    // 0x8fd138: r3 = Null
    //     0x8fd138: mov             x3, NULL
    // 0x8fd13c: b               #0x8fd144
    // 0x8fd140: mov             x3, x1
    // 0x8fd144: mov             x0, x3
    // 0x8fd148: stur            x3, [fp, #-8]
    // 0x8fd14c: r2 = Null
    //     0x8fd14c: mov             x2, NULL
    // 0x8fd150: r1 = Null
    //     0x8fd150: mov             x1, NULL
    // 0x8fd154: r4 = 60
    //     0x8fd154: movz            x4, #0x3c
    // 0x8fd158: branchIfSmi(r0, 0x8fd164)
    //     0x8fd158: tbz             w0, #0, #0x8fd164
    // 0x8fd15c: r4 = LoadClassIdInstr(r0)
    //     0x8fd15c: ldur            x4, [x0, #-1]
    //     0x8fd160: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd164: sub             x4, x4, #0x5a
    // 0x8fd168: cmp             x4, #2
    // 0x8fd16c: b.ls            #0x8fd184
    // 0x8fd170: r8 = List?
    //     0x8fd170: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8fd174: ldr             x8, [x8, #0x140]
    // 0x8fd178: r3 = Null
    //     0x8fd178: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e8e0] Null
    //     0x8fd17c: ldr             x3, [x3, #0x8e0]
    // 0x8fd180: r0 = List?()
    //     0x8fd180: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8fd184: ldur            x0, [fp, #-8]
    // 0x8fd188: cmp             w0, NULL
    // 0x8fd18c: b.ne            #0x8fd1a4
    // 0x8fd190: r1 = Null
    //     0x8fd190: mov             x1, NULL
    // 0x8fd194: r2 = 0
    //     0x8fd194: movz            x2, #0
    // 0x8fd198: r0 = _GrowableList()
    //     0x8fd198: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fd19c: mov             x5, x0
    // 0x8fd1a0: b               #0x8fd1a8
    // 0x8fd1a4: mov             x5, x0
    // 0x8fd1a8: ldur            x0, [fp, #-0x20]
    // 0x8fd1ac: ldur            x3, [fp, #-0x18]
    // 0x8fd1b0: ldur            x4, [fp, #-0x10]
    // 0x8fd1b4: stur            x5, [fp, #-8]
    // 0x8fd1b8: r1 = Function '<anonymous closure>': static.
    //     0x8fd1b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e8f0] AnonymousClosure: static (0x8fd288), in [package:nuonline/app/data/models/manasik.dart] Manasik::Manasik.fromMap (0x8fcd68)
    //     0x8fd1bc: ldr             x1, [x1, #0x8f0]
    // 0x8fd1c0: r2 = Null
    //     0x8fd1c0: mov             x2, NULL
    // 0x8fd1c4: r0 = AllocateClosure()
    //     0x8fd1c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8fd1c8: mov             x1, x0
    // 0x8fd1cc: ldur            x0, [fp, #-8]
    // 0x8fd1d0: r2 = LoadClassIdInstr(r0)
    //     0x8fd1d0: ldur            x2, [x0, #-1]
    //     0x8fd1d4: ubfx            x2, x2, #0xc, #0x14
    // 0x8fd1d8: r16 = <RelatedContent<String>>
    //     0x8fd1d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e810] TypeArguments: <RelatedContent<String>>
    //     0x8fd1dc: ldr             x16, [x16, #0x810]
    // 0x8fd1e0: stp             x0, x16, [SP, #8]
    // 0x8fd1e4: str             x1, [SP]
    // 0x8fd1e8: mov             x0, x2
    // 0x8fd1ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fd1ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fd1f0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fd1f0: movz            x17, #0xf28c
    //     0x8fd1f4: add             lr, x0, x17
    //     0x8fd1f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd1fc: blr             lr
    // 0x8fd200: r1 = LoadClassIdInstr(r0)
    //     0x8fd200: ldur            x1, [x0, #-1]
    //     0x8fd204: ubfx            x1, x1, #0xc, #0x14
    // 0x8fd208: mov             x16, x0
    // 0x8fd20c: mov             x0, x1
    // 0x8fd210: mov             x1, x16
    // 0x8fd214: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fd214: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fd218: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fd218: movz            x17, #0xd889
    //     0x8fd21c: add             lr, x0, x17
    //     0x8fd220: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd224: blr             lr
    // 0x8fd228: mov             x1, x0
    // 0x8fd22c: ldur            x0, [fp, #-0x10]
    // 0x8fd230: stur            x1, [fp, #-8]
    // 0x8fd234: r2 = LoadInt32Instr(r0)
    //     0x8fd234: sbfx            x2, x0, #1, #0x1f
    //     0x8fd238: tbz             w0, #0, #0x8fd240
    //     0x8fd23c: ldur            x2, [x0, #7]
    // 0x8fd240: stur            x2, [fp, #-0x28]
    // 0x8fd244: r0 = Manasik()
    //     0x8fd244: bl              #0x8fd27c  ; AllocateManasikStub -> Manasik (size=0x1c)
    // 0x8fd248: ldur            x1, [fp, #-0x28]
    // 0x8fd24c: StoreField: r0->field_7 = r1
    //     0x8fd24c: stur            x1, [x0, #7]
    // 0x8fd250: ldur            x1, [fp, #-0x18]
    // 0x8fd254: StoreField: r0->field_f = r1
    //     0x8fd254: stur            w1, [x0, #0xf]
    // 0x8fd258: ldur            x1, [fp, #-0x20]
    // 0x8fd25c: StoreField: r0->field_13 = r1
    //     0x8fd25c: stur            w1, [x0, #0x13]
    // 0x8fd260: ldur            x1, [fp, #-8]
    // 0x8fd264: ArrayStore: r0[0] = r1  ; List_4
    //     0x8fd264: stur            w1, [x0, #0x17]
    // 0x8fd268: LeaveFrame
    //     0x8fd268: mov             SP, fp
    //     0x8fd26c: ldp             fp, lr, [SP], #0x10
    // 0x8fd270: ret
    //     0x8fd270: ret             
    // 0x8fd274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd278: b               #0x8fcd88
  }
  [closure] static RelatedContent<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fd288, size: 0x58
    // 0x8fd288: EnterFrame
    //     0x8fd288: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd28c: mov             fp, SP
    // 0x8fd290: CheckStackOverflow
    //     0x8fd290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd294: cmp             SP, x16
    //     0x8fd298: b.ls            #0x8fd2d8
    // 0x8fd29c: ldr             x0, [fp, #0x10]
    // 0x8fd2a0: r2 = Null
    //     0x8fd2a0: mov             x2, NULL
    // 0x8fd2a4: r1 = Null
    //     0x8fd2a4: mov             x1, NULL
    // 0x8fd2a8: r8 = Map<String, dynamic>
    //     0x8fd2a8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fd2ac: r3 = Null
    //     0x8fd2ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e8f8] Null
    //     0x8fd2b0: ldr             x3, [x3, #0x8f8]
    // 0x8fd2b4: r0 = Map<String, dynamic>()
    //     0x8fd2b4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fd2b8: ldr             x2, [fp, #0x10]
    // 0x8fd2bc: r1 = <String>
    //     0x8fd2bc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8fd2c0: r3 = "youtube_id"
    //     0x8fd2c0: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x8fd2c4: ldr             x3, [x3, #0x3f0]
    // 0x8fd2c8: r0 = RelatedContent.fromMap()
    //     0x8fd2c8: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x8fd2cc: LeaveFrame
    //     0x8fd2cc: mov             SP, fp
    //     0x8fd2d0: ldp             fp, lr, [SP], #0x10
    // 0x8fd2d4: ret
    //     0x8fd2d4: ret             
    // 0x8fd2d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd2d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd2dc: b               #0x8fd29c
  }
  [closure] static RelatedContent<int> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fd2e0, size: 0x58
    // 0x8fd2e0: EnterFrame
    //     0x8fd2e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd2e4: mov             fp, SP
    // 0x8fd2e8: CheckStackOverflow
    //     0x8fd2e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd2ec: cmp             SP, x16
    //     0x8fd2f0: b.ls            #0x8fd330
    // 0x8fd2f4: ldr             x0, [fp, #0x10]
    // 0x8fd2f8: r2 = Null
    //     0x8fd2f8: mov             x2, NULL
    // 0x8fd2fc: r1 = Null
    //     0x8fd2fc: mov             x1, NULL
    // 0x8fd300: r8 = Map<String, dynamic>
    //     0x8fd300: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fd304: r3 = Null
    //     0x8fd304: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e948] Null
    //     0x8fd308: ldr             x3, [x3, #0x948]
    // 0x8fd30c: r0 = Map<String, dynamic>()
    //     0x8fd30c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fd310: ldr             x2, [fp, #0x10]
    // 0x8fd314: r1 = <int>
    //     0x8fd314: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x8fd318: r3 = "article_id"
    //     0x8fd318: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0x8fd31c: ldr             x3, [x3, #0x958]
    // 0x8fd320: r0 = RelatedContent.fromMap()
    //     0x8fd320: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0x8fd324: LeaveFrame
    //     0x8fd324: mov             SP, fp
    //     0x8fd328: ldp             fp, lr, [SP], #0x10
    // 0x8fd32c: ret
    //     0x8fd32c: ret             
    // 0x8fd330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd334: b               #0x8fd2f4
  }
}
