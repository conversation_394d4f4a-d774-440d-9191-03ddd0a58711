// lib: , url: package:nuonline/app/data/models/menu.dart

// class id: 1050032, size: 0x8
class :: {
}

// class id: 1605, size: 0x2c, field offset: 0x14
class Menu extends HiveObject {

  String dyn:get:label(Menu) {
    // ** addr: 0x7dacd4, size: 0x28
    // 0x7dacd4: ldr             x1, [SP]
    // 0x7dacd8: LoadField: r0 = r1->field_1b
    //     0x7dacd8: ldur            w0, [x1, #0x1b]
    // 0x7dacdc: DecompressPointer r0
    //     0x7dacdc: add             x0, x0, HEAP, lsl #32
    // 0x7dace0: ret
    //     0x7dace0: ret             
  }
  _ Menu(/* No info */) {
    // ** addr: 0x7ec598, size: 0x130
    // 0x7ec598: EnterFrame
    //     0x7ec598: stp             fp, lr, [SP, #-0x10]!
    //     0x7ec59c: mov             fp, SP
    // 0x7ec5a0: AllocStack(0x18)
    //     0x7ec5a0: sub             SP, SP, #0x18
    // 0x7ec5a4: SetupParameters(Menu this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0 */, dynamic _ /* r6 => r1 */, {dynamic params = Null /* r4 */})
    //     0x7ec5a4: mov             x0, x3
    //     0x7ec5a8: mov             x3, x1
    //     0x7ec5ac: stur            x1, [fp, #-8]
    //     0x7ec5b0: mov             x1, x6
    //     0x7ec5b4: ldur            w6, [x4, #0x13]
    //     0x7ec5b8: ldur            w7, [x4, #0x1f]
    //     0x7ec5bc: add             x7, x7, HEAP, lsl #32
    //     0x7ec5c0: add             x16, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x7ec5c4: ldr             x16, [x16, #0x3b8]
    //     0x7ec5c8: cmp             w7, w16
    //     0x7ec5cc: b.ne            #0x7ec5ec
    //     0x7ec5d0: ldur            w7, [x4, #0x23]
    //     0x7ec5d4: add             x7, x7, HEAP, lsl #32
    //     0x7ec5d8: sub             w4, w6, w7
    //     0x7ec5dc: add             x6, fp, w4, sxtw #2
    //     0x7ec5e0: ldr             x6, [x6, #8]
    //     0x7ec5e4: mov             x4, x6
    //     0x7ec5e8: b               #0x7ec5f0
    //     0x7ec5ec: mov             x4, NULL
    // 0x7ec5f0: CheckStackOverflow
    //     0x7ec5f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ec5f4: cmp             SP, x16
    //     0x7ec5f8: b.ls            #0x7ec6c0
    // 0x7ec5fc: StoreField: r3->field_13 = r5
    //     0x7ec5fc: stur            x5, [x3, #0x13]
    // 0x7ec600: StoreField: r3->field_1b = r0
    //     0x7ec600: stur            w0, [x3, #0x1b]
    //     0x7ec604: ldurb           w16, [x3, #-1]
    //     0x7ec608: ldurb           w17, [x0, #-1]
    //     0x7ec60c: and             x16, x17, x16, lsr #2
    //     0x7ec610: tst             x16, HEAP, lsr #32
    //     0x7ec614: b.eq            #0x7ec61c
    //     0x7ec618: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7ec61c: mov             x0, x2
    // 0x7ec620: StoreField: r3->field_1f = r0
    //     0x7ec620: stur            w0, [x3, #0x1f]
    //     0x7ec624: ldurb           w16, [x3, #-1]
    //     0x7ec628: ldurb           w17, [x0, #-1]
    //     0x7ec62c: and             x16, x17, x16, lsr #2
    //     0x7ec630: tst             x16, HEAP, lsr #32
    //     0x7ec634: b.eq            #0x7ec63c
    //     0x7ec638: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7ec63c: mov             x0, x1
    // 0x7ec640: StoreField: r3->field_23 = r0
    //     0x7ec640: stur            w0, [x3, #0x23]
    //     0x7ec644: ldurb           w16, [x3, #-1]
    //     0x7ec648: ldurb           w17, [x0, #-1]
    //     0x7ec64c: and             x16, x17, x16, lsr #2
    //     0x7ec650: tst             x16, HEAP, lsr #32
    //     0x7ec654: b.eq            #0x7ec65c
    //     0x7ec658: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7ec65c: mov             x0, x4
    // 0x7ec660: StoreField: r3->field_27 = r0
    //     0x7ec660: stur            w0, [x3, #0x27]
    //     0x7ec664: ldurb           w16, [x3, #-1]
    //     0x7ec668: ldurb           w17, [x0, #-1]
    //     0x7ec66c: and             x16, x17, x16, lsr #2
    //     0x7ec670: tst             x16, HEAP, lsr #32
    //     0x7ec674: b.eq            #0x7ec67c
    //     0x7ec678: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7ec67c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x7ec67c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x7ec680: ldr             x16, [x16, #0x9f8]
    // 0x7ec684: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7ec688: stp             lr, x16, [SP]
    // 0x7ec68c: r0 = Map._fromLiteral()
    //     0x7ec68c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7ec690: ldur            x1, [fp, #-8]
    // 0x7ec694: StoreField: r1->field_f = r0
    //     0x7ec694: stur            w0, [x1, #0xf]
    //     0x7ec698: ldurb           w16, [x1, #-1]
    //     0x7ec69c: ldurb           w17, [x0, #-1]
    //     0x7ec6a0: and             x16, x17, x16, lsr #2
    //     0x7ec6a4: tst             x16, HEAP, lsr #32
    //     0x7ec6a8: b.eq            #0x7ec6b0
    //     0x7ec6ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7ec6b0: r0 = Null
    //     0x7ec6b0: mov             x0, NULL
    // 0x7ec6b4: LeaveFrame
    //     0x7ec6b4: mov             SP, fp
    //     0x7ec6b8: ldp             fp, lr, [SP], #0x10
    // 0x7ec6bc: ret
    //     0x7ec6bc: ret             
    // 0x7ec6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec6c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec6c4: b               #0x7ec5fc
  }
  factory _ Menu.fromMap(/* No info */) {
    // ** addr: 0x7ef7a0, size: 0x2c0
    // 0x7ef7a0: EnterFrame
    //     0x7ef7a0: stp             fp, lr, [SP, #-0x10]!
    //     0x7ef7a4: mov             fp, SP
    // 0x7ef7a8: AllocStack(0x40)
    //     0x7ef7a8: sub             SP, SP, #0x40
    // 0x7ef7ac: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7ef7ac: mov             x3, x2
    //     0x7ef7b0: stur            x2, [fp, #-8]
    // 0x7ef7b4: CheckStackOverflow
    //     0x7ef7b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ef7b8: cmp             SP, x16
    //     0x7ef7bc: b.ls            #0x7efa58
    // 0x7ef7c0: r0 = LoadClassIdInstr(r3)
    //     0x7ef7c0: ldur            x0, [x3, #-1]
    //     0x7ef7c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7ef7c8: mov             x1, x3
    // 0x7ef7cc: r2 = "order"
    //     0x7ef7cc: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x7ef7d0: ldr             x2, [x2, #0xb78]
    // 0x7ef7d4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ef7d4: sub             lr, x0, #0x114
    //     0x7ef7d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef7dc: blr             lr
    // 0x7ef7e0: mov             x3, x0
    // 0x7ef7e4: r2 = Null
    //     0x7ef7e4: mov             x2, NULL
    // 0x7ef7e8: r1 = Null
    //     0x7ef7e8: mov             x1, NULL
    // 0x7ef7ec: stur            x3, [fp, #-0x10]
    // 0x7ef7f0: branchIfSmi(r0, 0x7ef818)
    //     0x7ef7f0: tbz             w0, #0, #0x7ef818
    // 0x7ef7f4: r4 = LoadClassIdInstr(r0)
    //     0x7ef7f4: ldur            x4, [x0, #-1]
    //     0x7ef7f8: ubfx            x4, x4, #0xc, #0x14
    // 0x7ef7fc: sub             x4, x4, #0x3c
    // 0x7ef800: cmp             x4, #1
    // 0x7ef804: b.ls            #0x7ef818
    // 0x7ef808: r8 = int
    //     0x7ef808: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7ef80c: r3 = Null
    //     0x7ef80c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d660] Null
    //     0x7ef810: ldr             x3, [x3, #0x660]
    // 0x7ef814: r0 = int()
    //     0x7ef814: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7ef818: ldur            x3, [fp, #-8]
    // 0x7ef81c: r0 = LoadClassIdInstr(r3)
    //     0x7ef81c: ldur            x0, [x3, #-1]
    //     0x7ef820: ubfx            x0, x0, #0xc, #0x14
    // 0x7ef824: mov             x1, x3
    // 0x7ef828: r2 = "label"
    //     0x7ef828: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb80] "label"
    //     0x7ef82c: ldr             x2, [x2, #0xb80]
    // 0x7ef830: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ef830: sub             lr, x0, #0x114
    //     0x7ef834: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef838: blr             lr
    // 0x7ef83c: mov             x3, x0
    // 0x7ef840: r2 = Null
    //     0x7ef840: mov             x2, NULL
    // 0x7ef844: r1 = Null
    //     0x7ef844: mov             x1, NULL
    // 0x7ef848: stur            x3, [fp, #-0x18]
    // 0x7ef84c: r4 = 60
    //     0x7ef84c: movz            x4, #0x3c
    // 0x7ef850: branchIfSmi(r0, 0x7ef85c)
    //     0x7ef850: tbz             w0, #0, #0x7ef85c
    // 0x7ef854: r4 = LoadClassIdInstr(r0)
    //     0x7ef854: ldur            x4, [x0, #-1]
    //     0x7ef858: ubfx            x4, x4, #0xc, #0x14
    // 0x7ef85c: sub             x4, x4, #0x5e
    // 0x7ef860: cmp             x4, #1
    // 0x7ef864: b.ls            #0x7ef878
    // 0x7ef868: r8 = String
    //     0x7ef868: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ef86c: r3 = Null
    //     0x7ef86c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d670] Null
    //     0x7ef870: ldr             x3, [x3, #0x670]
    // 0x7ef874: r0 = String()
    //     0x7ef874: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ef878: ldur            x3, [fp, #-8]
    // 0x7ef87c: r0 = LoadClassIdInstr(r3)
    //     0x7ef87c: ldur            x0, [x3, #-1]
    //     0x7ef880: ubfx            x0, x0, #0xc, #0x14
    // 0x7ef884: mov             x1, x3
    // 0x7ef888: r2 = "icon"
    //     0x7ef888: add             x2, PP, #8, lsl #12  ; [pp+0x8828] "icon"
    //     0x7ef88c: ldr             x2, [x2, #0x828]
    // 0x7ef890: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ef890: sub             lr, x0, #0x114
    //     0x7ef894: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef898: blr             lr
    // 0x7ef89c: mov             x3, x0
    // 0x7ef8a0: r2 = Null
    //     0x7ef8a0: mov             x2, NULL
    // 0x7ef8a4: r1 = Null
    //     0x7ef8a4: mov             x1, NULL
    // 0x7ef8a8: stur            x3, [fp, #-0x20]
    // 0x7ef8ac: r4 = 60
    //     0x7ef8ac: movz            x4, #0x3c
    // 0x7ef8b0: branchIfSmi(r0, 0x7ef8bc)
    //     0x7ef8b0: tbz             w0, #0, #0x7ef8bc
    // 0x7ef8b4: r4 = LoadClassIdInstr(r0)
    //     0x7ef8b4: ldur            x4, [x0, #-1]
    //     0x7ef8b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7ef8bc: sub             x4, x4, #0x5e
    // 0x7ef8c0: cmp             x4, #1
    // 0x7ef8c4: b.ls            #0x7ef8d8
    // 0x7ef8c8: r8 = String
    //     0x7ef8c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ef8cc: r3 = Null
    //     0x7ef8cc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d680] Null
    //     0x7ef8d0: ldr             x3, [x3, #0x680]
    // 0x7ef8d4: r0 = String()
    //     0x7ef8d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ef8d8: ldur            x3, [fp, #-8]
    // 0x7ef8dc: r0 = LoadClassIdInstr(r3)
    //     0x7ef8dc: ldur            x0, [x3, #-1]
    //     0x7ef8e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7ef8e4: mov             x1, x3
    // 0x7ef8e8: r2 = "route"
    //     0x7ef8e8: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x7ef8ec: ldr             x2, [x2, #0x3a0]
    // 0x7ef8f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ef8f0: sub             lr, x0, #0x114
    //     0x7ef8f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef8f8: blr             lr
    // 0x7ef8fc: mov             x3, x0
    // 0x7ef900: r2 = Null
    //     0x7ef900: mov             x2, NULL
    // 0x7ef904: r1 = Null
    //     0x7ef904: mov             x1, NULL
    // 0x7ef908: stur            x3, [fp, #-0x28]
    // 0x7ef90c: r4 = 60
    //     0x7ef90c: movz            x4, #0x3c
    // 0x7ef910: branchIfSmi(r0, 0x7ef91c)
    //     0x7ef910: tbz             w0, #0, #0x7ef91c
    // 0x7ef914: r4 = LoadClassIdInstr(r0)
    //     0x7ef914: ldur            x4, [x0, #-1]
    //     0x7ef918: ubfx            x4, x4, #0xc, #0x14
    // 0x7ef91c: sub             x4, x4, #0x5e
    // 0x7ef920: cmp             x4, #1
    // 0x7ef924: b.ls            #0x7ef938
    // 0x7ef928: r8 = String
    //     0x7ef928: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7ef92c: r3 = Null
    //     0x7ef92c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d690] Null
    //     0x7ef930: ldr             x3, [x3, #0x690]
    // 0x7ef934: r0 = String()
    //     0x7ef934: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ef938: ldur            x1, [fp, #-8]
    // 0x7ef93c: r0 = LoadClassIdInstr(r1)
    //     0x7ef93c: ldur            x0, [x1, #-1]
    //     0x7ef940: ubfx            x0, x0, #0xc, #0x14
    // 0x7ef944: r2 = "params"
    //     0x7ef944: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x7ef948: ldr             x2, [x2, #0x3b8]
    // 0x7ef94c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ef94c: sub             lr, x0, #0x114
    //     0x7ef950: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef954: blr             lr
    // 0x7ef958: mov             x3, x0
    // 0x7ef95c: r2 = Null
    //     0x7ef95c: mov             x2, NULL
    // 0x7ef960: r1 = Null
    //     0x7ef960: mov             x1, NULL
    // 0x7ef964: stur            x3, [fp, #-8]
    // 0x7ef968: r8 = Map?
    //     0x7ef968: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0x7ef96c: ldr             x8, [x8, #0x590]
    // 0x7ef970: r3 = Null
    //     0x7ef970: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d6a0] Null
    //     0x7ef974: ldr             x3, [x3, #0x6a0]
    // 0x7ef978: r0 = Map?()
    //     0x7ef978: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0x7ef97c: ldur            x0, [fp, #-8]
    // 0x7ef980: cmp             w0, NULL
    // 0x7ef984: b.ne            #0x7ef990
    // 0x7ef988: r4 = Null
    //     0x7ef988: mov             x4, NULL
    // 0x7ef98c: b               #0x7ef9bc
    // 0x7ef990: r1 = LoadClassIdInstr(r0)
    //     0x7ef990: ldur            x1, [x0, #-1]
    //     0x7ef994: ubfx            x1, x1, #0xc, #0x14
    // 0x7ef998: r16 = <String, String>
    //     0x7ef998: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x7ef99c: ldr             x16, [x16, #0x668]
    // 0x7ef9a0: stp             x0, x16, [SP]
    // 0x7ef9a4: mov             x0, x1
    // 0x7ef9a8: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x7ef9a8: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x7ef9ac: r0 = GDT[cid_x0 + 0x5f4]()
    //     0x7ef9ac: add             lr, x0, #0x5f4
    //     0x7ef9b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ef9b4: blr             lr
    // 0x7ef9b8: mov             x4, x0
    // 0x7ef9bc: ldur            x3, [fp, #-0x10]
    // 0x7ef9c0: ldur            x2, [fp, #-0x18]
    // 0x7ef9c4: ldur            x1, [fp, #-0x20]
    // 0x7ef9c8: ldur            x0, [fp, #-0x28]
    // 0x7ef9cc: stur            x4, [fp, #-8]
    // 0x7ef9d0: r5 = LoadInt32Instr(r3)
    //     0x7ef9d0: sbfx            x5, x3, #1, #0x1f
    //     0x7ef9d4: tbz             w3, #0, #0x7ef9dc
    //     0x7ef9d8: ldur            x5, [x3, #7]
    // 0x7ef9dc: stur            x5, [fp, #-0x30]
    // 0x7ef9e0: r0 = Menu()
    //     0x7ef9e0: bl              #0x7ec8b0  ; AllocateMenuStub -> Menu (size=0x2c)
    // 0x7ef9e4: mov             x1, x0
    // 0x7ef9e8: ldur            x0, [fp, #-0x30]
    // 0x7ef9ec: stur            x1, [fp, #-0x10]
    // 0x7ef9f0: StoreField: r1->field_13 = r0
    //     0x7ef9f0: stur            x0, [x1, #0x13]
    // 0x7ef9f4: ldur            x0, [fp, #-0x18]
    // 0x7ef9f8: StoreField: r1->field_1b = r0
    //     0x7ef9f8: stur            w0, [x1, #0x1b]
    // 0x7ef9fc: ldur            x0, [fp, #-0x20]
    // 0x7efa00: StoreField: r1->field_1f = r0
    //     0x7efa00: stur            w0, [x1, #0x1f]
    // 0x7efa04: ldur            x0, [fp, #-0x28]
    // 0x7efa08: StoreField: r1->field_23 = r0
    //     0x7efa08: stur            w0, [x1, #0x23]
    // 0x7efa0c: ldur            x0, [fp, #-8]
    // 0x7efa10: StoreField: r1->field_27 = r0
    //     0x7efa10: stur            w0, [x1, #0x27]
    // 0x7efa14: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x7efa14: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x7efa18: ldr             x16, [x16, #0x9f8]
    // 0x7efa1c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7efa20: stp             lr, x16, [SP]
    // 0x7efa24: r0 = Map._fromLiteral()
    //     0x7efa24: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7efa28: ldur            x1, [fp, #-0x10]
    // 0x7efa2c: StoreField: r1->field_f = r0
    //     0x7efa2c: stur            w0, [x1, #0xf]
    //     0x7efa30: ldurb           w16, [x1, #-1]
    //     0x7efa34: ldurb           w17, [x0, #-1]
    //     0x7efa38: and             x16, x17, x16, lsr #2
    //     0x7efa3c: tst             x16, HEAP, lsr #32
    //     0x7efa40: b.eq            #0x7efa48
    //     0x7efa44: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x7efa48: mov             x0, x1
    // 0x7efa4c: LeaveFrame
    //     0x7efa4c: mov             SP, fp
    //     0x7efa50: ldp             fp, lr, [SP], #0x10
    // 0x7efa54: ret
    //     0x7efa54: ret             
    // 0x7efa58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7efa58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7efa5c: b               #0x7ef7c0
  }
  factory _ Menu.more(/* No info */) {
    // ** addr: 0xb9c364, size: 0x60
    // 0xb9c364: EnterFrame
    //     0xb9c364: stp             fp, lr, [SP, #-0x10]!
    //     0xb9c368: mov             fp, SP
    // 0xb9c36c: AllocStack(0x8)
    //     0xb9c36c: sub             SP, SP, #8
    // 0xb9c370: CheckStackOverflow
    //     0xb9c370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9c374: cmp             SP, x16
    //     0xb9c378: b.ls            #0xb9c3bc
    // 0xb9c37c: r0 = Menu()
    //     0xb9c37c: bl              #0x7ec8b0  ; AllocateMenuStub -> Menu (size=0x2c)
    // 0xb9c380: mov             x1, x0
    // 0xb9c384: r2 = "local::packages/nuikit/assets/images/feature/more.svg"
    //     0xb9c384: add             x2, PP, #0x34, lsl #12  ; [pp+0x34088] "local::packages/nuikit/assets/images/feature/more.svg"
    //     0xb9c388: ldr             x2, [x2, #0x88]
    // 0xb9c38c: r3 = "Lainnya"
    //     0xb9c38c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34090] "Lainnya"
    //     0xb9c390: ldr             x3, [x3, #0x90]
    // 0xb9c394: r5 = 99
    //     0xb9c394: movz            x5, #0x63
    // 0xb9c398: r6 = "/menu"
    //     0xb9c398: add             x6, PP, #0x23, lsl #12  ; [pp+0x23ed8] "/menu"
    //     0xb9c39c: ldr             x6, [x6, #0xed8]
    // 0xb9c3a0: stur            x0, [fp, #-8]
    // 0xb9c3a4: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xb9c3a4: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xb9c3a8: r0 = Menu()
    //     0xb9c3a8: bl              #0x7ec598  ; [package:nuonline/app/data/models/menu.dart] Menu::Menu
    // 0xb9c3ac: ldur            x0, [fp, #-8]
    // 0xb9c3b0: LeaveFrame
    //     0xb9c3b0: mov             SP, fp
    //     0xb9c3b4: ldp             fp, lr, [SP], #0x10
    // 0xb9c3b8: ret
    //     0xb9c3b8: ret             
    // 0xb9c3bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9c3bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9c3c0: b               #0xb9c37c
  }
}

// class id: 1655, size: 0x14, field offset: 0xc
class MenuAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa63150, size: 0x478
    // 0xa63150: EnterFrame
    //     0xa63150: stp             fp, lr, [SP, #-0x10]!
    //     0xa63154: mov             fp, SP
    // 0xa63158: AllocStack(0x58)
    //     0xa63158: sub             SP, SP, #0x58
    // 0xa6315c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa6315c: stur            x2, [fp, #-0x20]
    // 0xa63160: CheckStackOverflow
    //     0xa63160: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa63164: cmp             SP, x16
    //     0xa63168: b.ls            #0xa635b0
    // 0xa6316c: LoadField: r3 = r2->field_23
    //     0xa6316c: ldur            x3, [x2, #0x23]
    // 0xa63170: add             x0, x3, #1
    // 0xa63174: LoadField: r1 = r2->field_1b
    //     0xa63174: ldur            x1, [x2, #0x1b]
    // 0xa63178: cmp             x0, x1
    // 0xa6317c: b.gt            #0xa63554
    // 0xa63180: LoadField: r4 = r2->field_7
    //     0xa63180: ldur            w4, [x2, #7]
    // 0xa63184: DecompressPointer r4
    //     0xa63184: add             x4, x4, HEAP, lsl #32
    // 0xa63188: stur            x4, [fp, #-0x18]
    // 0xa6318c: StoreField: r2->field_23 = r0
    //     0xa6318c: stur            x0, [x2, #0x23]
    // 0xa63190: LoadField: r0 = r4->field_13
    //     0xa63190: ldur            w0, [x4, #0x13]
    // 0xa63194: r5 = LoadInt32Instr(r0)
    //     0xa63194: sbfx            x5, x0, #1, #0x1f
    // 0xa63198: mov             x0, x5
    // 0xa6319c: mov             x1, x3
    // 0xa631a0: stur            x5, [fp, #-0x10]
    // 0xa631a4: cmp             x1, x0
    // 0xa631a8: b.hs            #0xa635b8
    // 0xa631ac: LoadField: r0 = r4->field_7
    //     0xa631ac: ldur            x0, [x4, #7]
    // 0xa631b0: ldrb            w1, [x0, x3]
    // 0xa631b4: stur            x1, [fp, #-8]
    // 0xa631b8: r16 = <int, dynamic>
    //     0xa631b8: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa631bc: ldr             x16, [x16, #0xac0]
    // 0xa631c0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa631c4: stp             lr, x16, [SP]
    // 0xa631c8: r0 = Map._fromLiteral()
    //     0xa631c8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa631cc: mov             x2, x0
    // 0xa631d0: stur            x2, [fp, #-0x38]
    // 0xa631d4: r6 = 0
    //     0xa631d4: movz            x6, #0
    // 0xa631d8: ldur            x3, [fp, #-0x20]
    // 0xa631dc: ldur            x4, [fp, #-0x18]
    // 0xa631e0: ldur            x5, [fp, #-8]
    // 0xa631e4: stur            x6, [fp, #-0x30]
    // 0xa631e8: CheckStackOverflow
    //     0xa631e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa631ec: cmp             SP, x16
    //     0xa631f0: b.ls            #0xa635bc
    // 0xa631f4: cmp             x6, x5
    // 0xa631f8: b.ge            #0xa63284
    // 0xa631fc: LoadField: r7 = r3->field_23
    //     0xa631fc: ldur            x7, [x3, #0x23]
    // 0xa63200: add             x0, x7, #1
    // 0xa63204: LoadField: r1 = r3->field_1b
    //     0xa63204: ldur            x1, [x3, #0x1b]
    // 0xa63208: cmp             x0, x1
    // 0xa6320c: b.gt            #0xa6357c
    // 0xa63210: StoreField: r3->field_23 = r0
    //     0xa63210: stur            x0, [x3, #0x23]
    // 0xa63214: ldur            x0, [fp, #-0x10]
    // 0xa63218: mov             x1, x7
    // 0xa6321c: cmp             x1, x0
    // 0xa63220: b.hs            #0xa635c4
    // 0xa63224: LoadField: r0 = r4->field_7
    //     0xa63224: ldur            x0, [x4, #7]
    // 0xa63228: ldrb            w8, [x0, x7]
    // 0xa6322c: mov             x1, x3
    // 0xa63230: stur            x8, [fp, #-0x28]
    // 0xa63234: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa63234: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa63238: r0 = read()
    //     0xa63238: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa6323c: mov             x1, x0
    // 0xa63240: ldur            x0, [fp, #-0x28]
    // 0xa63244: lsl             x2, x0, #1
    // 0xa63248: r16 = LoadInt32Instr(r2)
    //     0xa63248: sbfx            x16, x2, #1, #0x1f
    // 0xa6324c: r17 = 11601
    //     0xa6324c: movz            x17, #0x2d51
    // 0xa63250: mul             x0, x16, x17
    // 0xa63254: umulh           x16, x16, x17
    // 0xa63258: eor             x0, x0, x16
    // 0xa6325c: r0 = 0
    //     0xa6325c: eor             x0, x0, x0, lsr #32
    // 0xa63260: ubfiz           x0, x0, #1, #0x1e
    // 0xa63264: r5 = LoadInt32Instr(r0)
    //     0xa63264: sbfx            x5, x0, #1, #0x1f
    // 0xa63268: mov             x3, x1
    // 0xa6326c: ldur            x1, [fp, #-0x38]
    // 0xa63270: r0 = _set()
    //     0xa63270: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa63274: ldur            x0, [fp, #-0x30]
    // 0xa63278: add             x6, x0, #1
    // 0xa6327c: ldur            x2, [fp, #-0x38]
    // 0xa63280: b               #0xa631d8
    // 0xa63284: mov             x0, x2
    // 0xa63288: mov             x1, x0
    // 0xa6328c: r2 = 0
    //     0xa6328c: movz            x2, #0
    // 0xa63290: r0 = _getValueOrData()
    //     0xa63290: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63294: ldur            x3, [fp, #-0x38]
    // 0xa63298: LoadField: r1 = r3->field_f
    //     0xa63298: ldur            w1, [x3, #0xf]
    // 0xa6329c: DecompressPointer r1
    //     0xa6329c: add             x1, x1, HEAP, lsl #32
    // 0xa632a0: cmp             w1, w0
    // 0xa632a4: b.ne            #0xa632b0
    // 0xa632a8: r4 = Null
    //     0xa632a8: mov             x4, NULL
    // 0xa632ac: b               #0xa632b4
    // 0xa632b0: mov             x4, x0
    // 0xa632b4: mov             x0, x4
    // 0xa632b8: stur            x4, [fp, #-0x18]
    // 0xa632bc: r2 = Null
    //     0xa632bc: mov             x2, NULL
    // 0xa632c0: r1 = Null
    //     0xa632c0: mov             x1, NULL
    // 0xa632c4: branchIfSmi(r0, 0xa632ec)
    //     0xa632c4: tbz             w0, #0, #0xa632ec
    // 0xa632c8: r4 = LoadClassIdInstr(r0)
    //     0xa632c8: ldur            x4, [x0, #-1]
    //     0xa632cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa632d0: sub             x4, x4, #0x3c
    // 0xa632d4: cmp             x4, #1
    // 0xa632d8: b.ls            #0xa632ec
    // 0xa632dc: r8 = int
    //     0xa632dc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa632e0: r3 = Null
    //     0xa632e0: add             x3, PP, #0x21, lsl #12  ; [pp+0x211c0] Null
    //     0xa632e4: ldr             x3, [x3, #0x1c0]
    // 0xa632e8: r0 = int()
    //     0xa632e8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa632ec: ldur            x1, [fp, #-0x38]
    // 0xa632f0: r2 = 2
    //     0xa632f0: movz            x2, #0x2
    // 0xa632f4: r0 = _getValueOrData()
    //     0xa632f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa632f8: ldur            x3, [fp, #-0x38]
    // 0xa632fc: LoadField: r1 = r3->field_f
    //     0xa632fc: ldur            w1, [x3, #0xf]
    // 0xa63300: DecompressPointer r1
    //     0xa63300: add             x1, x1, HEAP, lsl #32
    // 0xa63304: cmp             w1, w0
    // 0xa63308: b.ne            #0xa63314
    // 0xa6330c: r4 = Null
    //     0xa6330c: mov             x4, NULL
    // 0xa63310: b               #0xa63318
    // 0xa63314: mov             x4, x0
    // 0xa63318: mov             x0, x4
    // 0xa6331c: stur            x4, [fp, #-0x20]
    // 0xa63320: r2 = Null
    //     0xa63320: mov             x2, NULL
    // 0xa63324: r1 = Null
    //     0xa63324: mov             x1, NULL
    // 0xa63328: r4 = 60
    //     0xa63328: movz            x4, #0x3c
    // 0xa6332c: branchIfSmi(r0, 0xa63338)
    //     0xa6332c: tbz             w0, #0, #0xa63338
    // 0xa63330: r4 = LoadClassIdInstr(r0)
    //     0xa63330: ldur            x4, [x0, #-1]
    //     0xa63334: ubfx            x4, x4, #0xc, #0x14
    // 0xa63338: sub             x4, x4, #0x5e
    // 0xa6333c: cmp             x4, #1
    // 0xa63340: b.ls            #0xa63354
    // 0xa63344: r8 = String
    //     0xa63344: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa63348: r3 = Null
    //     0xa63348: add             x3, PP, #0x21, lsl #12  ; [pp+0x211d0] Null
    //     0xa6334c: ldr             x3, [x3, #0x1d0]
    // 0xa63350: r0 = String()
    //     0xa63350: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa63354: ldur            x1, [fp, #-0x38]
    // 0xa63358: r2 = 4
    //     0xa63358: movz            x2, #0x4
    // 0xa6335c: r0 = _getValueOrData()
    //     0xa6335c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63360: ldur            x3, [fp, #-0x38]
    // 0xa63364: LoadField: r1 = r3->field_f
    //     0xa63364: ldur            w1, [x3, #0xf]
    // 0xa63368: DecompressPointer r1
    //     0xa63368: add             x1, x1, HEAP, lsl #32
    // 0xa6336c: cmp             w1, w0
    // 0xa63370: b.ne            #0xa6337c
    // 0xa63374: r4 = Null
    //     0xa63374: mov             x4, NULL
    // 0xa63378: b               #0xa63380
    // 0xa6337c: mov             x4, x0
    // 0xa63380: mov             x0, x4
    // 0xa63384: stur            x4, [fp, #-0x40]
    // 0xa63388: r2 = Null
    //     0xa63388: mov             x2, NULL
    // 0xa6338c: r1 = Null
    //     0xa6338c: mov             x1, NULL
    // 0xa63390: r4 = 60
    //     0xa63390: movz            x4, #0x3c
    // 0xa63394: branchIfSmi(r0, 0xa633a0)
    //     0xa63394: tbz             w0, #0, #0xa633a0
    // 0xa63398: r4 = LoadClassIdInstr(r0)
    //     0xa63398: ldur            x4, [x0, #-1]
    //     0xa6339c: ubfx            x4, x4, #0xc, #0x14
    // 0xa633a0: sub             x4, x4, #0x5e
    // 0xa633a4: cmp             x4, #1
    // 0xa633a8: b.ls            #0xa633bc
    // 0xa633ac: r8 = String
    //     0xa633ac: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa633b0: r3 = Null
    //     0xa633b0: add             x3, PP, #0x21, lsl #12  ; [pp+0x211e0] Null
    //     0xa633b4: ldr             x3, [x3, #0x1e0]
    // 0xa633b8: r0 = String()
    //     0xa633b8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa633bc: ldur            x1, [fp, #-0x38]
    // 0xa633c0: r2 = 6
    //     0xa633c0: movz            x2, #0x6
    // 0xa633c4: r0 = _getValueOrData()
    //     0xa633c4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa633c8: ldur            x3, [fp, #-0x38]
    // 0xa633cc: LoadField: r1 = r3->field_f
    //     0xa633cc: ldur            w1, [x3, #0xf]
    // 0xa633d0: DecompressPointer r1
    //     0xa633d0: add             x1, x1, HEAP, lsl #32
    // 0xa633d4: cmp             w1, w0
    // 0xa633d8: b.ne            #0xa633e4
    // 0xa633dc: r4 = Null
    //     0xa633dc: mov             x4, NULL
    // 0xa633e0: b               #0xa633e8
    // 0xa633e4: mov             x4, x0
    // 0xa633e8: mov             x0, x4
    // 0xa633ec: stur            x4, [fp, #-0x48]
    // 0xa633f0: r2 = Null
    //     0xa633f0: mov             x2, NULL
    // 0xa633f4: r1 = Null
    //     0xa633f4: mov             x1, NULL
    // 0xa633f8: r4 = 60
    //     0xa633f8: movz            x4, #0x3c
    // 0xa633fc: branchIfSmi(r0, 0xa63408)
    //     0xa633fc: tbz             w0, #0, #0xa63408
    // 0xa63400: r4 = LoadClassIdInstr(r0)
    //     0xa63400: ldur            x4, [x0, #-1]
    //     0xa63404: ubfx            x4, x4, #0xc, #0x14
    // 0xa63408: sub             x4, x4, #0x5e
    // 0xa6340c: cmp             x4, #1
    // 0xa63410: b.ls            #0xa63424
    // 0xa63414: r8 = String
    //     0xa63414: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa63418: r3 = Null
    //     0xa63418: add             x3, PP, #0x21, lsl #12  ; [pp+0x211f0] Null
    //     0xa6341c: ldr             x3, [x3, #0x1f0]
    // 0xa63420: r0 = String()
    //     0xa63420: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa63424: ldur            x1, [fp, #-0x38]
    // 0xa63428: r2 = 8
    //     0xa63428: movz            x2, #0x8
    // 0xa6342c: r0 = _getValueOrData()
    //     0xa6342c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63430: mov             x1, x0
    // 0xa63434: ldur            x0, [fp, #-0x38]
    // 0xa63438: LoadField: r2 = r0->field_f
    //     0xa63438: ldur            w2, [x0, #0xf]
    // 0xa6343c: DecompressPointer r2
    //     0xa6343c: add             x2, x2, HEAP, lsl #32
    // 0xa63440: cmp             w2, w1
    // 0xa63444: b.ne            #0xa63450
    // 0xa63448: r3 = Null
    //     0xa63448: mov             x3, NULL
    // 0xa6344c: b               #0xa63454
    // 0xa63450: mov             x3, x1
    // 0xa63454: mov             x0, x3
    // 0xa63458: stur            x3, [fp, #-0x38]
    // 0xa6345c: r2 = Null
    //     0xa6345c: mov             x2, NULL
    // 0xa63460: r1 = Null
    //     0xa63460: mov             x1, NULL
    // 0xa63464: r8 = Map?
    //     0xa63464: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0xa63468: ldr             x8, [x8, #0x590]
    // 0xa6346c: r3 = Null
    //     0xa6346c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21200] Null
    //     0xa63470: ldr             x3, [x3, #0x200]
    // 0xa63474: r0 = Map?()
    //     0xa63474: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0xa63478: ldur            x0, [fp, #-0x38]
    // 0xa6347c: cmp             w0, NULL
    // 0xa63480: b.ne            #0xa6348c
    // 0xa63484: r4 = Null
    //     0xa63484: mov             x4, NULL
    // 0xa63488: b               #0xa634b8
    // 0xa6348c: r1 = LoadClassIdInstr(r0)
    //     0xa6348c: ldur            x1, [x0, #-1]
    //     0xa63490: ubfx            x1, x1, #0xc, #0x14
    // 0xa63494: r16 = <String, String>
    //     0xa63494: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xa63498: ldr             x16, [x16, #0x668]
    // 0xa6349c: stp             x0, x16, [SP]
    // 0xa634a0: mov             x0, x1
    // 0xa634a4: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa634a4: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa634a8: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa634a8: add             lr, x0, #0x5f4
    //     0xa634ac: ldr             lr, [x21, lr, lsl #3]
    //     0xa634b0: blr             lr
    // 0xa634b4: mov             x4, x0
    // 0xa634b8: ldur            x3, [fp, #-0x18]
    // 0xa634bc: ldur            x2, [fp, #-0x20]
    // 0xa634c0: ldur            x1, [fp, #-0x40]
    // 0xa634c4: ldur            x0, [fp, #-0x48]
    // 0xa634c8: stur            x4, [fp, #-0x38]
    // 0xa634cc: r5 = LoadInt32Instr(r3)
    //     0xa634cc: sbfx            x5, x3, #1, #0x1f
    //     0xa634d0: tbz             w3, #0, #0xa634d8
    //     0xa634d4: ldur            x5, [x3, #7]
    // 0xa634d8: stur            x5, [fp, #-8]
    // 0xa634dc: r0 = Menu()
    //     0xa634dc: bl              #0x7ec8b0  ; AllocateMenuStub -> Menu (size=0x2c)
    // 0xa634e0: mov             x1, x0
    // 0xa634e4: ldur            x0, [fp, #-8]
    // 0xa634e8: stur            x1, [fp, #-0x18]
    // 0xa634ec: StoreField: r1->field_13 = r0
    //     0xa634ec: stur            x0, [x1, #0x13]
    // 0xa634f0: ldur            x0, [fp, #-0x20]
    // 0xa634f4: StoreField: r1->field_1b = r0
    //     0xa634f4: stur            w0, [x1, #0x1b]
    // 0xa634f8: ldur            x0, [fp, #-0x40]
    // 0xa634fc: StoreField: r1->field_1f = r0
    //     0xa634fc: stur            w0, [x1, #0x1f]
    // 0xa63500: ldur            x0, [fp, #-0x48]
    // 0xa63504: StoreField: r1->field_23 = r0
    //     0xa63504: stur            w0, [x1, #0x23]
    // 0xa63508: ldur            x0, [fp, #-0x38]
    // 0xa6350c: StoreField: r1->field_27 = r0
    //     0xa6350c: stur            w0, [x1, #0x27]
    // 0xa63510: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa63510: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa63514: ldr             x16, [x16, #0x9f8]
    // 0xa63518: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6351c: stp             lr, x16, [SP]
    // 0xa63520: r0 = Map._fromLiteral()
    //     0xa63520: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa63524: ldur            x1, [fp, #-0x18]
    // 0xa63528: StoreField: r1->field_f = r0
    //     0xa63528: stur            w0, [x1, #0xf]
    //     0xa6352c: ldurb           w16, [x1, #-1]
    //     0xa63530: ldurb           w17, [x0, #-1]
    //     0xa63534: and             x16, x17, x16, lsr #2
    //     0xa63538: tst             x16, HEAP, lsr #32
    //     0xa6353c: b.eq            #0xa63544
    //     0xa63540: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa63544: mov             x0, x1
    // 0xa63548: LeaveFrame
    //     0xa63548: mov             SP, fp
    //     0xa6354c: ldp             fp, lr, [SP], #0x10
    // 0xa63550: ret
    //     0xa63550: ret             
    // 0xa63554: r0 = RangeError()
    //     0xa63554: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa63558: mov             x1, x0
    // 0xa6355c: r0 = "Not enough bytes available."
    //     0xa6355c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa63560: ldr             x0, [x0, #0x8a8]
    // 0xa63564: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63564: stur            w0, [x1, #0x17]
    // 0xa63568: r2 = false
    //     0xa63568: add             x2, NULL, #0x30  ; false
    // 0xa6356c: StoreField: r1->field_b = r2
    //     0xa6356c: stur            w2, [x1, #0xb]
    // 0xa63570: mov             x0, x1
    // 0xa63574: r0 = Throw()
    //     0xa63574: bl              #0xec04b8  ; ThrowStub
    // 0xa63578: brk             #0
    // 0xa6357c: r0 = "Not enough bytes available."
    //     0xa6357c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa63580: ldr             x0, [x0, #0x8a8]
    // 0xa63584: r2 = false
    //     0xa63584: add             x2, NULL, #0x30  ; false
    // 0xa63588: r0 = RangeError()
    //     0xa63588: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa6358c: mov             x1, x0
    // 0xa63590: r0 = "Not enough bytes available."
    //     0xa63590: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa63594: ldr             x0, [x0, #0x8a8]
    // 0xa63598: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63598: stur            w0, [x1, #0x17]
    // 0xa6359c: r0 = false
    //     0xa6359c: add             x0, NULL, #0x30  ; false
    // 0xa635a0: StoreField: r1->field_b = r0
    //     0xa635a0: stur            w0, [x1, #0xb]
    // 0xa635a4: mov             x0, x1
    // 0xa635a8: r0 = Throw()
    //     0xa635a8: bl              #0xec04b8  ; ThrowStub
    // 0xa635ac: brk             #0
    // 0xa635b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa635b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa635b4: b               #0xa6316c
    // 0xa635b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa635b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa635bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa635bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa635c0: b               #0xa631f4
    // 0xa635c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa635c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd4224, size: 0x3ac
    // 0xbd4224: EnterFrame
    //     0xbd4224: stp             fp, lr, [SP, #-0x10]!
    //     0xbd4228: mov             fp, SP
    // 0xbd422c: AllocStack(0x28)
    //     0xbd422c: sub             SP, SP, #0x28
    // 0xbd4230: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd4230: mov             x4, x2
    //     0xbd4234: stur            x2, [fp, #-8]
    //     0xbd4238: stur            x3, [fp, #-0x10]
    // 0xbd423c: CheckStackOverflow
    //     0xbd423c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd4240: cmp             SP, x16
    //     0xbd4244: b.ls            #0xbd45b0
    // 0xbd4248: mov             x0, x3
    // 0xbd424c: r2 = Null
    //     0xbd424c: mov             x2, NULL
    // 0xbd4250: r1 = Null
    //     0xbd4250: mov             x1, NULL
    // 0xbd4254: r4 = 60
    //     0xbd4254: movz            x4, #0x3c
    // 0xbd4258: branchIfSmi(r0, 0xbd4264)
    //     0xbd4258: tbz             w0, #0, #0xbd4264
    // 0xbd425c: r4 = LoadClassIdInstr(r0)
    //     0xbd425c: ldur            x4, [x0, #-1]
    //     0xbd4260: ubfx            x4, x4, #0xc, #0x14
    // 0xbd4264: cmp             x4, #0x645
    // 0xbd4268: b.eq            #0xbd4280
    // 0xbd426c: r8 = Menu
    //     0xbd426c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b2c0] Type: Menu
    //     0xbd4270: ldr             x8, [x8, #0x2c0]
    // 0xbd4274: r3 = Null
    //     0xbd4274: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b2c8] Null
    //     0xbd4278: ldr             x3, [x3, #0x2c8]
    // 0xbd427c: r0 = Menu()
    //     0xbd427c: bl              #0x7dace4  ; IsType_Menu_Stub
    // 0xbd4280: ldur            x0, [fp, #-8]
    // 0xbd4284: LoadField: r1 = r0->field_b
    //     0xbd4284: ldur            w1, [x0, #0xb]
    // 0xbd4288: DecompressPointer r1
    //     0xbd4288: add             x1, x1, HEAP, lsl #32
    // 0xbd428c: LoadField: r2 = r1->field_13
    //     0xbd428c: ldur            w2, [x1, #0x13]
    // 0xbd4290: LoadField: r1 = r0->field_13
    //     0xbd4290: ldur            x1, [x0, #0x13]
    // 0xbd4294: r3 = LoadInt32Instr(r2)
    //     0xbd4294: sbfx            x3, x2, #1, #0x1f
    // 0xbd4298: sub             x2, x3, x1
    // 0xbd429c: cmp             x2, #1
    // 0xbd42a0: b.ge            #0xbd42b0
    // 0xbd42a4: mov             x1, x0
    // 0xbd42a8: r2 = 1
    //     0xbd42a8: movz            x2, #0x1
    // 0xbd42ac: r0 = _increaseBufferSize()
    //     0xbd42ac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd42b0: ldur            x3, [fp, #-8]
    // 0xbd42b4: r2 = 5
    //     0xbd42b4: movz            x2, #0x5
    // 0xbd42b8: LoadField: r4 = r3->field_b
    //     0xbd42b8: ldur            w4, [x3, #0xb]
    // 0xbd42bc: DecompressPointer r4
    //     0xbd42bc: add             x4, x4, HEAP, lsl #32
    // 0xbd42c0: LoadField: r5 = r3->field_13
    //     0xbd42c0: ldur            x5, [x3, #0x13]
    // 0xbd42c4: add             x6, x5, #1
    // 0xbd42c8: StoreField: r3->field_13 = r6
    //     0xbd42c8: stur            x6, [x3, #0x13]
    // 0xbd42cc: LoadField: r0 = r4->field_13
    //     0xbd42cc: ldur            w0, [x4, #0x13]
    // 0xbd42d0: r7 = LoadInt32Instr(r0)
    //     0xbd42d0: sbfx            x7, x0, #1, #0x1f
    // 0xbd42d4: mov             x0, x7
    // 0xbd42d8: mov             x1, x5
    // 0xbd42dc: cmp             x1, x0
    // 0xbd42e0: b.hs            #0xbd45b8
    // 0xbd42e4: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd42e4: add             x0, x4, x5
    //     0xbd42e8: strb            w2, [x0, #0x17]
    // 0xbd42ec: sub             x0, x7, x6
    // 0xbd42f0: cmp             x0, #1
    // 0xbd42f4: b.ge            #0xbd4304
    // 0xbd42f8: mov             x1, x3
    // 0xbd42fc: r2 = 1
    //     0xbd42fc: movz            x2, #0x1
    // 0xbd4300: r0 = _increaseBufferSize()
    //     0xbd4300: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4304: ldur            x2, [fp, #-8]
    // 0xbd4308: ldur            x3, [fp, #-0x10]
    // 0xbd430c: LoadField: r4 = r2->field_b
    //     0xbd430c: ldur            w4, [x2, #0xb]
    // 0xbd4310: DecompressPointer r4
    //     0xbd4310: add             x4, x4, HEAP, lsl #32
    // 0xbd4314: LoadField: r5 = r2->field_13
    //     0xbd4314: ldur            x5, [x2, #0x13]
    // 0xbd4318: add             x0, x5, #1
    // 0xbd431c: StoreField: r2->field_13 = r0
    //     0xbd431c: stur            x0, [x2, #0x13]
    // 0xbd4320: LoadField: r0 = r4->field_13
    //     0xbd4320: ldur            w0, [x4, #0x13]
    // 0xbd4324: r1 = LoadInt32Instr(r0)
    //     0xbd4324: sbfx            x1, x0, #1, #0x1f
    // 0xbd4328: mov             x0, x1
    // 0xbd432c: mov             x1, x5
    // 0xbd4330: cmp             x1, x0
    // 0xbd4334: b.hs            #0xbd45bc
    // 0xbd4338: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd4338: add             x0, x4, x5
    //     0xbd433c: strb            wzr, [x0, #0x17]
    // 0xbd4340: LoadField: r4 = r3->field_13
    //     0xbd4340: ldur            x4, [x3, #0x13]
    // 0xbd4344: r0 = BoxInt64Instr(r4)
    //     0xbd4344: sbfiz           x0, x4, #1, #0x1f
    //     0xbd4348: cmp             x4, x0, asr #1
    //     0xbd434c: b.eq            #0xbd4358
    //     0xbd4350: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd4354: stur            x4, [x0, #7]
    // 0xbd4358: r16 = <int>
    //     0xbd4358: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd435c: stp             x2, x16, [SP, #8]
    // 0xbd4360: str             x0, [SP]
    // 0xbd4364: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4364: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4368: r0 = write()
    //     0xbd4368: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd436c: ldur            x0, [fp, #-8]
    // 0xbd4370: LoadField: r1 = r0->field_b
    //     0xbd4370: ldur            w1, [x0, #0xb]
    // 0xbd4374: DecompressPointer r1
    //     0xbd4374: add             x1, x1, HEAP, lsl #32
    // 0xbd4378: LoadField: r2 = r1->field_13
    //     0xbd4378: ldur            w2, [x1, #0x13]
    // 0xbd437c: LoadField: r1 = r0->field_13
    //     0xbd437c: ldur            x1, [x0, #0x13]
    // 0xbd4380: r3 = LoadInt32Instr(r2)
    //     0xbd4380: sbfx            x3, x2, #1, #0x1f
    // 0xbd4384: sub             x2, x3, x1
    // 0xbd4388: cmp             x2, #1
    // 0xbd438c: b.ge            #0xbd439c
    // 0xbd4390: mov             x1, x0
    // 0xbd4394: r2 = 1
    //     0xbd4394: movz            x2, #0x1
    // 0xbd4398: r0 = _increaseBufferSize()
    //     0xbd4398: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd439c: ldur            x2, [fp, #-8]
    // 0xbd43a0: ldur            x3, [fp, #-0x10]
    // 0xbd43a4: r4 = 1
    //     0xbd43a4: movz            x4, #0x1
    // 0xbd43a8: LoadField: r5 = r2->field_b
    //     0xbd43a8: ldur            w5, [x2, #0xb]
    // 0xbd43ac: DecompressPointer r5
    //     0xbd43ac: add             x5, x5, HEAP, lsl #32
    // 0xbd43b0: LoadField: r6 = r2->field_13
    //     0xbd43b0: ldur            x6, [x2, #0x13]
    // 0xbd43b4: add             x0, x6, #1
    // 0xbd43b8: StoreField: r2->field_13 = r0
    //     0xbd43b8: stur            x0, [x2, #0x13]
    // 0xbd43bc: LoadField: r0 = r5->field_13
    //     0xbd43bc: ldur            w0, [x5, #0x13]
    // 0xbd43c0: r1 = LoadInt32Instr(r0)
    //     0xbd43c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd43c4: mov             x0, x1
    // 0xbd43c8: mov             x1, x6
    // 0xbd43cc: cmp             x1, x0
    // 0xbd43d0: b.hs            #0xbd45c0
    // 0xbd43d4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd43d4: add             x0, x5, x6
    //     0xbd43d8: strb            w4, [x0, #0x17]
    // 0xbd43dc: LoadField: r0 = r3->field_1b
    //     0xbd43dc: ldur            w0, [x3, #0x1b]
    // 0xbd43e0: DecompressPointer r0
    //     0xbd43e0: add             x0, x0, HEAP, lsl #32
    // 0xbd43e4: r16 = <String>
    //     0xbd43e4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd43e8: stp             x2, x16, [SP, #8]
    // 0xbd43ec: str             x0, [SP]
    // 0xbd43f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd43f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd43f4: r0 = write()
    //     0xbd43f4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd43f8: ldur            x0, [fp, #-8]
    // 0xbd43fc: LoadField: r1 = r0->field_b
    //     0xbd43fc: ldur            w1, [x0, #0xb]
    // 0xbd4400: DecompressPointer r1
    //     0xbd4400: add             x1, x1, HEAP, lsl #32
    // 0xbd4404: LoadField: r2 = r1->field_13
    //     0xbd4404: ldur            w2, [x1, #0x13]
    // 0xbd4408: LoadField: r1 = r0->field_13
    //     0xbd4408: ldur            x1, [x0, #0x13]
    // 0xbd440c: r3 = LoadInt32Instr(r2)
    //     0xbd440c: sbfx            x3, x2, #1, #0x1f
    // 0xbd4410: sub             x2, x3, x1
    // 0xbd4414: cmp             x2, #1
    // 0xbd4418: b.ge            #0xbd4428
    // 0xbd441c: mov             x1, x0
    // 0xbd4420: r2 = 1
    //     0xbd4420: movz            x2, #0x1
    // 0xbd4424: r0 = _increaseBufferSize()
    //     0xbd4424: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4428: ldur            x2, [fp, #-8]
    // 0xbd442c: ldur            x3, [fp, #-0x10]
    // 0xbd4430: r4 = 2
    //     0xbd4430: movz            x4, #0x2
    // 0xbd4434: LoadField: r5 = r2->field_b
    //     0xbd4434: ldur            w5, [x2, #0xb]
    // 0xbd4438: DecompressPointer r5
    //     0xbd4438: add             x5, x5, HEAP, lsl #32
    // 0xbd443c: LoadField: r6 = r2->field_13
    //     0xbd443c: ldur            x6, [x2, #0x13]
    // 0xbd4440: add             x0, x6, #1
    // 0xbd4444: StoreField: r2->field_13 = r0
    //     0xbd4444: stur            x0, [x2, #0x13]
    // 0xbd4448: LoadField: r0 = r5->field_13
    //     0xbd4448: ldur            w0, [x5, #0x13]
    // 0xbd444c: r1 = LoadInt32Instr(r0)
    //     0xbd444c: sbfx            x1, x0, #1, #0x1f
    // 0xbd4450: mov             x0, x1
    // 0xbd4454: mov             x1, x6
    // 0xbd4458: cmp             x1, x0
    // 0xbd445c: b.hs            #0xbd45c4
    // 0xbd4460: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4460: add             x0, x5, x6
    //     0xbd4464: strb            w4, [x0, #0x17]
    // 0xbd4468: LoadField: r0 = r3->field_1f
    //     0xbd4468: ldur            w0, [x3, #0x1f]
    // 0xbd446c: DecompressPointer r0
    //     0xbd446c: add             x0, x0, HEAP, lsl #32
    // 0xbd4470: r16 = <String>
    //     0xbd4470: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd4474: stp             x2, x16, [SP, #8]
    // 0xbd4478: str             x0, [SP]
    // 0xbd447c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd447c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4480: r0 = write()
    //     0xbd4480: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4484: ldur            x0, [fp, #-8]
    // 0xbd4488: LoadField: r1 = r0->field_b
    //     0xbd4488: ldur            w1, [x0, #0xb]
    // 0xbd448c: DecompressPointer r1
    //     0xbd448c: add             x1, x1, HEAP, lsl #32
    // 0xbd4490: LoadField: r2 = r1->field_13
    //     0xbd4490: ldur            w2, [x1, #0x13]
    // 0xbd4494: LoadField: r1 = r0->field_13
    //     0xbd4494: ldur            x1, [x0, #0x13]
    // 0xbd4498: r3 = LoadInt32Instr(r2)
    //     0xbd4498: sbfx            x3, x2, #1, #0x1f
    // 0xbd449c: sub             x2, x3, x1
    // 0xbd44a0: cmp             x2, #1
    // 0xbd44a4: b.ge            #0xbd44b4
    // 0xbd44a8: mov             x1, x0
    // 0xbd44ac: r2 = 1
    //     0xbd44ac: movz            x2, #0x1
    // 0xbd44b0: r0 = _increaseBufferSize()
    //     0xbd44b0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd44b4: ldur            x2, [fp, #-8]
    // 0xbd44b8: ldur            x3, [fp, #-0x10]
    // 0xbd44bc: r4 = 3
    //     0xbd44bc: movz            x4, #0x3
    // 0xbd44c0: LoadField: r5 = r2->field_b
    //     0xbd44c0: ldur            w5, [x2, #0xb]
    // 0xbd44c4: DecompressPointer r5
    //     0xbd44c4: add             x5, x5, HEAP, lsl #32
    // 0xbd44c8: LoadField: r6 = r2->field_13
    //     0xbd44c8: ldur            x6, [x2, #0x13]
    // 0xbd44cc: add             x0, x6, #1
    // 0xbd44d0: StoreField: r2->field_13 = r0
    //     0xbd44d0: stur            x0, [x2, #0x13]
    // 0xbd44d4: LoadField: r0 = r5->field_13
    //     0xbd44d4: ldur            w0, [x5, #0x13]
    // 0xbd44d8: r1 = LoadInt32Instr(r0)
    //     0xbd44d8: sbfx            x1, x0, #1, #0x1f
    // 0xbd44dc: mov             x0, x1
    // 0xbd44e0: mov             x1, x6
    // 0xbd44e4: cmp             x1, x0
    // 0xbd44e8: b.hs            #0xbd45c8
    // 0xbd44ec: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd44ec: add             x0, x5, x6
    //     0xbd44f0: strb            w4, [x0, #0x17]
    // 0xbd44f4: LoadField: r0 = r3->field_23
    //     0xbd44f4: ldur            w0, [x3, #0x23]
    // 0xbd44f8: DecompressPointer r0
    //     0xbd44f8: add             x0, x0, HEAP, lsl #32
    // 0xbd44fc: r16 = <String>
    //     0xbd44fc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd4500: stp             x2, x16, [SP, #8]
    // 0xbd4504: str             x0, [SP]
    // 0xbd4508: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4508: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd450c: r0 = write()
    //     0xbd450c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4510: ldur            x0, [fp, #-8]
    // 0xbd4514: LoadField: r1 = r0->field_b
    //     0xbd4514: ldur            w1, [x0, #0xb]
    // 0xbd4518: DecompressPointer r1
    //     0xbd4518: add             x1, x1, HEAP, lsl #32
    // 0xbd451c: LoadField: r2 = r1->field_13
    //     0xbd451c: ldur            w2, [x1, #0x13]
    // 0xbd4520: LoadField: r1 = r0->field_13
    //     0xbd4520: ldur            x1, [x0, #0x13]
    // 0xbd4524: r3 = LoadInt32Instr(r2)
    //     0xbd4524: sbfx            x3, x2, #1, #0x1f
    // 0xbd4528: sub             x2, x3, x1
    // 0xbd452c: cmp             x2, #1
    // 0xbd4530: b.ge            #0xbd4540
    // 0xbd4534: mov             x1, x0
    // 0xbd4538: r2 = 1
    //     0xbd4538: movz            x2, #0x1
    // 0xbd453c: r0 = _increaseBufferSize()
    //     0xbd453c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4540: ldur            x2, [fp, #-8]
    // 0xbd4544: ldur            x3, [fp, #-0x10]
    // 0xbd4548: r4 = 4
    //     0xbd4548: movz            x4, #0x4
    // 0xbd454c: LoadField: r5 = r2->field_b
    //     0xbd454c: ldur            w5, [x2, #0xb]
    // 0xbd4550: DecompressPointer r5
    //     0xbd4550: add             x5, x5, HEAP, lsl #32
    // 0xbd4554: LoadField: r6 = r2->field_13
    //     0xbd4554: ldur            x6, [x2, #0x13]
    // 0xbd4558: add             x0, x6, #1
    // 0xbd455c: StoreField: r2->field_13 = r0
    //     0xbd455c: stur            x0, [x2, #0x13]
    // 0xbd4560: LoadField: r0 = r5->field_13
    //     0xbd4560: ldur            w0, [x5, #0x13]
    // 0xbd4564: r1 = LoadInt32Instr(r0)
    //     0xbd4564: sbfx            x1, x0, #1, #0x1f
    // 0xbd4568: mov             x0, x1
    // 0xbd456c: mov             x1, x6
    // 0xbd4570: cmp             x1, x0
    // 0xbd4574: b.hs            #0xbd45cc
    // 0xbd4578: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4578: add             x0, x5, x6
    //     0xbd457c: strb            w4, [x0, #0x17]
    // 0xbd4580: LoadField: r0 = r3->field_27
    //     0xbd4580: ldur            w0, [x3, #0x27]
    // 0xbd4584: DecompressPointer r0
    //     0xbd4584: add             x0, x0, HEAP, lsl #32
    // 0xbd4588: r16 = <Map<String, String>?>
    //     0xbd4588: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] TypeArguments: <Map<String, String>?>
    //     0xbd458c: ldr             x16, [x16, #0x178]
    // 0xbd4590: stp             x2, x16, [SP, #8]
    // 0xbd4594: str             x0, [SP]
    // 0xbd4598: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4598: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd459c: r0 = write()
    //     0xbd459c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd45a0: r0 = Null
    //     0xbd45a0: mov             x0, NULL
    // 0xbd45a4: LeaveFrame
    //     0xbd45a4: mov             SP, fp
    //     0xbd45a8: ldp             fp, lr, [SP], #0x10
    // 0xbd45ac: ret
    //     0xbd45ac: ret             
    // 0xbd45b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd45b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd45b4: b               #0xbd4248
    // 0xbd45b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd45bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd45c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd45c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd45c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd45cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd45cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf021c, size: 0x24
    // 0xbf021c: r1 = 60
    //     0xbf021c: movz            x1, #0x3c
    // 0xbf0220: r16 = LoadInt32Instr(r1)
    //     0xbf0220: sbfx            x16, x1, #1, #0x1f
    // 0xbf0224: r17 = 11601
    //     0xbf0224: movz            x17, #0x2d51
    // 0xbf0228: mul             x0, x16, x17
    // 0xbf022c: umulh           x16, x16, x17
    // 0xbf0230: eor             x0, x0, x16
    // 0xbf0234: r0 = 0
    //     0xbf0234: eor             x0, x0, x0, lsr #32
    // 0xbf0238: ubfiz           x0, x0, #1, #0x1e
    // 0xbf023c: ret
    //     0xbf023c: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76998, size: 0x9c
    // 0xd76998: EnterFrame
    //     0xd76998: stp             fp, lr, [SP, #-0x10]!
    //     0xd7699c: mov             fp, SP
    // 0xd769a0: AllocStack(0x10)
    //     0xd769a0: sub             SP, SP, #0x10
    // 0xd769a4: CheckStackOverflow
    //     0xd769a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd769a8: cmp             SP, x16
    //     0xd769ac: b.ls            #0xd76a2c
    // 0xd769b0: ldr             x0, [fp, #0x10]
    // 0xd769b4: cmp             w0, NULL
    // 0xd769b8: b.ne            #0xd769cc
    // 0xd769bc: r0 = false
    //     0xd769bc: add             x0, NULL, #0x30  ; false
    // 0xd769c0: LeaveFrame
    //     0xd769c0: mov             SP, fp
    //     0xd769c4: ldp             fp, lr, [SP], #0x10
    // 0xd769c8: ret
    //     0xd769c8: ret             
    // 0xd769cc: ldr             x1, [fp, #0x18]
    // 0xd769d0: cmp             w1, w0
    // 0xd769d4: b.ne            #0xd769e0
    // 0xd769d8: r0 = true
    //     0xd769d8: add             x0, NULL, #0x20  ; true
    // 0xd769dc: b               #0xd76a20
    // 0xd769e0: r1 = 60
    //     0xd769e0: movz            x1, #0x3c
    // 0xd769e4: branchIfSmi(r0, 0xd769f0)
    //     0xd769e4: tbz             w0, #0, #0xd769f0
    // 0xd769e8: r1 = LoadClassIdInstr(r0)
    //     0xd769e8: ldur            x1, [x0, #-1]
    //     0xd769ec: ubfx            x1, x1, #0xc, #0x14
    // 0xd769f0: cmp             x1, #0x677
    // 0xd769f4: b.ne            #0xd76a1c
    // 0xd769f8: r16 = MenuAdapter
    //     0xd769f8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2b8] Type: MenuAdapter
    //     0xd769fc: ldr             x16, [x16, #0x2b8]
    // 0xd76a00: r30 = MenuAdapter
    //     0xd76a00: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b2b8] Type: MenuAdapter
    //     0xd76a04: ldr             lr, [lr, #0x2b8]
    // 0xd76a08: stp             lr, x16, [SP]
    // 0xd76a0c: r0 = ==()
    //     0xd76a0c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76a10: tbnz            w0, #4, #0xd76a1c
    // 0xd76a14: r0 = true
    //     0xd76a14: add             x0, NULL, #0x20  ; true
    // 0xd76a18: b               #0xd76a20
    // 0xd76a1c: r0 = false
    //     0xd76a1c: add             x0, NULL, #0x30  ; false
    // 0xd76a20: LeaveFrame
    //     0xd76a20: mov             SP, fp
    //     0xd76a24: ldp             fp, lr, [SP], #0x10
    // 0xd76a28: ret
    //     0xd76a28: ret             
    // 0xd76a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76a30: b               #0xd769b0
  }
}
