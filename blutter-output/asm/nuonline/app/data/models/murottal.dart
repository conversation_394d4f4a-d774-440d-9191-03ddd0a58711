// lib: , url: package:nuonline/app/data/models/murottal.dart

// class id: 1050033, size: 0x8
class :: {
}

// class id: 5578, size: 0x24, field offset: 0x8
//   const constructor, 
class Murottal extends Equatable {

  _ copyWith(/* No info */) {
    // ** addr: 0x8f9e68, size: 0x5c
    // 0x8f9e68: EnterFrame
    //     0x8f9e68: stp             fp, lr, [SP, #-0x10]!
    //     0x8f9e6c: mov             fp, SP
    // 0x8f9e70: AllocStack(0x20)
    //     0x8f9e70: sub             SP, SP, #0x20
    // 0x8f9e74: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0x8f9e74: stur            x2, [fp, #-0x20]
    // 0x8f9e78: LoadField: r0 = r1->field_7
    //     0x8f9e78: ldur            w0, [x1, #7]
    // 0x8f9e7c: DecompressPointer r0
    //     0x8f9e7c: add             x0, x0, HEAP, lsl #32
    // 0x8f9e80: stur            x0, [fp, #-0x18]
    // 0x8f9e84: LoadField: r3 = r1->field_b
    //     0x8f9e84: ldur            x3, [x1, #0xb]
    // 0x8f9e88: stur            x3, [fp, #-0x10]
    // 0x8f9e8c: LoadField: r4 = r1->field_13
    //     0x8f9e8c: ldur            x4, [x1, #0x13]
    // 0x8f9e90: stur            x4, [fp, #-8]
    // 0x8f9e94: r0 = Murottal()
    //     0x8f9e94: bl              #0x8ae398  ; AllocateMurottalStub -> Murottal (size=0x24)
    // 0x8f9e98: ldur            x1, [fp, #-0x18]
    // 0x8f9e9c: StoreField: r0->field_7 = r1
    //     0x8f9e9c: stur            w1, [x0, #7]
    // 0x8f9ea0: ldur            x1, [fp, #-0x10]
    // 0x8f9ea4: StoreField: r0->field_b = r1
    //     0x8f9ea4: stur            x1, [x0, #0xb]
    // 0x8f9ea8: ldur            x1, [fp, #-8]
    // 0x8f9eac: StoreField: r0->field_13 = r1
    //     0x8f9eac: stur            x1, [x0, #0x13]
    // 0x8f9eb0: ldur            x1, [fp, #-0x20]
    // 0x8f9eb4: StoreField: r0->field_1b = r1
    //     0x8f9eb4: stur            x1, [x0, #0x1b]
    // 0x8f9eb8: LeaveFrame
    //     0x8f9eb8: mov             SP, fp
    //     0x8f9ebc: ldp             fp, lr, [SP], #0x10
    // 0x8f9ec0: ret
    //     0x8f9ec0: ret             
  }
  get _ basmalah(/* No info */) {
    // ** addr: 0x8fab84, size: 0x30
    // 0x8fab84: EnterFrame
    //     0x8fab84: stp             fp, lr, [SP, #-0x10]!
    //     0x8fab88: mov             fp, SP
    // 0x8fab8c: CheckStackOverflow
    //     0x8fab8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fab90: cmp             SP, x16
    //     0x8fab94: b.ls            #0x8fabac
    // 0x8fab98: r2 = 0
    //     0x8fab98: movz            x2, #0
    // 0x8fab9c: r0 = copyWith()
    //     0x8fab9c: bl              #0x8f9e68  ; [package:nuonline/app/data/models/murottal.dart] Murottal::copyWith
    // 0x8faba0: LeaveFrame
    //     0x8faba0: mov             SP, fp
    //     0x8faba4: ldp             fp, lr, [SP], #0x10
    // 0x8faba8: ret
    //     0x8faba8: ret             
    // 0x8fabac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fabac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fabb0: b               #0x8fab98
  }
  get _ taawudz(/* No info */) {
    // ** addr: 0x8fabb4, size: 0x30
    // 0x8fabb4: EnterFrame
    //     0x8fabb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fabb8: mov             fp, SP
    // 0x8fabbc: CheckStackOverflow
    //     0x8fabbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fabc0: cmp             SP, x16
    //     0x8fabc4: b.ls            #0x8fabdc
    // 0x8fabc8: r2 = -1
    //     0x8fabc8: movn            x2, #0
    // 0x8fabcc: r0 = copyWith()
    //     0x8fabcc: bl              #0x8f9e68  ; [package:nuonline/app/data/models/murottal.dart] Murottal::copyWith
    // 0x8fabd0: LeaveFrame
    //     0x8fabd0: mov             SP, fp
    //     0x8fabd4: ldp             fp, lr, [SP], #0x10
    // 0x8fabd8: ret
    //     0x8fabd8: ret             
    // 0x8fabdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fabdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fabe0: b               #0x8fabc8
  }
  _ toMap(/* No info */) {
    // ** addr: 0x8fb400, size: 0xe4
    // 0x8fb400: EnterFrame
    //     0x8fb400: stp             fp, lr, [SP, #-0x10]!
    //     0x8fb404: mov             fp, SP
    // 0x8fb408: AllocStack(0x18)
    //     0x8fb408: sub             SP, SP, #0x18
    // 0x8fb40c: SetupParameters(Murottal this /* r1 => r0, fp-0x8 */)
    //     0x8fb40c: mov             x0, x1
    //     0x8fb410: stur            x1, [fp, #-8]
    // 0x8fb414: CheckStackOverflow
    //     0x8fb414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fb418: cmp             SP, x16
    //     0x8fb41c: b.ls            #0x8fb4dc
    // 0x8fb420: r1 = Null
    //     0x8fb420: mov             x1, NULL
    // 0x8fb424: r2 = 16
    //     0x8fb424: movz            x2, #0x10
    // 0x8fb428: r0 = AllocateArray()
    //     0x8fb428: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8fb42c: mov             x2, x0
    // 0x8fb430: r16 = "surah_name"
    //     0x8fb430: add             x16, PP, #0x10, lsl #12  ; [pp+0x103a0] "surah_name"
    //     0x8fb434: ldr             x16, [x16, #0x3a0]
    // 0x8fb438: StoreField: r2->field_f = r16
    //     0x8fb438: stur            w16, [x2, #0xf]
    // 0x8fb43c: ldur            x3, [fp, #-8]
    // 0x8fb440: LoadField: r0 = r3->field_7
    //     0x8fb440: ldur            w0, [x3, #7]
    // 0x8fb444: DecompressPointer r0
    //     0x8fb444: add             x0, x0, HEAP, lsl #32
    // 0x8fb448: StoreField: r2->field_13 = r0
    //     0x8fb448: stur            w0, [x2, #0x13]
    // 0x8fb44c: r16 = "surah_id"
    //     0x8fb44c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x8fb450: ldr             x16, [x16, #0x340]
    // 0x8fb454: ArrayStore: r2[0] = r16  ; List_4
    //     0x8fb454: stur            w16, [x2, #0x17]
    // 0x8fb458: LoadField: r4 = r3->field_b
    //     0x8fb458: ldur            x4, [x3, #0xb]
    // 0x8fb45c: r0 = BoxInt64Instr(r4)
    //     0x8fb45c: sbfiz           x0, x4, #1, #0x1f
    //     0x8fb460: cmp             x4, x0, asr #1
    //     0x8fb464: b.eq            #0x8fb470
    //     0x8fb468: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fb46c: stur            x4, [x0, #7]
    // 0x8fb470: StoreField: r2->field_1b = r0
    //     0x8fb470: stur            w0, [x2, #0x1b]
    // 0x8fb474: r16 = "total"
    //     0x8fb474: add             x16, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0x8fb478: ldr             x16, [x16, #0xdc0]
    // 0x8fb47c: StoreField: r2->field_1f = r16
    //     0x8fb47c: stur            w16, [x2, #0x1f]
    // 0x8fb480: LoadField: r4 = r3->field_13
    //     0x8fb480: ldur            x4, [x3, #0x13]
    // 0x8fb484: r0 = BoxInt64Instr(r4)
    //     0x8fb484: sbfiz           x0, x4, #1, #0x1f
    //     0x8fb488: cmp             x4, x0, asr #1
    //     0x8fb48c: b.eq            #0x8fb498
    //     0x8fb490: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fb494: stur            x4, [x0, #7]
    // 0x8fb498: StoreField: r2->field_23 = r0
    //     0x8fb498: stur            w0, [x2, #0x23]
    // 0x8fb49c: r16 = "number"
    //     0x8fb49c: add             x16, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x8fb4a0: ldr             x16, [x16, #0x998]
    // 0x8fb4a4: StoreField: r2->field_27 = r16
    //     0x8fb4a4: stur            w16, [x2, #0x27]
    // 0x8fb4a8: LoadField: r4 = r3->field_1b
    //     0x8fb4a8: ldur            x4, [x3, #0x1b]
    // 0x8fb4ac: r0 = BoxInt64Instr(r4)
    //     0x8fb4ac: sbfiz           x0, x4, #1, #0x1f
    //     0x8fb4b0: cmp             x4, x0, asr #1
    //     0x8fb4b4: b.eq            #0x8fb4c0
    //     0x8fb4b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fb4bc: stur            x4, [x0, #7]
    // 0x8fb4c0: StoreField: r2->field_2b = r0
    //     0x8fb4c0: stur            w0, [x2, #0x2b]
    // 0x8fb4c4: r16 = <String, dynamic>
    //     0x8fb4c4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8fb4c8: stp             x2, x16, [SP]
    // 0x8fb4cc: r0 = Map._fromLiteral()
    //     0x8fb4cc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8fb4d0: LeaveFrame
    //     0x8fb4d0: mov             SP, fp
    //     0x8fb4d4: ldp             fp, lr, [SP], #0x10
    // 0x8fb4d8: ret
    //     0x8fb4d8: ret             
    // 0x8fb4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fb4dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fb4e0: b               #0x8fb420
  }
  get _ title(/* No info */) {
    // ** addr: 0x8fb664, size: 0xf4
    // 0x8fb664: EnterFrame
    //     0x8fb664: stp             fp, lr, [SP, #-0x10]!
    //     0x8fb668: mov             fp, SP
    // 0x8fb66c: AllocStack(0x20)
    //     0x8fb66c: sub             SP, SP, #0x20
    // 0x8fb670: CheckStackOverflow
    //     0x8fb670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fb674: cmp             SP, x16
    //     0x8fb678: b.ls            #0x8fb750
    // 0x8fb67c: LoadField: r0 = r1->field_1b
    //     0x8fb67c: ldur            x0, [x1, #0x1b]
    // 0x8fb680: stur            x0, [fp, #-0x10]
    // 0x8fb684: cmn             x0, #1
    // 0x8fb688: b.ne            #0x8fb6a0
    // 0x8fb68c: r0 = "Ta\'awudz"
    //     0x8fb68c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c030] "Ta\'awudz"
    //     0x8fb690: ldr             x0, [x0, #0x30]
    // 0x8fb694: LeaveFrame
    //     0x8fb694: mov             SP, fp
    //     0x8fb698: ldp             fp, lr, [SP], #0x10
    // 0x8fb69c: ret
    //     0x8fb69c: ret             
    // 0x8fb6a0: cbnz            x0, #0x8fb6b8
    // 0x8fb6a4: r0 = "Basmalah"
    //     0x8fb6a4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c038] "Basmalah"
    //     0x8fb6a8: ldr             x0, [x0, #0x38]
    // 0x8fb6ac: LeaveFrame
    //     0x8fb6ac: mov             SP, fp
    //     0x8fb6b0: ldp             fp, lr, [SP], #0x10
    // 0x8fb6b4: ret
    //     0x8fb6b4: ret             
    // 0x8fb6b8: r3 = 8
    //     0x8fb6b8: movz            x3, #0x8
    // 0x8fb6bc: LoadField: r4 = r1->field_7
    //     0x8fb6bc: ldur            w4, [x1, #7]
    // 0x8fb6c0: DecompressPointer r4
    //     0x8fb6c0: add             x4, x4, HEAP, lsl #32
    // 0x8fb6c4: mov             x2, x3
    // 0x8fb6c8: stur            x4, [fp, #-8]
    // 0x8fb6cc: r1 = Null
    //     0x8fb6cc: mov             x1, NULL
    // 0x8fb6d0: r0 = AllocateArray()
    //     0x8fb6d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8fb6d4: mov             x2, x0
    // 0x8fb6d8: stur            x2, [fp, #-0x18]
    // 0x8fb6dc: r16 = "Surah"
    //     0x8fb6dc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2ba40] "Surah"
    //     0x8fb6e0: ldr             x16, [x16, #0xa40]
    // 0x8fb6e4: StoreField: r2->field_f = r16
    //     0x8fb6e4: stur            w16, [x2, #0xf]
    // 0x8fb6e8: ldur            x0, [fp, #-8]
    // 0x8fb6ec: StoreField: r2->field_13 = r0
    //     0x8fb6ec: stur            w0, [x2, #0x13]
    // 0x8fb6f0: r16 = "Ayat"
    //     0x8fb6f0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c040] "Ayat"
    //     0x8fb6f4: ldr             x16, [x16, #0x40]
    // 0x8fb6f8: ArrayStore: r2[0] = r16  ; List_4
    //     0x8fb6f8: stur            w16, [x2, #0x17]
    // 0x8fb6fc: ldur            x3, [fp, #-0x10]
    // 0x8fb700: r0 = BoxInt64Instr(r3)
    //     0x8fb700: sbfiz           x0, x3, #1, #0x1f
    //     0x8fb704: cmp             x3, x0, asr #1
    //     0x8fb708: b.eq            #0x8fb714
    //     0x8fb70c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fb710: stur            x3, [x0, #7]
    // 0x8fb714: StoreField: r2->field_1b = r0
    //     0x8fb714: stur            w0, [x2, #0x1b]
    // 0x8fb718: r1 = <Object>
    //     0x8fb718: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x8fb71c: r0 = AllocateGrowableArray()
    //     0x8fb71c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8fb720: mov             x1, x0
    // 0x8fb724: ldur            x0, [fp, #-0x18]
    // 0x8fb728: StoreField: r1->field_f = r0
    //     0x8fb728: stur            w0, [x1, #0xf]
    // 0x8fb72c: r0 = 8
    //     0x8fb72c: movz            x0, #0x8
    // 0x8fb730: StoreField: r1->field_b = r0
    //     0x8fb730: stur            w0, [x1, #0xb]
    // 0x8fb734: r16 = " "
    //     0x8fb734: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x8fb738: str             x16, [SP]
    // 0x8fb73c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8fb73c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8fb740: r0 = join()
    //     0x8fb740: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0x8fb744: LeaveFrame
    //     0x8fb744: mov             SP, fp
    //     0x8fb748: ldp             fp, lr, [SP], #0x10
    // 0x8fb74c: ret
    //     0x8fb74c: ret             
    // 0x8fb750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fb750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fb754: b               #0x8fb67c
  }
  factory _ Murottal.fromMap(/* No info */) {
    // ** addr: 0x8fb8ac, size: 0x230
    // 0x8fb8ac: EnterFrame
    //     0x8fb8ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8fb8b0: mov             fp, SP
    // 0x8fb8b4: AllocStack(0x20)
    //     0x8fb8b4: sub             SP, SP, #0x20
    // 0x8fb8b8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x8fb8b8: mov             x0, x2
    //     0x8fb8bc: stur            x2, [fp, #-8]
    // 0x8fb8c0: CheckStackOverflow
    //     0x8fb8c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fb8c4: cmp             SP, x16
    //     0x8fb8c8: b.ls            #0x8fbad4
    // 0x8fb8cc: mov             x1, x0
    // 0x8fb8d0: r2 = "surah_name"
    //     0x8fb8d0: add             x2, PP, #0x10, lsl #12  ; [pp+0x103a0] "surah_name"
    //     0x8fb8d4: ldr             x2, [x2, #0x3a0]
    // 0x8fb8d8: r0 = _getValueOrData()
    //     0x8fb8d8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fb8dc: ldur            x3, [fp, #-8]
    // 0x8fb8e0: LoadField: r1 = r3->field_f
    //     0x8fb8e0: ldur            w1, [x3, #0xf]
    // 0x8fb8e4: DecompressPointer r1
    //     0x8fb8e4: add             x1, x1, HEAP, lsl #32
    // 0x8fb8e8: cmp             w1, w0
    // 0x8fb8ec: b.ne            #0x8fb8f8
    // 0x8fb8f0: r4 = Null
    //     0x8fb8f0: mov             x4, NULL
    // 0x8fb8f4: b               #0x8fb8fc
    // 0x8fb8f8: mov             x4, x0
    // 0x8fb8fc: mov             x0, x4
    // 0x8fb900: stur            x4, [fp, #-0x10]
    // 0x8fb904: r2 = Null
    //     0x8fb904: mov             x2, NULL
    // 0x8fb908: r1 = Null
    //     0x8fb908: mov             x1, NULL
    // 0x8fb90c: r4 = 60
    //     0x8fb90c: movz            x4, #0x3c
    // 0x8fb910: branchIfSmi(r0, 0x8fb91c)
    //     0x8fb910: tbz             w0, #0, #0x8fb91c
    // 0x8fb914: r4 = LoadClassIdInstr(r0)
    //     0x8fb914: ldur            x4, [x0, #-1]
    //     0x8fb918: ubfx            x4, x4, #0xc, #0x14
    // 0x8fb91c: sub             x4, x4, #0x5e
    // 0x8fb920: cmp             x4, #1
    // 0x8fb924: b.ls            #0x8fb938
    // 0x8fb928: r8 = String
    //     0x8fb928: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fb92c: r3 = Null
    //     0x8fb92c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bf68] Null
    //     0x8fb930: ldr             x3, [x3, #0xf68]
    // 0x8fb934: r0 = String()
    //     0x8fb934: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fb938: ldur            x1, [fp, #-8]
    // 0x8fb93c: r2 = "surah_id"
    //     0x8fb93c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x8fb940: ldr             x2, [x2, #0x340]
    // 0x8fb944: r0 = _getValueOrData()
    //     0x8fb944: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fb948: ldur            x3, [fp, #-8]
    // 0x8fb94c: LoadField: r1 = r3->field_f
    //     0x8fb94c: ldur            w1, [x3, #0xf]
    // 0x8fb950: DecompressPointer r1
    //     0x8fb950: add             x1, x1, HEAP, lsl #32
    // 0x8fb954: cmp             w1, w0
    // 0x8fb958: b.ne            #0x8fb964
    // 0x8fb95c: r4 = Null
    //     0x8fb95c: mov             x4, NULL
    // 0x8fb960: b               #0x8fb968
    // 0x8fb964: mov             x4, x0
    // 0x8fb968: mov             x0, x4
    // 0x8fb96c: stur            x4, [fp, #-0x18]
    // 0x8fb970: r2 = Null
    //     0x8fb970: mov             x2, NULL
    // 0x8fb974: r1 = Null
    //     0x8fb974: mov             x1, NULL
    // 0x8fb978: branchIfSmi(r0, 0x8fb9a0)
    //     0x8fb978: tbz             w0, #0, #0x8fb9a0
    // 0x8fb97c: r4 = LoadClassIdInstr(r0)
    //     0x8fb97c: ldur            x4, [x0, #-1]
    //     0x8fb980: ubfx            x4, x4, #0xc, #0x14
    // 0x8fb984: sub             x4, x4, #0x3c
    // 0x8fb988: cmp             x4, #1
    // 0x8fb98c: b.ls            #0x8fb9a0
    // 0x8fb990: r8 = int
    //     0x8fb990: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fb994: r3 = Null
    //     0x8fb994: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bf78] Null
    //     0x8fb998: ldr             x3, [x3, #0xf78]
    // 0x8fb99c: r0 = int()
    //     0x8fb99c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fb9a0: ldur            x1, [fp, #-8]
    // 0x8fb9a4: r2 = "total"
    //     0x8fb9a4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0x8fb9a8: ldr             x2, [x2, #0xdc0]
    // 0x8fb9ac: r0 = _getValueOrData()
    //     0x8fb9ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fb9b0: ldur            x3, [fp, #-8]
    // 0x8fb9b4: LoadField: r1 = r3->field_f
    //     0x8fb9b4: ldur            w1, [x3, #0xf]
    // 0x8fb9b8: DecompressPointer r1
    //     0x8fb9b8: add             x1, x1, HEAP, lsl #32
    // 0x8fb9bc: cmp             w1, w0
    // 0x8fb9c0: b.ne            #0x8fb9cc
    // 0x8fb9c4: r4 = Null
    //     0x8fb9c4: mov             x4, NULL
    // 0x8fb9c8: b               #0x8fb9d0
    // 0x8fb9cc: mov             x4, x0
    // 0x8fb9d0: mov             x0, x4
    // 0x8fb9d4: stur            x4, [fp, #-0x20]
    // 0x8fb9d8: r2 = Null
    //     0x8fb9d8: mov             x2, NULL
    // 0x8fb9dc: r1 = Null
    //     0x8fb9dc: mov             x1, NULL
    // 0x8fb9e0: branchIfSmi(r0, 0x8fba08)
    //     0x8fb9e0: tbz             w0, #0, #0x8fba08
    // 0x8fb9e4: r4 = LoadClassIdInstr(r0)
    //     0x8fb9e4: ldur            x4, [x0, #-1]
    //     0x8fb9e8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fb9ec: sub             x4, x4, #0x3c
    // 0x8fb9f0: cmp             x4, #1
    // 0x8fb9f4: b.ls            #0x8fba08
    // 0x8fb9f8: r8 = int
    //     0x8fb9f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fb9fc: r3 = Null
    //     0x8fb9fc: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bf88] Null
    //     0x8fba00: ldr             x3, [x3, #0xf88]
    // 0x8fba04: r0 = int()
    //     0x8fba04: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fba08: ldur            x1, [fp, #-8]
    // 0x8fba0c: r2 = "number"
    //     0x8fba0c: add             x2, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x8fba10: ldr             x2, [x2, #0x998]
    // 0x8fba14: r0 = _getValueOrData()
    //     0x8fba14: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8fba18: mov             x1, x0
    // 0x8fba1c: ldur            x0, [fp, #-8]
    // 0x8fba20: LoadField: r2 = r0->field_f
    //     0x8fba20: ldur            w2, [x0, #0xf]
    // 0x8fba24: DecompressPointer r2
    //     0x8fba24: add             x2, x2, HEAP, lsl #32
    // 0x8fba28: cmp             w2, w1
    // 0x8fba2c: b.ne            #0x8fba38
    // 0x8fba30: r6 = Null
    //     0x8fba30: mov             x6, NULL
    // 0x8fba34: b               #0x8fba3c
    // 0x8fba38: mov             x6, x1
    // 0x8fba3c: ldur            x5, [fp, #-0x10]
    // 0x8fba40: ldur            x4, [fp, #-0x18]
    // 0x8fba44: ldur            x3, [fp, #-0x20]
    // 0x8fba48: mov             x0, x6
    // 0x8fba4c: stur            x6, [fp, #-8]
    // 0x8fba50: r2 = Null
    //     0x8fba50: mov             x2, NULL
    // 0x8fba54: r1 = Null
    //     0x8fba54: mov             x1, NULL
    // 0x8fba58: branchIfSmi(r0, 0x8fba80)
    //     0x8fba58: tbz             w0, #0, #0x8fba80
    // 0x8fba5c: r4 = LoadClassIdInstr(r0)
    //     0x8fba5c: ldur            x4, [x0, #-1]
    //     0x8fba60: ubfx            x4, x4, #0xc, #0x14
    // 0x8fba64: sub             x4, x4, #0x3c
    // 0x8fba68: cmp             x4, #1
    // 0x8fba6c: b.ls            #0x8fba80
    // 0x8fba70: r8 = int
    //     0x8fba70: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fba74: r3 = Null
    //     0x8fba74: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bf98] Null
    //     0x8fba78: ldr             x3, [x3, #0xf98]
    // 0x8fba7c: r0 = int()
    //     0x8fba7c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fba80: r0 = Murottal()
    //     0x8fba80: bl              #0x8ae398  ; AllocateMurottalStub -> Murottal (size=0x24)
    // 0x8fba84: ldur            x1, [fp, #-0x10]
    // 0x8fba88: StoreField: r0->field_7 = r1
    //     0x8fba88: stur            w1, [x0, #7]
    // 0x8fba8c: ldur            x1, [fp, #-0x18]
    // 0x8fba90: r2 = LoadInt32Instr(r1)
    //     0x8fba90: sbfx            x2, x1, #1, #0x1f
    //     0x8fba94: tbz             w1, #0, #0x8fba9c
    //     0x8fba98: ldur            x2, [x1, #7]
    // 0x8fba9c: StoreField: r0->field_b = r2
    //     0x8fba9c: stur            x2, [x0, #0xb]
    // 0x8fbaa0: ldur            x1, [fp, #-0x20]
    // 0x8fbaa4: r2 = LoadInt32Instr(r1)
    //     0x8fbaa4: sbfx            x2, x1, #1, #0x1f
    //     0x8fbaa8: tbz             w1, #0, #0x8fbab0
    //     0x8fbaac: ldur            x2, [x1, #7]
    // 0x8fbab0: StoreField: r0->field_13 = r2
    //     0x8fbab0: stur            x2, [x0, #0x13]
    // 0x8fbab4: ldur            x1, [fp, #-8]
    // 0x8fbab8: r2 = LoadInt32Instr(r1)
    //     0x8fbab8: sbfx            x2, x1, #1, #0x1f
    //     0x8fbabc: tbz             w1, #0, #0x8fbac4
    //     0x8fbac0: ldur            x2, [x1, #7]
    // 0x8fbac4: StoreField: r0->field_1b = r2
    //     0x8fbac4: stur            x2, [x0, #0x1b]
    // 0x8fbac8: LeaveFrame
    //     0x8fbac8: mov             SP, fp
    //     0x8fbacc: ldp             fp, lr, [SP], #0x10
    // 0x8fbad0: ret
    //     0x8fbad0: ret             
    // 0x8fbad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fbad4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fbad8: b               #0x8fb8cc
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbee0, size: 0x90
    // 0xbdbee0: EnterFrame
    //     0xbdbee0: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbee4: mov             fp, SP
    // 0xbdbee8: AllocStack(0x18)
    //     0xbdbee8: sub             SP, SP, #0x18
    // 0xbdbeec: r3 = 4
    //     0xbdbeec: movz            x3, #0x4
    // 0xbdbef0: LoadField: r2 = r1->field_b
    //     0xbdbef0: ldur            x2, [x1, #0xb]
    // 0xbdbef4: LoadField: r4 = r1->field_1b
    //     0xbdbef4: ldur            x4, [x1, #0x1b]
    // 0xbdbef8: stur            x4, [fp, #-0x10]
    // 0xbdbefc: r0 = BoxInt64Instr(r2)
    //     0xbdbefc: sbfiz           x0, x2, #1, #0x1f
    //     0xbdbf00: cmp             x2, x0, asr #1
    //     0xbdbf04: b.eq            #0xbdbf10
    //     0xbdbf08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdbf0c: stur            x2, [x0, #7]
    // 0xbdbf10: mov             x2, x3
    // 0xbdbf14: r1 = Null
    //     0xbdbf14: mov             x1, NULL
    // 0xbdbf18: stur            x0, [fp, #-8]
    // 0xbdbf1c: r0 = AllocateArray()
    //     0xbdbf1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbf20: mov             x2, x0
    // 0xbdbf24: ldur            x0, [fp, #-8]
    // 0xbdbf28: stur            x2, [fp, #-0x18]
    // 0xbdbf2c: StoreField: r2->field_f = r0
    //     0xbdbf2c: stur            w0, [x2, #0xf]
    // 0xbdbf30: ldur            x3, [fp, #-0x10]
    // 0xbdbf34: r0 = BoxInt64Instr(r3)
    //     0xbdbf34: sbfiz           x0, x3, #1, #0x1f
    //     0xbdbf38: cmp             x3, x0, asr #1
    //     0xbdbf3c: b.eq            #0xbdbf48
    //     0xbdbf40: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdbf44: stur            x3, [x0, #7]
    // 0xbdbf48: StoreField: r2->field_13 = r0
    //     0xbdbf48: stur            w0, [x2, #0x13]
    // 0xbdbf4c: r1 = <Object?>
    //     0xbdbf4c: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbf50: r0 = AllocateGrowableArray()
    //     0xbdbf50: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbf54: ldur            x1, [fp, #-0x18]
    // 0xbdbf58: StoreField: r0->field_f = r1
    //     0xbdbf58: stur            w1, [x0, #0xf]
    // 0xbdbf5c: r1 = 4
    //     0xbdbf5c: movz            x1, #0x4
    // 0xbdbf60: StoreField: r0->field_b = r1
    //     0xbdbf60: stur            w1, [x0, #0xb]
    // 0xbdbf64: LeaveFrame
    //     0xbdbf64: mov             SP, fp
    //     0xbdbf68: ldp             fp, lr, [SP], #0x10
    // 0xbdbf6c: ret
    //     0xbdbf6c: ret             
  }
}
