// lib: , url: package:nuonline/app/data/models/notification.dart

// class id: 1050034, size: 0x8
class :: {
}

// class id: 1137, size: 0x20, field offset: 0x8
class PrayerTimeNotification extends Object
    implements Notification {

  int id(PrayerTimeNotification) {
    // ** addr: 0x81a3fc, size: 0x68
    // 0x81a3fc: EnterFrame
    //     0x81a3fc: stp             fp, lr, [SP, #-0x10]!
    //     0x81a400: mov             fp, SP
    // 0x81a404: CheckStackOverflow
    //     0x81a404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81a408: cmp             SP, x16
    //     0x81a40c: b.ls            #0x81a45c
    // 0x81a410: LoadField: r0 = r1->field_b
    //     0x81a410: ldur            w0, [x1, #0xb]
    // 0x81a414: DecompressPointer r0
    //     0x81a414: add             x0, x0, HEAP, lsl #32
    // 0x81a418: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x81a418: ldur            w2, [x1, #0x17]
    // 0x81a41c: DecompressPointer r2
    //     0x81a41c: add             x2, x2, HEAP, lsl #32
    // 0x81a420: tbnz            w2, #4, #0x81a438
    // 0x81a424: LoadField: r2 = r1->field_7
    //     0x81a424: ldur            w2, [x1, #7]
    // 0x81a428: DecompressPointer r2
    //     0x81a428: add             x2, x2, HEAP, lsl #32
    // 0x81a42c: LoadField: r1 = r2->field_7
    //     0x81a42c: ldur            x1, [x2, #7]
    // 0x81a430: mov             x2, x1
    // 0x81a434: b               #0x81a448
    // 0x81a438: LoadField: r2 = r1->field_7
    //     0x81a438: ldur            w2, [x1, #7]
    // 0x81a43c: DecompressPointer r2
    //     0x81a43c: add             x2, x2, HEAP, lsl #32
    // 0x81a440: LoadField: r1 = r2->field_7
    //     0x81a440: ldur            x1, [x2, #7]
    // 0x81a444: add             x2, x1, #0xb
    // 0x81a448: mov             x1, x0
    // 0x81a44c: r0 = DateTimeExtensions.toUniqueID()
    //     0x81a44c: bl              #0x81da9c  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueID
    // 0x81a450: LeaveFrame
    //     0x81a450: mov             SP, fp
    //     0x81a454: ldp             fp, lr, [SP], #0x10
    // 0x81a458: ret
    //     0x81a458: ret             
    // 0x81a45c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81a45c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81a460: b               #0x81a410
  }
  int dyn:get:id(PrayerTimeNotification) {
    // ** addr: 0x81a47c, size: 0x60
    // 0x81a47c: EnterFrame
    //     0x81a47c: stp             fp, lr, [SP, #-0x10]!
    //     0x81a480: mov             fp, SP
    // 0x81a484: CheckStackOverflow
    //     0x81a484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81a488: cmp             SP, x16
    //     0x81a48c: b.ls            #0x81a4bc
    // 0x81a490: ldr             x1, [fp, #0x10]
    // 0x81a494: r0 = id()
    //     0x81a494: bl              #0x81a3fc  ; [package:nuonline/app/data/models/notification.dart] PrayerTimeNotification::id
    // 0x81a498: mov             x2, x0
    // 0x81a49c: r0 = BoxInt64Instr(r2)
    //     0x81a49c: sbfiz           x0, x2, #1, #0x1f
    //     0x81a4a0: cmp             x2, x0, asr #1
    //     0x81a4a4: b.eq            #0x81a4b0
    //     0x81a4a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81a4ac: stur            x2, [x0, #7]
    // 0x81a4b0: LeaveFrame
    //     0x81a4b0: mov             SP, fp
    //     0x81a4b4: ldp             fp, lr, [SP], #0x10
    // 0x81a4b8: ret
    //     0x81a4b8: ret             
    // 0x81a4bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81a4bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81a4c0: b               #0x81a490
  }
  get _ group(/* No info */) {
    // ** addr: 0x81bc70, size: 0x140
    // 0x81bc70: EnterFrame
    //     0x81bc70: stp             fp, lr, [SP, #-0x10]!
    //     0x81bc74: mov             fp, SP
    // 0x81bc78: AllocStack(0x18)
    //     0x81bc78: sub             SP, SP, #0x18
    // 0x81bc7c: SetupParameters(PrayerTimeNotification this /* r1 => r0, fp-0x8 */)
    //     0x81bc7c: mov             x0, x1
    //     0x81bc80: stur            x1, [fp, #-8]
    // 0x81bc84: CheckStackOverflow
    //     0x81bc84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81bc88: cmp             SP, x16
    //     0x81bc8c: b.ls            #0x81bda8
    // 0x81bc90: LoadField: r1 = r0->field_7
    //     0x81bc90: ldur            w1, [x0, #7]
    // 0x81bc94: DecompressPointer r1
    //     0x81bc94: add             x1, x1, HEAP, lsl #32
    // 0x81bc98: r16 = Instance_Prayer
    //     0x81bc98: add             x16, PP, #8, lsl #12  ; [pp+0x8230] Obj!Prayer@e38dc1
    //     0x81bc9c: ldr             x16, [x16, #0x230]
    // 0x81bca0: cmp             w1, w16
    // 0x81bca4: b.ne            #0x81bd60
    // 0x81bca8: r3 = 6
    //     0x81bca8: movz            x3, #0x6
    // 0x81bcac: mov             x2, x3
    // 0x81bcb0: r1 = Null
    //     0x81bcb0: mov             x1, NULL
    // 0x81bcb4: r0 = AllocateArray()
    //     0x81bcb4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81bcb8: stur            x0, [fp, #-0x10]
    // 0x81bcbc: r16 = Instance_NotificationSound
    //     0x81bcbc: add             x16, PP, #8, lsl #12  ; [pp+0x8a98] Obj!NotificationSound@e304a1
    //     0x81bcc0: ldr             x16, [x16, #0xa98]
    // 0x81bcc4: StoreField: r0->field_f = r16
    //     0x81bcc4: stur            w16, [x0, #0xf]
    // 0x81bcc8: r16 = Instance_NotificationSound
    //     0x81bcc8: add             x16, PP, #8, lsl #12  ; [pp+0x8aa0] Obj!NotificationSound@e30481
    //     0x81bccc: ldr             x16, [x16, #0xaa0]
    // 0x81bcd0: StoreField: r0->field_13 = r16
    //     0x81bcd0: stur            w16, [x0, #0x13]
    // 0x81bcd4: r16 = Instance_NotificationSound
    //     0x81bcd4: add             x16, PP, #8, lsl #12  ; [pp+0x8aa8] Obj!NotificationSound@e30461
    //     0x81bcd8: ldr             x16, [x16, #0xaa8]
    // 0x81bcdc: ArrayStore: r0[0] = r16  ; List_4
    //     0x81bcdc: stur            w16, [x0, #0x17]
    // 0x81bce0: r1 = <NotificationSound>
    //     0x81bce0: add             x1, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0x81bce4: ldr             x1, [x1, #0x58]
    // 0x81bce8: r0 = AllocateGrowableArray()
    //     0x81bce8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x81bcec: mov             x1, x0
    // 0x81bcf0: ldur            x0, [fp, #-0x10]
    // 0x81bcf4: StoreField: r1->field_f = r0
    //     0x81bcf4: stur            w0, [x1, #0xf]
    // 0x81bcf8: r0 = 6
    //     0x81bcf8: movz            x0, #0x6
    // 0x81bcfc: StoreField: r1->field_b = r0
    //     0x81bcfc: stur            w0, [x1, #0xb]
    // 0x81bd00: ldur            x3, [fp, #-8]
    // 0x81bd04: LoadField: r4 = r3->field_13
    //     0x81bd04: ldur            w4, [x3, #0x13]
    // 0x81bd08: DecompressPointer r4
    //     0x81bd08: add             x4, x4, HEAP, lsl #32
    // 0x81bd0c: mov             x2, x4
    // 0x81bd10: stur            x4, [fp, #-0x10]
    // 0x81bd14: r0 = contains()
    //     0x81bd14: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x81bd18: tbnz            w0, #4, #0x81bd60
    // 0x81bd1c: ldur            x0, [fp, #-0x10]
    // 0x81bd20: r1 = Null
    //     0x81bd20: mov             x1, NULL
    // 0x81bd24: r2 = 6
    //     0x81bd24: movz            x2, #0x6
    // 0x81bd28: r0 = AllocateArray()
    //     0x81bd28: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81bd2c: r16 = "Payertime "
    //     0x81bd2c: add             x16, PP, #8, lsl #12  ; [pp+0x8ab0] "Payertime "
    //     0x81bd30: ldr             x16, [x16, #0xab0]
    // 0x81bd34: StoreField: r0->field_f = r16
    //     0x81bd34: stur            w16, [x0, #0xf]
    // 0x81bd38: ldur            x1, [fp, #-0x10]
    // 0x81bd3c: LoadField: r2 = r1->field_f
    //     0x81bd3c: ldur            w2, [x1, #0xf]
    // 0x81bd40: DecompressPointer r2
    //     0x81bd40: add             x2, x2, HEAP, lsl #32
    // 0x81bd44: StoreField: r0->field_13 = r2
    //     0x81bd44: stur            w2, [x0, #0x13]
    // 0x81bd48: r16 = " Fajr"
    //     0x81bd48: add             x16, PP, #8, lsl #12  ; [pp+0x8ab8] " Fajr"
    //     0x81bd4c: ldr             x16, [x16, #0xab8]
    // 0x81bd50: ArrayStore: r0[0] = r16  ; List_4
    //     0x81bd50: stur            w16, [x0, #0x17]
    // 0x81bd54: str             x0, [SP]
    // 0x81bd58: r0 = _interpolate()
    //     0x81bd58: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81bd5c: b               #0x81bd9c
    // 0x81bd60: ldur            x0, [fp, #-8]
    // 0x81bd64: r1 = Null
    //     0x81bd64: mov             x1, NULL
    // 0x81bd68: r2 = 4
    //     0x81bd68: movz            x2, #0x4
    // 0x81bd6c: r0 = AllocateArray()
    //     0x81bd6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81bd70: r16 = "Payertime "
    //     0x81bd70: add             x16, PP, #8, lsl #12  ; [pp+0x8ab0] "Payertime "
    //     0x81bd74: ldr             x16, [x16, #0xab0]
    // 0x81bd78: StoreField: r0->field_f = r16
    //     0x81bd78: stur            w16, [x0, #0xf]
    // 0x81bd7c: ldur            x1, [fp, #-8]
    // 0x81bd80: LoadField: r2 = r1->field_13
    //     0x81bd80: ldur            w2, [x1, #0x13]
    // 0x81bd84: DecompressPointer r2
    //     0x81bd84: add             x2, x2, HEAP, lsl #32
    // 0x81bd88: LoadField: r1 = r2->field_f
    //     0x81bd88: ldur            w1, [x2, #0xf]
    // 0x81bd8c: DecompressPointer r1
    //     0x81bd8c: add             x1, x1, HEAP, lsl #32
    // 0x81bd90: StoreField: r0->field_13 = r1
    //     0x81bd90: stur            w1, [x0, #0x13]
    // 0x81bd94: str             x0, [SP]
    // 0x81bd98: r0 = _interpolate()
    //     0x81bd98: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81bd9c: LeaveFrame
    //     0x81bd9c: mov             SP, fp
    //     0x81bda0: ldp             fp, lr, [SP], #0x10
    // 0x81bda4: ret
    //     0x81bda4: ret             
    // 0x81bda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81bda8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81bdac: b               #0x81bc90
  }
  get _ title(/* No info */) {
    // ** addr: 0x81bde0, size: 0x350
    // 0x81bde0: EnterFrame
    //     0x81bde0: stp             fp, lr, [SP, #-0x10]!
    //     0x81bde4: mov             fp, SP
    // 0x81bde8: AllocStack(0x30)
    //     0x81bde8: sub             SP, SP, #0x30
    // 0x81bdec: SetupParameters(PrayerTimeNotification this /* r1 => r0, fp-0x8 */)
    //     0x81bdec: mov             x0, x1
    //     0x81bdf0: stur            x1, [fp, #-8]
    // 0x81bdf4: CheckStackOverflow
    //     0x81bdf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81bdf8: cmp             SP, x16
    //     0x81bdfc: b.ls            #0x81c124
    // 0x81be00: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x81be00: ldur            w1, [x0, #0x17]
    // 0x81be04: DecompressPointer r1
    //     0x81be04: add             x1, x1, HEAP, lsl #32
    // 0x81be08: tbnz            w1, #4, #0x81bf64
    // 0x81be0c: r1 = Null
    //     0x81be0c: mov             x1, NULL
    // 0x81be10: r2 = 8
    //     0x81be10: movz            x2, #0x8
    // 0x81be14: r0 = AllocateArray()
    //     0x81be14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81be18: mov             x2, x0
    // 0x81be1c: stur            x2, [fp, #-0x10]
    // 0x81be20: r16 = "Waktu "
    //     0x81be20: add             x16, PP, #8, lsl #12  ; [pp+0x8ac0] "Waktu "
    //     0x81be24: ldr             x16, [x16, #0xac0]
    // 0x81be28: StoreField: r2->field_f = r16
    //     0x81be28: stur            w16, [x2, #0xf]
    // 0x81be2c: ldur            x3, [fp, #-8]
    // 0x81be30: LoadField: r0 = r3->field_7
    //     0x81be30: ldur            w0, [x3, #7]
    // 0x81be34: DecompressPointer r0
    //     0x81be34: add             x0, x0, HEAP, lsl #32
    // 0x81be38: LoadField: r4 = r0->field_7
    //     0x81be38: ldur            x4, [x0, #7]
    // 0x81be3c: cmp             x4, #4
    // 0x81be40: b.gt            #0x81bea8
    // 0x81be44: cmp             x4, #2
    // 0x81be48: b.gt            #0x81be88
    // 0x81be4c: cmp             x4, #1
    // 0x81be50: b.gt            #0x81be7c
    // 0x81be54: r0 = BoxInt64Instr(r4)
    //     0x81be54: sbfiz           x0, x4, #1, #0x1f
    //     0x81be58: cmp             x4, x0, asr #1
    //     0x81be5c: b.eq            #0x81be68
    //     0x81be60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81be64: stur            x4, [x0, #7]
    // 0x81be68: cmp             w0, #2
    // 0x81be6c: b.ne            #0x81bf0c
    // 0x81be70: r0 = "Subuh"
    //     0x81be70: add             x0, PP, #8, lsl #12  ; [pp+0x8688] "Subuh"
    //     0x81be74: ldr             x0, [x0, #0x688]
    // 0x81be78: b               #0x81bf14
    // 0x81be7c: r0 = "Terbit"
    //     0x81be7c: add             x0, PP, #8, lsl #12  ; [pp+0x8690] "Terbit"
    //     0x81be80: ldr             x0, [x0, #0x690]
    // 0x81be84: b               #0x81bf14
    // 0x81be88: cmp             x4, #3
    // 0x81be8c: b.gt            #0x81be9c
    // 0x81be90: r0 = "Dhuha"
    //     0x81be90: add             x0, PP, #8, lsl #12  ; [pp+0x8698] "Dhuha"
    //     0x81be94: ldr             x0, [x0, #0x698]
    // 0x81be98: b               #0x81bf14
    // 0x81be9c: r0 = "Zuhur"
    //     0x81be9c: add             x0, PP, #8, lsl #12  ; [pp+0x86a0] "Zuhur"
    //     0x81bea0: ldr             x0, [x0, #0x6a0]
    // 0x81bea4: b               #0x81bf14
    // 0x81bea8: cmp             x4, #6
    // 0x81beac: b.gt            #0x81bed0
    // 0x81beb0: cmp             x4, #5
    // 0x81beb4: b.gt            #0x81bec4
    // 0x81beb8: r0 = "Ashar"
    //     0x81beb8: add             x0, PP, #8, lsl #12  ; [pp+0x86a8] "Ashar"
    //     0x81bebc: ldr             x0, [x0, #0x6a8]
    // 0x81bec0: b               #0x81bf14
    // 0x81bec4: r0 = "Maghrib"
    //     0x81bec4: add             x0, PP, #8, lsl #12  ; [pp+0x86b0] "Maghrib"
    //     0x81bec8: ldr             x0, [x0, #0x6b0]
    // 0x81becc: b               #0x81bf14
    // 0x81bed0: cmp             x4, #7
    // 0x81bed4: b.gt            #0x81bee4
    // 0x81bed8: r0 = "Isya’"
    //     0x81bed8: add             x0, PP, #8, lsl #12  ; [pp+0x86b8] "Isya’"
    //     0x81bedc: ldr             x0, [x0, #0x6b8]
    // 0x81bee0: b               #0x81bf14
    // 0x81bee4: r0 = BoxInt64Instr(r4)
    //     0x81bee4: sbfiz           x0, x4, #1, #0x1f
    //     0x81bee8: cmp             x4, x0, asr #1
    //     0x81beec: b.eq            #0x81bef8
    //     0x81bef0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81bef4: stur            x4, [x0, #7]
    // 0x81bef8: cmp             w0, #0x10
    // 0x81befc: b.ne            #0x81bf0c
    // 0x81bf00: r0 = "Imsak"
    //     0x81bf00: add             x0, PP, #8, lsl #12  ; [pp+0x86c0] "Imsak"
    //     0x81bf04: ldr             x0, [x0, #0x6c0]
    // 0x81bf08: b               #0x81bf14
    // 0x81bf0c: r0 = "None"
    //     0x81bf0c: add             x0, PP, #8, lsl #12  ; [pp+0x86c8] "None"
    //     0x81bf10: ldr             x0, [x0, #0x6c8]
    // 0x81bf14: StoreField: r2->field_13 = r0
    //     0x81bf14: stur            w0, [x2, #0x13]
    // 0x81bf18: r16 = " "
    //     0x81bf18: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x81bf1c: ArrayStore: r2[0] = r16  ; List_4
    //     0x81bf1c: stur            w16, [x2, #0x17]
    // 0x81bf20: LoadField: r1 = r3->field_b
    //     0x81bf20: ldur            w1, [x3, #0xb]
    // 0x81bf24: DecompressPointer r1
    //     0x81bf24: add             x1, x1, HEAP, lsl #32
    // 0x81bf28: r0 = DateTimeExtensions.hm()
    //     0x81bf28: bl              #0x81c130  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.hm
    // 0x81bf2c: ldur            x1, [fp, #-0x10]
    // 0x81bf30: ArrayStore: r1[3] = r0  ; List_4
    //     0x81bf30: add             x25, x1, #0x1b
    //     0x81bf34: str             w0, [x25]
    //     0x81bf38: tbz             w0, #0, #0x81bf54
    //     0x81bf3c: ldurb           w16, [x1, #-1]
    //     0x81bf40: ldurb           w17, [x0, #-1]
    //     0x81bf44: and             x16, x17, x16, lsr #2
    //     0x81bf48: tst             x16, HEAP, lsr #32
    //     0x81bf4c: b.eq            #0x81bf54
    //     0x81bf50: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81bf54: ldur            x16, [fp, #-0x10]
    // 0x81bf58: str             x16, [SP]
    // 0x81bf5c: r0 = _interpolate()
    //     0x81bf5c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81bf60: b               #0x81c118
    // 0x81bf64: mov             x3, x0
    // 0x81bf68: LoadField: r0 = r3->field_1b
    //     0x81bf68: ldur            w0, [x3, #0x1b]
    // 0x81bf6c: DecompressPointer r0
    //     0x81bf6c: add             x0, x0, HEAP, lsl #32
    // 0x81bf70: stur            x0, [fp, #-0x10]
    // 0x81bf74: r1 = Null
    //     0x81bf74: mov             x1, NULL
    // 0x81bf78: r2 = 10
    //     0x81bf78: movz            x2, #0xa
    // 0x81bf7c: r0 = AllocateArray()
    //     0x81bf7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x81bf80: mov             x3, x0
    // 0x81bf84: ldur            x2, [fp, #-0x10]
    // 0x81bf88: stur            x3, [fp, #-0x28]
    // 0x81bf8c: StoreField: r3->field_f = r2
    //     0x81bf8c: stur            w2, [x3, #0xf]
    // 0x81bf90: r16 = " menit lagi "
    //     0x81bf90: add             x16, PP, #8, lsl #12  ; [pp+0x8ac8] " menit lagi "
    //     0x81bf94: ldr             x16, [x16, #0xac8]
    // 0x81bf98: StoreField: r3->field_13 = r16
    //     0x81bf98: stur            w16, [x3, #0x13]
    // 0x81bf9c: ldur            x4, [fp, #-8]
    // 0x81bfa0: LoadField: r0 = r4->field_7
    //     0x81bfa0: ldur            w0, [x4, #7]
    // 0x81bfa4: DecompressPointer r0
    //     0x81bfa4: add             x0, x0, HEAP, lsl #32
    // 0x81bfa8: LoadField: r5 = r0->field_7
    //     0x81bfa8: ldur            x5, [x0, #7]
    // 0x81bfac: cmp             x5, #4
    // 0x81bfb0: b.gt            #0x81c018
    // 0x81bfb4: cmp             x5, #2
    // 0x81bfb8: b.gt            #0x81bff8
    // 0x81bfbc: cmp             x5, #1
    // 0x81bfc0: b.gt            #0x81bfec
    // 0x81bfc4: r0 = BoxInt64Instr(r5)
    //     0x81bfc4: sbfiz           x0, x5, #1, #0x1f
    //     0x81bfc8: cmp             x5, x0, asr #1
    //     0x81bfcc: b.eq            #0x81bfd8
    //     0x81bfd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81bfd4: stur            x5, [x0, #7]
    // 0x81bfd8: cmp             w0, #2
    // 0x81bfdc: b.ne            #0x81c07c
    // 0x81bfe0: r0 = "Subuh"
    //     0x81bfe0: add             x0, PP, #8, lsl #12  ; [pp+0x8688] "Subuh"
    //     0x81bfe4: ldr             x0, [x0, #0x688]
    // 0x81bfe8: b               #0x81c084
    // 0x81bfec: r0 = "Terbit"
    //     0x81bfec: add             x0, PP, #8, lsl #12  ; [pp+0x8690] "Terbit"
    //     0x81bff0: ldr             x0, [x0, #0x690]
    // 0x81bff4: b               #0x81c084
    // 0x81bff8: cmp             x5, #3
    // 0x81bffc: b.gt            #0x81c00c
    // 0x81c000: r0 = "Dhuha"
    //     0x81c000: add             x0, PP, #8, lsl #12  ; [pp+0x8698] "Dhuha"
    //     0x81c004: ldr             x0, [x0, #0x698]
    // 0x81c008: b               #0x81c084
    // 0x81c00c: r0 = "Zuhur"
    //     0x81c00c: add             x0, PP, #8, lsl #12  ; [pp+0x86a0] "Zuhur"
    //     0x81c010: ldr             x0, [x0, #0x6a0]
    // 0x81c014: b               #0x81c084
    // 0x81c018: cmp             x5, #6
    // 0x81c01c: b.gt            #0x81c040
    // 0x81c020: cmp             x5, #5
    // 0x81c024: b.gt            #0x81c034
    // 0x81c028: r0 = "Ashar"
    //     0x81c028: add             x0, PP, #8, lsl #12  ; [pp+0x86a8] "Ashar"
    //     0x81c02c: ldr             x0, [x0, #0x6a8]
    // 0x81c030: b               #0x81c084
    // 0x81c034: r0 = "Maghrib"
    //     0x81c034: add             x0, PP, #8, lsl #12  ; [pp+0x86b0] "Maghrib"
    //     0x81c038: ldr             x0, [x0, #0x6b0]
    // 0x81c03c: b               #0x81c084
    // 0x81c040: cmp             x5, #7
    // 0x81c044: b.gt            #0x81c054
    // 0x81c048: r0 = "Isya’"
    //     0x81c048: add             x0, PP, #8, lsl #12  ; [pp+0x86b8] "Isya’"
    //     0x81c04c: ldr             x0, [x0, #0x6b8]
    // 0x81c050: b               #0x81c084
    // 0x81c054: r0 = BoxInt64Instr(r5)
    //     0x81c054: sbfiz           x0, x5, #1, #0x1f
    //     0x81c058: cmp             x5, x0, asr #1
    //     0x81c05c: b.eq            #0x81c068
    //     0x81c060: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81c064: stur            x5, [x0, #7]
    // 0x81c068: cmp             w0, #0x10
    // 0x81c06c: b.ne            #0x81c07c
    // 0x81c070: r0 = "Imsak"
    //     0x81c070: add             x0, PP, #8, lsl #12  ; [pp+0x86c0] "Imsak"
    //     0x81c074: ldr             x0, [x0, #0x6c0]
    // 0x81c078: b               #0x81c084
    // 0x81c07c: r0 = "None"
    //     0x81c07c: add             x0, PP, #8, lsl #12  ; [pp+0x86c8] "None"
    //     0x81c080: ldr             x0, [x0, #0x6c8]
    // 0x81c084: ArrayStore: r3[0] = r0  ; List_4
    //     0x81c084: stur            w0, [x3, #0x17]
    // 0x81c088: r16 = " "
    //     0x81c088: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0x81c08c: StoreField: r3->field_1b = r16
    //     0x81c08c: stur            w16, [x3, #0x1b]
    // 0x81c090: LoadField: r1 = r4->field_b
    //     0x81c090: ldur            w1, [x4, #0xb]
    // 0x81c094: DecompressPointer r1
    //     0x81c094: add             x1, x1, HEAP, lsl #32
    // 0x81c098: stur            x1, [fp, #-0x20]
    // 0x81c09c: cmp             w2, NULL
    // 0x81c0a0: b.eq            #0x81c12c
    // 0x81c0a4: r0 = LoadInt32Instr(r2)
    //     0x81c0a4: sbfx            x0, x2, #1, #0x1f
    //     0x81c0a8: tbz             w2, #0, #0x81c0b0
    //     0x81c0ac: ldur            x0, [x2, #7]
    // 0x81c0b0: r16 = 60000000
    //     0x81c0b0: movz            x16, #0x8700
    //     0x81c0b4: movk            x16, #0x393, lsl #16
    // 0x81c0b8: mul             x2, x0, x16
    // 0x81c0bc: stur            x2, [fp, #-0x18]
    // 0x81c0c0: r0 = Duration()
    //     0x81c0c0: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x81c0c4: mov             x1, x0
    // 0x81c0c8: ldur            x0, [fp, #-0x18]
    // 0x81c0cc: StoreField: r1->field_7 = r0
    //     0x81c0cc: stur            x0, [x1, #7]
    // 0x81c0d0: mov             x2, x1
    // 0x81c0d4: ldur            x1, [fp, #-0x20]
    // 0x81c0d8: r0 = add()
    //     0x81c0d8: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x81c0dc: mov             x1, x0
    // 0x81c0e0: r0 = DateTimeExtensions.hm()
    //     0x81c0e0: bl              #0x81c130  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.hm
    // 0x81c0e4: ldur            x1, [fp, #-0x28]
    // 0x81c0e8: ArrayStore: r1[4] = r0  ; List_4
    //     0x81c0e8: add             x25, x1, #0x1f
    //     0x81c0ec: str             w0, [x25]
    //     0x81c0f0: tbz             w0, #0, #0x81c10c
    //     0x81c0f4: ldurb           w16, [x1, #-1]
    //     0x81c0f8: ldurb           w17, [x0, #-1]
    //     0x81c0fc: and             x16, x17, x16, lsr #2
    //     0x81c100: tst             x16, HEAP, lsr #32
    //     0x81c104: b.eq            #0x81c10c
    //     0x81c108: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81c10c: ldur            x16, [fp, #-0x28]
    // 0x81c110: str             x16, [SP]
    // 0x81c114: r0 = _interpolate()
    //     0x81c114: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x81c118: LeaveFrame
    //     0x81c118: mov             SP, fp
    //     0x81c11c: ldp             fp, lr, [SP], #0x10
    // 0x81c120: ret
    //     0x81c120: ret             
    // 0x81c124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81c124: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81c128: b               #0x81be00
    // 0x81c12c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x81c12c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ sound(/* No info */) {
    // ** addr: 0x81db68, size: 0x1a0
    // 0x81db68: LoadField: r2 = r1->field_13
    //     0x81db68: ldur            w2, [x1, #0x13]
    // 0x81db6c: DecompressPointer r2
    //     0x81db6c: add             x2, x2, HEAP, lsl #32
    // 0x81db70: LoadField: r3 = r2->field_7
    //     0x81db70: ldur            x3, [x2, #7]
    // 0x81db74: cmp             x3, #6
    // 0x81db78: b.gt            #0x81dc1c
    // 0x81db7c: cmp             x3, #3
    // 0x81db80: b.gt            #0x81dbc4
    // 0x81db84: cmp             x3, #1
    // 0x81db88: b.gt            #0x81dba8
    // 0x81db8c: cmp             x3, #0
    // 0x81db90: b.gt            #0x81db9c
    // 0x81db94: r0 = Null
    //     0x81db94: mov             x0, NULL
    // 0x81db98: ret
    //     0x81db98: ret             
    // 0x81db9c: r0 = "silence"
    //     0x81db9c: add             x0, PP, #8, lsl #12  ; [pp+0x8678] "silence"
    //     0x81dba0: ldr             x0, [x0, #0x678]
    // 0x81dba4: ret
    //     0x81dba4: ret             
    // 0x81dba8: cmp             x3, #2
    // 0x81dbac: b.gt            #0x81dbb8
    // 0x81dbb0: r0 = "device"
    //     0x81dbb0: ldr             x0, [PP, #0x26b0]  ; [pp+0x26b0] "device"
    // 0x81dbb4: ret
    //     0x81dbb4: ret             
    // 0x81dbb8: r0 = "bedug"
    //     0x81dbb8: add             x0, PP, #8, lsl #12  ; [pp+0x8ca8] "bedug"
    //     0x81dbbc: ldr             x0, [x0, #0xca8]
    // 0x81dbc0: ret
    //     0x81dbc0: ret             
    // 0x81dbc4: cmp             x3, #5
    // 0x81dbc8: b.gt            #0x81dc10
    // 0x81dbcc: cmp             x3, #4
    // 0x81dbd0: b.gt            #0x81dbe0
    // 0x81dbd4: r0 = "kentongan"
    //     0x81dbd4: add             x0, PP, #8, lsl #12  ; [pp+0x8cb0] "kentongan"
    //     0x81dbd8: ldr             x0, [x0, #0xcb0]
    // 0x81dbdc: ret
    //     0x81dbdc: ret             
    // 0x81dbe0: LoadField: r2 = r1->field_7
    //     0x81dbe0: ldur            w2, [x1, #7]
    // 0x81dbe4: DecompressPointer r2
    //     0x81dbe4: add             x2, x2, HEAP, lsl #32
    // 0x81dbe8: r16 = Instance_Prayer
    //     0x81dbe8: add             x16, PP, #8, lsl #12  ; [pp+0x8230] Obj!Prayer@e38dc1
    //     0x81dbec: ldr             x16, [x16, #0x230]
    // 0x81dbf0: cmp             w2, w16
    // 0x81dbf4: b.ne            #0x81dc04
    // 0x81dbf8: r0 = "adzan_subuh_syech"
    //     0x81dbf8: add             x0, PP, #8, lsl #12  ; [pp+0x8cb8] "adzan_subuh_syech"
    //     0x81dbfc: ldr             x0, [x0, #0xcb8]
    // 0x81dc00: b               #0x81dc0c
    // 0x81dc04: r0 = "adzan_full_syech"
    //     0x81dc04: add             x0, PP, #8, lsl #12  ; [pp+0x8cc0] "adzan_full_syech"
    //     0x81dc08: ldr             x0, [x0, #0xcc0]
    // 0x81dc0c: ret
    //     0x81dc0c: ret             
    // 0x81dc10: r0 = "adzan_takbir_syech"
    //     0x81dc10: add             x0, PP, #8, lsl #12  ; [pp+0x8cc8] "adzan_takbir_syech"
    //     0x81dc14: ldr             x0, [x0, #0xcc8]
    // 0x81dc18: ret
    //     0x81dc18: ret             
    // 0x81dc1c: cmp             x3, #9
    // 0x81dc20: b.gt            #0x81dca0
    // 0x81dc24: cmp             x3, #8
    // 0x81dc28: b.gt            #0x81dc70
    // 0x81dc2c: cmp             x3, #7
    // 0x81dc30: b.gt            #0x81dc40
    // 0x81dc34: r0 = "tarhim_imsak"
    //     0x81dc34: add             x0, PP, #8, lsl #12  ; [pp+0x8cd0] "tarhim_imsak"
    //     0x81dc38: ldr             x0, [x0, #0xcd0]
    // 0x81dc3c: ret
    //     0x81dc3c: ret             
    // 0x81dc40: LoadField: r2 = r1->field_7
    //     0x81dc40: ldur            w2, [x1, #7]
    // 0x81dc44: DecompressPointer r2
    //     0x81dc44: add             x2, x2, HEAP, lsl #32
    // 0x81dc48: r16 = Instance_Prayer
    //     0x81dc48: add             x16, PP, #8, lsl #12  ; [pp+0x8230] Obj!Prayer@e38dc1
    //     0x81dc4c: ldr             x16, [x16, #0x230]
    // 0x81dc50: cmp             w2, w16
    // 0x81dc54: b.ne            #0x81dc64
    // 0x81dc58: r0 = "adzan_subuh_fikri"
    //     0x81dc58: add             x0, PP, #8, lsl #12  ; [pp+0x8cd8] "adzan_subuh_fikri"
    //     0x81dc5c: ldr             x0, [x0, #0xcd8]
    // 0x81dc60: b               #0x81dc6c
    // 0x81dc64: r0 = "adzan_full_fikri"
    //     0x81dc64: add             x0, PP, #8, lsl #12  ; [pp+0x8ce0] "adzan_full_fikri"
    //     0x81dc68: ldr             x0, [x0, #0xce0]
    // 0x81dc6c: ret
    //     0x81dc6c: ret             
    // 0x81dc70: LoadField: r2 = r1->field_7
    //     0x81dc70: ldur            w2, [x1, #7]
    // 0x81dc74: DecompressPointer r2
    //     0x81dc74: add             x2, x2, HEAP, lsl #32
    // 0x81dc78: r16 = Instance_Prayer
    //     0x81dc78: add             x16, PP, #8, lsl #12  ; [pp+0x8230] Obj!Prayer@e38dc1
    //     0x81dc7c: ldr             x16, [x16, #0x230]
    // 0x81dc80: cmp             w2, w16
    // 0x81dc84: b.ne            #0x81dc94
    // 0x81dc88: r0 = "adzan_subuh_rohani"
    //     0x81dc88: add             x0, PP, #8, lsl #12  ; [pp+0x8ce8] "adzan_subuh_rohani"
    //     0x81dc8c: ldr             x0, [x0, #0xce8]
    // 0x81dc90: b               #0x81dc9c
    // 0x81dc94: r0 = "adzan_full_rohani"
    //     0x81dc94: add             x0, PP, #8, lsl #12  ; [pp+0x8cf0] "adzan_full_rohani"
    //     0x81dc98: ldr             x0, [x0, #0xcf0]
    // 0x81dc9c: ret
    //     0x81dc9c: ret             
    // 0x81dca0: cmp             x3, #0xb
    // 0x81dca4: b.gt            #0x81dcc8
    // 0x81dca8: cmp             x3, #0xa
    // 0x81dcac: b.gt            #0x81dcbc
    // 0x81dcb0: r0 = "adzan_takbir_fikri_jiharkah"
    //     0x81dcb0: add             x0, PP, #8, lsl #12  ; [pp+0x8cf8] "adzan_takbir_fikri_jiharkah"
    //     0x81dcb4: ldr             x0, [x0, #0xcf8]
    // 0x81dcb8: ret
    //     0x81dcb8: ret             
    // 0x81dcbc: r0 = "adzan_takbir_fikri_nahawand"
    //     0x81dcbc: add             x0, PP, #8, lsl #12  ; [pp+0x8d00] "adzan_takbir_fikri_nahawand"
    //     0x81dcc0: ldr             x0, [x0, #0xd00]
    // 0x81dcc4: ret
    //     0x81dcc4: ret             
    // 0x81dcc8: r0 = BoxInt64Instr(r3)
    //     0x81dcc8: sbfiz           x0, x3, #1, #0x1f
    //     0x81dccc: cmp             x3, x0, asr #1
    //     0x81dcd0: b.eq            #0x81dcec
    //     0x81dcd4: stp             fp, lr, [SP, #-0x10]!
    //     0x81dcd8: mov             fp, SP
    //     0x81dcdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81dce0: mov             SP, fp
    //     0x81dce4: ldp             fp, lr, [SP], #0x10
    //     0x81dce8: stur            x3, [x0, #7]
    // 0x81dcec: cmp             w0, #0x18
    // 0x81dcf0: b.ne            #0x81dd00
    // 0x81dcf4: r0 = "adzan_takbir_rohani"
    //     0x81dcf4: add             x0, PP, #8, lsl #12  ; [pp+0x8d08] "adzan_takbir_rohani"
    //     0x81dcf8: ldr             x0, [x0, #0xd08]
    // 0x81dcfc: ret
    //     0x81dcfc: ret             
    // 0x81dd00: r0 = Null
    //     0x81dd00: mov             x0, NULL
    // 0x81dd04: ret
    //     0x81dd04: ret             
  }
}

// class id: 1138, size: 0x14, field offset: 0x8
class DonationNotification extends Object
    implements Notification {

  _ payload(/* No info */) {
    // ** addr: 0xecc3f4, size: 0x3c4
    // 0xecc3f4: EnterFrame
    //     0xecc3f4: stp             fp, lr, [SP, #-0x10]!
    //     0xecc3f8: mov             fp, SP
    // 0xecc3fc: AllocStack(0x30)
    //     0xecc3fc: sub             SP, SP, #0x30
    // 0xecc400: CheckStackOverflow
    //     0xecc400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecc404: cmp             SP, x16
    //     0xecc408: b.ls            #0xecc7b0
    // 0xecc40c: LoadField: r0 = r1->field_f
    //     0xecc40c: ldur            w0, [x1, #0xf]
    // 0xecc410: DecompressPointer r0
    //     0xecc410: add             x0, x0, HEAP, lsl #32
    // 0xecc414: mov             x1, x0
    // 0xecc418: stur            x0, [fp, #-8]
    // 0xecc41c: r2 = "id"
    //     0xecc41c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xecc420: ldr             x2, [x2, #0x740]
    // 0xecc424: r0 = containsKey()
    //     0xecc424: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xecc428: tbnz            w0, #4, #0xecc5c4
    // 0xecc42c: ldur            x0, [fp, #-8]
    // 0xecc430: r1 = Null
    //     0xecc430: mov             x1, NULL
    // 0xecc434: r2 = 8
    //     0xecc434: movz            x2, #0x8
    // 0xecc438: r0 = AllocateArray()
    //     0xecc438: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecc43c: stur            x0, [fp, #-0x10]
    // 0xecc440: r16 = "route"
    //     0xecc440: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc444: ldr             x16, [x16, #0x3a0]
    // 0xecc448: StoreField: r0->field_f = r16
    //     0xecc448: stur            w16, [x0, #0xf]
    // 0xecc44c: ldur            x1, [fp, #-8]
    // 0xecc450: r2 = "route"
    //     0xecc450: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc454: ldr             x2, [x2, #0x3a0]
    // 0xecc458: r0 = _getValueOrData()
    //     0xecc458: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xecc45c: ldur            x3, [fp, #-8]
    // 0xecc460: LoadField: r1 = r3->field_f
    //     0xecc460: ldur            w1, [x3, #0xf]
    // 0xecc464: DecompressPointer r1
    //     0xecc464: add             x1, x1, HEAP, lsl #32
    // 0xecc468: cmp             w1, w0
    // 0xecc46c: b.ne            #0xecc474
    // 0xecc470: r0 = Null
    //     0xecc470: mov             x0, NULL
    // 0xecc474: ldur            x4, [fp, #-0x10]
    // 0xecc478: mov             x1, x4
    // 0xecc47c: ArrayStore: r1[1] = r0  ; List_4
    //     0xecc47c: add             x25, x1, #0x13
    //     0xecc480: str             w0, [x25]
    //     0xecc484: tbz             w0, #0, #0xecc4a0
    //     0xecc488: ldurb           w16, [x1, #-1]
    //     0xecc48c: ldurb           w17, [x0, #-1]
    //     0xecc490: and             x16, x17, x16, lsr #2
    //     0xecc494: tst             x16, HEAP, lsr #32
    //     0xecc498: b.eq            #0xecc4a0
    //     0xecc49c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc4a0: r16 = "param"
    //     0xecc4a0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0xecc4a4: ldr             x16, [x16, #0x488]
    // 0xecc4a8: ArrayStore: r4[0] = r16  ; List_4
    //     0xecc4a8: stur            w16, [x4, #0x17]
    // 0xecc4ac: r1 = Null
    //     0xecc4ac: mov             x1, NULL
    // 0xecc4b0: r2 = 4
    //     0xecc4b0: movz            x2, #0x4
    // 0xecc4b4: r0 = AllocateArray()
    //     0xecc4b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecc4b8: stur            x0, [fp, #-0x18]
    // 0xecc4bc: r16 = "id"
    //     0xecc4bc: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xecc4c0: ldr             x16, [x16, #0x740]
    // 0xecc4c4: StoreField: r0->field_f = r16
    //     0xecc4c4: stur            w16, [x0, #0xf]
    // 0xecc4c8: ldur            x1, [fp, #-8]
    // 0xecc4cc: r2 = "id"
    //     0xecc4cc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xecc4d0: ldr             x2, [x2, #0x740]
    // 0xecc4d4: r0 = _getValueOrData()
    //     0xecc4d4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xecc4d8: mov             x1, x0
    // 0xecc4dc: ldur            x0, [fp, #-8]
    // 0xecc4e0: LoadField: r2 = r0->field_f
    //     0xecc4e0: ldur            w2, [x0, #0xf]
    // 0xecc4e4: DecompressPointer r2
    //     0xecc4e4: add             x2, x2, HEAP, lsl #32
    // 0xecc4e8: cmp             w2, w1
    // 0xecc4ec: b.ne            #0xecc4f8
    // 0xecc4f0: r3 = Null
    //     0xecc4f0: mov             x3, NULL
    // 0xecc4f4: b               #0xecc4fc
    // 0xecc4f8: mov             x3, x1
    // 0xecc4fc: mov             x0, x3
    // 0xecc500: stur            x3, [fp, #-0x20]
    // 0xecc504: r2 = Null
    //     0xecc504: mov             x2, NULL
    // 0xecc508: r1 = Null
    //     0xecc508: mov             x1, NULL
    // 0xecc50c: r4 = 60
    //     0xecc50c: movz            x4, #0x3c
    // 0xecc510: branchIfSmi(r0, 0xecc51c)
    //     0xecc510: tbz             w0, #0, #0xecc51c
    // 0xecc514: r4 = LoadClassIdInstr(r0)
    //     0xecc514: ldur            x4, [x0, #-1]
    //     0xecc518: ubfx            x4, x4, #0xc, #0x14
    // 0xecc51c: sub             x4, x4, #0x5e
    // 0xecc520: cmp             x4, #1
    // 0xecc524: b.ls            #0xecc538
    // 0xecc528: r8 = String
    //     0xecc528: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xecc52c: r3 = Null
    //     0xecc52c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12490] Null
    //     0xecc530: ldr             x3, [x3, #0x490]
    // 0xecc534: r0 = String()
    //     0xecc534: bl              #0xed43b0  ; IsType_String_Stub
    // 0xecc538: ldur            x1, [fp, #-0x20]
    // 0xecc53c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xecc53c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xecc540: r0 = tryParse()
    //     0xecc540: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0xecc544: ldur            x1, [fp, #-0x18]
    // 0xecc548: ArrayStore: r1[1] = r0  ; List_4
    //     0xecc548: add             x25, x1, #0x13
    //     0xecc54c: str             w0, [x25]
    //     0xecc550: tbz             w0, #0, #0xecc56c
    //     0xecc554: ldurb           w16, [x1, #-1]
    //     0xecc558: ldurb           w17, [x0, #-1]
    //     0xecc55c: and             x16, x17, x16, lsr #2
    //     0xecc560: tst             x16, HEAP, lsr #32
    //     0xecc564: b.eq            #0xecc56c
    //     0xecc568: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc56c: r16 = <String, int?>
    //     0xecc56c: add             x16, PP, #0x12, lsl #12  ; [pp+0x124a0] TypeArguments: <String, int?>
    //     0xecc570: ldr             x16, [x16, #0x4a0]
    // 0xecc574: ldur            lr, [fp, #-0x18]
    // 0xecc578: stp             lr, x16, [SP]
    // 0xecc57c: r0 = Map._fromLiteral()
    //     0xecc57c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecc580: ldur            x1, [fp, #-0x10]
    // 0xecc584: ArrayStore: r1[3] = r0  ; List_4
    //     0xecc584: add             x25, x1, #0x1b
    //     0xecc588: str             w0, [x25]
    //     0xecc58c: tbz             w0, #0, #0xecc5a8
    //     0xecc590: ldurb           w16, [x1, #-1]
    //     0xecc594: ldurb           w17, [x0, #-1]
    //     0xecc598: and             x16, x17, x16, lsr #2
    //     0xecc59c: tst             x16, HEAP, lsr #32
    //     0xecc5a0: b.eq            #0xecc5a8
    //     0xecc5a4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc5a8: r16 = <String, dynamic>
    //     0xecc5a8: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecc5ac: ldur            lr, [fp, #-0x10]
    // 0xecc5b0: stp             lr, x16, [SP]
    // 0xecc5b4: r0 = Map._fromLiteral()
    //     0xecc5b4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecc5b8: LeaveFrame
    //     0xecc5b8: mov             SP, fp
    //     0xecc5bc: ldp             fp, lr, [SP], #0x10
    // 0xecc5c0: ret
    //     0xecc5c0: ret             
    // 0xecc5c4: ldur            x0, [fp, #-8]
    // 0xecc5c8: mov             x1, x0
    // 0xecc5cc: r2 = "param"
    //     0xecc5cc: add             x2, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0xecc5d0: ldr             x2, [x2, #0x488]
    // 0xecc5d4: r0 = containsKey()
    //     0xecc5d4: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xecc5d8: tbnz            w0, #4, #0xecc6d4
    // 0xecc5dc: ldur            x0, [fp, #-8]
    // 0xecc5e0: r1 = Null
    //     0xecc5e0: mov             x1, NULL
    // 0xecc5e4: r2 = 8
    //     0xecc5e4: movz            x2, #0x8
    // 0xecc5e8: r0 = AllocateArray()
    //     0xecc5e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecc5ec: stur            x0, [fp, #-0x10]
    // 0xecc5f0: r16 = "route"
    //     0xecc5f0: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc5f4: ldr             x16, [x16, #0x3a0]
    // 0xecc5f8: StoreField: r0->field_f = r16
    //     0xecc5f8: stur            w16, [x0, #0xf]
    // 0xecc5fc: ldur            x1, [fp, #-8]
    // 0xecc600: r2 = "route"
    //     0xecc600: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc604: ldr             x2, [x2, #0x3a0]
    // 0xecc608: r0 = _getValueOrData()
    //     0xecc608: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xecc60c: ldur            x3, [fp, #-8]
    // 0xecc610: LoadField: r1 = r3->field_f
    //     0xecc610: ldur            w1, [x3, #0xf]
    // 0xecc614: DecompressPointer r1
    //     0xecc614: add             x1, x1, HEAP, lsl #32
    // 0xecc618: cmp             w1, w0
    // 0xecc61c: b.ne            #0xecc624
    // 0xecc620: r0 = Null
    //     0xecc620: mov             x0, NULL
    // 0xecc624: ldur            x4, [fp, #-0x10]
    // 0xecc628: mov             x1, x4
    // 0xecc62c: ArrayStore: r1[1] = r0  ; List_4
    //     0xecc62c: add             x25, x1, #0x13
    //     0xecc630: str             w0, [x25]
    //     0xecc634: tbz             w0, #0, #0xecc650
    //     0xecc638: ldurb           w16, [x1, #-1]
    //     0xecc63c: ldurb           w17, [x0, #-1]
    //     0xecc640: and             x16, x17, x16, lsr #2
    //     0xecc644: tst             x16, HEAP, lsr #32
    //     0xecc648: b.eq            #0xecc650
    //     0xecc64c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc650: r16 = "param"
    //     0xecc650: add             x16, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0xecc654: ldr             x16, [x16, #0x488]
    // 0xecc658: ArrayStore: r4[0] = r16  ; List_4
    //     0xecc658: stur            w16, [x4, #0x17]
    // 0xecc65c: mov             x1, x3
    // 0xecc660: r2 = "param"
    //     0xecc660: add             x2, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0xecc664: ldr             x2, [x2, #0x488]
    // 0xecc668: r0 = _getValueOrData()
    //     0xecc668: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xecc66c: mov             x1, x0
    // 0xecc670: ldur            x0, [fp, #-8]
    // 0xecc674: LoadField: r2 = r0->field_f
    //     0xecc674: ldur            w2, [x0, #0xf]
    // 0xecc678: DecompressPointer r2
    //     0xecc678: add             x2, x2, HEAP, lsl #32
    // 0xecc67c: cmp             w2, w1
    // 0xecc680: b.ne            #0xecc68c
    // 0xecc684: r0 = Null
    //     0xecc684: mov             x0, NULL
    // 0xecc688: b               #0xecc690
    // 0xecc68c: mov             x0, x1
    // 0xecc690: ldur            x1, [fp, #-0x10]
    // 0xecc694: ArrayStore: r1[3] = r0  ; List_4
    //     0xecc694: add             x25, x1, #0x1b
    //     0xecc698: str             w0, [x25]
    //     0xecc69c: tbz             w0, #0, #0xecc6b8
    //     0xecc6a0: ldurb           w16, [x1, #-1]
    //     0xecc6a4: ldurb           w17, [x0, #-1]
    //     0xecc6a8: and             x16, x17, x16, lsr #2
    //     0xecc6ac: tst             x16, HEAP, lsr #32
    //     0xecc6b0: b.eq            #0xecc6b8
    //     0xecc6b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc6b8: r16 = <String, dynamic>
    //     0xecc6b8: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecc6bc: ldur            lr, [fp, #-0x10]
    // 0xecc6c0: stp             lr, x16, [SP]
    // 0xecc6c4: r0 = Map._fromLiteral()
    //     0xecc6c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecc6c8: LeaveFrame
    //     0xecc6c8: mov             SP, fp
    //     0xecc6cc: ldp             fp, lr, [SP], #0x10
    // 0xecc6d0: ret
    //     0xecc6d0: ret             
    // 0xecc6d4: ldur            x0, [fp, #-8]
    // 0xecc6d8: r1 = Null
    //     0xecc6d8: mov             x1, NULL
    // 0xecc6dc: r2 = 8
    //     0xecc6dc: movz            x2, #0x8
    // 0xecc6e0: r0 = AllocateArray()
    //     0xecc6e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xecc6e4: stur            x0, [fp, #-0x10]
    // 0xecc6e8: r16 = "route"
    //     0xecc6e8: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc6ec: ldr             x16, [x16, #0x3a0]
    // 0xecc6f0: StoreField: r0->field_f = r16
    //     0xecc6f0: stur            w16, [x0, #0xf]
    // 0xecc6f4: ldur            x1, [fp, #-8]
    // 0xecc6f8: r2 = "route"
    //     0xecc6f8: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0xecc6fc: ldr             x2, [x2, #0x3a0]
    // 0xecc700: r0 = _getValueOrData()
    //     0xecc700: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xecc704: mov             x1, x0
    // 0xecc708: ldur            x0, [fp, #-8]
    // 0xecc70c: LoadField: r2 = r0->field_f
    //     0xecc70c: ldur            w2, [x0, #0xf]
    // 0xecc710: DecompressPointer r2
    //     0xecc710: add             x2, x2, HEAP, lsl #32
    // 0xecc714: cmp             w2, w1
    // 0xecc718: b.ne            #0xecc724
    // 0xecc71c: r0 = Null
    //     0xecc71c: mov             x0, NULL
    // 0xecc720: b               #0xecc728
    // 0xecc724: mov             x0, x1
    // 0xecc728: ldur            x2, [fp, #-0x10]
    // 0xecc72c: mov             x1, x2
    // 0xecc730: ArrayStore: r1[1] = r0  ; List_4
    //     0xecc730: add             x25, x1, #0x13
    //     0xecc734: str             w0, [x25]
    //     0xecc738: tbz             w0, #0, #0xecc754
    //     0xecc73c: ldurb           w16, [x1, #-1]
    //     0xecc740: ldurb           w17, [x0, #-1]
    //     0xecc744: and             x16, x17, x16, lsr #2
    //     0xecc748: tst             x16, HEAP, lsr #32
    //     0xecc74c: b.eq            #0xecc754
    //     0xecc750: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc754: r16 = "param"
    //     0xecc754: add             x16, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0xecc758: ldr             x16, [x16, #0x488]
    // 0xecc75c: ArrayStore: r2[0] = r16  ; List_4
    //     0xecc75c: stur            w16, [x2, #0x17]
    // 0xecc760: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0xecc764: stp             x16, NULL, [SP]
    // 0xecc768: r0 = Map._fromLiteral()
    //     0xecc768: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecc76c: ldur            x1, [fp, #-0x10]
    // 0xecc770: ArrayStore: r1[3] = r0  ; List_4
    //     0xecc770: add             x25, x1, #0x1b
    //     0xecc774: str             w0, [x25]
    //     0xecc778: tbz             w0, #0, #0xecc794
    //     0xecc77c: ldurb           w16, [x1, #-1]
    //     0xecc780: ldurb           w17, [x0, #-1]
    //     0xecc784: and             x16, x17, x16, lsr #2
    //     0xecc788: tst             x16, HEAP, lsr #32
    //     0xecc78c: b.eq            #0xecc794
    //     0xecc790: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xecc794: r16 = <String, dynamic>
    //     0xecc794: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xecc798: ldur            lr, [fp, #-0x10]
    // 0xecc79c: stp             lr, x16, [SP]
    // 0xecc7a0: r0 = Map._fromLiteral()
    //     0xecc7a0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xecc7a4: LeaveFrame
    //     0xecc7a4: mov             SP, fp
    //     0xecc7a8: ldp             fp, lr, [SP], #0x10
    // 0xecc7ac: ret
    //     0xecc7ac: ret             
    // 0xecc7b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecc7b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecc7b4: b               #0xecc40c
  }
  int dyn:get:id(DonationNotification) {
    // ** addr: 0xecc7d0, size: 0x20
    // 0xecc7d0: r0 = 12474
    //     0xecc7d0: movz            x0, #0x30ba
    // 0xecc7d4: ret
    //     0xecc7d4: ret             
  }
}

// class id: 1139, size: 0x10, field offset: 0x8
class QuranNotification extends Object
    implements Notification {

  int dyn:get:id(QuranNotification) {
    // ** addr: 0xb1ccb0, size: 0x20
    // 0xb1ccb0: r0 = 12472
    //     0xb1ccb0: movz            x0, #0x30b8
    // 0xb1ccb4: ret
    //     0xb1ccb4: ret             
  }
  get _ tzDateTime(/* No info */) {
    // ** addr: 0xb1ccb8, size: 0x78
    // 0xb1ccb8: EnterFrame
    //     0xb1ccb8: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ccbc: mov             fp, SP
    // 0xb1ccc0: AllocStack(0x10)
    //     0xb1ccc0: sub             SP, SP, #0x10
    // 0xb1ccc4: CheckStackOverflow
    //     0xb1ccc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ccc8: cmp             SP, x16
    //     0xb1cccc: b.ls            #0xb1cd1c
    // 0xb1ccd0: LoadField: r2 = r1->field_7
    //     0xb1ccd0: ldur            w2, [x1, #7]
    // 0xb1ccd4: DecompressPointer r2
    //     0xb1ccd4: add             x2, x2, HEAP, lsl #32
    // 0xb1ccd8: stur            x2, [fp, #-0x10]
    // 0xb1ccdc: r0 = LoadStaticField(0x12a4)
    //     0xb1ccdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1cce0: ldr             x0, [x0, #0x2548]
    //     0xb1cce4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1cce8: cmp             w0, w16
    // 0xb1ccec: b.eq            #0xb1cd24
    // 0xb1ccf0: stur            x0, [fp, #-8]
    // 0xb1ccf4: r0 = TZDateTime()
    //     0xb1ccf4: bl              #0x6fdf18  ; AllocateTZDateTimeStub -> TZDateTime (size=0x18)
    // 0xb1ccf8: mov             x1, x0
    // 0xb1ccfc: ldur            x2, [fp, #-0x10]
    // 0xb1cd00: ldur            x3, [fp, #-8]
    // 0xb1cd04: stur            x0, [fp, #-8]
    // 0xb1cd08: r0 = TZDateTime.from()
    //     0xb1cd08: bl              #0x6fd2cc  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime.from
    // 0xb1cd0c: ldur            x0, [fp, #-8]
    // 0xb1cd10: LeaveFrame
    //     0xb1cd10: mov             SP, fp
    //     0xb1cd14: ldp             fp, lr, [SP], #0x10
    // 0xb1cd18: ret
    //     0xb1cd18: ret             
    // 0xb1cd1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1cd1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1cd20: b               #0xb1ccd0
    // 0xb1cd24: r9 = _local
    //     0xb1cd24: add             x9, PP, #8, lsl #12  ; [pp+0x8718] Field <::._local@1194310200>: static late (offset: 0x12a4)
    //     0xb1cd28: ldr             x9, [x9, #0x718]
    // 0xb1cd2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1cd2c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ body(/* No info */) {
    // ** addr: 0xb1cd30, size: 0x90
    // 0xb1cd30: EnterFrame
    //     0xb1cd30: stp             fp, lr, [SP, #-0x10]!
    //     0xb1cd34: mov             fp, SP
    // 0xb1cd38: AllocStack(0x18)
    //     0xb1cd38: sub             SP, SP, #0x18
    // 0xb1cd3c: CheckStackOverflow
    //     0xb1cd3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1cd40: cmp             SP, x16
    //     0xb1cd44: b.ls            #0xb1cdb8
    // 0xb1cd48: LoadField: r0 = r1->field_b
    //     0xb1cd48: ldur            w0, [x1, #0xb]
    // 0xb1cd4c: DecompressPointer r0
    //     0xb1cd4c: add             x0, x0, HEAP, lsl #32
    // 0xb1cd50: stur            x0, [fp, #-0x10]
    // 0xb1cd54: LoadField: r3 = r0->field_2f
    //     0xb1cd54: ldur            w3, [x0, #0x2f]
    // 0xb1cd58: DecompressPointer r3
    //     0xb1cd58: add             x3, x3, HEAP, lsl #32
    // 0xb1cd5c: stur            x3, [fp, #-8]
    // 0xb1cd60: r1 = Null
    //     0xb1cd60: mov             x1, NULL
    // 0xb1cd64: r2 = 6
    //     0xb1cd64: movz            x2, #0x6
    // 0xb1cd68: r0 = AllocateArray()
    //     0xb1cd68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1cd6c: mov             x2, x0
    // 0xb1cd70: ldur            x0, [fp, #-8]
    // 0xb1cd74: StoreField: r2->field_f = r0
    //     0xb1cd74: stur            w0, [x2, #0xf]
    // 0xb1cd78: r16 = " Ayat "
    //     0xb1cd78: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b9d8] " Ayat "
    //     0xb1cd7c: ldr             x16, [x16, #0x9d8]
    // 0xb1cd80: StoreField: r2->field_13 = r16
    //     0xb1cd80: stur            w16, [x2, #0x13]
    // 0xb1cd84: ldur            x0, [fp, #-0x10]
    // 0xb1cd88: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xb1cd88: ldur            x3, [x0, #0x17]
    // 0xb1cd8c: r0 = BoxInt64Instr(r3)
    //     0xb1cd8c: sbfiz           x0, x3, #1, #0x1f
    //     0xb1cd90: cmp             x3, x0, asr #1
    //     0xb1cd94: b.eq            #0xb1cda0
    //     0xb1cd98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1cd9c: stur            x3, [x0, #7]
    // 0xb1cda0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1cda0: stur            w0, [x2, #0x17]
    // 0xb1cda4: str             x2, [SP]
    // 0xb1cda8: r0 = _interpolate()
    //     0xb1cda8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb1cdac: LeaveFrame
    //     0xb1cdac: mov             SP, fp
    //     0xb1cdb0: ldp             fp, lr, [SP], #0x10
    // 0xb1cdb4: ret
    //     0xb1cdb4: ret             
    // 0xb1cdb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1cdb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1cdbc: b               #0xb1cd48
  }
}

// class id: 1140, size: 0x8, field offset: 0x8
abstract class Notification extends Object {
}
