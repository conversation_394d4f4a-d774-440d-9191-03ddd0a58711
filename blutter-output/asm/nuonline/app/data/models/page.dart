// lib: , url: package:nuonline/app/data/models/page.dart

// class id: 1050035, size: 0x8
class :: {
}

// class id: 1136, size: 0x14, field offset: 0x8
class Page extends Object {

  factory _ Page.fromMap(/* No info */) {
    // ** addr: 0x8fd6f0, size: 0x218
    // 0x8fd6f0: EnterFrame
    //     0x8fd6f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd6f4: mov             fp, SP
    // 0x8fd6f8: AllocStack(0x20)
    //     0x8fd6f8: sub             SP, SP, #0x20
    // 0x8fd6fc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8fd6fc: mov             x3, x2
    //     0x8fd700: stur            x2, [fp, #-8]
    // 0x8fd704: CheckStackOverflow
    //     0x8fd704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd708: cmp             SP, x16
    //     0x8fd70c: b.ls            #0x8fd900
    // 0x8fd710: r0 = LoadClassIdInstr(r3)
    //     0x8fd710: ldur            x0, [x3, #-1]
    //     0x8fd714: ubfx            x0, x0, #0xc, #0x14
    // 0x8fd718: mov             x1, x3
    // 0x8fd71c: r2 = "id"
    //     0x8fd71c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fd720: ldr             x2, [x2, #0x740]
    // 0x8fd724: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fd724: sub             lr, x0, #0x114
    //     0x8fd728: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd72c: blr             lr
    // 0x8fd730: mov             x3, x0
    // 0x8fd734: r2 = Null
    //     0x8fd734: mov             x2, NULL
    // 0x8fd738: r1 = Null
    //     0x8fd738: mov             x1, NULL
    // 0x8fd73c: stur            x3, [fp, #-0x10]
    // 0x8fd740: branchIfSmi(r0, 0x8fd768)
    //     0x8fd740: tbz             w0, #0, #0x8fd768
    // 0x8fd744: r4 = LoadClassIdInstr(r0)
    //     0x8fd744: ldur            x4, [x0, #-1]
    //     0x8fd748: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd74c: sub             x4, x4, #0x3c
    // 0x8fd750: cmp             x4, #1
    // 0x8fd754: b.ls            #0x8fd768
    // 0x8fd758: r8 = int
    //     0x8fd758: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fd75c: r3 = Null
    //     0x8fd75c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f290] Null
    //     0x8fd760: ldr             x3, [x3, #0x290]
    // 0x8fd764: r0 = int()
    //     0x8fd764: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fd768: ldur            x3, [fp, #-8]
    // 0x8fd76c: r0 = LoadClassIdInstr(r3)
    //     0x8fd76c: ldur            x0, [x3, #-1]
    //     0x8fd770: ubfx            x0, x0, #0xc, #0x14
    // 0x8fd774: mov             x1, x3
    // 0x8fd778: r2 = "title"
    //     0x8fd778: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8fd77c: ldr             x2, [x2, #0x748]
    // 0x8fd780: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fd780: sub             lr, x0, #0x114
    //     0x8fd784: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd788: blr             lr
    // 0x8fd78c: r2 = Null
    //     0x8fd78c: mov             x2, NULL
    // 0x8fd790: r1 = Null
    //     0x8fd790: mov             x1, NULL
    // 0x8fd794: r4 = 60
    //     0x8fd794: movz            x4, #0x3c
    // 0x8fd798: branchIfSmi(r0, 0x8fd7a4)
    //     0x8fd798: tbz             w0, #0, #0x8fd7a4
    // 0x8fd79c: r4 = LoadClassIdInstr(r0)
    //     0x8fd79c: ldur            x4, [x0, #-1]
    //     0x8fd7a0: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd7a4: sub             x4, x4, #0x5e
    // 0x8fd7a8: cmp             x4, #1
    // 0x8fd7ac: b.ls            #0x8fd7c0
    // 0x8fd7b0: r8 = String
    //     0x8fd7b0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fd7b4: r3 = Null
    //     0x8fd7b4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f2a0] Null
    //     0x8fd7b8: ldr             x3, [x3, #0x2a0]
    // 0x8fd7bc: r0 = String()
    //     0x8fd7bc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fd7c0: ldur            x3, [fp, #-8]
    // 0x8fd7c4: r0 = LoadClassIdInstr(r3)
    //     0x8fd7c4: ldur            x0, [x3, #-1]
    //     0x8fd7c8: ubfx            x0, x0, #0xc, #0x14
    // 0x8fd7cc: mov             x1, x3
    // 0x8fd7d0: r2 = "url"
    //     0x8fd7d0: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x8fd7d4: ldr             x2, [x2, #0xd78]
    // 0x8fd7d8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fd7d8: sub             lr, x0, #0x114
    //     0x8fd7dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd7e0: blr             lr
    // 0x8fd7e4: r2 = Null
    //     0x8fd7e4: mov             x2, NULL
    // 0x8fd7e8: r1 = Null
    //     0x8fd7e8: mov             x1, NULL
    // 0x8fd7ec: r4 = 60
    //     0x8fd7ec: movz            x4, #0x3c
    // 0x8fd7f0: branchIfSmi(r0, 0x8fd7fc)
    //     0x8fd7f0: tbz             w0, #0, #0x8fd7fc
    // 0x8fd7f4: r4 = LoadClassIdInstr(r0)
    //     0x8fd7f4: ldur            x4, [x0, #-1]
    //     0x8fd7f8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd7fc: sub             x4, x4, #0x5e
    // 0x8fd800: cmp             x4, #1
    // 0x8fd804: b.ls            #0x8fd818
    // 0x8fd808: r8 = String
    //     0x8fd808: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fd80c: r3 = Null
    //     0x8fd80c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f2b0] Null
    //     0x8fd810: ldr             x3, [x3, #0x2b0]
    // 0x8fd814: r0 = String()
    //     0x8fd814: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fd818: ldur            x3, [fp, #-8]
    // 0x8fd81c: r0 = LoadClassIdInstr(r3)
    //     0x8fd81c: ldur            x0, [x3, #-1]
    //     0x8fd820: ubfx            x0, x0, #0xc, #0x14
    // 0x8fd824: mov             x1, x3
    // 0x8fd828: r2 = "content"
    //     0x8fd828: add             x2, PP, #0x19, lsl #12  ; [pp+0x19f58] "content"
    //     0x8fd82c: ldr             x2, [x2, #0xf58]
    // 0x8fd830: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fd830: sub             lr, x0, #0x114
    //     0x8fd834: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd838: blr             lr
    // 0x8fd83c: mov             x3, x0
    // 0x8fd840: r2 = Null
    //     0x8fd840: mov             x2, NULL
    // 0x8fd844: r1 = Null
    //     0x8fd844: mov             x1, NULL
    // 0x8fd848: stur            x3, [fp, #-0x18]
    // 0x8fd84c: r4 = 60
    //     0x8fd84c: movz            x4, #0x3c
    // 0x8fd850: branchIfSmi(r0, 0x8fd85c)
    //     0x8fd850: tbz             w0, #0, #0x8fd85c
    // 0x8fd854: r4 = LoadClassIdInstr(r0)
    //     0x8fd854: ldur            x4, [x0, #-1]
    //     0x8fd858: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd85c: sub             x4, x4, #0x5e
    // 0x8fd860: cmp             x4, #1
    // 0x8fd864: b.ls            #0x8fd878
    // 0x8fd868: r8 = String
    //     0x8fd868: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fd86c: r3 = Null
    //     0x8fd86c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f2c0] Null
    //     0x8fd870: ldr             x3, [x3, #0x2c0]
    // 0x8fd874: r0 = String()
    //     0x8fd874: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fd878: ldur            x1, [fp, #-8]
    // 0x8fd87c: r0 = LoadClassIdInstr(r1)
    //     0x8fd87c: ldur            x0, [x1, #-1]
    //     0x8fd880: ubfx            x0, x0, #0xc, #0x14
    // 0x8fd884: r2 = "date"
    //     0x8fd884: add             x2, PP, #9, lsl #12  ; [pp+0x91a8] "date"
    //     0x8fd888: ldr             x2, [x2, #0x1a8]
    // 0x8fd88c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fd88c: sub             lr, x0, #0x114
    //     0x8fd890: ldr             lr, [x21, lr, lsl #3]
    //     0x8fd894: blr             lr
    // 0x8fd898: r2 = Null
    //     0x8fd898: mov             x2, NULL
    // 0x8fd89c: r1 = Null
    //     0x8fd89c: mov             x1, NULL
    // 0x8fd8a0: r4 = 60
    //     0x8fd8a0: movz            x4, #0x3c
    // 0x8fd8a4: branchIfSmi(r0, 0x8fd8b0)
    //     0x8fd8a4: tbz             w0, #0, #0x8fd8b0
    // 0x8fd8a8: r4 = LoadClassIdInstr(r0)
    //     0x8fd8a8: ldur            x4, [x0, #-1]
    //     0x8fd8ac: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd8b0: sub             x4, x4, #0x5e
    // 0x8fd8b4: cmp             x4, #1
    // 0x8fd8b8: b.ls            #0x8fd8cc
    // 0x8fd8bc: r8 = String
    //     0x8fd8bc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fd8c0: r3 = Null
    //     0x8fd8c0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f2d0] Null
    //     0x8fd8c4: ldr             x3, [x3, #0x2d0]
    // 0x8fd8c8: r0 = String()
    //     0x8fd8c8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8fd8cc: ldur            x0, [fp, #-0x10]
    // 0x8fd8d0: r1 = LoadInt32Instr(r0)
    //     0x8fd8d0: sbfx            x1, x0, #1, #0x1f
    //     0x8fd8d4: tbz             w0, #0, #0x8fd8dc
    //     0x8fd8d8: ldur            x1, [x0, #7]
    // 0x8fd8dc: stur            x1, [fp, #-0x20]
    // 0x8fd8e0: r0 = Page()
    //     0x8fd8e0: bl              #0x8fd908  ; AllocatePageStub -> Page (size=0x14)
    // 0x8fd8e4: ldur            x1, [fp, #-0x20]
    // 0x8fd8e8: StoreField: r0->field_7 = r1
    //     0x8fd8e8: stur            x1, [x0, #7]
    // 0x8fd8ec: ldur            x1, [fp, #-0x18]
    // 0x8fd8f0: StoreField: r0->field_f = r1
    //     0x8fd8f0: stur            w1, [x0, #0xf]
    // 0x8fd8f4: LeaveFrame
    //     0x8fd8f4: mov             SP, fp
    //     0x8fd8f8: ldp             fp, lr, [SP], #0x10
    // 0x8fd8fc: ret
    //     0x8fd8fc: ret             
    // 0x8fd900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd904: b               #0x8fd710
  }
}
