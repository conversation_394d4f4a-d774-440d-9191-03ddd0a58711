// lib: , url: package:nuonline/app/data/models/playlist.dart

// class id: 1050036, size: 0x8
class :: {
}

// class id: 5577, size: 0x20, field offset: 0x8
//   const constructor, 
class Playlist extends Equatable {

  factory _ Playlist.fromMap(/* No info */) {
    // ** addr: 0x925740, size: 0x218
    // 0x925740: EnterFrame
    //     0x925740: stp             fp, lr, [SP, #-0x10]!
    //     0x925744: mov             fp, SP
    // 0x925748: AllocStack(0x28)
    //     0x925748: sub             SP, SP, #0x28
    // 0x92574c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x92574c: mov             x3, x2
    //     0x925750: stur            x2, [fp, #-8]
    // 0x925754: CheckStackOverflow
    //     0x925754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925758: cmp             SP, x16
    //     0x92575c: b.ls            #0x925950
    // 0x925760: r0 = LoadClassIdInstr(r3)
    //     0x925760: ldur            x0, [x3, #-1]
    //     0x925764: ubfx            x0, x0, #0xc, #0x14
    // 0x925768: mov             x1, x3
    // 0x92576c: r2 = "id"
    //     0x92576c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x925770: ldr             x2, [x2, #0x740]
    // 0x925774: r0 = GDT[cid_x0 + -0x114]()
    //     0x925774: sub             lr, x0, #0x114
    //     0x925778: ldr             lr, [x21, lr, lsl #3]
    //     0x92577c: blr             lr
    // 0x925780: mov             x3, x0
    // 0x925784: r2 = Null
    //     0x925784: mov             x2, NULL
    // 0x925788: r1 = Null
    //     0x925788: mov             x1, NULL
    // 0x92578c: stur            x3, [fp, #-0x10]
    // 0x925790: branchIfSmi(r0, 0x9257b8)
    //     0x925790: tbz             w0, #0, #0x9257b8
    // 0x925794: r4 = LoadClassIdInstr(r0)
    //     0x925794: ldur            x4, [x0, #-1]
    //     0x925798: ubfx            x4, x4, #0xc, #0x14
    // 0x92579c: sub             x4, x4, #0x3c
    // 0x9257a0: cmp             x4, #1
    // 0x9257a4: b.ls            #0x9257b8
    // 0x9257a8: r8 = int
    //     0x9257a8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x9257ac: r3 = Null
    //     0x9257ac: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f58] Null
    //     0x9257b0: ldr             x3, [x3, #0xf58]
    // 0x9257b4: r0 = int()
    //     0x9257b4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x9257b8: ldur            x3, [fp, #-8]
    // 0x9257bc: r0 = LoadClassIdInstr(r3)
    //     0x9257bc: ldur            x0, [x3, #-1]
    //     0x9257c0: ubfx            x0, x0, #0xc, #0x14
    // 0x9257c4: mov             x1, x3
    // 0x9257c8: r2 = "name"
    //     0x9257c8: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x9257cc: r0 = GDT[cid_x0 + -0x114]()
    //     0x9257cc: sub             lr, x0, #0x114
    //     0x9257d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9257d4: blr             lr
    // 0x9257d8: mov             x3, x0
    // 0x9257dc: r2 = Null
    //     0x9257dc: mov             x2, NULL
    // 0x9257e0: r1 = Null
    //     0x9257e0: mov             x1, NULL
    // 0x9257e4: stur            x3, [fp, #-0x18]
    // 0x9257e8: r4 = 60
    //     0x9257e8: movz            x4, #0x3c
    // 0x9257ec: branchIfSmi(r0, 0x9257f8)
    //     0x9257ec: tbz             w0, #0, #0x9257f8
    // 0x9257f0: r4 = LoadClassIdInstr(r0)
    //     0x9257f0: ldur            x4, [x0, #-1]
    //     0x9257f4: ubfx            x4, x4, #0xc, #0x14
    // 0x9257f8: sub             x4, x4, #0x5e
    // 0x9257fc: cmp             x4, #1
    // 0x925800: b.ls            #0x925814
    // 0x925804: r8 = String
    //     0x925804: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x925808: r3 = Null
    //     0x925808: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f68] Null
    //     0x92580c: ldr             x3, [x3, #0xf68]
    // 0x925810: r0 = String()
    //     0x925810: bl              #0xed43b0  ; IsType_String_Stub
    // 0x925814: ldur            x3, [fp, #-8]
    // 0x925818: r0 = LoadClassIdInstr(r3)
    //     0x925818: ldur            x0, [x3, #-1]
    //     0x92581c: ubfx            x0, x0, #0xc, #0x14
    // 0x925820: mov             x1, x3
    // 0x925824: r2 = "total"
    //     0x925824: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0x925828: ldr             x2, [x2, #0xdc0]
    // 0x92582c: r0 = GDT[cid_x0 + -0x114]()
    //     0x92582c: sub             lr, x0, #0x114
    //     0x925830: ldr             lr, [x21, lr, lsl #3]
    //     0x925834: blr             lr
    // 0x925838: mov             x3, x0
    // 0x92583c: r2 = Null
    //     0x92583c: mov             x2, NULL
    // 0x925840: r1 = Null
    //     0x925840: mov             x1, NULL
    // 0x925844: stur            x3, [fp, #-0x20]
    // 0x925848: branchIfSmi(r0, 0x925870)
    //     0x925848: tbz             w0, #0, #0x925870
    // 0x92584c: r4 = LoadClassIdInstr(r0)
    //     0x92584c: ldur            x4, [x0, #-1]
    //     0x925850: ubfx            x4, x4, #0xc, #0x14
    // 0x925854: sub             x4, x4, #0x3c
    // 0x925858: cmp             x4, #1
    // 0x92585c: b.ls            #0x925870
    // 0x925860: r8 = int
    //     0x925860: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x925864: r3 = Null
    //     0x925864: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f78] Null
    //     0x925868: ldr             x3, [x3, #0xf78]
    // 0x92586c: r0 = int()
    //     0x92586c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x925870: ldur            x3, [fp, #-8]
    // 0x925874: r0 = LoadClassIdInstr(r3)
    //     0x925874: ldur            x0, [x3, #-1]
    //     0x925878: ubfx            x0, x0, #0xc, #0x14
    // 0x92587c: mov             x1, x3
    // 0x925880: r2 = "featured"
    //     0x925880: add             x2, PP, #0x31, lsl #12  ; [pp+0x31f88] "featured"
    //     0x925884: ldr             x2, [x2, #0xf88]
    // 0x925888: r0 = GDT[cid_x0 + -0x114]()
    //     0x925888: sub             lr, x0, #0x114
    //     0x92588c: ldr             lr, [x21, lr, lsl #3]
    //     0x925890: blr             lr
    // 0x925894: cmp             w0, NULL
    // 0x925898: b.ne            #0x9258a4
    // 0x92589c: r3 = Null
    //     0x92589c: mov             x3, NULL
    // 0x9258a0: b               #0x9258f4
    // 0x9258a4: ldur            x1, [fp, #-8]
    // 0x9258a8: r0 = LoadClassIdInstr(r1)
    //     0x9258a8: ldur            x0, [x1, #-1]
    //     0x9258ac: ubfx            x0, x0, #0xc, #0x14
    // 0x9258b0: r2 = "featured"
    //     0x9258b0: add             x2, PP, #0x31, lsl #12  ; [pp+0x31f88] "featured"
    //     0x9258b4: ldr             x2, [x2, #0xf88]
    // 0x9258b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9258b8: sub             lr, x0, #0x114
    //     0x9258bc: ldr             lr, [x21, lr, lsl #3]
    //     0x9258c0: blr             lr
    // 0x9258c4: mov             x3, x0
    // 0x9258c8: r2 = Null
    //     0x9258c8: mov             x2, NULL
    // 0x9258cc: r1 = Null
    //     0x9258cc: mov             x1, NULL
    // 0x9258d0: stur            x3, [fp, #-8]
    // 0x9258d4: r8 = Map<String, dynamic>
    //     0x9258d4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x9258d8: r3 = Null
    //     0x9258d8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f90] Null
    //     0x9258dc: ldr             x3, [x3, #0xf90]
    // 0x9258e0: r0 = Map<String, dynamic>()
    //     0x9258e0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9258e4: ldur            x2, [fp, #-8]
    // 0x9258e8: r1 = Null
    //     0x9258e8: mov             x1, NULL
    // 0x9258ec: r0 = Video.fromMap()
    //     0x9258ec: bl              #0x9066f4  ; [package:nuonline/app/data/models/video.dart] Video::Video.fromMap
    // 0x9258f0: mov             x3, x0
    // 0x9258f4: ldur            x2, [fp, #-0x10]
    // 0x9258f8: ldur            x1, [fp, #-0x18]
    // 0x9258fc: ldur            x0, [fp, #-0x20]
    // 0x925900: stur            x3, [fp, #-8]
    // 0x925904: r4 = LoadInt32Instr(r2)
    //     0x925904: sbfx            x4, x2, #1, #0x1f
    //     0x925908: tbz             w2, #0, #0x925910
    //     0x92590c: ldur            x4, [x2, #7]
    // 0x925910: stur            x4, [fp, #-0x28]
    // 0x925914: r0 = Playlist()
    //     0x925914: bl              #0x925958  ; AllocatePlaylistStub -> Playlist (size=0x20)
    // 0x925918: ldur            x1, [fp, #-0x28]
    // 0x92591c: StoreField: r0->field_7 = r1
    //     0x92591c: stur            x1, [x0, #7]
    // 0x925920: ldur            x1, [fp, #-0x18]
    // 0x925924: StoreField: r0->field_f = r1
    //     0x925924: stur            w1, [x0, #0xf]
    // 0x925928: ldur            x1, [fp, #-0x20]
    // 0x92592c: r2 = LoadInt32Instr(r1)
    //     0x92592c: sbfx            x2, x1, #1, #0x1f
    //     0x925930: tbz             w1, #0, #0x925938
    //     0x925934: ldur            x2, [x1, #7]
    // 0x925938: StoreField: r0->field_13 = r2
    //     0x925938: stur            x2, [x0, #0x13]
    // 0x92593c: ldur            x1, [fp, #-8]
    // 0x925940: StoreField: r0->field_1b = r1
    //     0x925940: stur            w1, [x0, #0x1b]
    // 0x925944: LeaveFrame
    //     0x925944: mov             SP, fp
    //     0x925948: ldp             fp, lr, [SP], #0x10
    // 0x92594c: ret
    //     0x92594c: ret             
    // 0x925950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925954: b               #0x925760
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe3444c, size: 0x188
    // 0xe3444c: EnterFrame
    //     0xe3444c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34450: mov             fp, SP
    // 0xe34454: AllocStack(0x20)
    //     0xe34454: sub             SP, SP, #0x20
    // 0xe34458: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe34458: mov             x3, x1
    //     0xe3445c: stur            x1, [fp, #-8]
    // 0xe34460: CheckStackOverflow
    //     0xe34460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34464: cmp             SP, x16
    //     0xe34468: b.ls            #0xe345cc
    // 0xe3446c: mov             x0, x3
    // 0xe34470: r2 = Null
    //     0xe34470: mov             x2, NULL
    // 0xe34474: r1 = Null
    //     0xe34474: mov             x1, NULL
    // 0xe34478: cmp             w0, NULL
    // 0xe3447c: b.eq            #0xe34520
    // 0xe34480: branchIfSmi(r0, 0xe34520)
    //     0xe34480: tbz             w0, #0, #0xe34520
    // 0xe34484: r3 = LoadClassIdInstr(r0)
    //     0xe34484: ldur            x3, [x0, #-1]
    //     0xe34488: ubfx            x3, x3, #0xc, #0x14
    // 0xe3448c: r17 = 6718
    //     0xe3448c: movz            x17, #0x1a3e
    // 0xe34490: cmp             x3, x17
    // 0xe34494: b.eq            #0xe34528
    // 0xe34498: sub             x3, x3, #0x5a
    // 0xe3449c: cmp             x3, #2
    // 0xe344a0: b.ls            #0xe34528
    // 0xe344a4: r4 = LoadClassIdInstr(r0)
    //     0xe344a4: ldur            x4, [x0, #-1]
    //     0xe344a8: ubfx            x4, x4, #0xc, #0x14
    // 0xe344ac: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe344b0: ldr             x3, [x3, #0x18]
    // 0xe344b4: ldr             x3, [x3, x4, lsl #3]
    // 0xe344b8: LoadField: r3 = r3->field_2b
    //     0xe344b8: ldur            w3, [x3, #0x2b]
    // 0xe344bc: DecompressPointer r3
    //     0xe344bc: add             x3, x3, HEAP, lsl #32
    // 0xe344c0: cmp             w3, NULL
    // 0xe344c4: b.eq            #0xe34520
    // 0xe344c8: LoadField: r3 = r3->field_f
    //     0xe344c8: ldur            w3, [x3, #0xf]
    // 0xe344cc: lsr             x3, x3, #3
    // 0xe344d0: r17 = 6718
    //     0xe344d0: movz            x17, #0x1a3e
    // 0xe344d4: cmp             x3, x17
    // 0xe344d8: b.eq            #0xe34528
    // 0xe344dc: r3 = SubtypeTestCache
    //     0xe344dc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f28] SubtypeTestCache
    //     0xe344e0: ldr             x3, [x3, #0xf28]
    // 0xe344e4: r30 = Subtype1TestCacheStub
    //     0xe344e4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe344e8: LoadField: r30 = r30->field_7
    //     0xe344e8: ldur            lr, [lr, #7]
    // 0xe344ec: blr             lr
    // 0xe344f0: cmp             w7, NULL
    // 0xe344f4: b.eq            #0xe34500
    // 0xe344f8: tbnz            w7, #4, #0xe34520
    // 0xe344fc: b               #0xe34528
    // 0xe34500: r8 = List
    //     0xe34500: add             x8, PP, #0x31, lsl #12  ; [pp+0x31f30] Type: List
    //     0xe34504: ldr             x8, [x8, #0xf30]
    // 0xe34508: r3 = SubtypeTestCache
    //     0xe34508: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f38] SubtypeTestCache
    //     0xe3450c: ldr             x3, [x3, #0xf38]
    // 0xe34510: r30 = InstanceOfStub
    //     0xe34510: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe34514: LoadField: r30 = r30->field_7
    //     0xe34514: ldur            lr, [lr, #7]
    // 0xe34518: blr             lr
    // 0xe3451c: b               #0xe3452c
    // 0xe34520: r0 = false
    //     0xe34520: add             x0, NULL, #0x30  ; false
    // 0xe34524: b               #0xe3452c
    // 0xe34528: r0 = true
    //     0xe34528: add             x0, NULL, #0x20  ; true
    // 0xe3452c: tbnz            w0, #4, #0xe345b0
    // 0xe34530: ldur            x0, [fp, #-8]
    // 0xe34534: r1 = Function '<anonymous closure>': static.
    //     0xe34534: add             x1, PP, #0x31, lsl #12  ; [pp+0x31f40] AnonymousClosure: static (0xe345d4), in [package:nuonline/app/data/models/playlist.dart] Playlist::fromResponse (0xe3444c)
    //     0xe34538: ldr             x1, [x1, #0xf40]
    // 0xe3453c: r2 = Null
    //     0xe3453c: mov             x2, NULL
    // 0xe34540: r0 = AllocateClosure()
    //     0xe34540: bl              #0xec1630  ; AllocateClosureStub
    // 0xe34544: mov             x1, x0
    // 0xe34548: ldur            x0, [fp, #-8]
    // 0xe3454c: r2 = LoadClassIdInstr(r0)
    //     0xe3454c: ldur            x2, [x0, #-1]
    //     0xe34550: ubfx            x2, x2, #0xc, #0x14
    // 0xe34554: r16 = <Playlist>
    //     0xe34554: add             x16, PP, #0x29, lsl #12  ; [pp+0x290d0] TypeArguments: <Playlist>
    //     0xe34558: ldr             x16, [x16, #0xd0]
    // 0xe3455c: stp             x0, x16, [SP, #8]
    // 0xe34560: str             x1, [SP]
    // 0xe34564: mov             x0, x2
    // 0xe34568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe34568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe3456c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe3456c: movz            x17, #0xf28c
    //     0xe34570: add             lr, x0, x17
    //     0xe34574: ldr             lr, [x21, lr, lsl #3]
    //     0xe34578: blr             lr
    // 0xe3457c: r1 = LoadClassIdInstr(r0)
    //     0xe3457c: ldur            x1, [x0, #-1]
    //     0xe34580: ubfx            x1, x1, #0xc, #0x14
    // 0xe34584: mov             x16, x0
    // 0xe34588: mov             x0, x1
    // 0xe3458c: mov             x1, x16
    // 0xe34590: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe34590: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe34594: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe34594: movz            x17, #0xd889
    //     0xe34598: add             lr, x0, x17
    //     0xe3459c: ldr             lr, [x21, lr, lsl #3]
    //     0xe345a0: blr             lr
    // 0xe345a4: LeaveFrame
    //     0xe345a4: mov             SP, fp
    //     0xe345a8: ldp             fp, lr, [SP], #0x10
    // 0xe345ac: ret
    //     0xe345ac: ret             
    // 0xe345b0: r1 = <Playlist>
    //     0xe345b0: add             x1, PP, #0x29, lsl #12  ; [pp+0x290d0] TypeArguments: <Playlist>
    //     0xe345b4: ldr             x1, [x1, #0xd0]
    // 0xe345b8: r2 = 0
    //     0xe345b8: movz            x2, #0
    // 0xe345bc: r0 = _GrowableList()
    //     0xe345bc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe345c0: LeaveFrame
    //     0xe345c0: mov             SP, fp
    //     0xe345c4: ldp             fp, lr, [SP], #0x10
    // 0xe345c8: ret
    //     0xe345c8: ret             
    // 0xe345cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe345cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe345d0: b               #0xe3446c
  }
  [closure] static Playlist <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe345d4, size: 0x50
    // 0xe345d4: EnterFrame
    //     0xe345d4: stp             fp, lr, [SP, #-0x10]!
    //     0xe345d8: mov             fp, SP
    // 0xe345dc: CheckStackOverflow
    //     0xe345dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe345e0: cmp             SP, x16
    //     0xe345e4: b.ls            #0xe3461c
    // 0xe345e8: ldr             x0, [fp, #0x10]
    // 0xe345ec: r2 = Null
    //     0xe345ec: mov             x2, NULL
    // 0xe345f0: r1 = Null
    //     0xe345f0: mov             x1, NULL
    // 0xe345f4: r8 = Map<String, dynamic>
    //     0xe345f4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe345f8: r3 = Null
    //     0xe345f8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31f48] Null
    //     0xe345fc: ldr             x3, [x3, #0xf48]
    // 0xe34600: r0 = Map<String, dynamic>()
    //     0xe34600: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe34604: ldr             x2, [fp, #0x10]
    // 0xe34608: r1 = Null
    //     0xe34608: mov             x1, NULL
    // 0xe3460c: r0 = Playlist.fromMap()
    //     0xe3460c: bl              #0x925740  ; [package:nuonline/app/data/models/playlist.dart] Playlist::Playlist.fromMap
    // 0xe34610: LeaveFrame
    //     0xe34610: mov             SP, fp
    //     0xe34614: ldp             fp, lr, [SP], #0x10
    // 0xe34618: ret
    //     0xe34618: ret             
    // 0xe3461c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3461c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34620: b               #0xe345e8
  }
}
