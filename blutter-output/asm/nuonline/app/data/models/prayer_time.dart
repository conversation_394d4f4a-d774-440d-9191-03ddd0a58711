// lib: , url: package:nuonline/app/data/models/prayer_time.dart

// class id: 1050037, size: 0x8
class :: {
}

// class id: 1134, size: 0x18, field offset: 0x8
class UpcomingPrayerTime extends Object {
}

// class id: 1135, size: 0x2c, field offset: 0x8
//   const constructor, 
class PrayerTime extends Object {
}

// class id: 5576, size: 0x1c, field offset: 0x8
//   const constructor, 
class PrayerTimeOption extends Equatable {

  String dyn:get:label(PrayerTimeOption) {
    // ** addr: 0x83a27c, size: 0x48
    // 0x83a27c: EnterFrame
    //     0x83a27c: stp             fp, lr, [SP, #-0x10]!
    //     0x83a280: mov             fp, SP
    // 0x83a284: CheckStackOverflow
    //     0x83a284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a288: cmp             SP, x16
    //     0x83a28c: b.ls            #0x83a2a4
    // 0x83a290: ldr             x1, [fp, #0x10]
    // 0x83a294: r0 = label()
    //     0x83a294: bl              #0x83a2ac  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::label
    // 0x83a298: LeaveFrame
    //     0x83a298: mov             SP, fp
    //     0x83a29c: ldp             fp, lr, [SP], #0x10
    // 0x83a2a0: ret
    //     0x83a2a0: ret             
    // 0x83a2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a2a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a2a8: b               #0x83a290
  }
  String label(PrayerTimeOption) {
    // ** addr: 0x83a2ac, size: 0x50
    // 0x83a2ac: EnterFrame
    //     0x83a2ac: stp             fp, lr, [SP, #-0x10]!
    //     0x83a2b0: mov             fp, SP
    // 0x83a2b4: CheckStackOverflow
    //     0x83a2b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a2b8: cmp             SP, x16
    //     0x83a2bc: b.ls            #0x83a2f4
    // 0x83a2c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x83a2c0: ldur            w0, [x1, #0x17]
    // 0x83a2c4: DecompressPointer r0
    //     0x83a2c4: add             x0, x0, HEAP, lsl #32
    // 0x83a2c8: tbnz            w0, #4, #0x83a2dc
    // 0x83a2cc: r0 = hijri()
    //     0x83a2cc: bl              #0x83a5c0  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::hijri
    // 0x83a2d0: mov             x1, x0
    // 0x83a2d4: r0 = mY()
    //     0x83a2d4: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x83a2d8: b               #0x83a2e8
    // 0x83a2dc: r0 = gregorian()
    //     0x83a2dc: bl              #0x83a378  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::gregorian
    // 0x83a2e0: mov             x1, x0
    // 0x83a2e4: r0 = DateTimeExtensions.mY()
    //     0x83a2e4: bl              #0x83a2fc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.mY
    // 0x83a2e8: LeaveFrame
    //     0x83a2e8: mov             SP, fp
    //     0x83a2ec: ldp             fp, lr, [SP], #0x10
    // 0x83a2f0: ret
    //     0x83a2f0: ret             
    // 0x83a2f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a2f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a2f8: b               #0x83a2c0
  }
  get _ gregorian(/* No info */) {
    // ** addr: 0x83a378, size: 0x74
    // 0x83a378: EnterFrame
    //     0x83a378: stp             fp, lr, [SP, #-0x10]!
    //     0x83a37c: mov             fp, SP
    // 0x83a380: AllocStack(0x20)
    //     0x83a380: sub             SP, SP, #0x20
    // 0x83a384: CheckStackOverflow
    //     0x83a384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a388: cmp             SP, x16
    //     0x83a38c: b.ls            #0x83a3e4
    // 0x83a390: LoadField: r2 = r1->field_f
    //     0x83a390: ldur            x2, [x1, #0xf]
    // 0x83a394: stur            x2, [fp, #-0x10]
    // 0x83a398: LoadField: r3 = r1->field_7
    //     0x83a398: ldur            x3, [x1, #7]
    // 0x83a39c: r0 = BoxInt64Instr(r3)
    //     0x83a39c: sbfiz           x0, x3, #1, #0x1f
    //     0x83a3a0: cmp             x3, x0, asr #1
    //     0x83a3a4: b.eq            #0x83a3b0
    //     0x83a3a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83a3ac: stur            x3, [x0, #7]
    // 0x83a3b0: stur            x0, [fp, #-8]
    // 0x83a3b4: r0 = DateTime()
    //     0x83a3b4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x83a3b8: stur            x0, [fp, #-0x18]
    // 0x83a3bc: ldur            x16, [fp, #-8]
    // 0x83a3c0: str             x16, [SP]
    // 0x83a3c4: mov             x1, x0
    // 0x83a3c8: ldur            x2, [fp, #-0x10]
    // 0x83a3cc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x83a3cc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x83a3d0: r0 = DateTime()
    //     0x83a3d0: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x83a3d4: ldur            x0, [fp, #-0x18]
    // 0x83a3d8: LeaveFrame
    //     0x83a3d8: mov             SP, fp
    //     0x83a3dc: ldp             fp, lr, [SP], #0x10
    // 0x83a3e0: ret
    //     0x83a3e0: ret             
    // 0x83a3e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a3e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a3e8: b               #0x83a390
  }
  get _ hijri(/* No info */) {
    // ** addr: 0x83a5c0, size: 0x38
    // 0x83a5c0: EnterFrame
    //     0x83a5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x83a5c4: mov             fp, SP
    // 0x83a5c8: CheckStackOverflow
    //     0x83a5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83a5cc: cmp             SP, x16
    //     0x83a5d0: b.ls            #0x83a5f0
    // 0x83a5d4: LoadField: r0 = r1->field_f
    //     0x83a5d4: ldur            x0, [x1, #0xf]
    // 0x83a5d8: LoadField: r2 = r1->field_7
    //     0x83a5d8: ldur            x2, [x1, #7]
    // 0x83a5dc: mov             x1, x0
    // 0x83a5e0: r0 = init()
    //     0x83a5e0: bl              #0x83a5f8  ; [package:nuonline/services/hijri_service.dart] HijriService::init
    // 0x83a5e4: LeaveFrame
    //     0x83a5e4: mov             SP, fp
    //     0x83a5e8: ldp             fp, lr, [SP], #0x10
    // 0x83a5ec: ret
    //     0x83a5ec: ret             
    // 0x83a5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83a5f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83a5f4: b               #0x83a5d4
  }
  String filename(PrayerTimeOption) {
    // ** addr: 0xb0de04, size: 0xb0
    // 0xb0de04: EnterFrame
    //     0xb0de04: stp             fp, lr, [SP, #-0x10]!
    //     0xb0de08: mov             fp, SP
    // 0xb0de0c: AllocStack(0x18)
    //     0xb0de0c: sub             SP, SP, #0x18
    // 0xb0de10: CheckStackOverflow
    //     0xb0de10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0de14: cmp             SP, x16
    //     0xb0de18: b.ls            #0xb0deac
    // 0xb0de1c: r0 = label()
    //     0xb0de1c: bl              #0x83a2ac  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::label
    // 0xb0de20: mov             x1, x0
    // 0xb0de24: r2 = " "
    //     0xb0de24: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb0de28: r3 = "-"
    //     0xb0de28: ldr             x3, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb0de2c: r0 = replaceAll()
    //     0xb0de2c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb0de30: r1 = LoadClassIdInstr(r0)
    //     0xb0de30: ldur            x1, [x0, #-1]
    //     0xb0de34: ubfx            x1, x1, #0xc, #0x14
    // 0xb0de38: str             x0, [SP]
    // 0xb0de3c: mov             x0, x1
    // 0xb0de40: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb0de40: sub             lr, x0, #0xffe
    //     0xb0de44: ldr             lr, [x21, lr, lsl #3]
    //     0xb0de48: blr             lr
    // 0xb0de4c: r1 = Null
    //     0xb0de4c: mov             x1, NULL
    // 0xb0de50: r2 = 4
    //     0xb0de50: movz            x2, #0x4
    // 0xb0de54: stur            x0, [fp, #-8]
    // 0xb0de58: r0 = AllocateArray()
    //     0xb0de58: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb0de5c: stur            x0, [fp, #-0x10]
    // 0xb0de60: r16 = "nu-online-jadwal-shalat"
    //     0xb0de60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e0f8] "nu-online-jadwal-shalat"
    //     0xb0de64: ldr             x16, [x16, #0xf8]
    // 0xb0de68: StoreField: r0->field_f = r16
    //     0xb0de68: stur            w16, [x0, #0xf]
    // 0xb0de6c: ldur            x1, [fp, #-8]
    // 0xb0de70: StoreField: r0->field_13 = r1
    //     0xb0de70: stur            w1, [x0, #0x13]
    // 0xb0de74: r1 = <String>
    //     0xb0de74: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb0de78: r0 = AllocateGrowableArray()
    //     0xb0de78: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb0de7c: mov             x1, x0
    // 0xb0de80: ldur            x0, [fp, #-0x10]
    // 0xb0de84: StoreField: r1->field_f = r0
    //     0xb0de84: stur            w0, [x1, #0xf]
    // 0xb0de88: r0 = 4
    //     0xb0de88: movz            x0, #0x4
    // 0xb0de8c: StoreField: r1->field_b = r0
    //     0xb0de8c: stur            w0, [x1, #0xb]
    // 0xb0de90: r16 = "-"
    //     0xb0de90: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb0de94: str             x16, [SP]
    // 0xb0de98: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb0de98: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb0de9c: r0 = join()
    //     0xb0de9c: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb0dea0: LeaveFrame
    //     0xb0dea0: mov             SP, fp
    //     0xb0dea4: ldp             fp, lr, [SP], #0x10
    // 0xb0dea8: ret
    //     0xb0dea8: ret             
    // 0xb0deac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0deac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0deb0: b               #0xb0de1c
  }
  get _ days(/* No info */) {
    // ** addr: 0xb11704, size: 0x394
    // 0xb11704: EnterFrame
    //     0xb11704: stp             fp, lr, [SP, #-0x10]!
    //     0xb11708: mov             fp, SP
    // 0xb1170c: AllocStack(0x48)
    //     0xb1170c: sub             SP, SP, #0x48
    // 0xb11710: SetupParameters(PrayerTimeOption this /* r1 => r0, fp-0x8 */)
    //     0xb11710: mov             x0, x1
    //     0xb11714: stur            x1, [fp, #-8]
    // 0xb11718: CheckStackOverflow
    //     0xb11718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1171c: cmp             SP, x16
    //     0xb11720: b.ls            #0xb11a64
    // 0xb11724: r1 = <DateTime>
    //     0xb11724: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0xb11728: ldr             x1, [x1, #0xdd8]
    // 0xb1172c: r2 = 0
    //     0xb1172c: movz            x2, #0
    // 0xb11730: r0 = _GrowableList()
    //     0xb11730: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb11734: ldur            x1, [fp, #-8]
    // 0xb11738: stur            x0, [fp, #-0x10]
    // 0xb1173c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb1173c: ldur            w2, [x1, #0x17]
    // 0xb11740: DecompressPointer r2
    //     0xb11740: add             x2, x2, HEAP, lsl #32
    // 0xb11744: tbnz            w2, #4, #0xb11864
    // 0xb11748: LoadField: r2 = r1->field_f
    //     0xb11748: ldur            x2, [x1, #0xf]
    // 0xb1174c: LoadField: r3 = r1->field_7
    //     0xb1174c: ldur            x3, [x1, #7]
    // 0xb11750: mov             x1, x2
    // 0xb11754: mov             x2, x3
    // 0xb11758: r0 = init()
    //     0xb11758: bl              #0x83a5f8  ; [package:nuonline/services/hijri_service.dart] HijriService::init
    // 0xb1175c: mov             x1, x0
    // 0xb11760: r0 = daysInMonth()
    //     0xb11760: bl              #0x814b04  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::daysInMonth
    // 0xb11764: stur            x0, [fp, #-0x28]
    // 0xb11768: LoadField: r1 = r0->field_b
    //     0xb11768: ldur            w1, [x0, #0xb]
    // 0xb1176c: r2 = LoadInt32Instr(r1)
    //     0xb1176c: sbfx            x2, x1, #1, #0x1f
    // 0xb11770: stur            x2, [fp, #-0x20]
    // 0xb11774: ldur            x3, [fp, #-0x10]
    // 0xb11778: r1 = 0
    //     0xb11778: movz            x1, #0
    // 0xb1177c: CheckStackOverflow
    //     0xb1177c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb11780: cmp             SP, x16
    //     0xb11784: b.ls            #0xb11a6c
    // 0xb11788: LoadField: r4 = r0->field_b
    //     0xb11788: ldur            w4, [x0, #0xb]
    // 0xb1178c: r5 = LoadInt32Instr(r4)
    //     0xb1178c: sbfx            x5, x4, #1, #0x1f
    // 0xb11790: cmp             x2, x5
    // 0xb11794: b.ne            #0xb11a48
    // 0xb11798: cmp             x1, x5
    // 0xb1179c: b.ge            #0xb11858
    // 0xb117a0: LoadField: r4 = r0->field_f
    //     0xb117a0: ldur            w4, [x0, #0xf]
    // 0xb117a4: DecompressPointer r4
    //     0xb117a4: add             x4, x4, HEAP, lsl #32
    // 0xb117a8: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0xb117a8: add             x16, x4, x1, lsl #2
    //     0xb117ac: ldur            w5, [x16, #0xf]
    // 0xb117b0: DecompressPointer r5
    //     0xb117b0: add             x5, x5, HEAP, lsl #32
    // 0xb117b4: add             x4, x1, #1
    // 0xb117b8: mov             x1, x5
    // 0xb117bc: stur            x4, [fp, #-0x18]
    // 0xb117c0: r0 = toGregorian()
    //     0xb117c0: bl              #0xb11d50  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::toGregorian
    // 0xb117c4: mov             x2, x0
    // 0xb117c8: ldur            x0, [fp, #-0x10]
    // 0xb117cc: stur            x2, [fp, #-0x38]
    // 0xb117d0: LoadField: r1 = r0->field_b
    //     0xb117d0: ldur            w1, [x0, #0xb]
    // 0xb117d4: LoadField: r3 = r0->field_f
    //     0xb117d4: ldur            w3, [x0, #0xf]
    // 0xb117d8: DecompressPointer r3
    //     0xb117d8: add             x3, x3, HEAP, lsl #32
    // 0xb117dc: LoadField: r4 = r3->field_b
    //     0xb117dc: ldur            w4, [x3, #0xb]
    // 0xb117e0: r3 = LoadInt32Instr(r1)
    //     0xb117e0: sbfx            x3, x1, #1, #0x1f
    // 0xb117e4: stur            x3, [fp, #-0x30]
    // 0xb117e8: r1 = LoadInt32Instr(r4)
    //     0xb117e8: sbfx            x1, x4, #1, #0x1f
    // 0xb117ec: cmp             x3, x1
    // 0xb117f0: b.ne            #0xb117fc
    // 0xb117f4: mov             x1, x0
    // 0xb117f8: r0 = _growToNextCapacity()
    //     0xb117f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb117fc: ldur            x2, [fp, #-0x10]
    // 0xb11800: ldur            x3, [fp, #-0x30]
    // 0xb11804: add             x0, x3, #1
    // 0xb11808: lsl             x1, x0, #1
    // 0xb1180c: StoreField: r2->field_b = r1
    //     0xb1180c: stur            w1, [x2, #0xb]
    // 0xb11810: LoadField: r1 = r2->field_f
    //     0xb11810: ldur            w1, [x2, #0xf]
    // 0xb11814: DecompressPointer r1
    //     0xb11814: add             x1, x1, HEAP, lsl #32
    // 0xb11818: ldur            x0, [fp, #-0x38]
    // 0xb1181c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1181c: add             x25, x1, x3, lsl #2
    //     0xb11820: add             x25, x25, #0xf
    //     0xb11824: str             w0, [x25]
    //     0xb11828: tbz             w0, #0, #0xb11844
    //     0xb1182c: ldurb           w16, [x1, #-1]
    //     0xb11830: ldurb           w17, [x0, #-1]
    //     0xb11834: and             x16, x17, x16, lsr #2
    //     0xb11838: tst             x16, HEAP, lsr #32
    //     0xb1183c: b.eq            #0xb11844
    //     0xb11840: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb11844: ldur            x1, [fp, #-0x18]
    // 0xb11848: mov             x3, x2
    // 0xb1184c: ldur            x0, [fp, #-0x28]
    // 0xb11850: ldur            x2, [fp, #-0x20]
    // 0xb11854: b               #0xb1177c
    // 0xb11858: mov             x2, x3
    // 0xb1185c: mov             x4, x2
    // 0xb11860: b               #0xb11a18
    // 0xb11864: mov             x2, x0
    // 0xb11868: r0 = gregorian()
    //     0xb11868: bl              #0x83a378  ; [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::gregorian
    // 0xb1186c: stur            x0, [fp, #-8]
    // 0xb11870: r0 = Jiffy()
    //     0xb11870: bl              #0x916174  ; AllocateJiffyStub -> Jiffy (size=0xc)
    // 0xb11874: mov             x3, x0
    // 0xb11878: r0 = Sentinel
    //     0xb11878: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1187c: stur            x3, [fp, #-0x38]
    // 0xb11880: StoreField: r3->field_7 = r0
    //     0xb11880: stur            w0, [x3, #7]
    // 0xb11884: mov             x1, x3
    // 0xb11888: ldur            x2, [fp, #-8]
    // 0xb1188c: r0 = _initializeDateTime()
    //     0xb1188c: bl              #0x915f90  ; [package:jiffy/src/jiffy.dart] Jiffy::_initializeDateTime
    // 0xb11890: r0 = _initializeLocale()
    //     0xb11890: bl              #0x914d78  ; [package:jiffy/src/jiffy.dart] Jiffy::_initializeLocale
    // 0xb11894: ldur            x1, [fp, #-0x38]
    // 0xb11898: r0 = clone()
    //     0xb11898: bl              #0x914704  ; [package:jiffy/src/jiffy.dart] Jiffy::clone
    // 0xb1189c: mov             x1, x0
    // 0xb118a0: r0 = endOf()
    //     0xb118a0: bl              #0xb11a98  ; [package:jiffy/src/jiffy.dart] Jiffy::endOf
    // 0xb118a4: LoadField: r2 = r0->field_7
    //     0xb118a4: ldur            w2, [x0, #7]
    // 0xb118a8: DecompressPointer r2
    //     0xb118a8: add             x2, x2, HEAP, lsl #32
    // 0xb118ac: r16 = Sentinel
    //     0xb118ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb118b0: cmp             w2, w16
    // 0xb118b4: b.eq            #0xb11a74
    // 0xb118b8: stur            x2, [fp, #-8]
    // 0xb118bc: ldur            x0, [fp, #-0x10]
    // 0xb118c0: r3 = 0
    //     0xb118c0: movz            x3, #0
    // 0xb118c4: stur            x3, [fp, #-0x18]
    // 0xb118c8: CheckStackOverflow
    //     0xb118c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb118cc: cmp             SP, x16
    //     0xb118d0: b.ls            #0xb11a80
    // 0xb118d4: mov             x1, x2
    // 0xb118d8: r0 = _parts()
    //     0xb118d8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xb118dc: mov             x2, x0
    // 0xb118e0: LoadField: r0 = r2->field_b
    //     0xb118e0: ldur            w0, [x2, #0xb]
    // 0xb118e4: r1 = LoadInt32Instr(r0)
    //     0xb118e4: sbfx            x1, x0, #1, #0x1f
    // 0xb118e8: mov             x0, x1
    // 0xb118ec: r1 = 5
    //     0xb118ec: movz            x1, #0x5
    // 0xb118f0: cmp             x1, x0
    // 0xb118f4: b.hs            #0xb11a88
    // 0xb118f8: LoadField: r0 = r2->field_23
    //     0xb118f8: ldur            w0, [x2, #0x23]
    // 0xb118fc: DecompressPointer r0
    //     0xb118fc: add             x0, x0, HEAP, lsl #32
    // 0xb11900: r1 = LoadInt32Instr(r0)
    //     0xb11900: sbfx            x1, x0, #1, #0x1f
    //     0xb11904: tbz             w0, #0, #0xb1190c
    //     0xb11908: ldur            x1, [x0, #7]
    // 0xb1190c: ldur            x0, [fp, #-0x18]
    // 0xb11910: cmp             x0, x1
    // 0xb11914: b.ge            #0xb11a14
    // 0xb11918: ldur            x1, [fp, #-0x10]
    // 0xb1191c: r0 = Jiffy()
    //     0xb1191c: bl              #0x916174  ; AllocateJiffyStub -> Jiffy (size=0xc)
    // 0xb11920: mov             x3, x0
    // 0xb11924: r0 = Sentinel
    //     0xb11924: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11928: stur            x3, [fp, #-0x40]
    // 0xb1192c: StoreField: r3->field_7 = r0
    //     0xb1192c: stur            w0, [x3, #7]
    // 0xb11930: mov             x1, x3
    // 0xb11934: ldur            x2, [fp, #-0x38]
    // 0xb11938: r0 = _initializeDateTime()
    //     0xb11938: bl              #0x915f90  ; [package:jiffy/src/jiffy.dart] Jiffy::_initializeDateTime
    // 0xb1193c: r0 = _initializeLocale()
    //     0xb1193c: bl              #0x914d78  ; [package:jiffy/src/jiffy.dart] Jiffy::_initializeLocale
    // 0xb11940: ldur            x2, [fp, #-0x18]
    // 0xb11944: r0 = BoxInt64Instr(r2)
    //     0xb11944: sbfiz           x0, x2, #1, #0x1f
    //     0xb11948: cmp             x2, x0, asr #1
    //     0xb1194c: b.eq            #0xb11958
    //     0xb11950: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb11954: stur            x2, [x0, #7]
    // 0xb11958: str             x0, [SP]
    // 0xb1195c: ldur            x1, [fp, #-0x40]
    // 0xb11960: r4 = const [0, 0x2, 0x1, 0x1, days, 0x1, null]
    //     0xb11960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e608] List(7) [0, 0x2, 0x1, 0x1, "days", 0x1, Null]
    //     0xb11964: ldr             x4, [x4, #0x608]
    // 0xb11968: r0 = add()
    //     0xb11968: bl              #0x914514  ; [package:jiffy/src/jiffy.dart] Jiffy::add
    // 0xb1196c: LoadField: r2 = r0->field_7
    //     0xb1196c: ldur            w2, [x0, #7]
    // 0xb11970: DecompressPointer r2
    //     0xb11970: add             x2, x2, HEAP, lsl #32
    // 0xb11974: r16 = Sentinel
    //     0xb11974: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb11978: cmp             w2, w16
    // 0xb1197c: b.eq            #0xb11a8c
    // 0xb11980: ldur            x0, [fp, #-0x10]
    // 0xb11984: stur            x2, [fp, #-0x40]
    // 0xb11988: LoadField: r1 = r0->field_b
    //     0xb11988: ldur            w1, [x0, #0xb]
    // 0xb1198c: LoadField: r3 = r0->field_f
    //     0xb1198c: ldur            w3, [x0, #0xf]
    // 0xb11990: DecompressPointer r3
    //     0xb11990: add             x3, x3, HEAP, lsl #32
    // 0xb11994: LoadField: r4 = r3->field_b
    //     0xb11994: ldur            w4, [x3, #0xb]
    // 0xb11998: r3 = LoadInt32Instr(r1)
    //     0xb11998: sbfx            x3, x1, #1, #0x1f
    // 0xb1199c: stur            x3, [fp, #-0x20]
    // 0xb119a0: r1 = LoadInt32Instr(r4)
    //     0xb119a0: sbfx            x1, x4, #1, #0x1f
    // 0xb119a4: cmp             x3, x1
    // 0xb119a8: b.ne            #0xb119b4
    // 0xb119ac: mov             x1, x0
    // 0xb119b0: r0 = _growToNextCapacity()
    //     0xb119b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb119b4: ldur            x4, [fp, #-0x10]
    // 0xb119b8: ldur            x3, [fp, #-0x18]
    // 0xb119bc: ldur            x2, [fp, #-0x20]
    // 0xb119c0: add             x0, x2, #1
    // 0xb119c4: lsl             x1, x0, #1
    // 0xb119c8: StoreField: r4->field_b = r1
    //     0xb119c8: stur            w1, [x4, #0xb]
    // 0xb119cc: LoadField: r1 = r4->field_f
    //     0xb119cc: ldur            w1, [x4, #0xf]
    // 0xb119d0: DecompressPointer r1
    //     0xb119d0: add             x1, x1, HEAP, lsl #32
    // 0xb119d4: ldur            x0, [fp, #-0x40]
    // 0xb119d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb119d8: add             x25, x1, x2, lsl #2
    //     0xb119dc: add             x25, x25, #0xf
    //     0xb119e0: str             w0, [x25]
    //     0xb119e4: tbz             w0, #0, #0xb11a00
    //     0xb119e8: ldurb           w16, [x1, #-1]
    //     0xb119ec: ldurb           w17, [x0, #-1]
    //     0xb119f0: and             x16, x17, x16, lsr #2
    //     0xb119f4: tst             x16, HEAP, lsr #32
    //     0xb119f8: b.eq            #0xb11a00
    //     0xb119fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb11a00: add             x0, x3, #1
    // 0xb11a04: mov             x3, x0
    // 0xb11a08: mov             x0, x4
    // 0xb11a0c: ldur            x2, [fp, #-8]
    // 0xb11a10: b               #0xb118c4
    // 0xb11a14: ldur            x4, [fp, #-0x10]
    // 0xb11a18: r1 = Function '<anonymous closure>':.
    //     0xb11a18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e610] AnonymousClosure: (0xb12164), in [package:nuonline/app/data/models/prayer_time.dart] PrayerTimeOption::days (0xb11704)
    //     0xb11a1c: ldr             x1, [x1, #0x610]
    // 0xb11a20: r2 = Null
    //     0xb11a20: mov             x2, NULL
    // 0xb11a24: r0 = AllocateClosure()
    //     0xb11a24: bl              #0xec1630  ; AllocateClosureStub
    // 0xb11a28: str             x0, [SP]
    // 0xb11a2c: ldur            x1, [fp, #-0x10]
    // 0xb11a30: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb11a30: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb11a34: r0 = sort()
    //     0xb11a34: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xb11a38: ldur            x0, [fp, #-0x10]
    // 0xb11a3c: LeaveFrame
    //     0xb11a3c: mov             SP, fp
    //     0xb11a40: ldp             fp, lr, [SP], #0x10
    // 0xb11a44: ret
    //     0xb11a44: ret             
    // 0xb11a48: r0 = ConcurrentModificationError()
    //     0xb11a48: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb11a4c: mov             x1, x0
    // 0xb11a50: ldur            x0, [fp, #-0x28]
    // 0xb11a54: StoreField: r1->field_b = r0
    //     0xb11a54: stur            w0, [x1, #0xb]
    // 0xb11a58: mov             x0, x1
    // 0xb11a5c: r0 = Throw()
    //     0xb11a5c: bl              #0xec04b8  ; ThrowStub
    // 0xb11a60: brk             #0
    // 0xb11a64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11a64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11a68: b               #0xb11724
    // 0xb11a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11a70: b               #0xb11788
    // 0xb11a74: r9 = _dateTime
    //     0xb11a74: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e618] Field <Jiffy._dateTime@1568470045>: late (offset: 0x8)
    //     0xb11a78: ldr             x9, [x9, #0x618]
    // 0xb11a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb11a7c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb11a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11a80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11a84: b               #0xb118d4
    // 0xb11a88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb11a88: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb11a8c: r9 = _dateTime
    //     0xb11a8c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e618] Field <Jiffy._dateTime@1568470045>: late (offset: 0x8)
    //     0xb11a90: ldr             x9, [x9, #0x618]
    // 0xb11a94: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb11a94: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, DateTime, DateTime) {
    // ** addr: 0xb12164, size: 0x64
    // 0xb12164: EnterFrame
    //     0xb12164: stp             fp, lr, [SP, #-0x10]!
    //     0xb12168: mov             fp, SP
    // 0xb1216c: CheckStackOverflow
    //     0xb1216c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12170: cmp             SP, x16
    //     0xb12174: b.ls            #0xb121c0
    // 0xb12178: ldr             x1, [fp, #0x18]
    // 0xb1217c: r0 = LoadClassIdInstr(r1)
    //     0xb1217c: ldur            x0, [x1, #-1]
    //     0xb12180: ubfx            x0, x0, #0xc, #0x14
    // 0xb12184: ldr             x2, [fp, #0x10]
    // 0xb12188: r0 = GDT[cid_x0 + 0x138b7]()
    //     0xb12188: movz            x17, #0x38b7
    //     0xb1218c: movk            x17, #0x1, lsl #16
    //     0xb12190: add             lr, x0, x17
    //     0xb12194: ldr             lr, [x21, lr, lsl #3]
    //     0xb12198: blr             lr
    // 0xb1219c: mov             x2, x0
    // 0xb121a0: r0 = BoxInt64Instr(r2)
    //     0xb121a0: sbfiz           x0, x2, #1, #0x1f
    //     0xb121a4: cmp             x2, x0, asr #1
    //     0xb121a8: b.eq            #0xb121b4
    //     0xb121ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb121b0: stur            x2, [x0, #7]
    // 0xb121b4: LeaveFrame
    //     0xb121b4: mov             SP, fp
    //     0xb121b8: ldp             fp, lr, [SP], #0x10
    // 0xb121bc: ret
    //     0xb121bc: ret             
    // 0xb121c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb121c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb121c4: b               #0xb12178
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdbf70, size: 0xa4
    // 0xbdbf70: EnterFrame
    //     0xbdbf70: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbf74: mov             fp, SP
    // 0xbdbf78: AllocStack(0x20)
    //     0xbdbf78: sub             SP, SP, #0x20
    // 0xbdbf7c: r3 = 6
    //     0xbdbf7c: movz            x3, #0x6
    // 0xbdbf80: LoadField: r2 = r1->field_7
    //     0xbdbf80: ldur            x2, [x1, #7]
    // 0xbdbf84: LoadField: r4 = r1->field_f
    //     0xbdbf84: ldur            x4, [x1, #0xf]
    // 0xbdbf88: stur            x4, [fp, #-0x18]
    // 0xbdbf8c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbdbf8c: ldur            w5, [x1, #0x17]
    // 0xbdbf90: DecompressPointer r5
    //     0xbdbf90: add             x5, x5, HEAP, lsl #32
    // 0xbdbf94: stur            x5, [fp, #-0x10]
    // 0xbdbf98: r0 = BoxInt64Instr(r2)
    //     0xbdbf98: sbfiz           x0, x2, #1, #0x1f
    //     0xbdbf9c: cmp             x2, x0, asr #1
    //     0xbdbfa0: b.eq            #0xbdbfac
    //     0xbdbfa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdbfa8: stur            x2, [x0, #7]
    // 0xbdbfac: mov             x2, x3
    // 0xbdbfb0: r1 = Null
    //     0xbdbfb0: mov             x1, NULL
    // 0xbdbfb4: stur            x0, [fp, #-8]
    // 0xbdbfb8: r0 = AllocateArray()
    //     0xbdbfb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdbfbc: mov             x2, x0
    // 0xbdbfc0: ldur            x0, [fp, #-8]
    // 0xbdbfc4: stur            x2, [fp, #-0x20]
    // 0xbdbfc8: StoreField: r2->field_f = r0
    //     0xbdbfc8: stur            w0, [x2, #0xf]
    // 0xbdbfcc: ldur            x3, [fp, #-0x18]
    // 0xbdbfd0: r0 = BoxInt64Instr(r3)
    //     0xbdbfd0: sbfiz           x0, x3, #1, #0x1f
    //     0xbdbfd4: cmp             x3, x0, asr #1
    //     0xbdbfd8: b.eq            #0xbdbfe4
    //     0xbdbfdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdbfe0: stur            x3, [x0, #7]
    // 0xbdbfe4: StoreField: r2->field_13 = r0
    //     0xbdbfe4: stur            w0, [x2, #0x13]
    // 0xbdbfe8: ldur            x0, [fp, #-0x10]
    // 0xbdbfec: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdbfec: stur            w0, [x2, #0x17]
    // 0xbdbff0: r1 = <Object?>
    //     0xbdbff0: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdbff4: r0 = AllocateGrowableArray()
    //     0xbdbff4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdbff8: ldur            x1, [fp, #-0x20]
    // 0xbdbffc: StoreField: r0->field_f = r1
    //     0xbdbffc: stur            w1, [x0, #0xf]
    // 0xbdc000: r1 = 6
    //     0xbdc000: movz            x1, #0x6
    // 0xbdc004: StoreField: r0->field_b = r1
    //     0xbdc004: stur            w1, [x0, #0xb]
    // 0xbdc008: LeaveFrame
    //     0xbdc008: mov             SP, fp
    //     0xbdc00c: ldp             fp, lr, [SP], #0x10
    // 0xbdc010: ret
    //     0xbdc010: ret             
  }
}
