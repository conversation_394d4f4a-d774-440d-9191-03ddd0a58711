// lib: , url: package:nuonline/app/data/models/prayer_time_notification_setting.dart

// class id: 1050038, size: 0x8
class :: {
}

// class id: 1133, size: 0x10, field offset: 0x8
class PrayerSound extends Object {

  _ toMap(/* No info */) {
    // ** addr: 0xb490cc, size: 0x80
    // 0xb490cc: EnterFrame
    //     0xb490cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb490d0: mov             fp, SP
    // 0xb490d4: AllocStack(0x18)
    //     0xb490d4: sub             SP, SP, #0x18
    // 0xb490d8: SetupParameters(PrayerSound this /* r1 => r0, fp-0x8 */)
    //     0xb490d8: mov             x0, x1
    //     0xb490dc: stur            x1, [fp, #-8]
    // 0xb490e0: CheckStackOverflow
    //     0xb490e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb490e4: cmp             SP, x16
    //     0xb490e8: b.ls            #0xb49144
    // 0xb490ec: r1 = Null
    //     0xb490ec: mov             x1, NULL
    // 0xb490f0: r2 = 8
    //     0xb490f0: movz            x2, #0x8
    // 0xb490f4: r0 = AllocateArray()
    //     0xb490f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb490f8: r16 = "title"
    //     0xb490f8: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xb490fc: ldr             x16, [x16, #0x748]
    // 0xb49100: StoreField: r0->field_f = r16
    //     0xb49100: stur            w16, [x0, #0xf]
    // 0xb49104: ldur            x1, [fp, #-8]
    // 0xb49108: LoadField: r2 = r1->field_7
    //     0xb49108: ldur            w2, [x1, #7]
    // 0xb4910c: DecompressPointer r2
    //     0xb4910c: add             x2, x2, HEAP, lsl #32
    // 0xb49110: StoreField: r0->field_13 = r2
    //     0xb49110: stur            w2, [x0, #0x13]
    // 0xb49114: r16 = "filename"
    //     0xb49114: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a3d8] "filename"
    //     0xb49118: ldr             x16, [x16, #0x3d8]
    // 0xb4911c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb4911c: stur            w16, [x0, #0x17]
    // 0xb49120: LoadField: r2 = r1->field_b
    //     0xb49120: ldur            w2, [x1, #0xb]
    // 0xb49124: DecompressPointer r2
    //     0xb49124: add             x2, x2, HEAP, lsl #32
    // 0xb49128: StoreField: r0->field_1b = r2
    //     0xb49128: stur            w2, [x0, #0x1b]
    // 0xb4912c: r16 = <String, dynamic>
    //     0xb4912c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xb49130: stp             x0, x16, [SP]
    // 0xb49134: r0 = Map._fromLiteral()
    //     0xb49134: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb49138: LeaveFrame
    //     0xb49138: mov             SP, fp
    //     0xb4913c: ldp             fp, lr, [SP], #0x10
    // 0xb49140: ret
    //     0xb49140: ret             
    // 0xb49144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb49144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb49148: b               #0xb490ec
  }
}

// class id: 1604, size: 0x80, field offset: 0x14
class PrayerTimeNotificationSetting extends HiveObject {

  factory _ PrayerTimeNotificationSetting.defaultSetting(/* No info */) {
    // ** addr: 0x83101c, size: 0xe4
    // 0x83101c: EnterFrame
    //     0x83101c: stp             fp, lr, [SP, #-0x10]!
    //     0x831020: mov             fp, SP
    // 0x831024: AllocStack(0x18)
    //     0x831024: sub             SP, SP, #0x18
    // 0x831028: CheckStackOverflow
    //     0x831028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83102c: cmp             SP, x16
    //     0x831030: b.ls            #0x8310f8
    // 0x831034: r0 = PrayerTimeNotificationSetting()
    //     0x831034: bl              #0x831100  ; AllocatePrayerTimeNotificationSettingStub -> PrayerTimeNotificationSetting (size=0x80)
    // 0x831038: mov             x1, x0
    // 0x83103c: r0 = Instance_NotificationSound
    //     0x83103c: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9e8] Obj!NotificationSound@e304e1
    //     0x831040: ldr             x0, [x0, #0x9e8]
    // 0x831044: stur            x1, [fp, #-8]
    // 0x831048: StoreField: r1->field_13 = r0
    //     0x831048: stur            w0, [x1, #0x13]
    // 0x83104c: r0 = Instance_NotificationSound
    //     0x83104c: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9f0] Obj!NotificationSound@e304c1
    //     0x831050: ldr             x0, [x0, #0x9f0]
    // 0x831054: ArrayStore: r1[0] = r0  ; List_4
    //     0x831054: stur            w0, [x1, #0x17]
    // 0x831058: r2 = Instance_NotificationSound
    //     0x831058: add             x2, PP, #8, lsl #12  ; [pp+0x8aa0] Obj!NotificationSound@e30481
    //     0x83105c: ldr             x2, [x2, #0xaa0]
    // 0x831060: StoreField: r1->field_1b = r2
    //     0x831060: stur            w2, [x1, #0x1b]
    // 0x831064: StoreField: r1->field_1f = r0
    //     0x831064: stur            w0, [x1, #0x1f]
    // 0x831068: StoreField: r1->field_23 = r0
    //     0x831068: stur            w0, [x1, #0x23]
    // 0x83106c: StoreField: r1->field_27 = r0
    //     0x83106c: stur            w0, [x1, #0x27]
    // 0x831070: StoreField: r1->field_2b = r0
    //     0x831070: stur            w0, [x1, #0x2b]
    // 0x831074: StoreField: r1->field_2f = r2
    //     0x831074: stur            w2, [x1, #0x2f]
    // 0x831078: StoreField: r1->field_33 = r0
    //     0x831078: stur            w0, [x1, #0x33]
    // 0x83107c: StoreField: r1->field_37 = r2
    //     0x83107c: stur            w2, [x1, #0x37]
    // 0x831080: StoreField: r1->field_3b = r0
    //     0x831080: stur            w0, [x1, #0x3b]
    // 0x831084: StoreField: r1->field_3f = r2
    //     0x831084: stur            w2, [x1, #0x3f]
    // 0x831088: StoreField: r1->field_43 = r0
    //     0x831088: stur            w0, [x1, #0x43]
    // 0x83108c: StoreField: r1->field_47 = r2
    //     0x83108c: stur            w2, [x1, #0x47]
    // 0x831090: r0 = 10
    //     0x831090: movz            x0, #0xa
    // 0x831094: StoreField: r1->field_4b = r0
    //     0x831094: stur            x0, [x1, #0x4b]
    // 0x831098: StoreField: r1->field_53 = r0
    //     0x831098: stur            x0, [x1, #0x53]
    // 0x83109c: StoreField: r1->field_5b = r0
    //     0x83109c: stur            x0, [x1, #0x5b]
    // 0x8310a0: StoreField: r1->field_63 = r0
    //     0x8310a0: stur            x0, [x1, #0x63]
    // 0x8310a4: StoreField: r1->field_6b = r0
    //     0x8310a4: stur            x0, [x1, #0x6b]
    // 0x8310a8: StoreField: r1->field_73 = r0
    //     0x8310a8: stur            x0, [x1, #0x73]
    // 0x8310ac: r0 = true
    //     0x8310ac: add             x0, NULL, #0x20  ; true
    // 0x8310b0: StoreField: r1->field_7b = r0
    //     0x8310b0: stur            w0, [x1, #0x7b]
    // 0x8310b4: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8310b4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8310b8: ldr             x16, [x16, #0x9f8]
    // 0x8310bc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8310c0: stp             lr, x16, [SP]
    // 0x8310c4: r0 = Map._fromLiteral()
    //     0x8310c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8310c8: ldur            x1, [fp, #-8]
    // 0x8310cc: StoreField: r1->field_f = r0
    //     0x8310cc: stur            w0, [x1, #0xf]
    //     0x8310d0: ldurb           w16, [x1, #-1]
    //     0x8310d4: ldurb           w17, [x0, #-1]
    //     0x8310d8: and             x16, x17, x16, lsr #2
    //     0x8310dc: tst             x16, HEAP, lsr #32
    //     0x8310e0: b.eq            #0x8310e8
    //     0x8310e4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8310e8: mov             x0, x1
    // 0x8310ec: LeaveFrame
    //     0x8310ec: mov             SP, fp
    //     0x8310f0: ldp             fp, lr, [SP], #0x10
    // 0x8310f4: ret
    //     0x8310f4: ret             
    // 0x8310f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8310f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8310fc: b               #0x831034
  }
  _ copyWith(/* No info */) {
    // ** addr: 0x9051e0, size: 0x36c
    // 0x9051e0: EnterFrame
    //     0x9051e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9051e4: mov             fp, SP
    // 0x9051e8: AllocStack(0xc0)
    //     0x9051e8: sub             SP, SP, #0xc0
    // 0x9051ec: CheckStackOverflow
    //     0x9051ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9051f0: cmp             SP, x16
    //     0x9051f4: b.ls            #0x905544
    // 0x9051f8: ldr             x0, [fp, #0x28]
    // 0x9051fc: cmp             w0, NULL
    // 0x905200: b.ne            #0x905214
    // 0x905204: LoadField: r0 = r1->field_13
    //     0x905204: ldur            w0, [x1, #0x13]
    // 0x905208: DecompressPointer r0
    //     0x905208: add             x0, x0, HEAP, lsl #32
    // 0x90520c: mov             x4, x0
    // 0x905210: b               #0x905218
    // 0x905214: mov             x4, x0
    // 0x905218: ldr             x0, [fp, #0x38]
    // 0x90521c: stur            x4, [fp, #-0xa8]
    // 0x905220: ArrayLoad: r8 = r1[0]  ; List_4
    //     0x905220: ldur            w8, [x1, #0x17]
    // 0x905224: DecompressPointer r8
    //     0x905224: add             x8, x8, HEAP, lsl #32
    // 0x905228: stur            x8, [fp, #-0xa0]
    // 0x90522c: cmp             w0, NULL
    // 0x905230: b.ne            #0x905244
    // 0x905234: LoadField: r0 = r1->field_1b
    //     0x905234: ldur            w0, [x1, #0x1b]
    // 0x905238: DecompressPointer r0
    //     0x905238: add             x0, x0, HEAP, lsl #32
    // 0x90523c: mov             x9, x0
    // 0x905240: b               #0x905248
    // 0x905244: mov             x9, x0
    // 0x905248: ldr             x0, [fp, #0x10]
    // 0x90524c: stur            x9, [fp, #-0x98]
    // 0x905250: LoadField: r10 = r1->field_1f
    //     0x905250: ldur            w10, [x1, #0x1f]
    // 0x905254: DecompressPointer r10
    //     0x905254: add             x10, x10, HEAP, lsl #32
    // 0x905258: stur            x10, [fp, #-0x90]
    // 0x90525c: cmp             w0, NULL
    // 0x905260: b.ne            #0x905274
    // 0x905264: LoadField: r0 = r1->field_23
    //     0x905264: ldur            w0, [x1, #0x23]
    // 0x905268: DecompressPointer r0
    //     0x905268: add             x0, x0, HEAP, lsl #32
    // 0x90526c: mov             x11, x0
    // 0x905270: b               #0x905278
    // 0x905274: mov             x11, x0
    // 0x905278: ldr             x0, [fp, #0x40]
    // 0x90527c: stur            x11, [fp, #-0x88]
    // 0x905280: cmp             w0, NULL
    // 0x905284: b.ne            #0x905298
    // 0x905288: LoadField: r0 = r1->field_27
    //     0x905288: ldur            w0, [x1, #0x27]
    // 0x90528c: DecompressPointer r0
    //     0x90528c: add             x0, x0, HEAP, lsl #32
    // 0x905290: mov             x12, x0
    // 0x905294: b               #0x90529c
    // 0x905298: mov             x12, x0
    // 0x90529c: ldr             x0, [fp, #0x48]
    // 0x9052a0: stur            x12, [fp, #-0x80]
    // 0x9052a4: LoadField: r13 = r1->field_2b
    //     0x9052a4: ldur            w13, [x1, #0x2b]
    // 0x9052a8: DecompressPointer r13
    //     0x9052a8: add             x13, x13, HEAP, lsl #32
    // 0x9052ac: stur            x13, [fp, #-0x78]
    // 0x9052b0: cmp             w0, NULL
    // 0x9052b4: b.ne            #0x9052c0
    // 0x9052b8: LoadField: r0 = r1->field_2f
    //     0x9052b8: ldur            w0, [x1, #0x2f]
    // 0x9052bc: DecompressPointer r0
    //     0x9052bc: add             x0, x0, HEAP, lsl #32
    // 0x9052c0: stur            x0, [fp, #-8]
    // 0x9052c4: LoadField: r14 = r1->field_33
    //     0x9052c4: ldur            w14, [x1, #0x33]
    // 0x9052c8: DecompressPointer r14
    //     0x9052c8: add             x14, x14, HEAP, lsl #32
    // 0x9052cc: stur            x14, [fp, #-0x70]
    // 0x9052d0: cmp             w2, NULL
    // 0x9052d4: b.ne            #0x9052e8
    // 0x9052d8: LoadField: r2 = r1->field_37
    //     0x9052d8: ldur            w2, [x1, #0x37]
    // 0x9052dc: DecompressPointer r2
    //     0x9052dc: add             x2, x2, HEAP, lsl #32
    // 0x9052e0: mov             x19, x2
    // 0x9052e4: b               #0x9052ec
    // 0x9052e8: mov             x19, x2
    // 0x9052ec: ldr             x2, [fp, #0x18]
    // 0x9052f0: stur            x19, [fp, #-0x68]
    // 0x9052f4: LoadField: r20 = r1->field_3b
    //     0x9052f4: ldur            w20, [x1, #0x3b]
    // 0x9052f8: DecompressPointer r20
    //     0x9052f8: add             x20, x20, HEAP, lsl #32
    // 0x9052fc: stur            x20, [fp, #-0x60]
    // 0x905300: cmp             w2, NULL
    // 0x905304: b.ne            #0x905318
    // 0x905308: LoadField: r2 = r1->field_3f
    //     0x905308: ldur            w2, [x1, #0x3f]
    // 0x90530c: DecompressPointer r2
    //     0x90530c: add             x2, x2, HEAP, lsl #32
    // 0x905310: mov             x23, x2
    // 0x905314: b               #0x90531c
    // 0x905318: mov             x23, x2
    // 0x90531c: ldr             x2, [fp, #0x20]
    // 0x905320: stur            x23, [fp, #-0x58]
    // 0x905324: LoadField: r24 = r1->field_43
    //     0x905324: ldur            w24, [x1, #0x43]
    // 0x905328: DecompressPointer r24
    //     0x905328: add             x24, x24, HEAP, lsl #32
    // 0x90532c: stur            x24, [fp, #-0x50]
    // 0x905330: cmp             w2, NULL
    // 0x905334: b.ne            #0x905340
    // 0x905338: LoadField: r2 = r1->field_47
    //     0x905338: ldur            w2, [x1, #0x47]
    // 0x90533c: DecompressPointer r2
    //     0x90533c: add             x2, x2, HEAP, lsl #32
    // 0x905340: stur            x2, [fp, #-0x48]
    // 0x905344: cmp             w6, NULL
    // 0x905348: b.ne            #0x905358
    // 0x90534c: LoadField: r6 = r1->field_4b
    //     0x90534c: ldur            x6, [x1, #0x4b]
    // 0x905350: mov             x25, x6
    // 0x905354: b               #0x905364
    // 0x905358: r25 = LoadInt32Instr(r6)
    //     0x905358: sbfx            x25, x6, #1, #0x1f
    //     0x90535c: tbz             w6, #0, #0x905364
    //     0x905360: ldur            x25, [x6, #7]
    // 0x905364: ldr             x6, [fp, #0x50]
    // 0x905368: stur            x25, [fp, #-0x40]
    // 0x90536c: cmp             w6, NULL
    // 0x905370: b.ne            #0x905380
    // 0x905374: LoadField: r6 = r1->field_53
    //     0x905374: ldur            x6, [x1, #0x53]
    // 0x905378: mov             x0, x6
    // 0x90537c: b               #0x90538c
    // 0x905380: r0 = LoadInt32Instr(r6)
    //     0x905380: sbfx            x0, x6, #1, #0x1f
    //     0x905384: tbz             w6, #0, #0x90538c
    //     0x905388: ldur            x0, [x6, #7]
    // 0x90538c: stur            x0, [fp, #-0x10]
    // 0x905390: cmp             w5, NULL
    // 0x905394: b.ne            #0x9053a0
    // 0x905398: LoadField: r5 = r1->field_5b
    //     0x905398: ldur            x5, [x1, #0x5b]
    // 0x90539c: b               #0x9053b0
    // 0x9053a0: r6 = LoadInt32Instr(r5)
    //     0x9053a0: sbfx            x6, x5, #1, #0x1f
    //     0x9053a4: tbz             w5, #0, #0x9053ac
    //     0x9053a8: ldur            x6, [x5, #7]
    // 0x9053ac: mov             x5, x6
    // 0x9053b0: stur            x5, [fp, #-0x38]
    // 0x9053b4: cmp             w3, NULL
    // 0x9053b8: b.ne            #0x9053c8
    // 0x9053bc: LoadField: r3 = r1->field_63
    //     0x9053bc: ldur            x3, [x1, #0x63]
    // 0x9053c0: mov             x6, x3
    // 0x9053c4: b               #0x9053d4
    // 0x9053c8: r6 = LoadInt32Instr(r3)
    //     0x9053c8: sbfx            x6, x3, #1, #0x1f
    //     0x9053cc: tbz             w3, #0, #0x9053d4
    //     0x9053d0: ldur            x6, [x3, #7]
    // 0x9053d4: ldr             x3, [fp, #0x58]
    // 0x9053d8: stur            x6, [fp, #-0x30]
    // 0x9053dc: cmp             w3, NULL
    // 0x9053e0: b.ne            #0x9053f0
    // 0x9053e4: LoadField: r3 = r1->field_6b
    //     0x9053e4: ldur            x3, [x1, #0x6b]
    // 0x9053e8: mov             x0, x3
    // 0x9053ec: b               #0x9053fc
    // 0x9053f0: r0 = LoadInt32Instr(r3)
    //     0x9053f0: sbfx            x0, x3, #1, #0x1f
    //     0x9053f4: tbz             w3, #0, #0x9053fc
    //     0x9053f8: ldur            x0, [x3, #7]
    // 0x9053fc: stur            x0, [fp, #-0x28]
    // 0x905400: cmp             w7, NULL
    // 0x905404: b.ne            #0x905414
    // 0x905408: LoadField: r3 = r1->field_73
    //     0x905408: ldur            x3, [x1, #0x73]
    // 0x90540c: mov             x7, x3
    // 0x905410: b               #0x905424
    // 0x905414: r3 = LoadInt32Instr(r7)
    //     0x905414: sbfx            x3, x7, #1, #0x1f
    //     0x905418: tbz             w7, #0, #0x905420
    //     0x90541c: ldur            x3, [x7, #7]
    // 0x905420: mov             x7, x3
    // 0x905424: ldr             x3, [fp, #0x30]
    // 0x905428: stur            x7, [fp, #-0x20]
    // 0x90542c: cmp             w3, NULL
    // 0x905430: b.ne            #0x905444
    // 0x905434: LoadField: r3 = r1->field_7b
    //     0x905434: ldur            w3, [x1, #0x7b]
    // 0x905438: DecompressPointer r3
    //     0x905438: add             x3, x3, HEAP, lsl #32
    // 0x90543c: mov             x1, x3
    // 0x905440: b               #0x905448
    // 0x905444: mov             x1, x3
    // 0x905448: stur            x1, [fp, #-0x18]
    // 0x90544c: r0 = PrayerTimeNotificationSetting()
    //     0x90544c: bl              #0x831100  ; AllocatePrayerTimeNotificationSettingStub -> PrayerTimeNotificationSetting (size=0x80)
    // 0x905450: mov             x1, x0
    // 0x905454: ldur            x0, [fp, #-0xa8]
    // 0x905458: stur            x1, [fp, #-0xb0]
    // 0x90545c: StoreField: r1->field_13 = r0
    //     0x90545c: stur            w0, [x1, #0x13]
    // 0x905460: ldur            x0, [fp, #-0xa0]
    // 0x905464: ArrayStore: r1[0] = r0  ; List_4
    //     0x905464: stur            w0, [x1, #0x17]
    // 0x905468: ldur            x0, [fp, #-0x98]
    // 0x90546c: StoreField: r1->field_1b = r0
    //     0x90546c: stur            w0, [x1, #0x1b]
    // 0x905470: ldur            x0, [fp, #-0x90]
    // 0x905474: StoreField: r1->field_1f = r0
    //     0x905474: stur            w0, [x1, #0x1f]
    // 0x905478: ldur            x0, [fp, #-0x88]
    // 0x90547c: StoreField: r1->field_23 = r0
    //     0x90547c: stur            w0, [x1, #0x23]
    // 0x905480: ldur            x0, [fp, #-0x80]
    // 0x905484: StoreField: r1->field_27 = r0
    //     0x905484: stur            w0, [x1, #0x27]
    // 0x905488: ldur            x0, [fp, #-0x78]
    // 0x90548c: StoreField: r1->field_2b = r0
    //     0x90548c: stur            w0, [x1, #0x2b]
    // 0x905490: ldur            x0, [fp, #-8]
    // 0x905494: StoreField: r1->field_2f = r0
    //     0x905494: stur            w0, [x1, #0x2f]
    // 0x905498: ldur            x0, [fp, #-0x70]
    // 0x90549c: StoreField: r1->field_33 = r0
    //     0x90549c: stur            w0, [x1, #0x33]
    // 0x9054a0: ldur            x0, [fp, #-0x68]
    // 0x9054a4: StoreField: r1->field_37 = r0
    //     0x9054a4: stur            w0, [x1, #0x37]
    // 0x9054a8: ldur            x0, [fp, #-0x60]
    // 0x9054ac: StoreField: r1->field_3b = r0
    //     0x9054ac: stur            w0, [x1, #0x3b]
    // 0x9054b0: ldur            x0, [fp, #-0x58]
    // 0x9054b4: StoreField: r1->field_3f = r0
    //     0x9054b4: stur            w0, [x1, #0x3f]
    // 0x9054b8: ldur            x0, [fp, #-0x50]
    // 0x9054bc: StoreField: r1->field_43 = r0
    //     0x9054bc: stur            w0, [x1, #0x43]
    // 0x9054c0: ldur            x0, [fp, #-0x48]
    // 0x9054c4: StoreField: r1->field_47 = r0
    //     0x9054c4: stur            w0, [x1, #0x47]
    // 0x9054c8: ldur            x0, [fp, #-0x40]
    // 0x9054cc: StoreField: r1->field_4b = r0
    //     0x9054cc: stur            x0, [x1, #0x4b]
    // 0x9054d0: ldur            x0, [fp, #-0x10]
    // 0x9054d4: StoreField: r1->field_53 = r0
    //     0x9054d4: stur            x0, [x1, #0x53]
    // 0x9054d8: ldur            x0, [fp, #-0x38]
    // 0x9054dc: StoreField: r1->field_5b = r0
    //     0x9054dc: stur            x0, [x1, #0x5b]
    // 0x9054e0: ldur            x0, [fp, #-0x30]
    // 0x9054e4: StoreField: r1->field_63 = r0
    //     0x9054e4: stur            x0, [x1, #0x63]
    // 0x9054e8: ldur            x0, [fp, #-0x28]
    // 0x9054ec: StoreField: r1->field_6b = r0
    //     0x9054ec: stur            x0, [x1, #0x6b]
    // 0x9054f0: ldur            x0, [fp, #-0x20]
    // 0x9054f4: StoreField: r1->field_73 = r0
    //     0x9054f4: stur            x0, [x1, #0x73]
    // 0x9054f8: ldur            x0, [fp, #-0x18]
    // 0x9054fc: StoreField: r1->field_7b = r0
    //     0x9054fc: stur            w0, [x1, #0x7b]
    // 0x905500: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x905500: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x905504: ldr             x16, [x16, #0x9f8]
    // 0x905508: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90550c: stp             lr, x16, [SP]
    // 0x905510: r0 = Map._fromLiteral()
    //     0x905510: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x905514: ldur            x1, [fp, #-0xb0]
    // 0x905518: StoreField: r1->field_f = r0
    //     0x905518: stur            w0, [x1, #0xf]
    //     0x90551c: ldurb           w16, [x1, #-1]
    //     0x905520: ldurb           w17, [x0, #-1]
    //     0x905524: and             x16, x17, x16, lsr #2
    //     0x905528: tst             x16, HEAP, lsr #32
    //     0x90552c: b.eq            #0x905534
    //     0x905530: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x905534: mov             x0, x1
    // 0x905538: LeaveFrame
    //     0x905538: mov             SP, fp
    //     0x90553c: ldp             fp, lr, [SP], #0x10
    // 0x905540: ret
    //     0x905540: ret             
    // 0x905544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905544: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905548: b               #0x9051f8
  }
}

// class id: 1653, size: 0x14, field offset: 0xc
class NotificationSoundAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa641b0, size: 0x200
    // 0xa641b0: EnterFrame
    //     0xa641b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa641b4: mov             fp, SP
    // 0xa641b8: LoadField: r3 = r2->field_23
    //     0xa641b8: ldur            x3, [x2, #0x23]
    // 0xa641bc: add             x0, x3, #1
    // 0xa641c0: LoadField: r1 = r2->field_1b
    //     0xa641c0: ldur            x1, [x2, #0x1b]
    // 0xa641c4: cmp             x0, x1
    // 0xa641c8: b.gt            #0xa64384
    // 0xa641cc: LoadField: r4 = r2->field_7
    //     0xa641cc: ldur            w4, [x2, #7]
    // 0xa641d0: DecompressPointer r4
    //     0xa641d0: add             x4, x4, HEAP, lsl #32
    // 0xa641d4: StoreField: r2->field_23 = r0
    //     0xa641d4: stur            x0, [x2, #0x23]
    // 0xa641d8: LoadField: r0 = r4->field_13
    //     0xa641d8: ldur            w0, [x4, #0x13]
    // 0xa641dc: r1 = LoadInt32Instr(r0)
    //     0xa641dc: sbfx            x1, x0, #1, #0x1f
    // 0xa641e0: mov             x0, x1
    // 0xa641e4: mov             x1, x3
    // 0xa641e8: cmp             x1, x0
    // 0xa641ec: b.hs            #0xa643ac
    // 0xa641f0: LoadField: r0 = r4->field_7
    //     0xa641f0: ldur            x0, [x4, #7]
    // 0xa641f4: ldrb            w1, [x0, x3]
    // 0xa641f8: cmp             x1, #6
    // 0xa641fc: b.gt            #0xa642c4
    // 0xa64200: cmp             x1, #3
    // 0xa64204: b.gt            #0xa64278
    // 0xa64208: cmp             x1, #1
    // 0xa6420c: b.gt            #0xa64248
    // 0xa64210: cmp             x1, #0
    // 0xa64214: b.gt            #0xa64234
    // 0xa64218: lsl             x0, x1, #1
    // 0xa6421c: cbnz            w0, #0xa64370
    // 0xa64220: r0 = Instance_NotificationSound
    //     0xa64220: add             x0, PP, #8, lsl #12  ; [pp+0x8220] Obj!NotificationSound@e30501
    //     0xa64224: ldr             x0, [x0, #0x220]
    // 0xa64228: LeaveFrame
    //     0xa64228: mov             SP, fp
    //     0xa6422c: ldp             fp, lr, [SP], #0x10
    // 0xa64230: ret
    //     0xa64230: ret             
    // 0xa64234: r0 = Instance_NotificationSound
    //     0xa64234: add             x0, PP, #0x21, lsl #12  ; [pp+0x21030] Obj!NotificationSound@e305e1
    //     0xa64238: ldr             x0, [x0, #0x30]
    // 0xa6423c: LeaveFrame
    //     0xa6423c: mov             SP, fp
    //     0xa64240: ldp             fp, lr, [SP], #0x10
    // 0xa64244: ret
    //     0xa64244: ret             
    // 0xa64248: cmp             x1, #2
    // 0xa6424c: b.gt            #0xa64264
    // 0xa64250: r0 = Instance_NotificationSound
    //     0xa64250: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9f0] Obj!NotificationSound@e304c1
    //     0xa64254: ldr             x0, [x0, #0x9f0]
    // 0xa64258: LeaveFrame
    //     0xa64258: mov             SP, fp
    //     0xa6425c: ldp             fp, lr, [SP], #0x10
    // 0xa64260: ret
    //     0xa64260: ret             
    // 0xa64264: r0 = Instance_NotificationSound
    //     0xa64264: add             x0, PP, #0x21, lsl #12  ; [pp+0x21038] Obj!NotificationSound@e305c1
    //     0xa64268: ldr             x0, [x0, #0x38]
    // 0xa6426c: LeaveFrame
    //     0xa6426c: mov             SP, fp
    //     0xa64270: ldp             fp, lr, [SP], #0x10
    // 0xa64274: ret
    //     0xa64274: ret             
    // 0xa64278: cmp             x1, #5
    // 0xa6427c: b.gt            #0xa642b0
    // 0xa64280: cmp             x1, #4
    // 0xa64284: b.gt            #0xa6429c
    // 0xa64288: r0 = Instance_NotificationSound
    //     0xa64288: add             x0, PP, #0x21, lsl #12  ; [pp+0x21040] Obj!NotificationSound@e305a1
    //     0xa6428c: ldr             x0, [x0, #0x40]
    // 0xa64290: LeaveFrame
    //     0xa64290: mov             SP, fp
    //     0xa64294: ldp             fp, lr, [SP], #0x10
    // 0xa64298: ret
    //     0xa64298: ret             
    // 0xa6429c: r0 = Instance_NotificationSound
    //     0xa6429c: add             x0, PP, #8, lsl #12  ; [pp+0x8a98] Obj!NotificationSound@e304a1
    //     0xa642a0: ldr             x0, [x0, #0xa98]
    // 0xa642a4: LeaveFrame
    //     0xa642a4: mov             SP, fp
    //     0xa642a8: ldp             fp, lr, [SP], #0x10
    // 0xa642ac: ret
    //     0xa642ac: ret             
    // 0xa642b0: r0 = Instance_NotificationSound
    //     0xa642b0: add             x0, PP, #0x21, lsl #12  ; [pp+0x21048] Obj!NotificationSound@e30581
    //     0xa642b4: ldr             x0, [x0, #0x48]
    // 0xa642b8: LeaveFrame
    //     0xa642b8: mov             SP, fp
    //     0xa642bc: ldp             fp, lr, [SP], #0x10
    // 0xa642c0: ret
    //     0xa642c0: ret             
    // 0xa642c4: cmp             x1, #9
    // 0xa642c8: b.gt            #0xa64318
    // 0xa642cc: cmp             x1, #8
    // 0xa642d0: b.gt            #0xa64304
    // 0xa642d4: cmp             x1, #7
    // 0xa642d8: b.gt            #0xa642f0
    // 0xa642dc: r0 = Instance_NotificationSound
    //     0xa642dc: add             x0, PP, #0xb, lsl #12  ; [pp+0xb9e8] Obj!NotificationSound@e304e1
    //     0xa642e0: ldr             x0, [x0, #0x9e8]
    // 0xa642e4: LeaveFrame
    //     0xa642e4: mov             SP, fp
    //     0xa642e8: ldp             fp, lr, [SP], #0x10
    // 0xa642ec: ret
    //     0xa642ec: ret             
    // 0xa642f0: r0 = Instance_NotificationSound
    //     0xa642f0: add             x0, PP, #8, lsl #12  ; [pp+0x8aa0] Obj!NotificationSound@e30481
    //     0xa642f4: ldr             x0, [x0, #0xaa0]
    // 0xa642f8: LeaveFrame
    //     0xa642f8: mov             SP, fp
    //     0xa642fc: ldp             fp, lr, [SP], #0x10
    // 0xa64300: ret
    //     0xa64300: ret             
    // 0xa64304: r0 = Instance_NotificationSound
    //     0xa64304: add             x0, PP, #8, lsl #12  ; [pp+0x8aa8] Obj!NotificationSound@e30461
    //     0xa64308: ldr             x0, [x0, #0xaa8]
    // 0xa6430c: LeaveFrame
    //     0xa6430c: mov             SP, fp
    //     0xa64310: ldp             fp, lr, [SP], #0x10
    // 0xa64314: ret
    //     0xa64314: ret             
    // 0xa64318: cmp             x1, #0xb
    // 0xa6431c: b.gt            #0xa64350
    // 0xa64320: cmp             x1, #0xa
    // 0xa64324: b.gt            #0xa6433c
    // 0xa64328: r0 = Instance_NotificationSound
    //     0xa64328: add             x0, PP, #0x21, lsl #12  ; [pp+0x21050] Obj!NotificationSound@e30561
    //     0xa6432c: ldr             x0, [x0, #0x50]
    // 0xa64330: LeaveFrame
    //     0xa64330: mov             SP, fp
    //     0xa64334: ldp             fp, lr, [SP], #0x10
    // 0xa64338: ret
    //     0xa64338: ret             
    // 0xa6433c: r0 = Instance_NotificationSound
    //     0xa6433c: add             x0, PP, #0x21, lsl #12  ; [pp+0x21058] Obj!NotificationSound@e30541
    //     0xa64340: ldr             x0, [x0, #0x58]
    // 0xa64344: LeaveFrame
    //     0xa64344: mov             SP, fp
    //     0xa64348: ldp             fp, lr, [SP], #0x10
    // 0xa6434c: ret
    //     0xa6434c: ret             
    // 0xa64350: lsl             x0, x1, #1
    // 0xa64354: cmp             w0, #0x18
    // 0xa64358: b.ne            #0xa64370
    // 0xa6435c: r0 = Instance_NotificationSound
    //     0xa6435c: add             x0, PP, #0x21, lsl #12  ; [pp+0x21060] Obj!NotificationSound@e30521
    //     0xa64360: ldr             x0, [x0, #0x60]
    // 0xa64364: LeaveFrame
    //     0xa64364: mov             SP, fp
    //     0xa64368: ldp             fp, lr, [SP], #0x10
    // 0xa6436c: ret
    //     0xa6436c: ret             
    // 0xa64370: r0 = Instance_NotificationSound
    //     0xa64370: add             x0, PP, #8, lsl #12  ; [pp+0x8220] Obj!NotificationSound@e30501
    //     0xa64374: ldr             x0, [x0, #0x220]
    // 0xa64378: LeaveFrame
    //     0xa64378: mov             SP, fp
    //     0xa6437c: ldp             fp, lr, [SP], #0x10
    // 0xa64380: ret
    //     0xa64380: ret             
    // 0xa64384: r0 = RangeError()
    //     0xa64384: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64388: mov             x1, x0
    // 0xa6438c: r0 = "Not enough bytes available."
    //     0xa6438c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64390: ldr             x0, [x0, #0x8a8]
    // 0xa64394: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64394: stur            w0, [x1, #0x17]
    // 0xa64398: r0 = false
    //     0xa64398: add             x0, NULL, #0x30  ; false
    // 0xa6439c: StoreField: r1->field_b = r0
    //     0xa6439c: stur            w0, [x1, #0xb]
    // 0xa643a0: mov             x0, x1
    // 0xa643a4: r0 = Throw()
    //     0xa643a4: bl              #0xec04b8  ; ThrowStub
    // 0xa643a8: brk             #0
    // 0xa643ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa643ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd5304, size: 0x678
    // 0xbd5304: EnterFrame
    //     0xbd5304: stp             fp, lr, [SP, #-0x10]!
    //     0xbd5308: mov             fp, SP
    // 0xbd530c: AllocStack(0x10)
    //     0xbd530c: sub             SP, SP, #0x10
    // 0xbd5310: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd5310: mov             x4, x2
    //     0xbd5314: stur            x2, [fp, #-8]
    //     0xbd5318: stur            x3, [fp, #-0x10]
    // 0xbd531c: CheckStackOverflow
    //     0xbd531c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd5320: cmp             SP, x16
    //     0xbd5324: b.ls            #0xbd5940
    // 0xbd5328: mov             x0, x3
    // 0xbd532c: r2 = Null
    //     0xbd532c: mov             x2, NULL
    // 0xbd5330: r1 = Null
    //     0xbd5330: mov             x1, NULL
    // 0xbd5334: r4 = 60
    //     0xbd5334: movz            x4, #0x3c
    // 0xbd5338: branchIfSmi(r0, 0xbd5344)
    //     0xbd5338: tbz             w0, #0, #0xbd5344
    // 0xbd533c: r4 = LoadClassIdInstr(r0)
    //     0xbd533c: ldur            x4, [x0, #-1]
    //     0xbd5340: ubfx            x4, x4, #0xc, #0x14
    // 0xbd5344: r17 = 6836
    //     0xbd5344: movz            x17, #0x1ab4
    // 0xbd5348: cmp             x4, x17
    // 0xbd534c: b.eq            #0xbd5364
    // 0xbd5350: r8 = NotificationSound
    //     0xbd5350: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xbd5354: ldr             x8, [x8, #0x288]
    // 0xbd5358: r3 = Null
    //     0xbd5358: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b290] Null
    //     0xbd535c: ldr             x3, [x3, #0x290]
    // 0xbd5360: r0 = NotificationSound()
    //     0xbd5360: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xbd5364: ldur            x0, [fp, #-0x10]
    // 0xbd5368: LoadField: r1 = r0->field_7
    //     0xbd5368: ldur            x1, [x0, #7]
    // 0xbd536c: cmp             x1, #6
    // 0xbd5370: b.gt            #0xbd5698
    // 0xbd5374: cmp             x1, #3
    // 0xbd5378: b.gt            #0xbd5548
    // 0xbd537c: cmp             x1, #1
    // 0xbd5380: b.gt            #0xbd5468
    // 0xbd5384: cmp             x1, #0
    // 0xbd5388: b.gt            #0xbd53f8
    // 0xbd538c: ldur            x0, [fp, #-8]
    // 0xbd5390: LoadField: r1 = r0->field_b
    //     0xbd5390: ldur            w1, [x0, #0xb]
    // 0xbd5394: DecompressPointer r1
    //     0xbd5394: add             x1, x1, HEAP, lsl #32
    // 0xbd5398: LoadField: r2 = r1->field_13
    //     0xbd5398: ldur            w2, [x1, #0x13]
    // 0xbd539c: LoadField: r1 = r0->field_13
    //     0xbd539c: ldur            x1, [x0, #0x13]
    // 0xbd53a0: r3 = LoadInt32Instr(r2)
    //     0xbd53a0: sbfx            x3, x2, #1, #0x1f
    // 0xbd53a4: sub             x2, x3, x1
    // 0xbd53a8: cmp             x2, #1
    // 0xbd53ac: b.ge            #0xbd53bc
    // 0xbd53b0: mov             x1, x0
    // 0xbd53b4: r2 = 1
    //     0xbd53b4: movz            x2, #0x1
    // 0xbd53b8: r0 = _increaseBufferSize()
    //     0xbd53b8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd53bc: ldur            x0, [fp, #-8]
    // 0xbd53c0: LoadField: r2 = r0->field_b
    //     0xbd53c0: ldur            w2, [x0, #0xb]
    // 0xbd53c4: DecompressPointer r2
    //     0xbd53c4: add             x2, x2, HEAP, lsl #32
    // 0xbd53c8: LoadField: r3 = r0->field_13
    //     0xbd53c8: ldur            x3, [x0, #0x13]
    // 0xbd53cc: add             x1, x3, #1
    // 0xbd53d0: StoreField: r0->field_13 = r1
    //     0xbd53d0: stur            x1, [x0, #0x13]
    // 0xbd53d4: LoadField: r0 = r2->field_13
    //     0xbd53d4: ldur            w0, [x2, #0x13]
    // 0xbd53d8: r1 = LoadInt32Instr(r0)
    //     0xbd53d8: sbfx            x1, x0, #1, #0x1f
    // 0xbd53dc: mov             x0, x1
    // 0xbd53e0: mov             x1, x3
    // 0xbd53e4: cmp             x1, x0
    // 0xbd53e8: b.hs            #0xbd5948
    // 0xbd53ec: ArrayStore: r2[r3] = rZR  ; TypeUnknown_1
    //     0xbd53ec: add             x0, x2, x3
    //     0xbd53f0: strb            wzr, [x0, #0x17]
    // 0xbd53f4: b               #0xbd5930
    // 0xbd53f8: ldur            x0, [fp, #-8]
    // 0xbd53fc: LoadField: r1 = r0->field_b
    //     0xbd53fc: ldur            w1, [x0, #0xb]
    // 0xbd5400: DecompressPointer r1
    //     0xbd5400: add             x1, x1, HEAP, lsl #32
    // 0xbd5404: LoadField: r2 = r1->field_13
    //     0xbd5404: ldur            w2, [x1, #0x13]
    // 0xbd5408: LoadField: r1 = r0->field_13
    //     0xbd5408: ldur            x1, [x0, #0x13]
    // 0xbd540c: r3 = LoadInt32Instr(r2)
    //     0xbd540c: sbfx            x3, x2, #1, #0x1f
    // 0xbd5410: sub             x2, x3, x1
    // 0xbd5414: cmp             x2, #1
    // 0xbd5418: b.ge            #0xbd5428
    // 0xbd541c: mov             x1, x0
    // 0xbd5420: r2 = 1
    //     0xbd5420: movz            x2, #0x1
    // 0xbd5424: r0 = _increaseBufferSize()
    //     0xbd5424: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5428: ldur            x0, [fp, #-8]
    // 0xbd542c: r2 = 1
    //     0xbd542c: movz            x2, #0x1
    // 0xbd5430: LoadField: r3 = r0->field_b
    //     0xbd5430: ldur            w3, [x0, #0xb]
    // 0xbd5434: DecompressPointer r3
    //     0xbd5434: add             x3, x3, HEAP, lsl #32
    // 0xbd5438: LoadField: r4 = r0->field_13
    //     0xbd5438: ldur            x4, [x0, #0x13]
    // 0xbd543c: add             x1, x4, #1
    // 0xbd5440: StoreField: r0->field_13 = r1
    //     0xbd5440: stur            x1, [x0, #0x13]
    // 0xbd5444: LoadField: r0 = r3->field_13
    //     0xbd5444: ldur            w0, [x3, #0x13]
    // 0xbd5448: r1 = LoadInt32Instr(r0)
    //     0xbd5448: sbfx            x1, x0, #1, #0x1f
    // 0xbd544c: mov             x0, x1
    // 0xbd5450: mov             x1, x4
    // 0xbd5454: cmp             x1, x0
    // 0xbd5458: b.hs            #0xbd594c
    // 0xbd545c: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd545c: add             x0, x3, x4
    //     0xbd5460: strb            w2, [x0, #0x17]
    // 0xbd5464: b               #0xbd5930
    // 0xbd5468: ldur            x0, [fp, #-8]
    // 0xbd546c: r2 = 1
    //     0xbd546c: movz            x2, #0x1
    // 0xbd5470: cmp             x1, #2
    // 0xbd5474: b.gt            #0xbd54e0
    // 0xbd5478: LoadField: r1 = r0->field_b
    //     0xbd5478: ldur            w1, [x0, #0xb]
    // 0xbd547c: DecompressPointer r1
    //     0xbd547c: add             x1, x1, HEAP, lsl #32
    // 0xbd5480: LoadField: r3 = r1->field_13
    //     0xbd5480: ldur            w3, [x1, #0x13]
    // 0xbd5484: LoadField: r1 = r0->field_13
    //     0xbd5484: ldur            x1, [x0, #0x13]
    // 0xbd5488: r4 = LoadInt32Instr(r3)
    //     0xbd5488: sbfx            x4, x3, #1, #0x1f
    // 0xbd548c: sub             x3, x4, x1
    // 0xbd5490: cmp             x3, #1
    // 0xbd5494: b.ge            #0xbd54a0
    // 0xbd5498: mov             x1, x0
    // 0xbd549c: r0 = _increaseBufferSize()
    //     0xbd549c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd54a0: ldur            x0, [fp, #-8]
    // 0xbd54a4: r2 = 2
    //     0xbd54a4: movz            x2, #0x2
    // 0xbd54a8: LoadField: r3 = r0->field_b
    //     0xbd54a8: ldur            w3, [x0, #0xb]
    // 0xbd54ac: DecompressPointer r3
    //     0xbd54ac: add             x3, x3, HEAP, lsl #32
    // 0xbd54b0: LoadField: r4 = r0->field_13
    //     0xbd54b0: ldur            x4, [x0, #0x13]
    // 0xbd54b4: add             x1, x4, #1
    // 0xbd54b8: StoreField: r0->field_13 = r1
    //     0xbd54b8: stur            x1, [x0, #0x13]
    // 0xbd54bc: LoadField: r0 = r3->field_13
    //     0xbd54bc: ldur            w0, [x3, #0x13]
    // 0xbd54c0: r1 = LoadInt32Instr(r0)
    //     0xbd54c0: sbfx            x1, x0, #1, #0x1f
    // 0xbd54c4: mov             x0, x1
    // 0xbd54c8: mov             x1, x4
    // 0xbd54cc: cmp             x1, x0
    // 0xbd54d0: b.hs            #0xbd5950
    // 0xbd54d4: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd54d4: add             x0, x3, x4
    //     0xbd54d8: strb            w2, [x0, #0x17]
    // 0xbd54dc: b               #0xbd5930
    // 0xbd54e0: LoadField: r1 = r0->field_b
    //     0xbd54e0: ldur            w1, [x0, #0xb]
    // 0xbd54e4: DecompressPointer r1
    //     0xbd54e4: add             x1, x1, HEAP, lsl #32
    // 0xbd54e8: LoadField: r3 = r1->field_13
    //     0xbd54e8: ldur            w3, [x1, #0x13]
    // 0xbd54ec: LoadField: r1 = r0->field_13
    //     0xbd54ec: ldur            x1, [x0, #0x13]
    // 0xbd54f0: r4 = LoadInt32Instr(r3)
    //     0xbd54f0: sbfx            x4, x3, #1, #0x1f
    // 0xbd54f4: sub             x3, x4, x1
    // 0xbd54f8: cmp             x3, #1
    // 0xbd54fc: b.ge            #0xbd5508
    // 0xbd5500: mov             x1, x0
    // 0xbd5504: r0 = _increaseBufferSize()
    //     0xbd5504: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5508: ldur            x0, [fp, #-8]
    // 0xbd550c: r2 = 3
    //     0xbd550c: movz            x2, #0x3
    // 0xbd5510: LoadField: r3 = r0->field_b
    //     0xbd5510: ldur            w3, [x0, #0xb]
    // 0xbd5514: DecompressPointer r3
    //     0xbd5514: add             x3, x3, HEAP, lsl #32
    // 0xbd5518: LoadField: r4 = r0->field_13
    //     0xbd5518: ldur            x4, [x0, #0x13]
    // 0xbd551c: add             x1, x4, #1
    // 0xbd5520: StoreField: r0->field_13 = r1
    //     0xbd5520: stur            x1, [x0, #0x13]
    // 0xbd5524: LoadField: r0 = r3->field_13
    //     0xbd5524: ldur            w0, [x3, #0x13]
    // 0xbd5528: r1 = LoadInt32Instr(r0)
    //     0xbd5528: sbfx            x1, x0, #1, #0x1f
    // 0xbd552c: mov             x0, x1
    // 0xbd5530: mov             x1, x4
    // 0xbd5534: cmp             x1, x0
    // 0xbd5538: b.hs            #0xbd5954
    // 0xbd553c: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd553c: add             x0, x3, x4
    //     0xbd5540: strb            w2, [x0, #0x17]
    // 0xbd5544: b               #0xbd5930
    // 0xbd5548: ldur            x0, [fp, #-8]
    // 0xbd554c: r2 = 1
    //     0xbd554c: movz            x2, #0x1
    // 0xbd5550: cmp             x1, #5
    // 0xbd5554: b.gt            #0xbd5630
    // 0xbd5558: cmp             x1, #4
    // 0xbd555c: b.gt            #0xbd55c8
    // 0xbd5560: LoadField: r1 = r0->field_b
    //     0xbd5560: ldur            w1, [x0, #0xb]
    // 0xbd5564: DecompressPointer r1
    //     0xbd5564: add             x1, x1, HEAP, lsl #32
    // 0xbd5568: LoadField: r3 = r1->field_13
    //     0xbd5568: ldur            w3, [x1, #0x13]
    // 0xbd556c: LoadField: r1 = r0->field_13
    //     0xbd556c: ldur            x1, [x0, #0x13]
    // 0xbd5570: r4 = LoadInt32Instr(r3)
    //     0xbd5570: sbfx            x4, x3, #1, #0x1f
    // 0xbd5574: sub             x3, x4, x1
    // 0xbd5578: cmp             x3, #1
    // 0xbd557c: b.ge            #0xbd5588
    // 0xbd5580: mov             x1, x0
    // 0xbd5584: r0 = _increaseBufferSize()
    //     0xbd5584: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5588: ldur            x0, [fp, #-8]
    // 0xbd558c: r2 = 4
    //     0xbd558c: movz            x2, #0x4
    // 0xbd5590: LoadField: r3 = r0->field_b
    //     0xbd5590: ldur            w3, [x0, #0xb]
    // 0xbd5594: DecompressPointer r3
    //     0xbd5594: add             x3, x3, HEAP, lsl #32
    // 0xbd5598: LoadField: r4 = r0->field_13
    //     0xbd5598: ldur            x4, [x0, #0x13]
    // 0xbd559c: add             x1, x4, #1
    // 0xbd55a0: StoreField: r0->field_13 = r1
    //     0xbd55a0: stur            x1, [x0, #0x13]
    // 0xbd55a4: LoadField: r0 = r3->field_13
    //     0xbd55a4: ldur            w0, [x3, #0x13]
    // 0xbd55a8: r1 = LoadInt32Instr(r0)
    //     0xbd55a8: sbfx            x1, x0, #1, #0x1f
    // 0xbd55ac: mov             x0, x1
    // 0xbd55b0: mov             x1, x4
    // 0xbd55b4: cmp             x1, x0
    // 0xbd55b8: b.hs            #0xbd5958
    // 0xbd55bc: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd55bc: add             x0, x3, x4
    //     0xbd55c0: strb            w2, [x0, #0x17]
    // 0xbd55c4: b               #0xbd5930
    // 0xbd55c8: LoadField: r1 = r0->field_b
    //     0xbd55c8: ldur            w1, [x0, #0xb]
    // 0xbd55cc: DecompressPointer r1
    //     0xbd55cc: add             x1, x1, HEAP, lsl #32
    // 0xbd55d0: LoadField: r3 = r1->field_13
    //     0xbd55d0: ldur            w3, [x1, #0x13]
    // 0xbd55d4: LoadField: r1 = r0->field_13
    //     0xbd55d4: ldur            x1, [x0, #0x13]
    // 0xbd55d8: r4 = LoadInt32Instr(r3)
    //     0xbd55d8: sbfx            x4, x3, #1, #0x1f
    // 0xbd55dc: sub             x3, x4, x1
    // 0xbd55e0: cmp             x3, #1
    // 0xbd55e4: b.ge            #0xbd55f0
    // 0xbd55e8: mov             x1, x0
    // 0xbd55ec: r0 = _increaseBufferSize()
    //     0xbd55ec: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd55f0: ldur            x0, [fp, #-8]
    // 0xbd55f4: r2 = 5
    //     0xbd55f4: movz            x2, #0x5
    // 0xbd55f8: LoadField: r3 = r0->field_b
    //     0xbd55f8: ldur            w3, [x0, #0xb]
    // 0xbd55fc: DecompressPointer r3
    //     0xbd55fc: add             x3, x3, HEAP, lsl #32
    // 0xbd5600: LoadField: r4 = r0->field_13
    //     0xbd5600: ldur            x4, [x0, #0x13]
    // 0xbd5604: add             x1, x4, #1
    // 0xbd5608: StoreField: r0->field_13 = r1
    //     0xbd5608: stur            x1, [x0, #0x13]
    // 0xbd560c: LoadField: r0 = r3->field_13
    //     0xbd560c: ldur            w0, [x3, #0x13]
    // 0xbd5610: r1 = LoadInt32Instr(r0)
    //     0xbd5610: sbfx            x1, x0, #1, #0x1f
    // 0xbd5614: mov             x0, x1
    // 0xbd5618: mov             x1, x4
    // 0xbd561c: cmp             x1, x0
    // 0xbd5620: b.hs            #0xbd595c
    // 0xbd5624: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd5624: add             x0, x3, x4
    //     0xbd5628: strb            w2, [x0, #0x17]
    // 0xbd562c: b               #0xbd5930
    // 0xbd5630: LoadField: r1 = r0->field_b
    //     0xbd5630: ldur            w1, [x0, #0xb]
    // 0xbd5634: DecompressPointer r1
    //     0xbd5634: add             x1, x1, HEAP, lsl #32
    // 0xbd5638: LoadField: r3 = r1->field_13
    //     0xbd5638: ldur            w3, [x1, #0x13]
    // 0xbd563c: LoadField: r1 = r0->field_13
    //     0xbd563c: ldur            x1, [x0, #0x13]
    // 0xbd5640: r4 = LoadInt32Instr(r3)
    //     0xbd5640: sbfx            x4, x3, #1, #0x1f
    // 0xbd5644: sub             x3, x4, x1
    // 0xbd5648: cmp             x3, #1
    // 0xbd564c: b.ge            #0xbd5658
    // 0xbd5650: mov             x1, x0
    // 0xbd5654: r0 = _increaseBufferSize()
    //     0xbd5654: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5658: ldur            x0, [fp, #-8]
    // 0xbd565c: r2 = 6
    //     0xbd565c: movz            x2, #0x6
    // 0xbd5660: LoadField: r3 = r0->field_b
    //     0xbd5660: ldur            w3, [x0, #0xb]
    // 0xbd5664: DecompressPointer r3
    //     0xbd5664: add             x3, x3, HEAP, lsl #32
    // 0xbd5668: LoadField: r4 = r0->field_13
    //     0xbd5668: ldur            x4, [x0, #0x13]
    // 0xbd566c: add             x1, x4, #1
    // 0xbd5670: StoreField: r0->field_13 = r1
    //     0xbd5670: stur            x1, [x0, #0x13]
    // 0xbd5674: LoadField: r0 = r3->field_13
    //     0xbd5674: ldur            w0, [x3, #0x13]
    // 0xbd5678: r1 = LoadInt32Instr(r0)
    //     0xbd5678: sbfx            x1, x0, #1, #0x1f
    // 0xbd567c: mov             x0, x1
    // 0xbd5680: mov             x1, x4
    // 0xbd5684: cmp             x1, x0
    // 0xbd5688: b.hs            #0xbd5960
    // 0xbd568c: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd568c: add             x0, x3, x4
    //     0xbd5690: strb            w2, [x0, #0x17]
    // 0xbd5694: b               #0xbd5930
    // 0xbd5698: ldur            x0, [fp, #-8]
    // 0xbd569c: r2 = 1
    //     0xbd569c: movz            x2, #0x1
    // 0xbd56a0: cmp             x1, #9
    // 0xbd56a4: b.gt            #0xbd57f0
    // 0xbd56a8: cmp             x1, #8
    // 0xbd56ac: b.gt            #0xbd5788
    // 0xbd56b0: cmp             x1, #7
    // 0xbd56b4: b.gt            #0xbd5720
    // 0xbd56b8: LoadField: r1 = r0->field_b
    //     0xbd56b8: ldur            w1, [x0, #0xb]
    // 0xbd56bc: DecompressPointer r1
    //     0xbd56bc: add             x1, x1, HEAP, lsl #32
    // 0xbd56c0: LoadField: r3 = r1->field_13
    //     0xbd56c0: ldur            w3, [x1, #0x13]
    // 0xbd56c4: LoadField: r1 = r0->field_13
    //     0xbd56c4: ldur            x1, [x0, #0x13]
    // 0xbd56c8: r4 = LoadInt32Instr(r3)
    //     0xbd56c8: sbfx            x4, x3, #1, #0x1f
    // 0xbd56cc: sub             x3, x4, x1
    // 0xbd56d0: cmp             x3, #1
    // 0xbd56d4: b.ge            #0xbd56e0
    // 0xbd56d8: mov             x1, x0
    // 0xbd56dc: r0 = _increaseBufferSize()
    //     0xbd56dc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd56e0: ldur            x0, [fp, #-8]
    // 0xbd56e4: r2 = 7
    //     0xbd56e4: movz            x2, #0x7
    // 0xbd56e8: LoadField: r3 = r0->field_b
    //     0xbd56e8: ldur            w3, [x0, #0xb]
    // 0xbd56ec: DecompressPointer r3
    //     0xbd56ec: add             x3, x3, HEAP, lsl #32
    // 0xbd56f0: LoadField: r4 = r0->field_13
    //     0xbd56f0: ldur            x4, [x0, #0x13]
    // 0xbd56f4: add             x1, x4, #1
    // 0xbd56f8: StoreField: r0->field_13 = r1
    //     0xbd56f8: stur            x1, [x0, #0x13]
    // 0xbd56fc: LoadField: r0 = r3->field_13
    //     0xbd56fc: ldur            w0, [x3, #0x13]
    // 0xbd5700: r1 = LoadInt32Instr(r0)
    //     0xbd5700: sbfx            x1, x0, #1, #0x1f
    // 0xbd5704: mov             x0, x1
    // 0xbd5708: mov             x1, x4
    // 0xbd570c: cmp             x1, x0
    // 0xbd5710: b.hs            #0xbd5964
    // 0xbd5714: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd5714: add             x0, x3, x4
    //     0xbd5718: strb            w2, [x0, #0x17]
    // 0xbd571c: b               #0xbd5930
    // 0xbd5720: LoadField: r1 = r0->field_b
    //     0xbd5720: ldur            w1, [x0, #0xb]
    // 0xbd5724: DecompressPointer r1
    //     0xbd5724: add             x1, x1, HEAP, lsl #32
    // 0xbd5728: LoadField: r3 = r1->field_13
    //     0xbd5728: ldur            w3, [x1, #0x13]
    // 0xbd572c: LoadField: r1 = r0->field_13
    //     0xbd572c: ldur            x1, [x0, #0x13]
    // 0xbd5730: r4 = LoadInt32Instr(r3)
    //     0xbd5730: sbfx            x4, x3, #1, #0x1f
    // 0xbd5734: sub             x3, x4, x1
    // 0xbd5738: cmp             x3, #1
    // 0xbd573c: b.ge            #0xbd5748
    // 0xbd5740: mov             x1, x0
    // 0xbd5744: r0 = _increaseBufferSize()
    //     0xbd5744: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5748: ldur            x0, [fp, #-8]
    // 0xbd574c: r2 = 8
    //     0xbd574c: movz            x2, #0x8
    // 0xbd5750: LoadField: r3 = r0->field_b
    //     0xbd5750: ldur            w3, [x0, #0xb]
    // 0xbd5754: DecompressPointer r3
    //     0xbd5754: add             x3, x3, HEAP, lsl #32
    // 0xbd5758: LoadField: r4 = r0->field_13
    //     0xbd5758: ldur            x4, [x0, #0x13]
    // 0xbd575c: add             x1, x4, #1
    // 0xbd5760: StoreField: r0->field_13 = r1
    //     0xbd5760: stur            x1, [x0, #0x13]
    // 0xbd5764: LoadField: r0 = r3->field_13
    //     0xbd5764: ldur            w0, [x3, #0x13]
    // 0xbd5768: r1 = LoadInt32Instr(r0)
    //     0xbd5768: sbfx            x1, x0, #1, #0x1f
    // 0xbd576c: mov             x0, x1
    // 0xbd5770: mov             x1, x4
    // 0xbd5774: cmp             x1, x0
    // 0xbd5778: b.hs            #0xbd5968
    // 0xbd577c: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd577c: add             x0, x3, x4
    //     0xbd5780: strb            w2, [x0, #0x17]
    // 0xbd5784: b               #0xbd5930
    // 0xbd5788: LoadField: r1 = r0->field_b
    //     0xbd5788: ldur            w1, [x0, #0xb]
    // 0xbd578c: DecompressPointer r1
    //     0xbd578c: add             x1, x1, HEAP, lsl #32
    // 0xbd5790: LoadField: r3 = r1->field_13
    //     0xbd5790: ldur            w3, [x1, #0x13]
    // 0xbd5794: LoadField: r1 = r0->field_13
    //     0xbd5794: ldur            x1, [x0, #0x13]
    // 0xbd5798: r4 = LoadInt32Instr(r3)
    //     0xbd5798: sbfx            x4, x3, #1, #0x1f
    // 0xbd579c: sub             x3, x4, x1
    // 0xbd57a0: cmp             x3, #1
    // 0xbd57a4: b.ge            #0xbd57b0
    // 0xbd57a8: mov             x1, x0
    // 0xbd57ac: r0 = _increaseBufferSize()
    //     0xbd57ac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd57b0: ldur            x0, [fp, #-8]
    // 0xbd57b4: r2 = 9
    //     0xbd57b4: movz            x2, #0x9
    // 0xbd57b8: LoadField: r3 = r0->field_b
    //     0xbd57b8: ldur            w3, [x0, #0xb]
    // 0xbd57bc: DecompressPointer r3
    //     0xbd57bc: add             x3, x3, HEAP, lsl #32
    // 0xbd57c0: LoadField: r4 = r0->field_13
    //     0xbd57c0: ldur            x4, [x0, #0x13]
    // 0xbd57c4: add             x1, x4, #1
    // 0xbd57c8: StoreField: r0->field_13 = r1
    //     0xbd57c8: stur            x1, [x0, #0x13]
    // 0xbd57cc: LoadField: r0 = r3->field_13
    //     0xbd57cc: ldur            w0, [x3, #0x13]
    // 0xbd57d0: r1 = LoadInt32Instr(r0)
    //     0xbd57d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd57d4: mov             x0, x1
    // 0xbd57d8: mov             x1, x4
    // 0xbd57dc: cmp             x1, x0
    // 0xbd57e0: b.hs            #0xbd596c
    // 0xbd57e4: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd57e4: add             x0, x3, x4
    //     0xbd57e8: strb            w2, [x0, #0x17]
    // 0xbd57ec: b               #0xbd5930
    // 0xbd57f0: cmp             x1, #0xb
    // 0xbd57f4: b.gt            #0xbd58d0
    // 0xbd57f8: cmp             x1, #0xa
    // 0xbd57fc: b.gt            #0xbd5868
    // 0xbd5800: LoadField: r1 = r0->field_b
    //     0xbd5800: ldur            w1, [x0, #0xb]
    // 0xbd5804: DecompressPointer r1
    //     0xbd5804: add             x1, x1, HEAP, lsl #32
    // 0xbd5808: LoadField: r3 = r1->field_13
    //     0xbd5808: ldur            w3, [x1, #0x13]
    // 0xbd580c: LoadField: r1 = r0->field_13
    //     0xbd580c: ldur            x1, [x0, #0x13]
    // 0xbd5810: r4 = LoadInt32Instr(r3)
    //     0xbd5810: sbfx            x4, x3, #1, #0x1f
    // 0xbd5814: sub             x3, x4, x1
    // 0xbd5818: cmp             x3, #1
    // 0xbd581c: b.ge            #0xbd5828
    // 0xbd5820: mov             x1, x0
    // 0xbd5824: r0 = _increaseBufferSize()
    //     0xbd5824: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5828: ldur            x0, [fp, #-8]
    // 0xbd582c: r2 = 10
    //     0xbd582c: movz            x2, #0xa
    // 0xbd5830: LoadField: r3 = r0->field_b
    //     0xbd5830: ldur            w3, [x0, #0xb]
    // 0xbd5834: DecompressPointer r3
    //     0xbd5834: add             x3, x3, HEAP, lsl #32
    // 0xbd5838: LoadField: r4 = r0->field_13
    //     0xbd5838: ldur            x4, [x0, #0x13]
    // 0xbd583c: add             x1, x4, #1
    // 0xbd5840: StoreField: r0->field_13 = r1
    //     0xbd5840: stur            x1, [x0, #0x13]
    // 0xbd5844: LoadField: r0 = r3->field_13
    //     0xbd5844: ldur            w0, [x3, #0x13]
    // 0xbd5848: r1 = LoadInt32Instr(r0)
    //     0xbd5848: sbfx            x1, x0, #1, #0x1f
    // 0xbd584c: mov             x0, x1
    // 0xbd5850: mov             x1, x4
    // 0xbd5854: cmp             x1, x0
    // 0xbd5858: b.hs            #0xbd5970
    // 0xbd585c: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd585c: add             x0, x3, x4
    //     0xbd5860: strb            w2, [x0, #0x17]
    // 0xbd5864: b               #0xbd5930
    // 0xbd5868: LoadField: r1 = r0->field_b
    //     0xbd5868: ldur            w1, [x0, #0xb]
    // 0xbd586c: DecompressPointer r1
    //     0xbd586c: add             x1, x1, HEAP, lsl #32
    // 0xbd5870: LoadField: r3 = r1->field_13
    //     0xbd5870: ldur            w3, [x1, #0x13]
    // 0xbd5874: LoadField: r1 = r0->field_13
    //     0xbd5874: ldur            x1, [x0, #0x13]
    // 0xbd5878: r4 = LoadInt32Instr(r3)
    //     0xbd5878: sbfx            x4, x3, #1, #0x1f
    // 0xbd587c: sub             x3, x4, x1
    // 0xbd5880: cmp             x3, #1
    // 0xbd5884: b.ge            #0xbd5890
    // 0xbd5888: mov             x1, x0
    // 0xbd588c: r0 = _increaseBufferSize()
    //     0xbd588c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5890: ldur            x0, [fp, #-8]
    // 0xbd5894: r2 = 11
    //     0xbd5894: movz            x2, #0xb
    // 0xbd5898: LoadField: r3 = r0->field_b
    //     0xbd5898: ldur            w3, [x0, #0xb]
    // 0xbd589c: DecompressPointer r3
    //     0xbd589c: add             x3, x3, HEAP, lsl #32
    // 0xbd58a0: LoadField: r4 = r0->field_13
    //     0xbd58a0: ldur            x4, [x0, #0x13]
    // 0xbd58a4: add             x1, x4, #1
    // 0xbd58a8: StoreField: r0->field_13 = r1
    //     0xbd58a8: stur            x1, [x0, #0x13]
    // 0xbd58ac: LoadField: r0 = r3->field_13
    //     0xbd58ac: ldur            w0, [x3, #0x13]
    // 0xbd58b0: r1 = LoadInt32Instr(r0)
    //     0xbd58b0: sbfx            x1, x0, #1, #0x1f
    // 0xbd58b4: mov             x0, x1
    // 0xbd58b8: mov             x1, x4
    // 0xbd58bc: cmp             x1, x0
    // 0xbd58c0: b.hs            #0xbd5974
    // 0xbd58c4: ArrayStore: r3[r4] = r2  ; TypeUnknown_1
    //     0xbd58c4: add             x0, x3, x4
    //     0xbd58c8: strb            w2, [x0, #0x17]
    // 0xbd58cc: b               #0xbd5930
    // 0xbd58d0: LoadField: r1 = r0->field_b
    //     0xbd58d0: ldur            w1, [x0, #0xb]
    // 0xbd58d4: DecompressPointer r1
    //     0xbd58d4: add             x1, x1, HEAP, lsl #32
    // 0xbd58d8: LoadField: r3 = r1->field_13
    //     0xbd58d8: ldur            w3, [x1, #0x13]
    // 0xbd58dc: LoadField: r1 = r0->field_13
    //     0xbd58dc: ldur            x1, [x0, #0x13]
    // 0xbd58e0: r4 = LoadInt32Instr(r3)
    //     0xbd58e0: sbfx            x4, x3, #1, #0x1f
    // 0xbd58e4: sub             x3, x4, x1
    // 0xbd58e8: cmp             x3, #1
    // 0xbd58ec: b.ge            #0xbd58f8
    // 0xbd58f0: mov             x1, x0
    // 0xbd58f4: r0 = _increaseBufferSize()
    //     0xbd58f4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd58f8: ldur            x2, [fp, #-8]
    // 0xbd58fc: r3 = 12
    //     0xbd58fc: movz            x3, #0xc
    // 0xbd5900: LoadField: r4 = r2->field_b
    //     0xbd5900: ldur            w4, [x2, #0xb]
    // 0xbd5904: DecompressPointer r4
    //     0xbd5904: add             x4, x4, HEAP, lsl #32
    // 0xbd5908: LoadField: r5 = r2->field_13
    //     0xbd5908: ldur            x5, [x2, #0x13]
    // 0xbd590c: add             x6, x5, #1
    // 0xbd5910: StoreField: r2->field_13 = r6
    //     0xbd5910: stur            x6, [x2, #0x13]
    // 0xbd5914: LoadField: r2 = r4->field_13
    //     0xbd5914: ldur            w2, [x4, #0x13]
    // 0xbd5918: r0 = LoadInt32Instr(r2)
    //     0xbd5918: sbfx            x0, x2, #1, #0x1f
    // 0xbd591c: mov             x1, x5
    // 0xbd5920: cmp             x1, x0
    // 0xbd5924: b.hs            #0xbd5978
    // 0xbd5928: ArrayStore: r4[r5] = r3  ; TypeUnknown_1
    //     0xbd5928: add             x1, x4, x5
    //     0xbd592c: strb            w3, [x1, #0x17]
    // 0xbd5930: r0 = Null
    //     0xbd5930: mov             x0, NULL
    // 0xbd5934: LeaveFrame
    //     0xbd5934: mov             SP, fp
    //     0xbd5938: ldp             fp, lr, [SP], #0x10
    // 0xbd593c: ret
    //     0xbd593c: ret             
    // 0xbd5940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd5940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd5944: b               #0xbd5328
    // 0xbd5948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5948: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd594c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd594c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5950: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5950: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5954: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5954: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5958: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5958: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd595c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd595c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5960: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5960: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5964: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5964: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5968: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5968: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd596c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd596c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5970: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5970: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5974: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5974: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5978: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5978: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0264, size: 0x24
    // 0xbf0264: r1 = 54
    //     0xbf0264: movz            x1, #0x36
    // 0xbf0268: r16 = LoadInt32Instr(r1)
    //     0xbf0268: sbfx            x16, x1, #1, #0x1f
    // 0xbf026c: r17 = 11601
    //     0xbf026c: movz            x17, #0x2d51
    // 0xbf0270: mul             x0, x16, x17
    // 0xbf0274: umulh           x16, x16, x17
    // 0xbf0278: eor             x0, x0, x16
    // 0xbf027c: r0 = 0
    //     0xbf027c: eor             x0, x0, x0, lsr #32
    // 0xbf0280: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0284: ret
    //     0xbf0284: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76ad0, size: 0x9c
    // 0xd76ad0: EnterFrame
    //     0xd76ad0: stp             fp, lr, [SP, #-0x10]!
    //     0xd76ad4: mov             fp, SP
    // 0xd76ad8: AllocStack(0x10)
    //     0xd76ad8: sub             SP, SP, #0x10
    // 0xd76adc: CheckStackOverflow
    //     0xd76adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76ae0: cmp             SP, x16
    //     0xd76ae4: b.ls            #0xd76b64
    // 0xd76ae8: ldr             x0, [fp, #0x10]
    // 0xd76aec: cmp             w0, NULL
    // 0xd76af0: b.ne            #0xd76b04
    // 0xd76af4: r0 = false
    //     0xd76af4: add             x0, NULL, #0x30  ; false
    // 0xd76af8: LeaveFrame
    //     0xd76af8: mov             SP, fp
    //     0xd76afc: ldp             fp, lr, [SP], #0x10
    // 0xd76b00: ret
    //     0xd76b00: ret             
    // 0xd76b04: ldr             x1, [fp, #0x18]
    // 0xd76b08: cmp             w1, w0
    // 0xd76b0c: b.ne            #0xd76b18
    // 0xd76b10: r0 = true
    //     0xd76b10: add             x0, NULL, #0x20  ; true
    // 0xd76b14: b               #0xd76b58
    // 0xd76b18: r1 = 60
    //     0xd76b18: movz            x1, #0x3c
    // 0xd76b1c: branchIfSmi(r0, 0xd76b28)
    //     0xd76b1c: tbz             w0, #0, #0xd76b28
    // 0xd76b20: r1 = LoadClassIdInstr(r0)
    //     0xd76b20: ldur            x1, [x0, #-1]
    //     0xd76b24: ubfx            x1, x1, #0xc, #0x14
    // 0xd76b28: cmp             x1, #0x675
    // 0xd76b2c: b.ne            #0xd76b54
    // 0xd76b30: r16 = NotificationSoundAdapter
    //     0xd76b30: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b280] Type: NotificationSoundAdapter
    //     0xd76b34: ldr             x16, [x16, #0x280]
    // 0xd76b38: r30 = NotificationSoundAdapter
    //     0xd76b38: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b280] Type: NotificationSoundAdapter
    //     0xd76b3c: ldr             lr, [lr, #0x280]
    // 0xd76b40: stp             lr, x16, [SP]
    // 0xd76b44: r0 = ==()
    //     0xd76b44: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76b48: tbnz            w0, #4, #0xd76b54
    // 0xd76b4c: r0 = true
    //     0xd76b4c: add             x0, NULL, #0x20  ; true
    // 0xd76b50: b               #0xd76b58
    // 0xd76b54: r0 = false
    //     0xd76b54: add             x0, NULL, #0x30  ; false
    // 0xd76b58: LeaveFrame
    //     0xd76b58: mov             SP, fp
    //     0xd76b5c: ldp             fp, lr, [SP], #0x10
    // 0xd76b60: ret
    //     0xd76b60: ret             
    // 0xd76b64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76b64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76b68: b               #0xd76ae8
  }
}

// class id: 1654, size: 0x14, field offset: 0xc
class PrayerTimeNotificationSettingAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa635c8, size: 0xbe8
    // 0xa635c8: EnterFrame
    //     0xa635c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa635cc: mov             fp, SP
    // 0xa635d0: AllocStack(0xe0)
    //     0xa635d0: sub             SP, SP, #0xe0
    // 0xa635d4: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa635d4: stur            x2, [fp, #-0x20]
    // 0xa635d8: CheckStackOverflow
    //     0xa635d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa635dc: cmp             SP, x16
    //     0xa635e0: b.ls            #0xa64198
    // 0xa635e4: LoadField: r3 = r2->field_23
    //     0xa635e4: ldur            x3, [x2, #0x23]
    // 0xa635e8: add             x0, x3, #1
    // 0xa635ec: LoadField: r1 = r2->field_1b
    //     0xa635ec: ldur            x1, [x2, #0x1b]
    // 0xa635f0: cmp             x0, x1
    // 0xa635f4: b.gt            #0xa6413c
    // 0xa635f8: LoadField: r4 = r2->field_7
    //     0xa635f8: ldur            w4, [x2, #7]
    // 0xa635fc: DecompressPointer r4
    //     0xa635fc: add             x4, x4, HEAP, lsl #32
    // 0xa63600: stur            x4, [fp, #-0x18]
    // 0xa63604: StoreField: r2->field_23 = r0
    //     0xa63604: stur            x0, [x2, #0x23]
    // 0xa63608: LoadField: r0 = r4->field_13
    //     0xa63608: ldur            w0, [x4, #0x13]
    // 0xa6360c: r5 = LoadInt32Instr(r0)
    //     0xa6360c: sbfx            x5, x0, #1, #0x1f
    // 0xa63610: mov             x0, x5
    // 0xa63614: mov             x1, x3
    // 0xa63618: stur            x5, [fp, #-0x10]
    // 0xa6361c: cmp             x1, x0
    // 0xa63620: b.hs            #0xa641a0
    // 0xa63624: LoadField: r0 = r4->field_7
    //     0xa63624: ldur            x0, [x4, #7]
    // 0xa63628: ldrb            w1, [x0, x3]
    // 0xa6362c: stur            x1, [fp, #-8]
    // 0xa63630: r16 = <int, dynamic>
    //     0xa63630: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa63634: ldr             x16, [x16, #0xac0]
    // 0xa63638: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6363c: stp             lr, x16, [SP]
    // 0xa63640: r0 = Map._fromLiteral()
    //     0xa63640: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa63644: mov             x2, x0
    // 0xa63648: stur            x2, [fp, #-0x38]
    // 0xa6364c: r6 = 0
    //     0xa6364c: movz            x6, #0
    // 0xa63650: ldur            x3, [fp, #-0x20]
    // 0xa63654: ldur            x4, [fp, #-0x18]
    // 0xa63658: ldur            x5, [fp, #-8]
    // 0xa6365c: stur            x6, [fp, #-0x30]
    // 0xa63660: CheckStackOverflow
    //     0xa63660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa63664: cmp             SP, x16
    //     0xa63668: b.ls            #0xa641a4
    // 0xa6366c: cmp             x6, x5
    // 0xa63670: b.ge            #0xa636fc
    // 0xa63674: LoadField: r7 = r3->field_23
    //     0xa63674: ldur            x7, [x3, #0x23]
    // 0xa63678: add             x0, x7, #1
    // 0xa6367c: LoadField: r1 = r3->field_1b
    //     0xa6367c: ldur            x1, [x3, #0x1b]
    // 0xa63680: cmp             x0, x1
    // 0xa63684: b.gt            #0xa64164
    // 0xa63688: StoreField: r3->field_23 = r0
    //     0xa63688: stur            x0, [x3, #0x23]
    // 0xa6368c: ldur            x0, [fp, #-0x10]
    // 0xa63690: mov             x1, x7
    // 0xa63694: cmp             x1, x0
    // 0xa63698: b.hs            #0xa641ac
    // 0xa6369c: LoadField: r0 = r4->field_7
    //     0xa6369c: ldur            x0, [x4, #7]
    // 0xa636a0: ldrb            w8, [x0, x7]
    // 0xa636a4: mov             x1, x3
    // 0xa636a8: stur            x8, [fp, #-0x28]
    // 0xa636ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa636ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa636b0: r0 = read()
    //     0xa636b0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa636b4: mov             x1, x0
    // 0xa636b8: ldur            x0, [fp, #-0x28]
    // 0xa636bc: lsl             x2, x0, #1
    // 0xa636c0: r16 = LoadInt32Instr(r2)
    //     0xa636c0: sbfx            x16, x2, #1, #0x1f
    // 0xa636c4: r17 = 11601
    //     0xa636c4: movz            x17, #0x2d51
    // 0xa636c8: mul             x0, x16, x17
    // 0xa636cc: umulh           x16, x16, x17
    // 0xa636d0: eor             x0, x0, x16
    // 0xa636d4: r0 = 0
    //     0xa636d4: eor             x0, x0, x0, lsr #32
    // 0xa636d8: ubfiz           x0, x0, #1, #0x1e
    // 0xa636dc: r5 = LoadInt32Instr(r0)
    //     0xa636dc: sbfx            x5, x0, #1, #0x1f
    // 0xa636e0: mov             x3, x1
    // 0xa636e4: ldur            x1, [fp, #-0x38]
    // 0xa636e8: r0 = _set()
    //     0xa636e8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa636ec: ldur            x0, [fp, #-0x30]
    // 0xa636f0: add             x6, x0, #1
    // 0xa636f4: ldur            x2, [fp, #-0x38]
    // 0xa636f8: b               #0xa63650
    // 0xa636fc: mov             x0, x2
    // 0xa63700: mov             x1, x0
    // 0xa63704: r2 = 0
    //     0xa63704: movz            x2, #0
    // 0xa63708: r0 = _getValueOrData()
    //     0xa63708: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6370c: ldur            x3, [fp, #-0x38]
    // 0xa63710: LoadField: r1 = r3->field_f
    //     0xa63710: ldur            w1, [x3, #0xf]
    // 0xa63714: DecompressPointer r1
    //     0xa63714: add             x1, x1, HEAP, lsl #32
    // 0xa63718: cmp             w1, w0
    // 0xa6371c: b.ne            #0xa63728
    // 0xa63720: r4 = Null
    //     0xa63720: mov             x4, NULL
    // 0xa63724: b               #0xa6372c
    // 0xa63728: mov             x4, x0
    // 0xa6372c: mov             x0, x4
    // 0xa63730: stur            x4, [fp, #-0x18]
    // 0xa63734: r2 = Null
    //     0xa63734: mov             x2, NULL
    // 0xa63738: r1 = Null
    //     0xa63738: mov             x1, NULL
    // 0xa6373c: r4 = 60
    //     0xa6373c: movz            x4, #0x3c
    // 0xa63740: branchIfSmi(r0, 0xa6374c)
    //     0xa63740: tbz             w0, #0, #0xa6374c
    // 0xa63744: r4 = LoadClassIdInstr(r0)
    //     0xa63744: ldur            x4, [x0, #-1]
    //     0xa63748: ubfx            x4, x4, #0xc, #0x14
    // 0xa6374c: r17 = 6836
    //     0xa6374c: movz            x17, #0x1ab4
    // 0xa63750: cmp             x4, x17
    // 0xa63754: b.eq            #0xa6376c
    // 0xa63758: r8 = NotificationSound
    //     0xa63758: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa6375c: ldr             x8, [x8, #0x288]
    // 0xa63760: r3 = Null
    //     0xa63760: add             x3, PP, #0x21, lsl #12  ; [pp+0x21070] Null
    //     0xa63764: ldr             x3, [x3, #0x70]
    // 0xa63768: r0 = NotificationSound()
    //     0xa63768: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa6376c: ldur            x1, [fp, #-0x38]
    // 0xa63770: r2 = 2
    //     0xa63770: movz            x2, #0x2
    // 0xa63774: r0 = _getValueOrData()
    //     0xa63774: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63778: ldur            x3, [fp, #-0x38]
    // 0xa6377c: LoadField: r1 = r3->field_f
    //     0xa6377c: ldur            w1, [x3, #0xf]
    // 0xa63780: DecompressPointer r1
    //     0xa63780: add             x1, x1, HEAP, lsl #32
    // 0xa63784: cmp             w1, w0
    // 0xa63788: b.ne            #0xa63794
    // 0xa6378c: r4 = Null
    //     0xa6378c: mov             x4, NULL
    // 0xa63790: b               #0xa63798
    // 0xa63794: mov             x4, x0
    // 0xa63798: mov             x0, x4
    // 0xa6379c: stur            x4, [fp, #-0x20]
    // 0xa637a0: r2 = Null
    //     0xa637a0: mov             x2, NULL
    // 0xa637a4: r1 = Null
    //     0xa637a4: mov             x1, NULL
    // 0xa637a8: r4 = 60
    //     0xa637a8: movz            x4, #0x3c
    // 0xa637ac: branchIfSmi(r0, 0xa637b8)
    //     0xa637ac: tbz             w0, #0, #0xa637b8
    // 0xa637b0: r4 = LoadClassIdInstr(r0)
    //     0xa637b0: ldur            x4, [x0, #-1]
    //     0xa637b4: ubfx            x4, x4, #0xc, #0x14
    // 0xa637b8: r17 = 6836
    //     0xa637b8: movz            x17, #0x1ab4
    // 0xa637bc: cmp             x4, x17
    // 0xa637c0: b.eq            #0xa637d8
    // 0xa637c4: r8 = NotificationSound
    //     0xa637c4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa637c8: ldr             x8, [x8, #0x288]
    // 0xa637cc: r3 = Null
    //     0xa637cc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21080] Null
    //     0xa637d0: ldr             x3, [x3, #0x80]
    // 0xa637d4: r0 = NotificationSound()
    //     0xa637d4: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa637d8: ldur            x1, [fp, #-0x38]
    // 0xa637dc: r2 = 4
    //     0xa637dc: movz            x2, #0x4
    // 0xa637e0: r0 = _getValueOrData()
    //     0xa637e0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa637e4: ldur            x3, [fp, #-0x38]
    // 0xa637e8: LoadField: r1 = r3->field_f
    //     0xa637e8: ldur            w1, [x3, #0xf]
    // 0xa637ec: DecompressPointer r1
    //     0xa637ec: add             x1, x1, HEAP, lsl #32
    // 0xa637f0: cmp             w1, w0
    // 0xa637f4: b.ne            #0xa63800
    // 0xa637f8: r4 = Null
    //     0xa637f8: mov             x4, NULL
    // 0xa637fc: b               #0xa63804
    // 0xa63800: mov             x4, x0
    // 0xa63804: mov             x0, x4
    // 0xa63808: stur            x4, [fp, #-0x40]
    // 0xa6380c: r2 = Null
    //     0xa6380c: mov             x2, NULL
    // 0xa63810: r1 = Null
    //     0xa63810: mov             x1, NULL
    // 0xa63814: r4 = 60
    //     0xa63814: movz            x4, #0x3c
    // 0xa63818: branchIfSmi(r0, 0xa63824)
    //     0xa63818: tbz             w0, #0, #0xa63824
    // 0xa6381c: r4 = LoadClassIdInstr(r0)
    //     0xa6381c: ldur            x4, [x0, #-1]
    //     0xa63820: ubfx            x4, x4, #0xc, #0x14
    // 0xa63824: r17 = 6836
    //     0xa63824: movz            x17, #0x1ab4
    // 0xa63828: cmp             x4, x17
    // 0xa6382c: b.eq            #0xa63844
    // 0xa63830: r8 = NotificationSound
    //     0xa63830: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63834: ldr             x8, [x8, #0x288]
    // 0xa63838: r3 = Null
    //     0xa63838: add             x3, PP, #0x21, lsl #12  ; [pp+0x21090] Null
    //     0xa6383c: ldr             x3, [x3, #0x90]
    // 0xa63840: r0 = NotificationSound()
    //     0xa63840: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63844: ldur            x1, [fp, #-0x38]
    // 0xa63848: r2 = 6
    //     0xa63848: movz            x2, #0x6
    // 0xa6384c: r0 = _getValueOrData()
    //     0xa6384c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63850: ldur            x3, [fp, #-0x38]
    // 0xa63854: LoadField: r1 = r3->field_f
    //     0xa63854: ldur            w1, [x3, #0xf]
    // 0xa63858: DecompressPointer r1
    //     0xa63858: add             x1, x1, HEAP, lsl #32
    // 0xa6385c: cmp             w1, w0
    // 0xa63860: b.ne            #0xa6386c
    // 0xa63864: r4 = Null
    //     0xa63864: mov             x4, NULL
    // 0xa63868: b               #0xa63870
    // 0xa6386c: mov             x4, x0
    // 0xa63870: mov             x0, x4
    // 0xa63874: stur            x4, [fp, #-0x48]
    // 0xa63878: r2 = Null
    //     0xa63878: mov             x2, NULL
    // 0xa6387c: r1 = Null
    //     0xa6387c: mov             x1, NULL
    // 0xa63880: r4 = 60
    //     0xa63880: movz            x4, #0x3c
    // 0xa63884: branchIfSmi(r0, 0xa63890)
    //     0xa63884: tbz             w0, #0, #0xa63890
    // 0xa63888: r4 = LoadClassIdInstr(r0)
    //     0xa63888: ldur            x4, [x0, #-1]
    //     0xa6388c: ubfx            x4, x4, #0xc, #0x14
    // 0xa63890: r17 = 6836
    //     0xa63890: movz            x17, #0x1ab4
    // 0xa63894: cmp             x4, x17
    // 0xa63898: b.eq            #0xa638b0
    // 0xa6389c: r8 = NotificationSound
    //     0xa6389c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa638a0: ldr             x8, [x8, #0x288]
    // 0xa638a4: r3 = Null
    //     0xa638a4: add             x3, PP, #0x21, lsl #12  ; [pp+0x210a0] Null
    //     0xa638a8: ldr             x3, [x3, #0xa0]
    // 0xa638ac: r0 = NotificationSound()
    //     0xa638ac: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa638b0: ldur            x1, [fp, #-0x38]
    // 0xa638b4: r2 = 8
    //     0xa638b4: movz            x2, #0x8
    // 0xa638b8: r0 = _getValueOrData()
    //     0xa638b8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa638bc: ldur            x3, [fp, #-0x38]
    // 0xa638c0: LoadField: r1 = r3->field_f
    //     0xa638c0: ldur            w1, [x3, #0xf]
    // 0xa638c4: DecompressPointer r1
    //     0xa638c4: add             x1, x1, HEAP, lsl #32
    // 0xa638c8: cmp             w1, w0
    // 0xa638cc: b.ne            #0xa638d8
    // 0xa638d0: r4 = Null
    //     0xa638d0: mov             x4, NULL
    // 0xa638d4: b               #0xa638dc
    // 0xa638d8: mov             x4, x0
    // 0xa638dc: mov             x0, x4
    // 0xa638e0: stur            x4, [fp, #-0x50]
    // 0xa638e4: r2 = Null
    //     0xa638e4: mov             x2, NULL
    // 0xa638e8: r1 = Null
    //     0xa638e8: mov             x1, NULL
    // 0xa638ec: r4 = 60
    //     0xa638ec: movz            x4, #0x3c
    // 0xa638f0: branchIfSmi(r0, 0xa638fc)
    //     0xa638f0: tbz             w0, #0, #0xa638fc
    // 0xa638f4: r4 = LoadClassIdInstr(r0)
    //     0xa638f4: ldur            x4, [x0, #-1]
    //     0xa638f8: ubfx            x4, x4, #0xc, #0x14
    // 0xa638fc: r17 = 6836
    //     0xa638fc: movz            x17, #0x1ab4
    // 0xa63900: cmp             x4, x17
    // 0xa63904: b.eq            #0xa6391c
    // 0xa63908: r8 = NotificationSound
    //     0xa63908: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa6390c: ldr             x8, [x8, #0x288]
    // 0xa63910: r3 = Null
    //     0xa63910: add             x3, PP, #0x21, lsl #12  ; [pp+0x210b0] Null
    //     0xa63914: ldr             x3, [x3, #0xb0]
    // 0xa63918: r0 = NotificationSound()
    //     0xa63918: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa6391c: ldur            x1, [fp, #-0x38]
    // 0xa63920: r2 = 10
    //     0xa63920: movz            x2, #0xa
    // 0xa63924: r0 = _getValueOrData()
    //     0xa63924: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63928: ldur            x3, [fp, #-0x38]
    // 0xa6392c: LoadField: r1 = r3->field_f
    //     0xa6392c: ldur            w1, [x3, #0xf]
    // 0xa63930: DecompressPointer r1
    //     0xa63930: add             x1, x1, HEAP, lsl #32
    // 0xa63934: cmp             w1, w0
    // 0xa63938: b.ne            #0xa63944
    // 0xa6393c: r4 = Null
    //     0xa6393c: mov             x4, NULL
    // 0xa63940: b               #0xa63948
    // 0xa63944: mov             x4, x0
    // 0xa63948: mov             x0, x4
    // 0xa6394c: stur            x4, [fp, #-0x58]
    // 0xa63950: r2 = Null
    //     0xa63950: mov             x2, NULL
    // 0xa63954: r1 = Null
    //     0xa63954: mov             x1, NULL
    // 0xa63958: r4 = 60
    //     0xa63958: movz            x4, #0x3c
    // 0xa6395c: branchIfSmi(r0, 0xa63968)
    //     0xa6395c: tbz             w0, #0, #0xa63968
    // 0xa63960: r4 = LoadClassIdInstr(r0)
    //     0xa63960: ldur            x4, [x0, #-1]
    //     0xa63964: ubfx            x4, x4, #0xc, #0x14
    // 0xa63968: r17 = 6836
    //     0xa63968: movz            x17, #0x1ab4
    // 0xa6396c: cmp             x4, x17
    // 0xa63970: b.eq            #0xa63988
    // 0xa63974: r8 = NotificationSound
    //     0xa63974: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63978: ldr             x8, [x8, #0x288]
    // 0xa6397c: r3 = Null
    //     0xa6397c: add             x3, PP, #0x21, lsl #12  ; [pp+0x210c0] Null
    //     0xa63980: ldr             x3, [x3, #0xc0]
    // 0xa63984: r0 = NotificationSound()
    //     0xa63984: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63988: ldur            x1, [fp, #-0x38]
    // 0xa6398c: r2 = 12
    //     0xa6398c: movz            x2, #0xc
    // 0xa63990: r0 = _getValueOrData()
    //     0xa63990: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63994: ldur            x3, [fp, #-0x38]
    // 0xa63998: LoadField: r1 = r3->field_f
    //     0xa63998: ldur            w1, [x3, #0xf]
    // 0xa6399c: DecompressPointer r1
    //     0xa6399c: add             x1, x1, HEAP, lsl #32
    // 0xa639a0: cmp             w1, w0
    // 0xa639a4: b.ne            #0xa639b0
    // 0xa639a8: r4 = Null
    //     0xa639a8: mov             x4, NULL
    // 0xa639ac: b               #0xa639b4
    // 0xa639b0: mov             x4, x0
    // 0xa639b4: mov             x0, x4
    // 0xa639b8: stur            x4, [fp, #-0x60]
    // 0xa639bc: r2 = Null
    //     0xa639bc: mov             x2, NULL
    // 0xa639c0: r1 = Null
    //     0xa639c0: mov             x1, NULL
    // 0xa639c4: r4 = 60
    //     0xa639c4: movz            x4, #0x3c
    // 0xa639c8: branchIfSmi(r0, 0xa639d4)
    //     0xa639c8: tbz             w0, #0, #0xa639d4
    // 0xa639cc: r4 = LoadClassIdInstr(r0)
    //     0xa639cc: ldur            x4, [x0, #-1]
    //     0xa639d0: ubfx            x4, x4, #0xc, #0x14
    // 0xa639d4: r17 = 6836
    //     0xa639d4: movz            x17, #0x1ab4
    // 0xa639d8: cmp             x4, x17
    // 0xa639dc: b.eq            #0xa639f4
    // 0xa639e0: r8 = NotificationSound
    //     0xa639e0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa639e4: ldr             x8, [x8, #0x288]
    // 0xa639e8: r3 = Null
    //     0xa639e8: add             x3, PP, #0x21, lsl #12  ; [pp+0x210d0] Null
    //     0xa639ec: ldr             x3, [x3, #0xd0]
    // 0xa639f0: r0 = NotificationSound()
    //     0xa639f0: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa639f4: ldur            x1, [fp, #-0x38]
    // 0xa639f8: r2 = 14
    //     0xa639f8: movz            x2, #0xe
    // 0xa639fc: r0 = _getValueOrData()
    //     0xa639fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63a00: ldur            x3, [fp, #-0x38]
    // 0xa63a04: LoadField: r1 = r3->field_f
    //     0xa63a04: ldur            w1, [x3, #0xf]
    // 0xa63a08: DecompressPointer r1
    //     0xa63a08: add             x1, x1, HEAP, lsl #32
    // 0xa63a0c: cmp             w1, w0
    // 0xa63a10: b.ne            #0xa63a1c
    // 0xa63a14: r4 = Null
    //     0xa63a14: mov             x4, NULL
    // 0xa63a18: b               #0xa63a20
    // 0xa63a1c: mov             x4, x0
    // 0xa63a20: mov             x0, x4
    // 0xa63a24: stur            x4, [fp, #-0x68]
    // 0xa63a28: r2 = Null
    //     0xa63a28: mov             x2, NULL
    // 0xa63a2c: r1 = Null
    //     0xa63a2c: mov             x1, NULL
    // 0xa63a30: r4 = 60
    //     0xa63a30: movz            x4, #0x3c
    // 0xa63a34: branchIfSmi(r0, 0xa63a40)
    //     0xa63a34: tbz             w0, #0, #0xa63a40
    // 0xa63a38: r4 = LoadClassIdInstr(r0)
    //     0xa63a38: ldur            x4, [x0, #-1]
    //     0xa63a3c: ubfx            x4, x4, #0xc, #0x14
    // 0xa63a40: r17 = 6836
    //     0xa63a40: movz            x17, #0x1ab4
    // 0xa63a44: cmp             x4, x17
    // 0xa63a48: b.eq            #0xa63a60
    // 0xa63a4c: r8 = NotificationSound
    //     0xa63a4c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63a50: ldr             x8, [x8, #0x288]
    // 0xa63a54: r3 = Null
    //     0xa63a54: add             x3, PP, #0x21, lsl #12  ; [pp+0x210e0] Null
    //     0xa63a58: ldr             x3, [x3, #0xe0]
    // 0xa63a5c: r0 = NotificationSound()
    //     0xa63a5c: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63a60: ldur            x1, [fp, #-0x38]
    // 0xa63a64: r2 = 16
    //     0xa63a64: movz            x2, #0x10
    // 0xa63a68: r0 = _getValueOrData()
    //     0xa63a68: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63a6c: ldur            x3, [fp, #-0x38]
    // 0xa63a70: LoadField: r1 = r3->field_f
    //     0xa63a70: ldur            w1, [x3, #0xf]
    // 0xa63a74: DecompressPointer r1
    //     0xa63a74: add             x1, x1, HEAP, lsl #32
    // 0xa63a78: cmp             w1, w0
    // 0xa63a7c: b.ne            #0xa63a88
    // 0xa63a80: r4 = Null
    //     0xa63a80: mov             x4, NULL
    // 0xa63a84: b               #0xa63a8c
    // 0xa63a88: mov             x4, x0
    // 0xa63a8c: mov             x0, x4
    // 0xa63a90: stur            x4, [fp, #-0x70]
    // 0xa63a94: r2 = Null
    //     0xa63a94: mov             x2, NULL
    // 0xa63a98: r1 = Null
    //     0xa63a98: mov             x1, NULL
    // 0xa63a9c: r4 = 60
    //     0xa63a9c: movz            x4, #0x3c
    // 0xa63aa0: branchIfSmi(r0, 0xa63aac)
    //     0xa63aa0: tbz             w0, #0, #0xa63aac
    // 0xa63aa4: r4 = LoadClassIdInstr(r0)
    //     0xa63aa4: ldur            x4, [x0, #-1]
    //     0xa63aa8: ubfx            x4, x4, #0xc, #0x14
    // 0xa63aac: r17 = 6836
    //     0xa63aac: movz            x17, #0x1ab4
    // 0xa63ab0: cmp             x4, x17
    // 0xa63ab4: b.eq            #0xa63acc
    // 0xa63ab8: r8 = NotificationSound
    //     0xa63ab8: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63abc: ldr             x8, [x8, #0x288]
    // 0xa63ac0: r3 = Null
    //     0xa63ac0: add             x3, PP, #0x21, lsl #12  ; [pp+0x210f0] Null
    //     0xa63ac4: ldr             x3, [x3, #0xf0]
    // 0xa63ac8: r0 = NotificationSound()
    //     0xa63ac8: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63acc: ldur            x1, [fp, #-0x38]
    // 0xa63ad0: r2 = 18
    //     0xa63ad0: movz            x2, #0x12
    // 0xa63ad4: r0 = _getValueOrData()
    //     0xa63ad4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63ad8: ldur            x3, [fp, #-0x38]
    // 0xa63adc: LoadField: r1 = r3->field_f
    //     0xa63adc: ldur            w1, [x3, #0xf]
    // 0xa63ae0: DecompressPointer r1
    //     0xa63ae0: add             x1, x1, HEAP, lsl #32
    // 0xa63ae4: cmp             w1, w0
    // 0xa63ae8: b.ne            #0xa63af4
    // 0xa63aec: r4 = Null
    //     0xa63aec: mov             x4, NULL
    // 0xa63af0: b               #0xa63af8
    // 0xa63af4: mov             x4, x0
    // 0xa63af8: mov             x0, x4
    // 0xa63afc: stur            x4, [fp, #-0x78]
    // 0xa63b00: r2 = Null
    //     0xa63b00: mov             x2, NULL
    // 0xa63b04: r1 = Null
    //     0xa63b04: mov             x1, NULL
    // 0xa63b08: r4 = 60
    //     0xa63b08: movz            x4, #0x3c
    // 0xa63b0c: branchIfSmi(r0, 0xa63b18)
    //     0xa63b0c: tbz             w0, #0, #0xa63b18
    // 0xa63b10: r4 = LoadClassIdInstr(r0)
    //     0xa63b10: ldur            x4, [x0, #-1]
    //     0xa63b14: ubfx            x4, x4, #0xc, #0x14
    // 0xa63b18: r17 = 6836
    //     0xa63b18: movz            x17, #0x1ab4
    // 0xa63b1c: cmp             x4, x17
    // 0xa63b20: b.eq            #0xa63b38
    // 0xa63b24: r8 = NotificationSound
    //     0xa63b24: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63b28: ldr             x8, [x8, #0x288]
    // 0xa63b2c: r3 = Null
    //     0xa63b2c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21100] Null
    //     0xa63b30: ldr             x3, [x3, #0x100]
    // 0xa63b34: r0 = NotificationSound()
    //     0xa63b34: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63b38: ldur            x1, [fp, #-0x38]
    // 0xa63b3c: r2 = 20
    //     0xa63b3c: movz            x2, #0x14
    // 0xa63b40: r0 = _getValueOrData()
    //     0xa63b40: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63b44: ldur            x3, [fp, #-0x38]
    // 0xa63b48: LoadField: r1 = r3->field_f
    //     0xa63b48: ldur            w1, [x3, #0xf]
    // 0xa63b4c: DecompressPointer r1
    //     0xa63b4c: add             x1, x1, HEAP, lsl #32
    // 0xa63b50: cmp             w1, w0
    // 0xa63b54: b.ne            #0xa63b60
    // 0xa63b58: r4 = Null
    //     0xa63b58: mov             x4, NULL
    // 0xa63b5c: b               #0xa63b64
    // 0xa63b60: mov             x4, x0
    // 0xa63b64: mov             x0, x4
    // 0xa63b68: stur            x4, [fp, #-0x80]
    // 0xa63b6c: r2 = Null
    //     0xa63b6c: mov             x2, NULL
    // 0xa63b70: r1 = Null
    //     0xa63b70: mov             x1, NULL
    // 0xa63b74: r4 = 60
    //     0xa63b74: movz            x4, #0x3c
    // 0xa63b78: branchIfSmi(r0, 0xa63b84)
    //     0xa63b78: tbz             w0, #0, #0xa63b84
    // 0xa63b7c: r4 = LoadClassIdInstr(r0)
    //     0xa63b7c: ldur            x4, [x0, #-1]
    //     0xa63b80: ubfx            x4, x4, #0xc, #0x14
    // 0xa63b84: r17 = 6836
    //     0xa63b84: movz            x17, #0x1ab4
    // 0xa63b88: cmp             x4, x17
    // 0xa63b8c: b.eq            #0xa63ba4
    // 0xa63b90: r8 = NotificationSound
    //     0xa63b90: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63b94: ldr             x8, [x8, #0x288]
    // 0xa63b98: r3 = Null
    //     0xa63b98: add             x3, PP, #0x21, lsl #12  ; [pp+0x21110] Null
    //     0xa63b9c: ldr             x3, [x3, #0x110]
    // 0xa63ba0: r0 = NotificationSound()
    //     0xa63ba0: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63ba4: ldur            x1, [fp, #-0x38]
    // 0xa63ba8: r2 = 22
    //     0xa63ba8: movz            x2, #0x16
    // 0xa63bac: r0 = _getValueOrData()
    //     0xa63bac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63bb0: ldur            x3, [fp, #-0x38]
    // 0xa63bb4: LoadField: r1 = r3->field_f
    //     0xa63bb4: ldur            w1, [x3, #0xf]
    // 0xa63bb8: DecompressPointer r1
    //     0xa63bb8: add             x1, x1, HEAP, lsl #32
    // 0xa63bbc: cmp             w1, w0
    // 0xa63bc0: b.ne            #0xa63bcc
    // 0xa63bc4: r4 = Null
    //     0xa63bc4: mov             x4, NULL
    // 0xa63bc8: b               #0xa63bd0
    // 0xa63bcc: mov             x4, x0
    // 0xa63bd0: mov             x0, x4
    // 0xa63bd4: stur            x4, [fp, #-0x88]
    // 0xa63bd8: r2 = Null
    //     0xa63bd8: mov             x2, NULL
    // 0xa63bdc: r1 = Null
    //     0xa63bdc: mov             x1, NULL
    // 0xa63be0: r4 = 60
    //     0xa63be0: movz            x4, #0x3c
    // 0xa63be4: branchIfSmi(r0, 0xa63bf0)
    //     0xa63be4: tbz             w0, #0, #0xa63bf0
    // 0xa63be8: r4 = LoadClassIdInstr(r0)
    //     0xa63be8: ldur            x4, [x0, #-1]
    //     0xa63bec: ubfx            x4, x4, #0xc, #0x14
    // 0xa63bf0: r17 = 6836
    //     0xa63bf0: movz            x17, #0x1ab4
    // 0xa63bf4: cmp             x4, x17
    // 0xa63bf8: b.eq            #0xa63c10
    // 0xa63bfc: r8 = NotificationSound
    //     0xa63bfc: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63c00: ldr             x8, [x8, #0x288]
    // 0xa63c04: r3 = Null
    //     0xa63c04: add             x3, PP, #0x21, lsl #12  ; [pp+0x21120] Null
    //     0xa63c08: ldr             x3, [x3, #0x120]
    // 0xa63c0c: r0 = NotificationSound()
    //     0xa63c0c: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63c10: ldur            x1, [fp, #-0x38]
    // 0xa63c14: r2 = 24
    //     0xa63c14: movz            x2, #0x18
    // 0xa63c18: r0 = _getValueOrData()
    //     0xa63c18: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63c1c: ldur            x3, [fp, #-0x38]
    // 0xa63c20: LoadField: r1 = r3->field_f
    //     0xa63c20: ldur            w1, [x3, #0xf]
    // 0xa63c24: DecompressPointer r1
    //     0xa63c24: add             x1, x1, HEAP, lsl #32
    // 0xa63c28: cmp             w1, w0
    // 0xa63c2c: b.ne            #0xa63c38
    // 0xa63c30: r4 = Null
    //     0xa63c30: mov             x4, NULL
    // 0xa63c34: b               #0xa63c3c
    // 0xa63c38: mov             x4, x0
    // 0xa63c3c: mov             x0, x4
    // 0xa63c40: stur            x4, [fp, #-0x90]
    // 0xa63c44: r2 = Null
    //     0xa63c44: mov             x2, NULL
    // 0xa63c48: r1 = Null
    //     0xa63c48: mov             x1, NULL
    // 0xa63c4c: r4 = 60
    //     0xa63c4c: movz            x4, #0x3c
    // 0xa63c50: branchIfSmi(r0, 0xa63c5c)
    //     0xa63c50: tbz             w0, #0, #0xa63c5c
    // 0xa63c54: r4 = LoadClassIdInstr(r0)
    //     0xa63c54: ldur            x4, [x0, #-1]
    //     0xa63c58: ubfx            x4, x4, #0xc, #0x14
    // 0xa63c5c: r17 = 6836
    //     0xa63c5c: movz            x17, #0x1ab4
    // 0xa63c60: cmp             x4, x17
    // 0xa63c64: b.eq            #0xa63c7c
    // 0xa63c68: r8 = NotificationSound
    //     0xa63c68: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63c6c: ldr             x8, [x8, #0x288]
    // 0xa63c70: r3 = Null
    //     0xa63c70: add             x3, PP, #0x21, lsl #12  ; [pp+0x21130] Null
    //     0xa63c74: ldr             x3, [x3, #0x130]
    // 0xa63c78: r0 = NotificationSound()
    //     0xa63c78: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63c7c: ldur            x1, [fp, #-0x38]
    // 0xa63c80: r2 = 26
    //     0xa63c80: movz            x2, #0x1a
    // 0xa63c84: r0 = _getValueOrData()
    //     0xa63c84: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63c88: ldur            x3, [fp, #-0x38]
    // 0xa63c8c: LoadField: r1 = r3->field_f
    //     0xa63c8c: ldur            w1, [x3, #0xf]
    // 0xa63c90: DecompressPointer r1
    //     0xa63c90: add             x1, x1, HEAP, lsl #32
    // 0xa63c94: cmp             w1, w0
    // 0xa63c98: b.ne            #0xa63ca4
    // 0xa63c9c: r4 = Null
    //     0xa63c9c: mov             x4, NULL
    // 0xa63ca0: b               #0xa63ca8
    // 0xa63ca4: mov             x4, x0
    // 0xa63ca8: mov             x0, x4
    // 0xa63cac: stur            x4, [fp, #-0x98]
    // 0xa63cb0: r2 = Null
    //     0xa63cb0: mov             x2, NULL
    // 0xa63cb4: r1 = Null
    //     0xa63cb4: mov             x1, NULL
    // 0xa63cb8: r4 = 60
    //     0xa63cb8: movz            x4, #0x3c
    // 0xa63cbc: branchIfSmi(r0, 0xa63cc8)
    //     0xa63cbc: tbz             w0, #0, #0xa63cc8
    // 0xa63cc0: r4 = LoadClassIdInstr(r0)
    //     0xa63cc0: ldur            x4, [x0, #-1]
    //     0xa63cc4: ubfx            x4, x4, #0xc, #0x14
    // 0xa63cc8: r17 = 6836
    //     0xa63cc8: movz            x17, #0x1ab4
    // 0xa63ccc: cmp             x4, x17
    // 0xa63cd0: b.eq            #0xa63ce8
    // 0xa63cd4: r8 = NotificationSound
    //     0xa63cd4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b288] Type: NotificationSound
    //     0xa63cd8: ldr             x8, [x8, #0x288]
    // 0xa63cdc: r3 = Null
    //     0xa63cdc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21140] Null
    //     0xa63ce0: ldr             x3, [x3, #0x140]
    // 0xa63ce4: r0 = NotificationSound()
    //     0xa63ce4: bl              #0x81bdb0  ; IsType_NotificationSound_Stub
    // 0xa63ce8: ldur            x1, [fp, #-0x38]
    // 0xa63cec: r2 = 28
    //     0xa63cec: movz            x2, #0x1c
    // 0xa63cf0: r0 = _getValueOrData()
    //     0xa63cf0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63cf4: ldur            x3, [fp, #-0x38]
    // 0xa63cf8: LoadField: r1 = r3->field_f
    //     0xa63cf8: ldur            w1, [x3, #0xf]
    // 0xa63cfc: DecompressPointer r1
    //     0xa63cfc: add             x1, x1, HEAP, lsl #32
    // 0xa63d00: cmp             w1, w0
    // 0xa63d04: b.ne            #0xa63d10
    // 0xa63d08: r4 = Null
    //     0xa63d08: mov             x4, NULL
    // 0xa63d0c: b               #0xa63d14
    // 0xa63d10: mov             x4, x0
    // 0xa63d14: mov             x0, x4
    // 0xa63d18: stur            x4, [fp, #-0xa0]
    // 0xa63d1c: r2 = Null
    //     0xa63d1c: mov             x2, NULL
    // 0xa63d20: r1 = Null
    //     0xa63d20: mov             x1, NULL
    // 0xa63d24: branchIfSmi(r0, 0xa63d4c)
    //     0xa63d24: tbz             w0, #0, #0xa63d4c
    // 0xa63d28: r4 = LoadClassIdInstr(r0)
    //     0xa63d28: ldur            x4, [x0, #-1]
    //     0xa63d2c: ubfx            x4, x4, #0xc, #0x14
    // 0xa63d30: sub             x4, x4, #0x3c
    // 0xa63d34: cmp             x4, #1
    // 0xa63d38: b.ls            #0xa63d4c
    // 0xa63d3c: r8 = int
    //     0xa63d3c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63d40: r3 = Null
    //     0xa63d40: add             x3, PP, #0x21, lsl #12  ; [pp+0x21150] Null
    //     0xa63d44: ldr             x3, [x3, #0x150]
    // 0xa63d48: r0 = int()
    //     0xa63d48: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63d4c: ldur            x1, [fp, #-0x38]
    // 0xa63d50: r2 = 30
    //     0xa63d50: movz            x2, #0x1e
    // 0xa63d54: r0 = _getValueOrData()
    //     0xa63d54: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63d58: ldur            x3, [fp, #-0x38]
    // 0xa63d5c: LoadField: r1 = r3->field_f
    //     0xa63d5c: ldur            w1, [x3, #0xf]
    // 0xa63d60: DecompressPointer r1
    //     0xa63d60: add             x1, x1, HEAP, lsl #32
    // 0xa63d64: cmp             w1, w0
    // 0xa63d68: b.ne            #0xa63d74
    // 0xa63d6c: r4 = Null
    //     0xa63d6c: mov             x4, NULL
    // 0xa63d70: b               #0xa63d78
    // 0xa63d74: mov             x4, x0
    // 0xa63d78: mov             x0, x4
    // 0xa63d7c: stur            x4, [fp, #-0xa8]
    // 0xa63d80: r2 = Null
    //     0xa63d80: mov             x2, NULL
    // 0xa63d84: r1 = Null
    //     0xa63d84: mov             x1, NULL
    // 0xa63d88: branchIfSmi(r0, 0xa63db0)
    //     0xa63d88: tbz             w0, #0, #0xa63db0
    // 0xa63d8c: r4 = LoadClassIdInstr(r0)
    //     0xa63d8c: ldur            x4, [x0, #-1]
    //     0xa63d90: ubfx            x4, x4, #0xc, #0x14
    // 0xa63d94: sub             x4, x4, #0x3c
    // 0xa63d98: cmp             x4, #1
    // 0xa63d9c: b.ls            #0xa63db0
    // 0xa63da0: r8 = int
    //     0xa63da0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63da4: r3 = Null
    //     0xa63da4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21160] Null
    //     0xa63da8: ldr             x3, [x3, #0x160]
    // 0xa63dac: r0 = int()
    //     0xa63dac: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63db0: ldur            x1, [fp, #-0x38]
    // 0xa63db4: r2 = 32
    //     0xa63db4: movz            x2, #0x20
    // 0xa63db8: r0 = _getValueOrData()
    //     0xa63db8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63dbc: ldur            x3, [fp, #-0x38]
    // 0xa63dc0: LoadField: r1 = r3->field_f
    //     0xa63dc0: ldur            w1, [x3, #0xf]
    // 0xa63dc4: DecompressPointer r1
    //     0xa63dc4: add             x1, x1, HEAP, lsl #32
    // 0xa63dc8: cmp             w1, w0
    // 0xa63dcc: b.ne            #0xa63dd8
    // 0xa63dd0: r4 = Null
    //     0xa63dd0: mov             x4, NULL
    // 0xa63dd4: b               #0xa63ddc
    // 0xa63dd8: mov             x4, x0
    // 0xa63ddc: mov             x0, x4
    // 0xa63de0: stur            x4, [fp, #-0xb0]
    // 0xa63de4: r2 = Null
    //     0xa63de4: mov             x2, NULL
    // 0xa63de8: r1 = Null
    //     0xa63de8: mov             x1, NULL
    // 0xa63dec: branchIfSmi(r0, 0xa63e14)
    //     0xa63dec: tbz             w0, #0, #0xa63e14
    // 0xa63df0: r4 = LoadClassIdInstr(r0)
    //     0xa63df0: ldur            x4, [x0, #-1]
    //     0xa63df4: ubfx            x4, x4, #0xc, #0x14
    // 0xa63df8: sub             x4, x4, #0x3c
    // 0xa63dfc: cmp             x4, #1
    // 0xa63e00: b.ls            #0xa63e14
    // 0xa63e04: r8 = int
    //     0xa63e04: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63e08: r3 = Null
    //     0xa63e08: add             x3, PP, #0x21, lsl #12  ; [pp+0x21170] Null
    //     0xa63e0c: ldr             x3, [x3, #0x170]
    // 0xa63e10: r0 = int()
    //     0xa63e10: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63e14: ldur            x1, [fp, #-0x38]
    // 0xa63e18: r2 = 34
    //     0xa63e18: movz            x2, #0x22
    // 0xa63e1c: r0 = _getValueOrData()
    //     0xa63e1c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63e20: ldur            x3, [fp, #-0x38]
    // 0xa63e24: LoadField: r1 = r3->field_f
    //     0xa63e24: ldur            w1, [x3, #0xf]
    // 0xa63e28: DecompressPointer r1
    //     0xa63e28: add             x1, x1, HEAP, lsl #32
    // 0xa63e2c: cmp             w1, w0
    // 0xa63e30: b.ne            #0xa63e3c
    // 0xa63e34: r4 = Null
    //     0xa63e34: mov             x4, NULL
    // 0xa63e38: b               #0xa63e40
    // 0xa63e3c: mov             x4, x0
    // 0xa63e40: mov             x0, x4
    // 0xa63e44: stur            x4, [fp, #-0xb8]
    // 0xa63e48: r2 = Null
    //     0xa63e48: mov             x2, NULL
    // 0xa63e4c: r1 = Null
    //     0xa63e4c: mov             x1, NULL
    // 0xa63e50: branchIfSmi(r0, 0xa63e78)
    //     0xa63e50: tbz             w0, #0, #0xa63e78
    // 0xa63e54: r4 = LoadClassIdInstr(r0)
    //     0xa63e54: ldur            x4, [x0, #-1]
    //     0xa63e58: ubfx            x4, x4, #0xc, #0x14
    // 0xa63e5c: sub             x4, x4, #0x3c
    // 0xa63e60: cmp             x4, #1
    // 0xa63e64: b.ls            #0xa63e78
    // 0xa63e68: r8 = int
    //     0xa63e68: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63e6c: r3 = Null
    //     0xa63e6c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21180] Null
    //     0xa63e70: ldr             x3, [x3, #0x180]
    // 0xa63e74: r0 = int()
    //     0xa63e74: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63e78: ldur            x1, [fp, #-0x38]
    // 0xa63e7c: r2 = 36
    //     0xa63e7c: movz            x2, #0x24
    // 0xa63e80: r0 = _getValueOrData()
    //     0xa63e80: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63e84: ldur            x3, [fp, #-0x38]
    // 0xa63e88: LoadField: r1 = r3->field_f
    //     0xa63e88: ldur            w1, [x3, #0xf]
    // 0xa63e8c: DecompressPointer r1
    //     0xa63e8c: add             x1, x1, HEAP, lsl #32
    // 0xa63e90: cmp             w1, w0
    // 0xa63e94: b.ne            #0xa63ea0
    // 0xa63e98: r4 = Null
    //     0xa63e98: mov             x4, NULL
    // 0xa63e9c: b               #0xa63ea4
    // 0xa63ea0: mov             x4, x0
    // 0xa63ea4: mov             x0, x4
    // 0xa63ea8: stur            x4, [fp, #-0xc0]
    // 0xa63eac: r2 = Null
    //     0xa63eac: mov             x2, NULL
    // 0xa63eb0: r1 = Null
    //     0xa63eb0: mov             x1, NULL
    // 0xa63eb4: branchIfSmi(r0, 0xa63edc)
    //     0xa63eb4: tbz             w0, #0, #0xa63edc
    // 0xa63eb8: r4 = LoadClassIdInstr(r0)
    //     0xa63eb8: ldur            x4, [x0, #-1]
    //     0xa63ebc: ubfx            x4, x4, #0xc, #0x14
    // 0xa63ec0: sub             x4, x4, #0x3c
    // 0xa63ec4: cmp             x4, #1
    // 0xa63ec8: b.ls            #0xa63edc
    // 0xa63ecc: r8 = int
    //     0xa63ecc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63ed0: r3 = Null
    //     0xa63ed0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21190] Null
    //     0xa63ed4: ldr             x3, [x3, #0x190]
    // 0xa63ed8: r0 = int()
    //     0xa63ed8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63edc: ldur            x1, [fp, #-0x38]
    // 0xa63ee0: r2 = 38
    //     0xa63ee0: movz            x2, #0x26
    // 0xa63ee4: r0 = _getValueOrData()
    //     0xa63ee4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63ee8: ldur            x3, [fp, #-0x38]
    // 0xa63eec: LoadField: r1 = r3->field_f
    //     0xa63eec: ldur            w1, [x3, #0xf]
    // 0xa63ef0: DecompressPointer r1
    //     0xa63ef0: add             x1, x1, HEAP, lsl #32
    // 0xa63ef4: cmp             w1, w0
    // 0xa63ef8: b.ne            #0xa63f04
    // 0xa63efc: r4 = Null
    //     0xa63efc: mov             x4, NULL
    // 0xa63f00: b               #0xa63f08
    // 0xa63f04: mov             x4, x0
    // 0xa63f08: mov             x0, x4
    // 0xa63f0c: stur            x4, [fp, #-0xc8]
    // 0xa63f10: r2 = Null
    //     0xa63f10: mov             x2, NULL
    // 0xa63f14: r1 = Null
    //     0xa63f14: mov             x1, NULL
    // 0xa63f18: branchIfSmi(r0, 0xa63f40)
    //     0xa63f18: tbz             w0, #0, #0xa63f40
    // 0xa63f1c: r4 = LoadClassIdInstr(r0)
    //     0xa63f1c: ldur            x4, [x0, #-1]
    //     0xa63f20: ubfx            x4, x4, #0xc, #0x14
    // 0xa63f24: sub             x4, x4, #0x3c
    // 0xa63f28: cmp             x4, #1
    // 0xa63f2c: b.ls            #0xa63f40
    // 0xa63f30: r8 = int
    //     0xa63f30: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa63f34: r3 = Null
    //     0xa63f34: add             x3, PP, #0x21, lsl #12  ; [pp+0x211a0] Null
    //     0xa63f38: ldr             x3, [x3, #0x1a0]
    // 0xa63f3c: r0 = int()
    //     0xa63f3c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa63f40: ldur            x1, [fp, #-0x38]
    // 0xa63f44: r2 = 40
    //     0xa63f44: movz            x2, #0x28
    // 0xa63f48: r0 = _getValueOrData()
    //     0xa63f48: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa63f4c: mov             x1, x0
    // 0xa63f50: ldur            x0, [fp, #-0x38]
    // 0xa63f54: LoadField: r2 = r0->field_f
    //     0xa63f54: ldur            w2, [x0, #0xf]
    // 0xa63f58: DecompressPointer r2
    //     0xa63f58: add             x2, x2, HEAP, lsl #32
    // 0xa63f5c: cmp             w2, w1
    // 0xa63f60: b.ne            #0xa63f74
    // 0xa63f64: SaveReg r0
    //     0xa63f64: str             x0, [SP, #-8]!
    // 0xa63f68: stur            NULL, [fp, #-0x38]
    // 0xa63f6c: RestoreReg r0
    //     0xa63f6c: ldr             x0, [SP], #8
    // 0xa63f70: b               #0xa63f78
    // 0xa63f74: stur            x1, [fp, #-0x38]
    // 0xa63f78: ldur            x2, [fp, #-0x18]
    // 0xa63f7c: ldur            x1, [fp, #-0x20]
    // 0xa63f80: ldur            x0, [fp, #-0x40]
    // 0xa63f84: ldur            x25, [fp, #-0x48]
    // 0xa63f88: ldur            x24, [fp, #-0x50]
    // 0xa63f8c: ldur            x23, [fp, #-0x58]
    // 0xa63f90: ldur            x20, [fp, #-0x60]
    // 0xa63f94: ldur            x19, [fp, #-0x68]
    // 0xa63f98: ldur            x14, [fp, #-0x70]
    // 0xa63f9c: ldur            x13, [fp, #-0x78]
    // 0xa63fa0: ldur            x12, [fp, #-0x80]
    // 0xa63fa4: ldur            x11, [fp, #-0x88]
    // 0xa63fa8: ldur            x10, [fp, #-0x90]
    // 0xa63fac: ldur            x9, [fp, #-0x98]
    // 0xa63fb0: ldur            x8, [fp, #-0xa0]
    // 0xa63fb4: ldur            x7, [fp, #-0xa8]
    // 0xa63fb8: ldur            x6, [fp, #-0xb0]
    // 0xa63fbc: ldur            x5, [fp, #-0xb8]
    // 0xa63fc0: ldur            x4, [fp, #-0xc0]
    // 0xa63fc4: ldur            x3, [fp, #-0xc8]
    // 0xa63fc8: ldur            x0, [fp, #-0x38]
    // 0xa63fcc: r2 = Null
    //     0xa63fcc: mov             x2, NULL
    // 0xa63fd0: r1 = Null
    //     0xa63fd0: mov             x1, NULL
    // 0xa63fd4: r4 = 60
    //     0xa63fd4: movz            x4, #0x3c
    // 0xa63fd8: branchIfSmi(r0, 0xa63fe4)
    //     0xa63fd8: tbz             w0, #0, #0xa63fe4
    // 0xa63fdc: r4 = LoadClassIdInstr(r0)
    //     0xa63fdc: ldur            x4, [x0, #-1]
    //     0xa63fe0: ubfx            x4, x4, #0xc, #0x14
    // 0xa63fe4: cmp             x4, #0x3f
    // 0xa63fe8: b.eq            #0xa63ffc
    // 0xa63fec: r8 = bool?
    //     0xa63fec: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xa63ff0: r3 = Null
    //     0xa63ff0: add             x3, PP, #0x21, lsl #12  ; [pp+0x211b0] Null
    //     0xa63ff4: ldr             x3, [x3, #0x1b0]
    // 0xa63ff8: r0 = bool?()
    //     0xa63ff8: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xa63ffc: r0 = PrayerTimeNotificationSetting()
    //     0xa63ffc: bl              #0x831100  ; AllocatePrayerTimeNotificationSettingStub -> PrayerTimeNotificationSetting (size=0x80)
    // 0xa64000: mov             x1, x0
    // 0xa64004: ldur            x0, [fp, #-0x18]
    // 0xa64008: stur            x1, [fp, #-0xd0]
    // 0xa6400c: StoreField: r1->field_13 = r0
    //     0xa6400c: stur            w0, [x1, #0x13]
    // 0xa64010: ldur            x0, [fp, #-0x20]
    // 0xa64014: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64014: stur            w0, [x1, #0x17]
    // 0xa64018: ldur            x0, [fp, #-0x40]
    // 0xa6401c: StoreField: r1->field_1b = r0
    //     0xa6401c: stur            w0, [x1, #0x1b]
    // 0xa64020: ldur            x0, [fp, #-0x48]
    // 0xa64024: StoreField: r1->field_1f = r0
    //     0xa64024: stur            w0, [x1, #0x1f]
    // 0xa64028: ldur            x0, [fp, #-0x50]
    // 0xa6402c: StoreField: r1->field_23 = r0
    //     0xa6402c: stur            w0, [x1, #0x23]
    // 0xa64030: ldur            x0, [fp, #-0x58]
    // 0xa64034: StoreField: r1->field_27 = r0
    //     0xa64034: stur            w0, [x1, #0x27]
    // 0xa64038: ldur            x0, [fp, #-0x60]
    // 0xa6403c: StoreField: r1->field_2b = r0
    //     0xa6403c: stur            w0, [x1, #0x2b]
    // 0xa64040: ldur            x0, [fp, #-0x68]
    // 0xa64044: StoreField: r1->field_2f = r0
    //     0xa64044: stur            w0, [x1, #0x2f]
    // 0xa64048: ldur            x0, [fp, #-0x70]
    // 0xa6404c: StoreField: r1->field_33 = r0
    //     0xa6404c: stur            w0, [x1, #0x33]
    // 0xa64050: ldur            x0, [fp, #-0x78]
    // 0xa64054: StoreField: r1->field_37 = r0
    //     0xa64054: stur            w0, [x1, #0x37]
    // 0xa64058: ldur            x0, [fp, #-0x80]
    // 0xa6405c: StoreField: r1->field_3b = r0
    //     0xa6405c: stur            w0, [x1, #0x3b]
    // 0xa64060: ldur            x0, [fp, #-0x88]
    // 0xa64064: StoreField: r1->field_3f = r0
    //     0xa64064: stur            w0, [x1, #0x3f]
    // 0xa64068: ldur            x0, [fp, #-0x90]
    // 0xa6406c: StoreField: r1->field_43 = r0
    //     0xa6406c: stur            w0, [x1, #0x43]
    // 0xa64070: ldur            x0, [fp, #-0x98]
    // 0xa64074: StoreField: r1->field_47 = r0
    //     0xa64074: stur            w0, [x1, #0x47]
    // 0xa64078: ldur            x0, [fp, #-0xa0]
    // 0xa6407c: r2 = LoadInt32Instr(r0)
    //     0xa6407c: sbfx            x2, x0, #1, #0x1f
    //     0xa64080: tbz             w0, #0, #0xa64088
    //     0xa64084: ldur            x2, [x0, #7]
    // 0xa64088: StoreField: r1->field_4b = r2
    //     0xa64088: stur            x2, [x1, #0x4b]
    // 0xa6408c: ldur            x0, [fp, #-0xa8]
    // 0xa64090: r2 = LoadInt32Instr(r0)
    //     0xa64090: sbfx            x2, x0, #1, #0x1f
    //     0xa64094: tbz             w0, #0, #0xa6409c
    //     0xa64098: ldur            x2, [x0, #7]
    // 0xa6409c: StoreField: r1->field_53 = r2
    //     0xa6409c: stur            x2, [x1, #0x53]
    // 0xa640a0: ldur            x0, [fp, #-0xb0]
    // 0xa640a4: r2 = LoadInt32Instr(r0)
    //     0xa640a4: sbfx            x2, x0, #1, #0x1f
    //     0xa640a8: tbz             w0, #0, #0xa640b0
    //     0xa640ac: ldur            x2, [x0, #7]
    // 0xa640b0: StoreField: r1->field_5b = r2
    //     0xa640b0: stur            x2, [x1, #0x5b]
    // 0xa640b4: ldur            x0, [fp, #-0xb8]
    // 0xa640b8: r2 = LoadInt32Instr(r0)
    //     0xa640b8: sbfx            x2, x0, #1, #0x1f
    //     0xa640bc: tbz             w0, #0, #0xa640c4
    //     0xa640c0: ldur            x2, [x0, #7]
    // 0xa640c4: StoreField: r1->field_63 = r2
    //     0xa640c4: stur            x2, [x1, #0x63]
    // 0xa640c8: ldur            x0, [fp, #-0xc0]
    // 0xa640cc: r2 = LoadInt32Instr(r0)
    //     0xa640cc: sbfx            x2, x0, #1, #0x1f
    //     0xa640d0: tbz             w0, #0, #0xa640d8
    //     0xa640d4: ldur            x2, [x0, #7]
    // 0xa640d8: StoreField: r1->field_6b = r2
    //     0xa640d8: stur            x2, [x1, #0x6b]
    // 0xa640dc: ldur            x0, [fp, #-0xc8]
    // 0xa640e0: r2 = LoadInt32Instr(r0)
    //     0xa640e0: sbfx            x2, x0, #1, #0x1f
    //     0xa640e4: tbz             w0, #0, #0xa640ec
    //     0xa640e8: ldur            x2, [x0, #7]
    // 0xa640ec: StoreField: r1->field_73 = r2
    //     0xa640ec: stur            x2, [x1, #0x73]
    // 0xa640f0: ldur            x0, [fp, #-0x38]
    // 0xa640f4: StoreField: r1->field_7b = r0
    //     0xa640f4: stur            w0, [x1, #0x7b]
    // 0xa640f8: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa640f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa640fc: ldr             x16, [x16, #0x9f8]
    // 0xa64100: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64104: stp             lr, x16, [SP]
    // 0xa64108: r0 = Map._fromLiteral()
    //     0xa64108: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa6410c: ldur            x1, [fp, #-0xd0]
    // 0xa64110: StoreField: r1->field_f = r0
    //     0xa64110: stur            w0, [x1, #0xf]
    //     0xa64114: ldurb           w16, [x1, #-1]
    //     0xa64118: ldurb           w17, [x0, #-1]
    //     0xa6411c: and             x16, x17, x16, lsr #2
    //     0xa64120: tst             x16, HEAP, lsr #32
    //     0xa64124: b.eq            #0xa6412c
    //     0xa64128: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa6412c: mov             x0, x1
    // 0xa64130: LeaveFrame
    //     0xa64130: mov             SP, fp
    //     0xa64134: ldp             fp, lr, [SP], #0x10
    // 0xa64138: ret
    //     0xa64138: ret             
    // 0xa6413c: r0 = RangeError()
    //     0xa6413c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64140: mov             x1, x0
    // 0xa64144: r0 = "Not enough bytes available."
    //     0xa64144: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64148: ldr             x0, [x0, #0x8a8]
    // 0xa6414c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa6414c: stur            w0, [x1, #0x17]
    // 0xa64150: r2 = false
    //     0xa64150: add             x2, NULL, #0x30  ; false
    // 0xa64154: StoreField: r1->field_b = r2
    //     0xa64154: stur            w2, [x1, #0xb]
    // 0xa64158: mov             x0, x1
    // 0xa6415c: r0 = Throw()
    //     0xa6415c: bl              #0xec04b8  ; ThrowStub
    // 0xa64160: brk             #0
    // 0xa64164: r0 = "Not enough bytes available."
    //     0xa64164: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64168: ldr             x0, [x0, #0x8a8]
    // 0xa6416c: r2 = false
    //     0xa6416c: add             x2, NULL, #0x30  ; false
    // 0xa64170: r0 = RangeError()
    //     0xa64170: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64174: mov             x1, x0
    // 0xa64178: r0 = "Not enough bytes available."
    //     0xa64178: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6417c: ldr             x0, [x0, #0x8a8]
    // 0xa64180: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64180: stur            w0, [x1, #0x17]
    // 0xa64184: r0 = false
    //     0xa64184: add             x0, NULL, #0x30  ; false
    // 0xa64188: StoreField: r1->field_b = r0
    //     0xa64188: stur            w0, [x1, #0xb]
    // 0xa6418c: mov             x0, x1
    // 0xa64190: r0 = Throw()
    //     0xa64190: bl              #0xec04b8  ; ThrowStub
    // 0xa64194: brk             #0
    // 0xa64198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6419c: b               #0xa635e4
    // 0xa641a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa641a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa641a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa641a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa641a8: b               #0xa6366c
    // 0xa641ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa641ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd45d0, size: 0xd34
    // 0xbd45d0: EnterFrame
    //     0xbd45d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd45d4: mov             fp, SP
    // 0xbd45d8: AllocStack(0x28)
    //     0xbd45d8: sub             SP, SP, #0x28
    // 0xbd45dc: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd45dc: mov             x4, x2
    //     0xbd45e0: stur            x2, [fp, #-8]
    //     0xbd45e4: stur            x3, [fp, #-0x10]
    // 0xbd45e8: CheckStackOverflow
    //     0xbd45e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd45ec: cmp             SP, x16
    //     0xbd45f0: b.ls            #0xbd52a4
    // 0xbd45f4: mov             x0, x3
    // 0xbd45f8: r2 = Null
    //     0xbd45f8: mov             x2, NULL
    // 0xbd45fc: r1 = Null
    //     0xbd45fc: mov             x1, NULL
    // 0xbd4600: r4 = 60
    //     0xbd4600: movz            x4, #0x3c
    // 0xbd4604: branchIfSmi(r0, 0xbd4610)
    //     0xbd4604: tbz             w0, #0, #0xbd4610
    // 0xbd4608: r4 = LoadClassIdInstr(r0)
    //     0xbd4608: ldur            x4, [x0, #-1]
    //     0xbd460c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd4610: cmp             x4, #0x644
    // 0xbd4614: b.eq            #0xbd462c
    // 0xbd4618: r8 = PrayerTimeNotificationSetting
    //     0xbd4618: add             x8, PP, #8, lsl #12  ; [pp+0x8090] Type: PrayerTimeNotificationSetting
    //     0xbd461c: ldr             x8, [x8, #0x90]
    // 0xbd4620: r3 = Null
    //     0xbd4620: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b2a8] Null
    //     0xbd4624: ldr             x3, [x3, #0x2a8]
    // 0xbd4628: r0 = PrayerTimeNotificationSetting()
    //     0xbd4628: bl              #0x817a2c  ; IsType_PrayerTimeNotificationSetting_Stub
    // 0xbd462c: ldur            x0, [fp, #-8]
    // 0xbd4630: LoadField: r1 = r0->field_b
    //     0xbd4630: ldur            w1, [x0, #0xb]
    // 0xbd4634: DecompressPointer r1
    //     0xbd4634: add             x1, x1, HEAP, lsl #32
    // 0xbd4638: LoadField: r2 = r1->field_13
    //     0xbd4638: ldur            w2, [x1, #0x13]
    // 0xbd463c: LoadField: r1 = r0->field_13
    //     0xbd463c: ldur            x1, [x0, #0x13]
    // 0xbd4640: r3 = LoadInt32Instr(r2)
    //     0xbd4640: sbfx            x3, x2, #1, #0x1f
    // 0xbd4644: sub             x2, x3, x1
    // 0xbd4648: cmp             x2, #1
    // 0xbd464c: b.ge            #0xbd465c
    // 0xbd4650: mov             x1, x0
    // 0xbd4654: r2 = 1
    //     0xbd4654: movz            x2, #0x1
    // 0xbd4658: r0 = _increaseBufferSize()
    //     0xbd4658: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd465c: ldur            x3, [fp, #-8]
    // 0xbd4660: r2 = 21
    //     0xbd4660: movz            x2, #0x15
    // 0xbd4664: LoadField: r4 = r3->field_b
    //     0xbd4664: ldur            w4, [x3, #0xb]
    // 0xbd4668: DecompressPointer r4
    //     0xbd4668: add             x4, x4, HEAP, lsl #32
    // 0xbd466c: LoadField: r5 = r3->field_13
    //     0xbd466c: ldur            x5, [x3, #0x13]
    // 0xbd4670: add             x6, x5, #1
    // 0xbd4674: StoreField: r3->field_13 = r6
    //     0xbd4674: stur            x6, [x3, #0x13]
    // 0xbd4678: LoadField: r0 = r4->field_13
    //     0xbd4678: ldur            w0, [x4, #0x13]
    // 0xbd467c: r7 = LoadInt32Instr(r0)
    //     0xbd467c: sbfx            x7, x0, #1, #0x1f
    // 0xbd4680: mov             x0, x7
    // 0xbd4684: mov             x1, x5
    // 0xbd4688: cmp             x1, x0
    // 0xbd468c: b.hs            #0xbd52ac
    // 0xbd4690: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd4690: add             x0, x4, x5
    //     0xbd4694: strb            w2, [x0, #0x17]
    // 0xbd4698: sub             x0, x7, x6
    // 0xbd469c: cmp             x0, #1
    // 0xbd46a0: b.ge            #0xbd46b0
    // 0xbd46a4: mov             x1, x3
    // 0xbd46a8: r2 = 1
    //     0xbd46a8: movz            x2, #0x1
    // 0xbd46ac: r0 = _increaseBufferSize()
    //     0xbd46ac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd46b0: ldur            x2, [fp, #-8]
    // 0xbd46b4: ldur            x3, [fp, #-0x10]
    // 0xbd46b8: LoadField: r4 = r2->field_b
    //     0xbd46b8: ldur            w4, [x2, #0xb]
    // 0xbd46bc: DecompressPointer r4
    //     0xbd46bc: add             x4, x4, HEAP, lsl #32
    // 0xbd46c0: LoadField: r5 = r2->field_13
    //     0xbd46c0: ldur            x5, [x2, #0x13]
    // 0xbd46c4: add             x0, x5, #1
    // 0xbd46c8: StoreField: r2->field_13 = r0
    //     0xbd46c8: stur            x0, [x2, #0x13]
    // 0xbd46cc: LoadField: r0 = r4->field_13
    //     0xbd46cc: ldur            w0, [x4, #0x13]
    // 0xbd46d0: r1 = LoadInt32Instr(r0)
    //     0xbd46d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd46d4: mov             x0, x1
    // 0xbd46d8: mov             x1, x5
    // 0xbd46dc: cmp             x1, x0
    // 0xbd46e0: b.hs            #0xbd52b0
    // 0xbd46e4: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd46e4: add             x0, x4, x5
    //     0xbd46e8: strb            wzr, [x0, #0x17]
    // 0xbd46ec: LoadField: r0 = r3->field_13
    //     0xbd46ec: ldur            w0, [x3, #0x13]
    // 0xbd46f0: DecompressPointer r0
    //     0xbd46f0: add             x0, x0, HEAP, lsl #32
    // 0xbd46f4: r16 = <NotificationSound>
    //     0xbd46f4: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd46f8: ldr             x16, [x16, #0x58]
    // 0xbd46fc: stp             x2, x16, [SP, #8]
    // 0xbd4700: str             x0, [SP]
    // 0xbd4704: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4704: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4708: r0 = write()
    //     0xbd4708: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd470c: ldur            x0, [fp, #-8]
    // 0xbd4710: LoadField: r1 = r0->field_b
    //     0xbd4710: ldur            w1, [x0, #0xb]
    // 0xbd4714: DecompressPointer r1
    //     0xbd4714: add             x1, x1, HEAP, lsl #32
    // 0xbd4718: LoadField: r2 = r1->field_13
    //     0xbd4718: ldur            w2, [x1, #0x13]
    // 0xbd471c: LoadField: r1 = r0->field_13
    //     0xbd471c: ldur            x1, [x0, #0x13]
    // 0xbd4720: r3 = LoadInt32Instr(r2)
    //     0xbd4720: sbfx            x3, x2, #1, #0x1f
    // 0xbd4724: sub             x2, x3, x1
    // 0xbd4728: cmp             x2, #1
    // 0xbd472c: b.ge            #0xbd473c
    // 0xbd4730: mov             x1, x0
    // 0xbd4734: r2 = 1
    //     0xbd4734: movz            x2, #0x1
    // 0xbd4738: r0 = _increaseBufferSize()
    //     0xbd4738: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd473c: ldur            x2, [fp, #-8]
    // 0xbd4740: ldur            x3, [fp, #-0x10]
    // 0xbd4744: r4 = 1
    //     0xbd4744: movz            x4, #0x1
    // 0xbd4748: LoadField: r5 = r2->field_b
    //     0xbd4748: ldur            w5, [x2, #0xb]
    // 0xbd474c: DecompressPointer r5
    //     0xbd474c: add             x5, x5, HEAP, lsl #32
    // 0xbd4750: LoadField: r6 = r2->field_13
    //     0xbd4750: ldur            x6, [x2, #0x13]
    // 0xbd4754: add             x0, x6, #1
    // 0xbd4758: StoreField: r2->field_13 = r0
    //     0xbd4758: stur            x0, [x2, #0x13]
    // 0xbd475c: LoadField: r0 = r5->field_13
    //     0xbd475c: ldur            w0, [x5, #0x13]
    // 0xbd4760: r1 = LoadInt32Instr(r0)
    //     0xbd4760: sbfx            x1, x0, #1, #0x1f
    // 0xbd4764: mov             x0, x1
    // 0xbd4768: mov             x1, x6
    // 0xbd476c: cmp             x1, x0
    // 0xbd4770: b.hs            #0xbd52b4
    // 0xbd4774: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4774: add             x0, x5, x6
    //     0xbd4778: strb            w4, [x0, #0x17]
    // 0xbd477c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd477c: ldur            w0, [x3, #0x17]
    // 0xbd4780: DecompressPointer r0
    //     0xbd4780: add             x0, x0, HEAP, lsl #32
    // 0xbd4784: r16 = <NotificationSound>
    //     0xbd4784: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4788: ldr             x16, [x16, #0x58]
    // 0xbd478c: stp             x2, x16, [SP, #8]
    // 0xbd4790: str             x0, [SP]
    // 0xbd4794: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4794: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4798: r0 = write()
    //     0xbd4798: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd479c: ldur            x0, [fp, #-8]
    // 0xbd47a0: LoadField: r1 = r0->field_b
    //     0xbd47a0: ldur            w1, [x0, #0xb]
    // 0xbd47a4: DecompressPointer r1
    //     0xbd47a4: add             x1, x1, HEAP, lsl #32
    // 0xbd47a8: LoadField: r2 = r1->field_13
    //     0xbd47a8: ldur            w2, [x1, #0x13]
    // 0xbd47ac: LoadField: r1 = r0->field_13
    //     0xbd47ac: ldur            x1, [x0, #0x13]
    // 0xbd47b0: r3 = LoadInt32Instr(r2)
    //     0xbd47b0: sbfx            x3, x2, #1, #0x1f
    // 0xbd47b4: sub             x2, x3, x1
    // 0xbd47b8: cmp             x2, #1
    // 0xbd47bc: b.ge            #0xbd47cc
    // 0xbd47c0: mov             x1, x0
    // 0xbd47c4: r2 = 1
    //     0xbd47c4: movz            x2, #0x1
    // 0xbd47c8: r0 = _increaseBufferSize()
    //     0xbd47c8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd47cc: ldur            x2, [fp, #-8]
    // 0xbd47d0: ldur            x3, [fp, #-0x10]
    // 0xbd47d4: r4 = 2
    //     0xbd47d4: movz            x4, #0x2
    // 0xbd47d8: LoadField: r5 = r2->field_b
    //     0xbd47d8: ldur            w5, [x2, #0xb]
    // 0xbd47dc: DecompressPointer r5
    //     0xbd47dc: add             x5, x5, HEAP, lsl #32
    // 0xbd47e0: LoadField: r6 = r2->field_13
    //     0xbd47e0: ldur            x6, [x2, #0x13]
    // 0xbd47e4: add             x0, x6, #1
    // 0xbd47e8: StoreField: r2->field_13 = r0
    //     0xbd47e8: stur            x0, [x2, #0x13]
    // 0xbd47ec: LoadField: r0 = r5->field_13
    //     0xbd47ec: ldur            w0, [x5, #0x13]
    // 0xbd47f0: r1 = LoadInt32Instr(r0)
    //     0xbd47f0: sbfx            x1, x0, #1, #0x1f
    // 0xbd47f4: mov             x0, x1
    // 0xbd47f8: mov             x1, x6
    // 0xbd47fc: cmp             x1, x0
    // 0xbd4800: b.hs            #0xbd52b8
    // 0xbd4804: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4804: add             x0, x5, x6
    //     0xbd4808: strb            w4, [x0, #0x17]
    // 0xbd480c: LoadField: r0 = r3->field_1b
    //     0xbd480c: ldur            w0, [x3, #0x1b]
    // 0xbd4810: DecompressPointer r0
    //     0xbd4810: add             x0, x0, HEAP, lsl #32
    // 0xbd4814: r16 = <NotificationSound>
    //     0xbd4814: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4818: ldr             x16, [x16, #0x58]
    // 0xbd481c: stp             x2, x16, [SP, #8]
    // 0xbd4820: str             x0, [SP]
    // 0xbd4824: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4824: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4828: r0 = write()
    //     0xbd4828: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd482c: ldur            x0, [fp, #-8]
    // 0xbd4830: LoadField: r1 = r0->field_b
    //     0xbd4830: ldur            w1, [x0, #0xb]
    // 0xbd4834: DecompressPointer r1
    //     0xbd4834: add             x1, x1, HEAP, lsl #32
    // 0xbd4838: LoadField: r2 = r1->field_13
    //     0xbd4838: ldur            w2, [x1, #0x13]
    // 0xbd483c: LoadField: r1 = r0->field_13
    //     0xbd483c: ldur            x1, [x0, #0x13]
    // 0xbd4840: r3 = LoadInt32Instr(r2)
    //     0xbd4840: sbfx            x3, x2, #1, #0x1f
    // 0xbd4844: sub             x2, x3, x1
    // 0xbd4848: cmp             x2, #1
    // 0xbd484c: b.ge            #0xbd485c
    // 0xbd4850: mov             x1, x0
    // 0xbd4854: r2 = 1
    //     0xbd4854: movz            x2, #0x1
    // 0xbd4858: r0 = _increaseBufferSize()
    //     0xbd4858: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd485c: ldur            x2, [fp, #-8]
    // 0xbd4860: ldur            x3, [fp, #-0x10]
    // 0xbd4864: r4 = 3
    //     0xbd4864: movz            x4, #0x3
    // 0xbd4868: LoadField: r5 = r2->field_b
    //     0xbd4868: ldur            w5, [x2, #0xb]
    // 0xbd486c: DecompressPointer r5
    //     0xbd486c: add             x5, x5, HEAP, lsl #32
    // 0xbd4870: LoadField: r6 = r2->field_13
    //     0xbd4870: ldur            x6, [x2, #0x13]
    // 0xbd4874: add             x0, x6, #1
    // 0xbd4878: StoreField: r2->field_13 = r0
    //     0xbd4878: stur            x0, [x2, #0x13]
    // 0xbd487c: LoadField: r0 = r5->field_13
    //     0xbd487c: ldur            w0, [x5, #0x13]
    // 0xbd4880: r1 = LoadInt32Instr(r0)
    //     0xbd4880: sbfx            x1, x0, #1, #0x1f
    // 0xbd4884: mov             x0, x1
    // 0xbd4888: mov             x1, x6
    // 0xbd488c: cmp             x1, x0
    // 0xbd4890: b.hs            #0xbd52bc
    // 0xbd4894: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4894: add             x0, x5, x6
    //     0xbd4898: strb            w4, [x0, #0x17]
    // 0xbd489c: LoadField: r0 = r3->field_1f
    //     0xbd489c: ldur            w0, [x3, #0x1f]
    // 0xbd48a0: DecompressPointer r0
    //     0xbd48a0: add             x0, x0, HEAP, lsl #32
    // 0xbd48a4: r16 = <NotificationSound>
    //     0xbd48a4: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd48a8: ldr             x16, [x16, #0x58]
    // 0xbd48ac: stp             x2, x16, [SP, #8]
    // 0xbd48b0: str             x0, [SP]
    // 0xbd48b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd48b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd48b8: r0 = write()
    //     0xbd48b8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd48bc: ldur            x0, [fp, #-8]
    // 0xbd48c0: LoadField: r1 = r0->field_b
    //     0xbd48c0: ldur            w1, [x0, #0xb]
    // 0xbd48c4: DecompressPointer r1
    //     0xbd48c4: add             x1, x1, HEAP, lsl #32
    // 0xbd48c8: LoadField: r2 = r1->field_13
    //     0xbd48c8: ldur            w2, [x1, #0x13]
    // 0xbd48cc: LoadField: r1 = r0->field_13
    //     0xbd48cc: ldur            x1, [x0, #0x13]
    // 0xbd48d0: r3 = LoadInt32Instr(r2)
    //     0xbd48d0: sbfx            x3, x2, #1, #0x1f
    // 0xbd48d4: sub             x2, x3, x1
    // 0xbd48d8: cmp             x2, #1
    // 0xbd48dc: b.ge            #0xbd48ec
    // 0xbd48e0: mov             x1, x0
    // 0xbd48e4: r2 = 1
    //     0xbd48e4: movz            x2, #0x1
    // 0xbd48e8: r0 = _increaseBufferSize()
    //     0xbd48e8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd48ec: ldur            x2, [fp, #-8]
    // 0xbd48f0: ldur            x3, [fp, #-0x10]
    // 0xbd48f4: r4 = 4
    //     0xbd48f4: movz            x4, #0x4
    // 0xbd48f8: LoadField: r5 = r2->field_b
    //     0xbd48f8: ldur            w5, [x2, #0xb]
    // 0xbd48fc: DecompressPointer r5
    //     0xbd48fc: add             x5, x5, HEAP, lsl #32
    // 0xbd4900: LoadField: r6 = r2->field_13
    //     0xbd4900: ldur            x6, [x2, #0x13]
    // 0xbd4904: add             x0, x6, #1
    // 0xbd4908: StoreField: r2->field_13 = r0
    //     0xbd4908: stur            x0, [x2, #0x13]
    // 0xbd490c: LoadField: r0 = r5->field_13
    //     0xbd490c: ldur            w0, [x5, #0x13]
    // 0xbd4910: r1 = LoadInt32Instr(r0)
    //     0xbd4910: sbfx            x1, x0, #1, #0x1f
    // 0xbd4914: mov             x0, x1
    // 0xbd4918: mov             x1, x6
    // 0xbd491c: cmp             x1, x0
    // 0xbd4920: b.hs            #0xbd52c0
    // 0xbd4924: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4924: add             x0, x5, x6
    //     0xbd4928: strb            w4, [x0, #0x17]
    // 0xbd492c: LoadField: r0 = r3->field_23
    //     0xbd492c: ldur            w0, [x3, #0x23]
    // 0xbd4930: DecompressPointer r0
    //     0xbd4930: add             x0, x0, HEAP, lsl #32
    // 0xbd4934: r16 = <NotificationSound>
    //     0xbd4934: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4938: ldr             x16, [x16, #0x58]
    // 0xbd493c: stp             x2, x16, [SP, #8]
    // 0xbd4940: str             x0, [SP]
    // 0xbd4944: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4944: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4948: r0 = write()
    //     0xbd4948: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd494c: ldur            x0, [fp, #-8]
    // 0xbd4950: LoadField: r1 = r0->field_b
    //     0xbd4950: ldur            w1, [x0, #0xb]
    // 0xbd4954: DecompressPointer r1
    //     0xbd4954: add             x1, x1, HEAP, lsl #32
    // 0xbd4958: LoadField: r2 = r1->field_13
    //     0xbd4958: ldur            w2, [x1, #0x13]
    // 0xbd495c: LoadField: r1 = r0->field_13
    //     0xbd495c: ldur            x1, [x0, #0x13]
    // 0xbd4960: r3 = LoadInt32Instr(r2)
    //     0xbd4960: sbfx            x3, x2, #1, #0x1f
    // 0xbd4964: sub             x2, x3, x1
    // 0xbd4968: cmp             x2, #1
    // 0xbd496c: b.ge            #0xbd497c
    // 0xbd4970: mov             x1, x0
    // 0xbd4974: r2 = 1
    //     0xbd4974: movz            x2, #0x1
    // 0xbd4978: r0 = _increaseBufferSize()
    //     0xbd4978: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd497c: ldur            x2, [fp, #-8]
    // 0xbd4980: ldur            x3, [fp, #-0x10]
    // 0xbd4984: r4 = 5
    //     0xbd4984: movz            x4, #0x5
    // 0xbd4988: LoadField: r5 = r2->field_b
    //     0xbd4988: ldur            w5, [x2, #0xb]
    // 0xbd498c: DecompressPointer r5
    //     0xbd498c: add             x5, x5, HEAP, lsl #32
    // 0xbd4990: LoadField: r6 = r2->field_13
    //     0xbd4990: ldur            x6, [x2, #0x13]
    // 0xbd4994: add             x0, x6, #1
    // 0xbd4998: StoreField: r2->field_13 = r0
    //     0xbd4998: stur            x0, [x2, #0x13]
    // 0xbd499c: LoadField: r0 = r5->field_13
    //     0xbd499c: ldur            w0, [x5, #0x13]
    // 0xbd49a0: r1 = LoadInt32Instr(r0)
    //     0xbd49a0: sbfx            x1, x0, #1, #0x1f
    // 0xbd49a4: mov             x0, x1
    // 0xbd49a8: mov             x1, x6
    // 0xbd49ac: cmp             x1, x0
    // 0xbd49b0: b.hs            #0xbd52c4
    // 0xbd49b4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd49b4: add             x0, x5, x6
    //     0xbd49b8: strb            w4, [x0, #0x17]
    // 0xbd49bc: LoadField: r0 = r3->field_27
    //     0xbd49bc: ldur            w0, [x3, #0x27]
    // 0xbd49c0: DecompressPointer r0
    //     0xbd49c0: add             x0, x0, HEAP, lsl #32
    // 0xbd49c4: r16 = <NotificationSound>
    //     0xbd49c4: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd49c8: ldr             x16, [x16, #0x58]
    // 0xbd49cc: stp             x2, x16, [SP, #8]
    // 0xbd49d0: str             x0, [SP]
    // 0xbd49d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd49d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd49d8: r0 = write()
    //     0xbd49d8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd49dc: ldur            x0, [fp, #-8]
    // 0xbd49e0: LoadField: r1 = r0->field_b
    //     0xbd49e0: ldur            w1, [x0, #0xb]
    // 0xbd49e4: DecompressPointer r1
    //     0xbd49e4: add             x1, x1, HEAP, lsl #32
    // 0xbd49e8: LoadField: r2 = r1->field_13
    //     0xbd49e8: ldur            w2, [x1, #0x13]
    // 0xbd49ec: LoadField: r1 = r0->field_13
    //     0xbd49ec: ldur            x1, [x0, #0x13]
    // 0xbd49f0: r3 = LoadInt32Instr(r2)
    //     0xbd49f0: sbfx            x3, x2, #1, #0x1f
    // 0xbd49f4: sub             x2, x3, x1
    // 0xbd49f8: cmp             x2, #1
    // 0xbd49fc: b.ge            #0xbd4a0c
    // 0xbd4a00: mov             x1, x0
    // 0xbd4a04: r2 = 1
    //     0xbd4a04: movz            x2, #0x1
    // 0xbd4a08: r0 = _increaseBufferSize()
    //     0xbd4a08: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4a0c: ldur            x2, [fp, #-8]
    // 0xbd4a10: ldur            x3, [fp, #-0x10]
    // 0xbd4a14: r4 = 6
    //     0xbd4a14: movz            x4, #0x6
    // 0xbd4a18: LoadField: r5 = r2->field_b
    //     0xbd4a18: ldur            w5, [x2, #0xb]
    // 0xbd4a1c: DecompressPointer r5
    //     0xbd4a1c: add             x5, x5, HEAP, lsl #32
    // 0xbd4a20: LoadField: r6 = r2->field_13
    //     0xbd4a20: ldur            x6, [x2, #0x13]
    // 0xbd4a24: add             x0, x6, #1
    // 0xbd4a28: StoreField: r2->field_13 = r0
    //     0xbd4a28: stur            x0, [x2, #0x13]
    // 0xbd4a2c: LoadField: r0 = r5->field_13
    //     0xbd4a2c: ldur            w0, [x5, #0x13]
    // 0xbd4a30: r1 = LoadInt32Instr(r0)
    //     0xbd4a30: sbfx            x1, x0, #1, #0x1f
    // 0xbd4a34: mov             x0, x1
    // 0xbd4a38: mov             x1, x6
    // 0xbd4a3c: cmp             x1, x0
    // 0xbd4a40: b.hs            #0xbd52c8
    // 0xbd4a44: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4a44: add             x0, x5, x6
    //     0xbd4a48: strb            w4, [x0, #0x17]
    // 0xbd4a4c: LoadField: r0 = r3->field_2b
    //     0xbd4a4c: ldur            w0, [x3, #0x2b]
    // 0xbd4a50: DecompressPointer r0
    //     0xbd4a50: add             x0, x0, HEAP, lsl #32
    // 0xbd4a54: r16 = <NotificationSound>
    //     0xbd4a54: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4a58: ldr             x16, [x16, #0x58]
    // 0xbd4a5c: stp             x2, x16, [SP, #8]
    // 0xbd4a60: str             x0, [SP]
    // 0xbd4a64: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4a64: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4a68: r0 = write()
    //     0xbd4a68: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4a6c: ldur            x0, [fp, #-8]
    // 0xbd4a70: LoadField: r1 = r0->field_b
    //     0xbd4a70: ldur            w1, [x0, #0xb]
    // 0xbd4a74: DecompressPointer r1
    //     0xbd4a74: add             x1, x1, HEAP, lsl #32
    // 0xbd4a78: LoadField: r2 = r1->field_13
    //     0xbd4a78: ldur            w2, [x1, #0x13]
    // 0xbd4a7c: LoadField: r1 = r0->field_13
    //     0xbd4a7c: ldur            x1, [x0, #0x13]
    // 0xbd4a80: r3 = LoadInt32Instr(r2)
    //     0xbd4a80: sbfx            x3, x2, #1, #0x1f
    // 0xbd4a84: sub             x2, x3, x1
    // 0xbd4a88: cmp             x2, #1
    // 0xbd4a8c: b.ge            #0xbd4a9c
    // 0xbd4a90: mov             x1, x0
    // 0xbd4a94: r2 = 1
    //     0xbd4a94: movz            x2, #0x1
    // 0xbd4a98: r0 = _increaseBufferSize()
    //     0xbd4a98: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4a9c: ldur            x2, [fp, #-8]
    // 0xbd4aa0: ldur            x3, [fp, #-0x10]
    // 0xbd4aa4: r4 = 7
    //     0xbd4aa4: movz            x4, #0x7
    // 0xbd4aa8: LoadField: r5 = r2->field_b
    //     0xbd4aa8: ldur            w5, [x2, #0xb]
    // 0xbd4aac: DecompressPointer r5
    //     0xbd4aac: add             x5, x5, HEAP, lsl #32
    // 0xbd4ab0: LoadField: r6 = r2->field_13
    //     0xbd4ab0: ldur            x6, [x2, #0x13]
    // 0xbd4ab4: add             x0, x6, #1
    // 0xbd4ab8: StoreField: r2->field_13 = r0
    //     0xbd4ab8: stur            x0, [x2, #0x13]
    // 0xbd4abc: LoadField: r0 = r5->field_13
    //     0xbd4abc: ldur            w0, [x5, #0x13]
    // 0xbd4ac0: r1 = LoadInt32Instr(r0)
    //     0xbd4ac0: sbfx            x1, x0, #1, #0x1f
    // 0xbd4ac4: mov             x0, x1
    // 0xbd4ac8: mov             x1, x6
    // 0xbd4acc: cmp             x1, x0
    // 0xbd4ad0: b.hs            #0xbd52cc
    // 0xbd4ad4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4ad4: add             x0, x5, x6
    //     0xbd4ad8: strb            w4, [x0, #0x17]
    // 0xbd4adc: LoadField: r0 = r3->field_2f
    //     0xbd4adc: ldur            w0, [x3, #0x2f]
    // 0xbd4ae0: DecompressPointer r0
    //     0xbd4ae0: add             x0, x0, HEAP, lsl #32
    // 0xbd4ae4: r16 = <NotificationSound>
    //     0xbd4ae4: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4ae8: ldr             x16, [x16, #0x58]
    // 0xbd4aec: stp             x2, x16, [SP, #8]
    // 0xbd4af0: str             x0, [SP]
    // 0xbd4af4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4af4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4af8: r0 = write()
    //     0xbd4af8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4afc: ldur            x0, [fp, #-8]
    // 0xbd4b00: LoadField: r1 = r0->field_b
    //     0xbd4b00: ldur            w1, [x0, #0xb]
    // 0xbd4b04: DecompressPointer r1
    //     0xbd4b04: add             x1, x1, HEAP, lsl #32
    // 0xbd4b08: LoadField: r2 = r1->field_13
    //     0xbd4b08: ldur            w2, [x1, #0x13]
    // 0xbd4b0c: LoadField: r1 = r0->field_13
    //     0xbd4b0c: ldur            x1, [x0, #0x13]
    // 0xbd4b10: r3 = LoadInt32Instr(r2)
    //     0xbd4b10: sbfx            x3, x2, #1, #0x1f
    // 0xbd4b14: sub             x2, x3, x1
    // 0xbd4b18: cmp             x2, #1
    // 0xbd4b1c: b.ge            #0xbd4b2c
    // 0xbd4b20: mov             x1, x0
    // 0xbd4b24: r2 = 1
    //     0xbd4b24: movz            x2, #0x1
    // 0xbd4b28: r0 = _increaseBufferSize()
    //     0xbd4b28: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4b2c: ldur            x2, [fp, #-8]
    // 0xbd4b30: ldur            x3, [fp, #-0x10]
    // 0xbd4b34: r4 = 8
    //     0xbd4b34: movz            x4, #0x8
    // 0xbd4b38: LoadField: r5 = r2->field_b
    //     0xbd4b38: ldur            w5, [x2, #0xb]
    // 0xbd4b3c: DecompressPointer r5
    //     0xbd4b3c: add             x5, x5, HEAP, lsl #32
    // 0xbd4b40: LoadField: r6 = r2->field_13
    //     0xbd4b40: ldur            x6, [x2, #0x13]
    // 0xbd4b44: add             x0, x6, #1
    // 0xbd4b48: StoreField: r2->field_13 = r0
    //     0xbd4b48: stur            x0, [x2, #0x13]
    // 0xbd4b4c: LoadField: r0 = r5->field_13
    //     0xbd4b4c: ldur            w0, [x5, #0x13]
    // 0xbd4b50: r1 = LoadInt32Instr(r0)
    //     0xbd4b50: sbfx            x1, x0, #1, #0x1f
    // 0xbd4b54: mov             x0, x1
    // 0xbd4b58: mov             x1, x6
    // 0xbd4b5c: cmp             x1, x0
    // 0xbd4b60: b.hs            #0xbd52d0
    // 0xbd4b64: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4b64: add             x0, x5, x6
    //     0xbd4b68: strb            w4, [x0, #0x17]
    // 0xbd4b6c: LoadField: r0 = r3->field_33
    //     0xbd4b6c: ldur            w0, [x3, #0x33]
    // 0xbd4b70: DecompressPointer r0
    //     0xbd4b70: add             x0, x0, HEAP, lsl #32
    // 0xbd4b74: r16 = <NotificationSound>
    //     0xbd4b74: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4b78: ldr             x16, [x16, #0x58]
    // 0xbd4b7c: stp             x2, x16, [SP, #8]
    // 0xbd4b80: str             x0, [SP]
    // 0xbd4b84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4b84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4b88: r0 = write()
    //     0xbd4b88: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4b8c: ldur            x0, [fp, #-8]
    // 0xbd4b90: LoadField: r1 = r0->field_b
    //     0xbd4b90: ldur            w1, [x0, #0xb]
    // 0xbd4b94: DecompressPointer r1
    //     0xbd4b94: add             x1, x1, HEAP, lsl #32
    // 0xbd4b98: LoadField: r2 = r1->field_13
    //     0xbd4b98: ldur            w2, [x1, #0x13]
    // 0xbd4b9c: LoadField: r1 = r0->field_13
    //     0xbd4b9c: ldur            x1, [x0, #0x13]
    // 0xbd4ba0: r3 = LoadInt32Instr(r2)
    //     0xbd4ba0: sbfx            x3, x2, #1, #0x1f
    // 0xbd4ba4: sub             x2, x3, x1
    // 0xbd4ba8: cmp             x2, #1
    // 0xbd4bac: b.ge            #0xbd4bbc
    // 0xbd4bb0: mov             x1, x0
    // 0xbd4bb4: r2 = 1
    //     0xbd4bb4: movz            x2, #0x1
    // 0xbd4bb8: r0 = _increaseBufferSize()
    //     0xbd4bb8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4bbc: ldur            x2, [fp, #-8]
    // 0xbd4bc0: ldur            x3, [fp, #-0x10]
    // 0xbd4bc4: r4 = 9
    //     0xbd4bc4: movz            x4, #0x9
    // 0xbd4bc8: LoadField: r5 = r2->field_b
    //     0xbd4bc8: ldur            w5, [x2, #0xb]
    // 0xbd4bcc: DecompressPointer r5
    //     0xbd4bcc: add             x5, x5, HEAP, lsl #32
    // 0xbd4bd0: LoadField: r6 = r2->field_13
    //     0xbd4bd0: ldur            x6, [x2, #0x13]
    // 0xbd4bd4: add             x0, x6, #1
    // 0xbd4bd8: StoreField: r2->field_13 = r0
    //     0xbd4bd8: stur            x0, [x2, #0x13]
    // 0xbd4bdc: LoadField: r0 = r5->field_13
    //     0xbd4bdc: ldur            w0, [x5, #0x13]
    // 0xbd4be0: r1 = LoadInt32Instr(r0)
    //     0xbd4be0: sbfx            x1, x0, #1, #0x1f
    // 0xbd4be4: mov             x0, x1
    // 0xbd4be8: mov             x1, x6
    // 0xbd4bec: cmp             x1, x0
    // 0xbd4bf0: b.hs            #0xbd52d4
    // 0xbd4bf4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4bf4: add             x0, x5, x6
    //     0xbd4bf8: strb            w4, [x0, #0x17]
    // 0xbd4bfc: LoadField: r0 = r3->field_37
    //     0xbd4bfc: ldur            w0, [x3, #0x37]
    // 0xbd4c00: DecompressPointer r0
    //     0xbd4c00: add             x0, x0, HEAP, lsl #32
    // 0xbd4c04: r16 = <NotificationSound>
    //     0xbd4c04: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4c08: ldr             x16, [x16, #0x58]
    // 0xbd4c0c: stp             x2, x16, [SP, #8]
    // 0xbd4c10: str             x0, [SP]
    // 0xbd4c14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4c14: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4c18: r0 = write()
    //     0xbd4c18: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4c1c: ldur            x0, [fp, #-8]
    // 0xbd4c20: LoadField: r1 = r0->field_b
    //     0xbd4c20: ldur            w1, [x0, #0xb]
    // 0xbd4c24: DecompressPointer r1
    //     0xbd4c24: add             x1, x1, HEAP, lsl #32
    // 0xbd4c28: LoadField: r2 = r1->field_13
    //     0xbd4c28: ldur            w2, [x1, #0x13]
    // 0xbd4c2c: LoadField: r1 = r0->field_13
    //     0xbd4c2c: ldur            x1, [x0, #0x13]
    // 0xbd4c30: r3 = LoadInt32Instr(r2)
    //     0xbd4c30: sbfx            x3, x2, #1, #0x1f
    // 0xbd4c34: sub             x2, x3, x1
    // 0xbd4c38: cmp             x2, #1
    // 0xbd4c3c: b.ge            #0xbd4c4c
    // 0xbd4c40: mov             x1, x0
    // 0xbd4c44: r2 = 1
    //     0xbd4c44: movz            x2, #0x1
    // 0xbd4c48: r0 = _increaseBufferSize()
    //     0xbd4c48: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4c4c: ldur            x2, [fp, #-8]
    // 0xbd4c50: ldur            x3, [fp, #-0x10]
    // 0xbd4c54: r4 = 10
    //     0xbd4c54: movz            x4, #0xa
    // 0xbd4c58: LoadField: r5 = r2->field_b
    //     0xbd4c58: ldur            w5, [x2, #0xb]
    // 0xbd4c5c: DecompressPointer r5
    //     0xbd4c5c: add             x5, x5, HEAP, lsl #32
    // 0xbd4c60: LoadField: r6 = r2->field_13
    //     0xbd4c60: ldur            x6, [x2, #0x13]
    // 0xbd4c64: add             x0, x6, #1
    // 0xbd4c68: StoreField: r2->field_13 = r0
    //     0xbd4c68: stur            x0, [x2, #0x13]
    // 0xbd4c6c: LoadField: r0 = r5->field_13
    //     0xbd4c6c: ldur            w0, [x5, #0x13]
    // 0xbd4c70: r1 = LoadInt32Instr(r0)
    //     0xbd4c70: sbfx            x1, x0, #1, #0x1f
    // 0xbd4c74: mov             x0, x1
    // 0xbd4c78: mov             x1, x6
    // 0xbd4c7c: cmp             x1, x0
    // 0xbd4c80: b.hs            #0xbd52d8
    // 0xbd4c84: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4c84: add             x0, x5, x6
    //     0xbd4c88: strb            w4, [x0, #0x17]
    // 0xbd4c8c: LoadField: r0 = r3->field_3b
    //     0xbd4c8c: ldur            w0, [x3, #0x3b]
    // 0xbd4c90: DecompressPointer r0
    //     0xbd4c90: add             x0, x0, HEAP, lsl #32
    // 0xbd4c94: r16 = <NotificationSound>
    //     0xbd4c94: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4c98: ldr             x16, [x16, #0x58]
    // 0xbd4c9c: stp             x2, x16, [SP, #8]
    // 0xbd4ca0: str             x0, [SP]
    // 0xbd4ca4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4ca4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4ca8: r0 = write()
    //     0xbd4ca8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4cac: ldur            x0, [fp, #-8]
    // 0xbd4cb0: LoadField: r1 = r0->field_b
    //     0xbd4cb0: ldur            w1, [x0, #0xb]
    // 0xbd4cb4: DecompressPointer r1
    //     0xbd4cb4: add             x1, x1, HEAP, lsl #32
    // 0xbd4cb8: LoadField: r2 = r1->field_13
    //     0xbd4cb8: ldur            w2, [x1, #0x13]
    // 0xbd4cbc: LoadField: r1 = r0->field_13
    //     0xbd4cbc: ldur            x1, [x0, #0x13]
    // 0xbd4cc0: r3 = LoadInt32Instr(r2)
    //     0xbd4cc0: sbfx            x3, x2, #1, #0x1f
    // 0xbd4cc4: sub             x2, x3, x1
    // 0xbd4cc8: cmp             x2, #1
    // 0xbd4ccc: b.ge            #0xbd4cdc
    // 0xbd4cd0: mov             x1, x0
    // 0xbd4cd4: r2 = 1
    //     0xbd4cd4: movz            x2, #0x1
    // 0xbd4cd8: r0 = _increaseBufferSize()
    //     0xbd4cd8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4cdc: ldur            x2, [fp, #-8]
    // 0xbd4ce0: ldur            x3, [fp, #-0x10]
    // 0xbd4ce4: r4 = 11
    //     0xbd4ce4: movz            x4, #0xb
    // 0xbd4ce8: LoadField: r5 = r2->field_b
    //     0xbd4ce8: ldur            w5, [x2, #0xb]
    // 0xbd4cec: DecompressPointer r5
    //     0xbd4cec: add             x5, x5, HEAP, lsl #32
    // 0xbd4cf0: LoadField: r6 = r2->field_13
    //     0xbd4cf0: ldur            x6, [x2, #0x13]
    // 0xbd4cf4: add             x0, x6, #1
    // 0xbd4cf8: StoreField: r2->field_13 = r0
    //     0xbd4cf8: stur            x0, [x2, #0x13]
    // 0xbd4cfc: LoadField: r0 = r5->field_13
    //     0xbd4cfc: ldur            w0, [x5, #0x13]
    // 0xbd4d00: r1 = LoadInt32Instr(r0)
    //     0xbd4d00: sbfx            x1, x0, #1, #0x1f
    // 0xbd4d04: mov             x0, x1
    // 0xbd4d08: mov             x1, x6
    // 0xbd4d0c: cmp             x1, x0
    // 0xbd4d10: b.hs            #0xbd52dc
    // 0xbd4d14: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4d14: add             x0, x5, x6
    //     0xbd4d18: strb            w4, [x0, #0x17]
    // 0xbd4d1c: LoadField: r0 = r3->field_3f
    //     0xbd4d1c: ldur            w0, [x3, #0x3f]
    // 0xbd4d20: DecompressPointer r0
    //     0xbd4d20: add             x0, x0, HEAP, lsl #32
    // 0xbd4d24: r16 = <NotificationSound>
    //     0xbd4d24: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4d28: ldr             x16, [x16, #0x58]
    // 0xbd4d2c: stp             x2, x16, [SP, #8]
    // 0xbd4d30: str             x0, [SP]
    // 0xbd4d34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4d34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4d38: r0 = write()
    //     0xbd4d38: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4d3c: ldur            x0, [fp, #-8]
    // 0xbd4d40: LoadField: r1 = r0->field_b
    //     0xbd4d40: ldur            w1, [x0, #0xb]
    // 0xbd4d44: DecompressPointer r1
    //     0xbd4d44: add             x1, x1, HEAP, lsl #32
    // 0xbd4d48: LoadField: r2 = r1->field_13
    //     0xbd4d48: ldur            w2, [x1, #0x13]
    // 0xbd4d4c: LoadField: r1 = r0->field_13
    //     0xbd4d4c: ldur            x1, [x0, #0x13]
    // 0xbd4d50: r3 = LoadInt32Instr(r2)
    //     0xbd4d50: sbfx            x3, x2, #1, #0x1f
    // 0xbd4d54: sub             x2, x3, x1
    // 0xbd4d58: cmp             x2, #1
    // 0xbd4d5c: b.ge            #0xbd4d6c
    // 0xbd4d60: mov             x1, x0
    // 0xbd4d64: r2 = 1
    //     0xbd4d64: movz            x2, #0x1
    // 0xbd4d68: r0 = _increaseBufferSize()
    //     0xbd4d68: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4d6c: ldur            x2, [fp, #-8]
    // 0xbd4d70: ldur            x3, [fp, #-0x10]
    // 0xbd4d74: r4 = 12
    //     0xbd4d74: movz            x4, #0xc
    // 0xbd4d78: LoadField: r5 = r2->field_b
    //     0xbd4d78: ldur            w5, [x2, #0xb]
    // 0xbd4d7c: DecompressPointer r5
    //     0xbd4d7c: add             x5, x5, HEAP, lsl #32
    // 0xbd4d80: LoadField: r6 = r2->field_13
    //     0xbd4d80: ldur            x6, [x2, #0x13]
    // 0xbd4d84: add             x0, x6, #1
    // 0xbd4d88: StoreField: r2->field_13 = r0
    //     0xbd4d88: stur            x0, [x2, #0x13]
    // 0xbd4d8c: LoadField: r0 = r5->field_13
    //     0xbd4d8c: ldur            w0, [x5, #0x13]
    // 0xbd4d90: r1 = LoadInt32Instr(r0)
    //     0xbd4d90: sbfx            x1, x0, #1, #0x1f
    // 0xbd4d94: mov             x0, x1
    // 0xbd4d98: mov             x1, x6
    // 0xbd4d9c: cmp             x1, x0
    // 0xbd4da0: b.hs            #0xbd52e0
    // 0xbd4da4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4da4: add             x0, x5, x6
    //     0xbd4da8: strb            w4, [x0, #0x17]
    // 0xbd4dac: LoadField: r0 = r3->field_43
    //     0xbd4dac: ldur            w0, [x3, #0x43]
    // 0xbd4db0: DecompressPointer r0
    //     0xbd4db0: add             x0, x0, HEAP, lsl #32
    // 0xbd4db4: r16 = <NotificationSound>
    //     0xbd4db4: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4db8: ldr             x16, [x16, #0x58]
    // 0xbd4dbc: stp             x2, x16, [SP, #8]
    // 0xbd4dc0: str             x0, [SP]
    // 0xbd4dc4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4dc4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4dc8: r0 = write()
    //     0xbd4dc8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4dcc: ldur            x0, [fp, #-8]
    // 0xbd4dd0: LoadField: r1 = r0->field_b
    //     0xbd4dd0: ldur            w1, [x0, #0xb]
    // 0xbd4dd4: DecompressPointer r1
    //     0xbd4dd4: add             x1, x1, HEAP, lsl #32
    // 0xbd4dd8: LoadField: r2 = r1->field_13
    //     0xbd4dd8: ldur            w2, [x1, #0x13]
    // 0xbd4ddc: LoadField: r1 = r0->field_13
    //     0xbd4ddc: ldur            x1, [x0, #0x13]
    // 0xbd4de0: r3 = LoadInt32Instr(r2)
    //     0xbd4de0: sbfx            x3, x2, #1, #0x1f
    // 0xbd4de4: sub             x2, x3, x1
    // 0xbd4de8: cmp             x2, #1
    // 0xbd4dec: b.ge            #0xbd4dfc
    // 0xbd4df0: mov             x1, x0
    // 0xbd4df4: r2 = 1
    //     0xbd4df4: movz            x2, #0x1
    // 0xbd4df8: r0 = _increaseBufferSize()
    //     0xbd4df8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4dfc: ldur            x2, [fp, #-8]
    // 0xbd4e00: ldur            x3, [fp, #-0x10]
    // 0xbd4e04: r4 = 13
    //     0xbd4e04: movz            x4, #0xd
    // 0xbd4e08: LoadField: r5 = r2->field_b
    //     0xbd4e08: ldur            w5, [x2, #0xb]
    // 0xbd4e0c: DecompressPointer r5
    //     0xbd4e0c: add             x5, x5, HEAP, lsl #32
    // 0xbd4e10: LoadField: r6 = r2->field_13
    //     0xbd4e10: ldur            x6, [x2, #0x13]
    // 0xbd4e14: add             x0, x6, #1
    // 0xbd4e18: StoreField: r2->field_13 = r0
    //     0xbd4e18: stur            x0, [x2, #0x13]
    // 0xbd4e1c: LoadField: r0 = r5->field_13
    //     0xbd4e1c: ldur            w0, [x5, #0x13]
    // 0xbd4e20: r1 = LoadInt32Instr(r0)
    //     0xbd4e20: sbfx            x1, x0, #1, #0x1f
    // 0xbd4e24: mov             x0, x1
    // 0xbd4e28: mov             x1, x6
    // 0xbd4e2c: cmp             x1, x0
    // 0xbd4e30: b.hs            #0xbd52e4
    // 0xbd4e34: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4e34: add             x0, x5, x6
    //     0xbd4e38: strb            w4, [x0, #0x17]
    // 0xbd4e3c: LoadField: r0 = r3->field_47
    //     0xbd4e3c: ldur            w0, [x3, #0x47]
    // 0xbd4e40: DecompressPointer r0
    //     0xbd4e40: add             x0, x0, HEAP, lsl #32
    // 0xbd4e44: r16 = <NotificationSound>
    //     0xbd4e44: add             x16, PP, #8, lsl #12  ; [pp+0x8058] TypeArguments: <NotificationSound>
    //     0xbd4e48: ldr             x16, [x16, #0x58]
    // 0xbd4e4c: stp             x2, x16, [SP, #8]
    // 0xbd4e50: str             x0, [SP]
    // 0xbd4e54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4e54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4e58: r0 = write()
    //     0xbd4e58: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4e5c: ldur            x0, [fp, #-8]
    // 0xbd4e60: LoadField: r1 = r0->field_b
    //     0xbd4e60: ldur            w1, [x0, #0xb]
    // 0xbd4e64: DecompressPointer r1
    //     0xbd4e64: add             x1, x1, HEAP, lsl #32
    // 0xbd4e68: LoadField: r2 = r1->field_13
    //     0xbd4e68: ldur            w2, [x1, #0x13]
    // 0xbd4e6c: LoadField: r1 = r0->field_13
    //     0xbd4e6c: ldur            x1, [x0, #0x13]
    // 0xbd4e70: r3 = LoadInt32Instr(r2)
    //     0xbd4e70: sbfx            x3, x2, #1, #0x1f
    // 0xbd4e74: sub             x2, x3, x1
    // 0xbd4e78: cmp             x2, #1
    // 0xbd4e7c: b.ge            #0xbd4e8c
    // 0xbd4e80: mov             x1, x0
    // 0xbd4e84: r2 = 1
    //     0xbd4e84: movz            x2, #0x1
    // 0xbd4e88: r0 = _increaseBufferSize()
    //     0xbd4e88: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4e8c: ldur            x2, [fp, #-8]
    // 0xbd4e90: ldur            x3, [fp, #-0x10]
    // 0xbd4e94: r4 = 14
    //     0xbd4e94: movz            x4, #0xe
    // 0xbd4e98: LoadField: r5 = r2->field_b
    //     0xbd4e98: ldur            w5, [x2, #0xb]
    // 0xbd4e9c: DecompressPointer r5
    //     0xbd4e9c: add             x5, x5, HEAP, lsl #32
    // 0xbd4ea0: LoadField: r6 = r2->field_13
    //     0xbd4ea0: ldur            x6, [x2, #0x13]
    // 0xbd4ea4: add             x0, x6, #1
    // 0xbd4ea8: StoreField: r2->field_13 = r0
    //     0xbd4ea8: stur            x0, [x2, #0x13]
    // 0xbd4eac: LoadField: r0 = r5->field_13
    //     0xbd4eac: ldur            w0, [x5, #0x13]
    // 0xbd4eb0: r1 = LoadInt32Instr(r0)
    //     0xbd4eb0: sbfx            x1, x0, #1, #0x1f
    // 0xbd4eb4: mov             x0, x1
    // 0xbd4eb8: mov             x1, x6
    // 0xbd4ebc: cmp             x1, x0
    // 0xbd4ec0: b.hs            #0xbd52e8
    // 0xbd4ec4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4ec4: add             x0, x5, x6
    //     0xbd4ec8: strb            w4, [x0, #0x17]
    // 0xbd4ecc: LoadField: r4 = r3->field_4b
    //     0xbd4ecc: ldur            x4, [x3, #0x4b]
    // 0xbd4ed0: r0 = BoxInt64Instr(r4)
    //     0xbd4ed0: sbfiz           x0, x4, #1, #0x1f
    //     0xbd4ed4: cmp             x4, x0, asr #1
    //     0xbd4ed8: b.eq            #0xbd4ee4
    //     0xbd4edc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd4ee0: stur            x4, [x0, #7]
    // 0xbd4ee4: r16 = <int>
    //     0xbd4ee4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd4ee8: stp             x2, x16, [SP, #8]
    // 0xbd4eec: str             x0, [SP]
    // 0xbd4ef0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4ef0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4ef4: r0 = write()
    //     0xbd4ef4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4ef8: ldur            x0, [fp, #-8]
    // 0xbd4efc: LoadField: r1 = r0->field_b
    //     0xbd4efc: ldur            w1, [x0, #0xb]
    // 0xbd4f00: DecompressPointer r1
    //     0xbd4f00: add             x1, x1, HEAP, lsl #32
    // 0xbd4f04: LoadField: r2 = r1->field_13
    //     0xbd4f04: ldur            w2, [x1, #0x13]
    // 0xbd4f08: LoadField: r1 = r0->field_13
    //     0xbd4f08: ldur            x1, [x0, #0x13]
    // 0xbd4f0c: r3 = LoadInt32Instr(r2)
    //     0xbd4f0c: sbfx            x3, x2, #1, #0x1f
    // 0xbd4f10: sub             x2, x3, x1
    // 0xbd4f14: cmp             x2, #1
    // 0xbd4f18: b.ge            #0xbd4f28
    // 0xbd4f1c: mov             x1, x0
    // 0xbd4f20: r2 = 1
    //     0xbd4f20: movz            x2, #0x1
    // 0xbd4f24: r0 = _increaseBufferSize()
    //     0xbd4f24: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4f28: ldur            x2, [fp, #-8]
    // 0xbd4f2c: ldur            x3, [fp, #-0x10]
    // 0xbd4f30: r4 = 15
    //     0xbd4f30: movz            x4, #0xf
    // 0xbd4f34: LoadField: r5 = r2->field_b
    //     0xbd4f34: ldur            w5, [x2, #0xb]
    // 0xbd4f38: DecompressPointer r5
    //     0xbd4f38: add             x5, x5, HEAP, lsl #32
    // 0xbd4f3c: LoadField: r6 = r2->field_13
    //     0xbd4f3c: ldur            x6, [x2, #0x13]
    // 0xbd4f40: add             x0, x6, #1
    // 0xbd4f44: StoreField: r2->field_13 = r0
    //     0xbd4f44: stur            x0, [x2, #0x13]
    // 0xbd4f48: LoadField: r0 = r5->field_13
    //     0xbd4f48: ldur            w0, [x5, #0x13]
    // 0xbd4f4c: r1 = LoadInt32Instr(r0)
    //     0xbd4f4c: sbfx            x1, x0, #1, #0x1f
    // 0xbd4f50: mov             x0, x1
    // 0xbd4f54: mov             x1, x6
    // 0xbd4f58: cmp             x1, x0
    // 0xbd4f5c: b.hs            #0xbd52ec
    // 0xbd4f60: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4f60: add             x0, x5, x6
    //     0xbd4f64: strb            w4, [x0, #0x17]
    // 0xbd4f68: LoadField: r4 = r3->field_53
    //     0xbd4f68: ldur            x4, [x3, #0x53]
    // 0xbd4f6c: r0 = BoxInt64Instr(r4)
    //     0xbd4f6c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd4f70: cmp             x4, x0, asr #1
    //     0xbd4f74: b.eq            #0xbd4f80
    //     0xbd4f78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd4f7c: stur            x4, [x0, #7]
    // 0xbd4f80: r16 = <int>
    //     0xbd4f80: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd4f84: stp             x2, x16, [SP, #8]
    // 0xbd4f88: str             x0, [SP]
    // 0xbd4f8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd4f8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd4f90: r0 = write()
    //     0xbd4f90: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd4f94: ldur            x0, [fp, #-8]
    // 0xbd4f98: LoadField: r1 = r0->field_b
    //     0xbd4f98: ldur            w1, [x0, #0xb]
    // 0xbd4f9c: DecompressPointer r1
    //     0xbd4f9c: add             x1, x1, HEAP, lsl #32
    // 0xbd4fa0: LoadField: r2 = r1->field_13
    //     0xbd4fa0: ldur            w2, [x1, #0x13]
    // 0xbd4fa4: LoadField: r1 = r0->field_13
    //     0xbd4fa4: ldur            x1, [x0, #0x13]
    // 0xbd4fa8: r3 = LoadInt32Instr(r2)
    //     0xbd4fa8: sbfx            x3, x2, #1, #0x1f
    // 0xbd4fac: sub             x2, x3, x1
    // 0xbd4fb0: cmp             x2, #1
    // 0xbd4fb4: b.ge            #0xbd4fc4
    // 0xbd4fb8: mov             x1, x0
    // 0xbd4fbc: r2 = 1
    //     0xbd4fbc: movz            x2, #0x1
    // 0xbd4fc0: r0 = _increaseBufferSize()
    //     0xbd4fc0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd4fc4: ldur            x2, [fp, #-8]
    // 0xbd4fc8: ldur            x3, [fp, #-0x10]
    // 0xbd4fcc: r4 = 16
    //     0xbd4fcc: movz            x4, #0x10
    // 0xbd4fd0: LoadField: r5 = r2->field_b
    //     0xbd4fd0: ldur            w5, [x2, #0xb]
    // 0xbd4fd4: DecompressPointer r5
    //     0xbd4fd4: add             x5, x5, HEAP, lsl #32
    // 0xbd4fd8: LoadField: r6 = r2->field_13
    //     0xbd4fd8: ldur            x6, [x2, #0x13]
    // 0xbd4fdc: add             x0, x6, #1
    // 0xbd4fe0: StoreField: r2->field_13 = r0
    //     0xbd4fe0: stur            x0, [x2, #0x13]
    // 0xbd4fe4: LoadField: r0 = r5->field_13
    //     0xbd4fe4: ldur            w0, [x5, #0x13]
    // 0xbd4fe8: r1 = LoadInt32Instr(r0)
    //     0xbd4fe8: sbfx            x1, x0, #1, #0x1f
    // 0xbd4fec: mov             x0, x1
    // 0xbd4ff0: mov             x1, x6
    // 0xbd4ff4: cmp             x1, x0
    // 0xbd4ff8: b.hs            #0xbd52f0
    // 0xbd4ffc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd4ffc: add             x0, x5, x6
    //     0xbd5000: strb            w4, [x0, #0x17]
    // 0xbd5004: LoadField: r4 = r3->field_5b
    //     0xbd5004: ldur            x4, [x3, #0x5b]
    // 0xbd5008: r0 = BoxInt64Instr(r4)
    //     0xbd5008: sbfiz           x0, x4, #1, #0x1f
    //     0xbd500c: cmp             x4, x0, asr #1
    //     0xbd5010: b.eq            #0xbd501c
    //     0xbd5014: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd5018: stur            x4, [x0, #7]
    // 0xbd501c: r16 = <int>
    //     0xbd501c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd5020: stp             x2, x16, [SP, #8]
    // 0xbd5024: str             x0, [SP]
    // 0xbd5028: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5028: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd502c: r0 = write()
    //     0xbd502c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5030: ldur            x0, [fp, #-8]
    // 0xbd5034: LoadField: r1 = r0->field_b
    //     0xbd5034: ldur            w1, [x0, #0xb]
    // 0xbd5038: DecompressPointer r1
    //     0xbd5038: add             x1, x1, HEAP, lsl #32
    // 0xbd503c: LoadField: r2 = r1->field_13
    //     0xbd503c: ldur            w2, [x1, #0x13]
    // 0xbd5040: LoadField: r1 = r0->field_13
    //     0xbd5040: ldur            x1, [x0, #0x13]
    // 0xbd5044: r3 = LoadInt32Instr(r2)
    //     0xbd5044: sbfx            x3, x2, #1, #0x1f
    // 0xbd5048: sub             x2, x3, x1
    // 0xbd504c: cmp             x2, #1
    // 0xbd5050: b.ge            #0xbd5060
    // 0xbd5054: mov             x1, x0
    // 0xbd5058: r2 = 1
    //     0xbd5058: movz            x2, #0x1
    // 0xbd505c: r0 = _increaseBufferSize()
    //     0xbd505c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5060: ldur            x2, [fp, #-8]
    // 0xbd5064: ldur            x3, [fp, #-0x10]
    // 0xbd5068: r4 = 17
    //     0xbd5068: movz            x4, #0x11
    // 0xbd506c: LoadField: r5 = r2->field_b
    //     0xbd506c: ldur            w5, [x2, #0xb]
    // 0xbd5070: DecompressPointer r5
    //     0xbd5070: add             x5, x5, HEAP, lsl #32
    // 0xbd5074: LoadField: r6 = r2->field_13
    //     0xbd5074: ldur            x6, [x2, #0x13]
    // 0xbd5078: add             x0, x6, #1
    // 0xbd507c: StoreField: r2->field_13 = r0
    //     0xbd507c: stur            x0, [x2, #0x13]
    // 0xbd5080: LoadField: r0 = r5->field_13
    //     0xbd5080: ldur            w0, [x5, #0x13]
    // 0xbd5084: r1 = LoadInt32Instr(r0)
    //     0xbd5084: sbfx            x1, x0, #1, #0x1f
    // 0xbd5088: mov             x0, x1
    // 0xbd508c: mov             x1, x6
    // 0xbd5090: cmp             x1, x0
    // 0xbd5094: b.hs            #0xbd52f4
    // 0xbd5098: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5098: add             x0, x5, x6
    //     0xbd509c: strb            w4, [x0, #0x17]
    // 0xbd50a0: LoadField: r4 = r3->field_63
    //     0xbd50a0: ldur            x4, [x3, #0x63]
    // 0xbd50a4: r0 = BoxInt64Instr(r4)
    //     0xbd50a4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd50a8: cmp             x4, x0, asr #1
    //     0xbd50ac: b.eq            #0xbd50b8
    //     0xbd50b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd50b4: stur            x4, [x0, #7]
    // 0xbd50b8: r16 = <int>
    //     0xbd50b8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd50bc: stp             x2, x16, [SP, #8]
    // 0xbd50c0: str             x0, [SP]
    // 0xbd50c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd50c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd50c8: r0 = write()
    //     0xbd50c8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd50cc: ldur            x0, [fp, #-8]
    // 0xbd50d0: LoadField: r1 = r0->field_b
    //     0xbd50d0: ldur            w1, [x0, #0xb]
    // 0xbd50d4: DecompressPointer r1
    //     0xbd50d4: add             x1, x1, HEAP, lsl #32
    // 0xbd50d8: LoadField: r2 = r1->field_13
    //     0xbd50d8: ldur            w2, [x1, #0x13]
    // 0xbd50dc: LoadField: r1 = r0->field_13
    //     0xbd50dc: ldur            x1, [x0, #0x13]
    // 0xbd50e0: r3 = LoadInt32Instr(r2)
    //     0xbd50e0: sbfx            x3, x2, #1, #0x1f
    // 0xbd50e4: sub             x2, x3, x1
    // 0xbd50e8: cmp             x2, #1
    // 0xbd50ec: b.ge            #0xbd50fc
    // 0xbd50f0: mov             x1, x0
    // 0xbd50f4: r2 = 1
    //     0xbd50f4: movz            x2, #0x1
    // 0xbd50f8: r0 = _increaseBufferSize()
    //     0xbd50f8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd50fc: ldur            x2, [fp, #-8]
    // 0xbd5100: ldur            x3, [fp, #-0x10]
    // 0xbd5104: r4 = 18
    //     0xbd5104: movz            x4, #0x12
    // 0xbd5108: LoadField: r5 = r2->field_b
    //     0xbd5108: ldur            w5, [x2, #0xb]
    // 0xbd510c: DecompressPointer r5
    //     0xbd510c: add             x5, x5, HEAP, lsl #32
    // 0xbd5110: LoadField: r6 = r2->field_13
    //     0xbd5110: ldur            x6, [x2, #0x13]
    // 0xbd5114: add             x0, x6, #1
    // 0xbd5118: StoreField: r2->field_13 = r0
    //     0xbd5118: stur            x0, [x2, #0x13]
    // 0xbd511c: LoadField: r0 = r5->field_13
    //     0xbd511c: ldur            w0, [x5, #0x13]
    // 0xbd5120: r1 = LoadInt32Instr(r0)
    //     0xbd5120: sbfx            x1, x0, #1, #0x1f
    // 0xbd5124: mov             x0, x1
    // 0xbd5128: mov             x1, x6
    // 0xbd512c: cmp             x1, x0
    // 0xbd5130: b.hs            #0xbd52f8
    // 0xbd5134: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5134: add             x0, x5, x6
    //     0xbd5138: strb            w4, [x0, #0x17]
    // 0xbd513c: LoadField: r4 = r3->field_6b
    //     0xbd513c: ldur            x4, [x3, #0x6b]
    // 0xbd5140: r0 = BoxInt64Instr(r4)
    //     0xbd5140: sbfiz           x0, x4, #1, #0x1f
    //     0xbd5144: cmp             x4, x0, asr #1
    //     0xbd5148: b.eq            #0xbd5154
    //     0xbd514c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd5150: stur            x4, [x0, #7]
    // 0xbd5154: r16 = <int>
    //     0xbd5154: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd5158: stp             x2, x16, [SP, #8]
    // 0xbd515c: str             x0, [SP]
    // 0xbd5160: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5160: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5164: r0 = write()
    //     0xbd5164: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5168: ldur            x0, [fp, #-8]
    // 0xbd516c: LoadField: r1 = r0->field_b
    //     0xbd516c: ldur            w1, [x0, #0xb]
    // 0xbd5170: DecompressPointer r1
    //     0xbd5170: add             x1, x1, HEAP, lsl #32
    // 0xbd5174: LoadField: r2 = r1->field_13
    //     0xbd5174: ldur            w2, [x1, #0x13]
    // 0xbd5178: LoadField: r1 = r0->field_13
    //     0xbd5178: ldur            x1, [x0, #0x13]
    // 0xbd517c: r3 = LoadInt32Instr(r2)
    //     0xbd517c: sbfx            x3, x2, #1, #0x1f
    // 0xbd5180: sub             x2, x3, x1
    // 0xbd5184: cmp             x2, #1
    // 0xbd5188: b.ge            #0xbd5198
    // 0xbd518c: mov             x1, x0
    // 0xbd5190: r2 = 1
    //     0xbd5190: movz            x2, #0x1
    // 0xbd5194: r0 = _increaseBufferSize()
    //     0xbd5194: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5198: ldur            x2, [fp, #-8]
    // 0xbd519c: ldur            x3, [fp, #-0x10]
    // 0xbd51a0: r4 = 19
    //     0xbd51a0: movz            x4, #0x13
    // 0xbd51a4: LoadField: r5 = r2->field_b
    //     0xbd51a4: ldur            w5, [x2, #0xb]
    // 0xbd51a8: DecompressPointer r5
    //     0xbd51a8: add             x5, x5, HEAP, lsl #32
    // 0xbd51ac: LoadField: r6 = r2->field_13
    //     0xbd51ac: ldur            x6, [x2, #0x13]
    // 0xbd51b0: add             x0, x6, #1
    // 0xbd51b4: StoreField: r2->field_13 = r0
    //     0xbd51b4: stur            x0, [x2, #0x13]
    // 0xbd51b8: LoadField: r0 = r5->field_13
    //     0xbd51b8: ldur            w0, [x5, #0x13]
    // 0xbd51bc: r1 = LoadInt32Instr(r0)
    //     0xbd51bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd51c0: mov             x0, x1
    // 0xbd51c4: mov             x1, x6
    // 0xbd51c8: cmp             x1, x0
    // 0xbd51cc: b.hs            #0xbd52fc
    // 0xbd51d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd51d0: add             x0, x5, x6
    //     0xbd51d4: strb            w4, [x0, #0x17]
    // 0xbd51d8: LoadField: r4 = r3->field_73
    //     0xbd51d8: ldur            x4, [x3, #0x73]
    // 0xbd51dc: r0 = BoxInt64Instr(r4)
    //     0xbd51dc: sbfiz           x0, x4, #1, #0x1f
    //     0xbd51e0: cmp             x4, x0, asr #1
    //     0xbd51e4: b.eq            #0xbd51f0
    //     0xbd51e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd51ec: stur            x4, [x0, #7]
    // 0xbd51f0: r16 = <int>
    //     0xbd51f0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd51f4: stp             x2, x16, [SP, #8]
    // 0xbd51f8: str             x0, [SP]
    // 0xbd51fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd51fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5200: r0 = write()
    //     0xbd5200: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5204: ldur            x0, [fp, #-8]
    // 0xbd5208: LoadField: r1 = r0->field_b
    //     0xbd5208: ldur            w1, [x0, #0xb]
    // 0xbd520c: DecompressPointer r1
    //     0xbd520c: add             x1, x1, HEAP, lsl #32
    // 0xbd5210: LoadField: r2 = r1->field_13
    //     0xbd5210: ldur            w2, [x1, #0x13]
    // 0xbd5214: LoadField: r1 = r0->field_13
    //     0xbd5214: ldur            x1, [x0, #0x13]
    // 0xbd5218: r3 = LoadInt32Instr(r2)
    //     0xbd5218: sbfx            x3, x2, #1, #0x1f
    // 0xbd521c: sub             x2, x3, x1
    // 0xbd5220: cmp             x2, #1
    // 0xbd5224: b.ge            #0xbd5234
    // 0xbd5228: mov             x1, x0
    // 0xbd522c: r2 = 1
    //     0xbd522c: movz            x2, #0x1
    // 0xbd5230: r0 = _increaseBufferSize()
    //     0xbd5230: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5234: ldur            x2, [fp, #-8]
    // 0xbd5238: ldur            x3, [fp, #-0x10]
    // 0xbd523c: r4 = 20
    //     0xbd523c: movz            x4, #0x14
    // 0xbd5240: LoadField: r5 = r2->field_b
    //     0xbd5240: ldur            w5, [x2, #0xb]
    // 0xbd5244: DecompressPointer r5
    //     0xbd5244: add             x5, x5, HEAP, lsl #32
    // 0xbd5248: LoadField: r6 = r2->field_13
    //     0xbd5248: ldur            x6, [x2, #0x13]
    // 0xbd524c: add             x0, x6, #1
    // 0xbd5250: StoreField: r2->field_13 = r0
    //     0xbd5250: stur            x0, [x2, #0x13]
    // 0xbd5254: LoadField: r0 = r5->field_13
    //     0xbd5254: ldur            w0, [x5, #0x13]
    // 0xbd5258: r1 = LoadInt32Instr(r0)
    //     0xbd5258: sbfx            x1, x0, #1, #0x1f
    // 0xbd525c: mov             x0, x1
    // 0xbd5260: mov             x1, x6
    // 0xbd5264: cmp             x1, x0
    // 0xbd5268: b.hs            #0xbd5300
    // 0xbd526c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd526c: add             x0, x5, x6
    //     0xbd5270: strb            w4, [x0, #0x17]
    // 0xbd5274: LoadField: r0 = r3->field_7b
    //     0xbd5274: ldur            w0, [x3, #0x7b]
    // 0xbd5278: DecompressPointer r0
    //     0xbd5278: add             x0, x0, HEAP, lsl #32
    // 0xbd527c: r16 = <bool?>
    //     0xbd527c: add             x16, PP, #9, lsl #12  ; [pp+0x9018] TypeArguments: <bool?>
    //     0xbd5280: ldr             x16, [x16, #0x18]
    // 0xbd5284: stp             x2, x16, [SP, #8]
    // 0xbd5288: str             x0, [SP]
    // 0xbd528c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd528c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5290: r0 = write()
    //     0xbd5290: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5294: r0 = Null
    //     0xbd5294: mov             x0, NULL
    // 0xbd5298: LeaveFrame
    //     0xbd5298: mov             SP, fp
    //     0xbd529c: ldp             fp, lr, [SP], #0x10
    // 0xbd52a0: ret
    //     0xbd52a0: ret             
    // 0xbd52a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd52a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd52a8: b               #0xbd45f4
    // 0xbd52ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd52fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd52fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd5300: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd5300: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0240, size: 0x24
    // 0xbf0240: r1 = 52
    //     0xbf0240: movz            x1, #0x34
    // 0xbf0244: r16 = LoadInt32Instr(r1)
    //     0xbf0244: sbfx            x16, x1, #1, #0x1f
    // 0xbf0248: r17 = 11601
    //     0xbf0248: movz            x17, #0x2d51
    // 0xbf024c: mul             x0, x16, x17
    // 0xbf0250: umulh           x16, x16, x17
    // 0xbf0254: eor             x0, x0, x16
    // 0xbf0258: r0 = 0
    //     0xbf0258: eor             x0, x0, x0, lsr #32
    // 0xbf025c: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0260: ret
    //     0xbf0260: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76a34, size: 0x9c
    // 0xd76a34: EnterFrame
    //     0xd76a34: stp             fp, lr, [SP, #-0x10]!
    //     0xd76a38: mov             fp, SP
    // 0xd76a3c: AllocStack(0x10)
    //     0xd76a3c: sub             SP, SP, #0x10
    // 0xd76a40: CheckStackOverflow
    //     0xd76a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76a44: cmp             SP, x16
    //     0xd76a48: b.ls            #0xd76ac8
    // 0xd76a4c: ldr             x0, [fp, #0x10]
    // 0xd76a50: cmp             w0, NULL
    // 0xd76a54: b.ne            #0xd76a68
    // 0xd76a58: r0 = false
    //     0xd76a58: add             x0, NULL, #0x30  ; false
    // 0xd76a5c: LeaveFrame
    //     0xd76a5c: mov             SP, fp
    //     0xd76a60: ldp             fp, lr, [SP], #0x10
    // 0xd76a64: ret
    //     0xd76a64: ret             
    // 0xd76a68: ldr             x1, [fp, #0x18]
    // 0xd76a6c: cmp             w1, w0
    // 0xd76a70: b.ne            #0xd76a7c
    // 0xd76a74: r0 = true
    //     0xd76a74: add             x0, NULL, #0x20  ; true
    // 0xd76a78: b               #0xd76abc
    // 0xd76a7c: r1 = 60
    //     0xd76a7c: movz            x1, #0x3c
    // 0xd76a80: branchIfSmi(r0, 0xd76a8c)
    //     0xd76a80: tbz             w0, #0, #0xd76a8c
    // 0xd76a84: r1 = LoadClassIdInstr(r0)
    //     0xd76a84: ldur            x1, [x0, #-1]
    //     0xd76a88: ubfx            x1, x1, #0xc, #0x14
    // 0xd76a8c: cmp             x1, #0x676
    // 0xd76a90: b.ne            #0xd76ab8
    // 0xd76a94: r16 = PrayerTimeNotificationSettingAdapter
    //     0xd76a94: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2a0] Type: PrayerTimeNotificationSettingAdapter
    //     0xd76a98: ldr             x16, [x16, #0x2a0]
    // 0xd76a9c: r30 = PrayerTimeNotificationSettingAdapter
    //     0xd76a9c: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b2a0] Type: PrayerTimeNotificationSettingAdapter
    //     0xd76aa0: ldr             lr, [lr, #0x2a0]
    // 0xd76aa4: stp             lr, x16, [SP]
    // 0xd76aa8: r0 = ==()
    //     0xd76aa8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76aac: tbnz            w0, #4, #0xd76ab8
    // 0xd76ab0: r0 = true
    //     0xd76ab0: add             x0, NULL, #0x20  ; true
    // 0xd76ab4: b               #0xd76abc
    // 0xd76ab8: r0 = false
    //     0xd76ab8: add             x0, NULL, #0x30  ; false
    // 0xd76abc: LeaveFrame
    //     0xd76abc: mov             SP, fp
    //     0xd76ac0: ldp             fp, lr, [SP], #0x10
    // 0xd76ac4: ret
    //     0xd76ac4: ret             
    // 0xd76ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76ac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76acc: b               #0xd76a4c
  }
}

// class id: 6836, size: 0x14, field offset: 0x14
enum NotificationSound extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d2a8, size: 0x64
    // 0xc4d2a8: EnterFrame
    //     0xc4d2a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d2ac: mov             fp, SP
    // 0xc4d2b0: AllocStack(0x10)
    //     0xc4d2b0: sub             SP, SP, #0x10
    // 0xc4d2b4: SetupParameters(NotificationSound this /* r1 => r0, fp-0x8 */)
    //     0xc4d2b4: mov             x0, x1
    //     0xc4d2b8: stur            x1, [fp, #-8]
    // 0xc4d2bc: CheckStackOverflow
    //     0xc4d2bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d2c0: cmp             SP, x16
    //     0xc4d2c4: b.ls            #0xc4d304
    // 0xc4d2c8: r1 = Null
    //     0xc4d2c8: mov             x1, NULL
    // 0xc4d2cc: r2 = 4
    //     0xc4d2cc: movz            x2, #0x4
    // 0xc4d2d0: r0 = AllocateArray()
    //     0xc4d2d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d2d4: r16 = "NotificationSound."
    //     0xc4d2d4: add             x16, PP, #0x21, lsl #12  ; [pp+0x21068] "NotificationSound."
    //     0xc4d2d8: ldr             x16, [x16, #0x68]
    // 0xc4d2dc: StoreField: r0->field_f = r16
    //     0xc4d2dc: stur            w16, [x0, #0xf]
    // 0xc4d2e0: ldur            x1, [fp, #-8]
    // 0xc4d2e4: LoadField: r2 = r1->field_f
    //     0xc4d2e4: ldur            w2, [x1, #0xf]
    // 0xc4d2e8: DecompressPointer r2
    //     0xc4d2e8: add             x2, x2, HEAP, lsl #32
    // 0xc4d2ec: StoreField: r0->field_13 = r2
    //     0xc4d2ec: stur            w2, [x0, #0x13]
    // 0xc4d2f0: str             x0, [SP]
    // 0xc4d2f4: r0 = _interpolate()
    //     0xc4d2f4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d2f8: LeaveFrame
    //     0xc4d2f8: mov             SP, fp
    //     0xc4d2fc: ldp             fp, lr, [SP], #0x10
    // 0xc4d300: ret
    //     0xc4d300: ret             
    // 0xc4d304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d308: b               #0xc4d2c8
  }
}
