// lib: , url: package:nuonline/app/data/models/prayer_time_setting.dart

// class id: 1050039, size: 0x8
class :: {
}

// class id: 1603, size: 0x6c, field offset: 0x14
class PrayerTimeSetting extends HiveObject {

  get _ madhab(/* No info */) {
    // ** addr: 0x82ffbc, size: 0x5c
    // 0x82ffbc: EnterFrame
    //     0x82ffbc: stp             fp, lr, [SP, #-0x10]!
    //     0x82ffc0: mov             fp, SP
    // 0x82ffc4: AllocStack(0x10)
    //     0x82ffc4: sub             SP, SP, #0x10
    // 0x82ffc8: CheckStackOverflow
    //     0x82ffc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82ffcc: cmp             SP, x16
    //     0x82ffd0: b.ls            #0x830010
    // 0x82ffd4: LoadField: r0 = r1->field_3b
    //     0x82ffd4: ldur            w0, [x1, #0x3b]
    // 0x82ffd8: DecompressPointer r0
    //     0x82ffd8: add             x0, x0, HEAP, lsl #32
    // 0x82ffdc: r16 = "hanafi"
    //     0x82ffdc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb8c8] "hanafi"
    //     0x82ffe0: ldr             x16, [x16, #0x8c8]
    // 0x82ffe4: stp             x0, x16, [SP]
    // 0x82ffe8: r0 = ==()
    //     0x82ffe8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x82ffec: tbnz            w0, #4, #0x82fffc
    // 0x82fff0: r0 = Instance_Madhab
    //     0x82fff0: add             x0, PP, #0xb, lsl #12  ; [pp+0xb8d0] Obj!Madhab@e38e41
    //     0x82fff4: ldr             x0, [x0, #0x8d0]
    // 0x82fff8: b               #0x830004
    // 0x82fffc: r0 = Instance_Madhab
    //     0x82fffc: add             x0, PP, #0xb, lsl #12  ; [pp+0xb8d8] Obj!Madhab@e38e21
    //     0x830000: ldr             x0, [x0, #0x8d8]
    // 0x830004: LeaveFrame
    //     0x830004: mov             SP, fp
    //     0x830008: ldp             fp, lr, [SP], #0x10
    // 0x83000c: ret
    //     0x83000c: ret             
    // 0x830010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x830010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x830014: b               #0x82ffd4
  }
  get _ allowCustomIshaInterval(/* No info */) {
    // ** addr: 0x830150, size: 0xac
    // 0x830150: EnterFrame
    //     0x830150: stp             fp, lr, [SP, #-0x10]!
    //     0x830154: mov             fp, SP
    // 0x830158: AllocStack(0x18)
    //     0x830158: sub             SP, SP, #0x18
    // 0x83015c: r0 = 6
    //     0x83015c: movz            x0, #0x6
    // 0x830160: mov             x3, x1
    // 0x830164: stur            x1, [fp, #-8]
    // 0x830168: CheckStackOverflow
    //     0x830168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83016c: cmp             SP, x16
    //     0x830170: b.ls            #0x8301f4
    // 0x830174: mov             x2, x0
    // 0x830178: r1 = Null
    //     0x830178: mov             x1, NULL
    // 0x83017c: r0 = AllocateArray()
    //     0x83017c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x830180: stur            x0, [fp, #-0x10]
    // 0x830184: r16 = Instance_CalculationMethod
    //     0x830184: add             x16, PP, #0xb, lsl #12  ; [pp+0xb8e8] Obj!CalculationMethod@e39101
    //     0x830188: ldr             x16, [x16, #0x8e8]
    // 0x83018c: StoreField: r0->field_f = r16
    //     0x83018c: stur            w16, [x0, #0xf]
    // 0x830190: r16 = Instance_CalculationMethod
    //     0x830190: add             x16, PP, #0xb, lsl #12  ; [pp+0xb8f0] Obj!CalculationMethod@e390e1
    //     0x830194: ldr             x16, [x16, #0x8f0]
    // 0x830198: StoreField: r0->field_13 = r16
    //     0x830198: stur            w16, [x0, #0x13]
    // 0x83019c: r16 = Instance_CalculationMethod
    //     0x83019c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb8f8] Obj!CalculationMethod@e390c1
    //     0x8301a0: ldr             x16, [x16, #0x8f8]
    // 0x8301a4: ArrayStore: r0[0] = r16  ; List_4
    //     0x8301a4: stur            w16, [x0, #0x17]
    // 0x8301a8: r1 = <CalculationMethod>
    //     0x8301a8: add             x1, PP, #0xb, lsl #12  ; [pp+0xb900] TypeArguments: <CalculationMethod>
    //     0x8301ac: ldr             x1, [x1, #0x900]
    // 0x8301b0: r0 = AllocateGrowableArray()
    //     0x8301b0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8301b4: mov             x2, x0
    // 0x8301b8: ldur            x0, [fp, #-0x10]
    // 0x8301bc: stur            x2, [fp, #-0x18]
    // 0x8301c0: StoreField: r2->field_f = r0
    //     0x8301c0: stur            w0, [x2, #0xf]
    // 0x8301c4: r0 = 6
    //     0x8301c4: movz            x0, #0x6
    // 0x8301c8: StoreField: r2->field_b = r0
    //     0x8301c8: stur            w0, [x2, #0xb]
    // 0x8301cc: ldur            x0, [fp, #-8]
    // 0x8301d0: LoadField: r1 = r0->field_1b
    //     0x8301d0: ldur            w1, [x0, #0x1b]
    // 0x8301d4: DecompressPointer r1
    //     0x8301d4: add             x1, x1, HEAP, lsl #32
    // 0x8301d8: r0 = getCalculationMethodFromString()
    //     0x8301d8: bl              #0x8301fc  ; [package:adhan/src/calculation_method.dart] ::getCalculationMethodFromString
    // 0x8301dc: ldur            x1, [fp, #-0x18]
    // 0x8301e0: mov             x2, x0
    // 0x8301e4: r0 = contains()
    //     0x8301e4: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x8301e8: LeaveFrame
    //     0x8301e8: mov             SP, fp
    //     0x8301ec: ldp             fp, lr, [SP], #0x10
    // 0x8301f0: ret
    //     0x8301f0: ret             
    // 0x8301f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8301f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8301f8: b               #0x830174
  }
  get _ allowCustomAngle(/* No info */) {
    // ** addr: 0x8304bc, size: 0x54
    // 0x8304bc: EnterFrame
    //     0x8304bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8304c0: mov             fp, SP
    // 0x8304c4: CheckStackOverflow
    //     0x8304c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8304c8: cmp             SP, x16
    //     0x8304cc: b.ls            #0x830508
    // 0x8304d0: LoadField: r0 = r1->field_1b
    //     0x8304d0: ldur            w0, [x1, #0x1b]
    // 0x8304d4: DecompressPointer r0
    //     0x8304d4: add             x0, x0, HEAP, lsl #32
    // 0x8304d8: mov             x1, x0
    // 0x8304dc: r0 = getCalculationMethodFromString()
    //     0x8304dc: bl              #0x8301fc  ; [package:adhan/src/calculation_method.dart] ::getCalculationMethodFromString
    // 0x8304e0: r16 = Instance_CalculationMethod
    //     0x8304e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb920] Obj!CalculationMethod@e38ee1
    //     0x8304e4: ldr             x16, [x16, #0x920]
    // 0x8304e8: cmp             w0, w16
    // 0x8304ec: r16 = true
    //     0x8304ec: add             x16, NULL, #0x20  ; true
    // 0x8304f0: r17 = false
    //     0x8304f0: add             x17, NULL, #0x30  ; false
    // 0x8304f4: csel            x1, x16, x17, eq
    // 0x8304f8: mov             x0, x1
    // 0x8304fc: LeaveFrame
    //     0x8304fc: mov             SP, fp
    //     0x830500: ldp             fp, lr, [SP], #0x10
    // 0x830504: ret
    //     0x830504: ret             
    // 0x830508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x830508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83050c: b               #0x8304d0
  }
  get _ method(/* No info */) {
    // ** addr: 0x830fcc, size: 0x38
    // 0x830fcc: EnterFrame
    //     0x830fcc: stp             fp, lr, [SP, #-0x10]!
    //     0x830fd0: mov             fp, SP
    // 0x830fd4: CheckStackOverflow
    //     0x830fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x830fd8: cmp             SP, x16
    //     0x830fdc: b.ls            #0x830ffc
    // 0x830fe0: LoadField: r0 = r1->field_1b
    //     0x830fe0: ldur            w0, [x1, #0x1b]
    // 0x830fe4: DecompressPointer r0
    //     0x830fe4: add             x0, x0, HEAP, lsl #32
    // 0x830fe8: mov             x1, x0
    // 0x830fec: r0 = getCalculationMethodFromString()
    //     0x830fec: bl              #0x8301fc  ; [package:adhan/src/calculation_method.dart] ::getCalculationMethodFromString
    // 0x830ff0: LeaveFrame
    //     0x830ff0: mov             SP, fp
    //     0x830ff4: ldp             fp, lr, [SP], #0x10
    // 0x830ff8: ret
    //     0x830ff8: ret             
    // 0x830ffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x830ffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x831000: b               #0x830fe0
  }
  factory _ PrayerTimeSetting.fromCountry(/* No info */) {
    // ** addr: 0x8a6a80, size: 0x780
    // 0x8a6a80: EnterFrame
    //     0x8a6a80: stp             fp, lr, [SP, #-0x10]!
    //     0x8a6a84: mov             fp, SP
    // 0x8a6a88: AllocStack(0x18)
    //     0x8a6a88: sub             SP, SP, #0x18
    // 0x8a6a8c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8a6a8c: stur            x2, [fp, #-8]
    // 0x8a6a90: CheckStackOverflow
    //     0x8a6a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a6a94: cmp             SP, x16
    //     0x8a6a98: b.ls            #0x8a71f8
    // 0x8a6a9c: r16 = "ID"
    //     0x8a6a9c: add             x16, PP, #8, lsl #12  ; [pp+0x8f40] "ID"
    //     0x8a6aa0: ldr             x16, [x16, #0xf40]
    // 0x8a6aa4: stp             x2, x16, [SP]
    // 0x8a6aa8: r0 = ==()
    //     0x8a6aa8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6aac: tbz             w0, #4, #0x8a6ac8
    // 0x8a6ab0: r16 = "BN"
    //     0x8a6ab0: add             x16, PP, #8, lsl #12  ; [pp+0x8f50] "BN"
    //     0x8a6ab4: ldr             x16, [x16, #0xf50]
    // 0x8a6ab8: ldur            lr, [fp, #-8]
    // 0x8a6abc: stp             lr, x16, [SP]
    // 0x8a6ac0: r0 = ==()
    //     0x8a6ac0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6ac4: tbnz            w0, #4, #0x8a6ae4
    // 0x8a6ac8: r1 = Null
    //     0x8a6ac8: mov             x1, NULL
    // 0x8a6acc: r2 = "falakiyah_nu"
    //     0x8a6acc: add             x2, PP, #0xf, lsl #12  ; [pp+0xf300] "falakiyah_nu"
    //     0x8a6ad0: ldr             x2, [x2, #0x300]
    // 0x8a6ad4: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6ad4: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6ad8: LeaveFrame
    //     0x8a6ad8: mov             SP, fp
    //     0x8a6adc: ldp             fp, lr, [SP], #0x10
    // 0x8a6ae0: ret
    //     0x8a6ae0: ret             
    // 0x8a6ae4: r16 = "SA"
    //     0x8a6ae4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf308] "SA"
    //     0x8a6ae8: ldr             x16, [x16, #0x308]
    // 0x8a6aec: ldur            lr, [fp, #-8]
    // 0x8a6af0: stp             lr, x16, [SP]
    // 0x8a6af4: r0 = ==()
    //     0x8a6af4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6af8: tbnz            w0, #4, #0x8a6b18
    // 0x8a6afc: r1 = Null
    //     0x8a6afc: mov             x1, NULL
    // 0x8a6b00: r2 = "umm_al_qura"
    //     0x8a6b00: add             x2, PP, #0xf, lsl #12  ; [pp+0xf310] "umm_al_qura"
    //     0x8a6b04: ldr             x2, [x2, #0x310]
    // 0x8a6b08: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6b08: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6b0c: LeaveFrame
    //     0x8a6b0c: mov             SP, fp
    //     0x8a6b10: ldp             fp, lr, [SP], #0x10
    // 0x8a6b14: ret
    //     0x8a6b14: ret             
    // 0x8a6b18: r16 = "MY"
    //     0x8a6b18: add             x16, PP, #8, lsl #12  ; [pp+0x8f48] "MY"
    //     0x8a6b1c: ldr             x16, [x16, #0xf48]
    // 0x8a6b20: ldur            lr, [fp, #-8]
    // 0x8a6b24: stp             lr, x16, [SP]
    // 0x8a6b28: r0 = ==()
    //     0x8a6b28: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6b2c: tbnz            w0, #4, #0x8a6b4c
    // 0x8a6b30: r1 = Null
    //     0x8a6b30: mov             x1, NULL
    // 0x8a6b34: r2 = "jakim"
    //     0x8a6b34: add             x2, PP, #0xf, lsl #12  ; [pp+0xf318] "jakim"
    //     0x8a6b38: ldr             x2, [x2, #0x318]
    // 0x8a6b3c: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6b3c: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6b40: LeaveFrame
    //     0x8a6b40: mov             SP, fp
    //     0x8a6b44: ldp             fp, lr, [SP], #0x10
    // 0x8a6b48: ret
    //     0x8a6b48: ret             
    // 0x8a6b4c: r16 = "SG"
    //     0x8a6b4c: add             x16, PP, #8, lsl #12  ; [pp+0x8f60] "SG"
    //     0x8a6b50: ldr             x16, [x16, #0xf60]
    // 0x8a6b54: ldur            lr, [fp, #-8]
    // 0x8a6b58: stp             lr, x16, [SP]
    // 0x8a6b5c: r0 = ==()
    //     0x8a6b5c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6b60: tbnz            w0, #4, #0x8a6b80
    // 0x8a6b64: r1 = Null
    //     0x8a6b64: mov             x1, NULL
    // 0x8a6b68: r2 = "singapore"
    //     0x8a6b68: add             x2, PP, #0xf, lsl #12  ; [pp+0xf320] "singapore"
    //     0x8a6b6c: ldr             x2, [x2, #0x320]
    // 0x8a6b70: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6b70: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6b74: LeaveFrame
    //     0x8a6b74: mov             SP, fp
    //     0x8a6b78: ldp             fp, lr, [SP], #0x10
    // 0x8a6b7c: ret
    //     0x8a6b7c: ret             
    // 0x8a6b80: r16 = "TH"
    //     0x8a6b80: add             x16, PP, #0xf, lsl #12  ; [pp+0xf328] "TH"
    //     0x8a6b84: ldr             x16, [x16, #0x328]
    // 0x8a6b88: ldur            lr, [fp, #-8]
    // 0x8a6b8c: stp             lr, x16, [SP]
    // 0x8a6b90: r0 = ==()
    //     0x8a6b90: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6b94: tbnz            w0, #4, #0x8a6bb4
    // 0x8a6b98: r1 = Null
    //     0x8a6b98: mov             x1, NULL
    // 0x8a6b9c: r2 = "thailand"
    //     0x8a6b9c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf330] "thailand"
    //     0x8a6ba0: ldr             x2, [x2, #0x330]
    // 0x8a6ba4: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6ba4: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6ba8: LeaveFrame
    //     0x8a6ba8: mov             SP, fp
    //     0x8a6bac: ldp             fp, lr, [SP], #0x10
    // 0x8a6bb0: ret
    //     0x8a6bb0: ret             
    // 0x8a6bb4: r16 = "EG"
    //     0x8a6bb4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf338] "EG"
    //     0x8a6bb8: ldr             x16, [x16, #0x338]
    // 0x8a6bbc: ldur            lr, [fp, #-8]
    // 0x8a6bc0: stp             lr, x16, [SP]
    // 0x8a6bc4: r0 = ==()
    //     0x8a6bc4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6bc8: tbnz            w0, #4, #0x8a6be8
    // 0x8a6bcc: r1 = Null
    //     0x8a6bcc: mov             x1, NULL
    // 0x8a6bd0: r2 = "egyptian"
    //     0x8a6bd0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf340] "egyptian"
    //     0x8a6bd4: ldr             x2, [x2, #0x340]
    // 0x8a6bd8: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6bd8: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6bdc: LeaveFrame
    //     0x8a6bdc: mov             SP, fp
    //     0x8a6be0: ldp             fp, lr, [SP], #0x10
    // 0x8a6be4: ret
    //     0x8a6be4: ret             
    // 0x8a6be8: r16 = "IQ"
    //     0x8a6be8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf348] "IQ"
    //     0x8a6bec: ldr             x16, [x16, #0x348]
    // 0x8a6bf0: ldur            lr, [fp, #-8]
    // 0x8a6bf4: stp             lr, x16, [SP]
    // 0x8a6bf8: r0 = ==()
    //     0x8a6bf8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6bfc: tbz             w0, #4, #0x8a6c30
    // 0x8a6c00: r16 = "LB"
    //     0x8a6c00: add             x16, PP, #0xf, lsl #12  ; [pp+0xf350] "LB"
    //     0x8a6c04: ldr             x16, [x16, #0x350]
    // 0x8a6c08: ldur            lr, [fp, #-8]
    // 0x8a6c0c: stp             lr, x16, [SP]
    // 0x8a6c10: r0 = ==()
    //     0x8a6c10: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6c14: tbz             w0, #4, #0x8a6c30
    // 0x8a6c18: r16 = "SY"
    //     0x8a6c18: add             x16, PP, #0xf, lsl #12  ; [pp+0xf358] "SY"
    //     0x8a6c1c: ldr             x16, [x16, #0x358]
    // 0x8a6c20: ldur            lr, [fp, #-8]
    // 0x8a6c24: stp             lr, x16, [SP]
    // 0x8a6c28: r0 = ==()
    //     0x8a6c28: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6c2c: tbnz            w0, #4, #0x8a6c4c
    // 0x8a6c30: r1 = Null
    //     0x8a6c30: mov             x1, NULL
    // 0x8a6c34: r2 = "egyptian_bis"
    //     0x8a6c34: add             x2, PP, #0xf, lsl #12  ; [pp+0xf360] "egyptian_bis"
    //     0x8a6c38: ldr             x2, [x2, #0x360]
    // 0x8a6c3c: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6c3c: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6c40: LeaveFrame
    //     0x8a6c40: mov             SP, fp
    //     0x8a6c44: ldp             fp, lr, [SP], #0x10
    // 0x8a6c48: ret
    //     0x8a6c48: ret             
    // 0x8a6c4c: r16 = "KW"
    //     0x8a6c4c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf368] "KW"
    //     0x8a6c50: ldr             x16, [x16, #0x368]
    // 0x8a6c54: ldur            lr, [fp, #-8]
    // 0x8a6c58: stp             lr, x16, [SP]
    // 0x8a6c5c: r0 = ==()
    //     0x8a6c5c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6c60: tbnz            w0, #4, #0x8a6c80
    // 0x8a6c64: r1 = Null
    //     0x8a6c64: mov             x1, NULL
    // 0x8a6c68: r2 = "kuwait"
    //     0x8a6c68: add             x2, PP, #0xf, lsl #12  ; [pp+0xf370] "kuwait"
    //     0x8a6c6c: ldr             x2, [x2, #0x370]
    // 0x8a6c70: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6c70: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6c74: LeaveFrame
    //     0x8a6c74: mov             SP, fp
    //     0x8a6c78: ldp             fp, lr, [SP], #0x10
    // 0x8a6c7c: ret
    //     0x8a6c7c: ret             
    // 0x8a6c80: r16 = "QA"
    //     0x8a6c80: add             x16, PP, #0xf, lsl #12  ; [pp+0xf378] "QA"
    //     0x8a6c84: ldr             x16, [x16, #0x378]
    // 0x8a6c88: ldur            lr, [fp, #-8]
    // 0x8a6c8c: stp             lr, x16, [SP]
    // 0x8a6c90: r0 = ==()
    //     0x8a6c90: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6c94: tbnz            w0, #4, #0x8a6cb4
    // 0x8a6c98: r1 = Null
    //     0x8a6c98: mov             x1, NULL
    // 0x8a6c9c: r2 = "qatar"
    //     0x8a6c9c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf380] "qatar"
    //     0x8a6ca0: ldr             x2, [x2, #0x380]
    // 0x8a6ca4: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6ca4: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6ca8: LeaveFrame
    //     0x8a6ca8: mov             SP, fp
    //     0x8a6cac: ldp             fp, lr, [SP], #0x10
    // 0x8a6cb0: ret
    //     0x8a6cb0: ret             
    // 0x8a6cb4: r16 = "BH"
    //     0x8a6cb4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf388] "BH"
    //     0x8a6cb8: ldr             x16, [x16, #0x388]
    // 0x8a6cbc: ldur            lr, [fp, #-8]
    // 0x8a6cc0: stp             lr, x16, [SP]
    // 0x8a6cc4: r0 = ==()
    //     0x8a6cc4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6cc8: tbnz            w0, #4, #0x8a6ce8
    // 0x8a6ccc: r1 = Null
    //     0x8a6ccc: mov             x1, NULL
    // 0x8a6cd0: r2 = "bahrain"
    //     0x8a6cd0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf390] "bahrain"
    //     0x8a6cd4: ldr             x2, [x2, #0x390]
    // 0x8a6cd8: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a6cd8: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a6cdc: LeaveFrame
    //     0x8a6cdc: mov             SP, fp
    //     0x8a6ce0: ldp             fp, lr, [SP], #0x10
    // 0x8a6ce4: ret
    //     0x8a6ce4: ret             
    // 0x8a6ce8: r16 = "US"
    //     0x8a6ce8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf398] "US"
    //     0x8a6cec: ldr             x16, [x16, #0x398]
    // 0x8a6cf0: ldur            lr, [fp, #-8]
    // 0x8a6cf4: stp             lr, x16, [SP]
    // 0x8a6cf8: r0 = ==()
    //     0x8a6cf8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6cfc: tbz             w0, #4, #0x8a7090
    // 0x8a6d00: r16 = "AI"
    //     0x8a6d00: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3a0] "AI"
    //     0x8a6d04: ldr             x16, [x16, #0x3a0]
    // 0x8a6d08: ldur            lr, [fp, #-8]
    // 0x8a6d0c: stp             lr, x16, [SP]
    // 0x8a6d10: r0 = ==()
    //     0x8a6d10: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d14: tbz             w0, #4, #0x8a7090
    // 0x8a6d18: r16 = "AG"
    //     0x8a6d18: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3a8] "AG"
    //     0x8a6d1c: ldr             x16, [x16, #0x3a8]
    // 0x8a6d20: ldur            lr, [fp, #-8]
    // 0x8a6d24: stp             lr, x16, [SP]
    // 0x8a6d28: r0 = ==()
    //     0x8a6d28: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d2c: tbz             w0, #4, #0x8a7090
    // 0x8a6d30: r16 = "AW"
    //     0x8a6d30: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3b0] "AW"
    //     0x8a6d34: ldr             x16, [x16, #0x3b0]
    // 0x8a6d38: ldur            lr, [fp, #-8]
    // 0x8a6d3c: stp             lr, x16, [SP]
    // 0x8a6d40: r0 = ==()
    //     0x8a6d40: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d44: tbz             w0, #4, #0x8a7090
    // 0x8a6d48: r16 = "AN"
    //     0x8a6d48: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3b8] "AN"
    //     0x8a6d4c: ldr             x16, [x16, #0x3b8]
    // 0x8a6d50: ldur            lr, [fp, #-8]
    // 0x8a6d54: stp             lr, x16, [SP]
    // 0x8a6d58: r0 = ==()
    //     0x8a6d58: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d5c: tbz             w0, #4, #0x8a7090
    // 0x8a6d60: r16 = "BS"
    //     0x8a6d60: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3c0] "BS"
    //     0x8a6d64: ldr             x16, [x16, #0x3c0]
    // 0x8a6d68: ldur            lr, [fp, #-8]
    // 0x8a6d6c: stp             lr, x16, [SP]
    // 0x8a6d70: r0 = ==()
    //     0x8a6d70: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d74: tbz             w0, #4, #0x8a7090
    // 0x8a6d78: r16 = "BB"
    //     0x8a6d78: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3c8] "BB"
    //     0x8a6d7c: ldr             x16, [x16, #0x3c8]
    // 0x8a6d80: ldur            lr, [fp, #-8]
    // 0x8a6d84: stp             lr, x16, [SP]
    // 0x8a6d88: r0 = ==()
    //     0x8a6d88: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6d8c: tbz             w0, #4, #0x8a7090
    // 0x8a6d90: r16 = "BZ"
    //     0x8a6d90: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3d0] "BZ"
    //     0x8a6d94: ldr             x16, [x16, #0x3d0]
    // 0x8a6d98: ldur            lr, [fp, #-8]
    // 0x8a6d9c: stp             lr, x16, [SP]
    // 0x8a6da0: r0 = ==()
    //     0x8a6da0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6da4: tbz             w0, #4, #0x8a7090
    // 0x8a6da8: r16 = "BM"
    //     0x8a6da8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3d8] "BM"
    //     0x8a6dac: ldr             x16, [x16, #0x3d8]
    // 0x8a6db0: ldur            lr, [fp, #-8]
    // 0x8a6db4: stp             lr, x16, [SP]
    // 0x8a6db8: r0 = ==()
    //     0x8a6db8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6dbc: tbz             w0, #4, #0x8a7090
    // 0x8a6dc0: r16 = "KY"
    //     0x8a6dc0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3e0] "KY"
    //     0x8a6dc4: ldr             x16, [x16, #0x3e0]
    // 0x8a6dc8: ldur            lr, [fp, #-8]
    // 0x8a6dcc: stp             lr, x16, [SP]
    // 0x8a6dd0: r0 = ==()
    //     0x8a6dd0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6dd4: tbz             w0, #4, #0x8a7090
    // 0x8a6dd8: r16 = "DM"
    //     0x8a6dd8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3e8] "DM"
    //     0x8a6ddc: ldr             x16, [x16, #0x3e8]
    // 0x8a6de0: ldur            lr, [fp, #-8]
    // 0x8a6de4: stp             lr, x16, [SP]
    // 0x8a6de8: r0 = ==()
    //     0x8a6de8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6dec: tbz             w0, #4, #0x8a7090
    // 0x8a6df0: r16 = "DO"
    //     0x8a6df0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3f0] "DO"
    //     0x8a6df4: ldr             x16, [x16, #0x3f0]
    // 0x8a6df8: ldur            lr, [fp, #-8]
    // 0x8a6dfc: stp             lr, x16, [SP]
    // 0x8a6e00: r0 = ==()
    //     0x8a6e00: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e04: tbz             w0, #4, #0x8a7090
    // 0x8a6e08: r16 = "SV"
    //     0x8a6e08: add             x16, PP, #0xf, lsl #12  ; [pp+0xf3f8] "SV"
    //     0x8a6e0c: ldr             x16, [x16, #0x3f8]
    // 0x8a6e10: ldur            lr, [fp, #-8]
    // 0x8a6e14: stp             lr, x16, [SP]
    // 0x8a6e18: r0 = ==()
    //     0x8a6e18: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e1c: tbz             w0, #4, #0x8a7090
    // 0x8a6e20: r16 = "GL"
    //     0x8a6e20: add             x16, PP, #0xf, lsl #12  ; [pp+0xf400] "GL"
    //     0x8a6e24: ldr             x16, [x16, #0x400]
    // 0x8a6e28: ldur            lr, [fp, #-8]
    // 0x8a6e2c: stp             lr, x16, [SP]
    // 0x8a6e30: r0 = ==()
    //     0x8a6e30: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e34: tbz             w0, #4, #0x8a7090
    // 0x8a6e38: r16 = "GD"
    //     0x8a6e38: add             x16, PP, #0xf, lsl #12  ; [pp+0xf408] "GD"
    //     0x8a6e3c: ldr             x16, [x16, #0x408]
    // 0x8a6e40: ldur            lr, [fp, #-8]
    // 0x8a6e44: stp             lr, x16, [SP]
    // 0x8a6e48: r0 = ==()
    //     0x8a6e48: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e4c: tbz             w0, #4, #0x8a7090
    // 0x8a6e50: r16 = "GP"
    //     0x8a6e50: add             x16, PP, #0xf, lsl #12  ; [pp+0xf410] "GP"
    //     0x8a6e54: ldr             x16, [x16, #0x410]
    // 0x8a6e58: ldur            lr, [fp, #-8]
    // 0x8a6e5c: stp             lr, x16, [SP]
    // 0x8a6e60: r0 = ==()
    //     0x8a6e60: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e64: tbz             w0, #4, #0x8a7090
    // 0x8a6e68: r16 = "GT"
    //     0x8a6e68: add             x16, PP, #0xf, lsl #12  ; [pp+0xf418] "GT"
    //     0x8a6e6c: ldr             x16, [x16, #0x418]
    // 0x8a6e70: ldur            lr, [fp, #-8]
    // 0x8a6e74: stp             lr, x16, [SP]
    // 0x8a6e78: r0 = ==()
    //     0x8a6e78: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e7c: tbz             w0, #4, #0x8a7090
    // 0x8a6e80: r16 = "HT"
    //     0x8a6e80: add             x16, PP, #0xf, lsl #12  ; [pp+0xf420] "HT"
    //     0x8a6e84: ldr             x16, [x16, #0x420]
    // 0x8a6e88: ldur            lr, [fp, #-8]
    // 0x8a6e8c: stp             lr, x16, [SP]
    // 0x8a6e90: r0 = ==()
    //     0x8a6e90: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6e94: tbz             w0, #4, #0x8a7090
    // 0x8a6e98: r16 = "HN"
    //     0x8a6e98: add             x16, PP, #0xf, lsl #12  ; [pp+0xf428] "HN"
    //     0x8a6e9c: ldr             x16, [x16, #0x428]
    // 0x8a6ea0: ldur            lr, [fp, #-8]
    // 0x8a6ea4: stp             lr, x16, [SP]
    // 0x8a6ea8: r0 = ==()
    //     0x8a6ea8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6eac: tbz             w0, #4, #0x8a7090
    // 0x8a6eb0: r16 = "JM"
    //     0x8a6eb0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf430] "JM"
    //     0x8a6eb4: ldr             x16, [x16, #0x430]
    // 0x8a6eb8: ldur            lr, [fp, #-8]
    // 0x8a6ebc: stp             lr, x16, [SP]
    // 0x8a6ec0: r0 = ==()
    //     0x8a6ec0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6ec4: tbz             w0, #4, #0x8a7090
    // 0x8a6ec8: r16 = "CA"
    //     0x8a6ec8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf438] "CA"
    //     0x8a6ecc: ldr             x16, [x16, #0x438]
    // 0x8a6ed0: ldur            lr, [fp, #-8]
    // 0x8a6ed4: stp             lr, x16, [SP]
    // 0x8a6ed8: r0 = ==()
    //     0x8a6ed8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6edc: tbz             w0, #4, #0x8a7090
    // 0x8a6ee0: r16 = "CR"
    //     0x8a6ee0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf440] "CR"
    //     0x8a6ee4: ldr             x16, [x16, #0x440]
    // 0x8a6ee8: ldur            lr, [fp, #-8]
    // 0x8a6eec: stp             lr, x16, [SP]
    // 0x8a6ef0: r0 = ==()
    //     0x8a6ef0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6ef4: tbz             w0, #4, #0x8a7090
    // 0x8a6ef8: r16 = "CU"
    //     0x8a6ef8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf448] "CU"
    //     0x8a6efc: ldr             x16, [x16, #0x448]
    // 0x8a6f00: ldur            lr, [fp, #-8]
    // 0x8a6f04: stp             lr, x16, [SP]
    // 0x8a6f08: r0 = ==()
    //     0x8a6f08: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f0c: tbz             w0, #4, #0x8a7090
    // 0x8a6f10: r16 = "MQ"
    //     0x8a6f10: add             x16, PP, #0xf, lsl #12  ; [pp+0xf450] "MQ"
    //     0x8a6f14: ldr             x16, [x16, #0x450]
    // 0x8a6f18: ldur            lr, [fp, #-8]
    // 0x8a6f1c: stp             lr, x16, [SP]
    // 0x8a6f20: r0 = ==()
    //     0x8a6f20: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f24: tbz             w0, #4, #0x8a7090
    // 0x8a6f28: r16 = "MX"
    //     0x8a6f28: add             x16, PP, #0xf, lsl #12  ; [pp+0xf458] "MX"
    //     0x8a6f2c: ldr             x16, [x16, #0x458]
    // 0x8a6f30: ldur            lr, [fp, #-8]
    // 0x8a6f34: stp             lr, x16, [SP]
    // 0x8a6f38: r0 = ==()
    //     0x8a6f38: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f3c: tbz             w0, #4, #0x8a7090
    // 0x8a6f40: r16 = "MS"
    //     0x8a6f40: add             x16, PP, #0xf, lsl #12  ; [pp+0xf460] "MS"
    //     0x8a6f44: ldr             x16, [x16, #0x460]
    // 0x8a6f48: ldur            lr, [fp, #-8]
    // 0x8a6f4c: stp             lr, x16, [SP]
    // 0x8a6f50: r0 = ==()
    //     0x8a6f50: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f54: tbz             w0, #4, #0x8a7090
    // 0x8a6f58: r16 = "NI"
    //     0x8a6f58: add             x16, PP, #0xf, lsl #12  ; [pp+0xf468] "NI"
    //     0x8a6f5c: ldr             x16, [x16, #0x468]
    // 0x8a6f60: ldur            lr, [fp, #-8]
    // 0x8a6f64: stp             lr, x16, [SP]
    // 0x8a6f68: r0 = ==()
    //     0x8a6f68: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f6c: tbz             w0, #4, #0x8a7090
    // 0x8a6f70: r16 = "PA"
    //     0x8a6f70: add             x16, PP, #0xf, lsl #12  ; [pp+0xf470] "PA"
    //     0x8a6f74: ldr             x16, [x16, #0x470]
    // 0x8a6f78: ldur            lr, [fp, #-8]
    // 0x8a6f7c: stp             lr, x16, [SP]
    // 0x8a6f80: r0 = ==()
    //     0x8a6f80: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f84: tbz             w0, #4, #0x8a7090
    // 0x8a6f88: r16 = "PR"
    //     0x8a6f88: add             x16, PP, #0xf, lsl #12  ; [pp+0xf478] "PR"
    //     0x8a6f8c: ldr             x16, [x16, #0x478]
    // 0x8a6f90: ldur            lr, [fp, #-8]
    // 0x8a6f94: stp             lr, x16, [SP]
    // 0x8a6f98: r0 = ==()
    //     0x8a6f98: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6f9c: tbz             w0, #4, #0x8a7090
    // 0x8a6fa0: r16 = "BL"
    //     0x8a6fa0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf480] "BL"
    //     0x8a6fa4: ldr             x16, [x16, #0x480]
    // 0x8a6fa8: ldur            lr, [fp, #-8]
    // 0x8a6fac: stp             lr, x16, [SP]
    // 0x8a6fb0: r0 = ==()
    //     0x8a6fb0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6fb4: tbz             w0, #4, #0x8a7090
    // 0x8a6fb8: r16 = "KN"
    //     0x8a6fb8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf488] "KN"
    //     0x8a6fbc: ldr             x16, [x16, #0x488]
    // 0x8a6fc0: ldur            lr, [fp, #-8]
    // 0x8a6fc4: stp             lr, x16, [SP]
    // 0x8a6fc8: r0 = ==()
    //     0x8a6fc8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6fcc: tbz             w0, #4, #0x8a7090
    // 0x8a6fd0: r16 = "LC"
    //     0x8a6fd0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf490] "LC"
    //     0x8a6fd4: ldr             x16, [x16, #0x490]
    // 0x8a6fd8: ldur            lr, [fp, #-8]
    // 0x8a6fdc: stp             lr, x16, [SP]
    // 0x8a6fe0: r0 = ==()
    //     0x8a6fe0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6fe4: tbz             w0, #4, #0x8a7090
    // 0x8a6fe8: r16 = "MF"
    //     0x8a6fe8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf498] "MF"
    //     0x8a6fec: ldr             x16, [x16, #0x498]
    // 0x8a6ff0: ldur            lr, [fp, #-8]
    // 0x8a6ff4: stp             lr, x16, [SP]
    // 0x8a6ff8: r0 = ==()
    //     0x8a6ff8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a6ffc: tbz             w0, #4, #0x8a7090
    // 0x8a7000: r16 = "PM"
    //     0x8a7000: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4a0] "PM"
    //     0x8a7004: ldr             x16, [x16, #0x4a0]
    // 0x8a7008: ldur            lr, [fp, #-8]
    // 0x8a700c: stp             lr, x16, [SP]
    // 0x8a7010: r0 = ==()
    //     0x8a7010: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7014: tbz             w0, #4, #0x8a7090
    // 0x8a7018: r16 = "VC"
    //     0x8a7018: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4a8] "VC"
    //     0x8a701c: ldr             x16, [x16, #0x4a8]
    // 0x8a7020: ldur            lr, [fp, #-8]
    // 0x8a7024: stp             lr, x16, [SP]
    // 0x8a7028: r0 = ==()
    //     0x8a7028: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a702c: tbz             w0, #4, #0x8a7090
    // 0x8a7030: r16 = "TT"
    //     0x8a7030: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4b0] "TT"
    //     0x8a7034: ldr             x16, [x16, #0x4b0]
    // 0x8a7038: ldur            lr, [fp, #-8]
    // 0x8a703c: stp             lr, x16, [SP]
    // 0x8a7040: r0 = ==()
    //     0x8a7040: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7044: tbz             w0, #4, #0x8a7090
    // 0x8a7048: r16 = "TC"
    //     0x8a7048: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4b8] "TC"
    //     0x8a704c: ldr             x16, [x16, #0x4b8]
    // 0x8a7050: ldur            lr, [fp, #-8]
    // 0x8a7054: stp             lr, x16, [SP]
    // 0x8a7058: r0 = ==()
    //     0x8a7058: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a705c: tbz             w0, #4, #0x8a7090
    // 0x8a7060: r16 = "VI"
    //     0x8a7060: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4c0] "VI"
    //     0x8a7064: ldr             x16, [x16, #0x4c0]
    // 0x8a7068: ldur            lr, [fp, #-8]
    // 0x8a706c: stp             lr, x16, [SP]
    // 0x8a7070: r0 = ==()
    //     0x8a7070: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7074: tbz             w0, #4, #0x8a7090
    // 0x8a7078: r16 = "VG"
    //     0x8a7078: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4c8] "VG"
    //     0x8a707c: ldr             x16, [x16, #0x4c8]
    // 0x8a7080: ldur            lr, [fp, #-8]
    // 0x8a7084: stp             lr, x16, [SP]
    // 0x8a7088: r0 = ==()
    //     0x8a7088: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a708c: tbnz            w0, #4, #0x8a70ac
    // 0x8a7090: r1 = Null
    //     0x8a7090: mov             x1, NULL
    // 0x8a7094: r2 = "north_america"
    //     0x8a7094: add             x2, PP, #0xf, lsl #12  ; [pp+0xf4d0] "north_america"
    //     0x8a7098: ldr             x2, [x2, #0x4d0]
    // 0x8a709c: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a709c: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a70a0: LeaveFrame
    //     0x8a70a0: mov             SP, fp
    //     0x8a70a4: ldp             fp, lr, [SP], #0x10
    // 0x8a70a8: ret
    //     0x8a70a8: ret             
    // 0x8a70ac: r16 = "TR"
    //     0x8a70ac: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4d8] "TR"
    //     0x8a70b0: ldr             x16, [x16, #0x4d8]
    // 0x8a70b4: ldur            lr, [fp, #-8]
    // 0x8a70b8: stp             lr, x16, [SP]
    // 0x8a70bc: r0 = ==()
    //     0x8a70bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a70c0: tbnz            w0, #4, #0x8a70e0
    // 0x8a70c4: r1 = Null
    //     0x8a70c4: mov             x1, NULL
    // 0x8a70c8: r2 = "turkey"
    //     0x8a70c8: add             x2, PP, #0xf, lsl #12  ; [pp+0xf4e0] "turkey"
    //     0x8a70cc: ldr             x2, [x2, #0x4e0]
    // 0x8a70d0: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a70d0: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a70d4: LeaveFrame
    //     0x8a70d4: mov             SP, fp
    //     0x8a70d8: ldp             fp, lr, [SP], #0x10
    // 0x8a70dc: ret
    //     0x8a70dc: ret             
    // 0x8a70e0: r16 = "AF"
    //     0x8a70e0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4e8] "AF"
    //     0x8a70e4: ldr             x16, [x16, #0x4e8]
    // 0x8a70e8: ldur            lr, [fp, #-8]
    // 0x8a70ec: stp             lr, x16, [SP]
    // 0x8a70f0: r0 = ==()
    //     0x8a70f0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a70f4: tbz             w0, #4, #0x8a7158
    // 0x8a70f8: r16 = "BD"
    //     0x8a70f8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4f0] "BD"
    //     0x8a70fc: ldr             x16, [x16, #0x4f0]
    // 0x8a7100: ldur            lr, [fp, #-8]
    // 0x8a7104: stp             lr, x16, [SP]
    // 0x8a7108: r0 = ==()
    //     0x8a7108: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a710c: tbz             w0, #4, #0x8a7158
    // 0x8a7110: r16 = "IN"
    //     0x8a7110: add             x16, PP, #0xf, lsl #12  ; [pp+0xf4f8] "IN"
    //     0x8a7114: ldr             x16, [x16, #0x4f8]
    // 0x8a7118: ldur            lr, [fp, #-8]
    // 0x8a711c: stp             lr, x16, [SP]
    // 0x8a7120: r0 = ==()
    //     0x8a7120: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7124: tbz             w0, #4, #0x8a7158
    // 0x8a7128: r16 = "PK"
    //     0x8a7128: add             x16, PP, #0xf, lsl #12  ; [pp+0xf500] "PK"
    //     0x8a712c: ldr             x16, [x16, #0x500]
    // 0x8a7130: ldur            lr, [fp, #-8]
    // 0x8a7134: stp             lr, x16, [SP]
    // 0x8a7138: r0 = ==()
    //     0x8a7138: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a713c: tbz             w0, #4, #0x8a7158
    // 0x8a7140: r16 = "IR"
    //     0x8a7140: add             x16, PP, #0xf, lsl #12  ; [pp+0xf508] "IR"
    //     0x8a7144: ldr             x16, [x16, #0x508]
    // 0x8a7148: ldur            lr, [fp, #-8]
    // 0x8a714c: stp             lr, x16, [SP]
    // 0x8a7150: r0 = ==()
    //     0x8a7150: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7154: tbnz            w0, #4, #0x8a7174
    // 0x8a7158: r1 = Null
    //     0x8a7158: mov             x1, NULL
    // 0x8a715c: r2 = "karachi"
    //     0x8a715c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf510] "karachi"
    //     0x8a7160: ldr             x2, [x2, #0x510]
    // 0x8a7164: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a7164: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a7168: LeaveFrame
    //     0x8a7168: mov             SP, fp
    //     0x8a716c: ldp             fp, lr, [SP], #0x10
    // 0x8a7170: ret
    //     0x8a7170: ret             
    // 0x8a7174: r16 = "AE"
    //     0x8a7174: add             x16, PP, #0xf, lsl #12  ; [pp+0xf518] "AE"
    //     0x8a7178: ldr             x16, [x16, #0x518]
    // 0x8a717c: ldur            lr, [fp, #-8]
    // 0x8a7180: stp             lr, x16, [SP]
    // 0x8a7184: r0 = ==()
    //     0x8a7184: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a7188: tbnz            w0, #4, #0x8a71a8
    // 0x8a718c: r1 = Null
    //     0x8a718c: mov             x1, NULL
    // 0x8a7190: r2 = "dubai"
    //     0x8a7190: add             x2, PP, #0xf, lsl #12  ; [pp+0xf520] "dubai"
    //     0x8a7194: ldr             x2, [x2, #0x520]
    // 0x8a7198: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a7198: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a719c: LeaveFrame
    //     0x8a719c: mov             SP, fp
    //     0x8a71a0: ldp             fp, lr, [SP], #0x10
    // 0x8a71a4: ret
    //     0x8a71a4: ret             
    // 0x8a71a8: r16 = "GB"
    //     0x8a71a8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf528] "GB"
    //     0x8a71ac: ldr             x16, [x16, #0x528]
    // 0x8a71b0: ldur            lr, [fp, #-8]
    // 0x8a71b4: stp             lr, x16, [SP]
    // 0x8a71b8: r0 = ==()
    //     0x8a71b8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x8a71bc: tbnz            w0, #4, #0x8a71dc
    // 0x8a71c0: r1 = Null
    //     0x8a71c0: mov             x1, NULL
    // 0x8a71c4: r2 = "muslim_prayer_times_uk"
    //     0x8a71c4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf530] "muslim_prayer_times_uk"
    //     0x8a71c8: ldr             x2, [x2, #0x530]
    // 0x8a71cc: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a71cc: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a71d0: LeaveFrame
    //     0x8a71d0: mov             SP, fp
    //     0x8a71d4: ldp             fp, lr, [SP], #0x10
    // 0x8a71d8: ret
    //     0x8a71d8: ret             
    // 0x8a71dc: r1 = Null
    //     0x8a71dc: mov             x1, NULL
    // 0x8a71e0: r2 = "muslim_world_league"
    //     0x8a71e0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf538] "muslim_world_league"
    //     0x8a71e4: ldr             x2, [x2, #0x538]
    // 0x8a71e8: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a71e8: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a71ec: LeaveFrame
    //     0x8a71ec: mov             SP, fp
    //     0x8a71f0: ldp             fp, lr, [SP], #0x10
    // 0x8a71f4: ret
    //     0x8a71f4: ret             
    // 0x8a71f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a71f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a71fc: b               #0x8a6a9c
  }
  factory _ PrayerTimeSetting.fromMethodName(/* No info */) {
    // ** addr: 0x8a7200, size: 0xfc
    // 0x8a7200: EnterFrame
    //     0x8a7200: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7204: mov             fp, SP
    // 0x8a7208: AllocStack(0x78)
    //     0x8a7208: sub             SP, SP, #0x78
    // 0x8a720c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x8a720c: mov             x0, x2
    //     0x8a7210: stur            x2, [fp, #-8]
    // 0x8a7214: CheckStackOverflow
    //     0x8a7214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a7218: cmp             SP, x16
    //     0x8a721c: b.ls            #0x8a72f4
    // 0x8a7220: mov             x1, x0
    // 0x8a7224: r0 = getCalculationMethodFromString()
    //     0x8a7224: bl              #0x8301fc  ; [package:adhan/src/calculation_method.dart] ::getCalculationMethodFromString
    // 0x8a7228: mov             x1, x0
    // 0x8a722c: r0 = CalculationMethodExtensions.getParameters()
    //     0x8a722c: bl              #0x830510  ; [package:adhan/src/calculation_method.dart] ::CalculationMethodExtensions.getParameters
    // 0x8a7230: stur            x0, [fp, #-0x20]
    // 0x8a7234: LoadField: d0 = r0->field_b
    //     0x8a7234: ldur            d0, [x0, #0xb]
    // 0x8a7238: stur            d0, [fp, #-0x58]
    // 0x8a723c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8a723c: ldur            w2, [x0, #0x17]
    // 0x8a7240: DecompressPointer r2
    //     0x8a7240: add             x2, x2, HEAP, lsl #32
    // 0x8a7244: stur            x2, [fp, #-0x18]
    // 0x8a7248: LoadField: r3 = r0->field_1b
    //     0x8a7248: ldur            x3, [x0, #0x1b]
    // 0x8a724c: stur            x3, [fp, #-0x10]
    // 0x8a7250: r1 = Instance_HighLatitudeRule
    //     0x8a7250: add             x1, PP, #0xb, lsl #12  ; [pp+0xb9e0] Obj!HighLatitudeRule@e38e61
    //     0x8a7254: ldr             x1, [x1, #0x9e0]
    // 0x8a7258: r0 = HighLatitudeRuleExtensions.string()
    //     0x8a7258: bl              #0x840da4  ; [package:adhan/src/high_latitude_rule.dart] ::HighLatitudeRuleExtensions.string
    // 0x8a725c: mov             x1, x0
    // 0x8a7260: ldur            x0, [fp, #-0x20]
    // 0x8a7264: stur            x1, [fp, #-0x50]
    // 0x8a7268: LoadField: r2 = r0->field_2f
    //     0x8a7268: ldur            w2, [x0, #0x2f]
    // 0x8a726c: DecompressPointer r2
    //     0x8a726c: add             x2, x2, HEAP, lsl #32
    // 0x8a7270: ArrayLoad: r3 = r2[0]  ; List_8
    //     0x8a7270: ldur            x3, [x2, #0x17]
    // 0x8a7274: stur            x3, [fp, #-0x48]
    // 0x8a7278: LoadField: r0 = r2->field_1f
    //     0x8a7278: ldur            x0, [x2, #0x1f]
    // 0x8a727c: stur            x0, [fp, #-0x40]
    // 0x8a7280: LoadField: r4 = r2->field_27
    //     0x8a7280: ldur            x4, [x2, #0x27]
    // 0x8a7284: stur            x4, [fp, #-0x38]
    // 0x8a7288: LoadField: r7 = r2->field_2f
    //     0x8a7288: ldur            x7, [x2, #0x2f]
    // 0x8a728c: stur            x7, [fp, #-0x30]
    // 0x8a7290: LoadField: r5 = r2->field_7
    //     0x8a7290: ldur            x5, [x2, #7]
    // 0x8a7294: stur            x5, [fp, #-0x28]
    // 0x8a7298: r0 = PrayerTimeSetting()
    //     0x8a7298: bl              #0x8a7594  ; AllocatePrayerTimeSettingStub -> PrayerTimeSetting (size=0x6c)
    // 0x8a729c: stur            x0, [fp, #-0x20]
    // 0x8a72a0: ldur            x16, [fp, #-0x18]
    // 0x8a72a4: str             x16, [SP, #0x18]
    // 0x8a72a8: ldur            x1, [fp, #-0x10]
    // 0x8a72ac: str             x1, [SP, #0x10]
    // 0x8a72b0: ldur            x1, [fp, #-0x38]
    // 0x8a72b4: ldur            x16, [fp, #-8]
    // 0x8a72b8: stp             x16, x1, [SP]
    // 0x8a72bc: mov             x1, x0
    // 0x8a72c0: ldur            x2, [fp, #-0x40]
    // 0x8a72c4: ldur            x3, [fp, #-0x48]
    // 0x8a72c8: ldur            x5, [fp, #-0x28]
    // 0x8a72cc: ldur            d0, [fp, #-0x58]
    // 0x8a72d0: ldur            x6, [fp, #-0x50]
    // 0x8a72d4: ldur            x7, [fp, #-0x30]
    // 0x8a72d8: r4 = const [0, 0xb, 0x4, 0xb, null]
    //     0x8a72d8: add             x4, PP, #0xf, lsl #12  ; [pp+0xf540] List(5) [0, 0xb, 0x4, 0xb, Null]
    //     0x8a72dc: ldr             x4, [x4, #0x540]
    // 0x8a72e0: r0 = PrayerTimeSetting()
    //     0x8a72e0: bl              #0x8a72fc  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting
    // 0x8a72e4: ldur            x0, [fp, #-0x20]
    // 0x8a72e8: LeaveFrame
    //     0x8a72e8: mov             SP, fp
    //     0x8a72ec: ldp             fp, lr, [SP], #0x10
    // 0x8a72f0: ret
    //     0x8a72f0: ret             
    // 0x8a72f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a72f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a72f8: b               #0x8a7220
  }
  _ PrayerTimeSetting(/* No info */) {
    // ** addr: 0x8a72fc, size: 0x298
    // 0x8a72fc: EnterFrame
    //     0x8a72fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7300: mov             fp, SP
    // 0x8a7304: AllocStack(0x18)
    //     0x8a7304: sub             SP, SP, #0x18
    // 0x8a7308: SetupParameters(PrayerTimeSetting this /* r1 => r6, fp-0x8 */, dynamic _ /* r6 => r1 */, dynamic _ /* r9 */, dynamic _ /* r10 */, dynamic _ /* r11 */, dynamic _ /* r12 */, {int altitude = 0 /* r13 */, dynamic angleCorrection = false /* r14 */, dynamic auto = true /* r19 */, dynamic madhabName = "shafi" /* r4 */})
    //     0x8a7308: stur            x1, [fp, #-8]
    //     0x8a730c: mov             x16, x6
    //     0x8a7310: mov             x6, x1
    //     0x8a7314: mov             x1, x16
    //     0x8a7318: ldur            w0, [x4, #0x13]
    //     0x8a731c: sub             x8, x0, #0x16
    //     0x8a7320: add             x9, fp, w8, sxtw #2
    //     0x8a7324: ldr             x9, [x9, #0x28]
    //     0x8a7328: add             x10, fp, w8, sxtw #2
    //     0x8a732c: ldr             x10, [x10, #0x20]
    //     0x8a7330: add             x11, fp, w8, sxtw #2
    //     0x8a7334: ldr             x11, [x11, #0x18]
    //     0x8a7338: add             x12, fp, w8, sxtw #2
    //     0x8a733c: ldr             x12, [x12, #0x10]
    //     0x8a7340: ldur            w8, [x4, #0x1f]
    //     0x8a7344: add             x8, x8, HEAP, lsl #32
    //     0x8a7348: add             x16, PP, #0xf, lsl #12  ; [pp+0xf548] "altitude"
    //     0x8a734c: ldr             x16, [x16, #0x548]
    //     0x8a7350: cmp             w8, w16
    //     0x8a7354: b.ne            #0x8a7380
    //     0x8a7358: ldur            w8, [x4, #0x23]
    //     0x8a735c: add             x8, x8, HEAP, lsl #32
    //     0x8a7360: sub             w13, w0, w8
    //     0x8a7364: add             x8, fp, w13, sxtw #2
    //     0x8a7368: ldr             x8, [x8, #8]
    //     0x8a736c: sbfx            x13, x8, #1, #0x1f
    //     0x8a7370: tbz             w8, #0, #0x8a7378
    //     0x8a7374: ldur            x13, [x8, #7]
    //     0x8a7378: movz            x8, #0x1
    //     0x8a737c: b               #0x8a7388
    //     0x8a7380: movz            x13, #0
    //     0x8a7384: movz            x8, #0
    //     0x8a7388: lsl             x14, x8, #1
    //     0x8a738c: lsl             w19, w14, #1
    //     0x8a7390: add             w20, w19, #8
    //     0x8a7394: add             x16, x4, w20, sxtw #1
    //     0x8a7398: ldur            w23, [x16, #0xf]
    //     0x8a739c: add             x23, x23, HEAP, lsl #32
    //     0x8a73a0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2b0] "angleCorrection"
    //     0x8a73a4: ldr             x16, [x16, #0x2b0]
    //     0x8a73a8: cmp             w23, w16
    //     0x8a73ac: b.ne            #0x8a73e0
    //     0x8a73b0: add             w8, w19, #0xa
    //     0x8a73b4: add             x16, x4, w8, sxtw #1
    //     0x8a73b8: ldur            w19, [x16, #0xf]
    //     0x8a73bc: add             x19, x19, HEAP, lsl #32
    //     0x8a73c0: sub             w8, w0, w19
    //     0x8a73c4: add             x19, fp, w8, sxtw #2
    //     0x8a73c8: ldr             x19, [x19, #8]
    //     0x8a73cc: add             w8, w14, #2
    //     0x8a73d0: sbfx            x14, x8, #1, #0x1f
    //     0x8a73d4: mov             x8, x14
    //     0x8a73d8: mov             x14, x19
    //     0x8a73dc: b               #0x8a73e4
    //     0x8a73e0: add             x14, NULL, #0x30  ; false
    //     0x8a73e4: lsl             x19, x8, #1
    //     0x8a73e8: lsl             w20, w19, #1
    //     0x8a73ec: add             w23, w20, #8
    //     0x8a73f0: add             x16, x4, w23, sxtw #1
    //     0x8a73f4: ldur            w24, [x16, #0xf]
    //     0x8a73f8: add             x24, x24, HEAP, lsl #32
    //     0x8a73fc: add             x16, PP, #8, lsl #12  ; [pp+0x8f38] "auto"
    //     0x8a7400: ldr             x16, [x16, #0xf38]
    //     0x8a7404: cmp             w24, w16
    //     0x8a7408: b.ne            #0x8a743c
    //     0x8a740c: add             w8, w20, #0xa
    //     0x8a7410: add             x16, x4, w8, sxtw #1
    //     0x8a7414: ldur            w20, [x16, #0xf]
    //     0x8a7418: add             x20, x20, HEAP, lsl #32
    //     0x8a741c: sub             w8, w0, w20
    //     0x8a7420: add             x20, fp, w8, sxtw #2
    //     0x8a7424: ldr             x20, [x20, #8]
    //     0x8a7428: add             w8, w19, #2
    //     0x8a742c: sbfx            x19, x8, #1, #0x1f
    //     0x8a7430: mov             x8, x19
    //     0x8a7434: mov             x19, x20
    //     0x8a7438: b               #0x8a7440
    //     0x8a743c: add             x19, NULL, #0x20  ; true
    //     0x8a7440: lsl             x20, x8, #1
    //     0x8a7444: lsl             w8, w20, #1
    //     0x8a7448: add             w20, w8, #8
    //     0x8a744c: add             x16, x4, w20, sxtw #1
    //     0x8a7450: ldur            w23, [x16, #0xf]
    //     0x8a7454: add             x23, x23, HEAP, lsl #32
    //     0x8a7458: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2e8] "madhabName"
    //     0x8a745c: ldr             x16, [x16, #0x2e8]
    //     0x8a7460: cmp             w23, w16
    //     0x8a7464: b.ne            #0x8a748c
    //     0x8a7468: add             w20, w8, #0xa
    //     0x8a746c: add             x16, x4, w20, sxtw #1
    //     0x8a7470: ldur            w8, [x16, #0xf]
    //     0x8a7474: add             x8, x8, HEAP, lsl #32
    //     0x8a7478: sub             w4, w0, w8
    //     0x8a747c: add             x0, fp, w4, sxtw #2
    //     0x8a7480: ldr             x0, [x0, #8]
    //     0x8a7484: mov             x4, x0
    //     0x8a7488: b               #0x8a7494
    //     0x8a748c: add             x4, PP, #0xf, lsl #12  ; [pp+0xf550] "shafi"
    //     0x8a7490: ldr             x4, [x4, #0x550]
    // 0x8a7494: CheckStackOverflow
    //     0x8a7494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a7498: cmp             SP, x16
    //     0x8a749c: b.ls            #0x8a758c
    // 0x8a74a0: mov             x0, x12
    // 0x8a74a4: StoreField: r6->field_1b = r0
    //     0x8a74a4: stur            w0, [x6, #0x1b]
    //     0x8a74a8: ldurb           w16, [x6, #-1]
    //     0x8a74ac: ldurb           w17, [x0, #-1]
    //     0x8a74b0: and             x16, x17, x16, lsr #2
    //     0x8a74b4: tst             x16, HEAP, lsr #32
    //     0x8a74b8: b.eq            #0x8a74c0
    //     0x8a74bc: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8a74c0: StoreField: r6->field_13 = r19
    //     0x8a74c0: stur            w19, [x6, #0x13]
    // 0x8a74c4: ArrayStore: r6[0] = r14  ; List_4
    //     0x8a74c4: stur            w14, [x6, #0x17]
    // 0x8a74c8: StoreField: r6->field_33 = r13
    //     0x8a74c8: stur            x13, [x6, #0x33]
    // 0x8a74cc: StoreField: r6->field_1f = d0
    //     0x8a74cc: stur            d0, [x6, #0x1f]
    // 0x8a74d0: mov             x0, x9
    // 0x8a74d4: StoreField: r6->field_27 = r0
    //     0x8a74d4: stur            w0, [x6, #0x27]
    //     0x8a74d8: ldurb           w16, [x6, #-1]
    //     0x8a74dc: ldurb           w17, [x0, #-1]
    //     0x8a74e0: and             x16, x17, x16, lsr #2
    //     0x8a74e4: tst             x16, HEAP, lsr #32
    //     0x8a74e8: b.eq            #0x8a74f0
    //     0x8a74ec: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8a74f0: StoreField: r6->field_2b = r10
    //     0x8a74f0: stur            x10, [x6, #0x2b]
    // 0x8a74f4: mov             x0, x4
    // 0x8a74f8: StoreField: r6->field_3b = r0
    //     0x8a74f8: stur            w0, [x6, #0x3b]
    //     0x8a74fc: ldurb           w16, [x6, #-1]
    //     0x8a7500: ldurb           w17, [x0, #-1]
    //     0x8a7504: and             x16, x17, x16, lsr #2
    //     0x8a7508: tst             x16, HEAP, lsr #32
    //     0x8a750c: b.eq            #0x8a7514
    //     0x8a7510: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8a7514: mov             x0, x1
    // 0x8a7518: StoreField: r6->field_3f = r0
    //     0x8a7518: stur            w0, [x6, #0x3f]
    //     0x8a751c: ldurb           w16, [x6, #-1]
    //     0x8a7520: ldurb           w17, [x0, #-1]
    //     0x8a7524: and             x16, x17, x16, lsr #2
    //     0x8a7528: tst             x16, HEAP, lsr #32
    //     0x8a752c: b.eq            #0x8a7534
    //     0x8a7530: bl              #0xec0ac8  ; WriteBarrierWrappersStub
    // 0x8a7534: StoreField: r6->field_43 = r3
    //     0x8a7534: stur            x3, [x6, #0x43]
    // 0x8a7538: StoreField: r6->field_4b = r2
    //     0x8a7538: stur            x2, [x6, #0x4b]
    // 0x8a753c: StoreField: r6->field_53 = r11
    //     0x8a753c: stur            x11, [x6, #0x53]
    // 0x8a7540: StoreField: r6->field_5b = r7
    //     0x8a7540: stur            x7, [x6, #0x5b]
    // 0x8a7544: StoreField: r6->field_63 = r5
    //     0x8a7544: stur            x5, [x6, #0x63]
    // 0x8a7548: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8a7548: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8a754c: ldr             x16, [x16, #0x9f8]
    // 0x8a7550: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8a7554: stp             lr, x16, [SP]
    // 0x8a7558: r0 = Map._fromLiteral()
    //     0x8a7558: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8a755c: ldur            x1, [fp, #-8]
    // 0x8a7560: StoreField: r1->field_f = r0
    //     0x8a7560: stur            w0, [x1, #0xf]
    //     0x8a7564: ldurb           w16, [x1, #-1]
    //     0x8a7568: ldurb           w17, [x0, #-1]
    //     0x8a756c: and             x16, x17, x16, lsr #2
    //     0x8a7570: tst             x16, HEAP, lsr #32
    //     0x8a7574: b.eq            #0x8a757c
    //     0x8a7578: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8a757c: r0 = Null
    //     0x8a757c: mov             x0, NULL
    // 0x8a7580: LeaveFrame
    //     0x8a7580: mov             SP, fp
    //     0x8a7584: ldp             fp, lr, [SP], #0x10
    // 0x8a7588: ret
    //     0x8a7588: ret             
    // 0x8a758c: r0 = StackOverflowSharedWithFPURegs()
    //     0x8a758c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x8a7590: b               #0x8a74a0
  }
  _ copyWith(/* No info */) {
    // ** addr: 0x8a8690, size: 0x708
    // 0x8a8690: EnterFrame
    //     0x8a8690: stp             fp, lr, [SP, #-0x10]!
    //     0x8a8694: mov             fp, SP
    // 0x8a8698: AllocStack(0x88)
    //     0x8a8698: sub             SP, SP, #0x88
    // 0x8a869c: SetupParameters({dynamic angleCorrection = Null /* r5 */, dynamic asrAdjustment = Null /* r6 */, dynamic auto = Null /* r7 */, dynamic dhuhrAdjustment = Null /* r8 */, dynamic fajrAdjustment = Null /* r9 */, dynamic fajrAngle = Null /* r10 */, dynamic highLatitudeRuleName = Null /* r11 */, dynamic ishaAdjustment = Null /* r12 */, dynamic ishaAngle = Null /* r13 */, dynamic ishaInterval = Null /* r14 */, dynamic madhabName = Null /* r19 */, dynamic maghribAdjustment = Null /* r20 */, dynamic methodName = Null /* r0 */})
    //     0x8a869c: ldur            w0, [x4, #0x13]
    //     0x8a86a0: ldur            w3, [x4, #0x1f]
    //     0x8a86a4: add             x3, x3, HEAP, lsl #32
    //     0x8a86a8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2b0] "angleCorrection"
    //     0x8a86ac: ldr             x16, [x16, #0x2b0]
    //     0x8a86b0: cmp             w3, w16
    //     0x8a86b4: b.ne            #0x8a86d8
    //     0x8a86b8: ldur            w3, [x4, #0x23]
    //     0x8a86bc: add             x3, x3, HEAP, lsl #32
    //     0x8a86c0: sub             w5, w0, w3
    //     0x8a86c4: add             x3, fp, w5, sxtw #2
    //     0x8a86c8: ldr             x3, [x3, #8]
    //     0x8a86cc: mov             x5, x3
    //     0x8a86d0: movz            x3, #0x1
    //     0x8a86d4: b               #0x8a86e0
    //     0x8a86d8: mov             x5, NULL
    //     0x8a86dc: movz            x3, #0
    //     0x8a86e0: lsl             x6, x3, #1
    //     0x8a86e4: lsl             w7, w6, #1
    //     0x8a86e8: add             w8, w7, #8
    //     0x8a86ec: add             x16, x4, w8, sxtw #1
    //     0x8a86f0: ldur            w9, [x16, #0xf]
    //     0x8a86f4: add             x9, x9, HEAP, lsl #32
    //     0x8a86f8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2b8] "asrAdjustment"
    //     0x8a86fc: ldr             x16, [x16, #0x2b8]
    //     0x8a8700: cmp             w9, w16
    //     0x8a8704: b.ne            #0x8a8738
    //     0x8a8708: add             w3, w7, #0xa
    //     0x8a870c: add             x16, x4, w3, sxtw #1
    //     0x8a8710: ldur            w7, [x16, #0xf]
    //     0x8a8714: add             x7, x7, HEAP, lsl #32
    //     0x8a8718: sub             w3, w0, w7
    //     0x8a871c: add             x7, fp, w3, sxtw #2
    //     0x8a8720: ldr             x7, [x7, #8]
    //     0x8a8724: add             w3, w6, #2
    //     0x8a8728: sbfx            x6, x3, #1, #0x1f
    //     0x8a872c: mov             x3, x6
    //     0x8a8730: mov             x6, x7
    //     0x8a8734: b               #0x8a873c
    //     0x8a8738: mov             x6, NULL
    //     0x8a873c: lsl             x7, x3, #1
    //     0x8a8740: lsl             w8, w7, #1
    //     0x8a8744: add             w9, w8, #8
    //     0x8a8748: add             x16, x4, w9, sxtw #1
    //     0x8a874c: ldur            w10, [x16, #0xf]
    //     0x8a8750: add             x10, x10, HEAP, lsl #32
    //     0x8a8754: add             x16, PP, #8, lsl #12  ; [pp+0x8f38] "auto"
    //     0x8a8758: ldr             x16, [x16, #0xf38]
    //     0x8a875c: cmp             w10, w16
    //     0x8a8760: b.ne            #0x8a8794
    //     0x8a8764: add             w3, w8, #0xa
    //     0x8a8768: add             x16, x4, w3, sxtw #1
    //     0x8a876c: ldur            w8, [x16, #0xf]
    //     0x8a8770: add             x8, x8, HEAP, lsl #32
    //     0x8a8774: sub             w3, w0, w8
    //     0x8a8778: add             x8, fp, w3, sxtw #2
    //     0x8a877c: ldr             x8, [x8, #8]
    //     0x8a8780: add             w3, w7, #2
    //     0x8a8784: sbfx            x7, x3, #1, #0x1f
    //     0x8a8788: mov             x3, x7
    //     0x8a878c: mov             x7, x8
    //     0x8a8790: b               #0x8a8798
    //     0x8a8794: mov             x7, NULL
    //     0x8a8798: lsl             x8, x3, #1
    //     0x8a879c: lsl             w9, w8, #1
    //     0x8a87a0: add             w10, w9, #8
    //     0x8a87a4: add             x16, x4, w10, sxtw #1
    //     0x8a87a8: ldur            w11, [x16, #0xf]
    //     0x8a87ac: add             x11, x11, HEAP, lsl #32
    //     0x8a87b0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2c0] "dhuhrAdjustment"
    //     0x8a87b4: ldr             x16, [x16, #0x2c0]
    //     0x8a87b8: cmp             w11, w16
    //     0x8a87bc: b.ne            #0x8a87f0
    //     0x8a87c0: add             w3, w9, #0xa
    //     0x8a87c4: add             x16, x4, w3, sxtw #1
    //     0x8a87c8: ldur            w9, [x16, #0xf]
    //     0x8a87cc: add             x9, x9, HEAP, lsl #32
    //     0x8a87d0: sub             w3, w0, w9
    //     0x8a87d4: add             x9, fp, w3, sxtw #2
    //     0x8a87d8: ldr             x9, [x9, #8]
    //     0x8a87dc: add             w3, w8, #2
    //     0x8a87e0: sbfx            x8, x3, #1, #0x1f
    //     0x8a87e4: mov             x3, x8
    //     0x8a87e8: mov             x8, x9
    //     0x8a87ec: b               #0x8a87f4
    //     0x8a87f0: mov             x8, NULL
    //     0x8a87f4: lsl             x9, x3, #1
    //     0x8a87f8: lsl             w10, w9, #1
    //     0x8a87fc: add             w11, w10, #8
    //     0x8a8800: add             x16, x4, w11, sxtw #1
    //     0x8a8804: ldur            w12, [x16, #0xf]
    //     0x8a8808: add             x12, x12, HEAP, lsl #32
    //     0x8a880c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2c8] "fajrAdjustment"
    //     0x8a8810: ldr             x16, [x16, #0x2c8]
    //     0x8a8814: cmp             w12, w16
    //     0x8a8818: b.ne            #0x8a884c
    //     0x8a881c: add             w3, w10, #0xa
    //     0x8a8820: add             x16, x4, w3, sxtw #1
    //     0x8a8824: ldur            w10, [x16, #0xf]
    //     0x8a8828: add             x10, x10, HEAP, lsl #32
    //     0x8a882c: sub             w3, w0, w10
    //     0x8a8830: add             x10, fp, w3, sxtw #2
    //     0x8a8834: ldr             x10, [x10, #8]
    //     0x8a8838: add             w3, w9, #2
    //     0x8a883c: sbfx            x9, x3, #1, #0x1f
    //     0x8a8840: mov             x3, x9
    //     0x8a8844: mov             x9, x10
    //     0x8a8848: b               #0x8a8850
    //     0x8a884c: mov             x9, NULL
    //     0x8a8850: lsl             x10, x3, #1
    //     0x8a8854: lsl             w11, w10, #1
    //     0x8a8858: add             w12, w11, #8
    //     0x8a885c: add             x16, x4, w12, sxtw #1
    //     0x8a8860: ldur            w13, [x16, #0xf]
    //     0x8a8864: add             x13, x13, HEAP, lsl #32
    //     0x8a8868: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2d0] "fajrAngle"
    //     0x8a886c: ldr             x16, [x16, #0x2d0]
    //     0x8a8870: cmp             w13, w16
    //     0x8a8874: b.ne            #0x8a88a8
    //     0x8a8878: add             w3, w11, #0xa
    //     0x8a887c: add             x16, x4, w3, sxtw #1
    //     0x8a8880: ldur            w11, [x16, #0xf]
    //     0x8a8884: add             x11, x11, HEAP, lsl #32
    //     0x8a8888: sub             w3, w0, w11
    //     0x8a888c: add             x11, fp, w3, sxtw #2
    //     0x8a8890: ldr             x11, [x11, #8]
    //     0x8a8894: add             w3, w10, #2
    //     0x8a8898: sbfx            x10, x3, #1, #0x1f
    //     0x8a889c: mov             x3, x10
    //     0x8a88a0: mov             x10, x11
    //     0x8a88a4: b               #0x8a88ac
    //     0x8a88a8: mov             x10, NULL
    //     0x8a88ac: lsl             x11, x3, #1
    //     0x8a88b0: lsl             w12, w11, #1
    //     0x8a88b4: add             w13, w12, #8
    //     0x8a88b8: add             x16, x4, w13, sxtw #1
    //     0x8a88bc: ldur            w14, [x16, #0xf]
    //     0x8a88c0: add             x14, x14, HEAP, lsl #32
    //     0x8a88c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2d8] "highLatitudeRuleName"
    //     0x8a88c8: ldr             x16, [x16, #0x2d8]
    //     0x8a88cc: cmp             w14, w16
    //     0x8a88d0: b.ne            #0x8a8904
    //     0x8a88d4: add             w3, w12, #0xa
    //     0x8a88d8: add             x16, x4, w3, sxtw #1
    //     0x8a88dc: ldur            w12, [x16, #0xf]
    //     0x8a88e0: add             x12, x12, HEAP, lsl #32
    //     0x8a88e4: sub             w3, w0, w12
    //     0x8a88e8: add             x12, fp, w3, sxtw #2
    //     0x8a88ec: ldr             x12, [x12, #8]
    //     0x8a88f0: add             w3, w11, #2
    //     0x8a88f4: sbfx            x11, x3, #1, #0x1f
    //     0x8a88f8: mov             x3, x11
    //     0x8a88fc: mov             x11, x12
    //     0x8a8900: b               #0x8a8908
    //     0x8a8904: mov             x11, NULL
    //     0x8a8908: lsl             x12, x3, #1
    //     0x8a890c: lsl             w13, w12, #1
    //     0x8a8910: add             w14, w13, #8
    //     0x8a8914: add             x16, x4, w14, sxtw #1
    //     0x8a8918: ldur            w19, [x16, #0xf]
    //     0x8a891c: add             x19, x19, HEAP, lsl #32
    //     0x8a8920: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2e0] "ishaAdjustment"
    //     0x8a8924: ldr             x16, [x16, #0x2e0]
    //     0x8a8928: cmp             w19, w16
    //     0x8a892c: b.ne            #0x8a8960
    //     0x8a8930: add             w3, w13, #0xa
    //     0x8a8934: add             x16, x4, w3, sxtw #1
    //     0x8a8938: ldur            w13, [x16, #0xf]
    //     0x8a893c: add             x13, x13, HEAP, lsl #32
    //     0x8a8940: sub             w3, w0, w13
    //     0x8a8944: add             x13, fp, w3, sxtw #2
    //     0x8a8948: ldr             x13, [x13, #8]
    //     0x8a894c: add             w3, w12, #2
    //     0x8a8950: sbfx            x12, x3, #1, #0x1f
    //     0x8a8954: mov             x3, x12
    //     0x8a8958: mov             x12, x13
    //     0x8a895c: b               #0x8a8964
    //     0x8a8960: mov             x12, NULL
    //     0x8a8964: lsl             x13, x3, #1
    //     0x8a8968: lsl             w14, w13, #1
    //     0x8a896c: add             w19, w14, #8
    //     0x8a8970: add             x16, x4, w19, sxtw #1
    //     0x8a8974: ldur            w20, [x16, #0xf]
    //     0x8a8978: add             x20, x20, HEAP, lsl #32
    //     0x8a897c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9c8] "ishaAngle"
    //     0x8a8980: ldr             x16, [x16, #0x9c8]
    //     0x8a8984: cmp             w20, w16
    //     0x8a8988: b.ne            #0x8a89bc
    //     0x8a898c: add             w3, w14, #0xa
    //     0x8a8990: add             x16, x4, w3, sxtw #1
    //     0x8a8994: ldur            w14, [x16, #0xf]
    //     0x8a8998: add             x14, x14, HEAP, lsl #32
    //     0x8a899c: sub             w3, w0, w14
    //     0x8a89a0: add             x14, fp, w3, sxtw #2
    //     0x8a89a4: ldr             x14, [x14, #8]
    //     0x8a89a8: add             w3, w13, #2
    //     0x8a89ac: sbfx            x13, x3, #1, #0x1f
    //     0x8a89b0: mov             x3, x13
    //     0x8a89b4: mov             x13, x14
    //     0x8a89b8: b               #0x8a89c0
    //     0x8a89bc: mov             x13, NULL
    //     0x8a89c0: lsl             x14, x3, #1
    //     0x8a89c4: lsl             w19, w14, #1
    //     0x8a89c8: add             w20, w19, #8
    //     0x8a89cc: add             x16, x4, w20, sxtw #1
    //     0x8a89d0: ldur            w23, [x16, #0xf]
    //     0x8a89d4: add             x23, x23, HEAP, lsl #32
    //     0x8a89d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9d0] "ishaInterval"
    //     0x8a89dc: ldr             x16, [x16, #0x9d0]
    //     0x8a89e0: cmp             w23, w16
    //     0x8a89e4: b.ne            #0x8a8a18
    //     0x8a89e8: add             w3, w19, #0xa
    //     0x8a89ec: add             x16, x4, w3, sxtw #1
    //     0x8a89f0: ldur            w19, [x16, #0xf]
    //     0x8a89f4: add             x19, x19, HEAP, lsl #32
    //     0x8a89f8: sub             w3, w0, w19
    //     0x8a89fc: add             x19, fp, w3, sxtw #2
    //     0x8a8a00: ldr             x19, [x19, #8]
    //     0x8a8a04: add             w3, w14, #2
    //     0x8a8a08: sbfx            x14, x3, #1, #0x1f
    //     0x8a8a0c: mov             x3, x14
    //     0x8a8a10: mov             x14, x19
    //     0x8a8a14: b               #0x8a8a1c
    //     0x8a8a18: mov             x14, NULL
    //     0x8a8a1c: lsl             x19, x3, #1
    //     0x8a8a20: lsl             w20, w19, #1
    //     0x8a8a24: add             w23, w20, #8
    //     0x8a8a28: add             x16, x4, w23, sxtw #1
    //     0x8a8a2c: ldur            w24, [x16, #0xf]
    //     0x8a8a30: add             x24, x24, HEAP, lsl #32
    //     0x8a8a34: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2e8] "madhabName"
    //     0x8a8a38: ldr             x16, [x16, #0x2e8]
    //     0x8a8a3c: cmp             w24, w16
    //     0x8a8a40: b.ne            #0x8a8a74
    //     0x8a8a44: add             w3, w20, #0xa
    //     0x8a8a48: add             x16, x4, w3, sxtw #1
    //     0x8a8a4c: ldur            w20, [x16, #0xf]
    //     0x8a8a50: add             x20, x20, HEAP, lsl #32
    //     0x8a8a54: sub             w3, w0, w20
    //     0x8a8a58: add             x20, fp, w3, sxtw #2
    //     0x8a8a5c: ldr             x20, [x20, #8]
    //     0x8a8a60: add             w3, w19, #2
    //     0x8a8a64: sbfx            x19, x3, #1, #0x1f
    //     0x8a8a68: mov             x3, x19
    //     0x8a8a6c: mov             x19, x20
    //     0x8a8a70: b               #0x8a8a78
    //     0x8a8a74: mov             x19, NULL
    //     0x8a8a78: lsl             x20, x3, #1
    //     0x8a8a7c: lsl             w23, w20, #1
    //     0x8a8a80: add             w24, w23, #8
    //     0x8a8a84: add             x16, x4, w24, sxtw #1
    //     0x8a8a88: ldur            w25, [x16, #0xf]
    //     0x8a8a8c: add             x25, x25, HEAP, lsl #32
    //     0x8a8a90: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2f0] "maghribAdjustment"
    //     0x8a8a94: ldr             x16, [x16, #0x2f0]
    //     0x8a8a98: cmp             w25, w16
    //     0x8a8a9c: b.ne            #0x8a8ad0
    //     0x8a8aa0: add             w3, w23, #0xa
    //     0x8a8aa4: add             x16, x4, w3, sxtw #1
    //     0x8a8aa8: ldur            w23, [x16, #0xf]
    //     0x8a8aac: add             x23, x23, HEAP, lsl #32
    //     0x8a8ab0: sub             w3, w0, w23
    //     0x8a8ab4: add             x23, fp, w3, sxtw #2
    //     0x8a8ab8: ldr             x23, [x23, #8]
    //     0x8a8abc: add             w3, w20, #2
    //     0x8a8ac0: sbfx            x20, x3, #1, #0x1f
    //     0x8a8ac4: mov             x3, x20
    //     0x8a8ac8: mov             x20, x23
    //     0x8a8acc: b               #0x8a8ad4
    //     0x8a8ad0: mov             x20, NULL
    //     0x8a8ad4: lsl             x23, x3, #1
    //     0x8a8ad8: lsl             w3, w23, #1
    //     0x8a8adc: add             w23, w3, #8
    //     0x8a8ae0: add             x16, x4, w23, sxtw #1
    //     0x8a8ae4: ldur            w24, [x16, #0xf]
    //     0x8a8ae8: add             x24, x24, HEAP, lsl #32
    //     0x8a8aec: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2f8] "methodName"
    //     0x8a8af0: ldr             x16, [x16, #0x2f8]
    //     0x8a8af4: cmp             w24, w16
    //     0x8a8af8: b.ne            #0x8a8b1c
    //     0x8a8afc: add             w23, w3, #0xa
    //     0x8a8b00: add             x16, x4, w23, sxtw #1
    //     0x8a8b04: ldur            w3, [x16, #0xf]
    //     0x8a8b08: add             x3, x3, HEAP, lsl #32
    //     0x8a8b0c: sub             w4, w0, w3
    //     0x8a8b10: add             x0, fp, w4, sxtw #2
    //     0x8a8b14: ldr             x0, [x0, #8]
    //     0x8a8b18: b               #0x8a8b20
    //     0x8a8b1c: mov             x0, NULL
    // 0x8a8b20: CheckStackOverflow
    //     0x8a8b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a8b24: cmp             SP, x16
    //     0x8a8b28: b.ls            #0x8a8d90
    // 0x8a8b2c: cmp             w7, NULL
    // 0x8a8b30: b.ne            #0x8a8b40
    // 0x8a8b34: LoadField: r3 = r1->field_13
    //     0x8a8b34: ldur            w3, [x1, #0x13]
    // 0x8a8b38: DecompressPointer r3
    //     0x8a8b38: add             x3, x3, HEAP, lsl #32
    // 0x8a8b3c: b               #0x8a8b44
    // 0x8a8b40: mov             x3, x7
    // 0x8a8b44: stur            x3, [fp, #-0x68]
    // 0x8a8b48: cmp             w5, NULL
    // 0x8a8b4c: b.ne            #0x8a8b5c
    // 0x8a8b50: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8a8b50: ldur            w4, [x1, #0x17]
    // 0x8a8b54: DecompressPointer r4
    //     0x8a8b54: add             x4, x4, HEAP, lsl #32
    // 0x8a8b58: b               #0x8a8b60
    // 0x8a8b5c: mov             x4, x5
    // 0x8a8b60: stur            x4, [fp, #-0x60]
    // 0x8a8b64: cmp             w0, NULL
    // 0x8a8b68: b.ne            #0x8a8b74
    // 0x8a8b6c: LoadField: r0 = r1->field_1b
    //     0x8a8b6c: ldur            w0, [x1, #0x1b]
    // 0x8a8b70: DecompressPointer r0
    //     0x8a8b70: add             x0, x0, HEAP, lsl #32
    // 0x8a8b74: stur            x0, [fp, #-0x58]
    // 0x8a8b78: cmp             w10, NULL
    // 0x8a8b7c: b.ne            #0x8a8b88
    // 0x8a8b80: LoadField: d0 = r1->field_1f
    //     0x8a8b80: ldur            d0, [x1, #0x1f]
    // 0x8a8b84: b               #0x8a8b8c
    // 0x8a8b88: LoadField: d0 = r10->field_7
    //     0x8a8b88: ldur            d0, [x10, #7]
    // 0x8a8b8c: stur            d0, [fp, #-0x78]
    // 0x8a8b90: cmp             w13, NULL
    // 0x8a8b94: b.ne            #0x8a8ba4
    // 0x8a8b98: LoadField: r5 = r1->field_27
    //     0x8a8b98: ldur            w5, [x1, #0x27]
    // 0x8a8b9c: DecompressPointer r5
    //     0x8a8b9c: add             x5, x5, HEAP, lsl #32
    // 0x8a8ba0: b               #0x8a8ba8
    // 0x8a8ba4: mov             x5, x13
    // 0x8a8ba8: stur            x5, [fp, #-0x50]
    // 0x8a8bac: cmp             w14, NULL
    // 0x8a8bb0: b.ne            #0x8a8bbc
    // 0x8a8bb4: LoadField: r7 = r1->field_2b
    //     0x8a8bb4: ldur            x7, [x1, #0x2b]
    // 0x8a8bb8: b               #0x8a8bc8
    // 0x8a8bbc: r7 = LoadInt32Instr(r14)
    //     0x8a8bbc: sbfx            x7, x14, #1, #0x1f
    //     0x8a8bc0: tbz             w14, #0, #0x8a8bc8
    //     0x8a8bc4: ldur            x7, [x14, #7]
    // 0x8a8bc8: stur            x7, [fp, #-0x48]
    // 0x8a8bcc: cmp             w2, NULL
    // 0x8a8bd0: b.ne            #0x8a8bdc
    // 0x8a8bd4: LoadField: r2 = r1->field_33
    //     0x8a8bd4: ldur            x2, [x1, #0x33]
    // 0x8a8bd8: b               #0x8a8bec
    // 0x8a8bdc: r10 = LoadInt32Instr(r2)
    //     0x8a8bdc: sbfx            x10, x2, #1, #0x1f
    //     0x8a8be0: tbz             w2, #0, #0x8a8be8
    //     0x8a8be4: ldur            x10, [x2, #7]
    // 0x8a8be8: mov             x2, x10
    // 0x8a8bec: stur            x2, [fp, #-0x40]
    // 0x8a8bf0: cmp             w19, NULL
    // 0x8a8bf4: b.ne            #0x8a8c04
    // 0x8a8bf8: LoadField: r10 = r1->field_3b
    //     0x8a8bf8: ldur            w10, [x1, #0x3b]
    // 0x8a8bfc: DecompressPointer r10
    //     0x8a8bfc: add             x10, x10, HEAP, lsl #32
    // 0x8a8c00: b               #0x8a8c08
    // 0x8a8c04: mov             x10, x19
    // 0x8a8c08: stur            x10, [fp, #-0x38]
    // 0x8a8c0c: cmp             w11, NULL
    // 0x8a8c10: b.ne            #0x8a8c1c
    // 0x8a8c14: LoadField: r11 = r1->field_3f
    //     0x8a8c14: ldur            w11, [x1, #0x3f]
    // 0x8a8c18: DecompressPointer r11
    //     0x8a8c18: add             x11, x11, HEAP, lsl #32
    // 0x8a8c1c: stur            x11, [fp, #-0x30]
    // 0x8a8c20: cmp             w8, NULL
    // 0x8a8c24: b.ne            #0x8a8c30
    // 0x8a8c28: LoadField: r8 = r1->field_43
    //     0x8a8c28: ldur            x8, [x1, #0x43]
    // 0x8a8c2c: b               #0x8a8c40
    // 0x8a8c30: r13 = LoadInt32Instr(r8)
    //     0x8a8c30: sbfx            x13, x8, #1, #0x1f
    //     0x8a8c34: tbz             w8, #0, #0x8a8c3c
    //     0x8a8c38: ldur            x13, [x8, #7]
    // 0x8a8c3c: mov             x8, x13
    // 0x8a8c40: stur            x8, [fp, #-0x28]
    // 0x8a8c44: cmp             w6, NULL
    // 0x8a8c48: b.ne            #0x8a8c54
    // 0x8a8c4c: LoadField: r6 = r1->field_4b
    //     0x8a8c4c: ldur            x6, [x1, #0x4b]
    // 0x8a8c50: b               #0x8a8c64
    // 0x8a8c54: r13 = LoadInt32Instr(r6)
    //     0x8a8c54: sbfx            x13, x6, #1, #0x1f
    //     0x8a8c58: tbz             w6, #0, #0x8a8c60
    //     0x8a8c5c: ldur            x13, [x6, #7]
    // 0x8a8c60: mov             x6, x13
    // 0x8a8c64: stur            x6, [fp, #-0x20]
    // 0x8a8c68: cmp             w20, NULL
    // 0x8a8c6c: b.ne            #0x8a8c78
    // 0x8a8c70: LoadField: r13 = r1->field_53
    //     0x8a8c70: ldur            x13, [x1, #0x53]
    // 0x8a8c74: b               #0x8a8c84
    // 0x8a8c78: r13 = LoadInt32Instr(r20)
    //     0x8a8c78: sbfx            x13, x20, #1, #0x1f
    //     0x8a8c7c: tbz             w20, #0, #0x8a8c84
    //     0x8a8c80: ldur            x13, [x20, #7]
    // 0x8a8c84: stur            x13, [fp, #-0x18]
    // 0x8a8c88: cmp             w12, NULL
    // 0x8a8c8c: b.ne            #0x8a8c98
    // 0x8a8c90: LoadField: r12 = r1->field_5b
    //     0x8a8c90: ldur            x12, [x1, #0x5b]
    // 0x8a8c94: b               #0x8a8ca8
    // 0x8a8c98: r14 = LoadInt32Instr(r12)
    //     0x8a8c98: sbfx            x14, x12, #1, #0x1f
    //     0x8a8c9c: tbz             w12, #0, #0x8a8ca4
    //     0x8a8ca0: ldur            x14, [x12, #7]
    // 0x8a8ca4: mov             x12, x14
    // 0x8a8ca8: stur            x12, [fp, #-0x10]
    // 0x8a8cac: cmp             w9, NULL
    // 0x8a8cb0: b.ne            #0x8a8cc0
    // 0x8a8cb4: LoadField: r9 = r1->field_63
    //     0x8a8cb4: ldur            x9, [x1, #0x63]
    // 0x8a8cb8: mov             x1, x9
    // 0x8a8cbc: b               #0x8a8ccc
    // 0x8a8cc0: r1 = LoadInt32Instr(r9)
    //     0x8a8cc0: sbfx            x1, x9, #1, #0x1f
    //     0x8a8cc4: tbz             w9, #0, #0x8a8ccc
    //     0x8a8cc8: ldur            x1, [x9, #7]
    // 0x8a8ccc: stur            x1, [fp, #-8]
    // 0x8a8cd0: r0 = PrayerTimeSetting()
    //     0x8a8cd0: bl              #0x8a7594  ; AllocatePrayerTimeSettingStub -> PrayerTimeSetting (size=0x6c)
    // 0x8a8cd4: mov             x1, x0
    // 0x8a8cd8: ldur            x0, [fp, #-0x58]
    // 0x8a8cdc: stur            x1, [fp, #-0x70]
    // 0x8a8ce0: StoreField: r1->field_1b = r0
    //     0x8a8ce0: stur            w0, [x1, #0x1b]
    // 0x8a8ce4: ldur            x0, [fp, #-0x68]
    // 0x8a8ce8: StoreField: r1->field_13 = r0
    //     0x8a8ce8: stur            w0, [x1, #0x13]
    // 0x8a8cec: ldur            x0, [fp, #-0x60]
    // 0x8a8cf0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8a8cf0: stur            w0, [x1, #0x17]
    // 0x8a8cf4: ldur            x0, [fp, #-0x40]
    // 0x8a8cf8: StoreField: r1->field_33 = r0
    //     0x8a8cf8: stur            x0, [x1, #0x33]
    // 0x8a8cfc: ldur            d0, [fp, #-0x78]
    // 0x8a8d00: StoreField: r1->field_1f = d0
    //     0x8a8d00: stur            d0, [x1, #0x1f]
    // 0x8a8d04: ldur            x0, [fp, #-0x50]
    // 0x8a8d08: StoreField: r1->field_27 = r0
    //     0x8a8d08: stur            w0, [x1, #0x27]
    // 0x8a8d0c: ldur            x0, [fp, #-0x48]
    // 0x8a8d10: StoreField: r1->field_2b = r0
    //     0x8a8d10: stur            x0, [x1, #0x2b]
    // 0x8a8d14: ldur            x0, [fp, #-0x38]
    // 0x8a8d18: StoreField: r1->field_3b = r0
    //     0x8a8d18: stur            w0, [x1, #0x3b]
    // 0x8a8d1c: ldur            x0, [fp, #-0x30]
    // 0x8a8d20: StoreField: r1->field_3f = r0
    //     0x8a8d20: stur            w0, [x1, #0x3f]
    // 0x8a8d24: ldur            x0, [fp, #-0x28]
    // 0x8a8d28: StoreField: r1->field_43 = r0
    //     0x8a8d28: stur            x0, [x1, #0x43]
    // 0x8a8d2c: ldur            x0, [fp, #-0x20]
    // 0x8a8d30: StoreField: r1->field_4b = r0
    //     0x8a8d30: stur            x0, [x1, #0x4b]
    // 0x8a8d34: ldur            x0, [fp, #-0x18]
    // 0x8a8d38: StoreField: r1->field_53 = r0
    //     0x8a8d38: stur            x0, [x1, #0x53]
    // 0x8a8d3c: ldur            x0, [fp, #-0x10]
    // 0x8a8d40: StoreField: r1->field_5b = r0
    //     0x8a8d40: stur            x0, [x1, #0x5b]
    // 0x8a8d44: ldur            x0, [fp, #-8]
    // 0x8a8d48: StoreField: r1->field_63 = r0
    //     0x8a8d48: stur            x0, [x1, #0x63]
    // 0x8a8d4c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8a8d4c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8a8d50: ldr             x16, [x16, #0x9f8]
    // 0x8a8d54: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8a8d58: stp             lr, x16, [SP]
    // 0x8a8d5c: r0 = Map._fromLiteral()
    //     0x8a8d5c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8a8d60: ldur            x1, [fp, #-0x70]
    // 0x8a8d64: StoreField: r1->field_f = r0
    //     0x8a8d64: stur            w0, [x1, #0xf]
    //     0x8a8d68: ldurb           w16, [x1, #-1]
    //     0x8a8d6c: ldurb           w17, [x0, #-1]
    //     0x8a8d70: and             x16, x17, x16, lsr #2
    //     0x8a8d74: tst             x16, HEAP, lsr #32
    //     0x8a8d78: b.eq            #0x8a8d80
    //     0x8a8d7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8a8d80: mov             x0, x1
    // 0x8a8d84: LeaveFrame
    //     0x8a8d84: mov             SP, fp
    //     0x8a8d88: ldp             fp, lr, [SP], #0x10
    // 0x8a8d8c: ret
    //     0x8a8d8c: ret             
    // 0x8a8d90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a8d90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a8d94: b               #0x8a8b2c
  }
}

// class id: 1652, size: 0x14, field offset: 0xc
class PrayerTimeSettingAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa643b0, size: 0x880
    // 0xa643b0: EnterFrame
    //     0xa643b0: stp             fp, lr, [SP, #-0x10]!
    //     0xa643b4: mov             fp, SP
    // 0xa643b8: AllocStack(0xa8)
    //     0xa643b8: sub             SP, SP, #0xa8
    // 0xa643bc: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa643bc: stur            x2, [fp, #-0x20]
    // 0xa643c0: CheckStackOverflow
    //     0xa643c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa643c4: cmp             SP, x16
    //     0xa643c8: b.ls            #0xa64c18
    // 0xa643cc: LoadField: r3 = r2->field_23
    //     0xa643cc: ldur            x3, [x2, #0x23]
    // 0xa643d0: add             x0, x3, #1
    // 0xa643d4: LoadField: r1 = r2->field_1b
    //     0xa643d4: ldur            x1, [x2, #0x1b]
    // 0xa643d8: cmp             x0, x1
    // 0xa643dc: b.gt            #0xa64bbc
    // 0xa643e0: LoadField: r4 = r2->field_7
    //     0xa643e0: ldur            w4, [x2, #7]
    // 0xa643e4: DecompressPointer r4
    //     0xa643e4: add             x4, x4, HEAP, lsl #32
    // 0xa643e8: stur            x4, [fp, #-0x18]
    // 0xa643ec: StoreField: r2->field_23 = r0
    //     0xa643ec: stur            x0, [x2, #0x23]
    // 0xa643f0: LoadField: r0 = r4->field_13
    //     0xa643f0: ldur            w0, [x4, #0x13]
    // 0xa643f4: r5 = LoadInt32Instr(r0)
    //     0xa643f4: sbfx            x5, x0, #1, #0x1f
    // 0xa643f8: mov             x0, x5
    // 0xa643fc: mov             x1, x3
    // 0xa64400: stur            x5, [fp, #-0x10]
    // 0xa64404: cmp             x1, x0
    // 0xa64408: b.hs            #0xa64c20
    // 0xa6440c: LoadField: r0 = r4->field_7
    //     0xa6440c: ldur            x0, [x4, #7]
    // 0xa64410: ldrb            w1, [x0, x3]
    // 0xa64414: stur            x1, [fp, #-8]
    // 0xa64418: r16 = <int, dynamic>
    //     0xa64418: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa6441c: ldr             x16, [x16, #0xac0]
    // 0xa64420: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64424: stp             lr, x16, [SP]
    // 0xa64428: r0 = Map._fromLiteral()
    //     0xa64428: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa6442c: mov             x2, x0
    // 0xa64430: stur            x2, [fp, #-0x38]
    // 0xa64434: r6 = 0
    //     0xa64434: movz            x6, #0
    // 0xa64438: ldur            x3, [fp, #-0x20]
    // 0xa6443c: ldur            x4, [fp, #-0x18]
    // 0xa64440: ldur            x5, [fp, #-8]
    // 0xa64444: stur            x6, [fp, #-0x30]
    // 0xa64448: CheckStackOverflow
    //     0xa64448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6444c: cmp             SP, x16
    //     0xa64450: b.ls            #0xa64c24
    // 0xa64454: cmp             x6, x5
    // 0xa64458: b.ge            #0xa644e4
    // 0xa6445c: LoadField: r7 = r3->field_23
    //     0xa6445c: ldur            x7, [x3, #0x23]
    // 0xa64460: add             x0, x7, #1
    // 0xa64464: LoadField: r1 = r3->field_1b
    //     0xa64464: ldur            x1, [x3, #0x1b]
    // 0xa64468: cmp             x0, x1
    // 0xa6446c: b.gt            #0xa64be4
    // 0xa64470: StoreField: r3->field_23 = r0
    //     0xa64470: stur            x0, [x3, #0x23]
    // 0xa64474: ldur            x0, [fp, #-0x10]
    // 0xa64478: mov             x1, x7
    // 0xa6447c: cmp             x1, x0
    // 0xa64480: b.hs            #0xa64c2c
    // 0xa64484: LoadField: r0 = r4->field_7
    //     0xa64484: ldur            x0, [x4, #7]
    // 0xa64488: ldrb            w8, [x0, x7]
    // 0xa6448c: mov             x1, x3
    // 0xa64490: stur            x8, [fp, #-0x28]
    // 0xa64494: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa64494: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa64498: r0 = read()
    //     0xa64498: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa6449c: mov             x1, x0
    // 0xa644a0: ldur            x0, [fp, #-0x28]
    // 0xa644a4: lsl             x2, x0, #1
    // 0xa644a8: r16 = LoadInt32Instr(r2)
    //     0xa644a8: sbfx            x16, x2, #1, #0x1f
    // 0xa644ac: r17 = 11601
    //     0xa644ac: movz            x17, #0x2d51
    // 0xa644b0: mul             x0, x16, x17
    // 0xa644b4: umulh           x16, x16, x17
    // 0xa644b8: eor             x0, x0, x16
    // 0xa644bc: r0 = 0
    //     0xa644bc: eor             x0, x0, x0, lsr #32
    // 0xa644c0: ubfiz           x0, x0, #1, #0x1e
    // 0xa644c4: r5 = LoadInt32Instr(r0)
    //     0xa644c4: sbfx            x5, x0, #1, #0x1f
    // 0xa644c8: mov             x3, x1
    // 0xa644cc: ldur            x1, [fp, #-0x38]
    // 0xa644d0: r0 = _set()
    //     0xa644d0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa644d4: ldur            x0, [fp, #-0x30]
    // 0xa644d8: add             x6, x0, #1
    // 0xa644dc: ldur            x2, [fp, #-0x38]
    // 0xa644e0: b               #0xa64438
    // 0xa644e4: mov             x0, x2
    // 0xa644e8: mov             x1, x0
    // 0xa644ec: r2 = 4
    //     0xa644ec: movz            x2, #0x4
    // 0xa644f0: r0 = _getValueOrData()
    //     0xa644f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa644f4: ldur            x3, [fp, #-0x38]
    // 0xa644f8: LoadField: r1 = r3->field_f
    //     0xa644f8: ldur            w1, [x3, #0xf]
    // 0xa644fc: DecompressPointer r1
    //     0xa644fc: add             x1, x1, HEAP, lsl #32
    // 0xa64500: cmp             w1, w0
    // 0xa64504: b.ne            #0xa64510
    // 0xa64508: r4 = Null
    //     0xa64508: mov             x4, NULL
    // 0xa6450c: b               #0xa64514
    // 0xa64510: mov             x4, x0
    // 0xa64514: mov             x0, x4
    // 0xa64518: stur            x4, [fp, #-0x18]
    // 0xa6451c: r2 = Null
    //     0xa6451c: mov             x2, NULL
    // 0xa64520: r1 = Null
    //     0xa64520: mov             x1, NULL
    // 0xa64524: r4 = 60
    //     0xa64524: movz            x4, #0x3c
    // 0xa64528: branchIfSmi(r0, 0xa64534)
    //     0xa64528: tbz             w0, #0, #0xa64534
    // 0xa6452c: r4 = LoadClassIdInstr(r0)
    //     0xa6452c: ldur            x4, [x0, #-1]
    //     0xa64530: ubfx            x4, x4, #0xc, #0x14
    // 0xa64534: sub             x4, x4, #0x5e
    // 0xa64538: cmp             x4, #1
    // 0xa6453c: b.ls            #0xa64550
    // 0xa64540: r8 = String
    //     0xa64540: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa64544: r3 = Null
    //     0xa64544: add             x3, PP, #0x20, lsl #12  ; [pp+0x20eb8] Null
    //     0xa64548: ldr             x3, [x3, #0xeb8]
    // 0xa6454c: r0 = String()
    //     0xa6454c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa64550: ldur            x1, [fp, #-0x38]
    // 0xa64554: r2 = 0
    //     0xa64554: movz            x2, #0
    // 0xa64558: r0 = _getValueOrData()
    //     0xa64558: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6455c: ldur            x3, [fp, #-0x38]
    // 0xa64560: LoadField: r1 = r3->field_f
    //     0xa64560: ldur            w1, [x3, #0xf]
    // 0xa64564: DecompressPointer r1
    //     0xa64564: add             x1, x1, HEAP, lsl #32
    // 0xa64568: cmp             w1, w0
    // 0xa6456c: b.ne            #0xa64578
    // 0xa64570: r4 = Null
    //     0xa64570: mov             x4, NULL
    // 0xa64574: b               #0xa6457c
    // 0xa64578: mov             x4, x0
    // 0xa6457c: mov             x0, x4
    // 0xa64580: stur            x4, [fp, #-0x20]
    // 0xa64584: r2 = Null
    //     0xa64584: mov             x2, NULL
    // 0xa64588: r1 = Null
    //     0xa64588: mov             x1, NULL
    // 0xa6458c: r4 = 60
    //     0xa6458c: movz            x4, #0x3c
    // 0xa64590: branchIfSmi(r0, 0xa6459c)
    //     0xa64590: tbz             w0, #0, #0xa6459c
    // 0xa64594: r4 = LoadClassIdInstr(r0)
    //     0xa64594: ldur            x4, [x0, #-1]
    //     0xa64598: ubfx            x4, x4, #0xc, #0x14
    // 0xa6459c: cmp             x4, #0x3f
    // 0xa645a0: b.eq            #0xa645b4
    // 0xa645a4: r8 = bool
    //     0xa645a4: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa645a8: r3 = Null
    //     0xa645a8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ec8] Null
    //     0xa645ac: ldr             x3, [x3, #0xec8]
    // 0xa645b0: r0 = bool()
    //     0xa645b0: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa645b4: ldur            x1, [fp, #-0x38]
    // 0xa645b8: r2 = 2
    //     0xa645b8: movz            x2, #0x2
    // 0xa645bc: r0 = _getValueOrData()
    //     0xa645bc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa645c0: ldur            x3, [fp, #-0x38]
    // 0xa645c4: LoadField: r1 = r3->field_f
    //     0xa645c4: ldur            w1, [x3, #0xf]
    // 0xa645c8: DecompressPointer r1
    //     0xa645c8: add             x1, x1, HEAP, lsl #32
    // 0xa645cc: cmp             w1, w0
    // 0xa645d0: b.ne            #0xa645dc
    // 0xa645d4: r4 = Null
    //     0xa645d4: mov             x4, NULL
    // 0xa645d8: b               #0xa645e0
    // 0xa645dc: mov             x4, x0
    // 0xa645e0: mov             x0, x4
    // 0xa645e4: stur            x4, [fp, #-0x40]
    // 0xa645e8: r2 = Null
    //     0xa645e8: mov             x2, NULL
    // 0xa645ec: r1 = Null
    //     0xa645ec: mov             x1, NULL
    // 0xa645f0: r4 = 60
    //     0xa645f0: movz            x4, #0x3c
    // 0xa645f4: branchIfSmi(r0, 0xa64600)
    //     0xa645f4: tbz             w0, #0, #0xa64600
    // 0xa645f8: r4 = LoadClassIdInstr(r0)
    //     0xa645f8: ldur            x4, [x0, #-1]
    //     0xa645fc: ubfx            x4, x4, #0xc, #0x14
    // 0xa64600: cmp             x4, #0x3f
    // 0xa64604: b.eq            #0xa64618
    // 0xa64608: r8 = bool
    //     0xa64608: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa6460c: r3 = Null
    //     0xa6460c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ed8] Null
    //     0xa64610: ldr             x3, [x3, #0xed8]
    // 0xa64614: r0 = bool()
    //     0xa64614: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa64618: ldur            x1, [fp, #-0x38]
    // 0xa6461c: r2 = 12
    //     0xa6461c: movz            x2, #0xc
    // 0xa64620: r0 = _getValueOrData()
    //     0xa64620: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64624: ldur            x3, [fp, #-0x38]
    // 0xa64628: LoadField: r1 = r3->field_f
    //     0xa64628: ldur            w1, [x3, #0xf]
    // 0xa6462c: DecompressPointer r1
    //     0xa6462c: add             x1, x1, HEAP, lsl #32
    // 0xa64630: cmp             w1, w0
    // 0xa64634: b.ne            #0xa64640
    // 0xa64638: r4 = Null
    //     0xa64638: mov             x4, NULL
    // 0xa6463c: b               #0xa64644
    // 0xa64640: mov             x4, x0
    // 0xa64644: mov             x0, x4
    // 0xa64648: stur            x4, [fp, #-0x48]
    // 0xa6464c: r2 = Null
    //     0xa6464c: mov             x2, NULL
    // 0xa64650: r1 = Null
    //     0xa64650: mov             x1, NULL
    // 0xa64654: branchIfSmi(r0, 0xa6467c)
    //     0xa64654: tbz             w0, #0, #0xa6467c
    // 0xa64658: r4 = LoadClassIdInstr(r0)
    //     0xa64658: ldur            x4, [x0, #-1]
    //     0xa6465c: ubfx            x4, x4, #0xc, #0x14
    // 0xa64660: sub             x4, x4, #0x3c
    // 0xa64664: cmp             x4, #1
    // 0xa64668: b.ls            #0xa6467c
    // 0xa6466c: r8 = int
    //     0xa6466c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa64670: r3 = Null
    //     0xa64670: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ee8] Null
    //     0xa64674: ldr             x3, [x3, #0xee8]
    // 0xa64678: r0 = int()
    //     0xa64678: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa6467c: ldur            x1, [fp, #-0x38]
    // 0xa64680: r2 = 6
    //     0xa64680: movz            x2, #0x6
    // 0xa64684: r0 = _getValueOrData()
    //     0xa64684: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64688: ldur            x3, [fp, #-0x38]
    // 0xa6468c: LoadField: r1 = r3->field_f
    //     0xa6468c: ldur            w1, [x3, #0xf]
    // 0xa64690: DecompressPointer r1
    //     0xa64690: add             x1, x1, HEAP, lsl #32
    // 0xa64694: cmp             w1, w0
    // 0xa64698: b.ne            #0xa646a4
    // 0xa6469c: r4 = Null
    //     0xa6469c: mov             x4, NULL
    // 0xa646a0: b               #0xa646a8
    // 0xa646a4: mov             x4, x0
    // 0xa646a8: mov             x0, x4
    // 0xa646ac: stur            x4, [fp, #-0x50]
    // 0xa646b0: r2 = Null
    //     0xa646b0: mov             x2, NULL
    // 0xa646b4: r1 = Null
    //     0xa646b4: mov             x1, NULL
    // 0xa646b8: r4 = 60
    //     0xa646b8: movz            x4, #0x3c
    // 0xa646bc: branchIfSmi(r0, 0xa646c8)
    //     0xa646bc: tbz             w0, #0, #0xa646c8
    // 0xa646c0: r4 = LoadClassIdInstr(r0)
    //     0xa646c0: ldur            x4, [x0, #-1]
    //     0xa646c4: ubfx            x4, x4, #0xc, #0x14
    // 0xa646c8: cmp             x4, #0x3e
    // 0xa646cc: b.eq            #0xa646e0
    // 0xa646d0: r8 = double
    //     0xa646d0: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa646d4: r3 = Null
    //     0xa646d4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ef8] Null
    //     0xa646d8: ldr             x3, [x3, #0xef8]
    // 0xa646dc: r0 = double()
    //     0xa646dc: bl              #0xed4460  ; IsType_double_Stub
    // 0xa646e0: ldur            x1, [fp, #-0x38]
    // 0xa646e4: r2 = 8
    //     0xa646e4: movz            x2, #0x8
    // 0xa646e8: r0 = _getValueOrData()
    //     0xa646e8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa646ec: ldur            x3, [fp, #-0x38]
    // 0xa646f0: LoadField: r1 = r3->field_f
    //     0xa646f0: ldur            w1, [x3, #0xf]
    // 0xa646f4: DecompressPointer r1
    //     0xa646f4: add             x1, x1, HEAP, lsl #32
    // 0xa646f8: cmp             w1, w0
    // 0xa646fc: b.ne            #0xa64708
    // 0xa64700: r4 = Null
    //     0xa64700: mov             x4, NULL
    // 0xa64704: b               #0xa6470c
    // 0xa64708: mov             x4, x0
    // 0xa6470c: mov             x0, x4
    // 0xa64710: stur            x4, [fp, #-0x58]
    // 0xa64714: r2 = Null
    //     0xa64714: mov             x2, NULL
    // 0xa64718: r1 = Null
    //     0xa64718: mov             x1, NULL
    // 0xa6471c: r4 = 60
    //     0xa6471c: movz            x4, #0x3c
    // 0xa64720: branchIfSmi(r0, 0xa6472c)
    //     0xa64720: tbz             w0, #0, #0xa6472c
    // 0xa64724: r4 = LoadClassIdInstr(r0)
    //     0xa64724: ldur            x4, [x0, #-1]
    //     0xa64728: ubfx            x4, x4, #0xc, #0x14
    // 0xa6472c: cmp             x4, #0x3e
    // 0xa64730: b.eq            #0xa64744
    // 0xa64734: r8 = double?
    //     0xa64734: ldr             x8, [PP, #0x12d0]  ; [pp+0x12d0] Type: double?
    // 0xa64738: r3 = Null
    //     0xa64738: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f08] Null
    //     0xa6473c: ldr             x3, [x3, #0xf08]
    // 0xa64740: r0 = double?()
    //     0xa64740: bl              #0xed4434  ; IsType_double?_Stub
    // 0xa64744: ldur            x1, [fp, #-0x38]
    // 0xa64748: r2 = 10
    //     0xa64748: movz            x2, #0xa
    // 0xa6474c: r0 = _getValueOrData()
    //     0xa6474c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64750: ldur            x3, [fp, #-0x38]
    // 0xa64754: LoadField: r1 = r3->field_f
    //     0xa64754: ldur            w1, [x3, #0xf]
    // 0xa64758: DecompressPointer r1
    //     0xa64758: add             x1, x1, HEAP, lsl #32
    // 0xa6475c: cmp             w1, w0
    // 0xa64760: b.ne            #0xa6476c
    // 0xa64764: r4 = Null
    //     0xa64764: mov             x4, NULL
    // 0xa64768: b               #0xa64770
    // 0xa6476c: mov             x4, x0
    // 0xa64770: mov             x0, x4
    // 0xa64774: stur            x4, [fp, #-0x60]
    // 0xa64778: r2 = Null
    //     0xa64778: mov             x2, NULL
    // 0xa6477c: r1 = Null
    //     0xa6477c: mov             x1, NULL
    // 0xa64780: branchIfSmi(r0, 0xa647a8)
    //     0xa64780: tbz             w0, #0, #0xa647a8
    // 0xa64784: r4 = LoadClassIdInstr(r0)
    //     0xa64784: ldur            x4, [x0, #-1]
    //     0xa64788: ubfx            x4, x4, #0xc, #0x14
    // 0xa6478c: sub             x4, x4, #0x3c
    // 0xa64790: cmp             x4, #1
    // 0xa64794: b.ls            #0xa647a8
    // 0xa64798: r8 = int
    //     0xa64798: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa6479c: r3 = Null
    //     0xa6479c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f18] Null
    //     0xa647a0: ldr             x3, [x3, #0xf18]
    // 0xa647a4: r0 = int()
    //     0xa647a4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa647a8: ldur            x1, [fp, #-0x38]
    // 0xa647ac: r2 = 14
    //     0xa647ac: movz            x2, #0xe
    // 0xa647b0: r0 = _getValueOrData()
    //     0xa647b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa647b4: ldur            x3, [fp, #-0x38]
    // 0xa647b8: LoadField: r1 = r3->field_f
    //     0xa647b8: ldur            w1, [x3, #0xf]
    // 0xa647bc: DecompressPointer r1
    //     0xa647bc: add             x1, x1, HEAP, lsl #32
    // 0xa647c0: cmp             w1, w0
    // 0xa647c4: b.ne            #0xa647d0
    // 0xa647c8: r4 = Null
    //     0xa647c8: mov             x4, NULL
    // 0xa647cc: b               #0xa647d4
    // 0xa647d0: mov             x4, x0
    // 0xa647d4: mov             x0, x4
    // 0xa647d8: stur            x4, [fp, #-0x68]
    // 0xa647dc: r2 = Null
    //     0xa647dc: mov             x2, NULL
    // 0xa647e0: r1 = Null
    //     0xa647e0: mov             x1, NULL
    // 0xa647e4: r4 = 60
    //     0xa647e4: movz            x4, #0x3c
    // 0xa647e8: branchIfSmi(r0, 0xa647f4)
    //     0xa647e8: tbz             w0, #0, #0xa647f4
    // 0xa647ec: r4 = LoadClassIdInstr(r0)
    //     0xa647ec: ldur            x4, [x0, #-1]
    //     0xa647f0: ubfx            x4, x4, #0xc, #0x14
    // 0xa647f4: sub             x4, x4, #0x5e
    // 0xa647f8: cmp             x4, #1
    // 0xa647fc: b.ls            #0xa64810
    // 0xa64800: r8 = String
    //     0xa64800: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa64804: r3 = Null
    //     0xa64804: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f28] Null
    //     0xa64808: ldr             x3, [x3, #0xf28]
    // 0xa6480c: r0 = String()
    //     0xa6480c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa64810: ldur            x1, [fp, #-0x38]
    // 0xa64814: r2 = 16
    //     0xa64814: movz            x2, #0x10
    // 0xa64818: r0 = _getValueOrData()
    //     0xa64818: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6481c: ldur            x3, [fp, #-0x38]
    // 0xa64820: LoadField: r1 = r3->field_f
    //     0xa64820: ldur            w1, [x3, #0xf]
    // 0xa64824: DecompressPointer r1
    //     0xa64824: add             x1, x1, HEAP, lsl #32
    // 0xa64828: cmp             w1, w0
    // 0xa6482c: b.ne            #0xa64838
    // 0xa64830: r4 = Null
    //     0xa64830: mov             x4, NULL
    // 0xa64834: b               #0xa6483c
    // 0xa64838: mov             x4, x0
    // 0xa6483c: mov             x0, x4
    // 0xa64840: stur            x4, [fp, #-0x70]
    // 0xa64844: r2 = Null
    //     0xa64844: mov             x2, NULL
    // 0xa64848: r1 = Null
    //     0xa64848: mov             x1, NULL
    // 0xa6484c: r4 = 60
    //     0xa6484c: movz            x4, #0x3c
    // 0xa64850: branchIfSmi(r0, 0xa6485c)
    //     0xa64850: tbz             w0, #0, #0xa6485c
    // 0xa64854: r4 = LoadClassIdInstr(r0)
    //     0xa64854: ldur            x4, [x0, #-1]
    //     0xa64858: ubfx            x4, x4, #0xc, #0x14
    // 0xa6485c: sub             x4, x4, #0x5e
    // 0xa64860: cmp             x4, #1
    // 0xa64864: b.ls            #0xa64878
    // 0xa64868: r8 = String
    //     0xa64868: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa6486c: r3 = Null
    //     0xa6486c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f38] Null
    //     0xa64870: ldr             x3, [x3, #0xf38]
    // 0xa64874: r0 = String()
    //     0xa64874: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa64878: ldur            x1, [fp, #-0x38]
    // 0xa6487c: r2 = 18
    //     0xa6487c: movz            x2, #0x12
    // 0xa64880: r0 = _getValueOrData()
    //     0xa64880: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64884: ldur            x3, [fp, #-0x38]
    // 0xa64888: LoadField: r1 = r3->field_f
    //     0xa64888: ldur            w1, [x3, #0xf]
    // 0xa6488c: DecompressPointer r1
    //     0xa6488c: add             x1, x1, HEAP, lsl #32
    // 0xa64890: cmp             w1, w0
    // 0xa64894: b.ne            #0xa648a0
    // 0xa64898: r4 = Null
    //     0xa64898: mov             x4, NULL
    // 0xa6489c: b               #0xa648a4
    // 0xa648a0: mov             x4, x0
    // 0xa648a4: mov             x0, x4
    // 0xa648a8: stur            x4, [fp, #-0x78]
    // 0xa648ac: r2 = Null
    //     0xa648ac: mov             x2, NULL
    // 0xa648b0: r1 = Null
    //     0xa648b0: mov             x1, NULL
    // 0xa648b4: branchIfSmi(r0, 0xa648dc)
    //     0xa648b4: tbz             w0, #0, #0xa648dc
    // 0xa648b8: r4 = LoadClassIdInstr(r0)
    //     0xa648b8: ldur            x4, [x0, #-1]
    //     0xa648bc: ubfx            x4, x4, #0xc, #0x14
    // 0xa648c0: sub             x4, x4, #0x3c
    // 0xa648c4: cmp             x4, #1
    // 0xa648c8: b.ls            #0xa648dc
    // 0xa648cc: r8 = int
    //     0xa648cc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa648d0: r3 = Null
    //     0xa648d0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f48] Null
    //     0xa648d4: ldr             x3, [x3, #0xf48]
    // 0xa648d8: r0 = int()
    //     0xa648d8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa648dc: ldur            x1, [fp, #-0x38]
    // 0xa648e0: r2 = 20
    //     0xa648e0: movz            x2, #0x14
    // 0xa648e4: r0 = _getValueOrData()
    //     0xa648e4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa648e8: ldur            x3, [fp, #-0x38]
    // 0xa648ec: LoadField: r1 = r3->field_f
    //     0xa648ec: ldur            w1, [x3, #0xf]
    // 0xa648f0: DecompressPointer r1
    //     0xa648f0: add             x1, x1, HEAP, lsl #32
    // 0xa648f4: cmp             w1, w0
    // 0xa648f8: b.ne            #0xa64904
    // 0xa648fc: r4 = Null
    //     0xa648fc: mov             x4, NULL
    // 0xa64900: b               #0xa64908
    // 0xa64904: mov             x4, x0
    // 0xa64908: mov             x0, x4
    // 0xa6490c: stur            x4, [fp, #-0x80]
    // 0xa64910: r2 = Null
    //     0xa64910: mov             x2, NULL
    // 0xa64914: r1 = Null
    //     0xa64914: mov             x1, NULL
    // 0xa64918: branchIfSmi(r0, 0xa64940)
    //     0xa64918: tbz             w0, #0, #0xa64940
    // 0xa6491c: r4 = LoadClassIdInstr(r0)
    //     0xa6491c: ldur            x4, [x0, #-1]
    //     0xa64920: ubfx            x4, x4, #0xc, #0x14
    // 0xa64924: sub             x4, x4, #0x3c
    // 0xa64928: cmp             x4, #1
    // 0xa6492c: b.ls            #0xa64940
    // 0xa64930: r8 = int
    //     0xa64930: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa64934: r3 = Null
    //     0xa64934: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f58] Null
    //     0xa64938: ldr             x3, [x3, #0xf58]
    // 0xa6493c: r0 = int()
    //     0xa6493c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa64940: ldur            x1, [fp, #-0x38]
    // 0xa64944: r2 = 22
    //     0xa64944: movz            x2, #0x16
    // 0xa64948: r0 = _getValueOrData()
    //     0xa64948: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6494c: ldur            x3, [fp, #-0x38]
    // 0xa64950: LoadField: r1 = r3->field_f
    //     0xa64950: ldur            w1, [x3, #0xf]
    // 0xa64954: DecompressPointer r1
    //     0xa64954: add             x1, x1, HEAP, lsl #32
    // 0xa64958: cmp             w1, w0
    // 0xa6495c: b.ne            #0xa64968
    // 0xa64960: r4 = Null
    //     0xa64960: mov             x4, NULL
    // 0xa64964: b               #0xa6496c
    // 0xa64968: mov             x4, x0
    // 0xa6496c: mov             x0, x4
    // 0xa64970: stur            x4, [fp, #-0x88]
    // 0xa64974: r2 = Null
    //     0xa64974: mov             x2, NULL
    // 0xa64978: r1 = Null
    //     0xa64978: mov             x1, NULL
    // 0xa6497c: branchIfSmi(r0, 0xa649a4)
    //     0xa6497c: tbz             w0, #0, #0xa649a4
    // 0xa64980: r4 = LoadClassIdInstr(r0)
    //     0xa64980: ldur            x4, [x0, #-1]
    //     0xa64984: ubfx            x4, x4, #0xc, #0x14
    // 0xa64988: sub             x4, x4, #0x3c
    // 0xa6498c: cmp             x4, #1
    // 0xa64990: b.ls            #0xa649a4
    // 0xa64994: r8 = int
    //     0xa64994: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa64998: r3 = Null
    //     0xa64998: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f68] Null
    //     0xa6499c: ldr             x3, [x3, #0xf68]
    // 0xa649a0: r0 = int()
    //     0xa649a0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa649a4: ldur            x1, [fp, #-0x38]
    // 0xa649a8: r2 = 24
    //     0xa649a8: movz            x2, #0x18
    // 0xa649ac: r0 = _getValueOrData()
    //     0xa649ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa649b0: ldur            x3, [fp, #-0x38]
    // 0xa649b4: LoadField: r1 = r3->field_f
    //     0xa649b4: ldur            w1, [x3, #0xf]
    // 0xa649b8: DecompressPointer r1
    //     0xa649b8: add             x1, x1, HEAP, lsl #32
    // 0xa649bc: cmp             w1, w0
    // 0xa649c0: b.ne            #0xa649cc
    // 0xa649c4: r4 = Null
    //     0xa649c4: mov             x4, NULL
    // 0xa649c8: b               #0xa649d0
    // 0xa649cc: mov             x4, x0
    // 0xa649d0: mov             x0, x4
    // 0xa649d4: stur            x4, [fp, #-0x90]
    // 0xa649d8: r2 = Null
    //     0xa649d8: mov             x2, NULL
    // 0xa649dc: r1 = Null
    //     0xa649dc: mov             x1, NULL
    // 0xa649e0: branchIfSmi(r0, 0xa64a08)
    //     0xa649e0: tbz             w0, #0, #0xa64a08
    // 0xa649e4: r4 = LoadClassIdInstr(r0)
    //     0xa649e4: ldur            x4, [x0, #-1]
    //     0xa649e8: ubfx            x4, x4, #0xc, #0x14
    // 0xa649ec: sub             x4, x4, #0x3c
    // 0xa649f0: cmp             x4, #1
    // 0xa649f4: b.ls            #0xa64a08
    // 0xa649f8: r8 = int
    //     0xa649f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa649fc: r3 = Null
    //     0xa649fc: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f78] Null
    //     0xa64a00: ldr             x3, [x3, #0xf78]
    // 0xa64a04: r0 = int()
    //     0xa64a04: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa64a08: ldur            x1, [fp, #-0x38]
    // 0xa64a0c: r2 = 26
    //     0xa64a0c: movz            x2, #0x1a
    // 0xa64a10: r0 = _getValueOrData()
    //     0xa64a10: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64a14: mov             x1, x0
    // 0xa64a18: ldur            x0, [fp, #-0x38]
    // 0xa64a1c: LoadField: r2 = r0->field_f
    //     0xa64a1c: ldur            w2, [x0, #0xf]
    // 0xa64a20: DecompressPointer r2
    //     0xa64a20: add             x2, x2, HEAP, lsl #32
    // 0xa64a24: cmp             w2, w1
    // 0xa64a28: b.ne            #0xa64a34
    // 0xa64a2c: r20 = Null
    //     0xa64a2c: mov             x20, NULL
    // 0xa64a30: b               #0xa64a38
    // 0xa64a34: mov             x20, x1
    // 0xa64a38: ldur            x19, [fp, #-0x18]
    // 0xa64a3c: ldur            x14, [fp, #-0x20]
    // 0xa64a40: ldur            x13, [fp, #-0x40]
    // 0xa64a44: ldur            x12, [fp, #-0x48]
    // 0xa64a48: ldur            x11, [fp, #-0x50]
    // 0xa64a4c: ldur            x10, [fp, #-0x58]
    // 0xa64a50: ldur            x9, [fp, #-0x60]
    // 0xa64a54: ldur            x8, [fp, #-0x68]
    // 0xa64a58: ldur            x7, [fp, #-0x70]
    // 0xa64a5c: ldur            x6, [fp, #-0x78]
    // 0xa64a60: ldur            x5, [fp, #-0x80]
    // 0xa64a64: ldur            x4, [fp, #-0x88]
    // 0xa64a68: ldur            x3, [fp, #-0x90]
    // 0xa64a6c: mov             x0, x20
    // 0xa64a70: stur            x20, [fp, #-0x38]
    // 0xa64a74: r2 = Null
    //     0xa64a74: mov             x2, NULL
    // 0xa64a78: r1 = Null
    //     0xa64a78: mov             x1, NULL
    // 0xa64a7c: branchIfSmi(r0, 0xa64aa4)
    //     0xa64a7c: tbz             w0, #0, #0xa64aa4
    // 0xa64a80: r4 = LoadClassIdInstr(r0)
    //     0xa64a80: ldur            x4, [x0, #-1]
    //     0xa64a84: ubfx            x4, x4, #0xc, #0x14
    // 0xa64a88: sub             x4, x4, #0x3c
    // 0xa64a8c: cmp             x4, #1
    // 0xa64a90: b.ls            #0xa64aa4
    // 0xa64a94: r8 = int
    //     0xa64a94: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa64a98: r3 = Null
    //     0xa64a98: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f88] Null
    //     0xa64a9c: ldr             x3, [x3, #0xf88]
    // 0xa64aa0: r0 = int()
    //     0xa64aa0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa64aa4: r0 = PrayerTimeSetting()
    //     0xa64aa4: bl              #0x8a7594  ; AllocatePrayerTimeSettingStub -> PrayerTimeSetting (size=0x6c)
    // 0xa64aa8: mov             x1, x0
    // 0xa64aac: ldur            x0, [fp, #-0x18]
    // 0xa64ab0: stur            x1, [fp, #-0x98]
    // 0xa64ab4: StoreField: r1->field_1b = r0
    //     0xa64ab4: stur            w0, [x1, #0x1b]
    // 0xa64ab8: ldur            x0, [fp, #-0x20]
    // 0xa64abc: StoreField: r1->field_13 = r0
    //     0xa64abc: stur            w0, [x1, #0x13]
    // 0xa64ac0: ldur            x0, [fp, #-0x40]
    // 0xa64ac4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64ac4: stur            w0, [x1, #0x17]
    // 0xa64ac8: ldur            x0, [fp, #-0x48]
    // 0xa64acc: r2 = LoadInt32Instr(r0)
    //     0xa64acc: sbfx            x2, x0, #1, #0x1f
    //     0xa64ad0: tbz             w0, #0, #0xa64ad8
    //     0xa64ad4: ldur            x2, [x0, #7]
    // 0xa64ad8: StoreField: r1->field_33 = r2
    //     0xa64ad8: stur            x2, [x1, #0x33]
    // 0xa64adc: ldur            x0, [fp, #-0x50]
    // 0xa64ae0: LoadField: d0 = r0->field_7
    //     0xa64ae0: ldur            d0, [x0, #7]
    // 0xa64ae4: StoreField: r1->field_1f = d0
    //     0xa64ae4: stur            d0, [x1, #0x1f]
    // 0xa64ae8: ldur            x0, [fp, #-0x58]
    // 0xa64aec: StoreField: r1->field_27 = r0
    //     0xa64aec: stur            w0, [x1, #0x27]
    // 0xa64af0: ldur            x0, [fp, #-0x60]
    // 0xa64af4: r2 = LoadInt32Instr(r0)
    //     0xa64af4: sbfx            x2, x0, #1, #0x1f
    //     0xa64af8: tbz             w0, #0, #0xa64b00
    //     0xa64afc: ldur            x2, [x0, #7]
    // 0xa64b00: StoreField: r1->field_2b = r2
    //     0xa64b00: stur            x2, [x1, #0x2b]
    // 0xa64b04: ldur            x0, [fp, #-0x68]
    // 0xa64b08: StoreField: r1->field_3b = r0
    //     0xa64b08: stur            w0, [x1, #0x3b]
    // 0xa64b0c: ldur            x0, [fp, #-0x70]
    // 0xa64b10: StoreField: r1->field_3f = r0
    //     0xa64b10: stur            w0, [x1, #0x3f]
    // 0xa64b14: ldur            x0, [fp, #-0x78]
    // 0xa64b18: r2 = LoadInt32Instr(r0)
    //     0xa64b18: sbfx            x2, x0, #1, #0x1f
    //     0xa64b1c: tbz             w0, #0, #0xa64b24
    //     0xa64b20: ldur            x2, [x0, #7]
    // 0xa64b24: StoreField: r1->field_43 = r2
    //     0xa64b24: stur            x2, [x1, #0x43]
    // 0xa64b28: ldur            x0, [fp, #-0x80]
    // 0xa64b2c: r2 = LoadInt32Instr(r0)
    //     0xa64b2c: sbfx            x2, x0, #1, #0x1f
    //     0xa64b30: tbz             w0, #0, #0xa64b38
    //     0xa64b34: ldur            x2, [x0, #7]
    // 0xa64b38: StoreField: r1->field_4b = r2
    //     0xa64b38: stur            x2, [x1, #0x4b]
    // 0xa64b3c: ldur            x0, [fp, #-0x88]
    // 0xa64b40: r2 = LoadInt32Instr(r0)
    //     0xa64b40: sbfx            x2, x0, #1, #0x1f
    //     0xa64b44: tbz             w0, #0, #0xa64b4c
    //     0xa64b48: ldur            x2, [x0, #7]
    // 0xa64b4c: StoreField: r1->field_53 = r2
    //     0xa64b4c: stur            x2, [x1, #0x53]
    // 0xa64b50: ldur            x0, [fp, #-0x90]
    // 0xa64b54: r2 = LoadInt32Instr(r0)
    //     0xa64b54: sbfx            x2, x0, #1, #0x1f
    //     0xa64b58: tbz             w0, #0, #0xa64b60
    //     0xa64b5c: ldur            x2, [x0, #7]
    // 0xa64b60: StoreField: r1->field_5b = r2
    //     0xa64b60: stur            x2, [x1, #0x5b]
    // 0xa64b64: ldur            x0, [fp, #-0x38]
    // 0xa64b68: r2 = LoadInt32Instr(r0)
    //     0xa64b68: sbfx            x2, x0, #1, #0x1f
    //     0xa64b6c: tbz             w0, #0, #0xa64b74
    //     0xa64b70: ldur            x2, [x0, #7]
    // 0xa64b74: StoreField: r1->field_63 = r2
    //     0xa64b74: stur            x2, [x1, #0x63]
    // 0xa64b78: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa64b78: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa64b7c: ldr             x16, [x16, #0x9f8]
    // 0xa64b80: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64b84: stp             lr, x16, [SP]
    // 0xa64b88: r0 = Map._fromLiteral()
    //     0xa64b88: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa64b8c: ldur            x1, [fp, #-0x98]
    // 0xa64b90: StoreField: r1->field_f = r0
    //     0xa64b90: stur            w0, [x1, #0xf]
    //     0xa64b94: ldurb           w16, [x1, #-1]
    //     0xa64b98: ldurb           w17, [x0, #-1]
    //     0xa64b9c: and             x16, x17, x16, lsr #2
    //     0xa64ba0: tst             x16, HEAP, lsr #32
    //     0xa64ba4: b.eq            #0xa64bac
    //     0xa64ba8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa64bac: mov             x0, x1
    // 0xa64bb0: LeaveFrame
    //     0xa64bb0: mov             SP, fp
    //     0xa64bb4: ldp             fp, lr, [SP], #0x10
    // 0xa64bb8: ret
    //     0xa64bb8: ret             
    // 0xa64bbc: r0 = RangeError()
    //     0xa64bbc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64bc0: mov             x1, x0
    // 0xa64bc4: r0 = "Not enough bytes available."
    //     0xa64bc4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64bc8: ldr             x0, [x0, #0x8a8]
    // 0xa64bcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64bcc: stur            w0, [x1, #0x17]
    // 0xa64bd0: r2 = false
    //     0xa64bd0: add             x2, NULL, #0x30  ; false
    // 0xa64bd4: StoreField: r1->field_b = r2
    //     0xa64bd4: stur            w2, [x1, #0xb]
    // 0xa64bd8: mov             x0, x1
    // 0xa64bdc: r0 = Throw()
    //     0xa64bdc: bl              #0xec04b8  ; ThrowStub
    // 0xa64be0: brk             #0
    // 0xa64be4: r0 = "Not enough bytes available."
    //     0xa64be4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64be8: ldr             x0, [x0, #0x8a8]
    // 0xa64bec: r2 = false
    //     0xa64bec: add             x2, NULL, #0x30  ; false
    // 0xa64bf0: r0 = RangeError()
    //     0xa64bf0: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64bf4: mov             x1, x0
    // 0xa64bf8: r0 = "Not enough bytes available."
    //     0xa64bf8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64bfc: ldr             x0, [x0, #0x8a8]
    // 0xa64c00: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64c00: stur            w0, [x1, #0x17]
    // 0xa64c04: r0 = false
    //     0xa64c04: add             x0, NULL, #0x30  ; false
    // 0xa64c08: StoreField: r1->field_b = r0
    //     0xa64c08: stur            w0, [x1, #0xb]
    // 0xa64c0c: mov             x0, x1
    // 0xa64c10: r0 = Throw()
    //     0xa64c10: bl              #0xec04b8  ; ThrowStub
    // 0xa64c14: brk             #0
    // 0xa64c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64c1c: b               #0xa643cc
    // 0xa64c20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa64c20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa64c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64c24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64c28: b               #0xa64454
    // 0xa64c2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa64c2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd597c, size: 0x958
    // 0xbd597c: EnterFrame
    //     0xbd597c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd5980: mov             fp, SP
    // 0xbd5984: AllocStack(0x28)
    //     0xbd5984: sub             SP, SP, #0x28
    // 0xbd5988: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd5988: mov             x4, x2
    //     0xbd598c: stur            x2, [fp, #-8]
    //     0xbd5990: stur            x3, [fp, #-0x10]
    // 0xbd5994: CheckStackOverflow
    //     0xbd5994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd5998: cmp             SP, x16
    //     0xbd599c: b.ls            #0xbd6278
    // 0xbd59a0: mov             x0, x3
    // 0xbd59a4: r2 = Null
    //     0xbd59a4: mov             x2, NULL
    // 0xbd59a8: r1 = Null
    //     0xbd59a8: mov             x1, NULL
    // 0xbd59ac: r4 = 60
    //     0xbd59ac: movz            x4, #0x3c
    // 0xbd59b0: branchIfSmi(r0, 0xbd59bc)
    //     0xbd59b0: tbz             w0, #0, #0xbd59bc
    // 0xbd59b4: r4 = LoadClassIdInstr(r0)
    //     0xbd59b4: ldur            x4, [x0, #-1]
    //     0xbd59b8: ubfx            x4, x4, #0xc, #0x14
    // 0xbd59bc: cmp             x4, #0x643
    // 0xbd59c0: b.eq            #0xbd59d8
    // 0xbd59c4: r8 = PrayerTimeSetting
    //     0xbd59c4: add             x8, PP, #8, lsl #12  ; [pp+0x80a8] Type: PrayerTimeSetting
    //     0xbd59c8: ldr             x8, [x8, #0xa8]
    // 0xbd59cc: r3 = Null
    //     0xbd59cc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b1b0] Null
    //     0xbd59d0: ldr             x3, [x3, #0x1b0]
    // 0xbd59d4: r0 = PrayerTimeSetting()
    //     0xbd59d4: bl              #0x817acc  ; IsType_PrayerTimeSetting_Stub
    // 0xbd59d8: ldur            x0, [fp, #-8]
    // 0xbd59dc: LoadField: r1 = r0->field_b
    //     0xbd59dc: ldur            w1, [x0, #0xb]
    // 0xbd59e0: DecompressPointer r1
    //     0xbd59e0: add             x1, x1, HEAP, lsl #32
    // 0xbd59e4: LoadField: r2 = r1->field_13
    //     0xbd59e4: ldur            w2, [x1, #0x13]
    // 0xbd59e8: LoadField: r1 = r0->field_13
    //     0xbd59e8: ldur            x1, [x0, #0x13]
    // 0xbd59ec: r3 = LoadInt32Instr(r2)
    //     0xbd59ec: sbfx            x3, x2, #1, #0x1f
    // 0xbd59f0: sub             x2, x3, x1
    // 0xbd59f4: cmp             x2, #1
    // 0xbd59f8: b.ge            #0xbd5a08
    // 0xbd59fc: mov             x1, x0
    // 0xbd5a00: r2 = 1
    //     0xbd5a00: movz            x2, #0x1
    // 0xbd5a04: r0 = _increaseBufferSize()
    //     0xbd5a04: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5a08: ldur            x3, [fp, #-8]
    // 0xbd5a0c: r2 = 14
    //     0xbd5a0c: movz            x2, #0xe
    // 0xbd5a10: LoadField: r4 = r3->field_b
    //     0xbd5a10: ldur            w4, [x3, #0xb]
    // 0xbd5a14: DecompressPointer r4
    //     0xbd5a14: add             x4, x4, HEAP, lsl #32
    // 0xbd5a18: LoadField: r5 = r3->field_13
    //     0xbd5a18: ldur            x5, [x3, #0x13]
    // 0xbd5a1c: add             x6, x5, #1
    // 0xbd5a20: StoreField: r3->field_13 = r6
    //     0xbd5a20: stur            x6, [x3, #0x13]
    // 0xbd5a24: LoadField: r0 = r4->field_13
    //     0xbd5a24: ldur            w0, [x4, #0x13]
    // 0xbd5a28: r7 = LoadInt32Instr(r0)
    //     0xbd5a28: sbfx            x7, x0, #1, #0x1f
    // 0xbd5a2c: mov             x0, x7
    // 0xbd5a30: mov             x1, x5
    // 0xbd5a34: cmp             x1, x0
    // 0xbd5a38: b.hs            #0xbd6280
    // 0xbd5a3c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd5a3c: add             x0, x4, x5
    //     0xbd5a40: strb            w2, [x0, #0x17]
    // 0xbd5a44: sub             x0, x7, x6
    // 0xbd5a48: cmp             x0, #1
    // 0xbd5a4c: b.ge            #0xbd5a5c
    // 0xbd5a50: mov             x1, x3
    // 0xbd5a54: r2 = 1
    //     0xbd5a54: movz            x2, #0x1
    // 0xbd5a58: r0 = _increaseBufferSize()
    //     0xbd5a58: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5a5c: ldur            x2, [fp, #-8]
    // 0xbd5a60: ldur            x3, [fp, #-0x10]
    // 0xbd5a64: LoadField: r4 = r2->field_b
    //     0xbd5a64: ldur            w4, [x2, #0xb]
    // 0xbd5a68: DecompressPointer r4
    //     0xbd5a68: add             x4, x4, HEAP, lsl #32
    // 0xbd5a6c: LoadField: r5 = r2->field_13
    //     0xbd5a6c: ldur            x5, [x2, #0x13]
    // 0xbd5a70: add             x0, x5, #1
    // 0xbd5a74: StoreField: r2->field_13 = r0
    //     0xbd5a74: stur            x0, [x2, #0x13]
    // 0xbd5a78: LoadField: r0 = r4->field_13
    //     0xbd5a78: ldur            w0, [x4, #0x13]
    // 0xbd5a7c: r1 = LoadInt32Instr(r0)
    //     0xbd5a7c: sbfx            x1, x0, #1, #0x1f
    // 0xbd5a80: mov             x0, x1
    // 0xbd5a84: mov             x1, x5
    // 0xbd5a88: cmp             x1, x0
    // 0xbd5a8c: b.hs            #0xbd6284
    // 0xbd5a90: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd5a90: add             x0, x4, x5
    //     0xbd5a94: strb            wzr, [x0, #0x17]
    // 0xbd5a98: LoadField: r0 = r3->field_13
    //     0xbd5a98: ldur            w0, [x3, #0x13]
    // 0xbd5a9c: DecompressPointer r0
    //     0xbd5a9c: add             x0, x0, HEAP, lsl #32
    // 0xbd5aa0: r16 = <bool>
    //     0xbd5aa0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd5aa4: stp             x2, x16, [SP, #8]
    // 0xbd5aa8: str             x0, [SP]
    // 0xbd5aac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5aac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5ab0: r0 = write()
    //     0xbd5ab0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5ab4: ldur            x0, [fp, #-8]
    // 0xbd5ab8: LoadField: r1 = r0->field_b
    //     0xbd5ab8: ldur            w1, [x0, #0xb]
    // 0xbd5abc: DecompressPointer r1
    //     0xbd5abc: add             x1, x1, HEAP, lsl #32
    // 0xbd5ac0: LoadField: r2 = r1->field_13
    //     0xbd5ac0: ldur            w2, [x1, #0x13]
    // 0xbd5ac4: LoadField: r1 = r0->field_13
    //     0xbd5ac4: ldur            x1, [x0, #0x13]
    // 0xbd5ac8: r3 = LoadInt32Instr(r2)
    //     0xbd5ac8: sbfx            x3, x2, #1, #0x1f
    // 0xbd5acc: sub             x2, x3, x1
    // 0xbd5ad0: cmp             x2, #1
    // 0xbd5ad4: b.ge            #0xbd5ae4
    // 0xbd5ad8: mov             x1, x0
    // 0xbd5adc: r2 = 1
    //     0xbd5adc: movz            x2, #0x1
    // 0xbd5ae0: r0 = _increaseBufferSize()
    //     0xbd5ae0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5ae4: ldur            x2, [fp, #-8]
    // 0xbd5ae8: ldur            x3, [fp, #-0x10]
    // 0xbd5aec: r4 = 1
    //     0xbd5aec: movz            x4, #0x1
    // 0xbd5af0: LoadField: r5 = r2->field_b
    //     0xbd5af0: ldur            w5, [x2, #0xb]
    // 0xbd5af4: DecompressPointer r5
    //     0xbd5af4: add             x5, x5, HEAP, lsl #32
    // 0xbd5af8: LoadField: r6 = r2->field_13
    //     0xbd5af8: ldur            x6, [x2, #0x13]
    // 0xbd5afc: add             x0, x6, #1
    // 0xbd5b00: StoreField: r2->field_13 = r0
    //     0xbd5b00: stur            x0, [x2, #0x13]
    // 0xbd5b04: LoadField: r0 = r5->field_13
    //     0xbd5b04: ldur            w0, [x5, #0x13]
    // 0xbd5b08: r1 = LoadInt32Instr(r0)
    //     0xbd5b08: sbfx            x1, x0, #1, #0x1f
    // 0xbd5b0c: mov             x0, x1
    // 0xbd5b10: mov             x1, x6
    // 0xbd5b14: cmp             x1, x0
    // 0xbd5b18: b.hs            #0xbd6288
    // 0xbd5b1c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5b1c: add             x0, x5, x6
    //     0xbd5b20: strb            w4, [x0, #0x17]
    // 0xbd5b24: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd5b24: ldur            w0, [x3, #0x17]
    // 0xbd5b28: DecompressPointer r0
    //     0xbd5b28: add             x0, x0, HEAP, lsl #32
    // 0xbd5b2c: r16 = <bool>
    //     0xbd5b2c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd5b30: stp             x2, x16, [SP, #8]
    // 0xbd5b34: str             x0, [SP]
    // 0xbd5b38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5b38: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5b3c: r0 = write()
    //     0xbd5b3c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5b40: ldur            x0, [fp, #-8]
    // 0xbd5b44: LoadField: r1 = r0->field_b
    //     0xbd5b44: ldur            w1, [x0, #0xb]
    // 0xbd5b48: DecompressPointer r1
    //     0xbd5b48: add             x1, x1, HEAP, lsl #32
    // 0xbd5b4c: LoadField: r2 = r1->field_13
    //     0xbd5b4c: ldur            w2, [x1, #0x13]
    // 0xbd5b50: LoadField: r1 = r0->field_13
    //     0xbd5b50: ldur            x1, [x0, #0x13]
    // 0xbd5b54: r3 = LoadInt32Instr(r2)
    //     0xbd5b54: sbfx            x3, x2, #1, #0x1f
    // 0xbd5b58: sub             x2, x3, x1
    // 0xbd5b5c: cmp             x2, #1
    // 0xbd5b60: b.ge            #0xbd5b70
    // 0xbd5b64: mov             x1, x0
    // 0xbd5b68: r2 = 1
    //     0xbd5b68: movz            x2, #0x1
    // 0xbd5b6c: r0 = _increaseBufferSize()
    //     0xbd5b6c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5b70: ldur            x2, [fp, #-8]
    // 0xbd5b74: ldur            x3, [fp, #-0x10]
    // 0xbd5b78: r4 = 2
    //     0xbd5b78: movz            x4, #0x2
    // 0xbd5b7c: LoadField: r5 = r2->field_b
    //     0xbd5b7c: ldur            w5, [x2, #0xb]
    // 0xbd5b80: DecompressPointer r5
    //     0xbd5b80: add             x5, x5, HEAP, lsl #32
    // 0xbd5b84: LoadField: r6 = r2->field_13
    //     0xbd5b84: ldur            x6, [x2, #0x13]
    // 0xbd5b88: add             x0, x6, #1
    // 0xbd5b8c: StoreField: r2->field_13 = r0
    //     0xbd5b8c: stur            x0, [x2, #0x13]
    // 0xbd5b90: LoadField: r0 = r5->field_13
    //     0xbd5b90: ldur            w0, [x5, #0x13]
    // 0xbd5b94: r1 = LoadInt32Instr(r0)
    //     0xbd5b94: sbfx            x1, x0, #1, #0x1f
    // 0xbd5b98: mov             x0, x1
    // 0xbd5b9c: mov             x1, x6
    // 0xbd5ba0: cmp             x1, x0
    // 0xbd5ba4: b.hs            #0xbd628c
    // 0xbd5ba8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5ba8: add             x0, x5, x6
    //     0xbd5bac: strb            w4, [x0, #0x17]
    // 0xbd5bb0: LoadField: r0 = r3->field_1b
    //     0xbd5bb0: ldur            w0, [x3, #0x1b]
    // 0xbd5bb4: DecompressPointer r0
    //     0xbd5bb4: add             x0, x0, HEAP, lsl #32
    // 0xbd5bb8: r16 = <String>
    //     0xbd5bb8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd5bbc: stp             x2, x16, [SP, #8]
    // 0xbd5bc0: str             x0, [SP]
    // 0xbd5bc4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5bc4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5bc8: r0 = write()
    //     0xbd5bc8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5bcc: ldur            x0, [fp, #-8]
    // 0xbd5bd0: LoadField: r1 = r0->field_b
    //     0xbd5bd0: ldur            w1, [x0, #0xb]
    // 0xbd5bd4: DecompressPointer r1
    //     0xbd5bd4: add             x1, x1, HEAP, lsl #32
    // 0xbd5bd8: LoadField: r2 = r1->field_13
    //     0xbd5bd8: ldur            w2, [x1, #0x13]
    // 0xbd5bdc: LoadField: r1 = r0->field_13
    //     0xbd5bdc: ldur            x1, [x0, #0x13]
    // 0xbd5be0: r3 = LoadInt32Instr(r2)
    //     0xbd5be0: sbfx            x3, x2, #1, #0x1f
    // 0xbd5be4: sub             x2, x3, x1
    // 0xbd5be8: cmp             x2, #1
    // 0xbd5bec: b.ge            #0xbd5bfc
    // 0xbd5bf0: mov             x1, x0
    // 0xbd5bf4: r2 = 1
    //     0xbd5bf4: movz            x2, #0x1
    // 0xbd5bf8: r0 = _increaseBufferSize()
    //     0xbd5bf8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5bfc: ldur            x2, [fp, #-8]
    // 0xbd5c00: ldur            x3, [fp, #-0x10]
    // 0xbd5c04: r4 = 3
    //     0xbd5c04: movz            x4, #0x3
    // 0xbd5c08: LoadField: r5 = r2->field_b
    //     0xbd5c08: ldur            w5, [x2, #0xb]
    // 0xbd5c0c: DecompressPointer r5
    //     0xbd5c0c: add             x5, x5, HEAP, lsl #32
    // 0xbd5c10: LoadField: r6 = r2->field_13
    //     0xbd5c10: ldur            x6, [x2, #0x13]
    // 0xbd5c14: add             x0, x6, #1
    // 0xbd5c18: StoreField: r2->field_13 = r0
    //     0xbd5c18: stur            x0, [x2, #0x13]
    // 0xbd5c1c: LoadField: r0 = r5->field_13
    //     0xbd5c1c: ldur            w0, [x5, #0x13]
    // 0xbd5c20: r1 = LoadInt32Instr(r0)
    //     0xbd5c20: sbfx            x1, x0, #1, #0x1f
    // 0xbd5c24: mov             x0, x1
    // 0xbd5c28: mov             x1, x6
    // 0xbd5c2c: cmp             x1, x0
    // 0xbd5c30: b.hs            #0xbd6290
    // 0xbd5c34: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5c34: add             x0, x5, x6
    //     0xbd5c38: strb            w4, [x0, #0x17]
    // 0xbd5c3c: LoadField: d0 = r3->field_1f
    //     0xbd5c3c: ldur            d0, [x3, #0x1f]
    // 0xbd5c40: r0 = inline_Allocate_Double()
    //     0xbd5c40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd5c44: add             x0, x0, #0x10
    //     0xbd5c48: cmp             x1, x0
    //     0xbd5c4c: b.ls            #0xbd6294
    //     0xbd5c50: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd5c54: sub             x0, x0, #0xf
    //     0xbd5c58: movz            x1, #0xe15c
    //     0xbd5c5c: movk            x1, #0x3, lsl #16
    //     0xbd5c60: stur            x1, [x0, #-1]
    // 0xbd5c64: StoreField: r0->field_7 = d0
    //     0xbd5c64: stur            d0, [x0, #7]
    // 0xbd5c68: r16 = <double>
    //     0xbd5c68: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd5c6c: stp             x2, x16, [SP, #8]
    // 0xbd5c70: str             x0, [SP]
    // 0xbd5c74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5c74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5c78: r0 = write()
    //     0xbd5c78: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5c7c: ldur            x0, [fp, #-8]
    // 0xbd5c80: LoadField: r1 = r0->field_b
    //     0xbd5c80: ldur            w1, [x0, #0xb]
    // 0xbd5c84: DecompressPointer r1
    //     0xbd5c84: add             x1, x1, HEAP, lsl #32
    // 0xbd5c88: LoadField: r2 = r1->field_13
    //     0xbd5c88: ldur            w2, [x1, #0x13]
    // 0xbd5c8c: LoadField: r1 = r0->field_13
    //     0xbd5c8c: ldur            x1, [x0, #0x13]
    // 0xbd5c90: r3 = LoadInt32Instr(r2)
    //     0xbd5c90: sbfx            x3, x2, #1, #0x1f
    // 0xbd5c94: sub             x2, x3, x1
    // 0xbd5c98: cmp             x2, #1
    // 0xbd5c9c: b.ge            #0xbd5cac
    // 0xbd5ca0: mov             x1, x0
    // 0xbd5ca4: r2 = 1
    //     0xbd5ca4: movz            x2, #0x1
    // 0xbd5ca8: r0 = _increaseBufferSize()
    //     0xbd5ca8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5cac: ldur            x2, [fp, #-8]
    // 0xbd5cb0: ldur            x3, [fp, #-0x10]
    // 0xbd5cb4: r4 = 4
    //     0xbd5cb4: movz            x4, #0x4
    // 0xbd5cb8: LoadField: r5 = r2->field_b
    //     0xbd5cb8: ldur            w5, [x2, #0xb]
    // 0xbd5cbc: DecompressPointer r5
    //     0xbd5cbc: add             x5, x5, HEAP, lsl #32
    // 0xbd5cc0: LoadField: r6 = r2->field_13
    //     0xbd5cc0: ldur            x6, [x2, #0x13]
    // 0xbd5cc4: add             x0, x6, #1
    // 0xbd5cc8: StoreField: r2->field_13 = r0
    //     0xbd5cc8: stur            x0, [x2, #0x13]
    // 0xbd5ccc: LoadField: r0 = r5->field_13
    //     0xbd5ccc: ldur            w0, [x5, #0x13]
    // 0xbd5cd0: r1 = LoadInt32Instr(r0)
    //     0xbd5cd0: sbfx            x1, x0, #1, #0x1f
    // 0xbd5cd4: mov             x0, x1
    // 0xbd5cd8: mov             x1, x6
    // 0xbd5cdc: cmp             x1, x0
    // 0xbd5ce0: b.hs            #0xbd62ac
    // 0xbd5ce4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5ce4: add             x0, x5, x6
    //     0xbd5ce8: strb            w4, [x0, #0x17]
    // 0xbd5cec: LoadField: r0 = r3->field_27
    //     0xbd5cec: ldur            w0, [x3, #0x27]
    // 0xbd5cf0: DecompressPointer r0
    //     0xbd5cf0: add             x0, x0, HEAP, lsl #32
    // 0xbd5cf4: r16 = <double?>
    //     0xbd5cf4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] TypeArguments: <double?>
    //     0xbd5cf8: ldr             x16, [x16, #0x1c0]
    // 0xbd5cfc: stp             x2, x16, [SP, #8]
    // 0xbd5d00: str             x0, [SP]
    // 0xbd5d04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5d04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5d08: r0 = write()
    //     0xbd5d08: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5d0c: ldur            x0, [fp, #-8]
    // 0xbd5d10: LoadField: r1 = r0->field_b
    //     0xbd5d10: ldur            w1, [x0, #0xb]
    // 0xbd5d14: DecompressPointer r1
    //     0xbd5d14: add             x1, x1, HEAP, lsl #32
    // 0xbd5d18: LoadField: r2 = r1->field_13
    //     0xbd5d18: ldur            w2, [x1, #0x13]
    // 0xbd5d1c: LoadField: r1 = r0->field_13
    //     0xbd5d1c: ldur            x1, [x0, #0x13]
    // 0xbd5d20: r3 = LoadInt32Instr(r2)
    //     0xbd5d20: sbfx            x3, x2, #1, #0x1f
    // 0xbd5d24: sub             x2, x3, x1
    // 0xbd5d28: cmp             x2, #1
    // 0xbd5d2c: b.ge            #0xbd5d3c
    // 0xbd5d30: mov             x1, x0
    // 0xbd5d34: r2 = 1
    //     0xbd5d34: movz            x2, #0x1
    // 0xbd5d38: r0 = _increaseBufferSize()
    //     0xbd5d38: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5d3c: ldur            x2, [fp, #-8]
    // 0xbd5d40: ldur            x3, [fp, #-0x10]
    // 0xbd5d44: r4 = 5
    //     0xbd5d44: movz            x4, #0x5
    // 0xbd5d48: LoadField: r5 = r2->field_b
    //     0xbd5d48: ldur            w5, [x2, #0xb]
    // 0xbd5d4c: DecompressPointer r5
    //     0xbd5d4c: add             x5, x5, HEAP, lsl #32
    // 0xbd5d50: LoadField: r6 = r2->field_13
    //     0xbd5d50: ldur            x6, [x2, #0x13]
    // 0xbd5d54: add             x0, x6, #1
    // 0xbd5d58: StoreField: r2->field_13 = r0
    //     0xbd5d58: stur            x0, [x2, #0x13]
    // 0xbd5d5c: LoadField: r0 = r5->field_13
    //     0xbd5d5c: ldur            w0, [x5, #0x13]
    // 0xbd5d60: r1 = LoadInt32Instr(r0)
    //     0xbd5d60: sbfx            x1, x0, #1, #0x1f
    // 0xbd5d64: mov             x0, x1
    // 0xbd5d68: mov             x1, x6
    // 0xbd5d6c: cmp             x1, x0
    // 0xbd5d70: b.hs            #0xbd62b0
    // 0xbd5d74: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5d74: add             x0, x5, x6
    //     0xbd5d78: strb            w4, [x0, #0x17]
    // 0xbd5d7c: LoadField: r4 = r3->field_2b
    //     0xbd5d7c: ldur            x4, [x3, #0x2b]
    // 0xbd5d80: r0 = BoxInt64Instr(r4)
    //     0xbd5d80: sbfiz           x0, x4, #1, #0x1f
    //     0xbd5d84: cmp             x4, x0, asr #1
    //     0xbd5d88: b.eq            #0xbd5d94
    //     0xbd5d8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd5d90: stur            x4, [x0, #7]
    // 0xbd5d94: r16 = <int>
    //     0xbd5d94: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd5d98: stp             x2, x16, [SP, #8]
    // 0xbd5d9c: str             x0, [SP]
    // 0xbd5da0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5da0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5da4: r0 = write()
    //     0xbd5da4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5da8: ldur            x0, [fp, #-8]
    // 0xbd5dac: LoadField: r1 = r0->field_b
    //     0xbd5dac: ldur            w1, [x0, #0xb]
    // 0xbd5db0: DecompressPointer r1
    //     0xbd5db0: add             x1, x1, HEAP, lsl #32
    // 0xbd5db4: LoadField: r2 = r1->field_13
    //     0xbd5db4: ldur            w2, [x1, #0x13]
    // 0xbd5db8: LoadField: r1 = r0->field_13
    //     0xbd5db8: ldur            x1, [x0, #0x13]
    // 0xbd5dbc: r3 = LoadInt32Instr(r2)
    //     0xbd5dbc: sbfx            x3, x2, #1, #0x1f
    // 0xbd5dc0: sub             x2, x3, x1
    // 0xbd5dc4: cmp             x2, #1
    // 0xbd5dc8: b.ge            #0xbd5dd8
    // 0xbd5dcc: mov             x1, x0
    // 0xbd5dd0: r2 = 1
    //     0xbd5dd0: movz            x2, #0x1
    // 0xbd5dd4: r0 = _increaseBufferSize()
    //     0xbd5dd4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5dd8: ldur            x2, [fp, #-8]
    // 0xbd5ddc: ldur            x3, [fp, #-0x10]
    // 0xbd5de0: r4 = 6
    //     0xbd5de0: movz            x4, #0x6
    // 0xbd5de4: LoadField: r5 = r2->field_b
    //     0xbd5de4: ldur            w5, [x2, #0xb]
    // 0xbd5de8: DecompressPointer r5
    //     0xbd5de8: add             x5, x5, HEAP, lsl #32
    // 0xbd5dec: LoadField: r6 = r2->field_13
    //     0xbd5dec: ldur            x6, [x2, #0x13]
    // 0xbd5df0: add             x0, x6, #1
    // 0xbd5df4: StoreField: r2->field_13 = r0
    //     0xbd5df4: stur            x0, [x2, #0x13]
    // 0xbd5df8: LoadField: r0 = r5->field_13
    //     0xbd5df8: ldur            w0, [x5, #0x13]
    // 0xbd5dfc: r1 = LoadInt32Instr(r0)
    //     0xbd5dfc: sbfx            x1, x0, #1, #0x1f
    // 0xbd5e00: mov             x0, x1
    // 0xbd5e04: mov             x1, x6
    // 0xbd5e08: cmp             x1, x0
    // 0xbd5e0c: b.hs            #0xbd62b4
    // 0xbd5e10: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5e10: add             x0, x5, x6
    //     0xbd5e14: strb            w4, [x0, #0x17]
    // 0xbd5e18: LoadField: r4 = r3->field_33
    //     0xbd5e18: ldur            x4, [x3, #0x33]
    // 0xbd5e1c: r0 = BoxInt64Instr(r4)
    //     0xbd5e1c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd5e20: cmp             x4, x0, asr #1
    //     0xbd5e24: b.eq            #0xbd5e30
    //     0xbd5e28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd5e2c: stur            x4, [x0, #7]
    // 0xbd5e30: r16 = <int>
    //     0xbd5e30: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd5e34: stp             x2, x16, [SP, #8]
    // 0xbd5e38: str             x0, [SP]
    // 0xbd5e3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5e3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5e40: r0 = write()
    //     0xbd5e40: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5e44: ldur            x0, [fp, #-8]
    // 0xbd5e48: LoadField: r1 = r0->field_b
    //     0xbd5e48: ldur            w1, [x0, #0xb]
    // 0xbd5e4c: DecompressPointer r1
    //     0xbd5e4c: add             x1, x1, HEAP, lsl #32
    // 0xbd5e50: LoadField: r2 = r1->field_13
    //     0xbd5e50: ldur            w2, [x1, #0x13]
    // 0xbd5e54: LoadField: r1 = r0->field_13
    //     0xbd5e54: ldur            x1, [x0, #0x13]
    // 0xbd5e58: r3 = LoadInt32Instr(r2)
    //     0xbd5e58: sbfx            x3, x2, #1, #0x1f
    // 0xbd5e5c: sub             x2, x3, x1
    // 0xbd5e60: cmp             x2, #1
    // 0xbd5e64: b.ge            #0xbd5e74
    // 0xbd5e68: mov             x1, x0
    // 0xbd5e6c: r2 = 1
    //     0xbd5e6c: movz            x2, #0x1
    // 0xbd5e70: r0 = _increaseBufferSize()
    //     0xbd5e70: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5e74: ldur            x2, [fp, #-8]
    // 0xbd5e78: ldur            x3, [fp, #-0x10]
    // 0xbd5e7c: r4 = 7
    //     0xbd5e7c: movz            x4, #0x7
    // 0xbd5e80: LoadField: r5 = r2->field_b
    //     0xbd5e80: ldur            w5, [x2, #0xb]
    // 0xbd5e84: DecompressPointer r5
    //     0xbd5e84: add             x5, x5, HEAP, lsl #32
    // 0xbd5e88: LoadField: r6 = r2->field_13
    //     0xbd5e88: ldur            x6, [x2, #0x13]
    // 0xbd5e8c: add             x0, x6, #1
    // 0xbd5e90: StoreField: r2->field_13 = r0
    //     0xbd5e90: stur            x0, [x2, #0x13]
    // 0xbd5e94: LoadField: r0 = r5->field_13
    //     0xbd5e94: ldur            w0, [x5, #0x13]
    // 0xbd5e98: r1 = LoadInt32Instr(r0)
    //     0xbd5e98: sbfx            x1, x0, #1, #0x1f
    // 0xbd5e9c: mov             x0, x1
    // 0xbd5ea0: mov             x1, x6
    // 0xbd5ea4: cmp             x1, x0
    // 0xbd5ea8: b.hs            #0xbd62b8
    // 0xbd5eac: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5eac: add             x0, x5, x6
    //     0xbd5eb0: strb            w4, [x0, #0x17]
    // 0xbd5eb4: LoadField: r0 = r3->field_3b
    //     0xbd5eb4: ldur            w0, [x3, #0x3b]
    // 0xbd5eb8: DecompressPointer r0
    //     0xbd5eb8: add             x0, x0, HEAP, lsl #32
    // 0xbd5ebc: r16 = <String>
    //     0xbd5ebc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd5ec0: stp             x2, x16, [SP, #8]
    // 0xbd5ec4: str             x0, [SP]
    // 0xbd5ec8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5ec8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5ecc: r0 = write()
    //     0xbd5ecc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5ed0: ldur            x0, [fp, #-8]
    // 0xbd5ed4: LoadField: r1 = r0->field_b
    //     0xbd5ed4: ldur            w1, [x0, #0xb]
    // 0xbd5ed8: DecompressPointer r1
    //     0xbd5ed8: add             x1, x1, HEAP, lsl #32
    // 0xbd5edc: LoadField: r2 = r1->field_13
    //     0xbd5edc: ldur            w2, [x1, #0x13]
    // 0xbd5ee0: LoadField: r1 = r0->field_13
    //     0xbd5ee0: ldur            x1, [x0, #0x13]
    // 0xbd5ee4: r3 = LoadInt32Instr(r2)
    //     0xbd5ee4: sbfx            x3, x2, #1, #0x1f
    // 0xbd5ee8: sub             x2, x3, x1
    // 0xbd5eec: cmp             x2, #1
    // 0xbd5ef0: b.ge            #0xbd5f00
    // 0xbd5ef4: mov             x1, x0
    // 0xbd5ef8: r2 = 1
    //     0xbd5ef8: movz            x2, #0x1
    // 0xbd5efc: r0 = _increaseBufferSize()
    //     0xbd5efc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5f00: ldur            x2, [fp, #-8]
    // 0xbd5f04: ldur            x3, [fp, #-0x10]
    // 0xbd5f08: r4 = 8
    //     0xbd5f08: movz            x4, #0x8
    // 0xbd5f0c: LoadField: r5 = r2->field_b
    //     0xbd5f0c: ldur            w5, [x2, #0xb]
    // 0xbd5f10: DecompressPointer r5
    //     0xbd5f10: add             x5, x5, HEAP, lsl #32
    // 0xbd5f14: LoadField: r6 = r2->field_13
    //     0xbd5f14: ldur            x6, [x2, #0x13]
    // 0xbd5f18: add             x0, x6, #1
    // 0xbd5f1c: StoreField: r2->field_13 = r0
    //     0xbd5f1c: stur            x0, [x2, #0x13]
    // 0xbd5f20: LoadField: r0 = r5->field_13
    //     0xbd5f20: ldur            w0, [x5, #0x13]
    // 0xbd5f24: r1 = LoadInt32Instr(r0)
    //     0xbd5f24: sbfx            x1, x0, #1, #0x1f
    // 0xbd5f28: mov             x0, x1
    // 0xbd5f2c: mov             x1, x6
    // 0xbd5f30: cmp             x1, x0
    // 0xbd5f34: b.hs            #0xbd62bc
    // 0xbd5f38: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5f38: add             x0, x5, x6
    //     0xbd5f3c: strb            w4, [x0, #0x17]
    // 0xbd5f40: LoadField: r0 = r3->field_3f
    //     0xbd5f40: ldur            w0, [x3, #0x3f]
    // 0xbd5f44: DecompressPointer r0
    //     0xbd5f44: add             x0, x0, HEAP, lsl #32
    // 0xbd5f48: r16 = <String>
    //     0xbd5f48: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd5f4c: stp             x2, x16, [SP, #8]
    // 0xbd5f50: str             x0, [SP]
    // 0xbd5f54: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5f54: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5f58: r0 = write()
    //     0xbd5f58: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5f5c: ldur            x0, [fp, #-8]
    // 0xbd5f60: LoadField: r1 = r0->field_b
    //     0xbd5f60: ldur            w1, [x0, #0xb]
    // 0xbd5f64: DecompressPointer r1
    //     0xbd5f64: add             x1, x1, HEAP, lsl #32
    // 0xbd5f68: LoadField: r2 = r1->field_13
    //     0xbd5f68: ldur            w2, [x1, #0x13]
    // 0xbd5f6c: LoadField: r1 = r0->field_13
    //     0xbd5f6c: ldur            x1, [x0, #0x13]
    // 0xbd5f70: r3 = LoadInt32Instr(r2)
    //     0xbd5f70: sbfx            x3, x2, #1, #0x1f
    // 0xbd5f74: sub             x2, x3, x1
    // 0xbd5f78: cmp             x2, #1
    // 0xbd5f7c: b.ge            #0xbd5f8c
    // 0xbd5f80: mov             x1, x0
    // 0xbd5f84: r2 = 1
    //     0xbd5f84: movz            x2, #0x1
    // 0xbd5f88: r0 = _increaseBufferSize()
    //     0xbd5f88: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd5f8c: ldur            x2, [fp, #-8]
    // 0xbd5f90: ldur            x3, [fp, #-0x10]
    // 0xbd5f94: r4 = 9
    //     0xbd5f94: movz            x4, #0x9
    // 0xbd5f98: LoadField: r5 = r2->field_b
    //     0xbd5f98: ldur            w5, [x2, #0xb]
    // 0xbd5f9c: DecompressPointer r5
    //     0xbd5f9c: add             x5, x5, HEAP, lsl #32
    // 0xbd5fa0: LoadField: r6 = r2->field_13
    //     0xbd5fa0: ldur            x6, [x2, #0x13]
    // 0xbd5fa4: add             x0, x6, #1
    // 0xbd5fa8: StoreField: r2->field_13 = r0
    //     0xbd5fa8: stur            x0, [x2, #0x13]
    // 0xbd5fac: LoadField: r0 = r5->field_13
    //     0xbd5fac: ldur            w0, [x5, #0x13]
    // 0xbd5fb0: r1 = LoadInt32Instr(r0)
    //     0xbd5fb0: sbfx            x1, x0, #1, #0x1f
    // 0xbd5fb4: mov             x0, x1
    // 0xbd5fb8: mov             x1, x6
    // 0xbd5fbc: cmp             x1, x0
    // 0xbd5fc0: b.hs            #0xbd62c0
    // 0xbd5fc4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd5fc4: add             x0, x5, x6
    //     0xbd5fc8: strb            w4, [x0, #0x17]
    // 0xbd5fcc: LoadField: r4 = r3->field_43
    //     0xbd5fcc: ldur            x4, [x3, #0x43]
    // 0xbd5fd0: r0 = BoxInt64Instr(r4)
    //     0xbd5fd0: sbfiz           x0, x4, #1, #0x1f
    //     0xbd5fd4: cmp             x4, x0, asr #1
    //     0xbd5fd8: b.eq            #0xbd5fe4
    //     0xbd5fdc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd5fe0: stur            x4, [x0, #7]
    // 0xbd5fe4: r16 = <int>
    //     0xbd5fe4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd5fe8: stp             x2, x16, [SP, #8]
    // 0xbd5fec: str             x0, [SP]
    // 0xbd5ff0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd5ff0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd5ff4: r0 = write()
    //     0xbd5ff4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd5ff8: ldur            x0, [fp, #-8]
    // 0xbd5ffc: LoadField: r1 = r0->field_b
    //     0xbd5ffc: ldur            w1, [x0, #0xb]
    // 0xbd6000: DecompressPointer r1
    //     0xbd6000: add             x1, x1, HEAP, lsl #32
    // 0xbd6004: LoadField: r2 = r1->field_13
    //     0xbd6004: ldur            w2, [x1, #0x13]
    // 0xbd6008: LoadField: r1 = r0->field_13
    //     0xbd6008: ldur            x1, [x0, #0x13]
    // 0xbd600c: r3 = LoadInt32Instr(r2)
    //     0xbd600c: sbfx            x3, x2, #1, #0x1f
    // 0xbd6010: sub             x2, x3, x1
    // 0xbd6014: cmp             x2, #1
    // 0xbd6018: b.ge            #0xbd6028
    // 0xbd601c: mov             x1, x0
    // 0xbd6020: r2 = 1
    //     0xbd6020: movz            x2, #0x1
    // 0xbd6024: r0 = _increaseBufferSize()
    //     0xbd6024: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6028: ldur            x2, [fp, #-8]
    // 0xbd602c: ldur            x3, [fp, #-0x10]
    // 0xbd6030: r4 = 10
    //     0xbd6030: movz            x4, #0xa
    // 0xbd6034: LoadField: r5 = r2->field_b
    //     0xbd6034: ldur            w5, [x2, #0xb]
    // 0xbd6038: DecompressPointer r5
    //     0xbd6038: add             x5, x5, HEAP, lsl #32
    // 0xbd603c: LoadField: r6 = r2->field_13
    //     0xbd603c: ldur            x6, [x2, #0x13]
    // 0xbd6040: add             x0, x6, #1
    // 0xbd6044: StoreField: r2->field_13 = r0
    //     0xbd6044: stur            x0, [x2, #0x13]
    // 0xbd6048: LoadField: r0 = r5->field_13
    //     0xbd6048: ldur            w0, [x5, #0x13]
    // 0xbd604c: r1 = LoadInt32Instr(r0)
    //     0xbd604c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6050: mov             x0, x1
    // 0xbd6054: mov             x1, x6
    // 0xbd6058: cmp             x1, x0
    // 0xbd605c: b.hs            #0xbd62c4
    // 0xbd6060: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6060: add             x0, x5, x6
    //     0xbd6064: strb            w4, [x0, #0x17]
    // 0xbd6068: LoadField: r4 = r3->field_4b
    //     0xbd6068: ldur            x4, [x3, #0x4b]
    // 0xbd606c: r0 = BoxInt64Instr(r4)
    //     0xbd606c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6070: cmp             x4, x0, asr #1
    //     0xbd6074: b.eq            #0xbd6080
    //     0xbd6078: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd607c: stur            x4, [x0, #7]
    // 0xbd6080: r16 = <int>
    //     0xbd6080: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6084: stp             x2, x16, [SP, #8]
    // 0xbd6088: str             x0, [SP]
    // 0xbd608c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd608c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6090: r0 = write()
    //     0xbd6090: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6094: ldur            x0, [fp, #-8]
    // 0xbd6098: LoadField: r1 = r0->field_b
    //     0xbd6098: ldur            w1, [x0, #0xb]
    // 0xbd609c: DecompressPointer r1
    //     0xbd609c: add             x1, x1, HEAP, lsl #32
    // 0xbd60a0: LoadField: r2 = r1->field_13
    //     0xbd60a0: ldur            w2, [x1, #0x13]
    // 0xbd60a4: LoadField: r1 = r0->field_13
    //     0xbd60a4: ldur            x1, [x0, #0x13]
    // 0xbd60a8: r3 = LoadInt32Instr(r2)
    //     0xbd60a8: sbfx            x3, x2, #1, #0x1f
    // 0xbd60ac: sub             x2, x3, x1
    // 0xbd60b0: cmp             x2, #1
    // 0xbd60b4: b.ge            #0xbd60c4
    // 0xbd60b8: mov             x1, x0
    // 0xbd60bc: r2 = 1
    //     0xbd60bc: movz            x2, #0x1
    // 0xbd60c0: r0 = _increaseBufferSize()
    //     0xbd60c0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd60c4: ldur            x2, [fp, #-8]
    // 0xbd60c8: ldur            x3, [fp, #-0x10]
    // 0xbd60cc: r4 = 11
    //     0xbd60cc: movz            x4, #0xb
    // 0xbd60d0: LoadField: r5 = r2->field_b
    //     0xbd60d0: ldur            w5, [x2, #0xb]
    // 0xbd60d4: DecompressPointer r5
    //     0xbd60d4: add             x5, x5, HEAP, lsl #32
    // 0xbd60d8: LoadField: r6 = r2->field_13
    //     0xbd60d8: ldur            x6, [x2, #0x13]
    // 0xbd60dc: add             x0, x6, #1
    // 0xbd60e0: StoreField: r2->field_13 = r0
    //     0xbd60e0: stur            x0, [x2, #0x13]
    // 0xbd60e4: LoadField: r0 = r5->field_13
    //     0xbd60e4: ldur            w0, [x5, #0x13]
    // 0xbd60e8: r1 = LoadInt32Instr(r0)
    //     0xbd60e8: sbfx            x1, x0, #1, #0x1f
    // 0xbd60ec: mov             x0, x1
    // 0xbd60f0: mov             x1, x6
    // 0xbd60f4: cmp             x1, x0
    // 0xbd60f8: b.hs            #0xbd62c8
    // 0xbd60fc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd60fc: add             x0, x5, x6
    //     0xbd6100: strb            w4, [x0, #0x17]
    // 0xbd6104: LoadField: r4 = r3->field_53
    //     0xbd6104: ldur            x4, [x3, #0x53]
    // 0xbd6108: r0 = BoxInt64Instr(r4)
    //     0xbd6108: sbfiz           x0, x4, #1, #0x1f
    //     0xbd610c: cmp             x4, x0, asr #1
    //     0xbd6110: b.eq            #0xbd611c
    //     0xbd6114: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6118: stur            x4, [x0, #7]
    // 0xbd611c: r16 = <int>
    //     0xbd611c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6120: stp             x2, x16, [SP, #8]
    // 0xbd6124: str             x0, [SP]
    // 0xbd6128: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6128: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd612c: r0 = write()
    //     0xbd612c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6130: ldur            x0, [fp, #-8]
    // 0xbd6134: LoadField: r1 = r0->field_b
    //     0xbd6134: ldur            w1, [x0, #0xb]
    // 0xbd6138: DecompressPointer r1
    //     0xbd6138: add             x1, x1, HEAP, lsl #32
    // 0xbd613c: LoadField: r2 = r1->field_13
    //     0xbd613c: ldur            w2, [x1, #0x13]
    // 0xbd6140: LoadField: r1 = r0->field_13
    //     0xbd6140: ldur            x1, [x0, #0x13]
    // 0xbd6144: r3 = LoadInt32Instr(r2)
    //     0xbd6144: sbfx            x3, x2, #1, #0x1f
    // 0xbd6148: sub             x2, x3, x1
    // 0xbd614c: cmp             x2, #1
    // 0xbd6150: b.ge            #0xbd6160
    // 0xbd6154: mov             x1, x0
    // 0xbd6158: r2 = 1
    //     0xbd6158: movz            x2, #0x1
    // 0xbd615c: r0 = _increaseBufferSize()
    //     0xbd615c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6160: ldur            x2, [fp, #-8]
    // 0xbd6164: ldur            x3, [fp, #-0x10]
    // 0xbd6168: r4 = 12
    //     0xbd6168: movz            x4, #0xc
    // 0xbd616c: LoadField: r5 = r2->field_b
    //     0xbd616c: ldur            w5, [x2, #0xb]
    // 0xbd6170: DecompressPointer r5
    //     0xbd6170: add             x5, x5, HEAP, lsl #32
    // 0xbd6174: LoadField: r6 = r2->field_13
    //     0xbd6174: ldur            x6, [x2, #0x13]
    // 0xbd6178: add             x0, x6, #1
    // 0xbd617c: StoreField: r2->field_13 = r0
    //     0xbd617c: stur            x0, [x2, #0x13]
    // 0xbd6180: LoadField: r0 = r5->field_13
    //     0xbd6180: ldur            w0, [x5, #0x13]
    // 0xbd6184: r1 = LoadInt32Instr(r0)
    //     0xbd6184: sbfx            x1, x0, #1, #0x1f
    // 0xbd6188: mov             x0, x1
    // 0xbd618c: mov             x1, x6
    // 0xbd6190: cmp             x1, x0
    // 0xbd6194: b.hs            #0xbd62cc
    // 0xbd6198: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6198: add             x0, x5, x6
    //     0xbd619c: strb            w4, [x0, #0x17]
    // 0xbd61a0: LoadField: r4 = r3->field_5b
    //     0xbd61a0: ldur            x4, [x3, #0x5b]
    // 0xbd61a4: r0 = BoxInt64Instr(r4)
    //     0xbd61a4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd61a8: cmp             x4, x0, asr #1
    //     0xbd61ac: b.eq            #0xbd61b8
    //     0xbd61b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd61b4: stur            x4, [x0, #7]
    // 0xbd61b8: r16 = <int>
    //     0xbd61b8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd61bc: stp             x2, x16, [SP, #8]
    // 0xbd61c0: str             x0, [SP]
    // 0xbd61c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd61c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd61c8: r0 = write()
    //     0xbd61c8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd61cc: ldur            x0, [fp, #-8]
    // 0xbd61d0: LoadField: r1 = r0->field_b
    //     0xbd61d0: ldur            w1, [x0, #0xb]
    // 0xbd61d4: DecompressPointer r1
    //     0xbd61d4: add             x1, x1, HEAP, lsl #32
    // 0xbd61d8: LoadField: r2 = r1->field_13
    //     0xbd61d8: ldur            w2, [x1, #0x13]
    // 0xbd61dc: LoadField: r1 = r0->field_13
    //     0xbd61dc: ldur            x1, [x0, #0x13]
    // 0xbd61e0: r3 = LoadInt32Instr(r2)
    //     0xbd61e0: sbfx            x3, x2, #1, #0x1f
    // 0xbd61e4: sub             x2, x3, x1
    // 0xbd61e8: cmp             x2, #1
    // 0xbd61ec: b.ge            #0xbd61fc
    // 0xbd61f0: mov             x1, x0
    // 0xbd61f4: r2 = 1
    //     0xbd61f4: movz            x2, #0x1
    // 0xbd61f8: r0 = _increaseBufferSize()
    //     0xbd61f8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd61fc: ldur            x2, [fp, #-8]
    // 0xbd6200: ldur            x3, [fp, #-0x10]
    // 0xbd6204: r4 = 13
    //     0xbd6204: movz            x4, #0xd
    // 0xbd6208: LoadField: r5 = r2->field_b
    //     0xbd6208: ldur            w5, [x2, #0xb]
    // 0xbd620c: DecompressPointer r5
    //     0xbd620c: add             x5, x5, HEAP, lsl #32
    // 0xbd6210: LoadField: r6 = r2->field_13
    //     0xbd6210: ldur            x6, [x2, #0x13]
    // 0xbd6214: add             x0, x6, #1
    // 0xbd6218: StoreField: r2->field_13 = r0
    //     0xbd6218: stur            x0, [x2, #0x13]
    // 0xbd621c: LoadField: r0 = r5->field_13
    //     0xbd621c: ldur            w0, [x5, #0x13]
    // 0xbd6220: r1 = LoadInt32Instr(r0)
    //     0xbd6220: sbfx            x1, x0, #1, #0x1f
    // 0xbd6224: mov             x0, x1
    // 0xbd6228: mov             x1, x6
    // 0xbd622c: cmp             x1, x0
    // 0xbd6230: b.hs            #0xbd62d0
    // 0xbd6234: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6234: add             x0, x5, x6
    //     0xbd6238: strb            w4, [x0, #0x17]
    // 0xbd623c: LoadField: r4 = r3->field_63
    //     0xbd623c: ldur            x4, [x3, #0x63]
    // 0xbd6240: r0 = BoxInt64Instr(r4)
    //     0xbd6240: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6244: cmp             x4, x0, asr #1
    //     0xbd6248: b.eq            #0xbd6254
    //     0xbd624c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6250: stur            x4, [x0, #7]
    // 0xbd6254: r16 = <int>
    //     0xbd6254: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6258: stp             x2, x16, [SP, #8]
    // 0xbd625c: str             x0, [SP]
    // 0xbd6260: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6260: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6264: r0 = write()
    //     0xbd6264: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6268: r0 = Null
    //     0xbd6268: mov             x0, NULL
    // 0xbd626c: LeaveFrame
    //     0xbd626c: mov             SP, fp
    //     0xbd6270: ldp             fp, lr, [SP], #0x10
    // 0xbd6274: ret
    //     0xbd6274: ret             
    // 0xbd6278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd627c: b               #0xbd59a0
    // 0xbd6280: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6280: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6284: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6284: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6288: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6288: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd628c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd628c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6290: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6290: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6294: SaveReg d0
    //     0xbd6294: str             q0, [SP, #-0x10]!
    // 0xbd6298: stp             x2, x3, [SP, #-0x10]!
    // 0xbd629c: r0 = AllocateDouble()
    //     0xbd629c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd62a0: ldp             x2, x3, [SP], #0x10
    // 0xbd62a4: RestoreReg d0
    //     0xbd62a4: ldr             q0, [SP], #0x10
    // 0xbd62a8: b               #0xbd5c64
    // 0xbd62ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd62d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd62d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0288, size: 0x24
    // 0xbf0288: r1 = 40
    //     0xbf0288: movz            x1, #0x28
    // 0xbf028c: r16 = LoadInt32Instr(r1)
    //     0xbf028c: sbfx            x16, x1, #1, #0x1f
    // 0xbf0290: r17 = 11601
    //     0xbf0290: movz            x17, #0x2d51
    // 0xbf0294: mul             x0, x16, x17
    // 0xbf0298: umulh           x16, x16, x17
    // 0xbf029c: eor             x0, x0, x16
    // 0xbf02a0: r0 = 0
    //     0xbf02a0: eor             x0, x0, x0, lsr #32
    // 0xbf02a4: ubfiz           x0, x0, #1, #0x1e
    // 0xbf02a8: ret
    //     0xbf02a8: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76b6c, size: 0x9c
    // 0xd76b6c: EnterFrame
    //     0xd76b6c: stp             fp, lr, [SP, #-0x10]!
    //     0xd76b70: mov             fp, SP
    // 0xd76b74: AllocStack(0x10)
    //     0xd76b74: sub             SP, SP, #0x10
    // 0xd76b78: CheckStackOverflow
    //     0xd76b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76b7c: cmp             SP, x16
    //     0xd76b80: b.ls            #0xd76c00
    // 0xd76b84: ldr             x0, [fp, #0x10]
    // 0xd76b88: cmp             w0, NULL
    // 0xd76b8c: b.ne            #0xd76ba0
    // 0xd76b90: r0 = false
    //     0xd76b90: add             x0, NULL, #0x30  ; false
    // 0xd76b94: LeaveFrame
    //     0xd76b94: mov             SP, fp
    //     0xd76b98: ldp             fp, lr, [SP], #0x10
    // 0xd76b9c: ret
    //     0xd76b9c: ret             
    // 0xd76ba0: ldr             x1, [fp, #0x18]
    // 0xd76ba4: cmp             w1, w0
    // 0xd76ba8: b.ne            #0xd76bb4
    // 0xd76bac: r0 = true
    //     0xd76bac: add             x0, NULL, #0x20  ; true
    // 0xd76bb0: b               #0xd76bf4
    // 0xd76bb4: r1 = 60
    //     0xd76bb4: movz            x1, #0x3c
    // 0xd76bb8: branchIfSmi(r0, 0xd76bc4)
    //     0xd76bb8: tbz             w0, #0, #0xd76bc4
    // 0xd76bbc: r1 = LoadClassIdInstr(r0)
    //     0xd76bbc: ldur            x1, [x0, #-1]
    //     0xd76bc0: ubfx            x1, x1, #0xc, #0x14
    // 0xd76bc4: cmp             x1, #0x674
    // 0xd76bc8: b.ne            #0xd76bf0
    // 0xd76bcc: r16 = PrayerTimeSettingAdapter
    //     0xd76bcc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1a8] Type: PrayerTimeSettingAdapter
    //     0xd76bd0: ldr             x16, [x16, #0x1a8]
    // 0xd76bd4: r30 = PrayerTimeSettingAdapter
    //     0xd76bd4: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b1a8] Type: PrayerTimeSettingAdapter
    //     0xd76bd8: ldr             lr, [lr, #0x1a8]
    // 0xd76bdc: stp             lr, x16, [SP]
    // 0xd76be0: r0 = ==()
    //     0xd76be0: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76be4: tbnz            w0, #4, #0xd76bf0
    // 0xd76be8: r0 = true
    //     0xd76be8: add             x0, NULL, #0x20  ; true
    // 0xd76bec: b               #0xd76bf4
    // 0xd76bf0: r0 = false
    //     0xd76bf0: add             x0, NULL, #0x30  ; false
    // 0xd76bf4: LeaveFrame
    //     0xd76bf4: mov             SP, fp
    //     0xd76bf8: ldp             fp, lr, [SP], #0x10
    // 0xd76bfc: ret
    //     0xd76bfc: ret             
    // 0xd76c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76c00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76c04: b               #0xd76b84
  }
}
