// lib: , url: package:nuonline/app/data/models/prayer_time_widget.dart

// class id: 1050040, size: 0x8
class :: {
}

// class id: 1132, size: 0x38, field offset: 0x8
class PrayerTimeWidget extends Object {

  _ toMap(/* No info */) {
    // ** addr: 0x81eed0, size: 0x344
    // 0x81eed0: EnterFrame
    //     0x81eed0: stp             fp, lr, [SP, #-0x10]!
    //     0x81eed4: mov             fp, SP
    // 0x81eed8: AllocStack(0x28)
    //     0x81eed8: sub             SP, SP, #0x28
    // 0x81eedc: SetupParameters(PrayerTimeWidget this /* r1 => r1, fp-0x8 */)
    //     0x81eedc: stur            x1, [fp, #-8]
    // 0x81eee0: CheckStackOverflow
    //     0x81eee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81eee4: cmp             SP, x16
    //     0x81eee8: b.ls            #0x81f20c
    // 0x81eeec: r16 = <String, dynamic>
    //     0x81eeec: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x81eef0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x81eef4: stp             lr, x16, [SP]
    // 0x81eef8: r0 = Map._fromLiteral()
    //     0x81eef8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x81eefc: mov             x1, x0
    // 0x81ef00: ldur            x0, [fp, #-8]
    // 0x81ef04: stur            x1, [fp, #-0x18]
    // 0x81ef08: LoadField: r3 = r0->field_7
    //     0x81ef08: ldur            w3, [x0, #7]
    // 0x81ef0c: DecompressPointer r3
    //     0x81ef0c: add             x3, x3, HEAP, lsl #32
    // 0x81ef10: stur            x3, [fp, #-0x10]
    // 0x81ef14: r16 = "location"
    //     0x81ef14: ldr             x16, [PP, #0x1f40]  ; [pp+0x1f40] "location"
    // 0x81ef18: str             x16, [SP]
    // 0x81ef1c: r0 = hashCode()
    //     0x81ef1c: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81ef20: r5 = LoadInt32Instr(r0)
    //     0x81ef20: sbfx            x5, x0, #1, #0x1f
    //     0x81ef24: tbz             w0, #0, #0x81ef2c
    //     0x81ef28: ldur            x5, [x0, #7]
    // 0x81ef2c: ldur            x1, [fp, #-0x18]
    // 0x81ef30: ldur            x3, [fp, #-0x10]
    // 0x81ef34: r2 = "location"
    //     0x81ef34: ldr             x2, [PP, #0x1f40]  ; [pp+0x1f40] "location"
    // 0x81ef38: r0 = _set()
    //     0x81ef38: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81ef3c: ldur            x0, [fp, #-8]
    // 0x81ef40: LoadField: r3 = r0->field_b
    //     0x81ef40: ldur            w3, [x0, #0xb]
    // 0x81ef44: DecompressPointer r3
    //     0x81ef44: add             x3, x3, HEAP, lsl #32
    // 0x81ef48: stur            x3, [fp, #-0x10]
    // 0x81ef4c: r16 = "date"
    //     0x81ef4c: add             x16, PP, #9, lsl #12  ; [pp+0x91a8] "date"
    //     0x81ef50: ldr             x16, [x16, #0x1a8]
    // 0x81ef54: str             x16, [SP]
    // 0x81ef58: r0 = hashCode()
    //     0x81ef58: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81ef5c: r5 = LoadInt32Instr(r0)
    //     0x81ef5c: sbfx            x5, x0, #1, #0x1f
    //     0x81ef60: tbz             w0, #0, #0x81ef68
    //     0x81ef64: ldur            x5, [x0, #7]
    // 0x81ef68: ldur            x1, [fp, #-0x18]
    // 0x81ef6c: ldur            x3, [fp, #-0x10]
    // 0x81ef70: r2 = "date"
    //     0x81ef70: add             x2, PP, #9, lsl #12  ; [pp+0x91a8] "date"
    //     0x81ef74: ldr             x2, [x2, #0x1a8]
    // 0x81ef78: r0 = _set()
    //     0x81ef78: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81ef7c: ldur            x0, [fp, #-8]
    // 0x81ef80: LoadField: r3 = r0->field_f
    //     0x81ef80: ldur            w3, [x0, #0xf]
    // 0x81ef84: DecompressPointer r3
    //     0x81ef84: add             x3, x3, HEAP, lsl #32
    // 0x81ef88: stur            x3, [fp, #-0x10]
    // 0x81ef8c: r16 = "hijri"
    //     0x81ef8c: add             x16, PP, #9, lsl #12  ; [pp+0x91b0] "hijri"
    //     0x81ef90: ldr             x16, [x16, #0x1b0]
    // 0x81ef94: str             x16, [SP]
    // 0x81ef98: r0 = hashCode()
    //     0x81ef98: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81ef9c: r5 = LoadInt32Instr(r0)
    //     0x81ef9c: sbfx            x5, x0, #1, #0x1f
    //     0x81efa0: tbz             w0, #0, #0x81efa8
    //     0x81efa4: ldur            x5, [x0, #7]
    // 0x81efa8: ldur            x1, [fp, #-0x18]
    // 0x81efac: ldur            x3, [fp, #-0x10]
    // 0x81efb0: r2 = "hijri"
    //     0x81efb0: add             x2, PP, #9, lsl #12  ; [pp+0x91b0] "hijri"
    //     0x81efb4: ldr             x2, [x2, #0x1b0]
    // 0x81efb8: r0 = _set()
    //     0x81efb8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81efbc: ldur            x0, [fp, #-8]
    // 0x81efc0: LoadField: r3 = r0->field_13
    //     0x81efc0: ldur            w3, [x0, #0x13]
    // 0x81efc4: DecompressPointer r3
    //     0x81efc4: add             x3, x3, HEAP, lsl #32
    // 0x81efc8: stur            x3, [fp, #-0x10]
    // 0x81efcc: r16 = "subuh"
    //     0x81efcc: add             x16, PP, #9, lsl #12  ; [pp+0x91b8] "subuh"
    //     0x81efd0: ldr             x16, [x16, #0x1b8]
    // 0x81efd4: str             x16, [SP]
    // 0x81efd8: r0 = hashCode()
    //     0x81efd8: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81efdc: r5 = LoadInt32Instr(r0)
    //     0x81efdc: sbfx            x5, x0, #1, #0x1f
    //     0x81efe0: tbz             w0, #0, #0x81efe8
    //     0x81efe4: ldur            x5, [x0, #7]
    // 0x81efe8: ldur            x1, [fp, #-0x18]
    // 0x81efec: ldur            x3, [fp, #-0x10]
    // 0x81eff0: r2 = "subuh"
    //     0x81eff0: add             x2, PP, #9, lsl #12  ; [pp+0x91b8] "subuh"
    //     0x81eff4: ldr             x2, [x2, #0x1b8]
    // 0x81eff8: r0 = _set()
    //     0x81eff8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81effc: ldur            x0, [fp, #-8]
    // 0x81f000: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x81f000: ldur            w3, [x0, #0x17]
    // 0x81f004: DecompressPointer r3
    //     0x81f004: add             x3, x3, HEAP, lsl #32
    // 0x81f008: stur            x3, [fp, #-0x10]
    // 0x81f00c: r16 = "terbit"
    //     0x81f00c: add             x16, PP, #9, lsl #12  ; [pp+0x91c0] "terbit"
    //     0x81f010: ldr             x16, [x16, #0x1c0]
    // 0x81f014: str             x16, [SP]
    // 0x81f018: r0 = hashCode()
    //     0x81f018: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f01c: r5 = LoadInt32Instr(r0)
    //     0x81f01c: sbfx            x5, x0, #1, #0x1f
    //     0x81f020: tbz             w0, #0, #0x81f028
    //     0x81f024: ldur            x5, [x0, #7]
    // 0x81f028: ldur            x1, [fp, #-0x18]
    // 0x81f02c: ldur            x3, [fp, #-0x10]
    // 0x81f030: r2 = "terbit"
    //     0x81f030: add             x2, PP, #9, lsl #12  ; [pp+0x91c0] "terbit"
    //     0x81f034: ldr             x2, [x2, #0x1c0]
    // 0x81f038: r0 = _set()
    //     0x81f038: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f03c: ldur            x0, [fp, #-8]
    // 0x81f040: LoadField: r3 = r0->field_1b
    //     0x81f040: ldur            w3, [x0, #0x1b]
    // 0x81f044: DecompressPointer r3
    //     0x81f044: add             x3, x3, HEAP, lsl #32
    // 0x81f048: stur            x3, [fp, #-0x10]
    // 0x81f04c: r16 = "zuhur"
    //     0x81f04c: add             x16, PP, #9, lsl #12  ; [pp+0x91c8] "zuhur"
    //     0x81f050: ldr             x16, [x16, #0x1c8]
    // 0x81f054: str             x16, [SP]
    // 0x81f058: r0 = hashCode()
    //     0x81f058: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f05c: r5 = LoadInt32Instr(r0)
    //     0x81f05c: sbfx            x5, x0, #1, #0x1f
    //     0x81f060: tbz             w0, #0, #0x81f068
    //     0x81f064: ldur            x5, [x0, #7]
    // 0x81f068: ldur            x1, [fp, #-0x18]
    // 0x81f06c: ldur            x3, [fp, #-0x10]
    // 0x81f070: r2 = "zuhur"
    //     0x81f070: add             x2, PP, #9, lsl #12  ; [pp+0x91c8] "zuhur"
    //     0x81f074: ldr             x2, [x2, #0x1c8]
    // 0x81f078: r0 = _set()
    //     0x81f078: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f07c: ldur            x0, [fp, #-8]
    // 0x81f080: LoadField: r3 = r0->field_1f
    //     0x81f080: ldur            w3, [x0, #0x1f]
    // 0x81f084: DecompressPointer r3
    //     0x81f084: add             x3, x3, HEAP, lsl #32
    // 0x81f088: stur            x3, [fp, #-0x10]
    // 0x81f08c: r16 = "ashar"
    //     0x81f08c: add             x16, PP, #9, lsl #12  ; [pp+0x91d0] "ashar"
    //     0x81f090: ldr             x16, [x16, #0x1d0]
    // 0x81f094: str             x16, [SP]
    // 0x81f098: r0 = hashCode()
    //     0x81f098: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f09c: r5 = LoadInt32Instr(r0)
    //     0x81f09c: sbfx            x5, x0, #1, #0x1f
    //     0x81f0a0: tbz             w0, #0, #0x81f0a8
    //     0x81f0a4: ldur            x5, [x0, #7]
    // 0x81f0a8: ldur            x1, [fp, #-0x18]
    // 0x81f0ac: ldur            x3, [fp, #-0x10]
    // 0x81f0b0: r2 = "ashar"
    //     0x81f0b0: add             x2, PP, #9, lsl #12  ; [pp+0x91d0] "ashar"
    //     0x81f0b4: ldr             x2, [x2, #0x1d0]
    // 0x81f0b8: r0 = _set()
    //     0x81f0b8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f0bc: ldur            x0, [fp, #-8]
    // 0x81f0c0: LoadField: r3 = r0->field_23
    //     0x81f0c0: ldur            w3, [x0, #0x23]
    // 0x81f0c4: DecompressPointer r3
    //     0x81f0c4: add             x3, x3, HEAP, lsl #32
    // 0x81f0c8: stur            x3, [fp, #-0x10]
    // 0x81f0cc: r16 = "maghrib"
    //     0x81f0cc: add             x16, PP, #9, lsl #12  ; [pp+0x91d8] "maghrib"
    //     0x81f0d0: ldr             x16, [x16, #0x1d8]
    // 0x81f0d4: str             x16, [SP]
    // 0x81f0d8: r0 = hashCode()
    //     0x81f0d8: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f0dc: r5 = LoadInt32Instr(r0)
    //     0x81f0dc: sbfx            x5, x0, #1, #0x1f
    //     0x81f0e0: tbz             w0, #0, #0x81f0e8
    //     0x81f0e4: ldur            x5, [x0, #7]
    // 0x81f0e8: ldur            x1, [fp, #-0x18]
    // 0x81f0ec: ldur            x3, [fp, #-0x10]
    // 0x81f0f0: r2 = "maghrib"
    //     0x81f0f0: add             x2, PP, #9, lsl #12  ; [pp+0x91d8] "maghrib"
    //     0x81f0f4: ldr             x2, [x2, #0x1d8]
    // 0x81f0f8: r0 = _set()
    //     0x81f0f8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f0fc: ldur            x0, [fp, #-8]
    // 0x81f100: LoadField: r3 = r0->field_27
    //     0x81f100: ldur            w3, [x0, #0x27]
    // 0x81f104: DecompressPointer r3
    //     0x81f104: add             x3, x3, HEAP, lsl #32
    // 0x81f108: stur            x3, [fp, #-0x10]
    // 0x81f10c: r16 = "isya"
    //     0x81f10c: add             x16, PP, #9, lsl #12  ; [pp+0x91e0] "isya"
    //     0x81f110: ldr             x16, [x16, #0x1e0]
    // 0x81f114: str             x16, [SP]
    // 0x81f118: r0 = hashCode()
    //     0x81f118: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f11c: r5 = LoadInt32Instr(r0)
    //     0x81f11c: sbfx            x5, x0, #1, #0x1f
    //     0x81f120: tbz             w0, #0, #0x81f128
    //     0x81f124: ldur            x5, [x0, #7]
    // 0x81f128: ldur            x1, [fp, #-0x18]
    // 0x81f12c: ldur            x3, [fp, #-0x10]
    // 0x81f130: r2 = "isya"
    //     0x81f130: add             x2, PP, #9, lsl #12  ; [pp+0x91e0] "isya"
    //     0x81f134: ldr             x2, [x2, #0x1e0]
    // 0x81f138: r0 = _set()
    //     0x81f138: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f13c: ldur            x0, [fp, #-8]
    // 0x81f140: LoadField: r3 = r0->field_2b
    //     0x81f140: ldur            w3, [x0, #0x2b]
    // 0x81f144: DecompressPointer r3
    //     0x81f144: add             x3, x3, HEAP, lsl #32
    // 0x81f148: stur            x3, [fp, #-0x10]
    // 0x81f14c: r16 = "event"
    //     0x81f14c: add             x16, PP, #9, lsl #12  ; [pp+0x91e8] "event"
    //     0x81f150: ldr             x16, [x16, #0x1e8]
    // 0x81f154: str             x16, [SP]
    // 0x81f158: r0 = hashCode()
    //     0x81f158: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f15c: r5 = LoadInt32Instr(r0)
    //     0x81f15c: sbfx            x5, x0, #1, #0x1f
    //     0x81f160: tbz             w0, #0, #0x81f168
    //     0x81f164: ldur            x5, [x0, #7]
    // 0x81f168: ldur            x1, [fp, #-0x18]
    // 0x81f16c: ldur            x3, [fp, #-0x10]
    // 0x81f170: r2 = "event"
    //     0x81f170: add             x2, PP, #9, lsl #12  ; [pp+0x91e8] "event"
    //     0x81f174: ldr             x2, [x2, #0x1e8]
    // 0x81f178: r0 = _set()
    //     0x81f178: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f17c: ldur            x0, [fp, #-8]
    // 0x81f180: LoadField: r3 = r0->field_2f
    //     0x81f180: ldur            w3, [x0, #0x2f]
    // 0x81f184: DecompressPointer r3
    //     0x81f184: add             x3, x3, HEAP, lsl #32
    // 0x81f188: stur            x3, [fp, #-0x10]
    // 0x81f18c: r16 = "event_count"
    //     0x81f18c: add             x16, PP, #9, lsl #12  ; [pp+0x91f0] "event_count"
    //     0x81f190: ldr             x16, [x16, #0x1f0]
    // 0x81f194: str             x16, [SP]
    // 0x81f198: r0 = hashCode()
    //     0x81f198: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f19c: r5 = LoadInt32Instr(r0)
    //     0x81f19c: sbfx            x5, x0, #1, #0x1f
    //     0x81f1a0: tbz             w0, #0, #0x81f1a8
    //     0x81f1a4: ldur            x5, [x0, #7]
    // 0x81f1a8: ldur            x1, [fp, #-0x18]
    // 0x81f1ac: ldur            x3, [fp, #-0x10]
    // 0x81f1b0: r2 = "event_count"
    //     0x81f1b0: add             x2, PP, #9, lsl #12  ; [pp+0x91f0] "event_count"
    //     0x81f1b4: ldr             x2, [x2, #0x1f0]
    // 0x81f1b8: r0 = _set()
    //     0x81f1b8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f1bc: ldur            x0, [fp, #-8]
    // 0x81f1c0: LoadField: r3 = r0->field_33
    //     0x81f1c0: ldur            w3, [x0, #0x33]
    // 0x81f1c4: DecompressPointer r3
    //     0x81f1c4: add             x3, x3, HEAP, lsl #32
    // 0x81f1c8: stur            x3, [fp, #-0x10]
    // 0x81f1cc: r16 = "event_color"
    //     0x81f1cc: add             x16, PP, #9, lsl #12  ; [pp+0x91f8] "event_color"
    //     0x81f1d0: ldr             x16, [x16, #0x1f8]
    // 0x81f1d4: str             x16, [SP]
    // 0x81f1d8: r0 = hashCode()
    //     0x81f1d8: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0x81f1dc: r5 = LoadInt32Instr(r0)
    //     0x81f1dc: sbfx            x5, x0, #1, #0x1f
    //     0x81f1e0: tbz             w0, #0, #0x81f1e8
    //     0x81f1e4: ldur            x5, [x0, #7]
    // 0x81f1e8: ldur            x1, [fp, #-0x18]
    // 0x81f1ec: ldur            x3, [fp, #-0x10]
    // 0x81f1f0: r2 = "event_color"
    //     0x81f1f0: add             x2, PP, #9, lsl #12  ; [pp+0x91f8] "event_color"
    //     0x81f1f4: ldr             x2, [x2, #0x1f8]
    // 0x81f1f8: r0 = _set()
    //     0x81f1f8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x81f1fc: ldur            x0, [fp, #-0x18]
    // 0x81f200: LeaveFrame
    //     0x81f200: mov             SP, fp
    //     0x81f204: ldp             fp, lr, [SP], #0x10
    // 0x81f208: ret
    //     0x81f208: ret             
    // 0x81f20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81f20c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81f210: b               #0x81eeec
  }
}
