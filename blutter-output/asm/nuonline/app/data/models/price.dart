// lib: , url: package:nuonline/app/data/models/price.dart

// class id: 1050041, size: 0x8
class :: {
}

// class id: 1131, size: 0x1c, field offset: 0x8
class Price extends Object {

  static late final DateTime defaultLastUpdate; // offset: 0x158c

  _ toMap(/* No info */) {
    // ** addr: 0x90f2c4, size: 0xec
    // 0x90f2c4: EnterFrame
    //     0x90f2c4: stp             fp, lr, [SP, #-0x10]!
    //     0x90f2c8: mov             fp, SP
    // 0x90f2cc: AllocStack(0x20)
    //     0x90f2cc: sub             SP, SP, #0x20
    // 0x90f2d0: SetupParameters(Price this /* r1 => r0, fp-0x8 */)
    //     0x90f2d0: mov             x0, x1
    //     0x90f2d4: stur            x1, [fp, #-8]
    // 0x90f2d8: CheckStackOverflow
    //     0x90f2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f2dc: cmp             SP, x16
    //     0x90f2e0: b.ls            #0x90f3a8
    // 0x90f2e4: r1 = Null
    //     0x90f2e4: mov             x1, NULL
    // 0x90f2e8: r2 = 12
    //     0x90f2e8: movz            x2, #0xc
    // 0x90f2ec: r0 = AllocateArray()
    //     0x90f2ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90f2f0: mov             x2, x0
    // 0x90f2f4: stur            x2, [fp, #-0x10]
    // 0x90f2f8: r16 = "gold"
    //     0x90f2f8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27340] "gold"
    //     0x90f2fc: ldr             x16, [x16, #0x340]
    // 0x90f300: StoreField: r2->field_f = r16
    //     0x90f300: stur            w16, [x2, #0xf]
    // 0x90f304: ldur            x3, [fp, #-8]
    // 0x90f308: LoadField: r4 = r3->field_7
    //     0x90f308: ldur            x4, [x3, #7]
    // 0x90f30c: r0 = BoxInt64Instr(r4)
    //     0x90f30c: sbfiz           x0, x4, #1, #0x1f
    //     0x90f310: cmp             x4, x0, asr #1
    //     0x90f314: b.eq            #0x90f320
    //     0x90f318: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90f31c: stur            x4, [x0, #7]
    // 0x90f320: StoreField: r2->field_13 = r0
    //     0x90f320: stur            w0, [x2, #0x13]
    // 0x90f324: r16 = "silver"
    //     0x90f324: add             x16, PP, #0x27, lsl #12  ; [pp+0x27360] "silver"
    //     0x90f328: ldr             x16, [x16, #0x360]
    // 0x90f32c: ArrayStore: r2[0] = r16  ; List_4
    //     0x90f32c: stur            w16, [x2, #0x17]
    // 0x90f330: LoadField: r4 = r3->field_f
    //     0x90f330: ldur            x4, [x3, #0xf]
    // 0x90f334: r0 = BoxInt64Instr(r4)
    //     0x90f334: sbfiz           x0, x4, #1, #0x1f
    //     0x90f338: cmp             x4, x0, asr #1
    //     0x90f33c: b.eq            #0x90f348
    //     0x90f340: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90f344: stur            x4, [x0, #7]
    // 0x90f348: StoreField: r2->field_1b = r0
    //     0x90f348: stur            w0, [x2, #0x1b]
    // 0x90f34c: r16 = "last_update"
    //     0x90f34c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27378] "last_update"
    //     0x90f350: ldr             x16, [x16, #0x378]
    // 0x90f354: StoreField: r2->field_1f = r16
    //     0x90f354: stur            w16, [x2, #0x1f]
    // 0x90f358: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x90f358: ldur            w1, [x3, #0x17]
    // 0x90f35c: DecompressPointer r1
    //     0x90f35c: add             x1, x1, HEAP, lsl #32
    // 0x90f360: r0 = toIso8601String()
    //     0x90f360: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0x90f364: ldur            x1, [fp, #-0x10]
    // 0x90f368: ArrayStore: r1[5] = r0  ; List_4
    //     0x90f368: add             x25, x1, #0x23
    //     0x90f36c: str             w0, [x25]
    //     0x90f370: tbz             w0, #0, #0x90f38c
    //     0x90f374: ldurb           w16, [x1, #-1]
    //     0x90f378: ldurb           w17, [x0, #-1]
    //     0x90f37c: and             x16, x17, x16, lsr #2
    //     0x90f380: tst             x16, HEAP, lsr #32
    //     0x90f384: b.eq            #0x90f38c
    //     0x90f388: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90f38c: r16 = <String, dynamic>
    //     0x90f38c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x90f390: ldur            lr, [fp, #-0x10]
    // 0x90f394: stp             lr, x16, [SP]
    // 0x90f398: r0 = Map._fromLiteral()
    //     0x90f398: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90f39c: LeaveFrame
    //     0x90f39c: mov             SP, fp
    //     0x90f3a0: ldp             fp, lr, [SP], #0x10
    // 0x90f3a4: ret
    //     0x90f3a4: ret             
    // 0x90f3a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90f3a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90f3ac: b               #0x90f2e4
  }
  factory _ Price.fromJson(/* No info */) {
    // ** addr: 0x90f3b0, size: 0x2a0
    // 0x90f3b0: EnterFrame
    //     0x90f3b0: stp             fp, lr, [SP, #-0x10]!
    //     0x90f3b4: mov             fp, SP
    // 0x90f3b8: AllocStack(0x28)
    //     0x90f3b8: sub             SP, SP, #0x28
    // 0x90f3bc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x90f3bc: mov             x3, x2
    //     0x90f3c0: stur            x2, [fp, #-8]
    // 0x90f3c4: CheckStackOverflow
    //     0x90f3c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f3c8: cmp             SP, x16
    //     0x90f3cc: b.ls            #0x90f60c
    // 0x90f3d0: r0 = LoadClassIdInstr(r3)
    //     0x90f3d0: ldur            x0, [x3, #-1]
    //     0x90f3d4: ubfx            x0, x0, #0xc, #0x14
    // 0x90f3d8: mov             x1, x3
    // 0x90f3dc: r2 = "gold_price"
    //     0x90f3dc: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c280] "gold_price"
    //     0x90f3e0: ldr             x2, [x2, #0x280]
    // 0x90f3e4: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f3e4: sub             lr, x0, #0x114
    //     0x90f3e8: ldr             lr, [x21, lr, lsl #3]
    //     0x90f3ec: blr             lr
    // 0x90f3f0: mov             x3, x0
    // 0x90f3f4: r2 = Null
    //     0x90f3f4: mov             x2, NULL
    // 0x90f3f8: r1 = Null
    //     0x90f3f8: mov             x1, NULL
    // 0x90f3fc: stur            x3, [fp, #-0x10]
    // 0x90f400: r4 = 60
    //     0x90f400: movz            x4, #0x3c
    // 0x90f404: branchIfSmi(r0, 0x90f410)
    //     0x90f404: tbz             w0, #0, #0x90f410
    // 0x90f408: r4 = LoadClassIdInstr(r0)
    //     0x90f408: ldur            x4, [x0, #-1]
    //     0x90f40c: ubfx            x4, x4, #0xc, #0x14
    // 0x90f410: cmp             x4, #0x3e
    // 0x90f414: b.eq            #0x90f428
    // 0x90f418: r8 = double
    //     0x90f418: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0x90f41c: r3 = Null
    //     0x90f41c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c288] Null
    //     0x90f420: ldr             x3, [x3, #0x288]
    // 0x90f424: r0 = double()
    //     0x90f424: bl              #0xed4460  ; IsType_double_Stub
    // 0x90f428: ldur            x3, [fp, #-8]
    // 0x90f42c: r0 = LoadClassIdInstr(r3)
    //     0x90f42c: ldur            x0, [x3, #-1]
    //     0x90f430: ubfx            x0, x0, #0xc, #0x14
    // 0x90f434: mov             x1, x3
    // 0x90f438: r2 = "silver_price"
    //     0x90f438: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c298] "silver_price"
    //     0x90f43c: ldr             x2, [x2, #0x298]
    // 0x90f440: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f440: sub             lr, x0, #0x114
    //     0x90f444: ldr             lr, [x21, lr, lsl #3]
    //     0x90f448: blr             lr
    // 0x90f44c: mov             x3, x0
    // 0x90f450: r2 = Null
    //     0x90f450: mov             x2, NULL
    // 0x90f454: r1 = Null
    //     0x90f454: mov             x1, NULL
    // 0x90f458: stur            x3, [fp, #-0x18]
    // 0x90f45c: r4 = 60
    //     0x90f45c: movz            x4, #0x3c
    // 0x90f460: branchIfSmi(r0, 0x90f46c)
    //     0x90f460: tbz             w0, #0, #0x90f46c
    // 0x90f464: r4 = LoadClassIdInstr(r0)
    //     0x90f464: ldur            x4, [x0, #-1]
    //     0x90f468: ubfx            x4, x4, #0xc, #0x14
    // 0x90f46c: cmp             x4, #0x3e
    // 0x90f470: b.eq            #0x90f484
    // 0x90f474: r8 = double
    //     0x90f474: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0x90f478: r3 = Null
    //     0x90f478: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2a0] Null
    //     0x90f47c: ldr             x3, [x3, #0x2a0]
    // 0x90f480: r0 = double()
    //     0x90f480: bl              #0xed4460  ; IsType_double_Stub
    // 0x90f484: ldur            x0, [fp, #-0x10]
    // 0x90f488: LoadField: d0 = r0->field_7
    //     0x90f488: ldur            d0, [x0, #7]
    // 0x90f48c: d1 = 31.103477
    //     0x90f48c: add             x17, PP, #0x3c, lsl #12  ; [pp+0x3c2b0] IMM: double(31.1034768) from 0x403f1a7d749fe50d
    //     0x90f490: ldr             d1, [x17, #0x2b0]
    // 0x90f494: fdiv            d2, d0, d1
    // 0x90f498: ldur            x0, [fp, #-0x18]
    // 0x90f49c: LoadField: d0 = r0->field_7
    //     0x90f49c: ldur            d0, [x0, #7]
    // 0x90f4a0: fdiv            d3, d0, d1
    // 0x90f4a4: mov             v0.16b, v2.16b
    // 0x90f4a8: stur            d3, [fp, #-0x28]
    // 0x90f4ac: stp             fp, lr, [SP, #-0x10]!
    // 0x90f4b0: mov             fp, SP
    // 0x90f4b4: CallRuntime_LibcRound(double) -> double
    //     0x90f4b4: and             SP, SP, #0xfffffffffffffff0
    //     0x90f4b8: mov             sp, SP
    //     0x90f4bc: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x90f4c0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90f4c4: blr             x16
    //     0x90f4c8: movz            x16, #0x8
    //     0x90f4cc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90f4d0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x90f4d4: sub             sp, x16, #1, lsl #12
    //     0x90f4d8: mov             SP, fp
    //     0x90f4dc: ldp             fp, lr, [SP], #0x10
    // 0x90f4e0: fcmp            d0, d0
    // 0x90f4e4: b.vs            #0x90f614
    // 0x90f4e8: fcvtzs          x0, d0
    // 0x90f4ec: asr             x16, x0, #0x1e
    // 0x90f4f0: cmp             x16, x0, asr #63
    // 0x90f4f4: b.ne            #0x90f614
    // 0x90f4f8: lsl             x0, x0, #1
    // 0x90f4fc: ldur            d0, [fp, #-0x28]
    // 0x90f500: stur            x0, [fp, #-0x10]
    // 0x90f504: stp             fp, lr, [SP, #-0x10]!
    // 0x90f508: mov             fp, SP
    // 0x90f50c: CallRuntime_LibcRound(double) -> double
    //     0x90f50c: and             SP, SP, #0xfffffffffffffff0
    //     0x90f510: mov             sp, SP
    //     0x90f514: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x90f518: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90f51c: blr             x16
    //     0x90f520: movz            x16, #0x8
    //     0x90f524: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90f528: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x90f52c: sub             sp, x16, #1, lsl #12
    //     0x90f530: mov             SP, fp
    //     0x90f534: ldp             fp, lr, [SP], #0x10
    // 0x90f538: fcmp            d0, d0
    // 0x90f53c: b.vs            #0x90f630
    // 0x90f540: fcvtzs          x3, d0
    // 0x90f544: asr             x16, x3, #0x1e
    // 0x90f548: cmp             x16, x3, asr #63
    // 0x90f54c: b.ne            #0x90f630
    // 0x90f550: lsl             x3, x3, #1
    // 0x90f554: ldur            x1, [fp, #-8]
    // 0x90f558: stur            x3, [fp, #-0x18]
    // 0x90f55c: r0 = LoadClassIdInstr(r1)
    //     0x90f55c: ldur            x0, [x1, #-1]
    //     0x90f560: ubfx            x0, x0, #0xc, #0x14
    // 0x90f564: r2 = "date"
    //     0x90f564: add             x2, PP, #9, lsl #12  ; [pp+0x91a8] "date"
    //     0x90f568: ldr             x2, [x2, #0x1a8]
    // 0x90f56c: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f56c: sub             lr, x0, #0x114
    //     0x90f570: ldr             lr, [x21, lr, lsl #3]
    //     0x90f574: blr             lr
    // 0x90f578: mov             x3, x0
    // 0x90f57c: r2 = Null
    //     0x90f57c: mov             x2, NULL
    // 0x90f580: r1 = Null
    //     0x90f580: mov             x1, NULL
    // 0x90f584: stur            x3, [fp, #-8]
    // 0x90f588: r4 = 60
    //     0x90f588: movz            x4, #0x3c
    // 0x90f58c: branchIfSmi(r0, 0x90f598)
    //     0x90f58c: tbz             w0, #0, #0x90f598
    // 0x90f590: r4 = LoadClassIdInstr(r0)
    //     0x90f590: ldur            x4, [x0, #-1]
    //     0x90f594: ubfx            x4, x4, #0xc, #0x14
    // 0x90f598: sub             x4, x4, #0x5e
    // 0x90f59c: cmp             x4, #1
    // 0x90f5a0: b.ls            #0x90f5b4
    // 0x90f5a4: r8 = String
    //     0x90f5a4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x90f5a8: r3 = Null
    //     0x90f5a8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2b8] Null
    //     0x90f5ac: ldr             x3, [x3, #0x2b8]
    // 0x90f5b0: r0 = String()
    //     0x90f5b0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x90f5b4: ldur            x1, [fp, #-8]
    // 0x90f5b8: r0 = parse()
    //     0x90f5b8: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x90f5bc: mov             x1, x0
    // 0x90f5c0: ldur            x0, [fp, #-0x10]
    // 0x90f5c4: stur            x1, [fp, #-8]
    // 0x90f5c8: r2 = LoadInt32Instr(r0)
    //     0x90f5c8: sbfx            x2, x0, #1, #0x1f
    //     0x90f5cc: tbz             w0, #0, #0x90f5d4
    //     0x90f5d0: ldur            x2, [x0, #7]
    // 0x90f5d4: stur            x2, [fp, #-0x20]
    // 0x90f5d8: r0 = Price()
    //     0x90f5d8: bl              #0x90f650  ; AllocatePriceStub -> Price (size=0x1c)
    // 0x90f5dc: ldur            x1, [fp, #-0x20]
    // 0x90f5e0: StoreField: r0->field_7 = r1
    //     0x90f5e0: stur            x1, [x0, #7]
    // 0x90f5e4: ldur            x1, [fp, #-0x18]
    // 0x90f5e8: r2 = LoadInt32Instr(r1)
    //     0x90f5e8: sbfx            x2, x1, #1, #0x1f
    //     0x90f5ec: tbz             w1, #0, #0x90f5f4
    //     0x90f5f0: ldur            x2, [x1, #7]
    // 0x90f5f4: StoreField: r0->field_f = r2
    //     0x90f5f4: stur            x2, [x0, #0xf]
    // 0x90f5f8: ldur            x1, [fp, #-8]
    // 0x90f5fc: ArrayStore: r0[0] = r1  ; List_4
    //     0x90f5fc: stur            w1, [x0, #0x17]
    // 0x90f600: LeaveFrame
    //     0x90f600: mov             SP, fp
    //     0x90f604: ldp             fp, lr, [SP], #0x10
    // 0x90f608: ret
    //     0x90f608: ret             
    // 0x90f60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90f60c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90f610: b               #0x90f3d0
    // 0x90f614: SaveReg d0
    //     0x90f614: str             q0, [SP, #-0x10]!
    // 0x90f618: r0 = 74
    //     0x90f618: movz            x0, #0x4a
    // 0x90f61c: r30 = DoubleToIntegerStub
    //     0x90f61c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x90f620: LoadField: r30 = r30->field_7
    //     0x90f620: ldur            lr, [lr, #7]
    // 0x90f624: blr             lr
    // 0x90f628: RestoreReg d0
    //     0x90f628: ldr             q0, [SP], #0x10
    // 0x90f62c: b               #0x90f4fc
    // 0x90f630: SaveReg d0
    //     0x90f630: str             q0, [SP, #-0x10]!
    // 0x90f634: r0 = 74
    //     0x90f634: movz            x0, #0x4a
    // 0x90f638: r30 = DoubleToIntegerStub
    //     0x90f638: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x90f63c: LoadField: r30 = r30->field_7
    //     0x90f63c: ldur            lr, [lr, #7]
    // 0x90f640: blr             lr
    // 0x90f644: mov             x3, x0
    // 0x90f648: RestoreReg d0
    //     0x90f648: ldr             q0, [SP], #0x10
    // 0x90f64c: b               #0x90f554
  }
  factory _ Price.fromMap(/* No info */) {
    // ** addr: 0x90f840, size: 0x214
    // 0x90f840: EnterFrame
    //     0x90f840: stp             fp, lr, [SP, #-0x10]!
    //     0x90f844: mov             fp, SP
    // 0x90f848: AllocStack(0x20)
    //     0x90f848: sub             SP, SP, #0x20
    // 0x90f84c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x90f84c: mov             x3, x2
    //     0x90f850: stur            x2, [fp, #-8]
    // 0x90f854: CheckStackOverflow
    //     0x90f854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f858: cmp             SP, x16
    //     0x90f85c: b.ls            #0x90fa4c
    // 0x90f860: r0 = LoadClassIdInstr(r3)
    //     0x90f860: ldur            x0, [x3, #-1]
    //     0x90f864: ubfx            x0, x0, #0xc, #0x14
    // 0x90f868: mov             x1, x3
    // 0x90f86c: r2 = "gold"
    //     0x90f86c: add             x2, PP, #0x27, lsl #12  ; [pp+0x27340] "gold"
    //     0x90f870: ldr             x2, [x2, #0x340]
    // 0x90f874: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f874: sub             lr, x0, #0x114
    //     0x90f878: ldr             lr, [x21, lr, lsl #3]
    //     0x90f87c: blr             lr
    // 0x90f880: cmp             w0, NULL
    // 0x90f884: b.ne            #0x90f8e0
    // 0x90f888: r0 = Price()
    //     0x90f888: bl              #0x90f650  ; AllocatePriceStub -> Price (size=0x1c)
    // 0x90f88c: mov             x1, x0
    // 0x90f890: r0 = 822656
    //     0x90f890: movz            x0, #0x8d80
    //     0x90f894: movk            x0, #0xc, lsl #16
    // 0x90f898: stur            x1, [fp, #-0x10]
    // 0x90f89c: StoreField: r1->field_7 = r0
    //     0x90f89c: stur            x0, [x1, #7]
    // 0x90f8a0: r0 = 10227
    //     0x90f8a0: movz            x0, #0x27f3
    // 0x90f8a4: StoreField: r1->field_f = r0
    //     0x90f8a4: stur            x0, [x1, #0xf]
    // 0x90f8a8: r0 = InitLateStaticField(0x158c) // [package:nuonline/app/data/models/price.dart] Price::defaultLastUpdate
    //     0x90f8a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x90f8ac: ldr             x0, [x0, #0x2b18]
    //     0x90f8b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x90f8b4: cmp             w0, w16
    //     0x90f8b8: b.ne            #0x90f8c8
    //     0x90f8bc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27348] Field <Price.defaultLastUpdate>: static late final (offset: 0x158c)
    //     0x90f8c0: ldr             x2, [x2, #0x348]
    //     0x90f8c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x90f8c8: mov             x1, x0
    // 0x90f8cc: ldur            x0, [fp, #-0x10]
    // 0x90f8d0: ArrayStore: r0[0] = r1  ; List_4
    //     0x90f8d0: stur            w1, [x0, #0x17]
    // 0x90f8d4: LeaveFrame
    //     0x90f8d4: mov             SP, fp
    //     0x90f8d8: ldp             fp, lr, [SP], #0x10
    // 0x90f8dc: ret
    //     0x90f8dc: ret             
    // 0x90f8e0: ldur            x3, [fp, #-8]
    // 0x90f8e4: r0 = LoadClassIdInstr(r3)
    //     0x90f8e4: ldur            x0, [x3, #-1]
    //     0x90f8e8: ubfx            x0, x0, #0xc, #0x14
    // 0x90f8ec: mov             x1, x3
    // 0x90f8f0: r2 = "gold"
    //     0x90f8f0: add             x2, PP, #0x27, lsl #12  ; [pp+0x27340] "gold"
    //     0x90f8f4: ldr             x2, [x2, #0x340]
    // 0x90f8f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f8f8: sub             lr, x0, #0x114
    //     0x90f8fc: ldr             lr, [x21, lr, lsl #3]
    //     0x90f900: blr             lr
    // 0x90f904: mov             x3, x0
    // 0x90f908: r2 = Null
    //     0x90f908: mov             x2, NULL
    // 0x90f90c: r1 = Null
    //     0x90f90c: mov             x1, NULL
    // 0x90f910: stur            x3, [fp, #-0x10]
    // 0x90f914: branchIfSmi(r0, 0x90f93c)
    //     0x90f914: tbz             w0, #0, #0x90f93c
    // 0x90f918: r4 = LoadClassIdInstr(r0)
    //     0x90f918: ldur            x4, [x0, #-1]
    //     0x90f91c: ubfx            x4, x4, #0xc, #0x14
    // 0x90f920: sub             x4, x4, #0x3c
    // 0x90f924: cmp             x4, #1
    // 0x90f928: b.ls            #0x90f93c
    // 0x90f92c: r8 = int
    //     0x90f92c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x90f930: r3 = Null
    //     0x90f930: add             x3, PP, #0x27, lsl #12  ; [pp+0x27350] Null
    //     0x90f934: ldr             x3, [x3, #0x350]
    // 0x90f938: r0 = int()
    //     0x90f938: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x90f93c: ldur            x3, [fp, #-8]
    // 0x90f940: r0 = LoadClassIdInstr(r3)
    //     0x90f940: ldur            x0, [x3, #-1]
    //     0x90f944: ubfx            x0, x0, #0xc, #0x14
    // 0x90f948: mov             x1, x3
    // 0x90f94c: r2 = "silver"
    //     0x90f94c: add             x2, PP, #0x27, lsl #12  ; [pp+0x27360] "silver"
    //     0x90f950: ldr             x2, [x2, #0x360]
    // 0x90f954: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f954: sub             lr, x0, #0x114
    //     0x90f958: ldr             lr, [x21, lr, lsl #3]
    //     0x90f95c: blr             lr
    // 0x90f960: mov             x3, x0
    // 0x90f964: r2 = Null
    //     0x90f964: mov             x2, NULL
    // 0x90f968: r1 = Null
    //     0x90f968: mov             x1, NULL
    // 0x90f96c: stur            x3, [fp, #-0x18]
    // 0x90f970: branchIfSmi(r0, 0x90f998)
    //     0x90f970: tbz             w0, #0, #0x90f998
    // 0x90f974: r4 = LoadClassIdInstr(r0)
    //     0x90f974: ldur            x4, [x0, #-1]
    //     0x90f978: ubfx            x4, x4, #0xc, #0x14
    // 0x90f97c: sub             x4, x4, #0x3c
    // 0x90f980: cmp             x4, #1
    // 0x90f984: b.ls            #0x90f998
    // 0x90f988: r8 = int
    //     0x90f988: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x90f98c: r3 = Null
    //     0x90f98c: add             x3, PP, #0x27, lsl #12  ; [pp+0x27368] Null
    //     0x90f990: ldr             x3, [x3, #0x368]
    // 0x90f994: r0 = int()
    //     0x90f994: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x90f998: ldur            x1, [fp, #-8]
    // 0x90f99c: r0 = LoadClassIdInstr(r1)
    //     0x90f99c: ldur            x0, [x1, #-1]
    //     0x90f9a0: ubfx            x0, x0, #0xc, #0x14
    // 0x90f9a4: r2 = "last_update"
    //     0x90f9a4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27378] "last_update"
    //     0x90f9a8: ldr             x2, [x2, #0x378]
    // 0x90f9ac: r0 = GDT[cid_x0 + -0x114]()
    //     0x90f9ac: sub             lr, x0, #0x114
    //     0x90f9b0: ldr             lr, [x21, lr, lsl #3]
    //     0x90f9b4: blr             lr
    // 0x90f9b8: mov             x3, x0
    // 0x90f9bc: r2 = Null
    //     0x90f9bc: mov             x2, NULL
    // 0x90f9c0: r1 = Null
    //     0x90f9c0: mov             x1, NULL
    // 0x90f9c4: stur            x3, [fp, #-8]
    // 0x90f9c8: r4 = 60
    //     0x90f9c8: movz            x4, #0x3c
    // 0x90f9cc: branchIfSmi(r0, 0x90f9d8)
    //     0x90f9cc: tbz             w0, #0, #0x90f9d8
    // 0x90f9d0: r4 = LoadClassIdInstr(r0)
    //     0x90f9d0: ldur            x4, [x0, #-1]
    //     0x90f9d4: ubfx            x4, x4, #0xc, #0x14
    // 0x90f9d8: sub             x4, x4, #0x5e
    // 0x90f9dc: cmp             x4, #1
    // 0x90f9e0: b.ls            #0x90f9f4
    // 0x90f9e4: r8 = String
    //     0x90f9e4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x90f9e8: r3 = Null
    //     0x90f9e8: add             x3, PP, #0x27, lsl #12  ; [pp+0x27380] Null
    //     0x90f9ec: ldr             x3, [x3, #0x380]
    // 0x90f9f0: r0 = String()
    //     0x90f9f0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x90f9f4: ldur            x1, [fp, #-8]
    // 0x90f9f8: r0 = parse()
    //     0x90f9f8: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x90f9fc: mov             x1, x0
    // 0x90fa00: ldur            x0, [fp, #-0x10]
    // 0x90fa04: stur            x1, [fp, #-8]
    // 0x90fa08: r2 = LoadInt32Instr(r0)
    //     0x90fa08: sbfx            x2, x0, #1, #0x1f
    //     0x90fa0c: tbz             w0, #0, #0x90fa14
    //     0x90fa10: ldur            x2, [x0, #7]
    // 0x90fa14: stur            x2, [fp, #-0x20]
    // 0x90fa18: r0 = Price()
    //     0x90fa18: bl              #0x90f650  ; AllocatePriceStub -> Price (size=0x1c)
    // 0x90fa1c: ldur            x1, [fp, #-0x20]
    // 0x90fa20: StoreField: r0->field_7 = r1
    //     0x90fa20: stur            x1, [x0, #7]
    // 0x90fa24: ldur            x1, [fp, #-0x18]
    // 0x90fa28: r2 = LoadInt32Instr(r1)
    //     0x90fa28: sbfx            x2, x1, #1, #0x1f
    //     0x90fa2c: tbz             w1, #0, #0x90fa34
    //     0x90fa30: ldur            x2, [x1, #7]
    // 0x90fa34: StoreField: r0->field_f = r2
    //     0x90fa34: stur            x2, [x0, #0xf]
    // 0x90fa38: ldur            x1, [fp, #-8]
    // 0x90fa3c: ArrayStore: r0[0] = r1  ; List_4
    //     0x90fa3c: stur            w1, [x0, #0x17]
    // 0x90fa40: LeaveFrame
    //     0x90fa40: mov             SP, fp
    //     0x90fa44: ldp             fp, lr, [SP], #0x10
    // 0x90fa48: ret
    //     0x90fa48: ret             
    // 0x90fa4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90fa4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90fa50: b               #0x90f860
  }
  static DateTime defaultLastUpdate() {
    // ** addr: 0x90fac8, size: 0x58
    // 0x90fac8: EnterFrame
    //     0x90fac8: stp             fp, lr, [SP, #-0x10]!
    //     0x90facc: mov             fp, SP
    // 0x90fad0: AllocStack(0x18)
    //     0x90fad0: sub             SP, SP, #0x18
    // 0x90fad4: CheckStackOverflow
    //     0x90fad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90fad8: cmp             SP, x16
    //     0x90fadc: b.ls            #0x90fb18
    // 0x90fae0: r0 = DateTime()
    //     0x90fae0: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x90fae4: stur            x0, [fp, #-8]
    // 0x90fae8: r16 = 42
    //     0x90fae8: movz            x16, #0x2a
    // 0x90faec: r30 = 42
    //     0x90faec: movz            lr, #0x2a
    // 0x90faf0: stp             lr, x16, [SP]
    // 0x90faf4: mov             x1, x0
    // 0x90faf8: r2 = 2021
    //     0x90faf8: movz            x2, #0x7e5
    // 0x90fafc: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x90fafc: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x90fb00: ldr             x4, [x4, #0xe00]
    // 0x90fb04: r0 = DateTime()
    //     0x90fb04: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x90fb08: ldur            x0, [fp, #-8]
    // 0x90fb0c: LeaveFrame
    //     0x90fb0c: mov             SP, fp
    //     0x90fb10: ldp             fp, lr, [SP], #0x10
    // 0x90fb14: ret
    //     0x90fb14: ret             
    // 0x90fb18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90fb18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90fb1c: b               #0x90fae0
  }
  get _ nishabSilver(/* No info */) {
    // ** addr: 0xe36554, size: 0x18
    // 0xe36554: d1 = 543.350000
    //     0xe36554: add             x17, PP, #0x27, lsl #12  ; [pp+0x27be8] IMM: double(543.35) from 0x4080facccccccccd
    //     0xe36558: ldr             d1, [x17, #0xbe8]
    // 0xe3655c: LoadField: r0 = r1->field_f
    //     0xe3655c: ldur            x0, [x1, #0xf]
    // 0xe36560: scvtf           d2, x0
    // 0xe36564: fmul            d0, d2, d1
    // 0xe36568: ret
    //     0xe36568: ret             
  }
  get _ nishabGold(/* No info */) {
    // ** addr: 0xe375ac, size: 0x18
    // 0xe375ac: d1 = 77.500000
    //     0xe375ac: add             x17, PP, #0x27, lsl #12  ; [pp+0x27338] IMM: double(77.5) from 0x4053600000000000
    //     0xe375b0: ldr             d1, [x17, #0x338]
    // 0xe375b4: LoadField: r0 = r1->field_7
    //     0xe375b4: ldur            x0, [x1, #7]
    // 0xe375b8: scvtf           d2, x0
    // 0xe375bc: fmul            d0, d2, d1
    // 0xe375c0: ret
    //     0xe375c0: ret             
  }
}
