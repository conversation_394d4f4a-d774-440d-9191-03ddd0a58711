// lib: , url: package:nuonline/app/data/models/province.dart

// class id: 1050042, size: 0x8
class :: {
}

// class id: 1602, size: 0x20, field offset: 0x14
class Province extends HiveObject {

  String toJson(Province) {
    // ** addr: 0x836b0c, size: 0x48
    // 0x836b0c: EnterFrame
    //     0x836b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x836b10: mov             fp, SP
    // 0x836b14: CheckStackOverflow
    //     0x836b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x836b18: cmp             SP, x16
    //     0x836b1c: b.ls            #0x836b34
    // 0x836b20: ldr             x1, [fp, #0x10]
    // 0x836b24: r0 = toJson()
    //     0x836b24: bl              #0x836b3c  ; [package:nuonline/app/data/models/province.dart] Province::toJson
    // 0x836b28: LeaveFrame
    //     0x836b28: mov             SP, fp
    //     0x836b2c: ldp             fp, lr, [SP], #0x10
    // 0x836b30: ret
    //     0x836b30: ret             
    // 0x836b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x836b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x836b38: b               #0x836b20
  }
  String toJson(Province) {
    // ** addr: 0x836b3c, size: 0x3c
    // 0x836b3c: EnterFrame
    //     0x836b3c: stp             fp, lr, [SP, #-0x10]!
    //     0x836b40: mov             fp, SP
    // 0x836b44: CheckStackOverflow
    //     0x836b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x836b48: cmp             SP, x16
    //     0x836b4c: b.ls            #0x836b70
    // 0x836b50: r0 = toMap()
    //     0x836b50: bl              #0x836b78  ; [package:nuonline/app/data/models/province.dart] Province::toMap
    // 0x836b54: mov             x2, x0
    // 0x836b58: r1 = Instance_JsonCodec
    //     0x836b58: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x836b5c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x836b5c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x836b60: r0 = encode()
    //     0x836b60: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x836b64: LeaveFrame
    //     0x836b64: mov             SP, fp
    //     0x836b68: ldp             fp, lr, [SP], #0x10
    // 0x836b6c: ret
    //     0x836b6c: ret             
    // 0x836b70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x836b70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x836b74: b               #0x836b50
  }
  _ toMap(/* No info */) {
    // ** addr: 0x836b78, size: 0x90
    // 0x836b78: EnterFrame
    //     0x836b78: stp             fp, lr, [SP, #-0x10]!
    //     0x836b7c: mov             fp, SP
    // 0x836b80: AllocStack(0x18)
    //     0x836b80: sub             SP, SP, #0x18
    // 0x836b84: SetupParameters(Province this /* r1 => r0, fp-0x8 */)
    //     0x836b84: mov             x0, x1
    //     0x836b88: stur            x1, [fp, #-8]
    // 0x836b8c: CheckStackOverflow
    //     0x836b8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x836b90: cmp             SP, x16
    //     0x836b94: b.ls            #0x836c00
    // 0x836b98: r1 = Null
    //     0x836b98: mov             x1, NULL
    // 0x836b9c: r2 = 8
    //     0x836b9c: movz            x2, #0x8
    // 0x836ba0: r0 = AllocateArray()
    //     0x836ba0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x836ba4: mov             x2, x0
    // 0x836ba8: r16 = "id"
    //     0x836ba8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x836bac: ldr             x16, [x16, #0x740]
    // 0x836bb0: StoreField: r2->field_f = r16
    //     0x836bb0: stur            w16, [x2, #0xf]
    // 0x836bb4: ldur            x3, [fp, #-8]
    // 0x836bb8: LoadField: r4 = r3->field_13
    //     0x836bb8: ldur            x4, [x3, #0x13]
    // 0x836bbc: r0 = BoxInt64Instr(r4)
    //     0x836bbc: sbfiz           x0, x4, #1, #0x1f
    //     0x836bc0: cmp             x4, x0, asr #1
    //     0x836bc4: b.eq            #0x836bd0
    //     0x836bc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x836bcc: stur            x4, [x0, #7]
    // 0x836bd0: StoreField: r2->field_13 = r0
    //     0x836bd0: stur            w0, [x2, #0x13]
    // 0x836bd4: r16 = "name"
    //     0x836bd4: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x836bd8: ArrayStore: r2[0] = r16  ; List_4
    //     0x836bd8: stur            w16, [x2, #0x17]
    // 0x836bdc: LoadField: r0 = r3->field_1b
    //     0x836bdc: ldur            w0, [x3, #0x1b]
    // 0x836be0: DecompressPointer r0
    //     0x836be0: add             x0, x0, HEAP, lsl #32
    // 0x836be4: StoreField: r2->field_1b = r0
    //     0x836be4: stur            w0, [x2, #0x1b]
    // 0x836be8: r16 = <String, dynamic>
    //     0x836be8: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x836bec: stp             x2, x16, [SP]
    // 0x836bf0: r0 = Map._fromLiteral()
    //     0x836bf0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x836bf4: LeaveFrame
    //     0x836bf4: mov             SP, fp
    //     0x836bf8: ldp             fp, lr, [SP], #0x10
    // 0x836bfc: ret
    //     0x836bfc: ret             
    // 0x836c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x836c00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x836c04: b               #0x836b98
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x8feafc, size: 0x188
    // 0x8feafc: EnterFrame
    //     0x8feafc: stp             fp, lr, [SP, #-0x10]!
    //     0x8feb00: mov             fp, SP
    // 0x8feb04: AllocStack(0x20)
    //     0x8feb04: sub             SP, SP, #0x20
    // 0x8feb08: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8feb08: mov             x3, x1
    //     0x8feb0c: stur            x1, [fp, #-8]
    // 0x8feb10: CheckStackOverflow
    //     0x8feb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8feb14: cmp             SP, x16
    //     0x8feb18: b.ls            #0x8fec7c
    // 0x8feb1c: mov             x0, x3
    // 0x8feb20: r2 = Null
    //     0x8feb20: mov             x2, NULL
    // 0x8feb24: r1 = Null
    //     0x8feb24: mov             x1, NULL
    // 0x8feb28: cmp             w0, NULL
    // 0x8feb2c: b.eq            #0x8febd0
    // 0x8feb30: branchIfSmi(r0, 0x8febd0)
    //     0x8feb30: tbz             w0, #0, #0x8febd0
    // 0x8feb34: r3 = LoadClassIdInstr(r0)
    //     0x8feb34: ldur            x3, [x0, #-1]
    //     0x8feb38: ubfx            x3, x3, #0xc, #0x14
    // 0x8feb3c: r17 = 6718
    //     0x8feb3c: movz            x17, #0x1a3e
    // 0x8feb40: cmp             x3, x17
    // 0x8feb44: b.eq            #0x8febd8
    // 0x8feb48: sub             x3, x3, #0x5a
    // 0x8feb4c: cmp             x3, #2
    // 0x8feb50: b.ls            #0x8febd8
    // 0x8feb54: r4 = LoadClassIdInstr(r0)
    //     0x8feb54: ldur            x4, [x0, #-1]
    //     0x8feb58: ubfx            x4, x4, #0xc, #0x14
    // 0x8feb5c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8feb60: ldr             x3, [x3, #0x18]
    // 0x8feb64: ldr             x3, [x3, x4, lsl #3]
    // 0x8feb68: LoadField: r3 = r3->field_2b
    //     0x8feb68: ldur            w3, [x3, #0x2b]
    // 0x8feb6c: DecompressPointer r3
    //     0x8feb6c: add             x3, x3, HEAP, lsl #32
    // 0x8feb70: cmp             w3, NULL
    // 0x8feb74: b.eq            #0x8febd0
    // 0x8feb78: LoadField: r3 = r3->field_f
    //     0x8feb78: ldur            w3, [x3, #0xf]
    // 0x8feb7c: lsr             x3, x3, #3
    // 0x8feb80: r17 = 6718
    //     0x8feb80: movz            x17, #0x1a3e
    // 0x8feb84: cmp             x3, x17
    // 0x8feb88: b.eq            #0x8febd8
    // 0x8feb8c: r3 = SubtypeTestCache
    //     0x8feb8c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40430] SubtypeTestCache
    //     0x8feb90: ldr             x3, [x3, #0x430]
    // 0x8feb94: r30 = Subtype1TestCacheStub
    //     0x8feb94: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8feb98: LoadField: r30 = r30->field_7
    //     0x8feb98: ldur            lr, [lr, #7]
    // 0x8feb9c: blr             lr
    // 0x8feba0: cmp             w7, NULL
    // 0x8feba4: b.eq            #0x8febb0
    // 0x8feba8: tbnz            w7, #4, #0x8febd0
    // 0x8febac: b               #0x8febd8
    // 0x8febb0: r8 = List
    //     0x8febb0: add             x8, PP, #0x40, lsl #12  ; [pp+0x40438] Type: List
    //     0x8febb4: ldr             x8, [x8, #0x438]
    // 0x8febb8: r3 = SubtypeTestCache
    //     0x8febb8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40440] SubtypeTestCache
    //     0x8febbc: ldr             x3, [x3, #0x440]
    // 0x8febc0: r30 = InstanceOfStub
    //     0x8febc0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8febc4: LoadField: r30 = r30->field_7
    //     0x8febc4: ldur            lr, [lr, #7]
    // 0x8febc8: blr             lr
    // 0x8febcc: b               #0x8febdc
    // 0x8febd0: r0 = false
    //     0x8febd0: add             x0, NULL, #0x30  ; false
    // 0x8febd4: b               #0x8febdc
    // 0x8febd8: r0 = true
    //     0x8febd8: add             x0, NULL, #0x20  ; true
    // 0x8febdc: tbnz            w0, #4, #0x8fec60
    // 0x8febe0: ldur            x0, [fp, #-8]
    // 0x8febe4: r1 = Function '<anonymous closure>': static.
    //     0x8febe4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40448] AnonymousClosure: static (0x8fec84), in [package:nuonline/app/data/models/province.dart] Province::fromResponse (0x8feafc)
    //     0x8febe8: ldr             x1, [x1, #0x448]
    // 0x8febec: r2 = Null
    //     0x8febec: mov             x2, NULL
    // 0x8febf0: r0 = AllocateClosure()
    //     0x8febf0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8febf4: mov             x1, x0
    // 0x8febf8: ldur            x0, [fp, #-8]
    // 0x8febfc: r2 = LoadClassIdInstr(r0)
    //     0x8febfc: ldur            x2, [x0, #-1]
    //     0x8fec00: ubfx            x2, x2, #0xc, #0x14
    // 0x8fec04: r16 = <Province>
    //     0x8fec04: add             x16, PP, #8, lsl #12  ; [pp+0x80e8] TypeArguments: <Province>
    //     0x8fec08: ldr             x16, [x16, #0xe8]
    // 0x8fec0c: stp             x0, x16, [SP, #8]
    // 0x8fec10: str             x1, [SP]
    // 0x8fec14: mov             x0, x2
    // 0x8fec18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fec18: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fec1c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8fec1c: movz            x17, #0xf28c
    //     0x8fec20: add             lr, x0, x17
    //     0x8fec24: ldr             lr, [x21, lr, lsl #3]
    //     0x8fec28: blr             lr
    // 0x8fec2c: r1 = LoadClassIdInstr(r0)
    //     0x8fec2c: ldur            x1, [x0, #-1]
    //     0x8fec30: ubfx            x1, x1, #0xc, #0x14
    // 0x8fec34: mov             x16, x0
    // 0x8fec38: mov             x0, x1
    // 0x8fec3c: mov             x1, x16
    // 0x8fec40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fec40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fec44: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8fec44: movz            x17, #0xd889
    //     0x8fec48: add             lr, x0, x17
    //     0x8fec4c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fec50: blr             lr
    // 0x8fec54: LeaveFrame
    //     0x8fec54: mov             SP, fp
    //     0x8fec58: ldp             fp, lr, [SP], #0x10
    // 0x8fec5c: ret
    //     0x8fec5c: ret             
    // 0x8fec60: r1 = <Province>
    //     0x8fec60: add             x1, PP, #8, lsl #12  ; [pp+0x80e8] TypeArguments: <Province>
    //     0x8fec64: ldr             x1, [x1, #0xe8]
    // 0x8fec68: r2 = 0
    //     0x8fec68: movz            x2, #0
    // 0x8fec6c: r0 = _GrowableList()
    //     0x8fec6c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8fec70: LeaveFrame
    //     0x8fec70: mov             SP, fp
    //     0x8fec74: ldp             fp, lr, [SP], #0x10
    // 0x8fec78: ret
    //     0x8fec78: ret             
    // 0x8fec7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fec7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fec80: b               #0x8feb1c
  }
  [closure] static Province <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8fec84, size: 0x50
    // 0x8fec84: EnterFrame
    //     0x8fec84: stp             fp, lr, [SP, #-0x10]!
    //     0x8fec88: mov             fp, SP
    // 0x8fec8c: CheckStackOverflow
    //     0x8fec8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fec90: cmp             SP, x16
    //     0x8fec94: b.ls            #0x8feccc
    // 0x8fec98: ldr             x0, [fp, #0x10]
    // 0x8fec9c: r2 = Null
    //     0x8fec9c: mov             x2, NULL
    // 0x8feca0: r1 = Null
    //     0x8feca0: mov             x1, NULL
    // 0x8feca4: r8 = Map<String, dynamic>
    //     0x8feca4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8feca8: r3 = Null
    //     0x8feca8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40450] Null
    //     0x8fecac: ldr             x3, [x3, #0x450]
    // 0x8fecb0: r0 = Map<String, dynamic>()
    //     0x8fecb0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fecb4: ldr             x2, [fp, #0x10]
    // 0x8fecb8: r1 = Null
    //     0x8fecb8: mov             x1, NULL
    // 0x8fecbc: r0 = Province.fromMap()
    //     0x8fecbc: bl              #0x8fecd4  ; [package:nuonline/app/data/models/province.dart] Province::Province.fromMap
    // 0x8fecc0: LeaveFrame
    //     0x8fecc0: mov             SP, fp
    //     0x8fecc4: ldp             fp, lr, [SP], #0x10
    // 0x8fecc8: ret
    //     0x8fecc8: ret             
    // 0x8feccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8feccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fecd0: b               #0x8fec98
  }
  factory _ Province.fromMap(/* No info */) {
    // ** addr: 0x8fecd4, size: 0x14c
    // 0x8fecd4: EnterFrame
    //     0x8fecd4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fecd8: mov             fp, SP
    // 0x8fecdc: AllocStack(0x28)
    //     0x8fecdc: sub             SP, SP, #0x28
    // 0x8fece0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8fece0: mov             x3, x2
    //     0x8fece4: stur            x2, [fp, #-8]
    // 0x8fece8: CheckStackOverflow
    //     0x8fece8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fecec: cmp             SP, x16
    //     0x8fecf0: b.ls            #0x8fee18
    // 0x8fecf4: r0 = LoadClassIdInstr(r3)
    //     0x8fecf4: ldur            x0, [x3, #-1]
    //     0x8fecf8: ubfx            x0, x0, #0xc, #0x14
    // 0x8fecfc: mov             x1, x3
    // 0x8fed00: r2 = "id"
    //     0x8fed00: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8fed04: ldr             x2, [x2, #0x740]
    // 0x8fed08: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fed08: sub             lr, x0, #0x114
    //     0x8fed0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fed10: blr             lr
    // 0x8fed14: mov             x3, x0
    // 0x8fed18: r2 = Null
    //     0x8fed18: mov             x2, NULL
    // 0x8fed1c: r1 = Null
    //     0x8fed1c: mov             x1, NULL
    // 0x8fed20: stur            x3, [fp, #-0x10]
    // 0x8fed24: branchIfSmi(r0, 0x8fed4c)
    //     0x8fed24: tbz             w0, #0, #0x8fed4c
    // 0x8fed28: r4 = LoadClassIdInstr(r0)
    //     0x8fed28: ldur            x4, [x0, #-1]
    //     0x8fed2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fed30: sub             x4, x4, #0x3c
    // 0x8fed34: cmp             x4, #1
    // 0x8fed38: b.ls            #0x8fed4c
    // 0x8fed3c: r8 = int
    //     0x8fed3c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8fed40: r3 = Null
    //     0x8fed40: add             x3, PP, #0x40, lsl #12  ; [pp+0x40460] Null
    //     0x8fed44: ldr             x3, [x3, #0x460]
    // 0x8fed48: r0 = int()
    //     0x8fed48: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8fed4c: ldur            x1, [fp, #-8]
    // 0x8fed50: r0 = LoadClassIdInstr(r1)
    //     0x8fed50: ldur            x0, [x1, #-1]
    //     0x8fed54: ubfx            x0, x0, #0xc, #0x14
    // 0x8fed58: r2 = "name"
    //     0x8fed58: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8fed5c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8fed5c: sub             lr, x0, #0x114
    //     0x8fed60: ldr             lr, [x21, lr, lsl #3]
    //     0x8fed64: blr             lr
    // 0x8fed68: mov             x3, x0
    // 0x8fed6c: r2 = Null
    //     0x8fed6c: mov             x2, NULL
    // 0x8fed70: r1 = Null
    //     0x8fed70: mov             x1, NULL
    // 0x8fed74: stur            x3, [fp, #-8]
    // 0x8fed78: r4 = 60
    //     0x8fed78: movz            x4, #0x3c
    // 0x8fed7c: branchIfSmi(r0, 0x8fed88)
    //     0x8fed7c: tbz             w0, #0, #0x8fed88
    // 0x8fed80: r4 = LoadClassIdInstr(r0)
    //     0x8fed80: ldur            x4, [x0, #-1]
    //     0x8fed84: ubfx            x4, x4, #0xc, #0x14
    // 0x8fed88: sub             x4, x4, #0x5e
    // 0x8fed8c: cmp             x4, #1
    // 0x8fed90: b.ls            #0x8feda4
    // 0x8fed94: r8 = String
    //     0x8fed94: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8fed98: r3 = Null
    //     0x8fed98: add             x3, PP, #0x40, lsl #12  ; [pp+0x40470] Null
    //     0x8fed9c: ldr             x3, [x3, #0x470]
    // 0x8feda0: r0 = String()
    //     0x8feda0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8feda4: ldur            x0, [fp, #-0x10]
    // 0x8feda8: r1 = LoadInt32Instr(r0)
    //     0x8feda8: sbfx            x1, x0, #1, #0x1f
    //     0x8fedac: tbz             w0, #0, #0x8fedb4
    //     0x8fedb0: ldur            x1, [x0, #7]
    // 0x8fedb4: stur            x1, [fp, #-0x18]
    // 0x8fedb8: r0 = Province()
    //     0x8fedb8: bl              #0x8fee20  ; AllocateProvinceStub -> Province (size=0x20)
    // 0x8fedbc: mov             x1, x0
    // 0x8fedc0: ldur            x0, [fp, #-0x18]
    // 0x8fedc4: stur            x1, [fp, #-0x10]
    // 0x8fedc8: StoreField: r1->field_13 = r0
    //     0x8fedc8: stur            x0, [x1, #0x13]
    // 0x8fedcc: ldur            x0, [fp, #-8]
    // 0x8fedd0: StoreField: r1->field_1b = r0
    //     0x8fedd0: stur            w0, [x1, #0x1b]
    // 0x8fedd4: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8fedd4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8fedd8: ldr             x16, [x16, #0x9f8]
    // 0x8feddc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8fede0: stp             lr, x16, [SP]
    // 0x8fede4: r0 = Map._fromLiteral()
    //     0x8fede4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8fede8: ldur            x1, [fp, #-0x10]
    // 0x8fedec: StoreField: r1->field_f = r0
    //     0x8fedec: stur            w0, [x1, #0xf]
    //     0x8fedf0: ldurb           w16, [x1, #-1]
    //     0x8fedf4: ldurb           w17, [x0, #-1]
    //     0x8fedf8: and             x16, x17, x16, lsr #2
    //     0x8fedfc: tst             x16, HEAP, lsr #32
    //     0x8fee00: b.eq            #0x8fee08
    //     0x8fee04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8fee08: mov             x0, x1
    // 0x8fee0c: LeaveFrame
    //     0x8fee0c: mov             SP, fp
    //     0x8fee10: ldp             fp, lr, [SP], #0x10
    // 0x8fee14: ret
    //     0x8fee14: ret             
    // 0x8fee18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fee18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fee1c: b               #0x8fecf4
  }
}

// class id: 1651, size: 0x14, field offset: 0xc
class ProvinceAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa64c30, size: 0x2f4
    // 0xa64c30: EnterFrame
    //     0xa64c30: stp             fp, lr, [SP, #-0x10]!
    //     0xa64c34: mov             fp, SP
    // 0xa64c38: AllocStack(0x48)
    //     0xa64c38: sub             SP, SP, #0x48
    // 0xa64c3c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa64c3c: stur            x2, [fp, #-0x20]
    // 0xa64c40: CheckStackOverflow
    //     0xa64c40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa64c44: cmp             SP, x16
    //     0xa64c48: b.ls            #0xa64f0c
    // 0xa64c4c: LoadField: r3 = r2->field_23
    //     0xa64c4c: ldur            x3, [x2, #0x23]
    // 0xa64c50: add             x0, x3, #1
    // 0xa64c54: LoadField: r1 = r2->field_1b
    //     0xa64c54: ldur            x1, [x2, #0x1b]
    // 0xa64c58: cmp             x0, x1
    // 0xa64c5c: b.gt            #0xa64eb0
    // 0xa64c60: LoadField: r4 = r2->field_7
    //     0xa64c60: ldur            w4, [x2, #7]
    // 0xa64c64: DecompressPointer r4
    //     0xa64c64: add             x4, x4, HEAP, lsl #32
    // 0xa64c68: stur            x4, [fp, #-0x18]
    // 0xa64c6c: StoreField: r2->field_23 = r0
    //     0xa64c6c: stur            x0, [x2, #0x23]
    // 0xa64c70: LoadField: r0 = r4->field_13
    //     0xa64c70: ldur            w0, [x4, #0x13]
    // 0xa64c74: r5 = LoadInt32Instr(r0)
    //     0xa64c74: sbfx            x5, x0, #1, #0x1f
    // 0xa64c78: mov             x0, x5
    // 0xa64c7c: mov             x1, x3
    // 0xa64c80: stur            x5, [fp, #-0x10]
    // 0xa64c84: cmp             x1, x0
    // 0xa64c88: b.hs            #0xa64f14
    // 0xa64c8c: LoadField: r0 = r4->field_7
    //     0xa64c8c: ldur            x0, [x4, #7]
    // 0xa64c90: ldrb            w1, [x0, x3]
    // 0xa64c94: stur            x1, [fp, #-8]
    // 0xa64c98: r16 = <int, dynamic>
    //     0xa64c98: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa64c9c: ldr             x16, [x16, #0xac0]
    // 0xa64ca0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64ca4: stp             lr, x16, [SP]
    // 0xa64ca8: r0 = Map._fromLiteral()
    //     0xa64ca8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa64cac: mov             x2, x0
    // 0xa64cb0: stur            x2, [fp, #-0x38]
    // 0xa64cb4: r6 = 0
    //     0xa64cb4: movz            x6, #0
    // 0xa64cb8: ldur            x3, [fp, #-0x20]
    // 0xa64cbc: ldur            x4, [fp, #-0x18]
    // 0xa64cc0: ldur            x5, [fp, #-8]
    // 0xa64cc4: stur            x6, [fp, #-0x30]
    // 0xa64cc8: CheckStackOverflow
    //     0xa64cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa64ccc: cmp             SP, x16
    //     0xa64cd0: b.ls            #0xa64f18
    // 0xa64cd4: cmp             x6, x5
    // 0xa64cd8: b.ge            #0xa64d64
    // 0xa64cdc: LoadField: r7 = r3->field_23
    //     0xa64cdc: ldur            x7, [x3, #0x23]
    // 0xa64ce0: add             x0, x7, #1
    // 0xa64ce4: LoadField: r1 = r3->field_1b
    //     0xa64ce4: ldur            x1, [x3, #0x1b]
    // 0xa64ce8: cmp             x0, x1
    // 0xa64cec: b.gt            #0xa64ed8
    // 0xa64cf0: StoreField: r3->field_23 = r0
    //     0xa64cf0: stur            x0, [x3, #0x23]
    // 0xa64cf4: ldur            x0, [fp, #-0x10]
    // 0xa64cf8: mov             x1, x7
    // 0xa64cfc: cmp             x1, x0
    // 0xa64d00: b.hs            #0xa64f20
    // 0xa64d04: LoadField: r0 = r4->field_7
    //     0xa64d04: ldur            x0, [x4, #7]
    // 0xa64d08: ldrb            w8, [x0, x7]
    // 0xa64d0c: mov             x1, x3
    // 0xa64d10: stur            x8, [fp, #-0x28]
    // 0xa64d14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa64d14: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa64d18: r0 = read()
    //     0xa64d18: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa64d1c: mov             x1, x0
    // 0xa64d20: ldur            x0, [fp, #-0x28]
    // 0xa64d24: lsl             x2, x0, #1
    // 0xa64d28: r16 = LoadInt32Instr(r2)
    //     0xa64d28: sbfx            x16, x2, #1, #0x1f
    // 0xa64d2c: r17 = 11601
    //     0xa64d2c: movz            x17, #0x2d51
    // 0xa64d30: mul             x0, x16, x17
    // 0xa64d34: umulh           x16, x16, x17
    // 0xa64d38: eor             x0, x0, x16
    // 0xa64d3c: r0 = 0
    //     0xa64d3c: eor             x0, x0, x0, lsr #32
    // 0xa64d40: ubfiz           x0, x0, #1, #0x1e
    // 0xa64d44: r5 = LoadInt32Instr(r0)
    //     0xa64d44: sbfx            x5, x0, #1, #0x1f
    // 0xa64d48: mov             x3, x1
    // 0xa64d4c: ldur            x1, [fp, #-0x38]
    // 0xa64d50: r0 = _set()
    //     0xa64d50: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa64d54: ldur            x0, [fp, #-0x30]
    // 0xa64d58: add             x6, x0, #1
    // 0xa64d5c: ldur            x2, [fp, #-0x38]
    // 0xa64d60: b               #0xa64cb8
    // 0xa64d64: mov             x0, x2
    // 0xa64d68: mov             x1, x0
    // 0xa64d6c: r2 = 0
    //     0xa64d6c: movz            x2, #0
    // 0xa64d70: r0 = _getValueOrData()
    //     0xa64d70: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64d74: ldur            x3, [fp, #-0x38]
    // 0xa64d78: LoadField: r1 = r3->field_f
    //     0xa64d78: ldur            w1, [x3, #0xf]
    // 0xa64d7c: DecompressPointer r1
    //     0xa64d7c: add             x1, x1, HEAP, lsl #32
    // 0xa64d80: cmp             w1, w0
    // 0xa64d84: b.ne            #0xa64d90
    // 0xa64d88: r4 = Null
    //     0xa64d88: mov             x4, NULL
    // 0xa64d8c: b               #0xa64d94
    // 0xa64d90: mov             x4, x0
    // 0xa64d94: mov             x0, x4
    // 0xa64d98: stur            x4, [fp, #-0x18]
    // 0xa64d9c: r2 = Null
    //     0xa64d9c: mov             x2, NULL
    // 0xa64da0: r1 = Null
    //     0xa64da0: mov             x1, NULL
    // 0xa64da4: branchIfSmi(r0, 0xa64dcc)
    //     0xa64da4: tbz             w0, #0, #0xa64dcc
    // 0xa64da8: r4 = LoadClassIdInstr(r0)
    //     0xa64da8: ldur            x4, [x0, #-1]
    //     0xa64dac: ubfx            x4, x4, #0xc, #0x14
    // 0xa64db0: sub             x4, x4, #0x3c
    // 0xa64db4: cmp             x4, #1
    // 0xa64db8: b.ls            #0xa64dcc
    // 0xa64dbc: r8 = int
    //     0xa64dbc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa64dc0: r3 = Null
    //     0xa64dc0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21290] Null
    //     0xa64dc4: ldr             x3, [x3, #0x290]
    // 0xa64dc8: r0 = int()
    //     0xa64dc8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa64dcc: ldur            x1, [fp, #-0x38]
    // 0xa64dd0: r2 = 2
    //     0xa64dd0: movz            x2, #0x2
    // 0xa64dd4: r0 = _getValueOrData()
    //     0xa64dd4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64dd8: mov             x1, x0
    // 0xa64ddc: ldur            x0, [fp, #-0x38]
    // 0xa64de0: LoadField: r2 = r0->field_f
    //     0xa64de0: ldur            w2, [x0, #0xf]
    // 0xa64de4: DecompressPointer r2
    //     0xa64de4: add             x2, x2, HEAP, lsl #32
    // 0xa64de8: cmp             w2, w1
    // 0xa64dec: b.ne            #0xa64df8
    // 0xa64df0: r4 = Null
    //     0xa64df0: mov             x4, NULL
    // 0xa64df4: b               #0xa64dfc
    // 0xa64df8: mov             x4, x1
    // 0xa64dfc: ldur            x3, [fp, #-0x18]
    // 0xa64e00: mov             x0, x4
    // 0xa64e04: stur            x4, [fp, #-0x20]
    // 0xa64e08: r2 = Null
    //     0xa64e08: mov             x2, NULL
    // 0xa64e0c: r1 = Null
    //     0xa64e0c: mov             x1, NULL
    // 0xa64e10: r4 = 60
    //     0xa64e10: movz            x4, #0x3c
    // 0xa64e14: branchIfSmi(r0, 0xa64e20)
    //     0xa64e14: tbz             w0, #0, #0xa64e20
    // 0xa64e18: r4 = LoadClassIdInstr(r0)
    //     0xa64e18: ldur            x4, [x0, #-1]
    //     0xa64e1c: ubfx            x4, x4, #0xc, #0x14
    // 0xa64e20: sub             x4, x4, #0x5e
    // 0xa64e24: cmp             x4, #1
    // 0xa64e28: b.ls            #0xa64e3c
    // 0xa64e2c: r8 = String
    //     0xa64e2c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa64e30: r3 = Null
    //     0xa64e30: add             x3, PP, #0x21, lsl #12  ; [pp+0x212a0] Null
    //     0xa64e34: ldr             x3, [x3, #0x2a0]
    // 0xa64e38: r0 = String()
    //     0xa64e38: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa64e3c: ldur            x0, [fp, #-0x18]
    // 0xa64e40: r1 = LoadInt32Instr(r0)
    //     0xa64e40: sbfx            x1, x0, #1, #0x1f
    //     0xa64e44: tbz             w0, #0, #0xa64e4c
    //     0xa64e48: ldur            x1, [x0, #7]
    // 0xa64e4c: stur            x1, [fp, #-8]
    // 0xa64e50: r0 = Province()
    //     0xa64e50: bl              #0x8fee20  ; AllocateProvinceStub -> Province (size=0x20)
    // 0xa64e54: mov             x1, x0
    // 0xa64e58: ldur            x0, [fp, #-8]
    // 0xa64e5c: stur            x1, [fp, #-0x18]
    // 0xa64e60: StoreField: r1->field_13 = r0
    //     0xa64e60: stur            x0, [x1, #0x13]
    // 0xa64e64: ldur            x0, [fp, #-0x20]
    // 0xa64e68: StoreField: r1->field_1b = r0
    //     0xa64e68: stur            w0, [x1, #0x1b]
    // 0xa64e6c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa64e6c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa64e70: ldr             x16, [x16, #0x9f8]
    // 0xa64e74: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64e78: stp             lr, x16, [SP]
    // 0xa64e7c: r0 = Map._fromLiteral()
    //     0xa64e7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa64e80: ldur            x1, [fp, #-0x18]
    // 0xa64e84: StoreField: r1->field_f = r0
    //     0xa64e84: stur            w0, [x1, #0xf]
    //     0xa64e88: ldurb           w16, [x1, #-1]
    //     0xa64e8c: ldurb           w17, [x0, #-1]
    //     0xa64e90: and             x16, x17, x16, lsr #2
    //     0xa64e94: tst             x16, HEAP, lsr #32
    //     0xa64e98: b.eq            #0xa64ea0
    //     0xa64e9c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa64ea0: mov             x0, x1
    // 0xa64ea4: LeaveFrame
    //     0xa64ea4: mov             SP, fp
    //     0xa64ea8: ldp             fp, lr, [SP], #0x10
    // 0xa64eac: ret
    //     0xa64eac: ret             
    // 0xa64eb0: r0 = RangeError()
    //     0xa64eb0: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64eb4: mov             x1, x0
    // 0xa64eb8: r0 = "Not enough bytes available."
    //     0xa64eb8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64ebc: ldr             x0, [x0, #0x8a8]
    // 0xa64ec0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64ec0: stur            w0, [x1, #0x17]
    // 0xa64ec4: r2 = false
    //     0xa64ec4: add             x2, NULL, #0x30  ; false
    // 0xa64ec8: StoreField: r1->field_b = r2
    //     0xa64ec8: stur            w2, [x1, #0xb]
    // 0xa64ecc: mov             x0, x1
    // 0xa64ed0: r0 = Throw()
    //     0xa64ed0: bl              #0xec04b8  ; ThrowStub
    // 0xa64ed4: brk             #0
    // 0xa64ed8: r0 = "Not enough bytes available."
    //     0xa64ed8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64edc: ldr             x0, [x0, #0x8a8]
    // 0xa64ee0: r2 = false
    //     0xa64ee0: add             x2, NULL, #0x30  ; false
    // 0xa64ee4: r0 = RangeError()
    //     0xa64ee4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa64ee8: mov             x1, x0
    // 0xa64eec: r0 = "Not enough bytes available."
    //     0xa64eec: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa64ef0: ldr             x0, [x0, #0x8a8]
    // 0xa64ef4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa64ef4: stur            w0, [x1, #0x17]
    // 0xa64ef8: r0 = false
    //     0xa64ef8: add             x0, NULL, #0x30  ; false
    // 0xa64efc: StoreField: r1->field_b = r0
    //     0xa64efc: stur            w0, [x1, #0xb]
    // 0xa64f00: mov             x0, x1
    // 0xa64f04: r0 = Throw()
    //     0xa64f04: bl              #0xec04b8  ; ThrowStub
    // 0xa64f08: brk             #0
    // 0xa64f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64f10: b               #0xa64c4c
    // 0xa64f14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa64f14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa64f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64f18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64f1c: b               #0xa64cd4
    // 0xa64f20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa64f20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd62d4, size: 0x1f8
    // 0xbd62d4: EnterFrame
    //     0xbd62d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd62d8: mov             fp, SP
    // 0xbd62dc: AllocStack(0x28)
    //     0xbd62dc: sub             SP, SP, #0x28
    // 0xbd62e0: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd62e0: mov             x4, x2
    //     0xbd62e4: stur            x2, [fp, #-8]
    //     0xbd62e8: stur            x3, [fp, #-0x10]
    // 0xbd62ec: CheckStackOverflow
    //     0xbd62ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd62f0: cmp             SP, x16
    //     0xbd62f4: b.ls            #0xbd64b8
    // 0xbd62f8: mov             x0, x3
    // 0xbd62fc: r2 = Null
    //     0xbd62fc: mov             x2, NULL
    // 0xbd6300: r1 = Null
    //     0xbd6300: mov             x1, NULL
    // 0xbd6304: r4 = 60
    //     0xbd6304: movz            x4, #0x3c
    // 0xbd6308: branchIfSmi(r0, 0xbd6314)
    //     0xbd6308: tbz             w0, #0, #0xbd6314
    // 0xbd630c: r4 = LoadClassIdInstr(r0)
    //     0xbd630c: ldur            x4, [x0, #-1]
    //     0xbd6310: ubfx            x4, x4, #0xc, #0x14
    // 0xbd6314: cmp             x4, #0x642
    // 0xbd6318: b.eq            #0xbd6330
    // 0xbd631c: r8 = Province
    //     0xbd631c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b300] Type: Province
    //     0xbd6320: ldr             x8, [x8, #0x300]
    // 0xbd6324: r3 = Null
    //     0xbd6324: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b308] Null
    //     0xbd6328: ldr             x3, [x3, #0x308]
    // 0xbd632c: r0 = Province()
    //     0xbd632c: bl              #0x836c08  ; IsType_Province_Stub
    // 0xbd6330: ldur            x0, [fp, #-8]
    // 0xbd6334: LoadField: r1 = r0->field_b
    //     0xbd6334: ldur            w1, [x0, #0xb]
    // 0xbd6338: DecompressPointer r1
    //     0xbd6338: add             x1, x1, HEAP, lsl #32
    // 0xbd633c: LoadField: r2 = r1->field_13
    //     0xbd633c: ldur            w2, [x1, #0x13]
    // 0xbd6340: LoadField: r1 = r0->field_13
    //     0xbd6340: ldur            x1, [x0, #0x13]
    // 0xbd6344: r3 = LoadInt32Instr(r2)
    //     0xbd6344: sbfx            x3, x2, #1, #0x1f
    // 0xbd6348: sub             x2, x3, x1
    // 0xbd634c: cmp             x2, #1
    // 0xbd6350: b.ge            #0xbd6360
    // 0xbd6354: mov             x1, x0
    // 0xbd6358: r2 = 1
    //     0xbd6358: movz            x2, #0x1
    // 0xbd635c: r0 = _increaseBufferSize()
    //     0xbd635c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6360: ldur            x3, [fp, #-8]
    // 0xbd6364: r2 = 2
    //     0xbd6364: movz            x2, #0x2
    // 0xbd6368: LoadField: r4 = r3->field_b
    //     0xbd6368: ldur            w4, [x3, #0xb]
    // 0xbd636c: DecompressPointer r4
    //     0xbd636c: add             x4, x4, HEAP, lsl #32
    // 0xbd6370: LoadField: r5 = r3->field_13
    //     0xbd6370: ldur            x5, [x3, #0x13]
    // 0xbd6374: add             x6, x5, #1
    // 0xbd6378: StoreField: r3->field_13 = r6
    //     0xbd6378: stur            x6, [x3, #0x13]
    // 0xbd637c: LoadField: r0 = r4->field_13
    //     0xbd637c: ldur            w0, [x4, #0x13]
    // 0xbd6380: r7 = LoadInt32Instr(r0)
    //     0xbd6380: sbfx            x7, x0, #1, #0x1f
    // 0xbd6384: mov             x0, x7
    // 0xbd6388: mov             x1, x5
    // 0xbd638c: cmp             x1, x0
    // 0xbd6390: b.hs            #0xbd64c0
    // 0xbd6394: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd6394: add             x0, x4, x5
    //     0xbd6398: strb            w2, [x0, #0x17]
    // 0xbd639c: sub             x0, x7, x6
    // 0xbd63a0: cmp             x0, #1
    // 0xbd63a4: b.ge            #0xbd63b4
    // 0xbd63a8: mov             x1, x3
    // 0xbd63ac: r2 = 1
    //     0xbd63ac: movz            x2, #0x1
    // 0xbd63b0: r0 = _increaseBufferSize()
    //     0xbd63b0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd63b4: ldur            x2, [fp, #-8]
    // 0xbd63b8: ldur            x3, [fp, #-0x10]
    // 0xbd63bc: LoadField: r4 = r2->field_b
    //     0xbd63bc: ldur            w4, [x2, #0xb]
    // 0xbd63c0: DecompressPointer r4
    //     0xbd63c0: add             x4, x4, HEAP, lsl #32
    // 0xbd63c4: LoadField: r5 = r2->field_13
    //     0xbd63c4: ldur            x5, [x2, #0x13]
    // 0xbd63c8: add             x0, x5, #1
    // 0xbd63cc: StoreField: r2->field_13 = r0
    //     0xbd63cc: stur            x0, [x2, #0x13]
    // 0xbd63d0: LoadField: r0 = r4->field_13
    //     0xbd63d0: ldur            w0, [x4, #0x13]
    // 0xbd63d4: r1 = LoadInt32Instr(r0)
    //     0xbd63d4: sbfx            x1, x0, #1, #0x1f
    // 0xbd63d8: mov             x0, x1
    // 0xbd63dc: mov             x1, x5
    // 0xbd63e0: cmp             x1, x0
    // 0xbd63e4: b.hs            #0xbd64c4
    // 0xbd63e8: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd63e8: add             x0, x4, x5
    //     0xbd63ec: strb            wzr, [x0, #0x17]
    // 0xbd63f0: LoadField: r4 = r3->field_13
    //     0xbd63f0: ldur            x4, [x3, #0x13]
    // 0xbd63f4: r0 = BoxInt64Instr(r4)
    //     0xbd63f4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd63f8: cmp             x4, x0, asr #1
    //     0xbd63fc: b.eq            #0xbd6408
    //     0xbd6400: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6404: stur            x4, [x0, #7]
    // 0xbd6408: r16 = <int>
    //     0xbd6408: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd640c: stp             x2, x16, [SP, #8]
    // 0xbd6410: str             x0, [SP]
    // 0xbd6414: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6414: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6418: r0 = write()
    //     0xbd6418: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd641c: ldur            x0, [fp, #-8]
    // 0xbd6420: LoadField: r1 = r0->field_b
    //     0xbd6420: ldur            w1, [x0, #0xb]
    // 0xbd6424: DecompressPointer r1
    //     0xbd6424: add             x1, x1, HEAP, lsl #32
    // 0xbd6428: LoadField: r2 = r1->field_13
    //     0xbd6428: ldur            w2, [x1, #0x13]
    // 0xbd642c: LoadField: r1 = r0->field_13
    //     0xbd642c: ldur            x1, [x0, #0x13]
    // 0xbd6430: r3 = LoadInt32Instr(r2)
    //     0xbd6430: sbfx            x3, x2, #1, #0x1f
    // 0xbd6434: sub             x2, x3, x1
    // 0xbd6438: cmp             x2, #1
    // 0xbd643c: b.ge            #0xbd644c
    // 0xbd6440: mov             x1, x0
    // 0xbd6444: r2 = 1
    //     0xbd6444: movz            x2, #0x1
    // 0xbd6448: r0 = _increaseBufferSize()
    //     0xbd6448: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd644c: ldur            x2, [fp, #-8]
    // 0xbd6450: ldur            x3, [fp, #-0x10]
    // 0xbd6454: r4 = 1
    //     0xbd6454: movz            x4, #0x1
    // 0xbd6458: LoadField: r5 = r2->field_b
    //     0xbd6458: ldur            w5, [x2, #0xb]
    // 0xbd645c: DecompressPointer r5
    //     0xbd645c: add             x5, x5, HEAP, lsl #32
    // 0xbd6460: LoadField: r6 = r2->field_13
    //     0xbd6460: ldur            x6, [x2, #0x13]
    // 0xbd6464: add             x0, x6, #1
    // 0xbd6468: StoreField: r2->field_13 = r0
    //     0xbd6468: stur            x0, [x2, #0x13]
    // 0xbd646c: LoadField: r0 = r5->field_13
    //     0xbd646c: ldur            w0, [x5, #0x13]
    // 0xbd6470: r1 = LoadInt32Instr(r0)
    //     0xbd6470: sbfx            x1, x0, #1, #0x1f
    // 0xbd6474: mov             x0, x1
    // 0xbd6478: mov             x1, x6
    // 0xbd647c: cmp             x1, x0
    // 0xbd6480: b.hs            #0xbd64c8
    // 0xbd6484: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6484: add             x0, x5, x6
    //     0xbd6488: strb            w4, [x0, #0x17]
    // 0xbd648c: LoadField: r0 = r3->field_1b
    //     0xbd648c: ldur            w0, [x3, #0x1b]
    // 0xbd6490: DecompressPointer r0
    //     0xbd6490: add             x0, x0, HEAP, lsl #32
    // 0xbd6494: r16 = <String>
    //     0xbd6494: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd6498: stp             x2, x16, [SP, #8]
    // 0xbd649c: str             x0, [SP]
    // 0xbd64a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd64a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd64a4: r0 = write()
    //     0xbd64a4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd64a8: r0 = Null
    //     0xbd64a8: mov             x0, NULL
    // 0xbd64ac: LeaveFrame
    //     0xbd64ac: mov             SP, fp
    //     0xbd64b0: ldp             fp, lr, [SP], #0x10
    // 0xbd64b4: ret
    //     0xbd64b4: ret             
    // 0xbd64b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd64b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd64bc: b               #0xbd62f8
    // 0xbd64c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd64c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd64c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd64c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd64c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd64c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf02ac, size: 0x24
    // 0xbf02ac: r1 = 2
    //     0xbf02ac: movz            x1, #0x2
    // 0xbf02b0: r16 = LoadInt32Instr(r1)
    //     0xbf02b0: sbfx            x16, x1, #1, #0x1f
    // 0xbf02b4: r17 = 11601
    //     0xbf02b4: movz            x17, #0x2d51
    // 0xbf02b8: mul             x0, x16, x17
    // 0xbf02bc: umulh           x16, x16, x17
    // 0xbf02c0: eor             x0, x0, x16
    // 0xbf02c4: r0 = 0
    //     0xbf02c4: eor             x0, x0, x0, lsr #32
    // 0xbf02c8: ubfiz           x0, x0, #1, #0x1e
    // 0xbf02cc: ret
    //     0xbf02cc: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76c08, size: 0x9c
    // 0xd76c08: EnterFrame
    //     0xd76c08: stp             fp, lr, [SP, #-0x10]!
    //     0xd76c0c: mov             fp, SP
    // 0xd76c10: AllocStack(0x10)
    //     0xd76c10: sub             SP, SP, #0x10
    // 0xd76c14: CheckStackOverflow
    //     0xd76c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76c18: cmp             SP, x16
    //     0xd76c1c: b.ls            #0xd76c9c
    // 0xd76c20: ldr             x0, [fp, #0x10]
    // 0xd76c24: cmp             w0, NULL
    // 0xd76c28: b.ne            #0xd76c3c
    // 0xd76c2c: r0 = false
    //     0xd76c2c: add             x0, NULL, #0x30  ; false
    // 0xd76c30: LeaveFrame
    //     0xd76c30: mov             SP, fp
    //     0xd76c34: ldp             fp, lr, [SP], #0x10
    // 0xd76c38: ret
    //     0xd76c38: ret             
    // 0xd76c3c: ldr             x1, [fp, #0x18]
    // 0xd76c40: cmp             w1, w0
    // 0xd76c44: b.ne            #0xd76c50
    // 0xd76c48: r0 = true
    //     0xd76c48: add             x0, NULL, #0x20  ; true
    // 0xd76c4c: b               #0xd76c90
    // 0xd76c50: r1 = 60
    //     0xd76c50: movz            x1, #0x3c
    // 0xd76c54: branchIfSmi(r0, 0xd76c60)
    //     0xd76c54: tbz             w0, #0, #0xd76c60
    // 0xd76c58: r1 = LoadClassIdInstr(r0)
    //     0xd76c58: ldur            x1, [x0, #-1]
    //     0xd76c5c: ubfx            x1, x1, #0xc, #0x14
    // 0xd76c60: cmp             x1, #0x673
    // 0xd76c64: b.ne            #0xd76c8c
    // 0xd76c68: r16 = ProvinceAdapter
    //     0xd76c68: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2f8] Type: ProvinceAdapter
    //     0xd76c6c: ldr             x16, [x16, #0x2f8]
    // 0xd76c70: r30 = ProvinceAdapter
    //     0xd76c70: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b2f8] Type: ProvinceAdapter
    //     0xd76c74: ldr             lr, [lr, #0x2f8]
    // 0xd76c78: stp             lr, x16, [SP]
    // 0xd76c7c: r0 = ==()
    //     0xd76c7c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76c80: tbnz            w0, #4, #0xd76c8c
    // 0xd76c84: r0 = true
    //     0xd76c84: add             x0, NULL, #0x20  ; true
    // 0xd76c88: b               #0xd76c90
    // 0xd76c8c: r0 = false
    //     0xd76c8c: add             x0, NULL, #0x30  ; false
    // 0xd76c90: LeaveFrame
    //     0xd76c90: mov             SP, fp
    //     0xd76c94: ldp             fp, lr, [SP], #0x10
    // 0xd76c98: ret
    //     0xd76c98: ret             
    // 0xd76c9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76c9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76ca0: b               #0xd76c20
  }
}
