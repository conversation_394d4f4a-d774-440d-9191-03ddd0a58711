// lib: , url: package:nuonline/app/data/models/quran_setting.dart

// class id: 1050043, size: 0x8
class :: {
}

// class id: 6834, size: 0x14, field offset: 0x14
enum QuranListViewType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d370, size: 0x64
    // 0xc4d370: EnterFrame
    //     0xc4d370: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d374: mov             fp, SP
    // 0xc4d378: AllocStack(0x10)
    //     0xc4d378: sub             SP, SP, #0x10
    // 0xc4d37c: SetupParameters(QuranListViewType this /* r1 => r0, fp-0x8 */)
    //     0xc4d37c: mov             x0, x1
    //     0xc4d380: stur            x1, [fp, #-8]
    // 0xc4d384: CheckStackOverflow
    //     0xc4d384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d388: cmp             SP, x16
    //     0xc4d38c: b.ls            #0xc4d3cc
    // 0xc4d390: r1 = Null
    //     0xc4d390: mov             x1, NULL
    // 0xc4d394: r2 = 4
    //     0xc4d394: movz            x2, #0x4
    // 0xc4d398: r0 = AllocateArray()
    //     0xc4d398: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d39c: r16 = "QuranListViewType."
    //     0xc4d39c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37eb0] "QuranListViewType."
    //     0xc4d3a0: ldr             x16, [x16, #0xeb0]
    // 0xc4d3a4: StoreField: r0->field_f = r16
    //     0xc4d3a4: stur            w16, [x0, #0xf]
    // 0xc4d3a8: ldur            x1, [fp, #-8]
    // 0xc4d3ac: LoadField: r2 = r1->field_f
    //     0xc4d3ac: ldur            w2, [x1, #0xf]
    // 0xc4d3b0: DecompressPointer r2
    //     0xc4d3b0: add             x2, x2, HEAP, lsl #32
    // 0xc4d3b4: StoreField: r0->field_13 = r2
    //     0xc4d3b4: stur            w2, [x0, #0x13]
    // 0xc4d3b8: str             x0, [SP]
    // 0xc4d3bc: r0 = _interpolate()
    //     0xc4d3bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d3c0: LeaveFrame
    //     0xc4d3c0: mov             SP, fp
    //     0xc4d3c4: ldp             fp, lr, [SP], #0x10
    // 0xc4d3c8: ret
    //     0xc4d3c8: ret             
    // 0xc4d3cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d3cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d3d0: b               #0xc4d390
  }
}

// class id: 6835, size: 0x14, field offset: 0x14
enum QuranMainViewType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d30c, size: 0x64
    // 0xc4d30c: EnterFrame
    //     0xc4d30c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d310: mov             fp, SP
    // 0xc4d314: AllocStack(0x10)
    //     0xc4d314: sub             SP, SP, #0x10
    // 0xc4d318: SetupParameters(QuranMainViewType this /* r1 => r0, fp-0x8 */)
    //     0xc4d318: mov             x0, x1
    //     0xc4d31c: stur            x1, [fp, #-8]
    // 0xc4d320: CheckStackOverflow
    //     0xc4d320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d324: cmp             SP, x16
    //     0xc4d328: b.ls            #0xc4d368
    // 0xc4d32c: r1 = Null
    //     0xc4d32c: mov             x1, NULL
    // 0xc4d330: r2 = 4
    //     0xc4d330: movz            x2, #0x4
    // 0xc4d334: r0 = AllocateArray()
    //     0xc4d334: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d338: r16 = "QuranMainViewType."
    //     0xc4d338: add             x16, PP, #0x37, lsl #12  ; [pp+0x37eb8] "QuranMainViewType."
    //     0xc4d33c: ldr             x16, [x16, #0xeb8]
    // 0xc4d340: StoreField: r0->field_f = r16
    //     0xc4d340: stur            w16, [x0, #0xf]
    // 0xc4d344: ldur            x1, [fp, #-8]
    // 0xc4d348: LoadField: r2 = r1->field_f
    //     0xc4d348: ldur            w2, [x1, #0xf]
    // 0xc4d34c: DecompressPointer r2
    //     0xc4d34c: add             x2, x2, HEAP, lsl #32
    // 0xc4d350: StoreField: r0->field_13 = r2
    //     0xc4d350: stur            w2, [x0, #0x13]
    // 0xc4d354: str             x0, [SP]
    // 0xc4d358: r0 = _interpolate()
    //     0xc4d358: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d35c: LeaveFrame
    //     0xc4d35c: mov             SP, fp
    //     0xc4d360: ldp             fp, lr, [SP], #0x10
    // 0xc4d364: ret
    //     0xc4d364: ret             
    // 0xc4d368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d368: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d36c: b               #0xc4d32c
  }
}
