// lib: , url: package:nuonline/app/data/models/qurban.dart

// class id: 1050044, size: 0x8
class :: {
}

// class id: 1130, size: 0x20, field offset: 0x8
//   const constructor, 
class Qurban extends Object {

  factory _ Qurban.fromMap(/* No info */) {
    // ** addr: 0x7e9de0, size: 0x2d4
    // 0x7e9de0: EnterFrame
    //     0x7e9de0: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9de4: mov             fp, SP
    // 0x7e9de8: AllocStack(0x30)
    //     0x7e9de8: sub             SP, SP, #0x30
    // 0x7e9dec: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7e9dec: mov             x0, x2
    //     0x7e9df0: stur            x2, [fp, #-8]
    // 0x7e9df4: CheckStackOverflow
    //     0x7e9df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9df8: cmp             SP, x16
    //     0x7e9dfc: b.ls            #0x7ea0ac
    // 0x7e9e00: r1 = "Asia/Jakarta"
    //     0x7e9e00: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b210] "Asia/Jakarta"
    //     0x7e9e04: ldr             x1, [x1, #0x210]
    // 0x7e9e08: r0 = getLocation()
    //     0x7e9e08: bl              #0x6fdf24  ; [package:timezone/src/env.dart] ::getLocation
    // 0x7e9e0c: mov             x4, x0
    // 0x7e9e10: ldur            x3, [fp, #-8]
    // 0x7e9e14: stur            x4, [fp, #-0x10]
    // 0x7e9e18: r0 = LoadClassIdInstr(r3)
    //     0x7e9e18: ldur            x0, [x3, #-1]
    //     0x7e9e1c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9e20: mov             x1, x3
    // 0x7e9e24: r2 = "start_date"
    //     0x7e9e24: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a28] "start_date"
    //     0x7e9e28: ldr             x2, [x2, #0xa28]
    // 0x7e9e2c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9e2c: sub             lr, x0, #0x114
    //     0x7e9e30: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9e34: blr             lr
    // 0x7e9e38: mov             x3, x0
    // 0x7e9e3c: r2 = Null
    //     0x7e9e3c: mov             x2, NULL
    // 0x7e9e40: r1 = Null
    //     0x7e9e40: mov             x1, NULL
    // 0x7e9e44: stur            x3, [fp, #-0x18]
    // 0x7e9e48: r4 = 60
    //     0x7e9e48: movz            x4, #0x3c
    // 0x7e9e4c: branchIfSmi(r0, 0x7e9e58)
    //     0x7e9e4c: tbz             w0, #0, #0x7e9e58
    // 0x7e9e50: r4 = LoadClassIdInstr(r0)
    //     0x7e9e50: ldur            x4, [x0, #-1]
    //     0x7e9e54: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9e58: sub             x4, x4, #0x5e
    // 0x7e9e5c: cmp             x4, #1
    // 0x7e9e60: b.ls            #0x7e9e74
    // 0x7e9e64: r8 = String
    //     0x7e9e64: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e9e68: r3 = Null
    //     0x7e9e68: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d210] Null
    //     0x7e9e6c: ldr             x3, [x3, #0x210]
    // 0x7e9e70: r0 = String()
    //     0x7e9e70: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e9e74: ldur            x1, [fp, #-0x10]
    // 0x7e9e78: ldur            x2, [fp, #-0x18]
    // 0x7e9e7c: r0 = parse()
    //     0x7e9e7c: bl              #0x7ea0c0  ; [package:timezone/src/date_time.dart] TZDateTime::parse
    // 0x7e9e80: r1 = "Asia/Jakarta"
    //     0x7e9e80: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b210] "Asia/Jakarta"
    //     0x7e9e84: ldr             x1, [x1, #0x210]
    // 0x7e9e88: stur            x0, [fp, #-0x10]
    // 0x7e9e8c: r0 = getLocation()
    //     0x7e9e8c: bl              #0x6fdf24  ; [package:timezone/src/env.dart] ::getLocation
    // 0x7e9e90: mov             x4, x0
    // 0x7e9e94: ldur            x3, [fp, #-8]
    // 0x7e9e98: stur            x4, [fp, #-0x18]
    // 0x7e9e9c: r0 = LoadClassIdInstr(r3)
    //     0x7e9e9c: ldur            x0, [x3, #-1]
    //     0x7e9ea0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9ea4: mov             x1, x3
    // 0x7e9ea8: r2 = "end_date"
    //     0x7e9ea8: add             x2, PP, #0x32, lsl #12  ; [pp+0x32a40] "end_date"
    //     0x7e9eac: ldr             x2, [x2, #0xa40]
    // 0x7e9eb0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9eb0: sub             lr, x0, #0x114
    //     0x7e9eb4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9eb8: blr             lr
    // 0x7e9ebc: mov             x3, x0
    // 0x7e9ec0: r2 = Null
    //     0x7e9ec0: mov             x2, NULL
    // 0x7e9ec4: r1 = Null
    //     0x7e9ec4: mov             x1, NULL
    // 0x7e9ec8: stur            x3, [fp, #-0x20]
    // 0x7e9ecc: r4 = 60
    //     0x7e9ecc: movz            x4, #0x3c
    // 0x7e9ed0: branchIfSmi(r0, 0x7e9edc)
    //     0x7e9ed0: tbz             w0, #0, #0x7e9edc
    // 0x7e9ed4: r4 = LoadClassIdInstr(r0)
    //     0x7e9ed4: ldur            x4, [x0, #-1]
    //     0x7e9ed8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9edc: sub             x4, x4, #0x5e
    // 0x7e9ee0: cmp             x4, #1
    // 0x7e9ee4: b.ls            #0x7e9ef8
    // 0x7e9ee8: r8 = String
    //     0x7e9ee8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7e9eec: r3 = Null
    //     0x7e9eec: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d220] Null
    //     0x7e9ef0: ldr             x3, [x3, #0x220]
    // 0x7e9ef4: r0 = String()
    //     0x7e9ef4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7e9ef8: ldur            x1, [fp, #-0x18]
    // 0x7e9efc: ldur            x2, [fp, #-0x20]
    // 0x7e9f00: r0 = parse()
    //     0x7e9f00: bl              #0x7ea0c0  ; [package:timezone/src/date_time.dart] TZDateTime::parse
    // 0x7e9f04: mov             x4, x0
    // 0x7e9f08: ldur            x3, [fp, #-8]
    // 0x7e9f0c: stur            x4, [fp, #-0x18]
    // 0x7e9f10: r0 = LoadClassIdInstr(r3)
    //     0x7e9f10: ldur            x0, [x3, #-1]
    //     0x7e9f14: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9f18: mov             x1, x3
    // 0x7e9f1c: r2 = "image"
    //     0x7e9f1c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x7e9f20: ldr             x2, [x2, #0x520]
    // 0x7e9f24: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9f24: sub             lr, x0, #0x114
    //     0x7e9f28: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9f2c: blr             lr
    // 0x7e9f30: mov             x3, x0
    // 0x7e9f34: r2 = Null
    //     0x7e9f34: mov             x2, NULL
    // 0x7e9f38: r1 = Null
    //     0x7e9f38: mov             x1, NULL
    // 0x7e9f3c: stur            x3, [fp, #-0x20]
    // 0x7e9f40: r4 = 60
    //     0x7e9f40: movz            x4, #0x3c
    // 0x7e9f44: branchIfSmi(r0, 0x7e9f50)
    //     0x7e9f44: tbz             w0, #0, #0x7e9f50
    // 0x7e9f48: r4 = LoadClassIdInstr(r0)
    //     0x7e9f48: ldur            x4, [x0, #-1]
    //     0x7e9f4c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9f50: sub             x4, x4, #0x5e
    // 0x7e9f54: cmp             x4, #1
    // 0x7e9f58: b.ls            #0x7e9f6c
    // 0x7e9f5c: r8 = String?
    //     0x7e9f5c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e9f60: r3 = Null
    //     0x7e9f60: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d230] Null
    //     0x7e9f64: ldr             x3, [x3, #0x230]
    // 0x7e9f68: r0 = String?()
    //     0x7e9f68: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e9f6c: ldur            x0, [fp, #-0x20]
    // 0x7e9f70: cmp             w0, NULL
    // 0x7e9f74: b.ne            #0x7e9f80
    // 0x7e9f78: r4 = ""
    //     0x7e9f78: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e9f7c: b               #0x7e9f84
    // 0x7e9f80: mov             x4, x0
    // 0x7e9f84: ldur            x3, [fp, #-8]
    // 0x7e9f88: stur            x4, [fp, #-0x20]
    // 0x7e9f8c: r0 = LoadClassIdInstr(r3)
    //     0x7e9f8c: ldur            x0, [x3, #-1]
    //     0x7e9f90: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9f94: mov             x1, x3
    // 0x7e9f98: r2 = "total_qurban"
    //     0x7e9f98: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d240] "total_qurban"
    //     0x7e9f9c: ldr             x2, [x2, #0x240]
    // 0x7e9fa0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9fa0: sub             lr, x0, #0x114
    //     0x7e9fa4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9fa8: blr             lr
    // 0x7e9fac: mov             x3, x0
    // 0x7e9fb0: r2 = Null
    //     0x7e9fb0: mov             x2, NULL
    // 0x7e9fb4: r1 = Null
    //     0x7e9fb4: mov             x1, NULL
    // 0x7e9fb8: stur            x3, [fp, #-0x28]
    // 0x7e9fbc: branchIfSmi(r0, 0x7e9fe4)
    //     0x7e9fbc: tbz             w0, #0, #0x7e9fe4
    // 0x7e9fc0: r4 = LoadClassIdInstr(r0)
    //     0x7e9fc0: ldur            x4, [x0, #-1]
    //     0x7e9fc4: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9fc8: sub             x4, x4, #0x3c
    // 0x7e9fcc: cmp             x4, #1
    // 0x7e9fd0: b.ls            #0x7e9fe4
    // 0x7e9fd4: r8 = int?
    //     0x7e9fd4: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e9fd8: r3 = Null
    //     0x7e9fd8: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d248] Null
    //     0x7e9fdc: ldr             x3, [x3, #0x248]
    // 0x7e9fe0: r0 = int?()
    //     0x7e9fe0: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e9fe4: ldur            x0, [fp, #-0x28]
    // 0x7e9fe8: cmp             w0, NULL
    // 0x7e9fec: b.ne            #0x7e9ff8
    // 0x7e9ff0: r6 = 0
    //     0x7e9ff0: movz            x6, #0
    // 0x7e9ff4: b               #0x7ea008
    // 0x7e9ff8: r1 = LoadInt32Instr(r0)
    //     0x7e9ff8: sbfx            x1, x0, #1, #0x1f
    //     0x7e9ffc: tbz             w0, #0, #0x7ea004
    //     0x7ea000: ldur            x1, [x0, #7]
    // 0x7ea004: mov             x6, x1
    // 0x7ea008: ldur            x1, [fp, #-8]
    // 0x7ea00c: ldur            x5, [fp, #-0x10]
    // 0x7ea010: ldur            x4, [fp, #-0x18]
    // 0x7ea014: ldur            x3, [fp, #-0x20]
    // 0x7ea018: stur            x6, [fp, #-0x30]
    // 0x7ea01c: r0 = LoadClassIdInstr(r1)
    //     0x7ea01c: ldur            x0, [x1, #-1]
    //     0x7ea020: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea024: r2 = "url"
    //     0x7ea024: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x7ea028: ldr             x2, [x2, #0xd78]
    // 0x7ea02c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ea02c: sub             lr, x0, #0x114
    //     0x7ea030: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea034: blr             lr
    // 0x7ea038: mov             x3, x0
    // 0x7ea03c: r2 = Null
    //     0x7ea03c: mov             x2, NULL
    // 0x7ea040: r1 = Null
    //     0x7ea040: mov             x1, NULL
    // 0x7ea044: stur            x3, [fp, #-8]
    // 0x7ea048: r4 = 60
    //     0x7ea048: movz            x4, #0x3c
    // 0x7ea04c: branchIfSmi(r0, 0x7ea058)
    //     0x7ea04c: tbz             w0, #0, #0x7ea058
    // 0x7ea050: r4 = LoadClassIdInstr(r0)
    //     0x7ea050: ldur            x4, [x0, #-1]
    //     0x7ea054: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea058: sub             x4, x4, #0x5e
    // 0x7ea05c: cmp             x4, #1
    // 0x7ea060: b.ls            #0x7ea074
    // 0x7ea064: r8 = String?
    //     0x7ea064: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7ea068: r3 = Null
    //     0x7ea068: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d258] Null
    //     0x7ea06c: ldr             x3, [x3, #0x258]
    // 0x7ea070: r0 = String?()
    //     0x7ea070: bl              #0x600324  ; IsType_String?_Stub
    // 0x7ea074: r0 = Qurban()
    //     0x7ea074: bl              #0x7ea0b4  ; AllocateQurbanStub -> Qurban (size=0x20)
    // 0x7ea078: ldur            x1, [fp, #-0x10]
    // 0x7ea07c: StoreField: r0->field_7 = r1
    //     0x7ea07c: stur            w1, [x0, #7]
    // 0x7ea080: ldur            x1, [fp, #-0x18]
    // 0x7ea084: StoreField: r0->field_b = r1
    //     0x7ea084: stur            w1, [x0, #0xb]
    // 0x7ea088: ldur            x1, [fp, #-0x20]
    // 0x7ea08c: StoreField: r0->field_f = r1
    //     0x7ea08c: stur            w1, [x0, #0xf]
    // 0x7ea090: ldur            x1, [fp, #-0x30]
    // 0x7ea094: StoreField: r0->field_13 = r1
    //     0x7ea094: stur            x1, [x0, #0x13]
    // 0x7ea098: ldur            x1, [fp, #-8]
    // 0x7ea09c: StoreField: r0->field_1b = r1
    //     0x7ea09c: stur            w1, [x0, #0x1b]
    // 0x7ea0a0: LeaveFrame
    //     0x7ea0a0: mov             SP, fp
    //     0x7ea0a4: ldp             fp, lr, [SP], #0x10
    // 0x7ea0a8: ret
    //     0x7ea0a8: ret             
    // 0x7ea0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea0b0: b               #0x7e9e00
  }
  get _ progress(/* No info */) {
    // ** addr: 0xb3c2ec, size: 0x90
    // 0xb3c2ec: EnterFrame
    //     0xb3c2ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c2f0: mov             fp, SP
    // 0xb3c2f4: AllocStack(0x18)
    //     0xb3c2f4: sub             SP, SP, #0x18
    // 0xb3c2f8: SetupParameters(Qurban this /* r1 => r0, fp-0x8 */)
    //     0xb3c2f8: mov             x0, x1
    //     0xb3c2fc: stur            x1, [fp, #-8]
    // 0xb3c300: CheckStackOverflow
    //     0xb3c300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c304: cmp             SP, x16
    //     0xb3c308: b.ls            #0xb3c374
    // 0xb3c30c: mov             x1, x0
    // 0xb3c310: r0 = totalDay()
    //     0xb3c310: bl              #0xb3c37c  ; [package:nuonline/app/data/models/qurban.dart] Qurban::totalDay
    // 0xb3c314: ldur            x1, [fp, #-8]
    // 0xb3c318: stur            x0, [fp, #-0x10]
    // 0xb3c31c: r0 = remainingDay()
    //     0xb3c31c: bl              #0xb3c3cc  ; [package:nuonline/app/data/models/qurban.dart] Qurban::remainingDay
    // 0xb3c320: mov             x1, x0
    // 0xb3c324: ldur            x0, [fp, #-0x10]
    // 0xb3c328: sub             x3, x0, x1
    // 0xb3c32c: ldur            x0, [fp, #-8]
    // 0xb3c330: stur            x3, [fp, #-0x18]
    // 0xb3c334: LoadField: r1 = r0->field_b
    //     0xb3c334: ldur            w1, [x0, #0xb]
    // 0xb3c338: DecompressPointer r1
    //     0xb3c338: add             x1, x1, HEAP, lsl #32
    // 0xb3c33c: LoadField: r2 = r0->field_7
    //     0xb3c33c: ldur            w2, [x0, #7]
    // 0xb3c340: DecompressPointer r2
    //     0xb3c340: add             x2, x2, HEAP, lsl #32
    // 0xb3c344: r0 = difference()
    //     0xb3c344: bl              #0xeb3728  ; [package:timezone/src/date_time.dart] TZDateTime::difference
    // 0xb3c348: LoadField: r1 = r0->field_7
    //     0xb3c348: ldur            x1, [x0, #7]
    // 0xb3c34c: r0 = 86400000000
    //     0xb3c34c: add             x0, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xb3c350: ldr             x0, [x0, #0x268]
    // 0xb3c354: sdiv            x2, x1, x0
    // 0xb3c358: ldur            x0, [fp, #-0x18]
    // 0xb3c35c: scvtf           d1, x0
    // 0xb3c360: scvtf           d2, x2
    // 0xb3c364: fdiv            d0, d1, d2
    // 0xb3c368: LeaveFrame
    //     0xb3c368: mov             SP, fp
    //     0xb3c36c: ldp             fp, lr, [SP], #0x10
    // 0xb3c370: ret
    //     0xb3c370: ret             
    // 0xb3c374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c374: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c378: b               #0xb3c30c
  }
  get _ totalDay(/* No info */) {
    // ** addr: 0xb3c37c, size: 0x50
    // 0xb3c37c: EnterFrame
    //     0xb3c37c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c380: mov             fp, SP
    // 0xb3c384: CheckStackOverflow
    //     0xb3c384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c388: cmp             SP, x16
    //     0xb3c38c: b.ls            #0xb3c3c4
    // 0xb3c390: LoadField: r0 = r1->field_b
    //     0xb3c390: ldur            w0, [x1, #0xb]
    // 0xb3c394: DecompressPointer r0
    //     0xb3c394: add             x0, x0, HEAP, lsl #32
    // 0xb3c398: LoadField: r2 = r1->field_7
    //     0xb3c398: ldur            w2, [x1, #7]
    // 0xb3c39c: DecompressPointer r2
    //     0xb3c39c: add             x2, x2, HEAP, lsl #32
    // 0xb3c3a0: mov             x1, x0
    // 0xb3c3a4: r0 = difference()
    //     0xb3c3a4: bl              #0xeb3728  ; [package:timezone/src/date_time.dart] TZDateTime::difference
    // 0xb3c3a8: LoadField: r1 = r0->field_7
    //     0xb3c3a8: ldur            x1, [x0, #7]
    // 0xb3c3ac: r2 = 86400000000
    //     0xb3c3ac: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xb3c3b0: ldr             x2, [x2, #0x268]
    // 0xb3c3b4: sdiv            x0, x1, x2
    // 0xb3c3b8: LeaveFrame
    //     0xb3c3b8: mov             SP, fp
    //     0xb3c3bc: ldp             fp, lr, [SP], #0x10
    // 0xb3c3c0: ret
    //     0xb3c3c0: ret             
    // 0xb3c3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c3c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c3c8: b               #0xb3c390
  }
  get _ remainingDay(/* No info */) {
    // ** addr: 0xb3c3cc, size: 0x7c
    // 0xb3c3cc: EnterFrame
    //     0xb3c3cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c3d0: mov             fp, SP
    // 0xb3c3d4: AllocStack(0x10)
    //     0xb3c3d4: sub             SP, SP, #0x10
    // 0xb3c3d8: CheckStackOverflow
    //     0xb3c3d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c3dc: cmp             SP, x16
    //     0xb3c3e0: b.ls            #0xb3c440
    // 0xb3c3e4: LoadField: r0 = r1->field_b
    //     0xb3c3e4: ldur            w0, [x1, #0xb]
    // 0xb3c3e8: DecompressPointer r0
    //     0xb3c3e8: add             x0, x0, HEAP, lsl #32
    // 0xb3c3ec: stur            x0, [fp, #-8]
    // 0xb3c3f0: r0 = DateTime()
    //     0xb3c3f0: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb3c3f4: mov             x1, x0
    // 0xb3c3f8: r0 = false
    //     0xb3c3f8: add             x0, NULL, #0x30  ; false
    // 0xb3c3fc: stur            x1, [fp, #-0x10]
    // 0xb3c400: StoreField: r1->field_13 = r0
    //     0xb3c400: stur            w0, [x1, #0x13]
    // 0xb3c404: r0 = _getCurrentMicros()
    //     0xb3c404: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb3c408: r1 = LoadInt32Instr(r0)
    //     0xb3c408: sbfx            x1, x0, #1, #0x1f
    //     0xb3c40c: tbz             w0, #0, #0xb3c414
    //     0xb3c410: ldur            x1, [x0, #7]
    // 0xb3c414: ldur            x2, [fp, #-0x10]
    // 0xb3c418: StoreField: r2->field_7 = r1
    //     0xb3c418: stur            x1, [x2, #7]
    // 0xb3c41c: ldur            x1, [fp, #-8]
    // 0xb3c420: r0 = difference()
    //     0xb3c420: bl              #0xeb3728  ; [package:timezone/src/date_time.dart] TZDateTime::difference
    // 0xb3c424: LoadField: r1 = r0->field_7
    //     0xb3c424: ldur            x1, [x0, #7]
    // 0xb3c428: r2 = 86400000000
    //     0xb3c428: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xb3c42c: ldr             x2, [x2, #0x268]
    // 0xb3c430: sdiv            x0, x1, x2
    // 0xb3c434: LeaveFrame
    //     0xb3c434: mov             SP, fp
    //     0xb3c438: ldp             fp, lr, [SP], #0x10
    // 0xb3c43c: ret
    //     0xb3c43c: ret             
    // 0xb3c440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c444: b               #0xb3c3e4
  }
  get _ isEnded(/* No info */) {
    // ** addr: 0xb3c448, size: 0x6c
    // 0xb3c448: EnterFrame
    //     0xb3c448: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c44c: mov             fp, SP
    // 0xb3c450: AllocStack(0x10)
    //     0xb3c450: sub             SP, SP, #0x10
    // 0xb3c454: CheckStackOverflow
    //     0xb3c454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c458: cmp             SP, x16
    //     0xb3c45c: b.ls            #0xb3c4ac
    // 0xb3c460: LoadField: r0 = r1->field_b
    //     0xb3c460: ldur            w0, [x1, #0xb]
    // 0xb3c464: DecompressPointer r0
    //     0xb3c464: add             x0, x0, HEAP, lsl #32
    // 0xb3c468: stur            x0, [fp, #-8]
    // 0xb3c46c: r0 = DateTime()
    //     0xb3c46c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb3c470: mov             x1, x0
    // 0xb3c474: r0 = false
    //     0xb3c474: add             x0, NULL, #0x30  ; false
    // 0xb3c478: stur            x1, [fp, #-0x10]
    // 0xb3c47c: StoreField: r1->field_13 = r0
    //     0xb3c47c: stur            w0, [x1, #0x13]
    // 0xb3c480: r0 = _getCurrentMicros()
    //     0xb3c480: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb3c484: r1 = LoadInt32Instr(r0)
    //     0xb3c484: sbfx            x1, x0, #1, #0x1f
    //     0xb3c488: tbz             w0, #0, #0xb3c490
    //     0xb3c48c: ldur            x1, [x0, #7]
    // 0xb3c490: ldur            x2, [fp, #-0x10]
    // 0xb3c494: StoreField: r2->field_7 = r1
    //     0xb3c494: stur            x1, [x2, #7]
    // 0xb3c498: ldur            x1, [fp, #-8]
    // 0xb3c49c: r0 = isBefore()
    //     0xb3c49c: bl              #0xeb52d8  ; [package:timezone/src/date_time.dart] TZDateTime::isBefore
    // 0xb3c4a0: LeaveFrame
    //     0xb3c4a0: mov             SP, fp
    //     0xb3c4a4: ldp             fp, lr, [SP], #0x10
    // 0xb3c4a8: ret
    //     0xb3c4a8: ret             
    // 0xb3c4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c4ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c4b0: b               #0xb3c460
  }
}
