// lib: , url: package:nuonline/app/data/models/qurban_report.dart

// class id: 1050045, size: 0x8
class :: {

  static _ _$QurbanReportToJson(/* No info */) {
    // ** addr: 0x7d9ae4, size: 0xb0
    // 0x7d9ae4: EnterFrame
    //     0x7d9ae4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9ae8: mov             fp, SP
    // 0x7d9aec: AllocStack(0x18)
    //     0x7d9aec: sub             SP, SP, #0x18
    // 0x7d9af0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7d9af0: mov             x0, x1
    //     0x7d9af4: stur            x1, [fp, #-8]
    // 0x7d9af8: CheckStackOverflow
    //     0x7d9af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d9afc: cmp             SP, x16
    //     0x7d9b00: b.ls            #0x7d9b8c
    // 0x7d9b04: r1 = Null
    //     0x7d9b04: mov             x1, NULL
    // 0x7d9b08: r2 = 16
    //     0x7d9b08: movz            x2, #0x10
    // 0x7d9b0c: r0 = AllocateArray()
    //     0x7d9b0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7d9b10: r16 = "year"
    //     0x7d9b10: add             x16, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x7d9b14: ldr             x16, [x16, #0x310]
    // 0x7d9b18: StoreField: r0->field_f = r16
    //     0x7d9b18: stur            w16, [x0, #0xf]
    // 0x7d9b1c: ldur            x1, [fp, #-8]
    // 0x7d9b20: LoadField: r2 = r1->field_7
    //     0x7d9b20: ldur            w2, [x1, #7]
    // 0x7d9b24: DecompressPointer r2
    //     0x7d9b24: add             x2, x2, HEAP, lsl #32
    // 0x7d9b28: StoreField: r0->field_13 = r2
    //     0x7d9b28: stur            w2, [x0, #0x13]
    // 0x7d9b2c: r16 = "hijri_year"
    //     0x7d9b2c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d2b8] "hijri_year"
    //     0x7d9b30: ldr             x16, [x16, #0x2b8]
    // 0x7d9b34: ArrayStore: r0[0] = r16  ; List_4
    //     0x7d9b34: stur            w16, [x0, #0x17]
    // 0x7d9b38: LoadField: r2 = r1->field_b
    //     0x7d9b38: ldur            w2, [x1, #0xb]
    // 0x7d9b3c: DecompressPointer r2
    //     0x7d9b3c: add             x2, x2, HEAP, lsl #32
    // 0x7d9b40: StoreField: r0->field_1b = r2
    //     0x7d9b40: stur            w2, [x0, #0x1b]
    // 0x7d9b44: r16 = "transactions"
    //     0x7d9b44: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d2d0] "transactions"
    //     0x7d9b48: ldr             x16, [x16, #0x2d0]
    // 0x7d9b4c: StoreField: r0->field_1f = r16
    //     0x7d9b4c: stur            w16, [x0, #0x1f]
    // 0x7d9b50: LoadField: r2 = r1->field_f
    //     0x7d9b50: ldur            w2, [x1, #0xf]
    // 0x7d9b54: DecompressPointer r2
    //     0x7d9b54: add             x2, x2, HEAP, lsl #32
    // 0x7d9b58: StoreField: r0->field_23 = r2
    //     0x7d9b58: stur            w2, [x0, #0x23]
    // 0x7d9b5c: r16 = "count"
    //     0x7d9b5c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc640] "count"
    //     0x7d9b60: ldr             x16, [x16, #0x640]
    // 0x7d9b64: StoreField: r0->field_27 = r16
    //     0x7d9b64: stur            w16, [x0, #0x27]
    // 0x7d9b68: LoadField: r2 = r1->field_13
    //     0x7d9b68: ldur            w2, [x1, #0x13]
    // 0x7d9b6c: DecompressPointer r2
    //     0x7d9b6c: add             x2, x2, HEAP, lsl #32
    // 0x7d9b70: StoreField: r0->field_2b = r2
    //     0x7d9b70: stur            w2, [x0, #0x2b]
    // 0x7d9b74: r16 = <String, dynamic>
    //     0x7d9b74: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7d9b78: stp             x0, x16, [SP]
    // 0x7d9b7c: r0 = Map._fromLiteral()
    //     0x7d9b7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7d9b80: LeaveFrame
    //     0x7d9b80: mov             SP, fp
    //     0x7d9b84: ldp             fp, lr, [SP], #0x10
    // 0x7d9b88: ret
    //     0x7d9b88: ret             
    // 0x7d9b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d9b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d9b90: b               #0x7d9b04
  }
  static _ _$QurbanReportFromJson(/* No info */) {
    // ** addr: 0x7ea3e4, size: 0x32c
    // 0x7ea3e4: EnterFrame
    //     0x7ea3e4: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea3e8: mov             fp, SP
    // 0x7ea3ec: AllocStack(0x38)
    //     0x7ea3ec: sub             SP, SP, #0x38
    // 0x7ea3f0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7ea3f0: mov             x3, x1
    //     0x7ea3f4: stur            x1, [fp, #-8]
    // 0x7ea3f8: CheckStackOverflow
    //     0x7ea3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea3fc: cmp             SP, x16
    //     0x7ea400: b.ls            #0x7ea708
    // 0x7ea404: r0 = LoadClassIdInstr(r3)
    //     0x7ea404: ldur            x0, [x3, #-1]
    //     0x7ea408: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea40c: mov             x1, x3
    // 0x7ea410: r2 = "year"
    //     0x7ea410: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x7ea414: ldr             x2, [x2, #0x310]
    // 0x7ea418: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ea418: sub             lr, x0, #0x114
    //     0x7ea41c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea420: blr             lr
    // 0x7ea424: mov             x3, x0
    // 0x7ea428: r2 = Null
    //     0x7ea428: mov             x2, NULL
    // 0x7ea42c: r1 = Null
    //     0x7ea42c: mov             x1, NULL
    // 0x7ea430: stur            x3, [fp, #-0x10]
    // 0x7ea434: branchIfSmi(r0, 0x7ea460)
    //     0x7ea434: tbz             w0, #0, #0x7ea460
    // 0x7ea438: r4 = LoadClassIdInstr(r0)
    //     0x7ea438: ldur            x4, [x0, #-1]
    //     0x7ea43c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea440: sub             x4, x4, #0x3c
    // 0x7ea444: cmp             x4, #2
    // 0x7ea448: b.ls            #0x7ea460
    // 0x7ea44c: r8 = num?
    //     0x7ea44c: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7ea450: ldr             x8, [x8, #0x4f0]
    // 0x7ea454: r3 = Null
    //     0x7ea454: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d2a8] Null
    //     0x7ea458: ldr             x3, [x3, #0x2a8]
    // 0x7ea45c: r0 = DefaultNullableTypeTest()
    //     0x7ea45c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7ea460: ldur            x0, [fp, #-0x10]
    // 0x7ea464: cmp             w0, NULL
    // 0x7ea468: b.ne            #0x7ea474
    // 0x7ea46c: r4 = Null
    //     0x7ea46c: mov             x4, NULL
    // 0x7ea470: b               #0x7ea49c
    // 0x7ea474: r1 = 60
    //     0x7ea474: movz            x1, #0x3c
    // 0x7ea478: branchIfSmi(r0, 0x7ea484)
    //     0x7ea478: tbz             w0, #0, #0x7ea484
    // 0x7ea47c: r1 = LoadClassIdInstr(r0)
    //     0x7ea47c: ldur            x1, [x0, #-1]
    //     0x7ea480: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea484: str             x0, [SP]
    // 0x7ea488: mov             x0, x1
    // 0x7ea48c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ea48c: sub             lr, x0, #1, lsl #12
    //     0x7ea490: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea494: blr             lr
    // 0x7ea498: mov             x4, x0
    // 0x7ea49c: ldur            x3, [fp, #-8]
    // 0x7ea4a0: stur            x4, [fp, #-0x10]
    // 0x7ea4a4: r0 = LoadClassIdInstr(r3)
    //     0x7ea4a4: ldur            x0, [x3, #-1]
    //     0x7ea4a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea4ac: mov             x1, x3
    // 0x7ea4b0: r2 = "hijri_year"
    //     0x7ea4b0: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d2b8] "hijri_year"
    //     0x7ea4b4: ldr             x2, [x2, #0x2b8]
    // 0x7ea4b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ea4b8: sub             lr, x0, #0x114
    //     0x7ea4bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea4c0: blr             lr
    // 0x7ea4c4: mov             x3, x0
    // 0x7ea4c8: r2 = Null
    //     0x7ea4c8: mov             x2, NULL
    // 0x7ea4cc: r1 = Null
    //     0x7ea4cc: mov             x1, NULL
    // 0x7ea4d0: stur            x3, [fp, #-0x18]
    // 0x7ea4d4: branchIfSmi(r0, 0x7ea500)
    //     0x7ea4d4: tbz             w0, #0, #0x7ea500
    // 0x7ea4d8: r4 = LoadClassIdInstr(r0)
    //     0x7ea4d8: ldur            x4, [x0, #-1]
    //     0x7ea4dc: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea4e0: sub             x4, x4, #0x3c
    // 0x7ea4e4: cmp             x4, #2
    // 0x7ea4e8: b.ls            #0x7ea500
    // 0x7ea4ec: r8 = num?
    //     0x7ea4ec: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7ea4f0: ldr             x8, [x8, #0x4f0]
    // 0x7ea4f4: r3 = Null
    //     0x7ea4f4: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d2c0] Null
    //     0x7ea4f8: ldr             x3, [x3, #0x2c0]
    // 0x7ea4fc: r0 = DefaultNullableTypeTest()
    //     0x7ea4fc: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7ea500: ldur            x0, [fp, #-0x18]
    // 0x7ea504: cmp             w0, NULL
    // 0x7ea508: b.ne            #0x7ea514
    // 0x7ea50c: r4 = Null
    //     0x7ea50c: mov             x4, NULL
    // 0x7ea510: b               #0x7ea53c
    // 0x7ea514: r1 = 60
    //     0x7ea514: movz            x1, #0x3c
    // 0x7ea518: branchIfSmi(r0, 0x7ea524)
    //     0x7ea518: tbz             w0, #0, #0x7ea524
    // 0x7ea51c: r1 = LoadClassIdInstr(r0)
    //     0x7ea51c: ldur            x1, [x0, #-1]
    //     0x7ea520: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea524: str             x0, [SP]
    // 0x7ea528: mov             x0, x1
    // 0x7ea52c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ea52c: sub             lr, x0, #1, lsl #12
    //     0x7ea530: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea534: blr             lr
    // 0x7ea538: mov             x4, x0
    // 0x7ea53c: ldur            x3, [fp, #-8]
    // 0x7ea540: stur            x4, [fp, #-0x18]
    // 0x7ea544: r0 = LoadClassIdInstr(r3)
    //     0x7ea544: ldur            x0, [x3, #-1]
    //     0x7ea548: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea54c: mov             x1, x3
    // 0x7ea550: r2 = "transactions"
    //     0x7ea550: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d2d0] "transactions"
    //     0x7ea554: ldr             x2, [x2, #0x2d0]
    // 0x7ea558: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ea558: sub             lr, x0, #0x114
    //     0x7ea55c: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea560: blr             lr
    // 0x7ea564: mov             x3, x0
    // 0x7ea568: r2 = Null
    //     0x7ea568: mov             x2, NULL
    // 0x7ea56c: r1 = Null
    //     0x7ea56c: mov             x1, NULL
    // 0x7ea570: stur            x3, [fp, #-0x20]
    // 0x7ea574: r4 = 60
    //     0x7ea574: movz            x4, #0x3c
    // 0x7ea578: branchIfSmi(r0, 0x7ea584)
    //     0x7ea578: tbz             w0, #0, #0x7ea584
    // 0x7ea57c: r4 = LoadClassIdInstr(r0)
    //     0x7ea57c: ldur            x4, [x0, #-1]
    //     0x7ea580: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea584: sub             x4, x4, #0x5a
    // 0x7ea588: cmp             x4, #2
    // 0x7ea58c: b.ls            #0x7ea5a4
    // 0x7ea590: r8 = List?
    //     0x7ea590: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7ea594: ldr             x8, [x8, #0x140]
    // 0x7ea598: r3 = Null
    //     0x7ea598: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d2d8] Null
    //     0x7ea59c: ldr             x3, [x3, #0x2d8]
    // 0x7ea5a0: r0 = List?()
    //     0x7ea5a0: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7ea5a4: ldur            x0, [fp, #-0x20]
    // 0x7ea5a8: cmp             w0, NULL
    // 0x7ea5ac: b.ne            #0x7ea5b8
    // 0x7ea5b0: r3 = Null
    //     0x7ea5b0: mov             x3, NULL
    // 0x7ea5b4: b               #0x7ea62c
    // 0x7ea5b8: r1 = Function '<anonymous closure>': static.
    //     0x7ea5b8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d2e8] AnonymousClosure: static (0x7ea71c), in [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanReportFromJson (0x7ea3e4)
    //     0x7ea5bc: ldr             x1, [x1, #0x2e8]
    // 0x7ea5c0: r2 = Null
    //     0x7ea5c0: mov             x2, NULL
    // 0x7ea5c4: r0 = AllocateClosure()
    //     0x7ea5c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ea5c8: mov             x1, x0
    // 0x7ea5cc: ldur            x0, [fp, #-0x20]
    // 0x7ea5d0: r2 = LoadClassIdInstr(r0)
    //     0x7ea5d0: ldur            x2, [x0, #-1]
    //     0x7ea5d4: ubfx            x2, x2, #0xc, #0x14
    // 0x7ea5d8: r16 = <QurbanTransaction>
    //     0x7ea5d8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b368] TypeArguments: <QurbanTransaction>
    //     0x7ea5dc: ldr             x16, [x16, #0x368]
    // 0x7ea5e0: stp             x0, x16, [SP, #8]
    // 0x7ea5e4: str             x1, [SP]
    // 0x7ea5e8: mov             x0, x2
    // 0x7ea5ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ea5ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ea5f0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7ea5f0: movz            x17, #0xf28c
    //     0x7ea5f4: add             lr, x0, x17
    //     0x7ea5f8: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea5fc: blr             lr
    // 0x7ea600: r1 = LoadClassIdInstr(r0)
    //     0x7ea600: ldur            x1, [x0, #-1]
    //     0x7ea604: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea608: mov             x16, x0
    // 0x7ea60c: mov             x0, x1
    // 0x7ea610: mov             x1, x16
    // 0x7ea614: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ea614: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ea618: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7ea618: movz            x17, #0xd889
    //     0x7ea61c: add             lr, x0, x17
    //     0x7ea620: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea624: blr             lr
    // 0x7ea628: mov             x3, x0
    // 0x7ea62c: ldur            x1, [fp, #-8]
    // 0x7ea630: stur            x3, [fp, #-0x20]
    // 0x7ea634: r0 = LoadClassIdInstr(r1)
    //     0x7ea634: ldur            x0, [x1, #-1]
    //     0x7ea638: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea63c: r2 = "count"
    //     0x7ea63c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc640] "count"
    //     0x7ea640: ldr             x2, [x2, #0x640]
    // 0x7ea644: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ea644: sub             lr, x0, #0x114
    //     0x7ea648: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea64c: blr             lr
    // 0x7ea650: mov             x3, x0
    // 0x7ea654: r2 = Null
    //     0x7ea654: mov             x2, NULL
    // 0x7ea658: r1 = Null
    //     0x7ea658: mov             x1, NULL
    // 0x7ea65c: stur            x3, [fp, #-8]
    // 0x7ea660: branchIfSmi(r0, 0x7ea68c)
    //     0x7ea660: tbz             w0, #0, #0x7ea68c
    // 0x7ea664: r4 = LoadClassIdInstr(r0)
    //     0x7ea664: ldur            x4, [x0, #-1]
    //     0x7ea668: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea66c: sub             x4, x4, #0x3c
    // 0x7ea670: cmp             x4, #2
    // 0x7ea674: b.ls            #0x7ea68c
    // 0x7ea678: r8 = num?
    //     0x7ea678: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7ea67c: ldr             x8, [x8, #0x4f0]
    // 0x7ea680: r3 = Null
    //     0x7ea680: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d2f0] Null
    //     0x7ea684: ldr             x3, [x3, #0x2f0]
    // 0x7ea688: r0 = DefaultNullableTypeTest()
    //     0x7ea688: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7ea68c: ldur            x0, [fp, #-8]
    // 0x7ea690: cmp             w0, NULL
    // 0x7ea694: b.ne            #0x7ea6a0
    // 0x7ea698: r3 = Null
    //     0x7ea698: mov             x3, NULL
    // 0x7ea69c: b               #0x7ea6c8
    // 0x7ea6a0: r1 = 60
    //     0x7ea6a0: movz            x1, #0x3c
    // 0x7ea6a4: branchIfSmi(r0, 0x7ea6b0)
    //     0x7ea6a4: tbz             w0, #0, #0x7ea6b0
    // 0x7ea6a8: r1 = LoadClassIdInstr(r0)
    //     0x7ea6a8: ldur            x1, [x0, #-1]
    //     0x7ea6ac: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea6b0: str             x0, [SP]
    // 0x7ea6b4: mov             x0, x1
    // 0x7ea6b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7ea6b8: sub             lr, x0, #1, lsl #12
    //     0x7ea6bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea6c0: blr             lr
    // 0x7ea6c4: mov             x3, x0
    // 0x7ea6c8: ldur            x2, [fp, #-0x10]
    // 0x7ea6cc: ldur            x1, [fp, #-0x18]
    // 0x7ea6d0: ldur            x0, [fp, #-0x20]
    // 0x7ea6d4: stur            x3, [fp, #-8]
    // 0x7ea6d8: r0 = QurbanReport()
    //     0x7ea6d8: bl              #0x7ea710  ; AllocateQurbanReportStub -> QurbanReport (size=0x18)
    // 0x7ea6dc: ldur            x1, [fp, #-0x10]
    // 0x7ea6e0: StoreField: r0->field_7 = r1
    //     0x7ea6e0: stur            w1, [x0, #7]
    // 0x7ea6e4: ldur            x1, [fp, #-0x18]
    // 0x7ea6e8: StoreField: r0->field_b = r1
    //     0x7ea6e8: stur            w1, [x0, #0xb]
    // 0x7ea6ec: ldur            x1, [fp, #-0x20]
    // 0x7ea6f0: StoreField: r0->field_f = r1
    //     0x7ea6f0: stur            w1, [x0, #0xf]
    // 0x7ea6f4: ldur            x1, [fp, #-8]
    // 0x7ea6f8: StoreField: r0->field_13 = r1
    //     0x7ea6f8: stur            w1, [x0, #0x13]
    // 0x7ea6fc: LeaveFrame
    //     0x7ea6fc: mov             SP, fp
    //     0x7ea700: ldp             fp, lr, [SP], #0x10
    // 0x7ea704: ret
    //     0x7ea704: ret             
    // 0x7ea708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea708: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea70c: b               #0x7ea404
  }
  [closure] static QurbanTransaction <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7ea71c, size: 0x4c
    // 0x7ea71c: EnterFrame
    //     0x7ea71c: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea720: mov             fp, SP
    // 0x7ea724: CheckStackOverflow
    //     0x7ea724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea728: cmp             SP, x16
    //     0x7ea72c: b.ls            #0x7ea760
    // 0x7ea730: ldr             x0, [fp, #0x10]
    // 0x7ea734: r2 = Null
    //     0x7ea734: mov             x2, NULL
    // 0x7ea738: r1 = Null
    //     0x7ea738: mov             x1, NULL
    // 0x7ea73c: r8 = Map<String, dynamic>
    //     0x7ea73c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7ea740: r3 = Null
    //     0x7ea740: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d300] Null
    //     0x7ea744: ldr             x3, [x3, #0x300]
    // 0x7ea748: r0 = Map<String, dynamic>()
    //     0x7ea748: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7ea74c: ldr             x1, [fp, #0x10]
    // 0x7ea750: r0 = _$QurbanTransactionFromJson()
    //     0x7ea750: bl              #0x7eaa14  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanTransactionFromJson
    // 0x7ea754: LeaveFrame
    //     0x7ea754: mov             SP, fp
    //     0x7ea758: ldp             fp, lr, [SP], #0x10
    // 0x7ea75c: ret
    //     0x7ea75c: ret             
    // 0x7ea760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea760: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea764: b               #0x7ea730
  }
  static _ _$QurbanTransactionToJson(/* No info */) {
    // ** addr: 0x7ea7b0, size: 0x244
    // 0x7ea7b0: EnterFrame
    //     0x7ea7b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea7b4: mov             fp, SP
    // 0x7ea7b8: AllocStack(0x20)
    //     0x7ea7b8: sub             SP, SP, #0x20
    // 0x7ea7bc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7ea7bc: mov             x0, x1
    //     0x7ea7c0: stur            x1, [fp, #-8]
    // 0x7ea7c4: CheckStackOverflow
    //     0x7ea7c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea7c8: cmp             SP, x16
    //     0x7ea7cc: b.ls            #0x7ea9ec
    // 0x7ea7d0: r1 = Null
    //     0x7ea7d0: mov             x1, NULL
    // 0x7ea7d4: r2 = 36
    //     0x7ea7d4: movz            x2, #0x24
    // 0x7ea7d8: r0 = AllocateArray()
    //     0x7ea7d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ea7dc: stur            x0, [fp, #-0x10]
    // 0x7ea7e0: r16 = "id"
    //     0x7ea7e0: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7ea7e4: ldr             x16, [x16, #0x740]
    // 0x7ea7e8: StoreField: r0->field_f = r16
    //     0x7ea7e8: stur            w16, [x0, #0xf]
    // 0x7ea7ec: ldur            x2, [fp, #-8]
    // 0x7ea7f0: LoadField: r1 = r2->field_7
    //     0x7ea7f0: ldur            w1, [x2, #7]
    // 0x7ea7f4: DecompressPointer r1
    //     0x7ea7f4: add             x1, x1, HEAP, lsl #32
    // 0x7ea7f8: StoreField: r0->field_13 = r1
    //     0x7ea7f8: stur            w1, [x0, #0x13]
    // 0x7ea7fc: r16 = "trx_id"
    //     0x7ea7fc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b0d8] "trx_id"
    //     0x7ea800: ldr             x16, [x16, #0xd8]
    // 0x7ea804: ArrayStore: r0[0] = r16  ; List_4
    //     0x7ea804: stur            w16, [x0, #0x17]
    // 0x7ea808: LoadField: r1 = r2->field_b
    //     0x7ea808: ldur            w1, [x2, #0xb]
    // 0x7ea80c: DecompressPointer r1
    //     0x7ea80c: add             x1, x1, HEAP, lsl #32
    // 0x7ea810: StoreField: r0->field_1b = r1
    //     0x7ea810: stur            w1, [x0, #0x1b]
    // 0x7ea814: r16 = "amount"
    //     0x7ea814: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x7ea818: ldr             x16, [x16, #0xe0]
    // 0x7ea81c: StoreField: r0->field_1f = r16
    //     0x7ea81c: stur            w16, [x0, #0x1f]
    // 0x7ea820: LoadField: r1 = r2->field_f
    //     0x7ea820: ldur            w1, [x2, #0xf]
    // 0x7ea824: DecompressPointer r1
    //     0x7ea824: add             x1, x1, HEAP, lsl #32
    // 0x7ea828: StoreField: r0->field_23 = r1
    //     0x7ea828: stur            w1, [x0, #0x23]
    // 0x7ea82c: r16 = "updated_at"
    //     0x7ea82c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x7ea830: ldr             x16, [x16, #0xe88]
    // 0x7ea834: StoreField: r0->field_27 = r16
    //     0x7ea834: stur            w16, [x0, #0x27]
    // 0x7ea838: LoadField: r1 = r2->field_13
    //     0x7ea838: ldur            w1, [x2, #0x13]
    // 0x7ea83c: DecompressPointer r1
    //     0x7ea83c: add             x1, x1, HEAP, lsl #32
    // 0x7ea840: cmp             w1, NULL
    // 0x7ea844: b.ne            #0x7ea858
    // 0x7ea848: mov             x3, x2
    // 0x7ea84c: mov             x2, x0
    // 0x7ea850: r0 = Null
    //     0x7ea850: mov             x0, NULL
    // 0x7ea854: b               #0x7ea864
    // 0x7ea858: r0 = toIso8601String()
    //     0x7ea858: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0x7ea85c: ldur            x3, [fp, #-8]
    // 0x7ea860: ldur            x2, [fp, #-0x10]
    // 0x7ea864: mov             x1, x2
    // 0x7ea868: ArrayStore: r1[7] = r0  ; List_4
    //     0x7ea868: add             x25, x1, #0x2b
    //     0x7ea86c: str             w0, [x25]
    //     0x7ea870: tbz             w0, #0, #0x7ea88c
    //     0x7ea874: ldurb           w16, [x1, #-1]
    //     0x7ea878: ldurb           w17, [x0, #-1]
    //     0x7ea87c: and             x16, x17, x16, lsr #2
    //     0x7ea880: tst             x16, HEAP, lsr #32
    //     0x7ea884: b.eq            #0x7ea88c
    //     0x7ea888: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea88c: r16 = "created_at"
    //     0x7ea88c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b1d0] "created_at"
    //     0x7ea890: ldr             x16, [x16, #0x1d0]
    // 0x7ea894: StoreField: r2->field_2f = r16
    //     0x7ea894: stur            w16, [x2, #0x2f]
    // 0x7ea898: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x7ea898: ldur            w1, [x3, #0x17]
    // 0x7ea89c: DecompressPointer r1
    //     0x7ea89c: add             x1, x1, HEAP, lsl #32
    // 0x7ea8a0: cmp             w1, NULL
    // 0x7ea8a4: b.ne            #0x7ea8b0
    // 0x7ea8a8: r0 = Null
    //     0x7ea8a8: mov             x0, NULL
    // 0x7ea8ac: b               #0x7ea8bc
    // 0x7ea8b0: r0 = toIso8601String()
    //     0x7ea8b0: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0x7ea8b4: ldur            x3, [fp, #-8]
    // 0x7ea8b8: ldur            x2, [fp, #-0x10]
    // 0x7ea8bc: mov             x1, x2
    // 0x7ea8c0: ArrayStore: r1[9] = r0  ; List_4
    //     0x7ea8c0: add             x25, x1, #0x33
    //     0x7ea8c4: str             w0, [x25]
    //     0x7ea8c8: tbz             w0, #0, #0x7ea8e4
    //     0x7ea8cc: ldurb           w16, [x1, #-1]
    //     0x7ea8d0: ldurb           w17, [x0, #-1]
    //     0x7ea8d4: and             x16, x17, x16, lsr #2
    //     0x7ea8d8: tst             x16, HEAP, lsr #32
    //     0x7ea8dc: b.eq            #0x7ea8e4
    //     0x7ea8e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea8e4: r16 = "description"
    //     0x7ea8e4: add             x16, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x7ea8e8: ldr             x16, [x16, #0xd28]
    // 0x7ea8ec: StoreField: r2->field_37 = r16
    //     0x7ea8ec: stur            w16, [x2, #0x37]
    // 0x7ea8f0: LoadField: r0 = r3->field_1b
    //     0x7ea8f0: ldur            w0, [x3, #0x1b]
    // 0x7ea8f4: DecompressPointer r0
    //     0x7ea8f4: add             x0, x0, HEAP, lsl #32
    // 0x7ea8f8: mov             x1, x2
    // 0x7ea8fc: ArrayStore: r1[11] = r0  ; List_4
    //     0x7ea8fc: add             x25, x1, #0x3b
    //     0x7ea900: str             w0, [x25]
    //     0x7ea904: tbz             w0, #0, #0x7ea920
    //     0x7ea908: ldurb           w16, [x1, #-1]
    //     0x7ea90c: ldurb           w17, [x0, #-1]
    //     0x7ea910: and             x16, x17, x16, lsr #2
    //     0x7ea914: tst             x16, HEAP, lsr #32
    //     0x7ea918: b.eq            #0x7ea920
    //     0x7ea91c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea920: r16 = "year"
    //     0x7ea920: add             x16, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x7ea924: ldr             x16, [x16, #0x310]
    // 0x7ea928: StoreField: r2->field_3f = r16
    //     0x7ea928: stur            w16, [x2, #0x3f]
    // 0x7ea92c: LoadField: r0 = r3->field_1f
    //     0x7ea92c: ldur            w0, [x3, #0x1f]
    // 0x7ea930: DecompressPointer r0
    //     0x7ea930: add             x0, x0, HEAP, lsl #32
    // 0x7ea934: mov             x1, x2
    // 0x7ea938: ArrayStore: r1[13] = r0  ; List_4
    //     0x7ea938: add             x25, x1, #0x43
    //     0x7ea93c: str             w0, [x25]
    //     0x7ea940: tbz             w0, #0, #0x7ea95c
    //     0x7ea944: ldurb           w16, [x1, #-1]
    //     0x7ea948: ldurb           w17, [x0, #-1]
    //     0x7ea94c: and             x16, x17, x16, lsr #2
    //     0x7ea950: tst             x16, HEAP, lsr #32
    //     0x7ea954: b.eq            #0x7ea95c
    //     0x7ea958: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea95c: r16 = "count"
    //     0x7ea95c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc640] "count"
    //     0x7ea960: ldr             x16, [x16, #0x640]
    // 0x7ea964: StoreField: r2->field_47 = r16
    //     0x7ea964: stur            w16, [x2, #0x47]
    // 0x7ea968: LoadField: r0 = r3->field_23
    //     0x7ea968: ldur            w0, [x3, #0x23]
    // 0x7ea96c: DecompressPointer r0
    //     0x7ea96c: add             x0, x0, HEAP, lsl #32
    // 0x7ea970: mov             x1, x2
    // 0x7ea974: ArrayStore: r1[15] = r0  ; List_4
    //     0x7ea974: add             x25, x1, #0x4b
    //     0x7ea978: str             w0, [x25]
    //     0x7ea97c: tbz             w0, #0, #0x7ea998
    //     0x7ea980: ldurb           w16, [x1, #-1]
    //     0x7ea984: ldurb           w17, [x0, #-1]
    //     0x7ea988: and             x16, x17, x16, lsr #2
    //     0x7ea98c: tst             x16, HEAP, lsr #32
    //     0x7ea990: b.eq            #0x7ea998
    //     0x7ea994: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea998: r16 = "document"
    //     0x7ea998: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d390] "document"
    //     0x7ea99c: ldr             x16, [x16, #0x390]
    // 0x7ea9a0: StoreField: r2->field_4f = r16
    //     0x7ea9a0: stur            w16, [x2, #0x4f]
    // 0x7ea9a4: LoadField: r0 = r3->field_27
    //     0x7ea9a4: ldur            w0, [x3, #0x27]
    // 0x7ea9a8: DecompressPointer r0
    //     0x7ea9a8: add             x0, x0, HEAP, lsl #32
    // 0x7ea9ac: mov             x1, x2
    // 0x7ea9b0: ArrayStore: r1[17] = r0  ; List_4
    //     0x7ea9b0: add             x25, x1, #0x53
    //     0x7ea9b4: str             w0, [x25]
    //     0x7ea9b8: tbz             w0, #0, #0x7ea9d4
    //     0x7ea9bc: ldurb           w16, [x1, #-1]
    //     0x7ea9c0: ldurb           w17, [x0, #-1]
    //     0x7ea9c4: and             x16, x17, x16, lsr #2
    //     0x7ea9c8: tst             x16, HEAP, lsr #32
    //     0x7ea9cc: b.eq            #0x7ea9d4
    //     0x7ea9d0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ea9d4: r16 = <String, dynamic>
    //     0x7ea9d4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7ea9d8: stp             x2, x16, [SP]
    // 0x7ea9dc: r0 = Map._fromLiteral()
    //     0x7ea9dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7ea9e0: LeaveFrame
    //     0x7ea9e0: mov             SP, fp
    //     0x7ea9e4: ldp             fp, lr, [SP], #0x10
    // 0x7ea9e8: ret
    //     0x7ea9e8: ret             
    // 0x7ea9ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea9ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea9f0: b               #0x7ea7d0
  }
  static _ _$QurbanTransactionFromJson(/* No info */) {
    // ** addr: 0x7eaa14, size: 0x5a8
    // 0x7eaa14: EnterFrame
    //     0x7eaa14: stp             fp, lr, [SP, #-0x10]!
    //     0x7eaa18: mov             fp, SP
    // 0x7eaa1c: AllocStack(0x50)
    //     0x7eaa1c: sub             SP, SP, #0x50
    // 0x7eaa20: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7eaa20: mov             x3, x1
    //     0x7eaa24: stur            x1, [fp, #-8]
    // 0x7eaa28: CheckStackOverflow
    //     0x7eaa28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eaa2c: cmp             SP, x16
    //     0x7eaa30: b.ls            #0x7eafb4
    // 0x7eaa34: r0 = LoadClassIdInstr(r3)
    //     0x7eaa34: ldur            x0, [x3, #-1]
    //     0x7eaa38: ubfx            x0, x0, #0xc, #0x14
    // 0x7eaa3c: mov             x1, x3
    // 0x7eaa40: r2 = "id"
    //     0x7eaa40: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7eaa44: ldr             x2, [x2, #0x740]
    // 0x7eaa48: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eaa48: sub             lr, x0, #0x114
    //     0x7eaa4c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaa50: blr             lr
    // 0x7eaa54: mov             x3, x0
    // 0x7eaa58: r2 = Null
    //     0x7eaa58: mov             x2, NULL
    // 0x7eaa5c: r1 = Null
    //     0x7eaa5c: mov             x1, NULL
    // 0x7eaa60: stur            x3, [fp, #-0x10]
    // 0x7eaa64: branchIfSmi(r0, 0x7eaa90)
    //     0x7eaa64: tbz             w0, #0, #0x7eaa90
    // 0x7eaa68: r4 = LoadClassIdInstr(r0)
    //     0x7eaa68: ldur            x4, [x0, #-1]
    //     0x7eaa6c: ubfx            x4, x4, #0xc, #0x14
    // 0x7eaa70: sub             x4, x4, #0x3c
    // 0x7eaa74: cmp             x4, #2
    // 0x7eaa78: b.ls            #0x7eaa90
    // 0x7eaa7c: r8 = num?
    //     0x7eaa7c: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7eaa80: ldr             x8, [x8, #0x4f0]
    // 0x7eaa84: r3 = Null
    //     0x7eaa84: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d310] Null
    //     0x7eaa88: ldr             x3, [x3, #0x310]
    // 0x7eaa8c: r0 = DefaultNullableTypeTest()
    //     0x7eaa8c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7eaa90: ldur            x0, [fp, #-0x10]
    // 0x7eaa94: cmp             w0, NULL
    // 0x7eaa98: b.ne            #0x7eaaa4
    // 0x7eaa9c: r4 = Null
    //     0x7eaa9c: mov             x4, NULL
    // 0x7eaaa0: b               #0x7eaacc
    // 0x7eaaa4: r1 = 60
    //     0x7eaaa4: movz            x1, #0x3c
    // 0x7eaaa8: branchIfSmi(r0, 0x7eaab4)
    //     0x7eaaa8: tbz             w0, #0, #0x7eaab4
    // 0x7eaaac: r1 = LoadClassIdInstr(r0)
    //     0x7eaaac: ldur            x1, [x0, #-1]
    //     0x7eaab0: ubfx            x1, x1, #0xc, #0x14
    // 0x7eaab4: str             x0, [SP]
    // 0x7eaab8: mov             x0, x1
    // 0x7eaabc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7eaabc: sub             lr, x0, #1, lsl #12
    //     0x7eaac0: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaac4: blr             lr
    // 0x7eaac8: mov             x4, x0
    // 0x7eaacc: ldur            x3, [fp, #-8]
    // 0x7eaad0: stur            x4, [fp, #-0x10]
    // 0x7eaad4: r0 = LoadClassIdInstr(r3)
    //     0x7eaad4: ldur            x0, [x3, #-1]
    //     0x7eaad8: ubfx            x0, x0, #0xc, #0x14
    // 0x7eaadc: mov             x1, x3
    // 0x7eaae0: r2 = "trx_id"
    //     0x7eaae0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b0d8] "trx_id"
    //     0x7eaae4: ldr             x2, [x2, #0xd8]
    // 0x7eaae8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eaae8: sub             lr, x0, #0x114
    //     0x7eaaec: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaaf0: blr             lr
    // 0x7eaaf4: mov             x3, x0
    // 0x7eaaf8: r2 = Null
    //     0x7eaaf8: mov             x2, NULL
    // 0x7eaafc: r1 = Null
    //     0x7eaafc: mov             x1, NULL
    // 0x7eab00: stur            x3, [fp, #-0x18]
    // 0x7eab04: r4 = 60
    //     0x7eab04: movz            x4, #0x3c
    // 0x7eab08: branchIfSmi(r0, 0x7eab14)
    //     0x7eab08: tbz             w0, #0, #0x7eab14
    // 0x7eab0c: r4 = LoadClassIdInstr(r0)
    //     0x7eab0c: ldur            x4, [x0, #-1]
    //     0x7eab10: ubfx            x4, x4, #0xc, #0x14
    // 0x7eab14: sub             x4, x4, #0x5e
    // 0x7eab18: cmp             x4, #1
    // 0x7eab1c: b.ls            #0x7eab30
    // 0x7eab20: r8 = String?
    //     0x7eab20: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7eab24: r3 = Null
    //     0x7eab24: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d320] Null
    //     0x7eab28: ldr             x3, [x3, #0x320]
    // 0x7eab2c: r0 = String?()
    //     0x7eab2c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7eab30: ldur            x3, [fp, #-8]
    // 0x7eab34: r0 = LoadClassIdInstr(r3)
    //     0x7eab34: ldur            x0, [x3, #-1]
    //     0x7eab38: ubfx            x0, x0, #0xc, #0x14
    // 0x7eab3c: mov             x1, x3
    // 0x7eab40: r2 = "amount"
    //     0x7eab40: add             x2, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x7eab44: ldr             x2, [x2, #0xe0]
    // 0x7eab48: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eab48: sub             lr, x0, #0x114
    //     0x7eab4c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eab50: blr             lr
    // 0x7eab54: mov             x3, x0
    // 0x7eab58: r2 = Null
    //     0x7eab58: mov             x2, NULL
    // 0x7eab5c: r1 = Null
    //     0x7eab5c: mov             x1, NULL
    // 0x7eab60: stur            x3, [fp, #-0x20]
    // 0x7eab64: branchIfSmi(r0, 0x7eab90)
    //     0x7eab64: tbz             w0, #0, #0x7eab90
    // 0x7eab68: r4 = LoadClassIdInstr(r0)
    //     0x7eab68: ldur            x4, [x0, #-1]
    //     0x7eab6c: ubfx            x4, x4, #0xc, #0x14
    // 0x7eab70: sub             x4, x4, #0x3c
    // 0x7eab74: cmp             x4, #2
    // 0x7eab78: b.ls            #0x7eab90
    // 0x7eab7c: r8 = num?
    //     0x7eab7c: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7eab80: ldr             x8, [x8, #0x4f0]
    // 0x7eab84: r3 = Null
    //     0x7eab84: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d330] Null
    //     0x7eab88: ldr             x3, [x3, #0x330]
    // 0x7eab8c: r0 = DefaultNullableTypeTest()
    //     0x7eab8c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7eab90: ldur            x0, [fp, #-0x20]
    // 0x7eab94: cmp             w0, NULL
    // 0x7eab98: b.ne            #0x7eaba4
    // 0x7eab9c: r4 = Null
    //     0x7eab9c: mov             x4, NULL
    // 0x7eaba0: b               #0x7eabcc
    // 0x7eaba4: r1 = 60
    //     0x7eaba4: movz            x1, #0x3c
    // 0x7eaba8: branchIfSmi(r0, 0x7eabb4)
    //     0x7eaba8: tbz             w0, #0, #0x7eabb4
    // 0x7eabac: r1 = LoadClassIdInstr(r0)
    //     0x7eabac: ldur            x1, [x0, #-1]
    //     0x7eabb0: ubfx            x1, x1, #0xc, #0x14
    // 0x7eabb4: str             x0, [SP]
    // 0x7eabb8: mov             x0, x1
    // 0x7eabbc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7eabbc: sub             lr, x0, #1, lsl #12
    //     0x7eabc0: ldr             lr, [x21, lr, lsl #3]
    //     0x7eabc4: blr             lr
    // 0x7eabc8: mov             x4, x0
    // 0x7eabcc: ldur            x3, [fp, #-8]
    // 0x7eabd0: stur            x4, [fp, #-0x20]
    // 0x7eabd4: r0 = LoadClassIdInstr(r3)
    //     0x7eabd4: ldur            x0, [x3, #-1]
    //     0x7eabd8: ubfx            x0, x0, #0xc, #0x14
    // 0x7eabdc: mov             x1, x3
    // 0x7eabe0: r2 = "updated_at"
    //     0x7eabe0: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x7eabe4: ldr             x2, [x2, #0xe88]
    // 0x7eabe8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eabe8: sub             lr, x0, #0x114
    //     0x7eabec: ldr             lr, [x21, lr, lsl #3]
    //     0x7eabf0: blr             lr
    // 0x7eabf4: cmp             w0, NULL
    // 0x7eabf8: b.ne            #0x7eac04
    // 0x7eabfc: r4 = Null
    //     0x7eabfc: mov             x4, NULL
    // 0x7eac00: b               #0x7eac70
    // 0x7eac04: ldur            x3, [fp, #-8]
    // 0x7eac08: r0 = LoadClassIdInstr(r3)
    //     0x7eac08: ldur            x0, [x3, #-1]
    //     0x7eac0c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eac10: mov             x1, x3
    // 0x7eac14: r2 = "updated_at"
    //     0x7eac14: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x7eac18: ldr             x2, [x2, #0xe88]
    // 0x7eac1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eac1c: sub             lr, x0, #0x114
    //     0x7eac20: ldr             lr, [x21, lr, lsl #3]
    //     0x7eac24: blr             lr
    // 0x7eac28: mov             x3, x0
    // 0x7eac2c: r2 = Null
    //     0x7eac2c: mov             x2, NULL
    // 0x7eac30: r1 = Null
    //     0x7eac30: mov             x1, NULL
    // 0x7eac34: stur            x3, [fp, #-0x28]
    // 0x7eac38: r4 = 60
    //     0x7eac38: movz            x4, #0x3c
    // 0x7eac3c: branchIfSmi(r0, 0x7eac48)
    //     0x7eac3c: tbz             w0, #0, #0x7eac48
    // 0x7eac40: r4 = LoadClassIdInstr(r0)
    //     0x7eac40: ldur            x4, [x0, #-1]
    //     0x7eac44: ubfx            x4, x4, #0xc, #0x14
    // 0x7eac48: sub             x4, x4, #0x5e
    // 0x7eac4c: cmp             x4, #1
    // 0x7eac50: b.ls            #0x7eac64
    // 0x7eac54: r8 = String
    //     0x7eac54: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7eac58: r3 = Null
    //     0x7eac58: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d340] Null
    //     0x7eac5c: ldr             x3, [x3, #0x340]
    // 0x7eac60: r0 = String()
    //     0x7eac60: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7eac64: ldur            x1, [fp, #-0x28]
    // 0x7eac68: r0 = parse()
    //     0x7eac68: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7eac6c: mov             x4, x0
    // 0x7eac70: ldur            x3, [fp, #-8]
    // 0x7eac74: stur            x4, [fp, #-0x28]
    // 0x7eac78: r0 = LoadClassIdInstr(r3)
    //     0x7eac78: ldur            x0, [x3, #-1]
    //     0x7eac7c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eac80: mov             x1, x3
    // 0x7eac84: r2 = "created_at"
    //     0x7eac84: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1d0] "created_at"
    //     0x7eac88: ldr             x2, [x2, #0x1d0]
    // 0x7eac8c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eac8c: sub             lr, x0, #0x114
    //     0x7eac90: ldr             lr, [x21, lr, lsl #3]
    //     0x7eac94: blr             lr
    // 0x7eac98: cmp             w0, NULL
    // 0x7eac9c: b.ne            #0x7eaca8
    // 0x7eaca0: r4 = Null
    //     0x7eaca0: mov             x4, NULL
    // 0x7eaca4: b               #0x7ead14
    // 0x7eaca8: ldur            x3, [fp, #-8]
    // 0x7eacac: r0 = LoadClassIdInstr(r3)
    //     0x7eacac: ldur            x0, [x3, #-1]
    //     0x7eacb0: ubfx            x0, x0, #0xc, #0x14
    // 0x7eacb4: mov             x1, x3
    // 0x7eacb8: r2 = "created_at"
    //     0x7eacb8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1d0] "created_at"
    //     0x7eacbc: ldr             x2, [x2, #0x1d0]
    // 0x7eacc0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eacc0: sub             lr, x0, #0x114
    //     0x7eacc4: ldr             lr, [x21, lr, lsl #3]
    //     0x7eacc8: blr             lr
    // 0x7eaccc: mov             x3, x0
    // 0x7eacd0: r2 = Null
    //     0x7eacd0: mov             x2, NULL
    // 0x7eacd4: r1 = Null
    //     0x7eacd4: mov             x1, NULL
    // 0x7eacd8: stur            x3, [fp, #-0x30]
    // 0x7eacdc: r4 = 60
    //     0x7eacdc: movz            x4, #0x3c
    // 0x7eace0: branchIfSmi(r0, 0x7eacec)
    //     0x7eace0: tbz             w0, #0, #0x7eacec
    // 0x7eace4: r4 = LoadClassIdInstr(r0)
    //     0x7eace4: ldur            x4, [x0, #-1]
    //     0x7eace8: ubfx            x4, x4, #0xc, #0x14
    // 0x7eacec: sub             x4, x4, #0x5e
    // 0x7eacf0: cmp             x4, #1
    // 0x7eacf4: b.ls            #0x7ead08
    // 0x7eacf8: r8 = String
    //     0x7eacf8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7eacfc: r3 = Null
    //     0x7eacfc: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d350] Null
    //     0x7ead00: ldr             x3, [x3, #0x350]
    // 0x7ead04: r0 = String()
    //     0x7ead04: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7ead08: ldur            x1, [fp, #-0x30]
    // 0x7ead0c: r0 = parse()
    //     0x7ead0c: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7ead10: mov             x4, x0
    // 0x7ead14: ldur            x3, [fp, #-8]
    // 0x7ead18: stur            x4, [fp, #-0x30]
    // 0x7ead1c: r0 = LoadClassIdInstr(r3)
    //     0x7ead1c: ldur            x0, [x3, #-1]
    //     0x7ead20: ubfx            x0, x0, #0xc, #0x14
    // 0x7ead24: mov             x1, x3
    // 0x7ead28: r2 = "description"
    //     0x7ead28: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x7ead2c: ldr             x2, [x2, #0xd28]
    // 0x7ead30: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ead30: sub             lr, x0, #0x114
    //     0x7ead34: ldr             lr, [x21, lr, lsl #3]
    //     0x7ead38: blr             lr
    // 0x7ead3c: mov             x3, x0
    // 0x7ead40: r2 = Null
    //     0x7ead40: mov             x2, NULL
    // 0x7ead44: r1 = Null
    //     0x7ead44: mov             x1, NULL
    // 0x7ead48: stur            x3, [fp, #-0x38]
    // 0x7ead4c: r4 = 60
    //     0x7ead4c: movz            x4, #0x3c
    // 0x7ead50: branchIfSmi(r0, 0x7ead5c)
    //     0x7ead50: tbz             w0, #0, #0x7ead5c
    // 0x7ead54: r4 = LoadClassIdInstr(r0)
    //     0x7ead54: ldur            x4, [x0, #-1]
    //     0x7ead58: ubfx            x4, x4, #0xc, #0x14
    // 0x7ead5c: sub             x4, x4, #0x5e
    // 0x7ead60: cmp             x4, #1
    // 0x7ead64: b.ls            #0x7ead78
    // 0x7ead68: r8 = String?
    //     0x7ead68: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7ead6c: r3 = Null
    //     0x7ead6c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d360] Null
    //     0x7ead70: ldr             x3, [x3, #0x360]
    // 0x7ead74: r0 = String?()
    //     0x7ead74: bl              #0x600324  ; IsType_String?_Stub
    // 0x7ead78: ldur            x3, [fp, #-8]
    // 0x7ead7c: r0 = LoadClassIdInstr(r3)
    //     0x7ead7c: ldur            x0, [x3, #-1]
    //     0x7ead80: ubfx            x0, x0, #0xc, #0x14
    // 0x7ead84: mov             x1, x3
    // 0x7ead88: r2 = "year"
    //     0x7ead88: add             x2, PP, #9, lsl #12  ; [pp+0x9310] "year"
    //     0x7ead8c: ldr             x2, [x2, #0x310]
    // 0x7ead90: r0 = GDT[cid_x0 + -0x114]()
    //     0x7ead90: sub             lr, x0, #0x114
    //     0x7ead94: ldr             lr, [x21, lr, lsl #3]
    //     0x7ead98: blr             lr
    // 0x7ead9c: mov             x3, x0
    // 0x7eada0: r2 = Null
    //     0x7eada0: mov             x2, NULL
    // 0x7eada4: r1 = Null
    //     0x7eada4: mov             x1, NULL
    // 0x7eada8: stur            x3, [fp, #-0x40]
    // 0x7eadac: branchIfSmi(r0, 0x7eadd8)
    //     0x7eadac: tbz             w0, #0, #0x7eadd8
    // 0x7eadb0: r4 = LoadClassIdInstr(r0)
    //     0x7eadb0: ldur            x4, [x0, #-1]
    //     0x7eadb4: ubfx            x4, x4, #0xc, #0x14
    // 0x7eadb8: sub             x4, x4, #0x3c
    // 0x7eadbc: cmp             x4, #2
    // 0x7eadc0: b.ls            #0x7eadd8
    // 0x7eadc4: r8 = num?
    //     0x7eadc4: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7eadc8: ldr             x8, [x8, #0x4f0]
    // 0x7eadcc: r3 = Null
    //     0x7eadcc: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d370] Null
    //     0x7eadd0: ldr             x3, [x3, #0x370]
    // 0x7eadd4: r0 = DefaultNullableTypeTest()
    //     0x7eadd4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7eadd8: ldur            x0, [fp, #-0x40]
    // 0x7eaddc: cmp             w0, NULL
    // 0x7eade0: b.ne            #0x7eadec
    // 0x7eade4: r4 = Null
    //     0x7eade4: mov             x4, NULL
    // 0x7eade8: b               #0x7eae14
    // 0x7eadec: r1 = 60
    //     0x7eadec: movz            x1, #0x3c
    // 0x7eadf0: branchIfSmi(r0, 0x7eadfc)
    //     0x7eadf0: tbz             w0, #0, #0x7eadfc
    // 0x7eadf4: r1 = LoadClassIdInstr(r0)
    //     0x7eadf4: ldur            x1, [x0, #-1]
    //     0x7eadf8: ubfx            x1, x1, #0xc, #0x14
    // 0x7eadfc: str             x0, [SP]
    // 0x7eae00: mov             x0, x1
    // 0x7eae04: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7eae04: sub             lr, x0, #1, lsl #12
    //     0x7eae08: ldr             lr, [x21, lr, lsl #3]
    //     0x7eae0c: blr             lr
    // 0x7eae10: mov             x4, x0
    // 0x7eae14: ldur            x3, [fp, #-8]
    // 0x7eae18: stur            x4, [fp, #-0x40]
    // 0x7eae1c: r0 = LoadClassIdInstr(r3)
    //     0x7eae1c: ldur            x0, [x3, #-1]
    //     0x7eae20: ubfx            x0, x0, #0xc, #0x14
    // 0x7eae24: mov             x1, x3
    // 0x7eae28: r2 = "count"
    //     0x7eae28: add             x2, PP, #0xc, lsl #12  ; [pp+0xc640] "count"
    //     0x7eae2c: ldr             x2, [x2, #0x640]
    // 0x7eae30: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eae30: sub             lr, x0, #0x114
    //     0x7eae34: ldr             lr, [x21, lr, lsl #3]
    //     0x7eae38: blr             lr
    // 0x7eae3c: mov             x3, x0
    // 0x7eae40: r2 = Null
    //     0x7eae40: mov             x2, NULL
    // 0x7eae44: r1 = Null
    //     0x7eae44: mov             x1, NULL
    // 0x7eae48: stur            x3, [fp, #-0x48]
    // 0x7eae4c: branchIfSmi(r0, 0x7eae78)
    //     0x7eae4c: tbz             w0, #0, #0x7eae78
    // 0x7eae50: r4 = LoadClassIdInstr(r0)
    //     0x7eae50: ldur            x4, [x0, #-1]
    //     0x7eae54: ubfx            x4, x4, #0xc, #0x14
    // 0x7eae58: sub             x4, x4, #0x3c
    // 0x7eae5c: cmp             x4, #2
    // 0x7eae60: b.ls            #0x7eae78
    // 0x7eae64: r8 = num?
    //     0x7eae64: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x7eae68: ldr             x8, [x8, #0x4f0]
    // 0x7eae6c: r3 = Null
    //     0x7eae6c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d380] Null
    //     0x7eae70: ldr             x3, [x3, #0x380]
    // 0x7eae74: r0 = DefaultNullableTypeTest()
    //     0x7eae74: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x7eae78: ldur            x0, [fp, #-0x48]
    // 0x7eae7c: cmp             w0, NULL
    // 0x7eae80: b.ne            #0x7eae8c
    // 0x7eae84: r4 = Null
    //     0x7eae84: mov             x4, NULL
    // 0x7eae88: b               #0x7eaeb4
    // 0x7eae8c: r1 = 60
    //     0x7eae8c: movz            x1, #0x3c
    // 0x7eae90: branchIfSmi(r0, 0x7eae9c)
    //     0x7eae90: tbz             w0, #0, #0x7eae9c
    // 0x7eae94: r1 = LoadClassIdInstr(r0)
    //     0x7eae94: ldur            x1, [x0, #-1]
    //     0x7eae98: ubfx            x1, x1, #0xc, #0x14
    // 0x7eae9c: str             x0, [SP]
    // 0x7eaea0: mov             x0, x1
    // 0x7eaea4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7eaea4: sub             lr, x0, #1, lsl #12
    //     0x7eaea8: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaeac: blr             lr
    // 0x7eaeb0: mov             x4, x0
    // 0x7eaeb4: ldur            x3, [fp, #-8]
    // 0x7eaeb8: stur            x4, [fp, #-0x48]
    // 0x7eaebc: r0 = LoadClassIdInstr(r3)
    //     0x7eaebc: ldur            x0, [x3, #-1]
    //     0x7eaec0: ubfx            x0, x0, #0xc, #0x14
    // 0x7eaec4: mov             x1, x3
    // 0x7eaec8: r2 = "document"
    //     0x7eaec8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d390] "document"
    //     0x7eaecc: ldr             x2, [x2, #0x390]
    // 0x7eaed0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eaed0: sub             lr, x0, #0x114
    //     0x7eaed4: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaed8: blr             lr
    // 0x7eaedc: cmp             w0, NULL
    // 0x7eaee0: b.ne            #0x7eaeec
    // 0x7eaee4: r8 = Null
    //     0x7eaee4: mov             x8, NULL
    // 0x7eaee8: b               #0x7eaf38
    // 0x7eaeec: ldur            x1, [fp, #-8]
    // 0x7eaef0: r0 = LoadClassIdInstr(r1)
    //     0x7eaef0: ldur            x0, [x1, #-1]
    //     0x7eaef4: ubfx            x0, x0, #0xc, #0x14
    // 0x7eaef8: r2 = "document"
    //     0x7eaef8: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d390] "document"
    //     0x7eaefc: ldr             x2, [x2, #0x390]
    // 0x7eaf00: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eaf00: sub             lr, x0, #0x114
    //     0x7eaf04: ldr             lr, [x21, lr, lsl #3]
    //     0x7eaf08: blr             lr
    // 0x7eaf0c: mov             x3, x0
    // 0x7eaf10: r2 = Null
    //     0x7eaf10: mov             x2, NULL
    // 0x7eaf14: r1 = Null
    //     0x7eaf14: mov             x1, NULL
    // 0x7eaf18: stur            x3, [fp, #-8]
    // 0x7eaf1c: r8 = Map<String, dynamic>
    //     0x7eaf1c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7eaf20: r3 = Null
    //     0x7eaf20: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d398] Null
    //     0x7eaf24: ldr             x3, [x3, #0x398]
    // 0x7eaf28: r0 = Map<String, dynamic>()
    //     0x7eaf28: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7eaf2c: ldur            x1, [fp, #-8]
    // 0x7eaf30: r0 = _$QurbanDocumentFromJson()
    //     0x7eaf30: bl              #0x7eb0a8  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanDocumentFromJson
    // 0x7eaf34: mov             x8, x0
    // 0x7eaf38: ldur            x7, [fp, #-0x10]
    // 0x7eaf3c: ldur            x6, [fp, #-0x18]
    // 0x7eaf40: ldur            x5, [fp, #-0x20]
    // 0x7eaf44: ldur            x4, [fp, #-0x28]
    // 0x7eaf48: ldur            x3, [fp, #-0x30]
    // 0x7eaf4c: ldur            x2, [fp, #-0x38]
    // 0x7eaf50: ldur            x1, [fp, #-0x40]
    // 0x7eaf54: ldur            x0, [fp, #-0x48]
    // 0x7eaf58: stur            x8, [fp, #-8]
    // 0x7eaf5c: r0 = QurbanTransaction()
    //     0x7eaf5c: bl              #0x7eb09c  ; AllocateQurbanTransactionStub -> QurbanTransaction (size=0x2c)
    // 0x7eaf60: ldur            x1, [fp, #-0x10]
    // 0x7eaf64: StoreField: r0->field_7 = r1
    //     0x7eaf64: stur            w1, [x0, #7]
    // 0x7eaf68: ldur            x1, [fp, #-0x18]
    // 0x7eaf6c: StoreField: r0->field_b = r1
    //     0x7eaf6c: stur            w1, [x0, #0xb]
    // 0x7eaf70: ldur            x1, [fp, #-0x20]
    // 0x7eaf74: StoreField: r0->field_f = r1
    //     0x7eaf74: stur            w1, [x0, #0xf]
    // 0x7eaf78: ldur            x1, [fp, #-0x28]
    // 0x7eaf7c: StoreField: r0->field_13 = r1
    //     0x7eaf7c: stur            w1, [x0, #0x13]
    // 0x7eaf80: ldur            x1, [fp, #-0x30]
    // 0x7eaf84: ArrayStore: r0[0] = r1  ; List_4
    //     0x7eaf84: stur            w1, [x0, #0x17]
    // 0x7eaf88: ldur            x1, [fp, #-0x38]
    // 0x7eaf8c: StoreField: r0->field_1b = r1
    //     0x7eaf8c: stur            w1, [x0, #0x1b]
    // 0x7eaf90: ldur            x1, [fp, #-0x40]
    // 0x7eaf94: StoreField: r0->field_1f = r1
    //     0x7eaf94: stur            w1, [x0, #0x1f]
    // 0x7eaf98: ldur            x1, [fp, #-0x48]
    // 0x7eaf9c: StoreField: r0->field_23 = r1
    //     0x7eaf9c: stur            w1, [x0, #0x23]
    // 0x7eafa0: ldur            x1, [fp, #-8]
    // 0x7eafa4: StoreField: r0->field_27 = r1
    //     0x7eafa4: stur            w1, [x0, #0x27]
    // 0x7eafa8: LeaveFrame
    //     0x7eafa8: mov             SP, fp
    //     0x7eafac: ldp             fp, lr, [SP], #0x10
    // 0x7eafb0: ret
    //     0x7eafb0: ret             
    // 0x7eafb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eafb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eafb8: b               #0x7eaa34
  }
  static _ _$QurbanDocumentToJson(/* No info */) {
    // ** addr: 0x7eb004, size: 0x98
    // 0x7eb004: EnterFrame
    //     0x7eb004: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb008: mov             fp, SP
    // 0x7eb00c: AllocStack(0x18)
    //     0x7eb00c: sub             SP, SP, #0x18
    // 0x7eb010: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x7eb010: mov             x0, x1
    //     0x7eb014: stur            x1, [fp, #-8]
    // 0x7eb018: CheckStackOverflow
    //     0x7eb018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb01c: cmp             SP, x16
    //     0x7eb020: b.ls            #0x7eb094
    // 0x7eb024: r1 = Null
    //     0x7eb024: mov             x1, NULL
    // 0x7eb028: r2 = 12
    //     0x7eb028: movz            x2, #0xc
    // 0x7eb02c: r0 = AllocateArray()
    //     0x7eb02c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7eb030: r16 = "report"
    //     0x7eb030: add             x16, PP, #0x30, lsl #12  ; [pp+0x30480] "report"
    //     0x7eb034: ldr             x16, [x16, #0x480]
    // 0x7eb038: StoreField: r0->field_f = r16
    //     0x7eb038: stur            w16, [x0, #0xf]
    // 0x7eb03c: ldur            x1, [fp, #-8]
    // 0x7eb040: LoadField: r2 = r1->field_7
    //     0x7eb040: ldur            w2, [x1, #7]
    // 0x7eb044: DecompressPointer r2
    //     0x7eb044: add             x2, x2, HEAP, lsl #32
    // 0x7eb048: StoreField: r0->field_13 = r2
    //     0x7eb048: stur            w2, [x0, #0x13]
    // 0x7eb04c: r16 = "receipt"
    //     0x7eb04c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d3b8] "receipt"
    //     0x7eb050: ldr             x16, [x16, #0x3b8]
    // 0x7eb054: ArrayStore: r0[0] = r16  ; List_4
    //     0x7eb054: stur            w16, [x0, #0x17]
    // 0x7eb058: LoadField: r2 = r1->field_b
    //     0x7eb058: ldur            w2, [x1, #0xb]
    // 0x7eb05c: DecompressPointer r2
    //     0x7eb05c: add             x2, x2, HEAP, lsl #32
    // 0x7eb060: StoreField: r0->field_1b = r2
    //     0x7eb060: stur            w2, [x0, #0x1b]
    // 0x7eb064: r16 = "certificate"
    //     0x7eb064: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d3d0] "certificate"
    //     0x7eb068: ldr             x16, [x16, #0x3d0]
    // 0x7eb06c: StoreField: r0->field_1f = r16
    //     0x7eb06c: stur            w16, [x0, #0x1f]
    // 0x7eb070: LoadField: r2 = r1->field_f
    //     0x7eb070: ldur            w2, [x1, #0xf]
    // 0x7eb074: DecompressPointer r2
    //     0x7eb074: add             x2, x2, HEAP, lsl #32
    // 0x7eb078: StoreField: r0->field_23 = r2
    //     0x7eb078: stur            w2, [x0, #0x23]
    // 0x7eb07c: r16 = <String, dynamic>
    //     0x7eb07c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7eb080: stp             x0, x16, [SP]
    // 0x7eb084: r0 = Map._fromLiteral()
    //     0x7eb084: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7eb088: LeaveFrame
    //     0x7eb088: mov             SP, fp
    //     0x7eb08c: ldp             fp, lr, [SP], #0x10
    // 0x7eb090: ret
    //     0x7eb090: ret             
    // 0x7eb094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb098: b               #0x7eb024
  }
  static _ _$QurbanDocumentFromJson(/* No info */) {
    // ** addr: 0x7eb0a8, size: 0x168
    // 0x7eb0a8: EnterFrame
    //     0x7eb0a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb0ac: mov             fp, SP
    // 0x7eb0b0: AllocStack(0x18)
    //     0x7eb0b0: sub             SP, SP, #0x18
    // 0x7eb0b4: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x7eb0b4: mov             x3, x1
    //     0x7eb0b8: stur            x1, [fp, #-8]
    // 0x7eb0bc: CheckStackOverflow
    //     0x7eb0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb0c0: cmp             SP, x16
    //     0x7eb0c4: b.ls            #0x7eb208
    // 0x7eb0c8: r0 = LoadClassIdInstr(r3)
    //     0x7eb0c8: ldur            x0, [x3, #-1]
    //     0x7eb0cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb0d0: mov             x1, x3
    // 0x7eb0d4: r2 = "report"
    //     0x7eb0d4: add             x2, PP, #0x30, lsl #12  ; [pp+0x30480] "report"
    //     0x7eb0d8: ldr             x2, [x2, #0x480]
    // 0x7eb0dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb0dc: sub             lr, x0, #0x114
    //     0x7eb0e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb0e4: blr             lr
    // 0x7eb0e8: mov             x3, x0
    // 0x7eb0ec: r2 = Null
    //     0x7eb0ec: mov             x2, NULL
    // 0x7eb0f0: r1 = Null
    //     0x7eb0f0: mov             x1, NULL
    // 0x7eb0f4: stur            x3, [fp, #-0x10]
    // 0x7eb0f8: r4 = 60
    //     0x7eb0f8: movz            x4, #0x3c
    // 0x7eb0fc: branchIfSmi(r0, 0x7eb108)
    //     0x7eb0fc: tbz             w0, #0, #0x7eb108
    // 0x7eb100: r4 = LoadClassIdInstr(r0)
    //     0x7eb100: ldur            x4, [x0, #-1]
    //     0x7eb104: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb108: sub             x4, x4, #0x5e
    // 0x7eb10c: cmp             x4, #1
    // 0x7eb110: b.ls            #0x7eb124
    // 0x7eb114: r8 = String?
    //     0x7eb114: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7eb118: r3 = Null
    //     0x7eb118: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d3a8] Null
    //     0x7eb11c: ldr             x3, [x3, #0x3a8]
    // 0x7eb120: r0 = String?()
    //     0x7eb120: bl              #0x600324  ; IsType_String?_Stub
    // 0x7eb124: ldur            x3, [fp, #-8]
    // 0x7eb128: r0 = LoadClassIdInstr(r3)
    //     0x7eb128: ldur            x0, [x3, #-1]
    //     0x7eb12c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb130: mov             x1, x3
    // 0x7eb134: r2 = "receipt"
    //     0x7eb134: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d3b8] "receipt"
    //     0x7eb138: ldr             x2, [x2, #0x3b8]
    // 0x7eb13c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb13c: sub             lr, x0, #0x114
    //     0x7eb140: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb144: blr             lr
    // 0x7eb148: mov             x3, x0
    // 0x7eb14c: r2 = Null
    //     0x7eb14c: mov             x2, NULL
    // 0x7eb150: r1 = Null
    //     0x7eb150: mov             x1, NULL
    // 0x7eb154: stur            x3, [fp, #-0x18]
    // 0x7eb158: r4 = 60
    //     0x7eb158: movz            x4, #0x3c
    // 0x7eb15c: branchIfSmi(r0, 0x7eb168)
    //     0x7eb15c: tbz             w0, #0, #0x7eb168
    // 0x7eb160: r4 = LoadClassIdInstr(r0)
    //     0x7eb160: ldur            x4, [x0, #-1]
    //     0x7eb164: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb168: sub             x4, x4, #0x5e
    // 0x7eb16c: cmp             x4, #1
    // 0x7eb170: b.ls            #0x7eb184
    // 0x7eb174: r8 = String?
    //     0x7eb174: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7eb178: r3 = Null
    //     0x7eb178: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d3c0] Null
    //     0x7eb17c: ldr             x3, [x3, #0x3c0]
    // 0x7eb180: r0 = String?()
    //     0x7eb180: bl              #0x600324  ; IsType_String?_Stub
    // 0x7eb184: ldur            x1, [fp, #-8]
    // 0x7eb188: r0 = LoadClassIdInstr(r1)
    //     0x7eb188: ldur            x0, [x1, #-1]
    //     0x7eb18c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb190: r2 = "certificate"
    //     0x7eb190: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d3d0] "certificate"
    //     0x7eb194: ldr             x2, [x2, #0x3d0]
    // 0x7eb198: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb198: sub             lr, x0, #0x114
    //     0x7eb19c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb1a0: blr             lr
    // 0x7eb1a4: mov             x3, x0
    // 0x7eb1a8: r2 = Null
    //     0x7eb1a8: mov             x2, NULL
    // 0x7eb1ac: r1 = Null
    //     0x7eb1ac: mov             x1, NULL
    // 0x7eb1b0: stur            x3, [fp, #-8]
    // 0x7eb1b4: r4 = 60
    //     0x7eb1b4: movz            x4, #0x3c
    // 0x7eb1b8: branchIfSmi(r0, 0x7eb1c4)
    //     0x7eb1b8: tbz             w0, #0, #0x7eb1c4
    // 0x7eb1bc: r4 = LoadClassIdInstr(r0)
    //     0x7eb1bc: ldur            x4, [x0, #-1]
    //     0x7eb1c0: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb1c4: sub             x4, x4, #0x5e
    // 0x7eb1c8: cmp             x4, #1
    // 0x7eb1cc: b.ls            #0x7eb1e0
    // 0x7eb1d0: r8 = String?
    //     0x7eb1d0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7eb1d4: r3 = Null
    //     0x7eb1d4: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d3d8] Null
    //     0x7eb1d8: ldr             x3, [x3, #0x3d8]
    // 0x7eb1dc: r0 = String?()
    //     0x7eb1dc: bl              #0x600324  ; IsType_String?_Stub
    // 0x7eb1e0: r0 = QurbanDocument()
    //     0x7eb1e0: bl              #0x7eb210  ; AllocateQurbanDocumentStub -> QurbanDocument (size=0x14)
    // 0x7eb1e4: ldur            x1, [fp, #-0x10]
    // 0x7eb1e8: StoreField: r0->field_7 = r1
    //     0x7eb1e8: stur            w1, [x0, #7]
    // 0x7eb1ec: ldur            x1, [fp, #-0x18]
    // 0x7eb1f0: StoreField: r0->field_b = r1
    //     0x7eb1f0: stur            w1, [x0, #0xb]
    // 0x7eb1f4: ldur            x1, [fp, #-8]
    // 0x7eb1f8: StoreField: r0->field_f = r1
    //     0x7eb1f8: stur            w1, [x0, #0xf]
    // 0x7eb1fc: LeaveFrame
    //     0x7eb1fc: mov             SP, fp
    //     0x7eb200: ldp             fp, lr, [SP], #0x10
    // 0x7eb204: ret
    //     0x7eb204: ret             
    // 0x7eb208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb208: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb20c: b               #0x7eb0c8
  }
}

// class id: 1127, size: 0x14, field offset: 0x8
class QurbanDocument extends Object {

  Map<String, dynamic> toJson(QurbanDocument) {
    // ** addr: 0x7eafd4, size: 0x48
    // 0x7eafd4: EnterFrame
    //     0x7eafd4: stp             fp, lr, [SP, #-0x10]!
    //     0x7eafd8: mov             fp, SP
    // 0x7eafdc: CheckStackOverflow
    //     0x7eafdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eafe0: cmp             SP, x16
    //     0x7eafe4: b.ls            #0x7eaffc
    // 0x7eafe8: ldr             x1, [fp, #0x10]
    // 0x7eafec: r0 = _$QurbanDocumentToJson()
    //     0x7eafec: bl              #0x7eb004  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanDocumentToJson
    // 0x7eaff0: LeaveFrame
    //     0x7eaff0: mov             SP, fp
    //     0x7eaff4: ldp             fp, lr, [SP], #0x10
    // 0x7eaff8: ret
    //     0x7eaff8: ret             
    // 0x7eaffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eaffc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb000: b               #0x7eafe8
  }
}

// class id: 1128, size: 0x2c, field offset: 0x8
class QurbanTransaction extends Object {

  Map<String, dynamic> toJson(QurbanTransaction) {
    // ** addr: 0x7ea780, size: 0x48
    // 0x7ea780: EnterFrame
    //     0x7ea780: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea784: mov             fp, SP
    // 0x7ea788: CheckStackOverflow
    //     0x7ea788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea78c: cmp             SP, x16
    //     0x7ea790: b.ls            #0x7ea7a8
    // 0x7ea794: ldr             x1, [fp, #0x10]
    // 0x7ea798: r0 = _$QurbanTransactionToJson()
    //     0x7ea798: bl              #0x7ea7b0  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanTransactionToJson
    // 0x7ea79c: LeaveFrame
    //     0x7ea79c: mov             SP, fp
    //     0x7ea7a0: ldp             fp, lr, [SP], #0x10
    // 0x7ea7a4: ret
    //     0x7ea7a4: ret             
    // 0x7ea7a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea7a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea7ac: b               #0x7ea794
  }
}

// class id: 1129, size: 0x18, field offset: 0x8
class QurbanReport extends Object {

  Map<String, dynamic> toJson(QurbanReport) {
    // ** addr: 0x7d9ab4, size: 0x48
    // 0x7d9ab4: EnterFrame
    //     0x7d9ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9ab8: mov             fp, SP
    // 0x7d9abc: CheckStackOverflow
    //     0x7d9abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d9ac0: cmp             SP, x16
    //     0x7d9ac4: b.ls            #0x7d9adc
    // 0x7d9ac8: ldr             x1, [fp, #0x10]
    // 0x7d9acc: r0 = _$QurbanReportToJson()
    //     0x7d9acc: bl              #0x7d9ae4  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanReportToJson
    // 0x7d9ad0: LeaveFrame
    //     0x7d9ad0: mov             SP, fp
    //     0x7d9ad4: ldp             fp, lr, [SP], #0x10
    // 0x7d9ad8: ret
    //     0x7d9ad8: ret             
    // 0x7d9adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d9adc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d9ae0: b               #0x7d9ac8
  }
}
