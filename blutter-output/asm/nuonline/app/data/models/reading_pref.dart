// lib: , url: package:nuonline/app/data/models/reading_pref.dart

// class id: 1050046, size: 0x8
class :: {
}

// class id: 5575, size: 0x24, field offset: 0x8
//   const constructor, 
class ReadingPref extends Equatable {

  _Double field_8;
  _Double field_10;
  bool field_18;
  bool field_1c;
  bool field_20;

  factory _ ReadingPref.fromMap(/* No info */) {
    // ** addr: 0x8adb1c, size: 0x234
    // 0x8adb1c: EnterFrame
    //     0x8adb1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8adb20: mov             fp, SP
    // 0x8adb24: AllocStack(0x30)
    //     0x8adb24: sub             SP, SP, #0x30
    // 0x8adb28: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8adb28: mov             x3, x2
    //     0x8adb2c: stur            x2, [fp, #-8]
    // 0x8adb30: CheckStackOverflow
    //     0x8adb30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8adb34: cmp             SP, x16
    //     0x8adb38: b.ls            #0x8add48
    // 0x8adb3c: r0 = LoadClassIdInstr(r3)
    //     0x8adb3c: ldur            x0, [x3, #-1]
    //     0x8adb40: ubfx            x0, x0, #0xc, #0x14
    // 0x8adb44: mov             x1, x3
    // 0x8adb48: r2 = "arabic"
    //     0x8adb48: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e28] "arabic"
    //     0x8adb4c: ldr             x2, [x2, #0xe28]
    // 0x8adb50: r0 = GDT[cid_x0 + -0x114]()
    //     0x8adb50: sub             lr, x0, #0x114
    //     0x8adb54: ldr             lr, [x21, lr, lsl #3]
    //     0x8adb58: blr             lr
    // 0x8adb5c: mov             x3, x0
    // 0x8adb60: r2 = Null
    //     0x8adb60: mov             x2, NULL
    // 0x8adb64: r1 = Null
    //     0x8adb64: mov             x1, NULL
    // 0x8adb68: stur            x3, [fp, #-0x10]
    // 0x8adb6c: r4 = 60
    //     0x8adb6c: movz            x4, #0x3c
    // 0x8adb70: branchIfSmi(r0, 0x8adb7c)
    //     0x8adb70: tbz             w0, #0, #0x8adb7c
    // 0x8adb74: r4 = LoadClassIdInstr(r0)
    //     0x8adb74: ldur            x4, [x0, #-1]
    //     0x8adb78: ubfx            x4, x4, #0xc, #0x14
    // 0x8adb7c: cmp             x4, #0x3e
    // 0x8adb80: b.eq            #0x8adb94
    // 0x8adb84: r8 = double
    //     0x8adb84: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0x8adb88: r3 = Null
    //     0x8adb88: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c3b0] Null
    //     0x8adb8c: ldr             x3, [x3, #0x3b0]
    // 0x8adb90: r0 = double()
    //     0x8adb90: bl              #0xed4460  ; IsType_double_Stub
    // 0x8adb94: ldur            x3, [fp, #-8]
    // 0x8adb98: r0 = LoadClassIdInstr(r3)
    //     0x8adb98: ldur            x0, [x3, #-1]
    //     0x8adb9c: ubfx            x0, x0, #0xc, #0x14
    // 0x8adba0: mov             x1, x3
    // 0x8adba4: r2 = "latin"
    //     0x8adba4: add             x2, PP, #0x27, lsl #12  ; [pp+0x273c0] "latin"
    //     0x8adba8: ldr             x2, [x2, #0x3c0]
    // 0x8adbac: r0 = GDT[cid_x0 + -0x114]()
    //     0x8adbac: sub             lr, x0, #0x114
    //     0x8adbb0: ldr             lr, [x21, lr, lsl #3]
    //     0x8adbb4: blr             lr
    // 0x8adbb8: mov             x3, x0
    // 0x8adbbc: r2 = Null
    //     0x8adbbc: mov             x2, NULL
    // 0x8adbc0: r1 = Null
    //     0x8adbc0: mov             x1, NULL
    // 0x8adbc4: stur            x3, [fp, #-0x18]
    // 0x8adbc8: r4 = 60
    //     0x8adbc8: movz            x4, #0x3c
    // 0x8adbcc: branchIfSmi(r0, 0x8adbd8)
    //     0x8adbcc: tbz             w0, #0, #0x8adbd8
    // 0x8adbd0: r4 = LoadClassIdInstr(r0)
    //     0x8adbd0: ldur            x4, [x0, #-1]
    //     0x8adbd4: ubfx            x4, x4, #0xc, #0x14
    // 0x8adbd8: cmp             x4, #0x3e
    // 0x8adbdc: b.eq            #0x8adbf0
    // 0x8adbe0: r8 = double
    //     0x8adbe0: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0x8adbe4: r3 = Null
    //     0x8adbe4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c3c0] Null
    //     0x8adbe8: ldr             x3, [x3, #0x3c0]
    // 0x8adbec: r0 = double()
    //     0x8adbec: bl              #0xed4460  ; IsType_double_Stub
    // 0x8adbf0: ldur            x3, [fp, #-8]
    // 0x8adbf4: r0 = LoadClassIdInstr(r3)
    //     0x8adbf4: ldur            x0, [x3, #-1]
    //     0x8adbf8: ubfx            x0, x0, #0xc, #0x14
    // 0x8adbfc: mov             x1, x3
    // 0x8adc00: r2 = "translation"
    //     0x8adc00: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0x8adc04: ldr             x2, [x2, #0x190]
    // 0x8adc08: r0 = GDT[cid_x0 + -0x114]()
    //     0x8adc08: sub             lr, x0, #0x114
    //     0x8adc0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8adc10: blr             lr
    // 0x8adc14: mov             x3, x0
    // 0x8adc18: r2 = Null
    //     0x8adc18: mov             x2, NULL
    // 0x8adc1c: r1 = Null
    //     0x8adc1c: mov             x1, NULL
    // 0x8adc20: stur            x3, [fp, #-0x20]
    // 0x8adc24: r4 = 60
    //     0x8adc24: movz            x4, #0x3c
    // 0x8adc28: branchIfSmi(r0, 0x8adc34)
    //     0x8adc28: tbz             w0, #0, #0x8adc34
    // 0x8adc2c: r4 = LoadClassIdInstr(r0)
    //     0x8adc2c: ldur            x4, [x0, #-1]
    //     0x8adc30: ubfx            x4, x4, #0xc, #0x14
    // 0x8adc34: cmp             x4, #0x3f
    // 0x8adc38: b.eq            #0x8adc4c
    // 0x8adc3c: r8 = bool
    //     0x8adc3c: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x8adc40: r3 = Null
    //     0x8adc40: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c3d0] Null
    //     0x8adc44: ldr             x3, [x3, #0x3d0]
    // 0x8adc48: r0 = bool()
    //     0x8adc48: bl              #0xed4390  ; IsType_bool_Stub
    // 0x8adc4c: ldur            x3, [fp, #-8]
    // 0x8adc50: r0 = LoadClassIdInstr(r3)
    //     0x8adc50: ldur            x0, [x3, #-1]
    //     0x8adc54: ubfx            x0, x0, #0xc, #0x14
    // 0x8adc58: mov             x1, x3
    // 0x8adc5c: r2 = "transliteration"
    //     0x8adc5c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0x8adc60: ldr             x2, [x2, #0xe58]
    // 0x8adc64: r0 = GDT[cid_x0 + -0x114]()
    //     0x8adc64: sub             lr, x0, #0x114
    //     0x8adc68: ldr             lr, [x21, lr, lsl #3]
    //     0x8adc6c: blr             lr
    // 0x8adc70: mov             x3, x0
    // 0x8adc74: r2 = Null
    //     0x8adc74: mov             x2, NULL
    // 0x8adc78: r1 = Null
    //     0x8adc78: mov             x1, NULL
    // 0x8adc7c: stur            x3, [fp, #-0x28]
    // 0x8adc80: r4 = 60
    //     0x8adc80: movz            x4, #0x3c
    // 0x8adc84: branchIfSmi(r0, 0x8adc90)
    //     0x8adc84: tbz             w0, #0, #0x8adc90
    // 0x8adc88: r4 = LoadClassIdInstr(r0)
    //     0x8adc88: ldur            x4, [x0, #-1]
    //     0x8adc8c: ubfx            x4, x4, #0xc, #0x14
    // 0x8adc90: cmp             x4, #0x3f
    // 0x8adc94: b.eq            #0x8adca8
    // 0x8adc98: r8 = bool
    //     0x8adc98: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x8adc9c: r3 = Null
    //     0x8adc9c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c3e0] Null
    //     0x8adca0: ldr             x3, [x3, #0x3e0]
    // 0x8adca4: r0 = bool()
    //     0x8adca4: bl              #0xed4390  ; IsType_bool_Stub
    // 0x8adca8: ldur            x1, [fp, #-8]
    // 0x8adcac: r0 = LoadClassIdInstr(r1)
    //     0x8adcac: ldur            x0, [x1, #-1]
    //     0x8adcb0: ubfx            x0, x0, #0xc, #0x14
    // 0x8adcb4: r2 = "alwaysOnDisplay"
    //     0x8adcb4: add             x2, PP, #0x27, lsl #12  ; [pp+0x273c8] "alwaysOnDisplay"
    //     0x8adcb8: ldr             x2, [x2, #0x3c8]
    // 0x8adcbc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8adcbc: sub             lr, x0, #0x114
    //     0x8adcc0: ldr             lr, [x21, lr, lsl #3]
    //     0x8adcc4: blr             lr
    // 0x8adcc8: mov             x3, x0
    // 0x8adccc: r2 = Null
    //     0x8adccc: mov             x2, NULL
    // 0x8adcd0: r1 = Null
    //     0x8adcd0: mov             x1, NULL
    // 0x8adcd4: stur            x3, [fp, #-8]
    // 0x8adcd8: r4 = 60
    //     0x8adcd8: movz            x4, #0x3c
    // 0x8adcdc: branchIfSmi(r0, 0x8adce8)
    //     0x8adcdc: tbz             w0, #0, #0x8adce8
    // 0x8adce0: r4 = LoadClassIdInstr(r0)
    //     0x8adce0: ldur            x4, [x0, #-1]
    //     0x8adce4: ubfx            x4, x4, #0xc, #0x14
    // 0x8adce8: cmp             x4, #0x3f
    // 0x8adcec: b.eq            #0x8add00
    // 0x8adcf0: r8 = bool
    //     0x8adcf0: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x8adcf4: r3 = Null
    //     0x8adcf4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c3f0] Null
    //     0x8adcf8: ldr             x3, [x3, #0x3f0]
    // 0x8adcfc: r0 = bool()
    //     0x8adcfc: bl              #0xed4390  ; IsType_bool_Stub
    // 0x8add00: ldur            x0, [fp, #-0x10]
    // 0x8add04: LoadField: d0 = r0->field_7
    //     0x8add04: ldur            d0, [x0, #7]
    // 0x8add08: stur            d0, [fp, #-0x30]
    // 0x8add0c: r0 = ReadingPref()
    //     0x8add0c: bl              #0x8add50  ; AllocateReadingPrefStub -> ReadingPref (size=0x24)
    // 0x8add10: ldur            d0, [fp, #-0x30]
    // 0x8add14: StoreField: r0->field_7 = d0
    //     0x8add14: stur            d0, [x0, #7]
    // 0x8add18: ldur            x1, [fp, #-0x18]
    // 0x8add1c: LoadField: d0 = r1->field_7
    //     0x8add1c: ldur            d0, [x1, #7]
    // 0x8add20: StoreField: r0->field_f = d0
    //     0x8add20: stur            d0, [x0, #0xf]
    // 0x8add24: ldur            x1, [fp, #-0x20]
    // 0x8add28: ArrayStore: r0[0] = r1  ; List_4
    //     0x8add28: stur            w1, [x0, #0x17]
    // 0x8add2c: ldur            x1, [fp, #-0x28]
    // 0x8add30: StoreField: r0->field_1b = r1
    //     0x8add30: stur            w1, [x0, #0x1b]
    // 0x8add34: ldur            x1, [fp, #-8]
    // 0x8add38: StoreField: r0->field_1f = r1
    //     0x8add38: stur            w1, [x0, #0x1f]
    // 0x8add3c: LeaveFrame
    //     0x8add3c: mov             SP, fp
    //     0x8add40: ldp             fp, lr, [SP], #0x10
    // 0x8add44: ret
    //     0x8add44: ret             
    // 0x8add48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8add48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8add4c: b               #0x8adb3c
  }
  _ toMap(/* No info */) {
    // ** addr: 0x8addd0, size: 0x18c
    // 0x8addd0: EnterFrame
    //     0x8addd0: stp             fp, lr, [SP, #-0x10]!
    //     0x8addd4: mov             fp, SP
    // 0x8addd8: AllocStack(0x18)
    //     0x8addd8: sub             SP, SP, #0x18
    // 0x8adddc: SetupParameters(ReadingPref this /* r1 => r0, fp-0x8 */)
    //     0x8adddc: mov             x0, x1
    //     0x8adde0: stur            x1, [fp, #-8]
    // 0x8adde4: CheckStackOverflow
    //     0x8adde4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8adde8: cmp             SP, x16
    //     0x8addec: b.ls            #0x8adf24
    // 0x8addf0: r1 = Null
    //     0x8addf0: mov             x1, NULL
    // 0x8addf4: r2 = 20
    //     0x8addf4: movz            x2, #0x14
    // 0x8addf8: r0 = AllocateArray()
    //     0x8addf8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8addfc: mov             x2, x0
    // 0x8ade00: r16 = "arabic"
    //     0x8ade00: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e28] "arabic"
    //     0x8ade04: ldr             x16, [x16, #0xe28]
    // 0x8ade08: StoreField: r2->field_f = r16
    //     0x8ade08: stur            w16, [x2, #0xf]
    // 0x8ade0c: ldur            x3, [fp, #-8]
    // 0x8ade10: LoadField: d0 = r3->field_7
    //     0x8ade10: ldur            d0, [x3, #7]
    // 0x8ade14: r0 = inline_Allocate_Double()
    //     0x8ade14: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8ade18: add             x0, x0, #0x10
    //     0x8ade1c: cmp             x1, x0
    //     0x8ade20: b.ls            #0x8adf2c
    //     0x8ade24: str             x0, [THR, #0x50]  ; THR::top
    //     0x8ade28: sub             x0, x0, #0xf
    //     0x8ade2c: movz            x1, #0xe15c
    //     0x8ade30: movk            x1, #0x3, lsl #16
    //     0x8ade34: stur            x1, [x0, #-1]
    // 0x8ade38: StoreField: r0->field_7 = d0
    //     0x8ade38: stur            d0, [x0, #7]
    // 0x8ade3c: mov             x1, x2
    // 0x8ade40: ArrayStore: r1[1] = r0  ; List_4
    //     0x8ade40: add             x25, x1, #0x13
    //     0x8ade44: str             w0, [x25]
    //     0x8ade48: tbz             w0, #0, #0x8ade64
    //     0x8ade4c: ldurb           w16, [x1, #-1]
    //     0x8ade50: ldurb           w17, [x0, #-1]
    //     0x8ade54: and             x16, x17, x16, lsr #2
    //     0x8ade58: tst             x16, HEAP, lsr #32
    //     0x8ade5c: b.eq            #0x8ade64
    //     0x8ade60: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ade64: r16 = "latin"
    //     0x8ade64: add             x16, PP, #0x27, lsl #12  ; [pp+0x273c0] "latin"
    //     0x8ade68: ldr             x16, [x16, #0x3c0]
    // 0x8ade6c: ArrayStore: r2[0] = r16  ; List_4
    //     0x8ade6c: stur            w16, [x2, #0x17]
    // 0x8ade70: LoadField: d0 = r3->field_f
    //     0x8ade70: ldur            d0, [x3, #0xf]
    // 0x8ade74: r0 = inline_Allocate_Double()
    //     0x8ade74: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8ade78: add             x0, x0, #0x10
    //     0x8ade7c: cmp             x1, x0
    //     0x8ade80: b.ls            #0x8adf44
    //     0x8ade84: str             x0, [THR, #0x50]  ; THR::top
    //     0x8ade88: sub             x0, x0, #0xf
    //     0x8ade8c: movz            x1, #0xe15c
    //     0x8ade90: movk            x1, #0x3, lsl #16
    //     0x8ade94: stur            x1, [x0, #-1]
    // 0x8ade98: StoreField: r0->field_7 = d0
    //     0x8ade98: stur            d0, [x0, #7]
    // 0x8ade9c: mov             x1, x2
    // 0x8adea0: ArrayStore: r1[3] = r0  ; List_4
    //     0x8adea0: add             x25, x1, #0x1b
    //     0x8adea4: str             w0, [x25]
    //     0x8adea8: tbz             w0, #0, #0x8adec4
    //     0x8adeac: ldurb           w16, [x1, #-1]
    //     0x8adeb0: ldurb           w17, [x0, #-1]
    //     0x8adeb4: and             x16, x17, x16, lsr #2
    //     0x8adeb8: tst             x16, HEAP, lsr #32
    //     0x8adebc: b.eq            #0x8adec4
    //     0x8adec0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8adec4: r16 = "translation"
    //     0x8adec4: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0x8adec8: ldr             x16, [x16, #0x190]
    // 0x8adecc: StoreField: r2->field_1f = r16
    //     0x8adecc: stur            w16, [x2, #0x1f]
    // 0x8aded0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x8aded0: ldur            w0, [x3, #0x17]
    // 0x8aded4: DecompressPointer r0
    //     0x8aded4: add             x0, x0, HEAP, lsl #32
    // 0x8aded8: StoreField: r2->field_23 = r0
    //     0x8aded8: stur            w0, [x2, #0x23]
    // 0x8adedc: r16 = "transliteration"
    //     0x8adedc: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0x8adee0: ldr             x16, [x16, #0xe58]
    // 0x8adee4: StoreField: r2->field_27 = r16
    //     0x8adee4: stur            w16, [x2, #0x27]
    // 0x8adee8: LoadField: r0 = r3->field_1b
    //     0x8adee8: ldur            w0, [x3, #0x1b]
    // 0x8adeec: DecompressPointer r0
    //     0x8adeec: add             x0, x0, HEAP, lsl #32
    // 0x8adef0: StoreField: r2->field_2b = r0
    //     0x8adef0: stur            w0, [x2, #0x2b]
    // 0x8adef4: r16 = "alwaysOnDisplay"
    //     0x8adef4: add             x16, PP, #0x27, lsl #12  ; [pp+0x273c8] "alwaysOnDisplay"
    //     0x8adef8: ldr             x16, [x16, #0x3c8]
    // 0x8adefc: StoreField: r2->field_2f = r16
    //     0x8adefc: stur            w16, [x2, #0x2f]
    // 0x8adf00: LoadField: r0 = r3->field_1f
    //     0x8adf00: ldur            w0, [x3, #0x1f]
    // 0x8adf04: DecompressPointer r0
    //     0x8adf04: add             x0, x0, HEAP, lsl #32
    // 0x8adf08: StoreField: r2->field_33 = r0
    //     0x8adf08: stur            w0, [x2, #0x33]
    // 0x8adf0c: r16 = <String, dynamic>
    //     0x8adf0c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8adf10: stp             x2, x16, [SP]
    // 0x8adf14: r0 = Map._fromLiteral()
    //     0x8adf14: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8adf18: LeaveFrame
    //     0x8adf18: mov             SP, fp
    //     0x8adf1c: ldp             fp, lr, [SP], #0x10
    // 0x8adf20: ret
    //     0x8adf20: ret             
    // 0x8adf24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8adf24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8adf28: b               #0x8addf0
    // 0x8adf2c: SaveReg d0
    //     0x8adf2c: str             q0, [SP, #-0x10]!
    // 0x8adf30: stp             x2, x3, [SP, #-0x10]!
    // 0x8adf34: r0 = AllocateDouble()
    //     0x8adf34: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8adf38: ldp             x2, x3, [SP], #0x10
    // 0x8adf3c: RestoreReg d0
    //     0x8adf3c: ldr             q0, [SP], #0x10
    // 0x8adf40: b               #0x8ade38
    // 0x8adf44: SaveReg d0
    //     0x8adf44: str             q0, [SP, #-0x10]!
    // 0x8adf48: stp             x2, x3, [SP, #-0x10]!
    // 0x8adf4c: r0 = AllocateDouble()
    //     0x8adf4c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x8adf50: ldp             x2, x3, [SP], #0x10
    // 0x8adf54: RestoreReg d0
    //     0x8adf54: ldr             q0, [SP], #0x10
    // 0x8adf58: b               #0x8ade98
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xb4db34, size: 0x268
    // 0xb4db34: EnterFrame
    //     0xb4db34: stp             fp, lr, [SP, #-0x10]!
    //     0xb4db38: mov             fp, SP
    // 0xb4db3c: AllocStack(0x28)
    //     0xb4db3c: sub             SP, SP, #0x28
    // 0xb4db40: SetupParameters({dynamic alwaysOnDisplay = Null /* r3 */, dynamic arabic = Null /* r5 */, dynamic latin = Null /* r6 */, dynamic translation = Null /* r7 */, dynamic transliteration = Null /* r0 */})
    //     0xb4db40: ldur            w0, [x4, #0x13]
    //     0xb4db44: ldur            w2, [x4, #0x1f]
    //     0xb4db48: add             x2, x2, HEAP, lsl #32
    //     0xb4db4c: add             x16, PP, #0x27, lsl #12  ; [pp+0x273c8] "alwaysOnDisplay"
    //     0xb4db50: ldr             x16, [x16, #0x3c8]
    //     0xb4db54: cmp             w2, w16
    //     0xb4db58: b.ne            #0xb4db7c
    //     0xb4db5c: ldur            w2, [x4, #0x23]
    //     0xb4db60: add             x2, x2, HEAP, lsl #32
    //     0xb4db64: sub             w3, w0, w2
    //     0xb4db68: add             x2, fp, w3, sxtw #2
    //     0xb4db6c: ldr             x2, [x2, #8]
    //     0xb4db70: mov             x3, x2
    //     0xb4db74: movz            x2, #0x1
    //     0xb4db78: b               #0xb4db84
    //     0xb4db7c: mov             x3, NULL
    //     0xb4db80: movz            x2, #0
    //     0xb4db84: lsl             x5, x2, #1
    //     0xb4db88: lsl             w6, w5, #1
    //     0xb4db8c: add             w7, w6, #8
    //     0xb4db90: add             x16, x4, w7, sxtw #1
    //     0xb4db94: ldur            w8, [x16, #0xf]
    //     0xb4db98: add             x8, x8, HEAP, lsl #32
    //     0xb4db9c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e28] "arabic"
    //     0xb4dba0: ldr             x16, [x16, #0xe28]
    //     0xb4dba4: cmp             w8, w16
    //     0xb4dba8: b.ne            #0xb4dbdc
    //     0xb4dbac: add             w2, w6, #0xa
    //     0xb4dbb0: add             x16, x4, w2, sxtw #1
    //     0xb4dbb4: ldur            w6, [x16, #0xf]
    //     0xb4dbb8: add             x6, x6, HEAP, lsl #32
    //     0xb4dbbc: sub             w2, w0, w6
    //     0xb4dbc0: add             x6, fp, w2, sxtw #2
    //     0xb4dbc4: ldr             x6, [x6, #8]
    //     0xb4dbc8: add             w2, w5, #2
    //     0xb4dbcc: sbfx            x5, x2, #1, #0x1f
    //     0xb4dbd0: mov             x2, x5
    //     0xb4dbd4: mov             x5, x6
    //     0xb4dbd8: b               #0xb4dbe0
    //     0xb4dbdc: mov             x5, NULL
    //     0xb4dbe0: lsl             x6, x2, #1
    //     0xb4dbe4: lsl             w7, w6, #1
    //     0xb4dbe8: add             w8, w7, #8
    //     0xb4dbec: add             x16, x4, w8, sxtw #1
    //     0xb4dbf0: ldur            w9, [x16, #0xf]
    //     0xb4dbf4: add             x9, x9, HEAP, lsl #32
    //     0xb4dbf8: add             x16, PP, #0x27, lsl #12  ; [pp+0x273c0] "latin"
    //     0xb4dbfc: ldr             x16, [x16, #0x3c0]
    //     0xb4dc00: cmp             w9, w16
    //     0xb4dc04: b.ne            #0xb4dc38
    //     0xb4dc08: add             w2, w7, #0xa
    //     0xb4dc0c: add             x16, x4, w2, sxtw #1
    //     0xb4dc10: ldur            w7, [x16, #0xf]
    //     0xb4dc14: add             x7, x7, HEAP, lsl #32
    //     0xb4dc18: sub             w2, w0, w7
    //     0xb4dc1c: add             x7, fp, w2, sxtw #2
    //     0xb4dc20: ldr             x7, [x7, #8]
    //     0xb4dc24: add             w2, w6, #2
    //     0xb4dc28: sbfx            x6, x2, #1, #0x1f
    //     0xb4dc2c: mov             x2, x6
    //     0xb4dc30: mov             x6, x7
    //     0xb4dc34: b               #0xb4dc3c
    //     0xb4dc38: mov             x6, NULL
    //     0xb4dc3c: lsl             x7, x2, #1
    //     0xb4dc40: lsl             w8, w7, #1
    //     0xb4dc44: add             w9, w8, #8
    //     0xb4dc48: add             x16, x4, w9, sxtw #1
    //     0xb4dc4c: ldur            w10, [x16, #0xf]
    //     0xb4dc50: add             x10, x10, HEAP, lsl #32
    //     0xb4dc54: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0xb4dc58: ldr             x16, [x16, #0x190]
    //     0xb4dc5c: cmp             w10, w16
    //     0xb4dc60: b.ne            #0xb4dc94
    //     0xb4dc64: add             w2, w8, #0xa
    //     0xb4dc68: add             x16, x4, w2, sxtw #1
    //     0xb4dc6c: ldur            w8, [x16, #0xf]
    //     0xb4dc70: add             x8, x8, HEAP, lsl #32
    //     0xb4dc74: sub             w2, w0, w8
    //     0xb4dc78: add             x8, fp, w2, sxtw #2
    //     0xb4dc7c: ldr             x8, [x8, #8]
    //     0xb4dc80: add             w2, w7, #2
    //     0xb4dc84: sbfx            x7, x2, #1, #0x1f
    //     0xb4dc88: mov             x2, x7
    //     0xb4dc8c: mov             x7, x8
    //     0xb4dc90: b               #0xb4dc98
    //     0xb4dc94: mov             x7, NULL
    //     0xb4dc98: lsl             x8, x2, #1
    //     0xb4dc9c: lsl             w2, w8, #1
    //     0xb4dca0: add             w8, w2, #8
    //     0xb4dca4: add             x16, x4, w8, sxtw #1
    //     0xb4dca8: ldur            w9, [x16, #0xf]
    //     0xb4dcac: add             x9, x9, HEAP, lsl #32
    //     0xb4dcb0: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0xb4dcb4: ldr             x16, [x16, #0xe58]
    //     0xb4dcb8: cmp             w9, w16
    //     0xb4dcbc: b.ne            #0xb4dce0
    //     0xb4dcc0: add             w8, w2, #0xa
    //     0xb4dcc4: add             x16, x4, w8, sxtw #1
    //     0xb4dcc8: ldur            w2, [x16, #0xf]
    //     0xb4dccc: add             x2, x2, HEAP, lsl #32
    //     0xb4dcd0: sub             w4, w0, w2
    //     0xb4dcd4: add             x0, fp, w4, sxtw #2
    //     0xb4dcd8: ldr             x0, [x0, #8]
    //     0xb4dcdc: b               #0xb4dce4
    //     0xb4dce0: mov             x0, NULL
    // 0xb4dce4: cmp             w5, NULL
    // 0xb4dce8: b.ne            #0xb4dcf4
    // 0xb4dcec: LoadField: d0 = r1->field_7
    //     0xb4dcec: ldur            d0, [x1, #7]
    // 0xb4dcf0: b               #0xb4dcf8
    // 0xb4dcf4: LoadField: d0 = r5->field_7
    //     0xb4dcf4: ldur            d0, [x5, #7]
    // 0xb4dcf8: stur            d0, [fp, #-0x28]
    // 0xb4dcfc: cmp             w6, NULL
    // 0xb4dd00: b.ne            #0xb4dd0c
    // 0xb4dd04: LoadField: d1 = r1->field_f
    //     0xb4dd04: ldur            d1, [x1, #0xf]
    // 0xb4dd08: b               #0xb4dd10
    // 0xb4dd0c: LoadField: d1 = r6->field_7
    //     0xb4dd0c: ldur            d1, [x6, #7]
    // 0xb4dd10: stur            d1, [fp, #-0x20]
    // 0xb4dd14: cmp             w7, NULL
    // 0xb4dd18: b.ne            #0xb4dd28
    // 0xb4dd1c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb4dd1c: ldur            w2, [x1, #0x17]
    // 0xb4dd20: DecompressPointer r2
    //     0xb4dd20: add             x2, x2, HEAP, lsl #32
    // 0xb4dd24: b               #0xb4dd2c
    // 0xb4dd28: mov             x2, x7
    // 0xb4dd2c: stur            x2, [fp, #-0x18]
    // 0xb4dd30: cmp             w0, NULL
    // 0xb4dd34: b.ne            #0xb4dd40
    // 0xb4dd38: LoadField: r0 = r1->field_1b
    //     0xb4dd38: ldur            w0, [x1, #0x1b]
    // 0xb4dd3c: DecompressPointer r0
    //     0xb4dd3c: add             x0, x0, HEAP, lsl #32
    // 0xb4dd40: stur            x0, [fp, #-0x10]
    // 0xb4dd44: cmp             w3, NULL
    // 0xb4dd48: b.ne            #0xb4dd5c
    // 0xb4dd4c: LoadField: r3 = r1->field_1f
    //     0xb4dd4c: ldur            w3, [x1, #0x1f]
    // 0xb4dd50: DecompressPointer r3
    //     0xb4dd50: add             x3, x3, HEAP, lsl #32
    // 0xb4dd54: mov             x1, x3
    // 0xb4dd58: b               #0xb4dd60
    // 0xb4dd5c: mov             x1, x3
    // 0xb4dd60: stur            x1, [fp, #-8]
    // 0xb4dd64: r0 = ReadingPref()
    //     0xb4dd64: bl              #0x8add50  ; AllocateReadingPrefStub -> ReadingPref (size=0x24)
    // 0xb4dd68: ldur            d0, [fp, #-0x28]
    // 0xb4dd6c: StoreField: r0->field_7 = d0
    //     0xb4dd6c: stur            d0, [x0, #7]
    // 0xb4dd70: ldur            d0, [fp, #-0x20]
    // 0xb4dd74: StoreField: r0->field_f = d0
    //     0xb4dd74: stur            d0, [x0, #0xf]
    // 0xb4dd78: ldur            x1, [fp, #-0x18]
    // 0xb4dd7c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4dd7c: stur            w1, [x0, #0x17]
    // 0xb4dd80: ldur            x1, [fp, #-0x10]
    // 0xb4dd84: StoreField: r0->field_1b = r1
    //     0xb4dd84: stur            w1, [x0, #0x1b]
    // 0xb4dd88: ldur            x1, [fp, #-8]
    // 0xb4dd8c: StoreField: r0->field_1f = r1
    //     0xb4dd8c: stur            w1, [x0, #0x1f]
    // 0xb4dd90: LeaveFrame
    //     0xb4dd90: mov             SP, fp
    //     0xb4dd94: ldp             fp, lr, [SP], #0x10
    // 0xb4dd98: ret
    //     0xb4dd98: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc014, size: 0x130
    // 0xbdc014: EnterFrame
    //     0xbdc014: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc018: mov             fp, SP
    // 0xbdc01c: AllocStack(0x30)
    //     0xbdc01c: sub             SP, SP, #0x30
    // 0xbdc020: r0 = 10
    //     0xbdc020: movz            x0, #0xa
    // 0xbdc024: LoadField: d0 = r1->field_7
    //     0xbdc024: ldur            d0, [x1, #7]
    // 0xbdc028: LoadField: d1 = r1->field_f
    //     0xbdc028: ldur            d1, [x1, #0xf]
    // 0xbdc02c: stur            d1, [fp, #-0x30]
    // 0xbdc030: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbdc030: ldur            w3, [x1, #0x17]
    // 0xbdc034: DecompressPointer r3
    //     0xbdc034: add             x3, x3, HEAP, lsl #32
    // 0xbdc038: stur            x3, [fp, #-0x20]
    // 0xbdc03c: LoadField: r4 = r1->field_1b
    //     0xbdc03c: ldur            w4, [x1, #0x1b]
    // 0xbdc040: DecompressPointer r4
    //     0xbdc040: add             x4, x4, HEAP, lsl #32
    // 0xbdc044: stur            x4, [fp, #-0x18]
    // 0xbdc048: LoadField: r5 = r1->field_1f
    //     0xbdc048: ldur            w5, [x1, #0x1f]
    // 0xbdc04c: DecompressPointer r5
    //     0xbdc04c: add             x5, x5, HEAP, lsl #32
    // 0xbdc050: stur            x5, [fp, #-0x10]
    // 0xbdc054: r6 = inline_Allocate_Double()
    //     0xbdc054: ldp             x6, x1, [THR, #0x50]  ; THR::top
    //     0xbdc058: add             x6, x6, #0x10
    //     0xbdc05c: cmp             x1, x6
    //     0xbdc060: b.ls            #0xbdc108
    //     0xbdc064: str             x6, [THR, #0x50]  ; THR::top
    //     0xbdc068: sub             x6, x6, #0xf
    //     0xbdc06c: movz            x1, #0xe15c
    //     0xbdc070: movk            x1, #0x3, lsl #16
    //     0xbdc074: stur            x1, [x6, #-1]
    // 0xbdc078: StoreField: r6->field_7 = d0
    //     0xbdc078: stur            d0, [x6, #7]
    // 0xbdc07c: mov             x2, x0
    // 0xbdc080: stur            x6, [fp, #-8]
    // 0xbdc084: r1 = Null
    //     0xbdc084: mov             x1, NULL
    // 0xbdc088: r0 = AllocateArray()
    //     0xbdc088: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc08c: mov             x2, x0
    // 0xbdc090: ldur            x0, [fp, #-8]
    // 0xbdc094: stur            x2, [fp, #-0x28]
    // 0xbdc098: StoreField: r2->field_f = r0
    //     0xbdc098: stur            w0, [x2, #0xf]
    // 0xbdc09c: ldur            d0, [fp, #-0x30]
    // 0xbdc0a0: r0 = inline_Allocate_Double()
    //     0xbdc0a0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbdc0a4: add             x0, x0, #0x10
    //     0xbdc0a8: cmp             x1, x0
    //     0xbdc0ac: b.ls            #0xbdc12c
    //     0xbdc0b0: str             x0, [THR, #0x50]  ; THR::top
    //     0xbdc0b4: sub             x0, x0, #0xf
    //     0xbdc0b8: movz            x1, #0xe15c
    //     0xbdc0bc: movk            x1, #0x3, lsl #16
    //     0xbdc0c0: stur            x1, [x0, #-1]
    // 0xbdc0c4: StoreField: r0->field_7 = d0
    //     0xbdc0c4: stur            d0, [x0, #7]
    // 0xbdc0c8: StoreField: r2->field_13 = r0
    //     0xbdc0c8: stur            w0, [x2, #0x13]
    // 0xbdc0cc: ldur            x0, [fp, #-0x20]
    // 0xbdc0d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdc0d0: stur            w0, [x2, #0x17]
    // 0xbdc0d4: ldur            x0, [fp, #-0x18]
    // 0xbdc0d8: StoreField: r2->field_1b = r0
    //     0xbdc0d8: stur            w0, [x2, #0x1b]
    // 0xbdc0dc: ldur            x0, [fp, #-0x10]
    // 0xbdc0e0: StoreField: r2->field_1f = r0
    //     0xbdc0e0: stur            w0, [x2, #0x1f]
    // 0xbdc0e4: r1 = <Object?>
    //     0xbdc0e4: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc0e8: r0 = AllocateGrowableArray()
    //     0xbdc0e8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc0ec: ldur            x1, [fp, #-0x28]
    // 0xbdc0f0: StoreField: r0->field_f = r1
    //     0xbdc0f0: stur            w1, [x0, #0xf]
    // 0xbdc0f4: r1 = 10
    //     0xbdc0f4: movz            x1, #0xa
    // 0xbdc0f8: StoreField: r0->field_b = r1
    //     0xbdc0f8: stur            w1, [x0, #0xb]
    // 0xbdc0fc: LeaveFrame
    //     0xbdc0fc: mov             SP, fp
    //     0xbdc100: ldp             fp, lr, [SP], #0x10
    // 0xbdc104: ret
    //     0xbdc104: ret             
    // 0xbdc108: stp             q0, q1, [SP, #-0x20]!
    // 0xbdc10c: stp             x4, x5, [SP, #-0x10]!
    // 0xbdc110: stp             x0, x3, [SP, #-0x10]!
    // 0xbdc114: r0 = AllocateDouble()
    //     0xbdc114: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbdc118: mov             x6, x0
    // 0xbdc11c: ldp             x0, x3, [SP], #0x10
    // 0xbdc120: ldp             x4, x5, [SP], #0x10
    // 0xbdc124: ldp             q0, q1, [SP], #0x20
    // 0xbdc128: b               #0xbdc078
    // 0xbdc12c: SaveReg d0
    //     0xbdc12c: str             q0, [SP, #-0x10]!
    // 0xbdc130: SaveReg r2
    //     0xbdc130: str             x2, [SP, #-8]!
    // 0xbdc134: r0 = AllocateDouble()
    //     0xbdc134: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbdc138: RestoreReg r2
    //     0xbdc138: ldr             x2, [SP], #8
    // 0xbdc13c: RestoreReg d0
    //     0xbdc13c: ldr             q0, [SP], #0x10
    // 0xbdc140: b               #0xbdc0c4
  }
}
