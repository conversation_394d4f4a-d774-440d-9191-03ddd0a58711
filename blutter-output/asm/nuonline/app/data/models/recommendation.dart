// lib: , url: package:nuonline/app/data/models/recommendation.dart

// class id: 1050047, size: 0x8
class :: {
}

// class id: 1126, size: 0x20, field offset: 0x8
class Recommendation extends Object {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x8c12b0, size: 0x180
    // 0x8c12b0: EnterFrame
    //     0x8c12b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c12b4: mov             fp, SP
    // 0x8c12b8: AllocStack(0x20)
    //     0x8c12b8: sub             SP, SP, #0x20
    // 0x8c12bc: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8c12bc: mov             x3, x1
    //     0x8c12c0: stur            x1, [fp, #-8]
    // 0x8c12c4: CheckStackOverflow
    //     0x8c12c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c12c8: cmp             SP, x16
    //     0x8c12cc: b.ls            #0x8c1428
    // 0x8c12d0: mov             x0, x3
    // 0x8c12d4: r2 = Null
    //     0x8c12d4: mov             x2, NULL
    // 0x8c12d8: r1 = Null
    //     0x8c12d8: mov             x1, NULL
    // 0x8c12dc: cmp             w0, NULL
    // 0x8c12e0: b.eq            #0x8c1384
    // 0x8c12e4: branchIfSmi(r0, 0x8c1384)
    //     0x8c12e4: tbz             w0, #0, #0x8c1384
    // 0x8c12e8: r3 = LoadClassIdInstr(r0)
    //     0x8c12e8: ldur            x3, [x0, #-1]
    //     0x8c12ec: ubfx            x3, x3, #0xc, #0x14
    // 0x8c12f0: r17 = 6718
    //     0x8c12f0: movz            x17, #0x1a3e
    // 0x8c12f4: cmp             x3, x17
    // 0x8c12f8: b.eq            #0x8c138c
    // 0x8c12fc: sub             x3, x3, #0x5a
    // 0x8c1300: cmp             x3, #2
    // 0x8c1304: b.ls            #0x8c138c
    // 0x8c1308: r4 = LoadClassIdInstr(r0)
    //     0x8c1308: ldur            x4, [x0, #-1]
    //     0x8c130c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c1310: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8c1314: ldr             x3, [x3, #0x18]
    // 0x8c1318: ldr             x3, [x3, x4, lsl #3]
    // 0x8c131c: LoadField: r3 = r3->field_2b
    //     0x8c131c: ldur            w3, [x3, #0x2b]
    // 0x8c1320: DecompressPointer r3
    //     0x8c1320: add             x3, x3, HEAP, lsl #32
    // 0x8c1324: cmp             w3, NULL
    // 0x8c1328: b.eq            #0x8c1384
    // 0x8c132c: LoadField: r3 = r3->field_f
    //     0x8c132c: ldur            w3, [x3, #0xf]
    // 0x8c1330: lsr             x3, x3, #3
    // 0x8c1334: r17 = 6718
    //     0x8c1334: movz            x17, #0x1a3e
    // 0x8c1338: cmp             x3, x17
    // 0x8c133c: b.eq            #0x8c138c
    // 0x8c1340: r3 = SubtypeTestCache
    //     0x8c1340: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f320] SubtypeTestCache
    //     0x8c1344: ldr             x3, [x3, #0x320]
    // 0x8c1348: r30 = Subtype1TestCacheStub
    //     0x8c1348: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8c134c: LoadField: r30 = r30->field_7
    //     0x8c134c: ldur            lr, [lr, #7]
    // 0x8c1350: blr             lr
    // 0x8c1354: cmp             w7, NULL
    // 0x8c1358: b.eq            #0x8c1364
    // 0x8c135c: tbnz            w7, #4, #0x8c1384
    // 0x8c1360: b               #0x8c138c
    // 0x8c1364: r8 = List
    //     0x8c1364: add             x8, PP, #0x3f, lsl #12  ; [pp+0x3f328] Type: List
    //     0x8c1368: ldr             x8, [x8, #0x328]
    // 0x8c136c: r3 = SubtypeTestCache
    //     0x8c136c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f330] SubtypeTestCache
    //     0x8c1370: ldr             x3, [x3, #0x330]
    // 0x8c1374: r30 = InstanceOfStub
    //     0x8c1374: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8c1378: LoadField: r30 = r30->field_7
    //     0x8c1378: ldur            lr, [lr, #7]
    // 0x8c137c: blr             lr
    // 0x8c1380: b               #0x8c1390
    // 0x8c1384: r0 = false
    //     0x8c1384: add             x0, NULL, #0x30  ; false
    // 0x8c1388: b               #0x8c1390
    // 0x8c138c: r0 = true
    //     0x8c138c: add             x0, NULL, #0x20  ; true
    // 0x8c1390: tbnz            w0, #4, #0x8c1410
    // 0x8c1394: ldur            x0, [fp, #-8]
    // 0x8c1398: r1 = Function '<anonymous closure>': static.
    //     0x8c1398: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f338] AnonymousClosure: static (0x8c1430), in [package:nuonline/app/data/models/recommendation.dart] Recommendation::fromResponse (0x8c12b0)
    //     0x8c139c: ldr             x1, [x1, #0x338]
    // 0x8c13a0: r2 = Null
    //     0x8c13a0: mov             x2, NULL
    // 0x8c13a4: r0 = AllocateClosure()
    //     0x8c13a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c13a8: mov             x1, x0
    // 0x8c13ac: ldur            x0, [fp, #-8]
    // 0x8c13b0: r2 = LoadClassIdInstr(r0)
    //     0x8c13b0: ldur            x2, [x0, #-1]
    //     0x8c13b4: ubfx            x2, x2, #0xc, #0x14
    // 0x8c13b8: r16 = <Recommendation>
    //     0x8c13b8: ldr             x16, [PP, #0x7bc8]  ; [pp+0x7bc8] TypeArguments: <Recommendation>
    // 0x8c13bc: stp             x0, x16, [SP, #8]
    // 0x8c13c0: str             x1, [SP]
    // 0x8c13c4: mov             x0, x2
    // 0x8c13c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c13c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c13cc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8c13cc: movz            x17, #0xf28c
    //     0x8c13d0: add             lr, x0, x17
    //     0x8c13d4: ldr             lr, [x21, lr, lsl #3]
    //     0x8c13d8: blr             lr
    // 0x8c13dc: r1 = LoadClassIdInstr(r0)
    //     0x8c13dc: ldur            x1, [x0, #-1]
    //     0x8c13e0: ubfx            x1, x1, #0xc, #0x14
    // 0x8c13e4: mov             x16, x0
    // 0x8c13e8: mov             x0, x1
    // 0x8c13ec: mov             x1, x16
    // 0x8c13f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8c13f0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8c13f4: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8c13f4: movz            x17, #0xd889
    //     0x8c13f8: add             lr, x0, x17
    //     0x8c13fc: ldr             lr, [x21, lr, lsl #3]
    //     0x8c1400: blr             lr
    // 0x8c1404: LeaveFrame
    //     0x8c1404: mov             SP, fp
    //     0x8c1408: ldp             fp, lr, [SP], #0x10
    // 0x8c140c: ret
    //     0x8c140c: ret             
    // 0x8c1410: r1 = <Recommendation>
    //     0x8c1410: ldr             x1, [PP, #0x7bc8]  ; [pp+0x7bc8] TypeArguments: <Recommendation>
    // 0x8c1414: r2 = 0
    //     0x8c1414: movz            x2, #0
    // 0x8c1418: r0 = _GrowableList()
    //     0x8c1418: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8c141c: LeaveFrame
    //     0x8c141c: mov             SP, fp
    //     0x8c1420: ldp             fp, lr, [SP], #0x10
    // 0x8c1424: ret
    //     0x8c1424: ret             
    // 0x8c1428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1428: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c142c: b               #0x8c12d0
  }
  [closure] static Recommendation <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8c1430, size: 0x50
    // 0x8c1430: EnterFrame
    //     0x8c1430: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1434: mov             fp, SP
    // 0x8c1438: CheckStackOverflow
    //     0x8c1438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c143c: cmp             SP, x16
    //     0x8c1440: b.ls            #0x8c1478
    // 0x8c1444: ldr             x0, [fp, #0x10]
    // 0x8c1448: r2 = Null
    //     0x8c1448: mov             x2, NULL
    // 0x8c144c: r1 = Null
    //     0x8c144c: mov             x1, NULL
    // 0x8c1450: r8 = Map<String, dynamic>
    //     0x8c1450: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8c1454: r3 = Null
    //     0x8c1454: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f340] Null
    //     0x8c1458: ldr             x3, [x3, #0x340]
    // 0x8c145c: r0 = Map<String, dynamic>()
    //     0x8c145c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8c1460: ldr             x2, [fp, #0x10]
    // 0x8c1464: r1 = Null
    //     0x8c1464: mov             x1, NULL
    // 0x8c1468: r0 = Recommendation.fromMap()
    //     0x8c1468: bl              #0x8c1480  ; [package:nuonline/app/data/models/recommendation.dart] Recommendation::Recommendation.fromMap
    // 0x8c146c: LeaveFrame
    //     0x8c146c: mov             SP, fp
    //     0x8c1470: ldp             fp, lr, [SP], #0x10
    // 0x8c1474: ret
    //     0x8c1474: ret             
    // 0x8c1478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c147c: b               #0x8c1444
  }
  factory _ Recommendation.fromMap(/* No info */) {
    // ** addr: 0x8c1480, size: 0x2d4
    // 0x8c1480: EnterFrame
    //     0x8c1480: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1484: mov             fp, SP
    // 0x8c1488: AllocStack(0x28)
    //     0x8c1488: sub             SP, SP, #0x28
    // 0x8c148c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8c148c: mov             x3, x2
    //     0x8c1490: stur            x2, [fp, #-8]
    // 0x8c1494: CheckStackOverflow
    //     0x8c1494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c1498: cmp             SP, x16
    //     0x8c149c: b.ls            #0x8c174c
    // 0x8c14a0: r0 = LoadClassIdInstr(r3)
    //     0x8c14a0: ldur            x0, [x3, #-1]
    //     0x8c14a4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c14a8: mov             x1, x3
    // 0x8c14ac: r2 = "params"
    //     0x8c14ac: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x8c14b0: ldr             x2, [x2, #0x3b8]
    // 0x8c14b4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c14b4: sub             lr, x0, #0x114
    //     0x8c14b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c14bc: blr             lr
    // 0x8c14c0: r1 = 60
    //     0x8c14c0: movz            x1, #0x3c
    // 0x8c14c4: branchIfSmi(r0, 0x8c14d0)
    //     0x8c14c4: tbz             w0, #0, #0x8c14d0
    // 0x8c14c8: r1 = LoadClassIdInstr(r0)
    //     0x8c14c8: ldur            x1, [x0, #-1]
    //     0x8c14cc: ubfx            x1, x1, #0xc, #0x14
    // 0x8c14d0: sub             x16, x1, #0x5e
    // 0x8c14d4: cmp             x16, #1
    // 0x8c14d8: b.hi            #0x8c156c
    // 0x8c14dc: ldur            x3, [fp, #-8]
    // 0x8c14e0: r0 = LoadClassIdInstr(r3)
    //     0x8c14e0: ldur            x0, [x3, #-1]
    //     0x8c14e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8c14e8: mov             x1, x3
    // 0x8c14ec: r2 = "params"
    //     0x8c14ec: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x8c14f0: ldr             x2, [x2, #0x3b8]
    // 0x8c14f4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c14f4: sub             lr, x0, #0x114
    //     0x8c14f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8c14fc: blr             lr
    // 0x8c1500: mov             x3, x0
    // 0x8c1504: r2 = Null
    //     0x8c1504: mov             x2, NULL
    // 0x8c1508: r1 = Null
    //     0x8c1508: mov             x1, NULL
    // 0x8c150c: stur            x3, [fp, #-0x10]
    // 0x8c1510: r4 = 60
    //     0x8c1510: movz            x4, #0x3c
    // 0x8c1514: branchIfSmi(r0, 0x8c1520)
    //     0x8c1514: tbz             w0, #0, #0x8c1520
    // 0x8c1518: r4 = LoadClassIdInstr(r0)
    //     0x8c1518: ldur            x4, [x0, #-1]
    //     0x8c151c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c1520: sub             x4, x4, #0x5e
    // 0x8c1524: cmp             x4, #1
    // 0x8c1528: b.ls            #0x8c153c
    // 0x8c152c: r8 = String
    //     0x8c152c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c1530: r3 = Null
    //     0x8c1530: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f350] Null
    //     0x8c1534: ldr             x3, [x3, #0x350]
    // 0x8c1538: r0 = String()
    //     0x8c1538: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c153c: ldur            x1, [fp, #-0x10]
    // 0x8c1540: r0 = jsonDecode()
    //     0x8c1540: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x8c1544: mov             x3, x0
    // 0x8c1548: r2 = Null
    //     0x8c1548: mov             x2, NULL
    // 0x8c154c: r1 = Null
    //     0x8c154c: mov             x1, NULL
    // 0x8c1550: stur            x3, [fp, #-0x10]
    // 0x8c1554: r8 = Map<String, dynamic>
    //     0x8c1554: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8c1558: r3 = Null
    //     0x8c1558: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f360] Null
    //     0x8c155c: ldr             x3, [x3, #0x360]
    // 0x8c1560: r0 = Map<String, dynamic>()
    //     0x8c1560: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8c1564: ldur            x4, [fp, #-0x10]
    // 0x8c1568: b               #0x8c1570
    // 0x8c156c: r4 = Null
    //     0x8c156c: mov             x4, NULL
    // 0x8c1570: ldur            x3, [fp, #-8]
    // 0x8c1574: stur            x4, [fp, #-0x10]
    // 0x8c1578: r0 = LoadClassIdInstr(r3)
    //     0x8c1578: ldur            x0, [x3, #-1]
    //     0x8c157c: ubfx            x0, x0, #0xc, #0x14
    // 0x8c1580: mov             x1, x3
    // 0x8c1584: r2 = "order"
    //     0x8c1584: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x8c1588: ldr             x2, [x2, #0xb78]
    // 0x8c158c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c158c: sub             lr, x0, #0x114
    //     0x8c1590: ldr             lr, [x21, lr, lsl #3]
    //     0x8c1594: blr             lr
    // 0x8c1598: mov             x3, x0
    // 0x8c159c: r2 = Null
    //     0x8c159c: mov             x2, NULL
    // 0x8c15a0: r1 = Null
    //     0x8c15a0: mov             x1, NULL
    // 0x8c15a4: stur            x3, [fp, #-0x18]
    // 0x8c15a8: branchIfSmi(r0, 0x8c15d0)
    //     0x8c15a8: tbz             w0, #0, #0x8c15d0
    // 0x8c15ac: r4 = LoadClassIdInstr(r0)
    //     0x8c15ac: ldur            x4, [x0, #-1]
    //     0x8c15b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8c15b4: sub             x4, x4, #0x3c
    // 0x8c15b8: cmp             x4, #1
    // 0x8c15bc: b.ls            #0x8c15d0
    // 0x8c15c0: r8 = int?
    //     0x8c15c0: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x8c15c4: r3 = Null
    //     0x8c15c4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f370] Null
    //     0x8c15c8: ldr             x3, [x3, #0x370]
    // 0x8c15cc: r0 = int?()
    //     0x8c15cc: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x8c15d0: ldur            x0, [fp, #-0x18]
    // 0x8c15d4: cmp             w0, NULL
    // 0x8c15d8: b.ne            #0x8c15e4
    // 0x8c15dc: r5 = 0
    //     0x8c15dc: movz            x5, #0
    // 0x8c15e0: b               #0x8c15f4
    // 0x8c15e4: r1 = LoadInt32Instr(r0)
    //     0x8c15e4: sbfx            x1, x0, #1, #0x1f
    //     0x8c15e8: tbz             w0, #0, #0x8c15f0
    //     0x8c15ec: ldur            x1, [x0, #7]
    // 0x8c15f0: mov             x5, x1
    // 0x8c15f4: ldur            x3, [fp, #-8]
    // 0x8c15f8: ldur            x4, [fp, #-0x10]
    // 0x8c15fc: stur            x5, [fp, #-0x20]
    // 0x8c1600: r0 = LoadClassIdInstr(r3)
    //     0x8c1600: ldur            x0, [x3, #-1]
    //     0x8c1604: ubfx            x0, x0, #0xc, #0x14
    // 0x8c1608: mov             x1, x3
    // 0x8c160c: r2 = "action"
    //     0x8c160c: ldr             x2, [PP, #0x42b0]  ; [pp+0x42b0] "action"
    // 0x8c1610: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c1610: sub             lr, x0, #0x114
    //     0x8c1614: ldr             lr, [x21, lr, lsl #3]
    //     0x8c1618: blr             lr
    // 0x8c161c: mov             x3, x0
    // 0x8c1620: r2 = Null
    //     0x8c1620: mov             x2, NULL
    // 0x8c1624: r1 = Null
    //     0x8c1624: mov             x1, NULL
    // 0x8c1628: stur            x3, [fp, #-0x18]
    // 0x8c162c: r4 = 60
    //     0x8c162c: movz            x4, #0x3c
    // 0x8c1630: branchIfSmi(r0, 0x8c163c)
    //     0x8c1630: tbz             w0, #0, #0x8c163c
    // 0x8c1634: r4 = LoadClassIdInstr(r0)
    //     0x8c1634: ldur            x4, [x0, #-1]
    //     0x8c1638: ubfx            x4, x4, #0xc, #0x14
    // 0x8c163c: sub             x4, x4, #0x5e
    // 0x8c1640: cmp             x4, #1
    // 0x8c1644: b.ls            #0x8c1658
    // 0x8c1648: r8 = String
    //     0x8c1648: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c164c: r3 = Null
    //     0x8c164c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f380] Null
    //     0x8c1650: ldr             x3, [x3, #0x380]
    // 0x8c1654: r0 = String()
    //     0x8c1654: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c1658: ldur            x3, [fp, #-8]
    // 0x8c165c: r0 = LoadClassIdInstr(r3)
    //     0x8c165c: ldur            x0, [x3, #-1]
    //     0x8c1660: ubfx            x0, x0, #0xc, #0x14
    // 0x8c1664: mov             x1, x3
    // 0x8c1668: r2 = "title"
    //     0x8c1668: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x8c166c: ldr             x2, [x2, #0x748]
    // 0x8c1670: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c1670: sub             lr, x0, #0x114
    //     0x8c1674: ldr             lr, [x21, lr, lsl #3]
    //     0x8c1678: blr             lr
    // 0x8c167c: mov             x3, x0
    // 0x8c1680: r2 = Null
    //     0x8c1680: mov             x2, NULL
    // 0x8c1684: r1 = Null
    //     0x8c1684: mov             x1, NULL
    // 0x8c1688: stur            x3, [fp, #-0x28]
    // 0x8c168c: r4 = 60
    //     0x8c168c: movz            x4, #0x3c
    // 0x8c1690: branchIfSmi(r0, 0x8c169c)
    //     0x8c1690: tbz             w0, #0, #0x8c169c
    // 0x8c1694: r4 = LoadClassIdInstr(r0)
    //     0x8c1694: ldur            x4, [x0, #-1]
    //     0x8c1698: ubfx            x4, x4, #0xc, #0x14
    // 0x8c169c: sub             x4, x4, #0x5e
    // 0x8c16a0: cmp             x4, #1
    // 0x8c16a4: b.ls            #0x8c16b8
    // 0x8c16a8: r8 = String
    //     0x8c16a8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c16ac: r3 = Null
    //     0x8c16ac: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f390] Null
    //     0x8c16b0: ldr             x3, [x3, #0x390]
    // 0x8c16b4: r0 = String()
    //     0x8c16b4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c16b8: ldur            x1, [fp, #-8]
    // 0x8c16bc: r0 = LoadClassIdInstr(r1)
    //     0x8c16bc: ldur            x0, [x1, #-1]
    //     0x8c16c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8c16c4: r2 = "route"
    //     0x8c16c4: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x8c16c8: ldr             x2, [x2, #0x3a0]
    // 0x8c16cc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8c16cc: sub             lr, x0, #0x114
    //     0x8c16d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8c16d4: blr             lr
    // 0x8c16d8: mov             x3, x0
    // 0x8c16dc: r2 = Null
    //     0x8c16dc: mov             x2, NULL
    // 0x8c16e0: r1 = Null
    //     0x8c16e0: mov             x1, NULL
    // 0x8c16e4: stur            x3, [fp, #-8]
    // 0x8c16e8: r4 = 60
    //     0x8c16e8: movz            x4, #0x3c
    // 0x8c16ec: branchIfSmi(r0, 0x8c16f8)
    //     0x8c16ec: tbz             w0, #0, #0x8c16f8
    // 0x8c16f0: r4 = LoadClassIdInstr(r0)
    //     0x8c16f0: ldur            x4, [x0, #-1]
    //     0x8c16f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8c16f8: sub             x4, x4, #0x5e
    // 0x8c16fc: cmp             x4, #1
    // 0x8c1700: b.ls            #0x8c1714
    // 0x8c1704: r8 = String
    //     0x8c1704: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c1708: r3 = Null
    //     0x8c1708: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f3a0] Null
    //     0x8c170c: ldr             x3, [x3, #0x3a0]
    // 0x8c1710: r0 = String()
    //     0x8c1710: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c1714: r0 = Recommendation()
    //     0x8c1714: bl              #0x8c1754  ; AllocateRecommendationStub -> Recommendation (size=0x20)
    // 0x8c1718: ldur            x1, [fp, #-0x20]
    // 0x8c171c: StoreField: r0->field_7 = r1
    //     0x8c171c: stur            x1, [x0, #7]
    // 0x8c1720: ldur            x1, [fp, #-0x18]
    // 0x8c1724: StoreField: r0->field_f = r1
    //     0x8c1724: stur            w1, [x0, #0xf]
    // 0x8c1728: ldur            x1, [fp, #-0x28]
    // 0x8c172c: StoreField: r0->field_13 = r1
    //     0x8c172c: stur            w1, [x0, #0x13]
    // 0x8c1730: ldur            x1, [fp, #-8]
    // 0x8c1734: ArrayStore: r0[0] = r1  ; List_4
    //     0x8c1734: stur            w1, [x0, #0x17]
    // 0x8c1738: ldur            x1, [fp, #-0x10]
    // 0x8c173c: StoreField: r0->field_1b = r1
    //     0x8c173c: stur            w1, [x0, #0x1b]
    // 0x8c1740: LeaveFrame
    //     0x8c1740: mov             SP, fp
    //     0x8c1744: ldp             fp, lr, [SP], #0x10
    // 0x8c1748: ret
    //     0x8c1748: ret             
    // 0x8c174c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c174c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c1750: b               #0x8c14a0
  }
  get _ icon(/* No info */) {
    // ** addr: 0xb9e9cc, size: 0x21c
    // 0xb9e9cc: EnterFrame
    //     0xb9e9cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb9e9d0: mov             fp, SP
    // 0xb9e9d4: AllocStack(0x20)
    //     0xb9e9d4: sub             SP, SP, #0x20
    // 0xb9e9d8: SetupParameters(Recommendation this /* r1 => r1, fp-0x10 */)
    //     0xb9e9d8: stur            x1, [fp, #-0x10]
    // 0xb9e9dc: CheckStackOverflow
    //     0xb9e9dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9e9e0: cmp             SP, x16
    //     0xb9e9e4: b.ls            #0xb9ebe0
    // 0xb9e9e8: LoadField: r0 = r1->field_f
    //     0xb9e9e8: ldur            w0, [x1, #0xf]
    // 0xb9e9ec: DecompressPointer r0
    //     0xb9e9ec: add             x0, x0, HEAP, lsl #32
    // 0xb9e9f0: stur            x0, [fp, #-8]
    // 0xb9e9f4: r16 = "open_url"
    //     0xb9e9f4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d778] "open_url"
    //     0xb9e9f8: ldr             x16, [x16, #0x778]
    // 0xb9e9fc: stp             x0, x16, [SP]
    // 0xb9ea00: r0 = ==()
    //     0xb9ea00: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9ea04: tbnz            w0, #4, #0xb9ea5c
    // 0xb9ea08: ldur            x0, [fp, #-0x10]
    // 0xb9ea0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb9ea0c: ldur            w1, [x0, #0x17]
    // 0xb9ea10: DecompressPointer r1
    //     0xb9ea10: add             x1, x1, HEAP, lsl #32
    // 0xb9ea14: r0 = LoadClassIdInstr(r1)
    //     0xb9ea14: ldur            x0, [x1, #-1]
    //     0xb9ea18: ubfx            x0, x0, #0xc, #0x14
    // 0xb9ea1c: r2 = "y"
    //     0xb9ea1c: ldr             x2, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xb9ea20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb9ea20: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb9ea24: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb9ea24: sub             lr, x0, #0xffc
    //     0xb9ea28: ldr             lr, [x21, lr, lsl #3]
    //     0xb9ea2c: blr             lr
    // 0xb9ea30: tbnz            w0, #4, #0xb9ea48
    // 0xb9ea34: r0 = Instance_IconData
    //     0xb9ea34: add             x0, PP, #0x32, lsl #12  ; [pp+0x32600] Obj!IconData@e0ff11
    //     0xb9ea38: ldr             x0, [x0, #0x600]
    // 0xb9ea3c: LeaveFrame
    //     0xb9ea3c: mov             SP, fp
    //     0xb9ea40: ldp             fp, lr, [SP], #0x10
    // 0xb9ea44: ret
    //     0xb9ea44: ret             
    // 0xb9ea48: r0 = Instance_IconData
    //     0xb9ea48: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e48] Obj!IconData@e10991
    //     0xb9ea4c: ldr             x0, [x0, #0xe48]
    // 0xb9ea50: LeaveFrame
    //     0xb9ea50: mov             SP, fp
    //     0xb9ea54: ldp             fp, lr, [SP], #0x10
    // 0xb9ea58: ret
    //     0xb9ea58: ret             
    // 0xb9ea5c: r16 = "menu"
    //     0xb9ea5c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e50] "menu"
    //     0xb9ea60: ldr             x16, [x16, #0xe50]
    // 0xb9ea64: ldur            lr, [fp, #-8]
    // 0xb9ea68: stp             lr, x16, [SP]
    // 0xb9ea6c: r0 = ==()
    //     0xb9ea6c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9ea70: tbnz            w0, #4, #0xb9ea88
    // 0xb9ea74: r0 = Instance_IconData
    //     0xb9ea74: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e58] Obj!IconData@e10971
    //     0xb9ea78: ldr             x0, [x0, #0xe58]
    // 0xb9ea7c: LeaveFrame
    //     0xb9ea7c: mov             SP, fp
    //     0xb9ea80: ldp             fp, lr, [SP], #0x10
    // 0xb9ea84: ret
    //     0xb9ea84: ret             
    // 0xb9ea88: r16 = "tutorial_detail"
    //     0xb9ea88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e60] "tutorial_detail"
    //     0xb9ea8c: ldr             x16, [x16, #0xe60]
    // 0xb9ea90: ldur            lr, [fp, #-8]
    // 0xb9ea94: stp             lr, x16, [SP]
    // 0xb9ea98: r0 = ==()
    //     0xb9ea98: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9ea9c: tbz             w0, #4, #0xb9ead0
    // 0xb9eaa0: r16 = "doa_category"
    //     0xb9eaa0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e68] "doa_category"
    //     0xb9eaa4: ldr             x16, [x16, #0xe68]
    // 0xb9eaa8: ldur            lr, [fp, #-8]
    // 0xb9eaac: stp             lr, x16, [SP]
    // 0xb9eab0: r0 = ==()
    //     0xb9eab0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eab4: tbz             w0, #4, #0xb9ead0
    // 0xb9eab8: r16 = "doa_detail"
    //     0xb9eab8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e70] "doa_detail"
    //     0xb9eabc: ldr             x16, [x16, #0xe70]
    // 0xb9eac0: ldur            lr, [fp, #-8]
    // 0xb9eac4: stp             lr, x16, [SP]
    // 0xb9eac8: r0 = ==()
    //     0xb9eac8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eacc: tbnz            w0, #4, #0xb9eae4
    // 0xb9ead0: r0 = Instance_IconData
    //     0xb9ead0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e78] Obj!IconData@e10951
    //     0xb9ead4: ldr             x0, [x0, #0xe78]
    // 0xb9ead8: LeaveFrame
    //     0xb9ead8: mov             SP, fp
    //     0xb9eadc: ldp             fp, lr, [SP], #0x10
    // 0xb9eae0: ret
    //     0xb9eae0: ret             
    // 0xb9eae4: r16 = "quran_list_juz"
    //     0xb9eae4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e80] "quran_list_juz"
    //     0xb9eae8: ldr             x16, [x16, #0xe80]
    // 0xb9eaec: ldur            lr, [fp, #-8]
    // 0xb9eaf0: stp             lr, x16, [SP]
    // 0xb9eaf4: r0 = ==()
    //     0xb9eaf4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eaf8: tbz             w0, #4, #0xb9eb2c
    // 0xb9eafc: r16 = "quran_page"
    //     0xb9eafc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e88] "quran_page"
    //     0xb9eb00: ldr             x16, [x16, #0xe88]
    // 0xb9eb04: ldur            lr, [fp, #-8]
    // 0xb9eb08: stp             lr, x16, [SP]
    // 0xb9eb0c: r0 = ==()
    //     0xb9eb0c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb10: tbz             w0, #4, #0xb9eb2c
    // 0xb9eb14: r16 = "quran_list_surah"
    //     0xb9eb14: add             x16, PP, #0x33, lsl #12  ; [pp+0x33e90] "quran_list_surah"
    //     0xb9eb18: ldr             x16, [x16, #0xe90]
    // 0xb9eb1c: ldur            lr, [fp, #-8]
    // 0xb9eb20: stp             lr, x16, [SP]
    // 0xb9eb24: r0 = ==()
    //     0xb9eb24: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb28: tbnz            w0, #4, #0xb9eb40
    // 0xb9eb2c: r0 = Instance_IconData
    //     0xb9eb2c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e98] Obj!IconData@e10931
    //     0xb9eb30: ldr             x0, [x0, #0xe98]
    // 0xb9eb34: LeaveFrame
    //     0xb9eb34: mov             SP, fp
    //     0xb9eb38: ldp             fp, lr, [SP], #0x10
    // 0xb9eb3c: ret
    //     0xb9eb3c: ret             
    // 0xb9eb40: r16 = "article_tag"
    //     0xb9eb40: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ea0] "article_tag"
    //     0xb9eb44: ldr             x16, [x16, #0xea0]
    // 0xb9eb48: ldur            lr, [fp, #-8]
    // 0xb9eb4c: stp             lr, x16, [SP]
    // 0xb9eb50: r0 = ==()
    //     0xb9eb50: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb54: tbz             w0, #4, #0xb9ebb8
    // 0xb9eb58: r16 = "article_prefix"
    //     0xb9eb58: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ea8] "article_prefix"
    //     0xb9eb5c: ldr             x16, [x16, #0xea8]
    // 0xb9eb60: ldur            lr, [fp, #-8]
    // 0xb9eb64: stp             lr, x16, [SP]
    // 0xb9eb68: r0 = ==()
    //     0xb9eb68: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb6c: tbz             w0, #4, #0xb9ebb8
    // 0xb9eb70: r16 = "article_category"
    //     0xb9eb70: add             x16, PP, #0x33, lsl #12  ; [pp+0x33eb0] "article_category"
    //     0xb9eb74: ldr             x16, [x16, #0xeb0]
    // 0xb9eb78: ldur            lr, [fp, #-8]
    // 0xb9eb7c: stp             lr, x16, [SP]
    // 0xb9eb80: r0 = ==()
    //     0xb9eb80: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb84: tbz             w0, #4, #0xb9ebb8
    // 0xb9eb88: r16 = "article_detail"
    //     0xb9eb88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33eb8] "article_detail"
    //     0xb9eb8c: ldr             x16, [x16, #0xeb8]
    // 0xb9eb90: ldur            lr, [fp, #-8]
    // 0xb9eb94: stp             lr, x16, [SP]
    // 0xb9eb98: r0 = ==()
    //     0xb9eb98: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9eb9c: tbz             w0, #4, #0xb9ebb8
    // 0xb9eba0: r16 = "topic_detail"
    //     0xb9eba0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ec0] "topic_detail"
    //     0xb9eba4: ldr             x16, [x16, #0xec0]
    // 0xb9eba8: ldur            lr, [fp, #-8]
    // 0xb9ebac: stp             lr, x16, [SP]
    // 0xb9ebb0: r0 = ==()
    //     0xb9ebb0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb9ebb4: tbnz            w0, #4, #0xb9ebcc
    // 0xb9ebb8: r0 = Instance_IconData
    //     0xb9ebb8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33ec8] Obj!IconData@e10291
    //     0xb9ebbc: ldr             x0, [x0, #0xec8]
    // 0xb9ebc0: LeaveFrame
    //     0xb9ebc0: mov             SP, fp
    //     0xb9ebc4: ldp             fp, lr, [SP], #0x10
    // 0xb9ebc8: ret
    //     0xb9ebc8: ret             
    // 0xb9ebcc: r0 = Instance_IconData
    //     0xb9ebcc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e48] Obj!IconData@e10991
    //     0xb9ebd0: ldr             x0, [x0, #0xe48]
    // 0xb9ebd4: LeaveFrame
    //     0xb9ebd4: mov             SP, fp
    //     0xb9ebd8: ldp             fp, lr, [SP], #0x10
    // 0xb9ebdc: ret
    //     0xb9ebdc: ret             
    // 0xb9ebe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9ebe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9ebe4: b               #0xb9e9e8
  }
}

// class id: 1650, size: 0x14, field offset: 0xc
class RecommendationAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa64f24, size: 0x43c
    // 0xa64f24: EnterFrame
    //     0xa64f24: stp             fp, lr, [SP, #-0x10]!
    //     0xa64f28: mov             fp, SP
    // 0xa64f2c: AllocStack(0x58)
    //     0xa64f2c: sub             SP, SP, #0x58
    // 0xa64f30: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa64f30: stur            x2, [fp, #-0x20]
    // 0xa64f34: CheckStackOverflow
    //     0xa64f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa64f38: cmp             SP, x16
    //     0xa64f3c: b.ls            #0xa65348
    // 0xa64f40: LoadField: r3 = r2->field_23
    //     0xa64f40: ldur            x3, [x2, #0x23]
    // 0xa64f44: add             x0, x3, #1
    // 0xa64f48: LoadField: r1 = r2->field_1b
    //     0xa64f48: ldur            x1, [x2, #0x1b]
    // 0xa64f4c: cmp             x0, x1
    // 0xa64f50: b.gt            #0xa652ec
    // 0xa64f54: LoadField: r4 = r2->field_7
    //     0xa64f54: ldur            w4, [x2, #7]
    // 0xa64f58: DecompressPointer r4
    //     0xa64f58: add             x4, x4, HEAP, lsl #32
    // 0xa64f5c: stur            x4, [fp, #-0x18]
    // 0xa64f60: StoreField: r2->field_23 = r0
    //     0xa64f60: stur            x0, [x2, #0x23]
    // 0xa64f64: LoadField: r0 = r4->field_13
    //     0xa64f64: ldur            w0, [x4, #0x13]
    // 0xa64f68: r5 = LoadInt32Instr(r0)
    //     0xa64f68: sbfx            x5, x0, #1, #0x1f
    // 0xa64f6c: mov             x0, x5
    // 0xa64f70: mov             x1, x3
    // 0xa64f74: stur            x5, [fp, #-0x10]
    // 0xa64f78: cmp             x1, x0
    // 0xa64f7c: b.hs            #0xa65350
    // 0xa64f80: LoadField: r0 = r4->field_7
    //     0xa64f80: ldur            x0, [x4, #7]
    // 0xa64f84: ldrb            w1, [x0, x3]
    // 0xa64f88: stur            x1, [fp, #-8]
    // 0xa64f8c: r16 = <int, dynamic>
    //     0xa64f8c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa64f90: ldr             x16, [x16, #0xac0]
    // 0xa64f94: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa64f98: stp             lr, x16, [SP]
    // 0xa64f9c: r0 = Map._fromLiteral()
    //     0xa64f9c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa64fa0: mov             x2, x0
    // 0xa64fa4: stur            x2, [fp, #-0x38]
    // 0xa64fa8: r6 = 0
    //     0xa64fa8: movz            x6, #0
    // 0xa64fac: ldur            x3, [fp, #-0x20]
    // 0xa64fb0: ldur            x4, [fp, #-0x18]
    // 0xa64fb4: ldur            x5, [fp, #-8]
    // 0xa64fb8: stur            x6, [fp, #-0x30]
    // 0xa64fbc: CheckStackOverflow
    //     0xa64fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa64fc0: cmp             SP, x16
    //     0xa64fc4: b.ls            #0xa65354
    // 0xa64fc8: cmp             x6, x5
    // 0xa64fcc: b.ge            #0xa65058
    // 0xa64fd0: LoadField: r7 = r3->field_23
    //     0xa64fd0: ldur            x7, [x3, #0x23]
    // 0xa64fd4: add             x0, x7, #1
    // 0xa64fd8: LoadField: r1 = r3->field_1b
    //     0xa64fd8: ldur            x1, [x3, #0x1b]
    // 0xa64fdc: cmp             x0, x1
    // 0xa64fe0: b.gt            #0xa65314
    // 0xa64fe4: StoreField: r3->field_23 = r0
    //     0xa64fe4: stur            x0, [x3, #0x23]
    // 0xa64fe8: ldur            x0, [fp, #-0x10]
    // 0xa64fec: mov             x1, x7
    // 0xa64ff0: cmp             x1, x0
    // 0xa64ff4: b.hs            #0xa6535c
    // 0xa64ff8: LoadField: r0 = r4->field_7
    //     0xa64ff8: ldur            x0, [x4, #7]
    // 0xa64ffc: ldrb            w8, [x0, x7]
    // 0xa65000: mov             x1, x3
    // 0xa65004: stur            x8, [fp, #-0x28]
    // 0xa65008: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa65008: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa6500c: r0 = read()
    //     0xa6500c: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa65010: mov             x1, x0
    // 0xa65014: ldur            x0, [fp, #-0x28]
    // 0xa65018: lsl             x2, x0, #1
    // 0xa6501c: r16 = LoadInt32Instr(r2)
    //     0xa6501c: sbfx            x16, x2, #1, #0x1f
    // 0xa65020: r17 = 11601
    //     0xa65020: movz            x17, #0x2d51
    // 0xa65024: mul             x0, x16, x17
    // 0xa65028: umulh           x16, x16, x17
    // 0xa6502c: eor             x0, x0, x16
    // 0xa65030: r0 = 0
    //     0xa65030: eor             x0, x0, x0, lsr #32
    // 0xa65034: ubfiz           x0, x0, #1, #0x1e
    // 0xa65038: r5 = LoadInt32Instr(r0)
    //     0xa65038: sbfx            x5, x0, #1, #0x1f
    // 0xa6503c: mov             x3, x1
    // 0xa65040: ldur            x1, [fp, #-0x38]
    // 0xa65044: r0 = _set()
    //     0xa65044: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa65048: ldur            x0, [fp, #-0x30]
    // 0xa6504c: add             x6, x0, #1
    // 0xa65050: ldur            x2, [fp, #-0x38]
    // 0xa65054: b               #0xa64fac
    // 0xa65058: mov             x0, x2
    // 0xa6505c: mov             x1, x0
    // 0xa65060: r2 = 0
    //     0xa65060: movz            x2, #0
    // 0xa65064: r0 = _getValueOrData()
    //     0xa65064: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65068: ldur            x3, [fp, #-0x38]
    // 0xa6506c: LoadField: r1 = r3->field_f
    //     0xa6506c: ldur            w1, [x3, #0xf]
    // 0xa65070: DecompressPointer r1
    //     0xa65070: add             x1, x1, HEAP, lsl #32
    // 0xa65074: cmp             w1, w0
    // 0xa65078: b.ne            #0xa65084
    // 0xa6507c: r4 = Null
    //     0xa6507c: mov             x4, NULL
    // 0xa65080: b               #0xa65088
    // 0xa65084: mov             x4, x0
    // 0xa65088: mov             x0, x4
    // 0xa6508c: stur            x4, [fp, #-0x18]
    // 0xa65090: r2 = Null
    //     0xa65090: mov             x2, NULL
    // 0xa65094: r1 = Null
    //     0xa65094: mov             x1, NULL
    // 0xa65098: branchIfSmi(r0, 0xa650c0)
    //     0xa65098: tbz             w0, #0, #0xa650c0
    // 0xa6509c: r4 = LoadClassIdInstr(r0)
    //     0xa6509c: ldur            x4, [x0, #-1]
    //     0xa650a0: ubfx            x4, x4, #0xc, #0x14
    // 0xa650a4: sub             x4, x4, #0x3c
    // 0xa650a8: cmp             x4, #1
    // 0xa650ac: b.ls            #0xa650c0
    // 0xa650b0: r8 = int
    //     0xa650b0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa650b4: r3 = Null
    //     0xa650b4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e68] Null
    //     0xa650b8: ldr             x3, [x3, #0xe68]
    // 0xa650bc: r0 = int()
    //     0xa650bc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa650c0: ldur            x1, [fp, #-0x38]
    // 0xa650c4: r2 = 2
    //     0xa650c4: movz            x2, #0x2
    // 0xa650c8: r0 = _getValueOrData()
    //     0xa650c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa650cc: ldur            x3, [fp, #-0x38]
    // 0xa650d0: LoadField: r1 = r3->field_f
    //     0xa650d0: ldur            w1, [x3, #0xf]
    // 0xa650d4: DecompressPointer r1
    //     0xa650d4: add             x1, x1, HEAP, lsl #32
    // 0xa650d8: cmp             w1, w0
    // 0xa650dc: b.ne            #0xa650e8
    // 0xa650e0: r4 = Null
    //     0xa650e0: mov             x4, NULL
    // 0xa650e4: b               #0xa650ec
    // 0xa650e8: mov             x4, x0
    // 0xa650ec: mov             x0, x4
    // 0xa650f0: stur            x4, [fp, #-0x20]
    // 0xa650f4: r2 = Null
    //     0xa650f4: mov             x2, NULL
    // 0xa650f8: r1 = Null
    //     0xa650f8: mov             x1, NULL
    // 0xa650fc: r4 = 60
    //     0xa650fc: movz            x4, #0x3c
    // 0xa65100: branchIfSmi(r0, 0xa6510c)
    //     0xa65100: tbz             w0, #0, #0xa6510c
    // 0xa65104: r4 = LoadClassIdInstr(r0)
    //     0xa65104: ldur            x4, [x0, #-1]
    //     0xa65108: ubfx            x4, x4, #0xc, #0x14
    // 0xa6510c: sub             x4, x4, #0x5e
    // 0xa65110: cmp             x4, #1
    // 0xa65114: b.ls            #0xa65128
    // 0xa65118: r8 = String
    //     0xa65118: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa6511c: r3 = Null
    //     0xa6511c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e78] Null
    //     0xa65120: ldr             x3, [x3, #0xe78]
    // 0xa65124: r0 = String()
    //     0xa65124: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65128: ldur            x1, [fp, #-0x38]
    // 0xa6512c: r2 = 4
    //     0xa6512c: movz            x2, #0x4
    // 0xa65130: r0 = _getValueOrData()
    //     0xa65130: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65134: ldur            x3, [fp, #-0x38]
    // 0xa65138: LoadField: r1 = r3->field_f
    //     0xa65138: ldur            w1, [x3, #0xf]
    // 0xa6513c: DecompressPointer r1
    //     0xa6513c: add             x1, x1, HEAP, lsl #32
    // 0xa65140: cmp             w1, w0
    // 0xa65144: b.ne            #0xa65150
    // 0xa65148: r4 = Null
    //     0xa65148: mov             x4, NULL
    // 0xa6514c: b               #0xa65154
    // 0xa65150: mov             x4, x0
    // 0xa65154: mov             x0, x4
    // 0xa65158: stur            x4, [fp, #-0x40]
    // 0xa6515c: r2 = Null
    //     0xa6515c: mov             x2, NULL
    // 0xa65160: r1 = Null
    //     0xa65160: mov             x1, NULL
    // 0xa65164: r4 = 60
    //     0xa65164: movz            x4, #0x3c
    // 0xa65168: branchIfSmi(r0, 0xa65174)
    //     0xa65168: tbz             w0, #0, #0xa65174
    // 0xa6516c: r4 = LoadClassIdInstr(r0)
    //     0xa6516c: ldur            x4, [x0, #-1]
    //     0xa65170: ubfx            x4, x4, #0xc, #0x14
    // 0xa65174: sub             x4, x4, #0x5e
    // 0xa65178: cmp             x4, #1
    // 0xa6517c: b.ls            #0xa65190
    // 0xa65180: r8 = String
    //     0xa65180: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa65184: r3 = Null
    //     0xa65184: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e88] Null
    //     0xa65188: ldr             x3, [x3, #0xe88]
    // 0xa6518c: r0 = String()
    //     0xa6518c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65190: ldur            x1, [fp, #-0x38]
    // 0xa65194: r2 = 6
    //     0xa65194: movz            x2, #0x6
    // 0xa65198: r0 = _getValueOrData()
    //     0xa65198: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6519c: ldur            x3, [fp, #-0x38]
    // 0xa651a0: LoadField: r1 = r3->field_f
    //     0xa651a0: ldur            w1, [x3, #0xf]
    // 0xa651a4: DecompressPointer r1
    //     0xa651a4: add             x1, x1, HEAP, lsl #32
    // 0xa651a8: cmp             w1, w0
    // 0xa651ac: b.ne            #0xa651b8
    // 0xa651b0: r4 = Null
    //     0xa651b0: mov             x4, NULL
    // 0xa651b4: b               #0xa651bc
    // 0xa651b8: mov             x4, x0
    // 0xa651bc: mov             x0, x4
    // 0xa651c0: stur            x4, [fp, #-0x48]
    // 0xa651c4: r2 = Null
    //     0xa651c4: mov             x2, NULL
    // 0xa651c8: r1 = Null
    //     0xa651c8: mov             x1, NULL
    // 0xa651cc: r4 = 60
    //     0xa651cc: movz            x4, #0x3c
    // 0xa651d0: branchIfSmi(r0, 0xa651dc)
    //     0xa651d0: tbz             w0, #0, #0xa651dc
    // 0xa651d4: r4 = LoadClassIdInstr(r0)
    //     0xa651d4: ldur            x4, [x0, #-1]
    //     0xa651d8: ubfx            x4, x4, #0xc, #0x14
    // 0xa651dc: sub             x4, x4, #0x5e
    // 0xa651e0: cmp             x4, #1
    // 0xa651e4: b.ls            #0xa651f8
    // 0xa651e8: r8 = String
    //     0xa651e8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa651ec: r3 = Null
    //     0xa651ec: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e98] Null
    //     0xa651f0: ldr             x3, [x3, #0xe98]
    // 0xa651f4: r0 = String()
    //     0xa651f4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa651f8: ldur            x1, [fp, #-0x38]
    // 0xa651fc: r2 = 8
    //     0xa651fc: movz            x2, #0x8
    // 0xa65200: r0 = _getValueOrData()
    //     0xa65200: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65204: mov             x1, x0
    // 0xa65208: ldur            x0, [fp, #-0x38]
    // 0xa6520c: LoadField: r2 = r0->field_f
    //     0xa6520c: ldur            w2, [x0, #0xf]
    // 0xa65210: DecompressPointer r2
    //     0xa65210: add             x2, x2, HEAP, lsl #32
    // 0xa65214: cmp             w2, w1
    // 0xa65218: b.ne            #0xa65224
    // 0xa6521c: r3 = Null
    //     0xa6521c: mov             x3, NULL
    // 0xa65220: b               #0xa65228
    // 0xa65224: mov             x3, x1
    // 0xa65228: mov             x0, x3
    // 0xa6522c: stur            x3, [fp, #-0x38]
    // 0xa65230: r2 = Null
    //     0xa65230: mov             x2, NULL
    // 0xa65234: r1 = Null
    //     0xa65234: mov             x1, NULL
    // 0xa65238: r8 = Map?
    //     0xa65238: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0xa6523c: ldr             x8, [x8, #0x590]
    // 0xa65240: r3 = Null
    //     0xa65240: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ea8] Null
    //     0xa65244: ldr             x3, [x3, #0xea8]
    // 0xa65248: r0 = Map?()
    //     0xa65248: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0xa6524c: ldur            x0, [fp, #-0x38]
    // 0xa65250: cmp             w0, NULL
    // 0xa65254: b.ne            #0xa65260
    // 0xa65258: r4 = Null
    //     0xa65258: mov             x4, NULL
    // 0xa6525c: b               #0xa65288
    // 0xa65260: r1 = LoadClassIdInstr(r0)
    //     0xa65260: ldur            x1, [x0, #-1]
    //     0xa65264: ubfx            x1, x1, #0xc, #0x14
    // 0xa65268: r16 = <String, dynamic>
    //     0xa65268: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa6526c: stp             x0, x16, [SP]
    // 0xa65270: mov             x0, x1
    // 0xa65274: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa65274: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa65278: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa65278: add             lr, x0, #0x5f4
    //     0xa6527c: ldr             lr, [x21, lr, lsl #3]
    //     0xa65280: blr             lr
    // 0xa65284: mov             x4, x0
    // 0xa65288: ldur            x3, [fp, #-0x18]
    // 0xa6528c: ldur            x2, [fp, #-0x20]
    // 0xa65290: ldur            x1, [fp, #-0x40]
    // 0xa65294: ldur            x0, [fp, #-0x48]
    // 0xa65298: stur            x4, [fp, #-0x38]
    // 0xa6529c: r5 = LoadInt32Instr(r3)
    //     0xa6529c: sbfx            x5, x3, #1, #0x1f
    //     0xa652a0: tbz             w3, #0, #0xa652a8
    //     0xa652a4: ldur            x5, [x3, #7]
    // 0xa652a8: stur            x5, [fp, #-8]
    // 0xa652ac: r0 = Recommendation()
    //     0xa652ac: bl              #0x8c1754  ; AllocateRecommendationStub -> Recommendation (size=0x20)
    // 0xa652b0: mov             x1, x0
    // 0xa652b4: ldur            x0, [fp, #-8]
    // 0xa652b8: StoreField: r1->field_7 = r0
    //     0xa652b8: stur            x0, [x1, #7]
    // 0xa652bc: ldur            x0, [fp, #-0x20]
    // 0xa652c0: StoreField: r1->field_f = r0
    //     0xa652c0: stur            w0, [x1, #0xf]
    // 0xa652c4: ldur            x0, [fp, #-0x40]
    // 0xa652c8: StoreField: r1->field_13 = r0
    //     0xa652c8: stur            w0, [x1, #0x13]
    // 0xa652cc: ldur            x0, [fp, #-0x48]
    // 0xa652d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa652d0: stur            w0, [x1, #0x17]
    // 0xa652d4: ldur            x0, [fp, #-0x38]
    // 0xa652d8: StoreField: r1->field_1b = r0
    //     0xa652d8: stur            w0, [x1, #0x1b]
    // 0xa652dc: mov             x0, x1
    // 0xa652e0: LeaveFrame
    //     0xa652e0: mov             SP, fp
    //     0xa652e4: ldp             fp, lr, [SP], #0x10
    // 0xa652e8: ret
    //     0xa652e8: ret             
    // 0xa652ec: r0 = RangeError()
    //     0xa652ec: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa652f0: mov             x1, x0
    // 0xa652f4: r0 = "Not enough bytes available."
    //     0xa652f4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa652f8: ldr             x0, [x0, #0x8a8]
    // 0xa652fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa652fc: stur            w0, [x1, #0x17]
    // 0xa65300: r2 = false
    //     0xa65300: add             x2, NULL, #0x30  ; false
    // 0xa65304: StoreField: r1->field_b = r2
    //     0xa65304: stur            w2, [x1, #0xb]
    // 0xa65308: mov             x0, x1
    // 0xa6530c: r0 = Throw()
    //     0xa6530c: bl              #0xec04b8  ; ThrowStub
    // 0xa65310: brk             #0
    // 0xa65314: r0 = "Not enough bytes available."
    //     0xa65314: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa65318: ldr             x0, [x0, #0x8a8]
    // 0xa6531c: r2 = false
    //     0xa6531c: add             x2, NULL, #0x30  ; false
    // 0xa65320: r0 = RangeError()
    //     0xa65320: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa65324: mov             x1, x0
    // 0xa65328: r0 = "Not enough bytes available."
    //     0xa65328: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6532c: ldr             x0, [x0, #0x8a8]
    // 0xa65330: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65330: stur            w0, [x1, #0x17]
    // 0xa65334: r0 = false
    //     0xa65334: add             x0, NULL, #0x30  ; false
    // 0xa65338: StoreField: r1->field_b = r0
    //     0xa65338: stur            w0, [x1, #0xb]
    // 0xa6533c: mov             x0, x1
    // 0xa65340: r0 = Throw()
    //     0xa65340: bl              #0xec04b8  ; ThrowStub
    // 0xa65344: brk             #0
    // 0xa65348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6534c: b               #0xa64f40
    // 0xa65350: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa65350: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa65354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65354: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65358: b               #0xa64fc8
    // 0xa6535c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa6535c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd64cc, size: 0x3ac
    // 0xbd64cc: EnterFrame
    //     0xbd64cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbd64d0: mov             fp, SP
    // 0xbd64d4: AllocStack(0x28)
    //     0xbd64d4: sub             SP, SP, #0x28
    // 0xbd64d8: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd64d8: mov             x4, x2
    //     0xbd64dc: stur            x2, [fp, #-8]
    //     0xbd64e0: stur            x3, [fp, #-0x10]
    // 0xbd64e4: CheckStackOverflow
    //     0xbd64e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd64e8: cmp             SP, x16
    //     0xbd64ec: b.ls            #0xbd6858
    // 0xbd64f0: mov             x0, x3
    // 0xbd64f4: r2 = Null
    //     0xbd64f4: mov             x2, NULL
    // 0xbd64f8: r1 = Null
    //     0xbd64f8: mov             x1, NULL
    // 0xbd64fc: r4 = 60
    //     0xbd64fc: movz            x4, #0x3c
    // 0xbd6500: branchIfSmi(r0, 0xbd650c)
    //     0xbd6500: tbz             w0, #0, #0xbd650c
    // 0xbd6504: r4 = LoadClassIdInstr(r0)
    //     0xbd6504: ldur            x4, [x0, #-1]
    //     0xbd6508: ubfx            x4, x4, #0xc, #0x14
    // 0xbd650c: cmp             x4, #0x466
    // 0xbd6510: b.eq            #0xbd6528
    // 0xbd6514: r8 = Recommendation
    //     0xbd6514: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b190] Type: Recommendation
    //     0xbd6518: ldr             x8, [x8, #0x190]
    // 0xbd651c: r3 = Null
    //     0xbd651c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b198] Null
    //     0xbd6520: ldr             x3, [x3, #0x198]
    // 0xbd6524: r0 = Recommendation()
    //     0xbd6524: bl              #0x835f9c  ; IsType_Recommendation_Stub
    // 0xbd6528: ldur            x0, [fp, #-8]
    // 0xbd652c: LoadField: r1 = r0->field_b
    //     0xbd652c: ldur            w1, [x0, #0xb]
    // 0xbd6530: DecompressPointer r1
    //     0xbd6530: add             x1, x1, HEAP, lsl #32
    // 0xbd6534: LoadField: r2 = r1->field_13
    //     0xbd6534: ldur            w2, [x1, #0x13]
    // 0xbd6538: LoadField: r1 = r0->field_13
    //     0xbd6538: ldur            x1, [x0, #0x13]
    // 0xbd653c: r3 = LoadInt32Instr(r2)
    //     0xbd653c: sbfx            x3, x2, #1, #0x1f
    // 0xbd6540: sub             x2, x3, x1
    // 0xbd6544: cmp             x2, #1
    // 0xbd6548: b.ge            #0xbd6558
    // 0xbd654c: mov             x1, x0
    // 0xbd6550: r2 = 1
    //     0xbd6550: movz            x2, #0x1
    // 0xbd6554: r0 = _increaseBufferSize()
    //     0xbd6554: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6558: ldur            x3, [fp, #-8]
    // 0xbd655c: r2 = 5
    //     0xbd655c: movz            x2, #0x5
    // 0xbd6560: LoadField: r4 = r3->field_b
    //     0xbd6560: ldur            w4, [x3, #0xb]
    // 0xbd6564: DecompressPointer r4
    //     0xbd6564: add             x4, x4, HEAP, lsl #32
    // 0xbd6568: LoadField: r5 = r3->field_13
    //     0xbd6568: ldur            x5, [x3, #0x13]
    // 0xbd656c: add             x6, x5, #1
    // 0xbd6570: StoreField: r3->field_13 = r6
    //     0xbd6570: stur            x6, [x3, #0x13]
    // 0xbd6574: LoadField: r0 = r4->field_13
    //     0xbd6574: ldur            w0, [x4, #0x13]
    // 0xbd6578: r7 = LoadInt32Instr(r0)
    //     0xbd6578: sbfx            x7, x0, #1, #0x1f
    // 0xbd657c: mov             x0, x7
    // 0xbd6580: mov             x1, x5
    // 0xbd6584: cmp             x1, x0
    // 0xbd6588: b.hs            #0xbd6860
    // 0xbd658c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd658c: add             x0, x4, x5
    //     0xbd6590: strb            w2, [x0, #0x17]
    // 0xbd6594: sub             x0, x7, x6
    // 0xbd6598: cmp             x0, #1
    // 0xbd659c: b.ge            #0xbd65ac
    // 0xbd65a0: mov             x1, x3
    // 0xbd65a4: r2 = 1
    //     0xbd65a4: movz            x2, #0x1
    // 0xbd65a8: r0 = _increaseBufferSize()
    //     0xbd65a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd65ac: ldur            x2, [fp, #-8]
    // 0xbd65b0: ldur            x3, [fp, #-0x10]
    // 0xbd65b4: LoadField: r4 = r2->field_b
    //     0xbd65b4: ldur            w4, [x2, #0xb]
    // 0xbd65b8: DecompressPointer r4
    //     0xbd65b8: add             x4, x4, HEAP, lsl #32
    // 0xbd65bc: LoadField: r5 = r2->field_13
    //     0xbd65bc: ldur            x5, [x2, #0x13]
    // 0xbd65c0: add             x0, x5, #1
    // 0xbd65c4: StoreField: r2->field_13 = r0
    //     0xbd65c4: stur            x0, [x2, #0x13]
    // 0xbd65c8: LoadField: r0 = r4->field_13
    //     0xbd65c8: ldur            w0, [x4, #0x13]
    // 0xbd65cc: r1 = LoadInt32Instr(r0)
    //     0xbd65cc: sbfx            x1, x0, #1, #0x1f
    // 0xbd65d0: mov             x0, x1
    // 0xbd65d4: mov             x1, x5
    // 0xbd65d8: cmp             x1, x0
    // 0xbd65dc: b.hs            #0xbd6864
    // 0xbd65e0: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd65e0: add             x0, x4, x5
    //     0xbd65e4: strb            wzr, [x0, #0x17]
    // 0xbd65e8: LoadField: r4 = r3->field_7
    //     0xbd65e8: ldur            x4, [x3, #7]
    // 0xbd65ec: r0 = BoxInt64Instr(r4)
    //     0xbd65ec: sbfiz           x0, x4, #1, #0x1f
    //     0xbd65f0: cmp             x4, x0, asr #1
    //     0xbd65f4: b.eq            #0xbd6600
    //     0xbd65f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd65fc: stur            x4, [x0, #7]
    // 0xbd6600: r16 = <int>
    //     0xbd6600: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6604: stp             x2, x16, [SP, #8]
    // 0xbd6608: str             x0, [SP]
    // 0xbd660c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd660c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6610: r0 = write()
    //     0xbd6610: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6614: ldur            x0, [fp, #-8]
    // 0xbd6618: LoadField: r1 = r0->field_b
    //     0xbd6618: ldur            w1, [x0, #0xb]
    // 0xbd661c: DecompressPointer r1
    //     0xbd661c: add             x1, x1, HEAP, lsl #32
    // 0xbd6620: LoadField: r2 = r1->field_13
    //     0xbd6620: ldur            w2, [x1, #0x13]
    // 0xbd6624: LoadField: r1 = r0->field_13
    //     0xbd6624: ldur            x1, [x0, #0x13]
    // 0xbd6628: r3 = LoadInt32Instr(r2)
    //     0xbd6628: sbfx            x3, x2, #1, #0x1f
    // 0xbd662c: sub             x2, x3, x1
    // 0xbd6630: cmp             x2, #1
    // 0xbd6634: b.ge            #0xbd6644
    // 0xbd6638: mov             x1, x0
    // 0xbd663c: r2 = 1
    //     0xbd663c: movz            x2, #0x1
    // 0xbd6640: r0 = _increaseBufferSize()
    //     0xbd6640: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6644: ldur            x2, [fp, #-8]
    // 0xbd6648: ldur            x3, [fp, #-0x10]
    // 0xbd664c: r4 = 1
    //     0xbd664c: movz            x4, #0x1
    // 0xbd6650: LoadField: r5 = r2->field_b
    //     0xbd6650: ldur            w5, [x2, #0xb]
    // 0xbd6654: DecompressPointer r5
    //     0xbd6654: add             x5, x5, HEAP, lsl #32
    // 0xbd6658: LoadField: r6 = r2->field_13
    //     0xbd6658: ldur            x6, [x2, #0x13]
    // 0xbd665c: add             x0, x6, #1
    // 0xbd6660: StoreField: r2->field_13 = r0
    //     0xbd6660: stur            x0, [x2, #0x13]
    // 0xbd6664: LoadField: r0 = r5->field_13
    //     0xbd6664: ldur            w0, [x5, #0x13]
    // 0xbd6668: r1 = LoadInt32Instr(r0)
    //     0xbd6668: sbfx            x1, x0, #1, #0x1f
    // 0xbd666c: mov             x0, x1
    // 0xbd6670: mov             x1, x6
    // 0xbd6674: cmp             x1, x0
    // 0xbd6678: b.hs            #0xbd6868
    // 0xbd667c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd667c: add             x0, x5, x6
    //     0xbd6680: strb            w4, [x0, #0x17]
    // 0xbd6684: LoadField: r0 = r3->field_f
    //     0xbd6684: ldur            w0, [x3, #0xf]
    // 0xbd6688: DecompressPointer r0
    //     0xbd6688: add             x0, x0, HEAP, lsl #32
    // 0xbd668c: r16 = <String>
    //     0xbd668c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd6690: stp             x2, x16, [SP, #8]
    // 0xbd6694: str             x0, [SP]
    // 0xbd6698: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6698: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd669c: r0 = write()
    //     0xbd669c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd66a0: ldur            x0, [fp, #-8]
    // 0xbd66a4: LoadField: r1 = r0->field_b
    //     0xbd66a4: ldur            w1, [x0, #0xb]
    // 0xbd66a8: DecompressPointer r1
    //     0xbd66a8: add             x1, x1, HEAP, lsl #32
    // 0xbd66ac: LoadField: r2 = r1->field_13
    //     0xbd66ac: ldur            w2, [x1, #0x13]
    // 0xbd66b0: LoadField: r1 = r0->field_13
    //     0xbd66b0: ldur            x1, [x0, #0x13]
    // 0xbd66b4: r3 = LoadInt32Instr(r2)
    //     0xbd66b4: sbfx            x3, x2, #1, #0x1f
    // 0xbd66b8: sub             x2, x3, x1
    // 0xbd66bc: cmp             x2, #1
    // 0xbd66c0: b.ge            #0xbd66d0
    // 0xbd66c4: mov             x1, x0
    // 0xbd66c8: r2 = 1
    //     0xbd66c8: movz            x2, #0x1
    // 0xbd66cc: r0 = _increaseBufferSize()
    //     0xbd66cc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd66d0: ldur            x2, [fp, #-8]
    // 0xbd66d4: ldur            x3, [fp, #-0x10]
    // 0xbd66d8: r4 = 2
    //     0xbd66d8: movz            x4, #0x2
    // 0xbd66dc: LoadField: r5 = r2->field_b
    //     0xbd66dc: ldur            w5, [x2, #0xb]
    // 0xbd66e0: DecompressPointer r5
    //     0xbd66e0: add             x5, x5, HEAP, lsl #32
    // 0xbd66e4: LoadField: r6 = r2->field_13
    //     0xbd66e4: ldur            x6, [x2, #0x13]
    // 0xbd66e8: add             x0, x6, #1
    // 0xbd66ec: StoreField: r2->field_13 = r0
    //     0xbd66ec: stur            x0, [x2, #0x13]
    // 0xbd66f0: LoadField: r0 = r5->field_13
    //     0xbd66f0: ldur            w0, [x5, #0x13]
    // 0xbd66f4: r1 = LoadInt32Instr(r0)
    //     0xbd66f4: sbfx            x1, x0, #1, #0x1f
    // 0xbd66f8: mov             x0, x1
    // 0xbd66fc: mov             x1, x6
    // 0xbd6700: cmp             x1, x0
    // 0xbd6704: b.hs            #0xbd686c
    // 0xbd6708: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6708: add             x0, x5, x6
    //     0xbd670c: strb            w4, [x0, #0x17]
    // 0xbd6710: LoadField: r0 = r3->field_13
    //     0xbd6710: ldur            w0, [x3, #0x13]
    // 0xbd6714: DecompressPointer r0
    //     0xbd6714: add             x0, x0, HEAP, lsl #32
    // 0xbd6718: r16 = <String>
    //     0xbd6718: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd671c: stp             x2, x16, [SP, #8]
    // 0xbd6720: str             x0, [SP]
    // 0xbd6724: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6724: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6728: r0 = write()
    //     0xbd6728: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd672c: ldur            x0, [fp, #-8]
    // 0xbd6730: LoadField: r1 = r0->field_b
    //     0xbd6730: ldur            w1, [x0, #0xb]
    // 0xbd6734: DecompressPointer r1
    //     0xbd6734: add             x1, x1, HEAP, lsl #32
    // 0xbd6738: LoadField: r2 = r1->field_13
    //     0xbd6738: ldur            w2, [x1, #0x13]
    // 0xbd673c: LoadField: r1 = r0->field_13
    //     0xbd673c: ldur            x1, [x0, #0x13]
    // 0xbd6740: r3 = LoadInt32Instr(r2)
    //     0xbd6740: sbfx            x3, x2, #1, #0x1f
    // 0xbd6744: sub             x2, x3, x1
    // 0xbd6748: cmp             x2, #1
    // 0xbd674c: b.ge            #0xbd675c
    // 0xbd6750: mov             x1, x0
    // 0xbd6754: r2 = 1
    //     0xbd6754: movz            x2, #0x1
    // 0xbd6758: r0 = _increaseBufferSize()
    //     0xbd6758: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd675c: ldur            x2, [fp, #-8]
    // 0xbd6760: ldur            x3, [fp, #-0x10]
    // 0xbd6764: r4 = 3
    //     0xbd6764: movz            x4, #0x3
    // 0xbd6768: LoadField: r5 = r2->field_b
    //     0xbd6768: ldur            w5, [x2, #0xb]
    // 0xbd676c: DecompressPointer r5
    //     0xbd676c: add             x5, x5, HEAP, lsl #32
    // 0xbd6770: LoadField: r6 = r2->field_13
    //     0xbd6770: ldur            x6, [x2, #0x13]
    // 0xbd6774: add             x0, x6, #1
    // 0xbd6778: StoreField: r2->field_13 = r0
    //     0xbd6778: stur            x0, [x2, #0x13]
    // 0xbd677c: LoadField: r0 = r5->field_13
    //     0xbd677c: ldur            w0, [x5, #0x13]
    // 0xbd6780: r1 = LoadInt32Instr(r0)
    //     0xbd6780: sbfx            x1, x0, #1, #0x1f
    // 0xbd6784: mov             x0, x1
    // 0xbd6788: mov             x1, x6
    // 0xbd678c: cmp             x1, x0
    // 0xbd6790: b.hs            #0xbd6870
    // 0xbd6794: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6794: add             x0, x5, x6
    //     0xbd6798: strb            w4, [x0, #0x17]
    // 0xbd679c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbd679c: ldur            w0, [x3, #0x17]
    // 0xbd67a0: DecompressPointer r0
    //     0xbd67a0: add             x0, x0, HEAP, lsl #32
    // 0xbd67a4: r16 = <String>
    //     0xbd67a4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd67a8: stp             x2, x16, [SP, #8]
    // 0xbd67ac: str             x0, [SP]
    // 0xbd67b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd67b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd67b4: r0 = write()
    //     0xbd67b4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd67b8: ldur            x0, [fp, #-8]
    // 0xbd67bc: LoadField: r1 = r0->field_b
    //     0xbd67bc: ldur            w1, [x0, #0xb]
    // 0xbd67c0: DecompressPointer r1
    //     0xbd67c0: add             x1, x1, HEAP, lsl #32
    // 0xbd67c4: LoadField: r2 = r1->field_13
    //     0xbd67c4: ldur            w2, [x1, #0x13]
    // 0xbd67c8: LoadField: r1 = r0->field_13
    //     0xbd67c8: ldur            x1, [x0, #0x13]
    // 0xbd67cc: r3 = LoadInt32Instr(r2)
    //     0xbd67cc: sbfx            x3, x2, #1, #0x1f
    // 0xbd67d0: sub             x2, x3, x1
    // 0xbd67d4: cmp             x2, #1
    // 0xbd67d8: b.ge            #0xbd67e8
    // 0xbd67dc: mov             x1, x0
    // 0xbd67e0: r2 = 1
    //     0xbd67e0: movz            x2, #0x1
    // 0xbd67e4: r0 = _increaseBufferSize()
    //     0xbd67e4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd67e8: ldur            x2, [fp, #-8]
    // 0xbd67ec: ldur            x3, [fp, #-0x10]
    // 0xbd67f0: r4 = 4
    //     0xbd67f0: movz            x4, #0x4
    // 0xbd67f4: LoadField: r5 = r2->field_b
    //     0xbd67f4: ldur            w5, [x2, #0xb]
    // 0xbd67f8: DecompressPointer r5
    //     0xbd67f8: add             x5, x5, HEAP, lsl #32
    // 0xbd67fc: LoadField: r6 = r2->field_13
    //     0xbd67fc: ldur            x6, [x2, #0x13]
    // 0xbd6800: add             x0, x6, #1
    // 0xbd6804: StoreField: r2->field_13 = r0
    //     0xbd6804: stur            x0, [x2, #0x13]
    // 0xbd6808: LoadField: r0 = r5->field_13
    //     0xbd6808: ldur            w0, [x5, #0x13]
    // 0xbd680c: r1 = LoadInt32Instr(r0)
    //     0xbd680c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6810: mov             x0, x1
    // 0xbd6814: mov             x1, x6
    // 0xbd6818: cmp             x1, x0
    // 0xbd681c: b.hs            #0xbd6874
    // 0xbd6820: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6820: add             x0, x5, x6
    //     0xbd6824: strb            w4, [x0, #0x17]
    // 0xbd6828: LoadField: r0 = r3->field_1b
    //     0xbd6828: ldur            w0, [x3, #0x1b]
    // 0xbd682c: DecompressPointer r0
    //     0xbd682c: add             x0, x0, HEAP, lsl #32
    // 0xbd6830: r16 = <Map<String, dynamic>?>
    //     0xbd6830: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b180] TypeArguments: <Map<String, dynamic>?>
    //     0xbd6834: ldr             x16, [x16, #0x180]
    // 0xbd6838: stp             x2, x16, [SP, #8]
    // 0xbd683c: str             x0, [SP]
    // 0xbd6840: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6840: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6844: r0 = write()
    //     0xbd6844: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6848: r0 = Null
    //     0xbd6848: mov             x0, NULL
    // 0xbd684c: LeaveFrame
    //     0xbd684c: mov             SP, fp
    //     0xbd6850: ldp             fp, lr, [SP], #0x10
    // 0xbd6854: ret
    //     0xbd6854: ret             
    // 0xbd6858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6858: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd685c: b               #0xbd64f0
    // 0xbd6860: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6860: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6864: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6864: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6868: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6868: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd686c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd686c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6870: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6874: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf02d0, size: 0x24
    // 0xbf02d0: r1 = 66
    //     0xbf02d0: movz            x1, #0x42
    // 0xbf02d4: r16 = LoadInt32Instr(r1)
    //     0xbf02d4: sbfx            x16, x1, #1, #0x1f
    // 0xbf02d8: r17 = 11601
    //     0xbf02d8: movz            x17, #0x2d51
    // 0xbf02dc: mul             x0, x16, x17
    // 0xbf02e0: umulh           x16, x16, x17
    // 0xbf02e4: eor             x0, x0, x16
    // 0xbf02e8: r0 = 0
    //     0xbf02e8: eor             x0, x0, x0, lsr #32
    // 0xbf02ec: ubfiz           x0, x0, #1, #0x1e
    // 0xbf02f0: ret
    //     0xbf02f0: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76ca4, size: 0x9c
    // 0xd76ca4: EnterFrame
    //     0xd76ca4: stp             fp, lr, [SP, #-0x10]!
    //     0xd76ca8: mov             fp, SP
    // 0xd76cac: AllocStack(0x10)
    //     0xd76cac: sub             SP, SP, #0x10
    // 0xd76cb0: CheckStackOverflow
    //     0xd76cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76cb4: cmp             SP, x16
    //     0xd76cb8: b.ls            #0xd76d38
    // 0xd76cbc: ldr             x0, [fp, #0x10]
    // 0xd76cc0: cmp             w0, NULL
    // 0xd76cc4: b.ne            #0xd76cd8
    // 0xd76cc8: r0 = false
    //     0xd76cc8: add             x0, NULL, #0x30  ; false
    // 0xd76ccc: LeaveFrame
    //     0xd76ccc: mov             SP, fp
    //     0xd76cd0: ldp             fp, lr, [SP], #0x10
    // 0xd76cd4: ret
    //     0xd76cd4: ret             
    // 0xd76cd8: ldr             x1, [fp, #0x18]
    // 0xd76cdc: cmp             w1, w0
    // 0xd76ce0: b.ne            #0xd76cec
    // 0xd76ce4: r0 = true
    //     0xd76ce4: add             x0, NULL, #0x20  ; true
    // 0xd76ce8: b               #0xd76d2c
    // 0xd76cec: r1 = 60
    //     0xd76cec: movz            x1, #0x3c
    // 0xd76cf0: branchIfSmi(r0, 0xd76cfc)
    //     0xd76cf0: tbz             w0, #0, #0xd76cfc
    // 0xd76cf4: r1 = LoadClassIdInstr(r0)
    //     0xd76cf4: ldur            x1, [x0, #-1]
    //     0xd76cf8: ubfx            x1, x1, #0xc, #0x14
    // 0xd76cfc: cmp             x1, #0x672
    // 0xd76d00: b.ne            #0xd76d28
    // 0xd76d04: r16 = RecommendationAdapter
    //     0xd76d04: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b188] Type: RecommendationAdapter
    //     0xd76d08: ldr             x16, [x16, #0x188]
    // 0xd76d0c: r30 = RecommendationAdapter
    //     0xd76d0c: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b188] Type: RecommendationAdapter
    //     0xd76d10: ldr             lr, [lr, #0x188]
    // 0xd76d14: stp             lr, x16, [SP]
    // 0xd76d18: r0 = ==()
    //     0xd76d18: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76d1c: tbnz            w0, #4, #0xd76d28
    // 0xd76d20: r0 = true
    //     0xd76d20: add             x0, NULL, #0x20  ; true
    // 0xd76d24: b               #0xd76d2c
    // 0xd76d28: r0 = false
    //     0xd76d28: add             x0, NULL, #0x30  ; false
    // 0xd76d2c: LeaveFrame
    //     0xd76d2c: mov             SP, fp
    //     0xd76d30: ldp             fp, lr, [SP], #0x10
    // 0xd76d34: ret
    //     0xd76d34: ret             
    // 0xd76d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76d38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76d3c: b               #0xd76cbc
  }
}
