// lib: , url: package:nuonline/app/data/models/regency.dart

// class id: 1050048, size: 0x8
class :: {
}

// class id: 1125, size: 0x40, field offset: 0x8
class Regency extends Object {
}

// class id: 1649, size: 0x14, field offset: 0xc
class RegencyAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa65360, size: 0x58c
    // 0xa65360: EnterFrame
    //     0xa65360: stp             fp, lr, [SP, #-0x10]!
    //     0xa65364: mov             fp, SP
    // 0xa65368: AllocStack(0x70)
    //     0xa65368: sub             SP, SP, #0x70
    // 0xa6536c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa6536c: stur            x2, [fp, #-0x20]
    // 0xa65370: CheckStackOverflow
    //     0xa65370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65374: cmp             SP, x16
    //     0xa65378: b.ls            #0xa658d4
    // 0xa6537c: LoadField: r3 = r2->field_23
    //     0xa6537c: ldur            x3, [x2, #0x23]
    // 0xa65380: add             x0, x3, #1
    // 0xa65384: LoadField: r1 = r2->field_1b
    //     0xa65384: ldur            x1, [x2, #0x1b]
    // 0xa65388: cmp             x0, x1
    // 0xa6538c: b.gt            #0xa65878
    // 0xa65390: LoadField: r4 = r2->field_7
    //     0xa65390: ldur            w4, [x2, #7]
    // 0xa65394: DecompressPointer r4
    //     0xa65394: add             x4, x4, HEAP, lsl #32
    // 0xa65398: stur            x4, [fp, #-0x18]
    // 0xa6539c: StoreField: r2->field_23 = r0
    //     0xa6539c: stur            x0, [x2, #0x23]
    // 0xa653a0: LoadField: r0 = r4->field_13
    //     0xa653a0: ldur            w0, [x4, #0x13]
    // 0xa653a4: r5 = LoadInt32Instr(r0)
    //     0xa653a4: sbfx            x5, x0, #1, #0x1f
    // 0xa653a8: mov             x0, x5
    // 0xa653ac: mov             x1, x3
    // 0xa653b0: stur            x5, [fp, #-0x10]
    // 0xa653b4: cmp             x1, x0
    // 0xa653b8: b.hs            #0xa658dc
    // 0xa653bc: LoadField: r0 = r4->field_7
    //     0xa653bc: ldur            x0, [x4, #7]
    // 0xa653c0: ldrb            w1, [x0, x3]
    // 0xa653c4: stur            x1, [fp, #-8]
    // 0xa653c8: r16 = <int, dynamic>
    //     0xa653c8: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa653cc: ldr             x16, [x16, #0xac0]
    // 0xa653d0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa653d4: stp             lr, x16, [SP]
    // 0xa653d8: r0 = Map._fromLiteral()
    //     0xa653d8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa653dc: mov             x2, x0
    // 0xa653e0: stur            x2, [fp, #-0x38]
    // 0xa653e4: r6 = 0
    //     0xa653e4: movz            x6, #0
    // 0xa653e8: ldur            x3, [fp, #-0x20]
    // 0xa653ec: ldur            x4, [fp, #-0x18]
    // 0xa653f0: ldur            x5, [fp, #-8]
    // 0xa653f4: stur            x6, [fp, #-0x30]
    // 0xa653f8: CheckStackOverflow
    //     0xa653f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa653fc: cmp             SP, x16
    //     0xa65400: b.ls            #0xa658e0
    // 0xa65404: cmp             x6, x5
    // 0xa65408: b.ge            #0xa65494
    // 0xa6540c: LoadField: r7 = r3->field_23
    //     0xa6540c: ldur            x7, [x3, #0x23]
    // 0xa65410: add             x0, x7, #1
    // 0xa65414: LoadField: r1 = r3->field_1b
    //     0xa65414: ldur            x1, [x3, #0x1b]
    // 0xa65418: cmp             x0, x1
    // 0xa6541c: b.gt            #0xa658a0
    // 0xa65420: StoreField: r3->field_23 = r0
    //     0xa65420: stur            x0, [x3, #0x23]
    // 0xa65424: ldur            x0, [fp, #-0x10]
    // 0xa65428: mov             x1, x7
    // 0xa6542c: cmp             x1, x0
    // 0xa65430: b.hs            #0xa658e8
    // 0xa65434: LoadField: r0 = r4->field_7
    //     0xa65434: ldur            x0, [x4, #7]
    // 0xa65438: ldrb            w8, [x0, x7]
    // 0xa6543c: mov             x1, x3
    // 0xa65440: stur            x8, [fp, #-0x28]
    // 0xa65444: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa65444: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa65448: r0 = read()
    //     0xa65448: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa6544c: mov             x1, x0
    // 0xa65450: ldur            x0, [fp, #-0x28]
    // 0xa65454: lsl             x2, x0, #1
    // 0xa65458: r16 = LoadInt32Instr(r2)
    //     0xa65458: sbfx            x16, x2, #1, #0x1f
    // 0xa6545c: r17 = 11601
    //     0xa6545c: movz            x17, #0x2d51
    // 0xa65460: mul             x0, x16, x17
    // 0xa65464: umulh           x16, x16, x17
    // 0xa65468: eor             x0, x0, x16
    // 0xa6546c: r0 = 0
    //     0xa6546c: eor             x0, x0, x0, lsr #32
    // 0xa65470: ubfiz           x0, x0, #1, #0x1e
    // 0xa65474: r5 = LoadInt32Instr(r0)
    //     0xa65474: sbfx            x5, x0, #1, #0x1f
    // 0xa65478: mov             x3, x1
    // 0xa6547c: ldur            x1, [fp, #-0x38]
    // 0xa65480: r0 = _set()
    //     0xa65480: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa65484: ldur            x0, [fp, #-0x30]
    // 0xa65488: add             x6, x0, #1
    // 0xa6548c: ldur            x2, [fp, #-0x38]
    // 0xa65490: b               #0xa653e8
    // 0xa65494: mov             x0, x2
    // 0xa65498: mov             x1, x0
    // 0xa6549c: r2 = 0
    //     0xa6549c: movz            x2, #0
    // 0xa654a0: r0 = _getValueOrData()
    //     0xa654a0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa654a4: ldur            x3, [fp, #-0x38]
    // 0xa654a8: LoadField: r1 = r3->field_f
    //     0xa654a8: ldur            w1, [x3, #0xf]
    // 0xa654ac: DecompressPointer r1
    //     0xa654ac: add             x1, x1, HEAP, lsl #32
    // 0xa654b0: cmp             w1, w0
    // 0xa654b4: b.ne            #0xa654c0
    // 0xa654b8: r4 = Null
    //     0xa654b8: mov             x4, NULL
    // 0xa654bc: b               #0xa654c4
    // 0xa654c0: mov             x4, x0
    // 0xa654c4: mov             x0, x4
    // 0xa654c8: stur            x4, [fp, #-0x18]
    // 0xa654cc: r2 = Null
    //     0xa654cc: mov             x2, NULL
    // 0xa654d0: r1 = Null
    //     0xa654d0: mov             x1, NULL
    // 0xa654d4: branchIfSmi(r0, 0xa654fc)
    //     0xa654d4: tbz             w0, #0, #0xa654fc
    // 0xa654d8: r4 = LoadClassIdInstr(r0)
    //     0xa654d8: ldur            x4, [x0, #-1]
    //     0xa654dc: ubfx            x4, x4, #0xc, #0x14
    // 0xa654e0: sub             x4, x4, #0x3c
    // 0xa654e4: cmp             x4, #1
    // 0xa654e8: b.ls            #0xa654fc
    // 0xa654ec: r8 = int
    //     0xa654ec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa654f0: r3 = Null
    //     0xa654f0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21210] Null
    //     0xa654f4: ldr             x3, [x3, #0x210]
    // 0xa654f8: r0 = int()
    //     0xa654f8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa654fc: ldur            x1, [fp, #-0x38]
    // 0xa65500: r2 = 2
    //     0xa65500: movz            x2, #0x2
    // 0xa65504: r0 = _getValueOrData()
    //     0xa65504: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65508: ldur            x3, [fp, #-0x38]
    // 0xa6550c: LoadField: r1 = r3->field_f
    //     0xa6550c: ldur            w1, [x3, #0xf]
    // 0xa65510: DecompressPointer r1
    //     0xa65510: add             x1, x1, HEAP, lsl #32
    // 0xa65514: cmp             w1, w0
    // 0xa65518: b.ne            #0xa65524
    // 0xa6551c: r4 = Null
    //     0xa6551c: mov             x4, NULL
    // 0xa65520: b               #0xa65528
    // 0xa65524: mov             x4, x0
    // 0xa65528: mov             x0, x4
    // 0xa6552c: stur            x4, [fp, #-0x20]
    // 0xa65530: r2 = Null
    //     0xa65530: mov             x2, NULL
    // 0xa65534: r1 = Null
    //     0xa65534: mov             x1, NULL
    // 0xa65538: r4 = 60
    //     0xa65538: movz            x4, #0x3c
    // 0xa6553c: branchIfSmi(r0, 0xa65548)
    //     0xa6553c: tbz             w0, #0, #0xa65548
    // 0xa65540: r4 = LoadClassIdInstr(r0)
    //     0xa65540: ldur            x4, [x0, #-1]
    //     0xa65544: ubfx            x4, x4, #0xc, #0x14
    // 0xa65548: sub             x4, x4, #0x5e
    // 0xa6554c: cmp             x4, #1
    // 0xa65550: b.ls            #0xa65564
    // 0xa65554: r8 = String
    //     0xa65554: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa65558: r3 = Null
    //     0xa65558: add             x3, PP, #0x21, lsl #12  ; [pp+0x21220] Null
    //     0xa6555c: ldr             x3, [x3, #0x220]
    // 0xa65560: r0 = String()
    //     0xa65560: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65564: ldur            x1, [fp, #-0x38]
    // 0xa65568: r2 = 4
    //     0xa65568: movz            x2, #0x4
    // 0xa6556c: r0 = _getValueOrData()
    //     0xa6556c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65570: ldur            x3, [fp, #-0x38]
    // 0xa65574: LoadField: r1 = r3->field_f
    //     0xa65574: ldur            w1, [x3, #0xf]
    // 0xa65578: DecompressPointer r1
    //     0xa65578: add             x1, x1, HEAP, lsl #32
    // 0xa6557c: cmp             w1, w0
    // 0xa65580: b.ne            #0xa6558c
    // 0xa65584: r4 = Null
    //     0xa65584: mov             x4, NULL
    // 0xa65588: b               #0xa65590
    // 0xa6558c: mov             x4, x0
    // 0xa65590: mov             x0, x4
    // 0xa65594: stur            x4, [fp, #-0x40]
    // 0xa65598: r2 = Null
    //     0xa65598: mov             x2, NULL
    // 0xa6559c: r1 = Null
    //     0xa6559c: mov             x1, NULL
    // 0xa655a0: branchIfSmi(r0, 0xa655c8)
    //     0xa655a0: tbz             w0, #0, #0xa655c8
    // 0xa655a4: r4 = LoadClassIdInstr(r0)
    //     0xa655a4: ldur            x4, [x0, #-1]
    //     0xa655a8: ubfx            x4, x4, #0xc, #0x14
    // 0xa655ac: sub             x4, x4, #0x3c
    // 0xa655b0: cmp             x4, #1
    // 0xa655b4: b.ls            #0xa655c8
    // 0xa655b8: r8 = int
    //     0xa655b8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa655bc: r3 = Null
    //     0xa655bc: add             x3, PP, #0x21, lsl #12  ; [pp+0x21230] Null
    //     0xa655c0: ldr             x3, [x3, #0x230]
    // 0xa655c4: r0 = int()
    //     0xa655c4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa655c8: ldur            x1, [fp, #-0x38]
    // 0xa655cc: r2 = 6
    //     0xa655cc: movz            x2, #0x6
    // 0xa655d0: r0 = _getValueOrData()
    //     0xa655d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa655d4: ldur            x3, [fp, #-0x38]
    // 0xa655d8: LoadField: r1 = r3->field_f
    //     0xa655d8: ldur            w1, [x3, #0xf]
    // 0xa655dc: DecompressPointer r1
    //     0xa655dc: add             x1, x1, HEAP, lsl #32
    // 0xa655e0: cmp             w1, w0
    // 0xa655e4: b.ne            #0xa655f0
    // 0xa655e8: r4 = Null
    //     0xa655e8: mov             x4, NULL
    // 0xa655ec: b               #0xa655f4
    // 0xa655f0: mov             x4, x0
    // 0xa655f4: mov             x0, x4
    // 0xa655f8: stur            x4, [fp, #-0x48]
    // 0xa655fc: r2 = Null
    //     0xa655fc: mov             x2, NULL
    // 0xa65600: r1 = Null
    //     0xa65600: mov             x1, NULL
    // 0xa65604: r4 = 60
    //     0xa65604: movz            x4, #0x3c
    // 0xa65608: branchIfSmi(r0, 0xa65614)
    //     0xa65608: tbz             w0, #0, #0xa65614
    // 0xa6560c: r4 = LoadClassIdInstr(r0)
    //     0xa6560c: ldur            x4, [x0, #-1]
    //     0xa65610: ubfx            x4, x4, #0xc, #0x14
    // 0xa65614: cmp             x4, #0x3e
    // 0xa65618: b.eq            #0xa6562c
    // 0xa6561c: r8 = double
    //     0xa6561c: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa65620: r3 = Null
    //     0xa65620: add             x3, PP, #0x21, lsl #12  ; [pp+0x21240] Null
    //     0xa65624: ldr             x3, [x3, #0x240]
    // 0xa65628: r0 = double()
    //     0xa65628: bl              #0xed4460  ; IsType_double_Stub
    // 0xa6562c: ldur            x1, [fp, #-0x38]
    // 0xa65630: r2 = 8
    //     0xa65630: movz            x2, #0x8
    // 0xa65634: r0 = _getValueOrData()
    //     0xa65634: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65638: ldur            x3, [fp, #-0x38]
    // 0xa6563c: LoadField: r1 = r3->field_f
    //     0xa6563c: ldur            w1, [x3, #0xf]
    // 0xa65640: DecompressPointer r1
    //     0xa65640: add             x1, x1, HEAP, lsl #32
    // 0xa65644: cmp             w1, w0
    // 0xa65648: b.ne            #0xa65654
    // 0xa6564c: r4 = Null
    //     0xa6564c: mov             x4, NULL
    // 0xa65650: b               #0xa65658
    // 0xa65654: mov             x4, x0
    // 0xa65658: mov             x0, x4
    // 0xa6565c: stur            x4, [fp, #-0x50]
    // 0xa65660: r2 = Null
    //     0xa65660: mov             x2, NULL
    // 0xa65664: r1 = Null
    //     0xa65664: mov             x1, NULL
    // 0xa65668: r4 = 60
    //     0xa65668: movz            x4, #0x3c
    // 0xa6566c: branchIfSmi(r0, 0xa65678)
    //     0xa6566c: tbz             w0, #0, #0xa65678
    // 0xa65670: r4 = LoadClassIdInstr(r0)
    //     0xa65670: ldur            x4, [x0, #-1]
    //     0xa65674: ubfx            x4, x4, #0xc, #0x14
    // 0xa65678: cmp             x4, #0x3e
    // 0xa6567c: b.eq            #0xa65690
    // 0xa65680: r8 = double
    //     0xa65680: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xa65684: r3 = Null
    //     0xa65684: add             x3, PP, #0x21, lsl #12  ; [pp+0x21250] Null
    //     0xa65688: ldr             x3, [x3, #0x250]
    // 0xa6568c: r0 = double()
    //     0xa6568c: bl              #0xed4460  ; IsType_double_Stub
    // 0xa65690: ldur            x1, [fp, #-0x38]
    // 0xa65694: r2 = 10
    //     0xa65694: movz            x2, #0xa
    // 0xa65698: r0 = _getValueOrData()
    //     0xa65698: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6569c: ldur            x3, [fp, #-0x38]
    // 0xa656a0: LoadField: r1 = r3->field_f
    //     0xa656a0: ldur            w1, [x3, #0xf]
    // 0xa656a4: DecompressPointer r1
    //     0xa656a4: add             x1, x1, HEAP, lsl #32
    // 0xa656a8: cmp             w1, w0
    // 0xa656ac: b.ne            #0xa656b8
    // 0xa656b0: r4 = Null
    //     0xa656b0: mov             x4, NULL
    // 0xa656b4: b               #0xa656bc
    // 0xa656b8: mov             x4, x0
    // 0xa656bc: mov             x0, x4
    // 0xa656c0: stur            x4, [fp, #-0x58]
    // 0xa656c4: r2 = Null
    //     0xa656c4: mov             x2, NULL
    // 0xa656c8: r1 = Null
    //     0xa656c8: mov             x1, NULL
    // 0xa656cc: branchIfSmi(r0, 0xa656f4)
    //     0xa656cc: tbz             w0, #0, #0xa656f4
    // 0xa656d0: r4 = LoadClassIdInstr(r0)
    //     0xa656d0: ldur            x4, [x0, #-1]
    //     0xa656d4: ubfx            x4, x4, #0xc, #0x14
    // 0xa656d8: sub             x4, x4, #0x3c
    // 0xa656dc: cmp             x4, #1
    // 0xa656e0: b.ls            #0xa656f4
    // 0xa656e4: r8 = int
    //     0xa656e4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa656e8: r3 = Null
    //     0xa656e8: add             x3, PP, #0x21, lsl #12  ; [pp+0x21260] Null
    //     0xa656ec: ldr             x3, [x3, #0x260]
    // 0xa656f0: r0 = int()
    //     0xa656f0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa656f4: ldur            x1, [fp, #-0x38]
    // 0xa656f8: r2 = 12
    //     0xa656f8: movz            x2, #0xc
    // 0xa656fc: r0 = _getValueOrData()
    //     0xa656fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65700: ldur            x3, [fp, #-0x38]
    // 0xa65704: LoadField: r1 = r3->field_f
    //     0xa65704: ldur            w1, [x3, #0xf]
    // 0xa65708: DecompressPointer r1
    //     0xa65708: add             x1, x1, HEAP, lsl #32
    // 0xa6570c: cmp             w1, w0
    // 0xa65710: b.ne            #0xa6571c
    // 0xa65714: r4 = Null
    //     0xa65714: mov             x4, NULL
    // 0xa65718: b               #0xa65720
    // 0xa6571c: mov             x4, x0
    // 0xa65720: mov             x0, x4
    // 0xa65724: stur            x4, [fp, #-0x60]
    // 0xa65728: r2 = Null
    //     0xa65728: mov             x2, NULL
    // 0xa6572c: r1 = Null
    //     0xa6572c: mov             x1, NULL
    // 0xa65730: branchIfSmi(r0, 0xa65758)
    //     0xa65730: tbz             w0, #0, #0xa65758
    // 0xa65734: r4 = LoadClassIdInstr(r0)
    //     0xa65734: ldur            x4, [x0, #-1]
    //     0xa65738: ubfx            x4, x4, #0xc, #0x14
    // 0xa6573c: sub             x4, x4, #0x3c
    // 0xa65740: cmp             x4, #1
    // 0xa65744: b.ls            #0xa65758
    // 0xa65748: r8 = int
    //     0xa65748: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa6574c: r3 = Null
    //     0xa6574c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21270] Null
    //     0xa65750: ldr             x3, [x3, #0x270]
    // 0xa65754: r0 = int()
    //     0xa65754: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa65758: ldur            x1, [fp, #-0x38]
    // 0xa6575c: r2 = 14
    //     0xa6575c: movz            x2, #0xe
    // 0xa65760: r0 = _getValueOrData()
    //     0xa65760: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65764: mov             x1, x0
    // 0xa65768: ldur            x0, [fp, #-0x38]
    // 0xa6576c: LoadField: r2 = r0->field_f
    //     0xa6576c: ldur            w2, [x0, #0xf]
    // 0xa65770: DecompressPointer r2
    //     0xa65770: add             x2, x2, HEAP, lsl #32
    // 0xa65774: cmp             w2, w1
    // 0xa65778: b.ne            #0xa65784
    // 0xa6577c: r10 = Null
    //     0xa6577c: mov             x10, NULL
    // 0xa65780: b               #0xa65788
    // 0xa65784: mov             x10, x1
    // 0xa65788: ldur            x9, [fp, #-0x18]
    // 0xa6578c: ldur            x8, [fp, #-0x20]
    // 0xa65790: ldur            x7, [fp, #-0x40]
    // 0xa65794: ldur            x6, [fp, #-0x48]
    // 0xa65798: ldur            x5, [fp, #-0x50]
    // 0xa6579c: ldur            x4, [fp, #-0x58]
    // 0xa657a0: ldur            x3, [fp, #-0x60]
    // 0xa657a4: mov             x0, x10
    // 0xa657a8: stur            x10, [fp, #-0x38]
    // 0xa657ac: r2 = Null
    //     0xa657ac: mov             x2, NULL
    // 0xa657b0: r1 = Null
    //     0xa657b0: mov             x1, NULL
    // 0xa657b4: r4 = 60
    //     0xa657b4: movz            x4, #0x3c
    // 0xa657b8: branchIfSmi(r0, 0xa657c4)
    //     0xa657b8: tbz             w0, #0, #0xa657c4
    // 0xa657bc: r4 = LoadClassIdInstr(r0)
    //     0xa657bc: ldur            x4, [x0, #-1]
    //     0xa657c0: ubfx            x4, x4, #0xc, #0x14
    // 0xa657c4: sub             x4, x4, #0x5e
    // 0xa657c8: cmp             x4, #1
    // 0xa657cc: b.ls            #0xa657e0
    // 0xa657d0: r8 = String
    //     0xa657d0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa657d4: r3 = Null
    //     0xa657d4: add             x3, PP, #0x21, lsl #12  ; [pp+0x21280] Null
    //     0xa657d8: ldr             x3, [x3, #0x280]
    // 0xa657dc: r0 = String()
    //     0xa657dc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa657e0: ldur            x0, [fp, #-0x18]
    // 0xa657e4: r1 = LoadInt32Instr(r0)
    //     0xa657e4: sbfx            x1, x0, #1, #0x1f
    //     0xa657e8: tbz             w0, #0, #0xa657f0
    //     0xa657ec: ldur            x1, [x0, #7]
    // 0xa657f0: stur            x1, [fp, #-8]
    // 0xa657f4: r0 = Regency()
    //     0xa657f4: bl              #0xa658ec  ; AllocateRegencyStub -> Regency (size=0x40)
    // 0xa657f8: mov             x1, x0
    // 0xa657fc: ldur            x0, [fp, #-8]
    // 0xa65800: StoreField: r1->field_7 = r0
    //     0xa65800: stur            x0, [x1, #7]
    // 0xa65804: ldur            x0, [fp, #-0x20]
    // 0xa65808: StoreField: r1->field_f = r0
    //     0xa65808: stur            w0, [x1, #0xf]
    // 0xa6580c: ldur            x0, [fp, #-0x40]
    // 0xa65810: r2 = LoadInt32Instr(r0)
    //     0xa65810: sbfx            x2, x0, #1, #0x1f
    //     0xa65814: tbz             w0, #0, #0xa6581c
    //     0xa65818: ldur            x2, [x0, #7]
    // 0xa6581c: StoreField: r1->field_13 = r2
    //     0xa6581c: stur            x2, [x1, #0x13]
    // 0xa65820: ldur            x0, [fp, #-0x48]
    // 0xa65824: LoadField: d0 = r0->field_7
    //     0xa65824: ldur            d0, [x0, #7]
    // 0xa65828: StoreField: r1->field_1b = d0
    //     0xa65828: stur            d0, [x1, #0x1b]
    // 0xa6582c: ldur            x0, [fp, #-0x50]
    // 0xa65830: LoadField: d0 = r0->field_7
    //     0xa65830: ldur            d0, [x0, #7]
    // 0xa65834: StoreField: r1->field_23 = d0
    //     0xa65834: stur            d0, [x1, #0x23]
    // 0xa65838: ldur            x0, [fp, #-0x58]
    // 0xa6583c: r2 = LoadInt32Instr(r0)
    //     0xa6583c: sbfx            x2, x0, #1, #0x1f
    //     0xa65840: tbz             w0, #0, #0xa65848
    //     0xa65844: ldur            x2, [x0, #7]
    // 0xa65848: StoreField: r1->field_2b = r2
    //     0xa65848: stur            x2, [x1, #0x2b]
    // 0xa6584c: ldur            x0, [fp, #-0x60]
    // 0xa65850: r2 = LoadInt32Instr(r0)
    //     0xa65850: sbfx            x2, x0, #1, #0x1f
    //     0xa65854: tbz             w0, #0, #0xa6585c
    //     0xa65858: ldur            x2, [x0, #7]
    // 0xa6585c: StoreField: r1->field_33 = r2
    //     0xa6585c: stur            x2, [x1, #0x33]
    // 0xa65860: ldur            x0, [fp, #-0x38]
    // 0xa65864: StoreField: r1->field_3b = r0
    //     0xa65864: stur            w0, [x1, #0x3b]
    // 0xa65868: mov             x0, x1
    // 0xa6586c: LeaveFrame
    //     0xa6586c: mov             SP, fp
    //     0xa65870: ldp             fp, lr, [SP], #0x10
    // 0xa65874: ret
    //     0xa65874: ret             
    // 0xa65878: r0 = RangeError()
    //     0xa65878: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa6587c: mov             x1, x0
    // 0xa65880: r0 = "Not enough bytes available."
    //     0xa65880: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa65884: ldr             x0, [x0, #0x8a8]
    // 0xa65888: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65888: stur            w0, [x1, #0x17]
    // 0xa6588c: r2 = false
    //     0xa6588c: add             x2, NULL, #0x30  ; false
    // 0xa65890: StoreField: r1->field_b = r2
    //     0xa65890: stur            w2, [x1, #0xb]
    // 0xa65894: mov             x0, x1
    // 0xa65898: r0 = Throw()
    //     0xa65898: bl              #0xec04b8  ; ThrowStub
    // 0xa6589c: brk             #0
    // 0xa658a0: r0 = "Not enough bytes available."
    //     0xa658a0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa658a4: ldr             x0, [x0, #0x8a8]
    // 0xa658a8: r2 = false
    //     0xa658a8: add             x2, NULL, #0x30  ; false
    // 0xa658ac: r0 = RangeError()
    //     0xa658ac: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa658b0: mov             x1, x0
    // 0xa658b4: r0 = "Not enough bytes available."
    //     0xa658b4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa658b8: ldr             x0, [x0, #0x8a8]
    // 0xa658bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa658bc: stur            w0, [x1, #0x17]
    // 0xa658c0: r0 = false
    //     0xa658c0: add             x0, NULL, #0x30  ; false
    // 0xa658c4: StoreField: r1->field_b = r0
    //     0xa658c4: stur            w0, [x1, #0xb]
    // 0xa658c8: mov             x0, x1
    // 0xa658cc: r0 = Throw()
    //     0xa658cc: bl              #0xec04b8  ; ThrowStub
    // 0xa658d0: brk             #0
    // 0xa658d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa658d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa658d8: b               #0xa6537c
    // 0xa658dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa658dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa658e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa658e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa658e4: b               #0xa65404
    // 0xa658e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa658e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd6878, size: 0x600
    // 0xbd6878: EnterFrame
    //     0xbd6878: stp             fp, lr, [SP, #-0x10]!
    //     0xbd687c: mov             fp, SP
    // 0xbd6880: AllocStack(0x28)
    //     0xbd6880: sub             SP, SP, #0x28
    // 0xbd6884: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd6884: mov             x4, x2
    //     0xbd6888: stur            x2, [fp, #-8]
    //     0xbd688c: stur            x3, [fp, #-0x10]
    // 0xbd6890: CheckStackOverflow
    //     0xbd6890: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6894: cmp             SP, x16
    //     0xbd6898: b.ls            #0xbd6e1c
    // 0xbd689c: mov             x0, x3
    // 0xbd68a0: r2 = Null
    //     0xbd68a0: mov             x2, NULL
    // 0xbd68a4: r1 = Null
    //     0xbd68a4: mov             x1, NULL
    // 0xbd68a8: r4 = 60
    //     0xbd68a8: movz            x4, #0x3c
    // 0xbd68ac: branchIfSmi(r0, 0xbd68b8)
    //     0xbd68ac: tbz             w0, #0, #0xbd68b8
    // 0xbd68b0: r4 = LoadClassIdInstr(r0)
    //     0xbd68b0: ldur            x4, [x0, #-1]
    //     0xbd68b4: ubfx            x4, x4, #0xc, #0x14
    // 0xbd68b8: cmp             x4, #0x465
    // 0xbd68bc: b.eq            #0xbd68d4
    // 0xbd68c0: r8 = Regency
    //     0xbd68c0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b2e0] Type: Regency
    //     0xbd68c4: ldr             x8, [x8, #0x2e0]
    // 0xbd68c8: r3 = Null
    //     0xbd68c8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b2e8] Null
    //     0xbd68cc: ldr             x3, [x3, #0x2e8]
    // 0xbd68d0: r0 = Regency()
    //     0xbd68d0: bl              #0x836ad4  ; IsType_Regency_Stub
    // 0xbd68d4: ldur            x0, [fp, #-8]
    // 0xbd68d8: LoadField: r1 = r0->field_b
    //     0xbd68d8: ldur            w1, [x0, #0xb]
    // 0xbd68dc: DecompressPointer r1
    //     0xbd68dc: add             x1, x1, HEAP, lsl #32
    // 0xbd68e0: LoadField: r2 = r1->field_13
    //     0xbd68e0: ldur            w2, [x1, #0x13]
    // 0xbd68e4: LoadField: r1 = r0->field_13
    //     0xbd68e4: ldur            x1, [x0, #0x13]
    // 0xbd68e8: r3 = LoadInt32Instr(r2)
    //     0xbd68e8: sbfx            x3, x2, #1, #0x1f
    // 0xbd68ec: sub             x2, x3, x1
    // 0xbd68f0: cmp             x2, #1
    // 0xbd68f4: b.ge            #0xbd6904
    // 0xbd68f8: mov             x1, x0
    // 0xbd68fc: r2 = 1
    //     0xbd68fc: movz            x2, #0x1
    // 0xbd6900: r0 = _increaseBufferSize()
    //     0xbd6900: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6904: ldur            x3, [fp, #-8]
    // 0xbd6908: r2 = 8
    //     0xbd6908: movz            x2, #0x8
    // 0xbd690c: LoadField: r4 = r3->field_b
    //     0xbd690c: ldur            w4, [x3, #0xb]
    // 0xbd6910: DecompressPointer r4
    //     0xbd6910: add             x4, x4, HEAP, lsl #32
    // 0xbd6914: LoadField: r5 = r3->field_13
    //     0xbd6914: ldur            x5, [x3, #0x13]
    // 0xbd6918: add             x6, x5, #1
    // 0xbd691c: StoreField: r3->field_13 = r6
    //     0xbd691c: stur            x6, [x3, #0x13]
    // 0xbd6920: LoadField: r0 = r4->field_13
    //     0xbd6920: ldur            w0, [x4, #0x13]
    // 0xbd6924: r7 = LoadInt32Instr(r0)
    //     0xbd6924: sbfx            x7, x0, #1, #0x1f
    // 0xbd6928: mov             x0, x7
    // 0xbd692c: mov             x1, x5
    // 0xbd6930: cmp             x1, x0
    // 0xbd6934: b.hs            #0xbd6e24
    // 0xbd6938: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd6938: add             x0, x4, x5
    //     0xbd693c: strb            w2, [x0, #0x17]
    // 0xbd6940: sub             x0, x7, x6
    // 0xbd6944: cmp             x0, #1
    // 0xbd6948: b.ge            #0xbd6958
    // 0xbd694c: mov             x1, x3
    // 0xbd6950: r2 = 1
    //     0xbd6950: movz            x2, #0x1
    // 0xbd6954: r0 = _increaseBufferSize()
    //     0xbd6954: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6958: ldur            x2, [fp, #-8]
    // 0xbd695c: ldur            x3, [fp, #-0x10]
    // 0xbd6960: LoadField: r4 = r2->field_b
    //     0xbd6960: ldur            w4, [x2, #0xb]
    // 0xbd6964: DecompressPointer r4
    //     0xbd6964: add             x4, x4, HEAP, lsl #32
    // 0xbd6968: LoadField: r5 = r2->field_13
    //     0xbd6968: ldur            x5, [x2, #0x13]
    // 0xbd696c: add             x0, x5, #1
    // 0xbd6970: StoreField: r2->field_13 = r0
    //     0xbd6970: stur            x0, [x2, #0x13]
    // 0xbd6974: LoadField: r0 = r4->field_13
    //     0xbd6974: ldur            w0, [x4, #0x13]
    // 0xbd6978: r1 = LoadInt32Instr(r0)
    //     0xbd6978: sbfx            x1, x0, #1, #0x1f
    // 0xbd697c: mov             x0, x1
    // 0xbd6980: mov             x1, x5
    // 0xbd6984: cmp             x1, x0
    // 0xbd6988: b.hs            #0xbd6e28
    // 0xbd698c: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd698c: add             x0, x4, x5
    //     0xbd6990: strb            wzr, [x0, #0x17]
    // 0xbd6994: LoadField: r4 = r3->field_7
    //     0xbd6994: ldur            x4, [x3, #7]
    // 0xbd6998: r0 = BoxInt64Instr(r4)
    //     0xbd6998: sbfiz           x0, x4, #1, #0x1f
    //     0xbd699c: cmp             x4, x0, asr #1
    //     0xbd69a0: b.eq            #0xbd69ac
    //     0xbd69a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd69a8: stur            x4, [x0, #7]
    // 0xbd69ac: r16 = <int>
    //     0xbd69ac: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd69b0: stp             x2, x16, [SP, #8]
    // 0xbd69b4: str             x0, [SP]
    // 0xbd69b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd69b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd69bc: r0 = write()
    //     0xbd69bc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd69c0: ldur            x0, [fp, #-8]
    // 0xbd69c4: LoadField: r1 = r0->field_b
    //     0xbd69c4: ldur            w1, [x0, #0xb]
    // 0xbd69c8: DecompressPointer r1
    //     0xbd69c8: add             x1, x1, HEAP, lsl #32
    // 0xbd69cc: LoadField: r2 = r1->field_13
    //     0xbd69cc: ldur            w2, [x1, #0x13]
    // 0xbd69d0: LoadField: r1 = r0->field_13
    //     0xbd69d0: ldur            x1, [x0, #0x13]
    // 0xbd69d4: r3 = LoadInt32Instr(r2)
    //     0xbd69d4: sbfx            x3, x2, #1, #0x1f
    // 0xbd69d8: sub             x2, x3, x1
    // 0xbd69dc: cmp             x2, #1
    // 0xbd69e0: b.ge            #0xbd69f0
    // 0xbd69e4: mov             x1, x0
    // 0xbd69e8: r2 = 1
    //     0xbd69e8: movz            x2, #0x1
    // 0xbd69ec: r0 = _increaseBufferSize()
    //     0xbd69ec: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd69f0: ldur            x2, [fp, #-8]
    // 0xbd69f4: ldur            x3, [fp, #-0x10]
    // 0xbd69f8: r4 = 1
    //     0xbd69f8: movz            x4, #0x1
    // 0xbd69fc: LoadField: r5 = r2->field_b
    //     0xbd69fc: ldur            w5, [x2, #0xb]
    // 0xbd6a00: DecompressPointer r5
    //     0xbd6a00: add             x5, x5, HEAP, lsl #32
    // 0xbd6a04: LoadField: r6 = r2->field_13
    //     0xbd6a04: ldur            x6, [x2, #0x13]
    // 0xbd6a08: add             x0, x6, #1
    // 0xbd6a0c: StoreField: r2->field_13 = r0
    //     0xbd6a0c: stur            x0, [x2, #0x13]
    // 0xbd6a10: LoadField: r0 = r5->field_13
    //     0xbd6a10: ldur            w0, [x5, #0x13]
    // 0xbd6a14: r1 = LoadInt32Instr(r0)
    //     0xbd6a14: sbfx            x1, x0, #1, #0x1f
    // 0xbd6a18: mov             x0, x1
    // 0xbd6a1c: mov             x1, x6
    // 0xbd6a20: cmp             x1, x0
    // 0xbd6a24: b.hs            #0xbd6e2c
    // 0xbd6a28: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6a28: add             x0, x5, x6
    //     0xbd6a2c: strb            w4, [x0, #0x17]
    // 0xbd6a30: LoadField: r0 = r3->field_f
    //     0xbd6a30: ldur            w0, [x3, #0xf]
    // 0xbd6a34: DecompressPointer r0
    //     0xbd6a34: add             x0, x0, HEAP, lsl #32
    // 0xbd6a38: r16 = <String>
    //     0xbd6a38: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd6a3c: stp             x2, x16, [SP, #8]
    // 0xbd6a40: str             x0, [SP]
    // 0xbd6a44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6a44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6a48: r0 = write()
    //     0xbd6a48: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6a4c: ldur            x0, [fp, #-8]
    // 0xbd6a50: LoadField: r1 = r0->field_b
    //     0xbd6a50: ldur            w1, [x0, #0xb]
    // 0xbd6a54: DecompressPointer r1
    //     0xbd6a54: add             x1, x1, HEAP, lsl #32
    // 0xbd6a58: LoadField: r2 = r1->field_13
    //     0xbd6a58: ldur            w2, [x1, #0x13]
    // 0xbd6a5c: LoadField: r1 = r0->field_13
    //     0xbd6a5c: ldur            x1, [x0, #0x13]
    // 0xbd6a60: r3 = LoadInt32Instr(r2)
    //     0xbd6a60: sbfx            x3, x2, #1, #0x1f
    // 0xbd6a64: sub             x2, x3, x1
    // 0xbd6a68: cmp             x2, #1
    // 0xbd6a6c: b.ge            #0xbd6a7c
    // 0xbd6a70: mov             x1, x0
    // 0xbd6a74: r2 = 1
    //     0xbd6a74: movz            x2, #0x1
    // 0xbd6a78: r0 = _increaseBufferSize()
    //     0xbd6a78: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6a7c: ldur            x2, [fp, #-8]
    // 0xbd6a80: ldur            x3, [fp, #-0x10]
    // 0xbd6a84: r4 = 2
    //     0xbd6a84: movz            x4, #0x2
    // 0xbd6a88: LoadField: r5 = r2->field_b
    //     0xbd6a88: ldur            w5, [x2, #0xb]
    // 0xbd6a8c: DecompressPointer r5
    //     0xbd6a8c: add             x5, x5, HEAP, lsl #32
    // 0xbd6a90: LoadField: r6 = r2->field_13
    //     0xbd6a90: ldur            x6, [x2, #0x13]
    // 0xbd6a94: add             x0, x6, #1
    // 0xbd6a98: StoreField: r2->field_13 = r0
    //     0xbd6a98: stur            x0, [x2, #0x13]
    // 0xbd6a9c: LoadField: r0 = r5->field_13
    //     0xbd6a9c: ldur            w0, [x5, #0x13]
    // 0xbd6aa0: r1 = LoadInt32Instr(r0)
    //     0xbd6aa0: sbfx            x1, x0, #1, #0x1f
    // 0xbd6aa4: mov             x0, x1
    // 0xbd6aa8: mov             x1, x6
    // 0xbd6aac: cmp             x1, x0
    // 0xbd6ab0: b.hs            #0xbd6e30
    // 0xbd6ab4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6ab4: add             x0, x5, x6
    //     0xbd6ab8: strb            w4, [x0, #0x17]
    // 0xbd6abc: LoadField: r4 = r3->field_13
    //     0xbd6abc: ldur            x4, [x3, #0x13]
    // 0xbd6ac0: r0 = BoxInt64Instr(r4)
    //     0xbd6ac0: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6ac4: cmp             x4, x0, asr #1
    //     0xbd6ac8: b.eq            #0xbd6ad4
    //     0xbd6acc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6ad0: stur            x4, [x0, #7]
    // 0xbd6ad4: r16 = <int>
    //     0xbd6ad4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6ad8: stp             x2, x16, [SP, #8]
    // 0xbd6adc: str             x0, [SP]
    // 0xbd6ae0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6ae0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6ae4: r0 = write()
    //     0xbd6ae4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6ae8: ldur            x0, [fp, #-8]
    // 0xbd6aec: LoadField: r1 = r0->field_b
    //     0xbd6aec: ldur            w1, [x0, #0xb]
    // 0xbd6af0: DecompressPointer r1
    //     0xbd6af0: add             x1, x1, HEAP, lsl #32
    // 0xbd6af4: LoadField: r2 = r1->field_13
    //     0xbd6af4: ldur            w2, [x1, #0x13]
    // 0xbd6af8: LoadField: r1 = r0->field_13
    //     0xbd6af8: ldur            x1, [x0, #0x13]
    // 0xbd6afc: r3 = LoadInt32Instr(r2)
    //     0xbd6afc: sbfx            x3, x2, #1, #0x1f
    // 0xbd6b00: sub             x2, x3, x1
    // 0xbd6b04: cmp             x2, #1
    // 0xbd6b08: b.ge            #0xbd6b18
    // 0xbd6b0c: mov             x1, x0
    // 0xbd6b10: r2 = 1
    //     0xbd6b10: movz            x2, #0x1
    // 0xbd6b14: r0 = _increaseBufferSize()
    //     0xbd6b14: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6b18: ldur            x2, [fp, #-8]
    // 0xbd6b1c: ldur            x3, [fp, #-0x10]
    // 0xbd6b20: r4 = 3
    //     0xbd6b20: movz            x4, #0x3
    // 0xbd6b24: LoadField: r5 = r2->field_b
    //     0xbd6b24: ldur            w5, [x2, #0xb]
    // 0xbd6b28: DecompressPointer r5
    //     0xbd6b28: add             x5, x5, HEAP, lsl #32
    // 0xbd6b2c: LoadField: r6 = r2->field_13
    //     0xbd6b2c: ldur            x6, [x2, #0x13]
    // 0xbd6b30: add             x0, x6, #1
    // 0xbd6b34: StoreField: r2->field_13 = r0
    //     0xbd6b34: stur            x0, [x2, #0x13]
    // 0xbd6b38: LoadField: r0 = r5->field_13
    //     0xbd6b38: ldur            w0, [x5, #0x13]
    // 0xbd6b3c: r1 = LoadInt32Instr(r0)
    //     0xbd6b3c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6b40: mov             x0, x1
    // 0xbd6b44: mov             x1, x6
    // 0xbd6b48: cmp             x1, x0
    // 0xbd6b4c: b.hs            #0xbd6e34
    // 0xbd6b50: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6b50: add             x0, x5, x6
    //     0xbd6b54: strb            w4, [x0, #0x17]
    // 0xbd6b58: LoadField: d0 = r3->field_1b
    //     0xbd6b58: ldur            d0, [x3, #0x1b]
    // 0xbd6b5c: r0 = inline_Allocate_Double()
    //     0xbd6b5c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd6b60: add             x0, x0, #0x10
    //     0xbd6b64: cmp             x1, x0
    //     0xbd6b68: b.ls            #0xbd6e38
    //     0xbd6b6c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd6b70: sub             x0, x0, #0xf
    //     0xbd6b74: movz            x1, #0xe15c
    //     0xbd6b78: movk            x1, #0x3, lsl #16
    //     0xbd6b7c: stur            x1, [x0, #-1]
    // 0xbd6b80: StoreField: r0->field_7 = d0
    //     0xbd6b80: stur            d0, [x0, #7]
    // 0xbd6b84: r16 = <double>
    //     0xbd6b84: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd6b88: stp             x2, x16, [SP, #8]
    // 0xbd6b8c: str             x0, [SP]
    // 0xbd6b90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6b90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6b94: r0 = write()
    //     0xbd6b94: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6b98: ldur            x0, [fp, #-8]
    // 0xbd6b9c: LoadField: r1 = r0->field_b
    //     0xbd6b9c: ldur            w1, [x0, #0xb]
    // 0xbd6ba0: DecompressPointer r1
    //     0xbd6ba0: add             x1, x1, HEAP, lsl #32
    // 0xbd6ba4: LoadField: r2 = r1->field_13
    //     0xbd6ba4: ldur            w2, [x1, #0x13]
    // 0xbd6ba8: LoadField: r1 = r0->field_13
    //     0xbd6ba8: ldur            x1, [x0, #0x13]
    // 0xbd6bac: r3 = LoadInt32Instr(r2)
    //     0xbd6bac: sbfx            x3, x2, #1, #0x1f
    // 0xbd6bb0: sub             x2, x3, x1
    // 0xbd6bb4: cmp             x2, #1
    // 0xbd6bb8: b.ge            #0xbd6bc8
    // 0xbd6bbc: mov             x1, x0
    // 0xbd6bc0: r2 = 1
    //     0xbd6bc0: movz            x2, #0x1
    // 0xbd6bc4: r0 = _increaseBufferSize()
    //     0xbd6bc4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6bc8: ldur            x2, [fp, #-8]
    // 0xbd6bcc: ldur            x3, [fp, #-0x10]
    // 0xbd6bd0: r4 = 4
    //     0xbd6bd0: movz            x4, #0x4
    // 0xbd6bd4: LoadField: r5 = r2->field_b
    //     0xbd6bd4: ldur            w5, [x2, #0xb]
    // 0xbd6bd8: DecompressPointer r5
    //     0xbd6bd8: add             x5, x5, HEAP, lsl #32
    // 0xbd6bdc: LoadField: r6 = r2->field_13
    //     0xbd6bdc: ldur            x6, [x2, #0x13]
    // 0xbd6be0: add             x0, x6, #1
    // 0xbd6be4: StoreField: r2->field_13 = r0
    //     0xbd6be4: stur            x0, [x2, #0x13]
    // 0xbd6be8: LoadField: r0 = r5->field_13
    //     0xbd6be8: ldur            w0, [x5, #0x13]
    // 0xbd6bec: r1 = LoadInt32Instr(r0)
    //     0xbd6bec: sbfx            x1, x0, #1, #0x1f
    // 0xbd6bf0: mov             x0, x1
    // 0xbd6bf4: mov             x1, x6
    // 0xbd6bf8: cmp             x1, x0
    // 0xbd6bfc: b.hs            #0xbd6e50
    // 0xbd6c00: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6c00: add             x0, x5, x6
    //     0xbd6c04: strb            w4, [x0, #0x17]
    // 0xbd6c08: LoadField: d0 = r3->field_23
    //     0xbd6c08: ldur            d0, [x3, #0x23]
    // 0xbd6c0c: r0 = inline_Allocate_Double()
    //     0xbd6c0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd6c10: add             x0, x0, #0x10
    //     0xbd6c14: cmp             x1, x0
    //     0xbd6c18: b.ls            #0xbd6e54
    //     0xbd6c1c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd6c20: sub             x0, x0, #0xf
    //     0xbd6c24: movz            x1, #0xe15c
    //     0xbd6c28: movk            x1, #0x3, lsl #16
    //     0xbd6c2c: stur            x1, [x0, #-1]
    // 0xbd6c30: StoreField: r0->field_7 = d0
    //     0xbd6c30: stur            d0, [x0, #7]
    // 0xbd6c34: r16 = <double>
    //     0xbd6c34: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xbd6c38: stp             x2, x16, [SP, #8]
    // 0xbd6c3c: str             x0, [SP]
    // 0xbd6c40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6c40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6c44: r0 = write()
    //     0xbd6c44: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6c48: ldur            x0, [fp, #-8]
    // 0xbd6c4c: LoadField: r1 = r0->field_b
    //     0xbd6c4c: ldur            w1, [x0, #0xb]
    // 0xbd6c50: DecompressPointer r1
    //     0xbd6c50: add             x1, x1, HEAP, lsl #32
    // 0xbd6c54: LoadField: r2 = r1->field_13
    //     0xbd6c54: ldur            w2, [x1, #0x13]
    // 0xbd6c58: LoadField: r1 = r0->field_13
    //     0xbd6c58: ldur            x1, [x0, #0x13]
    // 0xbd6c5c: r3 = LoadInt32Instr(r2)
    //     0xbd6c5c: sbfx            x3, x2, #1, #0x1f
    // 0xbd6c60: sub             x2, x3, x1
    // 0xbd6c64: cmp             x2, #1
    // 0xbd6c68: b.ge            #0xbd6c78
    // 0xbd6c6c: mov             x1, x0
    // 0xbd6c70: r2 = 1
    //     0xbd6c70: movz            x2, #0x1
    // 0xbd6c74: r0 = _increaseBufferSize()
    //     0xbd6c74: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6c78: ldur            x2, [fp, #-8]
    // 0xbd6c7c: ldur            x3, [fp, #-0x10]
    // 0xbd6c80: r4 = 5
    //     0xbd6c80: movz            x4, #0x5
    // 0xbd6c84: LoadField: r5 = r2->field_b
    //     0xbd6c84: ldur            w5, [x2, #0xb]
    // 0xbd6c88: DecompressPointer r5
    //     0xbd6c88: add             x5, x5, HEAP, lsl #32
    // 0xbd6c8c: LoadField: r6 = r2->field_13
    //     0xbd6c8c: ldur            x6, [x2, #0x13]
    // 0xbd6c90: add             x0, x6, #1
    // 0xbd6c94: StoreField: r2->field_13 = r0
    //     0xbd6c94: stur            x0, [x2, #0x13]
    // 0xbd6c98: LoadField: r0 = r5->field_13
    //     0xbd6c98: ldur            w0, [x5, #0x13]
    // 0xbd6c9c: r1 = LoadInt32Instr(r0)
    //     0xbd6c9c: sbfx            x1, x0, #1, #0x1f
    // 0xbd6ca0: mov             x0, x1
    // 0xbd6ca4: mov             x1, x6
    // 0xbd6ca8: cmp             x1, x0
    // 0xbd6cac: b.hs            #0xbd6e6c
    // 0xbd6cb0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6cb0: add             x0, x5, x6
    //     0xbd6cb4: strb            w4, [x0, #0x17]
    // 0xbd6cb8: LoadField: r4 = r3->field_2b
    //     0xbd6cb8: ldur            x4, [x3, #0x2b]
    // 0xbd6cbc: r0 = BoxInt64Instr(r4)
    //     0xbd6cbc: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6cc0: cmp             x4, x0, asr #1
    //     0xbd6cc4: b.eq            #0xbd6cd0
    //     0xbd6cc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6ccc: stur            x4, [x0, #7]
    // 0xbd6cd0: r16 = <int>
    //     0xbd6cd0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6cd4: stp             x2, x16, [SP, #8]
    // 0xbd6cd8: str             x0, [SP]
    // 0xbd6cdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6cdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6ce0: r0 = write()
    //     0xbd6ce0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6ce4: ldur            x0, [fp, #-8]
    // 0xbd6ce8: LoadField: r1 = r0->field_b
    //     0xbd6ce8: ldur            w1, [x0, #0xb]
    // 0xbd6cec: DecompressPointer r1
    //     0xbd6cec: add             x1, x1, HEAP, lsl #32
    // 0xbd6cf0: LoadField: r2 = r1->field_13
    //     0xbd6cf0: ldur            w2, [x1, #0x13]
    // 0xbd6cf4: LoadField: r1 = r0->field_13
    //     0xbd6cf4: ldur            x1, [x0, #0x13]
    // 0xbd6cf8: r3 = LoadInt32Instr(r2)
    //     0xbd6cf8: sbfx            x3, x2, #1, #0x1f
    // 0xbd6cfc: sub             x2, x3, x1
    // 0xbd6d00: cmp             x2, #1
    // 0xbd6d04: b.ge            #0xbd6d14
    // 0xbd6d08: mov             x1, x0
    // 0xbd6d0c: r2 = 1
    //     0xbd6d0c: movz            x2, #0x1
    // 0xbd6d10: r0 = _increaseBufferSize()
    //     0xbd6d10: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6d14: ldur            x2, [fp, #-8]
    // 0xbd6d18: ldur            x3, [fp, #-0x10]
    // 0xbd6d1c: r4 = 6
    //     0xbd6d1c: movz            x4, #0x6
    // 0xbd6d20: LoadField: r5 = r2->field_b
    //     0xbd6d20: ldur            w5, [x2, #0xb]
    // 0xbd6d24: DecompressPointer r5
    //     0xbd6d24: add             x5, x5, HEAP, lsl #32
    // 0xbd6d28: LoadField: r6 = r2->field_13
    //     0xbd6d28: ldur            x6, [x2, #0x13]
    // 0xbd6d2c: add             x0, x6, #1
    // 0xbd6d30: StoreField: r2->field_13 = r0
    //     0xbd6d30: stur            x0, [x2, #0x13]
    // 0xbd6d34: LoadField: r0 = r5->field_13
    //     0xbd6d34: ldur            w0, [x5, #0x13]
    // 0xbd6d38: r1 = LoadInt32Instr(r0)
    //     0xbd6d38: sbfx            x1, x0, #1, #0x1f
    // 0xbd6d3c: mov             x0, x1
    // 0xbd6d40: mov             x1, x6
    // 0xbd6d44: cmp             x1, x0
    // 0xbd6d48: b.hs            #0xbd6e70
    // 0xbd6d4c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6d4c: add             x0, x5, x6
    //     0xbd6d50: strb            w4, [x0, #0x17]
    // 0xbd6d54: LoadField: r4 = r3->field_33
    //     0xbd6d54: ldur            x4, [x3, #0x33]
    // 0xbd6d58: r0 = BoxInt64Instr(r4)
    //     0xbd6d58: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6d5c: cmp             x4, x0, asr #1
    //     0xbd6d60: b.eq            #0xbd6d6c
    //     0xbd6d64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6d68: stur            x4, [x0, #7]
    // 0xbd6d6c: r16 = <int>
    //     0xbd6d6c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6d70: stp             x2, x16, [SP, #8]
    // 0xbd6d74: str             x0, [SP]
    // 0xbd6d78: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6d78: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6d7c: r0 = write()
    //     0xbd6d7c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6d80: ldur            x0, [fp, #-8]
    // 0xbd6d84: LoadField: r1 = r0->field_b
    //     0xbd6d84: ldur            w1, [x0, #0xb]
    // 0xbd6d88: DecompressPointer r1
    //     0xbd6d88: add             x1, x1, HEAP, lsl #32
    // 0xbd6d8c: LoadField: r2 = r1->field_13
    //     0xbd6d8c: ldur            w2, [x1, #0x13]
    // 0xbd6d90: LoadField: r1 = r0->field_13
    //     0xbd6d90: ldur            x1, [x0, #0x13]
    // 0xbd6d94: r3 = LoadInt32Instr(r2)
    //     0xbd6d94: sbfx            x3, x2, #1, #0x1f
    // 0xbd6d98: sub             x2, x3, x1
    // 0xbd6d9c: cmp             x2, #1
    // 0xbd6da0: b.ge            #0xbd6db0
    // 0xbd6da4: mov             x1, x0
    // 0xbd6da8: r2 = 1
    //     0xbd6da8: movz            x2, #0x1
    // 0xbd6dac: r0 = _increaseBufferSize()
    //     0xbd6dac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6db0: ldur            x2, [fp, #-8]
    // 0xbd6db4: ldur            x3, [fp, #-0x10]
    // 0xbd6db8: r4 = 7
    //     0xbd6db8: movz            x4, #0x7
    // 0xbd6dbc: LoadField: r5 = r2->field_b
    //     0xbd6dbc: ldur            w5, [x2, #0xb]
    // 0xbd6dc0: DecompressPointer r5
    //     0xbd6dc0: add             x5, x5, HEAP, lsl #32
    // 0xbd6dc4: LoadField: r6 = r2->field_13
    //     0xbd6dc4: ldur            x6, [x2, #0x13]
    // 0xbd6dc8: add             x0, x6, #1
    // 0xbd6dcc: StoreField: r2->field_13 = r0
    //     0xbd6dcc: stur            x0, [x2, #0x13]
    // 0xbd6dd0: LoadField: r0 = r5->field_13
    //     0xbd6dd0: ldur            w0, [x5, #0x13]
    // 0xbd6dd4: r1 = LoadInt32Instr(r0)
    //     0xbd6dd4: sbfx            x1, x0, #1, #0x1f
    // 0xbd6dd8: mov             x0, x1
    // 0xbd6ddc: mov             x1, x6
    // 0xbd6de0: cmp             x1, x0
    // 0xbd6de4: b.hs            #0xbd6e74
    // 0xbd6de8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd6de8: add             x0, x5, x6
    //     0xbd6dec: strb            w4, [x0, #0x17]
    // 0xbd6df0: LoadField: r0 = r3->field_3b
    //     0xbd6df0: ldur            w0, [x3, #0x3b]
    // 0xbd6df4: DecompressPointer r0
    //     0xbd6df4: add             x0, x0, HEAP, lsl #32
    // 0xbd6df8: r16 = <String>
    //     0xbd6df8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd6dfc: stp             x2, x16, [SP, #8]
    // 0xbd6e00: str             x0, [SP]
    // 0xbd6e04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6e04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6e08: r0 = write()
    //     0xbd6e08: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6e0c: r0 = Null
    //     0xbd6e0c: mov             x0, NULL
    // 0xbd6e10: LeaveFrame
    //     0xbd6e10: mov             SP, fp
    //     0xbd6e14: ldp             fp, lr, [SP], #0x10
    // 0xbd6e18: ret
    //     0xbd6e18: ret             
    // 0xbd6e1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd6e1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd6e20: b               #0xbd689c
    // 0xbd6e24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e38: SaveReg d0
    //     0xbd6e38: str             q0, [SP, #-0x10]!
    // 0xbd6e3c: stp             x2, x3, [SP, #-0x10]!
    // 0xbd6e40: r0 = AllocateDouble()
    //     0xbd6e40: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd6e44: ldp             x2, x3, [SP], #0x10
    // 0xbd6e48: RestoreReg d0
    //     0xbd6e48: ldr             q0, [SP], #0x10
    // 0xbd6e4c: b               #0xbd6b80
    // 0xbd6e50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e54: SaveReg d0
    //     0xbd6e54: str             q0, [SP, #-0x10]!
    // 0xbd6e58: stp             x2, x3, [SP, #-0x10]!
    // 0xbd6e5c: r0 = AllocateDouble()
    //     0xbd6e5c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbd6e60: ldp             x2, x3, [SP], #0x10
    // 0xbd6e64: RestoreReg d0
    //     0xbd6e64: ldr             q0, [SP], #0x10
    // 0xbd6e68: b               #0xbd6c30
    // 0xbd6e6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e6c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e70: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd6e74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd6e74: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf02f4, size: 0x24
    // 0xbf02f4: r1 = 4
    //     0xbf02f4: movz            x1, #0x4
    // 0xbf02f8: r16 = LoadInt32Instr(r1)
    //     0xbf02f8: sbfx            x16, x1, #1, #0x1f
    // 0xbf02fc: r17 = 11601
    //     0xbf02fc: movz            x17, #0x2d51
    // 0xbf0300: mul             x0, x16, x17
    // 0xbf0304: umulh           x16, x16, x17
    // 0xbf0308: eor             x0, x0, x16
    // 0xbf030c: r0 = 0
    //     0xbf030c: eor             x0, x0, x0, lsr #32
    // 0xbf0310: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0314: ret
    //     0xbf0314: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76d40, size: 0x9c
    // 0xd76d40: EnterFrame
    //     0xd76d40: stp             fp, lr, [SP, #-0x10]!
    //     0xd76d44: mov             fp, SP
    // 0xd76d48: AllocStack(0x10)
    //     0xd76d48: sub             SP, SP, #0x10
    // 0xd76d4c: CheckStackOverflow
    //     0xd76d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76d50: cmp             SP, x16
    //     0xd76d54: b.ls            #0xd76dd4
    // 0xd76d58: ldr             x0, [fp, #0x10]
    // 0xd76d5c: cmp             w0, NULL
    // 0xd76d60: b.ne            #0xd76d74
    // 0xd76d64: r0 = false
    //     0xd76d64: add             x0, NULL, #0x30  ; false
    // 0xd76d68: LeaveFrame
    //     0xd76d68: mov             SP, fp
    //     0xd76d6c: ldp             fp, lr, [SP], #0x10
    // 0xd76d70: ret
    //     0xd76d70: ret             
    // 0xd76d74: ldr             x1, [fp, #0x18]
    // 0xd76d78: cmp             w1, w0
    // 0xd76d7c: b.ne            #0xd76d88
    // 0xd76d80: r0 = true
    //     0xd76d80: add             x0, NULL, #0x20  ; true
    // 0xd76d84: b               #0xd76dc8
    // 0xd76d88: r1 = 60
    //     0xd76d88: movz            x1, #0x3c
    // 0xd76d8c: branchIfSmi(r0, 0xd76d98)
    //     0xd76d8c: tbz             w0, #0, #0xd76d98
    // 0xd76d90: r1 = LoadClassIdInstr(r0)
    //     0xd76d90: ldur            x1, [x0, #-1]
    //     0xd76d94: ubfx            x1, x1, #0xc, #0x14
    // 0xd76d98: cmp             x1, #0x671
    // 0xd76d9c: b.ne            #0xd76dc4
    // 0xd76da0: r16 = RegencyAdapter
    //     0xd76da0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2d8] Type: RegencyAdapter
    //     0xd76da4: ldr             x16, [x16, #0x2d8]
    // 0xd76da8: r30 = RegencyAdapter
    //     0xd76da8: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b2d8] Type: RegencyAdapter
    //     0xd76dac: ldr             lr, [lr, #0x2d8]
    // 0xd76db0: stp             lr, x16, [SP]
    // 0xd76db4: r0 = ==()
    //     0xd76db4: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76db8: tbnz            w0, #4, #0xd76dc4
    // 0xd76dbc: r0 = true
    //     0xd76dbc: add             x0, NULL, #0x20  ; true
    // 0xd76dc0: b               #0xd76dc8
    // 0xd76dc4: r0 = false
    //     0xd76dc4: add             x0, NULL, #0x30  ; false
    // 0xd76dc8: LeaveFrame
    //     0xd76dc8: mov             SP, fp
    //     0xd76dcc: ldp             fp, lr, [SP], #0x10
    // 0xd76dd0: ret
    //     0xd76dd0: ret             
    // 0xd76dd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76dd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76dd8: b               #0xd76d58
  }
}
