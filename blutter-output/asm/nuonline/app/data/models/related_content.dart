// lib: , url: package:nuonline/app/data/models/related_content.dart

// class id: 1050049, size: 0x8
class :: {
}

// class id: 1124, size: 0x1c, field offset: 0x8
class RelatedContent<X0> extends Object {

  factory _ RelatedContent.fromMap(/* No info */) {
    // ** addr: 0x72c9bc, size: 0x1d4
    // 0x72c9bc: EnterFrame
    //     0x72c9bc: stp             fp, lr, [SP, #-0x10]!
    //     0x72c9c0: mov             fp, SP
    // 0x72c9c4: AllocStack(0x28)
    //     0x72c9c4: sub             SP, SP, #0x28
    // 0x72c9c8: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2 */)
    //     0x72c9c8: mov             x4, x1
    //     0x72c9cc: stur            x2, [fp, #-0x10]
    //     0x72c9d0: mov             x16, x3
    //     0x72c9d4: mov             x3, x2
    //     0x72c9d8: mov             x2, x16
    //     0x72c9dc: stur            x1, [fp, #-8]
    // 0x72c9e0: CheckStackOverflow
    //     0x72c9e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c9e4: cmp             SP, x16
    //     0x72c9e8: b.ls            #0x72cb88
    // 0x72c9ec: r0 = LoadClassIdInstr(r3)
    //     0x72c9ec: ldur            x0, [x3, #-1]
    //     0x72c9f0: ubfx            x0, x0, #0xc, #0x14
    // 0x72c9f4: mov             x1, x3
    // 0x72c9f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x72c9f8: sub             lr, x0, #0x114
    //     0x72c9fc: ldr             lr, [x21, lr, lsl #3]
    //     0x72ca00: blr             lr
    // 0x72ca04: ldur            x2, [fp, #-8]
    // 0x72ca08: mov             x3, x0
    // 0x72ca0c: r1 = Null
    //     0x72ca0c: mov             x1, NULL
    // 0x72ca10: stur            x3, [fp, #-0x18]
    // 0x72ca14: cmp             w2, NULL
    // 0x72ca18: b.eq            #0x72ca38
    // 0x72ca1c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x72ca1c: ldur            w4, [x2, #0x17]
    // 0x72ca20: DecompressPointer r4
    //     0x72ca20: add             x4, x4, HEAP, lsl #32
    // 0x72ca24: r8 = X0
    //     0x72ca24: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x72ca28: LoadField: r9 = r4->field_7
    //     0x72ca28: ldur            x9, [x4, #7]
    // 0x72ca2c: r3 = Null
    //     0x72ca2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e908] Null
    //     0x72ca30: ldr             x3, [x3, #0x908]
    // 0x72ca34: blr             x9
    // 0x72ca38: ldur            x3, [fp, #-0x10]
    // 0x72ca3c: r0 = LoadClassIdInstr(r3)
    //     0x72ca3c: ldur            x0, [x3, #-1]
    //     0x72ca40: ubfx            x0, x0, #0xc, #0x14
    // 0x72ca44: mov             x1, x3
    // 0x72ca48: r2 = "title"
    //     0x72ca48: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x72ca4c: ldr             x2, [x2, #0x748]
    // 0x72ca50: r0 = GDT[cid_x0 + -0x114]()
    //     0x72ca50: sub             lr, x0, #0x114
    //     0x72ca54: ldr             lr, [x21, lr, lsl #3]
    //     0x72ca58: blr             lr
    // 0x72ca5c: mov             x3, x0
    // 0x72ca60: r2 = Null
    //     0x72ca60: mov             x2, NULL
    // 0x72ca64: r1 = Null
    //     0x72ca64: mov             x1, NULL
    // 0x72ca68: stur            x3, [fp, #-0x20]
    // 0x72ca6c: r4 = 60
    //     0x72ca6c: movz            x4, #0x3c
    // 0x72ca70: branchIfSmi(r0, 0x72ca7c)
    //     0x72ca70: tbz             w0, #0, #0x72ca7c
    // 0x72ca74: r4 = LoadClassIdInstr(r0)
    //     0x72ca74: ldur            x4, [x0, #-1]
    //     0x72ca78: ubfx            x4, x4, #0xc, #0x14
    // 0x72ca7c: sub             x4, x4, #0x5e
    // 0x72ca80: cmp             x4, #1
    // 0x72ca84: b.ls            #0x72ca98
    // 0x72ca88: r8 = String
    //     0x72ca88: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72ca8c: r3 = Null
    //     0x72ca8c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e918] Null
    //     0x72ca90: ldr             x3, [x3, #0x918]
    // 0x72ca94: r0 = String()
    //     0x72ca94: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72ca98: ldur            x3, [fp, #-0x10]
    // 0x72ca9c: r0 = LoadClassIdInstr(r3)
    //     0x72ca9c: ldur            x0, [x3, #-1]
    //     0x72caa0: ubfx            x0, x0, #0xc, #0x14
    // 0x72caa4: mov             x1, x3
    // 0x72caa8: r2 = "image"
    //     0x72caa8: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x72caac: ldr             x2, [x2, #0x520]
    // 0x72cab0: r0 = GDT[cid_x0 + -0x114]()
    //     0x72cab0: sub             lr, x0, #0x114
    //     0x72cab4: ldr             lr, [x21, lr, lsl #3]
    //     0x72cab8: blr             lr
    // 0x72cabc: mov             x3, x0
    // 0x72cac0: r2 = Null
    //     0x72cac0: mov             x2, NULL
    // 0x72cac4: r1 = Null
    //     0x72cac4: mov             x1, NULL
    // 0x72cac8: stur            x3, [fp, #-0x28]
    // 0x72cacc: r4 = 60
    //     0x72cacc: movz            x4, #0x3c
    // 0x72cad0: branchIfSmi(r0, 0x72cadc)
    //     0x72cad0: tbz             w0, #0, #0x72cadc
    // 0x72cad4: r4 = LoadClassIdInstr(r0)
    //     0x72cad4: ldur            x4, [x0, #-1]
    //     0x72cad8: ubfx            x4, x4, #0xc, #0x14
    // 0x72cadc: sub             x4, x4, #0x5e
    // 0x72cae0: cmp             x4, #1
    // 0x72cae4: b.ls            #0x72caf8
    // 0x72cae8: r8 = String
    //     0x72cae8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x72caec: r3 = Null
    //     0x72caec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e928] Null
    //     0x72caf0: ldr             x3, [x3, #0x928]
    // 0x72caf4: r0 = String()
    //     0x72caf4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x72caf8: ldur            x1, [fp, #-0x10]
    // 0x72cafc: r0 = LoadClassIdInstr(r1)
    //     0x72cafc: ldur            x0, [x1, #-1]
    //     0x72cb00: ubfx            x0, x0, #0xc, #0x14
    // 0x72cb04: r2 = "category"
    //     0x72cb04: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x72cb08: ldr             x2, [x2, #0x960]
    // 0x72cb0c: r0 = GDT[cid_x0 + -0x114]()
    //     0x72cb0c: sub             lr, x0, #0x114
    //     0x72cb10: ldr             lr, [x21, lr, lsl #3]
    //     0x72cb14: blr             lr
    // 0x72cb18: mov             x3, x0
    // 0x72cb1c: r2 = Null
    //     0x72cb1c: mov             x2, NULL
    // 0x72cb20: r1 = Null
    //     0x72cb20: mov             x1, NULL
    // 0x72cb24: stur            x3, [fp, #-0x10]
    // 0x72cb28: r4 = 60
    //     0x72cb28: movz            x4, #0x3c
    // 0x72cb2c: branchIfSmi(r0, 0x72cb38)
    //     0x72cb2c: tbz             w0, #0, #0x72cb38
    // 0x72cb30: r4 = LoadClassIdInstr(r0)
    //     0x72cb30: ldur            x4, [x0, #-1]
    //     0x72cb34: ubfx            x4, x4, #0xc, #0x14
    // 0x72cb38: sub             x4, x4, #0x5e
    // 0x72cb3c: cmp             x4, #1
    // 0x72cb40: b.ls            #0x72cb54
    // 0x72cb44: r8 = String?
    //     0x72cb44: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x72cb48: r3 = Null
    //     0x72cb48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e938] Null
    //     0x72cb4c: ldr             x3, [x3, #0x938]
    // 0x72cb50: r0 = String?()
    //     0x72cb50: bl              #0x600324  ; IsType_String?_Stub
    // 0x72cb54: ldur            x1, [fp, #-8]
    // 0x72cb58: r0 = RelatedContent()
    //     0x72cb58: bl              #0x72cbd8  ; AllocateRelatedContentStub -> RelatedContent<X0> (size=0x1c)
    // 0x72cb5c: ldur            x1, [fp, #-0x18]
    // 0x72cb60: StoreField: r0->field_b = r1
    //     0x72cb60: stur            w1, [x0, #0xb]
    // 0x72cb64: ldur            x1, [fp, #-0x20]
    // 0x72cb68: StoreField: r0->field_f = r1
    //     0x72cb68: stur            w1, [x0, #0xf]
    // 0x72cb6c: ldur            x1, [fp, #-0x28]
    // 0x72cb70: StoreField: r0->field_13 = r1
    //     0x72cb70: stur            w1, [x0, #0x13]
    // 0x72cb74: ldur            x1, [fp, #-0x10]
    // 0x72cb78: ArrayStore: r0[0] = r1  ; List_4
    //     0x72cb78: stur            w1, [x0, #0x17]
    // 0x72cb7c: LeaveFrame
    //     0x72cb7c: mov             SP, fp
    //     0x72cb80: ldp             fp, lr, [SP], #0x10
    // 0x72cb84: ret
    //     0x72cb84: ret             
    // 0x72cb88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72cb88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72cb8c: b               #0x72c9ec
  }
}
