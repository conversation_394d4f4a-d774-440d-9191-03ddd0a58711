// lib: , url: package:nuonline/app/data/models/simple_regency.dart

// class id: 1050050, size: 0x8
class :: {
}

// class id: 1123, size: 0x14, field offset: 0x8
class SimpleRegency extends Object {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x9217cc, size: 0x188
    // 0x9217cc: EnterFrame
    //     0x9217cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9217d0: mov             fp, SP
    // 0x9217d4: AllocStack(0x20)
    //     0x9217d4: sub             SP, SP, #0x20
    // 0x9217d8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x9217d8: mov             x3, x1
    //     0x9217dc: stur            x1, [fp, #-8]
    // 0x9217e0: CheckStackOverflow
    //     0x9217e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9217e4: cmp             SP, x16
    //     0x9217e8: b.ls            #0x92194c
    // 0x9217ec: mov             x0, x3
    // 0x9217f0: r2 = Null
    //     0x9217f0: mov             x2, NULL
    // 0x9217f4: r1 = Null
    //     0x9217f4: mov             x1, NULL
    // 0x9217f8: cmp             w0, NULL
    // 0x9217fc: b.eq            #0x9218a0
    // 0x921800: branchIfSmi(r0, 0x9218a0)
    //     0x921800: tbz             w0, #0, #0x9218a0
    // 0x921804: r3 = LoadClassIdInstr(r0)
    //     0x921804: ldur            x3, [x0, #-1]
    //     0x921808: ubfx            x3, x3, #0xc, #0x14
    // 0x92180c: r17 = 6718
    //     0x92180c: movz            x17, #0x1a3e
    // 0x921810: cmp             x3, x17
    // 0x921814: b.eq            #0x9218a8
    // 0x921818: sub             x3, x3, #0x5a
    // 0x92181c: cmp             x3, #2
    // 0x921820: b.ls            #0x9218a8
    // 0x921824: r4 = LoadClassIdInstr(r0)
    //     0x921824: ldur            x4, [x0, #-1]
    //     0x921828: ubfx            x4, x4, #0xc, #0x14
    // 0x92182c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x921830: ldr             x3, [x3, #0x18]
    // 0x921834: ldr             x3, [x3, x4, lsl #3]
    // 0x921838: LoadField: r3 = r3->field_2b
    //     0x921838: ldur            w3, [x3, #0x2b]
    // 0x92183c: DecompressPointer r3
    //     0x92183c: add             x3, x3, HEAP, lsl #32
    // 0x921840: cmp             w3, NULL
    // 0x921844: b.eq            #0x9218a0
    // 0x921848: LoadField: r3 = r3->field_f
    //     0x921848: ldur            w3, [x3, #0xf]
    // 0x92184c: lsr             x3, x3, #3
    // 0x921850: r17 = 6718
    //     0x921850: movz            x17, #0x1a3e
    // 0x921854: cmp             x3, x17
    // 0x921858: b.eq            #0x9218a8
    // 0x92185c: r3 = SubtypeTestCache
    //     0x92185c: add             x3, PP, #0x40, lsl #12  ; [pp+0x403a0] SubtypeTestCache
    //     0x921860: ldr             x3, [x3, #0x3a0]
    // 0x921864: r30 = Subtype1TestCacheStub
    //     0x921864: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x921868: LoadField: r30 = r30->field_7
    //     0x921868: ldur            lr, [lr, #7]
    // 0x92186c: blr             lr
    // 0x921870: cmp             w7, NULL
    // 0x921874: b.eq            #0x921880
    // 0x921878: tbnz            w7, #4, #0x9218a0
    // 0x92187c: b               #0x9218a8
    // 0x921880: r8 = List
    //     0x921880: add             x8, PP, #0x40, lsl #12  ; [pp+0x403a8] Type: List
    //     0x921884: ldr             x8, [x8, #0x3a8]
    // 0x921888: r3 = SubtypeTestCache
    //     0x921888: add             x3, PP, #0x40, lsl #12  ; [pp+0x403b0] SubtypeTestCache
    //     0x92188c: ldr             x3, [x3, #0x3b0]
    // 0x921890: r30 = InstanceOfStub
    //     0x921890: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x921894: LoadField: r30 = r30->field_7
    //     0x921894: ldur            lr, [lr, #7]
    // 0x921898: blr             lr
    // 0x92189c: b               #0x9218ac
    // 0x9218a0: r0 = false
    //     0x9218a0: add             x0, NULL, #0x30  ; false
    // 0x9218a4: b               #0x9218ac
    // 0x9218a8: r0 = true
    //     0x9218a8: add             x0, NULL, #0x20  ; true
    // 0x9218ac: tbnz            w0, #4, #0x921930
    // 0x9218b0: ldur            x0, [fp, #-8]
    // 0x9218b4: r1 = Function '<anonymous closure>': static.
    //     0x9218b4: add             x1, PP, #0x40, lsl #12  ; [pp+0x403b8] AnonymousClosure: static (0x921954), in [package:nuonline/app/data/models/simple_regency.dart] SimpleRegency::fromResponse (0x9217cc)
    //     0x9218b8: ldr             x1, [x1, #0x3b8]
    // 0x9218bc: r2 = Null
    //     0x9218bc: mov             x2, NULL
    // 0x9218c0: r0 = AllocateClosure()
    //     0x9218c0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9218c4: mov             x1, x0
    // 0x9218c8: ldur            x0, [fp, #-8]
    // 0x9218cc: r2 = LoadClassIdInstr(r0)
    //     0x9218cc: ldur            x2, [x0, #-1]
    //     0x9218d0: ubfx            x2, x2, #0xc, #0x14
    // 0x9218d4: r16 = <SimpleRegency>
    //     0x9218d4: add             x16, PP, #0x35, lsl #12  ; [pp+0x359e8] TypeArguments: <SimpleRegency>
    //     0x9218d8: ldr             x16, [x16, #0x9e8]
    // 0x9218dc: stp             x0, x16, [SP, #8]
    // 0x9218e0: str             x1, [SP]
    // 0x9218e4: mov             x0, x2
    // 0x9218e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9218e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9218ec: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x9218ec: movz            x17, #0xf28c
    //     0x9218f0: add             lr, x0, x17
    //     0x9218f4: ldr             lr, [x21, lr, lsl #3]
    //     0x9218f8: blr             lr
    // 0x9218fc: r1 = LoadClassIdInstr(r0)
    //     0x9218fc: ldur            x1, [x0, #-1]
    //     0x921900: ubfx            x1, x1, #0xc, #0x14
    // 0x921904: mov             x16, x0
    // 0x921908: mov             x0, x1
    // 0x92190c: mov             x1, x16
    // 0x921910: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x921910: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x921914: r0 = GDT[cid_x0 + 0xd889]()
    //     0x921914: movz            x17, #0xd889
    //     0x921918: add             lr, x0, x17
    //     0x92191c: ldr             lr, [x21, lr, lsl #3]
    //     0x921920: blr             lr
    // 0x921924: LeaveFrame
    //     0x921924: mov             SP, fp
    //     0x921928: ldp             fp, lr, [SP], #0x10
    // 0x92192c: ret
    //     0x92192c: ret             
    // 0x921930: r1 = <SimpleRegency>
    //     0x921930: add             x1, PP, #0x35, lsl #12  ; [pp+0x359e8] TypeArguments: <SimpleRegency>
    //     0x921934: ldr             x1, [x1, #0x9e8]
    // 0x921938: r2 = 0
    //     0x921938: movz            x2, #0
    // 0x92193c: r0 = _GrowableList()
    //     0x92193c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x921940: LeaveFrame
    //     0x921940: mov             SP, fp
    //     0x921944: ldp             fp, lr, [SP], #0x10
    // 0x921948: ret
    //     0x921948: ret             
    // 0x92194c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92194c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x921950: b               #0x9217ec
  }
  [closure] static SimpleRegency <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x921954, size: 0x50
    // 0x921954: EnterFrame
    //     0x921954: stp             fp, lr, [SP, #-0x10]!
    //     0x921958: mov             fp, SP
    // 0x92195c: CheckStackOverflow
    //     0x92195c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x921960: cmp             SP, x16
    //     0x921964: b.ls            #0x92199c
    // 0x921968: ldr             x0, [fp, #0x10]
    // 0x92196c: r2 = Null
    //     0x92196c: mov             x2, NULL
    // 0x921970: r1 = Null
    //     0x921970: mov             x1, NULL
    // 0x921974: r8 = Map<String, dynamic>
    //     0x921974: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x921978: r3 = Null
    //     0x921978: add             x3, PP, #0x40, lsl #12  ; [pp+0x403c0] Null
    //     0x92197c: ldr             x3, [x3, #0x3c0]
    // 0x921980: r0 = Map<String, dynamic>()
    //     0x921980: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x921984: ldr             x2, [fp, #0x10]
    // 0x921988: r1 = Null
    //     0x921988: mov             x1, NULL
    // 0x92198c: r0 = SimpleRegency.fromMap()
    //     0x92198c: bl              #0x9219a4  ; [package:nuonline/app/data/models/simple_regency.dart] SimpleRegency::SimpleRegency.fromMap
    // 0x921990: LeaveFrame
    //     0x921990: mov             SP, fp
    //     0x921994: ldp             fp, lr, [SP], #0x10
    // 0x921998: ret
    //     0x921998: ret             
    // 0x92199c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92199c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9219a0: b               #0x921968
  }
  factory _ SimpleRegency.fromMap(/* No info */) {
    // ** addr: 0x9219a4, size: 0x160
    // 0x9219a4: EnterFrame
    //     0x9219a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9219a8: mov             fp, SP
    // 0x9219ac: AllocStack(0x20)
    //     0x9219ac: sub             SP, SP, #0x20
    // 0x9219b0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x9219b0: mov             x3, x2
    //     0x9219b4: stur            x2, [fp, #-8]
    // 0x9219b8: CheckStackOverflow
    //     0x9219b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9219bc: cmp             SP, x16
    //     0x9219c0: b.ls            #0x921afc
    // 0x9219c4: r0 = LoadClassIdInstr(r3)
    //     0x9219c4: ldur            x0, [x3, #-1]
    //     0x9219c8: ubfx            x0, x0, #0xc, #0x14
    // 0x9219cc: mov             x1, x3
    // 0x9219d0: r2 = "id"
    //     0x9219d0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x9219d4: ldr             x2, [x2, #0x740]
    // 0x9219d8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9219d8: sub             lr, x0, #0x114
    //     0x9219dc: ldr             lr, [x21, lr, lsl #3]
    //     0x9219e0: blr             lr
    // 0x9219e4: mov             x3, x0
    // 0x9219e8: r2 = Null
    //     0x9219e8: mov             x2, NULL
    // 0x9219ec: r1 = Null
    //     0x9219ec: mov             x1, NULL
    // 0x9219f0: stur            x3, [fp, #-0x10]
    // 0x9219f4: branchIfSmi(r0, 0x921a1c)
    //     0x9219f4: tbz             w0, #0, #0x921a1c
    // 0x9219f8: r4 = LoadClassIdInstr(r0)
    //     0x9219f8: ldur            x4, [x0, #-1]
    //     0x9219fc: ubfx            x4, x4, #0xc, #0x14
    // 0x921a00: sub             x4, x4, #0x3c
    // 0x921a04: cmp             x4, #1
    // 0x921a08: b.ls            #0x921a1c
    // 0x921a0c: r8 = int
    //     0x921a0c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x921a10: r3 = Null
    //     0x921a10: add             x3, PP, #0x40, lsl #12  ; [pp+0x403d0] Null
    //     0x921a14: ldr             x3, [x3, #0x3d0]
    // 0x921a18: r0 = int()
    //     0x921a18: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x921a1c: ldur            x3, [fp, #-8]
    // 0x921a20: r0 = LoadClassIdInstr(r3)
    //     0x921a20: ldur            x0, [x3, #-1]
    //     0x921a24: ubfx            x0, x0, #0xc, #0x14
    // 0x921a28: mov             x1, x3
    // 0x921a2c: r2 = "name"
    //     0x921a2c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x921a30: r0 = GDT[cid_x0 + -0x114]()
    //     0x921a30: sub             lr, x0, #0x114
    //     0x921a34: ldr             lr, [x21, lr, lsl #3]
    //     0x921a38: blr             lr
    // 0x921a3c: mov             x3, x0
    // 0x921a40: r2 = Null
    //     0x921a40: mov             x2, NULL
    // 0x921a44: r1 = Null
    //     0x921a44: mov             x1, NULL
    // 0x921a48: stur            x3, [fp, #-0x18]
    // 0x921a4c: r4 = 60
    //     0x921a4c: movz            x4, #0x3c
    // 0x921a50: branchIfSmi(r0, 0x921a5c)
    //     0x921a50: tbz             w0, #0, #0x921a5c
    // 0x921a54: r4 = LoadClassIdInstr(r0)
    //     0x921a54: ldur            x4, [x0, #-1]
    //     0x921a58: ubfx            x4, x4, #0xc, #0x14
    // 0x921a5c: sub             x4, x4, #0x5e
    // 0x921a60: cmp             x4, #1
    // 0x921a64: b.ls            #0x921a78
    // 0x921a68: r8 = String
    //     0x921a68: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x921a6c: r3 = Null
    //     0x921a6c: add             x3, PP, #0x40, lsl #12  ; [pp+0x403e0] Null
    //     0x921a70: ldr             x3, [x3, #0x3e0]
    // 0x921a74: r0 = String()
    //     0x921a74: bl              #0xed43b0  ; IsType_String_Stub
    // 0x921a78: ldur            x1, [fp, #-8]
    // 0x921a7c: r0 = LoadClassIdInstr(r1)
    //     0x921a7c: ldur            x0, [x1, #-1]
    //     0x921a80: ubfx            x0, x0, #0xc, #0x14
    // 0x921a84: r2 = "province_id"
    //     0x921a84: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x921a88: ldr             x2, [x2, #0x100]
    // 0x921a8c: r0 = GDT[cid_x0 + -0x114]()
    //     0x921a8c: sub             lr, x0, #0x114
    //     0x921a90: ldr             lr, [x21, lr, lsl #3]
    //     0x921a94: blr             lr
    // 0x921a98: r2 = Null
    //     0x921a98: mov             x2, NULL
    // 0x921a9c: r1 = Null
    //     0x921a9c: mov             x1, NULL
    // 0x921aa0: branchIfSmi(r0, 0x921ac8)
    //     0x921aa0: tbz             w0, #0, #0x921ac8
    // 0x921aa4: r4 = LoadClassIdInstr(r0)
    //     0x921aa4: ldur            x4, [x0, #-1]
    //     0x921aa8: ubfx            x4, x4, #0xc, #0x14
    // 0x921aac: sub             x4, x4, #0x3c
    // 0x921ab0: cmp             x4, #1
    // 0x921ab4: b.ls            #0x921ac8
    // 0x921ab8: r8 = int
    //     0x921ab8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x921abc: r3 = Null
    //     0x921abc: add             x3, PP, #0x40, lsl #12  ; [pp+0x403f0] Null
    //     0x921ac0: ldr             x3, [x3, #0x3f0]
    // 0x921ac4: r0 = int()
    //     0x921ac4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x921ac8: ldur            x0, [fp, #-0x10]
    // 0x921acc: r1 = LoadInt32Instr(r0)
    //     0x921acc: sbfx            x1, x0, #1, #0x1f
    //     0x921ad0: tbz             w0, #0, #0x921ad8
    //     0x921ad4: ldur            x1, [x0, #7]
    // 0x921ad8: stur            x1, [fp, #-0x20]
    // 0x921adc: r0 = SimpleRegency()
    //     0x921adc: bl              #0x921b04  ; AllocateSimpleRegencyStub -> SimpleRegency (size=0x14)
    // 0x921ae0: ldur            x1, [fp, #-0x20]
    // 0x921ae4: StoreField: r0->field_7 = r1
    //     0x921ae4: stur            x1, [x0, #7]
    // 0x921ae8: ldur            x1, [fp, #-0x18]
    // 0x921aec: StoreField: r0->field_f = r1
    //     0x921aec: stur            w1, [x0, #0xf]
    // 0x921af0: LeaveFrame
    //     0x921af0: mov             SP, fp
    //     0x921af4: ldp             fp, lr, [SP], #0x10
    // 0x921af8: ret
    //     0x921af8: ret             
    // 0x921afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x921afc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x921b00: b               #0x9219c4
  }
}
