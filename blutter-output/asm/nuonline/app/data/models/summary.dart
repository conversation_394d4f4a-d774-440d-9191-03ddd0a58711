// lib: , url: package:nuonline/app/data/models/summary.dart

// class id: 1050052, size: 0x8
class :: {
}

// class id: 1601, size: 0x3c, field offset: 0x14
class Summary extends HiveObject {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x91ad24, size: 0x180
    // 0x91ad24: EnterFrame
    //     0x91ad24: stp             fp, lr, [SP, #-0x10]!
    //     0x91ad28: mov             fp, SP
    // 0x91ad2c: AllocStack(0x20)
    //     0x91ad2c: sub             SP, SP, #0x20
    // 0x91ad30: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x91ad30: mov             x3, x1
    //     0x91ad34: stur            x1, [fp, #-8]
    // 0x91ad38: CheckStackOverflow
    //     0x91ad38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ad3c: cmp             SP, x16
    //     0x91ad40: b.ls            #0x91ae9c
    // 0x91ad44: mov             x0, x3
    // 0x91ad48: r2 = Null
    //     0x91ad48: mov             x2, NULL
    // 0x91ad4c: r1 = Null
    //     0x91ad4c: mov             x1, NULL
    // 0x91ad50: cmp             w0, NULL
    // 0x91ad54: b.eq            #0x91adf8
    // 0x91ad58: branchIfSmi(r0, 0x91adf8)
    //     0x91ad58: tbz             w0, #0, #0x91adf8
    // 0x91ad5c: r3 = LoadClassIdInstr(r0)
    //     0x91ad5c: ldur            x3, [x0, #-1]
    //     0x91ad60: ubfx            x3, x3, #0xc, #0x14
    // 0x91ad64: r17 = 6718
    //     0x91ad64: movz            x17, #0x1a3e
    // 0x91ad68: cmp             x3, x17
    // 0x91ad6c: b.eq            #0x91ae00
    // 0x91ad70: sub             x3, x3, #0x5a
    // 0x91ad74: cmp             x3, #2
    // 0x91ad78: b.ls            #0x91ae00
    // 0x91ad7c: r4 = LoadClassIdInstr(r0)
    //     0x91ad7c: ldur            x4, [x0, #-1]
    //     0x91ad80: ubfx            x4, x4, #0xc, #0x14
    // 0x91ad84: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x91ad88: ldr             x3, [x3, #0x18]
    // 0x91ad8c: ldr             x3, [x3, x4, lsl #3]
    // 0x91ad90: LoadField: r3 = r3->field_2b
    //     0x91ad90: ldur            w3, [x3, #0x2b]
    // 0x91ad94: DecompressPointer r3
    //     0x91ad94: add             x3, x3, HEAP, lsl #32
    // 0x91ad98: cmp             w3, NULL
    // 0x91ad9c: b.eq            #0x91adf8
    // 0x91ada0: LoadField: r3 = r3->field_f
    //     0x91ada0: ldur            w3, [x3, #0xf]
    // 0x91ada4: lsr             x3, x3, #3
    // 0x91ada8: r17 = 6718
    //     0x91ada8: movz            x17, #0x1a3e
    // 0x91adac: cmp             x3, x17
    // 0x91adb0: b.eq            #0x91ae00
    // 0x91adb4: r3 = SubtypeTestCache
    //     0x91adb4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db28] SubtypeTestCache
    //     0x91adb8: ldr             x3, [x3, #0xb28]
    // 0x91adbc: r30 = Subtype1TestCacheStub
    //     0x91adbc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x91adc0: LoadField: r30 = r30->field_7
    //     0x91adc0: ldur            lr, [lr, #7]
    // 0x91adc4: blr             lr
    // 0x91adc8: cmp             w7, NULL
    // 0x91adcc: b.eq            #0x91add8
    // 0x91add0: tbnz            w7, #4, #0x91adf8
    // 0x91add4: b               #0x91ae00
    // 0x91add8: r8 = List
    //     0x91add8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2db30] Type: List
    //     0x91addc: ldr             x8, [x8, #0xb30]
    // 0x91ade0: r3 = SubtypeTestCache
    //     0x91ade0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db38] SubtypeTestCache
    //     0x91ade4: ldr             x3, [x3, #0xb38]
    // 0x91ade8: r30 = InstanceOfStub
    //     0x91ade8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x91adec: LoadField: r30 = r30->field_7
    //     0x91adec: ldur            lr, [lr, #7]
    // 0x91adf0: blr             lr
    // 0x91adf4: b               #0x91ae04
    // 0x91adf8: r0 = false
    //     0x91adf8: add             x0, NULL, #0x30  ; false
    // 0x91adfc: b               #0x91ae04
    // 0x91ae00: r0 = true
    //     0x91ae00: add             x0, NULL, #0x20  ; true
    // 0x91ae04: tbnz            w0, #4, #0x91ae84
    // 0x91ae08: ldur            x0, [fp, #-8]
    // 0x91ae0c: r1 = Function '<anonymous closure>': static.
    //     0x91ae0c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2db40] AnonymousClosure: static (0x91aea4), in [package:nuonline/app/data/models/summary.dart] Summary::fromResponse (0x91ad24)
    //     0x91ae10: ldr             x1, [x1, #0xb40]
    // 0x91ae14: r2 = Null
    //     0x91ae14: mov             x2, NULL
    // 0x91ae18: r0 = AllocateClosure()
    //     0x91ae18: bl              #0xec1630  ; AllocateClosureStub
    // 0x91ae1c: mov             x1, x0
    // 0x91ae20: ldur            x0, [fp, #-8]
    // 0x91ae24: r2 = LoadClassIdInstr(r0)
    //     0x91ae24: ldur            x2, [x0, #-1]
    //     0x91ae28: ubfx            x2, x2, #0xc, #0x14
    // 0x91ae2c: r16 = <Summary>
    //     0x91ae2c: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91ae30: stp             x0, x16, [SP, #8]
    // 0x91ae34: str             x1, [SP]
    // 0x91ae38: mov             x0, x2
    // 0x91ae3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91ae3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91ae40: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x91ae40: movz            x17, #0xf28c
    //     0x91ae44: add             lr, x0, x17
    //     0x91ae48: ldr             lr, [x21, lr, lsl #3]
    //     0x91ae4c: blr             lr
    // 0x91ae50: r1 = LoadClassIdInstr(r0)
    //     0x91ae50: ldur            x1, [x0, #-1]
    //     0x91ae54: ubfx            x1, x1, #0xc, #0x14
    // 0x91ae58: mov             x16, x0
    // 0x91ae5c: mov             x0, x1
    // 0x91ae60: mov             x1, x16
    // 0x91ae64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91ae64: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91ae68: r0 = GDT[cid_x0 + 0xd889]()
    //     0x91ae68: movz            x17, #0xd889
    //     0x91ae6c: add             lr, x0, x17
    //     0x91ae70: ldr             lr, [x21, lr, lsl #3]
    //     0x91ae74: blr             lr
    // 0x91ae78: LeaveFrame
    //     0x91ae78: mov             SP, fp
    //     0x91ae7c: ldp             fp, lr, [SP], #0x10
    // 0x91ae80: ret
    //     0x91ae80: ret             
    // 0x91ae84: r1 = <Summary>
    //     0x91ae84: ldr             x1, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91ae88: r2 = 0
    //     0x91ae88: movz            x2, #0
    // 0x91ae8c: r0 = _GrowableList()
    //     0x91ae8c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x91ae90: LeaveFrame
    //     0x91ae90: mov             SP, fp
    //     0x91ae94: ldp             fp, lr, [SP], #0x10
    // 0x91ae98: ret
    //     0x91ae98: ret             
    // 0x91ae9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ae9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91aea0: b               #0x91ad44
  }
  [closure] static Summary <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x91aea4, size: 0x50
    // 0x91aea4: EnterFrame
    //     0x91aea4: stp             fp, lr, [SP, #-0x10]!
    //     0x91aea8: mov             fp, SP
    // 0x91aeac: CheckStackOverflow
    //     0x91aeac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91aeb0: cmp             SP, x16
    //     0x91aeb4: b.ls            #0x91aeec
    // 0x91aeb8: ldr             x0, [fp, #0x10]
    // 0x91aebc: r2 = Null
    //     0x91aebc: mov             x2, NULL
    // 0x91aec0: r1 = Null
    //     0x91aec0: mov             x1, NULL
    // 0x91aec4: r8 = Map<String, dynamic>
    //     0x91aec4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x91aec8: r3 = Null
    //     0x91aec8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db48] Null
    //     0x91aecc: ldr             x3, [x3, #0xb48]
    // 0x91aed0: r0 = Map<String, dynamic>()
    //     0x91aed0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x91aed4: ldr             x2, [fp, #0x10]
    // 0x91aed8: r1 = Null
    //     0x91aed8: mov             x1, NULL
    // 0x91aedc: r0 = Summary.fromMap()
    //     0x91aedc: bl              #0x91aef4  ; [package:nuonline/app/data/models/summary.dart] Summary::Summary.fromMap
    // 0x91aee0: LeaveFrame
    //     0x91aee0: mov             SP, fp
    //     0x91aee4: ldp             fp, lr, [SP], #0x10
    // 0x91aee8: ret
    //     0x91aee8: ret             
    // 0x91aeec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91aeec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91aef0: b               #0x91aeb8
  }
  factory _ Summary.fromMap(/* No info */) {
    // ** addr: 0x91aef4, size: 0x3cc
    // 0x91aef4: EnterFrame
    //     0x91aef4: stp             fp, lr, [SP, #-0x10]!
    //     0x91aef8: mov             fp, SP
    // 0x91aefc: AllocStack(0x60)
    //     0x91aefc: sub             SP, SP, #0x60
    // 0x91af00: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x91af00: mov             x3, x2
    //     0x91af04: stur            x2, [fp, #-8]
    // 0x91af08: CheckStackOverflow
    //     0x91af08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91af0c: cmp             SP, x16
    //     0x91af10: b.ls            #0x91b2b8
    // 0x91af14: r0 = LoadClassIdInstr(r3)
    //     0x91af14: ldur            x0, [x3, #-1]
    //     0x91af18: ubfx            x0, x0, #0xc, #0x14
    // 0x91af1c: mov             x1, x3
    // 0x91af20: r2 = "order"
    //     0x91af20: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x91af24: ldr             x2, [x2, #0xb78]
    // 0x91af28: r0 = GDT[cid_x0 + -0x114]()
    //     0x91af28: sub             lr, x0, #0x114
    //     0x91af2c: ldr             lr, [x21, lr, lsl #3]
    //     0x91af30: blr             lr
    // 0x91af34: mov             x3, x0
    // 0x91af38: r2 = Null
    //     0x91af38: mov             x2, NULL
    // 0x91af3c: r1 = Null
    //     0x91af3c: mov             x1, NULL
    // 0x91af40: stur            x3, [fp, #-0x10]
    // 0x91af44: branchIfSmi(r0, 0x91af6c)
    //     0x91af44: tbz             w0, #0, #0x91af6c
    // 0x91af48: r4 = LoadClassIdInstr(r0)
    //     0x91af48: ldur            x4, [x0, #-1]
    //     0x91af4c: ubfx            x4, x4, #0xc, #0x14
    // 0x91af50: sub             x4, x4, #0x3c
    // 0x91af54: cmp             x4, #1
    // 0x91af58: b.ls            #0x91af6c
    // 0x91af5c: r8 = int
    //     0x91af5c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x91af60: r3 = Null
    //     0x91af60: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db58] Null
    //     0x91af64: ldr             x3, [x3, #0xb58]
    // 0x91af68: r0 = int()
    //     0x91af68: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91af6c: ldur            x3, [fp, #-8]
    // 0x91af70: r0 = LoadClassIdInstr(r3)
    //     0x91af70: ldur            x0, [x3, #-1]
    //     0x91af74: ubfx            x0, x0, #0xc, #0x14
    // 0x91af78: mov             x1, x3
    // 0x91af7c: r2 = "title"
    //     0x91af7c: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x91af80: ldr             x2, [x2, #0x748]
    // 0x91af84: r0 = GDT[cid_x0 + -0x114]()
    //     0x91af84: sub             lr, x0, #0x114
    //     0x91af88: ldr             lr, [x21, lr, lsl #3]
    //     0x91af8c: blr             lr
    // 0x91af90: mov             x3, x0
    // 0x91af94: r2 = Null
    //     0x91af94: mov             x2, NULL
    // 0x91af98: r1 = Null
    //     0x91af98: mov             x1, NULL
    // 0x91af9c: stur            x3, [fp, #-0x18]
    // 0x91afa0: r4 = 60
    //     0x91afa0: movz            x4, #0x3c
    // 0x91afa4: branchIfSmi(r0, 0x91afb0)
    //     0x91afa4: tbz             w0, #0, #0x91afb0
    // 0x91afa8: r4 = LoadClassIdInstr(r0)
    //     0x91afa8: ldur            x4, [x0, #-1]
    //     0x91afac: ubfx            x4, x4, #0xc, #0x14
    // 0x91afb0: sub             x4, x4, #0x5e
    // 0x91afb4: cmp             x4, #1
    // 0x91afb8: b.ls            #0x91afcc
    // 0x91afbc: r8 = String?
    //     0x91afbc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91afc0: r3 = Null
    //     0x91afc0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db68] Null
    //     0x91afc4: ldr             x3, [x3, #0xb68]
    // 0x91afc8: r0 = String?()
    //     0x91afc8: bl              #0x600324  ; IsType_String?_Stub
    // 0x91afcc: ldur            x3, [fp, #-8]
    // 0x91afd0: r0 = LoadClassIdInstr(r3)
    //     0x91afd0: ldur            x0, [x3, #-1]
    //     0x91afd4: ubfx            x0, x0, #0xc, #0x14
    // 0x91afd8: mov             x1, x3
    // 0x91afdc: r2 = "type"
    //     0x91afdc: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x91afe0: r0 = GDT[cid_x0 + -0x114]()
    //     0x91afe0: sub             lr, x0, #0x114
    //     0x91afe4: ldr             lr, [x21, lr, lsl #3]
    //     0x91afe8: blr             lr
    // 0x91afec: mov             x3, x0
    // 0x91aff0: r2 = Null
    //     0x91aff0: mov             x2, NULL
    // 0x91aff4: r1 = Null
    //     0x91aff4: mov             x1, NULL
    // 0x91aff8: stur            x3, [fp, #-0x20]
    // 0x91affc: r4 = 60
    //     0x91affc: movz            x4, #0x3c
    // 0x91b000: branchIfSmi(r0, 0x91b00c)
    //     0x91b000: tbz             w0, #0, #0x91b00c
    // 0x91b004: r4 = LoadClassIdInstr(r0)
    //     0x91b004: ldur            x4, [x0, #-1]
    //     0x91b008: ubfx            x4, x4, #0xc, #0x14
    // 0x91b00c: sub             x4, x4, #0x5e
    // 0x91b010: cmp             x4, #1
    // 0x91b014: b.ls            #0x91b028
    // 0x91b018: r8 = String
    //     0x91b018: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91b01c: r3 = Null
    //     0x91b01c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db78] Null
    //     0x91b020: ldr             x3, [x3, #0xb78]
    // 0x91b024: r0 = String()
    //     0x91b024: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91b028: ldur            x3, [fp, #-8]
    // 0x91b02c: r0 = LoadClassIdInstr(r3)
    //     0x91b02c: ldur            x0, [x3, #-1]
    //     0x91b030: ubfx            x0, x0, #0xc, #0x14
    // 0x91b034: mov             x1, x3
    // 0x91b038: r2 = "layout"
    //     0x91b038: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2db88] "layout"
    //     0x91b03c: ldr             x2, [x2, #0xb88]
    // 0x91b040: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b040: sub             lr, x0, #0x114
    //     0x91b044: ldr             lr, [x21, lr, lsl #3]
    //     0x91b048: blr             lr
    // 0x91b04c: mov             x3, x0
    // 0x91b050: r2 = Null
    //     0x91b050: mov             x2, NULL
    // 0x91b054: r1 = Null
    //     0x91b054: mov             x1, NULL
    // 0x91b058: stur            x3, [fp, #-0x28]
    // 0x91b05c: r4 = 60
    //     0x91b05c: movz            x4, #0x3c
    // 0x91b060: branchIfSmi(r0, 0x91b06c)
    //     0x91b060: tbz             w0, #0, #0x91b06c
    // 0x91b064: r4 = LoadClassIdInstr(r0)
    //     0x91b064: ldur            x4, [x0, #-1]
    //     0x91b068: ubfx            x4, x4, #0xc, #0x14
    // 0x91b06c: sub             x4, x4, #0x5e
    // 0x91b070: cmp             x4, #1
    // 0x91b074: b.ls            #0x91b088
    // 0x91b078: r8 = String
    //     0x91b078: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91b07c: r3 = Null
    //     0x91b07c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2db90] Null
    //     0x91b080: ldr             x3, [x3, #0xb90]
    // 0x91b084: r0 = String()
    //     0x91b084: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91b088: ldur            x3, [fp, #-8]
    // 0x91b08c: r0 = LoadClassIdInstr(r3)
    //     0x91b08c: ldur            x0, [x3, #-1]
    //     0x91b090: ubfx            x0, x0, #0xc, #0x14
    // 0x91b094: mov             x1, x3
    // 0x91b098: r2 = "items"
    //     0x91b098: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dba0] "items"
    //     0x91b09c: ldr             x2, [x2, #0xba0]
    // 0x91b0a0: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b0a0: sub             lr, x0, #0x114
    //     0x91b0a4: ldr             lr, [x21, lr, lsl #3]
    //     0x91b0a8: blr             lr
    // 0x91b0ac: mov             x2, x0
    // 0x91b0b0: r1 = Instance_JsonCodec
    //     0x91b0b0: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x91b0b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91b0b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91b0b8: r0 = encode()
    //     0x91b0b8: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x91b0bc: mov             x4, x0
    // 0x91b0c0: ldur            x3, [fp, #-8]
    // 0x91b0c4: stur            x4, [fp, #-0x30]
    // 0x91b0c8: r0 = LoadClassIdInstr(r3)
    //     0x91b0c8: ldur            x0, [x3, #-1]
    //     0x91b0cc: ubfx            x0, x0, #0xc, #0x14
    // 0x91b0d0: mov             x1, x3
    // 0x91b0d4: r2 = "route"
    //     0x91b0d4: add             x2, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x91b0d8: ldr             x2, [x2, #0x3a0]
    // 0x91b0dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b0dc: sub             lr, x0, #0x114
    //     0x91b0e0: ldr             lr, [x21, lr, lsl #3]
    //     0x91b0e4: blr             lr
    // 0x91b0e8: mov             x3, x0
    // 0x91b0ec: r2 = Null
    //     0x91b0ec: mov             x2, NULL
    // 0x91b0f0: r1 = Null
    //     0x91b0f0: mov             x1, NULL
    // 0x91b0f4: stur            x3, [fp, #-0x38]
    // 0x91b0f8: r4 = 60
    //     0x91b0f8: movz            x4, #0x3c
    // 0x91b0fc: branchIfSmi(r0, 0x91b108)
    //     0x91b0fc: tbz             w0, #0, #0x91b108
    // 0x91b100: r4 = LoadClassIdInstr(r0)
    //     0x91b100: ldur            x4, [x0, #-1]
    //     0x91b104: ubfx            x4, x4, #0xc, #0x14
    // 0x91b108: sub             x4, x4, #0x5e
    // 0x91b10c: cmp             x4, #1
    // 0x91b110: b.ls            #0x91b124
    // 0x91b114: r8 = String?
    //     0x91b114: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91b118: r3 = Null
    //     0x91b118: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dba8] Null
    //     0x91b11c: ldr             x3, [x3, #0xba8]
    // 0x91b120: r0 = String?()
    //     0x91b120: bl              #0x600324  ; IsType_String?_Stub
    // 0x91b124: ldur            x3, [fp, #-8]
    // 0x91b128: r0 = LoadClassIdInstr(r3)
    //     0x91b128: ldur            x0, [x3, #-1]
    //     0x91b12c: ubfx            x0, x0, #0xc, #0x14
    // 0x91b130: mov             x1, x3
    // 0x91b134: r2 = "params"
    //     0x91b134: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x91b138: ldr             x2, [x2, #0x3b8]
    // 0x91b13c: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b13c: sub             lr, x0, #0x114
    //     0x91b140: ldr             lr, [x21, lr, lsl #3]
    //     0x91b144: blr             lr
    // 0x91b148: mov             x3, x0
    // 0x91b14c: r2 = Null
    //     0x91b14c: mov             x2, NULL
    // 0x91b150: r1 = Null
    //     0x91b150: mov             x1, NULL
    // 0x91b154: stur            x3, [fp, #-0x40]
    // 0x91b158: r8 = Map<String, String>?
    //     0x91b158: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dbb8] Type: Map<String, String>?
    //     0x91b15c: ldr             x8, [x8, #0xbb8]
    // 0x91b160: r3 = Null
    //     0x91b160: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbc0] Null
    //     0x91b164: ldr             x3, [x3, #0xbc0]
    // 0x91b168: r0 = Map<String, String>?()
    //     0x91b168: bl              #0x70bdd4  ; IsType_Map<String, String>?_Stub
    // 0x91b16c: ldur            x3, [fp, #-8]
    // 0x91b170: r0 = LoadClassIdInstr(r3)
    //     0x91b170: ldur            x0, [x3, #-1]
    //     0x91b174: ubfx            x0, x0, #0xc, #0x14
    // 0x91b178: mov             x1, x3
    // 0x91b17c: r2 = "thumbnail_url"
    //     0x91b17c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dbd0] "thumbnail_url"
    //     0x91b180: ldr             x2, [x2, #0xbd0]
    // 0x91b184: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b184: sub             lr, x0, #0x114
    //     0x91b188: ldr             lr, [x21, lr, lsl #3]
    //     0x91b18c: blr             lr
    // 0x91b190: mov             x3, x0
    // 0x91b194: r2 = Null
    //     0x91b194: mov             x2, NULL
    // 0x91b198: r1 = Null
    //     0x91b198: mov             x1, NULL
    // 0x91b19c: stur            x3, [fp, #-0x48]
    // 0x91b1a0: r4 = 60
    //     0x91b1a0: movz            x4, #0x3c
    // 0x91b1a4: branchIfSmi(r0, 0x91b1b0)
    //     0x91b1a4: tbz             w0, #0, #0x91b1b0
    // 0x91b1a8: r4 = LoadClassIdInstr(r0)
    //     0x91b1a8: ldur            x4, [x0, #-1]
    //     0x91b1ac: ubfx            x4, x4, #0xc, #0x14
    // 0x91b1b0: sub             x4, x4, #0x5e
    // 0x91b1b4: cmp             x4, #1
    // 0x91b1b8: b.ls            #0x91b1cc
    // 0x91b1bc: r8 = String?
    //     0x91b1bc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91b1c0: r3 = Null
    //     0x91b1c0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbd8] Null
    //     0x91b1c4: ldr             x3, [x3, #0xbd8]
    // 0x91b1c8: r0 = String?()
    //     0x91b1c8: bl              #0x600324  ; IsType_String?_Stub
    // 0x91b1cc: ldur            x1, [fp, #-8]
    // 0x91b1d0: r0 = LoadClassIdInstr(r1)
    //     0x91b1d0: ldur            x0, [x1, #-1]
    //     0x91b1d4: ubfx            x0, x0, #0xc, #0x14
    // 0x91b1d8: r2 = "options"
    //     0x91b1d8: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b6d8] "options"
    //     0x91b1dc: ldr             x2, [x2, #0x6d8]
    // 0x91b1e0: r0 = GDT[cid_x0 + -0x114]()
    //     0x91b1e0: sub             lr, x0, #0x114
    //     0x91b1e4: ldr             lr, [x21, lr, lsl #3]
    //     0x91b1e8: blr             lr
    // 0x91b1ec: mov             x3, x0
    // 0x91b1f0: r2 = Null
    //     0x91b1f0: mov             x2, NULL
    // 0x91b1f4: r1 = Null
    //     0x91b1f4: mov             x1, NULL
    // 0x91b1f8: stur            x3, [fp, #-8]
    // 0x91b1fc: r8 = Map<String, dynamic>?
    //     0x91b1fc: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0x91b200: r3 = Null
    //     0x91b200: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dbe8] Null
    //     0x91b204: ldr             x3, [x3, #0xbe8]
    // 0x91b208: r0 = Map<String, dynamic>?()
    //     0x91b208: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0x91b20c: ldur            x0, [fp, #-0x10]
    // 0x91b210: r1 = LoadInt32Instr(r0)
    //     0x91b210: sbfx            x1, x0, #1, #0x1f
    //     0x91b214: tbz             w0, #0, #0x91b21c
    //     0x91b218: ldur            x1, [x0, #7]
    // 0x91b21c: stur            x1, [fp, #-0x50]
    // 0x91b220: r0 = Summary()
    //     0x91b220: bl              #0x91b5e0  ; AllocateSummaryStub -> Summary (size=0x3c)
    // 0x91b224: mov             x1, x0
    // 0x91b228: ldur            x0, [fp, #-0x50]
    // 0x91b22c: stur            x1, [fp, #-0x10]
    // 0x91b230: StoreField: r1->field_13 = r0
    //     0x91b230: stur            x0, [x1, #0x13]
    // 0x91b234: ldur            x0, [fp, #-0x20]
    // 0x91b238: StoreField: r1->field_1f = r0
    //     0x91b238: stur            w0, [x1, #0x1f]
    // 0x91b23c: ldur            x0, [fp, #-0x28]
    // 0x91b240: StoreField: r1->field_23 = r0
    //     0x91b240: stur            w0, [x1, #0x23]
    // 0x91b244: ldur            x0, [fp, #-0x30]
    // 0x91b248: StoreField: r1->field_27 = r0
    //     0x91b248: stur            w0, [x1, #0x27]
    // 0x91b24c: ldur            x0, [fp, #-0x18]
    // 0x91b250: StoreField: r1->field_1b = r0
    //     0x91b250: stur            w0, [x1, #0x1b]
    // 0x91b254: ldur            x0, [fp, #-0x38]
    // 0x91b258: StoreField: r1->field_2b = r0
    //     0x91b258: stur            w0, [x1, #0x2b]
    // 0x91b25c: ldur            x0, [fp, #-0x40]
    // 0x91b260: StoreField: r1->field_2f = r0
    //     0x91b260: stur            w0, [x1, #0x2f]
    // 0x91b264: ldur            x0, [fp, #-0x48]
    // 0x91b268: StoreField: r1->field_33 = r0
    //     0x91b268: stur            w0, [x1, #0x33]
    // 0x91b26c: ldur            x0, [fp, #-8]
    // 0x91b270: StoreField: r1->field_37 = r0
    //     0x91b270: stur            w0, [x1, #0x37]
    // 0x91b274: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x91b274: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x91b278: ldr             x16, [x16, #0x9f8]
    // 0x91b27c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x91b280: stp             lr, x16, [SP]
    // 0x91b284: r0 = Map._fromLiteral()
    //     0x91b284: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x91b288: ldur            x1, [fp, #-0x10]
    // 0x91b28c: StoreField: r1->field_f = r0
    //     0x91b28c: stur            w0, [x1, #0xf]
    //     0x91b290: ldurb           w16, [x1, #-1]
    //     0x91b294: ldurb           w17, [x0, #-1]
    //     0x91b298: and             x16, x17, x16, lsr #2
    //     0x91b29c: tst             x16, HEAP, lsr #32
    //     0x91b2a0: b.eq            #0x91b2a8
    //     0x91b2a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x91b2a8: mov             x0, x1
    // 0x91b2ac: LeaveFrame
    //     0x91b2ac: mov             SP, fp
    //     0x91b2b0: ldp             fp, lr, [SP], #0x10
    // 0x91b2b4: ret
    //     0x91b2b4: ret             
    // 0x91b2b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b2b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b2bc: b               #0x91af14
  }
  _ Summary(/* No info */) {
    // ** addr: 0x91b2c0, size: 0x320
    // 0x91b2c0: EnterFrame
    //     0x91b2c0: stp             fp, lr, [SP, #-0x10]!
    //     0x91b2c4: mov             fp, SP
    // 0x91b2c8: AllocStack(0x18)
    //     0x91b2c8: sub             SP, SP, #0x18
    // 0x91b2cc: SetupParameters(Summary this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r1 */, dynamic _ /* r6 => r0 */, {dynamic options = Null /* r8 */, dynamic params = Null /* r9 */, dynamic route = Null /* r10 */, dynamic thumbnailUrl = Null /* r11 */, dynamic title = Null /* r4 */})
    //     0x91b2cc: stur            x1, [fp, #-8]
    //     0x91b2d0: mov             x16, x3
    //     0x91b2d4: mov             x3, x1
    //     0x91b2d8: mov             x1, x16
    //     0x91b2dc: mov             x0, x6
    //     0x91b2e0: ldur            w6, [x4, #0x13]
    //     0x91b2e4: ldur            w7, [x4, #0x1f]
    //     0x91b2e8: add             x7, x7, HEAP, lsl #32
    //     0x91b2ec: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b6d8] "options"
    //     0x91b2f0: ldr             x16, [x16, #0x6d8]
    //     0x91b2f4: cmp             w7, w16
    //     0x91b2f8: b.ne            #0x91b31c
    //     0x91b2fc: ldur            w7, [x4, #0x23]
    //     0x91b300: add             x7, x7, HEAP, lsl #32
    //     0x91b304: sub             w8, w6, w7
    //     0x91b308: add             x7, fp, w8, sxtw #2
    //     0x91b30c: ldr             x7, [x7, #8]
    //     0x91b310: mov             x8, x7
    //     0x91b314: movz            x7, #0x1
    //     0x91b318: b               #0x91b324
    //     0x91b31c: mov             x8, NULL
    //     0x91b320: movz            x7, #0
    //     0x91b324: lsl             x9, x7, #1
    //     0x91b328: lsl             w10, w9, #1
    //     0x91b32c: add             w11, w10, #8
    //     0x91b330: add             x16, x4, w11, sxtw #1
    //     0x91b334: ldur            w12, [x16, #0xf]
    //     0x91b338: add             x12, x12, HEAP, lsl #32
    //     0x91b33c: add             x16, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x91b340: ldr             x16, [x16, #0x3b8]
    //     0x91b344: cmp             w12, w16
    //     0x91b348: b.ne            #0x91b37c
    //     0x91b34c: add             w7, w10, #0xa
    //     0x91b350: add             x16, x4, w7, sxtw #1
    //     0x91b354: ldur            w10, [x16, #0xf]
    //     0x91b358: add             x10, x10, HEAP, lsl #32
    //     0x91b35c: sub             w7, w6, w10
    //     0x91b360: add             x10, fp, w7, sxtw #2
    //     0x91b364: ldr             x10, [x10, #8]
    //     0x91b368: add             w7, w9, #2
    //     0x91b36c: sbfx            x9, x7, #1, #0x1f
    //     0x91b370: mov             x7, x9
    //     0x91b374: mov             x9, x10
    //     0x91b378: b               #0x91b380
    //     0x91b37c: mov             x9, NULL
    //     0x91b380: lsl             x10, x7, #1
    //     0x91b384: lsl             w11, w10, #1
    //     0x91b388: add             w12, w11, #8
    //     0x91b38c: add             x16, x4, w12, sxtw #1
    //     0x91b390: ldur            w13, [x16, #0xf]
    //     0x91b394: add             x13, x13, HEAP, lsl #32
    //     0x91b398: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x91b39c: ldr             x16, [x16, #0x3a0]
    //     0x91b3a0: cmp             w13, w16
    //     0x91b3a4: b.ne            #0x91b3d8
    //     0x91b3a8: add             w7, w11, #0xa
    //     0x91b3ac: add             x16, x4, w7, sxtw #1
    //     0x91b3b0: ldur            w11, [x16, #0xf]
    //     0x91b3b4: add             x11, x11, HEAP, lsl #32
    //     0x91b3b8: sub             w7, w6, w11
    //     0x91b3bc: add             x11, fp, w7, sxtw #2
    //     0x91b3c0: ldr             x11, [x11, #8]
    //     0x91b3c4: add             w7, w10, #2
    //     0x91b3c8: sbfx            x10, x7, #1, #0x1f
    //     0x91b3cc: mov             x7, x10
    //     0x91b3d0: mov             x10, x11
    //     0x91b3d4: b               #0x91b3dc
    //     0x91b3d8: mov             x10, NULL
    //     0x91b3dc: lsl             x11, x7, #1
    //     0x91b3e0: lsl             w12, w11, #1
    //     0x91b3e4: add             w13, w12, #8
    //     0x91b3e8: add             x16, x4, w13, sxtw #1
    //     0x91b3ec: ldur            w14, [x16, #0xf]
    //     0x91b3f0: add             x14, x14, HEAP, lsl #32
    //     0x91b3f4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2ddf8] "thumbnailUrl"
    //     0x91b3f8: ldr             x16, [x16, #0xdf8]
    //     0x91b3fc: cmp             w14, w16
    //     0x91b400: b.ne            #0x91b434
    //     0x91b404: add             w7, w12, #0xa
    //     0x91b408: add             x16, x4, w7, sxtw #1
    //     0x91b40c: ldur            w12, [x16, #0xf]
    //     0x91b410: add             x12, x12, HEAP, lsl #32
    //     0x91b414: sub             w7, w6, w12
    //     0x91b418: add             x12, fp, w7, sxtw #2
    //     0x91b41c: ldr             x12, [x12, #8]
    //     0x91b420: add             w7, w11, #2
    //     0x91b424: sbfx            x11, x7, #1, #0x1f
    //     0x91b428: mov             x7, x11
    //     0x91b42c: mov             x11, x12
    //     0x91b430: b               #0x91b438
    //     0x91b434: mov             x11, NULL
    //     0x91b438: lsl             x12, x7, #1
    //     0x91b43c: lsl             w7, w12, #1
    //     0x91b440: add             w12, w7, #8
    //     0x91b444: add             x16, x4, w12, sxtw #1
    //     0x91b448: ldur            w13, [x16, #0xf]
    //     0x91b44c: add             x13, x13, HEAP, lsl #32
    //     0x91b450: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x91b454: ldr             x16, [x16, #0x748]
    //     0x91b458: cmp             w13, w16
    //     0x91b45c: b.ne            #0x91b484
    //     0x91b460: add             w12, w7, #0xa
    //     0x91b464: add             x16, x4, w12, sxtw #1
    //     0x91b468: ldur            w7, [x16, #0xf]
    //     0x91b46c: add             x7, x7, HEAP, lsl #32
    //     0x91b470: sub             w4, w6, w7
    //     0x91b474: add             x6, fp, w4, sxtw #2
    //     0x91b478: ldr             x6, [x6, #8]
    //     0x91b47c: mov             x4, x6
    //     0x91b480: b               #0x91b488
    //     0x91b484: mov             x4, NULL
    // 0x91b488: CheckStackOverflow
    //     0x91b488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b48c: cmp             SP, x16
    //     0x91b490: b.ls            #0x91b5d8
    // 0x91b494: StoreField: r3->field_13 = r5
    //     0x91b494: stur            x5, [x3, #0x13]
    // 0x91b498: StoreField: r3->field_1f = r0
    //     0x91b498: stur            w0, [x3, #0x1f]
    //     0x91b49c: ldurb           w16, [x3, #-1]
    //     0x91b4a0: ldurb           w17, [x0, #-1]
    //     0x91b4a4: and             x16, x17, x16, lsr #2
    //     0x91b4a8: tst             x16, HEAP, lsr #32
    //     0x91b4ac: b.eq            #0x91b4b4
    //     0x91b4b0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b4b4: mov             x0, x1
    // 0x91b4b8: StoreField: r3->field_23 = r0
    //     0x91b4b8: stur            w0, [x3, #0x23]
    //     0x91b4bc: ldurb           w16, [x3, #-1]
    //     0x91b4c0: ldurb           w17, [x0, #-1]
    //     0x91b4c4: and             x16, x17, x16, lsr #2
    //     0x91b4c8: tst             x16, HEAP, lsr #32
    //     0x91b4cc: b.eq            #0x91b4d4
    //     0x91b4d0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b4d4: mov             x0, x2
    // 0x91b4d8: StoreField: r3->field_27 = r0
    //     0x91b4d8: stur            w0, [x3, #0x27]
    //     0x91b4dc: ldurb           w16, [x3, #-1]
    //     0x91b4e0: ldurb           w17, [x0, #-1]
    //     0x91b4e4: and             x16, x17, x16, lsr #2
    //     0x91b4e8: tst             x16, HEAP, lsr #32
    //     0x91b4ec: b.eq            #0x91b4f4
    //     0x91b4f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b4f4: mov             x0, x4
    // 0x91b4f8: StoreField: r3->field_1b = r0
    //     0x91b4f8: stur            w0, [x3, #0x1b]
    //     0x91b4fc: ldurb           w16, [x3, #-1]
    //     0x91b500: ldurb           w17, [x0, #-1]
    //     0x91b504: and             x16, x17, x16, lsr #2
    //     0x91b508: tst             x16, HEAP, lsr #32
    //     0x91b50c: b.eq            #0x91b514
    //     0x91b510: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b514: mov             x0, x10
    // 0x91b518: StoreField: r3->field_2b = r0
    //     0x91b518: stur            w0, [x3, #0x2b]
    //     0x91b51c: ldurb           w16, [x3, #-1]
    //     0x91b520: ldurb           w17, [x0, #-1]
    //     0x91b524: and             x16, x17, x16, lsr #2
    //     0x91b528: tst             x16, HEAP, lsr #32
    //     0x91b52c: b.eq            #0x91b534
    //     0x91b530: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b534: mov             x0, x9
    // 0x91b538: StoreField: r3->field_2f = r0
    //     0x91b538: stur            w0, [x3, #0x2f]
    //     0x91b53c: ldurb           w16, [x3, #-1]
    //     0x91b540: ldurb           w17, [x0, #-1]
    //     0x91b544: and             x16, x17, x16, lsr #2
    //     0x91b548: tst             x16, HEAP, lsr #32
    //     0x91b54c: b.eq            #0x91b554
    //     0x91b550: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b554: mov             x0, x11
    // 0x91b558: StoreField: r3->field_33 = r0
    //     0x91b558: stur            w0, [x3, #0x33]
    //     0x91b55c: ldurb           w16, [x3, #-1]
    //     0x91b560: ldurb           w17, [x0, #-1]
    //     0x91b564: and             x16, x17, x16, lsr #2
    //     0x91b568: tst             x16, HEAP, lsr #32
    //     0x91b56c: b.eq            #0x91b574
    //     0x91b570: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b574: mov             x0, x8
    // 0x91b578: StoreField: r3->field_37 = r0
    //     0x91b578: stur            w0, [x3, #0x37]
    //     0x91b57c: ldurb           w16, [x3, #-1]
    //     0x91b580: ldurb           w17, [x0, #-1]
    //     0x91b584: and             x16, x17, x16, lsr #2
    //     0x91b588: tst             x16, HEAP, lsr #32
    //     0x91b58c: b.eq            #0x91b594
    //     0x91b590: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x91b594: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x91b594: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x91b598: ldr             x16, [x16, #0x9f8]
    // 0x91b59c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x91b5a0: stp             lr, x16, [SP]
    // 0x91b5a4: r0 = Map._fromLiteral()
    //     0x91b5a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x91b5a8: ldur            x1, [fp, #-8]
    // 0x91b5ac: StoreField: r1->field_f = r0
    //     0x91b5ac: stur            w0, [x1, #0xf]
    //     0x91b5b0: ldurb           w16, [x1, #-1]
    //     0x91b5b4: ldurb           w17, [x0, #-1]
    //     0x91b5b8: and             x16, x17, x16, lsr #2
    //     0x91b5bc: tst             x16, HEAP, lsr #32
    //     0x91b5c0: b.eq            #0x91b5c8
    //     0x91b5c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x91b5c8: r0 = Null
    //     0x91b5c8: mov             x0, NULL
    // 0x91b5cc: LeaveFrame
    //     0x91b5cc: mov             SP, fp
    //     0x91b5d0: ldp             fp, lr, [SP], #0x10
    // 0x91b5d4: ret
    //     0x91b5d4: ret             
    // 0x91b5d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b5d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b5dc: b               #0x91b494
  }
  List<Y0> itemsBuilder<Y0>(Summary, (dynamic, Map<String, dynamic>) => Y0, ((dynamic, List<Y0>) => void)?) {
    // ** addr: 0xb9d870, size: 0x160
    // 0xb9d870: EnterFrame
    //     0xb9d870: stp             fp, lr, [SP, #-0x10]!
    //     0xb9d874: mov             fp, SP
    // 0xb9d878: AllocStack(0x30)
    //     0xb9d878: sub             SP, SP, #0x30
    // 0xb9d87c: SetupParameters()
    //     0xb9d87c: ldur            w0, [x4, #0xf]
    //     0xb9d880: cbnz            w0, #0xb9d88c
    //     0xb9d884: mov             x3, NULL
    //     0xb9d888: b               #0xb9d89c
    //     0xb9d88c: ldur            w0, [x4, #0x17]
    //     0xb9d890: add             x1, fp, w0, sxtw #2
    //     0xb9d894: ldr             x1, [x1, #0x10]
    //     0xb9d898: mov             x3, x1
    //     0xb9d89c: ldr             x2, [fp, #0x20]
    //     0xb9d8a0: ldr             x1, [fp, #0x18]
    //     0xb9d8a4: ldr             x0, [fp, #0x10]
    //     0xb9d8a8: stur            x3, [fp, #-8]
    // 0xb9d8ac: CheckStackOverflow
    //     0xb9d8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9d8b0: cmp             SP, x16
    //     0xb9d8b4: b.ls            #0xb9d9c8
    // 0xb9d8b8: r1 = 1
    //     0xb9d8b8: movz            x1, #0x1
    // 0xb9d8bc: r0 = AllocateContext()
    //     0xb9d8bc: bl              #0xec126c  ; AllocateContextStub
    // 0xb9d8c0: mov             x3, x0
    // 0xb9d8c4: ldr             x0, [fp, #0x18]
    // 0xb9d8c8: stur            x3, [fp, #-0x10]
    // 0xb9d8cc: StoreField: r3->field_f = r0
    //     0xb9d8cc: stur            w0, [x3, #0xf]
    // 0xb9d8d0: ldr             x0, [fp, #0x20]
    // 0xb9d8d4: LoadField: r2 = r0->field_27
    //     0xb9d8d4: ldur            w2, [x0, #0x27]
    // 0xb9d8d8: DecompressPointer r2
    //     0xb9d8d8: add             x2, x2, HEAP, lsl #32
    // 0xb9d8dc: r1 = Instance_JsonCodec
    //     0xb9d8dc: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0xb9d8e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb9d8e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb9d8e4: r0 = decode()
    //     0xb9d8e4: bl              #0x61c5d0  ; [dart:convert] JsonCodec::decode
    // 0xb9d8e8: mov             x3, x0
    // 0xb9d8ec: r2 = Null
    //     0xb9d8ec: mov             x2, NULL
    // 0xb9d8f0: r1 = Null
    //     0xb9d8f0: mov             x1, NULL
    // 0xb9d8f4: stur            x3, [fp, #-0x18]
    // 0xb9d8f8: r4 = 60
    //     0xb9d8f8: movz            x4, #0x3c
    // 0xb9d8fc: branchIfSmi(r0, 0xb9d908)
    //     0xb9d8fc: tbz             w0, #0, #0xb9d908
    // 0xb9d900: r4 = LoadClassIdInstr(r0)
    //     0xb9d900: ldur            x4, [x0, #-1]
    //     0xb9d904: ubfx            x4, x4, #0xc, #0x14
    // 0xb9d908: sub             x4, x4, #0x5a
    // 0xb9d90c: cmp             x4, #2
    // 0xb9d910: b.ls            #0xb9d924
    // 0xb9d914: r8 = List
    //     0xb9d914: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xb9d918: r3 = Null
    //     0xb9d918: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f80] Null
    //     0xb9d91c: ldr             x3, [x3, #0xf80]
    // 0xb9d920: r0 = List()
    //     0xb9d920: bl              #0xed6b40  ; IsType_List_Stub
    // 0xb9d924: ldur            x2, [fp, #-0x10]
    // 0xb9d928: r1 = Function '<anonymous closure>':.
    //     0xb9d928: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f90] AnonymousClosure: (0xb9d9d0), in [package:nuonline/app/data/models/summary.dart] Summary::itemsBuilder (0xb9d870)
    //     0xb9d92c: ldr             x1, [x1, #0xf90]
    // 0xb9d930: r0 = AllocateClosure()
    //     0xb9d930: bl              #0xec1630  ; AllocateClosureStub
    // 0xb9d934: mov             x1, x0
    // 0xb9d938: ldur            x0, [fp, #-8]
    // 0xb9d93c: StoreField: r1->field_b = r0
    //     0xb9d93c: stur            w0, [x1, #0xb]
    // 0xb9d940: ldur            x2, [fp, #-0x18]
    // 0xb9d944: r3 = LoadClassIdInstr(r2)
    //     0xb9d944: ldur            x3, [x2, #-1]
    //     0xb9d948: ubfx            x3, x3, #0xc, #0x14
    // 0xb9d94c: stp             x2, x0, [SP, #8]
    // 0xb9d950: str             x1, [SP]
    // 0xb9d954: mov             x0, x3
    // 0xb9d958: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9d958: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb9d95c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xb9d95c: movz            x17, #0xf28c
    //     0xb9d960: add             lr, x0, x17
    //     0xb9d964: ldr             lr, [x21, lr, lsl #3]
    //     0xb9d968: blr             lr
    // 0xb9d96c: r1 = LoadClassIdInstr(r0)
    //     0xb9d96c: ldur            x1, [x0, #-1]
    //     0xb9d970: ubfx            x1, x1, #0xc, #0x14
    // 0xb9d974: mov             x16, x0
    // 0xb9d978: mov             x0, x1
    // 0xb9d97c: mov             x1, x16
    // 0xb9d980: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb9d980: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb9d984: r0 = GDT[cid_x0 + 0xd889]()
    //     0xb9d984: movz            x17, #0xd889
    //     0xb9d988: add             lr, x0, x17
    //     0xb9d98c: ldr             lr, [x21, lr, lsl #3]
    //     0xb9d990: blr             lr
    // 0xb9d994: mov             x1, x0
    // 0xb9d998: ldr             x0, [fp, #0x10]
    // 0xb9d99c: stur            x1, [fp, #-8]
    // 0xb9d9a0: cmp             w0, NULL
    // 0xb9d9a4: b.eq            #0xb9d9b8
    // 0xb9d9a8: stp             x1, x0, [SP]
    // 0xb9d9ac: ClosureCall
    //     0xb9d9ac: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb9d9b0: ldur            x2, [x0, #0x1f]
    //     0xb9d9b4: blr             x2
    // 0xb9d9b8: ldur            x0, [fp, #-8]
    // 0xb9d9bc: LeaveFrame
    //     0xb9d9bc: mov             SP, fp
    //     0xb9d9c0: ldp             fp, lr, [SP], #0x10
    // 0xb9d9c4: ret
    //     0xb9d9c4: ret             
    // 0xb9d9c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9d9c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9d9cc: b               #0xb9d8b8
  }
  [closure] Y0 <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb9d9d0, size: 0x7c
    // 0xb9d9d0: EnterFrame
    //     0xb9d9d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb9d9d4: mov             fp, SP
    // 0xb9d9d8: AllocStack(0x18)
    //     0xb9d9d8: sub             SP, SP, #0x18
    // 0xb9d9dc: SetupParameters()
    //     0xb9d9dc: ldr             x0, [fp, #0x18]
    //     0xb9d9e0: ldur            w1, [x0, #0x17]
    //     0xb9d9e4: add             x1, x1, HEAP, lsl #32
    // 0xb9d9e8: CheckStackOverflow
    //     0xb9d9e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9d9ec: cmp             SP, x16
    //     0xb9d9f0: b.ls            #0xb9da44
    // 0xb9d9f4: LoadField: r3 = r1->field_f
    //     0xb9d9f4: ldur            w3, [x1, #0xf]
    // 0xb9d9f8: DecompressPointer r3
    //     0xb9d9f8: add             x3, x3, HEAP, lsl #32
    // 0xb9d9fc: ldr             x0, [fp, #0x10]
    // 0xb9da00: stur            x3, [fp, #-8]
    // 0xb9da04: r2 = Null
    //     0xb9da04: mov             x2, NULL
    // 0xb9da08: r1 = Null
    //     0xb9da08: mov             x1, NULL
    // 0xb9da0c: r8 = Map<String, dynamic>
    //     0xb9da0c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xb9da10: r3 = Null
    //     0xb9da10: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f98] Null
    //     0xb9da14: ldr             x3, [x3, #0xf98]
    // 0xb9da18: r0 = Map<String, dynamic>()
    //     0xb9da18: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xb9da1c: ldur            x16, [fp, #-8]
    // 0xb9da20: ldr             lr, [fp, #0x10]
    // 0xb9da24: stp             lr, x16, [SP]
    // 0xb9da28: ldur            x0, [fp, #-8]
    // 0xb9da2c: ClosureCall
    //     0xb9da2c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb9da30: ldur            x2, [x0, #0x1f]
    //     0xb9da34: blr             x2
    // 0xb9da38: LeaveFrame
    //     0xb9da38: mov             SP, fp
    //     0xb9da3c: ldp             fp, lr, [SP], #0x10
    // 0xb9da40: ret
    //     0xb9da40: ret             
    // 0xb9da44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9da44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9da48: b               #0xb9d9f4
  }
}

// class id: 1648, size: 0x14, field offset: 0xc
class SummaryAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa658f8, size: 0x674
    // 0xa658f8: EnterFrame
    //     0xa658f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa658fc: mov             fp, SP
    // 0xa65900: AllocStack(0x78)
    //     0xa65900: sub             SP, SP, #0x78
    // 0xa65904: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa65904: stur            x2, [fp, #-0x20]
    // 0xa65908: CheckStackOverflow
    //     0xa65908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6590c: cmp             SP, x16
    //     0xa65910: b.ls            #0xa65f54
    // 0xa65914: LoadField: r3 = r2->field_23
    //     0xa65914: ldur            x3, [x2, #0x23]
    // 0xa65918: add             x0, x3, #1
    // 0xa6591c: LoadField: r1 = r2->field_1b
    //     0xa6591c: ldur            x1, [x2, #0x1b]
    // 0xa65920: cmp             x0, x1
    // 0xa65924: b.gt            #0xa65ef8
    // 0xa65928: LoadField: r4 = r2->field_7
    //     0xa65928: ldur            w4, [x2, #7]
    // 0xa6592c: DecompressPointer r4
    //     0xa6592c: add             x4, x4, HEAP, lsl #32
    // 0xa65930: stur            x4, [fp, #-0x18]
    // 0xa65934: StoreField: r2->field_23 = r0
    //     0xa65934: stur            x0, [x2, #0x23]
    // 0xa65938: LoadField: r0 = r4->field_13
    //     0xa65938: ldur            w0, [x4, #0x13]
    // 0xa6593c: r5 = LoadInt32Instr(r0)
    //     0xa6593c: sbfx            x5, x0, #1, #0x1f
    // 0xa65940: mov             x0, x5
    // 0xa65944: mov             x1, x3
    // 0xa65948: stur            x5, [fp, #-0x10]
    // 0xa6594c: cmp             x1, x0
    // 0xa65950: b.hs            #0xa65f5c
    // 0xa65954: LoadField: r0 = r4->field_7
    //     0xa65954: ldur            x0, [x4, #7]
    // 0xa65958: ldrb            w1, [x0, x3]
    // 0xa6595c: stur            x1, [fp, #-8]
    // 0xa65960: r16 = <int, dynamic>
    //     0xa65960: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa65964: ldr             x16, [x16, #0xac0]
    // 0xa65968: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6596c: stp             lr, x16, [SP]
    // 0xa65970: r0 = Map._fromLiteral()
    //     0xa65970: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa65974: mov             x2, x0
    // 0xa65978: stur            x2, [fp, #-0x38]
    // 0xa6597c: r6 = 0
    //     0xa6597c: movz            x6, #0
    // 0xa65980: ldur            x3, [fp, #-0x20]
    // 0xa65984: ldur            x4, [fp, #-0x18]
    // 0xa65988: ldur            x5, [fp, #-8]
    // 0xa6598c: stur            x6, [fp, #-0x30]
    // 0xa65990: CheckStackOverflow
    //     0xa65990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65994: cmp             SP, x16
    //     0xa65998: b.ls            #0xa65f60
    // 0xa6599c: cmp             x6, x5
    // 0xa659a0: b.ge            #0xa65a2c
    // 0xa659a4: LoadField: r7 = r3->field_23
    //     0xa659a4: ldur            x7, [x3, #0x23]
    // 0xa659a8: add             x0, x7, #1
    // 0xa659ac: LoadField: r1 = r3->field_1b
    //     0xa659ac: ldur            x1, [x3, #0x1b]
    // 0xa659b0: cmp             x0, x1
    // 0xa659b4: b.gt            #0xa65f20
    // 0xa659b8: StoreField: r3->field_23 = r0
    //     0xa659b8: stur            x0, [x3, #0x23]
    // 0xa659bc: ldur            x0, [fp, #-0x10]
    // 0xa659c0: mov             x1, x7
    // 0xa659c4: cmp             x1, x0
    // 0xa659c8: b.hs            #0xa65f68
    // 0xa659cc: LoadField: r0 = r4->field_7
    //     0xa659cc: ldur            x0, [x4, #7]
    // 0xa659d0: ldrb            w8, [x0, x7]
    // 0xa659d4: mov             x1, x3
    // 0xa659d8: stur            x8, [fp, #-0x28]
    // 0xa659dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa659dc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa659e0: r0 = read()
    //     0xa659e0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa659e4: mov             x1, x0
    // 0xa659e8: ldur            x0, [fp, #-0x28]
    // 0xa659ec: lsl             x2, x0, #1
    // 0xa659f0: r16 = LoadInt32Instr(r2)
    //     0xa659f0: sbfx            x16, x2, #1, #0x1f
    // 0xa659f4: r17 = 11601
    //     0xa659f4: movz            x17, #0x2d51
    // 0xa659f8: mul             x0, x16, x17
    // 0xa659fc: umulh           x16, x16, x17
    // 0xa65a00: eor             x0, x0, x16
    // 0xa65a04: r0 = 0
    //     0xa65a04: eor             x0, x0, x0, lsr #32
    // 0xa65a08: ubfiz           x0, x0, #1, #0x1e
    // 0xa65a0c: r5 = LoadInt32Instr(r0)
    //     0xa65a0c: sbfx            x5, x0, #1, #0x1f
    // 0xa65a10: mov             x3, x1
    // 0xa65a14: ldur            x1, [fp, #-0x38]
    // 0xa65a18: r0 = _set()
    //     0xa65a18: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa65a1c: ldur            x0, [fp, #-0x30]
    // 0xa65a20: add             x6, x0, #1
    // 0xa65a24: ldur            x2, [fp, #-0x38]
    // 0xa65a28: b               #0xa65980
    // 0xa65a2c: mov             x0, x2
    // 0xa65a30: mov             x1, x0
    // 0xa65a34: r2 = 0
    //     0xa65a34: movz            x2, #0
    // 0xa65a38: r0 = _getValueOrData()
    //     0xa65a38: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65a3c: ldur            x3, [fp, #-0x38]
    // 0xa65a40: LoadField: r1 = r3->field_f
    //     0xa65a40: ldur            w1, [x3, #0xf]
    // 0xa65a44: DecompressPointer r1
    //     0xa65a44: add             x1, x1, HEAP, lsl #32
    // 0xa65a48: cmp             w1, w0
    // 0xa65a4c: b.ne            #0xa65a58
    // 0xa65a50: r4 = Null
    //     0xa65a50: mov             x4, NULL
    // 0xa65a54: b               #0xa65a5c
    // 0xa65a58: mov             x4, x0
    // 0xa65a5c: mov             x0, x4
    // 0xa65a60: stur            x4, [fp, #-0x18]
    // 0xa65a64: r2 = Null
    //     0xa65a64: mov             x2, NULL
    // 0xa65a68: r1 = Null
    //     0xa65a68: mov             x1, NULL
    // 0xa65a6c: branchIfSmi(r0, 0xa65a94)
    //     0xa65a6c: tbz             w0, #0, #0xa65a94
    // 0xa65a70: r4 = LoadClassIdInstr(r0)
    //     0xa65a70: ldur            x4, [x0, #-1]
    //     0xa65a74: ubfx            x4, x4, #0xc, #0x14
    // 0xa65a78: sub             x4, x4, #0x3c
    // 0xa65a7c: cmp             x4, #1
    // 0xa65a80: b.ls            #0xa65a94
    // 0xa65a84: r8 = int
    //     0xa65a84: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa65a88: r3 = Null
    //     0xa65a88: add             x3, PP, #0x20, lsl #12  ; [pp+0x20dd8] Null
    //     0xa65a8c: ldr             x3, [x3, #0xdd8]
    // 0xa65a90: r0 = int()
    //     0xa65a90: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa65a94: ldur            x1, [fp, #-0x38]
    // 0xa65a98: r2 = 4
    //     0xa65a98: movz            x2, #0x4
    // 0xa65a9c: r0 = _getValueOrData()
    //     0xa65a9c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65aa0: ldur            x3, [fp, #-0x38]
    // 0xa65aa4: LoadField: r1 = r3->field_f
    //     0xa65aa4: ldur            w1, [x3, #0xf]
    // 0xa65aa8: DecompressPointer r1
    //     0xa65aa8: add             x1, x1, HEAP, lsl #32
    // 0xa65aac: cmp             w1, w0
    // 0xa65ab0: b.ne            #0xa65abc
    // 0xa65ab4: r4 = Null
    //     0xa65ab4: mov             x4, NULL
    // 0xa65ab8: b               #0xa65ac0
    // 0xa65abc: mov             x4, x0
    // 0xa65ac0: mov             x0, x4
    // 0xa65ac4: stur            x4, [fp, #-0x20]
    // 0xa65ac8: r2 = Null
    //     0xa65ac8: mov             x2, NULL
    // 0xa65acc: r1 = Null
    //     0xa65acc: mov             x1, NULL
    // 0xa65ad0: r4 = 60
    //     0xa65ad0: movz            x4, #0x3c
    // 0xa65ad4: branchIfSmi(r0, 0xa65ae0)
    //     0xa65ad4: tbz             w0, #0, #0xa65ae0
    // 0xa65ad8: r4 = LoadClassIdInstr(r0)
    //     0xa65ad8: ldur            x4, [x0, #-1]
    //     0xa65adc: ubfx            x4, x4, #0xc, #0x14
    // 0xa65ae0: sub             x4, x4, #0x5e
    // 0xa65ae4: cmp             x4, #1
    // 0xa65ae8: b.ls            #0xa65afc
    // 0xa65aec: r8 = String
    //     0xa65aec: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa65af0: r3 = Null
    //     0xa65af0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20de8] Null
    //     0xa65af4: ldr             x3, [x3, #0xde8]
    // 0xa65af8: r0 = String()
    //     0xa65af8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65afc: ldur            x1, [fp, #-0x38]
    // 0xa65b00: r2 = 6
    //     0xa65b00: movz            x2, #0x6
    // 0xa65b04: r0 = _getValueOrData()
    //     0xa65b04: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65b08: ldur            x3, [fp, #-0x38]
    // 0xa65b0c: LoadField: r1 = r3->field_f
    //     0xa65b0c: ldur            w1, [x3, #0xf]
    // 0xa65b10: DecompressPointer r1
    //     0xa65b10: add             x1, x1, HEAP, lsl #32
    // 0xa65b14: cmp             w1, w0
    // 0xa65b18: b.ne            #0xa65b24
    // 0xa65b1c: r4 = Null
    //     0xa65b1c: mov             x4, NULL
    // 0xa65b20: b               #0xa65b28
    // 0xa65b24: mov             x4, x0
    // 0xa65b28: mov             x0, x4
    // 0xa65b2c: stur            x4, [fp, #-0x40]
    // 0xa65b30: r2 = Null
    //     0xa65b30: mov             x2, NULL
    // 0xa65b34: r1 = Null
    //     0xa65b34: mov             x1, NULL
    // 0xa65b38: r4 = 60
    //     0xa65b38: movz            x4, #0x3c
    // 0xa65b3c: branchIfSmi(r0, 0xa65b48)
    //     0xa65b3c: tbz             w0, #0, #0xa65b48
    // 0xa65b40: r4 = LoadClassIdInstr(r0)
    //     0xa65b40: ldur            x4, [x0, #-1]
    //     0xa65b44: ubfx            x4, x4, #0xc, #0x14
    // 0xa65b48: sub             x4, x4, #0x5e
    // 0xa65b4c: cmp             x4, #1
    // 0xa65b50: b.ls            #0xa65b64
    // 0xa65b54: r8 = String
    //     0xa65b54: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa65b58: r3 = Null
    //     0xa65b58: add             x3, PP, #0x20, lsl #12  ; [pp+0x20df8] Null
    //     0xa65b5c: ldr             x3, [x3, #0xdf8]
    // 0xa65b60: r0 = String()
    //     0xa65b60: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65b64: ldur            x1, [fp, #-0x38]
    // 0xa65b68: r2 = 8
    //     0xa65b68: movz            x2, #0x8
    // 0xa65b6c: r0 = _getValueOrData()
    //     0xa65b6c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65b70: ldur            x3, [fp, #-0x38]
    // 0xa65b74: LoadField: r1 = r3->field_f
    //     0xa65b74: ldur            w1, [x3, #0xf]
    // 0xa65b78: DecompressPointer r1
    //     0xa65b78: add             x1, x1, HEAP, lsl #32
    // 0xa65b7c: cmp             w1, w0
    // 0xa65b80: b.ne            #0xa65b8c
    // 0xa65b84: r4 = Null
    //     0xa65b84: mov             x4, NULL
    // 0xa65b88: b               #0xa65b90
    // 0xa65b8c: mov             x4, x0
    // 0xa65b90: mov             x0, x4
    // 0xa65b94: stur            x4, [fp, #-0x48]
    // 0xa65b98: r2 = Null
    //     0xa65b98: mov             x2, NULL
    // 0xa65b9c: r1 = Null
    //     0xa65b9c: mov             x1, NULL
    // 0xa65ba0: r4 = 60
    //     0xa65ba0: movz            x4, #0x3c
    // 0xa65ba4: branchIfSmi(r0, 0xa65bb0)
    //     0xa65ba4: tbz             w0, #0, #0xa65bb0
    // 0xa65ba8: r4 = LoadClassIdInstr(r0)
    //     0xa65ba8: ldur            x4, [x0, #-1]
    //     0xa65bac: ubfx            x4, x4, #0xc, #0x14
    // 0xa65bb0: sub             x4, x4, #0x5e
    // 0xa65bb4: cmp             x4, #1
    // 0xa65bb8: b.ls            #0xa65bcc
    // 0xa65bbc: r8 = String
    //     0xa65bbc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa65bc0: r3 = Null
    //     0xa65bc0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e08] Null
    //     0xa65bc4: ldr             x3, [x3, #0xe08]
    // 0xa65bc8: r0 = String()
    //     0xa65bc8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa65bcc: ldur            x1, [fp, #-0x38]
    // 0xa65bd0: r2 = 2
    //     0xa65bd0: movz            x2, #0x2
    // 0xa65bd4: r0 = _getValueOrData()
    //     0xa65bd4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65bd8: ldur            x3, [fp, #-0x38]
    // 0xa65bdc: LoadField: r1 = r3->field_f
    //     0xa65bdc: ldur            w1, [x3, #0xf]
    // 0xa65be0: DecompressPointer r1
    //     0xa65be0: add             x1, x1, HEAP, lsl #32
    // 0xa65be4: cmp             w1, w0
    // 0xa65be8: b.ne            #0xa65bf4
    // 0xa65bec: r4 = Null
    //     0xa65bec: mov             x4, NULL
    // 0xa65bf0: b               #0xa65bf8
    // 0xa65bf4: mov             x4, x0
    // 0xa65bf8: mov             x0, x4
    // 0xa65bfc: stur            x4, [fp, #-0x50]
    // 0xa65c00: r2 = Null
    //     0xa65c00: mov             x2, NULL
    // 0xa65c04: r1 = Null
    //     0xa65c04: mov             x1, NULL
    // 0xa65c08: r4 = 60
    //     0xa65c08: movz            x4, #0x3c
    // 0xa65c0c: branchIfSmi(r0, 0xa65c18)
    //     0xa65c0c: tbz             w0, #0, #0xa65c18
    // 0xa65c10: r4 = LoadClassIdInstr(r0)
    //     0xa65c10: ldur            x4, [x0, #-1]
    //     0xa65c14: ubfx            x4, x4, #0xc, #0x14
    // 0xa65c18: sub             x4, x4, #0x5e
    // 0xa65c1c: cmp             x4, #1
    // 0xa65c20: b.ls            #0xa65c34
    // 0xa65c24: r8 = String?
    //     0xa65c24: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa65c28: r3 = Null
    //     0xa65c28: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e18] Null
    //     0xa65c2c: ldr             x3, [x3, #0xe18]
    // 0xa65c30: r0 = String?()
    //     0xa65c30: bl              #0x600324  ; IsType_String?_Stub
    // 0xa65c34: ldur            x1, [fp, #-0x38]
    // 0xa65c38: r2 = 10
    //     0xa65c38: movz            x2, #0xa
    // 0xa65c3c: r0 = _getValueOrData()
    //     0xa65c3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65c40: ldur            x3, [fp, #-0x38]
    // 0xa65c44: LoadField: r1 = r3->field_f
    //     0xa65c44: ldur            w1, [x3, #0xf]
    // 0xa65c48: DecompressPointer r1
    //     0xa65c48: add             x1, x1, HEAP, lsl #32
    // 0xa65c4c: cmp             w1, w0
    // 0xa65c50: b.ne            #0xa65c5c
    // 0xa65c54: r4 = Null
    //     0xa65c54: mov             x4, NULL
    // 0xa65c58: b               #0xa65c60
    // 0xa65c5c: mov             x4, x0
    // 0xa65c60: mov             x0, x4
    // 0xa65c64: stur            x4, [fp, #-0x58]
    // 0xa65c68: r2 = Null
    //     0xa65c68: mov             x2, NULL
    // 0xa65c6c: r1 = Null
    //     0xa65c6c: mov             x1, NULL
    // 0xa65c70: r4 = 60
    //     0xa65c70: movz            x4, #0x3c
    // 0xa65c74: branchIfSmi(r0, 0xa65c80)
    //     0xa65c74: tbz             w0, #0, #0xa65c80
    // 0xa65c78: r4 = LoadClassIdInstr(r0)
    //     0xa65c78: ldur            x4, [x0, #-1]
    //     0xa65c7c: ubfx            x4, x4, #0xc, #0x14
    // 0xa65c80: sub             x4, x4, #0x5e
    // 0xa65c84: cmp             x4, #1
    // 0xa65c88: b.ls            #0xa65c9c
    // 0xa65c8c: r8 = String?
    //     0xa65c8c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa65c90: r3 = Null
    //     0xa65c90: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e28] Null
    //     0xa65c94: ldr             x3, [x3, #0xe28]
    // 0xa65c98: r0 = String?()
    //     0xa65c98: bl              #0x600324  ; IsType_String?_Stub
    // 0xa65c9c: ldur            x1, [fp, #-0x38]
    // 0xa65ca0: r2 = 12
    //     0xa65ca0: movz            x2, #0xc
    // 0xa65ca4: r0 = _getValueOrData()
    //     0xa65ca4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65ca8: ldur            x3, [fp, #-0x38]
    // 0xa65cac: LoadField: r1 = r3->field_f
    //     0xa65cac: ldur            w1, [x3, #0xf]
    // 0xa65cb0: DecompressPointer r1
    //     0xa65cb0: add             x1, x1, HEAP, lsl #32
    // 0xa65cb4: cmp             w1, w0
    // 0xa65cb8: b.ne            #0xa65cc4
    // 0xa65cbc: r4 = Null
    //     0xa65cbc: mov             x4, NULL
    // 0xa65cc0: b               #0xa65cc8
    // 0xa65cc4: mov             x4, x0
    // 0xa65cc8: mov             x0, x4
    // 0xa65ccc: stur            x4, [fp, #-0x60]
    // 0xa65cd0: r2 = Null
    //     0xa65cd0: mov             x2, NULL
    // 0xa65cd4: r1 = Null
    //     0xa65cd4: mov             x1, NULL
    // 0xa65cd8: r8 = Map?
    //     0xa65cd8: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0xa65cdc: ldr             x8, [x8, #0x590]
    // 0xa65ce0: r3 = Null
    //     0xa65ce0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e38] Null
    //     0xa65ce4: ldr             x3, [x3, #0xe38]
    // 0xa65ce8: r0 = Map?()
    //     0xa65ce8: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0xa65cec: ldur            x0, [fp, #-0x60]
    // 0xa65cf0: cmp             w0, NULL
    // 0xa65cf4: b.ne            #0xa65d00
    // 0xa65cf8: r3 = Null
    //     0xa65cf8: mov             x3, NULL
    // 0xa65cfc: b               #0xa65d2c
    // 0xa65d00: r1 = LoadClassIdInstr(r0)
    //     0xa65d00: ldur            x1, [x0, #-1]
    //     0xa65d04: ubfx            x1, x1, #0xc, #0x14
    // 0xa65d08: r16 = <String, String>
    //     0xa65d08: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xa65d0c: ldr             x16, [x16, #0x668]
    // 0xa65d10: stp             x0, x16, [SP]
    // 0xa65d14: mov             x0, x1
    // 0xa65d18: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa65d18: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa65d1c: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa65d1c: add             lr, x0, #0x5f4
    //     0xa65d20: ldr             lr, [x21, lr, lsl #3]
    //     0xa65d24: blr             lr
    // 0xa65d28: mov             x3, x0
    // 0xa65d2c: ldur            x0, [fp, #-0x38]
    // 0xa65d30: mov             x1, x0
    // 0xa65d34: stur            x3, [fp, #-0x60]
    // 0xa65d38: r2 = 14
    //     0xa65d38: movz            x2, #0xe
    // 0xa65d3c: r0 = _getValueOrData()
    //     0xa65d3c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65d40: ldur            x3, [fp, #-0x38]
    // 0xa65d44: LoadField: r1 = r3->field_f
    //     0xa65d44: ldur            w1, [x3, #0xf]
    // 0xa65d48: DecompressPointer r1
    //     0xa65d48: add             x1, x1, HEAP, lsl #32
    // 0xa65d4c: cmp             w1, w0
    // 0xa65d50: b.ne            #0xa65d5c
    // 0xa65d54: r4 = Null
    //     0xa65d54: mov             x4, NULL
    // 0xa65d58: b               #0xa65d60
    // 0xa65d5c: mov             x4, x0
    // 0xa65d60: mov             x0, x4
    // 0xa65d64: stur            x4, [fp, #-0x68]
    // 0xa65d68: r2 = Null
    //     0xa65d68: mov             x2, NULL
    // 0xa65d6c: r1 = Null
    //     0xa65d6c: mov             x1, NULL
    // 0xa65d70: r4 = 60
    //     0xa65d70: movz            x4, #0x3c
    // 0xa65d74: branchIfSmi(r0, 0xa65d80)
    //     0xa65d74: tbz             w0, #0, #0xa65d80
    // 0xa65d78: r4 = LoadClassIdInstr(r0)
    //     0xa65d78: ldur            x4, [x0, #-1]
    //     0xa65d7c: ubfx            x4, x4, #0xc, #0x14
    // 0xa65d80: sub             x4, x4, #0x5e
    // 0xa65d84: cmp             x4, #1
    // 0xa65d88: b.ls            #0xa65d9c
    // 0xa65d8c: r8 = String?
    //     0xa65d8c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa65d90: r3 = Null
    //     0xa65d90: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e48] Null
    //     0xa65d94: ldr             x3, [x3, #0xe48]
    // 0xa65d98: r0 = String?()
    //     0xa65d98: bl              #0x600324  ; IsType_String?_Stub
    // 0xa65d9c: ldur            x1, [fp, #-0x38]
    // 0xa65da0: r2 = 16
    //     0xa65da0: movz            x2, #0x10
    // 0xa65da4: r0 = _getValueOrData()
    //     0xa65da4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa65da8: mov             x1, x0
    // 0xa65dac: ldur            x0, [fp, #-0x38]
    // 0xa65db0: LoadField: r2 = r0->field_f
    //     0xa65db0: ldur            w2, [x0, #0xf]
    // 0xa65db4: DecompressPointer r2
    //     0xa65db4: add             x2, x2, HEAP, lsl #32
    // 0xa65db8: cmp             w2, w1
    // 0xa65dbc: b.ne            #0xa65dc8
    // 0xa65dc0: r3 = Null
    //     0xa65dc0: mov             x3, NULL
    // 0xa65dc4: b               #0xa65dcc
    // 0xa65dc8: mov             x3, x1
    // 0xa65dcc: mov             x0, x3
    // 0xa65dd0: stur            x3, [fp, #-0x38]
    // 0xa65dd4: r2 = Null
    //     0xa65dd4: mov             x2, NULL
    // 0xa65dd8: r1 = Null
    //     0xa65dd8: mov             x1, NULL
    // 0xa65ddc: r8 = Map?
    //     0xa65ddc: add             x8, PP, #0xe, lsl #12  ; [pp+0xe590] Type: Map?
    //     0xa65de0: ldr             x8, [x8, #0x590]
    // 0xa65de4: r3 = Null
    //     0xa65de4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20e58] Null
    //     0xa65de8: ldr             x3, [x3, #0xe58]
    // 0xa65dec: r0 = Map?()
    //     0xa65dec: bl              #0x7efa60  ; IsType_Map?_Stub
    // 0xa65df0: ldur            x0, [fp, #-0x38]
    // 0xa65df4: cmp             w0, NULL
    // 0xa65df8: b.ne            #0xa65e04
    // 0xa65dfc: r8 = Null
    //     0xa65dfc: mov             x8, NULL
    // 0xa65e00: b               #0xa65e2c
    // 0xa65e04: r1 = LoadClassIdInstr(r0)
    //     0xa65e04: ldur            x1, [x0, #-1]
    //     0xa65e08: ubfx            x1, x1, #0xc, #0x14
    // 0xa65e0c: r16 = <String, dynamic>
    //     0xa65e0c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa65e10: stp             x0, x16, [SP]
    // 0xa65e14: mov             x0, x1
    // 0xa65e18: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa65e18: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa65e1c: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa65e1c: add             lr, x0, #0x5f4
    //     0xa65e20: ldr             lr, [x21, lr, lsl #3]
    //     0xa65e24: blr             lr
    // 0xa65e28: mov             x8, x0
    // 0xa65e2c: ldur            x1, [fp, #-0x60]
    // 0xa65e30: ldur            x0, [fp, #-0x68]
    // 0xa65e34: ldur            x7, [fp, #-0x18]
    // 0xa65e38: ldur            x6, [fp, #-0x20]
    // 0xa65e3c: ldur            x5, [fp, #-0x40]
    // 0xa65e40: ldur            x4, [fp, #-0x48]
    // 0xa65e44: ldur            x3, [fp, #-0x50]
    // 0xa65e48: ldur            x2, [fp, #-0x58]
    // 0xa65e4c: stur            x8, [fp, #-0x38]
    // 0xa65e50: r9 = LoadInt32Instr(r7)
    //     0xa65e50: sbfx            x9, x7, #1, #0x1f
    //     0xa65e54: tbz             w7, #0, #0xa65e5c
    //     0xa65e58: ldur            x9, [x7, #7]
    // 0xa65e5c: stur            x9, [fp, #-8]
    // 0xa65e60: r0 = Summary()
    //     0xa65e60: bl              #0x91b5e0  ; AllocateSummaryStub -> Summary (size=0x3c)
    // 0xa65e64: mov             x1, x0
    // 0xa65e68: ldur            x0, [fp, #-8]
    // 0xa65e6c: stur            x1, [fp, #-0x18]
    // 0xa65e70: StoreField: r1->field_13 = r0
    //     0xa65e70: stur            x0, [x1, #0x13]
    // 0xa65e74: ldur            x0, [fp, #-0x20]
    // 0xa65e78: StoreField: r1->field_1f = r0
    //     0xa65e78: stur            w0, [x1, #0x1f]
    // 0xa65e7c: ldur            x0, [fp, #-0x40]
    // 0xa65e80: StoreField: r1->field_23 = r0
    //     0xa65e80: stur            w0, [x1, #0x23]
    // 0xa65e84: ldur            x0, [fp, #-0x48]
    // 0xa65e88: StoreField: r1->field_27 = r0
    //     0xa65e88: stur            w0, [x1, #0x27]
    // 0xa65e8c: ldur            x0, [fp, #-0x50]
    // 0xa65e90: StoreField: r1->field_1b = r0
    //     0xa65e90: stur            w0, [x1, #0x1b]
    // 0xa65e94: ldur            x0, [fp, #-0x58]
    // 0xa65e98: StoreField: r1->field_2b = r0
    //     0xa65e98: stur            w0, [x1, #0x2b]
    // 0xa65e9c: ldur            x0, [fp, #-0x60]
    // 0xa65ea0: StoreField: r1->field_2f = r0
    //     0xa65ea0: stur            w0, [x1, #0x2f]
    // 0xa65ea4: ldur            x0, [fp, #-0x68]
    // 0xa65ea8: StoreField: r1->field_33 = r0
    //     0xa65ea8: stur            w0, [x1, #0x33]
    // 0xa65eac: ldur            x0, [fp, #-0x38]
    // 0xa65eb0: StoreField: r1->field_37 = r0
    //     0xa65eb0: stur            w0, [x1, #0x37]
    // 0xa65eb4: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa65eb4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa65eb8: ldr             x16, [x16, #0x9f8]
    // 0xa65ebc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa65ec0: stp             lr, x16, [SP]
    // 0xa65ec4: r0 = Map._fromLiteral()
    //     0xa65ec4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa65ec8: ldur            x1, [fp, #-0x18]
    // 0xa65ecc: StoreField: r1->field_f = r0
    //     0xa65ecc: stur            w0, [x1, #0xf]
    //     0xa65ed0: ldurb           w16, [x1, #-1]
    //     0xa65ed4: ldurb           w17, [x0, #-1]
    //     0xa65ed8: and             x16, x17, x16, lsr #2
    //     0xa65edc: tst             x16, HEAP, lsr #32
    //     0xa65ee0: b.eq            #0xa65ee8
    //     0xa65ee4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa65ee8: mov             x0, x1
    // 0xa65eec: LeaveFrame
    //     0xa65eec: mov             SP, fp
    //     0xa65ef0: ldp             fp, lr, [SP], #0x10
    // 0xa65ef4: ret
    //     0xa65ef4: ret             
    // 0xa65ef8: r0 = RangeError()
    //     0xa65ef8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa65efc: mov             x1, x0
    // 0xa65f00: r0 = "Not enough bytes available."
    //     0xa65f00: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa65f04: ldr             x0, [x0, #0x8a8]
    // 0xa65f08: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65f08: stur            w0, [x1, #0x17]
    // 0xa65f0c: r2 = false
    //     0xa65f0c: add             x2, NULL, #0x30  ; false
    // 0xa65f10: StoreField: r1->field_b = r2
    //     0xa65f10: stur            w2, [x1, #0xb]
    // 0xa65f14: mov             x0, x1
    // 0xa65f18: r0 = Throw()
    //     0xa65f18: bl              #0xec04b8  ; ThrowStub
    // 0xa65f1c: brk             #0
    // 0xa65f20: r0 = "Not enough bytes available."
    //     0xa65f20: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa65f24: ldr             x0, [x0, #0x8a8]
    // 0xa65f28: r2 = false
    //     0xa65f28: add             x2, NULL, #0x30  ; false
    // 0xa65f2c: r0 = RangeError()
    //     0xa65f2c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa65f30: mov             x1, x0
    // 0xa65f34: r0 = "Not enough bytes available."
    //     0xa65f34: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa65f38: ldr             x0, [x0, #0x8a8]
    // 0xa65f3c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65f3c: stur            w0, [x1, #0x17]
    // 0xa65f40: r0 = false
    //     0xa65f40: add             x0, NULL, #0x30  ; false
    // 0xa65f44: StoreField: r1->field_b = r0
    //     0xa65f44: stur            w0, [x1, #0xb]
    // 0xa65f48: mov             x0, x1
    // 0xa65f4c: r0 = Throw()
    //     0xa65f4c: bl              #0xec04b8  ; ThrowStub
    // 0xa65f50: brk             #0
    // 0xa65f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65f58: b               #0xa65914
    // 0xa65f5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa65f5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa65f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65f60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65f64: b               #0xa6599c
    // 0xa65f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa65f68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd6e78, size: 0x5f0
    // 0xbd6e78: EnterFrame
    //     0xbd6e78: stp             fp, lr, [SP, #-0x10]!
    //     0xbd6e7c: mov             fp, SP
    // 0xbd6e80: AllocStack(0x28)
    //     0xbd6e80: sub             SP, SP, #0x28
    // 0xbd6e84: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd6e84: mov             x4, x2
    //     0xbd6e88: stur            x2, [fp, #-8]
    //     0xbd6e8c: stur            x3, [fp, #-0x10]
    // 0xbd6e90: CheckStackOverflow
    //     0xbd6e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6e94: cmp             SP, x16
    //     0xbd6e98: b.ls            #0xbd7438
    // 0xbd6e9c: mov             x0, x3
    // 0xbd6ea0: r2 = Null
    //     0xbd6ea0: mov             x2, NULL
    // 0xbd6ea4: r1 = Null
    //     0xbd6ea4: mov             x1, NULL
    // 0xbd6ea8: r4 = 60
    //     0xbd6ea8: movz            x4, #0x3c
    // 0xbd6eac: branchIfSmi(r0, 0xbd6eb8)
    //     0xbd6eac: tbz             w0, #0, #0xbd6eb8
    // 0xbd6eb0: r4 = LoadClassIdInstr(r0)
    //     0xbd6eb0: ldur            x4, [x0, #-1]
    //     0xbd6eb4: ubfx            x4, x4, #0xc, #0x14
    // 0xbd6eb8: cmp             x4, #0x641
    // 0xbd6ebc: b.eq            #0xbd6ed4
    // 0xbd6ec0: r8 = Summary
    //     0xbd6ec0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b160] Type: Summary
    //     0xbd6ec4: ldr             x8, [x8, #0x160]
    // 0xbd6ec8: r3 = Null
    //     0xbd6ec8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b168] Null
    //     0xbd6ecc: ldr             x3, [x3, #0x168]
    // 0xbd6ed0: r0 = Summary()
    //     0xbd6ed0: bl              #0x836024  ; IsType_Summary_Stub
    // 0xbd6ed4: ldur            x0, [fp, #-8]
    // 0xbd6ed8: LoadField: r1 = r0->field_b
    //     0xbd6ed8: ldur            w1, [x0, #0xb]
    // 0xbd6edc: DecompressPointer r1
    //     0xbd6edc: add             x1, x1, HEAP, lsl #32
    // 0xbd6ee0: LoadField: r2 = r1->field_13
    //     0xbd6ee0: ldur            w2, [x1, #0x13]
    // 0xbd6ee4: LoadField: r1 = r0->field_13
    //     0xbd6ee4: ldur            x1, [x0, #0x13]
    // 0xbd6ee8: r3 = LoadInt32Instr(r2)
    //     0xbd6ee8: sbfx            x3, x2, #1, #0x1f
    // 0xbd6eec: sub             x2, x3, x1
    // 0xbd6ef0: cmp             x2, #1
    // 0xbd6ef4: b.ge            #0xbd6f04
    // 0xbd6ef8: mov             x1, x0
    // 0xbd6efc: r2 = 1
    //     0xbd6efc: movz            x2, #0x1
    // 0xbd6f00: r0 = _increaseBufferSize()
    //     0xbd6f00: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6f04: ldur            x3, [fp, #-8]
    // 0xbd6f08: r2 = 9
    //     0xbd6f08: movz            x2, #0x9
    // 0xbd6f0c: LoadField: r4 = r3->field_b
    //     0xbd6f0c: ldur            w4, [x3, #0xb]
    // 0xbd6f10: DecompressPointer r4
    //     0xbd6f10: add             x4, x4, HEAP, lsl #32
    // 0xbd6f14: LoadField: r5 = r3->field_13
    //     0xbd6f14: ldur            x5, [x3, #0x13]
    // 0xbd6f18: add             x6, x5, #1
    // 0xbd6f1c: StoreField: r3->field_13 = r6
    //     0xbd6f1c: stur            x6, [x3, #0x13]
    // 0xbd6f20: LoadField: r0 = r4->field_13
    //     0xbd6f20: ldur            w0, [x4, #0x13]
    // 0xbd6f24: r7 = LoadInt32Instr(r0)
    //     0xbd6f24: sbfx            x7, x0, #1, #0x1f
    // 0xbd6f28: mov             x0, x7
    // 0xbd6f2c: mov             x1, x5
    // 0xbd6f30: cmp             x1, x0
    // 0xbd6f34: b.hs            #0xbd7440
    // 0xbd6f38: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd6f38: add             x0, x4, x5
    //     0xbd6f3c: strb            w2, [x0, #0x17]
    // 0xbd6f40: sub             x0, x7, x6
    // 0xbd6f44: cmp             x0, #1
    // 0xbd6f48: b.ge            #0xbd6f58
    // 0xbd6f4c: mov             x1, x3
    // 0xbd6f50: r2 = 1
    //     0xbd6f50: movz            x2, #0x1
    // 0xbd6f54: r0 = _increaseBufferSize()
    //     0xbd6f54: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6f58: ldur            x2, [fp, #-8]
    // 0xbd6f5c: ldur            x3, [fp, #-0x10]
    // 0xbd6f60: LoadField: r4 = r2->field_b
    //     0xbd6f60: ldur            w4, [x2, #0xb]
    // 0xbd6f64: DecompressPointer r4
    //     0xbd6f64: add             x4, x4, HEAP, lsl #32
    // 0xbd6f68: LoadField: r5 = r2->field_13
    //     0xbd6f68: ldur            x5, [x2, #0x13]
    // 0xbd6f6c: add             x0, x5, #1
    // 0xbd6f70: StoreField: r2->field_13 = r0
    //     0xbd6f70: stur            x0, [x2, #0x13]
    // 0xbd6f74: LoadField: r0 = r4->field_13
    //     0xbd6f74: ldur            w0, [x4, #0x13]
    // 0xbd6f78: r1 = LoadInt32Instr(r0)
    //     0xbd6f78: sbfx            x1, x0, #1, #0x1f
    // 0xbd6f7c: mov             x0, x1
    // 0xbd6f80: mov             x1, x5
    // 0xbd6f84: cmp             x1, x0
    // 0xbd6f88: b.hs            #0xbd7444
    // 0xbd6f8c: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd6f8c: add             x0, x4, x5
    //     0xbd6f90: strb            wzr, [x0, #0x17]
    // 0xbd6f94: LoadField: r4 = r3->field_13
    //     0xbd6f94: ldur            x4, [x3, #0x13]
    // 0xbd6f98: r0 = BoxInt64Instr(r4)
    //     0xbd6f98: sbfiz           x0, x4, #1, #0x1f
    //     0xbd6f9c: cmp             x4, x0, asr #1
    //     0xbd6fa0: b.eq            #0xbd6fac
    //     0xbd6fa4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd6fa8: stur            x4, [x0, #7]
    // 0xbd6fac: r16 = <int>
    //     0xbd6fac: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd6fb0: stp             x2, x16, [SP, #8]
    // 0xbd6fb4: str             x0, [SP]
    // 0xbd6fb8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd6fb8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd6fbc: r0 = write()
    //     0xbd6fbc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd6fc0: ldur            x0, [fp, #-8]
    // 0xbd6fc4: LoadField: r1 = r0->field_b
    //     0xbd6fc4: ldur            w1, [x0, #0xb]
    // 0xbd6fc8: DecompressPointer r1
    //     0xbd6fc8: add             x1, x1, HEAP, lsl #32
    // 0xbd6fcc: LoadField: r2 = r1->field_13
    //     0xbd6fcc: ldur            w2, [x1, #0x13]
    // 0xbd6fd0: LoadField: r1 = r0->field_13
    //     0xbd6fd0: ldur            x1, [x0, #0x13]
    // 0xbd6fd4: r3 = LoadInt32Instr(r2)
    //     0xbd6fd4: sbfx            x3, x2, #1, #0x1f
    // 0xbd6fd8: sub             x2, x3, x1
    // 0xbd6fdc: cmp             x2, #1
    // 0xbd6fe0: b.ge            #0xbd6ff0
    // 0xbd6fe4: mov             x1, x0
    // 0xbd6fe8: r2 = 1
    //     0xbd6fe8: movz            x2, #0x1
    // 0xbd6fec: r0 = _increaseBufferSize()
    //     0xbd6fec: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd6ff0: ldur            x2, [fp, #-8]
    // 0xbd6ff4: ldur            x3, [fp, #-0x10]
    // 0xbd6ff8: r4 = 1
    //     0xbd6ff8: movz            x4, #0x1
    // 0xbd6ffc: LoadField: r5 = r2->field_b
    //     0xbd6ffc: ldur            w5, [x2, #0xb]
    // 0xbd7000: DecompressPointer r5
    //     0xbd7000: add             x5, x5, HEAP, lsl #32
    // 0xbd7004: LoadField: r6 = r2->field_13
    //     0xbd7004: ldur            x6, [x2, #0x13]
    // 0xbd7008: add             x0, x6, #1
    // 0xbd700c: StoreField: r2->field_13 = r0
    //     0xbd700c: stur            x0, [x2, #0x13]
    // 0xbd7010: LoadField: r0 = r5->field_13
    //     0xbd7010: ldur            w0, [x5, #0x13]
    // 0xbd7014: r1 = LoadInt32Instr(r0)
    //     0xbd7014: sbfx            x1, x0, #1, #0x1f
    // 0xbd7018: mov             x0, x1
    // 0xbd701c: mov             x1, x6
    // 0xbd7020: cmp             x1, x0
    // 0xbd7024: b.hs            #0xbd7448
    // 0xbd7028: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7028: add             x0, x5, x6
    //     0xbd702c: strb            w4, [x0, #0x17]
    // 0xbd7030: LoadField: r0 = r3->field_1b
    //     0xbd7030: ldur            w0, [x3, #0x1b]
    // 0xbd7034: DecompressPointer r0
    //     0xbd7034: add             x0, x0, HEAP, lsl #32
    // 0xbd7038: r16 = <String?>
    //     0xbd7038: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd703c: stp             x2, x16, [SP, #8]
    // 0xbd7040: str             x0, [SP]
    // 0xbd7044: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7044: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7048: r0 = write()
    //     0xbd7048: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd704c: ldur            x0, [fp, #-8]
    // 0xbd7050: LoadField: r1 = r0->field_b
    //     0xbd7050: ldur            w1, [x0, #0xb]
    // 0xbd7054: DecompressPointer r1
    //     0xbd7054: add             x1, x1, HEAP, lsl #32
    // 0xbd7058: LoadField: r2 = r1->field_13
    //     0xbd7058: ldur            w2, [x1, #0x13]
    // 0xbd705c: LoadField: r1 = r0->field_13
    //     0xbd705c: ldur            x1, [x0, #0x13]
    // 0xbd7060: r3 = LoadInt32Instr(r2)
    //     0xbd7060: sbfx            x3, x2, #1, #0x1f
    // 0xbd7064: sub             x2, x3, x1
    // 0xbd7068: cmp             x2, #1
    // 0xbd706c: b.ge            #0xbd707c
    // 0xbd7070: mov             x1, x0
    // 0xbd7074: r2 = 1
    //     0xbd7074: movz            x2, #0x1
    // 0xbd7078: r0 = _increaseBufferSize()
    //     0xbd7078: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd707c: ldur            x2, [fp, #-8]
    // 0xbd7080: ldur            x3, [fp, #-0x10]
    // 0xbd7084: r4 = 2
    //     0xbd7084: movz            x4, #0x2
    // 0xbd7088: LoadField: r5 = r2->field_b
    //     0xbd7088: ldur            w5, [x2, #0xb]
    // 0xbd708c: DecompressPointer r5
    //     0xbd708c: add             x5, x5, HEAP, lsl #32
    // 0xbd7090: LoadField: r6 = r2->field_13
    //     0xbd7090: ldur            x6, [x2, #0x13]
    // 0xbd7094: add             x0, x6, #1
    // 0xbd7098: StoreField: r2->field_13 = r0
    //     0xbd7098: stur            x0, [x2, #0x13]
    // 0xbd709c: LoadField: r0 = r5->field_13
    //     0xbd709c: ldur            w0, [x5, #0x13]
    // 0xbd70a0: r1 = LoadInt32Instr(r0)
    //     0xbd70a0: sbfx            x1, x0, #1, #0x1f
    // 0xbd70a4: mov             x0, x1
    // 0xbd70a8: mov             x1, x6
    // 0xbd70ac: cmp             x1, x0
    // 0xbd70b0: b.hs            #0xbd744c
    // 0xbd70b4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd70b4: add             x0, x5, x6
    //     0xbd70b8: strb            w4, [x0, #0x17]
    // 0xbd70bc: LoadField: r0 = r3->field_1f
    //     0xbd70bc: ldur            w0, [x3, #0x1f]
    // 0xbd70c0: DecompressPointer r0
    //     0xbd70c0: add             x0, x0, HEAP, lsl #32
    // 0xbd70c4: r16 = <String>
    //     0xbd70c4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd70c8: stp             x2, x16, [SP, #8]
    // 0xbd70cc: str             x0, [SP]
    // 0xbd70d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd70d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd70d4: r0 = write()
    //     0xbd70d4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd70d8: ldur            x0, [fp, #-8]
    // 0xbd70dc: LoadField: r1 = r0->field_b
    //     0xbd70dc: ldur            w1, [x0, #0xb]
    // 0xbd70e0: DecompressPointer r1
    //     0xbd70e0: add             x1, x1, HEAP, lsl #32
    // 0xbd70e4: LoadField: r2 = r1->field_13
    //     0xbd70e4: ldur            w2, [x1, #0x13]
    // 0xbd70e8: LoadField: r1 = r0->field_13
    //     0xbd70e8: ldur            x1, [x0, #0x13]
    // 0xbd70ec: r3 = LoadInt32Instr(r2)
    //     0xbd70ec: sbfx            x3, x2, #1, #0x1f
    // 0xbd70f0: sub             x2, x3, x1
    // 0xbd70f4: cmp             x2, #1
    // 0xbd70f8: b.ge            #0xbd7108
    // 0xbd70fc: mov             x1, x0
    // 0xbd7100: r2 = 1
    //     0xbd7100: movz            x2, #0x1
    // 0xbd7104: r0 = _increaseBufferSize()
    //     0xbd7104: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7108: ldur            x2, [fp, #-8]
    // 0xbd710c: ldur            x3, [fp, #-0x10]
    // 0xbd7110: r4 = 3
    //     0xbd7110: movz            x4, #0x3
    // 0xbd7114: LoadField: r5 = r2->field_b
    //     0xbd7114: ldur            w5, [x2, #0xb]
    // 0xbd7118: DecompressPointer r5
    //     0xbd7118: add             x5, x5, HEAP, lsl #32
    // 0xbd711c: LoadField: r6 = r2->field_13
    //     0xbd711c: ldur            x6, [x2, #0x13]
    // 0xbd7120: add             x0, x6, #1
    // 0xbd7124: StoreField: r2->field_13 = r0
    //     0xbd7124: stur            x0, [x2, #0x13]
    // 0xbd7128: LoadField: r0 = r5->field_13
    //     0xbd7128: ldur            w0, [x5, #0x13]
    // 0xbd712c: r1 = LoadInt32Instr(r0)
    //     0xbd712c: sbfx            x1, x0, #1, #0x1f
    // 0xbd7130: mov             x0, x1
    // 0xbd7134: mov             x1, x6
    // 0xbd7138: cmp             x1, x0
    // 0xbd713c: b.hs            #0xbd7450
    // 0xbd7140: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7140: add             x0, x5, x6
    //     0xbd7144: strb            w4, [x0, #0x17]
    // 0xbd7148: LoadField: r0 = r3->field_23
    //     0xbd7148: ldur            w0, [x3, #0x23]
    // 0xbd714c: DecompressPointer r0
    //     0xbd714c: add             x0, x0, HEAP, lsl #32
    // 0xbd7150: r16 = <String>
    //     0xbd7150: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7154: stp             x2, x16, [SP, #8]
    // 0xbd7158: str             x0, [SP]
    // 0xbd715c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd715c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7160: r0 = write()
    //     0xbd7160: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7164: ldur            x0, [fp, #-8]
    // 0xbd7168: LoadField: r1 = r0->field_b
    //     0xbd7168: ldur            w1, [x0, #0xb]
    // 0xbd716c: DecompressPointer r1
    //     0xbd716c: add             x1, x1, HEAP, lsl #32
    // 0xbd7170: LoadField: r2 = r1->field_13
    //     0xbd7170: ldur            w2, [x1, #0x13]
    // 0xbd7174: LoadField: r1 = r0->field_13
    //     0xbd7174: ldur            x1, [x0, #0x13]
    // 0xbd7178: r3 = LoadInt32Instr(r2)
    //     0xbd7178: sbfx            x3, x2, #1, #0x1f
    // 0xbd717c: sub             x2, x3, x1
    // 0xbd7180: cmp             x2, #1
    // 0xbd7184: b.ge            #0xbd7194
    // 0xbd7188: mov             x1, x0
    // 0xbd718c: r2 = 1
    //     0xbd718c: movz            x2, #0x1
    // 0xbd7190: r0 = _increaseBufferSize()
    //     0xbd7190: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7194: ldur            x2, [fp, #-8]
    // 0xbd7198: ldur            x3, [fp, #-0x10]
    // 0xbd719c: r4 = 4
    //     0xbd719c: movz            x4, #0x4
    // 0xbd71a0: LoadField: r5 = r2->field_b
    //     0xbd71a0: ldur            w5, [x2, #0xb]
    // 0xbd71a4: DecompressPointer r5
    //     0xbd71a4: add             x5, x5, HEAP, lsl #32
    // 0xbd71a8: LoadField: r6 = r2->field_13
    //     0xbd71a8: ldur            x6, [x2, #0x13]
    // 0xbd71ac: add             x0, x6, #1
    // 0xbd71b0: StoreField: r2->field_13 = r0
    //     0xbd71b0: stur            x0, [x2, #0x13]
    // 0xbd71b4: LoadField: r0 = r5->field_13
    //     0xbd71b4: ldur            w0, [x5, #0x13]
    // 0xbd71b8: r1 = LoadInt32Instr(r0)
    //     0xbd71b8: sbfx            x1, x0, #1, #0x1f
    // 0xbd71bc: mov             x0, x1
    // 0xbd71c0: mov             x1, x6
    // 0xbd71c4: cmp             x1, x0
    // 0xbd71c8: b.hs            #0xbd7454
    // 0xbd71cc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd71cc: add             x0, x5, x6
    //     0xbd71d0: strb            w4, [x0, #0x17]
    // 0xbd71d4: LoadField: r0 = r3->field_27
    //     0xbd71d4: ldur            w0, [x3, #0x27]
    // 0xbd71d8: DecompressPointer r0
    //     0xbd71d8: add             x0, x0, HEAP, lsl #32
    // 0xbd71dc: r16 = <String>
    //     0xbd71dc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd71e0: stp             x2, x16, [SP, #8]
    // 0xbd71e4: str             x0, [SP]
    // 0xbd71e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd71e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd71ec: r0 = write()
    //     0xbd71ec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd71f0: ldur            x0, [fp, #-8]
    // 0xbd71f4: LoadField: r1 = r0->field_b
    //     0xbd71f4: ldur            w1, [x0, #0xb]
    // 0xbd71f8: DecompressPointer r1
    //     0xbd71f8: add             x1, x1, HEAP, lsl #32
    // 0xbd71fc: LoadField: r2 = r1->field_13
    //     0xbd71fc: ldur            w2, [x1, #0x13]
    // 0xbd7200: LoadField: r1 = r0->field_13
    //     0xbd7200: ldur            x1, [x0, #0x13]
    // 0xbd7204: r3 = LoadInt32Instr(r2)
    //     0xbd7204: sbfx            x3, x2, #1, #0x1f
    // 0xbd7208: sub             x2, x3, x1
    // 0xbd720c: cmp             x2, #1
    // 0xbd7210: b.ge            #0xbd7220
    // 0xbd7214: mov             x1, x0
    // 0xbd7218: r2 = 1
    //     0xbd7218: movz            x2, #0x1
    // 0xbd721c: r0 = _increaseBufferSize()
    //     0xbd721c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7220: ldur            x2, [fp, #-8]
    // 0xbd7224: ldur            x3, [fp, #-0x10]
    // 0xbd7228: r4 = 5
    //     0xbd7228: movz            x4, #0x5
    // 0xbd722c: LoadField: r5 = r2->field_b
    //     0xbd722c: ldur            w5, [x2, #0xb]
    // 0xbd7230: DecompressPointer r5
    //     0xbd7230: add             x5, x5, HEAP, lsl #32
    // 0xbd7234: LoadField: r6 = r2->field_13
    //     0xbd7234: ldur            x6, [x2, #0x13]
    // 0xbd7238: add             x0, x6, #1
    // 0xbd723c: StoreField: r2->field_13 = r0
    //     0xbd723c: stur            x0, [x2, #0x13]
    // 0xbd7240: LoadField: r0 = r5->field_13
    //     0xbd7240: ldur            w0, [x5, #0x13]
    // 0xbd7244: r1 = LoadInt32Instr(r0)
    //     0xbd7244: sbfx            x1, x0, #1, #0x1f
    // 0xbd7248: mov             x0, x1
    // 0xbd724c: mov             x1, x6
    // 0xbd7250: cmp             x1, x0
    // 0xbd7254: b.hs            #0xbd7458
    // 0xbd7258: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7258: add             x0, x5, x6
    //     0xbd725c: strb            w4, [x0, #0x17]
    // 0xbd7260: LoadField: r0 = r3->field_2b
    //     0xbd7260: ldur            w0, [x3, #0x2b]
    // 0xbd7264: DecompressPointer r0
    //     0xbd7264: add             x0, x0, HEAP, lsl #32
    // 0xbd7268: r16 = <String?>
    //     0xbd7268: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd726c: stp             x2, x16, [SP, #8]
    // 0xbd7270: str             x0, [SP]
    // 0xbd7274: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7274: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7278: r0 = write()
    //     0xbd7278: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd727c: ldur            x0, [fp, #-8]
    // 0xbd7280: LoadField: r1 = r0->field_b
    //     0xbd7280: ldur            w1, [x0, #0xb]
    // 0xbd7284: DecompressPointer r1
    //     0xbd7284: add             x1, x1, HEAP, lsl #32
    // 0xbd7288: LoadField: r2 = r1->field_13
    //     0xbd7288: ldur            w2, [x1, #0x13]
    // 0xbd728c: LoadField: r1 = r0->field_13
    //     0xbd728c: ldur            x1, [x0, #0x13]
    // 0xbd7290: r3 = LoadInt32Instr(r2)
    //     0xbd7290: sbfx            x3, x2, #1, #0x1f
    // 0xbd7294: sub             x2, x3, x1
    // 0xbd7298: cmp             x2, #1
    // 0xbd729c: b.ge            #0xbd72ac
    // 0xbd72a0: mov             x1, x0
    // 0xbd72a4: r2 = 1
    //     0xbd72a4: movz            x2, #0x1
    // 0xbd72a8: r0 = _increaseBufferSize()
    //     0xbd72a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd72ac: ldur            x2, [fp, #-8]
    // 0xbd72b0: ldur            x3, [fp, #-0x10]
    // 0xbd72b4: r4 = 6
    //     0xbd72b4: movz            x4, #0x6
    // 0xbd72b8: LoadField: r5 = r2->field_b
    //     0xbd72b8: ldur            w5, [x2, #0xb]
    // 0xbd72bc: DecompressPointer r5
    //     0xbd72bc: add             x5, x5, HEAP, lsl #32
    // 0xbd72c0: LoadField: r6 = r2->field_13
    //     0xbd72c0: ldur            x6, [x2, #0x13]
    // 0xbd72c4: add             x0, x6, #1
    // 0xbd72c8: StoreField: r2->field_13 = r0
    //     0xbd72c8: stur            x0, [x2, #0x13]
    // 0xbd72cc: LoadField: r0 = r5->field_13
    //     0xbd72cc: ldur            w0, [x5, #0x13]
    // 0xbd72d0: r1 = LoadInt32Instr(r0)
    //     0xbd72d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd72d4: mov             x0, x1
    // 0xbd72d8: mov             x1, x6
    // 0xbd72dc: cmp             x1, x0
    // 0xbd72e0: b.hs            #0xbd745c
    // 0xbd72e4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd72e4: add             x0, x5, x6
    //     0xbd72e8: strb            w4, [x0, #0x17]
    // 0xbd72ec: LoadField: r0 = r3->field_2f
    //     0xbd72ec: ldur            w0, [x3, #0x2f]
    // 0xbd72f0: DecompressPointer r0
    //     0xbd72f0: add             x0, x0, HEAP, lsl #32
    // 0xbd72f4: r16 = <Map<String, String>?>
    //     0xbd72f4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] TypeArguments: <Map<String, String>?>
    //     0xbd72f8: ldr             x16, [x16, #0x178]
    // 0xbd72fc: stp             x2, x16, [SP, #8]
    // 0xbd7300: str             x0, [SP]
    // 0xbd7304: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7304: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7308: r0 = write()
    //     0xbd7308: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd730c: ldur            x0, [fp, #-8]
    // 0xbd7310: LoadField: r1 = r0->field_b
    //     0xbd7310: ldur            w1, [x0, #0xb]
    // 0xbd7314: DecompressPointer r1
    //     0xbd7314: add             x1, x1, HEAP, lsl #32
    // 0xbd7318: LoadField: r2 = r1->field_13
    //     0xbd7318: ldur            w2, [x1, #0x13]
    // 0xbd731c: LoadField: r1 = r0->field_13
    //     0xbd731c: ldur            x1, [x0, #0x13]
    // 0xbd7320: r3 = LoadInt32Instr(r2)
    //     0xbd7320: sbfx            x3, x2, #1, #0x1f
    // 0xbd7324: sub             x2, x3, x1
    // 0xbd7328: cmp             x2, #1
    // 0xbd732c: b.ge            #0xbd733c
    // 0xbd7330: mov             x1, x0
    // 0xbd7334: r2 = 1
    //     0xbd7334: movz            x2, #0x1
    // 0xbd7338: r0 = _increaseBufferSize()
    //     0xbd7338: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd733c: ldur            x2, [fp, #-8]
    // 0xbd7340: ldur            x3, [fp, #-0x10]
    // 0xbd7344: r4 = 7
    //     0xbd7344: movz            x4, #0x7
    // 0xbd7348: LoadField: r5 = r2->field_b
    //     0xbd7348: ldur            w5, [x2, #0xb]
    // 0xbd734c: DecompressPointer r5
    //     0xbd734c: add             x5, x5, HEAP, lsl #32
    // 0xbd7350: LoadField: r6 = r2->field_13
    //     0xbd7350: ldur            x6, [x2, #0x13]
    // 0xbd7354: add             x0, x6, #1
    // 0xbd7358: StoreField: r2->field_13 = r0
    //     0xbd7358: stur            x0, [x2, #0x13]
    // 0xbd735c: LoadField: r0 = r5->field_13
    //     0xbd735c: ldur            w0, [x5, #0x13]
    // 0xbd7360: r1 = LoadInt32Instr(r0)
    //     0xbd7360: sbfx            x1, x0, #1, #0x1f
    // 0xbd7364: mov             x0, x1
    // 0xbd7368: mov             x1, x6
    // 0xbd736c: cmp             x1, x0
    // 0xbd7370: b.hs            #0xbd7460
    // 0xbd7374: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7374: add             x0, x5, x6
    //     0xbd7378: strb            w4, [x0, #0x17]
    // 0xbd737c: LoadField: r0 = r3->field_33
    //     0xbd737c: ldur            w0, [x3, #0x33]
    // 0xbd7380: DecompressPointer r0
    //     0xbd7380: add             x0, x0, HEAP, lsl #32
    // 0xbd7384: r16 = <String?>
    //     0xbd7384: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd7388: stp             x2, x16, [SP, #8]
    // 0xbd738c: str             x0, [SP]
    // 0xbd7390: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7390: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7394: r0 = write()
    //     0xbd7394: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7398: ldur            x0, [fp, #-8]
    // 0xbd739c: LoadField: r1 = r0->field_b
    //     0xbd739c: ldur            w1, [x0, #0xb]
    // 0xbd73a0: DecompressPointer r1
    //     0xbd73a0: add             x1, x1, HEAP, lsl #32
    // 0xbd73a4: LoadField: r2 = r1->field_13
    //     0xbd73a4: ldur            w2, [x1, #0x13]
    // 0xbd73a8: LoadField: r1 = r0->field_13
    //     0xbd73a8: ldur            x1, [x0, #0x13]
    // 0xbd73ac: r3 = LoadInt32Instr(r2)
    //     0xbd73ac: sbfx            x3, x2, #1, #0x1f
    // 0xbd73b0: sub             x2, x3, x1
    // 0xbd73b4: cmp             x2, #1
    // 0xbd73b8: b.ge            #0xbd73c8
    // 0xbd73bc: mov             x1, x0
    // 0xbd73c0: r2 = 1
    //     0xbd73c0: movz            x2, #0x1
    // 0xbd73c4: r0 = _increaseBufferSize()
    //     0xbd73c4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd73c8: ldur            x2, [fp, #-8]
    // 0xbd73cc: ldur            x3, [fp, #-0x10]
    // 0xbd73d0: r4 = 8
    //     0xbd73d0: movz            x4, #0x8
    // 0xbd73d4: LoadField: r5 = r2->field_b
    //     0xbd73d4: ldur            w5, [x2, #0xb]
    // 0xbd73d8: DecompressPointer r5
    //     0xbd73d8: add             x5, x5, HEAP, lsl #32
    // 0xbd73dc: LoadField: r6 = r2->field_13
    //     0xbd73dc: ldur            x6, [x2, #0x13]
    // 0xbd73e0: add             x0, x6, #1
    // 0xbd73e4: StoreField: r2->field_13 = r0
    //     0xbd73e4: stur            x0, [x2, #0x13]
    // 0xbd73e8: LoadField: r0 = r5->field_13
    //     0xbd73e8: ldur            w0, [x5, #0x13]
    // 0xbd73ec: r1 = LoadInt32Instr(r0)
    //     0xbd73ec: sbfx            x1, x0, #1, #0x1f
    // 0xbd73f0: mov             x0, x1
    // 0xbd73f4: mov             x1, x6
    // 0xbd73f8: cmp             x1, x0
    // 0xbd73fc: b.hs            #0xbd7464
    // 0xbd7400: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7400: add             x0, x5, x6
    //     0xbd7404: strb            w4, [x0, #0x17]
    // 0xbd7408: LoadField: r0 = r3->field_37
    //     0xbd7408: ldur            w0, [x3, #0x37]
    // 0xbd740c: DecompressPointer r0
    //     0xbd740c: add             x0, x0, HEAP, lsl #32
    // 0xbd7410: r16 = <Map<String, dynamic>?>
    //     0xbd7410: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b180] TypeArguments: <Map<String, dynamic>?>
    //     0xbd7414: ldr             x16, [x16, #0x180]
    // 0xbd7418: stp             x2, x16, [SP, #8]
    // 0xbd741c: str             x0, [SP]
    // 0xbd7420: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7420: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7424: r0 = write()
    //     0xbd7424: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7428: r0 = Null
    //     0xbd7428: mov             x0, NULL
    // 0xbd742c: LeaveFrame
    //     0xbd742c: mov             SP, fp
    //     0xbd7430: ldp             fp, lr, [SP], #0x10
    // 0xbd7434: ret
    //     0xbd7434: ret             
    // 0xbd7438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd743c: b               #0xbd6e9c
    // 0xbd7440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7440: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7444: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7444: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7448: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd744c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd744c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7450: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7450: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7454: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7454: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7458: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7458: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd745c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd745c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7460: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7460: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7464: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7464: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0318, size: 0x24
    // 0xbf0318: r1 = 62
    //     0xbf0318: movz            x1, #0x3e
    // 0xbf031c: r16 = LoadInt32Instr(r1)
    //     0xbf031c: sbfx            x16, x1, #1, #0x1f
    // 0xbf0320: r17 = 11601
    //     0xbf0320: movz            x17, #0x2d51
    // 0xbf0324: mul             x0, x16, x17
    // 0xbf0328: umulh           x16, x16, x17
    // 0xbf032c: eor             x0, x0, x16
    // 0xbf0330: r0 = 0
    //     0xbf0330: eor             x0, x0, x0, lsr #32
    // 0xbf0334: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0338: ret
    //     0xbf0338: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76ddc, size: 0x9c
    // 0xd76ddc: EnterFrame
    //     0xd76ddc: stp             fp, lr, [SP, #-0x10]!
    //     0xd76de0: mov             fp, SP
    // 0xd76de4: AllocStack(0x10)
    //     0xd76de4: sub             SP, SP, #0x10
    // 0xd76de8: CheckStackOverflow
    //     0xd76de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76dec: cmp             SP, x16
    //     0xd76df0: b.ls            #0xd76e70
    // 0xd76df4: ldr             x0, [fp, #0x10]
    // 0xd76df8: cmp             w0, NULL
    // 0xd76dfc: b.ne            #0xd76e10
    // 0xd76e00: r0 = false
    //     0xd76e00: add             x0, NULL, #0x30  ; false
    // 0xd76e04: LeaveFrame
    //     0xd76e04: mov             SP, fp
    //     0xd76e08: ldp             fp, lr, [SP], #0x10
    // 0xd76e0c: ret
    //     0xd76e0c: ret             
    // 0xd76e10: ldr             x1, [fp, #0x18]
    // 0xd76e14: cmp             w1, w0
    // 0xd76e18: b.ne            #0xd76e24
    // 0xd76e1c: r0 = true
    //     0xd76e1c: add             x0, NULL, #0x20  ; true
    // 0xd76e20: b               #0xd76e64
    // 0xd76e24: r1 = 60
    //     0xd76e24: movz            x1, #0x3c
    // 0xd76e28: branchIfSmi(r0, 0xd76e34)
    //     0xd76e28: tbz             w0, #0, #0xd76e34
    // 0xd76e2c: r1 = LoadClassIdInstr(r0)
    //     0xd76e2c: ldur            x1, [x0, #-1]
    //     0xd76e30: ubfx            x1, x1, #0xc, #0x14
    // 0xd76e34: cmp             x1, #0x670
    // 0xd76e38: b.ne            #0xd76e60
    // 0xd76e3c: r16 = SummaryAdapter
    //     0xd76e3c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b158] Type: SummaryAdapter
    //     0xd76e40: ldr             x16, [x16, #0x158]
    // 0xd76e44: r30 = SummaryAdapter
    //     0xd76e44: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b158] Type: SummaryAdapter
    //     0xd76e48: ldr             lr, [lr, #0x158]
    // 0xd76e4c: stp             lr, x16, [SP]
    // 0xd76e50: r0 = ==()
    //     0xd76e50: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76e54: tbnz            w0, #4, #0xd76e60
    // 0xd76e58: r0 = true
    //     0xd76e58: add             x0, NULL, #0x20  ; true
    // 0xd76e5c: b               #0xd76e64
    // 0xd76e60: r0 = false
    //     0xd76e60: add             x0, NULL, #0x30  ; false
    // 0xd76e64: LeaveFrame
    //     0xd76e64: mov             SP, fp
    //     0xd76e68: ldp             fp, lr, [SP], #0x10
    // 0xd76e6c: ret
    //     0xd76e6c: ret             
    // 0xd76e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76e74: b               #0xd76df4
  }
}
