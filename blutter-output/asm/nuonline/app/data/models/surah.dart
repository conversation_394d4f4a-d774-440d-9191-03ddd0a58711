// lib: , url: package:nuonline/app/data/models/surah.dart

// class id: 1050053, size: 0x8
class :: {
}

// class id: 1600, size: 0x30, field offset: 0x14
class Surah extends HiveObject {

  String toJson(Surah) {
    // ** addr: 0x83bd10, size: 0x48
    // 0x83bd10: EnterFrame
    //     0x83bd10: stp             fp, lr, [SP, #-0x10]!
    //     0x83bd14: mov             fp, SP
    // 0x83bd18: CheckStackOverflow
    //     0x83bd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83bd1c: cmp             SP, x16
    //     0x83bd20: b.ls            #0x83bd38
    // 0x83bd24: ldr             x1, [fp, #0x10]
    // 0x83bd28: r0 = toJson()
    //     0x83bd28: bl              #0x83bd40  ; [package:nuonline/app/data/models/surah.dart] Surah::toJson
    // 0x83bd2c: LeaveFrame
    //     0x83bd2c: mov             SP, fp
    //     0x83bd30: ldp             fp, lr, [SP], #0x10
    // 0x83bd34: ret
    //     0x83bd34: ret             
    // 0x83bd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83bd38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83bd3c: b               #0x83bd24
  }
  String toJson(Surah) {
    // ** addr: 0x83bd40, size: 0x3c
    // 0x83bd40: EnterFrame
    //     0x83bd40: stp             fp, lr, [SP, #-0x10]!
    //     0x83bd44: mov             fp, SP
    // 0x83bd48: CheckStackOverflow
    //     0x83bd48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83bd4c: cmp             SP, x16
    //     0x83bd50: b.ls            #0x83bd74
    // 0x83bd54: r0 = toMap()
    //     0x83bd54: bl              #0x83bd7c  ; [package:nuonline/app/data/models/surah.dart] Surah::toMap
    // 0x83bd58: mov             x2, x0
    // 0x83bd5c: r1 = Instance_JsonCodec
    //     0x83bd5c: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x83bd60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x83bd60: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83bd64: r0 = encode()
    //     0x83bd64: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x83bd68: LeaveFrame
    //     0x83bd68: mov             SP, fp
    //     0x83bd6c: ldp             fp, lr, [SP], #0x10
    // 0x83bd70: ret
    //     0x83bd70: ret             
    // 0x83bd74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83bd74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83bd78: b               #0x83bd54
  }
  _ toMap(/* No info */) {
    // ** addr: 0x83bd7c, size: 0x198
    // 0x83bd7c: EnterFrame
    //     0x83bd7c: stp             fp, lr, [SP, #-0x10]!
    //     0x83bd80: mov             fp, SP
    // 0x83bd84: AllocStack(0x18)
    //     0x83bd84: sub             SP, SP, #0x18
    // 0x83bd88: SetupParameters(Surah this /* r1 => r0, fp-0x8 */)
    //     0x83bd88: mov             x0, x1
    //     0x83bd8c: stur            x1, [fp, #-8]
    // 0x83bd90: CheckStackOverflow
    //     0x83bd90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83bd94: cmp             SP, x16
    //     0x83bd98: b.ls            #0x83bf0c
    // 0x83bd9c: r1 = Null
    //     0x83bd9c: mov             x1, NULL
    // 0x83bda0: r2 = 20
    //     0x83bda0: movz            x2, #0x14
    // 0x83bda4: r0 = AllocateArray()
    //     0x83bda4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83bda8: mov             x2, x0
    // 0x83bdac: r16 = "id"
    //     0x83bdac: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x83bdb0: ldr             x16, [x16, #0x740]
    // 0x83bdb4: StoreField: r2->field_f = r16
    //     0x83bdb4: stur            w16, [x2, #0xf]
    // 0x83bdb8: ldur            x3, [fp, #-8]
    // 0x83bdbc: LoadField: r4 = r3->field_13
    //     0x83bdbc: ldur            x4, [x3, #0x13]
    // 0x83bdc0: r0 = BoxInt64Instr(r4)
    //     0x83bdc0: sbfiz           x0, x4, #1, #0x1f
    //     0x83bdc4: cmp             x4, x0, asr #1
    //     0x83bdc8: b.eq            #0x83bdd4
    //     0x83bdcc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83bdd0: stur            x4, [x0, #7]
    // 0x83bdd4: mov             x1, x2
    // 0x83bdd8: ArrayStore: r1[1] = r0  ; List_4
    //     0x83bdd8: add             x25, x1, #0x13
    //     0x83bddc: str             w0, [x25]
    //     0x83bde0: tbz             w0, #0, #0x83bdfc
    //     0x83bde4: ldurb           w16, [x1, #-1]
    //     0x83bde8: ldurb           w17, [x0, #-1]
    //     0x83bdec: and             x16, x17, x16, lsr #2
    //     0x83bdf0: tst             x16, HEAP, lsr #32
    //     0x83bdf4: b.eq            #0x83bdfc
    //     0x83bdf8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83bdfc: r16 = "verseCount"
    //     0x83bdfc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23648] "verseCount"
    //     0x83be00: ldr             x16, [x16, #0x648]
    // 0x83be04: ArrayStore: r2[0] = r16  ; List_4
    //     0x83be04: stur            w16, [x2, #0x17]
    // 0x83be08: LoadField: r4 = r3->field_1b
    //     0x83be08: ldur            x4, [x3, #0x1b]
    // 0x83be0c: r0 = BoxInt64Instr(r4)
    //     0x83be0c: sbfiz           x0, x4, #1, #0x1f
    //     0x83be10: cmp             x4, x0, asr #1
    //     0x83be14: b.eq            #0x83be20
    //     0x83be18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83be1c: stur            x4, [x0, #7]
    // 0x83be20: mov             x1, x2
    // 0x83be24: ArrayStore: r1[3] = r0  ; List_4
    //     0x83be24: add             x25, x1, #0x1b
    //     0x83be28: str             w0, [x25]
    //     0x83be2c: tbz             w0, #0, #0x83be48
    //     0x83be30: ldurb           w16, [x1, #-1]
    //     0x83be34: ldurb           w17, [x0, #-1]
    //     0x83be38: and             x16, x17, x16, lsr #2
    //     0x83be3c: tst             x16, HEAP, lsr #32
    //     0x83be40: b.eq            #0x83be48
    //     0x83be44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83be48: r16 = "name"
    //     0x83be48: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x83be4c: StoreField: r2->field_1f = r16
    //     0x83be4c: stur            w16, [x2, #0x1f]
    // 0x83be50: LoadField: r0 = r3->field_23
    //     0x83be50: ldur            w0, [x3, #0x23]
    // 0x83be54: DecompressPointer r0
    //     0x83be54: add             x0, x0, HEAP, lsl #32
    // 0x83be58: mov             x1, x2
    // 0x83be5c: ArrayStore: r1[5] = r0  ; List_4
    //     0x83be5c: add             x25, x1, #0x23
    //     0x83be60: str             w0, [x25]
    //     0x83be64: tbz             w0, #0, #0x83be80
    //     0x83be68: ldurb           w16, [x1, #-1]
    //     0x83be6c: ldurb           w17, [x0, #-1]
    //     0x83be70: and             x16, x17, x16, lsr #2
    //     0x83be74: tst             x16, HEAP, lsr #32
    //     0x83be78: b.eq            #0x83be80
    //     0x83be7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83be80: r16 = "type"
    //     0x83be80: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x83be84: StoreField: r2->field_27 = r16
    //     0x83be84: stur            w16, [x2, #0x27]
    // 0x83be88: LoadField: r0 = r3->field_27
    //     0x83be88: ldur            w0, [x3, #0x27]
    // 0x83be8c: DecompressPointer r0
    //     0x83be8c: add             x0, x0, HEAP, lsl #32
    // 0x83be90: mov             x1, x2
    // 0x83be94: ArrayStore: r1[7] = r0  ; List_4
    //     0x83be94: add             x25, x1, #0x2b
    //     0x83be98: str             w0, [x25]
    //     0x83be9c: tbz             w0, #0, #0x83beb8
    //     0x83bea0: ldurb           w16, [x1, #-1]
    //     0x83bea4: ldurb           w17, [x0, #-1]
    //     0x83bea8: and             x16, x17, x16, lsr #2
    //     0x83beac: tst             x16, HEAP, lsr #32
    //     0x83beb0: b.eq            #0x83beb8
    //     0x83beb4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83beb8: r16 = "translate"
    //     0x83beb8: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e40] "translate"
    //     0x83bebc: ldr             x16, [x16, #0xe40]
    // 0x83bec0: StoreField: r2->field_2f = r16
    //     0x83bec0: stur            w16, [x2, #0x2f]
    // 0x83bec4: LoadField: r0 = r3->field_2b
    //     0x83bec4: ldur            w0, [x3, #0x2b]
    // 0x83bec8: DecompressPointer r0
    //     0x83bec8: add             x0, x0, HEAP, lsl #32
    // 0x83becc: mov             x1, x2
    // 0x83bed0: ArrayStore: r1[9] = r0  ; List_4
    //     0x83bed0: add             x25, x1, #0x33
    //     0x83bed4: str             w0, [x25]
    //     0x83bed8: tbz             w0, #0, #0x83bef4
    //     0x83bedc: ldurb           w16, [x1, #-1]
    //     0x83bee0: ldurb           w17, [x0, #-1]
    //     0x83bee4: and             x16, x17, x16, lsr #2
    //     0x83bee8: tst             x16, HEAP, lsr #32
    //     0x83beec: b.eq            #0x83bef4
    //     0x83bef0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83bef4: r16 = <String, dynamic>
    //     0x83bef4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x83bef8: stp             x2, x16, [SP]
    // 0x83befc: r0 = Map._fromLiteral()
    //     0x83befc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83bf00: LeaveFrame
    //     0x83bf00: mov             SP, fp
    //     0x83bf04: ldp             fp, lr, [SP], #0x10
    // 0x83bf08: ret
    //     0x83bf08: ret             
    // 0x83bf0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83bf0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83bf10: b               #0x83bd9c
  }
  _ Surah(/* No info */) {
    // ** addr: 0x83eb18, size: 0x114
    // 0x83eb18: EnterFrame
    //     0x83eb18: stp             fp, lr, [SP, #-0x10]!
    //     0x83eb1c: mov             fp, SP
    // 0x83eb20: AllocStack(0x18)
    //     0x83eb20: sub             SP, SP, #0x18
    // 0x83eb24: SetupParameters(Surah this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r1 */, {dynamic translate = Null /* r4 */})
    //     0x83eb24: mov             x0, x3
    //     0x83eb28: mov             x3, x1
    //     0x83eb2c: stur            x1, [fp, #-8]
    //     0x83eb30: mov             x1, x5
    //     0x83eb34: ldur            w5, [x4, #0x13]
    //     0x83eb38: ldur            w7, [x4, #0x1f]
    //     0x83eb3c: add             x7, x7, HEAP, lsl #32
    //     0x83eb40: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e40] "translate"
    //     0x83eb44: ldr             x16, [x16, #0xe40]
    //     0x83eb48: cmp             w7, w16
    //     0x83eb4c: b.ne            #0x83eb6c
    //     0x83eb50: ldur            w7, [x4, #0x23]
    //     0x83eb54: add             x7, x7, HEAP, lsl #32
    //     0x83eb58: sub             w4, w5, w7
    //     0x83eb5c: add             x5, fp, w4, sxtw #2
    //     0x83eb60: ldr             x5, [x5, #8]
    //     0x83eb64: mov             x4, x5
    //     0x83eb68: b               #0x83eb70
    //     0x83eb6c: mov             x4, NULL
    // 0x83eb70: CheckStackOverflow
    //     0x83eb70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83eb74: cmp             SP, x16
    //     0x83eb78: b.ls            #0x83ec24
    // 0x83eb7c: StoreField: r3->field_13 = r2
    //     0x83eb7c: stur            x2, [x3, #0x13]
    // 0x83eb80: StoreField: r3->field_1b = r6
    //     0x83eb80: stur            x6, [x3, #0x1b]
    // 0x83eb84: StoreField: r3->field_23 = r0
    //     0x83eb84: stur            w0, [x3, #0x23]
    //     0x83eb88: ldurb           w16, [x3, #-1]
    //     0x83eb8c: ldurb           w17, [x0, #-1]
    //     0x83eb90: and             x16, x17, x16, lsr #2
    //     0x83eb94: tst             x16, HEAP, lsr #32
    //     0x83eb98: b.eq            #0x83eba0
    //     0x83eb9c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x83eba0: mov             x0, x1
    // 0x83eba4: StoreField: r3->field_27 = r0
    //     0x83eba4: stur            w0, [x3, #0x27]
    //     0x83eba8: ldurb           w16, [x3, #-1]
    //     0x83ebac: ldurb           w17, [x0, #-1]
    //     0x83ebb0: and             x16, x17, x16, lsr #2
    //     0x83ebb4: tst             x16, HEAP, lsr #32
    //     0x83ebb8: b.eq            #0x83ebc0
    //     0x83ebbc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x83ebc0: mov             x0, x4
    // 0x83ebc4: StoreField: r3->field_2b = r0
    //     0x83ebc4: stur            w0, [x3, #0x2b]
    //     0x83ebc8: ldurb           w16, [x3, #-1]
    //     0x83ebcc: ldurb           w17, [x0, #-1]
    //     0x83ebd0: and             x16, x17, x16, lsr #2
    //     0x83ebd4: tst             x16, HEAP, lsr #32
    //     0x83ebd8: b.eq            #0x83ebe0
    //     0x83ebdc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x83ebe0: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x83ebe0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x83ebe4: ldr             x16, [x16, #0x9f8]
    // 0x83ebe8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x83ebec: stp             lr, x16, [SP]
    // 0x83ebf0: r0 = Map._fromLiteral()
    //     0x83ebf0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83ebf4: ldur            x1, [fp, #-8]
    // 0x83ebf8: StoreField: r1->field_f = r0
    //     0x83ebf8: stur            w0, [x1, #0xf]
    //     0x83ebfc: ldurb           w16, [x1, #-1]
    //     0x83ec00: ldurb           w17, [x0, #-1]
    //     0x83ec04: and             x16, x17, x16, lsr #2
    //     0x83ec08: tst             x16, HEAP, lsr #32
    //     0x83ec0c: b.eq            #0x83ec14
    //     0x83ec10: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x83ec14: r0 = Null
    //     0x83ec14: mov             x0, NULL
    // 0x83ec18: LeaveFrame
    //     0x83ec18: mov             SP, fp
    //     0x83ec1c: ldp             fp, lr, [SP], #0x10
    // 0x83ec20: ret
    //     0x83ec20: ret             
    // 0x83ec24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83ec24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83ec28: b               #0x83eb7c
  }
  factory _ Surah.fromMap(/* No info */) {
    // ** addr: 0x8ae760, size: 0x288
    // 0x8ae760: EnterFrame
    //     0x8ae760: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae764: mov             fp, SP
    // 0x8ae768: AllocStack(0x40)
    //     0x8ae768: sub             SP, SP, #0x40
    // 0x8ae76c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8ae76c: mov             x3, x2
    //     0x8ae770: stur            x2, [fp, #-8]
    // 0x8ae774: CheckStackOverflow
    //     0x8ae774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae778: cmp             SP, x16
    //     0x8ae77c: b.ls            #0x8ae9e0
    // 0x8ae780: r0 = LoadClassIdInstr(r3)
    //     0x8ae780: ldur            x0, [x3, #-1]
    //     0x8ae784: ubfx            x0, x0, #0xc, #0x14
    // 0x8ae788: mov             x1, x3
    // 0x8ae78c: r2 = "id"
    //     0x8ae78c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8ae790: ldr             x2, [x2, #0x740]
    // 0x8ae794: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ae794: sub             lr, x0, #0x114
    //     0x8ae798: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae79c: blr             lr
    // 0x8ae7a0: mov             x3, x0
    // 0x8ae7a4: r2 = Null
    //     0x8ae7a4: mov             x2, NULL
    // 0x8ae7a8: r1 = Null
    //     0x8ae7a8: mov             x1, NULL
    // 0x8ae7ac: stur            x3, [fp, #-0x10]
    // 0x8ae7b0: branchIfSmi(r0, 0x8ae7d8)
    //     0x8ae7b0: tbz             w0, #0, #0x8ae7d8
    // 0x8ae7b4: r4 = LoadClassIdInstr(r0)
    //     0x8ae7b4: ldur            x4, [x0, #-1]
    //     0x8ae7b8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ae7bc: sub             x4, x4, #0x3c
    // 0x8ae7c0: cmp             x4, #1
    // 0x8ae7c4: b.ls            #0x8ae7d8
    // 0x8ae7c8: r8 = int
    //     0x8ae7c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ae7cc: r3 = Null
    //     0x8ae7cc: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b678] Null
    //     0x8ae7d0: ldr             x3, [x3, #0x678]
    // 0x8ae7d4: r0 = int()
    //     0x8ae7d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ae7d8: ldur            x3, [fp, #-8]
    // 0x8ae7dc: r0 = LoadClassIdInstr(r3)
    //     0x8ae7dc: ldur            x0, [x3, #-1]
    //     0x8ae7e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ae7e4: mov             x1, x3
    // 0x8ae7e8: r2 = "verse_count"
    //     0x8ae7e8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b688] "verse_count"
    //     0x8ae7ec: ldr             x2, [x2, #0x688]
    // 0x8ae7f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ae7f0: sub             lr, x0, #0x114
    //     0x8ae7f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae7f8: blr             lr
    // 0x8ae7fc: mov             x3, x0
    // 0x8ae800: r2 = Null
    //     0x8ae800: mov             x2, NULL
    // 0x8ae804: r1 = Null
    //     0x8ae804: mov             x1, NULL
    // 0x8ae808: stur            x3, [fp, #-0x18]
    // 0x8ae80c: branchIfSmi(r0, 0x8ae834)
    //     0x8ae80c: tbz             w0, #0, #0x8ae834
    // 0x8ae810: r4 = LoadClassIdInstr(r0)
    //     0x8ae810: ldur            x4, [x0, #-1]
    //     0x8ae814: ubfx            x4, x4, #0xc, #0x14
    // 0x8ae818: sub             x4, x4, #0x3c
    // 0x8ae81c: cmp             x4, #1
    // 0x8ae820: b.ls            #0x8ae834
    // 0x8ae824: r8 = int
    //     0x8ae824: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8ae828: r3 = Null
    //     0x8ae828: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b690] Null
    //     0x8ae82c: ldr             x3, [x3, #0x690]
    // 0x8ae830: r0 = int()
    //     0x8ae830: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8ae834: ldur            x3, [fp, #-8]
    // 0x8ae838: r0 = LoadClassIdInstr(r3)
    //     0x8ae838: ldur            x0, [x3, #-1]
    //     0x8ae83c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ae840: mov             x1, x3
    // 0x8ae844: r2 = "name"
    //     0x8ae844: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8ae848: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ae848: sub             lr, x0, #0x114
    //     0x8ae84c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae850: blr             lr
    // 0x8ae854: mov             x3, x0
    // 0x8ae858: r2 = Null
    //     0x8ae858: mov             x2, NULL
    // 0x8ae85c: r1 = Null
    //     0x8ae85c: mov             x1, NULL
    // 0x8ae860: stur            x3, [fp, #-0x20]
    // 0x8ae864: r4 = 60
    //     0x8ae864: movz            x4, #0x3c
    // 0x8ae868: branchIfSmi(r0, 0x8ae874)
    //     0x8ae868: tbz             w0, #0, #0x8ae874
    // 0x8ae86c: r4 = LoadClassIdInstr(r0)
    //     0x8ae86c: ldur            x4, [x0, #-1]
    //     0x8ae870: ubfx            x4, x4, #0xc, #0x14
    // 0x8ae874: sub             x4, x4, #0x5e
    // 0x8ae878: cmp             x4, #1
    // 0x8ae87c: b.ls            #0x8ae890
    // 0x8ae880: r8 = String
    //     0x8ae880: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ae884: r3 = Null
    //     0x8ae884: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b6a0] Null
    //     0x8ae888: ldr             x3, [x3, #0x6a0]
    // 0x8ae88c: r0 = String()
    //     0x8ae88c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ae890: ldur            x3, [fp, #-8]
    // 0x8ae894: r0 = LoadClassIdInstr(r3)
    //     0x8ae894: ldur            x0, [x3, #-1]
    //     0x8ae898: ubfx            x0, x0, #0xc, #0x14
    // 0x8ae89c: mov             x1, x3
    // 0x8ae8a0: r2 = "type"
    //     0x8ae8a0: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x8ae8a4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ae8a4: sub             lr, x0, #0x114
    //     0x8ae8a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae8ac: blr             lr
    // 0x8ae8b0: mov             x3, x0
    // 0x8ae8b4: r2 = Null
    //     0x8ae8b4: mov             x2, NULL
    // 0x8ae8b8: r1 = Null
    //     0x8ae8b8: mov             x1, NULL
    // 0x8ae8bc: stur            x3, [fp, #-0x28]
    // 0x8ae8c0: r4 = 60
    //     0x8ae8c0: movz            x4, #0x3c
    // 0x8ae8c4: branchIfSmi(r0, 0x8ae8d0)
    //     0x8ae8c4: tbz             w0, #0, #0x8ae8d0
    // 0x8ae8c8: r4 = LoadClassIdInstr(r0)
    //     0x8ae8c8: ldur            x4, [x0, #-1]
    //     0x8ae8cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8ae8d0: sub             x4, x4, #0x5e
    // 0x8ae8d4: cmp             x4, #1
    // 0x8ae8d8: b.ls            #0x8ae8ec
    // 0x8ae8dc: r8 = String
    //     0x8ae8dc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ae8e0: r3 = Null
    //     0x8ae8e0: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b6b0] Null
    //     0x8ae8e4: ldr             x3, [x3, #0x6b0]
    // 0x8ae8e8: r0 = String()
    //     0x8ae8e8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ae8ec: ldur            x1, [fp, #-8]
    // 0x8ae8f0: r0 = LoadClassIdInstr(r1)
    //     0x8ae8f0: ldur            x0, [x1, #-1]
    //     0x8ae8f4: ubfx            x0, x0, #0xc, #0x14
    // 0x8ae8f8: r2 = "translate"
    //     0x8ae8f8: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e40] "translate"
    //     0x8ae8fc: ldr             x2, [x2, #0xe40]
    // 0x8ae900: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ae900: sub             lr, x0, #0x114
    //     0x8ae904: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae908: blr             lr
    // 0x8ae90c: mov             x3, x0
    // 0x8ae910: r2 = Null
    //     0x8ae910: mov             x2, NULL
    // 0x8ae914: r1 = Null
    //     0x8ae914: mov             x1, NULL
    // 0x8ae918: stur            x3, [fp, #-8]
    // 0x8ae91c: r4 = 60
    //     0x8ae91c: movz            x4, #0x3c
    // 0x8ae920: branchIfSmi(r0, 0x8ae92c)
    //     0x8ae920: tbz             w0, #0, #0x8ae92c
    // 0x8ae924: r4 = LoadClassIdInstr(r0)
    //     0x8ae924: ldur            x4, [x0, #-1]
    //     0x8ae928: ubfx            x4, x4, #0xc, #0x14
    // 0x8ae92c: sub             x4, x4, #0x5e
    // 0x8ae930: cmp             x4, #1
    // 0x8ae934: b.ls            #0x8ae948
    // 0x8ae938: r8 = String?
    //     0x8ae938: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ae93c: r3 = Null
    //     0x8ae93c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b6c0] Null
    //     0x8ae940: ldr             x3, [x3, #0x6c0]
    // 0x8ae944: r0 = String?()
    //     0x8ae944: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ae948: ldur            x0, [fp, #-0x10]
    // 0x8ae94c: r1 = LoadInt32Instr(r0)
    //     0x8ae94c: sbfx            x1, x0, #1, #0x1f
    //     0x8ae950: tbz             w0, #0, #0x8ae958
    //     0x8ae954: ldur            x1, [x0, #7]
    // 0x8ae958: stur            x1, [fp, #-0x30]
    // 0x8ae95c: r0 = Surah()
    //     0x8ae95c: bl              #0x83ec2c  ; AllocateSurahStub -> Surah (size=0x30)
    // 0x8ae960: mov             x1, x0
    // 0x8ae964: ldur            x0, [fp, #-0x30]
    // 0x8ae968: stur            x1, [fp, #-0x10]
    // 0x8ae96c: StoreField: r1->field_13 = r0
    //     0x8ae96c: stur            x0, [x1, #0x13]
    // 0x8ae970: ldur            x0, [fp, #-0x18]
    // 0x8ae974: r2 = LoadInt32Instr(r0)
    //     0x8ae974: sbfx            x2, x0, #1, #0x1f
    //     0x8ae978: tbz             w0, #0, #0x8ae980
    //     0x8ae97c: ldur            x2, [x0, #7]
    // 0x8ae980: StoreField: r1->field_1b = r2
    //     0x8ae980: stur            x2, [x1, #0x1b]
    // 0x8ae984: ldur            x0, [fp, #-0x20]
    // 0x8ae988: StoreField: r1->field_23 = r0
    //     0x8ae988: stur            w0, [x1, #0x23]
    // 0x8ae98c: ldur            x0, [fp, #-0x28]
    // 0x8ae990: StoreField: r1->field_27 = r0
    //     0x8ae990: stur            w0, [x1, #0x27]
    // 0x8ae994: ldur            x0, [fp, #-8]
    // 0x8ae998: StoreField: r1->field_2b = r0
    //     0x8ae998: stur            w0, [x1, #0x2b]
    // 0x8ae99c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x8ae99c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x8ae9a0: ldr             x16, [x16, #0x9f8]
    // 0x8ae9a4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8ae9a8: stp             lr, x16, [SP]
    // 0x8ae9ac: r0 = Map._fromLiteral()
    //     0x8ae9ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8ae9b0: ldur            x1, [fp, #-0x10]
    // 0x8ae9b4: StoreField: r1->field_f = r0
    //     0x8ae9b4: stur            w0, [x1, #0xf]
    //     0x8ae9b8: ldurb           w16, [x1, #-1]
    //     0x8ae9bc: ldurb           w17, [x0, #-1]
    //     0x8ae9c0: and             x16, x17, x16, lsr #2
    //     0x8ae9c4: tst             x16, HEAP, lsr #32
    //     0x8ae9c8: b.eq            #0x8ae9d0
    //     0x8ae9cc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ae9d0: mov             x0, x1
    // 0x8ae9d4: LeaveFrame
    //     0x8ae9d4: mov             SP, fp
    //     0x8ae9d8: ldp             fp, lr, [SP], #0x10
    // 0x8ae9dc: ret
    //     0x8ae9dc: ret             
    // 0x8ae9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae9e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae9e4: b               #0x8ae780
  }
}

// class id: 1647, size: 0x14, field offset: 0xc
class SurahAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa65f6c, size: 0x458
    // 0xa65f6c: EnterFrame
    //     0xa65f6c: stp             fp, lr, [SP, #-0x10]!
    //     0xa65f70: mov             fp, SP
    // 0xa65f74: AllocStack(0x58)
    //     0xa65f74: sub             SP, SP, #0x58
    // 0xa65f78: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa65f78: stur            x2, [fp, #-0x20]
    // 0xa65f7c: CheckStackOverflow
    //     0xa65f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65f80: cmp             SP, x16
    //     0xa65f84: b.ls            #0xa663ac
    // 0xa65f88: LoadField: r3 = r2->field_23
    //     0xa65f88: ldur            x3, [x2, #0x23]
    // 0xa65f8c: add             x0, x3, #1
    // 0xa65f90: LoadField: r1 = r2->field_1b
    //     0xa65f90: ldur            x1, [x2, #0x1b]
    // 0xa65f94: cmp             x0, x1
    // 0xa65f98: b.gt            #0xa66350
    // 0xa65f9c: LoadField: r4 = r2->field_7
    //     0xa65f9c: ldur            w4, [x2, #7]
    // 0xa65fa0: DecompressPointer r4
    //     0xa65fa0: add             x4, x4, HEAP, lsl #32
    // 0xa65fa4: stur            x4, [fp, #-0x18]
    // 0xa65fa8: StoreField: r2->field_23 = r0
    //     0xa65fa8: stur            x0, [x2, #0x23]
    // 0xa65fac: LoadField: r0 = r4->field_13
    //     0xa65fac: ldur            w0, [x4, #0x13]
    // 0xa65fb0: r5 = LoadInt32Instr(r0)
    //     0xa65fb0: sbfx            x5, x0, #1, #0x1f
    // 0xa65fb4: mov             x0, x5
    // 0xa65fb8: mov             x1, x3
    // 0xa65fbc: stur            x5, [fp, #-0x10]
    // 0xa65fc0: cmp             x1, x0
    // 0xa65fc4: b.hs            #0xa663b4
    // 0xa65fc8: LoadField: r0 = r4->field_7
    //     0xa65fc8: ldur            x0, [x4, #7]
    // 0xa65fcc: ldrb            w1, [x0, x3]
    // 0xa65fd0: stur            x1, [fp, #-8]
    // 0xa65fd4: r16 = <int, dynamic>
    //     0xa65fd4: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa65fd8: ldr             x16, [x16, #0xac0]
    // 0xa65fdc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa65fe0: stp             lr, x16, [SP]
    // 0xa65fe4: r0 = Map._fromLiteral()
    //     0xa65fe4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa65fe8: mov             x2, x0
    // 0xa65fec: stur            x2, [fp, #-0x38]
    // 0xa65ff0: r6 = 0
    //     0xa65ff0: movz            x6, #0
    // 0xa65ff4: ldur            x3, [fp, #-0x20]
    // 0xa65ff8: ldur            x4, [fp, #-0x18]
    // 0xa65ffc: ldur            x5, [fp, #-8]
    // 0xa66000: stur            x6, [fp, #-0x30]
    // 0xa66004: CheckStackOverflow
    //     0xa66004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66008: cmp             SP, x16
    //     0xa6600c: b.ls            #0xa663b8
    // 0xa66010: cmp             x6, x5
    // 0xa66014: b.ge            #0xa660a0
    // 0xa66018: LoadField: r7 = r3->field_23
    //     0xa66018: ldur            x7, [x3, #0x23]
    // 0xa6601c: add             x0, x7, #1
    // 0xa66020: LoadField: r1 = r3->field_1b
    //     0xa66020: ldur            x1, [x3, #0x1b]
    // 0xa66024: cmp             x0, x1
    // 0xa66028: b.gt            #0xa66378
    // 0xa6602c: StoreField: r3->field_23 = r0
    //     0xa6602c: stur            x0, [x3, #0x23]
    // 0xa66030: ldur            x0, [fp, #-0x10]
    // 0xa66034: mov             x1, x7
    // 0xa66038: cmp             x1, x0
    // 0xa6603c: b.hs            #0xa663c0
    // 0xa66040: LoadField: r0 = r4->field_7
    //     0xa66040: ldur            x0, [x4, #7]
    // 0xa66044: ldrb            w8, [x0, x7]
    // 0xa66048: mov             x1, x3
    // 0xa6604c: stur            x8, [fp, #-0x28]
    // 0xa66050: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa66050: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa66054: r0 = read()
    //     0xa66054: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa66058: mov             x1, x0
    // 0xa6605c: ldur            x0, [fp, #-0x28]
    // 0xa66060: lsl             x2, x0, #1
    // 0xa66064: r16 = LoadInt32Instr(r2)
    //     0xa66064: sbfx            x16, x2, #1, #0x1f
    // 0xa66068: r17 = 11601
    //     0xa66068: movz            x17, #0x2d51
    // 0xa6606c: mul             x0, x16, x17
    // 0xa66070: umulh           x16, x16, x17
    // 0xa66074: eor             x0, x0, x16
    // 0xa66078: r0 = 0
    //     0xa66078: eor             x0, x0, x0, lsr #32
    // 0xa6607c: ubfiz           x0, x0, #1, #0x1e
    // 0xa66080: r5 = LoadInt32Instr(r0)
    //     0xa66080: sbfx            x5, x0, #1, #0x1f
    // 0xa66084: mov             x3, x1
    // 0xa66088: ldur            x1, [fp, #-0x38]
    // 0xa6608c: r0 = _set()
    //     0xa6608c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa66090: ldur            x0, [fp, #-0x30]
    // 0xa66094: add             x6, x0, #1
    // 0xa66098: ldur            x2, [fp, #-0x38]
    // 0xa6609c: b               #0xa65ff4
    // 0xa660a0: mov             x0, x2
    // 0xa660a4: mov             x1, x0
    // 0xa660a8: r2 = 0
    //     0xa660a8: movz            x2, #0
    // 0xa660ac: r0 = _getValueOrData()
    //     0xa660ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa660b0: ldur            x3, [fp, #-0x38]
    // 0xa660b4: LoadField: r1 = r3->field_f
    //     0xa660b4: ldur            w1, [x3, #0xf]
    // 0xa660b8: DecompressPointer r1
    //     0xa660b8: add             x1, x1, HEAP, lsl #32
    // 0xa660bc: cmp             w1, w0
    // 0xa660c0: b.ne            #0xa660cc
    // 0xa660c4: r4 = Null
    //     0xa660c4: mov             x4, NULL
    // 0xa660c8: b               #0xa660d0
    // 0xa660cc: mov             x4, x0
    // 0xa660d0: mov             x0, x4
    // 0xa660d4: stur            x4, [fp, #-0x18]
    // 0xa660d8: r2 = Null
    //     0xa660d8: mov             x2, NULL
    // 0xa660dc: r1 = Null
    //     0xa660dc: mov             x1, NULL
    // 0xa660e0: branchIfSmi(r0, 0xa66108)
    //     0xa660e0: tbz             w0, #0, #0xa66108
    // 0xa660e4: r4 = LoadClassIdInstr(r0)
    //     0xa660e4: ldur            x4, [x0, #-1]
    //     0xa660e8: ubfx            x4, x4, #0xc, #0x14
    // 0xa660ec: sub             x4, x4, #0x3c
    // 0xa660f0: cmp             x4, #1
    // 0xa660f4: b.ls            #0xa66108
    // 0xa660f8: r8 = int
    //     0xa660f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa660fc: r3 = Null
    //     0xa660fc: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d88] Null
    //     0xa66100: ldr             x3, [x3, #0xd88]
    // 0xa66104: r0 = int()
    //     0xa66104: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66108: ldur            x1, [fp, #-0x38]
    // 0xa6610c: r2 = 2
    //     0xa6610c: movz            x2, #0x2
    // 0xa66110: r0 = _getValueOrData()
    //     0xa66110: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66114: ldur            x3, [fp, #-0x38]
    // 0xa66118: LoadField: r1 = r3->field_f
    //     0xa66118: ldur            w1, [x3, #0xf]
    // 0xa6611c: DecompressPointer r1
    //     0xa6611c: add             x1, x1, HEAP, lsl #32
    // 0xa66120: cmp             w1, w0
    // 0xa66124: b.ne            #0xa66130
    // 0xa66128: r4 = Null
    //     0xa66128: mov             x4, NULL
    // 0xa6612c: b               #0xa66134
    // 0xa66130: mov             x4, x0
    // 0xa66134: mov             x0, x4
    // 0xa66138: stur            x4, [fp, #-0x20]
    // 0xa6613c: r2 = Null
    //     0xa6613c: mov             x2, NULL
    // 0xa66140: r1 = Null
    //     0xa66140: mov             x1, NULL
    // 0xa66144: branchIfSmi(r0, 0xa6616c)
    //     0xa66144: tbz             w0, #0, #0xa6616c
    // 0xa66148: r4 = LoadClassIdInstr(r0)
    //     0xa66148: ldur            x4, [x0, #-1]
    //     0xa6614c: ubfx            x4, x4, #0xc, #0x14
    // 0xa66150: sub             x4, x4, #0x3c
    // 0xa66154: cmp             x4, #1
    // 0xa66158: b.ls            #0xa6616c
    // 0xa6615c: r8 = int
    //     0xa6615c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66160: r3 = Null
    //     0xa66160: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d98] Null
    //     0xa66164: ldr             x3, [x3, #0xd98]
    // 0xa66168: r0 = int()
    //     0xa66168: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa6616c: ldur            x1, [fp, #-0x38]
    // 0xa66170: r2 = 4
    //     0xa66170: movz            x2, #0x4
    // 0xa66174: r0 = _getValueOrData()
    //     0xa66174: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66178: ldur            x3, [fp, #-0x38]
    // 0xa6617c: LoadField: r1 = r3->field_f
    //     0xa6617c: ldur            w1, [x3, #0xf]
    // 0xa66180: DecompressPointer r1
    //     0xa66180: add             x1, x1, HEAP, lsl #32
    // 0xa66184: cmp             w1, w0
    // 0xa66188: b.ne            #0xa66194
    // 0xa6618c: r4 = Null
    //     0xa6618c: mov             x4, NULL
    // 0xa66190: b               #0xa66198
    // 0xa66194: mov             x4, x0
    // 0xa66198: mov             x0, x4
    // 0xa6619c: stur            x4, [fp, #-0x40]
    // 0xa661a0: r2 = Null
    //     0xa661a0: mov             x2, NULL
    // 0xa661a4: r1 = Null
    //     0xa661a4: mov             x1, NULL
    // 0xa661a8: r4 = 60
    //     0xa661a8: movz            x4, #0x3c
    // 0xa661ac: branchIfSmi(r0, 0xa661b8)
    //     0xa661ac: tbz             w0, #0, #0xa661b8
    // 0xa661b0: r4 = LoadClassIdInstr(r0)
    //     0xa661b0: ldur            x4, [x0, #-1]
    //     0xa661b4: ubfx            x4, x4, #0xc, #0x14
    // 0xa661b8: sub             x4, x4, #0x5e
    // 0xa661bc: cmp             x4, #1
    // 0xa661c0: b.ls            #0xa661d4
    // 0xa661c4: r8 = String
    //     0xa661c4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa661c8: r3 = Null
    //     0xa661c8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20da8] Null
    //     0xa661cc: ldr             x3, [x3, #0xda8]
    // 0xa661d0: r0 = String()
    //     0xa661d0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa661d4: ldur            x1, [fp, #-0x38]
    // 0xa661d8: r2 = 6
    //     0xa661d8: movz            x2, #0x6
    // 0xa661dc: r0 = _getValueOrData()
    //     0xa661dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa661e0: ldur            x3, [fp, #-0x38]
    // 0xa661e4: LoadField: r1 = r3->field_f
    //     0xa661e4: ldur            w1, [x3, #0xf]
    // 0xa661e8: DecompressPointer r1
    //     0xa661e8: add             x1, x1, HEAP, lsl #32
    // 0xa661ec: cmp             w1, w0
    // 0xa661f0: b.ne            #0xa661fc
    // 0xa661f4: r4 = Null
    //     0xa661f4: mov             x4, NULL
    // 0xa661f8: b               #0xa66200
    // 0xa661fc: mov             x4, x0
    // 0xa66200: mov             x0, x4
    // 0xa66204: stur            x4, [fp, #-0x48]
    // 0xa66208: r2 = Null
    //     0xa66208: mov             x2, NULL
    // 0xa6620c: r1 = Null
    //     0xa6620c: mov             x1, NULL
    // 0xa66210: r4 = 60
    //     0xa66210: movz            x4, #0x3c
    // 0xa66214: branchIfSmi(r0, 0xa66220)
    //     0xa66214: tbz             w0, #0, #0xa66220
    // 0xa66218: r4 = LoadClassIdInstr(r0)
    //     0xa66218: ldur            x4, [x0, #-1]
    //     0xa6621c: ubfx            x4, x4, #0xc, #0x14
    // 0xa66220: sub             x4, x4, #0x5e
    // 0xa66224: cmp             x4, #1
    // 0xa66228: b.ls            #0xa6623c
    // 0xa6622c: r8 = String
    //     0xa6622c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa66230: r3 = Null
    //     0xa66230: add             x3, PP, #0x20, lsl #12  ; [pp+0x20db8] Null
    //     0xa66234: ldr             x3, [x3, #0xdb8]
    // 0xa66238: r0 = String()
    //     0xa66238: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa6623c: ldur            x1, [fp, #-0x38]
    // 0xa66240: r2 = 8
    //     0xa66240: movz            x2, #0x8
    // 0xa66244: r0 = _getValueOrData()
    //     0xa66244: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66248: mov             x1, x0
    // 0xa6624c: ldur            x0, [fp, #-0x38]
    // 0xa66250: LoadField: r2 = r0->field_f
    //     0xa66250: ldur            w2, [x0, #0xf]
    // 0xa66254: DecompressPointer r2
    //     0xa66254: add             x2, x2, HEAP, lsl #32
    // 0xa66258: cmp             w2, w1
    // 0xa6625c: b.ne            #0xa66268
    // 0xa66260: r7 = Null
    //     0xa66260: mov             x7, NULL
    // 0xa66264: b               #0xa6626c
    // 0xa66268: mov             x7, x1
    // 0xa6626c: ldur            x6, [fp, #-0x18]
    // 0xa66270: ldur            x5, [fp, #-0x20]
    // 0xa66274: ldur            x4, [fp, #-0x40]
    // 0xa66278: ldur            x3, [fp, #-0x48]
    // 0xa6627c: mov             x0, x7
    // 0xa66280: stur            x7, [fp, #-0x38]
    // 0xa66284: r2 = Null
    //     0xa66284: mov             x2, NULL
    // 0xa66288: r1 = Null
    //     0xa66288: mov             x1, NULL
    // 0xa6628c: r4 = 60
    //     0xa6628c: movz            x4, #0x3c
    // 0xa66290: branchIfSmi(r0, 0xa6629c)
    //     0xa66290: tbz             w0, #0, #0xa6629c
    // 0xa66294: r4 = LoadClassIdInstr(r0)
    //     0xa66294: ldur            x4, [x0, #-1]
    //     0xa66298: ubfx            x4, x4, #0xc, #0x14
    // 0xa6629c: sub             x4, x4, #0x5e
    // 0xa662a0: cmp             x4, #1
    // 0xa662a4: b.ls            #0xa662b8
    // 0xa662a8: r8 = String?
    //     0xa662a8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa662ac: r3 = Null
    //     0xa662ac: add             x3, PP, #0x20, lsl #12  ; [pp+0x20dc8] Null
    //     0xa662b0: ldr             x3, [x3, #0xdc8]
    // 0xa662b4: r0 = String?()
    //     0xa662b4: bl              #0x600324  ; IsType_String?_Stub
    // 0xa662b8: ldur            x0, [fp, #-0x18]
    // 0xa662bc: r1 = LoadInt32Instr(r0)
    //     0xa662bc: sbfx            x1, x0, #1, #0x1f
    //     0xa662c0: tbz             w0, #0, #0xa662c8
    //     0xa662c4: ldur            x1, [x0, #7]
    // 0xa662c8: stur            x1, [fp, #-8]
    // 0xa662cc: r0 = Surah()
    //     0xa662cc: bl              #0x83ec2c  ; AllocateSurahStub -> Surah (size=0x30)
    // 0xa662d0: mov             x1, x0
    // 0xa662d4: ldur            x0, [fp, #-8]
    // 0xa662d8: stur            x1, [fp, #-0x18]
    // 0xa662dc: StoreField: r1->field_13 = r0
    //     0xa662dc: stur            x0, [x1, #0x13]
    // 0xa662e0: ldur            x0, [fp, #-0x20]
    // 0xa662e4: r2 = LoadInt32Instr(r0)
    //     0xa662e4: sbfx            x2, x0, #1, #0x1f
    //     0xa662e8: tbz             w0, #0, #0xa662f0
    //     0xa662ec: ldur            x2, [x0, #7]
    // 0xa662f0: StoreField: r1->field_1b = r2
    //     0xa662f0: stur            x2, [x1, #0x1b]
    // 0xa662f4: ldur            x0, [fp, #-0x40]
    // 0xa662f8: StoreField: r1->field_23 = r0
    //     0xa662f8: stur            w0, [x1, #0x23]
    // 0xa662fc: ldur            x0, [fp, #-0x48]
    // 0xa66300: StoreField: r1->field_27 = r0
    //     0xa66300: stur            w0, [x1, #0x27]
    // 0xa66304: ldur            x0, [fp, #-0x38]
    // 0xa66308: StoreField: r1->field_2b = r0
    //     0xa66308: stur            w0, [x1, #0x2b]
    // 0xa6630c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa6630c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa66310: ldr             x16, [x16, #0x9f8]
    // 0xa66314: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa66318: stp             lr, x16, [SP]
    // 0xa6631c: r0 = Map._fromLiteral()
    //     0xa6631c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa66320: ldur            x1, [fp, #-0x18]
    // 0xa66324: StoreField: r1->field_f = r0
    //     0xa66324: stur            w0, [x1, #0xf]
    //     0xa66328: ldurb           w16, [x1, #-1]
    //     0xa6632c: ldurb           w17, [x0, #-1]
    //     0xa66330: and             x16, x17, x16, lsr #2
    //     0xa66334: tst             x16, HEAP, lsr #32
    //     0xa66338: b.eq            #0xa66340
    //     0xa6633c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa66340: mov             x0, x1
    // 0xa66344: LeaveFrame
    //     0xa66344: mov             SP, fp
    //     0xa66348: ldp             fp, lr, [SP], #0x10
    // 0xa6634c: ret
    //     0xa6634c: ret             
    // 0xa66350: r0 = RangeError()
    //     0xa66350: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa66354: mov             x1, x0
    // 0xa66358: r0 = "Not enough bytes available."
    //     0xa66358: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6635c: ldr             x0, [x0, #0x8a8]
    // 0xa66360: ArrayStore: r1[0] = r0  ; List_4
    //     0xa66360: stur            w0, [x1, #0x17]
    // 0xa66364: r2 = false
    //     0xa66364: add             x2, NULL, #0x30  ; false
    // 0xa66368: StoreField: r1->field_b = r2
    //     0xa66368: stur            w2, [x1, #0xb]
    // 0xa6636c: mov             x0, x1
    // 0xa66370: r0 = Throw()
    //     0xa66370: bl              #0xec04b8  ; ThrowStub
    // 0xa66374: brk             #0
    // 0xa66378: r0 = "Not enough bytes available."
    //     0xa66378: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6637c: ldr             x0, [x0, #0x8a8]
    // 0xa66380: r2 = false
    //     0xa66380: add             x2, NULL, #0x30  ; false
    // 0xa66384: r0 = RangeError()
    //     0xa66384: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa66388: mov             x1, x0
    // 0xa6638c: r0 = "Not enough bytes available."
    //     0xa6638c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa66390: ldr             x0, [x0, #0x8a8]
    // 0xa66394: ArrayStore: r1[0] = r0  ; List_4
    //     0xa66394: stur            w0, [x1, #0x17]
    // 0xa66398: r0 = false
    //     0xa66398: add             x0, NULL, #0x30  ; false
    // 0xa6639c: StoreField: r1->field_b = r0
    //     0xa6639c: stur            w0, [x1, #0xb]
    // 0xa663a0: mov             x0, x1
    // 0xa663a4: r0 = Throw()
    //     0xa663a4: bl              #0xec04b8  ; ThrowStub
    // 0xa663a8: brk             #0
    // 0xa663ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa663ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa663b0: b               #0xa65f88
    // 0xa663b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa663b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa663b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa663b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa663bc: b               #0xa66010
    // 0xa663c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa663c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd7468, size: 0x3b8
    // 0xbd7468: EnterFrame
    //     0xbd7468: stp             fp, lr, [SP, #-0x10]!
    //     0xbd746c: mov             fp, SP
    // 0xbd7470: AllocStack(0x28)
    //     0xbd7470: sub             SP, SP, #0x28
    // 0xbd7474: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd7474: mov             x4, x2
    //     0xbd7478: stur            x2, [fp, #-8]
    //     0xbd747c: stur            x3, [fp, #-0x10]
    // 0xbd7480: CheckStackOverflow
    //     0xbd7480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7484: cmp             SP, x16
    //     0xbd7488: b.ls            #0xbd7800
    // 0xbd748c: mov             x0, x3
    // 0xbd7490: r2 = Null
    //     0xbd7490: mov             x2, NULL
    // 0xbd7494: r1 = Null
    //     0xbd7494: mov             x1, NULL
    // 0xbd7498: r4 = 60
    //     0xbd7498: movz            x4, #0x3c
    // 0xbd749c: branchIfSmi(r0, 0xbd74a8)
    //     0xbd749c: tbz             w0, #0, #0xbd74a8
    // 0xbd74a0: r4 = LoadClassIdInstr(r0)
    //     0xbd74a0: ldur            x4, [x0, #-1]
    //     0xbd74a4: ubfx            x4, x4, #0xc, #0x14
    // 0xbd74a8: cmp             x4, #0x640
    // 0xbd74ac: b.eq            #0xbd74c4
    // 0xbd74b0: r8 = Surah
    //     0xbd74b0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b140] Type: Surah
    //     0xbd74b4: ldr             x8, [x8, #0x140]
    // 0xbd74b8: r3 = Null
    //     0xbd74b8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b148] Null
    //     0xbd74bc: ldr             x3, [x3, #0x148]
    // 0xbd74c0: r0 = Surah()
    //     0xbd74c0: bl              #0x83bf14  ; IsType_Surah_Stub
    // 0xbd74c4: ldur            x0, [fp, #-8]
    // 0xbd74c8: LoadField: r1 = r0->field_b
    //     0xbd74c8: ldur            w1, [x0, #0xb]
    // 0xbd74cc: DecompressPointer r1
    //     0xbd74cc: add             x1, x1, HEAP, lsl #32
    // 0xbd74d0: LoadField: r2 = r1->field_13
    //     0xbd74d0: ldur            w2, [x1, #0x13]
    // 0xbd74d4: LoadField: r1 = r0->field_13
    //     0xbd74d4: ldur            x1, [x0, #0x13]
    // 0xbd74d8: r3 = LoadInt32Instr(r2)
    //     0xbd74d8: sbfx            x3, x2, #1, #0x1f
    // 0xbd74dc: sub             x2, x3, x1
    // 0xbd74e0: cmp             x2, #1
    // 0xbd74e4: b.ge            #0xbd74f4
    // 0xbd74e8: mov             x1, x0
    // 0xbd74ec: r2 = 1
    //     0xbd74ec: movz            x2, #0x1
    // 0xbd74f0: r0 = _increaseBufferSize()
    //     0xbd74f0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd74f4: ldur            x3, [fp, #-8]
    // 0xbd74f8: r2 = 5
    //     0xbd74f8: movz            x2, #0x5
    // 0xbd74fc: LoadField: r4 = r3->field_b
    //     0xbd74fc: ldur            w4, [x3, #0xb]
    // 0xbd7500: DecompressPointer r4
    //     0xbd7500: add             x4, x4, HEAP, lsl #32
    // 0xbd7504: LoadField: r5 = r3->field_13
    //     0xbd7504: ldur            x5, [x3, #0x13]
    // 0xbd7508: add             x6, x5, #1
    // 0xbd750c: StoreField: r3->field_13 = r6
    //     0xbd750c: stur            x6, [x3, #0x13]
    // 0xbd7510: LoadField: r0 = r4->field_13
    //     0xbd7510: ldur            w0, [x4, #0x13]
    // 0xbd7514: r7 = LoadInt32Instr(r0)
    //     0xbd7514: sbfx            x7, x0, #1, #0x1f
    // 0xbd7518: mov             x0, x7
    // 0xbd751c: mov             x1, x5
    // 0xbd7520: cmp             x1, x0
    // 0xbd7524: b.hs            #0xbd7808
    // 0xbd7528: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd7528: add             x0, x4, x5
    //     0xbd752c: strb            w2, [x0, #0x17]
    // 0xbd7530: sub             x0, x7, x6
    // 0xbd7534: cmp             x0, #1
    // 0xbd7538: b.ge            #0xbd7548
    // 0xbd753c: mov             x1, x3
    // 0xbd7540: r2 = 1
    //     0xbd7540: movz            x2, #0x1
    // 0xbd7544: r0 = _increaseBufferSize()
    //     0xbd7544: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7548: ldur            x2, [fp, #-8]
    // 0xbd754c: ldur            x3, [fp, #-0x10]
    // 0xbd7550: LoadField: r4 = r2->field_b
    //     0xbd7550: ldur            w4, [x2, #0xb]
    // 0xbd7554: DecompressPointer r4
    //     0xbd7554: add             x4, x4, HEAP, lsl #32
    // 0xbd7558: LoadField: r5 = r2->field_13
    //     0xbd7558: ldur            x5, [x2, #0x13]
    // 0xbd755c: add             x0, x5, #1
    // 0xbd7560: StoreField: r2->field_13 = r0
    //     0xbd7560: stur            x0, [x2, #0x13]
    // 0xbd7564: LoadField: r0 = r4->field_13
    //     0xbd7564: ldur            w0, [x4, #0x13]
    // 0xbd7568: r1 = LoadInt32Instr(r0)
    //     0xbd7568: sbfx            x1, x0, #1, #0x1f
    // 0xbd756c: mov             x0, x1
    // 0xbd7570: mov             x1, x5
    // 0xbd7574: cmp             x1, x0
    // 0xbd7578: b.hs            #0xbd780c
    // 0xbd757c: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd757c: add             x0, x4, x5
    //     0xbd7580: strb            wzr, [x0, #0x17]
    // 0xbd7584: LoadField: r4 = r3->field_13
    //     0xbd7584: ldur            x4, [x3, #0x13]
    // 0xbd7588: r0 = BoxInt64Instr(r4)
    //     0xbd7588: sbfiz           x0, x4, #1, #0x1f
    //     0xbd758c: cmp             x4, x0, asr #1
    //     0xbd7590: b.eq            #0xbd759c
    //     0xbd7594: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7598: stur            x4, [x0, #7]
    // 0xbd759c: r16 = <int>
    //     0xbd759c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd75a0: stp             x2, x16, [SP, #8]
    // 0xbd75a4: str             x0, [SP]
    // 0xbd75a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd75a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd75ac: r0 = write()
    //     0xbd75ac: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd75b0: ldur            x0, [fp, #-8]
    // 0xbd75b4: LoadField: r1 = r0->field_b
    //     0xbd75b4: ldur            w1, [x0, #0xb]
    // 0xbd75b8: DecompressPointer r1
    //     0xbd75b8: add             x1, x1, HEAP, lsl #32
    // 0xbd75bc: LoadField: r2 = r1->field_13
    //     0xbd75bc: ldur            w2, [x1, #0x13]
    // 0xbd75c0: LoadField: r1 = r0->field_13
    //     0xbd75c0: ldur            x1, [x0, #0x13]
    // 0xbd75c4: r3 = LoadInt32Instr(r2)
    //     0xbd75c4: sbfx            x3, x2, #1, #0x1f
    // 0xbd75c8: sub             x2, x3, x1
    // 0xbd75cc: cmp             x2, #1
    // 0xbd75d0: b.ge            #0xbd75e0
    // 0xbd75d4: mov             x1, x0
    // 0xbd75d8: r2 = 1
    //     0xbd75d8: movz            x2, #0x1
    // 0xbd75dc: r0 = _increaseBufferSize()
    //     0xbd75dc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd75e0: ldur            x2, [fp, #-8]
    // 0xbd75e4: ldur            x3, [fp, #-0x10]
    // 0xbd75e8: r4 = 1
    //     0xbd75e8: movz            x4, #0x1
    // 0xbd75ec: LoadField: r5 = r2->field_b
    //     0xbd75ec: ldur            w5, [x2, #0xb]
    // 0xbd75f0: DecompressPointer r5
    //     0xbd75f0: add             x5, x5, HEAP, lsl #32
    // 0xbd75f4: LoadField: r6 = r2->field_13
    //     0xbd75f4: ldur            x6, [x2, #0x13]
    // 0xbd75f8: add             x0, x6, #1
    // 0xbd75fc: StoreField: r2->field_13 = r0
    //     0xbd75fc: stur            x0, [x2, #0x13]
    // 0xbd7600: LoadField: r0 = r5->field_13
    //     0xbd7600: ldur            w0, [x5, #0x13]
    // 0xbd7604: r1 = LoadInt32Instr(r0)
    //     0xbd7604: sbfx            x1, x0, #1, #0x1f
    // 0xbd7608: mov             x0, x1
    // 0xbd760c: mov             x1, x6
    // 0xbd7610: cmp             x1, x0
    // 0xbd7614: b.hs            #0xbd7810
    // 0xbd7618: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7618: add             x0, x5, x6
    //     0xbd761c: strb            w4, [x0, #0x17]
    // 0xbd7620: LoadField: r5 = r3->field_1b
    //     0xbd7620: ldur            x5, [x3, #0x1b]
    // 0xbd7624: r0 = BoxInt64Instr(r5)
    //     0xbd7624: sbfiz           x0, x5, #1, #0x1f
    //     0xbd7628: cmp             x5, x0, asr #1
    //     0xbd762c: b.eq            #0xbd7638
    //     0xbd7630: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7634: stur            x5, [x0, #7]
    // 0xbd7638: r16 = <int>
    //     0xbd7638: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd763c: stp             x2, x16, [SP, #8]
    // 0xbd7640: str             x0, [SP]
    // 0xbd7644: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7644: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7648: r0 = write()
    //     0xbd7648: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd764c: ldur            x0, [fp, #-8]
    // 0xbd7650: LoadField: r1 = r0->field_b
    //     0xbd7650: ldur            w1, [x0, #0xb]
    // 0xbd7654: DecompressPointer r1
    //     0xbd7654: add             x1, x1, HEAP, lsl #32
    // 0xbd7658: LoadField: r2 = r1->field_13
    //     0xbd7658: ldur            w2, [x1, #0x13]
    // 0xbd765c: LoadField: r1 = r0->field_13
    //     0xbd765c: ldur            x1, [x0, #0x13]
    // 0xbd7660: r3 = LoadInt32Instr(r2)
    //     0xbd7660: sbfx            x3, x2, #1, #0x1f
    // 0xbd7664: sub             x2, x3, x1
    // 0xbd7668: cmp             x2, #1
    // 0xbd766c: b.ge            #0xbd767c
    // 0xbd7670: mov             x1, x0
    // 0xbd7674: r2 = 1
    //     0xbd7674: movz            x2, #0x1
    // 0xbd7678: r0 = _increaseBufferSize()
    //     0xbd7678: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd767c: ldur            x2, [fp, #-8]
    // 0xbd7680: ldur            x3, [fp, #-0x10]
    // 0xbd7684: r4 = 2
    //     0xbd7684: movz            x4, #0x2
    // 0xbd7688: LoadField: r5 = r2->field_b
    //     0xbd7688: ldur            w5, [x2, #0xb]
    // 0xbd768c: DecompressPointer r5
    //     0xbd768c: add             x5, x5, HEAP, lsl #32
    // 0xbd7690: LoadField: r6 = r2->field_13
    //     0xbd7690: ldur            x6, [x2, #0x13]
    // 0xbd7694: add             x0, x6, #1
    // 0xbd7698: StoreField: r2->field_13 = r0
    //     0xbd7698: stur            x0, [x2, #0x13]
    // 0xbd769c: LoadField: r0 = r5->field_13
    //     0xbd769c: ldur            w0, [x5, #0x13]
    // 0xbd76a0: r1 = LoadInt32Instr(r0)
    //     0xbd76a0: sbfx            x1, x0, #1, #0x1f
    // 0xbd76a4: mov             x0, x1
    // 0xbd76a8: mov             x1, x6
    // 0xbd76ac: cmp             x1, x0
    // 0xbd76b0: b.hs            #0xbd7814
    // 0xbd76b4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd76b4: add             x0, x5, x6
    //     0xbd76b8: strb            w4, [x0, #0x17]
    // 0xbd76bc: LoadField: r0 = r3->field_23
    //     0xbd76bc: ldur            w0, [x3, #0x23]
    // 0xbd76c0: DecompressPointer r0
    //     0xbd76c0: add             x0, x0, HEAP, lsl #32
    // 0xbd76c4: r16 = <String>
    //     0xbd76c4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd76c8: stp             x2, x16, [SP, #8]
    // 0xbd76cc: str             x0, [SP]
    // 0xbd76d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd76d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd76d4: r0 = write()
    //     0xbd76d4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd76d8: ldur            x0, [fp, #-8]
    // 0xbd76dc: LoadField: r1 = r0->field_b
    //     0xbd76dc: ldur            w1, [x0, #0xb]
    // 0xbd76e0: DecompressPointer r1
    //     0xbd76e0: add             x1, x1, HEAP, lsl #32
    // 0xbd76e4: LoadField: r2 = r1->field_13
    //     0xbd76e4: ldur            w2, [x1, #0x13]
    // 0xbd76e8: LoadField: r1 = r0->field_13
    //     0xbd76e8: ldur            x1, [x0, #0x13]
    // 0xbd76ec: r3 = LoadInt32Instr(r2)
    //     0xbd76ec: sbfx            x3, x2, #1, #0x1f
    // 0xbd76f0: sub             x2, x3, x1
    // 0xbd76f4: cmp             x2, #1
    // 0xbd76f8: b.ge            #0xbd7708
    // 0xbd76fc: mov             x1, x0
    // 0xbd7700: r2 = 1
    //     0xbd7700: movz            x2, #0x1
    // 0xbd7704: r0 = _increaseBufferSize()
    //     0xbd7704: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7708: ldur            x2, [fp, #-8]
    // 0xbd770c: ldur            x3, [fp, #-0x10]
    // 0xbd7710: r4 = 3
    //     0xbd7710: movz            x4, #0x3
    // 0xbd7714: LoadField: r5 = r2->field_b
    //     0xbd7714: ldur            w5, [x2, #0xb]
    // 0xbd7718: DecompressPointer r5
    //     0xbd7718: add             x5, x5, HEAP, lsl #32
    // 0xbd771c: LoadField: r6 = r2->field_13
    //     0xbd771c: ldur            x6, [x2, #0x13]
    // 0xbd7720: add             x0, x6, #1
    // 0xbd7724: StoreField: r2->field_13 = r0
    //     0xbd7724: stur            x0, [x2, #0x13]
    // 0xbd7728: LoadField: r0 = r5->field_13
    //     0xbd7728: ldur            w0, [x5, #0x13]
    // 0xbd772c: r1 = LoadInt32Instr(r0)
    //     0xbd772c: sbfx            x1, x0, #1, #0x1f
    // 0xbd7730: mov             x0, x1
    // 0xbd7734: mov             x1, x6
    // 0xbd7738: cmp             x1, x0
    // 0xbd773c: b.hs            #0xbd7818
    // 0xbd7740: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7740: add             x0, x5, x6
    //     0xbd7744: strb            w4, [x0, #0x17]
    // 0xbd7748: LoadField: r0 = r3->field_27
    //     0xbd7748: ldur            w0, [x3, #0x27]
    // 0xbd774c: DecompressPointer r0
    //     0xbd774c: add             x0, x0, HEAP, lsl #32
    // 0xbd7750: r16 = <String>
    //     0xbd7750: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7754: stp             x2, x16, [SP, #8]
    // 0xbd7758: str             x0, [SP]
    // 0xbd775c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd775c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7760: r0 = write()
    //     0xbd7760: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7764: ldur            x0, [fp, #-8]
    // 0xbd7768: LoadField: r1 = r0->field_b
    //     0xbd7768: ldur            w1, [x0, #0xb]
    // 0xbd776c: DecompressPointer r1
    //     0xbd776c: add             x1, x1, HEAP, lsl #32
    // 0xbd7770: LoadField: r2 = r1->field_13
    //     0xbd7770: ldur            w2, [x1, #0x13]
    // 0xbd7774: LoadField: r1 = r0->field_13
    //     0xbd7774: ldur            x1, [x0, #0x13]
    // 0xbd7778: r3 = LoadInt32Instr(r2)
    //     0xbd7778: sbfx            x3, x2, #1, #0x1f
    // 0xbd777c: sub             x2, x3, x1
    // 0xbd7780: cmp             x2, #1
    // 0xbd7784: b.ge            #0xbd7794
    // 0xbd7788: mov             x1, x0
    // 0xbd778c: r2 = 1
    //     0xbd778c: movz            x2, #0x1
    // 0xbd7790: r0 = _increaseBufferSize()
    //     0xbd7790: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7794: ldur            x2, [fp, #-8]
    // 0xbd7798: ldur            x3, [fp, #-0x10]
    // 0xbd779c: r4 = 4
    //     0xbd779c: movz            x4, #0x4
    // 0xbd77a0: LoadField: r5 = r2->field_b
    //     0xbd77a0: ldur            w5, [x2, #0xb]
    // 0xbd77a4: DecompressPointer r5
    //     0xbd77a4: add             x5, x5, HEAP, lsl #32
    // 0xbd77a8: LoadField: r6 = r2->field_13
    //     0xbd77a8: ldur            x6, [x2, #0x13]
    // 0xbd77ac: add             x0, x6, #1
    // 0xbd77b0: StoreField: r2->field_13 = r0
    //     0xbd77b0: stur            x0, [x2, #0x13]
    // 0xbd77b4: LoadField: r0 = r5->field_13
    //     0xbd77b4: ldur            w0, [x5, #0x13]
    // 0xbd77b8: r1 = LoadInt32Instr(r0)
    //     0xbd77b8: sbfx            x1, x0, #1, #0x1f
    // 0xbd77bc: mov             x0, x1
    // 0xbd77c0: mov             x1, x6
    // 0xbd77c4: cmp             x1, x0
    // 0xbd77c8: b.hs            #0xbd781c
    // 0xbd77cc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd77cc: add             x0, x5, x6
    //     0xbd77d0: strb            w4, [x0, #0x17]
    // 0xbd77d4: LoadField: r0 = r3->field_2b
    //     0xbd77d4: ldur            w0, [x3, #0x2b]
    // 0xbd77d8: DecompressPointer r0
    //     0xbd77d8: add             x0, x0, HEAP, lsl #32
    // 0xbd77dc: r16 = <String?>
    //     0xbd77dc: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd77e0: stp             x2, x16, [SP, #8]
    // 0xbd77e4: str             x0, [SP]
    // 0xbd77e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd77e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd77ec: r0 = write()
    //     0xbd77ec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd77f0: r0 = Null
    //     0xbd77f0: mov             x0, NULL
    // 0xbd77f4: LeaveFrame
    //     0xbd77f4: mov             SP, fp
    //     0xbd77f8: ldp             fp, lr, [SP], #0x10
    // 0xbd77fc: ret
    //     0xbd77fc: ret             
    // 0xbd7800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7804: b               #0xbd748c
    // 0xbd7808: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7808: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd780c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd780c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7810: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7814: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7818: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7818: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd781c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd781c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf033c, size: 0x24
    // 0xbf033c: r1 = 44
    //     0xbf033c: movz            x1, #0x2c
    // 0xbf0340: r16 = LoadInt32Instr(r1)
    //     0xbf0340: sbfx            x16, x1, #1, #0x1f
    // 0xbf0344: r17 = 11601
    //     0xbf0344: movz            x17, #0x2d51
    // 0xbf0348: mul             x0, x16, x17
    // 0xbf034c: umulh           x16, x16, x17
    // 0xbf0350: eor             x0, x0, x16
    // 0xbf0354: r0 = 0
    //     0xbf0354: eor             x0, x0, x0, lsr #32
    // 0xbf0358: ubfiz           x0, x0, #1, #0x1e
    // 0xbf035c: ret
    //     0xbf035c: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76e78, size: 0x9c
    // 0xd76e78: EnterFrame
    //     0xd76e78: stp             fp, lr, [SP, #-0x10]!
    //     0xd76e7c: mov             fp, SP
    // 0xd76e80: AllocStack(0x10)
    //     0xd76e80: sub             SP, SP, #0x10
    // 0xd76e84: CheckStackOverflow
    //     0xd76e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76e88: cmp             SP, x16
    //     0xd76e8c: b.ls            #0xd76f0c
    // 0xd76e90: ldr             x0, [fp, #0x10]
    // 0xd76e94: cmp             w0, NULL
    // 0xd76e98: b.ne            #0xd76eac
    // 0xd76e9c: r0 = false
    //     0xd76e9c: add             x0, NULL, #0x30  ; false
    // 0xd76ea0: LeaveFrame
    //     0xd76ea0: mov             SP, fp
    //     0xd76ea4: ldp             fp, lr, [SP], #0x10
    // 0xd76ea8: ret
    //     0xd76ea8: ret             
    // 0xd76eac: ldr             x1, [fp, #0x18]
    // 0xd76eb0: cmp             w1, w0
    // 0xd76eb4: b.ne            #0xd76ec0
    // 0xd76eb8: r0 = true
    //     0xd76eb8: add             x0, NULL, #0x20  ; true
    // 0xd76ebc: b               #0xd76f00
    // 0xd76ec0: r1 = 60
    //     0xd76ec0: movz            x1, #0x3c
    // 0xd76ec4: branchIfSmi(r0, 0xd76ed0)
    //     0xd76ec4: tbz             w0, #0, #0xd76ed0
    // 0xd76ec8: r1 = LoadClassIdInstr(r0)
    //     0xd76ec8: ldur            x1, [x0, #-1]
    //     0xd76ecc: ubfx            x1, x1, #0xc, #0x14
    // 0xd76ed0: cmp             x1, #0x66f
    // 0xd76ed4: b.ne            #0xd76efc
    // 0xd76ed8: r16 = SurahAdapter
    //     0xd76ed8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b138] Type: SurahAdapter
    //     0xd76edc: ldr             x16, [x16, #0x138]
    // 0xd76ee0: r30 = SurahAdapter
    //     0xd76ee0: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b138] Type: SurahAdapter
    //     0xd76ee4: ldr             lr, [lr, #0x138]
    // 0xd76ee8: stp             lr, x16, [SP]
    // 0xd76eec: r0 = ==()
    //     0xd76eec: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76ef0: tbnz            w0, #4, #0xd76efc
    // 0xd76ef4: r0 = true
    //     0xd76ef4: add             x0, NULL, #0x20  ; true
    // 0xd76ef8: b               #0xd76f00
    // 0xd76efc: r0 = false
    //     0xd76efc: add             x0, NULL, #0x30  ; false
    // 0xd76f00: LeaveFrame
    //     0xd76f00: mov             SP, fp
    //     0xd76f04: ldp             fp, lr, [SP], #0x10
    // 0xd76f08: ret
    //     0xd76f08: ret             
    // 0xd76f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76f10: b               #0xd76e90
  }
}
