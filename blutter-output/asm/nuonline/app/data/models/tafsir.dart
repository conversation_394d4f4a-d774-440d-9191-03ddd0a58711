// lib: , url: package:nuonline/app/data/models/tafsir.dart

// class id: 1050054, size: 0x8
class :: {
}

// class id: 1599, size: 0x4c, field offset: 0x14
class Tafsir extends HiveObject {

  String toJson(Tafsir) {
    // ** addr: 0x83ec5c, size: 0x48
    // 0x83ec5c: EnterFrame
    //     0x83ec5c: stp             fp, lr, [SP, #-0x10]!
    //     0x83ec60: mov             fp, SP
    // 0x83ec64: CheckStackOverflow
    //     0x83ec64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83ec68: cmp             SP, x16
    //     0x83ec6c: b.ls            #0x83ec84
    // 0x83ec70: ldr             x1, [fp, #0x10]
    // 0x83ec74: r0 = toJson()
    //     0x83ec74: bl              #0x83ec8c  ; [package:nuonline/app/data/models/tafsir.dart] Tafsir::toJson
    // 0x83ec78: LeaveFrame
    //     0x83ec78: mov             SP, fp
    //     0x83ec7c: ldp             fp, lr, [SP], #0x10
    // 0x83ec80: ret
    //     0x83ec80: ret             
    // 0x83ec84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83ec84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83ec88: b               #0x83ec70
  }
  String toJson(Tafsir) {
    // ** addr: 0x83ec8c, size: 0x3c
    // 0x83ec8c: EnterFrame
    //     0x83ec8c: stp             fp, lr, [SP, #-0x10]!
    //     0x83ec90: mov             fp, SP
    // 0x83ec94: CheckStackOverflow
    //     0x83ec94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83ec98: cmp             SP, x16
    //     0x83ec9c: b.ls            #0x83ecc0
    // 0x83eca0: r0 = toMap()
    //     0x83eca0: bl              #0x83ecc8  ; [package:nuonline/app/data/models/tafsir.dart] Tafsir::toMap
    // 0x83eca4: mov             x2, x0
    // 0x83eca8: r1 = Instance_JsonCodec
    //     0x83eca8: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x83ecac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x83ecac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83ecb0: r0 = encode()
    //     0x83ecb0: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x83ecb4: LeaveFrame
    //     0x83ecb4: mov             SP, fp
    //     0x83ecb8: ldp             fp, lr, [SP], #0x10
    // 0x83ecbc: ret
    //     0x83ecbc: ret             
    // 0x83ecc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83ecc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83ecc4: b               #0x83eca0
  }
  _ toMap(/* No info */) {
    // ** addr: 0x83ecc8, size: 0x2c0
    // 0x83ecc8: EnterFrame
    //     0x83ecc8: stp             fp, lr, [SP, #-0x10]!
    //     0x83eccc: mov             fp, SP
    // 0x83ecd0: AllocStack(0x18)
    //     0x83ecd0: sub             SP, SP, #0x18
    // 0x83ecd4: SetupParameters(Tafsir this /* r1 => r0, fp-0x8 */)
    //     0x83ecd4: mov             x0, x1
    //     0x83ecd8: stur            x1, [fp, #-8]
    // 0x83ecdc: CheckStackOverflow
    //     0x83ecdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83ece0: cmp             SP, x16
    //     0x83ece4: b.ls            #0x83ef80
    // 0x83ece8: r1 = Null
    //     0x83ece8: mov             x1, NULL
    // 0x83ecec: r2 = 36
    //     0x83ecec: movz            x2, #0x24
    // 0x83ecf0: r0 = AllocateArray()
    //     0x83ecf0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83ecf4: mov             x2, x0
    // 0x83ecf8: r16 = "id"
    //     0x83ecf8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x83ecfc: ldr             x16, [x16, #0x740]
    // 0x83ed00: StoreField: r2->field_f = r16
    //     0x83ed00: stur            w16, [x2, #0xf]
    // 0x83ed04: ldur            x3, [fp, #-8]
    // 0x83ed08: LoadField: r4 = r3->field_13
    //     0x83ed08: ldur            x4, [x3, #0x13]
    // 0x83ed0c: r0 = BoxInt64Instr(r4)
    //     0x83ed0c: sbfiz           x0, x4, #1, #0x1f
    //     0x83ed10: cmp             x4, x0, asr #1
    //     0x83ed14: b.eq            #0x83ed20
    //     0x83ed18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83ed1c: stur            x4, [x0, #7]
    // 0x83ed20: mov             x1, x2
    // 0x83ed24: ArrayStore: r1[1] = r0  ; List_4
    //     0x83ed24: add             x25, x1, #0x13
    //     0x83ed28: str             w0, [x25]
    //     0x83ed2c: tbz             w0, #0, #0x83ed48
    //     0x83ed30: ldurb           w16, [x1, #-1]
    //     0x83ed34: ldurb           w17, [x0, #-1]
    //     0x83ed38: and             x16, x17, x16, lsr #2
    //     0x83ed3c: tst             x16, HEAP, lsr #32
    //     0x83ed40: b.eq            #0x83ed48
    //     0x83ed44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ed48: r16 = "page"
    //     0x83ed48: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x83ed4c: ldr             x16, [x16, #0x300]
    // 0x83ed50: ArrayStore: r2[0] = r16  ; List_4
    //     0x83ed50: stur            w16, [x2, #0x17]
    // 0x83ed54: LoadField: r4 = r3->field_1b
    //     0x83ed54: ldur            x4, [x3, #0x1b]
    // 0x83ed58: r0 = BoxInt64Instr(r4)
    //     0x83ed58: sbfiz           x0, x4, #1, #0x1f
    //     0x83ed5c: cmp             x4, x0, asr #1
    //     0x83ed60: b.eq            #0x83ed6c
    //     0x83ed64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83ed68: stur            x4, [x0, #7]
    // 0x83ed6c: mov             x1, x2
    // 0x83ed70: ArrayStore: r1[3] = r0  ; List_4
    //     0x83ed70: add             x25, x1, #0x1b
    //     0x83ed74: str             w0, [x25]
    //     0x83ed78: tbz             w0, #0, #0x83ed94
    //     0x83ed7c: ldurb           w16, [x1, #-1]
    //     0x83ed80: ldurb           w17, [x0, #-1]
    //     0x83ed84: and             x16, x17, x16, lsr #2
    //     0x83ed88: tst             x16, HEAP, lsr #32
    //     0x83ed8c: b.eq            #0x83ed94
    //     0x83ed90: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ed94: r16 = "number"
    //     0x83ed94: add             x16, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x83ed98: ldr             x16, [x16, #0x998]
    // 0x83ed9c: StoreField: r2->field_1f = r16
    //     0x83ed9c: stur            w16, [x2, #0x1f]
    // 0x83eda0: LoadField: r4 = r3->field_23
    //     0x83eda0: ldur            x4, [x3, #0x23]
    // 0x83eda4: r0 = BoxInt64Instr(r4)
    //     0x83eda4: sbfiz           x0, x4, #1, #0x1f
    //     0x83eda8: cmp             x4, x0, asr #1
    //     0x83edac: b.eq            #0x83edb8
    //     0x83edb0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83edb4: stur            x4, [x0, #7]
    // 0x83edb8: mov             x1, x2
    // 0x83edbc: ArrayStore: r1[5] = r0  ; List_4
    //     0x83edbc: add             x25, x1, #0x23
    //     0x83edc0: str             w0, [x25]
    //     0x83edc4: tbz             w0, #0, #0x83ede0
    //     0x83edc8: ldurb           w16, [x1, #-1]
    //     0x83edcc: ldurb           w17, [x0, #-1]
    //     0x83edd0: and             x16, x17, x16, lsr #2
    //     0x83edd4: tst             x16, HEAP, lsr #32
    //     0x83edd8: b.eq            #0x83ede0
    //     0x83eddc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ede0: r16 = "juzId"
    //     0x83ede0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f0] "juzId"
    //     0x83ede4: ldr             x16, [x16, #0xf0]
    // 0x83ede8: StoreField: r2->field_27 = r16
    //     0x83ede8: stur            w16, [x2, #0x27]
    // 0x83edec: LoadField: r4 = r3->field_2b
    //     0x83edec: ldur            x4, [x3, #0x2b]
    // 0x83edf0: r0 = BoxInt64Instr(r4)
    //     0x83edf0: sbfiz           x0, x4, #1, #0x1f
    //     0x83edf4: cmp             x4, x0, asr #1
    //     0x83edf8: b.eq            #0x83ee04
    //     0x83edfc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83ee00: stur            x4, [x0, #7]
    // 0x83ee04: mov             x1, x2
    // 0x83ee08: ArrayStore: r1[7] = r0  ; List_4
    //     0x83ee08: add             x25, x1, #0x2b
    //     0x83ee0c: str             w0, [x25]
    //     0x83ee10: tbz             w0, #0, #0x83ee2c
    //     0x83ee14: ldurb           w16, [x1, #-1]
    //     0x83ee18: ldurb           w17, [x0, #-1]
    //     0x83ee1c: and             x16, x17, x16, lsr #2
    //     0x83ee20: tst             x16, HEAP, lsr #32
    //     0x83ee24: b.eq            #0x83ee2c
    //     0x83ee28: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ee2c: r16 = "surahId"
    //     0x83ee2c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0f8] "surahId"
    //     0x83ee30: ldr             x16, [x16, #0xf8]
    // 0x83ee34: StoreField: r2->field_2f = r16
    //     0x83ee34: stur            w16, [x2, #0x2f]
    // 0x83ee38: LoadField: r4 = r3->field_33
    //     0x83ee38: ldur            x4, [x3, #0x33]
    // 0x83ee3c: r0 = BoxInt64Instr(r4)
    //     0x83ee3c: sbfiz           x0, x4, #1, #0x1f
    //     0x83ee40: cmp             x4, x0, asr #1
    //     0x83ee44: b.eq            #0x83ee50
    //     0x83ee48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83ee4c: stur            x4, [x0, #7]
    // 0x83ee50: mov             x1, x2
    // 0x83ee54: ArrayStore: r1[9] = r0  ; List_4
    //     0x83ee54: add             x25, x1, #0x33
    //     0x83ee58: str             w0, [x25]
    //     0x83ee5c: tbz             w0, #0, #0x83ee78
    //     0x83ee60: ldurb           w16, [x1, #-1]
    //     0x83ee64: ldurb           w17, [x0, #-1]
    //     0x83ee68: and             x16, x17, x16, lsr #2
    //     0x83ee6c: tst             x16, HEAP, lsr #32
    //     0x83ee70: b.eq            #0x83ee78
    //     0x83ee74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ee78: r16 = "translation"
    //     0x83ee78: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0x83ee7c: ldr             x16, [x16, #0x190]
    // 0x83ee80: StoreField: r2->field_37 = r16
    //     0x83ee80: stur            w16, [x2, #0x37]
    // 0x83ee84: LoadField: r0 = r3->field_43
    //     0x83ee84: ldur            w0, [x3, #0x43]
    // 0x83ee88: DecompressPointer r0
    //     0x83ee88: add             x0, x0, HEAP, lsl #32
    // 0x83ee8c: mov             x1, x2
    // 0x83ee90: ArrayStore: r1[11] = r0  ; List_4
    //     0x83ee90: add             x25, x1, #0x3b
    //     0x83ee94: str             w0, [x25]
    //     0x83ee98: tbz             w0, #0, #0x83eeb4
    //     0x83ee9c: ldurb           w16, [x1, #-1]
    //     0x83eea0: ldurb           w17, [x0, #-1]
    //     0x83eea4: and             x16, x17, x16, lsr #2
    //     0x83eea8: tst             x16, HEAP, lsr #32
    //     0x83eeac: b.eq            #0x83eeb4
    //     0x83eeb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83eeb4: r16 = "tafsirWajiz"
    //     0x83eeb4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b100] "tafsirWajiz"
    //     0x83eeb8: ldr             x16, [x16, #0x100]
    // 0x83eebc: StoreField: r2->field_3f = r16
    //     0x83eebc: stur            w16, [x2, #0x3f]
    // 0x83eec0: LoadField: r0 = r3->field_3b
    //     0x83eec0: ldur            w0, [x3, #0x3b]
    // 0x83eec4: DecompressPointer r0
    //     0x83eec4: add             x0, x0, HEAP, lsl #32
    // 0x83eec8: mov             x1, x2
    // 0x83eecc: ArrayStore: r1[13] = r0  ; List_4
    //     0x83eecc: add             x25, x1, #0x43
    //     0x83eed0: str             w0, [x25]
    //     0x83eed4: tbz             w0, #0, #0x83eef0
    //     0x83eed8: ldurb           w16, [x1, #-1]
    //     0x83eedc: ldurb           w17, [x0, #-1]
    //     0x83eee0: and             x16, x17, x16, lsr #2
    //     0x83eee4: tst             x16, HEAP, lsr #32
    //     0x83eee8: b.eq            #0x83eef0
    //     0x83eeec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83eef0: r16 = "tafsirTahlili"
    //     0x83eef0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b108] "tafsirTahlili"
    //     0x83eef4: ldr             x16, [x16, #0x108]
    // 0x83eef8: StoreField: r2->field_47 = r16
    //     0x83eef8: stur            w16, [x2, #0x47]
    // 0x83eefc: LoadField: r0 = r3->field_3f
    //     0x83eefc: ldur            w0, [x3, #0x3f]
    // 0x83ef00: DecompressPointer r0
    //     0x83ef00: add             x0, x0, HEAP, lsl #32
    // 0x83ef04: mov             x1, x2
    // 0x83ef08: ArrayStore: r1[15] = r0  ; List_4
    //     0x83ef08: add             x25, x1, #0x4b
    //     0x83ef0c: str             w0, [x25]
    //     0x83ef10: tbz             w0, #0, #0x83ef2c
    //     0x83ef14: ldurb           w16, [x1, #-1]
    //     0x83ef18: ldurb           w17, [x0, #-1]
    //     0x83ef1c: and             x16, x17, x16, lsr #2
    //     0x83ef20: tst             x16, HEAP, lsr #32
    //     0x83ef24: b.eq            #0x83ef2c
    //     0x83ef28: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ef2c: r16 = "surahName"
    //     0x83ef2c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b110] "surahName"
    //     0x83ef30: ldr             x16, [x16, #0x110]
    // 0x83ef34: StoreField: r2->field_4f = r16
    //     0x83ef34: stur            w16, [x2, #0x4f]
    // 0x83ef38: LoadField: r0 = r3->field_47
    //     0x83ef38: ldur            w0, [x3, #0x47]
    // 0x83ef3c: DecompressPointer r0
    //     0x83ef3c: add             x0, x0, HEAP, lsl #32
    // 0x83ef40: mov             x1, x2
    // 0x83ef44: ArrayStore: r1[17] = r0  ; List_4
    //     0x83ef44: add             x25, x1, #0x53
    //     0x83ef48: str             w0, [x25]
    //     0x83ef4c: tbz             w0, #0, #0x83ef68
    //     0x83ef50: ldurb           w16, [x1, #-1]
    //     0x83ef54: ldurb           w17, [x0, #-1]
    //     0x83ef58: and             x16, x17, x16, lsr #2
    //     0x83ef5c: tst             x16, HEAP, lsr #32
    //     0x83ef60: b.eq            #0x83ef68
    //     0x83ef64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ef68: r16 = <String, dynamic>
    //     0x83ef68: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x83ef6c: stp             x2, x16, [SP]
    // 0x83ef70: r0 = Map._fromLiteral()
    //     0x83ef70: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83ef74: LeaveFrame
    //     0x83ef74: mov             SP, fp
    //     0x83ef78: ldp             fp, lr, [SP], #0x10
    // 0x83ef7c: ret
    //     0x83ef7c: ret             
    // 0x83ef80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83ef80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83ef84: b               #0x83ece8
  }
  factory _ Tafsir.fromMap(/* No info */) {
    // ** addr: 0x866fb4, size: 0x518
    // 0x866fb4: EnterFrame
    //     0x866fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x866fb8: mov             fp, SP
    // 0x866fbc: AllocStack(0x60)
    //     0x866fbc: sub             SP, SP, #0x60
    // 0x866fc0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x866fc0: mov             x3, x2
    //     0x866fc4: stur            x2, [fp, #-8]
    // 0x866fc8: CheckStackOverflow
    //     0x866fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866fcc: cmp             SP, x16
    //     0x866fd0: b.ls            #0x8674c4
    // 0x866fd4: r0 = LoadClassIdInstr(r3)
    //     0x866fd4: ldur            x0, [x3, #-1]
    //     0x866fd8: ubfx            x0, x0, #0xc, #0x14
    // 0x866fdc: mov             x1, x3
    // 0x866fe0: r2 = "id"
    //     0x866fe0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x866fe4: ldr             x2, [x2, #0x740]
    // 0x866fe8: r0 = GDT[cid_x0 + -0x114]()
    //     0x866fe8: sub             lr, x0, #0x114
    //     0x866fec: ldr             lr, [x21, lr, lsl #3]
    //     0x866ff0: blr             lr
    // 0x866ff4: mov             x3, x0
    // 0x866ff8: r2 = Null
    //     0x866ff8: mov             x2, NULL
    // 0x866ffc: r1 = Null
    //     0x866ffc: mov             x1, NULL
    // 0x867000: stur            x3, [fp, #-0x10]
    // 0x867004: branchIfSmi(r0, 0x86702c)
    //     0x867004: tbz             w0, #0, #0x86702c
    // 0x867008: r4 = LoadClassIdInstr(r0)
    //     0x867008: ldur            x4, [x0, #-1]
    //     0x86700c: ubfx            x4, x4, #0xc, #0x14
    // 0x867010: sub             x4, x4, #0x3c
    // 0x867014: cmp             x4, #1
    // 0x867018: b.ls            #0x86702c
    // 0x86701c: r8 = int
    //     0x86701c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x867020: r3 = Null
    //     0x867020: add             x3, PP, #0x10, lsl #12  ; [pp+0x102f0] Null
    //     0x867024: ldr             x3, [x3, #0x2f0]
    // 0x867028: r0 = int()
    //     0x867028: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x86702c: ldur            x3, [fp, #-8]
    // 0x867030: r0 = LoadClassIdInstr(r3)
    //     0x867030: ldur            x0, [x3, #-1]
    //     0x867034: ubfx            x0, x0, #0xc, #0x14
    // 0x867038: mov             x1, x3
    // 0x86703c: r2 = "page"
    //     0x86703c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x867040: ldr             x2, [x2, #0x300]
    // 0x867044: r0 = GDT[cid_x0 + -0x114]()
    //     0x867044: sub             lr, x0, #0x114
    //     0x867048: ldr             lr, [x21, lr, lsl #3]
    //     0x86704c: blr             lr
    // 0x867050: cmp             w0, NULL
    // 0x867054: b.eq            #0x8670cc
    // 0x867058: ldur            x3, [fp, #-8]
    // 0x86705c: r0 = LoadClassIdInstr(r3)
    //     0x86705c: ldur            x0, [x3, #-1]
    //     0x867060: ubfx            x0, x0, #0xc, #0x14
    // 0x867064: mov             x1, x3
    // 0x867068: r2 = "page"
    //     0x867068: add             x2, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x86706c: ldr             x2, [x2, #0x300]
    // 0x867070: r0 = GDT[cid_x0 + -0x114]()
    //     0x867070: sub             lr, x0, #0x114
    //     0x867074: ldr             lr, [x21, lr, lsl #3]
    //     0x867078: blr             lr
    // 0x86707c: mov             x3, x0
    // 0x867080: r2 = Null
    //     0x867080: mov             x2, NULL
    // 0x867084: r1 = Null
    //     0x867084: mov             x1, NULL
    // 0x867088: stur            x3, [fp, #-0x18]
    // 0x86708c: branchIfSmi(r0, 0x8670b4)
    //     0x86708c: tbz             w0, #0, #0x8670b4
    // 0x867090: r4 = LoadClassIdInstr(r0)
    //     0x867090: ldur            x4, [x0, #-1]
    //     0x867094: ubfx            x4, x4, #0xc, #0x14
    // 0x867098: sub             x4, x4, #0x3c
    // 0x86709c: cmp             x4, #1
    // 0x8670a0: b.ls            #0x8670b4
    // 0x8670a4: r8 = int
    //     0x8670a4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8670a8: r3 = Null
    //     0x8670a8: add             x3, PP, #0x10, lsl #12  ; [pp+0x10308] Null
    //     0x8670ac: ldr             x3, [x3, #0x308]
    // 0x8670b0: r0 = int()
    //     0x8670b0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8670b4: ldur            x0, [fp, #-0x18]
    // 0x8670b8: r1 = LoadInt32Instr(r0)
    //     0x8670b8: sbfx            x1, x0, #1, #0x1f
    //     0x8670bc: tbz             w0, #0, #0x8670c4
    //     0x8670c0: ldur            x1, [x0, #7]
    // 0x8670c4: mov             x4, x1
    // 0x8670c8: b               #0x8670d0
    // 0x8670cc: r4 = 0
    //     0x8670cc: movz            x4, #0
    // 0x8670d0: ldur            x3, [fp, #-8]
    // 0x8670d4: stur            x4, [fp, #-0x20]
    // 0x8670d8: r0 = LoadClassIdInstr(r3)
    //     0x8670d8: ldur            x0, [x3, #-1]
    //     0x8670dc: ubfx            x0, x0, #0xc, #0x14
    // 0x8670e0: mov             x1, x3
    // 0x8670e4: r2 = "number"
    //     0x8670e4: add             x2, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x8670e8: ldr             x2, [x2, #0x998]
    // 0x8670ec: r0 = GDT[cid_x0 + -0x114]()
    //     0x8670ec: sub             lr, x0, #0x114
    //     0x8670f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8670f4: blr             lr
    // 0x8670f8: mov             x3, x0
    // 0x8670fc: r2 = Null
    //     0x8670fc: mov             x2, NULL
    // 0x867100: r1 = Null
    //     0x867100: mov             x1, NULL
    // 0x867104: stur            x3, [fp, #-0x18]
    // 0x867108: branchIfSmi(r0, 0x867130)
    //     0x867108: tbz             w0, #0, #0x867130
    // 0x86710c: r4 = LoadClassIdInstr(r0)
    //     0x86710c: ldur            x4, [x0, #-1]
    //     0x867110: ubfx            x4, x4, #0xc, #0x14
    // 0x867114: sub             x4, x4, #0x3c
    // 0x867118: cmp             x4, #1
    // 0x86711c: b.ls            #0x867130
    // 0x867120: r8 = int
    //     0x867120: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x867124: r3 = Null
    //     0x867124: add             x3, PP, #0x10, lsl #12  ; [pp+0x10318] Null
    //     0x867128: ldr             x3, [x3, #0x318]
    // 0x86712c: r0 = int()
    //     0x86712c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x867130: ldur            x3, [fp, #-8]
    // 0x867134: r0 = LoadClassIdInstr(r3)
    //     0x867134: ldur            x0, [x3, #-1]
    //     0x867138: ubfx            x0, x0, #0xc, #0x14
    // 0x86713c: mov             x1, x3
    // 0x867140: r2 = "juz_id"
    //     0x867140: add             x2, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x867144: ldr             x2, [x2, #0x328]
    // 0x867148: r0 = GDT[cid_x0 + -0x114]()
    //     0x867148: sub             lr, x0, #0x114
    //     0x86714c: ldr             lr, [x21, lr, lsl #3]
    //     0x867150: blr             lr
    // 0x867154: cmp             w0, NULL
    // 0x867158: b.eq            #0x8671d0
    // 0x86715c: ldur            x3, [fp, #-8]
    // 0x867160: r0 = LoadClassIdInstr(r3)
    //     0x867160: ldur            x0, [x3, #-1]
    //     0x867164: ubfx            x0, x0, #0xc, #0x14
    // 0x867168: mov             x1, x3
    // 0x86716c: r2 = "juz_id"
    //     0x86716c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x867170: ldr             x2, [x2, #0x328]
    // 0x867174: r0 = GDT[cid_x0 + -0x114]()
    //     0x867174: sub             lr, x0, #0x114
    //     0x867178: ldr             lr, [x21, lr, lsl #3]
    //     0x86717c: blr             lr
    // 0x867180: mov             x3, x0
    // 0x867184: r2 = Null
    //     0x867184: mov             x2, NULL
    // 0x867188: r1 = Null
    //     0x867188: mov             x1, NULL
    // 0x86718c: stur            x3, [fp, #-0x28]
    // 0x867190: branchIfSmi(r0, 0x8671b8)
    //     0x867190: tbz             w0, #0, #0x8671b8
    // 0x867194: r4 = LoadClassIdInstr(r0)
    //     0x867194: ldur            x4, [x0, #-1]
    //     0x867198: ubfx            x4, x4, #0xc, #0x14
    // 0x86719c: sub             x4, x4, #0x3c
    // 0x8671a0: cmp             x4, #1
    // 0x8671a4: b.ls            #0x8671b8
    // 0x8671a8: r8 = int
    //     0x8671a8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8671ac: r3 = Null
    //     0x8671ac: add             x3, PP, #0x10, lsl #12  ; [pp+0x10330] Null
    //     0x8671b0: ldr             x3, [x3, #0x330]
    // 0x8671b4: r0 = int()
    //     0x8671b4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8671b8: ldur            x0, [fp, #-0x28]
    // 0x8671bc: r1 = LoadInt32Instr(r0)
    //     0x8671bc: sbfx            x1, x0, #1, #0x1f
    //     0x8671c0: tbz             w0, #0, #0x8671c8
    //     0x8671c4: ldur            x1, [x0, #7]
    // 0x8671c8: mov             x4, x1
    // 0x8671cc: b               #0x8671d4
    // 0x8671d0: r4 = 0
    //     0x8671d0: movz            x4, #0
    // 0x8671d4: ldur            x3, [fp, #-8]
    // 0x8671d8: stur            x4, [fp, #-0x30]
    // 0x8671dc: r0 = LoadClassIdInstr(r3)
    //     0x8671dc: ldur            x0, [x3, #-1]
    //     0x8671e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8671e4: mov             x1, x3
    // 0x8671e8: r2 = "surah_id"
    //     0x8671e8: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x8671ec: ldr             x2, [x2, #0x340]
    // 0x8671f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x8671f0: sub             lr, x0, #0x114
    //     0x8671f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8671f8: blr             lr
    // 0x8671fc: cmp             w0, NULL
    // 0x867200: b.eq            #0x867278
    // 0x867204: ldur            x3, [fp, #-8]
    // 0x867208: r0 = LoadClassIdInstr(r3)
    //     0x867208: ldur            x0, [x3, #-1]
    //     0x86720c: ubfx            x0, x0, #0xc, #0x14
    // 0x867210: mov             x1, x3
    // 0x867214: r2 = "surah_id"
    //     0x867214: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x867218: ldr             x2, [x2, #0x340]
    // 0x86721c: r0 = GDT[cid_x0 + -0x114]()
    //     0x86721c: sub             lr, x0, #0x114
    //     0x867220: ldr             lr, [x21, lr, lsl #3]
    //     0x867224: blr             lr
    // 0x867228: mov             x3, x0
    // 0x86722c: r2 = Null
    //     0x86722c: mov             x2, NULL
    // 0x867230: r1 = Null
    //     0x867230: mov             x1, NULL
    // 0x867234: stur            x3, [fp, #-0x28]
    // 0x867238: branchIfSmi(r0, 0x867260)
    //     0x867238: tbz             w0, #0, #0x867260
    // 0x86723c: r4 = LoadClassIdInstr(r0)
    //     0x86723c: ldur            x4, [x0, #-1]
    //     0x867240: ubfx            x4, x4, #0xc, #0x14
    // 0x867244: sub             x4, x4, #0x3c
    // 0x867248: cmp             x4, #1
    // 0x86724c: b.ls            #0x867260
    // 0x867250: r8 = int
    //     0x867250: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x867254: r3 = Null
    //     0x867254: add             x3, PP, #0x10, lsl #12  ; [pp+0x10348] Null
    //     0x867258: ldr             x3, [x3, #0x348]
    // 0x86725c: r0 = int()
    //     0x86725c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x867260: ldur            x0, [fp, #-0x28]
    // 0x867264: r1 = LoadInt32Instr(r0)
    //     0x867264: sbfx            x1, x0, #1, #0x1f
    //     0x867268: tbz             w0, #0, #0x867270
    //     0x86726c: ldur            x1, [x0, #7]
    // 0x867270: mov             x8, x1
    // 0x867274: b               #0x86727c
    // 0x867278: r8 = 0
    //     0x867278: movz            x8, #0
    // 0x86727c: ldur            x3, [fp, #-8]
    // 0x867280: ldur            x7, [fp, #-0x10]
    // 0x867284: ldur            x6, [fp, #-0x20]
    // 0x867288: ldur            x5, [fp, #-0x18]
    // 0x86728c: ldur            x4, [fp, #-0x30]
    // 0x867290: stur            x8, [fp, #-0x38]
    // 0x867294: r0 = LoadClassIdInstr(r3)
    //     0x867294: ldur            x0, [x3, #-1]
    //     0x867298: ubfx            x0, x0, #0xc, #0x14
    // 0x86729c: mov             x1, x3
    // 0x8672a0: r2 = "translation_id"
    //     0x8672a0: add             x2, PP, #0x10, lsl #12  ; [pp+0x10358] "translation_id"
    //     0x8672a4: ldr             x2, [x2, #0x358]
    // 0x8672a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8672a8: sub             lr, x0, #0x114
    //     0x8672ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8672b0: blr             lr
    // 0x8672b4: mov             x3, x0
    // 0x8672b8: r2 = Null
    //     0x8672b8: mov             x2, NULL
    // 0x8672bc: r1 = Null
    //     0x8672bc: mov             x1, NULL
    // 0x8672c0: stur            x3, [fp, #-0x28]
    // 0x8672c4: r4 = 60
    //     0x8672c4: movz            x4, #0x3c
    // 0x8672c8: branchIfSmi(r0, 0x8672d4)
    //     0x8672c8: tbz             w0, #0, #0x8672d4
    // 0x8672cc: r4 = LoadClassIdInstr(r0)
    //     0x8672cc: ldur            x4, [x0, #-1]
    //     0x8672d0: ubfx            x4, x4, #0xc, #0x14
    // 0x8672d4: sub             x4, x4, #0x5e
    // 0x8672d8: cmp             x4, #1
    // 0x8672dc: b.ls            #0x8672f0
    // 0x8672e0: r8 = String
    //     0x8672e0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8672e4: r3 = Null
    //     0x8672e4: add             x3, PP, #0x10, lsl #12  ; [pp+0x10360] Null
    //     0x8672e8: ldr             x3, [x3, #0x360]
    // 0x8672ec: r0 = String()
    //     0x8672ec: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8672f0: ldur            x3, [fp, #-8]
    // 0x8672f4: r0 = LoadClassIdInstr(r3)
    //     0x8672f4: ldur            x0, [x3, #-1]
    //     0x8672f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8672fc: mov             x1, x3
    // 0x867300: r2 = "tafsir_wajiz"
    //     0x867300: add             x2, PP, #0x10, lsl #12  ; [pp+0x10370] "tafsir_wajiz"
    //     0x867304: ldr             x2, [x2, #0x370]
    // 0x867308: r0 = GDT[cid_x0 + -0x114]()
    //     0x867308: sub             lr, x0, #0x114
    //     0x86730c: ldr             lr, [x21, lr, lsl #3]
    //     0x867310: blr             lr
    // 0x867314: mov             x3, x0
    // 0x867318: r2 = Null
    //     0x867318: mov             x2, NULL
    // 0x86731c: r1 = Null
    //     0x86731c: mov             x1, NULL
    // 0x867320: stur            x3, [fp, #-0x40]
    // 0x867324: r4 = 60
    //     0x867324: movz            x4, #0x3c
    // 0x867328: branchIfSmi(r0, 0x867334)
    //     0x867328: tbz             w0, #0, #0x867334
    // 0x86732c: r4 = LoadClassIdInstr(r0)
    //     0x86732c: ldur            x4, [x0, #-1]
    //     0x867330: ubfx            x4, x4, #0xc, #0x14
    // 0x867334: sub             x4, x4, #0x5e
    // 0x867338: cmp             x4, #1
    // 0x86733c: b.ls            #0x867350
    // 0x867340: r8 = String
    //     0x867340: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x867344: r3 = Null
    //     0x867344: add             x3, PP, #0x10, lsl #12  ; [pp+0x10378] Null
    //     0x867348: ldr             x3, [x3, #0x378]
    // 0x86734c: r0 = String()
    //     0x86734c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x867350: ldur            x3, [fp, #-8]
    // 0x867354: r0 = LoadClassIdInstr(r3)
    //     0x867354: ldur            x0, [x3, #-1]
    //     0x867358: ubfx            x0, x0, #0xc, #0x14
    // 0x86735c: mov             x1, x3
    // 0x867360: r2 = "tafsir_tahlili"
    //     0x867360: add             x2, PP, #0x10, lsl #12  ; [pp+0x10388] "tafsir_tahlili"
    //     0x867364: ldr             x2, [x2, #0x388]
    // 0x867368: r0 = GDT[cid_x0 + -0x114]()
    //     0x867368: sub             lr, x0, #0x114
    //     0x86736c: ldr             lr, [x21, lr, lsl #3]
    //     0x867370: blr             lr
    // 0x867374: mov             x3, x0
    // 0x867378: r2 = Null
    //     0x867378: mov             x2, NULL
    // 0x86737c: r1 = Null
    //     0x86737c: mov             x1, NULL
    // 0x867380: stur            x3, [fp, #-0x48]
    // 0x867384: r4 = 60
    //     0x867384: movz            x4, #0x3c
    // 0x867388: branchIfSmi(r0, 0x867394)
    //     0x867388: tbz             w0, #0, #0x867394
    // 0x86738c: r4 = LoadClassIdInstr(r0)
    //     0x86738c: ldur            x4, [x0, #-1]
    //     0x867390: ubfx            x4, x4, #0xc, #0x14
    // 0x867394: sub             x4, x4, #0x5e
    // 0x867398: cmp             x4, #1
    // 0x86739c: b.ls            #0x8673b0
    // 0x8673a0: r8 = String
    //     0x8673a0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8673a4: r3 = Null
    //     0x8673a4: add             x3, PP, #0x10, lsl #12  ; [pp+0x10390] Null
    //     0x8673a8: ldr             x3, [x3, #0x390]
    // 0x8673ac: r0 = String()
    //     0x8673ac: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8673b0: ldur            x1, [fp, #-8]
    // 0x8673b4: r0 = LoadClassIdInstr(r1)
    //     0x8673b4: ldur            x0, [x1, #-1]
    //     0x8673b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8673bc: r2 = "surah_name"
    //     0x8673bc: add             x2, PP, #0x10, lsl #12  ; [pp+0x103a0] "surah_name"
    //     0x8673c0: ldr             x2, [x2, #0x3a0]
    // 0x8673c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x8673c4: sub             lr, x0, #0x114
    //     0x8673c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8673cc: blr             lr
    // 0x8673d0: mov             x3, x0
    // 0x8673d4: r2 = Null
    //     0x8673d4: mov             x2, NULL
    // 0x8673d8: r1 = Null
    //     0x8673d8: mov             x1, NULL
    // 0x8673dc: stur            x3, [fp, #-8]
    // 0x8673e0: r4 = 60
    //     0x8673e0: movz            x4, #0x3c
    // 0x8673e4: branchIfSmi(r0, 0x8673f0)
    //     0x8673e4: tbz             w0, #0, #0x8673f0
    // 0x8673e8: r4 = LoadClassIdInstr(r0)
    //     0x8673e8: ldur            x4, [x0, #-1]
    //     0x8673ec: ubfx            x4, x4, #0xc, #0x14
    // 0x8673f0: sub             x4, x4, #0x5e
    // 0x8673f4: cmp             x4, #1
    // 0x8673f8: b.ls            #0x86740c
    // 0x8673fc: r8 = String
    //     0x8673fc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x867400: r3 = Null
    //     0x867400: add             x3, PP, #0x10, lsl #12  ; [pp+0x103a8] Null
    //     0x867404: ldr             x3, [x3, #0x3a8]
    // 0x867408: r0 = String()
    //     0x867408: bl              #0xed43b0  ; IsType_String_Stub
    // 0x86740c: ldur            x0, [fp, #-0x10]
    // 0x867410: r1 = LoadInt32Instr(r0)
    //     0x867410: sbfx            x1, x0, #1, #0x1f
    //     0x867414: tbz             w0, #0, #0x86741c
    //     0x867418: ldur            x1, [x0, #7]
    // 0x86741c: stur            x1, [fp, #-0x50]
    // 0x867420: r0 = Tafsir()
    //     0x867420: bl              #0x8674cc  ; AllocateTafsirStub -> Tafsir (size=0x4c)
    // 0x867424: mov             x1, x0
    // 0x867428: ldur            x0, [fp, #-0x50]
    // 0x86742c: stur            x1, [fp, #-0x10]
    // 0x867430: StoreField: r1->field_13 = r0
    //     0x867430: stur            x0, [x1, #0x13]
    // 0x867434: ldur            x0, [fp, #-0x20]
    // 0x867438: StoreField: r1->field_1b = r0
    //     0x867438: stur            x0, [x1, #0x1b]
    // 0x86743c: ldur            x0, [fp, #-0x18]
    // 0x867440: r2 = LoadInt32Instr(r0)
    //     0x867440: sbfx            x2, x0, #1, #0x1f
    //     0x867444: tbz             w0, #0, #0x86744c
    //     0x867448: ldur            x2, [x0, #7]
    // 0x86744c: StoreField: r1->field_23 = r2
    //     0x86744c: stur            x2, [x1, #0x23]
    // 0x867450: ldur            x0, [fp, #-0x30]
    // 0x867454: StoreField: r1->field_2b = r0
    //     0x867454: stur            x0, [x1, #0x2b]
    // 0x867458: ldur            x0, [fp, #-0x38]
    // 0x86745c: StoreField: r1->field_33 = r0
    //     0x86745c: stur            x0, [x1, #0x33]
    // 0x867460: ldur            x0, [fp, #-0x40]
    // 0x867464: StoreField: r1->field_3b = r0
    //     0x867464: stur            w0, [x1, #0x3b]
    // 0x867468: ldur            x0, [fp, #-0x48]
    // 0x86746c: StoreField: r1->field_3f = r0
    //     0x86746c: stur            w0, [x1, #0x3f]
    // 0x867470: ldur            x0, [fp, #-0x28]
    // 0x867474: StoreField: r1->field_43 = r0
    //     0x867474: stur            w0, [x1, #0x43]
    // 0x867478: ldur            x0, [fp, #-8]
    // 0x86747c: StoreField: r1->field_47 = r0
    //     0x86747c: stur            w0, [x1, #0x47]
    // 0x867480: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x867480: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x867484: ldr             x16, [x16, #0x9f8]
    // 0x867488: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x86748c: stp             lr, x16, [SP]
    // 0x867490: r0 = Map._fromLiteral()
    //     0x867490: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x867494: ldur            x1, [fp, #-0x10]
    // 0x867498: StoreField: r1->field_f = r0
    //     0x867498: stur            w0, [x1, #0xf]
    //     0x86749c: ldurb           w16, [x1, #-1]
    //     0x8674a0: ldurb           w17, [x0, #-1]
    //     0x8674a4: and             x16, x17, x16, lsr #2
    //     0x8674a8: tst             x16, HEAP, lsr #32
    //     0x8674ac: b.eq            #0x8674b4
    //     0x8674b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8674b4: mov             x0, x1
    // 0x8674b8: LeaveFrame
    //     0x8674b8: mov             SP, fp
    //     0x8674bc: ldp             fp, lr, [SP], #0x10
    // 0x8674c0: ret
    //     0x8674c0: ret             
    // 0x8674c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8674c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8674c8: b               #0x866fd4
  }
}

// class id: 1646, size: 0x14, field offset: 0xc
class TafsirAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa663c4, size: 0x640
    // 0xa663c4: EnterFrame
    //     0xa663c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa663c8: mov             fp, SP
    // 0xa663cc: AllocStack(0x78)
    //     0xa663cc: sub             SP, SP, #0x78
    // 0xa663d0: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa663d0: stur            x2, [fp, #-0x20]
    // 0xa663d4: CheckStackOverflow
    //     0xa663d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa663d8: cmp             SP, x16
    //     0xa663dc: b.ls            #0xa669ec
    // 0xa663e0: LoadField: r3 = r2->field_23
    //     0xa663e0: ldur            x3, [x2, #0x23]
    // 0xa663e4: add             x0, x3, #1
    // 0xa663e8: LoadField: r1 = r2->field_1b
    //     0xa663e8: ldur            x1, [x2, #0x1b]
    // 0xa663ec: cmp             x0, x1
    // 0xa663f0: b.gt            #0xa66990
    // 0xa663f4: LoadField: r4 = r2->field_7
    //     0xa663f4: ldur            w4, [x2, #7]
    // 0xa663f8: DecompressPointer r4
    //     0xa663f8: add             x4, x4, HEAP, lsl #32
    // 0xa663fc: stur            x4, [fp, #-0x18]
    // 0xa66400: StoreField: r2->field_23 = r0
    //     0xa66400: stur            x0, [x2, #0x23]
    // 0xa66404: LoadField: r0 = r4->field_13
    //     0xa66404: ldur            w0, [x4, #0x13]
    // 0xa66408: r5 = LoadInt32Instr(r0)
    //     0xa66408: sbfx            x5, x0, #1, #0x1f
    // 0xa6640c: mov             x0, x5
    // 0xa66410: mov             x1, x3
    // 0xa66414: stur            x5, [fp, #-0x10]
    // 0xa66418: cmp             x1, x0
    // 0xa6641c: b.hs            #0xa669f4
    // 0xa66420: LoadField: r0 = r4->field_7
    //     0xa66420: ldur            x0, [x4, #7]
    // 0xa66424: ldrb            w1, [x0, x3]
    // 0xa66428: stur            x1, [fp, #-8]
    // 0xa6642c: r16 = <int, dynamic>
    //     0xa6642c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa66430: ldr             x16, [x16, #0xac0]
    // 0xa66434: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa66438: stp             lr, x16, [SP]
    // 0xa6643c: r0 = Map._fromLiteral()
    //     0xa6643c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa66440: mov             x2, x0
    // 0xa66444: stur            x2, [fp, #-0x38]
    // 0xa66448: r6 = 0
    //     0xa66448: movz            x6, #0
    // 0xa6644c: ldur            x3, [fp, #-0x20]
    // 0xa66450: ldur            x4, [fp, #-0x18]
    // 0xa66454: ldur            x5, [fp, #-8]
    // 0xa66458: stur            x6, [fp, #-0x30]
    // 0xa6645c: CheckStackOverflow
    //     0xa6645c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66460: cmp             SP, x16
    //     0xa66464: b.ls            #0xa669f8
    // 0xa66468: cmp             x6, x5
    // 0xa6646c: b.ge            #0xa664f8
    // 0xa66470: LoadField: r7 = r3->field_23
    //     0xa66470: ldur            x7, [x3, #0x23]
    // 0xa66474: add             x0, x7, #1
    // 0xa66478: LoadField: r1 = r3->field_1b
    //     0xa66478: ldur            x1, [x3, #0x1b]
    // 0xa6647c: cmp             x0, x1
    // 0xa66480: b.gt            #0xa669b8
    // 0xa66484: StoreField: r3->field_23 = r0
    //     0xa66484: stur            x0, [x3, #0x23]
    // 0xa66488: ldur            x0, [fp, #-0x10]
    // 0xa6648c: mov             x1, x7
    // 0xa66490: cmp             x1, x0
    // 0xa66494: b.hs            #0xa66a00
    // 0xa66498: LoadField: r0 = r4->field_7
    //     0xa66498: ldur            x0, [x4, #7]
    // 0xa6649c: ldrb            w8, [x0, x7]
    // 0xa664a0: mov             x1, x3
    // 0xa664a4: stur            x8, [fp, #-0x28]
    // 0xa664a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa664a8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa664ac: r0 = read()
    //     0xa664ac: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa664b0: mov             x1, x0
    // 0xa664b4: ldur            x0, [fp, #-0x28]
    // 0xa664b8: lsl             x2, x0, #1
    // 0xa664bc: r16 = LoadInt32Instr(r2)
    //     0xa664bc: sbfx            x16, x2, #1, #0x1f
    // 0xa664c0: r17 = 11601
    //     0xa664c0: movz            x17, #0x2d51
    // 0xa664c4: mul             x0, x16, x17
    // 0xa664c8: umulh           x16, x16, x17
    // 0xa664cc: eor             x0, x0, x16
    // 0xa664d0: r0 = 0
    //     0xa664d0: eor             x0, x0, x0, lsr #32
    // 0xa664d4: ubfiz           x0, x0, #1, #0x1e
    // 0xa664d8: r5 = LoadInt32Instr(r0)
    //     0xa664d8: sbfx            x5, x0, #1, #0x1f
    // 0xa664dc: mov             x3, x1
    // 0xa664e0: ldur            x1, [fp, #-0x38]
    // 0xa664e4: r0 = _set()
    //     0xa664e4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa664e8: ldur            x0, [fp, #-0x30]
    // 0xa664ec: add             x6, x0, #1
    // 0xa664f0: ldur            x2, [fp, #-0x38]
    // 0xa664f4: b               #0xa6644c
    // 0xa664f8: mov             x0, x2
    // 0xa664fc: mov             x1, x0
    // 0xa66500: r2 = 0
    //     0xa66500: movz            x2, #0
    // 0xa66504: r0 = _getValueOrData()
    //     0xa66504: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66508: ldur            x3, [fp, #-0x38]
    // 0xa6650c: LoadField: r1 = r3->field_f
    //     0xa6650c: ldur            w1, [x3, #0xf]
    // 0xa66510: DecompressPointer r1
    //     0xa66510: add             x1, x1, HEAP, lsl #32
    // 0xa66514: cmp             w1, w0
    // 0xa66518: b.ne            #0xa66524
    // 0xa6651c: r4 = Null
    //     0xa6651c: mov             x4, NULL
    // 0xa66520: b               #0xa66528
    // 0xa66524: mov             x4, x0
    // 0xa66528: mov             x0, x4
    // 0xa6652c: stur            x4, [fp, #-0x18]
    // 0xa66530: r2 = Null
    //     0xa66530: mov             x2, NULL
    // 0xa66534: r1 = Null
    //     0xa66534: mov             x1, NULL
    // 0xa66538: branchIfSmi(r0, 0xa66560)
    //     0xa66538: tbz             w0, #0, #0xa66560
    // 0xa6653c: r4 = LoadClassIdInstr(r0)
    //     0xa6653c: ldur            x4, [x0, #-1]
    //     0xa66540: ubfx            x4, x4, #0xc, #0x14
    // 0xa66544: sub             x4, x4, #0x3c
    // 0xa66548: cmp             x4, #1
    // 0xa6654c: b.ls            #0xa66560
    // 0xa66550: r8 = int
    //     0xa66550: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66554: r3 = Null
    //     0xa66554: add             x3, PP, #0x20, lsl #12  ; [pp+0x20cf8] Null
    //     0xa66558: ldr             x3, [x3, #0xcf8]
    // 0xa6655c: r0 = int()
    //     0xa6655c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66560: ldur            x1, [fp, #-0x38]
    // 0xa66564: r2 = 2
    //     0xa66564: movz            x2, #0x2
    // 0xa66568: r0 = _getValueOrData()
    //     0xa66568: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6656c: ldur            x3, [fp, #-0x38]
    // 0xa66570: LoadField: r1 = r3->field_f
    //     0xa66570: ldur            w1, [x3, #0xf]
    // 0xa66574: DecompressPointer r1
    //     0xa66574: add             x1, x1, HEAP, lsl #32
    // 0xa66578: cmp             w1, w0
    // 0xa6657c: b.ne            #0xa66588
    // 0xa66580: r4 = Null
    //     0xa66580: mov             x4, NULL
    // 0xa66584: b               #0xa6658c
    // 0xa66588: mov             x4, x0
    // 0xa6658c: mov             x0, x4
    // 0xa66590: stur            x4, [fp, #-0x20]
    // 0xa66594: r2 = Null
    //     0xa66594: mov             x2, NULL
    // 0xa66598: r1 = Null
    //     0xa66598: mov             x1, NULL
    // 0xa6659c: branchIfSmi(r0, 0xa665c4)
    //     0xa6659c: tbz             w0, #0, #0xa665c4
    // 0xa665a0: r4 = LoadClassIdInstr(r0)
    //     0xa665a0: ldur            x4, [x0, #-1]
    //     0xa665a4: ubfx            x4, x4, #0xc, #0x14
    // 0xa665a8: sub             x4, x4, #0x3c
    // 0xa665ac: cmp             x4, #1
    // 0xa665b0: b.ls            #0xa665c4
    // 0xa665b4: r8 = int
    //     0xa665b4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa665b8: r3 = Null
    //     0xa665b8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d08] Null
    //     0xa665bc: ldr             x3, [x3, #0xd08]
    // 0xa665c0: r0 = int()
    //     0xa665c0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa665c4: ldur            x1, [fp, #-0x38]
    // 0xa665c8: r2 = 4
    //     0xa665c8: movz            x2, #0x4
    // 0xa665cc: r0 = _getValueOrData()
    //     0xa665cc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa665d0: ldur            x3, [fp, #-0x38]
    // 0xa665d4: LoadField: r1 = r3->field_f
    //     0xa665d4: ldur            w1, [x3, #0xf]
    // 0xa665d8: DecompressPointer r1
    //     0xa665d8: add             x1, x1, HEAP, lsl #32
    // 0xa665dc: cmp             w1, w0
    // 0xa665e0: b.ne            #0xa665ec
    // 0xa665e4: r4 = Null
    //     0xa665e4: mov             x4, NULL
    // 0xa665e8: b               #0xa665f0
    // 0xa665ec: mov             x4, x0
    // 0xa665f0: mov             x0, x4
    // 0xa665f4: stur            x4, [fp, #-0x40]
    // 0xa665f8: r2 = Null
    //     0xa665f8: mov             x2, NULL
    // 0xa665fc: r1 = Null
    //     0xa665fc: mov             x1, NULL
    // 0xa66600: branchIfSmi(r0, 0xa66628)
    //     0xa66600: tbz             w0, #0, #0xa66628
    // 0xa66604: r4 = LoadClassIdInstr(r0)
    //     0xa66604: ldur            x4, [x0, #-1]
    //     0xa66608: ubfx            x4, x4, #0xc, #0x14
    // 0xa6660c: sub             x4, x4, #0x3c
    // 0xa66610: cmp             x4, #1
    // 0xa66614: b.ls            #0xa66628
    // 0xa66618: r8 = int
    //     0xa66618: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa6661c: r3 = Null
    //     0xa6661c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d18] Null
    //     0xa66620: ldr             x3, [x3, #0xd18]
    // 0xa66624: r0 = int()
    //     0xa66624: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66628: ldur            x1, [fp, #-0x38]
    // 0xa6662c: r2 = 6
    //     0xa6662c: movz            x2, #0x6
    // 0xa66630: r0 = _getValueOrData()
    //     0xa66630: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66634: ldur            x3, [fp, #-0x38]
    // 0xa66638: LoadField: r1 = r3->field_f
    //     0xa66638: ldur            w1, [x3, #0xf]
    // 0xa6663c: DecompressPointer r1
    //     0xa6663c: add             x1, x1, HEAP, lsl #32
    // 0xa66640: cmp             w1, w0
    // 0xa66644: b.ne            #0xa66650
    // 0xa66648: r4 = Null
    //     0xa66648: mov             x4, NULL
    // 0xa6664c: b               #0xa66654
    // 0xa66650: mov             x4, x0
    // 0xa66654: mov             x0, x4
    // 0xa66658: stur            x4, [fp, #-0x48]
    // 0xa6665c: r2 = Null
    //     0xa6665c: mov             x2, NULL
    // 0xa66660: r1 = Null
    //     0xa66660: mov             x1, NULL
    // 0xa66664: branchIfSmi(r0, 0xa6668c)
    //     0xa66664: tbz             w0, #0, #0xa6668c
    // 0xa66668: r4 = LoadClassIdInstr(r0)
    //     0xa66668: ldur            x4, [x0, #-1]
    //     0xa6666c: ubfx            x4, x4, #0xc, #0x14
    // 0xa66670: sub             x4, x4, #0x3c
    // 0xa66674: cmp             x4, #1
    // 0xa66678: b.ls            #0xa6668c
    // 0xa6667c: r8 = int
    //     0xa6667c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66680: r3 = Null
    //     0xa66680: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d28] Null
    //     0xa66684: ldr             x3, [x3, #0xd28]
    // 0xa66688: r0 = int()
    //     0xa66688: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa6668c: ldur            x1, [fp, #-0x38]
    // 0xa66690: r2 = 8
    //     0xa66690: movz            x2, #0x8
    // 0xa66694: r0 = _getValueOrData()
    //     0xa66694: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66698: ldur            x3, [fp, #-0x38]
    // 0xa6669c: LoadField: r1 = r3->field_f
    //     0xa6669c: ldur            w1, [x3, #0xf]
    // 0xa666a0: DecompressPointer r1
    //     0xa666a0: add             x1, x1, HEAP, lsl #32
    // 0xa666a4: cmp             w1, w0
    // 0xa666a8: b.ne            #0xa666b4
    // 0xa666ac: r4 = Null
    //     0xa666ac: mov             x4, NULL
    // 0xa666b0: b               #0xa666b8
    // 0xa666b4: mov             x4, x0
    // 0xa666b8: mov             x0, x4
    // 0xa666bc: stur            x4, [fp, #-0x50]
    // 0xa666c0: r2 = Null
    //     0xa666c0: mov             x2, NULL
    // 0xa666c4: r1 = Null
    //     0xa666c4: mov             x1, NULL
    // 0xa666c8: branchIfSmi(r0, 0xa666f0)
    //     0xa666c8: tbz             w0, #0, #0xa666f0
    // 0xa666cc: r4 = LoadClassIdInstr(r0)
    //     0xa666cc: ldur            x4, [x0, #-1]
    //     0xa666d0: ubfx            x4, x4, #0xc, #0x14
    // 0xa666d4: sub             x4, x4, #0x3c
    // 0xa666d8: cmp             x4, #1
    // 0xa666dc: b.ls            #0xa666f0
    // 0xa666e0: r8 = int
    //     0xa666e0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa666e4: r3 = Null
    //     0xa666e4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d38] Null
    //     0xa666e8: ldr             x3, [x3, #0xd38]
    // 0xa666ec: r0 = int()
    //     0xa666ec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa666f0: ldur            x1, [fp, #-0x38]
    // 0xa666f4: r2 = 10
    //     0xa666f4: movz            x2, #0xa
    // 0xa666f8: r0 = _getValueOrData()
    //     0xa666f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa666fc: ldur            x3, [fp, #-0x38]
    // 0xa66700: LoadField: r1 = r3->field_f
    //     0xa66700: ldur            w1, [x3, #0xf]
    // 0xa66704: DecompressPointer r1
    //     0xa66704: add             x1, x1, HEAP, lsl #32
    // 0xa66708: cmp             w1, w0
    // 0xa6670c: b.ne            #0xa66718
    // 0xa66710: r4 = Null
    //     0xa66710: mov             x4, NULL
    // 0xa66714: b               #0xa6671c
    // 0xa66718: mov             x4, x0
    // 0xa6671c: mov             x0, x4
    // 0xa66720: stur            x4, [fp, #-0x58]
    // 0xa66724: r2 = Null
    //     0xa66724: mov             x2, NULL
    // 0xa66728: r1 = Null
    //     0xa66728: mov             x1, NULL
    // 0xa6672c: r4 = 60
    //     0xa6672c: movz            x4, #0x3c
    // 0xa66730: branchIfSmi(r0, 0xa6673c)
    //     0xa66730: tbz             w0, #0, #0xa6673c
    // 0xa66734: r4 = LoadClassIdInstr(r0)
    //     0xa66734: ldur            x4, [x0, #-1]
    //     0xa66738: ubfx            x4, x4, #0xc, #0x14
    // 0xa6673c: sub             x4, x4, #0x5e
    // 0xa66740: cmp             x4, #1
    // 0xa66744: b.ls            #0xa66758
    // 0xa66748: r8 = String
    //     0xa66748: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa6674c: r3 = Null
    //     0xa6674c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d48] Null
    //     0xa66750: ldr             x3, [x3, #0xd48]
    // 0xa66754: r0 = String()
    //     0xa66754: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa66758: ldur            x1, [fp, #-0x38]
    // 0xa6675c: r2 = 12
    //     0xa6675c: movz            x2, #0xc
    // 0xa66760: r0 = _getValueOrData()
    //     0xa66760: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66764: ldur            x3, [fp, #-0x38]
    // 0xa66768: LoadField: r1 = r3->field_f
    //     0xa66768: ldur            w1, [x3, #0xf]
    // 0xa6676c: DecompressPointer r1
    //     0xa6676c: add             x1, x1, HEAP, lsl #32
    // 0xa66770: cmp             w1, w0
    // 0xa66774: b.ne            #0xa66780
    // 0xa66778: r4 = Null
    //     0xa66778: mov             x4, NULL
    // 0xa6677c: b               #0xa66784
    // 0xa66780: mov             x4, x0
    // 0xa66784: mov             x0, x4
    // 0xa66788: stur            x4, [fp, #-0x60]
    // 0xa6678c: r2 = Null
    //     0xa6678c: mov             x2, NULL
    // 0xa66790: r1 = Null
    //     0xa66790: mov             x1, NULL
    // 0xa66794: r4 = 60
    //     0xa66794: movz            x4, #0x3c
    // 0xa66798: branchIfSmi(r0, 0xa667a4)
    //     0xa66798: tbz             w0, #0, #0xa667a4
    // 0xa6679c: r4 = LoadClassIdInstr(r0)
    //     0xa6679c: ldur            x4, [x0, #-1]
    //     0xa667a0: ubfx            x4, x4, #0xc, #0x14
    // 0xa667a4: sub             x4, x4, #0x5e
    // 0xa667a8: cmp             x4, #1
    // 0xa667ac: b.ls            #0xa667c0
    // 0xa667b0: r8 = String
    //     0xa667b0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa667b4: r3 = Null
    //     0xa667b4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d58] Null
    //     0xa667b8: ldr             x3, [x3, #0xd58]
    // 0xa667bc: r0 = String()
    //     0xa667bc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa667c0: ldur            x1, [fp, #-0x38]
    // 0xa667c4: r2 = 14
    //     0xa667c4: movz            x2, #0xe
    // 0xa667c8: r0 = _getValueOrData()
    //     0xa667c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa667cc: ldur            x3, [fp, #-0x38]
    // 0xa667d0: LoadField: r1 = r3->field_f
    //     0xa667d0: ldur            w1, [x3, #0xf]
    // 0xa667d4: DecompressPointer r1
    //     0xa667d4: add             x1, x1, HEAP, lsl #32
    // 0xa667d8: cmp             w1, w0
    // 0xa667dc: b.ne            #0xa667e8
    // 0xa667e0: r4 = Null
    //     0xa667e0: mov             x4, NULL
    // 0xa667e4: b               #0xa667ec
    // 0xa667e8: mov             x4, x0
    // 0xa667ec: mov             x0, x4
    // 0xa667f0: stur            x4, [fp, #-0x68]
    // 0xa667f4: r2 = Null
    //     0xa667f4: mov             x2, NULL
    // 0xa667f8: r1 = Null
    //     0xa667f8: mov             x1, NULL
    // 0xa667fc: r4 = 60
    //     0xa667fc: movz            x4, #0x3c
    // 0xa66800: branchIfSmi(r0, 0xa6680c)
    //     0xa66800: tbz             w0, #0, #0xa6680c
    // 0xa66804: r4 = LoadClassIdInstr(r0)
    //     0xa66804: ldur            x4, [x0, #-1]
    //     0xa66808: ubfx            x4, x4, #0xc, #0x14
    // 0xa6680c: sub             x4, x4, #0x5e
    // 0xa66810: cmp             x4, #1
    // 0xa66814: b.ls            #0xa66828
    // 0xa66818: r8 = String
    //     0xa66818: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa6681c: r3 = Null
    //     0xa6681c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d68] Null
    //     0xa66820: ldr             x3, [x3, #0xd68]
    // 0xa66824: r0 = String()
    //     0xa66824: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa66828: ldur            x1, [fp, #-0x38]
    // 0xa6682c: r2 = 16
    //     0xa6682c: movz            x2, #0x10
    // 0xa66830: r0 = _getValueOrData()
    //     0xa66830: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66834: mov             x1, x0
    // 0xa66838: ldur            x0, [fp, #-0x38]
    // 0xa6683c: LoadField: r2 = r0->field_f
    //     0xa6683c: ldur            w2, [x0, #0xf]
    // 0xa66840: DecompressPointer r2
    //     0xa66840: add             x2, x2, HEAP, lsl #32
    // 0xa66844: cmp             w2, w1
    // 0xa66848: b.ne            #0xa66854
    // 0xa6684c: r11 = Null
    //     0xa6684c: mov             x11, NULL
    // 0xa66850: b               #0xa66858
    // 0xa66854: mov             x11, x1
    // 0xa66858: ldur            x10, [fp, #-0x18]
    // 0xa6685c: ldur            x9, [fp, #-0x20]
    // 0xa66860: ldur            x8, [fp, #-0x40]
    // 0xa66864: ldur            x7, [fp, #-0x48]
    // 0xa66868: ldur            x6, [fp, #-0x50]
    // 0xa6686c: ldur            x5, [fp, #-0x58]
    // 0xa66870: ldur            x4, [fp, #-0x60]
    // 0xa66874: ldur            x3, [fp, #-0x68]
    // 0xa66878: mov             x0, x11
    // 0xa6687c: stur            x11, [fp, #-0x38]
    // 0xa66880: r2 = Null
    //     0xa66880: mov             x2, NULL
    // 0xa66884: r1 = Null
    //     0xa66884: mov             x1, NULL
    // 0xa66888: r4 = 60
    //     0xa66888: movz            x4, #0x3c
    // 0xa6688c: branchIfSmi(r0, 0xa66898)
    //     0xa6688c: tbz             w0, #0, #0xa66898
    // 0xa66890: r4 = LoadClassIdInstr(r0)
    //     0xa66890: ldur            x4, [x0, #-1]
    //     0xa66894: ubfx            x4, x4, #0xc, #0x14
    // 0xa66898: sub             x4, x4, #0x5e
    // 0xa6689c: cmp             x4, #1
    // 0xa668a0: b.ls            #0xa668b4
    // 0xa668a4: r8 = String
    //     0xa668a4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa668a8: r3 = Null
    //     0xa668a8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d78] Null
    //     0xa668ac: ldr             x3, [x3, #0xd78]
    // 0xa668b0: r0 = String()
    //     0xa668b0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa668b4: ldur            x0, [fp, #-0x18]
    // 0xa668b8: r1 = LoadInt32Instr(r0)
    //     0xa668b8: sbfx            x1, x0, #1, #0x1f
    //     0xa668bc: tbz             w0, #0, #0xa668c4
    //     0xa668c0: ldur            x1, [x0, #7]
    // 0xa668c4: stur            x1, [fp, #-8]
    // 0xa668c8: r0 = Tafsir()
    //     0xa668c8: bl              #0x8674cc  ; AllocateTafsirStub -> Tafsir (size=0x4c)
    // 0xa668cc: mov             x1, x0
    // 0xa668d0: ldur            x0, [fp, #-8]
    // 0xa668d4: stur            x1, [fp, #-0x18]
    // 0xa668d8: StoreField: r1->field_13 = r0
    //     0xa668d8: stur            x0, [x1, #0x13]
    // 0xa668dc: ldur            x0, [fp, #-0x20]
    // 0xa668e0: r2 = LoadInt32Instr(r0)
    //     0xa668e0: sbfx            x2, x0, #1, #0x1f
    //     0xa668e4: tbz             w0, #0, #0xa668ec
    //     0xa668e8: ldur            x2, [x0, #7]
    // 0xa668ec: StoreField: r1->field_1b = r2
    //     0xa668ec: stur            x2, [x1, #0x1b]
    // 0xa668f0: ldur            x0, [fp, #-0x40]
    // 0xa668f4: r2 = LoadInt32Instr(r0)
    //     0xa668f4: sbfx            x2, x0, #1, #0x1f
    //     0xa668f8: tbz             w0, #0, #0xa66900
    //     0xa668fc: ldur            x2, [x0, #7]
    // 0xa66900: StoreField: r1->field_23 = r2
    //     0xa66900: stur            x2, [x1, #0x23]
    // 0xa66904: ldur            x0, [fp, #-0x48]
    // 0xa66908: r2 = LoadInt32Instr(r0)
    //     0xa66908: sbfx            x2, x0, #1, #0x1f
    //     0xa6690c: tbz             w0, #0, #0xa66914
    //     0xa66910: ldur            x2, [x0, #7]
    // 0xa66914: StoreField: r1->field_2b = r2
    //     0xa66914: stur            x2, [x1, #0x2b]
    // 0xa66918: ldur            x0, [fp, #-0x50]
    // 0xa6691c: r2 = LoadInt32Instr(r0)
    //     0xa6691c: sbfx            x2, x0, #1, #0x1f
    //     0xa66920: tbz             w0, #0, #0xa66928
    //     0xa66924: ldur            x2, [x0, #7]
    // 0xa66928: StoreField: r1->field_33 = r2
    //     0xa66928: stur            x2, [x1, #0x33]
    // 0xa6692c: ldur            x0, [fp, #-0x58]
    // 0xa66930: StoreField: r1->field_3b = r0
    //     0xa66930: stur            w0, [x1, #0x3b]
    // 0xa66934: ldur            x0, [fp, #-0x60]
    // 0xa66938: StoreField: r1->field_3f = r0
    //     0xa66938: stur            w0, [x1, #0x3f]
    // 0xa6693c: ldur            x0, [fp, #-0x68]
    // 0xa66940: StoreField: r1->field_43 = r0
    //     0xa66940: stur            w0, [x1, #0x43]
    // 0xa66944: ldur            x0, [fp, #-0x38]
    // 0xa66948: StoreField: r1->field_47 = r0
    //     0xa66948: stur            w0, [x1, #0x47]
    // 0xa6694c: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xa6694c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xa66950: ldr             x16, [x16, #0x9f8]
    // 0xa66954: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa66958: stp             lr, x16, [SP]
    // 0xa6695c: r0 = Map._fromLiteral()
    //     0xa6695c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa66960: ldur            x1, [fp, #-0x18]
    // 0xa66964: StoreField: r1->field_f = r0
    //     0xa66964: stur            w0, [x1, #0xf]
    //     0xa66968: ldurb           w16, [x1, #-1]
    //     0xa6696c: ldurb           w17, [x0, #-1]
    //     0xa66970: and             x16, x17, x16, lsr #2
    //     0xa66974: tst             x16, HEAP, lsr #32
    //     0xa66978: b.eq            #0xa66980
    //     0xa6697c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa66980: mov             x0, x1
    // 0xa66984: LeaveFrame
    //     0xa66984: mov             SP, fp
    //     0xa66988: ldp             fp, lr, [SP], #0x10
    // 0xa6698c: ret
    //     0xa6698c: ret             
    // 0xa66990: r0 = RangeError()
    //     0xa66990: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa66994: mov             x1, x0
    // 0xa66998: r0 = "Not enough bytes available."
    //     0xa66998: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa6699c: ldr             x0, [x0, #0x8a8]
    // 0xa669a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa669a0: stur            w0, [x1, #0x17]
    // 0xa669a4: r2 = false
    //     0xa669a4: add             x2, NULL, #0x30  ; false
    // 0xa669a8: StoreField: r1->field_b = r2
    //     0xa669a8: stur            w2, [x1, #0xb]
    // 0xa669ac: mov             x0, x1
    // 0xa669b0: r0 = Throw()
    //     0xa669b0: bl              #0xec04b8  ; ThrowStub
    // 0xa669b4: brk             #0
    // 0xa669b8: r0 = "Not enough bytes available."
    //     0xa669b8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa669bc: ldr             x0, [x0, #0x8a8]
    // 0xa669c0: r2 = false
    //     0xa669c0: add             x2, NULL, #0x30  ; false
    // 0xa669c4: r0 = RangeError()
    //     0xa669c4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa669c8: mov             x1, x0
    // 0xa669cc: r0 = "Not enough bytes available."
    //     0xa669cc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa669d0: ldr             x0, [x0, #0x8a8]
    // 0xa669d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa669d4: stur            w0, [x1, #0x17]
    // 0xa669d8: r0 = false
    //     0xa669d8: add             x0, NULL, #0x30  ; false
    // 0xa669dc: StoreField: r1->field_b = r0
    //     0xa669dc: stur            w0, [x1, #0xb]
    // 0xa669e0: mov             x0, x1
    // 0xa669e4: r0 = Throw()
    //     0xa669e4: bl              #0xec04b8  ; ThrowStub
    // 0xa669e8: brk             #0
    // 0xa669ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa669ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa669f0: b               #0xa663e0
    // 0xa669f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa669f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa669f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa669f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa669fc: b               #0xa66468
    // 0xa66a00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa66a00: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd7820, size: 0x628
    // 0xbd7820: EnterFrame
    //     0xbd7820: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7824: mov             fp, SP
    // 0xbd7828: AllocStack(0x28)
    //     0xbd7828: sub             SP, SP, #0x28
    // 0xbd782c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd782c: mov             x4, x2
    //     0xbd7830: stur            x2, [fp, #-8]
    //     0xbd7834: stur            x3, [fp, #-0x10]
    // 0xbd7838: CheckStackOverflow
    //     0xbd7838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd783c: cmp             SP, x16
    //     0xbd7840: b.ls            #0xbd7e18
    // 0xbd7844: mov             x0, x3
    // 0xbd7848: r2 = Null
    //     0xbd7848: mov             x2, NULL
    // 0xbd784c: r1 = Null
    //     0xbd784c: mov             x1, NULL
    // 0xbd7850: r4 = 60
    //     0xbd7850: movz            x4, #0x3c
    // 0xbd7854: branchIfSmi(r0, 0xbd7860)
    //     0xbd7854: tbz             w0, #0, #0xbd7860
    // 0xbd7858: r4 = LoadClassIdInstr(r0)
    //     0xbd7858: ldur            x4, [x0, #-1]
    //     0xbd785c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd7860: cmp             x4, #0x63f
    // 0xbd7864: b.eq            #0xbd787c
    // 0xbd7868: r8 = Tafsir
    //     0xbd7868: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b120] Type: Tafsir
    //     0xbd786c: ldr             x8, [x8, #0x120]
    // 0xbd7870: r3 = Null
    //     0xbd7870: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b128] Null
    //     0xbd7874: ldr             x3, [x3, #0x128]
    // 0xbd7878: r0 = Tafsir()
    //     0xbd7878: bl              #0x83ef88  ; IsType_Tafsir_Stub
    // 0xbd787c: ldur            x0, [fp, #-8]
    // 0xbd7880: LoadField: r1 = r0->field_b
    //     0xbd7880: ldur            w1, [x0, #0xb]
    // 0xbd7884: DecompressPointer r1
    //     0xbd7884: add             x1, x1, HEAP, lsl #32
    // 0xbd7888: LoadField: r2 = r1->field_13
    //     0xbd7888: ldur            w2, [x1, #0x13]
    // 0xbd788c: LoadField: r1 = r0->field_13
    //     0xbd788c: ldur            x1, [x0, #0x13]
    // 0xbd7890: r3 = LoadInt32Instr(r2)
    //     0xbd7890: sbfx            x3, x2, #1, #0x1f
    // 0xbd7894: sub             x2, x3, x1
    // 0xbd7898: cmp             x2, #1
    // 0xbd789c: b.ge            #0xbd78ac
    // 0xbd78a0: mov             x1, x0
    // 0xbd78a4: r2 = 1
    //     0xbd78a4: movz            x2, #0x1
    // 0xbd78a8: r0 = _increaseBufferSize()
    //     0xbd78a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd78ac: ldur            x3, [fp, #-8]
    // 0xbd78b0: r2 = 9
    //     0xbd78b0: movz            x2, #0x9
    // 0xbd78b4: LoadField: r4 = r3->field_b
    //     0xbd78b4: ldur            w4, [x3, #0xb]
    // 0xbd78b8: DecompressPointer r4
    //     0xbd78b8: add             x4, x4, HEAP, lsl #32
    // 0xbd78bc: LoadField: r5 = r3->field_13
    //     0xbd78bc: ldur            x5, [x3, #0x13]
    // 0xbd78c0: add             x6, x5, #1
    // 0xbd78c4: StoreField: r3->field_13 = r6
    //     0xbd78c4: stur            x6, [x3, #0x13]
    // 0xbd78c8: LoadField: r0 = r4->field_13
    //     0xbd78c8: ldur            w0, [x4, #0x13]
    // 0xbd78cc: r7 = LoadInt32Instr(r0)
    //     0xbd78cc: sbfx            x7, x0, #1, #0x1f
    // 0xbd78d0: mov             x0, x7
    // 0xbd78d4: mov             x1, x5
    // 0xbd78d8: cmp             x1, x0
    // 0xbd78dc: b.hs            #0xbd7e20
    // 0xbd78e0: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd78e0: add             x0, x4, x5
    //     0xbd78e4: strb            w2, [x0, #0x17]
    // 0xbd78e8: sub             x0, x7, x6
    // 0xbd78ec: cmp             x0, #1
    // 0xbd78f0: b.ge            #0xbd7900
    // 0xbd78f4: mov             x1, x3
    // 0xbd78f8: r2 = 1
    //     0xbd78f8: movz            x2, #0x1
    // 0xbd78fc: r0 = _increaseBufferSize()
    //     0xbd78fc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7900: ldur            x2, [fp, #-8]
    // 0xbd7904: ldur            x3, [fp, #-0x10]
    // 0xbd7908: LoadField: r4 = r2->field_b
    //     0xbd7908: ldur            w4, [x2, #0xb]
    // 0xbd790c: DecompressPointer r4
    //     0xbd790c: add             x4, x4, HEAP, lsl #32
    // 0xbd7910: LoadField: r5 = r2->field_13
    //     0xbd7910: ldur            x5, [x2, #0x13]
    // 0xbd7914: add             x0, x5, #1
    // 0xbd7918: StoreField: r2->field_13 = r0
    //     0xbd7918: stur            x0, [x2, #0x13]
    // 0xbd791c: LoadField: r0 = r4->field_13
    //     0xbd791c: ldur            w0, [x4, #0x13]
    // 0xbd7920: r1 = LoadInt32Instr(r0)
    //     0xbd7920: sbfx            x1, x0, #1, #0x1f
    // 0xbd7924: mov             x0, x1
    // 0xbd7928: mov             x1, x5
    // 0xbd792c: cmp             x1, x0
    // 0xbd7930: b.hs            #0xbd7e24
    // 0xbd7934: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd7934: add             x0, x4, x5
    //     0xbd7938: strb            wzr, [x0, #0x17]
    // 0xbd793c: LoadField: r4 = r3->field_13
    //     0xbd793c: ldur            x4, [x3, #0x13]
    // 0xbd7940: r0 = BoxInt64Instr(r4)
    //     0xbd7940: sbfiz           x0, x4, #1, #0x1f
    //     0xbd7944: cmp             x4, x0, asr #1
    //     0xbd7948: b.eq            #0xbd7954
    //     0xbd794c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7950: stur            x4, [x0, #7]
    // 0xbd7954: r16 = <int>
    //     0xbd7954: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd7958: stp             x2, x16, [SP, #8]
    // 0xbd795c: str             x0, [SP]
    // 0xbd7960: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7960: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7964: r0 = write()
    //     0xbd7964: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7968: ldur            x0, [fp, #-8]
    // 0xbd796c: LoadField: r1 = r0->field_b
    //     0xbd796c: ldur            w1, [x0, #0xb]
    // 0xbd7970: DecompressPointer r1
    //     0xbd7970: add             x1, x1, HEAP, lsl #32
    // 0xbd7974: LoadField: r2 = r1->field_13
    //     0xbd7974: ldur            w2, [x1, #0x13]
    // 0xbd7978: LoadField: r1 = r0->field_13
    //     0xbd7978: ldur            x1, [x0, #0x13]
    // 0xbd797c: r3 = LoadInt32Instr(r2)
    //     0xbd797c: sbfx            x3, x2, #1, #0x1f
    // 0xbd7980: sub             x2, x3, x1
    // 0xbd7984: cmp             x2, #1
    // 0xbd7988: b.ge            #0xbd7998
    // 0xbd798c: mov             x1, x0
    // 0xbd7990: r2 = 1
    //     0xbd7990: movz            x2, #0x1
    // 0xbd7994: r0 = _increaseBufferSize()
    //     0xbd7994: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7998: ldur            x2, [fp, #-8]
    // 0xbd799c: ldur            x3, [fp, #-0x10]
    // 0xbd79a0: r4 = 1
    //     0xbd79a0: movz            x4, #0x1
    // 0xbd79a4: LoadField: r5 = r2->field_b
    //     0xbd79a4: ldur            w5, [x2, #0xb]
    // 0xbd79a8: DecompressPointer r5
    //     0xbd79a8: add             x5, x5, HEAP, lsl #32
    // 0xbd79ac: LoadField: r6 = r2->field_13
    //     0xbd79ac: ldur            x6, [x2, #0x13]
    // 0xbd79b0: add             x0, x6, #1
    // 0xbd79b4: StoreField: r2->field_13 = r0
    //     0xbd79b4: stur            x0, [x2, #0x13]
    // 0xbd79b8: LoadField: r0 = r5->field_13
    //     0xbd79b8: ldur            w0, [x5, #0x13]
    // 0xbd79bc: r1 = LoadInt32Instr(r0)
    //     0xbd79bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd79c0: mov             x0, x1
    // 0xbd79c4: mov             x1, x6
    // 0xbd79c8: cmp             x1, x0
    // 0xbd79cc: b.hs            #0xbd7e28
    // 0xbd79d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd79d0: add             x0, x5, x6
    //     0xbd79d4: strb            w4, [x0, #0x17]
    // 0xbd79d8: LoadField: r5 = r3->field_1b
    //     0xbd79d8: ldur            x5, [x3, #0x1b]
    // 0xbd79dc: r0 = BoxInt64Instr(r5)
    //     0xbd79dc: sbfiz           x0, x5, #1, #0x1f
    //     0xbd79e0: cmp             x5, x0, asr #1
    //     0xbd79e4: b.eq            #0xbd79f0
    //     0xbd79e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd79ec: stur            x5, [x0, #7]
    // 0xbd79f0: r16 = <int>
    //     0xbd79f0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd79f4: stp             x2, x16, [SP, #8]
    // 0xbd79f8: str             x0, [SP]
    // 0xbd79fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd79fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7a00: r0 = write()
    //     0xbd7a00: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7a04: ldur            x0, [fp, #-8]
    // 0xbd7a08: LoadField: r1 = r0->field_b
    //     0xbd7a08: ldur            w1, [x0, #0xb]
    // 0xbd7a0c: DecompressPointer r1
    //     0xbd7a0c: add             x1, x1, HEAP, lsl #32
    // 0xbd7a10: LoadField: r2 = r1->field_13
    //     0xbd7a10: ldur            w2, [x1, #0x13]
    // 0xbd7a14: LoadField: r1 = r0->field_13
    //     0xbd7a14: ldur            x1, [x0, #0x13]
    // 0xbd7a18: r3 = LoadInt32Instr(r2)
    //     0xbd7a18: sbfx            x3, x2, #1, #0x1f
    // 0xbd7a1c: sub             x2, x3, x1
    // 0xbd7a20: cmp             x2, #1
    // 0xbd7a24: b.ge            #0xbd7a34
    // 0xbd7a28: mov             x1, x0
    // 0xbd7a2c: r2 = 1
    //     0xbd7a2c: movz            x2, #0x1
    // 0xbd7a30: r0 = _increaseBufferSize()
    //     0xbd7a30: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7a34: ldur            x2, [fp, #-8]
    // 0xbd7a38: ldur            x3, [fp, #-0x10]
    // 0xbd7a3c: r4 = 2
    //     0xbd7a3c: movz            x4, #0x2
    // 0xbd7a40: LoadField: r5 = r2->field_b
    //     0xbd7a40: ldur            w5, [x2, #0xb]
    // 0xbd7a44: DecompressPointer r5
    //     0xbd7a44: add             x5, x5, HEAP, lsl #32
    // 0xbd7a48: LoadField: r6 = r2->field_13
    //     0xbd7a48: ldur            x6, [x2, #0x13]
    // 0xbd7a4c: add             x0, x6, #1
    // 0xbd7a50: StoreField: r2->field_13 = r0
    //     0xbd7a50: stur            x0, [x2, #0x13]
    // 0xbd7a54: LoadField: r0 = r5->field_13
    //     0xbd7a54: ldur            w0, [x5, #0x13]
    // 0xbd7a58: r1 = LoadInt32Instr(r0)
    //     0xbd7a58: sbfx            x1, x0, #1, #0x1f
    // 0xbd7a5c: mov             x0, x1
    // 0xbd7a60: mov             x1, x6
    // 0xbd7a64: cmp             x1, x0
    // 0xbd7a68: b.hs            #0xbd7e2c
    // 0xbd7a6c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7a6c: add             x0, x5, x6
    //     0xbd7a70: strb            w4, [x0, #0x17]
    // 0xbd7a74: LoadField: r4 = r3->field_23
    //     0xbd7a74: ldur            x4, [x3, #0x23]
    // 0xbd7a78: r0 = BoxInt64Instr(r4)
    //     0xbd7a78: sbfiz           x0, x4, #1, #0x1f
    //     0xbd7a7c: cmp             x4, x0, asr #1
    //     0xbd7a80: b.eq            #0xbd7a8c
    //     0xbd7a84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7a88: stur            x4, [x0, #7]
    // 0xbd7a8c: r16 = <int>
    //     0xbd7a8c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd7a90: stp             x2, x16, [SP, #8]
    // 0xbd7a94: str             x0, [SP]
    // 0xbd7a98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7a98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7a9c: r0 = write()
    //     0xbd7a9c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7aa0: ldur            x0, [fp, #-8]
    // 0xbd7aa4: LoadField: r1 = r0->field_b
    //     0xbd7aa4: ldur            w1, [x0, #0xb]
    // 0xbd7aa8: DecompressPointer r1
    //     0xbd7aa8: add             x1, x1, HEAP, lsl #32
    // 0xbd7aac: LoadField: r2 = r1->field_13
    //     0xbd7aac: ldur            w2, [x1, #0x13]
    // 0xbd7ab0: LoadField: r1 = r0->field_13
    //     0xbd7ab0: ldur            x1, [x0, #0x13]
    // 0xbd7ab4: r3 = LoadInt32Instr(r2)
    //     0xbd7ab4: sbfx            x3, x2, #1, #0x1f
    // 0xbd7ab8: sub             x2, x3, x1
    // 0xbd7abc: cmp             x2, #1
    // 0xbd7ac0: b.ge            #0xbd7ad0
    // 0xbd7ac4: mov             x1, x0
    // 0xbd7ac8: r2 = 1
    //     0xbd7ac8: movz            x2, #0x1
    // 0xbd7acc: r0 = _increaseBufferSize()
    //     0xbd7acc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7ad0: ldur            x2, [fp, #-8]
    // 0xbd7ad4: ldur            x3, [fp, #-0x10]
    // 0xbd7ad8: r4 = 3
    //     0xbd7ad8: movz            x4, #0x3
    // 0xbd7adc: LoadField: r5 = r2->field_b
    //     0xbd7adc: ldur            w5, [x2, #0xb]
    // 0xbd7ae0: DecompressPointer r5
    //     0xbd7ae0: add             x5, x5, HEAP, lsl #32
    // 0xbd7ae4: LoadField: r6 = r2->field_13
    //     0xbd7ae4: ldur            x6, [x2, #0x13]
    // 0xbd7ae8: add             x0, x6, #1
    // 0xbd7aec: StoreField: r2->field_13 = r0
    //     0xbd7aec: stur            x0, [x2, #0x13]
    // 0xbd7af0: LoadField: r0 = r5->field_13
    //     0xbd7af0: ldur            w0, [x5, #0x13]
    // 0xbd7af4: r1 = LoadInt32Instr(r0)
    //     0xbd7af4: sbfx            x1, x0, #1, #0x1f
    // 0xbd7af8: mov             x0, x1
    // 0xbd7afc: mov             x1, x6
    // 0xbd7b00: cmp             x1, x0
    // 0xbd7b04: b.hs            #0xbd7e30
    // 0xbd7b08: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7b08: add             x0, x5, x6
    //     0xbd7b0c: strb            w4, [x0, #0x17]
    // 0xbd7b10: LoadField: r4 = r3->field_2b
    //     0xbd7b10: ldur            x4, [x3, #0x2b]
    // 0xbd7b14: r0 = BoxInt64Instr(r4)
    //     0xbd7b14: sbfiz           x0, x4, #1, #0x1f
    //     0xbd7b18: cmp             x4, x0, asr #1
    //     0xbd7b1c: b.eq            #0xbd7b28
    //     0xbd7b20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7b24: stur            x4, [x0, #7]
    // 0xbd7b28: r16 = <int>
    //     0xbd7b28: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd7b2c: stp             x2, x16, [SP, #8]
    // 0xbd7b30: str             x0, [SP]
    // 0xbd7b34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7b34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7b38: r0 = write()
    //     0xbd7b38: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7b3c: ldur            x0, [fp, #-8]
    // 0xbd7b40: LoadField: r1 = r0->field_b
    //     0xbd7b40: ldur            w1, [x0, #0xb]
    // 0xbd7b44: DecompressPointer r1
    //     0xbd7b44: add             x1, x1, HEAP, lsl #32
    // 0xbd7b48: LoadField: r2 = r1->field_13
    //     0xbd7b48: ldur            w2, [x1, #0x13]
    // 0xbd7b4c: LoadField: r1 = r0->field_13
    //     0xbd7b4c: ldur            x1, [x0, #0x13]
    // 0xbd7b50: r3 = LoadInt32Instr(r2)
    //     0xbd7b50: sbfx            x3, x2, #1, #0x1f
    // 0xbd7b54: sub             x2, x3, x1
    // 0xbd7b58: cmp             x2, #1
    // 0xbd7b5c: b.ge            #0xbd7b6c
    // 0xbd7b60: mov             x1, x0
    // 0xbd7b64: r2 = 1
    //     0xbd7b64: movz            x2, #0x1
    // 0xbd7b68: r0 = _increaseBufferSize()
    //     0xbd7b68: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7b6c: ldur            x2, [fp, #-8]
    // 0xbd7b70: ldur            x3, [fp, #-0x10]
    // 0xbd7b74: r4 = 4
    //     0xbd7b74: movz            x4, #0x4
    // 0xbd7b78: LoadField: r5 = r2->field_b
    //     0xbd7b78: ldur            w5, [x2, #0xb]
    // 0xbd7b7c: DecompressPointer r5
    //     0xbd7b7c: add             x5, x5, HEAP, lsl #32
    // 0xbd7b80: LoadField: r6 = r2->field_13
    //     0xbd7b80: ldur            x6, [x2, #0x13]
    // 0xbd7b84: add             x0, x6, #1
    // 0xbd7b88: StoreField: r2->field_13 = r0
    //     0xbd7b88: stur            x0, [x2, #0x13]
    // 0xbd7b8c: LoadField: r0 = r5->field_13
    //     0xbd7b8c: ldur            w0, [x5, #0x13]
    // 0xbd7b90: r1 = LoadInt32Instr(r0)
    //     0xbd7b90: sbfx            x1, x0, #1, #0x1f
    // 0xbd7b94: mov             x0, x1
    // 0xbd7b98: mov             x1, x6
    // 0xbd7b9c: cmp             x1, x0
    // 0xbd7ba0: b.hs            #0xbd7e34
    // 0xbd7ba4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7ba4: add             x0, x5, x6
    //     0xbd7ba8: strb            w4, [x0, #0x17]
    // 0xbd7bac: LoadField: r4 = r3->field_33
    //     0xbd7bac: ldur            x4, [x3, #0x33]
    // 0xbd7bb0: r0 = BoxInt64Instr(r4)
    //     0xbd7bb0: sbfiz           x0, x4, #1, #0x1f
    //     0xbd7bb4: cmp             x4, x0, asr #1
    //     0xbd7bb8: b.eq            #0xbd7bc4
    //     0xbd7bbc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7bc0: stur            x4, [x0, #7]
    // 0xbd7bc4: r16 = <int>
    //     0xbd7bc4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd7bc8: stp             x2, x16, [SP, #8]
    // 0xbd7bcc: str             x0, [SP]
    // 0xbd7bd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7bd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7bd4: r0 = write()
    //     0xbd7bd4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7bd8: ldur            x0, [fp, #-8]
    // 0xbd7bdc: LoadField: r1 = r0->field_b
    //     0xbd7bdc: ldur            w1, [x0, #0xb]
    // 0xbd7be0: DecompressPointer r1
    //     0xbd7be0: add             x1, x1, HEAP, lsl #32
    // 0xbd7be4: LoadField: r2 = r1->field_13
    //     0xbd7be4: ldur            w2, [x1, #0x13]
    // 0xbd7be8: LoadField: r1 = r0->field_13
    //     0xbd7be8: ldur            x1, [x0, #0x13]
    // 0xbd7bec: r3 = LoadInt32Instr(r2)
    //     0xbd7bec: sbfx            x3, x2, #1, #0x1f
    // 0xbd7bf0: sub             x2, x3, x1
    // 0xbd7bf4: cmp             x2, #1
    // 0xbd7bf8: b.ge            #0xbd7c08
    // 0xbd7bfc: mov             x1, x0
    // 0xbd7c00: r2 = 1
    //     0xbd7c00: movz            x2, #0x1
    // 0xbd7c04: r0 = _increaseBufferSize()
    //     0xbd7c04: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7c08: ldur            x2, [fp, #-8]
    // 0xbd7c0c: ldur            x3, [fp, #-0x10]
    // 0xbd7c10: r4 = 5
    //     0xbd7c10: movz            x4, #0x5
    // 0xbd7c14: LoadField: r5 = r2->field_b
    //     0xbd7c14: ldur            w5, [x2, #0xb]
    // 0xbd7c18: DecompressPointer r5
    //     0xbd7c18: add             x5, x5, HEAP, lsl #32
    // 0xbd7c1c: LoadField: r6 = r2->field_13
    //     0xbd7c1c: ldur            x6, [x2, #0x13]
    // 0xbd7c20: add             x0, x6, #1
    // 0xbd7c24: StoreField: r2->field_13 = r0
    //     0xbd7c24: stur            x0, [x2, #0x13]
    // 0xbd7c28: LoadField: r0 = r5->field_13
    //     0xbd7c28: ldur            w0, [x5, #0x13]
    // 0xbd7c2c: r1 = LoadInt32Instr(r0)
    //     0xbd7c2c: sbfx            x1, x0, #1, #0x1f
    // 0xbd7c30: mov             x0, x1
    // 0xbd7c34: mov             x1, x6
    // 0xbd7c38: cmp             x1, x0
    // 0xbd7c3c: b.hs            #0xbd7e38
    // 0xbd7c40: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7c40: add             x0, x5, x6
    //     0xbd7c44: strb            w4, [x0, #0x17]
    // 0xbd7c48: LoadField: r0 = r3->field_3b
    //     0xbd7c48: ldur            w0, [x3, #0x3b]
    // 0xbd7c4c: DecompressPointer r0
    //     0xbd7c4c: add             x0, x0, HEAP, lsl #32
    // 0xbd7c50: r16 = <String>
    //     0xbd7c50: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7c54: stp             x2, x16, [SP, #8]
    // 0xbd7c58: str             x0, [SP]
    // 0xbd7c5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7c5c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7c60: r0 = write()
    //     0xbd7c60: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7c64: ldur            x0, [fp, #-8]
    // 0xbd7c68: LoadField: r1 = r0->field_b
    //     0xbd7c68: ldur            w1, [x0, #0xb]
    // 0xbd7c6c: DecompressPointer r1
    //     0xbd7c6c: add             x1, x1, HEAP, lsl #32
    // 0xbd7c70: LoadField: r2 = r1->field_13
    //     0xbd7c70: ldur            w2, [x1, #0x13]
    // 0xbd7c74: LoadField: r1 = r0->field_13
    //     0xbd7c74: ldur            x1, [x0, #0x13]
    // 0xbd7c78: r3 = LoadInt32Instr(r2)
    //     0xbd7c78: sbfx            x3, x2, #1, #0x1f
    // 0xbd7c7c: sub             x2, x3, x1
    // 0xbd7c80: cmp             x2, #1
    // 0xbd7c84: b.ge            #0xbd7c94
    // 0xbd7c88: mov             x1, x0
    // 0xbd7c8c: r2 = 1
    //     0xbd7c8c: movz            x2, #0x1
    // 0xbd7c90: r0 = _increaseBufferSize()
    //     0xbd7c90: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7c94: ldur            x2, [fp, #-8]
    // 0xbd7c98: ldur            x3, [fp, #-0x10]
    // 0xbd7c9c: r4 = 6
    //     0xbd7c9c: movz            x4, #0x6
    // 0xbd7ca0: LoadField: r5 = r2->field_b
    //     0xbd7ca0: ldur            w5, [x2, #0xb]
    // 0xbd7ca4: DecompressPointer r5
    //     0xbd7ca4: add             x5, x5, HEAP, lsl #32
    // 0xbd7ca8: LoadField: r6 = r2->field_13
    //     0xbd7ca8: ldur            x6, [x2, #0x13]
    // 0xbd7cac: add             x0, x6, #1
    // 0xbd7cb0: StoreField: r2->field_13 = r0
    //     0xbd7cb0: stur            x0, [x2, #0x13]
    // 0xbd7cb4: LoadField: r0 = r5->field_13
    //     0xbd7cb4: ldur            w0, [x5, #0x13]
    // 0xbd7cb8: r1 = LoadInt32Instr(r0)
    //     0xbd7cb8: sbfx            x1, x0, #1, #0x1f
    // 0xbd7cbc: mov             x0, x1
    // 0xbd7cc0: mov             x1, x6
    // 0xbd7cc4: cmp             x1, x0
    // 0xbd7cc8: b.hs            #0xbd7e3c
    // 0xbd7ccc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7ccc: add             x0, x5, x6
    //     0xbd7cd0: strb            w4, [x0, #0x17]
    // 0xbd7cd4: LoadField: r0 = r3->field_3f
    //     0xbd7cd4: ldur            w0, [x3, #0x3f]
    // 0xbd7cd8: DecompressPointer r0
    //     0xbd7cd8: add             x0, x0, HEAP, lsl #32
    // 0xbd7cdc: r16 = <String>
    //     0xbd7cdc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7ce0: stp             x2, x16, [SP, #8]
    // 0xbd7ce4: str             x0, [SP]
    // 0xbd7ce8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7ce8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7cec: r0 = write()
    //     0xbd7cec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7cf0: ldur            x0, [fp, #-8]
    // 0xbd7cf4: LoadField: r1 = r0->field_b
    //     0xbd7cf4: ldur            w1, [x0, #0xb]
    // 0xbd7cf8: DecompressPointer r1
    //     0xbd7cf8: add             x1, x1, HEAP, lsl #32
    // 0xbd7cfc: LoadField: r2 = r1->field_13
    //     0xbd7cfc: ldur            w2, [x1, #0x13]
    // 0xbd7d00: LoadField: r1 = r0->field_13
    //     0xbd7d00: ldur            x1, [x0, #0x13]
    // 0xbd7d04: r3 = LoadInt32Instr(r2)
    //     0xbd7d04: sbfx            x3, x2, #1, #0x1f
    // 0xbd7d08: sub             x2, x3, x1
    // 0xbd7d0c: cmp             x2, #1
    // 0xbd7d10: b.ge            #0xbd7d20
    // 0xbd7d14: mov             x1, x0
    // 0xbd7d18: r2 = 1
    //     0xbd7d18: movz            x2, #0x1
    // 0xbd7d1c: r0 = _increaseBufferSize()
    //     0xbd7d1c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7d20: ldur            x2, [fp, #-8]
    // 0xbd7d24: ldur            x3, [fp, #-0x10]
    // 0xbd7d28: r4 = 7
    //     0xbd7d28: movz            x4, #0x7
    // 0xbd7d2c: LoadField: r5 = r2->field_b
    //     0xbd7d2c: ldur            w5, [x2, #0xb]
    // 0xbd7d30: DecompressPointer r5
    //     0xbd7d30: add             x5, x5, HEAP, lsl #32
    // 0xbd7d34: LoadField: r6 = r2->field_13
    //     0xbd7d34: ldur            x6, [x2, #0x13]
    // 0xbd7d38: add             x0, x6, #1
    // 0xbd7d3c: StoreField: r2->field_13 = r0
    //     0xbd7d3c: stur            x0, [x2, #0x13]
    // 0xbd7d40: LoadField: r0 = r5->field_13
    //     0xbd7d40: ldur            w0, [x5, #0x13]
    // 0xbd7d44: r1 = LoadInt32Instr(r0)
    //     0xbd7d44: sbfx            x1, x0, #1, #0x1f
    // 0xbd7d48: mov             x0, x1
    // 0xbd7d4c: mov             x1, x6
    // 0xbd7d50: cmp             x1, x0
    // 0xbd7d54: b.hs            #0xbd7e40
    // 0xbd7d58: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7d58: add             x0, x5, x6
    //     0xbd7d5c: strb            w4, [x0, #0x17]
    // 0xbd7d60: LoadField: r0 = r3->field_43
    //     0xbd7d60: ldur            w0, [x3, #0x43]
    // 0xbd7d64: DecompressPointer r0
    //     0xbd7d64: add             x0, x0, HEAP, lsl #32
    // 0xbd7d68: r16 = <String>
    //     0xbd7d68: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7d6c: stp             x2, x16, [SP, #8]
    // 0xbd7d70: str             x0, [SP]
    // 0xbd7d74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7d74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7d78: r0 = write()
    //     0xbd7d78: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7d7c: ldur            x0, [fp, #-8]
    // 0xbd7d80: LoadField: r1 = r0->field_b
    //     0xbd7d80: ldur            w1, [x0, #0xb]
    // 0xbd7d84: DecompressPointer r1
    //     0xbd7d84: add             x1, x1, HEAP, lsl #32
    // 0xbd7d88: LoadField: r2 = r1->field_13
    //     0xbd7d88: ldur            w2, [x1, #0x13]
    // 0xbd7d8c: LoadField: r1 = r0->field_13
    //     0xbd7d8c: ldur            x1, [x0, #0x13]
    // 0xbd7d90: r3 = LoadInt32Instr(r2)
    //     0xbd7d90: sbfx            x3, x2, #1, #0x1f
    // 0xbd7d94: sub             x2, x3, x1
    // 0xbd7d98: cmp             x2, #1
    // 0xbd7d9c: b.ge            #0xbd7dac
    // 0xbd7da0: mov             x1, x0
    // 0xbd7da4: r2 = 1
    //     0xbd7da4: movz            x2, #0x1
    // 0xbd7da8: r0 = _increaseBufferSize()
    //     0xbd7da8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7dac: ldur            x2, [fp, #-8]
    // 0xbd7db0: ldur            x3, [fp, #-0x10]
    // 0xbd7db4: r4 = 8
    //     0xbd7db4: movz            x4, #0x8
    // 0xbd7db8: LoadField: r5 = r2->field_b
    //     0xbd7db8: ldur            w5, [x2, #0xb]
    // 0xbd7dbc: DecompressPointer r5
    //     0xbd7dbc: add             x5, x5, HEAP, lsl #32
    // 0xbd7dc0: LoadField: r6 = r2->field_13
    //     0xbd7dc0: ldur            x6, [x2, #0x13]
    // 0xbd7dc4: add             x0, x6, #1
    // 0xbd7dc8: StoreField: r2->field_13 = r0
    //     0xbd7dc8: stur            x0, [x2, #0x13]
    // 0xbd7dcc: LoadField: r0 = r5->field_13
    //     0xbd7dcc: ldur            w0, [x5, #0x13]
    // 0xbd7dd0: r1 = LoadInt32Instr(r0)
    //     0xbd7dd0: sbfx            x1, x0, #1, #0x1f
    // 0xbd7dd4: mov             x0, x1
    // 0xbd7dd8: mov             x1, x6
    // 0xbd7ddc: cmp             x1, x0
    // 0xbd7de0: b.hs            #0xbd7e44
    // 0xbd7de4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7de4: add             x0, x5, x6
    //     0xbd7de8: strb            w4, [x0, #0x17]
    // 0xbd7dec: LoadField: r0 = r3->field_47
    //     0xbd7dec: ldur            w0, [x3, #0x47]
    // 0xbd7df0: DecompressPointer r0
    //     0xbd7df0: add             x0, x0, HEAP, lsl #32
    // 0xbd7df4: r16 = <String>
    //     0xbd7df4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd7df8: stp             x2, x16, [SP, #8]
    // 0xbd7dfc: str             x0, [SP]
    // 0xbd7e00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7e00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7e04: r0 = write()
    //     0xbd7e04: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7e08: r0 = Null
    //     0xbd7e08: mov             x0, NULL
    // 0xbd7e0c: LeaveFrame
    //     0xbd7e0c: mov             SP, fp
    //     0xbd7e10: ldp             fp, lr, [SP], #0x10
    // 0xbd7e14: ret
    //     0xbd7e14: ret             
    // 0xbd7e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7e1c: b               #0xbd7844
    // 0xbd7e20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e40: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd7e44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd7e44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0360, size: 0x24
    // 0xbf0360: r1 = 42
    //     0xbf0360: movz            x1, #0x2a
    // 0xbf0364: r16 = LoadInt32Instr(r1)
    //     0xbf0364: sbfx            x16, x1, #1, #0x1f
    // 0xbf0368: r17 = 11601
    //     0xbf0368: movz            x17, #0x2d51
    // 0xbf036c: mul             x0, x16, x17
    // 0xbf0370: umulh           x16, x16, x17
    // 0xbf0374: eor             x0, x0, x16
    // 0xbf0378: r0 = 0
    //     0xbf0378: eor             x0, x0, x0, lsr #32
    // 0xbf037c: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0380: ret
    //     0xbf0380: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76f14, size: 0x9c
    // 0xd76f14: EnterFrame
    //     0xd76f14: stp             fp, lr, [SP, #-0x10]!
    //     0xd76f18: mov             fp, SP
    // 0xd76f1c: AllocStack(0x10)
    //     0xd76f1c: sub             SP, SP, #0x10
    // 0xd76f20: CheckStackOverflow
    //     0xd76f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76f24: cmp             SP, x16
    //     0xd76f28: b.ls            #0xd76fa8
    // 0xd76f2c: ldr             x0, [fp, #0x10]
    // 0xd76f30: cmp             w0, NULL
    // 0xd76f34: b.ne            #0xd76f48
    // 0xd76f38: r0 = false
    //     0xd76f38: add             x0, NULL, #0x30  ; false
    // 0xd76f3c: LeaveFrame
    //     0xd76f3c: mov             SP, fp
    //     0xd76f40: ldp             fp, lr, [SP], #0x10
    // 0xd76f44: ret
    //     0xd76f44: ret             
    // 0xd76f48: ldr             x1, [fp, #0x18]
    // 0xd76f4c: cmp             w1, w0
    // 0xd76f50: b.ne            #0xd76f5c
    // 0xd76f54: r0 = true
    //     0xd76f54: add             x0, NULL, #0x20  ; true
    // 0xd76f58: b               #0xd76f9c
    // 0xd76f5c: r1 = 60
    //     0xd76f5c: movz            x1, #0x3c
    // 0xd76f60: branchIfSmi(r0, 0xd76f6c)
    //     0xd76f60: tbz             w0, #0, #0xd76f6c
    // 0xd76f64: r1 = LoadClassIdInstr(r0)
    //     0xd76f64: ldur            x1, [x0, #-1]
    //     0xd76f68: ubfx            x1, x1, #0xc, #0x14
    // 0xd76f6c: cmp             x1, #0x66e
    // 0xd76f70: b.ne            #0xd76f98
    // 0xd76f74: r16 = TafsirAdapter
    //     0xd76f74: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b118] Type: TafsirAdapter
    //     0xd76f78: ldr             x16, [x16, #0x118]
    // 0xd76f7c: r30 = TafsirAdapter
    //     0xd76f7c: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b118] Type: TafsirAdapter
    //     0xd76f80: ldr             lr, [lr, #0x118]
    // 0xd76f84: stp             lr, x16, [SP]
    // 0xd76f88: r0 = ==()
    //     0xd76f88: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd76f8c: tbnz            w0, #4, #0xd76f98
    // 0xd76f90: r0 = true
    //     0xd76f90: add             x0, NULL, #0x20  ; true
    // 0xd76f94: b               #0xd76f9c
    // 0xd76f98: r0 = false
    //     0xd76f98: add             x0, NULL, #0x30  ; false
    // 0xd76f9c: LeaveFrame
    //     0xd76f9c: mov             SP, fp
    //     0xd76fa0: ldp             fp, lr, [SP], #0x10
    // 0xd76fa4: ret
    //     0xd76fa4: ret             
    // 0xd76fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd76fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd76fac: b               #0xd76f2c
  }
}
