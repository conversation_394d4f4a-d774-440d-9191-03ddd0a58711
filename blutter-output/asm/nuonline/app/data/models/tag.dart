// lib: , url: package:nuonline/app/data/models/tag.dart

// class id: 1050055, size: 0x8
class :: {
}

// class id: 1645, size: 0x14, field offset: 0xc
class TagAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa66a04, size: 0x2bc
    // 0xa66a04: EnterFrame
    //     0xa66a04: stp             fp, lr, [SP, #-0x10]!
    //     0xa66a08: mov             fp, SP
    // 0xa66a0c: AllocStack(0x48)
    //     0xa66a0c: sub             SP, SP, #0x48
    // 0xa66a10: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa66a10: stur            x2, [fp, #-0x20]
    // 0xa66a14: CheckStackOverflow
    //     0xa66a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66a18: cmp             SP, x16
    //     0xa66a1c: b.ls            #0xa66ca8
    // 0xa66a20: LoadField: r3 = r2->field_23
    //     0xa66a20: ldur            x3, [x2, #0x23]
    // 0xa66a24: add             x0, x3, #1
    // 0xa66a28: LoadField: r1 = r2->field_1b
    //     0xa66a28: ldur            x1, [x2, #0x1b]
    // 0xa66a2c: cmp             x0, x1
    // 0xa66a30: b.gt            #0xa66c4c
    // 0xa66a34: LoadField: r4 = r2->field_7
    //     0xa66a34: ldur            w4, [x2, #7]
    // 0xa66a38: DecompressPointer r4
    //     0xa66a38: add             x4, x4, HEAP, lsl #32
    // 0xa66a3c: stur            x4, [fp, #-0x18]
    // 0xa66a40: StoreField: r2->field_23 = r0
    //     0xa66a40: stur            x0, [x2, #0x23]
    // 0xa66a44: LoadField: r0 = r4->field_13
    //     0xa66a44: ldur            w0, [x4, #0x13]
    // 0xa66a48: r5 = LoadInt32Instr(r0)
    //     0xa66a48: sbfx            x5, x0, #1, #0x1f
    // 0xa66a4c: mov             x0, x5
    // 0xa66a50: mov             x1, x3
    // 0xa66a54: stur            x5, [fp, #-0x10]
    // 0xa66a58: cmp             x1, x0
    // 0xa66a5c: b.hs            #0xa66cb0
    // 0xa66a60: LoadField: r0 = r4->field_7
    //     0xa66a60: ldur            x0, [x4, #7]
    // 0xa66a64: ldrb            w1, [x0, x3]
    // 0xa66a68: stur            x1, [fp, #-8]
    // 0xa66a6c: r16 = <int, dynamic>
    //     0xa66a6c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa66a70: ldr             x16, [x16, #0xac0]
    // 0xa66a74: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa66a78: stp             lr, x16, [SP]
    // 0xa66a7c: r0 = Map._fromLiteral()
    //     0xa66a7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa66a80: mov             x2, x0
    // 0xa66a84: stur            x2, [fp, #-0x38]
    // 0xa66a88: r6 = 0
    //     0xa66a88: movz            x6, #0
    // 0xa66a8c: ldur            x3, [fp, #-0x20]
    // 0xa66a90: ldur            x4, [fp, #-0x18]
    // 0xa66a94: ldur            x5, [fp, #-8]
    // 0xa66a98: stur            x6, [fp, #-0x30]
    // 0xa66a9c: CheckStackOverflow
    //     0xa66a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66aa0: cmp             SP, x16
    //     0xa66aa4: b.ls            #0xa66cb4
    // 0xa66aa8: cmp             x6, x5
    // 0xa66aac: b.ge            #0xa66b38
    // 0xa66ab0: LoadField: r7 = r3->field_23
    //     0xa66ab0: ldur            x7, [x3, #0x23]
    // 0xa66ab4: add             x0, x7, #1
    // 0xa66ab8: LoadField: r1 = r3->field_1b
    //     0xa66ab8: ldur            x1, [x3, #0x1b]
    // 0xa66abc: cmp             x0, x1
    // 0xa66ac0: b.gt            #0xa66c74
    // 0xa66ac4: StoreField: r3->field_23 = r0
    //     0xa66ac4: stur            x0, [x3, #0x23]
    // 0xa66ac8: ldur            x0, [fp, #-0x10]
    // 0xa66acc: mov             x1, x7
    // 0xa66ad0: cmp             x1, x0
    // 0xa66ad4: b.hs            #0xa66cbc
    // 0xa66ad8: LoadField: r0 = r4->field_7
    //     0xa66ad8: ldur            x0, [x4, #7]
    // 0xa66adc: ldrb            w8, [x0, x7]
    // 0xa66ae0: mov             x1, x3
    // 0xa66ae4: stur            x8, [fp, #-0x28]
    // 0xa66ae8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa66ae8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa66aec: r0 = read()
    //     0xa66aec: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa66af0: mov             x1, x0
    // 0xa66af4: ldur            x0, [fp, #-0x28]
    // 0xa66af8: lsl             x2, x0, #1
    // 0xa66afc: r16 = LoadInt32Instr(r2)
    //     0xa66afc: sbfx            x16, x2, #1, #0x1f
    // 0xa66b00: r17 = 11601
    //     0xa66b00: movz            x17, #0x2d51
    // 0xa66b04: mul             x0, x16, x17
    // 0xa66b08: umulh           x16, x16, x17
    // 0xa66b0c: eor             x0, x0, x16
    // 0xa66b10: r0 = 0
    //     0xa66b10: eor             x0, x0, x0, lsr #32
    // 0xa66b14: ubfiz           x0, x0, #1, #0x1e
    // 0xa66b18: r5 = LoadInt32Instr(r0)
    //     0xa66b18: sbfx            x5, x0, #1, #0x1f
    // 0xa66b1c: mov             x3, x1
    // 0xa66b20: ldur            x1, [fp, #-0x38]
    // 0xa66b24: r0 = _set()
    //     0xa66b24: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa66b28: ldur            x0, [fp, #-0x30]
    // 0xa66b2c: add             x6, x0, #1
    // 0xa66b30: ldur            x2, [fp, #-0x38]
    // 0xa66b34: b               #0xa66a8c
    // 0xa66b38: mov             x0, x2
    // 0xa66b3c: mov             x1, x0
    // 0xa66b40: r2 = 0
    //     0xa66b40: movz            x2, #0
    // 0xa66b44: r0 = _getValueOrData()
    //     0xa66b44: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66b48: ldur            x3, [fp, #-0x38]
    // 0xa66b4c: LoadField: r1 = r3->field_f
    //     0xa66b4c: ldur            w1, [x3, #0xf]
    // 0xa66b50: DecompressPointer r1
    //     0xa66b50: add             x1, x1, HEAP, lsl #32
    // 0xa66b54: cmp             w1, w0
    // 0xa66b58: b.ne            #0xa66b64
    // 0xa66b5c: r4 = Null
    //     0xa66b5c: mov             x4, NULL
    // 0xa66b60: b               #0xa66b68
    // 0xa66b64: mov             x4, x0
    // 0xa66b68: mov             x0, x4
    // 0xa66b6c: stur            x4, [fp, #-0x18]
    // 0xa66b70: r2 = Null
    //     0xa66b70: mov             x2, NULL
    // 0xa66b74: r1 = Null
    //     0xa66b74: mov             x1, NULL
    // 0xa66b78: branchIfSmi(r0, 0xa66ba0)
    //     0xa66b78: tbz             w0, #0, #0xa66ba0
    // 0xa66b7c: r4 = LoadClassIdInstr(r0)
    //     0xa66b7c: ldur            x4, [x0, #-1]
    //     0xa66b80: ubfx            x4, x4, #0xc, #0x14
    // 0xa66b84: sub             x4, x4, #0x3c
    // 0xa66b88: cmp             x4, #1
    // 0xa66b8c: b.ls            #0xa66ba0
    // 0xa66b90: r8 = int
    //     0xa66b90: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66b94: r3 = Null
    //     0xa66b94: add             x3, PP, #0x21, lsl #12  ; [pp+0x216f8] Null
    //     0xa66b98: ldr             x3, [x3, #0x6f8]
    // 0xa66b9c: r0 = int()
    //     0xa66b9c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66ba0: ldur            x1, [fp, #-0x38]
    // 0xa66ba4: r2 = 2
    //     0xa66ba4: movz            x2, #0x2
    // 0xa66ba8: r0 = _getValueOrData()
    //     0xa66ba8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66bac: mov             x1, x0
    // 0xa66bb0: ldur            x0, [fp, #-0x38]
    // 0xa66bb4: LoadField: r2 = r0->field_f
    //     0xa66bb4: ldur            w2, [x0, #0xf]
    // 0xa66bb8: DecompressPointer r2
    //     0xa66bb8: add             x2, x2, HEAP, lsl #32
    // 0xa66bbc: cmp             w2, w1
    // 0xa66bc0: b.ne            #0xa66bcc
    // 0xa66bc4: r4 = Null
    //     0xa66bc4: mov             x4, NULL
    // 0xa66bc8: b               #0xa66bd0
    // 0xa66bcc: mov             x4, x1
    // 0xa66bd0: ldur            x3, [fp, #-0x18]
    // 0xa66bd4: mov             x0, x4
    // 0xa66bd8: stur            x4, [fp, #-0x20]
    // 0xa66bdc: r2 = Null
    //     0xa66bdc: mov             x2, NULL
    // 0xa66be0: r1 = Null
    //     0xa66be0: mov             x1, NULL
    // 0xa66be4: r4 = 60
    //     0xa66be4: movz            x4, #0x3c
    // 0xa66be8: branchIfSmi(r0, 0xa66bf4)
    //     0xa66be8: tbz             w0, #0, #0xa66bf4
    // 0xa66bec: r4 = LoadClassIdInstr(r0)
    //     0xa66bec: ldur            x4, [x0, #-1]
    //     0xa66bf0: ubfx            x4, x4, #0xc, #0x14
    // 0xa66bf4: sub             x4, x4, #0x5e
    // 0xa66bf8: cmp             x4, #1
    // 0xa66bfc: b.ls            #0xa66c10
    // 0xa66c00: r8 = String
    //     0xa66c00: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa66c04: r3 = Null
    //     0xa66c04: add             x3, PP, #0x21, lsl #12  ; [pp+0x21708] Null
    //     0xa66c08: ldr             x3, [x3, #0x708]
    // 0xa66c0c: r0 = String()
    //     0xa66c0c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa66c10: ldur            x0, [fp, #-0x18]
    // 0xa66c14: r1 = LoadInt32Instr(r0)
    //     0xa66c14: sbfx            x1, x0, #1, #0x1f
    //     0xa66c18: tbz             w0, #0, #0xa66c20
    //     0xa66c1c: ldur            x1, [x0, #7]
    // 0xa66c20: stur            x1, [fp, #-8]
    // 0xa66c24: r0 = Tag()
    //     0xa66c24: bl              #0x8a9ce0  ; AllocateTagStub -> Tag (size=0x14)
    // 0xa66c28: mov             x1, x0
    // 0xa66c2c: ldur            x0, [fp, #-8]
    // 0xa66c30: StoreField: r1->field_7 = r0
    //     0xa66c30: stur            x0, [x1, #7]
    // 0xa66c34: ldur            x0, [fp, #-0x20]
    // 0xa66c38: StoreField: r1->field_f = r0
    //     0xa66c38: stur            w0, [x1, #0xf]
    // 0xa66c3c: mov             x0, x1
    // 0xa66c40: LeaveFrame
    //     0xa66c40: mov             SP, fp
    //     0xa66c44: ldp             fp, lr, [SP], #0x10
    // 0xa66c48: ret
    //     0xa66c48: ret             
    // 0xa66c4c: r0 = RangeError()
    //     0xa66c4c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa66c50: mov             x1, x0
    // 0xa66c54: r0 = "Not enough bytes available."
    //     0xa66c54: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa66c58: ldr             x0, [x0, #0x8a8]
    // 0xa66c5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa66c5c: stur            w0, [x1, #0x17]
    // 0xa66c60: r2 = false
    //     0xa66c60: add             x2, NULL, #0x30  ; false
    // 0xa66c64: StoreField: r1->field_b = r2
    //     0xa66c64: stur            w2, [x1, #0xb]
    // 0xa66c68: mov             x0, x1
    // 0xa66c6c: r0 = Throw()
    //     0xa66c6c: bl              #0xec04b8  ; ThrowStub
    // 0xa66c70: brk             #0
    // 0xa66c74: r0 = "Not enough bytes available."
    //     0xa66c74: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa66c78: ldr             x0, [x0, #0x8a8]
    // 0xa66c7c: r2 = false
    //     0xa66c7c: add             x2, NULL, #0x30  ; false
    // 0xa66c80: r0 = RangeError()
    //     0xa66c80: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa66c84: mov             x1, x0
    // 0xa66c88: r0 = "Not enough bytes available."
    //     0xa66c88: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa66c8c: ldr             x0, [x0, #0x8a8]
    // 0xa66c90: ArrayStore: r1[0] = r0  ; List_4
    //     0xa66c90: stur            w0, [x1, #0x17]
    // 0xa66c94: r0 = false
    //     0xa66c94: add             x0, NULL, #0x30  ; false
    // 0xa66c98: StoreField: r1->field_b = r0
    //     0xa66c98: stur            w0, [x1, #0xb]
    // 0xa66c9c: mov             x0, x1
    // 0xa66ca0: r0 = Throw()
    //     0xa66ca0: bl              #0xec04b8  ; ThrowStub
    // 0xa66ca4: brk             #0
    // 0xa66ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66cac: b               #0xa66a20
    // 0xa66cb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa66cb0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa66cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66cb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66cb8: b               #0xa66aa8
    // 0xa66cbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa66cbc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd7e48, size: 0x1fc
    // 0xbd7e48: EnterFrame
    //     0xbd7e48: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7e4c: mov             fp, SP
    // 0xbd7e50: AllocStack(0x28)
    //     0xbd7e50: sub             SP, SP, #0x28
    // 0xbd7e54: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd7e54: mov             x4, x2
    //     0xbd7e58: stur            x2, [fp, #-8]
    //     0xbd7e5c: stur            x3, [fp, #-0x10]
    // 0xbd7e60: CheckStackOverflow
    //     0xbd7e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7e64: cmp             SP, x16
    //     0xbd7e68: b.ls            #0xbd8030
    // 0xbd7e6c: mov             x0, x3
    // 0xbd7e70: r2 = Null
    //     0xbd7e70: mov             x2, NULL
    // 0xbd7e74: r1 = Null
    //     0xbd7e74: mov             x1, NULL
    // 0xbd7e78: r4 = 60
    //     0xbd7e78: movz            x4, #0x3c
    // 0xbd7e7c: branchIfSmi(r0, 0xbd7e88)
    //     0xbd7e7c: tbz             w0, #0, #0xbd7e88
    // 0xbd7e80: r4 = LoadClassIdInstr(r0)
    //     0xbd7e80: ldur            x4, [x0, #-1]
    //     0xbd7e84: ubfx            x4, x4, #0xc, #0x14
    // 0xbd7e88: r17 = 5574
    //     0xbd7e88: movz            x17, #0x15c6
    // 0xbd7e8c: cmp             x4, x17
    // 0xbd7e90: b.eq            #0xbd7ea8
    // 0xbd7e94: r8 = Tag
    //     0xbd7e94: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b478] Type: Tag
    //     0xbd7e98: ldr             x8, [x8, #0x478]
    // 0xbd7e9c: r3 = Null
    //     0xbd7e9c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b480] Null
    //     0xbd7ea0: ldr             x3, [x3, #0x480]
    // 0xbd7ea4: r0 = Tag()
    //     0xbd7ea4: bl              #0x80e13c  ; IsType_Tag_Stub
    // 0xbd7ea8: ldur            x0, [fp, #-8]
    // 0xbd7eac: LoadField: r1 = r0->field_b
    //     0xbd7eac: ldur            w1, [x0, #0xb]
    // 0xbd7eb0: DecompressPointer r1
    //     0xbd7eb0: add             x1, x1, HEAP, lsl #32
    // 0xbd7eb4: LoadField: r2 = r1->field_13
    //     0xbd7eb4: ldur            w2, [x1, #0x13]
    // 0xbd7eb8: LoadField: r1 = r0->field_13
    //     0xbd7eb8: ldur            x1, [x0, #0x13]
    // 0xbd7ebc: r3 = LoadInt32Instr(r2)
    //     0xbd7ebc: sbfx            x3, x2, #1, #0x1f
    // 0xbd7ec0: sub             x2, x3, x1
    // 0xbd7ec4: cmp             x2, #1
    // 0xbd7ec8: b.ge            #0xbd7ed8
    // 0xbd7ecc: mov             x1, x0
    // 0xbd7ed0: r2 = 1
    //     0xbd7ed0: movz            x2, #0x1
    // 0xbd7ed4: r0 = _increaseBufferSize()
    //     0xbd7ed4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7ed8: ldur            x3, [fp, #-8]
    // 0xbd7edc: r2 = 2
    //     0xbd7edc: movz            x2, #0x2
    // 0xbd7ee0: LoadField: r4 = r3->field_b
    //     0xbd7ee0: ldur            w4, [x3, #0xb]
    // 0xbd7ee4: DecompressPointer r4
    //     0xbd7ee4: add             x4, x4, HEAP, lsl #32
    // 0xbd7ee8: LoadField: r5 = r3->field_13
    //     0xbd7ee8: ldur            x5, [x3, #0x13]
    // 0xbd7eec: add             x6, x5, #1
    // 0xbd7ef0: StoreField: r3->field_13 = r6
    //     0xbd7ef0: stur            x6, [x3, #0x13]
    // 0xbd7ef4: LoadField: r0 = r4->field_13
    //     0xbd7ef4: ldur            w0, [x4, #0x13]
    // 0xbd7ef8: r7 = LoadInt32Instr(r0)
    //     0xbd7ef8: sbfx            x7, x0, #1, #0x1f
    // 0xbd7efc: mov             x0, x7
    // 0xbd7f00: mov             x1, x5
    // 0xbd7f04: cmp             x1, x0
    // 0xbd7f08: b.hs            #0xbd8038
    // 0xbd7f0c: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd7f0c: add             x0, x4, x5
    //     0xbd7f10: strb            w2, [x0, #0x17]
    // 0xbd7f14: sub             x0, x7, x6
    // 0xbd7f18: cmp             x0, #1
    // 0xbd7f1c: b.ge            #0xbd7f2c
    // 0xbd7f20: mov             x1, x3
    // 0xbd7f24: r2 = 1
    //     0xbd7f24: movz            x2, #0x1
    // 0xbd7f28: r0 = _increaseBufferSize()
    //     0xbd7f28: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7f2c: ldur            x2, [fp, #-8]
    // 0xbd7f30: ldur            x3, [fp, #-0x10]
    // 0xbd7f34: LoadField: r4 = r2->field_b
    //     0xbd7f34: ldur            w4, [x2, #0xb]
    // 0xbd7f38: DecompressPointer r4
    //     0xbd7f38: add             x4, x4, HEAP, lsl #32
    // 0xbd7f3c: LoadField: r5 = r2->field_13
    //     0xbd7f3c: ldur            x5, [x2, #0x13]
    // 0xbd7f40: add             x0, x5, #1
    // 0xbd7f44: StoreField: r2->field_13 = r0
    //     0xbd7f44: stur            x0, [x2, #0x13]
    // 0xbd7f48: LoadField: r0 = r4->field_13
    //     0xbd7f48: ldur            w0, [x4, #0x13]
    // 0xbd7f4c: r1 = LoadInt32Instr(r0)
    //     0xbd7f4c: sbfx            x1, x0, #1, #0x1f
    // 0xbd7f50: mov             x0, x1
    // 0xbd7f54: mov             x1, x5
    // 0xbd7f58: cmp             x1, x0
    // 0xbd7f5c: b.hs            #0xbd803c
    // 0xbd7f60: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd7f60: add             x0, x4, x5
    //     0xbd7f64: strb            wzr, [x0, #0x17]
    // 0xbd7f68: LoadField: r4 = r3->field_7
    //     0xbd7f68: ldur            x4, [x3, #7]
    // 0xbd7f6c: r0 = BoxInt64Instr(r4)
    //     0xbd7f6c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd7f70: cmp             x4, x0, asr #1
    //     0xbd7f74: b.eq            #0xbd7f80
    //     0xbd7f78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd7f7c: stur            x4, [x0, #7]
    // 0xbd7f80: r16 = <int>
    //     0xbd7f80: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd7f84: stp             x2, x16, [SP, #8]
    // 0xbd7f88: str             x0, [SP]
    // 0xbd7f8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd7f8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd7f90: r0 = write()
    //     0xbd7f90: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd7f94: ldur            x0, [fp, #-8]
    // 0xbd7f98: LoadField: r1 = r0->field_b
    //     0xbd7f98: ldur            w1, [x0, #0xb]
    // 0xbd7f9c: DecompressPointer r1
    //     0xbd7f9c: add             x1, x1, HEAP, lsl #32
    // 0xbd7fa0: LoadField: r2 = r1->field_13
    //     0xbd7fa0: ldur            w2, [x1, #0x13]
    // 0xbd7fa4: LoadField: r1 = r0->field_13
    //     0xbd7fa4: ldur            x1, [x0, #0x13]
    // 0xbd7fa8: r3 = LoadInt32Instr(r2)
    //     0xbd7fa8: sbfx            x3, x2, #1, #0x1f
    // 0xbd7fac: sub             x2, x3, x1
    // 0xbd7fb0: cmp             x2, #1
    // 0xbd7fb4: b.ge            #0xbd7fc4
    // 0xbd7fb8: mov             x1, x0
    // 0xbd7fbc: r2 = 1
    //     0xbd7fbc: movz            x2, #0x1
    // 0xbd7fc0: r0 = _increaseBufferSize()
    //     0xbd7fc0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd7fc4: ldur            x2, [fp, #-8]
    // 0xbd7fc8: ldur            x3, [fp, #-0x10]
    // 0xbd7fcc: r4 = 1
    //     0xbd7fcc: movz            x4, #0x1
    // 0xbd7fd0: LoadField: r5 = r2->field_b
    //     0xbd7fd0: ldur            w5, [x2, #0xb]
    // 0xbd7fd4: DecompressPointer r5
    //     0xbd7fd4: add             x5, x5, HEAP, lsl #32
    // 0xbd7fd8: LoadField: r6 = r2->field_13
    //     0xbd7fd8: ldur            x6, [x2, #0x13]
    // 0xbd7fdc: add             x0, x6, #1
    // 0xbd7fe0: StoreField: r2->field_13 = r0
    //     0xbd7fe0: stur            x0, [x2, #0x13]
    // 0xbd7fe4: LoadField: r0 = r5->field_13
    //     0xbd7fe4: ldur            w0, [x5, #0x13]
    // 0xbd7fe8: r1 = LoadInt32Instr(r0)
    //     0xbd7fe8: sbfx            x1, x0, #1, #0x1f
    // 0xbd7fec: mov             x0, x1
    // 0xbd7ff0: mov             x1, x6
    // 0xbd7ff4: cmp             x1, x0
    // 0xbd7ff8: b.hs            #0xbd8040
    // 0xbd7ffc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd7ffc: add             x0, x5, x6
    //     0xbd8000: strb            w4, [x0, #0x17]
    // 0xbd8004: LoadField: r0 = r3->field_f
    //     0xbd8004: ldur            w0, [x3, #0xf]
    // 0xbd8008: DecompressPointer r0
    //     0xbd8008: add             x0, x0, HEAP, lsl #32
    // 0xbd800c: r16 = <String>
    //     0xbd800c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd8010: stp             x2, x16, [SP, #8]
    // 0xbd8014: str             x0, [SP]
    // 0xbd8018: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8018: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd801c: r0 = write()
    //     0xbd801c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8020: r0 = Null
    //     0xbd8020: mov             x0, NULL
    // 0xbd8024: LeaveFrame
    //     0xbd8024: mov             SP, fp
    //     0xbd8028: ldp             fp, lr, [SP], #0x10
    // 0xbd802c: ret
    //     0xbd802c: ret             
    // 0xbd8030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8034: b               #0xbd7e6c
    // 0xbd8038: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8038: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd803c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd803c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd8040: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8040: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0384, size: 0x24
    // 0xbf0384: r1 = 86
    //     0xbf0384: movz            x1, #0x56
    // 0xbf0388: r16 = LoadInt32Instr(r1)
    //     0xbf0388: sbfx            x16, x1, #1, #0x1f
    // 0xbf038c: r17 = 11601
    //     0xbf038c: movz            x17, #0x2d51
    // 0xbf0390: mul             x0, x16, x17
    // 0xbf0394: umulh           x16, x16, x17
    // 0xbf0398: eor             x0, x0, x16
    // 0xbf039c: r0 = 0
    //     0xbf039c: eor             x0, x0, x0, lsr #32
    // 0xbf03a0: ubfiz           x0, x0, #1, #0x1e
    // 0xbf03a4: ret
    //     0xbf03a4: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd76fb0, size: 0x9c
    // 0xd76fb0: EnterFrame
    //     0xd76fb0: stp             fp, lr, [SP, #-0x10]!
    //     0xd76fb4: mov             fp, SP
    // 0xd76fb8: AllocStack(0x10)
    //     0xd76fb8: sub             SP, SP, #0x10
    // 0xd76fbc: CheckStackOverflow
    //     0xd76fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd76fc0: cmp             SP, x16
    //     0xd76fc4: b.ls            #0xd77044
    // 0xd76fc8: ldr             x0, [fp, #0x10]
    // 0xd76fcc: cmp             w0, NULL
    // 0xd76fd0: b.ne            #0xd76fe4
    // 0xd76fd4: r0 = false
    //     0xd76fd4: add             x0, NULL, #0x30  ; false
    // 0xd76fd8: LeaveFrame
    //     0xd76fd8: mov             SP, fp
    //     0xd76fdc: ldp             fp, lr, [SP], #0x10
    // 0xd76fe0: ret
    //     0xd76fe0: ret             
    // 0xd76fe4: ldr             x1, [fp, #0x18]
    // 0xd76fe8: cmp             w1, w0
    // 0xd76fec: b.ne            #0xd76ff8
    // 0xd76ff0: r0 = true
    //     0xd76ff0: add             x0, NULL, #0x20  ; true
    // 0xd76ff4: b               #0xd77038
    // 0xd76ff8: r1 = 60
    //     0xd76ff8: movz            x1, #0x3c
    // 0xd76ffc: branchIfSmi(r0, 0xd77008)
    //     0xd76ffc: tbz             w0, #0, #0xd77008
    // 0xd77000: r1 = LoadClassIdInstr(r0)
    //     0xd77000: ldur            x1, [x0, #-1]
    //     0xd77004: ubfx            x1, x1, #0xc, #0x14
    // 0xd77008: cmp             x1, #0x66d
    // 0xd7700c: b.ne            #0xd77034
    // 0xd77010: r16 = TagAdapter
    //     0xd77010: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b470] Type: TagAdapter
    //     0xd77014: ldr             x16, [x16, #0x470]
    // 0xd77018: r30 = TagAdapter
    //     0xd77018: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b470] Type: TagAdapter
    //     0xd7701c: ldr             lr, [lr, #0x470]
    // 0xd77020: stp             lr, x16, [SP]
    // 0xd77024: r0 = ==()
    //     0xd77024: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd77028: tbnz            w0, #4, #0xd77034
    // 0xd7702c: r0 = true
    //     0xd7702c: add             x0, NULL, #0x20  ; true
    // 0xd77030: b               #0xd77038
    // 0xd77034: r0 = false
    //     0xd77034: add             x0, NULL, #0x30  ; false
    // 0xd77038: LeaveFrame
    //     0xd77038: mov             SP, fp
    //     0xd7703c: ldp             fp, lr, [SP], #0x10
    // 0xd77040: ret
    //     0xd77040: ret             
    // 0xd77044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd77044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd77048: b               #0xd76fc8
  }
}

// class id: 5574, size: 0x14, field offset: 0x8
//   const constructor, 
class Tag extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0x8a9a04, size: 0x180
    // 0x8a9a04: EnterFrame
    //     0x8a9a04: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9a08: mov             fp, SP
    // 0x8a9a0c: AllocStack(0x20)
    //     0x8a9a0c: sub             SP, SP, #0x20
    // 0x8a9a10: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x8a9a10: mov             x3, x1
    //     0x8a9a14: stur            x1, [fp, #-8]
    // 0x8a9a18: CheckStackOverflow
    //     0x8a9a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9a1c: cmp             SP, x16
    //     0x8a9a20: b.ls            #0x8a9b7c
    // 0x8a9a24: mov             x0, x3
    // 0x8a9a28: r2 = Null
    //     0x8a9a28: mov             x2, NULL
    // 0x8a9a2c: r1 = Null
    //     0x8a9a2c: mov             x1, NULL
    // 0x8a9a30: cmp             w0, NULL
    // 0x8a9a34: b.eq            #0x8a9ad8
    // 0x8a9a38: branchIfSmi(r0, 0x8a9ad8)
    //     0x8a9a38: tbz             w0, #0, #0x8a9ad8
    // 0x8a9a3c: r3 = LoadClassIdInstr(r0)
    //     0x8a9a3c: ldur            x3, [x0, #-1]
    //     0x8a9a40: ubfx            x3, x3, #0xc, #0x14
    // 0x8a9a44: r17 = 6718
    //     0x8a9a44: movz            x17, #0x1a3e
    // 0x8a9a48: cmp             x3, x17
    // 0x8a9a4c: b.eq            #0x8a9ae0
    // 0x8a9a50: sub             x3, x3, #0x5a
    // 0x8a9a54: cmp             x3, #2
    // 0x8a9a58: b.ls            #0x8a9ae0
    // 0x8a9a5c: r4 = LoadClassIdInstr(r0)
    //     0x8a9a5c: ldur            x4, [x0, #-1]
    //     0x8a9a60: ubfx            x4, x4, #0xc, #0x14
    // 0x8a9a64: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8a9a68: ldr             x3, [x3, #0x18]
    // 0x8a9a6c: ldr             x3, [x3, x4, lsl #3]
    // 0x8a9a70: LoadField: r3 = r3->field_2b
    //     0x8a9a70: ldur            w3, [x3, #0x2b]
    // 0x8a9a74: DecompressPointer r3
    //     0x8a9a74: add             x3, x3, HEAP, lsl #32
    // 0x8a9a78: cmp             w3, NULL
    // 0x8a9a7c: b.eq            #0x8a9ad8
    // 0x8a9a80: LoadField: r3 = r3->field_f
    //     0x8a9a80: ldur            w3, [x3, #0xf]
    // 0x8a9a84: lsr             x3, x3, #3
    // 0x8a9a88: r17 = 6718
    //     0x8a9a88: movz            x17, #0x1a3e
    // 0x8a9a8c: cmp             x3, x17
    // 0x8a9a90: b.eq            #0x8a9ae0
    // 0x8a9a94: r3 = SubtypeTestCache
    //     0x8a9a94: add             x3, PP, #0x40, lsl #12  ; [pp+0x409d8] SubtypeTestCache
    //     0x8a9a98: ldr             x3, [x3, #0x9d8]
    // 0x8a9a9c: r30 = Subtype1TestCacheStub
    //     0x8a9a9c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8a9aa0: LoadField: r30 = r30->field_7
    //     0x8a9aa0: ldur            lr, [lr, #7]
    // 0x8a9aa4: blr             lr
    // 0x8a9aa8: cmp             w7, NULL
    // 0x8a9aac: b.eq            #0x8a9ab8
    // 0x8a9ab0: tbnz            w7, #4, #0x8a9ad8
    // 0x8a9ab4: b               #0x8a9ae0
    // 0x8a9ab8: r8 = List
    //     0x8a9ab8: add             x8, PP, #0x40, lsl #12  ; [pp+0x409e0] Type: List
    //     0x8a9abc: ldr             x8, [x8, #0x9e0]
    // 0x8a9ac0: r3 = SubtypeTestCache
    //     0x8a9ac0: add             x3, PP, #0x40, lsl #12  ; [pp+0x409e8] SubtypeTestCache
    //     0x8a9ac4: ldr             x3, [x3, #0x9e8]
    // 0x8a9ac8: r30 = InstanceOfStub
    //     0x8a9ac8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8a9acc: LoadField: r30 = r30->field_7
    //     0x8a9acc: ldur            lr, [lr, #7]
    // 0x8a9ad0: blr             lr
    // 0x8a9ad4: b               #0x8a9ae4
    // 0x8a9ad8: r0 = false
    //     0x8a9ad8: add             x0, NULL, #0x30  ; false
    // 0x8a9adc: b               #0x8a9ae4
    // 0x8a9ae0: r0 = true
    //     0x8a9ae0: add             x0, NULL, #0x20  ; true
    // 0x8a9ae4: tbnz            w0, #4, #0x8a9b64
    // 0x8a9ae8: ldur            x0, [fp, #-8]
    // 0x8a9aec: r1 = Function '<anonymous closure>': static.
    //     0x8a9aec: add             x1, PP, #0x40, lsl #12  ; [pp+0x409f0] AnonymousClosure: static (0x8a9b84), in [package:nuonline/app/data/models/tag.dart] Tag::fromResponse (0x8a9a04)
    //     0x8a9af0: ldr             x1, [x1, #0x9f0]
    // 0x8a9af4: r2 = Null
    //     0x8a9af4: mov             x2, NULL
    // 0x8a9af8: r0 = AllocateClosure()
    //     0x8a9af8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a9afc: mov             x1, x0
    // 0x8a9b00: ldur            x0, [fp, #-8]
    // 0x8a9b04: r2 = LoadClassIdInstr(r0)
    //     0x8a9b04: ldur            x2, [x0, #-1]
    //     0x8a9b08: ubfx            x2, x2, #0xc, #0x14
    // 0x8a9b0c: r16 = <Tag>
    //     0x8a9b0c: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0x8a9b10: stp             x0, x16, [SP, #8]
    // 0x8a9b14: str             x1, [SP]
    // 0x8a9b18: mov             x0, x2
    // 0x8a9b1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a9b1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a9b20: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8a9b20: movz            x17, #0xf28c
    //     0x8a9b24: add             lr, x0, x17
    //     0x8a9b28: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9b2c: blr             lr
    // 0x8a9b30: r1 = LoadClassIdInstr(r0)
    //     0x8a9b30: ldur            x1, [x0, #-1]
    //     0x8a9b34: ubfx            x1, x1, #0xc, #0x14
    // 0x8a9b38: mov             x16, x0
    // 0x8a9b3c: mov             x0, x1
    // 0x8a9b40: mov             x1, x16
    // 0x8a9b44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a9b44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a9b48: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8a9b48: movz            x17, #0xd889
    //     0x8a9b4c: add             lr, x0, x17
    //     0x8a9b50: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9b54: blr             lr
    // 0x8a9b58: LeaveFrame
    //     0x8a9b58: mov             SP, fp
    //     0x8a9b5c: ldp             fp, lr, [SP], #0x10
    // 0x8a9b60: ret
    //     0x8a9b60: ret             
    // 0x8a9b64: r1 = <Tag>
    //     0x8a9b64: ldr             x1, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0x8a9b68: r2 = 0
    //     0x8a9b68: movz            x2, #0
    // 0x8a9b6c: r0 = _GrowableList()
    //     0x8a9b6c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8a9b70: LeaveFrame
    //     0x8a9b70: mov             SP, fp
    //     0x8a9b74: ldp             fp, lr, [SP], #0x10
    // 0x8a9b78: ret
    //     0x8a9b78: ret             
    // 0x8a9b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9b80: b               #0x8a9a24
  }
  [closure] static Tag <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8a9b84, size: 0x50
    // 0x8a9b84: EnterFrame
    //     0x8a9b84: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9b88: mov             fp, SP
    // 0x8a9b8c: CheckStackOverflow
    //     0x8a9b8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9b90: cmp             SP, x16
    //     0x8a9b94: b.ls            #0x8a9bcc
    // 0x8a9b98: ldr             x0, [fp, #0x10]
    // 0x8a9b9c: r2 = Null
    //     0x8a9b9c: mov             x2, NULL
    // 0x8a9ba0: r1 = Null
    //     0x8a9ba0: mov             x1, NULL
    // 0x8a9ba4: r8 = Map<String, dynamic>
    //     0x8a9ba4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8a9ba8: r3 = Null
    //     0x8a9ba8: add             x3, PP, #0x40, lsl #12  ; [pp+0x409f8] Null
    //     0x8a9bac: ldr             x3, [x3, #0x9f8]
    // 0x8a9bb0: r0 = Map<String, dynamic>()
    //     0x8a9bb0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8a9bb4: ldr             x2, [fp, #0x10]
    // 0x8a9bb8: r1 = Null
    //     0x8a9bb8: mov             x1, NULL
    // 0x8a9bbc: r0 = Tag.fromMap()
    //     0x8a9bbc: bl              #0x8a9bd4  ; [package:nuonline/app/data/models/tag.dart] Tag::Tag.fromMap
    // 0x8a9bc0: LeaveFrame
    //     0x8a9bc0: mov             SP, fp
    //     0x8a9bc4: ldp             fp, lr, [SP], #0x10
    // 0x8a9bc8: ret
    //     0x8a9bc8: ret             
    // 0x8a9bcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9bcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9bd0: b               #0x8a9b98
  }
  factory _ Tag.fromMap(/* No info */) {
    // ** addr: 0x8a9bd4, size: 0x10c
    // 0x8a9bd4: EnterFrame
    //     0x8a9bd4: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9bd8: mov             fp, SP
    // 0x8a9bdc: AllocStack(0x18)
    //     0x8a9bdc: sub             SP, SP, #0x18
    // 0x8a9be0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x8a9be0: mov             x3, x2
    //     0x8a9be4: stur            x2, [fp, #-8]
    // 0x8a9be8: CheckStackOverflow
    //     0x8a9be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9bec: cmp             SP, x16
    //     0x8a9bf0: b.ls            #0x8a9cd8
    // 0x8a9bf4: r0 = LoadClassIdInstr(r3)
    //     0x8a9bf4: ldur            x0, [x3, #-1]
    //     0x8a9bf8: ubfx            x0, x0, #0xc, #0x14
    // 0x8a9bfc: mov             x1, x3
    // 0x8a9c00: r2 = "id"
    //     0x8a9c00: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8a9c04: ldr             x2, [x2, #0x740]
    // 0x8a9c08: r0 = GDT[cid_x0 + -0x114]()
    //     0x8a9c08: sub             lr, x0, #0x114
    //     0x8a9c0c: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9c10: blr             lr
    // 0x8a9c14: mov             x3, x0
    // 0x8a9c18: r2 = Null
    //     0x8a9c18: mov             x2, NULL
    // 0x8a9c1c: r1 = Null
    //     0x8a9c1c: mov             x1, NULL
    // 0x8a9c20: stur            x3, [fp, #-0x10]
    // 0x8a9c24: branchIfSmi(r0, 0x8a9c4c)
    //     0x8a9c24: tbz             w0, #0, #0x8a9c4c
    // 0x8a9c28: r4 = LoadClassIdInstr(r0)
    //     0x8a9c28: ldur            x4, [x0, #-1]
    //     0x8a9c2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8a9c30: sub             x4, x4, #0x3c
    // 0x8a9c34: cmp             x4, #1
    // 0x8a9c38: b.ls            #0x8a9c4c
    // 0x8a9c3c: r8 = int
    //     0x8a9c3c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8a9c40: r3 = Null
    //     0x8a9c40: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a08] Null
    //     0x8a9c44: ldr             x3, [x3, #0xa08]
    // 0x8a9c48: r0 = int()
    //     0x8a9c48: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8a9c4c: ldur            x1, [fp, #-8]
    // 0x8a9c50: r0 = LoadClassIdInstr(r1)
    //     0x8a9c50: ldur            x0, [x1, #-1]
    //     0x8a9c54: ubfx            x0, x0, #0xc, #0x14
    // 0x8a9c58: r2 = "name"
    //     0x8a9c58: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x8a9c5c: r0 = GDT[cid_x0 + -0x114]()
    //     0x8a9c5c: sub             lr, x0, #0x114
    //     0x8a9c60: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9c64: blr             lr
    // 0x8a9c68: mov             x3, x0
    // 0x8a9c6c: r2 = Null
    //     0x8a9c6c: mov             x2, NULL
    // 0x8a9c70: r1 = Null
    //     0x8a9c70: mov             x1, NULL
    // 0x8a9c74: stur            x3, [fp, #-8]
    // 0x8a9c78: r4 = 60
    //     0x8a9c78: movz            x4, #0x3c
    // 0x8a9c7c: branchIfSmi(r0, 0x8a9c88)
    //     0x8a9c7c: tbz             w0, #0, #0x8a9c88
    // 0x8a9c80: r4 = LoadClassIdInstr(r0)
    //     0x8a9c80: ldur            x4, [x0, #-1]
    //     0x8a9c84: ubfx            x4, x4, #0xc, #0x14
    // 0x8a9c88: sub             x4, x4, #0x5e
    // 0x8a9c8c: cmp             x4, #1
    // 0x8a9c90: b.ls            #0x8a9ca4
    // 0x8a9c94: r8 = String
    //     0x8a9c94: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8a9c98: r3 = Null
    //     0x8a9c98: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a18] Null
    //     0x8a9c9c: ldr             x3, [x3, #0xa18]
    // 0x8a9ca0: r0 = String()
    //     0x8a9ca0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8a9ca4: ldur            x0, [fp, #-0x10]
    // 0x8a9ca8: r1 = LoadInt32Instr(r0)
    //     0x8a9ca8: sbfx            x1, x0, #1, #0x1f
    //     0x8a9cac: tbz             w0, #0, #0x8a9cb4
    //     0x8a9cb0: ldur            x1, [x0, #7]
    // 0x8a9cb4: stur            x1, [fp, #-0x18]
    // 0x8a9cb8: r0 = Tag()
    //     0x8a9cb8: bl              #0x8a9ce0  ; AllocateTagStub -> Tag (size=0x14)
    // 0x8a9cbc: ldur            x1, [fp, #-0x18]
    // 0x8a9cc0: StoreField: r0->field_7 = r1
    //     0x8a9cc0: stur            x1, [x0, #7]
    // 0x8a9cc4: ldur            x1, [fp, #-8]
    // 0x8a9cc8: StoreField: r0->field_f = r1
    //     0x8a9cc8: stur            w1, [x0, #0xf]
    // 0x8a9ccc: LeaveFrame
    //     0x8a9ccc: mov             SP, fp
    //     0x8a9cd0: ldp             fp, lr, [SP], #0x10
    // 0x8a9cd4: ret
    //     0x8a9cd4: ret             
    // 0x8a9cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9cdc: b               #0x8a9bf4
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc144, size: 0x80
    // 0xbdc144: EnterFrame
    //     0xbdc144: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc148: mov             fp, SP
    // 0xbdc14c: AllocStack(0x18)
    //     0xbdc14c: sub             SP, SP, #0x18
    // 0xbdc150: r3 = 4
    //     0xbdc150: movz            x3, #0x4
    // 0xbdc154: LoadField: r2 = r1->field_7
    //     0xbdc154: ldur            x2, [x1, #7]
    // 0xbdc158: LoadField: r4 = r1->field_f
    //     0xbdc158: ldur            w4, [x1, #0xf]
    // 0xbdc15c: DecompressPointer r4
    //     0xbdc15c: add             x4, x4, HEAP, lsl #32
    // 0xbdc160: stur            x4, [fp, #-0x10]
    // 0xbdc164: r0 = BoxInt64Instr(r2)
    //     0xbdc164: sbfiz           x0, x2, #1, #0x1f
    //     0xbdc168: cmp             x2, x0, asr #1
    //     0xbdc16c: b.eq            #0xbdc178
    //     0xbdc170: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc174: stur            x2, [x0, #7]
    // 0xbdc178: mov             x2, x3
    // 0xbdc17c: r1 = Null
    //     0xbdc17c: mov             x1, NULL
    // 0xbdc180: stur            x0, [fp, #-8]
    // 0xbdc184: r0 = AllocateArray()
    //     0xbdc184: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc188: mov             x2, x0
    // 0xbdc18c: ldur            x0, [fp, #-8]
    // 0xbdc190: stur            x2, [fp, #-0x18]
    // 0xbdc194: StoreField: r2->field_f = r0
    //     0xbdc194: stur            w0, [x2, #0xf]
    // 0xbdc198: ldur            x0, [fp, #-0x10]
    // 0xbdc19c: StoreField: r2->field_13 = r0
    //     0xbdc19c: stur            w0, [x2, #0x13]
    // 0xbdc1a0: r1 = <Object?>
    //     0xbdc1a0: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc1a4: r0 = AllocateGrowableArray()
    //     0xbdc1a4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc1a8: ldur            x1, [fp, #-0x18]
    // 0xbdc1ac: StoreField: r0->field_f = r1
    //     0xbdc1ac: stur            w1, [x0, #0xf]
    // 0xbdc1b0: r1 = 4
    //     0xbdc1b0: movz            x1, #0x4
    // 0xbdc1b4: StoreField: r0->field_b = r1
    //     0xbdc1b4: stur            w1, [x0, #0xb]
    // 0xbdc1b8: LeaveFrame
    //     0xbdc1b8: mov             SP, fp
    //     0xbdc1bc: ldp             fp, lr, [SP], #0x10
    // 0xbdc1c0: ret
    //     0xbdc1c0: ret             
  }
}
