// lib: , url: package:nuonline/app/data/models/tajweed.dart

// class id: 1050056, size: 0x8
class :: {
}

// class id: 1121, size: 0x2c, field offset: 0x8
class Tajweed extends Object {

  factory _ Tajweed.fromMap(/* No info */) {
    // ** addr: 0x7c7890, size: 0x2f4
    // 0x7c7890: EnterFrame
    //     0x7c7890: stp             fp, lr, [SP, #-0x10]!
    //     0x7c7894: mov             fp, SP
    // 0x7c7898: AllocStack(0x38)
    //     0x7c7898: sub             SP, SP, #0x38
    // 0x7c789c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7c789c: mov             x3, x2
    //     0x7c78a0: stur            x2, [fp, #-8]
    // 0x7c78a4: CheckStackOverflow
    //     0x7c78a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c78a8: cmp             SP, x16
    //     0x7c78ac: b.ls            #0x7c7b7c
    // 0x7c78b0: r0 = LoadClassIdInstr(r3)
    //     0x7c78b0: ldur            x0, [x3, #-1]
    //     0x7c78b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c78b8: mov             x1, x3
    // 0x7c78bc: r2 = "class"
    //     0x7c78bc: add             x2, PP, #0xd, lsl #12  ; [pp+0xd670] "class"
    //     0x7c78c0: ldr             x2, [x2, #0x670]
    // 0x7c78c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c78c4: sub             lr, x0, #0x114
    //     0x7c78c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7c78cc: blr             lr
    // 0x7c78d0: mov             x3, x0
    // 0x7c78d4: r2 = Null
    //     0x7c78d4: mov             x2, NULL
    // 0x7c78d8: r1 = Null
    //     0x7c78d8: mov             x1, NULL
    // 0x7c78dc: stur            x3, [fp, #-0x10]
    // 0x7c78e0: r4 = 60
    //     0x7c78e0: movz            x4, #0x3c
    // 0x7c78e4: branchIfSmi(r0, 0x7c78f0)
    //     0x7c78e4: tbz             w0, #0, #0x7c78f0
    // 0x7c78e8: r4 = LoadClassIdInstr(r0)
    //     0x7c78e8: ldur            x4, [x0, #-1]
    //     0x7c78ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7c78f0: sub             x4, x4, #0x5e
    // 0x7c78f4: cmp             x4, #1
    // 0x7c78f8: b.ls            #0x7c790c
    // 0x7c78fc: r8 = String?
    //     0x7c78fc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7c7900: r3 = Null
    //     0x7c7900: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a2d8] Null
    //     0x7c7904: ldr             x3, [x3, #0x2d8]
    // 0x7c7908: r0 = String?()
    //     0x7c7908: bl              #0x600324  ; IsType_String?_Stub
    // 0x7c790c: ldur            x0, [fp, #-0x10]
    // 0x7c7910: cmp             w0, NULL
    // 0x7c7914: b.ne            #0x7c7920
    // 0x7c7918: r4 = ""
    //     0x7c7918: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7c791c: b               #0x7c7924
    // 0x7c7920: mov             x4, x0
    // 0x7c7924: ldur            x3, [fp, #-8]
    // 0x7c7928: stur            x4, [fp, #-0x10]
    // 0x7c792c: r0 = LoadClassIdInstr(r3)
    //     0x7c792c: ldur            x0, [x3, #-1]
    //     0x7c7930: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7934: mov             x1, x3
    // 0x7c7938: r2 = "start_baris"
    //     0x7c7938: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a2e8] "start_baris"
    //     0x7c793c: ldr             x2, [x2, #0x2e8]
    // 0x7c7940: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7940: sub             lr, x0, #0x114
    //     0x7c7944: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7948: blr             lr
    // 0x7c794c: mov             x3, x0
    // 0x7c7950: r2 = Null
    //     0x7c7950: mov             x2, NULL
    // 0x7c7954: r1 = Null
    //     0x7c7954: mov             x1, NULL
    // 0x7c7958: stur            x3, [fp, #-0x18]
    // 0x7c795c: branchIfSmi(r0, 0x7c7984)
    //     0x7c795c: tbz             w0, #0, #0x7c7984
    // 0x7c7960: r4 = LoadClassIdInstr(r0)
    //     0x7c7960: ldur            x4, [x0, #-1]
    //     0x7c7964: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7968: sub             x4, x4, #0x3c
    // 0x7c796c: cmp             x4, #1
    // 0x7c7970: b.ls            #0x7c7984
    // 0x7c7974: r8 = int?
    //     0x7c7974: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7c7978: r3 = Null
    //     0x7c7978: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a2f0] Null
    //     0x7c797c: ldr             x3, [x3, #0x2f0]
    // 0x7c7980: r0 = int?()
    //     0x7c7980: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7c7984: ldur            x0, [fp, #-0x18]
    // 0x7c7988: cmp             w0, NULL
    // 0x7c798c: b.ne            #0x7c7998
    // 0x7c7990: r4 = 0
    //     0x7c7990: movz            x4, #0
    // 0x7c7994: b               #0x7c79a8
    // 0x7c7998: r1 = LoadInt32Instr(r0)
    //     0x7c7998: sbfx            x1, x0, #1, #0x1f
    //     0x7c799c: tbz             w0, #0, #0x7c79a4
    //     0x7c79a0: ldur            x1, [x0, #7]
    // 0x7c79a4: mov             x4, x1
    // 0x7c79a8: ldur            x3, [fp, #-8]
    // 0x7c79ac: stur            x4, [fp, #-0x20]
    // 0x7c79b0: r0 = LoadClassIdInstr(r3)
    //     0x7c79b0: ldur            x0, [x3, #-1]
    //     0x7c79b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c79b8: mov             x1, x3
    // 0x7c79bc: r2 = "end_baris"
    //     0x7c79bc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a300] "end_baris"
    //     0x7c79c0: ldr             x2, [x2, #0x300]
    // 0x7c79c4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c79c4: sub             lr, x0, #0x114
    //     0x7c79c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7c79cc: blr             lr
    // 0x7c79d0: mov             x3, x0
    // 0x7c79d4: r2 = Null
    //     0x7c79d4: mov             x2, NULL
    // 0x7c79d8: r1 = Null
    //     0x7c79d8: mov             x1, NULL
    // 0x7c79dc: stur            x3, [fp, #-0x18]
    // 0x7c79e0: branchIfSmi(r0, 0x7c7a08)
    //     0x7c79e0: tbz             w0, #0, #0x7c7a08
    // 0x7c79e4: r4 = LoadClassIdInstr(r0)
    //     0x7c79e4: ldur            x4, [x0, #-1]
    //     0x7c79e8: ubfx            x4, x4, #0xc, #0x14
    // 0x7c79ec: sub             x4, x4, #0x3c
    // 0x7c79f0: cmp             x4, #1
    // 0x7c79f4: b.ls            #0x7c7a08
    // 0x7c79f8: r8 = int?
    //     0x7c79f8: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7c79fc: r3 = Null
    //     0x7c79fc: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a308] Null
    //     0x7c7a00: ldr             x3, [x3, #0x308]
    // 0x7c7a04: r0 = int?()
    //     0x7c7a04: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7c7a08: ldur            x0, [fp, #-0x18]
    // 0x7c7a0c: cmp             w0, NULL
    // 0x7c7a10: b.ne            #0x7c7a1c
    // 0x7c7a14: r4 = 0
    //     0x7c7a14: movz            x4, #0
    // 0x7c7a18: b               #0x7c7a2c
    // 0x7c7a1c: r1 = LoadInt32Instr(r0)
    //     0x7c7a1c: sbfx            x1, x0, #1, #0x1f
    //     0x7c7a20: tbz             w0, #0, #0x7c7a28
    //     0x7c7a24: ldur            x1, [x0, #7]
    // 0x7c7a28: mov             x4, x1
    // 0x7c7a2c: ldur            x3, [fp, #-8]
    // 0x7c7a30: stur            x4, [fp, #-0x28]
    // 0x7c7a34: r0 = LoadClassIdInstr(r3)
    //     0x7c7a34: ldur            x0, [x3, #-1]
    //     0x7c7a38: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7a3c: mov             x1, x3
    // 0x7c7a40: r2 = "start_pojok"
    //     0x7c7a40: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a318] "start_pojok"
    //     0x7c7a44: ldr             x2, [x2, #0x318]
    // 0x7c7a48: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7a48: sub             lr, x0, #0x114
    //     0x7c7a4c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7a50: blr             lr
    // 0x7c7a54: mov             x3, x0
    // 0x7c7a58: r2 = Null
    //     0x7c7a58: mov             x2, NULL
    // 0x7c7a5c: r1 = Null
    //     0x7c7a5c: mov             x1, NULL
    // 0x7c7a60: stur            x3, [fp, #-0x18]
    // 0x7c7a64: branchIfSmi(r0, 0x7c7a8c)
    //     0x7c7a64: tbz             w0, #0, #0x7c7a8c
    // 0x7c7a68: r4 = LoadClassIdInstr(r0)
    //     0x7c7a68: ldur            x4, [x0, #-1]
    //     0x7c7a6c: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7a70: sub             x4, x4, #0x3c
    // 0x7c7a74: cmp             x4, #1
    // 0x7c7a78: b.ls            #0x7c7a8c
    // 0x7c7a7c: r8 = int?
    //     0x7c7a7c: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7c7a80: r3 = Null
    //     0x7c7a80: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a320] Null
    //     0x7c7a84: ldr             x3, [x3, #0x320]
    // 0x7c7a88: r0 = int?()
    //     0x7c7a88: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7c7a8c: ldur            x0, [fp, #-0x18]
    // 0x7c7a90: cmp             w0, NULL
    // 0x7c7a94: b.ne            #0x7c7aa0
    // 0x7c7a98: r3 = 0
    //     0x7c7a98: movz            x3, #0
    // 0x7c7a9c: b               #0x7c7ab0
    // 0x7c7aa0: r1 = LoadInt32Instr(r0)
    //     0x7c7aa0: sbfx            x1, x0, #1, #0x1f
    //     0x7c7aa4: tbz             w0, #0, #0x7c7aac
    //     0x7c7aa8: ldur            x1, [x0, #7]
    // 0x7c7aac: mov             x3, x1
    // 0x7c7ab0: ldur            x1, [fp, #-8]
    // 0x7c7ab4: stur            x3, [fp, #-0x30]
    // 0x7c7ab8: r0 = LoadClassIdInstr(r1)
    //     0x7c7ab8: ldur            x0, [x1, #-1]
    //     0x7c7abc: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7ac0: r2 = "end_pojok"
    //     0x7c7ac0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a330] "end_pojok"
    //     0x7c7ac4: ldr             x2, [x2, #0x330]
    // 0x7c7ac8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7ac8: sub             lr, x0, #0x114
    //     0x7c7acc: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7ad0: blr             lr
    // 0x7c7ad4: mov             x3, x0
    // 0x7c7ad8: r2 = Null
    //     0x7c7ad8: mov             x2, NULL
    // 0x7c7adc: r1 = Null
    //     0x7c7adc: mov             x1, NULL
    // 0x7c7ae0: stur            x3, [fp, #-8]
    // 0x7c7ae4: branchIfSmi(r0, 0x7c7b0c)
    //     0x7c7ae4: tbz             w0, #0, #0x7c7b0c
    // 0x7c7ae8: r4 = LoadClassIdInstr(r0)
    //     0x7c7ae8: ldur            x4, [x0, #-1]
    //     0x7c7aec: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7af0: sub             x4, x4, #0x3c
    // 0x7c7af4: cmp             x4, #1
    // 0x7c7af8: b.ls            #0x7c7b0c
    // 0x7c7afc: r8 = int?
    //     0x7c7afc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7c7b00: r3 = Null
    //     0x7c7b00: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a338] Null
    //     0x7c7b04: ldr             x3, [x3, #0x338]
    // 0x7c7b08: r0 = int?()
    //     0x7c7b08: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7c7b0c: ldur            x0, [fp, #-8]
    // 0x7c7b10: cmp             w0, NULL
    // 0x7c7b14: b.ne            #0x7c7b20
    // 0x7c7b18: r4 = 0
    //     0x7c7b18: movz            x4, #0
    // 0x7c7b1c: b               #0x7c7b30
    // 0x7c7b20: r1 = LoadInt32Instr(r0)
    //     0x7c7b20: sbfx            x1, x0, #1, #0x1f
    //     0x7c7b24: tbz             w0, #0, #0x7c7b2c
    //     0x7c7b28: ldur            x1, [x0, #7]
    // 0x7c7b2c: mov             x4, x1
    // 0x7c7b30: ldur            x3, [fp, #-0x10]
    // 0x7c7b34: ldur            x2, [fp, #-0x20]
    // 0x7c7b38: ldur            x1, [fp, #-0x28]
    // 0x7c7b3c: ldur            x0, [fp, #-0x30]
    // 0x7c7b40: stur            x4, [fp, #-0x38]
    // 0x7c7b44: r0 = Tajweed()
    //     0x7c7b44: bl              #0x7c7ba4  ; AllocateTajweedStub -> Tajweed (size=0x2c)
    // 0x7c7b48: ldur            x1, [fp, #-0x10]
    // 0x7c7b4c: StoreField: r0->field_7 = r1
    //     0x7c7b4c: stur            w1, [x0, #7]
    // 0x7c7b50: ldur            x1, [fp, #-0x20]
    // 0x7c7b54: StoreField: r0->field_b = r1
    //     0x7c7b54: stur            x1, [x0, #0xb]
    // 0x7c7b58: ldur            x1, [fp, #-0x28]
    // 0x7c7b5c: StoreField: r0->field_13 = r1
    //     0x7c7b5c: stur            x1, [x0, #0x13]
    // 0x7c7b60: ldur            x1, [fp, #-0x30]
    // 0x7c7b64: StoreField: r0->field_1b = r1
    //     0x7c7b64: stur            x1, [x0, #0x1b]
    // 0x7c7b68: ldur            x1, [fp, #-0x38]
    // 0x7c7b6c: StoreField: r0->field_23 = r1
    //     0x7c7b6c: stur            x1, [x0, #0x23]
    // 0x7c7b70: LeaveFrame
    //     0x7c7b70: mov             SP, fp
    //     0x7c7b74: ldp             fp, lr, [SP], #0x10
    // 0x7c7b78: ret
    //     0x7c7b78: ret             
    // 0x7c7b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c7b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c7b80: b               #0x7c78b0
  }
  _ colorOf(/* No info */) {
    // ** addr: 0xba939c, size: 0x568
    // 0xba939c: EnterFrame
    //     0xba939c: stp             fp, lr, [SP, #-0x10]!
    //     0xba93a0: mov             fp, SP
    // 0xba93a4: AllocStack(0x28)
    //     0xba93a4: sub             SP, SP, #0x28
    // 0xba93a8: SetupParameters(Tajweed this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xba93a8: mov             x0, x1
    //     0xba93ac: stur            x1, [fp, #-8]
    //     0xba93b0: mov             x1, x2
    // 0xba93b4: CheckStackOverflow
    //     0xba93b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba93b8: cmp             SP, x16
    //     0xba93bc: b.ls            #0xba98fc
    // 0xba93c0: r0 = of()
    //     0xba93c0: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba93c4: r16 = <NTajweedColor>
    //     0xba93c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ce0] TypeArguments: <NTajweedColor>
    //     0xba93c8: ldr             x16, [x16, #0xce0]
    // 0xba93cc: stp             x0, x16, [SP]
    // 0xba93d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba93d0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba93d4: r0 = extension()
    //     0xba93d4: bl              #0xb3132c  ; [package:flutter/src/material/theme_data.dart] ThemeData::extension
    // 0xba93d8: mov             x1, x0
    // 0xba93dc: ldur            x0, [fp, #-8]
    // 0xba93e0: stur            x1, [fp, #-0x18]
    // 0xba93e4: LoadField: r2 = r0->field_7
    //     0xba93e4: ldur            w2, [x0, #7]
    // 0xba93e8: DecompressPointer r2
    //     0xba93e8: add             x2, x2, HEAP, lsl #32
    // 0xba93ec: stur            x2, [fp, #-0x10]
    // 0xba93f0: r16 = "tidak-dilafalkan"
    //     0xba93f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x330f0] "tidak-dilafalkan"
    //     0xba93f4: ldr             x16, [x16, #0xf0]
    // 0xba93f8: stp             x2, x16, [SP]
    // 0xba93fc: r0 = ==()
    //     0xba93fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9400: tbnz            w0, #4, #0xba9428
    // 0xba9404: ldur            x0, [fp, #-0x18]
    // 0xba9408: cmp             w0, NULL
    // 0xba940c: b.ne            #0xba9418
    // 0xba9410: r0 = Null
    //     0xba9410: mov             x0, NULL
    // 0xba9414: b               #0xba98f0
    // 0xba9418: LoadField: r1 = r0->field_1f
    //     0xba9418: ldur            w1, [x0, #0x1f]
    // 0xba941c: DecompressPointer r1
    //     0xba941c: add             x1, x1, HEAP, lsl #32
    // 0xba9420: mov             x0, x1
    // 0xba9424: b               #0xba98f0
    // 0xba9428: ldur            x0, [fp, #-0x18]
    // 0xba942c: r16 = "ghunnah"
    //     0xba942c: add             x16, PP, #0x33, lsl #12  ; [pp+0x330f8] "ghunnah"
    //     0xba9430: ldr             x16, [x16, #0xf8]
    // 0xba9434: ldur            lr, [fp, #-0x10]
    // 0xba9438: stp             lr, x16, [SP]
    // 0xba943c: r0 = ==()
    //     0xba943c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9440: tbnz            w0, #4, #0xba9468
    // 0xba9444: ldur            x0, [fp, #-0x18]
    // 0xba9448: cmp             w0, NULL
    // 0xba944c: b.ne            #0xba9458
    // 0xba9450: r0 = Null
    //     0xba9450: mov             x0, NULL
    // 0xba9454: b               #0xba98f0
    // 0xba9458: LoadField: r1 = r0->field_b
    //     0xba9458: ldur            w1, [x0, #0xb]
    // 0xba945c: DecompressPointer r1
    //     0xba945c: add             x1, x1, HEAP, lsl #32
    // 0xba9460: mov             x0, x1
    // 0xba9464: b               #0xba98f0
    // 0xba9468: ldur            x0, [fp, #-0x18]
    // 0xba946c: r16 = "idgham-bighunnah"
    //     0xba946c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33100] "idgham-bighunnah"
    //     0xba9470: ldr             x16, [x16, #0x100]
    // 0xba9474: ldur            lr, [fp, #-0x10]
    // 0xba9478: stp             lr, x16, [SP]
    // 0xba947c: r0 = ==()
    //     0xba947c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9480: tbnz            w0, #4, #0xba94a8
    // 0xba9484: ldur            x0, [fp, #-0x18]
    // 0xba9488: cmp             w0, NULL
    // 0xba948c: b.ne            #0xba9498
    // 0xba9490: r0 = Null
    //     0xba9490: mov             x0, NULL
    // 0xba9494: b               #0xba98f0
    // 0xba9498: LoadField: r1 = r0->field_b
    //     0xba9498: ldur            w1, [x0, #0xb]
    // 0xba949c: DecompressPointer r1
    //     0xba949c: add             x1, x1, HEAP, lsl #32
    // 0xba94a0: mov             x0, x1
    // 0xba94a4: b               #0xba98f0
    // 0xba94a8: ldur            x0, [fp, #-0x18]
    // 0xba94ac: r16 = "idgham-bilaghunnah"
    //     0xba94ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33108] "idgham-bilaghunnah"
    //     0xba94b0: ldr             x16, [x16, #0x108]
    // 0xba94b4: ldur            lr, [fp, #-0x10]
    // 0xba94b8: stp             lr, x16, [SP]
    // 0xba94bc: r0 = ==()
    //     0xba94bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba94c0: tbnz            w0, #4, #0xba94e8
    // 0xba94c4: ldur            x0, [fp, #-0x18]
    // 0xba94c8: cmp             w0, NULL
    // 0xba94cc: b.ne            #0xba94d8
    // 0xba94d0: r0 = Null
    //     0xba94d0: mov             x0, NULL
    // 0xba94d4: b               #0xba98f0
    // 0xba94d8: LoadField: r1 = r0->field_f
    //     0xba94d8: ldur            w1, [x0, #0xf]
    // 0xba94dc: DecompressPointer r1
    //     0xba94dc: add             x1, x1, HEAP, lsl #32
    // 0xba94e0: mov             x0, x1
    // 0xba94e4: b               #0xba98f0
    // 0xba94e8: ldur            x0, [fp, #-0x18]
    // 0xba94ec: r16 = "idgham-mutamatsilain"
    //     0xba94ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33110] "idgham-mutamatsilain"
    //     0xba94f0: ldr             x16, [x16, #0x110]
    // 0xba94f4: ldur            lr, [fp, #-0x10]
    // 0xba94f8: stp             lr, x16, [SP]
    // 0xba94fc: r0 = ==()
    //     0xba94fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9500: tbnz            w0, #4, #0xba9528
    // 0xba9504: ldur            x0, [fp, #-0x18]
    // 0xba9508: cmp             w0, NULL
    // 0xba950c: b.ne            #0xba9518
    // 0xba9510: r0 = Null
    //     0xba9510: mov             x0, NULL
    // 0xba9514: b               #0xba98f0
    // 0xba9518: LoadField: r1 = r0->field_f
    //     0xba9518: ldur            w1, [x0, #0xf]
    // 0xba951c: DecompressPointer r1
    //     0xba951c: add             x1, x1, HEAP, lsl #32
    // 0xba9520: mov             x0, x1
    // 0xba9524: b               #0xba98f0
    // 0xba9528: ldur            x0, [fp, #-0x18]
    // 0xba952c: r16 = "idgham-mutajanisain"
    //     0xba952c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33118] "idgham-mutajanisain"
    //     0xba9530: ldr             x16, [x16, #0x118]
    // 0xba9534: ldur            lr, [fp, #-0x10]
    // 0xba9538: stp             lr, x16, [SP]
    // 0xba953c: r0 = ==()
    //     0xba953c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9540: tbnz            w0, #4, #0xba9568
    // 0xba9544: ldur            x0, [fp, #-0x18]
    // 0xba9548: cmp             w0, NULL
    // 0xba954c: b.ne            #0xba9558
    // 0xba9550: r0 = Null
    //     0xba9550: mov             x0, NULL
    // 0xba9554: b               #0xba98f0
    // 0xba9558: LoadField: r1 = r0->field_f
    //     0xba9558: ldur            w1, [x0, #0xf]
    // 0xba955c: DecompressPointer r1
    //     0xba955c: add             x1, x1, HEAP, lsl #32
    // 0xba9560: mov             x0, x1
    // 0xba9564: b               #0xba98f0
    // 0xba9568: ldur            x0, [fp, #-0x18]
    // 0xba956c: r16 = "idgham-mutaqaribain"
    //     0xba956c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33120] "idgham-mutaqaribain"
    //     0xba9570: ldr             x16, [x16, #0x120]
    // 0xba9574: ldur            lr, [fp, #-0x10]
    // 0xba9578: stp             lr, x16, [SP]
    // 0xba957c: r0 = ==()
    //     0xba957c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9580: tbnz            w0, #4, #0xba95a8
    // 0xba9584: ldur            x0, [fp, #-0x18]
    // 0xba9588: cmp             w0, NULL
    // 0xba958c: b.ne            #0xba9598
    // 0xba9590: r0 = Null
    //     0xba9590: mov             x0, NULL
    // 0xba9594: b               #0xba98f0
    // 0xba9598: LoadField: r1 = r0->field_f
    //     0xba9598: ldur            w1, [x0, #0xf]
    // 0xba959c: DecompressPointer r1
    //     0xba959c: add             x1, x1, HEAP, lsl #32
    // 0xba95a0: mov             x0, x1
    // 0xba95a4: b               #0xba98f0
    // 0xba95a8: ldur            x0, [fp, #-0x18]
    // 0xba95ac: r16 = "idgham-mimi"
    //     0xba95ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33128] "idgham-mimi"
    //     0xba95b0: ldr             x16, [x16, #0x128]
    // 0xba95b4: ldur            lr, [fp, #-0x10]
    // 0xba95b8: stp             lr, x16, [SP]
    // 0xba95bc: r0 = ==()
    //     0xba95bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba95c0: tbnz            w0, #4, #0xba95e8
    // 0xba95c4: ldur            x0, [fp, #-0x18]
    // 0xba95c8: cmp             w0, NULL
    // 0xba95cc: b.ne            #0xba95d8
    // 0xba95d0: r0 = Null
    //     0xba95d0: mov             x0, NULL
    // 0xba95d4: b               #0xba98f0
    // 0xba95d8: LoadField: r1 = r0->field_b
    //     0xba95d8: ldur            w1, [x0, #0xb]
    // 0xba95dc: DecompressPointer r1
    //     0xba95dc: add             x1, x1, HEAP, lsl #32
    // 0xba95e0: mov             x0, x1
    // 0xba95e4: b               #0xba98f0
    // 0xba95e8: ldur            x0, [fp, #-0x18]
    // 0xba95ec: r16 = "ikhfa"
    //     0xba95ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33130] "ikhfa"
    //     0xba95f0: ldr             x16, [x16, #0x130]
    // 0xba95f4: ldur            lr, [fp, #-0x10]
    // 0xba95f8: stp             lr, x16, [SP]
    // 0xba95fc: r0 = ==()
    //     0xba95fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9600: tbnz            w0, #4, #0xba9628
    // 0xba9604: ldur            x0, [fp, #-0x18]
    // 0xba9608: cmp             w0, NULL
    // 0xba960c: b.ne            #0xba9618
    // 0xba9610: r0 = Null
    //     0xba9610: mov             x0, NULL
    // 0xba9614: b               #0xba98f0
    // 0xba9618: LoadField: r1 = r0->field_13
    //     0xba9618: ldur            w1, [x0, #0x13]
    // 0xba961c: DecompressPointer r1
    //     0xba961c: add             x1, x1, HEAP, lsl #32
    // 0xba9620: mov             x0, x1
    // 0xba9624: b               #0xba98f0
    // 0xba9628: ldur            x0, [fp, #-0x18]
    // 0xba962c: r16 = "ikhfa-syafawi"
    //     0xba962c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33138] "ikhfa-syafawi"
    //     0xba9630: ldr             x16, [x16, #0x138]
    // 0xba9634: ldur            lr, [fp, #-0x10]
    // 0xba9638: stp             lr, x16, [SP]
    // 0xba963c: r0 = ==()
    //     0xba963c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9640: tbnz            w0, #4, #0xba9668
    // 0xba9644: ldur            x0, [fp, #-0x18]
    // 0xba9648: cmp             w0, NULL
    // 0xba964c: b.ne            #0xba9658
    // 0xba9650: r0 = Null
    //     0xba9650: mov             x0, NULL
    // 0xba9654: b               #0xba98f0
    // 0xba9658: LoadField: r1 = r0->field_13
    //     0xba9658: ldur            w1, [x0, #0x13]
    // 0xba965c: DecompressPointer r1
    //     0xba965c: add             x1, x1, HEAP, lsl #32
    // 0xba9660: mov             x0, x1
    // 0xba9664: b               #0xba98f0
    // 0xba9668: ldur            x0, [fp, #-0x18]
    // 0xba966c: r16 = "iqlab"
    //     0xba966c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33140] "iqlab"
    //     0xba9670: ldr             x16, [x16, #0x140]
    // 0xba9674: ldur            lr, [fp, #-0x10]
    // 0xba9678: stp             lr, x16, [SP]
    // 0xba967c: r0 = ==()
    //     0xba967c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9680: tbnz            w0, #4, #0xba96a8
    // 0xba9684: ldur            x0, [fp, #-0x18]
    // 0xba9688: cmp             w0, NULL
    // 0xba968c: b.ne            #0xba9698
    // 0xba9690: r0 = Null
    //     0xba9690: mov             x0, NULL
    // 0xba9694: b               #0xba98f0
    // 0xba9698: LoadField: r1 = r0->field_1b
    //     0xba9698: ldur            w1, [x0, #0x1b]
    // 0xba969c: DecompressPointer r1
    //     0xba969c: add             x1, x1, HEAP, lsl #32
    // 0xba96a0: mov             x0, x1
    // 0xba96a4: b               #0xba98f0
    // 0xba96a8: ldur            x0, [fp, #-0x18]
    // 0xba96ac: r16 = "qalqalah"
    //     0xba96ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33148] "qalqalah"
    //     0xba96b0: ldr             x16, [x16, #0x148]
    // 0xba96b4: ldur            lr, [fp, #-0x10]
    // 0xba96b8: stp             lr, x16, [SP]
    // 0xba96bc: r0 = ==()
    //     0xba96bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba96c0: tbnz            w0, #4, #0xba96e8
    // 0xba96c4: ldur            x0, [fp, #-0x18]
    // 0xba96c8: cmp             w0, NULL
    // 0xba96cc: b.ne            #0xba96d8
    // 0xba96d0: r0 = Null
    //     0xba96d0: mov             x0, NULL
    // 0xba96d4: b               #0xba98f0
    // 0xba96d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xba96d8: ldur            w1, [x0, #0x17]
    // 0xba96dc: DecompressPointer r1
    //     0xba96dc: add             x1, x1, HEAP, lsl #32
    // 0xba96e0: mov             x0, x1
    // 0xba96e4: b               #0xba98f0
    // 0xba96e8: ldur            x0, [fp, #-0x18]
    // 0xba96ec: r16 = "madd-wajib"
    //     0xba96ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33150] "madd-wajib"
    //     0xba96f0: ldr             x16, [x16, #0x150]
    // 0xba96f4: ldur            lr, [fp, #-0x10]
    // 0xba96f8: stp             lr, x16, [SP]
    // 0xba96fc: r0 = ==()
    //     0xba96fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9700: tbnz            w0, #4, #0xba9728
    // 0xba9704: ldur            x0, [fp, #-0x18]
    // 0xba9708: cmp             w0, NULL
    // 0xba970c: b.ne            #0xba9718
    // 0xba9710: r0 = Null
    //     0xba9710: mov             x0, NULL
    // 0xba9714: b               #0xba98f0
    // 0xba9718: LoadField: r1 = r0->field_1b
    //     0xba9718: ldur            w1, [x0, #0x1b]
    // 0xba971c: DecompressPointer r1
    //     0xba971c: add             x1, x1, HEAP, lsl #32
    // 0xba9720: mov             x0, x1
    // 0xba9724: b               #0xba98f0
    // 0xba9728: ldur            x0, [fp, #-0x18]
    // 0xba972c: r16 = "madd-jaiz"
    //     0xba972c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33158] "madd-jaiz"
    //     0xba9730: ldr             x16, [x16, #0x158]
    // 0xba9734: ldur            lr, [fp, #-0x10]
    // 0xba9738: stp             lr, x16, [SP]
    // 0xba973c: r0 = ==()
    //     0xba973c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9740: tbnz            w0, #4, #0xba9768
    // 0xba9744: ldur            x0, [fp, #-0x18]
    // 0xba9748: cmp             w0, NULL
    // 0xba974c: b.ne            #0xba9758
    // 0xba9750: r0 = Null
    //     0xba9750: mov             x0, NULL
    // 0xba9754: b               #0xba98f0
    // 0xba9758: LoadField: r1 = r0->field_13
    //     0xba9758: ldur            w1, [x0, #0x13]
    // 0xba975c: DecompressPointer r1
    //     0xba975c: add             x1, x1, HEAP, lsl #32
    // 0xba9760: mov             x0, x1
    // 0xba9764: b               #0xba98f0
    // 0xba9768: ldur            x0, [fp, #-0x18]
    // 0xba976c: r16 = "madd-shilah-thawilah"
    //     0xba976c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33160] "madd-shilah-thawilah"
    //     0xba9770: ldr             x16, [x16, #0x160]
    // 0xba9774: ldur            lr, [fp, #-0x10]
    // 0xba9778: stp             lr, x16, [SP]
    // 0xba977c: r0 = ==()
    //     0xba977c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9780: tbnz            w0, #4, #0xba97a8
    // 0xba9784: ldur            x0, [fp, #-0x18]
    // 0xba9788: cmp             w0, NULL
    // 0xba978c: b.ne            #0xba9798
    // 0xba9790: r0 = Null
    //     0xba9790: mov             x0, NULL
    // 0xba9794: b               #0xba98f0
    // 0xba9798: LoadField: r1 = r0->field_13
    //     0xba9798: ldur            w1, [x0, #0x13]
    // 0xba979c: DecompressPointer r1
    //     0xba979c: add             x1, x1, HEAP, lsl #32
    // 0xba97a0: mov             x0, x1
    // 0xba97a4: b               #0xba98f0
    // 0xba97a8: ldur            x0, [fp, #-0x18]
    // 0xba97ac: r16 = "madd-farqi"
    //     0xba97ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33168] "madd-farqi"
    //     0xba97b0: ldr             x16, [x16, #0x168]
    // 0xba97b4: ldur            lr, [fp, #-0x10]
    // 0xba97b8: stp             lr, x16, [SP]
    // 0xba97bc: r0 = ==()
    //     0xba97bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba97c0: tbnz            w0, #4, #0xba97e8
    // 0xba97c4: ldur            x0, [fp, #-0x18]
    // 0xba97c8: cmp             w0, NULL
    // 0xba97cc: b.ne            #0xba97d8
    // 0xba97d0: r0 = Null
    //     0xba97d0: mov             x0, NULL
    // 0xba97d4: b               #0xba98f0
    // 0xba97d8: LoadField: r1 = r0->field_b
    //     0xba97d8: ldur            w1, [x0, #0xb]
    // 0xba97dc: DecompressPointer r1
    //     0xba97dc: add             x1, x1, HEAP, lsl #32
    // 0xba97e0: mov             x0, x1
    // 0xba97e4: b               #0xba98f0
    // 0xba97e8: ldur            x0, [fp, #-0x18]
    // 0xba97ec: r16 = "madd-lazim-mukhaffaf-kilmi"
    //     0xba97ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33170] "madd-lazim-mukhaffaf-kilmi"
    //     0xba97f0: ldr             x16, [x16, #0x170]
    // 0xba97f4: ldur            lr, [fp, #-0x10]
    // 0xba97f8: stp             lr, x16, [SP]
    // 0xba97fc: r0 = ==()
    //     0xba97fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9800: tbnz            w0, #4, #0xba9828
    // 0xba9804: ldur            x0, [fp, #-0x18]
    // 0xba9808: cmp             w0, NULL
    // 0xba980c: b.ne            #0xba9818
    // 0xba9810: r0 = Null
    //     0xba9810: mov             x0, NULL
    // 0xba9814: b               #0xba98f0
    // 0xba9818: LoadField: r1 = r0->field_b
    //     0xba9818: ldur            w1, [x0, #0xb]
    // 0xba981c: DecompressPointer r1
    //     0xba981c: add             x1, x1, HEAP, lsl #32
    // 0xba9820: mov             x0, x1
    // 0xba9824: b               #0xba98f0
    // 0xba9828: ldur            x0, [fp, #-0x18]
    // 0xba982c: r16 = "madd-lazim-mutsaqqal-kilmi"
    //     0xba982c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33178] "madd-lazim-mutsaqqal-kilmi"
    //     0xba9830: ldr             x16, [x16, #0x178]
    // 0xba9834: ldur            lr, [fp, #-0x10]
    // 0xba9838: stp             lr, x16, [SP]
    // 0xba983c: r0 = ==()
    //     0xba983c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9840: tbnz            w0, #4, #0xba9868
    // 0xba9844: ldur            x0, [fp, #-0x18]
    // 0xba9848: cmp             w0, NULL
    // 0xba984c: b.ne            #0xba9858
    // 0xba9850: r0 = Null
    //     0xba9850: mov             x0, NULL
    // 0xba9854: b               #0xba98f0
    // 0xba9858: LoadField: r1 = r0->field_b
    //     0xba9858: ldur            w1, [x0, #0xb]
    // 0xba985c: DecompressPointer r1
    //     0xba985c: add             x1, x1, HEAP, lsl #32
    // 0xba9860: mov             x0, x1
    // 0xba9864: b               #0xba98f0
    // 0xba9868: ldur            x0, [fp, #-0x18]
    // 0xba986c: r16 = "madd-lazim-harfi-musyabba"
    //     0xba986c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33180] "madd-lazim-harfi-musyabba"
    //     0xba9870: ldr             x16, [x16, #0x180]
    // 0xba9874: ldur            lr, [fp, #-0x10]
    // 0xba9878: stp             lr, x16, [SP]
    // 0xba987c: r0 = ==()
    //     0xba987c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba9880: tbnz            w0, #4, #0xba98a8
    // 0xba9884: ldur            x0, [fp, #-0x18]
    // 0xba9888: cmp             w0, NULL
    // 0xba988c: b.ne            #0xba9898
    // 0xba9890: r0 = Null
    //     0xba9890: mov             x0, NULL
    // 0xba9894: b               #0xba98f0
    // 0xba9898: LoadField: r1 = r0->field_b
    //     0xba9898: ldur            w1, [x0, #0xb]
    // 0xba989c: DecompressPointer r1
    //     0xba989c: add             x1, x1, HEAP, lsl #32
    // 0xba98a0: mov             x0, x1
    // 0xba98a4: b               #0xba98f0
    // 0xba98a8: ldur            x0, [fp, #-0x18]
    // 0xba98ac: r16 = "madd-lazim-mutsaqal-harfi"
    //     0xba98ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33188] "madd-lazim-mutsaqal-harfi"
    //     0xba98b0: ldr             x16, [x16, #0x188]
    // 0xba98b4: ldur            lr, [fp, #-0x10]
    // 0xba98b8: stp             lr, x16, [SP]
    // 0xba98bc: r0 = ==()
    //     0xba98bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xba98c0: tbnz            w0, #4, #0xba98ec
    // 0xba98c4: ldur            x1, [fp, #-0x18]
    // 0xba98c8: cmp             w1, NULL
    // 0xba98cc: b.ne            #0xba98d8
    // 0xba98d0: r1 = Null
    //     0xba98d0: mov             x1, NULL
    // 0xba98d4: b               #0xba98e4
    // 0xba98d8: LoadField: r2 = r1->field_b
    //     0xba98d8: ldur            w2, [x1, #0xb]
    // 0xba98dc: DecompressPointer r2
    //     0xba98dc: add             x2, x2, HEAP, lsl #32
    // 0xba98e0: mov             x1, x2
    // 0xba98e4: mov             x0, x1
    // 0xba98e8: b               #0xba98f0
    // 0xba98ec: r0 = Null
    //     0xba98ec: mov             x0, NULL
    // 0xba98f0: LeaveFrame
    //     0xba98f0: mov             SP, fp
    //     0xba98f4: ldp             fp, lr, [SP], #0x10
    // 0xba98f8: ret
    //     0xba98f8: ret             
    // 0xba98fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba98fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba9900: b               #0xba93c0
  }
}

// class id: 1644, size: 0x14, field offset: 0xc
class TajweedAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa66cc0, size: 0x428
    // 0xa66cc0: EnterFrame
    //     0xa66cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa66cc4: mov             fp, SP
    // 0xa66cc8: AllocStack(0x58)
    //     0xa66cc8: sub             SP, SP, #0x58
    // 0xa66ccc: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa66ccc: stur            x2, [fp, #-0x20]
    // 0xa66cd0: CheckStackOverflow
    //     0xa66cd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66cd4: cmp             SP, x16
    //     0xa66cd8: b.ls            #0xa670d0
    // 0xa66cdc: LoadField: r3 = r2->field_23
    //     0xa66cdc: ldur            x3, [x2, #0x23]
    // 0xa66ce0: add             x0, x3, #1
    // 0xa66ce4: LoadField: r1 = r2->field_1b
    //     0xa66ce4: ldur            x1, [x2, #0x1b]
    // 0xa66ce8: cmp             x0, x1
    // 0xa66cec: b.gt            #0xa67074
    // 0xa66cf0: LoadField: r4 = r2->field_7
    //     0xa66cf0: ldur            w4, [x2, #7]
    // 0xa66cf4: DecompressPointer r4
    //     0xa66cf4: add             x4, x4, HEAP, lsl #32
    // 0xa66cf8: stur            x4, [fp, #-0x18]
    // 0xa66cfc: StoreField: r2->field_23 = r0
    //     0xa66cfc: stur            x0, [x2, #0x23]
    // 0xa66d00: LoadField: r0 = r4->field_13
    //     0xa66d00: ldur            w0, [x4, #0x13]
    // 0xa66d04: r5 = LoadInt32Instr(r0)
    //     0xa66d04: sbfx            x5, x0, #1, #0x1f
    // 0xa66d08: mov             x0, x5
    // 0xa66d0c: mov             x1, x3
    // 0xa66d10: stur            x5, [fp, #-0x10]
    // 0xa66d14: cmp             x1, x0
    // 0xa66d18: b.hs            #0xa670d8
    // 0xa66d1c: LoadField: r0 = r4->field_7
    //     0xa66d1c: ldur            x0, [x4, #7]
    // 0xa66d20: ldrb            w1, [x0, x3]
    // 0xa66d24: stur            x1, [fp, #-8]
    // 0xa66d28: r16 = <int, dynamic>
    //     0xa66d28: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa66d2c: ldr             x16, [x16, #0xac0]
    // 0xa66d30: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa66d34: stp             lr, x16, [SP]
    // 0xa66d38: r0 = Map._fromLiteral()
    //     0xa66d38: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa66d3c: mov             x2, x0
    // 0xa66d40: stur            x2, [fp, #-0x38]
    // 0xa66d44: r6 = 0
    //     0xa66d44: movz            x6, #0
    // 0xa66d48: ldur            x3, [fp, #-0x20]
    // 0xa66d4c: ldur            x4, [fp, #-0x18]
    // 0xa66d50: ldur            x5, [fp, #-8]
    // 0xa66d54: stur            x6, [fp, #-0x30]
    // 0xa66d58: CheckStackOverflow
    //     0xa66d58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66d5c: cmp             SP, x16
    //     0xa66d60: b.ls            #0xa670dc
    // 0xa66d64: cmp             x6, x5
    // 0xa66d68: b.ge            #0xa66df4
    // 0xa66d6c: LoadField: r7 = r3->field_23
    //     0xa66d6c: ldur            x7, [x3, #0x23]
    // 0xa66d70: add             x0, x7, #1
    // 0xa66d74: LoadField: r1 = r3->field_1b
    //     0xa66d74: ldur            x1, [x3, #0x1b]
    // 0xa66d78: cmp             x0, x1
    // 0xa66d7c: b.gt            #0xa6709c
    // 0xa66d80: StoreField: r3->field_23 = r0
    //     0xa66d80: stur            x0, [x3, #0x23]
    // 0xa66d84: ldur            x0, [fp, #-0x10]
    // 0xa66d88: mov             x1, x7
    // 0xa66d8c: cmp             x1, x0
    // 0xa66d90: b.hs            #0xa670e4
    // 0xa66d94: LoadField: r0 = r4->field_7
    //     0xa66d94: ldur            x0, [x4, #7]
    // 0xa66d98: ldrb            w8, [x0, x7]
    // 0xa66d9c: mov             x1, x3
    // 0xa66da0: stur            x8, [fp, #-0x28]
    // 0xa66da4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa66da4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa66da8: r0 = read()
    //     0xa66da8: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa66dac: mov             x1, x0
    // 0xa66db0: ldur            x0, [fp, #-0x28]
    // 0xa66db4: lsl             x2, x0, #1
    // 0xa66db8: r16 = LoadInt32Instr(r2)
    //     0xa66db8: sbfx            x16, x2, #1, #0x1f
    // 0xa66dbc: r17 = 11601
    //     0xa66dbc: movz            x17, #0x2d51
    // 0xa66dc0: mul             x0, x16, x17
    // 0xa66dc4: umulh           x16, x16, x17
    // 0xa66dc8: eor             x0, x0, x16
    // 0xa66dcc: r0 = 0
    //     0xa66dcc: eor             x0, x0, x0, lsr #32
    // 0xa66dd0: ubfiz           x0, x0, #1, #0x1e
    // 0xa66dd4: r5 = LoadInt32Instr(r0)
    //     0xa66dd4: sbfx            x5, x0, #1, #0x1f
    // 0xa66dd8: mov             x3, x1
    // 0xa66ddc: ldur            x1, [fp, #-0x38]
    // 0xa66de0: r0 = _set()
    //     0xa66de0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa66de4: ldur            x0, [fp, #-0x30]
    // 0xa66de8: add             x6, x0, #1
    // 0xa66dec: ldur            x2, [fp, #-0x38]
    // 0xa66df0: b               #0xa66d48
    // 0xa66df4: mov             x0, x2
    // 0xa66df8: mov             x1, x0
    // 0xa66dfc: r2 = 0
    //     0xa66dfc: movz            x2, #0
    // 0xa66e00: r0 = _getValueOrData()
    //     0xa66e00: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66e04: ldur            x3, [fp, #-0x38]
    // 0xa66e08: LoadField: r1 = r3->field_f
    //     0xa66e08: ldur            w1, [x3, #0xf]
    // 0xa66e0c: DecompressPointer r1
    //     0xa66e0c: add             x1, x1, HEAP, lsl #32
    // 0xa66e10: cmp             w1, w0
    // 0xa66e14: b.ne            #0xa66e20
    // 0xa66e18: r4 = Null
    //     0xa66e18: mov             x4, NULL
    // 0xa66e1c: b               #0xa66e24
    // 0xa66e20: mov             x4, x0
    // 0xa66e24: mov             x0, x4
    // 0xa66e28: stur            x4, [fp, #-0x18]
    // 0xa66e2c: r2 = Null
    //     0xa66e2c: mov             x2, NULL
    // 0xa66e30: r1 = Null
    //     0xa66e30: mov             x1, NULL
    // 0xa66e34: r4 = 60
    //     0xa66e34: movz            x4, #0x3c
    // 0xa66e38: branchIfSmi(r0, 0xa66e44)
    //     0xa66e38: tbz             w0, #0, #0xa66e44
    // 0xa66e3c: r4 = LoadClassIdInstr(r0)
    //     0xa66e3c: ldur            x4, [x0, #-1]
    //     0xa66e40: ubfx            x4, x4, #0xc, #0x14
    // 0xa66e44: sub             x4, x4, #0x5e
    // 0xa66e48: cmp             x4, #1
    // 0xa66e4c: b.ls            #0xa66e60
    // 0xa66e50: r8 = String
    //     0xa66e50: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa66e54: r3 = Null
    //     0xa66e54: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ca8] Null
    //     0xa66e58: ldr             x3, [x3, #0xca8]
    // 0xa66e5c: r0 = String()
    //     0xa66e5c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa66e60: ldur            x1, [fp, #-0x38]
    // 0xa66e64: r2 = 2
    //     0xa66e64: movz            x2, #0x2
    // 0xa66e68: r0 = _getValueOrData()
    //     0xa66e68: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66e6c: ldur            x3, [fp, #-0x38]
    // 0xa66e70: LoadField: r1 = r3->field_f
    //     0xa66e70: ldur            w1, [x3, #0xf]
    // 0xa66e74: DecompressPointer r1
    //     0xa66e74: add             x1, x1, HEAP, lsl #32
    // 0xa66e78: cmp             w1, w0
    // 0xa66e7c: b.ne            #0xa66e88
    // 0xa66e80: r4 = Null
    //     0xa66e80: mov             x4, NULL
    // 0xa66e84: b               #0xa66e8c
    // 0xa66e88: mov             x4, x0
    // 0xa66e8c: mov             x0, x4
    // 0xa66e90: stur            x4, [fp, #-0x20]
    // 0xa66e94: r2 = Null
    //     0xa66e94: mov             x2, NULL
    // 0xa66e98: r1 = Null
    //     0xa66e98: mov             x1, NULL
    // 0xa66e9c: branchIfSmi(r0, 0xa66ec4)
    //     0xa66e9c: tbz             w0, #0, #0xa66ec4
    // 0xa66ea0: r4 = LoadClassIdInstr(r0)
    //     0xa66ea0: ldur            x4, [x0, #-1]
    //     0xa66ea4: ubfx            x4, x4, #0xc, #0x14
    // 0xa66ea8: sub             x4, x4, #0x3c
    // 0xa66eac: cmp             x4, #1
    // 0xa66eb0: b.ls            #0xa66ec4
    // 0xa66eb4: r8 = int
    //     0xa66eb4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66eb8: r3 = Null
    //     0xa66eb8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20cb8] Null
    //     0xa66ebc: ldr             x3, [x3, #0xcb8]
    // 0xa66ec0: r0 = int()
    //     0xa66ec0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66ec4: ldur            x1, [fp, #-0x38]
    // 0xa66ec8: r2 = 4
    //     0xa66ec8: movz            x2, #0x4
    // 0xa66ecc: r0 = _getValueOrData()
    //     0xa66ecc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66ed0: ldur            x3, [fp, #-0x38]
    // 0xa66ed4: LoadField: r1 = r3->field_f
    //     0xa66ed4: ldur            w1, [x3, #0xf]
    // 0xa66ed8: DecompressPointer r1
    //     0xa66ed8: add             x1, x1, HEAP, lsl #32
    // 0xa66edc: cmp             w1, w0
    // 0xa66ee0: b.ne            #0xa66eec
    // 0xa66ee4: r4 = Null
    //     0xa66ee4: mov             x4, NULL
    // 0xa66ee8: b               #0xa66ef0
    // 0xa66eec: mov             x4, x0
    // 0xa66ef0: mov             x0, x4
    // 0xa66ef4: stur            x4, [fp, #-0x40]
    // 0xa66ef8: r2 = Null
    //     0xa66ef8: mov             x2, NULL
    // 0xa66efc: r1 = Null
    //     0xa66efc: mov             x1, NULL
    // 0xa66f00: branchIfSmi(r0, 0xa66f28)
    //     0xa66f00: tbz             w0, #0, #0xa66f28
    // 0xa66f04: r4 = LoadClassIdInstr(r0)
    //     0xa66f04: ldur            x4, [x0, #-1]
    //     0xa66f08: ubfx            x4, x4, #0xc, #0x14
    // 0xa66f0c: sub             x4, x4, #0x3c
    // 0xa66f10: cmp             x4, #1
    // 0xa66f14: b.ls            #0xa66f28
    // 0xa66f18: r8 = int
    //     0xa66f18: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66f1c: r3 = Null
    //     0xa66f1c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20cc8] Null
    //     0xa66f20: ldr             x3, [x3, #0xcc8]
    // 0xa66f24: r0 = int()
    //     0xa66f24: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66f28: ldur            x1, [fp, #-0x38]
    // 0xa66f2c: r2 = 6
    //     0xa66f2c: movz            x2, #0x6
    // 0xa66f30: r0 = _getValueOrData()
    //     0xa66f30: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66f34: ldur            x3, [fp, #-0x38]
    // 0xa66f38: LoadField: r1 = r3->field_f
    //     0xa66f38: ldur            w1, [x3, #0xf]
    // 0xa66f3c: DecompressPointer r1
    //     0xa66f3c: add             x1, x1, HEAP, lsl #32
    // 0xa66f40: cmp             w1, w0
    // 0xa66f44: b.ne            #0xa66f50
    // 0xa66f48: r4 = Null
    //     0xa66f48: mov             x4, NULL
    // 0xa66f4c: b               #0xa66f54
    // 0xa66f50: mov             x4, x0
    // 0xa66f54: mov             x0, x4
    // 0xa66f58: stur            x4, [fp, #-0x48]
    // 0xa66f5c: r2 = Null
    //     0xa66f5c: mov             x2, NULL
    // 0xa66f60: r1 = Null
    //     0xa66f60: mov             x1, NULL
    // 0xa66f64: branchIfSmi(r0, 0xa66f8c)
    //     0xa66f64: tbz             w0, #0, #0xa66f8c
    // 0xa66f68: r4 = LoadClassIdInstr(r0)
    //     0xa66f68: ldur            x4, [x0, #-1]
    //     0xa66f6c: ubfx            x4, x4, #0xc, #0x14
    // 0xa66f70: sub             x4, x4, #0x3c
    // 0xa66f74: cmp             x4, #1
    // 0xa66f78: b.ls            #0xa66f8c
    // 0xa66f7c: r8 = int
    //     0xa66f7c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66f80: r3 = Null
    //     0xa66f80: add             x3, PP, #0x20, lsl #12  ; [pp+0x20cd8] Null
    //     0xa66f84: ldr             x3, [x3, #0xcd8]
    // 0xa66f88: r0 = int()
    //     0xa66f88: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa66f8c: ldur            x1, [fp, #-0x38]
    // 0xa66f90: r2 = 8
    //     0xa66f90: movz            x2, #0x8
    // 0xa66f94: r0 = _getValueOrData()
    //     0xa66f94: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa66f98: mov             x1, x0
    // 0xa66f9c: ldur            x0, [fp, #-0x38]
    // 0xa66fa0: LoadField: r2 = r0->field_f
    //     0xa66fa0: ldur            w2, [x0, #0xf]
    // 0xa66fa4: DecompressPointer r2
    //     0xa66fa4: add             x2, x2, HEAP, lsl #32
    // 0xa66fa8: cmp             w2, w1
    // 0xa66fac: b.ne            #0xa66fb8
    // 0xa66fb0: r7 = Null
    //     0xa66fb0: mov             x7, NULL
    // 0xa66fb4: b               #0xa66fbc
    // 0xa66fb8: mov             x7, x1
    // 0xa66fbc: ldur            x6, [fp, #-0x18]
    // 0xa66fc0: ldur            x5, [fp, #-0x20]
    // 0xa66fc4: ldur            x4, [fp, #-0x40]
    // 0xa66fc8: ldur            x3, [fp, #-0x48]
    // 0xa66fcc: mov             x0, x7
    // 0xa66fd0: stur            x7, [fp, #-0x38]
    // 0xa66fd4: r2 = Null
    //     0xa66fd4: mov             x2, NULL
    // 0xa66fd8: r1 = Null
    //     0xa66fd8: mov             x1, NULL
    // 0xa66fdc: branchIfSmi(r0, 0xa67004)
    //     0xa66fdc: tbz             w0, #0, #0xa67004
    // 0xa66fe0: r4 = LoadClassIdInstr(r0)
    //     0xa66fe0: ldur            x4, [x0, #-1]
    //     0xa66fe4: ubfx            x4, x4, #0xc, #0x14
    // 0xa66fe8: sub             x4, x4, #0x3c
    // 0xa66fec: cmp             x4, #1
    // 0xa66ff0: b.ls            #0xa67004
    // 0xa66ff4: r8 = int
    //     0xa66ff4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa66ff8: r3 = Null
    //     0xa66ff8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ce8] Null
    //     0xa66ffc: ldr             x3, [x3, #0xce8]
    // 0xa67000: r0 = int()
    //     0xa67000: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa67004: r0 = Tajweed()
    //     0xa67004: bl              #0x7c7ba4  ; AllocateTajweedStub -> Tajweed (size=0x2c)
    // 0xa67008: mov             x1, x0
    // 0xa6700c: ldur            x0, [fp, #-0x18]
    // 0xa67010: StoreField: r1->field_7 = r0
    //     0xa67010: stur            w0, [x1, #7]
    // 0xa67014: ldur            x0, [fp, #-0x20]
    // 0xa67018: r2 = LoadInt32Instr(r0)
    //     0xa67018: sbfx            x2, x0, #1, #0x1f
    //     0xa6701c: tbz             w0, #0, #0xa67024
    //     0xa67020: ldur            x2, [x0, #7]
    // 0xa67024: StoreField: r1->field_b = r2
    //     0xa67024: stur            x2, [x1, #0xb]
    // 0xa67028: ldur            x0, [fp, #-0x40]
    // 0xa6702c: r2 = LoadInt32Instr(r0)
    //     0xa6702c: sbfx            x2, x0, #1, #0x1f
    //     0xa67030: tbz             w0, #0, #0xa67038
    //     0xa67034: ldur            x2, [x0, #7]
    // 0xa67038: StoreField: r1->field_13 = r2
    //     0xa67038: stur            x2, [x1, #0x13]
    // 0xa6703c: ldur            x0, [fp, #-0x48]
    // 0xa67040: r2 = LoadInt32Instr(r0)
    //     0xa67040: sbfx            x2, x0, #1, #0x1f
    //     0xa67044: tbz             w0, #0, #0xa6704c
    //     0xa67048: ldur            x2, [x0, #7]
    // 0xa6704c: StoreField: r1->field_1b = r2
    //     0xa6704c: stur            x2, [x1, #0x1b]
    // 0xa67050: ldur            x0, [fp, #-0x38]
    // 0xa67054: r2 = LoadInt32Instr(r0)
    //     0xa67054: sbfx            x2, x0, #1, #0x1f
    //     0xa67058: tbz             w0, #0, #0xa67060
    //     0xa6705c: ldur            x2, [x0, #7]
    // 0xa67060: StoreField: r1->field_23 = r2
    //     0xa67060: stur            x2, [x1, #0x23]
    // 0xa67064: mov             x0, x1
    // 0xa67068: LeaveFrame
    //     0xa67068: mov             SP, fp
    //     0xa6706c: ldp             fp, lr, [SP], #0x10
    // 0xa67070: ret
    //     0xa67070: ret             
    // 0xa67074: r0 = RangeError()
    //     0xa67074: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa67078: mov             x1, x0
    // 0xa6707c: r0 = "Not enough bytes available."
    //     0xa6707c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa67080: ldr             x0, [x0, #0x8a8]
    // 0xa67084: ArrayStore: r1[0] = r0  ; List_4
    //     0xa67084: stur            w0, [x1, #0x17]
    // 0xa67088: r2 = false
    //     0xa67088: add             x2, NULL, #0x30  ; false
    // 0xa6708c: StoreField: r1->field_b = r2
    //     0xa6708c: stur            w2, [x1, #0xb]
    // 0xa67090: mov             x0, x1
    // 0xa67094: r0 = Throw()
    //     0xa67094: bl              #0xec04b8  ; ThrowStub
    // 0xa67098: brk             #0
    // 0xa6709c: r0 = "Not enough bytes available."
    //     0xa6709c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa670a0: ldr             x0, [x0, #0x8a8]
    // 0xa670a4: r2 = false
    //     0xa670a4: add             x2, NULL, #0x30  ; false
    // 0xa670a8: r0 = RangeError()
    //     0xa670a8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa670ac: mov             x1, x0
    // 0xa670b0: r0 = "Not enough bytes available."
    //     0xa670b0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa670b4: ldr             x0, [x0, #0x8a8]
    // 0xa670b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa670b8: stur            w0, [x1, #0x17]
    // 0xa670bc: r0 = false
    //     0xa670bc: add             x0, NULL, #0x30  ; false
    // 0xa670c0: StoreField: r1->field_b = r0
    //     0xa670c0: stur            w0, [x1, #0xb]
    // 0xa670c4: mov             x0, x1
    // 0xa670c8: r0 = Throw()
    //     0xa670c8: bl              #0xec04b8  ; ThrowStub
    // 0xa670cc: brk             #0
    // 0xa670d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa670d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa670d4: b               #0xa66cdc
    // 0xa670d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa670d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa670dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa670dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa670e0: b               #0xa66d64
    // 0xa670e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa670e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd8044, size: 0x3d8
    // 0xbd8044: EnterFrame
    //     0xbd8044: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8048: mov             fp, SP
    // 0xbd804c: AllocStack(0x28)
    //     0xbd804c: sub             SP, SP, #0x28
    // 0xbd8050: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd8050: mov             x4, x2
    //     0xbd8054: stur            x2, [fp, #-8]
    //     0xbd8058: stur            x3, [fp, #-0x10]
    // 0xbd805c: CheckStackOverflow
    //     0xbd805c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8060: cmp             SP, x16
    //     0xbd8064: b.ls            #0xbd83fc
    // 0xbd8068: mov             x0, x3
    // 0xbd806c: r2 = Null
    //     0xbd806c: mov             x2, NULL
    // 0xbd8070: r1 = Null
    //     0xbd8070: mov             x1, NULL
    // 0xbd8074: r4 = 60
    //     0xbd8074: movz            x4, #0x3c
    // 0xbd8078: branchIfSmi(r0, 0xbd8084)
    //     0xbd8078: tbz             w0, #0, #0xbd8084
    // 0xbd807c: r4 = LoadClassIdInstr(r0)
    //     0xbd807c: ldur            x4, [x0, #-1]
    //     0xbd8080: ubfx            x4, x4, #0xc, #0x14
    // 0xbd8084: cmp             x4, #0x461
    // 0xbd8088: b.eq            #0xbd80a0
    // 0xbd808c: r8 = Tajweed
    //     0xbd808c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b0d8] Type: Tajweed
    //     0xbd8090: ldr             x8, [x8, #0xd8]
    // 0xbd8094: r3 = Null
    //     0xbd8094: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0e0] Null
    //     0xbd8098: ldr             x3, [x3, #0xe0]
    // 0xbd809c: r0 = Tajweed()
    //     0xbd809c: bl              #0x7c7b84  ; IsType_Tajweed_Stub
    // 0xbd80a0: ldur            x0, [fp, #-8]
    // 0xbd80a4: LoadField: r1 = r0->field_b
    //     0xbd80a4: ldur            w1, [x0, #0xb]
    // 0xbd80a8: DecompressPointer r1
    //     0xbd80a8: add             x1, x1, HEAP, lsl #32
    // 0xbd80ac: LoadField: r2 = r1->field_13
    //     0xbd80ac: ldur            w2, [x1, #0x13]
    // 0xbd80b0: LoadField: r1 = r0->field_13
    //     0xbd80b0: ldur            x1, [x0, #0x13]
    // 0xbd80b4: r3 = LoadInt32Instr(r2)
    //     0xbd80b4: sbfx            x3, x2, #1, #0x1f
    // 0xbd80b8: sub             x2, x3, x1
    // 0xbd80bc: cmp             x2, #1
    // 0xbd80c0: b.ge            #0xbd80d0
    // 0xbd80c4: mov             x1, x0
    // 0xbd80c8: r2 = 1
    //     0xbd80c8: movz            x2, #0x1
    // 0xbd80cc: r0 = _increaseBufferSize()
    //     0xbd80cc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd80d0: ldur            x3, [fp, #-8]
    // 0xbd80d4: r2 = 5
    //     0xbd80d4: movz            x2, #0x5
    // 0xbd80d8: LoadField: r4 = r3->field_b
    //     0xbd80d8: ldur            w4, [x3, #0xb]
    // 0xbd80dc: DecompressPointer r4
    //     0xbd80dc: add             x4, x4, HEAP, lsl #32
    // 0xbd80e0: LoadField: r5 = r3->field_13
    //     0xbd80e0: ldur            x5, [x3, #0x13]
    // 0xbd80e4: add             x6, x5, #1
    // 0xbd80e8: StoreField: r3->field_13 = r6
    //     0xbd80e8: stur            x6, [x3, #0x13]
    // 0xbd80ec: LoadField: r0 = r4->field_13
    //     0xbd80ec: ldur            w0, [x4, #0x13]
    // 0xbd80f0: r7 = LoadInt32Instr(r0)
    //     0xbd80f0: sbfx            x7, x0, #1, #0x1f
    // 0xbd80f4: mov             x0, x7
    // 0xbd80f8: mov             x1, x5
    // 0xbd80fc: cmp             x1, x0
    // 0xbd8100: b.hs            #0xbd8404
    // 0xbd8104: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd8104: add             x0, x4, x5
    //     0xbd8108: strb            w2, [x0, #0x17]
    // 0xbd810c: sub             x0, x7, x6
    // 0xbd8110: cmp             x0, #1
    // 0xbd8114: b.ge            #0xbd8124
    // 0xbd8118: mov             x1, x3
    // 0xbd811c: r2 = 1
    //     0xbd811c: movz            x2, #0x1
    // 0xbd8120: r0 = _increaseBufferSize()
    //     0xbd8120: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8124: ldur            x2, [fp, #-8]
    // 0xbd8128: ldur            x3, [fp, #-0x10]
    // 0xbd812c: LoadField: r4 = r2->field_b
    //     0xbd812c: ldur            w4, [x2, #0xb]
    // 0xbd8130: DecompressPointer r4
    //     0xbd8130: add             x4, x4, HEAP, lsl #32
    // 0xbd8134: LoadField: r5 = r2->field_13
    //     0xbd8134: ldur            x5, [x2, #0x13]
    // 0xbd8138: add             x0, x5, #1
    // 0xbd813c: StoreField: r2->field_13 = r0
    //     0xbd813c: stur            x0, [x2, #0x13]
    // 0xbd8140: LoadField: r0 = r4->field_13
    //     0xbd8140: ldur            w0, [x4, #0x13]
    // 0xbd8144: r1 = LoadInt32Instr(r0)
    //     0xbd8144: sbfx            x1, x0, #1, #0x1f
    // 0xbd8148: mov             x0, x1
    // 0xbd814c: mov             x1, x5
    // 0xbd8150: cmp             x1, x0
    // 0xbd8154: b.hs            #0xbd8408
    // 0xbd8158: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd8158: add             x0, x4, x5
    //     0xbd815c: strb            wzr, [x0, #0x17]
    // 0xbd8160: LoadField: r0 = r3->field_7
    //     0xbd8160: ldur            w0, [x3, #7]
    // 0xbd8164: DecompressPointer r0
    //     0xbd8164: add             x0, x0, HEAP, lsl #32
    // 0xbd8168: r16 = <String>
    //     0xbd8168: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd816c: stp             x2, x16, [SP, #8]
    // 0xbd8170: str             x0, [SP]
    // 0xbd8174: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8174: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8178: r0 = write()
    //     0xbd8178: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd817c: ldur            x0, [fp, #-8]
    // 0xbd8180: LoadField: r1 = r0->field_b
    //     0xbd8180: ldur            w1, [x0, #0xb]
    // 0xbd8184: DecompressPointer r1
    //     0xbd8184: add             x1, x1, HEAP, lsl #32
    // 0xbd8188: LoadField: r2 = r1->field_13
    //     0xbd8188: ldur            w2, [x1, #0x13]
    // 0xbd818c: LoadField: r1 = r0->field_13
    //     0xbd818c: ldur            x1, [x0, #0x13]
    // 0xbd8190: r3 = LoadInt32Instr(r2)
    //     0xbd8190: sbfx            x3, x2, #1, #0x1f
    // 0xbd8194: sub             x2, x3, x1
    // 0xbd8198: cmp             x2, #1
    // 0xbd819c: b.ge            #0xbd81ac
    // 0xbd81a0: mov             x1, x0
    // 0xbd81a4: r2 = 1
    //     0xbd81a4: movz            x2, #0x1
    // 0xbd81a8: r0 = _increaseBufferSize()
    //     0xbd81a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd81ac: ldur            x2, [fp, #-8]
    // 0xbd81b0: ldur            x3, [fp, #-0x10]
    // 0xbd81b4: r4 = 1
    //     0xbd81b4: movz            x4, #0x1
    // 0xbd81b8: LoadField: r5 = r2->field_b
    //     0xbd81b8: ldur            w5, [x2, #0xb]
    // 0xbd81bc: DecompressPointer r5
    //     0xbd81bc: add             x5, x5, HEAP, lsl #32
    // 0xbd81c0: LoadField: r6 = r2->field_13
    //     0xbd81c0: ldur            x6, [x2, #0x13]
    // 0xbd81c4: add             x0, x6, #1
    // 0xbd81c8: StoreField: r2->field_13 = r0
    //     0xbd81c8: stur            x0, [x2, #0x13]
    // 0xbd81cc: LoadField: r0 = r5->field_13
    //     0xbd81cc: ldur            w0, [x5, #0x13]
    // 0xbd81d0: r1 = LoadInt32Instr(r0)
    //     0xbd81d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd81d4: mov             x0, x1
    // 0xbd81d8: mov             x1, x6
    // 0xbd81dc: cmp             x1, x0
    // 0xbd81e0: b.hs            #0xbd840c
    // 0xbd81e4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd81e4: add             x0, x5, x6
    //     0xbd81e8: strb            w4, [x0, #0x17]
    // 0xbd81ec: LoadField: r5 = r3->field_b
    //     0xbd81ec: ldur            x5, [x3, #0xb]
    // 0xbd81f0: r0 = BoxInt64Instr(r5)
    //     0xbd81f0: sbfiz           x0, x5, #1, #0x1f
    //     0xbd81f4: cmp             x5, x0, asr #1
    //     0xbd81f8: b.eq            #0xbd8204
    //     0xbd81fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8200: stur            x5, [x0, #7]
    // 0xbd8204: r16 = <int>
    //     0xbd8204: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8208: stp             x2, x16, [SP, #8]
    // 0xbd820c: str             x0, [SP]
    // 0xbd8210: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8210: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8214: r0 = write()
    //     0xbd8214: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8218: ldur            x0, [fp, #-8]
    // 0xbd821c: LoadField: r1 = r0->field_b
    //     0xbd821c: ldur            w1, [x0, #0xb]
    // 0xbd8220: DecompressPointer r1
    //     0xbd8220: add             x1, x1, HEAP, lsl #32
    // 0xbd8224: LoadField: r2 = r1->field_13
    //     0xbd8224: ldur            w2, [x1, #0x13]
    // 0xbd8228: LoadField: r1 = r0->field_13
    //     0xbd8228: ldur            x1, [x0, #0x13]
    // 0xbd822c: r3 = LoadInt32Instr(r2)
    //     0xbd822c: sbfx            x3, x2, #1, #0x1f
    // 0xbd8230: sub             x2, x3, x1
    // 0xbd8234: cmp             x2, #1
    // 0xbd8238: b.ge            #0xbd8248
    // 0xbd823c: mov             x1, x0
    // 0xbd8240: r2 = 1
    //     0xbd8240: movz            x2, #0x1
    // 0xbd8244: r0 = _increaseBufferSize()
    //     0xbd8244: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8248: ldur            x2, [fp, #-8]
    // 0xbd824c: ldur            x3, [fp, #-0x10]
    // 0xbd8250: r4 = 2
    //     0xbd8250: movz            x4, #0x2
    // 0xbd8254: LoadField: r5 = r2->field_b
    //     0xbd8254: ldur            w5, [x2, #0xb]
    // 0xbd8258: DecompressPointer r5
    //     0xbd8258: add             x5, x5, HEAP, lsl #32
    // 0xbd825c: LoadField: r6 = r2->field_13
    //     0xbd825c: ldur            x6, [x2, #0x13]
    // 0xbd8260: add             x0, x6, #1
    // 0xbd8264: StoreField: r2->field_13 = r0
    //     0xbd8264: stur            x0, [x2, #0x13]
    // 0xbd8268: LoadField: r0 = r5->field_13
    //     0xbd8268: ldur            w0, [x5, #0x13]
    // 0xbd826c: r1 = LoadInt32Instr(r0)
    //     0xbd826c: sbfx            x1, x0, #1, #0x1f
    // 0xbd8270: mov             x0, x1
    // 0xbd8274: mov             x1, x6
    // 0xbd8278: cmp             x1, x0
    // 0xbd827c: b.hs            #0xbd8410
    // 0xbd8280: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8280: add             x0, x5, x6
    //     0xbd8284: strb            w4, [x0, #0x17]
    // 0xbd8288: LoadField: r4 = r3->field_13
    //     0xbd8288: ldur            x4, [x3, #0x13]
    // 0xbd828c: r0 = BoxInt64Instr(r4)
    //     0xbd828c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd8290: cmp             x4, x0, asr #1
    //     0xbd8294: b.eq            #0xbd82a0
    //     0xbd8298: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd829c: stur            x4, [x0, #7]
    // 0xbd82a0: r16 = <int>
    //     0xbd82a0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd82a4: stp             x2, x16, [SP, #8]
    // 0xbd82a8: str             x0, [SP]
    // 0xbd82ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd82ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd82b0: r0 = write()
    //     0xbd82b0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd82b4: ldur            x0, [fp, #-8]
    // 0xbd82b8: LoadField: r1 = r0->field_b
    //     0xbd82b8: ldur            w1, [x0, #0xb]
    // 0xbd82bc: DecompressPointer r1
    //     0xbd82bc: add             x1, x1, HEAP, lsl #32
    // 0xbd82c0: LoadField: r2 = r1->field_13
    //     0xbd82c0: ldur            w2, [x1, #0x13]
    // 0xbd82c4: LoadField: r1 = r0->field_13
    //     0xbd82c4: ldur            x1, [x0, #0x13]
    // 0xbd82c8: r3 = LoadInt32Instr(r2)
    //     0xbd82c8: sbfx            x3, x2, #1, #0x1f
    // 0xbd82cc: sub             x2, x3, x1
    // 0xbd82d0: cmp             x2, #1
    // 0xbd82d4: b.ge            #0xbd82e4
    // 0xbd82d8: mov             x1, x0
    // 0xbd82dc: r2 = 1
    //     0xbd82dc: movz            x2, #0x1
    // 0xbd82e0: r0 = _increaseBufferSize()
    //     0xbd82e0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd82e4: ldur            x2, [fp, #-8]
    // 0xbd82e8: ldur            x3, [fp, #-0x10]
    // 0xbd82ec: r4 = 3
    //     0xbd82ec: movz            x4, #0x3
    // 0xbd82f0: LoadField: r5 = r2->field_b
    //     0xbd82f0: ldur            w5, [x2, #0xb]
    // 0xbd82f4: DecompressPointer r5
    //     0xbd82f4: add             x5, x5, HEAP, lsl #32
    // 0xbd82f8: LoadField: r6 = r2->field_13
    //     0xbd82f8: ldur            x6, [x2, #0x13]
    // 0xbd82fc: add             x0, x6, #1
    // 0xbd8300: StoreField: r2->field_13 = r0
    //     0xbd8300: stur            x0, [x2, #0x13]
    // 0xbd8304: LoadField: r0 = r5->field_13
    //     0xbd8304: ldur            w0, [x5, #0x13]
    // 0xbd8308: r1 = LoadInt32Instr(r0)
    //     0xbd8308: sbfx            x1, x0, #1, #0x1f
    // 0xbd830c: mov             x0, x1
    // 0xbd8310: mov             x1, x6
    // 0xbd8314: cmp             x1, x0
    // 0xbd8318: b.hs            #0xbd8414
    // 0xbd831c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd831c: add             x0, x5, x6
    //     0xbd8320: strb            w4, [x0, #0x17]
    // 0xbd8324: LoadField: r4 = r3->field_1b
    //     0xbd8324: ldur            x4, [x3, #0x1b]
    // 0xbd8328: r0 = BoxInt64Instr(r4)
    //     0xbd8328: sbfiz           x0, x4, #1, #0x1f
    //     0xbd832c: cmp             x4, x0, asr #1
    //     0xbd8330: b.eq            #0xbd833c
    //     0xbd8334: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8338: stur            x4, [x0, #7]
    // 0xbd833c: r16 = <int>
    //     0xbd833c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8340: stp             x2, x16, [SP, #8]
    // 0xbd8344: str             x0, [SP]
    // 0xbd8348: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8348: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd834c: r0 = write()
    //     0xbd834c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8350: ldur            x0, [fp, #-8]
    // 0xbd8354: LoadField: r1 = r0->field_b
    //     0xbd8354: ldur            w1, [x0, #0xb]
    // 0xbd8358: DecompressPointer r1
    //     0xbd8358: add             x1, x1, HEAP, lsl #32
    // 0xbd835c: LoadField: r2 = r1->field_13
    //     0xbd835c: ldur            w2, [x1, #0x13]
    // 0xbd8360: LoadField: r1 = r0->field_13
    //     0xbd8360: ldur            x1, [x0, #0x13]
    // 0xbd8364: r3 = LoadInt32Instr(r2)
    //     0xbd8364: sbfx            x3, x2, #1, #0x1f
    // 0xbd8368: sub             x2, x3, x1
    // 0xbd836c: cmp             x2, #1
    // 0xbd8370: b.ge            #0xbd8380
    // 0xbd8374: mov             x1, x0
    // 0xbd8378: r2 = 1
    //     0xbd8378: movz            x2, #0x1
    // 0xbd837c: r0 = _increaseBufferSize()
    //     0xbd837c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8380: ldur            x2, [fp, #-8]
    // 0xbd8384: ldur            x3, [fp, #-0x10]
    // 0xbd8388: r4 = 4
    //     0xbd8388: movz            x4, #0x4
    // 0xbd838c: LoadField: r5 = r2->field_b
    //     0xbd838c: ldur            w5, [x2, #0xb]
    // 0xbd8390: DecompressPointer r5
    //     0xbd8390: add             x5, x5, HEAP, lsl #32
    // 0xbd8394: LoadField: r6 = r2->field_13
    //     0xbd8394: ldur            x6, [x2, #0x13]
    // 0xbd8398: add             x0, x6, #1
    // 0xbd839c: StoreField: r2->field_13 = r0
    //     0xbd839c: stur            x0, [x2, #0x13]
    // 0xbd83a0: LoadField: r0 = r5->field_13
    //     0xbd83a0: ldur            w0, [x5, #0x13]
    // 0xbd83a4: r1 = LoadInt32Instr(r0)
    //     0xbd83a4: sbfx            x1, x0, #1, #0x1f
    // 0xbd83a8: mov             x0, x1
    // 0xbd83ac: mov             x1, x6
    // 0xbd83b0: cmp             x1, x0
    // 0xbd83b4: b.hs            #0xbd8418
    // 0xbd83b8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd83b8: add             x0, x5, x6
    //     0xbd83bc: strb            w4, [x0, #0x17]
    // 0xbd83c0: LoadField: r4 = r3->field_23
    //     0xbd83c0: ldur            x4, [x3, #0x23]
    // 0xbd83c4: r0 = BoxInt64Instr(r4)
    //     0xbd83c4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd83c8: cmp             x4, x0, asr #1
    //     0xbd83cc: b.eq            #0xbd83d8
    //     0xbd83d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd83d4: stur            x4, [x0, #7]
    // 0xbd83d8: r16 = <int>
    //     0xbd83d8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd83dc: stp             x2, x16, [SP, #8]
    // 0xbd83e0: str             x0, [SP]
    // 0xbd83e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd83e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd83e8: r0 = write()
    //     0xbd83e8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd83ec: r0 = Null
    //     0xbd83ec: mov             x0, NULL
    // 0xbd83f0: LeaveFrame
    //     0xbd83f0: mov             SP, fp
    //     0xbd83f4: ldp             fp, lr, [SP], #0x10
    // 0xbd83f8: ret
    //     0xbd83f8: ret             
    // 0xbd83fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd83fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8400: b               #0xbd8068
    // 0xbd8404: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8404: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd8408: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8408: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd840c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd840c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd8410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8410: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd8414: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8414: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd8418: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8418: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf03a8, size: 0x24
    // 0xbf03a8: r1 = 58
    //     0xbf03a8: movz            x1, #0x3a
    // 0xbf03ac: r16 = LoadInt32Instr(r1)
    //     0xbf03ac: sbfx            x16, x1, #1, #0x1f
    // 0xbf03b0: r17 = 11601
    //     0xbf03b0: movz            x17, #0x2d51
    // 0xbf03b4: mul             x0, x16, x17
    // 0xbf03b8: umulh           x16, x16, x17
    // 0xbf03bc: eor             x0, x0, x16
    // 0xbf03c0: r0 = 0
    //     0xbf03c0: eor             x0, x0, x0, lsr #32
    // 0xbf03c4: ubfiz           x0, x0, #1, #0x1e
    // 0xbf03c8: ret
    //     0xbf03c8: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7704c, size: 0x9c
    // 0xd7704c: EnterFrame
    //     0xd7704c: stp             fp, lr, [SP, #-0x10]!
    //     0xd77050: mov             fp, SP
    // 0xd77054: AllocStack(0x10)
    //     0xd77054: sub             SP, SP, #0x10
    // 0xd77058: CheckStackOverflow
    //     0xd77058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7705c: cmp             SP, x16
    //     0xd77060: b.ls            #0xd770e0
    // 0xd77064: ldr             x0, [fp, #0x10]
    // 0xd77068: cmp             w0, NULL
    // 0xd7706c: b.ne            #0xd77080
    // 0xd77070: r0 = false
    //     0xd77070: add             x0, NULL, #0x30  ; false
    // 0xd77074: LeaveFrame
    //     0xd77074: mov             SP, fp
    //     0xd77078: ldp             fp, lr, [SP], #0x10
    // 0xd7707c: ret
    //     0xd7707c: ret             
    // 0xd77080: ldr             x1, [fp, #0x18]
    // 0xd77084: cmp             w1, w0
    // 0xd77088: b.ne            #0xd77094
    // 0xd7708c: r0 = true
    //     0xd7708c: add             x0, NULL, #0x20  ; true
    // 0xd77090: b               #0xd770d4
    // 0xd77094: r1 = 60
    //     0xd77094: movz            x1, #0x3c
    // 0xd77098: branchIfSmi(r0, 0xd770a4)
    //     0xd77098: tbz             w0, #0, #0xd770a4
    // 0xd7709c: r1 = LoadClassIdInstr(r0)
    //     0xd7709c: ldur            x1, [x0, #-1]
    //     0xd770a0: ubfx            x1, x1, #0xc, #0x14
    // 0xd770a4: cmp             x1, #0x66c
    // 0xd770a8: b.ne            #0xd770d0
    // 0xd770ac: r16 = TajweedAdapter
    //     0xd770ac: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0d0] Type: TajweedAdapter
    //     0xd770b0: ldr             x16, [x16, #0xd0]
    // 0xd770b4: r30 = TajweedAdapter
    //     0xd770b4: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b0d0] Type: TajweedAdapter
    //     0xd770b8: ldr             lr, [lr, #0xd0]
    // 0xd770bc: stp             lr, x16, [SP]
    // 0xd770c0: r0 = ==()
    //     0xd770c0: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd770c4: tbnz            w0, #4, #0xd770d0
    // 0xd770c8: r0 = true
    //     0xd770c8: add             x0, NULL, #0x20  ; true
    // 0xd770cc: b               #0xd770d4
    // 0xd770d0: r0 = false
    //     0xd770d0: add             x0, NULL, #0x30  ; false
    // 0xd770d4: LeaveFrame
    //     0xd770d4: mov             SP, fp
    //     0xd770d8: ldp             fp, lr, [SP], #0x10
    // 0xd770dc: ret
    //     0xd770dc: ret             
    // 0xd770e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd770e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd770e4: b               #0xd77064
  }
}
