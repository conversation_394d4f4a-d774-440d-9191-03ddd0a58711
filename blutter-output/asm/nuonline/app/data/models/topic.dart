// lib: , url: package:nuonline/app/data/models/topic.dart

// class id: 1050057, size: 0x8
class :: {
}

// class id: 5573, size: 0x1c, field offset: 0x8
//   const constructor, 
class Topic extends Equatable {

  factory Topic Topic.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0xafbab0, size: 0x1d8
    // 0xafbab0: EnterFrame
    //     0xafbab0: stp             fp, lr, [SP, #-0x10]!
    //     0xafbab4: mov             fp, SP
    // 0xafbab8: AllocStack(0x28)
    //     0xafbab8: sub             SP, SP, #0x28
    // 0xafbabc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xafbabc: mov             x3, x2
    //     0xafbac0: stur            x2, [fp, #-8]
    // 0xafbac4: CheckStackOverflow
    //     0xafbac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafbac8: cmp             SP, x16
    //     0xafbacc: b.ls            #0xafbc80
    // 0xafbad0: r0 = LoadClassIdInstr(r3)
    //     0xafbad0: ldur            x0, [x3, #-1]
    //     0xafbad4: ubfx            x0, x0, #0xc, #0x14
    // 0xafbad8: mov             x1, x3
    // 0xafbadc: r2 = "id"
    //     0xafbadc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xafbae0: ldr             x2, [x2, #0x740]
    // 0xafbae4: r0 = GDT[cid_x0 + -0x114]()
    //     0xafbae4: sub             lr, x0, #0x114
    //     0xafbae8: ldr             lr, [x21, lr, lsl #3]
    //     0xafbaec: blr             lr
    // 0xafbaf0: mov             x3, x0
    // 0xafbaf4: r2 = Null
    //     0xafbaf4: mov             x2, NULL
    // 0xafbaf8: r1 = Null
    //     0xafbaf8: mov             x1, NULL
    // 0xafbafc: stur            x3, [fp, #-0x10]
    // 0xafbb00: branchIfSmi(r0, 0xafbb28)
    //     0xafbb00: tbz             w0, #0, #0xafbb28
    // 0xafbb04: r4 = LoadClassIdInstr(r0)
    //     0xafbb04: ldur            x4, [x0, #-1]
    //     0xafbb08: ubfx            x4, x4, #0xc, #0x14
    // 0xafbb0c: sub             x4, x4, #0x3c
    // 0xafbb10: cmp             x4, #1
    // 0xafbb14: b.ls            #0xafbb28
    // 0xafbb18: r8 = int
    //     0xafbb18: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xafbb1c: r3 = Null
    //     0xafbb1c: add             x3, PP, #0x29, lsl #12  ; [pp+0x299c0] Null
    //     0xafbb20: ldr             x3, [x3, #0x9c0]
    // 0xafbb24: r0 = int()
    //     0xafbb24: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xafbb28: ldur            x3, [fp, #-8]
    // 0xafbb2c: r0 = LoadClassIdInstr(r3)
    //     0xafbb2c: ldur            x0, [x3, #-1]
    //     0xafbb30: ubfx            x0, x0, #0xc, #0x14
    // 0xafbb34: mov             x1, x3
    // 0xafbb38: r2 = "title"
    //     0xafbb38: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xafbb3c: ldr             x2, [x2, #0x748]
    // 0xafbb40: r0 = GDT[cid_x0 + -0x114]()
    //     0xafbb40: sub             lr, x0, #0x114
    //     0xafbb44: ldr             lr, [x21, lr, lsl #3]
    //     0xafbb48: blr             lr
    // 0xafbb4c: mov             x3, x0
    // 0xafbb50: r2 = Null
    //     0xafbb50: mov             x2, NULL
    // 0xafbb54: r1 = Null
    //     0xafbb54: mov             x1, NULL
    // 0xafbb58: stur            x3, [fp, #-0x18]
    // 0xafbb5c: r4 = 60
    //     0xafbb5c: movz            x4, #0x3c
    // 0xafbb60: branchIfSmi(r0, 0xafbb6c)
    //     0xafbb60: tbz             w0, #0, #0xafbb6c
    // 0xafbb64: r4 = LoadClassIdInstr(r0)
    //     0xafbb64: ldur            x4, [x0, #-1]
    //     0xafbb68: ubfx            x4, x4, #0xc, #0x14
    // 0xafbb6c: sub             x4, x4, #0x5e
    // 0xafbb70: cmp             x4, #1
    // 0xafbb74: b.ls            #0xafbb88
    // 0xafbb78: r8 = String
    //     0xafbb78: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xafbb7c: r3 = Null
    //     0xafbb7c: add             x3, PP, #0x29, lsl #12  ; [pp+0x299d0] Null
    //     0xafbb80: ldr             x3, [x3, #0x9d0]
    // 0xafbb84: r0 = String()
    //     0xafbb84: bl              #0xed43b0  ; IsType_String_Stub
    // 0xafbb88: ldur            x3, [fp, #-8]
    // 0xafbb8c: r0 = LoadClassIdInstr(r3)
    //     0xafbb8c: ldur            x0, [x3, #-1]
    //     0xafbb90: ubfx            x0, x0, #0xc, #0x14
    // 0xafbb94: mov             x1, x3
    // 0xafbb98: r2 = "image"
    //     0xafbb98: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xafbb9c: ldr             x2, [x2, #0x520]
    // 0xafbba0: r0 = GDT[cid_x0 + -0x114]()
    //     0xafbba0: sub             lr, x0, #0x114
    //     0xafbba4: ldr             lr, [x21, lr, lsl #3]
    //     0xafbba8: blr             lr
    // 0xafbbac: mov             x3, x0
    // 0xafbbb0: r2 = Null
    //     0xafbbb0: mov             x2, NULL
    // 0xafbbb4: r1 = Null
    //     0xafbbb4: mov             x1, NULL
    // 0xafbbb8: stur            x3, [fp, #-0x20]
    // 0xafbbbc: r8 = Map<String, dynamic>
    //     0xafbbbc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xafbbc0: r3 = Null
    //     0xafbbc0: add             x3, PP, #0x29, lsl #12  ; [pp+0x299e0] Null
    //     0xafbbc4: ldr             x3, [x3, #0x9e0]
    // 0xafbbc8: r0 = Map<String, dynamic>()
    //     0xafbbc8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xafbbcc: ldur            x2, [fp, #-0x20]
    // 0xafbbd0: r1 = Null
    //     0xafbbd0: mov             x1, NULL
    // 0xafbbd4: r0 = Image.fromMap()
    //     0xafbbd4: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xafbbd8: mov             x3, x0
    // 0xafbbdc: ldur            x1, [fp, #-8]
    // 0xafbbe0: stur            x3, [fp, #-0x20]
    // 0xafbbe4: r0 = LoadClassIdInstr(r1)
    //     0xafbbe4: ldur            x0, [x1, #-1]
    //     0xafbbe8: ubfx            x0, x0, #0xc, #0x14
    // 0xafbbec: r2 = "slug"
    //     0xafbbec: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xafbbf0: ldr             x2, [x2, #0x9a8]
    // 0xafbbf4: r0 = GDT[cid_x0 + -0x114]()
    //     0xafbbf4: sub             lr, x0, #0x114
    //     0xafbbf8: ldr             lr, [x21, lr, lsl #3]
    //     0xafbbfc: blr             lr
    // 0xafbc00: mov             x3, x0
    // 0xafbc04: r2 = Null
    //     0xafbc04: mov             x2, NULL
    // 0xafbc08: r1 = Null
    //     0xafbc08: mov             x1, NULL
    // 0xafbc0c: stur            x3, [fp, #-8]
    // 0xafbc10: r4 = 60
    //     0xafbc10: movz            x4, #0x3c
    // 0xafbc14: branchIfSmi(r0, 0xafbc20)
    //     0xafbc14: tbz             w0, #0, #0xafbc20
    // 0xafbc18: r4 = LoadClassIdInstr(r0)
    //     0xafbc18: ldur            x4, [x0, #-1]
    //     0xafbc1c: ubfx            x4, x4, #0xc, #0x14
    // 0xafbc20: sub             x4, x4, #0x5e
    // 0xafbc24: cmp             x4, #1
    // 0xafbc28: b.ls            #0xafbc3c
    // 0xafbc2c: r8 = String?
    //     0xafbc2c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xafbc30: r3 = Null
    //     0xafbc30: add             x3, PP, #0x29, lsl #12  ; [pp+0x299f0] Null
    //     0xafbc34: ldr             x3, [x3, #0x9f0]
    // 0xafbc38: r0 = String?()
    //     0xafbc38: bl              #0x600324  ; IsType_String?_Stub
    // 0xafbc3c: ldur            x0, [fp, #-0x10]
    // 0xafbc40: r1 = LoadInt32Instr(r0)
    //     0xafbc40: sbfx            x1, x0, #1, #0x1f
    //     0xafbc44: tbz             w0, #0, #0xafbc4c
    //     0xafbc48: ldur            x1, [x0, #7]
    // 0xafbc4c: stur            x1, [fp, #-0x28]
    // 0xafbc50: r0 = Topic()
    //     0xafbc50: bl              #0xafbc88  ; AllocateTopicStub -> Topic (size=0x1c)
    // 0xafbc54: ldur            x1, [fp, #-0x28]
    // 0xafbc58: StoreField: r0->field_7 = r1
    //     0xafbc58: stur            x1, [x0, #7]
    // 0xafbc5c: ldur            x1, [fp, #-0x18]
    // 0xafbc60: StoreField: r0->field_f = r1
    //     0xafbc60: stur            w1, [x0, #0xf]
    // 0xafbc64: ldur            x1, [fp, #-0x20]
    // 0xafbc68: StoreField: r0->field_13 = r1
    //     0xafbc68: stur            w1, [x0, #0x13]
    // 0xafbc6c: ldur            x1, [fp, #-8]
    // 0xafbc70: ArrayStore: r0[0] = r1  ; List_4
    //     0xafbc70: stur            w1, [x0, #0x17]
    // 0xafbc74: LeaveFrame
    //     0xafbc74: mov             SP, fp
    //     0xafbc78: ldp             fp, lr, [SP], #0x10
    // 0xafbc7c: ret
    //     0xafbc7c: ret             
    // 0xafbc80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafbc80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafbc84: b               #0xafbad0
  }
  _ toMap(/* No info */) {
    // ** addr: 0xb59294, size: 0x120
    // 0xb59294: EnterFrame
    //     0xb59294: stp             fp, lr, [SP, #-0x10]!
    //     0xb59298: mov             fp, SP
    // 0xb5929c: AllocStack(0x20)
    //     0xb5929c: sub             SP, SP, #0x20
    // 0xb592a0: SetupParameters(Topic this /* r1 => r0, fp-0x8 */)
    //     0xb592a0: mov             x0, x1
    //     0xb592a4: stur            x1, [fp, #-8]
    // 0xb592a8: CheckStackOverflow
    //     0xb592a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb592ac: cmp             SP, x16
    //     0xb592b0: b.ls            #0xb593ac
    // 0xb592b4: r1 = Null
    //     0xb592b4: mov             x1, NULL
    // 0xb592b8: r2 = 16
    //     0xb592b8: movz            x2, #0x10
    // 0xb592bc: r0 = AllocateArray()
    //     0xb592bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb592c0: mov             x2, x0
    // 0xb592c4: stur            x2, [fp, #-0x10]
    // 0xb592c8: r16 = "id"
    //     0xb592c8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb592cc: ldr             x16, [x16, #0x740]
    // 0xb592d0: StoreField: r2->field_f = r16
    //     0xb592d0: stur            w16, [x2, #0xf]
    // 0xb592d4: ldur            x3, [fp, #-8]
    // 0xb592d8: LoadField: r4 = r3->field_7
    //     0xb592d8: ldur            x4, [x3, #7]
    // 0xb592dc: r0 = BoxInt64Instr(r4)
    //     0xb592dc: sbfiz           x0, x4, #1, #0x1f
    //     0xb592e0: cmp             x4, x0, asr #1
    //     0xb592e4: b.eq            #0xb592f0
    //     0xb592e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb592ec: stur            x4, [x0, #7]
    // 0xb592f0: StoreField: r2->field_13 = r0
    //     0xb592f0: stur            w0, [x2, #0x13]
    // 0xb592f4: r16 = "title"
    //     0xb592f4: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xb592f8: ldr             x16, [x16, #0x748]
    // 0xb592fc: ArrayStore: r2[0] = r16  ; List_4
    //     0xb592fc: stur            w16, [x2, #0x17]
    // 0xb59300: LoadField: r0 = r3->field_f
    //     0xb59300: ldur            w0, [x3, #0xf]
    // 0xb59304: DecompressPointer r0
    //     0xb59304: add             x0, x0, HEAP, lsl #32
    // 0xb59308: StoreField: r2->field_1b = r0
    //     0xb59308: stur            w0, [x2, #0x1b]
    // 0xb5930c: r16 = "image"
    //     0xb5930c: add             x16, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xb59310: ldr             x16, [x16, #0x520]
    // 0xb59314: StoreField: r2->field_1f = r16
    //     0xb59314: stur            w16, [x2, #0x1f]
    // 0xb59318: LoadField: r1 = r3->field_13
    //     0xb59318: ldur            w1, [x3, #0x13]
    // 0xb5931c: DecompressPointer r1
    //     0xb5931c: add             x1, x1, HEAP, lsl #32
    // 0xb59320: r0 = toMap()
    //     0xb59320: bl              #0x72c884  ; [package:nuonline/app/data/models/image.dart] Image::toMap
    // 0xb59324: ldur            x1, [fp, #-0x10]
    // 0xb59328: ArrayStore: r1[5] = r0  ; List_4
    //     0xb59328: add             x25, x1, #0x23
    //     0xb5932c: str             w0, [x25]
    //     0xb59330: tbz             w0, #0, #0xb5934c
    //     0xb59334: ldurb           w16, [x1, #-1]
    //     0xb59338: ldurb           w17, [x0, #-1]
    //     0xb5933c: and             x16, x17, x16, lsr #2
    //     0xb59340: tst             x16, HEAP, lsr #32
    //     0xb59344: b.eq            #0xb5934c
    //     0xb59348: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb5934c: ldur            x2, [fp, #-0x10]
    // 0xb59350: r16 = "slug"
    //     0xb59350: add             x16, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xb59354: ldr             x16, [x16, #0x9a8]
    // 0xb59358: StoreField: r2->field_27 = r16
    //     0xb59358: stur            w16, [x2, #0x27]
    // 0xb5935c: ldur            x0, [fp, #-8]
    // 0xb59360: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb59360: ldur            w1, [x0, #0x17]
    // 0xb59364: DecompressPointer r1
    //     0xb59364: add             x1, x1, HEAP, lsl #32
    // 0xb59368: mov             x0, x1
    // 0xb5936c: mov             x1, x2
    // 0xb59370: ArrayStore: r1[7] = r0  ; List_4
    //     0xb59370: add             x25, x1, #0x2b
    //     0xb59374: str             w0, [x25]
    //     0xb59378: tbz             w0, #0, #0xb59394
    //     0xb5937c: ldurb           w16, [x1, #-1]
    //     0xb59380: ldurb           w17, [x0, #-1]
    //     0xb59384: and             x16, x17, x16, lsr #2
    //     0xb59388: tst             x16, HEAP, lsr #32
    //     0xb5938c: b.eq            #0xb59394
    //     0xb59390: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb59394: r16 = <String, dynamic>
    //     0xb59394: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xb59398: stp             x2, x16, [SP]
    // 0xb5939c: r0 = Map._fromLiteral()
    //     0xb5939c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb593a0: LeaveFrame
    //     0xb593a0: mov             SP, fp
    //     0xb593a4: ldp             fp, lr, [SP], #0x10
    // 0xb593a8: ret
    //     0xb593a8: ret             
    // 0xb593ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb593ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb593b0: b               #0xb592b4
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe3e190, size: 0x188
    // 0xe3e190: EnterFrame
    //     0xe3e190: stp             fp, lr, [SP, #-0x10]!
    //     0xe3e194: mov             fp, SP
    // 0xe3e198: AllocStack(0x20)
    //     0xe3e198: sub             SP, SP, #0x20
    // 0xe3e19c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe3e19c: mov             x3, x1
    //     0xe3e1a0: stur            x1, [fp, #-8]
    // 0xe3e1a4: CheckStackOverflow
    //     0xe3e1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3e1a8: cmp             SP, x16
    //     0xe3e1ac: b.ls            #0xe3e310
    // 0xe3e1b0: mov             x0, x3
    // 0xe3e1b4: r2 = Null
    //     0xe3e1b4: mov             x2, NULL
    // 0xe3e1b8: r1 = Null
    //     0xe3e1b8: mov             x1, NULL
    // 0xe3e1bc: cmp             w0, NULL
    // 0xe3e1c0: b.eq            #0xe3e264
    // 0xe3e1c4: branchIfSmi(r0, 0xe3e264)
    //     0xe3e1c4: tbz             w0, #0, #0xe3e264
    // 0xe3e1c8: r3 = LoadClassIdInstr(r0)
    //     0xe3e1c8: ldur            x3, [x0, #-1]
    //     0xe3e1cc: ubfx            x3, x3, #0xc, #0x14
    // 0xe3e1d0: r17 = 6718
    //     0xe3e1d0: movz            x17, #0x1a3e
    // 0xe3e1d4: cmp             x3, x17
    // 0xe3e1d8: b.eq            #0xe3e26c
    // 0xe3e1dc: sub             x3, x3, #0x5a
    // 0xe3e1e0: cmp             x3, #2
    // 0xe3e1e4: b.ls            #0xe3e26c
    // 0xe3e1e8: r4 = LoadClassIdInstr(r0)
    //     0xe3e1e8: ldur            x4, [x0, #-1]
    //     0xe3e1ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe3e1f0: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe3e1f4: ldr             x3, [x3, #0x18]
    // 0xe3e1f8: ldr             x3, [x3, x4, lsl #3]
    // 0xe3e1fc: LoadField: r3 = r3->field_2b
    //     0xe3e1fc: ldur            w3, [x3, #0x2b]
    // 0xe3e200: DecompressPointer r3
    //     0xe3e200: add             x3, x3, HEAP, lsl #32
    // 0xe3e204: cmp             w3, NULL
    // 0xe3e208: b.eq            #0xe3e264
    // 0xe3e20c: LoadField: r3 = r3->field_f
    //     0xe3e20c: ldur            w3, [x3, #0xf]
    // 0xe3e210: lsr             x3, x3, #3
    // 0xe3e214: r17 = 6718
    //     0xe3e214: movz            x17, #0x1a3e
    // 0xe3e218: cmp             x3, x17
    // 0xe3e21c: b.eq            #0xe3e26c
    // 0xe3e220: r3 = SubtypeTestCache
    //     0xe3e220: add             x3, PP, #0x29, lsl #12  ; [pp+0x29988] SubtypeTestCache
    //     0xe3e224: ldr             x3, [x3, #0x988]
    // 0xe3e228: r30 = Subtype1TestCacheStub
    //     0xe3e228: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe3e22c: LoadField: r30 = r30->field_7
    //     0xe3e22c: ldur            lr, [lr, #7]
    // 0xe3e230: blr             lr
    // 0xe3e234: cmp             w7, NULL
    // 0xe3e238: b.eq            #0xe3e244
    // 0xe3e23c: tbnz            w7, #4, #0xe3e264
    // 0xe3e240: b               #0xe3e26c
    // 0xe3e244: r8 = List
    //     0xe3e244: add             x8, PP, #0x29, lsl #12  ; [pp+0x29990] Type: List
    //     0xe3e248: ldr             x8, [x8, #0x990]
    // 0xe3e24c: r3 = SubtypeTestCache
    //     0xe3e24c: add             x3, PP, #0x29, lsl #12  ; [pp+0x29998] SubtypeTestCache
    //     0xe3e250: ldr             x3, [x3, #0x998]
    // 0xe3e254: r30 = InstanceOfStub
    //     0xe3e254: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe3e258: LoadField: r30 = r30->field_7
    //     0xe3e258: ldur            lr, [lr, #7]
    // 0xe3e25c: blr             lr
    // 0xe3e260: b               #0xe3e270
    // 0xe3e264: r0 = false
    //     0xe3e264: add             x0, NULL, #0x30  ; false
    // 0xe3e268: b               #0xe3e270
    // 0xe3e26c: r0 = true
    //     0xe3e26c: add             x0, NULL, #0x20  ; true
    // 0xe3e270: tbnz            w0, #4, #0xe3e2f4
    // 0xe3e274: ldur            x0, [fp, #-8]
    // 0xe3e278: r1 = Function '<anonymous closure>': static.
    //     0xe3e278: add             x1, PP, #0x29, lsl #12  ; [pp+0x299a0] AnonymousClosure: static (0xe3e318), in [package:nuonline/app/data/models/topic.dart] Topic::fromResponse (0xe3e190)
    //     0xe3e27c: ldr             x1, [x1, #0x9a0]
    // 0xe3e280: r2 = Null
    //     0xe3e280: mov             x2, NULL
    // 0xe3e284: r0 = AllocateClosure()
    //     0xe3e284: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3e288: mov             x1, x0
    // 0xe3e28c: ldur            x0, [fp, #-8]
    // 0xe3e290: r2 = LoadClassIdInstr(r0)
    //     0xe3e290: ldur            x2, [x0, #-1]
    //     0xe3e294: ubfx            x2, x2, #0xc, #0x14
    // 0xe3e298: r16 = <Topic>
    //     0xe3e298: add             x16, PP, #0x29, lsl #12  ; [pp+0x299a8] TypeArguments: <Topic>
    //     0xe3e29c: ldr             x16, [x16, #0x9a8]
    // 0xe3e2a0: stp             x0, x16, [SP, #8]
    // 0xe3e2a4: str             x1, [SP]
    // 0xe3e2a8: mov             x0, x2
    // 0xe3e2ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe3e2ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe3e2b0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe3e2b0: movz            x17, #0xf28c
    //     0xe3e2b4: add             lr, x0, x17
    //     0xe3e2b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe3e2bc: blr             lr
    // 0xe3e2c0: r1 = LoadClassIdInstr(r0)
    //     0xe3e2c0: ldur            x1, [x0, #-1]
    //     0xe3e2c4: ubfx            x1, x1, #0xc, #0x14
    // 0xe3e2c8: mov             x16, x0
    // 0xe3e2cc: mov             x0, x1
    // 0xe3e2d0: mov             x1, x16
    // 0xe3e2d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe3e2d4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe3e2d8: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe3e2d8: movz            x17, #0xd889
    //     0xe3e2dc: add             lr, x0, x17
    //     0xe3e2e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe3e2e4: blr             lr
    // 0xe3e2e8: LeaveFrame
    //     0xe3e2e8: mov             SP, fp
    //     0xe3e2ec: ldp             fp, lr, [SP], #0x10
    // 0xe3e2f0: ret
    //     0xe3e2f0: ret             
    // 0xe3e2f4: r1 = <Topic>
    //     0xe3e2f4: add             x1, PP, #0x29, lsl #12  ; [pp+0x299a8] TypeArguments: <Topic>
    //     0xe3e2f8: ldr             x1, [x1, #0x9a8]
    // 0xe3e2fc: r2 = 0
    //     0xe3e2fc: movz            x2, #0
    // 0xe3e300: r0 = _GrowableList()
    //     0xe3e300: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe3e304: LeaveFrame
    //     0xe3e304: mov             SP, fp
    //     0xe3e308: ldp             fp, lr, [SP], #0x10
    // 0xe3e30c: ret
    //     0xe3e30c: ret             
    // 0xe3e310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3e310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3e314: b               #0xe3e1b0
  }
  [closure] static Topic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe3e318, size: 0x50
    // 0xe3e318: EnterFrame
    //     0xe3e318: stp             fp, lr, [SP, #-0x10]!
    //     0xe3e31c: mov             fp, SP
    // 0xe3e320: CheckStackOverflow
    //     0xe3e320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3e324: cmp             SP, x16
    //     0xe3e328: b.ls            #0xe3e360
    // 0xe3e32c: ldr             x0, [fp, #0x10]
    // 0xe3e330: r2 = Null
    //     0xe3e330: mov             x2, NULL
    // 0xe3e334: r1 = Null
    //     0xe3e334: mov             x1, NULL
    // 0xe3e338: r8 = Map<String, dynamic>
    //     0xe3e338: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe3e33c: r3 = Null
    //     0xe3e33c: add             x3, PP, #0x29, lsl #12  ; [pp+0x299b0] Null
    //     0xe3e340: ldr             x3, [x3, #0x9b0]
    // 0xe3e344: r0 = Map<String, dynamic>()
    //     0xe3e344: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe3e348: ldr             x2, [fp, #0x10]
    // 0xe3e34c: r1 = Null
    //     0xe3e34c: mov             x1, NULL
    // 0xe3e350: r0 = Topic.fromMap()
    //     0xe3e350: bl              #0xafbab0  ; [package:nuonline/app/data/models/topic.dart] Topic::Topic.fromMap
    // 0xe3e354: LeaveFrame
    //     0xe3e354: mov             SP, fp
    //     0xe3e358: ldp             fp, lr, [SP], #0x10
    // 0xe3e35c: ret
    //     0xe3e35c: ret             
    // 0xe3e360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3e360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3e364: b               #0xe3e32c
  }
}
