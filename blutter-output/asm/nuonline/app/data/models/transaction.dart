// lib: , url: package:nuonline/app/data/models/transaction.dart

// class id: 1050058, size: 0x8
class :: {
}

// class id: 1120, size: 0x4c, field offset: 0x8
class TransactionRequest<X0> extends Object {

  factory _ TransactionRequest.fromMap(/* No info */) {
    // ** addr: 0x8109ec, size: 0x5d4
    // 0x8109ec: EnterFrame
    //     0x8109ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8109f0: mov             fp, SP
    // 0x8109f4: AllocStack(0x78)
    //     0x8109f4: sub             SP, SP, #0x78
    // 0x8109f8: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8109f8: mov             x4, x1
    //     0x8109fc: mov             x3, x2
    //     0x810a00: stur            x1, [fp, #-8]
    //     0x810a04: stur            x2, [fp, #-0x10]
    // 0x810a08: CheckStackOverflow
    //     0x810a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x810a0c: cmp             SP, x16
    //     0x810a10: b.ls            #0x810fb8
    // 0x810a14: r0 = LoadClassIdInstr(r3)
    //     0x810a14: ldur            x0, [x3, #-1]
    //     0x810a18: ubfx            x0, x0, #0xc, #0x14
    // 0x810a1c: mov             x1, x3
    // 0x810a20: r2 = "method"
    //     0x810a20: add             x2, PP, #0xd, lsl #12  ; [pp+0xd660] "method"
    //     0x810a24: ldr             x2, [x2, #0x660]
    // 0x810a28: r0 = GDT[cid_x0 + -0x114]()
    //     0x810a28: sub             lr, x0, #0x114
    //     0x810a2c: ldr             lr, [x21, lr, lsl #3]
    //     0x810a30: blr             lr
    // 0x810a34: mov             x3, x0
    // 0x810a38: r2 = Null
    //     0x810a38: mov             x2, NULL
    // 0x810a3c: r1 = Null
    //     0x810a3c: mov             x1, NULL
    // 0x810a40: stur            x3, [fp, #-0x18]
    // 0x810a44: r4 = 60
    //     0x810a44: movz            x4, #0x3c
    // 0x810a48: branchIfSmi(r0, 0x810a54)
    //     0x810a48: tbz             w0, #0, #0x810a54
    // 0x810a4c: r4 = LoadClassIdInstr(r0)
    //     0x810a4c: ldur            x4, [x0, #-1]
    //     0x810a50: ubfx            x4, x4, #0xc, #0x14
    // 0x810a54: r17 = 6844
    //     0x810a54: movz            x17, #0x1abc
    // 0x810a58: cmp             x4, x17
    // 0x810a5c: b.eq            #0x810a74
    // 0x810a60: r8 = PaymentMethod
    //     0x810a60: add             x8, PP, #0x36, lsl #12  ; [pp+0x36190] Type: PaymentMethod
    //     0x810a64: ldr             x8, [x8, #0x190]
    // 0x810a68: r3 = Null
    //     0x810a68: add             x3, PP, #0x36, lsl #12  ; [pp+0x36198] Null
    //     0x810a6c: ldr             x3, [x3, #0x198]
    // 0x810a70: r0 = PaymentMethod()
    //     0x810a70: bl              #0x6ff620  ; IsType_PaymentMethod_Stub
    // 0x810a74: ldur            x3, [fp, #-0x10]
    // 0x810a78: r0 = LoadClassIdInstr(r3)
    //     0x810a78: ldur            x0, [x3, #-1]
    //     0x810a7c: ubfx            x0, x0, #0xc, #0x14
    // 0x810a80: mov             x1, x3
    // 0x810a84: r2 = "type"
    //     0x810a84: ldr             x2, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0x810a88: r0 = GDT[cid_x0 + -0x114]()
    //     0x810a88: sub             lr, x0, #0x114
    //     0x810a8c: ldr             lr, [x21, lr, lsl #3]
    //     0x810a90: blr             lr
    // 0x810a94: mov             x3, x0
    // 0x810a98: r2 = Null
    //     0x810a98: mov             x2, NULL
    // 0x810a9c: r1 = Null
    //     0x810a9c: mov             x1, NULL
    // 0x810aa0: stur            x3, [fp, #-0x20]
    // 0x810aa4: r4 = 60
    //     0x810aa4: movz            x4, #0x3c
    // 0x810aa8: branchIfSmi(r0, 0x810ab4)
    //     0x810aa8: tbz             w0, #0, #0x810ab4
    // 0x810aac: r4 = LoadClassIdInstr(r0)
    //     0x810aac: ldur            x4, [x0, #-1]
    //     0x810ab0: ubfx            x4, x4, #0xc, #0x14
    // 0x810ab4: r17 = 6845
    //     0x810ab4: movz            x17, #0x1abd
    // 0x810ab8: cmp             x4, x17
    // 0x810abc: b.eq            #0x810ad4
    // 0x810ac0: r8 = PaymentType
    //     0x810ac0: add             x8, PP, #0x36, lsl #12  ; [pp+0x361a8] Type: PaymentType
    //     0x810ac4: ldr             x8, [x8, #0x1a8]
    // 0x810ac8: r3 = Null
    //     0x810ac8: add             x3, PP, #0x36, lsl #12  ; [pp+0x361b0] Null
    //     0x810acc: ldr             x3, [x3, #0x1b0]
    // 0x810ad0: r0 = PaymentType()
    //     0x810ad0: bl              #0x72b9f0  ; IsType_PaymentType_Stub
    // 0x810ad4: ldur            x3, [fp, #-0x10]
    // 0x810ad8: r0 = LoadClassIdInstr(r3)
    //     0x810ad8: ldur            x0, [x3, #-1]
    //     0x810adc: ubfx            x0, x0, #0xc, #0x14
    // 0x810ae0: mov             x1, x3
    // 0x810ae4: r2 = "extra"
    //     0x810ae4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf640] "extra"
    //     0x810ae8: ldr             x2, [x2, #0x640]
    // 0x810aec: r0 = GDT[cid_x0 + -0x114]()
    //     0x810aec: sub             lr, x0, #0x114
    //     0x810af0: ldr             lr, [x21, lr, lsl #3]
    //     0x810af4: blr             lr
    // 0x810af8: ldur            x2, [fp, #-8]
    // 0x810afc: mov             x3, x0
    // 0x810b00: r1 = Null
    //     0x810b00: mov             x1, NULL
    // 0x810b04: stur            x3, [fp, #-0x28]
    // 0x810b08: cmp             w0, NULL
    // 0x810b0c: b.eq            #0x810b34
    // 0x810b10: cmp             w2, NULL
    // 0x810b14: b.eq            #0x810b34
    // 0x810b18: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x810b18: ldur            w4, [x2, #0x17]
    // 0x810b1c: DecompressPointer r4
    //     0x810b1c: add             x4, x4, HEAP, lsl #32
    // 0x810b20: r8 = X0?
    //     0x810b20: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x810b24: LoadField: r9 = r4->field_7
    //     0x810b24: ldur            x9, [x4, #7]
    // 0x810b28: r3 = Null
    //     0x810b28: add             x3, PP, #0x36, lsl #12  ; [pp+0x361c0] Null
    //     0x810b2c: ldr             x3, [x3, #0x1c0]
    // 0x810b30: blr             x9
    // 0x810b34: ldur            x3, [fp, #-0x10]
    // 0x810b38: r0 = LoadClassIdInstr(r3)
    //     0x810b38: ldur            x0, [x3, #-1]
    //     0x810b3c: ubfx            x0, x0, #0xc, #0x14
    // 0x810b40: mov             x1, x3
    // 0x810b44: r2 = "amount"
    //     0x810b44: add             x2, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x810b48: ldr             x2, [x2, #0xe0]
    // 0x810b4c: r0 = GDT[cid_x0 + -0x114]()
    //     0x810b4c: sub             lr, x0, #0x114
    //     0x810b50: ldr             lr, [x21, lr, lsl #3]
    //     0x810b54: blr             lr
    // 0x810b58: mov             x3, x0
    // 0x810b5c: r2 = Null
    //     0x810b5c: mov             x2, NULL
    // 0x810b60: r1 = Null
    //     0x810b60: mov             x1, NULL
    // 0x810b64: stur            x3, [fp, #-0x30]
    // 0x810b68: branchIfSmi(r0, 0x810b90)
    //     0x810b68: tbz             w0, #0, #0x810b90
    // 0x810b6c: r4 = LoadClassIdInstr(r0)
    //     0x810b6c: ldur            x4, [x0, #-1]
    //     0x810b70: ubfx            x4, x4, #0xc, #0x14
    // 0x810b74: sub             x4, x4, #0x3c
    // 0x810b78: cmp             x4, #2
    // 0x810b7c: b.ls            #0x810b90
    // 0x810b80: r8 = num
    //     0x810b80: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0x810b84: r3 = Null
    //     0x810b84: add             x3, PP, #0x36, lsl #12  ; [pp+0x361d0] Null
    //     0x810b88: ldr             x3, [x3, #0x1d0]
    // 0x810b8c: r0 = num()
    //     0x810b8c: bl              #0xed4df4  ; IsType_num_Stub
    // 0x810b90: ldur            x3, [fp, #-0x10]
    // 0x810b94: r0 = LoadClassIdInstr(r3)
    //     0x810b94: ldur            x0, [x3, #-1]
    //     0x810b98: ubfx            x0, x0, #0xc, #0x14
    // 0x810b9c: mov             x1, x3
    // 0x810ba0: r2 = "name"
    //     0x810ba0: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x810ba4: r0 = GDT[cid_x0 + -0x114]()
    //     0x810ba4: sub             lr, x0, #0x114
    //     0x810ba8: ldr             lr, [x21, lr, lsl #3]
    //     0x810bac: blr             lr
    // 0x810bb0: mov             x3, x0
    // 0x810bb4: r2 = Null
    //     0x810bb4: mov             x2, NULL
    // 0x810bb8: r1 = Null
    //     0x810bb8: mov             x1, NULL
    // 0x810bbc: stur            x3, [fp, #-0x38]
    // 0x810bc0: r4 = 60
    //     0x810bc0: movz            x4, #0x3c
    // 0x810bc4: branchIfSmi(r0, 0x810bd0)
    //     0x810bc4: tbz             w0, #0, #0x810bd0
    // 0x810bc8: r4 = LoadClassIdInstr(r0)
    //     0x810bc8: ldur            x4, [x0, #-1]
    //     0x810bcc: ubfx            x4, x4, #0xc, #0x14
    // 0x810bd0: sub             x4, x4, #0x5e
    // 0x810bd4: cmp             x4, #1
    // 0x810bd8: b.ls            #0x810bec
    // 0x810bdc: r8 = String
    //     0x810bdc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x810be0: r3 = Null
    //     0x810be0: add             x3, PP, #0x36, lsl #12  ; [pp+0x361e0] Null
    //     0x810be4: ldr             x3, [x3, #0x1e0]
    // 0x810be8: r0 = String()
    //     0x810be8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x810bec: ldur            x3, [fp, #-0x10]
    // 0x810bf0: r0 = LoadClassIdInstr(r3)
    //     0x810bf0: ldur            x0, [x3, #-1]
    //     0x810bf4: ubfx            x0, x0, #0xc, #0x14
    // 0x810bf8: mov             x1, x3
    // 0x810bfc: r2 = "is_anon"
    //     0x810bfc: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b128] "is_anon"
    //     0x810c00: ldr             x2, [x2, #0x128]
    // 0x810c04: r0 = GDT[cid_x0 + -0x114]()
    //     0x810c04: sub             lr, x0, #0x114
    //     0x810c08: ldr             lr, [x21, lr, lsl #3]
    //     0x810c0c: blr             lr
    // 0x810c10: mov             x3, x0
    // 0x810c14: r2 = Null
    //     0x810c14: mov             x2, NULL
    // 0x810c18: r1 = Null
    //     0x810c18: mov             x1, NULL
    // 0x810c1c: stur            x3, [fp, #-0x40]
    // 0x810c20: r4 = 60
    //     0x810c20: movz            x4, #0x3c
    // 0x810c24: branchIfSmi(r0, 0x810c30)
    //     0x810c24: tbz             w0, #0, #0x810c30
    // 0x810c28: r4 = LoadClassIdInstr(r0)
    //     0x810c28: ldur            x4, [x0, #-1]
    //     0x810c2c: ubfx            x4, x4, #0xc, #0x14
    // 0x810c30: cmp             x4, #0x3f
    // 0x810c34: b.eq            #0x810c48
    // 0x810c38: r8 = bool
    //     0x810c38: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x810c3c: r3 = Null
    //     0x810c3c: add             x3, PP, #0x36, lsl #12  ; [pp+0x361f0] Null
    //     0x810c40: ldr             x3, [x3, #0x1f0]
    // 0x810c44: r0 = bool()
    //     0x810c44: bl              #0xed4390  ; IsType_bool_Stub
    // 0x810c48: ldur            x3, [fp, #-0x10]
    // 0x810c4c: r0 = LoadClassIdInstr(r3)
    //     0x810c4c: ldur            x0, [x3, #-1]
    //     0x810c50: ubfx            x0, x0, #0xc, #0x14
    // 0x810c54: mov             x1, x3
    // 0x810c58: r2 = "email"
    //     0x810c58: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b178] "email"
    //     0x810c5c: ldr             x2, [x2, #0x178]
    // 0x810c60: r0 = GDT[cid_x0 + -0x114]()
    //     0x810c60: sub             lr, x0, #0x114
    //     0x810c64: ldr             lr, [x21, lr, lsl #3]
    //     0x810c68: blr             lr
    // 0x810c6c: mov             x3, x0
    // 0x810c70: r2 = Null
    //     0x810c70: mov             x2, NULL
    // 0x810c74: r1 = Null
    //     0x810c74: mov             x1, NULL
    // 0x810c78: stur            x3, [fp, #-0x48]
    // 0x810c7c: r4 = 60
    //     0x810c7c: movz            x4, #0x3c
    // 0x810c80: branchIfSmi(r0, 0x810c8c)
    //     0x810c80: tbz             w0, #0, #0x810c8c
    // 0x810c84: r4 = LoadClassIdInstr(r0)
    //     0x810c84: ldur            x4, [x0, #-1]
    //     0x810c88: ubfx            x4, x4, #0xc, #0x14
    // 0x810c8c: sub             x4, x4, #0x5e
    // 0x810c90: cmp             x4, #1
    // 0x810c94: b.ls            #0x810ca8
    // 0x810c98: r8 = String?
    //     0x810c98: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x810c9c: r3 = Null
    //     0x810c9c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36200] Null
    //     0x810ca0: ldr             x3, [x3, #0x200]
    // 0x810ca4: r0 = String?()
    //     0x810ca4: bl              #0x600324  ; IsType_String?_Stub
    // 0x810ca8: ldur            x3, [fp, #-0x10]
    // 0x810cac: r0 = LoadClassIdInstr(r3)
    //     0x810cac: ldur            x0, [x3, #-1]
    //     0x810cb0: ubfx            x0, x0, #0xc, #0x14
    // 0x810cb4: mov             x1, x3
    // 0x810cb8: r2 = "phone"
    //     0x810cb8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b160] "phone"
    //     0x810cbc: ldr             x2, [x2, #0x160]
    // 0x810cc0: r0 = GDT[cid_x0 + -0x114]()
    //     0x810cc0: sub             lr, x0, #0x114
    //     0x810cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x810cc8: blr             lr
    // 0x810ccc: mov             x3, x0
    // 0x810cd0: r2 = Null
    //     0x810cd0: mov             x2, NULL
    // 0x810cd4: r1 = Null
    //     0x810cd4: mov             x1, NULL
    // 0x810cd8: stur            x3, [fp, #-0x50]
    // 0x810cdc: r4 = 60
    //     0x810cdc: movz            x4, #0x3c
    // 0x810ce0: branchIfSmi(r0, 0x810cec)
    //     0x810ce0: tbz             w0, #0, #0x810cec
    // 0x810ce4: r4 = LoadClassIdInstr(r0)
    //     0x810ce4: ldur            x4, [x0, #-1]
    //     0x810ce8: ubfx            x4, x4, #0xc, #0x14
    // 0x810cec: sub             x4, x4, #0x5e
    // 0x810cf0: cmp             x4, #1
    // 0x810cf4: b.ls            #0x810d08
    // 0x810cf8: r8 = String?
    //     0x810cf8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x810cfc: r3 = Null
    //     0x810cfc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36210] Null
    //     0x810d00: ldr             x3, [x3, #0x210]
    // 0x810d04: r0 = String?()
    //     0x810d04: bl              #0x600324  ; IsType_String?_Stub
    // 0x810d08: ldur            x3, [fp, #-0x10]
    // 0x810d0c: r0 = LoadClassIdInstr(r3)
    //     0x810d0c: ldur            x0, [x3, #-1]
    //     0x810d10: ubfx            x0, x0, #0xc, #0x14
    // 0x810d14: mov             x1, x3
    // 0x810d18: r2 = "description"
    //     0x810d18: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x810d1c: ldr             x2, [x2, #0xd28]
    // 0x810d20: r0 = GDT[cid_x0 + -0x114]()
    //     0x810d20: sub             lr, x0, #0x114
    //     0x810d24: ldr             lr, [x21, lr, lsl #3]
    //     0x810d28: blr             lr
    // 0x810d2c: mov             x3, x0
    // 0x810d30: r2 = Null
    //     0x810d30: mov             x2, NULL
    // 0x810d34: r1 = Null
    //     0x810d34: mov             x1, NULL
    // 0x810d38: stur            x3, [fp, #-0x58]
    // 0x810d3c: r4 = 60
    //     0x810d3c: movz            x4, #0x3c
    // 0x810d40: branchIfSmi(r0, 0x810d4c)
    //     0x810d40: tbz             w0, #0, #0x810d4c
    // 0x810d44: r4 = LoadClassIdInstr(r0)
    //     0x810d44: ldur            x4, [x0, #-1]
    //     0x810d48: ubfx            x4, x4, #0xc, #0x14
    // 0x810d4c: sub             x4, x4, #0x5e
    // 0x810d50: cmp             x4, #1
    // 0x810d54: b.ls            #0x810d68
    // 0x810d58: r8 = String?
    //     0x810d58: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x810d5c: r3 = Null
    //     0x810d5c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36220] Null
    //     0x810d60: ldr             x3, [x3, #0x220]
    // 0x810d64: r0 = String?()
    //     0x810d64: bl              #0x600324  ; IsType_String?_Stub
    // 0x810d68: ldur            x3, [fp, #-0x10]
    // 0x810d6c: r0 = LoadClassIdInstr(r3)
    //     0x810d6c: ldur            x0, [x3, #-1]
    //     0x810d70: ubfx            x0, x0, #0xc, #0x14
    // 0x810d74: mov             x1, x3
    // 0x810d78: r2 = "campaign"
    //     0x810d78: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe68] "campaign"
    //     0x810d7c: ldr             x2, [x2, #0xe68]
    // 0x810d80: r0 = GDT[cid_x0 + -0x114]()
    //     0x810d80: sub             lr, x0, #0x114
    //     0x810d84: ldr             lr, [x21, lr, lsl #3]
    //     0x810d88: blr             lr
    // 0x810d8c: mov             x3, x0
    // 0x810d90: r2 = Null
    //     0x810d90: mov             x2, NULL
    // 0x810d94: r1 = Null
    //     0x810d94: mov             x1, NULL
    // 0x810d98: stur            x3, [fp, #-0x60]
    // 0x810d9c: r4 = 60
    //     0x810d9c: movz            x4, #0x3c
    // 0x810da0: branchIfSmi(r0, 0x810dac)
    //     0x810da0: tbz             w0, #0, #0x810dac
    // 0x810da4: r4 = LoadClassIdInstr(r0)
    //     0x810da4: ldur            x4, [x0, #-1]
    //     0x810da8: ubfx            x4, x4, #0xc, #0x14
    // 0x810dac: cmp             x4, #0x486
    // 0x810db0: b.eq            #0x810dc8
    // 0x810db4: r8 = Campaign?
    //     0x810db4: add             x8, PP, #0x36, lsl #12  ; [pp+0x36120] Type: Campaign?
    //     0x810db8: ldr             x8, [x8, #0x120]
    // 0x810dbc: r3 = Null
    //     0x810dbc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36230] Null
    //     0x810dc0: ldr             x3, [x3, #0x230]
    // 0x810dc4: r0 = DefaultNullableTypeTest()
    //     0x810dc4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x810dc8: ldur            x3, [fp, #-0x10]
    // 0x810dcc: r0 = LoadClassIdInstr(r3)
    //     0x810dcc: ldur            x0, [x3, #-1]
    //     0x810dd0: ubfx            x0, x0, #0xc, #0x14
    // 0x810dd4: mov             x1, x3
    // 0x810dd8: r2 = "province_id"
    //     0x810dd8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x810ddc: ldr             x2, [x2, #0x100]
    // 0x810de0: r0 = GDT[cid_x0 + -0x114]()
    //     0x810de0: sub             lr, x0, #0x114
    //     0x810de4: ldr             lr, [x21, lr, lsl #3]
    //     0x810de8: blr             lr
    // 0x810dec: mov             x3, x0
    // 0x810df0: r2 = Null
    //     0x810df0: mov             x2, NULL
    // 0x810df4: r1 = Null
    //     0x810df4: mov             x1, NULL
    // 0x810df8: stur            x3, [fp, #-0x68]
    // 0x810dfc: branchIfSmi(r0, 0x810e24)
    //     0x810dfc: tbz             w0, #0, #0x810e24
    // 0x810e00: r4 = LoadClassIdInstr(r0)
    //     0x810e00: ldur            x4, [x0, #-1]
    //     0x810e04: ubfx            x4, x4, #0xc, #0x14
    // 0x810e08: sub             x4, x4, #0x3c
    // 0x810e0c: cmp             x4, #1
    // 0x810e10: b.ls            #0x810e24
    // 0x810e14: r8 = int?
    //     0x810e14: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x810e18: r3 = Null
    //     0x810e18: add             x3, PP, #0x36, lsl #12  ; [pp+0x36240] Null
    //     0x810e1c: ldr             x3, [x3, #0x240]
    // 0x810e20: r0 = int?()
    //     0x810e20: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x810e24: ldur            x3, [fp, #-0x10]
    // 0x810e28: r0 = LoadClassIdInstr(r3)
    //     0x810e28: ldur            x0, [x3, #-1]
    //     0x810e2c: ubfx            x0, x0, #0xc, #0x14
    // 0x810e30: mov             x1, x3
    // 0x810e34: r2 = "regency_id"
    //     0x810e34: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x810e38: ldr             x2, [x2, #0x118]
    // 0x810e3c: r0 = GDT[cid_x0 + -0x114]()
    //     0x810e3c: sub             lr, x0, #0x114
    //     0x810e40: ldr             lr, [x21, lr, lsl #3]
    //     0x810e44: blr             lr
    // 0x810e48: mov             x3, x0
    // 0x810e4c: r2 = Null
    //     0x810e4c: mov             x2, NULL
    // 0x810e50: r1 = Null
    //     0x810e50: mov             x1, NULL
    // 0x810e54: stur            x3, [fp, #-0x70]
    // 0x810e58: branchIfSmi(r0, 0x810e80)
    //     0x810e58: tbz             w0, #0, #0x810e80
    // 0x810e5c: r4 = LoadClassIdInstr(r0)
    //     0x810e5c: ldur            x4, [x0, #-1]
    //     0x810e60: ubfx            x4, x4, #0xc, #0x14
    // 0x810e64: sub             x4, x4, #0x3c
    // 0x810e68: cmp             x4, #1
    // 0x810e6c: b.ls            #0x810e80
    // 0x810e70: r8 = int?
    //     0x810e70: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x810e74: r3 = Null
    //     0x810e74: add             x3, PP, #0x36, lsl #12  ; [pp+0x36250] Null
    //     0x810e78: ldr             x3, [x3, #0x250]
    // 0x810e7c: r0 = int?()
    //     0x810e7c: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x810e80: ldur            x3, [fp, #-0x10]
    // 0x810e84: r0 = LoadClassIdInstr(r3)
    //     0x810e84: ldur            x0, [x3, #-1]
    //     0x810e88: ubfx            x0, x0, #0xc, #0x14
    // 0x810e8c: mov             x1, x3
    // 0x810e90: r2 = "district_id"
    //     0x810e90: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x810e94: ldr             x2, [x2, #0xfe8]
    // 0x810e98: r0 = GDT[cid_x0 + -0x114]()
    //     0x810e98: sub             lr, x0, #0x114
    //     0x810e9c: ldr             lr, [x21, lr, lsl #3]
    //     0x810ea0: blr             lr
    // 0x810ea4: mov             x3, x0
    // 0x810ea8: r2 = Null
    //     0x810ea8: mov             x2, NULL
    // 0x810eac: r1 = Null
    //     0x810eac: mov             x1, NULL
    // 0x810eb0: stur            x3, [fp, #-0x78]
    // 0x810eb4: branchIfSmi(r0, 0x810edc)
    //     0x810eb4: tbz             w0, #0, #0x810edc
    // 0x810eb8: r4 = LoadClassIdInstr(r0)
    //     0x810eb8: ldur            x4, [x0, #-1]
    //     0x810ebc: ubfx            x4, x4, #0xc, #0x14
    // 0x810ec0: sub             x4, x4, #0x3c
    // 0x810ec4: cmp             x4, #1
    // 0x810ec8: b.ls            #0x810edc
    // 0x810ecc: r8 = int?
    //     0x810ecc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x810ed0: r3 = Null
    //     0x810ed0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36260] Null
    //     0x810ed4: ldr             x3, [x3, #0x260]
    // 0x810ed8: r0 = int?()
    //     0x810ed8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x810edc: ldur            x1, [fp, #-0x10]
    // 0x810ee0: r0 = LoadClassIdInstr(r1)
    //     0x810ee0: ldur            x0, [x1, #-1]
    //     0x810ee4: ubfx            x0, x0, #0xc, #0x14
    // 0x810ee8: r2 = "locality_id"
    //     0x810ee8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe70] "locality_id"
    //     0x810eec: ldr             x2, [x2, #0xe70]
    // 0x810ef0: r0 = GDT[cid_x0 + -0x114]()
    //     0x810ef0: sub             lr, x0, #0x114
    //     0x810ef4: ldr             lr, [x21, lr, lsl #3]
    //     0x810ef8: blr             lr
    // 0x810efc: mov             x3, x0
    // 0x810f00: r2 = Null
    //     0x810f00: mov             x2, NULL
    // 0x810f04: r1 = Null
    //     0x810f04: mov             x1, NULL
    // 0x810f08: stur            x3, [fp, #-0x10]
    // 0x810f0c: branchIfSmi(r0, 0x810f34)
    //     0x810f0c: tbz             w0, #0, #0x810f34
    // 0x810f10: r4 = LoadClassIdInstr(r0)
    //     0x810f10: ldur            x4, [x0, #-1]
    //     0x810f14: ubfx            x4, x4, #0xc, #0x14
    // 0x810f18: sub             x4, x4, #0x3c
    // 0x810f1c: cmp             x4, #1
    // 0x810f20: b.ls            #0x810f34
    // 0x810f24: r8 = int?
    //     0x810f24: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x810f28: r3 = Null
    //     0x810f28: add             x3, PP, #0x36, lsl #12  ; [pp+0x36270] Null
    //     0x810f2c: ldr             x3, [x3, #0x270]
    // 0x810f30: r0 = int?()
    //     0x810f30: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x810f34: ldur            x1, [fp, #-8]
    // 0x810f38: r0 = TransactionRequest()
    //     0x810f38: bl              #0x810fc0  ; AllocateTransactionRequestStub -> TransactionRequest<X0> (size=0x4c)
    // 0x810f3c: ldur            x1, [fp, #-0x18]
    // 0x810f40: StoreField: r0->field_b = r1
    //     0x810f40: stur            w1, [x0, #0xb]
    // 0x810f44: ldur            x1, [fp, #-0x20]
    // 0x810f48: StoreField: r0->field_f = r1
    //     0x810f48: stur            w1, [x0, #0xf]
    // 0x810f4c: ldur            x1, [fp, #-0x28]
    // 0x810f50: StoreField: r0->field_13 = r1
    //     0x810f50: stur            w1, [x0, #0x13]
    // 0x810f54: ldur            x1, [fp, #-0x30]
    // 0x810f58: ArrayStore: r0[0] = r1  ; List_4
    //     0x810f58: stur            w1, [x0, #0x17]
    // 0x810f5c: ldur            x1, [fp, #-0x38]
    // 0x810f60: StoreField: r0->field_1b = r1
    //     0x810f60: stur            w1, [x0, #0x1b]
    // 0x810f64: ldur            x1, [fp, #-0x48]
    // 0x810f68: StoreField: r0->field_27 = r1
    //     0x810f68: stur            w1, [x0, #0x27]
    // 0x810f6c: ldur            x1, [fp, #-0x50]
    // 0x810f70: StoreField: r0->field_2b = r1
    //     0x810f70: stur            w1, [x0, #0x2b]
    // 0x810f74: ldur            x1, [fp, #-0x58]
    // 0x810f78: StoreField: r0->field_2f = r1
    //     0x810f78: stur            w1, [x0, #0x2f]
    // 0x810f7c: ldur            x1, [fp, #-0x40]
    // 0x810f80: StoreField: r0->field_33 = r1
    //     0x810f80: stur            w1, [x0, #0x33]
    // 0x810f84: ldur            x1, [fp, #-0x60]
    // 0x810f88: StoreField: r0->field_37 = r1
    //     0x810f88: stur            w1, [x0, #0x37]
    // 0x810f8c: ldur            x1, [fp, #-0x68]
    // 0x810f90: StoreField: r0->field_3b = r1
    //     0x810f90: stur            w1, [x0, #0x3b]
    // 0x810f94: ldur            x1, [fp, #-0x70]
    // 0x810f98: StoreField: r0->field_3f = r1
    //     0x810f98: stur            w1, [x0, #0x3f]
    // 0x810f9c: ldur            x1, [fp, #-0x78]
    // 0x810fa0: StoreField: r0->field_43 = r1
    //     0x810fa0: stur            w1, [x0, #0x43]
    // 0x810fa4: ldur            x1, [fp, #-0x10]
    // 0x810fa8: StoreField: r0->field_47 = r1
    //     0x810fa8: stur            w1, [x0, #0x47]
    // 0x810fac: LeaveFrame
    //     0x810fac: mov             SP, fp
    //     0x810fb0: ldp             fp, lr, [SP], #0x10
    // 0x810fb4: ret
    //     0x810fb4: ret             
    // 0x810fb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x810fb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x810fbc: b               #0x810a14
  }
  _ toMap(/* No info */) {
    // ** addr: 0xae5444, size: 0x1c8
    // 0xae5444: EnterFrame
    //     0xae5444: stp             fp, lr, [SP, #-0x10]!
    //     0xae5448: mov             fp, SP
    // 0xae544c: AllocStack(0x18)
    //     0xae544c: sub             SP, SP, #0x18
    // 0xae5450: SetupParameters(TransactionRequest<X0> this /* r1 => r0, fp-0x8 */)
    //     0xae5450: mov             x0, x1
    //     0xae5454: stur            x1, [fp, #-8]
    // 0xae5458: CheckStackOverflow
    //     0xae5458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae545c: cmp             SP, x16
    //     0xae5460: b.ls            #0xae5604
    // 0xae5464: r1 = Null
    //     0xae5464: mov             x1, NULL
    // 0xae5468: r2 = 64
    //     0xae5468: movz            x2, #0x40
    // 0xae546c: r0 = AllocateArray()
    //     0xae546c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xae5470: r16 = "method"
    //     0xae5470: add             x16, PP, #0xd, lsl #12  ; [pp+0xd660] "method"
    //     0xae5474: ldr             x16, [x16, #0x660]
    // 0xae5478: StoreField: r0->field_f = r16
    //     0xae5478: stur            w16, [x0, #0xf]
    // 0xae547c: ldur            x1, [fp, #-8]
    // 0xae5480: LoadField: r2 = r1->field_b
    //     0xae5480: ldur            w2, [x1, #0xb]
    // 0xae5484: DecompressPointer r2
    //     0xae5484: add             x2, x2, HEAP, lsl #32
    // 0xae5488: StoreField: r0->field_13 = r2
    //     0xae5488: stur            w2, [x0, #0x13]
    // 0xae548c: r16 = "type"
    //     0xae548c: ldr             x16, [PP, #0x3020]  ; [pp+0x3020] "type"
    // 0xae5490: ArrayStore: r0[0] = r16  ; List_4
    //     0xae5490: stur            w16, [x0, #0x17]
    // 0xae5494: LoadField: r2 = r1->field_f
    //     0xae5494: ldur            w2, [x1, #0xf]
    // 0xae5498: DecompressPointer r2
    //     0xae5498: add             x2, x2, HEAP, lsl #32
    // 0xae549c: StoreField: r0->field_1b = r2
    //     0xae549c: stur            w2, [x0, #0x1b]
    // 0xae54a0: r16 = "extra"
    //     0xae54a0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf640] "extra"
    //     0xae54a4: ldr             x16, [x16, #0x640]
    // 0xae54a8: StoreField: r0->field_1f = r16
    //     0xae54a8: stur            w16, [x0, #0x1f]
    // 0xae54ac: LoadField: r2 = r1->field_13
    //     0xae54ac: ldur            w2, [x1, #0x13]
    // 0xae54b0: DecompressPointer r2
    //     0xae54b0: add             x2, x2, HEAP, lsl #32
    // 0xae54b4: StoreField: r0->field_23 = r2
    //     0xae54b4: stur            w2, [x0, #0x23]
    // 0xae54b8: r16 = "amount"
    //     0xae54b8: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0xae54bc: ldr             x16, [x16, #0xe0]
    // 0xae54c0: StoreField: r0->field_27 = r16
    //     0xae54c0: stur            w16, [x0, #0x27]
    // 0xae54c4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae54c4: ldur            w2, [x1, #0x17]
    // 0xae54c8: DecompressPointer r2
    //     0xae54c8: add             x2, x2, HEAP, lsl #32
    // 0xae54cc: StoreField: r0->field_2b = r2
    //     0xae54cc: stur            w2, [x0, #0x2b]
    // 0xae54d0: r16 = "name"
    //     0xae54d0: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xae54d4: StoreField: r0->field_2f = r16
    //     0xae54d4: stur            w16, [x0, #0x2f]
    // 0xae54d8: LoadField: r2 = r1->field_1b
    //     0xae54d8: ldur            w2, [x1, #0x1b]
    // 0xae54dc: DecompressPointer r2
    //     0xae54dc: add             x2, x2, HEAP, lsl #32
    // 0xae54e0: StoreField: r0->field_33 = r2
    //     0xae54e0: stur            w2, [x0, #0x33]
    // 0xae54e4: r16 = "names"
    //     0xae54e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe60] "names"
    //     0xae54e8: ldr             x16, [x16, #0xe60]
    // 0xae54ec: StoreField: r0->field_37 = r16
    //     0xae54ec: stur            w16, [x0, #0x37]
    // 0xae54f0: LoadField: r2 = r1->field_1f
    //     0xae54f0: ldur            w2, [x1, #0x1f]
    // 0xae54f4: DecompressPointer r2
    //     0xae54f4: add             x2, x2, HEAP, lsl #32
    // 0xae54f8: StoreField: r0->field_3b = r2
    //     0xae54f8: stur            w2, [x0, #0x3b]
    // 0xae54fc: r16 = "days"
    //     0xae54fc: add             x16, PP, #8, lsl #12  ; [pp+0x8e70] "days"
    //     0xae5500: ldr             x16, [x16, #0xe70]
    // 0xae5504: StoreField: r0->field_3f = r16
    //     0xae5504: stur            w16, [x0, #0x3f]
    // 0xae5508: LoadField: r2 = r1->field_23
    //     0xae5508: ldur            w2, [x1, #0x23]
    // 0xae550c: DecompressPointer r2
    //     0xae550c: add             x2, x2, HEAP, lsl #32
    // 0xae5510: StoreField: r0->field_43 = r2
    //     0xae5510: stur            w2, [x0, #0x43]
    // 0xae5514: r16 = "is_anon"
    //     0xae5514: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b128] "is_anon"
    //     0xae5518: ldr             x16, [x16, #0x128]
    // 0xae551c: StoreField: r0->field_47 = r16
    //     0xae551c: stur            w16, [x0, #0x47]
    // 0xae5520: LoadField: r2 = r1->field_33
    //     0xae5520: ldur            w2, [x1, #0x33]
    // 0xae5524: DecompressPointer r2
    //     0xae5524: add             x2, x2, HEAP, lsl #32
    // 0xae5528: StoreField: r0->field_4b = r2
    //     0xae5528: stur            w2, [x0, #0x4b]
    // 0xae552c: r16 = "phone"
    //     0xae552c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b160] "phone"
    //     0xae5530: ldr             x16, [x16, #0x160]
    // 0xae5534: StoreField: r0->field_4f = r16
    //     0xae5534: stur            w16, [x0, #0x4f]
    // 0xae5538: LoadField: r2 = r1->field_2b
    //     0xae5538: ldur            w2, [x1, #0x2b]
    // 0xae553c: DecompressPointer r2
    //     0xae553c: add             x2, x2, HEAP, lsl #32
    // 0xae5540: StoreField: r0->field_53 = r2
    //     0xae5540: stur            w2, [x0, #0x53]
    // 0xae5544: r16 = "email"
    //     0xae5544: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b178] "email"
    //     0xae5548: ldr             x16, [x16, #0x178]
    // 0xae554c: StoreField: r0->field_57 = r16
    //     0xae554c: stur            w16, [x0, #0x57]
    // 0xae5550: LoadField: r2 = r1->field_27
    //     0xae5550: ldur            w2, [x1, #0x27]
    // 0xae5554: DecompressPointer r2
    //     0xae5554: add             x2, x2, HEAP, lsl #32
    // 0xae5558: StoreField: r0->field_5b = r2
    //     0xae5558: stur            w2, [x0, #0x5b]
    // 0xae555c: r16 = "description"
    //     0xae555c: add             x16, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0xae5560: ldr             x16, [x16, #0xd28]
    // 0xae5564: StoreField: r0->field_5f = r16
    //     0xae5564: stur            w16, [x0, #0x5f]
    // 0xae5568: LoadField: r2 = r1->field_2f
    //     0xae5568: ldur            w2, [x1, #0x2f]
    // 0xae556c: DecompressPointer r2
    //     0xae556c: add             x2, x2, HEAP, lsl #32
    // 0xae5570: StoreField: r0->field_63 = r2
    //     0xae5570: stur            w2, [x0, #0x63]
    // 0xae5574: r16 = "campaign"
    //     0xae5574: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe68] "campaign"
    //     0xae5578: ldr             x16, [x16, #0xe68]
    // 0xae557c: StoreField: r0->field_67 = r16
    //     0xae557c: stur            w16, [x0, #0x67]
    // 0xae5580: LoadField: r2 = r1->field_37
    //     0xae5580: ldur            w2, [x1, #0x37]
    // 0xae5584: DecompressPointer r2
    //     0xae5584: add             x2, x2, HEAP, lsl #32
    // 0xae5588: StoreField: r0->field_6b = r2
    //     0xae5588: stur            w2, [x0, #0x6b]
    // 0xae558c: r16 = "province_id"
    //     0xae558c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0xae5590: ldr             x16, [x16, #0x100]
    // 0xae5594: StoreField: r0->field_6f = r16
    //     0xae5594: stur            w16, [x0, #0x6f]
    // 0xae5598: LoadField: r2 = r1->field_3b
    //     0xae5598: ldur            w2, [x1, #0x3b]
    // 0xae559c: DecompressPointer r2
    //     0xae559c: add             x2, x2, HEAP, lsl #32
    // 0xae55a0: StoreField: r0->field_73 = r2
    //     0xae55a0: stur            w2, [x0, #0x73]
    // 0xae55a4: r16 = "regency_id"
    //     0xae55a4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0xae55a8: ldr             x16, [x16, #0x118]
    // 0xae55ac: StoreField: r0->field_77 = r16
    //     0xae55ac: stur            w16, [x0, #0x77]
    // 0xae55b0: LoadField: r2 = r1->field_3f
    //     0xae55b0: ldur            w2, [x1, #0x3f]
    // 0xae55b4: DecompressPointer r2
    //     0xae55b4: add             x2, x2, HEAP, lsl #32
    // 0xae55b8: StoreField: r0->field_7b = r2
    //     0xae55b8: stur            w2, [x0, #0x7b]
    // 0xae55bc: r16 = "district_id"
    //     0xae55bc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0xae55c0: ldr             x16, [x16, #0xfe8]
    // 0xae55c4: StoreField: r0->field_7f = r16
    //     0xae55c4: stur            w16, [x0, #0x7f]
    // 0xae55c8: LoadField: r2 = r1->field_43
    //     0xae55c8: ldur            w2, [x1, #0x43]
    // 0xae55cc: DecompressPointer r2
    //     0xae55cc: add             x2, x2, HEAP, lsl #32
    // 0xae55d0: StoreField: r0->field_83 = r2
    //     0xae55d0: stur            w2, [x0, #0x83]
    // 0xae55d4: r16 = "locality_id"
    //     0xae55d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe70] "locality_id"
    //     0xae55d8: ldr             x16, [x16, #0xe70]
    // 0xae55dc: StoreField: r0->field_87 = r16
    //     0xae55dc: stur            w16, [x0, #0x87]
    // 0xae55e0: LoadField: r2 = r1->field_47
    //     0xae55e0: ldur            w2, [x1, #0x47]
    // 0xae55e4: DecompressPointer r2
    //     0xae55e4: add             x2, x2, HEAP, lsl #32
    // 0xae55e8: StoreField: r0->field_8b = r2
    //     0xae55e8: stur            w2, [x0, #0x8b]
    // 0xae55ec: r16 = <String, dynamic>
    //     0xae55ec: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xae55f0: stp             x0, x16, [SP]
    // 0xae55f4: r0 = Map._fromLiteral()
    //     0xae55f4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xae55f8: LeaveFrame
    //     0xae55f8: mov             SP, fp
    //     0xae55fc: ldp             fp, lr, [SP], #0x10
    // 0xae5600: ret
    //     0xae5600: ret             
    // 0xae5604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5608: b               #0xae5464
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xae5680, size: 0x5f4
    // 0xae5680: EnterFrame
    //     0xae5680: stp             fp, lr, [SP, #-0x10]!
    //     0xae5684: mov             fp, SP
    // 0xae5688: AllocStack(0x80)
    //     0xae5688: sub             SP, SP, #0x80
    // 0xae568c: SetupParameters(dynamic _ /* r2 => r2, fp-0x80 */, {dynamic amount = Null /* r5 */, dynamic campaign = Null /* r6 */, dynamic description = Null /* r7 */, dynamic districtId = Null /* r8 */, dynamic email = Null /* r9 */, dynamic isAnon = Null /* r10 */, dynamic localityId = Null /* r11 */, dynamic method = Null /* r12 */, dynamic name = Null /* r13 */, dynamic phone = Null /* r14 */, dynamic provinceId = Null /* r19 */, dynamic regencyId = Null /* r0 */})
    //     0xae568c: stur            x2, [fp, #-0x80]
    //     0xae5690: ldur            w0, [x4, #0x13]
    //     0xae5694: ldur            w3, [x4, #0x1f]
    //     0xae5698: add             x3, x3, HEAP, lsl #32
    //     0xae569c: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0xae56a0: ldr             x16, [x16, #0xe0]
    //     0xae56a4: cmp             w3, w16
    //     0xae56a8: b.ne            #0xae56cc
    //     0xae56ac: ldur            w3, [x4, #0x23]
    //     0xae56b0: add             x3, x3, HEAP, lsl #32
    //     0xae56b4: sub             w5, w0, w3
    //     0xae56b8: add             x3, fp, w5, sxtw #2
    //     0xae56bc: ldr             x3, [x3, #8]
    //     0xae56c0: mov             x5, x3
    //     0xae56c4: movz            x3, #0x1
    //     0xae56c8: b               #0xae56d4
    //     0xae56cc: mov             x5, NULL
    //     0xae56d0: movz            x3, #0
    //     0xae56d4: lsl             x6, x3, #1
    //     0xae56d8: lsl             w7, w6, #1
    //     0xae56dc: add             w8, w7, #8
    //     0xae56e0: add             x16, x4, w8, sxtw #1
    //     0xae56e4: ldur            w9, [x16, #0xf]
    //     0xae56e8: add             x9, x9, HEAP, lsl #32
    //     0xae56ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe68] "campaign"
    //     0xae56f0: ldr             x16, [x16, #0xe68]
    //     0xae56f4: cmp             w9, w16
    //     0xae56f8: b.ne            #0xae572c
    //     0xae56fc: add             w3, w7, #0xa
    //     0xae5700: add             x16, x4, w3, sxtw #1
    //     0xae5704: ldur            w7, [x16, #0xf]
    //     0xae5708: add             x7, x7, HEAP, lsl #32
    //     0xae570c: sub             w3, w0, w7
    //     0xae5710: add             x7, fp, w3, sxtw #2
    //     0xae5714: ldr             x7, [x7, #8]
    //     0xae5718: add             w3, w6, #2
    //     0xae571c: sbfx            x6, x3, #1, #0x1f
    //     0xae5720: mov             x3, x6
    //     0xae5724: mov             x6, x7
    //     0xae5728: b               #0xae5730
    //     0xae572c: mov             x6, NULL
    //     0xae5730: lsl             x7, x3, #1
    //     0xae5734: lsl             w8, w7, #1
    //     0xae5738: add             w9, w8, #8
    //     0xae573c: add             x16, x4, w9, sxtw #1
    //     0xae5740: ldur            w10, [x16, #0xf]
    //     0xae5744: add             x10, x10, HEAP, lsl #32
    //     0xae5748: add             x16, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0xae574c: ldr             x16, [x16, #0xd28]
    //     0xae5750: cmp             w10, w16
    //     0xae5754: b.ne            #0xae5788
    //     0xae5758: add             w3, w8, #0xa
    //     0xae575c: add             x16, x4, w3, sxtw #1
    //     0xae5760: ldur            w8, [x16, #0xf]
    //     0xae5764: add             x8, x8, HEAP, lsl #32
    //     0xae5768: sub             w3, w0, w8
    //     0xae576c: add             x8, fp, w3, sxtw #2
    //     0xae5770: ldr             x8, [x8, #8]
    //     0xae5774: add             w3, w7, #2
    //     0xae5778: sbfx            x7, x3, #1, #0x1f
    //     0xae577c: mov             x3, x7
    //     0xae5780: mov             x7, x8
    //     0xae5784: b               #0xae578c
    //     0xae5788: mov             x7, NULL
    //     0xae578c: lsl             x8, x3, #1
    //     0xae5790: lsl             w9, w8, #1
    //     0xae5794: add             w10, w9, #8
    //     0xae5798: add             x16, x4, w10, sxtw #1
    //     0xae579c: ldur            w11, [x16, #0xf]
    //     0xae57a0: add             x11, x11, HEAP, lsl #32
    //     0xae57a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "districtId"
    //     0xae57a8: ldr             x16, [x16, #0xe78]
    //     0xae57ac: cmp             w11, w16
    //     0xae57b0: b.ne            #0xae57e4
    //     0xae57b4: add             w3, w9, #0xa
    //     0xae57b8: add             x16, x4, w3, sxtw #1
    //     0xae57bc: ldur            w9, [x16, #0xf]
    //     0xae57c0: add             x9, x9, HEAP, lsl #32
    //     0xae57c4: sub             w3, w0, w9
    //     0xae57c8: add             x9, fp, w3, sxtw #2
    //     0xae57cc: ldr             x9, [x9, #8]
    //     0xae57d0: add             w3, w8, #2
    //     0xae57d4: sbfx            x8, x3, #1, #0x1f
    //     0xae57d8: mov             x3, x8
    //     0xae57dc: mov             x8, x9
    //     0xae57e0: b               #0xae57e8
    //     0xae57e4: mov             x8, NULL
    //     0xae57e8: lsl             x9, x3, #1
    //     0xae57ec: lsl             w10, w9, #1
    //     0xae57f0: add             w11, w10, #8
    //     0xae57f4: add             x16, x4, w11, sxtw #1
    //     0xae57f8: ldur            w12, [x16, #0xf]
    //     0xae57fc: add             x12, x12, HEAP, lsl #32
    //     0xae5800: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b178] "email"
    //     0xae5804: ldr             x16, [x16, #0x178]
    //     0xae5808: cmp             w12, w16
    //     0xae580c: b.ne            #0xae5840
    //     0xae5810: add             w3, w10, #0xa
    //     0xae5814: add             x16, x4, w3, sxtw #1
    //     0xae5818: ldur            w10, [x16, #0xf]
    //     0xae581c: add             x10, x10, HEAP, lsl #32
    //     0xae5820: sub             w3, w0, w10
    //     0xae5824: add             x10, fp, w3, sxtw #2
    //     0xae5828: ldr             x10, [x10, #8]
    //     0xae582c: add             w3, w9, #2
    //     0xae5830: sbfx            x9, x3, #1, #0x1f
    //     0xae5834: mov             x3, x9
    //     0xae5838: mov             x9, x10
    //     0xae583c: b               #0xae5844
    //     0xae5840: mov             x9, NULL
    //     0xae5844: lsl             x10, x3, #1
    //     0xae5848: lsl             w11, w10, #1
    //     0xae584c: add             w12, w11, #8
    //     0xae5850: add             x16, x4, w12, sxtw #1
    //     0xae5854: ldur            w13, [x16, #0xf]
    //     0xae5858: add             x13, x13, HEAP, lsl #32
    //     0xae585c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe80] "isAnon"
    //     0xae5860: ldr             x16, [x16, #0xe80]
    //     0xae5864: cmp             w13, w16
    //     0xae5868: b.ne            #0xae589c
    //     0xae586c: add             w3, w11, #0xa
    //     0xae5870: add             x16, x4, w3, sxtw #1
    //     0xae5874: ldur            w11, [x16, #0xf]
    //     0xae5878: add             x11, x11, HEAP, lsl #32
    //     0xae587c: sub             w3, w0, w11
    //     0xae5880: add             x11, fp, w3, sxtw #2
    //     0xae5884: ldr             x11, [x11, #8]
    //     0xae5888: add             w3, w10, #2
    //     0xae588c: sbfx            x10, x3, #1, #0x1f
    //     0xae5890: mov             x3, x10
    //     0xae5894: mov             x10, x11
    //     0xae5898: b               #0xae58a0
    //     0xae589c: mov             x10, NULL
    //     0xae58a0: lsl             x11, x3, #1
    //     0xae58a4: lsl             w12, w11, #1
    //     0xae58a8: add             w13, w12, #8
    //     0xae58ac: add             x16, x4, w13, sxtw #1
    //     0xae58b0: ldur            w14, [x16, #0xf]
    //     0xae58b4: add             x14, x14, HEAP, lsl #32
    //     0xae58b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe88] "localityId"
    //     0xae58bc: ldr             x16, [x16, #0xe88]
    //     0xae58c0: cmp             w14, w16
    //     0xae58c4: b.ne            #0xae58f8
    //     0xae58c8: add             w3, w12, #0xa
    //     0xae58cc: add             x16, x4, w3, sxtw #1
    //     0xae58d0: ldur            w12, [x16, #0xf]
    //     0xae58d4: add             x12, x12, HEAP, lsl #32
    //     0xae58d8: sub             w3, w0, w12
    //     0xae58dc: add             x12, fp, w3, sxtw #2
    //     0xae58e0: ldr             x12, [x12, #8]
    //     0xae58e4: add             w3, w11, #2
    //     0xae58e8: sbfx            x11, x3, #1, #0x1f
    //     0xae58ec: mov             x3, x11
    //     0xae58f0: mov             x11, x12
    //     0xae58f4: b               #0xae58fc
    //     0xae58f8: mov             x11, NULL
    //     0xae58fc: lsl             x12, x3, #1
    //     0xae5900: lsl             w13, w12, #1
    //     0xae5904: add             w14, w13, #8
    //     0xae5908: add             x16, x4, w14, sxtw #1
    //     0xae590c: ldur            w19, [x16, #0xf]
    //     0xae5910: add             x19, x19, HEAP, lsl #32
    //     0xae5914: add             x16, PP, #0xd, lsl #12  ; [pp+0xd660] "method"
    //     0xae5918: ldr             x16, [x16, #0x660]
    //     0xae591c: cmp             w19, w16
    //     0xae5920: b.ne            #0xae5954
    //     0xae5924: add             w3, w13, #0xa
    //     0xae5928: add             x16, x4, w3, sxtw #1
    //     0xae592c: ldur            w13, [x16, #0xf]
    //     0xae5930: add             x13, x13, HEAP, lsl #32
    //     0xae5934: sub             w3, w0, w13
    //     0xae5938: add             x13, fp, w3, sxtw #2
    //     0xae593c: ldr             x13, [x13, #8]
    //     0xae5940: add             w3, w12, #2
    //     0xae5944: sbfx            x12, x3, #1, #0x1f
    //     0xae5948: mov             x3, x12
    //     0xae594c: mov             x12, x13
    //     0xae5950: b               #0xae5958
    //     0xae5954: mov             x12, NULL
    //     0xae5958: lsl             x13, x3, #1
    //     0xae595c: lsl             w14, w13, #1
    //     0xae5960: add             w19, w14, #8
    //     0xae5964: add             x16, x4, w19, sxtw #1
    //     0xae5968: ldur            w20, [x16, #0xf]
    //     0xae596c: add             x20, x20, HEAP, lsl #32
    //     0xae5970: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    //     0xae5974: cmp             w20, w16
    //     0xae5978: b.ne            #0xae59ac
    //     0xae597c: add             w3, w14, #0xa
    //     0xae5980: add             x16, x4, w3, sxtw #1
    //     0xae5984: ldur            w14, [x16, #0xf]
    //     0xae5988: add             x14, x14, HEAP, lsl #32
    //     0xae598c: sub             w3, w0, w14
    //     0xae5990: add             x14, fp, w3, sxtw #2
    //     0xae5994: ldr             x14, [x14, #8]
    //     0xae5998: add             w3, w13, #2
    //     0xae599c: sbfx            x13, x3, #1, #0x1f
    //     0xae59a0: mov             x3, x13
    //     0xae59a4: mov             x13, x14
    //     0xae59a8: b               #0xae59b0
    //     0xae59ac: mov             x13, NULL
    //     0xae59b0: lsl             x14, x3, #1
    //     0xae59b4: lsl             w19, w14, #1
    //     0xae59b8: add             w20, w19, #8
    //     0xae59bc: add             x16, x4, w20, sxtw #1
    //     0xae59c0: ldur            w23, [x16, #0xf]
    //     0xae59c4: add             x23, x23, HEAP, lsl #32
    //     0xae59c8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b160] "phone"
    //     0xae59cc: ldr             x16, [x16, #0x160]
    //     0xae59d0: cmp             w23, w16
    //     0xae59d4: b.ne            #0xae5a08
    //     0xae59d8: add             w3, w19, #0xa
    //     0xae59dc: add             x16, x4, w3, sxtw #1
    //     0xae59e0: ldur            w19, [x16, #0xf]
    //     0xae59e4: add             x19, x19, HEAP, lsl #32
    //     0xae59e8: sub             w3, w0, w19
    //     0xae59ec: add             x19, fp, w3, sxtw #2
    //     0xae59f0: ldr             x19, [x19, #8]
    //     0xae59f4: add             w3, w14, #2
    //     0xae59f8: sbfx            x14, x3, #1, #0x1f
    //     0xae59fc: mov             x3, x14
    //     0xae5a00: mov             x14, x19
    //     0xae5a04: b               #0xae5a0c
    //     0xae5a08: mov             x14, NULL
    //     0xae5a0c: lsl             x19, x3, #1
    //     0xae5a10: lsl             w20, w19, #1
    //     0xae5a14: add             w23, w20, #8
    //     0xae5a18: add             x16, x4, w23, sxtw #1
    //     0xae5a1c: ldur            w24, [x16, #0xf]
    //     0xae5a20: add             x24, x24, HEAP, lsl #32
    //     0xae5a24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe90] "provinceId"
    //     0xae5a28: ldr             x16, [x16, #0xe90]
    //     0xae5a2c: cmp             w24, w16
    //     0xae5a30: b.ne            #0xae5a64
    //     0xae5a34: add             w3, w20, #0xa
    //     0xae5a38: add             x16, x4, w3, sxtw #1
    //     0xae5a3c: ldur            w20, [x16, #0xf]
    //     0xae5a40: add             x20, x20, HEAP, lsl #32
    //     0xae5a44: sub             w3, w0, w20
    //     0xae5a48: add             x20, fp, w3, sxtw #2
    //     0xae5a4c: ldr             x20, [x20, #8]
    //     0xae5a50: add             w3, w19, #2
    //     0xae5a54: sbfx            x19, x3, #1, #0x1f
    //     0xae5a58: mov             x3, x19
    //     0xae5a5c: mov             x19, x20
    //     0xae5a60: b               #0xae5a68
    //     0xae5a64: mov             x19, NULL
    //     0xae5a68: lsl             x20, x3, #1
    //     0xae5a6c: lsl             w3, w20, #1
    //     0xae5a70: add             w20, w3, #8
    //     0xae5a74: add             x16, x4, w20, sxtw #1
    //     0xae5a78: ldur            w23, [x16, #0xf]
    //     0xae5a7c: add             x23, x23, HEAP, lsl #32
    //     0xae5a80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe98] "regencyId"
    //     0xae5a84: ldr             x16, [x16, #0xe98]
    //     0xae5a88: cmp             w23, w16
    //     0xae5a8c: b.ne            #0xae5ab0
    //     0xae5a90: add             w20, w3, #0xa
    //     0xae5a94: add             x16, x4, w20, sxtw #1
    //     0xae5a98: ldur            w3, [x16, #0xf]
    //     0xae5a9c: add             x3, x3, HEAP, lsl #32
    //     0xae5aa0: sub             w4, w0, w3
    //     0xae5aa4: add             x0, fp, w4, sxtw #2
    //     0xae5aa8: ldr             x0, [x0, #8]
    //     0xae5aac: b               #0xae5ab4
    //     0xae5ab0: mov             x0, NULL
    // 0xae5ab4: cmp             w12, NULL
    // 0xae5ab8: b.ne            #0xae5ac8
    // 0xae5abc: LoadField: r3 = r1->field_b
    //     0xae5abc: ldur            w3, [x1, #0xb]
    // 0xae5ac0: DecompressPointer r3
    //     0xae5ac0: add             x3, x3, HEAP, lsl #32
    // 0xae5ac4: b               #0xae5acc
    // 0xae5ac8: mov             x3, x12
    // 0xae5acc: stur            x3, [fp, #-0x78]
    // 0xae5ad0: LoadField: r4 = r1->field_13
    //     0xae5ad0: ldur            w4, [x1, #0x13]
    // 0xae5ad4: DecompressPointer r4
    //     0xae5ad4: add             x4, x4, HEAP, lsl #32
    // 0xae5ad8: stur            x4, [fp, #-0x70]
    // 0xae5adc: cmp             w5, NULL
    // 0xae5ae0: b.ne            #0xae5aec
    // 0xae5ae4: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xae5ae4: ldur            w5, [x1, #0x17]
    // 0xae5ae8: DecompressPointer r5
    //     0xae5ae8: add             x5, x5, HEAP, lsl #32
    // 0xae5aec: stur            x5, [fp, #-0x68]
    // 0xae5af0: cmp             w13, NULL
    // 0xae5af4: b.ne            #0xae5b04
    // 0xae5af8: LoadField: r12 = r1->field_1b
    //     0xae5af8: ldur            w12, [x1, #0x1b]
    // 0xae5afc: DecompressPointer r12
    //     0xae5afc: add             x12, x12, HEAP, lsl #32
    // 0xae5b00: b               #0xae5b08
    // 0xae5b04: mov             x12, x13
    // 0xae5b08: stur            x12, [fp, #-0x60]
    // 0xae5b0c: LoadField: r13 = r1->field_1f
    //     0xae5b0c: ldur            w13, [x1, #0x1f]
    // 0xae5b10: DecompressPointer r13
    //     0xae5b10: add             x13, x13, HEAP, lsl #32
    // 0xae5b14: stur            x13, [fp, #-0x58]
    // 0xae5b18: LoadField: r20 = r1->field_23
    //     0xae5b18: ldur            w20, [x1, #0x23]
    // 0xae5b1c: DecompressPointer r20
    //     0xae5b1c: add             x20, x20, HEAP, lsl #32
    // 0xae5b20: stur            x20, [fp, #-0x50]
    // 0xae5b24: cmp             w9, NULL
    // 0xae5b28: b.ne            #0xae5b34
    // 0xae5b2c: LoadField: r9 = r1->field_27
    //     0xae5b2c: ldur            w9, [x1, #0x27]
    // 0xae5b30: DecompressPointer r9
    //     0xae5b30: add             x9, x9, HEAP, lsl #32
    // 0xae5b34: stur            x9, [fp, #-0x48]
    // 0xae5b38: cmp             w14, NULL
    // 0xae5b3c: b.ne            #0xae5b48
    // 0xae5b40: LoadField: r14 = r1->field_2b
    //     0xae5b40: ldur            w14, [x1, #0x2b]
    // 0xae5b44: DecompressPointer r14
    //     0xae5b44: add             x14, x14, HEAP, lsl #32
    // 0xae5b48: stur            x14, [fp, #-0x40]
    // 0xae5b4c: cmp             w7, NULL
    // 0xae5b50: b.ne            #0xae5b5c
    // 0xae5b54: LoadField: r7 = r1->field_2f
    //     0xae5b54: ldur            w7, [x1, #0x2f]
    // 0xae5b58: DecompressPointer r7
    //     0xae5b58: add             x7, x7, HEAP, lsl #32
    // 0xae5b5c: stur            x7, [fp, #-0x38]
    // 0xae5b60: cmp             w10, NULL
    // 0xae5b64: b.ne            #0xae5b70
    // 0xae5b68: LoadField: r10 = r1->field_33
    //     0xae5b68: ldur            w10, [x1, #0x33]
    // 0xae5b6c: DecompressPointer r10
    //     0xae5b6c: add             x10, x10, HEAP, lsl #32
    // 0xae5b70: stur            x10, [fp, #-0x30]
    // 0xae5b74: cmp             w6, NULL
    // 0xae5b78: b.ne            #0xae5b84
    // 0xae5b7c: LoadField: r6 = r1->field_37
    //     0xae5b7c: ldur            w6, [x1, #0x37]
    // 0xae5b80: DecompressPointer r6
    //     0xae5b80: add             x6, x6, HEAP, lsl #32
    // 0xae5b84: stur            x6, [fp, #-0x28]
    // 0xae5b88: cmp             w19, NULL
    // 0xae5b8c: b.ne            #0xae5b98
    // 0xae5b90: LoadField: r19 = r1->field_3b
    //     0xae5b90: ldur            w19, [x1, #0x3b]
    // 0xae5b94: DecompressPointer r19
    //     0xae5b94: add             x19, x19, HEAP, lsl #32
    // 0xae5b98: stur            x19, [fp, #-0x20]
    // 0xae5b9c: cmp             w0, NULL
    // 0xae5ba0: b.ne            #0xae5bac
    // 0xae5ba4: LoadField: r0 = r1->field_3f
    //     0xae5ba4: ldur            w0, [x1, #0x3f]
    // 0xae5ba8: DecompressPointer r0
    //     0xae5ba8: add             x0, x0, HEAP, lsl #32
    // 0xae5bac: stur            x0, [fp, #-0x18]
    // 0xae5bb0: cmp             w8, NULL
    // 0xae5bb4: b.ne            #0xae5bc0
    // 0xae5bb8: LoadField: r8 = r1->field_43
    //     0xae5bb8: ldur            w8, [x1, #0x43]
    // 0xae5bbc: DecompressPointer r8
    //     0xae5bbc: add             x8, x8, HEAP, lsl #32
    // 0xae5bc0: stur            x8, [fp, #-0x10]
    // 0xae5bc4: cmp             w11, NULL
    // 0xae5bc8: b.ne            #0xae5bd4
    // 0xae5bcc: LoadField: r11 = r1->field_47
    //     0xae5bcc: ldur            w11, [x1, #0x47]
    // 0xae5bd0: DecompressPointer r11
    //     0xae5bd0: add             x11, x11, HEAP, lsl #32
    // 0xae5bd4: stur            x11, [fp, #-8]
    // 0xae5bd8: LoadField: r23 = r1->field_7
    //     0xae5bd8: ldur            w23, [x1, #7]
    // 0xae5bdc: DecompressPointer r23
    //     0xae5bdc: add             x23, x23, HEAP, lsl #32
    // 0xae5be0: mov             x1, x23
    // 0xae5be4: r0 = TransactionRequest()
    //     0xae5be4: bl              #0x810fc0  ; AllocateTransactionRequestStub -> TransactionRequest<X0> (size=0x4c)
    // 0xae5be8: ldur            x1, [fp, #-0x78]
    // 0xae5bec: StoreField: r0->field_b = r1
    //     0xae5bec: stur            w1, [x0, #0xb]
    // 0xae5bf0: ldur            x1, [fp, #-0x80]
    // 0xae5bf4: StoreField: r0->field_f = r1
    //     0xae5bf4: stur            w1, [x0, #0xf]
    // 0xae5bf8: ldur            x1, [fp, #-0x70]
    // 0xae5bfc: StoreField: r0->field_13 = r1
    //     0xae5bfc: stur            w1, [x0, #0x13]
    // 0xae5c00: ldur            x1, [fp, #-0x68]
    // 0xae5c04: ArrayStore: r0[0] = r1  ; List_4
    //     0xae5c04: stur            w1, [x0, #0x17]
    // 0xae5c08: ldur            x1, [fp, #-0x60]
    // 0xae5c0c: StoreField: r0->field_1b = r1
    //     0xae5c0c: stur            w1, [x0, #0x1b]
    // 0xae5c10: ldur            x1, [fp, #-0x58]
    // 0xae5c14: StoreField: r0->field_1f = r1
    //     0xae5c14: stur            w1, [x0, #0x1f]
    // 0xae5c18: ldur            x1, [fp, #-0x50]
    // 0xae5c1c: StoreField: r0->field_23 = r1
    //     0xae5c1c: stur            w1, [x0, #0x23]
    // 0xae5c20: ldur            x1, [fp, #-0x48]
    // 0xae5c24: StoreField: r0->field_27 = r1
    //     0xae5c24: stur            w1, [x0, #0x27]
    // 0xae5c28: ldur            x1, [fp, #-0x40]
    // 0xae5c2c: StoreField: r0->field_2b = r1
    //     0xae5c2c: stur            w1, [x0, #0x2b]
    // 0xae5c30: ldur            x1, [fp, #-0x38]
    // 0xae5c34: StoreField: r0->field_2f = r1
    //     0xae5c34: stur            w1, [x0, #0x2f]
    // 0xae5c38: ldur            x1, [fp, #-0x30]
    // 0xae5c3c: StoreField: r0->field_33 = r1
    //     0xae5c3c: stur            w1, [x0, #0x33]
    // 0xae5c40: ldur            x1, [fp, #-0x28]
    // 0xae5c44: StoreField: r0->field_37 = r1
    //     0xae5c44: stur            w1, [x0, #0x37]
    // 0xae5c48: ldur            x1, [fp, #-0x20]
    // 0xae5c4c: StoreField: r0->field_3b = r1
    //     0xae5c4c: stur            w1, [x0, #0x3b]
    // 0xae5c50: ldur            x1, [fp, #-0x18]
    // 0xae5c54: StoreField: r0->field_3f = r1
    //     0xae5c54: stur            w1, [x0, #0x3f]
    // 0xae5c58: ldur            x1, [fp, #-0x10]
    // 0xae5c5c: StoreField: r0->field_43 = r1
    //     0xae5c5c: stur            w1, [x0, #0x43]
    // 0xae5c60: ldur            x1, [fp, #-8]
    // 0xae5c64: StoreField: r0->field_47 = r1
    //     0xae5c64: stur            w1, [x0, #0x47]
    // 0xae5c68: LeaveFrame
    //     0xae5c68: mov             SP, fp
    //     0xae5c6c: ldp             fp, lr, [SP], #0x10
    // 0xae5c70: ret
    //     0xae5c70: ret             
  }
}

// class id: 5570, size: 0x50, field offset: 0x8
//   const constructor, 
class Transaction extends Equatable {

  factory _ Transaction.fromMap(/* No info */) {
    // ** addr: 0x6fcae8, size: 0x738
    // 0x6fcae8: EnterFrame
    //     0x6fcae8: stp             fp, lr, [SP, #-0x10]!
    //     0x6fcaec: mov             fp, SP
    // 0x6fcaf0: AllocStack(0x98)
    //     0x6fcaf0: sub             SP, SP, #0x98
    // 0x6fcaf4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x6fcaf4: mov             x3, x2
    //     0x6fcaf8: stur            x2, [fp, #-8]
    // 0x6fcafc: CheckStackOverflow
    //     0x6fcafc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fcb00: cmp             SP, x16
    //     0x6fcb04: b.ls            #0x6fd218
    // 0x6fcb08: r0 = LoadClassIdInstr(r3)
    //     0x6fcb08: ldur            x0, [x3, #-1]
    //     0x6fcb0c: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcb10: mov             x1, x3
    // 0x6fcb14: r2 = "id"
    //     0x6fcb14: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x6fcb18: ldr             x2, [x2, #0x740]
    // 0x6fcb1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcb1c: sub             lr, x0, #0x114
    //     0x6fcb20: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcb24: blr             lr
    // 0x6fcb28: mov             x3, x0
    // 0x6fcb2c: r2 = Null
    //     0x6fcb2c: mov             x2, NULL
    // 0x6fcb30: r1 = Null
    //     0x6fcb30: mov             x1, NULL
    // 0x6fcb34: stur            x3, [fp, #-0x10]
    // 0x6fcb38: branchIfSmi(r0, 0x6fcb60)
    //     0x6fcb38: tbz             w0, #0, #0x6fcb60
    // 0x6fcb3c: r4 = LoadClassIdInstr(r0)
    //     0x6fcb3c: ldur            x4, [x0, #-1]
    //     0x6fcb40: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcb44: sub             x4, x4, #0x3c
    // 0x6fcb48: cmp             x4, #1
    // 0x6fcb4c: b.ls            #0x6fcb60
    // 0x6fcb50: r8 = int
    //     0x6fcb50: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x6fcb54: r3 = Null
    //     0x6fcb54: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b0c8] Null
    //     0x6fcb58: ldr             x3, [x3, #0xc8]
    // 0x6fcb5c: r0 = int()
    //     0x6fcb5c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x6fcb60: ldur            x3, [fp, #-8]
    // 0x6fcb64: r0 = LoadClassIdInstr(r3)
    //     0x6fcb64: ldur            x0, [x3, #-1]
    //     0x6fcb68: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcb6c: mov             x1, x3
    // 0x6fcb70: r2 = "trx_id"
    //     0x6fcb70: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b0d8] "trx_id"
    //     0x6fcb74: ldr             x2, [x2, #0xd8]
    // 0x6fcb78: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcb78: sub             lr, x0, #0x114
    //     0x6fcb7c: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcb80: blr             lr
    // 0x6fcb84: mov             x3, x0
    // 0x6fcb88: r2 = Null
    //     0x6fcb88: mov             x2, NULL
    // 0x6fcb8c: r1 = Null
    //     0x6fcb8c: mov             x1, NULL
    // 0x6fcb90: stur            x3, [fp, #-0x18]
    // 0x6fcb94: r4 = 60
    //     0x6fcb94: movz            x4, #0x3c
    // 0x6fcb98: branchIfSmi(r0, 0x6fcba4)
    //     0x6fcb98: tbz             w0, #0, #0x6fcba4
    // 0x6fcb9c: r4 = LoadClassIdInstr(r0)
    //     0x6fcb9c: ldur            x4, [x0, #-1]
    //     0x6fcba0: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcba4: sub             x4, x4, #0x5e
    // 0x6fcba8: cmp             x4, #1
    // 0x6fcbac: b.ls            #0x6fcbc0
    // 0x6fcbb0: r8 = String
    //     0x6fcbb0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fcbb4: r3 = Null
    //     0x6fcbb4: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b0e0] Null
    //     0x6fcbb8: ldr             x3, [x3, #0xe0]
    // 0x6fcbbc: r0 = String()
    //     0x6fcbbc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fcbc0: ldur            x3, [fp, #-8]
    // 0x6fcbc4: r0 = LoadClassIdInstr(r3)
    //     0x6fcbc4: ldur            x0, [x3, #-1]
    //     0x6fcbc8: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcbcc: mov             x1, x3
    // 0x6fcbd0: r2 = "bill_number"
    //     0x6fcbd0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b0f0] "bill_number"
    //     0x6fcbd4: ldr             x2, [x2, #0xf0]
    // 0x6fcbd8: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcbd8: sub             lr, x0, #0x114
    //     0x6fcbdc: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcbe0: blr             lr
    // 0x6fcbe4: mov             x3, x0
    // 0x6fcbe8: r2 = Null
    //     0x6fcbe8: mov             x2, NULL
    // 0x6fcbec: r1 = Null
    //     0x6fcbec: mov             x1, NULL
    // 0x6fcbf0: stur            x3, [fp, #-0x20]
    // 0x6fcbf4: r4 = 60
    //     0x6fcbf4: movz            x4, #0x3c
    // 0x6fcbf8: branchIfSmi(r0, 0x6fcc04)
    //     0x6fcbf8: tbz             w0, #0, #0x6fcc04
    // 0x6fcbfc: r4 = LoadClassIdInstr(r0)
    //     0x6fcbfc: ldur            x4, [x0, #-1]
    //     0x6fcc00: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcc04: sub             x4, x4, #0x5e
    // 0x6fcc08: cmp             x4, #1
    // 0x6fcc0c: b.ls            #0x6fcc20
    // 0x6fcc10: r8 = String?
    //     0x6fcc10: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x6fcc14: r3 = Null
    //     0x6fcc14: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b0f8] Null
    //     0x6fcc18: ldr             x3, [x3, #0xf8]
    // 0x6fcc1c: r0 = String?()
    //     0x6fcc1c: bl              #0x600324  ; IsType_String?_Stub
    // 0x6fcc20: ldur            x3, [fp, #-8]
    // 0x6fcc24: r0 = LoadClassIdInstr(r3)
    //     0x6fcc24: ldur            x0, [x3, #-1]
    //     0x6fcc28: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcc2c: mov             x1, x3
    // 0x6fcc30: r2 = "name"
    //     0x6fcc30: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x6fcc34: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcc34: sub             lr, x0, #0x114
    //     0x6fcc38: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcc3c: blr             lr
    // 0x6fcc40: mov             x3, x0
    // 0x6fcc44: r2 = Null
    //     0x6fcc44: mov             x2, NULL
    // 0x6fcc48: r1 = Null
    //     0x6fcc48: mov             x1, NULL
    // 0x6fcc4c: stur            x3, [fp, #-0x28]
    // 0x6fcc50: r4 = 60
    //     0x6fcc50: movz            x4, #0x3c
    // 0x6fcc54: branchIfSmi(r0, 0x6fcc60)
    //     0x6fcc54: tbz             w0, #0, #0x6fcc60
    // 0x6fcc58: r4 = LoadClassIdInstr(r0)
    //     0x6fcc58: ldur            x4, [x0, #-1]
    //     0x6fcc5c: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcc60: sub             x4, x4, #0x5e
    // 0x6fcc64: cmp             x4, #1
    // 0x6fcc68: b.ls            #0x6fcc7c
    // 0x6fcc6c: r8 = String
    //     0x6fcc6c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fcc70: r3 = Null
    //     0x6fcc70: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b108] Null
    //     0x6fcc74: ldr             x3, [x3, #0x108]
    // 0x6fcc78: r0 = String()
    //     0x6fcc78: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fcc7c: ldur            x3, [fp, #-8]
    // 0x6fcc80: r0 = LoadClassIdInstr(r3)
    //     0x6fcc80: ldur            x0, [x3, #-1]
    //     0x6fcc84: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcc88: mov             x1, x3
    // 0x6fcc8c: r2 = "amount"
    //     0x6fcc8c: add             x2, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x6fcc90: ldr             x2, [x2, #0xe0]
    // 0x6fcc94: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcc94: sub             lr, x0, #0x114
    //     0x6fcc98: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcc9c: blr             lr
    // 0x6fcca0: mov             x3, x0
    // 0x6fcca4: r2 = Null
    //     0x6fcca4: mov             x2, NULL
    // 0x6fcca8: r1 = Null
    //     0x6fcca8: mov             x1, NULL
    // 0x6fccac: stur            x3, [fp, #-0x30]
    // 0x6fccb0: branchIfSmi(r0, 0x6fccd8)
    //     0x6fccb0: tbz             w0, #0, #0x6fccd8
    // 0x6fccb4: r4 = LoadClassIdInstr(r0)
    //     0x6fccb4: ldur            x4, [x0, #-1]
    //     0x6fccb8: ubfx            x4, x4, #0xc, #0x14
    // 0x6fccbc: sub             x4, x4, #0x3c
    // 0x6fccc0: cmp             x4, #1
    // 0x6fccc4: b.ls            #0x6fccd8
    // 0x6fccc8: r8 = int
    //     0x6fccc8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x6fcccc: r3 = Null
    //     0x6fcccc: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b118] Null
    //     0x6fccd0: ldr             x3, [x3, #0x118]
    // 0x6fccd4: r0 = int()
    //     0x6fccd4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x6fccd8: ldur            x3, [fp, #-8]
    // 0x6fccdc: r0 = LoadClassIdInstr(r3)
    //     0x6fccdc: ldur            x0, [x3, #-1]
    //     0x6fcce0: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcce4: mov             x1, x3
    // 0x6fcce8: r2 = "is_anon"
    //     0x6fcce8: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b128] "is_anon"
    //     0x6fccec: ldr             x2, [x2, #0x128]
    // 0x6fccf0: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fccf0: sub             lr, x0, #0x114
    //     0x6fccf4: ldr             lr, [x21, lr, lsl #3]
    //     0x6fccf8: blr             lr
    // 0x6fccfc: r1 = 60
    //     0x6fccfc: movz            x1, #0x3c
    // 0x6fcd00: branchIfSmi(r0, 0x6fcd0c)
    //     0x6fcd00: tbz             w0, #0, #0x6fcd0c
    // 0x6fcd04: r1 = LoadClassIdInstr(r0)
    //     0x6fcd04: ldur            x1, [x0, #-1]
    //     0x6fcd08: ubfx            x1, x1, #0xc, #0x14
    // 0x6fcd0c: r16 = 2
    //     0x6fcd0c: movz            x16, #0x2
    // 0x6fcd10: stp             x16, x0, [SP]
    // 0x6fcd14: mov             x0, x1
    // 0x6fcd18: mov             lr, x0
    // 0x6fcd1c: ldr             lr, [x21, lr, lsl #3]
    // 0x6fcd20: blr             lr
    // 0x6fcd24: mov             x4, x0
    // 0x6fcd28: ldur            x3, [fp, #-8]
    // 0x6fcd2c: stur            x4, [fp, #-0x38]
    // 0x6fcd30: r0 = LoadClassIdInstr(r3)
    //     0x6fcd30: ldur            x0, [x3, #-1]
    //     0x6fcd34: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcd38: mov             x1, x3
    // 0x6fcd3c: r2 = "status"
    //     0x6fcd3c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b130] "status"
    //     0x6fcd40: ldr             x2, [x2, #0x130]
    // 0x6fcd44: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcd44: sub             lr, x0, #0x114
    //     0x6fcd48: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcd4c: blr             lr
    // 0x6fcd50: mov             x3, x0
    // 0x6fcd54: r2 = Null
    //     0x6fcd54: mov             x2, NULL
    // 0x6fcd58: r1 = Null
    //     0x6fcd58: mov             x1, NULL
    // 0x6fcd5c: stur            x3, [fp, #-0x40]
    // 0x6fcd60: r4 = 60
    //     0x6fcd60: movz            x4, #0x3c
    // 0x6fcd64: branchIfSmi(r0, 0x6fcd70)
    //     0x6fcd64: tbz             w0, #0, #0x6fcd70
    // 0x6fcd68: r4 = LoadClassIdInstr(r0)
    //     0x6fcd68: ldur            x4, [x0, #-1]
    //     0x6fcd6c: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcd70: sub             x4, x4, #0x5e
    // 0x6fcd74: cmp             x4, #1
    // 0x6fcd78: b.ls            #0x6fcd8c
    // 0x6fcd7c: r8 = String
    //     0x6fcd7c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fcd80: r3 = Null
    //     0x6fcd80: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b138] Null
    //     0x6fcd84: ldr             x3, [x3, #0x138]
    // 0x6fcd88: r0 = String()
    //     0x6fcd88: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fcd8c: ldur            x3, [fp, #-8]
    // 0x6fcd90: r0 = LoadClassIdInstr(r3)
    //     0x6fcd90: ldur            x0, [x3, #-1]
    //     0x6fcd94: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcd98: mov             x1, x3
    // 0x6fcd9c: r2 = "donation"
    //     0x6fcd9c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b148] "donation"
    //     0x6fcda0: ldr             x2, [x2, #0x148]
    // 0x6fcda4: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcda4: sub             lr, x0, #0x114
    //     0x6fcda8: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcdac: blr             lr
    // 0x6fcdb0: mov             x3, x0
    // 0x6fcdb4: r2 = Null
    //     0x6fcdb4: mov             x2, NULL
    // 0x6fcdb8: r1 = Null
    //     0x6fcdb8: mov             x1, NULL
    // 0x6fcdbc: stur            x3, [fp, #-0x48]
    // 0x6fcdc0: r8 = Map<String, dynamic>
    //     0x6fcdc0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x6fcdc4: r3 = Null
    //     0x6fcdc4: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b150] Null
    //     0x6fcdc8: ldr             x3, [x3, #0x150]
    // 0x6fcdcc: r0 = Map<String, dynamic>()
    //     0x6fcdcc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x6fcdd0: ldur            x2, [fp, #-0x48]
    // 0x6fcdd4: r1 = Null
    //     0x6fcdd4: mov             x1, NULL
    // 0x6fcdd8: r0 = Donation.fromMap()
    //     0x6fcdd8: bl              #0x6ff644  ; [package:nuonline/app/data/models/transaction.dart] Donation::Donation.fromMap
    // 0x6fcddc: mov             x4, x0
    // 0x6fcde0: ldur            x3, [fp, #-8]
    // 0x6fcde4: stur            x4, [fp, #-0x48]
    // 0x6fcde8: r0 = LoadClassIdInstr(r3)
    //     0x6fcde8: ldur            x0, [x3, #-1]
    //     0x6fcdec: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcdf0: mov             x1, x3
    // 0x6fcdf4: r2 = "phone"
    //     0x6fcdf4: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b160] "phone"
    //     0x6fcdf8: ldr             x2, [x2, #0x160]
    // 0x6fcdfc: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcdfc: sub             lr, x0, #0x114
    //     0x6fce00: ldr             lr, [x21, lr, lsl #3]
    //     0x6fce04: blr             lr
    // 0x6fce08: mov             x3, x0
    // 0x6fce0c: r2 = Null
    //     0x6fce0c: mov             x2, NULL
    // 0x6fce10: r1 = Null
    //     0x6fce10: mov             x1, NULL
    // 0x6fce14: stur            x3, [fp, #-0x50]
    // 0x6fce18: r4 = 60
    //     0x6fce18: movz            x4, #0x3c
    // 0x6fce1c: branchIfSmi(r0, 0x6fce28)
    //     0x6fce1c: tbz             w0, #0, #0x6fce28
    // 0x6fce20: r4 = LoadClassIdInstr(r0)
    //     0x6fce20: ldur            x4, [x0, #-1]
    //     0x6fce24: ubfx            x4, x4, #0xc, #0x14
    // 0x6fce28: sub             x4, x4, #0x5e
    // 0x6fce2c: cmp             x4, #1
    // 0x6fce30: b.ls            #0x6fce44
    // 0x6fce34: r8 = String?
    //     0x6fce34: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x6fce38: r3 = Null
    //     0x6fce38: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b168] Null
    //     0x6fce3c: ldr             x3, [x3, #0x168]
    // 0x6fce40: r0 = String?()
    //     0x6fce40: bl              #0x600324  ; IsType_String?_Stub
    // 0x6fce44: ldur            x3, [fp, #-8]
    // 0x6fce48: r0 = LoadClassIdInstr(r3)
    //     0x6fce48: ldur            x0, [x3, #-1]
    //     0x6fce4c: ubfx            x0, x0, #0xc, #0x14
    // 0x6fce50: mov             x1, x3
    // 0x6fce54: r2 = "email"
    //     0x6fce54: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b178] "email"
    //     0x6fce58: ldr             x2, [x2, #0x178]
    // 0x6fce5c: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fce5c: sub             lr, x0, #0x114
    //     0x6fce60: ldr             lr, [x21, lr, lsl #3]
    //     0x6fce64: blr             lr
    // 0x6fce68: mov             x3, x0
    // 0x6fce6c: r2 = Null
    //     0x6fce6c: mov             x2, NULL
    // 0x6fce70: r1 = Null
    //     0x6fce70: mov             x1, NULL
    // 0x6fce74: stur            x3, [fp, #-0x58]
    // 0x6fce78: r4 = 60
    //     0x6fce78: movz            x4, #0x3c
    // 0x6fce7c: branchIfSmi(r0, 0x6fce88)
    //     0x6fce7c: tbz             w0, #0, #0x6fce88
    // 0x6fce80: r4 = LoadClassIdInstr(r0)
    //     0x6fce80: ldur            x4, [x0, #-1]
    //     0x6fce84: ubfx            x4, x4, #0xc, #0x14
    // 0x6fce88: sub             x4, x4, #0x5e
    // 0x6fce8c: cmp             x4, #1
    // 0x6fce90: b.ls            #0x6fcea4
    // 0x6fce94: r8 = String?
    //     0x6fce94: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x6fce98: r3 = Null
    //     0x6fce98: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b180] Null
    //     0x6fce9c: ldr             x3, [x3, #0x180]
    // 0x6fcea0: r0 = String?()
    //     0x6fcea0: bl              #0x600324  ; IsType_String?_Stub
    // 0x6fcea4: ldur            x3, [fp, #-8]
    // 0x6fcea8: r0 = LoadClassIdInstr(r3)
    //     0x6fcea8: ldur            x0, [x3, #-1]
    //     0x6fceac: ubfx            x0, x0, #0xc, #0x14
    // 0x6fceb0: mov             x1, x3
    // 0x6fceb4: r2 = "description"
    //     0x6fceb4: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x6fceb8: ldr             x2, [x2, #0xd28]
    // 0x6fcebc: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcebc: sub             lr, x0, #0x114
    //     0x6fcec0: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcec4: blr             lr
    // 0x6fcec8: mov             x3, x0
    // 0x6fcecc: r2 = Null
    //     0x6fcecc: mov             x2, NULL
    // 0x6fced0: r1 = Null
    //     0x6fced0: mov             x1, NULL
    // 0x6fced4: stur            x3, [fp, #-0x60]
    // 0x6fced8: r4 = 60
    //     0x6fced8: movz            x4, #0x3c
    // 0x6fcedc: branchIfSmi(r0, 0x6fcee8)
    //     0x6fcedc: tbz             w0, #0, #0x6fcee8
    // 0x6fcee0: r4 = LoadClassIdInstr(r0)
    //     0x6fcee0: ldur            x4, [x0, #-1]
    //     0x6fcee4: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcee8: sub             x4, x4, #0x5e
    // 0x6fceec: cmp             x4, #1
    // 0x6fcef0: b.ls            #0x6fcf04
    // 0x6fcef4: r8 = String?
    //     0x6fcef4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x6fcef8: r3 = Null
    //     0x6fcef8: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b190] Null
    //     0x6fcefc: ldr             x3, [x3, #0x190]
    // 0x6fcf00: r0 = String?()
    //     0x6fcf00: bl              #0x600324  ; IsType_String?_Stub
    // 0x6fcf04: ldur            x3, [fp, #-8]
    // 0x6fcf08: r0 = LoadClassIdInstr(r3)
    //     0x6fcf08: ldur            x0, [x3, #-1]
    //     0x6fcf0c: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcf10: mov             x1, x3
    // 0x6fcf14: r2 = "va_number"
    //     0x6fcf14: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1a0] "va_number"
    //     0x6fcf18: ldr             x2, [x2, #0x1a0]
    // 0x6fcf1c: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcf1c: sub             lr, x0, #0x114
    //     0x6fcf20: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcf24: blr             lr
    // 0x6fcf28: mov             x3, x0
    // 0x6fcf2c: r2 = Null
    //     0x6fcf2c: mov             x2, NULL
    // 0x6fcf30: r1 = Null
    //     0x6fcf30: mov             x1, NULL
    // 0x6fcf34: stur            x3, [fp, #-0x68]
    // 0x6fcf38: r4 = 60
    //     0x6fcf38: movz            x4, #0x3c
    // 0x6fcf3c: branchIfSmi(r0, 0x6fcf48)
    //     0x6fcf3c: tbz             w0, #0, #0x6fcf48
    // 0x6fcf40: r4 = LoadClassIdInstr(r0)
    //     0x6fcf40: ldur            x4, [x0, #-1]
    //     0x6fcf44: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcf48: sub             x4, x4, #0x5e
    // 0x6fcf4c: cmp             x4, #1
    // 0x6fcf50: b.ls            #0x6fcf64
    // 0x6fcf54: r8 = String?
    //     0x6fcf54: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x6fcf58: r3 = Null
    //     0x6fcf58: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b1a8] Null
    //     0x6fcf5c: ldr             x3, [x3, #0x1a8]
    // 0x6fcf60: r0 = String?()
    //     0x6fcf60: bl              #0x600324  ; IsType_String?_Stub
    // 0x6fcf64: ldur            x0, [fp, #-0x68]
    // 0x6fcf68: cmp             w0, NULL
    // 0x6fcf6c: b.ne            #0x6fcf78
    // 0x6fcf70: r19 = ""
    //     0x6fcf70: ldr             x19, [PP, #0x288]  ; [pp+0x288] ""
    // 0x6fcf74: b               #0x6fcf7c
    // 0x6fcf78: mov             x19, x0
    // 0x6fcf7c: ldur            x3, [fp, #-8]
    // 0x6fcf80: ldur            x14, [fp, #-0x10]
    // 0x6fcf84: ldur            x13, [fp, #-0x18]
    // 0x6fcf88: ldur            x12, [fp, #-0x20]
    // 0x6fcf8c: ldur            x11, [fp, #-0x28]
    // 0x6fcf90: ldur            x10, [fp, #-0x30]
    // 0x6fcf94: ldur            x9, [fp, #-0x38]
    // 0x6fcf98: ldur            x8, [fp, #-0x40]
    // 0x6fcf9c: ldur            x7, [fp, #-0x48]
    // 0x6fcfa0: ldur            x6, [fp, #-0x50]
    // 0x6fcfa4: ldur            x5, [fp, #-0x58]
    // 0x6fcfa8: ldur            x4, [fp, #-0x60]
    // 0x6fcfac: stur            x19, [fp, #-0x68]
    // 0x6fcfb0: r0 = LoadClassIdInstr(r3)
    //     0x6fcfb0: ldur            x0, [x3, #-1]
    //     0x6fcfb4: ubfx            x0, x0, #0xc, #0x14
    // 0x6fcfb8: mov             x1, x3
    // 0x6fcfbc: r2 = "payment_method"
    //     0x6fcfbc: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1b8] "payment_method"
    //     0x6fcfc0: ldr             x2, [x2, #0x1b8]
    // 0x6fcfc4: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fcfc4: sub             lr, x0, #0x114
    //     0x6fcfc8: ldr             lr, [x21, lr, lsl #3]
    //     0x6fcfcc: blr             lr
    // 0x6fcfd0: mov             x3, x0
    // 0x6fcfd4: r2 = Null
    //     0x6fcfd4: mov             x2, NULL
    // 0x6fcfd8: r1 = Null
    //     0x6fcfd8: mov             x1, NULL
    // 0x6fcfdc: stur            x3, [fp, #-0x70]
    // 0x6fcfe0: r4 = 60
    //     0x6fcfe0: movz            x4, #0x3c
    // 0x6fcfe4: branchIfSmi(r0, 0x6fcff0)
    //     0x6fcfe4: tbz             w0, #0, #0x6fcff0
    // 0x6fcfe8: r4 = LoadClassIdInstr(r0)
    //     0x6fcfe8: ldur            x4, [x0, #-1]
    //     0x6fcfec: ubfx            x4, x4, #0xc, #0x14
    // 0x6fcff0: sub             x4, x4, #0x5e
    // 0x6fcff4: cmp             x4, #1
    // 0x6fcff8: b.ls            #0x6fd00c
    // 0x6fcffc: r8 = String
    //     0x6fcffc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fd000: r3 = Null
    //     0x6fd000: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b1c0] Null
    //     0x6fd004: ldr             x3, [x3, #0x1c0]
    // 0x6fd008: r0 = String()
    //     0x6fd008: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fd00c: ldur            x1, [fp, #-0x70]
    // 0x6fd010: r0 = paymentMethodFromString()
    //     0x6fd010: bl              #0x6ff544  ; [package:nuonline/app/data/enums/payment_enum.dart] ::paymentMethodFromString
    // 0x6fd014: mov             x4, x0
    // 0x6fd018: ldur            x3, [fp, #-8]
    // 0x6fd01c: stur            x4, [fp, #-0x70]
    // 0x6fd020: r0 = LoadClassIdInstr(r3)
    //     0x6fd020: ldur            x0, [x3, #-1]
    //     0x6fd024: ubfx            x0, x0, #0xc, #0x14
    // 0x6fd028: mov             x1, x3
    // 0x6fd02c: r2 = "created_at"
    //     0x6fd02c: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1d0] "created_at"
    //     0x6fd030: ldr             x2, [x2, #0x1d0]
    // 0x6fd034: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fd034: sub             lr, x0, #0x114
    //     0x6fd038: ldr             lr, [x21, lr, lsl #3]
    //     0x6fd03c: blr             lr
    // 0x6fd040: mov             x3, x0
    // 0x6fd044: r2 = Null
    //     0x6fd044: mov             x2, NULL
    // 0x6fd048: r1 = Null
    //     0x6fd048: mov             x1, NULL
    // 0x6fd04c: stur            x3, [fp, #-0x78]
    // 0x6fd050: r4 = 60
    //     0x6fd050: movz            x4, #0x3c
    // 0x6fd054: branchIfSmi(r0, 0x6fd060)
    //     0x6fd054: tbz             w0, #0, #0x6fd060
    // 0x6fd058: r4 = LoadClassIdInstr(r0)
    //     0x6fd058: ldur            x4, [x0, #-1]
    //     0x6fd05c: ubfx            x4, x4, #0xc, #0x14
    // 0x6fd060: sub             x4, x4, #0x5e
    // 0x6fd064: cmp             x4, #1
    // 0x6fd068: b.ls            #0x6fd07c
    // 0x6fd06c: r8 = String
    //     0x6fd06c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fd070: r3 = Null
    //     0x6fd070: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b1d8] Null
    //     0x6fd074: ldr             x3, [x3, #0x1d8]
    // 0x6fd078: r0 = String()
    //     0x6fd078: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fd07c: ldur            x1, [fp, #-0x78]
    // 0x6fd080: r0 = toDateTime()
    //     0x6fd080: bl              #0x6fd22c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::toDateTime
    // 0x6fd084: mov             x4, x0
    // 0x6fd088: ldur            x3, [fp, #-8]
    // 0x6fd08c: stur            x4, [fp, #-0x78]
    // 0x6fd090: r0 = LoadClassIdInstr(r3)
    //     0x6fd090: ldur            x0, [x3, #-1]
    //     0x6fd094: ubfx            x0, x0, #0xc, #0x14
    // 0x6fd098: mov             x1, x3
    // 0x6fd09c: r2 = "updated_at"
    //     0x6fd09c: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x6fd0a0: ldr             x2, [x2, #0xe88]
    // 0x6fd0a4: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fd0a4: sub             lr, x0, #0x114
    //     0x6fd0a8: ldr             lr, [x21, lr, lsl #3]
    //     0x6fd0ac: blr             lr
    // 0x6fd0b0: mov             x3, x0
    // 0x6fd0b4: r2 = Null
    //     0x6fd0b4: mov             x2, NULL
    // 0x6fd0b8: r1 = Null
    //     0x6fd0b8: mov             x1, NULL
    // 0x6fd0bc: stur            x3, [fp, #-0x80]
    // 0x6fd0c0: r4 = 60
    //     0x6fd0c0: movz            x4, #0x3c
    // 0x6fd0c4: branchIfSmi(r0, 0x6fd0d0)
    //     0x6fd0c4: tbz             w0, #0, #0x6fd0d0
    // 0x6fd0c8: r4 = LoadClassIdInstr(r0)
    //     0x6fd0c8: ldur            x4, [x0, #-1]
    //     0x6fd0cc: ubfx            x4, x4, #0xc, #0x14
    // 0x6fd0d0: sub             x4, x4, #0x5e
    // 0x6fd0d4: cmp             x4, #1
    // 0x6fd0d8: b.ls            #0x6fd0ec
    // 0x6fd0dc: r8 = String
    //     0x6fd0dc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fd0e0: r3 = Null
    //     0x6fd0e0: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b1e8] Null
    //     0x6fd0e4: ldr             x3, [x3, #0x1e8]
    // 0x6fd0e8: r0 = String()
    //     0x6fd0e8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fd0ec: ldur            x1, [fp, #-0x80]
    // 0x6fd0f0: r0 = toDateTime()
    //     0x6fd0f0: bl              #0x6fd22c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::toDateTime
    // 0x6fd0f4: mov             x3, x0
    // 0x6fd0f8: ldur            x1, [fp, #-8]
    // 0x6fd0fc: stur            x3, [fp, #-0x80]
    // 0x6fd100: r0 = LoadClassIdInstr(r1)
    //     0x6fd100: ldur            x0, [x1, #-1]
    //     0x6fd104: ubfx            x0, x0, #0xc, #0x14
    // 0x6fd108: r2 = "expires_at"
    //     0x6fd108: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b1f8] "expires_at"
    //     0x6fd10c: ldr             x2, [x2, #0x1f8]
    // 0x6fd110: r0 = GDT[cid_x0 + -0x114]()
    //     0x6fd110: sub             lr, x0, #0x114
    //     0x6fd114: ldr             lr, [x21, lr, lsl #3]
    //     0x6fd118: blr             lr
    // 0x6fd11c: mov             x3, x0
    // 0x6fd120: r2 = Null
    //     0x6fd120: mov             x2, NULL
    // 0x6fd124: r1 = Null
    //     0x6fd124: mov             x1, NULL
    // 0x6fd128: stur            x3, [fp, #-8]
    // 0x6fd12c: r4 = 60
    //     0x6fd12c: movz            x4, #0x3c
    // 0x6fd130: branchIfSmi(r0, 0x6fd13c)
    //     0x6fd130: tbz             w0, #0, #0x6fd13c
    // 0x6fd134: r4 = LoadClassIdInstr(r0)
    //     0x6fd134: ldur            x4, [x0, #-1]
    //     0x6fd138: ubfx            x4, x4, #0xc, #0x14
    // 0x6fd13c: sub             x4, x4, #0x5e
    // 0x6fd140: cmp             x4, #1
    // 0x6fd144: b.ls            #0x6fd158
    // 0x6fd148: r8 = String
    //     0x6fd148: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6fd14c: r3 = Null
    //     0x6fd14c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b200] Null
    //     0x6fd150: ldr             x3, [x3, #0x200]
    // 0x6fd154: r0 = String()
    //     0x6fd154: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6fd158: ldur            x1, [fp, #-8]
    // 0x6fd15c: r0 = toDateTime()
    //     0x6fd15c: bl              #0x6fd22c  ; [package:nuonline/app/data/models/transaction.dart] Transaction::toDateTime
    // 0x6fd160: mov             x1, x0
    // 0x6fd164: ldur            x0, [fp, #-0x10]
    // 0x6fd168: stur            x1, [fp, #-8]
    // 0x6fd16c: r2 = LoadInt32Instr(r0)
    //     0x6fd16c: sbfx            x2, x0, #1, #0x1f
    //     0x6fd170: tbz             w0, #0, #0x6fd178
    //     0x6fd174: ldur            x2, [x0, #7]
    // 0x6fd178: stur            x2, [fp, #-0x88]
    // 0x6fd17c: r0 = Transaction()
    //     0x6fd17c: bl              #0x6fd220  ; AllocateTransactionStub -> Transaction (size=0x50)
    // 0x6fd180: ldur            x1, [fp, #-0x88]
    // 0x6fd184: StoreField: r0->field_7 = r1
    //     0x6fd184: stur            x1, [x0, #7]
    // 0x6fd188: ldur            x1, [fp, #-0x18]
    // 0x6fd18c: StoreField: r0->field_f = r1
    //     0x6fd18c: stur            w1, [x0, #0xf]
    // 0x6fd190: ldur            x1, [fp, #-0x28]
    // 0x6fd194: ArrayStore: r0[0] = r1  ; List_4
    //     0x6fd194: stur            w1, [x0, #0x17]
    // 0x6fd198: ldur            x1, [fp, #-0x30]
    // 0x6fd19c: r2 = LoadInt32Instr(r1)
    //     0x6fd19c: sbfx            x2, x1, #1, #0x1f
    //     0x6fd1a0: tbz             w1, #0, #0x6fd1a8
    //     0x6fd1a4: ldur            x2, [x1, #7]
    // 0x6fd1a8: StoreField: r0->field_1b = r2
    //     0x6fd1a8: stur            x2, [x0, #0x1b]
    // 0x6fd1ac: ldur            x1, [fp, #-0x38]
    // 0x6fd1b0: StoreField: r0->field_23 = r1
    //     0x6fd1b0: stur            w1, [x0, #0x23]
    // 0x6fd1b4: ldur            x1, [fp, #-0x40]
    // 0x6fd1b8: StoreField: r0->field_27 = r1
    //     0x6fd1b8: stur            w1, [x0, #0x27]
    // 0x6fd1bc: ldur            x1, [fp, #-0x48]
    // 0x6fd1c0: StoreField: r0->field_2b = r1
    //     0x6fd1c0: stur            w1, [x0, #0x2b]
    // 0x6fd1c4: ldur            x1, [fp, #-0x68]
    // 0x6fd1c8: StoreField: r0->field_3b = r1
    //     0x6fd1c8: stur            w1, [x0, #0x3b]
    // 0x6fd1cc: ldur            x1, [fp, #-0x70]
    // 0x6fd1d0: StoreField: r0->field_3f = r1
    //     0x6fd1d0: stur            w1, [x0, #0x3f]
    // 0x6fd1d4: ldur            x1, [fp, #-0x78]
    // 0x6fd1d8: StoreField: r0->field_43 = r1
    //     0x6fd1d8: stur            w1, [x0, #0x43]
    // 0x6fd1dc: ldur            x1, [fp, #-0x80]
    // 0x6fd1e0: StoreField: r0->field_47 = r1
    //     0x6fd1e0: stur            w1, [x0, #0x47]
    // 0x6fd1e4: ldur            x1, [fp, #-8]
    // 0x6fd1e8: StoreField: r0->field_4b = r1
    //     0x6fd1e8: stur            w1, [x0, #0x4b]
    // 0x6fd1ec: ldur            x1, [fp, #-0x20]
    // 0x6fd1f0: StoreField: r0->field_13 = r1
    //     0x6fd1f0: stur            w1, [x0, #0x13]
    // 0x6fd1f4: ldur            x1, [fp, #-0x50]
    // 0x6fd1f8: StoreField: r0->field_2f = r1
    //     0x6fd1f8: stur            w1, [x0, #0x2f]
    // 0x6fd1fc: ldur            x1, [fp, #-0x58]
    // 0x6fd200: StoreField: r0->field_33 = r1
    //     0x6fd200: stur            w1, [x0, #0x33]
    // 0x6fd204: ldur            x1, [fp, #-0x60]
    // 0x6fd208: StoreField: r0->field_37 = r1
    //     0x6fd208: stur            w1, [x0, #0x37]
    // 0x6fd20c: LeaveFrame
    //     0x6fd20c: mov             SP, fp
    //     0x6fd210: ldp             fp, lr, [SP], #0x10
    // 0x6fd214: ret
    //     0x6fd214: ret             
    // 0x6fd218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd21c: b               #0x6fcb08
  }
  static _ toDateTime(/* No info */) {
    // ** addr: 0x6fd22c, size: 0xa0
    // 0x6fd22c: EnterFrame
    //     0x6fd22c: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd230: mov             fp, SP
    // 0x6fd234: AllocStack(0x10)
    //     0x6fd234: sub             SP, SP, #0x10
    // 0x6fd238: CheckStackOverflow
    //     0x6fd238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd23c: cmp             SP, x16
    //     0x6fd240: b.ls            #0x6fd2c4
    // 0x6fd244: r0 = tryParse()
    //     0x6fd244: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x6fd248: cmp             w0, NULL
    // 0x6fd24c: b.ne            #0x6fd284
    // 0x6fd250: r0 = DateTime()
    //     0x6fd250: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x6fd254: mov             x1, x0
    // 0x6fd258: r0 = false
    //     0x6fd258: add             x0, NULL, #0x30  ; false
    // 0x6fd25c: stur            x1, [fp, #-8]
    // 0x6fd260: StoreField: r1->field_13 = r0
    //     0x6fd260: stur            w0, [x1, #0x13]
    // 0x6fd264: r0 = _getCurrentMicros()
    //     0x6fd264: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x6fd268: r1 = LoadInt32Instr(r0)
    //     0x6fd268: sbfx            x1, x0, #1, #0x1f
    //     0x6fd26c: tbz             w0, #0, #0x6fd274
    //     0x6fd270: ldur            x1, [x0, #7]
    // 0x6fd274: ldur            x0, [fp, #-8]
    // 0x6fd278: StoreField: r0->field_7 = r1
    //     0x6fd278: stur            x1, [x0, #7]
    // 0x6fd27c: mov             x2, x0
    // 0x6fd280: b               #0x6fd288
    // 0x6fd284: mov             x2, x0
    // 0x6fd288: stur            x2, [fp, #-8]
    // 0x6fd28c: r1 = "Asia/Jakarta"
    //     0x6fd28c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b210] "Asia/Jakarta"
    //     0x6fd290: ldr             x1, [x1, #0x210]
    // 0x6fd294: r0 = getLocation()
    //     0x6fd294: bl              #0x6fdf24  ; [package:timezone/src/env.dart] ::getLocation
    // 0x6fd298: stur            x0, [fp, #-0x10]
    // 0x6fd29c: r0 = TZDateTime()
    //     0x6fd29c: bl              #0x6fdf18  ; AllocateTZDateTimeStub -> TZDateTime (size=0x18)
    // 0x6fd2a0: mov             x1, x0
    // 0x6fd2a4: ldur            x2, [fp, #-8]
    // 0x6fd2a8: ldur            x3, [fp, #-0x10]
    // 0x6fd2ac: stur            x0, [fp, #-8]
    // 0x6fd2b0: r0 = TZDateTime.from()
    //     0x6fd2b0: bl              #0x6fd2cc  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime.from
    // 0x6fd2b4: ldur            x0, [fp, #-8]
    // 0x6fd2b8: LeaveFrame
    //     0x6fd2b8: mov             SP, fp
    //     0x6fd2bc: ldp             fp, lr, [SP], #0x10
    // 0x6fd2c0: ret
    //     0x6fd2c0: ret             
    // 0x6fd2c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd2c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd2c8: b               #0x6fd244
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x72b6e8, size: 0x188
    // 0x72b6e8: EnterFrame
    //     0x72b6e8: stp             fp, lr, [SP, #-0x10]!
    //     0x72b6ec: mov             fp, SP
    // 0x72b6f0: AllocStack(0x20)
    //     0x72b6f0: sub             SP, SP, #0x20
    // 0x72b6f4: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x72b6f4: mov             x3, x1
    //     0x72b6f8: stur            x1, [fp, #-8]
    // 0x72b6fc: CheckStackOverflow
    //     0x72b6fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72b700: cmp             SP, x16
    //     0x72b704: b.ls            #0x72b868
    // 0x72b708: mov             x0, x3
    // 0x72b70c: r2 = Null
    //     0x72b70c: mov             x2, NULL
    // 0x72b710: r1 = Null
    //     0x72b710: mov             x1, NULL
    // 0x72b714: cmp             w0, NULL
    // 0x72b718: b.eq            #0x72b7bc
    // 0x72b71c: branchIfSmi(r0, 0x72b7bc)
    //     0x72b71c: tbz             w0, #0, #0x72b7bc
    // 0x72b720: r3 = LoadClassIdInstr(r0)
    //     0x72b720: ldur            x3, [x0, #-1]
    //     0x72b724: ubfx            x3, x3, #0xc, #0x14
    // 0x72b728: r17 = 6718
    //     0x72b728: movz            x17, #0x1a3e
    // 0x72b72c: cmp             x3, x17
    // 0x72b730: b.eq            #0x72b7c4
    // 0x72b734: sub             x3, x3, #0x5a
    // 0x72b738: cmp             x3, #2
    // 0x72b73c: b.ls            #0x72b7c4
    // 0x72b740: r4 = LoadClassIdInstr(r0)
    //     0x72b740: ldur            x4, [x0, #-1]
    //     0x72b744: ubfx            x4, x4, #0xc, #0x14
    // 0x72b748: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x72b74c: ldr             x3, [x3, #0x18]
    // 0x72b750: ldr             x3, [x3, x4, lsl #3]
    // 0x72b754: LoadField: r3 = r3->field_2b
    //     0x72b754: ldur            w3, [x3, #0x2b]
    // 0x72b758: DecompressPointer r3
    //     0x72b758: add             x3, x3, HEAP, lsl #32
    // 0x72b75c: cmp             w3, NULL
    // 0x72b760: b.eq            #0x72b7bc
    // 0x72b764: LoadField: r3 = r3->field_f
    //     0x72b764: ldur            w3, [x3, #0xf]
    // 0x72b768: lsr             x3, x3, #3
    // 0x72b76c: r17 = 6718
    //     0x72b76c: movz            x17, #0x1a3e
    // 0x72b770: cmp             x3, x17
    // 0x72b774: b.eq            #0x72b7c4
    // 0x72b778: r3 = SubtypeTestCache
    //     0x72b778: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b098] SubtypeTestCache
    //     0x72b77c: ldr             x3, [x3, #0x98]
    // 0x72b780: r30 = Subtype1TestCacheStub
    //     0x72b780: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x72b784: LoadField: r30 = r30->field_7
    //     0x72b784: ldur            lr, [lr, #7]
    // 0x72b788: blr             lr
    // 0x72b78c: cmp             w7, NULL
    // 0x72b790: b.eq            #0x72b79c
    // 0x72b794: tbnz            w7, #4, #0x72b7bc
    // 0x72b798: b               #0x72b7c4
    // 0x72b79c: r8 = List
    //     0x72b79c: add             x8, PP, #0x2b, lsl #12  ; [pp+0x2b0a0] Type: List
    //     0x72b7a0: ldr             x8, [x8, #0xa0]
    // 0x72b7a4: r3 = SubtypeTestCache
    //     0x72b7a4: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b0a8] SubtypeTestCache
    //     0x72b7a8: ldr             x3, [x3, #0xa8]
    // 0x72b7ac: r30 = InstanceOfStub
    //     0x72b7ac: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x72b7b0: LoadField: r30 = r30->field_7
    //     0x72b7b0: ldur            lr, [lr, #7]
    // 0x72b7b4: blr             lr
    // 0x72b7b8: b               #0x72b7c8
    // 0x72b7bc: r0 = false
    //     0x72b7bc: add             x0, NULL, #0x30  ; false
    // 0x72b7c0: b               #0x72b7c8
    // 0x72b7c4: r0 = true
    //     0x72b7c4: add             x0, NULL, #0x20  ; true
    // 0x72b7c8: tbnz            w0, #4, #0x72b84c
    // 0x72b7cc: ldur            x0, [fp, #-8]
    // 0x72b7d0: r1 = Function '<anonymous closure>': static.
    //     0x72b7d0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b0b0] AnonymousClosure: static (0x72b870), in [package:nuonline/app/data/models/transaction.dart] Transaction::fromResponse (0x72b6e8)
    //     0x72b7d4: ldr             x1, [x1, #0xb0]
    // 0x72b7d8: r2 = Null
    //     0x72b7d8: mov             x2, NULL
    // 0x72b7dc: r0 = AllocateClosure()
    //     0x72b7dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x72b7e0: mov             x1, x0
    // 0x72b7e4: ldur            x0, [fp, #-8]
    // 0x72b7e8: r2 = LoadClassIdInstr(r0)
    //     0x72b7e8: ldur            x2, [x0, #-1]
    //     0x72b7ec: ubfx            x2, x2, #0xc, #0x14
    // 0x72b7f0: r16 = <Transaction>
    //     0x72b7f0: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x72b7f4: ldr             x16, [x16, #0x28]
    // 0x72b7f8: stp             x0, x16, [SP, #8]
    // 0x72b7fc: str             x1, [SP]
    // 0x72b800: mov             x0, x2
    // 0x72b804: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72b804: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72b808: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x72b808: movz            x17, #0xf28c
    //     0x72b80c: add             lr, x0, x17
    //     0x72b810: ldr             lr, [x21, lr, lsl #3]
    //     0x72b814: blr             lr
    // 0x72b818: r1 = LoadClassIdInstr(r0)
    //     0x72b818: ldur            x1, [x0, #-1]
    //     0x72b81c: ubfx            x1, x1, #0xc, #0x14
    // 0x72b820: mov             x16, x0
    // 0x72b824: mov             x0, x1
    // 0x72b828: mov             x1, x16
    // 0x72b82c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x72b82c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x72b830: r0 = GDT[cid_x0 + 0xd889]()
    //     0x72b830: movz            x17, #0xd889
    //     0x72b834: add             lr, x0, x17
    //     0x72b838: ldr             lr, [x21, lr, lsl #3]
    //     0x72b83c: blr             lr
    // 0x72b840: LeaveFrame
    //     0x72b840: mov             SP, fp
    //     0x72b844: ldp             fp, lr, [SP], #0x10
    // 0x72b848: ret
    //     0x72b848: ret             
    // 0x72b84c: r1 = <Transaction>
    //     0x72b84c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x72b850: ldr             x1, [x1, #0x28]
    // 0x72b854: r2 = 0
    //     0x72b854: movz            x2, #0
    // 0x72b858: r0 = _GrowableList()
    //     0x72b858: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x72b85c: LeaveFrame
    //     0x72b85c: mov             SP, fp
    //     0x72b860: ldp             fp, lr, [SP], #0x10
    // 0x72b864: ret
    //     0x72b864: ret             
    // 0x72b868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b868: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b86c: b               #0x72b708
  }
  [closure] static Transaction <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x72b870, size: 0x50
    // 0x72b870: EnterFrame
    //     0x72b870: stp             fp, lr, [SP, #-0x10]!
    //     0x72b874: mov             fp, SP
    // 0x72b878: CheckStackOverflow
    //     0x72b878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72b87c: cmp             SP, x16
    //     0x72b880: b.ls            #0x72b8b8
    // 0x72b884: ldr             x0, [fp, #0x10]
    // 0x72b888: r2 = Null
    //     0x72b888: mov             x2, NULL
    // 0x72b88c: r1 = Null
    //     0x72b88c: mov             x1, NULL
    // 0x72b890: r8 = Map<String, dynamic>
    //     0x72b890: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72b894: r3 = Null
    //     0x72b894: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b0b8] Null
    //     0x72b898: ldr             x3, [x3, #0xb8]
    // 0x72b89c: r0 = Map<String, dynamic>()
    //     0x72b89c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72b8a0: ldr             x2, [fp, #0x10]
    // 0x72b8a4: r1 = Null
    //     0x72b8a4: mov             x1, NULL
    // 0x72b8a8: r0 = Transaction.fromMap()
    //     0x72b8a8: bl              #0x6fcae8  ; [package:nuonline/app/data/models/transaction.dart] Transaction::Transaction.fromMap
    // 0x72b8ac: LeaveFrame
    //     0x72b8ac: mov             SP, fp
    //     0x72b8b0: ldp             fp, lr, [SP], #0x10
    // 0x72b8b4: ret
    //     0x72b8b4: ret             
    // 0x72b8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b8bc: b               #0x72b884
  }
  get _ isSuccess(/* No info */) {
    // ** addr: 0xae3b64, size: 0x58
    // 0xae3b64: EnterFrame
    //     0xae3b64: stp             fp, lr, [SP, #-0x10]!
    //     0xae3b68: mov             fp, SP
    // 0xae3b6c: AllocStack(0x10)
    //     0xae3b6c: sub             SP, SP, #0x10
    // 0xae3b70: CheckStackOverflow
    //     0xae3b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3b74: cmp             SP, x16
    //     0xae3b78: b.ls            #0xae3bb4
    // 0xae3b7c: LoadField: r0 = r1->field_27
    //     0xae3b7c: ldur            w0, [x1, #0x27]
    // 0xae3b80: DecompressPointer r0
    //     0xae3b80: add             x0, x0, HEAP, lsl #32
    // 0xae3b84: r1 = LoadClassIdInstr(r0)
    //     0xae3b84: ldur            x1, [x0, #-1]
    //     0xae3b88: ubfx            x1, x1, #0xc, #0x14
    // 0xae3b8c: r16 = "SUCCESS"
    //     0xae3b8c: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xae3b90: ldr             x16, [x16, #0x2c0]
    // 0xae3b94: stp             x16, x0, [SP]
    // 0xae3b98: mov             x0, x1
    // 0xae3b9c: mov             lr, x0
    // 0xae3ba0: ldr             lr, [x21, lr, lsl #3]
    // 0xae3ba4: blr             lr
    // 0xae3ba8: LeaveFrame
    //     0xae3ba8: mov             SP, fp
    //     0xae3bac: ldp             fp, lr, [SP], #0x10
    // 0xae3bb0: ret
    //     0xae3bb0: ret             
    // 0xae3bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3bb8: b               #0xae3b7c
  }
  get _ isPending(/* No info */) {
    // ** addr: 0xae3bc8, size: 0x58
    // 0xae3bc8: EnterFrame
    //     0xae3bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xae3bcc: mov             fp, SP
    // 0xae3bd0: AllocStack(0x10)
    //     0xae3bd0: sub             SP, SP, #0x10
    // 0xae3bd4: CheckStackOverflow
    //     0xae3bd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3bd8: cmp             SP, x16
    //     0xae3bdc: b.ls            #0xae3c18
    // 0xae3be0: LoadField: r0 = r1->field_27
    //     0xae3be0: ldur            w0, [x1, #0x27]
    // 0xae3be4: DecompressPointer r0
    //     0xae3be4: add             x0, x0, HEAP, lsl #32
    // 0xae3be8: r1 = LoadClassIdInstr(r0)
    //     0xae3be8: ldur            x1, [x0, #-1]
    //     0xae3bec: ubfx            x1, x1, #0xc, #0x14
    // 0xae3bf0: r16 = "PENDING"
    //     0xae3bf0: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xae3bf4: ldr             x16, [x16, #0x2c8]
    // 0xae3bf8: stp             x16, x0, [SP]
    // 0xae3bfc: mov             x0, x1
    // 0xae3c00: mov             lr, x0
    // 0xae3c04: ldr             lr, [x21, lr, lsl #3]
    // 0xae3c08: blr             lr
    // 0xae3c0c: LeaveFrame
    //     0xae3c0c: mov             SP, fp
    //     0xae3c10: ldp             fp, lr, [SP], #0x10
    // 0xae3c14: ret
    //     0xae3c14: ret             
    // 0xae3c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3c1c: b               #0xae3be0
  }
  get _ title(/* No info */) {
    // ** addr: 0xb8e3d4, size: 0x2cc
    // 0xb8e3d4: EnterFrame
    //     0xb8e3d4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e3d8: mov             fp, SP
    // 0xb8e3dc: AllocStack(0x48)
    //     0xb8e3dc: sub             SP, SP, #0x48
    // 0xb8e3e0: SetupParameters(Transaction this /* r1 => r0, fp-0x8 */)
    //     0xb8e3e0: mov             x0, x1
    //     0xb8e3e4: stur            x1, [fp, #-8]
    // 0xb8e3e8: CheckStackOverflow
    //     0xb8e3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e3ec: cmp             SP, x16
    //     0xb8e3f0: b.ls            #0xb8e698
    // 0xb8e3f4: r1 = <String?>
    //     0xb8e3f4: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb8e3f8: r2 = 0
    //     0xb8e3f8: movz            x2, #0
    // 0xb8e3fc: r0 = _GrowableList()
    //     0xb8e3fc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8e400: r1 = Null
    //     0xb8e400: mov             x1, NULL
    // 0xb8e404: r2 = 4
    //     0xb8e404: movz            x2, #0x4
    // 0xb8e408: stur            x0, [fp, #-0x10]
    // 0xb8e40c: r0 = AllocateArray()
    //     0xb8e40c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8e410: stur            x0, [fp, #-0x18]
    // 0xb8e414: r16 = 8
    //     0xb8e414: movz            x16, #0x8
    // 0xb8e418: StoreField: r0->field_f = r16
    //     0xb8e418: stur            w16, [x0, #0xf]
    // 0xb8e41c: r16 = 12
    //     0xb8e41c: movz            x16, #0xc
    // 0xb8e420: StoreField: r0->field_13 = r16
    //     0xb8e420: stur            w16, [x0, #0x13]
    // 0xb8e424: r1 = <int>
    //     0xb8e424: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb8e428: r0 = AllocateGrowableArray()
    //     0xb8e428: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8e42c: mov             x2, x0
    // 0xb8e430: ldur            x0, [fp, #-0x18]
    // 0xb8e434: StoreField: r2->field_f = r0
    //     0xb8e434: stur            w0, [x2, #0xf]
    // 0xb8e438: r0 = 4
    //     0xb8e438: movz            x0, #0x4
    // 0xb8e43c: StoreField: r2->field_b = r0
    //     0xb8e43c: stur            w0, [x2, #0xb]
    // 0xb8e440: ldur            x3, [fp, #-8]
    // 0xb8e444: LoadField: r4 = r3->field_2b
    //     0xb8e444: ldur            w4, [x3, #0x2b]
    // 0xb8e448: DecompressPointer r4
    //     0xb8e448: add             x4, x4, HEAP, lsl #32
    // 0xb8e44c: stur            x4, [fp, #-0x28]
    // 0xb8e450: LoadField: r5 = r4->field_13
    //     0xb8e450: ldur            w5, [x4, #0x13]
    // 0xb8e454: DecompressPointer r5
    //     0xb8e454: add             x5, x5, HEAP, lsl #32
    // 0xb8e458: stur            x5, [fp, #-0x18]
    // 0xb8e45c: LoadField: r6 = r5->field_7
    //     0xb8e45c: ldur            x6, [x5, #7]
    // 0xb8e460: stur            x6, [fp, #-0x20]
    // 0xb8e464: r0 = BoxInt64Instr(r6)
    //     0xb8e464: sbfiz           x0, x6, #1, #0x1f
    //     0xb8e468: cmp             x6, x0, asr #1
    //     0xb8e46c: b.eq            #0xb8e478
    //     0xb8e470: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8e474: stur            x6, [x0, #7]
    // 0xb8e478: mov             x1, x2
    // 0xb8e47c: mov             x2, x0
    // 0xb8e480: r0 = contains()
    //     0xb8e480: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xb8e484: tbz             w0, #4, #0xb8e53c
    // 0xb8e488: ldur            x0, [fp, #-0x28]
    // 0xb8e48c: r1 = <String>
    //     0xb8e48c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb8e490: r2 = 0
    //     0xb8e490: movz            x2, #0
    // 0xb8e494: r0 = _GrowableList()
    //     0xb8e494: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8e498: mov             x2, x0
    // 0xb8e49c: ldur            x0, [fp, #-0x28]
    // 0xb8e4a0: stur            x2, [fp, #-0x40]
    // 0xb8e4a4: LoadField: r1 = r0->field_7
    //     0xb8e4a4: ldur            x1, [x0, #7]
    // 0xb8e4a8: cmp             x1, #0xd
    // 0xb8e4ac: b.eq            #0xb8e534
    // 0xb8e4b0: ldur            x1, [fp, #-0x18]
    // 0xb8e4b4: LoadField: r3 = r1->field_f
    //     0xb8e4b4: ldur            w3, [x1, #0xf]
    // 0xb8e4b8: DecompressPointer r3
    //     0xb8e4b8: add             x3, x3, HEAP, lsl #32
    // 0xb8e4bc: stur            x3, [fp, #-0x38]
    // 0xb8e4c0: LoadField: r1 = r2->field_b
    //     0xb8e4c0: ldur            w1, [x2, #0xb]
    // 0xb8e4c4: LoadField: r4 = r2->field_f
    //     0xb8e4c4: ldur            w4, [x2, #0xf]
    // 0xb8e4c8: DecompressPointer r4
    //     0xb8e4c8: add             x4, x4, HEAP, lsl #32
    // 0xb8e4cc: LoadField: r5 = r4->field_b
    //     0xb8e4cc: ldur            w5, [x4, #0xb]
    // 0xb8e4d0: r4 = LoadInt32Instr(r1)
    //     0xb8e4d0: sbfx            x4, x1, #1, #0x1f
    // 0xb8e4d4: stur            x4, [fp, #-0x30]
    // 0xb8e4d8: r1 = LoadInt32Instr(r5)
    //     0xb8e4d8: sbfx            x1, x5, #1, #0x1f
    // 0xb8e4dc: cmp             x4, x1
    // 0xb8e4e0: b.ne            #0xb8e4ec
    // 0xb8e4e4: mov             x1, x2
    // 0xb8e4e8: r0 = _growToNextCapacity()
    //     0xb8e4e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e4ec: ldur            x2, [fp, #-0x40]
    // 0xb8e4f0: ldur            x3, [fp, #-0x30]
    // 0xb8e4f4: add             x0, x3, #1
    // 0xb8e4f8: lsl             x1, x0, #1
    // 0xb8e4fc: StoreField: r2->field_b = r1
    //     0xb8e4fc: stur            w1, [x2, #0xb]
    // 0xb8e500: LoadField: r1 = r2->field_f
    //     0xb8e500: ldur            w1, [x2, #0xf]
    // 0xb8e504: DecompressPointer r1
    //     0xb8e504: add             x1, x1, HEAP, lsl #32
    // 0xb8e508: ldur            x0, [fp, #-0x38]
    // 0xb8e50c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8e50c: add             x25, x1, x3, lsl #2
    //     0xb8e510: add             x25, x25, #0xf
    //     0xb8e514: str             w0, [x25]
    //     0xb8e518: tbz             w0, #0, #0xb8e534
    //     0xb8e51c: ldurb           w16, [x1, #-1]
    //     0xb8e520: ldurb           w17, [x0, #-1]
    //     0xb8e524: and             x16, x17, x16, lsr #2
    //     0xb8e528: tst             x16, HEAP, lsr #32
    //     0xb8e52c: b.eq            #0xb8e534
    //     0xb8e530: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8e534: ldur            x1, [fp, #-0x10]
    // 0xb8e538: r0 = addAll()
    //     0xb8e538: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb8e53c: ldur            x0, [fp, #-0x20]
    // 0xb8e540: cmp             x0, #4
    // 0xb8e544: b.ne            #0xb8e5d8
    // 0xb8e548: ldur            x0, [fp, #-8]
    // 0xb8e54c: ldur            x2, [fp, #-0x10]
    // 0xb8e550: LoadField: r3 = r0->field_37
    //     0xb8e550: ldur            w3, [x0, #0x37]
    // 0xb8e554: DecompressPointer r3
    //     0xb8e554: add             x3, x3, HEAP, lsl #32
    // 0xb8e558: stur            x3, [fp, #-0x18]
    // 0xb8e55c: LoadField: r0 = r2->field_b
    //     0xb8e55c: ldur            w0, [x2, #0xb]
    // 0xb8e560: LoadField: r1 = r2->field_f
    //     0xb8e560: ldur            w1, [x2, #0xf]
    // 0xb8e564: DecompressPointer r1
    //     0xb8e564: add             x1, x1, HEAP, lsl #32
    // 0xb8e568: LoadField: r4 = r1->field_b
    //     0xb8e568: ldur            w4, [x1, #0xb]
    // 0xb8e56c: r5 = LoadInt32Instr(r0)
    //     0xb8e56c: sbfx            x5, x0, #1, #0x1f
    // 0xb8e570: stur            x5, [fp, #-0x20]
    // 0xb8e574: r0 = LoadInt32Instr(r4)
    //     0xb8e574: sbfx            x0, x4, #1, #0x1f
    // 0xb8e578: cmp             x5, x0
    // 0xb8e57c: b.ne            #0xb8e588
    // 0xb8e580: mov             x1, x2
    // 0xb8e584: r0 = _growToNextCapacity()
    //     0xb8e584: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e588: ldur            x4, [fp, #-0x10]
    // 0xb8e58c: ldur            x2, [fp, #-0x20]
    // 0xb8e590: add             x0, x2, #1
    // 0xb8e594: lsl             x1, x0, #1
    // 0xb8e598: StoreField: r4->field_b = r1
    //     0xb8e598: stur            w1, [x4, #0xb]
    // 0xb8e59c: LoadField: r1 = r4->field_f
    //     0xb8e59c: ldur            w1, [x4, #0xf]
    // 0xb8e5a0: DecompressPointer r1
    //     0xb8e5a0: add             x1, x1, HEAP, lsl #32
    // 0xb8e5a4: ldur            x0, [fp, #-0x18]
    // 0xb8e5a8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8e5a8: add             x25, x1, x2, lsl #2
    //     0xb8e5ac: add             x25, x25, #0xf
    //     0xb8e5b0: str             w0, [x25]
    //     0xb8e5b4: tbz             w0, #0, #0xb8e5d0
    //     0xb8e5b8: ldurb           w16, [x1, #-1]
    //     0xb8e5bc: ldurb           w17, [x0, #-1]
    //     0xb8e5c0: and             x16, x17, x16, lsr #2
    //     0xb8e5c4: tst             x16, HEAP, lsr #32
    //     0xb8e5c8: b.eq            #0xb8e5d0
    //     0xb8e5cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8e5d0: mov             x2, x4
    // 0xb8e5d4: b               #0xb8e678
    // 0xb8e5d8: ldur            x4, [fp, #-0x10]
    // 0xb8e5dc: ldur            x0, [fp, #-0x28]
    // 0xb8e5e0: LoadField: r1 = r0->field_f
    //     0xb8e5e0: ldur            w1, [x0, #0xf]
    // 0xb8e5e4: DecompressPointer r1
    //     0xb8e5e4: add             x1, x1, HEAP, lsl #32
    // 0xb8e5e8: r2 = "Kontribusi"
    //     0xb8e5e8: add             x2, PP, #0x35, lsl #12  ; [pp+0x35020] "Kontribusi"
    //     0xb8e5ec: ldr             x2, [x2, #0x20]
    // 0xb8e5f0: r3 = ""
    //     0xb8e5f0: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb8e5f4: r0 = replaceAll()
    //     0xb8e5f4: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb8e5f8: mov             x2, x0
    // 0xb8e5fc: ldur            x0, [fp, #-0x10]
    // 0xb8e600: stur            x2, [fp, #-8]
    // 0xb8e604: LoadField: r1 = r0->field_b
    //     0xb8e604: ldur            w1, [x0, #0xb]
    // 0xb8e608: LoadField: r3 = r0->field_f
    //     0xb8e608: ldur            w3, [x0, #0xf]
    // 0xb8e60c: DecompressPointer r3
    //     0xb8e60c: add             x3, x3, HEAP, lsl #32
    // 0xb8e610: LoadField: r4 = r3->field_b
    //     0xb8e610: ldur            w4, [x3, #0xb]
    // 0xb8e614: r3 = LoadInt32Instr(r1)
    //     0xb8e614: sbfx            x3, x1, #1, #0x1f
    // 0xb8e618: stur            x3, [fp, #-0x20]
    // 0xb8e61c: r1 = LoadInt32Instr(r4)
    //     0xb8e61c: sbfx            x1, x4, #1, #0x1f
    // 0xb8e620: cmp             x3, x1
    // 0xb8e624: b.ne            #0xb8e630
    // 0xb8e628: mov             x1, x0
    // 0xb8e62c: r0 = _growToNextCapacity()
    //     0xb8e62c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8e630: ldur            x2, [fp, #-0x10]
    // 0xb8e634: ldur            x3, [fp, #-0x20]
    // 0xb8e638: add             x0, x3, #1
    // 0xb8e63c: lsl             x1, x0, #1
    // 0xb8e640: StoreField: r2->field_b = r1
    //     0xb8e640: stur            w1, [x2, #0xb]
    // 0xb8e644: LoadField: r1 = r2->field_f
    //     0xb8e644: ldur            w1, [x2, #0xf]
    // 0xb8e648: DecompressPointer r1
    //     0xb8e648: add             x1, x1, HEAP, lsl #32
    // 0xb8e64c: ldur            x0, [fp, #-8]
    // 0xb8e650: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8e650: add             x25, x1, x3, lsl #2
    //     0xb8e654: add             x25, x25, #0xf
    //     0xb8e658: str             w0, [x25]
    //     0xb8e65c: tbz             w0, #0, #0xb8e678
    //     0xb8e660: ldurb           w16, [x1, #-1]
    //     0xb8e664: ldurb           w17, [x0, #-1]
    //     0xb8e668: and             x16, x17, x16, lsr #2
    //     0xb8e66c: tst             x16, HEAP, lsr #32
    //     0xb8e670: b.eq            #0xb8e678
    //     0xb8e674: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb8e678: r16 = " "
    //     0xb8e678: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb8e67c: str             x16, [SP]
    // 0xb8e680: mov             x1, x2
    // 0xb8e684: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb8e684: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb8e688: r0 = join()
    //     0xb8e688: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb8e68c: LeaveFrame
    //     0xb8e68c: mov             SP, fp
    //     0xb8e690: ldp             fp, lr, [SP], #0x10
    // 0xb8e694: ret
    //     0xb8e694: ret             
    // 0xb8e698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e698: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e69c: b               #0xb8e3f4
  }
  get _ iconUrl(/* No info */) {
    // ** addr: 0xb8e6a0, size: 0x114
    // 0xb8e6a0: EnterFrame
    //     0xb8e6a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e6a4: mov             fp, SP
    // 0xb8e6a8: AllocStack(0x8)
    //     0xb8e6a8: sub             SP, SP, #8
    // 0xb8e6ac: CheckStackOverflow
    //     0xb8e6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e6b0: cmp             SP, x16
    //     0xb8e6b4: b.ls            #0xb8e7ac
    // 0xb8e6b8: LoadField: r0 = r1->field_2b
    //     0xb8e6b8: ldur            w0, [x1, #0x2b]
    // 0xb8e6bc: DecompressPointer r0
    //     0xb8e6bc: add             x0, x0, HEAP, lsl #32
    // 0xb8e6c0: stur            x0, [fp, #-8]
    // 0xb8e6c4: LoadField: r1 = r0->field_13
    //     0xb8e6c4: ldur            w1, [x0, #0x13]
    // 0xb8e6c8: DecompressPointer r1
    //     0xb8e6c8: add             x1, x1, HEAP, lsl #32
    // 0xb8e6cc: r0 = type()
    //     0xb8e6cc: bl              #0xb8e7b4  ; [package:nuonline/app/data/models/transaction.dart] DonationCategory::type
    // 0xb8e6d0: r16 = Instance_PaymentType
    //     0xb8e6d0: add             x16, PP, #0x24, lsl #12  ; [pp+0x245a8] Obj!PaymentType@e30ef1
    //     0xb8e6d4: ldr             x16, [x16, #0x5a8]
    // 0xb8e6d8: cmp             w0, w16
    // 0xb8e6dc: b.ne            #0xb8e6ec
    // 0xb8e6e0: r0 = "assets/images/feature/donasi.svg"
    //     0xb8e6e0: add             x0, PP, #0x30, lsl #12  ; [pp+0x302e0] "assets/images/feature/donasi.svg"
    //     0xb8e6e4: ldr             x0, [x0, #0x2e0]
    // 0xb8e6e8: b               #0xb8e7a0
    // 0xb8e6ec: r16 = Instance_PaymentType
    //     0xb8e6ec: add             x16, PP, #0x24, lsl #12  ; [pp+0x245c0] Obj!PaymentType@e30ec1
    //     0xb8e6f0: ldr             x16, [x16, #0x5c0]
    // 0xb8e6f4: cmp             w0, w16
    // 0xb8e6f8: b.ne            #0xb8e728
    // 0xb8e6fc: ldur            x1, [fp, #-8]
    // 0xb8e700: LoadField: r2 = r1->field_7
    //     0xb8e700: ldur            x2, [x1, #7]
    // 0xb8e704: cmp             x2, #2
    // 0xb8e708: b.ne            #0xb8e718
    // 0xb8e70c: r1 = "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xb8e70c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30310] "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xb8e710: ldr             x1, [x1, #0x310]
    // 0xb8e714: b               #0xb8e720
    // 0xb8e718: r1 = "assets/images/feature/zakat_mal.svg"
    //     0xb8e718: add             x1, PP, #0x30, lsl #12  ; [pp+0x302f8] "assets/images/feature/zakat_mal.svg"
    //     0xb8e71c: ldr             x1, [x1, #0x2f8]
    // 0xb8e720: mov             x0, x1
    // 0xb8e724: b               #0xb8e7a0
    // 0xb8e728: r16 = Instance_PaymentType
    //     0xb8e728: add             x16, PP, #0x24, lsl #12  ; [pp+0x245d8] Obj!PaymentType@e30e31
    //     0xb8e72c: ldr             x16, [x16, #0x5d8]
    // 0xb8e730: cmp             w0, w16
    // 0xb8e734: b.ne            #0xb8e744
    // 0xb8e738: r0 = "assets/images/feature/galang_dana.svg"
    //     0xb8e738: add             x0, PP, #0x30, lsl #12  ; [pp+0x30350] "assets/images/feature/galang_dana.svg"
    //     0xb8e73c: ldr             x0, [x0, #0x350]
    // 0xb8e740: b               #0xb8e7a0
    // 0xb8e744: r16 = Instance_PaymentType
    //     0xb8e744: add             x16, PP, #0x24, lsl #12  ; [pp+0x245f0] Obj!PaymentType@e30e01
    //     0xb8e748: ldr             x16, [x16, #0x5f0]
    // 0xb8e74c: cmp             w0, w16
    // 0xb8e750: b.ne            #0xb8e760
    // 0xb8e754: r0 = "assets/images/feature/qurban.svg"
    //     0xb8e754: add             x0, PP, #0x30, lsl #12  ; [pp+0x30320] "assets/images/feature/qurban.svg"
    //     0xb8e758: ldr             x0, [x0, #0x320]
    // 0xb8e75c: b               #0xb8e7a0
    // 0xb8e760: r16 = Instance_PaymentType
    //     0xb8e760: add             x16, PP, #0x24, lsl #12  ; [pp+0x24608] Obj!PaymentType@e30e61
    //     0xb8e764: ldr             x16, [x16, #0x608]
    // 0xb8e768: cmp             w0, w16
    // 0xb8e76c: b.ne            #0xb8e77c
    // 0xb8e770: r0 = "assets/images/feature/koin_nu.svg"
    //     0xb8e770: add             x0, PP, #0x30, lsl #12  ; [pp+0x30338] "assets/images/feature/koin_nu.svg"
    //     0xb8e774: ldr             x0, [x0, #0x338]
    // 0xb8e778: b               #0xb8e7a0
    // 0xb8e77c: r16 = Instance_PaymentType
    //     0xb8e77c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24638] Obj!PaymentType@e30e91
    //     0xb8e780: ldr             x16, [x16, #0x638]
    // 0xb8e784: cmp             w0, w16
    // 0xb8e788: b.ne            #0xb8e798
    // 0xb8e78c: r0 = "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xb8e78c: add             x0, PP, #0x30, lsl #12  ; [pp+0x30310] "assets/images/feature/zakat_fitrah_fidyah.svg"
    //     0xb8e790: ldr             x0, [x0, #0x310]
    // 0xb8e794: b               #0xb8e7a0
    // 0xb8e798: r0 = "assets/images/feature/donasi.svg"
    //     0xb8e798: add             x0, PP, #0x30, lsl #12  ; [pp+0x302e0] "assets/images/feature/donasi.svg"
    //     0xb8e79c: ldr             x0, [x0, #0x2e0]
    // 0xb8e7a0: LeaveFrame
    //     0xb8e7a0: mov             SP, fp
    //     0xb8e7a4: ldp             fp, lr, [SP], #0x10
    // 0xb8e7a8: ret
    //     0xb8e7a8: ret             
    // 0xb8e7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e7ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e7b0: b               #0xb8e6b8
  }
  get _ progressTextColor(/* No info */) {
    // ** addr: 0xb8e85c, size: 0x148
    // 0xb8e85c: EnterFrame
    //     0xb8e85c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e860: mov             fp, SP
    // 0xb8e864: AllocStack(0x28)
    //     0xb8e864: sub             SP, SP, #0x28
    // 0xb8e868: SetupParameters(Transaction this /* r1 => r1, fp-0x8 */)
    //     0xb8e868: stur            x1, [fp, #-8]
    // 0xb8e86c: CheckStackOverflow
    //     0xb8e86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e870: cmp             SP, x16
    //     0xb8e874: b.ls            #0xb8e99c
    // 0xb8e878: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8e878: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8e87c: ldr             x0, [x0, #0x2670]
    //     0xb8e880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8e884: cmp             w0, w16
    //     0xb8e888: b.ne            #0xb8e894
    //     0xb8e88c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8e890: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8e894: r0 = GetNavigation.isDarkMode()
    //     0xb8e894: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb8e898: mov             x1, x0
    // 0xb8e89c: ldur            x0, [fp, #-8]
    // 0xb8e8a0: stur            x1, [fp, #-0x18]
    // 0xb8e8a4: LoadField: r2 = r0->field_27
    //     0xb8e8a4: ldur            w2, [x0, #0x27]
    // 0xb8e8a8: DecompressPointer r2
    //     0xb8e8a8: add             x2, x2, HEAP, lsl #32
    // 0xb8e8ac: stur            x2, [fp, #-0x10]
    // 0xb8e8b0: r16 = "SUCCESS"
    //     0xb8e8b0: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8e8b4: ldr             x16, [x16, #0x2c0]
    // 0xb8e8b8: stp             x2, x16, [SP]
    // 0xb8e8bc: r0 = ==()
    //     0xb8e8bc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8e8c0: tbnz            w0, #4, #0xb8e8ec
    // 0xb8e8c4: ldur            x0, [fp, #-0x18]
    // 0xb8e8c8: tbnz            w0, #4, #0xb8e8d8
    // 0xb8e8cc: r0 = Instance_Color
    //     0xb8e8cc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35038] Obj!Color@e2b4a1
    //     0xb8e8d0: ldr             x0, [x0, #0x38]
    // 0xb8e8d4: b               #0xb8e8e0
    // 0xb8e8d8: r0 = Instance_Color
    //     0xb8e8d8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35040] Obj!Color@e27e11
    //     0xb8e8dc: ldr             x0, [x0, #0x40]
    // 0xb8e8e0: LeaveFrame
    //     0xb8e8e0: mov             SP, fp
    //     0xb8e8e4: ldp             fp, lr, [SP], #0x10
    // 0xb8e8e8: ret
    //     0xb8e8e8: ret             
    // 0xb8e8ec: ldur            x0, [fp, #-0x18]
    // 0xb8e8f0: r16 = "PENDING"
    //     0xb8e8f0: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8e8f4: ldr             x16, [x16, #0x2c8]
    // 0xb8e8f8: ldur            lr, [fp, #-0x10]
    // 0xb8e8fc: stp             lr, x16, [SP]
    // 0xb8e900: r0 = ==()
    //     0xb8e900: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8e904: tbnz            w0, #4, #0xb8e930
    // 0xb8e908: ldur            x0, [fp, #-0x18]
    // 0xb8e90c: tbnz            w0, #4, #0xb8e91c
    // 0xb8e910: r0 = Instance_Color
    //     0xb8e910: add             x0, PP, #0x35, lsl #12  ; [pp+0x35048] Obj!Color@e2b471
    //     0xb8e914: ldr             x0, [x0, #0x48]
    // 0xb8e918: b               #0xb8e924
    // 0xb8e91c: r0 = Instance_Color
    //     0xb8e91c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35050] Obj!Color@e2b441
    //     0xb8e920: ldr             x0, [x0, #0x50]
    // 0xb8e924: LeaveFrame
    //     0xb8e924: mov             SP, fp
    //     0xb8e928: ldp             fp, lr, [SP], #0x10
    // 0xb8e92c: ret
    //     0xb8e92c: ret             
    // 0xb8e930: ldur            x0, [fp, #-0x18]
    // 0xb8e934: r16 = "EXPIRED"
    //     0xb8e934: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8e938: ldr             x16, [x16, #0xf88]
    // 0xb8e93c: ldur            lr, [fp, #-0x10]
    // 0xb8e940: stp             lr, x16, [SP]
    // 0xb8e944: r0 = ==()
    //     0xb8e944: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8e948: tbnz            w0, #4, #0xb8e974
    // 0xb8e94c: ldur            x1, [fp, #-0x18]
    // 0xb8e950: tbnz            w1, #4, #0xb8e960
    // 0xb8e954: r0 = Instance_Color
    //     0xb8e954: add             x0, PP, #0x35, lsl #12  ; [pp+0x35058] Obj!Color@e2b411
    //     0xb8e958: ldr             x0, [x0, #0x58]
    // 0xb8e95c: b               #0xb8e968
    // 0xb8e960: r0 = Instance_Color
    //     0xb8e960: add             x0, PP, #0x35, lsl #12  ; [pp+0x35060] Obj!Color@e28ec1
    //     0xb8e964: ldr             x0, [x0, #0x60]
    // 0xb8e968: LeaveFrame
    //     0xb8e968: mov             SP, fp
    //     0xb8e96c: ldp             fp, lr, [SP], #0x10
    // 0xb8e970: ret
    //     0xb8e970: ret             
    // 0xb8e974: ldur            x1, [fp, #-0x18]
    // 0xb8e978: tbnz            w1, #4, #0xb8e988
    // 0xb8e97c: r0 = Instance_Color
    //     0xb8e97c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35068] Obj!Color@e2b3e1
    //     0xb8e980: ldr             x0, [x0, #0x68]
    // 0xb8e984: b               #0xb8e990
    // 0xb8e988: r0 = Instance_Color
    //     0xb8e988: add             x0, PP, #0x35, lsl #12  ; [pp+0x35070] Obj!Color@e2b1a1
    //     0xb8e98c: ldr             x0, [x0, #0x70]
    // 0xb8e990: LeaveFrame
    //     0xb8e990: mov             SP, fp
    //     0xb8e994: ldp             fp, lr, [SP], #0x10
    // 0xb8e998: ret
    //     0xb8e998: ret             
    // 0xb8e99c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e99c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e9a0: b               #0xb8e878
  }
  get _ progressBorderColor(/* No info */) {
    // ** addr: 0xb8e9a4, size: 0x148
    // 0xb8e9a4: EnterFrame
    //     0xb8e9a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e9a8: mov             fp, SP
    // 0xb8e9ac: AllocStack(0x28)
    //     0xb8e9ac: sub             SP, SP, #0x28
    // 0xb8e9b0: SetupParameters(Transaction this /* r1 => r1, fp-0x8 */)
    //     0xb8e9b0: stur            x1, [fp, #-8]
    // 0xb8e9b4: CheckStackOverflow
    //     0xb8e9b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e9b8: cmp             SP, x16
    //     0xb8e9bc: b.ls            #0xb8eae4
    // 0xb8e9c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8e9c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8e9c4: ldr             x0, [x0, #0x2670]
    //     0xb8e9c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8e9cc: cmp             w0, w16
    //     0xb8e9d0: b.ne            #0xb8e9dc
    //     0xb8e9d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8e9d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8e9dc: r0 = GetNavigation.isDarkMode()
    //     0xb8e9dc: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb8e9e0: mov             x1, x0
    // 0xb8e9e4: ldur            x0, [fp, #-8]
    // 0xb8e9e8: stur            x1, [fp, #-0x18]
    // 0xb8e9ec: LoadField: r2 = r0->field_27
    //     0xb8e9ec: ldur            w2, [x0, #0x27]
    // 0xb8e9f0: DecompressPointer r2
    //     0xb8e9f0: add             x2, x2, HEAP, lsl #32
    // 0xb8e9f4: stur            x2, [fp, #-0x10]
    // 0xb8e9f8: r16 = "SUCCESS"
    //     0xb8e9f8: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8e9fc: ldr             x16, [x16, #0x2c0]
    // 0xb8ea00: stp             x2, x16, [SP]
    // 0xb8ea04: r0 = ==()
    //     0xb8ea04: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8ea08: tbnz            w0, #4, #0xb8ea34
    // 0xb8ea0c: ldur            x0, [fp, #-0x18]
    // 0xb8ea10: tbnz            w0, #4, #0xb8ea20
    // 0xb8ea14: r0 = Instance_Color
    //     0xb8ea14: add             x0, PP, #0x35, lsl #12  ; [pp+0x35078] Obj!Color@e2b5c1
    //     0xb8ea18: ldr             x0, [x0, #0x78]
    // 0xb8ea1c: b               #0xb8ea28
    // 0xb8ea20: r0 = Instance_Color
    //     0xb8ea20: add             x0, PP, #0x35, lsl #12  ; [pp+0x35080] Obj!Color@e27ea1
    //     0xb8ea24: ldr             x0, [x0, #0x80]
    // 0xb8ea28: LeaveFrame
    //     0xb8ea28: mov             SP, fp
    //     0xb8ea2c: ldp             fp, lr, [SP], #0x10
    // 0xb8ea30: ret
    //     0xb8ea30: ret             
    // 0xb8ea34: ldur            x0, [fp, #-0x18]
    // 0xb8ea38: r16 = "PENDING"
    //     0xb8ea38: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8ea3c: ldr             x16, [x16, #0x2c8]
    // 0xb8ea40: ldur            lr, [fp, #-0x10]
    // 0xb8ea44: stp             lr, x16, [SP]
    // 0xb8ea48: r0 = ==()
    //     0xb8ea48: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8ea4c: tbnz            w0, #4, #0xb8ea78
    // 0xb8ea50: ldur            x0, [fp, #-0x18]
    // 0xb8ea54: tbnz            w0, #4, #0xb8ea64
    // 0xb8ea58: r0 = Instance_Color
    //     0xb8ea58: add             x0, PP, #0x35, lsl #12  ; [pp+0x35088] Obj!Color@e2b591
    //     0xb8ea5c: ldr             x0, [x0, #0x88]
    // 0xb8ea60: b               #0xb8ea6c
    // 0xb8ea64: r0 = Instance_Color
    //     0xb8ea64: add             x0, PP, #0x35, lsl #12  ; [pp+0x35090] Obj!Color@e2b561
    //     0xb8ea68: ldr             x0, [x0, #0x90]
    // 0xb8ea6c: LeaveFrame
    //     0xb8ea6c: mov             SP, fp
    //     0xb8ea70: ldp             fp, lr, [SP], #0x10
    // 0xb8ea74: ret
    //     0xb8ea74: ret             
    // 0xb8ea78: ldur            x0, [fp, #-0x18]
    // 0xb8ea7c: r16 = "EXPIRED"
    //     0xb8ea7c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8ea80: ldr             x16, [x16, #0xf88]
    // 0xb8ea84: ldur            lr, [fp, #-0x10]
    // 0xb8ea88: stp             lr, x16, [SP]
    // 0xb8ea8c: r0 = ==()
    //     0xb8ea8c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8ea90: tbnz            w0, #4, #0xb8eabc
    // 0xb8ea94: ldur            x1, [fp, #-0x18]
    // 0xb8ea98: tbnz            w1, #4, #0xb8eaa8
    // 0xb8ea9c: r0 = Instance_Color
    //     0xb8ea9c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35098] Obj!Color@e2b531
    //     0xb8eaa0: ldr             x0, [x0, #0x98]
    // 0xb8eaa4: b               #0xb8eab0
    // 0xb8eaa8: r0 = Instance_Color
    //     0xb8eaa8: add             x0, PP, #0x35, lsl #12  ; [pp+0x350a0] Obj!Color@e2b501
    //     0xb8eaac: ldr             x0, [x0, #0xa0]
    // 0xb8eab0: LeaveFrame
    //     0xb8eab0: mov             SP, fp
    //     0xb8eab4: ldp             fp, lr, [SP], #0x10
    // 0xb8eab8: ret
    //     0xb8eab8: ret             
    // 0xb8eabc: ldur            x1, [fp, #-0x18]
    // 0xb8eac0: tbnz            w1, #4, #0xb8ead0
    // 0xb8eac4: r0 = Instance_Color
    //     0xb8eac4: add             x0, PP, #0x35, lsl #12  ; [pp+0x350a8] Obj!Color@e2b4d1
    //     0xb8eac8: ldr             x0, [x0, #0xa8]
    // 0xb8eacc: b               #0xb8ead8
    // 0xb8ead0: r0 = Instance_Color
    //     0xb8ead0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35070] Obj!Color@e2b1a1
    //     0xb8ead4: ldr             x0, [x0, #0x70]
    // 0xb8ead8: LeaveFrame
    //     0xb8ead8: mov             SP, fp
    //     0xb8eadc: ldp             fp, lr, [SP], #0x10
    // 0xb8eae0: ret
    //     0xb8eae0: ret             
    // 0xb8eae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8eae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8eae8: b               #0xb8e9c0
  }
  get _ progressBackgroundColor(/* No info */) {
    // ** addr: 0xb8eaec, size: 0x148
    // 0xb8eaec: EnterFrame
    //     0xb8eaec: stp             fp, lr, [SP, #-0x10]!
    //     0xb8eaf0: mov             fp, SP
    // 0xb8eaf4: AllocStack(0x28)
    //     0xb8eaf4: sub             SP, SP, #0x28
    // 0xb8eaf8: SetupParameters(Transaction this /* r1 => r1, fp-0x8 */)
    //     0xb8eaf8: stur            x1, [fp, #-8]
    // 0xb8eafc: CheckStackOverflow
    //     0xb8eafc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8eb00: cmp             SP, x16
    //     0xb8eb04: b.ls            #0xb8ec2c
    // 0xb8eb08: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8eb08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8eb0c: ldr             x0, [x0, #0x2670]
    //     0xb8eb10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8eb14: cmp             w0, w16
    //     0xb8eb18: b.ne            #0xb8eb24
    //     0xb8eb1c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8eb20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb8eb24: r0 = GetNavigation.isDarkMode()
    //     0xb8eb24: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xb8eb28: mov             x1, x0
    // 0xb8eb2c: ldur            x0, [fp, #-8]
    // 0xb8eb30: stur            x1, [fp, #-0x18]
    // 0xb8eb34: LoadField: r2 = r0->field_27
    //     0xb8eb34: ldur            w2, [x0, #0x27]
    // 0xb8eb38: DecompressPointer r2
    //     0xb8eb38: add             x2, x2, HEAP, lsl #32
    // 0xb8eb3c: stur            x2, [fp, #-0x10]
    // 0xb8eb40: r16 = "SUCCESS"
    //     0xb8eb40: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c0] "SUCCESS"
    //     0xb8eb44: ldr             x16, [x16, #0x2c0]
    // 0xb8eb48: stp             x2, x16, [SP]
    // 0xb8eb4c: r0 = ==()
    //     0xb8eb4c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8eb50: tbnz            w0, #4, #0xb8eb7c
    // 0xb8eb54: ldur            x0, [fp, #-0x18]
    // 0xb8eb58: tbnz            w0, #4, #0xb8eb68
    // 0xb8eb5c: r0 = Instance_Color
    //     0xb8eb5c: add             x0, PP, #0x35, lsl #12  ; [pp+0x350b0] Obj!Color@e2b6e1
    //     0xb8eb60: ldr             x0, [x0, #0xb0]
    // 0xb8eb64: b               #0xb8eb70
    // 0xb8eb68: r0 = Instance_Color
    //     0xb8eb68: add             x0, PP, #0x35, lsl #12  ; [pp+0x350b8] Obj!Color@e2b6b1
    //     0xb8eb6c: ldr             x0, [x0, #0xb8]
    // 0xb8eb70: LeaveFrame
    //     0xb8eb70: mov             SP, fp
    //     0xb8eb74: ldp             fp, lr, [SP], #0x10
    // 0xb8eb78: ret
    //     0xb8eb78: ret             
    // 0xb8eb7c: ldur            x0, [fp, #-0x18]
    // 0xb8eb80: r16 = "PENDING"
    //     0xb8eb80: add             x16, PP, #0x30, lsl #12  ; [pp+0x302c8] "PENDING"
    //     0xb8eb84: ldr             x16, [x16, #0x2c8]
    // 0xb8eb88: ldur            lr, [fp, #-0x10]
    // 0xb8eb8c: stp             lr, x16, [SP]
    // 0xb8eb90: r0 = ==()
    //     0xb8eb90: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8eb94: tbnz            w0, #4, #0xb8ebc0
    // 0xb8eb98: ldur            x0, [fp, #-0x18]
    // 0xb8eb9c: tbnz            w0, #4, #0xb8ebac
    // 0xb8eba0: r0 = Instance_Color
    //     0xb8eba0: add             x0, PP, #0x35, lsl #12  ; [pp+0x350c0] Obj!Color@e2b681
    //     0xb8eba4: ldr             x0, [x0, #0xc0]
    // 0xb8eba8: b               #0xb8ebb4
    // 0xb8ebac: r0 = Instance_Color
    //     0xb8ebac: add             x0, PP, #0x35, lsl #12  ; [pp+0x350c8] Obj!Color@e2b651
    //     0xb8ebb0: ldr             x0, [x0, #0xc8]
    // 0xb8ebb4: LeaveFrame
    //     0xb8ebb4: mov             SP, fp
    //     0xb8ebb8: ldp             fp, lr, [SP], #0x10
    // 0xb8ebbc: ret
    //     0xb8ebbc: ret             
    // 0xb8ebc0: ldur            x0, [fp, #-0x18]
    // 0xb8ebc4: r16 = "EXPIRED"
    //     0xb8ebc4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f88] "EXPIRED"
    //     0xb8ebc8: ldr             x16, [x16, #0xf88]
    // 0xb8ebcc: ldur            lr, [fp, #-0x10]
    // 0xb8ebd0: stp             lr, x16, [SP]
    // 0xb8ebd4: r0 = ==()
    //     0xb8ebd4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb8ebd8: tbnz            w0, #4, #0xb8ec04
    // 0xb8ebdc: ldur            x1, [fp, #-0x18]
    // 0xb8ebe0: tbnz            w1, #4, #0xb8ebf0
    // 0xb8ebe4: r0 = Instance_Color
    //     0xb8ebe4: add             x0, PP, #0x35, lsl #12  ; [pp+0x350d0] Obj!Color@e2b621
    //     0xb8ebe8: ldr             x0, [x0, #0xd0]
    // 0xb8ebec: b               #0xb8ebf8
    // 0xb8ebf0: r0 = Instance_Color
    //     0xb8ebf0: add             x0, PP, #0x35, lsl #12  ; [pp+0x350d8] Obj!Color@e2b5f1
    //     0xb8ebf4: ldr             x0, [x0, #0xd8]
    // 0xb8ebf8: LeaveFrame
    //     0xb8ebf8: mov             SP, fp
    //     0xb8ebfc: ldp             fp, lr, [SP], #0x10
    // 0xb8ec00: ret
    //     0xb8ec00: ret             
    // 0xb8ec04: ldur            x1, [fp, #-0x18]
    // 0xb8ec08: tbnz            w1, #4, #0xb8ec18
    // 0xb8ec0c: r0 = Instance_Color
    //     0xb8ec0c: add             x0, PP, #0x35, lsl #12  ; [pp+0x350a8] Obj!Color@e2b4d1
    //     0xb8ec10: ldr             x0, [x0, #0xa8]
    // 0xb8ec14: b               #0xb8ec20
    // 0xb8ec18: r0 = Instance_Color
    //     0xb8ec18: add             x0, PP, #0x35, lsl #12  ; [pp+0x35070] Obj!Color@e2b1a1
    //     0xb8ec1c: ldr             x0, [x0, #0x70]
    // 0xb8ec20: LeaveFrame
    //     0xb8ec20: mov             SP, fp
    //     0xb8ec24: ldp             fp, lr, [SP], #0x10
    // 0xb8ec28: ret
    //     0xb8ec28: ret             
    // 0xb8ec2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8ec2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8ec30: b               #0xb8eb08
  }
  get _ anonName(/* No info */) {
    // ** addr: 0xb9567c, size: 0x104
    // 0xb9567c: EnterFrame
    //     0xb9567c: stp             fp, lr, [SP, #-0x10]!
    //     0xb95680: mov             fp, SP
    // 0xb95684: AllocStack(0x30)
    //     0xb95684: sub             SP, SP, #0x30
    // 0xb95688: CheckStackOverflow
    //     0xb95688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9568c: cmp             SP, x16
    //     0xb95690: b.ls            #0xb95778
    // 0xb95694: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb95694: ldur            w0, [x1, #0x17]
    // 0xb95698: DecompressPointer r0
    //     0xb95698: add             x0, x0, HEAP, lsl #32
    // 0xb9569c: stur            x0, [fp, #-8]
    // 0xb956a0: LoadField: r1 = r0->field_7
    //     0xb956a0: ldur            w1, [x0, #7]
    // 0xb956a4: r2 = LoadInt32Instr(r1)
    //     0xb956a4: sbfx            x2, x1, #1, #0x1f
    // 0xb956a8: stur            x2, [fp, #-0x10]
    // 0xb956ac: cmp             x2, #1
    // 0xb956b0: b.ne            #0xb956ec
    // 0xb956b4: r1 = Null
    //     0xb956b4: mov             x1, NULL
    // 0xb956b8: r2 = 4
    //     0xb956b8: movz            x2, #0x4
    // 0xb956bc: r0 = AllocateArray()
    //     0xb956bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb956c0: mov             x1, x0
    // 0xb956c4: ldur            x0, [fp, #-8]
    // 0xb956c8: StoreField: r1->field_f = r0
    //     0xb956c8: stur            w0, [x1, #0xf]
    // 0xb956cc: r16 = "******"
    //     0xb956cc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35410] "******"
    //     0xb956d0: ldr             x16, [x16, #0x410]
    // 0xb956d4: StoreField: r1->field_13 = r16
    //     0xb956d4: stur            w16, [x1, #0x13]
    // 0xb956d8: str             x1, [SP]
    // 0xb956dc: r0 = _interpolate()
    //     0xb956dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb956e0: LeaveFrame
    //     0xb956e0: mov             SP, fp
    //     0xb956e4: ldp             fp, lr, [SP], #0x10
    // 0xb956e8: ret
    //     0xb956e8: ret             
    // 0xb956ec: stp             xzr, x0, [SP]
    // 0xb956f0: r0 = []()
    //     0xb956f0: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb956f4: r1 = Null
    //     0xb956f4: mov             x1, NULL
    // 0xb956f8: r2 = 6
    //     0xb956f8: movz            x2, #0x6
    // 0xb956fc: stur            x0, [fp, #-0x18]
    // 0xb95700: r0 = AllocateArray()
    //     0xb95700: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb95704: mov             x1, x0
    // 0xb95708: ldur            x0, [fp, #-0x18]
    // 0xb9570c: stur            x1, [fp, #-0x20]
    // 0xb95710: StoreField: r1->field_f = r0
    //     0xb95710: stur            w0, [x1, #0xf]
    // 0xb95714: r16 = "*****"
    //     0xb95714: add             x16, PP, #0x35, lsl #12  ; [pp+0x35418] "*****"
    //     0xb95718: ldr             x16, [x16, #0x418]
    // 0xb9571c: StoreField: r1->field_13 = r16
    //     0xb9571c: stur            w16, [x1, #0x13]
    // 0xb95720: ldur            x0, [fp, #-0x10]
    // 0xb95724: sub             x2, x0, #1
    // 0xb95728: lsl             x0, x2, #1
    // 0xb9572c: ldur            x16, [fp, #-8]
    // 0xb95730: stp             x0, x16, [SP]
    // 0xb95734: r0 = []()
    //     0xb95734: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb95738: ldur            x1, [fp, #-0x20]
    // 0xb9573c: ArrayStore: r1[2] = r0  ; List_4
    //     0xb9573c: add             x25, x1, #0x17
    //     0xb95740: str             w0, [x25]
    //     0xb95744: tbz             w0, #0, #0xb95760
    //     0xb95748: ldurb           w16, [x1, #-1]
    //     0xb9574c: ldurb           w17, [x0, #-1]
    //     0xb95750: and             x16, x17, x16, lsr #2
    //     0xb95754: tst             x16, HEAP, lsr #32
    //     0xb95758: b.eq            #0xb95760
    //     0xb9575c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb95760: ldur            x16, [fp, #-0x20]
    // 0xb95764: str             x16, [SP]
    // 0xb95768: r0 = _interpolate()
    //     0xb95768: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb9576c: LeaveFrame
    //     0xb9576c: mov             SP, fp
    //     0xb95770: ldp             fp, lr, [SP], #0x10
    // 0xb95774: ret
    //     0xb95774: ret             
    // 0xb95778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb95778: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9577c: b               #0xb95694
  }
  get _ alias(/* No info */) {
    // ** addr: 0xb9592c, size: 0xac
    // 0xb9592c: EnterFrame
    //     0xb9592c: stp             fp, lr, [SP, #-0x10]!
    //     0xb95930: mov             fp, SP
    // 0xb95934: AllocStack(0x28)
    //     0xb95934: sub             SP, SP, #0x28
    // 0xb95938: CheckStackOverflow
    //     0xb95938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9593c: cmp             SP, x16
    //     0xb95940: b.ls            #0xb959d0
    // 0xb95944: LoadField: r0 = r1->field_23
    //     0xb95944: ldur            w0, [x1, #0x23]
    // 0xb95948: DecompressPointer r0
    //     0xb95948: add             x0, x0, HEAP, lsl #32
    // 0xb9594c: tbnz            w0, #4, #0xb959bc
    // 0xb95950: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb95950: ldur            w0, [x1, #0x17]
    // 0xb95954: DecompressPointer r0
    //     0xb95954: add             x0, x0, HEAP, lsl #32
    // 0xb95958: stur            x0, [fp, #-0x10]
    // 0xb9595c: LoadField: r1 = r0->field_7
    //     0xb9595c: ldur            w1, [x0, #7]
    // 0xb95960: r2 = LoadInt32Instr(r1)
    //     0xb95960: sbfx            x2, x1, #1, #0x1f
    // 0xb95964: stur            x2, [fp, #-8]
    // 0xb95968: cmp             x2, #1
    // 0xb9596c: b.ne            #0xb9597c
    // 0xb95970: LeaveFrame
    //     0xb95970: mov             SP, fp
    //     0xb95974: ldp             fp, lr, [SP], #0x10
    // 0xb95978: ret
    //     0xb95978: ret             
    // 0xb9597c: stp             xzr, x0, [SP]
    // 0xb95980: r0 = []()
    //     0xb95980: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb95984: mov             x1, x0
    // 0xb95988: ldur            x0, [fp, #-8]
    // 0xb9598c: stur            x1, [fp, #-0x18]
    // 0xb95990: sub             x2, x0, #1
    // 0xb95994: lsl             x0, x2, #1
    // 0xb95998: ldur            x16, [fp, #-0x10]
    // 0xb9599c: stp             x0, x16, [SP]
    // 0xb959a0: r0 = []()
    //     0xb959a0: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xb959a4: ldur            x16, [fp, #-0x18]
    // 0xb959a8: stp             x0, x16, [SP]
    // 0xb959ac: r0 = +()
    //     0xb959ac: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xb959b0: LeaveFrame
    //     0xb959b0: mov             SP, fp
    //     0xb959b4: ldp             fp, lr, [SP], #0x10
    // 0xb959b8: ret
    //     0xb959b8: ret             
    // 0xb959bc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb959bc: ldur            w0, [x1, #0x17]
    // 0xb959c0: DecompressPointer r0
    //     0xb959c0: add             x0, x0, HEAP, lsl #32
    // 0xb959c4: LeaveFrame
    //     0xb959c4: mov             SP, fp
    //     0xb959c8: ldp             fp, lr, [SP], #0x10
    // 0xb959cc: ret
    //     0xb959cc: ret             
    // 0xb959d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb959d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb959d4: b               #0xb95944
  }
}

// class id: 5571, size: 0x18, field offset: 0x8
//   const constructor, 
class Donation extends Equatable {

  factory _ Donation.fromMap(/* No info */) {
    // ** addr: 0x6ff644, size: 0x16c
    // 0x6ff644: EnterFrame
    //     0x6ff644: stp             fp, lr, [SP, #-0x10]!
    //     0x6ff648: mov             fp, SP
    // 0x6ff64c: AllocStack(0x20)
    //     0x6ff64c: sub             SP, SP, #0x20
    // 0x6ff650: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x6ff650: mov             x3, x2
    //     0x6ff654: stur            x2, [fp, #-8]
    // 0x6ff658: CheckStackOverflow
    //     0x6ff658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ff65c: cmp             SP, x16
    //     0x6ff660: b.ls            #0x6ff7a8
    // 0x6ff664: r0 = LoadClassIdInstr(r3)
    //     0x6ff664: ldur            x0, [x3, #-1]
    //     0x6ff668: ubfx            x0, x0, #0xc, #0x14
    // 0x6ff66c: mov             x1, x3
    // 0x6ff670: r2 = "id"
    //     0x6ff670: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x6ff674: ldr             x2, [x2, #0x740]
    // 0x6ff678: r0 = GDT[cid_x0 + -0x114]()
    //     0x6ff678: sub             lr, x0, #0x114
    //     0x6ff67c: ldr             lr, [x21, lr, lsl #3]
    //     0x6ff680: blr             lr
    // 0x6ff684: mov             x3, x0
    // 0x6ff688: r2 = Null
    //     0x6ff688: mov             x2, NULL
    // 0x6ff68c: r1 = Null
    //     0x6ff68c: mov             x1, NULL
    // 0x6ff690: stur            x3, [fp, #-0x10]
    // 0x6ff694: branchIfSmi(r0, 0x6ff6bc)
    //     0x6ff694: tbz             w0, #0, #0x6ff6bc
    // 0x6ff698: r4 = LoadClassIdInstr(r0)
    //     0x6ff698: ldur            x4, [x0, #-1]
    //     0x6ff69c: ubfx            x4, x4, #0xc, #0x14
    // 0x6ff6a0: sub             x4, x4, #0x3c
    // 0x6ff6a4: cmp             x4, #1
    // 0x6ff6a8: b.ls            #0x6ff6bc
    // 0x6ff6ac: r8 = int
    //     0x6ff6ac: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x6ff6b0: r3 = Null
    //     0x6ff6b0: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b258] Null
    //     0x6ff6b4: ldr             x3, [x3, #0x258]
    // 0x6ff6b8: r0 = int()
    //     0x6ff6b8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x6ff6bc: ldur            x3, [fp, #-8]
    // 0x6ff6c0: r0 = LoadClassIdInstr(r3)
    //     0x6ff6c0: ldur            x0, [x3, #-1]
    //     0x6ff6c4: ubfx            x0, x0, #0xc, #0x14
    // 0x6ff6c8: mov             x1, x3
    // 0x6ff6cc: r2 = "name"
    //     0x6ff6cc: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x6ff6d0: r0 = GDT[cid_x0 + -0x114]()
    //     0x6ff6d0: sub             lr, x0, #0x114
    //     0x6ff6d4: ldr             lr, [x21, lr, lsl #3]
    //     0x6ff6d8: blr             lr
    // 0x6ff6dc: mov             x3, x0
    // 0x6ff6e0: r2 = Null
    //     0x6ff6e0: mov             x2, NULL
    // 0x6ff6e4: r1 = Null
    //     0x6ff6e4: mov             x1, NULL
    // 0x6ff6e8: stur            x3, [fp, #-0x18]
    // 0x6ff6ec: r4 = 60
    //     0x6ff6ec: movz            x4, #0x3c
    // 0x6ff6f0: branchIfSmi(r0, 0x6ff6fc)
    //     0x6ff6f0: tbz             w0, #0, #0x6ff6fc
    // 0x6ff6f4: r4 = LoadClassIdInstr(r0)
    //     0x6ff6f4: ldur            x4, [x0, #-1]
    //     0x6ff6f8: ubfx            x4, x4, #0xc, #0x14
    // 0x6ff6fc: sub             x4, x4, #0x5e
    // 0x6ff700: cmp             x4, #1
    // 0x6ff704: b.ls            #0x6ff718
    // 0x6ff708: r8 = String
    //     0x6ff708: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6ff70c: r3 = Null
    //     0x6ff70c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b268] Null
    //     0x6ff710: ldr             x3, [x3, #0x268]
    // 0x6ff714: r0 = String()
    //     0x6ff714: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6ff718: ldur            x1, [fp, #-8]
    // 0x6ff71c: r0 = LoadClassIdInstr(r1)
    //     0x6ff71c: ldur            x0, [x1, #-1]
    //     0x6ff720: ubfx            x0, x0, #0xc, #0x14
    // 0x6ff724: r2 = "category"
    //     0x6ff724: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x6ff728: ldr             x2, [x2, #0x960]
    // 0x6ff72c: r0 = GDT[cid_x0 + -0x114]()
    //     0x6ff72c: sub             lr, x0, #0x114
    //     0x6ff730: ldr             lr, [x21, lr, lsl #3]
    //     0x6ff734: blr             lr
    // 0x6ff738: mov             x3, x0
    // 0x6ff73c: r2 = Null
    //     0x6ff73c: mov             x2, NULL
    // 0x6ff740: r1 = Null
    //     0x6ff740: mov             x1, NULL
    // 0x6ff744: stur            x3, [fp, #-8]
    // 0x6ff748: r8 = Map<String, dynamic>
    //     0x6ff748: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x6ff74c: r3 = Null
    //     0x6ff74c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b278] Null
    //     0x6ff750: ldr             x3, [x3, #0x278]
    // 0x6ff754: r0 = Map<String, dynamic>()
    //     0x6ff754: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x6ff758: ldur            x2, [fp, #-8]
    // 0x6ff75c: r1 = Null
    //     0x6ff75c: mov             x1, NULL
    // 0x6ff760: r0 = DonationCategory.fromMap()
    //     0x6ff760: bl              #0x6ff7bc  ; [package:nuonline/app/data/models/transaction.dart] DonationCategory::DonationCategory.fromMap
    // 0x6ff764: mov             x1, x0
    // 0x6ff768: ldur            x0, [fp, #-0x10]
    // 0x6ff76c: stur            x1, [fp, #-8]
    // 0x6ff770: r2 = LoadInt32Instr(r0)
    //     0x6ff770: sbfx            x2, x0, #1, #0x1f
    //     0x6ff774: tbz             w0, #0, #0x6ff77c
    //     0x6ff778: ldur            x2, [x0, #7]
    // 0x6ff77c: stur            x2, [fp, #-0x20]
    // 0x6ff780: r0 = Donation()
    //     0x6ff780: bl              #0x6ff7b0  ; AllocateDonationStub -> Donation (size=0x18)
    // 0x6ff784: ldur            x1, [fp, #-0x20]
    // 0x6ff788: StoreField: r0->field_7 = r1
    //     0x6ff788: stur            x1, [x0, #7]
    // 0x6ff78c: ldur            x1, [fp, #-0x18]
    // 0x6ff790: StoreField: r0->field_f = r1
    //     0x6ff790: stur            w1, [x0, #0xf]
    // 0x6ff794: ldur            x1, [fp, #-8]
    // 0x6ff798: StoreField: r0->field_13 = r1
    //     0x6ff798: stur            w1, [x0, #0x13]
    // 0x6ff79c: LeaveFrame
    //     0x6ff79c: mov             SP, fp
    //     0x6ff7a0: ldp             fp, lr, [SP], #0x10
    // 0x6ff7a4: ret
    //     0x6ff7a4: ret             
    // 0x6ff7a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ff7a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ff7ac: b               #0x6ff664
  }
  get _ categoryTitle(/* No info */) {
    // ** addr: 0xb94318, size: 0xa8
    // 0xb94318: EnterFrame
    //     0xb94318: stp             fp, lr, [SP, #-0x10]!
    //     0xb9431c: mov             fp, SP
    // 0xb94320: AllocStack(0x20)
    //     0xb94320: sub             SP, SP, #0x20
    // 0xb94324: SetupParameters(Donation this /* r1 => r0, fp-0x18 */)
    //     0xb94324: mov             x0, x1
    //     0xb94328: stur            x1, [fp, #-0x18]
    // 0xb9432c: CheckStackOverflow
    //     0xb9432c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94330: cmp             SP, x16
    //     0xb94334: b.ls            #0xb943b8
    // 0xb94338: LoadField: r3 = r0->field_13
    //     0xb94338: ldur            w3, [x0, #0x13]
    // 0xb9433c: DecompressPointer r3
    //     0xb9433c: add             x3, x3, HEAP, lsl #32
    // 0xb94340: stur            x3, [fp, #-0x10]
    // 0xb94344: LoadField: r4 = r3->field_f
    //     0xb94344: ldur            w4, [x3, #0xf]
    // 0xb94348: DecompressPointer r4
    //     0xb94348: add             x4, x4, HEAP, lsl #32
    // 0xb9434c: stur            x4, [fp, #-8]
    // 0xb94350: r1 = Null
    //     0xb94350: mov             x1, NULL
    // 0xb94354: r2 = 8
    //     0xb94354: movz            x2, #0x8
    // 0xb94358: r0 = AllocateArray()
    //     0xb94358: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb9435c: mov             x1, x0
    // 0xb94360: ldur            x0, [fp, #-8]
    // 0xb94364: StoreField: r1->field_f = r0
    //     0xb94364: stur            w0, [x1, #0xf]
    // 0xb94368: r16 = " "
    //     0xb94368: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb9436c: StoreField: r1->field_13 = r16
    //     0xb9436c: stur            w16, [x1, #0x13]
    // 0xb94370: ldur            x0, [fp, #-0x10]
    // 0xb94374: LoadField: r2 = r0->field_7
    //     0xb94374: ldur            x2, [x0, #7]
    // 0xb94378: cmp             x2, #2
    // 0xb9437c: b.ne            #0xb9438c
    // 0xb94380: r2 = "ke "
    //     0xb94380: add             x2, PP, #0x40, lsl #12  ; [pp+0x402c0] "ke "
    //     0xb94384: ldr             x2, [x2, #0x2c0]
    // 0xb94388: b               #0xb94390
    // 0xb9438c: r2 = ""
    //     0xb9438c: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xb94390: ldur            x0, [fp, #-0x18]
    // 0xb94394: ArrayStore: r1[0] = r2  ; List_4
    //     0xb94394: stur            w2, [x1, #0x17]
    // 0xb94398: LoadField: r2 = r0->field_f
    //     0xb94398: ldur            w2, [x0, #0xf]
    // 0xb9439c: DecompressPointer r2
    //     0xb9439c: add             x2, x2, HEAP, lsl #32
    // 0xb943a0: StoreField: r1->field_1b = r2
    //     0xb943a0: stur            w2, [x1, #0x1b]
    // 0xb943a4: str             x1, [SP]
    // 0xb943a8: r0 = _interpolate()
    //     0xb943a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb943ac: LeaveFrame
    //     0xb943ac: mov             SP, fp
    //     0xb943b0: ldp             fp, lr, [SP], #0x10
    // 0xb943b4: ret
    //     0xb943b4: ret             
    // 0xb943b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb943b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb943bc: b               #0xb94338
  }
}

// class id: 5572, size: 0x14, field offset: 0x8
//   const constructor, 
class DonationCategory extends Equatable {

  factory _ DonationCategory.fromMap(/* No info */) {
    // ** addr: 0x6ff7bc, size: 0x10c
    // 0x6ff7bc: EnterFrame
    //     0x6ff7bc: stp             fp, lr, [SP, #-0x10]!
    //     0x6ff7c0: mov             fp, SP
    // 0x6ff7c4: AllocStack(0x18)
    //     0x6ff7c4: sub             SP, SP, #0x18
    // 0x6ff7c8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x6ff7c8: mov             x3, x2
    //     0x6ff7cc: stur            x2, [fp, #-8]
    // 0x6ff7d0: CheckStackOverflow
    //     0x6ff7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ff7d4: cmp             SP, x16
    //     0x6ff7d8: b.ls            #0x6ff8c0
    // 0x6ff7dc: r0 = LoadClassIdInstr(r3)
    //     0x6ff7dc: ldur            x0, [x3, #-1]
    //     0x6ff7e0: ubfx            x0, x0, #0xc, #0x14
    // 0x6ff7e4: mov             x1, x3
    // 0x6ff7e8: r2 = "id"
    //     0x6ff7e8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x6ff7ec: ldr             x2, [x2, #0x740]
    // 0x6ff7f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x6ff7f0: sub             lr, x0, #0x114
    //     0x6ff7f4: ldr             lr, [x21, lr, lsl #3]
    //     0x6ff7f8: blr             lr
    // 0x6ff7fc: mov             x3, x0
    // 0x6ff800: r2 = Null
    //     0x6ff800: mov             x2, NULL
    // 0x6ff804: r1 = Null
    //     0x6ff804: mov             x1, NULL
    // 0x6ff808: stur            x3, [fp, #-0x10]
    // 0x6ff80c: branchIfSmi(r0, 0x6ff834)
    //     0x6ff80c: tbz             w0, #0, #0x6ff834
    // 0x6ff810: r4 = LoadClassIdInstr(r0)
    //     0x6ff810: ldur            x4, [x0, #-1]
    //     0x6ff814: ubfx            x4, x4, #0xc, #0x14
    // 0x6ff818: sub             x4, x4, #0x3c
    // 0x6ff81c: cmp             x4, #1
    // 0x6ff820: b.ls            #0x6ff834
    // 0x6ff824: r8 = int
    //     0x6ff824: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x6ff828: r3 = Null
    //     0x6ff828: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b288] Null
    //     0x6ff82c: ldr             x3, [x3, #0x288]
    // 0x6ff830: r0 = int()
    //     0x6ff830: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x6ff834: ldur            x1, [fp, #-8]
    // 0x6ff838: r0 = LoadClassIdInstr(r1)
    //     0x6ff838: ldur            x0, [x1, #-1]
    //     0x6ff83c: ubfx            x0, x0, #0xc, #0x14
    // 0x6ff840: r2 = "name"
    //     0x6ff840: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x6ff844: r0 = GDT[cid_x0 + -0x114]()
    //     0x6ff844: sub             lr, x0, #0x114
    //     0x6ff848: ldr             lr, [x21, lr, lsl #3]
    //     0x6ff84c: blr             lr
    // 0x6ff850: mov             x3, x0
    // 0x6ff854: r2 = Null
    //     0x6ff854: mov             x2, NULL
    // 0x6ff858: r1 = Null
    //     0x6ff858: mov             x1, NULL
    // 0x6ff85c: stur            x3, [fp, #-8]
    // 0x6ff860: r4 = 60
    //     0x6ff860: movz            x4, #0x3c
    // 0x6ff864: branchIfSmi(r0, 0x6ff870)
    //     0x6ff864: tbz             w0, #0, #0x6ff870
    // 0x6ff868: r4 = LoadClassIdInstr(r0)
    //     0x6ff868: ldur            x4, [x0, #-1]
    //     0x6ff86c: ubfx            x4, x4, #0xc, #0x14
    // 0x6ff870: sub             x4, x4, #0x5e
    // 0x6ff874: cmp             x4, #1
    // 0x6ff878: b.ls            #0x6ff88c
    // 0x6ff87c: r8 = String
    //     0x6ff87c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x6ff880: r3 = Null
    //     0x6ff880: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b298] Null
    //     0x6ff884: ldr             x3, [x3, #0x298]
    // 0x6ff888: r0 = String()
    //     0x6ff888: bl              #0xed43b0  ; IsType_String_Stub
    // 0x6ff88c: ldur            x0, [fp, #-0x10]
    // 0x6ff890: r1 = LoadInt32Instr(r0)
    //     0x6ff890: sbfx            x1, x0, #1, #0x1f
    //     0x6ff894: tbz             w0, #0, #0x6ff89c
    //     0x6ff898: ldur            x1, [x0, #7]
    // 0x6ff89c: stur            x1, [fp, #-0x18]
    // 0x6ff8a0: r0 = DonationCategory()
    //     0x6ff8a0: bl              #0x6ff8c8  ; AllocateDonationCategoryStub -> DonationCategory (size=0x14)
    // 0x6ff8a4: ldur            x1, [fp, #-0x18]
    // 0x6ff8a8: StoreField: r0->field_7 = r1
    //     0x6ff8a8: stur            x1, [x0, #7]
    // 0x6ff8ac: ldur            x1, [fp, #-8]
    // 0x6ff8b0: StoreField: r0->field_f = r1
    //     0x6ff8b0: stur            w1, [x0, #0xf]
    // 0x6ff8b4: LeaveFrame
    //     0x6ff8b4: mov             SP, fp
    //     0x6ff8b8: ldp             fp, lr, [SP], #0x10
    // 0x6ff8bc: ret
    //     0x6ff8bc: ret             
    // 0x6ff8c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ff8c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ff8c4: b               #0x6ff7dc
  }
  get _ type(/* No info */) {
    // ** addr: 0xb8e7b4, size: 0x74
    // 0xb8e7b4: EnterFrame
    //     0xb8e7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e7b8: mov             fp, SP
    // 0xb8e7bc: AllocStack(0x20)
    //     0xb8e7bc: sub             SP, SP, #0x20
    // 0xb8e7c0: SetupParameters(DonationCategory this /* r1 => r1, fp-0x8 */)
    //     0xb8e7c0: stur            x1, [fp, #-8]
    // 0xb8e7c4: CheckStackOverflow
    //     0xb8e7c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e7c8: cmp             SP, x16
    //     0xb8e7cc: b.ls            #0xb8e820
    // 0xb8e7d0: r1 = 1
    //     0xb8e7d0: movz            x1, #0x1
    // 0xb8e7d4: r0 = AllocateContext()
    //     0xb8e7d4: bl              #0xec126c  ; AllocateContextStub
    // 0xb8e7d8: mov             x1, x0
    // 0xb8e7dc: ldur            x0, [fp, #-8]
    // 0xb8e7e0: StoreField: r1->field_f = r0
    //     0xb8e7e0: stur            w0, [x1, #0xf]
    // 0xb8e7e4: mov             x2, x1
    // 0xb8e7e8: r1 = Function '<anonymous closure>':.
    //     0xb8e7e8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35028] AnonymousClosure: (0xb8e828), in [package:nuonline/app/data/models/transaction.dart] DonationCategory::type (0xb8e7b4)
    //     0xb8e7ec: ldr             x1, [x1, #0x28]
    // 0xb8e7f0: r0 = AllocateClosure()
    //     0xb8e7f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb8e7f4: r16 = <PaymentType>
    //     0xb8e7f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] TypeArguments: <PaymentType>
    //     0xb8e7f8: ldr             x16, [x16, #0xd68]
    // 0xb8e7fc: r30 = const [Instance of 'PaymentType', Instance of 'PaymentType', Instance of 'PaymentType', Instance of 'PaymentType', Instance of 'PaymentType', Instance of 'PaymentType']
    //     0xb8e7fc: add             lr, PP, #0x35, lsl #12  ; [pp+0x35030] List<PaymentType>(6)
    //     0xb8e800: ldr             lr, [lr, #0x30]
    // 0xb8e804: stp             lr, x16, [SP, #8]
    // 0xb8e808: str             x0, [SP]
    // 0xb8e80c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8e80c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8e810: r0 = IterableExtension.firstWhereOrNull()
    //     0xb8e810: bl              #0x7379ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0xb8e814: LeaveFrame
    //     0xb8e814: mov             SP, fp
    //     0xb8e818: ldp             fp, lr, [SP], #0x10
    // 0xb8e81c: ret
    //     0xb8e81c: ret             
    // 0xb8e820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e824: b               #0xb8e7d0
  }
  [closure] bool <anonymous closure>(dynamic, PaymentType) {
    // ** addr: 0xb8e828, size: 0x34
    // 0xb8e828: ldr             x1, [SP, #8]
    // 0xb8e82c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb8e82c: ldur            w2, [x1, #0x17]
    // 0xb8e830: DecompressPointer r2
    //     0xb8e830: add             x2, x2, HEAP, lsl #32
    // 0xb8e834: ldr             x1, [SP]
    // 0xb8e838: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xb8e838: ldur            x3, [x1, #0x17]
    // 0xb8e83c: LoadField: r1 = r2->field_f
    //     0xb8e83c: ldur            w1, [x2, #0xf]
    // 0xb8e840: DecompressPointer r1
    //     0xb8e840: add             x1, x1, HEAP, lsl #32
    // 0xb8e844: LoadField: r2 = r1->field_7
    //     0xb8e844: ldur            x2, [x1, #7]
    // 0xb8e848: cmp             x3, x2
    // 0xb8e84c: r16 = true
    //     0xb8e84c: add             x16, NULL, #0x20  ; true
    // 0xb8e850: r17 = false
    //     0xb8e850: add             x17, NULL, #0x30  ; false
    // 0xb8e854: csel            x0, x16, x17, eq
    // 0xb8e858: ret
    //     0xb8e858: ret             
  }
}
