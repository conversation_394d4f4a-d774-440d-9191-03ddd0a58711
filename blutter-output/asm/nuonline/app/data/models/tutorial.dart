// lib: , url: package:nuonline/app/data/models/tutorial.dart

// class id: 1050059, size: 0x8
class :: {
}

// class id: 1643, size: 0x14, field offset: 0xc
class TutorialAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa670e8, size: 0x420
    // 0xa670e8: EnterFrame
    //     0xa670e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa670ec: mov             fp, SP
    // 0xa670f0: AllocStack(0x58)
    //     0xa670f0: sub             SP, SP, #0x58
    // 0xa670f4: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa670f4: stur            x2, [fp, #-0x20]
    // 0xa670f8: CheckStackOverflow
    //     0xa670f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa670fc: cmp             SP, x16
    //     0xa67100: b.ls            #0xa674f0
    // 0xa67104: LoadField: r3 = r2->field_23
    //     0xa67104: ldur            x3, [x2, #0x23]
    // 0xa67108: add             x0, x3, #1
    // 0xa6710c: LoadField: r1 = r2->field_1b
    //     0xa6710c: ldur            x1, [x2, #0x1b]
    // 0xa67110: cmp             x0, x1
    // 0xa67114: b.gt            #0xa67494
    // 0xa67118: LoadField: r4 = r2->field_7
    //     0xa67118: ldur            w4, [x2, #7]
    // 0xa6711c: DecompressPointer r4
    //     0xa6711c: add             x4, x4, HEAP, lsl #32
    // 0xa67120: stur            x4, [fp, #-0x18]
    // 0xa67124: StoreField: r2->field_23 = r0
    //     0xa67124: stur            x0, [x2, #0x23]
    // 0xa67128: LoadField: r0 = r4->field_13
    //     0xa67128: ldur            w0, [x4, #0x13]
    // 0xa6712c: r5 = LoadInt32Instr(r0)
    //     0xa6712c: sbfx            x5, x0, #1, #0x1f
    // 0xa67130: mov             x0, x5
    // 0xa67134: mov             x1, x3
    // 0xa67138: stur            x5, [fp, #-0x10]
    // 0xa6713c: cmp             x1, x0
    // 0xa67140: b.hs            #0xa674f8
    // 0xa67144: LoadField: r0 = r4->field_7
    //     0xa67144: ldur            x0, [x4, #7]
    // 0xa67148: ldrb            w1, [x0, x3]
    // 0xa6714c: stur            x1, [fp, #-8]
    // 0xa67150: r16 = <int, dynamic>
    //     0xa67150: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa67154: ldr             x16, [x16, #0xac0]
    // 0xa67158: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6715c: stp             lr, x16, [SP]
    // 0xa67160: r0 = Map._fromLiteral()
    //     0xa67160: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa67164: mov             x2, x0
    // 0xa67168: stur            x2, [fp, #-0x38]
    // 0xa6716c: r6 = 0
    //     0xa6716c: movz            x6, #0
    // 0xa67170: ldur            x3, [fp, #-0x20]
    // 0xa67174: ldur            x4, [fp, #-0x18]
    // 0xa67178: ldur            x5, [fp, #-8]
    // 0xa6717c: stur            x6, [fp, #-0x30]
    // 0xa67180: CheckStackOverflow
    //     0xa67180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa67184: cmp             SP, x16
    //     0xa67188: b.ls            #0xa674fc
    // 0xa6718c: cmp             x6, x5
    // 0xa67190: b.ge            #0xa6721c
    // 0xa67194: LoadField: r7 = r3->field_23
    //     0xa67194: ldur            x7, [x3, #0x23]
    // 0xa67198: add             x0, x7, #1
    // 0xa6719c: LoadField: r1 = r3->field_1b
    //     0xa6719c: ldur            x1, [x3, #0x1b]
    // 0xa671a0: cmp             x0, x1
    // 0xa671a4: b.gt            #0xa674bc
    // 0xa671a8: StoreField: r3->field_23 = r0
    //     0xa671a8: stur            x0, [x3, #0x23]
    // 0xa671ac: ldur            x0, [fp, #-0x10]
    // 0xa671b0: mov             x1, x7
    // 0xa671b4: cmp             x1, x0
    // 0xa671b8: b.hs            #0xa67504
    // 0xa671bc: LoadField: r0 = r4->field_7
    //     0xa671bc: ldur            x0, [x4, #7]
    // 0xa671c0: ldrb            w8, [x0, x7]
    // 0xa671c4: mov             x1, x3
    // 0xa671c8: stur            x8, [fp, #-0x28]
    // 0xa671cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa671cc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa671d0: r0 = read()
    //     0xa671d0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa671d4: mov             x1, x0
    // 0xa671d8: ldur            x0, [fp, #-0x28]
    // 0xa671dc: lsl             x2, x0, #1
    // 0xa671e0: r16 = LoadInt32Instr(r2)
    //     0xa671e0: sbfx            x16, x2, #1, #0x1f
    // 0xa671e4: r17 = 11601
    //     0xa671e4: movz            x17, #0x2d51
    // 0xa671e8: mul             x0, x16, x17
    // 0xa671ec: umulh           x16, x16, x17
    // 0xa671f0: eor             x0, x0, x16
    // 0xa671f4: r0 = 0
    //     0xa671f4: eor             x0, x0, x0, lsr #32
    // 0xa671f8: ubfiz           x0, x0, #1, #0x1e
    // 0xa671fc: r5 = LoadInt32Instr(r0)
    //     0xa671fc: sbfx            x5, x0, #1, #0x1f
    // 0xa67200: mov             x3, x1
    // 0xa67204: ldur            x1, [fp, #-0x38]
    // 0xa67208: r0 = _set()
    //     0xa67208: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa6720c: ldur            x0, [fp, #-0x30]
    // 0xa67210: add             x6, x0, #1
    // 0xa67214: ldur            x2, [fp, #-0x38]
    // 0xa67218: b               #0xa67170
    // 0xa6721c: mov             x0, x2
    // 0xa67220: mov             x1, x0
    // 0xa67224: r2 = 0
    //     0xa67224: movz            x2, #0
    // 0xa67228: r0 = _getValueOrData()
    //     0xa67228: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6722c: ldur            x3, [fp, #-0x38]
    // 0xa67230: LoadField: r1 = r3->field_f
    //     0xa67230: ldur            w1, [x3, #0xf]
    // 0xa67234: DecompressPointer r1
    //     0xa67234: add             x1, x1, HEAP, lsl #32
    // 0xa67238: cmp             w1, w0
    // 0xa6723c: b.ne            #0xa67248
    // 0xa67240: r4 = Null
    //     0xa67240: mov             x4, NULL
    // 0xa67244: b               #0xa6724c
    // 0xa67248: mov             x4, x0
    // 0xa6724c: mov             x0, x4
    // 0xa67250: stur            x4, [fp, #-0x18]
    // 0xa67254: r2 = Null
    //     0xa67254: mov             x2, NULL
    // 0xa67258: r1 = Null
    //     0xa67258: mov             x1, NULL
    // 0xa6725c: branchIfSmi(r0, 0xa67284)
    //     0xa6725c: tbz             w0, #0, #0xa67284
    // 0xa67260: r4 = LoadClassIdInstr(r0)
    //     0xa67260: ldur            x4, [x0, #-1]
    //     0xa67264: ubfx            x4, x4, #0xc, #0x14
    // 0xa67268: sub             x4, x4, #0x3c
    // 0xa6726c: cmp             x4, #1
    // 0xa67270: b.ls            #0xa67284
    // 0xa67274: r8 = int
    //     0xa67274: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa67278: r3 = Null
    //     0xa67278: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c58] Null
    //     0xa6727c: ldr             x3, [x3, #0xc58]
    // 0xa67280: r0 = int()
    //     0xa67280: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa67284: ldur            x1, [fp, #-0x38]
    // 0xa67288: r2 = 2
    //     0xa67288: movz            x2, #0x2
    // 0xa6728c: r0 = _getValueOrData()
    //     0xa6728c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67290: ldur            x3, [fp, #-0x38]
    // 0xa67294: LoadField: r1 = r3->field_f
    //     0xa67294: ldur            w1, [x3, #0xf]
    // 0xa67298: DecompressPointer r1
    //     0xa67298: add             x1, x1, HEAP, lsl #32
    // 0xa6729c: cmp             w1, w0
    // 0xa672a0: b.ne            #0xa672ac
    // 0xa672a4: r4 = Null
    //     0xa672a4: mov             x4, NULL
    // 0xa672a8: b               #0xa672b0
    // 0xa672ac: mov             x4, x0
    // 0xa672b0: mov             x0, x4
    // 0xa672b4: stur            x4, [fp, #-0x20]
    // 0xa672b8: r2 = Null
    //     0xa672b8: mov             x2, NULL
    // 0xa672bc: r1 = Null
    //     0xa672bc: mov             x1, NULL
    // 0xa672c0: r4 = 60
    //     0xa672c0: movz            x4, #0x3c
    // 0xa672c4: branchIfSmi(r0, 0xa672d0)
    //     0xa672c4: tbz             w0, #0, #0xa672d0
    // 0xa672c8: r4 = LoadClassIdInstr(r0)
    //     0xa672c8: ldur            x4, [x0, #-1]
    //     0xa672cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa672d0: sub             x4, x4, #0x5e
    // 0xa672d4: cmp             x4, #1
    // 0xa672d8: b.ls            #0xa672ec
    // 0xa672dc: r8 = String
    //     0xa672dc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa672e0: r3 = Null
    //     0xa672e0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c68] Null
    //     0xa672e4: ldr             x3, [x3, #0xc68]
    // 0xa672e8: r0 = String()
    //     0xa672e8: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa672ec: ldur            x1, [fp, #-0x38]
    // 0xa672f0: r2 = 4
    //     0xa672f0: movz            x2, #0x4
    // 0xa672f4: r0 = _getValueOrData()
    //     0xa672f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa672f8: ldur            x3, [fp, #-0x38]
    // 0xa672fc: LoadField: r1 = r3->field_f
    //     0xa672fc: ldur            w1, [x3, #0xf]
    // 0xa67300: DecompressPointer r1
    //     0xa67300: add             x1, x1, HEAP, lsl #32
    // 0xa67304: cmp             w1, w0
    // 0xa67308: b.ne            #0xa67314
    // 0xa6730c: r4 = Null
    //     0xa6730c: mov             x4, NULL
    // 0xa67310: b               #0xa67318
    // 0xa67314: mov             x4, x0
    // 0xa67318: mov             x0, x4
    // 0xa6731c: stur            x4, [fp, #-0x40]
    // 0xa67320: r2 = Null
    //     0xa67320: mov             x2, NULL
    // 0xa67324: r1 = Null
    //     0xa67324: mov             x1, NULL
    // 0xa67328: r4 = 60
    //     0xa67328: movz            x4, #0x3c
    // 0xa6732c: branchIfSmi(r0, 0xa67338)
    //     0xa6732c: tbz             w0, #0, #0xa67338
    // 0xa67330: r4 = LoadClassIdInstr(r0)
    //     0xa67330: ldur            x4, [x0, #-1]
    //     0xa67334: ubfx            x4, x4, #0xc, #0x14
    // 0xa67338: sub             x4, x4, #0x5e
    // 0xa6733c: cmp             x4, #1
    // 0xa67340: b.ls            #0xa67354
    // 0xa67344: r8 = String
    //     0xa67344: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa67348: r3 = Null
    //     0xa67348: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c78] Null
    //     0xa6734c: ldr             x3, [x3, #0xc78]
    // 0xa67350: r0 = String()
    //     0xa67350: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa67354: ldur            x1, [fp, #-0x38]
    // 0xa67358: r2 = 6
    //     0xa67358: movz            x2, #0x6
    // 0xa6735c: r0 = _getValueOrData()
    //     0xa6735c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67360: ldur            x3, [fp, #-0x38]
    // 0xa67364: LoadField: r1 = r3->field_f
    //     0xa67364: ldur            w1, [x3, #0xf]
    // 0xa67368: DecompressPointer r1
    //     0xa67368: add             x1, x1, HEAP, lsl #32
    // 0xa6736c: cmp             w1, w0
    // 0xa67370: b.ne            #0xa6737c
    // 0xa67374: r4 = Null
    //     0xa67374: mov             x4, NULL
    // 0xa67378: b               #0xa67380
    // 0xa6737c: mov             x4, x0
    // 0xa67380: mov             x0, x4
    // 0xa67384: stur            x4, [fp, #-0x48]
    // 0xa67388: r2 = Null
    //     0xa67388: mov             x2, NULL
    // 0xa6738c: r1 = Null
    //     0xa6738c: mov             x1, NULL
    // 0xa67390: branchIfSmi(r0, 0xa673b8)
    //     0xa67390: tbz             w0, #0, #0xa673b8
    // 0xa67394: r4 = LoadClassIdInstr(r0)
    //     0xa67394: ldur            x4, [x0, #-1]
    //     0xa67398: ubfx            x4, x4, #0xc, #0x14
    // 0xa6739c: sub             x4, x4, #0x3c
    // 0xa673a0: cmp             x4, #1
    // 0xa673a4: b.ls            #0xa673b8
    // 0xa673a8: r8 = int
    //     0xa673a8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa673ac: r3 = Null
    //     0xa673ac: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c88] Null
    //     0xa673b0: ldr             x3, [x3, #0xc88]
    // 0xa673b4: r0 = int()
    //     0xa673b4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa673b8: ldur            x1, [fp, #-0x38]
    // 0xa673bc: r2 = 8
    //     0xa673bc: movz            x2, #0x8
    // 0xa673c0: r0 = _getValueOrData()
    //     0xa673c0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa673c4: mov             x1, x0
    // 0xa673c8: ldur            x0, [fp, #-0x38]
    // 0xa673cc: LoadField: r2 = r0->field_f
    //     0xa673cc: ldur            w2, [x0, #0xf]
    // 0xa673d0: DecompressPointer r2
    //     0xa673d0: add             x2, x2, HEAP, lsl #32
    // 0xa673d4: cmp             w2, w1
    // 0xa673d8: b.ne            #0xa673e4
    // 0xa673dc: r7 = Null
    //     0xa673dc: mov             x7, NULL
    // 0xa673e0: b               #0xa673e8
    // 0xa673e4: mov             x7, x1
    // 0xa673e8: ldur            x6, [fp, #-0x18]
    // 0xa673ec: ldur            x5, [fp, #-0x20]
    // 0xa673f0: ldur            x4, [fp, #-0x40]
    // 0xa673f4: ldur            x3, [fp, #-0x48]
    // 0xa673f8: mov             x0, x7
    // 0xa673fc: stur            x7, [fp, #-0x38]
    // 0xa67400: r2 = Null
    //     0xa67400: mov             x2, NULL
    // 0xa67404: r1 = Null
    //     0xa67404: mov             x1, NULL
    // 0xa67408: r4 = 60
    //     0xa67408: movz            x4, #0x3c
    // 0xa6740c: branchIfSmi(r0, 0xa67418)
    //     0xa6740c: tbz             w0, #0, #0xa67418
    // 0xa67410: r4 = LoadClassIdInstr(r0)
    //     0xa67410: ldur            x4, [x0, #-1]
    //     0xa67414: ubfx            x4, x4, #0xc, #0x14
    // 0xa67418: sub             x4, x4, #0x5e
    // 0xa6741c: cmp             x4, #1
    // 0xa67420: b.ls            #0xa67434
    // 0xa67424: r8 = String?
    //     0xa67424: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67428: r3 = Null
    //     0xa67428: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c98] Null
    //     0xa6742c: ldr             x3, [x3, #0xc98]
    // 0xa67430: r0 = String?()
    //     0xa67430: bl              #0x600324  ; IsType_String?_Stub
    // 0xa67434: ldur            x0, [fp, #-0x18]
    // 0xa67438: r1 = LoadInt32Instr(r0)
    //     0xa67438: sbfx            x1, x0, #1, #0x1f
    //     0xa6743c: tbz             w0, #0, #0xa67444
    //     0xa67440: ldur            x1, [x0, #7]
    // 0xa67444: stur            x1, [fp, #-8]
    // 0xa67448: r0 = Tutorial()
    //     0xa67448: bl              #0x91d2e8  ; AllocateTutorialStub -> Tutorial (size=0x24)
    // 0xa6744c: mov             x1, x0
    // 0xa67450: ldur            x0, [fp, #-8]
    // 0xa67454: StoreField: r1->field_7 = r0
    //     0xa67454: stur            x0, [x1, #7]
    // 0xa67458: ldur            x0, [fp, #-0x20]
    // 0xa6745c: StoreField: r1->field_f = r0
    //     0xa6745c: stur            w0, [x1, #0xf]
    // 0xa67460: ldur            x0, [fp, #-0x40]
    // 0xa67464: StoreField: r1->field_13 = r0
    //     0xa67464: stur            w0, [x1, #0x13]
    // 0xa67468: ldur            x0, [fp, #-0x48]
    // 0xa6746c: r2 = LoadInt32Instr(r0)
    //     0xa6746c: sbfx            x2, x0, #1, #0x1f
    //     0xa67470: tbz             w0, #0, #0xa67478
    //     0xa67474: ldur            x2, [x0, #7]
    // 0xa67478: ArrayStore: r1[0] = r2  ; List_8
    //     0xa67478: stur            x2, [x1, #0x17]
    // 0xa6747c: ldur            x0, [fp, #-0x38]
    // 0xa67480: StoreField: r1->field_1f = r0
    //     0xa67480: stur            w0, [x1, #0x1f]
    // 0xa67484: mov             x0, x1
    // 0xa67488: LeaveFrame
    //     0xa67488: mov             SP, fp
    //     0xa6748c: ldp             fp, lr, [SP], #0x10
    // 0xa67490: ret
    //     0xa67490: ret             
    // 0xa67494: r0 = RangeError()
    //     0xa67494: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa67498: mov             x1, x0
    // 0xa6749c: r0 = "Not enough bytes available."
    //     0xa6749c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa674a0: ldr             x0, [x0, #0x8a8]
    // 0xa674a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa674a4: stur            w0, [x1, #0x17]
    // 0xa674a8: r2 = false
    //     0xa674a8: add             x2, NULL, #0x30  ; false
    // 0xa674ac: StoreField: r1->field_b = r2
    //     0xa674ac: stur            w2, [x1, #0xb]
    // 0xa674b0: mov             x0, x1
    // 0xa674b4: r0 = Throw()
    //     0xa674b4: bl              #0xec04b8  ; ThrowStub
    // 0xa674b8: brk             #0
    // 0xa674bc: r0 = "Not enough bytes available."
    //     0xa674bc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa674c0: ldr             x0, [x0, #0x8a8]
    // 0xa674c4: r2 = false
    //     0xa674c4: add             x2, NULL, #0x30  ; false
    // 0xa674c8: r0 = RangeError()
    //     0xa674c8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa674cc: mov             x1, x0
    // 0xa674d0: r0 = "Not enough bytes available."
    //     0xa674d0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa674d4: ldr             x0, [x0, #0x8a8]
    // 0xa674d8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa674d8: stur            w0, [x1, #0x17]
    // 0xa674dc: r0 = false
    //     0xa674dc: add             x0, NULL, #0x30  ; false
    // 0xa674e0: StoreField: r1->field_b = r0
    //     0xa674e0: stur            w0, [x1, #0xb]
    // 0xa674e4: mov             x0, x1
    // 0xa674e8: r0 = Throw()
    //     0xa674e8: bl              #0xec04b8  ; ThrowStub
    // 0xa674ec: brk             #0
    // 0xa674f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa674f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa674f4: b               #0xa67104
    // 0xa674f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa674f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa674fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa674fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa67500: b               #0xa6718c
    // 0xa67504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa67504: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd841c, size: 0x3bc
    // 0xbd841c: EnterFrame
    //     0xbd841c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8420: mov             fp, SP
    // 0xbd8424: AllocStack(0x28)
    //     0xbd8424: sub             SP, SP, #0x28
    // 0xbd8428: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd8428: mov             x4, x2
    //     0xbd842c: stur            x2, [fp, #-8]
    //     0xbd8430: stur            x3, [fp, #-0x10]
    // 0xbd8434: CheckStackOverflow
    //     0xbd8434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8438: cmp             SP, x16
    //     0xbd843c: b.ls            #0xbd87b8
    // 0xbd8440: mov             x0, x3
    // 0xbd8444: r2 = Null
    //     0xbd8444: mov             x2, NULL
    // 0xbd8448: r1 = Null
    //     0xbd8448: mov             x1, NULL
    // 0xbd844c: r4 = 60
    //     0xbd844c: movz            x4, #0x3c
    // 0xbd8450: branchIfSmi(r0, 0xbd845c)
    //     0xbd8450: tbz             w0, #0, #0xbd845c
    // 0xbd8454: r4 = LoadClassIdInstr(r0)
    //     0xbd8454: ldur            x4, [x0, #-1]
    //     0xbd8458: ubfx            x4, x4, #0xc, #0x14
    // 0xbd845c: r17 = 5569
    //     0xbd845c: movz            x17, #0x15c1
    // 0xbd8460: cmp             x4, x17
    // 0xbd8464: b.eq            #0xbd847c
    // 0xbd8468: r8 = Tutorial
    //     0xbd8468: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b0b8] Type: Tutorial
    //     0xbd846c: ldr             x8, [x8, #0xb8]
    // 0xbd8470: r3 = Null
    //     0xbd8470: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0c0] Null
    //     0xbd8474: ldr             x3, [x3, #0xc0]
    // 0xbd8478: r0 = Tutorial()
    //     0xbd8478: bl              #0x835fbc  ; IsType_Tutorial_Stub
    // 0xbd847c: ldur            x0, [fp, #-8]
    // 0xbd8480: LoadField: r1 = r0->field_b
    //     0xbd8480: ldur            w1, [x0, #0xb]
    // 0xbd8484: DecompressPointer r1
    //     0xbd8484: add             x1, x1, HEAP, lsl #32
    // 0xbd8488: LoadField: r2 = r1->field_13
    //     0xbd8488: ldur            w2, [x1, #0x13]
    // 0xbd848c: LoadField: r1 = r0->field_13
    //     0xbd848c: ldur            x1, [x0, #0x13]
    // 0xbd8490: r3 = LoadInt32Instr(r2)
    //     0xbd8490: sbfx            x3, x2, #1, #0x1f
    // 0xbd8494: sub             x2, x3, x1
    // 0xbd8498: cmp             x2, #1
    // 0xbd849c: b.ge            #0xbd84ac
    // 0xbd84a0: mov             x1, x0
    // 0xbd84a4: r2 = 1
    //     0xbd84a4: movz            x2, #0x1
    // 0xbd84a8: r0 = _increaseBufferSize()
    //     0xbd84a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd84ac: ldur            x3, [fp, #-8]
    // 0xbd84b0: r2 = 5
    //     0xbd84b0: movz            x2, #0x5
    // 0xbd84b4: LoadField: r4 = r3->field_b
    //     0xbd84b4: ldur            w4, [x3, #0xb]
    // 0xbd84b8: DecompressPointer r4
    //     0xbd84b8: add             x4, x4, HEAP, lsl #32
    // 0xbd84bc: LoadField: r5 = r3->field_13
    //     0xbd84bc: ldur            x5, [x3, #0x13]
    // 0xbd84c0: add             x6, x5, #1
    // 0xbd84c4: StoreField: r3->field_13 = r6
    //     0xbd84c4: stur            x6, [x3, #0x13]
    // 0xbd84c8: LoadField: r0 = r4->field_13
    //     0xbd84c8: ldur            w0, [x4, #0x13]
    // 0xbd84cc: r7 = LoadInt32Instr(r0)
    //     0xbd84cc: sbfx            x7, x0, #1, #0x1f
    // 0xbd84d0: mov             x0, x7
    // 0xbd84d4: mov             x1, x5
    // 0xbd84d8: cmp             x1, x0
    // 0xbd84dc: b.hs            #0xbd87c0
    // 0xbd84e0: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd84e0: add             x0, x4, x5
    //     0xbd84e4: strb            w2, [x0, #0x17]
    // 0xbd84e8: sub             x0, x7, x6
    // 0xbd84ec: cmp             x0, #1
    // 0xbd84f0: b.ge            #0xbd8500
    // 0xbd84f4: mov             x1, x3
    // 0xbd84f8: r2 = 1
    //     0xbd84f8: movz            x2, #0x1
    // 0xbd84fc: r0 = _increaseBufferSize()
    //     0xbd84fc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8500: ldur            x2, [fp, #-8]
    // 0xbd8504: ldur            x3, [fp, #-0x10]
    // 0xbd8508: LoadField: r4 = r2->field_b
    //     0xbd8508: ldur            w4, [x2, #0xb]
    // 0xbd850c: DecompressPointer r4
    //     0xbd850c: add             x4, x4, HEAP, lsl #32
    // 0xbd8510: LoadField: r5 = r2->field_13
    //     0xbd8510: ldur            x5, [x2, #0x13]
    // 0xbd8514: add             x0, x5, #1
    // 0xbd8518: StoreField: r2->field_13 = r0
    //     0xbd8518: stur            x0, [x2, #0x13]
    // 0xbd851c: LoadField: r0 = r4->field_13
    //     0xbd851c: ldur            w0, [x4, #0x13]
    // 0xbd8520: r1 = LoadInt32Instr(r0)
    //     0xbd8520: sbfx            x1, x0, #1, #0x1f
    // 0xbd8524: mov             x0, x1
    // 0xbd8528: mov             x1, x5
    // 0xbd852c: cmp             x1, x0
    // 0xbd8530: b.hs            #0xbd87c4
    // 0xbd8534: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd8534: add             x0, x4, x5
    //     0xbd8538: strb            wzr, [x0, #0x17]
    // 0xbd853c: LoadField: r4 = r3->field_7
    //     0xbd853c: ldur            x4, [x3, #7]
    // 0xbd8540: r0 = BoxInt64Instr(r4)
    //     0xbd8540: sbfiz           x0, x4, #1, #0x1f
    //     0xbd8544: cmp             x4, x0, asr #1
    //     0xbd8548: b.eq            #0xbd8554
    //     0xbd854c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8550: stur            x4, [x0, #7]
    // 0xbd8554: r16 = <int>
    //     0xbd8554: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8558: stp             x2, x16, [SP, #8]
    // 0xbd855c: str             x0, [SP]
    // 0xbd8560: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8560: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8564: r0 = write()
    //     0xbd8564: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8568: ldur            x0, [fp, #-8]
    // 0xbd856c: LoadField: r1 = r0->field_b
    //     0xbd856c: ldur            w1, [x0, #0xb]
    // 0xbd8570: DecompressPointer r1
    //     0xbd8570: add             x1, x1, HEAP, lsl #32
    // 0xbd8574: LoadField: r2 = r1->field_13
    //     0xbd8574: ldur            w2, [x1, #0x13]
    // 0xbd8578: LoadField: r1 = r0->field_13
    //     0xbd8578: ldur            x1, [x0, #0x13]
    // 0xbd857c: r3 = LoadInt32Instr(r2)
    //     0xbd857c: sbfx            x3, x2, #1, #0x1f
    // 0xbd8580: sub             x2, x3, x1
    // 0xbd8584: cmp             x2, #1
    // 0xbd8588: b.ge            #0xbd8598
    // 0xbd858c: mov             x1, x0
    // 0xbd8590: r2 = 1
    //     0xbd8590: movz            x2, #0x1
    // 0xbd8594: r0 = _increaseBufferSize()
    //     0xbd8594: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8598: ldur            x2, [fp, #-8]
    // 0xbd859c: ldur            x3, [fp, #-0x10]
    // 0xbd85a0: r4 = 1
    //     0xbd85a0: movz            x4, #0x1
    // 0xbd85a4: LoadField: r5 = r2->field_b
    //     0xbd85a4: ldur            w5, [x2, #0xb]
    // 0xbd85a8: DecompressPointer r5
    //     0xbd85a8: add             x5, x5, HEAP, lsl #32
    // 0xbd85ac: LoadField: r6 = r2->field_13
    //     0xbd85ac: ldur            x6, [x2, #0x13]
    // 0xbd85b0: add             x0, x6, #1
    // 0xbd85b4: StoreField: r2->field_13 = r0
    //     0xbd85b4: stur            x0, [x2, #0x13]
    // 0xbd85b8: LoadField: r0 = r5->field_13
    //     0xbd85b8: ldur            w0, [x5, #0x13]
    // 0xbd85bc: r1 = LoadInt32Instr(r0)
    //     0xbd85bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd85c0: mov             x0, x1
    // 0xbd85c4: mov             x1, x6
    // 0xbd85c8: cmp             x1, x0
    // 0xbd85cc: b.hs            #0xbd87c8
    // 0xbd85d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd85d0: add             x0, x5, x6
    //     0xbd85d4: strb            w4, [x0, #0x17]
    // 0xbd85d8: LoadField: r0 = r3->field_f
    //     0xbd85d8: ldur            w0, [x3, #0xf]
    // 0xbd85dc: DecompressPointer r0
    //     0xbd85dc: add             x0, x0, HEAP, lsl #32
    // 0xbd85e0: r16 = <String>
    //     0xbd85e0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd85e4: stp             x2, x16, [SP, #8]
    // 0xbd85e8: str             x0, [SP]
    // 0xbd85ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd85ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd85f0: r0 = write()
    //     0xbd85f0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd85f4: ldur            x0, [fp, #-8]
    // 0xbd85f8: LoadField: r1 = r0->field_b
    //     0xbd85f8: ldur            w1, [x0, #0xb]
    // 0xbd85fc: DecompressPointer r1
    //     0xbd85fc: add             x1, x1, HEAP, lsl #32
    // 0xbd8600: LoadField: r2 = r1->field_13
    //     0xbd8600: ldur            w2, [x1, #0x13]
    // 0xbd8604: LoadField: r1 = r0->field_13
    //     0xbd8604: ldur            x1, [x0, #0x13]
    // 0xbd8608: r3 = LoadInt32Instr(r2)
    //     0xbd8608: sbfx            x3, x2, #1, #0x1f
    // 0xbd860c: sub             x2, x3, x1
    // 0xbd8610: cmp             x2, #1
    // 0xbd8614: b.ge            #0xbd8624
    // 0xbd8618: mov             x1, x0
    // 0xbd861c: r2 = 1
    //     0xbd861c: movz            x2, #0x1
    // 0xbd8620: r0 = _increaseBufferSize()
    //     0xbd8620: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8624: ldur            x2, [fp, #-8]
    // 0xbd8628: ldur            x3, [fp, #-0x10]
    // 0xbd862c: r4 = 2
    //     0xbd862c: movz            x4, #0x2
    // 0xbd8630: LoadField: r5 = r2->field_b
    //     0xbd8630: ldur            w5, [x2, #0xb]
    // 0xbd8634: DecompressPointer r5
    //     0xbd8634: add             x5, x5, HEAP, lsl #32
    // 0xbd8638: LoadField: r6 = r2->field_13
    //     0xbd8638: ldur            x6, [x2, #0x13]
    // 0xbd863c: add             x0, x6, #1
    // 0xbd8640: StoreField: r2->field_13 = r0
    //     0xbd8640: stur            x0, [x2, #0x13]
    // 0xbd8644: LoadField: r0 = r5->field_13
    //     0xbd8644: ldur            w0, [x5, #0x13]
    // 0xbd8648: r1 = LoadInt32Instr(r0)
    //     0xbd8648: sbfx            x1, x0, #1, #0x1f
    // 0xbd864c: mov             x0, x1
    // 0xbd8650: mov             x1, x6
    // 0xbd8654: cmp             x1, x0
    // 0xbd8658: b.hs            #0xbd87cc
    // 0xbd865c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd865c: add             x0, x5, x6
    //     0xbd8660: strb            w4, [x0, #0x17]
    // 0xbd8664: LoadField: r0 = r3->field_13
    //     0xbd8664: ldur            w0, [x3, #0x13]
    // 0xbd8668: DecompressPointer r0
    //     0xbd8668: add             x0, x0, HEAP, lsl #32
    // 0xbd866c: r16 = <String>
    //     0xbd866c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd8670: stp             x2, x16, [SP, #8]
    // 0xbd8674: str             x0, [SP]
    // 0xbd8678: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8678: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd867c: r0 = write()
    //     0xbd867c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8680: ldur            x0, [fp, #-8]
    // 0xbd8684: LoadField: r1 = r0->field_b
    //     0xbd8684: ldur            w1, [x0, #0xb]
    // 0xbd8688: DecompressPointer r1
    //     0xbd8688: add             x1, x1, HEAP, lsl #32
    // 0xbd868c: LoadField: r2 = r1->field_13
    //     0xbd868c: ldur            w2, [x1, #0x13]
    // 0xbd8690: LoadField: r1 = r0->field_13
    //     0xbd8690: ldur            x1, [x0, #0x13]
    // 0xbd8694: r3 = LoadInt32Instr(r2)
    //     0xbd8694: sbfx            x3, x2, #1, #0x1f
    // 0xbd8698: sub             x2, x3, x1
    // 0xbd869c: cmp             x2, #1
    // 0xbd86a0: b.ge            #0xbd86b0
    // 0xbd86a4: mov             x1, x0
    // 0xbd86a8: r2 = 1
    //     0xbd86a8: movz            x2, #0x1
    // 0xbd86ac: r0 = _increaseBufferSize()
    //     0xbd86ac: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd86b0: ldur            x2, [fp, #-8]
    // 0xbd86b4: ldur            x3, [fp, #-0x10]
    // 0xbd86b8: r4 = 3
    //     0xbd86b8: movz            x4, #0x3
    // 0xbd86bc: LoadField: r5 = r2->field_b
    //     0xbd86bc: ldur            w5, [x2, #0xb]
    // 0xbd86c0: DecompressPointer r5
    //     0xbd86c0: add             x5, x5, HEAP, lsl #32
    // 0xbd86c4: LoadField: r6 = r2->field_13
    //     0xbd86c4: ldur            x6, [x2, #0x13]
    // 0xbd86c8: add             x0, x6, #1
    // 0xbd86cc: StoreField: r2->field_13 = r0
    //     0xbd86cc: stur            x0, [x2, #0x13]
    // 0xbd86d0: LoadField: r0 = r5->field_13
    //     0xbd86d0: ldur            w0, [x5, #0x13]
    // 0xbd86d4: r1 = LoadInt32Instr(r0)
    //     0xbd86d4: sbfx            x1, x0, #1, #0x1f
    // 0xbd86d8: mov             x0, x1
    // 0xbd86dc: mov             x1, x6
    // 0xbd86e0: cmp             x1, x0
    // 0xbd86e4: b.hs            #0xbd87d0
    // 0xbd86e8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd86e8: add             x0, x5, x6
    //     0xbd86ec: strb            w4, [x0, #0x17]
    // 0xbd86f0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd86f0: ldur            x4, [x3, #0x17]
    // 0xbd86f4: r0 = BoxInt64Instr(r4)
    //     0xbd86f4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd86f8: cmp             x4, x0, asr #1
    //     0xbd86fc: b.eq            #0xbd8708
    //     0xbd8700: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8704: stur            x4, [x0, #7]
    // 0xbd8708: r16 = <int>
    //     0xbd8708: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd870c: stp             x2, x16, [SP, #8]
    // 0xbd8710: str             x0, [SP]
    // 0xbd8714: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8714: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8718: r0 = write()
    //     0xbd8718: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd871c: ldur            x0, [fp, #-8]
    // 0xbd8720: LoadField: r1 = r0->field_b
    //     0xbd8720: ldur            w1, [x0, #0xb]
    // 0xbd8724: DecompressPointer r1
    //     0xbd8724: add             x1, x1, HEAP, lsl #32
    // 0xbd8728: LoadField: r2 = r1->field_13
    //     0xbd8728: ldur            w2, [x1, #0x13]
    // 0xbd872c: LoadField: r1 = r0->field_13
    //     0xbd872c: ldur            x1, [x0, #0x13]
    // 0xbd8730: r3 = LoadInt32Instr(r2)
    //     0xbd8730: sbfx            x3, x2, #1, #0x1f
    // 0xbd8734: sub             x2, x3, x1
    // 0xbd8738: cmp             x2, #1
    // 0xbd873c: b.ge            #0xbd874c
    // 0xbd8740: mov             x1, x0
    // 0xbd8744: r2 = 1
    //     0xbd8744: movz            x2, #0x1
    // 0xbd8748: r0 = _increaseBufferSize()
    //     0xbd8748: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd874c: ldur            x2, [fp, #-8]
    // 0xbd8750: ldur            x3, [fp, #-0x10]
    // 0xbd8754: r4 = 4
    //     0xbd8754: movz            x4, #0x4
    // 0xbd8758: LoadField: r5 = r2->field_b
    //     0xbd8758: ldur            w5, [x2, #0xb]
    // 0xbd875c: DecompressPointer r5
    //     0xbd875c: add             x5, x5, HEAP, lsl #32
    // 0xbd8760: LoadField: r6 = r2->field_13
    //     0xbd8760: ldur            x6, [x2, #0x13]
    // 0xbd8764: add             x0, x6, #1
    // 0xbd8768: StoreField: r2->field_13 = r0
    //     0xbd8768: stur            x0, [x2, #0x13]
    // 0xbd876c: LoadField: r0 = r5->field_13
    //     0xbd876c: ldur            w0, [x5, #0x13]
    // 0xbd8770: r1 = LoadInt32Instr(r0)
    //     0xbd8770: sbfx            x1, x0, #1, #0x1f
    // 0xbd8774: mov             x0, x1
    // 0xbd8778: mov             x1, x6
    // 0xbd877c: cmp             x1, x0
    // 0xbd8780: b.hs            #0xbd87d4
    // 0xbd8784: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8784: add             x0, x5, x6
    //     0xbd8788: strb            w4, [x0, #0x17]
    // 0xbd878c: LoadField: r0 = r3->field_1f
    //     0xbd878c: ldur            w0, [x3, #0x1f]
    // 0xbd8790: DecompressPointer r0
    //     0xbd8790: add             x0, x0, HEAP, lsl #32
    // 0xbd8794: r16 = <String?>
    //     0xbd8794: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd8798: stp             x2, x16, [SP, #8]
    // 0xbd879c: str             x0, [SP]
    // 0xbd87a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd87a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd87a4: r0 = write()
    //     0xbd87a4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd87a8: r0 = Null
    //     0xbd87a8: mov             x0, NULL
    // 0xbd87ac: LeaveFrame
    //     0xbd87ac: mov             SP, fp
    //     0xbd87b0: ldp             fp, lr, [SP], #0x10
    // 0xbd87b4: ret
    //     0xbd87b4: ret             
    // 0xbd87b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd87b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd87bc: b               #0xbd8440
    // 0xbd87c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd87c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd87c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd87cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd87d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd87d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd87d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf03cc, size: 0x24
    // 0xbf03cc: r1 = 140
    //     0xbf03cc: movz            x1, #0x8c
    // 0xbf03d0: r16 = LoadInt32Instr(r1)
    //     0xbf03d0: sbfx            x16, x1, #1, #0x1f
    // 0xbf03d4: r17 = 11601
    //     0xbf03d4: movz            x17, #0x2d51
    // 0xbf03d8: mul             x0, x16, x17
    // 0xbf03dc: umulh           x16, x16, x17
    // 0xbf03e0: eor             x0, x0, x16
    // 0xbf03e4: r0 = 0
    //     0xbf03e4: eor             x0, x0, x0, lsr #32
    // 0xbf03e8: ubfiz           x0, x0, #1, #0x1e
    // 0xbf03ec: ret
    //     0xbf03ec: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd770e8, size: 0x9c
    // 0xd770e8: EnterFrame
    //     0xd770e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd770ec: mov             fp, SP
    // 0xd770f0: AllocStack(0x10)
    //     0xd770f0: sub             SP, SP, #0x10
    // 0xd770f4: CheckStackOverflow
    //     0xd770f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd770f8: cmp             SP, x16
    //     0xd770fc: b.ls            #0xd7717c
    // 0xd77100: ldr             x0, [fp, #0x10]
    // 0xd77104: cmp             w0, NULL
    // 0xd77108: b.ne            #0xd7711c
    // 0xd7710c: r0 = false
    //     0xd7710c: add             x0, NULL, #0x30  ; false
    // 0xd77110: LeaveFrame
    //     0xd77110: mov             SP, fp
    //     0xd77114: ldp             fp, lr, [SP], #0x10
    // 0xd77118: ret
    //     0xd77118: ret             
    // 0xd7711c: ldr             x1, [fp, #0x18]
    // 0xd77120: cmp             w1, w0
    // 0xd77124: b.ne            #0xd77130
    // 0xd77128: r0 = true
    //     0xd77128: add             x0, NULL, #0x20  ; true
    // 0xd7712c: b               #0xd77170
    // 0xd77130: r1 = 60
    //     0xd77130: movz            x1, #0x3c
    // 0xd77134: branchIfSmi(r0, 0xd77140)
    //     0xd77134: tbz             w0, #0, #0xd77140
    // 0xd77138: r1 = LoadClassIdInstr(r0)
    //     0xd77138: ldur            x1, [x0, #-1]
    //     0xd7713c: ubfx            x1, x1, #0xc, #0x14
    // 0xd77140: cmp             x1, #0x66b
    // 0xd77144: b.ne            #0xd7716c
    // 0xd77148: r16 = TutorialAdapter
    //     0xd77148: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0b0] Type: TutorialAdapter
    //     0xd7714c: ldr             x16, [x16, #0xb0]
    // 0xd77150: r30 = TutorialAdapter
    //     0xd77150: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b0b0] Type: TutorialAdapter
    //     0xd77154: ldr             lr, [lr, #0xb0]
    // 0xd77158: stp             lr, x16, [SP]
    // 0xd7715c: r0 = ==()
    //     0xd7715c: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd77160: tbnz            w0, #4, #0xd7716c
    // 0xd77164: r0 = true
    //     0xd77164: add             x0, NULL, #0x20  ; true
    // 0xd77168: b               #0xd77170
    // 0xd7716c: r0 = false
    //     0xd7716c: add             x0, NULL, #0x30  ; false
    // 0xd77170: LeaveFrame
    //     0xd77170: mov             SP, fp
    //     0xd77174: ldp             fp, lr, [SP], #0x10
    // 0xd77178: ret
    //     0xd77178: ret             
    // 0xd7717c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7717c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd77180: b               #0xd77100
  }
}

// class id: 5566, size: 0x18, field offset: 0x8
//   const constructor, 
class TutorialSearch extends Equatable {

  static _ fromResponse(/* No info */) {
    // ** addr: 0xe39130, size: 0x29c
    // 0xe39130: EnterFrame
    //     0xe39130: stp             fp, lr, [SP, #-0x10]!
    //     0xe39134: mov             fp, SP
    // 0xe39138: AllocStack(0x20)
    //     0xe39138: sub             SP, SP, #0x20
    // 0xe3913c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe3913c: mov             x3, x1
    //     0xe39140: stur            x1, [fp, #-8]
    // 0xe39144: CheckStackOverflow
    //     0xe39144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe39148: cmp             SP, x16
    //     0xe3914c: b.ls            #0xe393c4
    // 0xe39150: mov             x0, x3
    // 0xe39154: r2 = Null
    //     0xe39154: mov             x2, NULL
    // 0xe39158: r1 = Null
    //     0xe39158: mov             x1, NULL
    // 0xe3915c: cmp             w0, NULL
    // 0xe39160: b.eq            #0xe391f8
    // 0xe39164: branchIfSmi(r0, 0xe391f8)
    //     0xe39164: tbz             w0, #0, #0xe391f8
    // 0xe39168: r3 = LoadClassIdInstr(r0)
    //     0xe39168: ldur            x3, [x0, #-1]
    //     0xe3916c: ubfx            x3, x3, #0xc, #0x14
    // 0xe39170: r17 = 6717
    //     0xe39170: movz            x17, #0x1a3d
    // 0xe39174: cmp             x3, x17
    // 0xe39178: b.eq            #0xe39200
    // 0xe3917c: r4 = LoadClassIdInstr(r0)
    //     0xe3917c: ldur            x4, [x0, #-1]
    //     0xe39180: ubfx            x4, x4, #0xc, #0x14
    // 0xe39184: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe39188: ldr             x3, [x3, #0x18]
    // 0xe3918c: ldr             x3, [x3, x4, lsl #3]
    // 0xe39190: LoadField: r3 = r3->field_2b
    //     0xe39190: ldur            w3, [x3, #0x2b]
    // 0xe39194: DecompressPointer r3
    //     0xe39194: add             x3, x3, HEAP, lsl #32
    // 0xe39198: cmp             w3, NULL
    // 0xe3919c: b.eq            #0xe391f8
    // 0xe391a0: LoadField: r3 = r3->field_f
    //     0xe391a0: ldur            w3, [x3, #0xf]
    // 0xe391a4: lsr             x3, x3, #3
    // 0xe391a8: r17 = 6717
    //     0xe391a8: movz            x17, #0x1a3d
    // 0xe391ac: cmp             x3, x17
    // 0xe391b0: b.eq            #0xe39200
    // 0xe391b4: r3 = SubtypeTestCache
    //     0xe391b4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d818] SubtypeTestCache
    //     0xe391b8: ldr             x3, [x3, #0x818]
    // 0xe391bc: r30 = Subtype1TestCacheStub
    //     0xe391bc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe391c0: LoadField: r30 = r30->field_7
    //     0xe391c0: ldur            lr, [lr, #7]
    // 0xe391c4: blr             lr
    // 0xe391c8: cmp             w7, NULL
    // 0xe391cc: b.eq            #0xe391d8
    // 0xe391d0: tbnz            w7, #4, #0xe391f8
    // 0xe391d4: b               #0xe39200
    // 0xe391d8: r8 = Map
    //     0xe391d8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d820] Type: Map
    //     0xe391dc: ldr             x8, [x8, #0x820]
    // 0xe391e0: r3 = SubtypeTestCache
    //     0xe391e0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d828] SubtypeTestCache
    //     0xe391e4: ldr             x3, [x3, #0x828]
    // 0xe391e8: r30 = InstanceOfStub
    //     0xe391e8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe391ec: LoadField: r30 = r30->field_7
    //     0xe391ec: ldur            lr, [lr, #7]
    // 0xe391f0: blr             lr
    // 0xe391f4: b               #0xe39204
    // 0xe391f8: r0 = false
    //     0xe391f8: add             x0, NULL, #0x30  ; false
    // 0xe391fc: b               #0xe39204
    // 0xe39200: r0 = true
    //     0xe39200: add             x0, NULL, #0x20  ; true
    // 0xe39204: tbnz            w0, #4, #0xe39258
    // 0xe39208: ldur            x3, [fp, #-8]
    // 0xe3920c: r0 = LoadClassIdInstr(r3)
    //     0xe3920c: ldur            x0, [x3, #-1]
    //     0xe39210: ubfx            x0, x0, #0xc, #0x14
    // 0xe39214: mov             x1, x3
    // 0xe39218: r2 = "data"
    //     0xe39218: ldr             x2, [PP, #0xfb0]  ; [pp+0xfb0] "data"
    // 0xe3921c: r0 = GDT[cid_x0 + 0x55f]()
    //     0xe3921c: add             lr, x0, #0x55f
    //     0xe39220: ldr             lr, [x21, lr, lsl #3]
    //     0xe39224: blr             lr
    // 0xe39228: tbnz            w0, #4, #0xe39250
    // 0xe3922c: ldur            x1, [fp, #-8]
    // 0xe39230: r0 = LoadClassIdInstr(r1)
    //     0xe39230: ldur            x0, [x1, #-1]
    //     0xe39234: ubfx            x0, x0, #0xc, #0x14
    // 0xe39238: r2 = "data"
    //     0xe39238: ldr             x2, [PP, #0xfb0]  ; [pp+0xfb0] "data"
    // 0xe3923c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe3923c: sub             lr, x0, #0x114
    //     0xe39240: ldr             lr, [x21, lr, lsl #3]
    //     0xe39244: blr             lr
    // 0xe39248: mov             x3, x0
    // 0xe3924c: b               #0xe39260
    // 0xe39250: ldur            x1, [fp, #-8]
    // 0xe39254: b               #0xe3925c
    // 0xe39258: ldur            x1, [fp, #-8]
    // 0xe3925c: mov             x3, x1
    // 0xe39260: mov             x0, x3
    // 0xe39264: stur            x3, [fp, #-8]
    // 0xe39268: r2 = Null
    //     0xe39268: mov             x2, NULL
    // 0xe3926c: r1 = Null
    //     0xe3926c: mov             x1, NULL
    // 0xe39270: cmp             w0, NULL
    // 0xe39274: b.eq            #0xe39318
    // 0xe39278: branchIfSmi(r0, 0xe39318)
    //     0xe39278: tbz             w0, #0, #0xe39318
    // 0xe3927c: r3 = LoadClassIdInstr(r0)
    //     0xe3927c: ldur            x3, [x0, #-1]
    //     0xe39280: ubfx            x3, x3, #0xc, #0x14
    // 0xe39284: r17 = 6718
    //     0xe39284: movz            x17, #0x1a3e
    // 0xe39288: cmp             x3, x17
    // 0xe3928c: b.eq            #0xe39320
    // 0xe39290: sub             x3, x3, #0x5a
    // 0xe39294: cmp             x3, #2
    // 0xe39298: b.ls            #0xe39320
    // 0xe3929c: r4 = LoadClassIdInstr(r0)
    //     0xe3929c: ldur            x4, [x0, #-1]
    //     0xe392a0: ubfx            x4, x4, #0xc, #0x14
    // 0xe392a4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe392a8: ldr             x3, [x3, #0x18]
    // 0xe392ac: ldr             x3, [x3, x4, lsl #3]
    // 0xe392b0: LoadField: r3 = r3->field_2b
    //     0xe392b0: ldur            w3, [x3, #0x2b]
    // 0xe392b4: DecompressPointer r3
    //     0xe392b4: add             x3, x3, HEAP, lsl #32
    // 0xe392b8: cmp             w3, NULL
    // 0xe392bc: b.eq            #0xe39318
    // 0xe392c0: LoadField: r3 = r3->field_f
    //     0xe392c0: ldur            w3, [x3, #0xf]
    // 0xe392c4: lsr             x3, x3, #3
    // 0xe392c8: r17 = 6718
    //     0xe392c8: movz            x17, #0x1a3e
    // 0xe392cc: cmp             x3, x17
    // 0xe392d0: b.eq            #0xe39320
    // 0xe392d4: r3 = SubtypeTestCache
    //     0xe392d4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d830] SubtypeTestCache
    //     0xe392d8: ldr             x3, [x3, #0x830]
    // 0xe392dc: r30 = Subtype1TestCacheStub
    //     0xe392dc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe392e0: LoadField: r30 = r30->field_7
    //     0xe392e0: ldur            lr, [lr, #7]
    // 0xe392e4: blr             lr
    // 0xe392e8: cmp             w7, NULL
    // 0xe392ec: b.eq            #0xe392f8
    // 0xe392f0: tbnz            w7, #4, #0xe39318
    // 0xe392f4: b               #0xe39320
    // 0xe392f8: r8 = List
    //     0xe392f8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d838] Type: List
    //     0xe392fc: ldr             x8, [x8, #0x838]
    // 0xe39300: r3 = SubtypeTestCache
    //     0xe39300: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d840] SubtypeTestCache
    //     0xe39304: ldr             x3, [x3, #0x840]
    // 0xe39308: r30 = InstanceOfStub
    //     0xe39308: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe3930c: LoadField: r30 = r30->field_7
    //     0xe3930c: ldur            lr, [lr, #7]
    // 0xe39310: blr             lr
    // 0xe39314: b               #0xe39324
    // 0xe39318: r0 = false
    //     0xe39318: add             x0, NULL, #0x30  ; false
    // 0xe3931c: b               #0xe39324
    // 0xe39320: r0 = true
    //     0xe39320: add             x0, NULL, #0x20  ; true
    // 0xe39324: tbnz            w0, #4, #0xe393a8
    // 0xe39328: ldur            x0, [fp, #-8]
    // 0xe3932c: r1 = Function '<anonymous closure>': static.
    //     0xe3932c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d848] AnonymousClosure: static (0xe393cc), in [package:nuonline/app/data/models/tutorial.dart] TutorialSearch::fromResponse (0xe39130)
    //     0xe39330: ldr             x1, [x1, #0x848]
    // 0xe39334: r2 = Null
    //     0xe39334: mov             x2, NULL
    // 0xe39338: r0 = AllocateClosure()
    //     0xe39338: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3933c: mov             x1, x0
    // 0xe39340: ldur            x0, [fp, #-8]
    // 0xe39344: r2 = LoadClassIdInstr(r0)
    //     0xe39344: ldur            x2, [x0, #-1]
    //     0xe39348: ubfx            x2, x2, #0xc, #0x14
    // 0xe3934c: r16 = <TutorialSearch>
    //     0xe3934c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d7f8] TypeArguments: <TutorialSearch>
    //     0xe39350: ldr             x16, [x16, #0x7f8]
    // 0xe39354: stp             x0, x16, [SP, #8]
    // 0xe39358: str             x1, [SP]
    // 0xe3935c: mov             x0, x2
    // 0xe39360: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe39360: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe39364: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe39364: movz            x17, #0xf28c
    //     0xe39368: add             lr, x0, x17
    //     0xe3936c: ldr             lr, [x21, lr, lsl #3]
    //     0xe39370: blr             lr
    // 0xe39374: r1 = LoadClassIdInstr(r0)
    //     0xe39374: ldur            x1, [x0, #-1]
    //     0xe39378: ubfx            x1, x1, #0xc, #0x14
    // 0xe3937c: mov             x16, x0
    // 0xe39380: mov             x0, x1
    // 0xe39384: mov             x1, x16
    // 0xe39388: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe39388: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe3938c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe3938c: movz            x17, #0xd889
    //     0xe39390: add             lr, x0, x17
    //     0xe39394: ldr             lr, [x21, lr, lsl #3]
    //     0xe39398: blr             lr
    // 0xe3939c: LeaveFrame
    //     0xe3939c: mov             SP, fp
    //     0xe393a0: ldp             fp, lr, [SP], #0x10
    // 0xe393a4: ret
    //     0xe393a4: ret             
    // 0xe393a8: r1 = <TutorialSearch>
    //     0xe393a8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7f8] TypeArguments: <TutorialSearch>
    //     0xe393ac: ldr             x1, [x1, #0x7f8]
    // 0xe393b0: r2 = 0
    //     0xe393b0: movz            x2, #0
    // 0xe393b4: r0 = _GrowableList()
    //     0xe393b4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe393b8: LeaveFrame
    //     0xe393b8: mov             SP, fp
    //     0xe393bc: ldp             fp, lr, [SP], #0x10
    // 0xe393c0: ret
    //     0xe393c0: ret             
    // 0xe393c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe393c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe393c8: b               #0xe39150
  }
  [closure] static TutorialSearch <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe393cc, size: 0x50
    // 0xe393cc: EnterFrame
    //     0xe393cc: stp             fp, lr, [SP, #-0x10]!
    //     0xe393d0: mov             fp, SP
    // 0xe393d4: CheckStackOverflow
    //     0xe393d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe393d8: cmp             SP, x16
    //     0xe393dc: b.ls            #0xe39414
    // 0xe393e0: ldr             x0, [fp, #0x10]
    // 0xe393e4: r2 = Null
    //     0xe393e4: mov             x2, NULL
    // 0xe393e8: r1 = Null
    //     0xe393e8: mov             x1, NULL
    // 0xe393ec: r8 = Map<String, dynamic>
    //     0xe393ec: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe393f0: r3 = Null
    //     0xe393f0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d850] Null
    //     0xe393f4: ldr             x3, [x3, #0x850]
    // 0xe393f8: r0 = Map<String, dynamic>()
    //     0xe393f8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe393fc: ldr             x2, [fp, #0x10]
    // 0xe39400: r1 = Null
    //     0xe39400: mov             x1, NULL
    // 0xe39404: r0 = TutorialSearch.fromMap()
    //     0xe39404: bl              #0xe3941c  ; [package:nuonline/app/data/models/tutorial.dart] TutorialSearch::TutorialSearch.fromMap
    // 0xe39408: LeaveFrame
    //     0xe39408: mov             SP, fp
    //     0xe3940c: ldp             fp, lr, [SP], #0x10
    // 0xe39410: ret
    //     0xe39410: ret             
    // 0xe39414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe39414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe39418: b               #0xe393e0
  }
  factory _ TutorialSearch.fromMap(/* No info */) {
    // ** addr: 0xe3941c, size: 0x174
    // 0xe3941c: EnterFrame
    //     0xe3941c: stp             fp, lr, [SP, #-0x10]!
    //     0xe39420: mov             fp, SP
    // 0xe39424: AllocStack(0x20)
    //     0xe39424: sub             SP, SP, #0x20
    // 0xe39428: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe39428: mov             x3, x2
    //     0xe3942c: stur            x2, [fp, #-8]
    // 0xe39430: CheckStackOverflow
    //     0xe39430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe39434: cmp             SP, x16
    //     0xe39438: b.ls            #0xe39588
    // 0xe3943c: r0 = LoadClassIdInstr(r3)
    //     0xe3943c: ldur            x0, [x3, #-1]
    //     0xe39440: ubfx            x0, x0, #0xc, #0x14
    // 0xe39444: mov             x1, x3
    // 0xe39448: r2 = "id"
    //     0xe39448: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe3944c: ldr             x2, [x2, #0x740]
    // 0xe39450: r0 = GDT[cid_x0 + -0x114]()
    //     0xe39450: sub             lr, x0, #0x114
    //     0xe39454: ldr             lr, [x21, lr, lsl #3]
    //     0xe39458: blr             lr
    // 0xe3945c: mov             x3, x0
    // 0xe39460: r2 = Null
    //     0xe39460: mov             x2, NULL
    // 0xe39464: r1 = Null
    //     0xe39464: mov             x1, NULL
    // 0xe39468: stur            x3, [fp, #-0x10]
    // 0xe3946c: branchIfSmi(r0, 0xe39494)
    //     0xe3946c: tbz             w0, #0, #0xe39494
    // 0xe39470: r4 = LoadClassIdInstr(r0)
    //     0xe39470: ldur            x4, [x0, #-1]
    //     0xe39474: ubfx            x4, x4, #0xc, #0x14
    // 0xe39478: sub             x4, x4, #0x3c
    // 0xe3947c: cmp             x4, #1
    // 0xe39480: b.ls            #0xe39494
    // 0xe39484: r8 = int
    //     0xe39484: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe39488: r3 = Null
    //     0xe39488: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d860] Null
    //     0xe3948c: ldr             x3, [x3, #0x860]
    // 0xe39490: r0 = int()
    //     0xe39490: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe39494: ldur            x3, [fp, #-8]
    // 0xe39498: r0 = LoadClassIdInstr(r3)
    //     0xe39498: ldur            x0, [x3, #-1]
    //     0xe3949c: ubfx            x0, x0, #0xc, #0x14
    // 0xe394a0: mov             x1, x3
    // 0xe394a4: r2 = "name"
    //     0xe394a4: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe394a8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe394a8: sub             lr, x0, #0x114
    //     0xe394ac: ldr             lr, [x21, lr, lsl #3]
    //     0xe394b0: blr             lr
    // 0xe394b4: mov             x3, x0
    // 0xe394b8: r2 = Null
    //     0xe394b8: mov             x2, NULL
    // 0xe394bc: r1 = Null
    //     0xe394bc: mov             x1, NULL
    // 0xe394c0: stur            x3, [fp, #-0x18]
    // 0xe394c4: r4 = 60
    //     0xe394c4: movz            x4, #0x3c
    // 0xe394c8: branchIfSmi(r0, 0xe394d4)
    //     0xe394c8: tbz             w0, #0, #0xe394d4
    // 0xe394cc: r4 = LoadClassIdInstr(r0)
    //     0xe394cc: ldur            x4, [x0, #-1]
    //     0xe394d0: ubfx            x4, x4, #0xc, #0x14
    // 0xe394d4: sub             x4, x4, #0x5e
    // 0xe394d8: cmp             x4, #1
    // 0xe394dc: b.ls            #0xe394f0
    // 0xe394e0: r8 = String
    //     0xe394e0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe394e4: r3 = Null
    //     0xe394e4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d870] Null
    //     0xe394e8: ldr             x3, [x3, #0x870]
    // 0xe394ec: r0 = String()
    //     0xe394ec: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe394f0: ldur            x1, [fp, #-8]
    // 0xe394f4: r0 = LoadClassIdInstr(r1)
    //     0xe394f4: ldur            x0, [x1, #-1]
    //     0xe394f8: ubfx            x0, x0, #0xc, #0x14
    // 0xe394fc: r2 = "category"
    //     0xe394fc: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xe39500: ldr             x2, [x2, #0x960]
    // 0xe39504: r0 = GDT[cid_x0 + -0x114]()
    //     0xe39504: sub             lr, x0, #0x114
    //     0xe39508: ldr             lr, [x21, lr, lsl #3]
    //     0xe3950c: blr             lr
    // 0xe39510: mov             x3, x0
    // 0xe39514: r2 = Null
    //     0xe39514: mov             x2, NULL
    // 0xe39518: r1 = Null
    //     0xe39518: mov             x1, NULL
    // 0xe3951c: stur            x3, [fp, #-8]
    // 0xe39520: r4 = 60
    //     0xe39520: movz            x4, #0x3c
    // 0xe39524: branchIfSmi(r0, 0xe39530)
    //     0xe39524: tbz             w0, #0, #0xe39530
    // 0xe39528: r4 = LoadClassIdInstr(r0)
    //     0xe39528: ldur            x4, [x0, #-1]
    //     0xe3952c: ubfx            x4, x4, #0xc, #0x14
    // 0xe39530: sub             x4, x4, #0x5e
    // 0xe39534: cmp             x4, #1
    // 0xe39538: b.ls            #0xe3954c
    // 0xe3953c: r8 = String
    //     0xe3953c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe39540: r3 = Null
    //     0xe39540: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d880] Null
    //     0xe39544: ldr             x3, [x3, #0x880]
    // 0xe39548: r0 = String()
    //     0xe39548: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe3954c: ldur            x0, [fp, #-0x10]
    // 0xe39550: r1 = LoadInt32Instr(r0)
    //     0xe39550: sbfx            x1, x0, #1, #0x1f
    //     0xe39554: tbz             w0, #0, #0xe3955c
    //     0xe39558: ldur            x1, [x0, #7]
    // 0xe3955c: stur            x1, [fp, #-0x20]
    // 0xe39560: r0 = TutorialSearch()
    //     0xe39560: bl              #0xe39590  ; AllocateTutorialSearchStub -> TutorialSearch (size=0x18)
    // 0xe39564: ldur            x1, [fp, #-0x20]
    // 0xe39568: StoreField: r0->field_7 = r1
    //     0xe39568: stur            x1, [x0, #7]
    // 0xe3956c: ldur            x1, [fp, #-0x18]
    // 0xe39570: StoreField: r0->field_f = r1
    //     0xe39570: stur            w1, [x0, #0xf]
    // 0xe39574: ldur            x1, [fp, #-8]
    // 0xe39578: StoreField: r0->field_13 = r1
    //     0xe39578: stur            w1, [x0, #0x13]
    // 0xe3957c: LeaveFrame
    //     0xe3957c: mov             SP, fp
    //     0xe39580: ldp             fp, lr, [SP], #0x10
    // 0xe39584: ret
    //     0xe39584: ret             
    // 0xe39588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe39588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3958c: b               #0xe3943c
  }
}

// class id: 5567, size: 0x20, field offset: 0x8
//   const constructor, 
class TutorialSubCategory extends Equatable {

  _Mint field_8;
  _OneByteString field_10;
  TutorialCategory field_14;
  _OneByteString field_18;
  _ConstMap<String, dynamic> field_1c;

  static _ fromResponse(/* No info */) {
    // ** addr: 0x924b14, size: 0x188
    // 0x924b14: EnterFrame
    //     0x924b14: stp             fp, lr, [SP, #-0x10]!
    //     0x924b18: mov             fp, SP
    // 0x924b1c: AllocStack(0x20)
    //     0x924b1c: sub             SP, SP, #0x20
    // 0x924b20: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x924b20: mov             x3, x1
    //     0x924b24: stur            x1, [fp, #-8]
    // 0x924b28: CheckStackOverflow
    //     0x924b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924b2c: cmp             SP, x16
    //     0x924b30: b.ls            #0x924c94
    // 0x924b34: mov             x0, x3
    // 0x924b38: r2 = Null
    //     0x924b38: mov             x2, NULL
    // 0x924b3c: r1 = Null
    //     0x924b3c: mov             x1, NULL
    // 0x924b40: cmp             w0, NULL
    // 0x924b44: b.eq            #0x924be8
    // 0x924b48: branchIfSmi(r0, 0x924be8)
    //     0x924b48: tbz             w0, #0, #0x924be8
    // 0x924b4c: r3 = LoadClassIdInstr(r0)
    //     0x924b4c: ldur            x3, [x0, #-1]
    //     0x924b50: ubfx            x3, x3, #0xc, #0x14
    // 0x924b54: r17 = 6718
    //     0x924b54: movz            x17, #0x1a3e
    // 0x924b58: cmp             x3, x17
    // 0x924b5c: b.eq            #0x924bf0
    // 0x924b60: sub             x3, x3, #0x5a
    // 0x924b64: cmp             x3, #2
    // 0x924b68: b.ls            #0x924bf0
    // 0x924b6c: r4 = LoadClassIdInstr(r0)
    //     0x924b6c: ldur            x4, [x0, #-1]
    //     0x924b70: ubfx            x4, x4, #0xc, #0x14
    // 0x924b74: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x924b78: ldr             x3, [x3, #0x18]
    // 0x924b7c: ldr             x3, [x3, x4, lsl #3]
    // 0x924b80: LoadField: r3 = r3->field_2b
    //     0x924b80: ldur            w3, [x3, #0x2b]
    // 0x924b84: DecompressPointer r3
    //     0x924b84: add             x3, x3, HEAP, lsl #32
    // 0x924b88: cmp             w3, NULL
    // 0x924b8c: b.eq            #0x924be8
    // 0x924b90: LoadField: r3 = r3->field_f
    //     0x924b90: ldur            w3, [x3, #0xf]
    // 0x924b94: lsr             x3, x3, #3
    // 0x924b98: r17 = 6718
    //     0x924b98: movz            x17, #0x1a3e
    // 0x924b9c: cmp             x3, x17
    // 0x924ba0: b.eq            #0x924bf0
    // 0x924ba4: r3 = SubtypeTestCache
    //     0x924ba4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32200] SubtypeTestCache
    //     0x924ba8: ldr             x3, [x3, #0x200]
    // 0x924bac: r30 = Subtype1TestCacheStub
    //     0x924bac: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x924bb0: LoadField: r30 = r30->field_7
    //     0x924bb0: ldur            lr, [lr, #7]
    // 0x924bb4: blr             lr
    // 0x924bb8: cmp             w7, NULL
    // 0x924bbc: b.eq            #0x924bc8
    // 0x924bc0: tbnz            w7, #4, #0x924be8
    // 0x924bc4: b               #0x924bf0
    // 0x924bc8: r8 = List
    //     0x924bc8: add             x8, PP, #0x32, lsl #12  ; [pp+0x32208] Type: List
    //     0x924bcc: ldr             x8, [x8, #0x208]
    // 0x924bd0: r3 = SubtypeTestCache
    //     0x924bd0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32210] SubtypeTestCache
    //     0x924bd4: ldr             x3, [x3, #0x210]
    // 0x924bd8: r30 = InstanceOfStub
    //     0x924bd8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x924bdc: LoadField: r30 = r30->field_7
    //     0x924bdc: ldur            lr, [lr, #7]
    // 0x924be0: blr             lr
    // 0x924be4: b               #0x924bf4
    // 0x924be8: r0 = false
    //     0x924be8: add             x0, NULL, #0x30  ; false
    // 0x924bec: b               #0x924bf4
    // 0x924bf0: r0 = true
    //     0x924bf0: add             x0, NULL, #0x20  ; true
    // 0x924bf4: tbnz            w0, #4, #0x924c78
    // 0x924bf8: ldur            x0, [fp, #-8]
    // 0x924bfc: r1 = Function '<anonymous closure>': static.
    //     0x924bfc: add             x1, PP, #0x32, lsl #12  ; [pp+0x32218] AnonymousClosure: static (0x924c9c), in [package:nuonline/app/data/models/tutorial.dart] TutorialSubCategory::fromResponse (0x924b14)
    //     0x924c00: ldr             x1, [x1, #0x218]
    // 0x924c04: r2 = Null
    //     0x924c04: mov             x2, NULL
    // 0x924c08: r0 = AllocateClosure()
    //     0x924c08: bl              #0xec1630  ; AllocateClosureStub
    // 0x924c0c: mov             x1, x0
    // 0x924c10: ldur            x0, [fp, #-8]
    // 0x924c14: r2 = LoadClassIdInstr(r0)
    //     0x924c14: ldur            x2, [x0, #-1]
    //     0x924c18: ubfx            x2, x2, #0xc, #0x14
    // 0x924c1c: r16 = <TutorialSubCategory>
    //     0x924c1c: add             x16, PP, #0x32, lsl #12  ; [pp+0x321d0] TypeArguments: <TutorialSubCategory>
    //     0x924c20: ldr             x16, [x16, #0x1d0]
    // 0x924c24: stp             x0, x16, [SP, #8]
    // 0x924c28: str             x1, [SP]
    // 0x924c2c: mov             x0, x2
    // 0x924c30: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x924c30: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x924c34: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x924c34: movz            x17, #0xf28c
    //     0x924c38: add             lr, x0, x17
    //     0x924c3c: ldr             lr, [x21, lr, lsl #3]
    //     0x924c40: blr             lr
    // 0x924c44: r1 = LoadClassIdInstr(r0)
    //     0x924c44: ldur            x1, [x0, #-1]
    //     0x924c48: ubfx            x1, x1, #0xc, #0x14
    // 0x924c4c: mov             x16, x0
    // 0x924c50: mov             x0, x1
    // 0x924c54: mov             x1, x16
    // 0x924c58: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x924c58: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x924c5c: r0 = GDT[cid_x0 + 0xd889]()
    //     0x924c5c: movz            x17, #0xd889
    //     0x924c60: add             lr, x0, x17
    //     0x924c64: ldr             lr, [x21, lr, lsl #3]
    //     0x924c68: blr             lr
    // 0x924c6c: LeaveFrame
    //     0x924c6c: mov             SP, fp
    //     0x924c70: ldp             fp, lr, [SP], #0x10
    // 0x924c74: ret
    //     0x924c74: ret             
    // 0x924c78: r1 = <TutorialSubCategory>
    //     0x924c78: add             x1, PP, #0x32, lsl #12  ; [pp+0x321d0] TypeArguments: <TutorialSubCategory>
    //     0x924c7c: ldr             x1, [x1, #0x1d0]
    // 0x924c80: r2 = 0
    //     0x924c80: movz            x2, #0
    // 0x924c84: r0 = _GrowableList()
    //     0x924c84: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x924c88: LeaveFrame
    //     0x924c88: mov             SP, fp
    //     0x924c8c: ldp             fp, lr, [SP], #0x10
    // 0x924c90: ret
    //     0x924c90: ret             
    // 0x924c94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924c94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924c98: b               #0x924b34
  }
  [closure] static TutorialSubCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x924c9c, size: 0x50
    // 0x924c9c: EnterFrame
    //     0x924c9c: stp             fp, lr, [SP, #-0x10]!
    //     0x924ca0: mov             fp, SP
    // 0x924ca4: CheckStackOverflow
    //     0x924ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924ca8: cmp             SP, x16
    //     0x924cac: b.ls            #0x924ce4
    // 0x924cb0: ldr             x0, [fp, #0x10]
    // 0x924cb4: r2 = Null
    //     0x924cb4: mov             x2, NULL
    // 0x924cb8: r1 = Null
    //     0x924cb8: mov             x1, NULL
    // 0x924cbc: r8 = Map<String, dynamic>
    //     0x924cbc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x924cc0: r3 = Null
    //     0x924cc0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32220] Null
    //     0x924cc4: ldr             x3, [x3, #0x220]
    // 0x924cc8: r0 = Map<String, dynamic>()
    //     0x924cc8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x924ccc: ldr             x2, [fp, #0x10]
    // 0x924cd0: r1 = Null
    //     0x924cd0: mov             x1, NULL
    // 0x924cd4: r0 = TutorialSubCategory.fromMap()
    //     0x924cd4: bl              #0x924cec  ; [package:nuonline/app/data/models/tutorial.dart] TutorialSubCategory::TutorialSubCategory.fromMap
    // 0x924cd8: LeaveFrame
    //     0x924cd8: mov             SP, fp
    //     0x924cdc: ldp             fp, lr, [SP], #0x10
    // 0x924ce0: ret
    //     0x924ce0: ret             
    // 0x924ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924ce8: b               #0x924cb0
  }
  factory _ TutorialSubCategory.fromMap(/* No info */) {
    // ** addr: 0x924cec, size: 0x16c
    // 0x924cec: EnterFrame
    //     0x924cec: stp             fp, lr, [SP, #-0x10]!
    //     0x924cf0: mov             fp, SP
    // 0x924cf4: AllocStack(0x20)
    //     0x924cf4: sub             SP, SP, #0x20
    // 0x924cf8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x924cf8: mov             x3, x2
    //     0x924cfc: stur            x2, [fp, #-8]
    // 0x924d00: CheckStackOverflow
    //     0x924d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924d04: cmp             SP, x16
    //     0x924d08: b.ls            #0x924e50
    // 0x924d0c: r0 = LoadClassIdInstr(r3)
    //     0x924d0c: ldur            x0, [x3, #-1]
    //     0x924d10: ubfx            x0, x0, #0xc, #0x14
    // 0x924d14: mov             x1, x3
    // 0x924d18: r2 = "id"
    //     0x924d18: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x924d1c: ldr             x2, [x2, #0x740]
    // 0x924d20: r0 = GDT[cid_x0 + -0x114]()
    //     0x924d20: sub             lr, x0, #0x114
    //     0x924d24: ldr             lr, [x21, lr, lsl #3]
    //     0x924d28: blr             lr
    // 0x924d2c: mov             x3, x0
    // 0x924d30: r2 = Null
    //     0x924d30: mov             x2, NULL
    // 0x924d34: r1 = Null
    //     0x924d34: mov             x1, NULL
    // 0x924d38: stur            x3, [fp, #-0x10]
    // 0x924d3c: branchIfSmi(r0, 0x924d64)
    //     0x924d3c: tbz             w0, #0, #0x924d64
    // 0x924d40: r4 = LoadClassIdInstr(r0)
    //     0x924d40: ldur            x4, [x0, #-1]
    //     0x924d44: ubfx            x4, x4, #0xc, #0x14
    // 0x924d48: sub             x4, x4, #0x3c
    // 0x924d4c: cmp             x4, #1
    // 0x924d50: b.ls            #0x924d64
    // 0x924d54: r8 = int
    //     0x924d54: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x924d58: r3 = Null
    //     0x924d58: add             x3, PP, #0x32, lsl #12  ; [pp+0x32230] Null
    //     0x924d5c: ldr             x3, [x3, #0x230]
    // 0x924d60: r0 = int()
    //     0x924d60: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x924d64: ldur            x3, [fp, #-8]
    // 0x924d68: r0 = LoadClassIdInstr(r3)
    //     0x924d68: ldur            x0, [x3, #-1]
    //     0x924d6c: ubfx            x0, x0, #0xc, #0x14
    // 0x924d70: mov             x1, x3
    // 0x924d74: r2 = "name"
    //     0x924d74: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x924d78: r0 = GDT[cid_x0 + -0x114]()
    //     0x924d78: sub             lr, x0, #0x114
    //     0x924d7c: ldr             lr, [x21, lr, lsl #3]
    //     0x924d80: blr             lr
    // 0x924d84: mov             x3, x0
    // 0x924d88: r2 = Null
    //     0x924d88: mov             x2, NULL
    // 0x924d8c: r1 = Null
    //     0x924d8c: mov             x1, NULL
    // 0x924d90: stur            x3, [fp, #-0x18]
    // 0x924d94: r4 = 60
    //     0x924d94: movz            x4, #0x3c
    // 0x924d98: branchIfSmi(r0, 0x924da4)
    //     0x924d98: tbz             w0, #0, #0x924da4
    // 0x924d9c: r4 = LoadClassIdInstr(r0)
    //     0x924d9c: ldur            x4, [x0, #-1]
    //     0x924da0: ubfx            x4, x4, #0xc, #0x14
    // 0x924da4: sub             x4, x4, #0x5e
    // 0x924da8: cmp             x4, #1
    // 0x924dac: b.ls            #0x924dc0
    // 0x924db0: r8 = String
    //     0x924db0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x924db4: r3 = Null
    //     0x924db4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32240] Null
    //     0x924db8: ldr             x3, [x3, #0x240]
    // 0x924dbc: r0 = String()
    //     0x924dbc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x924dc0: ldur            x1, [fp, #-8]
    // 0x924dc4: r0 = LoadClassIdInstr(r1)
    //     0x924dc4: ldur            x0, [x1, #-1]
    //     0x924dc8: ubfx            x0, x0, #0xc, #0x14
    // 0x924dcc: r2 = "category"
    //     0x924dcc: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x924dd0: ldr             x2, [x2, #0x960]
    // 0x924dd4: r0 = GDT[cid_x0 + -0x114]()
    //     0x924dd4: sub             lr, x0, #0x114
    //     0x924dd8: ldr             lr, [x21, lr, lsl #3]
    //     0x924ddc: blr             lr
    // 0x924de0: mov             x3, x0
    // 0x924de4: r2 = Null
    //     0x924de4: mov             x2, NULL
    // 0x924de8: r1 = Null
    //     0x924de8: mov             x1, NULL
    // 0x924dec: stur            x3, [fp, #-8]
    // 0x924df0: r8 = Map<String, dynamic>
    //     0x924df0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x924df4: r3 = Null
    //     0x924df4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32250] Null
    //     0x924df8: ldr             x3, [x3, #0x250]
    // 0x924dfc: r0 = Map<String, dynamic>()
    //     0x924dfc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x924e00: ldur            x2, [fp, #-8]
    // 0x924e04: r1 = Null
    //     0x924e04: mov             x1, NULL
    // 0x924e08: r0 = TutorialCategory.fromMap()
    //     0x924e08: bl              #0x9244d0  ; [package:nuonline/app/data/models/tutorial.dart] TutorialCategory::TutorialCategory.fromMap
    // 0x924e0c: mov             x1, x0
    // 0x924e10: ldur            x0, [fp, #-0x10]
    // 0x924e14: stur            x1, [fp, #-8]
    // 0x924e18: r2 = LoadInt32Instr(r0)
    //     0x924e18: sbfx            x2, x0, #1, #0x1f
    //     0x924e1c: tbz             w0, #0, #0x924e24
    //     0x924e20: ldur            x2, [x0, #7]
    // 0x924e24: stur            x2, [fp, #-0x20]
    // 0x924e28: r0 = TutorialSubCategory()
    //     0x924e28: bl              #0x924e58  ; AllocateTutorialSubCategoryStub -> TutorialSubCategory (size=0x20)
    // 0x924e2c: ldur            x1, [fp, #-0x20]
    // 0x924e30: StoreField: r0->field_7 = r1
    //     0x924e30: stur            x1, [x0, #7]
    // 0x924e34: ldur            x1, [fp, #-0x18]
    // 0x924e38: StoreField: r0->field_f = r1
    //     0x924e38: stur            w1, [x0, #0xf]
    // 0x924e3c: ldur            x1, [fp, #-8]
    // 0x924e40: StoreField: r0->field_13 = r1
    //     0x924e40: stur            w1, [x0, #0x13]
    // 0x924e44: LeaveFrame
    //     0x924e44: mov             SP, fp
    //     0x924e48: ldp             fp, lr, [SP], #0x10
    // 0x924e4c: ret
    //     0x924e4c: ret             
    // 0x924e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924e50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924e54: b               #0x924d0c
  }
}

// class id: 5568, size: 0x1c, field offset: 0x8
//   const constructor, 
class TutorialCategory extends Equatable {

  _Mint field_8;
  _OneByteString field_10;
  _Mint field_14;

  static _ fromResponse(/* No info */) {
    // ** addr: 0x9242f8, size: 0x188
    // 0x9242f8: EnterFrame
    //     0x9242f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9242fc: mov             fp, SP
    // 0x924300: AllocStack(0x20)
    //     0x924300: sub             SP, SP, #0x20
    // 0x924304: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x924304: mov             x3, x1
    //     0x924308: stur            x1, [fp, #-8]
    // 0x92430c: CheckStackOverflow
    //     0x92430c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924310: cmp             SP, x16
    //     0x924314: b.ls            #0x924478
    // 0x924318: mov             x0, x3
    // 0x92431c: r2 = Null
    //     0x92431c: mov             x2, NULL
    // 0x924320: r1 = Null
    //     0x924320: mov             x1, NULL
    // 0x924324: cmp             w0, NULL
    // 0x924328: b.eq            #0x9243cc
    // 0x92432c: branchIfSmi(r0, 0x9243cc)
    //     0x92432c: tbz             w0, #0, #0x9243cc
    // 0x924330: r3 = LoadClassIdInstr(r0)
    //     0x924330: ldur            x3, [x0, #-1]
    //     0x924334: ubfx            x3, x3, #0xc, #0x14
    // 0x924338: r17 = 6718
    //     0x924338: movz            x17, #0x1a3e
    // 0x92433c: cmp             x3, x17
    // 0x924340: b.eq            #0x9243d4
    // 0x924344: sub             x3, x3, #0x5a
    // 0x924348: cmp             x3, #2
    // 0x92434c: b.ls            #0x9243d4
    // 0x924350: r4 = LoadClassIdInstr(r0)
    //     0x924350: ldur            x4, [x0, #-1]
    //     0x924354: ubfx            x4, x4, #0xc, #0x14
    // 0x924358: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x92435c: ldr             x3, [x3, #0x18]
    // 0x924360: ldr             x3, [x3, x4, lsl #3]
    // 0x924364: LoadField: r3 = r3->field_2b
    //     0x924364: ldur            w3, [x3, #0x2b]
    // 0x924368: DecompressPointer r3
    //     0x924368: add             x3, x3, HEAP, lsl #32
    // 0x92436c: cmp             w3, NULL
    // 0x924370: b.eq            #0x9243cc
    // 0x924374: LoadField: r3 = r3->field_f
    //     0x924374: ldur            w3, [x3, #0xf]
    // 0x924378: lsr             x3, x3, #3
    // 0x92437c: r17 = 6718
    //     0x92437c: movz            x17, #0x1a3e
    // 0x924380: cmp             x3, x17
    // 0x924384: b.eq            #0x9243d4
    // 0x924388: r3 = SubtypeTestCache
    //     0x924388: add             x3, PP, #0x29, lsl #12  ; [pp+0x29770] SubtypeTestCache
    //     0x92438c: ldr             x3, [x3, #0x770]
    // 0x924390: r30 = Subtype1TestCacheStub
    //     0x924390: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x924394: LoadField: r30 = r30->field_7
    //     0x924394: ldur            lr, [lr, #7]
    // 0x924398: blr             lr
    // 0x92439c: cmp             w7, NULL
    // 0x9243a0: b.eq            #0x9243ac
    // 0x9243a4: tbnz            w7, #4, #0x9243cc
    // 0x9243a8: b               #0x9243d4
    // 0x9243ac: r8 = List
    //     0x9243ac: add             x8, PP, #0x29, lsl #12  ; [pp+0x29778] Type: List
    //     0x9243b0: ldr             x8, [x8, #0x778]
    // 0x9243b4: r3 = SubtypeTestCache
    //     0x9243b4: add             x3, PP, #0x29, lsl #12  ; [pp+0x29780] SubtypeTestCache
    //     0x9243b8: ldr             x3, [x3, #0x780]
    // 0x9243bc: r30 = InstanceOfStub
    //     0x9243bc: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x9243c0: LoadField: r30 = r30->field_7
    //     0x9243c0: ldur            lr, [lr, #7]
    // 0x9243c4: blr             lr
    // 0x9243c8: b               #0x9243d8
    // 0x9243cc: r0 = false
    //     0x9243cc: add             x0, NULL, #0x30  ; false
    // 0x9243d0: b               #0x9243d8
    // 0x9243d4: r0 = true
    //     0x9243d4: add             x0, NULL, #0x20  ; true
    // 0x9243d8: tbnz            w0, #4, #0x92445c
    // 0x9243dc: ldur            x0, [fp, #-8]
    // 0x9243e0: r1 = Function '<anonymous closure>': static.
    //     0x9243e0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29788] AnonymousClosure: static (0x924480), in [package:nuonline/app/data/models/tutorial.dart] TutorialCategory::fromResponse (0x9242f8)
    //     0x9243e4: ldr             x1, [x1, #0x788]
    // 0x9243e8: r2 = Null
    //     0x9243e8: mov             x2, NULL
    // 0x9243ec: r0 = AllocateClosure()
    //     0x9243ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x9243f0: mov             x1, x0
    // 0x9243f4: ldur            x0, [fp, #-8]
    // 0x9243f8: r2 = LoadClassIdInstr(r0)
    //     0x9243f8: ldur            x2, [x0, #-1]
    //     0x9243fc: ubfx            x2, x2, #0xc, #0x14
    // 0x924400: r16 = <TutorialCategory>
    //     0x924400: add             x16, PP, #0x29, lsl #12  ; [pp+0x29790] TypeArguments: <TutorialCategory>
    //     0x924404: ldr             x16, [x16, #0x790]
    // 0x924408: stp             x0, x16, [SP, #8]
    // 0x92440c: str             x1, [SP]
    // 0x924410: mov             x0, x2
    // 0x924414: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x924414: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x924418: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x924418: movz            x17, #0xf28c
    //     0x92441c: add             lr, x0, x17
    //     0x924420: ldr             lr, [x21, lr, lsl #3]
    //     0x924424: blr             lr
    // 0x924428: r1 = LoadClassIdInstr(r0)
    //     0x924428: ldur            x1, [x0, #-1]
    //     0x92442c: ubfx            x1, x1, #0xc, #0x14
    // 0x924430: mov             x16, x0
    // 0x924434: mov             x0, x1
    // 0x924438: mov             x1, x16
    // 0x92443c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92443c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x924440: r0 = GDT[cid_x0 + 0xd889]()
    //     0x924440: movz            x17, #0xd889
    //     0x924444: add             lr, x0, x17
    //     0x924448: ldr             lr, [x21, lr, lsl #3]
    //     0x92444c: blr             lr
    // 0x924450: LeaveFrame
    //     0x924450: mov             SP, fp
    //     0x924454: ldp             fp, lr, [SP], #0x10
    // 0x924458: ret
    //     0x924458: ret             
    // 0x92445c: r1 = <TutorialCategory>
    //     0x92445c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29790] TypeArguments: <TutorialCategory>
    //     0x924460: ldr             x1, [x1, #0x790]
    // 0x924464: r2 = 0
    //     0x924464: movz            x2, #0
    // 0x924468: r0 = _GrowableList()
    //     0x924468: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x92446c: LeaveFrame
    //     0x92446c: mov             SP, fp
    //     0x924470: ldp             fp, lr, [SP], #0x10
    // 0x924474: ret
    //     0x924474: ret             
    // 0x924478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92447c: b               #0x924318
  }
  [closure] static TutorialCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x924480, size: 0x50
    // 0x924480: EnterFrame
    //     0x924480: stp             fp, lr, [SP, #-0x10]!
    //     0x924484: mov             fp, SP
    // 0x924488: CheckStackOverflow
    //     0x924488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92448c: cmp             SP, x16
    //     0x924490: b.ls            #0x9244c8
    // 0x924494: ldr             x0, [fp, #0x10]
    // 0x924498: r2 = Null
    //     0x924498: mov             x2, NULL
    // 0x92449c: r1 = Null
    //     0x92449c: mov             x1, NULL
    // 0x9244a0: r8 = Map<String, dynamic>
    //     0x9244a0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x9244a4: r3 = Null
    //     0x9244a4: add             x3, PP, #0x29, lsl #12  ; [pp+0x29798] Null
    //     0x9244a8: ldr             x3, [x3, #0x798]
    // 0x9244ac: r0 = Map<String, dynamic>()
    //     0x9244ac: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9244b0: ldr             x2, [fp, #0x10]
    // 0x9244b4: r1 = Null
    //     0x9244b4: mov             x1, NULL
    // 0x9244b8: r0 = TutorialCategory.fromMap()
    //     0x9244b8: bl              #0x9244d0  ; [package:nuonline/app/data/models/tutorial.dart] TutorialCategory::TutorialCategory.fromMap
    // 0x9244bc: LeaveFrame
    //     0x9244bc: mov             SP, fp
    //     0x9244c0: ldp             fp, lr, [SP], #0x10
    // 0x9244c4: ret
    //     0x9244c4: ret             
    // 0x9244c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9244c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9244cc: b               #0x924494
  }
  factory _ TutorialCategory.fromMap(/* No info */) {
    // ** addr: 0x9244d0, size: 0x19c
    // 0x9244d0: EnterFrame
    //     0x9244d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9244d4: mov             fp, SP
    // 0x9244d8: AllocStack(0x28)
    //     0x9244d8: sub             SP, SP, #0x28
    // 0x9244dc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x9244dc: mov             x3, x2
    //     0x9244e0: stur            x2, [fp, #-8]
    // 0x9244e4: CheckStackOverflow
    //     0x9244e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9244e8: cmp             SP, x16
    //     0x9244ec: b.ls            #0x924664
    // 0x9244f0: r0 = LoadClassIdInstr(r3)
    //     0x9244f0: ldur            x0, [x3, #-1]
    //     0x9244f4: ubfx            x0, x0, #0xc, #0x14
    // 0x9244f8: mov             x1, x3
    // 0x9244fc: r2 = "id"
    //     0x9244fc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x924500: ldr             x2, [x2, #0x740]
    // 0x924504: r0 = GDT[cid_x0 + -0x114]()
    //     0x924504: sub             lr, x0, #0x114
    //     0x924508: ldr             lr, [x21, lr, lsl #3]
    //     0x92450c: blr             lr
    // 0x924510: mov             x3, x0
    // 0x924514: r2 = Null
    //     0x924514: mov             x2, NULL
    // 0x924518: r1 = Null
    //     0x924518: mov             x1, NULL
    // 0x92451c: stur            x3, [fp, #-0x10]
    // 0x924520: branchIfSmi(r0, 0x924548)
    //     0x924520: tbz             w0, #0, #0x924548
    // 0x924524: r4 = LoadClassIdInstr(r0)
    //     0x924524: ldur            x4, [x0, #-1]
    //     0x924528: ubfx            x4, x4, #0xc, #0x14
    // 0x92452c: sub             x4, x4, #0x3c
    // 0x924530: cmp             x4, #1
    // 0x924534: b.ls            #0x924548
    // 0x924538: r8 = int
    //     0x924538: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x92453c: r3 = Null
    //     0x92453c: add             x3, PP, #0x29, lsl #12  ; [pp+0x297a8] Null
    //     0x924540: ldr             x3, [x3, #0x7a8]
    // 0x924544: r0 = int()
    //     0x924544: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x924548: ldur            x3, [fp, #-8]
    // 0x92454c: r0 = LoadClassIdInstr(r3)
    //     0x92454c: ldur            x0, [x3, #-1]
    //     0x924550: ubfx            x0, x0, #0xc, #0x14
    // 0x924554: mov             x1, x3
    // 0x924558: r2 = "name"
    //     0x924558: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x92455c: r0 = GDT[cid_x0 + -0x114]()
    //     0x92455c: sub             lr, x0, #0x114
    //     0x924560: ldr             lr, [x21, lr, lsl #3]
    //     0x924564: blr             lr
    // 0x924568: mov             x3, x0
    // 0x92456c: r2 = Null
    //     0x92456c: mov             x2, NULL
    // 0x924570: r1 = Null
    //     0x924570: mov             x1, NULL
    // 0x924574: stur            x3, [fp, #-0x18]
    // 0x924578: r4 = 60
    //     0x924578: movz            x4, #0x3c
    // 0x92457c: branchIfSmi(r0, 0x924588)
    //     0x92457c: tbz             w0, #0, #0x924588
    // 0x924580: r4 = LoadClassIdInstr(r0)
    //     0x924580: ldur            x4, [x0, #-1]
    //     0x924584: ubfx            x4, x4, #0xc, #0x14
    // 0x924588: sub             x4, x4, #0x5e
    // 0x92458c: cmp             x4, #1
    // 0x924590: b.ls            #0x9245a4
    // 0x924594: r8 = String
    //     0x924594: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x924598: r3 = Null
    //     0x924598: add             x3, PP, #0x29, lsl #12  ; [pp+0x297b8] Null
    //     0x92459c: ldr             x3, [x3, #0x7b8]
    // 0x9245a0: r0 = String()
    //     0x9245a0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9245a4: ldur            x1, [fp, #-8]
    // 0x9245a8: r0 = LoadClassIdInstr(r1)
    //     0x9245a8: ldur            x0, [x1, #-1]
    //     0x9245ac: ubfx            x0, x0, #0xc, #0x14
    // 0x9245b0: r2 = "total"
    //     0x9245b0: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dc0] "total"
    //     0x9245b4: ldr             x2, [x2, #0xdc0]
    // 0x9245b8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9245b8: sub             lr, x0, #0x114
    //     0x9245bc: ldr             lr, [x21, lr, lsl #3]
    //     0x9245c0: blr             lr
    // 0x9245c4: mov             x3, x0
    // 0x9245c8: r2 = Null
    //     0x9245c8: mov             x2, NULL
    // 0x9245cc: r1 = Null
    //     0x9245cc: mov             x1, NULL
    // 0x9245d0: stur            x3, [fp, #-8]
    // 0x9245d4: branchIfSmi(r0, 0x9245fc)
    //     0x9245d4: tbz             w0, #0, #0x9245fc
    // 0x9245d8: r4 = LoadClassIdInstr(r0)
    //     0x9245d8: ldur            x4, [x0, #-1]
    //     0x9245dc: ubfx            x4, x4, #0xc, #0x14
    // 0x9245e0: sub             x4, x4, #0x3c
    // 0x9245e4: cmp             x4, #1
    // 0x9245e8: b.ls            #0x9245fc
    // 0x9245ec: r8 = int?
    //     0x9245ec: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x9245f0: r3 = Null
    //     0x9245f0: add             x3, PP, #0x29, lsl #12  ; [pp+0x297c8] Null
    //     0x9245f4: ldr             x3, [x3, #0x7c8]
    // 0x9245f8: r0 = int?()
    //     0x9245f8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x9245fc: ldur            x0, [fp, #-8]
    // 0x924600: cmp             w0, NULL
    // 0x924604: b.ne            #0x924610
    // 0x924608: r2 = 0
    //     0x924608: movz            x2, #0
    // 0x92460c: b               #0x924620
    // 0x924610: r1 = LoadInt32Instr(r0)
    //     0x924610: sbfx            x1, x0, #1, #0x1f
    //     0x924614: tbz             w0, #0, #0x92461c
    //     0x924618: ldur            x1, [x0, #7]
    // 0x92461c: mov             x2, x1
    // 0x924620: ldur            x1, [fp, #-0x10]
    // 0x924624: ldur            x0, [fp, #-0x18]
    // 0x924628: stur            x2, [fp, #-0x28]
    // 0x92462c: r3 = LoadInt32Instr(r1)
    //     0x92462c: sbfx            x3, x1, #1, #0x1f
    //     0x924630: tbz             w1, #0, #0x924638
    //     0x924634: ldur            x3, [x1, #7]
    // 0x924638: stur            x3, [fp, #-0x20]
    // 0x92463c: r0 = TutorialCategory()
    //     0x92463c: bl              #0x92466c  ; AllocateTutorialCategoryStub -> TutorialCategory (size=0x1c)
    // 0x924640: ldur            x1, [fp, #-0x20]
    // 0x924644: StoreField: r0->field_7 = r1
    //     0x924644: stur            x1, [x0, #7]
    // 0x924648: ldur            x1, [fp, #-0x18]
    // 0x92464c: StoreField: r0->field_f = r1
    //     0x92464c: stur            w1, [x0, #0xf]
    // 0x924650: ldur            x1, [fp, #-0x28]
    // 0x924654: StoreField: r0->field_13 = r1
    //     0x924654: stur            x1, [x0, #0x13]
    // 0x924658: LeaveFrame
    //     0x924658: mov             SP, fp
    //     0x92465c: ldp             fp, lr, [SP], #0x10
    // 0x924660: ret
    //     0x924660: ret             
    // 0x924664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924664: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924668: b               #0x9244f0
  }
}

// class id: 5569, size: 0x24, field offset: 0x8
//   const constructor, 
class Tutorial extends Equatable {

  factory _ Tutorial.fromMap(/* No info */) {
    // ** addr: 0x91d07c, size: 0x26c
    // 0x91d07c: EnterFrame
    //     0x91d07c: stp             fp, lr, [SP, #-0x10]!
    //     0x91d080: mov             fp, SP
    // 0x91d084: AllocStack(0x40)
    //     0x91d084: sub             SP, SP, #0x40
    // 0x91d088: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x91d088: mov             x3, x2
    //     0x91d08c: stur            x2, [fp, #-8]
    // 0x91d090: CheckStackOverflow
    //     0x91d090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d094: cmp             SP, x16
    //     0x91d098: b.ls            #0x91d2e0
    // 0x91d09c: r0 = LoadClassIdInstr(r3)
    //     0x91d09c: ldur            x0, [x3, #-1]
    //     0x91d0a0: ubfx            x0, x0, #0xc, #0x14
    // 0x91d0a4: mov             x1, x3
    // 0x91d0a8: r2 = "id"
    //     0x91d0a8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x91d0ac: ldr             x2, [x2, #0x740]
    // 0x91d0b0: r0 = GDT[cid_x0 + -0x114]()
    //     0x91d0b0: sub             lr, x0, #0x114
    //     0x91d0b4: ldr             lr, [x21, lr, lsl #3]
    //     0x91d0b8: blr             lr
    // 0x91d0bc: mov             x3, x0
    // 0x91d0c0: r2 = Null
    //     0x91d0c0: mov             x2, NULL
    // 0x91d0c4: r1 = Null
    //     0x91d0c4: mov             x1, NULL
    // 0x91d0c8: stur            x3, [fp, #-0x10]
    // 0x91d0cc: branchIfSmi(r0, 0x91d0f4)
    //     0x91d0cc: tbz             w0, #0, #0x91d0f4
    // 0x91d0d0: r4 = LoadClassIdInstr(r0)
    //     0x91d0d0: ldur            x4, [x0, #-1]
    //     0x91d0d4: ubfx            x4, x4, #0xc, #0x14
    // 0x91d0d8: sub             x4, x4, #0x3c
    // 0x91d0dc: cmp             x4, #1
    // 0x91d0e0: b.ls            #0x91d0f4
    // 0x91d0e4: r8 = int
    //     0x91d0e4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x91d0e8: r3 = Null
    //     0x91d0e8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3ca80] Null
    //     0x91d0ec: ldr             x3, [x3, #0xa80]
    // 0x91d0f0: r0 = int()
    //     0x91d0f0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91d0f4: ldur            x3, [fp, #-8]
    // 0x91d0f8: r0 = LoadClassIdInstr(r3)
    //     0x91d0f8: ldur            x0, [x3, #-1]
    //     0x91d0fc: ubfx            x0, x0, #0xc, #0x14
    // 0x91d100: mov             x1, x3
    // 0x91d104: r2 = "name"
    //     0x91d104: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x91d108: r0 = GDT[cid_x0 + -0x114]()
    //     0x91d108: sub             lr, x0, #0x114
    //     0x91d10c: ldr             lr, [x21, lr, lsl #3]
    //     0x91d110: blr             lr
    // 0x91d114: mov             x3, x0
    // 0x91d118: r2 = Null
    //     0x91d118: mov             x2, NULL
    // 0x91d11c: r1 = Null
    //     0x91d11c: mov             x1, NULL
    // 0x91d120: stur            x3, [fp, #-0x18]
    // 0x91d124: r4 = 60
    //     0x91d124: movz            x4, #0x3c
    // 0x91d128: branchIfSmi(r0, 0x91d134)
    //     0x91d128: tbz             w0, #0, #0x91d134
    // 0x91d12c: r4 = LoadClassIdInstr(r0)
    //     0x91d12c: ldur            x4, [x0, #-1]
    //     0x91d130: ubfx            x4, x4, #0xc, #0x14
    // 0x91d134: sub             x4, x4, #0x5e
    // 0x91d138: cmp             x4, #1
    // 0x91d13c: b.ls            #0x91d150
    // 0x91d140: r8 = String
    //     0x91d140: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91d144: r3 = Null
    //     0x91d144: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3ca90] Null
    //     0x91d148: ldr             x3, [x3, #0xa90]
    // 0x91d14c: r0 = String()
    //     0x91d14c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91d150: ldur            x3, [fp, #-8]
    // 0x91d154: r0 = LoadClassIdInstr(r3)
    //     0x91d154: ldur            x0, [x3, #-1]
    //     0x91d158: ubfx            x0, x0, #0xc, #0x14
    // 0x91d15c: mov             x1, x3
    // 0x91d160: r2 = "category"
    //     0x91d160: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x91d164: ldr             x2, [x2, #0x960]
    // 0x91d168: r0 = GDT[cid_x0 + -0x114]()
    //     0x91d168: sub             lr, x0, #0x114
    //     0x91d16c: ldr             lr, [x21, lr, lsl #3]
    //     0x91d170: blr             lr
    // 0x91d174: r16 = "name"
    //     0x91d174: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x91d178: stp             x16, x0, [SP]
    // 0x91d17c: r4 = 0
    //     0x91d17c: movz            x4, #0
    // 0x91d180: ldr             x0, [SP, #8]
    // 0x91d184: r16 = UnlinkedCall_0x5f3c08
    //     0x91d184: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3caa0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x91d188: add             x16, x16, #0xaa0
    // 0x91d18c: ldp             x5, lr, [x16]
    // 0x91d190: blr             lr
    // 0x91d194: mov             x3, x0
    // 0x91d198: r2 = Null
    //     0x91d198: mov             x2, NULL
    // 0x91d19c: r1 = Null
    //     0x91d19c: mov             x1, NULL
    // 0x91d1a0: stur            x3, [fp, #-0x20]
    // 0x91d1a4: r4 = 60
    //     0x91d1a4: movz            x4, #0x3c
    // 0x91d1a8: branchIfSmi(r0, 0x91d1b4)
    //     0x91d1a8: tbz             w0, #0, #0x91d1b4
    // 0x91d1ac: r4 = LoadClassIdInstr(r0)
    //     0x91d1ac: ldur            x4, [x0, #-1]
    //     0x91d1b0: ubfx            x4, x4, #0xc, #0x14
    // 0x91d1b4: sub             x4, x4, #0x5e
    // 0x91d1b8: cmp             x4, #1
    // 0x91d1bc: b.ls            #0x91d1d0
    // 0x91d1c0: r8 = String
    //     0x91d1c0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91d1c4: r3 = Null
    //     0x91d1c4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cab0] Null
    //     0x91d1c8: ldr             x3, [x3, #0xab0]
    // 0x91d1cc: r0 = String()
    //     0x91d1cc: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91d1d0: ldur            x3, [fp, #-8]
    // 0x91d1d4: r0 = LoadClassIdInstr(r3)
    //     0x91d1d4: ldur            x0, [x3, #-1]
    //     0x91d1d8: ubfx            x0, x0, #0xc, #0x14
    // 0x91d1dc: mov             x1, x3
    // 0x91d1e0: r2 = "article_id"
    //     0x91d1e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0x91d1e4: ldr             x2, [x2, #0x958]
    // 0x91d1e8: r0 = GDT[cid_x0 + -0x114]()
    //     0x91d1e8: sub             lr, x0, #0x114
    //     0x91d1ec: ldr             lr, [x21, lr, lsl #3]
    //     0x91d1f0: blr             lr
    // 0x91d1f4: mov             x3, x0
    // 0x91d1f8: r2 = Null
    //     0x91d1f8: mov             x2, NULL
    // 0x91d1fc: r1 = Null
    //     0x91d1fc: mov             x1, NULL
    // 0x91d200: stur            x3, [fp, #-0x28]
    // 0x91d204: branchIfSmi(r0, 0x91d22c)
    //     0x91d204: tbz             w0, #0, #0x91d22c
    // 0x91d208: r4 = LoadClassIdInstr(r0)
    //     0x91d208: ldur            x4, [x0, #-1]
    //     0x91d20c: ubfx            x4, x4, #0xc, #0x14
    // 0x91d210: sub             x4, x4, #0x3c
    // 0x91d214: cmp             x4, #1
    // 0x91d218: b.ls            #0x91d22c
    // 0x91d21c: r8 = int
    //     0x91d21c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x91d220: r3 = Null
    //     0x91d220: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cac0] Null
    //     0x91d224: ldr             x3, [x3, #0xac0]
    // 0x91d228: r0 = int()
    //     0x91d228: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x91d22c: ldur            x1, [fp, #-8]
    // 0x91d230: r0 = LoadClassIdInstr(r1)
    //     0x91d230: ldur            x0, [x1, #-1]
    //     0x91d234: ubfx            x0, x0, #0xc, #0x14
    // 0x91d238: r2 = "youtube_id"
    //     0x91d238: add             x2, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x91d23c: ldr             x2, [x2, #0x3f0]
    // 0x91d240: r0 = GDT[cid_x0 + -0x114]()
    //     0x91d240: sub             lr, x0, #0x114
    //     0x91d244: ldr             lr, [x21, lr, lsl #3]
    //     0x91d248: blr             lr
    // 0x91d24c: mov             x3, x0
    // 0x91d250: r2 = Null
    //     0x91d250: mov             x2, NULL
    // 0x91d254: r1 = Null
    //     0x91d254: mov             x1, NULL
    // 0x91d258: stur            x3, [fp, #-8]
    // 0x91d25c: r4 = 60
    //     0x91d25c: movz            x4, #0x3c
    // 0x91d260: branchIfSmi(r0, 0x91d26c)
    //     0x91d260: tbz             w0, #0, #0x91d26c
    // 0x91d264: r4 = LoadClassIdInstr(r0)
    //     0x91d264: ldur            x4, [x0, #-1]
    //     0x91d268: ubfx            x4, x4, #0xc, #0x14
    // 0x91d26c: sub             x4, x4, #0x5e
    // 0x91d270: cmp             x4, #1
    // 0x91d274: b.ls            #0x91d288
    // 0x91d278: r8 = String?
    //     0x91d278: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x91d27c: r3 = Null
    //     0x91d27c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cad0] Null
    //     0x91d280: ldr             x3, [x3, #0xad0]
    // 0x91d284: r0 = String?()
    //     0x91d284: bl              #0x600324  ; IsType_String?_Stub
    // 0x91d288: ldur            x0, [fp, #-0x10]
    // 0x91d28c: r1 = LoadInt32Instr(r0)
    //     0x91d28c: sbfx            x1, x0, #1, #0x1f
    //     0x91d290: tbz             w0, #0, #0x91d298
    //     0x91d294: ldur            x1, [x0, #7]
    // 0x91d298: stur            x1, [fp, #-0x30]
    // 0x91d29c: r0 = Tutorial()
    //     0x91d29c: bl              #0x91d2e8  ; AllocateTutorialStub -> Tutorial (size=0x24)
    // 0x91d2a0: ldur            x1, [fp, #-0x30]
    // 0x91d2a4: StoreField: r0->field_7 = r1
    //     0x91d2a4: stur            x1, [x0, #7]
    // 0x91d2a8: ldur            x1, [fp, #-0x18]
    // 0x91d2ac: StoreField: r0->field_f = r1
    //     0x91d2ac: stur            w1, [x0, #0xf]
    // 0x91d2b0: ldur            x1, [fp, #-0x20]
    // 0x91d2b4: StoreField: r0->field_13 = r1
    //     0x91d2b4: stur            w1, [x0, #0x13]
    // 0x91d2b8: ldur            x1, [fp, #-0x28]
    // 0x91d2bc: r2 = LoadInt32Instr(r1)
    //     0x91d2bc: sbfx            x2, x1, #1, #0x1f
    //     0x91d2c0: tbz             w1, #0, #0x91d2c8
    //     0x91d2c4: ldur            x2, [x1, #7]
    // 0x91d2c8: ArrayStore: r0[0] = r2  ; List_8
    //     0x91d2c8: stur            x2, [x0, #0x17]
    // 0x91d2cc: ldur            x1, [fp, #-8]
    // 0x91d2d0: StoreField: r0->field_1f = r1
    //     0x91d2d0: stur            w1, [x0, #0x1f]
    // 0x91d2d4: LeaveFrame
    //     0x91d2d4: mov             SP, fp
    //     0x91d2d8: ldp             fp, lr, [SP], #0x10
    // 0x91d2dc: ret
    //     0x91d2dc: ret             
    // 0x91d2e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d2e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d2e4: b               #0x91d09c
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc1c4, size: 0x80
    // 0xbdc1c4: EnterFrame
    //     0xbdc1c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc1c8: mov             fp, SP
    // 0xbdc1cc: AllocStack(0x18)
    //     0xbdc1cc: sub             SP, SP, #0x18
    // 0xbdc1d0: r3 = 4
    //     0xbdc1d0: movz            x3, #0x4
    // 0xbdc1d4: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xbdc1d4: ldur            x2, [x1, #0x17]
    // 0xbdc1d8: LoadField: r4 = r1->field_1f
    //     0xbdc1d8: ldur            w4, [x1, #0x1f]
    // 0xbdc1dc: DecompressPointer r4
    //     0xbdc1dc: add             x4, x4, HEAP, lsl #32
    // 0xbdc1e0: stur            x4, [fp, #-0x10]
    // 0xbdc1e4: r0 = BoxInt64Instr(r2)
    //     0xbdc1e4: sbfiz           x0, x2, #1, #0x1f
    //     0xbdc1e8: cmp             x2, x0, asr #1
    //     0xbdc1ec: b.eq            #0xbdc1f8
    //     0xbdc1f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc1f4: stur            x2, [x0, #7]
    // 0xbdc1f8: mov             x2, x3
    // 0xbdc1fc: r1 = Null
    //     0xbdc1fc: mov             x1, NULL
    // 0xbdc200: stur            x0, [fp, #-8]
    // 0xbdc204: r0 = AllocateArray()
    //     0xbdc204: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc208: mov             x2, x0
    // 0xbdc20c: ldur            x0, [fp, #-8]
    // 0xbdc210: stur            x2, [fp, #-0x18]
    // 0xbdc214: StoreField: r2->field_f = r0
    //     0xbdc214: stur            w0, [x2, #0xf]
    // 0xbdc218: ldur            x0, [fp, #-0x10]
    // 0xbdc21c: StoreField: r2->field_13 = r0
    //     0xbdc21c: stur            w0, [x2, #0x13]
    // 0xbdc220: r1 = <Object?>
    //     0xbdc220: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc224: r0 = AllocateGrowableArray()
    //     0xbdc224: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc228: ldur            x1, [fp, #-0x18]
    // 0xbdc22c: StoreField: r0->field_f = r1
    //     0xbdc22c: stur            w1, [x0, #0xf]
    // 0xbdc230: r1 = 4
    //     0xbdc230: movz            x1, #0x4
    // 0xbdc234: StoreField: r0->field_b = r1
    //     0xbdc234: stur            w1, [x0, #0xb]
    // 0xbdc238: LeaveFrame
    //     0xbdc238: mov             SP, fp
    //     0xbdc23c: ldp             fp, lr, [SP], #0x10
    // 0xbdc240: ret
    //     0xbdc240: ret             
  }
}
