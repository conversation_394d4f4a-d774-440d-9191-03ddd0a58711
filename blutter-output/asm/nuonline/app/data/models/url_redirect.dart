// lib: , url: package:nuonline/app/data/models/url_redirect.dart

// class id: 1050060, size: 0x8
class :: {
}

// class id: 1119, size: 0x14, field offset: 0x8
class UrlRedirect extends Object {

  factory _ UrlRedirect.fromMap(/* No info */) {
    // ** addr: 0x7eb448, size: 0x1a8
    // 0x7eb448: EnterFrame
    //     0x7eb448: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb44c: mov             fp, SP
    // 0x7eb450: AllocStack(0x18)
    //     0x7eb450: sub             SP, SP, #0x18
    // 0x7eb454: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7eb454: mov             x3, x2
    //     0x7eb458: stur            x2, [fp, #-8]
    // 0x7eb45c: CheckStackOverflow
    //     0x7eb45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb460: cmp             SP, x16
    //     0x7eb464: b.ls            #0x7eb5e8
    // 0x7eb468: r0 = LoadClassIdInstr(r3)
    //     0x7eb468: ldur            x0, [x3, #-1]
    //     0x7eb46c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb470: mov             x1, x3
    // 0x7eb474: r2 = "url"
    //     0x7eb474: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x7eb478: ldr             x2, [x2, #0xd78]
    // 0x7eb47c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb47c: sub             lr, x0, #0x114
    //     0x7eb480: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb484: blr             lr
    // 0x7eb488: mov             x3, x0
    // 0x7eb48c: r2 = Null
    //     0x7eb48c: mov             x2, NULL
    // 0x7eb490: r1 = Null
    //     0x7eb490: mov             x1, NULL
    // 0x7eb494: stur            x3, [fp, #-0x10]
    // 0x7eb498: r4 = 60
    //     0x7eb498: movz            x4, #0x3c
    // 0x7eb49c: branchIfSmi(r0, 0x7eb4a8)
    //     0x7eb49c: tbz             w0, #0, #0x7eb4a8
    // 0x7eb4a0: r4 = LoadClassIdInstr(r0)
    //     0x7eb4a0: ldur            x4, [x0, #-1]
    //     0x7eb4a4: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb4a8: sub             x4, x4, #0x5e
    // 0x7eb4ac: cmp             x4, #1
    // 0x7eb4b0: b.ls            #0x7eb4c4
    // 0x7eb4b4: r8 = String
    //     0x7eb4b4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7eb4b8: r3 = Null
    //     0x7eb4b8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40810] Null
    //     0x7eb4bc: ldr             x3, [x3, #0x810]
    // 0x7eb4c0: r0 = String()
    //     0x7eb4c0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7eb4c4: ldur            x3, [fp, #-8]
    // 0x7eb4c8: r0 = LoadClassIdInstr(r3)
    //     0x7eb4c8: ldur            x0, [x3, #-1]
    //     0x7eb4cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb4d0: mov             x1, x3
    // 0x7eb4d4: r2 = "params"
    //     0x7eb4d4: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x7eb4d8: ldr             x2, [x2, #0x3b8]
    // 0x7eb4dc: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb4dc: sub             lr, x0, #0x114
    //     0x7eb4e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb4e4: blr             lr
    // 0x7eb4e8: cmp             w0, NULL
    // 0x7eb4ec: b.eq            #0x7eb53c
    // 0x7eb4f0: ldur            x3, [fp, #-8]
    // 0x7eb4f4: r0 = LoadClassIdInstr(r3)
    //     0x7eb4f4: ldur            x0, [x3, #-1]
    //     0x7eb4f8: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb4fc: mov             x1, x3
    // 0x7eb500: r2 = "params"
    //     0x7eb500: add             x2, PP, #9, lsl #12  ; [pp+0x93b8] "params"
    //     0x7eb504: ldr             x2, [x2, #0x3b8]
    // 0x7eb508: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb508: sub             lr, x0, #0x114
    //     0x7eb50c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb510: blr             lr
    // 0x7eb514: mov             x3, x0
    // 0x7eb518: r2 = Null
    //     0x7eb518: mov             x2, NULL
    // 0x7eb51c: r1 = Null
    //     0x7eb51c: mov             x1, NULL
    // 0x7eb520: stur            x3, [fp, #-0x18]
    // 0x7eb524: r8 = Map<String, dynamic>
    //     0x7eb524: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7eb528: r3 = Null
    //     0x7eb528: add             x3, PP, #0x40, lsl #12  ; [pp+0x40820] Null
    //     0x7eb52c: ldr             x3, [x3, #0x820]
    // 0x7eb530: r0 = Map<String, dynamic>()
    //     0x7eb530: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7eb534: ldur            x3, [fp, #-0x18]
    // 0x7eb538: b               #0x7eb540
    // 0x7eb53c: r3 = Null
    //     0x7eb53c: mov             x3, NULL
    // 0x7eb540: ldur            x1, [fp, #-8]
    // 0x7eb544: stur            x3, [fp, #-0x18]
    // 0x7eb548: r0 = LoadClassIdInstr(r1)
    //     0x7eb548: ldur            x0, [x1, #-1]
    //     0x7eb54c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb550: r2 = "is_external"
    //     0x7eb550: add             x2, PP, #0x40, lsl #12  ; [pp+0x40830] "is_external"
    //     0x7eb554: ldr             x2, [x2, #0x830]
    // 0x7eb558: r0 = GDT[cid_x0 + -0x114]()
    //     0x7eb558: sub             lr, x0, #0x114
    //     0x7eb55c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb560: blr             lr
    // 0x7eb564: mov             x3, x0
    // 0x7eb568: r2 = Null
    //     0x7eb568: mov             x2, NULL
    // 0x7eb56c: r1 = Null
    //     0x7eb56c: mov             x1, NULL
    // 0x7eb570: stur            x3, [fp, #-8]
    // 0x7eb574: r4 = 60
    //     0x7eb574: movz            x4, #0x3c
    // 0x7eb578: branchIfSmi(r0, 0x7eb584)
    //     0x7eb578: tbz             w0, #0, #0x7eb584
    // 0x7eb57c: r4 = LoadClassIdInstr(r0)
    //     0x7eb57c: ldur            x4, [x0, #-1]
    //     0x7eb580: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb584: cmp             x4, #0x3f
    // 0x7eb588: b.eq            #0x7eb59c
    // 0x7eb58c: r8 = bool?
    //     0x7eb58c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7eb590: r3 = Null
    //     0x7eb590: add             x3, PP, #0x40, lsl #12  ; [pp+0x40838] Null
    //     0x7eb594: ldr             x3, [x3, #0x838]
    // 0x7eb598: r0 = bool?()
    //     0x7eb598: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7eb59c: ldur            x0, [fp, #-8]
    // 0x7eb5a0: cmp             w0, NULL
    // 0x7eb5a4: b.ne            #0x7eb5b0
    // 0x7eb5a8: r2 = true
    //     0x7eb5a8: add             x2, NULL, #0x20  ; true
    // 0x7eb5ac: b               #0x7eb5b4
    // 0x7eb5b0: mov             x2, x0
    // 0x7eb5b4: ldur            x1, [fp, #-0x10]
    // 0x7eb5b8: ldur            x0, [fp, #-0x18]
    // 0x7eb5bc: stur            x2, [fp, #-8]
    // 0x7eb5c0: r0 = UrlRedirect()
    //     0x7eb5c0: bl              #0x7eb43c  ; AllocateUrlRedirectStub -> UrlRedirect (size=0x14)
    // 0x7eb5c4: ldur            x1, [fp, #-0x10]
    // 0x7eb5c8: StoreField: r0->field_7 = r1
    //     0x7eb5c8: stur            w1, [x0, #7]
    // 0x7eb5cc: ldur            x1, [fp, #-0x18]
    // 0x7eb5d0: StoreField: r0->field_b = r1
    //     0x7eb5d0: stur            w1, [x0, #0xb]
    // 0x7eb5d4: ldur            x1, [fp, #-8]
    // 0x7eb5d8: StoreField: r0->field_f = r1
    //     0x7eb5d8: stur            w1, [x0, #0xf]
    // 0x7eb5dc: LeaveFrame
    //     0x7eb5dc: mov             SP, fp
    //     0x7eb5e0: ldp             fp, lr, [SP], #0x10
    // 0x7eb5e4: ret
    //     0x7eb5e4: ret             
    // 0x7eb5e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb5e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb5ec: b               #0x7eb468
  }
}
