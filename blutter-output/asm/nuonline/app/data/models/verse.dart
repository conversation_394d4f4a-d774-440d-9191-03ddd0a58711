// lib: , url: package:nuonline/app/data/models/verse.dart

// class id: 1050061, size: 0x8
class :: {

  static String _extension#1.toArabic(int) {
    // ** addr: 0xb32690, size: 0x130
    // 0xb32690: EnterFrame
    //     0xb32690: stp             fp, lr, [SP, #-0x10]!
    //     0xb32694: mov             fp, SP
    // 0xb32698: AllocStack(0x18)
    //     0xb32698: sub             SP, SP, #0x18
    // 0xb3269c: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0xb3269c: mov             x2, x1
    // 0xb326a0: CheckStackOverflow
    //     0xb326a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb326a4: cmp             SP, x16
    //     0xb326a8: b.ls            #0xb327b0
    // 0xb326ac: r0 = BoxInt64Instr(r2)
    //     0xb326ac: sbfiz           x0, x2, #1, #0x1f
    //     0xb326b0: cmp             x2, x0, asr #1
    //     0xb326b4: b.eq            #0xb326c0
    //     0xb326b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb326bc: stur            x2, [x0, #7]
    // 0xb326c0: r1 = 60
    //     0xb326c0: movz            x1, #0x3c
    // 0xb326c4: branchIfSmi(r0, 0xb326d0)
    //     0xb326c4: tbz             w0, #0, #0xb326d0
    // 0xb326c8: r1 = LoadClassIdInstr(r0)
    //     0xb326c8: ldur            x1, [x0, #-1]
    //     0xb326cc: ubfx            x1, x1, #0xc, #0x14
    // 0xb326d0: str             x0, [SP]
    // 0xb326d4: mov             x0, x1
    // 0xb326d8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb326d8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb326dc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb326dc: movz            x17, #0x2b03
    //     0xb326e0: add             lr, x0, x17
    //     0xb326e4: ldr             lr, [x21, lr, lsl #3]
    //     0xb326e8: blr             lr
    // 0xb326ec: mov             x2, x0
    // 0xb326f0: r5 = 0
    //     0xb326f0: movz            x5, #0
    // 0xb326f4: r4 = const [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    //     0xb326f4: add             x4, PP, #0x20, lsl #12  ; [pp+0x20b28] List<String>(10)
    //     0xb326f8: ldr             x4, [x4, #0xb28]
    // 0xb326fc: r0 = const [٠, ١, ٢, ٣, ٤, ٥, ٦, ٧, ٨, ٩]
    //     0xb326fc: add             x0, PP, #0x20, lsl #12  ; [pp+0x20b30] List<String>(10)
    //     0xb32700: ldr             x0, [x0, #0xb30]
    // 0xb32704: stur            x5, [fp, #-8]
    // 0xb32708: stur            x2, [fp, #-0x10]
    // 0xb3270c: CheckStackOverflow
    //     0xb3270c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32710: cmp             SP, x16
    //     0xb32714: b.ls            #0xb327b8
    // 0xb32718: cmp             x5, #0xa
    // 0xb3271c: b.ge            #0xb3275c
    // 0xb32720: ArrayLoad: r1 = r4[r5]  ; Unknown_4
    //     0xb32720: add             x16, x4, x5, lsl #2
    //     0xb32724: ldur            w1, [x16, #0xf]
    // 0xb32728: DecompressPointer r1
    //     0xb32728: add             x1, x1, HEAP, lsl #32
    // 0xb3272c: ArrayLoad: r3 = r0[r5]  ; Unknown_4
    //     0xb3272c: add             x16, x0, x5, lsl #2
    //     0xb32730: ldur            w3, [x16, #0xf]
    // 0xb32734: DecompressPointer r3
    //     0xb32734: add             x3, x3, HEAP, lsl #32
    // 0xb32738: mov             x16, x1
    // 0xb3273c: mov             x1, x2
    // 0xb32740: mov             x2, x16
    // 0xb32744: r0 = replaceAll()
    //     0xb32744: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb32748: mov             x1, x0
    // 0xb3274c: ldur            x0, [fp, #-8]
    // 0xb32750: add             x5, x0, #1
    // 0xb32754: mov             x2, x1
    // 0xb32758: b               #0xb326f4
    // 0xb3275c: r1 = <int>
    //     0xb3275c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb32760: r0 = Runes()
    //     0xb32760: bl              #0x7bc644  ; AllocateRunesStub -> Runes (size=0x10)
    // 0xb32764: mov             x1, x0
    // 0xb32768: ldur            x0, [fp, #-0x10]
    // 0xb3276c: StoreField: r1->field_b = r0
    //     0xb3276c: stur            w0, [x1, #0xb]
    // 0xb32770: mov             x2, x1
    // 0xb32774: r1 = <int>
    //     0xb32774: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb32778: r0 = _GrowableList.of()
    //     0xb32778: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb3277c: stur            x0, [fp, #-0x10]
    // 0xb32780: LoadField: r1 = r0->field_7
    //     0xb32780: ldur            w1, [x0, #7]
    // 0xb32784: DecompressPointer r1
    //     0xb32784: add             x1, x1, HEAP, lsl #32
    // 0xb32788: r0 = ReversedListIterable()
    //     0xb32788: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xb3278c: mov             x1, x0
    // 0xb32790: ldur            x0, [fp, #-0x10]
    // 0xb32794: StoreField: r1->field_b = r0
    //     0xb32794: stur            w0, [x1, #0xb]
    // 0xb32798: r2 = 0
    //     0xb32798: movz            x2, #0
    // 0xb3279c: r3 = Null
    //     0xb3279c: mov             x3, NULL
    // 0xb327a0: r0 = createFromCharCodes()
    //     0xb327a0: bl              #0x601670  ; [dart:core] _StringBase::createFromCharCodes
    // 0xb327a4: LeaveFrame
    //     0xb327a4: mov             SP, fp
    //     0xb327a8: ldp             fp, lr, [SP], #0x10
    // 0xb327ac: ret
    //     0xb327ac: ret             
    // 0xb327b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb327b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb327b4: b               #0xb326ac
    // 0xb327b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb327b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb327bc: b               #0xb32718
  }
}

// class id: 1118, size: 0x64, field offset: 0x8
class Verse extends Object {

  String toJson(Verse) {
    // ** addr: 0x7609c4, size: 0x48
    // 0x7609c4: EnterFrame
    //     0x7609c4: stp             fp, lr, [SP, #-0x10]!
    //     0x7609c8: mov             fp, SP
    // 0x7609cc: CheckStackOverflow
    //     0x7609cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7609d0: cmp             SP, x16
    //     0x7609d4: b.ls            #0x7609ec
    // 0x7609d8: ldr             x1, [fp, #0x10]
    // 0x7609dc: r0 = toJson()
    //     0x7609dc: bl              #0x7609f4  ; [package:nuonline/app/data/models/verse.dart] Verse::toJson
    // 0x7609e0: LeaveFrame
    //     0x7609e0: mov             SP, fp
    //     0x7609e4: ldp             fp, lr, [SP], #0x10
    // 0x7609e8: ret
    //     0x7609e8: ret             
    // 0x7609ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7609ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7609f0: b               #0x7609d8
  }
  String toJson(Verse) {
    // ** addr: 0x7609f4, size: 0x3c
    // 0x7609f4: EnterFrame
    //     0x7609f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7609f8: mov             fp, SP
    // 0x7609fc: CheckStackOverflow
    //     0x7609fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x760a00: cmp             SP, x16
    //     0x760a04: b.ls            #0x760a28
    // 0x760a08: r0 = toMap()
    //     0x760a08: bl              #0x760a30  ; [package:nuonline/app/data/models/verse.dart] Verse::toMap
    // 0x760a0c: mov             x2, x0
    // 0x760a10: r1 = Instance_JsonCodec
    //     0x760a10: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x760a14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x760a14: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x760a18: r0 = encode()
    //     0x760a18: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x760a1c: LeaveFrame
    //     0x760a1c: mov             SP, fp
    //     0x760a20: ldp             fp, lr, [SP], #0x10
    // 0x760a24: ret
    //     0x760a24: ret             
    // 0x760a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x760a28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x760a2c: b               #0x760a08
  }
  _ toMap(/* No info */) {
    // ** addr: 0x760a30, size: 0x2ec
    // 0x760a30: EnterFrame
    //     0x760a30: stp             fp, lr, [SP, #-0x10]!
    //     0x760a34: mov             fp, SP
    // 0x760a38: AllocStack(0x18)
    //     0x760a38: sub             SP, SP, #0x18
    // 0x760a3c: SetupParameters(Verse this /* r1 => r0, fp-0x8 */)
    //     0x760a3c: mov             x0, x1
    //     0x760a40: stur            x1, [fp, #-8]
    // 0x760a44: CheckStackOverflow
    //     0x760a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x760a48: cmp             SP, x16
    //     0x760a4c: b.ls            #0x760d14
    // 0x760a50: r1 = Null
    //     0x760a50: mov             x1, NULL
    // 0x760a54: r2 = 44
    //     0x760a54: movz            x2, #0x2c
    // 0x760a58: r0 = AllocateArray()
    //     0x760a58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x760a5c: mov             x2, x0
    // 0x760a60: r16 = "id"
    //     0x760a60: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x760a64: ldr             x16, [x16, #0x740]
    // 0x760a68: StoreField: r2->field_f = r16
    //     0x760a68: stur            w16, [x2, #0xf]
    // 0x760a6c: ldur            x3, [fp, #-8]
    // 0x760a70: LoadField: r4 = r3->field_7
    //     0x760a70: ldur            x4, [x3, #7]
    // 0x760a74: r0 = BoxInt64Instr(r4)
    //     0x760a74: sbfiz           x0, x4, #1, #0x1f
    //     0x760a78: cmp             x4, x0, asr #1
    //     0x760a7c: b.eq            #0x760a88
    //     0x760a80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x760a84: stur            x4, [x0, #7]
    // 0x760a88: mov             x1, x2
    // 0x760a8c: ArrayStore: r1[1] = r0  ; List_4
    //     0x760a8c: add             x25, x1, #0x13
    //     0x760a90: str             w0, [x25]
    //     0x760a94: tbz             w0, #0, #0x760ab0
    //     0x760a98: ldurb           w16, [x1, #-1]
    //     0x760a9c: ldurb           w17, [x0, #-1]
    //     0x760aa0: and             x16, x17, x16, lsr #2
    //     0x760aa4: tst             x16, HEAP, lsr #32
    //     0x760aa8: b.eq            #0x760ab0
    //     0x760aac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760ab0: r16 = "page"
    //     0x760ab0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x760ab4: ldr             x16, [x16, #0x300]
    // 0x760ab8: ArrayStore: r2[0] = r16  ; List_4
    //     0x760ab8: stur            w16, [x2, #0x17]
    // 0x760abc: LoadField: r4 = r3->field_f
    //     0x760abc: ldur            x4, [x3, #0xf]
    // 0x760ac0: r0 = BoxInt64Instr(r4)
    //     0x760ac0: sbfiz           x0, x4, #1, #0x1f
    //     0x760ac4: cmp             x4, x0, asr #1
    //     0x760ac8: b.eq            #0x760ad4
    //     0x760acc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x760ad0: stur            x4, [x0, #7]
    // 0x760ad4: mov             x1, x2
    // 0x760ad8: ArrayStore: r1[3] = r0  ; List_4
    //     0x760ad8: add             x25, x1, #0x1b
    //     0x760adc: str             w0, [x25]
    //     0x760ae0: tbz             w0, #0, #0x760afc
    //     0x760ae4: ldurb           w16, [x1, #-1]
    //     0x760ae8: ldurb           w17, [x0, #-1]
    //     0x760aec: and             x16, x17, x16, lsr #2
    //     0x760af0: tst             x16, HEAP, lsr #32
    //     0x760af4: b.eq            #0x760afc
    //     0x760af8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760afc: r16 = "number"
    //     0x760afc: add             x16, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x760b00: ldr             x16, [x16, #0x998]
    // 0x760b04: StoreField: r2->field_1f = r16
    //     0x760b04: stur            w16, [x2, #0x1f]
    // 0x760b08: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x760b08: ldur            x4, [x3, #0x17]
    // 0x760b0c: r0 = BoxInt64Instr(r4)
    //     0x760b0c: sbfiz           x0, x4, #1, #0x1f
    //     0x760b10: cmp             x4, x0, asr #1
    //     0x760b14: b.eq            #0x760b20
    //     0x760b18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x760b1c: stur            x4, [x0, #7]
    // 0x760b20: mov             x1, x2
    // 0x760b24: ArrayStore: r1[5] = r0  ; List_4
    //     0x760b24: add             x25, x1, #0x23
    //     0x760b28: str             w0, [x25]
    //     0x760b2c: tbz             w0, #0, #0x760b48
    //     0x760b30: ldurb           w16, [x1, #-1]
    //     0x760b34: ldurb           w17, [x0, #-1]
    //     0x760b38: and             x16, x17, x16, lsr #2
    //     0x760b3c: tst             x16, HEAP, lsr #32
    //     0x760b40: b.eq            #0x760b48
    //     0x760b44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760b48: r16 = "value"
    //     0x760b48: ldr             x16, [PP, #0x4dc0]  ; [pp+0x4dc0] "value"
    // 0x760b4c: StoreField: r2->field_27 = r16
    //     0x760b4c: stur            w16, [x2, #0x27]
    // 0x760b50: LoadField: r0 = r3->field_1f
    //     0x760b50: ldur            w0, [x3, #0x1f]
    // 0x760b54: DecompressPointer r0
    //     0x760b54: add             x0, x0, HEAP, lsl #32
    // 0x760b58: mov             x1, x2
    // 0x760b5c: ArrayStore: r1[7] = r0  ; List_4
    //     0x760b5c: add             x25, x1, #0x2b
    //     0x760b60: str             w0, [x25]
    //     0x760b64: tbz             w0, #0, #0x760b80
    //     0x760b68: ldurb           w16, [x1, #-1]
    //     0x760b6c: ldurb           w17, [x0, #-1]
    //     0x760b70: and             x16, x17, x16, lsr #2
    //     0x760b74: tst             x16, HEAP, lsr #32
    //     0x760b78: b.eq            #0x760b80
    //     0x760b7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760b80: r16 = "translation"
    //     0x760b80: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0x760b84: ldr             x16, [x16, #0x190]
    // 0x760b88: StoreField: r2->field_2f = r16
    //     0x760b88: stur            w16, [x2, #0x2f]
    // 0x760b8c: LoadField: r0 = r3->field_23
    //     0x760b8c: ldur            w0, [x3, #0x23]
    // 0x760b90: DecompressPointer r0
    //     0x760b90: add             x0, x0, HEAP, lsl #32
    // 0x760b94: mov             x1, x2
    // 0x760b98: ArrayStore: r1[9] = r0  ; List_4
    //     0x760b98: add             x25, x1, #0x33
    //     0x760b9c: str             w0, [x25]
    //     0x760ba0: tbz             w0, #0, #0x760bbc
    //     0x760ba4: ldurb           w16, [x1, #-1]
    //     0x760ba8: ldurb           w17, [x0, #-1]
    //     0x760bac: and             x16, x17, x16, lsr #2
    //     0x760bb0: tst             x16, HEAP, lsr #32
    //     0x760bb4: b.eq            #0x760bbc
    //     0x760bb8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760bbc: r16 = "transliteration"
    //     0x760bbc: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0x760bc0: ldr             x16, [x16, #0xe58]
    // 0x760bc4: StoreField: r2->field_37 = r16
    //     0x760bc4: stur            w16, [x2, #0x37]
    // 0x760bc8: LoadField: r0 = r3->field_27
    //     0x760bc8: ldur            w0, [x3, #0x27]
    // 0x760bcc: DecompressPointer r0
    //     0x760bcc: add             x0, x0, HEAP, lsl #32
    // 0x760bd0: mov             x1, x2
    // 0x760bd4: ArrayStore: r1[11] = r0  ; List_4
    //     0x760bd4: add             x25, x1, #0x3b
    //     0x760bd8: str             w0, [x25]
    //     0x760bdc: tbz             w0, #0, #0x760bf8
    //     0x760be0: ldurb           w16, [x1, #-1]
    //     0x760be4: ldurb           w17, [x0, #-1]
    //     0x760be8: and             x16, x17, x16, lsr #2
    //     0x760bec: tst             x16, HEAP, lsr #32
    //     0x760bf0: b.eq            #0x760bf8
    //     0x760bf4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760bf8: r16 = "juz_id"
    //     0x760bf8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x760bfc: ldr             x16, [x16, #0x328]
    // 0x760c00: StoreField: r2->field_3f = r16
    //     0x760c00: stur            w16, [x2, #0x3f]
    // 0x760c04: LoadField: r4 = r3->field_2b
    //     0x760c04: ldur            x4, [x3, #0x2b]
    // 0x760c08: r0 = BoxInt64Instr(r4)
    //     0x760c08: sbfiz           x0, x4, #1, #0x1f
    //     0x760c0c: cmp             x4, x0, asr #1
    //     0x760c10: b.eq            #0x760c1c
    //     0x760c14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x760c18: stur            x4, [x0, #7]
    // 0x760c1c: mov             x1, x2
    // 0x760c20: ArrayStore: r1[13] = r0  ; List_4
    //     0x760c20: add             x25, x1, #0x43
    //     0x760c24: str             w0, [x25]
    //     0x760c28: tbz             w0, #0, #0x760c44
    //     0x760c2c: ldurb           w16, [x1, #-1]
    //     0x760c30: ldurb           w17, [x0, #-1]
    //     0x760c34: and             x16, x17, x16, lsr #2
    //     0x760c38: tst             x16, HEAP, lsr #32
    //     0x760c3c: b.eq            #0x760c44
    //     0x760c40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760c44: r16 = "surah_id"
    //     0x760c44: add             x16, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x760c48: ldr             x16, [x16, #0x340]
    // 0x760c4c: StoreField: r2->field_47 = r16
    //     0x760c4c: stur            w16, [x2, #0x47]
    // 0x760c50: LoadField: r4 = r3->field_33
    //     0x760c50: ldur            x4, [x3, #0x33]
    // 0x760c54: r0 = BoxInt64Instr(r4)
    //     0x760c54: sbfiz           x0, x4, #1, #0x1f
    //     0x760c58: cmp             x4, x0, asr #1
    //     0x760c5c: b.eq            #0x760c68
    //     0x760c60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x760c64: stur            x4, [x0, #7]
    // 0x760c68: mov             x1, x2
    // 0x760c6c: ArrayStore: r1[15] = r0  ; List_4
    //     0x760c6c: add             x25, x1, #0x4b
    //     0x760c70: str             w0, [x25]
    //     0x760c74: tbz             w0, #0, #0x760c90
    //     0x760c78: ldurb           w16, [x1, #-1]
    //     0x760c7c: ldurb           w17, [x0, #-1]
    //     0x760c80: and             x16, x17, x16, lsr #2
    //     0x760c84: tst             x16, HEAP, lsr #32
    //     0x760c88: b.eq            #0x760c90
    //     0x760c8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760c90: r16 = "last"
    //     0x760c90: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a268] "last"
    //     0x760c94: ldr             x16, [x16, #0x268]
    // 0x760c98: StoreField: r2->field_4f = r16
    //     0x760c98: stur            w16, [x2, #0x4f]
    // 0x760c9c: LoadField: r0 = r3->field_3f
    //     0x760c9c: ldur            w0, [x3, #0x3f]
    // 0x760ca0: DecompressPointer r0
    //     0x760ca0: add             x0, x0, HEAP, lsl #32
    // 0x760ca4: StoreField: r2->field_53 = r0
    //     0x760ca4: stur            w0, [x2, #0x53]
    // 0x760ca8: r16 = "bookmark"
    //     0x760ca8: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a0e8] "bookmark"
    //     0x760cac: ldr             x16, [x16, #0xe8]
    // 0x760cb0: StoreField: r2->field_57 = r16
    //     0x760cb0: stur            w16, [x2, #0x57]
    // 0x760cb4: LoadField: r0 = r3->field_43
    //     0x760cb4: ldur            w0, [x3, #0x43]
    // 0x760cb8: DecompressPointer r0
    //     0x760cb8: add             x0, x0, HEAP, lsl #32
    // 0x760cbc: StoreField: r2->field_5b = r0
    //     0x760cbc: stur            w0, [x2, #0x5b]
    // 0x760cc0: r16 = "updatedAt"
    //     0x760cc0: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a0f0] "updatedAt"
    //     0x760cc4: ldr             x16, [x16, #0xf0]
    // 0x760cc8: StoreField: r2->field_5f = r16
    //     0x760cc8: stur            w16, [x2, #0x5f]
    // 0x760ccc: LoadField: r0 = r3->field_47
    //     0x760ccc: ldur            w0, [x3, #0x47]
    // 0x760cd0: DecompressPointer r0
    //     0x760cd0: add             x0, x0, HEAP, lsl #32
    // 0x760cd4: mov             x1, x2
    // 0x760cd8: ArrayStore: r1[21] = r0  ; List_4
    //     0x760cd8: add             x25, x1, #0x63
    //     0x760cdc: str             w0, [x25]
    //     0x760ce0: tbz             w0, #0, #0x760cfc
    //     0x760ce4: ldurb           w16, [x1, #-1]
    //     0x760ce8: ldurb           w17, [x0, #-1]
    //     0x760cec: and             x16, x17, x16, lsr #2
    //     0x760cf0: tst             x16, HEAP, lsr #32
    //     0x760cf4: b.eq            #0x760cfc
    //     0x760cf8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x760cfc: r16 = <String, dynamic>
    //     0x760cfc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x760d00: stp             x2, x16, [SP]
    // 0x760d04: r0 = Map._fromLiteral()
    //     0x760d04: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x760d08: LeaveFrame
    //     0x760d08: mov             SP, fp
    //     0x760d0c: ldp             fp, lr, [SP], #0x10
    // 0x760d10: ret
    //     0x760d10: ret             
    // 0x760d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x760d14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x760d18: b               #0x760a50
  }
  factory _ Verse.fromMap(/* No info */) {
    // ** addr: 0x7c6bb0, size: 0xc84
    // 0x7c6bb0: EnterFrame
    //     0x7c6bb0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c6bb4: mov             fp, SP
    // 0x7c6bb8: AllocStack(0xb0)
    //     0x7c6bb8: sub             SP, SP, #0xb0
    // 0x7c6bbc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7c6bbc: mov             x3, x2
    //     0x7c6bc0: stur            x2, [fp, #-8]
    // 0x7c6bc4: CheckStackOverflow
    //     0x7c6bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c6bc8: cmp             SP, x16
    //     0x7c6bcc: b.ls            #0x7c782c
    // 0x7c6bd0: r0 = LoadClassIdInstr(r3)
    //     0x7c6bd0: ldur            x0, [x3, #-1]
    //     0x7c6bd4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6bd8: mov             x1, x3
    // 0x7c6bdc: r2 = "id"
    //     0x7c6bdc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x7c6be0: ldr             x2, [x2, #0x740]
    // 0x7c6be4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6be4: sub             lr, x0, #0x114
    //     0x7c6be8: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6bec: blr             lr
    // 0x7c6bf0: mov             x3, x0
    // 0x7c6bf4: r2 = Null
    //     0x7c6bf4: mov             x2, NULL
    // 0x7c6bf8: r1 = Null
    //     0x7c6bf8: mov             x1, NULL
    // 0x7c6bfc: stur            x3, [fp, #-0x10]
    // 0x7c6c00: branchIfSmi(r0, 0x7c6c28)
    //     0x7c6c00: tbz             w0, #0, #0x7c6c28
    // 0x7c6c04: r4 = LoadClassIdInstr(r0)
    //     0x7c6c04: ldur            x4, [x0, #-1]
    //     0x7c6c08: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6c0c: sub             x4, x4, #0x3c
    // 0x7c6c10: cmp             x4, #1
    // 0x7c6c14: b.ls            #0x7c6c28
    // 0x7c6c18: r8 = int
    //     0x7c6c18: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c6c1c: r3 = Null
    //     0x7c6c1c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a130] Null
    //     0x7c6c20: ldr             x3, [x3, #0x130]
    // 0x7c6c24: r0 = int()
    //     0x7c6c24: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c6c28: ldur            x3, [fp, #-8]
    // 0x7c6c2c: r0 = LoadClassIdInstr(r3)
    //     0x7c6c2c: ldur            x0, [x3, #-1]
    //     0x7c6c30: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6c34: mov             x1, x3
    // 0x7c6c38: r2 = "page"
    //     0x7c6c38: add             x2, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7c6c3c: ldr             x2, [x2, #0x300]
    // 0x7c6c40: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6c40: sub             lr, x0, #0x114
    //     0x7c6c44: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6c48: blr             lr
    // 0x7c6c4c: cmp             w0, NULL
    // 0x7c6c50: b.eq            #0x7c6cc8
    // 0x7c6c54: ldur            x3, [fp, #-8]
    // 0x7c6c58: r0 = LoadClassIdInstr(r3)
    //     0x7c6c58: ldur            x0, [x3, #-1]
    //     0x7c6c5c: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6c60: mov             x1, x3
    // 0x7c6c64: r2 = "page"
    //     0x7c6c64: add             x2, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7c6c68: ldr             x2, [x2, #0x300]
    // 0x7c6c6c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6c6c: sub             lr, x0, #0x114
    //     0x7c6c70: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6c74: blr             lr
    // 0x7c6c78: mov             x3, x0
    // 0x7c6c7c: r2 = Null
    //     0x7c6c7c: mov             x2, NULL
    // 0x7c6c80: r1 = Null
    //     0x7c6c80: mov             x1, NULL
    // 0x7c6c84: stur            x3, [fp, #-0x18]
    // 0x7c6c88: branchIfSmi(r0, 0x7c6cb0)
    //     0x7c6c88: tbz             w0, #0, #0x7c6cb0
    // 0x7c6c8c: r4 = LoadClassIdInstr(r0)
    //     0x7c6c8c: ldur            x4, [x0, #-1]
    //     0x7c6c90: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6c94: sub             x4, x4, #0x3c
    // 0x7c6c98: cmp             x4, #1
    // 0x7c6c9c: b.ls            #0x7c6cb0
    // 0x7c6ca0: r8 = int
    //     0x7c6ca0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c6ca4: r3 = Null
    //     0x7c6ca4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a140] Null
    //     0x7c6ca8: ldr             x3, [x3, #0x140]
    // 0x7c6cac: r0 = int()
    //     0x7c6cac: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c6cb0: ldur            x0, [fp, #-0x18]
    // 0x7c6cb4: r1 = LoadInt32Instr(r0)
    //     0x7c6cb4: sbfx            x1, x0, #1, #0x1f
    //     0x7c6cb8: tbz             w0, #0, #0x7c6cc0
    //     0x7c6cbc: ldur            x1, [x0, #7]
    // 0x7c6cc0: mov             x4, x1
    // 0x7c6cc4: b               #0x7c6ccc
    // 0x7c6cc8: r4 = 0
    //     0x7c6cc8: movz            x4, #0
    // 0x7c6ccc: ldur            x3, [fp, #-8]
    // 0x7c6cd0: stur            x4, [fp, #-0x20]
    // 0x7c6cd4: r0 = LoadClassIdInstr(r3)
    //     0x7c6cd4: ldur            x0, [x3, #-1]
    //     0x7c6cd8: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6cdc: mov             x1, x3
    // 0x7c6ce0: r2 = "number"
    //     0x7c6ce0: add             x2, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x7c6ce4: ldr             x2, [x2, #0x998]
    // 0x7c6ce8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6ce8: sub             lr, x0, #0x114
    //     0x7c6cec: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6cf0: blr             lr
    // 0x7c6cf4: mov             x3, x0
    // 0x7c6cf8: r2 = Null
    //     0x7c6cf8: mov             x2, NULL
    // 0x7c6cfc: r1 = Null
    //     0x7c6cfc: mov             x1, NULL
    // 0x7c6d00: stur            x3, [fp, #-0x18]
    // 0x7c6d04: branchIfSmi(r0, 0x7c6d2c)
    //     0x7c6d04: tbz             w0, #0, #0x7c6d2c
    // 0x7c6d08: r4 = LoadClassIdInstr(r0)
    //     0x7c6d08: ldur            x4, [x0, #-1]
    //     0x7c6d0c: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6d10: sub             x4, x4, #0x3c
    // 0x7c6d14: cmp             x4, #1
    // 0x7c6d18: b.ls            #0x7c6d2c
    // 0x7c6d1c: r8 = int
    //     0x7c6d1c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c6d20: r3 = Null
    //     0x7c6d20: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a150] Null
    //     0x7c6d24: ldr             x3, [x3, #0x150]
    // 0x7c6d28: r0 = int()
    //     0x7c6d28: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c6d2c: ldur            x3, [fp, #-8]
    // 0x7c6d30: r0 = LoadClassIdInstr(r3)
    //     0x7c6d30: ldur            x0, [x3, #-1]
    //     0x7c6d34: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6d38: mov             x1, x3
    // 0x7c6d3c: r2 = "text"
    //     0x7c6d3c: ldr             x2, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0x7c6d40: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c6d40: add             lr, x0, #0x55f
    //     0x7c6d44: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6d48: blr             lr
    // 0x7c6d4c: tbnz            w0, #4, #0x7c6db4
    // 0x7c6d50: ldur            x3, [fp, #-8]
    // 0x7c6d54: r0 = LoadClassIdInstr(r3)
    //     0x7c6d54: ldur            x0, [x3, #-1]
    //     0x7c6d58: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6d5c: mov             x1, x3
    // 0x7c6d60: r2 = "text"
    //     0x7c6d60: ldr             x2, [PP, #0x7060]  ; [pp+0x7060] "text"
    // 0x7c6d64: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6d64: sub             lr, x0, #0x114
    //     0x7c6d68: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6d6c: blr             lr
    // 0x7c6d70: mov             x3, x0
    // 0x7c6d74: r2 = Null
    //     0x7c6d74: mov             x2, NULL
    // 0x7c6d78: r1 = Null
    //     0x7c6d78: mov             x1, NULL
    // 0x7c6d7c: stur            x3, [fp, #-0x28]
    // 0x7c6d80: r4 = 60
    //     0x7c6d80: movz            x4, #0x3c
    // 0x7c6d84: branchIfSmi(r0, 0x7c6d90)
    //     0x7c6d84: tbz             w0, #0, #0x7c6d90
    // 0x7c6d88: r4 = LoadClassIdInstr(r0)
    //     0x7c6d88: ldur            x4, [x0, #-1]
    //     0x7c6d8c: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6d90: sub             x4, x4, #0x5e
    // 0x7c6d94: cmp             x4, #1
    // 0x7c6d98: b.ls            #0x7c6dac
    // 0x7c6d9c: r8 = String
    //     0x7c6d9c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c6da0: r3 = Null
    //     0x7c6da0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a160] Null
    //     0x7c6da4: ldr             x3, [x3, #0x160]
    // 0x7c6da8: r0 = String()
    //     0x7c6da8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c6dac: ldur            x4, [fp, #-0x28]
    // 0x7c6db0: b               #0x7c6e14
    // 0x7c6db4: ldur            x3, [fp, #-8]
    // 0x7c6db8: r0 = LoadClassIdInstr(r3)
    //     0x7c6db8: ldur            x0, [x3, #-1]
    //     0x7c6dbc: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6dc0: mov             x1, x3
    // 0x7c6dc4: r2 = "value"
    //     0x7c6dc4: ldr             x2, [PP, #0x4dc0]  ; [pp+0x4dc0] "value"
    // 0x7c6dc8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6dc8: sub             lr, x0, #0x114
    //     0x7c6dcc: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6dd0: blr             lr
    // 0x7c6dd4: mov             x3, x0
    // 0x7c6dd8: r2 = Null
    //     0x7c6dd8: mov             x2, NULL
    // 0x7c6ddc: r1 = Null
    //     0x7c6ddc: mov             x1, NULL
    // 0x7c6de0: stur            x3, [fp, #-0x28]
    // 0x7c6de4: r4 = 60
    //     0x7c6de4: movz            x4, #0x3c
    // 0x7c6de8: branchIfSmi(r0, 0x7c6df4)
    //     0x7c6de8: tbz             w0, #0, #0x7c6df4
    // 0x7c6dec: r4 = LoadClassIdInstr(r0)
    //     0x7c6dec: ldur            x4, [x0, #-1]
    //     0x7c6df0: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6df4: sub             x4, x4, #0x5e
    // 0x7c6df8: cmp             x4, #1
    // 0x7c6dfc: b.ls            #0x7c6e10
    // 0x7c6e00: r8 = String
    //     0x7c6e00: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c6e04: r3 = Null
    //     0x7c6e04: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a170] Null
    //     0x7c6e08: ldr             x3, [x3, #0x170]
    // 0x7c6e0c: r0 = String()
    //     0x7c6e0c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c6e10: ldur            x4, [fp, #-0x28]
    // 0x7c6e14: ldur            x3, [fp, #-8]
    // 0x7c6e18: stur            x4, [fp, #-0x28]
    // 0x7c6e1c: r0 = LoadClassIdInstr(r3)
    //     0x7c6e1c: ldur            x0, [x3, #-1]
    //     0x7c6e20: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6e24: mov             x1, x3
    // 0x7c6e28: r2 = "translation_id"
    //     0x7c6e28: add             x2, PP, #0x10, lsl #12  ; [pp+0x10358] "translation_id"
    //     0x7c6e2c: ldr             x2, [x2, #0x358]
    // 0x7c6e30: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c6e30: add             lr, x0, #0x55f
    //     0x7c6e34: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6e38: blr             lr
    // 0x7c6e3c: tbnz            w0, #4, #0x7c6ea8
    // 0x7c6e40: ldur            x3, [fp, #-8]
    // 0x7c6e44: r0 = LoadClassIdInstr(r3)
    //     0x7c6e44: ldur            x0, [x3, #-1]
    //     0x7c6e48: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6e4c: mov             x1, x3
    // 0x7c6e50: r2 = "translation_id"
    //     0x7c6e50: add             x2, PP, #0x10, lsl #12  ; [pp+0x10358] "translation_id"
    //     0x7c6e54: ldr             x2, [x2, #0x358]
    // 0x7c6e58: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6e58: sub             lr, x0, #0x114
    //     0x7c6e5c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6e60: blr             lr
    // 0x7c6e64: mov             x3, x0
    // 0x7c6e68: r2 = Null
    //     0x7c6e68: mov             x2, NULL
    // 0x7c6e6c: r1 = Null
    //     0x7c6e6c: mov             x1, NULL
    // 0x7c6e70: stur            x3, [fp, #-0x30]
    // 0x7c6e74: r4 = 60
    //     0x7c6e74: movz            x4, #0x3c
    // 0x7c6e78: branchIfSmi(r0, 0x7c6e84)
    //     0x7c6e78: tbz             w0, #0, #0x7c6e84
    // 0x7c6e7c: r4 = LoadClassIdInstr(r0)
    //     0x7c6e7c: ldur            x4, [x0, #-1]
    //     0x7c6e80: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6e84: sub             x4, x4, #0x5e
    // 0x7c6e88: cmp             x4, #1
    // 0x7c6e8c: b.ls            #0x7c6ea0
    // 0x7c6e90: r8 = String
    //     0x7c6e90: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c6e94: r3 = Null
    //     0x7c6e94: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a180] Null
    //     0x7c6e98: ldr             x3, [x3, #0x180]
    // 0x7c6e9c: r0 = String()
    //     0x7c6e9c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c6ea0: ldur            x4, [fp, #-0x30]
    // 0x7c6ea4: b               #0x7c6f0c
    // 0x7c6ea8: ldur            x3, [fp, #-8]
    // 0x7c6eac: r0 = LoadClassIdInstr(r3)
    //     0x7c6eac: ldur            x0, [x3, #-1]
    //     0x7c6eb0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6eb4: mov             x1, x3
    // 0x7c6eb8: r2 = "translation"
    //     0x7c6eb8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a190] "translation"
    //     0x7c6ebc: ldr             x2, [x2, #0x190]
    // 0x7c6ec0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6ec0: sub             lr, x0, #0x114
    //     0x7c6ec4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6ec8: blr             lr
    // 0x7c6ecc: mov             x3, x0
    // 0x7c6ed0: r2 = Null
    //     0x7c6ed0: mov             x2, NULL
    // 0x7c6ed4: r1 = Null
    //     0x7c6ed4: mov             x1, NULL
    // 0x7c6ed8: stur            x3, [fp, #-0x30]
    // 0x7c6edc: r4 = 60
    //     0x7c6edc: movz            x4, #0x3c
    // 0x7c6ee0: branchIfSmi(r0, 0x7c6eec)
    //     0x7c6ee0: tbz             w0, #0, #0x7c6eec
    // 0x7c6ee4: r4 = LoadClassIdInstr(r0)
    //     0x7c6ee4: ldur            x4, [x0, #-1]
    //     0x7c6ee8: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6eec: sub             x4, x4, #0x5e
    // 0x7c6ef0: cmp             x4, #1
    // 0x7c6ef4: b.ls            #0x7c6f08
    // 0x7c6ef8: r8 = String
    //     0x7c6ef8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c6efc: r3 = Null
    //     0x7c6efc: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a198] Null
    //     0x7c6f00: ldr             x3, [x3, #0x198]
    // 0x7c6f04: r0 = String()
    //     0x7c6f04: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c6f08: ldur            x4, [fp, #-0x30]
    // 0x7c6f0c: ldur            x3, [fp, #-8]
    // 0x7c6f10: stur            x4, [fp, #-0x30]
    // 0x7c6f14: r0 = LoadClassIdInstr(r3)
    //     0x7c6f14: ldur            x0, [x3, #-1]
    //     0x7c6f18: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6f1c: mov             x1, x3
    // 0x7c6f20: r2 = "translation_en"
    //     0x7c6f20: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1a8] "translation_en"
    //     0x7c6f24: ldr             x2, [x2, #0x1a8]
    // 0x7c6f28: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c6f28: add             lr, x0, #0x55f
    //     0x7c6f2c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6f30: blr             lr
    // 0x7c6f34: tbnz            w0, #4, #0x7c6fa0
    // 0x7c6f38: ldur            x3, [fp, #-8]
    // 0x7c6f3c: r0 = LoadClassIdInstr(r3)
    //     0x7c6f3c: ldur            x0, [x3, #-1]
    //     0x7c6f40: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6f44: mov             x1, x3
    // 0x7c6f48: r2 = "translation_en"
    //     0x7c6f48: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1a8] "translation_en"
    //     0x7c6f4c: ldr             x2, [x2, #0x1a8]
    // 0x7c6f50: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6f50: sub             lr, x0, #0x114
    //     0x7c6f54: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6f58: blr             lr
    // 0x7c6f5c: mov             x3, x0
    // 0x7c6f60: r2 = Null
    //     0x7c6f60: mov             x2, NULL
    // 0x7c6f64: r1 = Null
    //     0x7c6f64: mov             x1, NULL
    // 0x7c6f68: stur            x3, [fp, #-0x38]
    // 0x7c6f6c: r4 = 60
    //     0x7c6f6c: movz            x4, #0x3c
    // 0x7c6f70: branchIfSmi(r0, 0x7c6f7c)
    //     0x7c6f70: tbz             w0, #0, #0x7c6f7c
    // 0x7c6f74: r4 = LoadClassIdInstr(r0)
    //     0x7c6f74: ldur            x4, [x0, #-1]
    //     0x7c6f78: ubfx            x4, x4, #0xc, #0x14
    // 0x7c6f7c: sub             x4, x4, #0x5e
    // 0x7c6f80: cmp             x4, #1
    // 0x7c6f84: b.ls            #0x7c6f98
    // 0x7c6f88: r8 = String
    //     0x7c6f88: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c6f8c: r3 = Null
    //     0x7c6f8c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a1b0] Null
    //     0x7c6f90: ldr             x3, [x3, #0x1b0]
    // 0x7c6f94: r0 = String()
    //     0x7c6f94: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c6f98: ldur            x4, [fp, #-0x38]
    // 0x7c6f9c: b               #0x7c6fa4
    // 0x7c6fa0: r4 = Null
    //     0x7c6fa0: mov             x4, NULL
    // 0x7c6fa4: ldur            x3, [fp, #-8]
    // 0x7c6fa8: stur            x4, [fp, #-0x38]
    // 0x7c6fac: r0 = LoadClassIdInstr(r3)
    //     0x7c6fac: ldur            x0, [x3, #-1]
    //     0x7c6fb0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6fb4: mov             x1, x3
    // 0x7c6fb8: r2 = "translation_de"
    //     0x7c6fb8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1c0] "translation_de"
    //     0x7c6fbc: ldr             x2, [x2, #0x1c0]
    // 0x7c6fc0: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c6fc0: add             lr, x0, #0x55f
    //     0x7c6fc4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6fc8: blr             lr
    // 0x7c6fcc: tbnz            w0, #4, #0x7c7038
    // 0x7c6fd0: ldur            x3, [fp, #-8]
    // 0x7c6fd4: r0 = LoadClassIdInstr(r3)
    //     0x7c6fd4: ldur            x0, [x3, #-1]
    //     0x7c6fd8: ubfx            x0, x0, #0xc, #0x14
    // 0x7c6fdc: mov             x1, x3
    // 0x7c6fe0: r2 = "translation_de"
    //     0x7c6fe0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1c0] "translation_de"
    //     0x7c6fe4: ldr             x2, [x2, #0x1c0]
    // 0x7c6fe8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c6fe8: sub             lr, x0, #0x114
    //     0x7c6fec: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6ff0: blr             lr
    // 0x7c6ff4: mov             x3, x0
    // 0x7c6ff8: r2 = Null
    //     0x7c6ff8: mov             x2, NULL
    // 0x7c6ffc: r1 = Null
    //     0x7c6ffc: mov             x1, NULL
    // 0x7c7000: stur            x3, [fp, #-0x40]
    // 0x7c7004: r4 = 60
    //     0x7c7004: movz            x4, #0x3c
    // 0x7c7008: branchIfSmi(r0, 0x7c7014)
    //     0x7c7008: tbz             w0, #0, #0x7c7014
    // 0x7c700c: r4 = LoadClassIdInstr(r0)
    //     0x7c700c: ldur            x4, [x0, #-1]
    //     0x7c7010: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7014: sub             x4, x4, #0x5e
    // 0x7c7018: cmp             x4, #1
    // 0x7c701c: b.ls            #0x7c7030
    // 0x7c7020: r8 = String
    //     0x7c7020: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c7024: r3 = Null
    //     0x7c7024: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a1c8] Null
    //     0x7c7028: ldr             x3, [x3, #0x1c8]
    // 0x7c702c: r0 = String()
    //     0x7c702c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c7030: ldur            x4, [fp, #-0x40]
    // 0x7c7034: b               #0x7c703c
    // 0x7c7038: r4 = Null
    //     0x7c7038: mov             x4, NULL
    // 0x7c703c: ldur            x3, [fp, #-8]
    // 0x7c7040: stur            x4, [fp, #-0x40]
    // 0x7c7044: r0 = LoadClassIdInstr(r3)
    //     0x7c7044: ldur            x0, [x3, #-1]
    //     0x7c7048: ubfx            x0, x0, #0xc, #0x14
    // 0x7c704c: mov             x1, x3
    // 0x7c7050: r2 = "translation_tr"
    //     0x7c7050: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1d8] "translation_tr"
    //     0x7c7054: ldr             x2, [x2, #0x1d8]
    // 0x7c7058: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c7058: add             lr, x0, #0x55f
    //     0x7c705c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7060: blr             lr
    // 0x7c7064: tbnz            w0, #4, #0x7c70d0
    // 0x7c7068: ldur            x3, [fp, #-8]
    // 0x7c706c: r0 = LoadClassIdInstr(r3)
    //     0x7c706c: ldur            x0, [x3, #-1]
    //     0x7c7070: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7074: mov             x1, x3
    // 0x7c7078: r2 = "translation_tr"
    //     0x7c7078: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1d8] "translation_tr"
    //     0x7c707c: ldr             x2, [x2, #0x1d8]
    // 0x7c7080: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7080: sub             lr, x0, #0x114
    //     0x7c7084: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7088: blr             lr
    // 0x7c708c: mov             x3, x0
    // 0x7c7090: r2 = Null
    //     0x7c7090: mov             x2, NULL
    // 0x7c7094: r1 = Null
    //     0x7c7094: mov             x1, NULL
    // 0x7c7098: stur            x3, [fp, #-0x48]
    // 0x7c709c: r4 = 60
    //     0x7c709c: movz            x4, #0x3c
    // 0x7c70a0: branchIfSmi(r0, 0x7c70ac)
    //     0x7c70a0: tbz             w0, #0, #0x7c70ac
    // 0x7c70a4: r4 = LoadClassIdInstr(r0)
    //     0x7c70a4: ldur            x4, [x0, #-1]
    //     0x7c70a8: ubfx            x4, x4, #0xc, #0x14
    // 0x7c70ac: sub             x4, x4, #0x5e
    // 0x7c70b0: cmp             x4, #1
    // 0x7c70b4: b.ls            #0x7c70c8
    // 0x7c70b8: r8 = String
    //     0x7c70b8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c70bc: r3 = Null
    //     0x7c70bc: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a1e0] Null
    //     0x7c70c0: ldr             x3, [x3, #0x1e0]
    // 0x7c70c4: r0 = String()
    //     0x7c70c4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c70c8: ldur            x4, [fp, #-0x48]
    // 0x7c70cc: b               #0x7c70d4
    // 0x7c70d0: r4 = Null
    //     0x7c70d0: mov             x4, NULL
    // 0x7c70d4: ldur            x3, [fp, #-8]
    // 0x7c70d8: stur            x4, [fp, #-0x48]
    // 0x7c70dc: r0 = LoadClassIdInstr(r3)
    //     0x7c70dc: ldur            x0, [x3, #-1]
    //     0x7c70e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c70e4: mov             x1, x3
    // 0x7c70e8: r2 = "translation_fr"
    //     0x7c70e8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1f0] "translation_fr"
    //     0x7c70ec: ldr             x2, [x2, #0x1f0]
    // 0x7c70f0: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c70f0: add             lr, x0, #0x55f
    //     0x7c70f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c70f8: blr             lr
    // 0x7c70fc: tbnz            w0, #4, #0x7c7168
    // 0x7c7100: ldur            x3, [fp, #-8]
    // 0x7c7104: r0 = LoadClassIdInstr(r3)
    //     0x7c7104: ldur            x0, [x3, #-1]
    //     0x7c7108: ubfx            x0, x0, #0xc, #0x14
    // 0x7c710c: mov             x1, x3
    // 0x7c7110: r2 = "translation_fr"
    //     0x7c7110: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a1f0] "translation_fr"
    //     0x7c7114: ldr             x2, [x2, #0x1f0]
    // 0x7c7118: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7118: sub             lr, x0, #0x114
    //     0x7c711c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7120: blr             lr
    // 0x7c7124: mov             x3, x0
    // 0x7c7128: r2 = Null
    //     0x7c7128: mov             x2, NULL
    // 0x7c712c: r1 = Null
    //     0x7c712c: mov             x1, NULL
    // 0x7c7130: stur            x3, [fp, #-0x50]
    // 0x7c7134: r4 = 60
    //     0x7c7134: movz            x4, #0x3c
    // 0x7c7138: branchIfSmi(r0, 0x7c7144)
    //     0x7c7138: tbz             w0, #0, #0x7c7144
    // 0x7c713c: r4 = LoadClassIdInstr(r0)
    //     0x7c713c: ldur            x4, [x0, #-1]
    //     0x7c7140: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7144: sub             x4, x4, #0x5e
    // 0x7c7148: cmp             x4, #1
    // 0x7c714c: b.ls            #0x7c7160
    // 0x7c7150: r8 = String
    //     0x7c7150: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c7154: r3 = Null
    //     0x7c7154: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a1f8] Null
    //     0x7c7158: ldr             x3, [x3, #0x1f8]
    // 0x7c715c: r0 = String()
    //     0x7c715c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c7160: ldur            x4, [fp, #-0x50]
    // 0x7c7164: b               #0x7c716c
    // 0x7c7168: r4 = Null
    //     0x7c7168: mov             x4, NULL
    // 0x7c716c: ldur            x3, [fp, #-8]
    // 0x7c7170: stur            x4, [fp, #-0x50]
    // 0x7c7174: r0 = LoadClassIdInstr(r3)
    //     0x7c7174: ldur            x0, [x3, #-1]
    //     0x7c7178: ubfx            x0, x0, #0xc, #0x14
    // 0x7c717c: mov             x1, x3
    // 0x7c7180: r2 = "translation_my"
    //     0x7c7180: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a208] "translation_my"
    //     0x7c7184: ldr             x2, [x2, #0x208]
    // 0x7c7188: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c7188: add             lr, x0, #0x55f
    //     0x7c718c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7190: blr             lr
    // 0x7c7194: tbnz            w0, #4, #0x7c7200
    // 0x7c7198: ldur            x3, [fp, #-8]
    // 0x7c719c: r0 = LoadClassIdInstr(r3)
    //     0x7c719c: ldur            x0, [x3, #-1]
    //     0x7c71a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c71a4: mov             x1, x3
    // 0x7c71a8: r2 = "translation_my"
    //     0x7c71a8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a208] "translation_my"
    //     0x7c71ac: ldr             x2, [x2, #0x208]
    // 0x7c71b0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c71b0: sub             lr, x0, #0x114
    //     0x7c71b4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c71b8: blr             lr
    // 0x7c71bc: mov             x3, x0
    // 0x7c71c0: r2 = Null
    //     0x7c71c0: mov             x2, NULL
    // 0x7c71c4: r1 = Null
    //     0x7c71c4: mov             x1, NULL
    // 0x7c71c8: stur            x3, [fp, #-0x58]
    // 0x7c71cc: r4 = 60
    //     0x7c71cc: movz            x4, #0x3c
    // 0x7c71d0: branchIfSmi(r0, 0x7c71dc)
    //     0x7c71d0: tbz             w0, #0, #0x7c71dc
    // 0x7c71d4: r4 = LoadClassIdInstr(r0)
    //     0x7c71d4: ldur            x4, [x0, #-1]
    //     0x7c71d8: ubfx            x4, x4, #0xc, #0x14
    // 0x7c71dc: sub             x4, x4, #0x5e
    // 0x7c71e0: cmp             x4, #1
    // 0x7c71e4: b.ls            #0x7c71f8
    // 0x7c71e8: r8 = String
    //     0x7c71e8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c71ec: r3 = Null
    //     0x7c71ec: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a210] Null
    //     0x7c71f0: ldr             x3, [x3, #0x210]
    // 0x7c71f4: r0 = String()
    //     0x7c71f4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c71f8: ldur            x4, [fp, #-0x58]
    // 0x7c71fc: b               #0x7c7204
    // 0x7c7200: r4 = Null
    //     0x7c7200: mov             x4, NULL
    // 0x7c7204: ldur            x3, [fp, #-8]
    // 0x7c7208: stur            x4, [fp, #-0x58]
    // 0x7c720c: r0 = LoadClassIdInstr(r3)
    //     0x7c720c: ldur            x0, [x3, #-1]
    //     0x7c7210: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7214: mov             x1, x3
    // 0x7c7218: r2 = "transliteration"
    //     0x7c7218: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e58] "transliteration"
    //     0x7c721c: ldr             x2, [x2, #0xe58]
    // 0x7c7220: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7220: sub             lr, x0, #0x114
    //     0x7c7224: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7228: blr             lr
    // 0x7c722c: mov             x3, x0
    // 0x7c7230: r2 = Null
    //     0x7c7230: mov             x2, NULL
    // 0x7c7234: r1 = Null
    //     0x7c7234: mov             x1, NULL
    // 0x7c7238: stur            x3, [fp, #-0x60]
    // 0x7c723c: r4 = 60
    //     0x7c723c: movz            x4, #0x3c
    // 0x7c7240: branchIfSmi(r0, 0x7c724c)
    //     0x7c7240: tbz             w0, #0, #0x7c724c
    // 0x7c7244: r4 = LoadClassIdInstr(r0)
    //     0x7c7244: ldur            x4, [x0, #-1]
    //     0x7c7248: ubfx            x4, x4, #0xc, #0x14
    // 0x7c724c: sub             x4, x4, #0x5e
    // 0x7c7250: cmp             x4, #1
    // 0x7c7254: b.ls            #0x7c7268
    // 0x7c7258: r8 = String
    //     0x7c7258: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x7c725c: r3 = Null
    //     0x7c725c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a220] Null
    //     0x7c7260: ldr             x3, [x3, #0x220]
    // 0x7c7264: r0 = String()
    //     0x7c7264: bl              #0xed43b0  ; IsType_String_Stub
    // 0x7c7268: ldur            x3, [fp, #-8]
    // 0x7c726c: r0 = LoadClassIdInstr(r3)
    //     0x7c726c: ldur            x0, [x3, #-1]
    //     0x7c7270: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7274: mov             x1, x3
    // 0x7c7278: r2 = "juz_id"
    //     0x7c7278: add             x2, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x7c727c: ldr             x2, [x2, #0x328]
    // 0x7c7280: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7280: sub             lr, x0, #0x114
    //     0x7c7284: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7288: blr             lr
    // 0x7c728c: cmp             w0, NULL
    // 0x7c7290: b.eq            #0x7c7308
    // 0x7c7294: ldur            x3, [fp, #-8]
    // 0x7c7298: r0 = LoadClassIdInstr(r3)
    //     0x7c7298: ldur            x0, [x3, #-1]
    //     0x7c729c: ubfx            x0, x0, #0xc, #0x14
    // 0x7c72a0: mov             x1, x3
    // 0x7c72a4: r2 = "juz_id"
    //     0x7c72a4: add             x2, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x7c72a8: ldr             x2, [x2, #0x328]
    // 0x7c72ac: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c72ac: sub             lr, x0, #0x114
    //     0x7c72b0: ldr             lr, [x21, lr, lsl #3]
    //     0x7c72b4: blr             lr
    // 0x7c72b8: mov             x3, x0
    // 0x7c72bc: r2 = Null
    //     0x7c72bc: mov             x2, NULL
    // 0x7c72c0: r1 = Null
    //     0x7c72c0: mov             x1, NULL
    // 0x7c72c4: stur            x3, [fp, #-0x68]
    // 0x7c72c8: branchIfSmi(r0, 0x7c72f0)
    //     0x7c72c8: tbz             w0, #0, #0x7c72f0
    // 0x7c72cc: r4 = LoadClassIdInstr(r0)
    //     0x7c72cc: ldur            x4, [x0, #-1]
    //     0x7c72d0: ubfx            x4, x4, #0xc, #0x14
    // 0x7c72d4: sub             x4, x4, #0x3c
    // 0x7c72d8: cmp             x4, #1
    // 0x7c72dc: b.ls            #0x7c72f0
    // 0x7c72e0: r8 = int
    //     0x7c72e0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c72e4: r3 = Null
    //     0x7c72e4: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a230] Null
    //     0x7c72e8: ldr             x3, [x3, #0x230]
    // 0x7c72ec: r0 = int()
    //     0x7c72ec: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c72f0: ldur            x0, [fp, #-0x68]
    // 0x7c72f4: r1 = LoadInt32Instr(r0)
    //     0x7c72f4: sbfx            x1, x0, #1, #0x1f
    //     0x7c72f8: tbz             w0, #0, #0x7c7300
    //     0x7c72fc: ldur            x1, [x0, #7]
    // 0x7c7300: mov             x4, x1
    // 0x7c7304: b               #0x7c730c
    // 0x7c7308: r4 = 0
    //     0x7c7308: movz            x4, #0
    // 0x7c730c: ldur            x3, [fp, #-8]
    // 0x7c7310: stur            x4, [fp, #-0x70]
    // 0x7c7314: r0 = LoadClassIdInstr(r3)
    //     0x7c7314: ldur            x0, [x3, #-1]
    //     0x7c7318: ubfx            x0, x0, #0xc, #0x14
    // 0x7c731c: mov             x1, x3
    // 0x7c7320: r2 = "surah_id"
    //     0x7c7320: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x7c7324: ldr             x2, [x2, #0x340]
    // 0x7c7328: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7328: sub             lr, x0, #0x114
    //     0x7c732c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7330: blr             lr
    // 0x7c7334: cmp             w0, NULL
    // 0x7c7338: b.eq            #0x7c73b0
    // 0x7c733c: ldur            x3, [fp, #-8]
    // 0x7c7340: r0 = LoadClassIdInstr(r3)
    //     0x7c7340: ldur            x0, [x3, #-1]
    //     0x7c7344: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7348: mov             x1, x3
    // 0x7c734c: r2 = "surah_id"
    //     0x7c734c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x7c7350: ldr             x2, [x2, #0x340]
    // 0x7c7354: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7354: sub             lr, x0, #0x114
    //     0x7c7358: ldr             lr, [x21, lr, lsl #3]
    //     0x7c735c: blr             lr
    // 0x7c7360: mov             x3, x0
    // 0x7c7364: r2 = Null
    //     0x7c7364: mov             x2, NULL
    // 0x7c7368: r1 = Null
    //     0x7c7368: mov             x1, NULL
    // 0x7c736c: stur            x3, [fp, #-0x68]
    // 0x7c7370: branchIfSmi(r0, 0x7c7398)
    //     0x7c7370: tbz             w0, #0, #0x7c7398
    // 0x7c7374: r4 = LoadClassIdInstr(r0)
    //     0x7c7374: ldur            x4, [x0, #-1]
    //     0x7c7378: ubfx            x4, x4, #0xc, #0x14
    // 0x7c737c: sub             x4, x4, #0x3c
    // 0x7c7380: cmp             x4, #1
    // 0x7c7384: b.ls            #0x7c7398
    // 0x7c7388: r8 = int
    //     0x7c7388: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x7c738c: r3 = Null
    //     0x7c738c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a240] Null
    //     0x7c7390: ldr             x3, [x3, #0x240]
    // 0x7c7394: r0 = int()
    //     0x7c7394: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x7c7398: ldur            x0, [fp, #-0x68]
    // 0x7c739c: r1 = LoadInt32Instr(r0)
    //     0x7c739c: sbfx            x1, x0, #1, #0x1f
    //     0x7c73a0: tbz             w0, #0, #0x7c73a8
    //     0x7c73a4: ldur            x1, [x0, #7]
    // 0x7c73a8: mov             x4, x1
    // 0x7c73ac: b               #0x7c73b4
    // 0x7c73b0: r4 = 0
    //     0x7c73b0: movz            x4, #0
    // 0x7c73b4: ldur            x3, [fp, #-8]
    // 0x7c73b8: stur            x4, [fp, #-0x78]
    // 0x7c73bc: r0 = LoadClassIdInstr(r3)
    //     0x7c73bc: ldur            x0, [x3, #-1]
    //     0x7c73c0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c73c4: mov             x1, x3
    // 0x7c73c8: r2 = "stylistics"
    //     0x7c73c8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a250] "stylistics"
    //     0x7c73cc: ldr             x2, [x2, #0x250]
    // 0x7c73d0: r0 = GDT[cid_x0 + 0x55f]()
    //     0x7c73d0: add             lr, x0, #0x55f
    //     0x7c73d4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c73d8: blr             lr
    // 0x7c73dc: tbnz            w0, #4, #0x7c7448
    // 0x7c73e0: ldur            x3, [fp, #-8]
    // 0x7c73e4: r0 = LoadClassIdInstr(r3)
    //     0x7c73e4: ldur            x0, [x3, #-1]
    //     0x7c73e8: ubfx            x0, x0, #0xc, #0x14
    // 0x7c73ec: mov             x1, x3
    // 0x7c73f0: r2 = "stylistics"
    //     0x7c73f0: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a250] "stylistics"
    //     0x7c73f4: ldr             x2, [x2, #0x250]
    // 0x7c73f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c73f8: sub             lr, x0, #0x114
    //     0x7c73fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7400: blr             lr
    // 0x7c7404: mov             x3, x0
    // 0x7c7408: r2 = Null
    //     0x7c7408: mov             x2, NULL
    // 0x7c740c: r1 = Null
    //     0x7c740c: mov             x1, NULL
    // 0x7c7410: stur            x3, [fp, #-0x68]
    // 0x7c7414: r4 = 60
    //     0x7c7414: movz            x4, #0x3c
    // 0x7c7418: branchIfSmi(r0, 0x7c7424)
    //     0x7c7418: tbz             w0, #0, #0x7c7424
    // 0x7c741c: r4 = LoadClassIdInstr(r0)
    //     0x7c741c: ldur            x4, [x0, #-1]
    //     0x7c7420: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7424: sub             x4, x4, #0x5a
    // 0x7c7428: cmp             x4, #2
    // 0x7c742c: b.ls            #0x7c7440
    // 0x7c7430: r8 = List
    //     0x7c7430: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0x7c7434: r3 = Null
    //     0x7c7434: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a258] Null
    //     0x7c7438: ldr             x3, [x3, #0x258]
    // 0x7c743c: r0 = List()
    //     0x7c743c: bl              #0xed6b40  ; IsType_List_Stub
    // 0x7c7440: ldur            x4, [fp, #-0x68]
    // 0x7c7444: b               #0x7c7458
    // 0x7c7448: r1 = Null
    //     0x7c7448: mov             x1, NULL
    // 0x7c744c: r2 = 0
    //     0x7c744c: movz            x2, #0
    // 0x7c7450: r0 = _GrowableList()
    //     0x7c7450: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7c7454: mov             x4, x0
    // 0x7c7458: ldur            x3, [fp, #-8]
    // 0x7c745c: stur            x4, [fp, #-0x68]
    // 0x7c7460: r0 = LoadClassIdInstr(r3)
    //     0x7c7460: ldur            x0, [x3, #-1]
    //     0x7c7464: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7468: mov             x1, x3
    // 0x7c746c: r2 = "last"
    //     0x7c746c: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a268] "last"
    //     0x7c7470: ldr             x2, [x2, #0x268]
    // 0x7c7474: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7474: sub             lr, x0, #0x114
    //     0x7c7478: ldr             lr, [x21, lr, lsl #3]
    //     0x7c747c: blr             lr
    // 0x7c7480: mov             x3, x0
    // 0x7c7484: r2 = Null
    //     0x7c7484: mov             x2, NULL
    // 0x7c7488: r1 = Null
    //     0x7c7488: mov             x1, NULL
    // 0x7c748c: stur            x3, [fp, #-0x80]
    // 0x7c7490: r4 = 60
    //     0x7c7490: movz            x4, #0x3c
    // 0x7c7494: branchIfSmi(r0, 0x7c74a0)
    //     0x7c7494: tbz             w0, #0, #0x7c74a0
    // 0x7c7498: r4 = LoadClassIdInstr(r0)
    //     0x7c7498: ldur            x4, [x0, #-1]
    //     0x7c749c: ubfx            x4, x4, #0xc, #0x14
    // 0x7c74a0: cmp             x4, #0x3f
    // 0x7c74a4: b.eq            #0x7c74b8
    // 0x7c74a8: r8 = bool?
    //     0x7c74a8: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7c74ac: r3 = Null
    //     0x7c74ac: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a270] Null
    //     0x7c74b0: ldr             x3, [x3, #0x270]
    // 0x7c74b4: r0 = bool?()
    //     0x7c74b4: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7c74b8: ldur            x3, [fp, #-8]
    // 0x7c74bc: r0 = LoadClassIdInstr(r3)
    //     0x7c74bc: ldur            x0, [x3, #-1]
    //     0x7c74c0: ubfx            x0, x0, #0xc, #0x14
    // 0x7c74c4: mov             x1, x3
    // 0x7c74c8: r2 = "bookmark"
    //     0x7c74c8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a0e8] "bookmark"
    //     0x7c74cc: ldr             x2, [x2, #0xe8]
    // 0x7c74d0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c74d0: sub             lr, x0, #0x114
    //     0x7c74d4: ldr             lr, [x21, lr, lsl #3]
    //     0x7c74d8: blr             lr
    // 0x7c74dc: cmp             w0, NULL
    // 0x7c74e0: b.ne            #0x7c74ec
    // 0x7c74e4: r4 = false
    //     0x7c74e4: add             x4, NULL, #0x30  ; false
    // 0x7c74e8: b               #0x7c754c
    // 0x7c74ec: ldur            x3, [fp, #-8]
    // 0x7c74f0: r0 = LoadClassIdInstr(r3)
    //     0x7c74f0: ldur            x0, [x3, #-1]
    //     0x7c74f4: ubfx            x0, x0, #0xc, #0x14
    // 0x7c74f8: mov             x1, x3
    // 0x7c74fc: r2 = "bookmark"
    //     0x7c74fc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a0e8] "bookmark"
    //     0x7c7500: ldr             x2, [x2, #0xe8]
    // 0x7c7504: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7504: sub             lr, x0, #0x114
    //     0x7c7508: ldr             lr, [x21, lr, lsl #3]
    //     0x7c750c: blr             lr
    // 0x7c7510: mov             x3, x0
    // 0x7c7514: r2 = Null
    //     0x7c7514: mov             x2, NULL
    // 0x7c7518: r1 = Null
    //     0x7c7518: mov             x1, NULL
    // 0x7c751c: stur            x3, [fp, #-0x88]
    // 0x7c7520: r4 = 60
    //     0x7c7520: movz            x4, #0x3c
    // 0x7c7524: branchIfSmi(r0, 0x7c7530)
    //     0x7c7524: tbz             w0, #0, #0x7c7530
    // 0x7c7528: r4 = LoadClassIdInstr(r0)
    //     0x7c7528: ldur            x4, [x0, #-1]
    //     0x7c752c: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7530: cmp             x4, #0x3f
    // 0x7c7534: b.eq            #0x7c7548
    // 0x7c7538: r8 = bool
    //     0x7c7538: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x7c753c: r3 = Null
    //     0x7c753c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a280] Null
    //     0x7c7540: ldr             x3, [x3, #0x280]
    // 0x7c7544: r0 = bool()
    //     0x7c7544: bl              #0xed4390  ; IsType_bool_Stub
    // 0x7c7548: ldur            x4, [fp, #-0x88]
    // 0x7c754c: ldur            x3, [fp, #-8]
    // 0x7c7550: stur            x4, [fp, #-0x88]
    // 0x7c7554: r0 = LoadClassIdInstr(r3)
    //     0x7c7554: ldur            x0, [x3, #-1]
    //     0x7c7558: ubfx            x0, x0, #0xc, #0x14
    // 0x7c755c: mov             x1, x3
    // 0x7c7560: r2 = "updatedAt"
    //     0x7c7560: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a0f0] "updatedAt"
    //     0x7c7564: ldr             x2, [x2, #0xf0]
    // 0x7c7568: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7568: sub             lr, x0, #0x114
    //     0x7c756c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7570: blr             lr
    // 0x7c7574: cmp             w0, NULL
    // 0x7c7578: b.eq            #0x7c75f4
    // 0x7c757c: ldur            x3, [fp, #-8]
    // 0x7c7580: r0 = LoadClassIdInstr(r3)
    //     0x7c7580: ldur            x0, [x3, #-1]
    //     0x7c7584: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7588: mov             x1, x3
    // 0x7c758c: r2 = "updatedAt"
    //     0x7c758c: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a0f0] "updatedAt"
    //     0x7c7590: ldr             x2, [x2, #0xf0]
    // 0x7c7594: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c7594: sub             lr, x0, #0x114
    //     0x7c7598: ldr             lr, [x21, lr, lsl #3]
    //     0x7c759c: blr             lr
    // 0x7c75a0: mov             x3, x0
    // 0x7c75a4: r2 = Null
    //     0x7c75a4: mov             x2, NULL
    // 0x7c75a8: r1 = Null
    //     0x7c75a8: mov             x1, NULL
    // 0x7c75ac: stur            x3, [fp, #-0x90]
    // 0x7c75b0: r4 = 60
    //     0x7c75b0: movz            x4, #0x3c
    // 0x7c75b4: branchIfSmi(r0, 0x7c75c0)
    //     0x7c75b4: tbz             w0, #0, #0x7c75c0
    // 0x7c75b8: r4 = LoadClassIdInstr(r0)
    //     0x7c75b8: ldur            x4, [x0, #-1]
    //     0x7c75bc: ubfx            x4, x4, #0xc, #0x14
    // 0x7c75c0: cmp             x4, #0x1ae
    // 0x7c75c4: b.eq            #0x7c75ec
    // 0x7c75c8: r17 = -7188
    //     0x7c75c8: movn            x17, #0x1c13
    // 0x7c75cc: add             x4, x4, x17
    // 0x7c75d0: cmp             x4, #1
    // 0x7c75d4: b.ls            #0x7c75ec
    // 0x7c75d8: r8 = DateTime
    //     0x7c75d8: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a290] Type: DateTime
    //     0x7c75dc: ldr             x8, [x8, #0x290]
    // 0x7c75e0: r3 = Null
    //     0x7c75e0: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a298] Null
    //     0x7c75e4: ldr             x3, [x3, #0x298]
    // 0x7c75e8: r0 = DateTime()
    //     0x7c75e8: bl              #0x615e38  ; IsType_DateTime_Stub
    // 0x7c75ec: ldur            x3, [fp, #-0x90]
    // 0x7c75f0: b               #0x7c7624
    // 0x7c75f4: r0 = DateTime()
    //     0x7c75f4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x7c75f8: mov             x1, x0
    // 0x7c75fc: r0 = false
    //     0x7c75fc: add             x0, NULL, #0x30  ; false
    // 0x7c7600: stur            x1, [fp, #-0x90]
    // 0x7c7604: StoreField: r1->field_13 = r0
    //     0x7c7604: stur            w0, [x1, #0x13]
    // 0x7c7608: r0 = _getCurrentMicros()
    //     0x7c7608: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x7c760c: r1 = LoadInt32Instr(r0)
    //     0x7c760c: sbfx            x1, x0, #1, #0x1f
    //     0x7c7610: tbz             w0, #0, #0x7c7618
    //     0x7c7614: ldur            x1, [x0, #7]
    // 0x7c7618: ldur            x0, [fp, #-0x90]
    // 0x7c761c: StoreField: r0->field_7 = r1
    //     0x7c761c: stur            x1, [x0, #7]
    // 0x7c7620: mov             x3, x0
    // 0x7c7624: ldur            x1, [fp, #-8]
    // 0x7c7628: stur            x3, [fp, #-0x90]
    // 0x7c762c: r0 = LoadClassIdInstr(r1)
    //     0x7c762c: ldur            x0, [x1, #-1]
    //     0x7c7630: ubfx            x0, x0, #0xc, #0x14
    // 0x7c7634: r2 = "tajweed"
    //     0x7c7634: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a2a8] "tajweed"
    //     0x7c7638: ldr             x2, [x2, #0x2a8]
    // 0x7c763c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7c763c: sub             lr, x0, #0x114
    //     0x7c7640: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7644: blr             lr
    // 0x7c7648: mov             x3, x0
    // 0x7c764c: r2 = Null
    //     0x7c764c: mov             x2, NULL
    // 0x7c7650: r1 = Null
    //     0x7c7650: mov             x1, NULL
    // 0x7c7654: stur            x3, [fp, #-8]
    // 0x7c7658: r4 = 60
    //     0x7c7658: movz            x4, #0x3c
    // 0x7c765c: branchIfSmi(r0, 0x7c7668)
    //     0x7c765c: tbz             w0, #0, #0x7c7668
    // 0x7c7660: r4 = LoadClassIdInstr(r0)
    //     0x7c7660: ldur            x4, [x0, #-1]
    //     0x7c7664: ubfx            x4, x4, #0xc, #0x14
    // 0x7c7668: sub             x4, x4, #0x5a
    // 0x7c766c: cmp             x4, #2
    // 0x7c7670: b.ls            #0x7c7688
    // 0x7c7674: r8 = List?
    //     0x7c7674: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7c7678: ldr             x8, [x8, #0x140]
    // 0x7c767c: r3 = Null
    //     0x7c767c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a2b0] Null
    //     0x7c7680: ldr             x3, [x3, #0x2b0]
    // 0x7c7684: r0 = List?()
    //     0x7c7684: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7c7688: ldur            x0, [fp, #-8]
    // 0x7c768c: cmp             w0, NULL
    // 0x7c7690: b.ne            #0x7c76a8
    // 0x7c7694: r1 = Null
    //     0x7c7694: mov             x1, NULL
    // 0x7c7698: r2 = 0
    //     0x7c7698: movz            x2, #0
    // 0x7c769c: r0 = _GrowableList()
    //     0x7c769c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7c76a0: mov             x25, x0
    // 0x7c76a4: b               #0x7c76ac
    // 0x7c76a8: mov             x25, x0
    // 0x7c76ac: ldur            x24, [fp, #-0x10]
    // 0x7c76b0: ldur            x23, [fp, #-0x20]
    // 0x7c76b4: ldur            x20, [fp, #-0x18]
    // 0x7c76b8: ldur            x19, [fp, #-0x28]
    // 0x7c76bc: ldur            x14, [fp, #-0x30]
    // 0x7c76c0: ldur            x13, [fp, #-0x38]
    // 0x7c76c4: ldur            x12, [fp, #-0x40]
    // 0x7c76c8: ldur            x11, [fp, #-0x48]
    // 0x7c76cc: ldur            x10, [fp, #-0x50]
    // 0x7c76d0: ldur            x9, [fp, #-0x58]
    // 0x7c76d4: ldur            x8, [fp, #-0x60]
    // 0x7c76d8: ldur            x7, [fp, #-0x70]
    // 0x7c76dc: ldur            x6, [fp, #-0x78]
    // 0x7c76e0: ldur            x5, [fp, #-0x68]
    // 0x7c76e4: ldur            x4, [fp, #-0x80]
    // 0x7c76e8: ldur            x3, [fp, #-0x88]
    // 0x7c76ec: ldur            x0, [fp, #-0x90]
    // 0x7c76f0: stur            x25, [fp, #-8]
    // 0x7c76f4: r1 = Function '<anonymous closure>': static.
    //     0x7c76f4: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a2c0] AnonymousClosure: static (0x7c7840), in [package:nuonline/app/data/models/verse.dart] Verse::Verse.fromMap (0x7c6bb0)
    //     0x7c76f8: ldr             x1, [x1, #0x2c0]
    // 0x7c76fc: r2 = Null
    //     0x7c76fc: mov             x2, NULL
    // 0x7c7700: r0 = AllocateClosure()
    //     0x7c7700: bl              #0xec1630  ; AllocateClosureStub
    // 0x7c7704: mov             x1, x0
    // 0x7c7708: ldur            x0, [fp, #-8]
    // 0x7c770c: r2 = LoadClassIdInstr(r0)
    //     0x7c770c: ldur            x2, [x0, #-1]
    //     0x7c7710: ubfx            x2, x2, #0xc, #0x14
    // 0x7c7714: r16 = <Tajweed>
    //     0x7c7714: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f8] TypeArguments: <Tajweed>
    //     0x7c7718: ldr             x16, [x16, #0x1f8]
    // 0x7c771c: stp             x0, x16, [SP, #8]
    // 0x7c7720: str             x1, [SP]
    // 0x7c7724: mov             x0, x2
    // 0x7c7728: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7c7728: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7c772c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7c772c: movz            x17, #0xf28c
    //     0x7c7730: add             lr, x0, x17
    //     0x7c7734: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7738: blr             lr
    // 0x7c773c: r1 = LoadClassIdInstr(r0)
    //     0x7c773c: ldur            x1, [x0, #-1]
    //     0x7c7740: ubfx            x1, x1, #0xc, #0x14
    // 0x7c7744: mov             x16, x0
    // 0x7c7748: mov             x0, x1
    // 0x7c774c: mov             x1, x16
    // 0x7c7750: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7c7750: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7c7754: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7c7754: movz            x17, #0xd889
    //     0x7c7758: add             lr, x0, x17
    //     0x7c775c: ldr             lr, [x21, lr, lsl #3]
    //     0x7c7760: blr             lr
    // 0x7c7764: mov             x1, x0
    // 0x7c7768: ldur            x0, [fp, #-0x10]
    // 0x7c776c: stur            x1, [fp, #-8]
    // 0x7c7770: r2 = LoadInt32Instr(r0)
    //     0x7c7770: sbfx            x2, x0, #1, #0x1f
    //     0x7c7774: tbz             w0, #0, #0x7c777c
    //     0x7c7778: ldur            x2, [x0, #7]
    // 0x7c777c: stur            x2, [fp, #-0x98]
    // 0x7c7780: r0 = Verse()
    //     0x7c7780: bl              #0x7c7834  ; AllocateVerseStub -> Verse (size=0x64)
    // 0x7c7784: ldur            x1, [fp, #-0x98]
    // 0x7c7788: StoreField: r0->field_7 = r1
    //     0x7c7788: stur            x1, [x0, #7]
    // 0x7c778c: ldur            x1, [fp, #-0x20]
    // 0x7c7790: StoreField: r0->field_f = r1
    //     0x7c7790: stur            x1, [x0, #0xf]
    // 0x7c7794: ldur            x1, [fp, #-0x18]
    // 0x7c7798: r2 = LoadInt32Instr(r1)
    //     0x7c7798: sbfx            x2, x1, #1, #0x1f
    //     0x7c779c: tbz             w1, #0, #0x7c77a4
    //     0x7c77a0: ldur            x2, [x1, #7]
    // 0x7c77a4: ArrayStore: r0[0] = r2  ; List_8
    //     0x7c77a4: stur            x2, [x0, #0x17]
    // 0x7c77a8: ldur            x1, [fp, #-0x28]
    // 0x7c77ac: StoreField: r0->field_1f = r1
    //     0x7c77ac: stur            w1, [x0, #0x1f]
    // 0x7c77b0: ldur            x1, [fp, #-0x30]
    // 0x7c77b4: StoreField: r0->field_23 = r1
    //     0x7c77b4: stur            w1, [x0, #0x23]
    // 0x7c77b8: ldur            x1, [fp, #-0x38]
    // 0x7c77bc: StoreField: r0->field_4b = r1
    //     0x7c77bc: stur            w1, [x0, #0x4b]
    // 0x7c77c0: ldur            x1, [fp, #-0x40]
    // 0x7c77c4: StoreField: r0->field_4f = r1
    //     0x7c77c4: stur            w1, [x0, #0x4f]
    // 0x7c77c8: ldur            x1, [fp, #-0x48]
    // 0x7c77cc: StoreField: r0->field_53 = r1
    //     0x7c77cc: stur            w1, [x0, #0x53]
    // 0x7c77d0: ldur            x1, [fp, #-0x50]
    // 0x7c77d4: StoreField: r0->field_57 = r1
    //     0x7c77d4: stur            w1, [x0, #0x57]
    // 0x7c77d8: ldur            x1, [fp, #-0x58]
    // 0x7c77dc: StoreField: r0->field_5b = r1
    //     0x7c77dc: stur            w1, [x0, #0x5b]
    // 0x7c77e0: ldur            x1, [fp, #-0x60]
    // 0x7c77e4: StoreField: r0->field_27 = r1
    //     0x7c77e4: stur            w1, [x0, #0x27]
    // 0x7c77e8: ldur            x1, [fp, #-0x70]
    // 0x7c77ec: StoreField: r0->field_2b = r1
    //     0x7c77ec: stur            x1, [x0, #0x2b]
    // 0x7c77f0: ldur            x1, [fp, #-0x78]
    // 0x7c77f4: StoreField: r0->field_33 = r1
    //     0x7c77f4: stur            x1, [x0, #0x33]
    // 0x7c77f8: ldur            x1, [fp, #-0x68]
    // 0x7c77fc: StoreField: r0->field_3b = r1
    //     0x7c77fc: stur            w1, [x0, #0x3b]
    // 0x7c7800: ldur            x1, [fp, #-0x80]
    // 0x7c7804: StoreField: r0->field_3f = r1
    //     0x7c7804: stur            w1, [x0, #0x3f]
    // 0x7c7808: ldur            x1, [fp, #-0x88]
    // 0x7c780c: StoreField: r0->field_43 = r1
    //     0x7c780c: stur            w1, [x0, #0x43]
    // 0x7c7810: ldur            x1, [fp, #-0x90]
    // 0x7c7814: StoreField: r0->field_47 = r1
    //     0x7c7814: stur            w1, [x0, #0x47]
    // 0x7c7818: ldur            x1, [fp, #-8]
    // 0x7c781c: StoreField: r0->field_5f = r1
    //     0x7c781c: stur            w1, [x0, #0x5f]
    // 0x7c7820: LeaveFrame
    //     0x7c7820: mov             SP, fp
    //     0x7c7824: ldp             fp, lr, [SP], #0x10
    // 0x7c7828: ret
    //     0x7c7828: ret             
    // 0x7c782c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c782c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c7830: b               #0x7c6bd0
  }
  [closure] static Tajweed <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7c7840, size: 0x50
    // 0x7c7840: EnterFrame
    //     0x7c7840: stp             fp, lr, [SP, #-0x10]!
    //     0x7c7844: mov             fp, SP
    // 0x7c7848: CheckStackOverflow
    //     0x7c7848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c784c: cmp             SP, x16
    //     0x7c7850: b.ls            #0x7c7888
    // 0x7c7854: ldr             x0, [fp, #0x10]
    // 0x7c7858: r2 = Null
    //     0x7c7858: mov             x2, NULL
    // 0x7c785c: r1 = Null
    //     0x7c785c: mov             x1, NULL
    // 0x7c7860: r8 = Map<String, dynamic>
    //     0x7c7860: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7c7864: r3 = Null
    //     0x7c7864: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1a2c8] Null
    //     0x7c7868: ldr             x3, [x3, #0x2c8]
    // 0x7c786c: r0 = Map<String, dynamic>()
    //     0x7c786c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7c7870: ldr             x2, [fp, #0x10]
    // 0x7c7874: r1 = Null
    //     0x7c7874: mov             x1, NULL
    // 0x7c7878: r0 = Tajweed.fromMap()
    //     0x7c7878: bl              #0x7c7890  ; [package:nuonline/app/data/models/tajweed.dart] Tajweed::Tajweed.fromMap
    // 0x7c787c: LeaveFrame
    //     0x7c787c: mov             SP, fp
    //     0x7c7880: ldp             fp, lr, [SP], #0x10
    // 0x7c7884: ret
    //     0x7c7884: ret             
    // 0x7c7888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c7888: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c788c: b               #0x7c7854
  }
  _ copyWith(/* No info */) {
    // ** addr: 0xb220fc, size: 0x270
    // 0xb220fc: EnterFrame
    //     0xb220fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb22100: mov             fp, SP
    // 0xb22104: AllocStack(0x90)
    //     0xb22104: sub             SP, SP, #0x90
    // 0xb22108: SetupParameters({dynamic bookmark = Null /* r3 */, dynamic updatedAt = Null /* r5 */, dynamic value = Null /* r0 */})
    //     0xb22108: ldur            w0, [x4, #0x13]
    //     0xb2210c: ldur            w2, [x4, #0x1f]
    //     0xb22110: add             x2, x2, HEAP, lsl #32
    //     0xb22114: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a0e8] "bookmark"
    //     0xb22118: ldr             x16, [x16, #0xe8]
    //     0xb2211c: cmp             w2, w16
    //     0xb22120: b.ne            #0xb22144
    //     0xb22124: ldur            w2, [x4, #0x23]
    //     0xb22128: add             x2, x2, HEAP, lsl #32
    //     0xb2212c: sub             w3, w0, w2
    //     0xb22130: add             x2, fp, w3, sxtw #2
    //     0xb22134: ldr             x2, [x2, #8]
    //     0xb22138: mov             x3, x2
    //     0xb2213c: movz            x2, #0x1
    //     0xb22140: b               #0xb2214c
    //     0xb22144: mov             x3, NULL
    //     0xb22148: movz            x2, #0
    //     0xb2214c: lsl             x5, x2, #1
    //     0xb22150: lsl             w6, w5, #1
    //     0xb22154: add             w7, w6, #8
    //     0xb22158: add             x16, x4, w7, sxtw #1
    //     0xb2215c: ldur            w8, [x16, #0xf]
    //     0xb22160: add             x8, x8, HEAP, lsl #32
    //     0xb22164: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a0f0] "updatedAt"
    //     0xb22168: ldr             x16, [x16, #0xf0]
    //     0xb2216c: cmp             w8, w16
    //     0xb22170: b.ne            #0xb221a4
    //     0xb22174: add             w2, w6, #0xa
    //     0xb22178: add             x16, x4, w2, sxtw #1
    //     0xb2217c: ldur            w6, [x16, #0xf]
    //     0xb22180: add             x6, x6, HEAP, lsl #32
    //     0xb22184: sub             w2, w0, w6
    //     0xb22188: add             x6, fp, w2, sxtw #2
    //     0xb2218c: ldr             x6, [x6, #8]
    //     0xb22190: add             w2, w5, #2
    //     0xb22194: sbfx            x5, x2, #1, #0x1f
    //     0xb22198: mov             x2, x5
    //     0xb2219c: mov             x5, x6
    //     0xb221a0: b               #0xb221a8
    //     0xb221a4: mov             x5, NULL
    //     0xb221a8: lsl             x6, x2, #1
    //     0xb221ac: lsl             w2, w6, #1
    //     0xb221b0: add             w6, w2, #8
    //     0xb221b4: add             x16, x4, w6, sxtw #1
    //     0xb221b8: ldur            w7, [x16, #0xf]
    //     0xb221bc: add             x7, x7, HEAP, lsl #32
    //     0xb221c0: ldr             x16, [PP, #0x4dc0]  ; [pp+0x4dc0] "value"
    //     0xb221c4: cmp             w7, w16
    //     0xb221c8: b.ne            #0xb221ec
    //     0xb221cc: add             w6, w2, #0xa
    //     0xb221d0: add             x16, x4, w6, sxtw #1
    //     0xb221d4: ldur            w2, [x16, #0xf]
    //     0xb221d8: add             x2, x2, HEAP, lsl #32
    //     0xb221dc: sub             w4, w0, w2
    //     0xb221e0: add             x0, fp, w4, sxtw #2
    //     0xb221e4: ldr             x0, [x0, #8]
    //     0xb221e8: b               #0xb221f0
    //     0xb221ec: mov             x0, NULL
    // 0xb221f0: LoadField: r2 = r1->field_7
    //     0xb221f0: ldur            x2, [x1, #7]
    // 0xb221f4: stur            x2, [fp, #-0x90]
    // 0xb221f8: LoadField: r4 = r1->field_f
    //     0xb221f8: ldur            x4, [x1, #0xf]
    // 0xb221fc: stur            x4, [fp, #-0x88]
    // 0xb22200: ArrayLoad: r6 = r1[0]  ; List_8
    //     0xb22200: ldur            x6, [x1, #0x17]
    // 0xb22204: stur            x6, [fp, #-0x80]
    // 0xb22208: cmp             w0, NULL
    // 0xb2220c: b.ne            #0xb22218
    // 0xb22210: LoadField: r0 = r1->field_1f
    //     0xb22210: ldur            w0, [x1, #0x1f]
    // 0xb22214: DecompressPointer r0
    //     0xb22214: add             x0, x0, HEAP, lsl #32
    // 0xb22218: stur            x0, [fp, #-0x78]
    // 0xb2221c: LoadField: r7 = r1->field_23
    //     0xb2221c: ldur            w7, [x1, #0x23]
    // 0xb22220: DecompressPointer r7
    //     0xb22220: add             x7, x7, HEAP, lsl #32
    // 0xb22224: stur            x7, [fp, #-0x70]
    // 0xb22228: LoadField: r8 = r1->field_27
    //     0xb22228: ldur            w8, [x1, #0x27]
    // 0xb2222c: DecompressPointer r8
    //     0xb2222c: add             x8, x8, HEAP, lsl #32
    // 0xb22230: stur            x8, [fp, #-0x68]
    // 0xb22234: LoadField: r9 = r1->field_2b
    //     0xb22234: ldur            x9, [x1, #0x2b]
    // 0xb22238: stur            x9, [fp, #-0x60]
    // 0xb2223c: LoadField: r10 = r1->field_33
    //     0xb2223c: ldur            x10, [x1, #0x33]
    // 0xb22240: stur            x10, [fp, #-0x58]
    // 0xb22244: LoadField: r11 = r1->field_3b
    //     0xb22244: ldur            w11, [x1, #0x3b]
    // 0xb22248: DecompressPointer r11
    //     0xb22248: add             x11, x11, HEAP, lsl #32
    // 0xb2224c: stur            x11, [fp, #-0x50]
    // 0xb22250: LoadField: r12 = r1->field_3f
    //     0xb22250: ldur            w12, [x1, #0x3f]
    // 0xb22254: DecompressPointer r12
    //     0xb22254: add             x12, x12, HEAP, lsl #32
    // 0xb22258: stur            x12, [fp, #-0x48]
    // 0xb2225c: cmp             w3, NULL
    // 0xb22260: b.ne            #0xb2226c
    // 0xb22264: LoadField: r3 = r1->field_43
    //     0xb22264: ldur            w3, [x1, #0x43]
    // 0xb22268: DecompressPointer r3
    //     0xb22268: add             x3, x3, HEAP, lsl #32
    // 0xb2226c: stur            x3, [fp, #-0x40]
    // 0xb22270: cmp             w5, NULL
    // 0xb22274: b.ne            #0xb22280
    // 0xb22278: LoadField: r5 = r1->field_47
    //     0xb22278: ldur            w5, [x1, #0x47]
    // 0xb2227c: DecompressPointer r5
    //     0xb2227c: add             x5, x5, HEAP, lsl #32
    // 0xb22280: stur            x5, [fp, #-0x38]
    // 0xb22284: LoadField: r13 = r1->field_4b
    //     0xb22284: ldur            w13, [x1, #0x4b]
    // 0xb22288: DecompressPointer r13
    //     0xb22288: add             x13, x13, HEAP, lsl #32
    // 0xb2228c: stur            x13, [fp, #-0x30]
    // 0xb22290: LoadField: r14 = r1->field_4f
    //     0xb22290: ldur            w14, [x1, #0x4f]
    // 0xb22294: DecompressPointer r14
    //     0xb22294: add             x14, x14, HEAP, lsl #32
    // 0xb22298: stur            x14, [fp, #-0x28]
    // 0xb2229c: LoadField: r19 = r1->field_53
    //     0xb2229c: ldur            w19, [x1, #0x53]
    // 0xb222a0: DecompressPointer r19
    //     0xb222a0: add             x19, x19, HEAP, lsl #32
    // 0xb222a4: stur            x19, [fp, #-0x20]
    // 0xb222a8: LoadField: r20 = r1->field_57
    //     0xb222a8: ldur            w20, [x1, #0x57]
    // 0xb222ac: DecompressPointer r20
    //     0xb222ac: add             x20, x20, HEAP, lsl #32
    // 0xb222b0: stur            x20, [fp, #-0x18]
    // 0xb222b4: LoadField: r23 = r1->field_5b
    //     0xb222b4: ldur            w23, [x1, #0x5b]
    // 0xb222b8: DecompressPointer r23
    //     0xb222b8: add             x23, x23, HEAP, lsl #32
    // 0xb222bc: stur            x23, [fp, #-0x10]
    // 0xb222c0: LoadField: r24 = r1->field_5f
    //     0xb222c0: ldur            w24, [x1, #0x5f]
    // 0xb222c4: DecompressPointer r24
    //     0xb222c4: add             x24, x24, HEAP, lsl #32
    // 0xb222c8: stur            x24, [fp, #-8]
    // 0xb222cc: r0 = Verse()
    //     0xb222cc: bl              #0x7c7834  ; AllocateVerseStub -> Verse (size=0x64)
    // 0xb222d0: ldur            x1, [fp, #-0x90]
    // 0xb222d4: StoreField: r0->field_7 = r1
    //     0xb222d4: stur            x1, [x0, #7]
    // 0xb222d8: ldur            x1, [fp, #-0x88]
    // 0xb222dc: StoreField: r0->field_f = r1
    //     0xb222dc: stur            x1, [x0, #0xf]
    // 0xb222e0: ldur            x1, [fp, #-0x80]
    // 0xb222e4: ArrayStore: r0[0] = r1  ; List_8
    //     0xb222e4: stur            x1, [x0, #0x17]
    // 0xb222e8: ldur            x1, [fp, #-0x78]
    // 0xb222ec: StoreField: r0->field_1f = r1
    //     0xb222ec: stur            w1, [x0, #0x1f]
    // 0xb222f0: ldur            x1, [fp, #-0x70]
    // 0xb222f4: StoreField: r0->field_23 = r1
    //     0xb222f4: stur            w1, [x0, #0x23]
    // 0xb222f8: ldur            x1, [fp, #-0x30]
    // 0xb222fc: StoreField: r0->field_4b = r1
    //     0xb222fc: stur            w1, [x0, #0x4b]
    // 0xb22300: ldur            x1, [fp, #-0x28]
    // 0xb22304: StoreField: r0->field_4f = r1
    //     0xb22304: stur            w1, [x0, #0x4f]
    // 0xb22308: ldur            x1, [fp, #-0x20]
    // 0xb2230c: StoreField: r0->field_53 = r1
    //     0xb2230c: stur            w1, [x0, #0x53]
    // 0xb22310: ldur            x1, [fp, #-0x18]
    // 0xb22314: StoreField: r0->field_57 = r1
    //     0xb22314: stur            w1, [x0, #0x57]
    // 0xb22318: ldur            x1, [fp, #-0x10]
    // 0xb2231c: StoreField: r0->field_5b = r1
    //     0xb2231c: stur            w1, [x0, #0x5b]
    // 0xb22320: ldur            x1, [fp, #-0x68]
    // 0xb22324: StoreField: r0->field_27 = r1
    //     0xb22324: stur            w1, [x0, #0x27]
    // 0xb22328: ldur            x1, [fp, #-0x60]
    // 0xb2232c: StoreField: r0->field_2b = r1
    //     0xb2232c: stur            x1, [x0, #0x2b]
    // 0xb22330: ldur            x1, [fp, #-0x58]
    // 0xb22334: StoreField: r0->field_33 = r1
    //     0xb22334: stur            x1, [x0, #0x33]
    // 0xb22338: ldur            x1, [fp, #-0x50]
    // 0xb2233c: StoreField: r0->field_3b = r1
    //     0xb2233c: stur            w1, [x0, #0x3b]
    // 0xb22340: ldur            x1, [fp, #-0x48]
    // 0xb22344: StoreField: r0->field_3f = r1
    //     0xb22344: stur            w1, [x0, #0x3f]
    // 0xb22348: ldur            x1, [fp, #-0x40]
    // 0xb2234c: StoreField: r0->field_43 = r1
    //     0xb2234c: stur            w1, [x0, #0x43]
    // 0xb22350: ldur            x1, [fp, #-0x38]
    // 0xb22354: StoreField: r0->field_47 = r1
    //     0xb22354: stur            w1, [x0, #0x47]
    // 0xb22358: ldur            x1, [fp, #-8]
    // 0xb2235c: StoreField: r0->field_5f = r1
    //     0xb2235c: stur            w1, [x0, #0x5f]
    // 0xb22360: LeaveFrame
    //     0xb22360: mov             SP, fp
    //     0xb22364: ldp             fp, lr, [SP], #0x10
    // 0xb22368: ret
    //     0xb22368: ret             
  }
  String numberArabic(Verse) {
    // ** addr: 0xb325f0, size: 0xa0
    // 0xb325f0: EnterFrame
    //     0xb325f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb325f4: mov             fp, SP
    // 0xb325f8: AllocStack(0x18)
    //     0xb325f8: sub             SP, SP, #0x18
    // 0xb325fc: SetupParameters(Verse this /* r1 => r0, fp-0x8 */)
    //     0xb325fc: mov             x0, x1
    //     0xb32600: stur            x1, [fp, #-8]
    // 0xb32604: CheckStackOverflow
    //     0xb32604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32608: cmp             SP, x16
    //     0xb3260c: b.ls            #0xb32688
    // 0xb32610: r1 = Null
    //     0xb32610: mov             x1, NULL
    // 0xb32614: r2 = 6
    //     0xb32614: movz            x2, #0x6
    // 0xb32618: r0 = AllocateArray()
    //     0xb32618: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb3261c: stur            x0, [fp, #-0x10]
    // 0xb32620: r16 = "‮ "
    //     0xb32620: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b5f8] "‮ "
    //     0xb32624: ldr             x16, [x16, #0x5f8]
    // 0xb32628: StoreField: r0->field_f = r16
    //     0xb32628: stur            w16, [x0, #0xf]
    // 0xb3262c: ldur            x1, [fp, #-8]
    // 0xb32630: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xb32630: ldur            x2, [x1, #0x17]
    // 0xb32634: mov             x1, x2
    // 0xb32638: r0 = _extension#1.toArabic()
    //     0xb32638: bl              #0xb32690  ; [package:nuonline/app/data/models/verse.dart] ::_extension#1.toArabic
    // 0xb3263c: ldur            x1, [fp, #-0x10]
    // 0xb32640: ArrayStore: r1[1] = r0  ; List_4
    //     0xb32640: add             x25, x1, #0x13
    //     0xb32644: str             w0, [x25]
    //     0xb32648: tbz             w0, #0, #0xb32664
    //     0xb3264c: ldurb           w16, [x1, #-1]
    //     0xb32650: ldurb           w17, [x0, #-1]
    //     0xb32654: and             x16, x17, x16, lsr #2
    //     0xb32658: tst             x16, HEAP, lsr #32
    //     0xb3265c: b.eq            #0xb32664
    //     0xb32660: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb32664: ldur            x0, [fp, #-0x10]
    // 0xb32668: r16 = "۝"
    //     0xb32668: add             x16, PP, #0x20, lsl #12  ; [pp+0x20b20] "۝"
    //     0xb3266c: ldr             x16, [x16, #0xb20]
    // 0xb32670: ArrayStore: r0[0] = r16  ; List_4
    //     0xb32670: stur            w16, [x0, #0x17]
    // 0xb32674: str             x0, [SP]
    // 0xb32678: r0 = _interpolate()
    //     0xb32678: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb3267c: LeaveFrame
    //     0xb3267c: mov             SP, fp
    //     0xb32680: ldp             fp, lr, [SP], #0x10
    // 0xb32684: ret
    //     0xb32684: ret             
    // 0xb32688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3268c: b               #0xb32610
  }
  String arabicNumberLeft(Verse) {
    // ** addr: 0xba8d70, size: 0x68
    // 0xba8d70: EnterFrame
    //     0xba8d70: stp             fp, lr, [SP, #-0x10]!
    //     0xba8d74: mov             fp, SP
    // 0xba8d78: AllocStack(0x10)
    //     0xba8d78: sub             SP, SP, #0x10
    // 0xba8d7c: CheckStackOverflow
    //     0xba8d7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba8d80: cmp             SP, x16
    //     0xba8d84: b.ls            #0xba8dd0
    // 0xba8d88: ArrayLoad: r0 = r1[0]  ; List_8
    //     0xba8d88: ldur            x0, [x1, #0x17]
    // 0xba8d8c: mov             x1, x0
    // 0xba8d90: r0 = _extension#1.toArabic()
    //     0xba8d90: bl              #0xb32690  ; [package:nuonline/app/data/models/verse.dart] ::_extension#1.toArabic
    // 0xba8d94: r1 = Null
    //     0xba8d94: mov             x1, NULL
    // 0xba8d98: r2 = 4
    //     0xba8d98: movz            x2, #0x4
    // 0xba8d9c: stur            x0, [fp, #-8]
    // 0xba8da0: r0 = AllocateArray()
    //     0xba8da0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xba8da4: mov             x1, x0
    // 0xba8da8: ldur            x0, [fp, #-8]
    // 0xba8dac: StoreField: r1->field_f = r0
    //     0xba8dac: stur            w0, [x1, #0xf]
    // 0xba8db0: r16 = "۝"
    //     0xba8db0: add             x16, PP, #0x20, lsl #12  ; [pp+0x20b20] "۝"
    //     0xba8db4: ldr             x16, [x16, #0xb20]
    // 0xba8db8: StoreField: r1->field_13 = r16
    //     0xba8db8: stur            w16, [x1, #0x13]
    // 0xba8dbc: str             x1, [SP]
    // 0xba8dc0: r0 = _interpolate()
    //     0xba8dc0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xba8dc4: LeaveFrame
    //     0xba8dc4: mov             SP, fp
    //     0xba8dc8: ldp             fp, lr, [SP], #0x10
    // 0xba8dcc: ret
    //     0xba8dcc: ret             
    // 0xba8dd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba8dd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba8dd4: b               #0xba8d88
  }
  _ tranlationFromLanguage(/* No info */) {
    // ** addr: 0xbaa6a0, size: 0x208
    // 0xbaa6a0: EnterFrame
    //     0xbaa6a0: stp             fp, lr, [SP, #-0x10]!
    //     0xbaa6a4: mov             fp, SP
    // 0xbaa6a8: AllocStack(0x8)
    //     0xbaa6a8: sub             SP, SP, #8
    // 0xbaa6ac: CheckStackOverflow
    //     0xbaa6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaa6b0: cmp             SP, x16
    //     0xbaa6b4: b.ls            #0xbaa8a0
    // 0xbaa6b8: r16 = Instance_QuranTranslationLanguage
    //     0xbaa6b8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33378] Obj!QuranTranslationLanguage@e302e1
    //     0xbaa6bc: ldr             x16, [x16, #0x378]
    // 0xbaa6c0: cmp             w2, w16
    // 0xbaa6c4: b.ne            #0xbaa700
    // 0xbaa6c8: LoadField: r0 = r1->field_23
    //     0xbaa6c8: ldur            w0, [x1, #0x23]
    // 0xbaa6cc: DecompressPointer r0
    //     0xbaa6cc: add             x0, x0, HEAP, lsl #32
    // 0xbaa6d0: r1 = LoadClassIdInstr(r0)
    //     0xbaa6d0: ldur            x1, [x0, #-1]
    //     0xbaa6d4: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa6d8: str             x0, [SP]
    // 0xbaa6dc: mov             x0, x1
    // 0xbaa6e0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa6e0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa6e4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa6e4: movz            x17, #0x2b03
    //     0xbaa6e8: add             lr, x0, x17
    //     0xbaa6ec: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa6f0: blr             lr
    // 0xbaa6f4: LeaveFrame
    //     0xbaa6f4: mov             SP, fp
    //     0xbaa6f8: ldp             fp, lr, [SP], #0x10
    // 0xbaa6fc: ret
    //     0xbaa6fc: ret             
    // 0xbaa700: r16 = Instance_QuranTranslationLanguage
    //     0xbaa700: add             x16, PP, #0x33, lsl #12  ; [pp+0x33380] Obj!QuranTranslationLanguage@e30381
    //     0xbaa704: ldr             x16, [x16, #0x380]
    // 0xbaa708: cmp             w2, w16
    // 0xbaa70c: b.ne            #0xbaa748
    // 0xbaa710: LoadField: r0 = r1->field_4b
    //     0xbaa710: ldur            w0, [x1, #0x4b]
    // 0xbaa714: DecompressPointer r0
    //     0xbaa714: add             x0, x0, HEAP, lsl #32
    // 0xbaa718: r1 = LoadClassIdInstr(r0)
    //     0xbaa718: ldur            x1, [x0, #-1]
    //     0xbaa71c: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa720: str             x0, [SP]
    // 0xbaa724: mov             x0, x1
    // 0xbaa728: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa728: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa72c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa72c: movz            x17, #0x2b03
    //     0xbaa730: add             lr, x0, x17
    //     0xbaa734: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa738: blr             lr
    // 0xbaa73c: LeaveFrame
    //     0xbaa73c: mov             SP, fp
    //     0xbaa740: ldp             fp, lr, [SP], #0x10
    // 0xbaa744: ret
    //     0xbaa744: ret             
    // 0xbaa748: r16 = Instance_QuranTranslationLanguage
    //     0xbaa748: add             x16, PP, #0x33, lsl #12  ; [pp+0x33388] Obj!QuranTranslationLanguage@e30361
    //     0xbaa74c: ldr             x16, [x16, #0x388]
    // 0xbaa750: cmp             w2, w16
    // 0xbaa754: b.ne            #0xbaa790
    // 0xbaa758: LoadField: r0 = r1->field_4f
    //     0xbaa758: ldur            w0, [x1, #0x4f]
    // 0xbaa75c: DecompressPointer r0
    //     0xbaa75c: add             x0, x0, HEAP, lsl #32
    // 0xbaa760: r1 = LoadClassIdInstr(r0)
    //     0xbaa760: ldur            x1, [x0, #-1]
    //     0xbaa764: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa768: str             x0, [SP]
    // 0xbaa76c: mov             x0, x1
    // 0xbaa770: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa770: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa774: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa774: movz            x17, #0x2b03
    //     0xbaa778: add             lr, x0, x17
    //     0xbaa77c: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa780: blr             lr
    // 0xbaa784: LeaveFrame
    //     0xbaa784: mov             SP, fp
    //     0xbaa788: ldp             fp, lr, [SP], #0x10
    // 0xbaa78c: ret
    //     0xbaa78c: ret             
    // 0xbaa790: r16 = Instance_QuranTranslationLanguage
    //     0xbaa790: add             x16, PP, #0x33, lsl #12  ; [pp+0x33390] Obj!QuranTranslationLanguage@e30341
    //     0xbaa794: ldr             x16, [x16, #0x390]
    // 0xbaa798: cmp             w2, w16
    // 0xbaa79c: b.ne            #0xbaa7d8
    // 0xbaa7a0: LoadField: r0 = r1->field_53
    //     0xbaa7a0: ldur            w0, [x1, #0x53]
    // 0xbaa7a4: DecompressPointer r0
    //     0xbaa7a4: add             x0, x0, HEAP, lsl #32
    // 0xbaa7a8: r1 = LoadClassIdInstr(r0)
    //     0xbaa7a8: ldur            x1, [x0, #-1]
    //     0xbaa7ac: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa7b0: str             x0, [SP]
    // 0xbaa7b4: mov             x0, x1
    // 0xbaa7b8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa7b8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa7bc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa7bc: movz            x17, #0x2b03
    //     0xbaa7c0: add             lr, x0, x17
    //     0xbaa7c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa7c8: blr             lr
    // 0xbaa7cc: LeaveFrame
    //     0xbaa7cc: mov             SP, fp
    //     0xbaa7d0: ldp             fp, lr, [SP], #0x10
    // 0xbaa7d4: ret
    //     0xbaa7d4: ret             
    // 0xbaa7d8: r16 = Instance_QuranTranslationLanguage
    //     0xbaa7d8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33398] Obj!QuranTranslationLanguage@e30321
    //     0xbaa7dc: ldr             x16, [x16, #0x398]
    // 0xbaa7e0: cmp             w2, w16
    // 0xbaa7e4: b.ne            #0xbaa820
    // 0xbaa7e8: LoadField: r0 = r1->field_57
    //     0xbaa7e8: ldur            w0, [x1, #0x57]
    // 0xbaa7ec: DecompressPointer r0
    //     0xbaa7ec: add             x0, x0, HEAP, lsl #32
    // 0xbaa7f0: r1 = LoadClassIdInstr(r0)
    //     0xbaa7f0: ldur            x1, [x0, #-1]
    //     0xbaa7f4: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa7f8: str             x0, [SP]
    // 0xbaa7fc: mov             x0, x1
    // 0xbaa800: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa800: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa804: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa804: movz            x17, #0x2b03
    //     0xbaa808: add             lr, x0, x17
    //     0xbaa80c: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa810: blr             lr
    // 0xbaa814: LeaveFrame
    //     0xbaa814: mov             SP, fp
    //     0xbaa818: ldp             fp, lr, [SP], #0x10
    // 0xbaa81c: ret
    //     0xbaa81c: ret             
    // 0xbaa820: r16 = Instance_QuranTranslationLanguage
    //     0xbaa820: add             x16, PP, #0x33, lsl #12  ; [pp+0x333a0] Obj!QuranTranslationLanguage@e30301
    //     0xbaa824: ldr             x16, [x16, #0x3a0]
    // 0xbaa828: cmp             w2, w16
    // 0xbaa82c: b.ne            #0xbaa868
    // 0xbaa830: LoadField: r0 = r1->field_5b
    //     0xbaa830: ldur            w0, [x1, #0x5b]
    // 0xbaa834: DecompressPointer r0
    //     0xbaa834: add             x0, x0, HEAP, lsl #32
    // 0xbaa838: r1 = LoadClassIdInstr(r0)
    //     0xbaa838: ldur            x1, [x0, #-1]
    //     0xbaa83c: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa840: str             x0, [SP]
    // 0xbaa844: mov             x0, x1
    // 0xbaa848: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa848: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa84c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa84c: movz            x17, #0x2b03
    //     0xbaa850: add             lr, x0, x17
    //     0xbaa854: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa858: blr             lr
    // 0xbaa85c: LeaveFrame
    //     0xbaa85c: mov             SP, fp
    //     0xbaa860: ldp             fp, lr, [SP], #0x10
    // 0xbaa864: ret
    //     0xbaa864: ret             
    // 0xbaa868: LoadField: r0 = r1->field_23
    //     0xbaa868: ldur            w0, [x1, #0x23]
    // 0xbaa86c: DecompressPointer r0
    //     0xbaa86c: add             x0, x0, HEAP, lsl #32
    // 0xbaa870: r1 = LoadClassIdInstr(r0)
    //     0xbaa870: ldur            x1, [x0, #-1]
    //     0xbaa874: ubfx            x1, x1, #0xc, #0x14
    // 0xbaa878: str             x0, [SP]
    // 0xbaa87c: mov             x0, x1
    // 0xbaa880: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbaa880: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbaa884: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbaa884: movz            x17, #0x2b03
    //     0xbaa888: add             lr, x0, x17
    //     0xbaa88c: ldr             lr, [x21, lr, lsl #3]
    //     0xbaa890: blr             lr
    // 0xbaa894: LeaveFrame
    //     0xbaa894: mov             SP, fp
    //     0xbaa898: ldp             fp, lr, [SP], #0x10
    // 0xbaa89c: ret
    //     0xbaa89c: ret             
    // 0xbaa8a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaa8a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaa8a4: b               #0xbaa6b8
  }
  String toString(Verse) {
    // ** addr: 0xc3296c, size: 0xac
    // 0xc3296c: EnterFrame
    //     0xc3296c: stp             fp, lr, [SP, #-0x10]!
    //     0xc32970: mov             fp, SP
    // 0xc32974: AllocStack(0x10)
    //     0xc32974: sub             SP, SP, #0x10
    // 0xc32978: CheckStackOverflow
    //     0xc32978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3297c: cmp             SP, x16
    //     0xc32980: b.ls            #0xc32a10
    // 0xc32984: r1 = Null
    //     0xc32984: mov             x1, NULL
    // 0xc32988: r2 = 10
    //     0xc32988: movz            x2, #0xa
    // 0xc3298c: r0 = AllocateArray()
    //     0xc3298c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc32990: stur            x0, [fp, #-8]
    // 0xc32994: r16 = "‮"
    //     0xc32994: add             x16, PP, #0x20, lsl #12  ; [pp+0x20b18] "‮"
    //     0xc32998: ldr             x16, [x16, #0xb18]
    // 0xc3299c: StoreField: r0->field_f = r16
    //     0xc3299c: stur            w16, [x0, #0xf]
    // 0xc329a0: ldr             x1, [fp, #0x10]
    // 0xc329a4: LoadField: r2 = r1->field_1f
    //     0xc329a4: ldur            w2, [x1, #0x1f]
    // 0xc329a8: DecompressPointer r2
    //     0xc329a8: add             x2, x2, HEAP, lsl #32
    // 0xc329ac: StoreField: r0->field_13 = r2
    //     0xc329ac: stur            w2, [x0, #0x13]
    // 0xc329b0: r16 = " "
    //     0xc329b0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc329b4: ArrayStore: r0[0] = r16  ; List_4
    //     0xc329b4: stur            w16, [x0, #0x17]
    // 0xc329b8: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xc329b8: ldur            x2, [x1, #0x17]
    // 0xc329bc: mov             x1, x2
    // 0xc329c0: r0 = _extension#1.toArabic()
    //     0xc329c0: bl              #0xb32690  ; [package:nuonline/app/data/models/verse.dart] ::_extension#1.toArabic
    // 0xc329c4: ldur            x1, [fp, #-8]
    // 0xc329c8: ArrayStore: r1[3] = r0  ; List_4
    //     0xc329c8: add             x25, x1, #0x1b
    //     0xc329cc: str             w0, [x25]
    //     0xc329d0: tbz             w0, #0, #0xc329ec
    //     0xc329d4: ldurb           w16, [x1, #-1]
    //     0xc329d8: ldurb           w17, [x0, #-1]
    //     0xc329dc: and             x16, x17, x16, lsr #2
    //     0xc329e0: tst             x16, HEAP, lsr #32
    //     0xc329e4: b.eq            #0xc329ec
    //     0xc329e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc329ec: ldur            x0, [fp, #-8]
    // 0xc329f0: r16 = "۝"
    //     0xc329f0: add             x16, PP, #0x20, lsl #12  ; [pp+0x20b20] "۝"
    //     0xc329f4: ldr             x16, [x16, #0xb20]
    // 0xc329f8: StoreField: r0->field_1f = r16
    //     0xc329f8: stur            w16, [x0, #0x1f]
    // 0xc329fc: str             x0, [SP]
    // 0xc32a00: r0 = _interpolate()
    //     0xc32a00: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc32a04: LeaveFrame
    //     0xc32a04: mov             SP, fp
    //     0xc32a08: ldp             fp, lr, [SP], #0x10
    // 0xc32a0c: ret
    //     0xc32a0c: ret             
    // 0xc32a10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc32a10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc32a14: b               #0xc32984
  }
}

// class id: 1642, size: 0x14, field offset: 0xc
class VerseAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa67508, size: 0xae4
    // 0xa67508: EnterFrame
    //     0xa67508: stp             fp, lr, [SP, #-0x10]!
    //     0xa6750c: mov             fp, SP
    // 0xa67510: AllocStack(0xc0)
    //     0xa67510: sub             SP, SP, #0xc0
    // 0xa67514: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa67514: stur            x2, [fp, #-0x20]
    // 0xa67518: CheckStackOverflow
    //     0xa67518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6751c: cmp             SP, x16
    //     0xa67520: b.ls            #0xa67fd4
    // 0xa67524: LoadField: r3 = r2->field_23
    //     0xa67524: ldur            x3, [x2, #0x23]
    // 0xa67528: add             x0, x3, #1
    // 0xa6752c: LoadField: r1 = r2->field_1b
    //     0xa6752c: ldur            x1, [x2, #0x1b]
    // 0xa67530: cmp             x0, x1
    // 0xa67534: b.gt            #0xa67f78
    // 0xa67538: LoadField: r4 = r2->field_7
    //     0xa67538: ldur            w4, [x2, #7]
    // 0xa6753c: DecompressPointer r4
    //     0xa6753c: add             x4, x4, HEAP, lsl #32
    // 0xa67540: stur            x4, [fp, #-0x18]
    // 0xa67544: StoreField: r2->field_23 = r0
    //     0xa67544: stur            x0, [x2, #0x23]
    // 0xa67548: LoadField: r0 = r4->field_13
    //     0xa67548: ldur            w0, [x4, #0x13]
    // 0xa6754c: r5 = LoadInt32Instr(r0)
    //     0xa6754c: sbfx            x5, x0, #1, #0x1f
    // 0xa67550: mov             x0, x5
    // 0xa67554: mov             x1, x3
    // 0xa67558: stur            x5, [fp, #-0x10]
    // 0xa6755c: cmp             x1, x0
    // 0xa67560: b.hs            #0xa67fdc
    // 0xa67564: LoadField: r0 = r4->field_7
    //     0xa67564: ldur            x0, [x4, #7]
    // 0xa67568: ldrb            w1, [x0, x3]
    // 0xa6756c: stur            x1, [fp, #-8]
    // 0xa67570: r16 = <int, dynamic>
    //     0xa67570: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa67574: ldr             x16, [x16, #0xac0]
    // 0xa67578: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa6757c: stp             lr, x16, [SP]
    // 0xa67580: r0 = Map._fromLiteral()
    //     0xa67580: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa67584: mov             x2, x0
    // 0xa67588: stur            x2, [fp, #-0x38]
    // 0xa6758c: r6 = 0
    //     0xa6758c: movz            x6, #0
    // 0xa67590: ldur            x3, [fp, #-0x20]
    // 0xa67594: ldur            x4, [fp, #-0x18]
    // 0xa67598: ldur            x5, [fp, #-8]
    // 0xa6759c: stur            x6, [fp, #-0x30]
    // 0xa675a0: CheckStackOverflow
    //     0xa675a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa675a4: cmp             SP, x16
    //     0xa675a8: b.ls            #0xa67fe0
    // 0xa675ac: cmp             x6, x5
    // 0xa675b0: b.ge            #0xa6763c
    // 0xa675b4: LoadField: r7 = r3->field_23
    //     0xa675b4: ldur            x7, [x3, #0x23]
    // 0xa675b8: add             x0, x7, #1
    // 0xa675bc: LoadField: r1 = r3->field_1b
    //     0xa675bc: ldur            x1, [x3, #0x1b]
    // 0xa675c0: cmp             x0, x1
    // 0xa675c4: b.gt            #0xa67fa0
    // 0xa675c8: StoreField: r3->field_23 = r0
    //     0xa675c8: stur            x0, [x3, #0x23]
    // 0xa675cc: ldur            x0, [fp, #-0x10]
    // 0xa675d0: mov             x1, x7
    // 0xa675d4: cmp             x1, x0
    // 0xa675d8: b.hs            #0xa67fe8
    // 0xa675dc: LoadField: r0 = r4->field_7
    //     0xa675dc: ldur            x0, [x4, #7]
    // 0xa675e0: ldrb            w8, [x0, x7]
    // 0xa675e4: mov             x1, x3
    // 0xa675e8: stur            x8, [fp, #-0x28]
    // 0xa675ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa675ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa675f0: r0 = read()
    //     0xa675f0: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa675f4: mov             x1, x0
    // 0xa675f8: ldur            x0, [fp, #-0x28]
    // 0xa675fc: lsl             x2, x0, #1
    // 0xa67600: r16 = LoadInt32Instr(r2)
    //     0xa67600: sbfx            x16, x2, #1, #0x1f
    // 0xa67604: r17 = 11601
    //     0xa67604: movz            x17, #0x2d51
    // 0xa67608: mul             x0, x16, x17
    // 0xa6760c: umulh           x16, x16, x17
    // 0xa67610: eor             x0, x0, x16
    // 0xa67614: r0 = 0
    //     0xa67614: eor             x0, x0, x0, lsr #32
    // 0xa67618: ubfiz           x0, x0, #1, #0x1e
    // 0xa6761c: r5 = LoadInt32Instr(r0)
    //     0xa6761c: sbfx            x5, x0, #1, #0x1f
    // 0xa67620: mov             x3, x1
    // 0xa67624: ldur            x1, [fp, #-0x38]
    // 0xa67628: r0 = _set()
    //     0xa67628: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa6762c: ldur            x0, [fp, #-0x30]
    // 0xa67630: add             x6, x0, #1
    // 0xa67634: ldur            x2, [fp, #-0x38]
    // 0xa67638: b               #0xa67590
    // 0xa6763c: mov             x0, x2
    // 0xa67640: mov             x1, x0
    // 0xa67644: r2 = 0
    //     0xa67644: movz            x2, #0
    // 0xa67648: r0 = _getValueOrData()
    //     0xa67648: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6764c: ldur            x3, [fp, #-0x38]
    // 0xa67650: LoadField: r1 = r3->field_f
    //     0xa67650: ldur            w1, [x3, #0xf]
    // 0xa67654: DecompressPointer r1
    //     0xa67654: add             x1, x1, HEAP, lsl #32
    // 0xa67658: cmp             w1, w0
    // 0xa6765c: b.ne            #0xa67668
    // 0xa67660: r4 = Null
    //     0xa67660: mov             x4, NULL
    // 0xa67664: b               #0xa6766c
    // 0xa67668: mov             x4, x0
    // 0xa6766c: mov             x0, x4
    // 0xa67670: stur            x4, [fp, #-0x18]
    // 0xa67674: r2 = Null
    //     0xa67674: mov             x2, NULL
    // 0xa67678: r1 = Null
    //     0xa67678: mov             x1, NULL
    // 0xa6767c: branchIfSmi(r0, 0xa676a4)
    //     0xa6767c: tbz             w0, #0, #0xa676a4
    // 0xa67680: r4 = LoadClassIdInstr(r0)
    //     0xa67680: ldur            x4, [x0, #-1]
    //     0xa67684: ubfx            x4, x4, #0xc, #0x14
    // 0xa67688: sub             x4, x4, #0x3c
    // 0xa6768c: cmp             x4, #1
    // 0xa67690: b.ls            #0xa676a4
    // 0xa67694: r8 = int
    //     0xa67694: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa67698: r3 = Null
    //     0xa67698: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b38] Null
    //     0xa6769c: ldr             x3, [x3, #0xb38]
    // 0xa676a0: r0 = int()
    //     0xa676a0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa676a4: ldur            x1, [fp, #-0x38]
    // 0xa676a8: r2 = 2
    //     0xa676a8: movz            x2, #0x2
    // 0xa676ac: r0 = _getValueOrData()
    //     0xa676ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa676b0: ldur            x3, [fp, #-0x38]
    // 0xa676b4: LoadField: r1 = r3->field_f
    //     0xa676b4: ldur            w1, [x3, #0xf]
    // 0xa676b8: DecompressPointer r1
    //     0xa676b8: add             x1, x1, HEAP, lsl #32
    // 0xa676bc: cmp             w1, w0
    // 0xa676c0: b.ne            #0xa676cc
    // 0xa676c4: r4 = Null
    //     0xa676c4: mov             x4, NULL
    // 0xa676c8: b               #0xa676d0
    // 0xa676cc: mov             x4, x0
    // 0xa676d0: mov             x0, x4
    // 0xa676d4: stur            x4, [fp, #-0x20]
    // 0xa676d8: r2 = Null
    //     0xa676d8: mov             x2, NULL
    // 0xa676dc: r1 = Null
    //     0xa676dc: mov             x1, NULL
    // 0xa676e0: branchIfSmi(r0, 0xa67708)
    //     0xa676e0: tbz             w0, #0, #0xa67708
    // 0xa676e4: r4 = LoadClassIdInstr(r0)
    //     0xa676e4: ldur            x4, [x0, #-1]
    //     0xa676e8: ubfx            x4, x4, #0xc, #0x14
    // 0xa676ec: sub             x4, x4, #0x3c
    // 0xa676f0: cmp             x4, #1
    // 0xa676f4: b.ls            #0xa67708
    // 0xa676f8: r8 = int
    //     0xa676f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa676fc: r3 = Null
    //     0xa676fc: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b48] Null
    //     0xa67700: ldr             x3, [x3, #0xb48]
    // 0xa67704: r0 = int()
    //     0xa67704: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa67708: ldur            x1, [fp, #-0x38]
    // 0xa6770c: r2 = 4
    //     0xa6770c: movz            x2, #0x4
    // 0xa67710: r0 = _getValueOrData()
    //     0xa67710: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67714: ldur            x3, [fp, #-0x38]
    // 0xa67718: LoadField: r1 = r3->field_f
    //     0xa67718: ldur            w1, [x3, #0xf]
    // 0xa6771c: DecompressPointer r1
    //     0xa6771c: add             x1, x1, HEAP, lsl #32
    // 0xa67720: cmp             w1, w0
    // 0xa67724: b.ne            #0xa67730
    // 0xa67728: r4 = Null
    //     0xa67728: mov             x4, NULL
    // 0xa6772c: b               #0xa67734
    // 0xa67730: mov             x4, x0
    // 0xa67734: mov             x0, x4
    // 0xa67738: stur            x4, [fp, #-0x40]
    // 0xa6773c: r2 = Null
    //     0xa6773c: mov             x2, NULL
    // 0xa67740: r1 = Null
    //     0xa67740: mov             x1, NULL
    // 0xa67744: branchIfSmi(r0, 0xa6776c)
    //     0xa67744: tbz             w0, #0, #0xa6776c
    // 0xa67748: r4 = LoadClassIdInstr(r0)
    //     0xa67748: ldur            x4, [x0, #-1]
    //     0xa6774c: ubfx            x4, x4, #0xc, #0x14
    // 0xa67750: sub             x4, x4, #0x3c
    // 0xa67754: cmp             x4, #1
    // 0xa67758: b.ls            #0xa6776c
    // 0xa6775c: r8 = int
    //     0xa6775c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa67760: r3 = Null
    //     0xa67760: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b58] Null
    //     0xa67764: ldr             x3, [x3, #0xb58]
    // 0xa67768: r0 = int()
    //     0xa67768: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa6776c: ldur            x1, [fp, #-0x38]
    // 0xa67770: r2 = 6
    //     0xa67770: movz            x2, #0x6
    // 0xa67774: r0 = _getValueOrData()
    //     0xa67774: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67778: ldur            x3, [fp, #-0x38]
    // 0xa6777c: LoadField: r1 = r3->field_f
    //     0xa6777c: ldur            w1, [x3, #0xf]
    // 0xa67780: DecompressPointer r1
    //     0xa67780: add             x1, x1, HEAP, lsl #32
    // 0xa67784: cmp             w1, w0
    // 0xa67788: b.ne            #0xa67794
    // 0xa6778c: r4 = Null
    //     0xa6778c: mov             x4, NULL
    // 0xa67790: b               #0xa67798
    // 0xa67794: mov             x4, x0
    // 0xa67798: mov             x0, x4
    // 0xa6779c: stur            x4, [fp, #-0x48]
    // 0xa677a0: r2 = Null
    //     0xa677a0: mov             x2, NULL
    // 0xa677a4: r1 = Null
    //     0xa677a4: mov             x1, NULL
    // 0xa677a8: r4 = 60
    //     0xa677a8: movz            x4, #0x3c
    // 0xa677ac: branchIfSmi(r0, 0xa677b8)
    //     0xa677ac: tbz             w0, #0, #0xa677b8
    // 0xa677b0: r4 = LoadClassIdInstr(r0)
    //     0xa677b0: ldur            x4, [x0, #-1]
    //     0xa677b4: ubfx            x4, x4, #0xc, #0x14
    // 0xa677b8: sub             x4, x4, #0x5e
    // 0xa677bc: cmp             x4, #1
    // 0xa677c0: b.ls            #0xa677d4
    // 0xa677c4: r8 = String
    //     0xa677c4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa677c8: r3 = Null
    //     0xa677c8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b68] Null
    //     0xa677cc: ldr             x3, [x3, #0xb68]
    // 0xa677d0: r0 = String()
    //     0xa677d0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa677d4: ldur            x1, [fp, #-0x38]
    // 0xa677d8: r2 = 8
    //     0xa677d8: movz            x2, #0x8
    // 0xa677dc: r0 = _getValueOrData()
    //     0xa677dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa677e0: ldur            x3, [fp, #-0x38]
    // 0xa677e4: LoadField: r1 = r3->field_f
    //     0xa677e4: ldur            w1, [x3, #0xf]
    // 0xa677e8: DecompressPointer r1
    //     0xa677e8: add             x1, x1, HEAP, lsl #32
    // 0xa677ec: cmp             w1, w0
    // 0xa677f0: b.ne            #0xa677fc
    // 0xa677f4: r4 = Null
    //     0xa677f4: mov             x4, NULL
    // 0xa677f8: b               #0xa67800
    // 0xa677fc: mov             x4, x0
    // 0xa67800: mov             x0, x4
    // 0xa67804: stur            x4, [fp, #-0x50]
    // 0xa67808: r2 = Null
    //     0xa67808: mov             x2, NULL
    // 0xa6780c: r1 = Null
    //     0xa6780c: mov             x1, NULL
    // 0xa67810: r4 = 60
    //     0xa67810: movz            x4, #0x3c
    // 0xa67814: branchIfSmi(r0, 0xa67820)
    //     0xa67814: tbz             w0, #0, #0xa67820
    // 0xa67818: r4 = LoadClassIdInstr(r0)
    //     0xa67818: ldur            x4, [x0, #-1]
    //     0xa6781c: ubfx            x4, x4, #0xc, #0x14
    // 0xa67820: sub             x4, x4, #0x5e
    // 0xa67824: cmp             x4, #1
    // 0xa67828: b.ls            #0xa6783c
    // 0xa6782c: r8 = String?
    //     0xa6782c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67830: r3 = Null
    //     0xa67830: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b78] Null
    //     0xa67834: ldr             x3, [x3, #0xb78]
    // 0xa67838: r0 = String?()
    //     0xa67838: bl              #0x600324  ; IsType_String?_Stub
    // 0xa6783c: ldur            x1, [fp, #-0x38]
    // 0xa67840: r2 = 24
    //     0xa67840: movz            x2, #0x18
    // 0xa67844: r0 = _getValueOrData()
    //     0xa67844: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67848: ldur            x3, [fp, #-0x38]
    // 0xa6784c: LoadField: r1 = r3->field_f
    //     0xa6784c: ldur            w1, [x3, #0xf]
    // 0xa67850: DecompressPointer r1
    //     0xa67850: add             x1, x1, HEAP, lsl #32
    // 0xa67854: cmp             w1, w0
    // 0xa67858: b.ne            #0xa67864
    // 0xa6785c: r4 = Null
    //     0xa6785c: mov             x4, NULL
    // 0xa67860: b               #0xa67868
    // 0xa67864: mov             x4, x0
    // 0xa67868: mov             x0, x4
    // 0xa6786c: stur            x4, [fp, #-0x58]
    // 0xa67870: r2 = Null
    //     0xa67870: mov             x2, NULL
    // 0xa67874: r1 = Null
    //     0xa67874: mov             x1, NULL
    // 0xa67878: r4 = 60
    //     0xa67878: movz            x4, #0x3c
    // 0xa6787c: branchIfSmi(r0, 0xa67888)
    //     0xa6787c: tbz             w0, #0, #0xa67888
    // 0xa67880: r4 = LoadClassIdInstr(r0)
    //     0xa67880: ldur            x4, [x0, #-1]
    //     0xa67884: ubfx            x4, x4, #0xc, #0x14
    // 0xa67888: sub             x4, x4, #0x5e
    // 0xa6788c: cmp             x4, #1
    // 0xa67890: b.ls            #0xa678a4
    // 0xa67894: r8 = String?
    //     0xa67894: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67898: r3 = Null
    //     0xa67898: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b88] Null
    //     0xa6789c: ldr             x3, [x3, #0xb88]
    // 0xa678a0: r0 = String?()
    //     0xa678a0: bl              #0x600324  ; IsType_String?_Stub
    // 0xa678a4: ldur            x1, [fp, #-0x38]
    // 0xa678a8: r2 = 26
    //     0xa678a8: movz            x2, #0x1a
    // 0xa678ac: r0 = _getValueOrData()
    //     0xa678ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa678b0: ldur            x3, [fp, #-0x38]
    // 0xa678b4: LoadField: r1 = r3->field_f
    //     0xa678b4: ldur            w1, [x3, #0xf]
    // 0xa678b8: DecompressPointer r1
    //     0xa678b8: add             x1, x1, HEAP, lsl #32
    // 0xa678bc: cmp             w1, w0
    // 0xa678c0: b.ne            #0xa678cc
    // 0xa678c4: r4 = Null
    //     0xa678c4: mov             x4, NULL
    // 0xa678c8: b               #0xa678d0
    // 0xa678cc: mov             x4, x0
    // 0xa678d0: mov             x0, x4
    // 0xa678d4: stur            x4, [fp, #-0x60]
    // 0xa678d8: r2 = Null
    //     0xa678d8: mov             x2, NULL
    // 0xa678dc: r1 = Null
    //     0xa678dc: mov             x1, NULL
    // 0xa678e0: r4 = 60
    //     0xa678e0: movz            x4, #0x3c
    // 0xa678e4: branchIfSmi(r0, 0xa678f0)
    //     0xa678e4: tbz             w0, #0, #0xa678f0
    // 0xa678e8: r4 = LoadClassIdInstr(r0)
    //     0xa678e8: ldur            x4, [x0, #-1]
    //     0xa678ec: ubfx            x4, x4, #0xc, #0x14
    // 0xa678f0: sub             x4, x4, #0x5e
    // 0xa678f4: cmp             x4, #1
    // 0xa678f8: b.ls            #0xa6790c
    // 0xa678fc: r8 = String?
    //     0xa678fc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67900: r3 = Null
    //     0xa67900: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b98] Null
    //     0xa67904: ldr             x3, [x3, #0xb98]
    // 0xa67908: r0 = String?()
    //     0xa67908: bl              #0x600324  ; IsType_String?_Stub
    // 0xa6790c: ldur            x1, [fp, #-0x38]
    // 0xa67910: r2 = 28
    //     0xa67910: movz            x2, #0x1c
    // 0xa67914: r0 = _getValueOrData()
    //     0xa67914: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67918: ldur            x3, [fp, #-0x38]
    // 0xa6791c: LoadField: r1 = r3->field_f
    //     0xa6791c: ldur            w1, [x3, #0xf]
    // 0xa67920: DecompressPointer r1
    //     0xa67920: add             x1, x1, HEAP, lsl #32
    // 0xa67924: cmp             w1, w0
    // 0xa67928: b.ne            #0xa67934
    // 0xa6792c: r4 = Null
    //     0xa6792c: mov             x4, NULL
    // 0xa67930: b               #0xa67938
    // 0xa67934: mov             x4, x0
    // 0xa67938: mov             x0, x4
    // 0xa6793c: stur            x4, [fp, #-0x68]
    // 0xa67940: r2 = Null
    //     0xa67940: mov             x2, NULL
    // 0xa67944: r1 = Null
    //     0xa67944: mov             x1, NULL
    // 0xa67948: r4 = 60
    //     0xa67948: movz            x4, #0x3c
    // 0xa6794c: branchIfSmi(r0, 0xa67958)
    //     0xa6794c: tbz             w0, #0, #0xa67958
    // 0xa67950: r4 = LoadClassIdInstr(r0)
    //     0xa67950: ldur            x4, [x0, #-1]
    //     0xa67954: ubfx            x4, x4, #0xc, #0x14
    // 0xa67958: sub             x4, x4, #0x5e
    // 0xa6795c: cmp             x4, #1
    // 0xa67960: b.ls            #0xa67974
    // 0xa67964: r8 = String?
    //     0xa67964: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67968: r3 = Null
    //     0xa67968: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ba8] Null
    //     0xa6796c: ldr             x3, [x3, #0xba8]
    // 0xa67970: r0 = String?()
    //     0xa67970: bl              #0x600324  ; IsType_String?_Stub
    // 0xa67974: ldur            x1, [fp, #-0x38]
    // 0xa67978: r2 = 30
    //     0xa67978: movz            x2, #0x1e
    // 0xa6797c: r0 = _getValueOrData()
    //     0xa6797c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67980: ldur            x3, [fp, #-0x38]
    // 0xa67984: LoadField: r1 = r3->field_f
    //     0xa67984: ldur            w1, [x3, #0xf]
    // 0xa67988: DecompressPointer r1
    //     0xa67988: add             x1, x1, HEAP, lsl #32
    // 0xa6798c: cmp             w1, w0
    // 0xa67990: b.ne            #0xa6799c
    // 0xa67994: r4 = Null
    //     0xa67994: mov             x4, NULL
    // 0xa67998: b               #0xa679a0
    // 0xa6799c: mov             x4, x0
    // 0xa679a0: mov             x0, x4
    // 0xa679a4: stur            x4, [fp, #-0x70]
    // 0xa679a8: r2 = Null
    //     0xa679a8: mov             x2, NULL
    // 0xa679ac: r1 = Null
    //     0xa679ac: mov             x1, NULL
    // 0xa679b0: r4 = 60
    //     0xa679b0: movz            x4, #0x3c
    // 0xa679b4: branchIfSmi(r0, 0xa679c0)
    //     0xa679b4: tbz             w0, #0, #0xa679c0
    // 0xa679b8: r4 = LoadClassIdInstr(r0)
    //     0xa679b8: ldur            x4, [x0, #-1]
    //     0xa679bc: ubfx            x4, x4, #0xc, #0x14
    // 0xa679c0: sub             x4, x4, #0x5e
    // 0xa679c4: cmp             x4, #1
    // 0xa679c8: b.ls            #0xa679dc
    // 0xa679cc: r8 = String?
    //     0xa679cc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa679d0: r3 = Null
    //     0xa679d0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20bb8] Null
    //     0xa679d4: ldr             x3, [x3, #0xbb8]
    // 0xa679d8: r0 = String?()
    //     0xa679d8: bl              #0x600324  ; IsType_String?_Stub
    // 0xa679dc: ldur            x1, [fp, #-0x38]
    // 0xa679e0: r2 = 32
    //     0xa679e0: movz            x2, #0x20
    // 0xa679e4: r0 = _getValueOrData()
    //     0xa679e4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa679e8: ldur            x3, [fp, #-0x38]
    // 0xa679ec: LoadField: r1 = r3->field_f
    //     0xa679ec: ldur            w1, [x3, #0xf]
    // 0xa679f0: DecompressPointer r1
    //     0xa679f0: add             x1, x1, HEAP, lsl #32
    // 0xa679f4: cmp             w1, w0
    // 0xa679f8: b.ne            #0xa67a04
    // 0xa679fc: r4 = Null
    //     0xa679fc: mov             x4, NULL
    // 0xa67a00: b               #0xa67a08
    // 0xa67a04: mov             x4, x0
    // 0xa67a08: mov             x0, x4
    // 0xa67a0c: stur            x4, [fp, #-0x78]
    // 0xa67a10: r2 = Null
    //     0xa67a10: mov             x2, NULL
    // 0xa67a14: r1 = Null
    //     0xa67a14: mov             x1, NULL
    // 0xa67a18: r4 = 60
    //     0xa67a18: movz            x4, #0x3c
    // 0xa67a1c: branchIfSmi(r0, 0xa67a28)
    //     0xa67a1c: tbz             w0, #0, #0xa67a28
    // 0xa67a20: r4 = LoadClassIdInstr(r0)
    //     0xa67a20: ldur            x4, [x0, #-1]
    //     0xa67a24: ubfx            x4, x4, #0xc, #0x14
    // 0xa67a28: sub             x4, x4, #0x5e
    // 0xa67a2c: cmp             x4, #1
    // 0xa67a30: b.ls            #0xa67a44
    // 0xa67a34: r8 = String?
    //     0xa67a34: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67a38: r3 = Null
    //     0xa67a38: add             x3, PP, #0x20, lsl #12  ; [pp+0x20bc8] Null
    //     0xa67a3c: ldr             x3, [x3, #0xbc8]
    // 0xa67a40: r0 = String?()
    //     0xa67a40: bl              #0x600324  ; IsType_String?_Stub
    // 0xa67a44: ldur            x1, [fp, #-0x38]
    // 0xa67a48: r2 = 10
    //     0xa67a48: movz            x2, #0xa
    // 0xa67a4c: r0 = _getValueOrData()
    //     0xa67a4c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67a50: ldur            x3, [fp, #-0x38]
    // 0xa67a54: LoadField: r1 = r3->field_f
    //     0xa67a54: ldur            w1, [x3, #0xf]
    // 0xa67a58: DecompressPointer r1
    //     0xa67a58: add             x1, x1, HEAP, lsl #32
    // 0xa67a5c: cmp             w1, w0
    // 0xa67a60: b.ne            #0xa67a6c
    // 0xa67a64: r4 = Null
    //     0xa67a64: mov             x4, NULL
    // 0xa67a68: b               #0xa67a70
    // 0xa67a6c: mov             x4, x0
    // 0xa67a70: mov             x0, x4
    // 0xa67a74: stur            x4, [fp, #-0x80]
    // 0xa67a78: r2 = Null
    //     0xa67a78: mov             x2, NULL
    // 0xa67a7c: r1 = Null
    //     0xa67a7c: mov             x1, NULL
    // 0xa67a80: r4 = 60
    //     0xa67a80: movz            x4, #0x3c
    // 0xa67a84: branchIfSmi(r0, 0xa67a90)
    //     0xa67a84: tbz             w0, #0, #0xa67a90
    // 0xa67a88: r4 = LoadClassIdInstr(r0)
    //     0xa67a88: ldur            x4, [x0, #-1]
    //     0xa67a8c: ubfx            x4, x4, #0xc, #0x14
    // 0xa67a90: sub             x4, x4, #0x5e
    // 0xa67a94: cmp             x4, #1
    // 0xa67a98: b.ls            #0xa67aac
    // 0xa67a9c: r8 = String?
    //     0xa67a9c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xa67aa0: r3 = Null
    //     0xa67aa0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20bd8] Null
    //     0xa67aa4: ldr             x3, [x3, #0xbd8]
    // 0xa67aa8: r0 = String?()
    //     0xa67aa8: bl              #0x600324  ; IsType_String?_Stub
    // 0xa67aac: ldur            x1, [fp, #-0x38]
    // 0xa67ab0: r2 = 12
    //     0xa67ab0: movz            x2, #0xc
    // 0xa67ab4: r0 = _getValueOrData()
    //     0xa67ab4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67ab8: ldur            x3, [fp, #-0x38]
    // 0xa67abc: LoadField: r1 = r3->field_f
    //     0xa67abc: ldur            w1, [x3, #0xf]
    // 0xa67ac0: DecompressPointer r1
    //     0xa67ac0: add             x1, x1, HEAP, lsl #32
    // 0xa67ac4: cmp             w1, w0
    // 0xa67ac8: b.ne            #0xa67ad4
    // 0xa67acc: r4 = Null
    //     0xa67acc: mov             x4, NULL
    // 0xa67ad0: b               #0xa67ad8
    // 0xa67ad4: mov             x4, x0
    // 0xa67ad8: mov             x0, x4
    // 0xa67adc: stur            x4, [fp, #-0x88]
    // 0xa67ae0: r2 = Null
    //     0xa67ae0: mov             x2, NULL
    // 0xa67ae4: r1 = Null
    //     0xa67ae4: mov             x1, NULL
    // 0xa67ae8: branchIfSmi(r0, 0xa67b10)
    //     0xa67ae8: tbz             w0, #0, #0xa67b10
    // 0xa67aec: r4 = LoadClassIdInstr(r0)
    //     0xa67aec: ldur            x4, [x0, #-1]
    //     0xa67af0: ubfx            x4, x4, #0xc, #0x14
    // 0xa67af4: sub             x4, x4, #0x3c
    // 0xa67af8: cmp             x4, #1
    // 0xa67afc: b.ls            #0xa67b10
    // 0xa67b00: r8 = int
    //     0xa67b00: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa67b04: r3 = Null
    //     0xa67b04: add             x3, PP, #0x20, lsl #12  ; [pp+0x20be8] Null
    //     0xa67b08: ldr             x3, [x3, #0xbe8]
    // 0xa67b0c: r0 = int()
    //     0xa67b0c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa67b10: ldur            x1, [fp, #-0x38]
    // 0xa67b14: r2 = 14
    //     0xa67b14: movz            x2, #0xe
    // 0xa67b18: r0 = _getValueOrData()
    //     0xa67b18: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67b1c: ldur            x3, [fp, #-0x38]
    // 0xa67b20: LoadField: r1 = r3->field_f
    //     0xa67b20: ldur            w1, [x3, #0xf]
    // 0xa67b24: DecompressPointer r1
    //     0xa67b24: add             x1, x1, HEAP, lsl #32
    // 0xa67b28: cmp             w1, w0
    // 0xa67b2c: b.ne            #0xa67b38
    // 0xa67b30: r4 = Null
    //     0xa67b30: mov             x4, NULL
    // 0xa67b34: b               #0xa67b3c
    // 0xa67b38: mov             x4, x0
    // 0xa67b3c: mov             x0, x4
    // 0xa67b40: stur            x4, [fp, #-0x90]
    // 0xa67b44: r2 = Null
    //     0xa67b44: mov             x2, NULL
    // 0xa67b48: r1 = Null
    //     0xa67b48: mov             x1, NULL
    // 0xa67b4c: branchIfSmi(r0, 0xa67b74)
    //     0xa67b4c: tbz             w0, #0, #0xa67b74
    // 0xa67b50: r4 = LoadClassIdInstr(r0)
    //     0xa67b50: ldur            x4, [x0, #-1]
    //     0xa67b54: ubfx            x4, x4, #0xc, #0x14
    // 0xa67b58: sub             x4, x4, #0x3c
    // 0xa67b5c: cmp             x4, #1
    // 0xa67b60: b.ls            #0xa67b74
    // 0xa67b64: r8 = int
    //     0xa67b64: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa67b68: r3 = Null
    //     0xa67b68: add             x3, PP, #0x20, lsl #12  ; [pp+0x20bf8] Null
    //     0xa67b6c: ldr             x3, [x3, #0xbf8]
    // 0xa67b70: r0 = int()
    //     0xa67b70: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa67b74: ldur            x1, [fp, #-0x38]
    // 0xa67b78: r2 = 16
    //     0xa67b78: movz            x2, #0x10
    // 0xa67b7c: r0 = _getValueOrData()
    //     0xa67b7c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67b80: ldur            x3, [fp, #-0x38]
    // 0xa67b84: LoadField: r1 = r3->field_f
    //     0xa67b84: ldur            w1, [x3, #0xf]
    // 0xa67b88: DecompressPointer r1
    //     0xa67b88: add             x1, x1, HEAP, lsl #32
    // 0xa67b8c: cmp             w1, w0
    // 0xa67b90: b.ne            #0xa67b9c
    // 0xa67b94: r4 = Null
    //     0xa67b94: mov             x4, NULL
    // 0xa67b98: b               #0xa67ba0
    // 0xa67b9c: mov             x4, x0
    // 0xa67ba0: mov             x0, x4
    // 0xa67ba4: stur            x4, [fp, #-0x98]
    // 0xa67ba8: r2 = Null
    //     0xa67ba8: mov             x2, NULL
    // 0xa67bac: r1 = Null
    //     0xa67bac: mov             x1, NULL
    // 0xa67bb0: r4 = 60
    //     0xa67bb0: movz            x4, #0x3c
    // 0xa67bb4: branchIfSmi(r0, 0xa67bc0)
    //     0xa67bb4: tbz             w0, #0, #0xa67bc0
    // 0xa67bb8: r4 = LoadClassIdInstr(r0)
    //     0xa67bb8: ldur            x4, [x0, #-1]
    //     0xa67bbc: ubfx            x4, x4, #0xc, #0x14
    // 0xa67bc0: sub             x4, x4, #0x5a
    // 0xa67bc4: cmp             x4, #2
    // 0xa67bc8: b.ls            #0xa67be0
    // 0xa67bcc: r8 = List?
    //     0xa67bcc: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xa67bd0: ldr             x8, [x8, #0x140]
    // 0xa67bd4: r3 = Null
    //     0xa67bd4: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c08] Null
    //     0xa67bd8: ldr             x3, [x3, #0xc08]
    // 0xa67bdc: r0 = List?()
    //     0xa67bdc: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xa67be0: ldur            x0, [fp, #-0x98]
    // 0xa67be4: cmp             w0, NULL
    // 0xa67be8: b.ne            #0xa67bf4
    // 0xa67bec: r3 = Null
    //     0xa67bec: mov             x3, NULL
    // 0xa67bf0: b               #0xa67c1c
    // 0xa67bf4: r1 = LoadClassIdInstr(r0)
    //     0xa67bf4: ldur            x1, [x0, #-1]
    //     0xa67bf8: ubfx            x1, x1, #0xc, #0x14
    // 0xa67bfc: stp             x0, NULL, [SP]
    // 0xa67c00: mov             x0, x1
    // 0xa67c04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa67c04: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa67c08: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa67c08: movz            x17, #0xf30c
    //     0xa67c0c: add             lr, x0, x17
    //     0xa67c10: ldr             lr, [x21, lr, lsl #3]
    //     0xa67c14: blr             lr
    // 0xa67c18: mov             x3, x0
    // 0xa67c1c: ldur            x0, [fp, #-0x38]
    // 0xa67c20: mov             x1, x0
    // 0xa67c24: stur            x3, [fp, #-0x98]
    // 0xa67c28: r2 = 18
    //     0xa67c28: movz            x2, #0x12
    // 0xa67c2c: r0 = _getValueOrData()
    //     0xa67c2c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67c30: ldur            x3, [fp, #-0x38]
    // 0xa67c34: LoadField: r1 = r3->field_f
    //     0xa67c34: ldur            w1, [x3, #0xf]
    // 0xa67c38: DecompressPointer r1
    //     0xa67c38: add             x1, x1, HEAP, lsl #32
    // 0xa67c3c: cmp             w1, w0
    // 0xa67c40: b.ne            #0xa67c4c
    // 0xa67c44: r4 = Null
    //     0xa67c44: mov             x4, NULL
    // 0xa67c48: b               #0xa67c50
    // 0xa67c4c: mov             x4, x0
    // 0xa67c50: mov             x0, x4
    // 0xa67c54: stur            x4, [fp, #-0xa0]
    // 0xa67c58: r2 = Null
    //     0xa67c58: mov             x2, NULL
    // 0xa67c5c: r1 = Null
    //     0xa67c5c: mov             x1, NULL
    // 0xa67c60: r4 = 60
    //     0xa67c60: movz            x4, #0x3c
    // 0xa67c64: branchIfSmi(r0, 0xa67c70)
    //     0xa67c64: tbz             w0, #0, #0xa67c70
    // 0xa67c68: r4 = LoadClassIdInstr(r0)
    //     0xa67c68: ldur            x4, [x0, #-1]
    //     0xa67c6c: ubfx            x4, x4, #0xc, #0x14
    // 0xa67c70: cmp             x4, #0x3f
    // 0xa67c74: b.eq            #0xa67c88
    // 0xa67c78: r8 = bool?
    //     0xa67c78: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xa67c7c: r3 = Null
    //     0xa67c7c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c18] Null
    //     0xa67c80: ldr             x3, [x3, #0xc18]
    // 0xa67c84: r0 = bool?()
    //     0xa67c84: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xa67c88: ldur            x1, [fp, #-0x38]
    // 0xa67c8c: r2 = 20
    //     0xa67c8c: movz            x2, #0x14
    // 0xa67c90: r0 = _getValueOrData()
    //     0xa67c90: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67c94: ldur            x3, [fp, #-0x38]
    // 0xa67c98: LoadField: r1 = r3->field_f
    //     0xa67c98: ldur            w1, [x3, #0xf]
    // 0xa67c9c: DecompressPointer r1
    //     0xa67c9c: add             x1, x1, HEAP, lsl #32
    // 0xa67ca0: cmp             w1, w0
    // 0xa67ca4: b.ne            #0xa67cb0
    // 0xa67ca8: r4 = Null
    //     0xa67ca8: mov             x4, NULL
    // 0xa67cac: b               #0xa67cb4
    // 0xa67cb0: mov             x4, x0
    // 0xa67cb4: mov             x0, x4
    // 0xa67cb8: stur            x4, [fp, #-0xa8]
    // 0xa67cbc: r2 = Null
    //     0xa67cbc: mov             x2, NULL
    // 0xa67cc0: r1 = Null
    //     0xa67cc0: mov             x1, NULL
    // 0xa67cc4: r4 = 60
    //     0xa67cc4: movz            x4, #0x3c
    // 0xa67cc8: branchIfSmi(r0, 0xa67cd4)
    //     0xa67cc8: tbz             w0, #0, #0xa67cd4
    // 0xa67ccc: r4 = LoadClassIdInstr(r0)
    //     0xa67ccc: ldur            x4, [x0, #-1]
    //     0xa67cd0: ubfx            x4, x4, #0xc, #0x14
    // 0xa67cd4: cmp             x4, #0x3f
    // 0xa67cd8: b.eq            #0xa67cec
    // 0xa67cdc: r8 = bool
    //     0xa67cdc: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xa67ce0: r3 = Null
    //     0xa67ce0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c28] Null
    //     0xa67ce4: ldr             x3, [x3, #0xc28]
    // 0xa67ce8: r0 = bool()
    //     0xa67ce8: bl              #0xed4390  ; IsType_bool_Stub
    // 0xa67cec: ldur            x1, [fp, #-0x38]
    // 0xa67cf0: r2 = 22
    //     0xa67cf0: movz            x2, #0x16
    // 0xa67cf4: r0 = _getValueOrData()
    //     0xa67cf4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67cf8: ldur            x3, [fp, #-0x38]
    // 0xa67cfc: LoadField: r1 = r3->field_f
    //     0xa67cfc: ldur            w1, [x3, #0xf]
    // 0xa67d00: DecompressPointer r1
    //     0xa67d00: add             x1, x1, HEAP, lsl #32
    // 0xa67d04: cmp             w1, w0
    // 0xa67d08: b.ne            #0xa67d14
    // 0xa67d0c: r4 = Null
    //     0xa67d0c: mov             x4, NULL
    // 0xa67d10: b               #0xa67d18
    // 0xa67d14: mov             x4, x0
    // 0xa67d18: mov             x0, x4
    // 0xa67d1c: stur            x4, [fp, #-0xb0]
    // 0xa67d20: r2 = Null
    //     0xa67d20: mov             x2, NULL
    // 0xa67d24: r1 = Null
    //     0xa67d24: mov             x1, NULL
    // 0xa67d28: r4 = 60
    //     0xa67d28: movz            x4, #0x3c
    // 0xa67d2c: branchIfSmi(r0, 0xa67d38)
    //     0xa67d2c: tbz             w0, #0, #0xa67d38
    // 0xa67d30: r4 = LoadClassIdInstr(r0)
    //     0xa67d30: ldur            x4, [x0, #-1]
    //     0xa67d34: ubfx            x4, x4, #0xc, #0x14
    // 0xa67d38: cmp             x4, #0x1ae
    // 0xa67d3c: b.eq            #0xa67d64
    // 0xa67d40: r17 = -7188
    //     0xa67d40: movn            x17, #0x1c13
    // 0xa67d44: add             x4, x4, x17
    // 0xa67d48: cmp             x4, #1
    // 0xa67d4c: b.ls            #0xa67d64
    // 0xa67d50: r8 = DateTime
    //     0xa67d50: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a290] Type: DateTime
    //     0xa67d54: ldr             x8, [x8, #0x290]
    // 0xa67d58: r3 = Null
    //     0xa67d58: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c38] Null
    //     0xa67d5c: ldr             x3, [x3, #0xc38]
    // 0xa67d60: r0 = DateTime()
    //     0xa67d60: bl              #0x615e38  ; IsType_DateTime_Stub
    // 0xa67d64: ldur            x1, [fp, #-0x38]
    // 0xa67d68: r2 = 34
    //     0xa67d68: movz            x2, #0x22
    // 0xa67d6c: r0 = _getValueOrData()
    //     0xa67d6c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67d70: mov             x1, x0
    // 0xa67d74: ldur            x0, [fp, #-0x38]
    // 0xa67d78: LoadField: r2 = r0->field_f
    //     0xa67d78: ldur            w2, [x0, #0xf]
    // 0xa67d7c: DecompressPointer r2
    //     0xa67d7c: add             x2, x2, HEAP, lsl #32
    // 0xa67d80: cmp             w2, w1
    // 0xa67d84: b.eq            #0xa67d90
    // 0xa67d88: cmp             w1, NULL
    // 0xa67d8c: b.ne            #0xa67da8
    // 0xa67d90: r1 = <Tajweed>
    //     0xa67d90: add             x1, PP, #0xf, lsl #12  ; [pp+0xf1f8] TypeArguments: <Tajweed>
    //     0xa67d94: ldr             x1, [x1, #0x1f8]
    // 0xa67d98: r2 = 0
    //     0xa67d98: movz            x2, #0
    // 0xa67d9c: r0 = _GrowableList()
    //     0xa67d9c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa67da0: mov             x23, x0
    // 0xa67da4: b               #0xa67e48
    // 0xa67da8: mov             x1, x0
    // 0xa67dac: r2 = 34
    //     0xa67dac: movz            x2, #0x22
    // 0xa67db0: r0 = _getValueOrData()
    //     0xa67db0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa67db4: mov             x1, x0
    // 0xa67db8: ldur            x0, [fp, #-0x38]
    // 0xa67dbc: LoadField: r2 = r0->field_f
    //     0xa67dbc: ldur            w2, [x0, #0xf]
    // 0xa67dc0: DecompressPointer r2
    //     0xa67dc0: add             x2, x2, HEAP, lsl #32
    // 0xa67dc4: cmp             w2, w1
    // 0xa67dc8: b.ne            #0xa67dd4
    // 0xa67dcc: r3 = Null
    //     0xa67dcc: mov             x3, NULL
    // 0xa67dd0: b               #0xa67dd8
    // 0xa67dd4: mov             x3, x1
    // 0xa67dd8: mov             x0, x3
    // 0xa67ddc: stur            x3, [fp, #-0x38]
    // 0xa67de0: r2 = Null
    //     0xa67de0: mov             x2, NULL
    // 0xa67de4: r1 = Null
    //     0xa67de4: mov             x1, NULL
    // 0xa67de8: r4 = 60
    //     0xa67de8: movz            x4, #0x3c
    // 0xa67dec: branchIfSmi(r0, 0xa67df8)
    //     0xa67dec: tbz             w0, #0, #0xa67df8
    // 0xa67df0: r4 = LoadClassIdInstr(r0)
    //     0xa67df0: ldur            x4, [x0, #-1]
    //     0xa67df4: ubfx            x4, x4, #0xc, #0x14
    // 0xa67df8: sub             x4, x4, #0x5a
    // 0xa67dfc: cmp             x4, #2
    // 0xa67e00: b.ls            #0xa67e14
    // 0xa67e04: r8 = List
    //     0xa67e04: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xa67e08: r3 = Null
    //     0xa67e08: add             x3, PP, #0x20, lsl #12  ; [pp+0x20c48] Null
    //     0xa67e0c: ldr             x3, [x3, #0xc48]
    // 0xa67e10: r0 = List()
    //     0xa67e10: bl              #0xed6b40  ; IsType_List_Stub
    // 0xa67e14: ldur            x0, [fp, #-0x38]
    // 0xa67e18: r1 = LoadClassIdInstr(r0)
    //     0xa67e18: ldur            x1, [x0, #-1]
    //     0xa67e1c: ubfx            x1, x1, #0xc, #0x14
    // 0xa67e20: r16 = <Tajweed>
    //     0xa67e20: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f8] TypeArguments: <Tajweed>
    //     0xa67e24: ldr             x16, [x16, #0x1f8]
    // 0xa67e28: stp             x0, x16, [SP]
    // 0xa67e2c: mov             x0, x1
    // 0xa67e30: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa67e30: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa67e34: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xa67e34: movz            x17, #0xf30c
    //     0xa67e38: add             lr, x0, x17
    //     0xa67e3c: ldr             lr, [x21, lr, lsl #3]
    //     0xa67e40: blr             lr
    // 0xa67e44: mov             x23, x0
    // 0xa67e48: ldur            x3, [fp, #-0x98]
    // 0xa67e4c: ldur            x2, [fp, #-0xa0]
    // 0xa67e50: ldur            x1, [fp, #-0xa8]
    // 0xa67e54: ldur            x0, [fp, #-0xb0]
    // 0xa67e58: ldur            x20, [fp, #-0x18]
    // 0xa67e5c: ldur            x19, [fp, #-0x20]
    // 0xa67e60: ldur            x14, [fp, #-0x40]
    // 0xa67e64: ldur            x13, [fp, #-0x48]
    // 0xa67e68: ldur            x12, [fp, #-0x50]
    // 0xa67e6c: ldur            x11, [fp, #-0x58]
    // 0xa67e70: ldur            x10, [fp, #-0x60]
    // 0xa67e74: ldur            x9, [fp, #-0x68]
    // 0xa67e78: ldur            x8, [fp, #-0x70]
    // 0xa67e7c: ldur            x7, [fp, #-0x78]
    // 0xa67e80: ldur            x6, [fp, #-0x80]
    // 0xa67e84: ldur            x5, [fp, #-0x88]
    // 0xa67e88: ldur            x4, [fp, #-0x90]
    // 0xa67e8c: stur            x23, [fp, #-0x38]
    // 0xa67e90: r24 = LoadInt32Instr(r20)
    //     0xa67e90: sbfx            x24, x20, #1, #0x1f
    //     0xa67e94: tbz             w20, #0, #0xa67e9c
    //     0xa67e98: ldur            x24, [x20, #7]
    // 0xa67e9c: stur            x24, [fp, #-8]
    // 0xa67ea0: r0 = Verse()
    //     0xa67ea0: bl              #0x7c7834  ; AllocateVerseStub -> Verse (size=0x64)
    // 0xa67ea4: mov             x1, x0
    // 0xa67ea8: ldur            x0, [fp, #-8]
    // 0xa67eac: StoreField: r1->field_7 = r0
    //     0xa67eac: stur            x0, [x1, #7]
    // 0xa67eb0: ldur            x0, [fp, #-0x20]
    // 0xa67eb4: r2 = LoadInt32Instr(r0)
    //     0xa67eb4: sbfx            x2, x0, #1, #0x1f
    //     0xa67eb8: tbz             w0, #0, #0xa67ec0
    //     0xa67ebc: ldur            x2, [x0, #7]
    // 0xa67ec0: StoreField: r1->field_f = r2
    //     0xa67ec0: stur            x2, [x1, #0xf]
    // 0xa67ec4: ldur            x0, [fp, #-0x40]
    // 0xa67ec8: r2 = LoadInt32Instr(r0)
    //     0xa67ec8: sbfx            x2, x0, #1, #0x1f
    //     0xa67ecc: tbz             w0, #0, #0xa67ed4
    //     0xa67ed0: ldur            x2, [x0, #7]
    // 0xa67ed4: ArrayStore: r1[0] = r2  ; List_8
    //     0xa67ed4: stur            x2, [x1, #0x17]
    // 0xa67ed8: ldur            x0, [fp, #-0x48]
    // 0xa67edc: StoreField: r1->field_1f = r0
    //     0xa67edc: stur            w0, [x1, #0x1f]
    // 0xa67ee0: ldur            x0, [fp, #-0x50]
    // 0xa67ee4: StoreField: r1->field_23 = r0
    //     0xa67ee4: stur            w0, [x1, #0x23]
    // 0xa67ee8: ldur            x0, [fp, #-0x58]
    // 0xa67eec: StoreField: r1->field_4b = r0
    //     0xa67eec: stur            w0, [x1, #0x4b]
    // 0xa67ef0: ldur            x0, [fp, #-0x60]
    // 0xa67ef4: StoreField: r1->field_4f = r0
    //     0xa67ef4: stur            w0, [x1, #0x4f]
    // 0xa67ef8: ldur            x0, [fp, #-0x68]
    // 0xa67efc: StoreField: r1->field_53 = r0
    //     0xa67efc: stur            w0, [x1, #0x53]
    // 0xa67f00: ldur            x0, [fp, #-0x70]
    // 0xa67f04: StoreField: r1->field_57 = r0
    //     0xa67f04: stur            w0, [x1, #0x57]
    // 0xa67f08: ldur            x0, [fp, #-0x78]
    // 0xa67f0c: StoreField: r1->field_5b = r0
    //     0xa67f0c: stur            w0, [x1, #0x5b]
    // 0xa67f10: ldur            x0, [fp, #-0x80]
    // 0xa67f14: StoreField: r1->field_27 = r0
    //     0xa67f14: stur            w0, [x1, #0x27]
    // 0xa67f18: ldur            x0, [fp, #-0x88]
    // 0xa67f1c: r2 = LoadInt32Instr(r0)
    //     0xa67f1c: sbfx            x2, x0, #1, #0x1f
    //     0xa67f20: tbz             w0, #0, #0xa67f28
    //     0xa67f24: ldur            x2, [x0, #7]
    // 0xa67f28: StoreField: r1->field_2b = r2
    //     0xa67f28: stur            x2, [x1, #0x2b]
    // 0xa67f2c: ldur            x0, [fp, #-0x90]
    // 0xa67f30: r2 = LoadInt32Instr(r0)
    //     0xa67f30: sbfx            x2, x0, #1, #0x1f
    //     0xa67f34: tbz             w0, #0, #0xa67f3c
    //     0xa67f38: ldur            x2, [x0, #7]
    // 0xa67f3c: StoreField: r1->field_33 = r2
    //     0xa67f3c: stur            x2, [x1, #0x33]
    // 0xa67f40: ldur            x0, [fp, #-0x98]
    // 0xa67f44: StoreField: r1->field_3b = r0
    //     0xa67f44: stur            w0, [x1, #0x3b]
    // 0xa67f48: ldur            x0, [fp, #-0xa0]
    // 0xa67f4c: StoreField: r1->field_3f = r0
    //     0xa67f4c: stur            w0, [x1, #0x3f]
    // 0xa67f50: ldur            x0, [fp, #-0xa8]
    // 0xa67f54: StoreField: r1->field_43 = r0
    //     0xa67f54: stur            w0, [x1, #0x43]
    // 0xa67f58: ldur            x0, [fp, #-0xb0]
    // 0xa67f5c: StoreField: r1->field_47 = r0
    //     0xa67f5c: stur            w0, [x1, #0x47]
    // 0xa67f60: ldur            x0, [fp, #-0x38]
    // 0xa67f64: StoreField: r1->field_5f = r0
    //     0xa67f64: stur            w0, [x1, #0x5f]
    // 0xa67f68: mov             x0, x1
    // 0xa67f6c: LeaveFrame
    //     0xa67f6c: mov             SP, fp
    //     0xa67f70: ldp             fp, lr, [SP], #0x10
    // 0xa67f74: ret
    //     0xa67f74: ret             
    // 0xa67f78: r0 = RangeError()
    //     0xa67f78: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa67f7c: mov             x1, x0
    // 0xa67f80: r0 = "Not enough bytes available."
    //     0xa67f80: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa67f84: ldr             x0, [x0, #0x8a8]
    // 0xa67f88: ArrayStore: r1[0] = r0  ; List_4
    //     0xa67f88: stur            w0, [x1, #0x17]
    // 0xa67f8c: r2 = false
    //     0xa67f8c: add             x2, NULL, #0x30  ; false
    // 0xa67f90: StoreField: r1->field_b = r2
    //     0xa67f90: stur            w2, [x1, #0xb]
    // 0xa67f94: mov             x0, x1
    // 0xa67f98: r0 = Throw()
    //     0xa67f98: bl              #0xec04b8  ; ThrowStub
    // 0xa67f9c: brk             #0
    // 0xa67fa0: r0 = "Not enough bytes available."
    //     0xa67fa0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa67fa4: ldr             x0, [x0, #0x8a8]
    // 0xa67fa8: r2 = false
    //     0xa67fa8: add             x2, NULL, #0x30  ; false
    // 0xa67fac: r0 = RangeError()
    //     0xa67fac: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa67fb0: mov             x1, x0
    // 0xa67fb4: r0 = "Not enough bytes available."
    //     0xa67fb4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa67fb8: ldr             x0, [x0, #0x8a8]
    // 0xa67fbc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa67fbc: stur            w0, [x1, #0x17]
    // 0xa67fc0: r0 = false
    //     0xa67fc0: add             x0, NULL, #0x30  ; false
    // 0xa67fc4: StoreField: r1->field_b = r0
    //     0xa67fc4: stur            w0, [x1, #0xb]
    // 0xa67fc8: mov             x0, x1
    // 0xa67fcc: r0 = Throw()
    //     0xa67fcc: bl              #0xec04b8  ; ThrowStub
    // 0xa67fd0: brk             #0
    // 0xa67fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa67fd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa67fd8: b               #0xa67524
    // 0xa67fdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa67fdc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa67fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa67fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa67fe4: b               #0xa675ac
    // 0xa67fe8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa67fe8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd87d8, size: 0xb48
    // 0xbd87d8: EnterFrame
    //     0xbd87d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbd87dc: mov             fp, SP
    // 0xbd87e0: AllocStack(0x28)
    //     0xbd87e0: sub             SP, SP, #0x28
    // 0xbd87e4: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd87e4: mov             x4, x2
    //     0xbd87e8: stur            x2, [fp, #-8]
    //     0xbd87ec: stur            x3, [fp, #-0x10]
    // 0xbd87f0: CheckStackOverflow
    //     0xbd87f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd87f4: cmp             SP, x16
    //     0xbd87f8: b.ls            #0xbd92cc
    // 0xbd87fc: mov             x0, x3
    // 0xbd8800: r2 = Null
    //     0xbd8800: mov             x2, NULL
    // 0xbd8804: r1 = Null
    //     0xbd8804: mov             x1, NULL
    // 0xbd8808: r4 = 60
    //     0xbd8808: movz            x4, #0x3c
    // 0xbd880c: branchIfSmi(r0, 0xbd8818)
    //     0xbd880c: tbz             w0, #0, #0xbd8818
    // 0xbd8810: r4 = LoadClassIdInstr(r0)
    //     0xbd8810: ldur            x4, [x0, #-1]
    //     0xbd8814: ubfx            x4, x4, #0xc, #0x14
    // 0xbd8818: cmp             x4, #0x45e
    // 0xbd881c: b.eq            #0xbd8834
    // 0xbd8820: r8 = Verse
    //     0xbd8820: add             x8, PP, #0x1a, lsl #12  ; [pp+0x1a0c8] Type: Verse
    //     0xbd8824: ldr             x8, [x8, #0xc8]
    // 0xbd8828: r3 = Null
    //     0xbd8828: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b090] Null
    //     0xbd882c: ldr             x3, [x3, #0x90]
    // 0xbd8830: r0 = Verse()
    //     0xbd8830: bl              #0x760d1c  ; IsType_Verse_Stub
    // 0xbd8834: ldur            x0, [fp, #-8]
    // 0xbd8838: LoadField: r1 = r0->field_b
    //     0xbd8838: ldur            w1, [x0, #0xb]
    // 0xbd883c: DecompressPointer r1
    //     0xbd883c: add             x1, x1, HEAP, lsl #32
    // 0xbd8840: LoadField: r2 = r1->field_13
    //     0xbd8840: ldur            w2, [x1, #0x13]
    // 0xbd8844: LoadField: r1 = r0->field_13
    //     0xbd8844: ldur            x1, [x0, #0x13]
    // 0xbd8848: r3 = LoadInt32Instr(r2)
    //     0xbd8848: sbfx            x3, x2, #1, #0x1f
    // 0xbd884c: sub             x2, x3, x1
    // 0xbd8850: cmp             x2, #1
    // 0xbd8854: b.ge            #0xbd8864
    // 0xbd8858: mov             x1, x0
    // 0xbd885c: r2 = 1
    //     0xbd885c: movz            x2, #0x1
    // 0xbd8860: r0 = _increaseBufferSize()
    //     0xbd8860: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8864: ldur            x3, [fp, #-8]
    // 0xbd8868: r2 = 18
    //     0xbd8868: movz            x2, #0x12
    // 0xbd886c: LoadField: r4 = r3->field_b
    //     0xbd886c: ldur            w4, [x3, #0xb]
    // 0xbd8870: DecompressPointer r4
    //     0xbd8870: add             x4, x4, HEAP, lsl #32
    // 0xbd8874: LoadField: r5 = r3->field_13
    //     0xbd8874: ldur            x5, [x3, #0x13]
    // 0xbd8878: add             x6, x5, #1
    // 0xbd887c: StoreField: r3->field_13 = r6
    //     0xbd887c: stur            x6, [x3, #0x13]
    // 0xbd8880: LoadField: r0 = r4->field_13
    //     0xbd8880: ldur            w0, [x4, #0x13]
    // 0xbd8884: r7 = LoadInt32Instr(r0)
    //     0xbd8884: sbfx            x7, x0, #1, #0x1f
    // 0xbd8888: mov             x0, x7
    // 0xbd888c: mov             x1, x5
    // 0xbd8890: cmp             x1, x0
    // 0xbd8894: b.hs            #0xbd92d4
    // 0xbd8898: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd8898: add             x0, x4, x5
    //     0xbd889c: strb            w2, [x0, #0x17]
    // 0xbd88a0: sub             x0, x7, x6
    // 0xbd88a4: cmp             x0, #1
    // 0xbd88a8: b.ge            #0xbd88b8
    // 0xbd88ac: mov             x1, x3
    // 0xbd88b0: r2 = 1
    //     0xbd88b0: movz            x2, #0x1
    // 0xbd88b4: r0 = _increaseBufferSize()
    //     0xbd88b4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd88b8: ldur            x2, [fp, #-8]
    // 0xbd88bc: ldur            x3, [fp, #-0x10]
    // 0xbd88c0: LoadField: r4 = r2->field_b
    //     0xbd88c0: ldur            w4, [x2, #0xb]
    // 0xbd88c4: DecompressPointer r4
    //     0xbd88c4: add             x4, x4, HEAP, lsl #32
    // 0xbd88c8: LoadField: r5 = r2->field_13
    //     0xbd88c8: ldur            x5, [x2, #0x13]
    // 0xbd88cc: add             x0, x5, #1
    // 0xbd88d0: StoreField: r2->field_13 = r0
    //     0xbd88d0: stur            x0, [x2, #0x13]
    // 0xbd88d4: LoadField: r0 = r4->field_13
    //     0xbd88d4: ldur            w0, [x4, #0x13]
    // 0xbd88d8: r1 = LoadInt32Instr(r0)
    //     0xbd88d8: sbfx            x1, x0, #1, #0x1f
    // 0xbd88dc: mov             x0, x1
    // 0xbd88e0: mov             x1, x5
    // 0xbd88e4: cmp             x1, x0
    // 0xbd88e8: b.hs            #0xbd92d8
    // 0xbd88ec: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd88ec: add             x0, x4, x5
    //     0xbd88f0: strb            wzr, [x0, #0x17]
    // 0xbd88f4: LoadField: r4 = r3->field_7
    //     0xbd88f4: ldur            x4, [x3, #7]
    // 0xbd88f8: r0 = BoxInt64Instr(r4)
    //     0xbd88f8: sbfiz           x0, x4, #1, #0x1f
    //     0xbd88fc: cmp             x4, x0, asr #1
    //     0xbd8900: b.eq            #0xbd890c
    //     0xbd8904: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8908: stur            x4, [x0, #7]
    // 0xbd890c: r16 = <int>
    //     0xbd890c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8910: stp             x2, x16, [SP, #8]
    // 0xbd8914: str             x0, [SP]
    // 0xbd8918: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8918: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd891c: r0 = write()
    //     0xbd891c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8920: ldur            x0, [fp, #-8]
    // 0xbd8924: LoadField: r1 = r0->field_b
    //     0xbd8924: ldur            w1, [x0, #0xb]
    // 0xbd8928: DecompressPointer r1
    //     0xbd8928: add             x1, x1, HEAP, lsl #32
    // 0xbd892c: LoadField: r2 = r1->field_13
    //     0xbd892c: ldur            w2, [x1, #0x13]
    // 0xbd8930: LoadField: r1 = r0->field_13
    //     0xbd8930: ldur            x1, [x0, #0x13]
    // 0xbd8934: r3 = LoadInt32Instr(r2)
    //     0xbd8934: sbfx            x3, x2, #1, #0x1f
    // 0xbd8938: sub             x2, x3, x1
    // 0xbd893c: cmp             x2, #1
    // 0xbd8940: b.ge            #0xbd8950
    // 0xbd8944: mov             x1, x0
    // 0xbd8948: r2 = 1
    //     0xbd8948: movz            x2, #0x1
    // 0xbd894c: r0 = _increaseBufferSize()
    //     0xbd894c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8950: ldur            x2, [fp, #-8]
    // 0xbd8954: ldur            x3, [fp, #-0x10]
    // 0xbd8958: r4 = 1
    //     0xbd8958: movz            x4, #0x1
    // 0xbd895c: LoadField: r5 = r2->field_b
    //     0xbd895c: ldur            w5, [x2, #0xb]
    // 0xbd8960: DecompressPointer r5
    //     0xbd8960: add             x5, x5, HEAP, lsl #32
    // 0xbd8964: LoadField: r6 = r2->field_13
    //     0xbd8964: ldur            x6, [x2, #0x13]
    // 0xbd8968: add             x0, x6, #1
    // 0xbd896c: StoreField: r2->field_13 = r0
    //     0xbd896c: stur            x0, [x2, #0x13]
    // 0xbd8970: LoadField: r0 = r5->field_13
    //     0xbd8970: ldur            w0, [x5, #0x13]
    // 0xbd8974: r1 = LoadInt32Instr(r0)
    //     0xbd8974: sbfx            x1, x0, #1, #0x1f
    // 0xbd8978: mov             x0, x1
    // 0xbd897c: mov             x1, x6
    // 0xbd8980: cmp             x1, x0
    // 0xbd8984: b.hs            #0xbd92dc
    // 0xbd8988: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8988: add             x0, x5, x6
    //     0xbd898c: strb            w4, [x0, #0x17]
    // 0xbd8990: LoadField: r5 = r3->field_f
    //     0xbd8990: ldur            x5, [x3, #0xf]
    // 0xbd8994: r0 = BoxInt64Instr(r5)
    //     0xbd8994: sbfiz           x0, x5, #1, #0x1f
    //     0xbd8998: cmp             x5, x0, asr #1
    //     0xbd899c: b.eq            #0xbd89a8
    //     0xbd89a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd89a4: stur            x5, [x0, #7]
    // 0xbd89a8: r16 = <int>
    //     0xbd89a8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd89ac: stp             x2, x16, [SP, #8]
    // 0xbd89b0: str             x0, [SP]
    // 0xbd89b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd89b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd89b8: r0 = write()
    //     0xbd89b8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd89bc: ldur            x0, [fp, #-8]
    // 0xbd89c0: LoadField: r1 = r0->field_b
    //     0xbd89c0: ldur            w1, [x0, #0xb]
    // 0xbd89c4: DecompressPointer r1
    //     0xbd89c4: add             x1, x1, HEAP, lsl #32
    // 0xbd89c8: LoadField: r2 = r1->field_13
    //     0xbd89c8: ldur            w2, [x1, #0x13]
    // 0xbd89cc: LoadField: r1 = r0->field_13
    //     0xbd89cc: ldur            x1, [x0, #0x13]
    // 0xbd89d0: r3 = LoadInt32Instr(r2)
    //     0xbd89d0: sbfx            x3, x2, #1, #0x1f
    // 0xbd89d4: sub             x2, x3, x1
    // 0xbd89d8: cmp             x2, #1
    // 0xbd89dc: b.ge            #0xbd89ec
    // 0xbd89e0: mov             x1, x0
    // 0xbd89e4: r2 = 1
    //     0xbd89e4: movz            x2, #0x1
    // 0xbd89e8: r0 = _increaseBufferSize()
    //     0xbd89e8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd89ec: ldur            x2, [fp, #-8]
    // 0xbd89f0: ldur            x3, [fp, #-0x10]
    // 0xbd89f4: r4 = 2
    //     0xbd89f4: movz            x4, #0x2
    // 0xbd89f8: LoadField: r5 = r2->field_b
    //     0xbd89f8: ldur            w5, [x2, #0xb]
    // 0xbd89fc: DecompressPointer r5
    //     0xbd89fc: add             x5, x5, HEAP, lsl #32
    // 0xbd8a00: LoadField: r6 = r2->field_13
    //     0xbd8a00: ldur            x6, [x2, #0x13]
    // 0xbd8a04: add             x0, x6, #1
    // 0xbd8a08: StoreField: r2->field_13 = r0
    //     0xbd8a08: stur            x0, [x2, #0x13]
    // 0xbd8a0c: LoadField: r0 = r5->field_13
    //     0xbd8a0c: ldur            w0, [x5, #0x13]
    // 0xbd8a10: r1 = LoadInt32Instr(r0)
    //     0xbd8a10: sbfx            x1, x0, #1, #0x1f
    // 0xbd8a14: mov             x0, x1
    // 0xbd8a18: mov             x1, x6
    // 0xbd8a1c: cmp             x1, x0
    // 0xbd8a20: b.hs            #0xbd92e0
    // 0xbd8a24: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8a24: add             x0, x5, x6
    //     0xbd8a28: strb            w4, [x0, #0x17]
    // 0xbd8a2c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd8a2c: ldur            x4, [x3, #0x17]
    // 0xbd8a30: r0 = BoxInt64Instr(r4)
    //     0xbd8a30: sbfiz           x0, x4, #1, #0x1f
    //     0xbd8a34: cmp             x4, x0, asr #1
    //     0xbd8a38: b.eq            #0xbd8a44
    //     0xbd8a3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8a40: stur            x4, [x0, #7]
    // 0xbd8a44: r16 = <int>
    //     0xbd8a44: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8a48: stp             x2, x16, [SP, #8]
    // 0xbd8a4c: str             x0, [SP]
    // 0xbd8a50: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8a50: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8a54: r0 = write()
    //     0xbd8a54: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8a58: ldur            x0, [fp, #-8]
    // 0xbd8a5c: LoadField: r1 = r0->field_b
    //     0xbd8a5c: ldur            w1, [x0, #0xb]
    // 0xbd8a60: DecompressPointer r1
    //     0xbd8a60: add             x1, x1, HEAP, lsl #32
    // 0xbd8a64: LoadField: r2 = r1->field_13
    //     0xbd8a64: ldur            w2, [x1, #0x13]
    // 0xbd8a68: LoadField: r1 = r0->field_13
    //     0xbd8a68: ldur            x1, [x0, #0x13]
    // 0xbd8a6c: r3 = LoadInt32Instr(r2)
    //     0xbd8a6c: sbfx            x3, x2, #1, #0x1f
    // 0xbd8a70: sub             x2, x3, x1
    // 0xbd8a74: cmp             x2, #1
    // 0xbd8a78: b.ge            #0xbd8a88
    // 0xbd8a7c: mov             x1, x0
    // 0xbd8a80: r2 = 1
    //     0xbd8a80: movz            x2, #0x1
    // 0xbd8a84: r0 = _increaseBufferSize()
    //     0xbd8a84: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8a88: ldur            x2, [fp, #-8]
    // 0xbd8a8c: ldur            x3, [fp, #-0x10]
    // 0xbd8a90: r4 = 3
    //     0xbd8a90: movz            x4, #0x3
    // 0xbd8a94: LoadField: r5 = r2->field_b
    //     0xbd8a94: ldur            w5, [x2, #0xb]
    // 0xbd8a98: DecompressPointer r5
    //     0xbd8a98: add             x5, x5, HEAP, lsl #32
    // 0xbd8a9c: LoadField: r6 = r2->field_13
    //     0xbd8a9c: ldur            x6, [x2, #0x13]
    // 0xbd8aa0: add             x0, x6, #1
    // 0xbd8aa4: StoreField: r2->field_13 = r0
    //     0xbd8aa4: stur            x0, [x2, #0x13]
    // 0xbd8aa8: LoadField: r0 = r5->field_13
    //     0xbd8aa8: ldur            w0, [x5, #0x13]
    // 0xbd8aac: r1 = LoadInt32Instr(r0)
    //     0xbd8aac: sbfx            x1, x0, #1, #0x1f
    // 0xbd8ab0: mov             x0, x1
    // 0xbd8ab4: mov             x1, x6
    // 0xbd8ab8: cmp             x1, x0
    // 0xbd8abc: b.hs            #0xbd92e4
    // 0xbd8ac0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8ac0: add             x0, x5, x6
    //     0xbd8ac4: strb            w4, [x0, #0x17]
    // 0xbd8ac8: LoadField: r0 = r3->field_1f
    //     0xbd8ac8: ldur            w0, [x3, #0x1f]
    // 0xbd8acc: DecompressPointer r0
    //     0xbd8acc: add             x0, x0, HEAP, lsl #32
    // 0xbd8ad0: r16 = <String>
    //     0xbd8ad0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd8ad4: stp             x2, x16, [SP, #8]
    // 0xbd8ad8: str             x0, [SP]
    // 0xbd8adc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8adc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8ae0: r0 = write()
    //     0xbd8ae0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8ae4: ldur            x0, [fp, #-8]
    // 0xbd8ae8: LoadField: r1 = r0->field_b
    //     0xbd8ae8: ldur            w1, [x0, #0xb]
    // 0xbd8aec: DecompressPointer r1
    //     0xbd8aec: add             x1, x1, HEAP, lsl #32
    // 0xbd8af0: LoadField: r2 = r1->field_13
    //     0xbd8af0: ldur            w2, [x1, #0x13]
    // 0xbd8af4: LoadField: r1 = r0->field_13
    //     0xbd8af4: ldur            x1, [x0, #0x13]
    // 0xbd8af8: r3 = LoadInt32Instr(r2)
    //     0xbd8af8: sbfx            x3, x2, #1, #0x1f
    // 0xbd8afc: sub             x2, x3, x1
    // 0xbd8b00: cmp             x2, #1
    // 0xbd8b04: b.ge            #0xbd8b14
    // 0xbd8b08: mov             x1, x0
    // 0xbd8b0c: r2 = 1
    //     0xbd8b0c: movz            x2, #0x1
    // 0xbd8b10: r0 = _increaseBufferSize()
    //     0xbd8b10: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8b14: ldur            x2, [fp, #-8]
    // 0xbd8b18: ldur            x3, [fp, #-0x10]
    // 0xbd8b1c: r4 = 4
    //     0xbd8b1c: movz            x4, #0x4
    // 0xbd8b20: LoadField: r5 = r2->field_b
    //     0xbd8b20: ldur            w5, [x2, #0xb]
    // 0xbd8b24: DecompressPointer r5
    //     0xbd8b24: add             x5, x5, HEAP, lsl #32
    // 0xbd8b28: LoadField: r6 = r2->field_13
    //     0xbd8b28: ldur            x6, [x2, #0x13]
    // 0xbd8b2c: add             x0, x6, #1
    // 0xbd8b30: StoreField: r2->field_13 = r0
    //     0xbd8b30: stur            x0, [x2, #0x13]
    // 0xbd8b34: LoadField: r0 = r5->field_13
    //     0xbd8b34: ldur            w0, [x5, #0x13]
    // 0xbd8b38: r1 = LoadInt32Instr(r0)
    //     0xbd8b38: sbfx            x1, x0, #1, #0x1f
    // 0xbd8b3c: mov             x0, x1
    // 0xbd8b40: mov             x1, x6
    // 0xbd8b44: cmp             x1, x0
    // 0xbd8b48: b.hs            #0xbd92e8
    // 0xbd8b4c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8b4c: add             x0, x5, x6
    //     0xbd8b50: strb            w4, [x0, #0x17]
    // 0xbd8b54: LoadField: r0 = r3->field_23
    //     0xbd8b54: ldur            w0, [x3, #0x23]
    // 0xbd8b58: DecompressPointer r0
    //     0xbd8b58: add             x0, x0, HEAP, lsl #32
    // 0xbd8b5c: r16 = <String?>
    //     0xbd8b5c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd8b60: stp             x2, x16, [SP, #8]
    // 0xbd8b64: str             x0, [SP]
    // 0xbd8b68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8b68: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8b6c: r0 = write()
    //     0xbd8b6c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8b70: ldur            x0, [fp, #-8]
    // 0xbd8b74: LoadField: r1 = r0->field_b
    //     0xbd8b74: ldur            w1, [x0, #0xb]
    // 0xbd8b78: DecompressPointer r1
    //     0xbd8b78: add             x1, x1, HEAP, lsl #32
    // 0xbd8b7c: LoadField: r2 = r1->field_13
    //     0xbd8b7c: ldur            w2, [x1, #0x13]
    // 0xbd8b80: LoadField: r1 = r0->field_13
    //     0xbd8b80: ldur            x1, [x0, #0x13]
    // 0xbd8b84: r3 = LoadInt32Instr(r2)
    //     0xbd8b84: sbfx            x3, x2, #1, #0x1f
    // 0xbd8b88: sub             x2, x3, x1
    // 0xbd8b8c: cmp             x2, #1
    // 0xbd8b90: b.ge            #0xbd8ba0
    // 0xbd8b94: mov             x1, x0
    // 0xbd8b98: r2 = 1
    //     0xbd8b98: movz            x2, #0x1
    // 0xbd8b9c: r0 = _increaseBufferSize()
    //     0xbd8b9c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8ba0: ldur            x2, [fp, #-8]
    // 0xbd8ba4: ldur            x3, [fp, #-0x10]
    // 0xbd8ba8: r4 = 5
    //     0xbd8ba8: movz            x4, #0x5
    // 0xbd8bac: LoadField: r5 = r2->field_b
    //     0xbd8bac: ldur            w5, [x2, #0xb]
    // 0xbd8bb0: DecompressPointer r5
    //     0xbd8bb0: add             x5, x5, HEAP, lsl #32
    // 0xbd8bb4: LoadField: r6 = r2->field_13
    //     0xbd8bb4: ldur            x6, [x2, #0x13]
    // 0xbd8bb8: add             x0, x6, #1
    // 0xbd8bbc: StoreField: r2->field_13 = r0
    //     0xbd8bbc: stur            x0, [x2, #0x13]
    // 0xbd8bc0: LoadField: r0 = r5->field_13
    //     0xbd8bc0: ldur            w0, [x5, #0x13]
    // 0xbd8bc4: r1 = LoadInt32Instr(r0)
    //     0xbd8bc4: sbfx            x1, x0, #1, #0x1f
    // 0xbd8bc8: mov             x0, x1
    // 0xbd8bcc: mov             x1, x6
    // 0xbd8bd0: cmp             x1, x0
    // 0xbd8bd4: b.hs            #0xbd92ec
    // 0xbd8bd8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8bd8: add             x0, x5, x6
    //     0xbd8bdc: strb            w4, [x0, #0x17]
    // 0xbd8be0: LoadField: r0 = r3->field_27
    //     0xbd8be0: ldur            w0, [x3, #0x27]
    // 0xbd8be4: DecompressPointer r0
    //     0xbd8be4: add             x0, x0, HEAP, lsl #32
    // 0xbd8be8: r16 = <String?>
    //     0xbd8be8: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd8bec: stp             x2, x16, [SP, #8]
    // 0xbd8bf0: str             x0, [SP]
    // 0xbd8bf4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8bf4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8bf8: r0 = write()
    //     0xbd8bf8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8bfc: ldur            x0, [fp, #-8]
    // 0xbd8c00: LoadField: r1 = r0->field_b
    //     0xbd8c00: ldur            w1, [x0, #0xb]
    // 0xbd8c04: DecompressPointer r1
    //     0xbd8c04: add             x1, x1, HEAP, lsl #32
    // 0xbd8c08: LoadField: r2 = r1->field_13
    //     0xbd8c08: ldur            w2, [x1, #0x13]
    // 0xbd8c0c: LoadField: r1 = r0->field_13
    //     0xbd8c0c: ldur            x1, [x0, #0x13]
    // 0xbd8c10: r3 = LoadInt32Instr(r2)
    //     0xbd8c10: sbfx            x3, x2, #1, #0x1f
    // 0xbd8c14: sub             x2, x3, x1
    // 0xbd8c18: cmp             x2, #1
    // 0xbd8c1c: b.ge            #0xbd8c2c
    // 0xbd8c20: mov             x1, x0
    // 0xbd8c24: r2 = 1
    //     0xbd8c24: movz            x2, #0x1
    // 0xbd8c28: r0 = _increaseBufferSize()
    //     0xbd8c28: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8c2c: ldur            x2, [fp, #-8]
    // 0xbd8c30: ldur            x3, [fp, #-0x10]
    // 0xbd8c34: r4 = 6
    //     0xbd8c34: movz            x4, #0x6
    // 0xbd8c38: LoadField: r5 = r2->field_b
    //     0xbd8c38: ldur            w5, [x2, #0xb]
    // 0xbd8c3c: DecompressPointer r5
    //     0xbd8c3c: add             x5, x5, HEAP, lsl #32
    // 0xbd8c40: LoadField: r6 = r2->field_13
    //     0xbd8c40: ldur            x6, [x2, #0x13]
    // 0xbd8c44: add             x0, x6, #1
    // 0xbd8c48: StoreField: r2->field_13 = r0
    //     0xbd8c48: stur            x0, [x2, #0x13]
    // 0xbd8c4c: LoadField: r0 = r5->field_13
    //     0xbd8c4c: ldur            w0, [x5, #0x13]
    // 0xbd8c50: r1 = LoadInt32Instr(r0)
    //     0xbd8c50: sbfx            x1, x0, #1, #0x1f
    // 0xbd8c54: mov             x0, x1
    // 0xbd8c58: mov             x1, x6
    // 0xbd8c5c: cmp             x1, x0
    // 0xbd8c60: b.hs            #0xbd92f0
    // 0xbd8c64: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8c64: add             x0, x5, x6
    //     0xbd8c68: strb            w4, [x0, #0x17]
    // 0xbd8c6c: LoadField: r4 = r3->field_2b
    //     0xbd8c6c: ldur            x4, [x3, #0x2b]
    // 0xbd8c70: r0 = BoxInt64Instr(r4)
    //     0xbd8c70: sbfiz           x0, x4, #1, #0x1f
    //     0xbd8c74: cmp             x4, x0, asr #1
    //     0xbd8c78: b.eq            #0xbd8c84
    //     0xbd8c7c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8c80: stur            x4, [x0, #7]
    // 0xbd8c84: r16 = <int>
    //     0xbd8c84: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8c88: stp             x2, x16, [SP, #8]
    // 0xbd8c8c: str             x0, [SP]
    // 0xbd8c90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8c90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8c94: r0 = write()
    //     0xbd8c94: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8c98: ldur            x0, [fp, #-8]
    // 0xbd8c9c: LoadField: r1 = r0->field_b
    //     0xbd8c9c: ldur            w1, [x0, #0xb]
    // 0xbd8ca0: DecompressPointer r1
    //     0xbd8ca0: add             x1, x1, HEAP, lsl #32
    // 0xbd8ca4: LoadField: r2 = r1->field_13
    //     0xbd8ca4: ldur            w2, [x1, #0x13]
    // 0xbd8ca8: LoadField: r1 = r0->field_13
    //     0xbd8ca8: ldur            x1, [x0, #0x13]
    // 0xbd8cac: r3 = LoadInt32Instr(r2)
    //     0xbd8cac: sbfx            x3, x2, #1, #0x1f
    // 0xbd8cb0: sub             x2, x3, x1
    // 0xbd8cb4: cmp             x2, #1
    // 0xbd8cb8: b.ge            #0xbd8cc8
    // 0xbd8cbc: mov             x1, x0
    // 0xbd8cc0: r2 = 1
    //     0xbd8cc0: movz            x2, #0x1
    // 0xbd8cc4: r0 = _increaseBufferSize()
    //     0xbd8cc4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8cc8: ldur            x2, [fp, #-8]
    // 0xbd8ccc: ldur            x3, [fp, #-0x10]
    // 0xbd8cd0: r4 = 7
    //     0xbd8cd0: movz            x4, #0x7
    // 0xbd8cd4: LoadField: r5 = r2->field_b
    //     0xbd8cd4: ldur            w5, [x2, #0xb]
    // 0xbd8cd8: DecompressPointer r5
    //     0xbd8cd8: add             x5, x5, HEAP, lsl #32
    // 0xbd8cdc: LoadField: r6 = r2->field_13
    //     0xbd8cdc: ldur            x6, [x2, #0x13]
    // 0xbd8ce0: add             x0, x6, #1
    // 0xbd8ce4: StoreField: r2->field_13 = r0
    //     0xbd8ce4: stur            x0, [x2, #0x13]
    // 0xbd8ce8: LoadField: r0 = r5->field_13
    //     0xbd8ce8: ldur            w0, [x5, #0x13]
    // 0xbd8cec: r1 = LoadInt32Instr(r0)
    //     0xbd8cec: sbfx            x1, x0, #1, #0x1f
    // 0xbd8cf0: mov             x0, x1
    // 0xbd8cf4: mov             x1, x6
    // 0xbd8cf8: cmp             x1, x0
    // 0xbd8cfc: b.hs            #0xbd92f4
    // 0xbd8d00: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8d00: add             x0, x5, x6
    //     0xbd8d04: strb            w4, [x0, #0x17]
    // 0xbd8d08: LoadField: r4 = r3->field_33
    //     0xbd8d08: ldur            x4, [x3, #0x33]
    // 0xbd8d0c: r0 = BoxInt64Instr(r4)
    //     0xbd8d0c: sbfiz           x0, x4, #1, #0x1f
    //     0xbd8d10: cmp             x4, x0, asr #1
    //     0xbd8d14: b.eq            #0xbd8d20
    //     0xbd8d18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd8d1c: stur            x4, [x0, #7]
    // 0xbd8d20: r16 = <int>
    //     0xbd8d20: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd8d24: stp             x2, x16, [SP, #8]
    // 0xbd8d28: str             x0, [SP]
    // 0xbd8d2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8d2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8d30: r0 = write()
    //     0xbd8d30: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8d34: ldur            x0, [fp, #-8]
    // 0xbd8d38: LoadField: r1 = r0->field_b
    //     0xbd8d38: ldur            w1, [x0, #0xb]
    // 0xbd8d3c: DecompressPointer r1
    //     0xbd8d3c: add             x1, x1, HEAP, lsl #32
    // 0xbd8d40: LoadField: r2 = r1->field_13
    //     0xbd8d40: ldur            w2, [x1, #0x13]
    // 0xbd8d44: LoadField: r1 = r0->field_13
    //     0xbd8d44: ldur            x1, [x0, #0x13]
    // 0xbd8d48: r3 = LoadInt32Instr(r2)
    //     0xbd8d48: sbfx            x3, x2, #1, #0x1f
    // 0xbd8d4c: sub             x2, x3, x1
    // 0xbd8d50: cmp             x2, #1
    // 0xbd8d54: b.ge            #0xbd8d64
    // 0xbd8d58: mov             x1, x0
    // 0xbd8d5c: r2 = 1
    //     0xbd8d5c: movz            x2, #0x1
    // 0xbd8d60: r0 = _increaseBufferSize()
    //     0xbd8d60: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8d64: ldur            x2, [fp, #-8]
    // 0xbd8d68: ldur            x3, [fp, #-0x10]
    // 0xbd8d6c: r4 = 8
    //     0xbd8d6c: movz            x4, #0x8
    // 0xbd8d70: LoadField: r5 = r2->field_b
    //     0xbd8d70: ldur            w5, [x2, #0xb]
    // 0xbd8d74: DecompressPointer r5
    //     0xbd8d74: add             x5, x5, HEAP, lsl #32
    // 0xbd8d78: LoadField: r6 = r2->field_13
    //     0xbd8d78: ldur            x6, [x2, #0x13]
    // 0xbd8d7c: add             x0, x6, #1
    // 0xbd8d80: StoreField: r2->field_13 = r0
    //     0xbd8d80: stur            x0, [x2, #0x13]
    // 0xbd8d84: LoadField: r0 = r5->field_13
    //     0xbd8d84: ldur            w0, [x5, #0x13]
    // 0xbd8d88: r1 = LoadInt32Instr(r0)
    //     0xbd8d88: sbfx            x1, x0, #1, #0x1f
    // 0xbd8d8c: mov             x0, x1
    // 0xbd8d90: mov             x1, x6
    // 0xbd8d94: cmp             x1, x0
    // 0xbd8d98: b.hs            #0xbd92f8
    // 0xbd8d9c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8d9c: add             x0, x5, x6
    //     0xbd8da0: strb            w4, [x0, #0x17]
    // 0xbd8da4: LoadField: r0 = r3->field_3b
    //     0xbd8da4: ldur            w0, [x3, #0x3b]
    // 0xbd8da8: DecompressPointer r0
    //     0xbd8da8: add             x0, x0, HEAP, lsl #32
    // 0xbd8dac: r16 = <List?>
    //     0xbd8dac: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0a0] TypeArguments: <List?>
    //     0xbd8db0: ldr             x16, [x16, #0xa0]
    // 0xbd8db4: stp             x2, x16, [SP, #8]
    // 0xbd8db8: str             x0, [SP]
    // 0xbd8dbc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8dbc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8dc0: r0 = write()
    //     0xbd8dc0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8dc4: ldur            x0, [fp, #-8]
    // 0xbd8dc8: LoadField: r1 = r0->field_b
    //     0xbd8dc8: ldur            w1, [x0, #0xb]
    // 0xbd8dcc: DecompressPointer r1
    //     0xbd8dcc: add             x1, x1, HEAP, lsl #32
    // 0xbd8dd0: LoadField: r2 = r1->field_13
    //     0xbd8dd0: ldur            w2, [x1, #0x13]
    // 0xbd8dd4: LoadField: r1 = r0->field_13
    //     0xbd8dd4: ldur            x1, [x0, #0x13]
    // 0xbd8dd8: r3 = LoadInt32Instr(r2)
    //     0xbd8dd8: sbfx            x3, x2, #1, #0x1f
    // 0xbd8ddc: sub             x2, x3, x1
    // 0xbd8de0: cmp             x2, #1
    // 0xbd8de4: b.ge            #0xbd8df4
    // 0xbd8de8: mov             x1, x0
    // 0xbd8dec: r2 = 1
    //     0xbd8dec: movz            x2, #0x1
    // 0xbd8df0: r0 = _increaseBufferSize()
    //     0xbd8df0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8df4: ldur            x2, [fp, #-8]
    // 0xbd8df8: ldur            x3, [fp, #-0x10]
    // 0xbd8dfc: r4 = 9
    //     0xbd8dfc: movz            x4, #0x9
    // 0xbd8e00: LoadField: r5 = r2->field_b
    //     0xbd8e00: ldur            w5, [x2, #0xb]
    // 0xbd8e04: DecompressPointer r5
    //     0xbd8e04: add             x5, x5, HEAP, lsl #32
    // 0xbd8e08: LoadField: r6 = r2->field_13
    //     0xbd8e08: ldur            x6, [x2, #0x13]
    // 0xbd8e0c: add             x0, x6, #1
    // 0xbd8e10: StoreField: r2->field_13 = r0
    //     0xbd8e10: stur            x0, [x2, #0x13]
    // 0xbd8e14: LoadField: r0 = r5->field_13
    //     0xbd8e14: ldur            w0, [x5, #0x13]
    // 0xbd8e18: r1 = LoadInt32Instr(r0)
    //     0xbd8e18: sbfx            x1, x0, #1, #0x1f
    // 0xbd8e1c: mov             x0, x1
    // 0xbd8e20: mov             x1, x6
    // 0xbd8e24: cmp             x1, x0
    // 0xbd8e28: b.hs            #0xbd92fc
    // 0xbd8e2c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8e2c: add             x0, x5, x6
    //     0xbd8e30: strb            w4, [x0, #0x17]
    // 0xbd8e34: LoadField: r0 = r3->field_3f
    //     0xbd8e34: ldur            w0, [x3, #0x3f]
    // 0xbd8e38: DecompressPointer r0
    //     0xbd8e38: add             x0, x0, HEAP, lsl #32
    // 0xbd8e3c: r16 = <bool?>
    //     0xbd8e3c: add             x16, PP, #9, lsl #12  ; [pp+0x9018] TypeArguments: <bool?>
    //     0xbd8e40: ldr             x16, [x16, #0x18]
    // 0xbd8e44: stp             x2, x16, [SP, #8]
    // 0xbd8e48: str             x0, [SP]
    // 0xbd8e4c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8e4c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8e50: r0 = write()
    //     0xbd8e50: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8e54: ldur            x0, [fp, #-8]
    // 0xbd8e58: LoadField: r1 = r0->field_b
    //     0xbd8e58: ldur            w1, [x0, #0xb]
    // 0xbd8e5c: DecompressPointer r1
    //     0xbd8e5c: add             x1, x1, HEAP, lsl #32
    // 0xbd8e60: LoadField: r2 = r1->field_13
    //     0xbd8e60: ldur            w2, [x1, #0x13]
    // 0xbd8e64: LoadField: r1 = r0->field_13
    //     0xbd8e64: ldur            x1, [x0, #0x13]
    // 0xbd8e68: r3 = LoadInt32Instr(r2)
    //     0xbd8e68: sbfx            x3, x2, #1, #0x1f
    // 0xbd8e6c: sub             x2, x3, x1
    // 0xbd8e70: cmp             x2, #1
    // 0xbd8e74: b.ge            #0xbd8e84
    // 0xbd8e78: mov             x1, x0
    // 0xbd8e7c: r2 = 1
    //     0xbd8e7c: movz            x2, #0x1
    // 0xbd8e80: r0 = _increaseBufferSize()
    //     0xbd8e80: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8e84: ldur            x2, [fp, #-8]
    // 0xbd8e88: ldur            x3, [fp, #-0x10]
    // 0xbd8e8c: r4 = 10
    //     0xbd8e8c: movz            x4, #0xa
    // 0xbd8e90: LoadField: r5 = r2->field_b
    //     0xbd8e90: ldur            w5, [x2, #0xb]
    // 0xbd8e94: DecompressPointer r5
    //     0xbd8e94: add             x5, x5, HEAP, lsl #32
    // 0xbd8e98: LoadField: r6 = r2->field_13
    //     0xbd8e98: ldur            x6, [x2, #0x13]
    // 0xbd8e9c: add             x0, x6, #1
    // 0xbd8ea0: StoreField: r2->field_13 = r0
    //     0xbd8ea0: stur            x0, [x2, #0x13]
    // 0xbd8ea4: LoadField: r0 = r5->field_13
    //     0xbd8ea4: ldur            w0, [x5, #0x13]
    // 0xbd8ea8: r1 = LoadInt32Instr(r0)
    //     0xbd8ea8: sbfx            x1, x0, #1, #0x1f
    // 0xbd8eac: mov             x0, x1
    // 0xbd8eb0: mov             x1, x6
    // 0xbd8eb4: cmp             x1, x0
    // 0xbd8eb8: b.hs            #0xbd9300
    // 0xbd8ebc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8ebc: add             x0, x5, x6
    //     0xbd8ec0: strb            w4, [x0, #0x17]
    // 0xbd8ec4: LoadField: r0 = r3->field_43
    //     0xbd8ec4: ldur            w0, [x3, #0x43]
    // 0xbd8ec8: DecompressPointer r0
    //     0xbd8ec8: add             x0, x0, HEAP, lsl #32
    // 0xbd8ecc: r16 = <bool>
    //     0xbd8ecc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbd8ed0: stp             x2, x16, [SP, #8]
    // 0xbd8ed4: str             x0, [SP]
    // 0xbd8ed8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8ed8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8edc: r0 = write()
    //     0xbd8edc: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8ee0: ldur            x0, [fp, #-8]
    // 0xbd8ee4: LoadField: r1 = r0->field_b
    //     0xbd8ee4: ldur            w1, [x0, #0xb]
    // 0xbd8ee8: DecompressPointer r1
    //     0xbd8ee8: add             x1, x1, HEAP, lsl #32
    // 0xbd8eec: LoadField: r2 = r1->field_13
    //     0xbd8eec: ldur            w2, [x1, #0x13]
    // 0xbd8ef0: LoadField: r1 = r0->field_13
    //     0xbd8ef0: ldur            x1, [x0, #0x13]
    // 0xbd8ef4: r3 = LoadInt32Instr(r2)
    //     0xbd8ef4: sbfx            x3, x2, #1, #0x1f
    // 0xbd8ef8: sub             x2, x3, x1
    // 0xbd8efc: cmp             x2, #1
    // 0xbd8f00: b.ge            #0xbd8f10
    // 0xbd8f04: mov             x1, x0
    // 0xbd8f08: r2 = 1
    //     0xbd8f08: movz            x2, #0x1
    // 0xbd8f0c: r0 = _increaseBufferSize()
    //     0xbd8f0c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8f10: ldur            x2, [fp, #-8]
    // 0xbd8f14: ldur            x3, [fp, #-0x10]
    // 0xbd8f18: r4 = 11
    //     0xbd8f18: movz            x4, #0xb
    // 0xbd8f1c: LoadField: r5 = r2->field_b
    //     0xbd8f1c: ldur            w5, [x2, #0xb]
    // 0xbd8f20: DecompressPointer r5
    //     0xbd8f20: add             x5, x5, HEAP, lsl #32
    // 0xbd8f24: LoadField: r6 = r2->field_13
    //     0xbd8f24: ldur            x6, [x2, #0x13]
    // 0xbd8f28: add             x0, x6, #1
    // 0xbd8f2c: StoreField: r2->field_13 = r0
    //     0xbd8f2c: stur            x0, [x2, #0x13]
    // 0xbd8f30: LoadField: r0 = r5->field_13
    //     0xbd8f30: ldur            w0, [x5, #0x13]
    // 0xbd8f34: r1 = LoadInt32Instr(r0)
    //     0xbd8f34: sbfx            x1, x0, #1, #0x1f
    // 0xbd8f38: mov             x0, x1
    // 0xbd8f3c: mov             x1, x6
    // 0xbd8f40: cmp             x1, x0
    // 0xbd8f44: b.hs            #0xbd9304
    // 0xbd8f48: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8f48: add             x0, x5, x6
    //     0xbd8f4c: strb            w4, [x0, #0x17]
    // 0xbd8f50: LoadField: r0 = r3->field_47
    //     0xbd8f50: ldur            w0, [x3, #0x47]
    // 0xbd8f54: DecompressPointer r0
    //     0xbd8f54: add             x0, x0, HEAP, lsl #32
    // 0xbd8f58: r16 = <DateTime>
    //     0xbd8f58: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0xbd8f5c: ldr             x16, [x16, #0xdd8]
    // 0xbd8f60: stp             x2, x16, [SP, #8]
    // 0xbd8f64: str             x0, [SP]
    // 0xbd8f68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8f68: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8f6c: r0 = write()
    //     0xbd8f6c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8f70: ldur            x0, [fp, #-8]
    // 0xbd8f74: LoadField: r1 = r0->field_b
    //     0xbd8f74: ldur            w1, [x0, #0xb]
    // 0xbd8f78: DecompressPointer r1
    //     0xbd8f78: add             x1, x1, HEAP, lsl #32
    // 0xbd8f7c: LoadField: r2 = r1->field_13
    //     0xbd8f7c: ldur            w2, [x1, #0x13]
    // 0xbd8f80: LoadField: r1 = r0->field_13
    //     0xbd8f80: ldur            x1, [x0, #0x13]
    // 0xbd8f84: r3 = LoadInt32Instr(r2)
    //     0xbd8f84: sbfx            x3, x2, #1, #0x1f
    // 0xbd8f88: sub             x2, x3, x1
    // 0xbd8f8c: cmp             x2, #1
    // 0xbd8f90: b.ge            #0xbd8fa0
    // 0xbd8f94: mov             x1, x0
    // 0xbd8f98: r2 = 1
    //     0xbd8f98: movz            x2, #0x1
    // 0xbd8f9c: r0 = _increaseBufferSize()
    //     0xbd8f9c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd8fa0: ldur            x2, [fp, #-8]
    // 0xbd8fa4: ldur            x3, [fp, #-0x10]
    // 0xbd8fa8: r4 = 12
    //     0xbd8fa8: movz            x4, #0xc
    // 0xbd8fac: LoadField: r5 = r2->field_b
    //     0xbd8fac: ldur            w5, [x2, #0xb]
    // 0xbd8fb0: DecompressPointer r5
    //     0xbd8fb0: add             x5, x5, HEAP, lsl #32
    // 0xbd8fb4: LoadField: r6 = r2->field_13
    //     0xbd8fb4: ldur            x6, [x2, #0x13]
    // 0xbd8fb8: add             x0, x6, #1
    // 0xbd8fbc: StoreField: r2->field_13 = r0
    //     0xbd8fbc: stur            x0, [x2, #0x13]
    // 0xbd8fc0: LoadField: r0 = r5->field_13
    //     0xbd8fc0: ldur            w0, [x5, #0x13]
    // 0xbd8fc4: r1 = LoadInt32Instr(r0)
    //     0xbd8fc4: sbfx            x1, x0, #1, #0x1f
    // 0xbd8fc8: mov             x0, x1
    // 0xbd8fcc: mov             x1, x6
    // 0xbd8fd0: cmp             x1, x0
    // 0xbd8fd4: b.hs            #0xbd9308
    // 0xbd8fd8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd8fd8: add             x0, x5, x6
    //     0xbd8fdc: strb            w4, [x0, #0x17]
    // 0xbd8fe0: LoadField: r0 = r3->field_4b
    //     0xbd8fe0: ldur            w0, [x3, #0x4b]
    // 0xbd8fe4: DecompressPointer r0
    //     0xbd8fe4: add             x0, x0, HEAP, lsl #32
    // 0xbd8fe8: r16 = <String?>
    //     0xbd8fe8: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd8fec: stp             x2, x16, [SP, #8]
    // 0xbd8ff0: str             x0, [SP]
    // 0xbd8ff4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd8ff4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd8ff8: r0 = write()
    //     0xbd8ff8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd8ffc: ldur            x0, [fp, #-8]
    // 0xbd9000: LoadField: r1 = r0->field_b
    //     0xbd9000: ldur            w1, [x0, #0xb]
    // 0xbd9004: DecompressPointer r1
    //     0xbd9004: add             x1, x1, HEAP, lsl #32
    // 0xbd9008: LoadField: r2 = r1->field_13
    //     0xbd9008: ldur            w2, [x1, #0x13]
    // 0xbd900c: LoadField: r1 = r0->field_13
    //     0xbd900c: ldur            x1, [x0, #0x13]
    // 0xbd9010: r3 = LoadInt32Instr(r2)
    //     0xbd9010: sbfx            x3, x2, #1, #0x1f
    // 0xbd9014: sub             x2, x3, x1
    // 0xbd9018: cmp             x2, #1
    // 0xbd901c: b.ge            #0xbd902c
    // 0xbd9020: mov             x1, x0
    // 0xbd9024: r2 = 1
    //     0xbd9024: movz            x2, #0x1
    // 0xbd9028: r0 = _increaseBufferSize()
    //     0xbd9028: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd902c: ldur            x2, [fp, #-8]
    // 0xbd9030: ldur            x3, [fp, #-0x10]
    // 0xbd9034: r4 = 13
    //     0xbd9034: movz            x4, #0xd
    // 0xbd9038: LoadField: r5 = r2->field_b
    //     0xbd9038: ldur            w5, [x2, #0xb]
    // 0xbd903c: DecompressPointer r5
    //     0xbd903c: add             x5, x5, HEAP, lsl #32
    // 0xbd9040: LoadField: r6 = r2->field_13
    //     0xbd9040: ldur            x6, [x2, #0x13]
    // 0xbd9044: add             x0, x6, #1
    // 0xbd9048: StoreField: r2->field_13 = r0
    //     0xbd9048: stur            x0, [x2, #0x13]
    // 0xbd904c: LoadField: r0 = r5->field_13
    //     0xbd904c: ldur            w0, [x5, #0x13]
    // 0xbd9050: r1 = LoadInt32Instr(r0)
    //     0xbd9050: sbfx            x1, x0, #1, #0x1f
    // 0xbd9054: mov             x0, x1
    // 0xbd9058: mov             x1, x6
    // 0xbd905c: cmp             x1, x0
    // 0xbd9060: b.hs            #0xbd930c
    // 0xbd9064: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9064: add             x0, x5, x6
    //     0xbd9068: strb            w4, [x0, #0x17]
    // 0xbd906c: LoadField: r0 = r3->field_4f
    //     0xbd906c: ldur            w0, [x3, #0x4f]
    // 0xbd9070: DecompressPointer r0
    //     0xbd9070: add             x0, x0, HEAP, lsl #32
    // 0xbd9074: r16 = <String?>
    //     0xbd9074: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd9078: stp             x2, x16, [SP, #8]
    // 0xbd907c: str             x0, [SP]
    // 0xbd9080: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9080: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9084: r0 = write()
    //     0xbd9084: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9088: ldur            x0, [fp, #-8]
    // 0xbd908c: LoadField: r1 = r0->field_b
    //     0xbd908c: ldur            w1, [x0, #0xb]
    // 0xbd9090: DecompressPointer r1
    //     0xbd9090: add             x1, x1, HEAP, lsl #32
    // 0xbd9094: LoadField: r2 = r1->field_13
    //     0xbd9094: ldur            w2, [x1, #0x13]
    // 0xbd9098: LoadField: r1 = r0->field_13
    //     0xbd9098: ldur            x1, [x0, #0x13]
    // 0xbd909c: r3 = LoadInt32Instr(r2)
    //     0xbd909c: sbfx            x3, x2, #1, #0x1f
    // 0xbd90a0: sub             x2, x3, x1
    // 0xbd90a4: cmp             x2, #1
    // 0xbd90a8: b.ge            #0xbd90b8
    // 0xbd90ac: mov             x1, x0
    // 0xbd90b0: r2 = 1
    //     0xbd90b0: movz            x2, #0x1
    // 0xbd90b4: r0 = _increaseBufferSize()
    //     0xbd90b4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd90b8: ldur            x2, [fp, #-8]
    // 0xbd90bc: ldur            x3, [fp, #-0x10]
    // 0xbd90c0: r4 = 14
    //     0xbd90c0: movz            x4, #0xe
    // 0xbd90c4: LoadField: r5 = r2->field_b
    //     0xbd90c4: ldur            w5, [x2, #0xb]
    // 0xbd90c8: DecompressPointer r5
    //     0xbd90c8: add             x5, x5, HEAP, lsl #32
    // 0xbd90cc: LoadField: r6 = r2->field_13
    //     0xbd90cc: ldur            x6, [x2, #0x13]
    // 0xbd90d0: add             x0, x6, #1
    // 0xbd90d4: StoreField: r2->field_13 = r0
    //     0xbd90d4: stur            x0, [x2, #0x13]
    // 0xbd90d8: LoadField: r0 = r5->field_13
    //     0xbd90d8: ldur            w0, [x5, #0x13]
    // 0xbd90dc: r1 = LoadInt32Instr(r0)
    //     0xbd90dc: sbfx            x1, x0, #1, #0x1f
    // 0xbd90e0: mov             x0, x1
    // 0xbd90e4: mov             x1, x6
    // 0xbd90e8: cmp             x1, x0
    // 0xbd90ec: b.hs            #0xbd9310
    // 0xbd90f0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd90f0: add             x0, x5, x6
    //     0xbd90f4: strb            w4, [x0, #0x17]
    // 0xbd90f8: LoadField: r0 = r3->field_53
    //     0xbd90f8: ldur            w0, [x3, #0x53]
    // 0xbd90fc: DecompressPointer r0
    //     0xbd90fc: add             x0, x0, HEAP, lsl #32
    // 0xbd9100: r16 = <String?>
    //     0xbd9100: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd9104: stp             x2, x16, [SP, #8]
    // 0xbd9108: str             x0, [SP]
    // 0xbd910c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd910c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9110: r0 = write()
    //     0xbd9110: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9114: ldur            x0, [fp, #-8]
    // 0xbd9118: LoadField: r1 = r0->field_b
    //     0xbd9118: ldur            w1, [x0, #0xb]
    // 0xbd911c: DecompressPointer r1
    //     0xbd911c: add             x1, x1, HEAP, lsl #32
    // 0xbd9120: LoadField: r2 = r1->field_13
    //     0xbd9120: ldur            w2, [x1, #0x13]
    // 0xbd9124: LoadField: r1 = r0->field_13
    //     0xbd9124: ldur            x1, [x0, #0x13]
    // 0xbd9128: r3 = LoadInt32Instr(r2)
    //     0xbd9128: sbfx            x3, x2, #1, #0x1f
    // 0xbd912c: sub             x2, x3, x1
    // 0xbd9130: cmp             x2, #1
    // 0xbd9134: b.ge            #0xbd9144
    // 0xbd9138: mov             x1, x0
    // 0xbd913c: r2 = 1
    //     0xbd913c: movz            x2, #0x1
    // 0xbd9140: r0 = _increaseBufferSize()
    //     0xbd9140: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9144: ldur            x2, [fp, #-8]
    // 0xbd9148: ldur            x3, [fp, #-0x10]
    // 0xbd914c: r4 = 15
    //     0xbd914c: movz            x4, #0xf
    // 0xbd9150: LoadField: r5 = r2->field_b
    //     0xbd9150: ldur            w5, [x2, #0xb]
    // 0xbd9154: DecompressPointer r5
    //     0xbd9154: add             x5, x5, HEAP, lsl #32
    // 0xbd9158: LoadField: r6 = r2->field_13
    //     0xbd9158: ldur            x6, [x2, #0x13]
    // 0xbd915c: add             x0, x6, #1
    // 0xbd9160: StoreField: r2->field_13 = r0
    //     0xbd9160: stur            x0, [x2, #0x13]
    // 0xbd9164: LoadField: r0 = r5->field_13
    //     0xbd9164: ldur            w0, [x5, #0x13]
    // 0xbd9168: r1 = LoadInt32Instr(r0)
    //     0xbd9168: sbfx            x1, x0, #1, #0x1f
    // 0xbd916c: mov             x0, x1
    // 0xbd9170: mov             x1, x6
    // 0xbd9174: cmp             x1, x0
    // 0xbd9178: b.hs            #0xbd9314
    // 0xbd917c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd917c: add             x0, x5, x6
    //     0xbd9180: strb            w4, [x0, #0x17]
    // 0xbd9184: LoadField: r0 = r3->field_57
    //     0xbd9184: ldur            w0, [x3, #0x57]
    // 0xbd9188: DecompressPointer r0
    //     0xbd9188: add             x0, x0, HEAP, lsl #32
    // 0xbd918c: r16 = <String?>
    //     0xbd918c: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd9190: stp             x2, x16, [SP, #8]
    // 0xbd9194: str             x0, [SP]
    // 0xbd9198: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9198: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd919c: r0 = write()
    //     0xbd919c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd91a0: ldur            x0, [fp, #-8]
    // 0xbd91a4: LoadField: r1 = r0->field_b
    //     0xbd91a4: ldur            w1, [x0, #0xb]
    // 0xbd91a8: DecompressPointer r1
    //     0xbd91a8: add             x1, x1, HEAP, lsl #32
    // 0xbd91ac: LoadField: r2 = r1->field_13
    //     0xbd91ac: ldur            w2, [x1, #0x13]
    // 0xbd91b0: LoadField: r1 = r0->field_13
    //     0xbd91b0: ldur            x1, [x0, #0x13]
    // 0xbd91b4: r3 = LoadInt32Instr(r2)
    //     0xbd91b4: sbfx            x3, x2, #1, #0x1f
    // 0xbd91b8: sub             x2, x3, x1
    // 0xbd91bc: cmp             x2, #1
    // 0xbd91c0: b.ge            #0xbd91d0
    // 0xbd91c4: mov             x1, x0
    // 0xbd91c8: r2 = 1
    //     0xbd91c8: movz            x2, #0x1
    // 0xbd91cc: r0 = _increaseBufferSize()
    //     0xbd91cc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd91d0: ldur            x2, [fp, #-8]
    // 0xbd91d4: ldur            x3, [fp, #-0x10]
    // 0xbd91d8: r4 = 16
    //     0xbd91d8: movz            x4, #0x10
    // 0xbd91dc: LoadField: r5 = r2->field_b
    //     0xbd91dc: ldur            w5, [x2, #0xb]
    // 0xbd91e0: DecompressPointer r5
    //     0xbd91e0: add             x5, x5, HEAP, lsl #32
    // 0xbd91e4: LoadField: r6 = r2->field_13
    //     0xbd91e4: ldur            x6, [x2, #0x13]
    // 0xbd91e8: add             x0, x6, #1
    // 0xbd91ec: StoreField: r2->field_13 = r0
    //     0xbd91ec: stur            x0, [x2, #0x13]
    // 0xbd91f0: LoadField: r0 = r5->field_13
    //     0xbd91f0: ldur            w0, [x5, #0x13]
    // 0xbd91f4: r1 = LoadInt32Instr(r0)
    //     0xbd91f4: sbfx            x1, x0, #1, #0x1f
    // 0xbd91f8: mov             x0, x1
    // 0xbd91fc: mov             x1, x6
    // 0xbd9200: cmp             x1, x0
    // 0xbd9204: b.hs            #0xbd9318
    // 0xbd9208: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9208: add             x0, x5, x6
    //     0xbd920c: strb            w4, [x0, #0x17]
    // 0xbd9210: LoadField: r0 = r3->field_5b
    //     0xbd9210: ldur            w0, [x3, #0x5b]
    // 0xbd9214: DecompressPointer r0
    //     0xbd9214: add             x0, x0, HEAP, lsl #32
    // 0xbd9218: r16 = <String?>
    //     0xbd9218: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xbd921c: stp             x2, x16, [SP, #8]
    // 0xbd9220: str             x0, [SP]
    // 0xbd9224: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9224: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9228: r0 = write()
    //     0xbd9228: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd922c: ldur            x0, [fp, #-8]
    // 0xbd9230: LoadField: r1 = r0->field_b
    //     0xbd9230: ldur            w1, [x0, #0xb]
    // 0xbd9234: DecompressPointer r1
    //     0xbd9234: add             x1, x1, HEAP, lsl #32
    // 0xbd9238: LoadField: r2 = r1->field_13
    //     0xbd9238: ldur            w2, [x1, #0x13]
    // 0xbd923c: LoadField: r1 = r0->field_13
    //     0xbd923c: ldur            x1, [x0, #0x13]
    // 0xbd9240: r3 = LoadInt32Instr(r2)
    //     0xbd9240: sbfx            x3, x2, #1, #0x1f
    // 0xbd9244: sub             x2, x3, x1
    // 0xbd9248: cmp             x2, #1
    // 0xbd924c: b.ge            #0xbd925c
    // 0xbd9250: mov             x1, x0
    // 0xbd9254: r2 = 1
    //     0xbd9254: movz            x2, #0x1
    // 0xbd9258: r0 = _increaseBufferSize()
    //     0xbd9258: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd925c: ldur            x2, [fp, #-8]
    // 0xbd9260: ldur            x3, [fp, #-0x10]
    // 0xbd9264: r4 = 17
    //     0xbd9264: movz            x4, #0x11
    // 0xbd9268: LoadField: r5 = r2->field_b
    //     0xbd9268: ldur            w5, [x2, #0xb]
    // 0xbd926c: DecompressPointer r5
    //     0xbd926c: add             x5, x5, HEAP, lsl #32
    // 0xbd9270: LoadField: r6 = r2->field_13
    //     0xbd9270: ldur            x6, [x2, #0x13]
    // 0xbd9274: add             x0, x6, #1
    // 0xbd9278: StoreField: r2->field_13 = r0
    //     0xbd9278: stur            x0, [x2, #0x13]
    // 0xbd927c: LoadField: r0 = r5->field_13
    //     0xbd927c: ldur            w0, [x5, #0x13]
    // 0xbd9280: r1 = LoadInt32Instr(r0)
    //     0xbd9280: sbfx            x1, x0, #1, #0x1f
    // 0xbd9284: mov             x0, x1
    // 0xbd9288: mov             x1, x6
    // 0xbd928c: cmp             x1, x0
    // 0xbd9290: b.hs            #0xbd931c
    // 0xbd9294: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9294: add             x0, x5, x6
    //     0xbd9298: strb            w4, [x0, #0x17]
    // 0xbd929c: LoadField: r0 = r3->field_5f
    //     0xbd929c: ldur            w0, [x3, #0x5f]
    // 0xbd92a0: DecompressPointer r0
    //     0xbd92a0: add             x0, x0, HEAP, lsl #32
    // 0xbd92a4: r16 = <List<Tajweed>>
    //     0xbd92a4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b0a8] TypeArguments: <List<Tajweed>>
    //     0xbd92a8: ldr             x16, [x16, #0xa8]
    // 0xbd92ac: stp             x2, x16, [SP, #8]
    // 0xbd92b0: str             x0, [SP]
    // 0xbd92b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd92b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd92b8: r0 = write()
    //     0xbd92b8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd92bc: r0 = Null
    //     0xbd92bc: mov             x0, NULL
    // 0xbd92c0: LeaveFrame
    //     0xbd92c0: mov             SP, fp
    //     0xbd92c4: ldp             fp, lr, [SP], #0x10
    // 0xbd92c8: ret
    //     0xbd92c8: ret             
    // 0xbd92cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd92cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd92d0: b               #0xbd87fc
    // 0xbd92d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd92fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd92fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9300: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9300: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9304: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9304: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9308: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9308: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd930c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd930c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9310: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9310: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9314: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9314: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9318: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9318: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd931c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd931c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf03f0, size: 0x24
    // 0xbf03f0: r1 = 48
    //     0xbf03f0: movz            x1, #0x30
    // 0xbf03f4: r16 = LoadInt32Instr(r1)
    //     0xbf03f4: sbfx            x16, x1, #1, #0x1f
    // 0xbf03f8: r17 = 11601
    //     0xbf03f8: movz            x17, #0x2d51
    // 0xbf03fc: mul             x0, x16, x17
    // 0xbf0400: umulh           x16, x16, x17
    // 0xbf0404: eor             x0, x0, x16
    // 0xbf0408: r0 = 0
    //     0xbf0408: eor             x0, x0, x0, lsr #32
    // 0xbf040c: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0410: ret
    //     0xbf0410: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd77184, size: 0x9c
    // 0xd77184: EnterFrame
    //     0xd77184: stp             fp, lr, [SP, #-0x10]!
    //     0xd77188: mov             fp, SP
    // 0xd7718c: AllocStack(0x10)
    //     0xd7718c: sub             SP, SP, #0x10
    // 0xd77190: CheckStackOverflow
    //     0xd77190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd77194: cmp             SP, x16
    //     0xd77198: b.ls            #0xd77218
    // 0xd7719c: ldr             x0, [fp, #0x10]
    // 0xd771a0: cmp             w0, NULL
    // 0xd771a4: b.ne            #0xd771b8
    // 0xd771a8: r0 = false
    //     0xd771a8: add             x0, NULL, #0x30  ; false
    // 0xd771ac: LeaveFrame
    //     0xd771ac: mov             SP, fp
    //     0xd771b0: ldp             fp, lr, [SP], #0x10
    // 0xd771b4: ret
    //     0xd771b4: ret             
    // 0xd771b8: ldr             x1, [fp, #0x18]
    // 0xd771bc: cmp             w1, w0
    // 0xd771c0: b.ne            #0xd771cc
    // 0xd771c4: r0 = true
    //     0xd771c4: add             x0, NULL, #0x20  ; true
    // 0xd771c8: b               #0xd7720c
    // 0xd771cc: r1 = 60
    //     0xd771cc: movz            x1, #0x3c
    // 0xd771d0: branchIfSmi(r0, 0xd771dc)
    //     0xd771d0: tbz             w0, #0, #0xd771dc
    // 0xd771d4: r1 = LoadClassIdInstr(r0)
    //     0xd771d4: ldur            x1, [x0, #-1]
    //     0xd771d8: ubfx            x1, x1, #0xc, #0x14
    // 0xd771dc: cmp             x1, #0x66a
    // 0xd771e0: b.ne            #0xd77208
    // 0xd771e4: r16 = VerseAdapter
    //     0xd771e4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b088] Type: VerseAdapter
    //     0xd771e8: ldr             x16, [x16, #0x88]
    // 0xd771ec: r30 = VerseAdapter
    //     0xd771ec: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b088] Type: VerseAdapter
    //     0xd771f0: ldr             lr, [lr, #0x88]
    // 0xd771f4: stp             lr, x16, [SP]
    // 0xd771f8: r0 = ==()
    //     0xd771f8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd771fc: tbnz            w0, #4, #0xd77208
    // 0xd77200: r0 = true
    //     0xd77200: add             x0, NULL, #0x20  ; true
    // 0xd77204: b               #0xd7720c
    // 0xd77208: r0 = false
    //     0xd77208: add             x0, NULL, #0x30  ; false
    // 0xd7720c: LeaveFrame
    //     0xd7720c: mov             SP, fp
    //     0xd77210: ldp             fp, lr, [SP], #0x10
    // 0xd77214: ret
    //     0xd77214: ret             
    // 0xd77218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd77218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7721c: b               #0xd7719c
  }
}

// class id: 6833, size: 0x14, field offset: 0x14
enum QuranTranslationLanguage extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d3d4, size: 0x64
    // 0xc4d3d4: EnterFrame
    //     0xc4d3d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d3d8: mov             fp, SP
    // 0xc4d3dc: AllocStack(0x10)
    //     0xc4d3dc: sub             SP, SP, #0x10
    // 0xc4d3e0: SetupParameters(QuranTranslationLanguage this /* r1 => r0, fp-0x8 */)
    //     0xc4d3e0: mov             x0, x1
    //     0xc4d3e4: stur            x1, [fp, #-8]
    // 0xc4d3e8: CheckStackOverflow
    //     0xc4d3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d3ec: cmp             SP, x16
    //     0xc4d3f0: b.ls            #0xc4d430
    // 0xc4d3f4: r1 = Null
    //     0xc4d3f4: mov             x1, NULL
    // 0xc4d3f8: r2 = 4
    //     0xc4d3f8: movz            x2, #0x4
    // 0xc4d3fc: r0 = AllocateArray()
    //     0xc4d3fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d400: r16 = "QuranTranslationLanguage."
    //     0xc4d400: add             x16, PP, #0x37, lsl #12  ; [pp+0x37ea8] "QuranTranslationLanguage."
    //     0xc4d404: ldr             x16, [x16, #0xea8]
    // 0xc4d408: StoreField: r0->field_f = r16
    //     0xc4d408: stur            w16, [x0, #0xf]
    // 0xc4d40c: ldur            x1, [fp, #-8]
    // 0xc4d410: LoadField: r2 = r1->field_f
    //     0xc4d410: ldur            w2, [x1, #0xf]
    // 0xc4d414: DecompressPointer r2
    //     0xc4d414: add             x2, x2, HEAP, lsl #32
    // 0xc4d418: StoreField: r0->field_13 = r2
    //     0xc4d418: stur            w2, [x0, #0x13]
    // 0xc4d41c: str             x0, [SP]
    // 0xc4d420: r0 = _interpolate()
    //     0xc4d420: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d424: LeaveFrame
    //     0xc4d424: mov             SP, fp
    //     0xc4d428: ldp             fp, lr, [SP], #0x10
    // 0xc4d42c: ret
    //     0xc4d42c: ret             
    // 0xc4d430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d434: b               #0xc4d3f4
  }
}
