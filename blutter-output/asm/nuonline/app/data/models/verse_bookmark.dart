// lib: , url: package:nuonline/app/data/models/verse_bookmark.dart

// class id: 1050062, size: 0x8
class :: {
}

// class id: 1117, size: 0x40, field offset: 0x8
class VerseBookmark extends Object {

  String toJson(VerseBookmark) {
    // ** addr: 0x83b814, size: 0x48
    // 0x83b814: EnterFrame
    //     0x83b814: stp             fp, lr, [SP, #-0x10]!
    //     0x83b818: mov             fp, SP
    // 0x83b81c: CheckStackOverflow
    //     0x83b81c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83b820: cmp             SP, x16
    //     0x83b824: b.ls            #0x83b83c
    // 0x83b828: ldr             x1, [fp, #0x10]
    // 0x83b82c: r0 = to<PERSON>son()
    //     0x83b82c: bl              #0x83b844  ; [package:nuonline/app/data/models/verse_bookmark.dart] VerseBookmark::toJson
    // 0x83b830: LeaveFrame
    //     0x83b830: mov             SP, fp
    //     0x83b834: ldp             fp, lr, [SP], #0x10
    // 0x83b838: ret
    //     0x83b838: ret             
    // 0x83b83c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83b83c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83b840: b               #0x83b828
  }
  String toJson(VerseBookmark) {
    // ** addr: 0x83b844, size: 0x3c
    // 0x83b844: EnterFrame
    //     0x83b844: stp             fp, lr, [SP, #-0x10]!
    //     0x83b848: mov             fp, SP
    // 0x83b84c: CheckStackOverflow
    //     0x83b84c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83b850: cmp             SP, x16
    //     0x83b854: b.ls            #0x83b878
    // 0x83b858: r0 = toMap()
    //     0x83b858: bl              #0x83b880  ; [package:nuonline/app/data/models/verse_bookmark.dart] VerseBookmark::toMap
    // 0x83b85c: mov             x2, x0
    // 0x83b860: r1 = Instance_JsonCodec
    //     0x83b860: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!JsonCodec@e2ccc1
    // 0x83b864: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x83b864: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83b868: r0 = encode()
    //     0x83b868: bl              #0xcebad8  ; [dart:convert] JsonCodec::encode
    // 0x83b86c: LeaveFrame
    //     0x83b86c: mov             SP, fp
    //     0x83b870: ldp             fp, lr, [SP], #0x10
    // 0x83b874: ret
    //     0x83b874: ret             
    // 0x83b878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83b878: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83b87c: b               #0x83b858
  }
  _ toMap(/* No info */) {
    // ** addr: 0x83b880, size: 0x2ec
    // 0x83b880: EnterFrame
    //     0x83b880: stp             fp, lr, [SP, #-0x10]!
    //     0x83b884: mov             fp, SP
    // 0x83b888: AllocStack(0x20)
    //     0x83b888: sub             SP, SP, #0x20
    // 0x83b88c: SetupParameters(VerseBookmark this /* r1 => r0, fp-0x8 */)
    //     0x83b88c: mov             x0, x1
    //     0x83b890: stur            x1, [fp, #-8]
    // 0x83b894: CheckStackOverflow
    //     0x83b894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83b898: cmp             SP, x16
    //     0x83b89c: b.ls            #0x83bb64
    // 0x83b8a0: r1 = Null
    //     0x83b8a0: mov             x1, NULL
    // 0x83b8a4: r2 = 36
    //     0x83b8a4: movz            x2, #0x24
    // 0x83b8a8: r0 = AllocateArray()
    //     0x83b8a8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x83b8ac: mov             x2, x0
    // 0x83b8b0: stur            x2, [fp, #-0x10]
    // 0x83b8b4: r16 = "id"
    //     0x83b8b4: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x83b8b8: ldr             x16, [x16, #0x740]
    // 0x83b8bc: StoreField: r2->field_f = r16
    //     0x83b8bc: stur            w16, [x2, #0xf]
    // 0x83b8c0: ldur            x3, [fp, #-8]
    // 0x83b8c4: LoadField: r4 = r3->field_7
    //     0x83b8c4: ldur            x4, [x3, #7]
    // 0x83b8c8: r0 = BoxInt64Instr(r4)
    //     0x83b8c8: sbfiz           x0, x4, #1, #0x1f
    //     0x83b8cc: cmp             x4, x0, asr #1
    //     0x83b8d0: b.eq            #0x83b8dc
    //     0x83b8d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83b8d8: stur            x4, [x0, #7]
    // 0x83b8dc: mov             x1, x2
    // 0x83b8e0: ArrayStore: r1[1] = r0  ; List_4
    //     0x83b8e0: add             x25, x1, #0x13
    //     0x83b8e4: str             w0, [x25]
    //     0x83b8e8: tbz             w0, #0, #0x83b904
    //     0x83b8ec: ldurb           w16, [x1, #-1]
    //     0x83b8f0: ldurb           w17, [x0, #-1]
    //     0x83b8f4: and             x16, x17, x16, lsr #2
    //     0x83b8f8: tst             x16, HEAP, lsr #32
    //     0x83b8fc: b.eq            #0x83b904
    //     0x83b900: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83b904: r16 = "page"
    //     0x83b904: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x83b908: ldr             x16, [x16, #0x300]
    // 0x83b90c: ArrayStore: r2[0] = r16  ; List_4
    //     0x83b90c: stur            w16, [x2, #0x17]
    // 0x83b910: LoadField: r4 = r3->field_f
    //     0x83b910: ldur            x4, [x3, #0xf]
    // 0x83b914: r0 = BoxInt64Instr(r4)
    //     0x83b914: sbfiz           x0, x4, #1, #0x1f
    //     0x83b918: cmp             x4, x0, asr #1
    //     0x83b91c: b.eq            #0x83b928
    //     0x83b920: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83b924: stur            x4, [x0, #7]
    // 0x83b928: mov             x1, x2
    // 0x83b92c: ArrayStore: r1[3] = r0  ; List_4
    //     0x83b92c: add             x25, x1, #0x1b
    //     0x83b930: str             w0, [x25]
    //     0x83b934: tbz             w0, #0, #0x83b950
    //     0x83b938: ldurb           w16, [x1, #-1]
    //     0x83b93c: ldurb           w17, [x0, #-1]
    //     0x83b940: and             x16, x17, x16, lsr #2
    //     0x83b944: tst             x16, HEAP, lsr #32
    //     0x83b948: b.eq            #0x83b950
    //     0x83b94c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83b950: r16 = "number"
    //     0x83b950: add             x16, PP, #8, lsl #12  ; [pp+0x8998] "number"
    //     0x83b954: ldr             x16, [x16, #0x998]
    // 0x83b958: StoreField: r2->field_1f = r16
    //     0x83b958: stur            w16, [x2, #0x1f]
    // 0x83b95c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x83b95c: ldur            x4, [x3, #0x17]
    // 0x83b960: r0 = BoxInt64Instr(r4)
    //     0x83b960: sbfiz           x0, x4, #1, #0x1f
    //     0x83b964: cmp             x4, x0, asr #1
    //     0x83b968: b.eq            #0x83b974
    //     0x83b96c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83b970: stur            x4, [x0, #7]
    // 0x83b974: mov             x1, x2
    // 0x83b978: ArrayStore: r1[5] = r0  ; List_4
    //     0x83b978: add             x25, x1, #0x23
    //     0x83b97c: str             w0, [x25]
    //     0x83b980: tbz             w0, #0, #0x83b99c
    //     0x83b984: ldurb           w16, [x1, #-1]
    //     0x83b988: ldurb           w17, [x0, #-1]
    //     0x83b98c: and             x16, x17, x16, lsr #2
    //     0x83b990: tst             x16, HEAP, lsr #32
    //     0x83b994: b.eq            #0x83b99c
    //     0x83b998: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83b99c: r16 = "juz_id"
    //     0x83b99c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10328] "juz_id"
    //     0x83b9a0: ldr             x16, [x16, #0x328]
    // 0x83b9a4: StoreField: r2->field_27 = r16
    //     0x83b9a4: stur            w16, [x2, #0x27]
    // 0x83b9a8: LoadField: r4 = r3->field_1f
    //     0x83b9a8: ldur            x4, [x3, #0x1f]
    // 0x83b9ac: r0 = BoxInt64Instr(r4)
    //     0x83b9ac: sbfiz           x0, x4, #1, #0x1f
    //     0x83b9b0: cmp             x4, x0, asr #1
    //     0x83b9b4: b.eq            #0x83b9c0
    //     0x83b9b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83b9bc: stur            x4, [x0, #7]
    // 0x83b9c0: mov             x1, x2
    // 0x83b9c4: ArrayStore: r1[7] = r0  ; List_4
    //     0x83b9c4: add             x25, x1, #0x2b
    //     0x83b9c8: str             w0, [x25]
    //     0x83b9cc: tbz             w0, #0, #0x83b9e8
    //     0x83b9d0: ldurb           w16, [x1, #-1]
    //     0x83b9d4: ldurb           w17, [x0, #-1]
    //     0x83b9d8: and             x16, x17, x16, lsr #2
    //     0x83b9dc: tst             x16, HEAP, lsr #32
    //     0x83b9e0: b.eq            #0x83b9e8
    //     0x83b9e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83b9e8: r16 = "surah_id"
    //     0x83b9e8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10340] "surah_id"
    //     0x83b9ec: ldr             x16, [x16, #0x340]
    // 0x83b9f0: StoreField: r2->field_2f = r16
    //     0x83b9f0: stur            w16, [x2, #0x2f]
    // 0x83b9f4: LoadField: r4 = r3->field_27
    //     0x83b9f4: ldur            x4, [x3, #0x27]
    // 0x83b9f8: r0 = BoxInt64Instr(r4)
    //     0x83b9f8: sbfiz           x0, x4, #1, #0x1f
    //     0x83b9fc: cmp             x4, x0, asr #1
    //     0x83ba00: b.eq            #0x83ba0c
    //     0x83ba04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x83ba08: stur            x4, [x0, #7]
    // 0x83ba0c: mov             x1, x2
    // 0x83ba10: ArrayStore: r1[9] = r0  ; List_4
    //     0x83ba10: add             x25, x1, #0x33
    //     0x83ba14: str             w0, [x25]
    //     0x83ba18: tbz             w0, #0, #0x83ba34
    //     0x83ba1c: ldurb           w16, [x1, #-1]
    //     0x83ba20: ldurb           w17, [x0, #-1]
    //     0x83ba24: and             x16, x17, x16, lsr #2
    //     0x83ba28: tst             x16, HEAP, lsr #32
    //     0x83ba2c: b.eq            #0x83ba34
    //     0x83ba30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ba34: r16 = "surah_name"
    //     0x83ba34: add             x16, PP, #0x10, lsl #12  ; [pp+0x103a0] "surah_name"
    //     0x83ba38: ldr             x16, [x16, #0x3a0]
    // 0x83ba3c: StoreField: r2->field_37 = r16
    //     0x83ba3c: stur            w16, [x2, #0x37]
    // 0x83ba40: LoadField: r0 = r3->field_2f
    //     0x83ba40: ldur            w0, [x3, #0x2f]
    // 0x83ba44: DecompressPointer r0
    //     0x83ba44: add             x0, x0, HEAP, lsl #32
    // 0x83ba48: mov             x1, x2
    // 0x83ba4c: ArrayStore: r1[11] = r0  ; List_4
    //     0x83ba4c: add             x25, x1, #0x3b
    //     0x83ba50: str             w0, [x25]
    //     0x83ba54: tbz             w0, #0, #0x83ba70
    //     0x83ba58: ldurb           w16, [x1, #-1]
    //     0x83ba5c: ldurb           w17, [x0, #-1]
    //     0x83ba60: and             x16, x17, x16, lsr #2
    //     0x83ba64: tst             x16, HEAP, lsr #32
    //     0x83ba68: b.eq            #0x83ba70
    //     0x83ba6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83ba70: r16 = "route"
    //     0x83ba70: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "route"
    //     0x83ba74: ldr             x16, [x16, #0x3a0]
    // 0x83ba78: StoreField: r2->field_3f = r16
    //     0x83ba78: stur            w16, [x2, #0x3f]
    // 0x83ba7c: LoadField: r0 = r3->field_33
    //     0x83ba7c: ldur            w0, [x3, #0x33]
    // 0x83ba80: DecompressPointer r0
    //     0x83ba80: add             x0, x0, HEAP, lsl #32
    // 0x83ba84: mov             x1, x2
    // 0x83ba88: ArrayStore: r1[13] = r0  ; List_4
    //     0x83ba88: add             x25, x1, #0x43
    //     0x83ba8c: str             w0, [x25]
    //     0x83ba90: tbz             w0, #0, #0x83baac
    //     0x83ba94: ldurb           w16, [x1, #-1]
    //     0x83ba98: ldurb           w17, [x0, #-1]
    //     0x83ba9c: and             x16, x17, x16, lsr #2
    //     0x83baa0: tst             x16, HEAP, lsr #32
    //     0x83baa4: b.eq            #0x83baac
    //     0x83baa8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83baac: r16 = "param"
    //     0x83baac: add             x16, PP, #0x12, lsl #12  ; [pp+0x12488] "param"
    //     0x83bab0: ldr             x16, [x16, #0x488]
    // 0x83bab4: StoreField: r2->field_47 = r16
    //     0x83bab4: stur            w16, [x2, #0x47]
    // 0x83bab8: LoadField: r0 = r3->field_37
    //     0x83bab8: ldur            w0, [x3, #0x37]
    // 0x83babc: DecompressPointer r0
    //     0x83babc: add             x0, x0, HEAP, lsl #32
    // 0x83bac0: mov             x1, x2
    // 0x83bac4: ArrayStore: r1[15] = r0  ; List_4
    //     0x83bac4: add             x25, x1, #0x4b
    //     0x83bac8: str             w0, [x25]
    //     0x83bacc: tbz             w0, #0, #0x83bae8
    //     0x83bad0: ldurb           w16, [x1, #-1]
    //     0x83bad4: ldurb           w17, [x0, #-1]
    //     0x83bad8: and             x16, x17, x16, lsr #2
    //     0x83badc: tst             x16, HEAP, lsr #32
    //     0x83bae0: b.eq            #0x83bae8
    //     0x83bae4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83bae8: r16 = "updatedAt"
    //     0x83bae8: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a0f0] "updatedAt"
    //     0x83baec: ldr             x16, [x16, #0xf0]
    // 0x83baf0: StoreField: r2->field_4f = r16
    //     0x83baf0: stur            w16, [x2, #0x4f]
    // 0x83baf4: LoadField: r1 = r3->field_3b
    //     0x83baf4: ldur            w1, [x3, #0x3b]
    // 0x83baf8: DecompressPointer r1
    //     0x83baf8: add             x1, x1, HEAP, lsl #32
    // 0x83bafc: cmp             w1, NULL
    // 0x83bb00: b.ne            #0x83bb0c
    // 0x83bb04: r0 = Null
    //     0x83bb04: mov             x0, NULL
    // 0x83bb08: b               #0x83bb20
    // 0x83bb0c: r0 = LoadClassIdInstr(r1)
    //     0x83bb0c: ldur            x0, [x1, #-1]
    //     0x83bb10: ubfx            x0, x0, #0xc, #0x14
    // 0x83bb14: r0 = GDT[cid_x0 + -0x2ac]()
    //     0x83bb14: sub             lr, x0, #0x2ac
    //     0x83bb18: ldr             lr, [x21, lr, lsl #3]
    //     0x83bb1c: blr             lr
    // 0x83bb20: ldur            x1, [fp, #-0x10]
    // 0x83bb24: ArrayStore: r1[17] = r0  ; List_4
    //     0x83bb24: add             x25, x1, #0x53
    //     0x83bb28: str             w0, [x25]
    //     0x83bb2c: tbz             w0, #0, #0x83bb48
    //     0x83bb30: ldurb           w16, [x1, #-1]
    //     0x83bb34: ldurb           w17, [x0, #-1]
    //     0x83bb38: and             x16, x17, x16, lsr #2
    //     0x83bb3c: tst             x16, HEAP, lsr #32
    //     0x83bb40: b.eq            #0x83bb48
    //     0x83bb44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x83bb48: r16 = <String, dynamic>
    //     0x83bb48: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x83bb4c: ldur            lr, [fp, #-0x10]
    // 0x83bb50: stp             lr, x16, [SP]
    // 0x83bb54: r0 = Map._fromLiteral()
    //     0x83bb54: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83bb58: LeaveFrame
    //     0x83bb58: mov             SP, fp
    //     0x83bb5c: ldp             fp, lr, [SP], #0x10
    // 0x83bb60: ret
    //     0x83bb60: ret             
    // 0x83bb64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83bb64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83bb68: b               #0x83b8a0
  }
}

// class id: 1641, size: 0x14, field offset: 0xc
class VerseBookmarkAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa67fec, size: 0x628
    // 0xa67fec: EnterFrame
    //     0xa67fec: stp             fp, lr, [SP, #-0x10]!
    //     0xa67ff0: mov             fp, SP
    // 0xa67ff4: AllocStack(0x78)
    //     0xa67ff4: sub             SP, SP, #0x78
    // 0xa67ff8: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa67ff8: stur            x2, [fp, #-0x20]
    // 0xa67ffc: CheckStackOverflow
    //     0xa67ffc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa68000: cmp             SP, x16
    //     0xa68004: b.ls            #0xa685fc
    // 0xa68008: LoadField: r3 = r2->field_23
    //     0xa68008: ldur            x3, [x2, #0x23]
    // 0xa6800c: add             x0, x3, #1
    // 0xa68010: LoadField: r1 = r2->field_1b
    //     0xa68010: ldur            x1, [x2, #0x1b]
    // 0xa68014: cmp             x0, x1
    // 0xa68018: b.gt            #0xa685a0
    // 0xa6801c: LoadField: r4 = r2->field_7
    //     0xa6801c: ldur            w4, [x2, #7]
    // 0xa68020: DecompressPointer r4
    //     0xa68020: add             x4, x4, HEAP, lsl #32
    // 0xa68024: stur            x4, [fp, #-0x18]
    // 0xa68028: StoreField: r2->field_23 = r0
    //     0xa68028: stur            x0, [x2, #0x23]
    // 0xa6802c: LoadField: r0 = r4->field_13
    //     0xa6802c: ldur            w0, [x4, #0x13]
    // 0xa68030: r5 = LoadInt32Instr(r0)
    //     0xa68030: sbfx            x5, x0, #1, #0x1f
    // 0xa68034: mov             x0, x5
    // 0xa68038: mov             x1, x3
    // 0xa6803c: stur            x5, [fp, #-0x10]
    // 0xa68040: cmp             x1, x0
    // 0xa68044: b.hs            #0xa68604
    // 0xa68048: LoadField: r0 = r4->field_7
    //     0xa68048: ldur            x0, [x4, #7]
    // 0xa6804c: ldrb            w1, [x0, x3]
    // 0xa68050: stur            x1, [fp, #-8]
    // 0xa68054: r16 = <int, dynamic>
    //     0xa68054: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa68058: ldr             x16, [x16, #0xac0]
    // 0xa6805c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa68060: stp             lr, x16, [SP]
    // 0xa68064: r0 = Map._fromLiteral()
    //     0xa68064: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa68068: mov             x2, x0
    // 0xa6806c: stur            x2, [fp, #-0x38]
    // 0xa68070: r6 = 0
    //     0xa68070: movz            x6, #0
    // 0xa68074: ldur            x3, [fp, #-0x20]
    // 0xa68078: ldur            x4, [fp, #-0x18]
    // 0xa6807c: ldur            x5, [fp, #-8]
    // 0xa68080: stur            x6, [fp, #-0x30]
    // 0xa68084: CheckStackOverflow
    //     0xa68084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa68088: cmp             SP, x16
    //     0xa6808c: b.ls            #0xa68608
    // 0xa68090: cmp             x6, x5
    // 0xa68094: b.ge            #0xa68120
    // 0xa68098: LoadField: r7 = r3->field_23
    //     0xa68098: ldur            x7, [x3, #0x23]
    // 0xa6809c: add             x0, x7, #1
    // 0xa680a0: LoadField: r1 = r3->field_1b
    //     0xa680a0: ldur            x1, [x3, #0x1b]
    // 0xa680a4: cmp             x0, x1
    // 0xa680a8: b.gt            #0xa685c8
    // 0xa680ac: StoreField: r3->field_23 = r0
    //     0xa680ac: stur            x0, [x3, #0x23]
    // 0xa680b0: ldur            x0, [fp, #-0x10]
    // 0xa680b4: mov             x1, x7
    // 0xa680b8: cmp             x1, x0
    // 0xa680bc: b.hs            #0xa68610
    // 0xa680c0: LoadField: r0 = r4->field_7
    //     0xa680c0: ldur            x0, [x4, #7]
    // 0xa680c4: ldrb            w8, [x0, x7]
    // 0xa680c8: mov             x1, x3
    // 0xa680cc: stur            x8, [fp, #-0x28]
    // 0xa680d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa680d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa680d4: r0 = read()
    //     0xa680d4: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa680d8: mov             x1, x0
    // 0xa680dc: ldur            x0, [fp, #-0x28]
    // 0xa680e0: lsl             x2, x0, #1
    // 0xa680e4: r16 = LoadInt32Instr(r2)
    //     0xa680e4: sbfx            x16, x2, #1, #0x1f
    // 0xa680e8: r17 = 11601
    //     0xa680e8: movz            x17, #0x2d51
    // 0xa680ec: mul             x0, x16, x17
    // 0xa680f0: umulh           x16, x16, x17
    // 0xa680f4: eor             x0, x0, x16
    // 0xa680f8: r0 = 0
    //     0xa680f8: eor             x0, x0, x0, lsr #32
    // 0xa680fc: ubfiz           x0, x0, #1, #0x1e
    // 0xa68100: r5 = LoadInt32Instr(r0)
    //     0xa68100: sbfx            x5, x0, #1, #0x1f
    // 0xa68104: mov             x3, x1
    // 0xa68108: ldur            x1, [fp, #-0x38]
    // 0xa6810c: r0 = _set()
    //     0xa6810c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa68110: ldur            x0, [fp, #-0x30]
    // 0xa68114: add             x6, x0, #1
    // 0xa68118: ldur            x2, [fp, #-0x38]
    // 0xa6811c: b               #0xa68074
    // 0xa68120: mov             x0, x2
    // 0xa68124: mov             x1, x0
    // 0xa68128: r2 = 0
    //     0xa68128: movz            x2, #0
    // 0xa6812c: r0 = _getValueOrData()
    //     0xa6812c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68130: ldur            x3, [fp, #-0x38]
    // 0xa68134: LoadField: r1 = r3->field_f
    //     0xa68134: ldur            w1, [x3, #0xf]
    // 0xa68138: DecompressPointer r1
    //     0xa68138: add             x1, x1, HEAP, lsl #32
    // 0xa6813c: cmp             w1, w0
    // 0xa68140: b.ne            #0xa6814c
    // 0xa68144: r4 = Null
    //     0xa68144: mov             x4, NULL
    // 0xa68148: b               #0xa68150
    // 0xa6814c: mov             x4, x0
    // 0xa68150: mov             x0, x4
    // 0xa68154: stur            x4, [fp, #-0x18]
    // 0xa68158: r2 = Null
    //     0xa68158: mov             x2, NULL
    // 0xa6815c: r1 = Null
    //     0xa6815c: mov             x1, NULL
    // 0xa68160: branchIfSmi(r0, 0xa68188)
    //     0xa68160: tbz             w0, #0, #0xa68188
    // 0xa68164: r4 = LoadClassIdInstr(r0)
    //     0xa68164: ldur            x4, [x0, #-1]
    //     0xa68168: ubfx            x4, x4, #0xc, #0x14
    // 0xa6816c: sub             x4, x4, #0x3c
    // 0xa68170: cmp             x4, #1
    // 0xa68174: b.ls            #0xa68188
    // 0xa68178: r8 = int
    //     0xa68178: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa6817c: r3 = Null
    //     0xa6817c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20f98] Null
    //     0xa68180: ldr             x3, [x3, #0xf98]
    // 0xa68184: r0 = int()
    //     0xa68184: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa68188: ldur            x1, [fp, #-0x38]
    // 0xa6818c: r2 = 2
    //     0xa6818c: movz            x2, #0x2
    // 0xa68190: r0 = _getValueOrData()
    //     0xa68190: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68194: ldur            x3, [fp, #-0x38]
    // 0xa68198: LoadField: r1 = r3->field_f
    //     0xa68198: ldur            w1, [x3, #0xf]
    // 0xa6819c: DecompressPointer r1
    //     0xa6819c: add             x1, x1, HEAP, lsl #32
    // 0xa681a0: cmp             w1, w0
    // 0xa681a4: b.ne            #0xa681b0
    // 0xa681a8: r4 = Null
    //     0xa681a8: mov             x4, NULL
    // 0xa681ac: b               #0xa681b4
    // 0xa681b0: mov             x4, x0
    // 0xa681b4: mov             x0, x4
    // 0xa681b8: stur            x4, [fp, #-0x20]
    // 0xa681bc: r2 = Null
    //     0xa681bc: mov             x2, NULL
    // 0xa681c0: r1 = Null
    //     0xa681c0: mov             x1, NULL
    // 0xa681c4: branchIfSmi(r0, 0xa681ec)
    //     0xa681c4: tbz             w0, #0, #0xa681ec
    // 0xa681c8: r4 = LoadClassIdInstr(r0)
    //     0xa681c8: ldur            x4, [x0, #-1]
    //     0xa681cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa681d0: sub             x4, x4, #0x3c
    // 0xa681d4: cmp             x4, #1
    // 0xa681d8: b.ls            #0xa681ec
    // 0xa681dc: r8 = int
    //     0xa681dc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa681e0: r3 = Null
    //     0xa681e0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20fa8] Null
    //     0xa681e4: ldr             x3, [x3, #0xfa8]
    // 0xa681e8: r0 = int()
    //     0xa681e8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa681ec: ldur            x1, [fp, #-0x38]
    // 0xa681f0: r2 = 4
    //     0xa681f0: movz            x2, #0x4
    // 0xa681f4: r0 = _getValueOrData()
    //     0xa681f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa681f8: ldur            x3, [fp, #-0x38]
    // 0xa681fc: LoadField: r1 = r3->field_f
    //     0xa681fc: ldur            w1, [x3, #0xf]
    // 0xa68200: DecompressPointer r1
    //     0xa68200: add             x1, x1, HEAP, lsl #32
    // 0xa68204: cmp             w1, w0
    // 0xa68208: b.ne            #0xa68214
    // 0xa6820c: r4 = Null
    //     0xa6820c: mov             x4, NULL
    // 0xa68210: b               #0xa68218
    // 0xa68214: mov             x4, x0
    // 0xa68218: mov             x0, x4
    // 0xa6821c: stur            x4, [fp, #-0x40]
    // 0xa68220: r2 = Null
    //     0xa68220: mov             x2, NULL
    // 0xa68224: r1 = Null
    //     0xa68224: mov             x1, NULL
    // 0xa68228: branchIfSmi(r0, 0xa68250)
    //     0xa68228: tbz             w0, #0, #0xa68250
    // 0xa6822c: r4 = LoadClassIdInstr(r0)
    //     0xa6822c: ldur            x4, [x0, #-1]
    //     0xa68230: ubfx            x4, x4, #0xc, #0x14
    // 0xa68234: sub             x4, x4, #0x3c
    // 0xa68238: cmp             x4, #1
    // 0xa6823c: b.ls            #0xa68250
    // 0xa68240: r8 = int
    //     0xa68240: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa68244: r3 = Null
    //     0xa68244: add             x3, PP, #0x20, lsl #12  ; [pp+0x20fb8] Null
    //     0xa68248: ldr             x3, [x3, #0xfb8]
    // 0xa6824c: r0 = int()
    //     0xa6824c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa68250: ldur            x1, [fp, #-0x38]
    // 0xa68254: r2 = 6
    //     0xa68254: movz            x2, #0x6
    // 0xa68258: r0 = _getValueOrData()
    //     0xa68258: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6825c: ldur            x3, [fp, #-0x38]
    // 0xa68260: LoadField: r1 = r3->field_f
    //     0xa68260: ldur            w1, [x3, #0xf]
    // 0xa68264: DecompressPointer r1
    //     0xa68264: add             x1, x1, HEAP, lsl #32
    // 0xa68268: cmp             w1, w0
    // 0xa6826c: b.ne            #0xa68278
    // 0xa68270: r4 = Null
    //     0xa68270: mov             x4, NULL
    // 0xa68274: b               #0xa6827c
    // 0xa68278: mov             x4, x0
    // 0xa6827c: mov             x0, x4
    // 0xa68280: stur            x4, [fp, #-0x48]
    // 0xa68284: r2 = Null
    //     0xa68284: mov             x2, NULL
    // 0xa68288: r1 = Null
    //     0xa68288: mov             x1, NULL
    // 0xa6828c: branchIfSmi(r0, 0xa682b4)
    //     0xa6828c: tbz             w0, #0, #0xa682b4
    // 0xa68290: r4 = LoadClassIdInstr(r0)
    //     0xa68290: ldur            x4, [x0, #-1]
    //     0xa68294: ubfx            x4, x4, #0xc, #0x14
    // 0xa68298: sub             x4, x4, #0x3c
    // 0xa6829c: cmp             x4, #1
    // 0xa682a0: b.ls            #0xa682b4
    // 0xa682a4: r8 = int
    //     0xa682a4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa682a8: r3 = Null
    //     0xa682a8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20fc8] Null
    //     0xa682ac: ldr             x3, [x3, #0xfc8]
    // 0xa682b0: r0 = int()
    //     0xa682b0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa682b4: ldur            x1, [fp, #-0x38]
    // 0xa682b8: r2 = 8
    //     0xa682b8: movz            x2, #0x8
    // 0xa682bc: r0 = _getValueOrData()
    //     0xa682bc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa682c0: ldur            x3, [fp, #-0x38]
    // 0xa682c4: LoadField: r1 = r3->field_f
    //     0xa682c4: ldur            w1, [x3, #0xf]
    // 0xa682c8: DecompressPointer r1
    //     0xa682c8: add             x1, x1, HEAP, lsl #32
    // 0xa682cc: cmp             w1, w0
    // 0xa682d0: b.ne            #0xa682dc
    // 0xa682d4: r4 = Null
    //     0xa682d4: mov             x4, NULL
    // 0xa682d8: b               #0xa682e0
    // 0xa682dc: mov             x4, x0
    // 0xa682e0: mov             x0, x4
    // 0xa682e4: stur            x4, [fp, #-0x50]
    // 0xa682e8: r2 = Null
    //     0xa682e8: mov             x2, NULL
    // 0xa682ec: r1 = Null
    //     0xa682ec: mov             x1, NULL
    // 0xa682f0: branchIfSmi(r0, 0xa68318)
    //     0xa682f0: tbz             w0, #0, #0xa68318
    // 0xa682f4: r4 = LoadClassIdInstr(r0)
    //     0xa682f4: ldur            x4, [x0, #-1]
    //     0xa682f8: ubfx            x4, x4, #0xc, #0x14
    // 0xa682fc: sub             x4, x4, #0x3c
    // 0xa68300: cmp             x4, #1
    // 0xa68304: b.ls            #0xa68318
    // 0xa68308: r8 = int
    //     0xa68308: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa6830c: r3 = Null
    //     0xa6830c: add             x3, PP, #0x20, lsl #12  ; [pp+0x20fd8] Null
    //     0xa68310: ldr             x3, [x3, #0xfd8]
    // 0xa68314: r0 = int()
    //     0xa68314: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa68318: ldur            x1, [fp, #-0x38]
    // 0xa6831c: r2 = 10
    //     0xa6831c: movz            x2, #0xa
    // 0xa68320: r0 = _getValueOrData()
    //     0xa68320: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68324: ldur            x3, [fp, #-0x38]
    // 0xa68328: LoadField: r1 = r3->field_f
    //     0xa68328: ldur            w1, [x3, #0xf]
    // 0xa6832c: DecompressPointer r1
    //     0xa6832c: add             x1, x1, HEAP, lsl #32
    // 0xa68330: cmp             w1, w0
    // 0xa68334: b.ne            #0xa68340
    // 0xa68338: r4 = Null
    //     0xa68338: mov             x4, NULL
    // 0xa6833c: b               #0xa68344
    // 0xa68340: mov             x4, x0
    // 0xa68344: mov             x0, x4
    // 0xa68348: stur            x4, [fp, #-0x58]
    // 0xa6834c: r2 = Null
    //     0xa6834c: mov             x2, NULL
    // 0xa68350: r1 = Null
    //     0xa68350: mov             x1, NULL
    // 0xa68354: r4 = 60
    //     0xa68354: movz            x4, #0x3c
    // 0xa68358: branchIfSmi(r0, 0xa68364)
    //     0xa68358: tbz             w0, #0, #0xa68364
    // 0xa6835c: r4 = LoadClassIdInstr(r0)
    //     0xa6835c: ldur            x4, [x0, #-1]
    //     0xa68360: ubfx            x4, x4, #0xc, #0x14
    // 0xa68364: sub             x4, x4, #0x5e
    // 0xa68368: cmp             x4, #1
    // 0xa6836c: b.ls            #0xa68380
    // 0xa68370: r8 = String
    //     0xa68370: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa68374: r3 = Null
    //     0xa68374: add             x3, PP, #0x20, lsl #12  ; [pp+0x20fe8] Null
    //     0xa68378: ldr             x3, [x3, #0xfe8]
    // 0xa6837c: r0 = String()
    //     0xa6837c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa68380: ldur            x1, [fp, #-0x38]
    // 0xa68384: r2 = 12
    //     0xa68384: movz            x2, #0xc
    // 0xa68388: r0 = _getValueOrData()
    //     0xa68388: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6838c: ldur            x3, [fp, #-0x38]
    // 0xa68390: LoadField: r1 = r3->field_f
    //     0xa68390: ldur            w1, [x3, #0xf]
    // 0xa68394: DecompressPointer r1
    //     0xa68394: add             x1, x1, HEAP, lsl #32
    // 0xa68398: cmp             w1, w0
    // 0xa6839c: b.ne            #0xa683a8
    // 0xa683a0: r4 = Null
    //     0xa683a0: mov             x4, NULL
    // 0xa683a4: b               #0xa683ac
    // 0xa683a8: mov             x4, x0
    // 0xa683ac: mov             x0, x4
    // 0xa683b0: stur            x4, [fp, #-0x60]
    // 0xa683b4: r2 = Null
    //     0xa683b4: mov             x2, NULL
    // 0xa683b8: r1 = Null
    //     0xa683b8: mov             x1, NULL
    // 0xa683bc: r4 = 60
    //     0xa683bc: movz            x4, #0x3c
    // 0xa683c0: branchIfSmi(r0, 0xa683cc)
    //     0xa683c0: tbz             w0, #0, #0xa683cc
    // 0xa683c4: r4 = LoadClassIdInstr(r0)
    //     0xa683c4: ldur            x4, [x0, #-1]
    //     0xa683c8: ubfx            x4, x4, #0xc, #0x14
    // 0xa683cc: sub             x4, x4, #0x5e
    // 0xa683d0: cmp             x4, #1
    // 0xa683d4: b.ls            #0xa683e8
    // 0xa683d8: r8 = String
    //     0xa683d8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa683dc: r3 = Null
    //     0xa683dc: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ff8] Null
    //     0xa683e0: ldr             x3, [x3, #0xff8]
    // 0xa683e4: r0 = String()
    //     0xa683e4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa683e8: ldur            x1, [fp, #-0x38]
    // 0xa683ec: r2 = 14
    //     0xa683ec: movz            x2, #0xe
    // 0xa683f0: r0 = _getValueOrData()
    //     0xa683f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa683f4: ldur            x3, [fp, #-0x38]
    // 0xa683f8: LoadField: r1 = r3->field_f
    //     0xa683f8: ldur            w1, [x3, #0xf]
    // 0xa683fc: DecompressPointer r1
    //     0xa683fc: add             x1, x1, HEAP, lsl #32
    // 0xa68400: cmp             w1, w0
    // 0xa68404: b.ne            #0xa68410
    // 0xa68408: r4 = Null
    //     0xa68408: mov             x4, NULL
    // 0xa6840c: b               #0xa68414
    // 0xa68410: mov             x4, x0
    // 0xa68414: mov             x0, x4
    // 0xa68418: stur            x4, [fp, #-0x68]
    // 0xa6841c: r2 = Null
    //     0xa6841c: mov             x2, NULL
    // 0xa68420: r1 = Null
    //     0xa68420: mov             x1, NULL
    // 0xa68424: r8 = Map
    //     0xa68424: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xa68428: r3 = Null
    //     0xa68428: add             x3, PP, #0x21, lsl #12  ; [pp+0x21008] Null
    //     0xa6842c: ldr             x3, [x3, #8]
    // 0xa68430: r0 = Map()
    //     0xa68430: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xa68434: ldur            x0, [fp, #-0x68]
    // 0xa68438: r1 = LoadClassIdInstr(r0)
    //     0xa68438: ldur            x1, [x0, #-1]
    //     0xa6843c: ubfx            x1, x1, #0xc, #0x14
    // 0xa68440: r16 = <String, dynamic>
    //     0xa68440: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xa68444: stp             x0, x16, [SP]
    // 0xa68448: mov             x0, x1
    // 0xa6844c: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xa6844c: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xa68450: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xa68450: add             lr, x0, #0x5f4
    //     0xa68454: ldr             lr, [x21, lr, lsl #3]
    //     0xa68458: blr             lr
    // 0xa6845c: ldur            x1, [fp, #-0x38]
    // 0xa68460: r2 = 16
    //     0xa68460: movz            x2, #0x10
    // 0xa68464: stur            x0, [fp, #-0x68]
    // 0xa68468: r0 = _getValueOrData()
    //     0xa68468: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6846c: mov             x1, x0
    // 0xa68470: ldur            x0, [fp, #-0x38]
    // 0xa68474: LoadField: r2 = r0->field_f
    //     0xa68474: ldur            w2, [x0, #0xf]
    // 0xa68478: DecompressPointer r2
    //     0xa68478: add             x2, x2, HEAP, lsl #32
    // 0xa6847c: cmp             w2, w1
    // 0xa68480: b.ne            #0xa6848c
    // 0xa68484: r11 = Null
    //     0xa68484: mov             x11, NULL
    // 0xa68488: b               #0xa68490
    // 0xa6848c: mov             x11, x1
    // 0xa68490: ldur            x3, [fp, #-0x68]
    // 0xa68494: ldur            x10, [fp, #-0x18]
    // 0xa68498: ldur            x9, [fp, #-0x20]
    // 0xa6849c: ldur            x8, [fp, #-0x40]
    // 0xa684a0: ldur            x7, [fp, #-0x48]
    // 0xa684a4: ldur            x6, [fp, #-0x50]
    // 0xa684a8: ldur            x5, [fp, #-0x58]
    // 0xa684ac: ldur            x4, [fp, #-0x60]
    // 0xa684b0: mov             x0, x11
    // 0xa684b4: stur            x11, [fp, #-0x38]
    // 0xa684b8: r2 = Null
    //     0xa684b8: mov             x2, NULL
    // 0xa684bc: r1 = Null
    //     0xa684bc: mov             x1, NULL
    // 0xa684c0: r4 = 60
    //     0xa684c0: movz            x4, #0x3c
    // 0xa684c4: branchIfSmi(r0, 0xa684d0)
    //     0xa684c4: tbz             w0, #0, #0xa684d0
    // 0xa684c8: r4 = LoadClassIdInstr(r0)
    //     0xa684c8: ldur            x4, [x0, #-1]
    //     0xa684cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa684d0: cmp             x4, #0x1ae
    // 0xa684d4: b.eq            #0xa684fc
    // 0xa684d8: r17 = -7188
    //     0xa684d8: movn            x17, #0x1c13
    // 0xa684dc: add             x4, x4, x17
    // 0xa684e0: cmp             x4, #1
    // 0xa684e4: b.ls            #0xa684fc
    // 0xa684e8: r8 = DateTime?
    //     0xa684e8: add             x8, PP, #0x21, lsl #12  ; [pp+0x21018] Type: DateTime?
    //     0xa684ec: ldr             x8, [x8, #0x18]
    // 0xa684f0: r3 = Null
    //     0xa684f0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21020] Null
    //     0xa684f4: ldr             x3, [x3, #0x20]
    // 0xa684f8: r0 = DefaultNullableTypeTest()
    //     0xa684f8: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0xa684fc: ldur            x0, [fp, #-0x18]
    // 0xa68500: r1 = LoadInt32Instr(r0)
    //     0xa68500: sbfx            x1, x0, #1, #0x1f
    //     0xa68504: tbz             w0, #0, #0xa6850c
    //     0xa68508: ldur            x1, [x0, #7]
    // 0xa6850c: stur            x1, [fp, #-8]
    // 0xa68510: r0 = VerseBookmark()
    //     0xa68510: bl              #0xa68614  ; AllocateVerseBookmarkStub -> VerseBookmark (size=0x40)
    // 0xa68514: mov             x1, x0
    // 0xa68518: ldur            x0, [fp, #-8]
    // 0xa6851c: StoreField: r1->field_7 = r0
    //     0xa6851c: stur            x0, [x1, #7]
    // 0xa68520: ldur            x0, [fp, #-0x20]
    // 0xa68524: r2 = LoadInt32Instr(r0)
    //     0xa68524: sbfx            x2, x0, #1, #0x1f
    //     0xa68528: tbz             w0, #0, #0xa68530
    //     0xa6852c: ldur            x2, [x0, #7]
    // 0xa68530: StoreField: r1->field_f = r2
    //     0xa68530: stur            x2, [x1, #0xf]
    // 0xa68534: ldur            x0, [fp, #-0x40]
    // 0xa68538: r2 = LoadInt32Instr(r0)
    //     0xa68538: sbfx            x2, x0, #1, #0x1f
    //     0xa6853c: tbz             w0, #0, #0xa68544
    //     0xa68540: ldur            x2, [x0, #7]
    // 0xa68544: ArrayStore: r1[0] = r2  ; List_8
    //     0xa68544: stur            x2, [x1, #0x17]
    // 0xa68548: ldur            x0, [fp, #-0x48]
    // 0xa6854c: r2 = LoadInt32Instr(r0)
    //     0xa6854c: sbfx            x2, x0, #1, #0x1f
    //     0xa68550: tbz             w0, #0, #0xa68558
    //     0xa68554: ldur            x2, [x0, #7]
    // 0xa68558: StoreField: r1->field_1f = r2
    //     0xa68558: stur            x2, [x1, #0x1f]
    // 0xa6855c: ldur            x0, [fp, #-0x50]
    // 0xa68560: r2 = LoadInt32Instr(r0)
    //     0xa68560: sbfx            x2, x0, #1, #0x1f
    //     0xa68564: tbz             w0, #0, #0xa6856c
    //     0xa68568: ldur            x2, [x0, #7]
    // 0xa6856c: StoreField: r1->field_27 = r2
    //     0xa6856c: stur            x2, [x1, #0x27]
    // 0xa68570: ldur            x0, [fp, #-0x58]
    // 0xa68574: StoreField: r1->field_2f = r0
    //     0xa68574: stur            w0, [x1, #0x2f]
    // 0xa68578: ldur            x0, [fp, #-0x60]
    // 0xa6857c: StoreField: r1->field_33 = r0
    //     0xa6857c: stur            w0, [x1, #0x33]
    // 0xa68580: ldur            x0, [fp, #-0x68]
    // 0xa68584: StoreField: r1->field_37 = r0
    //     0xa68584: stur            w0, [x1, #0x37]
    // 0xa68588: ldur            x0, [fp, #-0x38]
    // 0xa6858c: StoreField: r1->field_3b = r0
    //     0xa6858c: stur            w0, [x1, #0x3b]
    // 0xa68590: mov             x0, x1
    // 0xa68594: LeaveFrame
    //     0xa68594: mov             SP, fp
    //     0xa68598: ldp             fp, lr, [SP], #0x10
    // 0xa6859c: ret
    //     0xa6859c: ret             
    // 0xa685a0: r0 = RangeError()
    //     0xa685a0: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa685a4: mov             x1, x0
    // 0xa685a8: r0 = "Not enough bytes available."
    //     0xa685a8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa685ac: ldr             x0, [x0, #0x8a8]
    // 0xa685b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa685b0: stur            w0, [x1, #0x17]
    // 0xa685b4: r2 = false
    //     0xa685b4: add             x2, NULL, #0x30  ; false
    // 0xa685b8: StoreField: r1->field_b = r2
    //     0xa685b8: stur            w2, [x1, #0xb]
    // 0xa685bc: mov             x0, x1
    // 0xa685c0: r0 = Throw()
    //     0xa685c0: bl              #0xec04b8  ; ThrowStub
    // 0xa685c4: brk             #0
    // 0xa685c8: r0 = "Not enough bytes available."
    //     0xa685c8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa685cc: ldr             x0, [x0, #0x8a8]
    // 0xa685d0: r2 = false
    //     0xa685d0: add             x2, NULL, #0x30  ; false
    // 0xa685d4: r0 = RangeError()
    //     0xa685d4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa685d8: mov             x1, x0
    // 0xa685dc: r0 = "Not enough bytes available."
    //     0xa685dc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa685e0: ldr             x0, [x0, #0x8a8]
    // 0xa685e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xa685e4: stur            w0, [x1, #0x17]
    // 0xa685e8: r0 = false
    //     0xa685e8: add             x0, NULL, #0x30  ; false
    // 0xa685ec: StoreField: r1->field_b = r0
    //     0xa685ec: stur            w0, [x1, #0xb]
    // 0xa685f0: mov             x0, x1
    // 0xa685f4: r0 = Throw()
    //     0xa685f4: bl              #0xec04b8  ; ThrowStub
    // 0xa685f8: brk             #0
    // 0xa685fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa685fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa68600: b               #0xa68008
    // 0xa68604: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa68604: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa68608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa68608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6860c: b               #0xa68090
    // 0xa68610: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa68610: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd9320, size: 0x62c
    // 0xbd9320: EnterFrame
    //     0xbd9320: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9324: mov             fp, SP
    // 0xbd9328: AllocStack(0x28)
    //     0xbd9328: sub             SP, SP, #0x28
    // 0xbd932c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd932c: mov             x4, x2
    //     0xbd9330: stur            x2, [fp, #-8]
    //     0xbd9334: stur            x3, [fp, #-0x10]
    // 0xbd9338: CheckStackOverflow
    //     0xbd9338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd933c: cmp             SP, x16
    //     0xbd9340: b.ls            #0xbd991c
    // 0xbd9344: mov             x0, x3
    // 0xbd9348: r2 = Null
    //     0xbd9348: mov             x2, NULL
    // 0xbd934c: r1 = Null
    //     0xbd934c: mov             x1, NULL
    // 0xbd9350: r4 = 60
    //     0xbd9350: movz            x4, #0x3c
    // 0xbd9354: branchIfSmi(r0, 0xbd9360)
    //     0xbd9354: tbz             w0, #0, #0xbd9360
    // 0xbd9358: r4 = LoadClassIdInstr(r0)
    //     0xbd9358: ldur            x4, [x0, #-1]
    //     0xbd935c: ubfx            x4, x4, #0xc, #0x14
    // 0xbd9360: cmp             x4, #0x45d
    // 0xbd9364: b.eq            #0xbd937c
    // 0xbd9368: r8 = VerseBookmark
    //     0xbd9368: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b260] Type: VerseBookmark
    //     0xbd936c: ldr             x8, [x8, #0x260]
    // 0xbd9370: r3 = Null
    //     0xbd9370: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b268] Null
    //     0xbd9374: ldr             x3, [x3, #0x268]
    // 0xbd9378: r0 = VerseBookmark()
    //     0xbd9378: bl              #0x83bb6c  ; IsType_VerseBookmark_Stub
    // 0xbd937c: ldur            x0, [fp, #-8]
    // 0xbd9380: LoadField: r1 = r0->field_b
    //     0xbd9380: ldur            w1, [x0, #0xb]
    // 0xbd9384: DecompressPointer r1
    //     0xbd9384: add             x1, x1, HEAP, lsl #32
    // 0xbd9388: LoadField: r2 = r1->field_13
    //     0xbd9388: ldur            w2, [x1, #0x13]
    // 0xbd938c: LoadField: r1 = r0->field_13
    //     0xbd938c: ldur            x1, [x0, #0x13]
    // 0xbd9390: r3 = LoadInt32Instr(r2)
    //     0xbd9390: sbfx            x3, x2, #1, #0x1f
    // 0xbd9394: sub             x2, x3, x1
    // 0xbd9398: cmp             x2, #1
    // 0xbd939c: b.ge            #0xbd93ac
    // 0xbd93a0: mov             x1, x0
    // 0xbd93a4: r2 = 1
    //     0xbd93a4: movz            x2, #0x1
    // 0xbd93a8: r0 = _increaseBufferSize()
    //     0xbd93a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd93ac: ldur            x3, [fp, #-8]
    // 0xbd93b0: r2 = 9
    //     0xbd93b0: movz            x2, #0x9
    // 0xbd93b4: LoadField: r4 = r3->field_b
    //     0xbd93b4: ldur            w4, [x3, #0xb]
    // 0xbd93b8: DecompressPointer r4
    //     0xbd93b8: add             x4, x4, HEAP, lsl #32
    // 0xbd93bc: LoadField: r5 = r3->field_13
    //     0xbd93bc: ldur            x5, [x3, #0x13]
    // 0xbd93c0: add             x6, x5, #1
    // 0xbd93c4: StoreField: r3->field_13 = r6
    //     0xbd93c4: stur            x6, [x3, #0x13]
    // 0xbd93c8: LoadField: r0 = r4->field_13
    //     0xbd93c8: ldur            w0, [x4, #0x13]
    // 0xbd93cc: r7 = LoadInt32Instr(r0)
    //     0xbd93cc: sbfx            x7, x0, #1, #0x1f
    // 0xbd93d0: mov             x0, x7
    // 0xbd93d4: mov             x1, x5
    // 0xbd93d8: cmp             x1, x0
    // 0xbd93dc: b.hs            #0xbd9924
    // 0xbd93e0: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd93e0: add             x0, x4, x5
    //     0xbd93e4: strb            w2, [x0, #0x17]
    // 0xbd93e8: sub             x0, x7, x6
    // 0xbd93ec: cmp             x0, #1
    // 0xbd93f0: b.ge            #0xbd9400
    // 0xbd93f4: mov             x1, x3
    // 0xbd93f8: r2 = 1
    //     0xbd93f8: movz            x2, #0x1
    // 0xbd93fc: r0 = _increaseBufferSize()
    //     0xbd93fc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9400: ldur            x2, [fp, #-8]
    // 0xbd9404: ldur            x3, [fp, #-0x10]
    // 0xbd9408: LoadField: r4 = r2->field_b
    //     0xbd9408: ldur            w4, [x2, #0xb]
    // 0xbd940c: DecompressPointer r4
    //     0xbd940c: add             x4, x4, HEAP, lsl #32
    // 0xbd9410: LoadField: r5 = r2->field_13
    //     0xbd9410: ldur            x5, [x2, #0x13]
    // 0xbd9414: add             x0, x5, #1
    // 0xbd9418: StoreField: r2->field_13 = r0
    //     0xbd9418: stur            x0, [x2, #0x13]
    // 0xbd941c: LoadField: r0 = r4->field_13
    //     0xbd941c: ldur            w0, [x4, #0x13]
    // 0xbd9420: r1 = LoadInt32Instr(r0)
    //     0xbd9420: sbfx            x1, x0, #1, #0x1f
    // 0xbd9424: mov             x0, x1
    // 0xbd9428: mov             x1, x5
    // 0xbd942c: cmp             x1, x0
    // 0xbd9430: b.hs            #0xbd9928
    // 0xbd9434: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd9434: add             x0, x4, x5
    //     0xbd9438: strb            wzr, [x0, #0x17]
    // 0xbd943c: LoadField: r4 = r3->field_7
    //     0xbd943c: ldur            x4, [x3, #7]
    // 0xbd9440: r0 = BoxInt64Instr(r4)
    //     0xbd9440: sbfiz           x0, x4, #1, #0x1f
    //     0xbd9444: cmp             x4, x0, asr #1
    //     0xbd9448: b.eq            #0xbd9454
    //     0xbd944c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9450: stur            x4, [x0, #7]
    // 0xbd9454: r16 = <int>
    //     0xbd9454: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd9458: stp             x2, x16, [SP, #8]
    // 0xbd945c: str             x0, [SP]
    // 0xbd9460: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9460: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9464: r0 = write()
    //     0xbd9464: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9468: ldur            x0, [fp, #-8]
    // 0xbd946c: LoadField: r1 = r0->field_b
    //     0xbd946c: ldur            w1, [x0, #0xb]
    // 0xbd9470: DecompressPointer r1
    //     0xbd9470: add             x1, x1, HEAP, lsl #32
    // 0xbd9474: LoadField: r2 = r1->field_13
    //     0xbd9474: ldur            w2, [x1, #0x13]
    // 0xbd9478: LoadField: r1 = r0->field_13
    //     0xbd9478: ldur            x1, [x0, #0x13]
    // 0xbd947c: r3 = LoadInt32Instr(r2)
    //     0xbd947c: sbfx            x3, x2, #1, #0x1f
    // 0xbd9480: sub             x2, x3, x1
    // 0xbd9484: cmp             x2, #1
    // 0xbd9488: b.ge            #0xbd9498
    // 0xbd948c: mov             x1, x0
    // 0xbd9490: r2 = 1
    //     0xbd9490: movz            x2, #0x1
    // 0xbd9494: r0 = _increaseBufferSize()
    //     0xbd9494: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9498: ldur            x2, [fp, #-8]
    // 0xbd949c: ldur            x3, [fp, #-0x10]
    // 0xbd94a0: r4 = 1
    //     0xbd94a0: movz            x4, #0x1
    // 0xbd94a4: LoadField: r5 = r2->field_b
    //     0xbd94a4: ldur            w5, [x2, #0xb]
    // 0xbd94a8: DecompressPointer r5
    //     0xbd94a8: add             x5, x5, HEAP, lsl #32
    // 0xbd94ac: LoadField: r6 = r2->field_13
    //     0xbd94ac: ldur            x6, [x2, #0x13]
    // 0xbd94b0: add             x0, x6, #1
    // 0xbd94b4: StoreField: r2->field_13 = r0
    //     0xbd94b4: stur            x0, [x2, #0x13]
    // 0xbd94b8: LoadField: r0 = r5->field_13
    //     0xbd94b8: ldur            w0, [x5, #0x13]
    // 0xbd94bc: r1 = LoadInt32Instr(r0)
    //     0xbd94bc: sbfx            x1, x0, #1, #0x1f
    // 0xbd94c0: mov             x0, x1
    // 0xbd94c4: mov             x1, x6
    // 0xbd94c8: cmp             x1, x0
    // 0xbd94cc: b.hs            #0xbd992c
    // 0xbd94d0: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd94d0: add             x0, x5, x6
    //     0xbd94d4: strb            w4, [x0, #0x17]
    // 0xbd94d8: LoadField: r5 = r3->field_f
    //     0xbd94d8: ldur            x5, [x3, #0xf]
    // 0xbd94dc: r0 = BoxInt64Instr(r5)
    //     0xbd94dc: sbfiz           x0, x5, #1, #0x1f
    //     0xbd94e0: cmp             x5, x0, asr #1
    //     0xbd94e4: b.eq            #0xbd94f0
    //     0xbd94e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd94ec: stur            x5, [x0, #7]
    // 0xbd94f0: r16 = <int>
    //     0xbd94f0: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd94f4: stp             x2, x16, [SP, #8]
    // 0xbd94f8: str             x0, [SP]
    // 0xbd94fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd94fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9500: r0 = write()
    //     0xbd9500: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9504: ldur            x0, [fp, #-8]
    // 0xbd9508: LoadField: r1 = r0->field_b
    //     0xbd9508: ldur            w1, [x0, #0xb]
    // 0xbd950c: DecompressPointer r1
    //     0xbd950c: add             x1, x1, HEAP, lsl #32
    // 0xbd9510: LoadField: r2 = r1->field_13
    //     0xbd9510: ldur            w2, [x1, #0x13]
    // 0xbd9514: LoadField: r1 = r0->field_13
    //     0xbd9514: ldur            x1, [x0, #0x13]
    // 0xbd9518: r3 = LoadInt32Instr(r2)
    //     0xbd9518: sbfx            x3, x2, #1, #0x1f
    // 0xbd951c: sub             x2, x3, x1
    // 0xbd9520: cmp             x2, #1
    // 0xbd9524: b.ge            #0xbd9534
    // 0xbd9528: mov             x1, x0
    // 0xbd952c: r2 = 1
    //     0xbd952c: movz            x2, #0x1
    // 0xbd9530: r0 = _increaseBufferSize()
    //     0xbd9530: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9534: ldur            x2, [fp, #-8]
    // 0xbd9538: ldur            x3, [fp, #-0x10]
    // 0xbd953c: r4 = 2
    //     0xbd953c: movz            x4, #0x2
    // 0xbd9540: LoadField: r5 = r2->field_b
    //     0xbd9540: ldur            w5, [x2, #0xb]
    // 0xbd9544: DecompressPointer r5
    //     0xbd9544: add             x5, x5, HEAP, lsl #32
    // 0xbd9548: LoadField: r6 = r2->field_13
    //     0xbd9548: ldur            x6, [x2, #0x13]
    // 0xbd954c: add             x0, x6, #1
    // 0xbd9550: StoreField: r2->field_13 = r0
    //     0xbd9550: stur            x0, [x2, #0x13]
    // 0xbd9554: LoadField: r0 = r5->field_13
    //     0xbd9554: ldur            w0, [x5, #0x13]
    // 0xbd9558: r1 = LoadInt32Instr(r0)
    //     0xbd9558: sbfx            x1, x0, #1, #0x1f
    // 0xbd955c: mov             x0, x1
    // 0xbd9560: mov             x1, x6
    // 0xbd9564: cmp             x1, x0
    // 0xbd9568: b.hs            #0xbd9930
    // 0xbd956c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd956c: add             x0, x5, x6
    //     0xbd9570: strb            w4, [x0, #0x17]
    // 0xbd9574: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd9574: ldur            x4, [x3, #0x17]
    // 0xbd9578: r0 = BoxInt64Instr(r4)
    //     0xbd9578: sbfiz           x0, x4, #1, #0x1f
    //     0xbd957c: cmp             x4, x0, asr #1
    //     0xbd9580: b.eq            #0xbd958c
    //     0xbd9584: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9588: stur            x4, [x0, #7]
    // 0xbd958c: r16 = <int>
    //     0xbd958c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd9590: stp             x2, x16, [SP, #8]
    // 0xbd9594: str             x0, [SP]
    // 0xbd9598: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9598: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd959c: r0 = write()
    //     0xbd959c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd95a0: ldur            x0, [fp, #-8]
    // 0xbd95a4: LoadField: r1 = r0->field_b
    //     0xbd95a4: ldur            w1, [x0, #0xb]
    // 0xbd95a8: DecompressPointer r1
    //     0xbd95a8: add             x1, x1, HEAP, lsl #32
    // 0xbd95ac: LoadField: r2 = r1->field_13
    //     0xbd95ac: ldur            w2, [x1, #0x13]
    // 0xbd95b0: LoadField: r1 = r0->field_13
    //     0xbd95b0: ldur            x1, [x0, #0x13]
    // 0xbd95b4: r3 = LoadInt32Instr(r2)
    //     0xbd95b4: sbfx            x3, x2, #1, #0x1f
    // 0xbd95b8: sub             x2, x3, x1
    // 0xbd95bc: cmp             x2, #1
    // 0xbd95c0: b.ge            #0xbd95d0
    // 0xbd95c4: mov             x1, x0
    // 0xbd95c8: r2 = 1
    //     0xbd95c8: movz            x2, #0x1
    // 0xbd95cc: r0 = _increaseBufferSize()
    //     0xbd95cc: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd95d0: ldur            x2, [fp, #-8]
    // 0xbd95d4: ldur            x3, [fp, #-0x10]
    // 0xbd95d8: r4 = 3
    //     0xbd95d8: movz            x4, #0x3
    // 0xbd95dc: LoadField: r5 = r2->field_b
    //     0xbd95dc: ldur            w5, [x2, #0xb]
    // 0xbd95e0: DecompressPointer r5
    //     0xbd95e0: add             x5, x5, HEAP, lsl #32
    // 0xbd95e4: LoadField: r6 = r2->field_13
    //     0xbd95e4: ldur            x6, [x2, #0x13]
    // 0xbd95e8: add             x0, x6, #1
    // 0xbd95ec: StoreField: r2->field_13 = r0
    //     0xbd95ec: stur            x0, [x2, #0x13]
    // 0xbd95f0: LoadField: r0 = r5->field_13
    //     0xbd95f0: ldur            w0, [x5, #0x13]
    // 0xbd95f4: r1 = LoadInt32Instr(r0)
    //     0xbd95f4: sbfx            x1, x0, #1, #0x1f
    // 0xbd95f8: mov             x0, x1
    // 0xbd95fc: mov             x1, x6
    // 0xbd9600: cmp             x1, x0
    // 0xbd9604: b.hs            #0xbd9934
    // 0xbd9608: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9608: add             x0, x5, x6
    //     0xbd960c: strb            w4, [x0, #0x17]
    // 0xbd9610: LoadField: r4 = r3->field_1f
    //     0xbd9610: ldur            x4, [x3, #0x1f]
    // 0xbd9614: r0 = BoxInt64Instr(r4)
    //     0xbd9614: sbfiz           x0, x4, #1, #0x1f
    //     0xbd9618: cmp             x4, x0, asr #1
    //     0xbd961c: b.eq            #0xbd9628
    //     0xbd9620: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9624: stur            x4, [x0, #7]
    // 0xbd9628: r16 = <int>
    //     0xbd9628: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd962c: stp             x2, x16, [SP, #8]
    // 0xbd9630: str             x0, [SP]
    // 0xbd9634: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9634: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9638: r0 = write()
    //     0xbd9638: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd963c: ldur            x0, [fp, #-8]
    // 0xbd9640: LoadField: r1 = r0->field_b
    //     0xbd9640: ldur            w1, [x0, #0xb]
    // 0xbd9644: DecompressPointer r1
    //     0xbd9644: add             x1, x1, HEAP, lsl #32
    // 0xbd9648: LoadField: r2 = r1->field_13
    //     0xbd9648: ldur            w2, [x1, #0x13]
    // 0xbd964c: LoadField: r1 = r0->field_13
    //     0xbd964c: ldur            x1, [x0, #0x13]
    // 0xbd9650: r3 = LoadInt32Instr(r2)
    //     0xbd9650: sbfx            x3, x2, #1, #0x1f
    // 0xbd9654: sub             x2, x3, x1
    // 0xbd9658: cmp             x2, #1
    // 0xbd965c: b.ge            #0xbd966c
    // 0xbd9660: mov             x1, x0
    // 0xbd9664: r2 = 1
    //     0xbd9664: movz            x2, #0x1
    // 0xbd9668: r0 = _increaseBufferSize()
    //     0xbd9668: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd966c: ldur            x2, [fp, #-8]
    // 0xbd9670: ldur            x3, [fp, #-0x10]
    // 0xbd9674: r4 = 4
    //     0xbd9674: movz            x4, #0x4
    // 0xbd9678: LoadField: r5 = r2->field_b
    //     0xbd9678: ldur            w5, [x2, #0xb]
    // 0xbd967c: DecompressPointer r5
    //     0xbd967c: add             x5, x5, HEAP, lsl #32
    // 0xbd9680: LoadField: r6 = r2->field_13
    //     0xbd9680: ldur            x6, [x2, #0x13]
    // 0xbd9684: add             x0, x6, #1
    // 0xbd9688: StoreField: r2->field_13 = r0
    //     0xbd9688: stur            x0, [x2, #0x13]
    // 0xbd968c: LoadField: r0 = r5->field_13
    //     0xbd968c: ldur            w0, [x5, #0x13]
    // 0xbd9690: r1 = LoadInt32Instr(r0)
    //     0xbd9690: sbfx            x1, x0, #1, #0x1f
    // 0xbd9694: mov             x0, x1
    // 0xbd9698: mov             x1, x6
    // 0xbd969c: cmp             x1, x0
    // 0xbd96a0: b.hs            #0xbd9938
    // 0xbd96a4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd96a4: add             x0, x5, x6
    //     0xbd96a8: strb            w4, [x0, #0x17]
    // 0xbd96ac: LoadField: r4 = r3->field_27
    //     0xbd96ac: ldur            x4, [x3, #0x27]
    // 0xbd96b0: r0 = BoxInt64Instr(r4)
    //     0xbd96b0: sbfiz           x0, x4, #1, #0x1f
    //     0xbd96b4: cmp             x4, x0, asr #1
    //     0xbd96b8: b.eq            #0xbd96c4
    //     0xbd96bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd96c0: stur            x4, [x0, #7]
    // 0xbd96c4: r16 = <int>
    //     0xbd96c4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd96c8: stp             x2, x16, [SP, #8]
    // 0xbd96cc: str             x0, [SP]
    // 0xbd96d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd96d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd96d4: r0 = write()
    //     0xbd96d4: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd96d8: ldur            x0, [fp, #-8]
    // 0xbd96dc: LoadField: r1 = r0->field_b
    //     0xbd96dc: ldur            w1, [x0, #0xb]
    // 0xbd96e0: DecompressPointer r1
    //     0xbd96e0: add             x1, x1, HEAP, lsl #32
    // 0xbd96e4: LoadField: r2 = r1->field_13
    //     0xbd96e4: ldur            w2, [x1, #0x13]
    // 0xbd96e8: LoadField: r1 = r0->field_13
    //     0xbd96e8: ldur            x1, [x0, #0x13]
    // 0xbd96ec: r3 = LoadInt32Instr(r2)
    //     0xbd96ec: sbfx            x3, x2, #1, #0x1f
    // 0xbd96f0: sub             x2, x3, x1
    // 0xbd96f4: cmp             x2, #1
    // 0xbd96f8: b.ge            #0xbd9708
    // 0xbd96fc: mov             x1, x0
    // 0xbd9700: r2 = 1
    //     0xbd9700: movz            x2, #0x1
    // 0xbd9704: r0 = _increaseBufferSize()
    //     0xbd9704: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9708: ldur            x2, [fp, #-8]
    // 0xbd970c: ldur            x3, [fp, #-0x10]
    // 0xbd9710: r4 = 5
    //     0xbd9710: movz            x4, #0x5
    // 0xbd9714: LoadField: r5 = r2->field_b
    //     0xbd9714: ldur            w5, [x2, #0xb]
    // 0xbd9718: DecompressPointer r5
    //     0xbd9718: add             x5, x5, HEAP, lsl #32
    // 0xbd971c: LoadField: r6 = r2->field_13
    //     0xbd971c: ldur            x6, [x2, #0x13]
    // 0xbd9720: add             x0, x6, #1
    // 0xbd9724: StoreField: r2->field_13 = r0
    //     0xbd9724: stur            x0, [x2, #0x13]
    // 0xbd9728: LoadField: r0 = r5->field_13
    //     0xbd9728: ldur            w0, [x5, #0x13]
    // 0xbd972c: r1 = LoadInt32Instr(r0)
    //     0xbd972c: sbfx            x1, x0, #1, #0x1f
    // 0xbd9730: mov             x0, x1
    // 0xbd9734: mov             x1, x6
    // 0xbd9738: cmp             x1, x0
    // 0xbd973c: b.hs            #0xbd993c
    // 0xbd9740: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9740: add             x0, x5, x6
    //     0xbd9744: strb            w4, [x0, #0x17]
    // 0xbd9748: LoadField: r0 = r3->field_2f
    //     0xbd9748: ldur            w0, [x3, #0x2f]
    // 0xbd974c: DecompressPointer r0
    //     0xbd974c: add             x0, x0, HEAP, lsl #32
    // 0xbd9750: r16 = <String>
    //     0xbd9750: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd9754: stp             x2, x16, [SP, #8]
    // 0xbd9758: str             x0, [SP]
    // 0xbd975c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd975c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9760: r0 = write()
    //     0xbd9760: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9764: ldur            x0, [fp, #-8]
    // 0xbd9768: LoadField: r1 = r0->field_b
    //     0xbd9768: ldur            w1, [x0, #0xb]
    // 0xbd976c: DecompressPointer r1
    //     0xbd976c: add             x1, x1, HEAP, lsl #32
    // 0xbd9770: LoadField: r2 = r1->field_13
    //     0xbd9770: ldur            w2, [x1, #0x13]
    // 0xbd9774: LoadField: r1 = r0->field_13
    //     0xbd9774: ldur            x1, [x0, #0x13]
    // 0xbd9778: r3 = LoadInt32Instr(r2)
    //     0xbd9778: sbfx            x3, x2, #1, #0x1f
    // 0xbd977c: sub             x2, x3, x1
    // 0xbd9780: cmp             x2, #1
    // 0xbd9784: b.ge            #0xbd9794
    // 0xbd9788: mov             x1, x0
    // 0xbd978c: r2 = 1
    //     0xbd978c: movz            x2, #0x1
    // 0xbd9790: r0 = _increaseBufferSize()
    //     0xbd9790: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9794: ldur            x2, [fp, #-8]
    // 0xbd9798: ldur            x3, [fp, #-0x10]
    // 0xbd979c: r4 = 6
    //     0xbd979c: movz            x4, #0x6
    // 0xbd97a0: LoadField: r5 = r2->field_b
    //     0xbd97a0: ldur            w5, [x2, #0xb]
    // 0xbd97a4: DecompressPointer r5
    //     0xbd97a4: add             x5, x5, HEAP, lsl #32
    // 0xbd97a8: LoadField: r6 = r2->field_13
    //     0xbd97a8: ldur            x6, [x2, #0x13]
    // 0xbd97ac: add             x0, x6, #1
    // 0xbd97b0: StoreField: r2->field_13 = r0
    //     0xbd97b0: stur            x0, [x2, #0x13]
    // 0xbd97b4: LoadField: r0 = r5->field_13
    //     0xbd97b4: ldur            w0, [x5, #0x13]
    // 0xbd97b8: r1 = LoadInt32Instr(r0)
    //     0xbd97b8: sbfx            x1, x0, #1, #0x1f
    // 0xbd97bc: mov             x0, x1
    // 0xbd97c0: mov             x1, x6
    // 0xbd97c4: cmp             x1, x0
    // 0xbd97c8: b.hs            #0xbd9940
    // 0xbd97cc: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd97cc: add             x0, x5, x6
    //     0xbd97d0: strb            w4, [x0, #0x17]
    // 0xbd97d4: LoadField: r0 = r3->field_33
    //     0xbd97d4: ldur            w0, [x3, #0x33]
    // 0xbd97d8: DecompressPointer r0
    //     0xbd97d8: add             x0, x0, HEAP, lsl #32
    // 0xbd97dc: r16 = <String>
    //     0xbd97dc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd97e0: stp             x2, x16, [SP, #8]
    // 0xbd97e4: str             x0, [SP]
    // 0xbd97e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd97e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd97ec: r0 = write()
    //     0xbd97ec: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd97f0: ldur            x0, [fp, #-8]
    // 0xbd97f4: LoadField: r1 = r0->field_b
    //     0xbd97f4: ldur            w1, [x0, #0xb]
    // 0xbd97f8: DecompressPointer r1
    //     0xbd97f8: add             x1, x1, HEAP, lsl #32
    // 0xbd97fc: LoadField: r2 = r1->field_13
    //     0xbd97fc: ldur            w2, [x1, #0x13]
    // 0xbd9800: LoadField: r1 = r0->field_13
    //     0xbd9800: ldur            x1, [x0, #0x13]
    // 0xbd9804: r3 = LoadInt32Instr(r2)
    //     0xbd9804: sbfx            x3, x2, #1, #0x1f
    // 0xbd9808: sub             x2, x3, x1
    // 0xbd980c: cmp             x2, #1
    // 0xbd9810: b.ge            #0xbd9820
    // 0xbd9814: mov             x1, x0
    // 0xbd9818: r2 = 1
    //     0xbd9818: movz            x2, #0x1
    // 0xbd981c: r0 = _increaseBufferSize()
    //     0xbd981c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9820: ldur            x2, [fp, #-8]
    // 0xbd9824: ldur            x3, [fp, #-0x10]
    // 0xbd9828: r4 = 7
    //     0xbd9828: movz            x4, #0x7
    // 0xbd982c: LoadField: r5 = r2->field_b
    //     0xbd982c: ldur            w5, [x2, #0xb]
    // 0xbd9830: DecompressPointer r5
    //     0xbd9830: add             x5, x5, HEAP, lsl #32
    // 0xbd9834: LoadField: r6 = r2->field_13
    //     0xbd9834: ldur            x6, [x2, #0x13]
    // 0xbd9838: add             x0, x6, #1
    // 0xbd983c: StoreField: r2->field_13 = r0
    //     0xbd983c: stur            x0, [x2, #0x13]
    // 0xbd9840: LoadField: r0 = r5->field_13
    //     0xbd9840: ldur            w0, [x5, #0x13]
    // 0xbd9844: r1 = LoadInt32Instr(r0)
    //     0xbd9844: sbfx            x1, x0, #1, #0x1f
    // 0xbd9848: mov             x0, x1
    // 0xbd984c: mov             x1, x6
    // 0xbd9850: cmp             x1, x0
    // 0xbd9854: b.hs            #0xbd9944
    // 0xbd9858: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9858: add             x0, x5, x6
    //     0xbd985c: strb            w4, [x0, #0x17]
    // 0xbd9860: LoadField: r0 = r3->field_37
    //     0xbd9860: ldur            w0, [x3, #0x37]
    // 0xbd9864: DecompressPointer r0
    //     0xbd9864: add             x0, x0, HEAP, lsl #32
    // 0xbd9868: r16 = <Map<String, dynamic>>
    //     0xbd9868: ldr             x16, [PP, #0x1d40]  ; [pp+0x1d40] TypeArguments: <Map<String, dynamic>>
    // 0xbd986c: stp             x2, x16, [SP, #8]
    // 0xbd9870: str             x0, [SP]
    // 0xbd9874: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9874: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9878: r0 = write()
    //     0xbd9878: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd987c: ldur            x0, [fp, #-8]
    // 0xbd9880: LoadField: r1 = r0->field_b
    //     0xbd9880: ldur            w1, [x0, #0xb]
    // 0xbd9884: DecompressPointer r1
    //     0xbd9884: add             x1, x1, HEAP, lsl #32
    // 0xbd9888: LoadField: r2 = r1->field_13
    //     0xbd9888: ldur            w2, [x1, #0x13]
    // 0xbd988c: LoadField: r1 = r0->field_13
    //     0xbd988c: ldur            x1, [x0, #0x13]
    // 0xbd9890: r3 = LoadInt32Instr(r2)
    //     0xbd9890: sbfx            x3, x2, #1, #0x1f
    // 0xbd9894: sub             x2, x3, x1
    // 0xbd9898: cmp             x2, #1
    // 0xbd989c: b.ge            #0xbd98ac
    // 0xbd98a0: mov             x1, x0
    // 0xbd98a4: r2 = 1
    //     0xbd98a4: movz            x2, #0x1
    // 0xbd98a8: r0 = _increaseBufferSize()
    //     0xbd98a8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd98ac: ldur            x2, [fp, #-8]
    // 0xbd98b0: ldur            x3, [fp, #-0x10]
    // 0xbd98b4: r4 = 8
    //     0xbd98b4: movz            x4, #0x8
    // 0xbd98b8: LoadField: r5 = r2->field_b
    //     0xbd98b8: ldur            w5, [x2, #0xb]
    // 0xbd98bc: DecompressPointer r5
    //     0xbd98bc: add             x5, x5, HEAP, lsl #32
    // 0xbd98c0: LoadField: r6 = r2->field_13
    //     0xbd98c0: ldur            x6, [x2, #0x13]
    // 0xbd98c4: add             x0, x6, #1
    // 0xbd98c8: StoreField: r2->field_13 = r0
    //     0xbd98c8: stur            x0, [x2, #0x13]
    // 0xbd98cc: LoadField: r0 = r5->field_13
    //     0xbd98cc: ldur            w0, [x5, #0x13]
    // 0xbd98d0: r1 = LoadInt32Instr(r0)
    //     0xbd98d0: sbfx            x1, x0, #1, #0x1f
    // 0xbd98d4: mov             x0, x1
    // 0xbd98d8: mov             x1, x6
    // 0xbd98dc: cmp             x1, x0
    // 0xbd98e0: b.hs            #0xbd9948
    // 0xbd98e4: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd98e4: add             x0, x5, x6
    //     0xbd98e8: strb            w4, [x0, #0x17]
    // 0xbd98ec: LoadField: r0 = r3->field_3b
    //     0xbd98ec: ldur            w0, [x3, #0x3b]
    // 0xbd98f0: DecompressPointer r0
    //     0xbd98f0: add             x0, x0, HEAP, lsl #32
    // 0xbd98f4: r16 = <DateTime?>
    //     0xbd98f4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b278] TypeArguments: <DateTime?>
    //     0xbd98f8: ldr             x16, [x16, #0x278]
    // 0xbd98fc: stp             x2, x16, [SP, #8]
    // 0xbd9900: str             x0, [SP]
    // 0xbd9904: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9904: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9908: r0 = write()
    //     0xbd9908: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd990c: r0 = Null
    //     0xbd990c: mov             x0, NULL
    // 0xbd9910: LeaveFrame
    //     0xbd9910: mov             SP, fp
    //     0xbd9914: ldp             fp, lr, [SP], #0x10
    // 0xbd9918: ret
    //     0xbd9918: ret             
    // 0xbd991c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd991c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9920: b               #0xbd9344
    // 0xbd9924: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9924: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9928: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9928: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd992c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd992c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9930: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9930: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9934: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9934: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9938: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9938: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd993c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd993c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9940: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9940: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9944: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9944: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9948: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0414, size: 0x24
    // 0xbf0414: r1 = 50
    //     0xbf0414: movz            x1, #0x32
    // 0xbf0418: r16 = LoadInt32Instr(r1)
    //     0xbf0418: sbfx            x16, x1, #1, #0x1f
    // 0xbf041c: r17 = 11601
    //     0xbf041c: movz            x17, #0x2d51
    // 0xbf0420: mul             x0, x16, x17
    // 0xbf0424: umulh           x16, x16, x17
    // 0xbf0428: eor             x0, x0, x16
    // 0xbf042c: r0 = 0
    //     0xbf042c: eor             x0, x0, x0, lsr #32
    // 0xbf0430: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0434: ret
    //     0xbf0434: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd77220, size: 0x9c
    // 0xd77220: EnterFrame
    //     0xd77220: stp             fp, lr, [SP, #-0x10]!
    //     0xd77224: mov             fp, SP
    // 0xd77228: AllocStack(0x10)
    //     0xd77228: sub             SP, SP, #0x10
    // 0xd7722c: CheckStackOverflow
    //     0xd7722c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd77230: cmp             SP, x16
    //     0xd77234: b.ls            #0xd772b4
    // 0xd77238: ldr             x0, [fp, #0x10]
    // 0xd7723c: cmp             w0, NULL
    // 0xd77240: b.ne            #0xd77254
    // 0xd77244: r0 = false
    //     0xd77244: add             x0, NULL, #0x30  ; false
    // 0xd77248: LeaveFrame
    //     0xd77248: mov             SP, fp
    //     0xd7724c: ldp             fp, lr, [SP], #0x10
    // 0xd77250: ret
    //     0xd77250: ret             
    // 0xd77254: ldr             x1, [fp, #0x18]
    // 0xd77258: cmp             w1, w0
    // 0xd7725c: b.ne            #0xd77268
    // 0xd77260: r0 = true
    //     0xd77260: add             x0, NULL, #0x20  ; true
    // 0xd77264: b               #0xd772a8
    // 0xd77268: r1 = 60
    //     0xd77268: movz            x1, #0x3c
    // 0xd7726c: branchIfSmi(r0, 0xd77278)
    //     0xd7726c: tbz             w0, #0, #0xd77278
    // 0xd77270: r1 = LoadClassIdInstr(r0)
    //     0xd77270: ldur            x1, [x0, #-1]
    //     0xd77274: ubfx            x1, x1, #0xc, #0x14
    // 0xd77278: cmp             x1, #0x669
    // 0xd7727c: b.ne            #0xd772a4
    // 0xd77280: r16 = VerseBookmarkAdapter
    //     0xd77280: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b258] Type: VerseBookmarkAdapter
    //     0xd77284: ldr             x16, [x16, #0x258]
    // 0xd77288: r30 = VerseBookmarkAdapter
    //     0xd77288: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b258] Type: VerseBookmarkAdapter
    //     0xd7728c: ldr             lr, [lr, #0x258]
    // 0xd77290: stp             lr, x16, [SP]
    // 0xd77294: r0 = ==()
    //     0xd77294: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd77298: tbnz            w0, #4, #0xd772a4
    // 0xd7729c: r0 = true
    //     0xd7729c: add             x0, NULL, #0x20  ; true
    // 0xd772a0: b               #0xd772a8
    // 0xd772a4: r0 = false
    //     0xd772a4: add             x0, NULL, #0x30  ; false
    // 0xd772a8: LeaveFrame
    //     0xd772a8: mov             SP, fp
    //     0xd772ac: ldp             fp, lr, [SP], #0x10
    // 0xd772b0: ret
    //     0xd772b0: ret             
    // 0xd772b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd772b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd772b8: b               #0xd77238
  }
}
