// lib: , url: package:nuonline/app/data/models/video.dart

// class id: 1050063, size: 0x8
class :: {
}

// class id: 5564, size: 0x34, field offset: 0x8
//   const constructor, 
class VideoDetail extends Equatable {

  factory _ VideoDetail.fromMap(/* No info */) {
    // ** addr: 0x906194, size: 0x504
    // 0x906194: EnterFrame
    //     0x906194: stp             fp, lr, [SP, #-0x10]!
    //     0x906198: mov             fp, SP
    // 0x90619c: AllocStack(0x68)
    //     0x90619c: sub             SP, SP, #0x68
    // 0x9061a0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x9061a0: mov             x3, x2
    //     0x9061a4: stur            x2, [fp, #-8]
    // 0x9061a8: CheckStackOverflow
    //     0x9061a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9061ac: cmp             SP, x16
    //     0x9061b0: b.ls            #0x906690
    // 0x9061b4: r0 = LoadClassIdInstr(r3)
    //     0x9061b4: ldur            x0, [x3, #-1]
    //     0x9061b8: ubfx            x0, x0, #0xc, #0x14
    // 0x9061bc: mov             x1, x3
    // 0x9061c0: r2 = "id"
    //     0x9061c0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x9061c4: ldr             x2, [x2, #0x740]
    // 0x9061c8: r0 = GDT[cid_x0 + -0x114]()
    //     0x9061c8: sub             lr, x0, #0x114
    //     0x9061cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9061d0: blr             lr
    // 0x9061d4: mov             x3, x0
    // 0x9061d8: r2 = Null
    //     0x9061d8: mov             x2, NULL
    // 0x9061dc: r1 = Null
    //     0x9061dc: mov             x1, NULL
    // 0x9061e0: stur            x3, [fp, #-0x10]
    // 0x9061e4: branchIfSmi(r0, 0x90620c)
    //     0x9061e4: tbz             w0, #0, #0x90620c
    // 0x9061e8: r4 = LoadClassIdInstr(r0)
    //     0x9061e8: ldur            x4, [x0, #-1]
    //     0x9061ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9061f0: sub             x4, x4, #0x3c
    // 0x9061f4: cmp             x4, #1
    // 0x9061f8: b.ls            #0x90620c
    // 0x9061fc: r8 = int
    //     0x9061fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x906200: r3 = Null
    //     0x906200: add             x3, PP, #0x32, lsl #12  ; [pp+0x32098] Null
    //     0x906204: ldr             x3, [x3, #0x98]
    // 0x906208: r0 = int()
    //     0x906208: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x90620c: ldur            x3, [fp, #-8]
    // 0x906210: r0 = LoadClassIdInstr(r3)
    //     0x906210: ldur            x0, [x3, #-1]
    //     0x906214: ubfx            x0, x0, #0xc, #0x14
    // 0x906218: mov             x1, x3
    // 0x90621c: r2 = "youtube_id"
    //     0x90621c: add             x2, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x906220: ldr             x2, [x2, #0x3f0]
    // 0x906224: r0 = GDT[cid_x0 + -0x114]()
    //     0x906224: sub             lr, x0, #0x114
    //     0x906228: ldr             lr, [x21, lr, lsl #3]
    //     0x90622c: blr             lr
    // 0x906230: mov             x3, x0
    // 0x906234: r2 = Null
    //     0x906234: mov             x2, NULL
    // 0x906238: r1 = Null
    //     0x906238: mov             x1, NULL
    // 0x90623c: stur            x3, [fp, #-0x18]
    // 0x906240: r4 = 60
    //     0x906240: movz            x4, #0x3c
    // 0x906244: branchIfSmi(r0, 0x906250)
    //     0x906244: tbz             w0, #0, #0x906250
    // 0x906248: r4 = LoadClassIdInstr(r0)
    //     0x906248: ldur            x4, [x0, #-1]
    //     0x90624c: ubfx            x4, x4, #0xc, #0x14
    // 0x906250: sub             x4, x4, #0x5e
    // 0x906254: cmp             x4, #1
    // 0x906258: b.ls            #0x90626c
    // 0x90625c: r8 = String
    //     0x90625c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x906260: r3 = Null
    //     0x906260: add             x3, PP, #0x32, lsl #12  ; [pp+0x320a8] Null
    //     0x906264: ldr             x3, [x3, #0xa8]
    // 0x906268: r0 = String()
    //     0x906268: bl              #0xed43b0  ; IsType_String_Stub
    // 0x90626c: ldur            x3, [fp, #-8]
    // 0x906270: r0 = LoadClassIdInstr(r3)
    //     0x906270: ldur            x0, [x3, #-1]
    //     0x906274: ubfx            x0, x0, #0xc, #0x14
    // 0x906278: mov             x1, x3
    // 0x90627c: r2 = "title"
    //     0x90627c: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x906280: ldr             x2, [x2, #0x748]
    // 0x906284: r0 = GDT[cid_x0 + -0x114]()
    //     0x906284: sub             lr, x0, #0x114
    //     0x906288: ldr             lr, [x21, lr, lsl #3]
    //     0x90628c: blr             lr
    // 0x906290: mov             x3, x0
    // 0x906294: r2 = Null
    //     0x906294: mov             x2, NULL
    // 0x906298: r1 = Null
    //     0x906298: mov             x1, NULL
    // 0x90629c: stur            x3, [fp, #-0x20]
    // 0x9062a0: r4 = 60
    //     0x9062a0: movz            x4, #0x3c
    // 0x9062a4: branchIfSmi(r0, 0x9062b0)
    //     0x9062a4: tbz             w0, #0, #0x9062b0
    // 0x9062a8: r4 = LoadClassIdInstr(r0)
    //     0x9062a8: ldur            x4, [x0, #-1]
    //     0x9062ac: ubfx            x4, x4, #0xc, #0x14
    // 0x9062b0: sub             x4, x4, #0x5e
    // 0x9062b4: cmp             x4, #1
    // 0x9062b8: b.ls            #0x9062cc
    // 0x9062bc: r8 = String
    //     0x9062bc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9062c0: r3 = Null
    //     0x9062c0: add             x3, PP, #0x32, lsl #12  ; [pp+0x320b8] Null
    //     0x9062c4: ldr             x3, [x3, #0xb8]
    // 0x9062c8: r0 = String()
    //     0x9062c8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9062cc: ldur            x3, [fp, #-8]
    // 0x9062d0: r0 = LoadClassIdInstr(r3)
    //     0x9062d0: ldur            x0, [x3, #-1]
    //     0x9062d4: ubfx            x0, x0, #0xc, #0x14
    // 0x9062d8: mov             x1, x3
    // 0x9062dc: r2 = "description"
    //     0x9062dc: add             x2, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x9062e0: ldr             x2, [x2, #0xd28]
    // 0x9062e4: r0 = GDT[cid_x0 + -0x114]()
    //     0x9062e4: sub             lr, x0, #0x114
    //     0x9062e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9062ec: blr             lr
    // 0x9062f0: mov             x3, x0
    // 0x9062f4: r2 = Null
    //     0x9062f4: mov             x2, NULL
    // 0x9062f8: r1 = Null
    //     0x9062f8: mov             x1, NULL
    // 0x9062fc: stur            x3, [fp, #-0x28]
    // 0x906300: r4 = 60
    //     0x906300: movz            x4, #0x3c
    // 0x906304: branchIfSmi(r0, 0x906310)
    //     0x906304: tbz             w0, #0, #0x906310
    // 0x906308: r4 = LoadClassIdInstr(r0)
    //     0x906308: ldur            x4, [x0, #-1]
    //     0x90630c: ubfx            x4, x4, #0xc, #0x14
    // 0x906310: sub             x4, x4, #0x5e
    // 0x906314: cmp             x4, #1
    // 0x906318: b.ls            #0x90632c
    // 0x90631c: r8 = String
    //     0x90631c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x906320: r3 = Null
    //     0x906320: add             x3, PP, #0x32, lsl #12  ; [pp+0x320c8] Null
    //     0x906324: ldr             x3, [x3, #0xc8]
    // 0x906328: r0 = String()
    //     0x906328: bl              #0xed43b0  ; IsType_String_Stub
    // 0x90632c: ldur            x3, [fp, #-8]
    // 0x906330: r0 = LoadClassIdInstr(r3)
    //     0x906330: ldur            x0, [x3, #-1]
    //     0x906334: ubfx            x0, x0, #0xc, #0x14
    // 0x906338: mov             x1, x3
    // 0x90633c: r2 = "image"
    //     0x90633c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x906340: ldr             x2, [x2, #0x520]
    // 0x906344: r0 = GDT[cid_x0 + -0x114]()
    //     0x906344: sub             lr, x0, #0x114
    //     0x906348: ldr             lr, [x21, lr, lsl #3]
    //     0x90634c: blr             lr
    // 0x906350: mov             x3, x0
    // 0x906354: r2 = Null
    //     0x906354: mov             x2, NULL
    // 0x906358: r1 = Null
    //     0x906358: mov             x1, NULL
    // 0x90635c: stur            x3, [fp, #-0x30]
    // 0x906360: r8 = Map<String, dynamic>
    //     0x906360: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x906364: r3 = Null
    //     0x906364: add             x3, PP, #0x32, lsl #12  ; [pp+0x320d8] Null
    //     0x906368: ldr             x3, [x3, #0xd8]
    // 0x90636c: r0 = Map<String, dynamic>()
    //     0x90636c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x906370: ldur            x2, [fp, #-0x30]
    // 0x906374: r1 = Null
    //     0x906374: mov             x1, NULL
    // 0x906378: r0 = Image.fromMap()
    //     0x906378: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x90637c: mov             x4, x0
    // 0x906380: ldur            x3, [fp, #-8]
    // 0x906384: stur            x4, [fp, #-0x30]
    // 0x906388: r0 = LoadClassIdInstr(r3)
    //     0x906388: ldur            x0, [x3, #-1]
    //     0x90638c: ubfx            x0, x0, #0xc, #0x14
    // 0x906390: mov             x1, x3
    // 0x906394: r2 = "related"
    //     0x906394: add             x2, PP, #0x32, lsl #12  ; [pp+0x320e8] "related"
    //     0x906398: ldr             x2, [x2, #0xe8]
    // 0x90639c: r0 = GDT[cid_x0 + -0x114]()
    //     0x90639c: sub             lr, x0, #0x114
    //     0x9063a0: ldr             lr, [x21, lr, lsl #3]
    //     0x9063a4: blr             lr
    // 0x9063a8: mov             x3, x0
    // 0x9063ac: r2 = Null
    //     0x9063ac: mov             x2, NULL
    // 0x9063b0: r1 = Null
    //     0x9063b0: mov             x1, NULL
    // 0x9063b4: stur            x3, [fp, #-0x38]
    // 0x9063b8: r4 = 60
    //     0x9063b8: movz            x4, #0x3c
    // 0x9063bc: branchIfSmi(r0, 0x9063c8)
    //     0x9063bc: tbz             w0, #0, #0x9063c8
    // 0x9063c0: r4 = LoadClassIdInstr(r0)
    //     0x9063c0: ldur            x4, [x0, #-1]
    //     0x9063c4: ubfx            x4, x4, #0xc, #0x14
    // 0x9063c8: sub             x4, x4, #0x5a
    // 0x9063cc: cmp             x4, #2
    // 0x9063d0: b.ls            #0x9063e8
    // 0x9063d4: r8 = List?
    //     0x9063d4: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x9063d8: ldr             x8, [x8, #0x140]
    // 0x9063dc: r3 = Null
    //     0x9063dc: add             x3, PP, #0x32, lsl #12  ; [pp+0x320f0] Null
    //     0x9063e0: ldr             x3, [x3, #0xf0]
    // 0x9063e4: r0 = List?()
    //     0x9063e4: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x9063e8: ldur            x0, [fp, #-0x38]
    // 0x9063ec: cmp             w0, NULL
    // 0x9063f0: b.ne            #0x906408
    // 0x9063f4: r1 = Null
    //     0x9063f4: mov             x1, NULL
    // 0x9063f8: r2 = 0
    //     0x9063f8: movz            x2, #0
    // 0x9063fc: r0 = _GrowableList()
    //     0x9063fc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x906400: mov             x3, x0
    // 0x906404: b               #0x90640c
    // 0x906408: mov             x3, x0
    // 0x90640c: ldur            x0, [fp, #-8]
    // 0x906410: stur            x3, [fp, #-0x38]
    // 0x906414: r1 = Function '<anonymous closure>': static.
    //     0x906414: add             x1, PP, #0x32, lsl #12  ; [pp+0x32100] AnonymousClosure: static (0x9066a4), in [package:nuonline/app/data/models/video.dart] VideoDetail::VideoDetail.fromMap (0x906194)
    //     0x906418: ldr             x1, [x1, #0x100]
    // 0x90641c: r2 = Null
    //     0x90641c: mov             x2, NULL
    // 0x906420: r0 = AllocateClosure()
    //     0x906420: bl              #0xec1630  ; AllocateClosureStub
    // 0x906424: mov             x1, x0
    // 0x906428: ldur            x0, [fp, #-0x38]
    // 0x90642c: r2 = LoadClassIdInstr(r0)
    //     0x90642c: ldur            x2, [x0, #-1]
    //     0x906430: ubfx            x2, x2, #0xc, #0x14
    // 0x906434: r16 = <Video>
    //     0x906434: add             x16, PP, #0x29, lsl #12  ; [pp+0x290c0] TypeArguments: <Video>
    //     0x906438: ldr             x16, [x16, #0xc0]
    // 0x90643c: stp             x0, x16, [SP, #8]
    // 0x906440: str             x1, [SP]
    // 0x906444: mov             x0, x2
    // 0x906448: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x906448: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90644c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x90644c: movz            x17, #0xf28c
    //     0x906450: add             lr, x0, x17
    //     0x906454: ldr             lr, [x21, lr, lsl #3]
    //     0x906458: blr             lr
    // 0x90645c: r1 = LoadClassIdInstr(r0)
    //     0x90645c: ldur            x1, [x0, #-1]
    //     0x906460: ubfx            x1, x1, #0xc, #0x14
    // 0x906464: mov             x16, x0
    // 0x906468: mov             x0, x1
    // 0x90646c: mov             x1, x16
    // 0x906470: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x906470: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x906474: r0 = GDT[cid_x0 + 0xd889]()
    //     0x906474: movz            x17, #0xd889
    //     0x906478: add             lr, x0, x17
    //     0x90647c: ldr             lr, [x21, lr, lsl #3]
    //     0x906480: blr             lr
    // 0x906484: mov             x4, x0
    // 0x906488: ldur            x3, [fp, #-8]
    // 0x90648c: stur            x4, [fp, #-0x38]
    // 0x906490: r0 = LoadClassIdInstr(r3)
    //     0x906490: ldur            x0, [x3, #-1]
    //     0x906494: ubfx            x0, x0, #0xc, #0x14
    // 0x906498: mov             x1, x3
    // 0x90649c: r2 = "category"
    //     0x90649c: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x9064a0: ldr             x2, [x2, #0x960]
    // 0x9064a4: r0 = GDT[cid_x0 + -0x114]()
    //     0x9064a4: sub             lr, x0, #0x114
    //     0x9064a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9064ac: blr             lr
    // 0x9064b0: r16 = "name"
    //     0x9064b0: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x9064b4: stp             x16, x0, [SP]
    // 0x9064b8: r4 = 0
    //     0x9064b8: movz            x4, #0
    // 0x9064bc: ldr             x0, [SP, #8]
    // 0x9064c0: r5 = UnlinkedCall_0x5f3c08
    //     0x9064c0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32108] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x9064c4: ldp             x5, lr, [x16, #0x108]
    // 0x9064c8: blr             lr
    // 0x9064cc: mov             x3, x0
    // 0x9064d0: r2 = Null
    //     0x9064d0: mov             x2, NULL
    // 0x9064d4: r1 = Null
    //     0x9064d4: mov             x1, NULL
    // 0x9064d8: stur            x3, [fp, #-0x40]
    // 0x9064dc: r4 = 60
    //     0x9064dc: movz            x4, #0x3c
    // 0x9064e0: branchIfSmi(r0, 0x9064ec)
    //     0x9064e0: tbz             w0, #0, #0x9064ec
    // 0x9064e4: r4 = LoadClassIdInstr(r0)
    //     0x9064e4: ldur            x4, [x0, #-1]
    //     0x9064e8: ubfx            x4, x4, #0xc, #0x14
    // 0x9064ec: sub             x4, x4, #0x5e
    // 0x9064f0: cmp             x4, #1
    // 0x9064f4: b.ls            #0x906508
    // 0x9064f8: r8 = String
    //     0x9064f8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9064fc: r3 = Null
    //     0x9064fc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32118] Null
    //     0x906500: ldr             x3, [x3, #0x118]
    // 0x906504: r0 = String()
    //     0x906504: bl              #0xed43b0  ; IsType_String_Stub
    // 0x906508: ldur            x3, [fp, #-8]
    // 0x90650c: r0 = LoadClassIdInstr(r3)
    //     0x90650c: ldur            x0, [x3, #-1]
    //     0x906510: ubfx            x0, x0, #0xc, #0x14
    // 0x906514: mov             x1, x3
    // 0x906518: r2 = "category"
    //     0x906518: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x90651c: ldr             x2, [x2, #0x960]
    // 0x906520: r0 = GDT[cid_x0 + -0x114]()
    //     0x906520: sub             lr, x0, #0x114
    //     0x906524: ldr             lr, [x21, lr, lsl #3]
    //     0x906528: blr             lr
    // 0x90652c: r16 = "id"
    //     0x90652c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x906530: ldr             x16, [x16, #0x740]
    // 0x906534: stp             x16, x0, [SP]
    // 0x906538: r4 = 0
    //     0x906538: movz            x4, #0
    // 0x90653c: ldr             x0, [SP, #8]
    // 0x906540: r5 = UnlinkedCall_0x5f3c08
    //     0x906540: add             x16, PP, #0x32, lsl #12  ; [pp+0x32128] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x906544: ldp             x5, lr, [x16, #0x128]
    // 0x906548: blr             lr
    // 0x90654c: mov             x3, x0
    // 0x906550: r2 = Null
    //     0x906550: mov             x2, NULL
    // 0x906554: r1 = Null
    //     0x906554: mov             x1, NULL
    // 0x906558: stur            x3, [fp, #-0x48]
    // 0x90655c: branchIfSmi(r0, 0x906584)
    //     0x90655c: tbz             w0, #0, #0x906584
    // 0x906560: r4 = LoadClassIdInstr(r0)
    //     0x906560: ldur            x4, [x0, #-1]
    //     0x906564: ubfx            x4, x4, #0xc, #0x14
    // 0x906568: sub             x4, x4, #0x3c
    // 0x90656c: cmp             x4, #1
    // 0x906570: b.ls            #0x906584
    // 0x906574: r8 = int
    //     0x906574: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x906578: r3 = Null
    //     0x906578: add             x3, PP, #0x32, lsl #12  ; [pp+0x32138] Null
    //     0x90657c: ldr             x3, [x3, #0x138]
    // 0x906580: r0 = int()
    //     0x906580: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x906584: ldur            x1, [fp, #-8]
    // 0x906588: r0 = LoadClassIdInstr(r1)
    //     0x906588: ldur            x0, [x1, #-1]
    //     0x90658c: ubfx            x0, x0, #0xc, #0x14
    // 0x906590: r2 = "duration"
    //     0x906590: ldr             x2, [PP, #0x4e68]  ; [pp+0x4e68] "duration"
    // 0x906594: r0 = GDT[cid_x0 + -0x114]()
    //     0x906594: sub             lr, x0, #0x114
    //     0x906598: ldr             lr, [x21, lr, lsl #3]
    //     0x90659c: blr             lr
    // 0x9065a0: mov             x3, x0
    // 0x9065a4: r2 = Null
    //     0x9065a4: mov             x2, NULL
    // 0x9065a8: r1 = Null
    //     0x9065a8: mov             x1, NULL
    // 0x9065ac: stur            x3, [fp, #-8]
    // 0x9065b0: r4 = 60
    //     0x9065b0: movz            x4, #0x3c
    // 0x9065b4: branchIfSmi(r0, 0x9065c0)
    //     0x9065b4: tbz             w0, #0, #0x9065c0
    // 0x9065b8: r4 = LoadClassIdInstr(r0)
    //     0x9065b8: ldur            x4, [x0, #-1]
    //     0x9065bc: ubfx            x4, x4, #0xc, #0x14
    // 0x9065c0: sub             x4, x4, #0x5e
    // 0x9065c4: cmp             x4, #1
    // 0x9065c8: b.ls            #0x9065dc
    // 0x9065cc: r8 = String?
    //     0x9065cc: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x9065d0: r3 = Null
    //     0x9065d0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32148] Null
    //     0x9065d4: ldr             x3, [x3, #0x148]
    // 0x9065d8: r0 = String?()
    //     0x9065d8: bl              #0x600324  ; IsType_String?_Stub
    // 0x9065dc: ldur            x0, [fp, #-8]
    // 0x9065e0: cmp             w0, NULL
    // 0x9065e4: b.ne            #0x9065f4
    // 0x9065e8: r8 = "00:00"
    //     0x9065e8: add             x8, PP, #0x29, lsl #12  ; [pp+0x29478] "00:00"
    //     0x9065ec: ldr             x8, [x8, #0x478]
    // 0x9065f0: b               #0x9065f8
    // 0x9065f4: mov             x8, x0
    // 0x9065f8: ldur            x7, [fp, #-0x10]
    // 0x9065fc: ldur            x6, [fp, #-0x18]
    // 0x906600: ldur            x5, [fp, #-0x20]
    // 0x906604: ldur            x4, [fp, #-0x28]
    // 0x906608: ldur            x3, [fp, #-0x30]
    // 0x90660c: ldur            x2, [fp, #-0x38]
    // 0x906610: ldur            x1, [fp, #-0x40]
    // 0x906614: ldur            x0, [fp, #-0x48]
    // 0x906618: stur            x8, [fp, #-8]
    // 0x90661c: r9 = LoadInt32Instr(r7)
    //     0x90661c: sbfx            x9, x7, #1, #0x1f
    //     0x906620: tbz             w7, #0, #0x906628
    //     0x906624: ldur            x9, [x7, #7]
    // 0x906628: stur            x9, [fp, #-0x50]
    // 0x90662c: r0 = VideoDetail()
    //     0x90662c: bl              #0x906698  ; AllocateVideoDetailStub -> VideoDetail (size=0x34)
    // 0x906630: ldur            x1, [fp, #-0x50]
    // 0x906634: StoreField: r0->field_7 = r1
    //     0x906634: stur            x1, [x0, #7]
    // 0x906638: ldur            x1, [fp, #-0x18]
    // 0x90663c: StoreField: r0->field_f = r1
    //     0x90663c: stur            w1, [x0, #0xf]
    // 0x906640: ldur            x1, [fp, #-0x20]
    // 0x906644: StoreField: r0->field_13 = r1
    //     0x906644: stur            w1, [x0, #0x13]
    // 0x906648: ldur            x1, [fp, #-0x28]
    // 0x90664c: ArrayStore: r0[0] = r1  ; List_4
    //     0x90664c: stur            w1, [x0, #0x17]
    // 0x906650: ldur            x1, [fp, #-0x30]
    // 0x906654: StoreField: r0->field_1b = r1
    //     0x906654: stur            w1, [x0, #0x1b]
    // 0x906658: ldur            x1, [fp, #-0x38]
    // 0x90665c: StoreField: r0->field_1f = r1
    //     0x90665c: stur            w1, [x0, #0x1f]
    // 0x906660: ldur            x1, [fp, #-0x40]
    // 0x906664: StoreField: r0->field_23 = r1
    //     0x906664: stur            w1, [x0, #0x23]
    // 0x906668: ldur            x1, [fp, #-0x48]
    // 0x90666c: r2 = LoadInt32Instr(r1)
    //     0x90666c: sbfx            x2, x1, #1, #0x1f
    //     0x906670: tbz             w1, #0, #0x906678
    //     0x906674: ldur            x2, [x1, #7]
    // 0x906678: StoreField: r0->field_27 = r2
    //     0x906678: stur            x2, [x0, #0x27]
    // 0x90667c: ldur            x1, [fp, #-8]
    // 0x906680: StoreField: r0->field_2f = r1
    //     0x906680: stur            w1, [x0, #0x2f]
    // 0x906684: LeaveFrame
    //     0x906684: mov             SP, fp
    //     0x906688: ldp             fp, lr, [SP], #0x10
    // 0x90668c: ret
    //     0x90668c: ret             
    // 0x906690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906694: b               #0x9061b4
  }
  [closure] static Video <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x9066a4, size: 0x50
    // 0x9066a4: EnterFrame
    //     0x9066a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9066a8: mov             fp, SP
    // 0x9066ac: CheckStackOverflow
    //     0x9066ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9066b0: cmp             SP, x16
    //     0x9066b4: b.ls            #0x9066ec
    // 0x9066b8: ldr             x0, [fp, #0x10]
    // 0x9066bc: r2 = Null
    //     0x9066bc: mov             x2, NULL
    // 0x9066c0: r1 = Null
    //     0x9066c0: mov             x1, NULL
    // 0x9066c4: r8 = Map<String, dynamic>
    //     0x9066c4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x9066c8: r3 = Null
    //     0x9066c8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32158] Null
    //     0x9066cc: ldr             x3, [x3, #0x158]
    // 0x9066d0: r0 = Map<String, dynamic>()
    //     0x9066d0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9066d4: ldr             x2, [fp, #0x10]
    // 0x9066d8: r1 = Null
    //     0x9066d8: mov             x1, NULL
    // 0x9066dc: r0 = Video.fromMap()
    //     0x9066dc: bl              #0x9066f4  ; [package:nuonline/app/data/models/video.dart] Video::Video.fromMap
    // 0x9066e0: LeaveFrame
    //     0x9066e0: mov             SP, fp
    //     0x9066e4: ldp             fp, lr, [SP], #0x10
    // 0x9066e8: ret
    //     0x9066e8: ret             
    // 0x9066ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9066ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9066f0: b               #0x9066b8
  }
  _ toVideo(/* No info */) {
    // ** addr: 0xb5ab9c, size: 0x90
    // 0xb5ab9c: EnterFrame
    //     0xb5ab9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5aba0: mov             fp, SP
    // 0xb5aba4: AllocStack(0x30)
    //     0xb5aba4: sub             SP, SP, #0x30
    // 0xb5aba8: LoadField: r0 = r1->field_7
    //     0xb5aba8: ldur            x0, [x1, #7]
    // 0xb5abac: stur            x0, [fp, #-0x30]
    // 0xb5abb0: LoadField: r2 = r1->field_f
    //     0xb5abb0: ldur            w2, [x1, #0xf]
    // 0xb5abb4: DecompressPointer r2
    //     0xb5abb4: add             x2, x2, HEAP, lsl #32
    // 0xb5abb8: stur            x2, [fp, #-0x28]
    // 0xb5abbc: LoadField: r3 = r1->field_13
    //     0xb5abbc: ldur            w3, [x1, #0x13]
    // 0xb5abc0: DecompressPointer r3
    //     0xb5abc0: add             x3, x3, HEAP, lsl #32
    // 0xb5abc4: stur            x3, [fp, #-0x20]
    // 0xb5abc8: LoadField: r4 = r1->field_1b
    //     0xb5abc8: ldur            w4, [x1, #0x1b]
    // 0xb5abcc: DecompressPointer r4
    //     0xb5abcc: add             x4, x4, HEAP, lsl #32
    // 0xb5abd0: stur            x4, [fp, #-0x18]
    // 0xb5abd4: LoadField: r5 = r1->field_23
    //     0xb5abd4: ldur            w5, [x1, #0x23]
    // 0xb5abd8: DecompressPointer r5
    //     0xb5abd8: add             x5, x5, HEAP, lsl #32
    // 0xb5abdc: stur            x5, [fp, #-0x10]
    // 0xb5abe0: LoadField: r6 = r1->field_2f
    //     0xb5abe0: ldur            w6, [x1, #0x2f]
    // 0xb5abe4: DecompressPointer r6
    //     0xb5abe4: add             x6, x6, HEAP, lsl #32
    // 0xb5abe8: stur            x6, [fp, #-8]
    // 0xb5abec: r0 = Video()
    //     0xb5abec: bl              #0x906a60  ; AllocateVideoStub -> Video (size=0x24)
    // 0xb5abf0: ldur            x1, [fp, #-0x30]
    // 0xb5abf4: StoreField: r0->field_7 = r1
    //     0xb5abf4: stur            x1, [x0, #7]
    // 0xb5abf8: ldur            x1, [fp, #-0x28]
    // 0xb5abfc: StoreField: r0->field_f = r1
    //     0xb5abfc: stur            w1, [x0, #0xf]
    // 0xb5ac00: ldur            x1, [fp, #-0x20]
    // 0xb5ac04: StoreField: r0->field_13 = r1
    //     0xb5ac04: stur            w1, [x0, #0x13]
    // 0xb5ac08: ldur            x1, [fp, #-0x18]
    // 0xb5ac0c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5ac0c: stur            w1, [x0, #0x17]
    // 0xb5ac10: ldur            x1, [fp, #-0x10]
    // 0xb5ac14: StoreField: r0->field_1b = r1
    //     0xb5ac14: stur            w1, [x0, #0x1b]
    // 0xb5ac18: ldur            x1, [fp, #-8]
    // 0xb5ac1c: StoreField: r0->field_1f = r1
    //     0xb5ac1c: stur            w1, [x0, #0x1f]
    // 0xb5ac20: LeaveFrame
    //     0xb5ac20: mov             SP, fp
    //     0xb5ac24: ldp             fp, lr, [SP], #0x10
    // 0xb5ac28: ret
    //     0xb5ac28: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc244, size: 0x6c
    // 0xbdc244: EnterFrame
    //     0xbdc244: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc248: mov             fp, SP
    // 0xbdc24c: AllocStack(0x10)
    //     0xbdc24c: sub             SP, SP, #0x10
    // 0xbdc250: r3 = 2
    //     0xbdc250: movz            x3, #0x2
    // 0xbdc254: LoadField: r2 = r1->field_7
    //     0xbdc254: ldur            x2, [x1, #7]
    // 0xbdc258: r0 = BoxInt64Instr(r2)
    //     0xbdc258: sbfiz           x0, x2, #1, #0x1f
    //     0xbdc25c: cmp             x2, x0, asr #1
    //     0xbdc260: b.eq            #0xbdc26c
    //     0xbdc264: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc268: stur            x2, [x0, #7]
    // 0xbdc26c: mov             x2, x3
    // 0xbdc270: r1 = Null
    //     0xbdc270: mov             x1, NULL
    // 0xbdc274: stur            x0, [fp, #-8]
    // 0xbdc278: r0 = AllocateArray()
    //     0xbdc278: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc27c: mov             x2, x0
    // 0xbdc280: ldur            x0, [fp, #-8]
    // 0xbdc284: stur            x2, [fp, #-0x10]
    // 0xbdc288: StoreField: r2->field_f = r0
    //     0xbdc288: stur            w0, [x2, #0xf]
    // 0xbdc28c: r1 = <Object?>
    //     0xbdc28c: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc290: r0 = AllocateGrowableArray()
    //     0xbdc290: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc294: ldur            x1, [fp, #-0x10]
    // 0xbdc298: StoreField: r0->field_f = r1
    //     0xbdc298: stur            w1, [x0, #0xf]
    // 0xbdc29c: r1 = 2
    //     0xbdc29c: movz            x1, #0x2
    // 0xbdc2a0: StoreField: r0->field_b = r1
    //     0xbdc2a0: stur            w1, [x0, #0xb]
    // 0xbdc2a4: LeaveFrame
    //     0xbdc2a4: mov             SP, fp
    //     0xbdc2a8: ldp             fp, lr, [SP], #0x10
    // 0xbdc2ac: ret
    //     0xbdc2ac: ret             
  }
}

// class id: 5565, size: 0x24, field offset: 0x8
//   const constructor, 
class Video extends Equatable {

  factory Video Video.fromMap(dynamic, Map<String, dynamic>) {
    // ** addr: 0x9066f4, size: 0x36c
    // 0x9066f4: EnterFrame
    //     0x9066f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9066f8: mov             fp, SP
    // 0x9066fc: AllocStack(0x48)
    //     0x9066fc: sub             SP, SP, #0x48
    // 0x906700: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x906700: mov             x3, x2
    //     0x906704: stur            x2, [fp, #-8]
    // 0x906708: CheckStackOverflow
    //     0x906708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90670c: cmp             SP, x16
    //     0x906710: b.ls            #0x906a58
    // 0x906714: r0 = LoadClassIdInstr(r3)
    //     0x906714: ldur            x0, [x3, #-1]
    //     0x906718: ubfx            x0, x0, #0xc, #0x14
    // 0x90671c: mov             x1, x3
    // 0x906720: r2 = "id"
    //     0x906720: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x906724: ldr             x2, [x2, #0x740]
    // 0x906728: r0 = GDT[cid_x0 + -0x114]()
    //     0x906728: sub             lr, x0, #0x114
    //     0x90672c: ldr             lr, [x21, lr, lsl #3]
    //     0x906730: blr             lr
    // 0x906734: mov             x3, x0
    // 0x906738: r2 = Null
    //     0x906738: mov             x2, NULL
    // 0x90673c: r1 = Null
    //     0x90673c: mov             x1, NULL
    // 0x906740: stur            x3, [fp, #-0x10]
    // 0x906744: branchIfSmi(r0, 0x90676c)
    //     0x906744: tbz             w0, #0, #0x90676c
    // 0x906748: r4 = LoadClassIdInstr(r0)
    //     0x906748: ldur            x4, [x0, #-1]
    //     0x90674c: ubfx            x4, x4, #0xc, #0x14
    // 0x906750: sub             x4, x4, #0x3c
    // 0x906754: cmp             x4, #1
    // 0x906758: b.ls            #0x90676c
    // 0x90675c: r8 = int
    //     0x90675c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x906760: r3 = Null
    //     0x906760: add             x3, PP, #0x29, lsl #12  ; [pp+0x293e0] Null
    //     0x906764: ldr             x3, [x3, #0x3e0]
    // 0x906768: r0 = int()
    //     0x906768: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x90676c: ldur            x3, [fp, #-8]
    // 0x906770: r0 = LoadClassIdInstr(r3)
    //     0x906770: ldur            x0, [x3, #-1]
    //     0x906774: ubfx            x0, x0, #0xc, #0x14
    // 0x906778: mov             x1, x3
    // 0x90677c: r2 = "youtube_id"
    //     0x90677c: add             x2, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0x906780: ldr             x2, [x2, #0x3f0]
    // 0x906784: r0 = GDT[cid_x0 + -0x114]()
    //     0x906784: sub             lr, x0, #0x114
    //     0x906788: ldr             lr, [x21, lr, lsl #3]
    //     0x90678c: blr             lr
    // 0x906790: mov             x3, x0
    // 0x906794: r2 = Null
    //     0x906794: mov             x2, NULL
    // 0x906798: r1 = Null
    //     0x906798: mov             x1, NULL
    // 0x90679c: stur            x3, [fp, #-0x18]
    // 0x9067a0: r4 = 60
    //     0x9067a0: movz            x4, #0x3c
    // 0x9067a4: branchIfSmi(r0, 0x9067b0)
    //     0x9067a4: tbz             w0, #0, #0x9067b0
    // 0x9067a8: r4 = LoadClassIdInstr(r0)
    //     0x9067a8: ldur            x4, [x0, #-1]
    //     0x9067ac: ubfx            x4, x4, #0xc, #0x14
    // 0x9067b0: sub             x4, x4, #0x5e
    // 0x9067b4: cmp             x4, #1
    // 0x9067b8: b.ls            #0x9067cc
    // 0x9067bc: r8 = String
    //     0x9067bc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9067c0: r3 = Null
    //     0x9067c0: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f8] Null
    //     0x9067c4: ldr             x3, [x3, #0x3f8]
    // 0x9067c8: r0 = String()
    //     0x9067c8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x9067cc: ldur            x3, [fp, #-8]
    // 0x9067d0: r0 = LoadClassIdInstr(r3)
    //     0x9067d0: ldur            x0, [x3, #-1]
    //     0x9067d4: ubfx            x0, x0, #0xc, #0x14
    // 0x9067d8: mov             x1, x3
    // 0x9067dc: r2 = "title"
    //     0x9067dc: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x9067e0: ldr             x2, [x2, #0x748]
    // 0x9067e4: r0 = GDT[cid_x0 + -0x114]()
    //     0x9067e4: sub             lr, x0, #0x114
    //     0x9067e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9067ec: blr             lr
    // 0x9067f0: mov             x3, x0
    // 0x9067f4: r2 = Null
    //     0x9067f4: mov             x2, NULL
    // 0x9067f8: r1 = Null
    //     0x9067f8: mov             x1, NULL
    // 0x9067fc: stur            x3, [fp, #-0x20]
    // 0x906800: r4 = 60
    //     0x906800: movz            x4, #0x3c
    // 0x906804: branchIfSmi(r0, 0x906810)
    //     0x906804: tbz             w0, #0, #0x906810
    // 0x906808: r4 = LoadClassIdInstr(r0)
    //     0x906808: ldur            x4, [x0, #-1]
    //     0x90680c: ubfx            x4, x4, #0xc, #0x14
    // 0x906810: sub             x4, x4, #0x5e
    // 0x906814: cmp             x4, #1
    // 0x906818: b.ls            #0x90682c
    // 0x90681c: r8 = String
    //     0x90681c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x906820: r3 = Null
    //     0x906820: add             x3, PP, #0x29, lsl #12  ; [pp+0x29408] Null
    //     0x906824: ldr             x3, [x3, #0x408]
    // 0x906828: r0 = String()
    //     0x906828: bl              #0xed43b0  ; IsType_String_Stub
    // 0x90682c: ldur            x3, [fp, #-8]
    // 0x906830: r0 = LoadClassIdInstr(r3)
    //     0x906830: ldur            x0, [x3, #-1]
    //     0x906834: ubfx            x0, x0, #0xc, #0x14
    // 0x906838: mov             x1, x3
    // 0x90683c: r2 = "image"
    //     0x90683c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0x906840: ldr             x2, [x2, #0x520]
    // 0x906844: r0 = GDT[cid_x0 + -0x114]()
    //     0x906844: sub             lr, x0, #0x114
    //     0x906848: ldr             lr, [x21, lr, lsl #3]
    //     0x90684c: blr             lr
    // 0x906850: mov             x3, x0
    // 0x906854: r2 = Null
    //     0x906854: mov             x2, NULL
    // 0x906858: r1 = Null
    //     0x906858: mov             x1, NULL
    // 0x90685c: stur            x3, [fp, #-0x28]
    // 0x906860: r8 = Map<String, dynamic>
    //     0x906860: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x906864: r3 = Null
    //     0x906864: add             x3, PP, #0x29, lsl #12  ; [pp+0x29418] Null
    //     0x906868: ldr             x3, [x3, #0x418]
    // 0x90686c: r0 = Map<String, dynamic>()
    //     0x90686c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x906870: ldur            x2, [fp, #-0x28]
    // 0x906874: r1 = Null
    //     0x906874: mov             x1, NULL
    // 0x906878: r0 = Image.fromMap()
    //     0x906878: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0x90687c: mov             x4, x0
    // 0x906880: ldur            x3, [fp, #-8]
    // 0x906884: stur            x4, [fp, #-0x28]
    // 0x906888: r0 = LoadClassIdInstr(r3)
    //     0x906888: ldur            x0, [x3, #-1]
    //     0x90688c: ubfx            x0, x0, #0xc, #0x14
    // 0x906890: mov             x1, x3
    // 0x906894: r2 = "category"
    //     0x906894: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x906898: ldr             x2, [x2, #0x960]
    // 0x90689c: r0 = GDT[cid_x0 + -0x114]()
    //     0x90689c: sub             lr, x0, #0x114
    //     0x9068a0: ldr             lr, [x21, lr, lsl #3]
    //     0x9068a4: blr             lr
    // 0x9068a8: r16 = "name"
    //     0x9068a8: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x9068ac: stp             x16, x0, [SP]
    // 0x9068b0: r4 = 0
    //     0x9068b0: movz            x4, #0
    // 0x9068b4: ldr             x0, [SP, #8]
    // 0x9068b8: r16 = UnlinkedCall_0x5f3c08
    //     0x9068b8: add             x16, PP, #0x29, lsl #12  ; [pp+0x29428] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x9068bc: add             x16, x16, #0x428
    // 0x9068c0: ldp             x5, lr, [x16]
    // 0x9068c4: blr             lr
    // 0x9068c8: mov             x3, x0
    // 0x9068cc: r2 = Null
    //     0x9068cc: mov             x2, NULL
    // 0x9068d0: r1 = Null
    //     0x9068d0: mov             x1, NULL
    // 0x9068d4: stur            x3, [fp, #-0x30]
    // 0x9068d8: r4 = 60
    //     0x9068d8: movz            x4, #0x3c
    // 0x9068dc: branchIfSmi(r0, 0x9068e8)
    //     0x9068dc: tbz             w0, #0, #0x9068e8
    // 0x9068e0: r4 = LoadClassIdInstr(r0)
    //     0x9068e0: ldur            x4, [x0, #-1]
    //     0x9068e4: ubfx            x4, x4, #0xc, #0x14
    // 0x9068e8: sub             x4, x4, #0x5e
    // 0x9068ec: cmp             x4, #1
    // 0x9068f0: b.ls            #0x906904
    // 0x9068f4: r8 = String
    //     0x9068f4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x9068f8: r3 = Null
    //     0x9068f8: add             x3, PP, #0x29, lsl #12  ; [pp+0x29438] Null
    //     0x9068fc: ldr             x3, [x3, #0x438]
    // 0x906900: r0 = String()
    //     0x906900: bl              #0xed43b0  ; IsType_String_Stub
    // 0x906904: ldur            x3, [fp, #-8]
    // 0x906908: r0 = LoadClassIdInstr(r3)
    //     0x906908: ldur            x0, [x3, #-1]
    //     0x90690c: ubfx            x0, x0, #0xc, #0x14
    // 0x906910: mov             x1, x3
    // 0x906914: r2 = "category"
    //     0x906914: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x906918: ldr             x2, [x2, #0x960]
    // 0x90691c: r0 = GDT[cid_x0 + -0x114]()
    //     0x90691c: sub             lr, x0, #0x114
    //     0x906920: ldr             lr, [x21, lr, lsl #3]
    //     0x906924: blr             lr
    // 0x906928: r16 = "id"
    //     0x906928: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x90692c: ldr             x16, [x16, #0x740]
    // 0x906930: stp             x16, x0, [SP]
    // 0x906934: r4 = 0
    //     0x906934: movz            x4, #0
    // 0x906938: ldr             x0, [SP, #8]
    // 0x90693c: r16 = UnlinkedCall_0x5f3c08
    //     0x90693c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29448] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x906940: add             x16, x16, #0x448
    // 0x906944: ldp             x5, lr, [x16]
    // 0x906948: blr             lr
    // 0x90694c: r2 = Null
    //     0x90694c: mov             x2, NULL
    // 0x906950: r1 = Null
    //     0x906950: mov             x1, NULL
    // 0x906954: branchIfSmi(r0, 0x90697c)
    //     0x906954: tbz             w0, #0, #0x90697c
    // 0x906958: r4 = LoadClassIdInstr(r0)
    //     0x906958: ldur            x4, [x0, #-1]
    //     0x90695c: ubfx            x4, x4, #0xc, #0x14
    // 0x906960: sub             x4, x4, #0x3c
    // 0x906964: cmp             x4, #1
    // 0x906968: b.ls            #0x90697c
    // 0x90696c: r8 = int
    //     0x90696c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x906970: r3 = Null
    //     0x906970: add             x3, PP, #0x29, lsl #12  ; [pp+0x29458] Null
    //     0x906974: ldr             x3, [x3, #0x458]
    // 0x906978: r0 = int()
    //     0x906978: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x90697c: ldur            x1, [fp, #-8]
    // 0x906980: r0 = LoadClassIdInstr(r1)
    //     0x906980: ldur            x0, [x1, #-1]
    //     0x906984: ubfx            x0, x0, #0xc, #0x14
    // 0x906988: r2 = "duration"
    //     0x906988: ldr             x2, [PP, #0x4e68]  ; [pp+0x4e68] "duration"
    // 0x90698c: r0 = GDT[cid_x0 + -0x114]()
    //     0x90698c: sub             lr, x0, #0x114
    //     0x906990: ldr             lr, [x21, lr, lsl #3]
    //     0x906994: blr             lr
    // 0x906998: mov             x3, x0
    // 0x90699c: r2 = Null
    //     0x90699c: mov             x2, NULL
    // 0x9069a0: r1 = Null
    //     0x9069a0: mov             x1, NULL
    // 0x9069a4: stur            x3, [fp, #-8]
    // 0x9069a8: r4 = 60
    //     0x9069a8: movz            x4, #0x3c
    // 0x9069ac: branchIfSmi(r0, 0x9069b8)
    //     0x9069ac: tbz             w0, #0, #0x9069b8
    // 0x9069b0: r4 = LoadClassIdInstr(r0)
    //     0x9069b0: ldur            x4, [x0, #-1]
    //     0x9069b4: ubfx            x4, x4, #0xc, #0x14
    // 0x9069b8: sub             x4, x4, #0x5e
    // 0x9069bc: cmp             x4, #1
    // 0x9069c0: b.ls            #0x9069d4
    // 0x9069c4: r8 = String?
    //     0x9069c4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x9069c8: r3 = Null
    //     0x9069c8: add             x3, PP, #0x29, lsl #12  ; [pp+0x29468] Null
    //     0x9069cc: ldr             x3, [x3, #0x468]
    // 0x9069d0: r0 = String?()
    //     0x9069d0: bl              #0x600324  ; IsType_String?_Stub
    // 0x9069d4: ldur            x0, [fp, #-8]
    // 0x9069d8: cmp             w0, NULL
    // 0x9069dc: b.ne            #0x9069ec
    // 0x9069e0: r5 = "00:00"
    //     0x9069e0: add             x5, PP, #0x29, lsl #12  ; [pp+0x29478] "00:00"
    //     0x9069e4: ldr             x5, [x5, #0x478]
    // 0x9069e8: b               #0x9069f0
    // 0x9069ec: mov             x5, x0
    // 0x9069f0: ldur            x4, [fp, #-0x10]
    // 0x9069f4: ldur            x3, [fp, #-0x18]
    // 0x9069f8: ldur            x2, [fp, #-0x20]
    // 0x9069fc: ldur            x1, [fp, #-0x28]
    // 0x906a00: ldur            x0, [fp, #-0x30]
    // 0x906a04: stur            x5, [fp, #-8]
    // 0x906a08: r6 = LoadInt32Instr(r4)
    //     0x906a08: sbfx            x6, x4, #1, #0x1f
    //     0x906a0c: tbz             w4, #0, #0x906a14
    //     0x906a10: ldur            x6, [x4, #7]
    // 0x906a14: stur            x6, [fp, #-0x38]
    // 0x906a18: r0 = Video()
    //     0x906a18: bl              #0x906a60  ; AllocateVideoStub -> Video (size=0x24)
    // 0x906a1c: ldur            x1, [fp, #-0x38]
    // 0x906a20: StoreField: r0->field_7 = r1
    //     0x906a20: stur            x1, [x0, #7]
    // 0x906a24: ldur            x1, [fp, #-0x18]
    // 0x906a28: StoreField: r0->field_f = r1
    //     0x906a28: stur            w1, [x0, #0xf]
    // 0x906a2c: ldur            x1, [fp, #-0x20]
    // 0x906a30: StoreField: r0->field_13 = r1
    //     0x906a30: stur            w1, [x0, #0x13]
    // 0x906a34: ldur            x1, [fp, #-0x28]
    // 0x906a38: ArrayStore: r0[0] = r1  ; List_4
    //     0x906a38: stur            w1, [x0, #0x17]
    // 0x906a3c: ldur            x1, [fp, #-0x30]
    // 0x906a40: StoreField: r0->field_1b = r1
    //     0x906a40: stur            w1, [x0, #0x1b]
    // 0x906a44: ldur            x1, [fp, #-8]
    // 0x906a48: StoreField: r0->field_1f = r1
    //     0x906a48: stur            w1, [x0, #0x1f]
    // 0x906a4c: LeaveFrame
    //     0x906a4c: mov             SP, fp
    //     0x906a50: ldp             fp, lr, [SP], #0x10
    // 0x906a54: ret
    //     0x906a54: ret             
    // 0x906a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906a58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906a5c: b               #0x906714
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0x91d80c, size: 0x188
    // 0x91d80c: EnterFrame
    //     0x91d80c: stp             fp, lr, [SP, #-0x10]!
    //     0x91d810: mov             fp, SP
    // 0x91d814: AllocStack(0x20)
    //     0x91d814: sub             SP, SP, #0x20
    // 0x91d818: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x91d818: mov             x3, x1
    //     0x91d81c: stur            x1, [fp, #-8]
    // 0x91d820: CheckStackOverflow
    //     0x91d820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d824: cmp             SP, x16
    //     0x91d828: b.ls            #0x91d98c
    // 0x91d82c: mov             x0, x3
    // 0x91d830: r2 = Null
    //     0x91d830: mov             x2, NULL
    // 0x91d834: r1 = Null
    //     0x91d834: mov             x1, NULL
    // 0x91d838: cmp             w0, NULL
    // 0x91d83c: b.eq            #0x91d8e0
    // 0x91d840: branchIfSmi(r0, 0x91d8e0)
    //     0x91d840: tbz             w0, #0, #0x91d8e0
    // 0x91d844: r3 = LoadClassIdInstr(r0)
    //     0x91d844: ldur            x3, [x0, #-1]
    //     0x91d848: ubfx            x3, x3, #0xc, #0x14
    // 0x91d84c: r17 = 6718
    //     0x91d84c: movz            x17, #0x1a3e
    // 0x91d850: cmp             x3, x17
    // 0x91d854: b.eq            #0x91d8e8
    // 0x91d858: sub             x3, x3, #0x5a
    // 0x91d85c: cmp             x3, #2
    // 0x91d860: b.ls            #0x91d8e8
    // 0x91d864: r4 = LoadClassIdInstr(r0)
    //     0x91d864: ldur            x4, [x0, #-1]
    //     0x91d868: ubfx            x4, x4, #0xc, #0x14
    // 0x91d86c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x91d870: ldr             x3, [x3, #0x18]
    // 0x91d874: ldr             x3, [x3, x4, lsl #3]
    // 0x91d878: LoadField: r3 = r3->field_2b
    //     0x91d878: ldur            w3, [x3, #0x2b]
    // 0x91d87c: DecompressPointer r3
    //     0x91d87c: add             x3, x3, HEAP, lsl #32
    // 0x91d880: cmp             w3, NULL
    // 0x91d884: b.eq            #0x91d8e0
    // 0x91d888: LoadField: r3 = r3->field_f
    //     0x91d888: ldur            w3, [x3, #0xf]
    // 0x91d88c: lsr             x3, x3, #3
    // 0x91d890: r17 = 6718
    //     0x91d890: movz            x17, #0x1a3e
    // 0x91d894: cmp             x3, x17
    // 0x91d898: b.eq            #0x91d8e8
    // 0x91d89c: r3 = SubtypeTestCache
    //     0x91d89c: add             x3, PP, #0x29, lsl #12  ; [pp+0x293b0] SubtypeTestCache
    //     0x91d8a0: ldr             x3, [x3, #0x3b0]
    // 0x91d8a4: r30 = Subtype1TestCacheStub
    //     0x91d8a4: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x91d8a8: LoadField: r30 = r30->field_7
    //     0x91d8a8: ldur            lr, [lr, #7]
    // 0x91d8ac: blr             lr
    // 0x91d8b0: cmp             w7, NULL
    // 0x91d8b4: b.eq            #0x91d8c0
    // 0x91d8b8: tbnz            w7, #4, #0x91d8e0
    // 0x91d8bc: b               #0x91d8e8
    // 0x91d8c0: r8 = List
    //     0x91d8c0: add             x8, PP, #0x29, lsl #12  ; [pp+0x293b8] Type: List
    //     0x91d8c4: ldr             x8, [x8, #0x3b8]
    // 0x91d8c8: r3 = SubtypeTestCache
    //     0x91d8c8: add             x3, PP, #0x29, lsl #12  ; [pp+0x293c0] SubtypeTestCache
    //     0x91d8cc: ldr             x3, [x3, #0x3c0]
    // 0x91d8d0: r30 = InstanceOfStub
    //     0x91d8d0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x91d8d4: LoadField: r30 = r30->field_7
    //     0x91d8d4: ldur            lr, [lr, #7]
    // 0x91d8d8: blr             lr
    // 0x91d8dc: b               #0x91d8ec
    // 0x91d8e0: r0 = false
    //     0x91d8e0: add             x0, NULL, #0x30  ; false
    // 0x91d8e4: b               #0x91d8ec
    // 0x91d8e8: r0 = true
    //     0x91d8e8: add             x0, NULL, #0x20  ; true
    // 0x91d8ec: tbnz            w0, #4, #0x91d970
    // 0x91d8f0: ldur            x0, [fp, #-8]
    // 0x91d8f4: r1 = Function '<anonymous closure>': static.
    //     0x91d8f4: add             x1, PP, #0x29, lsl #12  ; [pp+0x293c8] AnonymousClosure: static (0x91d994), in [package:nuonline/app/data/models/video.dart] Video::fromResponse (0x91d80c)
    //     0x91d8f8: ldr             x1, [x1, #0x3c8]
    // 0x91d8fc: r2 = Null
    //     0x91d8fc: mov             x2, NULL
    // 0x91d900: r0 = AllocateClosure()
    //     0x91d900: bl              #0xec1630  ; AllocateClosureStub
    // 0x91d904: mov             x1, x0
    // 0x91d908: ldur            x0, [fp, #-8]
    // 0x91d90c: r2 = LoadClassIdInstr(r0)
    //     0x91d90c: ldur            x2, [x0, #-1]
    //     0x91d910: ubfx            x2, x2, #0xc, #0x14
    // 0x91d914: r16 = <Video>
    //     0x91d914: add             x16, PP, #0x29, lsl #12  ; [pp+0x290c0] TypeArguments: <Video>
    //     0x91d918: ldr             x16, [x16, #0xc0]
    // 0x91d91c: stp             x0, x16, [SP, #8]
    // 0x91d920: str             x1, [SP]
    // 0x91d924: mov             x0, x2
    // 0x91d928: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91d928: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91d92c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x91d92c: movz            x17, #0xf28c
    //     0x91d930: add             lr, x0, x17
    //     0x91d934: ldr             lr, [x21, lr, lsl #3]
    //     0x91d938: blr             lr
    // 0x91d93c: r1 = LoadClassIdInstr(r0)
    //     0x91d93c: ldur            x1, [x0, #-1]
    //     0x91d940: ubfx            x1, x1, #0xc, #0x14
    // 0x91d944: mov             x16, x0
    // 0x91d948: mov             x0, x1
    // 0x91d94c: mov             x1, x16
    // 0x91d950: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d950: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d954: r0 = GDT[cid_x0 + 0xd889]()
    //     0x91d954: movz            x17, #0xd889
    //     0x91d958: add             lr, x0, x17
    //     0x91d95c: ldr             lr, [x21, lr, lsl #3]
    //     0x91d960: blr             lr
    // 0x91d964: LeaveFrame
    //     0x91d964: mov             SP, fp
    //     0x91d968: ldp             fp, lr, [SP], #0x10
    // 0x91d96c: ret
    //     0x91d96c: ret             
    // 0x91d970: r1 = <Video>
    //     0x91d970: add             x1, PP, #0x29, lsl #12  ; [pp+0x290c0] TypeArguments: <Video>
    //     0x91d974: ldr             x1, [x1, #0xc0]
    // 0x91d978: r2 = 0
    //     0x91d978: movz            x2, #0
    // 0x91d97c: r0 = _GrowableList()
    //     0x91d97c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x91d980: LeaveFrame
    //     0x91d980: mov             SP, fp
    //     0x91d984: ldp             fp, lr, [SP], #0x10
    // 0x91d988: ret
    //     0x91d988: ret             
    // 0x91d98c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d98c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d990: b               #0x91d82c
  }
  [closure] static Video <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x91d994, size: 0x50
    // 0x91d994: EnterFrame
    //     0x91d994: stp             fp, lr, [SP, #-0x10]!
    //     0x91d998: mov             fp, SP
    // 0x91d99c: CheckStackOverflow
    //     0x91d99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d9a0: cmp             SP, x16
    //     0x91d9a4: b.ls            #0x91d9dc
    // 0x91d9a8: ldr             x0, [fp, #0x10]
    // 0x91d9ac: r2 = Null
    //     0x91d9ac: mov             x2, NULL
    // 0x91d9b0: r1 = Null
    //     0x91d9b0: mov             x1, NULL
    // 0x91d9b4: r8 = Map<String, dynamic>
    //     0x91d9b4: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x91d9b8: r3 = Null
    //     0x91d9b8: add             x3, PP, #0x29, lsl #12  ; [pp+0x293d0] Null
    //     0x91d9bc: ldr             x3, [x3, #0x3d0]
    // 0x91d9c0: r0 = Map<String, dynamic>()
    //     0x91d9c0: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x91d9c4: ldr             x2, [fp, #0x10]
    // 0x91d9c8: r1 = Null
    //     0x91d9c8: mov             x1, NULL
    // 0x91d9cc: r0 = Video.fromMap()
    //     0x91d9cc: bl              #0x9066f4  ; [package:nuonline/app/data/models/video.dart] Video::Video.fromMap
    // 0x91d9d0: LeaveFrame
    //     0x91d9d0: mov             SP, fp
    //     0x91d9d4: ldp             fp, lr, [SP], #0x10
    // 0x91d9d8: ret
    //     0x91d9d8: ret             
    // 0x91d9dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d9dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d9e0: b               #0x91d9a8
  }
  get _ shareableText(/* No info */) {
    // ** addr: 0xb5aa8c, size: 0xac
    // 0xb5aa8c: EnterFrame
    //     0xb5aa8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5aa90: mov             fp, SP
    // 0xb5aa94: AllocStack(0x20)
    //     0xb5aa94: sub             SP, SP, #0x20
    // 0xb5aa98: SetupParameters(Video this /* r1 => r0, fp-0x10 */)
    //     0xb5aa98: mov             x0, x1
    //     0xb5aa9c: stur            x1, [fp, #-0x10]
    // 0xb5aaa0: CheckStackOverflow
    //     0xb5aaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5aaa4: cmp             SP, x16
    //     0xb5aaa8: b.ls            #0xb5ab30
    // 0xb5aaac: LoadField: r3 = r0->field_13
    //     0xb5aaac: ldur            w3, [x0, #0x13]
    // 0xb5aab0: DecompressPointer r3
    //     0xb5aab0: add             x3, x3, HEAP, lsl #32
    // 0xb5aab4: stur            x3, [fp, #-8]
    // 0xb5aab8: r1 = Null
    //     0xb5aab8: mov             x1, NULL
    // 0xb5aabc: r2 = 8
    //     0xb5aabc: movz            x2, #0x8
    // 0xb5aac0: r0 = AllocateArray()
    //     0xb5aac0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb5aac4: mov             x2, x0
    // 0xb5aac8: ldur            x0, [fp, #-8]
    // 0xb5aacc: stur            x2, [fp, #-0x18]
    // 0xb5aad0: StoreField: r2->field_f = r0
    //     0xb5aad0: stur            w0, [x2, #0xf]
    // 0xb5aad4: r16 = "\n\n"
    //     0xb5aad4: ldr             x16, [PP, #0x3360]  ; [pp+0x3360] "\n\n"
    // 0xb5aad8: StoreField: r2->field_13 = r16
    //     0xb5aad8: stur            w16, [x2, #0x13]
    // 0xb5aadc: ldur            x1, [fp, #-0x10]
    // 0xb5aae0: r0 = url()
    //     0xb5aae0: bl              #0xb5ab38  ; [package:nuonline/app/data/models/video.dart] Video::url
    // 0xb5aae4: ldur            x1, [fp, #-0x18]
    // 0xb5aae8: ArrayStore: r1[2] = r0  ; List_4
    //     0xb5aae8: add             x25, x1, #0x17
    //     0xb5aaec: str             w0, [x25]
    //     0xb5aaf0: tbz             w0, #0, #0xb5ab0c
    //     0xb5aaf4: ldurb           w16, [x1, #-1]
    //     0xb5aaf8: ldurb           w17, [x0, #-1]
    //     0xb5aafc: and             x16, x17, x16, lsr #2
    //     0xb5ab00: tst             x16, HEAP, lsr #32
    //     0xb5ab04: b.eq            #0xb5ab0c
    //     0xb5ab08: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb5ab0c: ldur            x0, [fp, #-0x18]
    // 0xb5ab10: r16 = "\n___\nDownload NU Online Super App, aplikasi keislaman terlengkap!\nhttps://nu.or.id/superapp (Android/iOS)"
    //     0xb5ab10: add             x16, PP, #0x29, lsl #12  ; [pp+0x29600] "\n___\nDownload NU Online Super App, aplikasi keislaman terlengkap!\nhttps://nu.or.id/superapp (Android/iOS)"
    //     0xb5ab14: ldr             x16, [x16, #0x600]
    // 0xb5ab18: StoreField: r0->field_1b = r16
    //     0xb5ab18: stur            w16, [x0, #0x1b]
    // 0xb5ab1c: str             x0, [SP]
    // 0xb5ab20: r0 = _interpolate()
    //     0xb5ab20: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb5ab24: LeaveFrame
    //     0xb5ab24: mov             SP, fp
    //     0xb5ab28: ldp             fp, lr, [SP], #0x10
    // 0xb5ab2c: ret
    //     0xb5ab2c: ret             
    // 0xb5ab30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5ab30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5ab34: b               #0xb5aaac
  }
  get _ url(/* No info */) {
    // ** addr: 0xb5ab38, size: 0x64
    // 0xb5ab38: EnterFrame
    //     0xb5ab38: stp             fp, lr, [SP, #-0x10]!
    //     0xb5ab3c: mov             fp, SP
    // 0xb5ab40: AllocStack(0x10)
    //     0xb5ab40: sub             SP, SP, #0x10
    // 0xb5ab44: SetupParameters(Video this /* r1 => r0, fp-0x8 */)
    //     0xb5ab44: mov             x0, x1
    //     0xb5ab48: stur            x1, [fp, #-8]
    // 0xb5ab4c: CheckStackOverflow
    //     0xb5ab4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5ab50: cmp             SP, x16
    //     0xb5ab54: b.ls            #0xb5ab94
    // 0xb5ab58: r1 = Null
    //     0xb5ab58: mov             x1, NULL
    // 0xb5ab5c: r2 = 4
    //     0xb5ab5c: movz            x2, #0x4
    // 0xb5ab60: r0 = AllocateArray()
    //     0xb5ab60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb5ab64: r16 = "https://www.youtube.com/watch\?v="
    //     0xb5ab64: add             x16, PP, #0x29, lsl #12  ; [pp+0x29608] "https://www.youtube.com/watch\?v="
    //     0xb5ab68: ldr             x16, [x16, #0x608]
    // 0xb5ab6c: StoreField: r0->field_f = r16
    //     0xb5ab6c: stur            w16, [x0, #0xf]
    // 0xb5ab70: ldur            x1, [fp, #-8]
    // 0xb5ab74: LoadField: r2 = r1->field_f
    //     0xb5ab74: ldur            w2, [x1, #0xf]
    // 0xb5ab78: DecompressPointer r2
    //     0xb5ab78: add             x2, x2, HEAP, lsl #32
    // 0xb5ab7c: StoreField: r0->field_13 = r2
    //     0xb5ab7c: stur            w2, [x0, #0x13]
    // 0xb5ab80: str             x0, [SP]
    // 0xb5ab84: r0 = _interpolate()
    //     0xb5ab84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb5ab88: LeaveFrame
    //     0xb5ab88: mov             SP, fp
    //     0xb5ab8c: ldp             fp, lr, [SP], #0x10
    // 0xb5ab90: ret
    //     0xb5ab90: ret             
    // 0xb5ab94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5ab94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5ab98: b               #0xb5ab58
  }
}
