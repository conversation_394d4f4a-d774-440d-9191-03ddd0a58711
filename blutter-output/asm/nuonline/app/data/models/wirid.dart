// lib: , url: package:nuonline/app/data/models/wirid.dart

// class id: 1050064, size: 0x8
class :: {
}

// class id: 1639, size: 0x14, field offset: 0xc
class WiridTitleTypeAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa68a4c, size: 0xcc
    // 0xa68a4c: EnterFrame
    //     0xa68a4c: stp             fp, lr, [SP, #-0x10]!
    //     0xa68a50: mov             fp, SP
    // 0xa68a54: LoadField: r3 = r2->field_23
    //     0xa68a54: ldur            x3, [x2, #0x23]
    // 0xa68a58: add             x0, x3, #1
    // 0xa68a5c: LoadField: r1 = r2->field_1b
    //     0xa68a5c: ldur            x1, [x2, #0x1b]
    // 0xa68a60: cmp             x0, x1
    // 0xa68a64: b.gt            #0xa68aec
    // 0xa68a68: LoadField: r4 = r2->field_7
    //     0xa68a68: ldur            w4, [x2, #7]
    // 0xa68a6c: DecompressPointer r4
    //     0xa68a6c: add             x4, x4, HEAP, lsl #32
    // 0xa68a70: StoreField: r2->field_23 = r0
    //     0xa68a70: stur            x0, [x2, #0x23]
    // 0xa68a74: LoadField: r0 = r4->field_13
    //     0xa68a74: ldur            w0, [x4, #0x13]
    // 0xa68a78: r1 = LoadInt32Instr(r0)
    //     0xa68a78: sbfx            x1, x0, #1, #0x1f
    // 0xa68a7c: mov             x0, x1
    // 0xa68a80: mov             x1, x3
    // 0xa68a84: cmp             x1, x0
    // 0xa68a88: b.hs            #0xa68b14
    // 0xa68a8c: LoadField: r0 = r4->field_7
    //     0xa68a8c: ldur            x0, [x4, #7]
    // 0xa68a90: ldrb            w1, [x0, x3]
    // 0xa68a94: cmp             x1, #0
    // 0xa68a98: b.gt            #0xa68ab8
    // 0xa68a9c: lsl             x0, x1, #1
    // 0xa68aa0: cbnz            w0, #0xa68ad8
    // 0xa68aa4: r0 = Instance_WiridTitleType
    //     0xa68aa4: add             x0, PP, #0x20, lsl #12  ; [pp+0x20ab0] Obj!WiridTitleType@e302a1
    //     0xa68aa8: ldr             x0, [x0, #0xab0]
    // 0xa68aac: LeaveFrame
    //     0xa68aac: mov             SP, fp
    //     0xa68ab0: ldp             fp, lr, [SP], #0x10
    // 0xa68ab4: ret
    //     0xa68ab4: ret             
    // 0xa68ab8: lsl             x0, x1, #1
    // 0xa68abc: cmp             w0, #2
    // 0xa68ac0: b.ne            #0xa68ad8
    // 0xa68ac4: r0 = Instance_WiridTitleType
    //     0xa68ac4: add             x0, PP, #0x20, lsl #12  ; [pp+0x20ab8] Obj!WiridTitleType@e302c1
    //     0xa68ac8: ldr             x0, [x0, #0xab8]
    // 0xa68acc: LeaveFrame
    //     0xa68acc: mov             SP, fp
    //     0xa68ad0: ldp             fp, lr, [SP], #0x10
    // 0xa68ad4: ret
    //     0xa68ad4: ret             
    // 0xa68ad8: r0 = Instance_WiridTitleType
    //     0xa68ad8: add             x0, PP, #0x20, lsl #12  ; [pp+0x20ab0] Obj!WiridTitleType@e302a1
    //     0xa68adc: ldr             x0, [x0, #0xab0]
    // 0xa68ae0: LeaveFrame
    //     0xa68ae0: mov             SP, fp
    //     0xa68ae4: ldp             fp, lr, [SP], #0x10
    // 0xa68ae8: ret
    //     0xa68ae8: ret             
    // 0xa68aec: r0 = RangeError()
    //     0xa68aec: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa68af0: mov             x1, x0
    // 0xa68af4: r0 = "Not enough bytes available."
    //     0xa68af4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa68af8: ldr             x0, [x0, #0x8a8]
    // 0xa68afc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa68afc: stur            w0, [x1, #0x17]
    // 0xa68b00: r0 = false
    //     0xa68b00: add             x0, NULL, #0x30  ; false
    // 0xa68b04: StoreField: r1->field_b = r0
    //     0xa68b04: stur            w0, [x1, #0xb]
    // 0xa68b08: mov             x0, x1
    // 0xa68b0c: r0 = Throw()
    //     0xa68b0c: bl              #0xec04b8  ; ThrowStub
    // 0xa68b10: brk             #0
    // 0xa68b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa68b14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd9d1c, size: 0x164
    // 0xbd9d1c: EnterFrame
    //     0xbd9d1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9d20: mov             fp, SP
    // 0xbd9d24: AllocStack(0x10)
    //     0xbd9d24: sub             SP, SP, #0x10
    // 0xbd9d28: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd9d28: mov             x4, x2
    //     0xbd9d2c: stur            x2, [fp, #-8]
    //     0xbd9d30: stur            x3, [fp, #-0x10]
    // 0xbd9d34: CheckStackOverflow
    //     0xbd9d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9d38: cmp             SP, x16
    //     0xbd9d3c: b.ls            #0xbd9e70
    // 0xbd9d40: mov             x0, x3
    // 0xbd9d44: r2 = Null
    //     0xbd9d44: mov             x2, NULL
    // 0xbd9d48: r1 = Null
    //     0xbd9d48: mov             x1, NULL
    // 0xbd9d4c: r4 = 60
    //     0xbd9d4c: movz            x4, #0x3c
    // 0xbd9d50: branchIfSmi(r0, 0xbd9d5c)
    //     0xbd9d50: tbz             w0, #0, #0xbd9d5c
    // 0xbd9d54: r4 = LoadClassIdInstr(r0)
    //     0xbd9d54: ldur            x4, [x0, #-1]
    //     0xbd9d58: ubfx            x4, x4, #0xc, #0x14
    // 0xbd9d5c: r17 = 6832
    //     0xbd9d5c: movz            x17, #0x1ab0
    // 0xbd9d60: cmp             x4, x17
    // 0xbd9d64: b.eq            #0xbd9d7c
    // 0xbd9d68: r8 = WiridTitleType
    //     0xbd9d68: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b050] Type: WiridTitleType
    //     0xbd9d6c: ldr             x8, [x8, #0x50]
    // 0xbd9d70: r3 = Null
    //     0xbd9d70: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b058] Null
    //     0xbd9d74: ldr             x3, [x3, #0x58]
    // 0xbd9d78: r0 = WiridTitleType()
    //     0xbd9d78: bl              #0x842c78  ; IsType_WiridTitleType_Stub
    // 0xbd9d7c: ldur            x0, [fp, #-0x10]
    // 0xbd9d80: LoadField: r1 = r0->field_7
    //     0xbd9d80: ldur            x1, [x0, #7]
    // 0xbd9d84: cmp             x1, #0
    // 0xbd9d88: b.gt            #0xbd9df8
    // 0xbd9d8c: ldur            x0, [fp, #-8]
    // 0xbd9d90: LoadField: r1 = r0->field_b
    //     0xbd9d90: ldur            w1, [x0, #0xb]
    // 0xbd9d94: DecompressPointer r1
    //     0xbd9d94: add             x1, x1, HEAP, lsl #32
    // 0xbd9d98: LoadField: r2 = r1->field_13
    //     0xbd9d98: ldur            w2, [x1, #0x13]
    // 0xbd9d9c: LoadField: r1 = r0->field_13
    //     0xbd9d9c: ldur            x1, [x0, #0x13]
    // 0xbd9da0: r3 = LoadInt32Instr(r2)
    //     0xbd9da0: sbfx            x3, x2, #1, #0x1f
    // 0xbd9da4: sub             x2, x3, x1
    // 0xbd9da8: cmp             x2, #1
    // 0xbd9dac: b.ge            #0xbd9dbc
    // 0xbd9db0: mov             x1, x0
    // 0xbd9db4: r2 = 1
    //     0xbd9db4: movz            x2, #0x1
    // 0xbd9db8: r0 = _increaseBufferSize()
    //     0xbd9db8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9dbc: ldur            x0, [fp, #-8]
    // 0xbd9dc0: LoadField: r2 = r0->field_b
    //     0xbd9dc0: ldur            w2, [x0, #0xb]
    // 0xbd9dc4: DecompressPointer r2
    //     0xbd9dc4: add             x2, x2, HEAP, lsl #32
    // 0xbd9dc8: LoadField: r3 = r0->field_13
    //     0xbd9dc8: ldur            x3, [x0, #0x13]
    // 0xbd9dcc: add             x1, x3, #1
    // 0xbd9dd0: StoreField: r0->field_13 = r1
    //     0xbd9dd0: stur            x1, [x0, #0x13]
    // 0xbd9dd4: LoadField: r0 = r2->field_13
    //     0xbd9dd4: ldur            w0, [x2, #0x13]
    // 0xbd9dd8: r1 = LoadInt32Instr(r0)
    //     0xbd9dd8: sbfx            x1, x0, #1, #0x1f
    // 0xbd9ddc: mov             x0, x1
    // 0xbd9de0: mov             x1, x3
    // 0xbd9de4: cmp             x1, x0
    // 0xbd9de8: b.hs            #0xbd9e78
    // 0xbd9dec: ArrayStore: r2[r3] = rZR  ; TypeUnknown_1
    //     0xbd9dec: add             x0, x2, x3
    //     0xbd9df0: strb            wzr, [x0, #0x17]
    // 0xbd9df4: b               #0xbd9e60
    // 0xbd9df8: ldur            x0, [fp, #-8]
    // 0xbd9dfc: LoadField: r1 = r0->field_b
    //     0xbd9dfc: ldur            w1, [x0, #0xb]
    // 0xbd9e00: DecompressPointer r1
    //     0xbd9e00: add             x1, x1, HEAP, lsl #32
    // 0xbd9e04: LoadField: r2 = r1->field_13
    //     0xbd9e04: ldur            w2, [x1, #0x13]
    // 0xbd9e08: LoadField: r1 = r0->field_13
    //     0xbd9e08: ldur            x1, [x0, #0x13]
    // 0xbd9e0c: r3 = LoadInt32Instr(r2)
    //     0xbd9e0c: sbfx            x3, x2, #1, #0x1f
    // 0xbd9e10: sub             x2, x3, x1
    // 0xbd9e14: cmp             x2, #1
    // 0xbd9e18: b.ge            #0xbd9e28
    // 0xbd9e1c: mov             x1, x0
    // 0xbd9e20: r2 = 1
    //     0xbd9e20: movz            x2, #0x1
    // 0xbd9e24: r0 = _increaseBufferSize()
    //     0xbd9e24: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9e28: ldur            x2, [fp, #-8]
    // 0xbd9e2c: r3 = 1
    //     0xbd9e2c: movz            x3, #0x1
    // 0xbd9e30: LoadField: r4 = r2->field_b
    //     0xbd9e30: ldur            w4, [x2, #0xb]
    // 0xbd9e34: DecompressPointer r4
    //     0xbd9e34: add             x4, x4, HEAP, lsl #32
    // 0xbd9e38: LoadField: r5 = r2->field_13
    //     0xbd9e38: ldur            x5, [x2, #0x13]
    // 0xbd9e3c: add             x6, x5, #1
    // 0xbd9e40: StoreField: r2->field_13 = r6
    //     0xbd9e40: stur            x6, [x2, #0x13]
    // 0xbd9e44: LoadField: r2 = r4->field_13
    //     0xbd9e44: ldur            w2, [x4, #0x13]
    // 0xbd9e48: r0 = LoadInt32Instr(r2)
    //     0xbd9e48: sbfx            x0, x2, #1, #0x1f
    // 0xbd9e4c: mov             x1, x5
    // 0xbd9e50: cmp             x1, x0
    // 0xbd9e54: b.hs            #0xbd9e7c
    // 0xbd9e58: ArrayStore: r4[r5] = r3  ; TypeUnknown_1
    //     0xbd9e58: add             x1, x4, x5
    //     0xbd9e5c: strb            w3, [x1, #0x17]
    // 0xbd9e60: r0 = Null
    //     0xbd9e60: mov             x0, NULL
    // 0xbd9e64: LeaveFrame
    //     0xbd9e64: mov             SP, fp
    //     0xbd9e68: ldp             fp, lr, [SP], #0x10
    // 0xbd9e6c: ret
    //     0xbd9e6c: ret             
    // 0xbd9e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9e74: b               #0xbd9d40
    // 0xbd9e78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9e78: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9e7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9e7c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf045c, size: 0x24
    // 0xbf045c: r1 = 222
    //     0xbf045c: movz            x1, #0xde
    // 0xbf0460: r16 = LoadInt32Instr(r1)
    //     0xbf0460: sbfx            x16, x1, #1, #0x1f
    // 0xbf0464: r17 = 11601
    //     0xbf0464: movz            x17, #0x2d51
    // 0xbf0468: mul             x0, x16, x17
    // 0xbf046c: umulh           x16, x16, x17
    // 0xbf0470: eor             x0, x0, x16
    // 0xbf0474: r0 = 0
    //     0xbf0474: eor             x0, x0, x0, lsr #32
    // 0xbf0478: ubfiz           x0, x0, #1, #0x1e
    // 0xbf047c: ret
    //     0xbf047c: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd77358, size: 0x9c
    // 0xd77358: EnterFrame
    //     0xd77358: stp             fp, lr, [SP, #-0x10]!
    //     0xd7735c: mov             fp, SP
    // 0xd77360: AllocStack(0x10)
    //     0xd77360: sub             SP, SP, #0x10
    // 0xd77364: CheckStackOverflow
    //     0xd77364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd77368: cmp             SP, x16
    //     0xd7736c: b.ls            #0xd773ec
    // 0xd77370: ldr             x0, [fp, #0x10]
    // 0xd77374: cmp             w0, NULL
    // 0xd77378: b.ne            #0xd7738c
    // 0xd7737c: r0 = false
    //     0xd7737c: add             x0, NULL, #0x30  ; false
    // 0xd77380: LeaveFrame
    //     0xd77380: mov             SP, fp
    //     0xd77384: ldp             fp, lr, [SP], #0x10
    // 0xd77388: ret
    //     0xd77388: ret             
    // 0xd7738c: ldr             x1, [fp, #0x18]
    // 0xd77390: cmp             w1, w0
    // 0xd77394: b.ne            #0xd773a0
    // 0xd77398: r0 = true
    //     0xd77398: add             x0, NULL, #0x20  ; true
    // 0xd7739c: b               #0xd773e0
    // 0xd773a0: r1 = 60
    //     0xd773a0: movz            x1, #0x3c
    // 0xd773a4: branchIfSmi(r0, 0xd773b0)
    //     0xd773a4: tbz             w0, #0, #0xd773b0
    // 0xd773a8: r1 = LoadClassIdInstr(r0)
    //     0xd773a8: ldur            x1, [x0, #-1]
    //     0xd773ac: ubfx            x1, x1, #0xc, #0x14
    // 0xd773b0: cmp             x1, #0x667
    // 0xd773b4: b.ne            #0xd773dc
    // 0xd773b8: r16 = WiridTitleTypeAdapter
    //     0xd773b8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b048] Type: WiridTitleTypeAdapter
    //     0xd773bc: ldr             x16, [x16, #0x48]
    // 0xd773c0: r30 = WiridTitleTypeAdapter
    //     0xd773c0: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b048] Type: WiridTitleTypeAdapter
    //     0xd773c4: ldr             lr, [lr, #0x48]
    // 0xd773c8: stp             lr, x16, [SP]
    // 0xd773cc: r0 = ==()
    //     0xd773cc: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd773d0: tbnz            w0, #4, #0xd773dc
    // 0xd773d4: r0 = true
    //     0xd773d4: add             x0, NULL, #0x20  ; true
    // 0xd773d8: b               #0xd773e0
    // 0xd773dc: r0 = false
    //     0xd773dc: add             x0, NULL, #0x30  ; false
    // 0xd773e0: LeaveFrame
    //     0xd773e0: mov             SP, fp
    //     0xd773e4: ldp             fp, lr, [SP], #0x10
    // 0xd773e8: ret
    //     0xd773e8: ret             
    // 0xd773ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd773ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd773f0: b               #0xd77370
  }
}

// class id: 1640, size: 0x14, field offset: 0xc
class WiridAdapter extends TypeAdapter<dynamic> {

  _ read(/* No info */) {
    // ** addr: 0xa68620, size: 0x42c
    // 0xa68620: EnterFrame
    //     0xa68620: stp             fp, lr, [SP, #-0x10]!
    //     0xa68624: mov             fp, SP
    // 0xa68628: AllocStack(0x58)
    //     0xa68628: sub             SP, SP, #0x58
    // 0xa6862c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0xa6862c: stur            x2, [fp, #-0x20]
    // 0xa68630: CheckStackOverflow
    //     0xa68630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa68634: cmp             SP, x16
    //     0xa68638: b.ls            #0xa68a34
    // 0xa6863c: LoadField: r3 = r2->field_23
    //     0xa6863c: ldur            x3, [x2, #0x23]
    // 0xa68640: add             x0, x3, #1
    // 0xa68644: LoadField: r1 = r2->field_1b
    //     0xa68644: ldur            x1, [x2, #0x1b]
    // 0xa68648: cmp             x0, x1
    // 0xa6864c: b.gt            #0xa689d8
    // 0xa68650: LoadField: r4 = r2->field_7
    //     0xa68650: ldur            w4, [x2, #7]
    // 0xa68654: DecompressPointer r4
    //     0xa68654: add             x4, x4, HEAP, lsl #32
    // 0xa68658: stur            x4, [fp, #-0x18]
    // 0xa6865c: StoreField: r2->field_23 = r0
    //     0xa6865c: stur            x0, [x2, #0x23]
    // 0xa68660: LoadField: r0 = r4->field_13
    //     0xa68660: ldur            w0, [x4, #0x13]
    // 0xa68664: r5 = LoadInt32Instr(r0)
    //     0xa68664: sbfx            x5, x0, #1, #0x1f
    // 0xa68668: mov             x0, x5
    // 0xa6866c: mov             x1, x3
    // 0xa68670: stur            x5, [fp, #-0x10]
    // 0xa68674: cmp             x1, x0
    // 0xa68678: b.hs            #0xa68a3c
    // 0xa6867c: LoadField: r0 = r4->field_7
    //     0xa6867c: ldur            x0, [x4, #7]
    // 0xa68680: ldrb            w1, [x0, x3]
    // 0xa68684: stur            x1, [fp, #-8]
    // 0xa68688: r16 = <int, dynamic>
    //     0xa68688: add             x16, PP, #0x20, lsl #12  ; [pp+0x20ac0] TypeArguments: <int, dynamic>
    //     0xa6868c: ldr             x16, [x16, #0xac0]
    // 0xa68690: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xa68694: stp             lr, x16, [SP]
    // 0xa68698: r0 = Map._fromLiteral()
    //     0xa68698: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa6869c: mov             x2, x0
    // 0xa686a0: stur            x2, [fp, #-0x38]
    // 0xa686a4: r6 = 0
    //     0xa686a4: movz            x6, #0
    // 0xa686a8: ldur            x3, [fp, #-0x20]
    // 0xa686ac: ldur            x4, [fp, #-0x18]
    // 0xa686b0: ldur            x5, [fp, #-8]
    // 0xa686b4: stur            x6, [fp, #-0x30]
    // 0xa686b8: CheckStackOverflow
    //     0xa686b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa686bc: cmp             SP, x16
    //     0xa686c0: b.ls            #0xa68a40
    // 0xa686c4: cmp             x6, x5
    // 0xa686c8: b.ge            #0xa68754
    // 0xa686cc: LoadField: r7 = r3->field_23
    //     0xa686cc: ldur            x7, [x3, #0x23]
    // 0xa686d0: add             x0, x7, #1
    // 0xa686d4: LoadField: r1 = r3->field_1b
    //     0xa686d4: ldur            x1, [x3, #0x1b]
    // 0xa686d8: cmp             x0, x1
    // 0xa686dc: b.gt            #0xa68a00
    // 0xa686e0: StoreField: r3->field_23 = r0
    //     0xa686e0: stur            x0, [x3, #0x23]
    // 0xa686e4: ldur            x0, [fp, #-0x10]
    // 0xa686e8: mov             x1, x7
    // 0xa686ec: cmp             x1, x0
    // 0xa686f0: b.hs            #0xa68a48
    // 0xa686f4: LoadField: r0 = r4->field_7
    //     0xa686f4: ldur            x0, [x4, #7]
    // 0xa686f8: ldrb            w8, [x0, x7]
    // 0xa686fc: mov             x1, x3
    // 0xa68700: stur            x8, [fp, #-0x28]
    // 0xa68704: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa68704: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa68708: r0 = read()
    //     0xa68708: bl              #0xa5c374  ; [package:hive/src/binary/binary_reader_impl.dart] BinaryReaderImpl::read
    // 0xa6870c: mov             x1, x0
    // 0xa68710: ldur            x0, [fp, #-0x28]
    // 0xa68714: lsl             x2, x0, #1
    // 0xa68718: r16 = LoadInt32Instr(r2)
    //     0xa68718: sbfx            x16, x2, #1, #0x1f
    // 0xa6871c: r17 = 11601
    //     0xa6871c: movz            x17, #0x2d51
    // 0xa68720: mul             x0, x16, x17
    // 0xa68724: umulh           x16, x16, x17
    // 0xa68728: eor             x0, x0, x16
    // 0xa6872c: r0 = 0
    //     0xa6872c: eor             x0, x0, x0, lsr #32
    // 0xa68730: ubfiz           x0, x0, #1, #0x1e
    // 0xa68734: r5 = LoadInt32Instr(r0)
    //     0xa68734: sbfx            x5, x0, #1, #0x1f
    // 0xa68738: mov             x3, x1
    // 0xa6873c: ldur            x1, [fp, #-0x38]
    // 0xa68740: r0 = _set()
    //     0xa68740: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xa68744: ldur            x0, [fp, #-0x30]
    // 0xa68748: add             x6, x0, #1
    // 0xa6874c: ldur            x2, [fp, #-0x38]
    // 0xa68750: b               #0xa686a8
    // 0xa68754: mov             x0, x2
    // 0xa68758: mov             x1, x0
    // 0xa6875c: r2 = 0
    //     0xa6875c: movz            x2, #0
    // 0xa68760: r0 = _getValueOrData()
    //     0xa68760: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68764: ldur            x3, [fp, #-0x38]
    // 0xa68768: LoadField: r1 = r3->field_f
    //     0xa68768: ldur            w1, [x3, #0xf]
    // 0xa6876c: DecompressPointer r1
    //     0xa6876c: add             x1, x1, HEAP, lsl #32
    // 0xa68770: cmp             w1, w0
    // 0xa68774: b.ne            #0xa68780
    // 0xa68778: r4 = Null
    //     0xa68778: mov             x4, NULL
    // 0xa6877c: b               #0xa68784
    // 0xa68780: mov             x4, x0
    // 0xa68784: mov             x0, x4
    // 0xa68788: stur            x4, [fp, #-0x18]
    // 0xa6878c: r2 = Null
    //     0xa6878c: mov             x2, NULL
    // 0xa68790: r1 = Null
    //     0xa68790: mov             x1, NULL
    // 0xa68794: branchIfSmi(r0, 0xa687bc)
    //     0xa68794: tbz             w0, #0, #0xa687bc
    // 0xa68798: r4 = LoadClassIdInstr(r0)
    //     0xa68798: ldur            x4, [x0, #-1]
    //     0xa6879c: ubfx            x4, x4, #0xc, #0x14
    // 0xa687a0: sub             x4, x4, #0x3c
    // 0xa687a4: cmp             x4, #1
    // 0xa687a8: b.ls            #0xa687bc
    // 0xa687ac: r8 = int
    //     0xa687ac: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa687b0: r3 = Null
    //     0xa687b0: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ac8] Null
    //     0xa687b4: ldr             x3, [x3, #0xac8]
    // 0xa687b8: r0 = int()
    //     0xa687b8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa687bc: ldur            x1, [fp, #-0x38]
    // 0xa687c0: r2 = 2
    //     0xa687c0: movz            x2, #0x2
    // 0xa687c4: r0 = _getValueOrData()
    //     0xa687c4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa687c8: ldur            x3, [fp, #-0x38]
    // 0xa687cc: LoadField: r1 = r3->field_f
    //     0xa687cc: ldur            w1, [x3, #0xf]
    // 0xa687d0: DecompressPointer r1
    //     0xa687d0: add             x1, x1, HEAP, lsl #32
    // 0xa687d4: cmp             w1, w0
    // 0xa687d8: b.ne            #0xa687e4
    // 0xa687dc: r4 = Null
    //     0xa687dc: mov             x4, NULL
    // 0xa687e0: b               #0xa687e8
    // 0xa687e4: mov             x4, x0
    // 0xa687e8: mov             x0, x4
    // 0xa687ec: stur            x4, [fp, #-0x20]
    // 0xa687f0: r2 = Null
    //     0xa687f0: mov             x2, NULL
    // 0xa687f4: r1 = Null
    //     0xa687f4: mov             x1, NULL
    // 0xa687f8: r4 = 60
    //     0xa687f8: movz            x4, #0x3c
    // 0xa687fc: branchIfSmi(r0, 0xa68808)
    //     0xa687fc: tbz             w0, #0, #0xa68808
    // 0xa68800: r4 = LoadClassIdInstr(r0)
    //     0xa68800: ldur            x4, [x0, #-1]
    //     0xa68804: ubfx            x4, x4, #0xc, #0x14
    // 0xa68808: sub             x4, x4, #0x5e
    // 0xa6880c: cmp             x4, #1
    // 0xa68810: b.ls            #0xa68824
    // 0xa68814: r8 = String
    //     0xa68814: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xa68818: r3 = Null
    //     0xa68818: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ad8] Null
    //     0xa6881c: ldr             x3, [x3, #0xad8]
    // 0xa68820: r0 = String()
    //     0xa68820: bl              #0xed43b0  ; IsType_String_Stub
    // 0xa68824: ldur            x1, [fp, #-0x38]
    // 0xa68828: r2 = 4
    //     0xa68828: movz            x2, #0x4
    // 0xa6882c: r0 = _getValueOrData()
    //     0xa6882c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68830: ldur            x3, [fp, #-0x38]
    // 0xa68834: LoadField: r1 = r3->field_f
    //     0xa68834: ldur            w1, [x3, #0xf]
    // 0xa68838: DecompressPointer r1
    //     0xa68838: add             x1, x1, HEAP, lsl #32
    // 0xa6883c: cmp             w1, w0
    // 0xa68840: b.ne            #0xa6884c
    // 0xa68844: r4 = Null
    //     0xa68844: mov             x4, NULL
    // 0xa68848: b               #0xa68850
    // 0xa6884c: mov             x4, x0
    // 0xa68850: mov             x0, x4
    // 0xa68854: stur            x4, [fp, #-0x40]
    // 0xa68858: r2 = Null
    //     0xa68858: mov             x2, NULL
    // 0xa6885c: r1 = Null
    //     0xa6885c: mov             x1, NULL
    // 0xa68860: r4 = 60
    //     0xa68860: movz            x4, #0x3c
    // 0xa68864: branchIfSmi(r0, 0xa68870)
    //     0xa68864: tbz             w0, #0, #0xa68870
    // 0xa68868: r4 = LoadClassIdInstr(r0)
    //     0xa68868: ldur            x4, [x0, #-1]
    //     0xa6886c: ubfx            x4, x4, #0xc, #0x14
    // 0xa68870: r17 = 6832
    //     0xa68870: movz            x17, #0x1ab0
    // 0xa68874: cmp             x4, x17
    // 0xa68878: b.eq            #0xa68890
    // 0xa6887c: r8 = WiridTitleType
    //     0xa6887c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b050] Type: WiridTitleType
    //     0xa68880: ldr             x8, [x8, #0x50]
    // 0xa68884: r3 = Null
    //     0xa68884: add             x3, PP, #0x20, lsl #12  ; [pp+0x20ae8] Null
    //     0xa68888: ldr             x3, [x3, #0xae8]
    // 0xa6888c: r0 = WiridTitleType()
    //     0xa6888c: bl              #0x842c78  ; IsType_WiridTitleType_Stub
    // 0xa68890: ldur            x1, [fp, #-0x38]
    // 0xa68894: r2 = 6
    //     0xa68894: movz            x2, #0x6
    // 0xa68898: r0 = _getValueOrData()
    //     0xa68898: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa6889c: ldur            x3, [fp, #-0x38]
    // 0xa688a0: LoadField: r1 = r3->field_f
    //     0xa688a0: ldur            w1, [x3, #0xf]
    // 0xa688a4: DecompressPointer r1
    //     0xa688a4: add             x1, x1, HEAP, lsl #32
    // 0xa688a8: cmp             w1, w0
    // 0xa688ac: b.ne            #0xa688b8
    // 0xa688b0: r4 = Null
    //     0xa688b0: mov             x4, NULL
    // 0xa688b4: b               #0xa688bc
    // 0xa688b8: mov             x4, x0
    // 0xa688bc: mov             x0, x4
    // 0xa688c0: stur            x4, [fp, #-0x48]
    // 0xa688c4: r2 = Null
    //     0xa688c4: mov             x2, NULL
    // 0xa688c8: r1 = Null
    //     0xa688c8: mov             x1, NULL
    // 0xa688cc: branchIfSmi(r0, 0xa688f4)
    //     0xa688cc: tbz             w0, #0, #0xa688f4
    // 0xa688d0: r4 = LoadClassIdInstr(r0)
    //     0xa688d0: ldur            x4, [x0, #-1]
    //     0xa688d4: ubfx            x4, x4, #0xc, #0x14
    // 0xa688d8: sub             x4, x4, #0x3c
    // 0xa688dc: cmp             x4, #1
    // 0xa688e0: b.ls            #0xa688f4
    // 0xa688e4: r8 = int
    //     0xa688e4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa688e8: r3 = Null
    //     0xa688e8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20af8] Null
    //     0xa688ec: ldr             x3, [x3, #0xaf8]
    // 0xa688f0: r0 = int()
    //     0xa688f0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa688f4: ldur            x1, [fp, #-0x38]
    // 0xa688f8: r2 = 8
    //     0xa688f8: movz            x2, #0x8
    // 0xa688fc: r0 = _getValueOrData()
    //     0xa688fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa68900: mov             x1, x0
    // 0xa68904: ldur            x0, [fp, #-0x38]
    // 0xa68908: LoadField: r2 = r0->field_f
    //     0xa68908: ldur            w2, [x0, #0xf]
    // 0xa6890c: DecompressPointer r2
    //     0xa6890c: add             x2, x2, HEAP, lsl #32
    // 0xa68910: cmp             w2, w1
    // 0xa68914: b.ne            #0xa68920
    // 0xa68918: r7 = Null
    //     0xa68918: mov             x7, NULL
    // 0xa6891c: b               #0xa68924
    // 0xa68920: mov             x7, x1
    // 0xa68924: ldur            x6, [fp, #-0x18]
    // 0xa68928: ldur            x5, [fp, #-0x20]
    // 0xa6892c: ldur            x4, [fp, #-0x40]
    // 0xa68930: ldur            x3, [fp, #-0x48]
    // 0xa68934: mov             x0, x7
    // 0xa68938: stur            x7, [fp, #-0x38]
    // 0xa6893c: r2 = Null
    //     0xa6893c: mov             x2, NULL
    // 0xa68940: r1 = Null
    //     0xa68940: mov             x1, NULL
    // 0xa68944: branchIfSmi(r0, 0xa6896c)
    //     0xa68944: tbz             w0, #0, #0xa6896c
    // 0xa68948: r4 = LoadClassIdInstr(r0)
    //     0xa68948: ldur            x4, [x0, #-1]
    //     0xa6894c: ubfx            x4, x4, #0xc, #0x14
    // 0xa68950: sub             x4, x4, #0x3c
    // 0xa68954: cmp             x4, #1
    // 0xa68958: b.ls            #0xa6896c
    // 0xa6895c: r8 = int
    //     0xa6895c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xa68960: r3 = Null
    //     0xa68960: add             x3, PP, #0x20, lsl #12  ; [pp+0x20b08] Null
    //     0xa68964: ldr             x3, [x3, #0xb08]
    // 0xa68968: r0 = int()
    //     0xa68968: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xa6896c: ldur            x0, [fp, #-0x18]
    // 0xa68970: r1 = LoadInt32Instr(r0)
    //     0xa68970: sbfx            x1, x0, #1, #0x1f
    //     0xa68974: tbz             w0, #0, #0xa6897c
    //     0xa68978: ldur            x1, [x0, #7]
    // 0xa6897c: stur            x1, [fp, #-8]
    // 0xa68980: r0 = Wirid()
    //     0xa68980: bl              #0x8f1ed8  ; AllocateWiridStub -> Wirid (size=0x28)
    // 0xa68984: mov             x1, x0
    // 0xa68988: ldur            x0, [fp, #-8]
    // 0xa6898c: StoreField: r1->field_7 = r0
    //     0xa6898c: stur            x0, [x1, #7]
    // 0xa68990: ldur            x0, [fp, #-0x20]
    // 0xa68994: StoreField: r1->field_f = r0
    //     0xa68994: stur            w0, [x1, #0xf]
    // 0xa68998: ldur            x0, [fp, #-0x40]
    // 0xa6899c: StoreField: r1->field_13 = r0
    //     0xa6899c: stur            w0, [x1, #0x13]
    // 0xa689a0: ldur            x0, [fp, #-0x48]
    // 0xa689a4: r2 = LoadInt32Instr(r0)
    //     0xa689a4: sbfx            x2, x0, #1, #0x1f
    //     0xa689a8: tbz             w0, #0, #0xa689b0
    //     0xa689ac: ldur            x2, [x0, #7]
    // 0xa689b0: ArrayStore: r1[0] = r2  ; List_8
    //     0xa689b0: stur            x2, [x1, #0x17]
    // 0xa689b4: ldur            x0, [fp, #-0x38]
    // 0xa689b8: r2 = LoadInt32Instr(r0)
    //     0xa689b8: sbfx            x2, x0, #1, #0x1f
    //     0xa689bc: tbz             w0, #0, #0xa689c4
    //     0xa689c0: ldur            x2, [x0, #7]
    // 0xa689c4: StoreField: r1->field_1f = r2
    //     0xa689c4: stur            x2, [x1, #0x1f]
    // 0xa689c8: mov             x0, x1
    // 0xa689cc: LeaveFrame
    //     0xa689cc: mov             SP, fp
    //     0xa689d0: ldp             fp, lr, [SP], #0x10
    // 0xa689d4: ret
    //     0xa689d4: ret             
    // 0xa689d8: r0 = RangeError()
    //     0xa689d8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa689dc: mov             x1, x0
    // 0xa689e0: r0 = "Not enough bytes available."
    //     0xa689e0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa689e4: ldr             x0, [x0, #0x8a8]
    // 0xa689e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa689e8: stur            w0, [x1, #0x17]
    // 0xa689ec: r2 = false
    //     0xa689ec: add             x2, NULL, #0x30  ; false
    // 0xa689f0: StoreField: r1->field_b = r2
    //     0xa689f0: stur            w2, [x1, #0xb]
    // 0xa689f4: mov             x0, x1
    // 0xa689f8: r0 = Throw()
    //     0xa689f8: bl              #0xec04b8  ; ThrowStub
    // 0xa689fc: brk             #0
    // 0xa68a00: r0 = "Not enough bytes available."
    //     0xa68a00: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa68a04: ldr             x0, [x0, #0x8a8]
    // 0xa68a08: r2 = false
    //     0xa68a08: add             x2, NULL, #0x30  ; false
    // 0xa68a0c: r0 = RangeError()
    //     0xa68a0c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa68a10: mov             x1, x0
    // 0xa68a14: r0 = "Not enough bytes available."
    //     0xa68a14: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] "Not enough bytes available."
    //     0xa68a18: ldr             x0, [x0, #0x8a8]
    // 0xa68a1c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa68a1c: stur            w0, [x1, #0x17]
    // 0xa68a20: r0 = false
    //     0xa68a20: add             x0, NULL, #0x30  ; false
    // 0xa68a24: StoreField: r1->field_b = r0
    //     0xa68a24: stur            w0, [x1, #0xb]
    // 0xa68a28: mov             x0, x1
    // 0xa68a2c: r0 = Throw()
    //     0xa68a2c: bl              #0xec04b8  ; ThrowStub
    // 0xa68a30: brk             #0
    // 0xa68a34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa68a34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa68a38: b               #0xa6863c
    // 0xa68a3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa68a3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa68a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa68a40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa68a44: b               #0xa686c4
    // 0xa68a48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa68a48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ write(/* No info */) {
    // ** addr: 0xbd994c, size: 0x3d0
    // 0xbd994c: EnterFrame
    //     0xbd994c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9950: mov             fp, SP
    // 0xbd9954: AllocStack(0x28)
    //     0xbd9954: sub             SP, SP, #0x28
    // 0xbd9958: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xbd9958: mov             x4, x2
    //     0xbd995c: stur            x2, [fp, #-8]
    //     0xbd9960: stur            x3, [fp, #-0x10]
    // 0xbd9964: CheckStackOverflow
    //     0xbd9964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9968: cmp             SP, x16
    //     0xbd996c: b.ls            #0xbd9cfc
    // 0xbd9970: mov             x0, x3
    // 0xbd9974: r2 = Null
    //     0xbd9974: mov             x2, NULL
    // 0xbd9978: r1 = Null
    //     0xbd9978: mov             x1, NULL
    // 0xbd997c: r4 = 60
    //     0xbd997c: movz            x4, #0x3c
    // 0xbd9980: branchIfSmi(r0, 0xbd998c)
    //     0xbd9980: tbz             w0, #0, #0xbd998c
    // 0xbd9984: r4 = LoadClassIdInstr(r0)
    //     0xbd9984: ldur            x4, [x0, #-1]
    //     0xbd9988: ubfx            x4, x4, #0xc, #0x14
    // 0xbd998c: r17 = 5563
    //     0xbd998c: movz            x17, #0x15bb
    // 0xbd9990: cmp             x4, x17
    // 0xbd9994: b.eq            #0xbd99ac
    // 0xbd9998: r8 = Wirid
    //     0xbd9998: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b070] Type: Wirid
    //     0xbd999c: ldr             x8, [x8, #0x70]
    // 0xbd99a0: r3 = Null
    //     0xbd99a0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b078] Null
    //     0xbd99a4: ldr             x3, [x3, #0x78]
    // 0xbd99a8: r0 = Wirid()
    //     0xbd99a8: bl              #0x842c9c  ; IsType_Wirid_Stub
    // 0xbd99ac: ldur            x0, [fp, #-8]
    // 0xbd99b0: LoadField: r1 = r0->field_b
    //     0xbd99b0: ldur            w1, [x0, #0xb]
    // 0xbd99b4: DecompressPointer r1
    //     0xbd99b4: add             x1, x1, HEAP, lsl #32
    // 0xbd99b8: LoadField: r2 = r1->field_13
    //     0xbd99b8: ldur            w2, [x1, #0x13]
    // 0xbd99bc: LoadField: r1 = r0->field_13
    //     0xbd99bc: ldur            x1, [x0, #0x13]
    // 0xbd99c0: r3 = LoadInt32Instr(r2)
    //     0xbd99c0: sbfx            x3, x2, #1, #0x1f
    // 0xbd99c4: sub             x2, x3, x1
    // 0xbd99c8: cmp             x2, #1
    // 0xbd99cc: b.ge            #0xbd99dc
    // 0xbd99d0: mov             x1, x0
    // 0xbd99d4: r2 = 1
    //     0xbd99d4: movz            x2, #0x1
    // 0xbd99d8: r0 = _increaseBufferSize()
    //     0xbd99d8: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd99dc: ldur            x3, [fp, #-8]
    // 0xbd99e0: r2 = 5
    //     0xbd99e0: movz            x2, #0x5
    // 0xbd99e4: LoadField: r4 = r3->field_b
    //     0xbd99e4: ldur            w4, [x3, #0xb]
    // 0xbd99e8: DecompressPointer r4
    //     0xbd99e8: add             x4, x4, HEAP, lsl #32
    // 0xbd99ec: LoadField: r5 = r3->field_13
    //     0xbd99ec: ldur            x5, [x3, #0x13]
    // 0xbd99f0: add             x6, x5, #1
    // 0xbd99f4: StoreField: r3->field_13 = r6
    //     0xbd99f4: stur            x6, [x3, #0x13]
    // 0xbd99f8: LoadField: r0 = r4->field_13
    //     0xbd99f8: ldur            w0, [x4, #0x13]
    // 0xbd99fc: r7 = LoadInt32Instr(r0)
    //     0xbd99fc: sbfx            x7, x0, #1, #0x1f
    // 0xbd9a00: mov             x0, x7
    // 0xbd9a04: mov             x1, x5
    // 0xbd9a08: cmp             x1, x0
    // 0xbd9a0c: b.hs            #0xbd9d04
    // 0xbd9a10: ArrayStore: r4[r5] = r2  ; TypeUnknown_1
    //     0xbd9a10: add             x0, x4, x5
    //     0xbd9a14: strb            w2, [x0, #0x17]
    // 0xbd9a18: sub             x0, x7, x6
    // 0xbd9a1c: cmp             x0, #1
    // 0xbd9a20: b.ge            #0xbd9a30
    // 0xbd9a24: mov             x1, x3
    // 0xbd9a28: r2 = 1
    //     0xbd9a28: movz            x2, #0x1
    // 0xbd9a2c: r0 = _increaseBufferSize()
    //     0xbd9a2c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9a30: ldur            x2, [fp, #-8]
    // 0xbd9a34: ldur            x3, [fp, #-0x10]
    // 0xbd9a38: LoadField: r4 = r2->field_b
    //     0xbd9a38: ldur            w4, [x2, #0xb]
    // 0xbd9a3c: DecompressPointer r4
    //     0xbd9a3c: add             x4, x4, HEAP, lsl #32
    // 0xbd9a40: LoadField: r5 = r2->field_13
    //     0xbd9a40: ldur            x5, [x2, #0x13]
    // 0xbd9a44: add             x0, x5, #1
    // 0xbd9a48: StoreField: r2->field_13 = r0
    //     0xbd9a48: stur            x0, [x2, #0x13]
    // 0xbd9a4c: LoadField: r0 = r4->field_13
    //     0xbd9a4c: ldur            w0, [x4, #0x13]
    // 0xbd9a50: r1 = LoadInt32Instr(r0)
    //     0xbd9a50: sbfx            x1, x0, #1, #0x1f
    // 0xbd9a54: mov             x0, x1
    // 0xbd9a58: mov             x1, x5
    // 0xbd9a5c: cmp             x1, x0
    // 0xbd9a60: b.hs            #0xbd9d08
    // 0xbd9a64: ArrayStore: r4[r5] = rZR  ; TypeUnknown_1
    //     0xbd9a64: add             x0, x4, x5
    //     0xbd9a68: strb            wzr, [x0, #0x17]
    // 0xbd9a6c: LoadField: r4 = r3->field_7
    //     0xbd9a6c: ldur            x4, [x3, #7]
    // 0xbd9a70: r0 = BoxInt64Instr(r4)
    //     0xbd9a70: sbfiz           x0, x4, #1, #0x1f
    //     0xbd9a74: cmp             x4, x0, asr #1
    //     0xbd9a78: b.eq            #0xbd9a84
    //     0xbd9a7c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9a80: stur            x4, [x0, #7]
    // 0xbd9a84: r16 = <int>
    //     0xbd9a84: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd9a88: stp             x2, x16, [SP, #8]
    // 0xbd9a8c: str             x0, [SP]
    // 0xbd9a90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9a90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9a94: r0 = write()
    //     0xbd9a94: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9a98: ldur            x0, [fp, #-8]
    // 0xbd9a9c: LoadField: r1 = r0->field_b
    //     0xbd9a9c: ldur            w1, [x0, #0xb]
    // 0xbd9aa0: DecompressPointer r1
    //     0xbd9aa0: add             x1, x1, HEAP, lsl #32
    // 0xbd9aa4: LoadField: r2 = r1->field_13
    //     0xbd9aa4: ldur            w2, [x1, #0x13]
    // 0xbd9aa8: LoadField: r1 = r0->field_13
    //     0xbd9aa8: ldur            x1, [x0, #0x13]
    // 0xbd9aac: r3 = LoadInt32Instr(r2)
    //     0xbd9aac: sbfx            x3, x2, #1, #0x1f
    // 0xbd9ab0: sub             x2, x3, x1
    // 0xbd9ab4: cmp             x2, #1
    // 0xbd9ab8: b.ge            #0xbd9ac8
    // 0xbd9abc: mov             x1, x0
    // 0xbd9ac0: r2 = 1
    //     0xbd9ac0: movz            x2, #0x1
    // 0xbd9ac4: r0 = _increaseBufferSize()
    //     0xbd9ac4: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9ac8: ldur            x2, [fp, #-8]
    // 0xbd9acc: ldur            x3, [fp, #-0x10]
    // 0xbd9ad0: r4 = 1
    //     0xbd9ad0: movz            x4, #0x1
    // 0xbd9ad4: LoadField: r5 = r2->field_b
    //     0xbd9ad4: ldur            w5, [x2, #0xb]
    // 0xbd9ad8: DecompressPointer r5
    //     0xbd9ad8: add             x5, x5, HEAP, lsl #32
    // 0xbd9adc: LoadField: r6 = r2->field_13
    //     0xbd9adc: ldur            x6, [x2, #0x13]
    // 0xbd9ae0: add             x0, x6, #1
    // 0xbd9ae4: StoreField: r2->field_13 = r0
    //     0xbd9ae4: stur            x0, [x2, #0x13]
    // 0xbd9ae8: LoadField: r0 = r5->field_13
    //     0xbd9ae8: ldur            w0, [x5, #0x13]
    // 0xbd9aec: r1 = LoadInt32Instr(r0)
    //     0xbd9aec: sbfx            x1, x0, #1, #0x1f
    // 0xbd9af0: mov             x0, x1
    // 0xbd9af4: mov             x1, x6
    // 0xbd9af8: cmp             x1, x0
    // 0xbd9afc: b.hs            #0xbd9d0c
    // 0xbd9b00: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9b00: add             x0, x5, x6
    //     0xbd9b04: strb            w4, [x0, #0x17]
    // 0xbd9b08: LoadField: r0 = r3->field_f
    //     0xbd9b08: ldur            w0, [x3, #0xf]
    // 0xbd9b0c: DecompressPointer r0
    //     0xbd9b0c: add             x0, x0, HEAP, lsl #32
    // 0xbd9b10: r16 = <String>
    //     0xbd9b10: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbd9b14: stp             x2, x16, [SP, #8]
    // 0xbd9b18: str             x0, [SP]
    // 0xbd9b1c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9b1c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9b20: r0 = write()
    //     0xbd9b20: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9b24: ldur            x0, [fp, #-8]
    // 0xbd9b28: LoadField: r1 = r0->field_b
    //     0xbd9b28: ldur            w1, [x0, #0xb]
    // 0xbd9b2c: DecompressPointer r1
    //     0xbd9b2c: add             x1, x1, HEAP, lsl #32
    // 0xbd9b30: LoadField: r2 = r1->field_13
    //     0xbd9b30: ldur            w2, [x1, #0x13]
    // 0xbd9b34: LoadField: r1 = r0->field_13
    //     0xbd9b34: ldur            x1, [x0, #0x13]
    // 0xbd9b38: r3 = LoadInt32Instr(r2)
    //     0xbd9b38: sbfx            x3, x2, #1, #0x1f
    // 0xbd9b3c: sub             x2, x3, x1
    // 0xbd9b40: cmp             x2, #1
    // 0xbd9b44: b.ge            #0xbd9b54
    // 0xbd9b48: mov             x1, x0
    // 0xbd9b4c: r2 = 1
    //     0xbd9b4c: movz            x2, #0x1
    // 0xbd9b50: r0 = _increaseBufferSize()
    //     0xbd9b50: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9b54: ldur            x2, [fp, #-8]
    // 0xbd9b58: ldur            x3, [fp, #-0x10]
    // 0xbd9b5c: r4 = 2
    //     0xbd9b5c: movz            x4, #0x2
    // 0xbd9b60: LoadField: r5 = r2->field_b
    //     0xbd9b60: ldur            w5, [x2, #0xb]
    // 0xbd9b64: DecompressPointer r5
    //     0xbd9b64: add             x5, x5, HEAP, lsl #32
    // 0xbd9b68: LoadField: r6 = r2->field_13
    //     0xbd9b68: ldur            x6, [x2, #0x13]
    // 0xbd9b6c: add             x0, x6, #1
    // 0xbd9b70: StoreField: r2->field_13 = r0
    //     0xbd9b70: stur            x0, [x2, #0x13]
    // 0xbd9b74: LoadField: r0 = r5->field_13
    //     0xbd9b74: ldur            w0, [x5, #0x13]
    // 0xbd9b78: r1 = LoadInt32Instr(r0)
    //     0xbd9b78: sbfx            x1, x0, #1, #0x1f
    // 0xbd9b7c: mov             x0, x1
    // 0xbd9b80: mov             x1, x6
    // 0xbd9b84: cmp             x1, x0
    // 0xbd9b88: b.hs            #0xbd9d10
    // 0xbd9b8c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9b8c: add             x0, x5, x6
    //     0xbd9b90: strb            w4, [x0, #0x17]
    // 0xbd9b94: LoadField: r0 = r3->field_13
    //     0xbd9b94: ldur            w0, [x3, #0x13]
    // 0xbd9b98: DecompressPointer r0
    //     0xbd9b98: add             x0, x0, HEAP, lsl #32
    // 0xbd9b9c: r16 = <WiridTitleType>
    //     0xbd9b9c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d0] TypeArguments: <WiridTitleType>
    //     0xbd9ba0: ldr             x16, [x16, #0x1d0]
    // 0xbd9ba4: stp             x2, x16, [SP, #8]
    // 0xbd9ba8: str             x0, [SP]
    // 0xbd9bac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9bac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9bb0: r0 = write()
    //     0xbd9bb0: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9bb4: ldur            x0, [fp, #-8]
    // 0xbd9bb8: LoadField: r1 = r0->field_b
    //     0xbd9bb8: ldur            w1, [x0, #0xb]
    // 0xbd9bbc: DecompressPointer r1
    //     0xbd9bbc: add             x1, x1, HEAP, lsl #32
    // 0xbd9bc0: LoadField: r2 = r1->field_13
    //     0xbd9bc0: ldur            w2, [x1, #0x13]
    // 0xbd9bc4: LoadField: r1 = r0->field_13
    //     0xbd9bc4: ldur            x1, [x0, #0x13]
    // 0xbd9bc8: r3 = LoadInt32Instr(r2)
    //     0xbd9bc8: sbfx            x3, x2, #1, #0x1f
    // 0xbd9bcc: sub             x2, x3, x1
    // 0xbd9bd0: cmp             x2, #1
    // 0xbd9bd4: b.ge            #0xbd9be4
    // 0xbd9bd8: mov             x1, x0
    // 0xbd9bdc: r2 = 1
    //     0xbd9bdc: movz            x2, #0x1
    // 0xbd9be0: r0 = _increaseBufferSize()
    //     0xbd9be0: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9be4: ldur            x2, [fp, #-8]
    // 0xbd9be8: ldur            x3, [fp, #-0x10]
    // 0xbd9bec: r4 = 3
    //     0xbd9bec: movz            x4, #0x3
    // 0xbd9bf0: LoadField: r5 = r2->field_b
    //     0xbd9bf0: ldur            w5, [x2, #0xb]
    // 0xbd9bf4: DecompressPointer r5
    //     0xbd9bf4: add             x5, x5, HEAP, lsl #32
    // 0xbd9bf8: LoadField: r6 = r2->field_13
    //     0xbd9bf8: ldur            x6, [x2, #0x13]
    // 0xbd9bfc: add             x0, x6, #1
    // 0xbd9c00: StoreField: r2->field_13 = r0
    //     0xbd9c00: stur            x0, [x2, #0x13]
    // 0xbd9c04: LoadField: r0 = r5->field_13
    //     0xbd9c04: ldur            w0, [x5, #0x13]
    // 0xbd9c08: r1 = LoadInt32Instr(r0)
    //     0xbd9c08: sbfx            x1, x0, #1, #0x1f
    // 0xbd9c0c: mov             x0, x1
    // 0xbd9c10: mov             x1, x6
    // 0xbd9c14: cmp             x1, x0
    // 0xbd9c18: b.hs            #0xbd9d14
    // 0xbd9c1c: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9c1c: add             x0, x5, x6
    //     0xbd9c20: strb            w4, [x0, #0x17]
    // 0xbd9c24: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbd9c24: ldur            x4, [x3, #0x17]
    // 0xbd9c28: r0 = BoxInt64Instr(r4)
    //     0xbd9c28: sbfiz           x0, x4, #1, #0x1f
    //     0xbd9c2c: cmp             x4, x0, asr #1
    //     0xbd9c30: b.eq            #0xbd9c3c
    //     0xbd9c34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9c38: stur            x4, [x0, #7]
    // 0xbd9c3c: r16 = <int>
    //     0xbd9c3c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd9c40: stp             x2, x16, [SP, #8]
    // 0xbd9c44: str             x0, [SP]
    // 0xbd9c48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9c48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9c4c: r0 = write()
    //     0xbd9c4c: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9c50: ldur            x0, [fp, #-8]
    // 0xbd9c54: LoadField: r1 = r0->field_b
    //     0xbd9c54: ldur            w1, [x0, #0xb]
    // 0xbd9c58: DecompressPointer r1
    //     0xbd9c58: add             x1, x1, HEAP, lsl #32
    // 0xbd9c5c: LoadField: r2 = r1->field_13
    //     0xbd9c5c: ldur            w2, [x1, #0x13]
    // 0xbd9c60: LoadField: r1 = r0->field_13
    //     0xbd9c60: ldur            x1, [x0, #0x13]
    // 0xbd9c64: r3 = LoadInt32Instr(r2)
    //     0xbd9c64: sbfx            x3, x2, #1, #0x1f
    // 0xbd9c68: sub             x2, x3, x1
    // 0xbd9c6c: cmp             x2, #1
    // 0xbd9c70: b.ge            #0xbd9c80
    // 0xbd9c74: mov             x1, x0
    // 0xbd9c78: r2 = 1
    //     0xbd9c78: movz            x2, #0x1
    // 0xbd9c7c: r0 = _increaseBufferSize()
    //     0xbd9c7c: bl              #0x7c6308  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::_increaseBufferSize
    // 0xbd9c80: ldur            x2, [fp, #-8]
    // 0xbd9c84: ldur            x3, [fp, #-0x10]
    // 0xbd9c88: r4 = 4
    //     0xbd9c88: movz            x4, #0x4
    // 0xbd9c8c: LoadField: r5 = r2->field_b
    //     0xbd9c8c: ldur            w5, [x2, #0xb]
    // 0xbd9c90: DecompressPointer r5
    //     0xbd9c90: add             x5, x5, HEAP, lsl #32
    // 0xbd9c94: LoadField: r6 = r2->field_13
    //     0xbd9c94: ldur            x6, [x2, #0x13]
    // 0xbd9c98: add             x0, x6, #1
    // 0xbd9c9c: StoreField: r2->field_13 = r0
    //     0xbd9c9c: stur            x0, [x2, #0x13]
    // 0xbd9ca0: LoadField: r0 = r5->field_13
    //     0xbd9ca0: ldur            w0, [x5, #0x13]
    // 0xbd9ca4: r1 = LoadInt32Instr(r0)
    //     0xbd9ca4: sbfx            x1, x0, #1, #0x1f
    // 0xbd9ca8: mov             x0, x1
    // 0xbd9cac: mov             x1, x6
    // 0xbd9cb0: cmp             x1, x0
    // 0xbd9cb4: b.hs            #0xbd9d18
    // 0xbd9cb8: ArrayStore: r5[r6] = r4  ; TypeUnknown_1
    //     0xbd9cb8: add             x0, x5, x6
    //     0xbd9cbc: strb            w4, [x0, #0x17]
    // 0xbd9cc0: LoadField: r4 = r3->field_1f
    //     0xbd9cc0: ldur            x4, [x3, #0x1f]
    // 0xbd9cc4: r0 = BoxInt64Instr(r4)
    //     0xbd9cc4: sbfiz           x0, x4, #1, #0x1f
    //     0xbd9cc8: cmp             x4, x0, asr #1
    //     0xbd9ccc: b.eq            #0xbd9cd8
    //     0xbd9cd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd9cd4: stur            x4, [x0, #7]
    // 0xbd9cd8: r16 = <int>
    //     0xbd9cd8: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xbd9cdc: stp             x2, x16, [SP, #8]
    // 0xbd9ce0: str             x0, [SP]
    // 0xbd9ce4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbd9ce4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbd9ce8: r0 = write()
    //     0xbd9ce8: bl              #0x7c2f90  ; [package:hive/src/binary/binary_writer_impl.dart] BinaryWriterImpl::write
    // 0xbd9cec: r0 = Null
    //     0xbd9cec: mov             x0, NULL
    // 0xbd9cf0: LeaveFrame
    //     0xbd9cf0: mov             SP, fp
    //     0xbd9cf4: ldp             fp, lr, [SP], #0x10
    // 0xbd9cf8: ret
    //     0xbd9cf8: ret             
    // 0xbd9cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9cfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9d00: b               #0xbd9970
    // 0xbd9d04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9d08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9d0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9d10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d10: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9d14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbd9d18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd9d18: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf0438, size: 0x24
    // 0xbf0438: r1 = 220
    //     0xbf0438: movz            x1, #0xdc
    // 0xbf043c: r16 = LoadInt32Instr(r1)
    //     0xbf043c: sbfx            x16, x1, #1, #0x1f
    // 0xbf0440: r17 = 11601
    //     0xbf0440: movz            x17, #0x2d51
    // 0xbf0444: mul             x0, x16, x17
    // 0xbf0448: umulh           x16, x16, x17
    // 0xbf044c: eor             x0, x0, x16
    // 0xbf0450: r0 = 0
    //     0xbf0450: eor             x0, x0, x0, lsr #32
    // 0xbf0454: ubfiz           x0, x0, #1, #0x1e
    // 0xbf0458: ret
    //     0xbf0458: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd772bc, size: 0x9c
    // 0xd772bc: EnterFrame
    //     0xd772bc: stp             fp, lr, [SP, #-0x10]!
    //     0xd772c0: mov             fp, SP
    // 0xd772c4: AllocStack(0x10)
    //     0xd772c4: sub             SP, SP, #0x10
    // 0xd772c8: CheckStackOverflow
    //     0xd772c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd772cc: cmp             SP, x16
    //     0xd772d0: b.ls            #0xd77350
    // 0xd772d4: ldr             x0, [fp, #0x10]
    // 0xd772d8: cmp             w0, NULL
    // 0xd772dc: b.ne            #0xd772f0
    // 0xd772e0: r0 = false
    //     0xd772e0: add             x0, NULL, #0x30  ; false
    // 0xd772e4: LeaveFrame
    //     0xd772e4: mov             SP, fp
    //     0xd772e8: ldp             fp, lr, [SP], #0x10
    // 0xd772ec: ret
    //     0xd772ec: ret             
    // 0xd772f0: ldr             x1, [fp, #0x18]
    // 0xd772f4: cmp             w1, w0
    // 0xd772f8: b.ne            #0xd77304
    // 0xd772fc: r0 = true
    //     0xd772fc: add             x0, NULL, #0x20  ; true
    // 0xd77300: b               #0xd77344
    // 0xd77304: r1 = 60
    //     0xd77304: movz            x1, #0x3c
    // 0xd77308: branchIfSmi(r0, 0xd77314)
    //     0xd77308: tbz             w0, #0, #0xd77314
    // 0xd7730c: r1 = LoadClassIdInstr(r0)
    //     0xd7730c: ldur            x1, [x0, #-1]
    //     0xd77310: ubfx            x1, x1, #0xc, #0x14
    // 0xd77314: cmp             x1, #0x668
    // 0xd77318: b.ne            #0xd77340
    // 0xd7731c: r16 = WiridAdapter
    //     0xd7731c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b068] Type: WiridAdapter
    //     0xd77320: ldr             x16, [x16, #0x68]
    // 0xd77324: r30 = WiridAdapter
    //     0xd77324: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1b068] Type: WiridAdapter
    //     0xd77328: ldr             lr, [lr, #0x68]
    // 0xd7732c: stp             lr, x16, [SP]
    // 0xd77330: r0 = ==()
    //     0xd77330: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd77334: tbnz            w0, #4, #0xd77340
    // 0xd77338: r0 = true
    //     0xd77338: add             x0, NULL, #0x20  ; true
    // 0xd7733c: b               #0xd77344
    // 0xd77340: r0 = false
    //     0xd77340: add             x0, NULL, #0x30  ; false
    // 0xd77344: LeaveFrame
    //     0xd77344: mov             SP, fp
    //     0xd77348: ldp             fp, lr, [SP], #0x10
    // 0xd7734c: ret
    //     0xd7734c: ret             
    // 0xd77350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd77350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd77354: b               #0xd772d4
  }
}

// class id: 5563, size: 0x28, field offset: 0x8
//   const constructor, 
class Wirid extends Equatable {

  _Mint field_8;
  _OneByteString field_10;
  WiridTitleType field_14;
  _Mint field_18;
  _Mint field_20;

  _ copyWith(/* No info */) {
    // ** addr: 0x8f1d24, size: 0x1b4
    // 0x8f1d24: EnterFrame
    //     0x8f1d24: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1d28: mov             fp, SP
    // 0x8f1d2c: AllocStack(0x28)
    //     0x8f1d2c: sub             SP, SP, #0x28
    // 0x8f1d30: SetupParameters({dynamic currentCount = Null /* r3 */, dynamic maxCount = Null /* r5 */, dynamic order = Null /* r0 */})
    //     0x8f1d30: ldur            w0, [x4, #0x13]
    //     0x8f1d34: ldur            w2, [x4, #0x1f]
    //     0x8f1d38: add             x2, x2, HEAP, lsl #32
    //     0x8f1d3c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29bc8] "currentCount"
    //     0x8f1d40: ldr             x16, [x16, #0xbc8]
    //     0x8f1d44: cmp             w2, w16
    //     0x8f1d48: b.ne            #0x8f1d6c
    //     0x8f1d4c: ldur            w2, [x4, #0x23]
    //     0x8f1d50: add             x2, x2, HEAP, lsl #32
    //     0x8f1d54: sub             w3, w0, w2
    //     0x8f1d58: add             x2, fp, w3, sxtw #2
    //     0x8f1d5c: ldr             x2, [x2, #8]
    //     0x8f1d60: mov             x3, x2
    //     0x8f1d64: movz            x2, #0x1
    //     0x8f1d68: b               #0x8f1d74
    //     0x8f1d6c: mov             x3, NULL
    //     0x8f1d70: movz            x2, #0
    //     0x8f1d74: lsl             x5, x2, #1
    //     0x8f1d78: lsl             w6, w5, #1
    //     0x8f1d7c: add             w7, w6, #8
    //     0x8f1d80: add             x16, x4, w7, sxtw #1
    //     0x8f1d84: ldur            w8, [x16, #0xf]
    //     0x8f1d88: add             x8, x8, HEAP, lsl #32
    //     0x8f1d8c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29bd0] "maxCount"
    //     0x8f1d90: ldr             x16, [x16, #0xbd0]
    //     0x8f1d94: cmp             w8, w16
    //     0x8f1d98: b.ne            #0x8f1dcc
    //     0x8f1d9c: add             w2, w6, #0xa
    //     0x8f1da0: add             x16, x4, w2, sxtw #1
    //     0x8f1da4: ldur            w6, [x16, #0xf]
    //     0x8f1da8: add             x6, x6, HEAP, lsl #32
    //     0x8f1dac: sub             w2, w0, w6
    //     0x8f1db0: add             x6, fp, w2, sxtw #2
    //     0x8f1db4: ldr             x6, [x6, #8]
    //     0x8f1db8: add             w2, w5, #2
    //     0x8f1dbc: sbfx            x5, x2, #1, #0x1f
    //     0x8f1dc0: mov             x2, x5
    //     0x8f1dc4: mov             x5, x6
    //     0x8f1dc8: b               #0x8f1dd0
    //     0x8f1dcc: mov             x5, NULL
    //     0x8f1dd0: lsl             x6, x2, #1
    //     0x8f1dd4: lsl             w2, w6, #1
    //     0x8f1dd8: add             w6, w2, #8
    //     0x8f1ddc: add             x16, x4, w6, sxtw #1
    //     0x8f1de0: ldur            w7, [x16, #0xf]
    //     0x8f1de4: add             x7, x7, HEAP, lsl #32
    //     0x8f1de8: add             x16, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0x8f1dec: ldr             x16, [x16, #0xb78]
    //     0x8f1df0: cmp             w7, w16
    //     0x8f1df4: b.ne            #0x8f1e18
    //     0x8f1df8: add             w6, w2, #0xa
    //     0x8f1dfc: add             x16, x4, w6, sxtw #1
    //     0x8f1e00: ldur            w2, [x16, #0xf]
    //     0x8f1e04: add             x2, x2, HEAP, lsl #32
    //     0x8f1e08: sub             w4, w0, w2
    //     0x8f1e0c: add             x0, fp, w4, sxtw #2
    //     0x8f1e10: ldr             x0, [x0, #8]
    //     0x8f1e14: b               #0x8f1e1c
    //     0x8f1e18: mov             x0, NULL
    // 0x8f1e1c: cmp             w0, NULL
    // 0x8f1e20: b.ne            #0x8f1e2c
    // 0x8f1e24: LoadField: r0 = r1->field_7
    //     0x8f1e24: ldur            x0, [x1, #7]
    // 0x8f1e28: b               #0x8f1e3c
    // 0x8f1e2c: r2 = LoadInt32Instr(r0)
    //     0x8f1e2c: sbfx            x2, x0, #1, #0x1f
    //     0x8f1e30: tbz             w0, #0, #0x8f1e38
    //     0x8f1e34: ldur            x2, [x0, #7]
    // 0x8f1e38: mov             x0, x2
    // 0x8f1e3c: stur            x0, [fp, #-0x28]
    // 0x8f1e40: LoadField: r2 = r1->field_f
    //     0x8f1e40: ldur            w2, [x1, #0xf]
    // 0x8f1e44: DecompressPointer r2
    //     0x8f1e44: add             x2, x2, HEAP, lsl #32
    // 0x8f1e48: stur            x2, [fp, #-0x20]
    // 0x8f1e4c: LoadField: r4 = r1->field_13
    //     0x8f1e4c: ldur            w4, [x1, #0x13]
    // 0x8f1e50: DecompressPointer r4
    //     0x8f1e50: add             x4, x4, HEAP, lsl #32
    // 0x8f1e54: stur            x4, [fp, #-0x18]
    // 0x8f1e58: cmp             w5, NULL
    // 0x8f1e5c: b.ne            #0x8f1e68
    // 0x8f1e60: ArrayLoad: r5 = r1[0]  ; List_8
    //     0x8f1e60: ldur            x5, [x1, #0x17]
    // 0x8f1e64: b               #0x8f1e78
    // 0x8f1e68: r6 = LoadInt32Instr(r5)
    //     0x8f1e68: sbfx            x6, x5, #1, #0x1f
    //     0x8f1e6c: tbz             w5, #0, #0x8f1e74
    //     0x8f1e70: ldur            x6, [x5, #7]
    // 0x8f1e74: mov             x5, x6
    // 0x8f1e78: stur            x5, [fp, #-0x10]
    // 0x8f1e7c: cmp             w3, NULL
    // 0x8f1e80: b.ne            #0x8f1e90
    // 0x8f1e84: LoadField: r3 = r1->field_1f
    //     0x8f1e84: ldur            x3, [x1, #0x1f]
    // 0x8f1e88: mov             x1, x3
    // 0x8f1e8c: b               #0x8f1e9c
    // 0x8f1e90: r1 = LoadInt32Instr(r3)
    //     0x8f1e90: sbfx            x1, x3, #1, #0x1f
    //     0x8f1e94: tbz             w3, #0, #0x8f1e9c
    //     0x8f1e98: ldur            x1, [x3, #7]
    // 0x8f1e9c: stur            x1, [fp, #-8]
    // 0x8f1ea0: r0 = Wirid()
    //     0x8f1ea0: bl              #0x8f1ed8  ; AllocateWiridStub -> Wirid (size=0x28)
    // 0x8f1ea4: ldur            x1, [fp, #-0x28]
    // 0x8f1ea8: StoreField: r0->field_7 = r1
    //     0x8f1ea8: stur            x1, [x0, #7]
    // 0x8f1eac: ldur            x1, [fp, #-0x20]
    // 0x8f1eb0: StoreField: r0->field_f = r1
    //     0x8f1eb0: stur            w1, [x0, #0xf]
    // 0x8f1eb4: ldur            x1, [fp, #-0x18]
    // 0x8f1eb8: StoreField: r0->field_13 = r1
    //     0x8f1eb8: stur            w1, [x0, #0x13]
    // 0x8f1ebc: ldur            x1, [fp, #-0x10]
    // 0x8f1ec0: ArrayStore: r0[0] = r1  ; List_8
    //     0x8f1ec0: stur            x1, [x0, #0x17]
    // 0x8f1ec4: ldur            x1, [fp, #-8]
    // 0x8f1ec8: StoreField: r0->field_1f = r1
    //     0x8f1ec8: stur            x1, [x0, #0x1f]
    // 0x8f1ecc: LeaveFrame
    //     0x8f1ecc: mov             SP, fp
    //     0x8f1ed0: ldp             fp, lr, [SP], #0x10
    // 0x8f1ed4: ret
    //     0x8f1ed4: ret             
  }
  get _ props(/* No info */) {
    // ** addr: 0xbdc2b0, size: 0xdc
    // 0xbdc2b0: EnterFrame
    //     0xbdc2b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc2b4: mov             fp, SP
    // 0xbdc2b8: AllocStack(0x30)
    //     0xbdc2b8: sub             SP, SP, #0x30
    // 0xbdc2bc: r3 = 10
    //     0xbdc2bc: movz            x3, #0xa
    // 0xbdc2c0: LoadField: r2 = r1->field_7
    //     0xbdc2c0: ldur            x2, [x1, #7]
    // 0xbdc2c4: LoadField: r4 = r1->field_f
    //     0xbdc2c4: ldur            w4, [x1, #0xf]
    // 0xbdc2c8: DecompressPointer r4
    //     0xbdc2c8: add             x4, x4, HEAP, lsl #32
    // 0xbdc2cc: stur            x4, [fp, #-0x28]
    // 0xbdc2d0: LoadField: r5 = r1->field_1f
    //     0xbdc2d0: ldur            x5, [x1, #0x1f]
    // 0xbdc2d4: stur            x5, [fp, #-0x20]
    // 0xbdc2d8: ArrayLoad: r6 = r1[0]  ; List_8
    //     0xbdc2d8: ldur            x6, [x1, #0x17]
    // 0xbdc2dc: stur            x6, [fp, #-0x18]
    // 0xbdc2e0: LoadField: r7 = r1->field_13
    //     0xbdc2e0: ldur            w7, [x1, #0x13]
    // 0xbdc2e4: DecompressPointer r7
    //     0xbdc2e4: add             x7, x7, HEAP, lsl #32
    // 0xbdc2e8: stur            x7, [fp, #-0x10]
    // 0xbdc2ec: r0 = BoxInt64Instr(r2)
    //     0xbdc2ec: sbfiz           x0, x2, #1, #0x1f
    //     0xbdc2f0: cmp             x2, x0, asr #1
    //     0xbdc2f4: b.eq            #0xbdc300
    //     0xbdc2f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc2fc: stur            x2, [x0, #7]
    // 0xbdc300: mov             x2, x3
    // 0xbdc304: r1 = Null
    //     0xbdc304: mov             x1, NULL
    // 0xbdc308: stur            x0, [fp, #-8]
    // 0xbdc30c: r0 = AllocateArray()
    //     0xbdc30c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc310: mov             x2, x0
    // 0xbdc314: ldur            x0, [fp, #-8]
    // 0xbdc318: stur            x2, [fp, #-0x30]
    // 0xbdc31c: StoreField: r2->field_f = r0
    //     0xbdc31c: stur            w0, [x2, #0xf]
    // 0xbdc320: ldur            x0, [fp, #-0x28]
    // 0xbdc324: StoreField: r2->field_13 = r0
    //     0xbdc324: stur            w0, [x2, #0x13]
    // 0xbdc328: ldur            x3, [fp, #-0x20]
    // 0xbdc32c: r0 = BoxInt64Instr(r3)
    //     0xbdc32c: sbfiz           x0, x3, #1, #0x1f
    //     0xbdc330: cmp             x3, x0, asr #1
    //     0xbdc334: b.eq            #0xbdc340
    //     0xbdc338: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc33c: stur            x3, [x0, #7]
    // 0xbdc340: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdc340: stur            w0, [x2, #0x17]
    // 0xbdc344: ldur            x3, [fp, #-0x18]
    // 0xbdc348: r0 = BoxInt64Instr(r3)
    //     0xbdc348: sbfiz           x0, x3, #1, #0x1f
    //     0xbdc34c: cmp             x3, x0, asr #1
    //     0xbdc350: b.eq            #0xbdc35c
    //     0xbdc354: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdc358: stur            x3, [x0, #7]
    // 0xbdc35c: StoreField: r2->field_1b = r0
    //     0xbdc35c: stur            w0, [x2, #0x1b]
    // 0xbdc360: ldur            x0, [fp, #-0x10]
    // 0xbdc364: StoreField: r2->field_1f = r0
    //     0xbdc364: stur            w0, [x2, #0x1f]
    // 0xbdc368: r1 = <Object?>
    //     0xbdc368: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc36c: r0 = AllocateGrowableArray()
    //     0xbdc36c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc370: ldur            x1, [fp, #-0x30]
    // 0xbdc374: StoreField: r0->field_f = r1
    //     0xbdc374: stur            w1, [x0, #0xf]
    // 0xbdc378: r1 = 10
    //     0xbdc378: movz            x1, #0xa
    // 0xbdc37c: StoreField: r0->field_b = r1
    //     0xbdc37c: stur            w1, [x0, #0xb]
    // 0xbdc380: LeaveFrame
    //     0xbdc380: mov             SP, fp
    //     0xbdc384: ldp             fp, lr, [SP], #0x10
    // 0xbdc388: ret
    //     0xbdc388: ret             
  }
}

// class id: 6832, size: 0x14, field offset: 0x14
enum WiridTitleType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d438, size: 0x64
    // 0xc4d438: EnterFrame
    //     0xc4d438: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d43c: mov             fp, SP
    // 0xc4d440: AllocStack(0x10)
    //     0xc4d440: sub             SP, SP, #0x10
    // 0xc4d444: SetupParameters(WiridTitleType this /* r1 => r0, fp-0x8 */)
    //     0xc4d444: mov             x0, x1
    //     0xc4d448: stur            x1, [fp, #-8]
    // 0xc4d44c: CheckStackOverflow
    //     0xc4d44c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d450: cmp             SP, x16
    //     0xc4d454: b.ls            #0xc4d494
    // 0xc4d458: r1 = Null
    //     0xc4d458: mov             x1, NULL
    // 0xc4d45c: r2 = 4
    //     0xc4d45c: movz            x2, #0x4
    // 0xc4d460: r0 = AllocateArray()
    //     0xc4d460: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d464: r16 = "WiridTitleType."
    //     0xc4d464: add             x16, PP, #0x23, lsl #12  ; [pp+0x23640] "WiridTitleType."
    //     0xc4d468: ldr             x16, [x16, #0x640]
    // 0xc4d46c: StoreField: r0->field_f = r16
    //     0xc4d46c: stur            w16, [x0, #0xf]
    // 0xc4d470: ldur            x1, [fp, #-8]
    // 0xc4d474: LoadField: r2 = r1->field_f
    //     0xc4d474: ldur            w2, [x1, #0xf]
    // 0xc4d478: DecompressPointer r2
    //     0xc4d478: add             x2, x2, HEAP, lsl #32
    // 0xc4d47c: StoreField: r0->field_13 = r2
    //     0xc4d47c: stur            w2, [x0, #0x13]
    // 0xc4d480: str             x0, [SP]
    // 0xc4d484: r0 = _interpolate()
    //     0xc4d484: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d488: LeaveFrame
    //     0xc4d488: mov             SP, fp
    //     0xc4d48c: ldp             fp, lr, [SP], #0x10
    // 0xc4d490: ret
    //     0xc4d490: ret             
    // 0xc4d494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d498: b               #0xc4d458
  }
}
