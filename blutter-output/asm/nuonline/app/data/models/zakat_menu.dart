// lib: , url: package:nuonline/app/data/models/zakat_menu.dart

// class id: 1050065, size: 0x8
class :: {
}

// class id: 5562, size: 0x18, field offset: 0x8
//   const constructor, 
class ZakatMenu extends Equatable {

  _OneByteString field_8;
  _OneByteString field_c;
  _OneByteString field_10;

  get _ props(/* No info */) {
    // ** addr: 0xbdc38c, size: 0x5c
    // 0xbdc38c: EnterFrame
    //     0xbdc38c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc390: mov             fp, SP
    // 0xbdc394: AllocStack(0x10)
    //     0xbdc394: sub             SP, SP, #0x10
    // 0xbdc398: r0 = 2
    //     0xbdc398: movz            x0, #0x2
    // 0xbdc39c: LoadField: r3 = r1->field_7
    //     0xbdc39c: ldur            w3, [x1, #7]
    // 0xbdc3a0: DecompressPointer r3
    //     0xbdc3a0: add             x3, x3, HEAP, lsl #32
    // 0xbdc3a4: mov             x2, x0
    // 0xbdc3a8: stur            x3, [fp, #-8]
    // 0xbdc3ac: r1 = Null
    //     0xbdc3ac: mov             x1, NULL
    // 0xbdc3b0: r0 = AllocateArray()
    //     0xbdc3b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbdc3b4: mov             x2, x0
    // 0xbdc3b8: ldur            x0, [fp, #-8]
    // 0xbdc3bc: stur            x2, [fp, #-0x10]
    // 0xbdc3c0: StoreField: r2->field_f = r0
    //     0xbdc3c0: stur            w0, [x2, #0xf]
    // 0xbdc3c4: r1 = <Object?>
    //     0xbdc3c4: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbdc3c8: r0 = AllocateGrowableArray()
    //     0xbdc3c8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbdc3cc: ldur            x1, [fp, #-0x10]
    // 0xbdc3d0: StoreField: r0->field_f = r1
    //     0xbdc3d0: stur            w1, [x0, #0xf]
    // 0xbdc3d4: r1 = 2
    //     0xbdc3d4: movz            x1, #0x2
    // 0xbdc3d8: StoreField: r0->field_b = r1
    //     0xbdc3d8: stur            w1, [x0, #0xb]
    // 0xbdc3dc: LeaveFrame
    //     0xbdc3dc: mov             SP, fp
    //     0xbdc3e0: ldp             fp, lr, [SP], #0x10
    // 0xbdc3e4: ret
    //     0xbdc3e4: ret             
  }
}

// class id: 6831, size: 0x14, field offset: 0x14
enum ZakatType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4d49c, size: 0x64
    // 0xc4d49c: EnterFrame
    //     0xc4d49c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4d4a0: mov             fp, SP
    // 0xc4d4a4: AllocStack(0x10)
    //     0xc4d4a4: sub             SP, SP, #0x10
    // 0xc4d4a8: SetupParameters(ZakatType this /* r1 => r0, fp-0x8 */)
    //     0xc4d4a8: mov             x0, x1
    //     0xc4d4ac: stur            x1, [fp, #-8]
    // 0xc4d4b0: CheckStackOverflow
    //     0xc4d4b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4d4b4: cmp             SP, x16
    //     0xc4d4b8: b.ls            #0xc4d4f8
    // 0xc4d4bc: r1 = Null
    //     0xc4d4bc: mov             x1, NULL
    // 0xc4d4c0: r2 = 4
    //     0xc4d4c0: movz            x2, #0x4
    // 0xc4d4c4: r0 = AllocateArray()
    //     0xc4d4c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4d4c8: r16 = "ZakatType."
    //     0xc4d4c8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31628] "ZakatType."
    //     0xc4d4cc: ldr             x16, [x16, #0x628]
    // 0xc4d4d0: StoreField: r0->field_f = r16
    //     0xc4d4d0: stur            w16, [x0, #0xf]
    // 0xc4d4d4: ldur            x1, [fp, #-8]
    // 0xc4d4d8: LoadField: r2 = r1->field_f
    //     0xc4d4d8: ldur            w2, [x1, #0xf]
    // 0xc4d4dc: DecompressPointer r2
    //     0xc4d4dc: add             x2, x2, HEAP, lsl #32
    // 0xc4d4e0: StoreField: r0->field_13 = r2
    //     0xc4d4e0: stur            w2, [x0, #0x13]
    // 0xc4d4e4: str             x0, [SP]
    // 0xc4d4e8: r0 = _interpolate()
    //     0xc4d4e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4d4ec: LeaveFrame
    //     0xc4d4ec: mov             SP, fp
    //     0xc4d4f0: ldp             fp, lr, [SP], #0x10
    // 0xc4d4f4: ret
    //     0xc4d4f4: ret             
    // 0xc4d4f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4d4f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4d4fc: b               #0xc4d4bc
  }
}
