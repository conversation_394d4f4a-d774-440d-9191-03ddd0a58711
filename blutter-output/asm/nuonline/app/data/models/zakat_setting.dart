// lib: , url: package:nuonline/app/data/models/zakat_setting.dart

// class id: 1050066, size: 0x8
class :: {
}

// class id: 1116, size: 0x48, field offset: 0x8
class ZakatSetting extends Object {

  factory _ ZakatSetting.fromMap(/* No info */) {
    // ** addr: 0x7e9348, size: 0x71c
    // 0x7e9348: EnterFrame
    //     0x7e9348: stp             fp, lr, [SP, #-0x10]!
    //     0x7e934c: mov             fp, SP
    // 0x7e9350: AllocStack(0x68)
    //     0x7e9350: sub             SP, SP, #0x68
    // 0x7e9354: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x7e9354: mov             x3, x2
    //     0x7e9358: stur            x2, [fp, #-8]
    // 0x7e935c: CheckStackOverflow
    //     0x7e935c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9360: cmp             SP, x16
    //     0x7e9364: b.ls            #0x7e9a5c
    // 0x7e9368: r0 = LoadClassIdInstr(r3)
    //     0x7e9368: ldur            x0, [x3, #-1]
    //     0x7e936c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9370: mov             x1, x3
    // 0x7e9374: r2 = "enabled"
    //     0x7e9374: ldr             x2, [PP, #0x3e58]  ; [pp+0x3e58] "enabled"
    // 0x7e9378: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9378: sub             lr, x0, #0x114
    //     0x7e937c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9380: blr             lr
    // 0x7e9384: mov             x3, x0
    // 0x7e9388: r2 = Null
    //     0x7e9388: mov             x2, NULL
    // 0x7e938c: r1 = Null
    //     0x7e938c: mov             x1, NULL
    // 0x7e9390: stur            x3, [fp, #-0x10]
    // 0x7e9394: r4 = 60
    //     0x7e9394: movz            x4, #0x3c
    // 0x7e9398: branchIfSmi(r0, 0x7e93a4)
    //     0x7e9398: tbz             w0, #0, #0x7e93a4
    // 0x7e939c: r4 = LoadClassIdInstr(r0)
    //     0x7e939c: ldur            x4, [x0, #-1]
    //     0x7e93a0: ubfx            x4, x4, #0xc, #0x14
    // 0x7e93a4: cmp             x4, #0x3f
    // 0x7e93a8: b.eq            #0x7e93bc
    // 0x7e93ac: r8 = bool?
    //     0x7e93ac: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7e93b0: r3 = Null
    //     0x7e93b0: add             x3, PP, #0x35, lsl #12  ; [pp+0x35660] Null
    //     0x7e93b4: ldr             x3, [x3, #0x660]
    // 0x7e93b8: r0 = bool?()
    //     0x7e93b8: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7e93bc: ldur            x0, [fp, #-0x10]
    // 0x7e93c0: cmp             w0, NULL
    // 0x7e93c4: b.ne            #0x7e93d0
    // 0x7e93c8: r4 = false
    //     0x7e93c8: add             x4, NULL, #0x30  ; false
    // 0x7e93cc: b               #0x7e93d4
    // 0x7e93d0: mov             x4, x0
    // 0x7e93d4: ldur            x3, [fp, #-8]
    // 0x7e93d8: stur            x4, [fp, #-0x10]
    // 0x7e93dc: r0 = LoadClassIdInstr(r3)
    //     0x7e93dc: ldur            x0, [x3, #-1]
    //     0x7e93e0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e93e4: mov             x1, x3
    // 0x7e93e8: r2 = "premium_price"
    //     0x7e93e8: add             x2, PP, #0x35, lsl #12  ; [pp+0x35670] "premium_price"
    //     0x7e93ec: ldr             x2, [x2, #0x670]
    // 0x7e93f0: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e93f0: sub             lr, x0, #0x114
    //     0x7e93f4: ldr             lr, [x21, lr, lsl #3]
    //     0x7e93f8: blr             lr
    // 0x7e93fc: mov             x3, x0
    // 0x7e9400: r2 = Null
    //     0x7e9400: mov             x2, NULL
    // 0x7e9404: r1 = Null
    //     0x7e9404: mov             x1, NULL
    // 0x7e9408: stur            x3, [fp, #-0x18]
    // 0x7e940c: branchIfSmi(r0, 0x7e9434)
    //     0x7e940c: tbz             w0, #0, #0x7e9434
    // 0x7e9410: r4 = LoadClassIdInstr(r0)
    //     0x7e9410: ldur            x4, [x0, #-1]
    //     0x7e9414: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9418: sub             x4, x4, #0x3c
    // 0x7e941c: cmp             x4, #1
    // 0x7e9420: b.ls            #0x7e9434
    // 0x7e9424: r8 = int?
    //     0x7e9424: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e9428: r3 = Null
    //     0x7e9428: add             x3, PP, #0x35, lsl #12  ; [pp+0x35678] Null
    //     0x7e942c: ldr             x3, [x3, #0x678]
    // 0x7e9430: r0 = int?()
    //     0x7e9430: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e9434: ldur            x0, [fp, #-0x18]
    // 0x7e9438: cmp             w0, NULL
    // 0x7e943c: b.ne            #0x7e9448
    // 0x7e9440: r4 = 0
    //     0x7e9440: movz            x4, #0
    // 0x7e9444: b               #0x7e9458
    // 0x7e9448: r1 = LoadInt32Instr(r0)
    //     0x7e9448: sbfx            x1, x0, #1, #0x1f
    //     0x7e944c: tbz             w0, #0, #0x7e9454
    //     0x7e9450: ldur            x1, [x0, #7]
    // 0x7e9454: mov             x4, x1
    // 0x7e9458: ldur            x3, [fp, #-8]
    // 0x7e945c: stur            x4, [fp, #-0x20]
    // 0x7e9460: r0 = LoadClassIdInstr(r3)
    //     0x7e9460: ldur            x0, [x3, #-1]
    //     0x7e9464: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9468: mov             x1, x3
    // 0x7e946c: r2 = "medium_price"
    //     0x7e946c: add             x2, PP, #0x35, lsl #12  ; [pp+0x35688] "medium_price"
    //     0x7e9470: ldr             x2, [x2, #0x688]
    // 0x7e9474: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9474: sub             lr, x0, #0x114
    //     0x7e9478: ldr             lr, [x21, lr, lsl #3]
    //     0x7e947c: blr             lr
    // 0x7e9480: mov             x3, x0
    // 0x7e9484: r2 = Null
    //     0x7e9484: mov             x2, NULL
    // 0x7e9488: r1 = Null
    //     0x7e9488: mov             x1, NULL
    // 0x7e948c: stur            x3, [fp, #-0x18]
    // 0x7e9490: branchIfSmi(r0, 0x7e94b8)
    //     0x7e9490: tbz             w0, #0, #0x7e94b8
    // 0x7e9494: r4 = LoadClassIdInstr(r0)
    //     0x7e9494: ldur            x4, [x0, #-1]
    //     0x7e9498: ubfx            x4, x4, #0xc, #0x14
    // 0x7e949c: sub             x4, x4, #0x3c
    // 0x7e94a0: cmp             x4, #1
    // 0x7e94a4: b.ls            #0x7e94b8
    // 0x7e94a8: r8 = int?
    //     0x7e94a8: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e94ac: r3 = Null
    //     0x7e94ac: add             x3, PP, #0x35, lsl #12  ; [pp+0x35690] Null
    //     0x7e94b0: ldr             x3, [x3, #0x690]
    // 0x7e94b4: r0 = int?()
    //     0x7e94b4: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e94b8: ldur            x0, [fp, #-0x18]
    // 0x7e94bc: cmp             w0, NULL
    // 0x7e94c0: b.ne            #0x7e94cc
    // 0x7e94c4: r4 = 0
    //     0x7e94c4: movz            x4, #0
    // 0x7e94c8: b               #0x7e94dc
    // 0x7e94cc: r1 = LoadInt32Instr(r0)
    //     0x7e94cc: sbfx            x1, x0, #1, #0x1f
    //     0x7e94d0: tbz             w0, #0, #0x7e94d8
    //     0x7e94d4: ldur            x1, [x0, #7]
    // 0x7e94d8: mov             x4, x1
    // 0x7e94dc: ldur            x3, [fp, #-8]
    // 0x7e94e0: stur            x4, [fp, #-0x28]
    // 0x7e94e4: r0 = LoadClassIdInstr(r3)
    //     0x7e94e4: ldur            x0, [x3, #-1]
    //     0x7e94e8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e94ec: mov             x1, x3
    // 0x7e94f0: r2 = "premium_enabled"
    //     0x7e94f0: add             x2, PP, #0x35, lsl #12  ; [pp+0x356a0] "premium_enabled"
    //     0x7e94f4: ldr             x2, [x2, #0x6a0]
    // 0x7e94f8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e94f8: sub             lr, x0, #0x114
    //     0x7e94fc: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9500: blr             lr
    // 0x7e9504: mov             x3, x0
    // 0x7e9508: r2 = Null
    //     0x7e9508: mov             x2, NULL
    // 0x7e950c: r1 = Null
    //     0x7e950c: mov             x1, NULL
    // 0x7e9510: stur            x3, [fp, #-0x18]
    // 0x7e9514: r4 = 60
    //     0x7e9514: movz            x4, #0x3c
    // 0x7e9518: branchIfSmi(r0, 0x7e9524)
    //     0x7e9518: tbz             w0, #0, #0x7e9524
    // 0x7e951c: r4 = LoadClassIdInstr(r0)
    //     0x7e951c: ldur            x4, [x0, #-1]
    //     0x7e9520: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9524: cmp             x4, #0x3f
    // 0x7e9528: b.eq            #0x7e953c
    // 0x7e952c: r8 = bool?
    //     0x7e952c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7e9530: r3 = Null
    //     0x7e9530: add             x3, PP, #0x35, lsl #12  ; [pp+0x356a8] Null
    //     0x7e9534: ldr             x3, [x3, #0x6a8]
    // 0x7e9538: r0 = bool?()
    //     0x7e9538: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7e953c: ldur            x0, [fp, #-0x18]
    // 0x7e9540: cmp             w0, NULL
    // 0x7e9544: b.ne            #0x7e9550
    // 0x7e9548: r4 = false
    //     0x7e9548: add             x4, NULL, #0x30  ; false
    // 0x7e954c: b               #0x7e9554
    // 0x7e9550: mov             x4, x0
    // 0x7e9554: ldur            x3, [fp, #-8]
    // 0x7e9558: stur            x4, [fp, #-0x18]
    // 0x7e955c: r0 = LoadClassIdInstr(r3)
    //     0x7e955c: ldur            x0, [x3, #-1]
    //     0x7e9560: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9564: mov             x1, x3
    // 0x7e9568: r2 = "medium_enabled"
    //     0x7e9568: add             x2, PP, #0x35, lsl #12  ; [pp+0x356b8] "medium_enabled"
    //     0x7e956c: ldr             x2, [x2, #0x6b8]
    // 0x7e9570: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9570: sub             lr, x0, #0x114
    //     0x7e9574: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9578: blr             lr
    // 0x7e957c: mov             x3, x0
    // 0x7e9580: r2 = Null
    //     0x7e9580: mov             x2, NULL
    // 0x7e9584: r1 = Null
    //     0x7e9584: mov             x1, NULL
    // 0x7e9588: stur            x3, [fp, #-0x30]
    // 0x7e958c: r4 = 60
    //     0x7e958c: movz            x4, #0x3c
    // 0x7e9590: branchIfSmi(r0, 0x7e959c)
    //     0x7e9590: tbz             w0, #0, #0x7e959c
    // 0x7e9594: r4 = LoadClassIdInstr(r0)
    //     0x7e9594: ldur            x4, [x0, #-1]
    //     0x7e9598: ubfx            x4, x4, #0xc, #0x14
    // 0x7e959c: cmp             x4, #0x3f
    // 0x7e95a0: b.eq            #0x7e95b4
    // 0x7e95a4: r8 = bool?
    //     0x7e95a4: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0x7e95a8: r3 = Null
    //     0x7e95a8: add             x3, PP, #0x35, lsl #12  ; [pp+0x356c0] Null
    //     0x7e95ac: ldr             x3, [x3, #0x6c0]
    // 0x7e95b0: r0 = bool?()
    //     0x7e95b0: bl              #0x60b174  ; IsType_bool?_Stub
    // 0x7e95b4: ldur            x0, [fp, #-0x30]
    // 0x7e95b8: cmp             w0, NULL
    // 0x7e95bc: b.ne            #0x7e95c8
    // 0x7e95c0: r4 = false
    //     0x7e95c0: add             x4, NULL, #0x30  ; false
    // 0x7e95c4: b               #0x7e95cc
    // 0x7e95c8: mov             x4, x0
    // 0x7e95cc: ldur            x3, [fp, #-8]
    // 0x7e95d0: stur            x4, [fp, #-0x30]
    // 0x7e95d4: r0 = LoadClassIdInstr(r3)
    //     0x7e95d4: ldur            x0, [x3, #-1]
    //     0x7e95d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e95dc: mov             x1, x3
    // 0x7e95e0: r2 = "fitrah_banner"
    //     0x7e95e0: add             x2, PP, #0x35, lsl #12  ; [pp+0x356d0] "fitrah_banner"
    //     0x7e95e4: ldr             x2, [x2, #0x6d0]
    // 0x7e95e8: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e95e8: sub             lr, x0, #0x114
    //     0x7e95ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7e95f0: blr             lr
    // 0x7e95f4: mov             x3, x0
    // 0x7e95f8: r2 = Null
    //     0x7e95f8: mov             x2, NULL
    // 0x7e95fc: r1 = Null
    //     0x7e95fc: mov             x1, NULL
    // 0x7e9600: stur            x3, [fp, #-0x38]
    // 0x7e9604: r4 = 60
    //     0x7e9604: movz            x4, #0x3c
    // 0x7e9608: branchIfSmi(r0, 0x7e9614)
    //     0x7e9608: tbz             w0, #0, #0x7e9614
    // 0x7e960c: r4 = LoadClassIdInstr(r0)
    //     0x7e960c: ldur            x4, [x0, #-1]
    //     0x7e9610: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9614: sub             x4, x4, #0x5e
    // 0x7e9618: cmp             x4, #1
    // 0x7e961c: b.ls            #0x7e9630
    // 0x7e9620: r8 = String?
    //     0x7e9620: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e9624: r3 = Null
    //     0x7e9624: add             x3, PP, #0x35, lsl #12  ; [pp+0x356d8] Null
    //     0x7e9628: ldr             x3, [x3, #0x6d8]
    // 0x7e962c: r0 = String?()
    //     0x7e962c: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e9630: ldur            x0, [fp, #-0x38]
    // 0x7e9634: cmp             w0, NULL
    // 0x7e9638: b.ne            #0x7e9648
    // 0x7e963c: r4 = "packages/nuikit/assets/images/illustration/banner-zakat-fitrah.png"
    //     0x7e963c: add             x4, PP, #0x35, lsl #12  ; [pp+0x356e8] "packages/nuikit/assets/images/illustration/banner-zakat-fitrah.png"
    //     0x7e9640: ldr             x4, [x4, #0x6e8]
    // 0x7e9644: b               #0x7e964c
    // 0x7e9648: mov             x4, x0
    // 0x7e964c: ldur            x3, [fp, #-8]
    // 0x7e9650: stur            x4, [fp, #-0x38]
    // 0x7e9654: r0 = LoadClassIdInstr(r3)
    //     0x7e9654: ldur            x0, [x3, #-1]
    //     0x7e9658: ubfx            x0, x0, #0xc, #0x14
    // 0x7e965c: mov             x1, x3
    // 0x7e9660: r2 = "fitrah_description"
    //     0x7e9660: add             x2, PP, #0x35, lsl #12  ; [pp+0x356f0] "fitrah_description"
    //     0x7e9664: ldr             x2, [x2, #0x6f0]
    // 0x7e9668: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9668: sub             lr, x0, #0x114
    //     0x7e966c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9670: blr             lr
    // 0x7e9674: mov             x3, x0
    // 0x7e9678: r2 = Null
    //     0x7e9678: mov             x2, NULL
    // 0x7e967c: r1 = Null
    //     0x7e967c: mov             x1, NULL
    // 0x7e9680: stur            x3, [fp, #-0x40]
    // 0x7e9684: r4 = 60
    //     0x7e9684: movz            x4, #0x3c
    // 0x7e9688: branchIfSmi(r0, 0x7e9694)
    //     0x7e9688: tbz             w0, #0, #0x7e9694
    // 0x7e968c: r4 = LoadClassIdInstr(r0)
    //     0x7e968c: ldur            x4, [x0, #-1]
    //     0x7e9690: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9694: sub             x4, x4, #0x5e
    // 0x7e9698: cmp             x4, #1
    // 0x7e969c: b.ls            #0x7e96b0
    // 0x7e96a0: r8 = String?
    //     0x7e96a0: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e96a4: r3 = Null
    //     0x7e96a4: add             x3, PP, #0x35, lsl #12  ; [pp+0x356f8] Null
    //     0x7e96a8: ldr             x3, [x3, #0x6f8]
    // 0x7e96ac: r0 = String?()
    //     0x7e96ac: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e96b0: ldur            x0, [fp, #-0x40]
    // 0x7e96b4: cmp             w0, NULL
    // 0x7e96b8: b.ne            #0x7e96c4
    // 0x7e96bc: r4 = ""
    //     0x7e96bc: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e96c0: b               #0x7e96c8
    // 0x7e96c4: mov             x4, x0
    // 0x7e96c8: ldur            x3, [fp, #-8]
    // 0x7e96cc: stur            x4, [fp, #-0x40]
    // 0x7e96d0: r0 = LoadClassIdInstr(r3)
    //     0x7e96d0: ldur            x0, [x3, #-1]
    //     0x7e96d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e96d8: mov             x1, x3
    // 0x7e96dc: r2 = "fitrah_started_at"
    //     0x7e96dc: add             x2, PP, #0x35, lsl #12  ; [pp+0x35708] "fitrah_started_at"
    //     0x7e96e0: ldr             x2, [x2, #0x708]
    // 0x7e96e4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e96e4: sub             lr, x0, #0x114
    //     0x7e96e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e96ec: blr             lr
    // 0x7e96f0: mov             x3, x0
    // 0x7e96f4: r2 = Null
    //     0x7e96f4: mov             x2, NULL
    // 0x7e96f8: r1 = Null
    //     0x7e96f8: mov             x1, NULL
    // 0x7e96fc: stur            x3, [fp, #-0x48]
    // 0x7e9700: r4 = 60
    //     0x7e9700: movz            x4, #0x3c
    // 0x7e9704: branchIfSmi(r0, 0x7e9710)
    //     0x7e9704: tbz             w0, #0, #0x7e9710
    // 0x7e9708: r4 = LoadClassIdInstr(r0)
    //     0x7e9708: ldur            x4, [x0, #-1]
    //     0x7e970c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9710: sub             x4, x4, #0x5e
    // 0x7e9714: cmp             x4, #1
    // 0x7e9718: b.ls            #0x7e972c
    // 0x7e971c: r8 = String?
    //     0x7e971c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e9720: r3 = Null
    //     0x7e9720: add             x3, PP, #0x35, lsl #12  ; [pp+0x35710] Null
    //     0x7e9724: ldr             x3, [x3, #0x710]
    // 0x7e9728: r0 = String?()
    //     0x7e9728: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e972c: ldur            x0, [fp, #-0x48]
    // 0x7e9730: cmp             w0, NULL
    // 0x7e9734: b.ne            #0x7e9740
    // 0x7e9738: r1 = ""
    //     0x7e9738: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e973c: b               #0x7e9744
    // 0x7e9740: mov             x1, x0
    // 0x7e9744: ldur            x0, [fp, #-8]
    // 0x7e9748: r0 = tryParse()
    //     0x7e9748: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x7e974c: mov             x4, x0
    // 0x7e9750: ldur            x3, [fp, #-8]
    // 0x7e9754: stur            x4, [fp, #-0x48]
    // 0x7e9758: r0 = LoadClassIdInstr(r3)
    //     0x7e9758: ldur            x0, [x3, #-1]
    //     0x7e975c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9760: mov             x1, x3
    // 0x7e9764: r2 = "fitrah_ended_at"
    //     0x7e9764: add             x2, PP, #0x35, lsl #12  ; [pp+0x35720] "fitrah_ended_at"
    //     0x7e9768: ldr             x2, [x2, #0x720]
    // 0x7e976c: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e976c: sub             lr, x0, #0x114
    //     0x7e9770: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9774: blr             lr
    // 0x7e9778: mov             x3, x0
    // 0x7e977c: r2 = Null
    //     0x7e977c: mov             x2, NULL
    // 0x7e9780: r1 = Null
    //     0x7e9780: mov             x1, NULL
    // 0x7e9784: stur            x3, [fp, #-0x50]
    // 0x7e9788: r4 = 60
    //     0x7e9788: movz            x4, #0x3c
    // 0x7e978c: branchIfSmi(r0, 0x7e9798)
    //     0x7e978c: tbz             w0, #0, #0x7e9798
    // 0x7e9790: r4 = LoadClassIdInstr(r0)
    //     0x7e9790: ldur            x4, [x0, #-1]
    //     0x7e9794: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9798: sub             x4, x4, #0x5e
    // 0x7e979c: cmp             x4, #1
    // 0x7e97a0: b.ls            #0x7e97b4
    // 0x7e97a4: r8 = String?
    //     0x7e97a4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e97a8: r3 = Null
    //     0x7e97a8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35728] Null
    //     0x7e97ac: ldr             x3, [x3, #0x728]
    // 0x7e97b0: r0 = String?()
    //     0x7e97b0: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e97b4: ldur            x0, [fp, #-0x50]
    // 0x7e97b8: cmp             w0, NULL
    // 0x7e97bc: b.ne            #0x7e97c8
    // 0x7e97c0: r1 = ""
    //     0x7e97c0: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e97c4: b               #0x7e97cc
    // 0x7e97c8: mov             x1, x0
    // 0x7e97cc: ldur            x0, [fp, #-8]
    // 0x7e97d0: r0 = tryParse()
    //     0x7e97d0: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x7e97d4: mov             x4, x0
    // 0x7e97d8: ldur            x3, [fp, #-8]
    // 0x7e97dc: stur            x4, [fp, #-0x50]
    // 0x7e97e0: r0 = LoadClassIdInstr(r3)
    //     0x7e97e0: ldur            x0, [x3, #-1]
    //     0x7e97e4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e97e8: mov             x1, x3
    // 0x7e97ec: r2 = "fitrah_total_muzakki"
    //     0x7e97ec: add             x2, PP, #0x35, lsl #12  ; [pp+0x35738] "fitrah_total_muzakki"
    //     0x7e97f0: ldr             x2, [x2, #0x738]
    // 0x7e97f4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e97f4: sub             lr, x0, #0x114
    //     0x7e97f8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e97fc: blr             lr
    // 0x7e9800: mov             x3, x0
    // 0x7e9804: r2 = Null
    //     0x7e9804: mov             x2, NULL
    // 0x7e9808: r1 = Null
    //     0x7e9808: mov             x1, NULL
    // 0x7e980c: stur            x3, [fp, #-0x58]
    // 0x7e9810: branchIfSmi(r0, 0x7e9838)
    //     0x7e9810: tbz             w0, #0, #0x7e9838
    // 0x7e9814: r4 = LoadClassIdInstr(r0)
    //     0x7e9814: ldur            x4, [x0, #-1]
    //     0x7e9818: ubfx            x4, x4, #0xc, #0x14
    // 0x7e981c: sub             x4, x4, #0x3c
    // 0x7e9820: cmp             x4, #1
    // 0x7e9824: b.ls            #0x7e9838
    // 0x7e9828: r8 = int?
    //     0x7e9828: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e982c: r3 = Null
    //     0x7e982c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35740] Null
    //     0x7e9830: ldr             x3, [x3, #0x740]
    // 0x7e9834: r0 = int?()
    //     0x7e9834: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e9838: ldur            x3, [fp, #-8]
    // 0x7e983c: r0 = LoadClassIdInstr(r3)
    //     0x7e983c: ldur            x0, [x3, #-1]
    //     0x7e9840: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9844: mov             x1, x3
    // 0x7e9848: r2 = "fidyah_price"
    //     0x7e9848: add             x2, PP, #0x35, lsl #12  ; [pp+0x35750] "fidyah_price"
    //     0x7e984c: ldr             x2, [x2, #0x750]
    // 0x7e9850: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9850: sub             lr, x0, #0x114
    //     0x7e9854: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9858: blr             lr
    // 0x7e985c: mov             x3, x0
    // 0x7e9860: r2 = Null
    //     0x7e9860: mov             x2, NULL
    // 0x7e9864: r1 = Null
    //     0x7e9864: mov             x1, NULL
    // 0x7e9868: stur            x3, [fp, #-0x60]
    // 0x7e986c: branchIfSmi(r0, 0x7e9894)
    //     0x7e986c: tbz             w0, #0, #0x7e9894
    // 0x7e9870: r4 = LoadClassIdInstr(r0)
    //     0x7e9870: ldur            x4, [x0, #-1]
    //     0x7e9874: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9878: sub             x4, x4, #0x3c
    // 0x7e987c: cmp             x4, #1
    // 0x7e9880: b.ls            #0x7e9894
    // 0x7e9884: r8 = int?
    //     0x7e9884: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0x7e9888: r3 = Null
    //     0x7e9888: add             x3, PP, #0x35, lsl #12  ; [pp+0x35758] Null
    //     0x7e988c: ldr             x3, [x3, #0x758]
    // 0x7e9890: r0 = int?()
    //     0x7e9890: bl              #0xed4d88  ; IsType_int?_Stub
    // 0x7e9894: ldur            x0, [fp, #-0x60]
    // 0x7e9898: cmp             w0, NULL
    // 0x7e989c: b.ne            #0x7e98a8
    // 0x7e98a0: r4 = 50000
    //     0x7e98a0: movz            x4, #0xc350
    // 0x7e98a4: b               #0x7e98b8
    // 0x7e98a8: r1 = LoadInt32Instr(r0)
    //     0x7e98a8: sbfx            x1, x0, #1, #0x1f
    //     0x7e98ac: tbz             w0, #0, #0x7e98b4
    //     0x7e98b0: ldur            x1, [x0, #7]
    // 0x7e98b4: mov             x4, x1
    // 0x7e98b8: ldur            x3, [fp, #-8]
    // 0x7e98bc: stur            x4, [fp, #-0x68]
    // 0x7e98c0: r0 = LoadClassIdInstr(r3)
    //     0x7e98c0: ldur            x0, [x3, #-1]
    //     0x7e98c4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e98c8: mov             x1, x3
    // 0x7e98cc: r2 = "fidyah_banner"
    //     0x7e98cc: add             x2, PP, #0x35, lsl #12  ; [pp+0x35768] "fidyah_banner"
    //     0x7e98d0: ldr             x2, [x2, #0x768]
    // 0x7e98d4: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e98d4: sub             lr, x0, #0x114
    //     0x7e98d8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e98dc: blr             lr
    // 0x7e98e0: mov             x3, x0
    // 0x7e98e4: r2 = Null
    //     0x7e98e4: mov             x2, NULL
    // 0x7e98e8: r1 = Null
    //     0x7e98e8: mov             x1, NULL
    // 0x7e98ec: stur            x3, [fp, #-0x60]
    // 0x7e98f0: r4 = 60
    //     0x7e98f0: movz            x4, #0x3c
    // 0x7e98f4: branchIfSmi(r0, 0x7e9900)
    //     0x7e98f4: tbz             w0, #0, #0x7e9900
    // 0x7e98f8: r4 = LoadClassIdInstr(r0)
    //     0x7e98f8: ldur            x4, [x0, #-1]
    //     0x7e98fc: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9900: sub             x4, x4, #0x5e
    // 0x7e9904: cmp             x4, #1
    // 0x7e9908: b.ls            #0x7e991c
    // 0x7e990c: r8 = String?
    //     0x7e990c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e9910: r3 = Null
    //     0x7e9910: add             x3, PP, #0x35, lsl #12  ; [pp+0x35770] Null
    //     0x7e9914: ldr             x3, [x3, #0x770]
    // 0x7e9918: r0 = String?()
    //     0x7e9918: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e991c: ldur            x0, [fp, #-0x60]
    // 0x7e9920: cmp             w0, NULL
    // 0x7e9924: b.ne            #0x7e9934
    // 0x7e9928: r3 = "packages/nuikit/assets/images/illustration/banner-fidyah.png"
    //     0x7e9928: add             x3, PP, #0x35, lsl #12  ; [pp+0x35780] "packages/nuikit/assets/images/illustration/banner-fidyah.png"
    //     0x7e992c: ldr             x3, [x3, #0x780]
    // 0x7e9930: b               #0x7e9938
    // 0x7e9934: mov             x3, x0
    // 0x7e9938: ldur            x1, [fp, #-8]
    // 0x7e993c: stur            x3, [fp, #-0x60]
    // 0x7e9940: r0 = LoadClassIdInstr(r1)
    //     0x7e9940: ldur            x0, [x1, #-1]
    //     0x7e9944: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9948: r2 = "fidyah_description"
    //     0x7e9948: add             x2, PP, #0x35, lsl #12  ; [pp+0x35788] "fidyah_description"
    //     0x7e994c: ldr             x2, [x2, #0x788]
    // 0x7e9950: r0 = GDT[cid_x0 + -0x114]()
    //     0x7e9950: sub             lr, x0, #0x114
    //     0x7e9954: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9958: blr             lr
    // 0x7e995c: mov             x3, x0
    // 0x7e9960: r2 = Null
    //     0x7e9960: mov             x2, NULL
    // 0x7e9964: r1 = Null
    //     0x7e9964: mov             x1, NULL
    // 0x7e9968: stur            x3, [fp, #-8]
    // 0x7e996c: r4 = 60
    //     0x7e996c: movz            x4, #0x3c
    // 0x7e9970: branchIfSmi(r0, 0x7e997c)
    //     0x7e9970: tbz             w0, #0, #0x7e997c
    // 0x7e9974: r4 = LoadClassIdInstr(r0)
    //     0x7e9974: ldur            x4, [x0, #-1]
    //     0x7e9978: ubfx            x4, x4, #0xc, #0x14
    // 0x7e997c: sub             x4, x4, #0x5e
    // 0x7e9980: cmp             x4, #1
    // 0x7e9984: b.ls            #0x7e9998
    // 0x7e9988: r8 = String?
    //     0x7e9988: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x7e998c: r3 = Null
    //     0x7e998c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35790] Null
    //     0x7e9990: ldr             x3, [x3, #0x790]
    // 0x7e9994: r0 = String?()
    //     0x7e9994: bl              #0x600324  ; IsType_String?_Stub
    // 0x7e9998: ldur            x0, [fp, #-8]
    // 0x7e999c: cmp             w0, NULL
    // 0x7e99a0: b.ne            #0x7e99ac
    // 0x7e99a4: r12 = ""
    //     0x7e99a4: ldr             x12, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7e99a8: b               #0x7e99b0
    // 0x7e99ac: mov             x12, x0
    // 0x7e99b0: ldur            x11, [fp, #-0x10]
    // 0x7e99b4: ldur            x10, [fp, #-0x20]
    // 0x7e99b8: ldur            x9, [fp, #-0x28]
    // 0x7e99bc: ldur            x8, [fp, #-0x18]
    // 0x7e99c0: ldur            x7, [fp, #-0x30]
    // 0x7e99c4: ldur            x6, [fp, #-0x38]
    // 0x7e99c8: ldur            x5, [fp, #-0x40]
    // 0x7e99cc: ldur            x4, [fp, #-0x48]
    // 0x7e99d0: ldur            x3, [fp, #-0x50]
    // 0x7e99d4: ldur            x2, [fp, #-0x58]
    // 0x7e99d8: ldur            x1, [fp, #-0x68]
    // 0x7e99dc: ldur            x0, [fp, #-0x60]
    // 0x7e99e0: stur            x12, [fp, #-8]
    // 0x7e99e4: r0 = ZakatSetting()
    //     0x7e99e4: bl              #0x7e9a64  ; AllocateZakatSettingStub -> ZakatSetting (size=0x48)
    // 0x7e99e8: ldur            x1, [fp, #-0x10]
    // 0x7e99ec: StoreField: r0->field_7 = r1
    //     0x7e99ec: stur            w1, [x0, #7]
    // 0x7e99f0: ldur            x1, [fp, #-0x20]
    // 0x7e99f4: StoreField: r0->field_b = r1
    //     0x7e99f4: stur            x1, [x0, #0xb]
    // 0x7e99f8: ldur            x1, [fp, #-0x28]
    // 0x7e99fc: StoreField: r0->field_13 = r1
    //     0x7e99fc: stur            x1, [x0, #0x13]
    // 0x7e9a00: ldur            x1, [fp, #-0x18]
    // 0x7e9a04: StoreField: r0->field_1b = r1
    //     0x7e9a04: stur            w1, [x0, #0x1b]
    // 0x7e9a08: ldur            x1, [fp, #-0x30]
    // 0x7e9a0c: StoreField: r0->field_1f = r1
    //     0x7e9a0c: stur            w1, [x0, #0x1f]
    // 0x7e9a10: ldur            x1, [fp, #-0x38]
    // 0x7e9a14: StoreField: r0->field_23 = r1
    //     0x7e9a14: stur            w1, [x0, #0x23]
    // 0x7e9a18: ldur            x1, [fp, #-0x40]
    // 0x7e9a1c: StoreField: r0->field_27 = r1
    //     0x7e9a1c: stur            w1, [x0, #0x27]
    // 0x7e9a20: ldur            x1, [fp, #-0x48]
    // 0x7e9a24: StoreField: r0->field_2b = r1
    //     0x7e9a24: stur            w1, [x0, #0x2b]
    // 0x7e9a28: ldur            x1, [fp, #-0x50]
    // 0x7e9a2c: StoreField: r0->field_2f = r1
    //     0x7e9a2c: stur            w1, [x0, #0x2f]
    // 0x7e9a30: ldur            x1, [fp, #-0x58]
    // 0x7e9a34: StoreField: r0->field_33 = r1
    //     0x7e9a34: stur            w1, [x0, #0x33]
    // 0x7e9a38: ldur            x1, [fp, #-0x68]
    // 0x7e9a3c: StoreField: r0->field_37 = r1
    //     0x7e9a3c: stur            x1, [x0, #0x37]
    // 0x7e9a40: ldur            x1, [fp, #-0x60]
    // 0x7e9a44: StoreField: r0->field_3f = r1
    //     0x7e9a44: stur            w1, [x0, #0x3f]
    // 0x7e9a48: ldur            x1, [fp, #-8]
    // 0x7e9a4c: StoreField: r0->field_43 = r1
    //     0x7e9a4c: stur            w1, [x0, #0x43]
    // 0x7e9a50: LeaveFrame
    //     0x7e9a50: mov             SP, fp
    //     0x7e9a54: ldp             fp, lr, [SP], #0x10
    // 0x7e9a58: ret
    //     0x7e9a58: ret             
    // 0x7e9a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9a60: b               #0x7e9368
  }
  get _ isFitrahEnded(/* No info */) {
    // ** addr: 0xaf64e4, size: 0x54
    // 0xaf64e4: EnterFrame
    //     0xaf64e4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf64e8: mov             fp, SP
    // 0xaf64ec: CheckStackOverflow
    //     0xaf64ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf64f0: cmp             SP, x16
    //     0xaf64f4: b.ls            #0xaf6530
    // 0xaf64f8: LoadField: r0 = r1->field_7
    //     0xaf64f8: ldur            w0, [x1, #7]
    // 0xaf64fc: DecompressPointer r0
    //     0xaf64fc: add             x0, x0, HEAP, lsl #32
    // 0xaf6500: tbz             w0, #4, #0xaf650c
    // 0xaf6504: r0 = true
    //     0xaf6504: add             x0, NULL, #0x20  ; true
    // 0xaf6508: b               #0xaf6524
    // 0xaf650c: r0 = fitrahRemainingDay()
    //     0xaf650c: bl              #0xaf66e0  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::fitrahRemainingDay
    // 0xaf6510: tbnz            x0, #0x3f, #0xaf651c
    // 0xaf6514: r1 = false
    //     0xaf6514: add             x1, NULL, #0x30  ; false
    // 0xaf6518: b               #0xaf6520
    // 0xaf651c: r1 = true
    //     0xaf651c: add             x1, NULL, #0x20  ; true
    // 0xaf6520: mov             x0, x1
    // 0xaf6524: LeaveFrame
    //     0xaf6524: mov             SP, fp
    //     0xaf6528: ldp             fp, lr, [SP], #0x10
    // 0xaf652c: ret
    //     0xaf652c: ret             
    // 0xaf6530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf6530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf6534: b               #0xaf64f8
  }
  get _ fitrahProgress(/* No info */) {
    // ** addr: 0xaf65c0, size: 0x90
    // 0xaf65c0: EnterFrame
    //     0xaf65c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf65c4: mov             fp, SP
    // 0xaf65c8: AllocStack(0x18)
    //     0xaf65c8: sub             SP, SP, #0x18
    // 0xaf65cc: SetupParameters(ZakatSetting this /* r1 => r0, fp-0x8 */)
    //     0xaf65cc: mov             x0, x1
    //     0xaf65d0: stur            x1, [fp, #-8]
    // 0xaf65d4: CheckStackOverflow
    //     0xaf65d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf65d8: cmp             SP, x16
    //     0xaf65dc: b.ls            #0xaf6648
    // 0xaf65e0: mov             x1, x0
    // 0xaf65e4: r0 = fitrahTotalDay()
    //     0xaf65e4: bl              #0xaf6650  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::fitrahTotalDay
    // 0xaf65e8: cbnz            x0, #0xaf65fc
    // 0xaf65ec: d0 = 0.000000
    //     0xaf65ec: eor             v0.16b, v0.16b, v0.16b
    // 0xaf65f0: LeaveFrame
    //     0xaf65f0: mov             SP, fp
    //     0xaf65f4: ldp             fp, lr, [SP], #0x10
    // 0xaf65f8: ret
    //     0xaf65f8: ret             
    // 0xaf65fc: ldur            x1, [fp, #-8]
    // 0xaf6600: r0 = fitrahTotalDay()
    //     0xaf6600: bl              #0xaf6650  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::fitrahTotalDay
    // 0xaf6604: ldur            x1, [fp, #-8]
    // 0xaf6608: stur            x0, [fp, #-0x10]
    // 0xaf660c: r0 = fitrahRemainingDay()
    //     0xaf660c: bl              #0xaf66e0  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::fitrahRemainingDay
    // 0xaf6610: mov             x1, x0
    // 0xaf6614: ldur            x0, [fp, #-0x10]
    // 0xaf6618: sub             x2, x0, x1
    // 0xaf661c: ldur            x1, [fp, #-8]
    // 0xaf6620: stur            x2, [fp, #-0x18]
    // 0xaf6624: r0 = fitrahTotalDay()
    //     0xaf6624: bl              #0xaf6650  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::fitrahTotalDay
    // 0xaf6628: mov             x1, x0
    // 0xaf662c: ldur            x0, [fp, #-0x18]
    // 0xaf6630: scvtf           d1, x0
    // 0xaf6634: scvtf           d2, x1
    // 0xaf6638: fdiv            d0, d1, d2
    // 0xaf663c: LeaveFrame
    //     0xaf663c: mov             SP, fp
    //     0xaf6640: ldp             fp, lr, [SP], #0x10
    // 0xaf6644: ret
    //     0xaf6644: ret             
    // 0xaf6648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf6648: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf664c: b               #0xaf65e0
  }
  get _ fitrahTotalDay(/* No info */) {
    // ** addr: 0xaf6650, size: 0x90
    // 0xaf6650: EnterFrame
    //     0xaf6650: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6654: mov             fp, SP
    // 0xaf6658: CheckStackOverflow
    //     0xaf6658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf665c: cmp             SP, x16
    //     0xaf6660: b.ls            #0xaf66d0
    // 0xaf6664: LoadField: r2 = r1->field_2b
    //     0xaf6664: ldur            w2, [x1, #0x2b]
    // 0xaf6668: DecompressPointer r2
    //     0xaf6668: add             x2, x2, HEAP, lsl #32
    // 0xaf666c: cmp             w2, NULL
    // 0xaf6670: b.ne            #0xaf6694
    // 0xaf6674: LoadField: r0 = r1->field_2f
    //     0xaf6674: ldur            w0, [x1, #0x2f]
    // 0xaf6678: DecompressPointer r0
    //     0xaf6678: add             x0, x0, HEAP, lsl #32
    // 0xaf667c: cmp             w0, NULL
    // 0xaf6680: b.ne            #0xaf6694
    // 0xaf6684: r0 = 0
    //     0xaf6684: movz            x0, #0
    // 0xaf6688: LeaveFrame
    //     0xaf6688: mov             SP, fp
    //     0xaf668c: ldp             fp, lr, [SP], #0x10
    // 0xaf6690: ret
    //     0xaf6690: ret             
    // 0xaf6694: LoadField: r0 = r1->field_2f
    //     0xaf6694: ldur            w0, [x1, #0x2f]
    // 0xaf6698: DecompressPointer r0
    //     0xaf6698: add             x0, x0, HEAP, lsl #32
    // 0xaf669c: cmp             w0, NULL
    // 0xaf66a0: b.eq            #0xaf66d8
    // 0xaf66a4: cmp             w2, NULL
    // 0xaf66a8: b.eq            #0xaf66dc
    // 0xaf66ac: mov             x1, x0
    // 0xaf66b0: r0 = difference()
    //     0xaf66b0: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xaf66b4: LoadField: r1 = r0->field_7
    //     0xaf66b4: ldur            x1, [x0, #7]
    // 0xaf66b8: r2 = 86400000000
    //     0xaf66b8: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xaf66bc: ldr             x2, [x2, #0x268]
    // 0xaf66c0: sdiv            x0, x1, x2
    // 0xaf66c4: LeaveFrame
    //     0xaf66c4: mov             SP, fp
    //     0xaf66c8: ldp             fp, lr, [SP], #0x10
    // 0xaf66cc: ret
    //     0xaf66cc: ret             
    // 0xaf66d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf66d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf66d4: b               #0xaf6664
    // 0xaf66d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf66d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf66dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf66dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ fitrahRemainingDay(/* No info */) {
    // ** addr: 0xaf66e0, size: 0xc4
    // 0xaf66e0: EnterFrame
    //     0xaf66e0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf66e4: mov             fp, SP
    // 0xaf66e8: AllocStack(0x10)
    //     0xaf66e8: sub             SP, SP, #0x10
    // 0xaf66ec: CheckStackOverflow
    //     0xaf66ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf66f0: cmp             SP, x16
    //     0xaf66f4: b.ls            #0xaf679c
    // 0xaf66f8: LoadField: r0 = r1->field_2f
    //     0xaf66f8: ldur            w0, [x1, #0x2f]
    // 0xaf66fc: DecompressPointer r0
    //     0xaf66fc: add             x0, x0, HEAP, lsl #32
    // 0xaf6700: stur            x0, [fp, #-8]
    // 0xaf6704: cmp             w0, NULL
    // 0xaf6708: b.ne            #0xaf6714
    // 0xaf670c: r1 = Null
    //     0xaf670c: mov             x1, NULL
    // 0xaf6710: b               #0xaf6770
    // 0xaf6714: r0 = DateTime()
    //     0xaf6714: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xaf6718: mov             x1, x0
    // 0xaf671c: r0 = false
    //     0xaf671c: add             x0, NULL, #0x30  ; false
    // 0xaf6720: stur            x1, [fp, #-0x10]
    // 0xaf6724: StoreField: r1->field_13 = r0
    //     0xaf6724: stur            w0, [x1, #0x13]
    // 0xaf6728: r0 = _getCurrentMicros()
    //     0xaf6728: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xaf672c: r1 = LoadInt32Instr(r0)
    //     0xaf672c: sbfx            x1, x0, #1, #0x1f
    //     0xaf6730: tbz             w0, #0, #0xaf6738
    //     0xaf6734: ldur            x1, [x0, #7]
    // 0xaf6738: ldur            x2, [fp, #-0x10]
    // 0xaf673c: StoreField: r2->field_7 = r1
    //     0xaf673c: stur            x1, [x2, #7]
    // 0xaf6740: ldur            x1, [fp, #-8]
    // 0xaf6744: r0 = difference()
    //     0xaf6744: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xaf6748: LoadField: r2 = r0->field_7
    //     0xaf6748: ldur            x2, [x0, #7]
    // 0xaf674c: r3 = 86400000000
    //     0xaf674c: add             x3, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xaf6750: ldr             x3, [x3, #0x268]
    // 0xaf6754: sdiv            x4, x2, x3
    // 0xaf6758: r0 = BoxInt64Instr(r4)
    //     0xaf6758: sbfiz           x0, x4, #1, #0x1f
    //     0xaf675c: cmp             x4, x0, asr #1
    //     0xaf6760: b.eq            #0xaf676c
    //     0xaf6764: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf6768: stur            x4, [x0, #7]
    // 0xaf676c: mov             x1, x0
    // 0xaf6770: cmp             w1, NULL
    // 0xaf6774: b.ne            #0xaf6780
    // 0xaf6778: r0 = 0
    //     0xaf6778: movz            x0, #0
    // 0xaf677c: b               #0xaf6790
    // 0xaf6780: r2 = LoadInt32Instr(r1)
    //     0xaf6780: sbfx            x2, x1, #1, #0x1f
    //     0xaf6784: tbz             w1, #0, #0xaf678c
    //     0xaf6788: ldur            x2, [x1, #7]
    // 0xaf678c: mov             x0, x2
    // 0xaf6790: LeaveFrame
    //     0xaf6790: mov             SP, fp
    //     0xaf6794: ldp             fp, lr, [SP], #0x10
    // 0xaf6798: ret
    //     0xaf6798: ret             
    // 0xaf679c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf679c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf67a0: b               #0xaf66f8
  }
}
