// lib: , url: package:nuonline/app/data/models/ziarah.dart

// class id: 1050067, size: 0x8
class :: {
}

// class id: 1111, size: 0x4c, field offset: 0x8
class ZiarahDetail extends Object {

  get _ die(/* No info */) {
    // ** addr: 0xb6c764, size: 0x1dc
    // 0xb6c764: EnterFrame
    //     0xb6c764: stp             fp, lr, [SP, #-0x10]!
    //     0xb6c768: mov             fp, SP
    // 0xb6c76c: AllocStack(0x30)
    //     0xb6c76c: sub             SP, SP, #0x30
    // 0xb6c770: SetupParameters(ZiarahDetail this /* r1 => r0, fp-0x8 */)
    //     0xb6c770: mov             x0, x1
    //     0xb6c774: stur            x1, [fp, #-8]
    // 0xb6c778: CheckStackOverflow
    //     0xb6c778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6c77c: cmp             SP, x16
    //     0xb6c780: b.ls            #0xb6c938
    // 0xb6c784: r1 = <String?>
    //     0xb6c784: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb6c788: r2 = 0
    //     0xb6c788: movz            x2, #0
    // 0xb6c78c: r0 = _GrowableList()
    //     0xb6c78c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb6c790: mov             x2, x0
    // 0xb6c794: ldur            x0, [fp, #-8]
    // 0xb6c798: stur            x2, [fp, #-0x10]
    // 0xb6c79c: LoadField: r1 = r0->field_3f
    //     0xb6c79c: ldur            w1, [x0, #0x3f]
    // 0xb6c7a0: DecompressPointer r1
    //     0xb6c7a0: add             x1, x1, HEAP, lsl #32
    // 0xb6c7a4: cmp             w1, NULL
    // 0xb6c7a8: b.eq            #0xb6c844
    // 0xb6c7ac: LoadField: r3 = r1->field_f
    //     0xb6c7ac: ldur            w3, [x1, #0xf]
    // 0xb6c7b0: DecompressPointer r3
    //     0xb6c7b0: add             x3, x3, HEAP, lsl #32
    // 0xb6c7b4: mov             x1, x3
    // 0xb6c7b8: r0 = StringExtension.toKab()
    //     0xb6c7b8: bl              #0xb6c940  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.toKab
    // 0xb6c7bc: mov             x1, x0
    // 0xb6c7c0: r0 = capitalize()
    //     0xb6c7c0: bl              #0xb6c9f0  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::capitalize
    // 0xb6c7c4: mov             x2, x0
    // 0xb6c7c8: ldur            x0, [fp, #-0x10]
    // 0xb6c7cc: stur            x2, [fp, #-0x20]
    // 0xb6c7d0: LoadField: r1 = r0->field_b
    //     0xb6c7d0: ldur            w1, [x0, #0xb]
    // 0xb6c7d4: LoadField: r3 = r0->field_f
    //     0xb6c7d4: ldur            w3, [x0, #0xf]
    // 0xb6c7d8: DecompressPointer r3
    //     0xb6c7d8: add             x3, x3, HEAP, lsl #32
    // 0xb6c7dc: LoadField: r4 = r3->field_b
    //     0xb6c7dc: ldur            w4, [x3, #0xb]
    // 0xb6c7e0: r3 = LoadInt32Instr(r1)
    //     0xb6c7e0: sbfx            x3, x1, #1, #0x1f
    // 0xb6c7e4: stur            x3, [fp, #-0x18]
    // 0xb6c7e8: r1 = LoadInt32Instr(r4)
    //     0xb6c7e8: sbfx            x1, x4, #1, #0x1f
    // 0xb6c7ec: cmp             x3, x1
    // 0xb6c7f0: b.ne            #0xb6c7fc
    // 0xb6c7f4: mov             x1, x0
    // 0xb6c7f8: r0 = _growToNextCapacity()
    //     0xb6c7f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6c7fc: ldur            x2, [fp, #-0x10]
    // 0xb6c800: ldur            x3, [fp, #-0x18]
    // 0xb6c804: add             x0, x3, #1
    // 0xb6c808: lsl             x1, x0, #1
    // 0xb6c80c: StoreField: r2->field_b = r1
    //     0xb6c80c: stur            w1, [x2, #0xb]
    // 0xb6c810: LoadField: r1 = r2->field_f
    //     0xb6c810: ldur            w1, [x2, #0xf]
    // 0xb6c814: DecompressPointer r1
    //     0xb6c814: add             x1, x1, HEAP, lsl #32
    // 0xb6c818: ldur            x0, [fp, #-0x20]
    // 0xb6c81c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6c81c: add             x25, x1, x3, lsl #2
    //     0xb6c820: add             x25, x25, #0xf
    //     0xb6c824: str             w0, [x25]
    //     0xb6c828: tbz             w0, #0, #0xb6c844
    //     0xb6c82c: ldurb           w16, [x1, #-1]
    //     0xb6c830: ldurb           w17, [x0, #-1]
    //     0xb6c834: and             x16, x17, x16, lsr #2
    //     0xb6c838: tst             x16, HEAP, lsr #32
    //     0xb6c83c: b.eq            #0xb6c844
    //     0xb6c840: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6c844: ldur            x0, [fp, #-8]
    // 0xb6c848: LoadField: r1 = r0->field_23
    //     0xb6c848: ldur            w1, [x0, #0x23]
    // 0xb6c84c: DecompressPointer r1
    //     0xb6c84c: add             x1, x1, HEAP, lsl #32
    // 0xb6c850: stur            x1, [fp, #-0x20]
    // 0xb6c854: str             x1, [SP]
    // 0xb6c858: r0 = toString()
    //     0xb6c858: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6c85c: r1 = LoadClassIdInstr(r0)
    //     0xb6c85c: ldur            x1, [x0, #-1]
    //     0xb6c860: ubfx            x1, x1, #0xc, #0x14
    // 0xb6c864: r16 = "-"
    //     0xb6c864: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb6c868: stp             x16, x0, [SP]
    // 0xb6c86c: mov             x0, x1
    // 0xb6c870: mov             lr, x0
    // 0xb6c874: ldr             lr, [x21, lr, lsl #3]
    // 0xb6c878: blr             lr
    // 0xb6c87c: tbz             w0, #4, #0xb6c914
    // 0xb6c880: ldur            x1, [fp, #-0x10]
    // 0xb6c884: ldur            x16, [fp, #-0x20]
    // 0xb6c888: str             x16, [SP]
    // 0xb6c88c: r0 = toString()
    //     0xb6c88c: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6c890: mov             x2, x0
    // 0xb6c894: ldur            x0, [fp, #-0x10]
    // 0xb6c898: stur            x2, [fp, #-8]
    // 0xb6c89c: LoadField: r1 = r0->field_b
    //     0xb6c89c: ldur            w1, [x0, #0xb]
    // 0xb6c8a0: LoadField: r3 = r0->field_f
    //     0xb6c8a0: ldur            w3, [x0, #0xf]
    // 0xb6c8a4: DecompressPointer r3
    //     0xb6c8a4: add             x3, x3, HEAP, lsl #32
    // 0xb6c8a8: LoadField: r4 = r3->field_b
    //     0xb6c8a8: ldur            w4, [x3, #0xb]
    // 0xb6c8ac: r3 = LoadInt32Instr(r1)
    //     0xb6c8ac: sbfx            x3, x1, #1, #0x1f
    // 0xb6c8b0: stur            x3, [fp, #-0x18]
    // 0xb6c8b4: r1 = LoadInt32Instr(r4)
    //     0xb6c8b4: sbfx            x1, x4, #1, #0x1f
    // 0xb6c8b8: cmp             x3, x1
    // 0xb6c8bc: b.ne            #0xb6c8c8
    // 0xb6c8c0: mov             x1, x0
    // 0xb6c8c4: r0 = _growToNextCapacity()
    //     0xb6c8c4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6c8c8: ldur            x2, [fp, #-0x10]
    // 0xb6c8cc: ldur            x3, [fp, #-0x18]
    // 0xb6c8d0: add             x0, x3, #1
    // 0xb6c8d4: lsl             x1, x0, #1
    // 0xb6c8d8: StoreField: r2->field_b = r1
    //     0xb6c8d8: stur            w1, [x2, #0xb]
    // 0xb6c8dc: LoadField: r1 = r2->field_f
    //     0xb6c8dc: ldur            w1, [x2, #0xf]
    // 0xb6c8e0: DecompressPointer r1
    //     0xb6c8e0: add             x1, x1, HEAP, lsl #32
    // 0xb6c8e4: ldur            x0, [fp, #-8]
    // 0xb6c8e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6c8e8: add             x25, x1, x3, lsl #2
    //     0xb6c8ec: add             x25, x25, #0xf
    //     0xb6c8f0: str             w0, [x25]
    //     0xb6c8f4: tbz             w0, #0, #0xb6c910
    //     0xb6c8f8: ldurb           w16, [x1, #-1]
    //     0xb6c8fc: ldurb           w17, [x0, #-1]
    //     0xb6c900: and             x16, x17, x16, lsr #2
    //     0xb6c904: tst             x16, HEAP, lsr #32
    //     0xb6c908: b.eq            #0xb6c910
    //     0xb6c90c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6c910: b               #0xb6c918
    // 0xb6c914: ldur            x2, [fp, #-0x10]
    // 0xb6c918: r16 = ", "
    //     0xb6c918: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb6c91c: str             x16, [SP]
    // 0xb6c920: mov             x1, x2
    // 0xb6c924: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb6c924: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb6c928: r0 = join()
    //     0xb6c928: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb6c92c: LeaveFrame
    //     0xb6c92c: mov             SP, fp
    //     0xb6c930: ldp             fp, lr, [SP], #0x10
    // 0xb6c934: ret
    //     0xb6c934: ret             
    // 0xb6c938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6c938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6c93c: b               #0xb6c784
  }
  get _ dieIsEmpty(/* No info */) {
    // ** addr: 0xb6c97c, size: 0x74
    // 0xb6c97c: EnterFrame
    //     0xb6c97c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6c980: mov             fp, SP
    // 0xb6c984: AllocStack(0x10)
    //     0xb6c984: sub             SP, SP, #0x10
    // 0xb6c988: CheckStackOverflow
    //     0xb6c988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6c98c: cmp             SP, x16
    //     0xb6c990: b.ls            #0xb6c9e8
    // 0xb6c994: LoadField: r0 = r1->field_3f
    //     0xb6c994: ldur            w0, [x1, #0x3f]
    // 0xb6c998: DecompressPointer r0
    //     0xb6c998: add             x0, x0, HEAP, lsl #32
    // 0xb6c99c: cmp             w0, NULL
    // 0xb6c9a0: b.ne            #0xb6c9d8
    // 0xb6c9a4: LoadField: r0 = r1->field_23
    //     0xb6c9a4: ldur            w0, [x1, #0x23]
    // 0xb6c9a8: DecompressPointer r0
    //     0xb6c9a8: add             x0, x0, HEAP, lsl #32
    // 0xb6c9ac: str             x0, [SP]
    // 0xb6c9b0: r0 = toString()
    //     0xb6c9b0: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6c9b4: r1 = LoadClassIdInstr(r0)
    //     0xb6c9b4: ldur            x1, [x0, #-1]
    //     0xb6c9b8: ubfx            x1, x1, #0xc, #0x14
    // 0xb6c9bc: r16 = "-"
    //     0xb6c9bc: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb6c9c0: stp             x16, x0, [SP]
    // 0xb6c9c4: mov             x0, x1
    // 0xb6c9c8: mov             lr, x0
    // 0xb6c9cc: ldr             lr, [x21, lr, lsl #3]
    // 0xb6c9d0: blr             lr
    // 0xb6c9d4: b               #0xb6c9dc
    // 0xb6c9d8: r0 = false
    //     0xb6c9d8: add             x0, NULL, #0x30  ; false
    // 0xb6c9dc: LeaveFrame
    //     0xb6c9dc: mov             SP, fp
    //     0xb6c9e0: ldp             fp, lr, [SP], #0x10
    // 0xb6c9e4: ret
    //     0xb6c9e4: ret             
    // 0xb6c9e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6c9e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6c9ec: b               #0xb6c994
  }
  get _ born(/* No info */) {
    // ** addr: 0xb6cbb0, size: 0x1dc
    // 0xb6cbb0: EnterFrame
    //     0xb6cbb0: stp             fp, lr, [SP, #-0x10]!
    //     0xb6cbb4: mov             fp, SP
    // 0xb6cbb8: AllocStack(0x30)
    //     0xb6cbb8: sub             SP, SP, #0x30
    // 0xb6cbbc: SetupParameters(ZiarahDetail this /* r1 => r0, fp-0x8 */)
    //     0xb6cbbc: mov             x0, x1
    //     0xb6cbc0: stur            x1, [fp, #-8]
    // 0xb6cbc4: CheckStackOverflow
    //     0xb6cbc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6cbc8: cmp             SP, x16
    //     0xb6cbcc: b.ls            #0xb6cd84
    // 0xb6cbd0: r1 = <String?>
    //     0xb6cbd0: ldr             x1, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xb6cbd4: r2 = 0
    //     0xb6cbd4: movz            x2, #0
    // 0xb6cbd8: r0 = _GrowableList()
    //     0xb6cbd8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb6cbdc: mov             x2, x0
    // 0xb6cbe0: ldur            x0, [fp, #-8]
    // 0xb6cbe4: stur            x2, [fp, #-0x10]
    // 0xb6cbe8: LoadField: r1 = r0->field_3b
    //     0xb6cbe8: ldur            w1, [x0, #0x3b]
    // 0xb6cbec: DecompressPointer r1
    //     0xb6cbec: add             x1, x1, HEAP, lsl #32
    // 0xb6cbf0: cmp             w1, NULL
    // 0xb6cbf4: b.eq            #0xb6cc90
    // 0xb6cbf8: LoadField: r3 = r1->field_f
    //     0xb6cbf8: ldur            w3, [x1, #0xf]
    // 0xb6cbfc: DecompressPointer r3
    //     0xb6cbfc: add             x3, x3, HEAP, lsl #32
    // 0xb6cc00: mov             x1, x3
    // 0xb6cc04: r0 = StringExtension.toKab()
    //     0xb6cc04: bl              #0xb6c940  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.toKab
    // 0xb6cc08: mov             x1, x0
    // 0xb6cc0c: r0 = capitalize()
    //     0xb6cc0c: bl              #0xb6c9f0  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::capitalize
    // 0xb6cc10: mov             x2, x0
    // 0xb6cc14: ldur            x0, [fp, #-0x10]
    // 0xb6cc18: stur            x2, [fp, #-0x20]
    // 0xb6cc1c: LoadField: r1 = r0->field_b
    //     0xb6cc1c: ldur            w1, [x0, #0xb]
    // 0xb6cc20: LoadField: r3 = r0->field_f
    //     0xb6cc20: ldur            w3, [x0, #0xf]
    // 0xb6cc24: DecompressPointer r3
    //     0xb6cc24: add             x3, x3, HEAP, lsl #32
    // 0xb6cc28: LoadField: r4 = r3->field_b
    //     0xb6cc28: ldur            w4, [x3, #0xb]
    // 0xb6cc2c: r3 = LoadInt32Instr(r1)
    //     0xb6cc2c: sbfx            x3, x1, #1, #0x1f
    // 0xb6cc30: stur            x3, [fp, #-0x18]
    // 0xb6cc34: r1 = LoadInt32Instr(r4)
    //     0xb6cc34: sbfx            x1, x4, #1, #0x1f
    // 0xb6cc38: cmp             x3, x1
    // 0xb6cc3c: b.ne            #0xb6cc48
    // 0xb6cc40: mov             x1, x0
    // 0xb6cc44: r0 = _growToNextCapacity()
    //     0xb6cc44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6cc48: ldur            x2, [fp, #-0x10]
    // 0xb6cc4c: ldur            x3, [fp, #-0x18]
    // 0xb6cc50: add             x0, x3, #1
    // 0xb6cc54: lsl             x1, x0, #1
    // 0xb6cc58: StoreField: r2->field_b = r1
    //     0xb6cc58: stur            w1, [x2, #0xb]
    // 0xb6cc5c: LoadField: r1 = r2->field_f
    //     0xb6cc5c: ldur            w1, [x2, #0xf]
    // 0xb6cc60: DecompressPointer r1
    //     0xb6cc60: add             x1, x1, HEAP, lsl #32
    // 0xb6cc64: ldur            x0, [fp, #-0x20]
    // 0xb6cc68: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6cc68: add             x25, x1, x3, lsl #2
    //     0xb6cc6c: add             x25, x25, #0xf
    //     0xb6cc70: str             w0, [x25]
    //     0xb6cc74: tbz             w0, #0, #0xb6cc90
    //     0xb6cc78: ldurb           w16, [x1, #-1]
    //     0xb6cc7c: ldurb           w17, [x0, #-1]
    //     0xb6cc80: and             x16, x17, x16, lsr #2
    //     0xb6cc84: tst             x16, HEAP, lsr #32
    //     0xb6cc88: b.eq            #0xb6cc90
    //     0xb6cc8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6cc90: ldur            x0, [fp, #-8]
    // 0xb6cc94: LoadField: r1 = r0->field_1f
    //     0xb6cc94: ldur            w1, [x0, #0x1f]
    // 0xb6cc98: DecompressPointer r1
    //     0xb6cc98: add             x1, x1, HEAP, lsl #32
    // 0xb6cc9c: stur            x1, [fp, #-0x20]
    // 0xb6cca0: str             x1, [SP]
    // 0xb6cca4: r0 = toString()
    //     0xb6cca4: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6cca8: r1 = LoadClassIdInstr(r0)
    //     0xb6cca8: ldur            x1, [x0, #-1]
    //     0xb6ccac: ubfx            x1, x1, #0xc, #0x14
    // 0xb6ccb0: r16 = "-"
    //     0xb6ccb0: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb6ccb4: stp             x16, x0, [SP]
    // 0xb6ccb8: mov             x0, x1
    // 0xb6ccbc: mov             lr, x0
    // 0xb6ccc0: ldr             lr, [x21, lr, lsl #3]
    // 0xb6ccc4: blr             lr
    // 0xb6ccc8: tbz             w0, #4, #0xb6cd60
    // 0xb6cccc: ldur            x1, [fp, #-0x10]
    // 0xb6ccd0: ldur            x16, [fp, #-0x20]
    // 0xb6ccd4: str             x16, [SP]
    // 0xb6ccd8: r0 = toString()
    //     0xb6ccd8: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6ccdc: mov             x2, x0
    // 0xb6cce0: ldur            x0, [fp, #-0x10]
    // 0xb6cce4: stur            x2, [fp, #-8]
    // 0xb6cce8: LoadField: r1 = r0->field_b
    //     0xb6cce8: ldur            w1, [x0, #0xb]
    // 0xb6ccec: LoadField: r3 = r0->field_f
    //     0xb6ccec: ldur            w3, [x0, #0xf]
    // 0xb6ccf0: DecompressPointer r3
    //     0xb6ccf0: add             x3, x3, HEAP, lsl #32
    // 0xb6ccf4: LoadField: r4 = r3->field_b
    //     0xb6ccf4: ldur            w4, [x3, #0xb]
    // 0xb6ccf8: r3 = LoadInt32Instr(r1)
    //     0xb6ccf8: sbfx            x3, x1, #1, #0x1f
    // 0xb6ccfc: stur            x3, [fp, #-0x18]
    // 0xb6cd00: r1 = LoadInt32Instr(r4)
    //     0xb6cd00: sbfx            x1, x4, #1, #0x1f
    // 0xb6cd04: cmp             x3, x1
    // 0xb6cd08: b.ne            #0xb6cd14
    // 0xb6cd0c: mov             x1, x0
    // 0xb6cd10: r0 = _growToNextCapacity()
    //     0xb6cd10: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6cd14: ldur            x2, [fp, #-0x10]
    // 0xb6cd18: ldur            x3, [fp, #-0x18]
    // 0xb6cd1c: add             x0, x3, #1
    // 0xb6cd20: lsl             x1, x0, #1
    // 0xb6cd24: StoreField: r2->field_b = r1
    //     0xb6cd24: stur            w1, [x2, #0xb]
    // 0xb6cd28: LoadField: r1 = r2->field_f
    //     0xb6cd28: ldur            w1, [x2, #0xf]
    // 0xb6cd2c: DecompressPointer r1
    //     0xb6cd2c: add             x1, x1, HEAP, lsl #32
    // 0xb6cd30: ldur            x0, [fp, #-8]
    // 0xb6cd34: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6cd34: add             x25, x1, x3, lsl #2
    //     0xb6cd38: add             x25, x25, #0xf
    //     0xb6cd3c: str             w0, [x25]
    //     0xb6cd40: tbz             w0, #0, #0xb6cd5c
    //     0xb6cd44: ldurb           w16, [x1, #-1]
    //     0xb6cd48: ldurb           w17, [x0, #-1]
    //     0xb6cd4c: and             x16, x17, x16, lsr #2
    //     0xb6cd50: tst             x16, HEAP, lsr #32
    //     0xb6cd54: b.eq            #0xb6cd5c
    //     0xb6cd58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6cd5c: b               #0xb6cd64
    // 0xb6cd60: ldur            x2, [fp, #-0x10]
    // 0xb6cd64: r16 = ", "
    //     0xb6cd64: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb6cd68: str             x16, [SP]
    // 0xb6cd6c: mov             x1, x2
    // 0xb6cd70: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb6cd70: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb6cd74: r0 = join()
    //     0xb6cd74: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb6cd78: LeaveFrame
    //     0xb6cd78: mov             SP, fp
    //     0xb6cd7c: ldp             fp, lr, [SP], #0x10
    // 0xb6cd80: ret
    //     0xb6cd80: ret             
    // 0xb6cd84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6cd84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6cd88: b               #0xb6cbd0
  }
  get _ bornIsEmpty(/* No info */) {
    // ** addr: 0xb6cd8c, size: 0x74
    // 0xb6cd8c: EnterFrame
    //     0xb6cd8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6cd90: mov             fp, SP
    // 0xb6cd94: AllocStack(0x10)
    //     0xb6cd94: sub             SP, SP, #0x10
    // 0xb6cd98: CheckStackOverflow
    //     0xb6cd98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6cd9c: cmp             SP, x16
    //     0xb6cda0: b.ls            #0xb6cdf8
    // 0xb6cda4: LoadField: r0 = r1->field_3b
    //     0xb6cda4: ldur            w0, [x1, #0x3b]
    // 0xb6cda8: DecompressPointer r0
    //     0xb6cda8: add             x0, x0, HEAP, lsl #32
    // 0xb6cdac: cmp             w0, NULL
    // 0xb6cdb0: b.ne            #0xb6cde8
    // 0xb6cdb4: LoadField: r0 = r1->field_1f
    //     0xb6cdb4: ldur            w0, [x1, #0x1f]
    // 0xb6cdb8: DecompressPointer r0
    //     0xb6cdb8: add             x0, x0, HEAP, lsl #32
    // 0xb6cdbc: str             x0, [SP]
    // 0xb6cdc0: r0 = toString()
    //     0xb6cdc0: bl              #0xc32a18  ; [package:nuonline/app/data/models/ziarah.dart] FigureDate::toString
    // 0xb6cdc4: r1 = LoadClassIdInstr(r0)
    //     0xb6cdc4: ldur            x1, [x0, #-1]
    //     0xb6cdc8: ubfx            x1, x1, #0xc, #0x14
    // 0xb6cdcc: r16 = "-"
    //     0xb6cdcc: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb6cdd0: stp             x16, x0, [SP]
    // 0xb6cdd4: mov             x0, x1
    // 0xb6cdd8: mov             lr, x0
    // 0xb6cddc: ldr             lr, [x21, lr, lsl #3]
    // 0xb6cde0: blr             lr
    // 0xb6cde4: b               #0xb6cdec
    // 0xb6cde8: r0 = false
    //     0xb6cde8: add             x0, NULL, #0x30  ; false
    // 0xb6cdec: LeaveFrame
    //     0xb6cdec: mov             SP, fp
    //     0xb6cdf0: ldp             fp, lr, [SP], #0x10
    // 0xb6cdf4: ret
    //     0xb6cdf4: ret             
    // 0xb6cdf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6cdf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6cdfc: b               #0xb6cda4
  }
  factory _ ZiarahDetail.fromMap(/* No info */) {
    // ** addr: 0xe7b394, size: 0xb74
    // 0xe7b394: EnterFrame
    //     0xe7b394: stp             fp, lr, [SP, #-0x10]!
    //     0xe7b398: mov             fp, SP
    // 0xe7b39c: AllocStack(0xa0)
    //     0xe7b39c: sub             SP, SP, #0xa0
    // 0xe7b3a0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe7b3a0: mov             x3, x2
    //     0xe7b3a4: stur            x2, [fp, #-8]
    // 0xe7b3a8: CheckStackOverflow
    //     0xe7b3a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7b3ac: cmp             SP, x16
    //     0xe7b3b0: b.ls            #0xe7bf00
    // 0xe7b3b4: r0 = LoadClassIdInstr(r3)
    //     0xe7b3b4: ldur            x0, [x3, #-1]
    //     0xe7b3b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b3bc: mov             x1, x3
    // 0xe7b3c0: r2 = "id"
    //     0xe7b3c0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7b3c4: ldr             x2, [x2, #0x740]
    // 0xe7b3c8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b3c8: sub             lr, x0, #0x114
    //     0xe7b3cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b3d0: blr             lr
    // 0xe7b3d4: mov             x3, x0
    // 0xe7b3d8: r2 = Null
    //     0xe7b3d8: mov             x2, NULL
    // 0xe7b3dc: r1 = Null
    //     0xe7b3dc: mov             x1, NULL
    // 0xe7b3e0: stur            x3, [fp, #-0x10]
    // 0xe7b3e4: branchIfSmi(r0, 0xe7b40c)
    //     0xe7b3e4: tbz             w0, #0, #0xe7b40c
    // 0xe7b3e8: r4 = LoadClassIdInstr(r0)
    //     0xe7b3e8: ldur            x4, [x0, #-1]
    //     0xe7b3ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b3f0: sub             x4, x4, #0x3c
    // 0xe7b3f4: cmp             x4, #1
    // 0xe7b3f8: b.ls            #0xe7b40c
    // 0xe7b3fc: r8 = int
    //     0xe7b3fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe7b400: r3 = Null
    //     0xe7b400: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f20] Null
    //     0xe7b404: ldr             x3, [x3, #0xf20]
    // 0xe7b408: r0 = int()
    //     0xe7b408: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe7b40c: ldur            x3, [fp, #-8]
    // 0xe7b410: r0 = LoadClassIdInstr(r3)
    //     0xe7b410: ldur            x0, [x3, #-1]
    //     0xe7b414: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b418: mov             x1, x3
    // 0xe7b41c: r2 = "name"
    //     0xe7b41c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7b420: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b420: sub             lr, x0, #0x114
    //     0xe7b424: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b428: blr             lr
    // 0xe7b42c: mov             x3, x0
    // 0xe7b430: r2 = Null
    //     0xe7b430: mov             x2, NULL
    // 0xe7b434: r1 = Null
    //     0xe7b434: mov             x1, NULL
    // 0xe7b438: stur            x3, [fp, #-0x18]
    // 0xe7b43c: r4 = 60
    //     0xe7b43c: movz            x4, #0x3c
    // 0xe7b440: branchIfSmi(r0, 0xe7b44c)
    //     0xe7b440: tbz             w0, #0, #0xe7b44c
    // 0xe7b444: r4 = LoadClassIdInstr(r0)
    //     0xe7b444: ldur            x4, [x0, #-1]
    //     0xe7b448: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b44c: sub             x4, x4, #0x5e
    // 0xe7b450: cmp             x4, #1
    // 0xe7b454: b.ls            #0xe7b468
    // 0xe7b458: r8 = String
    //     0xe7b458: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7b45c: r3 = Null
    //     0xe7b45c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f30] Null
    //     0xe7b460: ldr             x3, [x3, #0xf30]
    // 0xe7b464: r0 = String()
    //     0xe7b464: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7b468: ldur            x3, [fp, #-8]
    // 0xe7b46c: r0 = LoadClassIdInstr(r3)
    //     0xe7b46c: ldur            x0, [x3, #-1]
    //     0xe7b470: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b474: mov             x1, x3
    // 0xe7b478: r2 = "alias"
    //     0xe7b478: add             x2, PP, #0x49, lsl #12  ; [pp+0x49f40] "alias"
    //     0xe7b47c: ldr             x2, [x2, #0xf40]
    // 0xe7b480: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b480: sub             lr, x0, #0x114
    //     0xe7b484: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b488: blr             lr
    // 0xe7b48c: mov             x3, x0
    // 0xe7b490: r2 = Null
    //     0xe7b490: mov             x2, NULL
    // 0xe7b494: r1 = Null
    //     0xe7b494: mov             x1, NULL
    // 0xe7b498: stur            x3, [fp, #-0x20]
    // 0xe7b49c: r4 = 60
    //     0xe7b49c: movz            x4, #0x3c
    // 0xe7b4a0: branchIfSmi(r0, 0xe7b4ac)
    //     0xe7b4a0: tbz             w0, #0, #0xe7b4ac
    // 0xe7b4a4: r4 = LoadClassIdInstr(r0)
    //     0xe7b4a4: ldur            x4, [x0, #-1]
    //     0xe7b4a8: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b4ac: sub             x4, x4, #0x5e
    // 0xe7b4b0: cmp             x4, #1
    // 0xe7b4b4: b.ls            #0xe7b4c8
    // 0xe7b4b8: r8 = String?
    //     0xe7b4b8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xe7b4bc: r3 = Null
    //     0xe7b4bc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f48] Null
    //     0xe7b4c0: ldr             x3, [x3, #0xf48]
    // 0xe7b4c4: r0 = String?()
    //     0xe7b4c4: bl              #0x600324  ; IsType_String?_Stub
    // 0xe7b4c8: ldur            x3, [fp, #-8]
    // 0xe7b4cc: r0 = LoadClassIdInstr(r3)
    //     0xe7b4cc: ldur            x0, [x3, #-1]
    //     0xe7b4d0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b4d4: mov             x1, x3
    // 0xe7b4d8: r2 = "image"
    //     0xe7b4d8: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xe7b4dc: ldr             x2, [x2, #0x520]
    // 0xe7b4e0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b4e0: sub             lr, x0, #0x114
    //     0xe7b4e4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b4e8: blr             lr
    // 0xe7b4ec: mov             x3, x0
    // 0xe7b4f0: r2 = Null
    //     0xe7b4f0: mov             x2, NULL
    // 0xe7b4f4: r1 = Null
    //     0xe7b4f4: mov             x1, NULL
    // 0xe7b4f8: stur            x3, [fp, #-0x28]
    // 0xe7b4fc: r8 = Map<String, dynamic>
    //     0xe7b4fc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7b500: r3 = Null
    //     0xe7b500: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f58] Null
    //     0xe7b504: ldr             x3, [x3, #0xf58]
    // 0xe7b508: r0 = Map<String, dynamic>()
    //     0xe7b508: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7b50c: ldur            x2, [fp, #-0x28]
    // 0xe7b510: r1 = Null
    //     0xe7b510: mov             x1, NULL
    // 0xe7b514: r0 = Image.fromMap()
    //     0xe7b514: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xe7b518: mov             x4, x0
    // 0xe7b51c: ldur            x3, [fp, #-8]
    // 0xe7b520: stur            x4, [fp, #-0x28]
    // 0xe7b524: r0 = LoadClassIdInstr(r3)
    //     0xe7b524: ldur            x0, [x3, #-1]
    //     0xe7b528: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b52c: mov             x1, x3
    // 0xe7b530: r2 = "caption"
    //     0xe7b530: add             x2, PP, #0x19, lsl #12  ; [pp+0x19fc8] "caption"
    //     0xe7b534: ldr             x2, [x2, #0xfc8]
    // 0xe7b538: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b538: sub             lr, x0, #0x114
    //     0xe7b53c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b540: blr             lr
    // 0xe7b544: mov             x3, x0
    // 0xe7b548: r2 = Null
    //     0xe7b548: mov             x2, NULL
    // 0xe7b54c: r1 = Null
    //     0xe7b54c: mov             x1, NULL
    // 0xe7b550: stur            x3, [fp, #-0x30]
    // 0xe7b554: r4 = 60
    //     0xe7b554: movz            x4, #0x3c
    // 0xe7b558: branchIfSmi(r0, 0xe7b564)
    //     0xe7b558: tbz             w0, #0, #0xe7b564
    // 0xe7b55c: r4 = LoadClassIdInstr(r0)
    //     0xe7b55c: ldur            x4, [x0, #-1]
    //     0xe7b560: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b564: sub             x4, x4, #0x5e
    // 0xe7b568: cmp             x4, #1
    // 0xe7b56c: b.ls            #0xe7b580
    // 0xe7b570: r8 = String?
    //     0xe7b570: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xe7b574: r3 = Null
    //     0xe7b574: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f68] Null
    //     0xe7b578: ldr             x3, [x3, #0xf68]
    // 0xe7b57c: r0 = String?()
    //     0xe7b57c: bl              #0x600324  ; IsType_String?_Stub
    // 0xe7b580: ldur            x3, [fp, #-8]
    // 0xe7b584: r0 = LoadClassIdInstr(r3)
    //     0xe7b584: ldur            x0, [x3, #-1]
    //     0xe7b588: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b58c: mov             x1, x3
    // 0xe7b590: r2 = "year_born"
    //     0xe7b590: add             x2, PP, #0x49, lsl #12  ; [pp+0x49f78] "year_born"
    //     0xe7b594: ldr             x2, [x2, #0xf78]
    // 0xe7b598: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b598: sub             lr, x0, #0x114
    //     0xe7b59c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b5a0: blr             lr
    // 0xe7b5a4: mov             x3, x0
    // 0xe7b5a8: r2 = Null
    //     0xe7b5a8: mov             x2, NULL
    // 0xe7b5ac: r1 = Null
    //     0xe7b5ac: mov             x1, NULL
    // 0xe7b5b0: stur            x3, [fp, #-0x38]
    // 0xe7b5b4: branchIfSmi(r0, 0xe7b5dc)
    //     0xe7b5b4: tbz             w0, #0, #0xe7b5dc
    // 0xe7b5b8: r4 = LoadClassIdInstr(r0)
    //     0xe7b5b8: ldur            x4, [x0, #-1]
    //     0xe7b5bc: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b5c0: sub             x4, x4, #0x3c
    // 0xe7b5c4: cmp             x4, #1
    // 0xe7b5c8: b.ls            #0xe7b5dc
    // 0xe7b5cc: r8 = int?
    //     0xe7b5cc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b5d0: r3 = Null
    //     0xe7b5d0: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f80] Null
    //     0xe7b5d4: ldr             x3, [x3, #0xf80]
    // 0xe7b5d8: r0 = int?()
    //     0xe7b5d8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b5dc: ldur            x3, [fp, #-8]
    // 0xe7b5e0: r0 = LoadClassIdInstr(r3)
    //     0xe7b5e0: ldur            x0, [x3, #-1]
    //     0xe7b5e4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b5e8: mov             x1, x3
    // 0xe7b5ec: r2 = "month_born"
    //     0xe7b5ec: add             x2, PP, #0x49, lsl #12  ; [pp+0x49f90] "month_born"
    //     0xe7b5f0: ldr             x2, [x2, #0xf90]
    // 0xe7b5f4: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b5f4: sub             lr, x0, #0x114
    //     0xe7b5f8: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b5fc: blr             lr
    // 0xe7b600: mov             x3, x0
    // 0xe7b604: r2 = Null
    //     0xe7b604: mov             x2, NULL
    // 0xe7b608: r1 = Null
    //     0xe7b608: mov             x1, NULL
    // 0xe7b60c: stur            x3, [fp, #-0x40]
    // 0xe7b610: branchIfSmi(r0, 0xe7b638)
    //     0xe7b610: tbz             w0, #0, #0xe7b638
    // 0xe7b614: r4 = LoadClassIdInstr(r0)
    //     0xe7b614: ldur            x4, [x0, #-1]
    //     0xe7b618: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b61c: sub             x4, x4, #0x3c
    // 0xe7b620: cmp             x4, #1
    // 0xe7b624: b.ls            #0xe7b638
    // 0xe7b628: r8 = int?
    //     0xe7b628: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b62c: r3 = Null
    //     0xe7b62c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f98] Null
    //     0xe7b630: ldr             x3, [x3, #0xf98]
    // 0xe7b634: r0 = int?()
    //     0xe7b634: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b638: ldur            x3, [fp, #-8]
    // 0xe7b63c: r0 = LoadClassIdInstr(r3)
    //     0xe7b63c: ldur            x0, [x3, #-1]
    //     0xe7b640: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b644: mov             x1, x3
    // 0xe7b648: r2 = "day_born"
    //     0xe7b648: add             x2, PP, #0x49, lsl #12  ; [pp+0x49fa8] "day_born"
    //     0xe7b64c: ldr             x2, [x2, #0xfa8]
    // 0xe7b650: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b650: sub             lr, x0, #0x114
    //     0xe7b654: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b658: blr             lr
    // 0xe7b65c: mov             x3, x0
    // 0xe7b660: r2 = Null
    //     0xe7b660: mov             x2, NULL
    // 0xe7b664: r1 = Null
    //     0xe7b664: mov             x1, NULL
    // 0xe7b668: stur            x3, [fp, #-0x48]
    // 0xe7b66c: branchIfSmi(r0, 0xe7b694)
    //     0xe7b66c: tbz             w0, #0, #0xe7b694
    // 0xe7b670: r4 = LoadClassIdInstr(r0)
    //     0xe7b670: ldur            x4, [x0, #-1]
    //     0xe7b674: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b678: sub             x4, x4, #0x3c
    // 0xe7b67c: cmp             x4, #1
    // 0xe7b680: b.ls            #0xe7b694
    // 0xe7b684: r8 = int?
    //     0xe7b684: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b688: r3 = Null
    //     0xe7b688: add             x3, PP, #0x49, lsl #12  ; [pp+0x49fb0] Null
    //     0xe7b68c: ldr             x3, [x3, #0xfb0]
    // 0xe7b690: r0 = int?()
    //     0xe7b690: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b694: r0 = FigureDate()
    //     0xe7b694: bl              #0xe7c274  ; AllocateFigureDateStub -> FigureDate (size=0x14)
    // 0xe7b698: mov             x3, x0
    // 0xe7b69c: ldur            x0, [fp, #-0x38]
    // 0xe7b6a0: stur            x3, [fp, #-0x50]
    // 0xe7b6a4: StoreField: r3->field_7 = r0
    //     0xe7b6a4: stur            w0, [x3, #7]
    // 0xe7b6a8: ldur            x0, [fp, #-0x40]
    // 0xe7b6ac: StoreField: r3->field_b = r0
    //     0xe7b6ac: stur            w0, [x3, #0xb]
    // 0xe7b6b0: ldur            x0, [fp, #-0x48]
    // 0xe7b6b4: StoreField: r3->field_f = r0
    //     0xe7b6b4: stur            w0, [x3, #0xf]
    // 0xe7b6b8: ldur            x4, [fp, #-8]
    // 0xe7b6bc: r0 = LoadClassIdInstr(r4)
    //     0xe7b6bc: ldur            x0, [x4, #-1]
    //     0xe7b6c0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b6c4: mov             x1, x4
    // 0xe7b6c8: r2 = "year_die"
    //     0xe7b6c8: add             x2, PP, #0x49, lsl #12  ; [pp+0x49fc0] "year_die"
    //     0xe7b6cc: ldr             x2, [x2, #0xfc0]
    // 0xe7b6d0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b6d0: sub             lr, x0, #0x114
    //     0xe7b6d4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b6d8: blr             lr
    // 0xe7b6dc: mov             x3, x0
    // 0xe7b6e0: r2 = Null
    //     0xe7b6e0: mov             x2, NULL
    // 0xe7b6e4: r1 = Null
    //     0xe7b6e4: mov             x1, NULL
    // 0xe7b6e8: stur            x3, [fp, #-0x38]
    // 0xe7b6ec: branchIfSmi(r0, 0xe7b714)
    //     0xe7b6ec: tbz             w0, #0, #0xe7b714
    // 0xe7b6f0: r4 = LoadClassIdInstr(r0)
    //     0xe7b6f0: ldur            x4, [x0, #-1]
    //     0xe7b6f4: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b6f8: sub             x4, x4, #0x3c
    // 0xe7b6fc: cmp             x4, #1
    // 0xe7b700: b.ls            #0xe7b714
    // 0xe7b704: r8 = int?
    //     0xe7b704: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b708: r3 = Null
    //     0xe7b708: add             x3, PP, #0x49, lsl #12  ; [pp+0x49fc8] Null
    //     0xe7b70c: ldr             x3, [x3, #0xfc8]
    // 0xe7b710: r0 = int?()
    //     0xe7b710: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b714: ldur            x3, [fp, #-8]
    // 0xe7b718: r0 = LoadClassIdInstr(r3)
    //     0xe7b718: ldur            x0, [x3, #-1]
    //     0xe7b71c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b720: mov             x1, x3
    // 0xe7b724: r2 = "month_die"
    //     0xe7b724: add             x2, PP, #0x49, lsl #12  ; [pp+0x49fd8] "month_die"
    //     0xe7b728: ldr             x2, [x2, #0xfd8]
    // 0xe7b72c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b72c: sub             lr, x0, #0x114
    //     0xe7b730: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b734: blr             lr
    // 0xe7b738: mov             x3, x0
    // 0xe7b73c: r2 = Null
    //     0xe7b73c: mov             x2, NULL
    // 0xe7b740: r1 = Null
    //     0xe7b740: mov             x1, NULL
    // 0xe7b744: stur            x3, [fp, #-0x40]
    // 0xe7b748: branchIfSmi(r0, 0xe7b770)
    //     0xe7b748: tbz             w0, #0, #0xe7b770
    // 0xe7b74c: r4 = LoadClassIdInstr(r0)
    //     0xe7b74c: ldur            x4, [x0, #-1]
    //     0xe7b750: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b754: sub             x4, x4, #0x3c
    // 0xe7b758: cmp             x4, #1
    // 0xe7b75c: b.ls            #0xe7b770
    // 0xe7b760: r8 = int?
    //     0xe7b760: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b764: r3 = Null
    //     0xe7b764: add             x3, PP, #0x49, lsl #12  ; [pp+0x49fe0] Null
    //     0xe7b768: ldr             x3, [x3, #0xfe0]
    // 0xe7b76c: r0 = int?()
    //     0xe7b76c: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b770: ldur            x3, [fp, #-8]
    // 0xe7b774: r0 = LoadClassIdInstr(r3)
    //     0xe7b774: ldur            x0, [x3, #-1]
    //     0xe7b778: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b77c: mov             x1, x3
    // 0xe7b780: r2 = "day_die"
    //     0xe7b780: add             x2, PP, #0x49, lsl #12  ; [pp+0x49ff0] "day_die"
    //     0xe7b784: ldr             x2, [x2, #0xff0]
    // 0xe7b788: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b788: sub             lr, x0, #0x114
    //     0xe7b78c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b790: blr             lr
    // 0xe7b794: mov             x3, x0
    // 0xe7b798: r2 = Null
    //     0xe7b798: mov             x2, NULL
    // 0xe7b79c: r1 = Null
    //     0xe7b79c: mov             x1, NULL
    // 0xe7b7a0: stur            x3, [fp, #-0x48]
    // 0xe7b7a4: branchIfSmi(r0, 0xe7b7cc)
    //     0xe7b7a4: tbz             w0, #0, #0xe7b7cc
    // 0xe7b7a8: r4 = LoadClassIdInstr(r0)
    //     0xe7b7a8: ldur            x4, [x0, #-1]
    //     0xe7b7ac: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b7b0: sub             x4, x4, #0x3c
    // 0xe7b7b4: cmp             x4, #1
    // 0xe7b7b8: b.ls            #0xe7b7cc
    // 0xe7b7bc: r8 = int?
    //     0xe7b7bc: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xe7b7c0: r3 = Null
    //     0xe7b7c0: add             x3, PP, #0x49, lsl #12  ; [pp+0x49ff8] Null
    //     0xe7b7c4: ldr             x3, [x3, #0xff8]
    // 0xe7b7c8: r0 = int?()
    //     0xe7b7c8: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xe7b7cc: r0 = FigureDate()
    //     0xe7b7cc: bl              #0xe7c274  ; AllocateFigureDateStub -> FigureDate (size=0x14)
    // 0xe7b7d0: mov             x3, x0
    // 0xe7b7d4: ldur            x0, [fp, #-0x38]
    // 0xe7b7d8: stur            x3, [fp, #-0x58]
    // 0xe7b7dc: StoreField: r3->field_7 = r0
    //     0xe7b7dc: stur            w0, [x3, #7]
    // 0xe7b7e0: ldur            x0, [fp, #-0x40]
    // 0xe7b7e4: StoreField: r3->field_b = r0
    //     0xe7b7e4: stur            w0, [x3, #0xb]
    // 0xe7b7e8: ldur            x0, [fp, #-0x48]
    // 0xe7b7ec: StoreField: r3->field_f = r0
    //     0xe7b7ec: stur            w0, [x3, #0xf]
    // 0xe7b7f0: ldur            x4, [fp, #-8]
    // 0xe7b7f4: r0 = LoadClassIdInstr(r4)
    //     0xe7b7f4: ldur            x0, [x4, #-1]
    //     0xe7b7f8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b7fc: mov             x1, x4
    // 0xe7b800: r2 = "address"
    //     0xe7b800: add             x2, PP, #0xc, lsl #12  ; [pp+0xc828] "address"
    //     0xe7b804: ldr             x2, [x2, #0x828]
    // 0xe7b808: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b808: sub             lr, x0, #0x114
    //     0xe7b80c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b810: blr             lr
    // 0xe7b814: mov             x3, x0
    // 0xe7b818: r2 = Null
    //     0xe7b818: mov             x2, NULL
    // 0xe7b81c: r1 = Null
    //     0xe7b81c: mov             x1, NULL
    // 0xe7b820: stur            x3, [fp, #-0x38]
    // 0xe7b824: r4 = 60
    //     0xe7b824: movz            x4, #0x3c
    // 0xe7b828: branchIfSmi(r0, 0xe7b834)
    //     0xe7b828: tbz             w0, #0, #0xe7b834
    // 0xe7b82c: r4 = LoadClassIdInstr(r0)
    //     0xe7b82c: ldur            x4, [x0, #-1]
    //     0xe7b830: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b834: sub             x4, x4, #0x5e
    // 0xe7b838: cmp             x4, #1
    // 0xe7b83c: b.ls            #0xe7b850
    // 0xe7b840: r8 = String
    //     0xe7b840: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7b844: r3 = Null
    //     0xe7b844: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a008] Null
    //     0xe7b848: ldr             x3, [x3, #8]
    // 0xe7b84c: r0 = String()
    //     0xe7b84c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7b850: ldur            x3, [fp, #-8]
    // 0xe7b854: r0 = LoadClassIdInstr(r3)
    //     0xe7b854: ldur            x0, [x3, #-1]
    //     0xe7b858: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b85c: mov             x1, x3
    // 0xe7b860: r2 = "maps"
    //     0xe7b860: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a018] "maps"
    //     0xe7b864: ldr             x2, [x2, #0x18]
    // 0xe7b868: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b868: sub             lr, x0, #0x114
    //     0xe7b86c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b870: blr             lr
    // 0xe7b874: mov             x3, x0
    // 0xe7b878: r2 = Null
    //     0xe7b878: mov             x2, NULL
    // 0xe7b87c: r1 = Null
    //     0xe7b87c: mov             x1, NULL
    // 0xe7b880: stur            x3, [fp, #-0x40]
    // 0xe7b884: r4 = 60
    //     0xe7b884: movz            x4, #0x3c
    // 0xe7b888: branchIfSmi(r0, 0xe7b894)
    //     0xe7b888: tbz             w0, #0, #0xe7b894
    // 0xe7b88c: r4 = LoadClassIdInstr(r0)
    //     0xe7b88c: ldur            x4, [x0, #-1]
    //     0xe7b890: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b894: sub             x4, x4, #0x5e
    // 0xe7b898: cmp             x4, #1
    // 0xe7b89c: b.ls            #0xe7b8b0
    // 0xe7b8a0: r8 = String
    //     0xe7b8a0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7b8a4: r3 = Null
    //     0xe7b8a4: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a020] Null
    //     0xe7b8a8: ldr             x3, [x3, #0x20]
    // 0xe7b8ac: r0 = String()
    //     0xe7b8ac: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7b8b0: ldur            x3, [fp, #-8]
    // 0xe7b8b4: r0 = LoadClassIdInstr(r3)
    //     0xe7b8b4: ldur            x0, [x3, #-1]
    //     0xe7b8b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b8bc: mov             x1, x3
    // 0xe7b8c0: r2 = "distance"
    //     0xe7b8c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed58] "distance"
    //     0xe7b8c4: ldr             x2, [x2, #0xd58]
    // 0xe7b8c8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b8c8: sub             lr, x0, #0x114
    //     0xe7b8cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b8d0: blr             lr
    // 0xe7b8d4: r2 = Null
    //     0xe7b8d4: mov             x2, NULL
    // 0xe7b8d8: r1 = Null
    //     0xe7b8d8: mov             x1, NULL
    // 0xe7b8dc: r4 = 60
    //     0xe7b8dc: movz            x4, #0x3c
    // 0xe7b8e0: branchIfSmi(r0, 0xe7b8ec)
    //     0xe7b8e0: tbz             w0, #0, #0xe7b8ec
    // 0xe7b8e4: r4 = LoadClassIdInstr(r0)
    //     0xe7b8e4: ldur            x4, [x0, #-1]
    //     0xe7b8e8: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b8ec: cmp             x4, #0x3e
    // 0xe7b8f0: b.eq            #0xe7b904
    // 0xe7b8f4: r8 = double?
    //     0xe7b8f4: ldr             x8, [PP, #0x12d0]  ; [pp+0x12d0] Type: double?
    // 0xe7b8f8: r3 = Null
    //     0xe7b8f8: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a030] Null
    //     0xe7b8fc: ldr             x3, [x3, #0x30]
    // 0xe7b900: r0 = double?()
    //     0xe7b900: bl              #0xed4434  ; IsType_double?_Stub
    // 0xe7b904: ldur            x3, [fp, #-8]
    // 0xe7b908: r0 = LoadClassIdInstr(r3)
    //     0xe7b908: ldur            x0, [x3, #-1]
    //     0xe7b90c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b910: mov             x1, x3
    // 0xe7b914: r2 = "bio"
    //     0xe7b914: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a040] "bio"
    //     0xe7b918: ldr             x2, [x2, #0x40]
    // 0xe7b91c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b91c: sub             lr, x0, #0x114
    //     0xe7b920: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b924: blr             lr
    // 0xe7b928: mov             x3, x0
    // 0xe7b92c: r2 = Null
    //     0xe7b92c: mov             x2, NULL
    // 0xe7b930: r1 = Null
    //     0xe7b930: mov             x1, NULL
    // 0xe7b934: stur            x3, [fp, #-0x48]
    // 0xe7b938: r4 = 60
    //     0xe7b938: movz            x4, #0x3c
    // 0xe7b93c: branchIfSmi(r0, 0xe7b948)
    //     0xe7b93c: tbz             w0, #0, #0xe7b948
    // 0xe7b940: r4 = LoadClassIdInstr(r0)
    //     0xe7b940: ldur            x4, [x0, #-1]
    //     0xe7b944: ubfx            x4, x4, #0xc, #0x14
    // 0xe7b948: sub             x4, x4, #0x5e
    // 0xe7b94c: cmp             x4, #1
    // 0xe7b950: b.ls            #0xe7b964
    // 0xe7b954: r8 = String?
    //     0xe7b954: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xe7b958: r3 = Null
    //     0xe7b958: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a048] Null
    //     0xe7b95c: ldr             x3, [x3, #0x48]
    // 0xe7b960: r0 = String?()
    //     0xe7b960: bl              #0x600324  ; IsType_String?_Stub
    // 0xe7b964: ldur            x3, [fp, #-8]
    // 0xe7b968: r0 = LoadClassIdInstr(r3)
    //     0xe7b968: ldur            x0, [x3, #-1]
    //     0xe7b96c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b970: mov             x1, x3
    // 0xe7b974: r2 = "category"
    //     0xe7b974: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xe7b978: ldr             x2, [x2, #0x960]
    // 0xe7b97c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b97c: sub             lr, x0, #0x114
    //     0xe7b980: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b984: blr             lr
    // 0xe7b988: mov             x3, x0
    // 0xe7b98c: r2 = Null
    //     0xe7b98c: mov             x2, NULL
    // 0xe7b990: r1 = Null
    //     0xe7b990: mov             x1, NULL
    // 0xe7b994: stur            x3, [fp, #-0x60]
    // 0xe7b998: r8 = Map<String, dynamic>?
    //     0xe7b998: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0xe7b99c: r3 = Null
    //     0xe7b99c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a058] Null
    //     0xe7b9a0: ldr             x3, [x3, #0x58]
    // 0xe7b9a4: r0 = Map<String, dynamic>?()
    //     0xe7b9a4: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0xe7b9a8: ldur            x0, [fp, #-0x60]
    // 0xe7b9ac: cmp             w0, NULL
    // 0xe7b9b0: b.ne            #0xe7b9c0
    // 0xe7b9b4: r2 = _ConstMap len:3
    //     0xe7b9b4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37ce8] Map<String, Object>(3)
    //     0xe7b9b8: ldr             x2, [x2, #0xce8]
    // 0xe7b9bc: b               #0xe7b9c4
    // 0xe7b9c0: mov             x2, x0
    // 0xe7b9c4: ldur            x0, [fp, #-8]
    // 0xe7b9c8: r1 = Null
    //     0xe7b9c8: mov             x1, NULL
    // 0xe7b9cc: r0 = ZiarahCategory.fromMap()
    //     0xe7b9cc: bl              #0xe7c094  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahCategory::ZiarahCategory.fromMap
    // 0xe7b9d0: mov             x4, x0
    // 0xe7b9d4: ldur            x3, [fp, #-8]
    // 0xe7b9d8: stur            x4, [fp, #-0x60]
    // 0xe7b9dc: r0 = LoadClassIdInstr(r3)
    //     0xe7b9dc: ldur            x0, [x3, #-1]
    //     0xe7b9e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7b9e4: mov             x1, x3
    // 0xe7b9e8: r2 = "city"
    //     0xe7b9e8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37cf0] "city"
    //     0xe7b9ec: ldr             x2, [x2, #0xcf0]
    // 0xe7b9f0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7b9f0: sub             lr, x0, #0x114
    //     0xe7b9f4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7b9f8: blr             lr
    // 0xe7b9fc: mov             x3, x0
    // 0xe7ba00: r2 = Null
    //     0xe7ba00: mov             x2, NULL
    // 0xe7ba04: r1 = Null
    //     0xe7ba04: mov             x1, NULL
    // 0xe7ba08: stur            x3, [fp, #-0x68]
    // 0xe7ba0c: r8 = Map<String, dynamic>
    //     0xe7ba0c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7ba10: r3 = Null
    //     0xe7ba10: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a068] Null
    //     0xe7ba14: ldr             x3, [x3, #0x68]
    // 0xe7ba18: r0 = Map<String, dynamic>()
    //     0xe7ba18: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7ba1c: ldur            x2, [fp, #-0x68]
    // 0xe7ba20: r1 = Null
    //     0xe7ba20: mov             x1, NULL
    // 0xe7ba24: r0 = ZiarahPlace.fromMap()
    //     0xe7ba24: bl              #0xe7bf14  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::ZiarahPlace.fromMap
    // 0xe7ba28: mov             x4, x0
    // 0xe7ba2c: ldur            x3, [fp, #-8]
    // 0xe7ba30: stur            x4, [fp, #-0x68]
    // 0xe7ba34: r0 = LoadClassIdInstr(r3)
    //     0xe7ba34: ldur            x0, [x3, #-1]
    //     0xe7ba38: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ba3c: mov             x1, x3
    // 0xe7ba40: r2 = "born_city"
    //     0xe7ba40: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a078] "born_city"
    //     0xe7ba44: ldr             x2, [x2, #0x78]
    // 0xe7ba48: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7ba48: sub             lr, x0, #0x114
    //     0xe7ba4c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ba50: blr             lr
    // 0xe7ba54: cmp             w0, NULL
    // 0xe7ba58: b.ne            #0xe7ba64
    // 0xe7ba5c: r4 = Null
    //     0xe7ba5c: mov             x4, NULL
    // 0xe7ba60: b               #0xe7bab8
    // 0xe7ba64: ldur            x3, [fp, #-8]
    // 0xe7ba68: r0 = LoadClassIdInstr(r3)
    //     0xe7ba68: ldur            x0, [x3, #-1]
    //     0xe7ba6c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7ba70: mov             x1, x3
    // 0xe7ba74: r2 = "born_city"
    //     0xe7ba74: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a078] "born_city"
    //     0xe7ba78: ldr             x2, [x2, #0x78]
    // 0xe7ba7c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7ba7c: sub             lr, x0, #0x114
    //     0xe7ba80: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ba84: blr             lr
    // 0xe7ba88: mov             x3, x0
    // 0xe7ba8c: r2 = Null
    //     0xe7ba8c: mov             x2, NULL
    // 0xe7ba90: r1 = Null
    //     0xe7ba90: mov             x1, NULL
    // 0xe7ba94: stur            x3, [fp, #-0x70]
    // 0xe7ba98: r8 = Map<String, dynamic>
    //     0xe7ba98: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7ba9c: r3 = Null
    //     0xe7ba9c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a080] Null
    //     0xe7baa0: ldr             x3, [x3, #0x80]
    // 0xe7baa4: r0 = Map<String, dynamic>()
    //     0xe7baa4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7baa8: ldur            x2, [fp, #-0x70]
    // 0xe7baac: r1 = Null
    //     0xe7baac: mov             x1, NULL
    // 0xe7bab0: r0 = ZiarahPlace.fromMap()
    //     0xe7bab0: bl              #0xe7bf14  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::ZiarahPlace.fromMap
    // 0xe7bab4: mov             x4, x0
    // 0xe7bab8: ldur            x3, [fp, #-8]
    // 0xe7babc: stur            x4, [fp, #-0x70]
    // 0xe7bac0: r0 = LoadClassIdInstr(r3)
    //     0xe7bac0: ldur            x0, [x3, #-1]
    //     0xe7bac4: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bac8: mov             x1, x3
    // 0xe7bacc: r2 = "die_city"
    //     0xe7bacc: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a090] "die_city"
    //     0xe7bad0: ldr             x2, [x2, #0x90]
    // 0xe7bad4: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bad4: sub             lr, x0, #0x114
    //     0xe7bad8: ldr             lr, [x21, lr, lsl #3]
    //     0xe7badc: blr             lr
    // 0xe7bae0: cmp             w0, NULL
    // 0xe7bae4: b.ne            #0xe7baf0
    // 0xe7bae8: r4 = Null
    //     0xe7bae8: mov             x4, NULL
    // 0xe7baec: b               #0xe7bb44
    // 0xe7baf0: ldur            x3, [fp, #-8]
    // 0xe7baf4: r0 = LoadClassIdInstr(r3)
    //     0xe7baf4: ldur            x0, [x3, #-1]
    //     0xe7baf8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bafc: mov             x1, x3
    // 0xe7bb00: r2 = "die_city"
    //     0xe7bb00: add             x2, PP, #0x4a, lsl #12  ; [pp+0x4a090] "die_city"
    //     0xe7bb04: ldr             x2, [x2, #0x90]
    // 0xe7bb08: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bb08: sub             lr, x0, #0x114
    //     0xe7bb0c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bb10: blr             lr
    // 0xe7bb14: mov             x3, x0
    // 0xe7bb18: r2 = Null
    //     0xe7bb18: mov             x2, NULL
    // 0xe7bb1c: r1 = Null
    //     0xe7bb1c: mov             x1, NULL
    // 0xe7bb20: stur            x3, [fp, #-0x78]
    // 0xe7bb24: r8 = Map<String, dynamic>
    //     0xe7bb24: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7bb28: r3 = Null
    //     0xe7bb28: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a098] Null
    //     0xe7bb2c: ldr             x3, [x3, #0x98]
    // 0xe7bb30: r0 = Map<String, dynamic>()
    //     0xe7bb30: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7bb34: ldur            x2, [fp, #-0x78]
    // 0xe7bb38: r1 = Null
    //     0xe7bb38: mov             x1, NULL
    // 0xe7bb3c: r0 = ZiarahPlace.fromMap()
    //     0xe7bb3c: bl              #0xe7bf14  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::ZiarahPlace.fromMap
    // 0xe7bb40: mov             x4, x0
    // 0xe7bb44: ldur            x3, [fp, #-8]
    // 0xe7bb48: stur            x4, [fp, #-0x78]
    // 0xe7bb4c: r0 = LoadClassIdInstr(r3)
    //     0xe7bb4c: ldur            x0, [x3, #-1]
    //     0xe7bb50: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bb54: mov             x1, x3
    // 0xe7bb58: r2 = "longitude"
    //     0xe7bb58: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c478] "longitude"
    //     0xe7bb5c: ldr             x2, [x2, #0x478]
    // 0xe7bb60: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bb60: sub             lr, x0, #0x114
    //     0xe7bb64: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bb68: blr             lr
    // 0xe7bb6c: mov             x3, x0
    // 0xe7bb70: r2 = Null
    //     0xe7bb70: mov             x2, NULL
    // 0xe7bb74: r1 = Null
    //     0xe7bb74: mov             x1, NULL
    // 0xe7bb78: stur            x3, [fp, #-0x80]
    // 0xe7bb7c: r4 = 60
    //     0xe7bb7c: movz            x4, #0x3c
    // 0xe7bb80: branchIfSmi(r0, 0xe7bb8c)
    //     0xe7bb80: tbz             w0, #0, #0xe7bb8c
    // 0xe7bb84: r4 = LoadClassIdInstr(r0)
    //     0xe7bb84: ldur            x4, [x0, #-1]
    //     0xe7bb88: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bb8c: sub             x4, x4, #0x5e
    // 0xe7bb90: cmp             x4, #1
    // 0xe7bb94: b.ls            #0xe7bba8
    // 0xe7bb98: r8 = String
    //     0xe7bb98: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7bb9c: r3 = Null
    //     0xe7bb9c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a0a8] Null
    //     0xe7bba0: ldr             x3, [x3, #0xa8]
    // 0xe7bba4: r0 = String()
    //     0xe7bba4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7bba8: ldur            x1, [fp, #-0x80]
    // 0xe7bbac: r0 = _parse()
    //     0xe7bbac: bl              #0x61caa4  ; [dart:core] double::_parse
    // 0xe7bbb0: ldur            x3, [fp, #-8]
    // 0xe7bbb4: r0 = LoadClassIdInstr(r3)
    //     0xe7bbb4: ldur            x0, [x3, #-1]
    //     0xe7bbb8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bbbc: mov             x1, x3
    // 0xe7bbc0: r2 = "latitude"
    //     0xe7bbc0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c470] "latitude"
    //     0xe7bbc4: ldr             x2, [x2, #0x470]
    // 0xe7bbc8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bbc8: sub             lr, x0, #0x114
    //     0xe7bbcc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bbd0: blr             lr
    // 0xe7bbd4: mov             x3, x0
    // 0xe7bbd8: r2 = Null
    //     0xe7bbd8: mov             x2, NULL
    // 0xe7bbdc: r1 = Null
    //     0xe7bbdc: mov             x1, NULL
    // 0xe7bbe0: stur            x3, [fp, #-0x80]
    // 0xe7bbe4: r4 = 60
    //     0xe7bbe4: movz            x4, #0x3c
    // 0xe7bbe8: branchIfSmi(r0, 0xe7bbf4)
    //     0xe7bbe8: tbz             w0, #0, #0xe7bbf4
    // 0xe7bbec: r4 = LoadClassIdInstr(r0)
    //     0xe7bbec: ldur            x4, [x0, #-1]
    //     0xe7bbf0: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bbf4: sub             x4, x4, #0x5e
    // 0xe7bbf8: cmp             x4, #1
    // 0xe7bbfc: b.ls            #0xe7bc10
    // 0xe7bc00: r8 = String
    //     0xe7bc00: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7bc04: r3 = Null
    //     0xe7bc04: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a0b8] Null
    //     0xe7bc08: ldr             x3, [x3, #0xb8]
    // 0xe7bc0c: r0 = String()
    //     0xe7bc0c: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7bc10: ldur            x1, [fp, #-0x80]
    // 0xe7bc14: r0 = _parse()
    //     0xe7bc14: bl              #0x61caa4  ; [dart:core] double::_parse
    // 0xe7bc18: ldur            x3, [fp, #-8]
    // 0xe7bc1c: r0 = LoadClassIdInstr(r3)
    //     0xe7bc1c: ldur            x0, [x3, #-1]
    //     0xe7bc20: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bc24: mov             x1, x3
    // 0xe7bc28: r2 = "articles"
    //     0xe7bc28: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8b8] "articles"
    //     0xe7bc2c: ldr             x2, [x2, #0x8b8]
    // 0xe7bc30: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bc30: sub             lr, x0, #0x114
    //     0xe7bc34: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bc38: blr             lr
    // 0xe7bc3c: mov             x3, x0
    // 0xe7bc40: r2 = Null
    //     0xe7bc40: mov             x2, NULL
    // 0xe7bc44: r1 = Null
    //     0xe7bc44: mov             x1, NULL
    // 0xe7bc48: stur            x3, [fp, #-0x80]
    // 0xe7bc4c: r4 = 60
    //     0xe7bc4c: movz            x4, #0x3c
    // 0xe7bc50: branchIfSmi(r0, 0xe7bc5c)
    //     0xe7bc50: tbz             w0, #0, #0xe7bc5c
    // 0xe7bc54: r4 = LoadClassIdInstr(r0)
    //     0xe7bc54: ldur            x4, [x0, #-1]
    //     0xe7bc58: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bc5c: sub             x4, x4, #0x5a
    // 0xe7bc60: cmp             x4, #2
    // 0xe7bc64: b.ls            #0xe7bc7c
    // 0xe7bc68: r8 = List?
    //     0xe7bc68: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xe7bc6c: ldr             x8, [x8, #0x140]
    // 0xe7bc70: r3 = Null
    //     0xe7bc70: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a0c8] Null
    //     0xe7bc74: ldr             x3, [x3, #0xc8]
    // 0xe7bc78: r0 = List?()
    //     0xe7bc78: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xe7bc7c: ldur            x0, [fp, #-0x80]
    // 0xe7bc80: cmp             w0, NULL
    // 0xe7bc84: b.ne            #0xe7bc9c
    // 0xe7bc88: r1 = Null
    //     0xe7bc88: mov             x1, NULL
    // 0xe7bc8c: r2 = 0
    //     0xe7bc8c: movz            x2, #0
    // 0xe7bc90: r0 = _GrowableList()
    //     0xe7bc90: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7bc94: mov             x3, x0
    // 0xe7bc98: b               #0xe7bca0
    // 0xe7bc9c: mov             x3, x0
    // 0xe7bca0: ldur            x0, [fp, #-8]
    // 0xe7bca4: stur            x3, [fp, #-0x80]
    // 0xe7bca8: r1 = Function '<anonymous closure>': static.
    //     0xe7bca8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a0d8] AnonymousClosure: static (0xe7c2d8), in [package:nuonline/app/data/models/ziarah.dart] ZiarahDetail::ZiarahDetail.fromMap (0xe7b394)
    //     0xe7bcac: ldr             x1, [x1, #0xd8]
    // 0xe7bcb0: r2 = Null
    //     0xe7bcb0: mov             x2, NULL
    // 0xe7bcb4: r0 = AllocateClosure()
    //     0xe7bcb4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7bcb8: mov             x1, x0
    // 0xe7bcbc: ldur            x0, [fp, #-0x80]
    // 0xe7bcc0: r2 = LoadClassIdInstr(r0)
    //     0xe7bcc0: ldur            x2, [x0, #-1]
    //     0xe7bcc4: ubfx            x2, x2, #0xc, #0x14
    // 0xe7bcc8: r16 = <RelatedContent<int>>
    //     0xe7bcc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e808] TypeArguments: <RelatedContent<int>>
    //     0xe7bccc: ldr             x16, [x16, #0x808]
    // 0xe7bcd0: stp             x0, x16, [SP, #8]
    // 0xe7bcd4: str             x1, [SP]
    // 0xe7bcd8: mov             x0, x2
    // 0xe7bcdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7bcdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7bce0: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7bce0: movz            x17, #0xf28c
    //     0xe7bce4: add             lr, x0, x17
    //     0xe7bce8: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bcec: blr             lr
    // 0xe7bcf0: r1 = LoadClassIdInstr(r0)
    //     0xe7bcf0: ldur            x1, [x0, #-1]
    //     0xe7bcf4: ubfx            x1, x1, #0xc, #0x14
    // 0xe7bcf8: mov             x16, x0
    // 0xe7bcfc: mov             x0, x1
    // 0xe7bd00: mov             x1, x16
    // 0xe7bd04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7bd04: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7bd08: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7bd08: movz            x17, #0xd889
    //     0xe7bd0c: add             lr, x0, x17
    //     0xe7bd10: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bd14: blr             lr
    // 0xe7bd18: mov             x3, x0
    // 0xe7bd1c: ldur            x1, [fp, #-8]
    // 0xe7bd20: stur            x3, [fp, #-0x80]
    // 0xe7bd24: r0 = LoadClassIdInstr(r1)
    //     0xe7bd24: ldur            x0, [x1, #-1]
    //     0xe7bd28: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bd2c: r2 = "videos"
    //     0xe7bd2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e8d8] "videos"
    //     0xe7bd30: ldr             x2, [x2, #0x8d8]
    // 0xe7bd34: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bd34: sub             lr, x0, #0x114
    //     0xe7bd38: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bd3c: blr             lr
    // 0xe7bd40: mov             x3, x0
    // 0xe7bd44: r2 = Null
    //     0xe7bd44: mov             x2, NULL
    // 0xe7bd48: r1 = Null
    //     0xe7bd48: mov             x1, NULL
    // 0xe7bd4c: stur            x3, [fp, #-8]
    // 0xe7bd50: r4 = 60
    //     0xe7bd50: movz            x4, #0x3c
    // 0xe7bd54: branchIfSmi(r0, 0xe7bd60)
    //     0xe7bd54: tbz             w0, #0, #0xe7bd60
    // 0xe7bd58: r4 = LoadClassIdInstr(r0)
    //     0xe7bd58: ldur            x4, [x0, #-1]
    //     0xe7bd5c: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bd60: sub             x4, x4, #0x5a
    // 0xe7bd64: cmp             x4, #2
    // 0xe7bd68: b.ls            #0xe7bd80
    // 0xe7bd6c: r8 = List?
    //     0xe7bd6c: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xe7bd70: ldr             x8, [x8, #0x140]
    // 0xe7bd74: r3 = Null
    //     0xe7bd74: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a0e0] Null
    //     0xe7bd78: ldr             x3, [x3, #0xe0]
    // 0xe7bd7c: r0 = List?()
    //     0xe7bd7c: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xe7bd80: ldur            x0, [fp, #-8]
    // 0xe7bd84: cmp             w0, NULL
    // 0xe7bd88: b.ne            #0xe7bda0
    // 0xe7bd8c: r1 = Null
    //     0xe7bd8c: mov             x1, NULL
    // 0xe7bd90: r2 = 0
    //     0xe7bd90: movz            x2, #0
    // 0xe7bd94: r0 = _GrowableList()
    //     0xe7bd94: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7bd98: mov             x23, x0
    // 0xe7bd9c: b               #0xe7bda4
    // 0xe7bda0: mov             x23, x0
    // 0xe7bda4: ldur            x20, [fp, #-0x10]
    // 0xe7bda8: ldur            x19, [fp, #-0x18]
    // 0xe7bdac: ldur            x14, [fp, #-0x20]
    // 0xe7bdb0: ldur            x13, [fp, #-0x28]
    // 0xe7bdb4: ldur            x12, [fp, #-0x30]
    // 0xe7bdb8: ldur            x11, [fp, #-0x50]
    // 0xe7bdbc: ldur            x10, [fp, #-0x58]
    // 0xe7bdc0: ldur            x9, [fp, #-0x38]
    // 0xe7bdc4: ldur            x8, [fp, #-0x40]
    // 0xe7bdc8: ldur            x7, [fp, #-0x48]
    // 0xe7bdcc: ldur            x6, [fp, #-0x60]
    // 0xe7bdd0: ldur            x5, [fp, #-0x68]
    // 0xe7bdd4: ldur            x4, [fp, #-0x70]
    // 0xe7bdd8: ldur            x3, [fp, #-0x78]
    // 0xe7bddc: ldur            x0, [fp, #-0x80]
    // 0xe7bde0: stur            x23, [fp, #-8]
    // 0xe7bde4: r1 = Function '<anonymous closure>': static.
    //     0xe7bde4: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a0f0] AnonymousClosure: static (0xe7c280), in [package:nuonline/app/data/models/ziarah.dart] ZiarahDetail::ZiarahDetail.fromMap (0xe7b394)
    //     0xe7bde8: ldr             x1, [x1, #0xf0]
    // 0xe7bdec: r2 = Null
    //     0xe7bdec: mov             x2, NULL
    // 0xe7bdf0: r0 = AllocateClosure()
    //     0xe7bdf0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7bdf4: mov             x1, x0
    // 0xe7bdf8: ldur            x0, [fp, #-8]
    // 0xe7bdfc: r2 = LoadClassIdInstr(r0)
    //     0xe7bdfc: ldur            x2, [x0, #-1]
    //     0xe7be00: ubfx            x2, x2, #0xc, #0x14
    // 0xe7be04: r16 = <RelatedContent<String>>
    //     0xe7be04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e810] TypeArguments: <RelatedContent<String>>
    //     0xe7be08: ldr             x16, [x16, #0x810]
    // 0xe7be0c: stp             x0, x16, [SP, #8]
    // 0xe7be10: str             x1, [SP]
    // 0xe7be14: mov             x0, x2
    // 0xe7be18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7be18: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7be1c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7be1c: movz            x17, #0xf28c
    //     0xe7be20: add             lr, x0, x17
    //     0xe7be24: ldr             lr, [x21, lr, lsl #3]
    //     0xe7be28: blr             lr
    // 0xe7be2c: r1 = LoadClassIdInstr(r0)
    //     0xe7be2c: ldur            x1, [x0, #-1]
    //     0xe7be30: ubfx            x1, x1, #0xc, #0x14
    // 0xe7be34: mov             x16, x0
    // 0xe7be38: mov             x0, x1
    // 0xe7be3c: mov             x1, x16
    // 0xe7be40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7be40: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7be44: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7be44: movz            x17, #0xd889
    //     0xe7be48: add             lr, x0, x17
    //     0xe7be4c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7be50: blr             lr
    // 0xe7be54: mov             x1, x0
    // 0xe7be58: ldur            x0, [fp, #-0x10]
    // 0xe7be5c: stur            x1, [fp, #-8]
    // 0xe7be60: r2 = LoadInt32Instr(r0)
    //     0xe7be60: sbfx            x2, x0, #1, #0x1f
    //     0xe7be64: tbz             w0, #0, #0xe7be6c
    //     0xe7be68: ldur            x2, [x0, #7]
    // 0xe7be6c: stur            x2, [fp, #-0x88]
    // 0xe7be70: r0 = ZiarahDetail()
    //     0xe7be70: bl              #0xe7bf08  ; AllocateZiarahDetailStub -> ZiarahDetail (size=0x4c)
    // 0xe7be74: ldur            x1, [fp, #-0x88]
    // 0xe7be78: StoreField: r0->field_7 = r1
    //     0xe7be78: stur            x1, [x0, #7]
    // 0xe7be7c: ldur            x1, [fp, #-0x18]
    // 0xe7be80: StoreField: r0->field_f = r1
    //     0xe7be80: stur            w1, [x0, #0xf]
    // 0xe7be84: ldur            x1, [fp, #-0x20]
    // 0xe7be88: StoreField: r0->field_13 = r1
    //     0xe7be88: stur            w1, [x0, #0x13]
    // 0xe7be8c: ldur            x1, [fp, #-0x28]
    // 0xe7be90: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7be90: stur            w1, [x0, #0x17]
    // 0xe7be94: ldur            x1, [fp, #-0x30]
    // 0xe7be98: StoreField: r0->field_1b = r1
    //     0xe7be98: stur            w1, [x0, #0x1b]
    // 0xe7be9c: ldur            x1, [fp, #-0x50]
    // 0xe7bea0: StoreField: r0->field_1f = r1
    //     0xe7bea0: stur            w1, [x0, #0x1f]
    // 0xe7bea4: ldur            x1, [fp, #-0x58]
    // 0xe7bea8: StoreField: r0->field_23 = r1
    //     0xe7bea8: stur            w1, [x0, #0x23]
    // 0xe7beac: ldur            x1, [fp, #-0x38]
    // 0xe7beb0: StoreField: r0->field_27 = r1
    //     0xe7beb0: stur            w1, [x0, #0x27]
    // 0xe7beb4: ldur            x1, [fp, #-0x40]
    // 0xe7beb8: StoreField: r0->field_2b = r1
    //     0xe7beb8: stur            w1, [x0, #0x2b]
    // 0xe7bebc: ldur            x1, [fp, #-0x48]
    // 0xe7bec0: StoreField: r0->field_2f = r1
    //     0xe7bec0: stur            w1, [x0, #0x2f]
    // 0xe7bec4: ldur            x1, [fp, #-0x60]
    // 0xe7bec8: StoreField: r0->field_33 = r1
    //     0xe7bec8: stur            w1, [x0, #0x33]
    // 0xe7becc: ldur            x1, [fp, #-0x68]
    // 0xe7bed0: StoreField: r0->field_37 = r1
    //     0xe7bed0: stur            w1, [x0, #0x37]
    // 0xe7bed4: ldur            x1, [fp, #-0x70]
    // 0xe7bed8: StoreField: r0->field_3b = r1
    //     0xe7bed8: stur            w1, [x0, #0x3b]
    // 0xe7bedc: ldur            x1, [fp, #-0x78]
    // 0xe7bee0: StoreField: r0->field_3f = r1
    //     0xe7bee0: stur            w1, [x0, #0x3f]
    // 0xe7bee4: ldur            x1, [fp, #-0x80]
    // 0xe7bee8: StoreField: r0->field_43 = r1
    //     0xe7bee8: stur            w1, [x0, #0x43]
    // 0xe7beec: ldur            x1, [fp, #-8]
    // 0xe7bef0: StoreField: r0->field_47 = r1
    //     0xe7bef0: stur            w1, [x0, #0x47]
    // 0xe7bef4: LeaveFrame
    //     0xe7bef4: mov             SP, fp
    //     0xe7bef8: ldp             fp, lr, [SP], #0x10
    // 0xe7befc: ret
    //     0xe7befc: ret             
    // 0xe7bf00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7bf00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7bf04: b               #0xe7b3b4
  }
  [closure] static RelatedContent<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7c280, size: 0x58
    // 0xe7c280: EnterFrame
    //     0xe7c280: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c284: mov             fp, SP
    // 0xe7c288: CheckStackOverflow
    //     0xe7c288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c28c: cmp             SP, x16
    //     0xe7c290: b.ls            #0xe7c2d0
    // 0xe7c294: ldr             x0, [fp, #0x10]
    // 0xe7c298: r2 = Null
    //     0xe7c298: mov             x2, NULL
    // 0xe7c29c: r1 = Null
    //     0xe7c29c: mov             x1, NULL
    // 0xe7c2a0: r8 = Map<String, dynamic>
    //     0xe7c2a0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7c2a4: r3 = Null
    //     0xe7c2a4: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a0f8] Null
    //     0xe7c2a8: ldr             x3, [x3, #0xf8]
    // 0xe7c2ac: r0 = Map<String, dynamic>()
    //     0xe7c2ac: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7c2b0: ldr             x2, [fp, #0x10]
    // 0xe7c2b4: r1 = <String>
    //     0xe7c2b4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe7c2b8: r3 = "youtube_id"
    //     0xe7c2b8: add             x3, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0xe7c2bc: ldr             x3, [x3, #0x3f0]
    // 0xe7c2c0: r0 = RelatedContent.fromMap()
    //     0xe7c2c0: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0xe7c2c4: LeaveFrame
    //     0xe7c2c4: mov             SP, fp
    //     0xe7c2c8: ldp             fp, lr, [SP], #0x10
    // 0xe7c2cc: ret
    //     0xe7c2cc: ret             
    // 0xe7c2d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c2d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c2d4: b               #0xe7c294
  }
  [closure] static RelatedContent<int> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7c2d8, size: 0x58
    // 0xe7c2d8: EnterFrame
    //     0xe7c2d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c2dc: mov             fp, SP
    // 0xe7c2e0: CheckStackOverflow
    //     0xe7c2e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c2e4: cmp             SP, x16
    //     0xe7c2e8: b.ls            #0xe7c328
    // 0xe7c2ec: ldr             x0, [fp, #0x10]
    // 0xe7c2f0: r2 = Null
    //     0xe7c2f0: mov             x2, NULL
    // 0xe7c2f4: r1 = Null
    //     0xe7c2f4: mov             x1, NULL
    // 0xe7c2f8: r8 = Map<String, dynamic>
    //     0xe7c2f8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7c2fc: r3 = Null
    //     0xe7c2fc: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a108] Null
    //     0xe7c300: ldr             x3, [x3, #0x108]
    // 0xe7c304: r0 = Map<String, dynamic>()
    //     0xe7c304: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7c308: ldr             x2, [fp, #0x10]
    // 0xe7c30c: r1 = <int>
    //     0xe7c30c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xe7c310: r3 = "article_id"
    //     0xe7c310: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e958] "article_id"
    //     0xe7c314: ldr             x3, [x3, #0x958]
    // 0xe7c318: r0 = RelatedContent.fromMap()
    //     0xe7c318: bl              #0x72c9bc  ; [package:nuonline/app/data/models/related_content.dart] RelatedContent::RelatedContent.fromMap
    // 0xe7c31c: LeaveFrame
    //     0xe7c31c: mov             SP, fp
    //     0xe7c320: ldp             fp, lr, [SP], #0x10
    // 0xe7c324: ret
    //     0xe7c324: ret             
    // 0xe7c328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c32c: b               #0xe7c2ec
  }
}

// class id: 1112, size: 0x14, field offset: 0x8
class FigureDate extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc32a18, size: 0x184
    // 0xc32a18: EnterFrame
    //     0xc32a18: stp             fp, lr, [SP, #-0x10]!
    //     0xc32a1c: mov             fp, SP
    // 0xc32a20: AllocStack(0x30)
    //     0xc32a20: sub             SP, SP, #0x30
    // 0xc32a24: CheckStackOverflow
    //     0xc32a24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc32a28: cmp             SP, x16
    //     0xc32a2c: b.ls            #0xc32b94
    // 0xc32a30: ldr             x0, [fp, #0x10]
    // 0xc32a34: LoadField: r1 = r0->field_f
    //     0xc32a34: ldur            w1, [x0, #0xf]
    // 0xc32a38: DecompressPointer r1
    //     0xc32a38: add             x1, x1, HEAP, lsl #32
    // 0xc32a3c: stur            x1, [fp, #-0x18]
    // 0xc32a40: cmp             w1, NULL
    // 0xc32a44: b.eq            #0xc32b38
    // 0xc32a48: LoadField: r2 = r0->field_b
    //     0xc32a48: ldur            w2, [x0, #0xb]
    // 0xc32a4c: DecompressPointer r2
    //     0xc32a4c: add             x2, x2, HEAP, lsl #32
    // 0xc32a50: stur            x2, [fp, #-0x10]
    // 0xc32a54: cmp             w2, NULL
    // 0xc32a58: b.eq            #0xc32b30
    // 0xc32a5c: LoadField: r3 = r0->field_7
    //     0xc32a5c: ldur            w3, [x0, #7]
    // 0xc32a60: DecompressPointer r3
    //     0xc32a60: add             x3, x3, HEAP, lsl #32
    // 0xc32a64: cmp             w3, NULL
    // 0xc32a68: b.eq            #0xc32b28
    // 0xc32a6c: r0 = LoadInt32Instr(r3)
    //     0xc32a6c: sbfx            x0, x3, #1, #0x1f
    //     0xc32a70: tbz             w3, #0, #0xc32a78
    //     0xc32a74: ldur            x0, [x3, #7]
    // 0xc32a78: stur            x0, [fp, #-8]
    // 0xc32a7c: r0 = DateTime()
    //     0xc32a7c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xc32a80: stur            x0, [fp, #-0x20]
    // 0xc32a84: ldur            x16, [fp, #-0x10]
    // 0xc32a88: ldur            lr, [fp, #-0x18]
    // 0xc32a8c: stp             lr, x16, [SP]
    // 0xc32a90: mov             x1, x0
    // 0xc32a94: ldur            x2, [fp, #-8]
    // 0xc32a98: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xc32a98: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xc32a9c: ldr             x4, [x4, #0xe00]
    // 0xc32aa0: r0 = DateTime()
    //     0xc32aa0: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0xc32aa4: ldur            x1, [fp, #-0x20]
    // 0xc32aa8: r0 = DateTimeExtensions.humanize()
    //     0xc32aa8: bl              #0xb58638  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.humanize
    // 0xc32aac: mov             x1, x0
    // 0xc32ab0: r2 = "Minggu"
    //     0xc32ab0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e190] "Minggu"
    //     0xc32ab4: ldr             x2, [x2, #0x190]
    // 0xc32ab8: r3 = "Ahad"
    //     0xc32ab8: add             x3, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xc32abc: ldr             x3, [x3, #0x128]
    // 0xc32ac0: r0 = replaceAll()
    //     0xc32ac0: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xc32ac4: r1 = Null
    //     0xc32ac4: mov             x1, NULL
    // 0xc32ac8: r2 = 4
    //     0xc32ac8: movz            x2, #0x4
    // 0xc32acc: stur            x0, [fp, #-0x10]
    // 0xc32ad0: r0 = AllocateArray()
    //     0xc32ad0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc32ad4: mov             x2, x0
    // 0xc32ad8: ldur            x0, [fp, #-0x10]
    // 0xc32adc: stur            x2, [fp, #-0x18]
    // 0xc32ae0: StoreField: r2->field_f = r0
    //     0xc32ae0: stur            w0, [x2, #0xf]
    // 0xc32ae4: r16 = "M"
    //     0xc32ae4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b808] "M"
    //     0xc32ae8: ldr             x16, [x16, #0x808]
    // 0xc32aec: StoreField: r2->field_13 = r16
    //     0xc32aec: stur            w16, [x2, #0x13]
    // 0xc32af0: r1 = <String>
    //     0xc32af0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc32af4: r0 = AllocateGrowableArray()
    //     0xc32af4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc32af8: mov             x1, x0
    // 0xc32afc: ldur            x0, [fp, #-0x18]
    // 0xc32b00: StoreField: r1->field_f = r0
    //     0xc32b00: stur            w0, [x1, #0xf]
    // 0xc32b04: r2 = 4
    //     0xc32b04: movz            x2, #0x4
    // 0xc32b08: StoreField: r1->field_b = r2
    //     0xc32b08: stur            w2, [x1, #0xb]
    // 0xc32b0c: r16 = " "
    //     0xc32b0c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc32b10: str             x16, [SP]
    // 0xc32b14: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc32b14: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc32b18: r0 = join()
    //     0xc32b18: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xc32b1c: LeaveFrame
    //     0xc32b1c: mov             SP, fp
    //     0xc32b20: ldp             fp, lr, [SP], #0x10
    // 0xc32b24: ret
    //     0xc32b24: ret             
    // 0xc32b28: r2 = 4
    //     0xc32b28: movz            x2, #0x4
    // 0xc32b2c: b               #0xc32b3c
    // 0xc32b30: r2 = 4
    //     0xc32b30: movz            x2, #0x4
    // 0xc32b34: b               #0xc32b3c
    // 0xc32b38: r2 = 4
    //     0xc32b38: movz            x2, #0x4
    // 0xc32b3c: LoadField: r3 = r0->field_7
    //     0xc32b3c: ldur            w3, [x0, #7]
    // 0xc32b40: DecompressPointer r3
    //     0xc32b40: add             x3, x3, HEAP, lsl #32
    // 0xc32b44: stur            x3, [fp, #-0x10]
    // 0xc32b48: cmp             w3, NULL
    // 0xc32b4c: b.eq            #0xc32b84
    // 0xc32b50: r1 = Null
    //     0xc32b50: mov             x1, NULL
    // 0xc32b54: r0 = AllocateArray()
    //     0xc32b54: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc32b58: mov             x1, x0
    // 0xc32b5c: ldur            x0, [fp, #-0x10]
    // 0xc32b60: StoreField: r1->field_f = r0
    //     0xc32b60: stur            w0, [x1, #0xf]
    // 0xc32b64: r16 = " M"
    //     0xc32b64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ef80] " M"
    //     0xc32b68: ldr             x16, [x16, #0xf80]
    // 0xc32b6c: StoreField: r1->field_13 = r16
    //     0xc32b6c: stur            w16, [x1, #0x13]
    // 0xc32b70: str             x1, [SP]
    // 0xc32b74: r0 = _interpolate()
    //     0xc32b74: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc32b78: LeaveFrame
    //     0xc32b78: mov             SP, fp
    //     0xc32b7c: ldp             fp, lr, [SP], #0x10
    // 0xc32b80: ret
    //     0xc32b80: ret             
    // 0xc32b84: r0 = "-"
    //     0xc32b84: ldr             x0, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc32b88: LeaveFrame
    //     0xc32b88: mov             SP, fp
    //     0xc32b8c: ldp             fp, lr, [SP], #0x10
    // 0xc32b90: ret
    //     0xc32b90: ret             
    // 0xc32b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc32b94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc32b98: b               #0xc32a30
  }
}

// class id: 1113, size: 0x28, field offset: 0x8
class Ziarah extends Object {

  get _ distanceKM(/* No info */) {
    // ** addr: 0xb6ec4c, size: 0xcc
    // 0xb6ec4c: EnterFrame
    //     0xb6ec4c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6ec50: mov             fp, SP
    // 0xb6ec54: AllocStack(0x18)
    //     0xb6ec54: sub             SP, SP, #0x18
    // 0xb6ec58: CheckStackOverflow
    //     0xb6ec58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6ec5c: cmp             SP, x16
    //     0xb6ec60: b.ls            #0xb6ecfc
    // 0xb6ec64: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xb6ec64: ldur            d0, [x1, #0x17]
    // 0xb6ec68: r1 = inline_Allocate_Double()
    //     0xb6ec68: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xb6ec6c: add             x1, x1, #0x10
    //     0xb6ec70: cmp             x0, x1
    //     0xb6ec74: b.ls            #0xb6ed04
    //     0xb6ec78: str             x1, [THR, #0x50]  ; THR::top
    //     0xb6ec7c: sub             x1, x1, #0xf
    //     0xb6ec80: movz            x0, #0xe15c
    //     0xb6ec84: movk            x0, #0x3, lsl #16
    //     0xb6ec88: stur            x0, [x1, #-1]
    // 0xb6ec8c: StoreField: r1->field_7 = d0
    //     0xb6ec8c: stur            d0, [x1, #7]
    // 0xb6ec90: r2 = 1
    //     0xb6ec90: movz            x2, #0x1
    // 0xb6ec94: r0 = toStringAsFixed()
    //     0xb6ec94: bl              #0xebc43c  ; [dart:core] _Double::toStringAsFixed
    // 0xb6ec98: r1 = Null
    //     0xb6ec98: mov             x1, NULL
    // 0xb6ec9c: r2 = 4
    //     0xb6ec9c: movz            x2, #0x4
    // 0xb6eca0: stur            x0, [fp, #-8]
    // 0xb6eca4: r0 = AllocateArray()
    //     0xb6eca4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb6eca8: mov             x2, x0
    // 0xb6ecac: ldur            x0, [fp, #-8]
    // 0xb6ecb0: stur            x2, [fp, #-0x10]
    // 0xb6ecb4: StoreField: r2->field_f = r0
    //     0xb6ecb4: stur            w0, [x2, #0xf]
    // 0xb6ecb8: r16 = "KM"
    //     0xb6ecb8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28b50] "KM"
    //     0xb6ecbc: ldr             x16, [x16, #0xb50]
    // 0xb6ecc0: StoreField: r2->field_13 = r16
    //     0xb6ecc0: stur            w16, [x2, #0x13]
    // 0xb6ecc4: r1 = <String>
    //     0xb6ecc4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb6ecc8: r0 = AllocateGrowableArray()
    //     0xb6ecc8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb6eccc: mov             x1, x0
    // 0xb6ecd0: ldur            x0, [fp, #-0x10]
    // 0xb6ecd4: StoreField: r1->field_f = r0
    //     0xb6ecd4: stur            w0, [x1, #0xf]
    // 0xb6ecd8: r0 = 4
    //     0xb6ecd8: movz            x0, #0x4
    // 0xb6ecdc: StoreField: r1->field_b = r0
    //     0xb6ecdc: stur            w0, [x1, #0xb]
    // 0xb6ece0: r16 = " "
    //     0xb6ece0: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb6ece4: str             x16, [SP]
    // 0xb6ece8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb6ece8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb6ecec: r0 = join()
    //     0xb6ecec: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb6ecf0: LeaveFrame
    //     0xb6ecf0: mov             SP, fp
    //     0xb6ecf4: ldp             fp, lr, [SP], #0x10
    // 0xb6ecf8: ret
    //     0xb6ecf8: ret             
    // 0xb6ecfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6ecfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6ed00: b               #0xb6ec64
    // 0xb6ed04: SaveReg d0
    //     0xb6ed04: str             q0, [SP, #-0x10]!
    // 0xb6ed08: r0 = AllocateDouble()
    //     0xb6ed08: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb6ed0c: mov             x1, x0
    // 0xb6ed10: RestoreReg d0
    //     0xb6ed10: ldr             q0, [SP], #0x10
    // 0xb6ed14: b               #0xb6ec8c
  }
  static _ fromResponse(/* No info */) {
    // ** addr: 0xe7cfbc, size: 0x188
    // 0xe7cfbc: EnterFrame
    //     0xe7cfbc: stp             fp, lr, [SP, #-0x10]!
    //     0xe7cfc0: mov             fp, SP
    // 0xe7cfc4: AllocStack(0x20)
    //     0xe7cfc4: sub             SP, SP, #0x20
    // 0xe7cfc8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe7cfc8: mov             x3, x1
    //     0xe7cfcc: stur            x1, [fp, #-8]
    // 0xe7cfd0: CheckStackOverflow
    //     0xe7cfd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7cfd4: cmp             SP, x16
    //     0xe7cfd8: b.ls            #0xe7d13c
    // 0xe7cfdc: mov             x0, x3
    // 0xe7cfe0: r2 = Null
    //     0xe7cfe0: mov             x2, NULL
    // 0xe7cfe4: r1 = Null
    //     0xe7cfe4: mov             x1, NULL
    // 0xe7cfe8: cmp             w0, NULL
    // 0xe7cfec: b.eq            #0xe7d090
    // 0xe7cff0: branchIfSmi(r0, 0xe7d090)
    //     0xe7cff0: tbz             w0, #0, #0xe7d090
    // 0xe7cff4: r3 = LoadClassIdInstr(r0)
    //     0xe7cff4: ldur            x3, [x0, #-1]
    //     0xe7cff8: ubfx            x3, x3, #0xc, #0x14
    // 0xe7cffc: r17 = 6718
    //     0xe7cffc: movz            x17, #0x1a3e
    // 0xe7d000: cmp             x3, x17
    // 0xe7d004: b.eq            #0xe7d098
    // 0xe7d008: sub             x3, x3, #0x5a
    // 0xe7d00c: cmp             x3, #2
    // 0xe7d010: b.ls            #0xe7d098
    // 0xe7d014: r4 = LoadClassIdInstr(r0)
    //     0xe7d014: ldur            x4, [x0, #-1]
    //     0xe7d018: ubfx            x4, x4, #0xc, #0x14
    // 0xe7d01c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xe7d020: ldr             x3, [x3, #0x18]
    // 0xe7d024: ldr             x3, [x3, x4, lsl #3]
    // 0xe7d028: LoadField: r3 = r3->field_2b
    //     0xe7d028: ldur            w3, [x3, #0x2b]
    // 0xe7d02c: DecompressPointer r3
    //     0xe7d02c: add             x3, x3, HEAP, lsl #32
    // 0xe7d030: cmp             w3, NULL
    // 0xe7d034: b.eq            #0xe7d090
    // 0xe7d038: LoadField: r3 = r3->field_f
    //     0xe7d038: ldur            w3, [x3, #0xf]
    // 0xe7d03c: lsr             x3, x3, #3
    // 0xe7d040: r17 = 6718
    //     0xe7d040: movz            x17, #0x1a3e
    // 0xe7d044: cmp             x3, x17
    // 0xe7d048: b.eq            #0xe7d098
    // 0xe7d04c: r3 = SubtypeTestCache
    //     0xe7d04c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c60] SubtypeTestCache
    //     0xe7d050: ldr             x3, [x3, #0xc60]
    // 0xe7d054: r30 = Subtype1TestCacheStub
    //     0xe7d054: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xe7d058: LoadField: r30 = r30->field_7
    //     0xe7d058: ldur            lr, [lr, #7]
    // 0xe7d05c: blr             lr
    // 0xe7d060: cmp             w7, NULL
    // 0xe7d064: b.eq            #0xe7d070
    // 0xe7d068: tbnz            w7, #4, #0xe7d090
    // 0xe7d06c: b               #0xe7d098
    // 0xe7d070: r8 = List
    //     0xe7d070: add             x8, PP, #0x37, lsl #12  ; [pp+0x37c68] Type: List
    //     0xe7d074: ldr             x8, [x8, #0xc68]
    // 0xe7d078: r3 = SubtypeTestCache
    //     0xe7d078: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c70] SubtypeTestCache
    //     0xe7d07c: ldr             x3, [x3, #0xc70]
    // 0xe7d080: r30 = InstanceOfStub
    //     0xe7d080: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xe7d084: LoadField: r30 = r30->field_7
    //     0xe7d084: ldur            lr, [lr, #7]
    // 0xe7d088: blr             lr
    // 0xe7d08c: b               #0xe7d09c
    // 0xe7d090: r0 = false
    //     0xe7d090: add             x0, NULL, #0x30  ; false
    // 0xe7d094: b               #0xe7d09c
    // 0xe7d098: r0 = true
    //     0xe7d098: add             x0, NULL, #0x20  ; true
    // 0xe7d09c: tbnz            w0, #4, #0xe7d120
    // 0xe7d0a0: ldur            x0, [fp, #-8]
    // 0xe7d0a4: r1 = Function '<anonymous closure>': static.
    //     0xe7d0a4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37c78] AnonymousClosure: static (0xe7d144), in [package:nuonline/app/data/models/ziarah.dart] Ziarah::fromResponse (0xe7cfbc)
    //     0xe7d0a8: ldr             x1, [x1, #0xc78]
    // 0xe7d0ac: r2 = Null
    //     0xe7d0ac: mov             x2, NULL
    // 0xe7d0b0: r0 = AllocateClosure()
    //     0xe7d0b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7d0b4: mov             x1, x0
    // 0xe7d0b8: ldur            x0, [fp, #-8]
    // 0xe7d0bc: r2 = LoadClassIdInstr(r0)
    //     0xe7d0bc: ldur            x2, [x0, #-1]
    //     0xe7d0c0: ubfx            x2, x2, #0xc, #0x14
    // 0xe7d0c4: r16 = <Ziarah>
    //     0xe7d0c4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bdb8] TypeArguments: <Ziarah>
    //     0xe7d0c8: ldr             x16, [x16, #0xdb8]
    // 0xe7d0cc: stp             x0, x16, [SP, #8]
    // 0xe7d0d0: str             x1, [SP]
    // 0xe7d0d4: mov             x0, x2
    // 0xe7d0d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7d0d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7d0dc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7d0dc: movz            x17, #0xf28c
    //     0xe7d0e0: add             lr, x0, x17
    //     0xe7d0e4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d0e8: blr             lr
    // 0xe7d0ec: r1 = LoadClassIdInstr(r0)
    //     0xe7d0ec: ldur            x1, [x0, #-1]
    //     0xe7d0f0: ubfx            x1, x1, #0xc, #0x14
    // 0xe7d0f4: mov             x16, x0
    // 0xe7d0f8: mov             x0, x1
    // 0xe7d0fc: mov             x1, x16
    // 0xe7d100: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7d100: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7d104: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7d104: movz            x17, #0xd889
    //     0xe7d108: add             lr, x0, x17
    //     0xe7d10c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d110: blr             lr
    // 0xe7d114: LeaveFrame
    //     0xe7d114: mov             SP, fp
    //     0xe7d118: ldp             fp, lr, [SP], #0x10
    // 0xe7d11c: ret
    //     0xe7d11c: ret             
    // 0xe7d120: r1 = <Ziarah>
    //     0xe7d120: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bdb8] TypeArguments: <Ziarah>
    //     0xe7d124: ldr             x1, [x1, #0xdb8]
    // 0xe7d128: r2 = 0
    //     0xe7d128: movz            x2, #0
    // 0xe7d12c: r0 = _GrowableList()
    //     0xe7d12c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7d130: LeaveFrame
    //     0xe7d130: mov             SP, fp
    //     0xe7d134: ldp             fp, lr, [SP], #0x10
    // 0xe7d138: ret
    //     0xe7d138: ret             
    // 0xe7d13c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d13c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d140: b               #0xe7cfdc
  }
  [closure] static Ziarah <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7d144, size: 0x50
    // 0xe7d144: EnterFrame
    //     0xe7d144: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d148: mov             fp, SP
    // 0xe7d14c: CheckStackOverflow
    //     0xe7d14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d150: cmp             SP, x16
    //     0xe7d154: b.ls            #0xe7d18c
    // 0xe7d158: ldr             x0, [fp, #0x10]
    // 0xe7d15c: r2 = Null
    //     0xe7d15c: mov             x2, NULL
    // 0xe7d160: r1 = Null
    //     0xe7d160: mov             x1, NULL
    // 0xe7d164: r8 = Map<String, dynamic>
    //     0xe7d164: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7d168: r3 = Null
    //     0xe7d168: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c80] Null
    //     0xe7d16c: ldr             x3, [x3, #0xc80]
    // 0xe7d170: r0 = Map<String, dynamic>()
    //     0xe7d170: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7d174: ldr             x2, [fp, #0x10]
    // 0xe7d178: r1 = Null
    //     0xe7d178: mov             x1, NULL
    // 0xe7d17c: r0 = Ziarah.fromMap()
    //     0xe7d17c: bl              #0xe7d194  ; [package:nuonline/app/data/models/ziarah.dart] Ziarah::Ziarah.fromMap
    // 0xe7d180: LeaveFrame
    //     0xe7d180: mov             SP, fp
    //     0xe7d184: ldp             fp, lr, [SP], #0x10
    // 0xe7d188: ret
    //     0xe7d188: ret             
    // 0xe7d18c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d18c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d190: b               #0xe7d158
  }
  factory _ Ziarah.fromMap(/* No info */) {
    // ** addr: 0xe7d194, size: 0x2d4
    // 0xe7d194: EnterFrame
    //     0xe7d194: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d198: mov             fp, SP
    // 0xe7d19c: AllocStack(0x38)
    //     0xe7d19c: sub             SP, SP, #0x38
    // 0xe7d1a0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe7d1a0: mov             x3, x2
    //     0xe7d1a4: stur            x2, [fp, #-8]
    // 0xe7d1a8: CheckStackOverflow
    //     0xe7d1a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d1ac: cmp             SP, x16
    //     0xe7d1b0: b.ls            #0xe7d460
    // 0xe7d1b4: r0 = LoadClassIdInstr(r3)
    //     0xe7d1b4: ldur            x0, [x3, #-1]
    //     0xe7d1b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d1bc: mov             x1, x3
    // 0xe7d1c0: r2 = "id"
    //     0xe7d1c0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7d1c4: ldr             x2, [x2, #0x740]
    // 0xe7d1c8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d1c8: sub             lr, x0, #0x114
    //     0xe7d1cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d1d0: blr             lr
    // 0xe7d1d4: mov             x3, x0
    // 0xe7d1d8: r2 = Null
    //     0xe7d1d8: mov             x2, NULL
    // 0xe7d1dc: r1 = Null
    //     0xe7d1dc: mov             x1, NULL
    // 0xe7d1e0: stur            x3, [fp, #-0x10]
    // 0xe7d1e4: branchIfSmi(r0, 0xe7d20c)
    //     0xe7d1e4: tbz             w0, #0, #0xe7d20c
    // 0xe7d1e8: r4 = LoadClassIdInstr(r0)
    //     0xe7d1e8: ldur            x4, [x0, #-1]
    //     0xe7d1ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe7d1f0: sub             x4, x4, #0x3c
    // 0xe7d1f4: cmp             x4, #1
    // 0xe7d1f8: b.ls            #0xe7d20c
    // 0xe7d1fc: r8 = int
    //     0xe7d1fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe7d200: r3 = Null
    //     0xe7d200: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c90] Null
    //     0xe7d204: ldr             x3, [x3, #0xc90]
    // 0xe7d208: r0 = int()
    //     0xe7d208: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe7d20c: ldur            x3, [fp, #-8]
    // 0xe7d210: r0 = LoadClassIdInstr(r3)
    //     0xe7d210: ldur            x0, [x3, #-1]
    //     0xe7d214: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d218: mov             x1, x3
    // 0xe7d21c: r2 = "name"
    //     0xe7d21c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7d220: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d220: sub             lr, x0, #0x114
    //     0xe7d224: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d228: blr             lr
    // 0xe7d22c: mov             x3, x0
    // 0xe7d230: r2 = Null
    //     0xe7d230: mov             x2, NULL
    // 0xe7d234: r1 = Null
    //     0xe7d234: mov             x1, NULL
    // 0xe7d238: stur            x3, [fp, #-0x18]
    // 0xe7d23c: r4 = 60
    //     0xe7d23c: movz            x4, #0x3c
    // 0xe7d240: branchIfSmi(r0, 0xe7d24c)
    //     0xe7d240: tbz             w0, #0, #0xe7d24c
    // 0xe7d244: r4 = LoadClassIdInstr(r0)
    //     0xe7d244: ldur            x4, [x0, #-1]
    //     0xe7d248: ubfx            x4, x4, #0xc, #0x14
    // 0xe7d24c: sub             x4, x4, #0x5e
    // 0xe7d250: cmp             x4, #1
    // 0xe7d254: b.ls            #0xe7d268
    // 0xe7d258: r8 = String
    //     0xe7d258: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7d25c: r3 = Null
    //     0xe7d25c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37ca0] Null
    //     0xe7d260: ldr             x3, [x3, #0xca0]
    // 0xe7d264: r0 = String()
    //     0xe7d264: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7d268: ldur            x3, [fp, #-8]
    // 0xe7d26c: r0 = LoadClassIdInstr(r3)
    //     0xe7d26c: ldur            x0, [x3, #-1]
    //     0xe7d270: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d274: mov             x1, x3
    // 0xe7d278: r2 = "image"
    //     0xe7d278: add             x2, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xe7d27c: ldr             x2, [x2, #0x520]
    // 0xe7d280: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d280: sub             lr, x0, #0x114
    //     0xe7d284: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d288: blr             lr
    // 0xe7d28c: mov             x3, x0
    // 0xe7d290: r2 = Null
    //     0xe7d290: mov             x2, NULL
    // 0xe7d294: r1 = Null
    //     0xe7d294: mov             x1, NULL
    // 0xe7d298: stur            x3, [fp, #-0x20]
    // 0xe7d29c: r8 = Map<String, dynamic>
    //     0xe7d29c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7d2a0: r3 = Null
    //     0xe7d2a0: add             x3, PP, #0x37, lsl #12  ; [pp+0x37cb0] Null
    //     0xe7d2a4: ldr             x3, [x3, #0xcb0]
    // 0xe7d2a8: r0 = Map<String, dynamic>()
    //     0xe7d2a8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7d2ac: ldur            x2, [fp, #-0x20]
    // 0xe7d2b0: r1 = Null
    //     0xe7d2b0: mov             x1, NULL
    // 0xe7d2b4: r0 = Image.fromMap()
    //     0xe7d2b4: bl              #0x72c630  ; [package:nuonline/app/data/models/image.dart] Image::Image.fromMap
    // 0xe7d2b8: mov             x4, x0
    // 0xe7d2bc: ldur            x3, [fp, #-8]
    // 0xe7d2c0: stur            x4, [fp, #-0x20]
    // 0xe7d2c4: r0 = LoadClassIdInstr(r3)
    //     0xe7d2c4: ldur            x0, [x3, #-1]
    //     0xe7d2c8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d2cc: mov             x1, x3
    // 0xe7d2d0: r2 = "distance"
    //     0xe7d2d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed58] "distance"
    //     0xe7d2d4: ldr             x2, [x2, #0xd58]
    // 0xe7d2d8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d2d8: sub             lr, x0, #0x114
    //     0xe7d2dc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d2e0: blr             lr
    // 0xe7d2e4: mov             x3, x0
    // 0xe7d2e8: r2 = Null
    //     0xe7d2e8: mov             x2, NULL
    // 0xe7d2ec: r1 = Null
    //     0xe7d2ec: mov             x1, NULL
    // 0xe7d2f0: stur            x3, [fp, #-0x28]
    // 0xe7d2f4: r4 = 60
    //     0xe7d2f4: movz            x4, #0x3c
    // 0xe7d2f8: branchIfSmi(r0, 0xe7d304)
    //     0xe7d2f8: tbz             w0, #0, #0xe7d304
    // 0xe7d2fc: r4 = LoadClassIdInstr(r0)
    //     0xe7d2fc: ldur            x4, [x0, #-1]
    //     0xe7d300: ubfx            x4, x4, #0xc, #0x14
    // 0xe7d304: cmp             x4, #0x3e
    // 0xe7d308: b.eq            #0xe7d31c
    // 0xe7d30c: r8 = double
    //     0xe7d30c: ldr             x8, [PP, #0x1388]  ; [pp+0x1388] Type: double
    // 0xe7d310: r3 = Null
    //     0xe7d310: add             x3, PP, #0x37, lsl #12  ; [pp+0x37cc0] Null
    //     0xe7d314: ldr             x3, [x3, #0xcc0]
    // 0xe7d318: r0 = double()
    //     0xe7d318: bl              #0xed4460  ; IsType_double_Stub
    // 0xe7d31c: ldur            x0, [fp, #-0x28]
    // 0xe7d320: LoadField: d0 = r0->field_7
    //     0xe7d320: ldur            d0, [x0, #7]
    // 0xe7d324: d1 = 1.609344
    //     0xe7d324: add             x17, PP, #0x37, lsl #12  ; [pp+0x37cd0] IMM: double(1.609344) from 0x3ff9bfdf7e8038a0
    //     0xe7d328: ldr             d1, [x17, #0xcd0]
    // 0xe7d32c: fmul            d2, d0, d1
    // 0xe7d330: ldur            x3, [fp, #-8]
    // 0xe7d334: stur            d2, [fp, #-0x38]
    // 0xe7d338: r0 = LoadClassIdInstr(r3)
    //     0xe7d338: ldur            x0, [x3, #-1]
    //     0xe7d33c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d340: mov             x1, x3
    // 0xe7d344: r2 = "category"
    //     0xe7d344: add             x2, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0xe7d348: ldr             x2, [x2, #0x960]
    // 0xe7d34c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d34c: sub             lr, x0, #0x114
    //     0xe7d350: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d354: blr             lr
    // 0xe7d358: mov             x3, x0
    // 0xe7d35c: r2 = Null
    //     0xe7d35c: mov             x2, NULL
    // 0xe7d360: r1 = Null
    //     0xe7d360: mov             x1, NULL
    // 0xe7d364: stur            x3, [fp, #-0x28]
    // 0xe7d368: r8 = Map<String, dynamic>?
    //     0xe7d368: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0xe7d36c: r3 = Null
    //     0xe7d36c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37cd8] Null
    //     0xe7d370: ldr             x3, [x3, #0xcd8]
    // 0xe7d374: r0 = Map<String, dynamic>?()
    //     0xe7d374: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0xe7d378: ldur            x0, [fp, #-0x28]
    // 0xe7d37c: cmp             w0, NULL
    // 0xe7d380: b.ne            #0xe7d390
    // 0xe7d384: r2 = _ConstMap len:3
    //     0xe7d384: add             x2, PP, #0x37, lsl #12  ; [pp+0x37ce8] Map<String, Object>(3)
    //     0xe7d388: ldr             x2, [x2, #0xce8]
    // 0xe7d38c: b               #0xe7d394
    // 0xe7d390: mov             x2, x0
    // 0xe7d394: ldur            x0, [fp, #-8]
    // 0xe7d398: ldur            x5, [fp, #-0x10]
    // 0xe7d39c: ldur            x4, [fp, #-0x18]
    // 0xe7d3a0: ldur            x3, [fp, #-0x20]
    // 0xe7d3a4: ldur            d0, [fp, #-0x38]
    // 0xe7d3a8: r1 = Null
    //     0xe7d3a8: mov             x1, NULL
    // 0xe7d3ac: r0 = ZiarahCategory.fromMap()
    //     0xe7d3ac: bl              #0xe7c094  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahCategory::ZiarahCategory.fromMap
    // 0xe7d3b0: mov             x3, x0
    // 0xe7d3b4: ldur            x1, [fp, #-8]
    // 0xe7d3b8: stur            x3, [fp, #-0x28]
    // 0xe7d3bc: r0 = LoadClassIdInstr(r1)
    //     0xe7d3bc: ldur            x0, [x1, #-1]
    //     0xe7d3c0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7d3c4: r2 = "city"
    //     0xe7d3c4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37cf0] "city"
    //     0xe7d3c8: ldr             x2, [x2, #0xcf0]
    // 0xe7d3cc: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7d3cc: sub             lr, x0, #0x114
    //     0xe7d3d0: ldr             lr, [x21, lr, lsl #3]
    //     0xe7d3d4: blr             lr
    // 0xe7d3d8: mov             x3, x0
    // 0xe7d3dc: r2 = Null
    //     0xe7d3dc: mov             x2, NULL
    // 0xe7d3e0: r1 = Null
    //     0xe7d3e0: mov             x1, NULL
    // 0xe7d3e4: stur            x3, [fp, #-8]
    // 0xe7d3e8: r8 = Map<String, dynamic>
    //     0xe7d3e8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7d3ec: r3 = Null
    //     0xe7d3ec: add             x3, PP, #0x37, lsl #12  ; [pp+0x37cf8] Null
    //     0xe7d3f0: ldr             x3, [x3, #0xcf8]
    // 0xe7d3f4: r0 = Map<String, dynamic>()
    //     0xe7d3f4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7d3f8: ldur            x2, [fp, #-8]
    // 0xe7d3fc: r1 = Null
    //     0xe7d3fc: mov             x1, NULL
    // 0xe7d400: r0 = ZiarahPlace.fromMap()
    //     0xe7d400: bl              #0xe7bf14  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahPlace::ZiarahPlace.fromMap
    // 0xe7d404: mov             x1, x0
    // 0xe7d408: ldur            x0, [fp, #-0x10]
    // 0xe7d40c: stur            x1, [fp, #-8]
    // 0xe7d410: r2 = LoadInt32Instr(r0)
    //     0xe7d410: sbfx            x2, x0, #1, #0x1f
    //     0xe7d414: tbz             w0, #0, #0xe7d41c
    //     0xe7d418: ldur            x2, [x0, #7]
    // 0xe7d41c: stur            x2, [fp, #-0x30]
    // 0xe7d420: r0 = Ziarah()
    //     0xe7d420: bl              #0xe7d468  ; AllocateZiarahStub -> Ziarah (size=0x28)
    // 0xe7d424: ldur            x1, [fp, #-0x30]
    // 0xe7d428: StoreField: r0->field_7 = r1
    //     0xe7d428: stur            x1, [x0, #7]
    // 0xe7d42c: ldur            x1, [fp, #-0x18]
    // 0xe7d430: StoreField: r0->field_f = r1
    //     0xe7d430: stur            w1, [x0, #0xf]
    // 0xe7d434: ldur            x1, [fp, #-0x20]
    // 0xe7d438: StoreField: r0->field_13 = r1
    //     0xe7d438: stur            w1, [x0, #0x13]
    // 0xe7d43c: ldur            d0, [fp, #-0x38]
    // 0xe7d440: ArrayStore: r0[0] = d0  ; List_8
    //     0xe7d440: stur            d0, [x0, #0x17]
    // 0xe7d444: ldur            x1, [fp, #-0x28]
    // 0xe7d448: StoreField: r0->field_1f = r1
    //     0xe7d448: stur            w1, [x0, #0x1f]
    // 0xe7d44c: ldur            x1, [fp, #-8]
    // 0xe7d450: StoreField: r0->field_23 = r1
    //     0xe7d450: stur            w1, [x0, #0x23]
    // 0xe7d454: LeaveFrame
    //     0xe7d454: mov             SP, fp
    //     0xe7d458: ldp             fp, lr, [SP], #0x10
    // 0xe7d45c: ret
    //     0xe7d45c: ret             
    // 0xe7d460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d460: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d464: b               #0xe7d1b4
  }
}

// class id: 1114, size: 0x18, field offset: 0x8
class ZiarahPlace extends Object {

  get _ full(/* No info */) {
    // ** addr: 0xb6ce00, size: 0x180
    // 0xb6ce00: EnterFrame
    //     0xb6ce00: stp             fp, lr, [SP, #-0x10]!
    //     0xb6ce04: mov             fp, SP
    // 0xb6ce08: AllocStack(0x38)
    //     0xb6ce08: sub             SP, SP, #0x38
    // 0xb6ce0c: SetupParameters(ZiarahPlace this /* r1 => r0, fp-0x8 */)
    //     0xb6ce0c: mov             x0, x1
    //     0xb6ce10: stur            x1, [fp, #-8]
    // 0xb6ce14: CheckStackOverflow
    //     0xb6ce14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6ce18: cmp             SP, x16
    //     0xb6ce1c: b.ls            #0xb6cf78
    // 0xb6ce20: LoadField: r1 = r0->field_f
    //     0xb6ce20: ldur            w1, [x0, #0xf]
    // 0xb6ce24: DecompressPointer r1
    //     0xb6ce24: add             x1, x1, HEAP, lsl #32
    // 0xb6ce28: r0 = StringExtension.toKab()
    //     0xb6ce28: bl              #0xb6c940  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.toKab
    // 0xb6ce2c: r1 = Null
    //     0xb6ce2c: mov             x1, NULL
    // 0xb6ce30: r2 = 2
    //     0xb6ce30: movz            x2, #0x2
    // 0xb6ce34: stur            x0, [fp, #-0x10]
    // 0xb6ce38: r0 = AllocateArray()
    //     0xb6ce38: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb6ce3c: mov             x2, x0
    // 0xb6ce40: ldur            x0, [fp, #-0x10]
    // 0xb6ce44: stur            x2, [fp, #-0x18]
    // 0xb6ce48: StoreField: r2->field_f = r0
    //     0xb6ce48: stur            w0, [x2, #0xf]
    // 0xb6ce4c: r1 = <String>
    //     0xb6ce4c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb6ce50: r0 = AllocateGrowableArray()
    //     0xb6ce50: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb6ce54: mov             x1, x0
    // 0xb6ce58: ldur            x0, [fp, #-0x18]
    // 0xb6ce5c: stur            x1, [fp, #-0x20]
    // 0xb6ce60: StoreField: r1->field_f = r0
    //     0xb6ce60: stur            w0, [x1, #0xf]
    // 0xb6ce64: r0 = 2
    //     0xb6ce64: movz            x0, #0x2
    // 0xb6ce68: StoreField: r1->field_b = r0
    //     0xb6ce68: stur            w0, [x1, #0xb]
    // 0xb6ce6c: ldur            x0, [fp, #-8]
    // 0xb6ce70: LoadField: r2 = r0->field_13
    //     0xb6ce70: ldur            w2, [x0, #0x13]
    // 0xb6ce74: DecompressPointer r2
    //     0xb6ce74: add             x2, x2, HEAP, lsl #32
    // 0xb6ce78: stur            x2, [fp, #-0x10]
    // 0xb6ce7c: r0 = LoadClassIdInstr(r2)
    //     0xb6ce7c: ldur            x0, [x2, #-1]
    //     0xb6ce80: ubfx            x0, x0, #0xc, #0x14
    // 0xb6ce84: r16 = "LUAR NEGERI"
    //     0xb6ce84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ed68] "LUAR NEGERI"
    //     0xb6ce88: ldr             x16, [x16, #0xd68]
    // 0xb6ce8c: stp             x16, x2, [SP]
    // 0xb6ce90: mov             lr, x0
    // 0xb6ce94: ldr             lr, [x21, lr, lsl #3]
    // 0xb6ce98: blr             lr
    // 0xb6ce9c: tbz             w0, #4, #0xb6cf1c
    // 0xb6cea0: ldur            x0, [fp, #-0x20]
    // 0xb6cea4: LoadField: r1 = r0->field_b
    //     0xb6cea4: ldur            w1, [x0, #0xb]
    // 0xb6cea8: LoadField: r2 = r0->field_f
    //     0xb6cea8: ldur            w2, [x0, #0xf]
    // 0xb6ceac: DecompressPointer r2
    //     0xb6ceac: add             x2, x2, HEAP, lsl #32
    // 0xb6ceb0: LoadField: r3 = r2->field_b
    //     0xb6ceb0: ldur            w3, [x2, #0xb]
    // 0xb6ceb4: r2 = LoadInt32Instr(r1)
    //     0xb6ceb4: sbfx            x2, x1, #1, #0x1f
    // 0xb6ceb8: stur            x2, [fp, #-0x28]
    // 0xb6cebc: r1 = LoadInt32Instr(r3)
    //     0xb6cebc: sbfx            x1, x3, #1, #0x1f
    // 0xb6cec0: cmp             x2, x1
    // 0xb6cec4: b.ne            #0xb6ced0
    // 0xb6cec8: mov             x1, x0
    // 0xb6cecc: r0 = _growToNextCapacity()
    //     0xb6cecc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6ced0: ldur            x2, [fp, #-0x20]
    // 0xb6ced4: ldur            x3, [fp, #-0x28]
    // 0xb6ced8: add             x0, x3, #1
    // 0xb6cedc: lsl             x1, x0, #1
    // 0xb6cee0: StoreField: r2->field_b = r1
    //     0xb6cee0: stur            w1, [x2, #0xb]
    // 0xb6cee4: LoadField: r1 = r2->field_f
    //     0xb6cee4: ldur            w1, [x2, #0xf]
    // 0xb6cee8: DecompressPointer r1
    //     0xb6cee8: add             x1, x1, HEAP, lsl #32
    // 0xb6ceec: ldur            x0, [fp, #-0x10]
    // 0xb6cef0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6cef0: add             x25, x1, x3, lsl #2
    //     0xb6cef4: add             x25, x25, #0xf
    //     0xb6cef8: str             w0, [x25]
    //     0xb6cefc: tbz             w0, #0, #0xb6cf18
    //     0xb6cf00: ldurb           w16, [x1, #-1]
    //     0xb6cf04: ldurb           w17, [x0, #-1]
    //     0xb6cf08: and             x16, x17, x16, lsr #2
    //     0xb6cf0c: tst             x16, HEAP, lsr #32
    //     0xb6cf10: b.eq            #0xb6cf18
    //     0xb6cf14: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb6cf18: b               #0xb6cf20
    // 0xb6cf1c: ldur            x2, [fp, #-0x20]
    // 0xb6cf20: r16 = ", "
    //     0xb6cf20: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xb6cf24: str             x16, [SP]
    // 0xb6cf28: mov             x1, x2
    // 0xb6cf2c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb6cf2c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb6cf30: r0 = join()
    //     0xb6cf30: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xb6cf34: mov             x1, x0
    // 0xb6cf38: r0 = capitalize()
    //     0xb6cf38: bl              #0xb6c9f0  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::capitalize
    // 0xb6cf3c: mov             x1, x0
    // 0xb6cf40: r2 = "Dki "
    //     0xb6cf40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed70] "Dki "
    //     0xb6cf44: ldr             x2, [x2, #0xd70]
    // 0xb6cf48: r3 = "DKI "
    //     0xb6cf48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ed78] "DKI "
    //     0xb6cf4c: ldr             x3, [x3, #0xd78]
    // 0xb6cf50: r0 = replaceAll()
    //     0xb6cf50: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb6cf54: mov             x1, x0
    // 0xb6cf58: r2 = "Di "
    //     0xb6cf58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ed80] "Di "
    //     0xb6cf5c: ldr             x2, [x2, #0xd80]
    // 0xb6cf60: r3 = "DI "
    //     0xb6cf60: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ed88] "DI "
    //     0xb6cf64: ldr             x3, [x3, #0xd88]
    // 0xb6cf68: r0 = replaceAll()
    //     0xb6cf68: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb6cf6c: LeaveFrame
    //     0xb6cf6c: mov             SP, fp
    //     0xb6cf70: ldp             fp, lr, [SP], #0x10
    // 0xb6cf74: ret
    //     0xb6cf74: ret             
    // 0xb6cf78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6cf78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6cf7c: b               #0xb6ce20
  }
  factory _ ZiarahPlace.fromMap(/* No info */) {
    // ** addr: 0xe7bf14, size: 0x174
    // 0xe7bf14: EnterFrame
    //     0xe7bf14: stp             fp, lr, [SP, #-0x10]!
    //     0xe7bf18: mov             fp, SP
    // 0xe7bf1c: AllocStack(0x20)
    //     0xe7bf1c: sub             SP, SP, #0x20
    // 0xe7bf20: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe7bf20: mov             x3, x2
    //     0xe7bf24: stur            x2, [fp, #-8]
    // 0xe7bf28: CheckStackOverflow
    //     0xe7bf28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7bf2c: cmp             SP, x16
    //     0xe7bf30: b.ls            #0xe7c080
    // 0xe7bf34: r0 = LoadClassIdInstr(r3)
    //     0xe7bf34: ldur            x0, [x3, #-1]
    //     0xe7bf38: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bf3c: mov             x1, x3
    // 0xe7bf40: r2 = "id"
    //     0xe7bf40: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7bf44: ldr             x2, [x2, #0x740]
    // 0xe7bf48: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bf48: sub             lr, x0, #0x114
    //     0xe7bf4c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bf50: blr             lr
    // 0xe7bf54: mov             x3, x0
    // 0xe7bf58: r2 = Null
    //     0xe7bf58: mov             x2, NULL
    // 0xe7bf5c: r1 = Null
    //     0xe7bf5c: mov             x1, NULL
    // 0xe7bf60: stur            x3, [fp, #-0x10]
    // 0xe7bf64: branchIfSmi(r0, 0xe7bf8c)
    //     0xe7bf64: tbz             w0, #0, #0xe7bf8c
    // 0xe7bf68: r4 = LoadClassIdInstr(r0)
    //     0xe7bf68: ldur            x4, [x0, #-1]
    //     0xe7bf6c: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bf70: sub             x4, x4, #0x3c
    // 0xe7bf74: cmp             x4, #1
    // 0xe7bf78: b.ls            #0xe7bf8c
    // 0xe7bf7c: r8 = int
    //     0xe7bf7c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe7bf80: r3 = Null
    //     0xe7bf80: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d08] Null
    //     0xe7bf84: ldr             x3, [x3, #0xd08]
    // 0xe7bf88: r0 = int()
    //     0xe7bf88: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe7bf8c: ldur            x3, [fp, #-8]
    // 0xe7bf90: r0 = LoadClassIdInstr(r3)
    //     0xe7bf90: ldur            x0, [x3, #-1]
    //     0xe7bf94: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bf98: mov             x1, x3
    // 0xe7bf9c: r2 = "name"
    //     0xe7bf9c: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7bfa0: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bfa0: sub             lr, x0, #0x114
    //     0xe7bfa4: ldr             lr, [x21, lr, lsl #3]
    //     0xe7bfa8: blr             lr
    // 0xe7bfac: mov             x3, x0
    // 0xe7bfb0: r2 = Null
    //     0xe7bfb0: mov             x2, NULL
    // 0xe7bfb4: r1 = Null
    //     0xe7bfb4: mov             x1, NULL
    // 0xe7bfb8: stur            x3, [fp, #-0x18]
    // 0xe7bfbc: r4 = 60
    //     0xe7bfbc: movz            x4, #0x3c
    // 0xe7bfc0: branchIfSmi(r0, 0xe7bfcc)
    //     0xe7bfc0: tbz             w0, #0, #0xe7bfcc
    // 0xe7bfc4: r4 = LoadClassIdInstr(r0)
    //     0xe7bfc4: ldur            x4, [x0, #-1]
    //     0xe7bfc8: ubfx            x4, x4, #0xc, #0x14
    // 0xe7bfcc: sub             x4, x4, #0x5e
    // 0xe7bfd0: cmp             x4, #1
    // 0xe7bfd4: b.ls            #0xe7bfe8
    // 0xe7bfd8: r8 = String
    //     0xe7bfd8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7bfdc: r3 = Null
    //     0xe7bfdc: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d18] Null
    //     0xe7bfe0: ldr             x3, [x3, #0xd18]
    // 0xe7bfe4: r0 = String()
    //     0xe7bfe4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7bfe8: ldur            x1, [fp, #-8]
    // 0xe7bfec: r0 = LoadClassIdInstr(r1)
    //     0xe7bfec: ldur            x0, [x1, #-1]
    //     0xe7bff0: ubfx            x0, x0, #0xc, #0x14
    // 0xe7bff4: r2 = "province"
    //     0xe7bff4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37d28] "province"
    //     0xe7bff8: ldr             x2, [x2, #0xd28]
    // 0xe7bffc: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7bffc: sub             lr, x0, #0x114
    //     0xe7c000: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c004: blr             lr
    // 0xe7c008: mov             x3, x0
    // 0xe7c00c: r2 = Null
    //     0xe7c00c: mov             x2, NULL
    // 0xe7c010: r1 = Null
    //     0xe7c010: mov             x1, NULL
    // 0xe7c014: stur            x3, [fp, #-8]
    // 0xe7c018: r4 = 60
    //     0xe7c018: movz            x4, #0x3c
    // 0xe7c01c: branchIfSmi(r0, 0xe7c028)
    //     0xe7c01c: tbz             w0, #0, #0xe7c028
    // 0xe7c020: r4 = LoadClassIdInstr(r0)
    //     0xe7c020: ldur            x4, [x0, #-1]
    //     0xe7c024: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c028: sub             x4, x4, #0x5e
    // 0xe7c02c: cmp             x4, #1
    // 0xe7c030: b.ls            #0xe7c044
    // 0xe7c034: r8 = String
    //     0xe7c034: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7c038: r3 = Null
    //     0xe7c038: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d30] Null
    //     0xe7c03c: ldr             x3, [x3, #0xd30]
    // 0xe7c040: r0 = String()
    //     0xe7c040: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7c044: ldur            x0, [fp, #-0x10]
    // 0xe7c048: r1 = LoadInt32Instr(r0)
    //     0xe7c048: sbfx            x1, x0, #1, #0x1f
    //     0xe7c04c: tbz             w0, #0, #0xe7c054
    //     0xe7c050: ldur            x1, [x0, #7]
    // 0xe7c054: stur            x1, [fp, #-0x20]
    // 0xe7c058: r0 = ZiarahPlace()
    //     0xe7c058: bl              #0xe7c088  ; AllocateZiarahPlaceStub -> ZiarahPlace (size=0x18)
    // 0xe7c05c: ldur            x1, [fp, #-0x20]
    // 0xe7c060: StoreField: r0->field_7 = r1
    //     0xe7c060: stur            x1, [x0, #7]
    // 0xe7c064: ldur            x1, [fp, #-0x18]
    // 0xe7c068: StoreField: r0->field_f = r1
    //     0xe7c068: stur            w1, [x0, #0xf]
    // 0xe7c06c: ldur            x1, [fp, #-8]
    // 0xe7c070: StoreField: r0->field_13 = r1
    //     0xe7c070: stur            w1, [x0, #0x13]
    // 0xe7c074: LeaveFrame
    //     0xe7c074: mov             SP, fp
    //     0xe7c078: ldp             fp, lr, [SP], #0x10
    // 0xe7c07c: ret
    //     0xe7c07c: ret             
    // 0xe7c080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c084: b               #0xe7bf34
  }
}

// class id: 1115, size: 0x1c, field offset: 0x8
class ZiarahCategory extends Object {

  factory _ ZiarahCategory.fromMap(/* No info */) {
    // ** addr: 0xe7c094, size: 0x1d4
    // 0xe7c094: EnterFrame
    //     0xe7c094: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c098: mov             fp, SP
    // 0xe7c09c: AllocStack(0x28)
    //     0xe7c09c: sub             SP, SP, #0x28
    // 0xe7c0a0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xe7c0a0: mov             x3, x2
    //     0xe7c0a4: stur            x2, [fp, #-8]
    // 0xe7c0a8: CheckStackOverflow
    //     0xe7c0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c0ac: cmp             SP, x16
    //     0xe7c0b0: b.ls            #0xe7c260
    // 0xe7c0b4: r0 = LoadClassIdInstr(r3)
    //     0xe7c0b4: ldur            x0, [x3, #-1]
    //     0xe7c0b8: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c0bc: mov             x1, x3
    // 0xe7c0c0: r2 = "id"
    //     0xe7c0c0: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe7c0c4: ldr             x2, [x2, #0x740]
    // 0xe7c0c8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c0c8: sub             lr, x0, #0x114
    //     0xe7c0cc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c0d0: blr             lr
    // 0xe7c0d4: mov             x3, x0
    // 0xe7c0d8: r2 = Null
    //     0xe7c0d8: mov             x2, NULL
    // 0xe7c0dc: r1 = Null
    //     0xe7c0dc: mov             x1, NULL
    // 0xe7c0e0: stur            x3, [fp, #-0x10]
    // 0xe7c0e4: branchIfSmi(r0, 0xe7c10c)
    //     0xe7c0e4: tbz             w0, #0, #0xe7c10c
    // 0xe7c0e8: r4 = LoadClassIdInstr(r0)
    //     0xe7c0e8: ldur            x4, [x0, #-1]
    //     0xe7c0ec: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c0f0: sub             x4, x4, #0x3c
    // 0xe7c0f4: cmp             x4, #1
    // 0xe7c0f8: b.ls            #0xe7c10c
    // 0xe7c0fc: r8 = int
    //     0xe7c0fc: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe7c100: r3 = Null
    //     0xe7c100: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d40] Null
    //     0xe7c104: ldr             x3, [x3, #0xd40]
    // 0xe7c108: r0 = int()
    //     0xe7c108: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe7c10c: ldur            x3, [fp, #-8]
    // 0xe7c110: r0 = LoadClassIdInstr(r3)
    //     0xe7c110: ldur            x0, [x3, #-1]
    //     0xe7c114: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c118: mov             x1, x3
    // 0xe7c11c: r2 = "parent_id"
    //     0xe7c11c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19cf8] "parent_id"
    //     0xe7c120: ldr             x2, [x2, #0xcf8]
    // 0xe7c124: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c124: sub             lr, x0, #0x114
    //     0xe7c128: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c12c: blr             lr
    // 0xe7c130: mov             x3, x0
    // 0xe7c134: r2 = Null
    //     0xe7c134: mov             x2, NULL
    // 0xe7c138: r1 = Null
    //     0xe7c138: mov             x1, NULL
    // 0xe7c13c: stur            x3, [fp, #-0x18]
    // 0xe7c140: branchIfSmi(r0, 0xe7c168)
    //     0xe7c140: tbz             w0, #0, #0xe7c168
    // 0xe7c144: r4 = LoadClassIdInstr(r0)
    //     0xe7c144: ldur            x4, [x0, #-1]
    //     0xe7c148: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c14c: sub             x4, x4, #0x3c
    // 0xe7c150: cmp             x4, #1
    // 0xe7c154: b.ls            #0xe7c168
    // 0xe7c158: r8 = int
    //     0xe7c158: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xe7c15c: r3 = Null
    //     0xe7c15c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d50] Null
    //     0xe7c160: ldr             x3, [x3, #0xd50]
    // 0xe7c164: r0 = int()
    //     0xe7c164: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xe7c168: ldur            x3, [fp, #-8]
    // 0xe7c16c: r0 = LoadClassIdInstr(r3)
    //     0xe7c16c: ldur            x0, [x3, #-1]
    //     0xe7c170: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c174: mov             x1, x3
    // 0xe7c178: r2 = "name"
    //     0xe7c178: ldr             x2, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0xe7c17c: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c17c: sub             lr, x0, #0x114
    //     0xe7c180: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c184: blr             lr
    // 0xe7c188: mov             x3, x0
    // 0xe7c18c: r2 = Null
    //     0xe7c18c: mov             x2, NULL
    // 0xe7c190: r1 = Null
    //     0xe7c190: mov             x1, NULL
    // 0xe7c194: stur            x3, [fp, #-0x20]
    // 0xe7c198: r4 = 60
    //     0xe7c198: movz            x4, #0x3c
    // 0xe7c19c: branchIfSmi(r0, 0xe7c1a8)
    //     0xe7c19c: tbz             w0, #0, #0xe7c1a8
    // 0xe7c1a0: r4 = LoadClassIdInstr(r0)
    //     0xe7c1a0: ldur            x4, [x0, #-1]
    //     0xe7c1a4: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c1a8: sub             x4, x4, #0x5e
    // 0xe7c1ac: cmp             x4, #1
    // 0xe7c1b0: b.ls            #0xe7c1c4
    // 0xe7c1b4: r8 = String
    //     0xe7c1b4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xe7c1b8: r3 = Null
    //     0xe7c1b8: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d60] Null
    //     0xe7c1bc: ldr             x3, [x3, #0xd60]
    // 0xe7c1c0: r0 = String()
    //     0xe7c1c0: bl              #0xed43b0  ; IsType_String_Stub
    // 0xe7c1c4: ldur            x1, [fp, #-8]
    // 0xe7c1c8: r0 = LoadClassIdInstr(r1)
    //     0xe7c1c8: ldur            x0, [x1, #-1]
    //     0xe7c1cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe7c1d0: r2 = "slug"
    //     0xe7c1d0: add             x2, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xe7c1d4: ldr             x2, [x2, #0x9a8]
    // 0xe7c1d8: r0 = GDT[cid_x0 + -0x114]()
    //     0xe7c1d8: sub             lr, x0, #0x114
    //     0xe7c1dc: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c1e0: blr             lr
    // 0xe7c1e4: r2 = Null
    //     0xe7c1e4: mov             x2, NULL
    // 0xe7c1e8: r1 = Null
    //     0xe7c1e8: mov             x1, NULL
    // 0xe7c1ec: r4 = 60
    //     0xe7c1ec: movz            x4, #0x3c
    // 0xe7c1f0: branchIfSmi(r0, 0xe7c1fc)
    //     0xe7c1f0: tbz             w0, #0, #0xe7c1fc
    // 0xe7c1f4: r4 = LoadClassIdInstr(r0)
    //     0xe7c1f4: ldur            x4, [x0, #-1]
    //     0xe7c1f8: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c1fc: sub             x4, x4, #0x5e
    // 0xe7c200: cmp             x4, #1
    // 0xe7c204: b.ls            #0xe7c218
    // 0xe7c208: r8 = String?
    //     0xe7c208: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xe7c20c: r3 = Null
    //     0xe7c20c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37d70] Null
    //     0xe7c210: ldr             x3, [x3, #0xd70]
    // 0xe7c214: r0 = String?()
    //     0xe7c214: bl              #0x600324  ; IsType_String?_Stub
    // 0xe7c218: ldur            x0, [fp, #-0x10]
    // 0xe7c21c: r1 = LoadInt32Instr(r0)
    //     0xe7c21c: sbfx            x1, x0, #1, #0x1f
    //     0xe7c220: tbz             w0, #0, #0xe7c228
    //     0xe7c224: ldur            x1, [x0, #7]
    // 0xe7c228: stur            x1, [fp, #-0x28]
    // 0xe7c22c: r0 = ZiarahCategory()
    //     0xe7c22c: bl              #0xe7c268  ; AllocateZiarahCategoryStub -> ZiarahCategory (size=0x1c)
    // 0xe7c230: ldur            x1, [fp, #-0x28]
    // 0xe7c234: StoreField: r0->field_7 = r1
    //     0xe7c234: stur            x1, [x0, #7]
    // 0xe7c238: ldur            x1, [fp, #-0x18]
    // 0xe7c23c: r2 = LoadInt32Instr(r1)
    //     0xe7c23c: sbfx            x2, x1, #1, #0x1f
    //     0xe7c240: tbz             w1, #0, #0xe7c248
    //     0xe7c244: ldur            x2, [x1, #7]
    // 0xe7c248: StoreField: r0->field_f = r2
    //     0xe7c248: stur            x2, [x0, #0xf]
    // 0xe7c24c: ldur            x1, [fp, #-0x20]
    // 0xe7c250: ArrayStore: r0[0] = r1  ; List_4
    //     0xe7c250: stur            w1, [x0, #0x17]
    // 0xe7c254: LeaveFrame
    //     0xe7c254: mov             SP, fp
    //     0xe7c258: ldp             fp, lr, [SP], #0x10
    // 0xe7c25c: ret
    //     0xe7c25c: ret             
    // 0xe7c260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c264: b               #0xe7c0b4
  }
}
