// lib: , url: package:nuonline/app/data/repositories/article_repository.dart

// class id: 1050068, size: 0x8
class :: {
}

// class id: 1110, size: 0x14, field offset: 0x8
class ArticleRepository extends Object {

  _ findAllTag(/* No info */) async {
    // ** addr: 0x8a9914, size: 0xf0
    // 0x8a9914: EnterFrame
    //     0x8a9914: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9918: mov             fp, SP
    // 0x8a991c: AllocStack(0x80)
    //     0x8a991c: sub             SP, SP, #0x80
    // 0x8a9920: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x58 */)
    //     0x8a9920: stur            NULL, [fp, #-8]
    //     0x8a9924: stur            x1, [fp, #-0x58]
    // 0x8a9928: CheckStackOverflow
    //     0x8a9928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a992c: cmp             SP, x16
    //     0x8a9930: b.ls            #0x8a99fc
    // 0x8a9934: InitAsync() -> Future<ApiResult<List<Tag>>>
    //     0x8a9934: add             x0, PP, #0x40, lsl #12  ; [pp+0x409c8] TypeArguments: <ApiResult<List<Tag>>>
    //     0x8a9938: ldr             x0, [x0, #0x9c8]
    //     0x8a993c: bl              #0x661298  ; InitAsyncStub
    // 0x8a9940: ldur            x0, [fp, #-0x58]
    // 0x8a9944: LoadField: r1 = r0->field_7
    //     0x8a9944: ldur            w1, [x0, #7]
    // 0x8a9948: DecompressPointer r1
    //     0x8a9948: add             x1, x1, HEAP, lsl #32
    // 0x8a994c: stp             x1, NULL, [SP, #0x10]
    // 0x8a9950: r16 = "/articles/tags"
    //     0x8a9950: add             x16, PP, #0x40, lsl #12  ; [pp+0x409d0] "/articles/tags"
    //     0x8a9954: ldr             x16, [x16, #0x9d0]
    // 0x8a9958: r30 = _ConstMap len:0
    //     0x8a9958: add             lr, PP, #0x11, lsl #12  ; [pp+0x11250] Map<String, String?>(0)
    //     0x8a995c: ldr             lr, [lr, #0x250]
    // 0x8a9960: stp             lr, x16, [SP]
    // 0x8a9964: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8a9964: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8a9968: ldr             x4, [x4, #0x2f0]
    // 0x8a996c: r0 = get()
    //     0x8a996c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8a9970: mov             x1, x0
    // 0x8a9974: stur            x1, [fp, #-0x58]
    // 0x8a9978: r0 = Await()
    //     0x8a9978: bl              #0x661044  ; AwaitStub
    // 0x8a997c: stur            x0, [fp, #-0x58]
    // 0x8a9980: LoadField: r1 = r0->field_b
    //     0x8a9980: ldur            w1, [x0, #0xb]
    // 0x8a9984: DecompressPointer r1
    //     0x8a9984: add             x1, x1, HEAP, lsl #32
    // 0x8a9988: r0 = fromResponse()
    //     0x8a9988: bl              #0x8a9a04  ; [package:nuonline/app/data/models/tag.dart] Tag::fromResponse
    // 0x8a998c: mov             x3, x0
    // 0x8a9990: ldur            x0, [fp, #-0x58]
    // 0x8a9994: stur            x3, [fp, #-0x60]
    // 0x8a9998: LoadField: r2 = r0->field_1b
    //     0x8a9998: ldur            w2, [x0, #0x1b]
    // 0x8a999c: DecompressPointer r2
    //     0x8a999c: add             x2, x2, HEAP, lsl #32
    // 0x8a99a0: r1 = Null
    //     0x8a99a0: mov             x1, NULL
    // 0x8a99a4: r0 = Pagination.fromHeaders()
    //     0x8a99a4: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x8a99a8: r1 = <List<Tag>>
    //     0x8a99a8: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f0] TypeArguments: <List<Tag>>
    //     0x8a99ac: ldr             x1, [x1, #0x4f0]
    // 0x8a99b0: stur            x0, [fp, #-0x58]
    // 0x8a99b4: r0 = _$SuccessImpl()
    //     0x8a99b4: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8a99b8: mov             x1, x0
    // 0x8a99bc: ldur            x0, [fp, #-0x60]
    // 0x8a99c0: StoreField: r1->field_b = r0
    //     0x8a99c0: stur            w0, [x1, #0xb]
    // 0x8a99c4: ldur            x0, [fp, #-0x58]
    // 0x8a99c8: StoreField: r1->field_f = r0
    //     0x8a99c8: stur            w0, [x1, #0xf]
    // 0x8a99cc: mov             x0, x1
    // 0x8a99d0: r0 = ReturnAsyncNotFuture()
    //     0x8a99d0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a99d4: sub             SP, fp, #0x80
    // 0x8a99d8: mov             x1, x0
    // 0x8a99dc: r0 = getDioException()
    //     0x8a99dc: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8a99e0: r1 = <List<Tag>>
    //     0x8a99e0: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f0] TypeArguments: <List<Tag>>
    //     0x8a99e4: ldr             x1, [x1, #0x4f0]
    // 0x8a99e8: stur            x0, [fp, #-0x58]
    // 0x8a99ec: r0 = _$FailureImpl()
    //     0x8a99ec: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8a99f0: ldur            x1, [fp, #-0x58]
    // 0x8a99f4: StoreField: r0->field_b = r1
    //     0x8a99f4: stur            w1, [x0, #0xb]
    // 0x8a99f8: r0 = ReturnAsyncNotFuture()
    //     0x8a99f8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a99fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a99fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9a00: b               #0x8a9934
  }
  _ findAllFromBookmark(/* No info */) {
    // ** addr: 0x8aa108, size: 0x7c
    // 0x8aa108: EnterFrame
    //     0x8aa108: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa10c: mov             fp, SP
    // 0x8aa110: AllocStack(0x8)
    //     0x8aa110: sub             SP, SP, #8
    // 0x8aa114: CheckStackOverflow
    //     0x8aa114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa118: cmp             SP, x16
    //     0x8aa11c: b.ls            #0x8aa174
    // 0x8aa120: LoadField: r0 = r1->field_b
    //     0x8aa120: ldur            w0, [x1, #0xb]
    // 0x8aa124: DecompressPointer r0
    //     0x8aa124: add             x0, x0, HEAP, lsl #32
    // 0x8aa128: mov             x1, x0
    // 0x8aa12c: r0 = articlesBookmark()
    //     0x8aa12c: bl              #0x8aa184  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::articlesBookmark
    // 0x8aa130: mov             x1, x0
    // 0x8aa134: stur            x0, [fp, #-8]
    // 0x8aa138: r0 = checkOpen()
    //     0x8aa138: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aa13c: ldur            x0, [fp, #-8]
    // 0x8aa140: LoadField: r1 = r0->field_1b
    //     0x8aa140: ldur            w1, [x0, #0x1b]
    // 0x8aa144: DecompressPointer r1
    //     0x8aa144: add             x1, x1, HEAP, lsl #32
    // 0x8aa148: r16 = Sentinel
    //     0x8aa148: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aa14c: cmp             w1, w16
    // 0x8aa150: b.eq            #0x8aa17c
    // 0x8aa154: r0 = getValues()
    //     0x8aa154: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8aa158: LoadField: r1 = r0->field_7
    //     0x8aa158: ldur            w1, [x0, #7]
    // 0x8aa15c: DecompressPointer r1
    //     0x8aa15c: add             x1, x1, HEAP, lsl #32
    // 0x8aa160: mov             x2, x0
    // 0x8aa164: r0 = _GrowableList.of()
    //     0x8aa164: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8aa168: LeaveFrame
    //     0x8aa168: mov             SP, fp
    //     0x8aa16c: ldp             fp, lr, [SP], #0x10
    // 0x8aa170: ret
    //     0x8aa170: ret             
    // 0x8aa174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa178: b               #0x8aa120
    // 0x8aa17c: r9 = keystore
    //     0x8aa17c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8aa180: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aa180: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ counter(/* No info */) async {
    // ** addr: 0x8eb908, size: 0x114
    // 0x8eb908: EnterFrame
    //     0x8eb908: stp             fp, lr, [SP, #-0x10]!
    //     0x8eb90c: mov             fp, SP
    // 0x8eb910: AllocStack(0x40)
    //     0x8eb910: sub             SP, SP, #0x40
    // 0x8eb914: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8eb914: stur            NULL, [fp, #-8]
    //     0x8eb918: stur            x1, [fp, #-0x10]
    //     0x8eb91c: stur            x2, [fp, #-0x18]
    // 0x8eb920: CheckStackOverflow
    //     0x8eb920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb924: cmp             SP, x16
    //     0x8eb928: b.ls            #0x8eba14
    // 0x8eb92c: InitAsync() -> Future<void?>
    //     0x8eb92c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8eb930: bl              #0x661298  ; InitAsyncStub
    // 0x8eb934: ldur            x0, [fp, #-0x10]
    // 0x8eb938: LoadField: r3 = r0->field_7
    //     0x8eb938: ldur            w3, [x0, #7]
    // 0x8eb93c: DecompressPointer r3
    //     0x8eb93c: add             x3, x3, HEAP, lsl #32
    // 0x8eb940: stur            x3, [fp, #-0x20]
    // 0x8eb944: r1 = Null
    //     0x8eb944: mov             x1, NULL
    // 0x8eb948: r2 = 4
    //     0x8eb948: movz            x2, #0x4
    // 0x8eb94c: r0 = AllocateArray()
    //     0x8eb94c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8eb950: mov             x2, x0
    // 0x8eb954: stur            x2, [fp, #-0x10]
    // 0x8eb958: r16 = "id"
    //     0x8eb958: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8eb95c: ldr             x16, [x16, #0x740]
    // 0x8eb960: StoreField: r2->field_f = r16
    //     0x8eb960: stur            w16, [x2, #0xf]
    // 0x8eb964: ldur            x3, [fp, #-0x18]
    // 0x8eb968: r0 = BoxInt64Instr(r3)
    //     0x8eb968: sbfiz           x0, x3, #1, #0x1f
    //     0x8eb96c: cmp             x3, x0, asr #1
    //     0x8eb970: b.eq            #0x8eb97c
    //     0x8eb974: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eb978: stur            x3, [x0, #7]
    // 0x8eb97c: r1 = 60
    //     0x8eb97c: movz            x1, #0x3c
    // 0x8eb980: branchIfSmi(r0, 0x8eb98c)
    //     0x8eb980: tbz             w0, #0, #0x8eb98c
    // 0x8eb984: r1 = LoadClassIdInstr(r0)
    //     0x8eb984: ldur            x1, [x0, #-1]
    //     0x8eb988: ubfx            x1, x1, #0xc, #0x14
    // 0x8eb98c: str             x0, [SP]
    // 0x8eb990: mov             x0, x1
    // 0x8eb994: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8eb994: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8eb998: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8eb998: movz            x17, #0x2b03
    //     0x8eb99c: add             lr, x0, x17
    //     0x8eb9a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8eb9a4: blr             lr
    // 0x8eb9a8: ldur            x1, [fp, #-0x10]
    // 0x8eb9ac: ArrayStore: r1[1] = r0  ; List_4
    //     0x8eb9ac: add             x25, x1, #0x13
    //     0x8eb9b0: str             w0, [x25]
    //     0x8eb9b4: tbz             w0, #0, #0x8eb9d0
    //     0x8eb9b8: ldurb           w16, [x1, #-1]
    //     0x8eb9bc: ldurb           w17, [x0, #-1]
    //     0x8eb9c0: and             x16, x17, x16, lsr #2
    //     0x8eb9c4: tst             x16, HEAP, lsr #32
    //     0x8eb9c8: b.eq            #0x8eb9d0
    //     0x8eb9cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8eb9d0: r16 = <String, dynamic>
    //     0x8eb9d0: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8eb9d4: ldur            lr, [fp, #-0x10]
    // 0x8eb9d8: stp             lr, x16, [SP]
    // 0x8eb9dc: r0 = Map._fromLiteral()
    //     0x8eb9dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8eb9e0: ldur            x16, [fp, #-0x20]
    // 0x8eb9e4: stp             x16, NULL, [SP, #0x10]
    // 0x8eb9e8: r16 = "/articles/counter"
    //     0x8eb9e8: add             x16, PP, #0x40, lsl #12  ; [pp+0x40cf8] "/articles/counter"
    //     0x8eb9ec: ldr             x16, [x16, #0xcf8]
    // 0x8eb9f0: stp             x0, x16, [SP]
    // 0x8eb9f4: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8eb9f4: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8eb9f8: ldr             x4, [x4, #0x2f0]
    // 0x8eb9fc: r0 = post()
    //     0x8eb9fc: bl              #0x7eb5f0  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::post
    // 0x8eba00: mov             x1, x0
    // 0x8eba04: stur            x1, [fp, #-0x10]
    // 0x8eba08: r0 = Await()
    //     0x8eba08: bl              #0x661044  ; AwaitStub
    // 0x8eba0c: r0 = Null
    //     0x8eba0c: mov             x0, NULL
    // 0x8eba10: r0 = ReturnAsyncNotFuture()
    //     0x8eba10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8eba14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eba14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eba18: b               #0x8eb92c
  }
  _ findById(/* No info */) async {
    // ** addr: 0x8eba1c, size: 0x124
    // 0x8eba1c: EnterFrame
    //     0x8eba1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8eba20: mov             fp, SP
    // 0x8eba24: AllocStack(0x80)
    //     0x8eba24: sub             SP, SP, #0x80
    // 0x8eba28: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x8eba28: stur            NULL, [fp, #-8]
    //     0x8eba2c: stur            x1, [fp, #-0x58]
    //     0x8eba30: stur            x2, [fp, #-0x60]
    // 0x8eba34: CheckStackOverflow
    //     0x8eba34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eba38: cmp             SP, x16
    //     0x8eba3c: b.ls            #0x8ebb38
    // 0x8eba40: InitAsync() -> Future<ApiResult<ArticleDetail>>
    //     0x8eba40: add             x0, PP, #0x40, lsl #12  ; [pp+0x40d00] TypeArguments: <ApiResult<ArticleDetail>>
    //     0x8eba44: ldr             x0, [x0, #0xd00]
    //     0x8eba48: bl              #0x661298  ; InitAsyncStub
    // 0x8eba4c: ldur            x1, [fp, #-0x58]
    // 0x8eba50: ldur            x0, [fp, #-0x60]
    // 0x8eba54: LoadField: r3 = r1->field_7
    //     0x8eba54: ldur            w3, [x1, #7]
    // 0x8eba58: DecompressPointer r3
    //     0x8eba58: add             x3, x3, HEAP, lsl #32
    // 0x8eba5c: stur            x3, [fp, #-0x68]
    // 0x8eba60: r1 = Null
    //     0x8eba60: mov             x1, NULL
    // 0x8eba64: r2 = 4
    //     0x8eba64: movz            x2, #0x4
    // 0x8eba68: r0 = AllocateArray()
    //     0x8eba68: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8eba6c: mov             x2, x0
    // 0x8eba70: r16 = "/articles/"
    //     0x8eba70: add             x16, PP, #0x40, lsl #12  ; [pp+0x408d0] "/articles/"
    //     0x8eba74: ldr             x16, [x16, #0x8d0]
    // 0x8eba78: StoreField: r2->field_f = r16
    //     0x8eba78: stur            w16, [x2, #0xf]
    // 0x8eba7c: ldur            x3, [fp, #-0x60]
    // 0x8eba80: r0 = BoxInt64Instr(r3)
    //     0x8eba80: sbfiz           x0, x3, #1, #0x1f
    //     0x8eba84: cmp             x3, x0, asr #1
    //     0x8eba88: b.eq            #0x8eba94
    //     0x8eba8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eba90: stur            x3, [x0, #7]
    // 0x8eba94: StoreField: r2->field_13 = r0
    //     0x8eba94: stur            w0, [x2, #0x13]
    // 0x8eba98: str             x2, [SP]
    // 0x8eba9c: r0 = _interpolate()
    //     0x8eba9c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ebaa0: ldur            x16, [fp, #-0x68]
    // 0x8ebaa4: stp             x16, NULL, [SP, #8]
    // 0x8ebaa8: str             x0, [SP]
    // 0x8ebaac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ebaac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ebab0: r0 = get()
    //     0x8ebab0: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8ebab4: mov             x1, x0
    // 0x8ebab8: stur            x1, [fp, #-0x58]
    // 0x8ebabc: r0 = Await()
    //     0x8ebabc: bl              #0x661044  ; AwaitStub
    // 0x8ebac0: LoadField: r3 = r0->field_b
    //     0x8ebac0: ldur            w3, [x0, #0xb]
    // 0x8ebac4: DecompressPointer r3
    //     0x8ebac4: add             x3, x3, HEAP, lsl #32
    // 0x8ebac8: mov             x0, x3
    // 0x8ebacc: stur            x3, [fp, #-0x58]
    // 0x8ebad0: r2 = Null
    //     0x8ebad0: mov             x2, NULL
    // 0x8ebad4: r1 = Null
    //     0x8ebad4: mov             x1, NULL
    // 0x8ebad8: r8 = Map<String, dynamic>
    //     0x8ebad8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ebadc: r3 = Null
    //     0x8ebadc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d08] Null
    //     0x8ebae0: ldr             x3, [x3, #0xd08]
    // 0x8ebae4: r0 = Map<String, dynamic>()
    //     0x8ebae4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ebae8: ldur            x2, [fp, #-0x58]
    // 0x8ebaec: r1 = Null
    //     0x8ebaec: mov             x1, NULL
    // 0x8ebaf0: r0 = ArticleDetail.fromMap()
    //     0x8ebaf0: bl              #0x8ebb40  ; [package:nuonline/app/data/models/article.dart] ArticleDetail::ArticleDetail.fromMap
    // 0x8ebaf4: r1 = <ArticleDetail>
    //     0x8ebaf4: ldr             x1, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0x8ebaf8: stur            x0, [fp, #-0x58]
    // 0x8ebafc: r0 = _$SuccessImpl()
    //     0x8ebafc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8ebb00: mov             x1, x0
    // 0x8ebb04: ldur            x0, [fp, #-0x58]
    // 0x8ebb08: StoreField: r1->field_b = r0
    //     0x8ebb08: stur            w0, [x1, #0xb]
    // 0x8ebb0c: mov             x0, x1
    // 0x8ebb10: r0 = ReturnAsyncNotFuture()
    //     0x8ebb10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ebb14: sub             SP, fp, #0x80
    // 0x8ebb18: mov             x1, x0
    // 0x8ebb1c: r0 = getDioException()
    //     0x8ebb1c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8ebb20: r1 = <ArticleDetail>
    //     0x8ebb20: ldr             x1, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0x8ebb24: stur            x0, [fp, #-0x58]
    // 0x8ebb28: r0 = _$FailureImpl()
    //     0x8ebb28: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8ebb2c: ldur            x1, [fp, #-0x58]
    // 0x8ebb30: StoreField: r0->field_b = r1
    //     0x8ebb30: stur            w1, [x0, #0xb]
    // 0x8ebb34: r0 = ReturnAsyncNotFuture()
    //     0x8ebb34: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ebb38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ebb38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ebb3c: b               #0x8eba40
  }
  _ findByIdFromBookmark(/* No info */) {
    // ** addr: 0x8ecf00, size: 0x8c
    // 0x8ecf00: EnterFrame
    //     0x8ecf00: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecf04: mov             fp, SP
    // 0x8ecf08: AllocStack(0x20)
    //     0x8ecf08: sub             SP, SP, #0x20
    // 0x8ecf0c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8ecf0c: stur            x2, [fp, #-8]
    // 0x8ecf10: CheckStackOverflow
    //     0x8ecf10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecf14: cmp             SP, x16
    //     0x8ecf18: b.ls            #0x8ecf84
    // 0x8ecf1c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8ecf1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ecf20: ldr             x0, [x0, #0x2728]
    //     0x8ecf24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ecf28: cmp             w0, w16
    //     0x8ecf2c: b.ne            #0x8ecf38
    //     0x8ecf30: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8ecf34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ecf38: r16 = <ArticleDetail>
    //     0x8ecf38: ldr             x16, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0x8ecf3c: stp             x0, x16, [SP, #8]
    // 0x8ecf40: r16 = "v2_articles_bookmark"
    //     0x8ecf40: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "v2_articles_bookmark"
    // 0x8ecf44: str             x16, [SP]
    // 0x8ecf48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ecf48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ecf4c: r0 = box()
    //     0x8ecf4c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ecf50: mov             x3, x0
    // 0x8ecf54: ldur            x2, [fp, #-8]
    // 0x8ecf58: r0 = BoxInt64Instr(r2)
    //     0x8ecf58: sbfiz           x0, x2, #1, #0x1f
    //     0x8ecf5c: cmp             x2, x0, asr #1
    //     0x8ecf60: b.eq            #0x8ecf6c
    //     0x8ecf64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ecf68: stur            x2, [x0, #7]
    // 0x8ecf6c: mov             x1, x3
    // 0x8ecf70: mov             x2, x0
    // 0x8ecf74: r0 = get()
    //     0x8ecf74: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8ecf78: LeaveFrame
    //     0x8ecf78: mov             SP, fp
    //     0x8ecf7c: ldp             fp, lr, [SP], #0x10
    // 0x8ecf80: ret
    //     0x8ecf80: ret             
    // 0x8ecf84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecf84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecf88: b               #0x8ecf1c
  }
  _ findAll(/* No info */) async {
    // ** addr: 0x8ff2cc, size: 0xf0
    // 0x8ff2cc: EnterFrame
    //     0x8ff2cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff2d0: mov             fp, SP
    // 0x8ff2d4: AllocStack(0x88)
    //     0x8ff2d4: sub             SP, SP, #0x88
    // 0x8ff2d8: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x8ff2d8: stur            NULL, [fp, #-8]
    //     0x8ff2dc: stur            x1, [fp, #-0x60]
    //     0x8ff2e0: stur            x2, [fp, #-0x68]
    // 0x8ff2e4: CheckStackOverflow
    //     0x8ff2e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff2e8: cmp             SP, x16
    //     0x8ff2ec: b.ls            #0x8ff3b4
    // 0x8ff2f0: InitAsync() -> Future<ApiResult<List<Article>>>
    //     0x8ff2f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d8c8] TypeArguments: <ApiResult<List<Article>>>
    //     0x8ff2f4: ldr             x0, [x0, #0x8c8]
    //     0x8ff2f8: bl              #0x661298  ; InitAsyncStub
    // 0x8ff2fc: ldur            x0, [fp, #-0x60]
    // 0x8ff300: LoadField: r1 = r0->field_7
    //     0x8ff300: ldur            w1, [x0, #7]
    // 0x8ff304: DecompressPointer r1
    //     0x8ff304: add             x1, x1, HEAP, lsl #32
    // 0x8ff308: stp             x1, NULL, [SP, #0x10]
    // 0x8ff30c: r16 = "/articles"
    //     0x8ff30c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d8d0] "/articles"
    //     0x8ff310: ldr             x16, [x16, #0x8d0]
    // 0x8ff314: ldur            lr, [fp, #-0x68]
    // 0x8ff318: stp             lr, x16, [SP]
    // 0x8ff31c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8ff31c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8ff320: ldr             x4, [x4, #0x2f0]
    // 0x8ff324: r0 = get()
    //     0x8ff324: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8ff328: mov             x1, x0
    // 0x8ff32c: stur            x1, [fp, #-0x60]
    // 0x8ff330: r0 = Await()
    //     0x8ff330: bl              #0x661044  ; AwaitStub
    // 0x8ff334: stur            x0, [fp, #-0x60]
    // 0x8ff338: LoadField: r1 = r0->field_b
    //     0x8ff338: ldur            w1, [x0, #0xb]
    // 0x8ff33c: DecompressPointer r1
    //     0x8ff33c: add             x1, x1, HEAP, lsl #32
    // 0x8ff340: r0 = fromResponse()
    //     0x8ff340: bl              #0x8ff3bc  ; [package:nuonline/app/data/models/article.dart] Article::fromResponse
    // 0x8ff344: mov             x3, x0
    // 0x8ff348: ldur            x0, [fp, #-0x60]
    // 0x8ff34c: stur            x3, [fp, #-0x68]
    // 0x8ff350: LoadField: r2 = r0->field_1b
    //     0x8ff350: ldur            w2, [x0, #0x1b]
    // 0x8ff354: DecompressPointer r2
    //     0x8ff354: add             x2, x2, HEAP, lsl #32
    // 0x8ff358: r1 = Null
    //     0x8ff358: mov             x1, NULL
    // 0x8ff35c: r0 = Pagination.fromHeaders()
    //     0x8ff35c: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x8ff360: r1 = <List<Article>>
    //     0x8ff360: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0x8ff364: ldr             x1, [x1, #0x4f8]
    // 0x8ff368: stur            x0, [fp, #-0x60]
    // 0x8ff36c: r0 = _$SuccessImpl()
    //     0x8ff36c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8ff370: mov             x1, x0
    // 0x8ff374: ldur            x0, [fp, #-0x68]
    // 0x8ff378: StoreField: r1->field_b = r0
    //     0x8ff378: stur            w0, [x1, #0xb]
    // 0x8ff37c: ldur            x0, [fp, #-0x60]
    // 0x8ff380: StoreField: r1->field_f = r0
    //     0x8ff380: stur            w0, [x1, #0xf]
    // 0x8ff384: mov             x0, x1
    // 0x8ff388: r0 = ReturnAsyncNotFuture()
    //     0x8ff388: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ff38c: sub             SP, fp, #0x88
    // 0x8ff390: mov             x1, x0
    // 0x8ff394: r0 = getDioException()
    //     0x8ff394: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8ff398: r1 = <List<Article>>
    //     0x8ff398: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0x8ff39c: ldr             x1, [x1, #0x4f8]
    // 0x8ff3a0: stur            x0, [fp, #-0x60]
    // 0x8ff3a4: r0 = _$FailureImpl()
    //     0x8ff3a4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8ff3a8: ldur            x1, [fp, #-0x60]
    // 0x8ff3ac: StoreField: r0->field_b = r1
    //     0x8ff3ac: stur            w1, [x0, #0xb]
    // 0x8ff3b0: r0 = ReturnAsyncNotFuture()
    //     0x8ff3b0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ff3b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff3b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff3b8: b               #0x8ff2f0
  }
  _ saveToBookmark(/* No info */) async {
    // ** addr: 0xa42a30, size: 0xac
    // 0xa42a30: EnterFrame
    //     0xa42a30: stp             fp, lr, [SP, #-0x10]!
    //     0xa42a34: mov             fp, SP
    // 0xa42a38: AllocStack(0x30)
    //     0xa42a38: sub             SP, SP, #0x30
    // 0xa42a3c: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xa42a3c: stur            NULL, [fp, #-8]
    //     0xa42a40: mov             x3, x2
    //     0xa42a44: stur            x1, [fp, #-0x10]
    //     0xa42a48: stur            x2, [fp, #-0x18]
    // 0xa42a4c: CheckStackOverflow
    //     0xa42a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42a50: cmp             SP, x16
    //     0xa42a54: b.ls            #0xa42ad4
    // 0xa42a58: InitAsync() -> Future<void?>
    //     0xa42a58: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa42a5c: bl              #0x661298  ; InitAsyncStub
    // 0xa42a60: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xa42a60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa42a64: ldr             x0, [x0, #0x2728]
    //     0xa42a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa42a6c: cmp             w0, w16
    //     0xa42a70: b.ne            #0xa42a7c
    //     0xa42a74: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xa42a78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa42a7c: r16 = <ArticleDetail>
    //     0xa42a7c: ldr             x16, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0xa42a80: stp             x0, x16, [SP, #8]
    // 0xa42a84: r16 = "v2_articles_bookmark"
    //     0xa42a84: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "v2_articles_bookmark"
    // 0xa42a88: str             x16, [SP]
    // 0xa42a8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa42a8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa42a90: r0 = box()
    //     0xa42a90: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xa42a94: mov             x2, x0
    // 0xa42a98: ldur            x3, [fp, #-0x18]
    // 0xa42a9c: LoadField: r4 = r3->field_7
    //     0xa42a9c: ldur            x4, [x3, #7]
    // 0xa42aa0: r0 = BoxInt64Instr(r4)
    //     0xa42aa0: sbfiz           x0, x4, #1, #0x1f
    //     0xa42aa4: cmp             x4, x0, asr #1
    //     0xa42aa8: b.eq            #0xa42ab4
    //     0xa42aac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa42ab0: stur            x4, [x0, #7]
    // 0xa42ab4: mov             x1, x2
    // 0xa42ab8: mov             x2, x0
    // 0xa42abc: r0 = put()
    //     0xa42abc: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xa42ac0: mov             x1, x0
    // 0xa42ac4: stur            x1, [fp, #-0x10]
    // 0xa42ac8: r0 = Await()
    //     0xa42ac8: bl              #0x661044  ; AwaitStub
    // 0xa42acc: r0 = Null
    //     0xa42acc: mov             x0, NULL
    // 0xa42ad0: r0 = ReturnAsyncNotFuture()
    //     0xa42ad0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa42ad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42ad4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42ad8: b               #0xa42a58
  }
  _ deleteFromBookmark(/* No info */) async {
    // ** addr: 0xa42b84, size: 0x88
    // 0xa42b84: EnterFrame
    //     0xa42b84: stp             fp, lr, [SP, #-0x10]!
    //     0xa42b88: mov             fp, SP
    // 0xa42b8c: AllocStack(0x30)
    //     0xa42b8c: sub             SP, SP, #0x30
    // 0xa42b90: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa42b90: stur            NULL, [fp, #-8]
    //     0xa42b94: stur            x1, [fp, #-0x10]
    //     0xa42b98: stur            x2, [fp, #-0x18]
    // 0xa42b9c: CheckStackOverflow
    //     0xa42b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42ba0: cmp             SP, x16
    //     0xa42ba4: b.ls            #0xa42c04
    // 0xa42ba8: InitAsync() -> Future<void?>
    //     0xa42ba8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa42bac: bl              #0x661298  ; InitAsyncStub
    // 0xa42bb0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xa42bb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa42bb4: ldr             x0, [x0, #0x2728]
    //     0xa42bb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa42bbc: cmp             w0, w16
    //     0xa42bc0: b.ne            #0xa42bcc
    //     0xa42bc4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xa42bc8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa42bcc: r16 = <ArticleDetail>
    //     0xa42bcc: ldr             x16, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0xa42bd0: stp             x0, x16, [SP, #8]
    // 0xa42bd4: r16 = "v2_articles_bookmark"
    //     0xa42bd4: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "v2_articles_bookmark"
    // 0xa42bd8: str             x16, [SP]
    // 0xa42bdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa42bdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa42be0: r0 = box()
    //     0xa42be0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xa42be4: mov             x1, x0
    // 0xa42be8: ldur            x2, [fp, #-0x18]
    // 0xa42bec: r0 = delete()
    //     0xa42bec: bl              #0xa424f8  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::delete
    // 0xa42bf0: mov             x1, x0
    // 0xa42bf4: stur            x1, [fp, #-0x10]
    // 0xa42bf8: r0 = Await()
    //     0xa42bf8: bl              #0x661044  ; AwaitStub
    // 0xa42bfc: r0 = Null
    //     0xa42bfc: mov             x0, NULL
    // 0xa42c00: r0 = ReturnAsyncNotFuture()
    //     0xa42c00: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa42c04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42c04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42c08: b               #0xa42ba8
  }
  _ findAllByAuthor(/* No info */) async {
    // ** addr: 0xe322b4, size: 0x194
    // 0xe322b4: EnterFrame
    //     0xe322b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe322b8: mov             fp, SP
    // 0xe322bc: AllocStack(0xa0)
    //     0xe322bc: sub             SP, SP, #0xa0
    // 0xe322c0: SetupParameters(ArticleRepository this /* r1 => r3, fp-0x68 */, dynamic _ /* r2 => r1, fp-0x70 */, dynamic _ /* r3 => r2, fp-0x78 */)
    //     0xe322c0: stur            NULL, [fp, #-8]
    //     0xe322c4: stur            x1, [fp, #-0x68]
    //     0xe322c8: mov             x16, x2
    //     0xe322cc: mov             x2, x1
    //     0xe322d0: mov             x1, x16
    //     0xe322d4: mov             x16, x3
    //     0xe322d8: mov             x3, x2
    //     0xe322dc: mov             x2, x16
    //     0xe322e0: stur            x1, [fp, #-0x70]
    //     0xe322e4: stur            x2, [fp, #-0x78]
    // 0xe322e8: CheckStackOverflow
    //     0xe322e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe322ec: cmp             SP, x16
    //     0xe322f0: b.ls            #0xe32440
    // 0xe322f4: InitAsync() -> Future<ApiResult<List<Article>>>
    //     0xe322f4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d8c8] TypeArguments: <ApiResult<List<Article>>>
    //     0xe322f8: ldr             x0, [x0, #0x8c8]
    //     0xe322fc: bl              #0x661298  ; InitAsyncStub
    // 0xe32300: ldur            x1, [fp, #-0x68]
    // 0xe32304: ldur            x0, [fp, #-0x70]
    // 0xe32308: LoadField: r2 = r1->field_7
    //     0xe32308: ldur            w2, [x1, #7]
    // 0xe3230c: DecompressPointer r2
    //     0xe3230c: add             x2, x2, HEAP, lsl #32
    // 0xe32310: stur            x2, [fp, #-0x80]
    // 0xe32314: r16 = <String, dynamic>
    //     0xe32314: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe32318: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xe3231c: stp             lr, x16, [SP]
    // 0xe32320: r0 = Map._fromLiteral()
    //     0xe32320: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe32324: mov             x3, x0
    // 0xe32328: ldur            x2, [fp, #-0x70]
    // 0xe3232c: stur            x3, [fp, #-0x68]
    // 0xe32330: r0 = BoxInt64Instr(r2)
    //     0xe32330: sbfiz           x0, x2, #1, #0x1f
    //     0xe32334: cmp             x2, x0, asr #1
    //     0xe32338: b.eq            #0xe32344
    //     0xe3233c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe32340: stur            x2, [x0, #7]
    // 0xe32344: r1 = 60
    //     0xe32344: movz            x1, #0x3c
    // 0xe32348: branchIfSmi(r0, 0xe32354)
    //     0xe32348: tbz             w0, #0, #0xe32354
    // 0xe3234c: r1 = LoadClassIdInstr(r0)
    //     0xe3234c: ldur            x1, [x0, #-1]
    //     0xe32350: ubfx            x1, x1, #0xc, #0x14
    // 0xe32354: str             x0, [SP]
    // 0xe32358: mov             x0, x1
    // 0xe3235c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe3235c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe32360: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe32360: movz            x17, #0x2b03
    //     0xe32364: add             lr, x0, x17
    //     0xe32368: ldr             lr, [x21, lr, lsl #3]
    //     0xe3236c: blr             lr
    // 0xe32370: ldur            x1, [fp, #-0x68]
    // 0xe32374: mov             x3, x0
    // 0xe32378: r2 = "id"
    //     0xe32378: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe3237c: ldr             x2, [x2, #0x740]
    // 0xe32380: r0 = []=()
    //     0xe32380: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe32384: ldur            x1, [fp, #-0x68]
    // 0xe32388: ldur            x2, [fp, #-0x78]
    // 0xe3238c: r0 = addAll()
    //     0xe3238c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xe32390: ldur            x16, [fp, #-0x80]
    // 0xe32394: stp             x16, NULL, [SP, #0x10]
    // 0xe32398: r16 = "/authors"
    //     0xe32398: add             x16, PP, #0x41, lsl #12  ; [pp+0x412f8] "/authors"
    //     0xe3239c: ldr             x16, [x16, #0x2f8]
    // 0xe323a0: ldur            lr, [fp, #-0x68]
    // 0xe323a4: stp             lr, x16, [SP]
    // 0xe323a8: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe323a8: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe323ac: ldr             x4, [x4, #0x2f0]
    // 0xe323b0: r0 = get()
    //     0xe323b0: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe323b4: mov             x1, x0
    // 0xe323b8: stur            x1, [fp, #-0x68]
    // 0xe323bc: r0 = Await()
    //     0xe323bc: bl              #0x661044  ; AwaitStub
    // 0xe323c0: stur            x0, [fp, #-0x68]
    // 0xe323c4: LoadField: r1 = r0->field_b
    //     0xe323c4: ldur            w1, [x0, #0xb]
    // 0xe323c8: DecompressPointer r1
    //     0xe323c8: add             x1, x1, HEAP, lsl #32
    // 0xe323cc: r0 = fromResponse()
    //     0xe323cc: bl              #0x8ff3bc  ; [package:nuonline/app/data/models/article.dart] Article::fromResponse
    // 0xe323d0: mov             x3, x0
    // 0xe323d4: ldur            x0, [fp, #-0x68]
    // 0xe323d8: stur            x3, [fp, #-0x78]
    // 0xe323dc: LoadField: r2 = r0->field_1b
    //     0xe323dc: ldur            w2, [x0, #0x1b]
    // 0xe323e0: DecompressPointer r2
    //     0xe323e0: add             x2, x2, HEAP, lsl #32
    // 0xe323e4: r1 = Null
    //     0xe323e4: mov             x1, NULL
    // 0xe323e8: r0 = Pagination.fromHeaders()
    //     0xe323e8: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe323ec: r1 = <List<Article>>
    //     0xe323ec: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe323f0: ldr             x1, [x1, #0x4f8]
    // 0xe323f4: stur            x0, [fp, #-0x68]
    // 0xe323f8: r0 = _$SuccessImpl()
    //     0xe323f8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe323fc: mov             x1, x0
    // 0xe32400: ldur            x0, [fp, #-0x78]
    // 0xe32404: StoreField: r1->field_b = r0
    //     0xe32404: stur            w0, [x1, #0xb]
    // 0xe32408: ldur            x0, [fp, #-0x68]
    // 0xe3240c: StoreField: r1->field_f = r0
    //     0xe3240c: stur            w0, [x1, #0xf]
    // 0xe32410: mov             x0, x1
    // 0xe32414: r0 = ReturnAsyncNotFuture()
    //     0xe32414: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe32418: sub             SP, fp, #0xa0
    // 0xe3241c: mov             x1, x0
    // 0xe32420: r0 = getDioException()
    //     0xe32420: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe32424: r1 = <List<Article>>
    //     0xe32424: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe32428: ldr             x1, [x1, #0x4f8]
    // 0xe3242c: stur            x0, [fp, #-0x68]
    // 0xe32430: r0 = _$FailureImpl()
    //     0xe32430: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe32434: ldur            x1, [fp, #-0x68]
    // 0xe32438: StoreField: r0->field_b = r1
    //     0xe32438: stur            w1, [x0, #0xb]
    // 0xe3243c: r0 = ReturnAsyncNotFuture()
    //     0xe3243c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe32440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32440: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32444: b               #0xe322f4
  }
  _ findAllv4(/* No info */) async {
    // ** addr: 0xe32e6c, size: 0xf0
    // 0xe32e6c: EnterFrame
    //     0xe32e6c: stp             fp, lr, [SP, #-0x10]!
    //     0xe32e70: mov             fp, SP
    // 0xe32e74: AllocStack(0x88)
    //     0xe32e74: sub             SP, SP, #0x88
    // 0xe32e78: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0xe32e78: stur            NULL, [fp, #-8]
    //     0xe32e7c: stur            x1, [fp, #-0x60]
    //     0xe32e80: stur            x2, [fp, #-0x68]
    // 0xe32e84: CheckStackOverflow
    //     0xe32e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32e88: cmp             SP, x16
    //     0xe32e8c: b.ls            #0xe32f54
    // 0xe32e90: InitAsync() -> Future<ApiResult<List<Article>>>
    //     0xe32e90: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d8c8] TypeArguments: <ApiResult<List<Article>>>
    //     0xe32e94: ldr             x0, [x0, #0x8c8]
    //     0xe32e98: bl              #0x661298  ; InitAsyncStub
    // 0xe32e9c: ldur            x0, [fp, #-0x60]
    // 0xe32ea0: LoadField: r1 = r0->field_f
    //     0xe32ea0: ldur            w1, [x0, #0xf]
    // 0xe32ea4: DecompressPointer r1
    //     0xe32ea4: add             x1, x1, HEAP, lsl #32
    // 0xe32ea8: stp             x1, NULL, [SP, #0x10]
    // 0xe32eac: r16 = "/articles"
    //     0xe32eac: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d8d0] "/articles"
    //     0xe32eb0: ldr             x16, [x16, #0x8d0]
    // 0xe32eb4: ldur            lr, [fp, #-0x68]
    // 0xe32eb8: stp             lr, x16, [SP]
    // 0xe32ebc: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe32ebc: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe32ec0: ldr             x4, [x4, #0x2f0]
    // 0xe32ec4: r0 = get()
    //     0xe32ec4: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe32ec8: mov             x1, x0
    // 0xe32ecc: stur            x1, [fp, #-0x60]
    // 0xe32ed0: r0 = Await()
    //     0xe32ed0: bl              #0x661044  ; AwaitStub
    // 0xe32ed4: stur            x0, [fp, #-0x60]
    // 0xe32ed8: LoadField: r1 = r0->field_b
    //     0xe32ed8: ldur            w1, [x0, #0xb]
    // 0xe32edc: DecompressPointer r1
    //     0xe32edc: add             x1, x1, HEAP, lsl #32
    // 0xe32ee0: r0 = fromResponse()
    //     0xe32ee0: bl              #0x8ff3bc  ; [package:nuonline/app/data/models/article.dart] Article::fromResponse
    // 0xe32ee4: mov             x3, x0
    // 0xe32ee8: ldur            x0, [fp, #-0x60]
    // 0xe32eec: stur            x3, [fp, #-0x68]
    // 0xe32ef0: LoadField: r2 = r0->field_1b
    //     0xe32ef0: ldur            w2, [x0, #0x1b]
    // 0xe32ef4: DecompressPointer r2
    //     0xe32ef4: add             x2, x2, HEAP, lsl #32
    // 0xe32ef8: r1 = Null
    //     0xe32ef8: mov             x1, NULL
    // 0xe32efc: r0 = Pagination.fromHeaders()
    //     0xe32efc: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe32f00: r1 = <List<Article>>
    //     0xe32f00: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe32f04: ldr             x1, [x1, #0x4f8]
    // 0xe32f08: stur            x0, [fp, #-0x60]
    // 0xe32f0c: r0 = _$SuccessImpl()
    //     0xe32f0c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe32f10: mov             x1, x0
    // 0xe32f14: ldur            x0, [fp, #-0x68]
    // 0xe32f18: StoreField: r1->field_b = r0
    //     0xe32f18: stur            w0, [x1, #0xb]
    // 0xe32f1c: ldur            x0, [fp, #-0x60]
    // 0xe32f20: StoreField: r1->field_f = r0
    //     0xe32f20: stur            w0, [x1, #0xf]
    // 0xe32f24: mov             x0, x1
    // 0xe32f28: r0 = ReturnAsyncNotFuture()
    //     0xe32f28: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe32f2c: sub             SP, fp, #0x88
    // 0xe32f30: mov             x1, x0
    // 0xe32f34: r0 = getDioException()
    //     0xe32f34: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe32f38: r1 = <List<Article>>
    //     0xe32f38: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe32f3c: ldr             x1, [x1, #0x4f8]
    // 0xe32f40: stur            x0, [fp, #-0x60]
    // 0xe32f44: r0 = _$FailureImpl()
    //     0xe32f44: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe32f48: ldur            x1, [fp, #-0x60]
    // 0xe32f4c: StoreField: r0->field_b = r1
    //     0xe32f4c: stur            w1, [x0, #0xb]
    // 0xe32f50: r0 = ReturnAsyncNotFuture()
    //     0xe32f50: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe32f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32f54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32f58: b               #0xe32e90
  }
  _ findAllByPath(/* No info */) async {
    // ** addr: 0xe334a4, size: 0x120
    // 0xe334a4: EnterFrame
    //     0xe334a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe334a8: mov             fp, SP
    // 0xe334ac: AllocStack(0xa0)
    //     0xe334ac: sub             SP, SP, #0xa0
    // 0xe334b0: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x68 */, dynamic _ /* r2 => r2, fp-0x70 */, dynamic _ /* r3 => r3, fp-0x78 */)
    //     0xe334b0: stur            NULL, [fp, #-8]
    //     0xe334b4: stur            x1, [fp, #-0x68]
    //     0xe334b8: stur            x2, [fp, #-0x70]
    //     0xe334bc: stur            x3, [fp, #-0x78]
    // 0xe334c0: CheckStackOverflow
    //     0xe334c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe334c4: cmp             SP, x16
    //     0xe334c8: b.ls            #0xe335bc
    // 0xe334cc: InitAsync() -> Future<ApiResult<List<Article>>>
    //     0xe334cc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d8c8] TypeArguments: <ApiResult<List<Article>>>
    //     0xe334d0: ldr             x0, [x0, #0x8c8]
    //     0xe334d4: bl              #0x661298  ; InitAsyncStub
    // 0xe334d8: ldur            x1, [fp, #-0x68]
    // 0xe334dc: ldur            x0, [fp, #-0x70]
    // 0xe334e0: LoadField: r3 = r1->field_7
    //     0xe334e0: ldur            w3, [x1, #7]
    // 0xe334e4: DecompressPointer r3
    //     0xe334e4: add             x3, x3, HEAP, lsl #32
    // 0xe334e8: stur            x3, [fp, #-0x80]
    // 0xe334ec: r1 = Null
    //     0xe334ec: mov             x1, NULL
    // 0xe334f0: r2 = 4
    //     0xe334f0: movz            x2, #0x4
    // 0xe334f4: r0 = AllocateArray()
    //     0xe334f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe334f8: r16 = "/articles/"
    //     0xe334f8: add             x16, PP, #0x40, lsl #12  ; [pp+0x408d0] "/articles/"
    //     0xe334fc: ldr             x16, [x16, #0x8d0]
    // 0xe33500: StoreField: r0->field_f = r16
    //     0xe33500: stur            w16, [x0, #0xf]
    // 0xe33504: ldur            x1, [fp, #-0x70]
    // 0xe33508: StoreField: r0->field_13 = r1
    //     0xe33508: stur            w1, [x0, #0x13]
    // 0xe3350c: str             x0, [SP]
    // 0xe33510: r0 = _interpolate()
    //     0xe33510: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe33514: ldur            x16, [fp, #-0x80]
    // 0xe33518: stp             x16, NULL, [SP, #0x10]
    // 0xe3351c: ldur            x16, [fp, #-0x78]
    // 0xe33520: stp             x16, x0, [SP]
    // 0xe33524: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe33524: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe33528: ldr             x4, [x4, #0x2f0]
    // 0xe3352c: r0 = get()
    //     0xe3352c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe33530: mov             x1, x0
    // 0xe33534: stur            x1, [fp, #-0x68]
    // 0xe33538: r0 = Await()
    //     0xe33538: bl              #0x661044  ; AwaitStub
    // 0xe3353c: stur            x0, [fp, #-0x68]
    // 0xe33540: LoadField: r1 = r0->field_b
    //     0xe33540: ldur            w1, [x0, #0xb]
    // 0xe33544: DecompressPointer r1
    //     0xe33544: add             x1, x1, HEAP, lsl #32
    // 0xe33548: r0 = fromResponse()
    //     0xe33548: bl              #0x8ff3bc  ; [package:nuonline/app/data/models/article.dart] Article::fromResponse
    // 0xe3354c: mov             x3, x0
    // 0xe33550: ldur            x0, [fp, #-0x68]
    // 0xe33554: stur            x3, [fp, #-0x70]
    // 0xe33558: LoadField: r2 = r0->field_1b
    //     0xe33558: ldur            w2, [x0, #0x1b]
    // 0xe3355c: DecompressPointer r2
    //     0xe3355c: add             x2, x2, HEAP, lsl #32
    // 0xe33560: r1 = Null
    //     0xe33560: mov             x1, NULL
    // 0xe33564: r0 = Pagination.fromHeaders()
    //     0xe33564: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe33568: r1 = <List<Article>>
    //     0xe33568: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe3356c: ldr             x1, [x1, #0x4f8]
    // 0xe33570: stur            x0, [fp, #-0x68]
    // 0xe33574: r0 = _$SuccessImpl()
    //     0xe33574: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe33578: mov             x1, x0
    // 0xe3357c: ldur            x0, [fp, #-0x70]
    // 0xe33580: StoreField: r1->field_b = r0
    //     0xe33580: stur            w0, [x1, #0xb]
    // 0xe33584: ldur            x0, [fp, #-0x68]
    // 0xe33588: StoreField: r1->field_f = r0
    //     0xe33588: stur            w0, [x1, #0xf]
    // 0xe3358c: mov             x0, x1
    // 0xe33590: r0 = ReturnAsyncNotFuture()
    //     0xe33590: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe33594: sub             SP, fp, #0xa0
    // 0xe33598: mov             x1, x0
    // 0xe3359c: r0 = getDioException()
    //     0xe3359c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe335a0: r1 = <List<Article>>
    //     0xe335a0: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b4f8] TypeArguments: <List<Article>>
    //     0xe335a4: ldr             x1, [x1, #0x4f8]
    // 0xe335a8: stur            x0, [fp, #-0x68]
    // 0xe335ac: r0 = _$FailureImpl()
    //     0xe335ac: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe335b0: ldur            x1, [fp, #-0x68]
    // 0xe335b4: StoreField: r0->field_b = r1
    //     0xe335b4: stur            w1, [x0, #0xb]
    // 0xe335b8: r0 = ReturnAsyncNotFuture()
    //     0xe335b8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe335bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe335bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe335c0: b               #0xe334cc
  }
  _ counterTopic(/* No info */) async {
    // ** addr: 0xe338b0, size: 0x114
    // 0xe338b0: EnterFrame
    //     0xe338b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe338b4: mov             fp, SP
    // 0xe338b8: AllocStack(0x40)
    //     0xe338b8: sub             SP, SP, #0x40
    // 0xe338bc: SetupParameters(ArticleRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe338bc: stur            NULL, [fp, #-8]
    //     0xe338c0: stur            x1, [fp, #-0x10]
    //     0xe338c4: stur            x2, [fp, #-0x18]
    // 0xe338c8: CheckStackOverflow
    //     0xe338c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe338cc: cmp             SP, x16
    //     0xe338d0: b.ls            #0xe339bc
    // 0xe338d4: InitAsync() -> Future<void?>
    //     0xe338d4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe338d8: bl              #0x661298  ; InitAsyncStub
    // 0xe338dc: ldur            x0, [fp, #-0x10]
    // 0xe338e0: LoadField: r3 = r0->field_7
    //     0xe338e0: ldur            w3, [x0, #7]
    // 0xe338e4: DecompressPointer r3
    //     0xe338e4: add             x3, x3, HEAP, lsl #32
    // 0xe338e8: stur            x3, [fp, #-0x20]
    // 0xe338ec: r1 = Null
    //     0xe338ec: mov             x1, NULL
    // 0xe338f0: r2 = 4
    //     0xe338f0: movz            x2, #0x4
    // 0xe338f4: r0 = AllocateArray()
    //     0xe338f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe338f8: mov             x2, x0
    // 0xe338fc: stur            x2, [fp, #-0x10]
    // 0xe33900: r16 = "id"
    //     0xe33900: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xe33904: ldr             x16, [x16, #0x740]
    // 0xe33908: StoreField: r2->field_f = r16
    //     0xe33908: stur            w16, [x2, #0xf]
    // 0xe3390c: ldur            x3, [fp, #-0x18]
    // 0xe33910: r0 = BoxInt64Instr(r3)
    //     0xe33910: sbfiz           x0, x3, #1, #0x1f
    //     0xe33914: cmp             x3, x0, asr #1
    //     0xe33918: b.eq            #0xe33924
    //     0xe3391c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe33920: stur            x3, [x0, #7]
    // 0xe33924: r1 = 60
    //     0xe33924: movz            x1, #0x3c
    // 0xe33928: branchIfSmi(r0, 0xe33934)
    //     0xe33928: tbz             w0, #0, #0xe33934
    // 0xe3392c: r1 = LoadClassIdInstr(r0)
    //     0xe3392c: ldur            x1, [x0, #-1]
    //     0xe33930: ubfx            x1, x1, #0xc, #0x14
    // 0xe33934: str             x0, [SP]
    // 0xe33938: mov             x0, x1
    // 0xe3393c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe3393c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe33940: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe33940: movz            x17, #0x2b03
    //     0xe33944: add             lr, x0, x17
    //     0xe33948: ldr             lr, [x21, lr, lsl #3]
    //     0xe3394c: blr             lr
    // 0xe33950: ldur            x1, [fp, #-0x10]
    // 0xe33954: ArrayStore: r1[1] = r0  ; List_4
    //     0xe33954: add             x25, x1, #0x13
    //     0xe33958: str             w0, [x25]
    //     0xe3395c: tbz             w0, #0, #0xe33978
    //     0xe33960: ldurb           w16, [x1, #-1]
    //     0xe33964: ldurb           w17, [x0, #-1]
    //     0xe33968: and             x16, x17, x16, lsr #2
    //     0xe3396c: tst             x16, HEAP, lsr #32
    //     0xe33970: b.eq            #0xe33978
    //     0xe33974: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe33978: r16 = <String, dynamic>
    //     0xe33978: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe3397c: ldur            lr, [fp, #-0x10]
    // 0xe33980: stp             lr, x16, [SP]
    // 0xe33984: r0 = Map._fromLiteral()
    //     0xe33984: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe33988: ldur            x16, [fp, #-0x20]
    // 0xe3398c: stp             x16, NULL, [SP, #0x10]
    // 0xe33990: r16 = "/topics/counter"
    //     0xe33990: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3cb00] "/topics/counter"
    //     0xe33994: ldr             x16, [x16, #0xb00]
    // 0xe33998: stp             x0, x16, [SP]
    // 0xe3399c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe3399c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe339a0: ldr             x4, [x4, #0x2f0]
    // 0xe339a4: r0 = post()
    //     0xe339a4: bl              #0x7eb5f0  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::post
    // 0xe339a8: mov             x1, x0
    // 0xe339ac: stur            x1, [fp, #-0x10]
    // 0xe339b0: r0 = Await()
    //     0xe339b0: bl              #0x661044  ; AwaitStub
    // 0xe339b4: r0 = Null
    //     0xe339b4: mov             x0, NULL
    // 0xe339b8: r0 = ReturnAsyncNotFuture()
    //     0xe339b8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe339bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe339bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe339c0: b               #0xe338d4
  }
}
