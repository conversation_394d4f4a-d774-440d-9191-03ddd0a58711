// lib: , url: package:nuonline/app/data/repositories/calendar_repository.dart

// class id: 1050069, size: 0x8
class :: {
}

// class id: 1109, size: 0x14, field offset: 0x8
class CalendarRepository extends Object {

  _ update(/* No info */) async {
    // ** addr: 0x8eda88, size: 0x614
    // 0x8eda88: EnterFrame
    //     0x8eda88: stp             fp, lr, [SP, #-0x10]!
    //     0x8eda8c: mov             fp, SP
    // 0x8eda90: AllocStack(0x138)
    //     0x8eda90: sub             SP, SP, #0x138
    // 0x8eda94: SetupParameters(CalendarRepository this /* r1 => r1, fp-0xa0 */)
    //     0x8eda94: stur            NULL, [fp, #-8]
    //     0x8eda98: stur            x1, [fp, #-0xa0]
    // 0x8eda9c: CheckStackOverflow
    //     0x8eda9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8edaa0: cmp             SP, x16
    //     0x8edaa4: b.ls            #0x8ee084
    // 0x8edaa8: InitAsync() -> Future<void?>
    //     0x8edaa8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8edaac: bl              #0x661298  ; InitAsyncStub
    // 0x8edab0: ldur            x0, [fp, #-0xa0]
    // 0x8edab4: LoadField: r3 = r0->field_7
    //     0x8edab4: ldur            w3, [x0, #7]
    // 0x8edab8: DecompressPointer r3
    //     0x8edab8: add             x3, x3, HEAP, lsl #32
    // 0x8edabc: stur            x3, [fp, #-0xa8]
    // 0x8edac0: r1 = Null
    //     0x8edac0: mov             x1, NULL
    // 0x8edac4: r2 = 4
    //     0x8edac4: movz            x2, #0x4
    // 0x8edac8: r0 = AllocateArray()
    //     0x8edac8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8edacc: stur            x0, [fp, #-0xb8]
    // 0x8edad0: r16 = "last_update"
    //     0x8edad0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27378] "last_update"
    //     0x8edad4: ldr             x16, [x16, #0x378]
    // 0x8edad8: StoreField: r0->field_f = r16
    //     0x8edad8: stur            w16, [x0, #0xf]
    // 0x8edadc: ldur            x2, [fp, #-0xa0]
    // 0x8edae0: LoadField: r3 = r2->field_f
    //     0x8edae0: ldur            w3, [x2, #0xf]
    // 0x8edae4: DecompressPointer r3
    //     0x8edae4: add             x3, x3, HEAP, lsl #32
    // 0x8edae8: mov             x1, x3
    // 0x8edaec: stur            x3, [fp, #-0xb0]
    // 0x8edaf0: r0 = lastUpdateCalendar()
    //     0x8edaf0: bl              #0x8ee744  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::lastUpdateCalendar
    // 0x8edaf4: mov             x1, x0
    // 0x8edaf8: r0 = toIso8601String()
    //     0x8edaf8: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0x8edafc: ldur            x1, [fp, #-0xb8]
    // 0x8edb00: ArrayStore: r1[1] = r0  ; List_4
    //     0x8edb00: add             x25, x1, #0x13
    //     0x8edb04: str             w0, [x25]
    //     0x8edb08: tbz             w0, #0, #0x8edb24
    //     0x8edb0c: ldurb           w16, [x1, #-1]
    //     0x8edb10: ldurb           w17, [x0, #-1]
    //     0x8edb14: and             x16, x17, x16, lsr #2
    //     0x8edb18: tst             x16, HEAP, lsr #32
    //     0x8edb1c: b.eq            #0x8edb24
    //     0x8edb20: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8edb24: r16 = <String, dynamic>
    //     0x8edb24: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8edb28: ldur            lr, [fp, #-0xb8]
    // 0x8edb2c: stp             lr, x16, [SP]
    // 0x8edb30: r0 = Map._fromLiteral()
    //     0x8edb30: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8edb34: ldur            x16, [fp, #-0xa8]
    // 0x8edb38: stp             x16, NULL, [SP, #0x10]
    // 0x8edb3c: r16 = "/calendar/update"
    //     0x8edb3c: add             x16, PP, #0x40, lsl #12  ; [pp+0x406f8] "/calendar/update"
    //     0x8edb40: ldr             x16, [x16, #0x6f8]
    // 0x8edb44: stp             x0, x16, [SP]
    // 0x8edb48: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8edb48: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8edb4c: ldr             x4, [x4, #0x2f0]
    // 0x8edb50: r0 = get()
    //     0x8edb50: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8edb54: mov             x1, x0
    // 0x8edb58: stur            x1, [fp, #-0xa8]
    // 0x8edb5c: r0 = Await()
    //     0x8edb5c: bl              #0x661044  ; AwaitStub
    // 0x8edb60: LoadField: r3 = r0->field_b
    //     0x8edb60: ldur            w3, [x0, #0xb]
    // 0x8edb64: DecompressPointer r3
    //     0x8edb64: add             x3, x3, HEAP, lsl #32
    // 0x8edb68: stur            x3, [fp, #-0xa8]
    // 0x8edb6c: r1 = <HijriResponse>
    //     0x8edb6c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40700] TypeArguments: <HijriResponse>
    //     0x8edb70: ldr             x1, [x1, #0x700]
    // 0x8edb74: r2 = 0
    //     0x8edb74: movz            x2, #0
    // 0x8edb78: r0 = _GrowableList()
    //     0x8edb78: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8edb7c: mov             x3, x0
    // 0x8edb80: ldur            x0, [fp, #-0xa8]
    // 0x8edb84: r2 = Null
    //     0x8edb84: mov             x2, NULL
    // 0x8edb88: r1 = Null
    //     0x8edb88: mov             x1, NULL
    // 0x8edb8c: stur            x3, [fp, #-0xb8]
    // 0x8edb90: cmp             w0, NULL
    // 0x8edb94: b.eq            #0x8edc38
    // 0x8edb98: branchIfSmi(r0, 0x8edc38)
    //     0x8edb98: tbz             w0, #0, #0x8edc38
    // 0x8edb9c: r3 = LoadClassIdInstr(r0)
    //     0x8edb9c: ldur            x3, [x0, #-1]
    //     0x8edba0: ubfx            x3, x3, #0xc, #0x14
    // 0x8edba4: r17 = 6718
    //     0x8edba4: movz            x17, #0x1a3e
    // 0x8edba8: cmp             x3, x17
    // 0x8edbac: b.eq            #0x8edc40
    // 0x8edbb0: sub             x3, x3, #0x5a
    // 0x8edbb4: cmp             x3, #2
    // 0x8edbb8: b.ls            #0x8edc40
    // 0x8edbbc: r4 = LoadClassIdInstr(r0)
    //     0x8edbbc: ldur            x4, [x0, #-1]
    //     0x8edbc0: ubfx            x4, x4, #0xc, #0x14
    // 0x8edbc4: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x8edbc8: ldr             x3, [x3, #0x18]
    // 0x8edbcc: ldr             x3, [x3, x4, lsl #3]
    // 0x8edbd0: LoadField: r3 = r3->field_2b
    //     0x8edbd0: ldur            w3, [x3, #0x2b]
    // 0x8edbd4: DecompressPointer r3
    //     0x8edbd4: add             x3, x3, HEAP, lsl #32
    // 0x8edbd8: cmp             w3, NULL
    // 0x8edbdc: b.eq            #0x8edc38
    // 0x8edbe0: LoadField: r3 = r3->field_f
    //     0x8edbe0: ldur            w3, [x3, #0xf]
    // 0x8edbe4: lsr             x3, x3, #3
    // 0x8edbe8: r17 = 6718
    //     0x8edbe8: movz            x17, #0x1a3e
    // 0x8edbec: cmp             x3, x17
    // 0x8edbf0: b.eq            #0x8edc40
    // 0x8edbf4: r3 = SubtypeTestCache
    //     0x8edbf4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40708] SubtypeTestCache
    //     0x8edbf8: ldr             x3, [x3, #0x708]
    // 0x8edbfc: r30 = Subtype1TestCacheStub
    //     0x8edbfc: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x8edc00: LoadField: r30 = r30->field_7
    //     0x8edc00: ldur            lr, [lr, #7]
    // 0x8edc04: blr             lr
    // 0x8edc08: cmp             w7, NULL
    // 0x8edc0c: b.eq            #0x8edc18
    // 0x8edc10: tbnz            w7, #4, #0x8edc38
    // 0x8edc14: b               #0x8edc40
    // 0x8edc18: r8 = List
    //     0x8edc18: add             x8, PP, #0x40, lsl #12  ; [pp+0x40710] Type: List
    //     0x8edc1c: ldr             x8, [x8, #0x710]
    // 0x8edc20: r3 = SubtypeTestCache
    //     0x8edc20: add             x3, PP, #0x40, lsl #12  ; [pp+0x40718] SubtypeTestCache
    //     0x8edc24: ldr             x3, [x3, #0x718]
    // 0x8edc28: r30 = InstanceOfStub
    //     0x8edc28: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x8edc2c: LoadField: r30 = r30->field_7
    //     0x8edc2c: ldur            lr, [lr, #7]
    // 0x8edc30: blr             lr
    // 0x8edc34: b               #0x8edc44
    // 0x8edc38: r0 = false
    //     0x8edc38: add             x0, NULL, #0x30  ; false
    // 0x8edc3c: b               #0x8edc44
    // 0x8edc40: r0 = true
    //     0x8edc40: add             x0, NULL, #0x20  ; true
    // 0x8edc44: tbnz            w0, #4, #0x8edcc4
    // 0x8edc48: ldur            x0, [fp, #-0xa8]
    // 0x8edc4c: r1 = Function '<anonymous closure>':.
    //     0x8edc4c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40720] AnonymousClosure: (0x8ee870), in [package:nuonline/app/data/repositories/calendar_repository.dart] CalendarRepository::update (0x8eda88)
    //     0x8edc50: ldr             x1, [x1, #0x720]
    // 0x8edc54: r2 = Null
    //     0x8edc54: mov             x2, NULL
    // 0x8edc58: r0 = AllocateClosure()
    //     0x8edc58: bl              #0xec1630  ; AllocateClosureStub
    // 0x8edc5c: mov             x1, x0
    // 0x8edc60: ldur            x0, [fp, #-0xa8]
    // 0x8edc64: r2 = LoadClassIdInstr(r0)
    //     0x8edc64: ldur            x2, [x0, #-1]
    //     0x8edc68: ubfx            x2, x2, #0xc, #0x14
    // 0x8edc6c: r16 = <HijriResponse>
    //     0x8edc6c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40700] TypeArguments: <HijriResponse>
    //     0x8edc70: ldr             x16, [x16, #0x700]
    // 0x8edc74: stp             x0, x16, [SP, #8]
    // 0x8edc78: str             x1, [SP]
    // 0x8edc7c: mov             x0, x2
    // 0x8edc80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8edc80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8edc84: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8edc84: movz            x17, #0xf28c
    //     0x8edc88: add             lr, x0, x17
    //     0x8edc8c: ldr             lr, [x21, lr, lsl #3]
    //     0x8edc90: blr             lr
    // 0x8edc94: r1 = LoadClassIdInstr(r0)
    //     0x8edc94: ldur            x1, [x0, #-1]
    //     0x8edc98: ubfx            x1, x1, #0xc, #0x14
    // 0x8edc9c: mov             x16, x0
    // 0x8edca0: mov             x0, x1
    // 0x8edca4: mov             x1, x16
    // 0x8edca8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8edca8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8edcac: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8edcac: movz            x17, #0xd889
    //     0x8edcb0: add             lr, x0, x17
    //     0x8edcb4: ldr             lr, [x21, lr, lsl #3]
    //     0x8edcb8: blr             lr
    // 0x8edcbc: mov             x1, x0
    // 0x8edcc0: b               #0x8edcc8
    // 0x8edcc4: ldur            x1, [fp, #-0xb8]
    // 0x8edcc8: stur            x1, [fp, #-0xa8]
    // 0x8edccc: r16 = <String, HijriDate>
    //     0x8edccc: add             x16, PP, #0x12, lsl #12  ; [pp+0x127d8] TypeArguments: <String, HijriDate>
    //     0x8edcd0: ldr             x16, [x16, #0x7d8]
    // 0x8edcd4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8edcd8: stp             lr, x16, [SP]
    // 0x8edcdc: r0 = Map._fromLiteral()
    //     0x8edcdc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8edce0: mov             x2, x0
    // 0x8edce4: ldur            x0, [fp, #-0xa8]
    // 0x8edce8: stur            x2, [fp, #-0xc0]
    // 0x8edcec: LoadField: r3 = r0->field_7
    //     0x8edcec: ldur            w3, [x0, #7]
    // 0x8edcf0: DecompressPointer r3
    //     0x8edcf0: add             x3, x3, HEAP, lsl #32
    // 0x8edcf4: mov             x1, x3
    // 0x8edcf8: stur            x3, [fp, #-0xb8]
    // 0x8edcfc: r0 = ListIterator()
    //     0x8edcfc: bl              #0x644848  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x8edd00: ldur            x1, [fp, #-0xa8]
    // 0x8edd04: StoreField: r0->field_b = r1
    //     0x8edd04: stur            w1, [x0, #0xb]
    // 0x8edd08: LoadField: r2 = r1->field_b
    //     0x8edd08: ldur            w2, [x1, #0xb]
    // 0x8edd0c: r3 = LoadInt32Instr(r2)
    //     0x8edd0c: sbfx            x3, x2, #1, #0x1f
    // 0x8edd10: stur            x3, [fp, #-0xc8]
    // 0x8edd14: StoreField: r0->field_f = r3
    //     0x8edd14: stur            x3, [x0, #0xf]
    // 0x8edd18: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8edd18: stur            xzr, [x0, #0x17]
    // 0x8edd1c: ldur            x4, [fp, #-0xa0]
    // 0x8edd20: mov             x2, x0
    // 0x8edd24: ldur            x0, [fp, #-0xc0]
    // 0x8edd28: stur            x4, [fp, #-0xa0]
    // 0x8edd2c: stur            x2, [fp, #-0xb8]
    // 0x8edd30: stur            x0, [fp, #-0xc0]
    // 0x8edd34: CheckStackOverflow
    //     0x8edd34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8edd38: cmp             SP, x16
    //     0x8edd3c: b.ls            #0x8ee08c
    // 0x8edd40: str             x1, [SP]
    // 0x8edd44: r0 = length()
    //     0x8edd44: bl              #0xa96414  ; [dart:core] _GrowableList::length
    // 0x8edd48: stur            x0, [fp, #-0xd0]
    // 0x8edd4c: r1 = LoadInt32Instr(r0)
    //     0x8edd4c: sbfx            x1, x0, #1, #0x1f
    //     0x8edd50: tbz             w0, #0, #0x8edd58
    //     0x8edd54: ldur            x1, [x0, #7]
    // 0x8edd58: ldur            x3, [fp, #-0xc8]
    // 0x8edd5c: cmp             x3, x1
    // 0x8edd60: b.ne            #0x8ee05c
    // 0x8edd64: ldur            x4, [fp, #-0xb8]
    // 0x8edd68: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x8edd68: ldur            x2, [x4, #0x17]
    // 0x8edd6c: cmp             x2, x1
    // 0x8edd70: b.ge            #0x8edfa4
    // 0x8edd74: ldur            x1, [fp, #-0xa8]
    // 0x8edd78: r0 = elementAt()
    //     0x8edd78: bl              #0xa690e8  ; [dart:core] _GrowableList::elementAt
    // 0x8edd7c: mov             x4, x0
    // 0x8edd80: ldur            x3, [fp, #-0xb8]
    // 0x8edd84: stur            x4, [fp, #-0xe0]
    // 0x8edd88: StoreField: r3->field_1f = r0
    //     0x8edd88: stur            w0, [x3, #0x1f]
    //     0x8edd8c: tbz             w0, #0, #0x8edda8
    //     0x8edd90: ldurb           w16, [x3, #-1]
    //     0x8edd94: ldurb           w17, [x0, #-1]
    //     0x8edd98: and             x16, x17, x16, lsr #2
    //     0x8edd9c: tst             x16, HEAP, lsr #32
    //     0x8edda0: b.eq            #0x8edda8
    //     0x8edda4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8edda8: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x8edda8: ldur            x0, [x3, #0x17]
    // 0x8eddac: add             x1, x0, #1
    // 0x8eddb0: ArrayStore: r3[0] = r1  ; List_8
    //     0x8eddb0: stur            x1, [x3, #0x17]
    // 0x8eddb4: cmp             w4, NULL
    // 0x8eddb8: b.ne            #0x8eddf8
    // 0x8eddbc: LoadField: r5 = r3->field_7
    //     0x8eddbc: ldur            w5, [x3, #7]
    // 0x8eddc0: DecompressPointer r5
    //     0x8eddc0: add             x5, x5, HEAP, lsl #32
    // 0x8eddc4: mov             x0, x4
    // 0x8eddc8: mov             x2, x5
    // 0x8eddcc: stur            x5, [fp, #-0xd8]
    // 0x8eddd0: r1 = Null
    //     0x8eddd0: mov             x1, NULL
    // 0x8eddd4: cmp             w2, NULL
    // 0x8eddd8: b.eq            #0x8eddf8
    // 0x8edddc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8edddc: ldur            w4, [x2, #0x17]
    // 0x8edde0: DecompressPointer r4
    //     0x8edde0: add             x4, x4, HEAP, lsl #32
    // 0x8edde4: r8 = X0
    //     0x8edde4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8edde8: LoadField: r9 = r4->field_7
    //     0x8edde8: ldur            x9, [x4, #7]
    // 0x8eddec: r3 = Null
    //     0x8eddec: add             x3, PP, #0x40, lsl #12  ; [pp+0x40728] Null
    //     0x8eddf0: ldr             x3, [x3, #0x728]
    // 0x8eddf4: blr             x9
    // 0x8eddf8: ldur            x4, [fp, #-0xa0]
    // 0x8eddfc: ldur            x2, [fp, #-0xe0]
    // 0x8ede00: ldur            x0, [fp, #-0xb8]
    // 0x8ede04: ldur            x1, [fp, #-0xc0]
    // 0x8ede08: r3 = 0
    //     0x8ede08: movz            x3, #0
    // 0x8ede0c: stur            x4, [fp, #-0xd8]
    // 0x8ede10: stur            x3, [fp, #-0xe8]
    // 0x8ede14: stur            x2, [fp, #-0xe0]
    // 0x8ede18: stur            x0, [fp, #-0xf0]
    // 0x8ede1c: stur            x1, [fp, #-0xf8]
    // 0x8ede20: CheckStackOverflow
    //     0x8ede20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ede24: cmp             SP, x16
    //     0x8ede28: b.ls            #0x8ee094
    // 0x8ede2c: LoadField: r5 = r2->field_1f
    //     0x8ede2c: ldur            x5, [x2, #0x1f]
    // 0x8ede30: cmp             x3, x5
    // 0x8ede34: b.ge            #0x8edf8c
    // 0x8ede38: r0 = DateFormat()
    //     0x8ede38: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x8ede3c: r1 = Function '<anonymous closure>':.
    //     0x8ede3c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40738] AnonymousClosure: (0x8ee780), of [package:intl/src/intl/date_format.dart] DateFormat
    //     0x8ede40: ldr             x1, [x1, #0x738]
    // 0x8ede44: r2 = Null
    //     0x8ede44: mov             x2, NULL
    // 0x8ede48: stur            x0, [fp, #-0x100]
    // 0x8ede4c: r0 = AllocateClosure()
    //     0x8ede4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ede50: r1 = "id_ID"
    //     0x8ede50: add             x1, PP, #9, lsl #12  ; [pp+0x9200] "id_ID"
    //     0x8ede54: ldr             x1, [x1, #0x200]
    // 0x8ede58: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x8ede58: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x8ede5c: ldr             x2, [x2, #0xad0]
    // 0x8ede60: r17 = -264
    //     0x8ede60: movn            x17, #0x107
    // 0x8ede64: str             x0, [fp, x17]
    // 0x8ede68: r0 = verifiedLocale()
    //     0x8ede68: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x8ede6c: ldur            x3, [fp, #-0x100]
    // 0x8ede70: StoreField: r3->field_7 = r0
    //     0x8ede70: stur            w0, [x3, #7]
    //     0x8ede74: ldurb           w16, [x3, #-1]
    //     0x8ede78: ldurb           w17, [x0, #-1]
    //     0x8ede7c: and             x16, x17, x16, lsr #2
    //     0x8ede80: tst             x16, HEAP, lsr #32
    //     0x8ede84: b.eq            #0x8ede8c
    //     0x8ede88: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8ede8c: mov             x1, x3
    // 0x8ede90: r2 = "yyyy-M-d"
    //     0x8ede90: add             x2, PP, #0x40, lsl #12  ; [pp+0x40740] "yyyy-M-d"
    //     0x8ede94: ldr             x2, [x2, #0x740]
    // 0x8ede98: r0 = addPattern()
    //     0x8ede98: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x8ede9c: ldur            x2, [fp, #-0xe0]
    // 0x8edea0: LoadField: r1 = r2->field_27
    //     0x8edea0: ldur            w1, [x2, #0x27]
    // 0x8edea4: DecompressPointer r1
    //     0x8edea4: add             x1, x1, HEAP, lsl #32
    // 0x8edea8: ldur            x0, [fp, #-0xe8]
    // 0x8edeac: r17 = -264
    //     0x8edeac: movn            x17, #0x107
    // 0x8edeb0: str             x1, [fp, x17]
    // 0x8edeb4: add             x3, x0, #1
    // 0x8edeb8: r17 = -272
    //     0x8edeb8: movn            x17, #0x10f
    // 0x8edebc: str             x3, [fp, x17]
    // 0x8edec0: r0 = Duration()
    //     0x8edec0: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x8edec4: r17 = -272
    //     0x8edec4: movn            x17, #0x10f
    // 0x8edec8: ldr             x3, [fp, x17]
    // 0x8edecc: r16 = 86400000000
    //     0x8edecc: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x8eded0: ldr             x16, [x16, #0x268]
    // 0x8eded4: mul             x1, x3, x16
    // 0x8eded8: StoreField: r0->field_7 = r1
    //     0x8eded8: stur            x1, [x0, #7]
    // 0x8ededc: r17 = -264
    //     0x8ededc: movn            x17, #0x107
    // 0x8edee0: ldr             x1, [fp, x17]
    // 0x8edee4: mov             x2, x0
    // 0x8edee8: r0 = add()
    //     0x8edee8: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x8edeec: ldur            x1, [fp, #-0x100]
    // 0x8edef0: mov             x2, x0
    // 0x8edef4: r0 = format()
    //     0x8edef4: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x8edef8: ldur            x2, [fp, #-0xe0]
    // 0x8edefc: stur            x0, [fp, #-0x100]
    // 0x8edf00: ArrayLoad: r1 = r2[0]  ; List_8
    //     0x8edf00: ldur            x1, [x2, #0x17]
    // 0x8edf04: r17 = -280
    //     0x8edf04: movn            x17, #0x117
    // 0x8edf08: str             x1, [fp, x17]
    // 0x8edf0c: LoadField: r3 = r2->field_f
    //     0x8edf0c: ldur            x3, [x2, #0xf]
    // 0x8edf10: stur            x3, [fp, #-0xe8]
    // 0x8edf14: r0 = HijriDate()
    //     0x8edf14: bl              #0x8ee738  ; AllocateHijriDateStub -> HijriDate (size=0x24)
    // 0x8edf18: r17 = -272
    //     0x8edf18: movn            x17, #0x10f
    // 0x8edf1c: ldr             x3, [fp, x17]
    // 0x8edf20: r17 = -264
    //     0x8edf20: movn            x17, #0x107
    // 0x8edf24: str             x0, [fp, x17]
    // 0x8edf28: StoreField: r0->field_7 = r3
    //     0x8edf28: stur            x3, [x0, #7]
    // 0x8edf2c: r17 = -280
    //     0x8edf2c: movn            x17, #0x117
    // 0x8edf30: ldr             x1, [fp, x17]
    // 0x8edf34: StoreField: r0->field_f = r1
    //     0x8edf34: stur            x1, [x0, #0xf]
    // 0x8edf38: ldur            x1, [fp, #-0xe8]
    // 0x8edf3c: ArrayStore: r0[0] = r1  ; List_8
    //     0x8edf3c: stur            x1, [x0, #0x17]
    // 0x8edf40: ldur            x4, [fp, #-0x100]
    // 0x8edf44: StoreField: r0->field_1f = r4
    //     0x8edf44: stur            w4, [x0, #0x1f]
    // 0x8edf48: ldur            x1, [fp, #-0xf8]
    // 0x8edf4c: mov             x2, x4
    // 0x8edf50: r0 = _hashCode()
    //     0x8edf50: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x8edf54: ldur            x1, [fp, #-0xf8]
    // 0x8edf58: ldur            x2, [fp, #-0x100]
    // 0x8edf5c: r17 = -264
    //     0x8edf5c: movn            x17, #0x107
    // 0x8edf60: ldr             x3, [fp, x17]
    // 0x8edf64: mov             x5, x0
    // 0x8edf68: stur            x0, [fp, #-0xe8]
    // 0x8edf6c: r0 = _set()
    //     0x8edf6c: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x8edf70: ldur            x4, [fp, #-0xd8]
    // 0x8edf74: r17 = -272
    //     0x8edf74: movn            x17, #0x10f
    // 0x8edf78: ldr             x3, [fp, x17]
    // 0x8edf7c: ldur            x2, [fp, #-0xe0]
    // 0x8edf80: ldur            x0, [fp, #-0xf0]
    // 0x8edf84: ldur            x1, [fp, #-0xf8]
    // 0x8edf88: b               #0x8ede0c
    // 0x8edf8c: ldur            x4, [fp, #-0xd8]
    // 0x8edf90: ldur            x2, [fp, #-0xf0]
    // 0x8edf94: ldur            x0, [fp, #-0xf8]
    // 0x8edf98: ldur            x1, [fp, #-0xa8]
    // 0x8edf9c: ldur            x3, [fp, #-0xc8]
    // 0x8edfa0: b               #0x8edd28
    // 0x8edfa4: ldur            x1, [fp, #-0xa0]
    // 0x8edfa8: mov             x0, x4
    // 0x8edfac: StoreField: r0->field_1f = rNULL
    //     0x8edfac: stur            NULL, [x0, #0x1f]
    // 0x8edfb0: LoadField: r0 = r1->field_b
    //     0x8edfb0: ldur            w0, [x1, #0xb]
    // 0x8edfb4: DecompressPointer r0
    //     0x8edfb4: add             x0, x0, HEAP, lsl #32
    // 0x8edfb8: stur            x0, [fp, #-0xd8]
    // 0x8edfbc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8edfbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8edfc0: ldr             x0, [x0, #0x2728]
    //     0x8edfc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8edfc8: cmp             w0, w16
    //     0x8edfcc: b.ne            #0x8edfd8
    //     0x8edfd0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8edfd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8edfd8: r16 = <HijriDate>
    //     0x8edfd8: ldr             x16, [PP, #0x7bc0]  ; [pp+0x7bc0] TypeArguments: <HijriDate>
    // 0x8edfdc: stp             x0, x16, [SP, #8]
    // 0x8edfe0: r16 = "v2_calendar"
    //     0x8edfe0: ldr             x16, [PP, #0x7c68]  ; [pp+0x7c68] "v2_calendar"
    // 0x8edfe4: str             x16, [SP]
    // 0x8edfe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8edfe8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8edfec: r0 = box()
    //     0x8edfec: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8edff0: mov             x1, x0
    // 0x8edff4: ldur            x2, [fp, #-0xc0]
    // 0x8edff8: r0 = putAll()
    //     0x8edff8: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x8edffc: r0 = DateTime()
    //     0x8edffc: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8ee000: mov             x1, x0
    // 0x8ee004: r0 = false
    //     0x8ee004: add             x0, NULL, #0x30  ; false
    // 0x8ee008: stur            x1, [fp, #-0xa0]
    // 0x8ee00c: StoreField: r1->field_13 = r0
    //     0x8ee00c: stur            w0, [x1, #0x13]
    // 0x8ee010: r0 = _getCurrentMicros()
    //     0x8ee010: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x8ee014: r1 = LoadInt32Instr(r0)
    //     0x8ee014: sbfx            x1, x0, #1, #0x1f
    //     0x8ee018: tbz             w0, #0, #0x8ee020
    //     0x8ee01c: ldur            x1, [x0, #7]
    // 0x8ee020: ldur            x0, [fp, #-0xa0]
    // 0x8ee024: StoreField: r0->field_7 = r1
    //     0x8ee024: stur            x1, [x0, #7]
    // 0x8ee028: ldur            x1, [fp, #-0xb0]
    // 0x8ee02c: r0 = _lastUpdateCalendar()
    //     0x8ee02c: bl              #0x8ee69c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_lastUpdateCalendar
    // 0x8ee030: ldur            x1, [fp, #-0xa0]
    // 0x8ee034: stur            x0, [fp, #-0xc0]
    // 0x8ee038: r0 = toIso8601String()
    //     0x8ee038: bl              #0xd318a4  ; [dart:core] DateTime::toIso8601String
    // 0x8ee03c: ldur            x1, [fp, #-0xc0]
    // 0x8ee040: mov             x2, x0
    // 0x8ee044: stur            x0, [fp, #-0xc0]
    // 0x8ee048: r0 = val=()
    //     0x8ee048: bl              #0x8ee0f4  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val=
    // 0x8ee04c: b               #0x8ee054
    // 0x8ee050: sub             SP, fp, #0x138
    // 0x8ee054: r0 = Null
    //     0x8ee054: mov             x0, NULL
    // 0x8ee058: r0 = ReturnAsyncNotFuture()
    //     0x8ee058: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ee05c: ldur            x1, [fp, #-0xa8]
    // 0x8ee060: ldur            x0, [fp, #-0xb8]
    // 0x8ee064: r0 = ConcurrentModificationError()
    //     0x8ee064: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8ee068: mov             x1, x0
    // 0x8ee06c: ldur            x0, [fp, #-0xa8]
    // 0x8ee070: stur            x1, [fp, #-0xa0]
    // 0x8ee074: StoreField: r1->field_b = r0
    //     0x8ee074: stur            w0, [x1, #0xb]
    // 0x8ee078: mov             x0, x1
    // 0x8ee07c: r0 = Throw()
    //     0x8ee07c: bl              #0xec04b8  ; ThrowStub
    // 0x8ee080: brk             #0
    // 0x8ee084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee084: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee088: b               #0x8edaa8
    // 0x8ee08c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee08c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee090: b               #0x8edd40
    // 0x8ee094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee098: b               #0x8ede2c
  }
  [closure] HijriResponse <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ee870, size: 0x50
    // 0x8ee870: EnterFrame
    //     0x8ee870: stp             fp, lr, [SP, #-0x10]!
    //     0x8ee874: mov             fp, SP
    // 0x8ee878: CheckStackOverflow
    //     0x8ee878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ee87c: cmp             SP, x16
    //     0x8ee880: b.ls            #0x8ee8b8
    // 0x8ee884: ldr             x0, [fp, #0x10]
    // 0x8ee888: r2 = Null
    //     0x8ee888: mov             x2, NULL
    // 0x8ee88c: r1 = Null
    //     0x8ee88c: mov             x1, NULL
    // 0x8ee890: r8 = Map<String, dynamic>
    //     0x8ee890: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ee894: r3 = Null
    //     0x8ee894: add             x3, PP, #0x40, lsl #12  ; [pp+0x40748] Null
    //     0x8ee898: ldr             x3, [x3, #0x748]
    // 0x8ee89c: r0 = Map<String, dynamic>()
    //     0x8ee89c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ee8a0: ldr             x2, [fp, #0x10]
    // 0x8ee8a4: r1 = Null
    //     0x8ee8a4: mov             x1, NULL
    // 0x8ee8a8: r0 = HijriResponse.fromJson()
    //     0x8ee8a8: bl              #0x8ee8c0  ; [package:nuonline/app/data/models/calendar.dart] HijriResponse::HijriResponse.fromJson
    // 0x8ee8ac: LeaveFrame
    //     0x8ee8ac: mov             SP, fp
    //     0x8ee8b0: ldp             fp, lr, [SP], #0x10
    // 0x8ee8b4: ret
    //     0x8ee8b4: ret             
    // 0x8ee8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee8bc: b               #0x8ee884
  }
}
