// lib: , url: package:nuonline/app/data/repositories/category_repository.dart

// class id: 1050070, size: 0x8
class :: {
}

// class id: 1107, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _CategoryRepository&Object&AutoSyncMixin extends Object
     with AutoSyncMixin {

  _ markAsSynced(/* No info */) async {
    // ** addr: 0x8aa530, size: 0x94
    // 0x8aa530: EnterFrame
    //     0x8aa530: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa534: mov             fp, SP
    // 0x8aa538: AllocStack(0x20)
    //     0x8aa538: sub             SP, SP, #0x20
    // 0x8aa53c: SetupParameters(_CategoryRepository&Object&AutoSyncMixin this /* r1 => r1, fp-0x10 */)
    //     0x8aa53c: stur            NULL, [fp, #-8]
    //     0x8aa540: stur            x1, [fp, #-0x10]
    // 0x8aa544: CheckStackOverflow
    //     0x8aa544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa548: cmp             SP, x16
    //     0x8aa54c: b.ls            #0x8aa5bc
    // 0x8aa550: InitAsync() -> Future<void?>
    //     0x8aa550: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8aa554: bl              #0x661298  ; InitAsyncStub
    // 0x8aa558: ldur            x1, [fp, #-0x10]
    // 0x8aa55c: r0 = syncStorage()
    //     0x8aa55c: bl              #0x8aa5c4  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::syncStorage
    // 0x8aa560: stur            x0, [fp, #-0x10]
    // 0x8aa564: r0 = DateTime()
    //     0x8aa564: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8aa568: mov             x1, x0
    // 0x8aa56c: r0 = false
    //     0x8aa56c: add             x0, NULL, #0x30  ; false
    // 0x8aa570: stur            x1, [fp, #-0x18]
    // 0x8aa574: StoreField: r1->field_13 = r0
    //     0x8aa574: stur            w0, [x1, #0x13]
    // 0x8aa578: r0 = _getCurrentMicros()
    //     0x8aa578: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x8aa57c: r1 = LoadInt32Instr(r0)
    //     0x8aa57c: sbfx            x1, x0, #1, #0x1f
    //     0x8aa580: tbz             w0, #0, #0x8aa588
    //     0x8aa584: ldur            x1, [x0, #7]
    // 0x8aa588: ldur            x0, [fp, #-0x18]
    // 0x8aa58c: StoreField: r0->field_7 = r1
    //     0x8aa58c: stur            x1, [x0, #7]
    // 0x8aa590: str             x0, [SP]
    // 0x8aa594: r0 = toString()
    //     0x8aa594: bl              #0xbffa50  ; [dart:core] DateTime::toString
    // 0x8aa598: ldur            x1, [fp, #-0x10]
    // 0x8aa59c: mov             x3, x0
    // 0x8aa5a0: r2 = "v2_categories"
    //     0x8aa5a0: ldr             x2, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8aa5a4: r0 = put()
    //     0x8aa5a4: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0x8aa5a8: mov             x1, x0
    // 0x8aa5ac: stur            x1, [fp, #-0x10]
    // 0x8aa5b0: r0 = Await()
    //     0x8aa5b0: bl              #0x661044  ; AwaitStub
    // 0x8aa5b4: r0 = Null
    //     0x8aa5b4: mov             x0, NULL
    // 0x8aa5b8: r0 = ReturnAsyncNotFuture()
    //     0x8aa5b8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aa5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa5bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa5c0: b               #0x8aa550
  }
  _ isNeedToSync(/* No info */) async {
    // ** addr: 0x8aac88, size: 0x1f4
    // 0x8aac88: EnterFrame
    //     0x8aac88: stp             fp, lr, [SP, #-0x10]!
    //     0x8aac8c: mov             fp, SP
    // 0x8aac90: AllocStack(0xa0)
    //     0x8aac90: sub             SP, SP, #0xa0
    // 0x8aac94: SetupParameters(_CategoryRepository&Object&AutoSyncMixin this /* r1 => r1, fp-0x68 */)
    //     0x8aac94: stur            NULL, [fp, #-8]
    //     0x8aac98: stur            x1, [fp, #-0x68]
    // 0x8aac9c: CheckStackOverflow
    //     0x8aac9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aaca0: cmp             SP, x16
    //     0x8aaca4: b.ls            #0x8aae74
    // 0x8aaca8: InitAsync() -> Future<bool>
    //     0x8aaca8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x8aacac: bl              #0x661298  ; InitAsyncStub
    // 0x8aacb0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aacb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aacb4: ldr             x0, [x0, #0x2728]
    //     0x8aacb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aacbc: cmp             w0, w16
    //     0x8aacc0: b.ne            #0x8aaccc
    //     0x8aacc4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aacc8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aaccc: r16 = <String>
    //     0x8aaccc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8aacd0: stp             x0, x16, [SP, #8]
    // 0x8aacd4: r16 = "v2_syncs"
    //     0x8aacd4: ldr             x16, [PP, #0x7c50]  ; [pp+0x7c50] "v2_syncs"
    // 0x8aacd8: str             x16, [SP]
    // 0x8aacdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aacdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aace0: r0 = box()
    //     0x8aace0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aace4: mov             x1, x0
    // 0x8aace8: r2 = "v2_categories"
    //     0x8aace8: ldr             x2, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8aacec: r0 = get()
    //     0x8aacec: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8aacf0: cmp             w0, NULL
    // 0x8aacf4: b.ne            #0x8aad00
    // 0x8aacf8: r0 = true
    //     0x8aacf8: add             x0, NULL, #0x20  ; true
    // 0x8aacfc: r0 = ReturnAsyncNotFuture()
    //     0x8aacfc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aad00: mov             x1, x0
    // 0x8aad04: r0 = tryParse()
    //     0x8aad04: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x8aad08: stur            x0, [fp, #-0x70]
    // 0x8aad0c: cmp             w0, NULL
    // 0x8aad10: b.ne            #0x8aad1c
    // 0x8aad14: r0 = true
    //     0x8aad14: add             x0, NULL, #0x20  ; true
    // 0x8aad18: r0 = ReturnAsyncNotFuture()
    //     0x8aad18: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aad1c: mov             x1, x0
    // 0x8aad20: r2 = 1
    //     0x8aad20: movz            x2, #0x1
    // 0x8aad24: r0 = DateTimeExtensions.addDay()
    //     0x8aad24: bl              #0x8aae7c  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.addDay
    // 0x8aad28: stur            x0, [fp, #-0x78]
    // 0x8aad2c: r0 = DateTime()
    //     0x8aad2c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8aad30: mov             x1, x0
    // 0x8aad34: r0 = false
    //     0x8aad34: add             x0, NULL, #0x30  ; false
    // 0x8aad38: stur            x1, [fp, #-0x80]
    // 0x8aad3c: StoreField: r1->field_13 = r0
    //     0x8aad3c: stur            w0, [x1, #0x13]
    // 0x8aad40: r0 = _getCurrentMicros()
    //     0x8aad40: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x8aad44: r1 = LoadInt32Instr(r0)
    //     0x8aad44: sbfx            x1, x0, #1, #0x1f
    //     0x8aad48: tbz             w0, #0, #0x8aad50
    //     0x8aad4c: ldur            x1, [x0, #7]
    // 0x8aad50: ldur            x2, [fp, #-0x80]
    // 0x8aad54: StoreField: r2->field_7 = r1
    //     0x8aad54: stur            x1, [x2, #7]
    // 0x8aad58: ldur            x1, [fp, #-0x78]
    // 0x8aad5c: r0 = isAfter()
    //     0x8aad5c: bl              #0xd61fd4  ; [dart:core] DateTime::isAfter
    // 0x8aad60: tbnz            w0, #4, #0x8aad6c
    // 0x8aad64: r0 = false
    //     0x8aad64: add             x0, NULL, #0x30  ; false
    // 0x8aad68: r0 = ReturnAsyncNotFuture()
    //     0x8aad68: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aad6c: ldur            x0, [fp, #-0x68]
    // 0x8aad70: LoadField: r1 = r0->field_7
    //     0x8aad70: ldur            w1, [x0, #7]
    // 0x8aad74: DecompressPointer r1
    //     0x8aad74: add             x1, x1, HEAP, lsl #32
    // 0x8aad78: stp             x1, NULL, [SP, #0x10]
    // 0x8aad7c: r16 = "/categories/sync"
    //     0x8aad7c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36420] "/categories/sync"
    //     0x8aad80: ldr             x16, [x16, #0x420]
    // 0x8aad84: r30 = false
    //     0x8aad84: add             lr, NULL, #0x30  ; false
    // 0x8aad88: stp             lr, x16, [SP]
    // 0x8aad8c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8aad8c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8aad90: r0 = withRetry()
    //     0x8aad90: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x8aad94: mov             x1, x0
    // 0x8aad98: stur            x1, [fp, #-0x68]
    // 0x8aad9c: r0 = Await()
    //     0x8aad9c: bl              #0x661044  ; AwaitStub
    // 0x8aada0: LoadField: r3 = r0->field_b
    //     0x8aada0: ldur            w3, [x0, #0xb]
    // 0x8aada4: DecompressPointer r3
    //     0x8aada4: add             x3, x3, HEAP, lsl #32
    // 0x8aada8: mov             x0, x3
    // 0x8aadac: stur            x3, [fp, #-0x68]
    // 0x8aadb0: r2 = Null
    //     0x8aadb0: mov             x2, NULL
    // 0x8aadb4: r1 = Null
    //     0x8aadb4: mov             x1, NULL
    // 0x8aadb8: r8 = Map<String, dynamic>
    //     0x8aadb8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8aadbc: r3 = Null
    //     0x8aadbc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36428] Null
    //     0x8aadc0: ldr             x3, [x3, #0x428]
    // 0x8aadc4: r0 = Map<String, dynamic>()
    //     0x8aadc4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8aadc8: ldur            x1, [fp, #-0x68]
    // 0x8aadcc: r0 = LoadClassIdInstr(r1)
    //     0x8aadcc: ldur            x0, [x1, #-1]
    //     0x8aadd0: ubfx            x0, x0, #0xc, #0x14
    // 0x8aadd4: r2 = "updated_at"
    //     0x8aadd4: add             x2, PP, #0x17, lsl #12  ; [pp+0x17e88] "updated_at"
    //     0x8aadd8: ldr             x2, [x2, #0xe88]
    // 0x8aaddc: r0 = GDT[cid_x0 + -0x114]()
    //     0x8aaddc: sub             lr, x0, #0x114
    //     0x8aade0: ldr             lr, [x21, lr, lsl #3]
    //     0x8aade4: blr             lr
    // 0x8aade8: mov             x3, x0
    // 0x8aadec: r2 = Null
    //     0x8aadec: mov             x2, NULL
    // 0x8aadf0: r1 = Null
    //     0x8aadf0: mov             x1, NULL
    // 0x8aadf4: stur            x3, [fp, #-0x68]
    // 0x8aadf8: r4 = 60
    //     0x8aadf8: movz            x4, #0x3c
    // 0x8aadfc: branchIfSmi(r0, 0x8aae08)
    //     0x8aadfc: tbz             w0, #0, #0x8aae08
    // 0x8aae00: r4 = LoadClassIdInstr(r0)
    //     0x8aae00: ldur            x4, [x0, #-1]
    //     0x8aae04: ubfx            x4, x4, #0xc, #0x14
    // 0x8aae08: sub             x4, x4, #0x5e
    // 0x8aae0c: cmp             x4, #1
    // 0x8aae10: b.ls            #0x8aae24
    // 0x8aae14: r8 = String
    //     0x8aae14: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8aae18: r3 = Null
    //     0x8aae18: add             x3, PP, #0x36, lsl #12  ; [pp+0x36438] Null
    //     0x8aae1c: ldr             x3, [x3, #0x438]
    // 0x8aae20: r0 = String()
    //     0x8aae20: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8aae24: ldur            x1, [fp, #-0x68]
    // 0x8aae28: r0 = tryParse()
    //     0x8aae28: bl              #0x6fe140  ; [dart:core] DateTime::tryParse
    // 0x8aae2c: cmp             w0, NULL
    // 0x8aae30: b.ne            #0x8aae3c
    // 0x8aae34: r0 = true
    //     0x8aae34: add             x0, NULL, #0x20  ; true
    // 0x8aae38: r0 = ReturnAsyncNotFuture()
    //     0x8aae38: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aae3c: mov             x1, x0
    // 0x8aae40: r0 = toLocal()
    //     0x8aae40: bl              #0x82c4e0  ; [dart:core] DateTime::toLocal
    // 0x8aae44: ldur            x1, [fp, #-0x70]
    // 0x8aae48: mov             x2, x0
    // 0x8aae4c: r0 = compareTo()
    //     0x8aae4c: bl              #0x665dbc  ; [dart:core] DateTime::compareTo
    // 0x8aae50: tbnz            x0, #0x3f, #0x8aae5c
    // 0x8aae54: r1 = false
    //     0x8aae54: add             x1, NULL, #0x30  ; false
    // 0x8aae58: b               #0x8aae60
    // 0x8aae5c: r1 = true
    //     0x8aae5c: add             x1, NULL, #0x20  ; true
    // 0x8aae60: mov             x0, x1
    // 0x8aae64: r0 = ReturnAsyncNotFuture()
    //     0x8aae64: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aae68: sub             SP, fp, #0xa0
    // 0x8aae6c: r0 = false
    //     0x8aae6c: add             x0, NULL, #0x30  ; false
    // 0x8aae70: r0 = ReturnAsyncNotFuture()
    //     0x8aae70: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aae74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aae74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aae78: b               #0x8aaca8
  }
}

// class id: 1108, size: 0x10, field offset: 0x8
class CategoryRepository extends _CategoryRepository&Object&AutoSyncMixin {

  _ findAll(/* No info */) async {
    // ** addr: 0x8aa274, size: 0x25c
    // 0x8aa274: EnterFrame
    //     0x8aa274: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa278: mov             fp, SP
    // 0x8aa27c: AllocStack(0x90)
    //     0x8aa27c: sub             SP, SP, #0x90
    // 0x8aa280: SetupParameters(CategoryRepository this /* r1 => r1, fp-0x68 */)
    //     0x8aa280: stur            NULL, [fp, #-8]
    //     0x8aa284: stur            x1, [fp, #-0x68]
    // 0x8aa288: CheckStackOverflow
    //     0x8aa288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa28c: cmp             SP, x16
    //     0x8aa290: b.ls            #0x8aa4b8
    // 0x8aa294: r1 = 1
    //     0x8aa294: movz            x1, #0x1
    // 0x8aa298: r0 = AllocateContext()
    //     0x8aa298: bl              #0xec126c  ; AllocateContextStub
    // 0x8aa29c: mov             x2, x0
    // 0x8aa2a0: ldur            x1, [fp, #-0x68]
    // 0x8aa2a4: stur            x2, [fp, #-0x70]
    // 0x8aa2a8: StoreField: r2->field_f = r1
    //     0x8aa2a8: stur            w1, [x2, #0xf]
    // 0x8aa2ac: InitAsync() -> Future<ApiResult<List<Category>>>
    //     0x8aa2ac: add             x0, PP, #0x36, lsl #12  ; [pp+0x363b8] TypeArguments: <ApiResult<List<Category>>>
    //     0x8aa2b0: ldr             x0, [x0, #0x3b8]
    //     0x8aa2b4: bl              #0x661298  ; InitAsyncStub
    // 0x8aa2b8: ldur            x1, [fp, #-0x68]
    // 0x8aa2bc: r0 = isNeedToSync()
    //     0x8aa2bc: bl              #0x8aac88  ; [package:nuonline/app/data/repositories/category_repository.dart] _CategoryRepository&Object&AutoSyncMixin::isNeedToSync
    // 0x8aa2c0: mov             x1, x0
    // 0x8aa2c4: stur            x1, [fp, #-0x78]
    // 0x8aa2c8: r0 = Await()
    //     0x8aa2c8: bl              #0x661044  ; AwaitStub
    // 0x8aa2cc: r16 = true
    //     0x8aa2cc: add             x16, NULL, #0x20  ; true
    // 0x8aa2d0: cmp             w0, w16
    // 0x8aa2d4: b.eq            #0x8aa368
    // 0x8aa2d8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aa2d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aa2dc: ldr             x0, [x0, #0x2728]
    //     0x8aa2e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aa2e4: cmp             w0, w16
    //     0x8aa2e8: b.ne            #0x8aa2f4
    //     0x8aa2ec: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aa2f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aa2f4: r16 = <Category>
    //     0x8aa2f4: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aa2f8: stp             x0, x16, [SP, #8]
    // 0x8aa2fc: r16 = "v2_categories"
    //     0x8aa2fc: ldr             x16, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8aa300: str             x16, [SP]
    // 0x8aa304: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa304: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa308: r0 = box()
    //     0x8aa308: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aa30c: mov             x1, x0
    // 0x8aa310: stur            x0, [fp, #-0x78]
    // 0x8aa314: r0 = checkOpen()
    //     0x8aa314: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aa318: ldur            x0, [fp, #-0x78]
    // 0x8aa31c: LoadField: r1 = r0->field_1b
    //     0x8aa31c: ldur            w1, [x0, #0x1b]
    // 0x8aa320: DecompressPointer r1
    //     0x8aa320: add             x1, x1, HEAP, lsl #32
    // 0x8aa324: r16 = Sentinel
    //     0x8aa324: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aa328: cmp             w1, w16
    // 0x8aa32c: b.eq            #0x8aa4c0
    // 0x8aa330: r0 = getValues()
    //     0x8aa330: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8aa334: LoadField: r1 = r0->field_7
    //     0x8aa334: ldur            w1, [x0, #7]
    // 0x8aa338: DecompressPointer r1
    //     0x8aa338: add             x1, x1, HEAP, lsl #32
    // 0x8aa33c: mov             x2, x0
    // 0x8aa340: r0 = _GrowableList.of()
    //     0x8aa340: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8aa344: r1 = <List<Category>>
    //     0x8aa344: add             x1, PP, #0x30, lsl #12  ; [pp+0x30ba0] TypeArguments: <List<Category>>
    //     0x8aa348: ldr             x1, [x1, #0xba0]
    // 0x8aa34c: stur            x0, [fp, #-0x78]
    // 0x8aa350: r0 = _$SuccessImpl()
    //     0x8aa350: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8aa354: mov             x1, x0
    // 0x8aa358: ldur            x0, [fp, #-0x78]
    // 0x8aa35c: StoreField: r1->field_b = r0
    //     0x8aa35c: stur            w0, [x1, #0xb]
    // 0x8aa360: mov             x0, x1
    // 0x8aa364: r0 = ReturnAsyncNotFuture()
    //     0x8aa364: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aa368: ldur            x1, [fp, #-0x68]
    // 0x8aa36c: LoadField: r0 = r1->field_7
    //     0x8aa36c: ldur            w0, [x1, #7]
    // 0x8aa370: DecompressPointer r0
    //     0x8aa370: add             x0, x0, HEAP, lsl #32
    // 0x8aa374: stp             x0, NULL, [SP, #8]
    // 0x8aa378: r16 = "/categories"
    //     0x8aa378: add             x16, PP, #0x36, lsl #12  ; [pp+0x363c0] "/categories"
    //     0x8aa37c: ldr             x16, [x16, #0x3c0]
    // 0x8aa380: str             x16, [SP]
    // 0x8aa384: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa384: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa388: r0 = get()
    //     0x8aa388: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8aa38c: mov             x1, x0
    // 0x8aa390: stur            x1, [fp, #-0x78]
    // 0x8aa394: r0 = Await()
    //     0x8aa394: bl              #0x661044  ; AwaitStub
    // 0x8aa398: LoadField: r1 = r0->field_b
    //     0x8aa398: ldur            w1, [x0, #0xb]
    // 0x8aa39c: DecompressPointer r1
    //     0x8aa39c: add             x1, x1, HEAP, lsl #32
    // 0x8aa3a0: r0 = fromResponse()
    //     0x8aa3a0: bl              #0x8aa75c  ; [package:nuonline/app/data/models/category.dart] Category::fromResponse
    // 0x8aa3a4: ldur            x2, [fp, #-0x70]
    // 0x8aa3a8: r1 = Function '<anonymous closure>':.
    //     0x8aa3a8: add             x1, PP, #0x36, lsl #12  ; [pp+0x363c8] AnonymousClosure: (0x8aaed8), in [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::findAll (0x8aa274)
    //     0x8aa3ac: ldr             x1, [x1, #0x3c8]
    // 0x8aa3b0: stur            x0, [fp, #-0x78]
    // 0x8aa3b4: r0 = AllocateClosure()
    //     0x8aa3b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aa3b8: r16 = <Category>
    //     0x8aa3b8: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aa3bc: ldur            lr, [fp, #-0x78]
    // 0x8aa3c0: stp             lr, x16, [SP, #8]
    // 0x8aa3c4: str             x0, [SP]
    // 0x8aa3c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa3c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa3cc: r0 = map()
    //     0x8aa3cc: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x8aa3d0: stur            x0, [fp, #-0x78]
    // 0x8aa3d4: LoadField: r3 = r0->field_7
    //     0x8aa3d4: ldur            w3, [x0, #7]
    // 0x8aa3d8: DecompressPointer r3
    //     0x8aa3d8: add             x3, x3, HEAP, lsl #32
    // 0x8aa3dc: mov             x1, x3
    // 0x8aa3e0: mov             x2, x0
    // 0x8aa3e4: stur            x3, [fp, #-0x70]
    // 0x8aa3e8: r0 = _GrowableList.of()
    //     0x8aa3e8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8aa3ec: ldur            x1, [fp, #-0x68]
    // 0x8aa3f0: mov             x2, x0
    // 0x8aa3f4: r0 = updateAll()
    //     0x8aa3f4: bl              #0x8aa65c  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::updateAll
    // 0x8aa3f8: mov             x1, x0
    // 0x8aa3fc: stur            x1, [fp, #-0x70]
    // 0x8aa400: r0 = Await()
    //     0x8aa400: bl              #0x661044  ; AwaitStub
    // 0x8aa404: ldur            x1, [fp, #-0x68]
    // 0x8aa408: r0 = markAsSynced()
    //     0x8aa408: bl              #0x8aa530  ; [package:nuonline/app/data/repositories/category_repository.dart] _CategoryRepository&Object&AutoSyncMixin::markAsSynced
    // 0x8aa40c: mov             x1, x0
    // 0x8aa410: stur            x1, [fp, #-0x70]
    // 0x8aa414: r0 = Await()
    //     0x8aa414: bl              #0x661044  ; AwaitStub
    // 0x8aa418: ldur            x0, [fp, #-0x68]
    // 0x8aa41c: LoadField: r1 = r0->field_b
    //     0x8aa41c: ldur            w1, [x0, #0xb]
    // 0x8aa420: DecompressPointer r1
    //     0x8aa420: add             x1, x1, HEAP, lsl #32
    // 0x8aa424: r0 = categories()
    //     0x8aa424: bl              #0x8aa4d0  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::categories
    // 0x8aa428: mov             x1, x0
    // 0x8aa42c: stur            x0, [fp, #-0x68]
    // 0x8aa430: r0 = checkOpen()
    //     0x8aa430: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aa434: ldur            x0, [fp, #-0x68]
    // 0x8aa438: LoadField: r1 = r0->field_1b
    //     0x8aa438: ldur            w1, [x0, #0x1b]
    // 0x8aa43c: DecompressPointer r1
    //     0x8aa43c: add             x1, x1, HEAP, lsl #32
    // 0x8aa440: r16 = Sentinel
    //     0x8aa440: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aa444: cmp             w1, w16
    // 0x8aa448: b.eq            #0x8aa4c8
    // 0x8aa44c: r0 = getValues()
    //     0x8aa44c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8aa450: stur            x0, [fp, #-0x70]
    // 0x8aa454: LoadField: r3 = r0->field_7
    //     0x8aa454: ldur            w3, [x0, #7]
    // 0x8aa458: DecompressPointer r3
    //     0x8aa458: add             x3, x3, HEAP, lsl #32
    // 0x8aa45c: mov             x1, x3
    // 0x8aa460: mov             x2, x0
    // 0x8aa464: stur            x3, [fp, #-0x68]
    // 0x8aa468: r0 = _GrowableList.of()
    //     0x8aa468: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8aa46c: r1 = <List<Category>>
    //     0x8aa46c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30ba0] TypeArguments: <List<Category>>
    //     0x8aa470: ldr             x1, [x1, #0xba0]
    // 0x8aa474: stur            x0, [fp, #-0x68]
    // 0x8aa478: r0 = _$SuccessImpl()
    //     0x8aa478: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8aa47c: mov             x1, x0
    // 0x8aa480: ldur            x0, [fp, #-0x68]
    // 0x8aa484: StoreField: r1->field_b = r0
    //     0x8aa484: stur            w0, [x1, #0xb]
    // 0x8aa488: mov             x0, x1
    // 0x8aa48c: r0 = ReturnAsyncNotFuture()
    //     0x8aa48c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aa490: sub             SP, fp, #0x90
    // 0x8aa494: mov             x1, x0
    // 0x8aa498: r0 = getDioException()
    //     0x8aa498: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8aa49c: r1 = <List<Category>>
    //     0x8aa49c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30ba0] TypeArguments: <List<Category>>
    //     0x8aa4a0: ldr             x1, [x1, #0xba0]
    // 0x8aa4a4: stur            x0, [fp, #-0x68]
    // 0x8aa4a8: r0 = _$FailureImpl()
    //     0x8aa4a8: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8aa4ac: ldur            x1, [fp, #-0x68]
    // 0x8aa4b0: StoreField: r0->field_b = r1
    //     0x8aa4b0: stur            w1, [x0, #0xb]
    // 0x8aa4b4: r0 = ReturnAsyncNotFuture()
    //     0x8aa4b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aa4b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa4b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa4bc: b               #0x8aa294
    // 0x8aa4c0: r9 = keystore
    //     0x8aa4c0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8aa4c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aa4c4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8aa4c8: r9 = keystore
    //     0x8aa4c8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8aa4cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aa4cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ syncStorage(/* No info */) {
    // ** addr: 0x8aa5c4, size: 0x38
    // 0x8aa5c4: EnterFrame
    //     0x8aa5c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa5c8: mov             fp, SP
    // 0x8aa5cc: CheckStackOverflow
    //     0x8aa5cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa5d0: cmp             SP, x16
    //     0x8aa5d4: b.ls            #0x8aa5f4
    // 0x8aa5d8: LoadField: r0 = r1->field_b
    //     0x8aa5d8: ldur            w0, [x1, #0xb]
    // 0x8aa5dc: DecompressPointer r0
    //     0x8aa5dc: add             x0, x0, HEAP, lsl #32
    // 0x8aa5e0: mov             x1, x0
    // 0x8aa5e4: r0 = syncs()
    //     0x8aa5e4: bl              #0x8aa5fc  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::syncs
    // 0x8aa5e8: LeaveFrame
    //     0x8aa5e8: mov             SP, fp
    //     0x8aa5ec: ldp             fp, lr, [SP], #0x10
    // 0x8aa5f0: ret
    //     0x8aa5f0: ret             
    // 0x8aa5f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa5f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa5f8: b               #0x8aa5d8
  }
  _ updateAll(/* No info */) async {
    // ** addr: 0x8aa65c, size: 0xb4
    // 0x8aa65c: EnterFrame
    //     0x8aa65c: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa660: mov             fp, SP
    // 0x8aa664: AllocStack(0x30)
    //     0x8aa664: sub             SP, SP, #0x30
    // 0x8aa668: SetupParameters(CategoryRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8aa668: stur            NULL, [fp, #-8]
    //     0x8aa66c: stur            x1, [fp, #-0x10]
    //     0x8aa670: stur            x2, [fp, #-0x18]
    // 0x8aa674: CheckStackOverflow
    //     0x8aa674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa678: cmp             SP, x16
    //     0x8aa67c: b.ls            #0x8aa708
    // 0x8aa680: InitAsync() -> Future<void?>
    //     0x8aa680: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8aa684: bl              #0x661298  ; InitAsyncStub
    // 0x8aa688: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aa688: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aa68c: ldr             x0, [x0, #0x2728]
    //     0x8aa690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aa694: cmp             w0, w16
    //     0x8aa698: b.ne            #0x8aa6a4
    //     0x8aa69c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aa6a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aa6a4: r16 = <Category>
    //     0x8aa6a4: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aa6a8: stp             x0, x16, [SP, #8]
    // 0x8aa6ac: r16 = "v2_categories"
    //     0x8aa6ac: ldr             x16, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8aa6b0: str             x16, [SP]
    // 0x8aa6b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa6b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa6b8: r0 = box()
    //     0x8aa6b8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aa6bc: r1 = Function '<anonymous closure>':.
    //     0x8aa6bc: add             x1, PP, #0x36, lsl #12  ; [pp+0x363d0] AnonymousClosure: (0x8aa710), in [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::updateAll (0x8aa65c)
    //     0x8aa6c0: ldr             x1, [x1, #0x3d0]
    // 0x8aa6c4: r2 = Null
    //     0x8aa6c4: mov             x2, NULL
    // 0x8aa6c8: stur            x0, [fp, #-0x10]
    // 0x8aa6cc: r0 = AllocateClosure()
    //     0x8aa6cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aa6d0: ldur            x2, [fp, #-0x18]
    // 0x8aa6d4: mov             x3, x0
    // 0x8aa6d8: r1 = <dynamic, Category>
    //     0x8aa6d8: add             x1, PP, #0x36, lsl #12  ; [pp+0x363d8] TypeArguments: <dynamic, Category>
    //     0x8aa6dc: ldr             x1, [x1, #0x3d8]
    // 0x8aa6e0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8aa6e0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8aa6e4: r0 = LinkedHashMap.fromIterable()
    //     0x8aa6e4: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x8aa6e8: ldur            x1, [fp, #-0x10]
    // 0x8aa6ec: mov             x2, x0
    // 0x8aa6f0: r0 = putAll()
    //     0x8aa6f0: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x8aa6f4: mov             x1, x0
    // 0x8aa6f8: stur            x1, [fp, #-0x10]
    // 0x8aa6fc: r0 = Await()
    //     0x8aa6fc: bl              #0x661044  ; AwaitStub
    // 0x8aa700: r0 = Null
    //     0x8aa700: mov             x0, NULL
    // 0x8aa704: r0 = ReturnAsyncNotFuture()
    //     0x8aa704: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aa708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa708: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa70c: b               #0x8aa680
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8aa710, size: 0x4c
    // 0x8aa710: EnterFrame
    //     0x8aa710: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa714: mov             fp, SP
    // 0x8aa718: AllocStack(0x8)
    //     0x8aa718: sub             SP, SP, #8
    // 0x8aa71c: CheckStackOverflow
    //     0x8aa71c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa720: cmp             SP, x16
    //     0x8aa724: b.ls            #0x8aa754
    // 0x8aa728: ldr             x16, [fp, #0x10]
    // 0x8aa72c: str             x16, [SP]
    // 0x8aa730: r4 = 0
    //     0x8aa730: movz            x4, #0
    // 0x8aa734: ldr             x0, [SP]
    // 0x8aa738: r16 = UnlinkedCall_0x5f3c08
    //     0x8aa738: add             x16, PP, #0x36, lsl #12  ; [pp+0x363e0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8aa73c: add             x16, x16, #0x3e0
    // 0x8aa740: ldp             x5, lr, [x16]
    // 0x8aa744: blr             lr
    // 0x8aa748: LeaveFrame
    //     0x8aa748: mov             SP, fp
    //     0x8aa74c: ldp             fp, lr, [SP], #0x10
    // 0x8aa750: ret
    //     0x8aa750: ret             
    // 0x8aa754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa754: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa758: b               #0x8aa728
  }
  [closure] Category <anonymous closure>(dynamic, Category) {
    // ** addr: 0x8aaed8, size: 0x218
    // 0x8aaed8: EnterFrame
    //     0x8aaed8: stp             fp, lr, [SP, #-0x10]!
    //     0x8aaedc: mov             fp, SP
    // 0x8aaee0: AllocStack(0x30)
    //     0x8aaee0: sub             SP, SP, #0x30
    // 0x8aaee4: SetupParameters()
    //     0x8aaee4: ldr             x0, [fp, #0x18]
    //     0x8aaee8: ldur            w1, [x0, #0x17]
    //     0x8aaeec: add             x1, x1, HEAP, lsl #32
    //     0x8aaef0: stur            x1, [fp, #-8]
    // 0x8aaef4: CheckStackOverflow
    //     0x8aaef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aaef8: cmp             SP, x16
    //     0x8aaefc: b.ls            #0x8ab0e0
    // 0x8aaf00: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aaf00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aaf04: ldr             x0, [x0, #0x2728]
    //     0x8aaf08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aaf0c: cmp             w0, w16
    //     0x8aaf10: b.ne            #0x8aaf1c
    //     0x8aaf14: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aaf18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aaf1c: stur            x0, [fp, #-0x10]
    // 0x8aaf20: r16 = <Category>
    //     0x8aaf20: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8aaf24: stp             x0, x16, [SP, #8]
    // 0x8aaf28: r16 = "v2_categories"
    //     0x8aaf28: ldr             x16, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8aaf2c: str             x16, [SP]
    // 0x8aaf30: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aaf30: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aaf34: r0 = box()
    //     0x8aaf34: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aaf38: mov             x1, x0
    // 0x8aaf3c: stur            x0, [fp, #-0x18]
    // 0x8aaf40: r0 = checkOpen()
    //     0x8aaf40: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aaf44: ldur            x0, [fp, #-0x18]
    // 0x8aaf48: LoadField: r1 = r0->field_1b
    //     0x8aaf48: ldur            w1, [x0, #0x1b]
    // 0x8aaf4c: DecompressPointer r1
    //     0x8aaf4c: add             x1, x1, HEAP, lsl #32
    // 0x8aaf50: r16 = Sentinel
    //     0x8aaf50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aaf54: cmp             w1, w16
    // 0x8aaf58: b.eq            #0x8ab0e8
    // 0x8aaf5c: LoadField: r0 = r1->field_13
    //     0x8aaf5c: ldur            w0, [x1, #0x13]
    // 0x8aaf60: DecompressPointer r0
    //     0x8aaf60: add             x0, x0, HEAP, lsl #32
    // 0x8aaf64: LoadField: r1 = r0->field_1f
    //     0x8aaf64: ldur            x1, [x0, #0x1f]
    // 0x8aaf68: cbnz            x1, #0x8ab044
    // 0x8aaf6c: ldr             x2, [fp, #0x10]
    // 0x8aaf70: ldur            x0, [fp, #-8]
    // 0x8aaf74: LoadField: r1 = r0->field_f
    //     0x8aaf74: ldur            w1, [x0, #0xf]
    // 0x8aaf78: DecompressPointer r1
    //     0x8aaf78: add             x1, x1, HEAP, lsl #32
    // 0x8aaf7c: r0 = defaultSelectedIDs()
    //     0x8aaf7c: bl              #0x8ab1d0  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::defaultSelectedIDs
    // 0x8aaf80: mov             x2, x0
    // 0x8aaf84: ldr             x3, [fp, #0x10]
    // 0x8aaf88: LoadField: r4 = r3->field_13
    //     0x8aaf88: ldur            x4, [x3, #0x13]
    // 0x8aaf8c: r0 = BoxInt64Instr(r4)
    //     0x8aaf8c: sbfiz           x0, x4, #1, #0x1f
    //     0x8aaf90: cmp             x4, x0, asr #1
    //     0x8aaf94: b.eq            #0x8aafa0
    //     0x8aaf98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aaf9c: stur            x4, [x0, #7]
    // 0x8aafa0: mov             x1, x2
    // 0x8aafa4: mov             x2, x0
    // 0x8aafa8: r0 = containsKey()
    //     0x8aafa8: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x8aafac: tbnz            w0, #4, #0x8ab034
    // 0x8aafb0: ldr             x0, [fp, #0x10]
    // 0x8aafb4: ldur            x1, [fp, #-8]
    // 0x8aafb8: LoadField: r2 = r1->field_f
    //     0x8aafb8: ldur            w2, [x1, #0xf]
    // 0x8aafbc: DecompressPointer r2
    //     0x8aafbc: add             x2, x2, HEAP, lsl #32
    // 0x8aafc0: mov             x1, x2
    // 0x8aafc4: r0 = defaultSelectedIDs()
    //     0x8aafc4: bl              #0x8ab1d0  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::defaultSelectedIDs
    // 0x8aafc8: mov             x4, x0
    // 0x8aafcc: ldr             x3, [fp, #0x10]
    // 0x8aafd0: stur            x4, [fp, #-8]
    // 0x8aafd4: LoadField: r2 = r3->field_13
    //     0x8aafd4: ldur            x2, [x3, #0x13]
    // 0x8aafd8: r0 = BoxInt64Instr(r2)
    //     0x8aafd8: sbfiz           x0, x2, #1, #0x1f
    //     0x8aafdc: cmp             x2, x0, asr #1
    //     0x8aafe0: b.eq            #0x8aafec
    //     0x8aafe4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aafe8: stur            x2, [x0, #7]
    // 0x8aafec: mov             x1, x4
    // 0x8aaff0: mov             x2, x0
    // 0x8aaff4: r0 = _getValueOrData()
    //     0x8aaff4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8aaff8: mov             x1, x0
    // 0x8aaffc: ldur            x0, [fp, #-8]
    // 0x8ab000: LoadField: r2 = r0->field_f
    //     0x8ab000: ldur            w2, [x0, #0xf]
    // 0x8ab004: DecompressPointer r2
    //     0x8ab004: add             x2, x2, HEAP, lsl #32
    // 0x8ab008: cmp             w2, w1
    // 0x8ab00c: b.ne            #0x8ab018
    // 0x8ab010: r2 = Null
    //     0x8ab010: mov             x2, NULL
    // 0x8ab014: b               #0x8ab01c
    // 0x8ab018: mov             x2, x1
    // 0x8ab01c: ldr             x1, [fp, #0x10]
    // 0x8ab020: r3 = true
    //     0x8ab020: add             x3, NULL, #0x20  ; true
    // 0x8ab024: r0 = copyWith()
    //     0x8ab024: bl              #0x8ab0f0  ; [package:nuonline/app/data/models/category.dart] Category::copyWith
    // 0x8ab028: LeaveFrame
    //     0x8ab028: mov             SP, fp
    //     0x8ab02c: ldp             fp, lr, [SP], #0x10
    // 0x8ab030: ret
    //     0x8ab030: ret             
    // 0x8ab034: ldr             x0, [fp, #0x10]
    // 0x8ab038: LeaveFrame
    //     0x8ab038: mov             SP, fp
    //     0x8ab03c: ldp             fp, lr, [SP], #0x10
    // 0x8ab040: ret
    //     0x8ab040: ret             
    // 0x8ab044: ldr             x1, [fp, #0x10]
    // 0x8ab048: r16 = <Category>
    //     0x8ab048: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x8ab04c: ldur            lr, [fp, #-0x10]
    // 0x8ab050: stp             lr, x16, [SP, #8]
    // 0x8ab054: r16 = "v2_categories"
    //     0x8ab054: ldr             x16, [PP, #0x7bf0]  ; [pp+0x7bf0] "v2_categories"
    // 0x8ab058: str             x16, [SP]
    // 0x8ab05c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ab05c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ab060: r0 = box()
    //     0x8ab060: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ab064: mov             x2, x0
    // 0x8ab068: ldr             x3, [fp, #0x10]
    // 0x8ab06c: LoadField: r4 = r3->field_13
    //     0x8ab06c: ldur            x4, [x3, #0x13]
    // 0x8ab070: r0 = BoxInt64Instr(r4)
    //     0x8ab070: sbfiz           x0, x4, #1, #0x1f
    //     0x8ab074: cmp             x4, x0, asr #1
    //     0x8ab078: b.eq            #0x8ab084
    //     0x8ab07c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ab080: stur            x4, [x0, #7]
    // 0x8ab084: mov             x1, x2
    // 0x8ab088: mov             x2, x0
    // 0x8ab08c: r0 = get()
    //     0x8ab08c: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8ab090: cmp             w0, NULL
    // 0x8ab094: b.eq            #0x8ab0d0
    // 0x8ab098: LoadField: r2 = r0->field_27
    //     0x8ab098: ldur            x2, [x0, #0x27]
    // 0x8ab09c: LoadField: r3 = r0->field_23
    //     0x8ab09c: ldur            w3, [x0, #0x23]
    // 0x8ab0a0: DecompressPointer r3
    //     0x8ab0a0: add             x3, x3, HEAP, lsl #32
    // 0x8ab0a4: r0 = BoxInt64Instr(r2)
    //     0x8ab0a4: sbfiz           x0, x2, #1, #0x1f
    //     0x8ab0a8: cmp             x2, x0, asr #1
    //     0x8ab0ac: b.eq            #0x8ab0b8
    //     0x8ab0b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ab0b4: stur            x2, [x0, #7]
    // 0x8ab0b8: ldr             x1, [fp, #0x10]
    // 0x8ab0bc: mov             x2, x0
    // 0x8ab0c0: r0 = copyWith()
    //     0x8ab0c0: bl              #0x8ab0f0  ; [package:nuonline/app/data/models/category.dart] Category::copyWith
    // 0x8ab0c4: LeaveFrame
    //     0x8ab0c4: mov             SP, fp
    //     0x8ab0c8: ldp             fp, lr, [SP], #0x10
    // 0x8ab0cc: ret
    //     0x8ab0cc: ret             
    // 0x8ab0d0: ldr             x0, [fp, #0x10]
    // 0x8ab0d4: LeaveFrame
    //     0x8ab0d4: mov             SP, fp
    //     0x8ab0d8: ldp             fp, lr, [SP], #0x10
    // 0x8ab0dc: ret
    //     0x8ab0dc: ret             
    // 0x8ab0e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab0e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab0e4: b               #0x8aaf00
    // 0x8ab0e8: r9 = keystore
    //     0x8ab0e8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8ab0ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ab0ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ defaultSelectedIDs(/* No info */) {
    // ** addr: 0x8ab1d0, size: 0x11c
    // 0x8ab1d0: EnterFrame
    //     0x8ab1d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab1d4: mov             fp, SP
    // 0x8ab1d8: AllocStack(0x10)
    //     0x8ab1d8: sub             SP, SP, #0x10
    // 0x8ab1dc: CheckStackOverflow
    //     0x8ab1dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab1e0: cmp             SP, x16
    //     0x8ab1e4: b.ls            #0x8ab2e4
    // 0x8ab1e8: r1 = Null
    //     0x8ab1e8: mov             x1, NULL
    // 0x8ab1ec: r2 = 56
    //     0x8ab1ec: movz            x2, #0x38
    // 0x8ab1f0: r0 = AllocateArray()
    //     0x8ab1f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ab1f4: r16 = 88
    //     0x8ab1f4: movz            x16, #0x58
    // 0x8ab1f8: StoreField: r0->field_f = r16
    //     0x8ab1f8: stur            w16, [x0, #0xf]
    // 0x8ab1fc: StoreField: r0->field_13 = rZR
    //     0x8ab1fc: stur            wzr, [x0, #0x13]
    // 0x8ab200: r16 = 90
    //     0x8ab200: movz            x16, #0x5a
    // 0x8ab204: ArrayStore: r0[0] = r16  ; List_4
    //     0x8ab204: stur            w16, [x0, #0x17]
    // 0x8ab208: r16 = 2
    //     0x8ab208: movz            x16, #0x2
    // 0x8ab20c: StoreField: r0->field_1b = r16
    //     0x8ab20c: stur            w16, [x0, #0x1b]
    // 0x8ab210: r16 = 4
    //     0x8ab210: movz            x16, #0x4
    // 0x8ab214: StoreField: r0->field_1f = r16
    //     0x8ab214: stur            w16, [x0, #0x1f]
    // 0x8ab218: r16 = 4
    //     0x8ab218: movz            x16, #0x4
    // 0x8ab21c: StoreField: r0->field_23 = r16
    //     0x8ab21c: stur            w16, [x0, #0x23]
    // 0x8ab220: r16 = 6
    //     0x8ab220: movz            x16, #0x6
    // 0x8ab224: StoreField: r0->field_27 = r16
    //     0x8ab224: stur            w16, [x0, #0x27]
    // 0x8ab228: r16 = 6
    //     0x8ab228: movz            x16, #0x6
    // 0x8ab22c: StoreField: r0->field_2b = r16
    //     0x8ab22c: stur            w16, [x0, #0x2b]
    // 0x8ab230: r16 = 14
    //     0x8ab230: movz            x16, #0xe
    // 0x8ab234: StoreField: r0->field_2f = r16
    //     0x8ab234: stur            w16, [x0, #0x2f]
    // 0x8ab238: r16 = 8
    //     0x8ab238: movz            x16, #0x8
    // 0x8ab23c: StoreField: r0->field_33 = r16
    //     0x8ab23c: stur            w16, [x0, #0x33]
    // 0x8ab240: r16 = 124
    //     0x8ab240: movz            x16, #0x7c
    // 0x8ab244: StoreField: r0->field_37 = r16
    //     0x8ab244: stur            w16, [x0, #0x37]
    // 0x8ab248: r16 = 10
    //     0x8ab248: movz            x16, #0xa
    // 0x8ab24c: StoreField: r0->field_3b = r16
    //     0x8ab24c: stur            w16, [x0, #0x3b]
    // 0x8ab250: r16 = 24
    //     0x8ab250: movz            x16, #0x18
    // 0x8ab254: StoreField: r0->field_3f = r16
    //     0x8ab254: stur            w16, [x0, #0x3f]
    // 0x8ab258: r16 = 12
    //     0x8ab258: movz            x16, #0xc
    // 0x8ab25c: StoreField: r0->field_43 = r16
    //     0x8ab25c: stur            w16, [x0, #0x43]
    // 0x8ab260: r16 = 16
    //     0x8ab260: movz            x16, #0x10
    // 0x8ab264: StoreField: r0->field_47 = r16
    //     0x8ab264: stur            w16, [x0, #0x47]
    // 0x8ab268: r16 = 14
    //     0x8ab268: movz            x16, #0xe
    // 0x8ab26c: StoreField: r0->field_4b = r16
    //     0x8ab26c: stur            w16, [x0, #0x4b]
    // 0x8ab270: r16 = 18
    //     0x8ab270: movz            x16, #0x12
    // 0x8ab274: StoreField: r0->field_4f = r16
    //     0x8ab274: stur            w16, [x0, #0x4f]
    // 0x8ab278: StoreField: r0->field_53 = rZR
    //     0x8ab278: stur            wzr, [x0, #0x53]
    // 0x8ab27c: r16 = 118
    //     0x8ab27c: movz            x16, #0x76
    // 0x8ab280: StoreField: r0->field_57 = r16
    //     0x8ab280: stur            w16, [x0, #0x57]
    // 0x8ab284: r16 = 2
    //     0x8ab284: movz            x16, #0x2
    // 0x8ab288: StoreField: r0->field_5b = r16
    //     0x8ab288: stur            w16, [x0, #0x5b]
    // 0x8ab28c: r16 = 102
    //     0x8ab28c: movz            x16, #0x66
    // 0x8ab290: StoreField: r0->field_5f = r16
    //     0x8ab290: stur            w16, [x0, #0x5f]
    // 0x8ab294: r16 = 4
    //     0x8ab294: movz            x16, #0x4
    // 0x8ab298: StoreField: r0->field_63 = r16
    //     0x8ab298: stur            w16, [x0, #0x63]
    // 0x8ab29c: r16 = 178
    //     0x8ab29c: movz            x16, #0xb2
    // 0x8ab2a0: StoreField: r0->field_67 = r16
    //     0x8ab2a0: stur            w16, [x0, #0x67]
    // 0x8ab2a4: r16 = 6
    //     0x8ab2a4: movz            x16, #0x6
    // 0x8ab2a8: StoreField: r0->field_6b = r16
    //     0x8ab2a8: stur            w16, [x0, #0x6b]
    // 0x8ab2ac: r16 = 156
    //     0x8ab2ac: movz            x16, #0x9c
    // 0x8ab2b0: StoreField: r0->field_6f = r16
    //     0x8ab2b0: stur            w16, [x0, #0x6f]
    // 0x8ab2b4: r16 = 8
    //     0x8ab2b4: movz            x16, #0x8
    // 0x8ab2b8: StoreField: r0->field_73 = r16
    //     0x8ab2b8: stur            w16, [x0, #0x73]
    // 0x8ab2bc: r16 = 136
    //     0x8ab2bc: movz            x16, #0x88
    // 0x8ab2c0: StoreField: r0->field_77 = r16
    //     0x8ab2c0: stur            w16, [x0, #0x77]
    // 0x8ab2c4: r16 = 10
    //     0x8ab2c4: movz            x16, #0xa
    // 0x8ab2c8: StoreField: r0->field_7b = r16
    //     0x8ab2c8: stur            w16, [x0, #0x7b]
    // 0x8ab2cc: r16 = <int, int>
    //     0x8ab2cc: ldr             x16, [PP, #0x28b0]  ; [pp+0x28b0] TypeArguments: <int, int>
    // 0x8ab2d0: stp             x0, x16, [SP]
    // 0x8ab2d4: r0 = Map._fromLiteral()
    //     0x8ab2d4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8ab2d8: LeaveFrame
    //     0x8ab2d8: mov             SP, fp
    //     0x8ab2dc: ldp             fp, lr, [SP], #0x10
    // 0x8ab2e0: ret
    //     0x8ab2e0: ret             
    // 0x8ab2e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab2e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab2e8: b               #0x8ab1e8
  }
}
