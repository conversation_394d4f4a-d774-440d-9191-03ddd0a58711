// lib: , url: package:nuonline/app/data/repositories/counter_repository.dart

// class id: 1050071, size: 0x8
class :: {
}

// class id: 1105, size: 0xc, field offset: 0x8
class CounterRepository extends Object {

  _ record(/* No info */) async {
    // ** addr: 0x8efad4, size: 0x168
    // 0x8efad4: EnterFrame
    //     0x8efad4: stp             fp, lr, [SP, #-0x10]!
    //     0x8efad8: mov             fp, SP
    // 0x8efadc: AllocStack(0x90)
    //     0x8efadc: sub             SP, SP, #0x90
    // 0x8efae0: SetupParameters(CounterRepository this /* r1 => r3, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */, dynamic _ /* r3 => r1, fp-0x70 */)
    //     0x8efae0: stur            NULL, [fp, #-8]
    //     0x8efae4: stur            x1, [fp, #-0x60]
    //     0x8efae8: mov             x16, x3
    //     0x8efaec: mov             x3, x1
    //     0x8efaf0: mov             x1, x16
    //     0x8efaf4: stur            x2, [fp, #-0x68]
    //     0x8efaf8: stur            x1, [fp, #-0x70]
    // 0x8efafc: CheckStackOverflow
    //     0x8efafc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8efb00: cmp             SP, x16
    //     0x8efb04: b.ls            #0x8efc34
    // 0x8efb08: InitAsync() -> Future<ApiResult>
    //     0x8efb08: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d3e0] TypeArguments: <ApiResult>
    //     0x8efb0c: ldr             x0, [x0, #0x3e0]
    //     0x8efb10: bl              #0x661298  ; InitAsyncStub
    // 0x8efb14: ldur            x1, [fp, #-0x60]
    // 0x8efb18: ldur            x0, [fp, #-0x68]
    // 0x8efb1c: LoadField: r3 = r1->field_7
    //     0x8efb1c: ldur            w3, [x1, #7]
    // 0x8efb20: DecompressPointer r3
    //     0x8efb20: add             x3, x3, HEAP, lsl #32
    // 0x8efb24: stur            x3, [fp, #-0x78]
    // 0x8efb28: r1 = Null
    //     0x8efb28: mov             x1, NULL
    // 0x8efb2c: r2 = 8
    //     0x8efb2c: movz            x2, #0x8
    // 0x8efb30: r0 = AllocateArray()
    //     0x8efb30: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8efb34: stur            x0, [fp, #-0x60]
    // 0x8efb38: r16 = "/counter/"
    //     0x8efb38: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d3e8] "/counter/"
    //     0x8efb3c: ldr             x16, [x16, #0x3e8]
    // 0x8efb40: StoreField: r0->field_f = r16
    //     0x8efb40: stur            w16, [x0, #0xf]
    // 0x8efb44: ldur            x1, [fp, #-0x70]
    // 0x8efb48: r0 = CounterTypeExtension.route()
    //     0x8efb48: bl              #0x8efc3c  ; [package:nuonline/app/data/enums/counter_enum.dart] ::CounterTypeExtension.route
    // 0x8efb4c: ldur            x1, [fp, #-0x60]
    // 0x8efb50: ArrayStore: r1[1] = r0  ; List_4
    //     0x8efb50: add             x25, x1, #0x13
    //     0x8efb54: str             w0, [x25]
    //     0x8efb58: tbz             w0, #0, #0x8efb74
    //     0x8efb5c: ldurb           w16, [x1, #-1]
    //     0x8efb60: ldurb           w17, [x0, #-1]
    //     0x8efb64: and             x16, x17, x16, lsr #2
    //     0x8efb68: tst             x16, HEAP, lsr #32
    //     0x8efb6c: b.eq            #0x8efb74
    //     0x8efb70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8efb74: ldur            x2, [fp, #-0x60]
    // 0x8efb78: r16 = "/"
    //     0x8efb78: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x8efb7c: ArrayStore: r2[0] = r16  ; List_4
    //     0x8efb7c: stur            w16, [x2, #0x17]
    // 0x8efb80: ldur            x3, [fp, #-0x68]
    // 0x8efb84: r0 = BoxInt64Instr(r3)
    //     0x8efb84: sbfiz           x0, x3, #1, #0x1f
    //     0x8efb88: cmp             x3, x0, asr #1
    //     0x8efb8c: b.eq            #0x8efb98
    //     0x8efb90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8efb94: stur            x3, [x0, #7]
    // 0x8efb98: mov             x1, x2
    // 0x8efb9c: ArrayStore: r1[3] = r0  ; List_4
    //     0x8efb9c: add             x25, x1, #0x1b
    //     0x8efba0: str             w0, [x25]
    //     0x8efba4: tbz             w0, #0, #0x8efbc0
    //     0x8efba8: ldurb           w16, [x1, #-1]
    //     0x8efbac: ldurb           w17, [x0, #-1]
    //     0x8efbb0: and             x16, x17, x16, lsr #2
    //     0x8efbb4: tst             x16, HEAP, lsr #32
    //     0x8efbb8: b.eq            #0x8efbc0
    //     0x8efbbc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8efbc0: str             x2, [SP]
    // 0x8efbc4: r0 = _interpolate()
    //     0x8efbc4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8efbc8: ldur            x16, [fp, #-0x78]
    // 0x8efbcc: stp             x16, NULL, [SP, #8]
    // 0x8efbd0: str             x0, [SP]
    // 0x8efbd4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8efbd4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8efbd8: r0 = get()
    //     0x8efbd8: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8efbdc: mov             x1, x0
    // 0x8efbe0: stur            x1, [fp, #-0x60]
    // 0x8efbe4: r0 = Await()
    //     0x8efbe4: bl              #0x661044  ; AwaitStub
    // 0x8efbe8: LoadField: r2 = r0->field_b
    //     0x8efbe8: ldur            w2, [x0, #0xb]
    // 0x8efbec: DecompressPointer r2
    //     0x8efbec: add             x2, x2, HEAP, lsl #32
    // 0x8efbf0: stur            x2, [fp, #-0x60]
    // 0x8efbf4: r1 = Null
    //     0x8efbf4: mov             x1, NULL
    // 0x8efbf8: r0 = _$SuccessImpl()
    //     0x8efbf8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8efbfc: mov             x1, x0
    // 0x8efc00: ldur            x0, [fp, #-0x60]
    // 0x8efc04: StoreField: r1->field_b = r0
    //     0x8efc04: stur            w0, [x1, #0xb]
    // 0x8efc08: mov             x0, x1
    // 0x8efc0c: r0 = ReturnAsyncNotFuture()
    //     0x8efc0c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8efc10: sub             SP, fp, #0x90
    // 0x8efc14: mov             x1, x0
    // 0x8efc18: r0 = getDioException()
    //     0x8efc18: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8efc1c: r1 = Null
    //     0x8efc1c: mov             x1, NULL
    // 0x8efc20: stur            x0, [fp, #-0x60]
    // 0x8efc24: r0 = _$FailureImpl()
    //     0x8efc24: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8efc28: ldur            x1, [fp, #-0x60]
    // 0x8efc2c: StoreField: r0->field_b = r1
    //     0x8efc2c: stur            w1, [x0, #0xb]
    // 0x8efc30: r0 = ReturnAsyncNotFuture()
    //     0x8efc30: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8efc34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8efc34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8efc38: b               #0x8efb08
  }
  static _ find(/* No info */) {
    // ** addr: 0x8efcf4, size: 0x64
    // 0x8efcf4: EnterFrame
    //     0x8efcf4: stp             fp, lr, [SP, #-0x10]!
    //     0x8efcf8: mov             fp, SP
    // 0x8efcfc: AllocStack(0x10)
    //     0x8efcfc: sub             SP, SP, #0x10
    // 0x8efd00: CheckStackOverflow
    //     0x8efd00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8efd04: cmp             SP, x16
    //     0x8efd08: b.ls            #0x8efd50
    // 0x8efd0c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8efd0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8efd10: ldr             x0, [x0, #0x2670]
    //     0x8efd14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8efd18: cmp             w0, w16
    //     0x8efd1c: b.ne            #0x8efd28
    //     0x8efd20: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8efd24: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8efd28: r16 = <CounterRepository>
    //     0x8efd28: add             x16, PP, #0x10, lsl #12  ; [pp+0x101e8] TypeArguments: <CounterRepository>
    //     0x8efd2c: ldr             x16, [x16, #0x1e8]
    // 0x8efd30: r30 = "counter_repo"
    //     0x8efd30: add             lr, PP, #0x10, lsl #12  ; [pp+0x101f0] "counter_repo"
    //     0x8efd34: ldr             lr, [lr, #0x1f0]
    // 0x8efd38: stp             lr, x16, [SP]
    // 0x8efd3c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8efd3c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8efd40: r0 = Inst.find()
    //     0x8efd40: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8efd44: LeaveFrame
    //     0x8efd44: mov             SP, fp
    //     0x8efd48: ldp             fp, lr, [SP], #0x10
    // 0x8efd4c: ret
    //     0x8efd4c: ret             
    // 0x8efd50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8efd50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8efd54: b               #0x8efd0c
  }
}
