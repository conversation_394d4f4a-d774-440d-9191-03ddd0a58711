// lib: , url: package:nuonline/app/data/repositories/diacritic_repository.dart

// class id: 1050072, size: 0x8
class :: {
}

// class id: 1104, size: 0xc, field offset: 0x8
class DiacriticRepository extends Object {

  _ findAllHistory(/* No info */) async {
    // ** addr: 0x8f3bd8, size: 0xac
    // 0x8f3bd8: EnterFrame
    //     0x8f3bd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f3bdc: mov             fp, SP
    // 0x8f3be0: AllocStack(0x28)
    //     0x8f3be0: sub             SP, SP, #0x28
    // 0x8f3be4: SetupParameters(DiacriticRepository this /* r1 => r1, fp-0x10 */)
    //     0x8f3be4: stur            NULL, [fp, #-8]
    //     0x8f3be8: stur            x1, [fp, #-0x10]
    // 0x8f3bec: CheckStackOverflow
    //     0x8f3bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f3bf0: cmp             SP, x16
    //     0x8f3bf4: b.ls            #0x8f3c74
    // 0x8f3bf8: InitAsync() -> Future<List<String>>
    //     0x8f3bf8: add             x0, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x8f3bfc: ldr             x0, [x0, #0xb40]
    //     0x8f3c00: bl              #0x661298  ; InitAsyncStub
    // 0x8f3c04: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8f3c04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f3c08: ldr             x0, [x0, #0x2728]
    //     0x8f3c0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f3c10: cmp             w0, w16
    //     0x8f3c14: b.ne            #0x8f3c20
    //     0x8f3c18: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8f3c1c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f3c20: r16 = <String>
    //     0x8f3c20: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8f3c24: stp             x0, x16, [SP, #8]
    // 0x8f3c28: r16 = "v2_diacritic_search_history"
    //     0x8f3c28: ldr             x16, [PP, #0x7c80]  ; [pp+0x7c80] "v2_diacritic_search_history"
    // 0x8f3c2c: str             x16, [SP]
    // 0x8f3c30: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f3c30: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f3c34: r0 = box()
    //     0x8f3c34: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f3c38: mov             x1, x0
    // 0x8f3c3c: stur            x0, [fp, #-0x10]
    // 0x8f3c40: r0 = checkOpen()
    //     0x8f3c40: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8f3c44: ldur            x0, [fp, #-0x10]
    // 0x8f3c48: LoadField: r1 = r0->field_1b
    //     0x8f3c48: ldur            w1, [x0, #0x1b]
    // 0x8f3c4c: DecompressPointer r1
    //     0x8f3c4c: add             x1, x1, HEAP, lsl #32
    // 0x8f3c50: r16 = Sentinel
    //     0x8f3c50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f3c54: cmp             w1, w16
    // 0x8f3c58: b.eq            #0x8f3c7c
    // 0x8f3c5c: r0 = getValues()
    //     0x8f3c5c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8f3c60: LoadField: r1 = r0->field_7
    //     0x8f3c60: ldur            w1, [x0, #7]
    // 0x8f3c64: DecompressPointer r1
    //     0x8f3c64: add             x1, x1, HEAP, lsl #32
    // 0x8f3c68: mov             x2, x0
    // 0x8f3c6c: r0 = _GrowableList.of()
    //     0x8f3c6c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8f3c70: r0 = ReturnAsyncNotFuture()
    //     0x8f3c70: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f3c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f3c74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f3c78: b               #0x8f3bf8
    // 0x8f3c7c: r9 = keystore
    //     0x8f3c7c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8f3c80: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f3c80: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ updateAllHistory(/* No info */) async {
    // ** addr: 0x9276cc, size: 0xb0
    // 0x9276cc: EnterFrame
    //     0x9276cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9276d0: mov             fp, SP
    // 0x9276d4: AllocStack(0x38)
    //     0x9276d4: sub             SP, SP, #0x38
    // 0x9276d8: SetupParameters(DiacriticRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x9276d8: stur            NULL, [fp, #-8]
    //     0x9276dc: stur            x1, [fp, #-0x10]
    //     0x9276e0: stur            x2, [fp, #-0x18]
    // 0x9276e4: CheckStackOverflow
    //     0x9276e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9276e8: cmp             SP, x16
    //     0x9276ec: b.ls            #0x927774
    // 0x9276f0: InitAsync() -> Future<void?>
    //     0x9276f0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x9276f4: bl              #0x661298  ; InitAsyncStub
    // 0x9276f8: ldur            x0, [fp, #-0x10]
    // 0x9276fc: LoadField: r1 = r0->field_7
    //     0x9276fc: ldur            w1, [x0, #7]
    // 0x927700: DecompressPointer r1
    //     0x927700: add             x1, x1, HEAP, lsl #32
    // 0x927704: r0 = diacriticSearchHistory()
    //     0x927704: bl              #0x8f3c84  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::diacriticSearchHistory
    // 0x927708: mov             x1, x0
    // 0x92770c: r0 = clear()
    //     0x92770c: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0x927710: mov             x1, x0
    // 0x927714: stur            x1, [fp, #-0x20]
    // 0x927718: r0 = Await()
    //     0x927718: bl              #0x661044  ; AwaitStub
    // 0x92771c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x92771c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x927720: ldr             x0, [x0, #0x2728]
    //     0x927724: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x927728: cmp             w0, w16
    //     0x92772c: b.ne            #0x927738
    //     0x927730: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x927734: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x927738: r16 = <String>
    //     0x927738: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x92773c: stp             x0, x16, [SP, #8]
    // 0x927740: r16 = "v2_diacritic_search_history"
    //     0x927740: ldr             x16, [PP, #0x7c80]  ; [pp+0x7c80] "v2_diacritic_search_history"
    // 0x927744: str             x16, [SP]
    // 0x927748: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x927748: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92774c: r0 = box()
    //     0x92774c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x927750: mov             x1, x0
    // 0x927754: ldur            x2, [fp, #-0x18]
    // 0x927758: r0 = addAll()
    //     0x927758: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0x92775c: r16 = <Iterable<int>>
    //     0x92775c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2daf8] TypeArguments: <Iterable<int>>
    //     0x927760: ldr             x16, [x16, #0xaf8]
    // 0x927764: stp             x0, x16, [SP]
    // 0x927768: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x927768: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x92776c: r0 = FutureExtensions.ignore()
    //     0x92776c: bl              #0x7082c8  ; [dart:async] ::FutureExtensions.ignore
    // 0x927770: r0 = ReturnAsync()
    //     0x927770: b               #0x6576a4  ; ReturnAsyncStub
    // 0x927774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927778: b               #0x9276f0
  }
  _ findAll(/* No info */) async {
    // ** addr: 0xbefac8, size: 0x130
    // 0xbefac8: EnterFrame
    //     0xbefac8: stp             fp, lr, [SP, #-0x10]!
    //     0xbefacc: mov             fp, SP
    // 0xbefad0: AllocStack(0x40)
    //     0xbefad0: sub             SP, SP, #0x40
    // 0xbefad4: SetupParameters(DiacriticRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbefad4: stur            NULL, [fp, #-8]
    //     0xbefad8: stur            x1, [fp, #-0x10]
    //     0xbefadc: stur            x2, [fp, #-0x18]
    // 0xbefae0: CheckStackOverflow
    //     0xbefae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbefae4: cmp             SP, x16
    //     0xbefae8: b.ls            #0xbefbe8
    // 0xbefaec: r1 = 1
    //     0xbefaec: movz            x1, #0x1
    // 0xbefaf0: r0 = AllocateContext()
    //     0xbefaf0: bl              #0xec126c  ; AllocateContextStub
    // 0xbefaf4: mov             x1, x0
    // 0xbefaf8: ldur            x0, [fp, #-0x18]
    // 0xbefafc: stur            x1, [fp, #-0x20]
    // 0xbefb00: StoreField: r1->field_f = r0
    //     0xbefb00: stur            w0, [x1, #0xf]
    // 0xbefb04: InitAsync() -> Future<ApiResult<List<Diacritic>>>
    //     0xbefb04: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bda0] TypeArguments: <ApiResult<List<Diacritic>>>
    //     0xbefb08: ldr             x0, [x0, #0xda0]
    //     0xbefb0c: bl              #0x661298  ; InitAsyncStub
    // 0xbefb10: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xbefb10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbefb14: ldr             x0, [x0, #0x2728]
    //     0xbefb18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbefb1c: cmp             w0, w16
    //     0xbefb20: b.ne            #0xbefb2c
    //     0xbefb24: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xbefb28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbefb2c: r16 = <Diacritic>
    //     0xbefb2c: ldr             x16, [PP, #0x7bd0]  ; [pp+0x7bd0] TypeArguments: <Diacritic>
    // 0xbefb30: stp             x0, x16, [SP, #8]
    // 0xbefb34: r16 = "v2_diacritic"
    //     0xbefb34: ldr             x16, [PP, #0x7c78]  ; [pp+0x7c78] "v2_diacritic"
    // 0xbefb38: str             x16, [SP]
    // 0xbefb3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbefb3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbefb40: r0 = box()
    //     0xbefb40: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xbefb44: mov             x1, x0
    // 0xbefb48: stur            x0, [fp, #-0x10]
    // 0xbefb4c: r0 = checkOpen()
    //     0xbefb4c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xbefb50: ldur            x0, [fp, #-0x10]
    // 0xbefb54: LoadField: r1 = r0->field_1b
    //     0xbefb54: ldur            w1, [x0, #0x1b]
    // 0xbefb58: DecompressPointer r1
    //     0xbefb58: add             x1, x1, HEAP, lsl #32
    // 0xbefb5c: r16 = Sentinel
    //     0xbefb5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbefb60: cmp             w1, w16
    // 0xbefb64: b.eq            #0xbefbf0
    // 0xbefb68: r0 = getValues()
    //     0xbefb68: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xbefb6c: LoadField: r1 = r0->field_7
    //     0xbefb6c: ldur            w1, [x0, #7]
    // 0xbefb70: DecompressPointer r1
    //     0xbefb70: add             x1, x1, HEAP, lsl #32
    // 0xbefb74: mov             x2, x0
    // 0xbefb78: r0 = _GrowableList.of()
    //     0xbefb78: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbefb7c: ldur            x2, [fp, #-0x20]
    // 0xbefb80: stur            x0, [fp, #-0x18]
    // 0xbefb84: LoadField: r1 = r2->field_f
    //     0xbefb84: ldur            w1, [x2, #0xf]
    // 0xbefb88: DecompressPointer r1
    //     0xbefb88: add             x1, x1, HEAP, lsl #32
    // 0xbefb8c: LoadField: r3 = r1->field_7
    //     0xbefb8c: ldur            w3, [x1, #7]
    // 0xbefb90: cbnz            w3, #0xbefb9c
    // 0xbefb94: r4 = false
    //     0xbefb94: add             x4, NULL, #0x30  ; false
    // 0xbefb98: b               #0xbefba0
    // 0xbefb9c: r4 = true
    //     0xbefb9c: add             x4, NULL, #0x20  ; true
    // 0xbefba0: stur            x4, [fp, #-0x10]
    // 0xbefba4: r1 = Function '<anonymous closure>':.
    //     0xbefba4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bda8] AnonymousClosure: (0xbefc58), in [package:nuonline/app/data/repositories/diacritic_repository.dart] DiacriticRepository::findAll (0xbefac8)
    //     0xbefba8: ldr             x1, [x1, #0xda8]
    // 0xbefbac: r0 = AllocateClosure()
    //     0xbefbac: bl              #0xec1630  ; AllocateClosureStub
    // 0xbefbb0: r16 = <Diacritic>
    //     0xbefbb0: ldr             x16, [PP, #0x7bd0]  ; [pp+0x7bd0] TypeArguments: <Diacritic>
    // 0xbefbb4: ldur            lr, [fp, #-0x18]
    // 0xbefbb8: stp             lr, x16, [SP, #0x10]
    // 0xbefbbc: ldur            x16, [fp, #-0x10]
    // 0xbefbc0: stp             x0, x16, [SP]
    // 0xbefbc4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xbefbc4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xbefbc8: r0 = ListExtension.optional()
    //     0xbefbc8: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xbefbcc: r1 = <List<Diacritic>>
    //     0xbefbcc: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bc38] TypeArguments: <List<Diacritic>>
    //     0xbefbd0: ldr             x1, [x1, #0xc38]
    // 0xbefbd4: stur            x0, [fp, #-0x10]
    // 0xbefbd8: r0 = _$SuccessImpl()
    //     0xbefbd8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xbefbdc: ldur            x1, [fp, #-0x10]
    // 0xbefbe0: StoreField: r0->field_b = r1
    //     0xbefbe0: stur            w1, [x0, #0xb]
    // 0xbefbe4: r0 = ReturnAsyncNotFuture()
    //     0xbefbe4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbefbe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefbe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefbec: b               #0xbefaec
    // 0xbefbf0: r9 = keystore
    //     0xbefbf0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xbefbf4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbefbf4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] List<Diacritic> <anonymous closure>(dynamic, List<Diacritic>) {
    // ** addr: 0xbefc58, size: 0x5c
    // 0xbefc58: EnterFrame
    //     0xbefc58: stp             fp, lr, [SP, #-0x10]!
    //     0xbefc5c: mov             fp, SP
    // 0xbefc60: AllocStack(0x18)
    //     0xbefc60: sub             SP, SP, #0x18
    // 0xbefc64: SetupParameters()
    //     0xbefc64: ldr             x0, [fp, #0x18]
    //     0xbefc68: ldur            w2, [x0, #0x17]
    //     0xbefc6c: add             x2, x2, HEAP, lsl #32
    // 0xbefc70: CheckStackOverflow
    //     0xbefc70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbefc74: cmp             SP, x16
    //     0xbefc78: b.ls            #0xbefcac
    // 0xbefc7c: r1 = Function '<anonymous closure>':.
    //     0xbefc7c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2bdb0] AnonymousClosure: (0xbefcb4), in [package:nuonline/app/data/repositories/diacritic_repository.dart] DiacriticRepository::findAll (0xbefac8)
    //     0xbefc80: ldr             x1, [x1, #0xdb0]
    // 0xbefc84: r0 = AllocateClosure()
    //     0xbefc84: bl              #0xec1630  ; AllocateClosureStub
    // 0xbefc88: r16 = <Diacritic>
    //     0xbefc88: ldr             x16, [PP, #0x7bd0]  ; [pp+0x7bd0] TypeArguments: <Diacritic>
    // 0xbefc8c: ldr             lr, [fp, #0x10]
    // 0xbefc90: stp             lr, x16, [SP, #8]
    // 0xbefc94: str             x0, [SP]
    // 0xbefc98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbefc98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbefc9c: r0 = ListExtension.filter()
    //     0xbefc9c: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xbefca0: LeaveFrame
    //     0xbefca0: mov             SP, fp
    //     0xbefca4: ldp             fp, lr, [SP], #0x10
    // 0xbefca8: ret
    //     0xbefca8: ret             
    // 0xbefcac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefcac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefcb0: b               #0xbefc7c
  }
  [closure] bool <anonymous closure>(dynamic, Diacritic) {
    // ** addr: 0xbefcb4, size: 0x1a0
    // 0xbefcb4: EnterFrame
    //     0xbefcb4: stp             fp, lr, [SP, #-0x10]!
    //     0xbefcb8: mov             fp, SP
    // 0xbefcbc: AllocStack(0x10)
    //     0xbefcbc: sub             SP, SP, #0x10
    // 0xbefcc0: SetupParameters()
    //     0xbefcc0: ldr             x0, [fp, #0x18]
    //     0xbefcc4: ldur            w3, [x0, #0x17]
    //     0xbefcc8: add             x3, x3, HEAP, lsl #32
    //     0xbefccc: stur            x3, [fp, #-8]
    // 0xbefcd0: CheckStackOverflow
    //     0xbefcd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbefcd4: cmp             SP, x16
    //     0xbefcd8: b.ls            #0xbefe4c
    // 0xbefcdc: LoadField: r1 = r3->field_f
    //     0xbefcdc: ldur            w1, [x3, #0xf]
    // 0xbefce0: DecompressPointer r1
    //     0xbefce0: add             x1, x1, HEAP, lsl #32
    // 0xbefce4: r0 = LoadClassIdInstr(r1)
    //     0xbefce4: ldur            x0, [x1, #-1]
    //     0xbefce8: ubfx            x0, x0, #0xc, #0x14
    // 0xbefcec: r2 = "ا"
    //     0xbefcec: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bc88] "ا"
    //     0xbefcf0: ldr             x2, [x2, #0xc88]
    // 0xbefcf4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbefcf4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbefcf8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbefcf8: sub             lr, x0, #0xffc
    //     0xbefcfc: ldr             lr, [x21, lr, lsl #3]
    //     0xbefd00: blr             lr
    // 0xbefd04: tbnz            w0, #4, #0xbefdf8
    // 0xbefd08: ldr             x0, [fp, #0x10]
    // 0xbefd0c: ldur            x3, [fp, #-8]
    // 0xbefd10: LoadField: r4 = r0->field_1f
    //     0xbefd10: ldur            w4, [x0, #0x1f]
    // 0xbefd14: DecompressPointer r4
    //     0xbefd14: add             x4, x4, HEAP, lsl #32
    // 0xbefd18: stur            x4, [fp, #-0x10]
    // 0xbefd1c: LoadField: r2 = r3->field_f
    //     0xbefd1c: ldur            w2, [x3, #0xf]
    // 0xbefd20: DecompressPointer r2
    //     0xbefd20: add             x2, x2, HEAP, lsl #32
    // 0xbefd24: r0 = LoadClassIdInstr(r4)
    //     0xbefd24: ldur            x0, [x4, #-1]
    //     0xbefd28: ubfx            x0, x0, #0xc, #0x14
    // 0xbefd2c: mov             x1, x4
    // 0xbefd30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbefd30: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbefd34: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbefd34: sub             lr, x0, #0xffc
    //     0xbefd38: ldr             lr, [x21, lr, lsl #3]
    //     0xbefd3c: blr             lr
    // 0xbefd40: tbz             w0, #4, #0xbefd94
    // 0xbefd44: ldur            x0, [fp, #-8]
    // 0xbefd48: ldur            x4, [fp, #-0x10]
    // 0xbefd4c: LoadField: r1 = r0->field_f
    //     0xbefd4c: ldur            w1, [x0, #0xf]
    // 0xbefd50: DecompressPointer r1
    //     0xbefd50: add             x1, x1, HEAP, lsl #32
    // 0xbefd54: r2 = "ا"
    //     0xbefd54: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bc88] "ا"
    //     0xbefd58: ldr             x2, [x2, #0xc88]
    // 0xbefd5c: r3 = "أ"
    //     0xbefd5c: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bc90] "أ"
    //     0xbefd60: ldr             x3, [x3, #0xc90]
    // 0xbefd64: r0 = replaceAll()
    //     0xbefd64: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbefd68: ldur            x3, [fp, #-0x10]
    // 0xbefd6c: r1 = LoadClassIdInstr(r3)
    //     0xbefd6c: ldur            x1, [x3, #-1]
    //     0xbefd70: ubfx            x1, x1, #0xc, #0x14
    // 0xbefd74: mov             x2, x0
    // 0xbefd78: mov             x0, x1
    // 0xbefd7c: mov             x1, x3
    // 0xbefd80: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbefd80: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbefd84: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbefd84: sub             lr, x0, #0xffc
    //     0xbefd88: ldr             lr, [x21, lr, lsl #3]
    //     0xbefd8c: blr             lr
    // 0xbefd90: tbnz            w0, #4, #0xbefd9c
    // 0xbefd94: r0 = true
    //     0xbefd94: add             x0, NULL, #0x20  ; true
    // 0xbefd98: b               #0xbefdec
    // 0xbefd9c: ldur            x1, [fp, #-8]
    // 0xbefda0: ldur            x0, [fp, #-0x10]
    // 0xbefda4: LoadField: r2 = r1->field_f
    //     0xbefda4: ldur            w2, [x1, #0xf]
    // 0xbefda8: DecompressPointer r2
    //     0xbefda8: add             x2, x2, HEAP, lsl #32
    // 0xbefdac: mov             x1, x2
    // 0xbefdb0: r2 = "ا"
    //     0xbefdb0: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2bc88] "ا"
    //     0xbefdb4: ldr             x2, [x2, #0xc88]
    // 0xbefdb8: r3 = "إ"
    //     0xbefdb8: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2bc98] "إ"
    //     0xbefdbc: ldr             x3, [x3, #0xc98]
    // 0xbefdc0: r0 = replaceAll()
    //     0xbefdc0: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbefdc4: ldur            x1, [fp, #-0x10]
    // 0xbefdc8: r2 = LoadClassIdInstr(r1)
    //     0xbefdc8: ldur            x2, [x1, #-1]
    //     0xbefdcc: ubfx            x2, x2, #0xc, #0x14
    // 0xbefdd0: mov             x16, x0
    // 0xbefdd4: mov             x0, x2
    // 0xbefdd8: mov             x2, x16
    // 0xbefddc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbefddc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbefde0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbefde0: sub             lr, x0, #0xffc
    //     0xbefde4: ldr             lr, [x21, lr, lsl #3]
    //     0xbefde8: blr             lr
    // 0xbefdec: LeaveFrame
    //     0xbefdec: mov             SP, fp
    //     0xbefdf0: ldp             fp, lr, [SP], #0x10
    // 0xbefdf4: ret
    //     0xbefdf4: ret             
    // 0xbefdf8: ldr             x0, [fp, #0x10]
    // 0xbefdfc: ldur            x1, [fp, #-8]
    // 0xbefe00: LoadField: r2 = r0->field_1f
    //     0xbefe00: ldur            w2, [x0, #0x1f]
    // 0xbefe04: DecompressPointer r2
    //     0xbefe04: add             x2, x2, HEAP, lsl #32
    // 0xbefe08: LoadField: r0 = r1->field_f
    //     0xbefe08: ldur            w0, [x1, #0xf]
    // 0xbefe0c: DecompressPointer r0
    //     0xbefe0c: add             x0, x0, HEAP, lsl #32
    // 0xbefe10: r1 = LoadClassIdInstr(r2)
    //     0xbefe10: ldur            x1, [x2, #-1]
    //     0xbefe14: ubfx            x1, x1, #0xc, #0x14
    // 0xbefe18: mov             x16, x2
    // 0xbefe1c: mov             x2, x1
    // 0xbefe20: mov             x1, x16
    // 0xbefe24: mov             x16, x0
    // 0xbefe28: mov             x0, x2
    // 0xbefe2c: mov             x2, x16
    // 0xbefe30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbefe30: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbefe34: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbefe34: sub             lr, x0, #0xffc
    //     0xbefe38: ldr             lr, [x21, lr, lsl #3]
    //     0xbefe3c: blr             lr
    // 0xbefe40: LeaveFrame
    //     0xbefe40: mov             SP, fp
    //     0xbefe44: ldp             fp, lr, [SP], #0x10
    // 0xbefe48: ret
    //     0xbefe48: ret             
    // 0xbefe4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefe4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefe50: b               #0xbefcdc
  }
}
