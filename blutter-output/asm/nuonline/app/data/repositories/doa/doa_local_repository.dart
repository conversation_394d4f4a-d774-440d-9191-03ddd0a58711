// lib: , url: package:nuonline/app/data/repositories/doa/doa_local_repository.dart

// class id: 1050073, size: 0x8
class :: {
}

// class id: 1103, size: 0xc, field offset: 0x8
class DoaLocalRepository extends Object
    implements DoaRepository {

  static _ find(/* No info */) {
    // ** addr: 0x80f380, size: 0x64
    // 0x80f380: EnterFrame
    //     0x80f380: stp             fp, lr, [SP, #-0x10]!
    //     0x80f384: mov             fp, SP
    // 0x80f388: AllocStack(0x10)
    //     0x80f388: sub             SP, SP, #0x10
    // 0x80f38c: CheckStackOverflow
    //     0x80f38c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f390: cmp             SP, x16
    //     0x80f394: b.ls            #0x80f3dc
    // 0x80f398: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f398: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f39c: ldr             x0, [x0, #0x2670]
    //     0x80f3a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f3a4: cmp             w0, w16
    //     0x80f3a8: b.ne            #0x80f3b4
    //     0x80f3ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f3b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f3b4: r16 = <DoaRepository>
    //     0x80f3b4: add             x16, PP, #0x10, lsl #12  ; [pp+0x100f8] TypeArguments: <DoaRepository>
    //     0x80f3b8: ldr             x16, [x16, #0xf8]
    // 0x80f3bc: r30 = "doa_local_repo"
    //     0x80f3bc: add             lr, PP, #0x10, lsl #12  ; [pp+0x10100] "doa_local_repo"
    //     0x80f3c0: ldr             lr, [lr, #0x100]
    // 0x80f3c4: stp             lr, x16, [SP]
    // 0x80f3c8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80f3c8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80f3cc: r0 = Inst.find()
    //     0x80f3cc: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80f3d0: LeaveFrame
    //     0x80f3d0: mov             SP, fp
    //     0x80f3d4: ldp             fp, lr, [SP], #0x10
    // 0x80f3d8: ret
    //     0x80f3d8: ret             
    // 0x80f3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f3dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f3e0: b               #0x80f398
  }
  _ findAll(/* No info */) async {
    // ** addr: 0xe76fd0, size: 0x140
    // 0xe76fd0: EnterFrame
    //     0xe76fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xe76fd4: mov             fp, SP
    // 0xe76fd8: AllocStack(0x40)
    //     0xe76fd8: sub             SP, SP, #0x40
    // 0xe76fdc: SetupParameters(DoaLocalRepository this /* r1 => r3, fp-0x18 */)
    //     0xe76fdc: stur            NULL, [fp, #-8]
    //     0xe76fe0: mov             x3, x1
    //     0xe76fe4: stur            x1, [fp, #-0x18]
    // 0xe76fe8: CheckStackOverflow
    //     0xe76fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76fec: cmp             SP, x16
    //     0xe76ff0: b.ls            #0xe77100
    // 0xe76ff4: r0 = BoxInt64Instr(r2)
    //     0xe76ff4: sbfiz           x0, x2, #1, #0x1f
    //     0xe76ff8: cmp             x2, x0, asr #1
    //     0xe76ffc: b.eq            #0xe77008
    //     0xe77000: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe77004: stur            x2, [x0, #7]
    // 0xe77008: stur            x0, [fp, #-0x10]
    // 0xe7700c: r1 = 1
    //     0xe7700c: movz            x1, #0x1
    // 0xe77010: r0 = AllocateContext()
    //     0xe77010: bl              #0xec126c  ; AllocateContextStub
    // 0xe77014: mov             x1, x0
    // 0xe77018: ldur            x0, [fp, #-0x10]
    // 0xe7701c: stur            x1, [fp, #-0x20]
    // 0xe77020: StoreField: r1->field_f = r0
    //     0xe77020: stur            w0, [x1, #0xf]
    // 0xe77024: InitAsync() -> Future<ApiResult<List<Doa>>>
    //     0xe77024: add             x0, PP, #0x35, lsl #12  ; [pp+0x35dd0] TypeArguments: <ApiResult<List<Doa>>>
    //     0xe77028: ldr             x0, [x0, #0xdd0]
    //     0xe7702c: bl              #0x661298  ; InitAsyncStub
    // 0xe77030: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe77030: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe77034: ldr             x0, [x0, #0x2728]
    //     0xe77038: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7703c: cmp             w0, w16
    //     0xe77040: b.ne            #0xe7704c
    //     0xe77044: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe77048: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7704c: r16 = <Doa>
    //     0xe7704c: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77050: stp             x0, x16, [SP, #8]
    // 0xe77054: r16 = "v2_doa"
    //     0xe77054: ldr             x16, [PP, #0x7c38]  ; [pp+0x7c38] "v2_doa"
    // 0xe77058: str             x16, [SP]
    // 0xe7705c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7705c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77060: r0 = box()
    //     0xe77060: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe77064: mov             x1, x0
    // 0xe77068: stur            x0, [fp, #-0x10]
    // 0xe7706c: r0 = checkOpen()
    //     0xe7706c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe77070: ldur            x0, [fp, #-0x10]
    // 0xe77074: LoadField: r1 = r0->field_1b
    //     0xe77074: ldur            w1, [x0, #0x1b]
    // 0xe77078: DecompressPointer r1
    //     0xe77078: add             x1, x1, HEAP, lsl #32
    // 0xe7707c: r16 = Sentinel
    //     0xe7707c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe77080: cmp             w1, w16
    // 0xe77084: b.eq            #0xe77108
    // 0xe77088: r0 = getValues()
    //     0xe77088: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe7708c: LoadField: r1 = r0->field_7
    //     0xe7708c: ldur            w1, [x0, #7]
    // 0xe77090: DecompressPointer r1
    //     0xe77090: add             x1, x1, HEAP, lsl #32
    // 0xe77094: mov             x2, x0
    // 0xe77098: r0 = _GrowableList.of()
    //     0xe77098: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe7709c: ldur            x2, [fp, #-0x20]
    // 0xe770a0: r1 = Function '<anonymous closure>':.
    //     0xe770a0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41300] AnonymousClosure: (0xe77220), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAll (0xe76fd0)
    //     0xe770a4: ldr             x1, [x1, #0x300]
    // 0xe770a8: stur            x0, [fp, #-0x10]
    // 0xe770ac: r0 = AllocateClosure()
    //     0xe770ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xe770b0: r16 = <Doa>
    //     0xe770b0: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe770b4: ldur            lr, [fp, #-0x10]
    // 0xe770b8: stp             lr, x16, [SP, #0x10]
    // 0xe770bc: r16 = true
    //     0xe770bc: add             x16, NULL, #0x20  ; true
    // 0xe770c0: stp             x0, x16, [SP]
    // 0xe770c4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe770c4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe770c8: r0 = ListExtension.optional()
    //     0xe770c8: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe770cc: r1 = Function '<anonymous closure>':.
    //     0xe770cc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41308] AnonymousClosure: (0xe771f8), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAll (0xe76fd0)
    //     0xe770d0: ldr             x1, [x1, #0x308]
    // 0xe770d4: r2 = Null
    //     0xe770d4: mov             x2, NULL
    // 0xe770d8: stur            x0, [fp, #-0x10]
    // 0xe770dc: r0 = AllocateClosure()
    //     0xe770dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe770e0: r16 = <Doa, FutureOr<ApiResult<List<Doa>>>>
    //     0xe770e0: add             x16, PP, #0x41, lsl #12  ; [pp+0x41310] TypeArguments: <Doa, FutureOr<ApiResult<List<Doa>>>>
    //     0xe770e4: ldr             x16, [x16, #0x310]
    // 0xe770e8: ldur            lr, [fp, #-0x10]
    // 0xe770ec: stp             lr, x16, [SP, #8]
    // 0xe770f0: str             x0, [SP]
    // 0xe770f4: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe770f4: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe770f8: r0 = ListExtension.to()
    //     0xe770f8: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe770fc: r0 = ReturnAsync()
    //     0xe770fc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe77100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77104: b               #0xe76ff4
    // 0xe77108: r9 = keystore
    //     0xe77108: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7710c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7710c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ApiResult<List<Doa>> <anonymous closure>(dynamic, List<Doa>) {
    // ** addr: 0xe771f8, size: 0x28
    // 0xe771f8: EnterFrame
    //     0xe771f8: stp             fp, lr, [SP, #-0x10]!
    //     0xe771fc: mov             fp, SP
    // 0xe77200: r1 = <List<Doa>>
    //     0xe77200: add             x1, PP, #0x35, lsl #12  ; [pp+0x35da0] TypeArguments: <List<Doa>>
    //     0xe77204: ldr             x1, [x1, #0xda0]
    // 0xe77208: r0 = _$SuccessImpl()
    //     0xe77208: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7720c: ldr             x1, [fp, #0x10]
    // 0xe77210: StoreField: r0->field_b = r1
    //     0xe77210: stur            w1, [x0, #0xb]
    // 0xe77214: LeaveFrame
    //     0xe77214: mov             SP, fp
    //     0xe77218: ldp             fp, lr, [SP], #0x10
    // 0xe7721c: ret
    //     0xe7721c: ret             
  }
  [closure] List<Doa> <anonymous closure>(dynamic, List<Doa>) {
    // ** addr: 0xe77220, size: 0x5c
    // 0xe77220: EnterFrame
    //     0xe77220: stp             fp, lr, [SP, #-0x10]!
    //     0xe77224: mov             fp, SP
    // 0xe77228: AllocStack(0x18)
    //     0xe77228: sub             SP, SP, #0x18
    // 0xe7722c: SetupParameters()
    //     0xe7722c: ldr             x0, [fp, #0x18]
    //     0xe77230: ldur            w2, [x0, #0x17]
    //     0xe77234: add             x2, x2, HEAP, lsl #32
    // 0xe77238: CheckStackOverflow
    //     0xe77238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7723c: cmp             SP, x16
    //     0xe77240: b.ls            #0xe77274
    // 0xe77244: r1 = Function '<anonymous closure>':.
    //     0xe77244: add             x1, PP, #0x41, lsl #12  ; [pp+0x41318] AnonymousClosure: (0x900fb8), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByJuzId (0x900e9c)
    //     0xe77248: ldr             x1, [x1, #0x318]
    // 0xe7724c: r0 = AllocateClosure()
    //     0xe7724c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe77250: r16 = <Doa>
    //     0xe77250: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77254: ldr             lr, [fp, #0x10]
    // 0xe77258: stp             lr, x16, [SP, #8]
    // 0xe7725c: str             x0, [SP]
    // 0xe77260: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe77260: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77264: r0 = ListExtension.filter()
    //     0xe77264: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe77268: LeaveFrame
    //     0xe77268: mov             SP, fp
    //     0xe7726c: ldp             fp, lr, [SP], #0x10
    // 0xe77270: ret
    //     0xe77270: ret             
    // 0xe77274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77278: b               #0xe77244
  }
  _ findAllBookmark(/* No info */) async {
    // ** addr: 0xe7727c, size: 0xdc
    // 0xe7727c: EnterFrame
    //     0xe7727c: stp             fp, lr, [SP, #-0x10]!
    //     0xe77280: mov             fp, SP
    // 0xe77284: AllocStack(0x28)
    //     0xe77284: sub             SP, SP, #0x28
    // 0xe77288: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe77288: stur            NULL, [fp, #-8]
    //     0xe7728c: stur            x1, [fp, #-0x10]
    // 0xe77290: CheckStackOverflow
    //     0xe77290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77294: cmp             SP, x16
    //     0xe77298: b.ls            #0xe77348
    // 0xe7729c: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xe7729c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xe772a0: ldr             x0, [x0, #0xd38]
    //     0xe772a4: bl              #0x661298  ; InitAsyncStub
    // 0xe772a8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe772a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe772ac: ldr             x0, [x0, #0x2728]
    //     0xe772b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe772b4: cmp             w0, w16
    //     0xe772b8: b.ne            #0xe772c4
    //     0xe772bc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe772c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe772c4: r16 = <DoaSubCategory>
    //     0xe772c4: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe772c8: stp             x0, x16, [SP, #8]
    // 0xe772cc: r16 = "v2_doa_bookmark"
    //     0xe772cc: ldr             x16, [PP, #0x7c40]  ; [pp+0x7c40] "v2_doa_bookmark"
    // 0xe772d0: str             x16, [SP]
    // 0xe772d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe772d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe772d8: r0 = box()
    //     0xe772d8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe772dc: mov             x1, x0
    // 0xe772e0: stur            x0, [fp, #-0x10]
    // 0xe772e4: r0 = checkOpen()
    //     0xe772e4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe772e8: ldur            x0, [fp, #-0x10]
    // 0xe772ec: LoadField: r1 = r0->field_1b
    //     0xe772ec: ldur            w1, [x0, #0x1b]
    // 0xe772f0: DecompressPointer r1
    //     0xe772f0: add             x1, x1, HEAP, lsl #32
    // 0xe772f4: r16 = Sentinel
    //     0xe772f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe772f8: cmp             w1, w16
    // 0xe772fc: b.eq            #0xe77350
    // 0xe77300: r0 = getValues()
    //     0xe77300: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe77304: LoadField: r1 = r0->field_7
    //     0xe77304: ldur            w1, [x0, #7]
    // 0xe77308: DecompressPointer r1
    //     0xe77308: add             x1, x1, HEAP, lsl #32
    // 0xe7730c: mov             x2, x0
    // 0xe77310: r0 = _GrowableList.of()
    //     0xe77310: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe77314: r1 = Function '<anonymous closure>':.
    //     0xe77314: add             x1, PP, #0x37, lsl #12  ; [pp+0x37ea0] AnonymousClosure: (0xe77358), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe77318: ldr             x1, [x1, #0xea0]
    // 0xe7731c: r2 = Null
    //     0xe7731c: mov             x2, NULL
    // 0xe77320: stur            x0, [fp, #-0x10]
    // 0xe77324: r0 = AllocateClosure()
    //     0xe77324: bl              #0xec1630  ; AllocateClosureStub
    // 0xe77328: r16 = <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe77328: add             x16, PP, #0x37, lsl #12  ; [pp+0x37e68] TypeArguments: <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe7732c: ldr             x16, [x16, #0xe68]
    // 0xe77330: ldur            lr, [fp, #-0x10]
    // 0xe77334: stp             lr, x16, [SP, #8]
    // 0xe77338: str             x0, [SP]
    // 0xe7733c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe7733c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe77340: r0 = ListExtension.to()
    //     0xe77340: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe77344: r0 = ReturnAsync()
    //     0xe77344: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe77348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7734c: b               #0xe7729c
    // 0xe77350: r9 = keystore
    //     0xe77350: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe77354: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe77354: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ApiResult<List<DoaSubCategory>> <anonymous closure>(dynamic, List<DoaSubCategory>) {
    // ** addr: 0xe77358, size: 0x28
    // 0xe77358: EnterFrame
    //     0xe77358: stp             fp, lr, [SP, #-0x10]!
    //     0xe7735c: mov             fp, SP
    // 0xe77360: r1 = <List<DoaSubCategory>>
    //     0xe77360: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xe77364: ldr             x1, [x1, #0x7d8]
    // 0xe77368: r0 = _$SuccessImpl()
    //     0xe77368: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7736c: ldr             x1, [fp, #0x10]
    // 0xe77370: StoreField: r0->field_b = r1
    //     0xe77370: stur            w1, [x0, #0xb]
    // 0xe77374: LeaveFrame
    //     0xe77374: mov             SP, fp
    //     0xe77378: ldp             fp, lr, [SP], #0x10
    // 0xe7737c: ret
    //     0xe7737c: ret             
  }
  _ findAllHistory(/* No info */) async {
    // ** addr: 0xe777c4, size: 0xac
    // 0xe777c4: EnterFrame
    //     0xe777c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe777c8: mov             fp, SP
    // 0xe777cc: AllocStack(0x28)
    //     0xe777cc: sub             SP, SP, #0x28
    // 0xe777d0: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe777d0: stur            NULL, [fp, #-8]
    //     0xe777d4: stur            x1, [fp, #-0x10]
    // 0xe777d8: CheckStackOverflow
    //     0xe777d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe777dc: cmp             SP, x16
    //     0xe777e0: b.ls            #0xe77860
    // 0xe777e4: InitAsync() -> Future<List<String>>
    //     0xe777e4: add             x0, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0xe777e8: ldr             x0, [x0, #0xb40]
    //     0xe777ec: bl              #0x661298  ; InitAsyncStub
    // 0xe777f0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe777f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe777f4: ldr             x0, [x0, #0x2728]
    //     0xe777f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe777fc: cmp             w0, w16
    //     0xe77800: b.ne            #0xe7780c
    //     0xe77804: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe77808: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7780c: r16 = <String>
    //     0xe7780c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe77810: stp             x0, x16, [SP, #8]
    // 0xe77814: r16 = "v2_doa_search_history"
    //     0xe77814: ldr             x16, [PP, #0x7c48]  ; [pp+0x7c48] "v2_doa_search_history"
    // 0xe77818: str             x16, [SP]
    // 0xe7781c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7781c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77820: r0 = box()
    //     0xe77820: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe77824: mov             x1, x0
    // 0xe77828: stur            x0, [fp, #-0x10]
    // 0xe7782c: r0 = checkOpen()
    //     0xe7782c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe77830: ldur            x0, [fp, #-0x10]
    // 0xe77834: LoadField: r1 = r0->field_1b
    //     0xe77834: ldur            w1, [x0, #0x1b]
    // 0xe77838: DecompressPointer r1
    //     0xe77838: add             x1, x1, HEAP, lsl #32
    // 0xe7783c: r16 = Sentinel
    //     0xe7783c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe77840: cmp             w1, w16
    // 0xe77844: b.eq            #0xe77868
    // 0xe77848: r0 = getValues()
    //     0xe77848: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe7784c: LoadField: r1 = r0->field_7
    //     0xe7784c: ldur            w1, [x0, #7]
    // 0xe77850: DecompressPointer r1
    //     0xe77850: add             x1, x1, HEAP, lsl #32
    // 0xe77854: mov             x2, x0
    // 0xe77858: r0 = _GrowableList.of()
    //     0xe77858: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe7785c: r0 = ReturnAsyncNotFuture()
    //     0xe7785c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe77860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77864: b               #0xe777e4
    // 0xe77868: r9 = keystore
    //     0xe77868: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7786c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7786c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ findLastUpdated(/* No info */) async {
    // ** addr: 0xe77ee8, size: 0xe8
    // 0xe77ee8: EnterFrame
    //     0xe77ee8: stp             fp, lr, [SP, #-0x10]!
    //     0xe77eec: mov             fp, SP
    // 0xe77ef0: AllocStack(0x28)
    //     0xe77ef0: sub             SP, SP, #0x28
    // 0xe77ef4: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe77ef4: stur            NULL, [fp, #-8]
    //     0xe77ef8: stur            x1, [fp, #-0x10]
    // 0xe77efc: CheckStackOverflow
    //     0xe77efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe77f00: cmp             SP, x16
    //     0xe77f04: b.ls            #0xe77fc0
    // 0xe77f08: InitAsync() -> Future<Doa?>
    //     0xe77f08: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a3d8] TypeArguments: <Doa?>
    //     0xe77f0c: ldr             x0, [x0, #0x3d8]
    //     0xe77f10: bl              #0x661298  ; InitAsyncStub
    // 0xe77f14: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe77f14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe77f18: ldr             x0, [x0, #0x2728]
    //     0xe77f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe77f20: cmp             w0, w16
    //     0xe77f24: b.ne            #0xe77f30
    //     0xe77f28: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe77f2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe77f30: r16 = <Doa>
    //     0xe77f30: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77f34: stp             x0, x16, [SP, #8]
    // 0xe77f38: r16 = "v2_doa"
    //     0xe77f38: ldr             x16, [PP, #0x7c38]  ; [pp+0x7c38] "v2_doa"
    // 0xe77f3c: str             x16, [SP]
    // 0xe77f40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe77f40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77f44: r0 = box()
    //     0xe77f44: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe77f48: mov             x1, x0
    // 0xe77f4c: stur            x0, [fp, #-0x10]
    // 0xe77f50: r0 = checkOpen()
    //     0xe77f50: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe77f54: ldur            x0, [fp, #-0x10]
    // 0xe77f58: LoadField: r1 = r0->field_1b
    //     0xe77f58: ldur            w1, [x0, #0x1b]
    // 0xe77f5c: DecompressPointer r1
    //     0xe77f5c: add             x1, x1, HEAP, lsl #32
    // 0xe77f60: r16 = Sentinel
    //     0xe77f60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe77f64: cmp             w1, w16
    // 0xe77f68: b.eq            #0xe77fc8
    // 0xe77f6c: r0 = getValues()
    //     0xe77f6c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe77f70: LoadField: r1 = r0->field_7
    //     0xe77f70: ldur            w1, [x0, #7]
    // 0xe77f74: DecompressPointer r1
    //     0xe77f74: add             x1, x1, HEAP, lsl #32
    // 0xe77f78: mov             x2, x0
    // 0xe77f7c: r0 = _GrowableList.of()
    //     0xe77f7c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe77f80: r1 = Function '<anonymous closure>':.
    //     0xe77f80: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a3e0] AnonymousClosure: (0xe78174), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findLastUpdated (0xe77ee8)
    //     0xe77f84: ldr             x1, [x1, #0x3e0]
    // 0xe77f88: r2 = Null
    //     0xe77f88: mov             x2, NULL
    // 0xe77f8c: stur            x0, [fp, #-0x10]
    // 0xe77f90: r0 = AllocateClosure()
    //     0xe77f90: bl              #0xec1630  ; AllocateClosureStub
    // 0xe77f94: r16 = <Doa>
    //     0xe77f94: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77f98: ldur            lr, [fp, #-0x10]
    // 0xe77f9c: stp             lr, x16, [SP, #8]
    // 0xe77fa0: str             x0, [SP]
    // 0xe77fa4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe77fa4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe77fa8: r0 = ListExtension.sortByDesc()
    //     0xe77fa8: bl              #0xe78010  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortByDesc
    // 0xe77fac: r16 = <Doa>
    //     0xe77fac: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe77fb0: stp             x0, x16, [SP]
    // 0xe77fb4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe77fb4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe77fb8: r0 = ListExtension.firstOrNull()
    //     0xe77fb8: bl              #0xe77fd0  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.firstOrNull
    // 0xe77fbc: r0 = ReturnAsync()
    //     0xe77fbc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe77fc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe77fc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe77fc4: b               #0xe77f08
    // 0xe77fc8: r9 = keystore
    //     0xe77fc8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe77fcc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe77fcc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DateTime <anonymous closure>(dynamic, Doa) {
    // ** addr: 0xe78174, size: 0x38
    // 0xe78174: EnterFrame
    //     0xe78174: stp             fp, lr, [SP, #-0x10]!
    //     0xe78178: mov             fp, SP
    // 0xe7817c: CheckStackOverflow
    //     0xe7817c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78180: cmp             SP, x16
    //     0xe78184: b.ls            #0xe781a4
    // 0xe78188: ldr             x0, [fp, #0x10]
    // 0xe7818c: LoadField: r1 = r0->field_33
    //     0xe7818c: ldur            w1, [x0, #0x33]
    // 0xe78190: DecompressPointer r1
    //     0xe78190: add             x1, x1, HEAP, lsl #32
    // 0xe78194: r0 = parse()
    //     0xe78194: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0xe78198: LeaveFrame
    //     0xe78198: mov             SP, fp
    //     0xe7819c: ldp             fp, lr, [SP], #0x10
    // 0xe781a0: ret
    //     0xe781a0: ret             
    // 0xe781a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe781a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe781a8: b               #0xe78188
  }
  _ updateAll(/* No info */) {
    // ** addr: 0xe781ac, size: 0x9c
    // 0xe781ac: EnterFrame
    //     0xe781ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe781b0: mov             fp, SP
    // 0xe781b4: AllocStack(0x28)
    //     0xe781b4: sub             SP, SP, #0x28
    // 0xe781b8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe781b8: stur            x2, [fp, #-8]
    // 0xe781bc: CheckStackOverflow
    //     0xe781bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe781c0: cmp             SP, x16
    //     0xe781c4: b.ls            #0xe78240
    // 0xe781c8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe781c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe781cc: ldr             x0, [x0, #0x2728]
    //     0xe781d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe781d4: cmp             w0, w16
    //     0xe781d8: b.ne            #0xe781e4
    //     0xe781dc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe781e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe781e4: r16 = <Doa>
    //     0xe781e4: ldr             x16, [PP, #0x7bb0]  ; [pp+0x7bb0] TypeArguments: <Doa>
    // 0xe781e8: stp             x0, x16, [SP, #8]
    // 0xe781ec: r16 = "v2_doa"
    //     0xe781ec: ldr             x16, [PP, #0x7c38]  ; [pp+0x7c38] "v2_doa"
    // 0xe781f0: str             x16, [SP]
    // 0xe781f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe781f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe781f8: r0 = box()
    //     0xe781f8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe781fc: r1 = Function '<anonymous closure>':.
    //     0xe781fc: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a3c0] AnonymousClosure: (0xe78248), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::updateAll (0xe781ac)
    //     0xe78200: ldr             x1, [x1, #0x3c0]
    // 0xe78204: r2 = Null
    //     0xe78204: mov             x2, NULL
    // 0xe78208: stur            x0, [fp, #-0x10]
    // 0xe7820c: r0 = AllocateClosure()
    //     0xe7820c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78210: ldur            x2, [fp, #-8]
    // 0xe78214: mov             x3, x0
    // 0xe78218: r1 = <dynamic, Doa>
    //     0xe78218: add             x1, PP, #0x17, lsl #12  ; [pp+0x17dd0] TypeArguments: <dynamic, Doa>
    //     0xe7821c: ldr             x1, [x1, #0xdd0]
    // 0xe78220: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe78220: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe78224: r0 = LinkedHashMap.fromIterable()
    //     0xe78224: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xe78228: ldur            x1, [fp, #-0x10]
    // 0xe7822c: mov             x2, x0
    // 0xe78230: r0 = putAll()
    //     0xe78230: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xe78234: LeaveFrame
    //     0xe78234: mov             SP, fp
    //     0xe78238: ldp             fp, lr, [SP], #0x10
    // 0xe7823c: ret
    //     0xe7823c: ret             
    // 0xe78240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78244: b               #0xe781c8
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe78248, size: 0x4c
    // 0xe78248: EnterFrame
    //     0xe78248: stp             fp, lr, [SP, #-0x10]!
    //     0xe7824c: mov             fp, SP
    // 0xe78250: AllocStack(0x8)
    //     0xe78250: sub             SP, SP, #8
    // 0xe78254: CheckStackOverflow
    //     0xe78254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78258: cmp             SP, x16
    //     0xe7825c: b.ls            #0xe7828c
    // 0xe78260: ldr             x16, [fp, #0x10]
    // 0xe78264: str             x16, [SP]
    // 0xe78268: r4 = 0
    //     0xe78268: movz            x4, #0
    // 0xe7826c: ldr             x0, [SP]
    // 0xe78270: r16 = UnlinkedCall_0x5f3c08
    //     0xe78270: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a3c8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xe78274: add             x16, x16, #0x3c8
    // 0xe78278: ldp             x5, lr, [x16]
    // 0xe7827c: blr             lr
    // 0xe78280: LeaveFrame
    //     0xe78280: mov             SP, fp
    //     0xe78284: ldp             fp, lr, [SP], #0x10
    // 0xe78288: ret
    //     0xe78288: ret             
    // 0xe7828c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7828c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78290: b               #0xe78260
  }
  _ updateAllCategory(/* No info */) async {
    // ** addr: 0xe78294, size: 0xa4
    // 0xe78294: EnterFrame
    //     0xe78294: stp             fp, lr, [SP, #-0x10]!
    //     0xe78298: mov             fp, SP
    // 0xe7829c: AllocStack(0x30)
    //     0xe7829c: sub             SP, SP, #0x30
    // 0xe782a0: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe782a0: stur            NULL, [fp, #-8]
    //     0xe782a4: stur            x1, [fp, #-0x10]
    //     0xe782a8: stur            x2, [fp, #-0x18]
    // 0xe782ac: CheckStackOverflow
    //     0xe782ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe782b0: cmp             SP, x16
    //     0xe782b4: b.ls            #0xe78330
    // 0xe782b8: InitAsync() -> Future<void?>
    //     0xe782b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe782bc: bl              #0x661298  ; InitAsyncStub
    // 0xe782c0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe782c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe782c4: ldr             x0, [x0, #0x2728]
    //     0xe782c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe782cc: cmp             w0, w16
    //     0xe782d0: b.ne            #0xe782dc
    //     0xe782d4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe782d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe782dc: r16 = <DoaCategory>
    //     0xe782dc: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe782e0: stp             x0, x16, [SP, #8]
    // 0xe782e4: r16 = "v2_doa_categories"
    //     0xe782e4: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "v2_doa_categories"
    // 0xe782e8: str             x16, [SP]
    // 0xe782ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe782ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe782f0: r0 = box()
    //     0xe782f0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe782f4: r1 = Function '<anonymous closure>':.
    //     0xe782f4: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a3a8] AnonymousClosure: (0xe78338), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::updateAllCategory (0xe78294)
    //     0xe782f8: ldr             x1, [x1, #0x3a8]
    // 0xe782fc: r2 = Null
    //     0xe782fc: mov             x2, NULL
    // 0xe78300: stur            x0, [fp, #-0x10]
    // 0xe78304: r0 = AllocateClosure()
    //     0xe78304: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78308: ldur            x2, [fp, #-0x18]
    // 0xe7830c: mov             x3, x0
    // 0xe78310: r1 = <dynamic, DoaCategory>
    //     0xe78310: add             x1, PP, #0x19, lsl #12  ; [pp+0x19d78] TypeArguments: <dynamic, DoaCategory>
    //     0xe78314: ldr             x1, [x1, #0xd78]
    // 0xe78318: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe78318: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7831c: r0 = LinkedHashMap.fromIterable()
    //     0xe7831c: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xe78320: ldur            x1, [fp, #-0x10]
    // 0xe78324: mov             x2, x0
    // 0xe78328: r0 = putAll()
    //     0xe78328: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xe7832c: r0 = ReturnAsync()
    //     0xe7832c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe78330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78334: b               #0xe782b8
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe78338, size: 0x4c
    // 0xe78338: EnterFrame
    //     0xe78338: stp             fp, lr, [SP, #-0x10]!
    //     0xe7833c: mov             fp, SP
    // 0xe78340: AllocStack(0x8)
    //     0xe78340: sub             SP, SP, #8
    // 0xe78344: CheckStackOverflow
    //     0xe78344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78348: cmp             SP, x16
    //     0xe7834c: b.ls            #0xe7837c
    // 0xe78350: ldr             x16, [fp, #0x10]
    // 0xe78354: str             x16, [SP]
    // 0xe78358: r4 = 0
    //     0xe78358: movz            x4, #0
    // 0xe7835c: ldr             x0, [SP]
    // 0xe78360: r16 = UnlinkedCall_0x5f3c08
    //     0xe78360: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a3b0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xe78364: add             x16, x16, #0x3b0
    // 0xe78368: ldp             x5, lr, [x16]
    // 0xe7836c: blr             lr
    // 0xe78370: LeaveFrame
    //     0xe78370: mov             SP, fp
    //     0xe78374: ldp             fp, lr, [SP], #0x10
    // 0xe78378: ret
    //     0xe78378: ret             
    // 0xe7837c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7837c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78380: b               #0xe78350
  }
  _ updateAllHistory(/* No info */) async {
    // ** addr: 0xe78384, size: 0xb0
    // 0xe78384: EnterFrame
    //     0xe78384: stp             fp, lr, [SP, #-0x10]!
    //     0xe78388: mov             fp, SP
    // 0xe7838c: AllocStack(0x38)
    //     0xe7838c: sub             SP, SP, #0x38
    // 0xe78390: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe78390: stur            NULL, [fp, #-8]
    //     0xe78394: stur            x1, [fp, #-0x10]
    //     0xe78398: stur            x2, [fp, #-0x18]
    // 0xe7839c: CheckStackOverflow
    //     0xe7839c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe783a0: cmp             SP, x16
    //     0xe783a4: b.ls            #0xe7842c
    // 0xe783a8: InitAsync() -> Future<void?>
    //     0xe783a8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe783ac: bl              #0x661298  ; InitAsyncStub
    // 0xe783b0: ldur            x0, [fp, #-0x10]
    // 0xe783b4: LoadField: r1 = r0->field_7
    //     0xe783b4: ldur            w1, [x0, #7]
    // 0xe783b8: DecompressPointer r1
    //     0xe783b8: add             x1, x1, HEAP, lsl #32
    // 0xe783bc: r0 = doaSearchHistory()
    //     0xe783bc: bl              #0xe77870  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::doaSearchHistory
    // 0xe783c0: mov             x1, x0
    // 0xe783c4: r0 = clear()
    //     0xe783c4: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0xe783c8: mov             x1, x0
    // 0xe783cc: stur            x1, [fp, #-0x20]
    // 0xe783d0: r0 = Await()
    //     0xe783d0: bl              #0x661044  ; AwaitStub
    // 0xe783d4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe783d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe783d8: ldr             x0, [x0, #0x2728]
    //     0xe783dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe783e0: cmp             w0, w16
    //     0xe783e4: b.ne            #0xe783f0
    //     0xe783e8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe783ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe783f0: r16 = <String>
    //     0xe783f0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe783f4: stp             x0, x16, [SP, #8]
    // 0xe783f8: r16 = "v2_doa_search_history"
    //     0xe783f8: ldr             x16, [PP, #0x7c48]  ; [pp+0x7c48] "v2_doa_search_history"
    // 0xe783fc: str             x16, [SP]
    // 0xe78400: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78400: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78404: r0 = box()
    //     0xe78404: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe78408: mov             x1, x0
    // 0xe7840c: ldur            x2, [fp, #-0x18]
    // 0xe78410: r0 = addAll()
    //     0xe78410: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0xe78414: r16 = <Iterable<int>>
    //     0xe78414: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2daf8] TypeArguments: <Iterable<int>>
    //     0xe78418: ldr             x16, [x16, #0xaf8]
    // 0xe7841c: stp             x0, x16, [SP]
    // 0xe78420: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe78420: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe78424: r0 = FutureExtensions.ignore()
    //     0xe78424: bl              #0x7082c8  ; [dart:async] ::FutureExtensions.ignore
    // 0xe78428: r0 = ReturnAsync()
    //     0xe78428: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7842c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7842c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78430: b               #0xe783a8
  }
  _ updateBookmark(/* No info */) async {
    // ** addr: 0xe78434, size: 0x100
    // 0xe78434: EnterFrame
    //     0xe78434: stp             fp, lr, [SP, #-0x10]!
    //     0xe78438: mov             fp, SP
    // 0xe7843c: AllocStack(0x38)
    //     0xe7843c: sub             SP, SP, #0x38
    // 0xe78440: SetupParameters(DoaLocalRepository this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe78440: stur            NULL, [fp, #-8]
    //     0xe78444: stur            x1, [fp, #-0x10]
    //     0xe78448: mov             x16, x2
    //     0xe7844c: mov             x2, x1
    //     0xe78450: mov             x1, x16
    //     0xe78454: stur            x1, [fp, #-0x18]
    //     0xe78458: stur            x3, [fp, #-0x20]
    // 0xe7845c: CheckStackOverflow
    //     0xe7845c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78460: cmp             SP, x16
    //     0xe78464: b.ls            #0xe7852c
    // 0xe78468: InitAsync() -> Future<void?>
    //     0xe78468: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe7846c: bl              #0x661298  ; InitAsyncStub
    // 0xe78470: ldur            x0, [fp, #-0x20]
    // 0xe78474: tbz             w0, #4, #0xe784c4
    // 0xe78478: ldur            x3, [fp, #-0x18]
    // 0xe7847c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7847c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe78480: ldr             x0, [x0, #0x2728]
    //     0xe78484: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78488: cmp             w0, w16
    //     0xe7848c: b.ne            #0xe78498
    //     0xe78490: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe78494: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe78498: r16 = <DoaSubCategory>
    //     0xe78498: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7849c: stp             x0, x16, [SP, #8]
    // 0xe784a0: r16 = "v2_doa_bookmark"
    //     0xe784a0: ldr             x16, [PP, #0x7c40]  ; [pp+0x7c40] "v2_doa_bookmark"
    // 0xe784a4: str             x16, [SP]
    // 0xe784a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe784a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe784ac: r0 = box()
    //     0xe784ac: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe784b0: ldur            x3, [fp, #-0x18]
    // 0xe784b4: LoadField: r2 = r3->field_7
    //     0xe784b4: ldur            x2, [x3, #7]
    // 0xe784b8: mov             x1, x0
    // 0xe784bc: r0 = delete()
    //     0xe784bc: bl              #0xa424f8  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::delete
    // 0xe784c0: r0 = ReturnAsync()
    //     0xe784c0: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe784c4: ldur            x3, [fp, #-0x18]
    // 0xe784c8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe784c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe784cc: ldr             x0, [x0, #0x2728]
    //     0xe784d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe784d4: cmp             w0, w16
    //     0xe784d8: b.ne            #0xe784e4
    //     0xe784dc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe784e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe784e4: r16 = <DoaSubCategory>
    //     0xe784e4: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe784e8: stp             x0, x16, [SP, #8]
    // 0xe784ec: r16 = "v2_doa_bookmark"
    //     0xe784ec: ldr             x16, [PP, #0x7c40]  ; [pp+0x7c40] "v2_doa_bookmark"
    // 0xe784f0: str             x16, [SP]
    // 0xe784f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe784f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe784f8: r0 = box()
    //     0xe784f8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe784fc: mov             x2, x0
    // 0xe78500: ldur            x3, [fp, #-0x18]
    // 0xe78504: LoadField: r4 = r3->field_7
    //     0xe78504: ldur            x4, [x3, #7]
    // 0xe78508: r0 = BoxInt64Instr(r4)
    //     0xe78508: sbfiz           x0, x4, #1, #0x1f
    //     0xe7850c: cmp             x4, x0, asr #1
    //     0xe78510: b.eq            #0xe7851c
    //     0xe78514: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe78518: stur            x4, [x0, #7]
    // 0xe7851c: mov             x1, x2
    // 0xe78520: mov             x2, x0
    // 0xe78524: r0 = put()
    //     0xe78524: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xe78528: r0 = ReturnAsync()
    //     0xe78528: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7852c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7852c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78530: b               #0xe78468
  }
  _ findSubCategoryById(/* No info */) async {
    // ** addr: 0xe78534, size: 0xc0
    // 0xe78534: EnterFrame
    //     0xe78534: stp             fp, lr, [SP, #-0x10]!
    //     0xe78538: mov             fp, SP
    // 0xe7853c: AllocStack(0x30)
    //     0xe7853c: sub             SP, SP, #0x30
    // 0xe78540: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe78540: stur            NULL, [fp, #-8]
    //     0xe78544: stur            x1, [fp, #-0x10]
    //     0xe78548: stur            x2, [fp, #-0x18]
    // 0xe7854c: CheckStackOverflow
    //     0xe7854c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78550: cmp             SP, x16
    //     0xe78554: b.ls            #0xe785ec
    // 0xe78558: InitAsync() -> Future<ApiResult<DoaSubCategory>>
    //     0xe78558: add             x0, PP, #0x40, lsl #12  ; [pp+0x40568] TypeArguments: <ApiResult<DoaSubCategory>>
    //     0xe7855c: ldr             x0, [x0, #0x568]
    //     0xe78560: bl              #0x661298  ; InitAsyncStub
    // 0xe78564: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe78564: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe78568: ldr             x0, [x0, #0x2728]
    //     0xe7856c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78570: cmp             w0, w16
    //     0xe78574: b.ne            #0xe78580
    //     0xe78578: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7857c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe78580: r16 = <DoaSubCategory>
    //     0xe78580: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe78584: stp             x0, x16, [SP, #8]
    // 0xe78588: r16 = "v2_doa_sub_categories"
    //     0xe78588: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe7858c: str             x16, [SP]
    // 0xe78590: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78590: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78594: r0 = box()
    //     0xe78594: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe78598: mov             x3, x0
    // 0xe7859c: ldur            x2, [fp, #-0x18]
    // 0xe785a0: r0 = BoxInt64Instr(r2)
    //     0xe785a0: sbfiz           x0, x2, #1, #0x1f
    //     0xe785a4: cmp             x2, x0, asr #1
    //     0xe785a8: b.eq            #0xe785b4
    //     0xe785ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe785b0: stur            x2, [x0, #7]
    // 0xe785b4: mov             x1, x3
    // 0xe785b8: mov             x2, x0
    // 0xe785bc: r0 = get()
    //     0xe785bc: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0xe785c0: stur            x0, [fp, #-0x10]
    // 0xe785c4: cmp             w0, NULL
    // 0xe785c8: b.ne            #0xe785d8
    // 0xe785cc: r0 = Instance__$FailureImpl
    //     0xe785cc: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a388] Obj!_$FailureImpl<DoaSubCategory>@e0e561
    //     0xe785d0: ldr             x0, [x0, #0x388]
    // 0xe785d4: r0 = ReturnAsyncNotFuture()
    //     0xe785d4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe785d8: r1 = <DoaSubCategory>
    //     0xe785d8: ldr             x1, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe785dc: r0 = _$SuccessImpl()
    //     0xe785dc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe785e0: ldur            x1, [fp, #-0x10]
    // 0xe785e4: StoreField: r0->field_b = r1
    //     0xe785e4: stur            w1, [x0, #0xb]
    // 0xe785e8: r0 = ReturnAsyncNotFuture()
    //     0xe785e8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe785ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe785ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe785f0: b               #0xe78558
  }
  _ findSubCategoryByIdWithRelated(/* No info */) async {
    // ** addr: 0xe785f4, size: 0x1b0
    // 0xe785f4: EnterFrame
    //     0xe785f4: stp             fp, lr, [SP, #-0x10]!
    //     0xe785f8: mov             fp, SP
    // 0xe785fc: AllocStack(0x40)
    //     0xe785fc: sub             SP, SP, #0x40
    // 0xe78600: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe78600: stur            NULL, [fp, #-8]
    //     0xe78604: stur            x1, [fp, #-0x10]
    //     0xe78608: stur            x2, [fp, #-0x18]
    // 0xe7860c: CheckStackOverflow
    //     0xe7860c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78610: cmp             SP, x16
    //     0xe78614: b.ls            #0xe78794
    // 0xe78618: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xe78618: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xe7861c: ldr             x0, [x0, #0xd38]
    //     0xe78620: bl              #0x661298  ; InitAsyncStub
    // 0xe78624: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe78624: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe78628: ldr             x0, [x0, #0x2728]
    //     0xe7862c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78630: cmp             w0, w16
    //     0xe78634: b.ne            #0xe78640
    //     0xe78638: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7863c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe78640: stur            x0, [fp, #-0x10]
    // 0xe78644: r16 = <DoaSubCategory>
    //     0xe78644: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe78648: stp             x0, x16, [SP, #8]
    // 0xe7864c: r16 = "v2_doa_sub_categories"
    //     0xe7864c: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe78650: str             x16, [SP]
    // 0xe78654: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78654: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78658: r0 = box()
    //     0xe78658: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7865c: mov             x3, x0
    // 0xe78660: ldur            x2, [fp, #-0x18]
    // 0xe78664: r0 = BoxInt64Instr(r2)
    //     0xe78664: sbfiz           x0, x2, #1, #0x1f
    //     0xe78668: cmp             x2, x0, asr #1
    //     0xe7866c: b.eq            #0xe78678
    //     0xe78670: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe78674: stur            x2, [x0, #7]
    // 0xe78678: mov             x1, x3
    // 0xe7867c: mov             x2, x0
    // 0xe78680: r0 = get()
    //     0xe78680: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0xe78684: stur            x0, [fp, #-0x20]
    // 0xe78688: r1 = 1
    //     0xe78688: movz            x1, #0x1
    // 0xe7868c: r0 = AllocateContext()
    //     0xe7868c: bl              #0xec126c  ; AllocateContextStub
    // 0xe78690: mov             x1, x0
    // 0xe78694: ldur            x0, [fp, #-0x20]
    // 0xe78698: stur            x1, [fp, #-0x28]
    // 0xe7869c: StoreField: r1->field_f = r0
    //     0xe7869c: stur            w0, [x1, #0xf]
    // 0xe786a0: cmp             w0, NULL
    // 0xe786a4: b.ne            #0xe786b4
    // 0xe786a8: r0 = Instance__$FailureImpl
    //     0xe786a8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37e50] Obj!_$FailureImpl<List<DoaSubCategory>>@e0e571
    //     0xe786ac: ldr             x0, [x0, #0xe50]
    // 0xe786b0: r0 = ReturnAsyncNotFuture()
    //     0xe786b0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe786b4: r16 = <DoaSubCategory>
    //     0xe786b4: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe786b8: ldur            lr, [fp, #-0x10]
    // 0xe786bc: stp             lr, x16, [SP, #8]
    // 0xe786c0: r16 = "v2_doa_sub_categories"
    //     0xe786c0: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe786c4: str             x16, [SP]
    // 0xe786c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe786c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe786cc: r0 = box()
    //     0xe786cc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe786d0: mov             x1, x0
    // 0xe786d4: stur            x0, [fp, #-0x10]
    // 0xe786d8: r0 = checkOpen()
    //     0xe786d8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe786dc: ldur            x0, [fp, #-0x10]
    // 0xe786e0: LoadField: r1 = r0->field_1b
    //     0xe786e0: ldur            w1, [x0, #0x1b]
    // 0xe786e4: DecompressPointer r1
    //     0xe786e4: add             x1, x1, HEAP, lsl #32
    // 0xe786e8: r16 = Sentinel
    //     0xe786e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe786ec: cmp             w1, w16
    // 0xe786f0: b.eq            #0xe7879c
    // 0xe786f4: r0 = getValues()
    //     0xe786f4: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe786f8: LoadField: r1 = r0->field_7
    //     0xe786f8: ldur            w1, [x0, #7]
    // 0xe786fc: DecompressPointer r1
    //     0xe786fc: add             x1, x1, HEAP, lsl #32
    // 0xe78700: mov             x2, x0
    // 0xe78704: r0 = _GrowableList.of()
    //     0xe78704: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe78708: ldur            x2, [fp, #-0x28]
    // 0xe7870c: r1 = Function '<anonymous closure>':.
    //     0xe7870c: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a368] AnonymousClosure: (0xe7884c), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findSubCategoryByIdWithRelated (0xe785f4)
    //     0xe78710: ldr             x1, [x1, #0x368]
    // 0xe78714: stur            x0, [fp, #-0x10]
    // 0xe78718: r0 = AllocateClosure()
    //     0xe78718: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7871c: r16 = <DoaSubCategory>
    //     0xe7871c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe78720: ldur            lr, [fp, #-0x10]
    // 0xe78724: stp             lr, x16, [SP, #8]
    // 0xe78728: str             x0, [SP]
    // 0xe7872c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7872c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78730: r0 = ListExtension.filter()
    //     0xe78730: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe78734: r1 = Function '<anonymous closure>':.
    //     0xe78734: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a370] AnonymousClosure: (0xe787a4), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe78738: ldr             x1, [x1, #0x370]
    // 0xe7873c: r2 = Null
    //     0xe7873c: mov             x2, NULL
    // 0xe78740: stur            x0, [fp, #-0x10]
    // 0xe78744: r0 = AllocateClosure()
    //     0xe78744: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78748: r16 = <DoaSubCategory>
    //     0xe78748: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7874c: ldur            lr, [fp, #-0x10]
    // 0xe78750: stp             lr, x16, [SP, #8]
    // 0xe78754: str             x0, [SP]
    // 0xe78758: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78758: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7875c: r0 = ListExtension.sortBy()
    //     0xe7875c: bl              #0x7ec728  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortBy
    // 0xe78760: r1 = Function '<anonymous closure>':.
    //     0xe78760: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a378] AnonymousClosure: (0xe77358), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe78764: ldr             x1, [x1, #0x378]
    // 0xe78768: r2 = Null
    //     0xe78768: mov             x2, NULL
    // 0xe7876c: stur            x0, [fp, #-0x10]
    // 0xe78770: r0 = AllocateClosure()
    //     0xe78770: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78774: r16 = <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe78774: add             x16, PP, #0x37, lsl #12  ; [pp+0x37e68] TypeArguments: <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe78778: ldr             x16, [x16, #0xe68]
    // 0xe7877c: ldur            lr, [fp, #-0x10]
    // 0xe78780: stp             lr, x16, [SP, #8]
    // 0xe78784: str             x0, [SP]
    // 0xe78788: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe78788: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe7878c: r0 = ListExtension.to()
    //     0xe7878c: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe78790: r0 = ReturnAsync()
    //     0xe78790: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe78794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78798: b               #0xe78618
    // 0xe7879c: r9 = keystore
    //     0xe7879c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe787a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe787a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xe787a4, size: 0x54
    // 0xe787a4: ldr             x2, [SP]
    // 0xe787a8: LoadField: r3 = r2->field_1f
    //     0xe787a8: ldur            w3, [x2, #0x1f]
    // 0xe787ac: DecompressPointer r3
    //     0xe787ac: add             x3, x3, HEAP, lsl #32
    // 0xe787b0: cmp             w3, NULL
    // 0xe787b4: b.ne            #0xe787c4
    // 0xe787b8: LoadField: r4 = r2->field_7
    //     0xe787b8: ldur            x4, [x2, #7]
    // 0xe787bc: mov             x2, x4
    // 0xe787c0: b               #0xe787d0
    // 0xe787c4: r2 = LoadInt32Instr(r3)
    //     0xe787c4: sbfx            x2, x3, #1, #0x1f
    //     0xe787c8: tbz             w3, #0, #0xe787d0
    //     0xe787cc: ldur            x2, [x3, #7]
    // 0xe787d0: r0 = BoxInt64Instr(r2)
    //     0xe787d0: sbfiz           x0, x2, #1, #0x1f
    //     0xe787d4: cmp             x2, x0, asr #1
    //     0xe787d8: b.eq            #0xe787f4
    //     0xe787dc: stp             fp, lr, [SP, #-0x10]!
    //     0xe787e0: mov             fp, SP
    //     0xe787e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe787e8: mov             SP, fp
    //     0xe787ec: ldp             fp, lr, [SP], #0x10
    //     0xe787f0: stur            x2, [x0, #7]
    // 0xe787f4: ret
    //     0xe787f4: ret             
  }
  [closure] List<DoaSubCategory> <anonymous closure>(dynamic, List<DoaSubCategory>) {
    // ** addr: 0xe787f8, size: 0x54
    // 0xe787f8: EnterFrame
    //     0xe787f8: stp             fp, lr, [SP, #-0x10]!
    //     0xe787fc: mov             fp, SP
    // 0xe78800: AllocStack(0x18)
    //     0xe78800: sub             SP, SP, #0x18
    // 0xe78804: CheckStackOverflow
    //     0xe78804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78808: cmp             SP, x16
    //     0xe7880c: b.ls            #0xe78844
    // 0xe78810: r1 = Function '<anonymous closure>':.
    //     0xe78810: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e78] AnonymousClosure: (0xe787a4), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe78814: ldr             x1, [x1, #0xe78]
    // 0xe78818: r2 = Null
    //     0xe78818: mov             x2, NULL
    // 0xe7881c: r0 = AllocateClosure()
    //     0xe7881c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78820: r16 = <DoaSubCategory>
    //     0xe78820: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe78824: ldr             lr, [fp, #0x10]
    // 0xe78828: stp             lr, x16, [SP, #8]
    // 0xe7882c: str             x0, [SP]
    // 0xe78830: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78830: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78834: r0 = ListExtension.sortBy()
    //     0xe78834: bl              #0x7ec728  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortBy
    // 0xe78838: LeaveFrame
    //     0xe78838: mov             SP, fp
    //     0xe7883c: ldp             fp, lr, [SP], #0x10
    // 0xe78840: ret
    //     0xe78840: ret             
    // 0xe78844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78848: b               #0xe78810
  }
  [closure] bool <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xe7884c, size: 0x48
    // 0xe7884c: ldr             x1, [SP, #8]
    // 0xe78850: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe78850: ldur            w2, [x1, #0x17]
    // 0xe78854: DecompressPointer r2
    //     0xe78854: add             x2, x2, HEAP, lsl #32
    // 0xe78858: ldr             x1, [SP]
    // 0xe7885c: LoadField: r3 = r1->field_13
    //     0xe7885c: ldur            x3, [x1, #0x13]
    // 0xe78860: LoadField: r1 = r2->field_f
    //     0xe78860: ldur            w1, [x2, #0xf]
    // 0xe78864: DecompressPointer r1
    //     0xe78864: add             x1, x1, HEAP, lsl #32
    // 0xe78868: cmp             w1, NULL
    // 0xe7886c: b.eq            #0xe78888
    // 0xe78870: LoadField: r2 = r1->field_13
    //     0xe78870: ldur            x2, [x1, #0x13]
    // 0xe78874: cmp             x3, x2
    // 0xe78878: r16 = true
    //     0xe78878: add             x16, NULL, #0x20  ; true
    // 0xe7887c: r17 = false
    //     0xe7887c: add             x17, NULL, #0x30  ; false
    // 0xe78880: csel            x0, x16, x17, eq
    // 0xe78884: ret
    //     0xe78884: ret             
    // 0xe78888: EnterFrame
    //     0xe78888: stp             fp, lr, [SP, #-0x10]!
    //     0xe7888c: mov             fp, SP
    // 0xe78890: r0 = NullErrorSharedWithoutFPURegs()
    //     0xe78890: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ findLastUpdatedSubCategory(/* No info */) async {
    // ** addr: 0xe79848, size: 0xe8
    // 0xe79848: EnterFrame
    //     0xe79848: stp             fp, lr, [SP, #-0x10]!
    //     0xe7984c: mov             fp, SP
    // 0xe79850: AllocStack(0x28)
    //     0xe79850: sub             SP, SP, #0x28
    // 0xe79854: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe79854: stur            NULL, [fp, #-8]
    //     0xe79858: stur            x1, [fp, #-0x10]
    // 0xe7985c: CheckStackOverflow
    //     0xe7985c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79860: cmp             SP, x16
    //     0xe79864: b.ls            #0xe79920
    // 0xe79868: InitAsync() -> Future<DoaSubCategory?>
    //     0xe79868: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a3e8] TypeArguments: <DoaSubCategory?>
    //     0xe7986c: ldr             x0, [x0, #0x3e8]
    //     0xe79870: bl              #0x661298  ; InitAsyncStub
    // 0xe79874: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79874: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79878: ldr             x0, [x0, #0x2728]
    //     0xe7987c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe79880: cmp             w0, w16
    //     0xe79884: b.ne            #0xe79890
    //     0xe79888: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7988c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe79890: r16 = <DoaSubCategory>
    //     0xe79890: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe79894: stp             x0, x16, [SP, #8]
    // 0xe79898: r16 = "v2_doa_sub_categories"
    //     0xe79898: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe7989c: str             x16, [SP]
    // 0xe798a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe798a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe798a4: r0 = box()
    //     0xe798a4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe798a8: mov             x1, x0
    // 0xe798ac: stur            x0, [fp, #-0x10]
    // 0xe798b0: r0 = checkOpen()
    //     0xe798b0: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe798b4: ldur            x0, [fp, #-0x10]
    // 0xe798b8: LoadField: r1 = r0->field_1b
    //     0xe798b8: ldur            w1, [x0, #0x1b]
    // 0xe798bc: DecompressPointer r1
    //     0xe798bc: add             x1, x1, HEAP, lsl #32
    // 0xe798c0: r16 = Sentinel
    //     0xe798c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe798c4: cmp             w1, w16
    // 0xe798c8: b.eq            #0xe79928
    // 0xe798cc: r0 = getValues()
    //     0xe798cc: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe798d0: LoadField: r1 = r0->field_7
    //     0xe798d0: ldur            w1, [x0, #7]
    // 0xe798d4: DecompressPointer r1
    //     0xe798d4: add             x1, x1, HEAP, lsl #32
    // 0xe798d8: mov             x2, x0
    // 0xe798dc: r0 = _GrowableList.of()
    //     0xe798dc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe798e0: r1 = Function '<anonymous closure>':.
    //     0xe798e0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51668] AnonymousClosure: (0xe79930), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findLastUpdatedSubCategory (0xe79848)
    //     0xe798e4: ldr             x1, [x1, #0x668]
    // 0xe798e8: r2 = Null
    //     0xe798e8: mov             x2, NULL
    // 0xe798ec: stur            x0, [fp, #-0x10]
    // 0xe798f0: r0 = AllocateClosure()
    //     0xe798f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe798f4: r16 = <DoaSubCategory>
    //     0xe798f4: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe798f8: ldur            lr, [fp, #-0x10]
    // 0xe798fc: stp             lr, x16, [SP, #8]
    // 0xe79900: str             x0, [SP]
    // 0xe79904: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79904: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79908: r0 = ListExtension.sortByDesc()
    //     0xe79908: bl              #0xe78010  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortByDesc
    // 0xe7990c: r16 = <DoaSubCategory>
    //     0xe7990c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe79910: stp             x0, x16, [SP]
    // 0xe79914: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe79914: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe79918: r0 = ListExtension.firstOrNull()
    //     0xe79918: bl              #0xe77fd0  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.firstOrNull
    // 0xe7991c: r0 = ReturnAsync()
    //     0xe7991c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe79920: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79920: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79924: b               #0xe79868
    // 0xe79928: r9 = keystore
    //     0xe79928: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7992c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7992c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DateTime <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xe79930, size: 0x38
    // 0xe79930: EnterFrame
    //     0xe79930: stp             fp, lr, [SP, #-0x10]!
    //     0xe79934: mov             fp, SP
    // 0xe79938: CheckStackOverflow
    //     0xe79938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7993c: cmp             SP, x16
    //     0xe79940: b.ls            #0xe79960
    // 0xe79944: ldr             x0, [fp, #0x10]
    // 0xe79948: LoadField: r1 = r0->field_27
    //     0xe79948: ldur            w1, [x0, #0x27]
    // 0xe7994c: DecompressPointer r1
    //     0xe7994c: add             x1, x1, HEAP, lsl #32
    // 0xe79950: r0 = parse()
    //     0xe79950: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0xe79954: LeaveFrame
    //     0xe79954: mov             SP, fp
    //     0xe79958: ldp             fp, lr, [SP], #0x10
    // 0xe7995c: ret
    //     0xe7995c: ret             
    // 0xe79960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79960: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79964: b               #0xe79944
  }
  _ updateAllSubCategory(/* No info */) async {
    // ** addr: 0xe79968, size: 0xa4
    // 0xe79968: EnterFrame
    //     0xe79968: stp             fp, lr, [SP, #-0x10]!
    //     0xe7996c: mov             fp, SP
    // 0xe79970: AllocStack(0x30)
    //     0xe79970: sub             SP, SP, #0x30
    // 0xe79974: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe79974: stur            NULL, [fp, #-8]
    //     0xe79978: stur            x1, [fp, #-0x10]
    //     0xe7997c: stur            x2, [fp, #-0x18]
    // 0xe79980: CheckStackOverflow
    //     0xe79980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79984: cmp             SP, x16
    //     0xe79988: b.ls            #0xe79a04
    // 0xe7998c: InitAsync() -> Future<void?>
    //     0xe7998c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe79990: bl              #0x661298  ; InitAsyncStub
    // 0xe79994: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79994: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79998: ldr             x0, [x0, #0x2728]
    //     0xe7999c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe799a0: cmp             w0, w16
    //     0xe799a4: b.ne            #0xe799b0
    //     0xe799a8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe799ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe799b0: r16 = <DoaSubCategory>
    //     0xe799b0: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe799b4: stp             x0, x16, [SP, #8]
    // 0xe799b8: r16 = "v2_doa_sub_categories"
    //     0xe799b8: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe799bc: str             x16, [SP]
    // 0xe799c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe799c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe799c4: r0 = box()
    //     0xe799c4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe799c8: r1 = Function '<anonymous closure>':.
    //     0xe799c8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a390] AnonymousClosure: (0xe79a0c), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::updateAllSubCategory (0xe79968)
    //     0xe799cc: ldr             x1, [x1, #0x390]
    // 0xe799d0: r2 = Null
    //     0xe799d0: mov             x2, NULL
    // 0xe799d4: stur            x0, [fp, #-0x10]
    // 0xe799d8: r0 = AllocateClosure()
    //     0xe799d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe799dc: ldur            x2, [fp, #-0x18]
    // 0xe799e0: mov             x3, x0
    // 0xe799e4: r1 = <dynamic, DoaSubCategory>
    //     0xe799e4: add             x1, PP, #0x19, lsl #12  ; [pp+0x19cb0] TypeArguments: <dynamic, DoaSubCategory>
    //     0xe799e8: ldr             x1, [x1, #0xcb0]
    // 0xe799ec: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe799ec: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe799f0: r0 = LinkedHashMap.fromIterable()
    //     0xe799f0: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xe799f4: ldur            x1, [fp, #-0x10]
    // 0xe799f8: mov             x2, x0
    // 0xe799fc: r0 = putAll()
    //     0xe799fc: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xe79a00: r0 = ReturnAsync()
    //     0xe79a00: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe79a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79a04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79a08: b               #0xe7998c
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe79a0c, size: 0x4c
    // 0xe79a0c: EnterFrame
    //     0xe79a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe79a10: mov             fp, SP
    // 0xe79a14: AllocStack(0x8)
    //     0xe79a14: sub             SP, SP, #8
    // 0xe79a18: CheckStackOverflow
    //     0xe79a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79a1c: cmp             SP, x16
    //     0xe79a20: b.ls            #0xe79a50
    // 0xe79a24: ldr             x16, [fp, #0x10]
    // 0xe79a28: str             x16, [SP]
    // 0xe79a2c: r4 = 0
    //     0xe79a2c: movz            x4, #0
    // 0xe79a30: ldr             x0, [SP]
    // 0xe79a34: r16 = UnlinkedCall_0x5f3c08
    //     0xe79a34: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a398] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xe79a38: add             x16, x16, #0x398
    // 0xe79a3c: ldp             x5, lr, [x16]
    // 0xe79a40: blr             lr
    // 0xe79a44: LeaveFrame
    //     0xe79a44: mov             SP, fp
    //     0xe79a48: ldp             fp, lr, [SP], #0x10
    // 0xe79a4c: ret
    //     0xe79a4c: ret             
    // 0xe79a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79a54: b               #0xe79a24
  }
  _ findAllCategory(/* No info */) async {
    // ** addr: 0xe79a58, size: 0x154
    // 0xe79a58: EnterFrame
    //     0xe79a58: stp             fp, lr, [SP, #-0x10]!
    //     0xe79a5c: mov             fp, SP
    // 0xe79a60: AllocStack(0x40)
    //     0xe79a60: sub             SP, SP, #0x40
    // 0xe79a64: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe79a64: stur            NULL, [fp, #-8]
    //     0xe79a68: stur            x1, [fp, #-0x10]
    //     0xe79a6c: stur            x2, [fp, #-0x18]
    // 0xe79a70: CheckStackOverflow
    //     0xe79a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79a74: cmp             SP, x16
    //     0xe79a78: b.ls            #0xe79b9c
    // 0xe79a7c: r1 = 1
    //     0xe79a7c: movz            x1, #0x1
    // 0xe79a80: r0 = AllocateContext()
    //     0xe79a80: bl              #0xec126c  ; AllocateContextStub
    // 0xe79a84: mov             x1, x0
    // 0xe79a88: ldur            x0, [fp, #-0x18]
    // 0xe79a8c: stur            x1, [fp, #-0x20]
    // 0xe79a90: StoreField: r1->field_f = r0
    //     0xe79a90: stur            w0, [x1, #0xf]
    // 0xe79a94: InitAsync() -> Future<ApiResult<List<DoaCategory>>>
    //     0xe79a94: add             x0, PP, #0x40, lsl #12  ; [pp+0x40680] TypeArguments: <ApiResult<List<DoaCategory>>>
    //     0xe79a98: ldr             x0, [x0, #0x680]
    //     0xe79a9c: bl              #0x661298  ; InitAsyncStub
    // 0xe79aa0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79aa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79aa4: ldr             x0, [x0, #0x2728]
    //     0xe79aa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe79aac: cmp             w0, w16
    //     0xe79ab0: b.ne            #0xe79abc
    //     0xe79ab4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe79ab8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe79abc: r16 = <DoaCategory>
    //     0xe79abc: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79ac0: stp             x0, x16, [SP, #8]
    // 0xe79ac4: r16 = "v2_doa_categories"
    //     0xe79ac4: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "v2_doa_categories"
    // 0xe79ac8: str             x16, [SP]
    // 0xe79acc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79acc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79ad0: r0 = box()
    //     0xe79ad0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe79ad4: mov             x1, x0
    // 0xe79ad8: stur            x0, [fp, #-0x10]
    // 0xe79adc: r0 = checkOpen()
    //     0xe79adc: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe79ae0: ldur            x0, [fp, #-0x10]
    // 0xe79ae4: LoadField: r1 = r0->field_1b
    //     0xe79ae4: ldur            w1, [x0, #0x1b]
    // 0xe79ae8: DecompressPointer r1
    //     0xe79ae8: add             x1, x1, HEAP, lsl #32
    // 0xe79aec: r16 = Sentinel
    //     0xe79aec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe79af0: cmp             w1, w16
    // 0xe79af4: b.eq            #0xe79ba4
    // 0xe79af8: r0 = getValues()
    //     0xe79af8: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe79afc: LoadField: r1 = r0->field_7
    //     0xe79afc: ldur            w1, [x0, #7]
    // 0xe79b00: DecompressPointer r1
    //     0xe79b00: add             x1, x1, HEAP, lsl #32
    // 0xe79b04: mov             x2, x0
    // 0xe79b08: r0 = _GrowableList.of()
    //     0xe79b08: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe79b0c: ldur            x2, [fp, #-0x20]
    // 0xe79b10: r1 = Function '<anonymous closure>':.
    //     0xe79b10: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a3f8] AnonymousClosure: (0xe79bd4), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllCategory (0xe79a58)
    //     0xe79b14: ldr             x1, [x1, #0x3f8]
    // 0xe79b18: stur            x0, [fp, #-0x10]
    // 0xe79b1c: r0 = AllocateClosure()
    //     0xe79b1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79b20: r16 = <DoaCategory>
    //     0xe79b20: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79b24: ldur            lr, [fp, #-0x10]
    // 0xe79b28: stp             lr, x16, [SP, #0x10]
    // 0xe79b2c: r16 = true
    //     0xe79b2c: add             x16, NULL, #0x20  ; true
    // 0xe79b30: stp             x0, x16, [SP]
    // 0xe79b34: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe79b34: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe79b38: r0 = ListExtension.optional()
    //     0xe79b38: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe79b3c: r1 = Function '<anonymous closure>':.
    //     0xe79b3c: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a400] AnonymousClosure: (0xe787a4), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe79b40: ldr             x1, [x1, #0x400]
    // 0xe79b44: r2 = Null
    //     0xe79b44: mov             x2, NULL
    // 0xe79b48: stur            x0, [fp, #-0x10]
    // 0xe79b4c: r0 = AllocateClosure()
    //     0xe79b4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79b50: r16 = <DoaCategory>
    //     0xe79b50: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79b54: ldur            lr, [fp, #-0x10]
    // 0xe79b58: stp             lr, x16, [SP, #8]
    // 0xe79b5c: str             x0, [SP]
    // 0xe79b60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79b60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79b64: r0 = ListExtension.sortBy()
    //     0xe79b64: bl              #0x7ec728  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortBy
    // 0xe79b68: r1 = Function '<anonymous closure>':.
    //     0xe79b68: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a408] AnonymousClosure: (0xe79bac), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllCategory (0xe79a58)
    //     0xe79b6c: ldr             x1, [x1, #0x408]
    // 0xe79b70: r2 = Null
    //     0xe79b70: mov             x2, NULL
    // 0xe79b74: stur            x0, [fp, #-0x10]
    // 0xe79b78: r0 = AllocateClosure()
    //     0xe79b78: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79b7c: r16 = <DoaCategory, FutureOr<ApiResult<List<DoaCategory>>>>
    //     0xe79b7c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a410] TypeArguments: <DoaCategory, FutureOr<ApiResult<List<DoaCategory>>>>
    //     0xe79b80: ldr             x16, [x16, #0x410]
    // 0xe79b84: ldur            lr, [fp, #-0x10]
    // 0xe79b88: stp             lr, x16, [SP, #8]
    // 0xe79b8c: str             x0, [SP]
    // 0xe79b90: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe79b90: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe79b94: r0 = ListExtension.to()
    //     0xe79b94: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe79b98: r0 = ReturnAsync()
    //     0xe79b98: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe79b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79ba0: b               #0xe79a7c
    // 0xe79ba4: r9 = keystore
    //     0xe79ba4: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe79ba8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe79ba8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ApiResult<List<DoaCategory>> <anonymous closure>(dynamic, List<DoaCategory>) {
    // ** addr: 0xe79bac, size: 0x28
    // 0xe79bac: EnterFrame
    //     0xe79bac: stp             fp, lr, [SP, #-0x10]!
    //     0xe79bb0: mov             fp, SP
    // 0xe79bb4: r1 = <List<DoaCategory>>
    //     0xe79bb4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b48] TypeArguments: <List<DoaCategory>>
    //     0xe79bb8: ldr             x1, [x1, #0xb48]
    // 0xe79bbc: r0 = _$SuccessImpl()
    //     0xe79bbc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe79bc0: ldr             x1, [fp, #0x10]
    // 0xe79bc4: StoreField: r0->field_b = r1
    //     0xe79bc4: stur            w1, [x0, #0xb]
    // 0xe79bc8: LeaveFrame
    //     0xe79bc8: mov             SP, fp
    //     0xe79bcc: ldp             fp, lr, [SP], #0x10
    // 0xe79bd0: ret
    //     0xe79bd0: ret             
  }
  [closure] List<DoaCategory> <anonymous closure>(dynamic, List<DoaCategory>) {
    // ** addr: 0xe79bd4, size: 0x5c
    // 0xe79bd4: EnterFrame
    //     0xe79bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe79bd8: mov             fp, SP
    // 0xe79bdc: AllocStack(0x18)
    //     0xe79bdc: sub             SP, SP, #0x18
    // 0xe79be0: SetupParameters()
    //     0xe79be0: ldr             x0, [fp, #0x18]
    //     0xe79be4: ldur            w2, [x0, #0x17]
    //     0xe79be8: add             x2, x2, HEAP, lsl #32
    // 0xe79bec: CheckStackOverflow
    //     0xe79bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79bf0: cmp             SP, x16
    //     0xe79bf4: b.ls            #0xe79c28
    // 0xe79bf8: r1 = Function '<anonymous closure>':.
    //     0xe79bf8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a418] AnonymousClosure: (0xe79c30), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllCategory (0xe79a58)
    //     0xe79bfc: ldr             x1, [x1, #0x418]
    // 0xe79c00: r0 = AllocateClosure()
    //     0xe79c00: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79c04: r16 = <DoaCategory>
    //     0xe79c04: ldr             x16, [PP, #0x7ba0]  ; [pp+0x7ba0] TypeArguments: <DoaCategory>
    // 0xe79c08: ldr             lr, [fp, #0x10]
    // 0xe79c0c: stp             lr, x16, [SP, #8]
    // 0xe79c10: str             x0, [SP]
    // 0xe79c14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79c14: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79c18: r0 = ListExtension.filter()
    //     0xe79c18: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe79c1c: LeaveFrame
    //     0xe79c1c: mov             SP, fp
    //     0xe79c20: ldp             fp, lr, [SP], #0x10
    // 0xe79c24: ret
    //     0xe79c24: ret             
    // 0xe79c28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79c28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79c2c: b               #0xe79bf8
  }
  [closure] bool <anonymous closure>(dynamic, DoaCategory) {
    // ** addr: 0xe79c30, size: 0x68
    // 0xe79c30: EnterFrame
    //     0xe79c30: stp             fp, lr, [SP, #-0x10]!
    //     0xe79c34: mov             fp, SP
    // 0xe79c38: AllocStack(0x10)
    //     0xe79c38: sub             SP, SP, #0x10
    // 0xe79c3c: SetupParameters()
    //     0xe79c3c: ldr             x0, [fp, #0x18]
    //     0xe79c40: ldur            w1, [x0, #0x17]
    //     0xe79c44: add             x1, x1, HEAP, lsl #32
    // 0xe79c48: CheckStackOverflow
    //     0xe79c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79c4c: cmp             SP, x16
    //     0xe79c50: b.ls            #0xe79c90
    // 0xe79c54: ldr             x0, [fp, #0x10]
    // 0xe79c58: LoadField: r2 = r0->field_1b
    //     0xe79c58: ldur            w2, [x0, #0x1b]
    // 0xe79c5c: DecompressPointer r2
    //     0xe79c5c: add             x2, x2, HEAP, lsl #32
    // 0xe79c60: LoadField: r0 = r1->field_f
    //     0xe79c60: ldur            w0, [x1, #0xf]
    // 0xe79c64: DecompressPointer r0
    //     0xe79c64: add             x0, x0, HEAP, lsl #32
    // 0xe79c68: r1 = LoadClassIdInstr(r2)
    //     0xe79c68: ldur            x1, [x2, #-1]
    //     0xe79c6c: ubfx            x1, x1, #0xc, #0x14
    // 0xe79c70: stp             x0, x2, [SP]
    // 0xe79c74: mov             x0, x1
    // 0xe79c78: mov             lr, x0
    // 0xe79c7c: ldr             lr, [x21, lr, lsl #3]
    // 0xe79c80: blr             lr
    // 0xe79c84: LeaveFrame
    //     0xe79c84: mov             SP, fp
    //     0xe79c88: ldp             fp, lr, [SP], #0x10
    // 0xe79c8c: ret
    //     0xe79c8c: ret             
    // 0xe79c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79c90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79c94: b               #0xe79c54
  }
  _ findBookmarkById(/* No info */) async {
    // ** addr: 0xe7a244, size: 0x11c
    // 0xe7a244: EnterFrame
    //     0xe7a244: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a248: mov             fp, SP
    // 0xe7a24c: AllocStack(0x38)
    //     0xe7a24c: sub             SP, SP, #0x38
    // 0xe7a250: SetupParameters(DoaLocalRepository this /* r1 => r3, fp-0x18 */)
    //     0xe7a250: stur            NULL, [fp, #-8]
    //     0xe7a254: mov             x3, x1
    //     0xe7a258: stur            x1, [fp, #-0x18]
    // 0xe7a25c: CheckStackOverflow
    //     0xe7a25c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a260: cmp             SP, x16
    //     0xe7a264: b.ls            #0xe7a350
    // 0xe7a268: r0 = BoxInt64Instr(r2)
    //     0xe7a268: sbfiz           x0, x2, #1, #0x1f
    //     0xe7a26c: cmp             x2, x0, asr #1
    //     0xe7a270: b.eq            #0xe7a27c
    //     0xe7a274: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7a278: stur            x2, [x0, #7]
    // 0xe7a27c: stur            x0, [fp, #-0x10]
    // 0xe7a280: r1 = 1
    //     0xe7a280: movz            x1, #0x1
    // 0xe7a284: r0 = AllocateContext()
    //     0xe7a284: bl              #0xec126c  ; AllocateContextStub
    // 0xe7a288: mov             x1, x0
    // 0xe7a28c: ldur            x0, [fp, #-0x10]
    // 0xe7a290: stur            x1, [fp, #-0x20]
    // 0xe7a294: StoreField: r1->field_f = r0
    //     0xe7a294: stur            w0, [x1, #0xf]
    // 0xe7a298: InitAsync() -> Future<DoaSubCategory?>
    //     0xe7a298: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a3e8] TypeArguments: <DoaSubCategory?>
    //     0xe7a29c: ldr             x0, [x0, #0x3e8]
    //     0xe7a2a0: bl              #0x661298  ; InitAsyncStub
    // 0xe7a2a4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7a2a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7a2a8: ldr             x0, [x0, #0x2728]
    //     0xe7a2ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7a2b0: cmp             w0, w16
    //     0xe7a2b4: b.ne            #0xe7a2c0
    //     0xe7a2b8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7a2bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7a2c0: r16 = <DoaSubCategory>
    //     0xe7a2c0: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a2c4: stp             x0, x16, [SP, #8]
    // 0xe7a2c8: r16 = "v2_doa_bookmark"
    //     0xe7a2c8: ldr             x16, [PP, #0x7c40]  ; [pp+0x7c40] "v2_doa_bookmark"
    // 0xe7a2cc: str             x16, [SP]
    // 0xe7a2d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a2d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a2d4: r0 = box()
    //     0xe7a2d4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7a2d8: mov             x1, x0
    // 0xe7a2dc: stur            x0, [fp, #-0x10]
    // 0xe7a2e0: r0 = checkOpen()
    //     0xe7a2e0: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe7a2e4: ldur            x0, [fp, #-0x10]
    // 0xe7a2e8: LoadField: r1 = r0->field_1b
    //     0xe7a2e8: ldur            w1, [x0, #0x1b]
    // 0xe7a2ec: DecompressPointer r1
    //     0xe7a2ec: add             x1, x1, HEAP, lsl #32
    // 0xe7a2f0: r16 = Sentinel
    //     0xe7a2f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7a2f4: cmp             w1, w16
    // 0xe7a2f8: b.eq            #0xe7a358
    // 0xe7a2fc: r0 = getValues()
    //     0xe7a2fc: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe7a300: LoadField: r1 = r0->field_7
    //     0xe7a300: ldur            w1, [x0, #7]
    // 0xe7a304: DecompressPointer r1
    //     0xe7a304: add             x1, x1, HEAP, lsl #32
    // 0xe7a308: mov             x2, x0
    // 0xe7a30c: r0 = _GrowableList.of()
    //     0xe7a30c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe7a310: ldur            x2, [fp, #-0x20]
    // 0xe7a314: r1 = Function '<anonymous closure>':.
    //     0xe7a314: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a3f0] AnonymousClosure: (0x7bcaf0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByVerseId (0x7bcb2c)
    //     0xe7a318: ldr             x1, [x1, #0x3f0]
    // 0xe7a31c: stur            x0, [fp, #-0x10]
    // 0xe7a320: r0 = AllocateClosure()
    //     0xe7a320: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a324: r16 = <DoaSubCategory>
    //     0xe7a324: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a328: ldur            lr, [fp, #-0x10]
    // 0xe7a32c: stp             lr, x16, [SP, #8]
    // 0xe7a330: str             x0, [SP]
    // 0xe7a334: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a334: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a338: r0 = ListExtension.filter()
    //     0xe7a338: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe7a33c: r16 = <DoaSubCategory>
    //     0xe7a33c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a340: stp             x0, x16, [SP]
    // 0xe7a344: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe7a344: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe7a348: r0 = ListExtension.firstOrNull()
    //     0xe7a348: bl              #0xe77fd0  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.firstOrNull
    // 0xe7a34c: r0 = ReturnAsync()
    //     0xe7a34c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7a350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a354: b               #0xe7a268
    // 0xe7a358: r9 = keystore
    //     0xe7a358: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7a35c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7a35c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ findAllSubCategory(/* No info */) async {
    // ** addr: 0xe7a360, size: 0x290
    // 0xe7a360: EnterFrame
    //     0xe7a360: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a364: mov             fp, SP
    // 0xe7a368: AllocStack(0x50)
    //     0xe7a368: sub             SP, SP, #0x50
    // 0xe7a36c: SetupParameters(DoaLocalRepository this /* r1 => r1, fp-0x20 */, {dynamic categoryId = Null /* r2, fp-0x18 */, dynamic query = "" /* r0, fp-0x10 */})
    //     0xe7a36c: stur            NULL, [fp, #-8]
    //     0xe7a370: stur            x1, [fp, #-0x20]
    //     0xe7a374: ldur            w0, [x4, #0x13]
    //     0xe7a378: ldur            w2, [x4, #0x1f]
    //     0xe7a37c: add             x2, x2, HEAP, lsl #32
    //     0xe7a380: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c48] "categoryId"
    //     0xe7a384: ldr             x16, [x16, #0xc48]
    //     0xe7a388: cmp             w2, w16
    //     0xe7a38c: b.ne            #0xe7a3ac
    //     0xe7a390: ldur            w2, [x4, #0x23]
    //     0xe7a394: add             x2, x2, HEAP, lsl #32
    //     0xe7a398: sub             w3, w0, w2
    //     0xe7a39c: add             x2, fp, w3, sxtw #2
    //     0xe7a3a0: ldr             x2, [x2, #8]
    //     0xe7a3a4: movz            x3, #0x1
    //     0xe7a3a8: b               #0xe7a3b4
    //     0xe7a3ac: movz            x3, #0
    //     0xe7a3b0: mov             x2, NULL
    //     0xe7a3b4: stur            x2, [fp, #-0x18]
    //     0xe7a3b8: lsl             x5, x3, #1
    //     0xe7a3bc: lsl             w3, w5, #1
    //     0xe7a3c0: add             w5, w3, #8
    //     0xe7a3c4: add             x16, x4, w5, sxtw #1
    //     0xe7a3c8: ldur            w6, [x16, #0xf]
    //     0xe7a3cc: add             x6, x6, HEAP, lsl #32
    //     0xe7a3d0: ldr             x16, [PP, #0x3650]  ; [pp+0x3650] "query"
    //     0xe7a3d4: cmp             w6, w16
    //     0xe7a3d8: b.ne            #0xe7a3fc
    //     0xe7a3dc: add             w5, w3, #0xa
    //     0xe7a3e0: add             x16, x4, w5, sxtw #1
    //     0xe7a3e4: ldur            w3, [x16, #0xf]
    //     0xe7a3e8: add             x3, x3, HEAP, lsl #32
    //     0xe7a3ec: sub             w4, w0, w3
    //     0xe7a3f0: add             x0, fp, w4, sxtw #2
    //     0xe7a3f4: ldr             x0, [x0, #8]
    //     0xe7a3f8: b               #0xe7a400
    //     0xe7a3fc: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    //     0xe7a400: stur            x0, [fp, #-0x10]
    // 0xe7a404: CheckStackOverflow
    //     0xe7a404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a408: cmp             SP, x16
    //     0xe7a40c: b.ls            #0xe7a5e0
    // 0xe7a410: r1 = 2
    //     0xe7a410: movz            x1, #0x2
    // 0xe7a414: r0 = AllocateContext()
    //     0xe7a414: bl              #0xec126c  ; AllocateContextStub
    // 0xe7a418: mov             x1, x0
    // 0xe7a41c: ldur            x0, [fp, #-0x18]
    // 0xe7a420: stur            x1, [fp, #-0x28]
    // 0xe7a424: StoreField: r1->field_f = r0
    //     0xe7a424: stur            w0, [x1, #0xf]
    // 0xe7a428: ldur            x0, [fp, #-0x10]
    // 0xe7a42c: StoreField: r1->field_13 = r0
    //     0xe7a42c: stur            w0, [x1, #0x13]
    // 0xe7a430: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xe7a430: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xe7a434: ldr             x0, [x0, #0xd38]
    //     0xe7a438: bl              #0x661298  ; InitAsyncStub
    // 0xe7a43c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7a43c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7a440: ldr             x0, [x0, #0x2728]
    //     0xe7a444: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7a448: cmp             w0, w16
    //     0xe7a44c: b.ne            #0xe7a458
    //     0xe7a450: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7a454: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7a458: r16 = <DoaSubCategory>
    //     0xe7a458: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a45c: stp             x0, x16, [SP, #8]
    // 0xe7a460: r16 = "v2_doa_sub_categories"
    //     0xe7a460: ldr             x16, [PP, #0x7c30]  ; [pp+0x7c30] "v2_doa_sub_categories"
    // 0xe7a464: str             x16, [SP]
    // 0xe7a468: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a468: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a46c: r0 = box()
    //     0xe7a46c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7a470: mov             x1, x0
    // 0xe7a474: stur            x0, [fp, #-0x10]
    // 0xe7a478: r0 = checkOpen()
    //     0xe7a478: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe7a47c: ldur            x0, [fp, #-0x10]
    // 0xe7a480: LoadField: r1 = r0->field_1b
    //     0xe7a480: ldur            w1, [x0, #0x1b]
    // 0xe7a484: DecompressPointer r1
    //     0xe7a484: add             x1, x1, HEAP, lsl #32
    // 0xe7a488: r16 = Sentinel
    //     0xe7a488: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7a48c: cmp             w1, w16
    // 0xe7a490: b.eq            #0xe7a5e8
    // 0xe7a494: r0 = getValues()
    //     0xe7a494: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe7a498: LoadField: r1 = r0->field_7
    //     0xe7a498: ldur            w1, [x0, #7]
    // 0xe7a49c: DecompressPointer r1
    //     0xe7a49c: add             x1, x1, HEAP, lsl #32
    // 0xe7a4a0: mov             x2, x0
    // 0xe7a4a4: r0 = _GrowableList.of()
    //     0xe7a4a4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe7a4a8: mov             x3, x0
    // 0xe7a4ac: ldur            x0, [fp, #-0x28]
    // 0xe7a4b0: stur            x3, [fp, #-0x18]
    // 0xe7a4b4: LoadField: r1 = r0->field_f
    //     0xe7a4b4: ldur            w1, [x0, #0xf]
    // 0xe7a4b8: DecompressPointer r1
    //     0xe7a4b8: add             x1, x1, HEAP, lsl #32
    // 0xe7a4bc: cmp             w1, NULL
    // 0xe7a4c0: r16 = true
    //     0xe7a4c0: add             x16, NULL, #0x20  ; true
    // 0xe7a4c4: r17 = false
    //     0xe7a4c4: add             x17, NULL, #0x30  ; false
    // 0xe7a4c8: csel            x4, x16, x17, ne
    // 0xe7a4cc: mov             x2, x0
    // 0xe7a4d0: stur            x4, [fp, #-0x10]
    // 0xe7a4d4: r1 = Function '<anonymous closure>':.
    //     0xe7a4d4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e30] AnonymousClosure: (0xe7a728), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a4d8: ldr             x1, [x1, #0xe30]
    // 0xe7a4dc: r0 = AllocateClosure()
    //     0xe7a4dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a4e0: r16 = <DoaSubCategory>
    //     0xe7a4e0: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a4e4: ldur            lr, [fp, #-0x18]
    // 0xe7a4e8: stp             lr, x16, [SP, #0x10]
    // 0xe7a4ec: ldur            x16, [fp, #-0x10]
    // 0xe7a4f0: stp             x0, x16, [SP]
    // 0xe7a4f4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe7a4f4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe7a4f8: r0 = ListExtension.optional()
    //     0xe7a4f8: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe7a4fc: mov             x3, x0
    // 0xe7a500: ldur            x0, [fp, #-0x28]
    // 0xe7a504: stur            x3, [fp, #-0x18]
    // 0xe7a508: LoadField: r1 = r0->field_13
    //     0xe7a508: ldur            w1, [x0, #0x13]
    // 0xe7a50c: DecompressPointer r1
    //     0xe7a50c: add             x1, x1, HEAP, lsl #32
    // 0xe7a510: LoadField: r2 = r1->field_7
    //     0xe7a510: ldur            w2, [x1, #7]
    // 0xe7a514: cbnz            w2, #0xe7a520
    // 0xe7a518: r4 = false
    //     0xe7a518: add             x4, NULL, #0x30  ; false
    // 0xe7a51c: b               #0xe7a524
    // 0xe7a520: r4 = true
    //     0xe7a520: add             x4, NULL, #0x20  ; true
    // 0xe7a524: mov             x2, x0
    // 0xe7a528: stur            x4, [fp, #-0x10]
    // 0xe7a52c: r1 = Function '<anonymous closure>':.
    //     0xe7a52c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e38] AnonymousClosure: (0xe7a5f0), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a530: ldr             x1, [x1, #0xe38]
    // 0xe7a534: r0 = AllocateClosure()
    //     0xe7a534: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a538: r1 = Function '<anonymous closure>':.
    //     0xe7a538: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e40] AnonymousClosure: (0xe787f8), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a53c: ldr             x1, [x1, #0xe40]
    // 0xe7a540: r2 = Null
    //     0xe7a540: mov             x2, NULL
    // 0xe7a544: stur            x0, [fp, #-0x20]
    // 0xe7a548: r0 = AllocateClosure()
    //     0xe7a548: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a54c: r16 = <DoaSubCategory>
    //     0xe7a54c: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a550: ldur            lr, [fp, #-0x18]
    // 0xe7a554: stp             lr, x16, [SP, #0x18]
    // 0xe7a558: ldur            x16, [fp, #-0x10]
    // 0xe7a55c: ldur            lr, [fp, #-0x20]
    // 0xe7a560: stp             lr, x16, [SP, #8]
    // 0xe7a564: str             x0, [SP]
    // 0xe7a568: r4 = const [0x1, 0x4, 0x4, 0x3, orElse, 0x3, null]
    //     0xe7a568: add             x4, PP, #0x37, lsl #12  ; [pp+0x37e48] List(7) [0x1, 0x4, 0x4, 0x3, "orElse", 0x3, Null]
    //     0xe7a56c: ldr             x4, [x4, #0xe48]
    // 0xe7a570: r0 = ListExtension.optional()
    //     0xe7a570: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe7a574: mov             x3, x0
    // 0xe7a578: ldur            x0, [fp, #-0x28]
    // 0xe7a57c: stur            x3, [fp, #-0x18]
    // 0xe7a580: LoadField: r1 = r0->field_13
    //     0xe7a580: ldur            w1, [x0, #0x13]
    // 0xe7a584: DecompressPointer r1
    //     0xe7a584: add             x1, x1, HEAP, lsl #32
    // 0xe7a588: LoadField: r0 = r1->field_7
    //     0xe7a588: ldur            w0, [x1, #7]
    // 0xe7a58c: cbz             w0, #0xe7a59c
    // 0xe7a590: r0 = Instance__$FailureImpl
    //     0xe7a590: add             x0, PP, #0x37, lsl #12  ; [pp+0x37e50] Obj!_$FailureImpl<List<DoaSubCategory>>@e0e571
    //     0xe7a594: ldr             x0, [x0, #0xe50]
    // 0xe7a598: b               #0xe7a5a4
    // 0xe7a59c: r0 = Instance__$SuccessImpl
    //     0xe7a59c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37e58] Obj!_$SuccessImpl<List<DoaSubCategory>>@e0e5e1
    //     0xe7a5a0: ldr             x0, [x0, #0xe58]
    // 0xe7a5a4: stur            x0, [fp, #-0x10]
    // 0xe7a5a8: r1 = Function '<anonymous closure>':.
    //     0xe7a5a8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e60] AnonymousClosure: (0xe77358), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a5ac: ldr             x1, [x1, #0xe60]
    // 0xe7a5b0: r2 = Null
    //     0xe7a5b0: mov             x2, NULL
    // 0xe7a5b4: r0 = AllocateClosure()
    //     0xe7a5b4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a5b8: r16 = <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe7a5b8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37e68] TypeArguments: <DoaSubCategory, FutureOr<ApiResult<List<DoaSubCategory>>>>
    //     0xe7a5bc: ldr             x16, [x16, #0xe68]
    // 0xe7a5c0: ldur            lr, [fp, #-0x18]
    // 0xe7a5c4: stp             lr, x16, [SP, #0x10]
    // 0xe7a5c8: ldur            x16, [fp, #-0x10]
    // 0xe7a5cc: stp             x16, x0, [SP]
    // 0xe7a5d0: r4 = const [0x2, 0x3, 0x3, 0x2, orEmpty, 0x2, null]
    //     0xe7a5d0: add             x4, PP, #0x37, lsl #12  ; [pp+0x37e70] List(7) [0x2, 0x3, 0x3, 0x2, "orEmpty", 0x2, Null]
    //     0xe7a5d4: ldr             x4, [x4, #0xe70]
    // 0xe7a5d8: r0 = ListExtension.to()
    //     0xe7a5d8: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe7a5dc: r0 = ReturnAsync()
    //     0xe7a5dc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7a5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a5e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a5e4: b               #0xe7a410
    // 0xe7a5e8: r9 = keystore
    //     0xe7a5e8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7a5ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7a5ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] List<DoaSubCategory> <anonymous closure>(dynamic, List<DoaSubCategory>) {
    // ** addr: 0xe7a5f0, size: 0x70
    // 0xe7a5f0: EnterFrame
    //     0xe7a5f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a5f4: mov             fp, SP
    // 0xe7a5f8: AllocStack(0x28)
    //     0xe7a5f8: sub             SP, SP, #0x28
    // 0xe7a5fc: SetupParameters()
    //     0xe7a5fc: ldr             x0, [fp, #0x18]
    //     0xe7a600: ldur            w1, [x0, #0x17]
    //     0xe7a604: add             x1, x1, HEAP, lsl #32
    // 0xe7a608: CheckStackOverflow
    //     0xe7a608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a60c: cmp             SP, x16
    //     0xe7a610: b.ls            #0xe7a658
    // 0xe7a614: LoadField: r0 = r1->field_13
    //     0xe7a614: ldur            w0, [x1, #0x13]
    // 0xe7a618: DecompressPointer r0
    //     0xe7a618: add             x0, x0, HEAP, lsl #32
    // 0xe7a61c: stur            x0, [fp, #-8]
    // 0xe7a620: r1 = Function '<anonymous closure>':.
    //     0xe7a620: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e88] AnonymousClosure: (0xe7a660), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a624: ldr             x1, [x1, #0xe88]
    // 0xe7a628: r2 = Null
    //     0xe7a628: mov             x2, NULL
    // 0xe7a62c: r0 = AllocateClosure()
    //     0xe7a62c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a630: r16 = <DoaSubCategory>
    //     0xe7a630: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a634: ldr             lr, [fp, #0x10]
    // 0xe7a638: stp             lr, x16, [SP, #0x10]
    // 0xe7a63c: ldur            x16, [fp, #-8]
    // 0xe7a640: stp             x0, x16, [SP]
    // 0xe7a644: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe7a644: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe7a648: r0 = ListExtension.search()
    //     0xe7a648: bl              #0xb3f3c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.search
    // 0xe7a64c: LeaveFrame
    //     0xe7a64c: mov             SP, fp
    //     0xe7a650: ldp             fp, lr, [SP], #0x10
    // 0xe7a654: ret
    //     0xe7a654: ret             
    // 0xe7a658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a65c: b               #0xe7a614
  }
  [closure] String <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xe7a660, size: 0x30
    // 0xe7a660: EnterFrame
    //     0xe7a660: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a664: mov             fp, SP
    // 0xe7a668: CheckStackOverflow
    //     0xe7a668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a66c: cmp             SP, x16
    //     0xe7a670: b.ls            #0xe7a688
    // 0xe7a674: ldr             x1, [fp, #0x10]
    // 0xe7a678: r0 = searchable()
    //     0xe7a678: bl              #0xe7a690  ; [package:nuonline/app/data/models/doa.dart] DoaSubCategory::searchable
    // 0xe7a67c: LeaveFrame
    //     0xe7a67c: mov             SP, fp
    //     0xe7a680: ldp             fp, lr, [SP], #0x10
    // 0xe7a684: ret
    //     0xe7a684: ret             
    // 0xe7a688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a68c: b               #0xe7a674
  }
  [closure] List<DoaSubCategory> <anonymous closure>(dynamic, List<DoaSubCategory>) {
    // ** addr: 0xe7a728, size: 0x5c
    // 0xe7a728: EnterFrame
    //     0xe7a728: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a72c: mov             fp, SP
    // 0xe7a730: AllocStack(0x18)
    //     0xe7a730: sub             SP, SP, #0x18
    // 0xe7a734: SetupParameters()
    //     0xe7a734: ldr             x0, [fp, #0x18]
    //     0xe7a738: ldur            w2, [x0, #0x17]
    //     0xe7a73c: add             x2, x2, HEAP, lsl #32
    // 0xe7a740: CheckStackOverflow
    //     0xe7a740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a744: cmp             SP, x16
    //     0xe7a748: b.ls            #0xe7a77c
    // 0xe7a74c: r1 = Function '<anonymous closure>':.
    //     0xe7a74c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37e90] AnonymousClosure: (0xe7a784), in [package:nuonline/app/data/repositories/doa/doa_local_repository.dart] DoaLocalRepository::findAllSubCategory (0xe7a360)
    //     0xe7a750: ldr             x1, [x1, #0xe90]
    // 0xe7a754: r0 = AllocateClosure()
    //     0xe7a754: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a758: r16 = <DoaSubCategory>
    //     0xe7a758: ldr             x16, [PP, #0x7ba8]  ; [pp+0x7ba8] TypeArguments: <DoaSubCategory>
    // 0xe7a75c: ldr             lr, [fp, #0x10]
    // 0xe7a760: stp             lr, x16, [SP, #8]
    // 0xe7a764: str             x0, [SP]
    // 0xe7a768: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a768: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a76c: r0 = ListExtension.filter()
    //     0xe7a76c: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe7a770: LeaveFrame
    //     0xe7a770: mov             SP, fp
    //     0xe7a774: ldp             fp, lr, [SP], #0x10
    // 0xe7a778: ret
    //     0xe7a778: ret             
    // 0xe7a77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a77c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a780: b               #0xe7a74c
  }
  [closure] bool <anonymous closure>(dynamic, DoaSubCategory) {
    // ** addr: 0xe7a784, size: 0x50
    // 0xe7a784: ldr             x1, [SP, #8]
    // 0xe7a788: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe7a788: ldur            w2, [x1, #0x17]
    // 0xe7a78c: DecompressPointer r2
    //     0xe7a78c: add             x2, x2, HEAP, lsl #32
    // 0xe7a790: ldr             x1, [SP]
    // 0xe7a794: LoadField: r3 = r1->field_13
    //     0xe7a794: ldur            x3, [x1, #0x13]
    // 0xe7a798: LoadField: r1 = r2->field_f
    //     0xe7a798: ldur            w1, [x2, #0xf]
    // 0xe7a79c: DecompressPointer r1
    //     0xe7a79c: add             x1, x1, HEAP, lsl #32
    // 0xe7a7a0: cmp             w1, NULL
    // 0xe7a7a4: b.eq            #0xe7a7c8
    // 0xe7a7a8: r2 = LoadInt32Instr(r1)
    //     0xe7a7a8: sbfx            x2, x1, #1, #0x1f
    //     0xe7a7ac: tbz             w1, #0, #0xe7a7b4
    //     0xe7a7b0: ldur            x2, [x1, #7]
    // 0xe7a7b4: cmp             x3, x2
    // 0xe7a7b8: r16 = true
    //     0xe7a7b8: add             x16, NULL, #0x20  ; true
    // 0xe7a7bc: r17 = false
    //     0xe7a7bc: add             x17, NULL, #0x30  ; false
    // 0xe7a7c0: csel            x0, x16, x17, eq
    // 0xe7a7c4: ret
    //     0xe7a7c4: ret             
    // 0xe7a7c8: EnterFrame
    //     0xe7a7c8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a7cc: mov             fp, SP
    // 0xe7a7d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7a7d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
