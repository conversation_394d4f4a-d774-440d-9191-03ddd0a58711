// lib: , url: package:nuonline/app/data/repositories/doa/doa_remote_repository.dart

// class id: 1050074, size: 0x8
class :: {
}

// class id: 1101, size: 0x10, field offset: 0x8
class DoaRemoteRepository extends Object
    implements DoaRepository {

  static _ find(/* No info */) {
    // ** addr: 0x80f97c, size: 0x64
    // 0x80f97c: EnterFrame
    //     0x80f97c: stp             fp, lr, [SP, #-0x10]!
    //     0x80f980: mov             fp, SP
    // 0x80f984: AllocStack(0x10)
    //     0x80f984: sub             SP, SP, #0x10
    // 0x80f988: CheckStackOverflow
    //     0x80f988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80f98c: cmp             SP, x16
    //     0x80f990: b.ls            #0x80f9d8
    // 0x80f994: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80f994: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80f998: ldr             x0, [x0, #0x2670]
    //     0x80f99c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80f9a0: cmp             w0, w16
    //     0x80f9a4: b.ne            #0x80f9b0
    //     0x80f9a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80f9ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80f9b0: r16 = <DoaRepository>
    //     0x80f9b0: add             x16, PP, #0x10, lsl #12  ; [pp+0x100f8] TypeArguments: <DoaRepository>
    //     0x80f9b4: ldr             x16, [x16, #0xf8]
    // 0x80f9b8: r30 = "doa_remote_repo"
    //     0x80f9b8: add             lr, PP, #0x10, lsl #12  ; [pp+0x10110] "doa_remote_repo"
    //     0x80f9bc: ldr             lr, [lr, #0x110]
    // 0x80f9c0: stp             lr, x16, [SP]
    // 0x80f9c4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80f9c4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80f9c8: r0 = Inst.find()
    //     0x80f9c8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80f9cc: LeaveFrame
    //     0x80f9cc: mov             SP, fp
    //     0x80f9d0: ldp             fp, lr, [SP], #0x10
    // 0x80f9d4: ret
    //     0x80f9d4: ret             
    // 0x80f9d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80f9d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80f9dc: b               #0x80f994
  }
  _ findAllByLastUpdated(/* No info */) async {
    // ** addr: 0xe77380, size: 0x124
    // 0xe77380: EnterFrame
    //     0xe77380: stp             fp, lr, [SP, #-0x10]!
    //     0xe77384: mov             fp, SP
    // 0xe77388: AllocStack(0x88)
    //     0xe77388: sub             SP, SP, #0x88
    // 0xe7738c: SetupParameters(DoaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe7738c: stur            NULL, [fp, #-8]
    //     0xe77390: stur            x1, [fp, #-0x58]
    //     0xe77394: stur            x2, [fp, #-0x60]
    // 0xe77398: CheckStackOverflow
    //     0xe77398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7739c: cmp             SP, x16
    //     0xe773a0: b.ls            #0xe7749c
    // 0xe773a4: InitAsync() -> Future<ApiResult<List<Doa>>>
    //     0xe773a4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35dd0] TypeArguments: <ApiResult<List<Doa>>>
    //     0xe773a8: ldr             x0, [x0, #0xdd0]
    //     0xe773ac: bl              #0x661298  ; InitAsyncStub
    // 0xe773b0: ldur            x0, [fp, #-0x58]
    // 0xe773b4: LoadField: r1 = r0->field_b
    //     0xe773b4: ldur            w1, [x0, #0xb]
    // 0xe773b8: DecompressPointer r1
    //     0xe773b8: add             x1, x1, HEAP, lsl #32
    // 0xe773bc: r0 = homeUpdateConfig()
    //     0xe773bc: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0xe773c0: LoadField: r1 = r0->field_b
    //     0xe773c0: ldur            w1, [x0, #0xb]
    // 0xe773c4: DecompressPointer r1
    //     0xe773c4: add             x1, x1, HEAP, lsl #32
    // 0xe773c8: tbz             w1, #4, #0xe773d8
    // 0xe773cc: r0 = Instance__$FailureImpl
    //     0xe773cc: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a328] Obj!_$FailureImpl<List<Doa>>@e0e541
    //     0xe773d0: ldr             x0, [x0, #0x328]
    // 0xe773d4: r0 = ReturnAsyncNotFuture()
    //     0xe773d4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe773d8: ldur            x0, [fp, #-0x58]
    // 0xe773dc: ldur            x3, [fp, #-0x60]
    // 0xe773e0: LoadField: r4 = r0->field_7
    //     0xe773e0: ldur            w4, [x0, #7]
    // 0xe773e4: DecompressPointer r4
    //     0xe773e4: add             x4, x4, HEAP, lsl #32
    // 0xe773e8: stur            x4, [fp, #-0x68]
    // 0xe773ec: r1 = Null
    //     0xe773ec: mov             x1, NULL
    // 0xe773f0: r2 = 4
    //     0xe773f0: movz            x2, #0x4
    // 0xe773f4: r0 = AllocateArray()
    //     0xe773f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe773f8: r16 = "last_updated_at"
    //     0xe773f8: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1e8] "last_updated_at"
    //     0xe773fc: ldr             x16, [x16, #0x1e8]
    // 0xe77400: StoreField: r0->field_f = r16
    //     0xe77400: stur            w16, [x0, #0xf]
    // 0xe77404: ldur            x1, [fp, #-0x60]
    // 0xe77408: StoreField: r0->field_13 = r1
    //     0xe77408: stur            w1, [x0, #0x13]
    // 0xe7740c: r16 = <String, dynamic>
    //     0xe7740c: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe77410: stp             x0, x16, [SP]
    // 0xe77414: r0 = Map._fromLiteral()
    //     0xe77414: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe77418: ldur            x16, [fp, #-0x68]
    // 0xe7741c: stp             x16, NULL, [SP, #0x10]
    // 0xe77420: r16 = "/doa/latest"
    //     0xe77420: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a330] "/doa/latest"
    //     0xe77424: ldr             x16, [x16, #0x330]
    // 0xe77428: stp             x0, x16, [SP]
    // 0xe7742c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe7742c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe77430: ldr             x4, [x4, #0x2f0]
    // 0xe77434: r0 = get()
    //     0xe77434: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe77438: mov             x1, x0
    // 0xe7743c: stur            x1, [fp, #-0x58]
    // 0xe77440: r0 = Await()
    //     0xe77440: bl              #0x661044  ; AwaitStub
    // 0xe77444: LoadField: r1 = r0->field_b
    //     0xe77444: ldur            w1, [x0, #0xb]
    // 0xe77448: DecompressPointer r1
    //     0xe77448: add             x1, x1, HEAP, lsl #32
    // 0xe7744c: r0 = fromResponse()
    //     0xe7744c: bl              #0xe774a4  ; [package:nuonline/app/data/models/doa.dart] Doa::fromResponse
    // 0xe77450: r1 = <List<Doa>>
    //     0xe77450: add             x1, PP, #0x35, lsl #12  ; [pp+0x35da0] TypeArguments: <List<Doa>>
    //     0xe77454: ldr             x1, [x1, #0xda0]
    // 0xe77458: stur            x0, [fp, #-0x58]
    // 0xe7745c: r0 = _$SuccessImpl()
    //     0xe7745c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe77460: mov             x1, x0
    // 0xe77464: ldur            x0, [fp, #-0x58]
    // 0xe77468: StoreField: r1->field_b = r0
    //     0xe77468: stur            w0, [x1, #0xb]
    // 0xe7746c: mov             x0, x1
    // 0xe77470: r0 = ReturnAsyncNotFuture()
    //     0xe77470: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe77474: sub             SP, fp, #0x88
    // 0xe77478: mov             x1, x0
    // 0xe7747c: r0 = getDioException()
    //     0xe7747c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe77480: r1 = <List<Doa>>
    //     0xe77480: add             x1, PP, #0x35, lsl #12  ; [pp+0x35da0] TypeArguments: <List<Doa>>
    //     0xe77484: ldr             x1, [x1, #0xda0]
    // 0xe77488: stur            x0, [fp, #-0x58]
    // 0xe7748c: r0 = _$FailureImpl()
    //     0xe7748c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe77490: ldur            x1, [fp, #-0x58]
    // 0xe77494: StoreField: r0->field_b = r1
    //     0xe77494: stur            w1, [x0, #0xb]
    // 0xe77498: r0 = ReturnAsyncNotFuture()
    //     0xe77498: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7749c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7749c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe774a0: b               #0xe773a4
  }
  _ findAllRelated(/* No info */) async {
    // ** addr: 0xe778d0, size: 0x12c
    // 0xe778d0: EnterFrame
    //     0xe778d0: stp             fp, lr, [SP, #-0x10]!
    //     0xe778d4: mov             fp, SP
    // 0xe778d8: AllocStack(0x80)
    //     0xe778d8: sub             SP, SP, #0x80
    // 0xe778dc: SetupParameters(DoaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe778dc: stur            NULL, [fp, #-8]
    //     0xe778e0: stur            x1, [fp, #-0x58]
    //     0xe778e4: stur            x2, [fp, #-0x60]
    // 0xe778e8: CheckStackOverflow
    //     0xe778e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe778ec: cmp             SP, x16
    //     0xe778f0: b.ls            #0xe779f4
    // 0xe778f4: InitAsync() -> Future<ApiResult<List<DoaRelated>>>
    //     0xe778f4: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a228] TypeArguments: <ApiResult<List<DoaRelated>>>
    //     0xe778f8: ldr             x0, [x0, #0x228]
    //     0xe778fc: bl              #0x661298  ; InitAsyncStub
    // 0xe77900: ldur            x0, [fp, #-0x58]
    // 0xe77904: LoadField: r1 = r0->field_b
    //     0xe77904: ldur            w1, [x0, #0xb]
    // 0xe77908: DecompressPointer r1
    //     0xe77908: add             x1, x1, HEAP, lsl #32
    // 0xe7790c: r0 = homeUpdateConfig()
    //     0xe7790c: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0xe77910: LoadField: r1 = r0->field_b
    //     0xe77910: ldur            w1, [x0, #0xb]
    // 0xe77914: DecompressPointer r1
    //     0xe77914: add             x1, x1, HEAP, lsl #32
    // 0xe77918: tbz             w1, #4, #0xe77928
    // 0xe7791c: r0 = Instance__$FailureImpl
    //     0xe7791c: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a230] Obj!_$FailureImpl<List<DoaRelated>>@e0e551
    //     0xe77920: ldr             x0, [x0, #0x230]
    // 0xe77924: r0 = ReturnAsyncNotFuture()
    //     0xe77924: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe77928: ldur            x0, [fp, #-0x58]
    // 0xe7792c: ldur            x3, [fp, #-0x60]
    // 0xe77930: LoadField: r4 = r0->field_7
    //     0xe77930: ldur            w4, [x0, #7]
    // 0xe77934: DecompressPointer r4
    //     0xe77934: add             x4, x4, HEAP, lsl #32
    // 0xe77938: stur            x4, [fp, #-0x68]
    // 0xe7793c: r1 = Null
    //     0xe7793c: mov             x1, NULL
    // 0xe77940: r2 = 4
    //     0xe77940: movz            x2, #0x4
    // 0xe77944: r0 = AllocateArray()
    //     0xe77944: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe77948: mov             x2, x0
    // 0xe7794c: r16 = "/doa/related/"
    //     0xe7794c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a238] "/doa/related/"
    //     0xe77950: ldr             x16, [x16, #0x238]
    // 0xe77954: StoreField: r2->field_f = r16
    //     0xe77954: stur            w16, [x2, #0xf]
    // 0xe77958: ldur            x3, [fp, #-0x60]
    // 0xe7795c: r0 = BoxInt64Instr(r3)
    //     0xe7795c: sbfiz           x0, x3, #1, #0x1f
    //     0xe77960: cmp             x3, x0, asr #1
    //     0xe77964: b.eq            #0xe77970
    //     0xe77968: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7796c: stur            x3, [x0, #7]
    // 0xe77970: StoreField: r2->field_13 = r0
    //     0xe77970: stur            w0, [x2, #0x13]
    // 0xe77974: str             x2, [SP]
    // 0xe77978: r0 = _interpolate()
    //     0xe77978: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7797c: ldur            x16, [fp, #-0x68]
    // 0xe77980: stp             x16, NULL, [SP, #8]
    // 0xe77984: str             x0, [SP]
    // 0xe77988: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe77988: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7798c: r0 = get()
    //     0xe7798c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe77990: mov             x1, x0
    // 0xe77994: stur            x1, [fp, #-0x58]
    // 0xe77998: r0 = Await()
    //     0xe77998: bl              #0x661044  ; AwaitStub
    // 0xe7799c: LoadField: r1 = r0->field_b
    //     0xe7799c: ldur            w1, [x0, #0xb]
    // 0xe779a0: DecompressPointer r1
    //     0xe779a0: add             x1, x1, HEAP, lsl #32
    // 0xe779a4: r0 = fromResponse()
    //     0xe779a4: bl              #0xe779fc  ; [package:nuonline/app/data/models/doa.dart] DoaRelated::fromResponse
    // 0xe779a8: r1 = <List<DoaRelated>>
    //     0xe779a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40578] TypeArguments: <List<DoaRelated>>
    //     0xe779ac: ldr             x1, [x1, #0x578]
    // 0xe779b0: stur            x0, [fp, #-0x58]
    // 0xe779b4: r0 = _$SuccessImpl()
    //     0xe779b4: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe779b8: mov             x1, x0
    // 0xe779bc: ldur            x0, [fp, #-0x58]
    // 0xe779c0: StoreField: r1->field_b = r0
    //     0xe779c0: stur            w0, [x1, #0xb]
    // 0xe779c4: mov             x0, x1
    // 0xe779c8: r0 = ReturnAsyncNotFuture()
    //     0xe779c8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe779cc: sub             SP, fp, #0x80
    // 0xe779d0: mov             x1, x0
    // 0xe779d4: r0 = getDioException()
    //     0xe779d4: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe779d8: r1 = <List<DoaRelated>>
    //     0xe779d8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40578] TypeArguments: <List<DoaRelated>>
    //     0xe779dc: ldr             x1, [x1, #0x578]
    // 0xe779e0: stur            x0, [fp, #-0x58]
    // 0xe779e4: r0 = _$FailureImpl()
    //     0xe779e4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe779e8: ldur            x1, [fp, #-0x58]
    // 0xe779ec: StoreField: r0->field_b = r1
    //     0xe779ec: stur            w1, [x0, #0xb]
    // 0xe779f0: r0 = ReturnAsyncNotFuture()
    //     0xe779f0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe779f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe779f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe779f8: b               #0xe778f4
  }
  _ findAllCategory(/* No info */) async {
    // ** addr: 0xe79c98, size: 0xe8
    // 0xe79c98: EnterFrame
    //     0xe79c98: stp             fp, lr, [SP, #-0x10]!
    //     0xe79c9c: mov             fp, SP
    // 0xe79ca0: AllocStack(0x78)
    //     0xe79ca0: sub             SP, SP, #0x78
    // 0xe79ca4: SetupParameters(DoaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe79ca4: stur            NULL, [fp, #-8]
    //     0xe79ca8: stur            x1, [fp, #-0x58]
    //     0xe79cac: stur            x2, [fp, #-0x60]
    // 0xe79cb0: CheckStackOverflow
    //     0xe79cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79cb4: cmp             SP, x16
    //     0xe79cb8: b.ls            #0xe79d78
    // 0xe79cbc: InitAsync() -> Future<ApiResult<List<DoaCategory>>>
    //     0xe79cbc: add             x0, PP, #0x40, lsl #12  ; [pp+0x40680] TypeArguments: <ApiResult<List<DoaCategory>>>
    //     0xe79cc0: ldr             x0, [x0, #0x680]
    //     0xe79cc4: bl              #0x661298  ; InitAsyncStub
    // 0xe79cc8: ldur            x0, [fp, #-0x58]
    // 0xe79ccc: LoadField: r1 = r0->field_b
    //     0xe79ccc: ldur            w1, [x0, #0xb]
    // 0xe79cd0: DecompressPointer r1
    //     0xe79cd0: add             x1, x1, HEAP, lsl #32
    // 0xe79cd4: r0 = homeUpdateConfig()
    //     0xe79cd4: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0xe79cd8: LoadField: r1 = r0->field_b
    //     0xe79cd8: ldur            w1, [x0, #0xb]
    // 0xe79cdc: DecompressPointer r1
    //     0xe79cdc: add             x1, x1, HEAP, lsl #32
    // 0xe79ce0: tbz             w1, #4, #0xe79cf0
    // 0xe79ce4: r0 = Instance__$FailureImpl
    //     0xe79ce4: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a2e8] Obj!_$FailureImpl<List<DoaCategory>>@e0e591
    //     0xe79ce8: ldr             x0, [x0, #0x2e8]
    // 0xe79cec: r0 = ReturnAsyncNotFuture()
    //     0xe79cec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79cf0: ldur            x0, [fp, #-0x58]
    // 0xe79cf4: LoadField: r1 = r0->field_7
    //     0xe79cf4: ldur            w1, [x0, #7]
    // 0xe79cf8: DecompressPointer r1
    //     0xe79cf8: add             x1, x1, HEAP, lsl #32
    // 0xe79cfc: stp             x1, NULL, [SP, #8]
    // 0xe79d00: r16 = "/doa/categories"
    //     0xe79d00: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a2f0] "/doa/categories"
    //     0xe79d04: ldr             x16, [x16, #0x2f0]
    // 0xe79d08: str             x16, [SP]
    // 0xe79d0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79d0c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79d10: r0 = get()
    //     0xe79d10: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe79d14: mov             x1, x0
    // 0xe79d18: stur            x1, [fp, #-0x58]
    // 0xe79d1c: r0 = Await()
    //     0xe79d1c: bl              #0x661044  ; AwaitStub
    // 0xe79d20: LoadField: r1 = r0->field_b
    //     0xe79d20: ldur            w1, [x0, #0xb]
    // 0xe79d24: DecompressPointer r1
    //     0xe79d24: add             x1, x1, HEAP, lsl #32
    // 0xe79d28: r0 = fromResponse()
    //     0xe79d28: bl              #0xe79d80  ; [package:nuonline/app/data/models/doa.dart] DoaCategory::fromResponse
    // 0xe79d2c: r1 = <List<DoaCategory>>
    //     0xe79d2c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b48] TypeArguments: <List<DoaCategory>>
    //     0xe79d30: ldr             x1, [x1, #0xb48]
    // 0xe79d34: stur            x0, [fp, #-0x58]
    // 0xe79d38: r0 = _$SuccessImpl()
    //     0xe79d38: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe79d3c: mov             x1, x0
    // 0xe79d40: ldur            x0, [fp, #-0x58]
    // 0xe79d44: StoreField: r1->field_b = r0
    //     0xe79d44: stur            w0, [x1, #0xb]
    // 0xe79d48: mov             x0, x1
    // 0xe79d4c: r0 = ReturnAsyncNotFuture()
    //     0xe79d4c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79d50: sub             SP, fp, #0x78
    // 0xe79d54: mov             x1, x0
    // 0xe79d58: r0 = getDioException()
    //     0xe79d58: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe79d5c: r1 = <List<DoaCategory>>
    //     0xe79d5c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35b48] TypeArguments: <List<DoaCategory>>
    //     0xe79d60: ldr             x1, [x1, #0xb48]
    // 0xe79d64: stur            x0, [fp, #-0x58]
    // 0xe79d68: r0 = _$FailureImpl()
    //     0xe79d68: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe79d6c: ldur            x1, [fp, #-0x58]
    // 0xe79d70: StoreField: r0->field_b = r1
    //     0xe79d70: stur            w1, [x0, #0xb]
    // 0xe79d74: r0 = ReturnAsyncNotFuture()
    //     0xe79d74: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79d78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79d7c: b               #0xe79cbc
  }
  _ findAllSubCategoryByLastUpdated(/* No info */) async {
    // ** addr: 0xe79f50, size: 0x124
    // 0xe79f50: EnterFrame
    //     0xe79f50: stp             fp, lr, [SP, #-0x10]!
    //     0xe79f54: mov             fp, SP
    // 0xe79f58: AllocStack(0x88)
    //     0xe79f58: sub             SP, SP, #0x88
    // 0xe79f5c: SetupParameters(DoaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe79f5c: stur            NULL, [fp, #-8]
    //     0xe79f60: stur            x1, [fp, #-0x58]
    //     0xe79f64: stur            x2, [fp, #-0x60]
    // 0xe79f68: CheckStackOverflow
    //     0xe79f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79f6c: cmp             SP, x16
    //     0xe79f70: b.ls            #0xe7a06c
    // 0xe79f74: InitAsync() -> Future<ApiResult<List<DoaSubCategory>>>
    //     0xe79f74: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bd38] TypeArguments: <ApiResult<List<DoaSubCategory>>>
    //     0xe79f78: ldr             x0, [x0, #0xd38]
    //     0xe79f7c: bl              #0x661298  ; InitAsyncStub
    // 0xe79f80: ldur            x0, [fp, #-0x58]
    // 0xe79f84: LoadField: r1 = r0->field_b
    //     0xe79f84: ldur            w1, [x0, #0xb]
    // 0xe79f88: DecompressPointer r1
    //     0xe79f88: add             x1, x1, HEAP, lsl #32
    // 0xe79f8c: r0 = homeUpdateConfig()
    //     0xe79f8c: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0xe79f90: LoadField: r1 = r0->field_b
    //     0xe79f90: ldur            w1, [x0, #0xb]
    // 0xe79f94: DecompressPointer r1
    //     0xe79f94: add             x1, x1, HEAP, lsl #32
    // 0xe79f98: tbz             w1, #4, #0xe79fa8
    // 0xe79f9c: r0 = Instance__$FailureImpl
    //     0xe79f9c: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a1e0] Obj!_$FailureImpl<List<DoaSubCategory>>@e0e5a1
    //     0xe79fa0: ldr             x0, [x0, #0x1e0]
    // 0xe79fa4: r0 = ReturnAsyncNotFuture()
    //     0xe79fa4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79fa8: ldur            x0, [fp, #-0x58]
    // 0xe79fac: ldur            x3, [fp, #-0x60]
    // 0xe79fb0: LoadField: r4 = r0->field_7
    //     0xe79fb0: ldur            w4, [x0, #7]
    // 0xe79fb4: DecompressPointer r4
    //     0xe79fb4: add             x4, x4, HEAP, lsl #32
    // 0xe79fb8: stur            x4, [fp, #-0x68]
    // 0xe79fbc: r1 = Null
    //     0xe79fbc: mov             x1, NULL
    // 0xe79fc0: r2 = 4
    //     0xe79fc0: movz            x2, #0x4
    // 0xe79fc4: r0 = AllocateArray()
    //     0xe79fc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe79fc8: r16 = "last_updated_at"
    //     0xe79fc8: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1e8] "last_updated_at"
    //     0xe79fcc: ldr             x16, [x16, #0x1e8]
    // 0xe79fd0: StoreField: r0->field_f = r16
    //     0xe79fd0: stur            w16, [x0, #0xf]
    // 0xe79fd4: ldur            x1, [fp, #-0x60]
    // 0xe79fd8: StoreField: r0->field_13 = r1
    //     0xe79fd8: stur            w1, [x0, #0x13]
    // 0xe79fdc: r16 = <String, dynamic>
    //     0xe79fdc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe79fe0: stp             x0, x16, [SP]
    // 0xe79fe4: r0 = Map._fromLiteral()
    //     0xe79fe4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe79fe8: ldur            x16, [fp, #-0x68]
    // 0xe79fec: stp             x16, NULL, [SP, #0x10]
    // 0xe79ff0: r16 = "/doa/sub-categories/latest"
    //     0xe79ff0: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1f0] "/doa/sub-categories/latest"
    //     0xe79ff4: ldr             x16, [x16, #0x1f0]
    // 0xe79ff8: stp             x0, x16, [SP]
    // 0xe79ffc: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe79ffc: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe7a000: ldr             x4, [x4, #0x2f0]
    // 0xe7a004: r0 = get()
    //     0xe7a004: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7a008: mov             x1, x0
    // 0xe7a00c: stur            x1, [fp, #-0x58]
    // 0xe7a010: r0 = Await()
    //     0xe7a010: bl              #0x661044  ; AwaitStub
    // 0xe7a014: LoadField: r1 = r0->field_b
    //     0xe7a014: ldur            w1, [x0, #0xb]
    // 0xe7a018: DecompressPointer r1
    //     0xe7a018: add             x1, x1, HEAP, lsl #32
    // 0xe7a01c: r0 = fromResponse()
    //     0xe7a01c: bl              #0xe7a074  ; [package:nuonline/app/data/models/doa.dart] DoaSubCategory::fromResponse
    // 0xe7a020: r1 = <List<DoaSubCategory>>
    //     0xe7a020: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xe7a024: ldr             x1, [x1, #0x7d8]
    // 0xe7a028: stur            x0, [fp, #-0x58]
    // 0xe7a02c: r0 = _$SuccessImpl()
    //     0xe7a02c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7a030: mov             x1, x0
    // 0xe7a034: ldur            x0, [fp, #-0x58]
    // 0xe7a038: StoreField: r1->field_b = r0
    //     0xe7a038: stur            w0, [x1, #0xb]
    // 0xe7a03c: mov             x0, x1
    // 0xe7a040: r0 = ReturnAsyncNotFuture()
    //     0xe7a040: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7a044: sub             SP, fp, #0x88
    // 0xe7a048: mov             x1, x0
    // 0xe7a04c: r0 = getDioException()
    //     0xe7a04c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7a050: r1 = <List<DoaSubCategory>>
    //     0xe7a050: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f7d8] TypeArguments: <List<DoaSubCategory>>
    //     0xe7a054: ldr             x1, [x1, #0x7d8]
    // 0xe7a058: stur            x0, [fp, #-0x58]
    // 0xe7a05c: r0 = _$FailureImpl()
    //     0xe7a05c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7a060: ldur            x1, [fp, #-0x58]
    // 0xe7a064: StoreField: r0->field_b = r1
    //     0xe7a064: stur            w1, [x0, #0xb]
    // 0xe7a068: r0 = ReturnAsyncNotFuture()
    //     0xe7a068: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7a06c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a06c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a070: b               #0xe79f74
  }
  _ findAllSubCategory(/* No info */) {
    // ** addr: 0xe7a7d4, size: 0x2c
    // 0xe7a7d4: EnterFrame
    //     0xe7a7d4: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a7d8: mov             fp, SP
    // 0xe7a7dc: LoadField: r0 = r4->field_1f
    //     0xe7a7dc: ldur            w0, [x4, #0x1f]
    // 0xe7a7e0: DecompressPointer r0
    //     0xe7a7e0: add             x0, x0, HEAP, lsl #32
    // 0xe7a7e4: r16 = "categoryId"
    //     0xe7a7e4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c48] "categoryId"
    //     0xe7a7e8: ldr             x16, [x16, #0xc48]
    // 0xe7a7ec: cmp             w0, w16
    // 0xe7a7f0: b.eq            #0xe7a7f4
    // 0xe7a7f4: r0 = UnimplementedError()
    //     0xe7a7f4: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xe7a7f8: r0 = Throw()
    //     0xe7a7f8: bl              #0xec04b8  ; ThrowStub
    // 0xe7a7fc: brk             #0
  }
}
