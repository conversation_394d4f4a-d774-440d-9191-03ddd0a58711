// lib: , url: package:nuonline/app/data/repositories/donation_repository.dart

// class id: 1050076, size: 0x8
class :: {
}

// class id: 1100, size: 0x10, field offset: 0x8
class DonationRepository extends Object {

  _ addTransaction(/* No info */) async {
    // ** addr: 0x6fbdac, size: 0x588
    // 0x6fbdac: EnterFrame
    //     0x6fbdac: stp             fp, lr, [SP, #-0x10]!
    //     0x6fbdb0: mov             fp, SP
    // 0x6fbdb4: AllocStack(0xb8)
    //     0x6fbdb4: sub             SP, SP, #0xb8
    // 0x6fbdb8: SetupParameters(DonationRepository this /* r1 => r1, fp-0x70 */, dynamic _ /* r2 => r2, fp-0x78 */)
    //     0x6fbdb8: stur            NULL, [fp, #-8]
    //     0x6fbdbc: stur            x1, [fp, #-0x70]
    //     0x6fbdc0: stur            x2, [fp, #-0x78]
    // 0x6fbdc4: CheckStackOverflow
    //     0x6fbdc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fbdc8: cmp             SP, x16
    //     0x6fbdcc: b.ls            #0x6fc32c
    // 0x6fbdd0: InitAsync() -> Future<ApiResult<Transaction>>
    //     0x6fbdd0: add             x0, PP, #0x40, lsl #12  ; [pp+0x404a0] TypeArguments: <ApiResult<Transaction>>
    //     0x6fbdd4: ldr             x0, [x0, #0x4a0]
    //     0x6fbdd8: bl              #0x661298  ; InitAsyncStub
    // 0x6fbddc: ldur            x3, [fp, #-0x70]
    // 0x6fbde0: ldur            x0, [fp, #-0x78]
    // 0x6fbde4: LoadField: r4 = r3->field_7
    //     0x6fbde4: ldur            w4, [x3, #7]
    // 0x6fbde8: DecompressPointer r4
    //     0x6fbde8: add             x4, x4, HEAP, lsl #32
    // 0x6fbdec: stur            x4, [fp, #-0x80]
    // 0x6fbdf0: r1 = Null
    //     0x6fbdf0: mov             x1, NULL
    // 0x6fbdf4: r2 = 60
    //     0x6fbdf4: movz            x2, #0x3c
    // 0x6fbdf8: r0 = AllocateArray()
    //     0x6fbdf8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6fbdfc: mov             x2, x0
    // 0x6fbe00: stur            x2, [fp, #-0x88]
    // 0x6fbe04: r16 = "udid"
    //     0x6fbe04: add             x16, PP, #0x10, lsl #12  ; [pp+0x10410] "udid"
    //     0x6fbe08: ldr             x16, [x16, #0x410]
    // 0x6fbe0c: StoreField: r2->field_f = r16
    //     0x6fbe0c: stur            w16, [x2, #0xf]
    // 0x6fbe10: ldur            x0, [fp, #-0x70]
    // 0x6fbe14: LoadField: r1 = r0->field_b
    //     0x6fbe14: ldur            w1, [x0, #0xb]
    // 0x6fbe18: DecompressPointer r1
    //     0x6fbe18: add             x1, x1, HEAP, lsl #32
    // 0x6fbe1c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x6fbe1c: ldur            w0, [x1, #0x17]
    // 0x6fbe20: DecompressPointer r0
    //     0x6fbe20: add             x0, x0, HEAP, lsl #32
    // 0x6fbe24: StoreField: r2->field_13 = r0
    //     0x6fbe24: stur            w0, [x2, #0x13]
    // 0x6fbe28: r16 = "donation_id"
    //     0x6fbe28: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b078] "donation_id"
    //     0x6fbe2c: ldr             x16, [x16, #0x78]
    // 0x6fbe30: ArrayStore: r2[0] = r16  ; List_4
    //     0x6fbe30: stur            w16, [x2, #0x17]
    // 0x6fbe34: ldur            x3, [fp, #-0x78]
    // 0x6fbe38: LoadField: r0 = r3->field_37
    //     0x6fbe38: ldur            w0, [x3, #0x37]
    // 0x6fbe3c: DecompressPointer r0
    //     0x6fbe3c: add             x0, x0, HEAP, lsl #32
    // 0x6fbe40: cmp             w0, NULL
    // 0x6fbe44: b.ne            #0x6fbe50
    // 0x6fbe48: r0 = Null
    //     0x6fbe48: mov             x0, NULL
    // 0x6fbe4c: b               #0x6fbe68
    // 0x6fbe50: LoadField: r4 = r0->field_7
    //     0x6fbe50: ldur            x4, [x0, #7]
    // 0x6fbe54: r0 = BoxInt64Instr(r4)
    //     0x6fbe54: sbfiz           x0, x4, #1, #0x1f
    //     0x6fbe58: cmp             x4, x0, asr #1
    //     0x6fbe5c: b.eq            #0x6fbe68
    //     0x6fbe60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6fbe64: stur            x4, [x0, #7]
    // 0x6fbe68: cmp             w0, NULL
    // 0x6fbe6c: b.ne            #0x6fbe94
    // 0x6fbe70: LoadField: r1 = r3->field_13
    //     0x6fbe70: ldur            w1, [x3, #0x13]
    // 0x6fbe74: DecompressPointer r1
    //     0x6fbe74: add             x1, x1, HEAP, lsl #32
    // 0x6fbe78: cmp             w1, NULL
    // 0x6fbe7c: b.ne            #0x6fbe88
    // 0x6fbe80: r0 = Null
    //     0x6fbe80: mov             x0, NULL
    // 0x6fbe84: b               #0x6fbe94
    // 0x6fbe88: r0 = ZakatTypesExtension.id()
    //     0x6fbe88: bl              #0x72a57c  ; [package:nuonline/app/data/enums/zakat_enum.dart] ::ZakatTypesExtension.id
    // 0x6fbe8c: lsl             x1, x0, #1
    // 0x6fbe90: mov             x0, x1
    // 0x6fbe94: cmp             w0, NULL
    // 0x6fbe98: b.ne            #0x6fbeb4
    // 0x6fbe9c: ldur            x2, [fp, #-0x78]
    // 0x6fbea0: LoadField: r0 = r2->field_f
    //     0x6fbea0: ldur            w0, [x2, #0xf]
    // 0x6fbea4: DecompressPointer r0
    //     0x6fbea4: add             x0, x0, HEAP, lsl #32
    // 0x6fbea8: ArrayLoad: r1 = r0[0]  ; List_8
    //     0x6fbea8: ldur            x1, [x0, #0x17]
    // 0x6fbeac: mov             x4, x1
    // 0x6fbeb0: b               #0x6fbec8
    // 0x6fbeb4: ldur            x2, [fp, #-0x78]
    // 0x6fbeb8: r1 = LoadInt32Instr(r0)
    //     0x6fbeb8: sbfx            x1, x0, #1, #0x1f
    //     0x6fbebc: tbz             w0, #0, #0x6fbec4
    //     0x6fbec0: ldur            x1, [x0, #7]
    // 0x6fbec4: mov             x4, x1
    // 0x6fbec8: ldur            x3, [fp, #-0x88]
    // 0x6fbecc: r0 = BoxInt64Instr(r4)
    //     0x6fbecc: sbfiz           x0, x4, #1, #0x1f
    //     0x6fbed0: cmp             x4, x0, asr #1
    //     0x6fbed4: b.eq            #0x6fbee0
    //     0x6fbed8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6fbedc: stur            x4, [x0, #7]
    // 0x6fbee0: mov             x1, x3
    // 0x6fbee4: ArrayStore: r1[3] = r0  ; List_4
    //     0x6fbee4: add             x25, x1, #0x1b
    //     0x6fbee8: str             w0, [x25]
    //     0x6fbeec: tbz             w0, #0, #0x6fbf08
    //     0x6fbef0: ldurb           w16, [x1, #-1]
    //     0x6fbef4: ldurb           w17, [x0, #-1]
    //     0x6fbef8: and             x16, x17, x16, lsr #2
    //     0x6fbefc: tst             x16, HEAP, lsr #32
    //     0x6fbf00: b.eq            #0x6fbf08
    //     0x6fbf04: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fbf08: r16 = "amount"
    //     0x6fbf08: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x6fbf0c: ldr             x16, [x16, #0xe0]
    // 0x6fbf10: StoreField: r3->field_1f = r16
    //     0x6fbf10: stur            w16, [x3, #0x1f]
    // 0x6fbf14: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x6fbf14: ldur            w0, [x2, #0x17]
    // 0x6fbf18: DecompressPointer r0
    //     0x6fbf18: add             x0, x0, HEAP, lsl #32
    // 0x6fbf1c: mov             x1, x3
    // 0x6fbf20: ArrayStore: r1[5] = r0  ; List_4
    //     0x6fbf20: add             x25, x1, #0x23
    //     0x6fbf24: str             w0, [x25]
    //     0x6fbf28: tbz             w0, #0, #0x6fbf44
    //     0x6fbf2c: ldurb           w16, [x1, #-1]
    //     0x6fbf30: ldurb           w17, [x0, #-1]
    //     0x6fbf34: and             x16, x17, x16, lsr #2
    //     0x6fbf38: tst             x16, HEAP, lsr #32
    //     0x6fbf3c: b.eq            #0x6fbf44
    //     0x6fbf40: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fbf44: r16 = "name"
    //     0x6fbf44: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    // 0x6fbf48: StoreField: r3->field_27 = r16
    //     0x6fbf48: stur            w16, [x3, #0x27]
    // 0x6fbf4c: LoadField: r0 = r2->field_1b
    //     0x6fbf4c: ldur            w0, [x2, #0x1b]
    // 0x6fbf50: DecompressPointer r0
    //     0x6fbf50: add             x0, x0, HEAP, lsl #32
    // 0x6fbf54: mov             x1, x3
    // 0x6fbf58: ArrayStore: r1[7] = r0  ; List_4
    //     0x6fbf58: add             x25, x1, #0x2b
    //     0x6fbf5c: str             w0, [x25]
    //     0x6fbf60: tbz             w0, #0, #0x6fbf7c
    //     0x6fbf64: ldurb           w16, [x1, #-1]
    //     0x6fbf68: ldurb           w17, [x0, #-1]
    //     0x6fbf6c: and             x16, x17, x16, lsr #2
    //     0x6fbf70: tst             x16, HEAP, lsr #32
    //     0x6fbf74: b.eq            #0x6fbf7c
    //     0x6fbf78: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fbf7c: r16 = "payment_method"
    //     0x6fbf7c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b1b8] "payment_method"
    //     0x6fbf80: ldr             x16, [x16, #0x1b8]
    // 0x6fbf84: StoreField: r3->field_2f = r16
    //     0x6fbf84: stur            w16, [x3, #0x2f]
    // 0x6fbf88: LoadField: r0 = r2->field_b
    //     0x6fbf88: ldur            w0, [x2, #0xb]
    // 0x6fbf8c: DecompressPointer r0
    //     0x6fbf8c: add             x0, x0, HEAP, lsl #32
    // 0x6fbf90: LoadField: r1 = r0->field_7
    //     0x6fbf90: ldur            x1, [x0, #7]
    // 0x6fbf94: cmp             x1, #1
    // 0x6fbf98: b.gt            #0x6fbfbc
    // 0x6fbf9c: cmp             x1, #0
    // 0x6fbfa0: b.gt            #0x6fbfb0
    // 0x6fbfa4: r0 = "qris"
    //     0x6fbfa4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b218] "qris"
    //     0x6fbfa8: ldr             x0, [x0, #0x218]
    // 0x6fbfac: b               #0x6fbfd8
    // 0x6fbfb0: r0 = "va"
    //     0x6fbfb0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b228] "va"
    //     0x6fbfb4: ldr             x0, [x0, #0x228]
    // 0x6fbfb8: b               #0x6fbfd8
    // 0x6fbfbc: cmp             x1, #2
    // 0x6fbfc0: b.gt            #0x6fbfd0
    // 0x6fbfc4: r0 = "bsi"
    //     0x6fbfc4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b238] "bsi"
    //     0x6fbfc8: ldr             x0, [x0, #0x238]
    // 0x6fbfcc: b               #0x6fbfd8
    // 0x6fbfd0: r0 = "bri"
    //     0x6fbfd0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b248] "bri"
    //     0x6fbfd4: ldr             x0, [x0, #0x248]
    // 0x6fbfd8: mov             x1, x3
    // 0x6fbfdc: ArrayStore: r1[9] = r0  ; List_4
    //     0x6fbfdc: add             x25, x1, #0x33
    //     0x6fbfe0: str             w0, [x25]
    //     0x6fbfe4: tbz             w0, #0, #0x6fc000
    //     0x6fbfe8: ldurb           w16, [x1, #-1]
    //     0x6fbfec: ldurb           w17, [x0, #-1]
    //     0x6fbff0: and             x16, x17, x16, lsr #2
    //     0x6fbff4: tst             x16, HEAP, lsr #32
    //     0x6fbff8: b.eq            #0x6fc000
    //     0x6fbffc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc000: r16 = "is_anon"
    //     0x6fc000: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b128] "is_anon"
    //     0x6fc004: ldr             x16, [x16, #0x128]
    // 0x6fc008: StoreField: r3->field_37 = r16
    //     0x6fc008: stur            w16, [x3, #0x37]
    // 0x6fc00c: LoadField: r0 = r2->field_33
    //     0x6fc00c: ldur            w0, [x2, #0x33]
    // 0x6fc010: DecompressPointer r0
    //     0x6fc010: add             x0, x0, HEAP, lsl #32
    // 0x6fc014: StoreField: r3->field_3b = r0
    //     0x6fc014: stur            w0, [x3, #0x3b]
    // 0x6fc018: r16 = "phone"
    //     0x6fc018: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b160] "phone"
    //     0x6fc01c: ldr             x16, [x16, #0x160]
    // 0x6fc020: StoreField: r3->field_3f = r16
    //     0x6fc020: stur            w16, [x3, #0x3f]
    // 0x6fc024: LoadField: r0 = r2->field_2b
    //     0x6fc024: ldur            w0, [x2, #0x2b]
    // 0x6fc028: DecompressPointer r0
    //     0x6fc028: add             x0, x0, HEAP, lsl #32
    // 0x6fc02c: mov             x1, x3
    // 0x6fc030: ArrayStore: r1[13] = r0  ; List_4
    //     0x6fc030: add             x25, x1, #0x43
    //     0x6fc034: str             w0, [x25]
    //     0x6fc038: tbz             w0, #0, #0x6fc054
    //     0x6fc03c: ldurb           w16, [x1, #-1]
    //     0x6fc040: ldurb           w17, [x0, #-1]
    //     0x6fc044: and             x16, x17, x16, lsr #2
    //     0x6fc048: tst             x16, HEAP, lsr #32
    //     0x6fc04c: b.eq            #0x6fc054
    //     0x6fc050: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc054: r16 = "email"
    //     0x6fc054: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b178] "email"
    //     0x6fc058: ldr             x16, [x16, #0x178]
    // 0x6fc05c: StoreField: r3->field_47 = r16
    //     0x6fc05c: stur            w16, [x3, #0x47]
    // 0x6fc060: LoadField: r0 = r2->field_27
    //     0x6fc060: ldur            w0, [x2, #0x27]
    // 0x6fc064: DecompressPointer r0
    //     0x6fc064: add             x0, x0, HEAP, lsl #32
    // 0x6fc068: mov             x1, x3
    // 0x6fc06c: ArrayStore: r1[15] = r0  ; List_4
    //     0x6fc06c: add             x25, x1, #0x4b
    //     0x6fc070: str             w0, [x25]
    //     0x6fc074: tbz             w0, #0, #0x6fc090
    //     0x6fc078: ldurb           w16, [x1, #-1]
    //     0x6fc07c: ldurb           w17, [x0, #-1]
    //     0x6fc080: and             x16, x17, x16, lsr #2
    //     0x6fc084: tst             x16, HEAP, lsr #32
    //     0x6fc088: b.eq            #0x6fc090
    //     0x6fc08c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc090: r16 = "description"
    //     0x6fc090: add             x16, PP, #0x19, lsl #12  ; [pp+0x19d28] "description"
    //     0x6fc094: ldr             x16, [x16, #0xd28]
    // 0x6fc098: StoreField: r3->field_4f = r16
    //     0x6fc098: stur            w16, [x3, #0x4f]
    // 0x6fc09c: LoadField: r0 = r2->field_2f
    //     0x6fc09c: ldur            w0, [x2, #0x2f]
    // 0x6fc0a0: DecompressPointer r0
    //     0x6fc0a0: add             x0, x0, HEAP, lsl #32
    // 0x6fc0a4: mov             x1, x3
    // 0x6fc0a8: ArrayStore: r1[17] = r0  ; List_4
    //     0x6fc0a8: add             x25, x1, #0x53
    //     0x6fc0ac: str             w0, [x25]
    //     0x6fc0b0: tbz             w0, #0, #0x6fc0cc
    //     0x6fc0b4: ldurb           w16, [x1, #-1]
    //     0x6fc0b8: ldurb           w17, [x0, #-1]
    //     0x6fc0bc: and             x16, x17, x16, lsr #2
    //     0x6fc0c0: tst             x16, HEAP, lsr #32
    //     0x6fc0c4: b.eq            #0x6fc0cc
    //     0x6fc0c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc0cc: r16 = "names"
    //     0x6fc0cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe60] "names"
    //     0x6fc0d0: ldr             x16, [x16, #0xe60]
    // 0x6fc0d4: StoreField: r3->field_57 = r16
    //     0x6fc0d4: stur            w16, [x3, #0x57]
    // 0x6fc0d8: LoadField: r0 = r2->field_1f
    //     0x6fc0d8: ldur            w0, [x2, #0x1f]
    // 0x6fc0dc: DecompressPointer r0
    //     0x6fc0dc: add             x0, x0, HEAP, lsl #32
    // 0x6fc0e0: mov             x1, x3
    // 0x6fc0e4: ArrayStore: r1[19] = r0  ; List_4
    //     0x6fc0e4: add             x25, x1, #0x5b
    //     0x6fc0e8: str             w0, [x25]
    //     0x6fc0ec: tbz             w0, #0, #0x6fc108
    //     0x6fc0f0: ldurb           w16, [x1, #-1]
    //     0x6fc0f4: ldurb           w17, [x0, #-1]
    //     0x6fc0f8: and             x16, x17, x16, lsr #2
    //     0x6fc0fc: tst             x16, HEAP, lsr #32
    //     0x6fc100: b.eq            #0x6fc108
    //     0x6fc104: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc108: r16 = "days"
    //     0x6fc108: add             x16, PP, #8, lsl #12  ; [pp+0x8e70] "days"
    //     0x6fc10c: ldr             x16, [x16, #0xe70]
    // 0x6fc110: StoreField: r3->field_5f = r16
    //     0x6fc110: stur            w16, [x3, #0x5f]
    // 0x6fc114: LoadField: r0 = r2->field_23
    //     0x6fc114: ldur            w0, [x2, #0x23]
    // 0x6fc118: DecompressPointer r0
    //     0x6fc118: add             x0, x0, HEAP, lsl #32
    // 0x6fc11c: mov             x1, x3
    // 0x6fc120: ArrayStore: r1[21] = r0  ; List_4
    //     0x6fc120: add             x25, x1, #0x63
    //     0x6fc124: str             w0, [x25]
    //     0x6fc128: tbz             w0, #0, #0x6fc144
    //     0x6fc12c: ldurb           w16, [x1, #-1]
    //     0x6fc130: ldurb           w17, [x0, #-1]
    //     0x6fc134: and             x16, x17, x16, lsr #2
    //     0x6fc138: tst             x16, HEAP, lsr #32
    //     0x6fc13c: b.eq            #0x6fc144
    //     0x6fc140: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc144: r16 = "province_id"
    //     0x6fc144: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x6fc148: ldr             x16, [x16, #0x100]
    // 0x6fc14c: StoreField: r3->field_67 = r16
    //     0x6fc14c: stur            w16, [x3, #0x67]
    // 0x6fc150: LoadField: r0 = r2->field_3b
    //     0x6fc150: ldur            w0, [x2, #0x3b]
    // 0x6fc154: DecompressPointer r0
    //     0x6fc154: add             x0, x0, HEAP, lsl #32
    // 0x6fc158: mov             x1, x3
    // 0x6fc15c: ArrayStore: r1[23] = r0  ; List_4
    //     0x6fc15c: add             x25, x1, #0x6b
    //     0x6fc160: str             w0, [x25]
    //     0x6fc164: tbz             w0, #0, #0x6fc180
    //     0x6fc168: ldurb           w16, [x1, #-1]
    //     0x6fc16c: ldurb           w17, [x0, #-1]
    //     0x6fc170: and             x16, x17, x16, lsr #2
    //     0x6fc174: tst             x16, HEAP, lsr #32
    //     0x6fc178: b.eq            #0x6fc180
    //     0x6fc17c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc180: r16 = "regency_id"
    //     0x6fc180: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x6fc184: ldr             x16, [x16, #0x118]
    // 0x6fc188: StoreField: r3->field_6f = r16
    //     0x6fc188: stur            w16, [x3, #0x6f]
    // 0x6fc18c: LoadField: r0 = r2->field_3f
    //     0x6fc18c: ldur            w0, [x2, #0x3f]
    // 0x6fc190: DecompressPointer r0
    //     0x6fc190: add             x0, x0, HEAP, lsl #32
    // 0x6fc194: mov             x1, x3
    // 0x6fc198: ArrayStore: r1[25] = r0  ; List_4
    //     0x6fc198: add             x25, x1, #0x73
    //     0x6fc19c: str             w0, [x25]
    //     0x6fc1a0: tbz             w0, #0, #0x6fc1bc
    //     0x6fc1a4: ldurb           w16, [x1, #-1]
    //     0x6fc1a8: ldurb           w17, [x0, #-1]
    //     0x6fc1ac: and             x16, x17, x16, lsr #2
    //     0x6fc1b0: tst             x16, HEAP, lsr #32
    //     0x6fc1b4: b.eq            #0x6fc1bc
    //     0x6fc1b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc1bc: r16 = "district_id"
    //     0x6fc1bc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x6fc1c0: ldr             x16, [x16, #0xfe8]
    // 0x6fc1c4: StoreField: r3->field_77 = r16
    //     0x6fc1c4: stur            w16, [x3, #0x77]
    // 0x6fc1c8: LoadField: r0 = r2->field_43
    //     0x6fc1c8: ldur            w0, [x2, #0x43]
    // 0x6fc1cc: DecompressPointer r0
    //     0x6fc1cc: add             x0, x0, HEAP, lsl #32
    // 0x6fc1d0: mov             x1, x3
    // 0x6fc1d4: ArrayStore: r1[27] = r0  ; List_4
    //     0x6fc1d4: add             x25, x1, #0x7b
    //     0x6fc1d8: str             w0, [x25]
    //     0x6fc1dc: tbz             w0, #0, #0x6fc1f8
    //     0x6fc1e0: ldurb           w16, [x1, #-1]
    //     0x6fc1e4: ldurb           w17, [x0, #-1]
    //     0x6fc1e8: and             x16, x17, x16, lsr #2
    //     0x6fc1ec: tst             x16, HEAP, lsr #32
    //     0x6fc1f0: b.eq            #0x6fc1f8
    //     0x6fc1f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc1f8: r16 = "locality_id"
    //     0x6fc1f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe70] "locality_id"
    //     0x6fc1fc: ldr             x16, [x16, #0xe70]
    // 0x6fc200: StoreField: r3->field_7f = r16
    //     0x6fc200: stur            w16, [x3, #0x7f]
    // 0x6fc204: LoadField: r0 = r2->field_47
    //     0x6fc204: ldur            w0, [x2, #0x47]
    // 0x6fc208: DecompressPointer r0
    //     0x6fc208: add             x0, x0, HEAP, lsl #32
    // 0x6fc20c: mov             x1, x3
    // 0x6fc210: ArrayStore: r1[29] = r0  ; List_4
    //     0x6fc210: add             x25, x1, #0x83
    //     0x6fc214: str             w0, [x25]
    //     0x6fc218: tbz             w0, #0, #0x6fc234
    //     0x6fc21c: ldurb           w16, [x1, #-1]
    //     0x6fc220: ldurb           w17, [x0, #-1]
    //     0x6fc224: and             x16, x17, x16, lsr #2
    //     0x6fc228: tst             x16, HEAP, lsr #32
    //     0x6fc22c: b.eq            #0x6fc234
    //     0x6fc230: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x6fc234: r16 = <String, Object?>
    //     0x6fc234: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0x6fc238: ldr             x16, [x16, #0x738]
    // 0x6fc23c: stp             x3, x16, [SP]
    // 0x6fc240: r0 = Map._fromLiteral()
    //     0x6fc240: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x6fc244: ldur            x16, [fp, #-0x80]
    // 0x6fc248: stp             x16, NULL, [SP, #0x20]
    // 0x6fc24c: r16 = "/donation-transactions/"
    //     0x6fc24c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b088] "/donation-transactions/"
    //     0x6fc250: ldr             x16, [x16, #0x88]
    // 0x6fc254: r30 = false
    //     0x6fc254: add             lr, NULL, #0x30  ; false
    // 0x6fc258: stp             lr, x16, [SP, #0x10]
    // 0x6fc25c: r16 = "POST"
    //     0x6fc25c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10430] "POST"
    //     0x6fc260: ldr             x16, [x16, #0x430]
    // 0x6fc264: stp             x0, x16, [SP]
    // 0x6fc268: r4 = const [0x1, 0x5, 0x5, 0x3, data, 0x4, method, 0x3, null]
    //     0x6fc268: add             x4, PP, #0x40, lsl #12  ; [pp+0x404d8] List(9) [0x1, 0x5, 0x5, 0x3, "data", 0x4, "method", 0x3, Null]
    //     0x6fc26c: ldr             x4, [x4, #0x4d8]
    // 0x6fc270: r0 = withRetry()
    //     0x6fc270: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x6fc274: mov             x1, x0
    // 0x6fc278: stur            x1, [fp, #-0x70]
    // 0x6fc27c: r0 = Await()
    //     0x6fc27c: bl              #0x661044  ; AwaitStub
    // 0x6fc280: mov             x3, x0
    // 0x6fc284: stur            x3, [fp, #-0x78]
    // 0x6fc288: LoadField: r4 = r3->field_b
    //     0x6fc288: ldur            w4, [x3, #0xb]
    // 0x6fc28c: DecompressPointer r4
    //     0x6fc28c: add             x4, x4, HEAP, lsl #32
    // 0x6fc290: mov             x0, x4
    // 0x6fc294: stur            x4, [fp, #-0x70]
    // 0x6fc298: r2 = Null
    //     0x6fc298: mov             x2, NULL
    // 0x6fc29c: r1 = Null
    //     0x6fc29c: mov             x1, NULL
    // 0x6fc2a0: r8 = Map<String, dynamic>
    //     0x6fc2a0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x6fc2a4: r3 = Null
    //     0x6fc2a4: add             x3, PP, #0x40, lsl #12  ; [pp+0x404e0] Null
    //     0x6fc2a8: ldr             x3, [x3, #0x4e0]
    // 0x6fc2ac: r0 = Map<String, dynamic>()
    //     0x6fc2ac: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x6fc2b0: ldur            x2, [fp, #-0x70]
    // 0x6fc2b4: r1 = Null
    //     0x6fc2b4: mov             x1, NULL
    // 0x6fc2b8: r0 = Transaction.fromMap()
    //     0x6fc2b8: bl              #0x6fcae8  ; [package:nuonline/app/data/models/transaction.dart] Transaction::Transaction.fromMap
    // 0x6fc2bc: mov             x3, x0
    // 0x6fc2c0: ldur            x0, [fp, #-0x78]
    // 0x6fc2c4: stur            x3, [fp, #-0x70]
    // 0x6fc2c8: LoadField: r2 = r0->field_1b
    //     0x6fc2c8: ldur            w2, [x0, #0x1b]
    // 0x6fc2cc: DecompressPointer r2
    //     0x6fc2cc: add             x2, x2, HEAP, lsl #32
    // 0x6fc2d0: r1 = Null
    //     0x6fc2d0: mov             x1, NULL
    // 0x6fc2d4: r0 = Pagination.fromHeaders()
    //     0x6fc2d4: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x6fc2d8: r1 = <Transaction>
    //     0x6fc2d8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x6fc2dc: ldr             x1, [x1, #0x28]
    // 0x6fc2e0: stur            x0, [fp, #-0x78]
    // 0x6fc2e4: r0 = _$SuccessImpl()
    //     0x6fc2e4: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x6fc2e8: mov             x1, x0
    // 0x6fc2ec: ldur            x0, [fp, #-0x70]
    // 0x6fc2f0: StoreField: r1->field_b = r0
    //     0x6fc2f0: stur            w0, [x1, #0xb]
    // 0x6fc2f4: ldur            x0, [fp, #-0x78]
    // 0x6fc2f8: StoreField: r1->field_f = r0
    //     0x6fc2f8: stur            w0, [x1, #0xf]
    // 0x6fc2fc: mov             x0, x1
    // 0x6fc300: r0 = ReturnAsyncNotFuture()
    //     0x6fc300: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x6fc304: sub             SP, fp, #0xb8
    // 0x6fc308: mov             x1, x0
    // 0x6fc30c: r0 = getDioException()
    //     0x6fc30c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x6fc310: r1 = <Transaction>
    //     0x6fc310: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x6fc314: ldr             x1, [x1, #0x28]
    // 0x6fc318: stur            x0, [fp, #-0x70]
    // 0x6fc31c: r0 = _$FailureImpl()
    //     0x6fc31c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x6fc320: ldur            x1, [fp, #-0x70]
    // 0x6fc324: StoreField: r0->field_b = r1
    //     0x6fc324: stur            w1, [x0, #0xb]
    // 0x6fc328: r0 = ReturnAsyncNotFuture()
    //     0x6fc328: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x6fc32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fc32c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fc330: b               #0x6fbdd0
  }
  _ findTransactionById(/* No info */) async {
    // ** addr: 0x72a9b8, size: 0x12c
    // 0x72a9b8: EnterFrame
    //     0x72a9b8: stp             fp, lr, [SP, #-0x10]!
    //     0x72a9bc: mov             fp, SP
    // 0x72a9c0: AllocStack(0x80)
    //     0x72a9c0: sub             SP, SP, #0x80
    // 0x72a9c4: SetupParameters(DonationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x72a9c4: stur            NULL, [fp, #-8]
    //     0x72a9c8: stur            x1, [fp, #-0x58]
    //     0x72a9cc: stur            x2, [fp, #-0x60]
    // 0x72a9d0: CheckStackOverflow
    //     0x72a9d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72a9d4: cmp             SP, x16
    //     0x72a9d8: b.ls            #0x72aadc
    // 0x72a9dc: InitAsync() -> Future<ApiResult<Transaction>>
    //     0x72a9dc: add             x0, PP, #0x40, lsl #12  ; [pp+0x404a0] TypeArguments: <ApiResult<Transaction>>
    //     0x72a9e0: ldr             x0, [x0, #0x4a0]
    //     0x72a9e4: bl              #0x661298  ; InitAsyncStub
    // 0x72a9e8: ldur            x1, [fp, #-0x58]
    // 0x72a9ec: ldur            x0, [fp, #-0x60]
    // 0x72a9f0: LoadField: r3 = r1->field_7
    //     0x72a9f0: ldur            w3, [x1, #7]
    // 0x72a9f4: DecompressPointer r3
    //     0x72a9f4: add             x3, x3, HEAP, lsl #32
    // 0x72a9f8: stur            x3, [fp, #-0x68]
    // 0x72a9fc: r1 = Null
    //     0x72a9fc: mov             x1, NULL
    // 0x72aa00: r2 = 4
    //     0x72aa00: movz            x2, #0x4
    // 0x72aa04: r0 = AllocateArray()
    //     0x72aa04: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72aa08: mov             x2, x0
    // 0x72aa0c: r16 = "/donation-transactions/"
    //     0x72aa0c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b088] "/donation-transactions/"
    //     0x72aa10: ldr             x16, [x16, #0x88]
    // 0x72aa14: StoreField: r2->field_f = r16
    //     0x72aa14: stur            w16, [x2, #0xf]
    // 0x72aa18: ldur            x3, [fp, #-0x60]
    // 0x72aa1c: r0 = BoxInt64Instr(r3)
    //     0x72aa1c: sbfiz           x0, x3, #1, #0x1f
    //     0x72aa20: cmp             x3, x0, asr #1
    //     0x72aa24: b.eq            #0x72aa30
    //     0x72aa28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72aa2c: stur            x3, [x0, #7]
    // 0x72aa30: StoreField: r2->field_13 = r0
    //     0x72aa30: stur            w0, [x2, #0x13]
    // 0x72aa34: str             x2, [SP]
    // 0x72aa38: r0 = _interpolate()
    //     0x72aa38: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x72aa3c: ldur            x16, [fp, #-0x68]
    // 0x72aa40: stp             x16, NULL, [SP, #8]
    // 0x72aa44: str             x0, [SP]
    // 0x72aa48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72aa48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72aa4c: r0 = get()
    //     0x72aa4c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x72aa50: mov             x1, x0
    // 0x72aa54: stur            x1, [fp, #-0x58]
    // 0x72aa58: r0 = Await()
    //     0x72aa58: bl              #0x661044  ; AwaitStub
    // 0x72aa5c: LoadField: r3 = r0->field_b
    //     0x72aa5c: ldur            w3, [x0, #0xb]
    // 0x72aa60: DecompressPointer r3
    //     0x72aa60: add             x3, x3, HEAP, lsl #32
    // 0x72aa64: mov             x0, x3
    // 0x72aa68: stur            x3, [fp, #-0x58]
    // 0x72aa6c: r2 = Null
    //     0x72aa6c: mov             x2, NULL
    // 0x72aa70: r1 = Null
    //     0x72aa70: mov             x1, NULL
    // 0x72aa74: r8 = Map<String, dynamic>
    //     0x72aa74: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72aa78: r3 = Null
    //     0x72aa78: add             x3, PP, #0x40, lsl #12  ; [pp+0x404a8] Null
    //     0x72aa7c: ldr             x3, [x3, #0x4a8]
    // 0x72aa80: r0 = Map<String, dynamic>()
    //     0x72aa80: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72aa84: ldur            x2, [fp, #-0x58]
    // 0x72aa88: r1 = Null
    //     0x72aa88: mov             x1, NULL
    // 0x72aa8c: r0 = Transaction.fromMap()
    //     0x72aa8c: bl              #0x6fcae8  ; [package:nuonline/app/data/models/transaction.dart] Transaction::Transaction.fromMap
    // 0x72aa90: r1 = <Transaction>
    //     0x72aa90: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x72aa94: ldr             x1, [x1, #0x28]
    // 0x72aa98: stur            x0, [fp, #-0x58]
    // 0x72aa9c: r0 = _$SuccessImpl()
    //     0x72aa9c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x72aaa0: mov             x1, x0
    // 0x72aaa4: ldur            x0, [fp, #-0x58]
    // 0x72aaa8: StoreField: r1->field_b = r0
    //     0x72aaa8: stur            w0, [x1, #0xb]
    // 0x72aaac: mov             x0, x1
    // 0x72aab0: r0 = ReturnAsyncNotFuture()
    //     0x72aab0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72aab4: sub             SP, fp, #0x80
    // 0x72aab8: mov             x1, x0
    // 0x72aabc: r0 = getDioException()
    //     0x72aabc: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x72aac0: r1 = <Transaction>
    //     0x72aac0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b028] TypeArguments: <Transaction>
    //     0x72aac4: ldr             x1, [x1, #0x28]
    // 0x72aac8: stur            x0, [fp, #-0x58]
    // 0x72aacc: r0 = _$FailureImpl()
    //     0x72aacc: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x72aad0: ldur            x1, [fp, #-0x58]
    // 0x72aad4: StoreField: r0->field_b = r1
    //     0x72aad4: stur            w1, [x0, #0xb]
    // 0x72aad8: r0 = ReturnAsyncNotFuture()
    //     0x72aad8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72aadc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72aadc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72aae0: b               #0x72a9dc
  }
  _ findAll(/* No info */) async {
    // ** addr: 0x72b550, size: 0x198
    // 0x72b550: EnterFrame
    //     0x72b550: stp             fp, lr, [SP, #-0x10]!
    //     0x72b554: mov             fp, SP
    // 0x72b558: AllocStack(0xb0)
    //     0x72b558: sub             SP, SP, #0xb0
    // 0x72b55c: SetupParameters(DonationRepository this /* r1 => r2, fp-0x80 */, dynamic _ /* r2 => r1, fp-0x88 */, {dynamic self = false /* r3, fp-0x78 */})
    //     0x72b55c: stur            NULL, [fp, #-8]
    //     0x72b560: stur            x1, [fp, #-0x80]
    //     0x72b564: mov             x16, x2
    //     0x72b568: mov             x2, x1
    //     0x72b56c: mov             x1, x16
    //     0x72b570: stur            x1, [fp, #-0x88]
    //     0x72b574: stur            x4, [fp, #-0x90]
    //     0x72b578: ldur            w0, [x4, #0x13]
    //     0x72b57c: ldur            w3, [x4, #0x1f]
    //     0x72b580: add             x3, x3, HEAP, lsl #32
    //     0x72b584: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b080] "self"
    //     0x72b588: ldr             x16, [x16, #0x80]
    //     0x72b58c: cmp             w3, w16
    //     0x72b590: b.ne            #0x72b5b0
    //     0x72b594: ldur            w3, [x4, #0x23]
    //     0x72b598: add             x3, x3, HEAP, lsl #32
    //     0x72b59c: sub             w5, w0, w3
    //     0x72b5a0: add             x0, fp, w5, sxtw #2
    //     0x72b5a4: ldr             x0, [x0, #8]
    //     0x72b5a8: mov             x3, x0
    //     0x72b5ac: b               #0x72b5b4
    //     0x72b5b0: add             x3, NULL, #0x30  ; false
    //     0x72b5b4: stur            x3, [fp, #-0x78]
    // 0x72b5b8: CheckStackOverflow
    //     0x72b5b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72b5bc: cmp             SP, x16
    //     0x72b5c0: b.ls            #0x72b6e0
    // 0x72b5c4: InitAsync() -> Future<ApiResult<List<Transaction>>>
    //     0x72b5c4: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b048] TypeArguments: <ApiResult<List<Transaction>>>
    //     0x72b5c8: ldr             x0, [x0, #0x48]
    //     0x72b5cc: bl              #0x661298  ; InitAsyncStub
    // 0x72b5d0: ldur            x0, [fp, #-0x78]
    // 0x72b5d4: tbnz            w0, #4, #0x72b628
    // 0x72b5d8: ldur            x0, [fp, #-0x80]
    // 0x72b5dc: r1 = Null
    //     0x72b5dc: mov             x1, NULL
    // 0x72b5e0: r2 = 4
    //     0x72b5e0: movz            x2, #0x4
    // 0x72b5e4: r0 = AllocateArray()
    //     0x72b5e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72b5e8: r16 = "udid"
    //     0x72b5e8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10410] "udid"
    //     0x72b5ec: ldr             x16, [x16, #0x410]
    // 0x72b5f0: StoreField: r0->field_f = r16
    //     0x72b5f0: stur            w16, [x0, #0xf]
    // 0x72b5f4: ldur            x1, [fp, #-0x80]
    // 0x72b5f8: LoadField: r2 = r1->field_b
    //     0x72b5f8: ldur            w2, [x1, #0xb]
    // 0x72b5fc: DecompressPointer r2
    //     0x72b5fc: add             x2, x2, HEAP, lsl #32
    // 0x72b600: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x72b600: ldur            w3, [x2, #0x17]
    // 0x72b604: DecompressPointer r3
    //     0x72b604: add             x3, x3, HEAP, lsl #32
    // 0x72b608: StoreField: r0->field_13 = r3
    //     0x72b608: stur            w3, [x0, #0x13]
    // 0x72b60c: r16 = <String, String?>
    //     0x72b60c: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0x72b610: ldr             x16, [x16, #0x198]
    // 0x72b614: stp             x0, x16, [SP]
    // 0x72b618: r0 = Map._fromLiteral()
    //     0x72b618: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x72b61c: ldur            x1, [fp, #-0x88]
    // 0x72b620: mov             x2, x0
    // 0x72b624: r0 = addAll()
    //     0x72b624: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x72b628: ldur            x0, [fp, #-0x80]
    // 0x72b62c: LoadField: r1 = r0->field_7
    //     0x72b62c: ldur            w1, [x0, #7]
    // 0x72b630: DecompressPointer r1
    //     0x72b630: add             x1, x1, HEAP, lsl #32
    // 0x72b634: stp             x1, NULL, [SP, #0x10]
    // 0x72b638: r16 = "/donation-transactions/"
    //     0x72b638: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b088] "/donation-transactions/"
    //     0x72b63c: ldr             x16, [x16, #0x88]
    // 0x72b640: ldur            lr, [fp, #-0x88]
    // 0x72b644: stp             lr, x16, [SP]
    // 0x72b648: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x72b648: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x72b64c: ldr             x4, [x4, #0x2f0]
    // 0x72b650: r0 = get()
    //     0x72b650: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x72b654: mov             x1, x0
    // 0x72b658: stur            x1, [fp, #-0x78]
    // 0x72b65c: r0 = Await()
    //     0x72b65c: bl              #0x661044  ; AwaitStub
    // 0x72b660: stur            x0, [fp, #-0x78]
    // 0x72b664: LoadField: r1 = r0->field_b
    //     0x72b664: ldur            w1, [x0, #0xb]
    // 0x72b668: DecompressPointer r1
    //     0x72b668: add             x1, x1, HEAP, lsl #32
    // 0x72b66c: r0 = fromResponse()
    //     0x72b66c: bl              #0x72b6e8  ; [package:nuonline/app/data/models/transaction.dart] Transaction::fromResponse
    // 0x72b670: mov             x3, x0
    // 0x72b674: ldur            x0, [fp, #-0x78]
    // 0x72b678: stur            x3, [fp, #-0x80]
    // 0x72b67c: LoadField: r2 = r0->field_1b
    //     0x72b67c: ldur            w2, [x0, #0x1b]
    // 0x72b680: DecompressPointer r2
    //     0x72b680: add             x2, x2, HEAP, lsl #32
    // 0x72b684: r1 = Null
    //     0x72b684: mov             x1, NULL
    // 0x72b688: r0 = Pagination.fromHeaders()
    //     0x72b688: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x72b68c: r1 = <List<Transaction>>
    //     0x72b68c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b090] TypeArguments: <List<Transaction>>
    //     0x72b690: ldr             x1, [x1, #0x90]
    // 0x72b694: stur            x0, [fp, #-0x78]
    // 0x72b698: r0 = _$SuccessImpl()
    //     0x72b698: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x72b69c: mov             x1, x0
    // 0x72b6a0: ldur            x0, [fp, #-0x80]
    // 0x72b6a4: StoreField: r1->field_b = r0
    //     0x72b6a4: stur            w0, [x1, #0xb]
    // 0x72b6a8: ldur            x0, [fp, #-0x78]
    // 0x72b6ac: StoreField: r1->field_f = r0
    //     0x72b6ac: stur            w0, [x1, #0xf]
    // 0x72b6b0: mov             x0, x1
    // 0x72b6b4: r0 = ReturnAsyncNotFuture()
    //     0x72b6b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72b6b8: sub             SP, fp, #0xb0
    // 0x72b6bc: mov             x1, x0
    // 0x72b6c0: r0 = getDioException()
    //     0x72b6c0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x72b6c4: r1 = <List<Transaction>>
    //     0x72b6c4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b090] TypeArguments: <List<Transaction>>
    //     0x72b6c8: ldr             x1, [x1, #0x90]
    // 0x72b6cc: stur            x0, [fp, #-0x78]
    // 0x72b6d0: r0 = _$FailureImpl()
    //     0x72b6d0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x72b6d4: ldur            x1, [fp, #-0x78]
    // 0x72b6d8: StoreField: r0->field_b = r1
    //     0x72b6d8: stur            w1, [x0, #0xb]
    // 0x72b6dc: r0 = ReturnAsyncNotFuture()
    //     0x72b6dc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72b6e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b6e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b6e4: b               #0x72b5c4
  }
  _ findCampaignById(/* No info */) async {
    // ** addr: 0x7e649c, size: 0x12c
    // 0x7e649c: EnterFrame
    //     0x7e649c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e64a0: mov             fp, SP
    // 0x7e64a4: AllocStack(0x80)
    //     0x7e64a4: sub             SP, SP, #0x80
    // 0x7e64a8: SetupParameters(DonationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x7e64a8: stur            NULL, [fp, #-8]
    //     0x7e64ac: stur            x1, [fp, #-0x58]
    //     0x7e64b0: stur            x2, [fp, #-0x60]
    // 0x7e64b4: CheckStackOverflow
    //     0x7e64b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e64b8: cmp             SP, x16
    //     0x7e64bc: b.ls            #0x7e65c0
    // 0x7e64c0: InitAsync() -> Future<ApiResult<Campaign>>
    //     0x7e64c0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40500] TypeArguments: <ApiResult<Campaign>>
    //     0x7e64c4: ldr             x0, [x0, #0x500]
    //     0x7e64c8: bl              #0x661298  ; InitAsyncStub
    // 0x7e64cc: ldur            x1, [fp, #-0x58]
    // 0x7e64d0: ldur            x0, [fp, #-0x60]
    // 0x7e64d4: LoadField: r3 = r1->field_7
    //     0x7e64d4: ldur            w3, [x1, #7]
    // 0x7e64d8: DecompressPointer r3
    //     0x7e64d8: add             x3, x3, HEAP, lsl #32
    // 0x7e64dc: stur            x3, [fp, #-0x68]
    // 0x7e64e0: r1 = Null
    //     0x7e64e0: mov             x1, NULL
    // 0x7e64e4: r2 = 4
    //     0x7e64e4: movz            x2, #0x4
    // 0x7e64e8: r0 = AllocateArray()
    //     0x7e64e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e64ec: mov             x2, x0
    // 0x7e64f0: r16 = "/donations/"
    //     0x7e64f0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40508] "/donations/"
    //     0x7e64f4: ldr             x16, [x16, #0x508]
    // 0x7e64f8: StoreField: r2->field_f = r16
    //     0x7e64f8: stur            w16, [x2, #0xf]
    // 0x7e64fc: ldur            x3, [fp, #-0x60]
    // 0x7e6500: r0 = BoxInt64Instr(r3)
    //     0x7e6500: sbfiz           x0, x3, #1, #0x1f
    //     0x7e6504: cmp             x3, x0, asr #1
    //     0x7e6508: b.eq            #0x7e6514
    //     0x7e650c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7e6510: stur            x3, [x0, #7]
    // 0x7e6514: StoreField: r2->field_13 = r0
    //     0x7e6514: stur            w0, [x2, #0x13]
    // 0x7e6518: str             x2, [SP]
    // 0x7e651c: r0 = _interpolate()
    //     0x7e651c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7e6520: ldur            x16, [fp, #-0x68]
    // 0x7e6524: stp             x16, NULL, [SP, #8]
    // 0x7e6528: str             x0, [SP]
    // 0x7e652c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e652c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e6530: r0 = get()
    //     0x7e6530: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7e6534: mov             x1, x0
    // 0x7e6538: stur            x1, [fp, #-0x58]
    // 0x7e653c: r0 = Await()
    //     0x7e653c: bl              #0x661044  ; AwaitStub
    // 0x7e6540: LoadField: r3 = r0->field_b
    //     0x7e6540: ldur            w3, [x0, #0xb]
    // 0x7e6544: DecompressPointer r3
    //     0x7e6544: add             x3, x3, HEAP, lsl #32
    // 0x7e6548: mov             x0, x3
    // 0x7e654c: stur            x3, [fp, #-0x58]
    // 0x7e6550: r2 = Null
    //     0x7e6550: mov             x2, NULL
    // 0x7e6554: r1 = Null
    //     0x7e6554: mov             x1, NULL
    // 0x7e6558: r8 = Map<String, dynamic>
    //     0x7e6558: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e655c: r3 = Null
    //     0x7e655c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40510] Null
    //     0x7e6560: ldr             x3, [x3, #0x510]
    // 0x7e6564: r0 = Map<String, dynamic>()
    //     0x7e6564: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e6568: ldur            x2, [fp, #-0x58]
    // 0x7e656c: r1 = Null
    //     0x7e656c: mov             x1, NULL
    // 0x7e6570: r0 = Campaign.fromMap()
    //     0x7e6570: bl              #0x7e65c8  ; [package:nuonline/app/data/models/campaign.dart] Campaign::Campaign.fromMap
    // 0x7e6574: r1 = <Campaign>
    //     0x7e6574: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0x7e6578: ldr             x1, [x1, #0x598]
    // 0x7e657c: stur            x0, [fp, #-0x58]
    // 0x7e6580: r0 = _$SuccessImpl()
    //     0x7e6580: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e6584: mov             x1, x0
    // 0x7e6588: ldur            x0, [fp, #-0x58]
    // 0x7e658c: StoreField: r1->field_b = r0
    //     0x7e658c: stur            w0, [x1, #0xb]
    // 0x7e6590: mov             x0, x1
    // 0x7e6594: r0 = ReturnAsyncNotFuture()
    //     0x7e6594: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e6598: sub             SP, fp, #0x80
    // 0x7e659c: mov             x1, x0
    // 0x7e65a0: r0 = getDioException()
    //     0x7e65a0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e65a4: r1 = <Campaign>
    //     0x7e65a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f598] TypeArguments: <Campaign>
    //     0x7e65a8: ldr             x1, [x1, #0x598]
    // 0x7e65ac: stur            x0, [fp, #-0x58]
    // 0x7e65b0: r0 = _$FailureImpl()
    //     0x7e65b0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e65b4: ldur            x1, [fp, #-0x58]
    // 0x7e65b8: StoreField: r0->field_b = r1
    //     0x7e65b8: stur            w1, [x0, #0xb]
    // 0x7e65bc: r0 = ReturnAsyncNotFuture()
    //     0x7e65bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e65c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e65c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e65c4: b               #0x7e64c0
  }
  _ findAllCampaign(/* No info */) async {
    // ** addr: 0x7e7d0c, size: 0x128
    // 0x7e7d0c: EnterFrame
    //     0x7e7d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e7d10: mov             fp, SP
    // 0x7e7d14: AllocStack(0x90)
    //     0x7e7d14: sub             SP, SP, #0x90
    // 0x7e7d18: SetupParameters(DonationRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x7e7d18: stur            NULL, [fp, #-8]
    //     0x7e7d1c: stur            x1, [fp, #-0x60]
    //     0x7e7d20: stur            x2, [fp, #-0x68]
    // 0x7e7d24: CheckStackOverflow
    //     0x7e7d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e7d28: cmp             SP, x16
    //     0x7e7d2c: b.ls            #0x7e7e2c
    // 0x7e7d30: InitAsync() -> Future<ApiResult<List<Campaign>>>
    //     0x7e7d30: add             x0, PP, #0x32, lsl #12  ; [pp+0x32990] TypeArguments: <ApiResult<List<Campaign>>>
    //     0x7e7d34: ldr             x0, [x0, #0x990]
    //     0x7e7d38: bl              #0x661298  ; InitAsyncStub
    // 0x7e7d3c: ldur            x1, [fp, #-0x60]
    // 0x7e7d40: ldur            x0, [fp, #-0x68]
    // 0x7e7d44: LoadField: r3 = r1->field_7
    //     0x7e7d44: ldur            w3, [x1, #7]
    // 0x7e7d48: DecompressPointer r3
    //     0x7e7d48: add             x3, x3, HEAP, lsl #32
    // 0x7e7d4c: stur            x3, [fp, #-0x70]
    // 0x7e7d50: r1 = Null
    //     0x7e7d50: mov             x1, NULL
    // 0x7e7d54: r2 = 4
    //     0x7e7d54: movz            x2, #0x4
    // 0x7e7d58: r0 = AllocateArray()
    //     0x7e7d58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e7d5c: r16 = "category_id"
    //     0x7e7d5c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0x7e7d60: ldr             x16, [x16, #0xe70]
    // 0x7e7d64: StoreField: r0->field_f = r16
    //     0x7e7d64: stur            w16, [x0, #0xf]
    // 0x7e7d68: ldur            x1, [fp, #-0x68]
    // 0x7e7d6c: lsl             x2, x1, #1
    // 0x7e7d70: StoreField: r0->field_13 = r2
    //     0x7e7d70: stur            w2, [x0, #0x13]
    // 0x7e7d74: r16 = <String, dynamic>
    //     0x7e7d74: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7e7d78: stp             x0, x16, [SP]
    // 0x7e7d7c: r0 = Map._fromLiteral()
    //     0x7e7d7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e7d80: ldur            x16, [fp, #-0x70]
    // 0x7e7d84: stp             x16, NULL, [SP, #0x10]
    // 0x7e7d88: r16 = "/donations"
    //     0x7e7d88: add             x16, PP, #0x32, lsl #12  ; [pp+0x32998] "/donations"
    //     0x7e7d8c: ldr             x16, [x16, #0x998]
    // 0x7e7d90: stp             x0, x16, [SP]
    // 0x7e7d94: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x7e7d94: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x7e7d98: ldr             x4, [x4, #0x2f0]
    // 0x7e7d9c: r0 = get()
    //     0x7e7d9c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7e7da0: mov             x1, x0
    // 0x7e7da4: stur            x1, [fp, #-0x60]
    // 0x7e7da8: r0 = Await()
    //     0x7e7da8: bl              #0x661044  ; AwaitStub
    // 0x7e7dac: stur            x0, [fp, #-0x60]
    // 0x7e7db0: LoadField: r1 = r0->field_b
    //     0x7e7db0: ldur            w1, [x0, #0xb]
    // 0x7e7db4: DecompressPointer r1
    //     0x7e7db4: add             x1, x1, HEAP, lsl #32
    // 0x7e7db8: r0 = fromResponse()
    //     0x7e7db8: bl              #0x7e7e34  ; [package:nuonline/app/data/models/campaign.dart] Campaign::fromResponse
    // 0x7e7dbc: mov             x3, x0
    // 0x7e7dc0: ldur            x0, [fp, #-0x60]
    // 0x7e7dc4: stur            x3, [fp, #-0x70]
    // 0x7e7dc8: LoadField: r2 = r0->field_1b
    //     0x7e7dc8: ldur            w2, [x0, #0x1b]
    // 0x7e7dcc: DecompressPointer r2
    //     0x7e7dcc: add             x2, x2, HEAP, lsl #32
    // 0x7e7dd0: r1 = Null
    //     0x7e7dd0: mov             x1, NULL
    // 0x7e7dd4: r0 = Pagination.fromHeaders()
    //     0x7e7dd4: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x7e7dd8: r1 = <List<Campaign>>
    //     0x7e7dd8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aef0] TypeArguments: <List<Campaign>>
    //     0x7e7ddc: ldr             x1, [x1, #0xef0]
    // 0x7e7de0: stur            x0, [fp, #-0x60]
    // 0x7e7de4: r0 = _$SuccessImpl()
    //     0x7e7de4: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e7de8: mov             x1, x0
    // 0x7e7dec: ldur            x0, [fp, #-0x70]
    // 0x7e7df0: StoreField: r1->field_b = r0
    //     0x7e7df0: stur            w0, [x1, #0xb]
    // 0x7e7df4: ldur            x0, [fp, #-0x60]
    // 0x7e7df8: StoreField: r1->field_f = r0
    //     0x7e7df8: stur            w0, [x1, #0xf]
    // 0x7e7dfc: mov             x0, x1
    // 0x7e7e00: r0 = ReturnAsyncNotFuture()
    //     0x7e7e00: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e7e04: sub             SP, fp, #0x90
    // 0x7e7e08: mov             x1, x0
    // 0x7e7e0c: r0 = getDioException()
    //     0x7e7e0c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e7e10: r1 = <List<Campaign>>
    //     0x7e7e10: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2aef0] TypeArguments: <List<Campaign>>
    //     0x7e7e14: ldr             x1, [x1, #0xef0]
    // 0x7e7e18: stur            x0, [fp, #-0x60]
    // 0x7e7e1c: r0 = _$FailureImpl()
    //     0x7e7e1c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e7e20: ldur            x1, [fp, #-0x60]
    // 0x7e7e24: StoreField: r0->field_b = r1
    //     0x7e7e24: stur            w1, [x0, #0xb]
    // 0x7e7e28: r0 = ReturnAsyncNotFuture()
    //     0x7e7e28: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e7e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e7e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e7e30: b               #0x7e7d30
  }
  _ findAllQurbanReport(/* No info */) async {
    // ** addr: 0x7ea190, size: 0x208
    // 0x7ea190: EnterFrame
    //     0x7ea190: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea194: mov             fp, SP
    // 0x7ea198: AllocStack(0x88)
    //     0x7ea198: sub             SP, SP, #0x88
    // 0x7ea19c: SetupParameters(DonationRepository this /* r1 => r1, fp-0x60 */)
    //     0x7ea19c: stur            NULL, [fp, #-8]
    //     0x7ea1a0: stur            x1, [fp, #-0x60]
    // 0x7ea1a4: CheckStackOverflow
    //     0x7ea1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea1a8: cmp             SP, x16
    //     0x7ea1ac: b.ls            #0x7ea390
    // 0x7ea1b0: InitAsync() -> Future<ApiResult<List<QurbanReport>>>
    //     0x7ea1b0: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3d268] TypeArguments: <ApiResult<List<QurbanReport>>>
    //     0x7ea1b4: ldr             x0, [x0, #0x268]
    //     0x7ea1b8: bl              #0x661298  ; InitAsyncStub
    // 0x7ea1bc: ldur            x0, [fp, #-0x60]
    // 0x7ea1c0: LoadField: r3 = r0->field_7
    //     0x7ea1c0: ldur            w3, [x0, #7]
    // 0x7ea1c4: DecompressPointer r3
    //     0x7ea1c4: add             x3, x3, HEAP, lsl #32
    // 0x7ea1c8: stur            x3, [fp, #-0x68]
    // 0x7ea1cc: r1 = Null
    //     0x7ea1cc: mov             x1, NULL
    // 0x7ea1d0: r2 = 4
    //     0x7ea1d0: movz            x2, #0x4
    // 0x7ea1d4: r0 = AllocateArray()
    //     0x7ea1d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7ea1d8: r16 = "udid"
    //     0x7ea1d8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10410] "udid"
    //     0x7ea1dc: ldr             x16, [x16, #0x410]
    // 0x7ea1e0: StoreField: r0->field_f = r16
    //     0x7ea1e0: stur            w16, [x0, #0xf]
    // 0x7ea1e4: ldur            x1, [fp, #-0x60]
    // 0x7ea1e8: LoadField: r2 = r1->field_b
    //     0x7ea1e8: ldur            w2, [x1, #0xb]
    // 0x7ea1ec: DecompressPointer r2
    //     0x7ea1ec: add             x2, x2, HEAP, lsl #32
    // 0x7ea1f0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x7ea1f0: ldur            w1, [x2, #0x17]
    // 0x7ea1f4: DecompressPointer r1
    //     0x7ea1f4: add             x1, x1, HEAP, lsl #32
    // 0x7ea1f8: StoreField: r0->field_13 = r1
    //     0x7ea1f8: stur            w1, [x0, #0x13]
    // 0x7ea1fc: r16 = <String, dynamic>
    //     0x7ea1fc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7ea200: stp             x0, x16, [SP]
    // 0x7ea204: r0 = Map._fromLiteral()
    //     0x7ea204: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7ea208: ldur            x16, [fp, #-0x68]
    // 0x7ea20c: stp             x16, NULL, [SP, #0x10]
    // 0x7ea210: r16 = "/qurban/report"
    //     0x7ea210: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d270] "/qurban/report"
    //     0x7ea214: ldr             x16, [x16, #0x270]
    // 0x7ea218: stp             x0, x16, [SP]
    // 0x7ea21c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x7ea21c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x7ea220: ldr             x4, [x4, #0x2f0]
    // 0x7ea224: r0 = get()
    //     0x7ea224: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7ea228: mov             x1, x0
    // 0x7ea22c: stur            x1, [fp, #-0x60]
    // 0x7ea230: r0 = Await()
    //     0x7ea230: bl              #0x661044  ; AwaitStub
    // 0x7ea234: mov             x3, x0
    // 0x7ea238: stur            x3, [fp, #-0x68]
    // 0x7ea23c: LoadField: r4 = r3->field_b
    //     0x7ea23c: ldur            w4, [x3, #0xb]
    // 0x7ea240: DecompressPointer r4
    //     0x7ea240: add             x4, x4, HEAP, lsl #32
    // 0x7ea244: mov             x0, x4
    // 0x7ea248: stur            x4, [fp, #-0x60]
    // 0x7ea24c: r2 = Null
    //     0x7ea24c: mov             x2, NULL
    // 0x7ea250: r1 = Null
    //     0x7ea250: mov             x1, NULL
    // 0x7ea254: r4 = 60
    //     0x7ea254: movz            x4, #0x3c
    // 0x7ea258: branchIfSmi(r0, 0x7ea264)
    //     0x7ea258: tbz             w0, #0, #0x7ea264
    // 0x7ea25c: r4 = LoadClassIdInstr(r0)
    //     0x7ea25c: ldur            x4, [x0, #-1]
    //     0x7ea260: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea264: sub             x4, x4, #0x5a
    // 0x7ea268: cmp             x4, #2
    // 0x7ea26c: b.ls            #0x7ea284
    // 0x7ea270: r8 = List?
    //     0x7ea270: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7ea274: ldr             x8, [x8, #0x140]
    // 0x7ea278: r3 = Null
    //     0x7ea278: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d278] Null
    //     0x7ea27c: ldr             x3, [x3, #0x278]
    // 0x7ea280: r0 = List?()
    //     0x7ea280: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7ea284: ldur            x0, [fp, #-0x60]
    // 0x7ea288: cmp             w0, NULL
    // 0x7ea28c: b.ne            #0x7ea2a4
    // 0x7ea290: r1 = Null
    //     0x7ea290: mov             x1, NULL
    // 0x7ea294: r2 = 0
    //     0x7ea294: movz            x2, #0
    // 0x7ea298: r0 = _GrowableList()
    //     0x7ea298: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7ea29c: mov             x3, x0
    // 0x7ea2a0: b               #0x7ea2a8
    // 0x7ea2a4: mov             x3, x0
    // 0x7ea2a8: ldur            x0, [fp, #-0x68]
    // 0x7ea2ac: stur            x3, [fp, #-0x60]
    // 0x7ea2b0: r1 = Function '<anonymous closure>':.
    //     0x7ea2b0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d288] AnonymousClosure: (0x7ea398), in [package:nuonline/app/data/repositories/donation_repository.dart] DonationRepository::findAllQurbanReport (0x7ea190)
    //     0x7ea2b4: ldr             x1, [x1, #0x288]
    // 0x7ea2b8: r2 = Null
    //     0x7ea2b8: mov             x2, NULL
    // 0x7ea2bc: r0 = AllocateClosure()
    //     0x7ea2bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ea2c0: mov             x1, x0
    // 0x7ea2c4: ldur            x0, [fp, #-0x60]
    // 0x7ea2c8: r2 = LoadClassIdInstr(r0)
    //     0x7ea2c8: ldur            x2, [x0, #-1]
    //     0x7ea2cc: ubfx            x2, x2, #0xc, #0x14
    // 0x7ea2d0: r16 = <QurbanReport>
    //     0x7ea2d0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d290] TypeArguments: <QurbanReport>
    //     0x7ea2d4: ldr             x16, [x16, #0x290]
    // 0x7ea2d8: stp             x0, x16, [SP, #8]
    // 0x7ea2dc: str             x1, [SP]
    // 0x7ea2e0: mov             x0, x2
    // 0x7ea2e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ea2e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ea2e8: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7ea2e8: movz            x17, #0xf28c
    //     0x7ea2ec: add             lr, x0, x17
    //     0x7ea2f0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea2f4: blr             lr
    // 0x7ea2f8: r1 = LoadClassIdInstr(r0)
    //     0x7ea2f8: ldur            x1, [x0, #-1]
    //     0x7ea2fc: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea300: mov             x16, x0
    // 0x7ea304: mov             x0, x1
    // 0x7ea308: mov             x1, x16
    // 0x7ea30c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7ea30c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7ea310: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7ea310: movz            x17, #0xd889
    //     0x7ea314: add             lr, x0, x17
    //     0x7ea318: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea31c: blr             lr
    // 0x7ea320: mov             x3, x0
    // 0x7ea324: ldur            x0, [fp, #-0x68]
    // 0x7ea328: stur            x3, [fp, #-0x60]
    // 0x7ea32c: LoadField: r2 = r0->field_1b
    //     0x7ea32c: ldur            w2, [x0, #0x1b]
    // 0x7ea330: DecompressPointer r2
    //     0x7ea330: add             x2, x2, HEAP, lsl #32
    // 0x7ea334: r1 = Null
    //     0x7ea334: mov             x1, NULL
    // 0x7ea338: r0 = Pagination.fromHeaders()
    //     0x7ea338: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x7ea33c: r1 = <List<QurbanReport>>
    //     0x7ea33c: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b330] TypeArguments: <List<QurbanReport>>
    //     0x7ea340: ldr             x1, [x1, #0x330]
    // 0x7ea344: stur            x0, [fp, #-0x68]
    // 0x7ea348: r0 = _$SuccessImpl()
    //     0x7ea348: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7ea34c: mov             x1, x0
    // 0x7ea350: ldur            x0, [fp, #-0x60]
    // 0x7ea354: StoreField: r1->field_b = r0
    //     0x7ea354: stur            w0, [x1, #0xb]
    // 0x7ea358: ldur            x0, [fp, #-0x68]
    // 0x7ea35c: StoreField: r1->field_f = r0
    //     0x7ea35c: stur            w0, [x1, #0xf]
    // 0x7ea360: mov             x0, x1
    // 0x7ea364: r0 = ReturnAsyncNotFuture()
    //     0x7ea364: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7ea368: sub             SP, fp, #0x88
    // 0x7ea36c: mov             x1, x0
    // 0x7ea370: r0 = getDioException()
    //     0x7ea370: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7ea374: r1 = <List<QurbanReport>>
    //     0x7ea374: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b330] TypeArguments: <List<QurbanReport>>
    //     0x7ea378: ldr             x1, [x1, #0x330]
    // 0x7ea37c: stur            x0, [fp, #-0x60]
    // 0x7ea380: r0 = _$FailureImpl()
    //     0x7ea380: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7ea384: ldur            x1, [fp, #-0x60]
    // 0x7ea388: StoreField: r0->field_b = r1
    //     0x7ea388: stur            w1, [x0, #0xb]
    // 0x7ea38c: r0 = ReturnAsyncNotFuture()
    //     0x7ea38c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7ea390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea394: b               #0x7ea1b0
  }
  [closure] QurbanReport <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7ea398, size: 0x4c
    // 0x7ea398: EnterFrame
    //     0x7ea398: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea39c: mov             fp, SP
    // 0x7ea3a0: CheckStackOverflow
    //     0x7ea3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea3a4: cmp             SP, x16
    //     0x7ea3a8: b.ls            #0x7ea3dc
    // 0x7ea3ac: ldr             x0, [fp, #0x10]
    // 0x7ea3b0: r2 = Null
    //     0x7ea3b0: mov             x2, NULL
    // 0x7ea3b4: r1 = Null
    //     0x7ea3b4: mov             x1, NULL
    // 0x7ea3b8: r8 = Map<String, dynamic>
    //     0x7ea3b8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7ea3bc: r3 = Null
    //     0x7ea3bc: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d298] Null
    //     0x7ea3c0: ldr             x3, [x3, #0x298]
    // 0x7ea3c4: r0 = Map<String, dynamic>()
    //     0x7ea3c4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7ea3c8: ldr             x1, [fp, #0x10]
    // 0x7ea3cc: r0 = _$QurbanReportFromJson()
    //     0x7ea3cc: bl              #0x7ea3e4  ; [package:nuonline/app/data/models/qurban_report.dart] ::_$QurbanReportFromJson
    // 0x7ea3d0: LeaveFrame
    //     0x7ea3d0: mov             SP, fp
    //     0x7ea3d4: ldp             fp, lr, [SP], #0x10
    // 0x7ea3d8: ret
    //     0x7ea3d8: ret             
    // 0x7ea3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea3dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea3e0: b               #0x7ea3ac
  }
  _ findAllNews(/* No info */) async {
    // ** addr: 0x7eb7f8, size: 0x1d4
    // 0x7eb7f8: EnterFrame
    //     0x7eb7f8: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb7fc: mov             fp, SP
    // 0x7eb800: AllocStack(0xc8)
    //     0x7eb800: sub             SP, SP, #0xc8
    // 0x7eb804: SetupParameters(DonationRepository this /* r1 => r1, fp-0x78 */, dynamic _ /* r2 => r2, fp-0x80 */, dynamic _ /* r3 => r3, fp-0x88 */, dynamic _ /* r5 => r5, fp-0x90 */, dynamic _ /* r6 => r6, fp-0x98 */)
    //     0x7eb804: stur            NULL, [fp, #-8]
    //     0x7eb808: stur            x1, [fp, #-0x78]
    //     0x7eb80c: stur            x2, [fp, #-0x80]
    //     0x7eb810: stur            x3, [fp, #-0x88]
    //     0x7eb814: stur            x5, [fp, #-0x90]
    //     0x7eb818: stur            x6, [fp, #-0x98]
    // 0x7eb81c: CheckStackOverflow
    //     0x7eb81c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb820: cmp             SP, x16
    //     0x7eb824: b.ls            #0x7eb9c4
    // 0x7eb828: InitAsync() -> Future<ApiResult<List<DonationNews>>>
    //     0x7eb828: add             x0, PP, #0x35, lsl #12  ; [pp+0x35880] TypeArguments: <ApiResult<List<DonationNews>>>
    //     0x7eb82c: ldr             x0, [x0, #0x880]
    //     0x7eb830: bl              #0x661298  ; InitAsyncStub
    // 0x7eb834: ldur            x1, [fp, #-0x78]
    // 0x7eb838: ldur            x5, [fp, #-0x80]
    // 0x7eb83c: ldur            x4, [fp, #-0x88]
    // 0x7eb840: ldur            x3, [fp, #-0x90]
    // 0x7eb844: ldur            x0, [fp, #-0x98]
    // 0x7eb848: LoadField: r6 = r1->field_7
    //     0x7eb848: ldur            w6, [x1, #7]
    // 0x7eb84c: DecompressPointer r6
    //     0x7eb84c: add             x6, x6, HEAP, lsl #32
    // 0x7eb850: stur            x6, [fp, #-0xa0]
    // 0x7eb854: r1 = Null
    //     0x7eb854: mov             x1, NULL
    // 0x7eb858: r2 = 12
    //     0x7eb858: movz            x2, #0xc
    // 0x7eb85c: r0 = AllocateArray()
    //     0x7eb85c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7eb860: mov             x2, x0
    // 0x7eb864: r16 = "page"
    //     0x7eb864: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7eb868: ldr             x16, [x16, #0x300]
    // 0x7eb86c: StoreField: r2->field_f = r16
    //     0x7eb86c: stur            w16, [x2, #0xf]
    // 0x7eb870: ldur            x3, [fp, #-0x90]
    // 0x7eb874: r0 = BoxInt64Instr(r3)
    //     0x7eb874: sbfiz           x0, x3, #1, #0x1f
    //     0x7eb878: cmp             x3, x0, asr #1
    //     0x7eb87c: b.eq            #0x7eb888
    //     0x7eb880: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7eb884: stur            x3, [x0, #7]
    // 0x7eb888: StoreField: r2->field_13 = r0
    //     0x7eb888: stur            w0, [x2, #0x13]
    // 0x7eb88c: r16 = "category_id"
    //     0x7eb88c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0x7eb890: ldr             x16, [x16, #0xe70]
    // 0x7eb894: ArrayStore: r2[0] = r16  ; List_4
    //     0x7eb894: stur            w16, [x2, #0x17]
    // 0x7eb898: ldur            x0, [fp, #-0x98]
    // 0x7eb89c: LoadField: r3 = r0->field_1f
    //     0x7eb89c: ldur            x3, [x0, #0x1f]
    // 0x7eb8a0: r0 = BoxInt64Instr(r3)
    //     0x7eb8a0: sbfiz           x0, x3, #1, #0x1f
    //     0x7eb8a4: cmp             x3, x0, asr #1
    //     0x7eb8a8: b.eq            #0x7eb8b4
    //     0x7eb8ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7eb8b0: stur            x3, [x0, #7]
    // 0x7eb8b4: StoreField: r2->field_1b = r0
    //     0x7eb8b4: stur            w0, [x2, #0x1b]
    // 0x7eb8b8: r16 = "news_category"
    //     0x7eb8b8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35888] "news_category"
    //     0x7eb8bc: ldr             x16, [x16, #0x888]
    // 0x7eb8c0: StoreField: r2->field_1f = r16
    //     0x7eb8c0: stur            w16, [x2, #0x1f]
    // 0x7eb8c4: ldur            x0, [fp, #-0x88]
    // 0x7eb8c8: StoreField: r2->field_23 = r0
    //     0x7eb8c8: stur            w0, [x2, #0x23]
    // 0x7eb8cc: r16 = <String, dynamic>
    //     0x7eb8cc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7eb8d0: stp             x2, x16, [SP]
    // 0x7eb8d4: r0 = Map._fromLiteral()
    //     0x7eb8d4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7eb8d8: mov             x1, x0
    // 0x7eb8dc: ldur            x0, [fp, #-0x80]
    // 0x7eb8e0: stur            x1, [fp, #-0x78]
    // 0x7eb8e4: cmp             w0, NULL
    // 0x7eb8e8: r16 = true
    //     0x7eb8e8: add             x16, NULL, #0x20  ; true
    // 0x7eb8ec: r17 = false
    //     0x7eb8ec: add             x17, NULL, #0x30  ; false
    // 0x7eb8f0: csel            x2, x16, x17, ne
    // 0x7eb8f4: r16 = <String, dynamic>
    //     0x7eb8f4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x7eb8f8: stp             x1, x16, [SP, #0x18]
    // 0x7eb8fc: r16 = "donation_id"
    //     0x7eb8fc: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b078] "donation_id"
    //     0x7eb900: ldr             x16, [x16, #0x78]
    // 0x7eb904: stp             x16, x2, [SP, #8]
    // 0x7eb908: str             x0, [SP]
    // 0x7eb90c: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0x7eb90c: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0x7eb910: r0 = MapExtension.addIf()
    //     0x7eb910: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0x7eb914: ldur            x16, [fp, #-0xa0]
    // 0x7eb918: stp             x16, NULL, [SP, #0x10]
    // 0x7eb91c: r16 = "/news/"
    //     0x7eb91c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35890] "/news/"
    //     0x7eb920: ldr             x16, [x16, #0x890]
    // 0x7eb924: ldur            lr, [fp, #-0x78]
    // 0x7eb928: stp             lr, x16, [SP]
    // 0x7eb92c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x7eb92c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x7eb930: ldr             x4, [x4, #0x2f0]
    // 0x7eb934: r0 = get()
    //     0x7eb934: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7eb938: mov             x1, x0
    // 0x7eb93c: stur            x1, [fp, #-0x78]
    // 0x7eb940: r0 = Await()
    //     0x7eb940: bl              #0x661044  ; AwaitStub
    // 0x7eb944: stur            x0, [fp, #-0x78]
    // 0x7eb948: LoadField: r1 = r0->field_b
    //     0x7eb948: ldur            w1, [x0, #0xb]
    // 0x7eb94c: DecompressPointer r1
    //     0x7eb94c: add             x1, x1, HEAP, lsl #32
    // 0x7eb950: r0 = fromResponse()
    //     0x7eb950: bl              #0x7eb9cc  ; [package:nuonline/app/data/models/donation_news.dart] DonationNews::fromResponse
    // 0x7eb954: mov             x3, x0
    // 0x7eb958: ldur            x0, [fp, #-0x78]
    // 0x7eb95c: stur            x3, [fp, #-0x80]
    // 0x7eb960: LoadField: r2 = r0->field_1b
    //     0x7eb960: ldur            w2, [x0, #0x1b]
    // 0x7eb964: DecompressPointer r2
    //     0x7eb964: add             x2, x2, HEAP, lsl #32
    // 0x7eb968: r1 = Null
    //     0x7eb968: mov             x1, NULL
    // 0x7eb96c: r0 = Pagination.fromHeaders()
    //     0x7eb96c: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x7eb970: r1 = <List<DonationNews>>
    //     0x7eb970: add             x1, PP, #0x30, lsl #12  ; [pp+0x30478] TypeArguments: <List<DonationNews>>
    //     0x7eb974: ldr             x1, [x1, #0x478]
    // 0x7eb978: stur            x0, [fp, #-0x78]
    // 0x7eb97c: r0 = _$SuccessImpl()
    //     0x7eb97c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7eb980: mov             x1, x0
    // 0x7eb984: ldur            x0, [fp, #-0x80]
    // 0x7eb988: StoreField: r1->field_b = r0
    //     0x7eb988: stur            w0, [x1, #0xb]
    // 0x7eb98c: ldur            x0, [fp, #-0x78]
    // 0x7eb990: StoreField: r1->field_f = r0
    //     0x7eb990: stur            w0, [x1, #0xf]
    // 0x7eb994: mov             x0, x1
    // 0x7eb998: r0 = ReturnAsyncNotFuture()
    //     0x7eb998: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7eb99c: sub             SP, fp, #0xc8
    // 0x7eb9a0: mov             x1, x0
    // 0x7eb9a4: r0 = getDioException()
    //     0x7eb9a4: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7eb9a8: r1 = <List<DonationNews>>
    //     0x7eb9a8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30478] TypeArguments: <List<DonationNews>>
    //     0x7eb9ac: ldr             x1, [x1, #0x478]
    // 0x7eb9b0: stur            x0, [fp, #-0x78]
    // 0x7eb9b4: r0 = _$FailureImpl()
    //     0x7eb9b4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7eb9b8: ldur            x1, [fp, #-0x78]
    // 0x7eb9bc: StoreField: r0->field_b = r1
    //     0x7eb9bc: stur            w1, [x0, #0xb]
    // 0x7eb9c0: r0 = ReturnAsyncNotFuture()
    //     0x7eb9c0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7eb9c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb9c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb9c8: b               #0x7eb828
  }
}
