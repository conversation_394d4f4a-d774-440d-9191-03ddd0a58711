// lib: , url: package:nuonline/app/data/repositories/donor_repository.dart

// class id: 1050077, size: 0x8
class :: {
}

// class id: 1099, size: 0xc, field offset: 0x8
class DonorRepository extends Object {

  _ getDonor(/* No info */) {
    // ** addr: 0x8f8390, size: 0xbc
    // 0x8f8390: EnterFrame
    //     0x8f8390: stp             fp, lr, [SP, #-0x10]!
    //     0x8f8394: mov             fp, SP
    // 0x8f8398: AllocStack(0x20)
    //     0x8f8398: sub             SP, SP, #0x20
    // 0x8f839c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x8f839c: stur            x2, [fp, #-8]
    // 0x8f83a0: CheckStackOverflow
    //     0x8f83a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f83a4: cmp             SP, x16
    //     0x8f83a8: b.ls            #0x8f8444
    // 0x8f83ac: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8f83ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f83b0: ldr             x0, [x0, #0x2728]
    //     0x8f83b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f83b8: cmp             w0, w16
    //     0x8f83bc: b.ne            #0x8f83c8
    //     0x8f83c0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8f83c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f83c8: stp             x0, NULL, [SP, #8]
    // 0x8f83cc: r16 = "v2_donation_donors"
    //     0x8f83cc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c0] "v2_donation_donors"
    //     0x8f83d0: ldr             x16, [x16, #0x1c0]
    // 0x8f83d4: str             x16, [SP]
    // 0x8f83d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f83d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f83dc: r0 = box()
    //     0x8f83dc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f83e0: mov             x1, x0
    // 0x8f83e4: ldur            x0, [fp, #-8]
    // 0x8f83e8: LoadField: r2 = r0->field_f
    //     0x8f83e8: ldur            w2, [x0, #0xf]
    // 0x8f83ec: DecompressPointer r2
    //     0x8f83ec: add             x2, x2, HEAP, lsl #32
    // 0x8f83f0: r0 = get()
    //     0x8f83f0: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8f83f4: mov             x3, x0
    // 0x8f83f8: r2 = Null
    //     0x8f83f8: mov             x2, NULL
    // 0x8f83fc: r1 = Null
    //     0x8f83fc: mov             x1, NULL
    // 0x8f8400: stur            x3, [fp, #-8]
    // 0x8f8404: r4 = 60
    //     0x8f8404: movz            x4, #0x3c
    // 0x8f8408: branchIfSmi(r0, 0x8f8414)
    //     0x8f8408: tbz             w0, #0, #0x8f8414
    // 0x8f840c: r4 = LoadClassIdInstr(r0)
    //     0x8f840c: ldur            x4, [x0, #-1]
    //     0x8f8410: ubfx            x4, x4, #0xc, #0x14
    // 0x8f8414: r17 = 5589
    //     0x8f8414: movz            x17, #0x15d5
    // 0x8f8418: cmp             x4, x17
    // 0x8f841c: b.eq            #0x8f8434
    // 0x8f8420: r8 = Donor?
    //     0x8f8420: add             x8, PP, #0x40, lsl #12  ; [pp+0x404c0] Type: Donor?
    //     0x8f8424: ldr             x8, [x8, #0x4c0]
    // 0x8f8428: r3 = Null
    //     0x8f8428: add             x3, PP, #0x40, lsl #12  ; [pp+0x404c8] Null
    //     0x8f842c: ldr             x3, [x3, #0x4c8]
    // 0x8f8430: r0 = DefaultNullableTypeTest()
    //     0x8f8430: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x8f8434: ldur            x0, [fp, #-8]
    // 0x8f8438: LeaveFrame
    //     0x8f8438: mov             SP, fp
    //     0x8f843c: ldp             fp, lr, [SP], #0x10
    // 0x8f8440: ret
    //     0x8f8440: ret             
    // 0x8f8444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f8444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f8448: b               #0x8f83ac
  }
  _ updateDonor(/* No info */) async {
    // ** addr: 0xae560c, size: 0x74
    // 0xae560c: EnterFrame
    //     0xae560c: stp             fp, lr, [SP, #-0x10]!
    //     0xae5610: mov             fp, SP
    // 0xae5614: AllocStack(0x20)
    //     0xae5614: sub             SP, SP, #0x20
    // 0xae5618: SetupParameters(DonorRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xae5618: stur            NULL, [fp, #-8]
    //     0xae561c: stur            x1, [fp, #-0x10]
    //     0xae5620: stur            x2, [fp, #-0x18]
    //     0xae5624: stur            x3, [fp, #-0x20]
    // 0xae5628: CheckStackOverflow
    //     0xae5628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae562c: cmp             SP, x16
    //     0xae5630: b.ls            #0xae5678
    // 0xae5634: InitAsync() -> Future<void?>
    //     0xae5634: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xae5638: bl              #0x661298  ; InitAsyncStub
    // 0xae563c: ldur            x0, [fp, #-0x10]
    // 0xae5640: LoadField: r1 = r0->field_7
    //     0xae5640: ldur            w1, [x0, #7]
    // 0xae5644: DecompressPointer r1
    //     0xae5644: add             x1, x1, HEAP, lsl #32
    // 0xae5648: r0 = box()
    //     0xae5648: bl              #0x8f844c  ; [package:nuonline/services/storage_service/donation_storage.dart] DonationStorage::box
    // 0xae564c: mov             x1, x0
    // 0xae5650: ldur            x0, [fp, #-0x18]
    // 0xae5654: LoadField: r2 = r0->field_f
    //     0xae5654: ldur            w2, [x0, #0xf]
    // 0xae5658: DecompressPointer r2
    //     0xae5658: add             x2, x2, HEAP, lsl #32
    // 0xae565c: ldur            x3, [fp, #-0x20]
    // 0xae5660: r0 = put()
    //     0xae5660: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xae5664: mov             x1, x0
    // 0xae5668: stur            x1, [fp, #-0x10]
    // 0xae566c: r0 = Await()
    //     0xae566c: bl              #0x661044  ; AwaitStub
    // 0xae5670: r0 = Null
    //     0xae5670: mov             x0, NULL
    // 0xae5674: r0 = ReturnAsyncNotFuture()
    //     0xae5674: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xae5678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae567c: b               #0xae5634
  }
}
