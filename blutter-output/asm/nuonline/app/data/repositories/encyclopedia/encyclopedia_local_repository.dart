// lib: , url: package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart

// class id: 1050078, size: 0x8
class :: {
}

// class id: 1098, size: 0xc, field offset: 0x8
class EncyclopediaLocalRepository extends Object
    implements EncyclopediaRepository {

  static _ find(/* No info */) {
    // ** addr: 0x81214c, size: 0x64
    // 0x81214c: EnterFrame
    //     0x81214c: stp             fp, lr, [SP, #-0x10]!
    //     0x812150: mov             fp, SP
    // 0x812154: AllocStack(0x10)
    //     0x812154: sub             SP, SP, #0x10
    // 0x812158: CheckStackOverflow
    //     0x812158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81215c: cmp             SP, x16
    //     0x812160: b.ls            #0x8121a8
    // 0x812164: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812164: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812168: ldr             x0, [x0, #0x2670]
    //     0x81216c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x812170: cmp             w0, w16
    //     0x812174: b.ne            #0x812180
    //     0x812178: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x81217c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x812180: r16 = <EncyclopediaRepository>
    //     0x812180: add             x16, PP, #0x10, lsl #12  ; [pp+0x10120] TypeArguments: <EncyclopediaRepository>
    //     0x812184: ldr             x16, [x16, #0x120]
    // 0x812188: r30 = "encyclopedia_local_repo"
    //     0x812188: add             lr, PP, #0x10, lsl #12  ; [pp+0x10128] "encyclopedia_local_repo"
    //     0x81218c: ldr             lr, [lr, #0x128]
    // 0x812190: stp             lr, x16, [SP]
    // 0x812194: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x812194: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x812198: r0 = Inst.find()
    //     0x812198: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x81219c: LeaveFrame
    //     0x81219c: mov             SP, fp
    //     0x8121a0: ldp             fp, lr, [SP], #0x10
    // 0x8121a4: ret
    //     0x8121a4: ret             
    // 0x8121a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8121a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8121ac: b               #0x812164
  }
  _ findAll(/* No info */) async {
    // ** addr: 0xe78894, size: 0x290
    // 0xe78894: EnterFrame
    //     0xe78894: stp             fp, lr, [SP, #-0x10]!
    //     0xe78898: mov             fp, SP
    // 0xe7889c: AllocStack(0x58)
    //     0xe7889c: sub             SP, SP, #0x58
    // 0xe788a0: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0xe788a0: stur            NULL, [fp, #-8]
    //     0xe788a4: stur            x1, [fp, #-0x10]
    //     0xe788a8: stur            x2, [fp, #-0x18]
    //     0xe788ac: stur            x3, [fp, #-0x20]
    //     0xe788b0: stur            x6, [fp, #-0x28]
    //     0xe788b4: stur            x7, [fp, #-0x30]
    // 0xe788b8: CheckStackOverflow
    //     0xe788b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe788bc: cmp             SP, x16
    //     0xe788c0: b.ls            #0xe78b14
    // 0xe788c4: r1 = 3
    //     0xe788c4: movz            x1, #0x3
    // 0xe788c8: r0 = AllocateContext()
    //     0xe788c8: bl              #0xec126c  ; AllocateContextStub
    // 0xe788cc: mov             x1, x0
    // 0xe788d0: ldur            x0, [fp, #-0x18]
    // 0xe788d4: stur            x1, [fp, #-0x38]
    // 0xe788d8: StoreField: r1->field_f = r0
    //     0xe788d8: stur            w0, [x1, #0xf]
    // 0xe788dc: ldur            x0, [fp, #-0x20]
    // 0xe788e0: StoreField: r1->field_13 = r0
    //     0xe788e0: stur            w0, [x1, #0x13]
    // 0xe788e4: ldur            x0, [fp, #-0x30]
    // 0xe788e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xe788e8: stur            w0, [x1, #0x17]
    // 0xe788ec: InitAsync() -> Future<ApiResult<List<Encyclopedia>>>
    //     0xe788ec: add             x0, PP, #0x47, lsl #12  ; [pp+0x47aa8] TypeArguments: <ApiResult<List<Encyclopedia>>>
    //     0xe788f0: ldr             x0, [x0, #0xaa8]
    //     0xe788f4: bl              #0x661298  ; InitAsyncStub
    // 0xe788f8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe788f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe788fc: ldr             x0, [x0, #0x2728]
    //     0xe78900: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78904: cmp             w0, w16
    //     0xe78908: b.ne            #0xe78914
    //     0xe7890c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe78910: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe78914: r16 = <Encyclopedia>
    //     0xe78914: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78918: stp             x0, x16, [SP, #8]
    // 0xe7891c: r16 = "v2_encyclopedias"
    //     0xe7891c: ldr             x16, [PP, #0x7c20]  ; [pp+0x7c20] "v2_encyclopedias"
    // 0xe78920: str             x16, [SP]
    // 0xe78924: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78924: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78928: r0 = box()
    //     0xe78928: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7892c: mov             x1, x0
    // 0xe78930: stur            x0, [fp, #-0x10]
    // 0xe78934: r0 = checkOpen()
    //     0xe78934: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe78938: ldur            x0, [fp, #-0x10]
    // 0xe7893c: LoadField: r1 = r0->field_1b
    //     0xe7893c: ldur            w1, [x0, #0x1b]
    // 0xe78940: DecompressPointer r1
    //     0xe78940: add             x1, x1, HEAP, lsl #32
    // 0xe78944: r16 = Sentinel
    //     0xe78944: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe78948: cmp             w1, w16
    // 0xe7894c: b.eq            #0xe78b1c
    // 0xe78950: r0 = getValues()
    //     0xe78950: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe78954: LoadField: r1 = r0->field_7
    //     0xe78954: ldur            w1, [x0, #7]
    // 0xe78958: DecompressPointer r1
    //     0xe78958: add             x1, x1, HEAP, lsl #32
    // 0xe7895c: mov             x2, x0
    // 0xe78960: r0 = _GrowableList.of()
    //     0xe78960: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe78964: mov             x3, x0
    // 0xe78968: ldur            x0, [fp, #-0x38]
    // 0xe7896c: stur            x3, [fp, #-0x18]
    // 0xe78970: LoadField: r1 = r0->field_f
    //     0xe78970: ldur            w1, [x0, #0xf]
    // 0xe78974: DecompressPointer r1
    //     0xe78974: add             x1, x1, HEAP, lsl #32
    // 0xe78978: cmp             w1, NULL
    // 0xe7897c: r16 = true
    //     0xe7897c: add             x16, NULL, #0x20  ; true
    // 0xe78980: r17 = false
    //     0xe78980: add             x17, NULL, #0x30  ; false
    // 0xe78984: csel            x4, x16, x17, ne
    // 0xe78988: mov             x2, x0
    // 0xe7898c: stur            x4, [fp, #-0x10]
    // 0xe78990: r1 = Function '<anonymous closure>':.
    //     0xe78990: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a188] AnonymousClosure: (0xe78dc8), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78994: ldr             x1, [x1, #0x188]
    // 0xe78998: r0 = AllocateClosure()
    //     0xe78998: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7899c: r16 = <Encyclopedia>
    //     0xe7899c: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe789a0: ldur            lr, [fp, #-0x18]
    // 0xe789a4: stp             lr, x16, [SP, #0x10]
    // 0xe789a8: ldur            x16, [fp, #-0x10]
    // 0xe789ac: stp             x0, x16, [SP]
    // 0xe789b0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe789b0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe789b4: r0 = ListExtension.optional()
    //     0xe789b4: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe789b8: mov             x1, x0
    // 0xe789bc: ldur            x2, [fp, #-0x38]
    // 0xe789c0: stur            x1, [fp, #-0x10]
    // 0xe789c4: LoadField: r0 = r2->field_13
    //     0xe789c4: ldur            w0, [x2, #0x13]
    // 0xe789c8: DecompressPointer r0
    //     0xe789c8: add             x0, x0, HEAP, lsl #32
    // 0xe789cc: r3 = LoadClassIdInstr(r0)
    //     0xe789cc: ldur            x3, [x0, #-1]
    //     0xe789d0: ubfx            x3, x3, #0xc, #0x14
    // 0xe789d4: r16 = "#"
    //     0xe789d4: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0xe789d8: stp             x16, x0, [SP]
    // 0xe789dc: mov             x0, x3
    // 0xe789e0: mov             lr, x0
    // 0xe789e4: ldr             lr, [x21, lr, lsl #3]
    // 0xe789e8: blr             lr
    // 0xe789ec: eor             x3, x0, #0x10
    // 0xe789f0: ldur            x2, [fp, #-0x38]
    // 0xe789f4: stur            x3, [fp, #-0x18]
    // 0xe789f8: r1 = Function '<anonymous closure>':.
    //     0xe789f8: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a190] AnonymousClosure: (0xe78cd8), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe789fc: ldr             x1, [x1, #0x190]
    // 0xe78a00: r0 = AllocateClosure()
    //     0xe78a00: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78a04: r16 = <Encyclopedia>
    //     0xe78a04: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78a08: ldur            lr, [fp, #-0x10]
    // 0xe78a0c: stp             lr, x16, [SP, #0x10]
    // 0xe78a10: ldur            x16, [fp, #-0x18]
    // 0xe78a14: stp             x0, x16, [SP]
    // 0xe78a18: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe78a18: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe78a1c: r0 = ListExtension.optional()
    //     0xe78a1c: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe78a20: ldur            x2, [fp, #-0x38]
    // 0xe78a24: stur            x0, [fp, #-0x18]
    // 0xe78a28: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xe78a28: ldur            w1, [x2, #0x17]
    // 0xe78a2c: DecompressPointer r1
    //     0xe78a2c: add             x1, x1, HEAP, lsl #32
    // 0xe78a30: LoadField: r3 = r1->field_7
    //     0xe78a30: ldur            w3, [x1, #7]
    // 0xe78a34: cbnz            w3, #0xe78a40
    // 0xe78a38: r4 = false
    //     0xe78a38: add             x4, NULL, #0x30  ; false
    // 0xe78a3c: b               #0xe78a44
    // 0xe78a40: r4 = true
    //     0xe78a40: add             x4, NULL, #0x20  ; true
    // 0xe78a44: stur            x4, [fp, #-0x10]
    // 0xe78a48: r1 = Function '<anonymous closure>':.
    //     0xe78a48: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a198] AnonymousClosure: (0xe78c4c), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78a4c: ldr             x1, [x1, #0x198]
    // 0xe78a50: r0 = AllocateClosure()
    //     0xe78a50: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78a54: r16 = <Encyclopedia>
    //     0xe78a54: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78a58: ldur            lr, [fp, #-0x18]
    // 0xe78a5c: stp             lr, x16, [SP, #0x10]
    // 0xe78a60: ldur            x16, [fp, #-0x10]
    // 0xe78a64: stp             x0, x16, [SP]
    // 0xe78a68: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe78a68: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe78a6c: r0 = ListExtension.optional()
    //     0xe78a6c: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xe78a70: r1 = Function '<anonymous closure>':.
    //     0xe78a70: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a1a0] AnonymousClosure: (0xe78b98), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78a74: ldr             x1, [x1, #0x1a0]
    // 0xe78a78: r2 = Null
    //     0xe78a78: mov             x2, NULL
    // 0xe78a7c: stur            x0, [fp, #-0x10]
    // 0xe78a80: r0 = AllocateClosure()
    //     0xe78a80: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78a84: r16 = <Encyclopedia>
    //     0xe78a84: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78a88: ldur            lr, [fp, #-0x10]
    // 0xe78a8c: stp             lr, x16, [SP, #8]
    // 0xe78a90: str             x0, [SP]
    // 0xe78a94: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78a94: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78a98: r0 = ListExtension.sortBy()
    //     0xe78a98: bl              #0x7ec728  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortBy
    // 0xe78a9c: r16 = <Encyclopedia>
    //     0xe78a9c: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78aa0: stp             x0, x16, [SP, #8]
    // 0xe78aa4: ldur            x0, [fp, #-0x28]
    // 0xe78aa8: str             x0, [SP]
    // 0xe78aac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78aac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78ab0: r0 = ListExtension.paginate()
    //     0xe78ab0: bl              #0xe78b24  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.paginate
    // 0xe78ab4: mov             x3, x0
    // 0xe78ab8: ldur            x0, [fp, #-0x28]
    // 0xe78abc: stur            x3, [fp, #-0x18]
    // 0xe78ac0: cbnz            x0, #0xe78ad0
    // 0xe78ac4: r0 = Instance__$FailureImpl
    //     0xe78ac4: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a1a8] Obj!_$FailureImpl<List<Encyclopedia>>@e0e581
    //     0xe78ac8: ldr             x0, [x0, #0x1a8]
    // 0xe78acc: b               #0xe78ad8
    // 0xe78ad0: r0 = Instance__$SuccessImpl
    //     0xe78ad0: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a1b0] Obj!_$SuccessImpl<List<Encyclopedia>>@e0e5c1
    //     0xe78ad4: ldr             x0, [x0, #0x1b0]
    // 0xe78ad8: stur            x0, [fp, #-0x10]
    // 0xe78adc: r1 = Function '<anonymous closure>':.
    //     0xe78adc: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a1b8] AnonymousClosure: (0xe78b70), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78ae0: ldr             x1, [x1, #0x1b8]
    // 0xe78ae4: r2 = Null
    //     0xe78ae4: mov             x2, NULL
    // 0xe78ae8: r0 = AllocateClosure()
    //     0xe78ae8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78aec: r16 = <Encyclopedia, FutureOr<ApiResult<List<Encyclopedia>>>>
    //     0xe78aec: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1c0] TypeArguments: <Encyclopedia, FutureOr<ApiResult<List<Encyclopedia>>>>
    //     0xe78af0: ldr             x16, [x16, #0x1c0]
    // 0xe78af4: ldur            lr, [fp, #-0x18]
    // 0xe78af8: stp             lr, x16, [SP, #0x10]
    // 0xe78afc: ldur            x16, [fp, #-0x10]
    // 0xe78b00: stp             x16, x0, [SP]
    // 0xe78b04: r4 = const [0x2, 0x3, 0x3, 0x2, orEmpty, 0x2, null]
    //     0xe78b04: add             x4, PP, #0x37, lsl #12  ; [pp+0x37e70] List(7) [0x2, 0x3, 0x3, 0x2, "orEmpty", 0x2, Null]
    //     0xe78b08: ldr             x4, [x4, #0xe70]
    // 0xe78b0c: r0 = ListExtension.to()
    //     0xe78b0c: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe78b10: r0 = ReturnAsync()
    //     0xe78b10: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe78b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78b18: b               #0xe788c4
    // 0xe78b1c: r9 = keystore
    //     0xe78b1c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe78b20: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe78b20: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ApiResult<List<Encyclopedia>> <anonymous closure>(dynamic, List<Encyclopedia>) {
    // ** addr: 0xe78b70, size: 0x28
    // 0xe78b70: EnterFrame
    //     0xe78b70: stp             fp, lr, [SP, #-0x10]!
    //     0xe78b74: mov             fp, SP
    // 0xe78b78: r1 = <List<Encyclopedia>>
    //     0xe78b78: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a50] TypeArguments: <List<Encyclopedia>>
    //     0xe78b7c: ldr             x1, [x1, #0xa50]
    // 0xe78b80: r0 = _$SuccessImpl()
    //     0xe78b80: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe78b84: ldr             x1, [fp, #0x10]
    // 0xe78b88: StoreField: r0->field_b = r1
    //     0xe78b88: stur            w1, [x0, #0xb]
    // 0xe78b8c: LeaveFrame
    //     0xe78b8c: mov             SP, fp
    //     0xe78b90: ldp             fp, lr, [SP], #0x10
    // 0xe78b94: ret
    //     0xe78b94: ret             
  }
  [closure] String <anonymous closure>(dynamic, Encyclopedia) {
    // ** addr: 0xe78b98, size: 0x5c
    // 0xe78b98: EnterFrame
    //     0xe78b98: stp             fp, lr, [SP, #-0x10]!
    //     0xe78b9c: mov             fp, SP
    // 0xe78ba0: AllocStack(0x10)
    //     0xe78ba0: sub             SP, SP, #0x10
    // 0xe78ba4: CheckStackOverflow
    //     0xe78ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78ba8: cmp             SP, x16
    //     0xe78bac: b.ls            #0xe78bec
    // 0xe78bb0: ldr             x0, [fp, #0x10]
    // 0xe78bb4: LoadField: r1 = r0->field_f
    //     0xe78bb4: ldur            w1, [x0, #0xf]
    // 0xe78bb8: DecompressPointer r1
    //     0xe78bb8: add             x1, x1, HEAP, lsl #32
    // 0xe78bbc: stp             xzr, x1, [SP]
    // 0xe78bc0: r0 = []()
    //     0xe78bc0: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xe78bc4: r1 = LoadClassIdInstr(r0)
    //     0xe78bc4: ldur            x1, [x0, #-1]
    //     0xe78bc8: ubfx            x1, x1, #0xc, #0x14
    // 0xe78bcc: str             x0, [SP]
    // 0xe78bd0: mov             x0, x1
    // 0xe78bd4: r0 = GDT[cid_x0 + -0xff6]()
    //     0xe78bd4: sub             lr, x0, #0xff6
    //     0xe78bd8: ldr             lr, [x21, lr, lsl #3]
    //     0xe78bdc: blr             lr
    // 0xe78be0: LeaveFrame
    //     0xe78be0: mov             SP, fp
    //     0xe78be4: ldp             fp, lr, [SP], #0x10
    // 0xe78be8: ret
    //     0xe78be8: ret             
    // 0xe78bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78bec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78bf0: b               #0xe78bb0
  }
  [closure] List<Encyclopedia> <anonymous closure>(dynamic, List<Encyclopedia>) {
    // ** addr: 0xe78c4c, size: 0x8c
    // 0xe78c4c: EnterFrame
    //     0xe78c4c: stp             fp, lr, [SP, #-0x10]!
    //     0xe78c50: mov             fp, SP
    // 0xe78c54: AllocStack(0x28)
    //     0xe78c54: sub             SP, SP, #0x28
    // 0xe78c58: SetupParameters()
    //     0xe78c58: ldr             x0, [fp, #0x18]
    //     0xe78c5c: ldur            w1, [x0, #0x17]
    //     0xe78c60: add             x1, x1, HEAP, lsl #32
    // 0xe78c64: CheckStackOverflow
    //     0xe78c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78c68: cmp             SP, x16
    //     0xe78c6c: b.ls            #0xe78cd0
    // 0xe78c70: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe78c70: ldur            w0, [x1, #0x17]
    // 0xe78c74: DecompressPointer r0
    //     0xe78c74: add             x0, x0, HEAP, lsl #32
    // 0xe78c78: r1 = LoadClassIdInstr(r0)
    //     0xe78c78: ldur            x1, [x0, #-1]
    //     0xe78c7c: ubfx            x1, x1, #0xc, #0x14
    // 0xe78c80: str             x0, [SP]
    // 0xe78c84: mov             x0, x1
    // 0xe78c88: r0 = GDT[cid_x0 + -0xffe]()
    //     0xe78c88: sub             lr, x0, #0xffe
    //     0xe78c8c: ldr             lr, [x21, lr, lsl #3]
    //     0xe78c90: blr             lr
    // 0xe78c94: r1 = Function '<anonymous closure>':.
    //     0xe78c94: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a1c8] AnonymousClosure: (0xbcde5c), in [package:nuonline/app/modules/tutorial/tutorial_list/controllers/tutorial_list_controller.dart] TutorialListController::filter (0xbcdda8)
    //     0xe78c98: ldr             x1, [x1, #0x1c8]
    // 0xe78c9c: r2 = Null
    //     0xe78c9c: mov             x2, NULL
    // 0xe78ca0: stur            x0, [fp, #-8]
    // 0xe78ca4: r0 = AllocateClosure()
    //     0xe78ca4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78ca8: r16 = <Encyclopedia>
    //     0xe78ca8: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78cac: ldr             lr, [fp, #0x10]
    // 0xe78cb0: stp             lr, x16, [SP, #0x10]
    // 0xe78cb4: ldur            x16, [fp, #-8]
    // 0xe78cb8: stp             x0, x16, [SP]
    // 0xe78cbc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe78cbc: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe78cc0: r0 = ListExtension.search()
    //     0xe78cc0: bl              #0xb3f3c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.search
    // 0xe78cc4: LeaveFrame
    //     0xe78cc4: mov             SP, fp
    //     0xe78cc8: ldp             fp, lr, [SP], #0x10
    // 0xe78ccc: ret
    //     0xe78ccc: ret             
    // 0xe78cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78cd4: b               #0xe78c70
  }
  [closure] List<Encyclopedia> <anonymous closure>(dynamic, List<Encyclopedia>) {
    // ** addr: 0xe78cd8, size: 0x5c
    // 0xe78cd8: EnterFrame
    //     0xe78cd8: stp             fp, lr, [SP, #-0x10]!
    //     0xe78cdc: mov             fp, SP
    // 0xe78ce0: AllocStack(0x18)
    //     0xe78ce0: sub             SP, SP, #0x18
    // 0xe78ce4: SetupParameters()
    //     0xe78ce4: ldr             x0, [fp, #0x18]
    //     0xe78ce8: ldur            w2, [x0, #0x17]
    //     0xe78cec: add             x2, x2, HEAP, lsl #32
    // 0xe78cf0: CheckStackOverflow
    //     0xe78cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78cf4: cmp             SP, x16
    //     0xe78cf8: b.ls            #0xe78d2c
    // 0xe78cfc: r1 = Function '<anonymous closure>':.
    //     0xe78cfc: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a1d0] AnonymousClosure: (0xe78d34), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78d00: ldr             x1, [x1, #0x1d0]
    // 0xe78d04: r0 = AllocateClosure()
    //     0xe78d04: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78d08: r16 = <Encyclopedia>
    //     0xe78d08: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78d0c: ldr             lr, [fp, #0x10]
    // 0xe78d10: stp             lr, x16, [SP, #8]
    // 0xe78d14: str             x0, [SP]
    // 0xe78d18: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78d18: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78d1c: r0 = ListExtension.filter()
    //     0xe78d1c: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe78d20: LeaveFrame
    //     0xe78d20: mov             SP, fp
    //     0xe78d24: ldp             fp, lr, [SP], #0x10
    // 0xe78d28: ret
    //     0xe78d28: ret             
    // 0xe78d2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78d2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78d30: b               #0xe78cfc
  }
  [closure] bool <anonymous closure>(dynamic, Encyclopedia) {
    // ** addr: 0xe78d34, size: 0x94
    // 0xe78d34: EnterFrame
    //     0xe78d34: stp             fp, lr, [SP, #-0x10]!
    //     0xe78d38: mov             fp, SP
    // 0xe78d3c: AllocStack(0x18)
    //     0xe78d3c: sub             SP, SP, #0x18
    // 0xe78d40: SetupParameters()
    //     0xe78d40: ldr             x0, [fp, #0x18]
    //     0xe78d44: ldur            w1, [x0, #0x17]
    //     0xe78d48: add             x1, x1, HEAP, lsl #32
    //     0xe78d4c: stur            x1, [fp, #-8]
    // 0xe78d50: CheckStackOverflow
    //     0xe78d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78d54: cmp             SP, x16
    //     0xe78d58: b.ls            #0xe78dc0
    // 0xe78d5c: ldr             x0, [fp, #0x10]
    // 0xe78d60: LoadField: r2 = r0->field_f
    //     0xe78d60: ldur            w2, [x0, #0xf]
    // 0xe78d64: DecompressPointer r2
    //     0xe78d64: add             x2, x2, HEAP, lsl #32
    // 0xe78d68: stp             xzr, x2, [SP]
    // 0xe78d6c: r0 = []()
    //     0xe78d6c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0xe78d70: r1 = LoadClassIdInstr(r0)
    //     0xe78d70: ldur            x1, [x0, #-1]
    //     0xe78d74: ubfx            x1, x1, #0xc, #0x14
    // 0xe78d78: str             x0, [SP]
    // 0xe78d7c: mov             x0, x1
    // 0xe78d80: r0 = GDT[cid_x0 + -0xff6]()
    //     0xe78d80: sub             lr, x0, #0xff6
    //     0xe78d84: ldr             lr, [x21, lr, lsl #3]
    //     0xe78d88: blr             lr
    // 0xe78d8c: mov             x1, x0
    // 0xe78d90: ldur            x0, [fp, #-8]
    // 0xe78d94: LoadField: r2 = r0->field_13
    //     0xe78d94: ldur            w2, [x0, #0x13]
    // 0xe78d98: DecompressPointer r2
    //     0xe78d98: add             x2, x2, HEAP, lsl #32
    // 0xe78d9c: r0 = LoadClassIdInstr(r1)
    //     0xe78d9c: ldur            x0, [x1, #-1]
    //     0xe78da0: ubfx            x0, x0, #0xc, #0x14
    // 0xe78da4: stp             x2, x1, [SP]
    // 0xe78da8: mov             lr, x0
    // 0xe78dac: ldr             lr, [x21, lr, lsl #3]
    // 0xe78db0: blr             lr
    // 0xe78db4: LeaveFrame
    //     0xe78db4: mov             SP, fp
    //     0xe78db8: ldp             fp, lr, [SP], #0x10
    // 0xe78dbc: ret
    //     0xe78dbc: ret             
    // 0xe78dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78dc4: b               #0xe78d5c
  }
  [closure] List<Encyclopedia> <anonymous closure>(dynamic, List<Encyclopedia>) {
    // ** addr: 0xe78dc8, size: 0x5c
    // 0xe78dc8: EnterFrame
    //     0xe78dc8: stp             fp, lr, [SP, #-0x10]!
    //     0xe78dcc: mov             fp, SP
    // 0xe78dd0: AllocStack(0x18)
    //     0xe78dd0: sub             SP, SP, #0x18
    // 0xe78dd4: SetupParameters()
    //     0xe78dd4: ldr             x0, [fp, #0x18]
    //     0xe78dd8: ldur            w2, [x0, #0x17]
    //     0xe78ddc: add             x2, x2, HEAP, lsl #32
    // 0xe78de0: CheckStackOverflow
    //     0xe78de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78de4: cmp             SP, x16
    //     0xe78de8: b.ls            #0xe78e1c
    // 0xe78dec: r1 = Function '<anonymous closure>':.
    //     0xe78dec: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a1d8] AnonymousClosure: (0xe78e24), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAll (0xe78894)
    //     0xe78df0: ldr             x1, [x1, #0x1d8]
    // 0xe78df4: r0 = AllocateClosure()
    //     0xe78df4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78df8: r16 = <Encyclopedia>
    //     0xe78df8: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe78dfc: ldr             lr, [fp, #0x10]
    // 0xe78e00: stp             lr, x16, [SP, #8]
    // 0xe78e04: str             x0, [SP]
    // 0xe78e08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78e08: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78e0c: r0 = ListExtension.filter()
    //     0xe78e0c: bl              #0x81e2c4  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.filter
    // 0xe78e10: LeaveFrame
    //     0xe78e10: mov             SP, fp
    //     0xe78e14: ldp             fp, lr, [SP], #0x10
    // 0xe78e18: ret
    //     0xe78e18: ret             
    // 0xe78e1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78e1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78e20: b               #0xe78dec
  }
  [closure] bool <anonymous closure>(dynamic, Encyclopedia) {
    // ** addr: 0xe78e24, size: 0x58
    // 0xe78e24: ldr             x1, [SP, #8]
    // 0xe78e28: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xe78e28: ldur            w2, [x1, #0x17]
    // 0xe78e2c: DecompressPointer r2
    //     0xe78e2c: add             x2, x2, HEAP, lsl #32
    // 0xe78e30: ldr             x1, [SP]
    // 0xe78e34: LoadField: r3 = r1->field_13
    //     0xe78e34: ldur            w3, [x1, #0x13]
    // 0xe78e38: DecompressPointer r3
    //     0xe78e38: add             x3, x3, HEAP, lsl #32
    // 0xe78e3c: LoadField: r1 = r3->field_7
    //     0xe78e3c: ldur            x1, [x3, #7]
    // 0xe78e40: LoadField: r3 = r2->field_f
    //     0xe78e40: ldur            w3, [x2, #0xf]
    // 0xe78e44: DecompressPointer r3
    //     0xe78e44: add             x3, x3, HEAP, lsl #32
    // 0xe78e48: cmp             w3, NULL
    // 0xe78e4c: b.eq            #0xe78e70
    // 0xe78e50: r2 = LoadInt32Instr(r3)
    //     0xe78e50: sbfx            x2, x3, #1, #0x1f
    //     0xe78e54: tbz             w3, #0, #0xe78e5c
    //     0xe78e58: ldur            x2, [x3, #7]
    // 0xe78e5c: cmp             x1, x2
    // 0xe78e60: r16 = true
    //     0xe78e60: add             x16, NULL, #0x20  ; true
    // 0xe78e64: r17 = false
    //     0xe78e64: add             x17, NULL, #0x30  ; false
    // 0xe78e68: csel            x0, x16, x17, eq
    // 0xe78e6c: ret
    //     0xe78e6c: ret             
    // 0xe78e70: EnterFrame
    //     0xe78e70: stp             fp, lr, [SP, #-0x10]!
    //     0xe78e74: mov             fp, SP
    // 0xe78e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe78e78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findAllCategory(/* No info */) async {
    // ** addr: 0xe78e7c, size: 0xdc
    // 0xe78e7c: EnterFrame
    //     0xe78e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe78e80: mov             fp, SP
    // 0xe78e84: AllocStack(0x28)
    //     0xe78e84: sub             SP, SP, #0x28
    // 0xe78e88: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe78e88: stur            NULL, [fp, #-8]
    //     0xe78e8c: stur            x1, [fp, #-0x10]
    // 0xe78e90: CheckStackOverflow
    //     0xe78e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78e94: cmp             SP, x16
    //     0xe78e98: b.ls            #0xe78f48
    // 0xe78e9c: InitAsync() -> Future<ApiResult<List<EncyclopediaCategory>>>
    //     0xe78e9c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff68] TypeArguments: <ApiResult<List<EncyclopediaCategory>>>
    //     0xe78ea0: ldr             x0, [x0, #0xf68]
    //     0xe78ea4: bl              #0x661298  ; InitAsyncStub
    // 0xe78ea8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe78ea8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe78eac: ldr             x0, [x0, #0x2728]
    //     0xe78eb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78eb4: cmp             w0, w16
    //     0xe78eb8: b.ne            #0xe78ec4
    //     0xe78ebc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe78ec0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe78ec4: r16 = <EncyclopediaCategory>
    //     0xe78ec4: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe78ec8: stp             x0, x16, [SP, #8]
    // 0xe78ecc: r16 = "v2_encyclopedia_categories"
    //     0xe78ecc: ldr             x16, [PP, #0x7c18]  ; [pp+0x7c18] "v2_encyclopedia_categories"
    // 0xe78ed0: str             x16, [SP]
    // 0xe78ed4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe78ed4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe78ed8: r0 = box()
    //     0xe78ed8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe78edc: mov             x1, x0
    // 0xe78ee0: stur            x0, [fp, #-0x10]
    // 0xe78ee4: r0 = checkOpen()
    //     0xe78ee4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe78ee8: ldur            x0, [fp, #-0x10]
    // 0xe78eec: LoadField: r1 = r0->field_1b
    //     0xe78eec: ldur            w1, [x0, #0x1b]
    // 0xe78ef0: DecompressPointer r1
    //     0xe78ef0: add             x1, x1, HEAP, lsl #32
    // 0xe78ef4: r16 = Sentinel
    //     0xe78ef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe78ef8: cmp             w1, w16
    // 0xe78efc: b.eq            #0xe78f50
    // 0xe78f00: r0 = getValues()
    //     0xe78f00: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe78f04: LoadField: r1 = r0->field_7
    //     0xe78f04: ldur            w1, [x0, #7]
    // 0xe78f08: DecompressPointer r1
    //     0xe78f08: add             x1, x1, HEAP, lsl #32
    // 0xe78f0c: mov             x2, x0
    // 0xe78f10: r0 = _GrowableList.of()
    //     0xe78f10: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe78f14: r1 = Function '<anonymous closure>':.
    //     0xe78f14: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a178] AnonymousClosure: (0xe78f58), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAllCategory (0xe78e7c)
    //     0xe78f18: ldr             x1, [x1, #0x178]
    // 0xe78f1c: r2 = Null
    //     0xe78f1c: mov             x2, NULL
    // 0xe78f20: stur            x0, [fp, #-0x10]
    // 0xe78f24: r0 = AllocateClosure()
    //     0xe78f24: bl              #0xec1630  ; AllocateClosureStub
    // 0xe78f28: r16 = <EncyclopediaCategory, FutureOr<ApiResult<List<EncyclopediaCategory>>>>
    //     0xe78f28: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a180] TypeArguments: <EncyclopediaCategory, FutureOr<ApiResult<List<EncyclopediaCategory>>>>
    //     0xe78f2c: ldr             x16, [x16, #0x180]
    // 0xe78f30: ldur            lr, [fp, #-0x10]
    // 0xe78f34: stp             lr, x16, [SP, #8]
    // 0xe78f38: str             x0, [SP]
    // 0xe78f3c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe78f3c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe78f40: r0 = ListExtension.to()
    //     0xe78f40: bl              #0xe77110  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.to
    // 0xe78f44: r0 = ReturnAsync()
    //     0xe78f44: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe78f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78f48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78f4c: b               #0xe78e9c
    // 0xe78f50: r9 = keystore
    //     0xe78f50: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe78f54: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe78f54: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ApiResult<List<EncyclopediaCategory>> <anonymous closure>(dynamic, List<EncyclopediaCategory>) {
    // ** addr: 0xe78f58, size: 0x28
    // 0xe78f58: EnterFrame
    //     0xe78f58: stp             fp, lr, [SP, #-0x10]!
    //     0xe78f5c: mov             fp, SP
    // 0xe78f60: r1 = <List<EncyclopediaCategory>>
    //     0xe78f60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f128] TypeArguments: <List<EncyclopediaCategory>>
    //     0xe78f64: ldr             x1, [x1, #0x128]
    // 0xe78f68: r0 = _$SuccessImpl()
    //     0xe78f68: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe78f6c: ldr             x1, [fp, #0x10]
    // 0xe78f70: StoreField: r0->field_b = r1
    //     0xe78f70: stur            w1, [x0, #0xb]
    // 0xe78f74: LeaveFrame
    //     0xe78f74: mov             SP, fp
    //     0xe78f78: ldp             fp, lr, [SP], #0x10
    // 0xe78f7c: ret
    //     0xe78f7c: ret             
  }
  _ findAllFilter(/* No info */) async {
    // ** addr: 0xe78fb8, size: 0x104
    // 0xe78fb8: EnterFrame
    //     0xe78fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xe78fbc: mov             fp, SP
    // 0xe78fc0: AllocStack(0x28)
    //     0xe78fc0: sub             SP, SP, #0x28
    // 0xe78fc4: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe78fc4: stur            NULL, [fp, #-8]
    //     0xe78fc8: stur            x1, [fp, #-0x10]
    // 0xe78fcc: CheckStackOverflow
    //     0xe78fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78fd0: cmp             SP, x16
    //     0xe78fd4: b.ls            #0xe790ac
    // 0xe78fd8: InitAsync() -> Future<ApiResult<List<String>>>
    //     0xe78fd8: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a160] TypeArguments: <ApiResult<List<String>>>
    //     0xe78fdc: ldr             x0, [x0, #0x160]
    //     0xe78fe0: bl              #0x661298  ; InitAsyncStub
    // 0xe78fe4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe78fe4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe78fe8: ldr             x0, [x0, #0x2728]
    //     0xe78fec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe78ff0: cmp             w0, w16
    //     0xe78ff4: b.ne            #0xe79000
    //     0xe78ff8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe78ffc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe79000: r16 = <Encyclopedia>
    //     0xe79000: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe79004: stp             x0, x16, [SP, #8]
    // 0xe79008: r16 = "v2_encyclopedias"
    //     0xe79008: ldr             x16, [PP, #0x7c20]  ; [pp+0x7c20] "v2_encyclopedias"
    // 0xe7900c: str             x16, [SP]
    // 0xe79010: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79010: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79014: r0 = box()
    //     0xe79014: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe79018: mov             x1, x0
    // 0xe7901c: stur            x0, [fp, #-0x10]
    // 0xe79020: r0 = checkOpen()
    //     0xe79020: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe79024: ldur            x0, [fp, #-0x10]
    // 0xe79028: LoadField: r1 = r0->field_1b
    //     0xe79028: ldur            w1, [x0, #0x1b]
    // 0xe7902c: DecompressPointer r1
    //     0xe7902c: add             x1, x1, HEAP, lsl #32
    // 0xe79030: r16 = Sentinel
    //     0xe79030: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe79034: cmp             w1, w16
    // 0xe79038: b.eq            #0xe790b4
    // 0xe7903c: r0 = getValues()
    //     0xe7903c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe79040: r1 = Function '<anonymous closure>':.
    //     0xe79040: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a168] AnonymousClosure: (0xe790bc), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findAllFilter (0xe78fb8)
    //     0xe79044: ldr             x1, [x1, #0x168]
    // 0xe79048: r2 = Null
    //     0xe79048: mov             x2, NULL
    // 0xe7904c: stur            x0, [fp, #-0x10]
    // 0xe79050: r0 = AllocateClosure()
    //     0xe79050: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79054: r16 = <String>
    //     0xe79054: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe79058: ldur            lr, [fp, #-0x10]
    // 0xe7905c: stp             lr, x16, [SP, #8]
    // 0xe79060: str             x0, [SP]
    // 0xe79064: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79064: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79068: r0 = map()
    //     0xe79068: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xe7906c: mov             x1, x0
    // 0xe79070: r0 = toSet()
    //     0xe79070: bl              #0x863cac  ; [dart:core] _GrowableList::toSet
    // 0xe79074: LoadField: r1 = r0->field_7
    //     0xe79074: ldur            w1, [x0, #7]
    // 0xe79078: DecompressPointer r1
    //     0xe79078: add             x1, x1, HEAP, lsl #32
    // 0xe7907c: mov             x2, x0
    // 0xe79080: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0xe79080: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0xe79084: mov             x1, x0
    // 0xe79088: stur            x0, [fp, #-0x10]
    // 0xe7908c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7908c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe79090: r0 = sort()
    //     0xe79090: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xe79094: r1 = <List<String>>
    //     0xe79094: add             x1, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0xe79098: ldr             x1, [x1, #0xb40]
    // 0xe7909c: r0 = _$SuccessImpl()
    //     0xe7909c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe790a0: ldur            x1, [fp, #-0x10]
    // 0xe790a4: StoreField: r0->field_b = r1
    //     0xe790a4: stur            w1, [x0, #0xb]
    // 0xe790a8: r0 = ReturnAsyncNotFuture()
    //     0xe790a8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe790ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe790ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe790b0: b               #0xe78fd8
    // 0xe790b4: r9 = keystore
    //     0xe790b4: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe790b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe790b8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] String <anonymous closure>(dynamic, Encyclopedia) {
    // ** addr: 0xe790bc, size: 0x30
    // 0xe790bc: EnterFrame
    //     0xe790bc: stp             fp, lr, [SP, #-0x10]!
    //     0xe790c0: mov             fp, SP
    // 0xe790c4: CheckStackOverflow
    //     0xe790c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe790c8: cmp             SP, x16
    //     0xe790cc: b.ls            #0xe790e4
    // 0xe790d0: ldr             x1, [fp, #0x10]
    // 0xe790d4: r0 = prefix()
    //     0xe790d4: bl              #0xe78bf4  ; [package:nuonline/app/data/models/encyclopedia.dart] Encyclopedia::prefix
    // 0xe790d8: LeaveFrame
    //     0xe790d8: mov             SP, fp
    //     0xe790dc: ldp             fp, lr, [SP], #0x10
    // 0xe790e0: ret
    //     0xe790e0: ret             
    // 0xe790e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe790e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe790e8: b               #0xe790d0
  }
  _ updateAllCategory(/* No info */) {
    // ** addr: 0xe79214, size: 0x9c
    // 0xe79214: EnterFrame
    //     0xe79214: stp             fp, lr, [SP, #-0x10]!
    //     0xe79218: mov             fp, SP
    // 0xe7921c: AllocStack(0x28)
    //     0xe7921c: sub             SP, SP, #0x28
    // 0xe79220: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe79220: stur            x2, [fp, #-8]
    // 0xe79224: CheckStackOverflow
    //     0xe79224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79228: cmp             SP, x16
    //     0xe7922c: b.ls            #0xe792a8
    // 0xe79230: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79230: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79234: ldr             x0, [x0, #0x2728]
    //     0xe79238: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7923c: cmp             w0, w16
    //     0xe79240: b.ne            #0xe7924c
    //     0xe79244: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe79248: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7924c: r16 = <EncyclopediaCategory>
    //     0xe7924c: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe79250: stp             x0, x16, [SP, #8]
    // 0xe79254: r16 = "v2_encyclopedia_categories"
    //     0xe79254: ldr             x16, [PP, #0x7c18]  ; [pp+0x7c18] "v2_encyclopedia_categories"
    // 0xe79258: str             x16, [SP]
    // 0xe7925c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7925c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79260: r0 = box()
    //     0xe79260: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe79264: r1 = Function '<anonymous closure>':.
    //     0xe79264: add             x1, PP, #0x51, lsl #12  ; [pp+0x51650] AnonymousClosure: (0xe792b0), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::updateAllCategory (0xe79214)
    //     0xe79268: ldr             x1, [x1, #0x650]
    // 0xe7926c: r2 = Null
    //     0xe7926c: mov             x2, NULL
    // 0xe79270: stur            x0, [fp, #-0x10]
    // 0xe79274: r0 = AllocateClosure()
    //     0xe79274: bl              #0xec1630  ; AllocateClosureStub
    // 0xe79278: ldur            x2, [fp, #-8]
    // 0xe7927c: mov             x3, x0
    // 0xe79280: r1 = <dynamic, EncyclopediaCategory>
    //     0xe79280: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a038] TypeArguments: <dynamic, EncyclopediaCategory>
    //     0xe79284: ldr             x1, [x1, #0x38]
    // 0xe79288: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe79288: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7928c: r0 = LinkedHashMap.fromIterable()
    //     0xe7928c: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xe79290: ldur            x1, [fp, #-0x10]
    // 0xe79294: mov             x2, x0
    // 0xe79298: r0 = putAll()
    //     0xe79298: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xe7929c: LeaveFrame
    //     0xe7929c: mov             SP, fp
    //     0xe792a0: ldp             fp, lr, [SP], #0x10
    // 0xe792a4: ret
    //     0xe792a4: ret             
    // 0xe792a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe792a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe792ac: b               #0xe79230
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe792b0, size: 0x4c
    // 0xe792b0: EnterFrame
    //     0xe792b0: stp             fp, lr, [SP, #-0x10]!
    //     0xe792b4: mov             fp, SP
    // 0xe792b8: AllocStack(0x8)
    //     0xe792b8: sub             SP, SP, #8
    // 0xe792bc: CheckStackOverflow
    //     0xe792bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe792c0: cmp             SP, x16
    //     0xe792c4: b.ls            #0xe792f4
    // 0xe792c8: ldr             x16, [fp, #0x10]
    // 0xe792cc: str             x16, [SP]
    // 0xe792d0: r4 = 0
    //     0xe792d0: movz            x4, #0
    // 0xe792d4: ldr             x0, [SP]
    // 0xe792d8: r16 = UnlinkedCall_0x5f3c08
    //     0xe792d8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51658] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xe792dc: add             x16, x16, #0x658
    // 0xe792e0: ldp             x5, lr, [x16]
    // 0xe792e4: blr             lr
    // 0xe792e8: LeaveFrame
    //     0xe792e8: mov             SP, fp
    //     0xe792ec: ldp             fp, lr, [SP], #0x10
    // 0xe792f0: ret
    //     0xe792f0: ret             
    // 0xe792f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe792f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe792f8: b               #0xe792c8
  }
  _ findLastUpdated(/* No info */) async {
    // ** addr: 0xe79608, size: 0xe8
    // 0xe79608: EnterFrame
    //     0xe79608: stp             fp, lr, [SP, #-0x10]!
    //     0xe7960c: mov             fp, SP
    // 0xe79610: AllocStack(0x28)
    //     0xe79610: sub             SP, SP, #0x28
    // 0xe79614: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe79614: stur            NULL, [fp, #-8]
    //     0xe79618: stur            x1, [fp, #-0x10]
    // 0xe7961c: CheckStackOverflow
    //     0xe7961c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79620: cmp             SP, x16
    //     0xe79624: b.ls            #0xe796e0
    // 0xe79628: InitAsync() -> Future<Encyclopedia?>
    //     0xe79628: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a130] TypeArguments: <Encyclopedia?>
    //     0xe7962c: ldr             x0, [x0, #0x130]
    //     0xe79630: bl              #0x661298  ; InitAsyncStub
    // 0xe79634: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79634: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79638: ldr             x0, [x0, #0x2728]
    //     0xe7963c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe79640: cmp             w0, w16
    //     0xe79644: b.ne            #0xe79650
    //     0xe79648: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7964c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe79650: r16 = <Encyclopedia>
    //     0xe79650: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe79654: stp             x0, x16, [SP, #8]
    // 0xe79658: r16 = "v2_encyclopedias"
    //     0xe79658: ldr             x16, [PP, #0x7c20]  ; [pp+0x7c20] "v2_encyclopedias"
    // 0xe7965c: str             x16, [SP]
    // 0xe79660: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79660: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79664: r0 = box()
    //     0xe79664: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe79668: mov             x1, x0
    // 0xe7966c: stur            x0, [fp, #-0x10]
    // 0xe79670: r0 = checkOpen()
    //     0xe79670: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe79674: ldur            x0, [fp, #-0x10]
    // 0xe79678: LoadField: r1 = r0->field_1b
    //     0xe79678: ldur            w1, [x0, #0x1b]
    // 0xe7967c: DecompressPointer r1
    //     0xe7967c: add             x1, x1, HEAP, lsl #32
    // 0xe79680: r16 = Sentinel
    //     0xe79680: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe79684: cmp             w1, w16
    // 0xe79688: b.eq            #0xe796e8
    // 0xe7968c: r0 = getValues()
    //     0xe7968c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe79690: LoadField: r1 = r0->field_7
    //     0xe79690: ldur            w1, [x0, #7]
    // 0xe79694: DecompressPointer r1
    //     0xe79694: add             x1, x1, HEAP, lsl #32
    // 0xe79698: mov             x2, x0
    // 0xe7969c: r0 = _GrowableList.of()
    //     0xe7969c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe796a0: r1 = Function '<anonymous closure>':.
    //     0xe796a0: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a138] AnonymousClosure: (0xe796f0), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findLastUpdated (0xe79608)
    //     0xe796a4: ldr             x1, [x1, #0x138]
    // 0xe796a8: r2 = Null
    //     0xe796a8: mov             x2, NULL
    // 0xe796ac: stur            x0, [fp, #-0x10]
    // 0xe796b0: r0 = AllocateClosure()
    //     0xe796b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe796b4: r16 = <Encyclopedia>
    //     0xe796b4: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe796b8: ldur            lr, [fp, #-0x10]
    // 0xe796bc: stp             lr, x16, [SP, #8]
    // 0xe796c0: str             x0, [SP]
    // 0xe796c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe796c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe796c8: r0 = ListExtension.sortByDesc()
    //     0xe796c8: bl              #0xe78010  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortByDesc
    // 0xe796cc: r16 = <Encyclopedia>
    //     0xe796cc: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe796d0: stp             x0, x16, [SP]
    // 0xe796d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe796d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe796d8: r0 = ListExtension.firstOrNull()
    //     0xe796d8: bl              #0xe77fd0  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.firstOrNull
    // 0xe796dc: r0 = ReturnAsync()
    //     0xe796dc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe796e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe796e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe796e4: b               #0xe79628
    // 0xe796e8: r9 = keystore
    //     0xe796e8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe796ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe796ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DateTime <anonymous closure>(dynamic, Encyclopedia) {
    // ** addr: 0xe796f0, size: 0x38
    // 0xe796f0: EnterFrame
    //     0xe796f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe796f4: mov             fp, SP
    // 0xe796f8: CheckStackOverflow
    //     0xe796f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe796fc: cmp             SP, x16
    //     0xe79700: b.ls            #0xe79720
    // 0xe79704: ldr             x0, [fp, #0x10]
    // 0xe79708: LoadField: r1 = r0->field_1f
    //     0xe79708: ldur            w1, [x0, #0x1f]
    // 0xe7970c: DecompressPointer r1
    //     0xe7970c: add             x1, x1, HEAP, lsl #32
    // 0xe79710: r0 = parse()
    //     0xe79710: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0xe79714: LeaveFrame
    //     0xe79714: mov             SP, fp
    //     0xe79718: ldp             fp, lr, [SP], #0x10
    // 0xe7971c: ret
    //     0xe7971c: ret             
    // 0xe79720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79724: b               #0xe79704
  }
  _ findLastUpdatedCategory(/* No info */) async {
    // ** addr: 0xe79728, size: 0xe8
    // 0xe79728: EnterFrame
    //     0xe79728: stp             fp, lr, [SP, #-0x10]!
    //     0xe7972c: mov             fp, SP
    // 0xe79730: AllocStack(0x28)
    //     0xe79730: sub             SP, SP, #0x28
    // 0xe79734: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe79734: stur            NULL, [fp, #-8]
    //     0xe79738: stur            x1, [fp, #-0x10]
    // 0xe7973c: CheckStackOverflow
    //     0xe7973c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79740: cmp             SP, x16
    //     0xe79744: b.ls            #0xe79800
    // 0xe79748: InitAsync() -> Future<EncyclopediaCategory?>
    //     0xe79748: add             x0, PP, #0x51, lsl #12  ; [pp+0x51640] TypeArguments: <EncyclopediaCategory?>
    //     0xe7974c: ldr             x0, [x0, #0x640]
    //     0xe79750: bl              #0x661298  ; InitAsyncStub
    // 0xe79754: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe79754: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe79758: ldr             x0, [x0, #0x2728]
    //     0xe7975c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe79760: cmp             w0, w16
    //     0xe79764: b.ne            #0xe79770
    //     0xe79768: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7976c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe79770: r16 = <EncyclopediaCategory>
    //     0xe79770: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe79774: stp             x0, x16, [SP, #8]
    // 0xe79778: r16 = "v2_encyclopedia_categories"
    //     0xe79778: ldr             x16, [PP, #0x7c18]  ; [pp+0x7c18] "v2_encyclopedia_categories"
    // 0xe7977c: str             x16, [SP]
    // 0xe79780: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe79780: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe79784: r0 = box()
    //     0xe79784: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe79788: mov             x1, x0
    // 0xe7978c: stur            x0, [fp, #-0x10]
    // 0xe79790: r0 = checkOpen()
    //     0xe79790: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe79794: ldur            x0, [fp, #-0x10]
    // 0xe79798: LoadField: r1 = r0->field_1b
    //     0xe79798: ldur            w1, [x0, #0x1b]
    // 0xe7979c: DecompressPointer r1
    //     0xe7979c: add             x1, x1, HEAP, lsl #32
    // 0xe797a0: r16 = Sentinel
    //     0xe797a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe797a4: cmp             w1, w16
    // 0xe797a8: b.eq            #0xe79808
    // 0xe797ac: r0 = getValues()
    //     0xe797ac: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe797b0: LoadField: r1 = r0->field_7
    //     0xe797b0: ldur            w1, [x0, #7]
    // 0xe797b4: DecompressPointer r1
    //     0xe797b4: add             x1, x1, HEAP, lsl #32
    // 0xe797b8: mov             x2, x0
    // 0xe797bc: r0 = _GrowableList.of()
    //     0xe797bc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe797c0: r1 = Function '<anonymous closure>':.
    //     0xe797c0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51648] AnonymousClosure: (0xe79810), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::findLastUpdatedCategory (0xe79728)
    //     0xe797c4: ldr             x1, [x1, #0x648]
    // 0xe797c8: r2 = Null
    //     0xe797c8: mov             x2, NULL
    // 0xe797cc: stur            x0, [fp, #-0x10]
    // 0xe797d0: r0 = AllocateClosure()
    //     0xe797d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe797d4: r16 = <EncyclopediaCategory>
    //     0xe797d4: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe797d8: ldur            lr, [fp, #-0x10]
    // 0xe797dc: stp             lr, x16, [SP, #8]
    // 0xe797e0: str             x0, [SP]
    // 0xe797e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe797e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe797e8: r0 = ListExtension.sortByDesc()
    //     0xe797e8: bl              #0xe78010  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortByDesc
    // 0xe797ec: r16 = <EncyclopediaCategory>
    //     0xe797ec: ldr             x16, [PP, #0x7b90]  ; [pp+0x7b90] TypeArguments: <EncyclopediaCategory>
    // 0xe797f0: stp             x0, x16, [SP]
    // 0xe797f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe797f4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe797f8: r0 = ListExtension.firstOrNull()
    //     0xe797f8: bl              #0xe77fd0  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.firstOrNull
    // 0xe797fc: r0 = ReturnAsync()
    //     0xe797fc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe79800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79804: b               #0xe79748
    // 0xe79808: r9 = keystore
    //     0xe79808: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7980c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7980c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] DateTime <anonymous closure>(dynamic, EncyclopediaCategory) {
    // ** addr: 0xe79810, size: 0x38
    // 0xe79810: EnterFrame
    //     0xe79810: stp             fp, lr, [SP, #-0x10]!
    //     0xe79814: mov             fp, SP
    // 0xe79818: CheckStackOverflow
    //     0xe79818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7981c: cmp             SP, x16
    //     0xe79820: b.ls            #0xe79840
    // 0xe79824: ldr             x0, [fp, #0x10]
    // 0xe79828: LoadField: r1 = r0->field_1b
    //     0xe79828: ldur            w1, [x0, #0x1b]
    // 0xe7982c: DecompressPointer r1
    //     0xe7982c: add             x1, x1, HEAP, lsl #32
    // 0xe79830: r0 = parse()
    //     0xe79830: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0xe79834: LeaveFrame
    //     0xe79834: mov             SP, fp
    //     0xe79838: ldp             fp, lr, [SP], #0x10
    // 0xe7983c: ret
    //     0xe7983c: ret             
    // 0xe79840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79844: b               #0xe79824
  }
  _ findById(/* No info */) async {
    // ** addr: 0xe7a800, size: 0xc0
    // 0xe7a800: EnterFrame
    //     0xe7a800: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a804: mov             fp, SP
    // 0xe7a808: AllocStack(0x30)
    //     0xe7a808: sub             SP, SP, #0x30
    // 0xe7a80c: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe7a80c: stur            NULL, [fp, #-8]
    //     0xe7a810: stur            x1, [fp, #-0x10]
    //     0xe7a814: stur            x2, [fp, #-0x18]
    // 0xe7a818: CheckStackOverflow
    //     0xe7a818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a81c: cmp             SP, x16
    //     0xe7a820: b.ls            #0xe7a8b8
    // 0xe7a824: InitAsync() -> Future<ApiResult<Encyclopedia>>
    //     0xe7a824: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff58] TypeArguments: <ApiResult<Encyclopedia>>
    //     0xe7a828: ldr             x0, [x0, #0xf58]
    //     0xe7a82c: bl              #0x661298  ; InitAsyncStub
    // 0xe7a830: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7a830: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7a834: ldr             x0, [x0, #0x2728]
    //     0xe7a838: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7a83c: cmp             w0, w16
    //     0xe7a840: b.ne            #0xe7a84c
    //     0xe7a844: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7a848: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7a84c: r16 = <Encyclopedia>
    //     0xe7a84c: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe7a850: stp             x0, x16, [SP, #8]
    // 0xe7a854: r16 = "v2_encyclopedias"
    //     0xe7a854: ldr             x16, [PP, #0x7c20]  ; [pp+0x7c20] "v2_encyclopedias"
    // 0xe7a858: str             x16, [SP]
    // 0xe7a85c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a85c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a860: r0 = box()
    //     0xe7a860: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7a864: mov             x3, x0
    // 0xe7a868: ldur            x2, [fp, #-0x18]
    // 0xe7a86c: r0 = BoxInt64Instr(r2)
    //     0xe7a86c: sbfiz           x0, x2, #1, #0x1f
    //     0xe7a870: cmp             x2, x0, asr #1
    //     0xe7a874: b.eq            #0xe7a880
    //     0xe7a878: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7a87c: stur            x2, [x0, #7]
    // 0xe7a880: mov             x1, x3
    // 0xe7a884: mov             x2, x0
    // 0xe7a888: r0 = get()
    //     0xe7a888: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0xe7a88c: stur            x0, [fp, #-0x10]
    // 0xe7a890: cmp             w0, NULL
    // 0xe7a894: b.ne            #0xe7a8a4
    // 0xe7a898: r0 = Instance__$FailureImpl
    //     0xe7a898: add             x0, PP, #0x4a, lsl #12  ; [pp+0x4a170] Obj!_$FailureImpl<Encyclopedia>@e0e5b1
    //     0xe7a89c: ldr             x0, [x0, #0x170]
    // 0xe7a8a0: r0 = ReturnAsyncNotFuture()
    //     0xe7a8a0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7a8a4: r1 = <Encyclopedia>
    //     0xe7a8a4: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe7a8a8: r0 = _$SuccessImpl()
    //     0xe7a8a8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7a8ac: ldur            x1, [fp, #-0x10]
    // 0xe7a8b0: StoreField: r0->field_b = r1
    //     0xe7a8b0: stur            w1, [x0, #0xb]
    // 0xe7a8b4: r0 = ReturnAsyncNotFuture()
    //     0xe7a8b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7a8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a8bc: b               #0xe7a824
  }
  _ updateAll(/* No info */) async {
    // ** addr: 0xe7a8c0, size: 0xa4
    // 0xe7a8c0: EnterFrame
    //     0xe7a8c0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a8c4: mov             fp, SP
    // 0xe7a8c8: AllocStack(0x30)
    //     0xe7a8c8: sub             SP, SP, #0x30
    // 0xe7a8cc: SetupParameters(EncyclopediaLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe7a8cc: stur            NULL, [fp, #-8]
    //     0xe7a8d0: stur            x1, [fp, #-0x10]
    //     0xe7a8d4: stur            x2, [fp, #-0x18]
    // 0xe7a8d8: CheckStackOverflow
    //     0xe7a8d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a8dc: cmp             SP, x16
    //     0xe7a8e0: b.ls            #0xe7a95c
    // 0xe7a8e4: InitAsync() -> Future<void?>
    //     0xe7a8e4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe7a8e8: bl              #0x661298  ; InitAsyncStub
    // 0xe7a8ec: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7a8ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7a8f0: ldr             x0, [x0, #0x2728]
    //     0xe7a8f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7a8f8: cmp             w0, w16
    //     0xe7a8fc: b.ne            #0xe7a908
    //     0xe7a900: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7a904: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7a908: r16 = <Encyclopedia>
    //     0xe7a908: ldr             x16, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe7a90c: stp             x0, x16, [SP, #8]
    // 0xe7a910: r16 = "v2_encyclopedias"
    //     0xe7a910: ldr             x16, [PP, #0x7c20]  ; [pp+0x7c20] "v2_encyclopedias"
    // 0xe7a914: str             x16, [SP]
    // 0xe7a918: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7a918: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7a91c: r0 = box()
    //     0xe7a91c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7a920: r1 = Function '<anonymous closure>':.
    //     0xe7a920: add             x1, PP, #0x4a, lsl #12  ; [pp+0x4a148] AnonymousClosure: (0xe7a964), in [package:nuonline/app/data/repositories/encyclopedia/encyclopedia_local_repository.dart] EncyclopediaLocalRepository::updateAll (0xe7a8c0)
    //     0xe7a924: ldr             x1, [x1, #0x148]
    // 0xe7a928: r2 = Null
    //     0xe7a928: mov             x2, NULL
    // 0xe7a92c: stur            x0, [fp, #-0x10]
    // 0xe7a930: r0 = AllocateClosure()
    //     0xe7a930: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7a934: ldur            x2, [fp, #-0x18]
    // 0xe7a938: mov             x3, x0
    // 0xe7a93c: r1 = <dynamic, Encyclopedia>
    //     0xe7a93c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19ee8] TypeArguments: <dynamic, Encyclopedia>
    //     0xe7a940: ldr             x1, [x1, #0xee8]
    // 0xe7a944: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe7a944: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe7a948: r0 = LinkedHashMap.fromIterable()
    //     0xe7a948: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xe7a94c: ldur            x1, [fp, #-0x10]
    // 0xe7a950: mov             x2, x0
    // 0xe7a954: r0 = putAll()
    //     0xe7a954: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xe7a958: r0 = ReturnAsync()
    //     0xe7a958: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7a95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a960: b               #0xe7a8e4
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7a964, size: 0x48
    // 0xe7a964: EnterFrame
    //     0xe7a964: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a968: mov             fp, SP
    // 0xe7a96c: AllocStack(0x8)
    //     0xe7a96c: sub             SP, SP, #8
    // 0xe7a970: CheckStackOverflow
    //     0xe7a970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a974: cmp             SP, x16
    //     0xe7a978: b.ls            #0xe7a9a4
    // 0xe7a97c: ldr             x16, [fp, #0x10]
    // 0xe7a980: str             x16, [SP]
    // 0xe7a984: r4 = 0
    //     0xe7a984: movz            x4, #0
    // 0xe7a988: ldr             x0, [SP]
    // 0xe7a98c: r5 = UnlinkedCall_0x5f3c08
    //     0xe7a98c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a150] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xe7a990: ldp             x5, lr, [x16, #0x150]
    // 0xe7a994: blr             lr
    // 0xe7a998: LeaveFrame
    //     0xe7a998: mov             SP, fp
    //     0xe7a99c: ldp             fp, lr, [SP], #0x10
    // 0xe7a9a0: ret
    //     0xe7a9a0: ret             
    // 0xe7a9a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7a9a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7a9a8: b               #0xe7a97c
  }
}
