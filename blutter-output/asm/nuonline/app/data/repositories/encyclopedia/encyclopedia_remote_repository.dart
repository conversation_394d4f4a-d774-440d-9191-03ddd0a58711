// lib: , url: package:nuonline/app/data/repositories/encyclopedia/encyclopedia_remote_repository.dart

// class id: 1050079, size: 0x8
class :: {
}

// class id: 1096, size: 0xc, field offset: 0x8
class EncyclopediaRemoteRepository extends Object
    implements EncyclopediaRepository {

  static _ find(/* No info */) {
    // ** addr: 0x8120e8, size: 0x64
    // 0x8120e8: EnterFrame
    //     0x8120e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8120ec: mov             fp, SP
    // 0x8120f0: AllocStack(0x10)
    //     0x8120f0: sub             SP, SP, #0x10
    // 0x8120f4: CheckStackOverflow
    //     0x8120f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8120f8: cmp             SP, x16
    //     0x8120fc: b.ls            #0x812144
    // 0x812100: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x812100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x812104: ldr             x0, [x0, #0x2670]
    //     0x812108: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x81210c: cmp             w0, w16
    //     0x812110: b.ne            #0x81211c
    //     0x812114: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x812118: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x81211c: r16 = <EncyclopediaRepository>
    //     0x81211c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10120] TypeArguments: <EncyclopediaRepository>
    //     0x812120: ldr             x16, [x16, #0x120]
    // 0x812124: r30 = "encyclopedia_remote_repo"
    //     0x812124: add             lr, PP, #0x10, lsl #12  ; [pp+0x10180] "encyclopedia_remote_repo"
    //     0x812128: ldr             lr, [lr, #0x180]
    // 0x81212c: stp             lr, x16, [SP]
    // 0x812130: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x812130: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x812134: r0 = Inst.find()
    //     0x812134: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x812138: LeaveFrame
    //     0x812138: mov             SP, fp
    //     0x81213c: ldp             fp, lr, [SP], #0x10
    // 0x812140: ret
    //     0x812140: ret             
    // 0x812144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x812144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x812148: b               #0x812100
  }
  _ findAllByLastUpdated(/* No info */) async {
    // ** addr: 0xe790ec, size: 0xf4
    // 0xe790ec: EnterFrame
    //     0xe790ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe790f0: mov             fp, SP
    // 0xe790f4: AllocStack(0x88)
    //     0xe790f4: sub             SP, SP, #0x88
    // 0xe790f8: SetupParameters(EncyclopediaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe790f8: stur            NULL, [fp, #-8]
    //     0xe790fc: stur            x1, [fp, #-0x58]
    //     0xe79100: stur            x2, [fp, #-0x60]
    // 0xe79104: CheckStackOverflow
    //     0xe79104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79108: cmp             SP, x16
    //     0xe7910c: b.ls            #0xe791d8
    // 0xe79110: InitAsync() -> Future<ApiResult<List<Encyclopedia>>>
    //     0xe79110: add             x0, PP, #0x47, lsl #12  ; [pp+0x47aa8] TypeArguments: <ApiResult<List<Encyclopedia>>>
    //     0xe79114: ldr             x0, [x0, #0xaa8]
    //     0xe79118: bl              #0x661298  ; InitAsyncStub
    // 0xe7911c: ldur            x1, [fp, #-0x58]
    // 0xe79120: ldur            x0, [fp, #-0x60]
    // 0xe79124: LoadField: r3 = r1->field_7
    //     0xe79124: ldur            w3, [x1, #7]
    // 0xe79128: DecompressPointer r3
    //     0xe79128: add             x3, x3, HEAP, lsl #32
    // 0xe7912c: stur            x3, [fp, #-0x68]
    // 0xe79130: r1 = Null
    //     0xe79130: mov             x1, NULL
    // 0xe79134: r2 = 4
    //     0xe79134: movz            x2, #0x4
    // 0xe79138: r0 = AllocateArray()
    //     0xe79138: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7913c: r16 = "last_updated_at"
    //     0xe7913c: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1e8] "last_updated_at"
    //     0xe79140: ldr             x16, [x16, #0x1e8]
    // 0xe79144: StoreField: r0->field_f = r16
    //     0xe79144: stur            w16, [x0, #0xf]
    // 0xe79148: ldur            x1, [fp, #-0x60]
    // 0xe7914c: StoreField: r0->field_13 = r1
    //     0xe7914c: stur            w1, [x0, #0x13]
    // 0xe79150: r16 = <String, dynamic>
    //     0xe79150: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe79154: stp             x0, x16, [SP]
    // 0xe79158: r0 = Map._fromLiteral()
    //     0xe79158: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe7915c: ldur            x16, [fp, #-0x68]
    // 0xe79160: stp             x16, NULL, [SP, #0x10]
    // 0xe79164: r16 = "/encyclopedia/latest"
    //     0xe79164: add             x16, PP, #0x51, lsl #12  ; [pp+0x51638] "/encyclopedia/latest"
    //     0xe79168: ldr             x16, [x16, #0x638]
    // 0xe7916c: stp             x0, x16, [SP]
    // 0xe79170: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe79170: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe79174: ldr             x4, [x4, #0x2f0]
    // 0xe79178: r0 = get()
    //     0xe79178: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7917c: mov             x1, x0
    // 0xe79180: stur            x1, [fp, #-0x58]
    // 0xe79184: r0 = Await()
    //     0xe79184: bl              #0x661044  ; AwaitStub
    // 0xe79188: r0 = fromResponse()
    //     0xe79188: bl              #0xe791e0  ; [package:nuonline/app/data/models/encyclopedia.dart] Encyclopedia::fromResponse
    // 0xe7918c: r1 = <List<Encyclopedia>>
    //     0xe7918c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a50] TypeArguments: <List<Encyclopedia>>
    //     0xe79190: ldr             x1, [x1, #0xa50]
    // 0xe79194: stur            x0, [fp, #-0x58]
    // 0xe79198: r0 = _$SuccessImpl()
    //     0xe79198: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7919c: mov             x1, x0
    // 0xe791a0: ldur            x0, [fp, #-0x58]
    // 0xe791a4: StoreField: r1->field_b = r0
    //     0xe791a4: stur            w0, [x1, #0xb]
    // 0xe791a8: mov             x0, x1
    // 0xe791ac: r0 = ReturnAsyncNotFuture()
    //     0xe791ac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe791b0: sub             SP, fp, #0x88
    // 0xe791b4: mov             x1, x0
    // 0xe791b8: r0 = getDioException()
    //     0xe791b8: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe791bc: r1 = <List<Encyclopedia>>
    //     0xe791bc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a50] TypeArguments: <List<Encyclopedia>>
    //     0xe791c0: ldr             x1, [x1, #0xa50]
    // 0xe791c4: stur            x0, [fp, #-0x58]
    // 0xe791c8: r0 = _$FailureImpl()
    //     0xe791c8: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe791cc: ldur            x1, [fp, #-0x58]
    // 0xe791d0: StoreField: r0->field_b = r1
    //     0xe791d0: stur            w1, [x0, #0xb]
    // 0xe791d4: r0 = ReturnAsyncNotFuture()
    //     0xe791d4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe791d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe791d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe791dc: b               #0xe79110
  }
  _ findAllCategoryByLastUpdated(/* No info */) async {
    // ** addr: 0xe792fc, size: 0x13c
    // 0xe792fc: EnterFrame
    //     0xe792fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe79300: mov             fp, SP
    // 0xe79304: AllocStack(0x90)
    //     0xe79304: sub             SP, SP, #0x90
    // 0xe79308: SetupParameters(EncyclopediaRemoteRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0xe79308: stur            NULL, [fp, #-8]
    //     0xe7930c: stur            x1, [fp, #-0x60]
    //     0xe79310: stur            x2, [fp, #-0x68]
    // 0xe79314: CheckStackOverflow
    //     0xe79314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe79318: cmp             SP, x16
    //     0xe7931c: b.ls            #0xe79430
    // 0xe79320: InitAsync() -> Future<ApiResult<List<EncyclopediaCategory>>>
    //     0xe79320: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff68] TypeArguments: <ApiResult<List<EncyclopediaCategory>>>
    //     0xe79324: ldr             x0, [x0, #0xf68]
    //     0xe79328: bl              #0x661298  ; InitAsyncStub
    // 0xe7932c: ldur            x1, [fp, #-0x60]
    // 0xe79330: ldur            x0, [fp, #-0x68]
    // 0xe79334: LoadField: r3 = r1->field_7
    //     0xe79334: ldur            w3, [x1, #7]
    // 0xe79338: DecompressPointer r3
    //     0xe79338: add             x3, x3, HEAP, lsl #32
    // 0xe7933c: stur            x3, [fp, #-0x70]
    // 0xe79340: r1 = Null
    //     0xe79340: mov             x1, NULL
    // 0xe79344: r2 = 4
    //     0xe79344: movz            x2, #0x4
    // 0xe79348: r0 = AllocateArray()
    //     0xe79348: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7934c: r16 = "/encyclopedia/categories"
    //     0xe7934c: add             x16, PP, #0x51, lsl #12  ; [pp+0x515f8] "/encyclopedia/categories"
    //     0xe79350: ldr             x16, [x16, #0x5f8]
    // 0xe79354: StoreField: r0->field_f = r16
    //     0xe79354: stur            w16, [x0, #0xf]
    // 0xe79358: ldur            x1, [fp, #-0x68]
    // 0xe7935c: LoadField: r2 = r1->field_7
    //     0xe7935c: ldur            w2, [x1, #7]
    // 0xe79360: cbnz            w2, #0xe7936c
    // 0xe79364: r2 = ""
    //     0xe79364: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe79368: b               #0xe79374
    // 0xe7936c: r2 = "/latest"
    //     0xe7936c: add             x2, PP, #0x51, lsl #12  ; [pp+0x51600] "/latest"
    //     0xe79370: ldr             x2, [x2, #0x600]
    // 0xe79374: StoreField: r0->field_13 = r2
    //     0xe79374: stur            w2, [x0, #0x13]
    // 0xe79378: str             x0, [SP]
    // 0xe7937c: r0 = _interpolate()
    //     0xe7937c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe79380: r1 = Null
    //     0xe79380: mov             x1, NULL
    // 0xe79384: r2 = 4
    //     0xe79384: movz            x2, #0x4
    // 0xe79388: stur            x0, [fp, #-0x60]
    // 0xe7938c: r0 = AllocateArray()
    //     0xe7938c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe79390: r16 = "last_updated_at"
    //     0xe79390: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a1e8] "last_updated_at"
    //     0xe79394: ldr             x16, [x16, #0x1e8]
    // 0xe79398: StoreField: r0->field_f = r16
    //     0xe79398: stur            w16, [x0, #0xf]
    // 0xe7939c: ldur            x1, [fp, #-0x68]
    // 0xe793a0: StoreField: r0->field_13 = r1
    //     0xe793a0: stur            w1, [x0, #0x13]
    // 0xe793a4: r16 = <String, dynamic>
    //     0xe793a4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe793a8: stp             x0, x16, [SP]
    // 0xe793ac: r0 = Map._fromLiteral()
    //     0xe793ac: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe793b0: ldur            x16, [fp, #-0x70]
    // 0xe793b4: stp             x16, NULL, [SP, #0x10]
    // 0xe793b8: ldur            x16, [fp, #-0x60]
    // 0xe793bc: stp             x0, x16, [SP]
    // 0xe793c0: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe793c0: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe793c4: ldr             x4, [x4, #0x2f0]
    // 0xe793c8: r0 = get()
    //     0xe793c8: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe793cc: mov             x1, x0
    // 0xe793d0: stur            x1, [fp, #-0x60]
    // 0xe793d4: r0 = Await()
    //     0xe793d4: bl              #0x661044  ; AwaitStub
    // 0xe793d8: LoadField: r1 = r0->field_b
    //     0xe793d8: ldur            w1, [x0, #0xb]
    // 0xe793dc: DecompressPointer r1
    //     0xe793dc: add             x1, x1, HEAP, lsl #32
    // 0xe793e0: r0 = fromResponse()
    //     0xe793e0: bl              #0xe79438  ; [package:nuonline/app/data/models/encyclopedia.dart] EncyclopediaCategory::fromResponse
    // 0xe793e4: r1 = <List<EncyclopediaCategory>>
    //     0xe793e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f128] TypeArguments: <List<EncyclopediaCategory>>
    //     0xe793e8: ldr             x1, [x1, #0x128]
    // 0xe793ec: stur            x0, [fp, #-0x60]
    // 0xe793f0: r0 = _$SuccessImpl()
    //     0xe793f0: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe793f4: mov             x1, x0
    // 0xe793f8: ldur            x0, [fp, #-0x60]
    // 0xe793fc: StoreField: r1->field_b = r0
    //     0xe793fc: stur            w0, [x1, #0xb]
    // 0xe79400: mov             x0, x1
    // 0xe79404: r0 = ReturnAsyncNotFuture()
    //     0xe79404: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79408: sub             SP, fp, #0x90
    // 0xe7940c: mov             x1, x0
    // 0xe79410: r0 = getDioException()
    //     0xe79410: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe79414: r1 = <List<EncyclopediaCategory>>
    //     0xe79414: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f128] TypeArguments: <List<EncyclopediaCategory>>
    //     0xe79418: ldr             x1, [x1, #0x128]
    // 0xe7941c: stur            x0, [fp, #-0x60]
    // 0xe79420: r0 = _$FailureImpl()
    //     0xe79420: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe79424: ldur            x1, [fp, #-0x60]
    // 0xe79428: StoreField: r0->field_b = r1
    //     0xe79428: stur            w1, [x0, #0xb]
    // 0xe7942c: r0 = ReturnAsyncNotFuture()
    //     0xe7942c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe79430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe79430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe79434: b               #0xe79320
  }
  _ findById(/* No info */) async {
    // ** addr: 0xe7a9ac, size: 0x124
    // 0xe7a9ac: EnterFrame
    //     0xe7a9ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a9b0: mov             fp, SP
    // 0xe7a9b4: AllocStack(0x80)
    //     0xe7a9b4: sub             SP, SP, #0x80
    // 0xe7a9b8: SetupParameters(EncyclopediaRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe7a9b8: stur            NULL, [fp, #-8]
    //     0xe7a9bc: stur            x1, [fp, #-0x58]
    //     0xe7a9c0: stur            x2, [fp, #-0x60]
    // 0xe7a9c4: CheckStackOverflow
    //     0xe7a9c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a9c8: cmp             SP, x16
    //     0xe7a9cc: b.ls            #0xe7aac8
    // 0xe7a9d0: InitAsync() -> Future<ApiResult<Encyclopedia>>
    //     0xe7a9d0: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3ff58] TypeArguments: <ApiResult<Encyclopedia>>
    //     0xe7a9d4: ldr             x0, [x0, #0xf58]
    //     0xe7a9d8: bl              #0x661298  ; InitAsyncStub
    // 0xe7a9dc: ldur            x1, [fp, #-0x58]
    // 0xe7a9e0: ldur            x0, [fp, #-0x60]
    // 0xe7a9e4: LoadField: r3 = r1->field_7
    //     0xe7a9e4: ldur            w3, [x1, #7]
    // 0xe7a9e8: DecompressPointer r3
    //     0xe7a9e8: add             x3, x3, HEAP, lsl #32
    // 0xe7a9ec: stur            x3, [fp, #-0x68]
    // 0xe7a9f0: r1 = Null
    //     0xe7a9f0: mov             x1, NULL
    // 0xe7a9f4: r2 = 4
    //     0xe7a9f4: movz            x2, #0x4
    // 0xe7a9f8: r0 = AllocateArray()
    //     0xe7a9f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7a9fc: mov             x2, x0
    // 0xe7aa00: r16 = "/encyclopedia/"
    //     0xe7aa00: add             x16, PP, #0x4a, lsl #12  ; [pp+0x4a118] "/encyclopedia/"
    //     0xe7aa04: ldr             x16, [x16, #0x118]
    // 0xe7aa08: StoreField: r2->field_f = r16
    //     0xe7aa08: stur            w16, [x2, #0xf]
    // 0xe7aa0c: ldur            x3, [fp, #-0x60]
    // 0xe7aa10: r0 = BoxInt64Instr(r3)
    //     0xe7aa10: sbfiz           x0, x3, #1, #0x1f
    //     0xe7aa14: cmp             x3, x0, asr #1
    //     0xe7aa18: b.eq            #0xe7aa24
    //     0xe7aa1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7aa20: stur            x3, [x0, #7]
    // 0xe7aa24: StoreField: r2->field_13 = r0
    //     0xe7aa24: stur            w0, [x2, #0x13]
    // 0xe7aa28: str             x2, [SP]
    // 0xe7aa2c: r0 = _interpolate()
    //     0xe7aa2c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7aa30: ldur            x16, [fp, #-0x68]
    // 0xe7aa34: stp             x16, NULL, [SP, #8]
    // 0xe7aa38: str             x0, [SP]
    // 0xe7aa3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7aa3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7aa40: r0 = get()
    //     0xe7aa40: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7aa44: mov             x1, x0
    // 0xe7aa48: stur            x1, [fp, #-0x58]
    // 0xe7aa4c: r0 = Await()
    //     0xe7aa4c: bl              #0x661044  ; AwaitStub
    // 0xe7aa50: LoadField: r3 = r0->field_b
    //     0xe7aa50: ldur            w3, [x0, #0xb]
    // 0xe7aa54: DecompressPointer r3
    //     0xe7aa54: add             x3, x3, HEAP, lsl #32
    // 0xe7aa58: mov             x0, x3
    // 0xe7aa5c: stur            x3, [fp, #-0x58]
    // 0xe7aa60: r2 = Null
    //     0xe7aa60: mov             x2, NULL
    // 0xe7aa64: r1 = Null
    //     0xe7aa64: mov             x1, NULL
    // 0xe7aa68: r8 = Map<String, dynamic>
    //     0xe7aa68: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7aa6c: r3 = Null
    //     0xe7aa6c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a120] Null
    //     0xe7aa70: ldr             x3, [x3, #0x120]
    // 0xe7aa74: r0 = Map<String, dynamic>()
    //     0xe7aa74: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7aa78: ldur            x2, [fp, #-0x58]
    // 0xe7aa7c: r1 = Null
    //     0xe7aa7c: mov             x1, NULL
    // 0xe7aa80: r0 = Encyclopedia.fromMap()
    //     0xe7aa80: bl              #0xdc8fe8  ; [package:nuonline/app/data/models/encyclopedia.dart] Encyclopedia::Encyclopedia.fromMap
    // 0xe7aa84: r1 = <Encyclopedia>
    //     0xe7aa84: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe7aa88: stur            x0, [fp, #-0x58]
    // 0xe7aa8c: r0 = _$SuccessImpl()
    //     0xe7aa8c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7aa90: mov             x1, x0
    // 0xe7aa94: ldur            x0, [fp, #-0x58]
    // 0xe7aa98: StoreField: r1->field_b = r0
    //     0xe7aa98: stur            w0, [x1, #0xb]
    // 0xe7aa9c: mov             x0, x1
    // 0xe7aaa0: r0 = ReturnAsyncNotFuture()
    //     0xe7aaa0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7aaa4: sub             SP, fp, #0x80
    // 0xe7aaa8: mov             x1, x0
    // 0xe7aaac: r0 = getDioException()
    //     0xe7aaac: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7aab0: r1 = <Encyclopedia>
    //     0xe7aab0: ldr             x1, [PP, #0x7b98]  ; [pp+0x7b98] TypeArguments: <Encyclopedia>
    // 0xe7aab4: stur            x0, [fp, #-0x58]
    // 0xe7aab8: r0 = _$FailureImpl()
    //     0xe7aab8: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7aabc: ldur            x1, [fp, #-0x58]
    // 0xe7aac0: StoreField: r0->field_b = r1
    //     0xe7aac0: stur            w1, [x0, #0xb]
    // 0xe7aac4: r0 = ReturnAsyncNotFuture()
    //     0xe7aac4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7aac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7aac8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7aacc: b               #0xe7a9d0
  }
}
