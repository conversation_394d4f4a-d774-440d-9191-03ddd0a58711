// lib: , url: package:nuonline/app/data/repositories/event_repository.dart

// class id: 1050081, size: 0x8
class :: {
}

// class id: 1095, size: 0x10, field offset: 0x8
class EventRepository extends Object {

  _ get(/* No info */) async {
    // ** addr: 0x81f494, size: 0x15b8
    // 0x81f494: EnterFrame
    //     0x81f494: stp             fp, lr, [SP, #-0x10]!
    //     0x81f498: mov             fp, SP
    // 0x81f49c: AllocStack(0xe8)
    //     0x81f49c: sub             SP, SP, #0xe8
    // 0x81f4a0: SetupParameters(EventRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x81f4a0: stur            NULL, [fp, #-8]
    //     0x81f4a4: stur            x1, [fp, #-0x10]
    //     0x81f4a8: stur            x2, [fp, #-0x18]
    // 0x81f4ac: CheckStackOverflow
    //     0x81f4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81f4b0: cmp             SP, x16
    //     0x81f4b4: b.ls            #0x8209cc
    // 0x81f4b8: r1 = 4
    //     0x81f4b8: movz            x1, #0x4
    // 0x81f4bc: r0 = AllocateContext()
    //     0x81f4bc: bl              #0xec126c  ; AllocateContextStub
    // 0x81f4c0: mov             x2, x0
    // 0x81f4c4: ldur            x1, [fp, #-0x10]
    // 0x81f4c8: stur            x2, [fp, #-0x20]
    // 0x81f4cc: StoreField: r2->field_f = r1
    //     0x81f4cc: stur            w1, [x2, #0xf]
    // 0x81f4d0: ldur            x0, [fp, #-0x18]
    // 0x81f4d4: StoreField: r2->field_13 = r0
    //     0x81f4d4: stur            w0, [x2, #0x13]
    // 0x81f4d8: InitAsync() -> Future<List<Event>>
    //     0x81f4d8: add             x0, PP, #9, lsl #12  ; [pp+0x90c0] TypeArguments: <List<Event>>
    //     0x81f4dc: ldr             x0, [x0, #0xc0]
    //     0x81f4e0: bl              #0x661298  ; InitAsyncStub
    // 0x81f4e4: ldur            x0, [fp, #-0x10]
    // 0x81f4e8: LoadField: r1 = r0->field_7
    //     0x81f4e8: ldur            w1, [x0, #7]
    // 0x81f4ec: DecompressPointer r1
    //     0x81f4ec: add             x1, x1, HEAP, lsl #32
    // 0x81f4f0: r16 = <List>
    //     0x81f4f0: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x81f4f4: stp             x1, x16, [SP, #8]
    // 0x81f4f8: r16 = "calendar.json"
    //     0x81f4f8: add             x16, PP, #9, lsl #12  ; [pp+0x9278] "calendar.json"
    //     0x81f4fc: ldr             x16, [x16, #0x278]
    // 0x81f500: str             x16, [SP]
    // 0x81f504: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x81f504: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x81f508: r0 = read()
    //     0x81f508: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x81f50c: mov             x1, x0
    // 0x81f510: stur            x1, [fp, #-0x18]
    // 0x81f514: r0 = Await()
    //     0x81f514: bl              #0x661044  ; AwaitStub
    // 0x81f518: mov             x2, x0
    // 0x81f51c: ldur            x0, [fp, #-0x10]
    // 0x81f520: stur            x2, [fp, #-0x28]
    // 0x81f524: LoadField: r3 = r0->field_b
    //     0x81f524: ldur            w3, [x0, #0xb]
    // 0x81f528: DecompressPointer r3
    //     0x81f528: add             x3, x3, HEAP, lsl #32
    // 0x81f52c: ldur            x4, [fp, #-0x20]
    // 0x81f530: stur            x3, [fp, #-0x18]
    // 0x81f534: LoadField: r1 = r4->field_13
    //     0x81f534: ldur            w1, [x4, #0x13]
    // 0x81f538: DecompressPointer r1
    //     0x81f538: add             x1, x1, HEAP, lsl #32
    // 0x81f53c: r0 = LoadClassIdInstr(r1)
    //     0x81f53c: ldur            x0, [x1, #-1]
    //     0x81f540: ubfx            x0, x0, #0xc, #0x14
    // 0x81f544: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81f544: sub             lr, x0, #0xff6
    //     0x81f548: ldr             lr, [x21, lr, lsl #3]
    //     0x81f54c: blr             lr
    // 0x81f550: mov             x3, x0
    // 0x81f554: ldur            x2, [fp, #-0x20]
    // 0x81f558: stur            x3, [fp, #-0x30]
    // 0x81f55c: LoadField: r1 = r2->field_13
    //     0x81f55c: ldur            w1, [x2, #0x13]
    // 0x81f560: DecompressPointer r1
    //     0x81f560: add             x1, x1, HEAP, lsl #32
    // 0x81f564: r0 = LoadClassIdInstr(r1)
    //     0x81f564: ldur            x0, [x1, #-1]
    //     0x81f568: ubfx            x0, x0, #0xc, #0x14
    // 0x81f56c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81f56c: sub             lr, x0, #0xfff
    //     0x81f570: ldr             lr, [x21, lr, lsl #3]
    //     0x81f574: blr             lr
    // 0x81f578: mov             x2, x0
    // 0x81f57c: r0 = BoxInt64Instr(r2)
    //     0x81f57c: sbfiz           x0, x2, #1, #0x1f
    //     0x81f580: cmp             x2, x0, asr #1
    //     0x81f584: b.eq            #0x81f590
    //     0x81f588: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81f58c: stur            x2, [x0, #7]
    // 0x81f590: stur            x0, [fp, #-0x10]
    // 0x81f594: r0 = DateTime()
    //     0x81f594: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81f598: stur            x0, [fp, #-0x38]
    // 0x81f59c: ldur            x16, [fp, #-0x10]
    // 0x81f5a0: stp             xzr, x16, [SP]
    // 0x81f5a4: mov             x1, x0
    // 0x81f5a8: ldur            x2, [fp, #-0x30]
    // 0x81f5ac: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x81f5ac: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x81f5b0: ldr             x4, [x4, #0xe00]
    // 0x81f5b4: r0 = DateTime()
    //     0x81f5b4: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x81f5b8: ldur            x1, [fp, #-0x18]
    // 0x81f5bc: ldur            x2, [fp, #-0x38]
    // 0x81f5c0: r0 = from()
    //     0x81f5c0: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0x81f5c4: mov             x3, x0
    // 0x81f5c8: ldur            x2, [fp, #-0x20]
    // 0x81f5cc: stur            x3, [fp, #-0x10]
    // 0x81f5d0: ArrayStore: r2[0] = r0  ; List_4
    //     0x81f5d0: stur            w0, [x2, #0x17]
    //     0x81f5d4: ldurb           w16, [x2, #-1]
    //     0x81f5d8: ldurb           w17, [x0, #-1]
    //     0x81f5dc: and             x16, x17, x16, lsr #2
    //     0x81f5e0: tst             x16, HEAP, lsr #32
    //     0x81f5e4: b.eq            #0x81f5ec
    //     0x81f5e8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x81f5ec: LoadField: r1 = r2->field_13
    //     0x81f5ec: ldur            w1, [x2, #0x13]
    // 0x81f5f0: DecompressPointer r1
    //     0x81f5f0: add             x1, x1, HEAP, lsl #32
    // 0x81f5f4: r0 = LoadClassIdInstr(r1)
    //     0x81f5f4: ldur            x0, [x1, #-1]
    //     0x81f5f8: ubfx            x0, x0, #0xc, #0x14
    // 0x81f5fc: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81f5fc: sub             lr, x0, #0xfff
    //     0x81f600: ldr             lr, [x21, lr, lsl #3]
    //     0x81f604: blr             lr
    // 0x81f608: cmp             x0, #0xc
    // 0x81f60c: b.ne            #0x81f664
    // 0x81f610: ldur            x2, [fp, #-0x20]
    // 0x81f614: LoadField: r1 = r2->field_13
    //     0x81f614: ldur            w1, [x2, #0x13]
    // 0x81f618: DecompressPointer r1
    //     0x81f618: add             x1, x1, HEAP, lsl #32
    // 0x81f61c: r0 = LoadClassIdInstr(r1)
    //     0x81f61c: ldur            x0, [x1, #-1]
    //     0x81f620: ubfx            x0, x0, #0xc, #0x14
    // 0x81f624: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81f624: sub             lr, x0, #0xff6
    //     0x81f628: ldr             lr, [x21, lr, lsl #3]
    //     0x81f62c: blr             lr
    // 0x81f630: add             x2, x0, #1
    // 0x81f634: stur            x2, [fp, #-0x30]
    // 0x81f638: r0 = DateTime()
    //     0x81f638: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81f63c: stur            x0, [fp, #-0x38]
    // 0x81f640: r16 = 2
    //     0x81f640: movz            x16, #0x2
    // 0x81f644: stp             xzr, x16, [SP]
    // 0x81f648: mov             x1, x0
    // 0x81f64c: ldur            x2, [fp, #-0x30]
    // 0x81f650: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x81f650: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x81f654: ldr             x4, [x4, #0xe00]
    // 0x81f658: r0 = DateTime()
    //     0x81f658: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x81f65c: ldur            x2, [fp, #-0x38]
    // 0x81f660: b               #0x81f6f4
    // 0x81f664: ldur            x2, [fp, #-0x20]
    // 0x81f668: LoadField: r1 = r2->field_13
    //     0x81f668: ldur            w1, [x2, #0x13]
    // 0x81f66c: DecompressPointer r1
    //     0x81f66c: add             x1, x1, HEAP, lsl #32
    // 0x81f670: r0 = LoadClassIdInstr(r1)
    //     0x81f670: ldur            x0, [x1, #-1]
    //     0x81f674: ubfx            x0, x0, #0xc, #0x14
    // 0x81f678: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81f678: sub             lr, x0, #0xff6
    //     0x81f67c: ldr             lr, [x21, lr, lsl #3]
    //     0x81f680: blr             lr
    // 0x81f684: mov             x3, x0
    // 0x81f688: ldur            x2, [fp, #-0x20]
    // 0x81f68c: stur            x3, [fp, #-0x30]
    // 0x81f690: LoadField: r1 = r2->field_13
    //     0x81f690: ldur            w1, [x2, #0x13]
    // 0x81f694: DecompressPointer r1
    //     0x81f694: add             x1, x1, HEAP, lsl #32
    // 0x81f698: r0 = LoadClassIdInstr(r1)
    //     0x81f698: ldur            x0, [x1, #-1]
    //     0x81f69c: ubfx            x0, x0, #0xc, #0x14
    // 0x81f6a0: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81f6a0: sub             lr, x0, #0xfff
    //     0x81f6a4: ldr             lr, [x21, lr, lsl #3]
    //     0x81f6a8: blr             lr
    // 0x81f6ac: add             x2, x0, #1
    // 0x81f6b0: r0 = BoxInt64Instr(r2)
    //     0x81f6b0: sbfiz           x0, x2, #1, #0x1f
    //     0x81f6b4: cmp             x2, x0, asr #1
    //     0x81f6b8: b.eq            #0x81f6c4
    //     0x81f6bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x81f6c0: stur            x2, [x0, #7]
    // 0x81f6c4: stur            x0, [fp, #-0x38]
    // 0x81f6c8: r0 = DateTime()
    //     0x81f6c8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81f6cc: stur            x0, [fp, #-0x40]
    // 0x81f6d0: ldur            x16, [fp, #-0x38]
    // 0x81f6d4: r30 = 2
    //     0x81f6d4: movz            lr, #0x2
    // 0x81f6d8: stp             lr, x16, [SP]
    // 0x81f6dc: mov             x1, x0
    // 0x81f6e0: ldur            x2, [fp, #-0x30]
    // 0x81f6e4: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x81f6e4: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x81f6e8: ldr             x4, [x4, #0xe00]
    // 0x81f6ec: r0 = DateTime()
    //     0x81f6ec: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x81f6f0: ldur            x2, [fp, #-0x40]
    // 0x81f6f4: ldur            x0, [fp, #-0x20]
    // 0x81f6f8: ldur            x3, [fp, #-0x28]
    // 0x81f6fc: ldur            x4, [fp, #-0x18]
    // 0x81f700: mov             x1, x4
    // 0x81f704: r0 = from()
    //     0x81f704: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0x81f708: mov             x4, x0
    // 0x81f70c: ldur            x3, [fp, #-0x20]
    // 0x81f710: stur            x4, [fp, #-0x38]
    // 0x81f714: StoreField: r3->field_1b = r0
    //     0x81f714: stur            w0, [x3, #0x1b]
    //     0x81f718: ldurb           w16, [x3, #-1]
    //     0x81f71c: ldurb           w17, [x0, #-1]
    //     0x81f720: and             x16, x17, x16, lsr #2
    //     0x81f724: tst             x16, HEAP, lsr #32
    //     0x81f728: b.eq            #0x81f730
    //     0x81f72c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x81f730: r1 = Function '<anonymous closure>':.
    //     0x81f730: add             x1, PP, #9, lsl #12  ; [pp+0x9280] AnonymousClosure: (0x821620), in [package:nuonline/app/data/repositories/event_repository.dart] EventRepository::get (0x81f494)
    //     0x81f734: ldr             x1, [x1, #0x280]
    // 0x81f738: r2 = Null
    //     0x81f738: mov             x2, NULL
    // 0x81f73c: r0 = AllocateClosure()
    //     0x81f73c: bl              #0xec1630  ; AllocateClosureStub
    // 0x81f740: mov             x1, x0
    // 0x81f744: ldur            x0, [fp, #-0x28]
    // 0x81f748: r2 = LoadClassIdInstr(r0)
    //     0x81f748: ldur            x2, [x0, #-1]
    //     0x81f74c: ubfx            x2, x2, #0xc, #0x14
    // 0x81f750: r16 = <EventConfig>
    //     0x81f750: add             x16, PP, #9, lsl #12  ; [pp+0x9288] TypeArguments: <EventConfig>
    //     0x81f754: ldr             x16, [x16, #0x288]
    // 0x81f758: stp             x0, x16, [SP, #8]
    // 0x81f75c: str             x1, [SP]
    // 0x81f760: mov             x0, x2
    // 0x81f764: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x81f764: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x81f768: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x81f768: movz            x17, #0xf28c
    //     0x81f76c: add             lr, x0, x17
    //     0x81f770: ldr             lr, [x21, lr, lsl #3]
    //     0x81f774: blr             lr
    // 0x81f778: ldur            x2, [fp, #-0x20]
    // 0x81f77c: r1 = Function '<anonymous closure>':.
    //     0x81f77c: add             x1, PP, #9, lsl #12  ; [pp+0x9290] AnonymousClosure: (0x8210c0), in [package:nuonline/app/data/repositories/event_repository.dart] EventRepository::get (0x81f494)
    //     0x81f780: ldr             x1, [x1, #0x290]
    // 0x81f784: stur            x0, [fp, #-0x28]
    // 0x81f788: r0 = AllocateClosure()
    //     0x81f788: bl              #0xec1630  ; AllocateClosureStub
    // 0x81f78c: ldur            x1, [fp, #-0x28]
    // 0x81f790: r2 = LoadClassIdInstr(r1)
    //     0x81f790: ldur            x2, [x1, #-1]
    //     0x81f794: ubfx            x2, x2, #0xc, #0x14
    // 0x81f798: mov             x16, x0
    // 0x81f79c: mov             x0, x2
    // 0x81f7a0: mov             x2, x16
    // 0x81f7a4: r0 = GDT[cid_x0 + 0xea28]()
    //     0x81f7a4: movz            x17, #0xea28
    //     0x81f7a8: add             lr, x0, x17
    //     0x81f7ac: ldr             lr, [x21, lr, lsl #3]
    //     0x81f7b0: blr             lr
    // 0x81f7b4: mov             x1, x0
    // 0x81f7b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x81f7b8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x81f7bc: r0 = toList()
    //     0x81f7bc: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0x81f7c0: r1 = <Event>
    //     0x81f7c0: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x81f7c4: ldr             x1, [x1, #0xd0]
    // 0x81f7c8: r2 = 0
    //     0x81f7c8: movz            x2, #0
    // 0x81f7cc: stur            x0, [fp, #-0x28]
    // 0x81f7d0: r0 = _GrowableList()
    //     0x81f7d0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x81f7d4: r1 = <String>
    //     0x81f7d4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x81f7d8: r2 = "1/10"
    //     0x81f7d8: add             x2, PP, #9, lsl #12  ; [pp+0x9298] "1/10"
    //     0x81f7dc: ldr             x2, [x2, #0x298]
    // 0x81f7e0: r3 = "10/12"
    //     0x81f7e0: add             x3, PP, #9, lsl #12  ; [pp+0x92a0] "10/12"
    //     0x81f7e4: ldr             x3, [x3, #0x2a0]
    // 0x81f7e8: r5 = "11/12"
    //     0x81f7e8: add             x5, PP, #9, lsl #12  ; [pp+0x92a8] "11/12"
    //     0x81f7ec: ldr             x5, [x5, #0x2a8]
    // 0x81f7f0: r6 = "12/12"
    //     0x81f7f0: add             x6, PP, #9, lsl #12  ; [pp+0x92b0] "12/12"
    //     0x81f7f4: ldr             x6, [x6, #0x2b0]
    // 0x81f7f8: r7 = "13/12"
    //     0x81f7f8: add             x7, PP, #9, lsl #12  ; [pp+0x92b8] "13/12"
    //     0x81f7fc: ldr             x7, [x7, #0x2b8]
    // 0x81f800: stur            x0, [fp, #-0x40]
    // 0x81f804: r0 = _GrowableList._literal5()
    //     0x81f804: bl              #0x820fe4  ; [dart:core] _GrowableList::_GrowableList._literal5
    // 0x81f808: mov             x4, x0
    // 0x81f80c: ldur            x3, [fp, #-0x28]
    // 0x81f810: stur            x4, [fp, #-0x68]
    // 0x81f814: LoadField: r5 = r3->field_7
    //     0x81f814: ldur            w5, [x3, #7]
    // 0x81f818: DecompressPointer r5
    //     0x81f818: add             x5, x5, HEAP, lsl #32
    // 0x81f81c: stur            x5, [fp, #-0x60]
    // 0x81f820: LoadField: r0 = r3->field_b
    //     0x81f820: ldur            w0, [x3, #0xb]
    // 0x81f824: r6 = LoadInt32Instr(r0)
    //     0x81f824: sbfx            x6, x0, #1, #0x1f
    // 0x81f828: ldur            x7, [fp, #-0x18]
    // 0x81f82c: stur            x6, [fp, #-0x58]
    // 0x81f830: LoadField: r8 = r7->field_23
    //     0x81f830: ldur            w8, [x7, #0x23]
    // 0x81f834: DecompressPointer r8
    //     0x81f834: add             x8, x8, HEAP, lsl #32
    // 0x81f838: stur            x8, [fp, #-0x50]
    // 0x81f83c: r0 = 0
    //     0x81f83c: movz            x0, #0
    // 0x81f840: ldur            x10, [fp, #-0x20]
    // 0x81f844: ldur            x12, [fp, #-0x10]
    // 0x81f848: ldur            x11, [fp, #-0x38]
    // 0x81f84c: ldur            x9, [fp, #-0x40]
    // 0x81f850: CheckStackOverflow
    //     0x81f850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81f854: cmp             SP, x16
    //     0x81f858: b.ls            #0x8209d4
    // 0x81f85c: LoadField: r1 = r3->field_b
    //     0x81f85c: ldur            w1, [x3, #0xb]
    // 0x81f860: r2 = LoadInt32Instr(r1)
    //     0x81f860: sbfx            x2, x1, #1, #0x1f
    // 0x81f864: cmp             x6, x2
    // 0x81f868: b.ne            #0x8209ac
    // 0x81f86c: cmp             x0, x2
    // 0x81f870: b.ge            #0x820944
    // 0x81f874: LoadField: r1 = r3->field_f
    //     0x81f874: ldur            w1, [x3, #0xf]
    // 0x81f878: DecompressPointer r1
    //     0x81f878: add             x1, x1, HEAP, lsl #32
    // 0x81f87c: ArrayLoad: r13 = r1[r0]  ; Unknown_4
    //     0x81f87c: add             x16, x1, x0, lsl #2
    //     0x81f880: ldur            w13, [x16, #0xf]
    // 0x81f884: DecompressPointer r13
    //     0x81f884: add             x13, x13, HEAP, lsl #32
    // 0x81f888: stur            x13, [fp, #-0x48]
    // 0x81f88c: add             x14, x0, #1
    // 0x81f890: stur            x14, [fp, #-0x30]
    // 0x81f894: cmp             w13, NULL
    // 0x81f898: b.ne            #0x81f8cc
    // 0x81f89c: mov             x0, x13
    // 0x81f8a0: mov             x2, x5
    // 0x81f8a4: r1 = Null
    //     0x81f8a4: mov             x1, NULL
    // 0x81f8a8: cmp             w2, NULL
    // 0x81f8ac: b.eq            #0x81f8cc
    // 0x81f8b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x81f8b0: ldur            w4, [x2, #0x17]
    // 0x81f8b4: DecompressPointer r4
    //     0x81f8b4: add             x4, x4, HEAP, lsl #32
    // 0x81f8b8: r8 = X0
    //     0x81f8b8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x81f8bc: LoadField: r9 = r4->field_7
    //     0x81f8bc: ldur            x9, [x4, #7]
    // 0x81f8c0: r3 = Null
    //     0x81f8c0: add             x3, PP, #9, lsl #12  ; [pp+0x92c0] Null
    //     0x81f8c4: ldr             x3, [x3, #0x2c0]
    // 0x81f8c8: blr             x9
    // 0x81f8cc: ldur            x0, [fp, #-0x48]
    // 0x81f8d0: r0 = _getCurrentMicros()
    //     0x81f8d0: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x81f8d4: ldur            x2, [fp, #-0x48]
    // 0x81f8d8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x81f8d8: ldur            w0, [x2, #0x17]
    // 0x81f8dc: DecompressPointer r0
    //     0x81f8dc: add             x0, x0, HEAP, lsl #32
    // 0x81f8e0: stur            x0, [fp, #-0xa0]
    // 0x81f8e4: LoadField: r1 = r2->field_1f
    //     0x81f8e4: ldur            w1, [x2, #0x1f]
    // 0x81f8e8: DecompressPointer r1
    //     0x81f8e8: add             x1, x1, HEAP, lsl #32
    // 0x81f8ec: stur            x1, [fp, #-0x98]
    // 0x81f8f0: LoadField: r3 = r2->field_23
    //     0x81f8f0: ldur            w3, [x2, #0x23]
    // 0x81f8f4: DecompressPointer r3
    //     0x81f8f4: add             x3, x3, HEAP, lsl #32
    // 0x81f8f8: stur            x3, [fp, #-0x90]
    // 0x81f8fc: LoadField: r4 = r2->field_27
    //     0x81f8fc: ldur            w4, [x2, #0x27]
    // 0x81f900: DecompressPointer r4
    //     0x81f900: add             x4, x4, HEAP, lsl #32
    // 0x81f904: stur            x4, [fp, #-0x88]
    // 0x81f908: LoadField: r5 = r2->field_1b
    //     0x81f908: ldur            w5, [x2, #0x1b]
    // 0x81f90c: DecompressPointer r5
    //     0x81f90c: add             x5, x5, HEAP, lsl #32
    // 0x81f910: LoadField: r6 = r5->field_7
    //     0x81f910: ldur            x6, [x5, #7]
    // 0x81f914: cmp             x6, #2
    // 0x81f918: b.gt            #0x81fd60
    // 0x81f91c: cmp             x6, #1
    // 0x81f920: b.gt            #0x81fbcc
    // 0x81f924: cmp             x6, #0
    // 0x81f928: b.gt            #0x81fa70
    // 0x81f92c: ldur            x5, [fp, #-0x40]
    // 0x81f930: LoadField: r6 = r2->field_7
    //     0x81f930: ldur            w6, [x2, #7]
    // 0x81f934: DecompressPointer r6
    //     0x81f934: add             x6, x6, HEAP, lsl #32
    // 0x81f938: cmp             w6, NULL
    // 0x81f93c: b.eq            #0x8209dc
    // 0x81f940: LoadField: r7 = r2->field_b
    //     0x81f940: ldur            w7, [x2, #0xb]
    // 0x81f944: DecompressPointer r7
    //     0x81f944: add             x7, x7, HEAP, lsl #32
    // 0x81f948: cmp             w7, NULL
    // 0x81f94c: b.eq            #0x8209e0
    // 0x81f950: LoadField: r8 = r2->field_f
    //     0x81f950: ldur            w8, [x2, #0xf]
    // 0x81f954: DecompressPointer r8
    //     0x81f954: add             x8, x8, HEAP, lsl #32
    // 0x81f958: cmp             w8, NULL
    // 0x81f95c: b.eq            #0x8209e4
    // 0x81f960: r2 = LoadInt32Instr(r6)
    //     0x81f960: sbfx            x2, x6, #1, #0x1f
    //     0x81f964: tbz             w6, #0, #0x81f96c
    //     0x81f968: ldur            x2, [x6, #7]
    // 0x81f96c: stur            x2, [fp, #-0x80]
    // 0x81f970: r6 = LoadInt32Instr(r7)
    //     0x81f970: sbfx            x6, x7, #1, #0x1f
    //     0x81f974: tbz             w7, #0, #0x81f97c
    //     0x81f978: ldur            x6, [x7, #7]
    // 0x81f97c: stur            x6, [fp, #-0x78]
    // 0x81f980: r7 = LoadInt32Instr(r8)
    //     0x81f980: sbfx            x7, x8, #1, #0x1f
    //     0x81f984: tbz             w8, #0, #0x81f98c
    //     0x81f988: ldur            x7, [x8, #7]
    // 0x81f98c: stur            x7, [fp, #-0x70]
    // 0x81f990: r0 = DateTime()
    //     0x81f990: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81f994: stur            x0, [fp, #-0xa8]
    // 0x81f998: stp             xzr, xzr, [SP, #0x10]
    // 0x81f99c: r16 = false
    //     0x81f99c: add             x16, NULL, #0x30  ; false
    // 0x81f9a0: stp             x16, xzr, [SP]
    // 0x81f9a4: mov             x1, x0
    // 0x81f9a8: ldur            x2, [fp, #-0x80]
    // 0x81f9ac: ldur            x3, [fp, #-0x78]
    // 0x81f9b0: ldur            x5, [fp, #-0x70]
    // 0x81f9b4: r6 = 0
    //     0x81f9b4: movz            x6, #0
    // 0x81f9b8: r7 = 0
    //     0x81f9b8: movz            x7, #0
    // 0x81f9bc: r0 = DateTime._internal()
    //     0x81f9bc: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x81f9c0: r0 = Event()
    //     0x81f9c0: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x81f9c4: mov             x2, x0
    // 0x81f9c8: ldur            x0, [fp, #-0xa8]
    // 0x81f9cc: stur            x2, [fp, #-0xb0]
    // 0x81f9d0: StoreField: r2->field_7 = r0
    //     0x81f9d0: stur            w0, [x2, #7]
    // 0x81f9d4: ldur            x3, [fp, #-0xa0]
    // 0x81f9d8: StoreField: r2->field_b = r3
    //     0x81f9d8: stur            w3, [x2, #0xb]
    // 0x81f9dc: ldur            x4, [fp, #-0x98]
    // 0x81f9e0: StoreField: r2->field_f = r4
    //     0x81f9e0: stur            w4, [x2, #0xf]
    // 0x81f9e4: ldur            x5, [fp, #-0x90]
    // 0x81f9e8: StoreField: r2->field_13 = r5
    //     0x81f9e8: stur            w5, [x2, #0x13]
    // 0x81f9ec: ldur            x6, [fp, #-0x88]
    // 0x81f9f0: ArrayStore: r2[0] = r6  ; List_4
    //     0x81f9f0: stur            w6, [x2, #0x17]
    // 0x81f9f4: ldur            x0, [fp, #-0x40]
    // 0x81f9f8: LoadField: r1 = r0->field_b
    //     0x81f9f8: ldur            w1, [x0, #0xb]
    // 0x81f9fc: LoadField: r3 = r0->field_f
    //     0x81f9fc: ldur            w3, [x0, #0xf]
    // 0x81fa00: DecompressPointer r3
    //     0x81fa00: add             x3, x3, HEAP, lsl #32
    // 0x81fa04: LoadField: r4 = r3->field_b
    //     0x81fa04: ldur            w4, [x3, #0xb]
    // 0x81fa08: r3 = LoadInt32Instr(r1)
    //     0x81fa08: sbfx            x3, x1, #1, #0x1f
    // 0x81fa0c: stur            x3, [fp, #-0x70]
    // 0x81fa10: r1 = LoadInt32Instr(r4)
    //     0x81fa10: sbfx            x1, x4, #1, #0x1f
    // 0x81fa14: cmp             x3, x1
    // 0x81fa18: b.ne            #0x81fa24
    // 0x81fa1c: mov             x1, x0
    // 0x81fa20: r0 = _growToNextCapacity()
    //     0x81fa20: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x81fa24: ldur            x7, [fp, #-0x40]
    // 0x81fa28: ldur            x2, [fp, #-0x70]
    // 0x81fa2c: add             x0, x2, #1
    // 0x81fa30: lsl             x1, x0, #1
    // 0x81fa34: StoreField: r7->field_b = r1
    //     0x81fa34: stur            w1, [x7, #0xb]
    // 0x81fa38: LoadField: r1 = r7->field_f
    //     0x81fa38: ldur            w1, [x7, #0xf]
    // 0x81fa3c: DecompressPointer r1
    //     0x81fa3c: add             x1, x1, HEAP, lsl #32
    // 0x81fa40: ldur            x0, [fp, #-0xb0]
    // 0x81fa44: ArrayStore: r1[r2] = r0  ; List_4
    //     0x81fa44: add             x25, x1, x2, lsl #2
    //     0x81fa48: add             x25, x25, #0xf
    //     0x81fa4c: str             w0, [x25]
    //     0x81fa50: tbz             w0, #0, #0x81fa6c
    //     0x81fa54: ldurb           w16, [x1, #-1]
    //     0x81fa58: ldurb           w17, [x0, #-1]
    //     0x81fa5c: and             x16, x17, x16, lsr #2
    //     0x81fa60: tst             x16, HEAP, lsr #32
    //     0x81fa64: b.eq            #0x81fa6c
    //     0x81fa68: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81fa6c: b               #0x820924
    // 0x81fa70: ldur            x8, [fp, #-0x20]
    // 0x81fa74: ldur            x7, [fp, #-0x40]
    // 0x81fa78: mov             x5, x3
    // 0x81fa7c: mov             x3, x0
    // 0x81fa80: mov             x6, x4
    // 0x81fa84: mov             x4, x1
    // 0x81fa88: LoadField: r1 = r8->field_13
    //     0x81fa88: ldur            w1, [x8, #0x13]
    // 0x81fa8c: DecompressPointer r1
    //     0x81fa8c: add             x1, x1, HEAP, lsl #32
    // 0x81fa90: r0 = LoadClassIdInstr(r1)
    //     0x81fa90: ldur            x0, [x1, #-1]
    //     0x81fa94: ubfx            x0, x0, #0xc, #0x14
    // 0x81fa98: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81fa98: sub             lr, x0, #0xff6
    //     0x81fa9c: ldr             lr, [x21, lr, lsl #3]
    //     0x81faa0: blr             lr
    // 0x81faa4: ldur            x2, [fp, #-0x48]
    // 0x81faa8: stur            x0, [fp, #-0x80]
    // 0x81faac: LoadField: r1 = r2->field_b
    //     0x81faac: ldur            w1, [x2, #0xb]
    // 0x81fab0: DecompressPointer r1
    //     0x81fab0: add             x1, x1, HEAP, lsl #32
    // 0x81fab4: cmp             w1, NULL
    // 0x81fab8: b.eq            #0x8209e8
    // 0x81fabc: LoadField: r3 = r2->field_f
    //     0x81fabc: ldur            w3, [x2, #0xf]
    // 0x81fac0: DecompressPointer r3
    //     0x81fac0: add             x3, x3, HEAP, lsl #32
    // 0x81fac4: cmp             w3, NULL
    // 0x81fac8: b.eq            #0x8209ec
    // 0x81facc: r2 = LoadInt32Instr(r1)
    //     0x81facc: sbfx            x2, x1, #1, #0x1f
    //     0x81fad0: tbz             w1, #0, #0x81fad8
    //     0x81fad4: ldur            x2, [x1, #7]
    // 0x81fad8: stur            x2, [fp, #-0x78]
    // 0x81fadc: r5 = LoadInt32Instr(r3)
    //     0x81fadc: sbfx            x5, x3, #1, #0x1f
    //     0x81fae0: tbz             w3, #0, #0x81fae8
    //     0x81fae4: ldur            x5, [x3, #7]
    // 0x81fae8: stur            x5, [fp, #-0x70]
    // 0x81faec: r0 = DateTime()
    //     0x81faec: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81faf0: stur            x0, [fp, #-0xa8]
    // 0x81faf4: stp             xzr, xzr, [SP, #0x10]
    // 0x81faf8: r16 = false
    //     0x81faf8: add             x16, NULL, #0x30  ; false
    // 0x81fafc: stp             x16, xzr, [SP]
    // 0x81fb00: mov             x1, x0
    // 0x81fb04: ldur            x2, [fp, #-0x80]
    // 0x81fb08: ldur            x3, [fp, #-0x78]
    // 0x81fb0c: ldur            x5, [fp, #-0x70]
    // 0x81fb10: r6 = 0
    //     0x81fb10: movz            x6, #0
    // 0x81fb14: r7 = 0
    //     0x81fb14: movz            x7, #0
    // 0x81fb18: r0 = DateTime._internal()
    //     0x81fb18: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x81fb1c: r0 = Event()
    //     0x81fb1c: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x81fb20: mov             x2, x0
    // 0x81fb24: ldur            x0, [fp, #-0xa8]
    // 0x81fb28: stur            x2, [fp, #-0xb0]
    // 0x81fb2c: StoreField: r2->field_7 = r0
    //     0x81fb2c: stur            w0, [x2, #7]
    // 0x81fb30: ldur            x0, [fp, #-0xa0]
    // 0x81fb34: StoreField: r2->field_b = r0
    //     0x81fb34: stur            w0, [x2, #0xb]
    // 0x81fb38: ldur            x4, [fp, #-0x98]
    // 0x81fb3c: StoreField: r2->field_f = r4
    //     0x81fb3c: stur            w4, [x2, #0xf]
    // 0x81fb40: ldur            x6, [fp, #-0x90]
    // 0x81fb44: StoreField: r2->field_13 = r6
    //     0x81fb44: stur            w6, [x2, #0x13]
    // 0x81fb48: ldur            x7, [fp, #-0x88]
    // 0x81fb4c: ArrayStore: r2[0] = r7  ; List_4
    //     0x81fb4c: stur            w7, [x2, #0x17]
    // 0x81fb50: ldur            x0, [fp, #-0x40]
    // 0x81fb54: LoadField: r1 = r0->field_b
    //     0x81fb54: ldur            w1, [x0, #0xb]
    // 0x81fb58: LoadField: r3 = r0->field_f
    //     0x81fb58: ldur            w3, [x0, #0xf]
    // 0x81fb5c: DecompressPointer r3
    //     0x81fb5c: add             x3, x3, HEAP, lsl #32
    // 0x81fb60: LoadField: r4 = r3->field_b
    //     0x81fb60: ldur            w4, [x3, #0xb]
    // 0x81fb64: r3 = LoadInt32Instr(r1)
    //     0x81fb64: sbfx            x3, x1, #1, #0x1f
    // 0x81fb68: stur            x3, [fp, #-0x70]
    // 0x81fb6c: r1 = LoadInt32Instr(r4)
    //     0x81fb6c: sbfx            x1, x4, #1, #0x1f
    // 0x81fb70: cmp             x3, x1
    // 0x81fb74: b.ne            #0x81fb80
    // 0x81fb78: mov             x1, x0
    // 0x81fb7c: r0 = _growToNextCapacity()
    //     0x81fb7c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x81fb80: ldur            x8, [fp, #-0x40]
    // 0x81fb84: ldur            x2, [fp, #-0x70]
    // 0x81fb88: add             x0, x2, #1
    // 0x81fb8c: lsl             x1, x0, #1
    // 0x81fb90: StoreField: r8->field_b = r1
    //     0x81fb90: stur            w1, [x8, #0xb]
    // 0x81fb94: LoadField: r1 = r8->field_f
    //     0x81fb94: ldur            w1, [x8, #0xf]
    // 0x81fb98: DecompressPointer r1
    //     0x81fb98: add             x1, x1, HEAP, lsl #32
    // 0x81fb9c: ldur            x0, [fp, #-0xb0]
    // 0x81fba0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x81fba0: add             x25, x1, x2, lsl #2
    //     0x81fba4: add             x25, x25, #0xf
    //     0x81fba8: str             w0, [x25]
    //     0x81fbac: tbz             w0, #0, #0x81fbc8
    //     0x81fbb0: ldurb           w16, [x1, #-1]
    //     0x81fbb4: ldurb           w17, [x0, #-1]
    //     0x81fbb8: and             x16, x17, x16, lsr #2
    //     0x81fbbc: tst             x16, HEAP, lsr #32
    //     0x81fbc0: b.eq            #0x81fbc8
    //     0x81fbc4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81fbc8: b               #0x820924
    // 0x81fbcc: ldur            x11, [fp, #-0x10]
    // 0x81fbd0: ldur            x10, [fp, #-0x38]
    // 0x81fbd4: ldur            x8, [fp, #-0x40]
    // 0x81fbd8: mov             x7, x4
    // 0x81fbdc: mov             x4, x1
    // 0x81fbe0: mov             x6, x3
    // 0x81fbe4: LoadField: r1 = r11->field_13
    //     0x81fbe4: ldur            w1, [x11, #0x13]
    // 0x81fbe8: DecompressPointer r1
    //     0x81fbe8: add             x1, x1, HEAP, lsl #32
    // 0x81fbec: r16 = Sentinel
    //     0x81fbec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81fbf0: cmp             w1, w16
    // 0x81fbf4: b.eq            #0x8209f0
    // 0x81fbf8: LoadField: r9 = r2->field_b
    //     0x81fbf8: ldur            w9, [x2, #0xb]
    // 0x81fbfc: DecompressPointer r9
    //     0x81fbfc: add             x9, x9, HEAP, lsl #32
    // 0x81fc00: stur            x9, [fp, #-0xb0]
    // 0x81fc04: LoadField: r12 = r2->field_f
    //     0x81fc04: ldur            w12, [x2, #0xf]
    // 0x81fc08: DecompressPointer r12
    //     0x81fc08: add             x12, x12, HEAP, lsl #32
    // 0x81fc0c: stur            x12, [fp, #-0xa8]
    // 0x81fc10: r2 = LoadInt32Instr(r1)
    //     0x81fc10: sbfx            x2, x1, #1, #0x1f
    //     0x81fc14: tbz             w1, #0, #0x81fc1c
    //     0x81fc18: ldur            x2, [x1, #7]
    // 0x81fc1c: ldur            x1, [fp, #-0x18]
    // 0x81fc20: mov             x3, x9
    // 0x81fc24: mov             x5, x12
    // 0x81fc28: r0 = hijriToGregorian()
    //     0x81fc28: bl              #0x820a4c  ; [package:nuonline/services/hijri_service.dart] HijriService::hijriToGregorian
    // 0x81fc2c: mov             x1, x0
    // 0x81fc30: ldur            x0, [fp, #-0x10]
    // 0x81fc34: LoadField: r2 = r0->field_13
    //     0x81fc34: ldur            w2, [x0, #0x13]
    // 0x81fc38: DecompressPointer r2
    //     0x81fc38: add             x2, x2, HEAP, lsl #32
    // 0x81fc3c: ldur            x4, [fp, #-0x38]
    // 0x81fc40: LoadField: r3 = r4->field_13
    //     0x81fc40: ldur            w3, [x4, #0x13]
    // 0x81fc44: DecompressPointer r3
    //     0x81fc44: add             x3, x3, HEAP, lsl #32
    // 0x81fc48: r16 = Sentinel
    //     0x81fc48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x81fc4c: cmp             w3, w16
    // 0x81fc50: b.eq            #0x8209fc
    // 0x81fc54: r5 = LoadInt32Instr(r2)
    //     0x81fc54: sbfx            x5, x2, #1, #0x1f
    //     0x81fc58: tbz             w2, #0, #0x81fc60
    //     0x81fc5c: ldur            x5, [x2, #7]
    // 0x81fc60: r2 = LoadInt32Instr(r3)
    //     0x81fc60: sbfx            x2, x3, #1, #0x1f
    //     0x81fc64: tbz             w3, #0, #0x81fc6c
    //     0x81fc68: ldur            x2, [x3, #7]
    // 0x81fc6c: cmp             x5, x2
    // 0x81fc70: b.eq            #0x81fc94
    // 0x81fc74: ldur            x3, [fp, #-0xb0]
    // 0x81fc78: cmp             w3, #2
    // 0x81fc7c: b.ne            #0x81fc94
    // 0x81fc80: ldur            x1, [fp, #-0x18]
    // 0x81fc84: ldur            x5, [fp, #-0xa8]
    // 0x81fc88: r0 = hijriToGregorian()
    //     0x81fc88: bl              #0x820a4c  ; [package:nuonline/services/hijri_service.dart] HijriService::hijriToGregorian
    // 0x81fc8c: mov             x5, x0
    // 0x81fc90: b               #0x81fc98
    // 0x81fc94: mov             x5, x1
    // 0x81fc98: ldur            x4, [fp, #-0x40]
    // 0x81fc9c: ldur            x0, [fp, #-0xa0]
    // 0x81fca0: ldur            x1, [fp, #-0x98]
    // 0x81fca4: ldur            x2, [fp, #-0x90]
    // 0x81fca8: ldur            x3, [fp, #-0x88]
    // 0x81fcac: stur            x5, [fp, #-0xa8]
    // 0x81fcb0: r0 = Event()
    //     0x81fcb0: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x81fcb4: mov             x2, x0
    // 0x81fcb8: ldur            x0, [fp, #-0xa8]
    // 0x81fcbc: stur            x2, [fp, #-0xb0]
    // 0x81fcc0: StoreField: r2->field_7 = r0
    //     0x81fcc0: stur            w0, [x2, #7]
    // 0x81fcc4: ldur            x3, [fp, #-0xa0]
    // 0x81fcc8: StoreField: r2->field_b = r3
    //     0x81fcc8: stur            w3, [x2, #0xb]
    // 0x81fccc: ldur            x4, [fp, #-0x98]
    // 0x81fcd0: StoreField: r2->field_f = r4
    //     0x81fcd0: stur            w4, [x2, #0xf]
    // 0x81fcd4: ldur            x5, [fp, #-0x90]
    // 0x81fcd8: StoreField: r2->field_13 = r5
    //     0x81fcd8: stur            w5, [x2, #0x13]
    // 0x81fcdc: ldur            x7, [fp, #-0x88]
    // 0x81fce0: ArrayStore: r2[0] = r7  ; List_4
    //     0x81fce0: stur            w7, [x2, #0x17]
    // 0x81fce4: ldur            x0, [fp, #-0x40]
    // 0x81fce8: LoadField: r1 = r0->field_b
    //     0x81fce8: ldur            w1, [x0, #0xb]
    // 0x81fcec: LoadField: r3 = r0->field_f
    //     0x81fcec: ldur            w3, [x0, #0xf]
    // 0x81fcf0: DecompressPointer r3
    //     0x81fcf0: add             x3, x3, HEAP, lsl #32
    // 0x81fcf4: LoadField: r4 = r3->field_b
    //     0x81fcf4: ldur            w4, [x3, #0xb]
    // 0x81fcf8: r3 = LoadInt32Instr(r1)
    //     0x81fcf8: sbfx            x3, x1, #1, #0x1f
    // 0x81fcfc: stur            x3, [fp, #-0x70]
    // 0x81fd00: r1 = LoadInt32Instr(r4)
    //     0x81fd00: sbfx            x1, x4, #1, #0x1f
    // 0x81fd04: cmp             x3, x1
    // 0x81fd08: b.ne            #0x81fd14
    // 0x81fd0c: mov             x1, x0
    // 0x81fd10: r0 = _growToNextCapacity()
    //     0x81fd10: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x81fd14: ldur            x8, [fp, #-0x40]
    // 0x81fd18: ldur            x2, [fp, #-0x70]
    // 0x81fd1c: add             x0, x2, #1
    // 0x81fd20: lsl             x1, x0, #1
    // 0x81fd24: StoreField: r8->field_b = r1
    //     0x81fd24: stur            w1, [x8, #0xb]
    // 0x81fd28: LoadField: r1 = r8->field_f
    //     0x81fd28: ldur            w1, [x8, #0xf]
    // 0x81fd2c: DecompressPointer r1
    //     0x81fd2c: add             x1, x1, HEAP, lsl #32
    // 0x81fd30: ldur            x0, [fp, #-0xb0]
    // 0x81fd34: ArrayStore: r1[r2] = r0  ; List_4
    //     0x81fd34: add             x25, x1, x2, lsl #2
    //     0x81fd38: add             x25, x25, #0xf
    //     0x81fd3c: str             w0, [x25]
    //     0x81fd40: tbz             w0, #0, #0x81fd5c
    //     0x81fd44: ldurb           w16, [x1, #-1]
    //     0x81fd48: ldurb           w17, [x0, #-1]
    //     0x81fd4c: and             x16, x17, x16, lsr #2
    //     0x81fd50: tst             x16, HEAP, lsr #32
    //     0x81fd54: b.eq            #0x81fd5c
    //     0x81fd58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81fd5c: b               #0x820924
    // 0x81fd60: ldur            x8, [fp, #-0x40]
    // 0x81fd64: mov             x5, x3
    // 0x81fd68: mov             x3, x0
    // 0x81fd6c: mov             x7, x4
    // 0x81fd70: mov             x4, x1
    // 0x81fd74: cmp             x6, #4
    // 0x81fd78: b.gt            #0x8203d8
    // 0x81fd7c: cmp             x6, #3
    // 0x81fd80: b.gt            #0x81fed4
    // 0x81fd84: ldur            x6, [fp, #-0x20]
    // 0x81fd88: LoadField: r1 = r6->field_13
    //     0x81fd88: ldur            w1, [x6, #0x13]
    // 0x81fd8c: DecompressPointer r1
    //     0x81fd8c: add             x1, x1, HEAP, lsl #32
    // 0x81fd90: r0 = LoadClassIdInstr(r1)
    //     0x81fd90: ldur            x0, [x1, #-1]
    //     0x81fd94: ubfx            x0, x0, #0xc, #0x14
    // 0x81fd98: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81fd98: sub             lr, x0, #0xff6
    //     0x81fd9c: ldr             lr, [x21, lr, lsl #3]
    //     0x81fda0: blr             lr
    // 0x81fda4: mov             x3, x0
    // 0x81fda8: ldur            x2, [fp, #-0x20]
    // 0x81fdac: stur            x3, [fp, #-0x70]
    // 0x81fdb0: LoadField: r1 = r2->field_13
    //     0x81fdb0: ldur            w1, [x2, #0x13]
    // 0x81fdb4: DecompressPointer r1
    //     0x81fdb4: add             x1, x1, HEAP, lsl #32
    // 0x81fdb8: r0 = LoadClassIdInstr(r1)
    //     0x81fdb8: ldur            x0, [x1, #-1]
    //     0x81fdbc: ubfx            x0, x0, #0xc, #0x14
    // 0x81fdc0: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81fdc0: sub             lr, x0, #0xfff
    //     0x81fdc4: ldr             lr, [x21, lr, lsl #3]
    //     0x81fdc8: blr             lr
    // 0x81fdcc: ldur            x2, [fp, #-0x48]
    // 0x81fdd0: stur            x0, [fp, #-0x80]
    // 0x81fdd4: LoadField: r1 = r2->field_f
    //     0x81fdd4: ldur            w1, [x2, #0xf]
    // 0x81fdd8: DecompressPointer r1
    //     0x81fdd8: add             x1, x1, HEAP, lsl #32
    // 0x81fddc: cmp             w1, NULL
    // 0x81fde0: b.eq            #0x820a08
    // 0x81fde4: r5 = LoadInt32Instr(r1)
    //     0x81fde4: sbfx            x5, x1, #1, #0x1f
    //     0x81fde8: tbz             w1, #0, #0x81fdf0
    //     0x81fdec: ldur            x5, [x1, #7]
    // 0x81fdf0: stur            x5, [fp, #-0x78]
    // 0x81fdf4: r0 = DateTime()
    //     0x81fdf4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81fdf8: stur            x0, [fp, #-0xa8]
    // 0x81fdfc: stp             xzr, xzr, [SP, #0x10]
    // 0x81fe00: r16 = false
    //     0x81fe00: add             x16, NULL, #0x30  ; false
    // 0x81fe04: stp             x16, xzr, [SP]
    // 0x81fe08: mov             x1, x0
    // 0x81fe0c: ldur            x2, [fp, #-0x70]
    // 0x81fe10: ldur            x3, [fp, #-0x80]
    // 0x81fe14: ldur            x5, [fp, #-0x78]
    // 0x81fe18: r6 = 0
    //     0x81fe18: movz            x6, #0
    // 0x81fe1c: r7 = 0
    //     0x81fe1c: movz            x7, #0
    // 0x81fe20: r0 = DateTime._internal()
    //     0x81fe20: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x81fe24: r0 = Event()
    //     0x81fe24: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x81fe28: mov             x2, x0
    // 0x81fe2c: ldur            x0, [fp, #-0xa8]
    // 0x81fe30: stur            x2, [fp, #-0xb0]
    // 0x81fe34: StoreField: r2->field_7 = r0
    //     0x81fe34: stur            w0, [x2, #7]
    // 0x81fe38: ldur            x3, [fp, #-0xa0]
    // 0x81fe3c: StoreField: r2->field_b = r3
    //     0x81fe3c: stur            w3, [x2, #0xb]
    // 0x81fe40: ldur            x4, [fp, #-0x98]
    // 0x81fe44: StoreField: r2->field_f = r4
    //     0x81fe44: stur            w4, [x2, #0xf]
    // 0x81fe48: ldur            x5, [fp, #-0x90]
    // 0x81fe4c: StoreField: r2->field_13 = r5
    //     0x81fe4c: stur            w5, [x2, #0x13]
    // 0x81fe50: ldur            x6, [fp, #-0x88]
    // 0x81fe54: ArrayStore: r2[0] = r6  ; List_4
    //     0x81fe54: stur            w6, [x2, #0x17]
    // 0x81fe58: ldur            x0, [fp, #-0x40]
    // 0x81fe5c: LoadField: r1 = r0->field_b
    //     0x81fe5c: ldur            w1, [x0, #0xb]
    // 0x81fe60: LoadField: r3 = r0->field_f
    //     0x81fe60: ldur            w3, [x0, #0xf]
    // 0x81fe64: DecompressPointer r3
    //     0x81fe64: add             x3, x3, HEAP, lsl #32
    // 0x81fe68: LoadField: r4 = r3->field_b
    //     0x81fe68: ldur            w4, [x3, #0xb]
    // 0x81fe6c: r3 = LoadInt32Instr(r1)
    //     0x81fe6c: sbfx            x3, x1, #1, #0x1f
    // 0x81fe70: stur            x3, [fp, #-0x70]
    // 0x81fe74: r1 = LoadInt32Instr(r4)
    //     0x81fe74: sbfx            x1, x4, #1, #0x1f
    // 0x81fe78: cmp             x3, x1
    // 0x81fe7c: b.ne            #0x81fe88
    // 0x81fe80: mov             x1, x0
    // 0x81fe84: r0 = _growToNextCapacity()
    //     0x81fe84: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x81fe88: ldur            x7, [fp, #-0x40]
    // 0x81fe8c: ldur            x2, [fp, #-0x70]
    // 0x81fe90: add             x0, x2, #1
    // 0x81fe94: lsl             x1, x0, #1
    // 0x81fe98: StoreField: r7->field_b = r1
    //     0x81fe98: stur            w1, [x7, #0xb]
    // 0x81fe9c: LoadField: r1 = r7->field_f
    //     0x81fe9c: ldur            w1, [x7, #0xf]
    // 0x81fea0: DecompressPointer r1
    //     0x81fea0: add             x1, x1, HEAP, lsl #32
    // 0x81fea4: ldur            x0, [fp, #-0xb0]
    // 0x81fea8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x81fea8: add             x25, x1, x2, lsl #2
    //     0x81feac: add             x25, x25, #0xf
    //     0x81feb0: str             w0, [x25]
    //     0x81feb4: tbz             w0, #0, #0x81fed0
    //     0x81feb8: ldurb           w16, [x1, #-1]
    //     0x81febc: ldurb           w17, [x0, #-1]
    //     0x81fec0: and             x16, x17, x16, lsr #2
    //     0x81fec4: tst             x16, HEAP, lsr #32
    //     0x81fec8: b.eq            #0x81fed0
    //     0x81fecc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x81fed0: b               #0x820924
    // 0x81fed4: mov             x6, x7
    // 0x81fed8: mov             x7, x8
    // 0x81fedc: ldur            x8, [fp, #-0x20]
    // 0x81fee0: LoadField: r1 = r8->field_13
    //     0x81fee0: ldur            w1, [x8, #0x13]
    // 0x81fee4: DecompressPointer r1
    //     0x81fee4: add             x1, x1, HEAP, lsl #32
    // 0x81fee8: r0 = LoadClassIdInstr(r1)
    //     0x81fee8: ldur            x0, [x1, #-1]
    //     0x81feec: ubfx            x0, x0, #0xc, #0x14
    // 0x81fef0: r0 = GDT[cid_x0 + -0xff6]()
    //     0x81fef0: sub             lr, x0, #0xff6
    //     0x81fef4: ldr             lr, [x21, lr, lsl #3]
    //     0x81fef8: blr             lr
    // 0x81fefc: mov             x3, x0
    // 0x81ff00: ldur            x2, [fp, #-0x20]
    // 0x81ff04: stur            x3, [fp, #-0x70]
    // 0x81ff08: LoadField: r1 = r2->field_13
    //     0x81ff08: ldur            w1, [x2, #0x13]
    // 0x81ff0c: DecompressPointer r1
    //     0x81ff0c: add             x1, x1, HEAP, lsl #32
    // 0x81ff10: r0 = LoadClassIdInstr(r1)
    //     0x81ff10: ldur            x0, [x1, #-1]
    //     0x81ff14: ubfx            x0, x0, #0xc, #0x14
    // 0x81ff18: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81ff18: sub             lr, x0, #0xfff
    //     0x81ff1c: ldr             lr, [x21, lr, lsl #3]
    //     0x81ff20: blr             lr
    // 0x81ff24: stur            x0, [fp, #-0x78]
    // 0x81ff28: r0 = DateTime()
    //     0x81ff28: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x81ff2c: stur            x0, [fp, #-0xa8]
    // 0x81ff30: stp             xzr, xzr, [SP, #0x10]
    // 0x81ff34: r16 = false
    //     0x81ff34: add             x16, NULL, #0x30  ; false
    // 0x81ff38: stp             x16, xzr, [SP]
    // 0x81ff3c: mov             x1, x0
    // 0x81ff40: ldur            x2, [fp, #-0x70]
    // 0x81ff44: ldur            x3, [fp, #-0x78]
    // 0x81ff48: r5 = 1
    //     0x81ff48: movz            x5, #0x1
    // 0x81ff4c: r6 = 0
    //     0x81ff4c: movz            x6, #0
    // 0x81ff50: r7 = 0
    //     0x81ff50: movz            x7, #0
    // 0x81ff54: r0 = DateTime._internal()
    //     0x81ff54: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x81ff58: ldur            x2, [fp, #-0x48]
    // 0x81ff5c: LoadField: r3 = r2->field_f
    //     0x81ff5c: ldur            w3, [x2, #0xf]
    // 0x81ff60: DecompressPointer r3
    //     0x81ff60: add             x3, x3, HEAP, lsl #32
    // 0x81ff64: stur            x3, [fp, #-0xb0]
    // 0x81ff68: ldur            x10, [fp, #-0xa8]
    // 0x81ff6c: ldur            x2, [fp, #-0x20]
    // 0x81ff70: ldur            x8, [fp, #-0x40]
    // 0x81ff74: ldur            x9, [fp, #-0x68]
    // 0x81ff78: ldur            x4, [fp, #-0xa0]
    // 0x81ff7c: ldur            x5, [fp, #-0x98]
    // 0x81ff80: ldur            x6, [fp, #-0x90]
    // 0x81ff84: ldur            x7, [fp, #-0x88]
    // 0x81ff88: stur            x10, [fp, #-0xa8]
    // 0x81ff8c: CheckStackOverflow
    //     0x81ff8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81ff90: cmp             SP, x16
    //     0x81ff94: b.ls            #0x820a0c
    // 0x81ff98: r0 = LoadClassIdInstr(r10)
    //     0x81ff98: ldur            x0, [x10, #-1]
    //     0x81ff9c: ubfx            x0, x0, #0xc, #0x14
    // 0x81ffa0: mov             x1, x10
    // 0x81ffa4: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81ffa4: sub             lr, x0, #0xfff
    //     0x81ffa8: ldr             lr, [x21, lr, lsl #3]
    //     0x81ffac: blr             lr
    // 0x81ffb0: mov             x3, x0
    // 0x81ffb4: ldur            x2, [fp, #-0x20]
    // 0x81ffb8: stur            x3, [fp, #-0x70]
    // 0x81ffbc: LoadField: r1 = r2->field_13
    //     0x81ffbc: ldur            w1, [x2, #0x13]
    // 0x81ffc0: DecompressPointer r1
    //     0x81ffc0: add             x1, x1, HEAP, lsl #32
    // 0x81ffc4: r0 = LoadClassIdInstr(r1)
    //     0x81ffc4: ldur            x0, [x1, #-1]
    //     0x81ffc8: ubfx            x0, x0, #0xc, #0x14
    // 0x81ffcc: r0 = GDT[cid_x0 + -0xfff]()
    //     0x81ffcc: sub             lr, x0, #0xfff
    //     0x81ffd0: ldr             lr, [x21, lr, lsl #3]
    //     0x81ffd4: blr             lr
    // 0x81ffd8: mov             x1, x0
    // 0x81ffdc: ldur            x0, [fp, #-0x70]
    // 0x81ffe0: cmp             x0, x1
    // 0x81ffe4: b.ne            #0x820924
    // 0x81ffe8: ldur            x2, [fp, #-0xa8]
    // 0x81ffec: ldur            x0, [fp, #-0xb0]
    // 0x81fff0: ldur            x1, [fp, #-0x50]
    // 0x81fff4: r0 = value()
    //     0x81fff4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x81fff8: r1 = LoadInt32Instr(r0)
    //     0x81fff8: sbfx            x1, x0, #1, #0x1f
    //     0x81fffc: tbz             w0, #0, #0x820004
    //     0x820000: ldur            x1, [x0, #7]
    // 0x820004: r16 = 86400000000
    //     0x820004: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x820008: ldr             x16, [x16, #0x268]
    // 0x82000c: mul             x0, x1, x16
    // 0x820010: stur            x0, [fp, #-0x70]
    // 0x820014: r0 = Duration()
    //     0x820014: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x820018: mov             x1, x0
    // 0x82001c: ldur            x0, [fp, #-0x70]
    // 0x820020: StoreField: r1->field_7 = r0
    //     0x820020: stur            x0, [x1, #7]
    // 0x820024: ldur            x3, [fp, #-0xa8]
    // 0x820028: r0 = LoadClassIdInstr(r3)
    //     0x820028: ldur            x0, [x3, #-1]
    //     0x82002c: ubfx            x0, x0, #0xc, #0x14
    // 0x820030: mov             x2, x1
    // 0x820034: mov             x1, x3
    // 0x820038: r0 = GDT[cid_x0 + -0xf8f]()
    //     0x820038: sub             lr, x0, #0xf8f
    //     0x82003c: ldr             lr, [x21, lr, lsl #3]
    //     0x820040: blr             lr
    // 0x820044: stur            x0, [fp, #-0xb8]
    // 0x820048: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x820048: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x82004c: ldr             x0, [x0, #0x2728]
    //     0x820050: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x820054: cmp             w0, w16
    //     0x820058: b.ne            #0x820064
    //     0x82005c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x820060: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x820064: r16 = <HijriDate>
    //     0x820064: ldr             x16, [PP, #0x7bc0]  ; [pp+0x7bc0] TypeArguments: <HijriDate>
    // 0x820068: stp             x0, x16, [SP, #8]
    // 0x82006c: r16 = "v2_calendar"
    //     0x82006c: ldr             x16, [PP, #0x7c68]  ; [pp+0x7c68] "v2_calendar"
    // 0x820070: str             x16, [SP]
    // 0x820074: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x820074: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x820078: r0 = box()
    //     0x820078: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x82007c: ldur            x1, [fp, #-0x18]
    // 0x820080: stur            x0, [fp, #-0xc0]
    // 0x820084: r0 = hijriMethod()
    //     0x820084: bl              #0x817818  ; [package:nuonline/services/hijri_service.dart] HijriService::hijriMethod
    // 0x820088: stur            x0, [fp, #-0xc8]
    // 0x82008c: r0 = NHijriCalendar()
    //     0x82008c: bl              #0x814f84  ; AllocateNHijriCalendarStub -> NHijriCalendar (size=0x20)
    // 0x820090: mov             x1, x0
    // 0x820094: ldur            x2, [fp, #-0xb8]
    // 0x820098: ldur            x3, [fp, #-0xc0]
    // 0x82009c: ldur            x5, [fp, #-0xc8]
    // 0x8200a0: stur            x0, [fp, #-0xb8]
    // 0x8200a4: r0 = NHijriCalendar.fromDate()
    //     0x8200a4: bl              #0x815680  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::NHijriCalendar.fromDate
    // 0x8200a8: ldur            x2, [fp, #-0xb8]
    // 0x8200ac: LoadField: r3 = r2->field_7
    //     0x8200ac: ldur            x3, [x2, #7]
    // 0x8200b0: r0 = BoxInt64Instr(r3)
    //     0x8200b0: sbfiz           x0, x3, #1, #0x1f
    //     0x8200b4: cmp             x3, x0, asr #1
    //     0x8200b8: b.eq            #0x8200c4
    //     0x8200bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8200c0: stur            x3, [x0, #7]
    // 0x8200c4: mov             x3, x0
    // 0x8200c8: ldur            x0, [fp, #-0xb0]
    // 0x8200cc: stur            x3, [fp, #-0xc8]
    // 0x8200d0: cmp             w3, w0
    // 0x8200d4: b.eq            #0x820110
    // 0x8200d8: and             w16, w3, w0
    // 0x8200dc: branchIfSmi(r16, 0x8203a8)
    //     0x8200dc: tbz             w16, #0, #0x8203a8
    // 0x8200e0: r16 = LoadClassIdInstr(r3)
    //     0x8200e0: ldur            x16, [x3, #-1]
    //     0x8200e4: ubfx            x16, x16, #0xc, #0x14
    // 0x8200e8: cmp             x16, #0x3d
    // 0x8200ec: b.ne            #0x8203a8
    // 0x8200f0: r16 = LoadClassIdInstr(r0)
    //     0x8200f0: ldur            x16, [x0, #-1]
    //     0x8200f4: ubfx            x16, x16, #0xc, #0x14
    // 0x8200f8: cmp             x16, #0x3d
    // 0x8200fc: b.ne            #0x8203a8
    // 0x820100: LoadField: r16 = r3->field_7
    //     0x820100: ldur            x16, [x3, #7]
    // 0x820104: LoadField: r17 = r0->field_7
    //     0x820104: ldur            x17, [x0, #7]
    // 0x820108: cmp             x16, x17
    // 0x82010c: b.ne            #0x8203a8
    // 0x820110: ldur            x4, [fp, #-0x98]
    // 0x820114: r16 = Instance_EventCategory
    //     0x820114: add             x16, PP, #9, lsl #12  ; [pp+0x92d0] Obj!EventCategory@e310a1
    //     0x820118: ldr             x16, [x16, #0x2d0]
    // 0x82011c: cmp             w4, w16
    // 0x820120: b.ne            #0x82023c
    // 0x820124: LoadField: r5 = r2->field_f
    //     0x820124: ldur            w5, [x2, #0xf]
    // 0x820128: DecompressPointer r5
    //     0x820128: add             x5, x5, HEAP, lsl #32
    // 0x82012c: r16 = Sentinel
    //     0x82012c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x820130: cmp             w5, w16
    // 0x820134: b.eq            #0x820a14
    // 0x820138: stur            x5, [fp, #-0xc0]
    // 0x82013c: cmp             w5, #0x12
    // 0x820140: b.eq            #0x820234
    // 0x820144: ldur            x6, [fp, #-0x68]
    // 0x820148: r1 = Null
    //     0x820148: mov             x1, NULL
    // 0x82014c: r2 = 6
    //     0x82014c: movz            x2, #0x6
    // 0x820150: r0 = AllocateArray()
    //     0x820150: bl              #0xec22fc  ; AllocateArrayStub
    // 0x820154: mov             x1, x0
    // 0x820158: ldur            x0, [fp, #-0xc8]
    // 0x82015c: StoreField: r1->field_f = r0
    //     0x82015c: stur            w0, [x1, #0xf]
    // 0x820160: r16 = "/"
    //     0x820160: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x820164: StoreField: r1->field_13 = r16
    //     0x820164: stur            w16, [x1, #0x13]
    // 0x820168: ldur            x0, [fp, #-0xc0]
    // 0x82016c: ArrayStore: r1[0] = r0  ; List_4
    //     0x82016c: stur            w0, [x1, #0x17]
    // 0x820170: str             x1, [SP]
    // 0x820174: r0 = _interpolate()
    //     0x820174: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x820178: mov             x3, x0
    // 0x82017c: ldur            x2, [fp, #-0x68]
    // 0x820180: stur            x3, [fp, #-0xb8]
    // 0x820184: LoadField: r0 = r2->field_b
    //     0x820184: ldur            w0, [x2, #0xb]
    // 0x820188: r4 = LoadInt32Instr(r0)
    //     0x820188: sbfx            x4, x0, #1, #0x1f
    // 0x82018c: stur            x4, [fp, #-0x78]
    // 0x820190: r1 = LoadInt32Instr(r0)
    //     0x820190: sbfx            x1, x0, #1, #0x1f
    // 0x820194: mov             x0, x1
    // 0x820198: r5 = 0
    //     0x820198: movz            x5, #0
    // 0x82019c: stur            x5, [fp, #-0x70]
    // 0x8201a0: CheckStackOverflow
    //     0x8201a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8201a4: cmp             SP, x16
    //     0x8201a8: b.ls            #0x820a20
    // 0x8201ac: cmp             x5, x4
    // 0x8201b0: b.ge            #0x820240
    // 0x8201b4: mov             x1, x5
    // 0x8201b8: cmp             x1, x0
    // 0x8201bc: b.hs            #0x820a28
    // 0x8201c0: LoadField: r0 = r2->field_f
    //     0x8201c0: ldur            w0, [x2, #0xf]
    // 0x8201c4: DecompressPointer r0
    //     0x8201c4: add             x0, x0, HEAP, lsl #32
    // 0x8201c8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x8201c8: add             x16, x0, x5, lsl #2
    //     0x8201cc: ldur            w1, [x16, #0xf]
    // 0x8201d0: DecompressPointer r1
    //     0x8201d0: add             x1, x1, HEAP, lsl #32
    // 0x8201d4: r0 = 60
    //     0x8201d4: movz            x0, #0x3c
    // 0x8201d8: branchIfSmi(r1, 0x8201e4)
    //     0x8201d8: tbz             w1, #0, #0x8201e4
    // 0x8201dc: r0 = LoadClassIdInstr(r1)
    //     0x8201dc: ldur            x0, [x1, #-1]
    //     0x8201e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8201e4: stp             x3, x1, [SP]
    // 0x8201e8: mov             lr, x0
    // 0x8201ec: ldr             lr, [x21, lr, lsl #3]
    // 0x8201f0: blr             lr
    // 0x8201f4: tbz             w0, #4, #0x82022c
    // 0x8201f8: ldur            x2, [fp, #-0x68]
    // 0x8201fc: ldur            x1, [fp, #-0x78]
    // 0x820200: LoadField: r0 = r2->field_b
    //     0x820200: ldur            w0, [x2, #0xb]
    // 0x820204: r3 = LoadInt32Instr(r0)
    //     0x820204: sbfx            x3, x0, #1, #0x1f
    // 0x820208: cmp             x1, x3
    // 0x82020c: b.ne            #0x82096c
    // 0x820210: ldur            x3, [fp, #-0x70]
    // 0x820214: add             x5, x3, #1
    // 0x820218: r3 = LoadInt32Instr(r0)
    //     0x820218: sbfx            x3, x0, #1, #0x1f
    // 0x82021c: mov             x0, x3
    // 0x820220: ldur            x3, [fp, #-0xb8]
    // 0x820224: mov             x4, x1
    // 0x820228: b               #0x82019c
    // 0x82022c: ldur            x2, [fp, #-0x68]
    // 0x820230: b               #0x820924
    // 0x820234: ldur            x2, [fp, #-0x68]
    // 0x820238: b               #0x820924
    // 0x82023c: ldur            x2, [fp, #-0x68]
    // 0x820240: ldur            x5, [fp, #-0x20]
    // 0x820244: ldur            x9, [fp, #-0x40]
    // 0x820248: ldur            x6, [fp, #-0xa0]
    // 0x82024c: ldur            x3, [fp, #-0x98]
    // 0x820250: ldur            x7, [fp, #-0x90]
    // 0x820254: ldur            x8, [fp, #-0x88]
    // 0x820258: ldur            x4, [fp, #-0xa8]
    // 0x82025c: LoadField: r1 = r5->field_13
    //     0x82025c: ldur            w1, [x5, #0x13]
    // 0x820260: DecompressPointer r1
    //     0x820260: add             x1, x1, HEAP, lsl #32
    // 0x820264: r0 = LoadClassIdInstr(r1)
    //     0x820264: ldur            x0, [x1, #-1]
    //     0x820268: ubfx            x0, x0, #0xc, #0x14
    // 0x82026c: r0 = GDT[cid_x0 + -0xff6]()
    //     0x82026c: sub             lr, x0, #0xff6
    //     0x820270: ldr             lr, [x21, lr, lsl #3]
    //     0x820274: blr             lr
    // 0x820278: mov             x3, x0
    // 0x82027c: ldur            x2, [fp, #-0x20]
    // 0x820280: stur            x3, [fp, #-0x70]
    // 0x820284: LoadField: r1 = r2->field_13
    //     0x820284: ldur            w1, [x2, #0x13]
    // 0x820288: DecompressPointer r1
    //     0x820288: add             x1, x1, HEAP, lsl #32
    // 0x82028c: r0 = LoadClassIdInstr(r1)
    //     0x82028c: ldur            x0, [x1, #-1]
    //     0x820290: ubfx            x0, x0, #0xc, #0x14
    // 0x820294: r0 = GDT[cid_x0 + -0xfff]()
    //     0x820294: sub             lr, x0, #0xfff
    //     0x820298: ldr             lr, [x21, lr, lsl #3]
    //     0x82029c: blr             lr
    // 0x8202a0: mov             x3, x0
    // 0x8202a4: ldur            x2, [fp, #-0xa8]
    // 0x8202a8: stur            x3, [fp, #-0x78]
    // 0x8202ac: r0 = LoadClassIdInstr(r2)
    //     0x8202ac: ldur            x0, [x2, #-1]
    //     0x8202b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8202b4: mov             x1, x2
    // 0x8202b8: r0 = GDT[cid_x0 + -0xfdf]()
    //     0x8202b8: sub             lr, x0, #0xfdf
    //     0x8202bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8202c0: blr             lr
    // 0x8202c4: stur            x0, [fp, #-0x80]
    // 0x8202c8: r0 = DateTime()
    //     0x8202c8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8202cc: stur            x0, [fp, #-0xb8]
    // 0x8202d0: stp             xzr, xzr, [SP, #0x10]
    // 0x8202d4: r16 = false
    //     0x8202d4: add             x16, NULL, #0x30  ; false
    // 0x8202d8: stp             x16, xzr, [SP]
    // 0x8202dc: mov             x1, x0
    // 0x8202e0: ldur            x2, [fp, #-0x70]
    // 0x8202e4: ldur            x3, [fp, #-0x78]
    // 0x8202e8: ldur            x5, [fp, #-0x80]
    // 0x8202ec: r6 = 0
    //     0x8202ec: movz            x6, #0
    // 0x8202f0: r7 = 0
    //     0x8202f0: movz            x7, #0
    // 0x8202f4: r0 = DateTime._internal()
    //     0x8202f4: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x8202f8: r0 = Event()
    //     0x8202f8: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x8202fc: mov             x2, x0
    // 0x820300: ldur            x0, [fp, #-0xb8]
    // 0x820304: stur            x2, [fp, #-0xc0]
    // 0x820308: StoreField: r2->field_7 = r0
    //     0x820308: stur            w0, [x2, #7]
    // 0x82030c: ldur            x0, [fp, #-0xa0]
    // 0x820310: StoreField: r2->field_b = r0
    //     0x820310: stur            w0, [x2, #0xb]
    // 0x820314: ldur            x3, [fp, #-0x98]
    // 0x820318: StoreField: r2->field_f = r3
    //     0x820318: stur            w3, [x2, #0xf]
    // 0x82031c: ldur            x4, [fp, #-0x90]
    // 0x820320: StoreField: r2->field_13 = r4
    //     0x820320: stur            w4, [x2, #0x13]
    // 0x820324: ldur            x5, [fp, #-0x88]
    // 0x820328: ArrayStore: r2[0] = r5  ; List_4
    //     0x820328: stur            w5, [x2, #0x17]
    // 0x82032c: ldur            x6, [fp, #-0x40]
    // 0x820330: LoadField: r1 = r6->field_b
    //     0x820330: ldur            w1, [x6, #0xb]
    // 0x820334: LoadField: r7 = r6->field_f
    //     0x820334: ldur            w7, [x6, #0xf]
    // 0x820338: DecompressPointer r7
    //     0x820338: add             x7, x7, HEAP, lsl #32
    // 0x82033c: LoadField: r8 = r7->field_b
    //     0x82033c: ldur            w8, [x7, #0xb]
    // 0x820340: r7 = LoadInt32Instr(r1)
    //     0x820340: sbfx            x7, x1, #1, #0x1f
    // 0x820344: stur            x7, [fp, #-0x70]
    // 0x820348: r1 = LoadInt32Instr(r8)
    //     0x820348: sbfx            x1, x8, #1, #0x1f
    // 0x82034c: cmp             x7, x1
    // 0x820350: b.ne            #0x82035c
    // 0x820354: mov             x1, x6
    // 0x820358: r0 = _growToNextCapacity()
    //     0x820358: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x82035c: ldur            x3, [fp, #-0x40]
    // 0x820360: ldur            x2, [fp, #-0x70]
    // 0x820364: add             x0, x2, #1
    // 0x820368: lsl             x1, x0, #1
    // 0x82036c: StoreField: r3->field_b = r1
    //     0x82036c: stur            w1, [x3, #0xb]
    // 0x820370: LoadField: r1 = r3->field_f
    //     0x820370: ldur            w1, [x3, #0xf]
    // 0x820374: DecompressPointer r1
    //     0x820374: add             x1, x1, HEAP, lsl #32
    // 0x820378: ldur            x0, [fp, #-0xc0]
    // 0x82037c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x82037c: add             x25, x1, x2, lsl #2
    //     0x820380: add             x25, x25, #0xf
    //     0x820384: str             w0, [x25]
    //     0x820388: tbz             w0, #0, #0x8203a4
    //     0x82038c: ldurb           w16, [x1, #-1]
    //     0x820390: ldurb           w17, [x0, #-1]
    //     0x820394: and             x16, x17, x16, lsr #2
    //     0x820398: tst             x16, HEAP, lsr #32
    //     0x82039c: b.eq            #0x8203a4
    //     0x8203a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8203a4: b               #0x8203ac
    // 0x8203a8: ldur            x3, [fp, #-0x40]
    // 0x8203ac: ldur            x1, [fp, #-0xa8]
    // 0x8203b0: r0 = LoadClassIdInstr(r1)
    //     0x8203b0: ldur            x0, [x1, #-1]
    //     0x8203b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8203b8: r2 = Instance_Duration
    //     0x8203b8: add             x2, PP, #9, lsl #12  ; [pp+0x92d8] Obj!Duration@e3a141
    //     0x8203bc: ldr             x2, [x2, #0x2d8]
    // 0x8203c0: r0 = GDT[cid_x0 + -0xf8f]()
    //     0x8203c0: sub             lr, x0, #0xf8f
    //     0x8203c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8203c8: blr             lr
    // 0x8203cc: mov             x10, x0
    // 0x8203d0: ldur            x3, [fp, #-0xb0]
    // 0x8203d4: b               #0x81ff6c
    // 0x8203d8: r0 = BoxInt64Instr(r6)
    //     0x8203d8: sbfiz           x0, x6, #1, #0x1f
    //     0x8203dc: cmp             x6, x0, asr #1
    //     0x8203e0: b.eq            #0x8203ec
    //     0x8203e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8203e8: stur            x6, [x0, #7]
    // 0x8203ec: cmp             w0, #0xa
    // 0x8203f0: b.ne            #0x820924
    // 0x8203f4: ldur            x3, [fp, #-0x20]
    // 0x8203f8: LoadField: r1 = r3->field_13
    //     0x8203f8: ldur            w1, [x3, #0x13]
    // 0x8203fc: DecompressPointer r1
    //     0x8203fc: add             x1, x1, HEAP, lsl #32
    // 0x820400: r0 = LoadClassIdInstr(r1)
    //     0x820400: ldur            x0, [x1, #-1]
    //     0x820404: ubfx            x0, x0, #0xc, #0x14
    // 0x820408: r0 = GDT[cid_x0 + -0xff6]()
    //     0x820408: sub             lr, x0, #0xff6
    //     0x82040c: ldr             lr, [x21, lr, lsl #3]
    //     0x820410: blr             lr
    // 0x820414: mov             x3, x0
    // 0x820418: ldur            x2, [fp, #-0x20]
    // 0x82041c: stur            x3, [fp, #-0x70]
    // 0x820420: LoadField: r1 = r2->field_13
    //     0x820420: ldur            w1, [x2, #0x13]
    // 0x820424: DecompressPointer r1
    //     0x820424: add             x1, x1, HEAP, lsl #32
    // 0x820428: r0 = LoadClassIdInstr(r1)
    //     0x820428: ldur            x0, [x1, #-1]
    //     0x82042c: ubfx            x0, x0, #0xc, #0x14
    // 0x820430: r0 = GDT[cid_x0 + -0xfff]()
    //     0x820430: sub             lr, x0, #0xfff
    //     0x820434: ldr             lr, [x21, lr, lsl #3]
    //     0x820438: blr             lr
    // 0x82043c: stur            x0, [fp, #-0x78]
    // 0x820440: r0 = DateTime()
    //     0x820440: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x820444: stur            x0, [fp, #-0xa8]
    // 0x820448: stp             xzr, xzr, [SP, #0x10]
    // 0x82044c: r16 = false
    //     0x82044c: add             x16, NULL, #0x30  ; false
    // 0x820450: stp             x16, xzr, [SP]
    // 0x820454: mov             x1, x0
    // 0x820458: ldur            x2, [fp, #-0x70]
    // 0x82045c: ldur            x3, [fp, #-0x78]
    // 0x820460: r5 = 1
    //     0x820460: movz            x5, #0x1
    // 0x820464: r6 = 0
    //     0x820464: movz            x6, #0
    // 0x820468: r7 = 0
    //     0x820468: movz            x7, #0
    // 0x82046c: r0 = DateTime._internal()
    //     0x82046c: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x820470: ldur            x0, [fp, #-0x48]
    // 0x820474: LoadField: r2 = r0->field_13
    //     0x820474: ldur            w2, [x0, #0x13]
    // 0x820478: DecompressPointer r2
    //     0x820478: add             x2, x2, HEAP, lsl #32
    // 0x82047c: stur            x2, [fp, #-0xb0]
    // 0x820480: ldur            x10, [fp, #-0xa8]
    // 0x820484: ldur            x3, [fp, #-0x20]
    // 0x820488: ldur            x4, [fp, #-0x40]
    // 0x82048c: ldur            x9, [fp, #-0x68]
    // 0x820490: ldur            x5, [fp, #-0xa0]
    // 0x820494: ldur            x6, [fp, #-0x98]
    // 0x820498: ldur            x7, [fp, #-0x90]
    // 0x82049c: ldur            x8, [fp, #-0x88]
    // 0x8204a0: stur            x10, [fp, #-0x48]
    // 0x8204a4: CheckStackOverflow
    //     0x8204a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8204a8: cmp             SP, x16
    //     0x8204ac: b.ls            #0x820a2c
    // 0x8204b0: r0 = LoadClassIdInstr(r10)
    //     0x8204b0: ldur            x0, [x10, #-1]
    //     0x8204b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8204b8: mov             x1, x10
    // 0x8204bc: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8204bc: sub             lr, x0, #0xfff
    //     0x8204c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8204c4: blr             lr
    // 0x8204c8: mov             x3, x0
    // 0x8204cc: ldur            x2, [fp, #-0x20]
    // 0x8204d0: stur            x3, [fp, #-0x70]
    // 0x8204d4: LoadField: r1 = r2->field_13
    //     0x8204d4: ldur            w1, [x2, #0x13]
    // 0x8204d8: DecompressPointer r1
    //     0x8204d8: add             x1, x1, HEAP, lsl #32
    // 0x8204dc: r0 = LoadClassIdInstr(r1)
    //     0x8204dc: ldur            x0, [x1, #-1]
    //     0x8204e0: ubfx            x0, x0, #0xc, #0x14
    // 0x8204e4: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8204e4: sub             lr, x0, #0xfff
    //     0x8204e8: ldr             lr, [x21, lr, lsl #3]
    //     0x8204ec: blr             lr
    // 0x8204f0: mov             x1, x0
    // 0x8204f4: ldur            x0, [fp, #-0x70]
    // 0x8204f8: cmp             x0, x1
    // 0x8204fc: b.ne            #0x820924
    // 0x820500: ldur            x2, [fp, #-0x48]
    // 0x820504: ldur            x0, [fp, #-0xb0]
    // 0x820508: ldur            x1, [fp, #-0x50]
    // 0x82050c: r0 = value()
    //     0x82050c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x820510: r1 = LoadInt32Instr(r0)
    //     0x820510: sbfx            x1, x0, #1, #0x1f
    //     0x820514: tbz             w0, #0, #0x82051c
    //     0x820518: ldur            x1, [x0, #7]
    // 0x82051c: r16 = 86400000000
    //     0x82051c: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x820520: ldr             x16, [x16, #0x268]
    // 0x820524: mul             x0, x1, x16
    // 0x820528: stur            x0, [fp, #-0x70]
    // 0x82052c: r0 = Duration()
    //     0x82052c: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x820530: mov             x1, x0
    // 0x820534: ldur            x0, [fp, #-0x70]
    // 0x820538: StoreField: r1->field_7 = r0
    //     0x820538: stur            x0, [x1, #7]
    // 0x82053c: ldur            x3, [fp, #-0x48]
    // 0x820540: r0 = LoadClassIdInstr(r3)
    //     0x820540: ldur            x0, [x3, #-1]
    //     0x820544: ubfx            x0, x0, #0xc, #0x14
    // 0x820548: mov             x2, x1
    // 0x82054c: mov             x1, x3
    // 0x820550: r0 = GDT[cid_x0 + -0xf8f]()
    //     0x820550: sub             lr, x0, #0xf8f
    //     0x820554: ldr             lr, [x21, lr, lsl #3]
    //     0x820558: blr             lr
    // 0x82055c: stur            x0, [fp, #-0xa8]
    // 0x820560: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x820560: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x820564: ldr             x0, [x0, #0x2728]
    //     0x820568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x82056c: cmp             w0, w16
    //     0x820570: b.ne            #0x82057c
    //     0x820574: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x820578: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x82057c: r16 = <HijriDate>
    //     0x82057c: ldr             x16, [PP, #0x7bc0]  ; [pp+0x7bc0] TypeArguments: <HijriDate>
    // 0x820580: stp             x0, x16, [SP, #8]
    // 0x820584: r16 = "v2_calendar"
    //     0x820584: ldr             x16, [PP, #0x7c68]  ; [pp+0x7c68] "v2_calendar"
    // 0x820588: str             x16, [SP]
    // 0x82058c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x82058c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x820590: r0 = box()
    //     0x820590: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x820594: ldur            x1, [fp, #-0x18]
    // 0x820598: stur            x0, [fp, #-0xb8]
    // 0x82059c: r0 = hijriMethod()
    //     0x82059c: bl              #0x817818  ; [package:nuonline/services/hijri_service.dart] HijriService::hijriMethod
    // 0x8205a0: stur            x0, [fp, #-0xc0]
    // 0x8205a4: r0 = NHijriCalendar()
    //     0x8205a4: bl              #0x814f84  ; AllocateNHijriCalendarStub -> NHijriCalendar (size=0x20)
    // 0x8205a8: mov             x1, x0
    // 0x8205ac: ldur            x2, [fp, #-0xa8]
    // 0x8205b0: ldur            x3, [fp, #-0xb8]
    // 0x8205b4: ldur            x5, [fp, #-0xc0]
    // 0x8205b8: stur            x0, [fp, #-0xa8]
    // 0x8205bc: r0 = NHijriCalendar.fromDate()
    //     0x8205bc: bl              #0x815680  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::NHijriCalendar.fromDate
    // 0x8205c0: ldur            x2, [fp, #-0x48]
    // 0x8205c4: r0 = LoadClassIdInstr(r2)
    //     0x8205c4: ldur            x0, [x2, #-1]
    //     0x8205c8: ubfx            x0, x0, #0xc, #0x14
    // 0x8205cc: mov             x1, x2
    // 0x8205d0: r0 = GDT[cid_x0 + -0xfad]()
    //     0x8205d0: sub             lr, x0, #0xfad
    //     0x8205d4: ldr             lr, [x21, lr, lsl #3]
    //     0x8205d8: blr             lr
    // 0x8205dc: mov             x2, x0
    // 0x8205e0: r0 = BoxInt64Instr(r2)
    //     0x8205e0: sbfiz           x0, x2, #1, #0x1f
    //     0x8205e4: cmp             x2, x0, asr #1
    //     0x8205e8: b.eq            #0x8205f4
    //     0x8205ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8205f0: stur            x2, [x0, #7]
    // 0x8205f4: ldur            x3, [fp, #-0xb0]
    // 0x8205f8: cmp             w0, w3
    // 0x8205fc: b.eq            #0x820638
    // 0x820600: and             w16, w0, w3
    // 0x820604: branchIfSmi(r16, 0x8208f4)
    //     0x820604: tbz             w16, #0, #0x8208f4
    // 0x820608: r16 = LoadClassIdInstr(r0)
    //     0x820608: ldur            x16, [x0, #-1]
    //     0x82060c: ubfx            x16, x16, #0xc, #0x14
    // 0x820610: cmp             x16, #0x3d
    // 0x820614: b.ne            #0x8208f4
    // 0x820618: r16 = LoadClassIdInstr(r3)
    //     0x820618: ldur            x16, [x3, #-1]
    //     0x82061c: ubfx            x16, x16, #0xc, #0x14
    // 0x820620: cmp             x16, #0x3d
    // 0x820624: b.ne            #0x8208f4
    // 0x820628: LoadField: r16 = r0->field_7
    //     0x820628: ldur            x16, [x0, #7]
    // 0x82062c: LoadField: r17 = r3->field_7
    //     0x82062c: ldur            x17, [x3, #7]
    // 0x820630: cmp             x16, x17
    // 0x820634: b.ne            #0x8208f4
    // 0x820638: ldur            x4, [fp, #-0x98]
    // 0x82063c: r16 = Instance_EventCategory
    //     0x82063c: add             x16, PP, #9, lsl #12  ; [pp+0x92d0] Obj!EventCategory@e310a1
    //     0x820640: ldr             x16, [x16, #0x2d0]
    // 0x820644: cmp             w4, w16
    // 0x820648: b.ne            #0x820788
    // 0x82064c: ldur            x0, [fp, #-0xa8]
    // 0x820650: LoadField: r5 = r0->field_f
    //     0x820650: ldur            w5, [x0, #0xf]
    // 0x820654: DecompressPointer r5
    //     0x820654: add             x5, x5, HEAP, lsl #32
    // 0x820658: r16 = Sentinel
    //     0x820658: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x82065c: cmp             w5, w16
    // 0x820660: b.eq            #0x820a34
    // 0x820664: stur            x5, [fp, #-0xb8]
    // 0x820668: cmp             w5, #0x12
    // 0x82066c: b.ne            #0x820678
    // 0x820670: ldur            x2, [fp, #-0x68]
    // 0x820674: b               #0x820780
    // 0x820678: ldur            x6, [fp, #-0x68]
    // 0x82067c: LoadField: r2 = r0->field_7
    //     0x82067c: ldur            x2, [x0, #7]
    // 0x820680: r0 = BoxInt64Instr(r2)
    //     0x820680: sbfiz           x0, x2, #1, #0x1f
    //     0x820684: cmp             x2, x0, asr #1
    //     0x820688: b.eq            #0x820694
    //     0x82068c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x820690: stur            x2, [x0, #7]
    // 0x820694: r1 = Null
    //     0x820694: mov             x1, NULL
    // 0x820698: r2 = 6
    //     0x820698: movz            x2, #0x6
    // 0x82069c: stur            x0, [fp, #-0xa8]
    // 0x8206a0: r0 = AllocateArray()
    //     0x8206a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8206a4: mov             x1, x0
    // 0x8206a8: ldur            x0, [fp, #-0xa8]
    // 0x8206ac: StoreField: r1->field_f = r0
    //     0x8206ac: stur            w0, [x1, #0xf]
    // 0x8206b0: r16 = "/"
    //     0x8206b0: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x8206b4: StoreField: r1->field_13 = r16
    //     0x8206b4: stur            w16, [x1, #0x13]
    // 0x8206b8: ldur            x0, [fp, #-0xb8]
    // 0x8206bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x8206bc: stur            w0, [x1, #0x17]
    // 0x8206c0: str             x1, [SP]
    // 0x8206c4: r0 = _interpolate()
    //     0x8206c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8206c8: mov             x3, x0
    // 0x8206cc: ldur            x2, [fp, #-0x68]
    // 0x8206d0: stur            x3, [fp, #-0xa8]
    // 0x8206d4: LoadField: r0 = r2->field_b
    //     0x8206d4: ldur            w0, [x2, #0xb]
    // 0x8206d8: r4 = LoadInt32Instr(r0)
    //     0x8206d8: sbfx            x4, x0, #1, #0x1f
    // 0x8206dc: stur            x4, [fp, #-0x78]
    // 0x8206e0: r1 = LoadInt32Instr(r0)
    //     0x8206e0: sbfx            x1, x0, #1, #0x1f
    // 0x8206e4: mov             x0, x1
    // 0x8206e8: r5 = 0
    //     0x8206e8: movz            x5, #0
    // 0x8206ec: stur            x5, [fp, #-0x70]
    // 0x8206f0: CheckStackOverflow
    //     0x8206f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8206f4: cmp             SP, x16
    //     0x8206f8: b.ls            #0x820a40
    // 0x8206fc: cmp             x5, x4
    // 0x820700: b.ge            #0x82078c
    // 0x820704: mov             x1, x5
    // 0x820708: cmp             x1, x0
    // 0x82070c: b.hs            #0x820a48
    // 0x820710: LoadField: r0 = r2->field_f
    //     0x820710: ldur            w0, [x2, #0xf]
    // 0x820714: DecompressPointer r0
    //     0x820714: add             x0, x0, HEAP, lsl #32
    // 0x820718: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x820718: add             x16, x0, x5, lsl #2
    //     0x82071c: ldur            w1, [x16, #0xf]
    // 0x820720: DecompressPointer r1
    //     0x820720: add             x1, x1, HEAP, lsl #32
    // 0x820724: r0 = 60
    //     0x820724: movz            x0, #0x3c
    // 0x820728: branchIfSmi(r1, 0x820734)
    //     0x820728: tbz             w1, #0, #0x820734
    // 0x82072c: r0 = LoadClassIdInstr(r1)
    //     0x82072c: ldur            x0, [x1, #-1]
    //     0x820730: ubfx            x0, x0, #0xc, #0x14
    // 0x820734: stp             x3, x1, [SP]
    // 0x820738: mov             lr, x0
    // 0x82073c: ldr             lr, [x21, lr, lsl #3]
    // 0x820740: blr             lr
    // 0x820744: tbz             w0, #4, #0x82077c
    // 0x820748: ldur            x2, [fp, #-0x68]
    // 0x82074c: ldur            x1, [fp, #-0x78]
    // 0x820750: LoadField: r0 = r2->field_b
    //     0x820750: ldur            w0, [x2, #0xb]
    // 0x820754: r3 = LoadInt32Instr(r0)
    //     0x820754: sbfx            x3, x0, #1, #0x1f
    // 0x820758: cmp             x1, x3
    // 0x82075c: b.ne            #0x82098c
    // 0x820760: ldur            x3, [fp, #-0x70]
    // 0x820764: add             x5, x3, #1
    // 0x820768: r3 = LoadInt32Instr(r0)
    //     0x820768: sbfx            x3, x0, #1, #0x1f
    // 0x82076c: mov             x0, x3
    // 0x820770: ldur            x3, [fp, #-0xa8]
    // 0x820774: mov             x4, x1
    // 0x820778: b               #0x8206ec
    // 0x82077c: ldur            x2, [fp, #-0x68]
    // 0x820780: ldur            x3, [fp, #-0x40]
    // 0x820784: b               #0x8208f8
    // 0x820788: ldur            x2, [fp, #-0x68]
    // 0x82078c: ldur            x5, [fp, #-0x20]
    // 0x820790: ldur            x6, [fp, #-0x40]
    // 0x820794: ldur            x7, [fp, #-0xa0]
    // 0x820798: ldur            x3, [fp, #-0x98]
    // 0x82079c: ldur            x8, [fp, #-0x90]
    // 0x8207a0: ldur            x9, [fp, #-0x88]
    // 0x8207a4: ldur            x4, [fp, #-0x48]
    // 0x8207a8: LoadField: r1 = r5->field_13
    //     0x8207a8: ldur            w1, [x5, #0x13]
    // 0x8207ac: DecompressPointer r1
    //     0x8207ac: add             x1, x1, HEAP, lsl #32
    // 0x8207b0: r0 = LoadClassIdInstr(r1)
    //     0x8207b0: ldur            x0, [x1, #-1]
    //     0x8207b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8207b8: r0 = GDT[cid_x0 + -0xff6]()
    //     0x8207b8: sub             lr, x0, #0xff6
    //     0x8207bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8207c0: blr             lr
    // 0x8207c4: mov             x3, x0
    // 0x8207c8: ldur            x2, [fp, #-0x20]
    // 0x8207cc: stur            x3, [fp, #-0x70]
    // 0x8207d0: LoadField: r1 = r2->field_13
    //     0x8207d0: ldur            w1, [x2, #0x13]
    // 0x8207d4: DecompressPointer r1
    //     0x8207d4: add             x1, x1, HEAP, lsl #32
    // 0x8207d8: r0 = LoadClassIdInstr(r1)
    //     0x8207d8: ldur            x0, [x1, #-1]
    //     0x8207dc: ubfx            x0, x0, #0xc, #0x14
    // 0x8207e0: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8207e0: sub             lr, x0, #0xfff
    //     0x8207e4: ldr             lr, [x21, lr, lsl #3]
    //     0x8207e8: blr             lr
    // 0x8207ec: mov             x3, x0
    // 0x8207f0: ldur            x2, [fp, #-0x48]
    // 0x8207f4: stur            x3, [fp, #-0x78]
    // 0x8207f8: r0 = LoadClassIdInstr(r2)
    //     0x8207f8: ldur            x0, [x2, #-1]
    //     0x8207fc: ubfx            x0, x0, #0xc, #0x14
    // 0x820800: mov             x1, x2
    // 0x820804: r0 = GDT[cid_x0 + -0xfdf]()
    //     0x820804: sub             lr, x0, #0xfdf
    //     0x820808: ldr             lr, [x21, lr, lsl #3]
    //     0x82080c: blr             lr
    // 0x820810: stur            x0, [fp, #-0x80]
    // 0x820814: r0 = DateTime()
    //     0x820814: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x820818: stur            x0, [fp, #-0xa8]
    // 0x82081c: stp             xzr, xzr, [SP, #0x10]
    // 0x820820: r16 = false
    //     0x820820: add             x16, NULL, #0x30  ; false
    // 0x820824: stp             x16, xzr, [SP]
    // 0x820828: mov             x1, x0
    // 0x82082c: ldur            x2, [fp, #-0x70]
    // 0x820830: ldur            x3, [fp, #-0x78]
    // 0x820834: ldur            x5, [fp, #-0x80]
    // 0x820838: r6 = 0
    //     0x820838: movz            x6, #0
    // 0x82083c: r7 = 0
    //     0x82083c: movz            x7, #0
    // 0x820840: r0 = DateTime._internal()
    //     0x820840: bl              #0x8174a0  ; [dart:core] DateTime::DateTime._internal
    // 0x820844: r0 = Event()
    //     0x820844: bl              #0x820fd8  ; AllocateEventStub -> Event (size=0x1c)
    // 0x820848: mov             x2, x0
    // 0x82084c: ldur            x0, [fp, #-0xa8]
    // 0x820850: stur            x2, [fp, #-0xb8]
    // 0x820854: StoreField: r2->field_7 = r0
    //     0x820854: stur            w0, [x2, #7]
    // 0x820858: ldur            x0, [fp, #-0xa0]
    // 0x82085c: StoreField: r2->field_b = r0
    //     0x82085c: stur            w0, [x2, #0xb]
    // 0x820860: ldur            x3, [fp, #-0x98]
    // 0x820864: StoreField: r2->field_f = r3
    //     0x820864: stur            w3, [x2, #0xf]
    // 0x820868: ldur            x4, [fp, #-0x90]
    // 0x82086c: StoreField: r2->field_13 = r4
    //     0x82086c: stur            w4, [x2, #0x13]
    // 0x820870: ldur            x5, [fp, #-0x88]
    // 0x820874: ArrayStore: r2[0] = r5  ; List_4
    //     0x820874: stur            w5, [x2, #0x17]
    // 0x820878: ldur            x6, [fp, #-0x40]
    // 0x82087c: LoadField: r1 = r6->field_b
    //     0x82087c: ldur            w1, [x6, #0xb]
    // 0x820880: LoadField: r7 = r6->field_f
    //     0x820880: ldur            w7, [x6, #0xf]
    // 0x820884: DecompressPointer r7
    //     0x820884: add             x7, x7, HEAP, lsl #32
    // 0x820888: LoadField: r8 = r7->field_b
    //     0x820888: ldur            w8, [x7, #0xb]
    // 0x82088c: r7 = LoadInt32Instr(r1)
    //     0x82088c: sbfx            x7, x1, #1, #0x1f
    // 0x820890: stur            x7, [fp, #-0x70]
    // 0x820894: r1 = LoadInt32Instr(r8)
    //     0x820894: sbfx            x1, x8, #1, #0x1f
    // 0x820898: cmp             x7, x1
    // 0x82089c: b.ne            #0x8208a8
    // 0x8208a0: mov             x1, x6
    // 0x8208a4: r0 = _growToNextCapacity()
    //     0x8208a4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8208a8: ldur            x3, [fp, #-0x40]
    // 0x8208ac: ldur            x2, [fp, #-0x70]
    // 0x8208b0: add             x0, x2, #1
    // 0x8208b4: lsl             x1, x0, #1
    // 0x8208b8: StoreField: r3->field_b = r1
    //     0x8208b8: stur            w1, [x3, #0xb]
    // 0x8208bc: LoadField: r1 = r3->field_f
    //     0x8208bc: ldur            w1, [x3, #0xf]
    // 0x8208c0: DecompressPointer r1
    //     0x8208c0: add             x1, x1, HEAP, lsl #32
    // 0x8208c4: ldur            x0, [fp, #-0xb8]
    // 0x8208c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8208c8: add             x25, x1, x2, lsl #2
    //     0x8208cc: add             x25, x25, #0xf
    //     0x8208d0: str             w0, [x25]
    //     0x8208d4: tbz             w0, #0, #0x8208f0
    //     0x8208d8: ldurb           w16, [x1, #-1]
    //     0x8208dc: ldurb           w17, [x0, #-1]
    //     0x8208e0: and             x16, x17, x16, lsr #2
    //     0x8208e4: tst             x16, HEAP, lsr #32
    //     0x8208e8: b.eq            #0x8208f0
    //     0x8208ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8208f0: b               #0x8208f8
    // 0x8208f4: ldur            x3, [fp, #-0x40]
    // 0x8208f8: ldur            x1, [fp, #-0x48]
    // 0x8208fc: r0 = LoadClassIdInstr(r1)
    //     0x8208fc: ldur            x0, [x1, #-1]
    //     0x820900: ubfx            x0, x0, #0xc, #0x14
    // 0x820904: r2 = Instance_Duration
    //     0x820904: add             x2, PP, #9, lsl #12  ; [pp+0x92d8] Obj!Duration@e3a141
    //     0x820908: ldr             x2, [x2, #0x2d8]
    // 0x82090c: r0 = GDT[cid_x0 + -0xf8f]()
    //     0x82090c: sub             lr, x0, #0xf8f
    //     0x820910: ldr             lr, [x21, lr, lsl #3]
    //     0x820914: blr             lr
    // 0x820918: mov             x10, x0
    // 0x82091c: ldur            x2, [fp, #-0xb0]
    // 0x820920: b               #0x820484
    // 0x820924: ldur            x0, [fp, #-0x30]
    // 0x820928: ldur            x7, [fp, #-0x18]
    // 0x82092c: ldur            x3, [fp, #-0x28]
    // 0x820930: ldur            x4, [fp, #-0x68]
    // 0x820934: ldur            x8, [fp, #-0x50]
    // 0x820938: ldur            x5, [fp, #-0x60]
    // 0x82093c: ldur            x6, [fp, #-0x58]
    // 0x820940: b               #0x81f840
    // 0x820944: r1 = Function '<anonymous closure>':.
    //     0x820944: add             x1, PP, #9, lsl #12  ; [pp+0x92e0] AnonymousClosure: (0x821074), in [package:nuonline/app/data/repositories/event_repository.dart] EventRepository::get (0x81f494)
    //     0x820948: ldr             x1, [x1, #0x2e0]
    // 0x82094c: r2 = Null
    //     0x82094c: mov             x2, NULL
    // 0x820950: r0 = AllocateClosure()
    //     0x820950: bl              #0xec1630  ; AllocateClosureStub
    // 0x820954: str             x0, [SP]
    // 0x820958: ldur            x1, [fp, #-0x40]
    // 0x82095c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x82095c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x820960: r0 = sort()
    //     0x820960: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x820964: ldur            x0, [fp, #-0x40]
    // 0x820968: r0 = ReturnAsyncNotFuture()
    //     0x820968: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x82096c: mov             x0, x2
    // 0x820970: r0 = ConcurrentModificationError()
    //     0x820970: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x820974: mov             x1, x0
    // 0x820978: ldur            x0, [fp, #-0x68]
    // 0x82097c: StoreField: r1->field_b = r0
    //     0x82097c: stur            w0, [x1, #0xb]
    // 0x820980: mov             x0, x1
    // 0x820984: r0 = Throw()
    //     0x820984: bl              #0xec04b8  ; ThrowStub
    // 0x820988: brk             #0
    // 0x82098c: mov             x0, x2
    // 0x820990: r0 = ConcurrentModificationError()
    //     0x820990: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x820994: mov             x1, x0
    // 0x820998: ldur            x0, [fp, #-0x68]
    // 0x82099c: StoreField: r1->field_b = r0
    //     0x82099c: stur            w0, [x1, #0xb]
    // 0x8209a0: mov             x0, x1
    // 0x8209a4: r0 = Throw()
    //     0x8209a4: bl              #0xec04b8  ; ThrowStub
    // 0x8209a8: brk             #0
    // 0x8209ac: mov             x0, x3
    // 0x8209b0: r0 = ConcurrentModificationError()
    //     0x8209b0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8209b4: mov             x1, x0
    // 0x8209b8: ldur            x0, [fp, #-0x28]
    // 0x8209bc: StoreField: r1->field_b = r0
    //     0x8209bc: stur            w0, [x1, #0xb]
    // 0x8209c0: mov             x0, x1
    // 0x8209c4: r0 = Throw()
    //     0x8209c4: bl              #0xec04b8  ; ThrowStub
    // 0x8209c8: brk             #0
    // 0x8209cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8209cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8209d0: b               #0x81f4b8
    // 0x8209d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8209d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8209d8: b               #0x81f85c
    // 0x8209dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8209dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8209e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8209e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8209e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8209e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8209e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8209e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8209ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8209ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8209f0: r9 = hYear
    //     0x8209f0: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x8209f4: ldr             x9, [x9, #0x270]
    // 0x8209f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8209f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8209fc: r9 = hYear
    //     0x8209fc: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x820a00: ldr             x9, [x9, #0x270]
    // 0x820a04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x820a04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x820a08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x820a08: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x820a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820a0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820a10: b               #0x81ff98
    // 0x820a14: r9 = hMonth
    //     0x820a14: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x820a18: ldr             x9, [x9, #0x278]
    // 0x820a1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x820a1c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x820a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820a24: b               #0x8201ac
    // 0x820a28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x820a28: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x820a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820a30: b               #0x8204b0
    // 0x820a34: r9 = hMonth
    //     0x820a34: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x820a38: ldr             x9, [x9, #0x278]
    // 0x820a3c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x820a3c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x820a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x820a40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x820a44: b               #0x8206fc
    // 0x820a48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x820a48: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, Event, Event) {
    // ** addr: 0x821074, size: 0x4c
    // 0x821074: EnterFrame
    //     0x821074: stp             fp, lr, [SP, #-0x10]!
    //     0x821078: mov             fp, SP
    // 0x82107c: CheckStackOverflow
    //     0x82107c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x821080: cmp             SP, x16
    //     0x821084: b.ls            #0x8210b8
    // 0x821088: ldr             x0, [fp, #0x18]
    // 0x82108c: LoadField: r1 = r0->field_7
    //     0x82108c: ldur            w1, [x0, #7]
    // 0x821090: DecompressPointer r1
    //     0x821090: add             x1, x1, HEAP, lsl #32
    // 0x821094: ldr             x0, [fp, #0x10]
    // 0x821098: LoadField: r2 = r0->field_7
    //     0x821098: ldur            w2, [x0, #7]
    // 0x82109c: DecompressPointer r2
    //     0x82109c: add             x2, x2, HEAP, lsl #32
    // 0x8210a0: r0 = compareTo()
    //     0x8210a0: bl              #0x665dbc  ; [dart:core] DateTime::compareTo
    // 0x8210a4: lsl             x1, x0, #1
    // 0x8210a8: mov             x0, x1
    // 0x8210ac: LeaveFrame
    //     0x8210ac: mov             SP, fp
    //     0x8210b0: ldp             fp, lr, [SP], #0x10
    // 0x8210b4: ret
    //     0x8210b4: ret             
    // 0x8210b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8210b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8210bc: b               #0x821088
  }
  [closure] bool <anonymous closure>(dynamic, EventConfig) {
    // ** addr: 0x8210c0, size: 0x1f0
    // 0x8210c0: EnterFrame
    //     0x8210c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8210c4: mov             fp, SP
    // 0x8210c8: AllocStack(0x18)
    //     0x8210c8: sub             SP, SP, #0x18
    // 0x8210cc: SetupParameters()
    //     0x8210cc: ldr             x0, [fp, #0x18]
    //     0x8210d0: ldur            w2, [x0, #0x17]
    //     0x8210d4: add             x2, x2, HEAP, lsl #32
    //     0x8210d8: stur            x2, [fp, #-0x10]
    // 0x8210dc: CheckStackOverflow
    //     0x8210dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8210e0: cmp             SP, x16
    //     0x8210e4: b.ls            #0x8212a8
    // 0x8210e8: ldr             x5, [fp, #0x10]
    // 0x8210ec: LoadField: r3 = r5->field_b
    //     0x8210ec: ldur            w3, [x5, #0xb]
    // 0x8210f0: DecompressPointer r3
    //     0x8210f0: add             x3, x3, HEAP, lsl #32
    // 0x8210f4: stur            x3, [fp, #-8]
    // 0x8210f8: cmp             w3, NULL
    // 0x8210fc: b.eq            #0x8211a0
    // 0x821100: LoadField: r1 = r2->field_13
    //     0x821100: ldur            w1, [x2, #0x13]
    // 0x821104: DecompressPointer r1
    //     0x821104: add             x1, x1, HEAP, lsl #32
    // 0x821108: r0 = LoadClassIdInstr(r1)
    //     0x821108: ldur            x0, [x1, #-1]
    //     0x82110c: ubfx            x0, x0, #0xc, #0x14
    // 0x821110: r0 = GDT[cid_x0 + -0xfff]()
    //     0x821110: sub             lr, x0, #0xfff
    //     0x821114: ldr             lr, [x21, lr, lsl #3]
    //     0x821118: blr             lr
    // 0x82111c: mov             x1, x0
    // 0x821120: ldur            x0, [fp, #-8]
    // 0x821124: r2 = LoadInt32Instr(r0)
    //     0x821124: sbfx            x2, x0, #1, #0x1f
    //     0x821128: tbz             w0, #0, #0x821130
    //     0x82112c: ldur            x2, [x0, #7]
    // 0x821130: cmp             x2, x1
    // 0x821134: b.ne            #0x821158
    // 0x821138: ldr             x4, [fp, #0x10]
    // 0x82113c: LoadField: r1 = r4->field_1b
    //     0x82113c: ldur            w1, [x4, #0x1b]
    // 0x821140: DecompressPointer r1
    //     0x821140: add             x1, x1, HEAP, lsl #32
    // 0x821144: r16 = Instance_EventType
    //     0x821144: add             x16, PP, #9, lsl #12  ; [pp+0x92e8] Obj!EventType@e30641
    //     0x821148: ldr             x16, [x16, #0x2e8]
    // 0x82114c: cmp             w1, w16
    // 0x821150: b.ne            #0x82115c
    // 0x821154: b               #0x8211a0
    // 0x821158: ldr             x4, [fp, #0x10]
    // 0x82115c: LoadField: r6 = r4->field_1b
    //     0x82115c: ldur            w6, [x4, #0x1b]
    // 0x821160: DecompressPointer r6
    //     0x821160: add             x6, x6, HEAP, lsl #32
    // 0x821164: stur            x6, [fp, #-0x18]
    // 0x821168: r16 = Instance_EventType
    //     0x821168: add             x16, PP, #9, lsl #12  ; [pp+0x92f0] Obj!EventType@e30621
    //     0x82116c: ldr             x16, [x16, #0x2f0]
    // 0x821170: cmp             w6, w16
    // 0x821174: b.ne            #0x8211a8
    // 0x821178: ldur            x7, [fp, #-0x10]
    // 0x82117c: LoadField: r1 = r7->field_f
    //     0x82117c: ldur            w1, [x7, #0xf]
    // 0x821180: DecompressPointer r1
    //     0x821180: add             x1, x1, HEAP, lsl #32
    // 0x821184: ArrayLoad: r2 = r7[0]  ; List_4
    //     0x821184: ldur            w2, [x7, #0x17]
    // 0x821188: DecompressPointer r2
    //     0x821188: add             x2, x2, HEAP, lsl #32
    // 0x82118c: LoadField: r3 = r7->field_1b
    //     0x82118c: ldur            w3, [x7, #0x1b]
    // 0x821190: DecompressPointer r3
    //     0x821190: add             x3, x3, HEAP, lsl #32
    // 0x821194: mov             x5, x4
    // 0x821198: r0 = isAnnualHijriEvent()
    //     0x821198: bl              #0x8212b0  ; [package:nuonline/app/data/repositories/event_repository.dart] EventRepository::isAnnualHijriEvent
    // 0x82119c: tbnz            w0, #4, #0x8211a8
    // 0x8211a0: r0 = true
    //     0x8211a0: add             x0, NULL, #0x20  ; true
    // 0x8211a4: b               #0x82129c
    // 0x8211a8: ldur            x0, [fp, #-0x18]
    // 0x8211ac: r16 = Instance_EventType
    //     0x8211ac: add             x16, PP, #9, lsl #12  ; [pp+0x92f8] Obj!EventType@e30601
    //     0x8211b0: ldr             x16, [x16, #0x2f8]
    // 0x8211b4: cmp             w0, w16
    // 0x8211b8: b.ne            #0x821298
    // 0x8211bc: ldur            x3, [fp, #-0x10]
    // 0x8211c0: ldur            x2, [fp, #-8]
    // 0x8211c4: LoadField: r1 = r3->field_13
    //     0x8211c4: ldur            w1, [x3, #0x13]
    // 0x8211c8: DecompressPointer r1
    //     0x8211c8: add             x1, x1, HEAP, lsl #32
    // 0x8211cc: r0 = LoadClassIdInstr(r1)
    //     0x8211cc: ldur            x0, [x1, #-1]
    //     0x8211d0: ubfx            x0, x0, #0xc, #0x14
    // 0x8211d4: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8211d4: sub             lr, x0, #0xfff
    //     0x8211d8: ldr             lr, [x21, lr, lsl #3]
    //     0x8211dc: blr             lr
    // 0x8211e0: mov             x1, x0
    // 0x8211e4: ldur            x0, [fp, #-8]
    // 0x8211e8: r2 = LoadInt32Instr(r0)
    //     0x8211e8: sbfx            x2, x0, #1, #0x1f
    //     0x8211ec: tbz             w0, #0, #0x8211f4
    //     0x8211f0: ldur            x2, [x0, #7]
    // 0x8211f4: cmp             x1, x2
    // 0x8211f8: b.ne            #0x821298
    // 0x8211fc: ldr             x2, [fp, #0x10]
    // 0x821200: ldur            x0, [fp, #-0x10]
    // 0x821204: LoadField: r1 = r0->field_13
    //     0x821204: ldur            w1, [x0, #0x13]
    // 0x821208: DecompressPointer r1
    //     0x821208: add             x1, x1, HEAP, lsl #32
    // 0x82120c: r0 = LoadClassIdInstr(r1)
    //     0x82120c: ldur            x0, [x1, #-1]
    //     0x821210: ubfx            x0, x0, #0xc, #0x14
    // 0x821214: r0 = GDT[cid_x0 + -0xff6]()
    //     0x821214: sub             lr, x0, #0xff6
    //     0x821218: ldr             lr, [x21, lr, lsl #3]
    //     0x82121c: blr             lr
    // 0x821220: mov             x3, x0
    // 0x821224: ldr             x2, [fp, #0x10]
    // 0x821228: LoadField: r4 = r2->field_7
    //     0x821228: ldur            w4, [x2, #7]
    // 0x82122c: DecompressPointer r4
    //     0x82122c: add             x4, x4, HEAP, lsl #32
    // 0x821230: r0 = BoxInt64Instr(r3)
    //     0x821230: sbfiz           x0, x3, #1, #0x1f
    //     0x821234: cmp             x3, x0, asr #1
    //     0x821238: b.eq            #0x821244
    //     0x82123c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x821240: stur            x3, [x0, #7]
    // 0x821244: cmp             w0, w4
    // 0x821248: b.eq            #0x82128c
    // 0x82124c: and             w16, w0, w4
    // 0x821250: branchIfSmi(r16, 0x821284)
    //     0x821250: tbz             w16, #0, #0x821284
    // 0x821254: r16 = LoadClassIdInstr(r0)
    //     0x821254: ldur            x16, [x0, #-1]
    //     0x821258: ubfx            x16, x16, #0xc, #0x14
    // 0x82125c: cmp             x16, #0x3d
    // 0x821260: b.ne            #0x821284
    // 0x821264: r16 = LoadClassIdInstr(r4)
    //     0x821264: ldur            x16, [x4, #-1]
    //     0x821268: ubfx            x16, x16, #0xc, #0x14
    // 0x82126c: cmp             x16, #0x3d
    // 0x821270: b.ne            #0x821284
    // 0x821274: LoadField: r16 = r0->field_7
    //     0x821274: ldur            x16, [x0, #7]
    // 0x821278: LoadField: r17 = r4->field_7
    //     0x821278: ldur            x17, [x4, #7]
    // 0x82127c: cmp             x16, x17
    // 0x821280: b.eq            #0x82128c
    // 0x821284: r1 = false
    //     0x821284: add             x1, NULL, #0x30  ; false
    // 0x821288: b               #0x821290
    // 0x82128c: r1 = true
    //     0x82128c: add             x1, NULL, #0x20  ; true
    // 0x821290: mov             x0, x1
    // 0x821294: b               #0x82129c
    // 0x821298: r0 = false
    //     0x821298: add             x0, NULL, #0x30  ; false
    // 0x82129c: LeaveFrame
    //     0x82129c: mov             SP, fp
    //     0x8212a0: ldp             fp, lr, [SP], #0x10
    // 0x8212a4: ret
    //     0x8212a4: ret             
    // 0x8212a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8212a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8212ac: b               #0x8210e8
  }
  _ isAnnualHijriEvent(/* No info */) {
    // ** addr: 0x8212b0, size: 0x170
    // 0x8212b0: EnterFrame
    //     0x8212b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8212b4: mov             fp, SP
    // 0x8212b8: AllocStack(0x20)
    //     0x8212b8: sub             SP, SP, #0x20
    // 0x8212bc: SetupParameters(dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x8212bc: mov             x4, x2
    //     0x8212c0: mov             x0, x3
    //     0x8212c4: stur            x2, [fp, #-0x18]
    //     0x8212c8: stur            x3, [fp, #-0x20]
    // 0x8212cc: CheckStackOverflow
    //     0x8212cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8212d0: cmp             SP, x16
    //     0x8212d4: b.ls            #0x8213f8
    // 0x8212d8: LoadField: r1 = r4->field_13
    //     0x8212d8: ldur            w1, [x4, #0x13]
    // 0x8212dc: DecompressPointer r1
    //     0x8212dc: add             x1, x1, HEAP, lsl #32
    // 0x8212e0: r16 = Sentinel
    //     0x8212e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8212e4: cmp             w1, w16
    // 0x8212e8: b.eq            #0x821400
    // 0x8212ec: LoadField: r2 = r5->field_b
    //     0x8212ec: ldur            w2, [x5, #0xb]
    // 0x8212f0: DecompressPointer r2
    //     0x8212f0: add             x2, x2, HEAP, lsl #32
    // 0x8212f4: cmp             w2, NULL
    // 0x8212f8: b.eq            #0x82140c
    // 0x8212fc: LoadField: r3 = r5->field_f
    //     0x8212fc: ldur            w3, [x5, #0xf]
    // 0x821300: DecompressPointer r3
    //     0x821300: add             x3, x3, HEAP, lsl #32
    // 0x821304: cmp             w3, NULL
    // 0x821308: b.eq            #0x821410
    // 0x82130c: r5 = LoadInt32Instr(r1)
    //     0x82130c: sbfx            x5, x1, #1, #0x1f
    //     0x821310: tbz             w1, #0, #0x821318
    //     0x821314: ldur            x5, [x1, #7]
    // 0x821318: r6 = LoadInt32Instr(r2)
    //     0x821318: sbfx            x6, x2, #1, #0x1f
    //     0x82131c: tbz             w2, #0, #0x821324
    //     0x821320: ldur            x6, [x2, #7]
    // 0x821324: stur            x6, [fp, #-0x10]
    // 0x821328: r7 = LoadInt32Instr(r3)
    //     0x821328: sbfx            x7, x3, #1, #0x1f
    //     0x82132c: tbz             w3, #0, #0x821334
    //     0x821330: ldur            x7, [x3, #7]
    // 0x821334: mov             x1, x0
    // 0x821338: mov             x2, x5
    // 0x82133c: mov             x3, x6
    // 0x821340: mov             x5, x7
    // 0x821344: stur            x7, [fp, #-8]
    // 0x821348: r0 = isAfter()
    //     0x821348: bl              #0x821520  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::isAfter
    // 0x82134c: tbnz            w0, #4, #0x821384
    // 0x821350: ldur            x0, [fp, #-0x18]
    // 0x821354: LoadField: r1 = r0->field_13
    //     0x821354: ldur            w1, [x0, #0x13]
    // 0x821358: DecompressPointer r1
    //     0x821358: add             x1, x1, HEAP, lsl #32
    // 0x82135c: r2 = LoadInt32Instr(r1)
    //     0x82135c: sbfx            x2, x1, #1, #0x1f
    //     0x821360: tbz             w1, #0, #0x821368
    //     0x821364: ldur            x2, [x1, #7]
    // 0x821368: mov             x1, x0
    // 0x82136c: ldur            x3, [fp, #-0x10]
    // 0x821370: ldur            x5, [fp, #-8]
    // 0x821374: r0 = isBefore()
    //     0x821374: bl              #0x821420  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::isBefore
    // 0x821378: tbnz            w0, #4, #0x821384
    // 0x82137c: r0 = true
    //     0x82137c: add             x0, NULL, #0x20  ; true
    // 0x821380: b               #0x8213ec
    // 0x821384: ldur            x0, [fp, #-0x20]
    // 0x821388: LoadField: r1 = r0->field_13
    //     0x821388: ldur            w1, [x0, #0x13]
    // 0x82138c: DecompressPointer r1
    //     0x82138c: add             x1, x1, HEAP, lsl #32
    // 0x821390: r16 = Sentinel
    //     0x821390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x821394: cmp             w1, w16
    // 0x821398: b.eq            #0x821414
    // 0x82139c: r2 = LoadInt32Instr(r1)
    //     0x82139c: sbfx            x2, x1, #1, #0x1f
    //     0x8213a0: tbz             w1, #0, #0x8213a8
    //     0x8213a4: ldur            x2, [x1, #7]
    // 0x8213a8: mov             x1, x0
    // 0x8213ac: ldur            x3, [fp, #-0x10]
    // 0x8213b0: ldur            x5, [fp, #-8]
    // 0x8213b4: r0 = isAfter()
    //     0x8213b4: bl              #0x821520  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::isAfter
    // 0x8213b8: tbnz            w0, #4, #0x8213e8
    // 0x8213bc: ldur            x0, [fp, #-0x20]
    // 0x8213c0: LoadField: r1 = r0->field_13
    //     0x8213c0: ldur            w1, [x0, #0x13]
    // 0x8213c4: DecompressPointer r1
    //     0x8213c4: add             x1, x1, HEAP, lsl #32
    // 0x8213c8: r2 = LoadInt32Instr(r1)
    //     0x8213c8: sbfx            x2, x1, #1, #0x1f
    //     0x8213cc: tbz             w1, #0, #0x8213d4
    //     0x8213d0: ldur            x2, [x1, #7]
    // 0x8213d4: ldur            x1, [fp, #-0x18]
    // 0x8213d8: ldur            x3, [fp, #-0x10]
    // 0x8213dc: ldur            x5, [fp, #-8]
    // 0x8213e0: r0 = isBefore()
    //     0x8213e0: bl              #0x821420  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::isBefore
    // 0x8213e4: b               #0x8213ec
    // 0x8213e8: r0 = false
    //     0x8213e8: add             x0, NULL, #0x30  ; false
    // 0x8213ec: LeaveFrame
    //     0x8213ec: mov             SP, fp
    //     0x8213f0: ldp             fp, lr, [SP], #0x10
    // 0x8213f4: ret
    //     0x8213f4: ret             
    // 0x8213f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8213f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8213fc: b               #0x8212d8
    // 0x821400: r9 = hYear
    //     0x821400: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x821404: ldr             x9, [x9, #0x270]
    // 0x821408: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x821408: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x82140c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x82140c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x821410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x821410: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x821414: r9 = hYear
    //     0x821414: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x821418: ldr             x9, [x9, #0x270]
    // 0x82141c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x82141c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] EventConfig <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x821620, size: 0x50
    // 0x821620: EnterFrame
    //     0x821620: stp             fp, lr, [SP, #-0x10]!
    //     0x821624: mov             fp, SP
    // 0x821628: CheckStackOverflow
    //     0x821628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82162c: cmp             SP, x16
    //     0x821630: b.ls            #0x821668
    // 0x821634: ldr             x0, [fp, #0x10]
    // 0x821638: r2 = Null
    //     0x821638: mov             x2, NULL
    // 0x82163c: r1 = Null
    //     0x82163c: mov             x1, NULL
    // 0x821640: r8 = Map<String, dynamic>
    //     0x821640: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x821644: r3 = Null
    //     0x821644: add             x3, PP, #9, lsl #12  ; [pp+0x9300] Null
    //     0x821648: ldr             x3, [x3, #0x300]
    // 0x82164c: r0 = Map<String, dynamic>()
    //     0x82164c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x821650: ldr             x2, [fp, #0x10]
    // 0x821654: r1 = Null
    //     0x821654: mov             x1, NULL
    // 0x821658: r0 = EventConfig.fromMap()
    //     0x821658: bl              #0x821670  ; [package:nuonline/app/data/models/event_config.dart] EventConfig::EventConfig.fromMap
    // 0x82165c: LeaveFrame
    //     0x82165c: mov             SP, fp
    //     0x821660: ldp             fp, lr, [SP], #0x10
    // 0x821664: ret
    //     0x821664: ret             
    // 0x821668: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x821668: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82166c: b               #0x821634
  }
}
