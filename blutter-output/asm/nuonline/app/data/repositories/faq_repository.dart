// lib: , url: package:nuonline/app/data/repositories/faq_repository.dart

// class id: 1050082, size: 0x8
class :: {
}

// class id: 1094, size: 0xc, field offset: 0x8
class FaqRepository extends Object {

  _ findAll(/* No info */) async {
    // ** addr: 0x7e8320, size: 0x2a8
    // 0x7e8320: EnterFrame
    //     0x7e8320: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8324: mov             fp, SP
    // 0x7e8328: AllocStack(0xc8)
    //     0x7e8328: sub             SP, SP, #0xc8
    // 0x7e832c: SetupParameters(FaqRepository this /* r1 => r1, fp-0x90 */, dynamic _ /* r2 => r2, fp-0x98 */, dynamic _ /* r3 => r3, fp-0xa0 */, {dynamic slug = Null /* r5, fp-0x88 */})
    //     0x7e832c: stur            NULL, [fp, #-8]
    //     0x7e8330: stur            x1, [fp, #-0x90]
    //     0x7e8334: stur            x2, [fp, #-0x98]
    //     0x7e8338: stur            x3, [fp, #-0xa0]
    //     0x7e833c: stur            x4, [fp, #-0xa8]
    //     0x7e8340: ldur            w0, [x4, #0x13]
    //     0x7e8344: ldur            w5, [x4, #0x1f]
    //     0x7e8348: add             x5, x5, HEAP, lsl #32
    //     0x7e834c: add             x16, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0x7e8350: ldr             x16, [x16, #0x9a8]
    //     0x7e8354: cmp             w5, w16
    //     0x7e8358: b.ne            #0x7e8378
    //     0x7e835c: ldur            w5, [x4, #0x23]
    //     0x7e8360: add             x5, x5, HEAP, lsl #32
    //     0x7e8364: sub             w6, w0, w5
    //     0x7e8368: add             x0, fp, w6, sxtw #2
    //     0x7e836c: ldr             x0, [x0, #8]
    //     0x7e8370: mov             x5, x0
    //     0x7e8374: b               #0x7e837c
    //     0x7e8378: mov             x5, NULL
    //     0x7e837c: stur            x5, [fp, #-0x88]
    // 0x7e8380: CheckStackOverflow
    //     0x7e8380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8384: cmp             SP, x16
    //     0x7e8388: b.ls            #0x7e85c0
    // 0x7e838c: InitAsync() -> Future<ApiResult<List<Faq>>>
    //     0x7e838c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34878] TypeArguments: <ApiResult<List<Faq>>>
    //     0x7e8390: ldr             x0, [x0, #0x878]
    //     0x7e8394: bl              #0x661298  ; InitAsyncStub
    // 0x7e8398: ldur            x1, [fp, #-0x90]
    // 0x7e839c: ldur            x3, [fp, #-0x98]
    // 0x7e83a0: ldur            x0, [fp, #-0xa0]
    // 0x7e83a4: ldur            x4, [fp, #-0x88]
    // 0x7e83a8: LoadField: r5 = r1->field_7
    //     0x7e83a8: ldur            w5, [x1, #7]
    // 0x7e83ac: DecompressPointer r5
    //     0x7e83ac: add             x5, x5, HEAP, lsl #32
    // 0x7e83b0: stur            x5, [fp, #-0xa8]
    // 0x7e83b4: r1 = Null
    //     0x7e83b4: mov             x1, NULL
    // 0x7e83b8: r2 = 16
    //     0x7e83b8: movz            x2, #0x10
    // 0x7e83bc: r0 = AllocateArray()
    //     0x7e83bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7e83c0: mov             x2, x0
    // 0x7e83c4: r16 = "category"
    //     0x7e83c4: add             x16, PP, #8, lsl #12  ; [pp+0x8960] "category"
    //     0x7e83c8: ldr             x16, [x16, #0x960]
    // 0x7e83cc: StoreField: r2->field_f = r16
    //     0x7e83cc: stur            w16, [x2, #0xf]
    // 0x7e83d0: ldur            x0, [fp, #-0x88]
    // 0x7e83d4: StoreField: r2->field_13 = r0
    //     0x7e83d4: stur            w0, [x2, #0x13]
    // 0x7e83d8: r16 = "page"
    //     0x7e83d8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0x7e83dc: ldr             x16, [x16, #0x300]
    // 0x7e83e0: ArrayStore: r2[0] = r16  ; List_4
    //     0x7e83e0: stur            w16, [x2, #0x17]
    // 0x7e83e4: ldur            x3, [fp, #-0x98]
    // 0x7e83e8: r0 = BoxInt64Instr(r3)
    //     0x7e83e8: sbfiz           x0, x3, #1, #0x1f
    //     0x7e83ec: cmp             x3, x0, asr #1
    //     0x7e83f0: b.eq            #0x7e83fc
    //     0x7e83f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7e83f8: stur            x3, [x0, #7]
    // 0x7e83fc: StoreField: r2->field_1b = r0
    //     0x7e83fc: stur            w0, [x2, #0x1b]
    // 0x7e8400: r16 = "limit"
    //     0x7e8400: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0x7e8404: ldr             x16, [x16, #0x50]
    // 0x7e8408: StoreField: r2->field_1f = r16
    //     0x7e8408: stur            w16, [x2, #0x1f]
    // 0x7e840c: r16 = 20
    //     0x7e840c: movz            x16, #0x14
    // 0x7e8410: StoreField: r2->field_23 = r16
    //     0x7e8410: stur            w16, [x2, #0x23]
    // 0x7e8414: r16 = "q"
    //     0x7e8414: add             x16, PP, #0x29, lsl #12  ; [pp+0x291d0] "q"
    //     0x7e8418: ldr             x16, [x16, #0x1d0]
    // 0x7e841c: StoreField: r2->field_27 = r16
    //     0x7e841c: stur            w16, [x2, #0x27]
    // 0x7e8420: ldur            x0, [fp, #-0xa0]
    // 0x7e8424: StoreField: r2->field_2b = r0
    //     0x7e8424: stur            w0, [x2, #0x2b]
    // 0x7e8428: r16 = <String, Object?>
    //     0x7e8428: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0x7e842c: ldr             x16, [x16, #0x738]
    // 0x7e8430: stp             x2, x16, [SP]
    // 0x7e8434: r0 = Map._fromLiteral()
    //     0x7e8434: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7e8438: ldur            x16, [fp, #-0xa8]
    // 0x7e843c: stp             x16, NULL, [SP, #0x10]
    // 0x7e8440: r16 = "/faq"
    //     0x7e8440: add             x16, PP, #0x24, lsl #12  ; [pp+0x247e0] "/faq"
    //     0x7e8444: ldr             x16, [x16, #0x7e0]
    // 0x7e8448: stp             x0, x16, [SP]
    // 0x7e844c: r4 = const [0x1, 0x3, 0x3, 0x2, data, 0x2, null]
    //     0x7e844c: add             x4, PP, #0x10, lsl #12  ; [pp+0x10428] List(7) [0x1, 0x3, 0x3, 0x2, "data", 0x2, Null]
    //     0x7e8450: ldr             x4, [x4, #0x428]
    // 0x7e8454: r0 = get()
    //     0x7e8454: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7e8458: mov             x1, x0
    // 0x7e845c: stur            x1, [fp, #-0x88]
    // 0x7e8460: r0 = Await()
    //     0x7e8460: bl              #0x661044  ; AwaitStub
    // 0x7e8464: mov             x3, x0
    // 0x7e8468: stur            x3, [fp, #-0x90]
    // 0x7e846c: LoadField: r4 = r3->field_b
    //     0x7e846c: ldur            w4, [x3, #0xb]
    // 0x7e8470: DecompressPointer r4
    //     0x7e8470: add             x4, x4, HEAP, lsl #32
    // 0x7e8474: mov             x0, x4
    // 0x7e8478: stur            x4, [fp, #-0x88]
    // 0x7e847c: r2 = Null
    //     0x7e847c: mov             x2, NULL
    // 0x7e8480: r1 = Null
    //     0x7e8480: mov             x1, NULL
    // 0x7e8484: r4 = 60
    //     0x7e8484: movz            x4, #0x3c
    // 0x7e8488: branchIfSmi(r0, 0x7e8494)
    //     0x7e8488: tbz             w0, #0, #0x7e8494
    // 0x7e848c: r4 = LoadClassIdInstr(r0)
    //     0x7e848c: ldur            x4, [x0, #-1]
    //     0x7e8490: ubfx            x4, x4, #0xc, #0x14
    // 0x7e8494: sub             x4, x4, #0x5a
    // 0x7e8498: cmp             x4, #2
    // 0x7e849c: b.ls            #0x7e84b4
    // 0x7e84a0: r8 = List?
    //     0x7e84a0: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7e84a4: ldr             x8, [x8, #0x140]
    // 0x7e84a8: r3 = Null
    //     0x7e84a8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34888] Null
    //     0x7e84ac: ldr             x3, [x3, #0x888]
    // 0x7e84b0: r0 = List?()
    //     0x7e84b0: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7e84b4: ldur            x0, [fp, #-0x88]
    // 0x7e84b8: cmp             w0, NULL
    // 0x7e84bc: b.ne            #0x7e84d4
    // 0x7e84c0: r1 = Null
    //     0x7e84c0: mov             x1, NULL
    // 0x7e84c4: r2 = 0
    //     0x7e84c4: movz            x2, #0
    // 0x7e84c8: r0 = _GrowableList()
    //     0x7e84c8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e84cc: mov             x3, x0
    // 0x7e84d0: b               #0x7e84d8
    // 0x7e84d4: mov             x3, x0
    // 0x7e84d8: ldur            x0, [fp, #-0x90]
    // 0x7e84dc: stur            x3, [fp, #-0x88]
    // 0x7e84e0: r1 = Function '<anonymous closure>':.
    //     0x7e84e0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34898] AnonymousClosure: (0x7e866c), in [package:nuonline/app/data/repositories/faq_repository.dart] FaqRepository::findAll (0x7e8320)
    //     0x7e84e4: ldr             x1, [x1, #0x898]
    // 0x7e84e8: r2 = Null
    //     0x7e84e8: mov             x2, NULL
    // 0x7e84ec: r0 = AllocateClosure()
    //     0x7e84ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e84f0: mov             x1, x0
    // 0x7e84f4: ldur            x0, [fp, #-0x88]
    // 0x7e84f8: r2 = LoadClassIdInstr(r0)
    //     0x7e84f8: ldur            x2, [x0, #-1]
    //     0x7e84fc: ubfx            x2, x2, #0xc, #0x14
    // 0x7e8500: r16 = <Faq>
    //     0x7e8500: add             x16, PP, #0x34, lsl #12  ; [pp+0x348a0] TypeArguments: <Faq>
    //     0x7e8504: ldr             x16, [x16, #0x8a0]
    // 0x7e8508: stp             x0, x16, [SP, #8]
    // 0x7e850c: str             x1, [SP]
    // 0x7e8510: mov             x0, x2
    // 0x7e8514: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e8514: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e8518: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7e8518: movz            x17, #0xf28c
    //     0x7e851c: add             lr, x0, x17
    //     0x7e8520: ldr             lr, [x21, lr, lsl #3]
    //     0x7e8524: blr             lr
    // 0x7e8528: r1 = LoadClassIdInstr(r0)
    //     0x7e8528: ldur            x1, [x0, #-1]
    //     0x7e852c: ubfx            x1, x1, #0xc, #0x14
    // 0x7e8530: mov             x16, x0
    // 0x7e8534: mov             x0, x1
    // 0x7e8538: mov             x1, x16
    // 0x7e853c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7e853c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7e8540: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7e8540: movz            x17, #0xd889
    //     0x7e8544: add             lr, x0, x17
    //     0x7e8548: ldr             lr, [x21, lr, lsl #3]
    //     0x7e854c: blr             lr
    // 0x7e8550: mov             x3, x0
    // 0x7e8554: ldur            x0, [fp, #-0x90]
    // 0x7e8558: stur            x3, [fp, #-0x88]
    // 0x7e855c: LoadField: r2 = r0->field_1b
    //     0x7e855c: ldur            w2, [x0, #0x1b]
    // 0x7e8560: DecompressPointer r2
    //     0x7e8560: add             x2, x2, HEAP, lsl #32
    // 0x7e8564: r1 = Null
    //     0x7e8564: mov             x1, NULL
    // 0x7e8568: r0 = Pagination.fromHeaders()
    //     0x7e8568: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x7e856c: r1 = <List<Faq>>
    //     0x7e856c: add             x1, PP, #0x34, lsl #12  ; [pp+0x348a8] TypeArguments: <List<Faq>>
    //     0x7e8570: ldr             x1, [x1, #0x8a8]
    // 0x7e8574: stur            x0, [fp, #-0x90]
    // 0x7e8578: r0 = _$SuccessImpl()
    //     0x7e8578: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e857c: mov             x1, x0
    // 0x7e8580: ldur            x0, [fp, #-0x88]
    // 0x7e8584: StoreField: r1->field_b = r0
    //     0x7e8584: stur            w0, [x1, #0xb]
    // 0x7e8588: ldur            x0, [fp, #-0x90]
    // 0x7e858c: StoreField: r1->field_f = r0
    //     0x7e858c: stur            w0, [x1, #0xf]
    // 0x7e8590: mov             x0, x1
    // 0x7e8594: r0 = ReturnAsyncNotFuture()
    //     0x7e8594: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e8598: sub             SP, fp, #0xc8
    // 0x7e859c: mov             x1, x0
    // 0x7e85a0: r0 = getDioException()
    //     0x7e85a0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e85a4: r1 = <List<Faq>>
    //     0x7e85a4: add             x1, PP, #0x34, lsl #12  ; [pp+0x348a8] TypeArguments: <List<Faq>>
    //     0x7e85a8: ldr             x1, [x1, #0x8a8]
    // 0x7e85ac: stur            x0, [fp, #-0x88]
    // 0x7e85b0: r0 = _$FailureImpl()
    //     0x7e85b0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e85b4: ldur            x1, [fp, #-0x88]
    // 0x7e85b8: StoreField: r0->field_b = r1
    //     0x7e85b8: stur            w1, [x0, #0xb]
    // 0x7e85bc: r0 = ReturnAsyncNotFuture()
    //     0x7e85bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e85c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e85c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e85c4: b               #0x7e838c
  }
  [closure] Faq <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7e866c, size: 0x50
    // 0x7e866c: EnterFrame
    //     0x7e866c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e8670: mov             fp, SP
    // 0x7e8674: CheckStackOverflow
    //     0x7e8674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e8678: cmp             SP, x16
    //     0x7e867c: b.ls            #0x7e86b4
    // 0x7e8680: ldr             x0, [fp, #0x10]
    // 0x7e8684: r2 = Null
    //     0x7e8684: mov             x2, NULL
    // 0x7e8688: r1 = Null
    //     0x7e8688: mov             x1, NULL
    // 0x7e868c: r8 = Map<String, dynamic>
    //     0x7e868c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e8690: r3 = Null
    //     0x7e8690: add             x3, PP, #0x34, lsl #12  ; [pp+0x348b0] Null
    //     0x7e8694: ldr             x3, [x3, #0x8b0]
    // 0x7e8698: r0 = Map<String, dynamic>()
    //     0x7e8698: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e869c: ldr             x2, [fp, #0x10]
    // 0x7e86a0: r1 = Null
    //     0x7e86a0: mov             x1, NULL
    // 0x7e86a4: r0 = Faq.fromMap()
    //     0x7e86a4: bl              #0x7e86bc  ; [package:nuonline/app/data/models/faq.dart] Faq::Faq.fromMap
    // 0x7e86a8: LeaveFrame
    //     0x7e86a8: mov             SP, fp
    //     0x7e86ac: ldp             fp, lr, [SP], #0x10
    // 0x7e86b0: ret
    //     0x7e86b0: ret             
    // 0x7e86b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e86b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e86b8: b               #0x7e8680
  }
  _ findAllCategory(/* No info */) async {
    // ** addr: 0x7e9018, size: 0x184
    // 0x7e9018: EnterFrame
    //     0x7e9018: stp             fp, lr, [SP, #-0x10]!
    //     0x7e901c: mov             fp, SP
    // 0x7e9020: AllocStack(0x78)
    //     0x7e9020: sub             SP, SP, #0x78
    // 0x7e9024: SetupParameters(FaqRepository this /* r1 => r1, fp-0x60 */)
    //     0x7e9024: stur            NULL, [fp, #-8]
    //     0x7e9028: stur            x1, [fp, #-0x60]
    // 0x7e902c: CheckStackOverflow
    //     0x7e902c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9030: cmp             SP, x16
    //     0x7e9034: b.ls            #0x7e9194
    // 0x7e9038: InitAsync() -> Future<ApiResult<List<FaqCategory>>>
    //     0x7e9038: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3feb8] TypeArguments: <ApiResult<List<FaqCategory>>>
    //     0x7e903c: ldr             x0, [x0, #0xeb8]
    //     0x7e9040: bl              #0x661298  ; InitAsyncStub
    // 0x7e9044: ldur            x0, [fp, #-0x60]
    // 0x7e9048: LoadField: r1 = r0->field_7
    //     0x7e9048: ldur            w1, [x0, #7]
    // 0x7e904c: DecompressPointer r1
    //     0x7e904c: add             x1, x1, HEAP, lsl #32
    // 0x7e9050: stp             x1, NULL, [SP, #8]
    // 0x7e9054: r16 = "/faq/categories"
    //     0x7e9054: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fec0] "/faq/categories"
    //     0x7e9058: ldr             x16, [x16, #0xec0]
    // 0x7e905c: str             x16, [SP]
    // 0x7e9060: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e9060: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e9064: r0 = get()
    //     0x7e9064: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7e9068: mov             x1, x0
    // 0x7e906c: stur            x1, [fp, #-0x60]
    // 0x7e9070: r0 = Await()
    //     0x7e9070: bl              #0x661044  ; AwaitStub
    // 0x7e9074: LoadField: r3 = r0->field_b
    //     0x7e9074: ldur            w3, [x0, #0xb]
    // 0x7e9078: DecompressPointer r3
    //     0x7e9078: add             x3, x3, HEAP, lsl #32
    // 0x7e907c: mov             x0, x3
    // 0x7e9080: stur            x3, [fp, #-0x60]
    // 0x7e9084: r2 = Null
    //     0x7e9084: mov             x2, NULL
    // 0x7e9088: r1 = Null
    //     0x7e9088: mov             x1, NULL
    // 0x7e908c: r4 = 60
    //     0x7e908c: movz            x4, #0x3c
    // 0x7e9090: branchIfSmi(r0, 0x7e909c)
    //     0x7e9090: tbz             w0, #0, #0x7e909c
    // 0x7e9094: r4 = LoadClassIdInstr(r0)
    //     0x7e9094: ldur            x4, [x0, #-1]
    //     0x7e9098: ubfx            x4, x4, #0xc, #0x14
    // 0x7e909c: sub             x4, x4, #0x5a
    // 0x7e90a0: cmp             x4, #2
    // 0x7e90a4: b.ls            #0x7e90bc
    // 0x7e90a8: r8 = List?
    //     0x7e90a8: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x7e90ac: ldr             x8, [x8, #0x140]
    // 0x7e90b0: r3 = Null
    //     0x7e90b0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fec8] Null
    //     0x7e90b4: ldr             x3, [x3, #0xec8]
    // 0x7e90b8: r0 = List?()
    //     0x7e90b8: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x7e90bc: ldur            x0, [fp, #-0x60]
    // 0x7e90c0: cmp             w0, NULL
    // 0x7e90c4: b.ne            #0x7e90d4
    // 0x7e90c8: r1 = Null
    //     0x7e90c8: mov             x1, NULL
    // 0x7e90cc: r2 = 0
    //     0x7e90cc: movz            x2, #0
    // 0x7e90d0: r0 = _GrowableList()
    //     0x7e90d0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x7e90d4: stur            x0, [fp, #-0x60]
    // 0x7e90d8: r1 = Function '<anonymous closure>':.
    //     0x7e90d8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fed8] AnonymousClosure: (0x7e919c), in [package:nuonline/app/data/repositories/faq_repository.dart] FaqRepository::findAllCategory (0x7e9018)
    //     0x7e90dc: ldr             x1, [x1, #0xed8]
    // 0x7e90e0: r2 = Null
    //     0x7e90e0: mov             x2, NULL
    // 0x7e90e4: r0 = AllocateClosure()
    //     0x7e90e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e90e8: mov             x1, x0
    // 0x7e90ec: ldur            x0, [fp, #-0x60]
    // 0x7e90f0: r2 = LoadClassIdInstr(r0)
    //     0x7e90f0: ldur            x2, [x0, #-1]
    //     0x7e90f4: ubfx            x2, x2, #0xc, #0x14
    // 0x7e90f8: r16 = <FaqCategory>
    //     0x7e90f8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fee0] TypeArguments: <FaqCategory>
    //     0x7e90fc: ldr             x16, [x16, #0xee0]
    // 0x7e9100: stp             x0, x16, [SP, #8]
    // 0x7e9104: str             x1, [SP]
    // 0x7e9108: mov             x0, x2
    // 0x7e910c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e910c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e9110: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7e9110: movz            x17, #0xf28c
    //     0x7e9114: add             lr, x0, x17
    //     0x7e9118: ldr             lr, [x21, lr, lsl #3]
    //     0x7e911c: blr             lr
    // 0x7e9120: r1 = LoadClassIdInstr(r0)
    //     0x7e9120: ldur            x1, [x0, #-1]
    //     0x7e9124: ubfx            x1, x1, #0xc, #0x14
    // 0x7e9128: mov             x16, x0
    // 0x7e912c: mov             x0, x1
    // 0x7e9130: mov             x1, x16
    // 0x7e9134: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7e9134: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7e9138: r0 = GDT[cid_x0 + 0xd889]()
    //     0x7e9138: movz            x17, #0xd889
    //     0x7e913c: add             lr, x0, x17
    //     0x7e9140: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9144: blr             lr
    // 0x7e9148: r1 = <List<FaqCategory>>
    //     0x7e9148: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f018] TypeArguments: <List<FaqCategory>>
    //     0x7e914c: ldr             x1, [x1, #0x18]
    // 0x7e9150: stur            x0, [fp, #-0x60]
    // 0x7e9154: r0 = _$SuccessImpl()
    //     0x7e9154: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e9158: mov             x1, x0
    // 0x7e915c: ldur            x0, [fp, #-0x60]
    // 0x7e9160: StoreField: r1->field_b = r0
    //     0x7e9160: stur            w0, [x1, #0xb]
    // 0x7e9164: mov             x0, x1
    // 0x7e9168: r0 = ReturnAsyncNotFuture()
    //     0x7e9168: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e916c: sub             SP, fp, #0x78
    // 0x7e9170: mov             x1, x0
    // 0x7e9174: r0 = getDioException()
    //     0x7e9174: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e9178: r1 = <List<FaqCategory>>
    //     0x7e9178: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f018] TypeArguments: <List<FaqCategory>>
    //     0x7e917c: ldr             x1, [x1, #0x18]
    // 0x7e9180: stur            x0, [fp, #-0x60]
    // 0x7e9184: r0 = _$FailureImpl()
    //     0x7e9184: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e9188: ldur            x1, [fp, #-0x60]
    // 0x7e918c: StoreField: r0->field_b = r1
    //     0x7e918c: stur            w1, [x0, #0xb]
    // 0x7e9190: r0 = ReturnAsyncNotFuture()
    //     0x7e9190: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e9194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9198: b               #0x7e9038
  }
  [closure] FaqCategory <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7e919c, size: 0x50
    // 0x7e919c: EnterFrame
    //     0x7e919c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e91a0: mov             fp, SP
    // 0x7e91a4: CheckStackOverflow
    //     0x7e91a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e91a8: cmp             SP, x16
    //     0x7e91ac: b.ls            #0x7e91e4
    // 0x7e91b0: ldr             x0, [fp, #0x10]
    // 0x7e91b4: r2 = Null
    //     0x7e91b4: mov             x2, NULL
    // 0x7e91b8: r1 = Null
    //     0x7e91b8: mov             x1, NULL
    // 0x7e91bc: r8 = Map<String, dynamic>
    //     0x7e91bc: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e91c0: r3 = Null
    //     0x7e91c0: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fee8] Null
    //     0x7e91c4: ldr             x3, [x3, #0xee8]
    // 0x7e91c8: r0 = Map<String, dynamic>()
    //     0x7e91c8: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e91cc: ldr             x2, [fp, #0x10]
    // 0x7e91d0: r1 = Null
    //     0x7e91d0: mov             x1, NULL
    // 0x7e91d4: r0 = FaqCategory.fromMap()
    //     0x7e91d4: bl              #0x7e8b70  ; [package:nuonline/app/data/models/faq.dart] FaqCategory::FaqCategory.fromMap
    // 0x7e91d8: LeaveFrame
    //     0x7e91d8: mov             SP, fp
    //     0x7e91dc: ldp             fp, lr, [SP], #0x10
    // 0x7e91e0: ret
    //     0x7e91e0: ret             
    // 0x7e91e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e91e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e91e8: b               #0x7e91b0
  }
}
