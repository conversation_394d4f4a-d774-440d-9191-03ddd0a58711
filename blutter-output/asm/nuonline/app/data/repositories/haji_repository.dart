// lib: , url: package:nuonline/app/data/repositories/haji_repository.dart

// class id: 1050083, size: 0x8
class :: {
}

// class id: 1093, size: 0xc, field offset: 0x8
class HajiRepository extends Object {

  static _ find(/* No info */) {
    // ** addr: 0x813f68, size: 0x64
    // 0x813f68: EnterFrame
    //     0x813f68: stp             fp, lr, [SP, #-0x10]!
    //     0x813f6c: mov             fp, SP
    // 0x813f70: AllocStack(0x10)
    //     0x813f70: sub             SP, SP, #0x10
    // 0x813f74: CheckStackOverflow
    //     0x813f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813f78: cmp             SP, x16
    //     0x813f7c: b.ls            #0x813fc4
    // 0x813f80: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x813f80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x813f84: ldr             x0, [x0, #0x2670]
    //     0x813f88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x813f8c: cmp             w0, w16
    //     0x813f90: b.ne            #0x813f9c
    //     0x813f94: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x813f98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x813f9c: r16 = <HajiRepository>
    //     0x813f9c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10150] TypeArguments: <HajiRepository>
    //     0x813fa0: ldr             x16, [x16, #0x150]
    // 0x813fa4: r30 = "haji_repo"
    //     0x813fa4: add             lr, PP, #0x10, lsl #12  ; [pp+0x10158] "haji_repo"
    //     0x813fa8: ldr             lr, [lr, #0x158]
    // 0x813fac: stp             lr, x16, [SP]
    // 0x813fb0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x813fb0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x813fb4: r0 = Inst.find()
    //     0x813fb4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x813fb8: LeaveFrame
    //     0x813fb8: mov             SP, fp
    //     0x813fbc: ldp             fp, lr, [SP], #0x10
    // 0x813fc0: ret
    //     0x813fc0: ret             
    // 0x813fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x813fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x813fc8: b               #0x813f80
  }
  _ findAll(/* No info */) async {
    // ** addr: 0x8fc0f4, size: 0xb8
    // 0x8fc0f4: EnterFrame
    //     0x8fc0f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc0f8: mov             fp, SP
    // 0x8fc0fc: AllocStack(0x68)
    //     0x8fc0fc: sub             SP, SP, #0x68
    // 0x8fc100: SetupParameters(HajiRepository this /* r1 => r1, fp-0x50 */)
    //     0x8fc100: stur            NULL, [fp, #-8]
    //     0x8fc104: stur            x1, [fp, #-0x50]
    // 0x8fc108: CheckStackOverflow
    //     0x8fc108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc10c: cmp             SP, x16
    //     0x8fc110: b.ls            #0x8fc1a4
    // 0x8fc114: InitAsync() -> Future<ApiResult<List<Haji>>>
    //     0x8fc114: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fda0] TypeArguments: <ApiResult<List<Haji>>>
    //     0x8fc118: ldr             x0, [x0, #0xda0]
    //     0x8fc11c: bl              #0x661298  ; InitAsyncStub
    // 0x8fc120: ldur            x0, [fp, #-0x50]
    // 0x8fc124: LoadField: r1 = r0->field_7
    //     0x8fc124: ldur            w1, [x0, #7]
    // 0x8fc128: DecompressPointer r1
    //     0x8fc128: add             x1, x1, HEAP, lsl #32
    // 0x8fc12c: stp             x1, NULL, [SP, #8]
    // 0x8fc130: r16 = "/jadwal/haji"
    //     0x8fc130: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fda8] "/jadwal/haji"
    //     0x8fc134: ldr             x16, [x16, #0xda8]
    // 0x8fc138: str             x16, [SP]
    // 0x8fc13c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc13c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc140: r0 = get()
    //     0x8fc140: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8fc144: mov             x1, x0
    // 0x8fc148: stur            x1, [fp, #-0x50]
    // 0x8fc14c: r0 = Await()
    //     0x8fc14c: bl              #0x661044  ; AwaitStub
    // 0x8fc150: mov             x1, x0
    // 0x8fc154: r0 = fromResponse()
    //     0x8fc154: bl              #0x8fc1ac  ; [package:nuonline/app/data/models/haji.dart] Haji::fromResponse
    // 0x8fc158: r1 = <List<Haji>>
    //     0x8fc158: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] TypeArguments: <List<Haji>>
    //     0x8fc15c: ldr             x1, [x1, #0x9f0]
    // 0x8fc160: stur            x0, [fp, #-0x50]
    // 0x8fc164: r0 = _$SuccessImpl()
    //     0x8fc164: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8fc168: mov             x1, x0
    // 0x8fc16c: ldur            x0, [fp, #-0x50]
    // 0x8fc170: StoreField: r1->field_b = r0
    //     0x8fc170: stur            w0, [x1, #0xb]
    // 0x8fc174: mov             x0, x1
    // 0x8fc178: r0 = ReturnAsyncNotFuture()
    //     0x8fc178: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fc17c: sub             SP, fp, #0x68
    // 0x8fc180: mov             x1, x0
    // 0x8fc184: r0 = getDioException()
    //     0x8fc184: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fc188: r1 = <List<Haji>>
    //     0x8fc188: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] TypeArguments: <List<Haji>>
    //     0x8fc18c: ldr             x1, [x1, #0x9f0]
    // 0x8fc190: stur            x0, [fp, #-0x50]
    // 0x8fc194: r0 = _$FailureImpl()
    //     0x8fc194: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8fc198: ldur            x1, [fp, #-0x50]
    // 0x8fc19c: StoreField: r0->field_b = r1
    //     0x8fc19c: stur            w1, [x0, #0xb]
    // 0x8fc1a0: r0 = ReturnAsyncNotFuture()
    //     0x8fc1a0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fc1a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc1a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc1a8: b               #0x8fc114
  }
}
