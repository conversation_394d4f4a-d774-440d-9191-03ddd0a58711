// lib: , url: package:nuonline/app/data/repositories/hikmah_repository.dart

// class id: 1050084, size: 0x8
class :: {
}

// class id: 1092, size: 0x8, field offset: 0x8
class HikmahRepository extends Object {

  _ findAll(/* No info */) async {
    // ** addr: 0x72bb64, size: 0x1e0
    // 0x72bb64: EnterFrame
    //     0x72bb64: stp             fp, lr, [SP, #-0x10]!
    //     0x72bb68: mov             fp, SP
    // 0x72bb6c: AllocStack(0x88)
    //     0x72bb6c: sub             SP, SP, #0x88
    // 0x72bb70: SetupParameters(HikmahRepository this /* r1 => r1, fp-0x68 */, dynamic _ /* r2 => r2, fp-0x70 */)
    //     0x72bb70: stur            NULL, [fp, #-8]
    //     0x72bb74: stur            x1, [fp, #-0x68]
    //     0x72bb78: stur            x2, [fp, #-0x70]
    // 0x72bb7c: CheckStackOverflow
    //     0x72bb7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72bb80: cmp             SP, x16
    //     0x72bb84: b.ls            #0x72bd3c
    // 0x72bb88: InitAsync() -> Future<ApiResult<List<Hikmah>>>
    //     0x72bb88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea78] TypeArguments: <ApiResult<List<Hikmah>>>
    //     0x72bb8c: ldr             x0, [x0, #0xa78]
    //     0x72bb90: bl              #0x661298  ; InitAsyncStub
    // 0x72bb94: ldur            x0, [fp, #-0x70]
    // 0x72bb98: r0 = InitLateStaticField(0x698) // [package:flutter/src/services/asset_bundle.dart] ::rootBundle
    //     0x72bb98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x72bb9c: ldr             x0, [x0, #0xd30]
    //     0x72bba0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x72bba4: cmp             w0, w16
    //     0x72bba8: b.ne            #0x72bbb4
    //     0x72bbac: ldr             x2, [PP, #0x3260]  ; [pp+0x3260] Field <::.rootBundle>: static late final (offset: 0x698)
    //     0x72bbb0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x72bbb4: r1 = Null
    //     0x72bbb4: mov             x1, NULL
    // 0x72bbb8: r2 = 6
    //     0x72bbb8: movz            x2, #0x6
    // 0x72bbbc: stur            x0, [fp, #-0x68]
    // 0x72bbc0: r0 = AllocateArray()
    //     0x72bbc0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x72bbc4: r16 = "assets/data/hikmah_"
    //     0x72bbc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea80] "assets/data/hikmah_"
    //     0x72bbc8: ldr             x16, [x16, #0xa80]
    // 0x72bbcc: StoreField: r0->field_f = r16
    //     0x72bbcc: stur            w16, [x0, #0xf]
    // 0x72bbd0: ldur            x1, [fp, #-0x70]
    // 0x72bbd4: tbnz            w1, #4, #0x72bbe4
    // 0x72bbd8: r1 = "umrah"
    //     0x72bbd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "umrah"
    //     0x72bbdc: ldr             x1, [x1, #0xa88]
    // 0x72bbe0: b               #0x72bbec
    // 0x72bbe4: r1 = "haji"
    //     0x72bbe4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea90] "haji"
    //     0x72bbe8: ldr             x1, [x1, #0xa90]
    // 0x72bbec: StoreField: r0->field_13 = r1
    //     0x72bbec: stur            w1, [x0, #0x13]
    // 0x72bbf0: r16 = ".json"
    //     0x72bbf0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e840] ".json"
    //     0x72bbf4: ldr             x16, [x16, #0x840]
    // 0x72bbf8: ArrayStore: r0[0] = r16  ; List_4
    //     0x72bbf8: stur            w16, [x0, #0x17]
    // 0x72bbfc: str             x0, [SP]
    // 0x72bc00: r0 = _interpolate()
    //     0x72bc00: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x72bc04: ldur            x1, [fp, #-0x68]
    // 0x72bc08: mov             x2, x0
    // 0x72bc0c: r0 = loadString()
    //     0x72bc0c: bl              #0x72bd88  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadString
    // 0x72bc10: mov             x1, x0
    // 0x72bc14: stur            x1, [fp, #-0x68]
    // 0x72bc18: r0 = Await()
    //     0x72bc18: bl              #0x661044  ; AwaitStub
    // 0x72bc1c: mov             x1, x0
    // 0x72bc20: r0 = jsonDecode()
    //     0x72bc20: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x72bc24: mov             x3, x0
    // 0x72bc28: r2 = Null
    //     0x72bc28: mov             x2, NULL
    // 0x72bc2c: r1 = Null
    //     0x72bc2c: mov             x1, NULL
    // 0x72bc30: stur            x3, [fp, #-0x68]
    // 0x72bc34: r4 = 60
    //     0x72bc34: movz            x4, #0x3c
    // 0x72bc38: branchIfSmi(r0, 0x72bc44)
    //     0x72bc38: tbz             w0, #0, #0x72bc44
    // 0x72bc3c: r4 = LoadClassIdInstr(r0)
    //     0x72bc3c: ldur            x4, [x0, #-1]
    //     0x72bc40: ubfx            x4, x4, #0xc, #0x14
    // 0x72bc44: sub             x4, x4, #0x5a
    // 0x72bc48: cmp             x4, #2
    // 0x72bc4c: b.ls            #0x72bc64
    // 0x72bc50: r8 = List?
    //     0x72bc50: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x72bc54: ldr             x8, [x8, #0x140]
    // 0x72bc58: r3 = Null
    //     0x72bc58: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea98] Null
    //     0x72bc5c: ldr             x3, [x3, #0xa98]
    // 0x72bc60: r0 = List?()
    //     0x72bc60: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x72bc64: ldur            x0, [fp, #-0x68]
    // 0x72bc68: cmp             w0, NULL
    // 0x72bc6c: b.ne            #0x72bc7c
    // 0x72bc70: r1 = Null
    //     0x72bc70: mov             x1, NULL
    // 0x72bc74: r2 = 0
    //     0x72bc74: movz            x2, #0
    // 0x72bc78: r0 = _GrowableList()
    //     0x72bc78: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x72bc7c: stur            x0, [fp, #-0x68]
    // 0x72bc80: r1 = Function '<anonymous closure>':.
    //     0x72bc80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] AnonymousClosure: (0x72c0e4), in [package:nuonline/app/data/repositories/hikmah_repository.dart] HikmahRepository::findAll (0x72bb64)
    //     0x72bc84: ldr             x1, [x1, #0xaa8]
    // 0x72bc88: r2 = Null
    //     0x72bc88: mov             x2, NULL
    // 0x72bc8c: r0 = AllocateClosure()
    //     0x72bc8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x72bc90: mov             x1, x0
    // 0x72bc94: ldur            x0, [fp, #-0x68]
    // 0x72bc98: r2 = LoadClassIdInstr(r0)
    //     0x72bc98: ldur            x2, [x0, #-1]
    //     0x72bc9c: ubfx            x2, x2, #0xc, #0x14
    // 0x72bca0: r16 = <Hikmah>
    //     0x72bca0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eab0] TypeArguments: <Hikmah>
    //     0x72bca4: ldr             x16, [x16, #0xab0]
    // 0x72bca8: stp             x0, x16, [SP, #8]
    // 0x72bcac: str             x1, [SP]
    // 0x72bcb0: mov             x0, x2
    // 0x72bcb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x72bcb4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x72bcb8: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x72bcb8: movz            x17, #0xf28c
    //     0x72bcbc: add             lr, x0, x17
    //     0x72bcc0: ldr             lr, [x21, lr, lsl #3]
    //     0x72bcc4: blr             lr
    // 0x72bcc8: r1 = LoadClassIdInstr(r0)
    //     0x72bcc8: ldur            x1, [x0, #-1]
    //     0x72bccc: ubfx            x1, x1, #0xc, #0x14
    // 0x72bcd0: mov             x16, x0
    // 0x72bcd4: mov             x0, x1
    // 0x72bcd8: mov             x1, x16
    // 0x72bcdc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x72bcdc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x72bce0: r0 = GDT[cid_x0 + 0xd889]()
    //     0x72bce0: movz            x17, #0xd889
    //     0x72bce4: add             lr, x0, x17
    //     0x72bce8: ldr             lr, [x21, lr, lsl #3]
    //     0x72bcec: blr             lr
    // 0x72bcf0: r1 = <List<Hikmah>>
    //     0x72bcf0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea50] TypeArguments: <List<Hikmah>>
    //     0x72bcf4: ldr             x1, [x1, #0xa50]
    // 0x72bcf8: stur            x0, [fp, #-0x68]
    // 0x72bcfc: r0 = _$SuccessImpl()
    //     0x72bcfc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x72bd00: mov             x1, x0
    // 0x72bd04: ldur            x0, [fp, #-0x68]
    // 0x72bd08: StoreField: r1->field_b = r0
    //     0x72bd08: stur            w0, [x1, #0xb]
    // 0x72bd0c: mov             x0, x1
    // 0x72bd10: r0 = ReturnAsyncNotFuture()
    //     0x72bd10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72bd14: sub             SP, fp, #0x88
    // 0x72bd18: mov             x1, x0
    // 0x72bd1c: r0 = getDioException()
    //     0x72bd1c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x72bd20: r1 = <List<Hikmah>>
    //     0x72bd20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea50] TypeArguments: <List<Hikmah>>
    //     0x72bd24: ldr             x1, [x1, #0xa50]
    // 0x72bd28: stur            x0, [fp, #-0x68]
    // 0x72bd2c: r0 = _$FailureImpl()
    //     0x72bd2c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x72bd30: ldur            x1, [fp, #-0x68]
    // 0x72bd34: StoreField: r0->field_b = r1
    //     0x72bd34: stur            w1, [x0, #0xb]
    // 0x72bd38: r0 = ReturnAsyncNotFuture()
    //     0x72bd38: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x72bd3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72bd3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72bd40: b               #0x72bb88
  }
  [closure] Hikmah <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x72c0e4, size: 0x50
    // 0x72c0e4: EnterFrame
    //     0x72c0e4: stp             fp, lr, [SP, #-0x10]!
    //     0x72c0e8: mov             fp, SP
    // 0x72c0ec: CheckStackOverflow
    //     0x72c0ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72c0f0: cmp             SP, x16
    //     0x72c0f4: b.ls            #0x72c12c
    // 0x72c0f8: ldr             x0, [fp, #0x10]
    // 0x72c0fc: r2 = Null
    //     0x72c0fc: mov             x2, NULL
    // 0x72c100: r1 = Null
    //     0x72c100: mov             x1, NULL
    // 0x72c104: r8 = Map<String, dynamic>
    //     0x72c104: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x72c108: r3 = Null
    //     0x72c108: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab8] Null
    //     0x72c10c: ldr             x3, [x3, #0xab8]
    // 0x72c110: r0 = Map<String, dynamic>()
    //     0x72c110: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x72c114: ldr             x2, [fp, #0x10]
    // 0x72c118: r1 = Null
    //     0x72c118: mov             x1, NULL
    // 0x72c11c: r0 = Hikmah.fromMap()
    //     0x72c11c: bl              #0x72c134  ; [package:nuonline/app/data/models/hikmah.dart] Hikmah::Hikmah.fromMap
    // 0x72c120: LeaveFrame
    //     0x72c120: mov             SP, fp
    //     0x72c124: ldp             fp, lr, [SP], #0x10
    // 0x72c128: ret
    //     0x72c128: ret             
    // 0x72c12c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72c12c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72c130: b               #0x72c0f8
  }
  static _ find(/* No info */) {
    // ** addr: 0x8138a0, size: 0x64
    // 0x8138a0: EnterFrame
    //     0x8138a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8138a4: mov             fp, SP
    // 0x8138a8: AllocStack(0x10)
    //     0x8138a8: sub             SP, SP, #0x10
    // 0x8138ac: CheckStackOverflow
    //     0x8138ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8138b0: cmp             SP, x16
    //     0x8138b4: b.ls            #0x8138fc
    // 0x8138b8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8138b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8138bc: ldr             x0, [x0, #0x2670]
    //     0x8138c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8138c4: cmp             w0, w16
    //     0x8138c8: b.ne            #0x8138d4
    //     0x8138cc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8138d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8138d4: r16 = <HikmahRepository>
    //     0x8138d4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10168] TypeArguments: <HikmahRepository>
    //     0x8138d8: ldr             x16, [x16, #0x168]
    // 0x8138dc: r30 = "hikmah_remote_repo"
    //     0x8138dc: add             lr, PP, #0x10, lsl #12  ; [pp+0x10170] "hikmah_remote_repo"
    //     0x8138e0: ldr             lr, [lr, #0x170]
    // 0x8138e4: stp             lr, x16, [SP]
    // 0x8138e8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8138e8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8138ec: r0 = Inst.find()
    //     0x8138ec: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8138f0: LeaveFrame
    //     0x8138f0: mov             SP, fp
    //     0x8138f4: ldp             fp, lr, [SP], #0x10
    // 0x8138f8: ret
    //     0x8138f8: ret             
    // 0x8138fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8138fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x813900: b               #0x8138b8
  }
}
