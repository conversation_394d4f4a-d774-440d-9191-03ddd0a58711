// lib: , url: package:nuonline/app/data/repositories/home_repository.dart

// class id: 1050085, size: 0x8
class :: {
}

// class id: 1091, size: 0x14, field offset: 0x8
class HomeRepository extends Object {

  _ findRecomendation(/* No info */) async {
    // ** addr: 0x8c07dc, size: 0x2ac
    // 0x8c07dc: EnterFrame
    //     0x8c07dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c07e0: mov             fp, SP
    // 0x8c07e4: AllocStack(0xa0)
    //     0x8c07e4: sub             SP, SP, #0xa0
    // 0x8c07e8: SetupParameters(HomeRepository this /* r1 => r1, fp-0x50 */)
    //     0x8c07e8: stur            NULL, [fp, #-8]
    //     0x8c07ec: stur            x1, [fp, #-0x50]
    // 0x8c07f0: CheckStackOverflow
    //     0x8c07f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c07f4: cmp             SP, x16
    //     0x8c07f8: b.ls            #0x8c0a70
    // 0x8c07fc: InitAsync() -> Future<ApiResult<List<Recommendation>>>
    //     0x8c07fc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f2f8] TypeArguments: <ApiResult<List<Recommendation>>>
    //     0x8c0800: ldr             x0, [x0, #0x2f8]
    //     0x8c0804: bl              #0x661298  ; InitAsyncStub
    // 0x8c0808: ldur            x0, [fp, #-0x50]
    // 0x8c080c: LoadField: r1 = r0->field_7
    //     0x8c080c: ldur            w1, [x0, #7]
    // 0x8c0810: DecompressPointer r1
    //     0x8c0810: add             x1, x1, HEAP, lsl #32
    // 0x8c0814: stp             x1, NULL, [SP, #0x20]
    // 0x8c0818: r16 = "/home/<USER>"
    //     0x8c0818: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f300] "/home/<USER>"
    //     0x8c081c: ldr             x16, [x16, #0x300]
    // 0x8c0820: r30 = false
    //     0x8c0820: add             lr, NULL, #0x30  ; false
    // 0x8c0824: stp             lr, x16, [SP, #0x10]
    // 0x8c0828: r16 = Instance_Duration
    //     0x8c0828: add             x16, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x8c082c: ldr             x16, [x16, #0x7b8]
    // 0x8c0830: r30 = Instance_Duration
    //     0x8c0830: add             lr, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x8c0834: ldr             lr, [lr, #0x7b8]
    // 0x8c0838: stp             lr, x16, [SP]
    // 0x8c083c: r4 = const [0x1, 0x5, 0x5, 0x3, receiveTimeout, 0x4, sendTimeout, 0x3, null]
    //     0x8c083c: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dae0] List(9) [0x1, 0x5, 0x5, 0x3, "receiveTimeout", 0x4, "sendTimeout", 0x3, Null]
    //     0x8c0840: ldr             x4, [x4, #0xae0]
    // 0x8c0844: r0 = withRetry()
    //     0x8c0844: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x8c0848: mov             x1, x0
    // 0x8c084c: stur            x1, [fp, #-0x58]
    // 0x8c0850: r0 = Await()
    //     0x8c0850: bl              #0x661044  ; AwaitStub
    // 0x8c0854: LoadField: r3 = r0->field_b
    //     0x8c0854: ldur            w3, [x0, #0xb]
    // 0x8c0858: DecompressPointer r3
    //     0x8c0858: add             x3, x3, HEAP, lsl #32
    // 0x8c085c: mov             x0, x3
    // 0x8c0860: stur            x3, [fp, #-0x58]
    // 0x8c0864: r2 = Null
    //     0x8c0864: mov             x2, NULL
    // 0x8c0868: r1 = Null
    //     0x8c0868: mov             x1, NULL
    // 0x8c086c: r4 = 60
    //     0x8c086c: movz            x4, #0x3c
    // 0x8c0870: branchIfSmi(r0, 0x8c087c)
    //     0x8c0870: tbz             w0, #0, #0x8c087c
    // 0x8c0874: r4 = LoadClassIdInstr(r0)
    //     0x8c0874: ldur            x4, [x0, #-1]
    //     0x8c0878: ubfx            x4, x4, #0xc, #0x14
    // 0x8c087c: sub             x4, x4, #0x5e
    // 0x8c0880: cmp             x4, #1
    // 0x8c0884: b.ls            #0x8c0898
    // 0x8c0888: r8 = String
    //     0x8c0888: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c088c: r3 = Null
    //     0x8c088c: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f308] Null
    //     0x8c0890: ldr             x3, [x3, #0x308]
    // 0x8c0894: r0 = String()
    //     0x8c0894: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c0898: ldur            x1, [fp, #-0x58]
    // 0x8c089c: r0 = jsonDecode()
    //     0x8c089c: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x8c08a0: mov             x1, x0
    // 0x8c08a4: r0 = fromResponse()
    //     0x8c08a4: bl              #0x8c12b0  ; [package:nuonline/app/data/models/recommendation.dart] Recommendation::fromResponse
    // 0x8c08a8: mov             x1, x0
    // 0x8c08ac: ldur            x0, [fp, #-0x50]
    // 0x8c08b0: stur            x1, [fp, #-0x60]
    // 0x8c08b4: LoadField: r2 = r0->field_b
    //     0x8c08b4: ldur            w2, [x0, #0xb]
    // 0x8c08b8: DecompressPointer r2
    //     0x8c08b8: add             x2, x2, HEAP, lsl #32
    // 0x8c08bc: stur            x2, [fp, #-0x58]
    // 0x8c08c0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8c08c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c08c4: ldr             x0, [x0, #0x2728]
    //     0x8c08c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c08cc: cmp             w0, w16
    //     0x8c08d0: b.ne            #0x8c08dc
    //     0x8c08d4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8c08d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c08dc: stur            x0, [fp, #-0x68]
    // 0x8c08e0: r16 = <Recommendation>
    //     0x8c08e0: ldr             x16, [PP, #0x7bc8]  ; [pp+0x7bc8] TypeArguments: <Recommendation>
    // 0x8c08e4: stp             x0, x16, [SP, #8]
    // 0x8c08e8: r16 = "v2_recommendation"
    //     0x8c08e8: ldr             x16, [PP, #0x7c70]  ; [pp+0x7c70] "v2_recommendation"
    // 0x8c08ec: str             x16, [SP]
    // 0x8c08f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c08f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c08f4: r0 = box()
    //     0x8c08f4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c08f8: mov             x1, x0
    // 0x8c08fc: r0 = clear()
    //     0x8c08fc: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0x8c0900: mov             x1, x0
    // 0x8c0904: stur            x1, [fp, #-0x70]
    // 0x8c0908: r0 = Await()
    //     0x8c0908: bl              #0x661044  ; AwaitStub
    // 0x8c090c: r16 = <Recommendation>
    //     0x8c090c: ldr             x16, [PP, #0x7bc8]  ; [pp+0x7bc8] TypeArguments: <Recommendation>
    // 0x8c0910: ldur            lr, [fp, #-0x68]
    // 0x8c0914: stp             lr, x16, [SP, #8]
    // 0x8c0918: r16 = "v2_recommendation"
    //     0x8c0918: ldr             x16, [PP, #0x7c70]  ; [pp+0x7c70] "v2_recommendation"
    // 0x8c091c: str             x16, [SP]
    // 0x8c0920: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c0920: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c0924: r0 = box()
    //     0x8c0924: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c0928: mov             x1, x0
    // 0x8c092c: ldur            x2, [fp, #-0x60]
    // 0x8c0930: r0 = addAll()
    //     0x8c0930: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0x8c0934: mov             x1, x0
    // 0x8c0938: stur            x1, [fp, #-0x58]
    // 0x8c093c: r0 = Await()
    //     0x8c093c: bl              #0x661044  ; AwaitStub
    // 0x8c0940: r1 = <List<Recommendation>>
    //     0x8c0940: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f318] TypeArguments: <List<Recommendation>>
    //     0x8c0944: ldr             x1, [x1, #0x318]
    // 0x8c0948: r0 = _$SuccessImpl()
    //     0x8c0948: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8c094c: mov             x1, x0
    // 0x8c0950: ldur            x0, [fp, #-0x60]
    // 0x8c0954: StoreField: r1->field_b = r0
    //     0x8c0954: stur            w0, [x1, #0xb]
    // 0x8c0958: mov             x0, x1
    // 0x8c095c: r0 = ReturnAsyncNotFuture()
    //     0x8c095c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c0960: sub             SP, fp, #0xa0
    // 0x8c0964: mov             x1, x0
    // 0x8c0968: stur            x0, [fp, #-0x58]
    // 0x8c096c: ldur            x0, [fp, #-0x10]
    // 0x8c0970: LoadField: r2 = r0->field_b
    //     0x8c0970: ldur            w2, [x0, #0xb]
    // 0x8c0974: DecompressPointer r2
    //     0x8c0974: add             x2, x2, HEAP, lsl #32
    // 0x8c0978: stur            x2, [fp, #-0x50]
    // 0x8c097c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8c097c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c0980: ldr             x0, [x0, #0x2728]
    //     0x8c0984: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c0988: cmp             w0, w16
    //     0x8c098c: b.ne            #0x8c0998
    //     0x8c0990: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8c0994: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c0998: r16 = <Recommendation>
    //     0x8c0998: ldr             x16, [PP, #0x7bc8]  ; [pp+0x7bc8] TypeArguments: <Recommendation>
    // 0x8c099c: stp             x0, x16, [SP, #8]
    // 0x8c09a0: r16 = "v2_recommendation"
    //     0x8c09a0: ldr             x16, [PP, #0x7c70]  ; [pp+0x7c70] "v2_recommendation"
    // 0x8c09a4: str             x16, [SP]
    // 0x8c09a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c09a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c09ac: r0 = box()
    //     0x8c09ac: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c09b0: mov             x1, x0
    // 0x8c09b4: stur            x0, [fp, #-0x60]
    // 0x8c09b8: r0 = checkOpen()
    //     0x8c09b8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c09bc: ldur            x0, [fp, #-0x60]
    // 0x8c09c0: LoadField: r1 = r0->field_1b
    //     0x8c09c0: ldur            w1, [x0, #0x1b]
    // 0x8c09c4: DecompressPointer r1
    //     0x8c09c4: add             x1, x1, HEAP, lsl #32
    // 0x8c09c8: r16 = Sentinel
    //     0x8c09c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c09cc: cmp             w1, w16
    // 0x8c09d0: b.eq            #0x8c0a78
    // 0x8c09d4: LoadField: r0 = r1->field_13
    //     0x8c09d4: ldur            w0, [x1, #0x13]
    // 0x8c09d8: DecompressPointer r0
    //     0x8c09d8: add             x0, x0, HEAP, lsl #32
    // 0x8c09dc: LoadField: r1 = r0->field_1f
    //     0x8c09dc: ldur            x1, [x0, #0x1f]
    // 0x8c09e0: cmp             x1, #0
    // 0x8c09e4: b.le            #0x8c0a4c
    // 0x8c09e8: ldur            x1, [fp, #-0x50]
    // 0x8c09ec: r0 = recomentations()
    //     0x8c09ec: bl              #0x8c0b00  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::recomentations
    // 0x8c09f0: mov             x1, x0
    // 0x8c09f4: stur            x0, [fp, #-0x50]
    // 0x8c09f8: r0 = checkOpen()
    //     0x8c09f8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c09fc: ldur            x0, [fp, #-0x50]
    // 0x8c0a00: LoadField: r1 = r0->field_1b
    //     0x8c0a00: ldur            w1, [x0, #0x1b]
    // 0x8c0a04: DecompressPointer r1
    //     0x8c0a04: add             x1, x1, HEAP, lsl #32
    // 0x8c0a08: r16 = Sentinel
    //     0x8c0a08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c0a0c: cmp             w1, w16
    // 0x8c0a10: b.eq            #0x8c0a80
    // 0x8c0a14: r0 = getValues()
    //     0x8c0a14: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8c0a18: LoadField: r1 = r0->field_7
    //     0x8c0a18: ldur            w1, [x0, #7]
    // 0x8c0a1c: DecompressPointer r1
    //     0x8c0a1c: add             x1, x1, HEAP, lsl #32
    // 0x8c0a20: mov             x2, x0
    // 0x8c0a24: r0 = _GrowableList.of()
    //     0x8c0a24: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8c0a28: r1 = <List<Recommendation>>
    //     0x8c0a28: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f318] TypeArguments: <List<Recommendation>>
    //     0x8c0a2c: ldr             x1, [x1, #0x318]
    // 0x8c0a30: stur            x0, [fp, #-0x50]
    // 0x8c0a34: r0 = _$SuccessImpl()
    //     0x8c0a34: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8c0a38: mov             x1, x0
    // 0x8c0a3c: ldur            x0, [fp, #-0x50]
    // 0x8c0a40: StoreField: r1->field_b = r0
    //     0x8c0a40: stur            w0, [x1, #0xb]
    // 0x8c0a44: mov             x0, x1
    // 0x8c0a48: r0 = ReturnAsyncNotFuture()
    //     0x8c0a48: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c0a4c: ldur            x1, [fp, #-0x58]
    // 0x8c0a50: r0 = getDioException()
    //     0x8c0a50: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8c0a54: r1 = <List<Recommendation>>
    //     0x8c0a54: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f318] TypeArguments: <List<Recommendation>>
    //     0x8c0a58: ldr             x1, [x1, #0x318]
    // 0x8c0a5c: stur            x0, [fp, #-0x50]
    // 0x8c0a60: r0 = _$FailureImpl()
    //     0x8c0a60: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8c0a64: ldur            x1, [fp, #-0x50]
    // 0x8c0a68: StoreField: r0->field_b = r1
    //     0x8c0a68: stur            w1, [x0, #0xb]
    // 0x8c0a6c: r0 = ReturnAsyncNotFuture()
    //     0x8c0a6c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c0a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c0a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c0a74: b               #0x8c07fc
    // 0x8c0a78: r9 = keystore
    //     0x8c0a78: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c0a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c0a7c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8c0a80: r9 = keystore
    //     0x8c0a80: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c0a84: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c0a84: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ findAllSummary(/* No info */) async {
    // ** addr: 0x91a894, size: 0x430
    // 0x91a894: EnterFrame
    //     0x91a894: stp             fp, lr, [SP, #-0x10]!
    //     0x91a898: mov             fp, SP
    // 0x91a89c: AllocStack(0xd8)
    //     0x91a89c: sub             SP, SP, #0xd8
    // 0x91a8a0: SetupParameters(HomeRepository this /* r1 => r1, fp-0x78 */)
    //     0x91a8a0: stur            NULL, [fp, #-8]
    //     0x91a8a4: stur            x1, [fp, #-0x78]
    // 0x91a8a8: CheckStackOverflow
    //     0x91a8a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91a8ac: cmp             SP, x16
    //     0x91a8b0: b.ls            #0x91ac94
    // 0x91a8b4: InitAsync() -> Future<ApiResult<List<Summary>>>
    //     0x91a8b4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dac8] TypeArguments: <ApiResult<List<Summary>>>
    //     0x91a8b8: ldr             x0, [x0, #0xac8]
    //     0x91a8bc: bl              #0x661298  ; InitAsyncStub
    // 0x91a8c0: ldur            x0, [fp, #-0x78]
    // 0x91a8c4: LoadField: r1 = r0->field_f
    //     0x91a8c4: ldur            w1, [x0, #0xf]
    // 0x91a8c8: DecompressPointer r1
    //     0x91a8c8: add             x1, x1, HEAP, lsl #32
    // 0x91a8cc: r0 = homeUpdateConfig()
    //     0x91a8cc: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0x91a8d0: LoadField: r1 = r0->field_7
    //     0x91a8d0: ldur            w1, [x0, #7]
    // 0x91a8d4: DecompressPointer r1
    //     0x91a8d4: add             x1, x1, HEAP, lsl #32
    // 0x91a8d8: tbz             w1, #4, #0x91a988
    // 0x91a8dc: ldur            x0, [fp, #-0x78]
    // 0x91a8e0: LoadField: r1 = r0->field_b
    //     0x91a8e0: ldur            w1, [x0, #0xb]
    // 0x91a8e4: DecompressPointer r1
    //     0x91a8e4: add             x1, x1, HEAP, lsl #32
    // 0x91a8e8: stur            x1, [fp, #-0x80]
    // 0x91a8ec: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91a8ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91a8f0: ldr             x0, [x0, #0x2728]
    //     0x91a8f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91a8f8: cmp             w0, w16
    //     0x91a8fc: b.ne            #0x91a908
    //     0x91a900: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91a904: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91a908: r16 = <Summary>
    //     0x91a908: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91a90c: stp             x0, x16, [SP, #8]
    // 0x91a910: r16 = "v2_summaries"
    //     0x91a910: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91a914: str             x16, [SP]
    // 0x91a918: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91a918: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91a91c: r0 = box()
    //     0x91a91c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91a920: mov             x1, x0
    // 0x91a924: stur            x0, [fp, #-0x80]
    // 0x91a928: r0 = checkOpen()
    //     0x91a928: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91a92c: ldur            x0, [fp, #-0x80]
    // 0x91a930: LoadField: r1 = r0->field_1b
    //     0x91a930: ldur            w1, [x0, #0x1b]
    // 0x91a934: DecompressPointer r1
    //     0x91a934: add             x1, x1, HEAP, lsl #32
    // 0x91a938: r16 = Sentinel
    //     0x91a938: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91a93c: cmp             w1, w16
    // 0x91a940: b.eq            #0x91ac9c
    // 0x91a944: r0 = getValues()
    //     0x91a944: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x91a948: stur            x0, [fp, #-0x88]
    // 0x91a94c: LoadField: r3 = r0->field_7
    //     0x91a94c: ldur            w3, [x0, #7]
    // 0x91a950: DecompressPointer r3
    //     0x91a950: add             x3, x3, HEAP, lsl #32
    // 0x91a954: mov             x1, x3
    // 0x91a958: mov             x2, x0
    // 0x91a95c: stur            x3, [fp, #-0x80]
    // 0x91a960: r0 = _GrowableList.of()
    //     0x91a960: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x91a964: r1 = <List<Summary>>
    //     0x91a964: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dad0] TypeArguments: <List<Summary>>
    //     0x91a968: ldr             x1, [x1, #0xad0]
    // 0x91a96c: stur            x0, [fp, #-0x80]
    // 0x91a970: r0 = _$SuccessImpl()
    //     0x91a970: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91a974: mov             x1, x0
    // 0x91a978: ldur            x0, [fp, #-0x80]
    // 0x91a97c: StoreField: r1->field_b = r0
    //     0x91a97c: stur            w0, [x1, #0xb]
    // 0x91a980: mov             x0, x1
    // 0x91a984: r0 = ReturnAsyncNotFuture()
    //     0x91a984: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91a988: ldur            x0, [fp, #-0x78]
    // 0x91a98c: LoadField: r1 = r0->field_7
    //     0x91a98c: ldur            w1, [x0, #7]
    // 0x91a990: DecompressPointer r1
    //     0x91a990: add             x1, x1, HEAP, lsl #32
    // 0x91a994: stur            x1, [fp, #-0x88]
    // 0x91a998: LoadField: r2 = r0->field_b
    //     0x91a998: ldur            w2, [x0, #0xb]
    // 0x91a99c: DecompressPointer r2
    //     0x91a99c: add             x2, x2, HEAP, lsl #32
    // 0x91a9a0: stur            x2, [fp, #-0x80]
    // 0x91a9a4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91a9a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91a9a8: ldr             x0, [x0, #0x2728]
    //     0x91a9ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91a9b0: cmp             w0, w16
    //     0x91a9b4: b.ne            #0x91a9c0
    //     0x91a9b8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91a9bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91a9c0: stur            x0, [fp, #-0x90]
    // 0x91a9c4: r16 = <Summary>
    //     0x91a9c4: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91a9c8: stp             x0, x16, [SP, #8]
    // 0x91a9cc: r16 = "v2_summaries"
    //     0x91a9cc: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91a9d0: str             x16, [SP]
    // 0x91a9d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91a9d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91a9d8: r0 = box()
    //     0x91a9d8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91a9dc: mov             x1, x0
    // 0x91a9e0: stur            x0, [fp, #-0x98]
    // 0x91a9e4: r0 = checkOpen()
    //     0x91a9e4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91a9e8: ldur            x0, [fp, #-0x98]
    // 0x91a9ec: LoadField: r1 = r0->field_1b
    //     0x91a9ec: ldur            w1, [x0, #0x1b]
    // 0x91a9f0: DecompressPointer r1
    //     0x91a9f0: add             x1, x1, HEAP, lsl #32
    // 0x91a9f4: r16 = Sentinel
    //     0x91a9f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91a9f8: cmp             w1, w16
    // 0x91a9fc: b.eq            #0x91aca4
    // 0x91aa00: LoadField: r0 = r1->field_13
    //     0x91aa00: ldur            w0, [x1, #0x13]
    // 0x91aa04: DecompressPointer r0
    //     0x91aa04: add             x0, x0, HEAP, lsl #32
    // 0x91aa08: LoadField: r1 = r0->field_1f
    //     0x91aa08: ldur            x1, [x0, #0x1f]
    // 0x91aa0c: cbz             x1, #0x91aa18
    // 0x91aa10: r0 = false
    //     0x91aa10: add             x0, NULL, #0x30  ; false
    // 0x91aa14: b               #0x91aa1c
    // 0x91aa18: r0 = true
    //     0x91aa18: add             x0, NULL, #0x20  ; true
    // 0x91aa1c: stur            x0, [fp, #-0x98]
    // 0x91aa20: r16 = <Summary>
    //     0x91aa20: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91aa24: ldur            lr, [fp, #-0x90]
    // 0x91aa28: stp             lr, x16, [SP, #8]
    // 0x91aa2c: r16 = "v2_summaries"
    //     0x91aa2c: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91aa30: str             x16, [SP]
    // 0x91aa34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91aa34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91aa38: r0 = box()
    //     0x91aa38: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91aa3c: mov             x1, x0
    // 0x91aa40: stur            x0, [fp, #-0xa0]
    // 0x91aa44: r0 = checkOpen()
    //     0x91aa44: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91aa48: ldur            x0, [fp, #-0xa0]
    // 0x91aa4c: LoadField: r1 = r0->field_1b
    //     0x91aa4c: ldur            w1, [x0, #0x1b]
    // 0x91aa50: DecompressPointer r1
    //     0x91aa50: add             x1, x1, HEAP, lsl #32
    // 0x91aa54: r16 = Sentinel
    //     0x91aa54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91aa58: cmp             w1, w16
    // 0x91aa5c: b.eq            #0x91acac
    // 0x91aa60: LoadField: r0 = r1->field_13
    //     0x91aa60: ldur            w0, [x1, #0x13]
    // 0x91aa64: DecompressPointer r0
    //     0x91aa64: add             x0, x0, HEAP, lsl #32
    // 0x91aa68: LoadField: r1 = r0->field_1f
    //     0x91aa68: ldur            x1, [x0, #0x1f]
    // 0x91aa6c: cbnz            x1, #0x91aa78
    // 0x91aa70: r0 = Null
    //     0x91aa70: mov             x0, NULL
    // 0x91aa74: b               #0x91aa80
    // 0x91aa78: r0 = Instance_Duration
    //     0x91aa78: add             x0, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x91aa7c: ldr             x0, [x0, #0x7b8]
    // 0x91aa80: stur            x0, [fp, #-0xa0]
    // 0x91aa84: r16 = <Summary>
    //     0x91aa84: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91aa88: ldur            lr, [fp, #-0x90]
    // 0x91aa8c: stp             lr, x16, [SP, #8]
    // 0x91aa90: r16 = "v2_summaries"
    //     0x91aa90: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91aa94: str             x16, [SP]
    // 0x91aa98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91aa98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91aa9c: r0 = box()
    //     0x91aa9c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91aaa0: mov             x1, x0
    // 0x91aaa4: stur            x0, [fp, #-0xa8]
    // 0x91aaa8: r0 = checkOpen()
    //     0x91aaa8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91aaac: ldur            x0, [fp, #-0xa8]
    // 0x91aab0: LoadField: r1 = r0->field_1b
    //     0x91aab0: ldur            w1, [x0, #0x1b]
    // 0x91aab4: DecompressPointer r1
    //     0x91aab4: add             x1, x1, HEAP, lsl #32
    // 0x91aab8: r16 = Sentinel
    //     0x91aab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91aabc: cmp             w1, w16
    // 0x91aac0: b.eq            #0x91acb4
    // 0x91aac4: LoadField: r0 = r1->field_13
    //     0x91aac4: ldur            w0, [x1, #0x13]
    // 0x91aac8: DecompressPointer r0
    //     0x91aac8: add             x0, x0, HEAP, lsl #32
    // 0x91aacc: LoadField: r1 = r0->field_1f
    //     0x91aacc: ldur            x1, [x0, #0x1f]
    // 0x91aad0: cbnz            x1, #0x91aadc
    // 0x91aad4: r0 = Null
    //     0x91aad4: mov             x0, NULL
    // 0x91aad8: b               #0x91aae4
    // 0x91aadc: r0 = Instance_Duration
    //     0x91aadc: add             x0, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x91aae0: ldr             x0, [x0, #0x7b8]
    // 0x91aae4: ldur            x16, [fp, #-0x88]
    // 0x91aae8: stp             x16, NULL, [SP, #0x20]
    // 0x91aaec: r16 = "/home/<USER>"
    //     0x91aaec: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dad8] "/home/<USER>"
    //     0x91aaf0: ldr             x16, [x16, #0xad8]
    // 0x91aaf4: ldur            lr, [fp, #-0x98]
    // 0x91aaf8: stp             lr, x16, [SP, #0x10]
    // 0x91aafc: ldur            x16, [fp, #-0xa0]
    // 0x91ab00: stp             x0, x16, [SP]
    // 0x91ab04: r4 = const [0x1, 0x5, 0x5, 0x3, receiveTimeout, 0x4, sendTimeout, 0x3, null]
    //     0x91ab04: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dae0] List(9) [0x1, 0x5, 0x5, 0x3, "receiveTimeout", 0x4, "sendTimeout", 0x3, Null]
    //     0x91ab08: ldr             x4, [x4, #0xae0]
    // 0x91ab0c: r0 = withRetry()
    //     0x91ab0c: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x91ab10: mov             x1, x0
    // 0x91ab14: stur            x1, [fp, #-0x88]
    // 0x91ab18: r0 = Await()
    //     0x91ab18: bl              #0x661044  ; AwaitStub
    // 0x91ab1c: LoadField: r1 = r0->field_b
    //     0x91ab1c: ldur            w1, [x0, #0xb]
    // 0x91ab20: DecompressPointer r1
    //     0x91ab20: add             x1, x1, HEAP, lsl #32
    // 0x91ab24: r0 = fromResponse()
    //     0x91ab24: bl              #0x91ad24  ; [package:nuonline/app/data/models/summary.dart] Summary::fromResponse
    // 0x91ab28: stur            x0, [fp, #-0x88]
    // 0x91ab2c: r16 = <Summary>
    //     0x91ab2c: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91ab30: ldur            lr, [fp, #-0x90]
    // 0x91ab34: stp             lr, x16, [SP, #8]
    // 0x91ab38: r16 = "v2_summaries"
    //     0x91ab38: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91ab3c: str             x16, [SP]
    // 0x91ab40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91ab40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91ab44: r0 = box()
    //     0x91ab44: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91ab48: mov             x1, x0
    // 0x91ab4c: r0 = clear()
    //     0x91ab4c: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0x91ab50: mov             x1, x0
    // 0x91ab54: stur            x1, [fp, #-0x98]
    // 0x91ab58: r0 = Await()
    //     0x91ab58: bl              #0x661044  ; AwaitStub
    // 0x91ab5c: r16 = <Summary>
    //     0x91ab5c: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91ab60: ldur            lr, [fp, #-0x90]
    // 0x91ab64: stp             lr, x16, [SP, #8]
    // 0x91ab68: r16 = "v2_summaries"
    //     0x91ab68: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91ab6c: str             x16, [SP]
    // 0x91ab70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91ab70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91ab74: r0 = box()
    //     0x91ab74: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91ab78: mov             x1, x0
    // 0x91ab7c: ldur            x2, [fp, #-0x88]
    // 0x91ab80: r0 = addAll()
    //     0x91ab80: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0x91ab84: mov             x1, x0
    // 0x91ab88: stur            x1, [fp, #-0x80]
    // 0x91ab8c: r0 = Await()
    //     0x91ab8c: bl              #0x661044  ; AwaitStub
    // 0x91ab90: r1 = <List<Summary>>
    //     0x91ab90: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dad0] TypeArguments: <List<Summary>>
    //     0x91ab94: ldr             x1, [x1, #0xad0]
    // 0x91ab98: r0 = _$SuccessImpl()
    //     0x91ab98: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91ab9c: mov             x1, x0
    // 0x91aba0: ldur            x0, [fp, #-0x88]
    // 0x91aba4: StoreField: r1->field_b = r0
    //     0x91aba4: stur            w0, [x1, #0xb]
    // 0x91aba8: mov             x0, x1
    // 0x91abac: r0 = ReturnAsyncNotFuture()
    //     0x91abac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91abb0: sub             SP, fp, #0xd8
    // 0x91abb4: mov             x1, x0
    // 0x91abb8: stur            x0, [fp, #-0x80]
    // 0x91abbc: ldur            x0, [fp, #-0x10]
    // 0x91abc0: LoadField: r2 = r0->field_b
    //     0x91abc0: ldur            w2, [x0, #0xb]
    // 0x91abc4: DecompressPointer r2
    //     0x91abc4: add             x2, x2, HEAP, lsl #32
    // 0x91abc8: stur            x2, [fp, #-0x78]
    // 0x91abcc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91abcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91abd0: ldr             x0, [x0, #0x2728]
    //     0x91abd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91abd8: cmp             w0, w16
    //     0x91abdc: b.ne            #0x91abe8
    //     0x91abe0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91abe4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91abe8: r16 = <Summary>
    //     0x91abe8: ldr             x16, [PP, #0x7b48]  ; [pp+0x7b48] TypeArguments: <Summary>
    // 0x91abec: stp             x0, x16, [SP, #8]
    // 0x91abf0: r16 = "v2_summaries"
    //     0x91abf0: ldr             x16, [PP, #0x7be0]  ; [pp+0x7be0] "v2_summaries"
    // 0x91abf4: str             x16, [SP]
    // 0x91abf8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91abf8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91abfc: r0 = box()
    //     0x91abfc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91ac00: mov             x1, x0
    // 0x91ac04: r0 = isNotEmpty()
    //     0x91ac04: bl              #0x8c0a88  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::isNotEmpty
    // 0x91ac08: tbnz            w0, #4, #0x91ac70
    // 0x91ac0c: ldur            x1, [fp, #-0x78]
    // 0x91ac10: r0 = summaries()
    //     0x91ac10: bl              #0x91acc4  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::summaries
    // 0x91ac14: mov             x1, x0
    // 0x91ac18: stur            x0, [fp, #-0x78]
    // 0x91ac1c: r0 = checkOpen()
    //     0x91ac1c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91ac20: ldur            x0, [fp, #-0x78]
    // 0x91ac24: LoadField: r1 = r0->field_1b
    //     0x91ac24: ldur            w1, [x0, #0x1b]
    // 0x91ac28: DecompressPointer r1
    //     0x91ac28: add             x1, x1, HEAP, lsl #32
    // 0x91ac2c: r16 = Sentinel
    //     0x91ac2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91ac30: cmp             w1, w16
    // 0x91ac34: b.eq            #0x91acbc
    // 0x91ac38: r0 = getValues()
    //     0x91ac38: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x91ac3c: LoadField: r1 = r0->field_7
    //     0x91ac3c: ldur            w1, [x0, #7]
    // 0x91ac40: DecompressPointer r1
    //     0x91ac40: add             x1, x1, HEAP, lsl #32
    // 0x91ac44: mov             x2, x0
    // 0x91ac48: r0 = _GrowableList.of()
    //     0x91ac48: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x91ac4c: r1 = <List<Summary>>
    //     0x91ac4c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dad0] TypeArguments: <List<Summary>>
    //     0x91ac50: ldr             x1, [x1, #0xad0]
    // 0x91ac54: stur            x0, [fp, #-0x78]
    // 0x91ac58: r0 = _$SuccessImpl()
    //     0x91ac58: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91ac5c: mov             x1, x0
    // 0x91ac60: ldur            x0, [fp, #-0x78]
    // 0x91ac64: StoreField: r1->field_b = r0
    //     0x91ac64: stur            w0, [x1, #0xb]
    // 0x91ac68: mov             x0, x1
    // 0x91ac6c: r0 = ReturnAsyncNotFuture()
    //     0x91ac6c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91ac70: ldur            x1, [fp, #-0x80]
    // 0x91ac74: r0 = getDioException()
    //     0x91ac74: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x91ac78: r1 = <List<Summary>>
    //     0x91ac78: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dad0] TypeArguments: <List<Summary>>
    //     0x91ac7c: ldr             x1, [x1, #0xad0]
    // 0x91ac80: stur            x0, [fp, #-0x78]
    // 0x91ac84: r0 = _$FailureImpl()
    //     0x91ac84: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x91ac88: ldur            x1, [fp, #-0x78]
    // 0x91ac8c: StoreField: r0->field_b = r1
    //     0x91ac8c: stur            w1, [x0, #0xb]
    // 0x91ac90: r0 = ReturnAsyncNotFuture()
    //     0x91ac90: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91ac94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ac94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ac98: b               #0x91a8b4
    // 0x91ac9c: r9 = keystore
    //     0x91ac9c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91aca0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91aca0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91aca4: r9 = keystore
    //     0x91aca4: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91aca8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91aca8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91acac: r9 = keystore
    //     0x91acac: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91acb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91acb0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91acb4: r9 = keystore
    //     0x91acb4: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91acb8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91acb8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91acbc: r9 = keystore
    //     0x91acbc: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91acc0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91acc0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ findCountdown(/* No info */) async {
    // ** addr: 0x91f5b4, size: 0x3a0
    // 0x91f5b4: EnterFrame
    //     0x91f5b4: stp             fp, lr, [SP, #-0x10]!
    //     0x91f5b8: mov             fp, SP
    // 0x91f5bc: AllocStack(0xa8)
    //     0x91f5bc: sub             SP, SP, #0xa8
    // 0x91f5c0: SetupParameters(HomeRepository this /* r1 => r1, fp-0x58 */)
    //     0x91f5c0: stur            NULL, [fp, #-8]
    //     0x91f5c4: stur            x1, [fp, #-0x58]
    // 0x91f5c8: CheckStackOverflow
    //     0x91f5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91f5cc: cmp             SP, x16
    //     0x91f5d0: b.ls            #0x91f934
    // 0x91f5d4: InitAsync() -> Future<ApiResult<Countdown?>>
    //     0x91f5d4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fb30] TypeArguments: <ApiResult<Countdown?>>
    //     0x91f5d8: ldr             x0, [x0, #0xb30]
    //     0x91f5dc: bl              #0x661298  ; InitAsyncStub
    // 0x91f5e0: ldur            x0, [fp, #-0x58]
    // 0x91f5e4: LoadField: r1 = r0->field_f
    //     0x91f5e4: ldur            w1, [x0, #0xf]
    // 0x91f5e8: DecompressPointer r1
    //     0x91f5e8: add             x1, x1, HEAP, lsl #32
    // 0x91f5ec: r0 = homeUpdateConfig()
    //     0x91f5ec: bl              #0x91b5ec  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::homeUpdateConfig
    // 0x91f5f0: LoadField: r1 = r0->field_f
    //     0x91f5f0: ldur            w1, [x0, #0xf]
    // 0x91f5f4: DecompressPointer r1
    //     0x91f5f4: add             x1, x1, HEAP, lsl #32
    // 0x91f5f8: tbz             w1, #4, #0x91f6b4
    // 0x91f5fc: ldur            x1, [fp, #-0x58]
    // 0x91f600: LoadField: r0 = r1->field_b
    //     0x91f600: ldur            w0, [x1, #0xb]
    // 0x91f604: DecompressPointer r0
    //     0x91f604: add             x0, x0, HEAP, lsl #32
    // 0x91f608: stur            x0, [fp, #-0x60]
    // 0x91f60c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91f60c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91f610: ldr             x0, [x0, #0x2728]
    //     0x91f614: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91f618: cmp             w0, w16
    //     0x91f61c: b.ne            #0x91f628
    //     0x91f620: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91f624: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91f628: r16 = <Countdown>
    //     0x91f628: ldr             x16, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91f62c: stp             x0, x16, [SP, #8]
    // 0x91f630: r16 = "v2_countdowns"
    //     0x91f630: ldr             x16, [PP, #0x7be8]  ; [pp+0x7be8] "v2_countdowns"
    // 0x91f634: str             x16, [SP]
    // 0x91f638: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91f638: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91f63c: r0 = box()
    //     0x91f63c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91f640: mov             x1, x0
    // 0x91f644: stur            x0, [fp, #-0x60]
    // 0x91f648: r0 = checkOpen()
    //     0x91f648: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91f64c: ldur            x0, [fp, #-0x60]
    // 0x91f650: LoadField: r1 = r0->field_1b
    //     0x91f650: ldur            w1, [x0, #0x1b]
    // 0x91f654: DecompressPointer r1
    //     0x91f654: add             x1, x1, HEAP, lsl #32
    // 0x91f658: r16 = Sentinel
    //     0x91f658: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91f65c: cmp             w1, w16
    // 0x91f660: b.eq            #0x91f93c
    // 0x91f664: r0 = getValues()
    //     0x91f664: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x91f668: stur            x0, [fp, #-0x68]
    // 0x91f66c: LoadField: r3 = r0->field_7
    //     0x91f66c: ldur            w3, [x0, #7]
    // 0x91f670: DecompressPointer r3
    //     0x91f670: add             x3, x3, HEAP, lsl #32
    // 0x91f674: mov             x1, x3
    // 0x91f678: mov             x2, x0
    // 0x91f67c: stur            x3, [fp, #-0x60]
    // 0x91f680: r0 = _GrowableList.of()
    //     0x91f680: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x91f684: ldur            x1, [fp, #-0x58]
    // 0x91f688: mov             x2, x0
    // 0x91f68c: r0 = getCountdown()
    //     0x91f68c: bl              #0x92007c  ; [package:nuonline/app/data/repositories/home_repository.dart] HomeRepository::getCountdown
    // 0x91f690: r1 = <Countdown?>
    //     0x91f690: add             x1, PP, #0x34, lsl #12  ; [pp+0x341b0] TypeArguments: <Countdown?>
    //     0x91f694: ldr             x1, [x1, #0x1b0]
    // 0x91f698: stur            x0, [fp, #-0x60]
    // 0x91f69c: r0 = _$SuccessImpl()
    //     0x91f69c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91f6a0: mov             x1, x0
    // 0x91f6a4: ldur            x0, [fp, #-0x60]
    // 0x91f6a8: StoreField: r1->field_b = r0
    //     0x91f6a8: stur            w0, [x1, #0xb]
    // 0x91f6ac: mov             x0, x1
    // 0x91f6b0: r0 = ReturnAsyncNotFuture()
    //     0x91f6b0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91f6b4: ldur            x1, [fp, #-0x58]
    // 0x91f6b8: LoadField: r0 = r1->field_7
    //     0x91f6b8: ldur            w0, [x1, #7]
    // 0x91f6bc: DecompressPointer r0
    //     0x91f6bc: add             x0, x0, HEAP, lsl #32
    // 0x91f6c0: stp             x0, NULL, [SP, #0x20]
    // 0x91f6c4: r16 = "/home/<USER>"
    //     0x91f6c4: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fb38] "/home/<USER>"
    //     0x91f6c8: ldr             x16, [x16, #0xb38]
    // 0x91f6cc: r30 = false
    //     0x91f6cc: add             lr, NULL, #0x30  ; false
    // 0x91f6d0: stp             lr, x16, [SP, #0x10]
    // 0x91f6d4: r16 = Instance_Duration
    //     0x91f6d4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x91f6d8: ldr             x16, [x16, #0x7b8]
    // 0x91f6dc: r30 = Instance_Duration
    //     0x91f6dc: add             lr, PP, #0xf, lsl #12  ; [pp+0xf7b8] Obj!Duration@e3a111
    //     0x91f6e0: ldr             lr, [lr, #0x7b8]
    // 0x91f6e4: stp             lr, x16, [SP]
    // 0x91f6e8: r4 = const [0x1, 0x5, 0x5, 0x3, receiveTimeout, 0x4, sendTimeout, 0x3, null]
    //     0x91f6e8: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dae0] List(9) [0x1, 0x5, 0x5, 0x3, "receiveTimeout", 0x4, "sendTimeout", 0x3, Null]
    //     0x91f6ec: ldr             x4, [x4, #0xae0]
    // 0x91f6f0: r0 = withRetry()
    //     0x91f6f0: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x91f6f4: mov             x1, x0
    // 0x91f6f8: stur            x1, [fp, #-0x60]
    // 0x91f6fc: r0 = Await()
    //     0x91f6fc: bl              #0x661044  ; AwaitStub
    // 0x91f700: LoadField: r3 = r0->field_b
    //     0x91f700: ldur            w3, [x0, #0xb]
    // 0x91f704: DecompressPointer r3
    //     0x91f704: add             x3, x3, HEAP, lsl #32
    // 0x91f708: mov             x0, x3
    // 0x91f70c: stur            x3, [fp, #-0x60]
    // 0x91f710: r2 = Null
    //     0x91f710: mov             x2, NULL
    // 0x91f714: r1 = Null
    //     0x91f714: mov             x1, NULL
    // 0x91f718: r4 = 60
    //     0x91f718: movz            x4, #0x3c
    // 0x91f71c: branchIfSmi(r0, 0x91f728)
    //     0x91f71c: tbz             w0, #0, #0x91f728
    // 0x91f720: r4 = LoadClassIdInstr(r0)
    //     0x91f720: ldur            x4, [x0, #-1]
    //     0x91f724: ubfx            x4, x4, #0xc, #0x14
    // 0x91f728: sub             x4, x4, #0x5e
    // 0x91f72c: cmp             x4, #1
    // 0x91f730: b.ls            #0x91f744
    // 0x91f734: r8 = String
    //     0x91f734: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x91f738: r3 = Null
    //     0x91f738: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3fb40] Null
    //     0x91f73c: ldr             x3, [x3, #0xb40]
    // 0x91f740: r0 = String()
    //     0x91f740: bl              #0xed43b0  ; IsType_String_Stub
    // 0x91f744: ldur            x1, [fp, #-0x60]
    // 0x91f748: r0 = jsonDecode()
    //     0x91f748: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x91f74c: mov             x1, x0
    // 0x91f750: r0 = fromResponse()
    //     0x91f750: bl              #0x91f9b4  ; [package:nuonline/app/data/models/countdown.dart] Countdown::fromResponse
    // 0x91f754: ldur            x1, [fp, #-0x58]
    // 0x91f758: stur            x0, [fp, #-0x68]
    // 0x91f75c: LoadField: r2 = r1->field_b
    //     0x91f75c: ldur            w2, [x1, #0xb]
    // 0x91f760: DecompressPointer r2
    //     0x91f760: add             x2, x2, HEAP, lsl #32
    // 0x91f764: stur            x2, [fp, #-0x60]
    // 0x91f768: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91f768: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91f76c: ldr             x0, [x0, #0x2728]
    //     0x91f770: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91f774: cmp             w0, w16
    //     0x91f778: b.ne            #0x91f784
    //     0x91f77c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91f780: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91f784: stur            x0, [fp, #-0x70]
    // 0x91f788: r16 = <Countdown>
    //     0x91f788: ldr             x16, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91f78c: stp             x0, x16, [SP, #8]
    // 0x91f790: r16 = "v2_countdowns"
    //     0x91f790: ldr             x16, [PP, #0x7be8]  ; [pp+0x7be8] "v2_countdowns"
    // 0x91f794: str             x16, [SP]
    // 0x91f798: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91f798: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91f79c: r0 = box()
    //     0x91f79c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91f7a0: mov             x1, x0
    // 0x91f7a4: r0 = clear()
    //     0x91f7a4: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0x91f7a8: mov             x1, x0
    // 0x91f7ac: stur            x1, [fp, #-0x78]
    // 0x91f7b0: r0 = Await()
    //     0x91f7b0: bl              #0x661044  ; AwaitStub
    // 0x91f7b4: r16 = <Countdown>
    //     0x91f7b4: ldr             x16, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91f7b8: ldur            lr, [fp, #-0x70]
    // 0x91f7bc: stp             lr, x16, [SP, #8]
    // 0x91f7c0: r16 = "v2_countdowns"
    //     0x91f7c0: ldr             x16, [PP, #0x7be8]  ; [pp+0x7be8] "v2_countdowns"
    // 0x91f7c4: str             x16, [SP]
    // 0x91f7c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91f7c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91f7cc: r0 = box()
    //     0x91f7cc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91f7d0: mov             x1, x0
    // 0x91f7d4: ldur            x2, [fp, #-0x68]
    // 0x91f7d8: r0 = addAll()
    //     0x91f7d8: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0x91f7dc: mov             x1, x0
    // 0x91f7e0: stur            x1, [fp, #-0x60]
    // 0x91f7e4: r0 = Await()
    //     0x91f7e4: bl              #0x661044  ; AwaitStub
    // 0x91f7e8: ldur            x1, [fp, #-0x58]
    // 0x91f7ec: ldur            x2, [fp, #-0x68]
    // 0x91f7f0: r0 = getCountdown()
    //     0x91f7f0: bl              #0x92007c  ; [package:nuonline/app/data/repositories/home_repository.dart] HomeRepository::getCountdown
    // 0x91f7f4: r1 = <Countdown?>
    //     0x91f7f4: add             x1, PP, #0x34, lsl #12  ; [pp+0x341b0] TypeArguments: <Countdown?>
    //     0x91f7f8: ldr             x1, [x1, #0x1b0]
    // 0x91f7fc: stur            x0, [fp, #-0x60]
    // 0x91f800: r0 = _$SuccessImpl()
    //     0x91f800: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91f804: mov             x1, x0
    // 0x91f808: ldur            x0, [fp, #-0x60]
    // 0x91f80c: StoreField: r1->field_b = r0
    //     0x91f80c: stur            w0, [x1, #0xb]
    // 0x91f810: mov             x0, x1
    // 0x91f814: r0 = ReturnAsyncNotFuture()
    //     0x91f814: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91f818: sub             SP, fp, #0xa8
    // 0x91f81c: mov             x1, x0
    // 0x91f820: stur            x0, [fp, #-0x60]
    // 0x91f824: ldur            x0, [fp, #-0x10]
    // 0x91f828: LoadField: r2 = r0->field_b
    //     0x91f828: ldur            w2, [x0, #0xb]
    // 0x91f82c: DecompressPointer r2
    //     0x91f82c: add             x2, x2, HEAP, lsl #32
    // 0x91f830: stur            x2, [fp, #-0x58]
    // 0x91f834: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91f834: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91f838: ldr             x0, [x0, #0x2728]
    //     0x91f83c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91f840: cmp             w0, w16
    //     0x91f844: b.ne            #0x91f850
    //     0x91f848: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91f84c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91f850: r16 = <Countdown>
    //     0x91f850: ldr             x16, [PP, #0x7b50]  ; [pp+0x7b50] TypeArguments: <Countdown>
    // 0x91f854: stp             x0, x16, [SP, #8]
    // 0x91f858: r16 = "v2_countdowns"
    //     0x91f858: ldr             x16, [PP, #0x7be8]  ; [pp+0x7be8] "v2_countdowns"
    // 0x91f85c: str             x16, [SP]
    // 0x91f860: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91f860: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91f864: r0 = box()
    //     0x91f864: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91f868: mov             x1, x0
    // 0x91f86c: stur            x0, [fp, #-0x68]
    // 0x91f870: r0 = checkOpen()
    //     0x91f870: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91f874: ldur            x0, [fp, #-0x68]
    // 0x91f878: LoadField: r1 = r0->field_1b
    //     0x91f878: ldur            w1, [x0, #0x1b]
    // 0x91f87c: DecompressPointer r1
    //     0x91f87c: add             x1, x1, HEAP, lsl #32
    // 0x91f880: r16 = Sentinel
    //     0x91f880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91f884: cmp             w1, w16
    // 0x91f888: b.eq            #0x91f944
    // 0x91f88c: LoadField: r0 = r1->field_13
    //     0x91f88c: ldur            w0, [x1, #0x13]
    // 0x91f890: DecompressPointer r0
    //     0x91f890: add             x0, x0, HEAP, lsl #32
    // 0x91f894: LoadField: r1 = r0->field_1f
    //     0x91f894: ldur            x1, [x0, #0x1f]
    // 0x91f898: cmp             x1, #0
    // 0x91f89c: b.le            #0x91f910
    // 0x91f8a0: ldur            x1, [fp, #-0x58]
    // 0x91f8a4: r0 = countdowns()
    //     0x91f8a4: bl              #0x91f954  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::countdowns
    // 0x91f8a8: mov             x1, x0
    // 0x91f8ac: stur            x0, [fp, #-0x58]
    // 0x91f8b0: r0 = checkOpen()
    //     0x91f8b0: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91f8b4: ldur            x0, [fp, #-0x58]
    // 0x91f8b8: LoadField: r1 = r0->field_1b
    //     0x91f8b8: ldur            w1, [x0, #0x1b]
    // 0x91f8bc: DecompressPointer r1
    //     0x91f8bc: add             x1, x1, HEAP, lsl #32
    // 0x91f8c0: r16 = Sentinel
    //     0x91f8c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91f8c4: cmp             w1, w16
    // 0x91f8c8: b.eq            #0x91f94c
    // 0x91f8cc: r0 = getValues()
    //     0x91f8cc: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x91f8d0: LoadField: r1 = r0->field_7
    //     0x91f8d0: ldur            w1, [x0, #7]
    // 0x91f8d4: DecompressPointer r1
    //     0x91f8d4: add             x1, x1, HEAP, lsl #32
    // 0x91f8d8: mov             x2, x0
    // 0x91f8dc: r0 = _GrowableList.of()
    //     0x91f8dc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x91f8e0: ldur            x1, [fp, #-0x10]
    // 0x91f8e4: mov             x2, x0
    // 0x91f8e8: r0 = getCountdown()
    //     0x91f8e8: bl              #0x92007c  ; [package:nuonline/app/data/repositories/home_repository.dart] HomeRepository::getCountdown
    // 0x91f8ec: r1 = <Countdown?>
    //     0x91f8ec: add             x1, PP, #0x34, lsl #12  ; [pp+0x341b0] TypeArguments: <Countdown?>
    //     0x91f8f0: ldr             x1, [x1, #0x1b0]
    // 0x91f8f4: stur            x0, [fp, #-0x58]
    // 0x91f8f8: r0 = _$SuccessImpl()
    //     0x91f8f8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91f8fc: mov             x1, x0
    // 0x91f900: ldur            x0, [fp, #-0x58]
    // 0x91f904: StoreField: r1->field_b = r0
    //     0x91f904: stur            w0, [x1, #0xb]
    // 0x91f908: mov             x0, x1
    // 0x91f90c: r0 = ReturnAsyncNotFuture()
    //     0x91f90c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91f910: ldur            x1, [fp, #-0x60]
    // 0x91f914: r0 = getDioException()
    //     0x91f914: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x91f918: r1 = <Countdown?>
    //     0x91f918: add             x1, PP, #0x34, lsl #12  ; [pp+0x341b0] TypeArguments: <Countdown?>
    //     0x91f91c: ldr             x1, [x1, #0x1b0]
    // 0x91f920: stur            x0, [fp, #-0x58]
    // 0x91f924: r0 = _$FailureImpl()
    //     0x91f924: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x91f928: ldur            x1, [fp, #-0x58]
    // 0x91f92c: StoreField: r0->field_b = r1
    //     0x91f92c: stur            w1, [x0, #0xb]
    // 0x91f930: r0 = ReturnAsyncNotFuture()
    //     0x91f930: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91f934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91f934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91f938: b               #0x91f5d4
    // 0x91f93c: r9 = keystore
    //     0x91f93c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91f940: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91f940: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91f944: r9 = keystore
    //     0x91f944: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91f948: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91f948: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91f94c: r9 = keystore
    //     0x91f94c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x91f950: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91f950: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getCountdown(/* No info */) {
    // ** addr: 0x92007c, size: 0x9c
    // 0x92007c: EnterFrame
    //     0x92007c: stp             fp, lr, [SP, #-0x10]!
    //     0x920080: mov             fp, SP
    // 0x920084: AllocStack(0x10)
    //     0x920084: sub             SP, SP, #0x10
    // 0x920088: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x920088: mov             x0, x2
    //     0x92008c: stur            x2, [fp, #-8]
    // 0x920090: CheckStackOverflow
    //     0x920090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920094: cmp             SP, x16
    //     0x920098: b.ls            #0x920110
    // 0x92009c: r1 = Function '<anonymous closure>':.
    //     0x92009c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fc30] AnonymousClosure: (0x92018c), in [package:nuonline/app/data/repositories/home_repository.dart] HomeRepository::getCountdown (0x92007c)
    //     0x9200a0: ldr             x1, [x1, #0xc30]
    // 0x9200a4: r2 = Null
    //     0x9200a4: mov             x2, NULL
    // 0x9200a8: r0 = AllocateClosure()
    //     0x9200a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9200ac: ldur            x1, [fp, #-8]
    // 0x9200b0: mov             x2, x0
    // 0x9200b4: r0 = where()
    //     0x9200b4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x9200b8: LoadField: r1 = r0->field_7
    //     0x9200b8: ldur            w1, [x0, #7]
    // 0x9200bc: DecompressPointer r1
    //     0x9200bc: add             x1, x1, HEAP, lsl #32
    // 0x9200c0: mov             x2, x0
    // 0x9200c4: r0 = _GrowableList.of()
    //     0x9200c4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x9200c8: r1 = Function '<anonymous closure>':.
    //     0x9200c8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fc38] AnonymousClosure: (0x920118), in [package:nuonline/app/data/repositories/home_repository.dart] HomeRepository::getCountdown (0x92007c)
    //     0x9200cc: ldr             x1, [x1, #0xc38]
    // 0x9200d0: r2 = Null
    //     0x9200d0: mov             x2, NULL
    // 0x9200d4: stur            x0, [fp, #-8]
    // 0x9200d8: r0 = AllocateClosure()
    //     0x9200d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9200dc: str             x0, [SP]
    // 0x9200e0: ldur            x1, [fp, #-8]
    // 0x9200e4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x9200e4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x9200e8: r0 = sort()
    //     0x9200e8: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x9200ec: ldur            x1, [fp, #-8]
    // 0x9200f0: LoadField: r0 = r1->field_b
    //     0x9200f0: ldur            w0, [x1, #0xb]
    // 0x9200f4: cbz             w0, #0x920100
    // 0x9200f8: r0 = first()
    //     0x9200f8: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x9200fc: b               #0x920104
    // 0x920100: r0 = Null
    //     0x920100: mov             x0, NULL
    // 0x920104: LeaveFrame
    //     0x920104: mov             SP, fp
    //     0x920108: ldp             fp, lr, [SP], #0x10
    // 0x92010c: ret
    //     0x92010c: ret             
    // 0x920110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920114: b               #0x92009c
  }
  [closure] int <anonymous closure>(dynamic, Countdown, Countdown) {
    // ** addr: 0x920118, size: 0x74
    // 0x920118: EnterFrame
    //     0x920118: stp             fp, lr, [SP, #-0x10]!
    //     0x92011c: mov             fp, SP
    // 0x920120: CheckStackOverflow
    //     0x920120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920124: cmp             SP, x16
    //     0x920128: b.ls            #0x920184
    // 0x92012c: ldr             x0, [fp, #0x10]
    // 0x920130: LoadField: r1 = r0->field_2f
    //     0x920130: ldur            w1, [x0, #0x2f]
    // 0x920134: DecompressPointer r1
    //     0x920134: add             x1, x1, HEAP, lsl #32
    // 0x920138: ldr             x0, [fp, #0x18]
    // 0x92013c: LoadField: r2 = r0->field_2f
    //     0x92013c: ldur            w2, [x0, #0x2f]
    // 0x920140: DecompressPointer r2
    //     0x920140: add             x2, x2, HEAP, lsl #32
    // 0x920144: r0 = LoadClassIdInstr(r1)
    //     0x920144: ldur            x0, [x1, #-1]
    //     0x920148: ubfx            x0, x0, #0xc, #0x14
    // 0x92014c: r0 = GDT[cid_x0 + 0x138b7]()
    //     0x92014c: movz            x17, #0x38b7
    //     0x920150: movk            x17, #0x1, lsl #16
    //     0x920154: add             lr, x0, x17
    //     0x920158: ldr             lr, [x21, lr, lsl #3]
    //     0x92015c: blr             lr
    // 0x920160: mov             x2, x0
    // 0x920164: r0 = BoxInt64Instr(r2)
    //     0x920164: sbfiz           x0, x2, #1, #0x1f
    //     0x920168: cmp             x2, x0, asr #1
    //     0x92016c: b.eq            #0x920178
    //     0x920170: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x920174: stur            x2, [x0, #7]
    // 0x920178: LeaveFrame
    //     0x920178: mov             SP, fp
    //     0x92017c: ldp             fp, lr, [SP], #0x10
    // 0x920180: ret
    //     0x920180: ret             
    // 0x920184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920184: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920188: b               #0x92012c
  }
  [closure] bool <anonymous closure>(dynamic, Countdown) {
    // ** addr: 0x92018c, size: 0x80
    // 0x92018c: EnterFrame
    //     0x92018c: stp             fp, lr, [SP, #-0x10]!
    //     0x920190: mov             fp, SP
    // 0x920194: AllocStack(0x10)
    //     0x920194: sub             SP, SP, #0x10
    // 0x920198: CheckStackOverflow
    //     0x920198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92019c: cmp             SP, x16
    //     0x9201a0: b.ls            #0x920204
    // 0x9201a4: ldr             x0, [fp, #0x10]
    // 0x9201a8: LoadField: r1 = r0->field_2f
    //     0x9201a8: ldur            w1, [x0, #0x2f]
    // 0x9201ac: DecompressPointer r1
    //     0x9201ac: add             x1, x1, HEAP, lsl #32
    // 0x9201b0: stur            x1, [fp, #-8]
    // 0x9201b4: r0 = DateTime()
    //     0x9201b4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x9201b8: mov             x1, x0
    // 0x9201bc: r0 = false
    //     0x9201bc: add             x0, NULL, #0x30  ; false
    // 0x9201c0: stur            x1, [fp, #-0x10]
    // 0x9201c4: StoreField: r1->field_13 = r0
    //     0x9201c4: stur            w0, [x1, #0x13]
    // 0x9201c8: r0 = _getCurrentMicros()
    //     0x9201c8: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x9201cc: r1 = LoadInt32Instr(r0)
    //     0x9201cc: sbfx            x1, x0, #1, #0x1f
    //     0x9201d0: tbz             w0, #0, #0x9201d8
    //     0x9201d4: ldur            x1, [x0, #7]
    // 0x9201d8: ldur            x2, [fp, #-0x10]
    // 0x9201dc: StoreField: r2->field_7 = r1
    //     0x9201dc: stur            x1, [x2, #7]
    // 0x9201e0: ldur            x1, [fp, #-8]
    // 0x9201e4: r0 = LoadClassIdInstr(r1)
    //     0x9201e4: ldur            x0, [x1, #-1]
    //     0x9201e8: ubfx            x0, x0, #0xc, #0x14
    // 0x9201ec: r0 = GDT[cid_x0 + -0xf0c]()
    //     0x9201ec: sub             lr, x0, #0xf0c
    //     0x9201f0: ldr             lr, [x21, lr, lsl #3]
    //     0x9201f4: blr             lr
    // 0x9201f8: LeaveFrame
    //     0x9201f8: mov             SP, fp
    //     0x9201fc: ldp             fp, lr, [SP], #0x10
    // 0x920200: ret
    //     0x920200: ret             
    // 0x920204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920204: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920208: b               #0x9201a4
  }
}
