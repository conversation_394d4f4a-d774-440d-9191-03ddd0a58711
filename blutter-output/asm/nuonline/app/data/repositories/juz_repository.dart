// lib: , url: package:nuonline/app/data/repositories/juz_repository.dart

// class id: 1050086, size: 0x8
class :: {
}

// class id: 1090, size: 0x10, field offset: 0x8
class JuzRepository extends Object {

  _ all(/* No info */) async {
    // ** addr: 0x9226e0, size: 0xe0
    // 0x9226e0: EnterFrame
    //     0x9226e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9226e4: mov             fp, SP
    // 0x9226e8: AllocStack(0x30)
    //     0x9226e8: sub             SP, SP, #0x30
    // 0x9226ec: SetupParameters(JuzRepository this /* r1 => r1, fp-0x10 */)
    //     0x9226ec: stur            NULL, [fp, #-8]
    //     0x9226f0: stur            x1, [fp, #-0x10]
    // 0x9226f4: CheckStackOverflow
    //     0x9226f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9226f8: cmp             SP, x16
    //     0x9226fc: b.ls            #0x9227b0
    // 0x922700: InitAsync() -> Future<Either<StorageError, List<Juz>>>
    //     0x922700: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3dbd0] TypeArguments: <Either<StorageError, List<Juz>>>
    //     0x922704: ldr             x0, [x0, #0xbd0]
    //     0x922708: bl              #0x661298  ; InitAsyncStub
    // 0x92270c: ldur            x1, [fp, #-0x10]
    // 0x922710: r0 = init()
    //     0x922710: bl              #0x922828  ; [package:nuonline/app/data/repositories/juz_repository.dart] JuzRepository::init
    // 0x922714: mov             x1, x0
    // 0x922718: stur            x1, [fp, #-0x18]
    // 0x92271c: r0 = Await()
    //     0x92271c: bl              #0x661044  ; AwaitStub
    // 0x922720: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x922720: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x922724: ldr             x0, [x0, #0x2728]
    //     0x922728: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92272c: cmp             w0, w16
    //     0x922730: b.ne            #0x92273c
    //     0x922734: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x922738: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92273c: r16 = <Juz>
    //     0x92273c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e8] TypeArguments: <Juz>
    //     0x922740: ldr             x16, [x16, #0x1e8]
    // 0x922744: stp             x0, x16, [SP, #8]
    // 0x922748: r16 = "v2_quran_juz"
    //     0x922748: add             x16, PP, #0xf, lsl #12  ; [pp+0xf210] "v2_quran_juz"
    //     0x92274c: ldr             x16, [x16, #0x210]
    // 0x922750: str             x16, [SP]
    // 0x922754: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x922754: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x922758: r0 = box()
    //     0x922758: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x92275c: mov             x1, x0
    // 0x922760: stur            x0, [fp, #-0x10]
    // 0x922764: r0 = checkOpen()
    //     0x922764: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x922768: ldur            x0, [fp, #-0x10]
    // 0x92276c: LoadField: r1 = r0->field_1b
    //     0x92276c: ldur            w1, [x0, #0x1b]
    // 0x922770: DecompressPointer r1
    //     0x922770: add             x1, x1, HEAP, lsl #32
    // 0x922774: r16 = Sentinel
    //     0x922774: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x922778: cmp             w1, w16
    // 0x92277c: b.eq            #0x9227b8
    // 0x922780: r0 = getValues()
    //     0x922780: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x922784: LoadField: r1 = r0->field_7
    //     0x922784: ldur            w1, [x0, #7]
    // 0x922788: DecompressPointer r1
    //     0x922788: add             x1, x1, HEAP, lsl #32
    // 0x92278c: mov             x2, x0
    // 0x922790: r0 = _GrowableList.of()
    //     0x922790: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x922794: r1 = <StorageError, List<Juz>>
    //     0x922794: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbd8] TypeArguments: <StorageError, List<Juz>>
    //     0x922798: ldr             x1, [x1, #0xbd8]
    // 0x92279c: stur            x0, [fp, #-0x10]
    // 0x9227a0: r0 = Right()
    //     0x9227a0: bl              #0x8c1ae0  ; AllocateRightStub -> Right<X0, X1> (size=0x10)
    // 0x9227a4: ldur            x1, [fp, #-0x10]
    // 0x9227a8: StoreField: r0->field_b = r1
    //     0x9227a8: stur            w1, [x0, #0xb]
    // 0x9227ac: r0 = ReturnAsyncNotFuture()
    //     0x9227ac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9227b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9227b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9227b4: b               #0x922700
    // 0x9227b8: r9 = keystore
    //     0x9227b8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x9227bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9227bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) async {
    // ** addr: 0x922828, size: 0x184
    // 0x922828: EnterFrame
    //     0x922828: stp             fp, lr, [SP, #-0x10]!
    //     0x92282c: mov             fp, SP
    // 0x922830: AllocStack(0x38)
    //     0x922830: sub             SP, SP, #0x38
    // 0x922834: SetupParameters(JuzRepository this /* r1 => r1, fp-0x10 */)
    //     0x922834: stur            NULL, [fp, #-8]
    //     0x922838: stur            x1, [fp, #-0x10]
    // 0x92283c: CheckStackOverflow
    //     0x92283c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922840: cmp             SP, x16
    //     0x922844: b.ls            #0x92299c
    // 0x922848: InitAsync() -> Future<void?>
    //     0x922848: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x92284c: bl              #0x661298  ; InitAsyncStub
    // 0x922850: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x922850: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x922854: ldr             x0, [x0, #0x2728]
    //     0x922858: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92285c: cmp             w0, w16
    //     0x922860: b.ne            #0x92286c
    //     0x922864: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x922868: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92286c: stur            x0, [fp, #-0x18]
    // 0x922870: r16 = <Juz>
    //     0x922870: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e8] TypeArguments: <Juz>
    //     0x922874: ldr             x16, [x16, #0x1e8]
    // 0x922878: stp             x0, x16, [SP, #8]
    // 0x92287c: r16 = "v2_quran_juz"
    //     0x92287c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf210] "v2_quran_juz"
    //     0x922880: ldr             x16, [x16, #0x210]
    // 0x922884: str             x16, [SP]
    // 0x922888: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x922888: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92288c: r0 = box()
    //     0x92288c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x922890: mov             x1, x0
    // 0x922894: stur            x0, [fp, #-0x20]
    // 0x922898: r0 = checkOpen()
    //     0x922898: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x92289c: ldur            x0, [fp, #-0x20]
    // 0x9228a0: LoadField: r1 = r0->field_1b
    //     0x9228a0: ldur            w1, [x0, #0x1b]
    // 0x9228a4: DecompressPointer r1
    //     0x9228a4: add             x1, x1, HEAP, lsl #32
    // 0x9228a8: r16 = Sentinel
    //     0x9228a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9228ac: cmp             w1, w16
    // 0x9228b0: b.eq            #0x9229a4
    // 0x9228b4: LoadField: r0 = r1->field_13
    //     0x9228b4: ldur            w0, [x1, #0x13]
    // 0x9228b8: DecompressPointer r0
    //     0x9228b8: add             x0, x0, HEAP, lsl #32
    // 0x9228bc: LoadField: r1 = r0->field_1f
    //     0x9228bc: ldur            x1, [x0, #0x1f]
    // 0x9228c0: cbnz            x1, #0x922994
    // 0x9228c4: ldur            x0, [fp, #-0x10]
    // 0x9228c8: LoadField: r1 = r0->field_b
    //     0x9228c8: ldur            w1, [x0, #0xb]
    // 0x9228cc: DecompressPointer r1
    //     0x9228cc: add             x1, x1, HEAP, lsl #32
    // 0x9228d0: r16 = <List>
    //     0x9228d0: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x9228d4: stp             x1, x16, [SP, #8]
    // 0x9228d8: r16 = "juzs.json"
    //     0x9228d8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dbe0] "juzs.json"
    //     0x9228dc: ldr             x16, [x16, #0xbe0]
    // 0x9228e0: str             x16, [SP]
    // 0x9228e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9228e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9228e8: r0 = read()
    //     0x9228e8: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x9228ec: r1 = Function '<anonymous closure>':.
    //     0x9228ec: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbe8] AnonymousClosure: (0x9229f8), in [package:nuonline/app/data/repositories/juz_repository.dart] JuzRepository::init (0x922828)
    //     0x9228f0: ldr             x1, [x1, #0xbe8]
    // 0x9228f4: r2 = Null
    //     0x9228f4: mov             x2, NULL
    // 0x9228f8: stur            x0, [fp, #-0x20]
    // 0x9228fc: r0 = AllocateClosure()
    //     0x9228fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x922900: r16 = <Iterable<Juz>>
    //     0x922900: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dbf0] TypeArguments: <Iterable<Juz>>
    //     0x922904: ldr             x16, [x16, #0xbf0]
    // 0x922908: ldur            lr, [fp, #-0x20]
    // 0x92290c: stp             lr, x16, [SP, #8]
    // 0x922910: str             x0, [SP]
    // 0x922914: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x922914: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x922918: r0 = then()
    //     0x922918: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x92291c: mov             x1, x0
    // 0x922920: stur            x1, [fp, #-0x20]
    // 0x922924: r0 = Await()
    //     0x922924: bl              #0x661044  ; AwaitStub
    // 0x922928: stur            x0, [fp, #-0x10]
    // 0x92292c: r16 = <Juz>
    //     0x92292c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e8] TypeArguments: <Juz>
    //     0x922930: ldr             x16, [x16, #0x1e8]
    // 0x922934: ldur            lr, [fp, #-0x18]
    // 0x922938: stp             lr, x16, [SP, #8]
    // 0x92293c: r16 = "v2_quran_juz"
    //     0x92293c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf210] "v2_quran_juz"
    //     0x922940: ldr             x16, [x16, #0x210]
    // 0x922944: str             x16, [SP]
    // 0x922948: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x922948: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92294c: r0 = box()
    //     0x92294c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x922950: r1 = Function '<anonymous closure>':.
    //     0x922950: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbf8] AnonymousClosure: (0x9229ac), in [package:nuonline/app/data/repositories/juz_repository.dart] JuzRepository::init (0x922828)
    //     0x922954: ldr             x1, [x1, #0xbf8]
    // 0x922958: r2 = Null
    //     0x922958: mov             x2, NULL
    // 0x92295c: stur            x0, [fp, #-0x18]
    // 0x922960: r0 = AllocateClosure()
    //     0x922960: bl              #0xec1630  ; AllocateClosureStub
    // 0x922964: ldur            x2, [fp, #-0x10]
    // 0x922968: mov             x3, x0
    // 0x92296c: r1 = <dynamic, Juz>
    //     0x92296c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc00] TypeArguments: <dynamic, Juz>
    //     0x922970: ldr             x1, [x1, #0xc00]
    // 0x922974: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x922974: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x922978: r0 = LinkedHashMap.fromIterable()
    //     0x922978: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x92297c: ldur            x1, [fp, #-0x18]
    // 0x922980: mov             x2, x0
    // 0x922984: r0 = putAll()
    //     0x922984: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x922988: mov             x1, x0
    // 0x92298c: stur            x1, [fp, #-0x10]
    // 0x922990: r0 = Await()
    //     0x922990: bl              #0x661044  ; AwaitStub
    // 0x922994: r0 = Null
    //     0x922994: mov             x0, NULL
    // 0x922998: r0 = ReturnAsyncNotFuture()
    //     0x922998: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x92299c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92299c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9229a0: b               #0x922848
    // 0x9229a4: r9 = keystore
    //     0x9229a4: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x9229a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9229a8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x9229ac, size: 0x4c
    // 0x9229ac: EnterFrame
    //     0x9229ac: stp             fp, lr, [SP, #-0x10]!
    //     0x9229b0: mov             fp, SP
    // 0x9229b4: AllocStack(0x8)
    //     0x9229b4: sub             SP, SP, #8
    // 0x9229b8: CheckStackOverflow
    //     0x9229b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9229bc: cmp             SP, x16
    //     0x9229c0: b.ls            #0x9229f0
    // 0x9229c4: ldr             x16, [fp, #0x10]
    // 0x9229c8: str             x16, [SP]
    // 0x9229cc: r4 = 0
    //     0x9229cc: movz            x4, #0
    // 0x9229d0: ldr             x0, [SP]
    // 0x9229d4: r16 = UnlinkedCall_0x5f3c08
    //     0x9229d4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dc08] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x9229d8: add             x16, x16, #0xc08
    // 0x9229dc: ldp             x5, lr, [x16]
    // 0x9229e0: blr             lr
    // 0x9229e4: LeaveFrame
    //     0x9229e4: mov             SP, fp
    //     0x9229e8: ldp             fp, lr, [SP], #0x10
    // 0x9229ec: ret
    //     0x9229ec: ret             
    // 0x9229f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9229f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9229f4: b               #0x9229c4
  }
  [closure] Iterable<Juz> <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x9229f8, size: 0x74
    // 0x9229f8: EnterFrame
    //     0x9229f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9229fc: mov             fp, SP
    // 0x922a00: AllocStack(0x18)
    //     0x922a00: sub             SP, SP, #0x18
    // 0x922a04: CheckStackOverflow
    //     0x922a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922a08: cmp             SP, x16
    //     0x922a0c: b.ls            #0x922a64
    // 0x922a10: r1 = Function '<anonymous closure>':.
    //     0x922a10: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc18] AnonymousClosure: (0x922a6c), in [package:nuonline/app/data/repositories/juz_repository.dart] JuzRepository::init (0x922828)
    //     0x922a14: ldr             x1, [x1, #0xc18]
    // 0x922a18: r2 = Null
    //     0x922a18: mov             x2, NULL
    // 0x922a1c: r0 = AllocateClosure()
    //     0x922a1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x922a20: mov             x1, x0
    // 0x922a24: ldr             x0, [fp, #0x10]
    // 0x922a28: r2 = LoadClassIdInstr(r0)
    //     0x922a28: ldur            x2, [x0, #-1]
    //     0x922a2c: ubfx            x2, x2, #0xc, #0x14
    // 0x922a30: r16 = <Juz>
    //     0x922a30: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e8] TypeArguments: <Juz>
    //     0x922a34: ldr             x16, [x16, #0x1e8]
    // 0x922a38: stp             x0, x16, [SP, #8]
    // 0x922a3c: str             x1, [SP]
    // 0x922a40: mov             x0, x2
    // 0x922a44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x922a44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x922a48: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x922a48: movz            x17, #0xf28c
    //     0x922a4c: add             lr, x0, x17
    //     0x922a50: ldr             lr, [x21, lr, lsl #3]
    //     0x922a54: blr             lr
    // 0x922a58: LeaveFrame
    //     0x922a58: mov             SP, fp
    //     0x922a5c: ldp             fp, lr, [SP], #0x10
    // 0x922a60: ret
    //     0x922a60: ret             
    // 0x922a64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922a64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922a68: b               #0x922a10
  }
  [closure] Juz <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x922a6c, size: 0x50
    // 0x922a6c: EnterFrame
    //     0x922a6c: stp             fp, lr, [SP, #-0x10]!
    //     0x922a70: mov             fp, SP
    // 0x922a74: CheckStackOverflow
    //     0x922a74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922a78: cmp             SP, x16
    //     0x922a7c: b.ls            #0x922ab4
    // 0x922a80: ldr             x0, [fp, #0x10]
    // 0x922a84: r2 = Null
    //     0x922a84: mov             x2, NULL
    // 0x922a88: r1 = Null
    //     0x922a88: mov             x1, NULL
    // 0x922a8c: r8 = Map<String, dynamic>
    //     0x922a8c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x922a90: r3 = Null
    //     0x922a90: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3dc20] Null
    //     0x922a94: ldr             x3, [x3, #0xc20]
    // 0x922a98: r0 = Map<String, dynamic>()
    //     0x922a98: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x922a9c: ldr             x2, [fp, #0x10]
    // 0x922aa0: r1 = Null
    //     0x922aa0: mov             x1, NULL
    // 0x922aa4: r0 = Juz.fromMap()
    //     0x922aa4: bl              #0x922abc  ; [package:nuonline/app/data/models/juz.dart] Juz::Juz.fromMap
    // 0x922aa8: LeaveFrame
    //     0x922aa8: mov             SP, fp
    //     0x922aac: ldp             fp, lr, [SP], #0x10
    // 0x922ab0: ret
    //     0x922ab0: ret             
    // 0x922ab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922ab4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922ab8: b               #0x922a80
  }
}
