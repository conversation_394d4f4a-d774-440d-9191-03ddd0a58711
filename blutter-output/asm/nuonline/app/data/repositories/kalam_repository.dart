// lib: , url: package:nuonline/app/data/repositories/kalam_repository.dart

// class id: 1050087, size: 0x8
class :: {
}

// class id: 1089, size: 0xc, field offset: 0x8
class KalamRepository extends Object {

  _ findAll(/* No info */) async {
    // ** addr: 0x8fdb94, size: 0xf0
    // 0x8fdb94: EnterFrame
    //     0x8fdb94: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdb98: mov             fp, SP
    // 0x8fdb9c: AllocStack(0x88)
    //     0x8fdb9c: sub             SP, SP, #0x88
    // 0x8fdba0: SetupParameters(KalamRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x8fdba0: stur            NULL, [fp, #-8]
    //     0x8fdba4: stur            x1, [fp, #-0x60]
    //     0x8fdba8: stur            x2, [fp, #-0x68]
    // 0x8fdbac: CheckStackOverflow
    //     0x8fdbac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fdbb0: cmp             SP, x16
    //     0x8fdbb4: b.ls            #0x8fdc7c
    // 0x8fdbb8: InitAsync() -> Future<ApiResult<List<Kalam>>>
    //     0x8fdbb8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d448] TypeArguments: <ApiResult<List<Kalam>>>
    //     0x8fdbbc: ldr             x0, [x0, #0x448]
    //     0x8fdbc0: bl              #0x661298  ; InitAsyncStub
    // 0x8fdbc4: ldur            x0, [fp, #-0x60]
    // 0x8fdbc8: LoadField: r1 = r0->field_7
    //     0x8fdbc8: ldur            w1, [x0, #7]
    // 0x8fdbcc: DecompressPointer r1
    //     0x8fdbcc: add             x1, x1, HEAP, lsl #32
    // 0x8fdbd0: stp             x1, NULL, [SP, #0x10]
    // 0x8fdbd4: r16 = "/kalam"
    //     0x8fdbd4: add             x16, PP, #0xf, lsl #12  ; [pp+0xfd80] "/kalam"
    //     0x8fdbd8: ldr             x16, [x16, #0xd80]
    // 0x8fdbdc: ldur            lr, [fp, #-0x68]
    // 0x8fdbe0: stp             lr, x16, [SP]
    // 0x8fdbe4: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8fdbe4: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8fdbe8: ldr             x4, [x4, #0x2f0]
    // 0x8fdbec: r0 = get()
    //     0x8fdbec: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8fdbf0: mov             x1, x0
    // 0x8fdbf4: stur            x1, [fp, #-0x60]
    // 0x8fdbf8: r0 = Await()
    //     0x8fdbf8: bl              #0x661044  ; AwaitStub
    // 0x8fdbfc: stur            x0, [fp, #-0x60]
    // 0x8fdc00: LoadField: r1 = r0->field_b
    //     0x8fdc00: ldur            w1, [x0, #0xb]
    // 0x8fdc04: DecompressPointer r1
    //     0x8fdc04: add             x1, x1, HEAP, lsl #32
    // 0x8fdc08: r0 = fromResponse()
    //     0x8fdc08: bl              #0x8fdc84  ; [package:nuonline/app/data/models/kalam.dart] Kalam::fromResponse
    // 0x8fdc0c: mov             x3, x0
    // 0x8fdc10: ldur            x0, [fp, #-0x60]
    // 0x8fdc14: stur            x3, [fp, #-0x68]
    // 0x8fdc18: LoadField: r2 = r0->field_1b
    //     0x8fdc18: ldur            w2, [x0, #0x1b]
    // 0x8fdc1c: DecompressPointer r2
    //     0x8fdc1c: add             x2, x2, HEAP, lsl #32
    // 0x8fdc20: r1 = Null
    //     0x8fdc20: mov             x1, NULL
    // 0x8fdc24: r0 = Pagination.fromHeaders()
    //     0x8fdc24: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x8fdc28: r1 = <List<Kalam>>
    //     0x8fdc28: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d450] TypeArguments: <List<Kalam>>
    //     0x8fdc2c: ldr             x1, [x1, #0x450]
    // 0x8fdc30: stur            x0, [fp, #-0x60]
    // 0x8fdc34: r0 = _$SuccessImpl()
    //     0x8fdc34: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8fdc38: mov             x1, x0
    // 0x8fdc3c: ldur            x0, [fp, #-0x68]
    // 0x8fdc40: StoreField: r1->field_b = r0
    //     0x8fdc40: stur            w0, [x1, #0xb]
    // 0x8fdc44: ldur            x0, [fp, #-0x60]
    // 0x8fdc48: StoreField: r1->field_f = r0
    //     0x8fdc48: stur            w0, [x1, #0xf]
    // 0x8fdc4c: mov             x0, x1
    // 0x8fdc50: r0 = ReturnAsyncNotFuture()
    //     0x8fdc50: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fdc54: sub             SP, fp, #0x88
    // 0x8fdc58: mov             x1, x0
    // 0x8fdc5c: r0 = getDioException()
    //     0x8fdc5c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fdc60: r1 = <List<Kalam>>
    //     0x8fdc60: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d450] TypeArguments: <List<Kalam>>
    //     0x8fdc64: ldr             x1, [x1, #0x450]
    // 0x8fdc68: stur            x0, [fp, #-0x60]
    // 0x8fdc6c: r0 = _$FailureImpl()
    //     0x8fdc6c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8fdc70: ldur            x1, [fp, #-0x60]
    // 0x8fdc74: StoreField: r0->field_b = r1
    //     0x8fdc74: stur            w1, [x0, #0xb]
    // 0x8fdc78: r0 = ReturnAsyncNotFuture()
    //     0x8fdc78: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fdc7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fdc7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fdc80: b               #0x8fdbb8
  }
  _ findAllCategories(/* No info */) async {
    // ** addr: 0x92037c, size: 0xbc
    // 0x92037c: EnterFrame
    //     0x92037c: stp             fp, lr, [SP, #-0x10]!
    //     0x920380: mov             fp, SP
    // 0x920384: AllocStack(0x68)
    //     0x920384: sub             SP, SP, #0x68
    // 0x920388: SetupParameters(KalamRepository this /* r1 => r1, fp-0x50 */)
    //     0x920388: stur            NULL, [fp, #-8]
    //     0x92038c: stur            x1, [fp, #-0x50]
    // 0x920390: CheckStackOverflow
    //     0x920390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920394: cmp             SP, x16
    //     0x920398: b.ls            #0x920430
    // 0x92039c: InitAsync() -> Future<ApiResult<List<KalamCategory>>>
    //     0x92039c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f1c0] TypeArguments: <ApiResult<List<KalamCategory>>>
    //     0x9203a0: ldr             x0, [x0, #0x1c0]
    //     0x9203a4: bl              #0x661298  ; InitAsyncStub
    // 0x9203a8: ldur            x0, [fp, #-0x50]
    // 0x9203ac: LoadField: r1 = r0->field_7
    //     0x9203ac: ldur            w1, [x0, #7]
    // 0x9203b0: DecompressPointer r1
    //     0x9203b0: add             x1, x1, HEAP, lsl #32
    // 0x9203b4: stp             x1, NULL, [SP, #8]
    // 0x9203b8: r16 = "/kalam/categories"
    //     0x9203b8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f1c8] "/kalam/categories"
    //     0x9203bc: ldr             x16, [x16, #0x1c8]
    // 0x9203c0: str             x16, [SP]
    // 0x9203c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9203c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9203c8: r0 = get()
    //     0x9203c8: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x9203cc: mov             x1, x0
    // 0x9203d0: stur            x1, [fp, #-0x50]
    // 0x9203d4: r0 = Await()
    //     0x9203d4: bl              #0x661044  ; AwaitStub
    // 0x9203d8: LoadField: r1 = r0->field_b
    //     0x9203d8: ldur            w1, [x0, #0xb]
    // 0x9203dc: DecompressPointer r1
    //     0x9203dc: add             x1, x1, HEAP, lsl #32
    // 0x9203e0: r0 = fromResponse()
    //     0x9203e0: bl              #0x920438  ; [package:nuonline/app/data/models/kalam.dart] KalamCategory::fromResponse
    // 0x9203e4: r1 = <List<KalamCategory>>
    //     0x9203e4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f1d0] TypeArguments: <List<KalamCategory>>
    //     0x9203e8: ldr             x1, [x1, #0x1d0]
    // 0x9203ec: stur            x0, [fp, #-0x50]
    // 0x9203f0: r0 = _$SuccessImpl()
    //     0x9203f0: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x9203f4: mov             x1, x0
    // 0x9203f8: ldur            x0, [fp, #-0x50]
    // 0x9203fc: StoreField: r1->field_b = r0
    //     0x9203fc: stur            w0, [x1, #0xb]
    // 0x920400: mov             x0, x1
    // 0x920404: r0 = ReturnAsyncNotFuture()
    //     0x920404: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x920408: sub             SP, fp, #0x68
    // 0x92040c: mov             x1, x0
    // 0x920410: r0 = getDioException()
    //     0x920410: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x920414: r1 = <List<KalamCategory>>
    //     0x920414: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f1d0] TypeArguments: <List<KalamCategory>>
    //     0x920418: ldr             x1, [x1, #0x1d0]
    // 0x92041c: stur            x0, [fp, #-0x50]
    // 0x920420: r0 = _$FailureImpl()
    //     0x920420: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x920424: ldur            x1, [fp, #-0x50]
    // 0x920428: StoreField: r0->field_b = r1
    //     0x920428: stur            w1, [x0, #0xb]
    // 0x92042c: r0 = ReturnAsyncNotFuture()
    //     0x92042c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x920430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920430: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920434: b               #0x92039c
  }
}
