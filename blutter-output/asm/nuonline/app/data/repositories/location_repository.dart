// lib: , url: package:nuonline/app/data/repositories/location_repository.dart

// class id: 1050088, size: 0x8
class :: {
}

// class id: 1088, size: 0x10, field offset: 0x8
class LocationRepository extends Object {

  _ getCurrentLocationStream(/* No info */) {
    // ** addr: 0x8a7d1c, size: 0xe0
    // 0x8a7d1c: EnterFrame
    //     0x8a7d1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7d20: mov             fp, SP
    // 0x8a7d24: AllocStack(0x28)
    //     0x8a7d24: sub             SP, SP, #0x28
    // 0x8a7d28: CheckStackOverflow
    //     0x8a7d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a7d2c: cmp             SP, x16
    //     0x8a7d30: b.ls            #0x8a7df4
    // 0x8a7d34: LoadField: r0 = r1->field_b
    //     0x8a7d34: ldur            w0, [x1, #0xb]
    // 0x8a7d38: DecompressPointer r0
    //     0x8a7d38: add             x0, x0, HEAP, lsl #32
    // 0x8a7d3c: stur            x0, [fp, #-8]
    // 0x8a7d40: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8a7d40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8a7d44: ldr             x0, [x0, #0x2728]
    //     0x8a7d48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8a7d4c: cmp             w0, w16
    //     0x8a7d50: b.ne            #0x8a7d5c
    //     0x8a7d54: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8a7d58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8a7d5c: stp             x0, NULL, [SP, #8]
    // 0x8a7d60: r16 = "v2_location"
    //     0x8a7d60: add             x16, PP, #8, lsl #12  ; [pp+0x80f8] "v2_location"
    //     0x8a7d64: ldr             x16, [x16, #0xf8]
    // 0x8a7d68: str             x16, [SP]
    // 0x8a7d6c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7d6c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7d70: r0 = box()
    //     0x8a7d70: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8a7d74: r16 = "current"
    //     0x8a7d74: add             x16, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0x8a7d78: ldr             x16, [x16, #0x110]
    // 0x8a7d7c: str             x16, [SP]
    // 0x8a7d80: mov             x1, x0
    // 0x8a7d84: r4 = const [0, 0x2, 0x1, 0x1, key, 0x1, null]
    //     0x8a7d84: add             x4, PP, #8, lsl #12  ; [pp+0x8078] List(7) [0, 0x2, 0x1, 0x1, "key", 0x1, Null]
    //     0x8a7d88: ldr             x4, [x4, #0x78]
    // 0x8a7d8c: r0 = watch()
    //     0x8a7d8c: bl              #0x836368  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::watch
    // 0x8a7d90: r1 = Function '<anonymous closure>':.
    //     0x8a7d90: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3cf40] AnonymousClosure: (0x8a7dfc), in [package:nuonline/app/data/repositories/location_repository.dart] LocationRepository::getCurrentLocationStream (0x8a7d1c)
    //     0x8a7d94: ldr             x1, [x1, #0xf40]
    // 0x8a7d98: r2 = Null
    //     0x8a7d98: mov             x2, NULL
    // 0x8a7d9c: stur            x0, [fp, #-0x10]
    // 0x8a7da0: r0 = AllocateClosure()
    //     0x8a7da0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a7da4: r16 = <Location?>
    //     0x8a7da4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32560] TypeArguments: <Location?>
    //     0x8a7da8: ldr             x16, [x16, #0x560]
    // 0x8a7dac: ldur            lr, [fp, #-0x10]
    // 0x8a7db0: stp             lr, x16, [SP, #8]
    // 0x8a7db4: str             x0, [SP]
    // 0x8a7db8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7db8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7dbc: r0 = map()
    //     0x8a7dbc: bl              #0x68d014  ; [dart:async] Stream::map
    // 0x8a7dc0: ldur            x1, [fp, #-8]
    // 0x8a7dc4: stur            x0, [fp, #-8]
    // 0x8a7dc8: r0 = current()
    //     0x8a7dc8: bl              #0x8312cc  ; [package:nuonline/services/storage_service/location_storage.dart] LocationStorage::current
    // 0x8a7dcc: r16 = <Location?>
    //     0x8a7dcc: add             x16, PP, #0x32, lsl #12  ; [pp+0x32560] TypeArguments: <Location?>
    //     0x8a7dd0: ldr             x16, [x16, #0x560]
    // 0x8a7dd4: ldur            lr, [fp, #-8]
    // 0x8a7dd8: stp             lr, x16, [SP, #8]
    // 0x8a7ddc: str             x0, [SP]
    // 0x8a7de0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7de0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7de4: r0 = StartWithExtension.startWith()
    //     0x8a7de4: bl              #0x8a7884  ; [package:rxdart/src/transformers/start_with.dart] ::StartWithExtension.startWith
    // 0x8a7de8: LeaveFrame
    //     0x8a7de8: mov             SP, fp
    //     0x8a7dec: ldp             fp, lr, [SP], #0x10
    // 0x8a7df0: ret
    //     0x8a7df0: ret             
    // 0x8a7df4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a7df4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a7df8: b               #0x8a7d34
  }
  [closure] Location? <anonymous closure>(dynamic, BoxEvent) {
    // ** addr: 0x8a7dfc, size: 0x64
    // 0x8a7dfc: EnterFrame
    //     0x8a7dfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7e00: mov             fp, SP
    // 0x8a7e04: AllocStack(0x8)
    //     0x8a7e04: sub             SP, SP, #8
    // 0x8a7e08: ldr             x0, [fp, #0x10]
    // 0x8a7e0c: LoadField: r3 = r0->field_b
    //     0x8a7e0c: ldur            w3, [x0, #0xb]
    // 0x8a7e10: DecompressPointer r3
    //     0x8a7e10: add             x3, x3, HEAP, lsl #32
    // 0x8a7e14: mov             x0, x3
    // 0x8a7e18: stur            x3, [fp, #-8]
    // 0x8a7e1c: r2 = Null
    //     0x8a7e1c: mov             x2, NULL
    // 0x8a7e20: r1 = Null
    //     0x8a7e20: mov             x1, NULL
    // 0x8a7e24: r4 = 60
    //     0x8a7e24: movz            x4, #0x3c
    // 0x8a7e28: branchIfSmi(r0, 0x8a7e34)
    //     0x8a7e28: tbz             w0, #0, #0x8a7e34
    // 0x8a7e2c: r4 = LoadClassIdInstr(r0)
    //     0x8a7e2c: ldur            x4, [x0, #-1]
    //     0x8a7e30: ubfx            x4, x4, #0xc, #0x14
    // 0x8a7e34: cmp             x4, #0x646
    // 0x8a7e38: b.eq            #0x8a7e50
    // 0x8a7e3c: r8 = Location?
    //     0x8a7e3c: add             x8, PP, #0xb, lsl #12  ; [pp+0xba30] Type: Location?
    //     0x8a7e40: ldr             x8, [x8, #0xa30]
    // 0x8a7e44: r3 = Null
    //     0x8a7e44: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cf48] Null
    //     0x8a7e48: ldr             x3, [x3, #0xf48]
    // 0x8a7e4c: r0 = Location?()
    //     0x8a7e4c: bl              #0x8149cc  ; IsType_Location?_Stub
    // 0x8a7e50: ldur            x0, [fp, #-8]
    // 0x8a7e54: LeaveFrame
    //     0x8a7e54: mov             SP, fp
    //     0x8a7e58: ldp             fp, lr, [SP], #0x10
    // 0x8a7e5c: ret
    //     0x8a7e5c: ret             
  }
  _ districtFindAll(/* No info */) async {
    // ** addr: 0x8fe338, size: 0x114
    // 0x8fe338: EnterFrame
    //     0x8fe338: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe33c: mov             fp, SP
    // 0x8fe340: AllocStack(0x88)
    //     0x8fe340: sub             SP, SP, #0x88
    // 0x8fe344: SetupParameters(LocationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x8fe344: stur            NULL, [fp, #-8]
    //     0x8fe348: stur            x1, [fp, #-0x58]
    //     0x8fe34c: stur            x2, [fp, #-0x60]
    // 0x8fe350: CheckStackOverflow
    //     0x8fe350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fe354: cmp             SP, x16
    //     0x8fe358: b.ls            #0x8fe444
    // 0x8fe35c: InitAsync() -> Future<ApiResult<List<District>>>
    //     0x8fe35c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f0b0] TypeArguments: <ApiResult<List<District>>>
    //     0x8fe360: ldr             x0, [x0, #0xb0]
    //     0x8fe364: bl              #0x661298  ; InitAsyncStub
    // 0x8fe368: ldur            x1, [fp, #-0x58]
    // 0x8fe36c: ldur            x0, [fp, #-0x60]
    // 0x8fe370: LoadField: r3 = r1->field_7
    //     0x8fe370: ldur            w3, [x1, #7]
    // 0x8fe374: DecompressPointer r3
    //     0x8fe374: add             x3, x3, HEAP, lsl #32
    // 0x8fe378: stur            x3, [fp, #-0x68]
    // 0x8fe37c: r1 = Null
    //     0x8fe37c: mov             x1, NULL
    // 0x8fe380: r2 = 4
    //     0x8fe380: movz            x2, #0x4
    // 0x8fe384: r0 = AllocateArray()
    //     0x8fe384: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8fe388: mov             x2, x0
    // 0x8fe38c: r16 = "regency_id"
    //     0x8fe38c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0x8fe390: ldr             x16, [x16, #0x118]
    // 0x8fe394: StoreField: r2->field_f = r16
    //     0x8fe394: stur            w16, [x2, #0xf]
    // 0x8fe398: ldur            x3, [fp, #-0x60]
    // 0x8fe39c: r0 = BoxInt64Instr(r3)
    //     0x8fe39c: sbfiz           x0, x3, #1, #0x1f
    //     0x8fe3a0: cmp             x3, x0, asr #1
    //     0x8fe3a4: b.eq            #0x8fe3b0
    //     0x8fe3a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fe3ac: stur            x3, [x0, #7]
    // 0x8fe3b0: StoreField: r2->field_13 = r0
    //     0x8fe3b0: stur            w0, [x2, #0x13]
    // 0x8fe3b4: r16 = <String, dynamic>
    //     0x8fe3b4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8fe3b8: stp             x2, x16, [SP]
    // 0x8fe3bc: r0 = Map._fromLiteral()
    //     0x8fe3bc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8fe3c0: ldur            x16, [fp, #-0x68]
    // 0x8fe3c4: stp             x16, NULL, [SP, #0x10]
    // 0x8fe3c8: r16 = "/districts"
    //     0x8fe3c8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f0b8] "/districts"
    //     0x8fe3cc: ldr             x16, [x16, #0xb8]
    // 0x8fe3d0: stp             x0, x16, [SP]
    // 0x8fe3d4: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x8fe3d4: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x8fe3d8: ldr             x4, [x4, #0x2f0]
    // 0x8fe3dc: r0 = get()
    //     0x8fe3dc: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8fe3e0: mov             x1, x0
    // 0x8fe3e4: stur            x1, [fp, #-0x58]
    // 0x8fe3e8: r0 = Await()
    //     0x8fe3e8: bl              #0x661044  ; AwaitStub
    // 0x8fe3ec: LoadField: r1 = r0->field_b
    //     0x8fe3ec: ldur            w1, [x0, #0xb]
    // 0x8fe3f0: DecompressPointer r1
    //     0x8fe3f0: add             x1, x1, HEAP, lsl #32
    // 0x8fe3f4: r0 = fromResponse()
    //     0x8fe3f4: bl              #0x8fe44c  ; [package:nuonline/app/data/models/district.dart] District::fromResponse
    // 0x8fe3f8: r1 = <List<District>>
    //     0x8fe3f8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f0c0] TypeArguments: <List<District>>
    //     0x8fe3fc: ldr             x1, [x1, #0xc0]
    // 0x8fe400: stur            x0, [fp, #-0x58]
    // 0x8fe404: r0 = _$SuccessImpl()
    //     0x8fe404: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8fe408: mov             x1, x0
    // 0x8fe40c: ldur            x0, [fp, #-0x58]
    // 0x8fe410: StoreField: r1->field_b = r0
    //     0x8fe410: stur            w0, [x1, #0xb]
    // 0x8fe414: mov             x0, x1
    // 0x8fe418: r0 = ReturnAsyncNotFuture()
    //     0x8fe418: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fe41c: sub             SP, fp, #0x88
    // 0x8fe420: mov             x1, x0
    // 0x8fe424: r0 = getDioException()
    //     0x8fe424: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fe428: r1 = <List<District>>
    //     0x8fe428: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f0c0] TypeArguments: <List<District>>
    //     0x8fe42c: ldr             x1, [x1, #0xc0]
    // 0x8fe430: stur            x0, [fp, #-0x58]
    // 0x8fe434: r0 = _$FailureImpl()
    //     0x8fe434: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8fe438: ldur            x1, [fp, #-0x58]
    // 0x8fe43c: StoreField: r0->field_b = r1
    //     0x8fe43c: stur            w1, [x0, #0xb]
    // 0x8fe440: r0 = ReturnAsyncNotFuture()
    //     0x8fe440: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fe444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fe444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fe448: b               #0x8fe35c
  }
  _ provinceFindAll(/* No info */) async {
    // ** addr: 0x8fea40, size: 0xbc
    // 0x8fea40: EnterFrame
    //     0x8fea40: stp             fp, lr, [SP, #-0x10]!
    //     0x8fea44: mov             fp, SP
    // 0x8fea48: AllocStack(0x68)
    //     0x8fea48: sub             SP, SP, #0x68
    // 0x8fea4c: SetupParameters(LocationRepository this /* r1 => r1, fp-0x50 */)
    //     0x8fea4c: stur            NULL, [fp, #-8]
    //     0x8fea50: stur            x1, [fp, #-0x50]
    // 0x8fea54: CheckStackOverflow
    //     0x8fea54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fea58: cmp             SP, x16
    //     0x8fea5c: b.ls            #0x8feaf4
    // 0x8fea60: InitAsync() -> Future<ApiResult<List<Province>>>
    //     0x8fea60: add             x0, PP, #0x40, lsl #12  ; [pp+0x40418] TypeArguments: <ApiResult<List<Province>>>
    //     0x8fea64: ldr             x0, [x0, #0x418]
    //     0x8fea68: bl              #0x661298  ; InitAsyncStub
    // 0x8fea6c: ldur            x0, [fp, #-0x50]
    // 0x8fea70: LoadField: r1 = r0->field_7
    //     0x8fea70: ldur            w1, [x0, #7]
    // 0x8fea74: DecompressPointer r1
    //     0x8fea74: add             x1, x1, HEAP, lsl #32
    // 0x8fea78: stp             x1, NULL, [SP, #8]
    // 0x8fea7c: r16 = "/provinces"
    //     0x8fea7c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40420] "/provinces"
    //     0x8fea80: ldr             x16, [x16, #0x420]
    // 0x8fea84: str             x16, [SP]
    // 0x8fea88: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fea88: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fea8c: r0 = get()
    //     0x8fea8c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8fea90: mov             x1, x0
    // 0x8fea94: stur            x1, [fp, #-0x50]
    // 0x8fea98: r0 = Await()
    //     0x8fea98: bl              #0x661044  ; AwaitStub
    // 0x8fea9c: LoadField: r1 = r0->field_b
    //     0x8fea9c: ldur            w1, [x0, #0xb]
    // 0x8feaa0: DecompressPointer r1
    //     0x8feaa0: add             x1, x1, HEAP, lsl #32
    // 0x8feaa4: r0 = fromResponse()
    //     0x8feaa4: bl              #0x8feafc  ; [package:nuonline/app/data/models/province.dart] Province::fromResponse
    // 0x8feaa8: r1 = <List<Province>>
    //     0x8feaa8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40428] TypeArguments: <List<Province>>
    //     0x8feaac: ldr             x1, [x1, #0x428]
    // 0x8feab0: stur            x0, [fp, #-0x50]
    // 0x8feab4: r0 = _$SuccessImpl()
    //     0x8feab4: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8feab8: mov             x1, x0
    // 0x8feabc: ldur            x0, [fp, #-0x50]
    // 0x8feac0: StoreField: r1->field_b = r0
    //     0x8feac0: stur            w0, [x1, #0xb]
    // 0x8feac4: mov             x0, x1
    // 0x8feac8: r0 = ReturnAsyncNotFuture()
    //     0x8feac8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8feacc: sub             SP, fp, #0x68
    // 0x8fead0: mov             x1, x0
    // 0x8fead4: r0 = getDioException()
    //     0x8fead4: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fead8: r1 = <List<Province>>
    //     0x8fead8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40428] TypeArguments: <List<Province>>
    //     0x8feadc: ldr             x1, [x1, #0x428]
    // 0x8feae0: stur            x0, [fp, #-0x50]
    // 0x8feae4: r0 = _$FailureImpl()
    //     0x8feae4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8feae8: ldur            x1, [fp, #-0x50]
    // 0x8feaec: StoreField: r0->field_b = r1
    //     0x8feaec: stur            w1, [x0, #0xb]
    // 0x8feaf0: r0 = ReturnAsyncNotFuture()
    //     0x8feaf0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8feaf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8feaf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8feaf8: b               #0x8fea60
  }
  get _ defaultLocation(/* No info */) {
    // ** addr: 0x8ff808, size: 0x80
    // 0x8ff808: EnterFrame
    //     0x8ff808: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff80c: mov             fp, SP
    // 0x8ff810: AllocStack(0x8)
    //     0x8ff810: sub             SP, SP, #8
    // 0x8ff814: CheckStackOverflow
    //     0x8ff814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff818: cmp             SP, x16
    //     0x8ff81c: b.ls            #0x8ff880
    // 0x8ff820: r0 = Location()
    //     0x8ff820: bl              #0x8ffb64  ; AllocateLocationStub -> Location (size=0x48)
    // 0x8ff824: mov             x1, x0
    // 0x8ff828: r2 = "ID"
    //     0x8ff828: add             x2, PP, #8, lsl #12  ; [pp+0x8f40] "ID"
    //     0x8ff82c: ldr             x2, [x2, #0xf40]
    // 0x8ff830: r3 = "Indonesia"
    //     0x8ff830: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cf60] "Indonesia"
    //     0x8ff834: ldr             x3, [x3, #0xf60]
    // 0x8ff838: d0 = -6.200080
    //     0x8ff838: add             x17, PP, #0x37, lsl #12  ; [pp+0x37d88] IMM: double(-6.20008) from 0xc018cce1c58255b0
    //     0x8ff83c: ldr             d0, [x17, #0xd88]
    // 0x8ff840: d1 = 106.832650
    //     0x8ff840: add             x17, PP, #0x37, lsl #12  ; [pp+0x37d90] IMM: double(106.83265) from 0x405ab54a2339c0ec
    //     0x8ff844: ldr             d1, [x17, #0xd90]
    // 0x8ff848: r5 = "DKI Jakarta"
    //     0x8ff848: add             x5, PP, #0x37, lsl #12  ; [pp+0x37d98] "DKI Jakarta"
    //     0x8ff84c: ldr             x5, [x5, #0xd98]
    // 0x8ff850: r6 = "Kota Jakarta Pusat"
    //     0x8ff850: add             x6, PP, #0x37, lsl #12  ; [pp+0x37da0] "Kota Jakarta Pusat"
    //     0x8ff854: ldr             x6, [x6, #0xda0]
    // 0x8ff858: r7 = "WIB"
    //     0x8ff858: add             x7, PP, #0x2c, lsl #12  ; [pp+0x2cf38] "WIB"
    //     0x8ff85c: ldr             x7, [x7, #0xf38]
    // 0x8ff860: stur            x0, [fp, #-8]
    // 0x8ff864: r4 = const [0, 0x8, 0, 0x8, null]
    //     0x8ff864: add             x4, PP, #0x37, lsl #12  ; [pp+0x37da8] List(5) [0, 0x8, 0, 0x8, Null]
    //     0x8ff868: ldr             x4, [x4, #0xda8]
    // 0x8ff86c: r0 = Location()
    //     0x8ff86c: bl              #0x8ff888  ; [package:nuonline/app/data/models/location.dart] Location::Location
    // 0x8ff870: ldur            x0, [fp, #-8]
    // 0x8ff874: LeaveFrame
    //     0x8ff874: mov             SP, fp
    //     0x8ff878: ldp             fp, lr, [SP], #0x10
    // 0x8ff87c: ret
    //     0x8ff87c: ret             
    // 0x8ff880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff884: b               #0x8ff820
  }
  _ getCurrentLocation(/* No info */) {
    // ** addr: 0x900820, size: 0x38
    // 0x900820: EnterFrame
    //     0x900820: stp             fp, lr, [SP, #-0x10]!
    //     0x900824: mov             fp, SP
    // 0x900828: CheckStackOverflow
    //     0x900828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90082c: cmp             SP, x16
    //     0x900830: b.ls            #0x900850
    // 0x900834: LoadField: r0 = r1->field_b
    //     0x900834: ldur            w0, [x1, #0xb]
    // 0x900838: DecompressPointer r0
    //     0x900838: add             x0, x0, HEAP, lsl #32
    // 0x90083c: mov             x1, x0
    // 0x900840: r0 = current()
    //     0x900840: bl              #0x8312cc  ; [package:nuonline/services/storage_service/location_storage.dart] LocationStorage::current
    // 0x900844: LeaveFrame
    //     0x900844: mov             SP, fp
    //     0x900848: ldp             fp, lr, [SP], #0x10
    // 0x90084c: ret
    //     0x90084c: ret             
    // 0x900850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x900854: b               #0x900834
  }
  _ getCurrentLocationFromGPS(/* No info */) async {
    // ** addr: 0x919128, size: 0x3a0
    // 0x919128: EnterFrame
    //     0x919128: stp             fp, lr, [SP, #-0x10]!
    //     0x91912c: mov             fp, SP
    // 0x919130: AllocStack(0x118)
    //     0x919130: sub             SP, SP, #0x118
    // 0x919134: SetupParameters(LocationRepository this /* r1 => r1, fp-0xc8 */, {dynamic onLoading = Null /* r2, fp-0xc0 */})
    //     0x919134: stur            NULL, [fp, #-8]
    //     0x919138: stur            x1, [fp, #-0xc8]
    //     0x91913c: stur            x4, [fp, #-0xd0]
    //     0x919140: ldur            w0, [x4, #0x13]
    //     0x919144: ldur            w2, [x4, #0x1f]
    //     0x919148: add             x2, x2, HEAP, lsl #32
    //     0x91914c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ec8] "onLoading"
    //     0x919150: ldr             x16, [x16, #0xec8]
    //     0x919154: cmp             w2, w16
    //     0x919158: b.ne            #0x919178
    //     0x91915c: ldur            w2, [x4, #0x23]
    //     0x919160: add             x2, x2, HEAP, lsl #32
    //     0x919164: sub             w3, w0, w2
    //     0x919168: add             x0, fp, w3, sxtw #2
    //     0x91916c: ldr             x0, [x0, #8]
    //     0x919170: mov             x2, x0
    //     0x919174: b               #0x91917c
    //     0x919178: mov             x2, NULL
    //     0x91917c: stur            x2, [fp, #-0xc0]
    // 0x919180: CheckStackOverflow
    //     0x919180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x919184: cmp             SP, x16
    //     0x919188: b.ls            #0x9194a4
    // 0x91918c: InitAsync() -> Future<Either<LocationError, Location>>
    //     0x91918c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c3d0] TypeArguments: <Either<LocationError, Location>>
    //     0x919190: ldr             x0, [x0, #0x3d0]
    //     0x919194: bl              #0x661298  ; InitAsyncStub
    // 0x919198: r0 = isLocationServiceEnabled()
    //     0x919198: bl              #0x91a64c  ; [package:geolocator/geolocator.dart] Geolocator::isLocationServiceEnabled
    // 0x91919c: mov             x1, x0
    // 0x9191a0: stur            x1, [fp, #-0xd8]
    // 0x9191a4: r0 = Await()
    //     0x9191a4: bl              #0x661044  ; AwaitStub
    // 0x9191a8: stur            x0, [fp, #-0xd8]
    // 0x9191ac: r16 = true
    //     0x9191ac: add             x16, NULL, #0x20  ; true
    // 0x9191b0: cmp             w0, w16
    // 0x9191b4: b.eq            #0x9191c4
    // 0x9191b8: r0 = Instance_Left
    //     0x9191b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c3d8] Obj!Left<LocationError, Location>@e25ce1
    //     0x9191bc: ldr             x0, [x0, #0x3d8]
    // 0x9191c0: r0 = ReturnAsyncNotFuture()
    //     0x9191c0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9191c4: r0 = checkPermission()
    //     0x9191c4: bl              #0x900400  ; [package:geolocator/geolocator.dart] Geolocator::checkPermission
    // 0x9191c8: mov             x1, x0
    // 0x9191cc: stur            x1, [fp, #-0xe0]
    // 0x9191d0: r0 = Await()
    //     0x9191d0: bl              #0x661044  ; AwaitStub
    // 0x9191d4: r16 = Instance_LocationPermission
    //     0x9191d4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c3e0] Obj!LocationPermission@e32ae1
    //     0x9191d8: ldr             x16, [x16, #0x3e0]
    // 0x9191dc: cmp             w0, w16
    // 0x9191e0: b.ne            #0x9191f0
    // 0x9191e4: r0 = Instance_Left
    //     0x9191e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c3d8] Obj!Left<LocationError, Location>@e25ce1
    //     0x9191e8: ldr             x0, [x0, #0x3d8]
    // 0x9191ec: r0 = ReturnAsyncNotFuture()
    //     0x9191ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9191f0: r16 = Instance_LocationPermission
    //     0x9191f0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c3e8] Obj!LocationPermission@e32ac1
    //     0x9191f4: ldr             x16, [x16, #0x3e8]
    // 0x9191f8: cmp             w0, w16
    // 0x9191fc: b.ne            #0x91920c
    // 0x919200: r0 = Instance_Left
    //     0x919200: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c3f0] Obj!Left<LocationError, Location>@e25cd1
    //     0x919204: ldr             x0, [x0, #0x3f0]
    // 0x919208: r0 = ReturnAsyncNotFuture()
    //     0x919208: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91920c: ldur            x0, [fp, #-0xc0]
    // 0x919210: cmp             w0, NULL
    // 0x919214: b.eq            #0x919224
    // 0x919218: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x919218: ldur            w1, [x0, #0x17]
    // 0x91921c: DecompressPointer r1
    //     0x91921c: add             x1, x1, HEAP, lsl #32
    // 0x919220: r0 = show()
    //     0x919220: bl              #0x91a118  ; [package:nuikit/src/widgets/loading/loading.dart] NLoadingDialog::show
    // 0x919224: r16 = Instance_Duration
    //     0x919224: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c3f8] Obj!Duration@e3a1c1
    //     0x919228: ldr             x16, [x16, #0x3f8]
    // 0x91922c: str             x16, [SP]
    // 0x919230: r4 = const [0, 0x1, 0x1, 0, timeLimit, 0, null]
    //     0x919230: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c400] List(7) [0, 0x1, 0x1, 0, "timeLimit", 0, Null]
    //     0x919234: ldr             x4, [x4, #0x400]
    // 0x919238: r0 = getCurrentPosition()
    //     0x919238: bl              #0x900128  ; [package:geolocator/geolocator.dart] Geolocator::getCurrentPosition
    // 0x91923c: mov             x1, x0
    // 0x919240: stur            x1, [fp, #-0xc0]
    // 0x919244: r0 = Await()
    //     0x919244: bl              #0x661044  ; AwaitStub
    // 0x919248: stur            x0, [fp, #-0xc0]
    // 0x91924c: LoadField: d2 = r0->field_7
    //     0x91924c: ldur            d2, [x0, #7]
    // 0x919250: stur            d2, [fp, #-0x100]
    // 0x919254: LoadField: d3 = r0->field_f
    //     0x919254: ldur            d3, [x0, #0xf]
    // 0x919258: mov             v0.16b, v2.16b
    // 0x91925c: mov             v1.16b, v3.16b
    // 0x919260: stur            d3, [fp, #-0xf8]
    // 0x919264: r0 = placemarkFromCoordinates()
    //     0x919264: bl              #0x919628  ; [package:geocoding/geocoding.dart] ::placemarkFromCoordinates
    // 0x919268: mov             x1, x0
    // 0x91926c: stur            x1, [fp, #-0xc8]
    // 0x919270: r0 = Await()
    //     0x919270: bl              #0x661044  ; AwaitStub
    // 0x919274: mov             x2, x0
    // 0x919278: stur            x2, [fp, #-0xc8]
    // 0x91927c: r0 = LoadClassIdInstr(r2)
    //     0x91927c: ldur            x0, [x2, #-1]
    //     0x919280: ubfx            x0, x0, #0xc, #0x14
    // 0x919284: mov             x1, x2
    // 0x919288: r0 = GDT[cid_x0 + 0xe879]()
    //     0x919288: movz            x17, #0xe879
    //     0x91928c: add             lr, x0, x17
    //     0x919290: ldr             lr, [x21, lr, lsl #3]
    //     0x919294: blr             lr
    // 0x919298: tbnz            w0, #4, #0x9192a8
    // 0x91929c: r0 = Instance_Left
    //     0x91929c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c408] Obj!Left<LocationError, Location>@e25cc1
    //     0x9192a0: ldr             x0, [x0, #0x408]
    // 0x9192a4: r0 = ReturnAsyncNotFuture()
    //     0x9192a4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9192a8: ldur            x1, [fp, #-0xc8]
    // 0x9192ac: ldur            x2, [fp, #-0xc0]
    // 0x9192b0: r0 = LoadClassIdInstr(r1)
    //     0x9192b0: ldur            x0, [x1, #-1]
    //     0x9192b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9192b8: stp             xzr, x1, [SP]
    // 0x9192bc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9192bc: movz            x17, #0x3037
    //     0x9192c0: movk            x17, #0x1, lsl #16
    //     0x9192c4: add             lr, x0, x17
    //     0x9192c8: ldr             lr, [x21, lr, lsl #3]
    //     0x9192cc: blr             lr
    // 0x9192d0: LoadField: r2 = r0->field_f
    //     0x9192d0: ldur            w2, [x0, #0xf]
    // 0x9192d4: DecompressPointer r2
    //     0x9192d4: add             x2, x2, HEAP, lsl #32
    // 0x9192d8: ldur            x1, [fp, #-0xc8]
    // 0x9192dc: stur            x2, [fp, #-0xd0]
    // 0x9192e0: r0 = LoadClassIdInstr(r1)
    //     0x9192e0: ldur            x0, [x1, #-1]
    //     0x9192e4: ubfx            x0, x0, #0xc, #0x14
    // 0x9192e8: stp             xzr, x1, [SP]
    // 0x9192ec: r0 = GDT[cid_x0 + 0x13037]()
    //     0x9192ec: movz            x17, #0x3037
    //     0x9192f0: movk            x17, #0x1, lsl #16
    //     0x9192f4: add             lr, x0, x17
    //     0x9192f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9192fc: blr             lr
    // 0x919300: LoadField: r3 = r0->field_13
    //     0x919300: ldur            w3, [x0, #0x13]
    // 0x919304: DecompressPointer r3
    //     0x919304: add             x3, x3, HEAP, lsl #32
    // 0x919308: ldur            x1, [fp, #-0xc8]
    // 0x91930c: stur            x3, [fp, #-0xd8]
    // 0x919310: r0 = LoadClassIdInstr(r1)
    //     0x919310: ldur            x0, [x1, #-1]
    //     0x919314: ubfx            x0, x0, #0xc, #0x14
    // 0x919318: stp             xzr, x1, [SP]
    // 0x91931c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x91931c: movz            x17, #0x3037
    //     0x919320: movk            x17, #0x1, lsl #16
    //     0x919324: add             lr, x0, x17
    //     0x919328: ldr             lr, [x21, lr, lsl #3]
    //     0x91932c: blr             lr
    // 0x919330: LoadField: r5 = r0->field_23
    //     0x919330: ldur            w5, [x0, #0x23]
    // 0x919334: DecompressPointer r5
    //     0x919334: add             x5, x5, HEAP, lsl #32
    // 0x919338: ldur            x0, [fp, #-0xc8]
    // 0x91933c: stur            x5, [fp, #-0xe0]
    // 0x919340: r1 = LoadClassIdInstr(r0)
    //     0x919340: ldur            x1, [x0, #-1]
    //     0x919344: ubfx            x1, x1, #0xc, #0x14
    // 0x919348: stp             xzr, x0, [SP]
    // 0x91934c: mov             x0, x1
    // 0x919350: r0 = GDT[cid_x0 + 0x13037]()
    //     0x919350: movz            x17, #0x3037
    //     0x919354: movk            x17, #0x1, lsl #16
    //     0x919358: add             lr, x0, x17
    //     0x91935c: ldr             lr, [x21, lr, lsl #3]
    //     0x919360: blr             lr
    // 0x919364: LoadField: r6 = r0->field_27
    //     0x919364: ldur            w6, [x0, #0x27]
    // 0x919368: DecompressPointer r6
    //     0x919368: add             x6, x6, HEAP, lsl #32
    // 0x91936c: stur            x6, [fp, #-0xc8]
    // 0x919370: r0 = DateTime()
    //     0x919370: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x919374: mov             x1, x0
    // 0x919378: r0 = false
    //     0x919378: add             x0, NULL, #0x30  ; false
    // 0x91937c: stur            x1, [fp, #-0xe8]
    // 0x919380: StoreField: r1->field_13 = r0
    //     0x919380: stur            w0, [x1, #0x13]
    // 0x919384: r0 = _getCurrentMicros()
    //     0x919384: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x919388: r1 = LoadInt32Instr(r0)
    //     0x919388: sbfx            x1, x0, #1, #0x1f
    //     0x91938c: tbz             w0, #0, #0x919394
    //     0x919390: ldur            x1, [x0, #7]
    // 0x919394: ldur            x0, [fp, #-0xe8]
    // 0x919398: StoreField: r0->field_7 = r1
    //     0x919398: stur            x1, [x0, #7]
    // 0x91939c: mov             x1, x0
    // 0x9193a0: r0 = timeZoneName()
    //     0x9193a0: bl              #0x919500  ; [dart:core] DateTime::timeZoneName
    // 0x9193a4: mov             x1, x0
    // 0x9193a8: ldur            x0, [fp, #-0xc0]
    // 0x9193ac: stur            x1, [fp, #-0xe8]
    // 0x9193b0: LoadField: d1 = r0->field_1b
    //     0x9193b0: ldur            d1, [x0, #0x1b]
    // 0x9193b4: mov             v0.16b, v1.16b
    // 0x9193b8: r17 = -264
    //     0x9193b8: movn            x17, #0x107
    // 0x9193bc: str             d1, [fp, x17]
    // 0x9193c0: stp             fp, lr, [SP, #-0x10]!
    // 0x9193c4: mov             fp, SP
    // 0x9193c8: CallRuntime_LibcRound(double) -> double
    //     0x9193c8: and             SP, SP, #0xfffffffffffffff0
    //     0x9193cc: mov             sp, SP
    //     0x9193d0: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x9193d4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x9193d8: blr             x16
    //     0x9193dc: movz            x16, #0x8
    //     0x9193e0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x9193e4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x9193e8: sub             sp, x16, #1, lsl #12
    //     0x9193ec: mov             SP, fp
    //     0x9193f0: ldp             fp, lr, [SP], #0x10
    // 0x9193f4: fcmp            d0, d0
    // 0x9193f8: b.vs            #0x9194ac
    // 0x9193fc: fcvtzs          x0, d0
    // 0x919400: asr             x16, x0, #0x1e
    // 0x919404: cmp             x16, x0, asr #63
    // 0x919408: b.ne            #0x9194ac
    // 0x91940c: lsl             x0, x0, #1
    // 0x919410: stur            x0, [fp, #-0xc0]
    // 0x919414: r0 = Location()
    //     0x919414: bl              #0x8ffb64  ; AllocateLocationStub -> Location (size=0x48)
    // 0x919418: stur            x0, [fp, #-0xf0]
    // 0x91941c: ldur            x16, [fp, #-0xc0]
    // 0x919420: r30 = true
    //     0x919420: add             lr, NULL, #0x20  ; true
    // 0x919424: stp             lr, x16, [SP]
    // 0x919428: mov             x1, x0
    // 0x91942c: ldur            x2, [fp, #-0xd0]
    // 0x919430: ldur            x3, [fp, #-0xd8]
    // 0x919434: ldur            d0, [fp, #-0x100]
    // 0x919438: ldur            d1, [fp, #-0xf8]
    // 0x91943c: ldur            x5, [fp, #-0xe0]
    // 0x919440: ldur            x6, [fp, #-0xc8]
    // 0x919444: ldur            x7, [fp, #-0xe8]
    // 0x919448: r4 = const [0, 0xa, 0x2, 0x8, altitude, 0x8, isFromGPS, 0x9, null]
    //     0x919448: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c410] List(9) [0, 0xa, 0x2, 0x8, "altitude", 0x8, "isFromGPS", 0x9, Null]
    //     0x91944c: ldr             x4, [x4, #0x410]
    // 0x919450: r0 = Location()
    //     0x919450: bl              #0x8ff888  ; [package:nuonline/app/data/models/location.dart] Location::Location
    // 0x919454: r1 = <LocationError, Location>
    //     0x919454: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c418] TypeArguments: <LocationError, Location>
    //     0x919458: ldr             x1, [x1, #0x418]
    // 0x91945c: r0 = Right()
    //     0x91945c: bl              #0x8c1ae0  ; AllocateRightStub -> Right<X0, X1> (size=0x10)
    // 0x919460: ldur            x2, [fp, #-0xf0]
    // 0x919464: StoreField: r0->field_b = r2
    //     0x919464: stur            w2, [x0, #0xb]
    // 0x919468: r0 = ReturnAsyncNotFuture()
    //     0x919468: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91946c: sub             SP, fp, #0x118
    // 0x919470: r1 = 60
    //     0x919470: movz            x1, #0x3c
    // 0x919474: branchIfSmi(r0, 0x919480)
    //     0x919474: tbz             w0, #0, #0x919480
    // 0x919478: r1 = LoadClassIdInstr(r0)
    //     0x919478: ldur            x1, [x0, #-1]
    //     0x91947c: ubfx            x1, x1, #0xc, #0x14
    // 0x919480: r17 = 6685
    //     0x919480: movz            x17, #0x1a1d
    // 0x919484: cmp             x1, x17
    // 0x919488: b.ne            #0x919498
    // 0x91948c: r0 = Instance_Left
    //     0x91948c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c420] Obj!Left<LocationError, Location>@e25cb1
    //     0x919490: ldr             x0, [x0, #0x420]
    // 0x919494: r0 = ReturnAsyncNotFuture()
    //     0x919494: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x919498: r0 = Instance_Left
    //     0x919498: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c428] Obj!Left<LocationError, Location>@e25ca1
    //     0x91949c: ldr             x0, [x0, #0x428]
    // 0x9194a0: r0 = ReturnAsyncNotFuture()
    //     0x9194a0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9194a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9194a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9194a8: b               #0x91918c
    // 0x9194ac: SaveReg d0
    //     0x9194ac: str             q0, [SP, #-0x10]!
    // 0x9194b0: r0 = 74
    //     0x9194b0: movz            x0, #0x4a
    // 0x9194b4: r30 = DoubleToIntegerStub
    //     0x9194b4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x9194b8: LoadField: r30 = r30->field_7
    //     0x9194b8: ldur            lr, [lr, #7]
    // 0x9194bc: blr             lr
    // 0x9194c0: RestoreReg d0
    //     0x9194c0: ldr             q0, [SP], #0x10
    // 0x9194c4: b               #0x919410
  }
  _ updateCurrentLocation(/* No info */) async {
    // ** addr: 0x91a77c, size: 0x94
    // 0x91a77c: EnterFrame
    //     0x91a77c: stp             fp, lr, [SP, #-0x10]!
    //     0x91a780: mov             fp, SP
    // 0x91a784: AllocStack(0x30)
    //     0x91a784: sub             SP, SP, #0x30
    // 0x91a788: SetupParameters(LocationRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x91a788: stur            NULL, [fp, #-8]
    //     0x91a78c: mov             x3, x2
    //     0x91a790: stur            x1, [fp, #-0x10]
    //     0x91a794: stur            x2, [fp, #-0x18]
    // 0x91a798: CheckStackOverflow
    //     0x91a798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91a79c: cmp             SP, x16
    //     0x91a7a0: b.ls            #0x91a808
    // 0x91a7a4: InitAsync() -> Future<void?>
    //     0x91a7a4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x91a7a8: bl              #0x661298  ; InitAsyncStub
    // 0x91a7ac: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91a7ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91a7b0: ldr             x0, [x0, #0x2728]
    //     0x91a7b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91a7b8: cmp             w0, w16
    //     0x91a7bc: b.ne            #0x91a7c8
    //     0x91a7c0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91a7c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91a7c8: stp             x0, NULL, [SP, #8]
    // 0x91a7cc: r16 = "v2_location"
    //     0x91a7cc: add             x16, PP, #8, lsl #12  ; [pp+0x80f8] "v2_location"
    //     0x91a7d0: ldr             x16, [x16, #0xf8]
    // 0x91a7d4: str             x16, [SP]
    // 0x91a7d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91a7d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91a7dc: r0 = box()
    //     0x91a7dc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91a7e0: mov             x1, x0
    // 0x91a7e4: ldur            x3, [fp, #-0x18]
    // 0x91a7e8: r2 = "current"
    //     0x91a7e8: add             x2, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0x91a7ec: ldr             x2, [x2, #0x110]
    // 0x91a7f0: r0 = put()
    //     0x91a7f0: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0x91a7f4: mov             x1, x0
    // 0x91a7f8: stur            x1, [fp, #-0x10]
    // 0x91a7fc: r0 = Await()
    //     0x91a7fc: bl              #0x661044  ; AwaitStub
    // 0x91a800: r0 = Null
    //     0x91a800: mov             x0, NULL
    // 0x91a804: r0 = ReturnAsyncNotFuture()
    //     0x91a804: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91a808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91a808: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91a80c: b               #0x91a7a4
  }
  _ localityFindAll(/* No info */) async {
    // ** addr: 0x920b30, size: 0x114
    // 0x920b30: EnterFrame
    //     0x920b30: stp             fp, lr, [SP, #-0x10]!
    //     0x920b34: mov             fp, SP
    // 0x920b38: AllocStack(0x88)
    //     0x920b38: sub             SP, SP, #0x88
    // 0x920b3c: SetupParameters(LocationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x920b3c: stur            NULL, [fp, #-8]
    //     0x920b40: stur            x1, [fp, #-0x58]
    //     0x920b44: stur            x2, [fp, #-0x60]
    // 0x920b48: CheckStackOverflow
    //     0x920b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x920b4c: cmp             SP, x16
    //     0x920b50: b.ls            #0x920c3c
    // 0x920b54: InitAsync() -> Future<ApiResult<List<Locality>>>
    //     0x920b54: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d088] TypeArguments: <ApiResult<List<Locality>>>
    //     0x920b58: ldr             x0, [x0, #0x88]
    //     0x920b5c: bl              #0x661298  ; InitAsyncStub
    // 0x920b60: ldur            x1, [fp, #-0x58]
    // 0x920b64: ldur            x0, [fp, #-0x60]
    // 0x920b68: LoadField: r3 = r1->field_7
    //     0x920b68: ldur            w3, [x1, #7]
    // 0x920b6c: DecompressPointer r3
    //     0x920b6c: add             x3, x3, HEAP, lsl #32
    // 0x920b70: stur            x3, [fp, #-0x68]
    // 0x920b74: r1 = Null
    //     0x920b74: mov             x1, NULL
    // 0x920b78: r2 = 4
    //     0x920b78: movz            x2, #0x4
    // 0x920b7c: r0 = AllocateArray()
    //     0x920b7c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x920b80: mov             x2, x0
    // 0x920b84: r16 = "district_id"
    //     0x920b84: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cfe8] "district_id"
    //     0x920b88: ldr             x16, [x16, #0xfe8]
    // 0x920b8c: StoreField: r2->field_f = r16
    //     0x920b8c: stur            w16, [x2, #0xf]
    // 0x920b90: ldur            x3, [fp, #-0x60]
    // 0x920b94: r0 = BoxInt64Instr(r3)
    //     0x920b94: sbfiz           x0, x3, #1, #0x1f
    //     0x920b98: cmp             x3, x0, asr #1
    //     0x920b9c: b.eq            #0x920ba8
    //     0x920ba0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x920ba4: stur            x3, [x0, #7]
    // 0x920ba8: StoreField: r2->field_13 = r0
    //     0x920ba8: stur            w0, [x2, #0x13]
    // 0x920bac: r16 = <String, dynamic>
    //     0x920bac: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x920bb0: stp             x2, x16, [SP]
    // 0x920bb4: r0 = Map._fromLiteral()
    //     0x920bb4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x920bb8: ldur            x16, [fp, #-0x68]
    // 0x920bbc: stp             x16, NULL, [SP, #0x10]
    // 0x920bc0: r16 = "/localities"
    //     0x920bc0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f090] "/localities"
    //     0x920bc4: ldr             x16, [x16, #0x90]
    // 0x920bc8: stp             x0, x16, [SP]
    // 0x920bcc: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x920bcc: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x920bd0: ldr             x4, [x4, #0x2f0]
    // 0x920bd4: r0 = get()
    //     0x920bd4: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x920bd8: mov             x1, x0
    // 0x920bdc: stur            x1, [fp, #-0x58]
    // 0x920be0: r0 = Await()
    //     0x920be0: bl              #0x661044  ; AwaitStub
    // 0x920be4: LoadField: r1 = r0->field_b
    //     0x920be4: ldur            w1, [x0, #0xb]
    // 0x920be8: DecompressPointer r1
    //     0x920be8: add             x1, x1, HEAP, lsl #32
    // 0x920bec: r0 = fromResponse()
    //     0x920bec: bl              #0x920c44  ; [package:nuonline/app/data/models/locality.dart] Locality::fromResponse
    // 0x920bf0: r1 = <List<Locality>>
    //     0x920bf0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d0a0] TypeArguments: <List<Locality>>
    //     0x920bf4: ldr             x1, [x1, #0xa0]
    // 0x920bf8: stur            x0, [fp, #-0x58]
    // 0x920bfc: r0 = _$SuccessImpl()
    //     0x920bfc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x920c00: mov             x1, x0
    // 0x920c04: ldur            x0, [fp, #-0x58]
    // 0x920c08: StoreField: r1->field_b = r0
    //     0x920c08: stur            w0, [x1, #0xb]
    // 0x920c0c: mov             x0, x1
    // 0x920c10: r0 = ReturnAsyncNotFuture()
    //     0x920c10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x920c14: sub             SP, fp, #0x88
    // 0x920c18: mov             x1, x0
    // 0x920c1c: r0 = getDioException()
    //     0x920c1c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x920c20: r1 = <List<Locality>>
    //     0x920c20: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d0a0] TypeArguments: <List<Locality>>
    //     0x920c24: ldr             x1, [x1, #0xa0]
    // 0x920c28: stur            x0, [fp, #-0x58]
    // 0x920c2c: r0 = _$FailureImpl()
    //     0x920c2c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x920c30: ldur            x1, [fp, #-0x58]
    // 0x920c34: StoreField: r0->field_b = r1
    //     0x920c34: stur            w1, [x0, #0xb]
    // 0x920c38: r0 = ReturnAsyncNotFuture()
    //     0x920c38: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x920c3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x920c3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x920c40: b               #0x920b54
  }
  _ regencyFindAll(/* No info */) async {
    // ** addr: 0x9216b8, size: 0x114
    // 0x9216b8: EnterFrame
    //     0x9216b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9216bc: mov             fp, SP
    // 0x9216c0: AllocStack(0x88)
    //     0x9216c0: sub             SP, SP, #0x88
    // 0x9216c4: SetupParameters(LocationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x9216c4: stur            NULL, [fp, #-8]
    //     0x9216c8: stur            x1, [fp, #-0x58]
    //     0x9216cc: stur            x2, [fp, #-0x60]
    // 0x9216d0: CheckStackOverflow
    //     0x9216d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9216d4: cmp             SP, x16
    //     0x9216d8: b.ls            #0x9217c4
    // 0x9216dc: InitAsync() -> Future<ApiResult<List<SimpleRegency>>>
    //     0x9216dc: add             x0, PP, #0x40, lsl #12  ; [pp+0x40388] TypeArguments: <ApiResult<List<SimpleRegency>>>
    //     0x9216e0: ldr             x0, [x0, #0x388]
    //     0x9216e4: bl              #0x661298  ; InitAsyncStub
    // 0x9216e8: ldur            x1, [fp, #-0x58]
    // 0x9216ec: ldur            x0, [fp, #-0x60]
    // 0x9216f0: LoadField: r3 = r1->field_7
    //     0x9216f0: ldur            w3, [x1, #7]
    // 0x9216f4: DecompressPointer r3
    //     0x9216f4: add             x3, x3, HEAP, lsl #32
    // 0x9216f8: stur            x3, [fp, #-0x68]
    // 0x9216fc: r1 = Null
    //     0x9216fc: mov             x1, NULL
    // 0x921700: r2 = 4
    //     0x921700: movz            x2, #0x4
    // 0x921704: r0 = AllocateArray()
    //     0x921704: bl              #0xec22fc  ; AllocateArrayStub
    // 0x921708: mov             x2, x0
    // 0x92170c: r16 = "province_id"
    //     0x92170c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0x921710: ldr             x16, [x16, #0x100]
    // 0x921714: StoreField: r2->field_f = r16
    //     0x921714: stur            w16, [x2, #0xf]
    // 0x921718: ldur            x3, [fp, #-0x60]
    // 0x92171c: r0 = BoxInt64Instr(r3)
    //     0x92171c: sbfiz           x0, x3, #1, #0x1f
    //     0x921720: cmp             x3, x0, asr #1
    //     0x921724: b.eq            #0x921730
    //     0x921728: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92172c: stur            x3, [x0, #7]
    // 0x921730: StoreField: r2->field_13 = r0
    //     0x921730: stur            w0, [x2, #0x13]
    // 0x921734: r16 = <String, dynamic>
    //     0x921734: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x921738: stp             x2, x16, [SP]
    // 0x92173c: r0 = Map._fromLiteral()
    //     0x92173c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x921740: ldur            x16, [fp, #-0x68]
    // 0x921744: stp             x16, NULL, [SP, #0x10]
    // 0x921748: r16 = "/regencies"
    //     0x921748: add             x16, PP, #0x40, lsl #12  ; [pp+0x40390] "/regencies"
    //     0x92174c: ldr             x16, [x16, #0x390]
    // 0x921750: stp             x0, x16, [SP]
    // 0x921754: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x921754: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x921758: ldr             x4, [x4, #0x2f0]
    // 0x92175c: r0 = get()
    //     0x92175c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x921760: mov             x1, x0
    // 0x921764: stur            x1, [fp, #-0x58]
    // 0x921768: r0 = Await()
    //     0x921768: bl              #0x661044  ; AwaitStub
    // 0x92176c: LoadField: r1 = r0->field_b
    //     0x92176c: ldur            w1, [x0, #0xb]
    // 0x921770: DecompressPointer r1
    //     0x921770: add             x1, x1, HEAP, lsl #32
    // 0x921774: r0 = fromResponse()
    //     0x921774: bl              #0x9217cc  ; [package:nuonline/app/data/models/simple_regency.dart] SimpleRegency::fromResponse
    // 0x921778: r1 = <List<SimpleRegency>>
    //     0x921778: add             x1, PP, #0x40, lsl #12  ; [pp+0x40398] TypeArguments: <List<SimpleRegency>>
    //     0x92177c: ldr             x1, [x1, #0x398]
    // 0x921780: stur            x0, [fp, #-0x58]
    // 0x921784: r0 = _$SuccessImpl()
    //     0x921784: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x921788: mov             x1, x0
    // 0x92178c: ldur            x0, [fp, #-0x58]
    // 0x921790: StoreField: r1->field_b = r0
    //     0x921790: stur            w0, [x1, #0xb]
    // 0x921794: mov             x0, x1
    // 0x921798: r0 = ReturnAsyncNotFuture()
    //     0x921798: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x92179c: sub             SP, fp, #0x88
    // 0x9217a0: mov             x1, x0
    // 0x9217a4: r0 = getDioException()
    //     0x9217a4: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x9217a8: r1 = <List<SimpleRegency>>
    //     0x9217a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40398] TypeArguments: <List<SimpleRegency>>
    //     0x9217ac: ldr             x1, [x1, #0x398]
    // 0x9217b0: stur            x0, [fp, #-0x58]
    // 0x9217b4: r0 = _$FailureImpl()
    //     0x9217b4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x9217b8: ldur            x1, [fp, #-0x58]
    // 0x9217bc: StoreField: r0->field_b = r1
    //     0x9217bc: stur            w1, [x0, #0xb]
    // 0x9217c0: r0 = ReturnAsyncNotFuture()
    //     0x9217c0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9217c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9217c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9217c8: b               #0x9216dc
  }
  _ locationFindAll(/* No info */) async {
    // ** addr: 0xb03eec, size: 0xfc
    // 0xb03eec: EnterFrame
    //     0xb03eec: stp             fp, lr, [SP, #-0x10]!
    //     0xb03ef0: mov             fp, SP
    // 0xb03ef4: AllocStack(0x88)
    //     0xb03ef4: sub             SP, SP, #0x88
    // 0xb03ef8: SetupParameters(LocationRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xb03ef8: stur            NULL, [fp, #-8]
    //     0xb03efc: stur            x1, [fp, #-0x58]
    //     0xb03f00: stur            x2, [fp, #-0x60]
    // 0xb03f04: CheckStackOverflow
    //     0xb03f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb03f08: cmp             SP, x16
    //     0xb03f0c: b.ls            #0xb03fe0
    // 0xb03f10: InitAsync() -> Future<ApiResult<List<Locality>>>
    //     0xb03f10: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d088] TypeArguments: <ApiResult<List<Locality>>>
    //     0xb03f14: ldr             x0, [x0, #0x88]
    //     0xb03f18: bl              #0x661298  ; InitAsyncStub
    // 0xb03f1c: ldur            x1, [fp, #-0x58]
    // 0xb03f20: ldur            x0, [fp, #-0x60]
    // 0xb03f24: LoadField: r3 = r1->field_7
    //     0xb03f24: ldur            w3, [x1, #7]
    // 0xb03f28: DecompressPointer r3
    //     0xb03f28: add             x3, x3, HEAP, lsl #32
    // 0xb03f2c: stur            x3, [fp, #-0x68]
    // 0xb03f30: r1 = Null
    //     0xb03f30: mov             x1, NULL
    // 0xb03f34: r2 = 4
    //     0xb03f34: movz            x2, #0x4
    // 0xb03f38: r0 = AllocateArray()
    //     0xb03f38: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb03f3c: r16 = "search"
    //     0xb03f3c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d090] "search"
    //     0xb03f40: ldr             x16, [x16, #0x90]
    // 0xb03f44: StoreField: r0->field_f = r16
    //     0xb03f44: stur            w16, [x0, #0xf]
    // 0xb03f48: ldur            x1, [fp, #-0x60]
    // 0xb03f4c: StoreField: r0->field_13 = r1
    //     0xb03f4c: stur            w1, [x0, #0x13]
    // 0xb03f50: r16 = <String, dynamic>
    //     0xb03f50: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xb03f54: stp             x0, x16, [SP]
    // 0xb03f58: r0 = Map._fromLiteral()
    //     0xb03f58: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb03f5c: ldur            x16, [fp, #-0x68]
    // 0xb03f60: stp             x16, NULL, [SP, #0x10]
    // 0xb03f64: r16 = "/locations"
    //     0xb03f64: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d098] "/locations"
    //     0xb03f68: ldr             x16, [x16, #0x98]
    // 0xb03f6c: stp             x0, x16, [SP]
    // 0xb03f70: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xb03f70: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xb03f74: ldr             x4, [x4, #0x2f0]
    // 0xb03f78: r0 = get()
    //     0xb03f78: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xb03f7c: mov             x1, x0
    // 0xb03f80: stur            x1, [fp, #-0x58]
    // 0xb03f84: r0 = Await()
    //     0xb03f84: bl              #0x661044  ; AwaitStub
    // 0xb03f88: LoadField: r1 = r0->field_b
    //     0xb03f88: ldur            w1, [x0, #0xb]
    // 0xb03f8c: DecompressPointer r1
    //     0xb03f8c: add             x1, x1, HEAP, lsl #32
    // 0xb03f90: r0 = fromResponse()
    //     0xb03f90: bl              #0x920c44  ; [package:nuonline/app/data/models/locality.dart] Locality::fromResponse
    // 0xb03f94: r1 = <List<Locality>>
    //     0xb03f94: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d0a0] TypeArguments: <List<Locality>>
    //     0xb03f98: ldr             x1, [x1, #0xa0]
    // 0xb03f9c: stur            x0, [fp, #-0x58]
    // 0xb03fa0: r0 = _$SuccessImpl()
    //     0xb03fa0: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xb03fa4: mov             x1, x0
    // 0xb03fa8: ldur            x0, [fp, #-0x58]
    // 0xb03fac: StoreField: r1->field_b = r0
    //     0xb03fac: stur            w0, [x1, #0xb]
    // 0xb03fb0: mov             x0, x1
    // 0xb03fb4: r0 = ReturnAsyncNotFuture()
    //     0xb03fb4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb03fb8: sub             SP, fp, #0x88
    // 0xb03fbc: mov             x1, x0
    // 0xb03fc0: r0 = getDioException()
    //     0xb03fc0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xb03fc4: r1 = <List<Locality>>
    //     0xb03fc4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d0a0] TypeArguments: <List<Locality>>
    //     0xb03fc8: ldr             x1, [x1, #0xa0]
    // 0xb03fcc: stur            x0, [fp, #-0x58]
    // 0xb03fd0: r0 = _$FailureImpl()
    //     0xb03fd0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xb03fd4: ldur            x1, [fp, #-0x58]
    // 0xb03fd8: StoreField: r0->field_b = r1
    //     0xb03fd8: stur            w1, [x0, #0xb]
    // 0xb03fdc: r0 = ReturnAsyncNotFuture()
    //     0xb03fdc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb03fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb03fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb03fe4: b               #0xb03f10
  }
  _ requestPermission(/* No info */) async {
    // ** addr: 0xb0b794, size: 0x48
    // 0xb0b794: EnterFrame
    //     0xb0b794: stp             fp, lr, [SP, #-0x10]!
    //     0xb0b798: mov             fp, SP
    // 0xb0b79c: AllocStack(0x18)
    //     0xb0b79c: sub             SP, SP, #0x18
    // 0xb0b7a0: SetupParameters(LocationRepository this /* r1 => r1, fp-0x10 */)
    //     0xb0b7a0: stur            NULL, [fp, #-8]
    //     0xb0b7a4: stur            x1, [fp, #-0x10]
    // 0xb0b7a8: CheckStackOverflow
    //     0xb0b7a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0b7ac: cmp             SP, x16
    //     0xb0b7b0: b.ls            #0xb0b7d4
    // 0xb0b7b4: InitAsync() -> Future<void?>
    //     0xb0b7b4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb0b7b8: bl              #0x661298  ; InitAsyncStub
    // 0xb0b7bc: r0 = requestPermission()
    //     0xb0b7bc: bl              #0x900398  ; [package:geolocator/geolocator.dart] Geolocator::requestPermission
    // 0xb0b7c0: mov             x1, x0
    // 0xb0b7c4: stur            x1, [fp, #-0x18]
    // 0xb0b7c8: r0 = Await()
    //     0xb0b7c8: bl              #0x661044  ; AwaitStub
    // 0xb0b7cc: r0 = Null
    //     0xb0b7cc: mov             x0, NULL
    // 0xb0b7d0: r0 = ReturnAsyncNotFuture()
    //     0xb0b7d0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb0b7d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0b7d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0b7d8: b               #0xb0b7b4
  }
  _ requestServicePermission(/* No info */) async {
    // ** addr: 0xb1ba04, size: 0x48
    // 0xb1ba04: EnterFrame
    //     0xb1ba04: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ba08: mov             fp, SP
    // 0xb1ba0c: AllocStack(0x18)
    //     0xb1ba0c: sub             SP, SP, #0x18
    // 0xb1ba10: SetupParameters(LocationRepository this /* r1 => r1, fp-0x10 */)
    //     0xb1ba10: stur            NULL, [fp, #-8]
    //     0xb1ba14: stur            x1, [fp, #-0x10]
    // 0xb1ba18: CheckStackOverflow
    //     0xb1ba18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ba1c: cmp             SP, x16
    //     0xb1ba20: b.ls            #0xb1ba44
    // 0xb1ba24: InitAsync() -> Future<void?>
    //     0xb1ba24: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb1ba28: bl              #0x661298  ; InitAsyncStub
    // 0xb1ba2c: r0 = openLocationSettings()
    //     0xb1ba2c: bl              #0xb1ba4c  ; [package:geolocator/geolocator.dart] Geolocator::openLocationSettings
    // 0xb1ba30: mov             x1, x0
    // 0xb1ba34: stur            x1, [fp, #-0x18]
    // 0xb1ba38: r0 = Await()
    //     0xb1ba38: bl              #0x661044  ; AwaitStub
    // 0xb1ba3c: r0 = Null
    //     0xb1ba3c: mov             x0, NULL
    // 0xb1ba40: r0 = ReturnAsyncNotFuture()
    //     0xb1ba40: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb1ba44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1ba44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1ba48: b               #0xb1ba24
  }
}
