// lib: , url: package:nuonline/app/data/repositories/manasik_repository.dart

// class id: 1050089, size: 0x8
class :: {
}

// class id: 1087, size: 0x8, field offset: 0x8
class ManasikRepository extends Object {

  static _ find(/* No info */) {
    // ** addr: 0x814594, size: 0x64
    // 0x814594: EnterFrame
    //     0x814594: stp             fp, lr, [SP, #-0x10]!
    //     0x814598: mov             fp, SP
    // 0x81459c: AllocStack(0x10)
    //     0x81459c: sub             SP, SP, #0x10
    // 0x8145a0: CheckStackOverflow
    //     0x8145a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8145a4: cmp             SP, x16
    //     0x8145a8: b.ls            #0x8145f0
    // 0x8145ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8145ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8145b0: ldr             x0, [x0, #0x2670]
    //     0x8145b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8145b8: cmp             w0, w16
    //     0x8145bc: b.ne            #0x8145c8
    //     0x8145c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8145c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8145c8: r16 = <ManasikRepository>
    //     0x8145c8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10138] TypeArguments: <ManasikRepository>
    //     0x8145cc: ldr             x16, [x16, #0x138]
    // 0x8145d0: r30 = "manasik_repo"
    //     0x8145d0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10140] "manasik_repo"
    //     0x8145d4: ldr             lr, [lr, #0x140]
    // 0x8145d8: stp             lr, x16, [SP]
    // 0x8145dc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8145dc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8145e0: r0 = Inst.find()
    //     0x8145e0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8145e4: LeaveFrame
    //     0x8145e4: mov             SP, fp
    //     0x8145e8: ldp             fp, lr, [SP], #0x10
    // 0x8145ec: ret
    //     0x8145ec: ret             
    // 0x8145f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8145f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8145f4: b               #0x8145ac
  }
  _ findById(/* No info */) async {
    // ** addr: 0x8fcc28, size: 0x140
    // 0x8fcc28: EnterFrame
    //     0x8fcc28: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcc2c: mov             fp, SP
    // 0x8fcc30: AllocStack(0x70)
    //     0x8fcc30: sub             SP, SP, #0x70
    // 0x8fcc34: SetupParameters(ManasikRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x8fcc34: stur            NULL, [fp, #-8]
    //     0x8fcc38: stur            x1, [fp, #-0x60]
    //     0x8fcc3c: stur            x2, [fp, #-0x68]
    // 0x8fcc40: CheckStackOverflow
    //     0x8fcc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcc44: cmp             SP, x16
    //     0x8fcc48: b.ls            #0x8fcd60
    // 0x8fcc4c: InitAsync() -> Future<ApiResult<Manasik>>
    //     0x8fcc4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e830] TypeArguments: <ApiResult<Manasik>>
    //     0x8fcc50: ldr             x0, [x0, #0x830]
    //     0x8fcc54: bl              #0x661298  ; InitAsyncStub
    // 0x8fcc58: ldur            x0, [fp, #-0x68]
    // 0x8fcc5c: r0 = InitLateStaticField(0x698) // [package:flutter/src/services/asset_bundle.dart] ::rootBundle
    //     0x8fcc5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8fcc60: ldr             x0, [x0, #0xd30]
    //     0x8fcc64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8fcc68: cmp             w0, w16
    //     0x8fcc6c: b.ne            #0x8fcc78
    //     0x8fcc70: ldr             x2, [PP, #0x3260]  ; [pp+0x3260] Field <::.rootBundle>: static late final (offset: 0x698)
    //     0x8fcc74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8fcc78: r1 = Null
    //     0x8fcc78: mov             x1, NULL
    // 0x8fcc7c: r2 = 6
    //     0x8fcc7c: movz            x2, #0x6
    // 0x8fcc80: stur            x0, [fp, #-0x60]
    // 0x8fcc84: r0 = AllocateArray()
    //     0x8fcc84: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8fcc88: mov             x2, x0
    // 0x8fcc8c: r16 = "assets/data/manasik_"
    //     0x8fcc8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e838] "assets/data/manasik_"
    //     0x8fcc90: ldr             x16, [x16, #0x838]
    // 0x8fcc94: StoreField: r2->field_f = r16
    //     0x8fcc94: stur            w16, [x2, #0xf]
    // 0x8fcc98: ldur            x3, [fp, #-0x68]
    // 0x8fcc9c: r0 = BoxInt64Instr(r3)
    //     0x8fcc9c: sbfiz           x0, x3, #1, #0x1f
    //     0x8fcca0: cmp             x3, x0, asr #1
    //     0x8fcca4: b.eq            #0x8fccb0
    //     0x8fcca8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fccac: stur            x3, [x0, #7]
    // 0x8fccb0: StoreField: r2->field_13 = r0
    //     0x8fccb0: stur            w0, [x2, #0x13]
    // 0x8fccb4: r16 = ".json"
    //     0x8fccb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e840] ".json"
    //     0x8fccb8: ldr             x16, [x16, #0x840]
    // 0x8fccbc: ArrayStore: r2[0] = r16  ; List_4
    //     0x8fccbc: stur            w16, [x2, #0x17]
    // 0x8fccc0: str             x2, [SP]
    // 0x8fccc4: r0 = _interpolate()
    //     0x8fccc4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8fccc8: ldur            x1, [fp, #-0x60]
    // 0x8fcccc: mov             x2, x0
    // 0x8fccd0: r0 = loadString()
    //     0x8fccd0: bl              #0x72bd88  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadString
    // 0x8fccd4: mov             x1, x0
    // 0x8fccd8: stur            x1, [fp, #-0x60]
    // 0x8fccdc: r0 = Await()
    //     0x8fccdc: bl              #0x661044  ; AwaitStub
    // 0x8fcce0: mov             x1, x0
    // 0x8fcce4: r0 = jsonDecode()
    //     0x8fcce4: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x8fcce8: mov             x3, x0
    // 0x8fccec: r2 = Null
    //     0x8fccec: mov             x2, NULL
    // 0x8fccf0: r1 = Null
    //     0x8fccf0: mov             x1, NULL
    // 0x8fccf4: stur            x3, [fp, #-0x60]
    // 0x8fccf8: r8 = Map<String, dynamic>
    //     0x8fccf8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fccfc: r3 = Null
    //     0x8fccfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e848] Null
    //     0x8fcd00: ldr             x3, [x3, #0x848]
    // 0x8fcd04: r0 = Map<String, dynamic>()
    //     0x8fcd04: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fcd08: ldur            x2, [fp, #-0x60]
    // 0x8fcd0c: r1 = Null
    //     0x8fcd0c: mov             x1, NULL
    // 0x8fcd10: r0 = Manasik.fromMap()
    //     0x8fcd10: bl              #0x8fcd68  ; [package:nuonline/app/data/models/manasik.dart] Manasik::Manasik.fromMap
    // 0x8fcd14: r1 = <Manasik>
    //     0x8fcd14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7f8] TypeArguments: <Manasik>
    //     0x8fcd18: ldr             x1, [x1, #0x7f8]
    // 0x8fcd1c: stur            x0, [fp, #-0x60]
    // 0x8fcd20: r0 = _$SuccessImpl()
    //     0x8fcd20: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8fcd24: mov             x1, x0
    // 0x8fcd28: ldur            x0, [fp, #-0x60]
    // 0x8fcd2c: StoreField: r1->field_b = r0
    //     0x8fcd2c: stur            w0, [x1, #0xb]
    // 0x8fcd30: mov             x0, x1
    // 0x8fcd34: r0 = ReturnAsyncNotFuture()
    //     0x8fcd34: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fcd38: sub             SP, fp, #0x70
    // 0x8fcd3c: mov             x1, x0
    // 0x8fcd40: r0 = getDioException()
    //     0x8fcd40: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fcd44: r1 = <Manasik>
    //     0x8fcd44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e7f8] TypeArguments: <Manasik>
    //     0x8fcd48: ldr             x1, [x1, #0x7f8]
    // 0x8fcd4c: stur            x0, [fp, #-0x60]
    // 0x8fcd50: r0 = _$FailureImpl()
    //     0x8fcd50: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8fcd54: ldur            x1, [fp, #-0x60]
    // 0x8fcd58: StoreField: r0->field_b = r1
    //     0x8fcd58: stur            w1, [x0, #0xb]
    // 0x8fcd5c: r0 = ReturnAsyncNotFuture()
    //     0x8fcd5c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fcd60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fcd60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fcd64: b               #0x8fcc4c
  }
}
