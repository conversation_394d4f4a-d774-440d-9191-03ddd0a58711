// lib: , url: package:nuonline/app/data/repositories/menu_repository.dart

// class id: 1050090, size: 0x8
class :: {
}

// class id: 1086, size: 0x14, field offset: 0x8
class MenuRepository extends Object {

  _ findAll(/* No info */) async {
    // ** addr: 0x7ec134, size: 0x464
    // 0x7ec134: EnterFrame
    //     0x7ec134: stp             fp, lr, [SP, #-0x10]!
    //     0x7ec138: mov             fp, SP
    // 0x7ec13c: AllocStack(0x78)
    //     0x7ec13c: sub             SP, SP, #0x78
    // 0x7ec140: SetupParameters(MenuRepository this /* r1 => r1, fp-0x10 */)
    //     0x7ec140: stur            NULL, [fp, #-8]
    //     0x7ec144: stur            x1, [fp, #-0x10]
    // 0x7ec148: CheckStackOverflow
    //     0x7ec148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ec14c: cmp             SP, x16
    //     0x7ec150: b.ls            #0x7ec574
    // 0x7ec154: InitAsync() -> Future<ApiResult<List<Menu>>>
    //     0x7ec154: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f3b0] TypeArguments: <ApiResult<List<Menu>>>
    //     0x7ec158: ldr             x0, [x0, #0x3b0]
    //     0x7ec15c: bl              #0x661298  ; InitAsyncStub
    // 0x7ec160: ldur            x0, [fp, #-0x10]
    // 0x7ec164: LoadField: r1 = r0->field_f
    //     0x7ec164: ldur            w1, [x0, #0xf]
    // 0x7ec168: DecompressPointer r1
    //     0x7ec168: add             x1, x1, HEAP, lsl #32
    // 0x7ec16c: r0 = menus()
    //     0x7ec16c: bl              #0x7ee9f8  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::menus
    // 0x7ec170: mov             x2, x0
    // 0x7ec174: ldur            x0, [fp, #-0x10]
    // 0x7ec178: stur            x2, [fp, #-0x18]
    // 0x7ec17c: LoadField: r1 = r0->field_7
    //     0x7ec17c: ldur            w1, [x0, #7]
    // 0x7ec180: DecompressPointer r1
    //     0x7ec180: add             x1, x1, HEAP, lsl #32
    // 0x7ec184: r0 = isDefaultMenu()
    //     0x7ec184: bl              #0x7ec8bc  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isDefaultMenu
    // 0x7ec188: tbz             w0, #4, #0x7ec508
    // 0x7ec18c: ldur            x2, [fp, #-0x18]
    // 0x7ec190: LoadField: r0 = r2->field_7
    //     0x7ec190: ldur            w0, [x2, #7]
    // 0x7ec194: DecompressPointer r0
    //     0x7ec194: add             x0, x0, HEAP, lsl #32
    // 0x7ec198: stur            x0, [fp, #-0x30]
    // 0x7ec19c: LoadField: r1 = r2->field_b
    //     0x7ec19c: ldur            w1, [x2, #0xb]
    // 0x7ec1a0: r3 = LoadInt32Instr(r1)
    //     0x7ec1a0: sbfx            x3, x1, #1, #0x1f
    // 0x7ec1a4: stur            x3, [fp, #-0x28]
    // 0x7ec1a8: r1 = 0
    //     0x7ec1a8: movz            x1, #0
    // 0x7ec1ac: CheckStackOverflow
    //     0x7ec1ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ec1b0: cmp             SP, x16
    //     0x7ec1b4: b.ls            #0x7ec57c
    // 0x7ec1b8: LoadField: r4 = r2->field_b
    //     0x7ec1b8: ldur            w4, [x2, #0xb]
    // 0x7ec1bc: r5 = LoadInt32Instr(r4)
    //     0x7ec1bc: sbfx            x5, x4, #1, #0x1f
    // 0x7ec1c0: cmp             x3, x5
    // 0x7ec1c4: b.ne            #0x7ec554
    // 0x7ec1c8: cmp             x1, x5
    // 0x7ec1cc: b.ge            #0x7ec490
    // 0x7ec1d0: LoadField: r4 = r2->field_f
    //     0x7ec1d0: ldur            w4, [x2, #0xf]
    // 0x7ec1d4: DecompressPointer r4
    //     0x7ec1d4: add             x4, x4, HEAP, lsl #32
    // 0x7ec1d8: ArrayLoad: r5 = r4[r1]  ; Unknown_4
    //     0x7ec1d8: add             x16, x4, x1, lsl #2
    //     0x7ec1dc: ldur            w5, [x16, #0xf]
    // 0x7ec1e0: DecompressPointer r5
    //     0x7ec1e0: add             x5, x5, HEAP, lsl #32
    // 0x7ec1e4: stur            x5, [fp, #-0x10]
    // 0x7ec1e8: add             x4, x1, #1
    // 0x7ec1ec: stur            x4, [fp, #-0x20]
    // 0x7ec1f0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x7ec1f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7ec1f4: ldr             x0, [x0, #0x2728]
    //     0x7ec1f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7ec1fc: cmp             w0, w16
    //     0x7ec200: b.ne            #0x7ec20c
    //     0x7ec204: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x7ec208: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7ec20c: r16 = <Menu>
    //     0x7ec20c: ldr             x16, [PP, #0x7b40]  ; [pp+0x7b40] TypeArguments: <Menu>
    // 0x7ec210: stp             x0, x16, [SP, #8]
    // 0x7ec214: r16 = "v2_menus"
    //     0x7ec214: ldr             x16, [PP, #0x7bd8]  ; [pp+0x7bd8] "v2_menus"
    // 0x7ec218: str             x16, [SP]
    // 0x7ec21c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ec21c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ec220: r0 = box()
    //     0x7ec220: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7ec224: mov             x2, x0
    // 0x7ec228: ldur            x0, [fp, #-0x10]
    // 0x7ec22c: stur            x2, [fp, #-0x40]
    // 0x7ec230: LoadField: r3 = r0->field_1b
    //     0x7ec230: ldur            w3, [x0, #0x1b]
    // 0x7ec234: DecompressPointer r3
    //     0x7ec234: add             x3, x3, HEAP, lsl #32
    // 0x7ec238: mov             x1, x2
    // 0x7ec23c: stur            x3, [fp, #-0x38]
    // 0x7ec240: r0 = checkOpen()
    //     0x7ec240: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7ec244: ldur            x0, [fp, #-0x40]
    // 0x7ec248: LoadField: r1 = r0->field_1b
    //     0x7ec248: ldur            w1, [x0, #0x1b]
    // 0x7ec24c: DecompressPointer r1
    //     0x7ec24c: add             x1, x1, HEAP, lsl #32
    // 0x7ec250: r16 = Sentinel
    //     0x7ec250: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7ec254: cmp             w1, w16
    // 0x7ec258: b.eq            #0x7ec584
    // 0x7ec25c: LoadField: r2 = r1->field_13
    //     0x7ec25c: ldur            w2, [x1, #0x13]
    // 0x7ec260: DecompressPointer r2
    //     0x7ec260: add             x2, x2, HEAP, lsl #32
    // 0x7ec264: mov             x1, x2
    // 0x7ec268: ldur            x2, [fp, #-0x38]
    // 0x7ec26c: r0 = _getNode()
    //     0x7ec26c: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0x7ec270: cmp             w0, NULL
    // 0x7ec274: b.ne            #0x7ec280
    // 0x7ec278: r0 = Null
    //     0x7ec278: mov             x0, NULL
    // 0x7ec27c: b               #0x7ec28c
    // 0x7ec280: LoadField: r1 = r0->field_f
    //     0x7ec280: ldur            w1, [x0, #0xf]
    // 0x7ec284: DecompressPointer r1
    //     0x7ec284: add             x1, x1, HEAP, lsl #32
    // 0x7ec288: mov             x0, x1
    // 0x7ec28c: cmp             w0, NULL
    // 0x7ec290: b.eq            #0x7ec2e8
    // 0x7ec294: ldur            x1, [fp, #-0x40]
    // 0x7ec298: LoadField: r3 = r0->field_b
    //     0x7ec298: ldur            w3, [x0, #0xb]
    // 0x7ec29c: DecompressPointer r3
    //     0x7ec29c: add             x3, x3, HEAP, lsl #32
    // 0x7ec2a0: stur            x3, [fp, #-0x38]
    // 0x7ec2a4: LoadField: r2 = r1->field_7
    //     0x7ec2a4: ldur            w2, [x1, #7]
    // 0x7ec2a8: DecompressPointer r2
    //     0x7ec2a8: add             x2, x2, HEAP, lsl #32
    // 0x7ec2ac: mov             x0, x3
    // 0x7ec2b0: r1 = Null
    //     0x7ec2b0: mov             x1, NULL
    // 0x7ec2b4: cmp             w0, NULL
    // 0x7ec2b8: b.eq            #0x7ec2e0
    // 0x7ec2bc: cmp             w2, NULL
    // 0x7ec2c0: b.eq            #0x7ec2e0
    // 0x7ec2c4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ec2c4: ldur            w4, [x2, #0x17]
    // 0x7ec2c8: DecompressPointer r4
    //     0x7ec2c8: add             x4, x4, HEAP, lsl #32
    // 0x7ec2cc: r8 = X0?
    //     0x7ec2cc: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0x7ec2d0: LoadField: r9 = r4->field_7
    //     0x7ec2d0: ldur            x9, [x4, #7]
    // 0x7ec2d4: r3 = Null
    //     0x7ec2d4: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f3b8] Null
    //     0x7ec2d8: ldr             x3, [x3, #0x3b8]
    // 0x7ec2dc: blr             x9
    // 0x7ec2e0: ldur            x0, [fp, #-0x38]
    // 0x7ec2e4: b               #0x7ec2ec
    // 0x7ec2e8: r0 = Null
    //     0x7ec2e8: mov             x0, NULL
    // 0x7ec2ec: cmp             w0, NULL
    // 0x7ec2f0: b.eq            #0x7ec47c
    // 0x7ec2f4: ldur            x2, [fp, #-0x18]
    // 0x7ec2f8: LoadField: r1 = r2->field_b
    //     0x7ec2f8: ldur            w1, [x2, #0xb]
    // 0x7ec2fc: r3 = LoadInt32Instr(r1)
    //     0x7ec2fc: sbfx            x3, x1, #1, #0x1f
    // 0x7ec300: LoadField: r1 = r2->field_f
    //     0x7ec300: ldur            w1, [x2, #0xf]
    // 0x7ec304: DecompressPointer r1
    //     0x7ec304: add             x1, x1, HEAP, lsl #32
    // 0x7ec308: ldur            x4, [fp, #-0x10]
    // 0x7ec30c: r5 = 0
    //     0x7ec30c: movz            x5, #0
    // 0x7ec310: CheckStackOverflow
    //     0x7ec310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ec314: cmp             SP, x16
    //     0x7ec318: b.ls            #0x7ec58c
    // 0x7ec31c: cmp             x5, x3
    // 0x7ec320: b.ge            #0x7ec34c
    // 0x7ec324: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x7ec324: add             x16, x1, x5, lsl #2
    //     0x7ec328: ldur            w6, [x16, #0xf]
    // 0x7ec32c: DecompressPointer r6
    //     0x7ec32c: add             x6, x6, HEAP, lsl #32
    // 0x7ec330: cmp             w6, w4
    // 0x7ec334: b.eq            #0x7ec344
    // 0x7ec338: add             x6, x5, #1
    // 0x7ec33c: mov             x5, x6
    // 0x7ec340: b               #0x7ec310
    // 0x7ec344: mov             x1, x5
    // 0x7ec348: b               #0x7ec350
    // 0x7ec34c: r1 = -1
    //     0x7ec34c: movn            x1, #0
    // 0x7ec350: stur            x1, [fp, #-0x60]
    // 0x7ec354: LoadField: r3 = r0->field_13
    //     0x7ec354: ldur            x3, [x0, #0x13]
    // 0x7ec358: stur            x3, [fp, #-0x58]
    // 0x7ec35c: LoadField: r0 = r4->field_1b
    //     0x7ec35c: ldur            w0, [x4, #0x1b]
    // 0x7ec360: DecompressPointer r0
    //     0x7ec360: add             x0, x0, HEAP, lsl #32
    // 0x7ec364: stur            x0, [fp, #-0x50]
    // 0x7ec368: LoadField: r5 = r4->field_1f
    //     0x7ec368: ldur            w5, [x4, #0x1f]
    // 0x7ec36c: DecompressPointer r5
    //     0x7ec36c: add             x5, x5, HEAP, lsl #32
    // 0x7ec370: stur            x5, [fp, #-0x48]
    // 0x7ec374: LoadField: r6 = r4->field_23
    //     0x7ec374: ldur            w6, [x4, #0x23]
    // 0x7ec378: DecompressPointer r6
    //     0x7ec378: add             x6, x6, HEAP, lsl #32
    // 0x7ec37c: stur            x6, [fp, #-0x40]
    // 0x7ec380: LoadField: r7 = r4->field_27
    //     0x7ec380: ldur            w7, [x4, #0x27]
    // 0x7ec384: DecompressPointer r7
    //     0x7ec384: add             x7, x7, HEAP, lsl #32
    // 0x7ec388: stur            x7, [fp, #-0x38]
    // 0x7ec38c: r0 = Menu()
    //     0x7ec38c: bl              #0x7ec8b0  ; AllocateMenuStub -> Menu (size=0x2c)
    // 0x7ec390: mov             x1, x0
    // 0x7ec394: ldur            x0, [fp, #-0x58]
    // 0x7ec398: stur            x1, [fp, #-0x10]
    // 0x7ec39c: StoreField: r1->field_13 = r0
    //     0x7ec39c: stur            x0, [x1, #0x13]
    // 0x7ec3a0: ldur            x0, [fp, #-0x50]
    // 0x7ec3a4: StoreField: r1->field_1b = r0
    //     0x7ec3a4: stur            w0, [x1, #0x1b]
    // 0x7ec3a8: ldur            x0, [fp, #-0x48]
    // 0x7ec3ac: StoreField: r1->field_1f = r0
    //     0x7ec3ac: stur            w0, [x1, #0x1f]
    // 0x7ec3b0: ldur            x0, [fp, #-0x40]
    // 0x7ec3b4: StoreField: r1->field_23 = r0
    //     0x7ec3b4: stur            w0, [x1, #0x23]
    // 0x7ec3b8: ldur            x0, [fp, #-0x38]
    // 0x7ec3bc: StoreField: r1->field_27 = r0
    //     0x7ec3bc: stur            w0, [x1, #0x27]
    // 0x7ec3c0: r16 = <HiveList<HiveObjectMixin>, int>
    //     0x7ec3c0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0x7ec3c4: ldr             x16, [x16, #0x9f8]
    // 0x7ec3c8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x7ec3cc: stp             lr, x16, [SP]
    // 0x7ec3d0: r0 = Map._fromLiteral()
    //     0x7ec3d0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7ec3d4: ldur            x3, [fp, #-0x10]
    // 0x7ec3d8: StoreField: r3->field_f = r0
    //     0x7ec3d8: stur            w0, [x3, #0xf]
    //     0x7ec3dc: ldurb           w16, [x3, #-1]
    //     0x7ec3e0: ldurb           w17, [x0, #-1]
    //     0x7ec3e4: and             x16, x17, x16, lsr #2
    //     0x7ec3e8: tst             x16, HEAP, lsr #32
    //     0x7ec3ec: b.eq            #0x7ec3f4
    //     0x7ec3f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7ec3f4: mov             x0, x3
    // 0x7ec3f8: ldur            x2, [fp, #-0x30]
    // 0x7ec3fc: r1 = Null
    //     0x7ec3fc: mov             x1, NULL
    // 0x7ec400: cmp             w2, NULL
    // 0x7ec404: b.eq            #0x7ec424
    // 0x7ec408: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7ec408: ldur            w4, [x2, #0x17]
    // 0x7ec40c: DecompressPointer r4
    //     0x7ec40c: add             x4, x4, HEAP, lsl #32
    // 0x7ec410: r8 = X0
    //     0x7ec410: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7ec414: LoadField: r9 = r4->field_7
    //     0x7ec414: ldur            x9, [x4, #7]
    // 0x7ec418: r3 = Null
    //     0x7ec418: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f3c8] Null
    //     0x7ec41c: ldr             x3, [x3, #0x3c8]
    // 0x7ec420: blr             x9
    // 0x7ec424: ldur            x2, [fp, #-0x18]
    // 0x7ec428: LoadField: r0 = r2->field_b
    //     0x7ec428: ldur            w0, [x2, #0xb]
    // 0x7ec42c: r1 = LoadInt32Instr(r0)
    //     0x7ec42c: sbfx            x1, x0, #1, #0x1f
    // 0x7ec430: mov             x0, x1
    // 0x7ec434: ldur            x1, [fp, #-0x60]
    // 0x7ec438: cmp             x1, x0
    // 0x7ec43c: b.hs            #0x7ec594
    // 0x7ec440: LoadField: r1 = r2->field_f
    //     0x7ec440: ldur            w1, [x2, #0xf]
    // 0x7ec444: DecompressPointer r1
    //     0x7ec444: add             x1, x1, HEAP, lsl #32
    // 0x7ec448: ldur            x0, [fp, #-0x10]
    // 0x7ec44c: ldur            x3, [fp, #-0x60]
    // 0x7ec450: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7ec450: add             x25, x1, x3, lsl #2
    //     0x7ec454: add             x25, x25, #0xf
    //     0x7ec458: str             w0, [x25]
    //     0x7ec45c: tbz             w0, #0, #0x7ec478
    //     0x7ec460: ldurb           w16, [x1, #-1]
    //     0x7ec464: ldurb           w17, [x0, #-1]
    //     0x7ec468: and             x16, x17, x16, lsr #2
    //     0x7ec46c: tst             x16, HEAP, lsr #32
    //     0x7ec470: b.eq            #0x7ec478
    //     0x7ec474: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7ec478: b               #0x7ec480
    // 0x7ec47c: ldur            x2, [fp, #-0x18]
    // 0x7ec480: ldur            x1, [fp, #-0x20]
    // 0x7ec484: ldur            x0, [fp, #-0x30]
    // 0x7ec488: ldur            x3, [fp, #-0x28]
    // 0x7ec48c: b               #0x7ec1ac
    // 0x7ec490: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x7ec490: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7ec494: ldr             x0, [x0, #0x2728]
    //     0x7ec498: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7ec49c: cmp             w0, w16
    //     0x7ec4a0: b.ne            #0x7ec4ac
    //     0x7ec4a4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x7ec4a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7ec4ac: r16 = <Menu>
    //     0x7ec4ac: ldr             x16, [PP, #0x7b40]  ; [pp+0x7b40] TypeArguments: <Menu>
    // 0x7ec4b0: stp             x0, x16, [SP, #8]
    // 0x7ec4b4: r16 = "v2_menus"
    //     0x7ec4b4: ldr             x16, [PP, #0x7bd8]  ; [pp+0x7bd8] "v2_menus"
    // 0x7ec4b8: str             x16, [SP]
    // 0x7ec4bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ec4bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ec4c0: r0 = box()
    //     0x7ec4c0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7ec4c4: r1 = Function '<anonymous closure>':.
    //     0x7ec4c4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3d8] AnonymousClosure: (0x7efac4), in [package:nuonline/app/data/repositories/menu_repository.dart] MenuRepository::findAll (0x7ec134)
    //     0x7ec4c8: ldr             x1, [x1, #0x3d8]
    // 0x7ec4cc: r2 = Null
    //     0x7ec4cc: mov             x2, NULL
    // 0x7ec4d0: stur            x0, [fp, #-0x10]
    // 0x7ec4d4: r0 = AllocateClosure()
    //     0x7ec4d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ec4d8: ldur            x2, [fp, #-0x18]
    // 0x7ec4dc: mov             x3, x0
    // 0x7ec4e0: r1 = <dynamic, Menu>
    //     0x7ec4e0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d620] TypeArguments: <dynamic, Menu>
    //     0x7ec4e4: ldr             x1, [x1, #0x620]
    // 0x7ec4e8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7ec4e8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7ec4ec: r0 = LinkedHashMap.fromIterable()
    //     0x7ec4ec: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x7ec4f0: ldur            x1, [fp, #-0x10]
    // 0x7ec4f4: mov             x2, x0
    // 0x7ec4f8: r0 = putAll()
    //     0x7ec4f8: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x7ec4fc: mov             x1, x0
    // 0x7ec500: stur            x1, [fp, #-0x10]
    // 0x7ec504: r0 = Await()
    //     0x7ec504: bl              #0x661044  ; AwaitStub
    // 0x7ec508: r1 = Function '<anonymous closure>':.
    //     0x7ec508: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3e0] Function: [dart:io] _ExternalBuffer::end (0x913470)
    //     0x7ec50c: ldr             x1, [x1, #0x3e0]
    // 0x7ec510: r2 = Null
    //     0x7ec510: mov             x2, NULL
    // 0x7ec514: r0 = AllocateClosure()
    //     0x7ec514: bl              #0xec1630  ; AllocateClosureStub
    // 0x7ec518: r16 = <Menu>
    //     0x7ec518: ldr             x16, [PP, #0x7b40]  ; [pp+0x7b40] TypeArguments: <Menu>
    // 0x7ec51c: ldur            lr, [fp, #-0x18]
    // 0x7ec520: stp             lr, x16, [SP, #8]
    // 0x7ec524: str             x0, [SP]
    // 0x7ec528: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7ec528: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7ec52c: r0 = ListExtension.sortBy()
    //     0x7ec52c: bl              #0x7ec728  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.sortBy
    // 0x7ec530: r1 = <List<Menu>>
    //     0x7ec530: add             x1, PP, #0x33, lsl #12  ; [pp+0x33d70] TypeArguments: <List<Menu>>
    //     0x7ec534: ldr             x1, [x1, #0xd70]
    // 0x7ec538: stur            x0, [fp, #-0x10]
    // 0x7ec53c: r0 = _$SuccessImpl()
    //     0x7ec53c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7ec540: mov             x1, x0
    // 0x7ec544: ldur            x0, [fp, #-0x10]
    // 0x7ec548: StoreField: r1->field_b = r0
    //     0x7ec548: stur            w0, [x1, #0xb]
    // 0x7ec54c: mov             x0, x1
    // 0x7ec550: r0 = ReturnAsyncNotFuture()
    //     0x7ec550: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7ec554: mov             x0, x2
    // 0x7ec558: r0 = ConcurrentModificationError()
    //     0x7ec558: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x7ec55c: mov             x1, x0
    // 0x7ec560: ldur            x0, [fp, #-0x18]
    // 0x7ec564: StoreField: r1->field_b = r0
    //     0x7ec564: stur            w0, [x1, #0xb]
    // 0x7ec568: mov             x0, x1
    // 0x7ec56c: r0 = Throw()
    //     0x7ec56c: bl              #0xec04b8  ; ThrowStub
    // 0x7ec570: brk             #0
    // 0x7ec574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec578: b               #0x7ec154
    // 0x7ec57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec57c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec580: b               #0x7ec1b8
    // 0x7ec584: r9 = keystore
    //     0x7ec584: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7ec588: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7ec588: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x7ec58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec58c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec590: b               #0x7ec31c
    // 0x7ec594: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7ec594: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7efac4, size: 0x4c
    // 0x7efac4: EnterFrame
    //     0x7efac4: stp             fp, lr, [SP, #-0x10]!
    //     0x7efac8: mov             fp, SP
    // 0x7efacc: AllocStack(0x8)
    //     0x7efacc: sub             SP, SP, #8
    // 0x7efad0: CheckStackOverflow
    //     0x7efad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7efad4: cmp             SP, x16
    //     0x7efad8: b.ls            #0x7efb08
    // 0x7efadc: ldr             x16, [fp, #0x10]
    // 0x7efae0: str             x16, [SP]
    // 0x7efae4: r4 = 0
    //     0x7efae4: movz            x4, #0
    // 0x7efae8: ldr             x0, [SP]
    // 0x7efaec: r16 = UnlinkedCall_0x5f3c08
    //     0x7efaec: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x7efaf0: add             x16, x16, #0x3e8
    // 0x7efaf4: ldp             x5, lr, [x16]
    // 0x7efaf8: blr             lr
    // 0x7efafc: LeaveFrame
    //     0x7efafc: mov             SP, fp
    //     0x7efb00: ldp             fp, lr, [SP], #0x10
    // 0x7efb04: ret
    //     0x7efb04: ret             
    // 0x7efb08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7efb08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7efb0c: b               #0x7efadc
  }
  _ restore(/* No info */) async {
    // ** addr: 0xb092c8, size: 0x78
    // 0xb092c8: EnterFrame
    //     0xb092c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb092cc: mov             fp, SP
    // 0xb092d0: AllocStack(0x10)
    //     0xb092d0: sub             SP, SP, #0x10
    // 0xb092d4: SetupParameters(MenuRepository this /* r1 => r1, fp-0x10 */)
    //     0xb092d4: stur            NULL, [fp, #-8]
    //     0xb092d8: stur            x1, [fp, #-0x10]
    // 0xb092dc: CheckStackOverflow
    //     0xb092dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb092e0: cmp             SP, x16
    //     0xb092e4: b.ls            #0xb09338
    // 0xb092e8: InitAsync() -> Future<void?>
    //     0xb092e8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb092ec: bl              #0x661298  ; InitAsyncStub
    // 0xb092f0: ldur            x0, [fp, #-0x10]
    // 0xb092f4: LoadField: r1 = r0->field_7
    //     0xb092f4: ldur            w1, [x0, #7]
    // 0xb092f8: DecompressPointer r1
    //     0xb092f8: add             x1, x1, HEAP, lsl #32
    // 0xb092fc: r2 = true
    //     0xb092fc: add             x2, NULL, #0x20  ; true
    // 0xb09300: r0 = isDefaultMenu=()
    //     0xb09300: bl              #0xb097d0  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isDefaultMenu=
    // 0xb09304: ldur            x0, [fp, #-0x10]
    // 0xb09308: LoadField: r1 = r0->field_f
    //     0xb09308: ldur            w1, [x0, #0xf]
    // 0xb0930c: DecompressPointer r1
    //     0xb0930c: add             x1, x1, HEAP, lsl #32
    // 0xb09310: r0 = menus()
    //     0xb09310: bl              #0x7ee9f8  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::menus
    // 0xb09314: ldur            x1, [fp, #-0x10]
    // 0xb09318: mov             x2, x0
    // 0xb0931c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb0931c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb09320: r0 = updateAll()
    //     0xb09320: bl              #0xb09340  ; [package:nuonline/app/data/repositories/menu_repository.dart] MenuRepository::updateAll
    // 0xb09324: mov             x1, x0
    // 0xb09328: stur            x1, [fp, #-0x10]
    // 0xb0932c: r0 = Await()
    //     0xb0932c: bl              #0x661044  ; AwaitStub
    // 0xb09330: r0 = Null
    //     0xb09330: mov             x0, NULL
    // 0xb09334: r0 = ReturnAsyncNotFuture()
    //     0xb09334: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb09338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb09338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb0933c: b               #0xb092e8
  }
  _ updateAll(/* No info */) async {
    // ** addr: 0xb09340, size: 0x178
    // 0xb09340: EnterFrame
    //     0xb09340: stp             fp, lr, [SP, #-0x10]!
    //     0xb09344: mov             fp, SP
    // 0xb09348: AllocStack(0x50)
    //     0xb09348: sub             SP, SP, #0x50
    // 0xb0934c: SetupParameters(MenuRepository this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */, {dynamic markAsChanged = false /* r5, fp-0x18 */, dynamic reIndex = false /* r3, fp-0x10 */})
    //     0xb0934c: stur            NULL, [fp, #-8]
    //     0xb09350: stur            x1, [fp, #-0x20]
    //     0xb09354: stur            x2, [fp, #-0x28]
    //     0xb09358: ldur            w0, [x4, #0x13]
    //     0xb0935c: ldur            w3, [x4, #0x1f]
    //     0xb09360: add             x3, x3, HEAP, lsl #32
    //     0xb09364: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d600] "markAsChanged"
    //     0xb09368: ldr             x16, [x16, #0x600]
    //     0xb0936c: cmp             w3, w16
    //     0xb09370: b.ne            #0xb09394
    //     0xb09374: ldur            w3, [x4, #0x23]
    //     0xb09378: add             x3, x3, HEAP, lsl #32
    //     0xb0937c: sub             w5, w0, w3
    //     0xb09380: add             x3, fp, w5, sxtw #2
    //     0xb09384: ldr             x3, [x3, #8]
    //     0xb09388: mov             x5, x3
    //     0xb0938c: movz            x3, #0x1
    //     0xb09390: b               #0xb0939c
    //     0xb09394: add             x5, NULL, #0x30  ; false
    //     0xb09398: movz            x3, #0
    //     0xb0939c: stur            x5, [fp, #-0x18]
    //     0xb093a0: lsl             x6, x3, #1
    //     0xb093a4: lsl             w3, w6, #1
    //     0xb093a8: add             w6, w3, #8
    //     0xb093ac: add             x16, x4, w6, sxtw #1
    //     0xb093b0: ldur            w7, [x16, #0xf]
    //     0xb093b4: add             x7, x7, HEAP, lsl #32
    //     0xb093b8: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d608] "reIndex"
    //     0xb093bc: ldr             x16, [x16, #0x608]
    //     0xb093c0: cmp             w7, w16
    //     0xb093c4: b.ne            #0xb093ec
    //     0xb093c8: add             w6, w3, #0xa
    //     0xb093cc: add             x16, x4, w6, sxtw #1
    //     0xb093d0: ldur            w3, [x16, #0xf]
    //     0xb093d4: add             x3, x3, HEAP, lsl #32
    //     0xb093d8: sub             w4, w0, w3
    //     0xb093dc: add             x0, fp, w4, sxtw #2
    //     0xb093e0: ldr             x0, [x0, #8]
    //     0xb093e4: mov             x3, x0
    //     0xb093e8: b               #0xb093f0
    //     0xb093ec: add             x3, NULL, #0x30  ; false
    //     0xb093f0: stur            x3, [fp, #-0x10]
    // 0xb093f4: CheckStackOverflow
    //     0xb093f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb093f8: cmp             SP, x16
    //     0xb093fc: b.ls            #0xb094b0
    // 0xb09400: InitAsync() -> Future<void?>
    //     0xb09400: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb09404: bl              #0x661298  ; InitAsyncStub
    // 0xb09408: ldur            x0, [fp, #-0x20]
    // 0xb0940c: LoadField: r1 = r0->field_b
    //     0xb0940c: ldur            w1, [x0, #0xb]
    // 0xb09410: DecompressPointer r1
    //     0xb09410: add             x1, x1, HEAP, lsl #32
    // 0xb09414: r0 = menus()
    //     0xb09414: bl              #0x7ec6c8  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::menus
    // 0xb09418: r1 = Function '<anonymous closure>':.
    //     0xb09418: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d610] AnonymousClosure: (0xb095cc), in [package:nuonline/app/data/repositories/menu_repository.dart] MenuRepository::updateAll (0xb09340)
    //     0xb0941c: ldr             x1, [x1, #0x610]
    // 0xb09420: r2 = Null
    //     0xb09420: mov             x2, NULL
    // 0xb09424: stur            x0, [fp, #-0x30]
    // 0xb09428: r0 = AllocateClosure()
    //     0xb09428: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0942c: r16 = <Menu>
    //     0xb0942c: ldr             x16, [PP, #0x7b40]  ; [pp+0x7b40] TypeArguments: <Menu>
    // 0xb09430: ldur            lr, [fp, #-0x28]
    // 0xb09434: stp             lr, x16, [SP, #0x10]
    // 0xb09438: ldur            x16, [fp, #-0x10]
    // 0xb0943c: stp             x0, x16, [SP]
    // 0xb09440: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb09440: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb09444: r0 = ListExtension.optional()
    //     0xb09444: bl              #0xb094b8  ; [package:nuonline/common/extensions/list_extension.dart] ::ListExtension.optional
    // 0xb09448: r1 = Function '<anonymous closure>':.
    //     0xb09448: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d618] AnonymousClosure: (0xb09580), in [package:nuonline/app/data/repositories/menu_repository.dart] MenuRepository::updateAll (0xb09340)
    //     0xb0944c: ldr             x1, [x1, #0x618]
    // 0xb09450: r2 = Null
    //     0xb09450: mov             x2, NULL
    // 0xb09454: stur            x0, [fp, #-0x10]
    // 0xb09458: r0 = AllocateClosure()
    //     0xb09458: bl              #0xec1630  ; AllocateClosureStub
    // 0xb0945c: ldur            x2, [fp, #-0x10]
    // 0xb09460: mov             x3, x0
    // 0xb09464: r1 = <dynamic, Menu>
    //     0xb09464: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d620] TypeArguments: <dynamic, Menu>
    //     0xb09468: ldr             x1, [x1, #0x620]
    // 0xb0946c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb0946c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb09470: r0 = LinkedHashMap.fromIterable()
    //     0xb09470: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0xb09474: ldur            x1, [fp, #-0x30]
    // 0xb09478: mov             x2, x0
    // 0xb0947c: r0 = putAll()
    //     0xb0947c: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xb09480: mov             x1, x0
    // 0xb09484: stur            x1, [fp, #-0x10]
    // 0xb09488: r0 = Await()
    //     0xb09488: bl              #0x661044  ; AwaitStub
    // 0xb0948c: ldur            x0, [fp, #-0x18]
    // 0xb09490: tbnz            w0, #4, #0xb094a8
    // 0xb09494: ldur            x0, [fp, #-0x20]
    // 0xb09498: LoadField: r1 = r0->field_7
    //     0xb09498: ldur            w1, [x0, #7]
    // 0xb0949c: DecompressPointer r1
    //     0xb0949c: add             x1, x1, HEAP, lsl #32
    // 0xb094a0: r2 = false
    //     0xb094a0: add             x2, NULL, #0x30  ; false
    // 0xb094a4: r0 = isDefaultMenu=()
    //     0xb094a4: bl              #0xb097d0  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::isDefaultMenu=
    // 0xb094a8: r0 = Null
    //     0xb094a8: mov             x0, NULL
    // 0xb094ac: r0 = ReturnAsyncNotFuture()
    //     0xb094ac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb094b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb094b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb094b4: b               #0xb09400
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb09580, size: 0x4c
    // 0xb09580: EnterFrame
    //     0xb09580: stp             fp, lr, [SP, #-0x10]!
    //     0xb09584: mov             fp, SP
    // 0xb09588: AllocStack(0x8)
    //     0xb09588: sub             SP, SP, #8
    // 0xb0958c: CheckStackOverflow
    //     0xb0958c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb09590: cmp             SP, x16
    //     0xb09594: b.ls            #0xb095c4
    // 0xb09598: ldr             x16, [fp, #0x10]
    // 0xb0959c: str             x16, [SP]
    // 0xb095a0: r4 = 0
    //     0xb095a0: movz            x4, #0
    // 0xb095a4: ldr             x0, [SP]
    // 0xb095a8: r16 = UnlinkedCall_0x5f3c08
    //     0xb095a8: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d628] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xb095ac: add             x16, x16, #0x628
    // 0xb095b0: ldp             x5, lr, [x16]
    // 0xb095b4: blr             lr
    // 0xb095b8: LeaveFrame
    //     0xb095b8: mov             SP, fp
    //     0xb095bc: ldp             fp, lr, [SP], #0x10
    // 0xb095c0: ret
    //     0xb095c0: ret             
    // 0xb095c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb095c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb095c8: b               #0xb09598
  }
  [closure] List<Menu> <anonymous closure>(dynamic, List<Menu>) {
    // ** addr: 0xb095cc, size: 0x204
    // 0xb095cc: EnterFrame
    //     0xb095cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb095d0: mov             fp, SP
    // 0xb095d4: AllocStack(0x50)
    //     0xb095d4: sub             SP, SP, #0x50
    // 0xb095d8: CheckStackOverflow
    //     0xb095d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb095dc: cmp             SP, x16
    //     0xb095e0: b.ls            #0xb097c0
    // 0xb095e4: r1 = <Menu>
    //     0xb095e4: ldr             x1, [PP, #0x7b40]  ; [pp+0x7b40] TypeArguments: <Menu>
    // 0xb095e8: r2 = 0
    //     0xb095e8: movz            x2, #0
    // 0xb095ec: r0 = _GrowableList()
    //     0xb095ec: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb095f0: mov             x1, x0
    // 0xb095f4: stur            x1, [fp, #-0x10]
    // 0xb095f8: r3 = 0
    //     0xb095f8: movz            x3, #0
    // 0xb095fc: ldr             x2, [fp, #0x10]
    // 0xb09600: stur            x3, [fp, #-8]
    // 0xb09604: CheckStackOverflow
    //     0xb09604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb09608: cmp             SP, x16
    //     0xb0960c: b.ls            #0xb097c8
    // 0xb09610: r0 = LoadClassIdInstr(r2)
    //     0xb09610: ldur            x0, [x2, #-1]
    //     0xb09614: ubfx            x0, x0, #0xc, #0x14
    // 0xb09618: str             x2, [SP]
    // 0xb0961c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb0961c: movz            x17, #0xc834
    //     0xb09620: add             lr, x0, x17
    //     0xb09624: ldr             lr, [x21, lr, lsl #3]
    //     0xb09628: blr             lr
    // 0xb0962c: r1 = LoadInt32Instr(r0)
    //     0xb0962c: sbfx            x1, x0, #1, #0x1f
    //     0xb09630: tbz             w0, #0, #0xb09638
    //     0xb09634: ldur            x1, [x0, #7]
    // 0xb09638: ldur            x2, [fp, #-8]
    // 0xb0963c: cmp             x2, x1
    // 0xb09640: b.ge            #0xb097ac
    // 0xb09644: ldr             x4, [fp, #0x10]
    // 0xb09648: ldur            x3, [fp, #-0x10]
    // 0xb0964c: r0 = BoxInt64Instr(r2)
    //     0xb0964c: sbfiz           x0, x2, #1, #0x1f
    //     0xb09650: cmp             x2, x0, asr #1
    //     0xb09654: b.eq            #0xb09660
    //     0xb09658: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0965c: stur            x2, [x0, #7]
    // 0xb09660: r1 = LoadClassIdInstr(r4)
    //     0xb09660: ldur            x1, [x4, #-1]
    //     0xb09664: ubfx            x1, x1, #0xc, #0x14
    // 0xb09668: stp             x0, x4, [SP]
    // 0xb0966c: mov             x0, x1
    // 0xb09670: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb09670: movz            x17, #0x3037
    //     0xb09674: movk            x17, #0x1, lsl #16
    //     0xb09678: add             lr, x0, x17
    //     0xb0967c: ldr             lr, [x21, lr, lsl #3]
    //     0xb09680: blr             lr
    // 0xb09684: mov             x1, x0
    // 0xb09688: ldur            x0, [fp, #-8]
    // 0xb0968c: add             x3, x0, #1
    // 0xb09690: stur            x3, [fp, #-0x38]
    // 0xb09694: LoadField: r0 = r1->field_1b
    //     0xb09694: ldur            w0, [x1, #0x1b]
    // 0xb09698: DecompressPointer r0
    //     0xb09698: add             x0, x0, HEAP, lsl #32
    // 0xb0969c: stur            x0, [fp, #-0x30]
    // 0xb096a0: LoadField: r2 = r1->field_1f
    //     0xb096a0: ldur            w2, [x1, #0x1f]
    // 0xb096a4: DecompressPointer r2
    //     0xb096a4: add             x2, x2, HEAP, lsl #32
    // 0xb096a8: stur            x2, [fp, #-0x28]
    // 0xb096ac: LoadField: r4 = r1->field_23
    //     0xb096ac: ldur            w4, [x1, #0x23]
    // 0xb096b0: DecompressPointer r4
    //     0xb096b0: add             x4, x4, HEAP, lsl #32
    // 0xb096b4: stur            x4, [fp, #-0x20]
    // 0xb096b8: LoadField: r5 = r1->field_27
    //     0xb096b8: ldur            w5, [x1, #0x27]
    // 0xb096bc: DecompressPointer r5
    //     0xb096bc: add             x5, x5, HEAP, lsl #32
    // 0xb096c0: stur            x5, [fp, #-0x18]
    // 0xb096c4: r0 = Menu()
    //     0xb096c4: bl              #0x7ec8b0  ; AllocateMenuStub -> Menu (size=0x2c)
    // 0xb096c8: ldur            x3, [fp, #-0x38]
    // 0xb096cc: stur            x0, [fp, #-0x40]
    // 0xb096d0: StoreField: r0->field_13 = r3
    //     0xb096d0: stur            x3, [x0, #0x13]
    // 0xb096d4: ldur            x1, [fp, #-0x30]
    // 0xb096d8: StoreField: r0->field_1b = r1
    //     0xb096d8: stur            w1, [x0, #0x1b]
    // 0xb096dc: ldur            x1, [fp, #-0x28]
    // 0xb096e0: StoreField: r0->field_1f = r1
    //     0xb096e0: stur            w1, [x0, #0x1f]
    // 0xb096e4: ldur            x1, [fp, #-0x20]
    // 0xb096e8: StoreField: r0->field_23 = r1
    //     0xb096e8: stur            w1, [x0, #0x23]
    // 0xb096ec: ldur            x1, [fp, #-0x18]
    // 0xb096f0: StoreField: r0->field_27 = r1
    //     0xb096f0: stur            w1, [x0, #0x27]
    // 0xb096f4: r16 = <HiveList<HiveObjectMixin>, int>
    //     0xb096f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9f8] TypeArguments: <HiveList<HiveObjectMixin>, int>
    //     0xb096f8: ldr             x16, [x16, #0x9f8]
    // 0xb096fc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xb09700: stp             lr, x16, [SP]
    // 0xb09704: r0 = Map._fromLiteral()
    //     0xb09704: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb09708: ldur            x2, [fp, #-0x40]
    // 0xb0970c: StoreField: r2->field_f = r0
    //     0xb0970c: stur            w0, [x2, #0xf]
    //     0xb09710: ldurb           w16, [x2, #-1]
    //     0xb09714: ldurb           w17, [x0, #-1]
    //     0xb09718: and             x16, x17, x16, lsr #2
    //     0xb0971c: tst             x16, HEAP, lsr #32
    //     0xb09720: b.eq            #0xb09728
    //     0xb09724: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb09728: ldur            x0, [fp, #-0x10]
    // 0xb0972c: LoadField: r1 = r0->field_b
    //     0xb0972c: ldur            w1, [x0, #0xb]
    // 0xb09730: LoadField: r3 = r0->field_f
    //     0xb09730: ldur            w3, [x0, #0xf]
    // 0xb09734: DecompressPointer r3
    //     0xb09734: add             x3, x3, HEAP, lsl #32
    // 0xb09738: LoadField: r4 = r3->field_b
    //     0xb09738: ldur            w4, [x3, #0xb]
    // 0xb0973c: r3 = LoadInt32Instr(r1)
    //     0xb0973c: sbfx            x3, x1, #1, #0x1f
    // 0xb09740: stur            x3, [fp, #-8]
    // 0xb09744: r1 = LoadInt32Instr(r4)
    //     0xb09744: sbfx            x1, x4, #1, #0x1f
    // 0xb09748: cmp             x3, x1
    // 0xb0974c: b.ne            #0xb09758
    // 0xb09750: mov             x1, x0
    // 0xb09754: r0 = _growToNextCapacity()
    //     0xb09754: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb09758: ldur            x2, [fp, #-0x10]
    // 0xb0975c: ldur            x3, [fp, #-8]
    // 0xb09760: add             x4, x3, #1
    // 0xb09764: lsl             x5, x4, #1
    // 0xb09768: StoreField: r2->field_b = r5
    //     0xb09768: stur            w5, [x2, #0xb]
    // 0xb0976c: LoadField: r1 = r2->field_f
    //     0xb0976c: ldur            w1, [x2, #0xf]
    // 0xb09770: DecompressPointer r1
    //     0xb09770: add             x1, x1, HEAP, lsl #32
    // 0xb09774: ldur            x0, [fp, #-0x40]
    // 0xb09778: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb09778: add             x25, x1, x3, lsl #2
    //     0xb0977c: add             x25, x25, #0xf
    //     0xb09780: str             w0, [x25]
    //     0xb09784: tbz             w0, #0, #0xb097a0
    //     0xb09788: ldurb           w16, [x1, #-1]
    //     0xb0978c: ldurb           w17, [x0, #-1]
    //     0xb09790: and             x16, x17, x16, lsr #2
    //     0xb09794: tst             x16, HEAP, lsr #32
    //     0xb09798: b.eq            #0xb097a0
    //     0xb0979c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb097a0: ldur            x3, [fp, #-0x38]
    // 0xb097a4: mov             x1, x2
    // 0xb097a8: b               #0xb095fc
    // 0xb097ac: ldur            x2, [fp, #-0x10]
    // 0xb097b0: mov             x0, x2
    // 0xb097b4: LeaveFrame
    //     0xb097b4: mov             SP, fp
    //     0xb097b8: ldp             fp, lr, [SP], #0x10
    // 0xb097bc: ret
    //     0xb097bc: ret             
    // 0xb097c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb097c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb097c4: b               #0xb095e4
    // 0xb097c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb097c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb097cc: b               #0xb09610
  }
}
