// lib: , url: package:nuonline/app/data/repositories/notification_repository.dart

// class id: 1050091, size: 0x8
class :: {
}

// class id: 1085, size: 0xc, field offset: 0x8
class NotificationRepository extends Object {

  get _ isPermissionGranted(/* No info */) async {
    // ** addr: 0x8ffc28, size: 0x38
    // 0x8ffc28: EnterFrame
    //     0x8ffc28: stp             fp, lr, [SP, #-0x10]!
    //     0x8ffc2c: mov             fp, SP
    // 0x8ffc30: AllocStack(0x10)
    //     0x8ffc30: sub             SP, SP, #0x10
    // 0x8ffc34: SetupParameters(NotificationRepository this /* r1 => r1, fp-0x10 */)
    //     0x8ffc34: stur            NULL, [fp, #-8]
    //     0x8ffc38: stur            x1, [fp, #-0x10]
    // 0x8ffc3c: CheckStackOverflow
    //     0x8ffc3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ffc40: cmp             SP, x16
    //     0x8ffc44: b.ls            #0x8ffc58
    // 0x8ffc48: InitAsync() -> Future<bool>
    //     0x8ffc48: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x8ffc4c: bl              #0x661298  ; InitAsyncStub
    // 0x8ffc50: r0 = PermissionCheckShortcuts.isGranted()
    //     0x8ffc50: bl              #0x8ffc60  ; [package:permission_handler/permission_handler.dart] ::PermissionCheckShortcuts.isGranted
    // 0x8ffc54: r0 = ReturnAsync()
    //     0x8ffc54: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8ffc58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ffc58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ffc5c: b               #0x8ffc48
  }
  _ requestPermission(/* No info */) async {
    // ** addr: 0x923d34, size: 0xdc
    // 0x923d34: EnterFrame
    //     0x923d34: stp             fp, lr, [SP, #-0x10]!
    //     0x923d38: mov             fp, SP
    // 0x923d3c: AllocStack(0x18)
    //     0x923d3c: sub             SP, SP, #0x18
    // 0x923d40: SetupParameters(NotificationRepository this /* r1 => r1, fp-0x18 */, {dynamic openSetting = false /* r2, fp-0x10 */})
    //     0x923d40: stur            NULL, [fp, #-8]
    //     0x923d44: stur            x1, [fp, #-0x18]
    //     0x923d48: ldur            w0, [x4, #0x13]
    //     0x923d4c: ldur            w2, [x4, #0x1f]
    //     0x923d50: add             x2, x2, HEAP, lsl #32
    //     0x923d54: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c800] "openSetting"
    //     0x923d58: ldr             x16, [x16, #0x800]
    //     0x923d5c: cmp             w2, w16
    //     0x923d60: b.ne            #0x923d80
    //     0x923d64: ldur            w2, [x4, #0x23]
    //     0x923d68: add             x2, x2, HEAP, lsl #32
    //     0x923d6c: sub             w3, w0, w2
    //     0x923d70: add             x0, fp, w3, sxtw #2
    //     0x923d74: ldr             x0, [x0, #8]
    //     0x923d78: mov             x2, x0
    //     0x923d7c: b               #0x923d84
    //     0x923d80: add             x2, NULL, #0x30  ; false
    //     0x923d84: stur            x2, [fp, #-0x10]
    // 0x923d88: CheckStackOverflow
    //     0x923d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923d8c: cmp             SP, x16
    //     0x923d90: b.ls            #0x923e08
    // 0x923d94: InitAsync() -> Future<void?>
    //     0x923d94: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x923d98: bl              #0x661298  ; InitAsyncStub
    // 0x923d9c: ldur            x0, [fp, #-0x18]
    // 0x923da0: LoadField: r1 = r0->field_7
    //     0x923da0: ldur            w1, [x0, #7]
    // 0x923da4: DecompressPointer r1
    //     0x923da4: add             x1, x1, HEAP, lsl #32
    // 0x923da8: r0 = requestAndroidPermission()
    //     0x923da8: bl              #0x923e78  ; [package:nuonline/services/notification_service.dart] NotificationService::requestAndroidPermission
    // 0x923dac: mov             x1, x0
    // 0x923db0: stur            x1, [fp, #-0x18]
    // 0x923db4: r0 = Await()
    //     0x923db4: bl              #0x661044  ; AwaitStub
    // 0x923db8: r0 = PermissionActions.status()
    //     0x923db8: bl              #0x8ffd10  ; [package:permission_handler/permission_handler.dart] ::PermissionActions.status
    // 0x923dbc: mov             x1, x0
    // 0x923dc0: stur            x1, [fp, #-0x18]
    // 0x923dc4: r0 = Await()
    //     0x923dc4: bl              #0x661044  ; AwaitStub
    // 0x923dc8: r16 = Instance_PermissionStatus
    //     0x923dc8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7e8] Obj!PermissionStatus@e2e421
    //     0x923dcc: ldr             x16, [x16, #0x7e8]
    // 0x923dd0: cmp             w0, w16
    // 0x923dd4: b.eq            #0x923df0
    // 0x923dd8: r16 = Instance_PermissionStatus
    //     0x923dd8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c7d0] Obj!PermissionStatus@e2e481
    //     0x923ddc: ldr             x16, [x16, #0x7d0]
    // 0x923de0: cmp             w0, w16
    // 0x923de4: b.ne            #0x923e00
    // 0x923de8: ldur            x0, [fp, #-0x10]
    // 0x923dec: tbnz            w0, #4, #0x923e00
    // 0x923df0: r0 = openAppSettings()
    //     0x923df0: bl              #0x923e10  ; [package:geolocator/geolocator.dart] Geolocator::openAppSettings
    // 0x923df4: mov             x1, x0
    // 0x923df8: stur            x1, [fp, #-0x10]
    // 0x923dfc: r0 = Await()
    //     0x923dfc: bl              #0x661044  ; AwaitStub
    // 0x923e00: r0 = Null
    //     0x923e00: mov             x0, NULL
    // 0x923e04: r0 = ReturnAsyncNotFuture()
    //     0x923e04: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x923e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x923e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923e0c: b               #0x923d94
  }
}
