// lib: , url: package:nuonline/app/data/repositories/page_repository.dart

// class id: 1050092, size: 0x8
class :: {
}

// class id: 1084, size: 0xc, field offset: 0x8
class PageRepository extends Object {

  _ findPageById(/* No info */) async {
    // ** addr: 0x8fd5dc, size: 0x114
    // 0x8fd5dc: EnterFrame
    //     0x8fd5dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd5e0: mov             fp, SP
    // 0x8fd5e4: AllocStack(0x80)
    //     0x8fd5e4: sub             SP, SP, #0x80
    // 0x8fd5e8: SetupParameters(PageRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x8fd5e8: stur            NULL, [fp, #-8]
    //     0x8fd5ec: stur            x1, [fp, #-0x58]
    //     0x8fd5f0: stur            x2, [fp, #-0x60]
    // 0x8fd5f4: CheckStackOverflow
    //     0x8fd5f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd5f8: cmp             SP, x16
    //     0x8fd5fc: b.ls            #0x8fd6e8
    // 0x8fd600: InitAsync() -> Future<ApiResult<Page>>
    //     0x8fd600: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f270] TypeArguments: <ApiResult<Page>>
    //     0x8fd604: ldr             x0, [x0, #0x270]
    //     0x8fd608: bl              #0x661298  ; InitAsyncStub
    // 0x8fd60c: ldur            x1, [fp, #-0x58]
    // 0x8fd610: ldur            x0, [fp, #-0x60]
    // 0x8fd614: LoadField: r3 = r1->field_7
    //     0x8fd614: ldur            w3, [x1, #7]
    // 0x8fd618: DecompressPointer r3
    //     0x8fd618: add             x3, x3, HEAP, lsl #32
    // 0x8fd61c: stur            x3, [fp, #-0x68]
    // 0x8fd620: r1 = Null
    //     0x8fd620: mov             x1, NULL
    // 0x8fd624: r2 = 4
    //     0x8fd624: movz            x2, #0x4
    // 0x8fd628: r0 = AllocateArray()
    //     0x8fd628: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8fd62c: r16 = "/pages/"
    //     0x8fd62c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f278] "/pages/"
    //     0x8fd630: ldr             x16, [x16, #0x278]
    // 0x8fd634: StoreField: r0->field_f = r16
    //     0x8fd634: stur            w16, [x0, #0xf]
    // 0x8fd638: ldur            x1, [fp, #-0x60]
    // 0x8fd63c: StoreField: r0->field_13 = r1
    //     0x8fd63c: stur            w1, [x0, #0x13]
    // 0x8fd640: str             x0, [SP]
    // 0x8fd644: r0 = _interpolate()
    //     0x8fd644: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8fd648: ldur            x16, [fp, #-0x68]
    // 0x8fd64c: stp             x16, NULL, [SP, #8]
    // 0x8fd650: str             x0, [SP]
    // 0x8fd654: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fd654: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fd658: r0 = get()
    //     0x8fd658: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x8fd65c: mov             x1, x0
    // 0x8fd660: stur            x1, [fp, #-0x58]
    // 0x8fd664: r0 = Await()
    //     0x8fd664: bl              #0x661044  ; AwaitStub
    // 0x8fd668: LoadField: r3 = r0->field_b
    //     0x8fd668: ldur            w3, [x0, #0xb]
    // 0x8fd66c: DecompressPointer r3
    //     0x8fd66c: add             x3, x3, HEAP, lsl #32
    // 0x8fd670: mov             x0, x3
    // 0x8fd674: stur            x3, [fp, #-0x58]
    // 0x8fd678: r2 = Null
    //     0x8fd678: mov             x2, NULL
    // 0x8fd67c: r1 = Null
    //     0x8fd67c: mov             x1, NULL
    // 0x8fd680: r8 = Map<String, dynamic>
    //     0x8fd680: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8fd684: r3 = Null
    //     0x8fd684: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f280] Null
    //     0x8fd688: ldr             x3, [x3, #0x280]
    // 0x8fd68c: r0 = Map<String, dynamic>()
    //     0x8fd68c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8fd690: ldur            x2, [fp, #-0x58]
    // 0x8fd694: r1 = Null
    //     0x8fd694: mov             x1, NULL
    // 0x8fd698: r0 = Page.fromMap()
    //     0x8fd698: bl              #0x8fd6f0  ; [package:nuonline/app/data/models/page.dart] Page::Page.fromMap
    // 0x8fd69c: r1 = <Page>
    //     0x8fd69c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d518] TypeArguments: <Page>
    //     0x8fd6a0: ldr             x1, [x1, #0x518]
    // 0x8fd6a4: stur            x0, [fp, #-0x58]
    // 0x8fd6a8: r0 = _$SuccessImpl()
    //     0x8fd6a8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x8fd6ac: mov             x1, x0
    // 0x8fd6b0: ldur            x0, [fp, #-0x58]
    // 0x8fd6b4: StoreField: r1->field_b = r0
    //     0x8fd6b4: stur            w0, [x1, #0xb]
    // 0x8fd6b8: mov             x0, x1
    // 0x8fd6bc: r0 = ReturnAsyncNotFuture()
    //     0x8fd6bc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fd6c0: sub             SP, fp, #0x80
    // 0x8fd6c4: mov             x1, x0
    // 0x8fd6c8: r0 = getDioException()
    //     0x8fd6c8: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x8fd6cc: r1 = <Page>
    //     0x8fd6cc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d518] TypeArguments: <Page>
    //     0x8fd6d0: ldr             x1, [x1, #0x518]
    // 0x8fd6d4: stur            x0, [fp, #-0x58]
    // 0x8fd6d8: r0 = _$FailureImpl()
    //     0x8fd6d8: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x8fd6dc: ldur            x1, [fp, #-0x58]
    // 0x8fd6e0: StoreField: r0->field_b = r1
    //     0x8fd6e0: stur            w1, [x0, #0xb]
    // 0x8fd6e4: r0 = ReturnAsyncNotFuture()
    //     0x8fd6e4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8fd6e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd6e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd6ec: b               #0x8fd600
  }
}
