// lib: , url: package:nuonline/app/data/repositories/prayer_time_repository.dart

// class id: 1050093, size: 0x8
class :: {

  static _ PrayerTimeSettingExtension.params(/* No info */) {
    // ** addr: 0x82fd44, size: 0x278
    // 0x82fd44: EnterFrame
    //     0x82fd44: stp             fp, lr, [SP, #-0x10]!
    //     0x82fd48: mov             fp, SP
    // 0x82fd4c: AllocStack(0x40)
    //     0x82fd4c: sub             SP, SP, #0x40
    // 0x82fd50: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x82fd50: mov             x0, x1
    //     0x82fd54: stur            x1, [fp, #-8]
    // 0x82fd58: CheckStackOverflow
    //     0x82fd58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82fd5c: cmp             SP, x16
    //     0x82fd60: b.ls            #0x82ff94
    // 0x82fd64: mov             x1, x0
    // 0x82fd68: r0 = method()
    //     0x82fd68: bl              #0x830fcc  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::method
    // 0x82fd6c: mov             x1, x0
    // 0x82fd70: r0 = CalculationMethodExtensions.getParameters()
    //     0x82fd70: bl              #0x830510  ; [package:adhan/src/calculation_method.dart] ::CalculationMethodExtensions.getParameters
    // 0x82fd74: mov             x2, x0
    // 0x82fd78: ldur            x0, [fp, #-8]
    // 0x82fd7c: stur            x2, [fp, #-0x18]
    // 0x82fd80: LoadField: r1 = r0->field_33
    //     0x82fd80: ldur            x1, [x0, #0x33]
    // 0x82fd84: tbz             x1, #0x3f, #0x82fd90
    // 0x82fd88: r3 = 0
    //     0x82fd88: movz            x3, #0
    // 0x82fd8c: b               #0x82fd94
    // 0x82fd90: mov             x3, x1
    // 0x82fd94: stur            x3, [fp, #-0x10]
    // 0x82fd98: LoadField: r1 = r0->field_13
    //     0x82fd98: ldur            w1, [x0, #0x13]
    // 0x82fd9c: DecompressPointer r1
    //     0x82fd9c: add             x1, x1, HEAP, lsl #32
    // 0x82fda0: tbz             w1, #4, #0x82ff28
    // 0x82fda4: mov             x1, x0
    // 0x82fda8: r0 = allowCustomAngle()
    //     0x82fda8: bl              #0x8304bc  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::allowCustomAngle
    // 0x82fdac: tbnz            w0, #4, #0x82fde8
    // 0x82fdb0: ldur            x2, [fp, #-8]
    // 0x82fdb4: ldur            x3, [fp, #-0x18]
    // 0x82fdb8: LoadField: d0 = r2->field_1f
    //     0x82fdb8: ldur            d0, [x2, #0x1f]
    // 0x82fdbc: StoreField: r3->field_b = d0
    //     0x82fdbc: stur            d0, [x3, #0xb]
    // 0x82fdc0: LoadField: r0 = r2->field_27
    //     0x82fdc0: ldur            w0, [x2, #0x27]
    // 0x82fdc4: DecompressPointer r0
    //     0x82fdc4: add             x0, x0, HEAP, lsl #32
    // 0x82fdc8: ArrayStore: r3[0] = r0  ; List_4
    //     0x82fdc8: stur            w0, [x3, #0x17]
    //     0x82fdcc: ldurb           w16, [x3, #-1]
    //     0x82fdd0: ldurb           w17, [x0, #-1]
    //     0x82fdd4: and             x16, x17, x16, lsr #2
    //     0x82fdd8: tst             x16, HEAP, lsr #32
    //     0x82fddc: b.eq            #0x82fde4
    //     0x82fde0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x82fde4: b               #0x82fdf0
    // 0x82fde8: ldur            x2, [fp, #-8]
    // 0x82fdec: ldur            x3, [fp, #-0x18]
    // 0x82fdf0: mov             x1, x2
    // 0x82fdf4: r0 = allowCustomIshaInterval()
    //     0x82fdf4: bl              #0x830150  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::allowCustomIshaInterval
    // 0x82fdf8: tbnz            w0, #4, #0x82fe10
    // 0x82fdfc: ldur            x1, [fp, #-8]
    // 0x82fe00: ldur            x0, [fp, #-0x18]
    // 0x82fe04: LoadField: r2 = r1->field_2b
    //     0x82fe04: ldur            x2, [x1, #0x2b]
    // 0x82fe08: StoreField: r0->field_1b = r2
    //     0x82fe08: stur            x2, [x0, #0x1b]
    // 0x82fe0c: b               #0x82fe18
    // 0x82fe10: ldur            x1, [fp, #-8]
    // 0x82fe14: ldur            x0, [fp, #-0x18]
    // 0x82fe18: LoadField: r2 = r1->field_63
    //     0x82fe18: ldur            x2, [x1, #0x63]
    // 0x82fe1c: stur            x2, [fp, #-0x40]
    // 0x82fe20: LoadField: r3 = r1->field_43
    //     0x82fe20: ldur            x3, [x1, #0x43]
    // 0x82fe24: stur            x3, [fp, #-0x38]
    // 0x82fe28: LoadField: r4 = r1->field_4b
    //     0x82fe28: ldur            x4, [x1, #0x4b]
    // 0x82fe2c: stur            x4, [fp, #-0x30]
    // 0x82fe30: LoadField: r5 = r1->field_53
    //     0x82fe30: ldur            x5, [x1, #0x53]
    // 0x82fe34: stur            x5, [fp, #-0x28]
    // 0x82fe38: LoadField: r6 = r1->field_5b
    //     0x82fe38: ldur            x6, [x1, #0x5b]
    // 0x82fe3c: stur            x6, [fp, #-0x20]
    // 0x82fe40: r0 = PrayerAdjustments()
    //     0x82fe40: bl              #0x830144  ; AllocatePrayerAdjustmentsStub -> PrayerAdjustments (size=0x38)
    // 0x82fe44: mov             x1, x0
    // 0x82fe48: ldur            x0, [fp, #-0x40]
    // 0x82fe4c: StoreField: r1->field_7 = r0
    //     0x82fe4c: stur            x0, [x1, #7]
    // 0x82fe50: StoreField: r1->field_f = rZR
    //     0x82fe50: stur            xzr, [x1, #0xf]
    // 0x82fe54: ldur            x0, [fp, #-0x38]
    // 0x82fe58: ArrayStore: r1[0] = r0  ; List_8
    //     0x82fe58: stur            x0, [x1, #0x17]
    // 0x82fe5c: ldur            x0, [fp, #-0x30]
    // 0x82fe60: StoreField: r1->field_1f = r0
    //     0x82fe60: stur            x0, [x1, #0x1f]
    // 0x82fe64: ldur            x0, [fp, #-0x28]
    // 0x82fe68: StoreField: r1->field_27 = r0
    //     0x82fe68: stur            x0, [x1, #0x27]
    // 0x82fe6c: ldur            x0, [fp, #-0x20]
    // 0x82fe70: StoreField: r1->field_2f = r0
    //     0x82fe70: stur            x0, [x1, #0x2f]
    // 0x82fe74: mov             x0, x1
    // 0x82fe78: ldur            x2, [fp, #-0x18]
    // 0x82fe7c: StoreField: r2->field_2f = r0
    //     0x82fe7c: stur            w0, [x2, #0x2f]
    //     0x82fe80: ldurb           w16, [x2, #-1]
    //     0x82fe84: ldurb           w17, [x0, #-1]
    //     0x82fe88: and             x16, x17, x16, lsr #2
    //     0x82fe8c: tst             x16, HEAP, lsr #32
    //     0x82fe90: b.eq            #0x82fe98
    //     0x82fe94: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x82fe98: ldur            x0, [fp, #-8]
    // 0x82fe9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x82fe9c: ldur            w1, [x0, #0x17]
    // 0x82fea0: DecompressPointer r1
    //     0x82fea0: add             x1, x1, HEAP, lsl #32
    // 0x82fea4: tbnz            w1, #4, #0x82fef8
    // 0x82fea8: ldur            x1, [fp, #-0x10]
    // 0x82feac: r0 = PrayerTimeSettingExtension.findMaghribAngleWithAltitude()
    //     0x82feac: bl              #0x830018  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.findMaghribAngleWithAltitude
    // 0x82feb0: r0 = inline_Allocate_Double()
    //     0x82feb0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x82feb4: add             x0, x0, #0x10
    //     0x82feb8: cmp             x1, x0
    //     0x82febc: b.ls            #0x82ff9c
    //     0x82fec0: str             x0, [THR, #0x50]  ; THR::top
    //     0x82fec4: sub             x0, x0, #0xf
    //     0x82fec8: movz            x1, #0xe15c
    //     0x82fecc: movk            x1, #0x3, lsl #16
    //     0x82fed0: stur            x1, [x0, #-1]
    // 0x82fed4: StoreField: r0->field_7 = d0
    //     0x82fed4: stur            d0, [x0, #7]
    // 0x82fed8: ldur            x2, [fp, #-0x18]
    // 0x82fedc: StoreField: r2->field_13 = r0
    //     0x82fedc: stur            w0, [x2, #0x13]
    //     0x82fee0: ldurb           w16, [x2, #-1]
    //     0x82fee4: ldurb           w17, [x0, #-1]
    //     0x82fee8: and             x16, x17, x16, lsr #2
    //     0x82feec: tst             x16, HEAP, lsr #32
    //     0x82fef0: b.eq            #0x82fef8
    //     0x82fef4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x82fef8: ldur            x1, [fp, #-8]
    // 0x82fefc: r0 = madhab()
    //     0x82fefc: bl              #0x82ffbc  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::madhab
    // 0x82ff00: ldur            x2, [fp, #-0x18]
    // 0x82ff04: StoreField: r2->field_23 = r0
    //     0x82ff04: stur            w0, [x2, #0x23]
    //     0x82ff08: ldurb           w16, [x2, #-1]
    //     0x82ff0c: ldurb           w17, [x0, #-1]
    //     0x82ff10: and             x16, x17, x16, lsr #2
    //     0x82ff14: tst             x16, HEAP, lsr #32
    //     0x82ff18: b.eq            #0x82ff20
    //     0x82ff1c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x82ff20: mov             x1, x2
    // 0x82ff24: b               #0x82ff84
    // 0x82ff28: mov             x1, x3
    // 0x82ff2c: tbnz            x1, #0x3f, #0x82ff80
    // 0x82ff30: r0 = PrayerTimeSettingExtension.findMaghribAngleWithAltitude()
    //     0x82ff30: bl              #0x830018  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.findMaghribAngleWithAltitude
    // 0x82ff34: r0 = inline_Allocate_Double()
    //     0x82ff34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x82ff38: add             x0, x0, #0x10
    //     0x82ff3c: cmp             x1, x0
    //     0x82ff40: b.ls            #0x82ffac
    //     0x82ff44: str             x0, [THR, #0x50]  ; THR::top
    //     0x82ff48: sub             x0, x0, #0xf
    //     0x82ff4c: movz            x1, #0xe15c
    //     0x82ff50: movk            x1, #0x3, lsl #16
    //     0x82ff54: stur            x1, [x0, #-1]
    // 0x82ff58: StoreField: r0->field_7 = d0
    //     0x82ff58: stur            d0, [x0, #7]
    // 0x82ff5c: ldur            x1, [fp, #-0x18]
    // 0x82ff60: StoreField: r1->field_13 = r0
    //     0x82ff60: stur            w0, [x1, #0x13]
    //     0x82ff64: ldurb           w16, [x1, #-1]
    //     0x82ff68: ldurb           w17, [x0, #-1]
    //     0x82ff6c: and             x16, x17, x16, lsr #2
    //     0x82ff70: tst             x16, HEAP, lsr #32
    //     0x82ff74: b.eq            #0x82ff7c
    //     0x82ff78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x82ff7c: b               #0x82ff84
    // 0x82ff80: mov             x1, x2
    // 0x82ff84: mov             x0, x1
    // 0x82ff88: LeaveFrame
    //     0x82ff88: mov             SP, fp
    //     0x82ff8c: ldp             fp, lr, [SP], #0x10
    // 0x82ff90: ret
    //     0x82ff90: ret             
    // 0x82ff94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82ff94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82ff98: b               #0x82fd64
    // 0x82ff9c: SaveReg d0
    //     0x82ff9c: str             q0, [SP, #-0x10]!
    // 0x82ffa0: r0 = AllocateDouble()
    //     0x82ffa0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x82ffa4: RestoreReg d0
    //     0x82ffa4: ldr             q0, [SP], #0x10
    // 0x82ffa8: b               #0x82fed4
    // 0x82ffac: SaveReg d0
    //     0x82ffac: str             q0, [SP, #-0x10]!
    // 0x82ffb0: r0 = AllocateDouble()
    //     0x82ffb0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x82ffb4: RestoreReg d0
    //     0x82ffb4: ldr             q0, [SP], #0x10
    // 0x82ffb8: b               #0x82ff58
  }
  static _ PrayerTimeSettingExtension.findMaghribAngleWithAltitude(/* No info */) {
    // ** addr: 0x830018, size: 0x12c
    // 0x830018: EnterFrame
    //     0x830018: stp             fp, lr, [SP, #-0x10]!
    //     0x83001c: mov             fp, SP
    // 0x830020: AllocStack(0x10)
    //     0x830020: sub             SP, SP, #0x10
    // 0x830024: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0x830024: mov             x2, x1
    // 0x830028: CheckStackOverflow
    //     0x830028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83002c: cmp             SP, x16
    //     0x830030: b.ls            #0x830120
    // 0x830034: r0 = BoxInt64Instr(r2)
    //     0x830034: sbfiz           x0, x2, #1, #0x1f
    //     0x830038: cmp             x2, x0, asr #1
    //     0x83003c: b.eq            #0x830048
    //     0x830040: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x830044: stur            x2, [x0, #7]
    // 0x830048: r1 = 60
    //     0x830048: movz            x1, #0x3c
    // 0x83004c: branchIfSmi(r0, 0x830058)
    //     0x83004c: tbz             w0, #0, #0x830058
    // 0x830050: r1 = LoadClassIdInstr(r0)
    //     0x830050: ldur            x1, [x0, #-1]
    //     0x830054: ubfx            x1, x1, #0xc, #0x14
    // 0x830058: str             x0, [SP]
    // 0x83005c: mov             x0, x1
    // 0x830060: r0 = GDT[cid_x0 + -0xffa]()
    //     0x830060: sub             lr, x0, #0xffa
    //     0x830064: ldr             lr, [x21, lr, lsl #3]
    //     0x830068: blr             lr
    // 0x83006c: LoadField: d0 = r0->field_7
    //     0x83006c: ldur            d0, [x0, #7]
    // 0x830070: fsqrt           d1, d0
    // 0x830074: d0 = 1.760000
    //     0x830074: add             x17, PP, #0xb, lsl #12  ; [pp+0xb8e0] IMM: double(1.76) from 0x3ffc28f5c28f5c29
    //     0x830078: ldr             d0, [x17, #0x8e0]
    // 0x83007c: fmul            d2, d1, d0
    // 0x830080: d0 = 50.000000
    //     0x830080: ldr             d0, [PP, #0x5ac0]  ; [pp+0x5ac0] IMM: double(50) from 0x4049000000000000
    // 0x830084: fadd            d1, d2, d0
    // 0x830088: stur            d1, [fp, #-8]
    // 0x83008c: d2 = 60.000000
    //     0x83008c: ldr             d2, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0x830090: fdiv            d0, d1, d2
    // 0x830094: stp             fp, lr, [SP, #-0x10]!
    // 0x830098: mov             fp, SP
    // 0x83009c: CallRuntime_LibcRound(double) -> double
    //     0x83009c: and             SP, SP, #0xfffffffffffffff0
    //     0x8300a0: mov             sp, SP
    //     0x8300a4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x8300a8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8300ac: blr             x16
    //     0x8300b0: movz            x16, #0x8
    //     0x8300b4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x8300b8: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x8300bc: sub             sp, x16, #1, lsl #12
    //     0x8300c0: mov             SP, fp
    //     0x8300c4: ldp             fp, lr, [SP], #0x10
    // 0x8300c8: fcmp            d0, d0
    // 0x8300cc: b.vs            #0x830128
    // 0x8300d0: fcvtzs          x0, d0
    // 0x8300d4: asr             x16, x0, #0x1e
    // 0x8300d8: cmp             x16, x0, asr #63
    // 0x8300dc: b.ne            #0x830128
    // 0x8300e0: lsl             x0, x0, #1
    // 0x8300e4: r1 = LoadInt32Instr(r0)
    //     0x8300e4: sbfx            x1, x0, #1, #0x1f
    //     0x8300e8: tbz             w0, #0, #0x8300f0
    //     0x8300ec: ldur            x1, [x0, #7]
    // 0x8300f0: r16 = 60
    //     0x8300f0: movz            x16, #0x3c
    // 0x8300f4: mul             x0, x1, x16
    // 0x8300f8: scvtf           d1, x0
    // 0x8300fc: ldur            d2, [fp, #-8]
    // 0x830100: fsub            d3, d2, d1
    // 0x830104: d1 = 60.000000
    //     0x830104: ldr             d1, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0x830108: fdiv            d2, d3, d1
    // 0x83010c: scvtf           d1, x1
    // 0x830110: fadd            d0, d1, d2
    // 0x830114: LeaveFrame
    //     0x830114: mov             SP, fp
    //     0x830118: ldp             fp, lr, [SP], #0x10
    // 0x83011c: ret
    //     0x83011c: ret             
    // 0x830120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x830120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x830124: b               #0x830034
    // 0x830128: SaveReg d0
    //     0x830128: str             q0, [SP, #-0x10]!
    // 0x83012c: r0 = 74
    //     0x83012c: movz            x0, #0x4a
    // 0x830130: r30 = DoubleToIntegerStub
    //     0x830130: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x830134: LoadField: r30 = r30->field_7
    //     0x830134: ldur            lr, [lr, #7]
    // 0x830138: blr             lr
    // 0x83013c: RestoreReg d0
    //     0x83013c: ldr             q0, [SP], #0x10
    // 0x830140: b               #0x8300e4
  }
}

// class id: 1083, size: 0x10, field offset: 0x8
class PrayerTimeRepository extends Object {

  static _ getRangedPrayerTime(/* No info */) async {
    // ** addr: 0x82aac0, size: 0x278
    // 0x82aac0: EnterFrame
    //     0x82aac0: stp             fp, lr, [SP, #-0x10]!
    //     0x82aac4: mov             fp, SP
    // 0x82aac8: AllocStack(0x50)
    //     0x82aac8: sub             SP, SP, #0x50
    // 0x82aacc: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x82aacc: stur            NULL, [fp, #-8]
    //     0x82aad0: stur            x1, [fp, #-0x10]
    //     0x82aad4: mov             x16, x3
    //     0x82aad8: mov             x3, x1
    //     0x82aadc: mov             x1, x16
    //     0x82aae0: stur            x2, [fp, #-0x18]
    //     0x82aae4: stur            x1, [fp, #-0x20]
    // 0x82aae8: CheckStackOverflow
    //     0x82aae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82aaec: cmp             SP, x16
    //     0x82aaf0: b.ls            #0x82ad1c
    // 0x82aaf4: InitAsync() -> Future<List<PrayerTimes>>
    //     0x82aaf4: add             x0, PP, #0xb, lsl #12  ; [pp+0xb638] TypeArguments: <List<PrayerTimes>>
    //     0x82aaf8: ldr             x0, [x0, #0x638]
    //     0x82aafc: bl              #0x661298  ; InitAsyncStub
    // 0x82ab00: r1 = <PrayerTimes>
    //     0x82ab00: add             x1, PP, #0xb, lsl #12  ; [pp+0xb640] TypeArguments: <PrayerTimes>
    //     0x82ab04: ldr             x1, [x1, #0x640]
    // 0x82ab08: r2 = 0
    //     0x82ab08: movz            x2, #0
    // 0x82ab0c: r0 = _GrowableList()
    //     0x82ab0c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x82ab10: stur            x0, [fp, #-0x30]
    // 0x82ab14: r2 = 0
    //     0x82ab14: movz            x2, #0
    // 0x82ab18: ldur            x1, [fp, #-0x18]
    // 0x82ab1c: stur            x2, [fp, #-0x28]
    // 0x82ab20: CheckStackOverflow
    //     0x82ab20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82ab24: cmp             SP, x16
    //     0x82ab28: b.ls            #0x82ad24
    // 0x82ab2c: cmp             x2, x1
    // 0x82ab30: b.ge            #0x82ad10
    // 0x82ab34: r0 = _getCurrentMicros()
    //     0x82ab34: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x82ab38: r1 = LoadInt32Instr(r0)
    //     0x82ab38: sbfx            x1, x0, #1, #0x1f
    //     0x82ab3c: tbz             w0, #0, #0x82ab44
    //     0x82ab40: ldur            x1, [x0, #7]
    // 0x82ab44: ldur            x0, [fp, #-0x28]
    // 0x82ab48: r16 = 86400000000
    //     0x82ab48: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x82ab4c: ldr             x16, [x16, #0x268]
    // 0x82ab50: mul             x2, x0, x16
    // 0x82ab54: add             x3, x1, x2
    // 0x82ab58: stur            x3, [fp, #-0x38]
    // 0x82ab5c: r0 = DateTime()
    //     0x82ab5c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x82ab60: mov             x1, x0
    // 0x82ab64: ldur            x2, [fp, #-0x38]
    // 0x82ab68: r3 = false
    //     0x82ab68: add             x3, NULL, #0x30  ; false
    // 0x82ab6c: stur            x0, [fp, #-0x40]
    // 0x82ab70: r0 = DateTime._withValue()
    //     0x82ab70: bl              #0x6fe64c  ; [dart:core] DateTime::DateTime._withValue
    // 0x82ab74: ldur            x1, [fp, #-0x40]
    // 0x82ab78: r0 = _parts()
    //     0x82ab78: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82ab7c: mov             x2, x0
    // 0x82ab80: LoadField: r0 = r2->field_b
    //     0x82ab80: ldur            w0, [x2, #0xb]
    // 0x82ab84: r1 = LoadInt32Instr(r0)
    //     0x82ab84: sbfx            x1, x0, #1, #0x1f
    // 0x82ab88: mov             x0, x1
    // 0x82ab8c: r1 = 8
    //     0x82ab8c: movz            x1, #0x8
    // 0x82ab90: cmp             x1, x0
    // 0x82ab94: b.hs            #0x82ad2c
    // 0x82ab98: LoadField: r0 = r2->field_2f
    //     0x82ab98: ldur            w0, [x2, #0x2f]
    // 0x82ab9c: DecompressPointer r0
    //     0x82ab9c: add             x0, x0, HEAP, lsl #32
    // 0x82aba0: ldur            x1, [fp, #-0x40]
    // 0x82aba4: stur            x0, [fp, #-0x48]
    // 0x82aba8: r0 = _parts()
    //     0x82aba8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82abac: mov             x2, x0
    // 0x82abb0: LoadField: r0 = r2->field_b
    //     0x82abb0: ldur            w0, [x2, #0xb]
    // 0x82abb4: r1 = LoadInt32Instr(r0)
    //     0x82abb4: sbfx            x1, x0, #1, #0x1f
    // 0x82abb8: mov             x0, x1
    // 0x82abbc: r1 = 7
    //     0x82abbc: movz            x1, #0x7
    // 0x82abc0: cmp             x1, x0
    // 0x82abc4: b.hs            #0x82ad30
    // 0x82abc8: LoadField: r0 = r2->field_2b
    //     0x82abc8: ldur            w0, [x2, #0x2b]
    // 0x82abcc: DecompressPointer r0
    //     0x82abcc: add             x0, x0, HEAP, lsl #32
    // 0x82abd0: ldur            x1, [fp, #-0x40]
    // 0x82abd4: stur            x0, [fp, #-0x50]
    // 0x82abd8: r0 = _parts()
    //     0x82abd8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x82abdc: mov             x2, x0
    // 0x82abe0: LoadField: r0 = r2->field_b
    //     0x82abe0: ldur            w0, [x2, #0xb]
    // 0x82abe4: r1 = LoadInt32Instr(r0)
    //     0x82abe4: sbfx            x1, x0, #1, #0x1f
    // 0x82abe8: mov             x0, x1
    // 0x82abec: r1 = 5
    //     0x82abec: movz            x1, #0x5
    // 0x82abf0: cmp             x1, x0
    // 0x82abf4: b.hs            #0x82ad34
    // 0x82abf8: LoadField: r0 = r2->field_23
    //     0x82abf8: ldur            w0, [x2, #0x23]
    // 0x82abfc: DecompressPointer r0
    //     0x82abfc: add             x0, x0, HEAP, lsl #32
    // 0x82ac00: ldur            x1, [fp, #-0x48]
    // 0x82ac04: stur            x0, [fp, #-0x40]
    // 0x82ac08: r2 = LoadInt32Instr(r1)
    //     0x82ac08: sbfx            x2, x1, #1, #0x1f
    //     0x82ac0c: tbz             w1, #0, #0x82ac14
    //     0x82ac10: ldur            x2, [x1, #7]
    // 0x82ac14: stur            x2, [fp, #-0x38]
    // 0x82ac18: r0 = DateComponents()
    //     0x82ac18: bl              #0x831004  ; AllocateDateComponentsStub -> DateComponents (size=0x20)
    // 0x82ac1c: mov             x2, x0
    // 0x82ac20: ldur            x0, [fp, #-0x38]
    // 0x82ac24: stur            x2, [fp, #-0x48]
    // 0x82ac28: StoreField: r2->field_7 = r0
    //     0x82ac28: stur            x0, [x2, #7]
    // 0x82ac2c: ldur            x0, [fp, #-0x50]
    // 0x82ac30: r1 = LoadInt32Instr(r0)
    //     0x82ac30: sbfx            x1, x0, #1, #0x1f
    //     0x82ac34: tbz             w0, #0, #0x82ac3c
    //     0x82ac38: ldur            x1, [x0, #7]
    // 0x82ac3c: StoreField: r2->field_f = r1
    //     0x82ac3c: stur            x1, [x2, #0xf]
    // 0x82ac40: ldur            x0, [fp, #-0x40]
    // 0x82ac44: r1 = LoadInt32Instr(r0)
    //     0x82ac44: sbfx            x1, x0, #1, #0x1f
    //     0x82ac48: tbz             w0, #0, #0x82ac50
    //     0x82ac4c: ldur            x1, [x0, #7]
    // 0x82ac50: ArrayStore: r2[0] = r1  ; List_8
    //     0x82ac50: stur            x1, [x2, #0x17]
    // 0x82ac54: ldur            x1, [fp, #-0x20]
    // 0x82ac58: r0 = PrayerTimeSettingExtension.params()
    //     0x82ac58: bl              #0x82fd44  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.params
    // 0x82ac5c: ldur            x1, [fp, #-0x48]
    // 0x82ac60: stur            x0, [fp, #-0x40]
    // 0x82ac64: r0 = resolveTimeByDateComponents()
    //     0x82ac64: bl              #0x82fc6c  ; [package:adhan/src/data/calendar_util.dart] CalendarUtil::resolveTimeByDateComponents
    // 0x82ac68: stur            x0, [fp, #-0x48]
    // 0x82ac6c: r0 = PrayerTimes()
    //     0x82ac6c: bl              #0x82fc60  ; AllocatePrayerTimesStub -> PrayerTimes (size=0x30)
    // 0x82ac70: mov             x1, x0
    // 0x82ac74: ldur            x2, [fp, #-0x10]
    // 0x82ac78: ldur            x3, [fp, #-0x48]
    // 0x82ac7c: ldur            x5, [fp, #-0x40]
    // 0x82ac80: stur            x0, [fp, #-0x40]
    // 0x82ac84: r0 = PrayerTimes._()
    //     0x82ac84: bl              #0x82ae4c  ; [package:adhan/src/prayer_times.dart] PrayerTimes::PrayerTimes._
    // 0x82ac88: ldur            x0, [fp, #-0x30]
    // 0x82ac8c: LoadField: r1 = r0->field_b
    //     0x82ac8c: ldur            w1, [x0, #0xb]
    // 0x82ac90: LoadField: r2 = r0->field_f
    //     0x82ac90: ldur            w2, [x0, #0xf]
    // 0x82ac94: DecompressPointer r2
    //     0x82ac94: add             x2, x2, HEAP, lsl #32
    // 0x82ac98: LoadField: r3 = r2->field_b
    //     0x82ac98: ldur            w3, [x2, #0xb]
    // 0x82ac9c: r2 = LoadInt32Instr(r1)
    //     0x82ac9c: sbfx            x2, x1, #1, #0x1f
    // 0x82aca0: stur            x2, [fp, #-0x38]
    // 0x82aca4: r1 = LoadInt32Instr(r3)
    //     0x82aca4: sbfx            x1, x3, #1, #0x1f
    // 0x82aca8: cmp             x2, x1
    // 0x82acac: b.ne            #0x82acb8
    // 0x82acb0: mov             x1, x0
    // 0x82acb4: r0 = _growToNextCapacity()
    //     0x82acb4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x82acb8: ldur            x3, [fp, #-0x30]
    // 0x82acbc: ldur            x4, [fp, #-0x28]
    // 0x82acc0: ldur            x2, [fp, #-0x38]
    // 0x82acc4: add             x5, x2, #1
    // 0x82acc8: lsl             x6, x5, #1
    // 0x82accc: StoreField: r3->field_b = r6
    //     0x82accc: stur            w6, [x3, #0xb]
    // 0x82acd0: LoadField: r1 = r3->field_f
    //     0x82acd0: ldur            w1, [x3, #0xf]
    // 0x82acd4: DecompressPointer r1
    //     0x82acd4: add             x1, x1, HEAP, lsl #32
    // 0x82acd8: ldur            x0, [fp, #-0x40]
    // 0x82acdc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x82acdc: add             x25, x1, x2, lsl #2
    //     0x82ace0: add             x25, x25, #0xf
    //     0x82ace4: str             w0, [x25]
    //     0x82ace8: tbz             w0, #0, #0x82ad04
    //     0x82acec: ldurb           w16, [x1, #-1]
    //     0x82acf0: ldurb           w17, [x0, #-1]
    //     0x82acf4: and             x16, x17, x16, lsr #2
    //     0x82acf8: tst             x16, HEAP, lsr #32
    //     0x82acfc: b.eq            #0x82ad04
    //     0x82ad00: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x82ad04: add             x2, x4, #1
    // 0x82ad08: mov             x0, x3
    // 0x82ad0c: b               #0x82ab18
    // 0x82ad10: mov             x3, x0
    // 0x82ad14: mov             x0, x3
    // 0x82ad18: r0 = ReturnAsyncNotFuture()
    //     0x82ad18: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x82ad1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82ad1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82ad20: b               #0x82aaf4
    // 0x82ad24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82ad24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82ad28: b               #0x82ab2c
    // 0x82ad2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82ad2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82ad30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82ad30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x82ad34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x82ad34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getPrayerTime(/* No info */) async {
    // ** addr: 0x8a6860, size: 0x8c
    // 0x8a6860: EnterFrame
    //     0x8a6860: stp             fp, lr, [SP, #-0x10]!
    //     0x8a6864: mov             fp, SP
    // 0x8a6868: AllocStack(0x20)
    //     0x8a6868: sub             SP, SP, #0x20
    // 0x8a686c: SetupParameters(PrayerTimeRepository this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x8a686c: stur            NULL, [fp, #-8]
    //     0x8a6870: stur            x1, [fp, #-0x10]
    //     0x8a6874: mov             x16, x3
    //     0x8a6878: mov             x3, x1
    //     0x8a687c: mov             x1, x16
    //     0x8a6880: stur            x2, [fp, #-0x18]
    //     0x8a6884: stur            x1, [fp, #-0x20]
    // 0x8a6888: CheckStackOverflow
    //     0x8a6888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a688c: cmp             SP, x16
    //     0x8a6890: b.ls            #0x8a68e4
    // 0x8a6894: InitAsync() -> Future<PrayerTimes>
    //     0x8a6894: add             x0, PP, #0xb, lsl #12  ; [pp+0xb640] TypeArguments: <PrayerTimes>
    //     0x8a6898: ldr             x0, [x0, #0x640]
    //     0x8a689c: bl              #0x661298  ; InitAsyncStub
    // 0x8a68a0: ldur            x1, [fp, #-0x10]
    // 0x8a68a4: r0 = getSetting()
    //     0x8a68a4: bl              #0x8a68ec  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::getSetting
    // 0x8a68a8: mov             x1, x0
    // 0x8a68ac: stur            x1, [fp, #-0x10]
    // 0x8a68b0: r0 = Await()
    //     0x8a68b0: bl              #0x661044  ; AwaitStub
    // 0x8a68b4: ldur            x1, [fp, #-0x20]
    // 0x8a68b8: stur            x0, [fp, #-0x10]
    // 0x8a68bc: r0 = from()
    //     0x8a68bc: bl              #0x82ad9c  ; [package:adhan/src/data/date_components.dart] DateComponents::from
    // 0x8a68c0: ldur            x1, [fp, #-0x10]
    // 0x8a68c4: stur            x0, [fp, #-0x10]
    // 0x8a68c8: r0 = PrayerTimeSettingExtension.params()
    //     0x8a68c8: bl              #0x82fd44  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.params
    // 0x8a68cc: ldur            x2, [fp, #-0x18]
    // 0x8a68d0: ldur            x3, [fp, #-0x10]
    // 0x8a68d4: mov             x5, x0
    // 0x8a68d8: r1 = Null
    //     0x8a68d8: mov             x1, NULL
    // 0x8a68dc: r0 = PrayerTimes()
    //     0x8a68dc: bl              #0x82ad38  ; [package:adhan/src/prayer_times.dart] PrayerTimes::PrayerTimes
    // 0x8a68e0: r0 = ReturnAsyncNotFuture()
    //     0x8a68e0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a68e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a68e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a68e8: b               #0x8a6894
  }
  _ getSetting(/* No info */) async {
    // ** addr: 0x8a68ec, size: 0x8c
    // 0x8a68ec: EnterFrame
    //     0x8a68ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8a68f0: mov             fp, SP
    // 0x8a68f4: AllocStack(0x20)
    //     0x8a68f4: sub             SP, SP, #0x20
    // 0x8a68f8: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x10 */)
    //     0x8a68f8: stur            NULL, [fp, #-8]
    //     0x8a68fc: stur            x1, [fp, #-0x10]
    // 0x8a6900: CheckStackOverflow
    //     0x8a6900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a6904: cmp             SP, x16
    //     0x8a6908: b.ls            #0x8a696c
    // 0x8a690c: InitAsync() -> Future<PrayerTimeSetting>
    //     0x8a690c: add             x0, PP, #8, lsl #12  ; [pp+0x8048] TypeArguments: <PrayerTimeSetting>
    //     0x8a6910: ldr             x0, [x0, #0x48]
    //     0x8a6914: bl              #0x661298  ; InitAsyncStub
    // 0x8a6918: ldur            x0, [fp, #-0x10]
    // 0x8a691c: LoadField: r2 = r0->field_7
    //     0x8a691c: ldur            w2, [x0, #7]
    // 0x8a6920: DecompressPointer r2
    //     0x8a6920: add             x2, x2, HEAP, lsl #32
    // 0x8a6924: mov             x1, x2
    // 0x8a6928: stur            x2, [fp, #-0x18]
    // 0x8a692c: r0 = setting()
    //     0x8a692c: bl              #0x83121c  ; [package:nuonline/services/storage_service/prayer_time_storage.dart] PrayerTimeStorage::setting
    // 0x8a6930: cmp             w0, NULL
    // 0x8a6934: b.ne            #0x8a6958
    // 0x8a6938: ldur            x1, [fp, #-0x10]
    // 0x8a693c: r0 = defaultSetting()
    //     0x8a693c: bl              #0x8a6a0c  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::defaultSetting
    // 0x8a6940: ldur            x1, [fp, #-0x10]
    // 0x8a6944: mov             x2, x0
    // 0x8a6948: r0 = updateSetting()
    //     0x8a6948: bl              #0x8a6978  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::updateSetting
    // 0x8a694c: mov             x1, x0
    // 0x8a6950: stur            x1, [fp, #-0x20]
    // 0x8a6954: r0 = Await()
    //     0x8a6954: bl              #0x661044  ; AwaitStub
    // 0x8a6958: ldur            x1, [fp, #-0x18]
    // 0x8a695c: r0 = setting()
    //     0x8a695c: bl              #0x83121c  ; [package:nuonline/services/storage_service/prayer_time_storage.dart] PrayerTimeStorage::setting
    // 0x8a6960: cmp             w0, NULL
    // 0x8a6964: b.eq            #0x8a6974
    // 0x8a6968: r0 = ReturnAsyncNotFuture()
    //     0x8a6968: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a696c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a696c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a6970: b               #0x8a690c
    // 0x8a6974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8a6974: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ updateSetting(/* No info */) async {
    // ** addr: 0x8a6978, size: 0x94
    // 0x8a6978: EnterFrame
    //     0x8a6978: stp             fp, lr, [SP, #-0x10]!
    //     0x8a697c: mov             fp, SP
    // 0x8a6980: AllocStack(0x30)
    //     0x8a6980: sub             SP, SP, #0x30
    // 0x8a6984: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x8a6984: stur            NULL, [fp, #-8]
    //     0x8a6988: mov             x3, x2
    //     0x8a698c: stur            x1, [fp, #-0x10]
    //     0x8a6990: stur            x2, [fp, #-0x18]
    // 0x8a6994: CheckStackOverflow
    //     0x8a6994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a6998: cmp             SP, x16
    //     0x8a699c: b.ls            #0x8a6a04
    // 0x8a69a0: InitAsync() -> Future<void?>
    //     0x8a69a0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8a69a4: bl              #0x661298  ; InitAsyncStub
    // 0x8a69a8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8a69a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8a69ac: ldr             x0, [x0, #0x2728]
    //     0x8a69b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8a69b4: cmp             w0, w16
    //     0x8a69b8: b.ne            #0x8a69c4
    //     0x8a69bc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8a69c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8a69c4: stp             x0, NULL, [SP, #8]
    // 0x8a69c8: r16 = "v2_prayer_time"
    //     0x8a69c8: add             x16, PP, #8, lsl #12  ; [pp+0x8060] "v2_prayer_time"
    //     0x8a69cc: ldr             x16, [x16, #0x60]
    // 0x8a69d0: str             x16, [SP]
    // 0x8a69d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a69d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a69d8: r0 = box()
    //     0x8a69d8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8a69dc: mov             x1, x0
    // 0x8a69e0: ldur            x3, [fp, #-0x18]
    // 0x8a69e4: r2 = "setting"
    //     0x8a69e4: add             x2, PP, #8, lsl #12  ; [pp+0x8070] "setting"
    //     0x8a69e8: ldr             x2, [x2, #0x70]
    // 0x8a69ec: r0 = put()
    //     0x8a69ec: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0x8a69f0: mov             x1, x0
    // 0x8a69f4: stur            x1, [fp, #-0x10]
    // 0x8a69f8: r0 = Await()
    //     0x8a69f8: bl              #0x661044  ; AwaitStub
    // 0x8a69fc: r0 = Null
    //     0x8a69fc: mov             x0, NULL
    // 0x8a6a00: r0 = ReturnAsyncNotFuture()
    //     0x8a6a00: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a6a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a6a04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a6a08: b               #0x8a69a0
  }
  get _ defaultSetting(/* No info */) {
    // ** addr: 0x8a6a0c, size: 0x74
    // 0x8a6a0c: EnterFrame
    //     0x8a6a0c: stp             fp, lr, [SP, #-0x10]!
    //     0x8a6a10: mov             fp, SP
    // 0x8a6a14: CheckStackOverflow
    //     0x8a6a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a6a18: cmp             SP, x16
    //     0x8a6a1c: b.ls            #0x8a6a78
    // 0x8a6a20: LoadField: r0 = r1->field_b
    //     0x8a6a20: ldur            w0, [x1, #0xb]
    // 0x8a6a24: DecompressPointer r0
    //     0x8a6a24: add             x0, x0, HEAP, lsl #32
    // 0x8a6a28: mov             x1, x0
    // 0x8a6a2c: r0 = current()
    //     0x8a6a2c: bl              #0x8312cc  ; [package:nuonline/services/storage_service/location_storage.dart] LocationStorage::current
    // 0x8a6a30: cmp             w0, NULL
    // 0x8a6a34: b.ne            #0x8a6a40
    // 0x8a6a38: r0 = Null
    //     0x8a6a38: mov             x0, NULL
    // 0x8a6a3c: b               #0x8a6a4c
    // 0x8a6a40: LoadField: r1 = r0->field_13
    //     0x8a6a40: ldur            w1, [x0, #0x13]
    // 0x8a6a44: DecompressPointer r1
    //     0x8a6a44: add             x1, x1, HEAP, lsl #32
    // 0x8a6a48: mov             x0, x1
    // 0x8a6a4c: cmp             w0, NULL
    // 0x8a6a50: b.ne            #0x8a6a60
    // 0x8a6a54: r2 = "ID"
    //     0x8a6a54: add             x2, PP, #8, lsl #12  ; [pp+0x8f40] "ID"
    //     0x8a6a58: ldr             x2, [x2, #0xf40]
    // 0x8a6a5c: b               #0x8a6a64
    // 0x8a6a60: mov             x2, x0
    // 0x8a6a64: r1 = Null
    //     0x8a6a64: mov             x1, NULL
    // 0x8a6a68: r0 = PrayerTimeSetting.fromCountry()
    //     0x8a6a68: bl              #0x8a6a80  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromCountry
    // 0x8a6a6c: LeaveFrame
    //     0x8a6a6c: mov             SP, fp
    //     0x8a6a70: ldp             fp, lr, [SP], #0x10
    // 0x8a6a74: ret
    //     0x8a6a74: ret             
    // 0x8a6a78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a6a78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a6a7c: b               #0x8a6a20
  }
  _ getSettingStream(/* No info */) {
    // ** addr: 0x8a7790, size: 0xf4
    // 0x8a7790: EnterFrame
    //     0x8a7790: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7794: mov             fp, SP
    // 0x8a7798: AllocStack(0x30)
    //     0x8a7798: sub             SP, SP, #0x30
    // 0x8a779c: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x10 */)
    //     0x8a779c: stur            x1, [fp, #-0x10]
    // 0x8a77a0: CheckStackOverflow
    //     0x8a77a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a77a4: cmp             SP, x16
    //     0x8a77a8: b.ls            #0x8a787c
    // 0x8a77ac: LoadField: r0 = r1->field_7
    //     0x8a77ac: ldur            w0, [x1, #7]
    // 0x8a77b0: DecompressPointer r0
    //     0x8a77b0: add             x0, x0, HEAP, lsl #32
    // 0x8a77b4: stur            x0, [fp, #-8]
    // 0x8a77b8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8a77b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8a77bc: ldr             x0, [x0, #0x2728]
    //     0x8a77c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8a77c4: cmp             w0, w16
    //     0x8a77c8: b.ne            #0x8a77d4
    //     0x8a77cc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8a77d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8a77d4: stp             x0, NULL, [SP, #8]
    // 0x8a77d8: r16 = "v2_prayer_time"
    //     0x8a77d8: add             x16, PP, #8, lsl #12  ; [pp+0x8060] "v2_prayer_time"
    //     0x8a77dc: ldr             x16, [x16, #0x60]
    // 0x8a77e0: str             x16, [SP]
    // 0x8a77e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a77e4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a77e8: r0 = box()
    //     0x8a77e8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8a77ec: r16 = "setting"
    //     0x8a77ec: add             x16, PP, #8, lsl #12  ; [pp+0x8070] "setting"
    //     0x8a77f0: ldr             x16, [x16, #0x70]
    // 0x8a77f4: str             x16, [SP]
    // 0x8a77f8: mov             x1, x0
    // 0x8a77fc: r4 = const [0, 0x2, 0x1, 0x1, key, 0x1, null]
    //     0x8a77fc: add             x4, PP, #8, lsl #12  ; [pp+0x8078] List(7) [0, 0x2, 0x1, 0x1, "key", 0x1, Null]
    //     0x8a7800: ldr             x4, [x4, #0x78]
    // 0x8a7804: r0 = watch()
    //     0x8a7804: bl              #0x836368  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::watch
    // 0x8a7808: r1 = Function '<anonymous closure>':.
    //     0x8a7808: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3cf20] AnonymousClosure: (0x8a7920), in [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::getSettingStream (0x8a7790)
    //     0x8a780c: ldr             x1, [x1, #0xf20]
    // 0x8a7810: r2 = Null
    //     0x8a7810: mov             x2, NULL
    // 0x8a7814: stur            x0, [fp, #-0x18]
    // 0x8a7818: r0 = AllocateClosure()
    //     0x8a7818: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a781c: r16 = <PrayerTimeSetting>
    //     0x8a781c: add             x16, PP, #8, lsl #12  ; [pp+0x8048] TypeArguments: <PrayerTimeSetting>
    //     0x8a7820: ldr             x16, [x16, #0x48]
    // 0x8a7824: ldur            lr, [fp, #-0x18]
    // 0x8a7828: stp             lr, x16, [SP, #8]
    // 0x8a782c: str             x0, [SP]
    // 0x8a7830: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7830: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7834: r0 = map()
    //     0x8a7834: bl              #0x68d014  ; [dart:async] Stream::map
    // 0x8a7838: ldur            x1, [fp, #-8]
    // 0x8a783c: stur            x0, [fp, #-8]
    // 0x8a7840: r0 = setting()
    //     0x8a7840: bl              #0x83121c  ; [package:nuonline/services/storage_service/prayer_time_storage.dart] PrayerTimeStorage::setting
    // 0x8a7844: cmp             w0, NULL
    // 0x8a7848: b.ne            #0x8a7854
    // 0x8a784c: ldur            x1, [fp, #-0x10]
    // 0x8a7850: r0 = defaultSetting()
    //     0x8a7850: bl              #0x8a6a0c  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::defaultSetting
    // 0x8a7854: r16 = <PrayerTimeSetting>
    //     0x8a7854: add             x16, PP, #8, lsl #12  ; [pp+0x8048] TypeArguments: <PrayerTimeSetting>
    //     0x8a7858: ldr             x16, [x16, #0x48]
    // 0x8a785c: ldur            lr, [fp, #-8]
    // 0x8a7860: stp             lr, x16, [SP, #8]
    // 0x8a7864: str             x0, [SP]
    // 0x8a7868: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7868: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a786c: r0 = StartWithExtension.startWith()
    //     0x8a786c: bl              #0x8a7884  ; [package:rxdart/src/transformers/start_with.dart] ::StartWithExtension.startWith
    // 0x8a7870: LeaveFrame
    //     0x8a7870: mov             SP, fp
    //     0x8a7874: ldp             fp, lr, [SP], #0x10
    // 0x8a7878: ret
    //     0x8a7878: ret             
    // 0x8a787c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a787c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a7880: b               #0x8a77ac
  }
  [closure] PrayerTimeSetting <anonymous closure>(dynamic, BoxEvent) {
    // ** addr: 0x8a7920, size: 0x64
    // 0x8a7920: EnterFrame
    //     0x8a7920: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7924: mov             fp, SP
    // 0x8a7928: AllocStack(0x8)
    //     0x8a7928: sub             SP, SP, #8
    // 0x8a792c: ldr             x0, [fp, #0x10]
    // 0x8a7930: LoadField: r3 = r0->field_b
    //     0x8a7930: ldur            w3, [x0, #0xb]
    // 0x8a7934: DecompressPointer r3
    //     0x8a7934: add             x3, x3, HEAP, lsl #32
    // 0x8a7938: mov             x0, x3
    // 0x8a793c: stur            x3, [fp, #-8]
    // 0x8a7940: r2 = Null
    //     0x8a7940: mov             x2, NULL
    // 0x8a7944: r1 = Null
    //     0x8a7944: mov             x1, NULL
    // 0x8a7948: r4 = 60
    //     0x8a7948: movz            x4, #0x3c
    // 0x8a794c: branchIfSmi(r0, 0x8a7958)
    //     0x8a794c: tbz             w0, #0, #0x8a7958
    // 0x8a7950: r4 = LoadClassIdInstr(r0)
    //     0x8a7950: ldur            x4, [x0, #-1]
    //     0x8a7954: ubfx            x4, x4, #0xc, #0x14
    // 0x8a7958: cmp             x4, #0x643
    // 0x8a795c: b.eq            #0x8a7974
    // 0x8a7960: r8 = PrayerTimeSetting
    //     0x8a7960: add             x8, PP, #8, lsl #12  ; [pp+0x80a8] Type: PrayerTimeSetting
    //     0x8a7964: ldr             x8, [x8, #0xa8]
    // 0x8a7968: r3 = Null
    //     0x8a7968: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3cf28] Null
    //     0x8a796c: ldr             x3, [x3, #0xf28]
    // 0x8a7970: r0 = PrayerTimeSetting()
    //     0x8a7970: bl              #0x817acc  ; IsType_PrayerTimeSetting_Stub
    // 0x8a7974: ldur            x0, [fp, #-8]
    // 0x8a7978: LeaveFrame
    //     0x8a7978: mov             SP, fp
    //     0x8a797c: ldp             fp, lr, [SP], #0x10
    // 0x8a7980: ret
    //     0x8a7980: ret             
  }
  _ getNotificationSettingStream(/* No info */) {
    // ** addr: 0x8a7b40, size: 0xac
    // 0x8a7b40: EnterFrame
    //     0x8a7b40: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7b44: mov             fp, SP
    // 0x8a7b48: AllocStack(0x20)
    //     0x8a7b48: sub             SP, SP, #0x20
    // 0x8a7b4c: CheckStackOverflow
    //     0x8a7b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a7b50: cmp             SP, x16
    //     0x8a7b54: b.ls            #0x8a7be4
    // 0x8a7b58: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8a7b58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8a7b5c: ldr             x0, [x0, #0x2728]
    //     0x8a7b60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8a7b64: cmp             w0, w16
    //     0x8a7b68: b.ne            #0x8a7b74
    //     0x8a7b6c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8a7b70: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8a7b74: stp             x0, NULL, [SP, #8]
    // 0x8a7b78: r16 = "v2_prayer_time_notification"
    //     0x8a7b78: add             x16, PP, #8, lsl #12  ; [pp+0x8068] "v2_prayer_time_notification"
    //     0x8a7b7c: ldr             x16, [x16, #0x68]
    // 0x8a7b80: str             x16, [SP]
    // 0x8a7b84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7b84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7b88: r0 = box()
    //     0x8a7b88: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8a7b8c: r16 = "setting"
    //     0x8a7b8c: add             x16, PP, #8, lsl #12  ; [pp+0x8070] "setting"
    //     0x8a7b90: ldr             x16, [x16, #0x70]
    // 0x8a7b94: str             x16, [SP]
    // 0x8a7b98: mov             x1, x0
    // 0x8a7b9c: r4 = const [0, 0x2, 0x1, 0x1, key, 0x1, null]
    //     0x8a7b9c: add             x4, PP, #8, lsl #12  ; [pp+0x8078] List(7) [0, 0x2, 0x1, 0x1, "key", 0x1, Null]
    //     0x8a7ba0: ldr             x4, [x4, #0x78]
    // 0x8a7ba4: r0 = watch()
    //     0x8a7ba4: bl              #0x836368  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::watch
    // 0x8a7ba8: r1 = Function '<anonymous closure>':.
    //     0x8a7ba8: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3ce68] AnonymousClosure: (0x8a7bec), in [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::getNotificationSettingStream (0x8a7b40)
    //     0x8a7bac: ldr             x1, [x1, #0xe68]
    // 0x8a7bb0: r2 = Null
    //     0x8a7bb0: mov             x2, NULL
    // 0x8a7bb4: stur            x0, [fp, #-8]
    // 0x8a7bb8: r0 = AllocateClosure()
    //     0x8a7bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a7bbc: r16 = <PrayerTimeNotificationSetting>
    //     0x8a7bbc: add             x16, PP, #8, lsl #12  ; [pp+0x8050] TypeArguments: <PrayerTimeNotificationSetting>
    //     0x8a7bc0: ldr             x16, [x16, #0x50]
    // 0x8a7bc4: ldur            lr, [fp, #-8]
    // 0x8a7bc8: stp             lr, x16, [SP, #8]
    // 0x8a7bcc: str             x0, [SP]
    // 0x8a7bd0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a7bd0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a7bd4: r0 = map()
    //     0x8a7bd4: bl              #0x68d014  ; [dart:async] Stream::map
    // 0x8a7bd8: LeaveFrame
    //     0x8a7bd8: mov             SP, fp
    //     0x8a7bdc: ldp             fp, lr, [SP], #0x10
    // 0x8a7be0: ret
    //     0x8a7be0: ret             
    // 0x8a7be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a7be4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a7be8: b               #0x8a7b58
  }
  [closure] PrayerTimeNotificationSetting <anonymous closure>(dynamic, BoxEvent) {
    // ** addr: 0x8a7bec, size: 0x64
    // 0x8a7bec: EnterFrame
    //     0x8a7bec: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7bf0: mov             fp, SP
    // 0x8a7bf4: AllocStack(0x8)
    //     0x8a7bf4: sub             SP, SP, #8
    // 0x8a7bf8: ldr             x0, [fp, #0x10]
    // 0x8a7bfc: LoadField: r3 = r0->field_b
    //     0x8a7bfc: ldur            w3, [x0, #0xb]
    // 0x8a7c00: DecompressPointer r3
    //     0x8a7c00: add             x3, x3, HEAP, lsl #32
    // 0x8a7c04: mov             x0, x3
    // 0x8a7c08: stur            x3, [fp, #-8]
    // 0x8a7c0c: r2 = Null
    //     0x8a7c0c: mov             x2, NULL
    // 0x8a7c10: r1 = Null
    //     0x8a7c10: mov             x1, NULL
    // 0x8a7c14: r4 = 60
    //     0x8a7c14: movz            x4, #0x3c
    // 0x8a7c18: branchIfSmi(r0, 0x8a7c24)
    //     0x8a7c18: tbz             w0, #0, #0x8a7c24
    // 0x8a7c1c: r4 = LoadClassIdInstr(r0)
    //     0x8a7c1c: ldur            x4, [x0, #-1]
    //     0x8a7c20: ubfx            x4, x4, #0xc, #0x14
    // 0x8a7c24: cmp             x4, #0x644
    // 0x8a7c28: b.eq            #0x8a7c40
    // 0x8a7c2c: r8 = PrayerTimeNotificationSetting
    //     0x8a7c2c: add             x8, PP, #8, lsl #12  ; [pp+0x8090] Type: PrayerTimeNotificationSetting
    //     0x8a7c30: ldr             x8, [x8, #0x90]
    // 0x8a7c34: r3 = Null
    //     0x8a7c34: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3ce70] Null
    //     0x8a7c38: ldr             x3, [x3, #0xe70]
    // 0x8a7c3c: r0 = PrayerTimeNotificationSetting()
    //     0x8a7c3c: bl              #0x817a2c  ; IsType_PrayerTimeNotificationSetting_Stub
    // 0x8a7c40: ldur            x0, [fp, #-8]
    // 0x8a7c44: LeaveFrame
    //     0x8a7c44: mov             SP, fp
    //     0x8a7c48: ldp             fp, lr, [SP], #0x10
    // 0x8a7c4c: ret
    //     0x8a7c4c: ret             
  }
  _ patchSetting(/* No info */) async {
    // ** addr: 0x8a7ef8, size: 0x798
    // 0x8a7ef8: EnterFrame
    //     0x8a7ef8: stp             fp, lr, [SP, #-0x10]!
    //     0x8a7efc: mov             fp, SP
    // 0x8a7f00: AllocStack(0xe8)
    //     0x8a7f00: sub             SP, SP, #0xe8
    // 0x8a7f04: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x78 */, {dynamic altitude = Null /* r3, fp-0x70 */, dynamic angleCorrection = Null /* r5, fp-0x68 */, dynamic asrAdjustment = Null /* r6, fp-0x60 */, dynamic auto = Null /* r7, fp-0x58 */, dynamic dhuhrAdjustment = Null /* r8, fp-0x50 */, dynamic fajrAdjustment = Null /* r9, fp-0x48 */, dynamic fajrAngle = Null /* r10, fp-0x40 */, dynamic ishaAdjustment = Null /* r11, fp-0x38 */, dynamic ishaAngle = Null /* r12, fp-0x30 */, dynamic ishaInterval = Null /* r13, fp-0x28 */, dynamic madhabName = Null /* r14, fp-0x20 */, dynamic maghribAdjustment = Null /* r19, fp-0x18 */, dynamic methodName = Null /* r2, fp-0x10 */})
    //     0x8a7f04: stur            NULL, [fp, #-8]
    //     0x8a7f08: stur            x1, [fp, #-0x78]
    //     0x8a7f0c: ldur            w0, [x4, #0x13]
    //     0x8a7f10: ldur            w2, [x4, #0x1f]
    //     0x8a7f14: add             x2, x2, HEAP, lsl #32
    //     0x8a7f18: add             x16, PP, #0xf, lsl #12  ; [pp+0xf548] "altitude"
    //     0x8a7f1c: ldr             x16, [x16, #0x548]
    //     0x8a7f20: cmp             w2, w16
    //     0x8a7f24: b.ne            #0x8a7f48
    //     0x8a7f28: ldur            w2, [x4, #0x23]
    //     0x8a7f2c: add             x2, x2, HEAP, lsl #32
    //     0x8a7f30: sub             w3, w0, w2
    //     0x8a7f34: add             x2, fp, w3, sxtw #2
    //     0x8a7f38: ldr             x2, [x2, #8]
    //     0x8a7f3c: mov             x3, x2
    //     0x8a7f40: movz            x2, #0x1
    //     0x8a7f44: b               #0x8a7f50
    //     0x8a7f48: mov             x3, NULL
    //     0x8a7f4c: movz            x2, #0
    //     0x8a7f50: stur            x3, [fp, #-0x70]
    //     0x8a7f54: lsl             x5, x2, #1
    //     0x8a7f58: lsl             w6, w5, #1
    //     0x8a7f5c: add             w7, w6, #8
    //     0x8a7f60: add             x16, x4, w7, sxtw #1
    //     0x8a7f64: ldur            w8, [x16, #0xf]
    //     0x8a7f68: add             x8, x8, HEAP, lsl #32
    //     0x8a7f6c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2b0] "angleCorrection"
    //     0x8a7f70: ldr             x16, [x16, #0x2b0]
    //     0x8a7f74: cmp             w8, w16
    //     0x8a7f78: b.ne            #0x8a7fac
    //     0x8a7f7c: add             w2, w6, #0xa
    //     0x8a7f80: add             x16, x4, w2, sxtw #1
    //     0x8a7f84: ldur            w6, [x16, #0xf]
    //     0x8a7f88: add             x6, x6, HEAP, lsl #32
    //     0x8a7f8c: sub             w2, w0, w6
    //     0x8a7f90: add             x6, fp, w2, sxtw #2
    //     0x8a7f94: ldr             x6, [x6, #8]
    //     0x8a7f98: add             w2, w5, #2
    //     0x8a7f9c: sbfx            x5, x2, #1, #0x1f
    //     0x8a7fa0: mov             x2, x5
    //     0x8a7fa4: mov             x5, x6
    //     0x8a7fa8: b               #0x8a7fb0
    //     0x8a7fac: mov             x5, NULL
    //     0x8a7fb0: stur            x5, [fp, #-0x68]
    //     0x8a7fb4: lsl             x6, x2, #1
    //     0x8a7fb8: lsl             w7, w6, #1
    //     0x8a7fbc: add             w8, w7, #8
    //     0x8a7fc0: add             x16, x4, w8, sxtw #1
    //     0x8a7fc4: ldur            w9, [x16, #0xf]
    //     0x8a7fc8: add             x9, x9, HEAP, lsl #32
    //     0x8a7fcc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2b8] "asrAdjustment"
    //     0x8a7fd0: ldr             x16, [x16, #0x2b8]
    //     0x8a7fd4: cmp             w9, w16
    //     0x8a7fd8: b.ne            #0x8a800c
    //     0x8a7fdc: add             w2, w7, #0xa
    //     0x8a7fe0: add             x16, x4, w2, sxtw #1
    //     0x8a7fe4: ldur            w7, [x16, #0xf]
    //     0x8a7fe8: add             x7, x7, HEAP, lsl #32
    //     0x8a7fec: sub             w2, w0, w7
    //     0x8a7ff0: add             x7, fp, w2, sxtw #2
    //     0x8a7ff4: ldr             x7, [x7, #8]
    //     0x8a7ff8: add             w2, w6, #2
    //     0x8a7ffc: sbfx            x6, x2, #1, #0x1f
    //     0x8a8000: mov             x2, x6
    //     0x8a8004: mov             x6, x7
    //     0x8a8008: b               #0x8a8010
    //     0x8a800c: mov             x6, NULL
    //     0x8a8010: stur            x6, [fp, #-0x60]
    //     0x8a8014: lsl             x7, x2, #1
    //     0x8a8018: lsl             w8, w7, #1
    //     0x8a801c: add             w9, w8, #8
    //     0x8a8020: add             x16, x4, w9, sxtw #1
    //     0x8a8024: ldur            w10, [x16, #0xf]
    //     0x8a8028: add             x10, x10, HEAP, lsl #32
    //     0x8a802c: add             x16, PP, #8, lsl #12  ; [pp+0x8f38] "auto"
    //     0x8a8030: ldr             x16, [x16, #0xf38]
    //     0x8a8034: cmp             w10, w16
    //     0x8a8038: b.ne            #0x8a806c
    //     0x8a803c: add             w2, w8, #0xa
    //     0x8a8040: add             x16, x4, w2, sxtw #1
    //     0x8a8044: ldur            w8, [x16, #0xf]
    //     0x8a8048: add             x8, x8, HEAP, lsl #32
    //     0x8a804c: sub             w2, w0, w8
    //     0x8a8050: add             x8, fp, w2, sxtw #2
    //     0x8a8054: ldr             x8, [x8, #8]
    //     0x8a8058: add             w2, w7, #2
    //     0x8a805c: sbfx            x7, x2, #1, #0x1f
    //     0x8a8060: mov             x2, x7
    //     0x8a8064: mov             x7, x8
    //     0x8a8068: b               #0x8a8070
    //     0x8a806c: mov             x7, NULL
    //     0x8a8070: stur            x7, [fp, #-0x58]
    //     0x8a8074: lsl             x8, x2, #1
    //     0x8a8078: lsl             w9, w8, #1
    //     0x8a807c: add             w10, w9, #8
    //     0x8a8080: add             x16, x4, w10, sxtw #1
    //     0x8a8084: ldur            w11, [x16, #0xf]
    //     0x8a8088: add             x11, x11, HEAP, lsl #32
    //     0x8a808c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2c0] "dhuhrAdjustment"
    //     0x8a8090: ldr             x16, [x16, #0x2c0]
    //     0x8a8094: cmp             w11, w16
    //     0x8a8098: b.ne            #0x8a80cc
    //     0x8a809c: add             w2, w9, #0xa
    //     0x8a80a0: add             x16, x4, w2, sxtw #1
    //     0x8a80a4: ldur            w9, [x16, #0xf]
    //     0x8a80a8: add             x9, x9, HEAP, lsl #32
    //     0x8a80ac: sub             w2, w0, w9
    //     0x8a80b0: add             x9, fp, w2, sxtw #2
    //     0x8a80b4: ldr             x9, [x9, #8]
    //     0x8a80b8: add             w2, w8, #2
    //     0x8a80bc: sbfx            x8, x2, #1, #0x1f
    //     0x8a80c0: mov             x2, x8
    //     0x8a80c4: mov             x8, x9
    //     0x8a80c8: b               #0x8a80d0
    //     0x8a80cc: mov             x8, NULL
    //     0x8a80d0: stur            x8, [fp, #-0x50]
    //     0x8a80d4: lsl             x9, x2, #1
    //     0x8a80d8: lsl             w10, w9, #1
    //     0x8a80dc: add             w11, w10, #8
    //     0x8a80e0: add             x16, x4, w11, sxtw #1
    //     0x8a80e4: ldur            w12, [x16, #0xf]
    //     0x8a80e8: add             x12, x12, HEAP, lsl #32
    //     0x8a80ec: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2c8] "fajrAdjustment"
    //     0x8a80f0: ldr             x16, [x16, #0x2c8]
    //     0x8a80f4: cmp             w12, w16
    //     0x8a80f8: b.ne            #0x8a812c
    //     0x8a80fc: add             w2, w10, #0xa
    //     0x8a8100: add             x16, x4, w2, sxtw #1
    //     0x8a8104: ldur            w10, [x16, #0xf]
    //     0x8a8108: add             x10, x10, HEAP, lsl #32
    //     0x8a810c: sub             w2, w0, w10
    //     0x8a8110: add             x10, fp, w2, sxtw #2
    //     0x8a8114: ldr             x10, [x10, #8]
    //     0x8a8118: add             w2, w9, #2
    //     0x8a811c: sbfx            x9, x2, #1, #0x1f
    //     0x8a8120: mov             x2, x9
    //     0x8a8124: mov             x9, x10
    //     0x8a8128: b               #0x8a8130
    //     0x8a812c: mov             x9, NULL
    //     0x8a8130: stur            x9, [fp, #-0x48]
    //     0x8a8134: lsl             x10, x2, #1
    //     0x8a8138: lsl             w11, w10, #1
    //     0x8a813c: add             w12, w11, #8
    //     0x8a8140: add             x16, x4, w12, sxtw #1
    //     0x8a8144: ldur            w13, [x16, #0xf]
    //     0x8a8148: add             x13, x13, HEAP, lsl #32
    //     0x8a814c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2d0] "fajrAngle"
    //     0x8a8150: ldr             x16, [x16, #0x2d0]
    //     0x8a8154: cmp             w13, w16
    //     0x8a8158: b.ne            #0x8a818c
    //     0x8a815c: add             w2, w11, #0xa
    //     0x8a8160: add             x16, x4, w2, sxtw #1
    //     0x8a8164: ldur            w11, [x16, #0xf]
    //     0x8a8168: add             x11, x11, HEAP, lsl #32
    //     0x8a816c: sub             w2, w0, w11
    //     0x8a8170: add             x11, fp, w2, sxtw #2
    //     0x8a8174: ldr             x11, [x11, #8]
    //     0x8a8178: add             w2, w10, #2
    //     0x8a817c: sbfx            x10, x2, #1, #0x1f
    //     0x8a8180: mov             x2, x10
    //     0x8a8184: mov             x10, x11
    //     0x8a8188: b               #0x8a8190
    //     0x8a818c: mov             x10, NULL
    //     0x8a8190: stur            x10, [fp, #-0x40]
    //     0x8a8194: lsl             x11, x2, #1
    //     0x8a8198: lsl             w12, w11, #1
    //     0x8a819c: add             w13, w12, #8
    //     0x8a81a0: add             x16, x4, w13, sxtw #1
    //     0x8a81a4: ldur            w14, [x16, #0xf]
    //     0x8a81a8: add             x14, x14, HEAP, lsl #32
    //     0x8a81ac: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2e0] "ishaAdjustment"
    //     0x8a81b0: ldr             x16, [x16, #0x2e0]
    //     0x8a81b4: cmp             w14, w16
    //     0x8a81b8: b.ne            #0x8a81ec
    //     0x8a81bc: add             w2, w12, #0xa
    //     0x8a81c0: add             x16, x4, w2, sxtw #1
    //     0x8a81c4: ldur            w12, [x16, #0xf]
    //     0x8a81c8: add             x12, x12, HEAP, lsl #32
    //     0x8a81cc: sub             w2, w0, w12
    //     0x8a81d0: add             x12, fp, w2, sxtw #2
    //     0x8a81d4: ldr             x12, [x12, #8]
    //     0x8a81d8: add             w2, w11, #2
    //     0x8a81dc: sbfx            x11, x2, #1, #0x1f
    //     0x8a81e0: mov             x2, x11
    //     0x8a81e4: mov             x11, x12
    //     0x8a81e8: b               #0x8a81f0
    //     0x8a81ec: mov             x11, NULL
    //     0x8a81f0: stur            x11, [fp, #-0x38]
    //     0x8a81f4: lsl             x12, x2, #1
    //     0x8a81f8: lsl             w13, w12, #1
    //     0x8a81fc: add             w14, w13, #8
    //     0x8a8200: add             x16, x4, w14, sxtw #1
    //     0x8a8204: ldur            w19, [x16, #0xf]
    //     0x8a8208: add             x19, x19, HEAP, lsl #32
    //     0x8a820c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9c8] "ishaAngle"
    //     0x8a8210: ldr             x16, [x16, #0x9c8]
    //     0x8a8214: cmp             w19, w16
    //     0x8a8218: b.ne            #0x8a824c
    //     0x8a821c: add             w2, w13, #0xa
    //     0x8a8220: add             x16, x4, w2, sxtw #1
    //     0x8a8224: ldur            w13, [x16, #0xf]
    //     0x8a8228: add             x13, x13, HEAP, lsl #32
    //     0x8a822c: sub             w2, w0, w13
    //     0x8a8230: add             x13, fp, w2, sxtw #2
    //     0x8a8234: ldr             x13, [x13, #8]
    //     0x8a8238: add             w2, w12, #2
    //     0x8a823c: sbfx            x12, x2, #1, #0x1f
    //     0x8a8240: mov             x2, x12
    //     0x8a8244: mov             x12, x13
    //     0x8a8248: b               #0x8a8250
    //     0x8a824c: mov             x12, NULL
    //     0x8a8250: stur            x12, [fp, #-0x30]
    //     0x8a8254: lsl             x13, x2, #1
    //     0x8a8258: lsl             w14, w13, #1
    //     0x8a825c: add             w19, w14, #8
    //     0x8a8260: add             x16, x4, w19, sxtw #1
    //     0x8a8264: ldur            w20, [x16, #0xf]
    //     0x8a8268: add             x20, x20, HEAP, lsl #32
    //     0x8a826c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9d0] "ishaInterval"
    //     0x8a8270: ldr             x16, [x16, #0x9d0]
    //     0x8a8274: cmp             w20, w16
    //     0x8a8278: b.ne            #0x8a82ac
    //     0x8a827c: add             w2, w14, #0xa
    //     0x8a8280: add             x16, x4, w2, sxtw #1
    //     0x8a8284: ldur            w14, [x16, #0xf]
    //     0x8a8288: add             x14, x14, HEAP, lsl #32
    //     0x8a828c: sub             w2, w0, w14
    //     0x8a8290: add             x14, fp, w2, sxtw #2
    //     0x8a8294: ldr             x14, [x14, #8]
    //     0x8a8298: add             w2, w13, #2
    //     0x8a829c: sbfx            x13, x2, #1, #0x1f
    //     0x8a82a0: mov             x2, x13
    //     0x8a82a4: mov             x13, x14
    //     0x8a82a8: b               #0x8a82b0
    //     0x8a82ac: mov             x13, NULL
    //     0x8a82b0: stur            x13, [fp, #-0x28]
    //     0x8a82b4: lsl             x14, x2, #1
    //     0x8a82b8: lsl             w19, w14, #1
    //     0x8a82bc: add             w20, w19, #8
    //     0x8a82c0: add             x16, x4, w20, sxtw #1
    //     0x8a82c4: ldur            w23, [x16, #0xf]
    //     0x8a82c8: add             x23, x23, HEAP, lsl #32
    //     0x8a82cc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2e8] "madhabName"
    //     0x8a82d0: ldr             x16, [x16, #0x2e8]
    //     0x8a82d4: cmp             w23, w16
    //     0x8a82d8: b.ne            #0x8a830c
    //     0x8a82dc: add             w2, w19, #0xa
    //     0x8a82e0: add             x16, x4, w2, sxtw #1
    //     0x8a82e4: ldur            w19, [x16, #0xf]
    //     0x8a82e8: add             x19, x19, HEAP, lsl #32
    //     0x8a82ec: sub             w2, w0, w19
    //     0x8a82f0: add             x19, fp, w2, sxtw #2
    //     0x8a82f4: ldr             x19, [x19, #8]
    //     0x8a82f8: add             w2, w14, #2
    //     0x8a82fc: sbfx            x14, x2, #1, #0x1f
    //     0x8a8300: mov             x2, x14
    //     0x8a8304: mov             x14, x19
    //     0x8a8308: b               #0x8a8310
    //     0x8a830c: mov             x14, NULL
    //     0x8a8310: stur            x14, [fp, #-0x20]
    //     0x8a8314: lsl             x19, x2, #1
    //     0x8a8318: lsl             w20, w19, #1
    //     0x8a831c: add             w23, w20, #8
    //     0x8a8320: add             x16, x4, w23, sxtw #1
    //     0x8a8324: ldur            w24, [x16, #0xf]
    //     0x8a8328: add             x24, x24, HEAP, lsl #32
    //     0x8a832c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2f0] "maghribAdjustment"
    //     0x8a8330: ldr             x16, [x16, #0x2f0]
    //     0x8a8334: cmp             w24, w16
    //     0x8a8338: b.ne            #0x8a836c
    //     0x8a833c: add             w2, w20, #0xa
    //     0x8a8340: add             x16, x4, w2, sxtw #1
    //     0x8a8344: ldur            w20, [x16, #0xf]
    //     0x8a8348: add             x20, x20, HEAP, lsl #32
    //     0x8a834c: sub             w2, w0, w20
    //     0x8a8350: add             x20, fp, w2, sxtw #2
    //     0x8a8354: ldr             x20, [x20, #8]
    //     0x8a8358: add             w2, w19, #2
    //     0x8a835c: sbfx            x19, x2, #1, #0x1f
    //     0x8a8360: mov             x2, x19
    //     0x8a8364: mov             x19, x20
    //     0x8a8368: b               #0x8a8370
    //     0x8a836c: mov             x19, NULL
    //     0x8a8370: stur            x19, [fp, #-0x18]
    //     0x8a8374: lsl             x20, x2, #1
    //     0x8a8378: lsl             w2, w20, #1
    //     0x8a837c: add             w20, w2, #8
    //     0x8a8380: add             x16, x4, w20, sxtw #1
    //     0x8a8384: ldur            w23, [x16, #0xf]
    //     0x8a8388: add             x23, x23, HEAP, lsl #32
    //     0x8a838c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf2f8] "methodName"
    //     0x8a8390: ldr             x16, [x16, #0x2f8]
    //     0x8a8394: cmp             w23, w16
    //     0x8a8398: b.ne            #0x8a83c0
    //     0x8a839c: add             w20, w2, #0xa
    //     0x8a83a0: add             x16, x4, w20, sxtw #1
    //     0x8a83a4: ldur            w2, [x16, #0xf]
    //     0x8a83a8: add             x2, x2, HEAP, lsl #32
    //     0x8a83ac: sub             w4, w0, w2
    //     0x8a83b0: add             x0, fp, w4, sxtw #2
    //     0x8a83b4: ldr             x0, [x0, #8]
    //     0x8a83b8: mov             x2, x0
    //     0x8a83bc: b               #0x8a83c4
    //     0x8a83c0: mov             x2, NULL
    //     0x8a83c4: stur            x2, [fp, #-0x10]
    // 0x8a83c8: CheckStackOverflow
    //     0x8a83c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a83cc: cmp             SP, x16
    //     0x8a83d0: b.ls            #0x8a8688
    // 0x8a83d4: InitAsync() -> Future<void?>
    //     0x8a83d4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8a83d8: bl              #0x661298  ; InitAsyncStub
    // 0x8a83dc: ldur            x1, [fp, #-0x78]
    // 0x8a83e0: r0 = getSetting()
    //     0x8a83e0: bl              #0x8a68ec  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::getSetting
    // 0x8a83e4: mov             x1, x0
    // 0x8a83e8: stur            x1, [fp, #-0x80]
    // 0x8a83ec: r0 = Await()
    //     0x8a83ec: bl              #0x661044  ; AwaitStub
    // 0x8a83f0: mov             x2, x0
    // 0x8a83f4: ldur            x1, [fp, #-0x58]
    // 0x8a83f8: stur            x2, [fp, #-0x88]
    // 0x8a83fc: cmp             w1, NULL
    // 0x8a8400: b.eq            #0x8a8424
    // 0x8a8404: LoadField: r0 = r2->field_13
    //     0x8a8404: ldur            w0, [x2, #0x13]
    // 0x8a8408: DecompressPointer r0
    //     0x8a8408: add             x0, x0, HEAP, lsl #32
    // 0x8a840c: cmp             w1, w0
    // 0x8a8410: r16 = true
    //     0x8a8410: add             x16, NULL, #0x20  ; true
    // 0x8a8414: r17 = false
    //     0x8a8414: add             x17, NULL, #0x30  ; false
    // 0x8a8418: csel            x3, x16, x17, ne
    // 0x8a841c: mov             x4, x3
    // 0x8a8420: b               #0x8a8428
    // 0x8a8424: r4 = false
    //     0x8a8424: add             x4, NULL, #0x30  ; false
    // 0x8a8428: ldur            x3, [fp, #-0x10]
    // 0x8a842c: stur            x4, [fp, #-0x80]
    // 0x8a8430: cmp             w3, NULL
    // 0x8a8434: b.eq            #0x8a8464
    // 0x8a8438: LoadField: r0 = r2->field_1b
    //     0x8a8438: ldur            w0, [x2, #0x1b]
    // 0x8a843c: DecompressPointer r0
    //     0x8a843c: add             x0, x0, HEAP, lsl #32
    // 0x8a8440: r5 = LoadClassIdInstr(r3)
    //     0x8a8440: ldur            x5, [x3, #-1]
    //     0x8a8444: ubfx            x5, x5, #0xc, #0x14
    // 0x8a8448: stp             x0, x3, [SP]
    // 0x8a844c: mov             x0, x5
    // 0x8a8450: mov             lr, x0
    // 0x8a8454: ldr             lr, [x21, lr, lsl #3]
    // 0x8a8458: blr             lr
    // 0x8a845c: eor             x1, x0, #0x10
    // 0x8a8460: b               #0x8a8468
    // 0x8a8464: r1 = false
    //     0x8a8464: add             x1, NULL, #0x30  ; false
    // 0x8a8468: ldur            x0, [fp, #-0x80]
    // 0x8a846c: tbnz            w0, #4, #0x8a8598
    // 0x8a8470: ldur            x0, [fp, #-0x58]
    // 0x8a8474: r16 = true
    //     0x8a8474: add             x16, NULL, #0x20  ; true
    // 0x8a8478: cmp             w0, w16
    // 0x8a847c: b.ne            #0x8a84e4
    // 0x8a8480: ldur            x2, [fp, #-0x70]
    // 0x8a8484: ldur            x1, [fp, #-0x78]
    // 0x8a8488: r0 = defaultSetting()
    //     0x8a8488: bl              #0x8a6a0c  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::defaultSetting
    // 0x8a848c: mov             x2, x0
    // 0x8a8490: ldur            x3, [fp, #-0x70]
    // 0x8a8494: cmp             w3, NULL
    // 0x8a8498: b.ne            #0x8a84ac
    // 0x8a849c: ldur            x4, [fp, #-0x88]
    // 0x8a84a0: LoadField: r0 = r4->field_33
    //     0x8a84a0: ldur            x0, [x4, #0x33]
    // 0x8a84a4: mov             x3, x0
    // 0x8a84a8: b               #0x8a84bc
    // 0x8a84ac: r0 = LoadInt32Instr(r3)
    //     0x8a84ac: sbfx            x0, x3, #1, #0x1f
    //     0x8a84b0: tbz             w3, #0, #0x8a84b8
    //     0x8a84b4: ldur            x0, [x3, #7]
    // 0x8a84b8: mov             x3, x0
    // 0x8a84bc: r0 = BoxInt64Instr(r3)
    //     0x8a84bc: sbfiz           x0, x3, #1, #0x1f
    //     0x8a84c0: cmp             x3, x0, asr #1
    //     0x8a84c4: b.eq            #0x8a84d0
    //     0x8a84c8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8a84cc: stur            x3, [x0, #7]
    // 0x8a84d0: mov             x1, x2
    // 0x8a84d4: mov             x2, x0
    // 0x8a84d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8a84d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8a84dc: r0 = copyWith()
    //     0x8a84dc: bl              #0x8a8690  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::copyWith
    // 0x8a84e0: b               #0x8a8590
    // 0x8a84e4: ldur            x3, [fp, #-0x70]
    // 0x8a84e8: ldur            x4, [fp, #-0x88]
    // 0x8a84ec: LoadField: r2 = r4->field_1b
    //     0x8a84ec: ldur            w2, [x4, #0x1b]
    // 0x8a84f0: DecompressPointer r2
    //     0x8a84f0: add             x2, x2, HEAP, lsl #32
    // 0x8a84f4: r1 = Null
    //     0x8a84f4: mov             x1, NULL
    // 0x8a84f8: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a84f8: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a84fc: mov             x3, x0
    // 0x8a8500: ldur            x2, [fp, #-0x70]
    // 0x8a8504: cmp             w2, NULL
    // 0x8a8508: b.ne            #0x8a851c
    // 0x8a850c: ldur            x0, [fp, #-0x88]
    // 0x8a8510: LoadField: r1 = r0->field_33
    //     0x8a8510: ldur            x1, [x0, #0x33]
    // 0x8a8514: mov             x4, x1
    // 0x8a8518: b               #0x8a8530
    // 0x8a851c: ldur            x0, [fp, #-0x88]
    // 0x8a8520: r1 = LoadInt32Instr(r2)
    //     0x8a8520: sbfx            x1, x2, #1, #0x1f
    //     0x8a8524: tbz             w2, #0, #0x8a852c
    //     0x8a8528: ldur            x1, [x2, #7]
    // 0x8a852c: mov             x4, x1
    // 0x8a8530: cmp             w2, NULL
    // 0x8a8534: b.ne            #0x8a8544
    // 0x8a8538: LoadField: r1 = r0->field_33
    //     0x8a8538: ldur            x1, [x0, #0x33]
    // 0x8a853c: mov             x0, x1
    // 0x8a8540: b               #0x8a8550
    // 0x8a8544: r0 = LoadInt32Instr(r2)
    //     0x8a8544: sbfx            x0, x2, #1, #0x1f
    //     0x8a8548: tbz             w2, #0, #0x8a8550
    //     0x8a854c: ldur            x0, [x2, #7]
    // 0x8a8550: tbz             x0, #0x3f, #0x8a855c
    // 0x8a8554: r2 = false
    //     0x8a8554: add             x2, NULL, #0x30  ; false
    // 0x8a8558: b               #0x8a8560
    // 0x8a855c: r2 = true
    //     0x8a855c: add             x2, NULL, #0x20  ; true
    // 0x8a8560: r0 = BoxInt64Instr(r4)
    //     0x8a8560: sbfiz           x0, x4, #1, #0x1f
    //     0x8a8564: cmp             x4, x0, asr #1
    //     0x8a8568: b.eq            #0x8a8574
    //     0x8a856c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8a8570: stur            x4, [x0, #7]
    // 0x8a8574: ldur            x16, [fp, #-0x58]
    // 0x8a8578: stp             x2, x16, [SP]
    // 0x8a857c: mov             x1, x3
    // 0x8a8580: mov             x2, x0
    // 0x8a8584: r4 = const [0, 0x4, 0x2, 0x2, angleCorrection, 0x3, auto, 0x2, null]
    //     0x8a8584: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2aa08] List(9) [0, 0x4, 0x2, 0x2, "angleCorrection", 0x3, "auto", 0x2, Null]
    //     0x8a8588: ldr             x4, [x4, #0xa08]
    // 0x8a858c: r0 = copyWith()
    //     0x8a858c: bl              #0x8a8690  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::copyWith
    // 0x8a8590: mov             x2, x0
    // 0x8a8594: b               #0x8a866c
    // 0x8a8598: ldur            x2, [fp, #-0x70]
    // 0x8a859c: ldur            x0, [fp, #-0x88]
    // 0x8a85a0: tbnz            w1, #4, #0x8a8610
    // 0x8a85a4: ldur            x2, [fp, #-0x10]
    // 0x8a85a8: r1 = Null
    //     0x8a85a8: mov             x1, NULL
    // 0x8a85ac: r0 = PrayerTimeSetting.fromMethodName()
    //     0x8a85ac: bl              #0x8a7200  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::PrayerTimeSetting.fromMethodName
    // 0x8a85b0: mov             x2, x0
    // 0x8a85b4: ldur            x1, [fp, #-0x88]
    // 0x8a85b8: LoadField: r3 = r1->field_13
    //     0x8a85b8: ldur            w3, [x1, #0x13]
    // 0x8a85bc: DecompressPointer r3
    //     0x8a85bc: add             x3, x3, HEAP, lsl #32
    // 0x8a85c0: LoadField: r4 = r1->field_3b
    //     0x8a85c0: ldur            w4, [x1, #0x3b]
    // 0x8a85c4: DecompressPointer r4
    //     0x8a85c4: add             x4, x4, HEAP, lsl #32
    // 0x8a85c8: LoadField: r5 = r1->field_3f
    //     0x8a85c8: ldur            w5, [x1, #0x3f]
    // 0x8a85cc: DecompressPointer r5
    //     0x8a85cc: add             x5, x5, HEAP, lsl #32
    // 0x8a85d0: ArrayLoad: r6 = r1[0]  ; List_4
    //     0x8a85d0: ldur            w6, [x1, #0x17]
    // 0x8a85d4: DecompressPointer r6
    //     0x8a85d4: add             x6, x6, HEAP, lsl #32
    // 0x8a85d8: LoadField: r7 = r1->field_33
    //     0x8a85d8: ldur            x7, [x1, #0x33]
    // 0x8a85dc: r0 = BoxInt64Instr(r7)
    //     0x8a85dc: sbfiz           x0, x7, #1, #0x1f
    //     0x8a85e0: cmp             x7, x0, asr #1
    //     0x8a85e4: b.eq            #0x8a85f0
    //     0x8a85e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8a85ec: stur            x7, [x0, #7]
    // 0x8a85f0: stp             x4, x3, [SP, #0x10]
    // 0x8a85f4: stp             x6, x5, [SP]
    // 0x8a85f8: mov             x1, x2
    // 0x8a85fc: mov             x2, x0
    // 0x8a8600: r4 = const [0, 0x6, 0x4, 0x2, angleCorrection, 0x5, auto, 0x2, highLatitudeRuleName, 0x4, madhabName, 0x3, null]
    //     0x8a8600: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2aa10] List(13) [0, 0x6, 0x4, 0x2, "angleCorrection", 0x5, "auto", 0x2, "highLatitudeRuleName", 0x4, "madhabName", 0x3, Null]
    //     0x8a8604: ldr             x4, [x4, #0xa10]
    // 0x8a8608: r0 = copyWith()
    //     0x8a8608: bl              #0x8a8690  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::copyWith
    // 0x8a860c: b               #0x8a8668
    // 0x8a8610: mov             x1, x0
    // 0x8a8614: ldur            x16, [fp, #-0x58]
    // 0x8a8618: ldur            lr, [fp, #-0x68]
    // 0x8a861c: stp             lr, x16, [SP, #0x50]
    // 0x8a8620: ldur            x16, [fp, #-0x10]
    // 0x8a8624: ldur            lr, [fp, #-0x40]
    // 0x8a8628: stp             lr, x16, [SP, #0x40]
    // 0x8a862c: ldur            x16, [fp, #-0x30]
    // 0x8a8630: ldur            lr, [fp, #-0x28]
    // 0x8a8634: stp             lr, x16, [SP, #0x30]
    // 0x8a8638: ldur            x16, [fp, #-0x20]
    // 0x8a863c: ldur            lr, [fp, #-0x50]
    // 0x8a8640: stp             lr, x16, [SP, #0x20]
    // 0x8a8644: ldur            x16, [fp, #-0x60]
    // 0x8a8648: ldur            lr, [fp, #-0x18]
    // 0x8a864c: stp             lr, x16, [SP, #0x10]
    // 0x8a8650: ldur            x16, [fp, #-0x38]
    // 0x8a8654: ldur            lr, [fp, #-0x48]
    // 0x8a8658: stp             lr, x16, [SP]
    // 0x8a865c: r4 = const [0, 0xe, 0xc, 0x2, angleCorrection, 0x3, asrAdjustment, 0xa, auto, 0x2, dhuhrAdjustment, 0x9, fajrAdjustment, 0xd, fajrAngle, 0x5, ishaAdjustment, 0xc, ishaAngle, 0x6, ishaInterval, 0x7, madhabName, 0x8, maghribAdjustment, 0xb, methodName, 0x4, null]
    //     0x8a865c: add             x4, PP, #0x2a, lsl #12  ; [pp+0x2aa18] List(29) [0, 0xe, 0xc, 0x2, "angleCorrection", 0x3, "asrAdjustment", 0xa, "auto", 0x2, "dhuhrAdjustment", 0x9, "fajrAdjustment", 0xd, "fajrAngle", 0x5, "ishaAdjustment", 0xc, "ishaAngle", 0x6, "ishaInterval", 0x7, "madhabName", 0x8, "maghribAdjustment", 0xb, "methodName", 0x4, Null]
    //     0x8a8660: ldr             x4, [x4, #0xa18]
    // 0x8a8664: r0 = copyWith()
    //     0x8a8664: bl              #0x8a8690  ; [package:nuonline/app/data/models/prayer_time_setting.dart] PrayerTimeSetting::copyWith
    // 0x8a8668: mov             x2, x0
    // 0x8a866c: ldur            x1, [fp, #-0x78]
    // 0x8a8670: r0 = updateSetting()
    //     0x8a8670: bl              #0x8a6978  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::updateSetting
    // 0x8a8674: mov             x1, x0
    // 0x8a8678: stur            x1, [fp, #-0x10]
    // 0x8a867c: r0 = Await()
    //     0x8a867c: bl              #0x661044  ; AwaitStub
    // 0x8a8680: r0 = Null
    //     0x8a8680: mov             x0, NULL
    // 0x8a8684: r0 = ReturnAsyncNotFuture()
    //     0x8a8684: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a8688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a8688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a868c: b               #0x8a83d4
  }
  _ getUpcomingPrayerTime(/* No info */) async {
    // ** addr: 0x8a8f60, size: 0xac
    // 0x8a8f60: EnterFrame
    //     0x8a8f60: stp             fp, lr, [SP, #-0x10]!
    //     0x8a8f64: mov             fp, SP
    // 0x8a8f68: AllocStack(0x20)
    //     0x8a8f68: sub             SP, SP, #0x20
    // 0x8a8f6c: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8a8f6c: stur            NULL, [fp, #-8]
    //     0x8a8f70: stur            x1, [fp, #-0x10]
    //     0x8a8f74: stur            x2, [fp, #-0x18]
    // 0x8a8f78: CheckStackOverflow
    //     0x8a8f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a8f7c: cmp             SP, x16
    //     0x8a8f80: b.ls            #0x8a9004
    // 0x8a8f84: InitAsync() -> Future<PrayerTimes>
    //     0x8a8f84: add             x0, PP, #0xb, lsl #12  ; [pp+0xb640] TypeArguments: <PrayerTimes>
    //     0x8a8f88: ldr             x0, [x0, #0x640]
    //     0x8a8f8c: bl              #0x661298  ; InitAsyncStub
    // 0x8a8f90: ldur            x1, [fp, #-0x10]
    // 0x8a8f94: r0 = getSetting()
    //     0x8a8f94: bl              #0x8a68ec  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::getSetting
    // 0x8a8f98: mov             x1, x0
    // 0x8a8f9c: stur            x1, [fp, #-0x10]
    // 0x8a8fa0: r0 = Await()
    //     0x8a8fa0: bl              #0x661044  ; AwaitStub
    // 0x8a8fa4: mov             x1, x0
    // 0x8a8fa8: stur            x0, [fp, #-0x10]
    // 0x8a8fac: r0 = PrayerTimeSettingExtension.params()
    //     0x8a8fac: bl              #0x82fd44  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.params
    // 0x8a8fb0: ldur            x2, [fp, #-0x18]
    // 0x8a8fb4: mov             x3, x0
    // 0x8a8fb8: r1 = Null
    //     0x8a8fb8: mov             x1, NULL
    // 0x8a8fbc: r0 = PrayerTimes.today()
    //     0x8a8fbc: bl              #0x8a944c  ; [package:adhan/src/prayer_times.dart] PrayerTimes::PrayerTimes.today
    // 0x8a8fc0: mov             x1, x0
    // 0x8a8fc4: stur            x0, [fp, #-0x20]
    // 0x8a8fc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a8fc8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a8fcc: r0 = nextPrayer()
    //     0x8a8fcc: bl              #0x8a90c4  ; [package:adhan/src/prayer_times.dart] PrayerTimes::nextPrayer
    // 0x8a8fd0: r16 = Instance_Prayer
    //     0x8a8fd0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2deb0] Obj!Prayer@e38e01
    //     0x8a8fd4: ldr             x16, [x16, #0xeb0]
    // 0x8a8fd8: cmp             w0, w16
    // 0x8a8fdc: b.ne            #0x8a8ffc
    // 0x8a8fe0: ldur            x1, [fp, #-0x10]
    // 0x8a8fe4: r0 = PrayerTimeSettingExtension.params()
    //     0x8a8fe4: bl              #0x82fd44  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] ::PrayerTimeSettingExtension.params
    // 0x8a8fe8: ldur            x2, [fp, #-0x18]
    // 0x8a8fec: mov             x3, x0
    // 0x8a8ff0: r1 = Null
    //     0x8a8ff0: mov             x1, NULL
    // 0x8a8ff4: r0 = PrayerTimes.tomorrow()
    //     0x8a8ff4: bl              #0x8a900c  ; [package:adhan/src/prayer_times.dart] PrayerTimes::PrayerTimes.tomorrow
    // 0x8a8ff8: b               #0x8a9000
    // 0x8a8ffc: ldur            x0, [fp, #-0x20]
    // 0x8a9000: r0 = ReturnAsyncNotFuture()
    //     0x8a9000: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8a9004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9008: b               #0x8a8f84
  }
  _ patchNotificationSetting(/* No info */) async {
    // ** addr: 0x904b20, size: 0x62c
    // 0x904b20: EnterFrame
    //     0x904b20: stp             fp, lr, [SP, #-0x10]!
    //     0x904b24: mov             fp, SP
    // 0x904b28: AllocStack(0xd8)
    //     0x904b28: sub             SP, SP, #0xd8
    // 0x904b2c: SetupParameters(PrayerTimeRepository this /* r1 => fp-0x10 */, {dynamic asrSound = Null /* r3, fp-0x88 */, dynamic beforeAsrDelay = Null /* r5, fp-0x80 */, dynamic beforeDhuhrDelay = Null /* r6, fp-0x78 */, dynamic beforeFajrDelay = Null /* r7, fp-0x70 */, dynamic beforeIshaDelay = Null /* r8, fp-0x68 */, dynamic beforeMaghribDelay = Null /* r9, fp-0x60 */, dynamic beforeSunriseDelay = Null /* r10, fp-0x58 */, dynamic dhuhrSound = Null /* r11, fp-0x50 */, dynamic dluhaSound = Null /* r12, fp-0x48 */, dynamic fajrSound = Null /* r13, fp-0x40 */, dynamic imsakRamadhanOnly = Null /* r14, fp-0x38 */, dynamic imsakSound = Null /* r19, fp-0x30 */, dynamic ishaSound = Null /* r20, fp-0x28 */, dynamic maghribSound = Null /* r2, fp-0x20 */, dynamic sunriseSound = Null /* r1, fp-0x18 */})
    //     0x904b2c: stur            NULL, [fp, #-8]
    //     0x904b30: stur            x1, [fp, #-0x10]
    //     0x904b34: ldur            w0, [x4, #0x13]
    //     0x904b38: ldur            w2, [x4, #0x1f]
    //     0x904b3c: add             x2, x2, HEAP, lsl #32
    //     0x904b40: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a640] "asrSound"
    //     0x904b44: ldr             x16, [x16, #0x640]
    //     0x904b48: cmp             w2, w16
    //     0x904b4c: b.ne            #0x904b70
    //     0x904b50: ldur            w2, [x4, #0x23]
    //     0x904b54: add             x2, x2, HEAP, lsl #32
    //     0x904b58: sub             w3, w0, w2
    //     0x904b5c: add             x2, fp, w3, sxtw #2
    //     0x904b60: ldr             x2, [x2, #8]
    //     0x904b64: mov             x3, x2
    //     0x904b68: movz            x2, #0x1
    //     0x904b6c: b               #0x904b78
    //     0x904b70: mov             x3, NULL
    //     0x904b74: movz            x2, #0
    //     0x904b78: stur            x3, [fp, #-0x88]
    //     0x904b7c: lsl             x5, x2, #1
    //     0x904b80: lsl             w6, w5, #1
    //     0x904b84: add             w7, w6, #8
    //     0x904b88: add             x16, x4, w7, sxtw #1
    //     0x904b8c: ldur            w8, [x16, #0xf]
    //     0x904b90: add             x8, x8, HEAP, lsl #32
    //     0x904b94: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a648] "beforeAsrDelay"
    //     0x904b98: ldr             x16, [x16, #0x648]
    //     0x904b9c: cmp             w8, w16
    //     0x904ba0: b.ne            #0x904bd4
    //     0x904ba4: add             w2, w6, #0xa
    //     0x904ba8: add             x16, x4, w2, sxtw #1
    //     0x904bac: ldur            w6, [x16, #0xf]
    //     0x904bb0: add             x6, x6, HEAP, lsl #32
    //     0x904bb4: sub             w2, w0, w6
    //     0x904bb8: add             x6, fp, w2, sxtw #2
    //     0x904bbc: ldr             x6, [x6, #8]
    //     0x904bc0: add             w2, w5, #2
    //     0x904bc4: sbfx            x5, x2, #1, #0x1f
    //     0x904bc8: mov             x2, x5
    //     0x904bcc: mov             x5, x6
    //     0x904bd0: b               #0x904bd8
    //     0x904bd4: mov             x5, NULL
    //     0x904bd8: stur            x5, [fp, #-0x80]
    //     0x904bdc: lsl             x6, x2, #1
    //     0x904be0: lsl             w7, w6, #1
    //     0x904be4: add             w8, w7, #8
    //     0x904be8: add             x16, x4, w8, sxtw #1
    //     0x904bec: ldur            w9, [x16, #0xf]
    //     0x904bf0: add             x9, x9, HEAP, lsl #32
    //     0x904bf4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a650] "beforeDhuhrDelay"
    //     0x904bf8: ldr             x16, [x16, #0x650]
    //     0x904bfc: cmp             w9, w16
    //     0x904c00: b.ne            #0x904c34
    //     0x904c04: add             w2, w7, #0xa
    //     0x904c08: add             x16, x4, w2, sxtw #1
    //     0x904c0c: ldur            w7, [x16, #0xf]
    //     0x904c10: add             x7, x7, HEAP, lsl #32
    //     0x904c14: sub             w2, w0, w7
    //     0x904c18: add             x7, fp, w2, sxtw #2
    //     0x904c1c: ldr             x7, [x7, #8]
    //     0x904c20: add             w2, w6, #2
    //     0x904c24: sbfx            x6, x2, #1, #0x1f
    //     0x904c28: mov             x2, x6
    //     0x904c2c: mov             x6, x7
    //     0x904c30: b               #0x904c38
    //     0x904c34: mov             x6, NULL
    //     0x904c38: stur            x6, [fp, #-0x78]
    //     0x904c3c: lsl             x7, x2, #1
    //     0x904c40: lsl             w8, w7, #1
    //     0x904c44: add             w9, w8, #8
    //     0x904c48: add             x16, x4, w9, sxtw #1
    //     0x904c4c: ldur            w10, [x16, #0xf]
    //     0x904c50: add             x10, x10, HEAP, lsl #32
    //     0x904c54: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a658] "beforeFajrDelay"
    //     0x904c58: ldr             x16, [x16, #0x658]
    //     0x904c5c: cmp             w10, w16
    //     0x904c60: b.ne            #0x904c94
    //     0x904c64: add             w2, w8, #0xa
    //     0x904c68: add             x16, x4, w2, sxtw #1
    //     0x904c6c: ldur            w8, [x16, #0xf]
    //     0x904c70: add             x8, x8, HEAP, lsl #32
    //     0x904c74: sub             w2, w0, w8
    //     0x904c78: add             x8, fp, w2, sxtw #2
    //     0x904c7c: ldr             x8, [x8, #8]
    //     0x904c80: add             w2, w7, #2
    //     0x904c84: sbfx            x7, x2, #1, #0x1f
    //     0x904c88: mov             x2, x7
    //     0x904c8c: mov             x7, x8
    //     0x904c90: b               #0x904c98
    //     0x904c94: mov             x7, NULL
    //     0x904c98: stur            x7, [fp, #-0x70]
    //     0x904c9c: lsl             x8, x2, #1
    //     0x904ca0: lsl             w9, w8, #1
    //     0x904ca4: add             w10, w9, #8
    //     0x904ca8: add             x16, x4, w10, sxtw #1
    //     0x904cac: ldur            w11, [x16, #0xf]
    //     0x904cb0: add             x11, x11, HEAP, lsl #32
    //     0x904cb4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a660] "beforeIshaDelay"
    //     0x904cb8: ldr             x16, [x16, #0x660]
    //     0x904cbc: cmp             w11, w16
    //     0x904cc0: b.ne            #0x904cf4
    //     0x904cc4: add             w2, w9, #0xa
    //     0x904cc8: add             x16, x4, w2, sxtw #1
    //     0x904ccc: ldur            w9, [x16, #0xf]
    //     0x904cd0: add             x9, x9, HEAP, lsl #32
    //     0x904cd4: sub             w2, w0, w9
    //     0x904cd8: add             x9, fp, w2, sxtw #2
    //     0x904cdc: ldr             x9, [x9, #8]
    //     0x904ce0: add             w2, w8, #2
    //     0x904ce4: sbfx            x8, x2, #1, #0x1f
    //     0x904ce8: mov             x2, x8
    //     0x904cec: mov             x8, x9
    //     0x904cf0: b               #0x904cf8
    //     0x904cf4: mov             x8, NULL
    //     0x904cf8: stur            x8, [fp, #-0x68]
    //     0x904cfc: lsl             x9, x2, #1
    //     0x904d00: lsl             w10, w9, #1
    //     0x904d04: add             w11, w10, #8
    //     0x904d08: add             x16, x4, w11, sxtw #1
    //     0x904d0c: ldur            w12, [x16, #0xf]
    //     0x904d10: add             x12, x12, HEAP, lsl #32
    //     0x904d14: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a668] "beforeMaghribDelay"
    //     0x904d18: ldr             x16, [x16, #0x668]
    //     0x904d1c: cmp             w12, w16
    //     0x904d20: b.ne            #0x904d54
    //     0x904d24: add             w2, w10, #0xa
    //     0x904d28: add             x16, x4, w2, sxtw #1
    //     0x904d2c: ldur            w10, [x16, #0xf]
    //     0x904d30: add             x10, x10, HEAP, lsl #32
    //     0x904d34: sub             w2, w0, w10
    //     0x904d38: add             x10, fp, w2, sxtw #2
    //     0x904d3c: ldr             x10, [x10, #8]
    //     0x904d40: add             w2, w9, #2
    //     0x904d44: sbfx            x9, x2, #1, #0x1f
    //     0x904d48: mov             x2, x9
    //     0x904d4c: mov             x9, x10
    //     0x904d50: b               #0x904d58
    //     0x904d54: mov             x9, NULL
    //     0x904d58: stur            x9, [fp, #-0x60]
    //     0x904d5c: lsl             x10, x2, #1
    //     0x904d60: lsl             w11, w10, #1
    //     0x904d64: add             w12, w11, #8
    //     0x904d68: add             x16, x4, w12, sxtw #1
    //     0x904d6c: ldur            w13, [x16, #0xf]
    //     0x904d70: add             x13, x13, HEAP, lsl #32
    //     0x904d74: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a670] "beforeSunriseDelay"
    //     0x904d78: ldr             x16, [x16, #0x670]
    //     0x904d7c: cmp             w13, w16
    //     0x904d80: b.ne            #0x904db4
    //     0x904d84: add             w2, w11, #0xa
    //     0x904d88: add             x16, x4, w2, sxtw #1
    //     0x904d8c: ldur            w11, [x16, #0xf]
    //     0x904d90: add             x11, x11, HEAP, lsl #32
    //     0x904d94: sub             w2, w0, w11
    //     0x904d98: add             x11, fp, w2, sxtw #2
    //     0x904d9c: ldr             x11, [x11, #8]
    //     0x904da0: add             w2, w10, #2
    //     0x904da4: sbfx            x10, x2, #1, #0x1f
    //     0x904da8: mov             x2, x10
    //     0x904dac: mov             x10, x11
    //     0x904db0: b               #0x904db8
    //     0x904db4: mov             x10, NULL
    //     0x904db8: stur            x10, [fp, #-0x58]
    //     0x904dbc: lsl             x11, x2, #1
    //     0x904dc0: lsl             w12, w11, #1
    //     0x904dc4: add             w13, w12, #8
    //     0x904dc8: add             x16, x4, w13, sxtw #1
    //     0x904dcc: ldur            w14, [x16, #0xf]
    //     0x904dd0: add             x14, x14, HEAP, lsl #32
    //     0x904dd4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a678] "dhuhrSound"
    //     0x904dd8: ldr             x16, [x16, #0x678]
    //     0x904ddc: cmp             w14, w16
    //     0x904de0: b.ne            #0x904e14
    //     0x904de4: add             w2, w12, #0xa
    //     0x904de8: add             x16, x4, w2, sxtw #1
    //     0x904dec: ldur            w12, [x16, #0xf]
    //     0x904df0: add             x12, x12, HEAP, lsl #32
    //     0x904df4: sub             w2, w0, w12
    //     0x904df8: add             x12, fp, w2, sxtw #2
    //     0x904dfc: ldr             x12, [x12, #8]
    //     0x904e00: add             w2, w11, #2
    //     0x904e04: sbfx            x11, x2, #1, #0x1f
    //     0x904e08: mov             x2, x11
    //     0x904e0c: mov             x11, x12
    //     0x904e10: b               #0x904e18
    //     0x904e14: mov             x11, NULL
    //     0x904e18: stur            x11, [fp, #-0x50]
    //     0x904e1c: lsl             x12, x2, #1
    //     0x904e20: lsl             w13, w12, #1
    //     0x904e24: add             w14, w13, #8
    //     0x904e28: add             x16, x4, w14, sxtw #1
    //     0x904e2c: ldur            w19, [x16, #0xf]
    //     0x904e30: add             x19, x19, HEAP, lsl #32
    //     0x904e34: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a680] "dluhaSound"
    //     0x904e38: ldr             x16, [x16, #0x680]
    //     0x904e3c: cmp             w19, w16
    //     0x904e40: b.ne            #0x904e74
    //     0x904e44: add             w2, w13, #0xa
    //     0x904e48: add             x16, x4, w2, sxtw #1
    //     0x904e4c: ldur            w13, [x16, #0xf]
    //     0x904e50: add             x13, x13, HEAP, lsl #32
    //     0x904e54: sub             w2, w0, w13
    //     0x904e58: add             x13, fp, w2, sxtw #2
    //     0x904e5c: ldr             x13, [x13, #8]
    //     0x904e60: add             w2, w12, #2
    //     0x904e64: sbfx            x12, x2, #1, #0x1f
    //     0x904e68: mov             x2, x12
    //     0x904e6c: mov             x12, x13
    //     0x904e70: b               #0x904e78
    //     0x904e74: mov             x12, NULL
    //     0x904e78: stur            x12, [fp, #-0x48]
    //     0x904e7c: lsl             x13, x2, #1
    //     0x904e80: lsl             w14, w13, #1
    //     0x904e84: add             w19, w14, #8
    //     0x904e88: add             x16, x4, w19, sxtw #1
    //     0x904e8c: ldur            w20, [x16, #0xf]
    //     0x904e90: add             x20, x20, HEAP, lsl #32
    //     0x904e94: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a688] "fajrSound"
    //     0x904e98: ldr             x16, [x16, #0x688]
    //     0x904e9c: cmp             w20, w16
    //     0x904ea0: b.ne            #0x904ed4
    //     0x904ea4: add             w2, w14, #0xa
    //     0x904ea8: add             x16, x4, w2, sxtw #1
    //     0x904eac: ldur            w14, [x16, #0xf]
    //     0x904eb0: add             x14, x14, HEAP, lsl #32
    //     0x904eb4: sub             w2, w0, w14
    //     0x904eb8: add             x14, fp, w2, sxtw #2
    //     0x904ebc: ldr             x14, [x14, #8]
    //     0x904ec0: add             w2, w13, #2
    //     0x904ec4: sbfx            x13, x2, #1, #0x1f
    //     0x904ec8: mov             x2, x13
    //     0x904ecc: mov             x13, x14
    //     0x904ed0: b               #0x904ed8
    //     0x904ed4: mov             x13, NULL
    //     0x904ed8: stur            x13, [fp, #-0x40]
    //     0x904edc: lsl             x14, x2, #1
    //     0x904ee0: lsl             w19, w14, #1
    //     0x904ee4: add             w20, w19, #8
    //     0x904ee8: add             x16, x4, w20, sxtw #1
    //     0x904eec: ldur            w23, [x16, #0xf]
    //     0x904ef0: add             x23, x23, HEAP, lsl #32
    //     0x904ef4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a690] "imsakRamadhanOnly"
    //     0x904ef8: ldr             x16, [x16, #0x690]
    //     0x904efc: cmp             w23, w16
    //     0x904f00: b.ne            #0x904f34
    //     0x904f04: add             w2, w19, #0xa
    //     0x904f08: add             x16, x4, w2, sxtw #1
    //     0x904f0c: ldur            w19, [x16, #0xf]
    //     0x904f10: add             x19, x19, HEAP, lsl #32
    //     0x904f14: sub             w2, w0, w19
    //     0x904f18: add             x19, fp, w2, sxtw #2
    //     0x904f1c: ldr             x19, [x19, #8]
    //     0x904f20: add             w2, w14, #2
    //     0x904f24: sbfx            x14, x2, #1, #0x1f
    //     0x904f28: mov             x2, x14
    //     0x904f2c: mov             x14, x19
    //     0x904f30: b               #0x904f38
    //     0x904f34: mov             x14, NULL
    //     0x904f38: stur            x14, [fp, #-0x38]
    //     0x904f3c: lsl             x19, x2, #1
    //     0x904f40: lsl             w20, w19, #1
    //     0x904f44: add             w23, w20, #8
    //     0x904f48: add             x16, x4, w23, sxtw #1
    //     0x904f4c: ldur            w24, [x16, #0xf]
    //     0x904f50: add             x24, x24, HEAP, lsl #32
    //     0x904f54: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a698] "imsakSound"
    //     0x904f58: ldr             x16, [x16, #0x698]
    //     0x904f5c: cmp             w24, w16
    //     0x904f60: b.ne            #0x904f94
    //     0x904f64: add             w2, w20, #0xa
    //     0x904f68: add             x16, x4, w2, sxtw #1
    //     0x904f6c: ldur            w20, [x16, #0xf]
    //     0x904f70: add             x20, x20, HEAP, lsl #32
    //     0x904f74: sub             w2, w0, w20
    //     0x904f78: add             x20, fp, w2, sxtw #2
    //     0x904f7c: ldr             x20, [x20, #8]
    //     0x904f80: add             w2, w19, #2
    //     0x904f84: sbfx            x19, x2, #1, #0x1f
    //     0x904f88: mov             x2, x19
    //     0x904f8c: mov             x19, x20
    //     0x904f90: b               #0x904f98
    //     0x904f94: mov             x19, NULL
    //     0x904f98: stur            x19, [fp, #-0x30]
    //     0x904f9c: lsl             x20, x2, #1
    //     0x904fa0: lsl             w23, w20, #1
    //     0x904fa4: add             w24, w23, #8
    //     0x904fa8: add             x16, x4, w24, sxtw #1
    //     0x904fac: ldur            w25, [x16, #0xf]
    //     0x904fb0: add             x25, x25, HEAP, lsl #32
    //     0x904fb4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a6a0] "ishaSound"
    //     0x904fb8: ldr             x16, [x16, #0x6a0]
    //     0x904fbc: cmp             w25, w16
    //     0x904fc0: b.ne            #0x904ff4
    //     0x904fc4: add             w2, w23, #0xa
    //     0x904fc8: add             x16, x4, w2, sxtw #1
    //     0x904fcc: ldur            w23, [x16, #0xf]
    //     0x904fd0: add             x23, x23, HEAP, lsl #32
    //     0x904fd4: sub             w2, w0, w23
    //     0x904fd8: add             x23, fp, w2, sxtw #2
    //     0x904fdc: ldr             x23, [x23, #8]
    //     0x904fe0: add             w2, w20, #2
    //     0x904fe4: sbfx            x20, x2, #1, #0x1f
    //     0x904fe8: mov             x2, x20
    //     0x904fec: mov             x20, x23
    //     0x904ff0: b               #0x904ff8
    //     0x904ff4: mov             x20, NULL
    //     0x904ff8: stur            x20, [fp, #-0x28]
    //     0x904ffc: lsl             x23, x2, #1
    //     0x905000: lsl             w24, w23, #1
    //     0x905004: add             w25, w24, #8
    //     0x905008: add             x16, x4, w25, sxtw #1
    //     0x90500c: ldur            w1, [x16, #0xf]
    //     0x905010: add             x1, x1, HEAP, lsl #32
    //     0x905014: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a6a8] "maghribSound"
    //     0x905018: ldr             x16, [x16, #0x6a8]
    //     0x90501c: cmp             w1, w16
    //     0x905020: b.ne            #0x905050
    //     0x905024: add             w1, w24, #0xa
    //     0x905028: add             x16, x4, w1, sxtw #1
    //     0x90502c: ldur            w2, [x16, #0xf]
    //     0x905030: add             x2, x2, HEAP, lsl #32
    //     0x905034: sub             w1, w0, w2
    //     0x905038: add             x2, fp, w1, sxtw #2
    //     0x90503c: ldr             x2, [x2, #8]
    //     0x905040: add             w1, w23, #2
    //     0x905044: sbfx            x23, x1, #1, #0x1f
    //     0x905048: mov             x1, x23
    //     0x90504c: b               #0x905058
    //     0x905050: mov             x1, x2
    //     0x905054: mov             x2, NULL
    //     0x905058: stur            x2, [fp, #-0x20]
    //     0x90505c: lsl             x23, x1, #1
    //     0x905060: lsl             w1, w23, #1
    //     0x905064: add             w23, w1, #8
    //     0x905068: add             x16, x4, w23, sxtw #1
    //     0x90506c: ldur            w24, [x16, #0xf]
    //     0x905070: add             x24, x24, HEAP, lsl #32
    //     0x905074: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2a6b0] "sunriseSound"
    //     0x905078: ldr             x16, [x16, #0x6b0]
    //     0x90507c: cmp             w24, w16
    //     0x905080: b.ne            #0x9050a8
    //     0x905084: add             w23, w1, #0xa
    //     0x905088: add             x16, x4, w23, sxtw #1
    //     0x90508c: ldur            w1, [x16, #0xf]
    //     0x905090: add             x1, x1, HEAP, lsl #32
    //     0x905094: sub             w4, w0, w1
    //     0x905098: add             x0, fp, w4, sxtw #2
    //     0x90509c: ldr             x0, [x0, #8]
    //     0x9050a0: mov             x1, x0
    //     0x9050a4: b               #0x9050ac
    //     0x9050a8: mov             x1, NULL
    //     0x9050ac: stur            x1, [fp, #-0x18]
    // 0x9050b0: CheckStackOverflow
    //     0x9050b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9050b4: cmp             SP, x16
    //     0x9050b8: b.ls            #0x905144
    // 0x9050bc: InitAsync() -> Future<void?>
    //     0x9050bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x9050c0: bl              #0x661298  ; InitAsyncStub
    // 0x9050c4: ldur            x1, [fp, #-0x10]
    // 0x9050c8: r0 = defaultNotificationSetting()
    //     0x9050c8: bl              #0x90554c  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::defaultNotificationSetting
    // 0x9050cc: ldur            x16, [fp, #-0x60]
    // 0x9050d0: ldur            lr, [fp, #-0x58]
    // 0x9050d4: stp             lr, x16, [SP, #0x40]
    // 0x9050d8: ldur            x16, [fp, #-0x50]
    // 0x9050dc: ldur            lr, [fp, #-0x48]
    // 0x9050e0: stp             lr, x16, [SP, #0x30]
    // 0x9050e4: ldur            x16, [fp, #-0x40]
    // 0x9050e8: ldur            lr, [fp, #-0x38]
    // 0x9050ec: stp             lr, x16, [SP, #0x20]
    // 0x9050f0: ldur            x16, [fp, #-0x30]
    // 0x9050f4: ldur            lr, [fp, #-0x28]
    // 0x9050f8: stp             lr, x16, [SP, #0x10]
    // 0x9050fc: ldur            x16, [fp, #-0x20]
    // 0x905100: ldur            lr, [fp, #-0x18]
    // 0x905104: stp             lr, x16, [SP]
    // 0x905108: mov             x1, x0
    // 0x90510c: ldur            x2, [fp, #-0x88]
    // 0x905110: ldur            x3, [fp, #-0x80]
    // 0x905114: ldur            x5, [fp, #-0x78]
    // 0x905118: ldur            x6, [fp, #-0x70]
    // 0x90511c: ldur            x7, [fp, #-0x68]
    // 0x905120: r0 = copyWith()
    //     0x905120: bl              #0x9051e0  ; [package:nuonline/app/data/models/prayer_time_notification_setting.dart] PrayerTimeNotificationSetting::copyWith
    // 0x905124: ldur            x1, [fp, #-0x10]
    // 0x905128: mov             x2, x0
    // 0x90512c: r0 = updateNotificationSetting()
    //     0x90512c: bl              #0x90514c  ; [package:nuonline/app/data/repositories/prayer_time_repository.dart] PrayerTimeRepository::updateNotificationSetting
    // 0x905130: mov             x1, x0
    // 0x905134: stur            x1, [fp, #-0x10]
    // 0x905138: r0 = Await()
    //     0x905138: bl              #0x661044  ; AwaitStub
    // 0x90513c: r0 = Null
    //     0x90513c: mov             x0, NULL
    // 0x905140: r0 = ReturnAsyncNotFuture()
    //     0x905140: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x905144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905144: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905148: b               #0x9050bc
  }
  _ updateNotificationSetting(/* No info */) async {
    // ** addr: 0x90514c, size: 0x94
    // 0x90514c: EnterFrame
    //     0x90514c: stp             fp, lr, [SP, #-0x10]!
    //     0x905150: mov             fp, SP
    // 0x905154: AllocStack(0x30)
    //     0x905154: sub             SP, SP, #0x30
    // 0x905158: SetupParameters(PrayerTimeRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x905158: stur            NULL, [fp, #-8]
    //     0x90515c: mov             x3, x2
    //     0x905160: stur            x1, [fp, #-0x10]
    //     0x905164: stur            x2, [fp, #-0x18]
    // 0x905168: CheckStackOverflow
    //     0x905168: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90516c: cmp             SP, x16
    //     0x905170: b.ls            #0x9051d8
    // 0x905174: InitAsync() -> Future<void?>
    //     0x905174: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x905178: bl              #0x661298  ; InitAsyncStub
    // 0x90517c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x90517c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x905180: ldr             x0, [x0, #0x2728]
    //     0x905184: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x905188: cmp             w0, w16
    //     0x90518c: b.ne            #0x905198
    //     0x905190: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x905194: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x905198: stp             x0, NULL, [SP, #8]
    // 0x90519c: r16 = "v2_prayer_time_notification"
    //     0x90519c: add             x16, PP, #8, lsl #12  ; [pp+0x8068] "v2_prayer_time_notification"
    //     0x9051a0: ldr             x16, [x16, #0x68]
    // 0x9051a4: str             x16, [SP]
    // 0x9051a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9051a8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9051ac: r0 = box()
    //     0x9051ac: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x9051b0: mov             x1, x0
    // 0x9051b4: ldur            x3, [fp, #-0x18]
    // 0x9051b8: r2 = "setting"
    //     0x9051b8: add             x2, PP, #8, lsl #12  ; [pp+0x8070] "setting"
    //     0x9051bc: ldr             x2, [x2, #0x70]
    // 0x9051c0: r0 = put()
    //     0x9051c0: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0x9051c4: mov             x1, x0
    // 0x9051c8: stur            x1, [fp, #-0x10]
    // 0x9051cc: r0 = Await()
    //     0x9051cc: bl              #0x661044  ; AwaitStub
    // 0x9051d0: r0 = Null
    //     0x9051d0: mov             x0, NULL
    // 0x9051d4: r0 = ReturnAsyncNotFuture()
    //     0x9051d4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9051d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9051d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9051dc: b               #0x905174
  }
  get _ defaultNotificationSetting(/* No info */) {
    // ** addr: 0x90554c, size: 0x48
    // 0x90554c: EnterFrame
    //     0x90554c: stp             fp, lr, [SP, #-0x10]!
    //     0x905550: mov             fp, SP
    // 0x905554: CheckStackOverflow
    //     0x905554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x905558: cmp             SP, x16
    //     0x90555c: b.ls            #0x90558c
    // 0x905560: LoadField: r0 = r1->field_7
    //     0x905560: ldur            w0, [x1, #7]
    // 0x905564: DecompressPointer r0
    //     0x905564: add             x0, x0, HEAP, lsl #32
    // 0x905568: mov             x1, x0
    // 0x90556c: r0 = settingNotification()
    //     0x90556c: bl              #0x83110c  ; [package:nuonline/services/storage_service/prayer_time_storage.dart] PrayerTimeStorage::settingNotification
    // 0x905570: cmp             w0, NULL
    // 0x905574: b.ne            #0x905580
    // 0x905578: r1 = Null
    //     0x905578: mov             x1, NULL
    // 0x90557c: r0 = PrayerTimeNotificationSetting.defaultSetting()
    //     0x90557c: bl              #0x83101c  ; [package:nuonline/app/data/models/prayer_time_notification_setting.dart] PrayerTimeNotificationSetting::PrayerTimeNotificationSetting.defaultSetting
    // 0x905580: LeaveFrame
    //     0x905580: mov             SP, fp
    //     0x905584: ldp             fp, lr, [SP], #0x10
    // 0x905588: ret
    //     0x905588: ret             
    // 0x90558c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90558c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905590: b               #0x905560
  }
}
