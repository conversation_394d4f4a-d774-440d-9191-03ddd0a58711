// lib: , url: package:nuonline/app/data/repositories/price_repository.dart

// class id: 1050094, size: 0x8
class :: {

  static _ DateOnlyCompare.isSameDate(/* No info */) {
    // ** addr: 0x90f65c, size: 0x1e4
    // 0x90f65c: EnterFrame
    //     0x90f65c: stp             fp, lr, [SP, #-0x10]!
    //     0x90f660: mov             fp, SP
    // 0x90f664: AllocStack(0x18)
    //     0x90f664: sub             SP, SP, #0x18
    // 0x90f668: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x90f668: mov             x0, x2
    //     0x90f66c: stur            x2, [fp, #-0x10]
    //     0x90f670: mov             x2, x1
    //     0x90f674: stur            x1, [fp, #-8]
    // 0x90f678: CheckStackOverflow
    //     0x90f678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f67c: cmp             SP, x16
    //     0x90f680: b.ls            #0x90f820
    // 0x90f684: mov             x1, x2
    // 0x90f688: r0 = _parts()
    //     0x90f688: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f68c: mov             x2, x0
    // 0x90f690: LoadField: r0 = r2->field_b
    //     0x90f690: ldur            w0, [x2, #0xb]
    // 0x90f694: r1 = LoadInt32Instr(r0)
    //     0x90f694: sbfx            x1, x0, #1, #0x1f
    // 0x90f698: mov             x0, x1
    // 0x90f69c: r1 = 8
    //     0x90f69c: movz            x1, #0x8
    // 0x90f6a0: cmp             x1, x0
    // 0x90f6a4: b.hs            #0x90f828
    // 0x90f6a8: LoadField: r0 = r2->field_2f
    //     0x90f6a8: ldur            w0, [x2, #0x2f]
    // 0x90f6ac: DecompressPointer r0
    //     0x90f6ac: add             x0, x0, HEAP, lsl #32
    // 0x90f6b0: ldur            x1, [fp, #-0x10]
    // 0x90f6b4: stur            x0, [fp, #-0x18]
    // 0x90f6b8: r0 = _parts()
    //     0x90f6b8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f6bc: mov             x2, x0
    // 0x90f6c0: LoadField: r0 = r2->field_b
    //     0x90f6c0: ldur            w0, [x2, #0xb]
    // 0x90f6c4: r1 = LoadInt32Instr(r0)
    //     0x90f6c4: sbfx            x1, x0, #1, #0x1f
    // 0x90f6c8: mov             x0, x1
    // 0x90f6cc: r1 = 8
    //     0x90f6cc: movz            x1, #0x8
    // 0x90f6d0: cmp             x1, x0
    // 0x90f6d4: b.hs            #0x90f82c
    // 0x90f6d8: LoadField: r0 = r2->field_2f
    //     0x90f6d8: ldur            w0, [x2, #0x2f]
    // 0x90f6dc: DecompressPointer r0
    //     0x90f6dc: add             x0, x0, HEAP, lsl #32
    // 0x90f6e0: ldur            x1, [fp, #-0x18]
    // 0x90f6e4: r2 = LoadInt32Instr(r1)
    //     0x90f6e4: sbfx            x2, x1, #1, #0x1f
    //     0x90f6e8: tbz             w1, #0, #0x90f6f0
    //     0x90f6ec: ldur            x2, [x1, #7]
    // 0x90f6f0: r1 = LoadInt32Instr(r0)
    //     0x90f6f0: sbfx            x1, x0, #1, #0x1f
    //     0x90f6f4: tbz             w0, #0, #0x90f6fc
    //     0x90f6f8: ldur            x1, [x0, #7]
    // 0x90f6fc: cmp             x2, x1
    // 0x90f700: b.ne            #0x90f810
    // 0x90f704: ldur            x1, [fp, #-8]
    // 0x90f708: r0 = _parts()
    //     0x90f708: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f70c: mov             x2, x0
    // 0x90f710: LoadField: r0 = r2->field_b
    //     0x90f710: ldur            w0, [x2, #0xb]
    // 0x90f714: r1 = LoadInt32Instr(r0)
    //     0x90f714: sbfx            x1, x0, #1, #0x1f
    // 0x90f718: mov             x0, x1
    // 0x90f71c: r1 = 7
    //     0x90f71c: movz            x1, #0x7
    // 0x90f720: cmp             x1, x0
    // 0x90f724: b.hs            #0x90f830
    // 0x90f728: LoadField: r0 = r2->field_2b
    //     0x90f728: ldur            w0, [x2, #0x2b]
    // 0x90f72c: DecompressPointer r0
    //     0x90f72c: add             x0, x0, HEAP, lsl #32
    // 0x90f730: ldur            x1, [fp, #-0x10]
    // 0x90f734: stur            x0, [fp, #-0x18]
    // 0x90f738: r0 = _parts()
    //     0x90f738: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f73c: mov             x2, x0
    // 0x90f740: LoadField: r0 = r2->field_b
    //     0x90f740: ldur            w0, [x2, #0xb]
    // 0x90f744: r1 = LoadInt32Instr(r0)
    //     0x90f744: sbfx            x1, x0, #1, #0x1f
    // 0x90f748: mov             x0, x1
    // 0x90f74c: r1 = 7
    //     0x90f74c: movz            x1, #0x7
    // 0x90f750: cmp             x1, x0
    // 0x90f754: b.hs            #0x90f834
    // 0x90f758: LoadField: r0 = r2->field_2b
    //     0x90f758: ldur            w0, [x2, #0x2b]
    // 0x90f75c: DecompressPointer r0
    //     0x90f75c: add             x0, x0, HEAP, lsl #32
    // 0x90f760: ldur            x1, [fp, #-0x18]
    // 0x90f764: r2 = LoadInt32Instr(r1)
    //     0x90f764: sbfx            x2, x1, #1, #0x1f
    //     0x90f768: tbz             w1, #0, #0x90f770
    //     0x90f76c: ldur            x2, [x1, #7]
    // 0x90f770: r1 = LoadInt32Instr(r0)
    //     0x90f770: sbfx            x1, x0, #1, #0x1f
    //     0x90f774: tbz             w0, #0, #0x90f77c
    //     0x90f778: ldur            x1, [x0, #7]
    // 0x90f77c: cmp             x2, x1
    // 0x90f780: b.ne            #0x90f810
    // 0x90f784: ldur            x1, [fp, #-8]
    // 0x90f788: r0 = _parts()
    //     0x90f788: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f78c: mov             x2, x0
    // 0x90f790: LoadField: r0 = r2->field_b
    //     0x90f790: ldur            w0, [x2, #0xb]
    // 0x90f794: r1 = LoadInt32Instr(r0)
    //     0x90f794: sbfx            x1, x0, #1, #0x1f
    // 0x90f798: mov             x0, x1
    // 0x90f79c: r1 = 5
    //     0x90f79c: movz            x1, #0x5
    // 0x90f7a0: cmp             x1, x0
    // 0x90f7a4: b.hs            #0x90f838
    // 0x90f7a8: LoadField: r0 = r2->field_23
    //     0x90f7a8: ldur            w0, [x2, #0x23]
    // 0x90f7ac: DecompressPointer r0
    //     0x90f7ac: add             x0, x0, HEAP, lsl #32
    // 0x90f7b0: ldur            x1, [fp, #-0x10]
    // 0x90f7b4: stur            x0, [fp, #-8]
    // 0x90f7b8: r0 = _parts()
    //     0x90f7b8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x90f7bc: mov             x2, x0
    // 0x90f7c0: LoadField: r3 = r2->field_b
    //     0x90f7c0: ldur            w3, [x2, #0xb]
    // 0x90f7c4: r0 = LoadInt32Instr(r3)
    //     0x90f7c4: sbfx            x0, x3, #1, #0x1f
    // 0x90f7c8: r1 = 5
    //     0x90f7c8: movz            x1, #0x5
    // 0x90f7cc: cmp             x1, x0
    // 0x90f7d0: b.hs            #0x90f83c
    // 0x90f7d4: LoadField: r1 = r2->field_23
    //     0x90f7d4: ldur            w1, [x2, #0x23]
    // 0x90f7d8: DecompressPointer r1
    //     0x90f7d8: add             x1, x1, HEAP, lsl #32
    // 0x90f7dc: ldur            x2, [fp, #-8]
    // 0x90f7e0: r3 = LoadInt32Instr(r2)
    //     0x90f7e0: sbfx            x3, x2, #1, #0x1f
    //     0x90f7e4: tbz             w2, #0, #0x90f7ec
    //     0x90f7e8: ldur            x3, [x2, #7]
    // 0x90f7ec: r2 = LoadInt32Instr(r1)
    //     0x90f7ec: sbfx            x2, x1, #1, #0x1f
    //     0x90f7f0: tbz             w1, #0, #0x90f7f8
    //     0x90f7f4: ldur            x2, [x1, #7]
    // 0x90f7f8: cmp             x3, x2
    // 0x90f7fc: r16 = true
    //     0x90f7fc: add             x16, NULL, #0x20  ; true
    // 0x90f800: r17 = false
    //     0x90f800: add             x17, NULL, #0x30  ; false
    // 0x90f804: csel            x1, x16, x17, eq
    // 0x90f808: mov             x0, x1
    // 0x90f80c: b               #0x90f814
    // 0x90f810: r0 = false
    //     0x90f810: add             x0, NULL, #0x30  ; false
    // 0x90f814: LeaveFrame
    //     0x90f814: mov             SP, fp
    //     0x90f818: ldp             fp, lr, [SP], #0x10
    // 0x90f81c: ret
    //     0x90f81c: ret             
    // 0x90f820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90f820: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90f824: b               #0x90f684
    // 0x90f828: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f828: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90f82c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f82c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90f830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f830: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90f834: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f834: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90f838: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f838: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90f83c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90f83c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 1082, size: 0x10, field offset: 0x8
class PriceRepository extends Object {

  _ updatePrice(/* No info */) async {
    // ** addr: 0x90f024, size: 0xe8
    // 0x90f024: EnterFrame
    //     0x90f024: stp             fp, lr, [SP, #-0x10]!
    //     0x90f028: mov             fp, SP
    // 0x90f02c: AllocStack(0x20)
    //     0x90f02c: sub             SP, SP, #0x20
    // 0x90f030: SetupParameters(PriceRepository this /* r1 => r1, fp-0x10 */)
    //     0x90f030: stur            NULL, [fp, #-8]
    //     0x90f034: stur            x1, [fp, #-0x10]
    // 0x90f038: CheckStackOverflow
    //     0x90f038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f03c: cmp             SP, x16
    //     0x90f040: b.ls            #0x90f104
    // 0x90f044: InitAsync() -> Future<void?>
    //     0x90f044: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x90f048: bl              #0x661298  ; InitAsyncStub
    // 0x90f04c: r0 = DateTime()
    //     0x90f04c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x90f050: mov             x1, x0
    // 0x90f054: r0 = false
    //     0x90f054: add             x0, NULL, #0x30  ; false
    // 0x90f058: stur            x1, [fp, #-0x18]
    // 0x90f05c: StoreField: r1->field_13 = r0
    //     0x90f05c: stur            w0, [x1, #0x13]
    // 0x90f060: r0 = _getCurrentMicros()
    //     0x90f060: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x90f064: r1 = LoadInt32Instr(r0)
    //     0x90f064: sbfx            x1, x0, #1, #0x1f
    //     0x90f068: tbz             w0, #0, #0x90f070
    //     0x90f06c: ldur            x1, [x0, #7]
    // 0x90f070: ldur            x2, [fp, #-0x18]
    // 0x90f074: StoreField: r2->field_7 = r1
    //     0x90f074: stur            x1, [x2, #7]
    // 0x90f078: ldur            x0, [fp, #-0x10]
    // 0x90f07c: LoadField: r1 = r0->field_b
    //     0x90f07c: ldur            w1, [x0, #0xb]
    // 0x90f080: DecompressPointer r1
    //     0x90f080: add             x1, x1, HEAP, lsl #32
    // 0x90f084: r0 = _price()
    //     0x90f084: bl              #0x90fa54  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_price
    // 0x90f088: mov             x1, x0
    // 0x90f08c: r0 = val()
    //     0x90f08c: bl              #0x7ec97c  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val
    // 0x90f090: mov             x2, x0
    // 0x90f094: r1 = Null
    //     0x90f094: mov             x1, NULL
    // 0x90f098: r0 = Price.fromMap()
    //     0x90f098: bl              #0x90f840  ; [package:nuonline/app/data/models/price.dart] Price::Price.fromMap
    // 0x90f09c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x90f09c: ldur            w1, [x0, #0x17]
    // 0x90f0a0: DecompressPointer r1
    //     0x90f0a0: add             x1, x1, HEAP, lsl #32
    // 0x90f0a4: stur            x1, [fp, #-0x20]
    // 0x90f0a8: r0 = InitLateStaticField(0x158c) // [package:nuonline/app/data/models/price.dart] Price::defaultLastUpdate
    //     0x90f0a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x90f0ac: ldr             x0, [x0, #0x2b18]
    //     0x90f0b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x90f0b4: cmp             w0, w16
    //     0x90f0b8: b.ne            #0x90f0c8
    //     0x90f0bc: add             x2, PP, #0x27, lsl #12  ; [pp+0x27348] Field <Price.defaultLastUpdate>: static late final (offset: 0x158c)
    //     0x90f0c0: ldr             x2, [x2, #0x348]
    //     0x90f0c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x90f0c8: ldur            x1, [fp, #-0x20]
    // 0x90f0cc: mov             x2, x0
    // 0x90f0d0: r0 = DateOnlyCompare.isSameDate()
    //     0x90f0d0: bl              #0x90f65c  ; [package:nuonline/app/data/repositories/price_repository.dart] ::DateOnlyCompare.isSameDate
    // 0x90f0d4: tbz             w0, #4, #0x90f0e8
    // 0x90f0d8: ldur            x1, [fp, #-0x20]
    // 0x90f0dc: ldur            x2, [fp, #-0x18]
    // 0x90f0e0: r0 = DateOnlyCompare.isSameDate()
    //     0x90f0e0: bl              #0x90f65c  ; [package:nuonline/app/data/repositories/price_repository.dart] ::DateOnlyCompare.isSameDate
    // 0x90f0e4: tbz             w0, #4, #0x90f0fc
    // 0x90f0e8: ldur            x1, [fp, #-0x10]
    // 0x90f0ec: r0 = _updatePrice()
    //     0x90f0ec: bl              #0x90f14c  ; [package:nuonline/app/data/repositories/price_repository.dart] PriceRepository::_updatePrice
    // 0x90f0f0: mov             x1, x0
    // 0x90f0f4: stur            x1, [fp, #-0x10]
    // 0x90f0f8: r0 = Await()
    //     0x90f0f8: bl              #0x661044  ; AwaitStub
    // 0x90f0fc: r0 = Null
    //     0x90f0fc: mov             x0, NULL
    // 0x90f100: r0 = ReturnAsyncNotFuture()
    //     0x90f100: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x90f104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90f104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90f108: b               #0x90f044
  }
  _ _updatePrice(/* No info */) async {
    // ** addr: 0x90f14c, size: 0x120
    // 0x90f14c: EnterFrame
    //     0x90f14c: stp             fp, lr, [SP, #-0x10]!
    //     0x90f150: mov             fp, SP
    // 0x90f154: AllocStack(0x68)
    //     0x90f154: sub             SP, SP, #0x68
    // 0x90f158: SetupParameters(PriceRepository this /* r1 => r1, fp-0x48 */)
    //     0x90f158: stur            NULL, [fp, #-8]
    //     0x90f15c: stur            x1, [fp, #-0x48]
    // 0x90f160: CheckStackOverflow
    //     0x90f160: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90f164: cmp             SP, x16
    //     0x90f168: b.ls            #0x90f264
    // 0x90f16c: InitAsync() -> Future<void?>
    //     0x90f16c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x90f170: bl              #0x661298  ; InitAsyncStub
    // 0x90f174: ldur            x0, [fp, #-0x48]
    // 0x90f178: LoadField: r1 = r0->field_7
    //     0x90f178: ldur            w1, [x0, #7]
    // 0x90f17c: DecompressPointer r1
    //     0x90f17c: add             x1, x1, HEAP, lsl #32
    // 0x90f180: stp             x1, NULL, [SP, #8]
    // 0x90f184: r16 = "/gold-price"
    //     0x90f184: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c260] "/gold-price"
    //     0x90f188: ldr             x16, [x16, #0x260]
    // 0x90f18c: str             x16, [SP]
    // 0x90f190: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90f190: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90f194: r0 = get()
    //     0x90f194: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x90f198: mov             x1, x0
    // 0x90f19c: stur            x1, [fp, #-0x50]
    // 0x90f1a0: r0 = Await()
    //     0x90f1a0: bl              #0x661044  ; AwaitStub
    // 0x90f1a4: mov             x1, x0
    // 0x90f1a8: ldur            x0, [fp, #-0x48]
    // 0x90f1ac: LoadField: r3 = r0->field_b
    //     0x90f1ac: ldur            w3, [x0, #0xb]
    // 0x90f1b0: DecompressPointer r3
    //     0x90f1b0: add             x3, x3, HEAP, lsl #32
    // 0x90f1b4: stur            x3, [fp, #-0x50]
    // 0x90f1b8: LoadField: r4 = r1->field_b
    //     0x90f1b8: ldur            w4, [x1, #0xb]
    // 0x90f1bc: DecompressPointer r4
    //     0x90f1bc: add             x4, x4, HEAP, lsl #32
    // 0x90f1c0: mov             x0, x4
    // 0x90f1c4: stur            x4, [fp, #-0x48]
    // 0x90f1c8: r2 = Null
    //     0x90f1c8: mov             x2, NULL
    // 0x90f1cc: r1 = Null
    //     0x90f1cc: mov             x1, NULL
    // 0x90f1d0: r8 = Map<String, dynamic>
    //     0x90f1d0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x90f1d4: r3 = Null
    //     0x90f1d4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c268] Null
    //     0x90f1d8: ldr             x3, [x3, #0x268]
    // 0x90f1dc: r0 = Map<String, dynamic>()
    //     0x90f1dc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x90f1e0: ldur            x2, [fp, #-0x48]
    // 0x90f1e4: r1 = Null
    //     0x90f1e4: mov             x1, NULL
    // 0x90f1e8: r0 = Price.fromJson()
    //     0x90f1e8: bl              #0x90f3b0  ; [package:nuonline/app/data/models/price.dart] Price::Price.fromJson
    // 0x90f1ec: ldur            x1, [fp, #-0x50]
    // 0x90f1f0: mov             x2, x0
    // 0x90f1f4: stur            x0, [fp, #-0x48]
    // 0x90f1f8: r0 = price=()
    //     0x90f1f8: bl              #0x90f26c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::price=
    // 0x90f1fc: b               #0x90f25c
    // 0x90f200: sub             SP, fp, #0x68
    // 0x90f204: stur            x0, [fp, #-0x48]
    // 0x90f208: r0 = InitLateStaticField(0x674) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x90f208: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x90f20c: ldr             x0, [x0, #0xce8]
    //     0x90f210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x90f214: cmp             w0, w16
    //     0x90f218: b.ne            #0x90f224
    //     0x90f21c: ldr             x2, [PP, #0x490]  ; [pp+0x490] Field <::.debugPrint>: static late (offset: 0x674)
    //     0x90f220: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x90f224: r1 = Null
    //     0x90f224: mov             x1, NULL
    // 0x90f228: r2 = 4
    //     0x90f228: movz            x2, #0x4
    // 0x90f22c: r0 = AllocateArray()
    //     0x90f22c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90f230: r16 = "Failed to update gold price : "
    //     0x90f230: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c278] "Failed to update gold price : "
    //     0x90f234: ldr             x16, [x16, #0x278]
    // 0x90f238: StoreField: r0->field_f = r16
    //     0x90f238: stur            w16, [x0, #0xf]
    // 0x90f23c: ldur            x1, [fp, #-0x48]
    // 0x90f240: StoreField: r0->field_13 = r1
    //     0x90f240: stur            w1, [x0, #0x13]
    // 0x90f244: str             x0, [SP]
    // 0x90f248: r0 = _interpolate()
    //     0x90f248: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x90f24c: str             NULL, [SP]
    // 0x90f250: mov             x1, x0
    // 0x90f254: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x90f254: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x90f258: r0 = debugPrintThrottled()
    //     0x90f258: bl              #0x63fa18  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x90f25c: r0 = Null
    //     0x90f25c: mov             x0, NULL
    // 0x90f260: r0 = ReturnAsyncNotFuture()
    //     0x90f260: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x90f264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90f264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90f268: b               #0x90f16c
  }
}
