// lib: , url: package:nuonline/app/data/repositories/qurban_repository.dart

// class id: 1050095, size: 0x8
class :: {
}

// class id: 1081, size: 0xc, field offset: 0x8
class QurbanRepository extends Object {

  _ find(/* No info */) async {
    // ** addr: 0x7e9cfc, size: 0xe4
    // 0x7e9cfc: EnterFrame
    //     0x7e9cfc: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9d00: mov             fp, SP
    // 0x7e9d04: AllocStack(0x68)
    //     0x7e9d04: sub             SP, SP, #0x68
    // 0x7e9d08: SetupParameters(QurbanRepository this /* r1 => r1, fp-0x50 */)
    //     0x7e9d08: stur            NULL, [fp, #-8]
    //     0x7e9d0c: stur            x1, [fp, #-0x50]
    // 0x7e9d10: CheckStackOverflow
    //     0x7e9d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9d14: cmp             SP, x16
    //     0x7e9d18: b.ls            #0x7e9dd8
    // 0x7e9d1c: InitAsync() -> Future<ApiResult<Qurban>>
    //     0x7e9d1c: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3d1f8] TypeArguments: <ApiResult<Qurban>>
    //     0x7e9d20: ldr             x0, [x0, #0x1f8]
    //     0x7e9d24: bl              #0x661298  ; InitAsyncStub
    // 0x7e9d28: ldur            x0, [fp, #-0x50]
    // 0x7e9d2c: LoadField: r1 = r0->field_7
    //     0x7e9d2c: ldur            w1, [x0, #7]
    // 0x7e9d30: DecompressPointer r1
    //     0x7e9d30: add             x1, x1, HEAP, lsl #32
    // 0x7e9d34: stp             x1, NULL, [SP, #8]
    // 0x7e9d38: r16 = "/qurban"
    //     0x7e9d38: add             x16, PP, #0x24, lsl #12  ; [pp+0x24820] "/qurban"
    //     0x7e9d3c: ldr             x16, [x16, #0x820]
    // 0x7e9d40: str             x16, [SP]
    // 0x7e9d44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e9d44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e9d48: r0 = get()
    //     0x7e9d48: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x7e9d4c: mov             x1, x0
    // 0x7e9d50: stur            x1, [fp, #-0x50]
    // 0x7e9d54: r0 = Await()
    //     0x7e9d54: bl              #0x661044  ; AwaitStub
    // 0x7e9d58: LoadField: r3 = r0->field_b
    //     0x7e9d58: ldur            w3, [x0, #0xb]
    // 0x7e9d5c: DecompressPointer r3
    //     0x7e9d5c: add             x3, x3, HEAP, lsl #32
    // 0x7e9d60: mov             x0, x3
    // 0x7e9d64: stur            x3, [fp, #-0x50]
    // 0x7e9d68: r2 = Null
    //     0x7e9d68: mov             x2, NULL
    // 0x7e9d6c: r1 = Null
    //     0x7e9d6c: mov             x1, NULL
    // 0x7e9d70: r8 = Map<String, dynamic>
    //     0x7e9d70: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e9d74: r3 = Null
    //     0x7e9d74: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d200] Null
    //     0x7e9d78: ldr             x3, [x3, #0x200]
    // 0x7e9d7c: r0 = Map<String, dynamic>()
    //     0x7e9d7c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e9d80: ldur            x2, [fp, #-0x50]
    // 0x7e9d84: r1 = Null
    //     0x7e9d84: mov             x1, NULL
    // 0x7e9d88: r0 = Qurban.fromMap()
    //     0x7e9d88: bl              #0x7e9de0  ; [package:nuonline/app/data/models/qurban.dart] Qurban::Qurban.fromMap
    // 0x7e9d8c: r1 = <Qurban>
    //     0x7e9d8c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32808] TypeArguments: <Qurban>
    //     0x7e9d90: ldr             x1, [x1, #0x808]
    // 0x7e9d94: stur            x0, [fp, #-0x50]
    // 0x7e9d98: r0 = _$SuccessImpl()
    //     0x7e9d98: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e9d9c: mov             x1, x0
    // 0x7e9da0: ldur            x0, [fp, #-0x50]
    // 0x7e9da4: StoreField: r1->field_b = r0
    //     0x7e9da4: stur            w0, [x1, #0xb]
    // 0x7e9da8: mov             x0, x1
    // 0x7e9dac: r0 = ReturnAsyncNotFuture()
    //     0x7e9dac: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e9db0: sub             SP, fp, #0x68
    // 0x7e9db4: mov             x1, x0
    // 0x7e9db8: r0 = getDioException()
    //     0x7e9db8: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e9dbc: r1 = <Qurban>
    //     0x7e9dbc: add             x1, PP, #0x32, lsl #12  ; [pp+0x32808] TypeArguments: <Qurban>
    //     0x7e9dc0: ldr             x1, [x1, #0x808]
    // 0x7e9dc4: stur            x0, [fp, #-0x50]
    // 0x7e9dc8: r0 = _$FailureImpl()
    //     0x7e9dc8: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e9dcc: ldur            x1, [fp, #-0x50]
    // 0x7e9dd0: StoreField: r0->field_b = r1
    //     0x7e9dd0: stur            w1, [x0, #0xb]
    // 0x7e9dd4: r0 = ReturnAsyncNotFuture()
    //     0x7e9dd4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e9dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9dd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9ddc: b               #0x7e9d1c
  }
}
