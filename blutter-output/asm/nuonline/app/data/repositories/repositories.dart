// lib: , url: package:nuonline/app/data/repositories/repositories.dart

// class id: 1050096, size: 0x8
class :: {
}

// class id: 1080, size: 0x8, field offset: 0x8
class RepositoriesBinding extends Object
    implements Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x865174, size: 0x87c
    // 0x865174: EnterFrame
    //     0x865174: stp             fp, lr, [SP, #-0x10]!
    //     0x865178: mov             fp, SP
    // 0x86517c: AllocStack(0x20)
    //     0x86517c: sub             SP, SP, #0x20
    // 0x865180: CheckStackOverflow
    //     0x865180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865184: cmp             SP, x16
    //     0x865188: b.ls            #0x8659e8
    // 0x86518c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x86518c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865190: ldr             x0, [x0, #0x2670]
    //     0x865194: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865198: cmp             w0, w16
    //     0x86519c: b.ne            #0x8651a8
    //     0x8651a0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8651a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8651a8: r1 = Function '<anonymous closure>':.
    //     0x8651a8: add             x1, PP, #0xf, lsl #12  ; [pp+0xff20] AnonymousClosure: (0x867744), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8651ac: ldr             x1, [x1, #0xf20]
    // 0x8651b0: r2 = Null
    //     0x8651b0: mov             x2, NULL
    // 0x8651b4: r0 = AllocateClosure()
    //     0x8651b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8651b8: r16 = <EventRepository>
    //     0x8651b8: add             x16, PP, #0xf, lsl #12  ; [pp+0xff28] TypeArguments: <EventRepository>
    //     0x8651bc: ldr             x16, [x16, #0xf28]
    // 0x8651c0: stp             x0, x16, [SP, #0x10]
    // 0x8651c4: r16 = true
    //     0x8651c4: add             x16, NULL, #0x20  ; true
    // 0x8651c8: r30 = "event_repo"
    //     0x8651c8: add             lr, PP, #0xf, lsl #12  ; [pp+0xff30] "event_repo"
    //     0x8651cc: ldr             lr, [lr, #0xf30]
    // 0x8651d0: stp             lr, x16, [SP]
    // 0x8651d4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8651d4: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8651d8: ldr             x4, [x4, #0xf38]
    // 0x8651dc: r0 = Inst.lazyPut()
    //     0x8651dc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8651e0: r1 = Function '<anonymous closure>':.
    //     0x8651e0: add             x1, PP, #0xf, lsl #12  ; [pp+0xff40] AnonymousClosure: (0x867684), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8651e4: ldr             x1, [x1, #0xf40]
    // 0x8651e8: r2 = Null
    //     0x8651e8: mov             x2, NULL
    // 0x8651ec: r0 = AllocateClosure()
    //     0x8651ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x8651f0: r16 = <MenuRepository>
    //     0x8651f0: add             x16, PP, #0xf, lsl #12  ; [pp+0xff48] TypeArguments: <MenuRepository>
    //     0x8651f4: ldr             x16, [x16, #0xf48]
    // 0x8651f8: stp             x0, x16, [SP, #0x10]
    // 0x8651fc: r16 = true
    //     0x8651fc: add             x16, NULL, #0x20  ; true
    // 0x865200: r30 = "menu_repo"
    //     0x865200: add             lr, PP, #0xf, lsl #12  ; [pp+0xff50] "menu_repo"
    //     0x865204: ldr             lr, [lr, #0xf50]
    // 0x865208: stp             lr, x16, [SP]
    // 0x86520c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86520c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865210: ldr             x4, [x4, #0xf38]
    // 0x865214: r0 = Inst.lazyPut()
    //     0x865214: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865218: r1 = Function '<anonymous closure>':.
    //     0x865218: add             x1, PP, #0xf, lsl #12  ; [pp+0xff58] AnonymousClosure: (0x8675a0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86521c: ldr             x1, [x1, #0xf58]
    // 0x865220: r2 = Null
    //     0x865220: mov             x2, NULL
    // 0x865224: r0 = AllocateClosure()
    //     0x865224: bl              #0xec1630  ; AllocateClosureStub
    // 0x865228: r16 = <HomeRepository>
    //     0x865228: add             x16, PP, #0xf, lsl #12  ; [pp+0xff60] TypeArguments: <HomeRepository>
    //     0x86522c: ldr             x16, [x16, #0xf60]
    // 0x865230: stp             x0, x16, [SP, #0x10]
    // 0x865234: r16 = true
    //     0x865234: add             x16, NULL, #0x20  ; true
    // 0x865238: r30 = "home_repo"
    //     0x865238: add             lr, PP, #0xf, lsl #12  ; [pp+0xff68] "home_repo"
    //     0x86523c: ldr             lr, [lr, #0xf68]
    // 0x865240: stp             lr, x16, [SP]
    // 0x865244: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865244: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865248: ldr             x4, [x4, #0xf38]
    // 0x86524c: r0 = Inst.lazyPut()
    //     0x86524c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865250: r1 = Function '<anonymous closure>':.
    //     0x865250: add             x1, PP, #0xf, lsl #12  ; [pp+0xff70] AnonymousClosure: (0x8674e4), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865254: ldr             x1, [x1, #0xf70]
    // 0x865258: r2 = Null
    //     0x865258: mov             x2, NULL
    // 0x86525c: r0 = AllocateClosure()
    //     0x86525c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865260: r16 = <LocationRepository>
    //     0x865260: add             x16, PP, #0xf, lsl #12  ; [pp+0xff78] TypeArguments: <LocationRepository>
    //     0x865264: ldr             x16, [x16, #0xf78]
    // 0x865268: stp             x0, x16, [SP, #0x10]
    // 0x86526c: r16 = true
    //     0x86526c: add             x16, NULL, #0x20  ; true
    // 0x865270: r30 = "location_repo"
    //     0x865270: add             lr, PP, #0xf, lsl #12  ; [pp+0xff80] "location_repo"
    //     0x865274: ldr             lr, [lr, #0xf80]
    // 0x865278: stp             lr, x16, [SP]
    // 0x86527c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86527c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865280: ldr             x4, [x4, #0xf38]
    // 0x865284: r0 = Inst.lazyPut()
    //     0x865284: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865288: r1 = Function '<anonymous closure>':.
    //     0x865288: add             x1, PP, #0xf, lsl #12  ; [pp+0xff88] AnonymousClosure: (0x866c48), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86528c: ldr             x1, [x1, #0xf88]
    // 0x865290: r2 = Null
    //     0x865290: mov             x2, NULL
    // 0x865294: r0 = AllocateClosure()
    //     0x865294: bl              #0xec1630  ; AllocateClosureStub
    // 0x865298: r16 = <TafsirRepository>
    //     0x865298: add             x16, PP, #0xf, lsl #12  ; [pp+0xff90] TypeArguments: <TafsirRepository>
    //     0x86529c: ldr             x16, [x16, #0xf90]
    // 0x8652a0: stp             x0, x16, [SP, #0x10]
    // 0x8652a4: r16 = true
    //     0x8652a4: add             x16, NULL, #0x20  ; true
    // 0x8652a8: r30 = "tafsir_repo"
    //     0x8652a8: add             lr, PP, #0xf, lsl #12  ; [pp+0xff98] "tafsir_repo"
    //     0x8652ac: ldr             lr, [lr, #0xf98]
    // 0x8652b0: stp             lr, x16, [SP]
    // 0x8652b4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8652b4: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8652b8: ldr             x4, [x4, #0xf38]
    // 0x8652bc: r0 = Inst.lazyPut()
    //     0x8652bc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8652c0: r1 = Function '<anonymous closure>':.
    //     0x8652c0: add             x1, PP, #0xf, lsl #12  ; [pp+0xffa0] AnonymousClosure: (0x866ba0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8652c4: ldr             x1, [x1, #0xfa0]
    // 0x8652c8: r2 = Null
    //     0x8652c8: mov             x2, NULL
    // 0x8652cc: r0 = AllocateClosure()
    //     0x8652cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8652d0: r16 = <SurahRepository>
    //     0x8652d0: add             x16, PP, #0xf, lsl #12  ; [pp+0xffa8] TypeArguments: <SurahRepository>
    //     0x8652d4: ldr             x16, [x16, #0xfa8]
    // 0x8652d8: stp             x0, x16, [SP, #0x10]
    // 0x8652dc: r16 = true
    //     0x8652dc: add             x16, NULL, #0x20  ; true
    // 0x8652e0: r30 = "surah_repo"
    //     0x8652e0: add             lr, PP, #0xf, lsl #12  ; [pp+0xffb0] "surah_repo"
    //     0x8652e4: ldr             lr, [lr, #0xfb0]
    // 0x8652e8: stp             lr, x16, [SP]
    // 0x8652ec: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8652ec: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8652f0: ldr             x4, [x4, #0xf38]
    // 0x8652f4: r0 = Inst.lazyPut()
    //     0x8652f4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8652f8: r1 = Function '<anonymous closure>':.
    //     0x8652f8: add             x1, PP, #0xf, lsl #12  ; [pp+0xffb8] AnonymousClosure: (0x866b00), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8652fc: ldr             x1, [x1, #0xfb8]
    // 0x865300: r2 = Null
    //     0x865300: mov             x2, NULL
    // 0x865304: r0 = AllocateClosure()
    //     0x865304: bl              #0xec1630  ; AllocateClosureStub
    // 0x865308: r16 = <PriceRepository>
    //     0x865308: add             x16, PP, #0xf, lsl #12  ; [pp+0xffc0] TypeArguments: <PriceRepository>
    //     0x86530c: ldr             x16, [x16, #0xfc0]
    // 0x865310: stp             x0, x16, [SP, #0x10]
    // 0x865314: r16 = true
    //     0x865314: add             x16, NULL, #0x20  ; true
    // 0x865318: r30 = "price_repo"
    //     0x865318: add             lr, PP, #0xf, lsl #12  ; [pp+0xffc8] "price_repo"
    //     0x86531c: ldr             lr, [lr, #0xfc8]
    // 0x865320: stp             lr, x16, [SP]
    // 0x865324: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865324: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865328: ldr             x4, [x4, #0xf38]
    // 0x86532c: r0 = Inst.lazyPut()
    //     0x86532c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865330: r1 = Function '<anonymous closure>':.
    //     0x865330: add             x1, PP, #0xf, lsl #12  ; [pp+0xffd0] AnonymousClosure: (0x866a80), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865334: ldr             x1, [x1, #0xfd0]
    // 0x865338: r2 = Null
    //     0x865338: mov             x2, NULL
    // 0x86533c: r0 = AllocateClosure()
    //     0x86533c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865340: r16 = <DonorRepository>
    //     0x865340: add             x16, PP, #0xf, lsl #12  ; [pp+0xffd8] TypeArguments: <DonorRepository>
    //     0x865344: ldr             x16, [x16, #0xfd8]
    // 0x865348: stp             x0, x16, [SP, #0x10]
    // 0x86534c: r16 = true
    //     0x86534c: add             x16, NULL, #0x20  ; true
    // 0x865350: r30 = "donor_repo"
    //     0x865350: add             lr, PP, #0xf, lsl #12  ; [pp+0xffe0] "donor_repo"
    //     0x865354: ldr             lr, [lr, #0xfe0]
    // 0x865358: stp             lr, x16, [SP]
    // 0x86535c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86535c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865360: ldr             x4, [x4, #0xf38]
    // 0x865364: r0 = Inst.lazyPut()
    //     0x865364: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865368: r1 = Function '<anonymous closure>':.
    //     0x865368: add             x1, PP, #0xf, lsl #12  ; [pp+0xffe8] AnonymousClosure: (0x8669d8), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86536c: ldr             x1, [x1, #0xfe8]
    // 0x865370: r2 = Null
    //     0x865370: mov             x2, NULL
    // 0x865374: r0 = AllocateClosure()
    //     0x865374: bl              #0xec1630  ; AllocateClosureStub
    // 0x865378: r16 = <JuzRepository>
    //     0x865378: add             x16, PP, #0xf, lsl #12  ; [pp+0xfff0] TypeArguments: <JuzRepository>
    //     0x86537c: ldr             x16, [x16, #0xff0]
    // 0x865380: stp             x0, x16, [SP, #0x10]
    // 0x865384: r16 = true
    //     0x865384: add             x16, NULL, #0x20  ; true
    // 0x865388: r30 = "juz_repo"
    //     0x865388: add             lr, PP, #0xf, lsl #12  ; [pp+0xfff8] "juz_repo"
    //     0x86538c: ldr             lr, [lr, #0xff8]
    // 0x865390: stp             lr, x16, [SP]
    // 0x865394: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865394: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865398: ldr             x4, [x4, #0xf38]
    // 0x86539c: r0 = Inst.lazyPut()
    //     0x86539c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8653a0: r1 = Function '<anonymous closure>':.
    //     0x8653a0: add             x1, PP, #0x10, lsl #12  ; [pp+0x10000] AnonymousClosure: (0x866940), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8653a4: ldr             x1, [x1]
    // 0x8653a8: r2 = Null
    //     0x8653a8: mov             x2, NULL
    // 0x8653ac: r0 = AllocateClosure()
    //     0x8653ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x8653b0: r16 = <PrayerTimeRepository>
    //     0x8653b0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10008] TypeArguments: <PrayerTimeRepository>
    //     0x8653b4: ldr             x16, [x16, #8]
    // 0x8653b8: stp             x0, x16, [SP, #0x10]
    // 0x8653bc: r16 = true
    //     0x8653bc: add             x16, NULL, #0x20  ; true
    // 0x8653c0: r30 = "prayer_time_repo"
    //     0x8653c0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10010] "prayer_time_repo"
    //     0x8653c4: ldr             lr, [lr, #0x10]
    // 0x8653c8: stp             lr, x16, [SP]
    // 0x8653cc: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8653cc: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8653d0: ldr             x4, [x4, #0xf38]
    // 0x8653d4: r0 = Inst.lazyPut()
    //     0x8653d4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8653d8: r1 = Function '<anonymous closure>':.
    //     0x8653d8: add             x1, PP, #0x10, lsl #12  ; [pp+0x10018] AnonymousClosure: (0x8668c0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8653dc: ldr             x1, [x1, #0x18]
    // 0x8653e0: r2 = Null
    //     0x8653e0: mov             x2, NULL
    // 0x8653e4: r0 = AllocateClosure()
    //     0x8653e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8653e8: r16 = <TahlilRepository>
    //     0x8653e8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10020] TypeArguments: <TahlilRepository>
    //     0x8653ec: ldr             x16, [x16, #0x20]
    // 0x8653f0: stp             x0, x16, [SP, #0x10]
    // 0x8653f4: r16 = true
    //     0x8653f4: add             x16, NULL, #0x20  ; true
    // 0x8653f8: r30 = "tahlil_repo"
    //     0x8653f8: add             lr, PP, #0x10, lsl #12  ; [pp+0x10028] "tahlil_repo"
    //     0x8653fc: ldr             lr, [lr, #0x28]
    // 0x865400: stp             lr, x16, [SP]
    // 0x865404: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865404: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865408: ldr             x4, [x4, #0xf38]
    // 0x86540c: r0 = Inst.lazyPut()
    //     0x86540c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865410: r1 = Function '<anonymous closure>':.
    //     0x865410: add             x1, PP, #0x10, lsl #12  ; [pp+0x10030] AnonymousClosure: (0x866818), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865414: ldr             x1, [x1, #0x30]
    // 0x865418: r2 = Null
    //     0x865418: mov             x2, NULL
    // 0x86541c: r0 = AllocateClosure()
    //     0x86541c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865420: r16 = <VersesRepository>
    //     0x865420: add             x16, PP, #0x10, lsl #12  ; [pp+0x10038] TypeArguments: <VersesRepository>
    //     0x865424: ldr             x16, [x16, #0x38]
    // 0x865428: stp             x0, x16, [SP, #0x10]
    // 0x86542c: r16 = true
    //     0x86542c: add             x16, NULL, #0x20  ; true
    // 0x865430: r30 = "verses_repo"
    //     0x865430: add             lr, PP, #0x10, lsl #12  ; [pp+0x10040] "verses_repo"
    //     0x865434: ldr             lr, [lr, #0x40]
    // 0x865438: stp             lr, x16, [SP]
    // 0x86543c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86543c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865440: ldr             x4, [x4, #0xf38]
    // 0x865444: r0 = Inst.lazyPut()
    //     0x865444: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865448: r1 = Function '<anonymous closure>':.
    //     0x865448: add             x1, PP, #0x10, lsl #12  ; [pp+0x10048] AnonymousClosure: (0x8667a0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86544c: ldr             x1, [x1, #0x48]
    // 0x865450: r2 = Null
    //     0x865450: mov             x2, NULL
    // 0x865454: r0 = AllocateClosure()
    //     0x865454: bl              #0xec1630  ; AllocateClosureStub
    // 0x865458: r16 = <NotificationRepository>
    //     0x865458: add             x16, PP, #0x10, lsl #12  ; [pp+0x10050] TypeArguments: <NotificationRepository>
    //     0x86545c: ldr             x16, [x16, #0x50]
    // 0x865460: stp             x0, x16, [SP, #0x10]
    // 0x865464: r16 = true
    //     0x865464: add             x16, NULL, #0x20  ; true
    // 0x865468: r30 = "notification_repo"
    //     0x865468: add             lr, PP, #0x10, lsl #12  ; [pp+0x10058] "notification_repo"
    //     0x86546c: ldr             lr, [lr, #0x58]
    // 0x865470: stp             lr, x16, [SP]
    // 0x865474: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865474: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865478: ldr             x4, [x4, #0xf38]
    // 0x86547c: r0 = Inst.lazyPut()
    //     0x86547c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865480: r1 = Function '<anonymous closure>':.
    //     0x865480: add             x1, PP, #0x10, lsl #12  ; [pp+0x10060] AnonymousClosure: (0x866700), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865484: ldr             x1, [x1, #0x60]
    // 0x865488: r2 = Null
    //     0x865488: mov             x2, NULL
    // 0x86548c: r0 = AllocateClosure()
    //     0x86548c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865490: r16 = <CategoryRepository>
    //     0x865490: add             x16, PP, #0x10, lsl #12  ; [pp+0x10068] TypeArguments: <CategoryRepository>
    //     0x865494: ldr             x16, [x16, #0x68]
    // 0x865498: stp             x0, x16, [SP, #0x10]
    // 0x86549c: r16 = true
    //     0x86549c: add             x16, NULL, #0x20  ; true
    // 0x8654a0: r30 = "category_repo"
    //     0x8654a0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10070] "category_repo"
    //     0x8654a4: ldr             lr, [lr, #0x70]
    // 0x8654a8: stp             lr, x16, [SP]
    // 0x8654ac: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8654ac: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8654b0: ldr             x4, [x4, #0xf38]
    // 0x8654b4: r0 = Inst.lazyPut()
    //     0x8654b4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8654b8: r1 = Function '<anonymous closure>':.
    //     0x8654b8: add             x1, PP, #0x10, lsl #12  ; [pp+0x10078] AnonymousClosure: (0x866680), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8654bc: ldr             x1, [x1, #0x78]
    // 0x8654c0: r2 = Null
    //     0x8654c0: mov             x2, NULL
    // 0x8654c4: r0 = AllocateClosure()
    //     0x8654c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8654c8: r16 = <PageRepository>
    //     0x8654c8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10080] TypeArguments: <PageRepository>
    //     0x8654cc: ldr             x16, [x16, #0x80]
    // 0x8654d0: stp             x0, x16, [SP, #0x10]
    // 0x8654d4: r16 = true
    //     0x8654d4: add             x16, NULL, #0x20  ; true
    // 0x8654d8: r30 = "page_repo"
    //     0x8654d8: add             lr, PP, #0x10, lsl #12  ; [pp+0x10088] "page_repo"
    //     0x8654dc: ldr             lr, [lr, #0x88]
    // 0x8654e0: stp             lr, x16, [SP]
    // 0x8654e4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8654e4: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8654e8: ldr             x4, [x4, #0xf38]
    // 0x8654ec: r0 = Inst.lazyPut()
    //     0x8654ec: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8654f0: r1 = Function '<anonymous closure>':.
    //     0x8654f0: add             x1, PP, #0x10, lsl #12  ; [pp+0x10090] AnonymousClosure: (0x8665b8), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8654f4: ldr             x1, [x1, #0x90]
    // 0x8654f8: r2 = Null
    //     0x8654f8: mov             x2, NULL
    // 0x8654fc: r0 = AllocateClosure()
    //     0x8654fc: bl              #0xec1630  ; AllocateClosureStub
    // 0x865500: r16 = <ArticleRepository>
    //     0x865500: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x865504: ldr             x16, [x16, #0x98]
    // 0x865508: stp             x0, x16, [SP, #0x10]
    // 0x86550c: r16 = true
    //     0x86550c: add             x16, NULL, #0x20  ; true
    // 0x865510: r30 = "article_repo"
    //     0x865510: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x865514: ldr             lr, [lr, #0xa0]
    // 0x865518: stp             lr, x16, [SP]
    // 0x86551c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86551c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865520: ldr             x4, [x4, #0xf38]
    // 0x865524: r0 = Inst.lazyPut()
    //     0x865524: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865528: r1 = Function '<anonymous closure>':.
    //     0x865528: add             x1, PP, #0x10, lsl #12  ; [pp+0x100a8] AnonymousClosure: (0x866528), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86552c: ldr             x1, [x1, #0xa8]
    // 0x865530: r2 = Null
    //     0x865530: mov             x2, NULL
    // 0x865534: r0 = AllocateClosure()
    //     0x865534: bl              #0xec1630  ; AllocateClosureStub
    // 0x865538: r16 = <DonationRepository>
    //     0x865538: add             x16, PP, #0x10, lsl #12  ; [pp+0x100b0] TypeArguments: <DonationRepository>
    //     0x86553c: ldr             x16, [x16, #0xb0]
    // 0x865540: stp             x0, x16, [SP, #0x10]
    // 0x865544: r16 = true
    //     0x865544: add             x16, NULL, #0x20  ; true
    // 0x865548: r30 = "donation_repo"
    //     0x865548: add             lr, PP, #0x10, lsl #12  ; [pp+0x100b8] "donation_repo"
    //     0x86554c: ldr             lr, [lr, #0xb8]
    // 0x865550: stp             lr, x16, [SP]
    // 0x865554: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865554: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865558: ldr             x4, [x4, #0xf38]
    // 0x86555c: r0 = Inst.lazyPut()
    //     0x86555c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865560: r1 = Function '<anonymous closure>':.
    //     0x865560: add             x1, PP, #0x10, lsl #12  ; [pp+0x100c0] AnonymousClosure: (0x8664a8), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865564: ldr             x1, [x1, #0xc0]
    // 0x865568: r2 = Null
    //     0x865568: mov             x2, NULL
    // 0x86556c: r0 = AllocateClosure()
    //     0x86556c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865570: r16 = <TopicRepository>
    //     0x865570: add             x16, PP, #0x10, lsl #12  ; [pp+0x100c8] TypeArguments: <TopicRepository>
    //     0x865574: ldr             x16, [x16, #0xc8]
    // 0x865578: stp             x0, x16, [SP, #0x10]
    // 0x86557c: r16 = true
    //     0x86557c: add             x16, NULL, #0x20  ; true
    // 0x865580: r30 = "topic_repo"
    //     0x865580: add             lr, PP, #0x10, lsl #12  ; [pp+0x100d0] "topic_repo"
    //     0x865584: ldr             lr, [lr, #0xd0]
    // 0x865588: stp             lr, x16, [SP]
    // 0x86558c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86558c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865590: ldr             x4, [x4, #0xf38]
    // 0x865594: r0 = Inst.lazyPut()
    //     0x865594: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865598: r1 = Function '<anonymous closure>':.
    //     0x865598: add             x1, PP, #0x10, lsl #12  ; [pp+0x100d8] AnonymousClosure: (0x866428), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86559c: ldr             x1, [x1, #0xd8]
    // 0x8655a0: r2 = Null
    //     0x8655a0: mov             x2, NULL
    // 0x8655a4: r0 = AllocateClosure()
    //     0x8655a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8655a8: r16 = <VideoRepository>
    //     0x8655a8: add             x16, PP, #0x10, lsl #12  ; [pp+0x100e0] TypeArguments: <VideoRepository>
    //     0x8655ac: ldr             x16, [x16, #0xe0]
    // 0x8655b0: stp             x0, x16, [SP, #0x10]
    // 0x8655b4: r16 = true
    //     0x8655b4: add             x16, NULL, #0x20  ; true
    // 0x8655b8: r30 = "video_repo"
    //     0x8655b8: add             lr, PP, #0x10, lsl #12  ; [pp+0x100e8] "video_repo"
    //     0x8655bc: ldr             lr, [lr, #0xe8]
    // 0x8655c0: stp             lr, x16, [SP]
    // 0x8655c4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8655c4: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8655c8: ldr             x4, [x4, #0xf38]
    // 0x8655cc: r0 = Inst.lazyPut()
    //     0x8655cc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8655d0: r1 = Function '<anonymous closure>':.
    //     0x8655d0: add             x1, PP, #0x10, lsl #12  ; [pp+0x100f0] AnonymousClosure: (0x8663b0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8655d4: ldr             x1, [x1, #0xf0]
    // 0x8655d8: r2 = Null
    //     0x8655d8: mov             x2, NULL
    // 0x8655dc: r0 = AllocateClosure()
    //     0x8655dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8655e0: r16 = <DoaRepository>
    //     0x8655e0: add             x16, PP, #0x10, lsl #12  ; [pp+0x100f8] TypeArguments: <DoaRepository>
    //     0x8655e4: ldr             x16, [x16, #0xf8]
    // 0x8655e8: stp             x0, x16, [SP, #0x10]
    // 0x8655ec: r16 = true
    //     0x8655ec: add             x16, NULL, #0x20  ; true
    // 0x8655f0: r30 = "doa_local_repo"
    //     0x8655f0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10100] "doa_local_repo"
    //     0x8655f4: ldr             lr, [lr, #0x100]
    // 0x8655f8: stp             lr, x16, [SP]
    // 0x8655fc: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8655fc: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865600: ldr             x4, [x4, #0xf38]
    // 0x865604: r0 = Inst.lazyPut()
    //     0x865604: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865608: r1 = Function '<anonymous closure>':.
    //     0x865608: add             x1, PP, #0x10, lsl #12  ; [pp+0x10108] AnonymousClosure: (0x866308), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86560c: ldr             x1, [x1, #0x108]
    // 0x865610: r2 = Null
    //     0x865610: mov             x2, NULL
    // 0x865614: r0 = AllocateClosure()
    //     0x865614: bl              #0xec1630  ; AllocateClosureStub
    // 0x865618: r16 = <DoaRepository>
    //     0x865618: add             x16, PP, #0x10, lsl #12  ; [pp+0x100f8] TypeArguments: <DoaRepository>
    //     0x86561c: ldr             x16, [x16, #0xf8]
    // 0x865620: stp             x0, x16, [SP, #0x10]
    // 0x865624: r16 = true
    //     0x865624: add             x16, NULL, #0x20  ; true
    // 0x865628: r30 = "doa_remote_repo"
    //     0x865628: add             lr, PP, #0x10, lsl #12  ; [pp+0x10110] "doa_remote_repo"
    //     0x86562c: ldr             lr, [lr, #0x110]
    // 0x865630: stp             lr, x16, [SP]
    // 0x865634: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865634: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865638: ldr             x4, [x4, #0xf38]
    // 0x86563c: r0 = Inst.lazyPut()
    //     0x86563c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865640: r1 = Function '<anonymous closure>':.
    //     0x865640: add             x1, PP, #0x10, lsl #12  ; [pp+0x10118] AnonymousClosure: (0x866290), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865644: ldr             x1, [x1, #0x118]
    // 0x865648: r2 = Null
    //     0x865648: mov             x2, NULL
    // 0x86564c: r0 = AllocateClosure()
    //     0x86564c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865650: r16 = <EncyclopediaRepository>
    //     0x865650: add             x16, PP, #0x10, lsl #12  ; [pp+0x10120] TypeArguments: <EncyclopediaRepository>
    //     0x865654: ldr             x16, [x16, #0x120]
    // 0x865658: stp             x0, x16, [SP, #0x10]
    // 0x86565c: r16 = true
    //     0x86565c: add             x16, NULL, #0x20  ; true
    // 0x865660: r30 = "encyclopedia_local_repo"
    //     0x865660: add             lr, PP, #0x10, lsl #12  ; [pp+0x10128] "encyclopedia_local_repo"
    //     0x865664: ldr             lr, [lr, #0x128]
    // 0x865668: stp             lr, x16, [SP]
    // 0x86566c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86566c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865670: ldr             x4, [x4, #0xf38]
    // 0x865674: r0 = Inst.lazyPut()
    //     0x865674: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865678: r1 = Function '<anonymous closure>':.
    //     0x865678: add             x1, PP, #0x10, lsl #12  ; [pp+0x10130] AnonymousClosure: (0x86621c), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86567c: ldr             x1, [x1, #0x130]
    // 0x865680: r2 = Null
    //     0x865680: mov             x2, NULL
    // 0x865684: r0 = AllocateClosure()
    //     0x865684: bl              #0xec1630  ; AllocateClosureStub
    // 0x865688: r16 = <ManasikRepository>
    //     0x865688: add             x16, PP, #0x10, lsl #12  ; [pp+0x10138] TypeArguments: <ManasikRepository>
    //     0x86568c: ldr             x16, [x16, #0x138]
    // 0x865690: stp             x0, x16, [SP, #0x10]
    // 0x865694: r16 = true
    //     0x865694: add             x16, NULL, #0x20  ; true
    // 0x865698: r30 = "manasik_repo"
    //     0x865698: add             lr, PP, #0x10, lsl #12  ; [pp+0x10140] "manasik_repo"
    //     0x86569c: ldr             lr, [lr, #0x140]
    // 0x8656a0: stp             lr, x16, [SP]
    // 0x8656a4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8656a4: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8656a8: ldr             x4, [x4, #0xf38]
    // 0x8656ac: r0 = Inst.lazyPut()
    //     0x8656ac: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8656b0: r1 = Function '<anonymous closure>':.
    //     0x8656b0: add             x1, PP, #0x10, lsl #12  ; [pp+0x10148] AnonymousClosure: (0x86619c), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8656b4: ldr             x1, [x1, #0x148]
    // 0x8656b8: r2 = Null
    //     0x8656b8: mov             x2, NULL
    // 0x8656bc: r0 = AllocateClosure()
    //     0x8656bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8656c0: r16 = <HajiRepository>
    //     0x8656c0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10150] TypeArguments: <HajiRepository>
    //     0x8656c4: ldr             x16, [x16, #0x150]
    // 0x8656c8: stp             x0, x16, [SP, #0x10]
    // 0x8656cc: r16 = true
    //     0x8656cc: add             x16, NULL, #0x20  ; true
    // 0x8656d0: r30 = "haji_repo"
    //     0x8656d0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10158] "haji_repo"
    //     0x8656d4: ldr             lr, [lr, #0x158]
    // 0x8656d8: stp             lr, x16, [SP]
    // 0x8656dc: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8656dc: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8656e0: ldr             x4, [x4, #0xf38]
    // 0x8656e4: r0 = Inst.lazyPut()
    //     0x8656e4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8656e8: r1 = Function '<anonymous closure>':.
    //     0x8656e8: add             x1, PP, #0x10, lsl #12  ; [pp+0x10160] AnonymousClosure: (0x866128), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8656ec: ldr             x1, [x1, #0x160]
    // 0x8656f0: r2 = Null
    //     0x8656f0: mov             x2, NULL
    // 0x8656f4: r0 = AllocateClosure()
    //     0x8656f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8656f8: r16 = <HikmahRepository>
    //     0x8656f8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10168] TypeArguments: <HikmahRepository>
    //     0x8656fc: ldr             x16, [x16, #0x168]
    // 0x865700: stp             x0, x16, [SP, #0x10]
    // 0x865704: r16 = true
    //     0x865704: add             x16, NULL, #0x20  ; true
    // 0x865708: r30 = "hikmah_remote_repo"
    //     0x865708: add             lr, PP, #0x10, lsl #12  ; [pp+0x10170] "hikmah_remote_repo"
    //     0x86570c: ldr             lr, [lr, #0x170]
    // 0x865710: stp             lr, x16, [SP]
    // 0x865714: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865714: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865718: ldr             x4, [x4, #0xf38]
    // 0x86571c: r0 = Inst.lazyPut()
    //     0x86571c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865720: r1 = Function '<anonymous closure>':.
    //     0x865720: add             x1, PP, #0x10, lsl #12  ; [pp+0x10178] AnonymousClosure: (0x8660a8), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865724: ldr             x1, [x1, #0x178]
    // 0x865728: r2 = Null
    //     0x865728: mov             x2, NULL
    // 0x86572c: r0 = AllocateClosure()
    //     0x86572c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865730: r16 = <EncyclopediaRepository>
    //     0x865730: add             x16, PP, #0x10, lsl #12  ; [pp+0x10120] TypeArguments: <EncyclopediaRepository>
    //     0x865734: ldr             x16, [x16, #0x120]
    // 0x865738: stp             x0, x16, [SP, #0x10]
    // 0x86573c: r16 = true
    //     0x86573c: add             x16, NULL, #0x20  ; true
    // 0x865740: r30 = "encyclopedia_remote_repo"
    //     0x865740: add             lr, PP, #0x10, lsl #12  ; [pp+0x10180] "encyclopedia_remote_repo"
    //     0x865744: ldr             lr, [lr, #0x180]
    // 0x865748: stp             lr, x16, [SP]
    // 0x86574c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x86574c: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865750: ldr             x4, [x4, #0xf38]
    // 0x865754: r0 = Inst.lazyPut()
    //     0x865754: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865758: r1 = Function '<anonymous closure>':.
    //     0x865758: add             x1, PP, #0x10, lsl #12  ; [pp+0x10188] AnonymousClosure: (0x866028), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86575c: ldr             x1, [x1, #0x188]
    // 0x865760: r2 = Null
    //     0x865760: mov             x2, NULL
    // 0x865764: r0 = AllocateClosure()
    //     0x865764: bl              #0xec1630  ; AllocateClosureStub
    // 0x865768: r16 = <KalamRepository>
    //     0x865768: add             x16, PP, #0x10, lsl #12  ; [pp+0x10190] TypeArguments: <KalamRepository>
    //     0x86576c: ldr             x16, [x16, #0x190]
    // 0x865770: stp             x0, x16, [SP, #0x10]
    // 0x865774: r16 = true
    //     0x865774: add             x16, NULL, #0x20  ; true
    // 0x865778: r30 = "kalam_repo"
    //     0x865778: add             lr, PP, #0x10, lsl #12  ; [pp+0x10198] "kalam_repo"
    //     0x86577c: ldr             lr, [lr, #0x198]
    // 0x865780: stp             lr, x16, [SP]
    // 0x865784: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865784: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865788: ldr             x4, [x4, #0xf38]
    // 0x86578c: r0 = Inst.lazyPut()
    //     0x86578c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865790: r1 = Function '<anonymous closure>':.
    //     0x865790: add             x1, PP, #0x10, lsl #12  ; [pp+0x101a0] AnonymousClosure: (0x865f88), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865794: ldr             x1, [x1, #0x1a0]
    // 0x865798: r2 = Null
    //     0x865798: mov             x2, NULL
    // 0x86579c: r0 = AllocateClosure()
    //     0x86579c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8657a0: r16 = <TutorialRepository>
    //     0x8657a0: add             x16, PP, #0x10, lsl #12  ; [pp+0x101a8] TypeArguments: <TutorialRepository>
    //     0x8657a4: ldr             x16, [x16, #0x1a8]
    // 0x8657a8: stp             x0, x16, [SP, #0x10]
    // 0x8657ac: r16 = true
    //     0x8657ac: add             x16, NULL, #0x20  ; true
    // 0x8657b0: r30 = "tutorial_repo"
    //     0x8657b0: add             lr, PP, #0x10, lsl #12  ; [pp+0x101b0] "tutorial_repo"
    //     0x8657b4: ldr             lr, [lr, #0x1b0]
    // 0x8657b8: stp             lr, x16, [SP]
    // 0x8657bc: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8657bc: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8657c0: ldr             x4, [x4, #0xf38]
    // 0x8657c4: r0 = Inst.lazyPut()
    //     0x8657c4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8657c8: r1 = Function '<anonymous closure>':.
    //     0x8657c8: add             x1, PP, #0x10, lsl #12  ; [pp+0x101b8] AnonymousClosure: (0x865ef0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8657cc: ldr             x1, [x1, #0x1b8]
    // 0x8657d0: r2 = Null
    //     0x8657d0: mov             x2, NULL
    // 0x8657d4: r0 = AllocateClosure()
    //     0x8657d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8657d8: r16 = <ZiarahRepository>
    //     0x8657d8: add             x16, PP, #0x10, lsl #12  ; [pp+0x101c0] TypeArguments: <ZiarahRepository>
    //     0x8657dc: ldr             x16, [x16, #0x1c0]
    // 0x8657e0: stp             x0, x16, [SP, #0x10]
    // 0x8657e4: r16 = "ziarah_local_repo"
    //     0x8657e4: add             x16, PP, #0x10, lsl #12  ; [pp+0x101c8] "ziarah_local_repo"
    //     0x8657e8: ldr             x16, [x16, #0x1c8]
    // 0x8657ec: r30 = true
    //     0x8657ec: add             lr, NULL, #0x20  ; true
    // 0x8657f0: stp             lr, x16, [SP]
    // 0x8657f4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x8657f4: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x8657f8: r0 = Inst.lazyPut()
    //     0x8657f8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8657fc: r1 = Function '<anonymous closure>':.
    //     0x8657fc: add             x1, PP, #0x10, lsl #12  ; [pp+0x101d0] AnonymousClosure: (0x865e48), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865800: ldr             x1, [x1, #0x1d0]
    // 0x865804: r2 = Null
    //     0x865804: mov             x2, NULL
    // 0x865808: r0 = AllocateClosure()
    //     0x865808: bl              #0xec1630  ; AllocateClosureStub
    // 0x86580c: r16 = <ZiarahRepository>
    //     0x86580c: add             x16, PP, #0x10, lsl #12  ; [pp+0x101c0] TypeArguments: <ZiarahRepository>
    //     0x865810: ldr             x16, [x16, #0x1c0]
    // 0x865814: stp             x0, x16, [SP, #0x10]
    // 0x865818: r16 = "ziarah_remote_repo"
    //     0x865818: add             x16, PP, #0x10, lsl #12  ; [pp+0x101d8] "ziarah_remote_repo"
    //     0x86581c: ldr             x16, [x16, #0x1d8]
    // 0x865820: r30 = true
    //     0x865820: add             lr, NULL, #0x20  ; true
    // 0x865824: stp             lr, x16, [SP]
    // 0x865828: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x865828: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x86582c: r0 = Inst.lazyPut()
    //     0x86582c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865830: r1 = Function '<anonymous closure>':.
    //     0x865830: add             x1, PP, #0x10, lsl #12  ; [pp+0x101e0] AnonymousClosure: (0x865dc8), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865834: ldr             x1, [x1, #0x1e0]
    // 0x865838: r2 = Null
    //     0x865838: mov             x2, NULL
    // 0x86583c: r0 = AllocateClosure()
    //     0x86583c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865840: r16 = <CounterRepository>
    //     0x865840: add             x16, PP, #0x10, lsl #12  ; [pp+0x101e8] TypeArguments: <CounterRepository>
    //     0x865844: ldr             x16, [x16, #0x1e8]
    // 0x865848: stp             x0, x16, [SP, #0x10]
    // 0x86584c: r16 = "counter_repo"
    //     0x86584c: add             x16, PP, #0x10, lsl #12  ; [pp+0x101f0] "counter_repo"
    //     0x865850: ldr             x16, [x16, #0x1f0]
    // 0x865854: r30 = true
    //     0x865854: add             lr, NULL, #0x20  ; true
    // 0x865858: stp             lr, x16, [SP]
    // 0x86585c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x86585c: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x865860: r0 = Inst.lazyPut()
    //     0x865860: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865864: r1 = Function '<anonymous closure>':.
    //     0x865864: add             x1, PP, #0x10, lsl #12  ; [pp+0x101f8] AnonymousClosure: (0x865d48), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865868: ldr             x1, [x1, #0x1f8]
    // 0x86586c: r2 = Null
    //     0x86586c: mov             x2, NULL
    // 0x865870: r0 = AllocateClosure()
    //     0x865870: bl              #0xec1630  ; AllocateClosureStub
    // 0x865874: r16 = <ZakatRepository>
    //     0x865874: add             x16, PP, #0x10, lsl #12  ; [pp+0x10200] TypeArguments: <ZakatRepository>
    //     0x865878: ldr             x16, [x16, #0x200]
    // 0x86587c: stp             x0, x16, [SP, #0x10]
    // 0x865880: r16 = "zakat_repo"
    //     0x865880: add             x16, PP, #0x10, lsl #12  ; [pp+0x10208] "zakat_repo"
    //     0x865884: ldr             x16, [x16, #0x208]
    // 0x865888: r30 = true
    //     0x865888: add             lr, NULL, #0x20  ; true
    // 0x86588c: stp             lr, x16, [SP]
    // 0x865890: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x865890: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x865894: r0 = Inst.lazyPut()
    //     0x865894: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865898: r1 = Function '<anonymous closure>':.
    //     0x865898: add             x1, PP, #0x10, lsl #12  ; [pp+0x10210] AnonymousClosure: (0x865c88), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86589c: ldr             x1, [x1, #0x210]
    // 0x8658a0: r2 = Null
    //     0x8658a0: mov             x2, NULL
    // 0x8658a4: r0 = AllocateClosure()
    //     0x8658a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8658a8: r16 = <CalendarRepository>
    //     0x8658a8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10218] TypeArguments: <CalendarRepository>
    //     0x8658ac: ldr             x16, [x16, #0x218]
    // 0x8658b0: stp             x0, x16, [SP, #0x10]
    // 0x8658b4: r16 = "calendar_repo"
    //     0x8658b4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10220] "calendar_repo"
    //     0x8658b8: ldr             x16, [x16, #0x220]
    // 0x8658bc: r30 = true
    //     0x8658bc: add             lr, NULL, #0x20  ; true
    // 0x8658c0: stp             lr, x16, [SP]
    // 0x8658c4: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x8658c4: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x8658c8: r0 = Inst.lazyPut()
    //     0x8658c8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8658cc: r1 = Function '<anonymous closure>':.
    //     0x8658cc: add             x1, PP, #0x10, lsl #12  ; [pp+0x10228] AnonymousClosure: (0x865c08), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8658d0: ldr             x1, [x1, #0x228]
    // 0x8658d4: r2 = Null
    //     0x8658d4: mov             x2, NULL
    // 0x8658d8: r0 = AllocateClosure()
    //     0x8658d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8658dc: r16 = <FaqRepository>
    //     0x8658dc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10230] TypeArguments: <FaqRepository>
    //     0x8658e0: ldr             x16, [x16, #0x230]
    // 0x8658e4: stp             x0, x16, [SP, #0x10]
    // 0x8658e8: r16 = "faq_repo"
    //     0x8658e8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10238] "faq_repo"
    //     0x8658ec: ldr             x16, [x16, #0x238]
    // 0x8658f0: r30 = true
    //     0x8658f0: add             lr, NULL, #0x20  ; true
    // 0x8658f4: stp             lr, x16, [SP]
    // 0x8658f8: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x8658f8: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x8658fc: r0 = Inst.lazyPut()
    //     0x8658fc: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865900: r1 = Function '<anonymous closure>':.
    //     0x865900: add             x1, PP, #0x10, lsl #12  ; [pp+0x10240] AnonymousClosure: (0x865b90), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865904: ldr             x1, [x1, #0x240]
    // 0x865908: r2 = Null
    //     0x865908: mov             x2, NULL
    // 0x86590c: r0 = AllocateClosure()
    //     0x86590c: bl              #0xec1630  ; AllocateClosureStub
    // 0x865910: r16 = <DiacriticRepository>
    //     0x865910: add             x16, PP, #0x10, lsl #12  ; [pp+0x10248] TypeArguments: <DiacriticRepository>
    //     0x865914: ldr             x16, [x16, #0x248]
    // 0x865918: stp             x0, x16, [SP, #0x10]
    // 0x86591c: r16 = "diacritic_repo"
    //     0x86591c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10250] "diacritic_repo"
    //     0x865920: ldr             x16, [x16, #0x250]
    // 0x865924: r30 = true
    //     0x865924: add             lr, NULL, #0x20  ; true
    // 0x865928: stp             lr, x16, [SP]
    // 0x86592c: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x86592c: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x865930: r0 = Inst.lazyPut()
    //     0x865930: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865934: r1 = Function '<anonymous closure>':.
    //     0x865934: add             x1, PP, #0x10, lsl #12  ; [pp+0x10258] AnonymousClosure: (0x865b10), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x865938: ldr             x1, [x1, #0x258]
    // 0x86593c: r2 = Null
    //     0x86593c: mov             x2, NULL
    // 0x865940: r0 = AllocateClosure()
    //     0x865940: bl              #0xec1630  ; AllocateClosureStub
    // 0x865944: r16 = <QurbanRepository>
    //     0x865944: add             x16, PP, #0x10, lsl #12  ; [pp+0x10260] TypeArguments: <QurbanRepository>
    //     0x865948: ldr             x16, [x16, #0x260]
    // 0x86594c: stp             x0, x16, [SP, #0x10]
    // 0x865950: r16 = "qurban_repo"
    //     0x865950: add             x16, PP, #0x10, lsl #12  ; [pp+0x10268] "qurban_repo"
    //     0x865954: ldr             x16, [x16, #0x268]
    // 0x865958: r30 = true
    //     0x865958: add             lr, NULL, #0x20  ; true
    // 0x86595c: stp             lr, x16, [SP]
    // 0x865960: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x2, tag, 0x1, null]
    //     0x865960: ldr             x4, [PP, #0xb0]  ; [pp+0xb0] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x2, "tag", 0x1, Null]
    // 0x865964: r0 = Inst.lazyPut()
    //     0x865964: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x865968: r1 = Function '<anonymous closure>':.
    //     0x865968: add             x1, PP, #0x10, lsl #12  ; [pp+0x10270] AnonymousClosure: (0x865a70), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x86596c: ldr             x1, [x1, #0x270]
    // 0x865970: r2 = Null
    //     0x865970: mov             x2, NULL
    // 0x865974: r0 = AllocateClosure()
    //     0x865974: bl              #0xec1630  ; AllocateClosureStub
    // 0x865978: r16 = <WiridRepository>
    //     0x865978: add             x16, PP, #0x10, lsl #12  ; [pp+0x10278] TypeArguments: <WiridRepository>
    //     0x86597c: ldr             x16, [x16, #0x278]
    // 0x865980: stp             x0, x16, [SP, #0x10]
    // 0x865984: r16 = true
    //     0x865984: add             x16, NULL, #0x20  ; true
    // 0x865988: r30 = "wirid_repo"
    //     0x865988: add             lr, PP, #0x10, lsl #12  ; [pp+0x10280] "wirid_repo"
    //     0x86598c: ldr             lr, [lr, #0x280]
    // 0x865990: stp             lr, x16, [SP]
    // 0x865994: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x865994: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x865998: ldr             x4, [x4, #0xf38]
    // 0x86599c: r0 = Inst.lazyPut()
    //     0x86599c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8659a0: r1 = Function '<anonymous closure>':.
    //     0x8659a0: add             x1, PP, #0x10, lsl #12  ; [pp+0x10288] AnonymousClosure: (0x8659f0), in [package:nuonline/app/data/repositories/repositories.dart] RepositoriesBinding::dependencies (0x865174)
    //     0x8659a4: ldr             x1, [x1, #0x288]
    // 0x8659a8: r2 = Null
    //     0x8659a8: mov             x2, NULL
    // 0x8659ac: r0 = AllocateClosure()
    //     0x8659ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x8659b0: r16 = <UrlRepository>
    //     0x8659b0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10290] TypeArguments: <UrlRepository>
    //     0x8659b4: ldr             x16, [x16, #0x290]
    // 0x8659b8: stp             x0, x16, [SP, #0x10]
    // 0x8659bc: r16 = true
    //     0x8659bc: add             x16, NULL, #0x20  ; true
    // 0x8659c0: r30 = "url_repo"
    //     0x8659c0: add             lr, PP, #0x10, lsl #12  ; [pp+0x10298] "url_repo"
    //     0x8659c4: ldr             lr, [lr, #0x298]
    // 0x8659c8: stp             lr, x16, [SP]
    // 0x8659cc: r4 = const [0x1, 0x3, 0x3, 0x1, fenix, 0x1, tag, 0x2, null]
    //     0x8659cc: add             x4, PP, #0xf, lsl #12  ; [pp+0xff38] List(9) [0x1, 0x3, 0x3, 0x1, "fenix", 0x1, "tag", 0x2, Null]
    //     0x8659d0: ldr             x4, [x4, #0xf38]
    // 0x8659d4: r0 = Inst.lazyPut()
    //     0x8659d4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x8659d8: r0 = Null
    //     0x8659d8: mov             x0, NULL
    // 0x8659dc: LeaveFrame
    //     0x8659dc: mov             SP, fp
    //     0x8659e0: ldp             fp, lr, [SP], #0x10
    // 0x8659e4: ret
    //     0x8659e4: ret             
    // 0x8659e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8659e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8659ec: b               #0x86518c
  }
  [closure] UrlRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8659f0, size: 0x74
    // 0x8659f0: EnterFrame
    //     0x8659f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8659f4: mov             fp, SP
    // 0x8659f8: AllocStack(0x18)
    //     0x8659f8: sub             SP, SP, #0x18
    // 0x8659fc: CheckStackOverflow
    //     0x8659fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865a00: cmp             SP, x16
    //     0x865a04: b.ls            #0x865a5c
    // 0x865a08: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865a08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865a0c: ldr             x0, [x0, #0x2670]
    //     0x865a10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865a14: cmp             w0, w16
    //     0x865a18: b.ne            #0x865a24
    //     0x865a1c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865a20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865a24: r16 = <ApiService>
    //     0x865a24: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865a28: ldr             x16, [x16, #0xb00]
    // 0x865a2c: r30 = "utils_api"
    //     0x865a2c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb78] "utils_api"
    //     0x865a30: ldr             lr, [lr, #0xb78]
    // 0x865a34: stp             lr, x16, [SP]
    // 0x865a38: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865a38: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865a3c: r0 = Inst.find()
    //     0x865a3c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865a40: stur            x0, [fp, #-8]
    // 0x865a44: r0 = UrlRepository()
    //     0x865a44: bl              #0x865a64  ; AllocateUrlRepositoryStub -> UrlRepository (size=0xc)
    // 0x865a48: ldur            x1, [fp, #-8]
    // 0x865a4c: StoreField: r0->field_7 = r1
    //     0x865a4c: stur            w1, [x0, #7]
    // 0x865a50: LeaveFrame
    //     0x865a50: mov             SP, fp
    //     0x865a54: ldp             fp, lr, [SP], #0x10
    // 0x865a58: ret
    //     0x865a58: ret             
    // 0x865a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865a60: b               #0x865a08
  }
  [closure] WiridRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865a70, size: 0x94
    // 0x865a70: EnterFrame
    //     0x865a70: stp             fp, lr, [SP, #-0x10]!
    //     0x865a74: mov             fp, SP
    // 0x865a78: AllocStack(0x20)
    //     0x865a78: sub             SP, SP, #0x20
    // 0x865a7c: CheckStackOverflow
    //     0x865a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865a80: cmp             SP, x16
    //     0x865a84: b.ls            #0x865afc
    // 0x865a88: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865a88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865a8c: ldr             x0, [x0, #0x2670]
    //     0x865a90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865a94: cmp             w0, w16
    //     0x865a98: b.ne            #0x865aa4
    //     0x865a9c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865aa0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865aa4: r16 = <AppStorage>
    //     0x865aa4: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x865aa8: r30 = "app_storage"
    //     0x865aa8: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x865aac: stp             lr, x16, [SP]
    // 0x865ab0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865ab0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865ab4: r0 = Inst.find()
    //     0x865ab4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865ab8: stur            x0, [fp, #-8]
    // 0x865abc: r16 = <WiridStorage>
    //     0x865abc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdbd8] TypeArguments: <WiridStorage>
    //     0x865ac0: ldr             x16, [x16, #0xbd8]
    // 0x865ac4: r30 = "wirid_storage"
    //     0x865ac4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdbe0] "wirid_storage"
    //     0x865ac8: ldr             lr, [lr, #0xbe0]
    // 0x865acc: stp             lr, x16, [SP]
    // 0x865ad0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865ad0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865ad4: r0 = Inst.find()
    //     0x865ad4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865ad8: stur            x0, [fp, #-0x10]
    // 0x865adc: r0 = WiridRepository()
    //     0x865adc: bl              #0x865b04  ; AllocateWiridRepositoryStub -> WiridRepository (size=0x10)
    // 0x865ae0: ldur            x1, [fp, #-8]
    // 0x865ae4: StoreField: r0->field_7 = r1
    //     0x865ae4: stur            w1, [x0, #7]
    // 0x865ae8: ldur            x1, [fp, #-0x10]
    // 0x865aec: StoreField: r0->field_b = r1
    //     0x865aec: stur            w1, [x0, #0xb]
    // 0x865af0: LeaveFrame
    //     0x865af0: mov             SP, fp
    //     0x865af4: ldp             fp, lr, [SP], #0x10
    // 0x865af8: ret
    //     0x865af8: ret             
    // 0x865afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865afc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865b00: b               #0x865a88
  }
  [closure] QurbanRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865b10, size: 0x74
    // 0x865b10: EnterFrame
    //     0x865b10: stp             fp, lr, [SP, #-0x10]!
    //     0x865b14: mov             fp, SP
    // 0x865b18: AllocStack(0x18)
    //     0x865b18: sub             SP, SP, #0x18
    // 0x865b1c: CheckStackOverflow
    //     0x865b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865b20: cmp             SP, x16
    //     0x865b24: b.ls            #0x865b7c
    // 0x865b28: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865b28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865b2c: ldr             x0, [x0, #0x2670]
    //     0x865b30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865b34: cmp             w0, w16
    //     0x865b38: b.ne            #0x865b44
    //     0x865b3c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865b40: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865b44: r16 = <ApiService>
    //     0x865b44: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865b48: ldr             x16, [x16, #0xb00]
    // 0x865b4c: r30 = "donation_api"
    //     0x865b4c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb68] "donation_api"
    //     0x865b50: ldr             lr, [lr, #0xb68]
    // 0x865b54: stp             lr, x16, [SP]
    // 0x865b58: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865b58: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865b5c: r0 = Inst.find()
    //     0x865b5c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865b60: stur            x0, [fp, #-8]
    // 0x865b64: r0 = QurbanRepository()
    //     0x865b64: bl              #0x865b84  ; AllocateQurbanRepositoryStub -> QurbanRepository (size=0xc)
    // 0x865b68: ldur            x1, [fp, #-8]
    // 0x865b6c: StoreField: r0->field_7 = r1
    //     0x865b6c: stur            w1, [x0, #7]
    // 0x865b70: LeaveFrame
    //     0x865b70: mov             SP, fp
    //     0x865b74: ldp             fp, lr, [SP], #0x10
    // 0x865b78: ret
    //     0x865b78: ret             
    // 0x865b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865b80: b               #0x865b28
  }
  [closure] DiacriticRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865b90, size: 0x6c
    // 0x865b90: EnterFrame
    //     0x865b90: stp             fp, lr, [SP, #-0x10]!
    //     0x865b94: mov             fp, SP
    // 0x865b98: AllocStack(0x18)
    //     0x865b98: sub             SP, SP, #0x18
    // 0x865b9c: CheckStackOverflow
    //     0x865b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865ba0: cmp             SP, x16
    //     0x865ba4: b.ls            #0x865bf4
    // 0x865ba8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865ba8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865bac: ldr             x0, [x0, #0x2670]
    //     0x865bb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865bb4: cmp             w0, w16
    //     0x865bb8: b.ne            #0x865bc4
    //     0x865bbc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865bc0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865bc4: r16 = <ContentStorage>
    //     0x865bc4: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x865bc8: r30 = "content_storage"
    //     0x865bc8: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x865bcc: stp             lr, x16, [SP]
    // 0x865bd0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865bd0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865bd4: r0 = Inst.find()
    //     0x865bd4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865bd8: stur            x0, [fp, #-8]
    // 0x865bdc: r0 = DiacriticRepository()
    //     0x865bdc: bl              #0x865bfc  ; AllocateDiacriticRepositoryStub -> DiacriticRepository (size=0xc)
    // 0x865be0: ldur            x1, [fp, #-8]
    // 0x865be4: StoreField: r0->field_7 = r1
    //     0x865be4: stur            w1, [x0, #7]
    // 0x865be8: LeaveFrame
    //     0x865be8: mov             SP, fp
    //     0x865bec: ldp             fp, lr, [SP], #0x10
    // 0x865bf0: ret
    //     0x865bf0: ret             
    // 0x865bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865bf8: b               #0x865ba8
  }
  [closure] FaqRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865c08, size: 0x74
    // 0x865c08: EnterFrame
    //     0x865c08: stp             fp, lr, [SP, #-0x10]!
    //     0x865c0c: mov             fp, SP
    // 0x865c10: AllocStack(0x18)
    //     0x865c10: sub             SP, SP, #0x18
    // 0x865c14: CheckStackOverflow
    //     0x865c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865c18: cmp             SP, x16
    //     0x865c1c: b.ls            #0x865c74
    // 0x865c20: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865c20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865c24: ldr             x0, [x0, #0x2670]
    //     0x865c28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865c2c: cmp             w0, w16
    //     0x865c30: b.ne            #0x865c3c
    //     0x865c34: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865c38: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865c3c: r16 = <ApiService>
    //     0x865c3c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865c40: ldr             x16, [x16, #0xb00]
    // 0x865c44: r30 = "app_api_v2"
    //     0x865c44: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x865c48: ldr             lr, [lr, #0xb18]
    // 0x865c4c: stp             lr, x16, [SP]
    // 0x865c50: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865c50: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865c54: r0 = Inst.find()
    //     0x865c54: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865c58: stur            x0, [fp, #-8]
    // 0x865c5c: r0 = FaqRepository()
    //     0x865c5c: bl              #0x865c7c  ; AllocateFaqRepositoryStub -> FaqRepository (size=0xc)
    // 0x865c60: ldur            x1, [fp, #-8]
    // 0x865c64: StoreField: r0->field_7 = r1
    //     0x865c64: stur            w1, [x0, #7]
    // 0x865c68: LeaveFrame
    //     0x865c68: mov             SP, fp
    //     0x865c6c: ldp             fp, lr, [SP], #0x10
    // 0x865c70: ret
    //     0x865c70: ret             
    // 0x865c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865c74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865c78: b               #0x865c20
  }
  [closure] CalendarRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865c88, size: 0xb4
    // 0x865c88: EnterFrame
    //     0x865c88: stp             fp, lr, [SP, #-0x10]!
    //     0x865c8c: mov             fp, SP
    // 0x865c90: AllocStack(0x28)
    //     0x865c90: sub             SP, SP, #0x28
    // 0x865c94: CheckStackOverflow
    //     0x865c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865c98: cmp             SP, x16
    //     0x865c9c: b.ls            #0x865d34
    // 0x865ca0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865ca0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865ca4: ldr             x0, [x0, #0x2670]
    //     0x865ca8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865cac: cmp             w0, w16
    //     0x865cb0: b.ne            #0x865cbc
    //     0x865cb4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865cb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865cbc: r16 = <ApiService>
    //     0x865cbc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865cc0: ldr             x16, [x16, #0xb00]
    // 0x865cc4: r30 = "app_api_v2"
    //     0x865cc4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x865cc8: ldr             lr, [lr, #0xb18]
    // 0x865ccc: stp             lr, x16, [SP]
    // 0x865cd0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865cd0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865cd4: r0 = Inst.find()
    //     0x865cd4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865cd8: stur            x0, [fp, #-8]
    // 0x865cdc: r16 = <ContentStorage>
    //     0x865cdc: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x865ce0: r30 = "content_storage"
    //     0x865ce0: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x865ce4: stp             lr, x16, [SP]
    // 0x865ce8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865ce8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865cec: r0 = Inst.find()
    //     0x865cec: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865cf0: stur            x0, [fp, #-0x10]
    // 0x865cf4: r16 = <AppStorage>
    //     0x865cf4: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x865cf8: r30 = "app_storage"
    //     0x865cf8: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x865cfc: stp             lr, x16, [SP]
    // 0x865d00: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865d00: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865d04: r0 = Inst.find()
    //     0x865d04: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865d08: stur            x0, [fp, #-0x18]
    // 0x865d0c: r0 = CalendarRepository()
    //     0x865d0c: bl              #0x865d3c  ; AllocateCalendarRepositoryStub -> CalendarRepository (size=0x14)
    // 0x865d10: ldur            x1, [fp, #-8]
    // 0x865d14: StoreField: r0->field_7 = r1
    //     0x865d14: stur            w1, [x0, #7]
    // 0x865d18: ldur            x1, [fp, #-0x10]
    // 0x865d1c: StoreField: r0->field_b = r1
    //     0x865d1c: stur            w1, [x0, #0xb]
    // 0x865d20: ldur            x1, [fp, #-0x18]
    // 0x865d24: StoreField: r0->field_f = r1
    //     0x865d24: stur            w1, [x0, #0xf]
    // 0x865d28: LeaveFrame
    //     0x865d28: mov             SP, fp
    //     0x865d2c: ldp             fp, lr, [SP], #0x10
    // 0x865d30: ret
    //     0x865d30: ret             
    // 0x865d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865d38: b               #0x865ca0
  }
  [closure] ZakatRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865d48, size: 0x74
    // 0x865d48: EnterFrame
    //     0x865d48: stp             fp, lr, [SP, #-0x10]!
    //     0x865d4c: mov             fp, SP
    // 0x865d50: AllocStack(0x18)
    //     0x865d50: sub             SP, SP, #0x18
    // 0x865d54: CheckStackOverflow
    //     0x865d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865d58: cmp             SP, x16
    //     0x865d5c: b.ls            #0x865db4
    // 0x865d60: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865d60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865d64: ldr             x0, [x0, #0x2670]
    //     0x865d68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865d6c: cmp             w0, w16
    //     0x865d70: b.ne            #0x865d7c
    //     0x865d74: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865d78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865d7c: r16 = <ApiService>
    //     0x865d7c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865d80: ldr             x16, [x16, #0xb00]
    // 0x865d84: r30 = "donation_api"
    //     0x865d84: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb68] "donation_api"
    //     0x865d88: ldr             lr, [lr, #0xb68]
    // 0x865d8c: stp             lr, x16, [SP]
    // 0x865d90: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865d90: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865d94: r0 = Inst.find()
    //     0x865d94: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865d98: stur            x0, [fp, #-8]
    // 0x865d9c: r0 = ZakatRepository()
    //     0x865d9c: bl              #0x865dbc  ; AllocateZakatRepositoryStub -> ZakatRepository (size=0xc)
    // 0x865da0: ldur            x1, [fp, #-8]
    // 0x865da4: StoreField: r0->field_7 = r1
    //     0x865da4: stur            w1, [x0, #7]
    // 0x865da8: LeaveFrame
    //     0x865da8: mov             SP, fp
    //     0x865dac: ldp             fp, lr, [SP], #0x10
    // 0x865db0: ret
    //     0x865db0: ret             
    // 0x865db4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865db4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865db8: b               #0x865d60
  }
  [closure] CounterRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865dc8, size: 0x74
    // 0x865dc8: EnterFrame
    //     0x865dc8: stp             fp, lr, [SP, #-0x10]!
    //     0x865dcc: mov             fp, SP
    // 0x865dd0: AllocStack(0x18)
    //     0x865dd0: sub             SP, SP, #0x18
    // 0x865dd4: CheckStackOverflow
    //     0x865dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865dd8: cmp             SP, x16
    //     0x865ddc: b.ls            #0x865e34
    // 0x865de0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865de0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865de4: ldr             x0, [x0, #0x2670]
    //     0x865de8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865dec: cmp             w0, w16
    //     0x865df0: b.ne            #0x865dfc
    //     0x865df4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865df8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865dfc: r16 = <ApiService>
    //     0x865dfc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865e00: ldr             x16, [x16, #0xb00]
    // 0x865e04: r30 = "app_api_v2"
    //     0x865e04: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x865e08: ldr             lr, [lr, #0xb18]
    // 0x865e0c: stp             lr, x16, [SP]
    // 0x865e10: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865e10: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865e14: r0 = Inst.find()
    //     0x865e14: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865e18: stur            x0, [fp, #-8]
    // 0x865e1c: r0 = CounterRepository()
    //     0x865e1c: bl              #0x865e3c  ; AllocateCounterRepositoryStub -> CounterRepository (size=0xc)
    // 0x865e20: ldur            x1, [fp, #-8]
    // 0x865e24: StoreField: r0->field_7 = r1
    //     0x865e24: stur            w1, [x0, #7]
    // 0x865e28: LeaveFrame
    //     0x865e28: mov             SP, fp
    //     0x865e2c: ldp             fp, lr, [SP], #0x10
    // 0x865e30: ret
    //     0x865e30: ret             
    // 0x865e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865e38: b               #0x865de0
  }
  [closure] ZiarahRemoteRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865e48, size: 0x9c
    // 0x865e48: EnterFrame
    //     0x865e48: stp             fp, lr, [SP, #-0x10]!
    //     0x865e4c: mov             fp, SP
    // 0x865e50: AllocStack(0x20)
    //     0x865e50: sub             SP, SP, #0x20
    // 0x865e54: CheckStackOverflow
    //     0x865e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865e58: cmp             SP, x16
    //     0x865e5c: b.ls            #0x865edc
    // 0x865e60: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865e60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865e64: ldr             x0, [x0, #0x2670]
    //     0x865e68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865e6c: cmp             w0, w16
    //     0x865e70: b.ne            #0x865e7c
    //     0x865e74: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865e78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865e7c: r16 = <ApiService>
    //     0x865e7c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865e80: ldr             x16, [x16, #0xb00]
    // 0x865e84: r30 = "app_api_v2"
    //     0x865e84: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x865e88: ldr             lr, [lr, #0xb18]
    // 0x865e8c: stp             lr, x16, [SP]
    // 0x865e90: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865e90: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865e94: r0 = Inst.find()
    //     0x865e94: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865e98: stur            x0, [fp, #-8]
    // 0x865e9c: r16 = <LocationRepository>
    //     0x865e9c: add             x16, PP, #0xf, lsl #12  ; [pp+0xff78] TypeArguments: <LocationRepository>
    //     0x865ea0: ldr             x16, [x16, #0xf78]
    // 0x865ea4: r30 = "location_repo"
    //     0x865ea4: add             lr, PP, #0xf, lsl #12  ; [pp+0xff80] "location_repo"
    //     0x865ea8: ldr             lr, [lr, #0xf80]
    // 0x865eac: stp             lr, x16, [SP]
    // 0x865eb0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865eb0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865eb4: r0 = Inst.find()
    //     0x865eb4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865eb8: stur            x0, [fp, #-0x10]
    // 0x865ebc: r0 = ZiarahRemoteRepository()
    //     0x865ebc: bl              #0x865ee4  ; AllocateZiarahRemoteRepositoryStub -> ZiarahRemoteRepository (size=0x10)
    // 0x865ec0: ldur            x1, [fp, #-8]
    // 0x865ec4: StoreField: r0->field_7 = r1
    //     0x865ec4: stur            w1, [x0, #7]
    // 0x865ec8: ldur            x1, [fp, #-0x10]
    // 0x865ecc: StoreField: r0->field_b = r1
    //     0x865ecc: stur            w1, [x0, #0xb]
    // 0x865ed0: LeaveFrame
    //     0x865ed0: mov             SP, fp
    //     0x865ed4: ldp             fp, lr, [SP], #0x10
    // 0x865ed8: ret
    //     0x865ed8: ret             
    // 0x865edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865edc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865ee0: b               #0x865e60
  }
  [closure] ZiarahLocalRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865ef0, size: 0x8c
    // 0x865ef0: EnterFrame
    //     0x865ef0: stp             fp, lr, [SP, #-0x10]!
    //     0x865ef4: mov             fp, SP
    // 0x865ef8: AllocStack(0x20)
    //     0x865ef8: sub             SP, SP, #0x20
    // 0x865efc: CheckStackOverflow
    //     0x865efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865f00: cmp             SP, x16
    //     0x865f04: b.ls            #0x865f74
    // 0x865f08: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865f08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865f0c: ldr             x0, [x0, #0x2670]
    //     0x865f10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865f14: cmp             w0, w16
    //     0x865f18: b.ne            #0x865f24
    //     0x865f1c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865f20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865f24: r16 = <ContentStorage>
    //     0x865f24: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x865f28: r30 = "content_storage"
    //     0x865f28: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x865f2c: stp             lr, x16, [SP]
    // 0x865f30: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865f30: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865f34: r0 = Inst.find()
    //     0x865f34: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865f38: stur            x0, [fp, #-8]
    // 0x865f3c: r16 = <LocationStorage>
    //     0x865f3c: ldr             x16, [PP, #0xc8]  ; [pp+0xc8] TypeArguments: <LocationStorage>
    // 0x865f40: r30 = "location_storage"
    //     0x865f40: ldr             lr, [PP, #0xb8]  ; [pp+0xb8] "location_storage"
    // 0x865f44: stp             lr, x16, [SP]
    // 0x865f48: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865f48: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865f4c: r0 = Inst.find()
    //     0x865f4c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865f50: stur            x0, [fp, #-0x10]
    // 0x865f54: r0 = ZiarahLocalRepository()
    //     0x865f54: bl              #0x865f7c  ; AllocateZiarahLocalRepositoryStub -> ZiarahLocalRepository (size=0x10)
    // 0x865f58: ldur            x1, [fp, #-8]
    // 0x865f5c: StoreField: r0->field_7 = r1
    //     0x865f5c: stur            w1, [x0, #7]
    // 0x865f60: ldur            x1, [fp, #-0x10]
    // 0x865f64: StoreField: r0->field_b = r1
    //     0x865f64: stur            w1, [x0, #0xb]
    // 0x865f68: LeaveFrame
    //     0x865f68: mov             SP, fp
    //     0x865f6c: ldp             fp, lr, [SP], #0x10
    // 0x865f70: ret
    //     0x865f70: ret             
    // 0x865f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865f74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865f78: b               #0x865f08
  }
  [closure] TutorialRepository <anonymous closure>(dynamic) {
    // ** addr: 0x865f88, size: 0x94
    // 0x865f88: EnterFrame
    //     0x865f88: stp             fp, lr, [SP, #-0x10]!
    //     0x865f8c: mov             fp, SP
    // 0x865f90: AllocStack(0x20)
    //     0x865f90: sub             SP, SP, #0x20
    // 0x865f94: CheckStackOverflow
    //     0x865f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865f98: cmp             SP, x16
    //     0x865f9c: b.ls            #0x866014
    // 0x865fa0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x865fa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865fa4: ldr             x0, [x0, #0x2670]
    //     0x865fa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865fac: cmp             w0, w16
    //     0x865fb0: b.ne            #0x865fbc
    //     0x865fb4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x865fb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x865fbc: r16 = <ApiService>
    //     0x865fbc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x865fc0: ldr             x16, [x16, #0xb00]
    // 0x865fc4: r30 = "app_api_v2"
    //     0x865fc4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x865fc8: ldr             lr, [lr, #0xb18]
    // 0x865fcc: stp             lr, x16, [SP]
    // 0x865fd0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865fd0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865fd4: r0 = Inst.find()
    //     0x865fd4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865fd8: stur            x0, [fp, #-8]
    // 0x865fdc: r16 = <ContentStorage>
    //     0x865fdc: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x865fe0: r30 = "content_storage"
    //     0x865fe0: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x865fe4: stp             lr, x16, [SP]
    // 0x865fe8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x865fe8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x865fec: r0 = Inst.find()
    //     0x865fec: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x865ff0: stur            x0, [fp, #-0x10]
    // 0x865ff4: r0 = TutorialRepository()
    //     0x865ff4: bl              #0x86601c  ; AllocateTutorialRepositoryStub -> TutorialRepository (size=0x10)
    // 0x865ff8: ldur            x1, [fp, #-8]
    // 0x865ffc: StoreField: r0->field_7 = r1
    //     0x865ffc: stur            w1, [x0, #7]
    // 0x866000: ldur            x1, [fp, #-0x10]
    // 0x866004: StoreField: r0->field_b = r1
    //     0x866004: stur            w1, [x0, #0xb]
    // 0x866008: LeaveFrame
    //     0x866008: mov             SP, fp
    //     0x86600c: ldp             fp, lr, [SP], #0x10
    // 0x866010: ret
    //     0x866010: ret             
    // 0x866014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866018: b               #0x865fa0
  }
  [closure] KalamRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866028, size: 0x74
    // 0x866028: EnterFrame
    //     0x866028: stp             fp, lr, [SP, #-0x10]!
    //     0x86602c: mov             fp, SP
    // 0x866030: AllocStack(0x18)
    //     0x866030: sub             SP, SP, #0x18
    // 0x866034: CheckStackOverflow
    //     0x866034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866038: cmp             SP, x16
    //     0x86603c: b.ls            #0x866094
    // 0x866040: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866040: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866044: ldr             x0, [x0, #0x2670]
    //     0x866048: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86604c: cmp             w0, w16
    //     0x866050: b.ne            #0x86605c
    //     0x866054: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866058: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86605c: r16 = <ApiService>
    //     0x86605c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866060: ldr             x16, [x16, #0xb00]
    // 0x866064: r30 = "app_api_v2"
    //     0x866064: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x866068: ldr             lr, [lr, #0xb18]
    // 0x86606c: stp             lr, x16, [SP]
    // 0x866070: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866070: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866074: r0 = Inst.find()
    //     0x866074: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866078: stur            x0, [fp, #-8]
    // 0x86607c: r0 = KalamRepository()
    //     0x86607c: bl              #0x86609c  ; AllocateKalamRepositoryStub -> KalamRepository (size=0xc)
    // 0x866080: ldur            x1, [fp, #-8]
    // 0x866084: StoreField: r0->field_7 = r1
    //     0x866084: stur            w1, [x0, #7]
    // 0x866088: LeaveFrame
    //     0x866088: mov             SP, fp
    //     0x86608c: ldp             fp, lr, [SP], #0x10
    // 0x866090: ret
    //     0x866090: ret             
    // 0x866094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866098: b               #0x866040
  }
  [closure] EncyclopediaRemoteRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8660a8, size: 0x74
    // 0x8660a8: EnterFrame
    //     0x8660a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8660ac: mov             fp, SP
    // 0x8660b0: AllocStack(0x18)
    //     0x8660b0: sub             SP, SP, #0x18
    // 0x8660b4: CheckStackOverflow
    //     0x8660b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8660b8: cmp             SP, x16
    //     0x8660bc: b.ls            #0x866114
    // 0x8660c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8660c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8660c4: ldr             x0, [x0, #0x2670]
    //     0x8660c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8660cc: cmp             w0, w16
    //     0x8660d0: b.ne            #0x8660dc
    //     0x8660d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8660d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8660dc: r16 = <ApiService>
    //     0x8660dc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8660e0: ldr             x16, [x16, #0xb00]
    // 0x8660e4: r30 = "app_api_v2"
    //     0x8660e4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x8660e8: ldr             lr, [lr, #0xb18]
    // 0x8660ec: stp             lr, x16, [SP]
    // 0x8660f0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8660f0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8660f4: r0 = Inst.find()
    //     0x8660f4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8660f8: stur            x0, [fp, #-8]
    // 0x8660fc: r0 = EncyclopediaRemoteRepository()
    //     0x8660fc: bl              #0x86611c  ; AllocateEncyclopediaRemoteRepositoryStub -> EncyclopediaRemoteRepository (size=0xc)
    // 0x866100: ldur            x1, [fp, #-8]
    // 0x866104: StoreField: r0->field_7 = r1
    //     0x866104: stur            w1, [x0, #7]
    // 0x866108: LeaveFrame
    //     0x866108: mov             SP, fp
    //     0x86610c: ldp             fp, lr, [SP], #0x10
    // 0x866110: ret
    //     0x866110: ret             
    // 0x866114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866118: b               #0x8660c0
  }
  [closure] HikmahRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866128, size: 0x68
    // 0x866128: EnterFrame
    //     0x866128: stp             fp, lr, [SP, #-0x10]!
    //     0x86612c: mov             fp, SP
    // 0x866130: AllocStack(0x10)
    //     0x866130: sub             SP, SP, #0x10
    // 0x866134: CheckStackOverflow
    //     0x866134: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866138: cmp             SP, x16
    //     0x86613c: b.ls            #0x866188
    // 0x866140: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866140: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866144: ldr             x0, [x0, #0x2670]
    //     0x866148: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86614c: cmp             w0, w16
    //     0x866150: b.ne            #0x86615c
    //     0x866154: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866158: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86615c: r16 = <ApiService>
    //     0x86615c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866160: ldr             x16, [x16, #0xb00]
    // 0x866164: r30 = "app_api_v2"
    //     0x866164: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x866168: ldr             lr, [lr, #0xb18]
    // 0x86616c: stp             lr, x16, [SP]
    // 0x866170: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866170: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866174: r0 = Inst.find()
    //     0x866174: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866178: r0 = HikmahRepository()
    //     0x866178: bl              #0x866190  ; AllocateHikmahRepositoryStub -> HikmahRepository (size=0x8)
    // 0x86617c: LeaveFrame
    //     0x86617c: mov             SP, fp
    //     0x866180: ldp             fp, lr, [SP], #0x10
    // 0x866184: ret
    //     0x866184: ret             
    // 0x866188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86618c: b               #0x866140
  }
  [closure] HajiRepository <anonymous closure>(dynamic) {
    // ** addr: 0x86619c, size: 0x74
    // 0x86619c: EnterFrame
    //     0x86619c: stp             fp, lr, [SP, #-0x10]!
    //     0x8661a0: mov             fp, SP
    // 0x8661a4: AllocStack(0x18)
    //     0x8661a4: sub             SP, SP, #0x18
    // 0x8661a8: CheckStackOverflow
    //     0x8661a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8661ac: cmp             SP, x16
    //     0x8661b0: b.ls            #0x866208
    // 0x8661b4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8661b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8661b8: ldr             x0, [x0, #0x2670]
    //     0x8661bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8661c0: cmp             w0, w16
    //     0x8661c4: b.ne            #0x8661d0
    //     0x8661c8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8661cc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8661d0: r16 = <ApiService>
    //     0x8661d0: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8661d4: ldr             x16, [x16, #0xb00]
    // 0x8661d8: r30 = "app_api_v2"
    //     0x8661d8: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x8661dc: ldr             lr, [lr, #0xb18]
    // 0x8661e0: stp             lr, x16, [SP]
    // 0x8661e4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8661e4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8661e8: r0 = Inst.find()
    //     0x8661e8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8661ec: stur            x0, [fp, #-8]
    // 0x8661f0: r0 = HajiRepository()
    //     0x8661f0: bl              #0x866210  ; AllocateHajiRepositoryStub -> HajiRepository (size=0xc)
    // 0x8661f4: ldur            x1, [fp, #-8]
    // 0x8661f8: StoreField: r0->field_7 = r1
    //     0x8661f8: stur            w1, [x0, #7]
    // 0x8661fc: LeaveFrame
    //     0x8661fc: mov             SP, fp
    //     0x866200: ldp             fp, lr, [SP], #0x10
    // 0x866204: ret
    //     0x866204: ret             
    // 0x866208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866208: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86620c: b               #0x8661b4
  }
  [closure] ManasikRepository <anonymous closure>(dynamic) {
    // ** addr: 0x86621c, size: 0x68
    // 0x86621c: EnterFrame
    //     0x86621c: stp             fp, lr, [SP, #-0x10]!
    //     0x866220: mov             fp, SP
    // 0x866224: AllocStack(0x10)
    //     0x866224: sub             SP, SP, #0x10
    // 0x866228: CheckStackOverflow
    //     0x866228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86622c: cmp             SP, x16
    //     0x866230: b.ls            #0x86627c
    // 0x866234: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866234: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866238: ldr             x0, [x0, #0x2670]
    //     0x86623c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866240: cmp             w0, w16
    //     0x866244: b.ne            #0x866250
    //     0x866248: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x86624c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866250: r16 = <ApiService>
    //     0x866250: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866254: ldr             x16, [x16, #0xb00]
    // 0x866258: r30 = "app_api_v2"
    //     0x866258: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x86625c: ldr             lr, [lr, #0xb18]
    // 0x866260: stp             lr, x16, [SP]
    // 0x866264: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866264: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866268: r0 = Inst.find()
    //     0x866268: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x86626c: r0 = ManasikRepository()
    //     0x86626c: bl              #0x866284  ; AllocateManasikRepositoryStub -> ManasikRepository (size=0x8)
    // 0x866270: LeaveFrame
    //     0x866270: mov             SP, fp
    //     0x866274: ldp             fp, lr, [SP], #0x10
    // 0x866278: ret
    //     0x866278: ret             
    // 0x86627c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86627c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866280: b               #0x866234
  }
  [closure] EncyclopediaLocalRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866290, size: 0x6c
    // 0x866290: EnterFrame
    //     0x866290: stp             fp, lr, [SP, #-0x10]!
    //     0x866294: mov             fp, SP
    // 0x866298: AllocStack(0x18)
    //     0x866298: sub             SP, SP, #0x18
    // 0x86629c: CheckStackOverflow
    //     0x86629c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8662a0: cmp             SP, x16
    //     0x8662a4: b.ls            #0x8662f4
    // 0x8662a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8662a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8662ac: ldr             x0, [x0, #0x2670]
    //     0x8662b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8662b4: cmp             w0, w16
    //     0x8662b8: b.ne            #0x8662c4
    //     0x8662bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8662c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8662c4: r16 = <ContentStorage>
    //     0x8662c4: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x8662c8: r30 = "content_storage"
    //     0x8662c8: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x8662cc: stp             lr, x16, [SP]
    // 0x8662d0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8662d0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8662d4: r0 = Inst.find()
    //     0x8662d4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8662d8: stur            x0, [fp, #-8]
    // 0x8662dc: r0 = EncyclopediaLocalRepository()
    //     0x8662dc: bl              #0x8662fc  ; AllocateEncyclopediaLocalRepositoryStub -> EncyclopediaLocalRepository (size=0xc)
    // 0x8662e0: ldur            x1, [fp, #-8]
    // 0x8662e4: StoreField: r0->field_7 = r1
    //     0x8662e4: stur            w1, [x0, #7]
    // 0x8662e8: LeaveFrame
    //     0x8662e8: mov             SP, fp
    //     0x8662ec: ldp             fp, lr, [SP], #0x10
    // 0x8662f0: ret
    //     0x8662f0: ret             
    // 0x8662f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8662f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8662f8: b               #0x8662a8
  }
  [closure] DoaRemoteRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866308, size: 0x9c
    // 0x866308: EnterFrame
    //     0x866308: stp             fp, lr, [SP, #-0x10]!
    //     0x86630c: mov             fp, SP
    // 0x866310: AllocStack(0x20)
    //     0x866310: sub             SP, SP, #0x20
    // 0x866314: CheckStackOverflow
    //     0x866314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866318: cmp             SP, x16
    //     0x86631c: b.ls            #0x86639c
    // 0x866320: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866320: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866324: ldr             x0, [x0, #0x2670]
    //     0x866328: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86632c: cmp             w0, w16
    //     0x866330: b.ne            #0x86633c
    //     0x866334: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866338: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86633c: r16 = <ApiService>
    //     0x86633c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866340: ldr             x16, [x16, #0xb00]
    // 0x866344: r30 = "app_api_v2"
    //     0x866344: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x866348: ldr             lr, [lr, #0xb18]
    // 0x86634c: stp             lr, x16, [SP]
    // 0x866350: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866350: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866354: r0 = Inst.find()
    //     0x866354: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866358: stur            x0, [fp, #-8]
    // 0x86635c: r16 = <RemoteConfigService>
    //     0x86635c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x866360: ldr             x16, [x16, #0xae8]
    // 0x866364: r30 = "remote_config_service"
    //     0x866364: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x866368: ldr             lr, [lr, #0xaf0]
    // 0x86636c: stp             lr, x16, [SP]
    // 0x866370: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866370: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866374: r0 = Inst.find()
    //     0x866374: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866378: stur            x0, [fp, #-0x10]
    // 0x86637c: r0 = DoaRemoteRepository()
    //     0x86637c: bl              #0x8663a4  ; AllocateDoaRemoteRepositoryStub -> DoaRemoteRepository (size=0x10)
    // 0x866380: ldur            x1, [fp, #-8]
    // 0x866384: StoreField: r0->field_7 = r1
    //     0x866384: stur            w1, [x0, #7]
    // 0x866388: ldur            x1, [fp, #-0x10]
    // 0x86638c: StoreField: r0->field_b = r1
    //     0x86638c: stur            w1, [x0, #0xb]
    // 0x866390: LeaveFrame
    //     0x866390: mov             SP, fp
    //     0x866394: ldp             fp, lr, [SP], #0x10
    // 0x866398: ret
    //     0x866398: ret             
    // 0x86639c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86639c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8663a0: b               #0x866320
  }
  [closure] DoaLocalRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8663b0, size: 0x6c
    // 0x8663b0: EnterFrame
    //     0x8663b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8663b4: mov             fp, SP
    // 0x8663b8: AllocStack(0x18)
    //     0x8663b8: sub             SP, SP, #0x18
    // 0x8663bc: CheckStackOverflow
    //     0x8663bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8663c0: cmp             SP, x16
    //     0x8663c4: b.ls            #0x866414
    // 0x8663c8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8663c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8663cc: ldr             x0, [x0, #0x2670]
    //     0x8663d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8663d4: cmp             w0, w16
    //     0x8663d8: b.ne            #0x8663e4
    //     0x8663dc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8663e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8663e4: r16 = <ContentStorage>
    //     0x8663e4: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x8663e8: r30 = "content_storage"
    //     0x8663e8: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x8663ec: stp             lr, x16, [SP]
    // 0x8663f0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8663f0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8663f4: r0 = Inst.find()
    //     0x8663f4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8663f8: stur            x0, [fp, #-8]
    // 0x8663fc: r0 = DoaLocalRepository()
    //     0x8663fc: bl              #0x86641c  ; AllocateDoaLocalRepositoryStub -> DoaLocalRepository (size=0xc)
    // 0x866400: ldur            x1, [fp, #-8]
    // 0x866404: StoreField: r0->field_7 = r1
    //     0x866404: stur            w1, [x0, #7]
    // 0x866408: LeaveFrame
    //     0x866408: mov             SP, fp
    //     0x86640c: ldp             fp, lr, [SP], #0x10
    // 0x866410: ret
    //     0x866410: ret             
    // 0x866414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866418: b               #0x8663c8
  }
  [closure] VideoRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866428, size: 0x74
    // 0x866428: EnterFrame
    //     0x866428: stp             fp, lr, [SP, #-0x10]!
    //     0x86642c: mov             fp, SP
    // 0x866430: AllocStack(0x18)
    //     0x866430: sub             SP, SP, #0x18
    // 0x866434: CheckStackOverflow
    //     0x866434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866438: cmp             SP, x16
    //     0x86643c: b.ls            #0x866494
    // 0x866440: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866440: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866444: ldr             x0, [x0, #0x2670]
    //     0x866448: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86644c: cmp             w0, w16
    //     0x866450: b.ne            #0x86645c
    //     0x866454: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866458: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86645c: r16 = <ApiService>
    //     0x86645c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866460: ldr             x16, [x16, #0xb00]
    // 0x866464: r30 = "cms_api_v2"
    //     0x866464: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb48] "cms_api_v2"
    //     0x866468: ldr             lr, [lr, #0xb48]
    // 0x86646c: stp             lr, x16, [SP]
    // 0x866470: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866470: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866474: r0 = Inst.find()
    //     0x866474: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866478: stur            x0, [fp, #-8]
    // 0x86647c: r0 = VideoRepository()
    //     0x86647c: bl              #0x86649c  ; AllocateVideoRepositoryStub -> VideoRepository (size=0xc)
    // 0x866480: ldur            x1, [fp, #-8]
    // 0x866484: StoreField: r0->field_7 = r1
    //     0x866484: stur            w1, [x0, #7]
    // 0x866488: LeaveFrame
    //     0x866488: mov             SP, fp
    //     0x86648c: ldp             fp, lr, [SP], #0x10
    // 0x866490: ret
    //     0x866490: ret             
    // 0x866494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866498: b               #0x866440
  }
  [closure] TopicRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8664a8, size: 0x74
    // 0x8664a8: EnterFrame
    //     0x8664a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8664ac: mov             fp, SP
    // 0x8664b0: AllocStack(0x18)
    //     0x8664b0: sub             SP, SP, #0x18
    // 0x8664b4: CheckStackOverflow
    //     0x8664b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8664b8: cmp             SP, x16
    //     0x8664bc: b.ls            #0x866514
    // 0x8664c0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8664c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8664c4: ldr             x0, [x0, #0x2670]
    //     0x8664c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8664cc: cmp             w0, w16
    //     0x8664d0: b.ne            #0x8664dc
    //     0x8664d4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8664d8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8664dc: r16 = <ApiService>
    //     0x8664dc: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8664e0: ldr             x16, [x16, #0xb00]
    // 0x8664e4: r30 = "cms_api_v2"
    //     0x8664e4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb48] "cms_api_v2"
    //     0x8664e8: ldr             lr, [lr, #0xb48]
    // 0x8664ec: stp             lr, x16, [SP]
    // 0x8664f0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8664f0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8664f4: r0 = Inst.find()
    //     0x8664f4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8664f8: stur            x0, [fp, #-8]
    // 0x8664fc: r0 = TopicRepository()
    //     0x8664fc: bl              #0x86651c  ; AllocateTopicRepositoryStub -> TopicRepository (size=0xc)
    // 0x866500: ldur            x1, [fp, #-8]
    // 0x866504: StoreField: r0->field_7 = r1
    //     0x866504: stur            w1, [x0, #7]
    // 0x866508: LeaveFrame
    //     0x866508: mov             SP, fp
    //     0x86650c: ldp             fp, lr, [SP], #0x10
    // 0x866510: ret
    //     0x866510: ret             
    // 0x866514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866518: b               #0x8664c0
  }
  [closure] DonationRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866528, size: 0x84
    // 0x866528: EnterFrame
    //     0x866528: stp             fp, lr, [SP, #-0x10]!
    //     0x86652c: mov             fp, SP
    // 0x866530: AllocStack(0x20)
    //     0x866530: sub             SP, SP, #0x20
    // 0x866534: CheckStackOverflow
    //     0x866534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866538: cmp             SP, x16
    //     0x86653c: b.ls            #0x8665a4
    // 0x866540: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866540: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866544: ldr             x0, [x0, #0x2670]
    //     0x866548: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86654c: cmp             w0, w16
    //     0x866550: b.ne            #0x86655c
    //     0x866554: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866558: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86655c: r16 = <ApiService>
    //     0x86655c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866560: ldr             x16, [x16, #0xb00]
    // 0x866564: r30 = "donation_api"
    //     0x866564: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb68] "donation_api"
    //     0x866568: ldr             lr, [lr, #0xb68]
    // 0x86656c: stp             lr, x16, [SP]
    // 0x866570: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866570: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866574: r0 = Inst.find()
    //     0x866574: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866578: stur            x0, [fp, #-8]
    // 0x86657c: r0 = find()
    //     0x86657c: bl              #0x83fc1c  ; [package:nuonline/services/device_service.dart] DeviceService::find
    // 0x866580: stur            x0, [fp, #-0x10]
    // 0x866584: r0 = DonationRepository()
    //     0x866584: bl              #0x8665ac  ; AllocateDonationRepositoryStub -> DonationRepository (size=0x10)
    // 0x866588: ldur            x1, [fp, #-8]
    // 0x86658c: StoreField: r0->field_7 = r1
    //     0x86658c: stur            w1, [x0, #7]
    // 0x866590: ldur            x1, [fp, #-0x10]
    // 0x866594: StoreField: r0->field_b = r1
    //     0x866594: stur            w1, [x0, #0xb]
    // 0x866598: LeaveFrame
    //     0x866598: mov             SP, fp
    //     0x86659c: ldp             fp, lr, [SP], #0x10
    // 0x8665a0: ret
    //     0x8665a0: ret             
    // 0x8665a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8665a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8665a8: b               #0x866540
  }
  [closure] ArticleRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8665b8, size: 0xbc
    // 0x8665b8: EnterFrame
    //     0x8665b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8665bc: mov             fp, SP
    // 0x8665c0: AllocStack(0x28)
    //     0x8665c0: sub             SP, SP, #0x28
    // 0x8665c4: CheckStackOverflow
    //     0x8665c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8665c8: cmp             SP, x16
    //     0x8665cc: b.ls            #0x86666c
    // 0x8665d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8665d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8665d4: ldr             x0, [x0, #0x2670]
    //     0x8665d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8665dc: cmp             w0, w16
    //     0x8665e0: b.ne            #0x8665ec
    //     0x8665e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8665e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8665ec: r16 = <ApiService>
    //     0x8665ec: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8665f0: ldr             x16, [x16, #0xb00]
    // 0x8665f4: r30 = "cms_api_v2"
    //     0x8665f4: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb48] "cms_api_v2"
    //     0x8665f8: ldr             lr, [lr, #0xb48]
    // 0x8665fc: stp             lr, x16, [SP]
    // 0x866600: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866600: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866604: r0 = Inst.find()
    //     0x866604: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866608: stur            x0, [fp, #-8]
    // 0x86660c: r16 = <ContentStorage>
    //     0x86660c: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x866610: r30 = "content_storage"
    //     0x866610: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x866614: stp             lr, x16, [SP]
    // 0x866618: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866618: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x86661c: r0 = Inst.find()
    //     0x86661c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866620: stur            x0, [fp, #-0x10]
    // 0x866624: r16 = <ApiService>
    //     0x866624: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866628: ldr             x16, [x16, #0xb00]
    // 0x86662c: r30 = "cms_api_v4"
    //     0x86662c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb58] "cms_api_v4"
    //     0x866630: ldr             lr, [lr, #0xb58]
    // 0x866634: stp             lr, x16, [SP]
    // 0x866638: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866638: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x86663c: r0 = Inst.find()
    //     0x86663c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866640: stur            x0, [fp, #-0x18]
    // 0x866644: r0 = ArticleRepository()
    //     0x866644: bl              #0x866674  ; AllocateArticleRepositoryStub -> ArticleRepository (size=0x14)
    // 0x866648: ldur            x1, [fp, #-8]
    // 0x86664c: StoreField: r0->field_7 = r1
    //     0x86664c: stur            w1, [x0, #7]
    // 0x866650: ldur            x1, [fp, #-0x10]
    // 0x866654: StoreField: r0->field_b = r1
    //     0x866654: stur            w1, [x0, #0xb]
    // 0x866658: ldur            x1, [fp, #-0x18]
    // 0x86665c: StoreField: r0->field_f = r1
    //     0x86665c: stur            w1, [x0, #0xf]
    // 0x866660: LeaveFrame
    //     0x866660: mov             SP, fp
    //     0x866664: ldp             fp, lr, [SP], #0x10
    // 0x866668: ret
    //     0x866668: ret             
    // 0x86666c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86666c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866670: b               #0x8665d0
  }
  [closure] PageRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866680, size: 0x74
    // 0x866680: EnterFrame
    //     0x866680: stp             fp, lr, [SP, #-0x10]!
    //     0x866684: mov             fp, SP
    // 0x866688: AllocStack(0x18)
    //     0x866688: sub             SP, SP, #0x18
    // 0x86668c: CheckStackOverflow
    //     0x86668c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866690: cmp             SP, x16
    //     0x866694: b.ls            #0x8666ec
    // 0x866698: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866698: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86669c: ldr             x0, [x0, #0x2670]
    //     0x8666a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8666a4: cmp             w0, w16
    //     0x8666a8: b.ne            #0x8666b4
    //     0x8666ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8666b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8666b4: r16 = <ApiService>
    //     0x8666b4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8666b8: ldr             x16, [x16, #0xb00]
    // 0x8666bc: r30 = "cms_api_v2"
    //     0x8666bc: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb48] "cms_api_v2"
    //     0x8666c0: ldr             lr, [lr, #0xb48]
    // 0x8666c4: stp             lr, x16, [SP]
    // 0x8666c8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8666c8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8666cc: r0 = Inst.find()
    //     0x8666cc: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8666d0: stur            x0, [fp, #-8]
    // 0x8666d4: r0 = PageRepository()
    //     0x8666d4: bl              #0x8666f4  ; AllocatePageRepositoryStub -> PageRepository (size=0xc)
    // 0x8666d8: ldur            x1, [fp, #-8]
    // 0x8666dc: StoreField: r0->field_7 = r1
    //     0x8666dc: stur            w1, [x0, #7]
    // 0x8666e0: LeaveFrame
    //     0x8666e0: mov             SP, fp
    //     0x8666e4: ldp             fp, lr, [SP], #0x10
    // 0x8666e8: ret
    //     0x8666e8: ret             
    // 0x8666ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8666ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8666f0: b               #0x866698
  }
  [closure] CategoryRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866700, size: 0x94
    // 0x866700: EnterFrame
    //     0x866700: stp             fp, lr, [SP, #-0x10]!
    //     0x866704: mov             fp, SP
    // 0x866708: AllocStack(0x20)
    //     0x866708: sub             SP, SP, #0x20
    // 0x86670c: CheckStackOverflow
    //     0x86670c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866710: cmp             SP, x16
    //     0x866714: b.ls            #0x86678c
    // 0x866718: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866718: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86671c: ldr             x0, [x0, #0x2670]
    //     0x866720: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866724: cmp             w0, w16
    //     0x866728: b.ne            #0x866734
    //     0x86672c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866730: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866734: r16 = <ApiService>
    //     0x866734: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866738: ldr             x16, [x16, #0xb00]
    // 0x86673c: r30 = "cms_api_v2"
    //     0x86673c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb48] "cms_api_v2"
    //     0x866740: ldr             lr, [lr, #0xb48]
    // 0x866744: stp             lr, x16, [SP]
    // 0x866748: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866748: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x86674c: r0 = Inst.find()
    //     0x86674c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866750: stur            x0, [fp, #-8]
    // 0x866754: r16 = <ContentStorage>
    //     0x866754: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x866758: r30 = "content_storage"
    //     0x866758: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x86675c: stp             lr, x16, [SP]
    // 0x866760: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866760: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866764: r0 = Inst.find()
    //     0x866764: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866768: stur            x0, [fp, #-0x10]
    // 0x86676c: r0 = CategoryRepository()
    //     0x86676c: bl              #0x866794  ; AllocateCategoryRepositoryStub -> CategoryRepository (size=0x10)
    // 0x866770: ldur            x1, [fp, #-8]
    // 0x866774: StoreField: r0->field_7 = r1
    //     0x866774: stur            w1, [x0, #7]
    // 0x866778: ldur            x1, [fp, #-0x10]
    // 0x86677c: StoreField: r0->field_b = r1
    //     0x86677c: stur            w1, [x0, #0xb]
    // 0x866780: LeaveFrame
    //     0x866780: mov             SP, fp
    //     0x866784: ldp             fp, lr, [SP], #0x10
    // 0x866788: ret
    //     0x866788: ret             
    // 0x86678c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86678c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866790: b               #0x866718
  }
  [closure] NotificationRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8667a0, size: 0x6c
    // 0x8667a0: EnterFrame
    //     0x8667a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8667a4: mov             fp, SP
    // 0x8667a8: AllocStack(0x18)
    //     0x8667a8: sub             SP, SP, #0x18
    // 0x8667ac: CheckStackOverflow
    //     0x8667ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8667b0: cmp             SP, x16
    //     0x8667b4: b.ls            #0x866804
    // 0x8667b8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8667b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8667bc: ldr             x0, [x0, #0x2670]
    //     0x8667c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8667c4: cmp             w0, w16
    //     0x8667c8: b.ne            #0x8667d4
    //     0x8667cc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8667d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8667d4: r16 = <NotificationService>
    //     0x8667d4: ldr             x16, [PP, #0xa8]  ; [pp+0xa8] TypeArguments: <NotificationService>
    // 0x8667d8: r30 = "notification_service"
    //     0x8667d8: ldr             lr, [PP, #0x90]  ; [pp+0x90] "notification_service"
    // 0x8667dc: stp             lr, x16, [SP]
    // 0x8667e0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8667e0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8667e4: r0 = Inst.find()
    //     0x8667e4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8667e8: stur            x0, [fp, #-8]
    // 0x8667ec: r0 = NotificationRepository()
    //     0x8667ec: bl              #0x86680c  ; AllocateNotificationRepositoryStub -> NotificationRepository (size=0xc)
    // 0x8667f0: ldur            x1, [fp, #-8]
    // 0x8667f4: StoreField: r0->field_7 = r1
    //     0x8667f4: stur            w1, [x0, #7]
    // 0x8667f8: LeaveFrame
    //     0x8667f8: mov             SP, fp
    //     0x8667fc: ldp             fp, lr, [SP], #0x10
    // 0x866800: ret
    //     0x866800: ret             
    // 0x866804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866808: b               #0x8667b8
  }
  [closure] VersesRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866818, size: 0x9c
    // 0x866818: EnterFrame
    //     0x866818: stp             fp, lr, [SP, #-0x10]!
    //     0x86681c: mov             fp, SP
    // 0x866820: AllocStack(0x20)
    //     0x866820: sub             SP, SP, #0x20
    // 0x866824: CheckStackOverflow
    //     0x866824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866828: cmp             SP, x16
    //     0x86682c: b.ls            #0x8668ac
    // 0x866830: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866830: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866834: ldr             x0, [x0, #0x2670]
    //     0x866838: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86683c: cmp             w0, w16
    //     0x866840: b.ne            #0x86684c
    //     0x866844: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866848: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86684c: r16 = <JsonService>
    //     0x86684c: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x866850: ldr             x16, [x16, #0xb0]
    // 0x866854: r30 = "json_service"
    //     0x866854: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x866858: ldr             lr, [lr, #0xa0]
    // 0x86685c: stp             lr, x16, [SP]
    // 0x866860: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866860: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866864: r0 = Inst.find()
    //     0x866864: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866868: stur            x0, [fp, #-8]
    // 0x86686c: r16 = <QuranStorage>
    //     0x86686c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdbc0] TypeArguments: <QuranStorage>
    //     0x866870: ldr             x16, [x16, #0xbc0]
    // 0x866874: r30 = "quran_storage"
    //     0x866874: add             lr, PP, #0xd, lsl #12  ; [pp+0xdbc8] "quran_storage"
    //     0x866878: ldr             lr, [lr, #0xbc8]
    // 0x86687c: stp             lr, x16, [SP]
    // 0x866880: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866880: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866884: r0 = Inst.find()
    //     0x866884: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866888: stur            x0, [fp, #-0x10]
    // 0x86688c: r0 = VersesRepository()
    //     0x86688c: bl              #0x8668b4  ; AllocateVersesRepositoryStub -> VersesRepository (size=0x10)
    // 0x866890: ldur            x1, [fp, #-8]
    // 0x866894: StoreField: r0->field_b = r1
    //     0x866894: stur            w1, [x0, #0xb]
    // 0x866898: ldur            x1, [fp, #-0x10]
    // 0x86689c: StoreField: r0->field_7 = r1
    //     0x86689c: stur            w1, [x0, #7]
    // 0x8668a0: LeaveFrame
    //     0x8668a0: mov             SP, fp
    //     0x8668a4: ldp             fp, lr, [SP], #0x10
    // 0x8668a8: ret
    //     0x8668a8: ret             
    // 0x8668ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8668ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8668b0: b               #0x866830
  }
  [closure] TahlilRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8668c0, size: 0x74
    // 0x8668c0: EnterFrame
    //     0x8668c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8668c4: mov             fp, SP
    // 0x8668c8: AllocStack(0x18)
    //     0x8668c8: sub             SP, SP, #0x18
    // 0x8668cc: CheckStackOverflow
    //     0x8668cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8668d0: cmp             SP, x16
    //     0x8668d4: b.ls            #0x86692c
    // 0x8668d8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8668d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8668dc: ldr             x0, [x0, #0x2670]
    //     0x8668e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8668e4: cmp             w0, w16
    //     0x8668e8: b.ne            #0x8668f4
    //     0x8668ec: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8668f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8668f4: r16 = <JsonService>
    //     0x8668f4: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x8668f8: ldr             x16, [x16, #0xb0]
    // 0x8668fc: r30 = "json_service"
    //     0x8668fc: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x866900: ldr             lr, [lr, #0xa0]
    // 0x866904: stp             lr, x16, [SP]
    // 0x866908: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866908: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x86690c: r0 = Inst.find()
    //     0x86690c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866910: stur            x0, [fp, #-8]
    // 0x866914: r0 = TahlilRepository()
    //     0x866914: bl              #0x866934  ; AllocateTahlilRepositoryStub -> TahlilRepository (size=0xc)
    // 0x866918: ldur            x1, [fp, #-8]
    // 0x86691c: StoreField: r0->field_7 = r1
    //     0x86691c: stur            w1, [x0, #7]
    // 0x866920: LeaveFrame
    //     0x866920: mov             SP, fp
    //     0x866924: ldp             fp, lr, [SP], #0x10
    // 0x866928: ret
    //     0x866928: ret             
    // 0x86692c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86692c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866930: b               #0x8668d8
  }
  [closure] PrayerTimeRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866940, size: 0x8c
    // 0x866940: EnterFrame
    //     0x866940: stp             fp, lr, [SP, #-0x10]!
    //     0x866944: mov             fp, SP
    // 0x866948: AllocStack(0x20)
    //     0x866948: sub             SP, SP, #0x20
    // 0x86694c: CheckStackOverflow
    //     0x86694c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866950: cmp             SP, x16
    //     0x866954: b.ls            #0x8669c4
    // 0x866958: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866958: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86695c: ldr             x0, [x0, #0x2670]
    //     0x866960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866964: cmp             w0, w16
    //     0x866968: b.ne            #0x866974
    //     0x86696c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866970: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866974: r16 = <PrayerTimeStorage>
    //     0x866974: ldr             x16, [PP, #0xe0]  ; [pp+0xe0] TypeArguments: <PrayerTimeStorage>
    // 0x866978: r30 = "prayer_time_storage"
    //     0x866978: ldr             lr, [PP, #0xd0]  ; [pp+0xd0] "prayer_time_storage"
    // 0x86697c: stp             lr, x16, [SP]
    // 0x866980: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866980: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866984: r0 = Inst.find()
    //     0x866984: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866988: stur            x0, [fp, #-8]
    // 0x86698c: r16 = <LocationStorage>
    //     0x86698c: ldr             x16, [PP, #0xc8]  ; [pp+0xc8] TypeArguments: <LocationStorage>
    // 0x866990: r30 = "location_storage"
    //     0x866990: ldr             lr, [PP, #0xb8]  ; [pp+0xb8] "location_storage"
    // 0x866994: stp             lr, x16, [SP]
    // 0x866998: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866998: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x86699c: r0 = Inst.find()
    //     0x86699c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8669a0: stur            x0, [fp, #-0x10]
    // 0x8669a4: r0 = PrayerTimeRepository()
    //     0x8669a4: bl              #0x8669cc  ; AllocatePrayerTimeRepositoryStub -> PrayerTimeRepository (size=0x10)
    // 0x8669a8: ldur            x1, [fp, #-8]
    // 0x8669ac: StoreField: r0->field_7 = r1
    //     0x8669ac: stur            w1, [x0, #7]
    // 0x8669b0: ldur            x1, [fp, #-0x10]
    // 0x8669b4: StoreField: r0->field_b = r1
    //     0x8669b4: stur            w1, [x0, #0xb]
    // 0x8669b8: LeaveFrame
    //     0x8669b8: mov             SP, fp
    //     0x8669bc: ldp             fp, lr, [SP], #0x10
    // 0x8669c0: ret
    //     0x8669c0: ret             
    // 0x8669c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8669c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8669c8: b               #0x866958
  }
  [closure] JuzRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8669d8, size: 0x9c
    // 0x8669d8: EnterFrame
    //     0x8669d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8669dc: mov             fp, SP
    // 0x8669e0: AllocStack(0x20)
    //     0x8669e0: sub             SP, SP, #0x20
    // 0x8669e4: CheckStackOverflow
    //     0x8669e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8669e8: cmp             SP, x16
    //     0x8669ec: b.ls            #0x866a6c
    // 0x8669f0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8669f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8669f4: ldr             x0, [x0, #0x2670]
    //     0x8669f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8669fc: cmp             w0, w16
    //     0x866a00: b.ne            #0x866a0c
    //     0x866a04: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866a08: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866a0c: r16 = <QuranStorage>
    //     0x866a0c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdbc0] TypeArguments: <QuranStorage>
    //     0x866a10: ldr             x16, [x16, #0xbc0]
    // 0x866a14: r30 = "quran_storage"
    //     0x866a14: add             lr, PP, #0xd, lsl #12  ; [pp+0xdbc8] "quran_storage"
    //     0x866a18: ldr             lr, [lr, #0xbc8]
    // 0x866a1c: stp             lr, x16, [SP]
    // 0x866a20: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866a20: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866a24: r0 = Inst.find()
    //     0x866a24: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866a28: stur            x0, [fp, #-8]
    // 0x866a2c: r16 = <JsonService>
    //     0x866a2c: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x866a30: ldr             x16, [x16, #0xb0]
    // 0x866a34: r30 = "json_service"
    //     0x866a34: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x866a38: ldr             lr, [lr, #0xa0]
    // 0x866a3c: stp             lr, x16, [SP]
    // 0x866a40: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866a40: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866a44: r0 = Inst.find()
    //     0x866a44: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866a48: stur            x0, [fp, #-0x10]
    // 0x866a4c: r0 = JuzRepository()
    //     0x866a4c: bl              #0x866a74  ; AllocateJuzRepositoryStub -> JuzRepository (size=0x10)
    // 0x866a50: ldur            x1, [fp, #-8]
    // 0x866a54: StoreField: r0->field_7 = r1
    //     0x866a54: stur            w1, [x0, #7]
    // 0x866a58: ldur            x1, [fp, #-0x10]
    // 0x866a5c: StoreField: r0->field_b = r1
    //     0x866a5c: stur            w1, [x0, #0xb]
    // 0x866a60: LeaveFrame
    //     0x866a60: mov             SP, fp
    //     0x866a64: ldp             fp, lr, [SP], #0x10
    // 0x866a68: ret
    //     0x866a68: ret             
    // 0x866a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866a70: b               #0x8669f0
  }
  [closure] DonorRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866a80, size: 0x74
    // 0x866a80: EnterFrame
    //     0x866a80: stp             fp, lr, [SP, #-0x10]!
    //     0x866a84: mov             fp, SP
    // 0x866a88: AllocStack(0x18)
    //     0x866a88: sub             SP, SP, #0x18
    // 0x866a8c: CheckStackOverflow
    //     0x866a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866a90: cmp             SP, x16
    //     0x866a94: b.ls            #0x866aec
    // 0x866a98: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866a98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866a9c: ldr             x0, [x0, #0x2670]
    //     0x866aa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866aa4: cmp             w0, w16
    //     0x866aa8: b.ne            #0x866ab4
    //     0x866aac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866ab0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866ab4: r16 = <DonationStorage>
    //     0x866ab4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdbf8] TypeArguments: <DonationStorage>
    //     0x866ab8: ldr             x16, [x16, #0xbf8]
    // 0x866abc: r30 = "donation_storage"
    //     0x866abc: add             lr, PP, #0xd, lsl #12  ; [pp+0xdc00] "donation_storage"
    //     0x866ac0: ldr             lr, [lr, #0xc00]
    // 0x866ac4: stp             lr, x16, [SP]
    // 0x866ac8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866ac8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866acc: r0 = Inst.find()
    //     0x866acc: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866ad0: stur            x0, [fp, #-8]
    // 0x866ad4: r0 = DonorRepository()
    //     0x866ad4: bl              #0x866af4  ; AllocateDonorRepositoryStub -> DonorRepository (size=0xc)
    // 0x866ad8: ldur            x1, [fp, #-8]
    // 0x866adc: StoreField: r0->field_7 = r1
    //     0x866adc: stur            w1, [x0, #7]
    // 0x866ae0: LeaveFrame
    //     0x866ae0: mov             SP, fp
    //     0x866ae4: ldp             fp, lr, [SP], #0x10
    // 0x866ae8: ret
    //     0x866ae8: ret             
    // 0x866aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866af0: b               #0x866a98
  }
  [closure] PriceRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866b00, size: 0x94
    // 0x866b00: EnterFrame
    //     0x866b00: stp             fp, lr, [SP, #-0x10]!
    //     0x866b04: mov             fp, SP
    // 0x866b08: AllocStack(0x20)
    //     0x866b08: sub             SP, SP, #0x20
    // 0x866b0c: CheckStackOverflow
    //     0x866b0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866b10: cmp             SP, x16
    //     0x866b14: b.ls            #0x866b8c
    // 0x866b18: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866b18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866b1c: ldr             x0, [x0, #0x2670]
    //     0x866b20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866b24: cmp             w0, w16
    //     0x866b28: b.ne            #0x866b34
    //     0x866b2c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866b30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866b34: r16 = <ApiService>
    //     0x866b34: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x866b38: ldr             x16, [x16, #0xb00]
    // 0x866b3c: r30 = "app_api_v2"
    //     0x866b3c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb18] "app_api_v2"
    //     0x866b40: ldr             lr, [lr, #0xb18]
    // 0x866b44: stp             lr, x16, [SP]
    // 0x866b48: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866b48: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866b4c: r0 = Inst.find()
    //     0x866b4c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866b50: stur            x0, [fp, #-8]
    // 0x866b54: r16 = <AppStorage>
    //     0x866b54: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x866b58: r30 = "app_storage"
    //     0x866b58: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x866b5c: stp             lr, x16, [SP]
    // 0x866b60: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866b60: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866b64: r0 = Inst.find()
    //     0x866b64: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866b68: stur            x0, [fp, #-0x10]
    // 0x866b6c: r0 = PriceRepository()
    //     0x866b6c: bl              #0x866b94  ; AllocatePriceRepositoryStub -> PriceRepository (size=0x10)
    // 0x866b70: ldur            x1, [fp, #-8]
    // 0x866b74: StoreField: r0->field_7 = r1
    //     0x866b74: stur            w1, [x0, #7]
    // 0x866b78: ldur            x1, [fp, #-0x10]
    // 0x866b7c: StoreField: r0->field_b = r1
    //     0x866b7c: stur            w1, [x0, #0xb]
    // 0x866b80: LeaveFrame
    //     0x866b80: mov             SP, fp
    //     0x866b84: ldp             fp, lr, [SP], #0x10
    // 0x866b88: ret
    //     0x866b88: ret             
    // 0x866b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866b8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866b90: b               #0x866b18
  }
  [closure] SurahRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866ba0, size: 0x9c
    // 0x866ba0: EnterFrame
    //     0x866ba0: stp             fp, lr, [SP, #-0x10]!
    //     0x866ba4: mov             fp, SP
    // 0x866ba8: AllocStack(0x20)
    //     0x866ba8: sub             SP, SP, #0x20
    // 0x866bac: CheckStackOverflow
    //     0x866bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866bb0: cmp             SP, x16
    //     0x866bb4: b.ls            #0x866c34
    // 0x866bb8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866bb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866bbc: ldr             x0, [x0, #0x2670]
    //     0x866bc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866bc4: cmp             w0, w16
    //     0x866bc8: b.ne            #0x866bd4
    //     0x866bcc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866bd0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866bd4: r16 = <QuranStorage>
    //     0x866bd4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdbc0] TypeArguments: <QuranStorage>
    //     0x866bd8: ldr             x16, [x16, #0xbc0]
    // 0x866bdc: r30 = "quran_storage"
    //     0x866bdc: add             lr, PP, #0xd, lsl #12  ; [pp+0xdbc8] "quran_storage"
    //     0x866be0: ldr             lr, [lr, #0xbc8]
    // 0x866be4: stp             lr, x16, [SP]
    // 0x866be8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866be8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866bec: r0 = Inst.find()
    //     0x866bec: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866bf0: stur            x0, [fp, #-8]
    // 0x866bf4: r16 = <JsonService>
    //     0x866bf4: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x866bf8: ldr             x16, [x16, #0xb0]
    // 0x866bfc: r30 = "json_service"
    //     0x866bfc: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x866c00: ldr             lr, [lr, #0xa0]
    // 0x866c04: stp             lr, x16, [SP]
    // 0x866c08: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866c08: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866c0c: r0 = Inst.find()
    //     0x866c0c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866c10: stur            x0, [fp, #-0x10]
    // 0x866c14: r0 = SurahRepository()
    //     0x866c14: bl              #0x866c3c  ; AllocateSurahRepositoryStub -> SurahRepository (size=0x10)
    // 0x866c18: ldur            x1, [fp, #-8]
    // 0x866c1c: StoreField: r0->field_7 = r1
    //     0x866c1c: stur            w1, [x0, #7]
    // 0x866c20: ldur            x1, [fp, #-0x10]
    // 0x866c24: StoreField: r0->field_b = r1
    //     0x866c24: stur            w1, [x0, #0xb]
    // 0x866c28: LeaveFrame
    //     0x866c28: mov             SP, fp
    //     0x866c2c: ldp             fp, lr, [SP], #0x10
    // 0x866c30: ret
    //     0x866c30: ret             
    // 0x866c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866c34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866c38: b               #0x866bb8
  }
  [closure] TafsirRepository <anonymous closure>(dynamic) {
    // ** addr: 0x866c48, size: 0xb0
    // 0x866c48: EnterFrame
    //     0x866c48: stp             fp, lr, [SP, #-0x10]!
    //     0x866c4c: mov             fp, SP
    // 0x866c50: AllocStack(0x28)
    //     0x866c50: sub             SP, SP, #0x28
    // 0x866c54: CheckStackOverflow
    //     0x866c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866c58: cmp             SP, x16
    //     0x866c5c: b.ls            #0x866cf0
    // 0x866c60: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x866c60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866c64: ldr             x0, [x0, #0x2670]
    //     0x866c68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866c6c: cmp             w0, w16
    //     0x866c70: b.ne            #0x866c7c
    //     0x866c74: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x866c78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866c7c: r16 = <TafsirStorage>
    //     0x866c7c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdba8] TypeArguments: <TafsirStorage>
    //     0x866c80: ldr             x16, [x16, #0xba8]
    // 0x866c84: r30 = "tafsir_storage"
    //     0x866c84: add             lr, PP, #0xd, lsl #12  ; [pp+0xdbb0] "tafsir_storage"
    //     0x866c88: ldr             lr, [lr, #0xbb0]
    // 0x866c8c: stp             lr, x16, [SP]
    // 0x866c90: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866c90: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866c94: r0 = Inst.find()
    //     0x866c94: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866c98: stur            x0, [fp, #-8]
    // 0x866c9c: r16 = <JsonService>
    //     0x866c9c: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x866ca0: ldr             x16, [x16, #0xb0]
    // 0x866ca4: r30 = "json_service"
    //     0x866ca4: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x866ca8: ldr             lr, [lr, #0xa0]
    // 0x866cac: stp             lr, x16, [SP]
    // 0x866cb0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x866cb0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x866cb4: r0 = Inst.find()
    //     0x866cb4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x866cb8: stur            x0, [fp, #-0x10]
    // 0x866cbc: r0 = TafsirRepository()
    //     0x866cbc: bl              #0x8674d8  ; AllocateTafsirRepositoryStub -> TafsirRepository (size=0x10)
    // 0x866cc0: mov             x2, x0
    // 0x866cc4: ldur            x0, [fp, #-8]
    // 0x866cc8: stur            x2, [fp, #-0x18]
    // 0x866ccc: StoreField: r2->field_7 = r0
    //     0x866ccc: stur            w0, [x2, #7]
    // 0x866cd0: ldur            x0, [fp, #-0x10]
    // 0x866cd4: StoreField: r2->field_b = r0
    //     0x866cd4: stur            w0, [x2, #0xb]
    // 0x866cd8: mov             x1, x2
    // 0x866cdc: r0 = init()
    //     0x866cdc: bl              #0x866cf8  ; [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init
    // 0x866ce0: ldur            x0, [fp, #-0x18]
    // 0x866ce4: LeaveFrame
    //     0x866ce4: mov             SP, fp
    //     0x866ce8: ldp             fp, lr, [SP], #0x10
    // 0x866cec: ret
    //     0x866cec: ret             
    // 0x866cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866cf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866cf4: b               #0x866c60
  }
  [closure] LocationRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8674e4, size: 0xb0
    // 0x8674e4: EnterFrame
    //     0x8674e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8674e8: mov             fp, SP
    // 0x8674ec: AllocStack(0x20)
    //     0x8674ec: sub             SP, SP, #0x20
    // 0x8674f0: CheckStackOverflow
    //     0x8674f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8674f4: cmp             SP, x16
    //     0x8674f8: b.ls            #0x86758c
    // 0x8674fc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8674fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x867500: ldr             x0, [x0, #0x2670]
    //     0x867504: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x867508: cmp             w0, w16
    //     0x86750c: b.ne            #0x867518
    //     0x867510: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x867514: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x867518: r16 = <LocationStorage>
    //     0x867518: ldr             x16, [PP, #0xc8]  ; [pp+0xc8] TypeArguments: <LocationStorage>
    // 0x86751c: r30 = "location_storage"
    //     0x86751c: ldr             lr, [PP, #0xb8]  ; [pp+0xb8] "location_storage"
    // 0x867520: stp             lr, x16, [SP]
    // 0x867524: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x867524: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867528: r0 = Inst.find()
    //     0x867528: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x86752c: stur            x0, [fp, #-8]
    // 0x867530: r16 = <JsonService>
    //     0x867530: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x867534: ldr             x16, [x16, #0xb0]
    // 0x867538: r30 = "json_service"
    //     0x867538: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x86753c: ldr             lr, [lr, #0xa0]
    // 0x867540: stp             lr, x16, [SP]
    // 0x867544: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x867544: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867548: r0 = Inst.find()
    //     0x867548: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x86754c: r16 = <ApiService>
    //     0x86754c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x867550: ldr             x16, [x16, #0xb00]
    // 0x867554: r30 = "app_api_v3"
    //     0x867554: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb28] "app_api_v3"
    //     0x867558: ldr             lr, [lr, #0xb28]
    // 0x86755c: stp             lr, x16, [SP]
    // 0x867560: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x867560: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867564: r0 = Inst.find()
    //     0x867564: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867568: stur            x0, [fp, #-0x10]
    // 0x86756c: r0 = LocationRepository()
    //     0x86756c: bl              #0x867594  ; AllocateLocationRepositoryStub -> LocationRepository (size=0x10)
    // 0x867570: ldur            x1, [fp, #-8]
    // 0x867574: StoreField: r0->field_b = r1
    //     0x867574: stur            w1, [x0, #0xb]
    // 0x867578: ldur            x1, [fp, #-0x10]
    // 0x86757c: StoreField: r0->field_7 = r1
    //     0x86757c: stur            w1, [x0, #7]
    // 0x867580: LeaveFrame
    //     0x867580: mov             SP, fp
    //     0x867584: ldp             fp, lr, [SP], #0x10
    // 0x867588: ret
    //     0x867588: ret             
    // 0x86758c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86758c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867590: b               #0x8674fc
  }
  [closure] HomeRepository <anonymous closure>(dynamic) {
    // ** addr: 0x8675a0, size: 0xd8
    // 0x8675a0: EnterFrame
    //     0x8675a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8675a4: mov             fp, SP
    // 0x8675a8: AllocStack(0x28)
    //     0x8675a8: sub             SP, SP, #0x28
    // 0x8675ac: CheckStackOverflow
    //     0x8675ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8675b0: cmp             SP, x16
    //     0x8675b4: b.ls            #0x867670
    // 0x8675b8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8675b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8675bc: ldr             x0, [x0, #0x2670]
    //     0x8675c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8675c4: cmp             w0, w16
    //     0x8675c8: b.ne            #0x8675d4
    //     0x8675cc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8675d0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8675d4: r16 = <ApiService>
    //     0x8675d4: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <ApiService>
    //     0x8675d8: ldr             x16, [x16, #0xb00]
    // 0x8675dc: r30 = "app_api_v3"
    //     0x8675dc: add             lr, PP, #0xd, lsl #12  ; [pp+0xdb28] "app_api_v3"
    //     0x8675e0: ldr             lr, [lr, #0xb28]
    // 0x8675e4: stp             lr, x16, [SP]
    // 0x8675e8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8675e8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8675ec: r0 = Inst.find()
    //     0x8675ec: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8675f0: stur            x0, [fp, #-8]
    // 0x8675f4: r16 = <ContentStorage>
    //     0x8675f4: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x8675f8: r30 = "content_storage"
    //     0x8675f8: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x8675fc: stp             lr, x16, [SP]
    // 0x867600: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x867600: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867604: r0 = Inst.find()
    //     0x867604: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867608: stur            x0, [fp, #-0x10]
    // 0x86760c: r16 = <JsonService>
    //     0x86760c: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x867610: ldr             x16, [x16, #0xb0]
    // 0x867614: r30 = "json_service"
    //     0x867614: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x867618: ldr             lr, [lr, #0xa0]
    // 0x86761c: stp             lr, x16, [SP]
    // 0x867620: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x867620: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867624: r0 = Inst.find()
    //     0x867624: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867628: r16 = <RemoteConfigService>
    //     0x867628: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x86762c: ldr             x16, [x16, #0xae8]
    // 0x867630: r30 = "remote_config_service"
    //     0x867630: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x867634: ldr             lr, [lr, #0xaf0]
    // 0x867638: stp             lr, x16, [SP]
    // 0x86763c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x86763c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867640: r0 = Inst.find()
    //     0x867640: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867644: stur            x0, [fp, #-0x18]
    // 0x867648: r0 = HomeRepository()
    //     0x867648: bl              #0x867678  ; AllocateHomeRepositoryStub -> HomeRepository (size=0x14)
    // 0x86764c: ldur            x1, [fp, #-8]
    // 0x867650: StoreField: r0->field_7 = r1
    //     0x867650: stur            w1, [x0, #7]
    // 0x867654: ldur            x1, [fp, #-0x10]
    // 0x867658: StoreField: r0->field_b = r1
    //     0x867658: stur            w1, [x0, #0xb]
    // 0x86765c: ldur            x1, [fp, #-0x18]
    // 0x867660: StoreField: r0->field_f = r1
    //     0x867660: stur            w1, [x0, #0xf]
    // 0x867664: LeaveFrame
    //     0x867664: mov             SP, fp
    //     0x867668: ldp             fp, lr, [SP], #0x10
    // 0x86766c: ret
    //     0x86766c: ret             
    // 0x867670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867670: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867674: b               #0x8675b8
  }
  [closure] MenuRepository <anonymous closure>(dynamic) {
    // ** addr: 0x867684, size: 0xb4
    // 0x867684: EnterFrame
    //     0x867684: stp             fp, lr, [SP, #-0x10]!
    //     0x867688: mov             fp, SP
    // 0x86768c: AllocStack(0x28)
    //     0x86768c: sub             SP, SP, #0x28
    // 0x867690: CheckStackOverflow
    //     0x867690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867694: cmp             SP, x16
    //     0x867698: b.ls            #0x867730
    // 0x86769c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x86769c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8676a0: ldr             x0, [x0, #0x2670]
    //     0x8676a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8676a8: cmp             w0, w16
    //     0x8676ac: b.ne            #0x8676b8
    //     0x8676b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8676b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8676b8: r16 = <AppStorage>
    //     0x8676b8: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x8676bc: r30 = "app_storage"
    //     0x8676bc: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x8676c0: stp             lr, x16, [SP]
    // 0x8676c4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8676c4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8676c8: r0 = Inst.find()
    //     0x8676c8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8676cc: stur            x0, [fp, #-8]
    // 0x8676d0: r16 = <ContentStorage>
    //     0x8676d0: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x8676d4: r30 = "content_storage"
    //     0x8676d4: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x8676d8: stp             lr, x16, [SP]
    // 0x8676dc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8676dc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8676e0: r0 = Inst.find()
    //     0x8676e0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8676e4: stur            x0, [fp, #-0x10]
    // 0x8676e8: r16 = <RemoteConfigService>
    //     0x8676e8: add             x16, PP, #0xd, lsl #12  ; [pp+0xdae8] TypeArguments: <RemoteConfigService>
    //     0x8676ec: ldr             x16, [x16, #0xae8]
    // 0x8676f0: r30 = "remote_config_service"
    //     0x8676f0: add             lr, PP, #0xd, lsl #12  ; [pp+0xdaf0] "remote_config_service"
    //     0x8676f4: ldr             lr, [lr, #0xaf0]
    // 0x8676f8: stp             lr, x16, [SP]
    // 0x8676fc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8676fc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867700: r0 = Inst.find()
    //     0x867700: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867704: stur            x0, [fp, #-0x18]
    // 0x867708: r0 = MenuRepository()
    //     0x867708: bl              #0x867738  ; AllocateMenuRepositoryStub -> MenuRepository (size=0x14)
    // 0x86770c: ldur            x1, [fp, #-8]
    // 0x867710: StoreField: r0->field_7 = r1
    //     0x867710: stur            w1, [x0, #7]
    // 0x867714: ldur            x1, [fp, #-0x10]
    // 0x867718: StoreField: r0->field_b = r1
    //     0x867718: stur            w1, [x0, #0xb]
    // 0x86771c: ldur            x1, [fp, #-0x18]
    // 0x867720: StoreField: r0->field_f = r1
    //     0x867720: stur            w1, [x0, #0xf]
    // 0x867724: LeaveFrame
    //     0x867724: mov             SP, fp
    //     0x867728: ldp             fp, lr, [SP], #0x10
    // 0x86772c: ret
    //     0x86772c: ret             
    // 0x867730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867734: b               #0x86769c
  }
  [closure] EventRepository <anonymous closure>(dynamic) {
    // ** addr: 0x867744, size: 0x94
    // 0x867744: EnterFrame
    //     0x867744: stp             fp, lr, [SP, #-0x10]!
    //     0x867748: mov             fp, SP
    // 0x86774c: AllocStack(0x20)
    //     0x86774c: sub             SP, SP, #0x20
    // 0x867750: CheckStackOverflow
    //     0x867750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867754: cmp             SP, x16
    //     0x867758: b.ls            #0x8677d0
    // 0x86775c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x86775c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x867760: ldr             x0, [x0, #0x2670]
    //     0x867764: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x867768: cmp             w0, w16
    //     0x86776c: b.ne            #0x867778
    //     0x867770: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x867774: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x867778: r16 = <JsonService>
    //     0x867778: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] TypeArguments: <JsonService>
    //     0x86777c: ldr             x16, [x16, #0xb0]
    // 0x867780: r30 = "json_service"
    //     0x867780: add             lr, PP, #9, lsl #12  ; [pp+0x90a0] "json_service"
    //     0x867784: ldr             lr, [lr, #0xa0]
    // 0x867788: stp             lr, x16, [SP]
    // 0x86778c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x86778c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x867790: r0 = Inst.find()
    //     0x867790: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x867794: stur            x0, [fp, #-8]
    // 0x867798: r16 = <HijriService>
    //     0x867798: ldr             x16, [PP, #0x120]  ; [pp+0x120] TypeArguments: <HijriService>
    // 0x86779c: r30 = "hijri_service"
    //     0x86779c: ldr             lr, [PP, #0x118]  ; [pp+0x118] "hijri_service"
    // 0x8677a0: stp             lr, x16, [SP]
    // 0x8677a4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8677a4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8677a8: r0 = Inst.find()
    //     0x8677a8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8677ac: stur            x0, [fp, #-0x10]
    // 0x8677b0: r0 = EventRepository()
    //     0x8677b0: bl              #0x821c04  ; AllocateEventRepositoryStub -> EventRepository (size=0x10)
    // 0x8677b4: ldur            x1, [fp, #-8]
    // 0x8677b8: StoreField: r0->field_7 = r1
    //     0x8677b8: stur            w1, [x0, #7]
    // 0x8677bc: ldur            x1, [fp, #-0x10]
    // 0x8677c0: StoreField: r0->field_b = r1
    //     0x8677c0: stur            w1, [x0, #0xb]
    // 0x8677c4: LeaveFrame
    //     0x8677c4: mov             SP, fp
    //     0x8677c8: ldp             fp, lr, [SP], #0x10
    // 0x8677cc: ret
    //     0x8677cc: ret             
    // 0x8677d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8677d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8677d4: b               #0x86775c
  }
}
