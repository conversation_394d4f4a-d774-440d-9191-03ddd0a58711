// lib: , url: package:nuonline/app/data/repositories/surah_repository.dart

// class id: 1050097, size: 0x8
class :: {
}

// class id: 1079, size: 0x10, field offset: 0x8
class SurahRepository extends Object {

  _ findSurahById(/* No info */) async {
    // ** addr: 0x8ae3a4, size: 0xc0
    // 0x8ae3a4: EnterFrame
    //     0x8ae3a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae3a8: mov             fp, SP
    // 0x8ae3ac: AllocStack(0x38)
    //     0x8ae3ac: sub             SP, SP, #0x38
    // 0x8ae3b0: SetupParameters(SurahRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8ae3b0: stur            NULL, [fp, #-8]
    //     0x8ae3b4: stur            x1, [fp, #-0x10]
    //     0x8ae3b8: stur            x2, [fp, #-0x18]
    // 0x8ae3bc: CheckStackOverflow
    //     0x8ae3bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae3c0: cmp             SP, x16
    //     0x8ae3c4: b.ls            #0x8ae458
    // 0x8ae3c8: InitAsync() -> Future<Surah>
    //     0x8ae3c8: add             x0, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8ae3cc: ldr             x0, [x0, #0x1e0]
    //     0x8ae3d0: bl              #0x661298  ; InitAsyncStub
    // 0x8ae3d4: ldur            x1, [fp, #-0x10]
    // 0x8ae3d8: r0 = init()
    //     0x8ae3d8: bl              #0x8ae4cc  ; [package:nuonline/app/data/repositories/surah_repository.dart] SurahRepository::init
    // 0x8ae3dc: mov             x1, x0
    // 0x8ae3e0: stur            x1, [fp, #-0x20]
    // 0x8ae3e4: r0 = Await()
    //     0x8ae3e4: bl              #0x661044  ; AwaitStub
    // 0x8ae3e8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8ae3e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ae3ec: ldr             x0, [x0, #0x2728]
    //     0x8ae3f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ae3f4: cmp             w0, w16
    //     0x8ae3f8: b.ne            #0x8ae404
    //     0x8ae3fc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8ae400: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ae404: r16 = <Surah>
    //     0x8ae404: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8ae408: ldr             x16, [x16, #0x1e0]
    // 0x8ae40c: stp             x0, x16, [SP, #8]
    // 0x8ae410: r16 = "v2_quran_surah"
    //     0x8ae410: add             x16, PP, #0xf, lsl #12  ; [pp+0xf208] "v2_quran_surah"
    //     0x8ae414: ldr             x16, [x16, #0x208]
    // 0x8ae418: str             x16, [SP]
    // 0x8ae41c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae41c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae420: r0 = box()
    //     0x8ae420: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ae424: mov             x3, x0
    // 0x8ae428: ldur            x2, [fp, #-0x18]
    // 0x8ae42c: r0 = BoxInt64Instr(r2)
    //     0x8ae42c: sbfiz           x0, x2, #1, #0x1f
    //     0x8ae430: cmp             x2, x0, asr #1
    //     0x8ae434: b.eq            #0x8ae440
    //     0x8ae438: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ae43c: stur            x2, [x0, #7]
    // 0x8ae440: mov             x1, x3
    // 0x8ae444: mov             x2, x0
    // 0x8ae448: r0 = get()
    //     0x8ae448: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8ae44c: cmp             w0, NULL
    // 0x8ae450: b.eq            #0x8ae460
    // 0x8ae454: r0 = ReturnAsyncNotFuture()
    //     0x8ae454: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ae458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae45c: b               #0x8ae3c8
    // 0x8ae460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8ae460: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) async {
    // ** addr: 0x8ae4cc, size: 0x184
    // 0x8ae4cc: EnterFrame
    //     0x8ae4cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae4d0: mov             fp, SP
    // 0x8ae4d4: AllocStack(0x38)
    //     0x8ae4d4: sub             SP, SP, #0x38
    // 0x8ae4d8: SetupParameters(SurahRepository this /* r1 => r1, fp-0x10 */)
    //     0x8ae4d8: stur            NULL, [fp, #-8]
    //     0x8ae4dc: stur            x1, [fp, #-0x10]
    // 0x8ae4e0: CheckStackOverflow
    //     0x8ae4e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae4e4: cmp             SP, x16
    //     0x8ae4e8: b.ls            #0x8ae640
    // 0x8ae4ec: InitAsync() -> Future<void?>
    //     0x8ae4ec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ae4f0: bl              #0x661298  ; InitAsyncStub
    // 0x8ae4f4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8ae4f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ae4f8: ldr             x0, [x0, #0x2728]
    //     0x8ae4fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ae500: cmp             w0, w16
    //     0x8ae504: b.ne            #0x8ae510
    //     0x8ae508: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8ae50c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ae510: stur            x0, [fp, #-0x18]
    // 0x8ae514: r16 = <Surah>
    //     0x8ae514: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8ae518: ldr             x16, [x16, #0x1e0]
    // 0x8ae51c: stp             x0, x16, [SP, #8]
    // 0x8ae520: r16 = "v2_quran_surah"
    //     0x8ae520: add             x16, PP, #0xf, lsl #12  ; [pp+0xf208] "v2_quran_surah"
    //     0x8ae524: ldr             x16, [x16, #0x208]
    // 0x8ae528: str             x16, [SP]
    // 0x8ae52c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae52c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae530: r0 = box()
    //     0x8ae530: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ae534: mov             x1, x0
    // 0x8ae538: stur            x0, [fp, #-0x20]
    // 0x8ae53c: r0 = checkOpen()
    //     0x8ae53c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8ae540: ldur            x0, [fp, #-0x20]
    // 0x8ae544: LoadField: r1 = r0->field_1b
    //     0x8ae544: ldur            w1, [x0, #0x1b]
    // 0x8ae548: DecompressPointer r1
    //     0x8ae548: add             x1, x1, HEAP, lsl #32
    // 0x8ae54c: r16 = Sentinel
    //     0x8ae54c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ae550: cmp             w1, w16
    // 0x8ae554: b.eq            #0x8ae648
    // 0x8ae558: LoadField: r0 = r1->field_13
    //     0x8ae558: ldur            w0, [x1, #0x13]
    // 0x8ae55c: DecompressPointer r0
    //     0x8ae55c: add             x0, x0, HEAP, lsl #32
    // 0x8ae560: LoadField: r1 = r0->field_1f
    //     0x8ae560: ldur            x1, [x0, #0x1f]
    // 0x8ae564: cbnz            x1, #0x8ae638
    // 0x8ae568: ldur            x0, [fp, #-0x10]
    // 0x8ae56c: LoadField: r1 = r0->field_b
    //     0x8ae56c: ldur            w1, [x0, #0xb]
    // 0x8ae570: DecompressPointer r1
    //     0x8ae570: add             x1, x1, HEAP, lsl #32
    // 0x8ae574: r16 = <List>
    //     0x8ae574: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x8ae578: stp             x1, x16, [SP, #8]
    // 0x8ae57c: r16 = "surahs.json"
    //     0x8ae57c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b628] "surahs.json"
    //     0x8ae580: ldr             x16, [x16, #0x628]
    // 0x8ae584: str             x16, [SP]
    // 0x8ae588: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae588: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae58c: r0 = read()
    //     0x8ae58c: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x8ae590: r1 = Function '<anonymous closure>':.
    //     0x8ae590: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b630] AnonymousClosure: (0x8ae69c), in [package:nuonline/app/data/repositories/surah_repository.dart] SurahRepository::init (0x8ae4cc)
    //     0x8ae594: ldr             x1, [x1, #0x630]
    // 0x8ae598: r2 = Null
    //     0x8ae598: mov             x2, NULL
    // 0x8ae59c: stur            x0, [fp, #-0x20]
    // 0x8ae5a0: r0 = AllocateClosure()
    //     0x8ae5a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ae5a4: r16 = <Iterable<Surah>>
    //     0x8ae5a4: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b638] TypeArguments: <Iterable<Surah>>
    //     0x8ae5a8: ldr             x16, [x16, #0x638]
    // 0x8ae5ac: ldur            lr, [fp, #-0x20]
    // 0x8ae5b0: stp             lr, x16, [SP, #8]
    // 0x8ae5b4: str             x0, [SP]
    // 0x8ae5b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae5b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae5bc: r0 = then()
    //     0x8ae5bc: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8ae5c0: mov             x1, x0
    // 0x8ae5c4: stur            x1, [fp, #-0x20]
    // 0x8ae5c8: r0 = Await()
    //     0x8ae5c8: bl              #0x661044  ; AwaitStub
    // 0x8ae5cc: stur            x0, [fp, #-0x10]
    // 0x8ae5d0: r16 = <Surah>
    //     0x8ae5d0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8ae5d4: ldr             x16, [x16, #0x1e0]
    // 0x8ae5d8: ldur            lr, [fp, #-0x18]
    // 0x8ae5dc: stp             lr, x16, [SP, #8]
    // 0x8ae5e0: r16 = "v2_quran_surah"
    //     0x8ae5e0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf208] "v2_quran_surah"
    //     0x8ae5e4: ldr             x16, [x16, #0x208]
    // 0x8ae5e8: str             x16, [SP]
    // 0x8ae5ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae5ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae5f0: r0 = box()
    //     0x8ae5f0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ae5f4: r1 = Function '<anonymous closure>':.
    //     0x8ae5f4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b640] AnonymousClosure: (0x8ae650), in [package:nuonline/app/data/repositories/surah_repository.dart] SurahRepository::init (0x8ae4cc)
    //     0x8ae5f8: ldr             x1, [x1, #0x640]
    // 0x8ae5fc: r2 = Null
    //     0x8ae5fc: mov             x2, NULL
    // 0x8ae600: stur            x0, [fp, #-0x18]
    // 0x8ae604: r0 = AllocateClosure()
    //     0x8ae604: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ae608: ldur            x2, [fp, #-0x10]
    // 0x8ae60c: mov             x3, x0
    // 0x8ae610: r1 = <dynamic, Surah>
    //     0x8ae610: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b648] TypeArguments: <dynamic, Surah>
    //     0x8ae614: ldr             x1, [x1, #0x648]
    // 0x8ae618: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8ae618: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8ae61c: r0 = LinkedHashMap.fromIterable()
    //     0x8ae61c: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x8ae620: ldur            x1, [fp, #-0x18]
    // 0x8ae624: mov             x2, x0
    // 0x8ae628: r0 = putAll()
    //     0x8ae628: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x8ae62c: mov             x1, x0
    // 0x8ae630: stur            x1, [fp, #-0x10]
    // 0x8ae634: r0 = Await()
    //     0x8ae634: bl              #0x661044  ; AwaitStub
    // 0x8ae638: r0 = Null
    //     0x8ae638: mov             x0, NULL
    // 0x8ae63c: r0 = ReturnAsyncNotFuture()
    //     0x8ae63c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ae640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae640: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae644: b               #0x8ae4ec
    // 0x8ae648: r9 = keystore
    //     0x8ae648: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8ae64c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ae64c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ae650, size: 0x4c
    // 0x8ae650: EnterFrame
    //     0x8ae650: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae654: mov             fp, SP
    // 0x8ae658: AllocStack(0x8)
    //     0x8ae658: sub             SP, SP, #8
    // 0x8ae65c: CheckStackOverflow
    //     0x8ae65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae660: cmp             SP, x16
    //     0x8ae664: b.ls            #0x8ae694
    // 0x8ae668: ldr             x16, [fp, #0x10]
    // 0x8ae66c: str             x16, [SP]
    // 0x8ae670: r4 = 0
    //     0x8ae670: movz            x4, #0
    // 0x8ae674: ldr             x0, [SP]
    // 0x8ae678: r16 = UnlinkedCall_0x5f3c08
    //     0x8ae678: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b650] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8ae67c: add             x16, x16, #0x650
    // 0x8ae680: ldp             x5, lr, [x16]
    // 0x8ae684: blr             lr
    // 0x8ae688: LeaveFrame
    //     0x8ae688: mov             SP, fp
    //     0x8ae68c: ldp             fp, lr, [SP], #0x10
    // 0x8ae690: ret
    //     0x8ae690: ret             
    // 0x8ae694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae698: b               #0x8ae668
  }
  [closure] Iterable<Surah> <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x8ae69c, size: 0x74
    // 0x8ae69c: EnterFrame
    //     0x8ae69c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae6a0: mov             fp, SP
    // 0x8ae6a4: AllocStack(0x18)
    //     0x8ae6a4: sub             SP, SP, #0x18
    // 0x8ae6a8: CheckStackOverflow
    //     0x8ae6a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae6ac: cmp             SP, x16
    //     0x8ae6b0: b.ls            #0x8ae708
    // 0x8ae6b4: r1 = Function '<anonymous closure>':.
    //     0x8ae6b4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b660] AnonymousClosure: (0x8ae710), in [package:nuonline/app/data/repositories/surah_repository.dart] SurahRepository::init (0x8ae4cc)
    //     0x8ae6b8: ldr             x1, [x1, #0x660]
    // 0x8ae6bc: r2 = Null
    //     0x8ae6bc: mov             x2, NULL
    // 0x8ae6c0: r0 = AllocateClosure()
    //     0x8ae6c0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ae6c4: mov             x1, x0
    // 0x8ae6c8: ldr             x0, [fp, #0x10]
    // 0x8ae6cc: r2 = LoadClassIdInstr(r0)
    //     0x8ae6cc: ldur            x2, [x0, #-1]
    //     0x8ae6d0: ubfx            x2, x2, #0xc, #0x14
    // 0x8ae6d4: r16 = <Surah>
    //     0x8ae6d4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8ae6d8: ldr             x16, [x16, #0x1e0]
    // 0x8ae6dc: stp             x0, x16, [SP, #8]
    // 0x8ae6e0: str             x1, [SP]
    // 0x8ae6e4: mov             x0, x2
    // 0x8ae6e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae6e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae6ec: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8ae6ec: movz            x17, #0xf28c
    //     0x8ae6f0: add             lr, x0, x17
    //     0x8ae6f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ae6f8: blr             lr
    // 0x8ae6fc: LeaveFrame
    //     0x8ae6fc: mov             SP, fp
    //     0x8ae700: ldp             fp, lr, [SP], #0x10
    // 0x8ae704: ret
    //     0x8ae704: ret             
    // 0x8ae708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae708: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae70c: b               #0x8ae6b4
  }
  [closure] Surah <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ae710, size: 0x50
    // 0x8ae710: EnterFrame
    //     0x8ae710: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae714: mov             fp, SP
    // 0x8ae718: CheckStackOverflow
    //     0x8ae718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae71c: cmp             SP, x16
    //     0x8ae720: b.ls            #0x8ae758
    // 0x8ae724: ldr             x0, [fp, #0x10]
    // 0x8ae728: r2 = Null
    //     0x8ae728: mov             x2, NULL
    // 0x8ae72c: r1 = Null
    //     0x8ae72c: mov             x1, NULL
    // 0x8ae730: r8 = Map<String, dynamic>
    //     0x8ae730: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8ae734: r3 = Null
    //     0x8ae734: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b668] Null
    //     0x8ae738: ldr             x3, [x3, #0x668]
    // 0x8ae73c: r0 = Map<String, dynamic>()
    //     0x8ae73c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8ae740: ldr             x2, [fp, #0x10]
    // 0x8ae744: r1 = Null
    //     0x8ae744: mov             x1, NULL
    // 0x8ae748: r0 = Surah.fromMap()
    //     0x8ae748: bl              #0x8ae760  ; [package:nuonline/app/data/models/surah.dart] Surah::Surah.fromMap
    // 0x8ae74c: LeaveFrame
    //     0x8ae74c: mov             SP, fp
    //     0x8ae750: ldp             fp, lr, [SP], #0x10
    // 0x8ae754: ret
    //     0x8ae754: ret             
    // 0x8ae758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae758: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae75c: b               #0x8ae724
  }
  _ all(/* No info */) async {
    // ** addr: 0x8c1a00, size: 0xe0
    // 0x8c1a00: EnterFrame
    //     0x8c1a00: stp             fp, lr, [SP, #-0x10]!
    //     0x8c1a04: mov             fp, SP
    // 0x8c1a08: AllocStack(0x30)
    //     0x8c1a08: sub             SP, SP, #0x30
    // 0x8c1a0c: SetupParameters(SurahRepository this /* r1 => r1, fp-0x10 */)
    //     0x8c1a0c: stur            NULL, [fp, #-8]
    //     0x8c1a10: stur            x1, [fp, #-0x10]
    // 0x8c1a14: CheckStackOverflow
    //     0x8c1a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c1a18: cmp             SP, x16
    //     0x8c1a1c: b.ls            #0x8c1ad0
    // 0x8c1a20: InitAsync() -> Future<Either<StorageError, List<Surah>>>
    //     0x8c1a20: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3da78] TypeArguments: <Either<StorageError, List<Surah>>>
    //     0x8c1a24: ldr             x0, [x0, #0xa78]
    //     0x8c1a28: bl              #0x661298  ; InitAsyncStub
    // 0x8c1a2c: ldur            x1, [fp, #-0x10]
    // 0x8c1a30: r0 = init()
    //     0x8c1a30: bl              #0x8ae4cc  ; [package:nuonline/app/data/repositories/surah_repository.dart] SurahRepository::init
    // 0x8c1a34: mov             x1, x0
    // 0x8c1a38: stur            x1, [fp, #-0x18]
    // 0x8c1a3c: r0 = Await()
    //     0x8c1a3c: bl              #0x661044  ; AwaitStub
    // 0x8c1a40: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8c1a40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c1a44: ldr             x0, [x0, #0x2728]
    //     0x8c1a48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c1a4c: cmp             w0, w16
    //     0x8c1a50: b.ne            #0x8c1a5c
    //     0x8c1a54: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8c1a58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c1a5c: r16 = <Surah>
    //     0x8c1a5c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0x8c1a60: ldr             x16, [x16, #0x1e0]
    // 0x8c1a64: stp             x0, x16, [SP, #8]
    // 0x8c1a68: r16 = "v2_quran_surah"
    //     0x8c1a68: add             x16, PP, #0xf, lsl #12  ; [pp+0xf208] "v2_quran_surah"
    //     0x8c1a6c: ldr             x16, [x16, #0x208]
    // 0x8c1a70: str             x16, [SP]
    // 0x8c1a74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c1a74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c1a78: r0 = box()
    //     0x8c1a78: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c1a7c: mov             x1, x0
    // 0x8c1a80: stur            x0, [fp, #-0x10]
    // 0x8c1a84: r0 = checkOpen()
    //     0x8c1a84: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c1a88: ldur            x0, [fp, #-0x10]
    // 0x8c1a8c: LoadField: r1 = r0->field_1b
    //     0x8c1a8c: ldur            w1, [x0, #0x1b]
    // 0x8c1a90: DecompressPointer r1
    //     0x8c1a90: add             x1, x1, HEAP, lsl #32
    // 0x8c1a94: r16 = Sentinel
    //     0x8c1a94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c1a98: cmp             w1, w16
    // 0x8c1a9c: b.eq            #0x8c1ad8
    // 0x8c1aa0: r0 = getValues()
    //     0x8c1aa0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8c1aa4: LoadField: r1 = r0->field_7
    //     0x8c1aa4: ldur            w1, [x0, #7]
    // 0x8c1aa8: DecompressPointer r1
    //     0x8c1aa8: add             x1, x1, HEAP, lsl #32
    // 0x8c1aac: mov             x2, x0
    // 0x8c1ab0: r0 = _GrowableList.of()
    //     0x8c1ab0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8c1ab4: r1 = <StorageError, List<Surah>>
    //     0x8c1ab4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3da80] TypeArguments: <StorageError, List<Surah>>
    //     0x8c1ab8: ldr             x1, [x1, #0xa80]
    // 0x8c1abc: stur            x0, [fp, #-0x10]
    // 0x8c1ac0: r0 = Right()
    //     0x8c1ac0: bl              #0x8c1ae0  ; AllocateRightStub -> Right<X0, X1> (size=0x10)
    // 0x8c1ac4: ldur            x1, [fp, #-0x10]
    // 0x8c1ac8: StoreField: r0->field_b = r1
    //     0x8c1ac8: stur            w1, [x0, #0xb]
    // 0x8c1acc: r0 = ReturnAsyncNotFuture()
    //     0x8c1acc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c1ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c1ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c1ad4: b               #0x8c1a20
    // 0x8c1ad8: r9 = keystore
    //     0x8c1ad8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c1adc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c1adc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ find(/* No info */) {
    // ** addr: 0xb25f14, size: 0x94
    // 0xb25f14: EnterFrame
    //     0xb25f14: stp             fp, lr, [SP, #-0x10]!
    //     0xb25f18: mov             fp, SP
    // 0xb25f1c: AllocStack(0x20)
    //     0xb25f1c: sub             SP, SP, #0x20
    // 0xb25f20: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xb25f20: stur            x2, [fp, #-8]
    // 0xb25f24: CheckStackOverflow
    //     0xb25f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb25f28: cmp             SP, x16
    //     0xb25f2c: b.ls            #0xb25fa0
    // 0xb25f30: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb25f30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb25f34: ldr             x0, [x0, #0x2728]
    //     0xb25f38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb25f3c: cmp             w0, w16
    //     0xb25f40: b.ne            #0xb25f4c
    //     0xb25f44: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb25f48: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb25f4c: r16 = <Surah>
    //     0xb25f4c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1e0] TypeArguments: <Surah>
    //     0xb25f50: ldr             x16, [x16, #0x1e0]
    // 0xb25f54: stp             x0, x16, [SP, #8]
    // 0xb25f58: r16 = "v2_quran_surah"
    //     0xb25f58: add             x16, PP, #0xf, lsl #12  ; [pp+0xf208] "v2_quran_surah"
    //     0xb25f5c: ldr             x16, [x16, #0x208]
    // 0xb25f60: str             x16, [SP]
    // 0xb25f64: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb25f64: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb25f68: r0 = box()
    //     0xb25f68: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb25f6c: mov             x3, x0
    // 0xb25f70: ldur            x2, [fp, #-8]
    // 0xb25f74: r0 = BoxInt64Instr(r2)
    //     0xb25f74: sbfiz           x0, x2, #1, #0x1f
    //     0xb25f78: cmp             x2, x0, asr #1
    //     0xb25f7c: b.eq            #0xb25f88
    //     0xb25f80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb25f84: stur            x2, [x0, #7]
    // 0xb25f88: mov             x1, x3
    // 0xb25f8c: mov             x2, x0
    // 0xb25f90: r0 = get()
    //     0xb25f90: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0xb25f94: LeaveFrame
    //     0xb25f94: mov             SP, fp
    //     0xb25f98: ldp             fp, lr, [SP], #0x10
    // 0xb25f9c: ret
    //     0xb25f9c: ret             
    // 0xb25fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb25fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb25fa4: b               #0xb25f30
  }
}
