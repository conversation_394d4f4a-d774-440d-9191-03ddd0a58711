// lib: , url: package:nuonline/app/data/repositories/tafsir_repository.dart

// class id: 1050098, size: 0x8
class :: {
}

// class id: 1078, size: 0x10, field offset: 0x8
class TafsirRepository extends Object {

  _ init(/* No info */) async {
    // ** addr: 0x866cf8, size: 0x144
    // 0x866cf8: EnterFrame
    //     0x866cf8: stp             fp, lr, [SP, #-0x10]!
    //     0x866cfc: mov             fp, SP
    // 0x866d00: AllocStack(0x38)
    //     0x866d00: sub             SP, SP, #0x38
    // 0x866d04: SetupParameters(TafsirRepository this /* r1 => r1, fp-0x10 */)
    //     0x866d04: stur            NULL, [fp, #-8]
    //     0x866d08: stur            x1, [fp, #-0x10]
    // 0x866d0c: CheckStackOverflow
    //     0x866d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866d10: cmp             SP, x16
    //     0x866d14: b.ls            #0x866e34
    // 0x866d18: InitAsync() -> Future<void?>
    //     0x866d18: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x866d1c: bl              #0x661298  ; InitAsyncStub
    // 0x866d20: ldur            x0, [fp, #-0x10]
    // 0x866d24: LoadField: r1 = r0->field_7
    //     0x866d24: ldur            w1, [x0, #7]
    // 0x866d28: DecompressPointer r1
    //     0x866d28: add             x1, x1, HEAP, lsl #32
    // 0x866d2c: stur            x1, [fp, #-0x18]
    // 0x866d30: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x866d30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x866d34: ldr             x0, [x0, #0x2728]
    //     0x866d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x866d3c: cmp             w0, w16
    //     0x866d40: b.ne            #0x866d4c
    //     0x866d44: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x866d48: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x866d4c: r16 = <Tafsir>
    //     0x866d4c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0x866d50: ldr             x16, [x16, #0x238]
    // 0x866d54: stp             x0, x16, [SP, #8]
    // 0x866d58: r16 = "v2_tafsir"
    //     0x866d58: add             x16, PP, #0xf, lsl #12  ; [pp+0xf240] "v2_tafsir"
    //     0x866d5c: ldr             x16, [x16, #0x240]
    // 0x866d60: str             x16, [SP]
    // 0x866d64: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x866d64: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x866d68: r0 = box()
    //     0x866d68: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x866d6c: mov             x1, x0
    // 0x866d70: r0 = isEmpty()
    //     0x866d70: bl              #0x7bd59c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::isEmpty
    // 0x866d74: tbnz            w0, #4, #0x866e2c
    // 0x866d78: ldur            x0, [fp, #-0x10]
    // 0x866d7c: LoadField: r1 = r0->field_b
    //     0x866d7c: ldur            w1, [x0, #0xb]
    // 0x866d80: DecompressPointer r1
    //     0x866d80: add             x1, x1, HEAP, lsl #32
    // 0x866d84: r16 = <List>
    //     0x866d84: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x866d88: stp             x1, x16, [SP, #8]
    // 0x866d8c: r16 = "tafsir.json"
    //     0x866d8c: add             x16, PP, #0x10, lsl #12  ; [pp+0x102a0] "tafsir.json"
    //     0x866d90: ldr             x16, [x16, #0x2a0]
    // 0x866d94: str             x16, [SP]
    // 0x866d98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x866d98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x866d9c: r0 = read()
    //     0x866d9c: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x866da0: r1 = Function '<anonymous closure>':.
    //     0x866da0: add             x1, PP, #0x10, lsl #12  ; [pp+0x102a8] AnonymousClosure: (0x866ef0), in [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init (0x866cf8)
    //     0x866da4: ldr             x1, [x1, #0x2a8]
    // 0x866da8: r2 = Null
    //     0x866da8: mov             x2, NULL
    // 0x866dac: stur            x0, [fp, #-0x20]
    // 0x866db0: r0 = AllocateClosure()
    //     0x866db0: bl              #0xec1630  ; AllocateClosureStub
    // 0x866db4: r16 = <Iterable<Tafsir>>
    //     0x866db4: add             x16, PP, #0x10, lsl #12  ; [pp+0x102b0] TypeArguments: <Iterable<Tafsir>>
    //     0x866db8: ldr             x16, [x16, #0x2b0]
    // 0x866dbc: ldur            lr, [fp, #-0x20]
    // 0x866dc0: stp             lr, x16, [SP, #8]
    // 0x866dc4: str             x0, [SP]
    // 0x866dc8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x866dc8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x866dcc: r0 = then()
    //     0x866dcc: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x866dd0: mov             x1, x0
    // 0x866dd4: stur            x1, [fp, #-0x20]
    // 0x866dd8: r0 = Await()
    //     0x866dd8: bl              #0x661044  ; AwaitStub
    // 0x866ddc: ldur            x1, [fp, #-0x18]
    // 0x866de0: stur            x0, [fp, #-0x10]
    // 0x866de4: r0 = box()
    //     0x866de4: bl              #0x866e3c  ; [package:nuonline/services/storage_service/quran_storage.dart] TafsirStorage::box
    // 0x866de8: r1 = Function '<anonymous closure>':.
    //     0x866de8: add             x1, PP, #0x10, lsl #12  ; [pp+0x102b8] AnonymousClosure: (0x866ea4), in [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init (0x866cf8)
    //     0x866dec: ldr             x1, [x1, #0x2b8]
    // 0x866df0: r2 = Null
    //     0x866df0: mov             x2, NULL
    // 0x866df4: stur            x0, [fp, #-0x18]
    // 0x866df8: r0 = AllocateClosure()
    //     0x866df8: bl              #0xec1630  ; AllocateClosureStub
    // 0x866dfc: ldur            x2, [fp, #-0x10]
    // 0x866e00: mov             x3, x0
    // 0x866e04: r1 = <dynamic, Tafsir>
    //     0x866e04: add             x1, PP, #0x10, lsl #12  ; [pp+0x102c0] TypeArguments: <dynamic, Tafsir>
    //     0x866e08: ldr             x1, [x1, #0x2c0]
    // 0x866e0c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x866e0c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x866e10: r0 = LinkedHashMap.fromIterable()
    //     0x866e10: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x866e14: ldur            x1, [fp, #-0x18]
    // 0x866e18: mov             x2, x0
    // 0x866e1c: r0 = putAll()
    //     0x866e1c: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x866e20: mov             x1, x0
    // 0x866e24: stur            x1, [fp, #-0x10]
    // 0x866e28: r0 = Await()
    //     0x866e28: bl              #0x661044  ; AwaitStub
    // 0x866e2c: r0 = Null
    //     0x866e2c: mov             x0, NULL
    // 0x866e30: r0 = ReturnAsyncNotFuture()
    //     0x866e30: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x866e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866e38: b               #0x866d18
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x866ea4, size: 0x4c
    // 0x866ea4: EnterFrame
    //     0x866ea4: stp             fp, lr, [SP, #-0x10]!
    //     0x866ea8: mov             fp, SP
    // 0x866eac: AllocStack(0x8)
    //     0x866eac: sub             SP, SP, #8
    // 0x866eb0: CheckStackOverflow
    //     0x866eb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866eb4: cmp             SP, x16
    //     0x866eb8: b.ls            #0x866ee8
    // 0x866ebc: ldr             x16, [fp, #0x10]
    // 0x866ec0: str             x16, [SP]
    // 0x866ec4: r4 = 0
    //     0x866ec4: movz            x4, #0
    // 0x866ec8: ldr             x0, [SP]
    // 0x866ecc: r16 = UnlinkedCall_0x5f3c08
    //     0x866ecc: add             x16, PP, #0x10, lsl #12  ; [pp+0x102c8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x866ed0: add             x16, x16, #0x2c8
    // 0x866ed4: ldp             x5, lr, [x16]
    // 0x866ed8: blr             lr
    // 0x866edc: LeaveFrame
    //     0x866edc: mov             SP, fp
    //     0x866ee0: ldp             fp, lr, [SP], #0x10
    // 0x866ee4: ret
    //     0x866ee4: ret             
    // 0x866ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866ee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866eec: b               #0x866ebc
  }
  [closure] Iterable<Tafsir> <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x866ef0, size: 0x74
    // 0x866ef0: EnterFrame
    //     0x866ef0: stp             fp, lr, [SP, #-0x10]!
    //     0x866ef4: mov             fp, SP
    // 0x866ef8: AllocStack(0x18)
    //     0x866ef8: sub             SP, SP, #0x18
    // 0x866efc: CheckStackOverflow
    //     0x866efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866f00: cmp             SP, x16
    //     0x866f04: b.ls            #0x866f5c
    // 0x866f08: r1 = Function '<anonymous closure>':.
    //     0x866f08: add             x1, PP, #0x10, lsl #12  ; [pp+0x102d8] AnonymousClosure: (0x866f64), in [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init (0x866cf8)
    //     0x866f0c: ldr             x1, [x1, #0x2d8]
    // 0x866f10: r2 = Null
    //     0x866f10: mov             x2, NULL
    // 0x866f14: r0 = AllocateClosure()
    //     0x866f14: bl              #0xec1630  ; AllocateClosureStub
    // 0x866f18: mov             x1, x0
    // 0x866f1c: ldr             x0, [fp, #0x10]
    // 0x866f20: r2 = LoadClassIdInstr(r0)
    //     0x866f20: ldur            x2, [x0, #-1]
    //     0x866f24: ubfx            x2, x2, #0xc, #0x14
    // 0x866f28: r16 = <Tafsir>
    //     0x866f28: add             x16, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0x866f2c: ldr             x16, [x16, #0x238]
    // 0x866f30: stp             x0, x16, [SP, #8]
    // 0x866f34: str             x1, [SP]
    // 0x866f38: mov             x0, x2
    // 0x866f3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x866f3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x866f40: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x866f40: movz            x17, #0xf28c
    //     0x866f44: add             lr, x0, x17
    //     0x866f48: ldr             lr, [x21, lr, lsl #3]
    //     0x866f4c: blr             lr
    // 0x866f50: LeaveFrame
    //     0x866f50: mov             SP, fp
    //     0x866f54: ldp             fp, lr, [SP], #0x10
    // 0x866f58: ret
    //     0x866f58: ret             
    // 0x866f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866f5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866f60: b               #0x866f08
  }
  [closure] Tafsir <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x866f64, size: 0x50
    // 0x866f64: EnterFrame
    //     0x866f64: stp             fp, lr, [SP, #-0x10]!
    //     0x866f68: mov             fp, SP
    // 0x866f6c: CheckStackOverflow
    //     0x866f6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866f70: cmp             SP, x16
    //     0x866f74: b.ls            #0x866fac
    // 0x866f78: ldr             x0, [fp, #0x10]
    // 0x866f7c: r2 = Null
    //     0x866f7c: mov             x2, NULL
    // 0x866f80: r1 = Null
    //     0x866f80: mov             x1, NULL
    // 0x866f84: r8 = Map<String, dynamic>
    //     0x866f84: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x866f88: r3 = Null
    //     0x866f88: add             x3, PP, #0x10, lsl #12  ; [pp+0x102e0] Null
    //     0x866f8c: ldr             x3, [x3, #0x2e0]
    // 0x866f90: r0 = Map<String, dynamic>()
    //     0x866f90: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x866f94: ldr             x2, [fp, #0x10]
    // 0x866f98: r1 = Null
    //     0x866f98: mov             x1, NULL
    // 0x866f9c: r0 = Tafsir.fromMap()
    //     0x866f9c: bl              #0x866fb4  ; [package:nuonline/app/data/models/tafsir.dart] Tafsir::Tafsir.fromMap
    // 0x866fa0: LeaveFrame
    //     0x866fa0: mov             SP, fp
    //     0x866fa4: ldp             fp, lr, [SP], #0x10
    // 0x866fa8: ret
    //     0x866fa8: ret             
    // 0x866fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866fac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866fb0: b               #0x866f78
  }
  _ findByVerseId(/* No info */) async {
    // ** addr: 0x8ea7b8, size: 0x110
    // 0x8ea7b8: EnterFrame
    //     0x8ea7b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ea7bc: mov             fp, SP
    // 0x8ea7c0: AllocStack(0x38)
    //     0x8ea7c0: sub             SP, SP, #0x38
    // 0x8ea7c4: SetupParameters(TafsirRepository this /* r1 => r3, fp-0x18 */)
    //     0x8ea7c4: stur            NULL, [fp, #-8]
    //     0x8ea7c8: mov             x3, x1
    //     0x8ea7cc: stur            x1, [fp, #-0x18]
    // 0x8ea7d0: CheckStackOverflow
    //     0x8ea7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ea7d4: cmp             SP, x16
    //     0x8ea7d8: b.ls            #0x8ea8b8
    // 0x8ea7dc: r0 = BoxInt64Instr(r2)
    //     0x8ea7dc: sbfiz           x0, x2, #1, #0x1f
    //     0x8ea7e0: cmp             x2, x0, asr #1
    //     0x8ea7e4: b.eq            #0x8ea7f0
    //     0x8ea7e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ea7ec: stur            x2, [x0, #7]
    // 0x8ea7f0: stur            x0, [fp, #-0x10]
    // 0x8ea7f4: r1 = 1
    //     0x8ea7f4: movz            x1, #0x1
    // 0x8ea7f8: r0 = AllocateContext()
    //     0x8ea7f8: bl              #0xec126c  ; AllocateContextStub
    // 0x8ea7fc: mov             x1, x0
    // 0x8ea800: ldur            x0, [fp, #-0x10]
    // 0x8ea804: stur            x1, [fp, #-0x20]
    // 0x8ea808: StoreField: r1->field_f = r0
    //     0x8ea808: stur            w0, [x1, #0xf]
    // 0x8ea80c: InitAsync() -> Future<Tafsir>
    //     0x8ea80c: add             x0, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0x8ea810: ldr             x0, [x0, #0x238]
    //     0x8ea814: bl              #0x661298  ; InitAsyncStub
    // 0x8ea818: ldur            x1, [fp, #-0x18]
    // 0x8ea81c: r0 = init()
    //     0x8ea81c: bl              #0x866cf8  ; [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init
    // 0x8ea820: mov             x1, x0
    // 0x8ea824: stur            x1, [fp, #-0x10]
    // 0x8ea828: r0 = Await()
    //     0x8ea828: bl              #0x661044  ; AwaitStub
    // 0x8ea82c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8ea82c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ea830: ldr             x0, [x0, #0x2728]
    //     0x8ea834: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ea838: cmp             w0, w16
    //     0x8ea83c: b.ne            #0x8ea848
    //     0x8ea840: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8ea844: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ea848: r16 = <Tafsir>
    //     0x8ea848: add             x16, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0x8ea84c: ldr             x16, [x16, #0x238]
    // 0x8ea850: stp             x0, x16, [SP, #8]
    // 0x8ea854: r16 = "v2_tafsir"
    //     0x8ea854: add             x16, PP, #0xf, lsl #12  ; [pp+0xf240] "v2_tafsir"
    //     0x8ea858: ldr             x16, [x16, #0x240]
    // 0x8ea85c: str             x16, [SP]
    // 0x8ea860: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ea860: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ea864: r0 = box()
    //     0x8ea864: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ea868: mov             x1, x0
    // 0x8ea86c: stur            x0, [fp, #-0x10]
    // 0x8ea870: r0 = checkOpen()
    //     0x8ea870: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8ea874: ldur            x0, [fp, #-0x10]
    // 0x8ea878: LoadField: r1 = r0->field_1b
    //     0x8ea878: ldur            w1, [x0, #0x1b]
    // 0x8ea87c: DecompressPointer r1
    //     0x8ea87c: add             x1, x1, HEAP, lsl #32
    // 0x8ea880: r16 = Sentinel
    //     0x8ea880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ea884: cmp             w1, w16
    // 0x8ea888: b.eq            #0x8ea8c0
    // 0x8ea88c: r0 = getValues()
    //     0x8ea88c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8ea890: ldur            x2, [fp, #-0x20]
    // 0x8ea894: r1 = Function '<anonymous closure>':.
    //     0x8ea894: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b6d0] AnonymousClosure: (0x8ea8c8), in [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::findByVerseId (0x8ea7b8)
    //     0x8ea898: ldr             x1, [x1, #0x6d0]
    // 0x8ea89c: stur            x0, [fp, #-0x10]
    // 0x8ea8a0: r0 = AllocateClosure()
    //     0x8ea8a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ea8a4: ldur            x1, [fp, #-0x10]
    // 0x8ea8a8: mov             x2, x0
    // 0x8ea8ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8ea8ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8ea8b0: r0 = firstWhere()
    //     0x8ea8b0: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0x8ea8b4: r0 = ReturnAsync()
    //     0x8ea8b4: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8ea8b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ea8b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ea8bc: b               #0x8ea7dc
    // 0x8ea8c0: r9 = keystore
    //     0x8ea8c0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8ea8c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ea8c4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Tafsir) {
    // ** addr: 0x8ea8c8, size: 0x3c
    // 0x8ea8c8: ldr             x1, [SP, #8]
    // 0x8ea8cc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8ea8cc: ldur            w2, [x1, #0x17]
    // 0x8ea8d0: DecompressPointer r2
    //     0x8ea8d0: add             x2, x2, HEAP, lsl #32
    // 0x8ea8d4: ldr             x1, [SP]
    // 0x8ea8d8: LoadField: r3 = r1->field_13
    //     0x8ea8d8: ldur            x3, [x1, #0x13]
    // 0x8ea8dc: LoadField: r1 = r2->field_f
    //     0x8ea8dc: ldur            w1, [x2, #0xf]
    // 0x8ea8e0: DecompressPointer r1
    //     0x8ea8e0: add             x1, x1, HEAP, lsl #32
    // 0x8ea8e4: r2 = LoadInt32Instr(r1)
    //     0x8ea8e4: sbfx            x2, x1, #1, #0x1f
    //     0x8ea8e8: tbz             w1, #0, #0x8ea8f0
    //     0x8ea8ec: ldur            x2, [x1, #7]
    // 0x8ea8f0: cmp             x3, x2
    // 0x8ea8f4: r16 = true
    //     0x8ea8f4: add             x16, NULL, #0x20  ; true
    // 0x8ea8f8: r17 = false
    //     0x8ea8f8: add             x17, NULL, #0x30  ; false
    // 0x8ea8fc: csel            x0, x16, x17, eq
    // 0x8ea900: ret
    //     0x8ea900: ret             
  }
  _ findTranslationQuery(/* No info */) async {
    // ** addr: 0xb53048, size: 0x770
    // 0xb53048: EnterFrame
    //     0xb53048: stp             fp, lr, [SP, #-0x10]!
    //     0xb5304c: mov             fp, SP
    // 0xb53050: AllocStack(0xc8)
    //     0xb53050: sub             SP, SP, #0xc8
    // 0xb53054: SetupParameters(TafsirRepository this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xb53054: stur            NULL, [fp, #-8]
    //     0xb53058: stur            x1, [fp, #-0x10]
    //     0xb5305c: mov             x16, x2
    //     0xb53060: mov             x2, x1
    //     0xb53064: mov             x1, x16
    //     0xb53068: stur            x1, [fp, #-0x18]
    //     0xb5306c: stur            x3, [fp, #-0x20]
    // 0xb53070: CheckStackOverflow
    //     0xb53070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53074: cmp             SP, x16
    //     0xb53078: b.ls            #0xb53798
    // 0xb5307c: InitAsync() -> Future<List<List<Tafsir>>>
    //     0xb5307c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29f40] TypeArguments: <List<List<Tafsir>>>
    //     0xb53080: ldr             x0, [x0, #0xf40]
    //     0xb53084: bl              #0x661298  ; InitAsyncStub
    // 0xb53088: ldur            x1, [fp, #-0x10]
    // 0xb5308c: r0 = init()
    //     0xb5308c: bl              #0x866cf8  ; [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::init
    // 0xb53090: r1 = <Tafsir>
    //     0xb53090: add             x1, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0xb53094: ldr             x1, [x1, #0x238]
    // 0xb53098: r2 = 0
    //     0xb53098: movz            x2, #0
    // 0xb5309c: r0 = _GrowableList()
    //     0xb5309c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb530a0: r1 = <Tafsir>
    //     0xb530a0: add             x1, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0xb530a4: ldr             x1, [x1, #0x238]
    // 0xb530a8: r2 = 0
    //     0xb530a8: movz            x2, #0
    // 0xb530ac: stur            x0, [fp, #-0x10]
    // 0xb530b0: r0 = _GrowableList()
    //     0xb530b0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb530b4: r1 = <Tafsir>
    //     0xb530b4: add             x1, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0xb530b8: ldr             x1, [x1, #0x238]
    // 0xb530bc: r2 = 0
    //     0xb530bc: movz            x2, #0
    // 0xb530c0: stur            x0, [fp, #-0x28]
    // 0xb530c4: r0 = _GrowableList()
    //     0xb530c4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb530c8: mov             x2, x0
    // 0xb530cc: ldur            x0, [fp, #-0x20]
    // 0xb530d0: stur            x2, [fp, #-0x30]
    // 0xb530d4: tbnz            w0, #4, #0xb53124
    // 0xb530d8: ldur            x1, [fp, #-0x18]
    // 0xb530dc: r0 = trim()
    //     0xb530dc: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xb530e0: r1 = Null
    //     0xb530e0: mov             x1, NULL
    // 0xb530e4: r2 = 2
    //     0xb530e4: movz            x2, #0x2
    // 0xb530e8: stur            x0, [fp, #-0x20]
    // 0xb530ec: r0 = AllocateArray()
    //     0xb530ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb530f0: mov             x2, x0
    // 0xb530f4: ldur            x0, [fp, #-0x20]
    // 0xb530f8: stur            x2, [fp, #-0x38]
    // 0xb530fc: StoreField: r2->field_f = r0
    //     0xb530fc: stur            w0, [x2, #0xf]
    // 0xb53100: r1 = <String>
    //     0xb53100: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb53104: r0 = AllocateGrowableArray()
    //     0xb53104: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb53108: mov             x1, x0
    // 0xb5310c: ldur            x0, [fp, #-0x38]
    // 0xb53110: StoreField: r1->field_f = r0
    //     0xb53110: stur            w0, [x1, #0xf]
    // 0xb53114: r0 = 2
    //     0xb53114: movz            x0, #0x2
    // 0xb53118: StoreField: r1->field_b = r0
    //     0xb53118: stur            w0, [x1, #0xb]
    // 0xb5311c: mov             x0, x1
    // 0xb53120: b               #0xb53150
    // 0xb53124: ldur            x1, [fp, #-0x18]
    // 0xb53128: r0 = trim()
    //     0xb53128: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xb5312c: r1 = LoadClassIdInstr(r0)
    //     0xb5312c: ldur            x1, [x0, #-1]
    //     0xb53130: ubfx            x1, x1, #0xc, #0x14
    // 0xb53134: mov             x16, x0
    // 0xb53138: mov             x0, x1
    // 0xb5313c: mov             x1, x16
    // 0xb53140: r2 = " "
    //     0xb53140: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb53144: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb53144: sub             lr, x0, #1, lsl #12
    //     0xb53148: ldr             lr, [x21, lr, lsl #3]
    //     0xb5314c: blr             lr
    // 0xb53150: stur            x0, [fp, #-0x18]
    // 0xb53154: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb53154: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb53158: ldr             x0, [x0, #0x2728]
    //     0xb5315c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb53160: cmp             w0, w16
    //     0xb53164: b.ne            #0xb53170
    //     0xb53168: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb5316c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb53170: r16 = <Tafsir>
    //     0xb53170: add             x16, PP, #0xf, lsl #12  ; [pp+0xf238] TypeArguments: <Tafsir>
    //     0xb53174: ldr             x16, [x16, #0x238]
    // 0xb53178: stp             x0, x16, [SP, #8]
    // 0xb5317c: r16 = "v2_tafsir"
    //     0xb5317c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf240] "v2_tafsir"
    //     0xb53180: ldr             x16, [x16, #0x240]
    // 0xb53184: str             x16, [SP]
    // 0xb53188: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb53188: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb5318c: r0 = box()
    //     0xb5318c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb53190: mov             x1, x0
    // 0xb53194: stur            x0, [fp, #-0x20]
    // 0xb53198: r0 = checkOpen()
    //     0xb53198: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb5319c: ldur            x0, [fp, #-0x20]
    // 0xb531a0: LoadField: r1 = r0->field_1b
    //     0xb531a0: ldur            w1, [x0, #0x1b]
    // 0xb531a4: DecompressPointer r1
    //     0xb531a4: add             x1, x1, HEAP, lsl #32
    // 0xb531a8: r16 = Sentinel
    //     0xb531a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb531ac: cmp             w1, w16
    // 0xb531b0: b.eq            #0xb537a0
    // 0xb531b4: r0 = getValues()
    //     0xb531b4: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb531b8: mov             x1, x0
    // 0xb531bc: r0 = iterator()
    //     0xb531bc: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0xb531c0: mov             x2, x0
    // 0xb531c4: stur            x2, [fp, #-0x48]
    // 0xb531c8: LoadField: r3 = r2->field_f
    //     0xb531c8: ldur            w3, [x2, #0xf]
    // 0xb531cc: DecompressPointer r3
    //     0xb531cc: add             x3, x3, HEAP, lsl #32
    // 0xb531d0: stur            x3, [fp, #-0x40]
    // 0xb531d4: LoadField: r4 = r2->field_13
    //     0xb531d4: ldur            w4, [x2, #0x13]
    // 0xb531d8: DecompressPointer r4
    //     0xb531d8: add             x4, x4, HEAP, lsl #32
    // 0xb531dc: stur            x4, [fp, #-0x38]
    // 0xb531e0: LoadField: r5 = r2->field_7
    //     0xb531e0: ldur            w5, [x2, #7]
    // 0xb531e4: DecompressPointer r5
    //     0xb531e4: add             x5, x5, HEAP, lsl #32
    // 0xb531e8: stur            x5, [fp, #-0x20]
    // 0xb531ec: ldur            x7, [fp, #-0x30]
    // 0xb531f0: ldur            x9, [fp, #-0x10]
    // 0xb531f4: ldur            x8, [fp, #-0x28]
    // 0xb531f8: ldur            x6, [fp, #-0x18]
    // 0xb531fc: CheckStackOverflow
    //     0xb531fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53200: cmp             SP, x16
    //     0xb53204: b.ls            #0xb537a8
    // 0xb53208: r0 = LoadClassIdInstr(r3)
    //     0xb53208: ldur            x0, [x3, #-1]
    //     0xb5320c: ubfx            x0, x0, #0xc, #0x14
    // 0xb53210: mov             x1, x3
    // 0xb53214: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xb53214: movz            x17, #0x292d
    //     0xb53218: movk            x17, #0x1, lsl #16
    //     0xb5321c: add             lr, x0, x17
    //     0xb53220: ldr             lr, [x21, lr, lsl #3]
    //     0xb53224: blr             lr
    // 0xb53228: tbnz            w0, #4, #0xb53754
    // 0xb5322c: ldur            x2, [fp, #-0x48]
    // 0xb53230: ldur            x3, [fp, #-0x40]
    // 0xb53234: r0 = LoadClassIdInstr(r3)
    //     0xb53234: ldur            x0, [x3, #-1]
    //     0xb53238: ubfx            x0, x0, #0xc, #0x14
    // 0xb5323c: mov             x1, x3
    // 0xb53240: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xb53240: movz            x17, #0x384d
    //     0xb53244: movk            x17, #0x1, lsl #16
    //     0xb53248: add             lr, x0, x17
    //     0xb5324c: ldr             lr, [x21, lr, lsl #3]
    //     0xb53250: blr             lr
    // 0xb53254: ldur            x16, [fp, #-0x38]
    // 0xb53258: stp             x0, x16, [SP]
    // 0xb5325c: ldur            x0, [fp, #-0x38]
    // 0xb53260: ClosureCall
    //     0xb53260: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb53264: ldur            x2, [x0, #0x1f]
    //     0xb53268: blr             x2
    // 0xb5326c: mov             x4, x0
    // 0xb53270: ldur            x3, [fp, #-0x48]
    // 0xb53274: stur            x4, [fp, #-0x50]
    // 0xb53278: StoreField: r3->field_b = r0
    //     0xb53278: stur            w0, [x3, #0xb]
    //     0xb5327c: tbz             w0, #0, #0xb53298
    //     0xb53280: ldurb           w16, [x3, #-1]
    //     0xb53284: ldurb           w17, [x0, #-1]
    //     0xb53288: and             x16, x17, x16, lsr #2
    //     0xb5328c: tst             x16, HEAP, lsr #32
    //     0xb53290: b.eq            #0xb53298
    //     0xb53294: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb53298: cmp             w4, NULL
    // 0xb5329c: b.ne            #0xb532d0
    // 0xb532a0: mov             x0, x4
    // 0xb532a4: ldur            x2, [fp, #-0x20]
    // 0xb532a8: r1 = Null
    //     0xb532a8: mov             x1, NULL
    // 0xb532ac: cmp             w2, NULL
    // 0xb532b0: b.eq            #0xb532d0
    // 0xb532b4: LoadField: r4 = r2->field_1b
    //     0xb532b4: ldur            w4, [x2, #0x1b]
    // 0xb532b8: DecompressPointer r4
    //     0xb532b8: add             x4, x4, HEAP, lsl #32
    // 0xb532bc: r8 = X1
    //     0xb532bc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xb532c0: LoadField: r9 = r4->field_7
    //     0xb532c0: ldur            x9, [x4, #7]
    // 0xb532c4: r3 = Null
    //     0xb532c4: add             x3, PP, #0x29, lsl #12  ; [pp+0x29f48] Null
    //     0xb532c8: ldr             x3, [x3, #0xf48]
    // 0xb532cc: blr             x9
    // 0xb532d0: ldur            x2, [fp, #-0x18]
    // 0xb532d4: ldur            x1, [fp, #-0x50]
    // 0xb532d8: LoadField: r0 = r2->field_b
    //     0xb532d8: ldur            w0, [x2, #0xb]
    // 0xb532dc: r3 = LoadInt32Instr(r0)
    //     0xb532dc: sbfx            x3, x0, #1, #0x1f
    // 0xb532e0: stur            x3, [fp, #-0x98]
    // 0xb532e4: LoadField: r4 = r1->field_43
    //     0xb532e4: ldur            w4, [x1, #0x43]
    // 0xb532e8: DecompressPointer r4
    //     0xb532e8: add             x4, x4, HEAP, lsl #32
    // 0xb532ec: stur            x4, [fp, #-0x90]
    // 0xb532f0: LoadField: r5 = r1->field_3b
    //     0xb532f0: ldur            w5, [x1, #0x3b]
    // 0xb532f4: DecompressPointer r5
    //     0xb532f4: add             x5, x5, HEAP, lsl #32
    // 0xb532f8: stur            x5, [fp, #-0x88]
    // 0xb532fc: LoadField: r6 = r1->field_3f
    //     0xb532fc: ldur            w6, [x1, #0x3f]
    // 0xb53300: DecompressPointer r6
    //     0xb53300: add             x6, x6, HEAP, lsl #32
    // 0xb53304: stur            x6, [fp, #-0x80]
    // 0xb53308: r9 = true
    //     0xb53308: add             x9, NULL, #0x20  ; true
    // 0xb5330c: r8 = true
    //     0xb5330c: add             x8, NULL, #0x20  ; true
    // 0xb53310: r7 = true
    //     0xb53310: add             x7, NULL, #0x20  ; true
    // 0xb53314: r0 = 0
    //     0xb53314: movz            x0, #0
    // 0xb53318: stur            x9, [fp, #-0x68]
    // 0xb5331c: stur            x8, [fp, #-0x70]
    // 0xb53320: stur            x7, [fp, #-0x78]
    // 0xb53324: CheckStackOverflow
    //     0xb53324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53328: cmp             SP, x16
    //     0xb5332c: b.ls            #0xb537b0
    // 0xb53330: LoadField: r10 = r2->field_b
    //     0xb53330: ldur            w10, [x2, #0xb]
    // 0xb53334: r11 = LoadInt32Instr(r10)
    //     0xb53334: sbfx            x11, x10, #1, #0x1f
    // 0xb53338: cmp             x3, x11
    // 0xb5333c: b.ne            #0xb53778
    // 0xb53340: cmp             x0, x11
    // 0xb53344: b.ge            #0xb53508
    // 0xb53348: LoadField: r10 = r2->field_f
    //     0xb53348: ldur            w10, [x2, #0xf]
    // 0xb5334c: DecompressPointer r10
    //     0xb5334c: add             x10, x10, HEAP, lsl #32
    // 0xb53350: ArrayLoad: r11 = r10[r0]  ; Unknown_4
    //     0xb53350: add             x16, x10, x0, lsl #2
    //     0xb53354: ldur            w11, [x16, #0xf]
    // 0xb53358: DecompressPointer r11
    //     0xb53358: add             x11, x11, HEAP, lsl #32
    // 0xb5335c: stur            x11, [fp, #-0x60]
    // 0xb53360: add             x10, x0, #1
    // 0xb53364: stur            x10, [fp, #-0x58]
    // 0xb53368: r0 = LoadClassIdInstr(r4)
    //     0xb53368: ldur            x0, [x4, #-1]
    //     0xb5336c: ubfx            x0, x0, #0xc, #0x14
    // 0xb53370: str             x4, [SP]
    // 0xb53374: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb53374: sub             lr, x0, #0xffe
    //     0xb53378: ldr             lr, [x21, lr, lsl #3]
    //     0xb5337c: blr             lr
    // 0xb53380: mov             x2, x0
    // 0xb53384: ldur            x1, [fp, #-0x60]
    // 0xb53388: stur            x2, [fp, #-0xa0]
    // 0xb5338c: r0 = LoadClassIdInstr(r1)
    //     0xb5338c: ldur            x0, [x1, #-1]
    //     0xb53390: ubfx            x0, x0, #0xc, #0x14
    // 0xb53394: str             x1, [SP]
    // 0xb53398: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb53398: sub             lr, x0, #0xffe
    //     0xb5339c: ldr             lr, [x21, lr, lsl #3]
    //     0xb533a0: blr             lr
    // 0xb533a4: ldur            x1, [fp, #-0xa0]
    // 0xb533a8: r2 = LoadClassIdInstr(r1)
    //     0xb533a8: ldur            x2, [x1, #-1]
    //     0xb533ac: ubfx            x2, x2, #0xc, #0x14
    // 0xb533b0: mov             x16, x0
    // 0xb533b4: mov             x0, x2
    // 0xb533b8: mov             x2, x16
    // 0xb533bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb533bc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb533c0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb533c0: sub             lr, x0, #0xffc
    //     0xb533c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb533c8: blr             lr
    // 0xb533cc: tbz             w0, #4, #0xb533d8
    // 0xb533d0: r9 = false
    //     0xb533d0: add             x9, NULL, #0x30  ; false
    // 0xb533d4: b               #0xb533dc
    // 0xb533d8: ldur            x9, [fp, #-0x68]
    // 0xb533dc: ldur            x2, [fp, #-0x88]
    // 0xb533e0: ldur            x1, [fp, #-0x60]
    // 0xb533e4: stur            x9, [fp, #-0xa0]
    // 0xb533e8: r0 = LoadClassIdInstr(r2)
    //     0xb533e8: ldur            x0, [x2, #-1]
    //     0xb533ec: ubfx            x0, x0, #0xc, #0x14
    // 0xb533f0: str             x2, [SP]
    // 0xb533f4: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb533f4: sub             lr, x0, #0xffe
    //     0xb533f8: ldr             lr, [x21, lr, lsl #3]
    //     0xb533fc: blr             lr
    // 0xb53400: mov             x2, x0
    // 0xb53404: ldur            x1, [fp, #-0x60]
    // 0xb53408: stur            x2, [fp, #-0xa8]
    // 0xb5340c: r0 = LoadClassIdInstr(r1)
    //     0xb5340c: ldur            x0, [x1, #-1]
    //     0xb53410: ubfx            x0, x0, #0xc, #0x14
    // 0xb53414: str             x1, [SP]
    // 0xb53418: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb53418: sub             lr, x0, #0xffe
    //     0xb5341c: ldr             lr, [x21, lr, lsl #3]
    //     0xb53420: blr             lr
    // 0xb53424: ldur            x1, [fp, #-0xa8]
    // 0xb53428: r2 = LoadClassIdInstr(r1)
    //     0xb53428: ldur            x2, [x1, #-1]
    //     0xb5342c: ubfx            x2, x2, #0xc, #0x14
    // 0xb53430: mov             x16, x0
    // 0xb53434: mov             x0, x2
    // 0xb53438: mov             x2, x16
    // 0xb5343c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb5343c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb53440: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb53440: sub             lr, x0, #0xffc
    //     0xb53444: ldr             lr, [x21, lr, lsl #3]
    //     0xb53448: blr             lr
    // 0xb5344c: tbz             w0, #4, #0xb53458
    // 0xb53450: r8 = false
    //     0xb53450: add             x8, NULL, #0x30  ; false
    // 0xb53454: b               #0xb5345c
    // 0xb53458: ldur            x8, [fp, #-0x70]
    // 0xb5345c: ldur            x2, [fp, #-0x80]
    // 0xb53460: ldur            x1, [fp, #-0x60]
    // 0xb53464: stur            x8, [fp, #-0xa8]
    // 0xb53468: r0 = LoadClassIdInstr(r2)
    //     0xb53468: ldur            x0, [x2, #-1]
    //     0xb5346c: ubfx            x0, x0, #0xc, #0x14
    // 0xb53470: str             x2, [SP]
    // 0xb53474: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb53474: sub             lr, x0, #0xffe
    //     0xb53478: ldr             lr, [x21, lr, lsl #3]
    //     0xb5347c: blr             lr
    // 0xb53480: mov             x1, x0
    // 0xb53484: ldur            x0, [fp, #-0x60]
    // 0xb53488: stur            x1, [fp, #-0xb0]
    // 0xb5348c: r2 = LoadClassIdInstr(r0)
    //     0xb5348c: ldur            x2, [x0, #-1]
    //     0xb53490: ubfx            x2, x2, #0xc, #0x14
    // 0xb53494: str             x0, [SP]
    // 0xb53498: mov             x0, x2
    // 0xb5349c: r0 = GDT[cid_x0 + -0xffe]()
    //     0xb5349c: sub             lr, x0, #0xffe
    //     0xb534a0: ldr             lr, [x21, lr, lsl #3]
    //     0xb534a4: blr             lr
    // 0xb534a8: ldur            x1, [fp, #-0xb0]
    // 0xb534ac: r2 = LoadClassIdInstr(r1)
    //     0xb534ac: ldur            x2, [x1, #-1]
    //     0xb534b0: ubfx            x2, x2, #0xc, #0x14
    // 0xb534b4: mov             x16, x0
    // 0xb534b8: mov             x0, x2
    // 0xb534bc: mov             x2, x16
    // 0xb534c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb534c0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb534c4: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb534c4: sub             lr, x0, #0xffc
    //     0xb534c8: ldr             lr, [x21, lr, lsl #3]
    //     0xb534cc: blr             lr
    // 0xb534d0: tbz             w0, #4, #0xb534dc
    // 0xb534d4: r7 = false
    //     0xb534d4: add             x7, NULL, #0x30  ; false
    // 0xb534d8: b               #0xb534e0
    // 0xb534dc: ldur            x7, [fp, #-0x78]
    // 0xb534e0: ldur            x9, [fp, #-0xa0]
    // 0xb534e4: ldur            x8, [fp, #-0xa8]
    // 0xb534e8: ldur            x0, [fp, #-0x58]
    // 0xb534ec: ldur            x2, [fp, #-0x18]
    // 0xb534f0: ldur            x4, [fp, #-0x90]
    // 0xb534f4: ldur            x5, [fp, #-0x88]
    // 0xb534f8: ldur            x6, [fp, #-0x80]
    // 0xb534fc: ldur            x1, [fp, #-0x50]
    // 0xb53500: ldur            x3, [fp, #-0x98]
    // 0xb53504: b               #0xb53318
    // 0xb53508: mov             x0, x9
    // 0xb5350c: tbnz            w0, #4, #0xb535c0
    // 0xb53510: ldur            x3, [fp, #-0x10]
    // 0xb53514: ldur            x0, [fp, #-0x50]
    // 0xb53518: r2 = Null
    //     0xb53518: mov             x2, NULL
    // 0xb5351c: r1 = Null
    //     0xb5351c: mov             x1, NULL
    // 0xb53520: r4 = LoadClassIdInstr(r0)
    //     0xb53520: ldur            x4, [x0, #-1]
    //     0xb53524: ubfx            x4, x4, #0xc, #0x14
    // 0xb53528: cmp             x4, #0x63f
    // 0xb5352c: b.eq            #0xb53544
    // 0xb53530: r8 = Tafsir
    //     0xb53530: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b120] Type: Tafsir
    //     0xb53534: ldr             x8, [x8, #0x120]
    // 0xb53538: r3 = Null
    //     0xb53538: add             x3, PP, #0x29, lsl #12  ; [pp+0x29f58] Null
    //     0xb5353c: ldr             x3, [x3, #0xf58]
    // 0xb53540: r0 = Tafsir()
    //     0xb53540: bl              #0x83ef88  ; IsType_Tafsir_Stub
    // 0xb53544: ldur            x0, [fp, #-0x10]
    // 0xb53548: LoadField: r1 = r0->field_b
    //     0xb53548: ldur            w1, [x0, #0xb]
    // 0xb5354c: LoadField: r2 = r0->field_f
    //     0xb5354c: ldur            w2, [x0, #0xf]
    // 0xb53550: DecompressPointer r2
    //     0xb53550: add             x2, x2, HEAP, lsl #32
    // 0xb53554: LoadField: r3 = r2->field_b
    //     0xb53554: ldur            w3, [x2, #0xb]
    // 0xb53558: r2 = LoadInt32Instr(r1)
    //     0xb53558: sbfx            x2, x1, #1, #0x1f
    // 0xb5355c: stur            x2, [fp, #-0x58]
    // 0xb53560: r1 = LoadInt32Instr(r3)
    //     0xb53560: sbfx            x1, x3, #1, #0x1f
    // 0xb53564: cmp             x2, x1
    // 0xb53568: b.ne            #0xb53574
    // 0xb5356c: mov             x1, x0
    // 0xb53570: r0 = _growToNextCapacity()
    //     0xb53570: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb53574: ldur            x3, [fp, #-0x10]
    // 0xb53578: ldur            x2, [fp, #-0x58]
    // 0xb5357c: add             x0, x2, #1
    // 0xb53580: lsl             x1, x0, #1
    // 0xb53584: StoreField: r3->field_b = r1
    //     0xb53584: stur            w1, [x3, #0xb]
    // 0xb53588: LoadField: r1 = r3->field_f
    //     0xb53588: ldur            w1, [x3, #0xf]
    // 0xb5358c: DecompressPointer r1
    //     0xb5358c: add             x1, x1, HEAP, lsl #32
    // 0xb53590: ldur            x0, [fp, #-0x50]
    // 0xb53594: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb53594: add             x25, x1, x2, lsl #2
    //     0xb53598: add             x25, x25, #0xf
    //     0xb5359c: str             w0, [x25]
    //     0xb535a0: tbz             w0, #0, #0xb535bc
    //     0xb535a4: ldurb           w16, [x1, #-1]
    //     0xb535a8: ldurb           w17, [x0, #-1]
    //     0xb535ac: and             x16, x17, x16, lsr #2
    //     0xb535b0: tst             x16, HEAP, lsr #32
    //     0xb535b4: b.eq            #0xb535bc
    //     0xb535b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb535bc: b               #0xb535c4
    // 0xb535c0: ldur            x3, [fp, #-0x10]
    // 0xb535c4: ldur            x0, [fp, #-0x70]
    // 0xb535c8: tbnz            w0, #4, #0xb5367c
    // 0xb535cc: ldur            x4, [fp, #-0x28]
    // 0xb535d0: ldur            x0, [fp, #-0x50]
    // 0xb535d4: r2 = Null
    //     0xb535d4: mov             x2, NULL
    // 0xb535d8: r1 = Null
    //     0xb535d8: mov             x1, NULL
    // 0xb535dc: r4 = LoadClassIdInstr(r0)
    //     0xb535dc: ldur            x4, [x0, #-1]
    //     0xb535e0: ubfx            x4, x4, #0xc, #0x14
    // 0xb535e4: cmp             x4, #0x63f
    // 0xb535e8: b.eq            #0xb53600
    // 0xb535ec: r8 = Tafsir
    //     0xb535ec: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b120] Type: Tafsir
    //     0xb535f0: ldr             x8, [x8, #0x120]
    // 0xb535f4: r3 = Null
    //     0xb535f4: add             x3, PP, #0x29, lsl #12  ; [pp+0x29f68] Null
    //     0xb535f8: ldr             x3, [x3, #0xf68]
    // 0xb535fc: r0 = Tafsir()
    //     0xb535fc: bl              #0x83ef88  ; IsType_Tafsir_Stub
    // 0xb53600: ldur            x0, [fp, #-0x28]
    // 0xb53604: LoadField: r1 = r0->field_b
    //     0xb53604: ldur            w1, [x0, #0xb]
    // 0xb53608: LoadField: r2 = r0->field_f
    //     0xb53608: ldur            w2, [x0, #0xf]
    // 0xb5360c: DecompressPointer r2
    //     0xb5360c: add             x2, x2, HEAP, lsl #32
    // 0xb53610: LoadField: r3 = r2->field_b
    //     0xb53610: ldur            w3, [x2, #0xb]
    // 0xb53614: r2 = LoadInt32Instr(r1)
    //     0xb53614: sbfx            x2, x1, #1, #0x1f
    // 0xb53618: stur            x2, [fp, #-0x58]
    // 0xb5361c: r1 = LoadInt32Instr(r3)
    //     0xb5361c: sbfx            x1, x3, #1, #0x1f
    // 0xb53620: cmp             x2, x1
    // 0xb53624: b.ne            #0xb53630
    // 0xb53628: mov             x1, x0
    // 0xb5362c: r0 = _growToNextCapacity()
    //     0xb5362c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb53630: ldur            x3, [fp, #-0x28]
    // 0xb53634: ldur            x2, [fp, #-0x58]
    // 0xb53638: add             x0, x2, #1
    // 0xb5363c: lsl             x1, x0, #1
    // 0xb53640: StoreField: r3->field_b = r1
    //     0xb53640: stur            w1, [x3, #0xb]
    // 0xb53644: LoadField: r1 = r3->field_f
    //     0xb53644: ldur            w1, [x3, #0xf]
    // 0xb53648: DecompressPointer r1
    //     0xb53648: add             x1, x1, HEAP, lsl #32
    // 0xb5364c: ldur            x0, [fp, #-0x50]
    // 0xb53650: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb53650: add             x25, x1, x2, lsl #2
    //     0xb53654: add             x25, x25, #0xf
    //     0xb53658: str             w0, [x25]
    //     0xb5365c: tbz             w0, #0, #0xb53678
    //     0xb53660: ldurb           w16, [x1, #-1]
    //     0xb53664: ldurb           w17, [x0, #-1]
    //     0xb53668: and             x16, x17, x16, lsr #2
    //     0xb5366c: tst             x16, HEAP, lsr #32
    //     0xb53670: b.eq            #0xb53678
    //     0xb53674: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb53678: b               #0xb53680
    // 0xb5367c: ldur            x3, [fp, #-0x28]
    // 0xb53680: ldur            x0, [fp, #-0x78]
    // 0xb53684: tbnz            w0, #4, #0xb53738
    // 0xb53688: ldur            x4, [fp, #-0x30]
    // 0xb5368c: ldur            x0, [fp, #-0x50]
    // 0xb53690: r2 = Null
    //     0xb53690: mov             x2, NULL
    // 0xb53694: r1 = Null
    //     0xb53694: mov             x1, NULL
    // 0xb53698: r4 = LoadClassIdInstr(r0)
    //     0xb53698: ldur            x4, [x0, #-1]
    //     0xb5369c: ubfx            x4, x4, #0xc, #0x14
    // 0xb536a0: cmp             x4, #0x63f
    // 0xb536a4: b.eq            #0xb536bc
    // 0xb536a8: r8 = Tafsir
    //     0xb536a8: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b120] Type: Tafsir
    //     0xb536ac: ldr             x8, [x8, #0x120]
    // 0xb536b0: r3 = Null
    //     0xb536b0: add             x3, PP, #0x29, lsl #12  ; [pp+0x29f78] Null
    //     0xb536b4: ldr             x3, [x3, #0xf78]
    // 0xb536b8: r0 = Tafsir()
    //     0xb536b8: bl              #0x83ef88  ; IsType_Tafsir_Stub
    // 0xb536bc: ldur            x0, [fp, #-0x30]
    // 0xb536c0: LoadField: r1 = r0->field_b
    //     0xb536c0: ldur            w1, [x0, #0xb]
    // 0xb536c4: LoadField: r2 = r0->field_f
    //     0xb536c4: ldur            w2, [x0, #0xf]
    // 0xb536c8: DecompressPointer r2
    //     0xb536c8: add             x2, x2, HEAP, lsl #32
    // 0xb536cc: LoadField: r3 = r2->field_b
    //     0xb536cc: ldur            w3, [x2, #0xb]
    // 0xb536d0: r2 = LoadInt32Instr(r1)
    //     0xb536d0: sbfx            x2, x1, #1, #0x1f
    // 0xb536d4: stur            x2, [fp, #-0x58]
    // 0xb536d8: r1 = LoadInt32Instr(r3)
    //     0xb536d8: sbfx            x1, x3, #1, #0x1f
    // 0xb536dc: cmp             x2, x1
    // 0xb536e0: b.ne            #0xb536ec
    // 0xb536e4: mov             x1, x0
    // 0xb536e8: r0 = _growToNextCapacity()
    //     0xb536e8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb536ec: ldur            x5, [fp, #-0x30]
    // 0xb536f0: ldur            x2, [fp, #-0x58]
    // 0xb536f4: add             x0, x2, #1
    // 0xb536f8: lsl             x1, x0, #1
    // 0xb536fc: StoreField: r5->field_b = r1
    //     0xb536fc: stur            w1, [x5, #0xb]
    // 0xb53700: LoadField: r1 = r5->field_f
    //     0xb53700: ldur            w1, [x5, #0xf]
    // 0xb53704: DecompressPointer r1
    //     0xb53704: add             x1, x1, HEAP, lsl #32
    // 0xb53708: ldur            x0, [fp, #-0x50]
    // 0xb5370c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb5370c: add             x25, x1, x2, lsl #2
    //     0xb53710: add             x25, x25, #0xf
    //     0xb53714: str             w0, [x25]
    //     0xb53718: tbz             w0, #0, #0xb53734
    //     0xb5371c: ldurb           w16, [x1, #-1]
    //     0xb53720: ldurb           w17, [x0, #-1]
    //     0xb53724: and             x16, x17, x16, lsr #2
    //     0xb53728: tst             x16, HEAP, lsr #32
    //     0xb5372c: b.eq            #0xb53734
    //     0xb53730: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb53734: b               #0xb5373c
    // 0xb53738: ldur            x5, [fp, #-0x30]
    // 0xb5373c: mov             x7, x5
    // 0xb53740: ldur            x2, [fp, #-0x48]
    // 0xb53744: ldur            x5, [fp, #-0x20]
    // 0xb53748: ldur            x3, [fp, #-0x40]
    // 0xb5374c: ldur            x4, [fp, #-0x38]
    // 0xb53750: b               #0xb531f0
    // 0xb53754: ldur            x5, [fp, #-0x30]
    // 0xb53758: ldur            x0, [fp, #-0x48]
    // 0xb5375c: StoreField: r0->field_b = rNULL
    //     0xb5375c: stur            NULL, [x0, #0xb]
    // 0xb53760: ldur            x2, [fp, #-0x10]
    // 0xb53764: ldur            x3, [fp, #-0x28]
    // 0xb53768: r1 = <List<Tafsir>>
    //     0xb53768: add             x1, PP, #0x29, lsl #12  ; [pp+0x29f88] TypeArguments: <List<Tafsir>>
    //     0xb5376c: ldr             x1, [x1, #0xf88]
    // 0xb53770: r0 = _GrowableList._literal3()
    //     0xb53770: bl              #0x62b240  ; [dart:core] _GrowableList::_GrowableList._literal3
    // 0xb53774: r0 = ReturnAsyncNotFuture()
    //     0xb53774: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb53778: mov             x0, x2
    // 0xb5377c: r0 = ConcurrentModificationError()
    //     0xb5377c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb53780: mov             x1, x0
    // 0xb53784: ldur            x0, [fp, #-0x18]
    // 0xb53788: StoreField: r1->field_b = r0
    //     0xb53788: stur            w0, [x1, #0xb]
    // 0xb5378c: mov             x0, x1
    // 0xb53790: r0 = Throw()
    //     0xb53790: bl              #0xec04b8  ; ThrowStub
    // 0xb53794: brk             #0
    // 0xb53798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb53798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5379c: b               #0xb5307c
    // 0xb537a0: r9 = keystore
    //     0xb537a0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb537a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb537a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb537a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb537a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb537ac: b               #0xb53208
    // 0xb537b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb537b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb537b4: b               #0xb53330
  }
}
