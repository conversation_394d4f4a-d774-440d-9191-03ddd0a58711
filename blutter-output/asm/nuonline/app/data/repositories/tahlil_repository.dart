// lib: , url: package:nuonline/app/data/repositories/tahlil_repository.dart

// class id: 1050099, size: 0x8
class :: {
}

// class id: 1077, size: 0xc, field offset: 0x8
class TahlilRepository extends Object {

  _ get(/* No info */) async {
    // ** addr: 0x8ae9e8, size: 0xe0
    // 0x8ae9e8: EnterFrame
    //     0x8ae9e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae9ec: mov             fp, SP
    // 0x8ae9f0: AllocStack(0x28)
    //     0x8ae9f0: sub             SP, SP, #0x28
    // 0x8ae9f4: SetupParameters(TahlilRepository this /* r1 => r1, fp-0x10 */)
    //     0x8ae9f4: stur            NULL, [fp, #-8]
    //     0x8ae9f8: stur            x1, [fp, #-0x10]
    // 0x8ae9fc: CheckStackOverflow
    //     0x8ae9fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aea00: cmp             SP, x16
    //     0x8aea04: b.ls            #0x8aeac0
    // 0x8aea08: InitAsync() -> Future<List<Verse>>
    //     0x8aea08: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b4d0] TypeArguments: <List<Verse>>
    //     0x8aea0c: ldr             x0, [x0, #0x4d0]
    //     0x8aea10: bl              #0x661298  ; InitAsyncStub
    // 0x8aea14: ldur            x0, [fp, #-0x10]
    // 0x8aea18: LoadField: r1 = r0->field_7
    //     0x8aea18: ldur            w1, [x0, #7]
    // 0x8aea1c: DecompressPointer r1
    //     0x8aea1c: add             x1, x1, HEAP, lsl #32
    // 0x8aea20: r16 = <List>
    //     0x8aea20: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x8aea24: stp             x1, x16, [SP, #8]
    // 0x8aea28: r16 = "tahlil.json"
    //     0x8aea28: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c408] "tahlil.json"
    //     0x8aea2c: ldr             x16, [x16, #0x408]
    // 0x8aea30: str             x16, [SP]
    // 0x8aea34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aea34: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aea38: r0 = read()
    //     0x8aea38: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x8aea3c: mov             x1, x0
    // 0x8aea40: stur            x1, [fp, #-0x10]
    // 0x8aea44: r0 = Await()
    //     0x8aea44: bl              #0x661044  ; AwaitStub
    // 0x8aea48: r1 = Function '<anonymous closure>':.
    //     0x8aea48: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c410] AnonymousClosure: (0x8aeac8), in [package:nuonline/app/data/repositories/tahlil_repository.dart] TahlilRepository::get (0x8ae9e8)
    //     0x8aea4c: ldr             x1, [x1, #0x410]
    // 0x8aea50: r2 = Null
    //     0x8aea50: mov             x2, NULL
    // 0x8aea54: stur            x0, [fp, #-0x10]
    // 0x8aea58: r0 = AllocateClosure()
    //     0x8aea58: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aea5c: mov             x1, x0
    // 0x8aea60: ldur            x0, [fp, #-0x10]
    // 0x8aea64: r2 = LoadClassIdInstr(r0)
    //     0x8aea64: ldur            x2, [x0, #-1]
    //     0x8aea68: ubfx            x2, x2, #0xc, #0x14
    // 0x8aea6c: r16 = <Verse>
    //     0x8aea6c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8aea70: ldr             x16, [x16, #0x1f0]
    // 0x8aea74: stp             x0, x16, [SP, #8]
    // 0x8aea78: str             x1, [SP]
    // 0x8aea7c: mov             x0, x2
    // 0x8aea80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aea80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aea84: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8aea84: movz            x17, #0xf28c
    //     0x8aea88: add             lr, x0, x17
    //     0x8aea8c: ldr             lr, [x21, lr, lsl #3]
    //     0x8aea90: blr             lr
    // 0x8aea94: r1 = LoadClassIdInstr(r0)
    //     0x8aea94: ldur            x1, [x0, #-1]
    //     0x8aea98: ubfx            x1, x1, #0xc, #0x14
    // 0x8aea9c: mov             x16, x0
    // 0x8aeaa0: mov             x0, x1
    // 0x8aeaa4: mov             x1, x16
    // 0x8aeaa8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8aeaa8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8aeaac: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8aeaac: movz            x17, #0xd889
    //     0x8aeab0: add             lr, x0, x17
    //     0x8aeab4: ldr             lr, [x21, lr, lsl #3]
    //     0x8aeab8: blr             lr
    // 0x8aeabc: r0 = ReturnAsyncNotFuture()
    //     0x8aeabc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8aeac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aeac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aeac4: b               #0x8aea08
  }
  [closure] Verse <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8aeac8, size: 0x50
    // 0x8aeac8: EnterFrame
    //     0x8aeac8: stp             fp, lr, [SP, #-0x10]!
    //     0x8aeacc: mov             fp, SP
    // 0x8aead0: CheckStackOverflow
    //     0x8aead0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aead4: cmp             SP, x16
    //     0x8aead8: b.ls            #0x8aeb10
    // 0x8aeadc: ldr             x0, [fp, #0x10]
    // 0x8aeae0: r2 = Null
    //     0x8aeae0: mov             x2, NULL
    // 0x8aeae4: r1 = Null
    //     0x8aeae4: mov             x1, NULL
    // 0x8aeae8: r8 = Map<String, dynamic>
    //     0x8aeae8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8aeaec: r3 = Null
    //     0x8aeaec: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c418] Null
    //     0x8aeaf0: ldr             x3, [x3, #0x418]
    // 0x8aeaf4: r0 = Map<String, dynamic>()
    //     0x8aeaf4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8aeaf8: ldr             x2, [fp, #0x10]
    // 0x8aeafc: r1 = Null
    //     0x8aeafc: mov             x1, NULL
    // 0x8aeb00: r0 = Verse.fromMap()
    //     0x8aeb00: bl              #0x7c6bb0  ; [package:nuonline/app/data/models/verse.dart] Verse::Verse.fromMap
    // 0x8aeb04: LeaveFrame
    //     0x8aeb04: mov             SP, fp
    //     0x8aeb08: ldp             fp, lr, [SP], #0x10
    // 0x8aeb0c: ret
    //     0x8aeb0c: ret             
    // 0x8aeb10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aeb10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aeb14: b               #0x8aeadc
  }
}
