// lib: , url: package:nuonline/app/data/repositories/topic_repository.dart

// class id: 1050100, size: 0x8
class :: {
}

// class id: 1076, size: 0xc, field offset: 0x8
class TopicRepository extends Object {

  _ findAll(/* No info */) async {
    // ** addr: 0xe3e0a0, size: 0xf0
    // 0xe3e0a0: EnterFrame
    //     0xe3e0a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe3e0a4: mov             fp, SP
    // 0xe3e0a8: AllocStack(0x88)
    //     0xe3e0a8: sub             SP, SP, #0x88
    // 0xe3e0ac: SetupParameters(TopicRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0xe3e0ac: stur            NULL, [fp, #-8]
    //     0xe3e0b0: stur            x1, [fp, #-0x60]
    //     0xe3e0b4: stur            x2, [fp, #-0x68]
    // 0xe3e0b8: CheckStackOverflow
    //     0xe3e0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3e0bc: cmp             SP, x16
    //     0xe3e0c0: b.ls            #0xe3e188
    // 0xe3e0c4: InitAsync() -> Future<ApiResult<List<Topic>>>
    //     0xe3e0c4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29970] TypeArguments: <ApiResult<List<Topic>>>
    //     0xe3e0c8: ldr             x0, [x0, #0x970]
    //     0xe3e0cc: bl              #0x661298  ; InitAsyncStub
    // 0xe3e0d0: ldur            x0, [fp, #-0x60]
    // 0xe3e0d4: LoadField: r1 = r0->field_7
    //     0xe3e0d4: ldur            w1, [x0, #7]
    // 0xe3e0d8: DecompressPointer r1
    //     0xe3e0d8: add             x1, x1, HEAP, lsl #32
    // 0xe3e0dc: stp             x1, NULL, [SP, #0x10]
    // 0xe3e0e0: r16 = "/topics"
    //     0xe3e0e0: add             x16, PP, #0x29, lsl #12  ; [pp+0x29978] "/topics"
    //     0xe3e0e4: ldr             x16, [x16, #0x978]
    // 0xe3e0e8: ldur            lr, [fp, #-0x68]
    // 0xe3e0ec: stp             lr, x16, [SP]
    // 0xe3e0f0: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe3e0f0: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe3e0f4: ldr             x4, [x4, #0x2f0]
    // 0xe3e0f8: r0 = get()
    //     0xe3e0f8: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe3e0fc: mov             x1, x0
    // 0xe3e100: stur            x1, [fp, #-0x60]
    // 0xe3e104: r0 = Await()
    //     0xe3e104: bl              #0x661044  ; AwaitStub
    // 0xe3e108: stur            x0, [fp, #-0x60]
    // 0xe3e10c: LoadField: r1 = r0->field_b
    //     0xe3e10c: ldur            w1, [x0, #0xb]
    // 0xe3e110: DecompressPointer r1
    //     0xe3e110: add             x1, x1, HEAP, lsl #32
    // 0xe3e114: r0 = fromResponse()
    //     0xe3e114: bl              #0xe3e190  ; [package:nuonline/app/data/models/topic.dart] Topic::fromResponse
    // 0xe3e118: mov             x3, x0
    // 0xe3e11c: ldur            x0, [fp, #-0x60]
    // 0xe3e120: stur            x3, [fp, #-0x68]
    // 0xe3e124: LoadField: r2 = r0->field_1b
    //     0xe3e124: ldur            w2, [x0, #0x1b]
    // 0xe3e128: DecompressPointer r2
    //     0xe3e128: add             x2, x2, HEAP, lsl #32
    // 0xe3e12c: r1 = Null
    //     0xe3e12c: mov             x1, NULL
    // 0xe3e130: r0 = Pagination.fromHeaders()
    //     0xe3e130: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe3e134: r1 = <List<Topic>>
    //     0xe3e134: add             x1, PP, #0x29, lsl #12  ; [pp+0x29980] TypeArguments: <List<Topic>>
    //     0xe3e138: ldr             x1, [x1, #0x980]
    // 0xe3e13c: stur            x0, [fp, #-0x60]
    // 0xe3e140: r0 = _$SuccessImpl()
    //     0xe3e140: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe3e144: mov             x1, x0
    // 0xe3e148: ldur            x0, [fp, #-0x68]
    // 0xe3e14c: StoreField: r1->field_b = r0
    //     0xe3e14c: stur            w0, [x1, #0xb]
    // 0xe3e150: ldur            x0, [fp, #-0x60]
    // 0xe3e154: StoreField: r1->field_f = r0
    //     0xe3e154: stur            w0, [x1, #0xf]
    // 0xe3e158: mov             x0, x1
    // 0xe3e15c: r0 = ReturnAsyncNotFuture()
    //     0xe3e15c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe3e160: sub             SP, fp, #0x88
    // 0xe3e164: mov             x1, x0
    // 0xe3e168: r0 = getDioException()
    //     0xe3e168: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe3e16c: r1 = <List<Topic>>
    //     0xe3e16c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29980] TypeArguments: <List<Topic>>
    //     0xe3e170: ldr             x1, [x1, #0x980]
    // 0xe3e174: stur            x0, [fp, #-0x60]
    // 0xe3e178: r0 = _$FailureImpl()
    //     0xe3e178: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe3e17c: ldur            x1, [fp, #-0x60]
    // 0xe3e180: StoreField: r0->field_b = r1
    //     0xe3e180: stur            w1, [x0, #0xb]
    // 0xe3e184: r0 = ReturnAsyncNotFuture()
    //     0xe3e184: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe3e188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3e188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3e18c: b               #0xe3e0c4
  }
}
