// lib: , url: package:nuonline/app/data/repositories/tutorial_repository.dart

// class id: 1050101, size: 0x8
class :: {
}

// class id: 1075, size: 0x10, field offset: 0x8
class TutorialRepository extends Object {

  _ findById(/* No info */) async {
    // ** addr: 0x91cf58, size: 0x124
    // 0x91cf58: EnterFrame
    //     0x91cf58: stp             fp, lr, [SP, #-0x10]!
    //     0x91cf5c: mov             fp, SP
    // 0x91cf60: AllocStack(0x80)
    //     0x91cf60: sub             SP, SP, #0x80
    // 0x91cf64: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x91cf64: stur            NULL, [fp, #-8]
    //     0x91cf68: stur            x1, [fp, #-0x58]
    //     0x91cf6c: stur            x2, [fp, #-0x60]
    // 0x91cf70: CheckStackOverflow
    //     0x91cf70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91cf74: cmp             SP, x16
    //     0x91cf78: b.ls            #0x91d074
    // 0x91cf7c: InitAsync() -> Future<ApiResult<Tutorial>>
    //     0x91cf7c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3ca60] TypeArguments: <ApiResult<Tutorial>>
    //     0x91cf80: ldr             x0, [x0, #0xa60]
    //     0x91cf84: bl              #0x661298  ; InitAsyncStub
    // 0x91cf88: ldur            x1, [fp, #-0x58]
    // 0x91cf8c: ldur            x0, [fp, #-0x60]
    // 0x91cf90: LoadField: r3 = r1->field_7
    //     0x91cf90: ldur            w3, [x1, #7]
    // 0x91cf94: DecompressPointer r3
    //     0x91cf94: add             x3, x3, HEAP, lsl #32
    // 0x91cf98: stur            x3, [fp, #-0x68]
    // 0x91cf9c: r1 = Null
    //     0x91cf9c: mov             x1, NULL
    // 0x91cfa0: r2 = 4
    //     0x91cfa0: movz            x2, #0x4
    // 0x91cfa4: r0 = AllocateArray()
    //     0x91cfa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x91cfa8: mov             x2, x0
    // 0x91cfac: r16 = "/tutorial/"
    //     0x91cfac: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca68] "/tutorial/"
    //     0x91cfb0: ldr             x16, [x16, #0xa68]
    // 0x91cfb4: StoreField: r2->field_f = r16
    //     0x91cfb4: stur            w16, [x2, #0xf]
    // 0x91cfb8: ldur            x3, [fp, #-0x60]
    // 0x91cfbc: r0 = BoxInt64Instr(r3)
    //     0x91cfbc: sbfiz           x0, x3, #1, #0x1f
    //     0x91cfc0: cmp             x3, x0, asr #1
    //     0x91cfc4: b.eq            #0x91cfd0
    //     0x91cfc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91cfcc: stur            x3, [x0, #7]
    // 0x91cfd0: StoreField: r2->field_13 = r0
    //     0x91cfd0: stur            w0, [x2, #0x13]
    // 0x91cfd4: str             x2, [SP]
    // 0x91cfd8: r0 = _interpolate()
    //     0x91cfd8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x91cfdc: ldur            x16, [fp, #-0x68]
    // 0x91cfe0: stp             x16, NULL, [SP, #8]
    // 0x91cfe4: str             x0, [SP]
    // 0x91cfe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91cfe8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91cfec: r0 = get()
    //     0x91cfec: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x91cff0: mov             x1, x0
    // 0x91cff4: stur            x1, [fp, #-0x58]
    // 0x91cff8: r0 = Await()
    //     0x91cff8: bl              #0x661044  ; AwaitStub
    // 0x91cffc: LoadField: r3 = r0->field_b
    //     0x91cffc: ldur            w3, [x0, #0xb]
    // 0x91d000: DecompressPointer r3
    //     0x91d000: add             x3, x3, HEAP, lsl #32
    // 0x91d004: mov             x0, x3
    // 0x91d008: stur            x3, [fp, #-0x58]
    // 0x91d00c: r2 = Null
    //     0x91d00c: mov             x2, NULL
    // 0x91d010: r1 = Null
    //     0x91d010: mov             x1, NULL
    // 0x91d014: r8 = Map<String, dynamic>
    //     0x91d014: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x91d018: r3 = Null
    //     0x91d018: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3ca70] Null
    //     0x91d01c: ldr             x3, [x3, #0xa70]
    // 0x91d020: r0 = Map<String, dynamic>()
    //     0x91d020: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x91d024: ldur            x2, [fp, #-0x58]
    // 0x91d028: r1 = Null
    //     0x91d028: mov             x1, NULL
    // 0x91d02c: r0 = Tutorial.fromMap()
    //     0x91d02c: bl              #0x91d07c  ; [package:nuonline/app/data/models/tutorial.dart] Tutorial::Tutorial.fromMap
    // 0x91d030: r1 = <Tutorial>
    //     0x91d030: ldr             x1, [PP, #0x7bb8]  ; [pp+0x7bb8] TypeArguments: <Tutorial>
    // 0x91d034: stur            x0, [fp, #-0x58]
    // 0x91d038: r0 = _$SuccessImpl()
    //     0x91d038: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91d03c: mov             x1, x0
    // 0x91d040: ldur            x0, [fp, #-0x58]
    // 0x91d044: StoreField: r1->field_b = r0
    //     0x91d044: stur            w0, [x1, #0xb]
    // 0x91d048: mov             x0, x1
    // 0x91d04c: r0 = ReturnAsyncNotFuture()
    //     0x91d04c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91d050: sub             SP, fp, #0x80
    // 0x91d054: mov             x1, x0
    // 0x91d058: r0 = getDioException()
    //     0x91d058: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x91d05c: r1 = <Tutorial>
    //     0x91d05c: ldr             x1, [PP, #0x7bb8]  ; [pp+0x7bb8] TypeArguments: <Tutorial>
    // 0x91d060: stur            x0, [fp, #-0x58]
    // 0x91d064: r0 = _$FailureImpl()
    //     0x91d064: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x91d068: ldur            x1, [fp, #-0x58]
    // 0x91d06c: StoreField: r0->field_b = r1
    //     0x91d06c: stur            w1, [x0, #0xb]
    // 0x91d070: r0 = ReturnAsyncNotFuture()
    //     0x91d070: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91d074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d078: b               #0x91cf7c
  }
  _ findByIdFromBookmark(/* No info */) {
    // ** addr: 0x91d43c, size: 0x8c
    // 0x91d43c: EnterFrame
    //     0x91d43c: stp             fp, lr, [SP, #-0x10]!
    //     0x91d440: mov             fp, SP
    // 0x91d444: AllocStack(0x20)
    //     0x91d444: sub             SP, SP, #0x20
    // 0x91d448: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x91d448: stur            x2, [fp, #-8]
    // 0x91d44c: CheckStackOverflow
    //     0x91d44c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d450: cmp             SP, x16
    //     0x91d454: b.ls            #0x91d4c0
    // 0x91d458: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x91d458: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91d45c: ldr             x0, [x0, #0x2728]
    //     0x91d460: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91d464: cmp             w0, w16
    //     0x91d468: b.ne            #0x91d474
    //     0x91d46c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x91d470: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91d474: r16 = <Tutorial>
    //     0x91d474: ldr             x16, [PP, #0x7bb8]  ; [pp+0x7bb8] TypeArguments: <Tutorial>
    // 0x91d478: stp             x0, x16, [SP, #8]
    // 0x91d47c: r16 = "v2_tutorial"
    //     0x91d47c: ldr             x16, [PP, #0x7c58]  ; [pp+0x7c58] "v2_tutorial"
    // 0x91d480: str             x16, [SP]
    // 0x91d484: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91d484: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91d488: r0 = box()
    //     0x91d488: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x91d48c: mov             x3, x0
    // 0x91d490: ldur            x2, [fp, #-8]
    // 0x91d494: r0 = BoxInt64Instr(r2)
    //     0x91d494: sbfiz           x0, x2, #1, #0x1f
    //     0x91d498: cmp             x2, x0, asr #1
    //     0x91d49c: b.eq            #0x91d4a8
    //     0x91d4a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91d4a4: stur            x2, [x0, #7]
    // 0x91d4a8: mov             x1, x3
    // 0x91d4ac: mov             x2, x0
    // 0x91d4b0: r0 = get()
    //     0x91d4b0: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x91d4b4: LeaveFrame
    //     0x91d4b4: mov             SP, fp
    //     0x91d4b8: ldp             fp, lr, [SP], #0x10
    // 0x91d4bc: ret
    //     0x91d4bc: ret             
    // 0x91d4c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d4c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d4c4: b               #0x91d458
  }
  _ findAllCategory(/* No info */) async {
    // ** addr: 0x92423c, size: 0xbc
    // 0x92423c: EnterFrame
    //     0x92423c: stp             fp, lr, [SP, #-0x10]!
    //     0x924240: mov             fp, SP
    // 0x924244: AllocStack(0x68)
    //     0x924244: sub             SP, SP, #0x68
    // 0x924248: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x50 */)
    //     0x924248: stur            NULL, [fp, #-8]
    //     0x92424c: stur            x1, [fp, #-0x50]
    // 0x924250: CheckStackOverflow
    //     0x924250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924254: cmp             SP, x16
    //     0x924258: b.ls            #0x9242f0
    // 0x92425c: InitAsync() -> Future<ApiResult<List<TutorialCategory>>>
    //     0x92425c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29760] TypeArguments: <ApiResult<List<TutorialCategory>>>
    //     0x924260: ldr             x0, [x0, #0x760]
    //     0x924264: bl              #0x661298  ; InitAsyncStub
    // 0x924268: ldur            x0, [fp, #-0x50]
    // 0x92426c: LoadField: r1 = r0->field_7
    //     0x92426c: ldur            w1, [x0, #7]
    // 0x924270: DecompressPointer r1
    //     0x924270: add             x1, x1, HEAP, lsl #32
    // 0x924274: stp             x1, NULL, [SP, #8]
    // 0x924278: r16 = "/tutorial/categories"
    //     0x924278: add             x16, PP, #0x29, lsl #12  ; [pp+0x29768] "/tutorial/categories"
    //     0x92427c: ldr             x16, [x16, #0x768]
    // 0x924280: str             x16, [SP]
    // 0x924284: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x924284: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x924288: r0 = get()
    //     0x924288: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x92428c: mov             x1, x0
    // 0x924290: stur            x1, [fp, #-0x50]
    // 0x924294: r0 = Await()
    //     0x924294: bl              #0x661044  ; AwaitStub
    // 0x924298: LoadField: r1 = r0->field_b
    //     0x924298: ldur            w1, [x0, #0xb]
    // 0x92429c: DecompressPointer r1
    //     0x92429c: add             x1, x1, HEAP, lsl #32
    // 0x9242a0: r0 = fromResponse()
    //     0x9242a0: bl              #0x9242f8  ; [package:nuonline/app/data/models/tutorial.dart] TutorialCategory::fromResponse
    // 0x9242a4: r1 = <List<TutorialCategory>>
    //     0x9242a4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29720] TypeArguments: <List<TutorialCategory>>
    //     0x9242a8: ldr             x1, [x1, #0x720]
    // 0x9242ac: stur            x0, [fp, #-0x50]
    // 0x9242b0: r0 = _$SuccessImpl()
    //     0x9242b0: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x9242b4: mov             x1, x0
    // 0x9242b8: ldur            x0, [fp, #-0x50]
    // 0x9242bc: StoreField: r1->field_b = r0
    //     0x9242bc: stur            w0, [x1, #0xb]
    // 0x9242c0: mov             x0, x1
    // 0x9242c4: r0 = ReturnAsyncNotFuture()
    //     0x9242c4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9242c8: sub             SP, fp, #0x68
    // 0x9242cc: mov             x1, x0
    // 0x9242d0: r0 = getDioException()
    //     0x9242d0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x9242d4: r1 = <List<TutorialCategory>>
    //     0x9242d4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29720] TypeArguments: <List<TutorialCategory>>
    //     0x9242d8: ldr             x1, [x1, #0x720]
    // 0x9242dc: stur            x0, [fp, #-0x50]
    // 0x9242e0: r0 = _$FailureImpl()
    //     0x9242e0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x9242e4: ldur            x1, [fp, #-0x50]
    // 0x9242e8: StoreField: r0->field_b = r1
    //     0x9242e8: stur            w1, [x0, #0xb]
    // 0x9242ec: r0 = ReturnAsyncNotFuture()
    //     0x9242ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9242f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9242f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9242f4: b               #0x92425c
  }
  _ findAllFromBookmark(/* No info */) {
    // ** addr: 0x9248b8, size: 0xa0
    // 0x9248b8: EnterFrame
    //     0x9248b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9248bc: mov             fp, SP
    // 0x9248c0: AllocStack(0x20)
    //     0x9248c0: sub             SP, SP, #0x20
    // 0x9248c4: CheckStackOverflow
    //     0x9248c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9248c8: cmp             SP, x16
    //     0x9248cc: b.ls            #0x924948
    // 0x9248d0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x9248d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9248d4: ldr             x0, [x0, #0x2728]
    //     0x9248d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9248dc: cmp             w0, w16
    //     0x9248e0: b.ne            #0x9248ec
    //     0x9248e4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x9248e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9248ec: r16 = <Tutorial>
    //     0x9248ec: ldr             x16, [PP, #0x7bb8]  ; [pp+0x7bb8] TypeArguments: <Tutorial>
    // 0x9248f0: stp             x0, x16, [SP, #8]
    // 0x9248f4: r16 = "v2_tutorial"
    //     0x9248f4: ldr             x16, [PP, #0x7c58]  ; [pp+0x7c58] "v2_tutorial"
    // 0x9248f8: str             x16, [SP]
    // 0x9248fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9248fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x924900: r0 = box()
    //     0x924900: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x924904: mov             x1, x0
    // 0x924908: stur            x0, [fp, #-8]
    // 0x92490c: r0 = checkOpen()
    //     0x92490c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x924910: ldur            x0, [fp, #-8]
    // 0x924914: LoadField: r1 = r0->field_1b
    //     0x924914: ldur            w1, [x0, #0x1b]
    // 0x924918: DecompressPointer r1
    //     0x924918: add             x1, x1, HEAP, lsl #32
    // 0x92491c: r16 = Sentinel
    //     0x92491c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x924920: cmp             w1, w16
    // 0x924924: b.eq            #0x924950
    // 0x924928: r0 = getValues()
    //     0x924928: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x92492c: LoadField: r1 = r0->field_7
    //     0x92492c: ldur            w1, [x0, #7]
    // 0x924930: DecompressPointer r1
    //     0x924930: add             x1, x1, HEAP, lsl #32
    // 0x924934: mov             x2, x0
    // 0x924938: r0 = _GrowableList.of()
    //     0x924938: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x92493c: LeaveFrame
    //     0x92493c: mov             SP, fp
    //     0x924940: ldp             fp, lr, [SP], #0x10
    // 0x924944: ret
    //     0x924944: ret             
    // 0x924948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92494c: b               #0x9248d0
    // 0x924950: r9 = keystore
    //     0x924950: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x924954: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x924954: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ findAllSubCategory(/* No info */) async {
    // ** addr: 0x924a10, size: 0x104
    // 0x924a10: EnterFrame
    //     0x924a10: stp             fp, lr, [SP, #-0x10]!
    //     0x924a14: mov             fp, SP
    // 0x924a18: AllocStack(0x80)
    //     0x924a18: sub             SP, SP, #0x80
    // 0x924a1c: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x924a1c: stur            NULL, [fp, #-8]
    //     0x924a20: stur            x1, [fp, #-0x58]
    //     0x924a24: stur            x2, [fp, #-0x60]
    // 0x924a28: CheckStackOverflow
    //     0x924a28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924a2c: cmp             SP, x16
    //     0x924a30: b.ls            #0x924b0c
    // 0x924a34: InitAsync() -> Future<ApiResult<List<TutorialSubCategory>>>
    //     0x924a34: add             x0, PP, #0x32, lsl #12  ; [pp+0x321e8] TypeArguments: <ApiResult<List<TutorialSubCategory>>>
    //     0x924a38: ldr             x0, [x0, #0x1e8]
    //     0x924a3c: bl              #0x661298  ; InitAsyncStub
    // 0x924a40: ldur            x1, [fp, #-0x58]
    // 0x924a44: ldur            x0, [fp, #-0x60]
    // 0x924a48: LoadField: r3 = r1->field_7
    //     0x924a48: ldur            w3, [x1, #7]
    // 0x924a4c: DecompressPointer r3
    //     0x924a4c: add             x3, x3, HEAP, lsl #32
    // 0x924a50: stur            x3, [fp, #-0x68]
    // 0x924a54: r1 = Null
    //     0x924a54: mov             x1, NULL
    // 0x924a58: r2 = 4
    //     0x924a58: movz            x2, #0x4
    // 0x924a5c: r0 = AllocateArray()
    //     0x924a5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x924a60: mov             x2, x0
    // 0x924a64: r16 = "/tutorial/categories/"
    //     0x924a64: add             x16, PP, #0x32, lsl #12  ; [pp+0x321f0] "/tutorial/categories/"
    //     0x924a68: ldr             x16, [x16, #0x1f0]
    // 0x924a6c: StoreField: r2->field_f = r16
    //     0x924a6c: stur            w16, [x2, #0xf]
    // 0x924a70: ldur            x3, [fp, #-0x60]
    // 0x924a74: r0 = BoxInt64Instr(r3)
    //     0x924a74: sbfiz           x0, x3, #1, #0x1f
    //     0x924a78: cmp             x3, x0, asr #1
    //     0x924a7c: b.eq            #0x924a88
    //     0x924a80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x924a84: stur            x3, [x0, #7]
    // 0x924a88: StoreField: r2->field_13 = r0
    //     0x924a88: stur            w0, [x2, #0x13]
    // 0x924a8c: str             x2, [SP]
    // 0x924a90: r0 = _interpolate()
    //     0x924a90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x924a94: ldur            x16, [fp, #-0x68]
    // 0x924a98: stp             x16, NULL, [SP, #8]
    // 0x924a9c: str             x0, [SP]
    // 0x924aa0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x924aa0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x924aa4: r0 = get()
    //     0x924aa4: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x924aa8: mov             x1, x0
    // 0x924aac: stur            x1, [fp, #-0x58]
    // 0x924ab0: r0 = Await()
    //     0x924ab0: bl              #0x661044  ; AwaitStub
    // 0x924ab4: LoadField: r1 = r0->field_b
    //     0x924ab4: ldur            w1, [x0, #0xb]
    // 0x924ab8: DecompressPointer r1
    //     0x924ab8: add             x1, x1, HEAP, lsl #32
    // 0x924abc: r0 = fromResponse()
    //     0x924abc: bl              #0x924b14  ; [package:nuonline/app/data/models/tutorial.dart] TutorialSubCategory::fromResponse
    // 0x924ac0: r1 = <List<TutorialSubCategory>>
    //     0x924ac0: add             x1, PP, #0x32, lsl #12  ; [pp+0x321f8] TypeArguments: <List<TutorialSubCategory>>
    //     0x924ac4: ldr             x1, [x1, #0x1f8]
    // 0x924ac8: stur            x0, [fp, #-0x58]
    // 0x924acc: r0 = _$SuccessImpl()
    //     0x924acc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x924ad0: mov             x1, x0
    // 0x924ad4: ldur            x0, [fp, #-0x58]
    // 0x924ad8: StoreField: r1->field_b = r0
    //     0x924ad8: stur            w0, [x1, #0xb]
    // 0x924adc: mov             x0, x1
    // 0x924ae0: r0 = ReturnAsyncNotFuture()
    //     0x924ae0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x924ae4: sub             SP, fp, #0x80
    // 0x924ae8: mov             x1, x0
    // 0x924aec: r0 = getDioException()
    //     0x924aec: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x924af0: r1 = <List<TutorialSubCategory>>
    //     0x924af0: add             x1, PP, #0x32, lsl #12  ; [pp+0x321f8] TypeArguments: <List<TutorialSubCategory>>
    //     0x924af4: ldr             x1, [x1, #0x1f8]
    // 0x924af8: stur            x0, [fp, #-0x58]
    // 0x924afc: r0 = _$FailureImpl()
    //     0x924afc: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x924b00: ldur            x1, [fp, #-0x58]
    // 0x924b04: StoreField: r0->field_b = r1
    //     0x924b04: stur            w1, [x0, #0xb]
    // 0x924b08: r0 = ReturnAsyncNotFuture()
    //     0x924b08: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x924b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924b0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924b10: b               #0x924a34
  }
  _ deleteFromBookmark(/* No info */) async {
    // ** addr: 0xa42494, size: 0x64
    // 0xa42494: EnterFrame
    //     0xa42494: stp             fp, lr, [SP, #-0x10]!
    //     0xa42498: mov             fp, SP
    // 0xa4249c: AllocStack(0x18)
    //     0xa4249c: sub             SP, SP, #0x18
    // 0xa424a0: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa424a0: stur            NULL, [fp, #-8]
    //     0xa424a4: stur            x1, [fp, #-0x10]
    //     0xa424a8: stur            x2, [fp, #-0x18]
    // 0xa424ac: CheckStackOverflow
    //     0xa424ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa424b0: cmp             SP, x16
    //     0xa424b4: b.ls            #0xa424f0
    // 0xa424b8: InitAsync() -> Future<void?>
    //     0xa424b8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa424bc: bl              #0x661298  ; InitAsyncStub
    // 0xa424c0: ldur            x0, [fp, #-0x10]
    // 0xa424c4: LoadField: r1 = r0->field_b
    //     0xa424c4: ldur            w1, [x0, #0xb]
    // 0xa424c8: DecompressPointer r1
    //     0xa424c8: add             x1, x1, HEAP, lsl #32
    // 0xa424cc: r0 = tutorial()
    //     0xa424cc: bl              #0x91d4c8  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::tutorial
    // 0xa424d0: mov             x1, x0
    // 0xa424d4: ldur            x2, [fp, #-0x18]
    // 0xa424d8: r0 = delete()
    //     0xa424d8: bl              #0xa424f8  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::delete
    // 0xa424dc: mov             x1, x0
    // 0xa424e0: stur            x1, [fp, #-0x10]
    // 0xa424e4: r0 = Await()
    //     0xa424e4: bl              #0x661044  ; AwaitStub
    // 0xa424e8: r0 = Null
    //     0xa424e8: mov             x0, NULL
    // 0xa424ec: r0 = ReturnAsyncNotFuture()
    //     0xa424ec: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa424f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa424f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa424f4: b               #0xa424b8
  }
  _ saveToBookmark(/* No info */) async {
    // ** addr: 0xa42844, size: 0xac
    // 0xa42844: EnterFrame
    //     0xa42844: stp             fp, lr, [SP, #-0x10]!
    //     0xa42848: mov             fp, SP
    // 0xa4284c: AllocStack(0x30)
    //     0xa4284c: sub             SP, SP, #0x30
    // 0xa42850: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xa42850: stur            NULL, [fp, #-8]
    //     0xa42854: mov             x3, x2
    //     0xa42858: stur            x1, [fp, #-0x10]
    //     0xa4285c: stur            x2, [fp, #-0x18]
    // 0xa42860: CheckStackOverflow
    //     0xa42860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42864: cmp             SP, x16
    //     0xa42868: b.ls            #0xa428e8
    // 0xa4286c: InitAsync() -> Future<void?>
    //     0xa4286c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa42870: bl              #0x661298  ; InitAsyncStub
    // 0xa42874: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xa42874: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa42878: ldr             x0, [x0, #0x2728]
    //     0xa4287c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa42880: cmp             w0, w16
    //     0xa42884: b.ne            #0xa42890
    //     0xa42888: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xa4288c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa42890: r16 = <Tutorial>
    //     0xa42890: ldr             x16, [PP, #0x7bb8]  ; [pp+0x7bb8] TypeArguments: <Tutorial>
    // 0xa42894: stp             x0, x16, [SP, #8]
    // 0xa42898: r16 = "v2_tutorial"
    //     0xa42898: ldr             x16, [PP, #0x7c58]  ; [pp+0x7c58] "v2_tutorial"
    // 0xa4289c: str             x16, [SP]
    // 0xa428a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa428a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa428a4: r0 = box()
    //     0xa428a4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xa428a8: mov             x2, x0
    // 0xa428ac: ldur            x3, [fp, #-0x18]
    // 0xa428b0: LoadField: r4 = r3->field_7
    //     0xa428b0: ldur            x4, [x3, #7]
    // 0xa428b4: r0 = BoxInt64Instr(r4)
    //     0xa428b4: sbfiz           x0, x4, #1, #0x1f
    //     0xa428b8: cmp             x4, x0, asr #1
    //     0xa428bc: b.eq            #0xa428c8
    //     0xa428c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa428c4: stur            x4, [x0, #7]
    // 0xa428c8: mov             x1, x2
    // 0xa428cc: mov             x2, x0
    // 0xa428d0: r0 = put()
    //     0xa428d0: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xa428d4: mov             x1, x0
    // 0xa428d8: stur            x1, [fp, #-0x10]
    // 0xa428dc: r0 = Await()
    //     0xa428dc: bl              #0x661044  ; AwaitStub
    // 0xa428e0: r0 = Null
    //     0xa428e0: mov             x0, NULL
    // 0xa428e4: r0 = ReturnAsyncNotFuture()
    //     0xa428e4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa428e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa428e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa428ec: b               #0xa4286c
  }
  _ search(/* No info */) async {
    // ** addr: 0xe39020, size: 0x110
    // 0xe39020: EnterFrame
    //     0xe39020: stp             fp, lr, [SP, #-0x10]!
    //     0xe39024: mov             fp, SP
    // 0xe39028: AllocStack(0x88)
    //     0xe39028: sub             SP, SP, #0x88
    // 0xe3902c: SetupParameters(TutorialRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe3902c: stur            NULL, [fp, #-8]
    //     0xe39030: stur            x1, [fp, #-0x58]
    //     0xe39034: stur            x2, [fp, #-0x60]
    // 0xe39038: CheckStackOverflow
    //     0xe39038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3903c: cmp             SP, x16
    //     0xe39040: b.ls            #0xe39128
    // 0xe39044: InitAsync() -> Future<ApiResult<List<TutorialSearch>>>
    //     0xe39044: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d800] TypeArguments: <ApiResult<List<TutorialSearch>>>
    //     0xe39048: ldr             x0, [x0, #0x800]
    //     0xe3904c: bl              #0x661298  ; InitAsyncStub
    // 0xe39050: ldur            x1, [fp, #-0x58]
    // 0xe39054: ldur            x0, [fp, #-0x60]
    // 0xe39058: LoadField: r3 = r1->field_7
    //     0xe39058: ldur            w3, [x1, #7]
    // 0xe3905c: DecompressPointer r3
    //     0xe3905c: add             x3, x3, HEAP, lsl #32
    // 0xe39060: stur            x3, [fp, #-0x68]
    // 0xe39064: r1 = Null
    //     0xe39064: mov             x1, NULL
    // 0xe39068: r2 = 8
    //     0xe39068: movz            x2, #0x8
    // 0xe3906c: r0 = AllocateArray()
    //     0xe3906c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe39070: r16 = "q"
    //     0xe39070: add             x16, PP, #0x29, lsl #12  ; [pp+0x291d0] "q"
    //     0xe39074: ldr             x16, [x16, #0x1d0]
    // 0xe39078: StoreField: r0->field_f = r16
    //     0xe39078: stur            w16, [x0, #0xf]
    // 0xe3907c: ldur            x1, [fp, #-0x60]
    // 0xe39080: StoreField: r0->field_13 = r1
    //     0xe39080: stur            w1, [x0, #0x13]
    // 0xe39084: r16 = "limit"
    //     0xe39084: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0xe39088: ldr             x16, [x16, #0x50]
    // 0xe3908c: ArrayStore: r0[0] = r16  ; List_4
    //     0xe3908c: stur            w16, [x0, #0x17]
    // 0xe39090: r16 = 100
    //     0xe39090: movz            x16, #0x64
    // 0xe39094: StoreField: r0->field_1b = r16
    //     0xe39094: stur            w16, [x0, #0x1b]
    // 0xe39098: r16 = <String, dynamic>
    //     0xe39098: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xe3909c: stp             x0, x16, [SP]
    // 0xe390a0: r0 = Map._fromLiteral()
    //     0xe390a0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe390a4: ldur            x16, [fp, #-0x68]
    // 0xe390a8: stp             x16, NULL, [SP, #0x10]
    // 0xe390ac: r16 = "/tutorial/search"
    //     0xe390ac: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d808] "/tutorial/search"
    //     0xe390b0: ldr             x16, [x16, #0x808]
    // 0xe390b4: stp             x0, x16, [SP]
    // 0xe390b8: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe390b8: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe390bc: ldr             x4, [x4, #0x2f0]
    // 0xe390c0: r0 = get()
    //     0xe390c0: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe390c4: mov             x1, x0
    // 0xe390c8: stur            x1, [fp, #-0x58]
    // 0xe390cc: r0 = Await()
    //     0xe390cc: bl              #0x661044  ; AwaitStub
    // 0xe390d0: LoadField: r1 = r0->field_b
    //     0xe390d0: ldur            w1, [x0, #0xb]
    // 0xe390d4: DecompressPointer r1
    //     0xe390d4: add             x1, x1, HEAP, lsl #32
    // 0xe390d8: r0 = fromResponse()
    //     0xe390d8: bl              #0xe39130  ; [package:nuonline/app/data/models/tutorial.dart] TutorialSearch::fromResponse
    // 0xe390dc: r1 = <List<TutorialSearch>>
    //     0xe390dc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d810] TypeArguments: <List<TutorialSearch>>
    //     0xe390e0: ldr             x1, [x1, #0x810]
    // 0xe390e4: stur            x0, [fp, #-0x58]
    // 0xe390e8: r0 = _$SuccessImpl()
    //     0xe390e8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe390ec: mov             x1, x0
    // 0xe390f0: ldur            x0, [fp, #-0x58]
    // 0xe390f4: StoreField: r1->field_b = r0
    //     0xe390f4: stur            w0, [x1, #0xb]
    // 0xe390f8: mov             x0, x1
    // 0xe390fc: r0 = ReturnAsyncNotFuture()
    //     0xe390fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe39100: sub             SP, fp, #0x88
    // 0xe39104: mov             x1, x0
    // 0xe39108: r0 = getDioException()
    //     0xe39108: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe3910c: r1 = <List<TutorialSearch>>
    //     0xe3910c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d810] TypeArguments: <List<TutorialSearch>>
    //     0xe39110: ldr             x1, [x1, #0x810]
    // 0xe39114: stur            x0, [fp, #-0x58]
    // 0xe39118: r0 = _$FailureImpl()
    //     0xe39118: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe3911c: ldur            x1, [fp, #-0x58]
    // 0xe39120: StoreField: r0->field_b = r1
    //     0xe39120: stur            w1, [x0, #0xb]
    // 0xe39124: r0 = ReturnAsyncNotFuture()
    //     0xe39124: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe39128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe39128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3912c: b               #0xe39044
  }
}
