// lib: , url: package:nuonline/app/data/repositories/url_repository.dart

// class id: 1050102, size: 0x8
class :: {
}

// class id: 1074, size: 0xc, field offset: 0x8
class UrlRepository extends Object {

  _ parse(/* No info */) async {
    // ** addr: 0x7eb25c, size: 0x1e0
    // 0x7eb25c: EnterFrame
    //     0x7eb25c: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb260: mov             fp, SP
    // 0x7eb264: AllocStack(0x98)
    //     0x7eb264: sub             SP, SP, #0x98
    // 0x7eb268: SetupParameters(UrlRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x7eb268: stur            NULL, [fp, #-8]
    //     0x7eb26c: stur            x1, [fp, #-0x60]
    //     0x7eb270: stur            x2, [fp, #-0x68]
    // 0x7eb274: CheckStackOverflow
    //     0x7eb274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb278: cmp             SP, x16
    //     0x7eb27c: b.ls            #0x7eb434
    // 0x7eb280: InitAsync() -> Future<ApiResult<UrlRedirect>>
    //     0x7eb280: add             x0, PP, #0x40, lsl #12  ; [pp+0x407e0] TypeArguments: <ApiResult<UrlRedirect>>
    //     0x7eb284: ldr             x0, [x0, #0x7e0]
    //     0x7eb288: bl              #0x661298  ; InitAsyncStub
    // 0x7eb28c: ldur            x1, [fp, #-0x60]
    // 0x7eb290: ldur            x2, [fp, #-0x68]
    // 0x7eb294: r0 = normalize()
    //     0x7eb294: bl              #0x7eb730  ; [package:nuonline/app/data/repositories/url_repository.dart] UrlRepository::normalize
    // 0x7eb298: mov             x1, x0
    // 0x7eb29c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x7eb29c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x7eb2a0: r0 = parse()
    //     0x7eb2a0: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0x7eb2a4: r1 = LoadClassIdInstr(r0)
    //     0x7eb2a4: ldur            x1, [x0, #-1]
    //     0x7eb2a8: ubfx            x1, x1, #0xc, #0x14
    // 0x7eb2ac: mov             x16, x0
    // 0x7eb2b0: mov             x0, x1
    // 0x7eb2b4: mov             x1, x16
    // 0x7eb2b8: r0 = GDT[cid_x0 + -0xf97]()
    //     0x7eb2b8: sub             lr, x0, #0xf97
    //     0x7eb2bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb2c0: blr             lr
    // 0x7eb2c4: mov             x2, x0
    // 0x7eb2c8: r1 = const [quran.nu.or.id, nu.or.id, islam.nu.or.id]
    //     0x7eb2c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x407e8] List<String>(3)
    //     0x7eb2cc: ldr             x1, [x1, #0x7e8]
    // 0x7eb2d0: r0 = contains()
    //     0x7eb2d0: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x7eb2d4: tbnz            w0, #4, #0x7eb410
    // 0x7eb2d8: ldur            x0, [fp, #-0x60]
    // 0x7eb2dc: LoadField: r3 = r0->field_7
    //     0x7eb2dc: ldur            w3, [x0, #7]
    // 0x7eb2e0: DecompressPointer r3
    //     0x7eb2e0: add             x3, x3, HEAP, lsl #32
    // 0x7eb2e4: stur            x3, [fp, #-0x70]
    // 0x7eb2e8: r1 = Null
    //     0x7eb2e8: mov             x1, NULL
    // 0x7eb2ec: r2 = 4
    //     0x7eb2ec: movz            x2, #0x4
    // 0x7eb2f0: r0 = AllocateArray()
    //     0x7eb2f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7eb2f4: stur            x0, [fp, #-0x78]
    // 0x7eb2f8: r16 = "url"
    //     0x7eb2f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x7eb2fc: ldr             x16, [x16, #0xd78]
    // 0x7eb300: StoreField: r0->field_f = r16
    //     0x7eb300: stur            w16, [x0, #0xf]
    // 0x7eb304: ldur            x1, [fp, #-0x60]
    // 0x7eb308: ldur            x2, [fp, #-0x68]
    // 0x7eb30c: r0 = normalize()
    //     0x7eb30c: bl              #0x7eb730  ; [package:nuonline/app/data/repositories/url_repository.dart] UrlRepository::normalize
    // 0x7eb310: ldur            x1, [fp, #-0x78]
    // 0x7eb314: ArrayStore: r1[1] = r0  ; List_4
    //     0x7eb314: add             x25, x1, #0x13
    //     0x7eb318: str             w0, [x25]
    //     0x7eb31c: tbz             w0, #0, #0x7eb338
    //     0x7eb320: ldurb           w16, [x1, #-1]
    //     0x7eb324: ldurb           w17, [x0, #-1]
    //     0x7eb328: and             x16, x17, x16, lsr #2
    //     0x7eb32c: tst             x16, HEAP, lsr #32
    //     0x7eb330: b.eq            #0x7eb338
    //     0x7eb334: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7eb338: r16 = <String, String>
    //     0x7eb338: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x7eb33c: ldr             x16, [x16, #0x668]
    // 0x7eb340: ldur            lr, [fp, #-0x78]
    // 0x7eb344: stp             lr, x16, [SP]
    // 0x7eb348: r0 = Map._fromLiteral()
    //     0x7eb348: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7eb34c: ldur            x16, [fp, #-0x70]
    // 0x7eb350: stp             x16, NULL, [SP, #0x10]
    // 0x7eb354: r16 = "/url-to-app"
    //     0x7eb354: add             x16, PP, #0x40, lsl #12  ; [pp+0x407f0] "/url-to-app"
    //     0x7eb358: ldr             x16, [x16, #0x7f0]
    // 0x7eb35c: stp             x0, x16, [SP]
    // 0x7eb360: r4 = const [0x1, 0x3, 0x3, 0x2, data, 0x2, null]
    //     0x7eb360: add             x4, PP, #0x10, lsl #12  ; [pp+0x10428] List(7) [0x1, 0x3, 0x3, 0x2, "data", 0x2, Null]
    //     0x7eb364: ldr             x4, [x4, #0x428]
    // 0x7eb368: r0 = post()
    //     0x7eb368: bl              #0x7eb5f0  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::post
    // 0x7eb36c: mov             x1, x0
    // 0x7eb370: stur            x1, [fp, #-0x60]
    // 0x7eb374: r0 = Await()
    //     0x7eb374: bl              #0x661044  ; AwaitStub
    // 0x7eb378: LoadField: r3 = r0->field_b
    //     0x7eb378: ldur            w3, [x0, #0xb]
    // 0x7eb37c: DecompressPointer r3
    //     0x7eb37c: add             x3, x3, HEAP, lsl #32
    // 0x7eb380: mov             x0, x3
    // 0x7eb384: stur            x3, [fp, #-0x60]
    // 0x7eb388: r2 = Null
    //     0x7eb388: mov             x2, NULL
    // 0x7eb38c: r1 = Null
    //     0x7eb38c: mov             x1, NULL
    // 0x7eb390: r8 = Map<String, dynamic>
    //     0x7eb390: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7eb394: r3 = Null
    //     0x7eb394: add             x3, PP, #0x40, lsl #12  ; [pp+0x407f8] Null
    //     0x7eb398: ldr             x3, [x3, #0x7f8]
    // 0x7eb39c: r0 = Map<String, dynamic>()
    //     0x7eb39c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7eb3a0: ldur            x2, [fp, #-0x60]
    // 0x7eb3a4: r1 = Null
    //     0x7eb3a4: mov             x1, NULL
    // 0x7eb3a8: r0 = UrlRedirect.fromMap()
    //     0x7eb3a8: bl              #0x7eb448  ; [package:nuonline/app/data/models/url_redirect.dart] UrlRedirect::UrlRedirect.fromMap
    // 0x7eb3ac: r1 = <UrlRedirect>
    //     0x7eb3ac: add             x1, PP, #0x36, lsl #12  ; [pp+0x36290] TypeArguments: <UrlRedirect>
    //     0x7eb3b0: ldr             x1, [x1, #0x290]
    // 0x7eb3b4: stur            x0, [fp, #-0x60]
    // 0x7eb3b8: r0 = _$SuccessImpl()
    //     0x7eb3b8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7eb3bc: mov             x1, x0
    // 0x7eb3c0: ldur            x0, [fp, #-0x60]
    // 0x7eb3c4: StoreField: r1->field_b = r0
    //     0x7eb3c4: stur            w0, [x1, #0xb]
    // 0x7eb3c8: mov             x0, x1
    // 0x7eb3cc: r0 = ReturnAsyncNotFuture()
    //     0x7eb3cc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7eb3d0: sub             SP, fp, #0x98
    // 0x7eb3d4: r0 = UrlRedirect()
    //     0x7eb3d4: bl              #0x7eb43c  ; AllocateUrlRedirectStub -> UrlRedirect (size=0x14)
    // 0x7eb3d8: mov             x2, x0
    // 0x7eb3dc: ldur            x0, [fp, #-0x18]
    // 0x7eb3e0: stur            x2, [fp, #-0x60]
    // 0x7eb3e4: StoreField: r2->field_7 = r0
    //     0x7eb3e4: stur            w0, [x2, #7]
    // 0x7eb3e8: r0 = true
    //     0x7eb3e8: add             x0, NULL, #0x20  ; true
    // 0x7eb3ec: StoreField: r2->field_f = r0
    //     0x7eb3ec: stur            w0, [x2, #0xf]
    // 0x7eb3f0: r1 = <UrlRedirect>
    //     0x7eb3f0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36290] TypeArguments: <UrlRedirect>
    //     0x7eb3f4: ldr             x1, [x1, #0x290]
    // 0x7eb3f8: r0 = _$SuccessImpl()
    //     0x7eb3f8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7eb3fc: mov             x1, x0
    // 0x7eb400: ldur            x0, [fp, #-0x60]
    // 0x7eb404: StoreField: r1->field_b = r0
    //     0x7eb404: stur            w0, [x1, #0xb]
    // 0x7eb408: mov             x0, x1
    // 0x7eb40c: r0 = ReturnAsyncNotFuture()
    //     0x7eb40c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7eb410: r0 = _Exception()
    //     0x7eb410: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0x7eb414: mov             x1, x0
    // 0x7eb418: r0 = "external url"
    //     0x7eb418: add             x0, PP, #0x40, lsl #12  ; [pp+0x40808] "external url"
    //     0x7eb41c: ldr             x0, [x0, #0x808]
    // 0x7eb420: stur            x1, [fp, #-0x60]
    // 0x7eb424: StoreField: r1->field_7 = r0
    //     0x7eb424: stur            w0, [x1, #7]
    // 0x7eb428: mov             x0, x1
    // 0x7eb42c: r0 = Throw()
    //     0x7eb42c: bl              #0xec04b8  ; ThrowStub
    // 0x7eb430: brk             #0
    // 0x7eb434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb438: b               #0x7eb280
  }
  _ normalize(/* No info */) {
    // ** addr: 0x7eb730, size: 0x40
    // 0x7eb730: EnterFrame
    //     0x7eb730: stp             fp, lr, [SP, #-0x10]!
    //     0x7eb734: mov             fp, SP
    // 0x7eb738: mov             x0, x1
    // 0x7eb73c: mov             x1, x2
    // 0x7eb740: CheckStackOverflow
    //     0x7eb740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb744: cmp             SP, x16
    //     0x7eb748: b.ls            #0x7eb768
    // 0x7eb74c: r2 = "www."
    //     0x7eb74c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40848] "www."
    //     0x7eb750: ldr             x2, [x2, #0x848]
    // 0x7eb754: r3 = ""
    //     0x7eb754: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0x7eb758: r0 = replaceFirst()
    //     0x7eb758: bl              #0x6440c0  ; [dart:core] _StringBase::replaceFirst
    // 0x7eb75c: LeaveFrame
    //     0x7eb75c: mov             SP, fp
    //     0x7eb760: ldp             fp, lr, [SP], #0x10
    // 0x7eb764: ret
    //     0x7eb764: ret             
    // 0x7eb768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eb768: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eb76c: b               #0x7eb74c
  }
}
