// lib: , url: package:nuonline/app/data/repositories/verse_repository.dart

// class id: 1050103, size: 0x8
class :: {
}

// class id: 1073, size: 0x10, field offset: 0x8
class VersesRepository extends Object {

  [closure] bool <anonymous closure>(dynamic, Verse) {
    // ** addr: 0x7bcaf0, size: 0x3c
    // 0x7bcaf0: ldr             x1, [SP, #8]
    // 0x7bcaf4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x7bcaf4: ldur            w2, [x1, #0x17]
    // 0x7bcaf8: DecompressPointer r2
    //     0x7bcaf8: add             x2, x2, HEAP, lsl #32
    // 0x7bcafc: ldr             x1, [SP]
    // 0x7bcb00: LoadField: r3 = r1->field_7
    //     0x7bcb00: ldur            x3, [x1, #7]
    // 0x7bcb04: LoadField: r1 = r2->field_f
    //     0x7bcb04: ldur            w1, [x2, #0xf]
    // 0x7bcb08: DecompressPointer r1
    //     0x7bcb08: add             x1, x1, HEAP, lsl #32
    // 0x7bcb0c: r2 = LoadInt32Instr(r1)
    //     0x7bcb0c: sbfx            x2, x1, #1, #0x1f
    //     0x7bcb10: tbz             w1, #0, #0x7bcb18
    //     0x7bcb14: ldur            x2, [x1, #7]
    // 0x7bcb18: cmp             x3, x2
    // 0x7bcb1c: r16 = true
    //     0x7bcb1c: add             x16, NULL, #0x20  ; true
    // 0x7bcb20: r17 = false
    //     0x7bcb20: add             x17, NULL, #0x30  ; false
    // 0x7bcb24: csel            x0, x16, x17, eq
    // 0x7bcb28: ret
    //     0x7bcb28: ret             
  }
  _ findByVerseId(/* No info */) async {
    // ** addr: 0x7bcb2c, size: 0x110
    // 0x7bcb2c: EnterFrame
    //     0x7bcb2c: stp             fp, lr, [SP, #-0x10]!
    //     0x7bcb30: mov             fp, SP
    // 0x7bcb34: AllocStack(0x38)
    //     0x7bcb34: sub             SP, SP, #0x38
    // 0x7bcb38: SetupParameters(VersesRepository this /* r1 => r3, fp-0x18 */)
    //     0x7bcb38: stur            NULL, [fp, #-8]
    //     0x7bcb3c: mov             x3, x1
    //     0x7bcb40: stur            x1, [fp, #-0x18]
    // 0x7bcb44: CheckStackOverflow
    //     0x7bcb44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bcb48: cmp             SP, x16
    //     0x7bcb4c: b.ls            #0x7bcc2c
    // 0x7bcb50: r0 = BoxInt64Instr(r2)
    //     0x7bcb50: sbfiz           x0, x2, #1, #0x1f
    //     0x7bcb54: cmp             x2, x0, asr #1
    //     0x7bcb58: b.eq            #0x7bcb64
    //     0x7bcb5c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7bcb60: stur            x2, [x0, #7]
    // 0x7bcb64: stur            x0, [fp, #-0x10]
    // 0x7bcb68: r1 = 1
    //     0x7bcb68: movz            x1, #0x1
    // 0x7bcb6c: r0 = AllocateContext()
    //     0x7bcb6c: bl              #0xec126c  ; AllocateContextStub
    // 0x7bcb70: mov             x1, x0
    // 0x7bcb74: ldur            x0, [fp, #-0x10]
    // 0x7bcb78: stur            x1, [fp, #-0x20]
    // 0x7bcb7c: StoreField: r1->field_f = r0
    //     0x7bcb7c: stur            w0, [x1, #0xf]
    // 0x7bcb80: InitAsync() -> Future<Verse>
    //     0x7bcb80: add             x0, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7bcb84: ldr             x0, [x0, #0x1f0]
    //     0x7bcb88: bl              #0x661298  ; InitAsyncStub
    // 0x7bcb8c: ldur            x1, [fp, #-0x18]
    // 0x7bcb90: r0 = init()
    //     0x7bcb90: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x7bcb94: mov             x1, x0
    // 0x7bcb98: stur            x1, [fp, #-0x10]
    // 0x7bcb9c: r0 = Await()
    //     0x7bcb9c: bl              #0x661044  ; AwaitStub
    // 0x7bcba0: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x7bcba0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7bcba4: ldr             x0, [x0, #0x2728]
    //     0x7bcba8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7bcbac: cmp             w0, w16
    //     0x7bcbb0: b.ne            #0x7bcbbc
    //     0x7bcbb4: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x7bcbb8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7bcbbc: r16 = <Verse>
    //     0x7bcbbc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7bcbc0: ldr             x16, [x16, #0x1f0]
    // 0x7bcbc4: stp             x0, x16, [SP, #8]
    // 0x7bcbc8: r16 = "v2_quran_verse"
    //     0x7bcbc8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x7bcbcc: ldr             x16, [x16, #0x218]
    // 0x7bcbd0: str             x16, [SP]
    // 0x7bcbd4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bcbd4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bcbd8: r0 = box()
    //     0x7bcbd8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7bcbdc: mov             x1, x0
    // 0x7bcbe0: stur            x0, [fp, #-0x10]
    // 0x7bcbe4: r0 = checkOpen()
    //     0x7bcbe4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7bcbe8: ldur            x0, [fp, #-0x10]
    // 0x7bcbec: LoadField: r1 = r0->field_1b
    //     0x7bcbec: ldur            w1, [x0, #0x1b]
    // 0x7bcbf0: DecompressPointer r1
    //     0x7bcbf0: add             x1, x1, HEAP, lsl #32
    // 0x7bcbf4: r16 = Sentinel
    //     0x7bcbf4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7bcbf8: cmp             w1, w16
    // 0x7bcbfc: b.eq            #0x7bcc34
    // 0x7bcc00: r0 = getValues()
    //     0x7bcc00: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x7bcc04: ldur            x2, [fp, #-0x20]
    // 0x7bcc08: r1 = Function '<anonymous closure>':.
    //     0x7bcc08: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b6e8] AnonymousClosure: (0x7bcaf0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByVerseId (0x7bcb2c)
    //     0x7bcc0c: ldr             x1, [x1, #0x6e8]
    // 0x7bcc10: stur            x0, [fp, #-0x10]
    // 0x7bcc14: r0 = AllocateClosure()
    //     0x7bcc14: bl              #0xec1630  ; AllocateClosureStub
    // 0x7bcc18: ldur            x1, [fp, #-0x10]
    // 0x7bcc1c: mov             x2, x0
    // 0x7bcc20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7bcc20: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7bcc24: r0 = firstWhere()
    //     0x7bcc24: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0x7bcc28: r0 = ReturnAsync()
    //     0x7bcc28: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7bcc2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bcc2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bcc30: b               #0x7bcb50
    // 0x7bcc34: r9 = keystore
    //     0x7bcc34: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7bcc38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7bcc38: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ init(/* No info */) async {
    // ** addr: 0x7bd3b0, size: 0x184
    // 0x7bd3b0: EnterFrame
    //     0x7bd3b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7bd3b4: mov             fp, SP
    // 0x7bd3b8: AllocStack(0x38)
    //     0x7bd3b8: sub             SP, SP, #0x38
    // 0x7bd3bc: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */)
    //     0x7bd3bc: stur            NULL, [fp, #-8]
    //     0x7bd3c0: stur            x1, [fp, #-0x10]
    // 0x7bd3c4: CheckStackOverflow
    //     0x7bd3c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7bd3c8: cmp             SP, x16
    //     0x7bd3cc: b.ls            #0x7bd524
    // 0x7bd3d0: InitAsync() -> Future<void?>
    //     0x7bd3d0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x7bd3d4: bl              #0x661298  ; InitAsyncStub
    // 0x7bd3d8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x7bd3d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7bd3dc: ldr             x0, [x0, #0x2728]
    //     0x7bd3e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7bd3e4: cmp             w0, w16
    //     0x7bd3e8: b.ne            #0x7bd3f4
    //     0x7bd3ec: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x7bd3f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7bd3f4: stur            x0, [fp, #-0x18]
    // 0x7bd3f8: r16 = <Verse>
    //     0x7bd3f8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7bd3fc: ldr             x16, [x16, #0x1f0]
    // 0x7bd400: stp             x0, x16, [SP, #8]
    // 0x7bd404: r16 = "v2_quran_verse"
    //     0x7bd404: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x7bd408: ldr             x16, [x16, #0x218]
    // 0x7bd40c: str             x16, [SP]
    // 0x7bd410: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bd410: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bd414: r0 = box()
    //     0x7bd414: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7bd418: mov             x1, x0
    // 0x7bd41c: stur            x0, [fp, #-0x20]
    // 0x7bd420: r0 = checkOpen()
    //     0x7bd420: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7bd424: ldur            x0, [fp, #-0x20]
    // 0x7bd428: LoadField: r1 = r0->field_1b
    //     0x7bd428: ldur            w1, [x0, #0x1b]
    // 0x7bd42c: DecompressPointer r1
    //     0x7bd42c: add             x1, x1, HEAP, lsl #32
    // 0x7bd430: r16 = Sentinel
    //     0x7bd430: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7bd434: cmp             w1, w16
    // 0x7bd438: b.eq            #0x7bd52c
    // 0x7bd43c: LoadField: r0 = r1->field_13
    //     0x7bd43c: ldur            w0, [x1, #0x13]
    // 0x7bd440: DecompressPointer r0
    //     0x7bd440: add             x0, x0, HEAP, lsl #32
    // 0x7bd444: LoadField: r1 = r0->field_1f
    //     0x7bd444: ldur            x1, [x0, #0x1f]
    // 0x7bd448: cbnz            x1, #0x7bd51c
    // 0x7bd44c: ldur            x0, [fp, #-0x10]
    // 0x7bd450: LoadField: r1 = r0->field_b
    //     0x7bd450: ldur            w1, [x0, #0xb]
    // 0x7bd454: DecompressPointer r1
    //     0x7bd454: add             x1, x1, HEAP, lsl #32
    // 0x7bd458: r16 = <List>
    //     0x7bd458: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x7bd45c: stp             x1, x16, [SP, #8]
    // 0x7bd460: r16 = "verses.json"
    //     0x7bd460: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a060] "verses.json"
    //     0x7bd464: ldr             x16, [x16, #0x60]
    // 0x7bd468: str             x16, [SP]
    // 0x7bd46c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bd46c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bd470: r0 = read()
    //     0x7bd470: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x7bd474: r1 = Function '<anonymous closure>':.
    //     0x7bd474: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b6f0] AnonymousClosure: (0x7c6aec), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init (0x7bd3b0)
    //     0x7bd478: ldr             x1, [x1, #0x6f0]
    // 0x7bd47c: r2 = Null
    //     0x7bd47c: mov             x2, NULL
    // 0x7bd480: stur            x0, [fp, #-0x20]
    // 0x7bd484: r0 = AllocateClosure()
    //     0x7bd484: bl              #0xec1630  ; AllocateClosureStub
    // 0x7bd488: r16 = <Iterable<Verse>>
    //     0x7bd488: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a070] TypeArguments: <Iterable<Verse>>
    //     0x7bd48c: ldr             x16, [x16, #0x70]
    // 0x7bd490: ldur            lr, [fp, #-0x20]
    // 0x7bd494: stp             lr, x16, [SP, #8]
    // 0x7bd498: str             x0, [SP]
    // 0x7bd49c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bd49c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bd4a0: r0 = then()
    //     0x7bd4a0: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x7bd4a4: mov             x1, x0
    // 0x7bd4a8: stur            x1, [fp, #-0x20]
    // 0x7bd4ac: r0 = Await()
    //     0x7bd4ac: bl              #0x661044  ; AwaitStub
    // 0x7bd4b0: stur            x0, [fp, #-0x10]
    // 0x7bd4b4: r16 = <Verse>
    //     0x7bd4b4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7bd4b8: ldr             x16, [x16, #0x1f0]
    // 0x7bd4bc: ldur            lr, [fp, #-0x18]
    // 0x7bd4c0: stp             lr, x16, [SP, #8]
    // 0x7bd4c4: r16 = "v2_quran_verse"
    //     0x7bd4c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x7bd4c8: ldr             x16, [x16, #0x218]
    // 0x7bd4cc: str             x16, [SP]
    // 0x7bd4d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7bd4d0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7bd4d4: r0 = box()
    //     0x7bd4d4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7bd4d8: r1 = Function '<anonymous closure>':.
    //     0x7bd4d8: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b6f8] AnonymousClosure: (0x7c6aa0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init (0x7bd3b0)
    //     0x7bd4dc: ldr             x1, [x1, #0x6f8]
    // 0x7bd4e0: r2 = Null
    //     0x7bd4e0: mov             x2, NULL
    // 0x7bd4e4: stur            x0, [fp, #-0x18]
    // 0x7bd4e8: r0 = AllocateClosure()
    //     0x7bd4e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x7bd4ec: ldur            x2, [fp, #-0x10]
    // 0x7bd4f0: mov             x3, x0
    // 0x7bd4f4: r1 = <dynamic, Verse>
    //     0x7bd4f4: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a088] TypeArguments: <dynamic, Verse>
    //     0x7bd4f8: ldr             x1, [x1, #0x88]
    // 0x7bd4fc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x7bd4fc: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x7bd500: r0 = LinkedHashMap.fromIterable()
    //     0x7bd500: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x7bd504: ldur            x1, [fp, #-0x18]
    // 0x7bd508: mov             x2, x0
    // 0x7bd50c: r0 = putAll()
    //     0x7bd50c: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x7bd510: mov             x1, x0
    // 0x7bd514: stur            x1, [fp, #-0x10]
    // 0x7bd518: r0 = Await()
    //     0x7bd518: bl              #0x661044  ; AwaitStub
    // 0x7bd51c: r0 = Null
    //     0x7bd51c: mov             x0, NULL
    // 0x7bd520: r0 = ReturnAsyncNotFuture()
    //     0x7bd520: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7bd524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7bd524: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7bd528: b               #0x7bd3d0
    // 0x7bd52c: r9 = keystore
    //     0x7bd52c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7bd530: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7bd530: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7c6aa0, size: 0x4c
    // 0x7c6aa0: EnterFrame
    //     0x7c6aa0: stp             fp, lr, [SP, #-0x10]!
    //     0x7c6aa4: mov             fp, SP
    // 0x7c6aa8: AllocStack(0x8)
    //     0x7c6aa8: sub             SP, SP, #8
    // 0x7c6aac: CheckStackOverflow
    //     0x7c6aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c6ab0: cmp             SP, x16
    //     0x7c6ab4: b.ls            #0x7c6ae4
    // 0x7c6ab8: ldr             x16, [fp, #0x10]
    // 0x7c6abc: str             x16, [SP]
    // 0x7c6ac0: r4 = 0
    //     0x7c6ac0: movz            x4, #0
    // 0x7c6ac4: ldr             x0, [SP]
    // 0x7c6ac8: r16 = UnlinkedCall_0x5f3c08
    //     0x7c6ac8: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b700] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x7c6acc: add             x16, x16, #0x700
    // 0x7c6ad0: ldp             x5, lr, [x16]
    // 0x7c6ad4: blr             lr
    // 0x7c6ad8: LeaveFrame
    //     0x7c6ad8: mov             SP, fp
    //     0x7c6adc: ldp             fp, lr, [SP], #0x10
    // 0x7c6ae0: ret
    //     0x7c6ae0: ret             
    // 0x7c6ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c6ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c6ae8: b               #0x7c6ab8
  }
  [closure] Iterable<Verse> <anonymous closure>(dynamic, List<dynamic>) {
    // ** addr: 0x7c6aec, size: 0x74
    // 0x7c6aec: EnterFrame
    //     0x7c6aec: stp             fp, lr, [SP, #-0x10]!
    //     0x7c6af0: mov             fp, SP
    // 0x7c6af4: AllocStack(0x18)
    //     0x7c6af4: sub             SP, SP, #0x18
    // 0x7c6af8: CheckStackOverflow
    //     0x7c6af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c6afc: cmp             SP, x16
    //     0x7c6b00: b.ls            #0x7c6b58
    // 0x7c6b04: r1 = Function '<anonymous closure>':.
    //     0x7c6b04: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b710] AnonymousClosure: (0x7c6b60), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init (0x7bd3b0)
    //     0x7c6b08: ldr             x1, [x1, #0x710]
    // 0x7c6b0c: r2 = Null
    //     0x7c6b0c: mov             x2, NULL
    // 0x7c6b10: r0 = AllocateClosure()
    //     0x7c6b10: bl              #0xec1630  ; AllocateClosureStub
    // 0x7c6b14: mov             x1, x0
    // 0x7c6b18: ldr             x0, [fp, #0x10]
    // 0x7c6b1c: r2 = LoadClassIdInstr(r0)
    //     0x7c6b1c: ldur            x2, [x0, #-1]
    //     0x7c6b20: ubfx            x2, x2, #0xc, #0x14
    // 0x7c6b24: r16 = <Verse>
    //     0x7c6b24: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7c6b28: ldr             x16, [x16, #0x1f0]
    // 0x7c6b2c: stp             x0, x16, [SP, #8]
    // 0x7c6b30: str             x1, [SP]
    // 0x7c6b34: mov             x0, x2
    // 0x7c6b38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7c6b38: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7c6b3c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x7c6b3c: movz            x17, #0xf28c
    //     0x7c6b40: add             lr, x0, x17
    //     0x7c6b44: ldr             lr, [x21, lr, lsl #3]
    //     0x7c6b48: blr             lr
    // 0x7c6b4c: LeaveFrame
    //     0x7c6b4c: mov             SP, fp
    //     0x7c6b50: ldp             fp, lr, [SP], #0x10
    // 0x7c6b54: ret
    //     0x7c6b54: ret             
    // 0x7c6b58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c6b58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c6b5c: b               #0x7c6b04
  }
  [closure] Verse <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7c6b60, size: 0x50
    // 0x7c6b60: EnterFrame
    //     0x7c6b60: stp             fp, lr, [SP, #-0x10]!
    //     0x7c6b64: mov             fp, SP
    // 0x7c6b68: CheckStackOverflow
    //     0x7c6b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c6b6c: cmp             SP, x16
    //     0x7c6b70: b.ls            #0x7c6ba8
    // 0x7c6b74: ldr             x0, [fp, #0x10]
    // 0x7c6b78: r2 = Null
    //     0x7c6b78: mov             x2, NULL
    // 0x7c6b7c: r1 = Null
    //     0x7c6b7c: mov             x1, NULL
    // 0x7c6b80: r8 = Map<String, dynamic>
    //     0x7c6b80: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7c6b84: r3 = Null
    //     0x7c6b84: add             x3, PP, #0x2b, lsl #12  ; [pp+0x2b718] Null
    //     0x7c6b88: ldr             x3, [x3, #0x718]
    // 0x7c6b8c: r0 = Map<String, dynamic>()
    //     0x7c6b8c: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7c6b90: ldr             x2, [fp, #0x10]
    // 0x7c6b94: r1 = Null
    //     0x7c6b94: mov             x1, NULL
    // 0x7c6b98: r0 = Verse.fromMap()
    //     0x7c6b98: bl              #0x7c6bb0  ; [package:nuonline/app/data/models/verse.dart] Verse::Verse.fromMap
    // 0x7c6b9c: LeaveFrame
    //     0x7c6b9c: mov             SP, fp
    //     0x7c6ba0: ldp             fp, lr, [SP], #0x10
    // 0x7c6ba4: ret
    //     0x7c6ba4: ret             
    // 0x7c6ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c6ba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c6bac: b               #0x7c6b74
  }
  _ bookmark(/* No info */) {
    // ** addr: 0x7e9af0, size: 0xf0
    // 0x7e9af0: EnterFrame
    //     0x7e9af0: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9af4: mov             fp, SP
    // 0x7e9af8: AllocStack(0x20)
    //     0x7e9af8: sub             SP, SP, #0x20
    // 0x7e9afc: CheckStackOverflow
    //     0x7e9afc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9b00: cmp             SP, x16
    //     0x7e9b04: b.ls            #0x7e9bd0
    // 0x7e9b08: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x7e9b08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7e9b0c: ldr             x0, [x0, #0x2728]
    //     0x7e9b10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7e9b14: cmp             w0, w16
    //     0x7e9b18: b.ne            #0x7e9b24
    //     0x7e9b1c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x7e9b20: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7e9b24: r16 = <Verse>
    //     0x7e9b24: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x7e9b28: ldr             x16, [x16, #0x1f0]
    // 0x7e9b2c: stp             x0, x16, [SP, #8]
    // 0x7e9b30: r16 = "v2_quran_verse"
    //     0x7e9b30: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x7e9b34: ldr             x16, [x16, #0x218]
    // 0x7e9b38: str             x16, [SP]
    // 0x7e9b3c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7e9b3c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7e9b40: r0 = box()
    //     0x7e9b40: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x7e9b44: mov             x1, x0
    // 0x7e9b48: stur            x0, [fp, #-8]
    // 0x7e9b4c: r0 = checkOpen()
    //     0x7e9b4c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x7e9b50: ldur            x0, [fp, #-8]
    // 0x7e9b54: LoadField: r1 = r0->field_1b
    //     0x7e9b54: ldur            w1, [x0, #0x1b]
    // 0x7e9b58: DecompressPointer r1
    //     0x7e9b58: add             x1, x1, HEAP, lsl #32
    // 0x7e9b5c: r16 = Sentinel
    //     0x7e9b5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7e9b60: cmp             w1, w16
    // 0x7e9b64: b.eq            #0x7e9bd8
    // 0x7e9b68: r0 = getValues()
    //     0x7e9b68: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x7e9b6c: r1 = Function '<anonymous closure>':.
    //     0x7e9b6c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db20] AnonymousClosure: (0x7e9c54), in [package:nuonline/app/modules/setting/controllers/setting_bookmark_controller.dart] SettingBookmarkController::onReady (0x923828)
    //     0x7e9b70: ldr             x1, [x1, #0xb20]
    // 0x7e9b74: r2 = Null
    //     0x7e9b74: mov             x2, NULL
    // 0x7e9b78: stur            x0, [fp, #-8]
    // 0x7e9b7c: r0 = AllocateClosure()
    //     0x7e9b7c: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e9b80: ldur            x1, [fp, #-8]
    // 0x7e9b84: mov             x2, x0
    // 0x7e9b88: r0 = where()
    //     0x7e9b88: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x7e9b8c: LoadField: r1 = r0->field_7
    //     0x7e9b8c: ldur            w1, [x0, #7]
    // 0x7e9b90: DecompressPointer r1
    //     0x7e9b90: add             x1, x1, HEAP, lsl #32
    // 0x7e9b94: mov             x2, x0
    // 0x7e9b98: r0 = _GrowableList.of()
    //     0x7e9b98: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x7e9b9c: r1 = Function '<anonymous closure>':.
    //     0x7e9b9c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db28] AnonymousClosure: (0x7e9be0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::bookmark (0x7e9af0)
    //     0x7e9ba0: ldr             x1, [x1, #0xb28]
    // 0x7e9ba4: r2 = Null
    //     0x7e9ba4: mov             x2, NULL
    // 0x7e9ba8: stur            x0, [fp, #-8]
    // 0x7e9bac: r0 = AllocateClosure()
    //     0x7e9bac: bl              #0xec1630  ; AllocateClosureStub
    // 0x7e9bb0: str             x0, [SP]
    // 0x7e9bb4: ldur            x1, [fp, #-8]
    // 0x7e9bb8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x7e9bb8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x7e9bbc: r0 = sort()
    //     0x7e9bbc: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x7e9bc0: ldur            x0, [fp, #-8]
    // 0x7e9bc4: LeaveFrame
    //     0x7e9bc4: mov             SP, fp
    //     0x7e9bc8: ldp             fp, lr, [SP], #0x10
    // 0x7e9bcc: ret
    //     0x7e9bcc: ret             
    // 0x7e9bd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9bd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9bd4: b               #0x7e9b08
    // 0x7e9bd8: r9 = keystore
    //     0x7e9bd8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x7e9bdc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7e9bdc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, Verse, Verse) {
    // ** addr: 0x7e9be0, size: 0x74
    // 0x7e9be0: EnterFrame
    //     0x7e9be0: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9be4: mov             fp, SP
    // 0x7e9be8: CheckStackOverflow
    //     0x7e9be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9bec: cmp             SP, x16
    //     0x7e9bf0: b.ls            #0x7e9c4c
    // 0x7e9bf4: ldr             x0, [fp, #0x10]
    // 0x7e9bf8: LoadField: r1 = r0->field_47
    //     0x7e9bf8: ldur            w1, [x0, #0x47]
    // 0x7e9bfc: DecompressPointer r1
    //     0x7e9bfc: add             x1, x1, HEAP, lsl #32
    // 0x7e9c00: ldr             x0, [fp, #0x18]
    // 0x7e9c04: LoadField: r2 = r0->field_47
    //     0x7e9c04: ldur            w2, [x0, #0x47]
    // 0x7e9c08: DecompressPointer r2
    //     0x7e9c08: add             x2, x2, HEAP, lsl #32
    // 0x7e9c0c: r0 = LoadClassIdInstr(r1)
    //     0x7e9c0c: ldur            x0, [x1, #-1]
    //     0x7e9c10: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9c14: r0 = GDT[cid_x0 + 0x138b7]()
    //     0x7e9c14: movz            x17, #0x38b7
    //     0x7e9c18: movk            x17, #0x1, lsl #16
    //     0x7e9c1c: add             lr, x0, x17
    //     0x7e9c20: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9c24: blr             lr
    // 0x7e9c28: mov             x2, x0
    // 0x7e9c2c: r0 = BoxInt64Instr(r2)
    //     0x7e9c2c: sbfiz           x0, x2, #1, #0x1f
    //     0x7e9c30: cmp             x2, x0, asr #1
    //     0x7e9c34: b.eq            #0x7e9c40
    //     0x7e9c38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7e9c3c: stur            x2, [x0, #7]
    // 0x7e9c40: LeaveFrame
    //     0x7e9c40: mov             SP, fp
    //     0x7e9c44: ldp             fp, lr, [SP], #0x10
    // 0x7e9c48: ret
    //     0x7e9c48: ret             
    // 0x7e9c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9c4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9c50: b               #0x7e9bf4
  }
  _ findBySurahId(/* No info */) async {
    // ** addr: 0x8ae18c, size: 0x11c
    // 0x8ae18c: EnterFrame
    //     0x8ae18c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ae190: mov             fp, SP
    // 0x8ae194: AllocStack(0x38)
    //     0x8ae194: sub             SP, SP, #0x38
    // 0x8ae198: SetupParameters(VersesRepository this /* r1 => r3, fp-0x18 */)
    //     0x8ae198: stur            NULL, [fp, #-8]
    //     0x8ae19c: mov             x3, x1
    //     0x8ae1a0: stur            x1, [fp, #-0x18]
    // 0x8ae1a4: CheckStackOverflow
    //     0x8ae1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ae1a8: cmp             SP, x16
    //     0x8ae1ac: b.ls            #0x8ae298
    // 0x8ae1b0: r0 = BoxInt64Instr(r2)
    //     0x8ae1b0: sbfiz           x0, x2, #1, #0x1f
    //     0x8ae1b4: cmp             x2, x0, asr #1
    //     0x8ae1b8: b.eq            #0x8ae1c4
    //     0x8ae1bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ae1c0: stur            x2, [x0, #7]
    // 0x8ae1c4: stur            x0, [fp, #-0x10]
    // 0x8ae1c8: r1 = 1
    //     0x8ae1c8: movz            x1, #0x1
    // 0x8ae1cc: r0 = AllocateContext()
    //     0x8ae1cc: bl              #0xec126c  ; AllocateContextStub
    // 0x8ae1d0: mov             x1, x0
    // 0x8ae1d4: ldur            x0, [fp, #-0x10]
    // 0x8ae1d8: stur            x1, [fp, #-0x20]
    // 0x8ae1dc: StoreField: r1->field_f = r0
    //     0x8ae1dc: stur            w0, [x1, #0xf]
    // 0x8ae1e0: InitAsync() -> Future<List<Verse>>
    //     0x8ae1e0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b4d0] TypeArguments: <List<Verse>>
    //     0x8ae1e4: ldr             x0, [x0, #0x4d0]
    //     0x8ae1e8: bl              #0x661298  ; InitAsyncStub
    // 0x8ae1ec: ldur            x1, [fp, #-0x18]
    // 0x8ae1f0: r0 = init()
    //     0x8ae1f0: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x8ae1f4: mov             x1, x0
    // 0x8ae1f8: stur            x1, [fp, #-0x10]
    // 0x8ae1fc: r0 = Await()
    //     0x8ae1fc: bl              #0x661044  ; AwaitStub
    // 0x8ae200: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8ae200: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ae204: ldr             x0, [x0, #0x2728]
    //     0x8ae208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ae20c: cmp             w0, w16
    //     0x8ae210: b.ne            #0x8ae21c
    //     0x8ae214: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8ae218: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ae21c: r16 = <Verse>
    //     0x8ae21c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8ae220: ldr             x16, [x16, #0x1f0]
    // 0x8ae224: stp             x0, x16, [SP, #8]
    // 0x8ae228: r16 = "v2_quran_verse"
    //     0x8ae228: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x8ae22c: ldr             x16, [x16, #0x218]
    // 0x8ae230: str             x16, [SP]
    // 0x8ae234: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ae234: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ae238: r0 = box()
    //     0x8ae238: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8ae23c: mov             x1, x0
    // 0x8ae240: stur            x0, [fp, #-0x10]
    // 0x8ae244: r0 = checkOpen()
    //     0x8ae244: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8ae248: ldur            x0, [fp, #-0x10]
    // 0x8ae24c: LoadField: r1 = r0->field_1b
    //     0x8ae24c: ldur            w1, [x0, #0x1b]
    // 0x8ae250: DecompressPointer r1
    //     0x8ae250: add             x1, x1, HEAP, lsl #32
    // 0x8ae254: r16 = Sentinel
    //     0x8ae254: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ae258: cmp             w1, w16
    // 0x8ae25c: b.eq            #0x8ae2a0
    // 0x8ae260: r0 = getValues()
    //     0x8ae260: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8ae264: ldur            x2, [fp, #-0x20]
    // 0x8ae268: r1 = Function '<anonymous closure>':.
    //     0x8ae268: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4b8] AnonymousClosure: (0x8ae2a8), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findBySurahId (0x8ae18c)
    //     0x8ae26c: ldr             x1, [x1, #0x4b8]
    // 0x8ae270: stur            x0, [fp, #-0x10]
    // 0x8ae274: r0 = AllocateClosure()
    //     0x8ae274: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ae278: ldur            x1, [fp, #-0x10]
    // 0x8ae27c: mov             x2, x0
    // 0x8ae280: r0 = where()
    //     0x8ae280: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x8ae284: LoadField: r1 = r0->field_7
    //     0x8ae284: ldur            w1, [x0, #7]
    // 0x8ae288: DecompressPointer r1
    //     0x8ae288: add             x1, x1, HEAP, lsl #32
    // 0x8ae28c: mov             x2, x0
    // 0x8ae290: r0 = _GrowableList.of()
    //     0x8ae290: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8ae294: r0 = ReturnAsyncNotFuture()
    //     0x8ae294: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ae298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ae298: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ae29c: b               #0x8ae1b0
    // 0x8ae2a0: r9 = keystore
    //     0x8ae2a0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8ae2a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ae2a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Verse) {
    // ** addr: 0x8ae2a8, size: 0x3c
    // 0x8ae2a8: ldr             x1, [SP, #8]
    // 0x8ae2ac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8ae2ac: ldur            w2, [x1, #0x17]
    // 0x8ae2b0: DecompressPointer r2
    //     0x8ae2b0: add             x2, x2, HEAP, lsl #32
    // 0x8ae2b4: ldr             x1, [SP]
    // 0x8ae2b8: LoadField: r3 = r1->field_33
    //     0x8ae2b8: ldur            x3, [x1, #0x33]
    // 0x8ae2bc: LoadField: r1 = r2->field_f
    //     0x8ae2bc: ldur            w1, [x2, #0xf]
    // 0x8ae2c0: DecompressPointer r1
    //     0x8ae2c0: add             x1, x1, HEAP, lsl #32
    // 0x8ae2c4: r2 = LoadInt32Instr(r1)
    //     0x8ae2c4: sbfx            x2, x1, #1, #0x1f
    //     0x8ae2c8: tbz             w1, #0, #0x8ae2d0
    //     0x8ae2cc: ldur            x2, [x1, #7]
    // 0x8ae2d0: cmp             x3, x2
    // 0x8ae2d4: r16 = true
    //     0x8ae2d4: add             x16, NULL, #0x20  ; true
    // 0x8ae2d8: r17 = false
    //     0x8ae2d8: add             x17, NULL, #0x30  ; false
    // 0x8ae2dc: csel            x0, x16, x17, eq
    // 0x8ae2e0: ret
    //     0x8ae2e0: ret             
  }
  _ findByVerseNumber(/* No info */) async {
    // ** addr: 0x8aed38, size: 0x130
    // 0x8aed38: EnterFrame
    //     0x8aed38: stp             fp, lr, [SP, #-0x10]!
    //     0x8aed3c: mov             fp, SP
    // 0x8aed40: AllocStack(0x40)
    //     0x8aed40: sub             SP, SP, #0x40
    // 0x8aed44: SetupParameters(VersesRepository this /* r1 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x8aed44: stur            NULL, [fp, #-8]
    //     0x8aed48: mov             x4, x1
    //     0x8aed4c: stur            x1, [fp, #-0x18]
    //     0x8aed50: stur            x3, [fp, #-0x20]
    // 0x8aed54: CheckStackOverflow
    //     0x8aed54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aed58: cmp             SP, x16
    //     0x8aed5c: b.ls            #0x8aee58
    // 0x8aed60: r0 = BoxInt64Instr(r2)
    //     0x8aed60: sbfiz           x0, x2, #1, #0x1f
    //     0x8aed64: cmp             x2, x0, asr #1
    //     0x8aed68: b.eq            #0x8aed74
    //     0x8aed6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aed70: stur            x2, [x0, #7]
    // 0x8aed74: stur            x0, [fp, #-0x10]
    // 0x8aed78: r1 = 2
    //     0x8aed78: movz            x1, #0x2
    // 0x8aed7c: r0 = AllocateContext()
    //     0x8aed7c: bl              #0xec126c  ; AllocateContextStub
    // 0x8aed80: mov             x2, x0
    // 0x8aed84: ldur            x0, [fp, #-0x10]
    // 0x8aed88: stur            x2, [fp, #-0x28]
    // 0x8aed8c: StoreField: r2->field_f = r0
    //     0x8aed8c: stur            w0, [x2, #0xf]
    // 0x8aed90: ldur            x3, [fp, #-0x20]
    // 0x8aed94: r0 = BoxInt64Instr(r3)
    //     0x8aed94: sbfiz           x0, x3, #1, #0x1f
    //     0x8aed98: cmp             x3, x0, asr #1
    //     0x8aed9c: b.eq            #0x8aeda8
    //     0x8aeda0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aeda4: stur            x3, [x0, #7]
    // 0x8aeda8: StoreField: r2->field_13 = r0
    //     0x8aeda8: stur            w0, [x2, #0x13]
    // 0x8aedac: InitAsync() -> Future<Verse>
    //     0x8aedac: add             x0, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8aedb0: ldr             x0, [x0, #0x1f0]
    //     0x8aedb4: bl              #0x661298  ; InitAsyncStub
    // 0x8aedb8: ldur            x1, [fp, #-0x18]
    // 0x8aedbc: r0 = init()
    //     0x8aedbc: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x8aedc0: mov             x1, x0
    // 0x8aedc4: stur            x1, [fp, #-0x10]
    // 0x8aedc8: r0 = Await()
    //     0x8aedc8: bl              #0x661044  ; AwaitStub
    // 0x8aedcc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aedcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aedd0: ldr             x0, [x0, #0x2728]
    //     0x8aedd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aedd8: cmp             w0, w16
    //     0x8aeddc: b.ne            #0x8aede8
    //     0x8aede0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aede4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aede8: r16 = <Verse>
    //     0x8aede8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8aedec: ldr             x16, [x16, #0x1f0]
    // 0x8aedf0: stp             x0, x16, [SP, #8]
    // 0x8aedf4: r16 = "v2_quran_verse"
    //     0x8aedf4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x8aedf8: ldr             x16, [x16, #0x218]
    // 0x8aedfc: str             x16, [SP]
    // 0x8aee00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aee00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aee04: r0 = box()
    //     0x8aee04: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aee08: mov             x1, x0
    // 0x8aee0c: stur            x0, [fp, #-0x10]
    // 0x8aee10: r0 = checkOpen()
    //     0x8aee10: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aee14: ldur            x0, [fp, #-0x10]
    // 0x8aee18: LoadField: r1 = r0->field_1b
    //     0x8aee18: ldur            w1, [x0, #0x1b]
    // 0x8aee1c: DecompressPointer r1
    //     0x8aee1c: add             x1, x1, HEAP, lsl #32
    // 0x8aee20: r16 = Sentinel
    //     0x8aee20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aee24: cmp             w1, w16
    // 0x8aee28: b.eq            #0x8aee60
    // 0x8aee2c: r0 = getValues()
    //     0x8aee2c: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8aee30: ldur            x2, [fp, #-0x28]
    // 0x8aee34: r1 = Function '<anonymous closure>':.
    //     0x8aee34: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c398] AnonymousClosure: (0x8aee68), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getVerse (0x8aeed0)
    //     0x8aee38: ldr             x1, [x1, #0x398]
    // 0x8aee3c: stur            x0, [fp, #-0x10]
    // 0x8aee40: r0 = AllocateClosure()
    //     0x8aee40: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aee44: ldur            x1, [fp, #-0x10]
    // 0x8aee48: mov             x2, x0
    // 0x8aee4c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8aee4c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8aee50: r0 = firstWhere()
    //     0x8aee50: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0x8aee54: r0 = ReturnAsync()
    //     0x8aee54: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8aee58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aee58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aee5c: b               #0x8aed60
    // 0x8aee60: r9 = keystore
    //     0x8aee60: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8aee64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aee64: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Verse) {
    // ** addr: 0x8aee68, size: 0x68
    // 0x8aee68: ldr             x1, [SP, #8]
    // 0x8aee6c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8aee6c: ldur            w2, [x1, #0x17]
    // 0x8aee70: DecompressPointer r2
    //     0x8aee70: add             x2, x2, HEAP, lsl #32
    // 0x8aee74: ldr             x1, [SP]
    // 0x8aee78: LoadField: r3 = r1->field_33
    //     0x8aee78: ldur            x3, [x1, #0x33]
    // 0x8aee7c: LoadField: r4 = r2->field_f
    //     0x8aee7c: ldur            w4, [x2, #0xf]
    // 0x8aee80: DecompressPointer r4
    //     0x8aee80: add             x4, x4, HEAP, lsl #32
    // 0x8aee84: r5 = LoadInt32Instr(r4)
    //     0x8aee84: sbfx            x5, x4, #1, #0x1f
    //     0x8aee88: tbz             w4, #0, #0x8aee90
    //     0x8aee8c: ldur            x5, [x4, #7]
    // 0x8aee90: cmp             x3, x5
    // 0x8aee94: b.ne            #0x8aeec8
    // 0x8aee98: ArrayLoad: r3 = r1[0]  ; List_8
    //     0x8aee98: ldur            x3, [x1, #0x17]
    // 0x8aee9c: LoadField: r1 = r2->field_13
    //     0x8aee9c: ldur            w1, [x2, #0x13]
    // 0x8aeea0: DecompressPointer r1
    //     0x8aeea0: add             x1, x1, HEAP, lsl #32
    // 0x8aeea4: r2 = LoadInt32Instr(r1)
    //     0x8aeea4: sbfx            x2, x1, #1, #0x1f
    //     0x8aeea8: tbz             w1, #0, #0x8aeeb0
    //     0x8aeeac: ldur            x2, [x1, #7]
    // 0x8aeeb0: cmp             x3, x2
    // 0x8aeeb4: r16 = true
    //     0x8aeeb4: add             x16, NULL, #0x20  ; true
    // 0x8aeeb8: r17 = false
    //     0x8aeeb8: add             x17, NULL, #0x30  ; false
    // 0x8aeebc: csel            x1, x16, x17, eq
    // 0x8aeec0: mov             x0, x1
    // 0x8aeec4: b               #0x8aeecc
    // 0x8aeec8: r0 = false
    //     0x8aeec8: add             x0, NULL, #0x30  ; false
    // 0x8aeecc: ret
    //     0x8aeecc: ret             
  }
  _ getVerse(/* No info */) async {
    // ** addr: 0x8aeed0, size: 0x130
    // 0x8aeed0: EnterFrame
    //     0x8aeed0: stp             fp, lr, [SP, #-0x10]!
    //     0x8aeed4: mov             fp, SP
    // 0x8aeed8: AllocStack(0x40)
    //     0x8aeed8: sub             SP, SP, #0x40
    // 0x8aeedc: SetupParameters(VersesRepository this /* r1 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x8aeedc: stur            NULL, [fp, #-8]
    //     0x8aeee0: mov             x4, x1
    //     0x8aeee4: stur            x1, [fp, #-0x18]
    //     0x8aeee8: stur            x3, [fp, #-0x20]
    // 0x8aeeec: CheckStackOverflow
    //     0x8aeeec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aeef0: cmp             SP, x16
    //     0x8aeef4: b.ls            #0x8aeff0
    // 0x8aeef8: r0 = BoxInt64Instr(r2)
    //     0x8aeef8: sbfiz           x0, x2, #1, #0x1f
    //     0x8aeefc: cmp             x2, x0, asr #1
    //     0x8aef00: b.eq            #0x8aef0c
    //     0x8aef04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aef08: stur            x2, [x0, #7]
    // 0x8aef0c: stur            x0, [fp, #-0x10]
    // 0x8aef10: r1 = 2
    //     0x8aef10: movz            x1, #0x2
    // 0x8aef14: r0 = AllocateContext()
    //     0x8aef14: bl              #0xec126c  ; AllocateContextStub
    // 0x8aef18: mov             x2, x0
    // 0x8aef1c: ldur            x0, [fp, #-0x10]
    // 0x8aef20: stur            x2, [fp, #-0x28]
    // 0x8aef24: StoreField: r2->field_f = r0
    //     0x8aef24: stur            w0, [x2, #0xf]
    // 0x8aef28: ldur            x3, [fp, #-0x20]
    // 0x8aef2c: r0 = BoxInt64Instr(r3)
    //     0x8aef2c: sbfiz           x0, x3, #1, #0x1f
    //     0x8aef30: cmp             x3, x0, asr #1
    //     0x8aef34: b.eq            #0x8aef40
    //     0x8aef38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aef3c: stur            x3, [x0, #7]
    // 0x8aef40: StoreField: r2->field_13 = r0
    //     0x8aef40: stur            w0, [x2, #0x13]
    // 0x8aef44: InitAsync() -> Future<Verse>
    //     0x8aef44: add             x0, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8aef48: ldr             x0, [x0, #0x1f0]
    //     0x8aef4c: bl              #0x661298  ; InitAsyncStub
    // 0x8aef50: ldur            x1, [fp, #-0x18]
    // 0x8aef54: r0 = init()
    //     0x8aef54: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x8aef58: mov             x1, x0
    // 0x8aef5c: stur            x1, [fp, #-0x10]
    // 0x8aef60: r0 = Await()
    //     0x8aef60: bl              #0x661044  ; AwaitStub
    // 0x8aef64: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8aef64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aef68: ldr             x0, [x0, #0x2728]
    //     0x8aef6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aef70: cmp             w0, w16
    //     0x8aef74: b.ne            #0x8aef80
    //     0x8aef78: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8aef7c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8aef80: r16 = <Verse>
    //     0x8aef80: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8aef84: ldr             x16, [x16, #0x1f0]
    // 0x8aef88: stp             x0, x16, [SP, #8]
    // 0x8aef8c: r16 = "v2_quran_verse"
    //     0x8aef8c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x8aef90: ldr             x16, [x16, #0x218]
    // 0x8aef94: str             x16, [SP]
    // 0x8aef98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aef98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aef9c: r0 = box()
    //     0x8aef9c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8aefa0: mov             x1, x0
    // 0x8aefa4: stur            x0, [fp, #-0x10]
    // 0x8aefa8: r0 = checkOpen()
    //     0x8aefa8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8aefac: ldur            x0, [fp, #-0x10]
    // 0x8aefb0: LoadField: r1 = r0->field_1b
    //     0x8aefb0: ldur            w1, [x0, #0x1b]
    // 0x8aefb4: DecompressPointer r1
    //     0x8aefb4: add             x1, x1, HEAP, lsl #32
    // 0x8aefb8: r16 = Sentinel
    //     0x8aefb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aefbc: cmp             w1, w16
    // 0x8aefc0: b.eq            #0x8aeff8
    // 0x8aefc4: r0 = getValues()
    //     0x8aefc4: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8aefc8: ldur            x2, [fp, #-0x28]
    // 0x8aefcc: r1 = Function '<anonymous closure>':.
    //     0x8aefcc: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b760] AnonymousClosure: (0x8aee68), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getVerse (0x8aeed0)
    //     0x8aefd0: ldr             x1, [x1, #0x760]
    // 0x8aefd4: stur            x0, [fp, #-0x10]
    // 0x8aefd8: r0 = AllocateClosure()
    //     0x8aefd8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aefdc: ldur            x1, [fp, #-0x10]
    // 0x8aefe0: mov             x2, x0
    // 0x8aefe4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8aefe4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8aefe8: r0 = firstWhere()
    //     0x8aefe8: bl              #0x7f0038  ; [dart:core] Iterable::firstWhere
    // 0x8aefec: r0 = ReturnAsync()
    //     0x8aefec: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8aeff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aeff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aeff4: b               #0x8aeef8
    // 0x8aeff8: r9 = keystore
    //     0x8aeff8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8aeffc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aeffc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ allPage(/* No info */) async {
    // ** addr: 0x8c291c, size: 0x22c
    // 0x8c291c: EnterFrame
    //     0x8c291c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c2920: mov             fp, SP
    // 0x8c2924: AllocStack(0x40)
    //     0x8c2924: sub             SP, SP, #0x40
    // 0x8c2928: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */)
    //     0x8c2928: stur            NULL, [fp, #-8]
    //     0x8c292c: stur            x1, [fp, #-0x10]
    // 0x8c2930: CheckStackOverflow
    //     0x8c2930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c2934: cmp             SP, x16
    //     0x8c2938: b.ls            #0x8c2b30
    // 0x8c293c: InitAsync() -> Future<List<Verse>>
    //     0x8c293c: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b4d0] TypeArguments: <List<Verse>>
    //     0x8c2940: ldr             x0, [x0, #0x4d0]
    //     0x8c2944: bl              #0x661298  ; InitAsyncStub
    // 0x8c2948: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8c2948: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8c294c: ldr             x0, [x0, #0x2728]
    //     0x8c2950: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8c2954: cmp             w0, w16
    //     0x8c2958: b.ne            #0x8c2964
    //     0x8c295c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8c2960: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8c2964: stur            x0, [fp, #-0x18]
    // 0x8c2968: r16 = <Verse>
    //     0x8c2968: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8c296c: ldr             x16, [x16, #0x1f0]
    // 0x8c2970: stp             x0, x16, [SP, #8]
    // 0x8c2974: r16 = "v2_quran_verse_page_2"
    //     0x8c2974: add             x16, PP, #0xf, lsl #12  ; [pp+0xf220] "v2_quran_verse_page_2"
    //     0x8c2978: ldr             x16, [x16, #0x220]
    // 0x8c297c: str             x16, [SP]
    // 0x8c2980: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c2980: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c2984: r0 = box()
    //     0x8c2984: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c2988: mov             x1, x0
    // 0x8c298c: stur            x0, [fp, #-0x20]
    // 0x8c2990: r0 = checkOpen()
    //     0x8c2990: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c2994: ldur            x0, [fp, #-0x20]
    // 0x8c2998: LoadField: r1 = r0->field_1b
    //     0x8c2998: ldur            w1, [x0, #0x1b]
    // 0x8c299c: DecompressPointer r1
    //     0x8c299c: add             x1, x1, HEAP, lsl #32
    // 0x8c29a0: r16 = Sentinel
    //     0x8c29a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c29a4: cmp             w1, w16
    // 0x8c29a8: b.eq            #0x8c2b38
    // 0x8c29ac: LoadField: r0 = r1->field_13
    //     0x8c29ac: ldur            w0, [x1, #0x13]
    // 0x8c29b0: DecompressPointer r0
    //     0x8c29b0: add             x0, x0, HEAP, lsl #32
    // 0x8c29b4: LoadField: r1 = r0->field_1f
    //     0x8c29b4: ldur            x1, [x0, #0x1f]
    // 0x8c29b8: cbnz            x1, #0x8c2ad0
    // 0x8c29bc: ldur            x0, [fp, #-0x10]
    // 0x8c29c0: LoadField: r1 = r0->field_b
    //     0x8c29c0: ldur            w1, [x0, #0xb]
    // 0x8c29c4: DecompressPointer r1
    //     0x8c29c4: add             x1, x1, HEAP, lsl #32
    // 0x8c29c8: r16 = <List>
    //     0x8c29c8: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x8c29cc: stp             x1, x16, [SP, #8]
    // 0x8c29d0: r16 = "number.json"
    //     0x8c29d0: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a098] "number.json"
    //     0x8c29d4: ldr             x16, [x16, #0x98]
    // 0x8c29d8: str             x16, [SP]
    // 0x8c29dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c29dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c29e0: r0 = read()
    //     0x8c29e0: bl              #0x7c6988  ; [package:nuonline/services/json_service.dart] JsonService::read
    // 0x8c29e4: mov             x1, x0
    // 0x8c29e8: stur            x1, [fp, #-0x20]
    // 0x8c29ec: r0 = Await()
    //     0x8c29ec: bl              #0x661044  ; AwaitStub
    // 0x8c29f0: r1 = Function '<anonymous closure>':.
    //     0x8c29f0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d760] AnonymousClosure: (0x8c2bfc), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::allPage (0x8c291c)
    //     0x8c29f4: ldr             x1, [x1, #0x760]
    // 0x8c29f8: r2 = Null
    //     0x8c29f8: mov             x2, NULL
    // 0x8c29fc: stur            x0, [fp, #-0x20]
    // 0x8c2a00: r0 = AllocateClosure()
    //     0x8c2a00: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c2a04: mov             x1, x0
    // 0x8c2a08: ldur            x0, [fp, #-0x20]
    // 0x8c2a0c: r2 = LoadClassIdInstr(r0)
    //     0x8c2a0c: ldur            x2, [x0, #-1]
    //     0x8c2a10: ubfx            x2, x2, #0xc, #0x14
    // 0x8c2a14: r16 = <Verse>
    //     0x8c2a14: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8c2a18: ldr             x16, [x16, #0x1f0]
    // 0x8c2a1c: stp             x0, x16, [SP, #8]
    // 0x8c2a20: str             x1, [SP]
    // 0x8c2a24: mov             x0, x2
    // 0x8c2a28: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c2a28: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c2a2c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x8c2a2c: movz            x17, #0xf28c
    //     0x8c2a30: add             lr, x0, x17
    //     0x8c2a34: ldr             lr, [x21, lr, lsl #3]
    //     0x8c2a38: blr             lr
    // 0x8c2a3c: r1 = LoadClassIdInstr(r0)
    //     0x8c2a3c: ldur            x1, [x0, #-1]
    //     0x8c2a40: ubfx            x1, x1, #0xc, #0x14
    // 0x8c2a44: mov             x16, x0
    // 0x8c2a48: mov             x0, x1
    // 0x8c2a4c: mov             x1, x16
    // 0x8c2a50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8c2a50: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8c2a54: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8c2a54: movz            x17, #0xd889
    //     0x8c2a58: add             lr, x0, x17
    //     0x8c2a5c: ldr             lr, [x21, lr, lsl #3]
    //     0x8c2a60: blr             lr
    // 0x8c2a64: stur            x0, [fp, #-0x20]
    // 0x8c2a68: r16 = <Verse>
    //     0x8c2a68: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8c2a6c: ldr             x16, [x16, #0x1f0]
    // 0x8c2a70: ldur            lr, [fp, #-0x18]
    // 0x8c2a74: stp             lr, x16, [SP, #8]
    // 0x8c2a78: r16 = "v2_quran_verse_page_2"
    //     0x8c2a78: add             x16, PP, #0xf, lsl #12  ; [pp+0xf220] "v2_quran_verse_page_2"
    //     0x8c2a7c: ldr             x16, [x16, #0x220]
    // 0x8c2a80: str             x16, [SP]
    // 0x8c2a84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c2a84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c2a88: r0 = box()
    //     0x8c2a88: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c2a8c: r1 = Function '<anonymous closure>':.
    //     0x8c2a8c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d768] AnonymousClosure: (0x8c2bb0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::allPage (0x8c291c)
    //     0x8c2a90: ldr             x1, [x1, #0x768]
    // 0x8c2a94: r2 = Null
    //     0x8c2a94: mov             x2, NULL
    // 0x8c2a98: stur            x0, [fp, #-0x28]
    // 0x8c2a9c: r0 = AllocateClosure()
    //     0x8c2a9c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8c2aa0: ldur            x2, [fp, #-0x20]
    // 0x8c2aa4: mov             x3, x0
    // 0x8c2aa8: r1 = <dynamic, Verse>
    //     0x8c2aa8: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1a088] TypeArguments: <dynamic, Verse>
    //     0x8c2aac: ldr             x1, [x1, #0x88]
    // 0x8c2ab0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8c2ab0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8c2ab4: r0 = LinkedHashMap.fromIterable()
    //     0x8c2ab4: bl              #0x7c66bc  ; [dart:collection] LinkedHashMap::LinkedHashMap.fromIterable
    // 0x8c2ab8: ldur            x1, [fp, #-0x28]
    // 0x8c2abc: mov             x2, x0
    // 0x8c2ac0: r0 = putAll()
    //     0x8c2ac0: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0x8c2ac4: mov             x1, x0
    // 0x8c2ac8: stur            x1, [fp, #-0x20]
    // 0x8c2acc: r0 = Await()
    //     0x8c2acc: bl              #0x661044  ; AwaitStub
    // 0x8c2ad0: r16 = <Verse>
    //     0x8c2ad0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x8c2ad4: ldr             x16, [x16, #0x1f0]
    // 0x8c2ad8: ldur            lr, [fp, #-0x18]
    // 0x8c2adc: stp             lr, x16, [SP, #8]
    // 0x8c2ae0: r16 = "v2_quran_verse_page_2"
    //     0x8c2ae0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf220] "v2_quran_verse_page_2"
    //     0x8c2ae4: ldr             x16, [x16, #0x220]
    // 0x8c2ae8: str             x16, [SP]
    // 0x8c2aec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c2aec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c2af0: r0 = box()
    //     0x8c2af0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8c2af4: mov             x1, x0
    // 0x8c2af8: stur            x0, [fp, #-0x10]
    // 0x8c2afc: r0 = checkOpen()
    //     0x8c2afc: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8c2b00: ldur            x0, [fp, #-0x10]
    // 0x8c2b04: LoadField: r1 = r0->field_1b
    //     0x8c2b04: ldur            w1, [x0, #0x1b]
    // 0x8c2b08: DecompressPointer r1
    //     0x8c2b08: add             x1, x1, HEAP, lsl #32
    // 0x8c2b0c: r16 = Sentinel
    //     0x8c2b0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8c2b10: cmp             w1, w16
    // 0x8c2b14: b.eq            #0x8c2b40
    // 0x8c2b18: r0 = getValues()
    //     0x8c2b18: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x8c2b1c: LoadField: r1 = r0->field_7
    //     0x8c2b1c: ldur            w1, [x0, #7]
    // 0x8c2b20: DecompressPointer r1
    //     0x8c2b20: add             x1, x1, HEAP, lsl #32
    // 0x8c2b24: mov             x2, x0
    // 0x8c2b28: r0 = _GrowableList.of()
    //     0x8c2b28: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8c2b2c: r0 = ReturnAsyncNotFuture()
    //     0x8c2b2c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8c2b30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c2b30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c2b34: b               #0x8c293c
    // 0x8c2b38: r9 = keystore
    //     0x8c2b38: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c2b3c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c2b3c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8c2b40: r9 = keystore
    //     0x8c2b40: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8c2b44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8c2b44: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8c2bb0, size: 0x4c
    // 0x8c2bb0: EnterFrame
    //     0x8c2bb0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c2bb4: mov             fp, SP
    // 0x8c2bb8: AllocStack(0x8)
    //     0x8c2bb8: sub             SP, SP, #8
    // 0x8c2bbc: CheckStackOverflow
    //     0x8c2bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c2bc0: cmp             SP, x16
    //     0x8c2bc4: b.ls            #0x8c2bf4
    // 0x8c2bc8: ldr             x16, [fp, #0x10]
    // 0x8c2bcc: str             x16, [SP]
    // 0x8c2bd0: r4 = 0
    //     0x8c2bd0: movz            x4, #0
    // 0x8c2bd4: ldr             x0, [SP]
    // 0x8c2bd8: r16 = UnlinkedCall_0x5f3c08
    //     0x8c2bd8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d770] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8c2bdc: add             x16, x16, #0x770
    // 0x8c2be0: ldp             x5, lr, [x16]
    // 0x8c2be4: blr             lr
    // 0x8c2be8: LeaveFrame
    //     0x8c2be8: mov             SP, fp
    //     0x8c2bec: ldp             fp, lr, [SP], #0x10
    // 0x8c2bf0: ret
    //     0x8c2bf0: ret             
    // 0x8c2bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c2bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c2bf8: b               #0x8c2bc8
  }
  [closure] Verse <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8c2bfc, size: 0xa0
    // 0x8c2bfc: EnterFrame
    //     0x8c2bfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c2c00: mov             fp, SP
    // 0x8c2c04: AllocStack(0x8)
    //     0x8c2c04: sub             SP, SP, #8
    // 0x8c2c08: CheckStackOverflow
    //     0x8c2c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c2c0c: cmp             SP, x16
    //     0x8c2c10: b.ls            #0x8c2c94
    // 0x8c2c14: ldr             x0, [fp, #0x10]
    // 0x8c2c18: r2 = Null
    //     0x8c2c18: mov             x2, NULL
    // 0x8c2c1c: r1 = Null
    //     0x8c2c1c: mov             x1, NULL
    // 0x8c2c20: r4 = 60
    //     0x8c2c20: movz            x4, #0x3c
    // 0x8c2c24: branchIfSmi(r0, 0x8c2c30)
    //     0x8c2c24: tbz             w0, #0, #0x8c2c30
    // 0x8c2c28: r4 = LoadClassIdInstr(r0)
    //     0x8c2c28: ldur            x4, [x0, #-1]
    //     0x8c2c2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c2c30: sub             x4, x4, #0x5e
    // 0x8c2c34: cmp             x4, #1
    // 0x8c2c38: b.ls            #0x8c2c4c
    // 0x8c2c3c: r8 = String
    //     0x8c2c3c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8c2c40: r3 = Null
    //     0x8c2c40: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d780] Null
    //     0x8c2c44: ldr             x3, [x3, #0x780]
    // 0x8c2c48: r0 = String()
    //     0x8c2c48: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8c2c4c: ldr             x1, [fp, #0x10]
    // 0x8c2c50: r0 = StringExtension.decryptVerse()
    //     0x8c2c50: bl              #0x8c2c9c  ; [package:nuonline/common/extensions/string_extension.dart] ::StringExtension.decryptVerse
    // 0x8c2c54: mov             x1, x0
    // 0x8c2c58: r0 = jsonDecode()
    //     0x8c2c58: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x8c2c5c: mov             x3, x0
    // 0x8c2c60: r2 = Null
    //     0x8c2c60: mov             x2, NULL
    // 0x8c2c64: r1 = Null
    //     0x8c2c64: mov             x1, NULL
    // 0x8c2c68: stur            x3, [fp, #-8]
    // 0x8c2c6c: r8 = Map<String, dynamic>
    //     0x8c2c6c: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x8c2c70: r3 = Null
    //     0x8c2c70: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d790] Null
    //     0x8c2c74: ldr             x3, [x3, #0x790]
    // 0x8c2c78: r0 = Map<String, dynamic>()
    //     0x8c2c78: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x8c2c7c: ldur            x2, [fp, #-8]
    // 0x8c2c80: r1 = Null
    //     0x8c2c80: mov             x1, NULL
    // 0x8c2c84: r0 = Verse.fromMap()
    //     0x8c2c84: bl              #0x7c6bb0  ; [package:nuonline/app/data/models/verse.dart] Verse::Verse.fromMap
    // 0x8c2c88: LeaveFrame
    //     0x8c2c88: mov             SP, fp
    //     0x8c2c8c: ldp             fp, lr, [SP], #0x10
    // 0x8c2c90: ret
    //     0x8c2c90: ret             
    // 0x8c2c94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c2c94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c2c98: b               #0x8c2c14
  }
  _ getLastReadStream(/* No info */) {
    // ** addr: 0x8e93a4, size: 0xb4
    // 0x8e93a4: EnterFrame
    //     0x8e93a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8e93a8: mov             fp, SP
    // 0x8e93ac: AllocStack(0x20)
    //     0x8e93ac: sub             SP, SP, #0x20
    // 0x8e93b0: CheckStackOverflow
    //     0x8e93b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e93b4: cmp             SP, x16
    //     0x8e93b8: b.ls            #0x8e9450
    // 0x8e93bc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8e93bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e93c0: ldr             x0, [x0, #0x2728]
    //     0x8e93c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e93c8: cmp             w0, w16
    //     0x8e93cc: b.ne            #0x8e93d8
    //     0x8e93d0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8e93d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8e93d8: r16 = <VerseBookmark>
    //     0x8e93d8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0x8e93dc: ldr             x16, [x16, #0x200]
    // 0x8e93e0: stp             x0, x16, [SP, #8]
    // 0x8e93e4: r16 = "v2_quran_last_read"
    //     0x8e93e4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf228] "v2_quran_last_read"
    //     0x8e93e8: ldr             x16, [x16, #0x228]
    // 0x8e93ec: str             x16, [SP]
    // 0x8e93f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e93f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e93f4: r0 = box()
    //     0x8e93f4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8e93f8: r16 = "current"
    //     0x8e93f8: add             x16, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0x8e93fc: ldr             x16, [x16, #0x110]
    // 0x8e9400: str             x16, [SP]
    // 0x8e9404: mov             x1, x0
    // 0x8e9408: r4 = const [0, 0x2, 0x1, 0x1, key, 0x1, null]
    //     0x8e9408: add             x4, PP, #8, lsl #12  ; [pp+0x8078] List(7) [0, 0x2, 0x1, 0x1, "key", 0x1, Null]
    //     0x8e940c: ldr             x4, [x4, #0x78]
    // 0x8e9410: r0 = watch()
    //     0x8e9410: bl              #0x836368  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::watch
    // 0x8e9414: r1 = Function '<anonymous closure>':.
    //     0x8e9414: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d7a0] AnonymousClosure: (0x8e94c0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getLastReadStream (0x8e93a4)
    //     0x8e9418: ldr             x1, [x1, #0x7a0]
    // 0x8e941c: r2 = Null
    //     0x8e941c: mov             x2, NULL
    // 0x8e9420: stur            x0, [fp, #-8]
    // 0x8e9424: r0 = AllocateClosure()
    //     0x8e9424: bl              #0xec1630  ; AllocateClosureStub
    // 0x8e9428: r16 = <VerseBookmark?>
    //     0x8e9428: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b9e8] TypeArguments: <VerseBookmark?>
    //     0x8e942c: ldr             x16, [x16, #0x9e8]
    // 0x8e9430: ldur            lr, [fp, #-8]
    // 0x8e9434: stp             lr, x16, [SP, #8]
    // 0x8e9438: str             x0, [SP]
    // 0x8e943c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8e943c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8e9440: r0 = map()
    //     0x8e9440: bl              #0x68d014  ; [dart:async] Stream::map
    // 0x8e9444: LeaveFrame
    //     0x8e9444: mov             SP, fp
    //     0x8e9448: ldp             fp, lr, [SP], #0x10
    // 0x8e944c: ret
    //     0x8e944c: ret             
    // 0x8e9450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e9450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e9454: b               #0x8e93bc
  }
  [closure] VerseBookmark? <anonymous closure>(dynamic, BoxEvent) {
    // ** addr: 0x8e94c0, size: 0x64
    // 0x8e94c0: EnterFrame
    //     0x8e94c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8e94c4: mov             fp, SP
    // 0x8e94c8: AllocStack(0x8)
    //     0x8e94c8: sub             SP, SP, #8
    // 0x8e94cc: ldr             x0, [fp, #0x10]
    // 0x8e94d0: LoadField: r3 = r0->field_b
    //     0x8e94d0: ldur            w3, [x0, #0xb]
    // 0x8e94d4: DecompressPointer r3
    //     0x8e94d4: add             x3, x3, HEAP, lsl #32
    // 0x8e94d8: mov             x0, x3
    // 0x8e94dc: stur            x3, [fp, #-8]
    // 0x8e94e0: r2 = Null
    //     0x8e94e0: mov             x2, NULL
    // 0x8e94e4: r1 = Null
    //     0x8e94e4: mov             x1, NULL
    // 0x8e94e8: r4 = 60
    //     0x8e94e8: movz            x4, #0x3c
    // 0x8e94ec: branchIfSmi(r0, 0x8e94f8)
    //     0x8e94ec: tbz             w0, #0, #0x8e94f8
    // 0x8e94f0: r4 = LoadClassIdInstr(r0)
    //     0x8e94f0: ldur            x4, [x0, #-1]
    //     0x8e94f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8e94f8: cmp             x4, #0x45d
    // 0x8e94fc: b.eq            #0x8e9514
    // 0x8e9500: r8 = VerseBookmark?
    //     0x8e9500: add             x8, PP, #0x3d, lsl #12  ; [pp+0x3d7a8] Type: VerseBookmark?
    //     0x8e9504: ldr             x8, [x8, #0x7a8]
    // 0x8e9508: r3 = Null
    //     0x8e9508: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3d7b0] Null
    //     0x8e950c: ldr             x3, [x3, #0x7b0]
    // 0x8e9510: r0 = VerseBookmark?()
    //     0x8e9510: bl              #0x83bb8c  ; IsType_VerseBookmark?_Stub
    // 0x8e9514: ldur            x0, [fp, #-8]
    // 0x8e9518: LeaveFrame
    //     0x8e9518: mov             SP, fp
    //     0x8e951c: ldp             fp, lr, [SP], #0x10
    // 0x8e9520: ret
    //     0x8e9520: ret             
  }
  _ getLastRead(/* No info */) async {
    // ** addr: 0x8e9524, size: 0x6c
    // 0x8e9524: EnterFrame
    //     0x8e9524: stp             fp, lr, [SP, #-0x10]!
    //     0x8e9528: mov             fp, SP
    // 0x8e952c: AllocStack(0x18)
    //     0x8e952c: sub             SP, SP, #0x18
    // 0x8e9530: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */)
    //     0x8e9530: stur            NULL, [fp, #-8]
    //     0x8e9534: stur            x1, [fp, #-0x10]
    // 0x8e9538: CheckStackOverflow
    //     0x8e9538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e953c: cmp             SP, x16
    //     0x8e9540: b.ls            #0x8e9588
    // 0x8e9544: InitAsync() -> Future<VerseBookmark?>
    //     0x8e9544: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b9e8] TypeArguments: <VerseBookmark?>
    //     0x8e9548: ldr             x0, [x0, #0x9e8]
    //     0x8e954c: bl              #0x661298  ; InitAsyncStub
    // 0x8e9550: ldur            x1, [fp, #-0x10]
    // 0x8e9554: r0 = init()
    //     0x8e9554: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x8e9558: mov             x1, x0
    // 0x8e955c: stur            x1, [fp, #-0x18]
    // 0x8e9560: r0 = Await()
    //     0x8e9560: bl              #0x661044  ; AwaitStub
    // 0x8e9564: ldur            x0, [fp, #-0x10]
    // 0x8e9568: LoadField: r1 = r0->field_7
    //     0x8e9568: ldur            w1, [x0, #7]
    // 0x8e956c: DecompressPointer r1
    //     0x8e956c: add             x1, x1, HEAP, lsl #32
    // 0x8e9570: r0 = lastReadBox()
    //     0x8e9570: bl              #0x8e9458  ; [package:nuonline/services/storage_service/quran_storage.dart] QuranStorage::lastReadBox
    // 0x8e9574: mov             x1, x0
    // 0x8e9578: r2 = "current"
    //     0x8e9578: add             x2, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0x8e957c: ldr             x2, [x2, #0x110]
    // 0x8e9580: r0 = get()
    //     0x8e9580: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8e9584: r0 = ReturnAsync()
    //     0x8e9584: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8e9588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e9588: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e958c: b               #0x8e9544
  }
  _ findByJuzId(/* No info */) async {
    // ** addr: 0x900e9c, size: 0x11c
    // 0x900e9c: EnterFrame
    //     0x900e9c: stp             fp, lr, [SP, #-0x10]!
    //     0x900ea0: mov             fp, SP
    // 0x900ea4: AllocStack(0x38)
    //     0x900ea4: sub             SP, SP, #0x38
    // 0x900ea8: SetupParameters(VersesRepository this /* r1 => r3, fp-0x18 */)
    //     0x900ea8: stur            NULL, [fp, #-8]
    //     0x900eac: mov             x3, x1
    //     0x900eb0: stur            x1, [fp, #-0x18]
    // 0x900eb4: CheckStackOverflow
    //     0x900eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900eb8: cmp             SP, x16
    //     0x900ebc: b.ls            #0x900fa8
    // 0x900ec0: r0 = BoxInt64Instr(r2)
    //     0x900ec0: sbfiz           x0, x2, #1, #0x1f
    //     0x900ec4: cmp             x2, x0, asr #1
    //     0x900ec8: b.eq            #0x900ed4
    //     0x900ecc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x900ed0: stur            x2, [x0, #7]
    // 0x900ed4: stur            x0, [fp, #-0x10]
    // 0x900ed8: r1 = 1
    //     0x900ed8: movz            x1, #0x1
    // 0x900edc: r0 = AllocateContext()
    //     0x900edc: bl              #0xec126c  ; AllocateContextStub
    // 0x900ee0: mov             x1, x0
    // 0x900ee4: ldur            x0, [fp, #-0x10]
    // 0x900ee8: stur            x1, [fp, #-0x20]
    // 0x900eec: StoreField: r1->field_f = r0
    //     0x900eec: stur            w0, [x1, #0xf]
    // 0x900ef0: InitAsync() -> Future<List<Verse>>
    //     0x900ef0: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b4d0] TypeArguments: <List<Verse>>
    //     0x900ef4: ldr             x0, [x0, #0x4d0]
    //     0x900ef8: bl              #0x661298  ; InitAsyncStub
    // 0x900efc: ldur            x1, [fp, #-0x18]
    // 0x900f00: r0 = init()
    //     0x900f00: bl              #0x7bd3b0  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::init
    // 0x900f04: mov             x1, x0
    // 0x900f08: stur            x1, [fp, #-0x10]
    // 0x900f0c: r0 = Await()
    //     0x900f0c: bl              #0x661044  ; AwaitStub
    // 0x900f10: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x900f10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x900f14: ldr             x0, [x0, #0x2728]
    //     0x900f18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x900f1c: cmp             w0, w16
    //     0x900f20: b.ne            #0x900f2c
    //     0x900f24: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x900f28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x900f2c: r16 = <Verse>
    //     0x900f2c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0x900f30: ldr             x16, [x16, #0x1f0]
    // 0x900f34: stp             x0, x16, [SP, #8]
    // 0x900f38: r16 = "v2_quran_verse"
    //     0x900f38: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0x900f3c: ldr             x16, [x16, #0x218]
    // 0x900f40: str             x16, [SP]
    // 0x900f44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x900f44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x900f48: r0 = box()
    //     0x900f48: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x900f4c: mov             x1, x0
    // 0x900f50: stur            x0, [fp, #-0x10]
    // 0x900f54: r0 = checkOpen()
    //     0x900f54: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x900f58: ldur            x0, [fp, #-0x10]
    // 0x900f5c: LoadField: r1 = r0->field_1b
    //     0x900f5c: ldur            w1, [x0, #0x1b]
    // 0x900f60: DecompressPointer r1
    //     0x900f60: add             x1, x1, HEAP, lsl #32
    // 0x900f64: r16 = Sentinel
    //     0x900f64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x900f68: cmp             w1, w16
    // 0x900f6c: b.eq            #0x900fb0
    // 0x900f70: r0 = getValues()
    //     0x900f70: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x900f74: ldur            x2, [fp, #-0x20]
    // 0x900f78: r1 = Function '<anonymous closure>':.
    //     0x900f78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f4b0] AnonymousClosure: (0x900fb8), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByJuzId (0x900e9c)
    //     0x900f7c: ldr             x1, [x1, #0x4b0]
    // 0x900f80: stur            x0, [fp, #-0x10]
    // 0x900f84: r0 = AllocateClosure()
    //     0x900f84: bl              #0xec1630  ; AllocateClosureStub
    // 0x900f88: ldur            x1, [fp, #-0x10]
    // 0x900f8c: mov             x2, x0
    // 0x900f90: r0 = where()
    //     0x900f90: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x900f94: LoadField: r1 = r0->field_7
    //     0x900f94: ldur            w1, [x0, #7]
    // 0x900f98: DecompressPointer r1
    //     0x900f98: add             x1, x1, HEAP, lsl #32
    // 0x900f9c: mov             x2, x0
    // 0x900fa0: r0 = _GrowableList.of()
    //     0x900fa0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x900fa4: r0 = ReturnAsyncNotFuture()
    //     0x900fa4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x900fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x900fac: b               #0x900ec0
    // 0x900fb0: r9 = keystore
    //     0x900fb0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x900fb4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x900fb4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Verse) {
    // ** addr: 0x900fb8, size: 0x3c
    // 0x900fb8: ldr             x1, [SP, #8]
    // 0x900fbc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x900fbc: ldur            w2, [x1, #0x17]
    // 0x900fc0: DecompressPointer r2
    //     0x900fc0: add             x2, x2, HEAP, lsl #32
    // 0x900fc4: ldr             x1, [SP]
    // 0x900fc8: LoadField: r3 = r1->field_2b
    //     0x900fc8: ldur            x3, [x1, #0x2b]
    // 0x900fcc: LoadField: r1 = r2->field_f
    //     0x900fcc: ldur            w1, [x2, #0xf]
    // 0x900fd0: DecompressPointer r1
    //     0x900fd0: add             x1, x1, HEAP, lsl #32
    // 0x900fd4: r2 = LoadInt32Instr(r1)
    //     0x900fd4: sbfx            x2, x1, #1, #0x1f
    //     0x900fd8: tbz             w1, #0, #0x900fe0
    //     0x900fdc: ldur            x2, [x1, #7]
    // 0x900fe0: cmp             x3, x2
    // 0x900fe4: r16 = true
    //     0x900fe4: add             x16, NULL, #0x20  ; true
    // 0x900fe8: r17 = false
    //     0x900fe8: add             x17, NULL, #0x30  ; false
    // 0x900fec: csel            x0, x16, x17, eq
    // 0x900ff0: ret
    //     0x900ff0: ret             
  }
  _ getHistoryStream(/* No info */) {
    // ** addr: 0x9230a4, size: 0xc0
    // 0x9230a4: EnterFrame
    //     0x9230a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9230a8: mov             fp, SP
    // 0x9230ac: AllocStack(0x28)
    //     0x9230ac: sub             SP, SP, #0x28
    // 0x9230b0: SetupParameters(VersesRepository this /* r1 => r1, fp-0x8 */)
    //     0x9230b0: stur            x1, [fp, #-8]
    // 0x9230b4: CheckStackOverflow
    //     0x9230b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9230b8: cmp             SP, x16
    //     0x9230bc: b.ls            #0x92315c
    // 0x9230c0: r1 = 1
    //     0x9230c0: movz            x1, #0x1
    // 0x9230c4: r0 = AllocateContext()
    //     0x9230c4: bl              #0xec126c  ; AllocateContextStub
    // 0x9230c8: mov             x1, x0
    // 0x9230cc: ldur            x0, [fp, #-8]
    // 0x9230d0: stur            x1, [fp, #-0x10]
    // 0x9230d4: StoreField: r1->field_f = r0
    //     0x9230d4: stur            w0, [x1, #0xf]
    // 0x9230d8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x9230d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9230dc: ldr             x0, [x0, #0x2728]
    //     0x9230e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9230e4: cmp             w0, w16
    //     0x9230e8: b.ne            #0x9230f4
    //     0x9230ec: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x9230f0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9230f4: r16 = <VerseBookmark>
    //     0x9230f4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0x9230f8: ldr             x16, [x16, #0x200]
    // 0x9230fc: stp             x0, x16, [SP, #8]
    // 0x923100: r16 = "v2_quran_history"
    //     0x923100: add             x16, PP, #0xf, lsl #12  ; [pp+0xf230] "v2_quran_history"
    //     0x923104: ldr             x16, [x16, #0x230]
    // 0x923108: str             x16, [SP]
    // 0x92310c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x92310c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x923110: r0 = box()
    //     0x923110: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x923114: mov             x1, x0
    // 0x923118: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x923118: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x92311c: r0 = watch()
    //     0x92311c: bl              #0x836368  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::watch
    // 0x923120: ldur            x2, [fp, #-0x10]
    // 0x923124: r1 = Function '<anonymous closure>':.
    //     0x923124: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dba0] AnonymousClosure: (0x9231cc), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getHistoryStream (0x9230a4)
    //     0x923128: ldr             x1, [x1, #0xba0]
    // 0x92312c: stur            x0, [fp, #-8]
    // 0x923130: r0 = AllocateClosure()
    //     0x923130: bl              #0xec1630  ; AllocateClosureStub
    // 0x923134: r16 = <List<VerseBookmark>>
    //     0x923134: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dba8] TypeArguments: <List<VerseBookmark>>
    //     0x923138: ldr             x16, [x16, #0xba8]
    // 0x92313c: ldur            lr, [fp, #-8]
    // 0x923140: stp             lr, x16, [SP, #8]
    // 0x923144: str             x0, [SP]
    // 0x923148: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x923148: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92314c: r0 = map()
    //     0x92314c: bl              #0x68d014  ; [dart:async] Stream::map
    // 0x923150: LeaveFrame
    //     0x923150: mov             SP, fp
    //     0x923154: ldp             fp, lr, [SP], #0x10
    // 0x923158: ret
    //     0x923158: ret             
    // 0x92315c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92315c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923160: b               #0x9230c0
  }
  [closure] List<VerseBookmark> <anonymous closure>(dynamic, BoxEvent) {
    // ** addr: 0x9231cc, size: 0xe4
    // 0x9231cc: EnterFrame
    //     0x9231cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9231d0: mov             fp, SP
    // 0x9231d4: AllocStack(0x20)
    //     0x9231d4: sub             SP, SP, #0x20
    // 0x9231d8: CheckStackOverflow
    //     0x9231d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9231dc: cmp             SP, x16
    //     0x9231e0: b.ls            #0x9232a0
    // 0x9231e4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x9231e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9231e8: ldr             x0, [x0, #0x2728]
    //     0x9231ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9231f0: cmp             w0, w16
    //     0x9231f4: b.ne            #0x923200
    //     0x9231f8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x9231fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x923200: r16 = <VerseBookmark>
    //     0x923200: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0x923204: ldr             x16, [x16, #0x200]
    // 0x923208: stp             x0, x16, [SP, #8]
    // 0x92320c: r16 = "v2_quran_history"
    //     0x92320c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf230] "v2_quran_history"
    //     0x923210: ldr             x16, [x16, #0x230]
    // 0x923214: str             x16, [SP]
    // 0x923218: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x923218: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92321c: r0 = box()
    //     0x92321c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x923220: mov             x1, x0
    // 0x923224: stur            x0, [fp, #-8]
    // 0x923228: r0 = checkOpen()
    //     0x923228: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x92322c: ldur            x0, [fp, #-8]
    // 0x923230: LoadField: r1 = r0->field_1b
    //     0x923230: ldur            w1, [x0, #0x1b]
    // 0x923234: DecompressPointer r1
    //     0x923234: add             x1, x1, HEAP, lsl #32
    // 0x923238: r16 = Sentinel
    //     0x923238: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92323c: cmp             w1, w16
    // 0x923240: b.eq            #0x9232a8
    // 0x923244: r0 = getValues()
    //     0x923244: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x923248: LoadField: r1 = r0->field_7
    //     0x923248: ldur            w1, [x0, #7]
    // 0x92324c: DecompressPointer r1
    //     0x92324c: add             x1, x1, HEAP, lsl #32
    // 0x923250: mov             x2, x0
    // 0x923254: r0 = _GrowableList.of()
    //     0x923254: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x923258: r1 = Function '<anonymous closure>':.
    //     0x923258: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbb0] AnonymousClosure: (0x9232b0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getHistoryStream (0x9230a4)
    //     0x92325c: ldr             x1, [x1, #0xbb0]
    // 0x923260: r2 = Null
    //     0x923260: mov             x2, NULL
    // 0x923264: stur            x0, [fp, #-8]
    // 0x923268: r0 = AllocateClosure()
    //     0x923268: bl              #0xec1630  ; AllocateClosureStub
    // 0x92326c: str             x0, [SP]
    // 0x923270: ldur            x1, [fp, #-8]
    // 0x923274: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x923274: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x923278: r0 = sort()
    //     0x923278: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x92327c: ldur            x1, [fp, #-8]
    // 0x923280: r2 = 5
    //     0x923280: movz            x2, #0x5
    // 0x923284: r0 = take()
    //     0x923284: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0x923288: mov             x1, x0
    // 0x92328c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x92328c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x923290: r0 = toList()
    //     0x923290: bl              #0x8633fc  ; [dart:_internal] SubListIterable::toList
    // 0x923294: LeaveFrame
    //     0x923294: mov             SP, fp
    //     0x923298: ldp             fp, lr, [SP], #0x10
    // 0x92329c: ret
    //     0x92329c: ret             
    // 0x9232a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9232a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9232a4: b               #0x9231e4
    // 0x9232a8: r9 = keystore
    //     0x9232a8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x9232ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9232ac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, VerseBookmark, VerseBookmark) {
    // ** addr: 0x9232b0, size: 0x94
    // 0x9232b0: EnterFrame
    //     0x9232b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9232b4: mov             fp, SP
    // 0x9232b8: CheckStackOverflow
    //     0x9232b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9232bc: cmp             SP, x16
    //     0x9232c0: b.ls            #0x92333c
    // 0x9232c4: ldr             x0, [fp, #0x10]
    // 0x9232c8: LoadField: r1 = r0->field_3b
    //     0x9232c8: ldur            w1, [x0, #0x3b]
    // 0x9232cc: DecompressPointer r1
    //     0x9232cc: add             x1, x1, HEAP, lsl #32
    // 0x9232d0: cmp             w1, NULL
    // 0x9232d4: b.eq            #0x92332c
    // 0x9232d8: ldr             x0, [fp, #0x18]
    // 0x9232dc: LoadField: r2 = r0->field_3b
    //     0x9232dc: ldur            w2, [x0, #0x3b]
    // 0x9232e0: DecompressPointer r2
    //     0x9232e0: add             x2, x2, HEAP, lsl #32
    // 0x9232e4: cmp             w2, NULL
    // 0x9232e8: b.eq            #0x92332c
    // 0x9232ec: r0 = LoadClassIdInstr(r1)
    //     0x9232ec: ldur            x0, [x1, #-1]
    //     0x9232f0: ubfx            x0, x0, #0xc, #0x14
    // 0x9232f4: r0 = GDT[cid_x0 + 0x138b7]()
    //     0x9232f4: movz            x17, #0x38b7
    //     0x9232f8: movk            x17, #0x1, lsl #16
    //     0x9232fc: add             lr, x0, x17
    //     0x923300: ldr             lr, [x21, lr, lsl #3]
    //     0x923304: blr             lr
    // 0x923308: mov             x2, x0
    // 0x92330c: r0 = BoxInt64Instr(r2)
    //     0x92330c: sbfiz           x0, x2, #1, #0x1f
    //     0x923310: cmp             x2, x0, asr #1
    //     0x923314: b.eq            #0x923320
    //     0x923318: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92331c: stur            x2, [x0, #7]
    // 0x923320: LeaveFrame
    //     0x923320: mov             SP, fp
    //     0x923324: ldp             fp, lr, [SP], #0x10
    // 0x923328: ret
    //     0x923328: ret             
    // 0x92332c: r0 = 0
    //     0x92332c: movz            x0, #0
    // 0x923330: LeaveFrame
    //     0x923330: mov             SP, fp
    //     0x923334: ldp             fp, lr, [SP], #0x10
    // 0x923338: ret
    //     0x923338: ret             
    // 0x92333c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92333c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923340: b               #0x9232c4
  }
  _ getHistory(/* No info */) async {
    // ** addr: 0x923344, size: 0xf0
    // 0x923344: EnterFrame
    //     0x923344: stp             fp, lr, [SP, #-0x10]!
    //     0x923348: mov             fp, SP
    // 0x92334c: AllocStack(0x28)
    //     0x92334c: sub             SP, SP, #0x28
    // 0x923350: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */)
    //     0x923350: stur            NULL, [fp, #-8]
    //     0x923354: stur            x1, [fp, #-0x10]
    // 0x923358: CheckStackOverflow
    //     0x923358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92335c: cmp             SP, x16
    //     0x923360: b.ls            #0x923424
    // 0x923364: InitAsync() -> Future<List<VerseBookmark>>
    //     0x923364: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3dba8] TypeArguments: <List<VerseBookmark>>
    //     0x923368: ldr             x0, [x0, #0xba8]
    //     0x92336c: bl              #0x661298  ; InitAsyncStub
    // 0x923370: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x923370: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x923374: ldr             x0, [x0, #0x2728]
    //     0x923378: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x92337c: cmp             w0, w16
    //     0x923380: b.ne            #0x92338c
    //     0x923384: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x923388: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x92338c: r16 = <VerseBookmark>
    //     0x92338c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0x923390: ldr             x16, [x16, #0x200]
    // 0x923394: stp             x0, x16, [SP, #8]
    // 0x923398: r16 = "v2_quran_history"
    //     0x923398: add             x16, PP, #0xf, lsl #12  ; [pp+0xf230] "v2_quran_history"
    //     0x92339c: ldr             x16, [x16, #0x230]
    // 0x9233a0: str             x16, [SP]
    // 0x9233a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9233a4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9233a8: r0 = box()
    //     0x9233a8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x9233ac: mov             x1, x0
    // 0x9233b0: stur            x0, [fp, #-0x10]
    // 0x9233b4: r0 = checkOpen()
    //     0x9233b4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x9233b8: ldur            x0, [fp, #-0x10]
    // 0x9233bc: LoadField: r1 = r0->field_1b
    //     0x9233bc: ldur            w1, [x0, #0x1b]
    // 0x9233c0: DecompressPointer r1
    //     0x9233c0: add             x1, x1, HEAP, lsl #32
    // 0x9233c4: r16 = Sentinel
    //     0x9233c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9233c8: cmp             w1, w16
    // 0x9233cc: b.eq            #0x92342c
    // 0x9233d0: r0 = getValues()
    //     0x9233d0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x9233d4: LoadField: r1 = r0->field_7
    //     0x9233d4: ldur            w1, [x0, #7]
    // 0x9233d8: DecompressPointer r1
    //     0x9233d8: add             x1, x1, HEAP, lsl #32
    // 0x9233dc: mov             x2, x0
    // 0x9233e0: r0 = _GrowableList.of()
    //     0x9233e0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x9233e4: r1 = Function '<anonymous closure>':.
    //     0x9233e4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbb8] AnonymousClosure: (0x9232b0), in [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::getHistoryStream (0x9230a4)
    //     0x9233e8: ldr             x1, [x1, #0xbb8]
    // 0x9233ec: r2 = Null
    //     0x9233ec: mov             x2, NULL
    // 0x9233f0: stur            x0, [fp, #-0x10]
    // 0x9233f4: r0 = AllocateClosure()
    //     0x9233f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9233f8: str             x0, [SP]
    // 0x9233fc: ldur            x1, [fp, #-0x10]
    // 0x923400: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x923400: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x923404: r0 = sort()
    //     0x923404: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x923408: ldur            x1, [fp, #-0x10]
    // 0x92340c: r2 = 5
    //     0x92340c: movz            x2, #0x5
    // 0x923410: r0 = take()
    //     0x923410: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0x923414: mov             x1, x0
    // 0x923418: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x923418: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x92341c: r0 = toList()
    //     0x92341c: bl              #0x8633fc  ; [dart:_internal] SubListIterable::toList
    // 0x923420: r0 = ReturnAsyncNotFuture()
    //     0x923420: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x923424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x923424: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923428: b               #0x923364
    // 0x92342c: r9 = keystore
    //     0x92342c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x923430: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x923430: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ addLastRead(/* No info */) async {
    // ** addr: 0xb1ce68, size: 0x9c
    // 0xb1ce68: EnterFrame
    //     0xb1ce68: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ce6c: mov             fp, SP
    // 0xb1ce70: AllocStack(0x30)
    //     0xb1ce70: sub             SP, SP, #0x30
    // 0xb1ce74: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xb1ce74: stur            NULL, [fp, #-8]
    //     0xb1ce78: mov             x3, x2
    //     0xb1ce7c: stur            x1, [fp, #-0x10]
    //     0xb1ce80: stur            x2, [fp, #-0x18]
    // 0xb1ce84: CheckStackOverflow
    //     0xb1ce84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1ce88: cmp             SP, x16
    //     0xb1ce8c: b.ls            #0xb1cefc
    // 0xb1ce90: InitAsync() -> Future<void?>
    //     0xb1ce90: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb1ce94: bl              #0x661298  ; InitAsyncStub
    // 0xb1ce98: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb1ce98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1ce9c: ldr             x0, [x0, #0x2728]
    //     0xb1cea0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1cea4: cmp             w0, w16
    //     0xb1cea8: b.ne            #0xb1ceb4
    //     0xb1ceac: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb1ceb0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb1ceb4: r16 = <VerseBookmark>
    //     0xb1ceb4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0xb1ceb8: ldr             x16, [x16, #0x200]
    // 0xb1cebc: stp             x0, x16, [SP, #8]
    // 0xb1cec0: r16 = "v2_quran_last_read"
    //     0xb1cec0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf228] "v2_quran_last_read"
    //     0xb1cec4: ldr             x16, [x16, #0x228]
    // 0xb1cec8: str             x16, [SP]
    // 0xb1cecc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1cecc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1ced0: r0 = box()
    //     0xb1ced0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb1ced4: mov             x1, x0
    // 0xb1ced8: ldur            x3, [fp, #-0x18]
    // 0xb1cedc: r2 = "current"
    //     0xb1cedc: add             x2, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0xb1cee0: ldr             x2, [x2, #0x110]
    // 0xb1cee4: r0 = put()
    //     0xb1cee4: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb1cee8: mov             x1, x0
    // 0xb1ceec: stur            x1, [fp, #-0x10]
    // 0xb1cef0: r0 = Await()
    //     0xb1cef0: bl              #0x661044  ; AwaitStub
    // 0xb1cef4: r0 = Null
    //     0xb1cef4: mov             x0, NULL
    // 0xb1cef8: r0 = ReturnAsyncNotFuture()
    //     0xb1cef8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb1cefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1cefc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1cf00: b               #0xb1ce90
  }
  _ addHistoryRead(/* No info */) async {
    // ** addr: 0xb1cf04, size: 0xc4
    // 0xb1cf04: EnterFrame
    //     0xb1cf04: stp             fp, lr, [SP, #-0x10]!
    //     0xb1cf08: mov             fp, SP
    // 0xb1cf0c: AllocStack(0x28)
    //     0xb1cf0c: sub             SP, SP, #0x28
    // 0xb1cf10: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xb1cf10: stur            NULL, [fp, #-8]
    //     0xb1cf14: mov             x3, x2
    //     0xb1cf18: stur            x1, [fp, #-0x10]
    //     0xb1cf1c: stur            x2, [fp, #-0x18]
    // 0xb1cf20: CheckStackOverflow
    //     0xb1cf20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1cf24: cmp             SP, x16
    //     0xb1cf28: b.ls            #0xb1cfc0
    // 0xb1cf2c: InitAsync() -> Future<void?>
    //     0xb1cf2c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xb1cf30: bl              #0x661298  ; InitAsyncStub
    // 0xb1cf34: ldur            x3, [fp, #-0x18]
    // 0xb1cf38: LoadField: r2 = r3->field_7
    //     0xb1cf38: ldur            x2, [x3, #7]
    // 0xb1cf3c: r0 = BoxInt64Instr(r2)
    //     0xb1cf3c: sbfiz           x0, x2, #1, #0x1f
    //     0xb1cf40: cmp             x2, x0, asr #1
    //     0xb1cf44: b.eq            #0xb1cf50
    //     0xb1cf48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1cf4c: stur            x2, [x0, #7]
    // 0xb1cf50: r1 = Null
    //     0xb1cf50: mov             x1, NULL
    // 0xb1cf54: r2 = 4
    //     0xb1cf54: movz            x2, #0x4
    // 0xb1cf58: stur            x0, [fp, #-0x20]
    // 0xb1cf5c: r0 = AllocateArray()
    //     0xb1cf5c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb1cf60: mov             x1, x0
    // 0xb1cf64: ldur            x0, [fp, #-0x20]
    // 0xb1cf68: StoreField: r1->field_f = r0
    //     0xb1cf68: stur            w0, [x1, #0xf]
    // 0xb1cf6c: ldur            x3, [fp, #-0x18]
    // 0xb1cf70: LoadField: r0 = r3->field_33
    //     0xb1cf70: ldur            w0, [x3, #0x33]
    // 0xb1cf74: DecompressPointer r0
    //     0xb1cf74: add             x0, x0, HEAP, lsl #32
    // 0xb1cf78: StoreField: r1->field_13 = r0
    //     0xb1cf78: stur            w0, [x1, #0x13]
    // 0xb1cf7c: str             x1, [SP]
    // 0xb1cf80: r0 = _interpolate()
    //     0xb1cf80: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb1cf84: mov             x2, x0
    // 0xb1cf88: ldur            x0, [fp, #-0x10]
    // 0xb1cf8c: stur            x2, [fp, #-0x20]
    // 0xb1cf90: LoadField: r1 = r0->field_7
    //     0xb1cf90: ldur            w1, [x0, #7]
    // 0xb1cf94: DecompressPointer r1
    //     0xb1cf94: add             x1, x1, HEAP, lsl #32
    // 0xb1cf98: r0 = historyBox()
    //     0xb1cf98: bl              #0x923164  ; [package:nuonline/services/storage_service/quran_storage.dart] QuranStorage::historyBox
    // 0xb1cf9c: mov             x1, x0
    // 0xb1cfa0: ldur            x2, [fp, #-0x20]
    // 0xb1cfa4: ldur            x3, [fp, #-0x18]
    // 0xb1cfa8: r0 = put()
    //     0xb1cfa8: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb1cfac: mov             x1, x0
    // 0xb1cfb0: stur            x1, [fp, #-0x10]
    // 0xb1cfb4: r0 = Await()
    //     0xb1cfb4: bl              #0x661044  ; AwaitStub
    // 0xb1cfb8: r0 = Null
    //     0xb1cfb8: mov             x0, NULL
    // 0xb1cfbc: r0 = ReturnAsyncNotFuture()
    //     0xb1cfbc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb1cfc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1cfc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1cfc4: b               #0xb1cf2c
  }
  _ removeBookmark(/* No info */) async {
    // ** addr: 0xbc6060, size: 0x11c
    // 0xbc6060: EnterFrame
    //     0xbc6060: stp             fp, lr, [SP, #-0x10]!
    //     0xbc6064: mov             fp, SP
    // 0xbc6068: AllocStack(0x40)
    //     0xbc6068: sub             SP, SP, #0x40
    // 0xbc606c: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbc606c: stur            NULL, [fp, #-8]
    //     0xbc6070: stur            x1, [fp, #-0x10]
    //     0xbc6074: stur            x2, [fp, #-0x18]
    // 0xbc6078: CheckStackOverflow
    //     0xbc6078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc607c: cmp             SP, x16
    //     0xbc6080: b.ls            #0xbc6174
    // 0xbc6084: InitAsync() -> Future<void?>
    //     0xbc6084: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc6088: bl              #0x661298  ; InitAsyncStub
    // 0xbc608c: ldur            x1, [fp, #-0x10]
    // 0xbc6090: ldur            x2, [fp, #-0x18]
    // 0xbc6094: r0 = findByVerseId()
    //     0xbc6094: bl              #0x7bcb2c  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByVerseId
    // 0xbc6098: mov             x1, x0
    // 0xbc609c: stur            x1, [fp, #-0x20]
    // 0xbc60a0: r0 = Await()
    //     0xbc60a0: bl              #0x661044  ; AwaitStub
    // 0xbc60a4: stur            x0, [fp, #-0x10]
    // 0xbc60a8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xbc60a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc60ac: ldr             x0, [x0, #0x2728]
    //     0xbc60b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc60b4: cmp             w0, w16
    //     0xbc60b8: b.ne            #0xbc60c4
    //     0xbc60bc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xbc60c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc60c4: r16 = <Verse>
    //     0xbc60c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0xbc60c8: ldr             x16, [x16, #0x1f0]
    // 0xbc60cc: stp             x0, x16, [SP, #8]
    // 0xbc60d0: r16 = "v2_quran_verse"
    //     0xbc60d0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0xbc60d4: ldr             x16, [x16, #0x218]
    // 0xbc60d8: str             x16, [SP]
    // 0xbc60dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbc60dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbc60e0: r0 = box()
    //     0xbc60e0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xbc60e4: ldur            x1, [fp, #-0x10]
    // 0xbc60e8: stur            x0, [fp, #-0x20]
    // 0xbc60ec: LoadField: r2 = r1->field_7
    //     0xbc60ec: ldur            x2, [x1, #7]
    // 0xbc60f0: stur            x2, [fp, #-0x18]
    // 0xbc60f4: r0 = DateTime()
    //     0xbc60f4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbc60f8: mov             x1, x0
    // 0xbc60fc: r0 = false
    //     0xbc60fc: add             x0, NULL, #0x30  ; false
    // 0xbc6100: stur            x1, [fp, #-0x28]
    // 0xbc6104: StoreField: r1->field_13 = r0
    //     0xbc6104: stur            w0, [x1, #0x13]
    // 0xbc6108: r0 = _getCurrentMicros()
    //     0xbc6108: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xbc610c: r1 = LoadInt32Instr(r0)
    //     0xbc610c: sbfx            x1, x0, #1, #0x1f
    //     0xbc6110: tbz             w0, #0, #0xbc6118
    //     0xbc6114: ldur            x1, [x0, #7]
    // 0xbc6118: ldur            x0, [fp, #-0x28]
    // 0xbc611c: StoreField: r0->field_7 = r1
    //     0xbc611c: stur            x1, [x0, #7]
    // 0xbc6120: r16 = false
    //     0xbc6120: add             x16, NULL, #0x30  ; false
    // 0xbc6124: stp             x0, x16, [SP]
    // 0xbc6128: ldur            x1, [fp, #-0x10]
    // 0xbc612c: r4 = const [0, 0x3, 0x2, 0x1, bookmark, 0x1, updatedAt, 0x2, null]
    //     0xbc612c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c088] List(9) [0, 0x3, 0x2, 0x1, "bookmark", 0x1, "updatedAt", 0x2, Null]
    //     0xbc6130: ldr             x4, [x4, #0x88]
    // 0xbc6134: r0 = copyWith()
    //     0xbc6134: bl              #0xb220fc  ; [package:nuonline/app/data/models/verse.dart] Verse::copyWith
    // 0xbc6138: mov             x3, x0
    // 0xbc613c: ldur            x2, [fp, #-0x18]
    // 0xbc6140: r0 = BoxInt64Instr(r2)
    //     0xbc6140: sbfiz           x0, x2, #1, #0x1f
    //     0xbc6144: cmp             x2, x0, asr #1
    //     0xbc6148: b.eq            #0xbc6154
    //     0xbc614c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbc6150: stur            x2, [x0, #7]
    // 0xbc6154: ldur            x1, [fp, #-0x20]
    // 0xbc6158: mov             x2, x0
    // 0xbc615c: r0 = put()
    //     0xbc615c: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xbc6160: mov             x1, x0
    // 0xbc6164: stur            x1, [fp, #-0x10]
    // 0xbc6168: r0 = Await()
    //     0xbc6168: bl              #0x661044  ; AwaitStub
    // 0xbc616c: r0 = Null
    //     0xbc616c: mov             x0, NULL
    // 0xbc6170: r0 = ReturnAsyncNotFuture()
    //     0xbc6170: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc6174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc6174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc6178: b               #0xbc6084
  }
  _ addBookmark(/* No info */) async {
    // ** addr: 0xbc74d4, size: 0x11c
    // 0xbc74d4: EnterFrame
    //     0xbc74d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc74d8: mov             fp, SP
    // 0xbc74dc: AllocStack(0x40)
    //     0xbc74dc: sub             SP, SP, #0x40
    // 0xbc74e0: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbc74e0: stur            NULL, [fp, #-8]
    //     0xbc74e4: stur            x1, [fp, #-0x10]
    //     0xbc74e8: stur            x2, [fp, #-0x18]
    // 0xbc74ec: CheckStackOverflow
    //     0xbc74ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc74f0: cmp             SP, x16
    //     0xbc74f4: b.ls            #0xbc75e8
    // 0xbc74f8: InitAsync() -> Future<void?>
    //     0xbc74f8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc74fc: bl              #0x661298  ; InitAsyncStub
    // 0xbc7500: ldur            x1, [fp, #-0x10]
    // 0xbc7504: ldur            x2, [fp, #-0x18]
    // 0xbc7508: r0 = findByVerseId()
    //     0xbc7508: bl              #0x7bcb2c  ; [package:nuonline/app/data/repositories/verse_repository.dart] VersesRepository::findByVerseId
    // 0xbc750c: mov             x1, x0
    // 0xbc7510: stur            x1, [fp, #-0x20]
    // 0xbc7514: r0 = Await()
    //     0xbc7514: bl              #0x661044  ; AwaitStub
    // 0xbc7518: stur            x0, [fp, #-0x10]
    // 0xbc751c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xbc751c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc7520: ldr             x0, [x0, #0x2728]
    //     0xbc7524: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc7528: cmp             w0, w16
    //     0xbc752c: b.ne            #0xbc7538
    //     0xbc7530: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xbc7534: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc7538: r16 = <Verse>
    //     0xbc7538: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1f0] TypeArguments: <Verse>
    //     0xbc753c: ldr             x16, [x16, #0x1f0]
    // 0xbc7540: stp             x0, x16, [SP, #8]
    // 0xbc7544: r16 = "v2_quran_verse"
    //     0xbc7544: add             x16, PP, #0xf, lsl #12  ; [pp+0xf218] "v2_quran_verse"
    //     0xbc7548: ldr             x16, [x16, #0x218]
    // 0xbc754c: str             x16, [SP]
    // 0xbc7550: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbc7550: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbc7554: r0 = box()
    //     0xbc7554: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xbc7558: ldur            x1, [fp, #-0x10]
    // 0xbc755c: stur            x0, [fp, #-0x20]
    // 0xbc7560: LoadField: r2 = r1->field_7
    //     0xbc7560: ldur            x2, [x1, #7]
    // 0xbc7564: stur            x2, [fp, #-0x18]
    // 0xbc7568: r0 = DateTime()
    //     0xbc7568: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbc756c: mov             x1, x0
    // 0xbc7570: r0 = false
    //     0xbc7570: add             x0, NULL, #0x30  ; false
    // 0xbc7574: stur            x1, [fp, #-0x28]
    // 0xbc7578: StoreField: r1->field_13 = r0
    //     0xbc7578: stur            w0, [x1, #0x13]
    // 0xbc757c: r0 = _getCurrentMicros()
    //     0xbc757c: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xbc7580: r1 = LoadInt32Instr(r0)
    //     0xbc7580: sbfx            x1, x0, #1, #0x1f
    //     0xbc7584: tbz             w0, #0, #0xbc758c
    //     0xbc7588: ldur            x1, [x0, #7]
    // 0xbc758c: ldur            x0, [fp, #-0x28]
    // 0xbc7590: StoreField: r0->field_7 = r1
    //     0xbc7590: stur            x1, [x0, #7]
    // 0xbc7594: r16 = true
    //     0xbc7594: add             x16, NULL, #0x20  ; true
    // 0xbc7598: stp             x0, x16, [SP]
    // 0xbc759c: ldur            x1, [fp, #-0x10]
    // 0xbc75a0: r4 = const [0, 0x3, 0x2, 0x1, bookmark, 0x1, updatedAt, 0x2, null]
    //     0xbc75a0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c088] List(9) [0, 0x3, 0x2, 0x1, "bookmark", 0x1, "updatedAt", 0x2, Null]
    //     0xbc75a4: ldr             x4, [x4, #0x88]
    // 0xbc75a8: r0 = copyWith()
    //     0xbc75a8: bl              #0xb220fc  ; [package:nuonline/app/data/models/verse.dart] Verse::copyWith
    // 0xbc75ac: mov             x3, x0
    // 0xbc75b0: ldur            x2, [fp, #-0x18]
    // 0xbc75b4: r0 = BoxInt64Instr(r2)
    //     0xbc75b4: sbfiz           x0, x2, #1, #0x1f
    //     0xbc75b8: cmp             x2, x0, asr #1
    //     0xbc75bc: b.eq            #0xbc75c8
    //     0xbc75c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbc75c4: stur            x2, [x0, #7]
    // 0xbc75c8: ldur            x1, [fp, #-0x20]
    // 0xbc75cc: mov             x2, x0
    // 0xbc75d0: r0 = put()
    //     0xbc75d0: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xbc75d4: mov             x1, x0
    // 0xbc75d8: stur            x1, [fp, #-0x10]
    // 0xbc75dc: r0 = Await()
    //     0xbc75dc: bl              #0x661044  ; AwaitStub
    // 0xbc75e0: r0 = Null
    //     0xbc75e0: mov             x0, NULL
    // 0xbc75e4: r0 = ReturnAsyncNotFuture()
    //     0xbc75e4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc75e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc75e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc75ec: b               #0xbc74f8
  }
  _ removeLastRead(/* No info */) async {
    // ** addr: 0xbc7d4c, size: 0x7c
    // 0xbc7d4c: EnterFrame
    //     0xbc7d4c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc7d50: mov             fp, SP
    // 0xbc7d54: AllocStack(0x28)
    //     0xbc7d54: sub             SP, SP, #0x28
    // 0xbc7d58: SetupParameters(VersesRepository this /* r1 => r1, fp-0x10 */)
    //     0xbc7d58: stur            NULL, [fp, #-8]
    //     0xbc7d5c: stur            x1, [fp, #-0x10]
    // 0xbc7d60: CheckStackOverflow
    //     0xbc7d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc7d64: cmp             SP, x16
    //     0xbc7d68: b.ls            #0xbc7dc0
    // 0xbc7d6c: InitAsync() -> Future<void?>
    //     0xbc7d6c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc7d70: bl              #0x661298  ; InitAsyncStub
    // 0xbc7d74: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xbc7d74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc7d78: ldr             x0, [x0, #0x2728]
    //     0xbc7d7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc7d80: cmp             w0, w16
    //     0xbc7d84: b.ne            #0xbc7d90
    //     0xbc7d88: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xbc7d8c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc7d90: r16 = <VerseBookmark>
    //     0xbc7d90: add             x16, PP, #0xf, lsl #12  ; [pp+0xf200] TypeArguments: <VerseBookmark>
    //     0xbc7d94: ldr             x16, [x16, #0x200]
    // 0xbc7d98: stp             x0, x16, [SP, #8]
    // 0xbc7d9c: r16 = "v2_quran_last_read"
    //     0xbc7d9c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf228] "v2_quran_last_read"
    //     0xbc7da0: ldr             x16, [x16, #0x228]
    // 0xbc7da4: str             x16, [SP]
    // 0xbc7da8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbc7da8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbc7dac: r0 = box()
    //     0xbc7dac: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xbc7db0: mov             x1, x0
    // 0xbc7db4: r0 = clear()
    //     0xbc7db4: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0xbc7db8: r0 = Null
    //     0xbc7db8: mov             x0, NULL
    // 0xbc7dbc: r0 = ReturnAsyncNotFuture()
    //     0xbc7dbc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc7dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc7dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc7dc4: b               #0xbc7d6c
  }
}
