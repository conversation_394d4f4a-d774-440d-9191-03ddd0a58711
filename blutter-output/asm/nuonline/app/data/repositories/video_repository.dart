// lib: , url: package:nuonline/app/data/repositories/video_repository.dart

// class id: 1050104, size: 0x8
class :: {
}

// class id: 1072, size: 0xc, field offset: 0x8
class VideoRepository extends Object {

  _ findByYoutubeId(/* No info */) async {
    // ** addr: 0x906080, size: 0x114
    // 0x906080: EnterFrame
    //     0x906080: stp             fp, lr, [SP, #-0x10]!
    //     0x906084: mov             fp, SP
    // 0x906088: AllocStack(0x80)
    //     0x906088: sub             SP, SP, #0x80
    // 0x90608c: SetupParameters(VideoRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x90608c: stur            NULL, [fp, #-8]
    //     0x906090: stur            x1, [fp, #-0x58]
    //     0x906094: stur            x2, [fp, #-0x60]
    // 0x906098: CheckStackOverflow
    //     0x906098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90609c: cmp             SP, x16
    //     0x9060a0: b.ls            #0x90618c
    // 0x9060a4: InitAsync() -> Future<ApiResult<VideoDetail>>
    //     0x9060a4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32078] TypeArguments: <ApiResult<VideoDetail>>
    //     0x9060a8: ldr             x0, [x0, #0x78]
    //     0x9060ac: bl              #0x661298  ; InitAsyncStub
    // 0x9060b0: ldur            x1, [fp, #-0x58]
    // 0x9060b4: ldur            x0, [fp, #-0x60]
    // 0x9060b8: LoadField: r3 = r1->field_7
    //     0x9060b8: ldur            w3, [x1, #7]
    // 0x9060bc: DecompressPointer r3
    //     0x9060bc: add             x3, x3, HEAP, lsl #32
    // 0x9060c0: stur            x3, [fp, #-0x68]
    // 0x9060c4: r1 = Null
    //     0x9060c4: mov             x1, NULL
    // 0x9060c8: r2 = 4
    //     0x9060c8: movz            x2, #0x4
    // 0x9060cc: r0 = AllocateArray()
    //     0x9060cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9060d0: r16 = "/youtube/"
    //     0x9060d0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32080] "/youtube/"
    //     0x9060d4: ldr             x16, [x16, #0x80]
    // 0x9060d8: StoreField: r0->field_f = r16
    //     0x9060d8: stur            w16, [x0, #0xf]
    // 0x9060dc: ldur            x1, [fp, #-0x60]
    // 0x9060e0: StoreField: r0->field_13 = r1
    //     0x9060e0: stur            w1, [x0, #0x13]
    // 0x9060e4: str             x0, [SP]
    // 0x9060e8: r0 = _interpolate()
    //     0x9060e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x9060ec: ldur            x16, [fp, #-0x68]
    // 0x9060f0: stp             x16, NULL, [SP, #8]
    // 0x9060f4: str             x0, [SP]
    // 0x9060f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9060f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9060fc: r0 = get()
    //     0x9060fc: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x906100: mov             x1, x0
    // 0x906104: stur            x1, [fp, #-0x58]
    // 0x906108: r0 = Await()
    //     0x906108: bl              #0x661044  ; AwaitStub
    // 0x90610c: LoadField: r3 = r0->field_b
    //     0x90610c: ldur            w3, [x0, #0xb]
    // 0x906110: DecompressPointer r3
    //     0x906110: add             x3, x3, HEAP, lsl #32
    // 0x906114: mov             x0, x3
    // 0x906118: stur            x3, [fp, #-0x58]
    // 0x90611c: r2 = Null
    //     0x90611c: mov             x2, NULL
    // 0x906120: r1 = Null
    //     0x906120: mov             x1, NULL
    // 0x906124: r8 = Map<String, dynamic>
    //     0x906124: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x906128: r3 = Null
    //     0x906128: add             x3, PP, #0x32, lsl #12  ; [pp+0x32088] Null
    //     0x90612c: ldr             x3, [x3, #0x88]
    // 0x906130: r0 = Map<String, dynamic>()
    //     0x906130: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x906134: ldur            x2, [fp, #-0x58]
    // 0x906138: r1 = Null
    //     0x906138: mov             x1, NULL
    // 0x90613c: r0 = VideoDetail.fromMap()
    //     0x90613c: bl              #0x906194  ; [package:nuonline/app/data/models/video.dart] VideoDetail::VideoDetail.fromMap
    // 0x906140: r1 = <VideoDetail>
    //     0x906140: add             x1, PP, #0x29, lsl #12  ; [pp+0x295b8] TypeArguments: <VideoDetail>
    //     0x906144: ldr             x1, [x1, #0x5b8]
    // 0x906148: stur            x0, [fp, #-0x58]
    // 0x90614c: r0 = _$SuccessImpl()
    //     0x90614c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x906150: mov             x1, x0
    // 0x906154: ldur            x0, [fp, #-0x58]
    // 0x906158: StoreField: r1->field_b = r0
    //     0x906158: stur            w0, [x1, #0xb]
    // 0x90615c: mov             x0, x1
    // 0x906160: r0 = ReturnAsyncNotFuture()
    //     0x906160: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x906164: sub             SP, fp, #0x80
    // 0x906168: mov             x1, x0
    // 0x90616c: r0 = getDioException()
    //     0x90616c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x906170: r1 = <VideoDetail>
    //     0x906170: add             x1, PP, #0x29, lsl #12  ; [pp+0x295b8] TypeArguments: <VideoDetail>
    //     0x906174: ldr             x1, [x1, #0x5b8]
    // 0x906178: stur            x0, [fp, #-0x58]
    // 0x90617c: r0 = _$FailureImpl()
    //     0x90617c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x906180: ldur            x1, [fp, #-0x58]
    // 0x906184: StoreField: r0->field_b = r1
    //     0x906184: stur            w1, [x0, #0xb]
    // 0x906188: r0 = ReturnAsyncNotFuture()
    //     0x906188: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x90618c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90618c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906190: b               #0x9060a4
  }
  _ findAll(/* No info */) async {
    // ** addr: 0x91d6dc, size: 0x130
    // 0x91d6dc: EnterFrame
    //     0x91d6dc: stp             fp, lr, [SP, #-0x10]!
    //     0x91d6e0: mov             fp, SP
    // 0x91d6e4: AllocStack(0xa0)
    //     0x91d6e4: sub             SP, SP, #0xa0
    // 0x91d6e8: SetupParameters(VideoRepository this /* r1 => r1, fp-0x78 */, {dynamic query = _ConstMap len:0 /* r2, fp-0x70 */})
    //     0x91d6e8: stur            NULL, [fp, #-8]
    //     0x91d6ec: stur            x1, [fp, #-0x78]
    //     0x91d6f0: stur            x4, [fp, #-0x80]
    //     0x91d6f4: ldur            w0, [x4, #0x13]
    //     0x91d6f8: ldur            w2, [x4, #0x1f]
    //     0x91d6fc: add             x2, x2, HEAP, lsl #32
    //     0x91d700: ldr             x16, [PP, #0x3650]  ; [pp+0x3650] "query"
    //     0x91d704: cmp             w2, w16
    //     0x91d708: b.ne            #0x91d728
    //     0x91d70c: ldur            w2, [x4, #0x23]
    //     0x91d710: add             x2, x2, HEAP, lsl #32
    //     0x91d714: sub             w3, w0, w2
    //     0x91d718: add             x0, fp, w3, sxtw #2
    //     0x91d71c: ldr             x0, [x0, #8]
    //     0x91d720: mov             x2, x0
    //     0x91d724: b               #0x91d730
    //     0x91d728: add             x2, PP, #0x11, lsl #12  ; [pp+0x11250] Map<String, String?>(0)
    //     0x91d72c: ldr             x2, [x2, #0x250]
    //     0x91d730: stur            x2, [fp, #-0x70]
    // 0x91d734: CheckStackOverflow
    //     0x91d734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d738: cmp             SP, x16
    //     0x91d73c: b.ls            #0x91d804
    // 0x91d740: InitAsync() -> Future<ApiResult<List<Video>>>
    //     0x91d740: add             x0, PP, #0x29, lsl #12  ; [pp+0x292e0] TypeArguments: <ApiResult<List<Video>>>
    //     0x91d744: ldr             x0, [x0, #0x2e0]
    //     0x91d748: bl              #0x661298  ; InitAsyncStub
    // 0x91d74c: ldur            x0, [fp, #-0x78]
    // 0x91d750: LoadField: r1 = r0->field_7
    //     0x91d750: ldur            w1, [x0, #7]
    // 0x91d754: DecompressPointer r1
    //     0x91d754: add             x1, x1, HEAP, lsl #32
    // 0x91d758: stp             x1, NULL, [SP, #0x10]
    // 0x91d75c: r16 = "/videos"
    //     0x91d75c: add             x16, PP, #0x29, lsl #12  ; [pp+0x292e8] "/videos"
    //     0x91d760: ldr             x16, [x16, #0x2e8]
    // 0x91d764: ldur            lr, [fp, #-0x70]
    // 0x91d768: stp             lr, x16, [SP]
    // 0x91d76c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0x91d76c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0x91d770: ldr             x4, [x4, #0x2f0]
    // 0x91d774: r0 = get()
    //     0x91d774: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x91d778: mov             x1, x0
    // 0x91d77c: stur            x1, [fp, #-0x70]
    // 0x91d780: r0 = Await()
    //     0x91d780: bl              #0x661044  ; AwaitStub
    // 0x91d784: stur            x0, [fp, #-0x70]
    // 0x91d788: LoadField: r1 = r0->field_b
    //     0x91d788: ldur            w1, [x0, #0xb]
    // 0x91d78c: DecompressPointer r1
    //     0x91d78c: add             x1, x1, HEAP, lsl #32
    // 0x91d790: r0 = fromResponse()
    //     0x91d790: bl              #0x91d80c  ; [package:nuonline/app/data/models/video.dart] Video::fromResponse
    // 0x91d794: mov             x3, x0
    // 0x91d798: ldur            x0, [fp, #-0x70]
    // 0x91d79c: stur            x3, [fp, #-0x78]
    // 0x91d7a0: LoadField: r2 = r0->field_1b
    //     0x91d7a0: ldur            w2, [x0, #0x1b]
    // 0x91d7a4: DecompressPointer r2
    //     0x91d7a4: add             x2, x2, HEAP, lsl #32
    // 0x91d7a8: r1 = Null
    //     0x91d7a8: mov             x1, NULL
    // 0x91d7ac: r0 = Pagination.fromHeaders()
    //     0x91d7ac: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0x91d7b0: r1 = <List<Video>>
    //     0x91d7b0: add             x1, PP, #0x29, lsl #12  ; [pp+0x292f8] TypeArguments: <List<Video>>
    //     0x91d7b4: ldr             x1, [x1, #0x2f8]
    // 0x91d7b8: stur            x0, [fp, #-0x70]
    // 0x91d7bc: r0 = _$SuccessImpl()
    //     0x91d7bc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x91d7c0: mov             x1, x0
    // 0x91d7c4: ldur            x0, [fp, #-0x78]
    // 0x91d7c8: StoreField: r1->field_b = r0
    //     0x91d7c8: stur            w0, [x1, #0xb]
    // 0x91d7cc: ldur            x0, [fp, #-0x70]
    // 0x91d7d0: StoreField: r1->field_f = r0
    //     0x91d7d0: stur            w0, [x1, #0xf]
    // 0x91d7d4: mov             x0, x1
    // 0x91d7d8: r0 = ReturnAsyncNotFuture()
    //     0x91d7d8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91d7dc: sub             SP, fp, #0xa0
    // 0x91d7e0: mov             x1, x0
    // 0x91d7e4: r0 = getDioException()
    //     0x91d7e4: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x91d7e8: r1 = <List<Video>>
    //     0x91d7e8: add             x1, PP, #0x29, lsl #12  ; [pp+0x292f8] TypeArguments: <List<Video>>
    //     0x91d7ec: ldr             x1, [x1, #0x2f8]
    // 0x91d7f0: stur            x0, [fp, #-0x70]
    // 0x91d7f4: r0 = _$FailureImpl()
    //     0x91d7f4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x91d7f8: ldur            x1, [fp, #-0x70]
    // 0x91d7fc: StoreField: r0->field_b = r1
    //     0x91d7fc: stur            w1, [x0, #0xb]
    // 0x91d800: r0 = ReturnAsyncNotFuture()
    //     0x91d800: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x91d804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d808: b               #0x91d740
  }
  _ findById(/* No info */) async {
    // ** addr: 0x9252dc, size: 0x12c
    // 0x9252dc: EnterFrame
    //     0x9252dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9252e0: mov             fp, SP
    // 0x9252e4: AllocStack(0x80)
    //     0x9252e4: sub             SP, SP, #0x80
    // 0x9252e8: SetupParameters(VideoRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x9252e8: stur            NULL, [fp, #-8]
    //     0x9252ec: stur            x1, [fp, #-0x58]
    //     0x9252f0: stur            x2, [fp, #-0x60]
    // 0x9252f4: CheckStackOverflow
    //     0x9252f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9252f8: cmp             SP, x16
    //     0x9252fc: b.ls            #0x925400
    // 0x925300: InitAsync() -> Future<ApiResult<VideoDetail>>
    //     0x925300: add             x0, PP, #0x32, lsl #12  ; [pp+0x32078] TypeArguments: <ApiResult<VideoDetail>>
    //     0x925304: ldr             x0, [x0, #0x78]
    //     0x925308: bl              #0x661298  ; InitAsyncStub
    // 0x92530c: ldur            x1, [fp, #-0x58]
    // 0x925310: ldur            x0, [fp, #-0x60]
    // 0x925314: LoadField: r3 = r1->field_7
    //     0x925314: ldur            w3, [x1, #7]
    // 0x925318: DecompressPointer r3
    //     0x925318: add             x3, x3, HEAP, lsl #32
    // 0x92531c: stur            x3, [fp, #-0x68]
    // 0x925320: r1 = Null
    //     0x925320: mov             x1, NULL
    // 0x925324: r2 = 4
    //     0x925324: movz            x2, #0x4
    // 0x925328: r0 = AllocateArray()
    //     0x925328: bl              #0xec22fc  ; AllocateArrayStub
    // 0x92532c: mov             x2, x0
    // 0x925330: r16 = "/videos/"
    //     0x925330: add             x16, PP, #0x32, lsl #12  ; [pp+0x32168] "/videos/"
    //     0x925334: ldr             x16, [x16, #0x168]
    // 0x925338: StoreField: r2->field_f = r16
    //     0x925338: stur            w16, [x2, #0xf]
    // 0x92533c: ldur            x3, [fp, #-0x60]
    // 0x925340: r0 = BoxInt64Instr(r3)
    //     0x925340: sbfiz           x0, x3, #1, #0x1f
    //     0x925344: cmp             x3, x0, asr #1
    //     0x925348: b.eq            #0x925354
    //     0x92534c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x925350: stur            x3, [x0, #7]
    // 0x925354: StoreField: r2->field_13 = r0
    //     0x925354: stur            w0, [x2, #0x13]
    // 0x925358: str             x2, [SP]
    // 0x92535c: r0 = _interpolate()
    //     0x92535c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x925360: ldur            x16, [fp, #-0x68]
    // 0x925364: stp             x16, NULL, [SP, #8]
    // 0x925368: str             x0, [SP]
    // 0x92536c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x92536c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x925370: r0 = get()
    //     0x925370: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x925374: mov             x1, x0
    // 0x925378: stur            x1, [fp, #-0x58]
    // 0x92537c: r0 = Await()
    //     0x92537c: bl              #0x661044  ; AwaitStub
    // 0x925380: LoadField: r3 = r0->field_b
    //     0x925380: ldur            w3, [x0, #0xb]
    // 0x925384: DecompressPointer r3
    //     0x925384: add             x3, x3, HEAP, lsl #32
    // 0x925388: mov             x0, x3
    // 0x92538c: stur            x3, [fp, #-0x58]
    // 0x925390: r2 = Null
    //     0x925390: mov             x2, NULL
    // 0x925394: r1 = Null
    //     0x925394: mov             x1, NULL
    // 0x925398: r8 = Map<String, dynamic>
    //     0x925398: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x92539c: r3 = Null
    //     0x92539c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32170] Null
    //     0x9253a0: ldr             x3, [x3, #0x170]
    // 0x9253a4: r0 = Map<String, dynamic>()
    //     0x9253a4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9253a8: ldur            x2, [fp, #-0x58]
    // 0x9253ac: r1 = Null
    //     0x9253ac: mov             x1, NULL
    // 0x9253b0: r0 = VideoDetail.fromMap()
    //     0x9253b0: bl              #0x906194  ; [package:nuonline/app/data/models/video.dart] VideoDetail::VideoDetail.fromMap
    // 0x9253b4: r1 = <VideoDetail>
    //     0x9253b4: add             x1, PP, #0x29, lsl #12  ; [pp+0x295b8] TypeArguments: <VideoDetail>
    //     0x9253b8: ldr             x1, [x1, #0x5b8]
    // 0x9253bc: stur            x0, [fp, #-0x58]
    // 0x9253c0: r0 = _$SuccessImpl()
    //     0x9253c0: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x9253c4: mov             x1, x0
    // 0x9253c8: ldur            x0, [fp, #-0x58]
    // 0x9253cc: StoreField: r1->field_b = r0
    //     0x9253cc: stur            w0, [x1, #0xb]
    // 0x9253d0: mov             x0, x1
    // 0x9253d4: r0 = ReturnAsyncNotFuture()
    //     0x9253d4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9253d8: sub             SP, fp, #0x80
    // 0x9253dc: mov             x1, x0
    // 0x9253e0: r0 = getDioException()
    //     0x9253e0: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x9253e4: r1 = <VideoDetail>
    //     0x9253e4: add             x1, PP, #0x29, lsl #12  ; [pp+0x295b8] TypeArguments: <VideoDetail>
    //     0x9253e8: ldr             x1, [x1, #0x5b8]
    // 0x9253ec: stur            x0, [fp, #-0x58]
    // 0x9253f0: r0 = _$FailureImpl()
    //     0x9253f0: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x9253f4: ldur            x1, [fp, #-0x58]
    // 0x9253f8: StoreField: r0->field_b = r1
    //     0x9253f8: stur            w1, [x0, #0xb]
    // 0x9253fc: r0 = ReturnAsyncNotFuture()
    //     0x9253fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x925400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925404: b               #0x925300
  }
  _ findPlaylist(/* No info */) async {
    // ** addr: 0x925614, size: 0x12c
    // 0x925614: EnterFrame
    //     0x925614: stp             fp, lr, [SP, #-0x10]!
    //     0x925618: mov             fp, SP
    // 0x92561c: AllocStack(0x80)
    //     0x92561c: sub             SP, SP, #0x80
    // 0x925620: SetupParameters(VideoRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x925620: stur            NULL, [fp, #-8]
    //     0x925624: stur            x1, [fp, #-0x58]
    //     0x925628: stur            x2, [fp, #-0x60]
    // 0x92562c: CheckStackOverflow
    //     0x92562c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925630: cmp             SP, x16
    //     0x925634: b.ls            #0x925738
    // 0x925638: InitAsync() -> Future<ApiResult<Playlist>>
    //     0x925638: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c7e0] TypeArguments: <ApiResult<Playlist>>
    //     0x92563c: ldr             x0, [x0, #0x7e0]
    //     0x925640: bl              #0x661298  ; InitAsyncStub
    // 0x925644: ldur            x1, [fp, #-0x58]
    // 0x925648: ldur            x0, [fp, #-0x60]
    // 0x92564c: LoadField: r3 = r1->field_7
    //     0x92564c: ldur            w3, [x1, #7]
    // 0x925650: DecompressPointer r3
    //     0x925650: add             x3, x3, HEAP, lsl #32
    // 0x925654: stur            x3, [fp, #-0x68]
    // 0x925658: r1 = Null
    //     0x925658: mov             x1, NULL
    // 0x92565c: r2 = 4
    //     0x92565c: movz            x2, #0x4
    // 0x925660: r0 = AllocateArray()
    //     0x925660: bl              #0xec22fc  ; AllocateArrayStub
    // 0x925664: mov             x2, x0
    // 0x925668: r16 = "/playlists/"
    //     0x925668: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c7e8] "/playlists/"
    //     0x92566c: ldr             x16, [x16, #0x7e8]
    // 0x925670: StoreField: r2->field_f = r16
    //     0x925670: stur            w16, [x2, #0xf]
    // 0x925674: ldur            x3, [fp, #-0x60]
    // 0x925678: r0 = BoxInt64Instr(r3)
    //     0x925678: sbfiz           x0, x3, #1, #0x1f
    //     0x92567c: cmp             x3, x0, asr #1
    //     0x925680: b.eq            #0x92568c
    //     0x925684: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x925688: stur            x3, [x0, #7]
    // 0x92568c: StoreField: r2->field_13 = r0
    //     0x92568c: stur            w0, [x2, #0x13]
    // 0x925690: str             x2, [SP]
    // 0x925694: r0 = _interpolate()
    //     0x925694: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x925698: ldur            x16, [fp, #-0x68]
    // 0x92569c: stp             x16, NULL, [SP, #8]
    // 0x9256a0: str             x0, [SP]
    // 0x9256a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9256a4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9256a8: r0 = get()
    //     0x9256a8: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0x9256ac: mov             x1, x0
    // 0x9256b0: stur            x1, [fp, #-0x58]
    // 0x9256b4: r0 = Await()
    //     0x9256b4: bl              #0x661044  ; AwaitStub
    // 0x9256b8: LoadField: r3 = r0->field_b
    //     0x9256b8: ldur            w3, [x0, #0xb]
    // 0x9256bc: DecompressPointer r3
    //     0x9256bc: add             x3, x3, HEAP, lsl #32
    // 0x9256c0: mov             x0, x3
    // 0x9256c4: stur            x3, [fp, #-0x58]
    // 0x9256c8: r2 = Null
    //     0x9256c8: mov             x2, NULL
    // 0x9256cc: r1 = Null
    //     0x9256cc: mov             x1, NULL
    // 0x9256d0: r8 = Map<String, dynamic>
    //     0x9256d0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x9256d4: r3 = Null
    //     0x9256d4: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c7f0] Null
    //     0x9256d8: ldr             x3, [x3, #0x7f0]
    // 0x9256dc: r0 = Map<String, dynamic>()
    //     0x9256dc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x9256e0: ldur            x2, [fp, #-0x58]
    // 0x9256e4: r1 = Null
    //     0x9256e4: mov             x1, NULL
    // 0x9256e8: r0 = Playlist.fromMap()
    //     0x9256e8: bl              #0x925740  ; [package:nuonline/app/data/models/playlist.dart] Playlist::Playlist.fromMap
    // 0x9256ec: r1 = <Playlist>
    //     0x9256ec: add             x1, PP, #0x29, lsl #12  ; [pp+0x290d0] TypeArguments: <Playlist>
    //     0x9256f0: ldr             x1, [x1, #0xd0]
    // 0x9256f4: stur            x0, [fp, #-0x58]
    // 0x9256f8: r0 = _$SuccessImpl()
    //     0x9256f8: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x9256fc: mov             x1, x0
    // 0x925700: ldur            x0, [fp, #-0x58]
    // 0x925704: StoreField: r1->field_b = r0
    //     0x925704: stur            w0, [x1, #0xb]
    // 0x925708: mov             x0, x1
    // 0x92570c: r0 = ReturnAsyncNotFuture()
    //     0x92570c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x925710: sub             SP, fp, #0x80
    // 0x925714: mov             x1, x0
    // 0x925718: r0 = getDioException()
    //     0x925718: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x92571c: r1 = <Playlist>
    //     0x92571c: add             x1, PP, #0x29, lsl #12  ; [pp+0x290d0] TypeArguments: <Playlist>
    //     0x925720: ldr             x1, [x1, #0xd0]
    // 0x925724: stur            x0, [fp, #-0x58]
    // 0x925728: r0 = _$FailureImpl()
    //     0x925728: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x92572c: ldur            x1, [fp, #-0x58]
    // 0x925730: StoreField: r0->field_b = r1
    //     0x925730: stur            w1, [x0, #0xb]
    // 0x925734: r0 = ReturnAsyncNotFuture()
    //     0x925734: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x925738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92573c: b               #0x925638
  }
  _ findAllPlaylist(/* No info */) async {
    // ** addr: 0xe3435c, size: 0xf0
    // 0xe3435c: EnterFrame
    //     0xe3435c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34360: mov             fp, SP
    // 0xe34364: AllocStack(0x88)
    //     0xe34364: sub             SP, SP, #0x88
    // 0xe34368: SetupParameters(VideoRepository this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0xe34368: stur            NULL, [fp, #-8]
    //     0xe3436c: stur            x1, [fp, #-0x60]
    //     0xe34370: stur            x2, [fp, #-0x68]
    // 0xe34374: CheckStackOverflow
    //     0xe34374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34378: cmp             SP, x16
    //     0xe3437c: b.ls            #0xe34444
    // 0xe34380: InitAsync() -> Future<ApiResult<List<Playlist>>>
    //     0xe34380: add             x0, PP, #0x31, lsl #12  ; [pp+0x31f10] TypeArguments: <ApiResult<List<Playlist>>>
    //     0xe34384: ldr             x0, [x0, #0xf10]
    //     0xe34388: bl              #0x661298  ; InitAsyncStub
    // 0xe3438c: ldur            x0, [fp, #-0x60]
    // 0xe34390: LoadField: r1 = r0->field_7
    //     0xe34390: ldur            w1, [x0, #7]
    // 0xe34394: DecompressPointer r1
    //     0xe34394: add             x1, x1, HEAP, lsl #32
    // 0xe34398: stp             x1, NULL, [SP, #0x10]
    // 0xe3439c: r16 = "/playlists"
    //     0xe3439c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31f18] "/playlists"
    //     0xe343a0: ldr             x16, [x16, #0xf18]
    // 0xe343a4: ldur            lr, [fp, #-0x68]
    // 0xe343a8: stp             lr, x16, [SP]
    // 0xe343ac: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe343ac: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe343b0: ldr             x4, [x4, #0x2f0]
    // 0xe343b4: r0 = get()
    //     0xe343b4: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe343b8: mov             x1, x0
    // 0xe343bc: stur            x1, [fp, #-0x60]
    // 0xe343c0: r0 = Await()
    //     0xe343c0: bl              #0x661044  ; AwaitStub
    // 0xe343c4: stur            x0, [fp, #-0x60]
    // 0xe343c8: LoadField: r1 = r0->field_b
    //     0xe343c8: ldur            w1, [x0, #0xb]
    // 0xe343cc: DecompressPointer r1
    //     0xe343cc: add             x1, x1, HEAP, lsl #32
    // 0xe343d0: r0 = fromResponse()
    //     0xe343d0: bl              #0xe3444c  ; [package:nuonline/app/data/models/playlist.dart] Playlist::fromResponse
    // 0xe343d4: mov             x3, x0
    // 0xe343d8: ldur            x0, [fp, #-0x60]
    // 0xe343dc: stur            x3, [fp, #-0x68]
    // 0xe343e0: LoadField: r2 = r0->field_1b
    //     0xe343e0: ldur            w2, [x0, #0x1b]
    // 0xe343e4: DecompressPointer r2
    //     0xe343e4: add             x2, x2, HEAP, lsl #32
    // 0xe343e8: r1 = Null
    //     0xe343e8: mov             x1, NULL
    // 0xe343ec: r0 = Pagination.fromHeaders()
    //     0xe343ec: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe343f0: r1 = <List<Playlist>>
    //     0xe343f0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31f20] TypeArguments: <List<Playlist>>
    //     0xe343f4: ldr             x1, [x1, #0xf20]
    // 0xe343f8: stur            x0, [fp, #-0x60]
    // 0xe343fc: r0 = _$SuccessImpl()
    //     0xe343fc: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe34400: mov             x1, x0
    // 0xe34404: ldur            x0, [fp, #-0x68]
    // 0xe34408: StoreField: r1->field_b = r0
    //     0xe34408: stur            w0, [x1, #0xb]
    // 0xe3440c: ldur            x0, [fp, #-0x60]
    // 0xe34410: StoreField: r1->field_f = r0
    //     0xe34410: stur            w0, [x1, #0xf]
    // 0xe34414: mov             x0, x1
    // 0xe34418: r0 = ReturnAsyncNotFuture()
    //     0xe34418: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe3441c: sub             SP, fp, #0x88
    // 0xe34420: mov             x1, x0
    // 0xe34424: r0 = getDioException()
    //     0xe34424: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe34428: r1 = <List<Playlist>>
    //     0xe34428: add             x1, PP, #0x31, lsl #12  ; [pp+0x31f20] TypeArguments: <List<Playlist>>
    //     0xe3442c: ldr             x1, [x1, #0xf20]
    // 0xe34430: stur            x0, [fp, #-0x60]
    // 0xe34434: r0 = _$FailureImpl()
    //     0xe34434: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe34438: ldur            x1, [fp, #-0x60]
    // 0xe3443c: StoreField: r0->field_b = r1
    //     0xe3443c: stur            w1, [x0, #0xb]
    // 0xe34440: r0 = ReturnAsyncNotFuture()
    //     0xe34440: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe34444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34444: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34448: b               #0xe34380
  }
}
