// lib: , url: package:nuonline/app/data/repositories/wirid_repository.dart

// class id: 1050105, size: 0x8
class :: {
}

// class id: 1071, size: 0x10, field offset: 0x8
class WiridRepository extends Object {

  _ findByOrder(/* No info */) async {
    // ** addr: 0x8f1a84, size: 0xc8
    // 0x8f1a84: EnterFrame
    //     0x8f1a84: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1a88: mov             fp, SP
    // 0x8f1a8c: AllocStack(0x38)
    //     0x8f1a8c: sub             SP, SP, #0x38
    // 0x8f1a90: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8f1a90: stur            NULL, [fp, #-8]
    //     0x8f1a94: stur            x1, [fp, #-0x10]
    //     0x8f1a98: stur            x2, [fp, #-0x18]
    // 0x8f1a9c: CheckStackOverflow
    //     0x8f1a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1aa0: cmp             SP, x16
    //     0x8f1aa4: b.ls            #0x8f1b44
    // 0x8f1aa8: InitAsync() -> Future<Wirid>
    //     0x8f1aa8: add             x0, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x8f1aac: ldr             x0, [x0, #0x1c8]
    //     0x8f1ab0: bl              #0x661298  ; InitAsyncStub
    // 0x8f1ab4: ldur            x1, [fp, #-0x10]
    // 0x8f1ab8: r0 = init()
    //     0x8f1ab8: bl              #0x8f1bb4  ; [package:nuonline/app/data/repositories/wirid_repository.dart] WiridRepository::init
    // 0x8f1abc: mov             x1, x0
    // 0x8f1ac0: stur            x1, [fp, #-0x20]
    // 0x8f1ac4: r0 = Await()
    //     0x8f1ac4: bl              #0x661044  ; AwaitStub
    // 0x8f1ac8: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8f1ac8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f1acc: ldr             x0, [x0, #0x2728]
    //     0x8f1ad0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f1ad4: cmp             w0, w16
    //     0x8f1ad8: b.ne            #0x8f1ae4
    //     0x8f1adc: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8f1ae0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f1ae4: r16 = <Wirid>
    //     0x8f1ae4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x8f1ae8: ldr             x16, [x16, #0x1c8]
    // 0x8f1aec: stp             x0, x16, [SP, #8]
    // 0x8f1af0: r16 = "v2_wirid"
    //     0x8f1af0: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0x8f1af4: ldr             x16, [x16, #0x1d8]
    // 0x8f1af8: str             x16, [SP]
    // 0x8f1afc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f1afc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f1b00: r0 = box()
    //     0x8f1b00: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f1b04: mov             x3, x0
    // 0x8f1b08: ldur            x2, [fp, #-0x18]
    // 0x8f1b0c: r0 = BoxInt64Instr(r2)
    //     0x8f1b0c: sbfiz           x0, x2, #1, #0x1f
    //     0x8f1b10: cmp             x2, x0, asr #1
    //     0x8f1b14: b.eq            #0x8f1b20
    //     0x8f1b18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1b1c: stur            x2, [x0, #7]
    // 0x8f1b20: mov             x1, x3
    // 0x8f1b24: mov             x2, x0
    // 0x8f1b28: r0 = get()
    //     0x8f1b28: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0x8f1b2c: cmp             w0, NULL
    // 0x8f1b30: b.eq            #0x8f1b38
    // 0x8f1b34: r0 = ReturnAsyncNotFuture()
    //     0x8f1b34: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f1b38: r0 = Instance_Wirid
    //     0x8f1b38: add             x0, PP, #0x29, lsl #12  ; [pp+0x29d80] Obj!Wirid@e25a81
    //     0x8f1b3c: ldr             x0, [x0, #0xd80]
    // 0x8f1b40: r0 = ReturnAsyncNotFuture()
    //     0x8f1b40: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f1b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1b44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1b48: b               #0x8f1aa8
  }
  _ init(/* No info */) async {
    // ** addr: 0x8f1bb4, size: 0x170
    // 0x8f1bb4: EnterFrame
    //     0x8f1bb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1bb8: mov             fp, SP
    // 0x8f1bbc: AllocStack(0x40)
    //     0x8f1bbc: sub             SP, SP, #0x40
    // 0x8f1bc0: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */)
    //     0x8f1bc0: stur            NULL, [fp, #-8]
    //     0x8f1bc4: stur            x1, [fp, #-0x10]
    // 0x8f1bc8: CheckStackOverflow
    //     0x8f1bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1bcc: cmp             SP, x16
    //     0x8f1bd0: b.ls            #0x8f1d14
    // 0x8f1bd4: InitAsync() -> Future<void?>
    //     0x8f1bd4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8f1bd8: bl              #0x661298  ; InitAsyncStub
    // 0x8f1bdc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8f1bdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f1be0: ldr             x0, [x0, #0x2728]
    //     0x8f1be4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f1be8: cmp             w0, w16
    //     0x8f1bec: b.ne            #0x8f1bf8
    //     0x8f1bf0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8f1bf4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f1bf8: stur            x0, [fp, #-0x18]
    // 0x8f1bfc: r16 = <Wirid>
    //     0x8f1bfc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x8f1c00: ldr             x16, [x16, #0x1c8]
    // 0x8f1c04: stp             x0, x16, [SP, #8]
    // 0x8f1c08: r16 = "v2_wirid"
    //     0x8f1c08: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0x8f1c0c: ldr             x16, [x16, #0x1d8]
    // 0x8f1c10: str             x16, [SP]
    // 0x8f1c14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f1c14: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f1c18: r0 = box()
    //     0x8f1c18: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f1c1c: mov             x1, x0
    // 0x8f1c20: stur            x0, [fp, #-0x20]
    // 0x8f1c24: r0 = checkOpen()
    //     0x8f1c24: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8f1c28: ldur            x0, [fp, #-0x20]
    // 0x8f1c2c: LoadField: r1 = r0->field_1b
    //     0x8f1c2c: ldur            w1, [x0, #0x1b]
    // 0x8f1c30: DecompressPointer r1
    //     0x8f1c30: add             x1, x1, HEAP, lsl #32
    // 0x8f1c34: r16 = Sentinel
    //     0x8f1c34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f1c38: cmp             w1, w16
    // 0x8f1c3c: b.eq            #0x8f1d1c
    // 0x8f1c40: LoadField: r0 = r1->field_13
    //     0x8f1c40: ldur            w0, [x1, #0x13]
    // 0x8f1c44: DecompressPointer r0
    //     0x8f1c44: add             x0, x0, HEAP, lsl #32
    // 0x8f1c48: LoadField: r1 = r0->field_1f
    //     0x8f1c48: ldur            x1, [x0, #0x1f]
    // 0x8f1c4c: cbnz            x1, #0x8f1d0c
    // 0x8f1c50: ldur            x0, [fp, #-0x10]
    // 0x8f1c54: r16 = <Wirid>
    //     0x8f1c54: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x8f1c58: ldr             x16, [x16, #0x1c8]
    // 0x8f1c5c: ldur            lr, [fp, #-0x18]
    // 0x8f1c60: stp             lr, x16, [SP, #8]
    // 0x8f1c64: r16 = "v2_wirid"
    //     0x8f1c64: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0x8f1c68: ldr             x16, [x16, #0x1d8]
    // 0x8f1c6c: str             x16, [SP]
    // 0x8f1c70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f1c70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f1c74: r0 = box()
    //     0x8f1c74: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f1c78: mov             x2, x0
    // 0x8f1c7c: ldur            x0, [fp, #-0x10]
    // 0x8f1c80: stur            x2, [fp, #-0x20]
    // 0x8f1c84: LoadField: r3 = r0->field_7
    //     0x8f1c84: ldur            w3, [x0, #7]
    // 0x8f1c88: DecompressPointer r3
    //     0x8f1c88: add             x3, x3, HEAP, lsl #32
    // 0x8f1c8c: mov             x1, x3
    // 0x8f1c90: stur            x3, [fp, #-0x18]
    // 0x8f1c94: r0 = tasbihCount()
    //     0x8f1c94: bl              #0x8f1f84  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::tasbihCount
    // 0x8f1c98: ldur            x1, [fp, #-0x18]
    // 0x8f1c9c: stur            x0, [fp, #-0x28]
    // 0x8f1ca0: r0 = tasbihMax()
    //     0x8f1ca0: bl              #0x8f1ee4  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::tasbihMax
    // 0x8f1ca4: mov             x3, x0
    // 0x8f1ca8: ldur            x2, [fp, #-0x28]
    // 0x8f1cac: r0 = BoxInt64Instr(r2)
    //     0x8f1cac: sbfiz           x0, x2, #1, #0x1f
    //     0x8f1cb0: cmp             x2, x0, asr #1
    //     0x8f1cb4: b.eq            #0x8f1cc0
    //     0x8f1cb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1cbc: stur            x2, [x0, #7]
    // 0x8f1cc0: mov             x2, x0
    // 0x8f1cc4: r0 = BoxInt64Instr(r3)
    //     0x8f1cc4: sbfiz           x0, x3, #1, #0x1f
    //     0x8f1cc8: cmp             x3, x0, asr #1
    //     0x8f1ccc: b.eq            #0x8f1cd8
    //     0x8f1cd0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1cd4: stur            x3, [x0, #7]
    // 0x8f1cd8: stp             x0, x2, [SP]
    // 0x8f1cdc: r1 = Instance_Wirid
    //     0x8f1cdc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29d80] Obj!Wirid@e25a81
    //     0x8f1ce0: ldr             x1, [x1, #0xd80]
    // 0x8f1ce4: r4 = const [0, 0x3, 0x2, 0x1, currentCount, 0x1, maxCount, 0x2, null]
    //     0x8f1ce4: add             x4, PP, #0x29, lsl #12  ; [pp+0x29d88] List(9) [0, 0x3, 0x2, 0x1, "currentCount", 0x1, "maxCount", 0x2, Null]
    //     0x8f1ce8: ldr             x4, [x4, #0xd88]
    // 0x8f1cec: r0 = copyWith()
    //     0x8f1cec: bl              #0x8f1d24  ; [package:nuonline/app/data/models/wirid.dart] Wirid::copyWith
    // 0x8f1cf0: ldur            x1, [fp, #-0x20]
    // 0x8f1cf4: mov             x3, x0
    // 0x8f1cf8: r2 = 0
    //     0x8f1cf8: movz            x2, #0
    // 0x8f1cfc: r0 = put()
    //     0x8f1cfc: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0x8f1d00: mov             x1, x0
    // 0x8f1d04: stur            x1, [fp, #-0x10]
    // 0x8f1d08: r0 = Await()
    //     0x8f1d08: bl              #0x661044  ; AwaitStub
    // 0x8f1d0c: r0 = Null
    //     0x8f1d0c: mov             x0, NULL
    // 0x8f1d10: r0 = ReturnAsyncNotFuture()
    //     0x8f1d10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f1d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1d14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1d18: b               #0x8f1bd4
    // 0x8f1d1c: r9 = keystore
    //     0x8f1d1c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8f1d20: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f1d20: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getTotalWirid(/* No info */) async {
    // ** addr: 0x8f2020, size: 0xd0
    // 0x8f2020: EnterFrame
    //     0x8f2020: stp             fp, lr, [SP, #-0x10]!
    //     0x8f2024: mov             fp, SP
    // 0x8f2028: AllocStack(0x30)
    //     0x8f2028: sub             SP, SP, #0x30
    // 0x8f202c: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */)
    //     0x8f202c: stur            NULL, [fp, #-8]
    //     0x8f2030: stur            x1, [fp, #-0x10]
    // 0x8f2034: CheckStackOverflow
    //     0x8f2034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f2038: cmp             SP, x16
    //     0x8f203c: b.ls            #0x8f20e0
    // 0x8f2040: InitAsync() -> Future<int>
    //     0x8f2040: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0x8f2044: bl              #0x661298  ; InitAsyncStub
    // 0x8f2048: ldur            x1, [fp, #-0x10]
    // 0x8f204c: r0 = init()
    //     0x8f204c: bl              #0x8f1bb4  ; [package:nuonline/app/data/repositories/wirid_repository.dart] WiridRepository::init
    // 0x8f2050: mov             x1, x0
    // 0x8f2054: stur            x1, [fp, #-0x18]
    // 0x8f2058: r0 = Await()
    //     0x8f2058: bl              #0x661044  ; AwaitStub
    // 0x8f205c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x8f205c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8f2060: ldr             x0, [x0, #0x2728]
    //     0x8f2064: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8f2068: cmp             w0, w16
    //     0x8f206c: b.ne            #0x8f2078
    //     0x8f2070: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x8f2074: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8f2078: r16 = <Wirid>
    //     0x8f2078: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x8f207c: ldr             x16, [x16, #0x1c8]
    // 0x8f2080: stp             x0, x16, [SP, #8]
    // 0x8f2084: r16 = "v2_wirid"
    //     0x8f2084: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0x8f2088: ldr             x16, [x16, #0x1d8]
    // 0x8f208c: str             x16, [SP]
    // 0x8f2090: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8f2090: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8f2094: r0 = box()
    //     0x8f2094: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x8f2098: mov             x1, x0
    // 0x8f209c: stur            x0, [fp, #-0x10]
    // 0x8f20a0: r0 = checkOpen()
    //     0x8f20a0: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x8f20a4: ldur            x2, [fp, #-0x10]
    // 0x8f20a8: LoadField: r3 = r2->field_1b
    //     0x8f20a8: ldur            w3, [x2, #0x1b]
    // 0x8f20ac: DecompressPointer r3
    //     0x8f20ac: add             x3, x3, HEAP, lsl #32
    // 0x8f20b0: r16 = Sentinel
    //     0x8f20b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f20b4: cmp             w3, w16
    // 0x8f20b8: b.eq            #0x8f20e8
    // 0x8f20bc: LoadField: r2 = r3->field_13
    //     0x8f20bc: ldur            w2, [x3, #0x13]
    // 0x8f20c0: DecompressPointer r2
    //     0x8f20c0: add             x2, x2, HEAP, lsl #32
    // 0x8f20c4: LoadField: r3 = r2->field_1f
    //     0x8f20c4: ldur            x3, [x2, #0x1f]
    // 0x8f20c8: r0 = BoxInt64Instr(r3)
    //     0x8f20c8: sbfiz           x0, x3, #1, #0x1f
    //     0x8f20cc: cmp             x3, x0, asr #1
    //     0x8f20d0: b.eq            #0x8f20dc
    //     0x8f20d4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f20d8: stur            x3, [x0, #7]
    // 0x8f20dc: r0 = ReturnAsyncNotFuture()
    //     0x8f20dc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8f20e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f20e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f20e4: b               #0x8f2040
    // 0x8f20e8: r9 = keystore
    //     0x8f20e8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x8f20ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f20ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ getAllWirid(/* No info */) async {
    // ** addr: 0x905e40, size: 0xc8
    // 0x905e40: EnterFrame
    //     0x905e40: stp             fp, lr, [SP, #-0x10]!
    //     0x905e44: mov             fp, SP
    // 0x905e48: AllocStack(0x30)
    //     0x905e48: sub             SP, SP, #0x30
    // 0x905e4c: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */)
    //     0x905e4c: stur            NULL, [fp, #-8]
    //     0x905e50: stur            x1, [fp, #-0x10]
    // 0x905e54: CheckStackOverflow
    //     0x905e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x905e58: cmp             SP, x16
    //     0x905e5c: b.ls            #0x905ef8
    // 0x905e60: InitAsync() -> Future<List<Wirid>>
    //     0x905e60: add             x0, PP, #0x29, lsl #12  ; [pp+0x29bc0] TypeArguments: <List<Wirid>>
    //     0x905e64: ldr             x0, [x0, #0xbc0]
    //     0x905e68: bl              #0x661298  ; InitAsyncStub
    // 0x905e6c: ldur            x1, [fp, #-0x10]
    // 0x905e70: r0 = init()
    //     0x905e70: bl              #0x8f1bb4  ; [package:nuonline/app/data/repositories/wirid_repository.dart] WiridRepository::init
    // 0x905e74: mov             x1, x0
    // 0x905e78: stur            x1, [fp, #-0x18]
    // 0x905e7c: r0 = Await()
    //     0x905e7c: bl              #0x661044  ; AwaitStub
    // 0x905e80: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x905e80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x905e84: ldr             x0, [x0, #0x2728]
    //     0x905e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x905e8c: cmp             w0, w16
    //     0x905e90: b.ne            #0x905e9c
    //     0x905e94: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x905e98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x905e9c: r16 = <Wirid>
    //     0x905e9c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0x905ea0: ldr             x16, [x16, #0x1c8]
    // 0x905ea4: stp             x0, x16, [SP, #8]
    // 0x905ea8: r16 = "v2_wirid"
    //     0x905ea8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0x905eac: ldr             x16, [x16, #0x1d8]
    // 0x905eb0: str             x16, [SP]
    // 0x905eb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x905eb4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x905eb8: r0 = box()
    //     0x905eb8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x905ebc: mov             x1, x0
    // 0x905ec0: stur            x0, [fp, #-0x10]
    // 0x905ec4: r0 = checkOpen()
    //     0x905ec4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x905ec8: ldur            x0, [fp, #-0x10]
    // 0x905ecc: LoadField: r1 = r0->field_1b
    //     0x905ecc: ldur            w1, [x0, #0x1b]
    // 0x905ed0: DecompressPointer r1
    //     0x905ed0: add             x1, x1, HEAP, lsl #32
    // 0x905ed4: r16 = Sentinel
    //     0x905ed4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x905ed8: cmp             w1, w16
    // 0x905edc: b.eq            #0x905f00
    // 0x905ee0: r0 = getValues()
    //     0x905ee0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x905ee4: LoadField: r1 = r0->field_7
    //     0x905ee4: ldur            w1, [x0, #7]
    // 0x905ee8: DecompressPointer r1
    //     0x905ee8: add             x1, x1, HEAP, lsl #32
    // 0x905eec: mov             x2, x0
    // 0x905ef0: r0 = _GrowableList.of()
    //     0x905ef0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x905ef4: r0 = ReturnAsyncNotFuture()
    //     0x905ef4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x905ef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905ef8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905efc: b               #0x905e60
    // 0x905f00: r9 = keystore
    //     0x905f00: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x905f04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x905f04: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ addWirid(/* No info */) async {
    // ** addr: 0xb53eb0, size: 0x1b0
    // 0xb53eb0: EnterFrame
    //     0xb53eb0: stp             fp, lr, [SP, #-0x10]!
    //     0xb53eb4: mov             fp, SP
    // 0xb53eb8: AllocStack(0x50)
    //     0xb53eb8: sub             SP, SP, #0x50
    // 0xb53ebc: SetupParameters(WiridRepository this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xb53ebc: stur            NULL, [fp, #-8]
    //     0xb53ec0: stur            x1, [fp, #-0x10]
    //     0xb53ec4: mov             x16, x2
    //     0xb53ec8: mov             x2, x1
    //     0xb53ecc: mov             x1, x16
    //     0xb53ed0: stur            x1, [fp, #-0x18]
    // 0xb53ed4: CheckStackOverflow
    //     0xb53ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb53ed8: cmp             SP, x16
    //     0xb53edc: b.ls            #0xb54048
    // 0xb53ee0: InitAsync() -> Future<List<Wirid>>
    //     0xb53ee0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29bc0] TypeArguments: <List<Wirid>>
    //     0xb53ee4: ldr             x0, [x0, #0xbc0]
    //     0xb53ee8: bl              #0x661298  ; InitAsyncStub
    // 0xb53eec: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb53eec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb53ef0: ldr             x0, [x0, #0x2728]
    //     0xb53ef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb53ef8: cmp             w0, w16
    //     0xb53efc: b.ne            #0xb53f08
    //     0xb53f00: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb53f04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb53f08: stur            x0, [fp, #-0x20]
    // 0xb53f0c: r16 = <Wirid>
    //     0xb53f0c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb53f10: ldr             x16, [x16, #0x1c8]
    // 0xb53f14: stp             x0, x16, [SP, #8]
    // 0xb53f18: r16 = "v2_wirid"
    //     0xb53f18: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb53f1c: ldr             x16, [x16, #0x1d8]
    // 0xb53f20: str             x16, [SP]
    // 0xb53f24: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb53f24: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb53f28: r0 = box()
    //     0xb53f28: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb53f2c: mov             x1, x0
    // 0xb53f30: stur            x0, [fp, #-0x28]
    // 0xb53f34: r0 = checkOpen()
    //     0xb53f34: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb53f38: ldur            x0, [fp, #-0x28]
    // 0xb53f3c: LoadField: r1 = r0->field_1b
    //     0xb53f3c: ldur            w1, [x0, #0x1b]
    // 0xb53f40: DecompressPointer r1
    //     0xb53f40: add             x1, x1, HEAP, lsl #32
    // 0xb53f44: r16 = Sentinel
    //     0xb53f44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb53f48: cmp             w1, w16
    // 0xb53f4c: b.eq            #0xb54050
    // 0xb53f50: LoadField: r0 = r1->field_13
    //     0xb53f50: ldur            w0, [x1, #0x13]
    // 0xb53f54: DecompressPointer r0
    //     0xb53f54: add             x0, x0, HEAP, lsl #32
    // 0xb53f58: LoadField: r1 = r0->field_1f
    //     0xb53f58: ldur            x1, [x0, #0x1f]
    // 0xb53f5c: cbnz            x1, #0xb53f68
    // 0xb53f60: r0 = 0
    //     0xb53f60: movz            x0, #0
    // 0xb53f64: b               #0xb53f6c
    // 0xb53f68: mov             x0, x1
    // 0xb53f6c: stur            x0, [fp, #-0x30]
    // 0xb53f70: r16 = <Wirid>
    //     0xb53f70: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb53f74: ldr             x16, [x16, #0x1c8]
    // 0xb53f78: ldur            lr, [fp, #-0x20]
    // 0xb53f7c: stp             lr, x16, [SP, #8]
    // 0xb53f80: r16 = "v2_wirid"
    //     0xb53f80: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb53f84: ldr             x16, [x16, #0x1d8]
    // 0xb53f88: str             x16, [SP]
    // 0xb53f8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb53f8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb53f90: r0 = box()
    //     0xb53f90: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb53f94: mov             x3, x0
    // 0xb53f98: ldur            x2, [fp, #-0x30]
    // 0xb53f9c: stur            x3, [fp, #-0x38]
    // 0xb53fa0: r0 = BoxInt64Instr(r2)
    //     0xb53fa0: sbfiz           x0, x2, #1, #0x1f
    //     0xb53fa4: cmp             x2, x0, asr #1
    //     0xb53fa8: b.eq            #0xb53fb4
    //     0xb53fac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb53fb0: stur            x2, [x0, #7]
    // 0xb53fb4: stur            x0, [fp, #-0x28]
    // 0xb53fb8: str             x0, [SP]
    // 0xb53fbc: ldur            x1, [fp, #-0x18]
    // 0xb53fc0: r4 = const [0, 0x2, 0x1, 0x1, order, 0x1, null]
    //     0xb53fc0: add             x4, PP, #0x29, lsl #12  ; [pp+0x29e10] List(7) [0, 0x2, 0x1, 0x1, "order", 0x1, Null]
    //     0xb53fc4: ldr             x4, [x4, #0xe10]
    // 0xb53fc8: r0 = copyWith()
    //     0xb53fc8: bl              #0x8f1d24  ; [package:nuonline/app/data/models/wirid.dart] Wirid::copyWith
    // 0xb53fcc: ldur            x1, [fp, #-0x38]
    // 0xb53fd0: ldur            x2, [fp, #-0x28]
    // 0xb53fd4: mov             x3, x0
    // 0xb53fd8: r0 = put()
    //     0xb53fd8: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb53fdc: mov             x1, x0
    // 0xb53fe0: stur            x1, [fp, #-0x18]
    // 0xb53fe4: r0 = Await()
    //     0xb53fe4: bl              #0x661044  ; AwaitStub
    // 0xb53fe8: r16 = <Wirid>
    //     0xb53fe8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb53fec: ldr             x16, [x16, #0x1c8]
    // 0xb53ff0: ldur            lr, [fp, #-0x20]
    // 0xb53ff4: stp             lr, x16, [SP, #8]
    // 0xb53ff8: r16 = "v2_wirid"
    //     0xb53ff8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb53ffc: ldr             x16, [x16, #0x1d8]
    // 0xb54000: str             x16, [SP]
    // 0xb54004: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb54004: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb54008: r0 = box()
    //     0xb54008: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb5400c: mov             x1, x0
    // 0xb54010: stur            x0, [fp, #-0x10]
    // 0xb54014: r0 = checkOpen()
    //     0xb54014: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb54018: ldur            x0, [fp, #-0x10]
    // 0xb5401c: LoadField: r1 = r0->field_1b
    //     0xb5401c: ldur            w1, [x0, #0x1b]
    // 0xb54020: DecompressPointer r1
    //     0xb54020: add             x1, x1, HEAP, lsl #32
    // 0xb54024: r16 = Sentinel
    //     0xb54024: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb54028: cmp             w1, w16
    // 0xb5402c: b.eq            #0xb54058
    // 0xb54030: r0 = getValues()
    //     0xb54030: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb54034: LoadField: r1 = r0->field_7
    //     0xb54034: ldur            w1, [x0, #7]
    // 0xb54038: DecompressPointer r1
    //     0xb54038: add             x1, x1, HEAP, lsl #32
    // 0xb5403c: mov             x2, x0
    // 0xb54040: r0 = _GrowableList.of()
    //     0xb54040: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb54044: r0 = ReturnAsyncNotFuture()
    //     0xb54044: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb54048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb54048: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5404c: b               #0xb53ee0
    // 0xb54050: r9 = keystore
    //     0xb54050: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb54054: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb54054: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb54058: r9 = keystore
    //     0xb54058: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb5405c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb5405c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ shiftOrder(/* No info */) async {
    // ** addr: 0xb54288, size: 0x7c0
    // 0xb54288: EnterFrame
    //     0xb54288: stp             fp, lr, [SP, #-0x10]!
    //     0xb5428c: mov             fp, SP
    // 0xb54290: AllocStack(0xa0)
    //     0xb54290: sub             SP, SP, #0xa0
    // 0xb54294: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xb54294: stur            NULL, [fp, #-8]
    //     0xb54298: stur            x1, [fp, #-0x10]
    //     0xb5429c: stur            x2, [fp, #-0x18]
    //     0xb542a0: stur            x3, [fp, #-0x20]
    // 0xb542a4: CheckStackOverflow
    //     0xb542a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb542a8: cmp             SP, x16
    //     0xb542ac: b.ls            #0xb54a18
    // 0xb542b0: InitAsync() -> Future<List<Wirid>>
    //     0xb542b0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29bc0] TypeArguments: <List<Wirid>>
    //     0xb542b4: ldr             x0, [x0, #0xbc0]
    //     0xb542b8: bl              #0x661298  ; InitAsyncStub
    // 0xb542bc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb542bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb542c0: ldr             x0, [x0, #0x2728]
    //     0xb542c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb542c8: cmp             w0, w16
    //     0xb542cc: b.ne            #0xb542d8
    //     0xb542d0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb542d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb542d8: stur            x0, [fp, #-0x28]
    // 0xb542dc: r16 = <Wirid>
    //     0xb542dc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb542e0: ldr             x16, [x16, #0x1c8]
    // 0xb542e4: stp             x0, x16, [SP, #8]
    // 0xb542e8: r16 = "v2_wirid"
    //     0xb542e8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb542ec: ldr             x16, [x16, #0x1d8]
    // 0xb542f0: str             x16, [SP]
    // 0xb542f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb542f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb542f8: r0 = box()
    //     0xb542f8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb542fc: mov             x2, x0
    // 0xb54300: ldur            x3, [fp, #-0x18]
    // 0xb54304: r0 = BoxInt64Instr(r3)
    //     0xb54304: sbfiz           x0, x3, #1, #0x1f
    //     0xb54308: cmp             x3, x0, asr #1
    //     0xb5430c: b.eq            #0xb54318
    //     0xb54310: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb54314: stur            x3, [x0, #7]
    // 0xb54318: mov             x1, x2
    // 0xb5431c: mov             x2, x0
    // 0xb54320: r0 = get()
    //     0xb54320: bl              #0x68f3ac  ; [package:hive/src/box/box_impl.dart] BoxImpl::get
    // 0xb54324: stur            x0, [fp, #-0x30]
    // 0xb54328: cmp             w0, NULL
    // 0xb5432c: b.eq            #0xb549b8
    // 0xb54330: ldur            x2, [fp, #-0x18]
    // 0xb54334: ldur            x1, [fp, #-0x20]
    // 0xb54338: r16 = <Wirid>
    //     0xb54338: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb5433c: ldr             x16, [x16, #0x1c8]
    // 0xb54340: ldur            lr, [fp, #-0x28]
    // 0xb54344: stp             lr, x16, [SP, #8]
    // 0xb54348: r16 = "v2_wirid"
    //     0xb54348: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb5434c: ldr             x16, [x16, #0x1d8]
    // 0xb54350: str             x16, [SP]
    // 0xb54354: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb54354: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb54358: r0 = box()
    //     0xb54358: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb5435c: mov             x1, x0
    // 0xb54360: ldur            x2, [fp, #-0x18]
    // 0xb54364: r0 = delete()
    //     0xb54364: bl              #0xa424f8  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::delete
    // 0xb54368: mov             x1, x0
    // 0xb5436c: stur            x1, [fp, #-0x38]
    // 0xb54370: r0 = Await()
    //     0xb54370: bl              #0x661044  ; AwaitStub
    // 0xb54374: ldur            x0, [fp, #-0x18]
    // 0xb54378: ldur            x1, [fp, #-0x20]
    // 0xb5437c: cmp             x0, x1
    // 0xb54380: b.ge            #0xb546a4
    // 0xb54384: sub             x2, x1, #1
    // 0xb54388: stur            x2, [fp, #-0x48]
    // 0xb5438c: add             x1, x0, #1
    // 0xb54390: mov             x0, x1
    // 0xb54394: stur            x0, [fp, #-0x40]
    // 0xb54398: CheckStackOverflow
    //     0xb54398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5439c: cmp             SP, x16
    //     0xb543a0: b.ls            #0xb54a20
    // 0xb543a4: cmp             x0, x2
    // 0xb543a8: b.gt            #0xb54624
    // 0xb543ac: r16 = <Wirid>
    //     0xb543ac: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb543b0: ldr             x16, [x16, #0x1c8]
    // 0xb543b4: ldur            lr, [fp, #-0x28]
    // 0xb543b8: stp             lr, x16, [SP, #8]
    // 0xb543bc: r16 = "v2_wirid"
    //     0xb543bc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb543c0: ldr             x16, [x16, #0x1d8]
    // 0xb543c4: str             x16, [SP]
    // 0xb543c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb543c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb543cc: r0 = box()
    //     0xb543cc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb543d0: mov             x1, x0
    // 0xb543d4: stur            x0, [fp, #-0x38]
    // 0xb543d8: r0 = checkOpen()
    //     0xb543d8: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb543dc: ldur            x3, [fp, #-0x38]
    // 0xb543e0: LoadField: r0 = r3->field_1b
    //     0xb543e0: ldur            w0, [x3, #0x1b]
    // 0xb543e4: DecompressPointer r0
    //     0xb543e4: add             x0, x0, HEAP, lsl #32
    // 0xb543e8: r16 = Sentinel
    //     0xb543e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb543ec: cmp             w0, w16
    // 0xb543f0: b.eq            #0xb54a28
    // 0xb543f4: LoadField: r2 = r0->field_13
    //     0xb543f4: ldur            w2, [x0, #0x13]
    // 0xb543f8: DecompressPointer r2
    //     0xb543f8: add             x2, x2, HEAP, lsl #32
    // 0xb543fc: ldur            x4, [fp, #-0x40]
    // 0xb54400: r0 = BoxInt64Instr(r4)
    //     0xb54400: sbfiz           x0, x4, #1, #0x1f
    //     0xb54404: cmp             x4, x0, asr #1
    //     0xb54408: b.eq            #0xb54414
    //     0xb5440c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb54410: stur            x4, [x0, #7]
    // 0xb54414: mov             x1, x2
    // 0xb54418: mov             x2, x0
    // 0xb5441c: r0 = _getNode()
    //     0xb5441c: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0xb54420: cmp             w0, NULL
    // 0xb54424: b.ne            #0xb54430
    // 0xb54428: r0 = Null
    //     0xb54428: mov             x0, NULL
    // 0xb5442c: b               #0xb5443c
    // 0xb54430: LoadField: r1 = r0->field_f
    //     0xb54430: ldur            w1, [x0, #0xf]
    // 0xb54434: DecompressPointer r1
    //     0xb54434: add             x1, x1, HEAP, lsl #32
    // 0xb54438: mov             x0, x1
    // 0xb5443c: cmp             w0, NULL
    // 0xb54440: b.eq            #0xb54498
    // 0xb54444: ldur            x1, [fp, #-0x38]
    // 0xb54448: LoadField: r3 = r0->field_b
    //     0xb54448: ldur            w3, [x0, #0xb]
    // 0xb5444c: DecompressPointer r3
    //     0xb5444c: add             x3, x3, HEAP, lsl #32
    // 0xb54450: stur            x3, [fp, #-0x50]
    // 0xb54454: LoadField: r2 = r1->field_7
    //     0xb54454: ldur            w2, [x1, #7]
    // 0xb54458: DecompressPointer r2
    //     0xb54458: add             x2, x2, HEAP, lsl #32
    // 0xb5445c: mov             x0, x3
    // 0xb54460: r1 = Null
    //     0xb54460: mov             x1, NULL
    // 0xb54464: cmp             w0, NULL
    // 0xb54468: b.eq            #0xb54490
    // 0xb5446c: cmp             w2, NULL
    // 0xb54470: b.eq            #0xb54490
    // 0xb54474: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb54474: ldur            w4, [x2, #0x17]
    // 0xb54478: DecompressPointer r4
    //     0xb54478: add             x4, x4, HEAP, lsl #32
    // 0xb5447c: r8 = X0?
    //     0xb5447c: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0xb54480: LoadField: r9 = r4->field_7
    //     0xb54480: ldur            x9, [x4, #7]
    // 0xb54484: r3 = Null
    //     0xb54484: add             x3, PP, #0x29, lsl #12  ; [pp+0x29e28] Null
    //     0xb54488: ldr             x3, [x3, #0xe28]
    // 0xb5448c: blr             x9
    // 0xb54490: ldur            x0, [fp, #-0x50]
    // 0xb54494: b               #0xb5449c
    // 0xb54498: r0 = Null
    //     0xb54498: mov             x0, NULL
    // 0xb5449c: stur            x0, [fp, #-0x38]
    // 0xb544a0: cmp             w0, NULL
    // 0xb544a4: b.eq            #0xb54610
    // 0xb544a8: ldur            x1, [fp, #-0x40]
    // 0xb544ac: r16 = <Wirid>
    //     0xb544ac: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb544b0: ldr             x16, [x16, #0x1c8]
    // 0xb544b4: ldur            lr, [fp, #-0x28]
    // 0xb544b8: stp             lr, x16, [SP, #8]
    // 0xb544bc: r16 = "v2_wirid"
    //     0xb544bc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb544c0: ldr             x16, [x16, #0x1d8]
    // 0xb544c4: str             x16, [SP]
    // 0xb544c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb544c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb544cc: r0 = box()
    //     0xb544cc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb544d0: mov             x3, x0
    // 0xb544d4: ldur            x2, [fp, #-0x40]
    // 0xb544d8: stur            x3, [fp, #-0x80]
    // 0xb544dc: sub             x4, x2, #1
    // 0xb544e0: stur            x4, [fp, #-0x78]
    // 0xb544e4: r0 = BoxInt64Instr(r4)
    //     0xb544e4: sbfiz           x0, x4, #1, #0x1f
    //     0xb544e8: cmp             x4, x0, asr #1
    //     0xb544ec: b.eq            #0xb544f8
    //     0xb544f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb544f4: stur            x4, [x0, #7]
    // 0xb544f8: mov             x1, x0
    // 0xb544fc: ldur            x0, [fp, #-0x38]
    // 0xb54500: stur            x1, [fp, #-0x70]
    // 0xb54504: LoadField: r5 = r0->field_f
    //     0xb54504: ldur            w5, [x0, #0xf]
    // 0xb54508: DecompressPointer r5
    //     0xb54508: add             x5, x5, HEAP, lsl #32
    // 0xb5450c: stur            x5, [fp, #-0x68]
    // 0xb54510: LoadField: r6 = r0->field_13
    //     0xb54510: ldur            w6, [x0, #0x13]
    // 0xb54514: DecompressPointer r6
    //     0xb54514: add             x6, x6, HEAP, lsl #32
    // 0xb54518: stur            x6, [fp, #-0x50]
    // 0xb5451c: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xb5451c: ldur            x7, [x0, #0x17]
    // 0xb54520: stur            x7, [fp, #-0x60]
    // 0xb54524: LoadField: r8 = r0->field_1f
    //     0xb54524: ldur            x8, [x0, #0x1f]
    // 0xb54528: stur            x8, [fp, #-0x58]
    // 0xb5452c: r0 = Wirid()
    //     0xb5452c: bl              #0x8f1ed8  ; AllocateWiridStub -> Wirid (size=0x28)
    // 0xb54530: mov             x3, x0
    // 0xb54534: ldur            x0, [fp, #-0x78]
    // 0xb54538: stur            x3, [fp, #-0x88]
    // 0xb5453c: StoreField: r3->field_7 = r0
    //     0xb5453c: stur            x0, [x3, #7]
    // 0xb54540: ldur            x0, [fp, #-0x68]
    // 0xb54544: StoreField: r3->field_f = r0
    //     0xb54544: stur            w0, [x3, #0xf]
    // 0xb54548: ldur            x0, [fp, #-0x50]
    // 0xb5454c: StoreField: r3->field_13 = r0
    //     0xb5454c: stur            w0, [x3, #0x13]
    // 0xb54550: ldur            x0, [fp, #-0x60]
    // 0xb54554: ArrayStore: r3[0] = r0  ; List_8
    //     0xb54554: stur            x0, [x3, #0x17]
    // 0xb54558: ldur            x0, [fp, #-0x58]
    // 0xb5455c: StoreField: r3->field_1f = r0
    //     0xb5455c: stur            x0, [x3, #0x1f]
    // 0xb54560: ldur            x4, [fp, #-0x80]
    // 0xb54564: LoadField: r5 = r4->field_7
    //     0xb54564: ldur            w5, [x4, #7]
    // 0xb54568: DecompressPointer r5
    //     0xb54568: add             x5, x5, HEAP, lsl #32
    // 0xb5456c: mov             x0, x3
    // 0xb54570: mov             x2, x5
    // 0xb54574: stur            x5, [fp, #-0x38]
    // 0xb54578: r1 = Null
    //     0xb54578: mov             x1, NULL
    // 0xb5457c: cmp             w2, NULL
    // 0xb54580: b.eq            #0xb545a0
    // 0xb54584: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb54584: ldur            w4, [x2, #0x17]
    // 0xb54588: DecompressPointer r4
    //     0xb54588: add             x4, x4, HEAP, lsl #32
    // 0xb5458c: r8 = X0
    //     0xb5458c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb54590: LoadField: r9 = r4->field_7
    //     0xb54590: ldur            x9, [x4, #7]
    // 0xb54594: r3 = Null
    //     0xb54594: add             x3, PP, #0x29, lsl #12  ; [pp+0x29e38] Null
    //     0xb54598: ldr             x3, [x3, #0xe38]
    // 0xb5459c: blr             x9
    // 0xb545a0: ldur            x2, [fp, #-0x38]
    // 0xb545a4: r1 = Null
    //     0xb545a4: mov             x1, NULL
    // 0xb545a8: r3 = <dynamic, X0>
    //     0xb545a8: add             x3, PP, #8, lsl #12  ; [pp+0x82d0] TypeArguments: <dynamic, X0>
    //     0xb545ac: ldr             x3, [x3, #0x2d0]
    // 0xb545b0: r0 = Null
    //     0xb545b0: mov             x0, NULL
    // 0xb545b4: cmp             x2, x0
    // 0xb545b8: b.eq            #0xb545c8
    // 0xb545bc: r30 = InstantiateTypeArgumentsStub
    //     0xb545bc: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xb545c0: LoadField: r30 = r30->field_7
    //     0xb545c0: ldur            lr, [lr, #7]
    // 0xb545c4: blr             lr
    // 0xb545c8: r1 = Null
    //     0xb545c8: mov             x1, NULL
    // 0xb545cc: r2 = 4
    //     0xb545cc: movz            x2, #0x4
    // 0xb545d0: stur            x0, [fp, #-0x38]
    // 0xb545d4: r0 = AllocateArray()
    //     0xb545d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb545d8: mov             x1, x0
    // 0xb545dc: ldur            x0, [fp, #-0x70]
    // 0xb545e0: StoreField: r1->field_f = r0
    //     0xb545e0: stur            w0, [x1, #0xf]
    // 0xb545e4: ldur            x0, [fp, #-0x88]
    // 0xb545e8: StoreField: r1->field_13 = r0
    //     0xb545e8: stur            w0, [x1, #0x13]
    // 0xb545ec: ldur            x16, [fp, #-0x38]
    // 0xb545f0: stp             x1, x16, [SP]
    // 0xb545f4: r0 = Map._fromLiteral()
    //     0xb545f4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb545f8: ldur            x1, [fp, #-0x80]
    // 0xb545fc: mov             x2, x0
    // 0xb54600: r0 = putAll()
    //     0xb54600: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xb54604: mov             x1, x0
    // 0xb54608: stur            x1, [fp, #-0x38]
    // 0xb5460c: r0 = Await()
    //     0xb5460c: bl              #0x661044  ; AwaitStub
    // 0xb54610: ldur            x0, [fp, #-0x40]
    // 0xb54614: add             x1, x0, #1
    // 0xb54618: mov             x0, x1
    // 0xb5461c: ldur            x2, [fp, #-0x48]
    // 0xb54620: b               #0xb54394
    // 0xb54624: mov             x0, x2
    // 0xb54628: r16 = <Wirid>
    //     0xb54628: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb5462c: ldr             x16, [x16, #0x1c8]
    // 0xb54630: ldur            lr, [fp, #-0x28]
    // 0xb54634: stp             lr, x16, [SP, #8]
    // 0xb54638: r16 = "v2_wirid"
    //     0xb54638: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb5463c: ldr             x16, [x16, #0x1d8]
    // 0xb54640: str             x16, [SP]
    // 0xb54644: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb54644: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb54648: r0 = box()
    //     0xb54648: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb5464c: mov             x3, x0
    // 0xb54650: ldur            x2, [fp, #-0x48]
    // 0xb54654: stur            x3, [fp, #-0x50]
    // 0xb54658: r0 = BoxInt64Instr(r2)
    //     0xb54658: sbfiz           x0, x2, #1, #0x1f
    //     0xb5465c: cmp             x2, x0, asr #1
    //     0xb54660: b.eq            #0xb5466c
    //     0xb54664: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb54668: stur            x2, [x0, #7]
    // 0xb5466c: stur            x0, [fp, #-0x38]
    // 0xb54670: str             x0, [SP]
    // 0xb54674: ldur            x1, [fp, #-0x30]
    // 0xb54678: r4 = const [0, 0x2, 0x1, 0x1, order, 0x1, null]
    //     0xb54678: add             x4, PP, #0x29, lsl #12  ; [pp+0x29e10] List(7) [0, 0x2, 0x1, 0x1, "order", 0x1, Null]
    //     0xb5467c: ldr             x4, [x4, #0xe10]
    // 0xb54680: r0 = copyWith()
    //     0xb54680: bl              #0x8f1d24  ; [package:nuonline/app/data/models/wirid.dart] Wirid::copyWith
    // 0xb54684: ldur            x1, [fp, #-0x50]
    // 0xb54688: ldur            x2, [fp, #-0x38]
    // 0xb5468c: mov             x3, x0
    // 0xb54690: r0 = put()
    //     0xb54690: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb54694: mov             x1, x0
    // 0xb54698: stur            x1, [fp, #-0x38]
    // 0xb5469c: r0 = Await()
    //     0xb5469c: bl              #0x661044  ; AwaitStub
    // 0xb546a0: b               #0xb549b8
    // 0xb546a4: sub             x2, x0, #1
    // 0xb546a8: mov             x0, x2
    // 0xb546ac: stur            x0, [fp, #-0x18]
    // 0xb546b0: CheckStackOverflow
    //     0xb546b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb546b4: cmp             SP, x16
    //     0xb546b8: b.ls            #0xb54a30
    // 0xb546bc: cmp             x0, x1
    // 0xb546c0: b.lt            #0xb5493c
    // 0xb546c4: r16 = <Wirid>
    //     0xb546c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb546c8: ldr             x16, [x16, #0x1c8]
    // 0xb546cc: ldur            lr, [fp, #-0x28]
    // 0xb546d0: stp             lr, x16, [SP, #8]
    // 0xb546d4: r16 = "v2_wirid"
    //     0xb546d4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb546d8: ldr             x16, [x16, #0x1d8]
    // 0xb546dc: str             x16, [SP]
    // 0xb546e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb546e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb546e4: r0 = box()
    //     0xb546e4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb546e8: mov             x1, x0
    // 0xb546ec: stur            x0, [fp, #-0x38]
    // 0xb546f0: r0 = checkOpen()
    //     0xb546f0: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb546f4: ldur            x3, [fp, #-0x38]
    // 0xb546f8: LoadField: r0 = r3->field_1b
    //     0xb546f8: ldur            w0, [x3, #0x1b]
    // 0xb546fc: DecompressPointer r0
    //     0xb546fc: add             x0, x0, HEAP, lsl #32
    // 0xb54700: r16 = Sentinel
    //     0xb54700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb54704: cmp             w0, w16
    // 0xb54708: b.eq            #0xb54a38
    // 0xb5470c: LoadField: r2 = r0->field_13
    //     0xb5470c: ldur            w2, [x0, #0x13]
    // 0xb54710: DecompressPointer r2
    //     0xb54710: add             x2, x2, HEAP, lsl #32
    // 0xb54714: ldur            x4, [fp, #-0x18]
    // 0xb54718: r0 = BoxInt64Instr(r4)
    //     0xb54718: sbfiz           x0, x4, #1, #0x1f
    //     0xb5471c: cmp             x4, x0, asr #1
    //     0xb54720: b.eq            #0xb5472c
    //     0xb54724: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb54728: stur            x4, [x0, #7]
    // 0xb5472c: mov             x1, x2
    // 0xb54730: mov             x2, x0
    // 0xb54734: r0 = _getNode()
    //     0xb54734: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0xb54738: cmp             w0, NULL
    // 0xb5473c: b.ne            #0xb54748
    // 0xb54740: r0 = Null
    //     0xb54740: mov             x0, NULL
    // 0xb54744: b               #0xb54754
    // 0xb54748: LoadField: r1 = r0->field_f
    //     0xb54748: ldur            w1, [x0, #0xf]
    // 0xb5474c: DecompressPointer r1
    //     0xb5474c: add             x1, x1, HEAP, lsl #32
    // 0xb54750: mov             x0, x1
    // 0xb54754: cmp             w0, NULL
    // 0xb54758: b.eq            #0xb547b0
    // 0xb5475c: ldur            x1, [fp, #-0x38]
    // 0xb54760: LoadField: r3 = r0->field_b
    //     0xb54760: ldur            w3, [x0, #0xb]
    // 0xb54764: DecompressPointer r3
    //     0xb54764: add             x3, x3, HEAP, lsl #32
    // 0xb54768: stur            x3, [fp, #-0x50]
    // 0xb5476c: LoadField: r2 = r1->field_7
    //     0xb5476c: ldur            w2, [x1, #7]
    // 0xb54770: DecompressPointer r2
    //     0xb54770: add             x2, x2, HEAP, lsl #32
    // 0xb54774: mov             x0, x3
    // 0xb54778: r1 = Null
    //     0xb54778: mov             x1, NULL
    // 0xb5477c: cmp             w0, NULL
    // 0xb54780: b.eq            #0xb547a8
    // 0xb54784: cmp             w2, NULL
    // 0xb54788: b.eq            #0xb547a8
    // 0xb5478c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb5478c: ldur            w4, [x2, #0x17]
    // 0xb54790: DecompressPointer r4
    //     0xb54790: add             x4, x4, HEAP, lsl #32
    // 0xb54794: r8 = X0?
    //     0xb54794: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0xb54798: LoadField: r9 = r4->field_7
    //     0xb54798: ldur            x9, [x4, #7]
    // 0xb5479c: r3 = Null
    //     0xb5479c: add             x3, PP, #0x29, lsl #12  ; [pp+0x29e48] Null
    //     0xb547a0: ldr             x3, [x3, #0xe48]
    // 0xb547a4: blr             x9
    // 0xb547a8: ldur            x0, [fp, #-0x50]
    // 0xb547ac: b               #0xb547b4
    // 0xb547b0: r0 = Null
    //     0xb547b0: mov             x0, NULL
    // 0xb547b4: stur            x0, [fp, #-0x38]
    // 0xb547b8: cmp             w0, NULL
    // 0xb547bc: b.eq            #0xb54928
    // 0xb547c0: ldur            x1, [fp, #-0x18]
    // 0xb547c4: r16 = <Wirid>
    //     0xb547c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb547c8: ldr             x16, [x16, #0x1c8]
    // 0xb547cc: ldur            lr, [fp, #-0x28]
    // 0xb547d0: stp             lr, x16, [SP, #8]
    // 0xb547d4: r16 = "v2_wirid"
    //     0xb547d4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb547d8: ldr             x16, [x16, #0x1d8]
    // 0xb547dc: str             x16, [SP]
    // 0xb547e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb547e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb547e4: r0 = box()
    //     0xb547e4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb547e8: mov             x3, x0
    // 0xb547ec: ldur            x2, [fp, #-0x18]
    // 0xb547f0: stur            x3, [fp, #-0x80]
    // 0xb547f4: add             x4, x2, #1
    // 0xb547f8: stur            x4, [fp, #-0x58]
    // 0xb547fc: r0 = BoxInt64Instr(r4)
    //     0xb547fc: sbfiz           x0, x4, #1, #0x1f
    //     0xb54800: cmp             x4, x0, asr #1
    //     0xb54804: b.eq            #0xb54810
    //     0xb54808: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5480c: stur            x4, [x0, #7]
    // 0xb54810: mov             x1, x0
    // 0xb54814: ldur            x0, [fp, #-0x38]
    // 0xb54818: stur            x1, [fp, #-0x70]
    // 0xb5481c: LoadField: r5 = r0->field_f
    //     0xb5481c: ldur            w5, [x0, #0xf]
    // 0xb54820: DecompressPointer r5
    //     0xb54820: add             x5, x5, HEAP, lsl #32
    // 0xb54824: stur            x5, [fp, #-0x68]
    // 0xb54828: LoadField: r6 = r0->field_13
    //     0xb54828: ldur            w6, [x0, #0x13]
    // 0xb5482c: DecompressPointer r6
    //     0xb5482c: add             x6, x6, HEAP, lsl #32
    // 0xb54830: stur            x6, [fp, #-0x50]
    // 0xb54834: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xb54834: ldur            x7, [x0, #0x17]
    // 0xb54838: stur            x7, [fp, #-0x48]
    // 0xb5483c: LoadField: r8 = r0->field_1f
    //     0xb5483c: ldur            x8, [x0, #0x1f]
    // 0xb54840: stur            x8, [fp, #-0x40]
    // 0xb54844: r0 = Wirid()
    //     0xb54844: bl              #0x8f1ed8  ; AllocateWiridStub -> Wirid (size=0x28)
    // 0xb54848: mov             x3, x0
    // 0xb5484c: ldur            x0, [fp, #-0x58]
    // 0xb54850: stur            x3, [fp, #-0x88]
    // 0xb54854: StoreField: r3->field_7 = r0
    //     0xb54854: stur            x0, [x3, #7]
    // 0xb54858: ldur            x0, [fp, #-0x68]
    // 0xb5485c: StoreField: r3->field_f = r0
    //     0xb5485c: stur            w0, [x3, #0xf]
    // 0xb54860: ldur            x0, [fp, #-0x50]
    // 0xb54864: StoreField: r3->field_13 = r0
    //     0xb54864: stur            w0, [x3, #0x13]
    // 0xb54868: ldur            x0, [fp, #-0x48]
    // 0xb5486c: ArrayStore: r3[0] = r0  ; List_8
    //     0xb5486c: stur            x0, [x3, #0x17]
    // 0xb54870: ldur            x0, [fp, #-0x40]
    // 0xb54874: StoreField: r3->field_1f = r0
    //     0xb54874: stur            x0, [x3, #0x1f]
    // 0xb54878: ldur            x4, [fp, #-0x80]
    // 0xb5487c: LoadField: r5 = r4->field_7
    //     0xb5487c: ldur            w5, [x4, #7]
    // 0xb54880: DecompressPointer r5
    //     0xb54880: add             x5, x5, HEAP, lsl #32
    // 0xb54884: mov             x0, x3
    // 0xb54888: mov             x2, x5
    // 0xb5488c: stur            x5, [fp, #-0x38]
    // 0xb54890: r1 = Null
    //     0xb54890: mov             x1, NULL
    // 0xb54894: cmp             w2, NULL
    // 0xb54898: b.eq            #0xb548b8
    // 0xb5489c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb5489c: ldur            w4, [x2, #0x17]
    // 0xb548a0: DecompressPointer r4
    //     0xb548a0: add             x4, x4, HEAP, lsl #32
    // 0xb548a4: r8 = X0
    //     0xb548a4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb548a8: LoadField: r9 = r4->field_7
    //     0xb548a8: ldur            x9, [x4, #7]
    // 0xb548ac: r3 = Null
    //     0xb548ac: add             x3, PP, #0x29, lsl #12  ; [pp+0x29e58] Null
    //     0xb548b0: ldr             x3, [x3, #0xe58]
    // 0xb548b4: blr             x9
    // 0xb548b8: ldur            x2, [fp, #-0x38]
    // 0xb548bc: r1 = Null
    //     0xb548bc: mov             x1, NULL
    // 0xb548c0: r3 = <dynamic, X0>
    //     0xb548c0: add             x3, PP, #8, lsl #12  ; [pp+0x82d0] TypeArguments: <dynamic, X0>
    //     0xb548c4: ldr             x3, [x3, #0x2d0]
    // 0xb548c8: r0 = Null
    //     0xb548c8: mov             x0, NULL
    // 0xb548cc: cmp             x2, x0
    // 0xb548d0: b.eq            #0xb548e0
    // 0xb548d4: r30 = InstantiateTypeArgumentsStub
    //     0xb548d4: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xb548d8: LoadField: r30 = r30->field_7
    //     0xb548d8: ldur            lr, [lr, #7]
    // 0xb548dc: blr             lr
    // 0xb548e0: r1 = Null
    //     0xb548e0: mov             x1, NULL
    // 0xb548e4: r2 = 4
    //     0xb548e4: movz            x2, #0x4
    // 0xb548e8: stur            x0, [fp, #-0x38]
    // 0xb548ec: r0 = AllocateArray()
    //     0xb548ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb548f0: mov             x1, x0
    // 0xb548f4: ldur            x0, [fp, #-0x70]
    // 0xb548f8: StoreField: r1->field_f = r0
    //     0xb548f8: stur            w0, [x1, #0xf]
    // 0xb548fc: ldur            x0, [fp, #-0x88]
    // 0xb54900: StoreField: r1->field_13 = r0
    //     0xb54900: stur            w0, [x1, #0x13]
    // 0xb54904: ldur            x16, [fp, #-0x38]
    // 0xb54908: stp             x1, x16, [SP]
    // 0xb5490c: r0 = Map._fromLiteral()
    //     0xb5490c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb54910: ldur            x1, [fp, #-0x80]
    // 0xb54914: mov             x2, x0
    // 0xb54918: r0 = putAll()
    //     0xb54918: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xb5491c: mov             x1, x0
    // 0xb54920: stur            x1, [fp, #-0x38]
    // 0xb54924: r0 = Await()
    //     0xb54924: bl              #0x661044  ; AwaitStub
    // 0xb54928: ldur            x0, [fp, #-0x18]
    // 0xb5492c: sub             x1, x0, #1
    // 0xb54930: mov             x0, x1
    // 0xb54934: ldur            x1, [fp, #-0x20]
    // 0xb54938: b               #0xb546ac
    // 0xb5493c: mov             x0, x1
    // 0xb54940: r16 = <Wirid>
    //     0xb54940: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb54944: ldr             x16, [x16, #0x1c8]
    // 0xb54948: ldur            lr, [fp, #-0x28]
    // 0xb5494c: stp             lr, x16, [SP, #8]
    // 0xb54950: r16 = "v2_wirid"
    //     0xb54950: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb54954: ldr             x16, [x16, #0x1d8]
    // 0xb54958: str             x16, [SP]
    // 0xb5495c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5495c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb54960: r0 = box()
    //     0xb54960: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb54964: mov             x3, x0
    // 0xb54968: ldur            x2, [fp, #-0x20]
    // 0xb5496c: stur            x3, [fp, #-0x50]
    // 0xb54970: r0 = BoxInt64Instr(r2)
    //     0xb54970: sbfiz           x0, x2, #1, #0x1f
    //     0xb54974: cmp             x2, x0, asr #1
    //     0xb54978: b.eq            #0xb54984
    //     0xb5497c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb54980: stur            x2, [x0, #7]
    // 0xb54984: stur            x0, [fp, #-0x38]
    // 0xb54988: str             x0, [SP]
    // 0xb5498c: ldur            x1, [fp, #-0x30]
    // 0xb54990: r4 = const [0, 0x2, 0x1, 0x1, order, 0x1, null]
    //     0xb54990: add             x4, PP, #0x29, lsl #12  ; [pp+0x29e10] List(7) [0, 0x2, 0x1, 0x1, "order", 0x1, Null]
    //     0xb54994: ldr             x4, [x4, #0xe10]
    // 0xb54998: r0 = copyWith()
    //     0xb54998: bl              #0x8f1d24  ; [package:nuonline/app/data/models/wirid.dart] Wirid::copyWith
    // 0xb5499c: ldur            x1, [fp, #-0x50]
    // 0xb549a0: ldur            x2, [fp, #-0x38]
    // 0xb549a4: mov             x3, x0
    // 0xb549a8: r0 = put()
    //     0xb549a8: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb549ac: mov             x1, x0
    // 0xb549b0: stur            x1, [fp, #-0x30]
    // 0xb549b4: r0 = Await()
    //     0xb549b4: bl              #0x661044  ; AwaitStub
    // 0xb549b8: r16 = <Wirid>
    //     0xb549b8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb549bc: ldr             x16, [x16, #0x1c8]
    // 0xb549c0: ldur            lr, [fp, #-0x28]
    // 0xb549c4: stp             lr, x16, [SP, #8]
    // 0xb549c8: r16 = "v2_wirid"
    //     0xb549c8: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb549cc: ldr             x16, [x16, #0x1d8]
    // 0xb549d0: str             x16, [SP]
    // 0xb549d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb549d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb549d8: r0 = box()
    //     0xb549d8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb549dc: mov             x1, x0
    // 0xb549e0: stur            x0, [fp, #-0x10]
    // 0xb549e4: r0 = checkOpen()
    //     0xb549e4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb549e8: ldur            x0, [fp, #-0x10]
    // 0xb549ec: LoadField: r1 = r0->field_1b
    //     0xb549ec: ldur            w1, [x0, #0x1b]
    // 0xb549f0: DecompressPointer r1
    //     0xb549f0: add             x1, x1, HEAP, lsl #32
    // 0xb549f4: r16 = Sentinel
    //     0xb549f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb549f8: cmp             w1, w16
    // 0xb549fc: b.eq            #0xb54a40
    // 0xb54a00: r0 = getValues()
    //     0xb54a00: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb54a04: LoadField: r1 = r0->field_7
    //     0xb54a04: ldur            w1, [x0, #7]
    // 0xb54a08: DecompressPointer r1
    //     0xb54a08: add             x1, x1, HEAP, lsl #32
    // 0xb54a0c: mov             x2, x0
    // 0xb54a10: r0 = _GrowableList.of()
    //     0xb54a10: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb54a14: r0 = ReturnAsyncNotFuture()
    //     0xb54a14: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb54a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb54a18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb54a1c: b               #0xb542b0
    // 0xb54a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb54a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb54a24: b               #0xb543a4
    // 0xb54a28: r9 = keystore
    //     0xb54a28: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb54a2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb54a2c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb54a30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb54a30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb54a34: b               #0xb546bc
    // 0xb54a38: r9 = keystore
    //     0xb54a38: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb54a3c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb54a3c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb54a40: r9 = keystore
    //     0xb54a40: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb54a44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb54a44: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ deleteWirid(/* No info */) async {
    // ** addr: 0xb55388, size: 0x460
    // 0xb55388: EnterFrame
    //     0xb55388: stp             fp, lr, [SP, #-0x10]!
    //     0xb5538c: mov             fp, SP
    // 0xb55390: AllocStack(0x80)
    //     0xb55390: sub             SP, SP, #0x80
    // 0xb55394: SetupParameters(WiridRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xb55394: stur            NULL, [fp, #-8]
    //     0xb55398: stur            x1, [fp, #-0x10]
    //     0xb5539c: stur            x2, [fp, #-0x18]
    // 0xb553a0: CheckStackOverflow
    //     0xb553a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb553a4: cmp             SP, x16
    //     0xb553a8: b.ls            #0xb557b8
    // 0xb553ac: InitAsync() -> Future<List<Wirid>>
    //     0xb553ac: add             x0, PP, #0x29, lsl #12  ; [pp+0x29bc0] TypeArguments: <List<Wirid>>
    //     0xb553b0: ldr             x0, [x0, #0xbc0]
    //     0xb553b4: bl              #0x661298  ; InitAsyncStub
    // 0xb553b8: ldur            x0, [fp, #-0x18]
    // 0xb553bc: add             x1, x0, #1
    // 0xb553c0: mov             x0, x1
    // 0xb553c4: stur            x0, [fp, #-0x18]
    // 0xb553c8: CheckStackOverflow
    //     0xb553c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb553cc: cmp             SP, x16
    //     0xb553d0: b.ls            #0xb557c0
    // 0xb553d4: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb553d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb553d8: ldr             x0, [x0, #0x2728]
    //     0xb553dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb553e0: cmp             w0, w16
    //     0xb553e4: b.ne            #0xb553f0
    //     0xb553e8: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb553ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb553f0: stur            x0, [fp, #-0x20]
    // 0xb553f4: r16 = <Wirid>
    //     0xb553f4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb553f8: ldr             x16, [x16, #0x1c8]
    // 0xb553fc: stp             x0, x16, [SP, #8]
    // 0xb55400: r16 = "v2_wirid"
    //     0xb55400: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb55404: ldr             x16, [x16, #0x1d8]
    // 0xb55408: str             x16, [SP]
    // 0xb5540c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5540c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb55410: r0 = box()
    //     0xb55410: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb55414: mov             x1, x0
    // 0xb55418: stur            x0, [fp, #-0x28]
    // 0xb5541c: r0 = checkOpen()
    //     0xb5541c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb55420: ldur            x0, [fp, #-0x28]
    // 0xb55424: LoadField: r1 = r0->field_1b
    //     0xb55424: ldur            w1, [x0, #0x1b]
    // 0xb55428: DecompressPointer r1
    //     0xb55428: add             x1, x1, HEAP, lsl #32
    // 0xb5542c: r16 = Sentinel
    //     0xb5542c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb55430: cmp             w1, w16
    // 0xb55434: b.eq            #0xb557c8
    // 0xb55438: LoadField: r0 = r1->field_13
    //     0xb55438: ldur            w0, [x1, #0x13]
    // 0xb5543c: DecompressPointer r0
    //     0xb5543c: add             x0, x0, HEAP, lsl #32
    // 0xb55440: LoadField: r1 = r0->field_1f
    //     0xb55440: ldur            x1, [x0, #0x1f]
    // 0xb55444: ldur            x0, [fp, #-0x18]
    // 0xb55448: cmp             x0, x1
    // 0xb5544c: b.ge            #0xb556c4
    // 0xb55450: r16 = <Wirid>
    //     0xb55450: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb55454: ldr             x16, [x16, #0x1c8]
    // 0xb55458: ldur            lr, [fp, #-0x20]
    // 0xb5545c: stp             lr, x16, [SP, #8]
    // 0xb55460: r16 = "v2_wirid"
    //     0xb55460: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb55464: ldr             x16, [x16, #0x1d8]
    // 0xb55468: str             x16, [SP]
    // 0xb5546c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5546c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb55470: r0 = box()
    //     0xb55470: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb55474: mov             x1, x0
    // 0xb55478: stur            x0, [fp, #-0x28]
    // 0xb5547c: r0 = checkOpen()
    //     0xb5547c: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb55480: ldur            x3, [fp, #-0x28]
    // 0xb55484: LoadField: r0 = r3->field_1b
    //     0xb55484: ldur            w0, [x3, #0x1b]
    // 0xb55488: DecompressPointer r0
    //     0xb55488: add             x0, x0, HEAP, lsl #32
    // 0xb5548c: r16 = Sentinel
    //     0xb5548c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb55490: cmp             w0, w16
    // 0xb55494: b.eq            #0xb557d0
    // 0xb55498: LoadField: r2 = r0->field_13
    //     0xb55498: ldur            w2, [x0, #0x13]
    // 0xb5549c: DecompressPointer r2
    //     0xb5549c: add             x2, x2, HEAP, lsl #32
    // 0xb554a0: ldur            x4, [fp, #-0x18]
    // 0xb554a4: r0 = BoxInt64Instr(r4)
    //     0xb554a4: sbfiz           x0, x4, #1, #0x1f
    //     0xb554a8: cmp             x4, x0, asr #1
    //     0xb554ac: b.eq            #0xb554b8
    //     0xb554b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb554b4: stur            x4, [x0, #7]
    // 0xb554b8: mov             x1, x2
    // 0xb554bc: mov             x2, x0
    // 0xb554c0: r0 = _getNode()
    //     0xb554c0: bl              #0x68f4f8  ; [package:hive/src/util/indexable_skip_list.dart] IndexableSkipList::_getNode
    // 0xb554c4: cmp             w0, NULL
    // 0xb554c8: b.ne            #0xb554d4
    // 0xb554cc: r0 = Null
    //     0xb554cc: mov             x0, NULL
    // 0xb554d0: b               #0xb554e0
    // 0xb554d4: LoadField: r1 = r0->field_f
    //     0xb554d4: ldur            w1, [x0, #0xf]
    // 0xb554d8: DecompressPointer r1
    //     0xb554d8: add             x1, x1, HEAP, lsl #32
    // 0xb554dc: mov             x0, x1
    // 0xb554e0: cmp             w0, NULL
    // 0xb554e4: b.eq            #0xb5553c
    // 0xb554e8: ldur            x1, [fp, #-0x28]
    // 0xb554ec: LoadField: r3 = r0->field_b
    //     0xb554ec: ldur            w3, [x0, #0xb]
    // 0xb554f0: DecompressPointer r3
    //     0xb554f0: add             x3, x3, HEAP, lsl #32
    // 0xb554f4: stur            x3, [fp, #-0x30]
    // 0xb554f8: LoadField: r2 = r1->field_7
    //     0xb554f8: ldur            w2, [x1, #7]
    // 0xb554fc: DecompressPointer r2
    //     0xb554fc: add             x2, x2, HEAP, lsl #32
    // 0xb55500: mov             x0, x3
    // 0xb55504: r1 = Null
    //     0xb55504: mov             x1, NULL
    // 0xb55508: cmp             w0, NULL
    // 0xb5550c: b.eq            #0xb55534
    // 0xb55510: cmp             w2, NULL
    // 0xb55514: b.eq            #0xb55534
    // 0xb55518: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb55518: ldur            w4, [x2, #0x17]
    // 0xb5551c: DecompressPointer r4
    //     0xb5551c: add             x4, x4, HEAP, lsl #32
    // 0xb55520: r8 = X0?
    //     0xb55520: ldr             x8, [PP, #0x11e0]  ; [pp+0x11e0] TypeParameter: X0?
    // 0xb55524: LoadField: r9 = r4->field_7
    //     0xb55524: ldur            x9, [x4, #7]
    // 0xb55528: r3 = Null
    //     0xb55528: add             x3, PP, #0x29, lsl #12  ; [pp+0x29ec0] Null
    //     0xb5552c: ldr             x3, [x3, #0xec0]
    // 0xb55530: blr             x9
    // 0xb55534: ldur            x0, [fp, #-0x30]
    // 0xb55538: b               #0xb55540
    // 0xb5553c: r0 = Null
    //     0xb5553c: mov             x0, NULL
    // 0xb55540: stur            x0, [fp, #-0x28]
    // 0xb55544: cmp             w0, NULL
    // 0xb55548: b.eq            #0xb556b4
    // 0xb5554c: ldur            x1, [fp, #-0x18]
    // 0xb55550: r16 = <Wirid>
    //     0xb55550: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb55554: ldr             x16, [x16, #0x1c8]
    // 0xb55558: ldur            lr, [fp, #-0x20]
    // 0xb5555c: stp             lr, x16, [SP, #8]
    // 0xb55560: r16 = "v2_wirid"
    //     0xb55560: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb55564: ldr             x16, [x16, #0x1d8]
    // 0xb55568: str             x16, [SP]
    // 0xb5556c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5556c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb55570: r0 = box()
    //     0xb55570: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb55574: mov             x3, x0
    // 0xb55578: ldur            x2, [fp, #-0x18]
    // 0xb5557c: stur            x3, [fp, #-0x60]
    // 0xb55580: sub             x4, x2, #1
    // 0xb55584: stur            x4, [fp, #-0x58]
    // 0xb55588: r0 = BoxInt64Instr(r4)
    //     0xb55588: sbfiz           x0, x4, #1, #0x1f
    //     0xb5558c: cmp             x4, x0, asr #1
    //     0xb55590: b.eq            #0xb5559c
    //     0xb55594: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb55598: stur            x4, [x0, #7]
    // 0xb5559c: mov             x1, x0
    // 0xb555a0: ldur            x0, [fp, #-0x28]
    // 0xb555a4: stur            x1, [fp, #-0x50]
    // 0xb555a8: LoadField: r5 = r0->field_f
    //     0xb555a8: ldur            w5, [x0, #0xf]
    // 0xb555ac: DecompressPointer r5
    //     0xb555ac: add             x5, x5, HEAP, lsl #32
    // 0xb555b0: stur            x5, [fp, #-0x48]
    // 0xb555b4: LoadField: r6 = r0->field_13
    //     0xb555b4: ldur            w6, [x0, #0x13]
    // 0xb555b8: DecompressPointer r6
    //     0xb555b8: add             x6, x6, HEAP, lsl #32
    // 0xb555bc: stur            x6, [fp, #-0x30]
    // 0xb555c0: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xb555c0: ldur            x7, [x0, #0x17]
    // 0xb555c4: stur            x7, [fp, #-0x40]
    // 0xb555c8: LoadField: r8 = r0->field_1f
    //     0xb555c8: ldur            x8, [x0, #0x1f]
    // 0xb555cc: stur            x8, [fp, #-0x38]
    // 0xb555d0: r0 = Wirid()
    //     0xb555d0: bl              #0x8f1ed8  ; AllocateWiridStub -> Wirid (size=0x28)
    // 0xb555d4: mov             x3, x0
    // 0xb555d8: ldur            x0, [fp, #-0x58]
    // 0xb555dc: stur            x3, [fp, #-0x68]
    // 0xb555e0: StoreField: r3->field_7 = r0
    //     0xb555e0: stur            x0, [x3, #7]
    // 0xb555e4: ldur            x0, [fp, #-0x48]
    // 0xb555e8: StoreField: r3->field_f = r0
    //     0xb555e8: stur            w0, [x3, #0xf]
    // 0xb555ec: ldur            x0, [fp, #-0x30]
    // 0xb555f0: StoreField: r3->field_13 = r0
    //     0xb555f0: stur            w0, [x3, #0x13]
    // 0xb555f4: ldur            x0, [fp, #-0x40]
    // 0xb555f8: ArrayStore: r3[0] = r0  ; List_8
    //     0xb555f8: stur            x0, [x3, #0x17]
    // 0xb555fc: ldur            x0, [fp, #-0x38]
    // 0xb55600: StoreField: r3->field_1f = r0
    //     0xb55600: stur            x0, [x3, #0x1f]
    // 0xb55604: ldur            x4, [fp, #-0x60]
    // 0xb55608: LoadField: r5 = r4->field_7
    //     0xb55608: ldur            w5, [x4, #7]
    // 0xb5560c: DecompressPointer r5
    //     0xb5560c: add             x5, x5, HEAP, lsl #32
    // 0xb55610: mov             x0, x3
    // 0xb55614: mov             x2, x5
    // 0xb55618: stur            x5, [fp, #-0x28]
    // 0xb5561c: r1 = Null
    //     0xb5561c: mov             x1, NULL
    // 0xb55620: cmp             w2, NULL
    // 0xb55624: b.eq            #0xb55644
    // 0xb55628: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb55628: ldur            w4, [x2, #0x17]
    // 0xb5562c: DecompressPointer r4
    //     0xb5562c: add             x4, x4, HEAP, lsl #32
    // 0xb55630: r8 = X0
    //     0xb55630: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb55634: LoadField: r9 = r4->field_7
    //     0xb55634: ldur            x9, [x4, #7]
    // 0xb55638: r3 = Null
    //     0xb55638: add             x3, PP, #0x29, lsl #12  ; [pp+0x29ed0] Null
    //     0xb5563c: ldr             x3, [x3, #0xed0]
    // 0xb55640: blr             x9
    // 0xb55644: ldur            x2, [fp, #-0x28]
    // 0xb55648: r1 = Null
    //     0xb55648: mov             x1, NULL
    // 0xb5564c: r3 = <dynamic, X0>
    //     0xb5564c: add             x3, PP, #8, lsl #12  ; [pp+0x82d0] TypeArguments: <dynamic, X0>
    //     0xb55650: ldr             x3, [x3, #0x2d0]
    // 0xb55654: r0 = Null
    //     0xb55654: mov             x0, NULL
    // 0xb55658: cmp             x2, x0
    // 0xb5565c: b.eq            #0xb5566c
    // 0xb55660: r30 = InstantiateTypeArgumentsStub
    //     0xb55660: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xb55664: LoadField: r30 = r30->field_7
    //     0xb55664: ldur            lr, [lr, #7]
    // 0xb55668: blr             lr
    // 0xb5566c: r1 = Null
    //     0xb5566c: mov             x1, NULL
    // 0xb55670: r2 = 4
    //     0xb55670: movz            x2, #0x4
    // 0xb55674: stur            x0, [fp, #-0x28]
    // 0xb55678: r0 = AllocateArray()
    //     0xb55678: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb5567c: mov             x1, x0
    // 0xb55680: ldur            x0, [fp, #-0x50]
    // 0xb55684: StoreField: r1->field_f = r0
    //     0xb55684: stur            w0, [x1, #0xf]
    // 0xb55688: ldur            x0, [fp, #-0x68]
    // 0xb5568c: StoreField: r1->field_13 = r0
    //     0xb5568c: stur            w0, [x1, #0x13]
    // 0xb55690: ldur            x16, [fp, #-0x28]
    // 0xb55694: stp             x1, x16, [SP]
    // 0xb55698: r0 = Map._fromLiteral()
    //     0xb55698: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb5569c: ldur            x1, [fp, #-0x60]
    // 0xb556a0: mov             x2, x0
    // 0xb556a4: r0 = putAll()
    //     0xb556a4: bl              #0x7bd5dc  ; [package:hive/src/box/box_impl.dart] BoxImpl::putAll
    // 0xb556a8: mov             x1, x0
    // 0xb556ac: stur            x1, [fp, #-0x28]
    // 0xb556b0: r0 = Await()
    //     0xb556b0: bl              #0x661044  ; AwaitStub
    // 0xb556b4: ldur            x0, [fp, #-0x18]
    // 0xb556b8: add             x1, x0, #1
    // 0xb556bc: mov             x0, x1
    // 0xb556c0: b               #0xb553c4
    // 0xb556c4: r16 = <Wirid>
    //     0xb556c4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb556c8: ldr             x16, [x16, #0x1c8]
    // 0xb556cc: ldur            lr, [fp, #-0x20]
    // 0xb556d0: stp             lr, x16, [SP, #8]
    // 0xb556d4: r16 = "v2_wirid"
    //     0xb556d4: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb556d8: ldr             x16, [x16, #0x1d8]
    // 0xb556dc: str             x16, [SP]
    // 0xb556e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb556e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb556e4: r0 = box()
    //     0xb556e4: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb556e8: stur            x0, [fp, #-0x28]
    // 0xb556ec: r16 = <Wirid>
    //     0xb556ec: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb556f0: ldr             x16, [x16, #0x1c8]
    // 0xb556f4: ldur            lr, [fp, #-0x20]
    // 0xb556f8: stp             lr, x16, [SP, #8]
    // 0xb556fc: r16 = "v2_wirid"
    //     0xb556fc: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb55700: ldr             x16, [x16, #0x1d8]
    // 0xb55704: str             x16, [SP]
    // 0xb55708: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb55708: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb5570c: r0 = box()
    //     0xb5570c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb55710: mov             x1, x0
    // 0xb55714: stur            x0, [fp, #-0x30]
    // 0xb55718: r0 = checkOpen()
    //     0xb55718: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb5571c: ldur            x0, [fp, #-0x30]
    // 0xb55720: LoadField: r1 = r0->field_1b
    //     0xb55720: ldur            w1, [x0, #0x1b]
    // 0xb55724: DecompressPointer r1
    //     0xb55724: add             x1, x1, HEAP, lsl #32
    // 0xb55728: r16 = Sentinel
    //     0xb55728: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb5572c: cmp             w1, w16
    // 0xb55730: b.eq            #0xb557d8
    // 0xb55734: LoadField: r0 = r1->field_13
    //     0xb55734: ldur            w0, [x1, #0x13]
    // 0xb55738: DecompressPointer r0
    //     0xb55738: add             x0, x0, HEAP, lsl #32
    // 0xb5573c: LoadField: r1 = r0->field_1f
    //     0xb5573c: ldur            x1, [x0, #0x1f]
    // 0xb55740: sub             x2, x1, #1
    // 0xb55744: ldur            x1, [fp, #-0x28]
    // 0xb55748: r0 = delete()
    //     0xb55748: bl              #0xa424f8  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::delete
    // 0xb5574c: mov             x1, x0
    // 0xb55750: stur            x1, [fp, #-0x28]
    // 0xb55754: r0 = Await()
    //     0xb55754: bl              #0x661044  ; AwaitStub
    // 0xb55758: r16 = <Wirid>
    //     0xb55758: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb5575c: ldr             x16, [x16, #0x1c8]
    // 0xb55760: ldur            lr, [fp, #-0x20]
    // 0xb55764: stp             lr, x16, [SP, #8]
    // 0xb55768: r16 = "v2_wirid"
    //     0xb55768: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb5576c: ldr             x16, [x16, #0x1d8]
    // 0xb55770: str             x16, [SP]
    // 0xb55774: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb55774: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb55778: r0 = box()
    //     0xb55778: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb5577c: mov             x1, x0
    // 0xb55780: stur            x0, [fp, #-0x10]
    // 0xb55784: r0 = checkOpen()
    //     0xb55784: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb55788: ldur            x0, [fp, #-0x10]
    // 0xb5578c: LoadField: r1 = r0->field_1b
    //     0xb5578c: ldur            w1, [x0, #0x1b]
    // 0xb55790: DecompressPointer r1
    //     0xb55790: add             x1, x1, HEAP, lsl #32
    // 0xb55794: r16 = Sentinel
    //     0xb55794: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb55798: cmp             w1, w16
    // 0xb5579c: b.eq            #0xb557e0
    // 0xb557a0: r0 = getValues()
    //     0xb557a0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb557a4: LoadField: r1 = r0->field_7
    //     0xb557a4: ldur            w1, [x0, #7]
    // 0xb557a8: DecompressPointer r1
    //     0xb557a8: add             x1, x1, HEAP, lsl #32
    // 0xb557ac: mov             x2, x0
    // 0xb557b0: r0 = _GrowableList.of()
    //     0xb557b0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb557b4: r0 = ReturnAsyncNotFuture()
    //     0xb557b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb557b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb557b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb557bc: b               #0xb553ac
    // 0xb557c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb557c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb557c4: b               #0xb553d4
    // 0xb557c8: r9 = keystore
    //     0xb557c8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb557cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb557cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb557d0: r9 = keystore
    //     0xb557d0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb557d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb557d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb557d8: r9 = keystore
    //     0xb557d8: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb557dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb557dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb557e0: r9 = keystore
    //     0xb557e0: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb557e4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb557e4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ updateWirid(/* No info */) async {
    // ** addr: 0xb55a14, size: 0x164
    // 0xb55a14: EnterFrame
    //     0xb55a14: stp             fp, lr, [SP, #-0x10]!
    //     0xb55a18: mov             fp, SP
    // 0xb55a1c: AllocStack(0x48)
    //     0xb55a1c: sub             SP, SP, #0x48
    // 0xb55a20: SetupParameters(WiridRepository this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xb55a20: stur            NULL, [fp, #-8]
    //     0xb55a24: stur            x1, [fp, #-0x10]
    //     0xb55a28: mov             x16, x2
    //     0xb55a2c: mov             x2, x1
    //     0xb55a30: mov             x1, x16
    //     0xb55a34: stur            x1, [fp, #-0x18]
    // 0xb55a38: CheckStackOverflow
    //     0xb55a38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb55a3c: cmp             SP, x16
    //     0xb55a40: b.ls            #0xb55b68
    // 0xb55a44: InitAsync() -> Future<List<Wirid>>
    //     0xb55a44: add             x0, PP, #0x29, lsl #12  ; [pp+0x29bc0] TypeArguments: <List<Wirid>>
    //     0xb55a48: ldr             x0, [x0, #0xbc0]
    //     0xb55a4c: bl              #0x661298  ; InitAsyncStub
    // 0xb55a50: ldur            x0, [fp, #-0x10]
    // 0xb55a54: LoadField: r1 = r0->field_b
    //     0xb55a54: ldur            w1, [x0, #0xb]
    // 0xb55a58: DecompressPointer r1
    //     0xb55a58: add             x1, x1, HEAP, lsl #32
    // 0xb55a5c: stur            x1, [fp, #-0x20]
    // 0xb55a60: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xb55a60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb55a64: ldr             x0, [x0, #0x2728]
    //     0xb55a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb55a6c: cmp             w0, w16
    //     0xb55a70: b.ne            #0xb55a7c
    //     0xb55a74: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xb55a78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb55a7c: r16 = <Wirid>
    //     0xb55a7c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1c8] TypeArguments: <Wirid>
    //     0xb55a80: ldr             x16, [x16, #0x1c8]
    // 0xb55a84: stp             x0, x16, [SP, #8]
    // 0xb55a88: r16 = "v2_wirid"
    //     0xb55a88: add             x16, PP, #0xf, lsl #12  ; [pp+0xf1d8] "v2_wirid"
    //     0xb55a8c: ldr             x16, [x16, #0x1d8]
    // 0xb55a90: str             x16, [SP]
    // 0xb55a94: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb55a94: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb55a98: r0 = box()
    //     0xb55a98: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xb55a9c: mov             x3, x0
    // 0xb55aa0: ldur            x2, [fp, #-0x18]
    // 0xb55aa4: stur            x3, [fp, #-0x30]
    // 0xb55aa8: LoadField: r4 = r2->field_7
    //     0xb55aa8: ldur            x4, [x2, #7]
    // 0xb55aac: stur            x4, [fp, #-0x28]
    // 0xb55ab0: LoadField: r0 = r2->field_1f
    //     0xb55ab0: ldur            x0, [x2, #0x1f]
    // 0xb55ab4: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xb55ab4: ldur            x5, [x2, #0x17]
    // 0xb55ab8: cmp             x0, x5
    // 0xb55abc: b.le            #0xb55ad8
    // 0xb55ac0: r0 = BoxInt64Instr(r5)
    //     0xb55ac0: sbfiz           x0, x5, #1, #0x1f
    //     0xb55ac4: cmp             x5, x0, asr #1
    //     0xb55ac8: b.eq            #0xb55ad4
    //     0xb55acc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb55ad0: stur            x5, [x0, #7]
    // 0xb55ad4: b               #0xb55adc
    // 0xb55ad8: r0 = Null
    //     0xb55ad8: mov             x0, NULL
    // 0xb55adc: str             x0, [SP]
    // 0xb55ae0: mov             x1, x2
    // 0xb55ae4: r4 = const [0, 0x2, 0x1, 0x1, currentCount, 0x1, null]
    //     0xb55ae4: add             x4, PP, #0x29, lsl #12  ; [pp+0x29bb8] List(7) [0, 0x2, 0x1, 0x1, "currentCount", 0x1, Null]
    //     0xb55ae8: ldr             x4, [x4, #0xbb8]
    // 0xb55aec: r0 = copyWith()
    //     0xb55aec: bl              #0x8f1d24  ; [package:nuonline/app/data/models/wirid.dart] Wirid::copyWith
    // 0xb55af0: mov             x3, x0
    // 0xb55af4: ldur            x2, [fp, #-0x28]
    // 0xb55af8: r0 = BoxInt64Instr(r2)
    //     0xb55af8: sbfiz           x0, x2, #1, #0x1f
    //     0xb55afc: cmp             x2, x0, asr #1
    //     0xb55b00: b.eq            #0xb55b0c
    //     0xb55b04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb55b08: stur            x2, [x0, #7]
    // 0xb55b0c: ldur            x1, [fp, #-0x30]
    // 0xb55b10: mov             x2, x0
    // 0xb55b14: r0 = put()
    //     0xb55b14: bl              #0x819b0c  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::put
    // 0xb55b18: mov             x1, x0
    // 0xb55b1c: stur            x1, [fp, #-0x18]
    // 0xb55b20: r0 = Await()
    //     0xb55b20: bl              #0x661044  ; AwaitStub
    // 0xb55b24: ldur            x1, [fp, #-0x20]
    // 0xb55b28: r0 = box()
    //     0xb55b28: bl              #0x8f1b4c  ; [package:nuonline/services/storage_service/wirid_storage.dart] WiridStorage::box
    // 0xb55b2c: mov             x1, x0
    // 0xb55b30: stur            x0, [fp, #-0x10]
    // 0xb55b34: r0 = checkOpen()
    //     0xb55b34: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xb55b38: ldur            x0, [fp, #-0x10]
    // 0xb55b3c: LoadField: r1 = r0->field_1b
    //     0xb55b3c: ldur            w1, [x0, #0x1b]
    // 0xb55b40: DecompressPointer r1
    //     0xb55b40: add             x1, x1, HEAP, lsl #32
    // 0xb55b44: r16 = Sentinel
    //     0xb55b44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb55b48: cmp             w1, w16
    // 0xb55b4c: b.eq            #0xb55b70
    // 0xb55b50: r0 = getValues()
    //     0xb55b50: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xb55b54: LoadField: r1 = r0->field_7
    //     0xb55b54: ldur            w1, [x0, #7]
    // 0xb55b58: DecompressPointer r1
    //     0xb55b58: add             x1, x1, HEAP, lsl #32
    // 0xb55b5c: mov             x2, x0
    // 0xb55b60: r0 = _GrowableList.of()
    //     0xb55b60: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb55b64: r0 = ReturnAsyncNotFuture()
    //     0xb55b64: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xb55b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb55b68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb55b6c: b               #0xb55a44
    // 0xb55b70: r9 = keystore
    //     0xb55b70: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xb55b74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb55b74: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
