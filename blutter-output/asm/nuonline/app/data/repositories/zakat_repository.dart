// lib: , url: package:nuonline/app/data/repositories/zakat_repository.dart

// class id: 1050106, size: 0x8
class :: {
}

// class id: 1070, size: 0xc, field offset: 0x8
class ZakatRepository extends Object {

  _ findSetting(/* No info */) async {
    // ** addr: 0x7e9244, size: 0x104
    // 0x7e9244: EnterFrame
    //     0x7e9244: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9248: mov             fp, SP
    // 0x7e924c: AllocStack(0x88)
    //     0x7e924c: sub             SP, SP, #0x88
    // 0x7e9250: SetupParameters(ZakatRepository this /* r1 => r1, fp-0x50 */)
    //     0x7e9250: stur            NULL, [fp, #-8]
    //     0x7e9254: stur            x1, [fp, #-0x50]
    // 0x7e9258: CheckStackOverflow
    //     0x7e9258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e925c: cmp             SP, x16
    //     0x7e9260: b.ls            #0x7e9340
    // 0x7e9264: InitAsync() -> Future<ApiResult<ZakatSetting>>
    //     0x7e9264: add             x0, PP, #0x35, lsl #12  ; [pp+0x35640] TypeArguments: <ApiResult<ZakatSetting>>
    //     0x7e9268: ldr             x0, [x0, #0x640]
    //     0x7e926c: bl              #0x661298  ; InitAsyncStub
    // 0x7e9270: ldur            x0, [fp, #-0x50]
    // 0x7e9274: LoadField: r1 = r0->field_7
    //     0x7e9274: ldur            w1, [x0, #7]
    // 0x7e9278: DecompressPointer r1
    //     0x7e9278: add             x1, x1, HEAP, lsl #32
    // 0x7e927c: stp             x1, NULL, [SP, #0x28]
    // 0x7e9280: r16 = "/zakat"
    //     0x7e9280: add             x16, PP, #0xf, lsl #12  ; [pp+0xfcf8] "/zakat"
    //     0x7e9284: ldr             x16, [x16, #0xcf8]
    // 0x7e9288: r30 = false
    //     0x7e9288: add             lr, NULL, #0x30  ; false
    // 0x7e928c: stp             lr, x16, [SP, #0x18]
    // 0x7e9290: r16 = "GET"
    //     0x7e9290: add             x16, PP, #0xf, lsl #12  ; [pp+0xf6a0] "GET"
    //     0x7e9294: ldr             x16, [x16, #0x6a0]
    // 0x7e9298: r30 = Instance_Duration
    //     0x7e9298: ldr             lr, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0x7e929c: stp             lr, x16, [SP, #8]
    // 0x7e92a0: r16 = Instance_Duration
    //     0x7e92a0: ldr             x16, [PP, #0x648]  ; [pp+0x648] Obj!Duration@e3a071
    // 0x7e92a4: str             x16, [SP]
    // 0x7e92a8: r4 = const [0x1, 0x6, 0x6, 0x3, method, 0x3, receiveTimeout, 0x5, sendTimeout, 0x4, null]
    //     0x7e92a8: add             x4, PP, #0x35, lsl #12  ; [pp+0x35648] List(11) [0x1, 0x6, 0x6, 0x3, "method", 0x3, "receiveTimeout", 0x5, "sendTimeout", 0x4, Null]
    //     0x7e92ac: ldr             x4, [x4, #0x648]
    // 0x7e92b0: r0 = withRetry()
    //     0x7e92b0: bl              #0x6ff8d4  ; [package:nuonline/services/api_service/api_service.dart] ApiService::withRetry
    // 0x7e92b4: mov             x1, x0
    // 0x7e92b8: stur            x1, [fp, #-0x50]
    // 0x7e92bc: r0 = Await()
    //     0x7e92bc: bl              #0x661044  ; AwaitStub
    // 0x7e92c0: LoadField: r3 = r0->field_b
    //     0x7e92c0: ldur            w3, [x0, #0xb]
    // 0x7e92c4: DecompressPointer r3
    //     0x7e92c4: add             x3, x3, HEAP, lsl #32
    // 0x7e92c8: mov             x0, x3
    // 0x7e92cc: stur            x3, [fp, #-0x50]
    // 0x7e92d0: r2 = Null
    //     0x7e92d0: mov             x2, NULL
    // 0x7e92d4: r1 = Null
    //     0x7e92d4: mov             x1, NULL
    // 0x7e92d8: r8 = Map<String, dynamic>
    //     0x7e92d8: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x7e92dc: r3 = Null
    //     0x7e92dc: add             x3, PP, #0x35, lsl #12  ; [pp+0x35650] Null
    //     0x7e92e0: ldr             x3, [x3, #0x650]
    // 0x7e92e4: r0 = Map<String, dynamic>()
    //     0x7e92e4: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x7e92e8: ldur            x2, [fp, #-0x50]
    // 0x7e92ec: r1 = Null
    //     0x7e92ec: mov             x1, NULL
    // 0x7e92f0: r0 = ZakatSetting.fromMap()
    //     0x7e92f0: bl              #0x7e9348  ; [package:nuonline/app/data/models/zakat_setting.dart] ZakatSetting::ZakatSetting.fromMap
    // 0x7e92f4: r1 = <ZakatSetting>
    //     0x7e92f4: add             x1, PP, #0x34, lsl #12  ; [pp+0x347a0] TypeArguments: <ZakatSetting>
    //     0x7e92f8: ldr             x1, [x1, #0x7a0]
    // 0x7e92fc: stur            x0, [fp, #-0x50]
    // 0x7e9300: r0 = _$SuccessImpl()
    //     0x7e9300: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0x7e9304: mov             x1, x0
    // 0x7e9308: ldur            x0, [fp, #-0x50]
    // 0x7e930c: StoreField: r1->field_b = r0
    //     0x7e930c: stur            w0, [x1, #0xb]
    // 0x7e9310: mov             x0, x1
    // 0x7e9314: r0 = ReturnAsyncNotFuture()
    //     0x7e9314: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e9318: sub             SP, fp, #0x88
    // 0x7e931c: mov             x1, x0
    // 0x7e9320: r0 = getDioException()
    //     0x7e9320: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0x7e9324: r1 = <ZakatSetting>
    //     0x7e9324: add             x1, PP, #0x34, lsl #12  ; [pp+0x347a0] TypeArguments: <ZakatSetting>
    //     0x7e9328: ldr             x1, [x1, #0x7a0]
    // 0x7e932c: stur            x0, [fp, #-0x50]
    // 0x7e9330: r0 = _$FailureImpl()
    //     0x7e9330: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0x7e9334: ldur            x1, [fp, #-0x50]
    // 0x7e9338: StoreField: r0->field_b = r1
    //     0x7e9338: stur            w1, [x0, #0xb]
    // 0x7e933c: r0 = ReturnAsyncNotFuture()
    //     0x7e933c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x7e9340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9340: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9344: b               #0x7e9264
  }
  static _ find(/* No info */) {
    // ** addr: 0x813400, size: 0x64
    // 0x813400: EnterFrame
    //     0x813400: stp             fp, lr, [SP, #-0x10]!
    //     0x813404: mov             fp, SP
    // 0x813408: AllocStack(0x10)
    //     0x813408: sub             SP, SP, #0x10
    // 0x81340c: CheckStackOverflow
    //     0x81340c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x813410: cmp             SP, x16
    //     0x813414: b.ls            #0x81345c
    // 0x813418: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x813418: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x81341c: ldr             x0, [x0, #0x2670]
    //     0x813420: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x813424: cmp             w0, w16
    //     0x813428: b.ne            #0x813434
    //     0x81342c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x813430: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x813434: r16 = <ZakatRepository>
    //     0x813434: add             x16, PP, #0x10, lsl #12  ; [pp+0x10200] TypeArguments: <ZakatRepository>
    //     0x813438: ldr             x16, [x16, #0x200]
    // 0x81343c: r30 = "zakat_repo"
    //     0x81343c: add             lr, PP, #0x10, lsl #12  ; [pp+0x10208] "zakat_repo"
    //     0x813440: ldr             lr, [lr, #0x208]
    // 0x813444: stp             lr, x16, [SP]
    // 0x813448: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x813448: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x81344c: r0 = Inst.find()
    //     0x81344c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x813450: LeaveFrame
    //     0x813450: mov             SP, fp
    //     0x813454: ldp             fp, lr, [SP], #0x10
    // 0x813458: ret
    //     0x813458: ret             
    // 0x81345c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81345c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x813460: b               #0x813418
  }
}
