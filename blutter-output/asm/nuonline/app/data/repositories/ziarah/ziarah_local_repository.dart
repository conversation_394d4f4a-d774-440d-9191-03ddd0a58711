// lib: , url: package:nuonline/app/data/repositories/ziarah/ziarah_local_repository.dart

// class id: 1050107, size: 0x8
class :: {
}

// class id: 1069, size: 0x10, field offset: 0x8
class ZiarahLocalRepository extends Object
    implements ZiarahRepository {

  static _ find(/* No info */) {
    // ** addr: 0x851c24, size: 0x64
    // 0x851c24: EnterFrame
    //     0x851c24: stp             fp, lr, [SP, #-0x10]!
    //     0x851c28: mov             fp, SP
    // 0x851c2c: AllocStack(0x10)
    //     0x851c2c: sub             SP, SP, #0x10
    // 0x851c30: CheckStackOverflow
    //     0x851c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851c34: cmp             SP, x16
    //     0x851c38: b.ls            #0x851c80
    // 0x851c3c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x851c3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x851c40: ldr             x0, [x0, #0x2670]
    //     0x851c44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x851c48: cmp             w0, w16
    //     0x851c4c: b.ne            #0x851c58
    //     0x851c50: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x851c54: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x851c58: r16 = <ZiarahRepository>
    //     0x851c58: add             x16, PP, #0x10, lsl #12  ; [pp+0x101c0] TypeArguments: <ZiarahRepository>
    //     0x851c5c: ldr             x16, [x16, #0x1c0]
    // 0x851c60: r30 = "ziarah_local_repo"
    //     0x851c60: add             lr, PP, #0x10, lsl #12  ; [pp+0x101c8] "ziarah_local_repo"
    //     0x851c64: ldr             lr, [lr, #0x1c8]
    // 0x851c68: stp             lr, x16, [SP]
    // 0x851c6c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x851c6c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x851c70: r0 = Inst.find()
    //     0x851c70: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x851c74: LeaveFrame
    //     0x851c74: mov             SP, fp
    //     0x851c78: ldp             fp, lr, [SP], #0x10
    // 0x851c7c: ret
    //     0x851c7c: ret             
    // 0x851c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851c84: b               #0x851c3c
  }
  _ findAllHistory(/* No info */) async {
    // ** addr: 0xe7b15c, size: 0xac
    // 0xe7b15c: EnterFrame
    //     0xe7b15c: stp             fp, lr, [SP, #-0x10]!
    //     0xe7b160: mov             fp, SP
    // 0xe7b164: AllocStack(0x28)
    //     0xe7b164: sub             SP, SP, #0x28
    // 0xe7b168: SetupParameters(ZiarahLocalRepository this /* r1 => r1, fp-0x10 */)
    //     0xe7b168: stur            NULL, [fp, #-8]
    //     0xe7b16c: stur            x1, [fp, #-0x10]
    // 0xe7b170: CheckStackOverflow
    //     0xe7b170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7b174: cmp             SP, x16
    //     0xe7b178: b.ls            #0xe7b1f8
    // 0xe7b17c: InitAsync() -> Future<List<String>>
    //     0xe7b17c: add             x0, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0xe7b180: ldr             x0, [x0, #0xb40]
    //     0xe7b184: bl              #0x661298  ; InitAsyncStub
    // 0xe7b188: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7b188: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7b18c: ldr             x0, [x0, #0x2728]
    //     0xe7b190: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7b194: cmp             w0, w16
    //     0xe7b198: b.ne            #0xe7b1a4
    //     0xe7b19c: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7b1a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7b1a4: r16 = <String>
    //     0xe7b1a4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe7b1a8: stp             x0, x16, [SP, #8]
    // 0xe7b1ac: r16 = "v2_ziarah_search_history"
    //     0xe7b1ac: ldr             x16, [PP, #0x7c60]  ; [pp+0x7c60] "v2_ziarah_search_history"
    // 0xe7b1b0: str             x16, [SP]
    // 0xe7b1b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7b1b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7b1b8: r0 = box()
    //     0xe7b1b8: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7b1bc: mov             x1, x0
    // 0xe7b1c0: stur            x0, [fp, #-0x10]
    // 0xe7b1c4: r0 = checkOpen()
    //     0xe7b1c4: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0xe7b1c8: ldur            x0, [fp, #-0x10]
    // 0xe7b1cc: LoadField: r1 = r0->field_1b
    //     0xe7b1cc: ldur            w1, [x0, #0x1b]
    // 0xe7b1d0: DecompressPointer r1
    //     0xe7b1d0: add             x1, x1, HEAP, lsl #32
    // 0xe7b1d4: r16 = Sentinel
    //     0xe7b1d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe7b1d8: cmp             w1, w16
    // 0xe7b1dc: b.eq            #0xe7b200
    // 0xe7b1e0: r0 = getValues()
    //     0xe7b1e0: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0xe7b1e4: LoadField: r1 = r0->field_7
    //     0xe7b1e4: ldur            w1, [x0, #7]
    // 0xe7b1e8: DecompressPointer r1
    //     0xe7b1e8: add             x1, x1, HEAP, lsl #32
    // 0xe7b1ec: mov             x2, x0
    // 0xe7b1f0: r0 = _GrowableList.of()
    //     0xe7b1f0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xe7b1f4: r0 = ReturnAsyncNotFuture()
    //     0xe7b1f4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7b1f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7b1f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7b1fc: b               #0xe7b17c
    // 0xe7b200: r9 = keystore
    //     0xe7b200: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0xe7b204: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe7b204: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ updateAllHistory(/* No info */) async {
    // ** addr: 0xe7c330, size: 0xb0
    // 0xe7c330: EnterFrame
    //     0xe7c330: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c334: mov             fp, SP
    // 0xe7c338: AllocStack(0x38)
    //     0xe7c338: sub             SP, SP, #0x38
    // 0xe7c33c: SetupParameters(ZiarahLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe7c33c: stur            NULL, [fp, #-8]
    //     0xe7c340: stur            x1, [fp, #-0x10]
    //     0xe7c344: stur            x2, [fp, #-0x18]
    // 0xe7c348: CheckStackOverflow
    //     0xe7c348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c34c: cmp             SP, x16
    //     0xe7c350: b.ls            #0xe7c3d8
    // 0xe7c354: InitAsync() -> Future<void?>
    //     0xe7c354: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe7c358: bl              #0x661298  ; InitAsyncStub
    // 0xe7c35c: ldur            x0, [fp, #-0x10]
    // 0xe7c360: LoadField: r1 = r0->field_7
    //     0xe7c360: ldur            w1, [x0, #7]
    // 0xe7c364: DecompressPointer r1
    //     0xe7c364: add             x1, x1, HEAP, lsl #32
    // 0xe7c368: r0 = ziarahSearchHistory()
    //     0xe7c368: bl              #0xe7b208  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::ziarahSearchHistory
    // 0xe7c36c: mov             x1, x0
    // 0xe7c370: r0 = clear()
    //     0xe7c370: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0xe7c374: mov             x1, x0
    // 0xe7c378: stur            x1, [fp, #-0x20]
    // 0xe7c37c: r0 = Await()
    //     0xe7c37c: bl              #0x661044  ; AwaitStub
    // 0xe7c380: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0xe7c380: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe7c384: ldr             x0, [x0, #0x2728]
    //     0xe7c388: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe7c38c: cmp             w0, w16
    //     0xe7c390: b.ne            #0xe7c39c
    //     0xe7c394: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0xe7c398: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xe7c39c: r16 = <String>
    //     0xe7c39c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xe7c3a0: stp             x0, x16, [SP, #8]
    // 0xe7c3a4: r16 = "v2_ziarah_search_history"
    //     0xe7c3a4: ldr             x16, [PP, #0x7c60]  ; [pp+0x7c60] "v2_ziarah_search_history"
    // 0xe7c3a8: str             x16, [SP]
    // 0xe7c3ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7c3ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7c3b0: r0 = box()
    //     0xe7c3b0: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0xe7c3b4: mov             x1, x0
    // 0xe7c3b8: ldur            x2, [fp, #-0x18]
    // 0xe7c3bc: r0 = addAll()
    //     0xe7c3bc: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0xe7c3c0: r16 = <Iterable<int>>
    //     0xe7c3c0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2daf8] TypeArguments: <Iterable<int>>
    //     0xe7c3c4: ldr             x16, [x16, #0xaf8]
    // 0xe7c3c8: stp             x0, x16, [SP]
    // 0xe7c3cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xe7c3cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xe7c3d0: r0 = FutureExtensions.ignore()
    //     0xe7c3d0: bl              #0x7082c8  ; [dart:async] ::FutureExtensions.ignore
    // 0xe7c3d4: r0 = ReturnAsync()
    //     0xe7c3d4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xe7c3d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c3d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c3dc: b               #0xe7c354
  }
  _ findAll(/* No info */) {
    // ** addr: 0xe7c858, size: 0x94
    // 0xe7c858: EnterFrame
    //     0xe7c858: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c85c: mov             fp, SP
    // 0xe7c860: LoadField: r0 = r4->field_1f
    //     0xe7c860: ldur            w0, [x4, #0x1f]
    // 0xe7c864: DecompressPointer r0
    //     0xe7c864: add             x0, x0, HEAP, lsl #32
    // 0xe7c868: r16 = "categoryId"
    //     0xe7c868: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c48] "categoryId"
    //     0xe7c86c: ldr             x16, [x16, #0xc48]
    // 0xe7c870: cmp             w0, w16
    // 0xe7c874: b.ne            #0xe7c880
    // 0xe7c878: r0 = 1
    //     0xe7c878: movz            x0, #0x1
    // 0xe7c87c: b               #0xe7c884
    // 0xe7c880: r0 = 0
    //     0xe7c880: movz            x0, #0
    // 0xe7c884: lsl             x1, x0, #1
    // 0xe7c888: lsl             w2, w1, #1
    // 0xe7c88c: add             w3, w2, #8
    // 0xe7c890: ArrayLoad: r2 = r4[r3]  ; Unknown_4
    //     0xe7c890: add             x16, x4, w3, sxtw #1
    //     0xe7c894: ldur            w2, [x16, #0xf]
    // 0xe7c898: DecompressPointer r2
    //     0xe7c898: add             x2, x2, HEAP, lsl #32
    // 0xe7c89c: r16 = "provinceId"
    //     0xe7c89c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe90] "provinceId"
    //     0xe7c8a0: ldr             x16, [x16, #0xe90]
    // 0xe7c8a4: cmp             w2, w16
    // 0xe7c8a8: b.ne            #0xe7c8b8
    // 0xe7c8ac: add             w0, w1, #2
    // 0xe7c8b0: r1 = LoadInt32Instr(r0)
    //     0xe7c8b0: sbfx            x1, x0, #1, #0x1f
    // 0xe7c8b4: mov             x0, x1
    // 0xe7c8b8: lsl             x1, x0, #1
    // 0xe7c8bc: lsl             w0, w1, #1
    // 0xe7c8c0: add             w1, w0, #8
    // 0xe7c8c4: ArrayLoad: r0 = r4[r1]  ; Unknown_4
    //     0xe7c8c4: add             x16, x4, w1, sxtw #1
    //     0xe7c8c8: ldur            w0, [x16, #0xf]
    // 0xe7c8cc: DecompressPointer r0
    //     0xe7c8cc: add             x0, x0, HEAP, lsl #32
    // 0xe7c8d0: r16 = "regencyId"
    //     0xe7c8d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe98] "regencyId"
    //     0xe7c8d4: ldr             x16, [x16, #0xe98]
    // 0xe7c8d8: cmp             w0, w16
    // 0xe7c8dc: b.eq            #0xe7c8e0
    // 0xe7c8e0: r0 = UnimplementedError()
    //     0xe7c8e0: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xe7c8e4: r0 = Throw()
    //     0xe7c8e4: bl              #0xec04b8  ; ThrowStub
    // 0xe7c8e8: brk             #0
  }
  _ updateCurrentLocation(/* No info */) async {
    // ** addr: 0xe7d5fc, size: 0x5c
    // 0xe7d5fc: EnterFrame
    //     0xe7d5fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d600: mov             fp, SP
    // 0xe7d604: AllocStack(0x18)
    //     0xe7d604: sub             SP, SP, #0x18
    // 0xe7d608: SetupParameters(ZiarahLocalRepository this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xe7d608: stur            NULL, [fp, #-8]
    //     0xe7d60c: stur            x1, [fp, #-0x10]
    //     0xe7d610: stur            x2, [fp, #-0x18]
    // 0xe7d614: CheckStackOverflow
    //     0xe7d614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d618: cmp             SP, x16
    //     0xe7d61c: b.ls            #0xe7d650
    // 0xe7d620: InitAsync() -> Future<void?>
    //     0xe7d620: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xe7d624: bl              #0x661298  ; InitAsyncStub
    // 0xe7d628: ldur            x0, [fp, #-0x10]
    // 0xe7d62c: LoadField: r1 = r0->field_b
    //     0xe7d62c: ldur            w1, [x0, #0xb]
    // 0xe7d630: DecompressPointer r1
    //     0xe7d630: add             x1, x1, HEAP, lsl #32
    // 0xe7d634: ldur            x2, [fp, #-0x18]
    // 0xe7d638: r0 = setZiarah()
    //     0xe7d638: bl              #0xe7d658  ; [package:nuonline/services/storage_service/location_storage.dart] LocationStorage::setZiarah
    // 0xe7d63c: mov             x1, x0
    // 0xe7d640: stur            x1, [fp, #-0x10]
    // 0xe7d644: r0 = Await()
    //     0xe7d644: bl              #0x661044  ; AwaitStub
    // 0xe7d648: r0 = Null
    //     0xe7d648: mov             x0, NULL
    // 0xe7d64c: r0 = ReturnAsyncNotFuture()
    //     0xe7d64c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7d650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d650: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d654: b               #0xe7d620
  }
  _ findCurrentLocation(/* No info */) {
    // ** addr: 0xe7d854, size: 0x38
    // 0xe7d854: EnterFrame
    //     0xe7d854: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d858: mov             fp, SP
    // 0xe7d85c: CheckStackOverflow
    //     0xe7d85c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d860: cmp             SP, x16
    //     0xe7d864: b.ls            #0xe7d884
    // 0xe7d868: LoadField: r0 = r1->field_b
    //     0xe7d868: ldur            w0, [x1, #0xb]
    // 0xe7d86c: DecompressPointer r0
    //     0xe7d86c: add             x0, x0, HEAP, lsl #32
    // 0xe7d870: mov             x1, x0
    // 0xe7d874: r0 = ziarah()
    //     0xe7d874: bl              #0xe7d88c  ; [package:nuonline/services/storage_service/location_storage.dart] LocationStorage::ziarah
    // 0xe7d878: LeaveFrame
    //     0xe7d878: mov             SP, fp
    //     0xe7d87c: ldp             fp, lr, [SP], #0x10
    // 0xe7d880: ret
    //     0xe7d880: ret             
    // 0xe7d884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d888: b               #0xe7d868
  }
}
