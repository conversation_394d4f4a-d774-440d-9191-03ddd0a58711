// lib: , url: package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart

// class id: 1050108, size: 0x8
class :: {
}

// class id: 1067, size: 0x10, field offset: 0x8
class ZiarahRemoteRepository extends Object
    implements ZiarahRepository {

  static _ find(/* No info */) {
    // ** addr: 0x851efc, size: 0x64
    // 0x851efc: EnterFrame
    //     0x851efc: stp             fp, lr, [SP, #-0x10]!
    //     0x851f00: mov             fp, SP
    // 0x851f04: AllocStack(0x10)
    //     0x851f04: sub             SP, SP, #0x10
    // 0x851f08: CheckStackOverflow
    //     0x851f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851f0c: cmp             SP, x16
    //     0x851f10: b.ls            #0x851f58
    // 0x851f14: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x851f14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x851f18: ldr             x0, [x0, #0x2670]
    //     0x851f1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x851f20: cmp             w0, w16
    //     0x851f24: b.ne            #0x851f30
    //     0x851f28: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x851f2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x851f30: r16 = <ZiarahRepository>
    //     0x851f30: add             x16, PP, #0x10, lsl #12  ; [pp+0x101c0] TypeArguments: <ZiarahRepository>
    //     0x851f34: ldr             x16, [x16, #0x1c0]
    // 0x851f38: r30 = "ziarah_remote_repo"
    //     0x851f38: add             lr, PP, #0x10, lsl #12  ; [pp+0x101d8] "ziarah_remote_repo"
    //     0x851f3c: ldr             lr, [lr, #0x1d8]
    // 0x851f40: stp             lr, x16, [SP]
    // 0x851f44: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x851f44: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x851f48: r0 = Inst.find()
    //     0x851f48: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x851f4c: LeaveFrame
    //     0x851f4c: mov             SP, fp
    //     0x851f50: ldp             fp, lr, [SP], #0x10
    // 0x851f54: ret
    //     0x851f54: ret             
    // 0x851f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851f58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851f5c: b               #0x851f14
  }
  _ findById(/* No info */) async {
    // ** addr: 0xe7b268, size: 0x12c
    // 0xe7b268: EnterFrame
    //     0xe7b268: stp             fp, lr, [SP, #-0x10]!
    //     0xe7b26c: mov             fp, SP
    // 0xe7b270: AllocStack(0x80)
    //     0xe7b270: sub             SP, SP, #0x80
    // 0xe7b274: SetupParameters(ZiarahRemoteRepository this /* r1 => r1, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0xe7b274: stur            NULL, [fp, #-8]
    //     0xe7b278: stur            x1, [fp, #-0x58]
    //     0xe7b27c: stur            x2, [fp, #-0x60]
    // 0xe7b280: CheckStackOverflow
    //     0xe7b280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7b284: cmp             SP, x16
    //     0xe7b288: b.ls            #0xe7b38c
    // 0xe7b28c: InitAsync() -> Future<ApiResult<ZiarahDetail>>
    //     0xe7b28c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fcf8] TypeArguments: <ApiResult<ZiarahDetail>>
    //     0xe7b290: ldr             x0, [x0, #0xcf8]
    //     0xe7b294: bl              #0x661298  ; InitAsyncStub
    // 0xe7b298: ldur            x1, [fp, #-0x58]
    // 0xe7b29c: ldur            x0, [fp, #-0x60]
    // 0xe7b2a0: LoadField: r3 = r1->field_7
    //     0xe7b2a0: ldur            w3, [x1, #7]
    // 0xe7b2a4: DecompressPointer r3
    //     0xe7b2a4: add             x3, x3, HEAP, lsl #32
    // 0xe7b2a8: stur            x3, [fp, #-0x68]
    // 0xe7b2ac: r1 = Null
    //     0xe7b2ac: mov             x1, NULL
    // 0xe7b2b0: r2 = 4
    //     0xe7b2b0: movz            x2, #0x4
    // 0xe7b2b4: r0 = AllocateArray()
    //     0xe7b2b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7b2b8: mov             x2, x0
    // 0xe7b2bc: r16 = "/ziarah/"
    //     0xe7b2bc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49f08] "/ziarah/"
    //     0xe7b2c0: ldr             x16, [x16, #0xf08]
    // 0xe7b2c4: StoreField: r2->field_f = r16
    //     0xe7b2c4: stur            w16, [x2, #0xf]
    // 0xe7b2c8: ldur            x3, [fp, #-0x60]
    // 0xe7b2cc: r0 = BoxInt64Instr(r3)
    //     0xe7b2cc: sbfiz           x0, x3, #1, #0x1f
    //     0xe7b2d0: cmp             x3, x0, asr #1
    //     0xe7b2d4: b.eq            #0xe7b2e0
    //     0xe7b2d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7b2dc: stur            x3, [x0, #7]
    // 0xe7b2e0: StoreField: r2->field_13 = r0
    //     0xe7b2e0: stur            w0, [x2, #0x13]
    // 0xe7b2e4: str             x2, [SP]
    // 0xe7b2e8: r0 = _interpolate()
    //     0xe7b2e8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7b2ec: ldur            x16, [fp, #-0x68]
    // 0xe7b2f0: stp             x16, NULL, [SP, #8]
    // 0xe7b2f4: str             x0, [SP]
    // 0xe7b2f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7b2f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7b2fc: r0 = get()
    //     0xe7b2fc: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7b300: mov             x1, x0
    // 0xe7b304: stur            x1, [fp, #-0x58]
    // 0xe7b308: r0 = Await()
    //     0xe7b308: bl              #0x661044  ; AwaitStub
    // 0xe7b30c: LoadField: r3 = r0->field_b
    //     0xe7b30c: ldur            w3, [x0, #0xb]
    // 0xe7b310: DecompressPointer r3
    //     0xe7b310: add             x3, x3, HEAP, lsl #32
    // 0xe7b314: mov             x0, x3
    // 0xe7b318: stur            x3, [fp, #-0x58]
    // 0xe7b31c: r2 = Null
    //     0xe7b31c: mov             x2, NULL
    // 0xe7b320: r1 = Null
    //     0xe7b320: mov             x1, NULL
    // 0xe7b324: r8 = Map<String, dynamic>
    //     0xe7b324: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7b328: r3 = Null
    //     0xe7b328: add             x3, PP, #0x49, lsl #12  ; [pp+0x49f10] Null
    //     0xe7b32c: ldr             x3, [x3, #0xf10]
    // 0xe7b330: r0 = Map<String, dynamic>()
    //     0xe7b330: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7b334: ldur            x2, [fp, #-0x58]
    // 0xe7b338: r1 = Null
    //     0xe7b338: mov             x1, NULL
    // 0xe7b33c: r0 = ZiarahDetail.fromMap()
    //     0xe7b33c: bl              #0xe7b394  ; [package:nuonline/app/data/models/ziarah.dart] ZiarahDetail::ZiarahDetail.fromMap
    // 0xe7b340: r1 = <ZiarahDetail>
    //     0xe7b340: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eef8] TypeArguments: <ZiarahDetail>
    //     0xe7b344: ldr             x1, [x1, #0xef8]
    // 0xe7b348: stur            x0, [fp, #-0x58]
    // 0xe7b34c: r0 = _$SuccessImpl()
    //     0xe7b34c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7b350: mov             x1, x0
    // 0xe7b354: ldur            x0, [fp, #-0x58]
    // 0xe7b358: StoreField: r1->field_b = r0
    //     0xe7b358: stur            w0, [x1, #0xb]
    // 0xe7b35c: mov             x0, x1
    // 0xe7b360: r0 = ReturnAsyncNotFuture()
    //     0xe7b360: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7b364: sub             SP, fp, #0x80
    // 0xe7b368: mov             x1, x0
    // 0xe7b36c: r0 = getDioException()
    //     0xe7b36c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7b370: r1 = <ZiarahDetail>
    //     0xe7b370: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eef8] TypeArguments: <ZiarahDetail>
    //     0xe7b374: ldr             x1, [x1, #0xef8]
    // 0xe7b378: stur            x0, [fp, #-0x58]
    // 0xe7b37c: r0 = _$FailureImpl()
    //     0xe7b37c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7b380: ldur            x1, [fp, #-0x58]
    // 0xe7b384: StoreField: r0->field_b = r1
    //     0xe7b384: stur            w1, [x0, #0xb]
    // 0xe7b388: r0 = ReturnAsyncNotFuture()
    //     0xe7b388: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7b38c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7b38c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7b390: b               #0xe7b28c
  }
  _ findAllProvince(/* No info */) async {
    // ** addr: 0xe7c3e0, size: 0x184
    // 0xe7c3e0: EnterFrame
    //     0xe7c3e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c3e4: mov             fp, SP
    // 0xe7c3e8: AllocStack(0x78)
    //     0xe7c3e8: sub             SP, SP, #0x78
    // 0xe7c3ec: SetupParameters(ZiarahRemoteRepository this /* r1 => r1, fp-0x60 */)
    //     0xe7c3ec: stur            NULL, [fp, #-8]
    //     0xe7c3f0: stur            x1, [fp, #-0x60]
    // 0xe7c3f4: CheckStackOverflow
    //     0xe7c3f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c3f8: cmp             SP, x16
    //     0xe7c3fc: b.ls            #0xe7c55c
    // 0xe7c400: InitAsync() -> Future<ApiResult<List<Area>>>
    //     0xe7c400: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c1d0] TypeArguments: <ApiResult<List<Area>>>
    //     0xe7c404: ldr             x0, [x0, #0x1d0]
    //     0xe7c408: bl              #0x661298  ; InitAsyncStub
    // 0xe7c40c: ldur            x0, [fp, #-0x60]
    // 0xe7c410: LoadField: r1 = r0->field_7
    //     0xe7c410: ldur            w1, [x0, #7]
    // 0xe7c414: DecompressPointer r1
    //     0xe7c414: add             x1, x1, HEAP, lsl #32
    // 0xe7c418: stp             x1, NULL, [SP, #8]
    // 0xe7c41c: r16 = "/ziarah/location"
    //     0xe7c41c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49ed8] "/ziarah/location"
    //     0xe7c420: ldr             x16, [x16, #0xed8]
    // 0xe7c424: str             x16, [SP]
    // 0xe7c428: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7c428: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7c42c: r0 = get()
    //     0xe7c42c: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7c430: mov             x1, x0
    // 0xe7c434: stur            x1, [fp, #-0x60]
    // 0xe7c438: r0 = Await()
    //     0xe7c438: bl              #0x661044  ; AwaitStub
    // 0xe7c43c: LoadField: r3 = r0->field_b
    //     0xe7c43c: ldur            w3, [x0, #0xb]
    // 0xe7c440: DecompressPointer r3
    //     0xe7c440: add             x3, x3, HEAP, lsl #32
    // 0xe7c444: mov             x0, x3
    // 0xe7c448: stur            x3, [fp, #-0x60]
    // 0xe7c44c: r2 = Null
    //     0xe7c44c: mov             x2, NULL
    // 0xe7c450: r1 = Null
    //     0xe7c450: mov             x1, NULL
    // 0xe7c454: r4 = 60
    //     0xe7c454: movz            x4, #0x3c
    // 0xe7c458: branchIfSmi(r0, 0xe7c464)
    //     0xe7c458: tbz             w0, #0, #0xe7c464
    // 0xe7c45c: r4 = LoadClassIdInstr(r0)
    //     0xe7c45c: ldur            x4, [x0, #-1]
    //     0xe7c460: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c464: sub             x4, x4, #0x5a
    // 0xe7c468: cmp             x4, #2
    // 0xe7c46c: b.ls            #0xe7c484
    // 0xe7c470: r8 = List?
    //     0xe7c470: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xe7c474: ldr             x8, [x8, #0x140]
    // 0xe7c478: r3 = Null
    //     0xe7c478: add             x3, PP, #0x49, lsl #12  ; [pp+0x49ee0] Null
    //     0xe7c47c: ldr             x3, [x3, #0xee0]
    // 0xe7c480: r0 = List?()
    //     0xe7c480: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xe7c484: ldur            x0, [fp, #-0x60]
    // 0xe7c488: cmp             w0, NULL
    // 0xe7c48c: b.ne            #0xe7c49c
    // 0xe7c490: r1 = Null
    //     0xe7c490: mov             x1, NULL
    // 0xe7c494: r2 = 0
    //     0xe7c494: movz            x2, #0
    // 0xe7c498: r0 = _GrowableList()
    //     0xe7c498: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7c49c: stur            x0, [fp, #-0x60]
    // 0xe7c4a0: r1 = Function '<anonymous closure>':.
    //     0xe7c4a0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49ef0] AnonymousClosure: (0xe7c564), in [package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart] ZiarahRemoteRepository::findAllProvince (0xe7c3e0)
    //     0xe7c4a4: ldr             x1, [x1, #0xef0]
    // 0xe7c4a8: r2 = Null
    //     0xe7c4a8: mov             x2, NULL
    // 0xe7c4ac: r0 = AllocateClosure()
    //     0xe7c4ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7c4b0: mov             x1, x0
    // 0xe7c4b4: ldur            x0, [fp, #-0x60]
    // 0xe7c4b8: r2 = LoadClassIdInstr(r0)
    //     0xe7c4b8: ldur            x2, [x0, #-1]
    //     0xe7c4bc: ubfx            x2, x2, #0xc, #0x14
    // 0xe7c4c0: r16 = <Area>
    //     0xe7c4c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee18] TypeArguments: <Area>
    //     0xe7c4c4: ldr             x16, [x16, #0xe18]
    // 0xe7c4c8: stp             x0, x16, [SP, #8]
    // 0xe7c4cc: str             x1, [SP]
    // 0xe7c4d0: mov             x0, x2
    // 0xe7c4d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7c4d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7c4d8: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7c4d8: movz            x17, #0xf28c
    //     0xe7c4dc: add             lr, x0, x17
    //     0xe7c4e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c4e4: blr             lr
    // 0xe7c4e8: r1 = LoadClassIdInstr(r0)
    //     0xe7c4e8: ldur            x1, [x0, #-1]
    //     0xe7c4ec: ubfx            x1, x1, #0xc, #0x14
    // 0xe7c4f0: mov             x16, x0
    // 0xe7c4f4: mov             x0, x1
    // 0xe7c4f8: mov             x1, x16
    // 0xe7c4fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7c4fc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7c500: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7c500: movz            x17, #0xd889
    //     0xe7c504: add             lr, x0, x17
    //     0xe7c508: ldr             lr, [x21, lr, lsl #3]
    //     0xe7c50c: blr             lr
    // 0xe7c510: r1 = <List<Area>>
    //     0xe7c510: add             x1, PP, #0x34, lsl #12  ; [pp+0x344d0] TypeArguments: <List<Area>>
    //     0xe7c514: ldr             x1, [x1, #0x4d0]
    // 0xe7c518: stur            x0, [fp, #-0x60]
    // 0xe7c51c: r0 = _$SuccessImpl()
    //     0xe7c51c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7c520: mov             x1, x0
    // 0xe7c524: ldur            x0, [fp, #-0x60]
    // 0xe7c528: StoreField: r1->field_b = r0
    //     0xe7c528: stur            w0, [x1, #0xb]
    // 0xe7c52c: mov             x0, x1
    // 0xe7c530: r0 = ReturnAsyncNotFuture()
    //     0xe7c530: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7c534: sub             SP, fp, #0x78
    // 0xe7c538: mov             x1, x0
    // 0xe7c53c: r0 = getDioException()
    //     0xe7c53c: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7c540: r1 = <List<Area>>
    //     0xe7c540: add             x1, PP, #0x34, lsl #12  ; [pp+0x344d0] TypeArguments: <List<Area>>
    //     0xe7c544: ldr             x1, [x1, #0x4d0]
    // 0xe7c548: stur            x0, [fp, #-0x60]
    // 0xe7c54c: r0 = _$FailureImpl()
    //     0xe7c54c: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7c550: ldur            x1, [fp, #-0x60]
    // 0xe7c554: StoreField: r0->field_b = r1
    //     0xe7c554: stur            w1, [x0, #0xb]
    // 0xe7c558: r0 = ReturnAsyncNotFuture()
    //     0xe7c558: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7c55c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c55c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c560: b               #0xe7c400
  }
  [closure] Area <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7c564, size: 0x50
    // 0xe7c564: EnterFrame
    //     0xe7c564: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c568: mov             fp, SP
    // 0xe7c56c: CheckStackOverflow
    //     0xe7c56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c570: cmp             SP, x16
    //     0xe7c574: b.ls            #0xe7c5ac
    // 0xe7c578: ldr             x0, [fp, #0x10]
    // 0xe7c57c: r2 = Null
    //     0xe7c57c: mov             x2, NULL
    // 0xe7c580: r1 = Null
    //     0xe7c580: mov             x1, NULL
    // 0xe7c584: r8 = Map<String, dynamic>
    //     0xe7c584: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7c588: r3 = Null
    //     0xe7c588: add             x3, PP, #0x49, lsl #12  ; [pp+0x49ef8] Null
    //     0xe7c58c: ldr             x3, [x3, #0xef8]
    // 0xe7c590: r0 = Map<String, dynamic>()
    //     0xe7c590: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7c594: ldr             x2, [fp, #0x10]
    // 0xe7c598: r1 = Null
    //     0xe7c598: mov             x1, NULL
    // 0xe7c59c: r0 = Area.fromMap()
    //     0xe7c59c: bl              #0xe7c5b4  ; [package:nuonline/app/data/models/area.dart] Area::Area.fromMap
    // 0xe7c5a0: LeaveFrame
    //     0xe7c5a0: mov             SP, fp
    //     0xe7c5a4: ldp             fp, lr, [SP], #0x10
    // 0xe7c5a8: ret
    //     0xe7c5a8: ret             
    // 0xe7c5ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7c5ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7c5b0: b               #0xe7c578
  }
  _ findAllRegency(/* No info */) async {
    // ** addr: 0xe7c8ec, size: 0x1b4
    // 0xe7c8ec: EnterFrame
    //     0xe7c8ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe7c8f0: mov             fp, SP
    // 0xe7c8f4: AllocStack(0x90)
    //     0xe7c8f4: sub             SP, SP, #0x90
    // 0xe7c8f8: SetupParameters(ZiarahRemoteRepository this /* r1 => r1, fp-0x68 */, dynamic _ /* r2 => r2, fp-0x70 */)
    //     0xe7c8f8: stur            NULL, [fp, #-8]
    //     0xe7c8fc: stur            x1, [fp, #-0x68]
    //     0xe7c900: stur            x2, [fp, #-0x70]
    // 0xe7c904: CheckStackOverflow
    //     0xe7c904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7c908: cmp             SP, x16
    //     0xe7c90c: b.ls            #0xe7ca98
    // 0xe7c910: InitAsync() -> Future<ApiResult<List<Area>>>
    //     0xe7c910: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c1d0] TypeArguments: <ApiResult<List<Area>>>
    //     0xe7c914: ldr             x0, [x0, #0x1d0]
    //     0xe7c918: bl              #0x661298  ; InitAsyncStub
    // 0xe7c91c: ldur            x1, [fp, #-0x68]
    // 0xe7c920: ldur            x0, [fp, #-0x70]
    // 0xe7c924: LoadField: r3 = r1->field_7
    //     0xe7c924: ldur            w3, [x1, #7]
    // 0xe7c928: DecompressPointer r3
    //     0xe7c928: add             x3, x3, HEAP, lsl #32
    // 0xe7c92c: stur            x3, [fp, #-0x78]
    // 0xe7c930: r1 = Null
    //     0xe7c930: mov             x1, NULL
    // 0xe7c934: r2 = 4
    //     0xe7c934: movz            x2, #0x4
    // 0xe7c938: r0 = AllocateArray()
    //     0xe7c938: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7c93c: r16 = "/ziarah/location/"
    //     0xe7c93c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49ea8] "/ziarah/location/"
    //     0xe7c940: ldr             x16, [x16, #0xea8]
    // 0xe7c944: StoreField: r0->field_f = r16
    //     0xe7c944: stur            w16, [x0, #0xf]
    // 0xe7c948: ldur            x1, [fp, #-0x70]
    // 0xe7c94c: StoreField: r0->field_13 = r1
    //     0xe7c94c: stur            w1, [x0, #0x13]
    // 0xe7c950: str             x0, [SP]
    // 0xe7c954: r0 = _interpolate()
    //     0xe7c954: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xe7c958: ldur            x16, [fp, #-0x78]
    // 0xe7c95c: stp             x16, NULL, [SP, #8]
    // 0xe7c960: str             x0, [SP]
    // 0xe7c964: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7c964: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7c968: r0 = get()
    //     0xe7c968: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7c96c: mov             x1, x0
    // 0xe7c970: stur            x1, [fp, #-0x68]
    // 0xe7c974: r0 = Await()
    //     0xe7c974: bl              #0x661044  ; AwaitStub
    // 0xe7c978: LoadField: r3 = r0->field_b
    //     0xe7c978: ldur            w3, [x0, #0xb]
    // 0xe7c97c: DecompressPointer r3
    //     0xe7c97c: add             x3, x3, HEAP, lsl #32
    // 0xe7c980: mov             x0, x3
    // 0xe7c984: stur            x3, [fp, #-0x68]
    // 0xe7c988: r2 = Null
    //     0xe7c988: mov             x2, NULL
    // 0xe7c98c: r1 = Null
    //     0xe7c98c: mov             x1, NULL
    // 0xe7c990: r4 = 60
    //     0xe7c990: movz            x4, #0x3c
    // 0xe7c994: branchIfSmi(r0, 0xe7c9a0)
    //     0xe7c994: tbz             w0, #0, #0xe7c9a0
    // 0xe7c998: r4 = LoadClassIdInstr(r0)
    //     0xe7c998: ldur            x4, [x0, #-1]
    //     0xe7c99c: ubfx            x4, x4, #0xc, #0x14
    // 0xe7c9a0: sub             x4, x4, #0x5a
    // 0xe7c9a4: cmp             x4, #2
    // 0xe7c9a8: b.ls            #0xe7c9c0
    // 0xe7c9ac: r8 = List?
    //     0xe7c9ac: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xe7c9b0: ldr             x8, [x8, #0x140]
    // 0xe7c9b4: r3 = Null
    //     0xe7c9b4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49eb0] Null
    //     0xe7c9b8: ldr             x3, [x3, #0xeb0]
    // 0xe7c9bc: r0 = List?()
    //     0xe7c9bc: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xe7c9c0: ldur            x0, [fp, #-0x68]
    // 0xe7c9c4: cmp             w0, NULL
    // 0xe7c9c8: b.ne            #0xe7c9d8
    // 0xe7c9cc: r1 = Null
    //     0xe7c9cc: mov             x1, NULL
    // 0xe7c9d0: r2 = 0
    //     0xe7c9d0: movz            x2, #0
    // 0xe7c9d4: r0 = _GrowableList()
    //     0xe7c9d4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xe7c9d8: stur            x0, [fp, #-0x68]
    // 0xe7c9dc: r1 = Function '<anonymous closure>':.
    //     0xe7c9dc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49ec0] AnonymousClosure: (0xe7caa0), in [package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart] ZiarahRemoteRepository::findAllRegency (0xe7c8ec)
    //     0xe7c9e0: ldr             x1, [x1, #0xec0]
    // 0xe7c9e4: r2 = Null
    //     0xe7c9e4: mov             x2, NULL
    // 0xe7c9e8: r0 = AllocateClosure()
    //     0xe7c9e8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe7c9ec: mov             x1, x0
    // 0xe7c9f0: ldur            x0, [fp, #-0x68]
    // 0xe7c9f4: r2 = LoadClassIdInstr(r0)
    //     0xe7c9f4: ldur            x2, [x0, #-1]
    //     0xe7c9f8: ubfx            x2, x2, #0xc, #0x14
    // 0xe7c9fc: r16 = <Area>
    //     0xe7c9fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee18] TypeArguments: <Area>
    //     0xe7ca00: ldr             x16, [x16, #0xe18]
    // 0xe7ca04: stp             x0, x16, [SP, #8]
    // 0xe7ca08: str             x1, [SP]
    // 0xe7ca0c: mov             x0, x2
    // 0xe7ca10: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe7ca10: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe7ca14: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xe7ca14: movz            x17, #0xf28c
    //     0xe7ca18: add             lr, x0, x17
    //     0xe7ca1c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ca20: blr             lr
    // 0xe7ca24: r1 = LoadClassIdInstr(r0)
    //     0xe7ca24: ldur            x1, [x0, #-1]
    //     0xe7ca28: ubfx            x1, x1, #0xc, #0x14
    // 0xe7ca2c: mov             x16, x0
    // 0xe7ca30: mov             x0, x1
    // 0xe7ca34: mov             x1, x16
    // 0xe7ca38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe7ca38: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe7ca3c: r0 = GDT[cid_x0 + 0xd889]()
    //     0xe7ca3c: movz            x17, #0xd889
    //     0xe7ca40: add             lr, x0, x17
    //     0xe7ca44: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ca48: blr             lr
    // 0xe7ca4c: r1 = <List<Area>>
    //     0xe7ca4c: add             x1, PP, #0x34, lsl #12  ; [pp+0x344d0] TypeArguments: <List<Area>>
    //     0xe7ca50: ldr             x1, [x1, #0x4d0]
    // 0xe7ca54: stur            x0, [fp, #-0x68]
    // 0xe7ca58: r0 = _$SuccessImpl()
    //     0xe7ca58: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7ca5c: mov             x1, x0
    // 0xe7ca60: ldur            x0, [fp, #-0x68]
    // 0xe7ca64: StoreField: r1->field_b = r0
    //     0xe7ca64: stur            w0, [x1, #0xb]
    // 0xe7ca68: mov             x0, x1
    // 0xe7ca6c: r0 = ReturnAsyncNotFuture()
    //     0xe7ca6c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7ca70: sub             SP, fp, #0x90
    // 0xe7ca74: mov             x1, x0
    // 0xe7ca78: r0 = getDioException()
    //     0xe7ca78: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7ca7c: r1 = <List<Area>>
    //     0xe7ca7c: add             x1, PP, #0x34, lsl #12  ; [pp+0x344d0] TypeArguments: <List<Area>>
    //     0xe7ca80: ldr             x1, [x1, #0x4d0]
    // 0xe7ca84: stur            x0, [fp, #-0x68]
    // 0xe7ca88: r0 = _$FailureImpl()
    //     0xe7ca88: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7ca8c: ldur            x1, [fp, #-0x68]
    // 0xe7ca90: StoreField: r0->field_b = r1
    //     0xe7ca90: stur            w1, [x0, #0xb]
    // 0xe7ca94: r0 = ReturnAsyncNotFuture()
    //     0xe7ca94: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7ca98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ca98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ca9c: b               #0xe7c910
  }
  [closure] Area <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xe7caa0, size: 0x50
    // 0xe7caa0: EnterFrame
    //     0xe7caa0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7caa4: mov             fp, SP
    // 0xe7caa8: CheckStackOverflow
    //     0xe7caa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7caac: cmp             SP, x16
    //     0xe7cab0: b.ls            #0xe7cae8
    // 0xe7cab4: ldr             x0, [fp, #0x10]
    // 0xe7cab8: r2 = Null
    //     0xe7cab8: mov             x2, NULL
    // 0xe7cabc: r1 = Null
    //     0xe7cabc: mov             x1, NULL
    // 0xe7cac0: r8 = Map<String, dynamic>
    //     0xe7cac0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0xe7cac4: r3 = Null
    //     0xe7cac4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49ec8] Null
    //     0xe7cac8: ldr             x3, [x3, #0xec8]
    // 0xe7cacc: r0 = Map<String, dynamic>()
    //     0xe7cacc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0xe7cad0: ldr             x2, [fp, #0x10]
    // 0xe7cad4: r1 = Null
    //     0xe7cad4: mov             x1, NULL
    // 0xe7cad8: r0 = Area.fromMap()
    //     0xe7cad8: bl              #0xe7c5b4  ; [package:nuonline/app/data/models/area.dart] Area::Area.fromMap
    // 0xe7cadc: LeaveFrame
    //     0xe7cadc: mov             SP, fp
    //     0xe7cae0: ldp             fp, lr, [SP], #0x10
    // 0xe7cae4: ret
    //     0xe7cae4: ret             
    // 0xe7cae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7cae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7caec: b               #0xe7cab4
  }
  _ findAll(/* No info */) async {
    // ** addr: 0xe7caf0, size: 0x4cc
    // 0xe7caf0: EnterFrame
    //     0xe7caf0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7caf4: mov             fp, SP
    // 0xe7caf8: AllocStack(0x110)
    //     0xe7caf8: sub             SP, SP, #0x110
    // 0xe7cafc: SetupParameters(ZiarahRemoteRepository this /* r1 => r1, fp-0xc0 */, dynamic _ /* r2 => r2, fp-0xc8 */, {dynamic categoryId = Null /* r5, fp-0xb8 */, dynamic provinceId = Null /* r6, fp-0xb0 */, dynamic regencyId = Null /* r7, fp-0xa8 */, dynamic search = "" /* r3, fp-0xa0 */})
    //     0xe7cafc: stur            NULL, [fp, #-8]
    //     0xe7cb00: stur            x1, [fp, #-0xc0]
    //     0xe7cb04: stur            x2, [fp, #-0xc8]
    //     0xe7cb08: stur            x4, [fp, #-0xd0]
    //     0xe7cb0c: ldur            w0, [x4, #0x13]
    //     0xe7cb10: ldur            w3, [x4, #0x1f]
    //     0xe7cb14: add             x3, x3, HEAP, lsl #32
    //     0xe7cb18: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c48] "categoryId"
    //     0xe7cb1c: ldr             x16, [x16, #0xc48]
    //     0xe7cb20: cmp             w3, w16
    //     0xe7cb24: b.ne            #0xe7cb48
    //     0xe7cb28: ldur            w3, [x4, #0x23]
    //     0xe7cb2c: add             x3, x3, HEAP, lsl #32
    //     0xe7cb30: sub             w5, w0, w3
    //     0xe7cb34: add             x3, fp, w5, sxtw #2
    //     0xe7cb38: ldr             x3, [x3, #8]
    //     0xe7cb3c: mov             x5, x3
    //     0xe7cb40: movz            x3, #0x1
    //     0xe7cb44: b               #0xe7cb50
    //     0xe7cb48: mov             x5, NULL
    //     0xe7cb4c: movz            x3, #0
    //     0xe7cb50: stur            x5, [fp, #-0xb8]
    //     0xe7cb54: lsl             x6, x3, #1
    //     0xe7cb58: lsl             w7, w6, #1
    //     0xe7cb5c: add             w8, w7, #8
    //     0xe7cb60: add             x16, x4, w8, sxtw #1
    //     0xe7cb64: ldur            w9, [x16, #0xf]
    //     0xe7cb68: add             x9, x9, HEAP, lsl #32
    //     0xe7cb6c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe90] "provinceId"
    //     0xe7cb70: ldr             x16, [x16, #0xe90]
    //     0xe7cb74: cmp             w9, w16
    //     0xe7cb78: b.ne            #0xe7cbac
    //     0xe7cb7c: add             w3, w7, #0xa
    //     0xe7cb80: add             x16, x4, w3, sxtw #1
    //     0xe7cb84: ldur            w7, [x16, #0xf]
    //     0xe7cb88: add             x7, x7, HEAP, lsl #32
    //     0xe7cb8c: sub             w3, w0, w7
    //     0xe7cb90: add             x7, fp, w3, sxtw #2
    //     0xe7cb94: ldr             x7, [x7, #8]
    //     0xe7cb98: add             w3, w6, #2
    //     0xe7cb9c: sbfx            x6, x3, #1, #0x1f
    //     0xe7cba0: mov             x3, x6
    //     0xe7cba4: mov             x6, x7
    //     0xe7cba8: b               #0xe7cbb0
    //     0xe7cbac: mov             x6, NULL
    //     0xe7cbb0: stur            x6, [fp, #-0xb0]
    //     0xe7cbb4: lsl             x7, x3, #1
    //     0xe7cbb8: lsl             w8, w7, #1
    //     0xe7cbbc: add             w9, w8, #8
    //     0xe7cbc0: add             x16, x4, w9, sxtw #1
    //     0xe7cbc4: ldur            w10, [x16, #0xf]
    //     0xe7cbc8: add             x10, x10, HEAP, lsl #32
    //     0xe7cbcc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe98] "regencyId"
    //     0xe7cbd0: ldr             x16, [x16, #0xe98]
    //     0xe7cbd4: cmp             w10, w16
    //     0xe7cbd8: b.ne            #0xe7cc0c
    //     0xe7cbdc: add             w3, w8, #0xa
    //     0xe7cbe0: add             x16, x4, w3, sxtw #1
    //     0xe7cbe4: ldur            w8, [x16, #0xf]
    //     0xe7cbe8: add             x8, x8, HEAP, lsl #32
    //     0xe7cbec: sub             w3, w0, w8
    //     0xe7cbf0: add             x8, fp, w3, sxtw #2
    //     0xe7cbf4: ldr             x8, [x8, #8]
    //     0xe7cbf8: add             w3, w7, #2
    //     0xe7cbfc: sbfx            x7, x3, #1, #0x1f
    //     0xe7cc00: mov             x3, x7
    //     0xe7cc04: mov             x7, x8
    //     0xe7cc08: b               #0xe7cc10
    //     0xe7cc0c: mov             x7, NULL
    //     0xe7cc10: stur            x7, [fp, #-0xa8]
    //     0xe7cc14: lsl             x8, x3, #1
    //     0xe7cc18: lsl             w3, w8, #1
    //     0xe7cc1c: add             w8, w3, #8
    //     0xe7cc20: add             x16, x4, w8, sxtw #1
    //     0xe7cc24: ldur            w9, [x16, #0xf]
    //     0xe7cc28: add             x9, x9, HEAP, lsl #32
    //     0xe7cc2c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d090] "search"
    //     0xe7cc30: ldr             x16, [x16, #0x90]
    //     0xe7cc34: cmp             w9, w16
    //     0xe7cc38: b.ne            #0xe7cc60
    //     0xe7cc3c: add             w8, w3, #0xa
    //     0xe7cc40: add             x16, x4, w8, sxtw #1
    //     0xe7cc44: ldur            w3, [x16, #0xf]
    //     0xe7cc48: add             x3, x3, HEAP, lsl #32
    //     0xe7cc4c: sub             w8, w0, w3
    //     0xe7cc50: add             x0, fp, w8, sxtw #2
    //     0xe7cc54: ldr             x0, [x0, #8]
    //     0xe7cc58: mov             x3, x0
    //     0xe7cc5c: b               #0xe7cc64
    //     0xe7cc60: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    //     0xe7cc64: stur            x3, [fp, #-0xa0]
    // 0xe7cc68: CheckStackOverflow
    //     0xe7cc68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7cc6c: cmp             SP, x16
    //     0xe7cc70: b.ls            #0xe7cfb4
    // 0xe7cc74: InitAsync() -> Future<ApiResult<List<Ziarah>>>
    //     0xe7cc74: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2bdd8] TypeArguments: <ApiResult<List<Ziarah>>>
    //     0xe7cc78: ldr             x0, [x0, #0xdd8]
    //     0xe7cc7c: bl              #0x661298  ; InitAsyncStub
    // 0xe7cc80: ldur            x0, [fp, #-0xa0]
    // 0xe7cc84: LoadField: r2 = r0->field_7
    //     0xe7cc84: ldur            w2, [x0, #7]
    // 0xe7cc88: stur            x2, [fp, #-0xd8]
    // 0xe7cc8c: cbnz            w2, #0xe7cc9c
    // 0xe7cc90: r7 = "/ziarah"
    //     0xe7cc90: add             x7, PP, #0xf, lsl #12  ; [pp+0xfc60] "/ziarah"
    //     0xe7cc94: ldr             x7, [x7, #0xc60]
    // 0xe7cc98: b               #0xe7cca4
    // 0xe7cc9c: r7 = "/ziarah/search"
    //     0xe7cc9c: add             x7, PP, #0x37, lsl #12  ; [pp+0x37c50] "/ziarah/search"
    //     0xe7cca0: ldr             x7, [x7, #0xc50]
    // 0xe7cca4: ldur            x3, [fp, #-0xc8]
    // 0xe7cca8: ldur            x4, [fp, #-0xb8]
    // 0xe7ccac: ldur            x5, [fp, #-0xb0]
    // 0xe7ccb0: ldur            x6, [fp, #-0xa8]
    // 0xe7ccb4: ldur            x1, [fp, #-0xc0]
    // 0xe7ccb8: stur            x7, [fp, #-0xd0]
    // 0xe7ccbc: r0 = query()
    //     0xe7ccbc: bl              #0xe7d474  ; [package:nuonline/app/data/repositories/ziarah/ziarah_remote_repository.dart] ZiarahRemoteRepository::query
    // 0xe7ccc0: r1 = Null
    //     0xe7ccc0: mov             x1, NULL
    // 0xe7ccc4: r2 = 4
    //     0xe7ccc4: movz            x2, #0x4
    // 0xe7ccc8: stur            x0, [fp, #-0xe0]
    // 0xe7cccc: r0 = AllocateArray()
    //     0xe7cccc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7ccd0: mov             x2, x0
    // 0xe7ccd4: stur            x2, [fp, #-0xe8]
    // 0xe7ccd8: r16 = "page"
    //     0xe7ccd8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe7ccdc: ldr             x16, [x16, #0x300]
    // 0xe7cce0: StoreField: r2->field_f = r16
    //     0xe7cce0: stur            w16, [x2, #0xf]
    // 0xe7cce4: ldur            x3, [fp, #-0xc8]
    // 0xe7cce8: r0 = BoxInt64Instr(r3)
    //     0xe7cce8: sbfiz           x0, x3, #1, #0x1f
    //     0xe7ccec: cmp             x3, x0, asr #1
    //     0xe7ccf0: b.eq            #0xe7ccfc
    //     0xe7ccf4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe7ccf8: stur            x3, [x0, #7]
    // 0xe7ccfc: r1 = 60
    //     0xe7ccfc: movz            x1, #0x3c
    // 0xe7cd00: branchIfSmi(r0, 0xe7cd0c)
    //     0xe7cd00: tbz             w0, #0, #0xe7cd0c
    // 0xe7cd04: r1 = LoadClassIdInstr(r0)
    //     0xe7cd04: ldur            x1, [x0, #-1]
    //     0xe7cd08: ubfx            x1, x1, #0xc, #0x14
    // 0xe7cd0c: str             x0, [SP]
    // 0xe7cd10: mov             x0, x1
    // 0xe7cd14: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7cd14: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7cd18: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7cd18: movz            x17, #0x2b03
    //     0xe7cd1c: add             lr, x0, x17
    //     0xe7cd20: ldr             lr, [x21, lr, lsl #3]
    //     0xe7cd24: blr             lr
    // 0xe7cd28: ldur            x1, [fp, #-0xe8]
    // 0xe7cd2c: ArrayStore: r1[1] = r0  ; List_4
    //     0xe7cd2c: add             x25, x1, #0x13
    //     0xe7cd30: str             w0, [x25]
    //     0xe7cd34: tbz             w0, #0, #0xe7cd50
    //     0xe7cd38: ldurb           w16, [x1, #-1]
    //     0xe7cd3c: ldurb           w17, [x0, #-1]
    //     0xe7cd40: and             x16, x17, x16, lsr #2
    //     0xe7cd44: tst             x16, HEAP, lsr #32
    //     0xe7cd48: b.eq            #0xe7cd50
    //     0xe7cd4c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7cd50: r16 = <String, String?>
    //     0xe7cd50: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7cd54: ldr             x16, [x16, #0x198]
    // 0xe7cd58: ldur            lr, [fp, #-0xe8]
    // 0xe7cd5c: stp             lr, x16, [SP]
    // 0xe7cd60: r0 = Map._fromLiteral()
    //     0xe7cd60: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe7cd64: ldur            x1, [fp, #-0xe0]
    // 0xe7cd68: mov             x2, x0
    // 0xe7cd6c: r0 = addAll()
    //     0xe7cd6c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xe7cd70: ldur            x0, [fp, #-0xb8]
    // 0xe7cd74: cmp             w0, NULL
    // 0xe7cd78: r16 = true
    //     0xe7cd78: add             x16, NULL, #0x20  ; true
    // 0xe7cd7c: r17 = false
    //     0xe7cd7c: add             x17, NULL, #0x30  ; false
    // 0xe7cd80: csel            x1, x16, x17, ne
    // 0xe7cd84: stur            x1, [fp, #-0xe8]
    // 0xe7cd88: r2 = 60
    //     0xe7cd88: movz            x2, #0x3c
    // 0xe7cd8c: branchIfSmi(r0, 0xe7cd98)
    //     0xe7cd8c: tbz             w0, #0, #0xe7cd98
    // 0xe7cd90: r2 = LoadClassIdInstr(r0)
    //     0xe7cd90: ldur            x2, [x0, #-1]
    //     0xe7cd94: ubfx            x2, x2, #0xc, #0x14
    // 0xe7cd98: str             x0, [SP]
    // 0xe7cd9c: mov             x0, x2
    // 0xe7cda0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7cda0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7cda4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7cda4: movz            x17, #0x2b03
    //     0xe7cda8: add             lr, x0, x17
    //     0xe7cdac: ldr             lr, [x21, lr, lsl #3]
    //     0xe7cdb0: blr             lr
    // 0xe7cdb4: r16 = <String, String?>
    //     0xe7cdb4: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7cdb8: ldr             x16, [x16, #0x198]
    // 0xe7cdbc: ldur            lr, [fp, #-0xe0]
    // 0xe7cdc0: stp             lr, x16, [SP, #0x18]
    // 0xe7cdc4: ldur            x16, [fp, #-0xe8]
    // 0xe7cdc8: r30 = "category_id"
    //     0xe7cdc8: add             lr, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0xe7cdcc: ldr             lr, [lr, #0xe70]
    // 0xe7cdd0: stp             lr, x16, [SP, #8]
    // 0xe7cdd4: str             x0, [SP]
    // 0xe7cdd8: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0xe7cdd8: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0xe7cddc: r0 = MapExtension.addIf()
    //     0xe7cddc: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0xe7cde0: ldur            x0, [fp, #-0xb0]
    // 0xe7cde4: cmp             w0, NULL
    // 0xe7cde8: r16 = true
    //     0xe7cde8: add             x16, NULL, #0x20  ; true
    // 0xe7cdec: r17 = false
    //     0xe7cdec: add             x17, NULL, #0x30  ; false
    // 0xe7cdf0: csel            x1, x16, x17, ne
    // 0xe7cdf4: stur            x1, [fp, #-0xb8]
    // 0xe7cdf8: r2 = 60
    //     0xe7cdf8: movz            x2, #0x3c
    // 0xe7cdfc: branchIfSmi(r0, 0xe7ce08)
    //     0xe7cdfc: tbz             w0, #0, #0xe7ce08
    // 0xe7ce00: r2 = LoadClassIdInstr(r0)
    //     0xe7ce00: ldur            x2, [x0, #-1]
    //     0xe7ce04: ubfx            x2, x2, #0xc, #0x14
    // 0xe7ce08: str             x0, [SP]
    // 0xe7ce0c: mov             x0, x2
    // 0xe7ce10: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7ce10: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7ce14: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7ce14: movz            x17, #0x2b03
    //     0xe7ce18: add             lr, x0, x17
    //     0xe7ce1c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ce20: blr             lr
    // 0xe7ce24: r16 = <String, String?>
    //     0xe7ce24: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7ce28: ldr             x16, [x16, #0x198]
    // 0xe7ce2c: ldur            lr, [fp, #-0xe0]
    // 0xe7ce30: stp             lr, x16, [SP, #0x18]
    // 0xe7ce34: ldur            x16, [fp, #-0xb8]
    // 0xe7ce38: r30 = "province_id"
    //     0xe7ce38: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2d100] "province_id"
    //     0xe7ce3c: ldr             lr, [lr, #0x100]
    // 0xe7ce40: stp             lr, x16, [SP, #8]
    // 0xe7ce44: str             x0, [SP]
    // 0xe7ce48: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0xe7ce48: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0xe7ce4c: r0 = MapExtension.addIf()
    //     0xe7ce4c: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0xe7ce50: ldur            x0, [fp, #-0xa8]
    // 0xe7ce54: cmp             w0, NULL
    // 0xe7ce58: r16 = true
    //     0xe7ce58: add             x16, NULL, #0x20  ; true
    // 0xe7ce5c: r17 = false
    //     0xe7ce5c: add             x17, NULL, #0x30  ; false
    // 0xe7ce60: csel            x1, x16, x17, ne
    // 0xe7ce64: stur            x1, [fp, #-0xb0]
    // 0xe7ce68: r2 = 60
    //     0xe7ce68: movz            x2, #0x3c
    // 0xe7ce6c: branchIfSmi(r0, 0xe7ce78)
    //     0xe7ce6c: tbz             w0, #0, #0xe7ce78
    // 0xe7ce70: r2 = LoadClassIdInstr(r0)
    //     0xe7ce70: ldur            x2, [x0, #-1]
    //     0xe7ce74: ubfx            x2, x2, #0xc, #0x14
    // 0xe7ce78: str             x0, [SP]
    // 0xe7ce7c: mov             x0, x2
    // 0xe7ce80: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe7ce80: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe7ce84: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe7ce84: movz            x17, #0x2b03
    //     0xe7ce88: add             lr, x0, x17
    //     0xe7ce8c: ldr             lr, [x21, lr, lsl #3]
    //     0xe7ce90: blr             lr
    // 0xe7ce94: r16 = <String, String?>
    //     0xe7ce94: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7ce98: ldr             x16, [x16, #0x198]
    // 0xe7ce9c: ldur            lr, [fp, #-0xe0]
    // 0xe7cea0: stp             lr, x16, [SP, #0x18]
    // 0xe7cea4: ldur            x16, [fp, #-0xb0]
    // 0xe7cea8: r30 = "regency_id"
    //     0xe7cea8: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2d118] "regency_id"
    //     0xe7ceac: ldr             lr, [lr, #0x118]
    // 0xe7ceb0: stp             lr, x16, [SP, #8]
    // 0xe7ceb4: str             x0, [SP]
    // 0xe7ceb8: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0xe7ceb8: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0xe7cebc: r0 = MapExtension.addIf()
    //     0xe7cebc: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0xe7cec0: ldur            x0, [fp, #-0xd8]
    // 0xe7cec4: cbnz            w0, #0xe7ced0
    // 0xe7cec8: r1 = false
    //     0xe7cec8: add             x1, NULL, #0x30  ; false
    // 0xe7cecc: b               #0xe7ced4
    // 0xe7ced0: r1 = true
    //     0xe7ced0: add             x1, NULL, #0x20  ; true
    // 0xe7ced4: r16 = <String, String?>
    //     0xe7ced4: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7ced8: ldr             x16, [x16, #0x198]
    // 0xe7cedc: ldur            lr, [fp, #-0xe0]
    // 0xe7cee0: stp             lr, x16, [SP, #0x18]
    // 0xe7cee4: r16 = "q"
    //     0xe7cee4: add             x16, PP, #0x29, lsl #12  ; [pp+0x291d0] "q"
    //     0xe7cee8: ldr             x16, [x16, #0x1d0]
    // 0xe7ceec: stp             x16, x1, [SP, #8]
    // 0xe7cef0: ldur            x16, [fp, #-0xa0]
    // 0xe7cef4: str             x16, [SP]
    // 0xe7cef8: r4 = const [0x2, 0x4, 0x4, 0x4, null]
    //     0xe7cef8: ldr             x4, [PP, #0x2108]  ; [pp+0x2108] List(5) [0x2, 0x4, 0x4, 0x4, Null]
    // 0xe7cefc: r0 = MapExtension.addIf()
    //     0xe7cefc: bl              #0x72b8c0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::MapExtension.addIf
    // 0xe7cf00: ldur            x0, [fp, #-0xc0]
    // 0xe7cf04: LoadField: r1 = r0->field_7
    //     0xe7cf04: ldur            w1, [x0, #7]
    // 0xe7cf08: DecompressPointer r1
    //     0xe7cf08: add             x1, x1, HEAP, lsl #32
    // 0xe7cf0c: stp             x1, NULL, [SP, #0x10]
    // 0xe7cf10: ldur            x16, [fp, #-0xd0]
    // 0xe7cf14: ldur            lr, [fp, #-0xe0]
    // 0xe7cf18: stp             lr, x16, [SP]
    // 0xe7cf1c: r4 = const [0x1, 0x3, 0x3, 0x2, queryParameters, 0x2, null]
    //     0xe7cf1c: add             x4, PP, #0x29, lsl #12  ; [pp+0x292f0] List(7) [0x1, 0x3, 0x3, 0x2, "queryParameters", 0x2, Null]
    //     0xe7cf20: ldr             x4, [x4, #0x2f0]
    // 0xe7cf24: r0 = get()
    //     0xe7cf24: bl              #0x72aae4  ; [package:dio/src/dio/dio_for_native.dart] _DioForNative&Object&DioMixin::get
    // 0xe7cf28: mov             x1, x0
    // 0xe7cf2c: stur            x1, [fp, #-0xa0]
    // 0xe7cf30: r0 = Await()
    //     0xe7cf30: bl              #0x661044  ; AwaitStub
    // 0xe7cf34: stur            x0, [fp, #-0xa0]
    // 0xe7cf38: LoadField: r1 = r0->field_b
    //     0xe7cf38: ldur            w1, [x0, #0xb]
    // 0xe7cf3c: DecompressPointer r1
    //     0xe7cf3c: add             x1, x1, HEAP, lsl #32
    // 0xe7cf40: r0 = fromResponse()
    //     0xe7cf40: bl              #0xe7cfbc  ; [package:nuonline/app/data/models/ziarah.dart] Ziarah::fromResponse
    // 0xe7cf44: mov             x3, x0
    // 0xe7cf48: ldur            x0, [fp, #-0xa0]
    // 0xe7cf4c: stur            x3, [fp, #-0xa8]
    // 0xe7cf50: LoadField: r2 = r0->field_1b
    //     0xe7cf50: ldur            w2, [x0, #0x1b]
    // 0xe7cf54: DecompressPointer r2
    //     0xe7cf54: add             x2, x2, HEAP, lsl #32
    // 0xe7cf58: r1 = Null
    //     0xe7cf58: mov             x1, NULL
    // 0xe7cf5c: r0 = Pagination.fromHeaders()
    //     0xe7cf5c: bl              #0x6fc738  ; [package:nuonline/services/api_service/api_result.dart] Pagination::Pagination.fromHeaders
    // 0xe7cf60: r1 = <List<Ziarah>>
    //     0xe7cf60: add             x1, PP, #0x37, lsl #12  ; [pp+0x37c58] TypeArguments: <List<Ziarah>>
    //     0xe7cf64: ldr             x1, [x1, #0xc58]
    // 0xe7cf68: stur            x0, [fp, #-0xa0]
    // 0xe7cf6c: r0 = _$SuccessImpl()
    //     0xe7cf6c: bl              #0x6fc72c  ; Allocate_$SuccessImplStub -> _$SuccessImpl<X0> (size=0x14)
    // 0xe7cf70: mov             x1, x0
    // 0xe7cf74: ldur            x0, [fp, #-0xa8]
    // 0xe7cf78: StoreField: r1->field_b = r0
    //     0xe7cf78: stur            w0, [x1, #0xb]
    // 0xe7cf7c: ldur            x0, [fp, #-0xa0]
    // 0xe7cf80: StoreField: r1->field_f = r0
    //     0xe7cf80: stur            w0, [x1, #0xf]
    // 0xe7cf84: mov             x0, x1
    // 0xe7cf88: r0 = ReturnAsyncNotFuture()
    //     0xe7cf88: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7cf8c: sub             SP, fp, #0x110
    // 0xe7cf90: mov             x1, x0
    // 0xe7cf94: r0 = getDioException()
    //     0xe7cf94: bl              #0x6fc340  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getDioException
    // 0xe7cf98: r1 = <List<Ziarah>>
    //     0xe7cf98: add             x1, PP, #0x37, lsl #12  ; [pp+0x37c58] TypeArguments: <List<Ziarah>>
    //     0xe7cf9c: ldr             x1, [x1, #0xc58]
    // 0xe7cfa0: stur            x0, [fp, #-0xa0]
    // 0xe7cfa4: r0 = _$FailureImpl()
    //     0xe7cfa4: bl              #0x6fc334  ; Allocate_$FailureImplStub -> _$FailureImpl<X0> (size=0x10)
    // 0xe7cfa8: ldur            x1, [fp, #-0xa0]
    // 0xe7cfac: StoreField: r0->field_b = r1
    //     0xe7cfac: stur            w1, [x0, #0xb]
    // 0xe7cfb0: r0 = ReturnAsyncNotFuture()
    //     0xe7cfb0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xe7cfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7cfb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7cfb8: b               #0xe7cc74
  }
  get _ query(/* No info */) {
    // ** addr: 0xe7d474, size: 0x188
    // 0xe7d474: EnterFrame
    //     0xe7d474: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d478: mov             fp, SP
    // 0xe7d47c: AllocStack(0x20)
    //     0xe7d47c: sub             SP, SP, #0x20
    // 0xe7d480: CheckStackOverflow
    //     0xe7d480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d484: cmp             SP, x16
    //     0xe7d488: b.ls            #0xe7d5c0
    // 0xe7d48c: LoadField: r0 = r1->field_b
    //     0xe7d48c: ldur            w0, [x1, #0xb]
    // 0xe7d490: DecompressPointer r0
    //     0xe7d490: add             x0, x0, HEAP, lsl #32
    // 0xe7d494: mov             x1, x0
    // 0xe7d498: stur            x0, [fp, #-8]
    // 0xe7d49c: r0 = getCurrentLocation()
    //     0xe7d49c: bl              #0x900820  ; [package:nuonline/app/data/repositories/location_repository.dart] LocationRepository::getCurrentLocation
    // 0xe7d4a0: cmp             w0, NULL
    // 0xe7d4a4: b.ne            #0xe7d4b0
    // 0xe7d4a8: ldur            x1, [fp, #-8]
    // 0xe7d4ac: r0 = defaultLocation()
    //     0xe7d4ac: bl              #0x8ff808  ; [package:nuonline/app/data/repositories/location_repository.dart] LocationRepository::defaultLocation
    // 0xe7d4b0: stur            x0, [fp, #-8]
    // 0xe7d4b4: r1 = Null
    //     0xe7d4b4: mov             x1, NULL
    // 0xe7d4b8: r2 = 8
    //     0xe7d4b8: movz            x2, #0x8
    // 0xe7d4bc: r0 = AllocateArray()
    //     0xe7d4bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe7d4c0: stur            x0, [fp, #-0x10]
    // 0xe7d4c4: r16 = "long"
    //     0xe7d4c4: add             x16, PP, #8, lsl #12  ; [pp+0x8e68] "long"
    //     0xe7d4c8: ldr             x16, [x16, #0xe68]
    // 0xe7d4cc: StoreField: r0->field_f = r16
    //     0xe7d4cc: stur            w16, [x0, #0xf]
    // 0xe7d4d0: ldur            x1, [fp, #-8]
    // 0xe7d4d4: LoadField: d0 = r1->field_1f
    //     0xe7d4d4: ldur            d0, [x1, #0x1f]
    // 0xe7d4d8: r2 = inline_Allocate_Double()
    //     0xe7d4d8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xe7d4dc: add             x2, x2, #0x10
    //     0xe7d4e0: cmp             x3, x2
    //     0xe7d4e4: b.ls            #0xe7d5c8
    //     0xe7d4e8: str             x2, [THR, #0x50]  ; THR::top
    //     0xe7d4ec: sub             x2, x2, #0xf
    //     0xe7d4f0: movz            x3, #0xe15c
    //     0xe7d4f4: movk            x3, #0x3, lsl #16
    //     0xe7d4f8: stur            x3, [x2, #-1]
    // 0xe7d4fc: StoreField: r2->field_7 = d0
    //     0xe7d4fc: stur            d0, [x2, #7]
    // 0xe7d500: str             x2, [SP]
    // 0xe7d504: r0 = toString()
    //     0xe7d504: bl              #0xc45dcc  ; [dart:core] _Double::toString
    // 0xe7d508: ldur            x1, [fp, #-0x10]
    // 0xe7d50c: ArrayStore: r1[1] = r0  ; List_4
    //     0xe7d50c: add             x25, x1, #0x13
    //     0xe7d510: str             w0, [x25]
    //     0xe7d514: tbz             w0, #0, #0xe7d530
    //     0xe7d518: ldurb           w16, [x1, #-1]
    //     0xe7d51c: ldurb           w17, [x0, #-1]
    //     0xe7d520: and             x16, x17, x16, lsr #2
    //     0xe7d524: tst             x16, HEAP, lsr #32
    //     0xe7d528: b.eq            #0xe7d530
    //     0xe7d52c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7d530: ldur            x1, [fp, #-0x10]
    // 0xe7d534: r16 = "lat"
    //     0xe7d534: add             x16, PP, #0x37, lsl #12  ; [pp+0x37d80] "lat"
    //     0xe7d538: ldr             x16, [x16, #0xd80]
    // 0xe7d53c: ArrayStore: r1[0] = r16  ; List_4
    //     0xe7d53c: stur            w16, [x1, #0x17]
    // 0xe7d540: ldur            x0, [fp, #-8]
    // 0xe7d544: LoadField: d0 = r0->field_27
    //     0xe7d544: ldur            d0, [x0, #0x27]
    // 0xe7d548: r0 = inline_Allocate_Double()
    //     0xe7d548: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xe7d54c: add             x0, x0, #0x10
    //     0xe7d550: cmp             x2, x0
    //     0xe7d554: b.ls            #0xe7d5e4
    //     0xe7d558: str             x0, [THR, #0x50]  ; THR::top
    //     0xe7d55c: sub             x0, x0, #0xf
    //     0xe7d560: movz            x2, #0xe15c
    //     0xe7d564: movk            x2, #0x3, lsl #16
    //     0xe7d568: stur            x2, [x0, #-1]
    // 0xe7d56c: StoreField: r0->field_7 = d0
    //     0xe7d56c: stur            d0, [x0, #7]
    // 0xe7d570: str             x0, [SP]
    // 0xe7d574: r0 = toString()
    //     0xe7d574: bl              #0xc45dcc  ; [dart:core] _Double::toString
    // 0xe7d578: ldur            x1, [fp, #-0x10]
    // 0xe7d57c: ArrayStore: r1[3] = r0  ; List_4
    //     0xe7d57c: add             x25, x1, #0x1b
    //     0xe7d580: str             w0, [x25]
    //     0xe7d584: tbz             w0, #0, #0xe7d5a0
    //     0xe7d588: ldurb           w16, [x1, #-1]
    //     0xe7d58c: ldurb           w17, [x0, #-1]
    //     0xe7d590: and             x16, x17, x16, lsr #2
    //     0xe7d594: tst             x16, HEAP, lsr #32
    //     0xe7d598: b.eq            #0xe7d5a0
    //     0xe7d59c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe7d5a0: r16 = <String, String?>
    //     0xe7d5a0: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe7d5a4: ldr             x16, [x16, #0x198]
    // 0xe7d5a8: ldur            lr, [fp, #-0x10]
    // 0xe7d5ac: stp             lr, x16, [SP]
    // 0xe7d5b0: r0 = Map._fromLiteral()
    //     0xe7d5b0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe7d5b4: LeaveFrame
    //     0xe7d5b4: mov             SP, fp
    //     0xe7d5b8: ldp             fp, lr, [SP], #0x10
    // 0xe7d5bc: ret
    //     0xe7d5bc: ret             
    // 0xe7d5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d5c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d5c4: b               #0xe7d48c
    // 0xe7d5c8: SaveReg d0
    //     0xe7d5c8: str             q0, [SP, #-0x10]!
    // 0xe7d5cc: stp             x0, x1, [SP, #-0x10]!
    // 0xe7d5d0: r0 = AllocateDouble()
    //     0xe7d5d0: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7d5d4: mov             x2, x0
    // 0xe7d5d8: ldp             x0, x1, [SP], #0x10
    // 0xe7d5dc: RestoreReg d0
    //     0xe7d5dc: ldr             q0, [SP], #0x10
    // 0xe7d5e0: b               #0xe7d4fc
    // 0xe7d5e4: SaveReg d0
    //     0xe7d5e4: str             q0, [SP, #-0x10]!
    // 0xe7d5e8: SaveReg r1
    //     0xe7d5e8: str             x1, [SP, #-8]!
    // 0xe7d5ec: r0 = AllocateDouble()
    //     0xe7d5ec: bl              #0xec2254  ; AllocateDoubleStub
    // 0xe7d5f0: RestoreReg r1
    //     0xe7d5f0: ldr             x1, [SP], #8
    // 0xe7d5f4: RestoreReg d0
    //     0xe7d5f4: ldr             q0, [SP], #0x10
    // 0xe7d5f8: b               #0xe7d56c
  }
  _ findCurrentLocation(/* No info */) {
    // ** addr: 0xe7d9e8, size: 0x14
    // 0xe7d9e8: EnterFrame
    //     0xe7d9e8: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d9ec: mov             fp, SP
    // 0xe7d9f0: r0 = UnimplementedError()
    //     0xe7d9f0: bl              #0x645560  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xe7d9f4: r0 = Throw()
    //     0xe7d9f4: bl              #0xec04b8  ; ThrowStub
    // 0xe7d9f8: brk             #0
  }
}
