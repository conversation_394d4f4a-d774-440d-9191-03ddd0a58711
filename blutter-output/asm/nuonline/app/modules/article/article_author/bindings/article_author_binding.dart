// lib: , url: package:nuonline/app/modules/article/article_author/bindings/article_author_binding.dart

// class id: 1050114, size: 0x8
class :: {
}

// class id: 2199, size: 0x8, field offset: 0x8
class ArticleAuthorBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80baf8, size: 0x130
    // 0x80baf8: EnterFrame
    //     0x80baf8: stp             fp, lr, [SP, #-0x10]!
    //     0x80bafc: mov             fp, SP
    // 0x80bb00: AllocStack(0x20)
    //     0x80bb00: sub             SP, SP, #0x20
    // 0x80bb04: CheckStackOverflow
    //     0x80bb04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80bb08: cmp             SP, x16
    //     0x80bb0c: b.ls            #0x80bc20
    // 0x80bb10: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80bb10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80bb14: ldr             x0, [x0, #0x2670]
    //     0x80bb18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80bb1c: cmp             w0, w16
    //     0x80bb20: b.ne            #0x80bb2c
    //     0x80bb24: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80bb28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80bb2c: r0 = GetNavigation.arguments()
    //     0x80bb2c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80bb30: r16 = "author"
    //     0x80bb30: add             x16, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x80bb34: ldr             x16, [x16, #0xbf8]
    // 0x80bb38: stp             x16, x0, [SP]
    // 0x80bb3c: r4 = 0
    //     0x80bb3c: movz            x4, #0
    // 0x80bb40: ldr             x0, [SP, #8]
    // 0x80bb44: r16 = UnlinkedCall_0x5f3c08
    //     0x80bb44: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c00] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80bb48: add             x16, x16, #0xc00
    // 0x80bb4c: ldp             x5, lr, [x16]
    // 0x80bb50: blr             lr
    // 0x80bb54: mov             x3, x0
    // 0x80bb58: r2 = Null
    //     0x80bb58: mov             x2, NULL
    // 0x80bb5c: r1 = Null
    //     0x80bb5c: mov             x1, NULL
    // 0x80bb60: stur            x3, [fp, #-8]
    // 0x80bb64: r4 = 60
    //     0x80bb64: movz            x4, #0x3c
    // 0x80bb68: branchIfSmi(r0, 0x80bb74)
    //     0x80bb68: tbz             w0, #0, #0x80bb74
    // 0x80bb6c: r4 = LoadClassIdInstr(r0)
    //     0x80bb6c: ldur            x4, [x0, #-1]
    //     0x80bb70: ubfx            x4, x4, #0xc, #0x14
    // 0x80bb74: r17 = 5596
    //     0x80bb74: movz            x17, #0x15dc
    // 0x80bb78: cmp             x4, x17
    // 0x80bb7c: b.eq            #0x80bb94
    // 0x80bb80: r8 = Author
    //     0x80bb80: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b4b8] Type: Author
    //     0x80bb84: ldr             x8, [x8, #0x4b8]
    // 0x80bb88: r3 = Null
    //     0x80bb88: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c10] Null
    //     0x80bb8c: ldr             x3, [x3, #0xc10]
    // 0x80bb90: r0 = Author()
    //     0x80bb90: bl              #0x80cd68  ; IsType_Author_Stub
    // 0x80bb94: ldur            x0, [fp, #-8]
    // 0x80bb98: LoadField: r2 = r0->field_7
    //     0x80bb98: ldur            x2, [x0, #7]
    // 0x80bb9c: r0 = BoxInt64Instr(r2)
    //     0x80bb9c: sbfiz           x0, x2, #1, #0x1f
    //     0x80bba0: cmp             x2, x0, asr #1
    //     0x80bba4: b.eq            #0x80bbb0
    //     0x80bba8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80bbac: stur            x2, [x0, #7]
    // 0x80bbb0: r1 = 60
    //     0x80bbb0: movz            x1, #0x3c
    // 0x80bbb4: branchIfSmi(r0, 0x80bbc0)
    //     0x80bbb4: tbz             w0, #0, #0x80bbc0
    // 0x80bbb8: r1 = LoadClassIdInstr(r0)
    //     0x80bbb8: ldur            x1, [x0, #-1]
    //     0x80bbbc: ubfx            x1, x1, #0xc, #0x14
    // 0x80bbc0: str             x0, [SP]
    // 0x80bbc4: mov             x0, x1
    // 0x80bbc8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x80bbc8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x80bbcc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x80bbcc: movz            x17, #0x2b03
    //     0x80bbd0: add             lr, x0, x17
    //     0x80bbd4: ldr             lr, [x21, lr, lsl #3]
    //     0x80bbd8: blr             lr
    // 0x80bbdc: r1 = Function '<anonymous closure>':.
    //     0x80bbdc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37c20] AnonymousClosure: (0x80c22c), in [package:nuonline/app/modules/article/article_author/bindings/article_author_binding.dart] ArticleAuthorBinding::dependencies (0x80baf8)
    //     0x80bbe0: ldr             x1, [x1, #0xc20]
    // 0x80bbe4: r2 = Null
    //     0x80bbe4: mov             x2, NULL
    // 0x80bbe8: stur            x0, [fp, #-8]
    // 0x80bbec: r0 = AllocateClosure()
    //     0x80bbec: bl              #0xec1630  ; AllocateClosureStub
    // 0x80bbf0: r16 = <ArticleAuthorController>
    //     0x80bbf0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d38] TypeArguments: <ArticleAuthorController>
    //     0x80bbf4: ldr             x16, [x16, #0xd38]
    // 0x80bbf8: stp             x0, x16, [SP, #8]
    // 0x80bbfc: ldur            x16, [fp, #-8]
    // 0x80bc00: str             x16, [SP]
    // 0x80bc04: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x80bc04: add             x4, PP, #0xb, lsl #12  ; [pp+0xb630] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x80bc08: ldr             x4, [x4, #0x630]
    // 0x80bc0c: r0 = Inst.lazyPut()
    //     0x80bc0c: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80bc10: r0 = Null
    //     0x80bc10: mov             x0, NULL
    // 0x80bc14: LeaveFrame
    //     0x80bc14: mov             SP, fp
    //     0x80bc18: ldp             fp, lr, [SP], #0x10
    // 0x80bc1c: ret
    //     0x80bc1c: ret             
    // 0x80bc20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80bc20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80bc24: b               #0x80bb10
  }
  [closure] ArticleAuthorController <anonymous closure>(dynamic) {
    // ** addr: 0x80c22c, size: 0x80
    // 0x80c22c: EnterFrame
    //     0x80c22c: stp             fp, lr, [SP, #-0x10]!
    //     0x80c230: mov             fp, SP
    // 0x80c234: AllocStack(0x18)
    //     0x80c234: sub             SP, SP, #0x18
    // 0x80c238: CheckStackOverflow
    //     0x80c238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80c23c: cmp             SP, x16
    //     0x80c240: b.ls            #0x80c2a4
    // 0x80c244: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80c244: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80c248: ldr             x0, [x0, #0x2670]
    //     0x80c24c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80c250: cmp             w0, w16
    //     0x80c254: b.ne            #0x80c260
    //     0x80c258: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80c25c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80c260: r16 = <ArticleRepository>
    //     0x80c260: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80c264: ldr             x16, [x16, #0x98]
    // 0x80c268: r30 = "article_repo"
    //     0x80c268: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80c26c: ldr             lr, [lr, #0xa0]
    // 0x80c270: stp             lr, x16, [SP]
    // 0x80c274: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80c274: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80c278: r0 = Inst.find()
    //     0x80c278: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80c27c: stur            x0, [fp, #-8]
    // 0x80c280: r0 = ArticleAuthorController()
    //     0x80c280: bl              #0x80cca0  ; AllocateArticleAuthorControllerStub -> ArticleAuthorController (size=0x40)
    // 0x80c284: mov             x1, x0
    // 0x80c288: ldur            x2, [fp, #-8]
    // 0x80c28c: stur            x0, [fp, #-8]
    // 0x80c290: r0 = ArticleAuthorController()
    //     0x80c290: bl              #0x80c2ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::ArticleAuthorController
    // 0x80c294: ldur            x0, [fp, #-8]
    // 0x80c298: LeaveFrame
    //     0x80c298: mov             SP, fp
    //     0x80c29c: ldp             fp, lr, [SP], #0x10
    // 0x80c2a0: ret
    //     0x80c2a0: ret             
    // 0x80c2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80c2a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80c2a8: b               #0x80c244
  }
}
