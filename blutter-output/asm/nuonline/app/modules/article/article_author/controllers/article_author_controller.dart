// lib: , url: package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart

// class id: 1050115, size: 0x8
class :: {
}

// class id: 2043, size: 0x38, field offset: 0x20
//   transformed mixin,
abstract class _ArticleAuthorController&GetxController&PagingMixin extends GetxController
     with PagingMixin<X0> {

  get _ hasNextPage(/* No info */) {
    // ** addr: 0x6fb9ec, size: 0x38
    // 0x6fb9ec: EnterFrame
    //     0x6fb9ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb9f0: mov             fp, SP
    // 0x6fb9f4: CheckStackOverflow
    //     0x6fb9f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fb9f8: cmp             SP, x16
    //     0x6fb9fc: b.ls            #0x6fba1c
    // 0x6fba00: LoadField: r0 = r1->field_23
    //     0x6fba00: ldur            w0, [x1, #0x23]
    // 0x6fba04: DecompressPointer r0
    //     0x6fba04: add             x0, x0, HEAP, lsl #32
    // 0x6fba08: mov             x1, x0
    // 0x6fba0c: r0 = value()
    //     0x6fba0c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x6fba10: LeaveFrame
    //     0x6fba10: mov             SP, fp
    //     0x6fba14: ldp             fp, lr, [SP], #0x10
    // 0x6fba18: ret
    //     0x6fba18: ret             
    // 0x6fba1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fba1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fba20: b               #0x6fba00
  }
  get _ page(/* No info */) {
    // ** addr: 0x72b460, size: 0x48
    // 0x72b460: EnterFrame
    //     0x72b460: stp             fp, lr, [SP, #-0x10]!
    //     0x72b464: mov             fp, SP
    // 0x72b468: CheckStackOverflow
    //     0x72b468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72b46c: cmp             SP, x16
    //     0x72b470: b.ls            #0x72b4a0
    // 0x72b474: LoadField: r0 = r1->field_1f
    //     0x72b474: ldur            w0, [x1, #0x1f]
    // 0x72b478: DecompressPointer r0
    //     0x72b478: add             x0, x0, HEAP, lsl #32
    // 0x72b47c: mov             x1, x0
    // 0x72b480: r0 = value()
    //     0x72b480: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x72b484: r1 = LoadInt32Instr(r0)
    //     0x72b484: sbfx            x1, x0, #1, #0x1f
    //     0x72b488: tbz             w0, #0, #0x72b490
    //     0x72b48c: ldur            x1, [x0, #7]
    // 0x72b490: mov             x0, x1
    // 0x72b494: LeaveFrame
    //     0x72b494: mov             SP, fp
    //     0x72b498: ldp             fp, lr, [SP], #0x10
    // 0x72b49c: ret
    //     0x72b49c: ret             
    // 0x72b4a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72b4a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72b4a4: b               #0x72b474
  }
  set _ hasError=(/* No info */) {
    // ** addr: 0x7f8b00, size: 0x40
    // 0x7f8b00: EnterFrame
    //     0x7f8b00: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8b04: mov             fp, SP
    // 0x7f8b08: CheckStackOverflow
    //     0x7f8b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8b0c: cmp             SP, x16
    //     0x7f8b10: b.ls            #0x7f8b38
    // 0x7f8b14: LoadField: r0 = r1->field_2b
    //     0x7f8b14: ldur            w0, [x1, #0x2b]
    // 0x7f8b18: DecompressPointer r0
    //     0x7f8b18: add             x0, x0, HEAP, lsl #32
    // 0x7f8b1c: mov             x1, x0
    // 0x7f8b20: r2 = true
    //     0x7f8b20: add             x2, NULL, #0x20  ; true
    // 0x7f8b24: r0 = value=()
    //     0x7f8b24: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8b28: r0 = true
    //     0x7f8b28: add             x0, NULL, #0x20  ; true
    // 0x7f8b2c: LeaveFrame
    //     0x7f8b2c: mov             SP, fp
    //     0x7f8b30: ldp             fp, lr, [SP], #0x10
    // 0x7f8b34: ret
    //     0x7f8b34: ret             
    // 0x7f8b38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8b38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8b3c: b               #0x7f8b14
  }
  _ appendToPage(/* No info */) {
    // ** addr: 0x7f8b40, size: 0x118
    // 0x7f8b40: EnterFrame
    //     0x7f8b40: stp             fp, lr, [SP, #-0x10]!
    //     0x7f8b44: mov             fp, SP
    // 0x7f8b48: AllocStack(0x20)
    //     0x7f8b48: sub             SP, SP, #0x20
    // 0x7f8b4c: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x7f8b4c: mov             x4, x1
    //     0x7f8b50: mov             x0, x3
    //     0x7f8b54: stur            x3, [fp, #-0x18]
    //     0x7f8b58: mov             x3, x2
    //     0x7f8b5c: stur            x1, [fp, #-8]
    //     0x7f8b60: stur            x2, [fp, #-0x10]
    // 0x7f8b64: CheckStackOverflow
    //     0x7f8b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f8b68: cmp             SP, x16
    //     0x7f8b6c: b.ls            #0x7f8c50
    // 0x7f8b70: LoadField: r1 = r4->field_27
    //     0x7f8b70: ldur            w1, [x4, #0x27]
    // 0x7f8b74: DecompressPointer r1
    //     0x7f8b74: add             x1, x1, HEAP, lsl #32
    // 0x7f8b78: r2 = false
    //     0x7f8b78: add             x2, NULL, #0x30  ; false
    // 0x7f8b7c: r0 = value=()
    //     0x7f8b7c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8b80: ldur            x0, [fp, #-8]
    // 0x7f8b84: LoadField: r1 = r0->field_23
    //     0x7f8b84: ldur            w1, [x0, #0x23]
    // 0x7f8b88: DecompressPointer r1
    //     0x7f8b88: add             x1, x1, HEAP, lsl #32
    // 0x7f8b8c: ldur            x2, [fp, #-0x18]
    // 0x7f8b90: r0 = value=()
    //     0x7f8b90: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8b94: ldur            x0, [fp, #-8]
    // 0x7f8b98: LoadField: r2 = r0->field_1f
    //     0x7f8b98: ldur            w2, [x0, #0x1f]
    // 0x7f8b9c: DecompressPointer r2
    //     0x7f8b9c: add             x2, x2, HEAP, lsl #32
    // 0x7f8ba0: mov             x1, x2
    // 0x7f8ba4: stur            x2, [fp, #-0x20]
    // 0x7f8ba8: r0 = value()
    //     0x7f8ba8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7f8bac: cmp             w0, #2
    // 0x7f8bb0: b.ne            #0x7f8bec
    // 0x7f8bb4: ldur            x2, [fp, #-0x10]
    // 0x7f8bb8: r0 = LoadClassIdInstr(r2)
    //     0x7f8bb8: ldur            x0, [x2, #-1]
    //     0x7f8bbc: ubfx            x0, x0, #0xc, #0x14
    // 0x7f8bc0: mov             x1, x2
    // 0x7f8bc4: r0 = GDT[cid_x0 + 0xe879]()
    //     0x7f8bc4: movz            x17, #0xe879
    //     0x7f8bc8: add             lr, x0, x17
    //     0x7f8bcc: ldr             lr, [x21, lr, lsl #3]
    //     0x7f8bd0: blr             lr
    // 0x7f8bd4: tbnz            w0, #4, #0x7f8bec
    // 0x7f8bd8: ldur            x0, [fp, #-8]
    // 0x7f8bdc: LoadField: r1 = r0->field_33
    //     0x7f8bdc: ldur            w1, [x0, #0x33]
    // 0x7f8be0: DecompressPointer r1
    //     0x7f8be0: add             x1, x1, HEAP, lsl #32
    // 0x7f8be4: r2 = true
    //     0x7f8be4: add             x2, NULL, #0x20  ; true
    // 0x7f8be8: r0 = value=()
    //     0x7f8be8: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8bec: ldur            x0, [fp, #-0x18]
    // 0x7f8bf0: tbnz            w0, #4, #0x7f8c2c
    // 0x7f8bf4: ldur            x1, [fp, #-0x20]
    // 0x7f8bf8: r0 = value()
    //     0x7f8bf8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x7f8bfc: r1 = LoadInt32Instr(r0)
    //     0x7f8bfc: sbfx            x1, x0, #1, #0x1f
    //     0x7f8c00: tbz             w0, #0, #0x7f8c08
    //     0x7f8c04: ldur            x1, [x0, #7]
    // 0x7f8c08: add             x2, x1, #1
    // 0x7f8c0c: r0 = BoxInt64Instr(r2)
    //     0x7f8c0c: sbfiz           x0, x2, #1, #0x1f
    //     0x7f8c10: cmp             x2, x0, asr #1
    //     0x7f8c14: b.eq            #0x7f8c20
    //     0x7f8c18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7f8c1c: stur            x2, [x0, #7]
    // 0x7f8c20: ldur            x1, [fp, #-0x20]
    // 0x7f8c24: mov             x2, x0
    // 0x7f8c28: r0 = value=()
    //     0x7f8c28: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x7f8c2c: ldur            x0, [fp, #-8]
    // 0x7f8c30: LoadField: r1 = r0->field_2f
    //     0x7f8c30: ldur            w1, [x0, #0x2f]
    // 0x7f8c34: DecompressPointer r1
    //     0x7f8c34: add             x1, x1, HEAP, lsl #32
    // 0x7f8c38: ldur            x2, [fp, #-0x10]
    // 0x7f8c3c: r0 = addAll()
    //     0x7f8c3c: bl              #0x667efc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::addAll
    // 0x7f8c40: r0 = Null
    //     0x7f8c40: mov             x0, NULL
    // 0x7f8c44: LeaveFrame
    //     0x7f8c44: mov             SP, fp
    //     0x7f8c48: ldp             fp, lr, [SP], #0x10
    // 0x7f8c4c: ret
    //     0x7f8c4c: ret             
    // 0x7f8c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f8c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f8c54: b               #0x7f8b70
  }
  _ _ArticleAuthorController&GetxController&PagingMixin(/* No info */) {
    // ** addr: 0x80c3d4, size: 0x140
    // 0x80c3d4: EnterFrame
    //     0x80c3d4: stp             fp, lr, [SP, #-0x10]!
    //     0x80c3d8: mov             fp, SP
    // 0x80c3dc: AllocStack(0x18)
    //     0x80c3dc: sub             SP, SP, #0x18
    // 0x80c3e0: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0x80c3e0: mov             x0, x1
    //     0x80c3e4: stur            x1, [fp, #-8]
    // 0x80c3e8: CheckStackOverflow
    //     0x80c3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80c3ec: cmp             SP, x16
    //     0x80c3f0: b.ls            #0x80c50c
    // 0x80c3f4: r1 = 1
    //     0x80c3f4: movz            x1, #0x1
    // 0x80c3f8: r0 = IntExtension.obs()
    //     0x80c3f8: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80c3fc: ldur            x2, [fp, #-8]
    // 0x80c400: StoreField: r2->field_1f = r0
    //     0x80c400: stur            w0, [x2, #0x1f]
    //     0x80c404: ldurb           w16, [x2, #-1]
    //     0x80c408: ldurb           w17, [x0, #-1]
    //     0x80c40c: and             x16, x17, x16, lsr #2
    //     0x80c410: tst             x16, HEAP, lsr #32
    //     0x80c414: b.eq            #0x80c41c
    //     0x80c418: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80c41c: r1 = true
    //     0x80c41c: add             x1, NULL, #0x20  ; true
    // 0x80c420: r0 = BoolExtension.obs()
    //     0x80c420: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80c424: ldur            x2, [fp, #-8]
    // 0x80c428: StoreField: r2->field_23 = r0
    //     0x80c428: stur            w0, [x2, #0x23]
    //     0x80c42c: ldurb           w16, [x2, #-1]
    //     0x80c430: ldurb           w17, [x0, #-1]
    //     0x80c434: and             x16, x17, x16, lsr #2
    //     0x80c438: tst             x16, HEAP, lsr #32
    //     0x80c43c: b.eq            #0x80c444
    //     0x80c440: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80c444: r1 = false
    //     0x80c444: add             x1, NULL, #0x30  ; false
    // 0x80c448: r0 = BoolExtension.obs()
    //     0x80c448: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80c44c: ldur            x2, [fp, #-8]
    // 0x80c450: StoreField: r2->field_27 = r0
    //     0x80c450: stur            w0, [x2, #0x27]
    //     0x80c454: ldurb           w16, [x2, #-1]
    //     0x80c458: ldurb           w17, [x0, #-1]
    //     0x80c45c: and             x16, x17, x16, lsr #2
    //     0x80c460: tst             x16, HEAP, lsr #32
    //     0x80c464: b.eq            #0x80c46c
    //     0x80c468: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80c46c: r1 = false
    //     0x80c46c: add             x1, NULL, #0x30  ; false
    // 0x80c470: r0 = BoolExtension.obs()
    //     0x80c470: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80c474: ldur            x3, [fp, #-8]
    // 0x80c478: StoreField: r3->field_2b = r0
    //     0x80c478: stur            w0, [x3, #0x2b]
    //     0x80c47c: ldurb           w16, [x3, #-1]
    //     0x80c480: ldurb           w17, [x0, #-1]
    //     0x80c484: and             x16, x17, x16, lsr #2
    //     0x80c488: tst             x16, HEAP, lsr #32
    //     0x80c48c: b.eq            #0x80c494
    //     0x80c490: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80c494: r1 = <Article>
    //     0x80c494: ldr             x1, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0x80c498: r2 = 0
    //     0x80c498: movz            x2, #0
    // 0x80c49c: r0 = _GrowableList()
    //     0x80c49c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80c4a0: r16 = <Article>
    //     0x80c4a0: ldr             x16, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0x80c4a4: stp             x0, x16, [SP]
    // 0x80c4a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80c4a8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80c4ac: r0 = ListExtension.obs()
    //     0x80c4ac: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80c4b0: ldur            x2, [fp, #-8]
    // 0x80c4b4: StoreField: r2->field_2f = r0
    //     0x80c4b4: stur            w0, [x2, #0x2f]
    //     0x80c4b8: ldurb           w16, [x2, #-1]
    //     0x80c4bc: ldurb           w17, [x0, #-1]
    //     0x80c4c0: and             x16, x17, x16, lsr #2
    //     0x80c4c4: tst             x16, HEAP, lsr #32
    //     0x80c4c8: b.eq            #0x80c4d0
    //     0x80c4cc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80c4d0: r1 = false
    //     0x80c4d0: add             x1, NULL, #0x30  ; false
    // 0x80c4d4: r0 = BoolExtension.obs()
    //     0x80c4d4: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80c4d8: ldur            x1, [fp, #-8]
    // 0x80c4dc: StoreField: r1->field_33 = r0
    //     0x80c4dc: stur            w0, [x1, #0x33]
    //     0x80c4e0: ldurb           w16, [x1, #-1]
    //     0x80c4e4: ldurb           w17, [x0, #-1]
    //     0x80c4e8: and             x16, x17, x16, lsr #2
    //     0x80c4ec: tst             x16, HEAP, lsr #32
    //     0x80c4f0: b.eq            #0x80c4f8
    //     0x80c4f4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80c4f8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80c4f8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80c4fc: r0 = Null
    //     0x80c4fc: mov             x0, NULL
    // 0x80c500: LeaveFrame
    //     0x80c500: mov             SP, fp
    //     0x80c504: ldp             fp, lr, [SP], #0x10
    // 0x80c508: ret
    //     0x80c508: ret             
    // 0x80c50c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80c50c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80c510: b               #0x80c3f4
  }
  get _ itemsCount(/* No info */) {
    // ** addr: 0xad18ac, size: 0xe4
    // 0xad18ac: EnterFrame
    //     0xad18ac: stp             fp, lr, [SP, #-0x10]!
    //     0xad18b0: mov             fp, SP
    // 0xad18b4: AllocStack(0x18)
    //     0xad18b4: sub             SP, SP, #0x18
    // 0xad18b8: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r0, fp-0x10 */)
    //     0xad18b8: mov             x0, x1
    //     0xad18bc: stur            x1, [fp, #-0x10]
    // 0xad18c0: CheckStackOverflow
    //     0xad18c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad18c4: cmp             SP, x16
    //     0xad18c8: b.ls            #0xad1988
    // 0xad18cc: LoadField: r2 = r0->field_2f
    //     0xad18cc: ldur            w2, [x0, #0x2f]
    // 0xad18d0: DecompressPointer r2
    //     0xad18d0: add             x2, x2, HEAP, lsl #32
    // 0xad18d4: mov             x1, x2
    // 0xad18d8: stur            x2, [fp, #-8]
    // 0xad18dc: r0 = value()
    //     0xad18dc: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad18e0: r1 = LoadClassIdInstr(r0)
    //     0xad18e0: ldur            x1, [x0, #-1]
    //     0xad18e4: ubfx            x1, x1, #0xc, #0x14
    // 0xad18e8: str             x0, [SP]
    // 0xad18ec: mov             x0, x1
    // 0xad18f0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad18f0: movz            x17, #0xc834
    //     0xad18f4: add             lr, x0, x17
    //     0xad18f8: ldr             lr, [x21, lr, lsl #3]
    //     0xad18fc: blr             lr
    // 0xad1900: cbnz            w0, #0xad190c
    // 0xad1904: r0 = 8
    //     0xad1904: movz            x0, #0x8
    // 0xad1908: b               #0xad197c
    // 0xad190c: ldur            x0, [fp, #-0x10]
    // 0xad1910: ldur            x1, [fp, #-8]
    // 0xad1914: r0 = value()
    //     0xad1914: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad1918: r1 = LoadClassIdInstr(r0)
    //     0xad1918: ldur            x1, [x0, #-1]
    //     0xad191c: ubfx            x1, x1, #0xc, #0x14
    // 0xad1920: str             x0, [SP]
    // 0xad1924: mov             x0, x1
    // 0xad1928: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad1928: movz            x17, #0xc834
    //     0xad192c: add             lr, x0, x17
    //     0xad1930: ldr             lr, [x21, lr, lsl #3]
    //     0xad1934: blr             lr
    // 0xad1938: mov             x2, x0
    // 0xad193c: ldur            x0, [fp, #-0x10]
    // 0xad1940: stur            x2, [fp, #-8]
    // 0xad1944: LoadField: r1 = r0->field_23
    //     0xad1944: ldur            w1, [x0, #0x23]
    // 0xad1948: DecompressPointer r1
    //     0xad1948: add             x1, x1, HEAP, lsl #32
    // 0xad194c: r0 = value()
    //     0xad194c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad1950: tst             x0, #0x10
    // 0xad1954: cset            x1, ne
    // 0xad1958: sub             x1, x1, #1
    // 0xad195c: and             x1, x1, #6
    // 0xad1960: ldur            x2, [fp, #-8]
    // 0xad1964: r3 = LoadInt32Instr(r2)
    //     0xad1964: sbfx            x3, x2, #1, #0x1f
    //     0xad1968: tbz             w2, #0, #0xad1970
    //     0xad196c: ldur            x3, [x2, #7]
    // 0xad1970: r2 = LoadInt32Instr(r1)
    //     0xad1970: sbfx            x2, x1, #1, #0x1f
    // 0xad1974: add             x1, x3, x2
    // 0xad1978: mov             x0, x1
    // 0xad197c: LeaveFrame
    //     0xad197c: mov             SP, fp
    //     0xad1980: ldp             fp, lr, [SP], #0x10
    // 0xad1984: ret
    //     0xad1984: ret             
    // 0xad1988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad198c: b               #0xad18cc
  }
  get _ hasError(/* No info */) {
    // ** addr: 0xad1aa0, size: 0x38
    // 0xad1aa0: EnterFrame
    //     0xad1aa0: stp             fp, lr, [SP, #-0x10]!
    //     0xad1aa4: mov             fp, SP
    // 0xad1aa8: CheckStackOverflow
    //     0xad1aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1aac: cmp             SP, x16
    //     0xad1ab0: b.ls            #0xad1ad0
    // 0xad1ab4: LoadField: r0 = r1->field_2b
    //     0xad1ab4: ldur            w0, [x1, #0x2b]
    // 0xad1ab8: DecompressPointer r0
    //     0xad1ab8: add             x0, x0, HEAP, lsl #32
    // 0xad1abc: mov             x1, x0
    // 0xad1ac0: r0 = value()
    //     0xad1ac0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad1ac4: LeaveFrame
    //     0xad1ac4: mov             SP, fp
    //     0xad1ac8: ldp             fp, lr, [SP], #0x10
    // 0xad1acc: ret
    //     0xad1acc: ret             
    // 0xad1ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1ad4: b               #0xad1ab4
  }
  _ onPageRefresh(/* No info */) async {
    // ** addr: 0xad1fcc, size: 0xc8
    // 0xad1fcc: EnterFrame
    //     0xad1fcc: stp             fp, lr, [SP, #-0x10]!
    //     0xad1fd0: mov             fp, SP
    // 0xad1fd4: AllocStack(0x18)
    //     0xad1fd4: sub             SP, SP, #0x18
    // 0xad1fd8: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r1, fp-0x10 */)
    //     0xad1fd8: stur            NULL, [fp, #-8]
    //     0xad1fdc: stur            x1, [fp, #-0x10]
    // 0xad1fe0: CheckStackOverflow
    //     0xad1fe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1fe4: cmp             SP, x16
    //     0xad1fe8: b.ls            #0xad208c
    // 0xad1fec: InitAsync() -> Future<void?>
    //     0xad1fec: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad1ff0: bl              #0x661298  ; InitAsyncStub
    // 0xad1ff4: ldur            x0, [fp, #-0x10]
    // 0xad1ff8: LoadField: r3 = r0->field_2f
    //     0xad1ff8: ldur            w3, [x0, #0x2f]
    // 0xad1ffc: DecompressPointer r3
    //     0xad1ffc: add             x3, x3, HEAP, lsl #32
    // 0xad2000: stur            x3, [fp, #-0x18]
    // 0xad2004: r1 = <Article>
    //     0xad2004: ldr             x1, [PP, #0x7b78]  ; [pp+0x7b78] TypeArguments: <Article>
    // 0xad2008: r2 = 0
    //     0xad2008: movz            x2, #0
    // 0xad200c: r0 = _GrowableList()
    //     0xad200c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xad2010: ldur            x1, [fp, #-0x18]
    // 0xad2014: mov             x2, x0
    // 0xad2018: r0 = value=()
    //     0xad2018: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0xad201c: ldur            x0, [fp, #-0x10]
    // 0xad2020: LoadField: r1 = r0->field_1f
    //     0xad2020: ldur            w1, [x0, #0x1f]
    // 0xad2024: DecompressPointer r1
    //     0xad2024: add             x1, x1, HEAP, lsl #32
    // 0xad2028: r2 = 2
    //     0xad2028: movz            x2, #0x2
    // 0xad202c: r0 = value=()
    //     0xad202c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad2030: ldur            x0, [fp, #-0x10]
    // 0xad2034: LoadField: r1 = r0->field_23
    //     0xad2034: ldur            w1, [x0, #0x23]
    // 0xad2038: DecompressPointer r1
    //     0xad2038: add             x1, x1, HEAP, lsl #32
    // 0xad203c: r2 = false
    //     0xad203c: add             x2, NULL, #0x30  ; false
    // 0xad2040: r0 = value=()
    //     0xad2040: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad2044: ldur            x0, [fp, #-0x10]
    // 0xad2048: LoadField: r1 = r0->field_27
    //     0xad2048: ldur            w1, [x0, #0x27]
    // 0xad204c: DecompressPointer r1
    //     0xad204c: add             x1, x1, HEAP, lsl #32
    // 0xad2050: r2 = false
    //     0xad2050: add             x2, NULL, #0x30  ; false
    // 0xad2054: r0 = value=()
    //     0xad2054: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad2058: ldur            x1, [fp, #-0x10]
    // 0xad205c: r0 = page()
    //     0xad205c: bl              #0x72b460  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::page
    // 0xad2060: ldur            x1, [fp, #-0x10]
    // 0xad2064: r2 = LoadClassIdInstr(r1)
    //     0xad2064: ldur            x2, [x1, #-1]
    //     0xad2068: ubfx            x2, x2, #0xc, #0x14
    // 0xad206c: mov             x16, x0
    // 0xad2070: mov             x0, x2
    // 0xad2074: mov             x2, x16
    // 0xad2078: r0 = GDT[cid_x0 + -0xe78]()
    //     0xad2078: sub             lr, x0, #0xe78
    //     0xad207c: ldr             lr, [x21, lr, lsl #3]
    //     0xad2080: blr             lr
    // 0xad2084: r0 = Null
    //     0xad2084: mov             x0, NULL
    // 0xad2088: r0 = ReturnAsyncNotFuture()
    //     0xad2088: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad208c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad208c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2090: b               #0xad1fec
  }
  [closure] Future<void> onPageRefresh(dynamic) {
    // ** addr: 0xad2094, size: 0x38
    // 0xad2094: EnterFrame
    //     0xad2094: stp             fp, lr, [SP, #-0x10]!
    //     0xad2098: mov             fp, SP
    // 0xad209c: ldr             x0, [fp, #0x10]
    // 0xad20a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad20a0: ldur            w1, [x0, #0x17]
    // 0xad20a4: DecompressPointer r1
    //     0xad20a4: add             x1, x1, HEAP, lsl #32
    // 0xad20a8: CheckStackOverflow
    //     0xad20a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad20ac: cmp             SP, x16
    //     0xad20b0: b.ls            #0xad20c4
    // 0xad20b4: r0 = onPageRefresh()
    //     0xad20b4: bl              #0xad1fcc  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh
    // 0xad20b8: LeaveFrame
    //     0xad20b8: mov             SP, fp
    //     0xad20bc: ldp             fp, lr, [SP], #0x10
    // 0xad20c0: ret
    //     0xad20c0: ret             
    // 0xad20c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad20c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad20c8: b               #0xad20b4
  }
  _ find(/* No info */) {
    // ** addr: 0xad2f38, size: 0x120
    // 0xad2f38: EnterFrame
    //     0xad2f38: stp             fp, lr, [SP, #-0x10]!
    //     0xad2f3c: mov             fp, SP
    // 0xad2f40: AllocStack(0x68)
    //     0xad2f40: sub             SP, SP, #0x68
    // 0xad2f44: SetupParameters(dynamic _ /* r2 => r2, fp-0x58 */)
    //     0xad2f44: stur            x2, [fp, #-0x58]
    // 0xad2f48: CheckStackOverflow
    //     0xad2f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2f4c: cmp             SP, x16
    //     0xad2f50: b.ls            #0xad3050
    // 0xad2f54: LoadField: r0 = r1->field_2f
    //     0xad2f54: ldur            w0, [x1, #0x2f]
    // 0xad2f58: DecompressPointer r0
    //     0xad2f58: add             x0, x0, HEAP, lsl #32
    // 0xad2f5c: mov             x1, x0
    // 0xad2f60: stur            x0, [fp, #-0x50]
    // 0xad2f64: r0 = value()
    //     0xad2f64: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad2f68: r1 = LoadClassIdInstr(r0)
    //     0xad2f68: ldur            x1, [x0, #-1]
    //     0xad2f6c: ubfx            x1, x1, #0xc, #0x14
    // 0xad2f70: str             x0, [SP]
    // 0xad2f74: mov             x0, x1
    // 0xad2f78: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad2f78: movz            x17, #0xc834
    //     0xad2f7c: add             lr, x0, x17
    //     0xad2f80: ldr             lr, [x21, lr, lsl #3]
    //     0xad2f84: blr             lr
    // 0xad2f88: cbz             w0, #0xad302c
    // 0xad2f8c: ldur            x0, [fp, #-0x58]
    // 0xad2f90: ldur            x1, [fp, #-0x50]
    // 0xad2f94: r0 = value()
    //     0xad2f94: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad2f98: r1 = LoadClassIdInstr(r0)
    //     0xad2f98: ldur            x1, [x0, #-1]
    //     0xad2f9c: ubfx            x1, x1, #0xc, #0x14
    // 0xad2fa0: str             x0, [SP]
    // 0xad2fa4: mov             x0, x1
    // 0xad2fa8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad2fa8: movz            x17, #0xc834
    //     0xad2fac: add             lr, x0, x17
    //     0xad2fb0: ldr             lr, [x21, lr, lsl #3]
    //     0xad2fb4: blr             lr
    // 0xad2fb8: r1 = LoadInt32Instr(r0)
    //     0xad2fb8: sbfx            x1, x0, #1, #0x1f
    //     0xad2fbc: tbz             w0, #0, #0xad2fc4
    //     0xad2fc0: ldur            x1, [x0, #7]
    // 0xad2fc4: ldur            x0, [fp, #-0x58]
    // 0xad2fc8: cmp             x1, x0
    // 0xad2fcc: b.le            #0xad301c
    // 0xad2fd0: ldur            x1, [fp, #-0x50]
    // 0xad2fd4: r0 = value()
    //     0xad2fd4: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad2fd8: mov             x3, x0
    // 0xad2fdc: ldur            x2, [fp, #-0x58]
    // 0xad2fe0: r0 = BoxInt64Instr(r2)
    //     0xad2fe0: sbfiz           x0, x2, #1, #0x1f
    //     0xad2fe4: cmp             x2, x0, asr #1
    //     0xad2fe8: b.eq            #0xad2ff4
    //     0xad2fec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad2ff0: stur            x2, [x0, #7]
    // 0xad2ff4: r1 = LoadClassIdInstr(r3)
    //     0xad2ff4: ldur            x1, [x3, #-1]
    //     0xad2ff8: ubfx            x1, x1, #0xc, #0x14
    // 0xad2ffc: stp             x0, x3, [SP]
    // 0xad3000: mov             x0, x1
    // 0xad3004: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad3004: movz            x17, #0x3037
    //     0xad3008: movk            x17, #0x1, lsl #16
    //     0xad300c: add             lr, x0, x17
    //     0xad3010: ldr             lr, [x21, lr, lsl #3]
    //     0xad3014: blr             lr
    // 0xad3018: b               #0xad3020
    // 0xad301c: r0 = Null
    //     0xad301c: mov             x0, NULL
    // 0xad3020: LeaveFrame
    //     0xad3020: mov             SP, fp
    //     0xad3024: ldp             fp, lr, [SP], #0x10
    // 0xad3028: ret
    //     0xad3028: ret             
    // 0xad302c: r0 = Null
    //     0xad302c: mov             x0, NULL
    // 0xad3030: LeaveFrame
    //     0xad3030: mov             SP, fp
    //     0xad3034: ldp             fp, lr, [SP], #0x10
    // 0xad3038: ret
    //     0xad3038: ret             
    // 0xad303c: sub             SP, fp, #0x68
    // 0xad3040: r0 = Null
    //     0xad3040: mov             x0, NULL
    // 0xad3044: LeaveFrame
    //     0xad3044: mov             SP, fp
    //     0xad3048: ldp             fp, lr, [SP], #0x10
    // 0xad304c: ret
    //     0xad304c: ret             
    // 0xad3050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3054: b               #0xad2f54
  }
  [closure] bool onPageScrolled(dynamic, ScrollNotification) {
    // ** addr: 0xad3058, size: 0x3c
    // 0xad3058: EnterFrame
    //     0xad3058: stp             fp, lr, [SP, #-0x10]!
    //     0xad305c: mov             fp, SP
    // 0xad3060: ldr             x0, [fp, #0x18]
    // 0xad3064: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad3064: ldur            w1, [x0, #0x17]
    // 0xad3068: DecompressPointer r1
    //     0xad3068: add             x1, x1, HEAP, lsl #32
    // 0xad306c: CheckStackOverflow
    //     0xad306c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3070: cmp             SP, x16
    //     0xad3074: b.ls            #0xad308c
    // 0xad3078: ldr             x2, [fp, #0x10]
    // 0xad307c: r0 = onPageScrolled()
    //     0xad307c: bl              #0xad3094  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled
    // 0xad3080: LeaveFrame
    //     0xad3080: mov             SP, fp
    //     0xad3084: ldp             fp, lr, [SP], #0x10
    // 0xad3088: ret
    //     0xad3088: ret             
    // 0xad308c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad308c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3090: b               #0xad3078
  }
  _ onPageScrolled(/* No info */) {
    // ** addr: 0xad3094, size: 0xdc
    // 0xad3094: EnterFrame
    //     0xad3094: stp             fp, lr, [SP, #-0x10]!
    //     0xad3098: mov             fp, SP
    // 0xad309c: AllocStack(0x10)
    //     0xad309c: sub             SP, SP, #0x10
    // 0xad30a0: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xad30a0: mov             x0, x1
    //     0xad30a4: stur            x1, [fp, #-8]
    //     0xad30a8: stur            x2, [fp, #-0x10]
    // 0xad30ac: CheckStackOverflow
    //     0xad30ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad30b0: cmp             SP, x16
    //     0xad30b4: b.ls            #0xad3160
    // 0xad30b8: mov             x1, x0
    // 0xad30bc: r0 = hasNextPage()
    //     0xad30bc: bl              #0x6fb9ec  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasNextPage
    // 0xad30c0: tbz             w0, #4, #0xad30d4
    // 0xad30c4: r0 = true
    //     0xad30c4: add             x0, NULL, #0x20  ; true
    // 0xad30c8: LeaveFrame
    //     0xad30c8: mov             SP, fp
    //     0xad30cc: ldp             fp, lr, [SP], #0x10
    // 0xad30d0: ret
    //     0xad30d0: ret             
    // 0xad30d4: ldur            x0, [fp, #-0x10]
    // 0xad30d8: LoadField: r1 = r0->field_f
    //     0xad30d8: ldur            w1, [x0, #0xf]
    // 0xad30dc: DecompressPointer r1
    //     0xad30dc: add             x1, x1, HEAP, lsl #32
    // 0xad30e0: LoadField: r0 = r1->field_f
    //     0xad30e0: ldur            w0, [x1, #0xf]
    // 0xad30e4: DecompressPointer r0
    //     0xad30e4: add             x0, x0, HEAP, lsl #32
    // 0xad30e8: cmp             w0, NULL
    // 0xad30ec: b.eq            #0xad3168
    // 0xad30f0: LoadField: r2 = r1->field_b
    //     0xad30f0: ldur            w2, [x1, #0xb]
    // 0xad30f4: DecompressPointer r2
    //     0xad30f4: add             x2, x2, HEAP, lsl #32
    // 0xad30f8: cmp             w2, NULL
    // 0xad30fc: b.eq            #0xad316c
    // 0xad3100: LoadField: d0 = r0->field_7
    //     0xad3100: ldur            d0, [x0, #7]
    // 0xad3104: LoadField: d1 = r2->field_7
    //     0xad3104: ldur            d1, [x2, #7]
    // 0xad3108: fcmp            d0, d1
    // 0xad310c: b.ne            #0xad3150
    // 0xad3110: ldur            x1, [fp, #-8]
    // 0xad3114: r0 = isFetching()
    //     0xad3114: bl              #0xad3170  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::isFetching
    // 0xad3118: tbz             w0, #4, #0xad3150
    // 0xad311c: ldur            x0, [fp, #-8]
    // 0xad3120: LoadField: r1 = r0->field_1f
    //     0xad3120: ldur            w1, [x0, #0x1f]
    // 0xad3124: DecompressPointer r1
    //     0xad3124: add             x1, x1, HEAP, lsl #32
    // 0xad3128: r0 = value()
    //     0xad3128: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad312c: r2 = LoadInt32Instr(r0)
    //     0xad312c: sbfx            x2, x0, #1, #0x1f
    //     0xad3130: tbz             w0, #0, #0xad3138
    //     0xad3134: ldur            x2, [x0, #7]
    // 0xad3138: ldur            x1, [fp, #-8]
    // 0xad313c: r0 = LoadClassIdInstr(r1)
    //     0xad313c: ldur            x0, [x1, #-1]
    //     0xad3140: ubfx            x0, x0, #0xc, #0x14
    // 0xad3144: r0 = GDT[cid_x0 + -0xe78]()
    //     0xad3144: sub             lr, x0, #0xe78
    //     0xad3148: ldr             lr, [x21, lr, lsl #3]
    //     0xad314c: blr             lr
    // 0xad3150: r0 = false
    //     0xad3150: add             x0, NULL, #0x30  ; false
    // 0xad3154: LeaveFrame
    //     0xad3154: mov             SP, fp
    //     0xad3158: ldp             fp, lr, [SP], #0x10
    // 0xad315c: ret
    //     0xad315c: ret             
    // 0xad3160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3164: b               #0xad30b8
    // 0xad3168: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad3168: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad316c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad316c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ isFetching(/* No info */) {
    // ** addr: 0xad3170, size: 0x38
    // 0xad3170: EnterFrame
    //     0xad3170: stp             fp, lr, [SP, #-0x10]!
    //     0xad3174: mov             fp, SP
    // 0xad3178: CheckStackOverflow
    //     0xad3178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad317c: cmp             SP, x16
    //     0xad3180: b.ls            #0xad31a0
    // 0xad3184: LoadField: r0 = r1->field_27
    //     0xad3184: ldur            w0, [x1, #0x27]
    // 0xad3188: DecompressPointer r0
    //     0xad3188: add             x0, x0, HEAP, lsl #32
    // 0xad318c: mov             x1, x0
    // 0xad3190: r0 = value()
    //     0xad3190: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad3194: LeaveFrame
    //     0xad3194: mov             SP, fp
    //     0xad3198: ldp             fp, lr, [SP], #0x10
    // 0xad319c: ret
    //     0xad319c: ret             
    // 0xad31a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad31a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad31a4: b               #0xad3184
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe32258, size: 0x5c
    // 0xe32258: EnterFrame
    //     0xe32258: stp             fp, lr, [SP, #-0x10]!
    //     0xe3225c: mov             fp, SP
    // 0xe32260: AllocStack(0x8)
    //     0xe32260: sub             SP, SP, #8
    // 0xe32264: SetupParameters(_ArticleAuthorController&GetxController&PagingMixin this /* r1 => r0, fp-0x8 */)
    //     0xe32264: mov             x0, x1
    //     0xe32268: stur            x1, [fp, #-8]
    // 0xe3226c: CheckStackOverflow
    //     0xe3226c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32270: cmp             SP, x16
    //     0xe32274: b.ls            #0xe322ac
    // 0xe32278: LoadField: r1 = r0->field_27
    //     0xe32278: ldur            w1, [x0, #0x27]
    // 0xe3227c: DecompressPointer r1
    //     0xe3227c: add             x1, x1, HEAP, lsl #32
    // 0xe32280: r2 = true
    //     0xe32280: add             x2, NULL, #0x20  ; true
    // 0xe32284: r0 = value=()
    //     0xe32284: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe32288: ldur            x0, [fp, #-8]
    // 0xe3228c: LoadField: r1 = r0->field_2b
    //     0xe3228c: ldur            w1, [x0, #0x2b]
    // 0xe32290: DecompressPointer r1
    //     0xe32290: add             x1, x1, HEAP, lsl #32
    // 0xe32294: r2 = false
    //     0xe32294: add             x2, NULL, #0x30  ; false
    // 0xe32298: r0 = value=()
    //     0xe32298: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe3229c: r0 = Null
    //     0xe3229c: mov             x0, NULL
    // 0xe322a0: LeaveFrame
    //     0xe322a0: mov             SP, fp
    //     0xe322a4: ldp             fp, lr, [SP], #0x10
    // 0xe322a8: ret
    //     0xe322a8: ret             
    // 0xe322ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe322ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe322b0: b               #0xe32278
  }
}

// class id: 2051, size: 0x40, field offset: 0x38
class ArticleAuthorController extends _ArticleAuthorController&GetxController&PagingMixin {

  _ ArticleAuthorController(/* No info */) {
    // ** addr: 0x80c2ac, size: 0x108
    // 0x80c2ac: EnterFrame
    //     0x80c2ac: stp             fp, lr, [SP, #-0x10]!
    //     0x80c2b0: mov             fp, SP
    // 0x80c2b4: AllocStack(0x28)
    //     0x80c2b4: sub             SP, SP, #0x28
    // 0x80c2b8: SetupParameters(ArticleAuthorController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x80c2b8: mov             x0, x2
    //     0x80c2bc: stur            x1, [fp, #-8]
    //     0x80c2c0: stur            x2, [fp, #-0x10]
    // 0x80c2c4: CheckStackOverflow
    //     0x80c2c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80c2c8: cmp             SP, x16
    //     0x80c2cc: b.ls            #0x80c3ac
    // 0x80c2d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80c2d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80c2d4: ldr             x0, [x0, #0x2670]
    //     0x80c2d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80c2dc: cmp             w0, w16
    //     0x80c2e0: b.ne            #0x80c2ec
    //     0x80c2e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80c2e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80c2ec: r0 = GetNavigation.arguments()
    //     0x80c2ec: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80c2f0: r16 = "author"
    //     0x80c2f0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x80c2f4: ldr             x16, [x16, #0xbf8]
    // 0x80c2f8: stp             x16, x0, [SP]
    // 0x80c2fc: r4 = 0
    //     0x80c2fc: movz            x4, #0
    // 0x80c300: ldr             x0, [SP, #8]
    // 0x80c304: r16 = UnlinkedCall_0x5f3c08
    //     0x80c304: add             x16, PP, #0x37, lsl #12  ; [pp+0x37c28] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80c308: add             x16, x16, #0xc28
    // 0x80c30c: ldp             x5, lr, [x16]
    // 0x80c310: blr             lr
    // 0x80c314: mov             x3, x0
    // 0x80c318: r2 = Null
    //     0x80c318: mov             x2, NULL
    // 0x80c31c: r1 = Null
    //     0x80c31c: mov             x1, NULL
    // 0x80c320: stur            x3, [fp, #-0x18]
    // 0x80c324: r4 = 60
    //     0x80c324: movz            x4, #0x3c
    // 0x80c328: branchIfSmi(r0, 0x80c334)
    //     0x80c328: tbz             w0, #0, #0x80c334
    // 0x80c32c: r4 = LoadClassIdInstr(r0)
    //     0x80c32c: ldur            x4, [x0, #-1]
    //     0x80c330: ubfx            x4, x4, #0xc, #0x14
    // 0x80c334: r17 = 5596
    //     0x80c334: movz            x17, #0x15dc
    // 0x80c338: cmp             x4, x17
    // 0x80c33c: b.eq            #0x80c354
    // 0x80c340: r8 = Author
    //     0x80c340: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b4b8] Type: Author
    //     0x80c344: ldr             x8, [x8, #0x4b8]
    // 0x80c348: r3 = Null
    //     0x80c348: add             x3, PP, #0x37, lsl #12  ; [pp+0x37c38] Null
    //     0x80c34c: ldr             x3, [x3, #0xc38]
    // 0x80c350: r0 = Author()
    //     0x80c350: bl              #0x80cd68  ; IsType_Author_Stub
    // 0x80c354: ldur            x0, [fp, #-0x18]
    // 0x80c358: ldur            x1, [fp, #-8]
    // 0x80c35c: StoreField: r1->field_3b = r0
    //     0x80c35c: stur            w0, [x1, #0x3b]
    //     0x80c360: ldurb           w16, [x1, #-1]
    //     0x80c364: ldurb           w17, [x0, #-1]
    //     0x80c368: and             x16, x17, x16, lsr #2
    //     0x80c36c: tst             x16, HEAP, lsr #32
    //     0x80c370: b.eq            #0x80c378
    //     0x80c374: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80c378: ldur            x0, [fp, #-0x10]
    // 0x80c37c: StoreField: r1->field_37 = r0
    //     0x80c37c: stur            w0, [x1, #0x37]
    //     0x80c380: ldurb           w16, [x1, #-1]
    //     0x80c384: ldurb           w17, [x0, #-1]
    //     0x80c388: and             x16, x17, x16, lsr #2
    //     0x80c38c: tst             x16, HEAP, lsr #32
    //     0x80c390: b.eq            #0x80c398
    //     0x80c394: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80c398: r0 = _ArticleAuthorController&GetxController&PagingMixin()
    //     0x80c398: bl              #0x80c3d4  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::_ArticleAuthorController&GetxController&PagingMixin
    // 0x80c39c: r0 = Null
    //     0x80c39c: mov             x0, NULL
    // 0x80c3a0: LeaveFrame
    //     0x80c3a0: mov             SP, fp
    //     0x80c3a4: ldp             fp, lr, [SP], #0x10
    // 0x80c3a8: ret
    //     0x80c3a8: ret             
    // 0x80c3ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80c3ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80c3b0: b               #0x80c2d0
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe32104, size: 0x154
    // 0xe32104: EnterFrame
    //     0xe32104: stp             fp, lr, [SP, #-0x10]!
    //     0xe32108: mov             fp, SP
    // 0xe3210c: AllocStack(0x48)
    //     0xe3210c: sub             SP, SP, #0x48
    // 0xe32110: SetupParameters(ArticleAuthorController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe32110: stur            x1, [fp, #-8]
    //     0xe32114: stur            x2, [fp, #-0x10]
    // 0xe32118: CheckStackOverflow
    //     0xe32118: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3211c: cmp             SP, x16
    //     0xe32120: b.ls            #0xe32250
    // 0xe32124: r1 = 1
    //     0xe32124: movz            x1, #0x1
    // 0xe32128: r0 = AllocateContext()
    //     0xe32128: bl              #0xec126c  ; AllocateContextStub
    // 0xe3212c: mov             x3, x0
    // 0xe32130: ldur            x0, [fp, #-8]
    // 0xe32134: stur            x3, [fp, #-0x28]
    // 0xe32138: StoreField: r3->field_f = r0
    //     0xe32138: stur            w0, [x3, #0xf]
    // 0xe3213c: LoadField: r4 = r0->field_37
    //     0xe3213c: ldur            w4, [x0, #0x37]
    // 0xe32140: DecompressPointer r4
    //     0xe32140: add             x4, x4, HEAP, lsl #32
    // 0xe32144: stur            x4, [fp, #-0x20]
    // 0xe32148: LoadField: r1 = r0->field_3b
    //     0xe32148: ldur            w1, [x0, #0x3b]
    // 0xe3214c: DecompressPointer r1
    //     0xe3214c: add             x1, x1, HEAP, lsl #32
    // 0xe32150: LoadField: r5 = r1->field_7
    //     0xe32150: ldur            x5, [x1, #7]
    // 0xe32154: stur            x5, [fp, #-0x18]
    // 0xe32158: r1 = Null
    //     0xe32158: mov             x1, NULL
    // 0xe3215c: r2 = 4
    //     0xe3215c: movz            x2, #0x4
    // 0xe32160: r0 = AllocateArray()
    //     0xe32160: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe32164: mov             x2, x0
    // 0xe32168: stur            x2, [fp, #-0x30]
    // 0xe3216c: r16 = "page"
    //     0xe3216c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe32170: ldr             x16, [x16, #0x300]
    // 0xe32174: StoreField: r2->field_f = r16
    //     0xe32174: stur            w16, [x2, #0xf]
    // 0xe32178: ldur            x3, [fp, #-0x10]
    // 0xe3217c: r0 = BoxInt64Instr(r3)
    //     0xe3217c: sbfiz           x0, x3, #1, #0x1f
    //     0xe32180: cmp             x3, x0, asr #1
    //     0xe32184: b.eq            #0xe32190
    //     0xe32188: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe3218c: stur            x3, [x0, #7]
    // 0xe32190: r1 = 60
    //     0xe32190: movz            x1, #0x3c
    // 0xe32194: branchIfSmi(r0, 0xe321a0)
    //     0xe32194: tbz             w0, #0, #0xe321a0
    // 0xe32198: r1 = LoadClassIdInstr(r0)
    //     0xe32198: ldur            x1, [x0, #-1]
    //     0xe3219c: ubfx            x1, x1, #0xc, #0x14
    // 0xe321a0: str             x0, [SP]
    // 0xe321a4: mov             x0, x1
    // 0xe321a8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe321a8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe321ac: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe321ac: movz            x17, #0x2b03
    //     0xe321b0: add             lr, x0, x17
    //     0xe321b4: ldr             lr, [x21, lr, lsl #3]
    //     0xe321b8: blr             lr
    // 0xe321bc: ldur            x1, [fp, #-0x30]
    // 0xe321c0: ArrayStore: r1[1] = r0  ; List_4
    //     0xe321c0: add             x25, x1, #0x13
    //     0xe321c4: str             w0, [x25]
    //     0xe321c8: tbz             w0, #0, #0xe321e4
    //     0xe321cc: ldurb           w16, [x1, #-1]
    //     0xe321d0: ldurb           w17, [x0, #-1]
    //     0xe321d4: and             x16, x17, x16, lsr #2
    //     0xe321d8: tst             x16, HEAP, lsr #32
    //     0xe321dc: b.eq            #0xe321e4
    //     0xe321e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe321e4: r16 = <String, String?>
    //     0xe321e4: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe321e8: ldr             x16, [x16, #0x198]
    // 0xe321ec: ldur            lr, [fp, #-0x30]
    // 0xe321f0: stp             lr, x16, [SP]
    // 0xe321f4: r0 = Map._fromLiteral()
    //     0xe321f4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe321f8: ldur            x1, [fp, #-0x20]
    // 0xe321fc: ldur            x2, [fp, #-0x18]
    // 0xe32200: mov             x3, x0
    // 0xe32204: r0 = findAllByAuthor()
    //     0xe32204: bl              #0xe322b4  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAllByAuthor
    // 0xe32208: ldur            x2, [fp, #-0x28]
    // 0xe3220c: r1 = Function '<anonymous closure>':.
    //     0xe3220c: add             x1, PP, #0x41, lsl #12  ; [pp+0x412e0] AnonymousClosure: (0xe32448), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::onPageRequest (0xe32104)
    //     0xe32210: ldr             x1, [x1, #0x2e0]
    // 0xe32214: stur            x0, [fp, #-0x20]
    // 0xe32218: r0 = AllocateClosure()
    //     0xe32218: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3221c: r16 = <void?>
    //     0xe3221c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32220: ldur            lr, [fp, #-0x20]
    // 0xe32224: stp             lr, x16, [SP, #8]
    // 0xe32228: str             x0, [SP]
    // 0xe3222c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe3222c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe32230: r0 = then()
    //     0xe32230: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe32234: ldur            x1, [fp, #-8]
    // 0xe32238: ldur            x2, [fp, #-0x10]
    // 0xe3223c: r0 = onPageRequest()
    //     0xe3223c: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe32240: r0 = Null
    //     0xe32240: mov             x0, NULL
    // 0xe32244: LeaveFrame
    //     0xe32244: mov             SP, fp
    //     0xe32248: ldp             fp, lr, [SP], #0x10
    // 0xe3224c: ret
    //     0xe3224c: ret             
    // 0xe32250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32254: b               #0xe32124
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Article>>) {
    // ** addr: 0xe32448, size: 0xa0
    // 0xe32448: EnterFrame
    //     0xe32448: stp             fp, lr, [SP, #-0x10]!
    //     0xe3244c: mov             fp, SP
    // 0xe32450: AllocStack(0x28)
    //     0xe32450: sub             SP, SP, #0x28
    // 0xe32454: SetupParameters()
    //     0xe32454: ldr             x0, [fp, #0x18]
    //     0xe32458: ldur            w1, [x0, #0x17]
    //     0xe3245c: add             x1, x1, HEAP, lsl #32
    // 0xe32460: CheckStackOverflow
    //     0xe32460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32464: cmp             SP, x16
    //     0xe32468: b.ls            #0xe324e0
    // 0xe3246c: LoadField: r0 = r1->field_f
    //     0xe3246c: ldur            w0, [x1, #0xf]
    // 0xe32470: DecompressPointer r0
    //     0xe32470: add             x0, x0, HEAP, lsl #32
    // 0xe32474: mov             x2, x0
    // 0xe32478: stur            x0, [fp, #-8]
    // 0xe3247c: r1 = Function '_onSuccess@1844176573':.
    //     0xe3247c: add             x1, PP, #0x41, lsl #12  ; [pp+0x412e8] AnonymousClosure: (0xe32588), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess (0xe325c8)
    //     0xe32480: ldr             x1, [x1, #0x2e8]
    // 0xe32484: r0 = AllocateClosure()
    //     0xe32484: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32488: ldur            x2, [fp, #-8]
    // 0xe3248c: r1 = Function '_onError@1844176573':.
    //     0xe3248c: add             x1, PP, #0x41, lsl #12  ; [pp+0x412f0] AnonymousClosure: (0xe324e8), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError (0xe32524)
    //     0xe32490: ldr             x1, [x1, #0x2f0]
    // 0xe32494: stur            x0, [fp, #-8]
    // 0xe32498: r0 = AllocateClosure()
    //     0xe32498: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3249c: mov             x1, x0
    // 0xe324a0: ldr             x0, [fp, #0x10]
    // 0xe324a4: r2 = LoadClassIdInstr(r0)
    //     0xe324a4: ldur            x2, [x0, #-1]
    //     0xe324a8: ubfx            x2, x2, #0xc, #0x14
    // 0xe324ac: r16 = <void?>
    //     0xe324ac: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe324b0: stp             x0, x16, [SP, #0x10]
    // 0xe324b4: ldur            x16, [fp, #-8]
    // 0xe324b8: stp             x16, x1, [SP]
    // 0xe324bc: mov             x0, x2
    // 0xe324c0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe324c0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe324c4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe324c4: sub             lr, x0, #1, lsl #12
    //     0xe324c8: ldr             lr, [x21, lr, lsl #3]
    //     0xe324cc: blr             lr
    // 0xe324d0: r0 = Null
    //     0xe324d0: mov             x0, NULL
    // 0xe324d4: LeaveFrame
    //     0xe324d4: mov             SP, fp
    //     0xe324d8: ldp             fp, lr, [SP], #0x10
    // 0xe324dc: ret
    //     0xe324dc: ret             
    // 0xe324e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe324e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe324e4: b               #0xe3246c
  }
  [closure] void _onError(dynamic, NetworkExceptions) {
    // ** addr: 0xe324e8, size: 0x3c
    // 0xe324e8: EnterFrame
    //     0xe324e8: stp             fp, lr, [SP, #-0x10]!
    //     0xe324ec: mov             fp, SP
    // 0xe324f0: ldr             x0, [fp, #0x18]
    // 0xe324f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe324f4: ldur            w1, [x0, #0x17]
    // 0xe324f8: DecompressPointer r1
    //     0xe324f8: add             x1, x1, HEAP, lsl #32
    // 0xe324fc: CheckStackOverflow
    //     0xe324fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32500: cmp             SP, x16
    //     0xe32504: b.ls            #0xe3251c
    // 0xe32508: ldr             x2, [fp, #0x10]
    // 0xe3250c: r0 = _onError()
    //     0xe3250c: bl              #0xe32524  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError
    // 0xe32510: LeaveFrame
    //     0xe32510: mov             SP, fp
    //     0xe32514: ldp             fp, lr, [SP], #0x10
    // 0xe32518: ret
    //     0xe32518: ret             
    // 0xe3251c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3251c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32520: b               #0xe32508
  }
  _ _onError(/* No info */) {
    // ** addr: 0xe32524, size: 0x64
    // 0xe32524: EnterFrame
    //     0xe32524: stp             fp, lr, [SP, #-0x10]!
    //     0xe32528: mov             fp, SP
    // 0xe3252c: AllocStack(0x8)
    //     0xe3252c: sub             SP, SP, #8
    // 0xe32530: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xe32530: mov             x0, x2
    //     0xe32534: stur            x2, [fp, #-8]
    // 0xe32538: CheckStackOverflow
    //     0xe32538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3253c: cmp             SP, x16
    //     0xe32540: b.ls            #0xe32580
    // 0xe32544: LoadField: r2 = r1->field_2b
    //     0xe32544: ldur            w2, [x1, #0x2b]
    // 0xe32548: DecompressPointer r2
    //     0xe32548: add             x2, x2, HEAP, lsl #32
    // 0xe3254c: mov             x1, x2
    // 0xe32550: r2 = true
    //     0xe32550: add             x2, NULL, #0x20  ; true
    // 0xe32554: r0 = value=()
    //     0xe32554: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe32558: ldur            x1, [fp, #-8]
    // 0xe3255c: r0 = getErrorMessage()
    //     0xe3255c: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0xe32560: mov             x1, x0
    // 0xe32564: r2 = Instance_IconData
    //     0xe32564: add             x2, PP, #0x28, lsl #12  ; [pp+0x28080] Obj!IconData@e0fe91
    //     0xe32568: ldr             x2, [x2, #0x80]
    // 0xe3256c: r0 = show()
    //     0xe3256c: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xe32570: r0 = Null
    //     0xe32570: mov             x0, NULL
    // 0xe32574: LeaveFrame
    //     0xe32574: mov             SP, fp
    //     0xe32578: ldp             fp, lr, [SP], #0x10
    // 0xe3257c: ret
    //     0xe3257c: ret             
    // 0xe32580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32580: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32584: b               #0xe32544
  }
  [closure] void _onSuccess(dynamic, List<Article>, Pagination?) {
    // ** addr: 0xe32588, size: 0x40
    // 0xe32588: EnterFrame
    //     0xe32588: stp             fp, lr, [SP, #-0x10]!
    //     0xe3258c: mov             fp, SP
    // 0xe32590: ldr             x0, [fp, #0x20]
    // 0xe32594: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe32594: ldur            w1, [x0, #0x17]
    // 0xe32598: DecompressPointer r1
    //     0xe32598: add             x1, x1, HEAP, lsl #32
    // 0xe3259c: CheckStackOverflow
    //     0xe3259c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe325a0: cmp             SP, x16
    //     0xe325a4: b.ls            #0xe325c0
    // 0xe325a8: ldr             x2, [fp, #0x18]
    // 0xe325ac: ldr             x3, [fp, #0x10]
    // 0xe325b0: r0 = _onSuccess()
    //     0xe325b0: bl              #0xe325c8  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess
    // 0xe325b4: LeaveFrame
    //     0xe325b4: mov             SP, fp
    //     0xe325b8: ldp             fp, lr, [SP], #0x10
    // 0xe325bc: ret
    //     0xe325bc: ret             
    // 0xe325c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe325c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe325c4: b               #0xe325a8
  }
  _ _onSuccess(/* No info */) {
    // ** addr: 0xe325c8, size: 0x70
    // 0xe325c8: EnterFrame
    //     0xe325c8: stp             fp, lr, [SP, #-0x10]!
    //     0xe325cc: mov             fp, SP
    // 0xe325d0: CheckStackOverflow
    //     0xe325d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe325d4: cmp             SP, x16
    //     0xe325d8: b.ls            #0xe32630
    // 0xe325dc: cmp             w3, NULL
    // 0xe325e0: b.ne            #0xe325ec
    // 0xe325e4: r0 = Null
    //     0xe325e4: mov             x0, NULL
    // 0xe325e8: b               #0xe32608
    // 0xe325ec: LoadField: r0 = r3->field_7
    //     0xe325ec: ldur            x0, [x3, #7]
    // 0xe325f0: LoadField: r4 = r3->field_f
    //     0xe325f0: ldur            x4, [x3, #0xf]
    // 0xe325f4: cmp             x0, x4
    // 0xe325f8: r16 = true
    //     0xe325f8: add             x16, NULL, #0x20  ; true
    // 0xe325fc: r17 = false
    //     0xe325fc: add             x17, NULL, #0x30  ; false
    // 0xe32600: csel            x3, x16, x17, lt
    // 0xe32604: mov             x0, x3
    // 0xe32608: cmp             w0, NULL
    // 0xe3260c: b.ne            #0xe32618
    // 0xe32610: r3 = false
    //     0xe32610: add             x3, NULL, #0x30  ; false
    // 0xe32614: b               #0xe3261c
    // 0xe32618: mov             x3, x0
    // 0xe3261c: r0 = appendToPage()
    //     0xe3261c: bl              #0x7f8b40  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::appendToPage
    // 0xe32620: r0 = Null
    //     0xe32620: mov             x0, NULL
    // 0xe32624: LeaveFrame
    //     0xe32624: mov             SP, fp
    //     0xe32628: ldp             fp, lr, [SP], #0x10
    // 0xe3262c: ret
    //     0xe3262c: ret             
    // 0xe32630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32630: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32634: b               #0xe325dc
  }
}
