// lib: , url: package:nuonline/app/modules/article/article_author/views/article_author_view.dart

// class id: 1050116, size: 0x8
class :: {
}

// class id: 4453, size: 0x14, field offset: 0x14
class ArticleAuthorView extends GetWidget<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0x862248, size: 0xf8
    // 0x862248: EnterFrame
    //     0x862248: stp             fp, lr, [SP, #-0x10]!
    //     0x86224c: mov             fp, SP
    // 0x862250: AllocStack(0x18)
    //     0x862250: sub             SP, SP, #0x18
    // 0x862254: CheckStackOverflow
    //     0x862254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862258: cmp             SP, x16
    //     0x86225c: b.ls            #0x862338
    // 0x862260: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x862260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x862264: ldr             x0, [x0, #0x2670]
    //     0x862268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86226c: cmp             w0, w16
    //     0x862270: b.ne            #0x86227c
    //     0x862274: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x862278: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x86227c: r0 = GetNavigation.arguments()
    //     0x86227c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x862280: r16 = "author"
    //     0x862280: add             x16, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x862284: ldr             x16, [x16, #0xbf8]
    // 0x862288: stp             x16, x0, [SP]
    // 0x86228c: r4 = 0
    //     0x86228c: movz            x4, #0
    // 0x862290: ldr             x0, [SP, #8]
    // 0x862294: r16 = UnlinkedCall_0x5f3c08
    //     0x862294: add             x16, PP, #0x41, lsl #12  ; [pp+0x412c0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x862298: add             x16, x16, #0x2c0
    // 0x86229c: ldp             x5, lr, [x16]
    // 0x8622a0: blr             lr
    // 0x8622a4: mov             x3, x0
    // 0x8622a8: r2 = Null
    //     0x8622a8: mov             x2, NULL
    // 0x8622ac: r1 = Null
    //     0x8622ac: mov             x1, NULL
    // 0x8622b0: stur            x3, [fp, #-8]
    // 0x8622b4: r4 = 60
    //     0x8622b4: movz            x4, #0x3c
    // 0x8622b8: branchIfSmi(r0, 0x8622c4)
    //     0x8622b8: tbz             w0, #0, #0x8622c4
    // 0x8622bc: r4 = LoadClassIdInstr(r0)
    //     0x8622bc: ldur            x4, [x0, #-1]
    //     0x8622c0: ubfx            x4, x4, #0xc, #0x14
    // 0x8622c4: r17 = 5596
    //     0x8622c4: movz            x17, #0x15dc
    // 0x8622c8: cmp             x4, x17
    // 0x8622cc: b.eq            #0x8622e4
    // 0x8622d0: r8 = Author
    //     0x8622d0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b4b8] Type: Author
    //     0x8622d4: ldr             x8, [x8, #0x4b8]
    // 0x8622d8: r3 = Null
    //     0x8622d8: add             x3, PP, #0x41, lsl #12  ; [pp+0x412d0] Null
    //     0x8622dc: ldr             x3, [x3, #0x2d0]
    // 0x8622e0: r0 = Author()
    //     0x8622e0: bl              #0x80cd68  ; IsType_Author_Stub
    // 0x8622e4: ldur            x0, [fp, #-8]
    // 0x8622e8: LoadField: r2 = r0->field_7
    //     0x8622e8: ldur            x2, [x0, #7]
    // 0x8622ec: r0 = BoxInt64Instr(r2)
    //     0x8622ec: sbfiz           x0, x2, #1, #0x1f
    //     0x8622f0: cmp             x2, x0, asr #1
    //     0x8622f4: b.eq            #0x862300
    //     0x8622f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8622fc: stur            x2, [x0, #7]
    // 0x862300: r1 = 60
    //     0x862300: movz            x1, #0x3c
    // 0x862304: branchIfSmi(r0, 0x862310)
    //     0x862304: tbz             w0, #0, #0x862310
    // 0x862308: r1 = LoadClassIdInstr(r0)
    //     0x862308: ldur            x1, [x0, #-1]
    //     0x86230c: ubfx            x1, x1, #0xc, #0x14
    // 0x862310: str             x0, [SP]
    // 0x862314: mov             x0, x1
    // 0x862318: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x862318: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x86231c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x86231c: movz            x17, #0x2b03
    //     0x862320: add             lr, x0, x17
    //     0x862324: ldr             lr, [x21, lr, lsl #3]
    //     0x862328: blr             lr
    // 0x86232c: LeaveFrame
    //     0x86232c: mov             SP, fp
    //     0x862330: ldp             fp, lr, [SP], #0x10
    // 0x862334: ret
    //     0x862334: ret             
    // 0x862338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862338: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86233c: b               #0x862260
  }
  _ build(/* No info */) {
    // ** addr: 0xbbeaa4, size: 0xa24
    // 0xbbeaa4: EnterFrame
    //     0xbbeaa4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbeaa8: mov             fp, SP
    // 0xbbeaac: AllocStack(0x70)
    //     0xbbeaac: sub             SP, SP, #0x70
    // 0xbbeab0: SetupParameters(ArticleAuthorView this /* r1 => r0, fp-0x8 */)
    //     0xbbeab0: mov             x0, x1
    //     0xbbeab4: stur            x1, [fp, #-8]
    // 0xbbeab8: CheckStackOverflow
    //     0xbbeab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbeabc: cmp             SP, x16
    //     0xbbeac0: b.ls            #0xbbf4b4
    // 0xbbeac4: r1 = 1
    //     0xbbeac4: movz            x1, #0x1
    // 0xbbeac8: r0 = AllocateContext()
    //     0xbbeac8: bl              #0xec126c  ; AllocateContextStub
    // 0xbbeacc: ldur            x2, [fp, #-8]
    // 0xbbead0: stur            x0, [fp, #-0x10]
    // 0xbbead4: StoreField: r0->field_f = r2
    //     0xbbead4: stur            w2, [x0, #0xf]
    // 0xbbead8: r0 = AppBar()
    //     0xbbead8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xbbeadc: stur            x0, [fp, #-0x18]
    // 0xbbeae0: r16 = Instance_Text
    //     0xbbeae0: add             x16, PP, #0x41, lsl #12  ; [pp+0x411e0] Obj!Text@e23d51
    //     0xbbeae4: ldr             x16, [x16, #0x1e0]
    // 0xbbeae8: str             x16, [SP]
    // 0xbbeaec: mov             x1, x0
    // 0xbbeaf0: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xbbeaf0: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xbbeaf4: ldr             x4, [x4, #0x6e8]
    // 0xbbeaf8: r0 = AppBar()
    //     0xbbeaf8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xbbeafc: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbbeafc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbeb00: ldr             x0, [x0, #0x26d0]
    //     0xbbeb04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbeb08: cmp             w0, w16
    //     0xbbeb0c: b.ne            #0xbbeb1c
    //     0xbbeb10: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbbeb14: ldr             x2, [x2, #0xb90]
    //     0xbbeb18: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbeb1c: mov             x1, x0
    // 0xbbeb20: ldur            x2, [fp, #-8]
    // 0xbbeb24: stur            x0, [fp, #-0x20]
    // 0xbbeb28: r0 = []()
    //     0xbbeb28: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbeb2c: mov             x4, x0
    // 0xbbeb30: ldur            x3, [fp, #-8]
    // 0xbbeb34: stur            x4, [fp, #-0x30]
    // 0xbbeb38: LoadField: r5 = r3->field_b
    //     0xbbeb38: ldur            w5, [x3, #0xb]
    // 0xbbeb3c: DecompressPointer r5
    //     0xbbeb3c: add             x5, x5, HEAP, lsl #32
    // 0xbbeb40: mov             x0, x4
    // 0xbbeb44: mov             x2, x5
    // 0xbbeb48: stur            x5, [fp, #-0x28]
    // 0xbbeb4c: r1 = Null
    //     0xbbeb4c: mov             x1, NULL
    // 0xbbeb50: cmp             w2, NULL
    // 0xbbeb54: b.eq            #0xbbeb78
    // 0xbbeb58: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbeb58: ldur            w4, [x2, #0x17]
    // 0xbbeb5c: DecompressPointer r4
    //     0xbbeb5c: add             x4, x4, HEAP, lsl #32
    // 0xbbeb60: r8 = X0 bound GetLifeCycleBase?
    //     0xbbeb60: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbeb64: ldr             x8, [x8, #0xb98]
    // 0xbbeb68: LoadField: r9 = r4->field_7
    //     0xbbeb68: ldur            x9, [x4, #7]
    // 0xbbeb6c: r3 = Null
    //     0xbbeb6c: add             x3, PP, #0x41, lsl #12  ; [pp+0x411e8] Null
    //     0xbbeb70: ldr             x3, [x3, #0x1e8]
    // 0xbbeb74: blr             x9
    // 0xbbeb78: ldur            x1, [fp, #-0x20]
    // 0xbbeb7c: ldur            x2, [fp, #-8]
    // 0xbbeb80: r0 = []()
    //     0xbbeb80: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbeb84: ldur            x2, [fp, #-0x28]
    // 0xbbeb88: mov             x3, x0
    // 0xbbeb8c: r1 = Null
    //     0xbbeb8c: mov             x1, NULL
    // 0xbbeb90: stur            x3, [fp, #-0x38]
    // 0xbbeb94: cmp             w2, NULL
    // 0xbbeb98: b.eq            #0xbbebbc
    // 0xbbeb9c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbeb9c: ldur            w4, [x2, #0x17]
    // 0xbbeba0: DecompressPointer r4
    //     0xbbeba0: add             x4, x4, HEAP, lsl #32
    // 0xbbeba4: r8 = X0 bound GetLifeCycleBase?
    //     0xbbeba4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbeba8: ldr             x8, [x8, #0xb98]
    // 0xbbebac: LoadField: r9 = r4->field_7
    //     0xbbebac: ldur            x9, [x4, #7]
    // 0xbbebb0: r3 = Null
    //     0xbbebb0: add             x3, PP, #0x41, lsl #12  ; [pp+0x411f8] Null
    //     0xbbebb4: ldr             x3, [x3, #0x1f8]
    // 0xbbebb8: blr             x9
    // 0xbbebbc: r0 = Radius()
    //     0xbbebbc: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xbbebc0: d0 = 65.000000
    //     0xbbebc0: ldr             d0, [PP, #0x6960]  ; [pp+0x6960] IMM: double(65) from 0x4050400000000000
    // 0xbbebc4: stur            x0, [fp, #-0x40]
    // 0xbbebc8: StoreField: r0->field_7 = d0
    //     0xbbebc8: stur            d0, [x0, #7]
    // 0xbbebcc: StoreField: r0->field_f = d0
    //     0xbbebcc: stur            d0, [x0, #0xf]
    // 0xbbebd0: r0 = BorderRadius()
    //     0xbbebd0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xbbebd4: mov             x3, x0
    // 0xbbebd8: ldur            x0, [fp, #-0x40]
    // 0xbbebdc: stur            x3, [fp, #-0x48]
    // 0xbbebe0: StoreField: r3->field_7 = r0
    //     0xbbebe0: stur            w0, [x3, #7]
    // 0xbbebe4: StoreField: r3->field_b = r0
    //     0xbbebe4: stur            w0, [x3, #0xb]
    // 0xbbebe8: StoreField: r3->field_f = r0
    //     0xbbebe8: stur            w0, [x3, #0xf]
    // 0xbbebec: StoreField: r3->field_13 = r0
    //     0xbbebec: stur            w0, [x3, #0x13]
    // 0xbbebf0: ldur            x1, [fp, #-0x20]
    // 0xbbebf4: ldur            x2, [fp, #-8]
    // 0xbbebf8: r0 = []()
    //     0xbbebf8: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbebfc: ldur            x2, [fp, #-0x28]
    // 0xbbec00: mov             x3, x0
    // 0xbbec04: r1 = Null
    //     0xbbec04: mov             x1, NULL
    // 0xbbec08: stur            x3, [fp, #-0x40]
    // 0xbbec0c: cmp             w2, NULL
    // 0xbbec10: b.eq            #0xbbec34
    // 0xbbec14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbec14: ldur            w4, [x2, #0x17]
    // 0xbbec18: DecompressPointer r4
    //     0xbbec18: add             x4, x4, HEAP, lsl #32
    // 0xbbec1c: r8 = X0 bound GetLifeCycleBase?
    //     0xbbec1c: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbec20: ldr             x8, [x8, #0xb98]
    // 0xbbec24: LoadField: r9 = r4->field_7
    //     0xbbec24: ldur            x9, [x4, #7]
    // 0xbbec28: r3 = Null
    //     0xbbec28: add             x3, PP, #0x41, lsl #12  ; [pp+0x41208] Null
    //     0xbbec2c: ldr             x3, [x3, #0x208]
    // 0xbbec30: blr             x9
    // 0xbbec34: ldur            x0, [fp, #-0x40]
    // 0xbbec38: LoadField: r1 = r0->field_3b
    //     0xbbec38: ldur            w1, [x0, #0x3b]
    // 0xbbec3c: DecompressPointer r1
    //     0xbbec3c: add             x1, x1, HEAP, lsl #32
    // 0xbbec40: LoadField: r0 = r1->field_13
    //     0xbbec40: ldur            w0, [x1, #0x13]
    // 0xbbec44: DecompressPointer r0
    //     0xbbec44: add             x0, x0, HEAP, lsl #32
    // 0xbbec48: stur            x0, [fp, #-0x40]
    // 0xbbec4c: r0 = NFadeInImageNetwork()
    //     0xbbec4c: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xbbec50: mov             x1, x0
    // 0xbbec54: ldur            x0, [fp, #-0x40]
    // 0xbbec58: stur            x1, [fp, #-0x50]
    // 0xbbec5c: StoreField: r1->field_b = r0
    //     0xbbec5c: stur            w0, [x1, #0xb]
    // 0xbbec60: r0 = "packages/nuikit/assets/images/icons/defaultPicture.svg"
    //     0xbbec60: add             x0, PP, #0x41, lsl #12  ; [pp+0x41218] "packages/nuikit/assets/images/icons/defaultPicture.svg"
    //     0xbbec64: ldr             x0, [x0, #0x218]
    // 0xbbec68: StoreField: r1->field_f = r0
    //     0xbbec68: stur            w0, [x1, #0xf]
    // 0xbbec6c: StoreField: r1->field_13 = r0
    //     0xbbec6c: stur            w0, [x1, #0x13]
    // 0xbbec70: r0 = Instance_BoxFit
    //     0xbbec70: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xbbec74: ldr             x0, [x0, #0xa28]
    // 0xbbec78: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbec78: stur            w0, [x1, #0x17]
    // 0xbbec7c: r0 = ClipRRect()
    //     0xbbec7c: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xbbec80: mov             x1, x0
    // 0xbbec84: ldur            x0, [fp, #-0x48]
    // 0xbbec88: stur            x1, [fp, #-0x40]
    // 0xbbec8c: StoreField: r1->field_f = r0
    //     0xbbec8c: stur            w0, [x1, #0xf]
    // 0xbbec90: r0 = Instance_Clip
    //     0xbbec90: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xbbec94: ldr             x0, [x0, #0x4f8]
    // 0xbbec98: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbec98: stur            w0, [x1, #0x17]
    // 0xbbec9c: ldur            x0, [fp, #-0x50]
    // 0xbbeca0: StoreField: r1->field_b = r0
    //     0xbbeca0: stur            w0, [x1, #0xb]
    // 0xbbeca4: r0 = SizedBox()
    //     0xbbeca4: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbeca8: mov             x3, x0
    // 0xbbecac: r0 = 65.000000
    //     0xbbecac: add             x0, PP, #0x41, lsl #12  ; [pp+0x41220] 65
    //     0xbbecb0: ldr             x0, [x0, #0x220]
    // 0xbbecb4: stur            x3, [fp, #-0x48]
    // 0xbbecb8: StoreField: r3->field_f = r0
    //     0xbbecb8: stur            w0, [x3, #0xf]
    // 0xbbecbc: StoreField: r3->field_13 = r0
    //     0xbbecbc: stur            w0, [x3, #0x13]
    // 0xbbecc0: ldur            x0, [fp, #-0x40]
    // 0xbbecc4: StoreField: r3->field_b = r0
    //     0xbbecc4: stur            w0, [x3, #0xb]
    // 0xbbecc8: ldur            x1, [fp, #-0x20]
    // 0xbbeccc: ldur            x2, [fp, #-8]
    // 0xbbecd0: r0 = []()
    //     0xbbecd0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbecd4: ldur            x2, [fp, #-0x28]
    // 0xbbecd8: mov             x3, x0
    // 0xbbecdc: r1 = Null
    //     0xbbecdc: mov             x1, NULL
    // 0xbbece0: stur            x3, [fp, #-0x40]
    // 0xbbece4: cmp             w2, NULL
    // 0xbbece8: b.eq            #0xbbed0c
    // 0xbbecec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbecec: ldur            w4, [x2, #0x17]
    // 0xbbecf0: DecompressPointer r4
    //     0xbbecf0: add             x4, x4, HEAP, lsl #32
    // 0xbbecf4: r8 = X0 bound GetLifeCycleBase?
    //     0xbbecf4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbecf8: ldr             x8, [x8, #0xb98]
    // 0xbbecfc: LoadField: r9 = r4->field_7
    //     0xbbecfc: ldur            x9, [x4, #7]
    // 0xbbed00: r3 = Null
    //     0xbbed00: add             x3, PP, #0x41, lsl #12  ; [pp+0x41228] Null
    //     0xbbed04: ldr             x3, [x3, #0x228]
    // 0xbbed08: blr             x9
    // 0xbbed0c: ldur            x0, [fp, #-0x40]
    // 0xbbed10: LoadField: r1 = r0->field_3b
    //     0xbbed10: ldur            w1, [x0, #0x3b]
    // 0xbbed14: DecompressPointer r1
    //     0xbbed14: add             x1, x1, HEAP, lsl #32
    // 0xbbed18: LoadField: r0 = r1->field_f
    //     0xbbed18: ldur            w0, [x1, #0xf]
    // 0xbbed1c: DecompressPointer r0
    //     0xbbed1c: add             x0, x0, HEAP, lsl #32
    // 0xbbed20: mov             x1, x0
    // 0xbbed24: r2 = "&RSQUO;"
    //     0xbbed24: add             x2, PP, #0x38, lsl #12  ; [pp+0x38290] "&RSQUO;"
    //     0xbbed28: ldr             x2, [x2, #0x290]
    // 0xbbed2c: r3 = "\'"
    //     0xbbed2c: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xbbed30: r0 = replaceAll()
    //     0xbbed30: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbbed34: mov             x1, x0
    // 0xbbed38: r2 = "&#039;"
    //     0xbbed38: add             x2, PP, #0x38, lsl #12  ; [pp+0x38298] "&#039;"
    //     0xbbed3c: ldr             x2, [x2, #0x298]
    // 0xbbed40: r3 = "\'"
    //     0xbbed40: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xbbed44: r0 = replaceAll()
    //     0xbbed44: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbbed48: stur            x0, [fp, #-0x40]
    // 0xbbed4c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbbed4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbed50: ldr             x0, [x0, #0x2670]
    //     0xbbed54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbed58: cmp             w0, w16
    //     0xbbed5c: b.ne            #0xbbed68
    //     0xbbed60: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbbed64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbed68: r0 = GetNavigation.textTheme()
    //     0xbbed68: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbbed6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbed6c: ldur            w1, [x0, #0x17]
    // 0xbbed70: DecompressPointer r1
    //     0xbbed70: add             x1, x1, HEAP, lsl #32
    // 0xbbed74: cmp             w1, NULL
    // 0xbbed78: b.eq            #0xbbf4bc
    // 0xbbed7c: r16 = 16.000000
    //     0xbbed7c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbbed80: ldr             x16, [x16, #0x80]
    // 0xbbed84: str             x16, [SP]
    // 0xbbed88: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xbbed88: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xbbed8c: ldr             x4, [x4, #0x88]
    // 0xbbed90: r0 = copyWith()
    //     0xbbed90: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbed94: stur            x0, [fp, #-0x50]
    // 0xbbed98: r0 = Text()
    //     0xbbed98: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbed9c: mov             x1, x0
    // 0xbbeda0: ldur            x0, [fp, #-0x40]
    // 0xbbeda4: stur            x1, [fp, #-0x58]
    // 0xbbeda8: StoreField: r1->field_b = r0
    //     0xbbeda8: stur            w0, [x1, #0xb]
    // 0xbbedac: ldur            x0, [fp, #-0x50]
    // 0xbbedb0: StoreField: r1->field_13 = r0
    //     0xbbedb0: stur            w0, [x1, #0x13]
    // 0xbbedb4: r0 = GetNavigation.theme()
    //     0xbbedb4: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xbbedb8: LoadField: r1 = r0->field_3f
    //     0xbbedb8: ldur            w1, [x0, #0x3f]
    // 0xbbedbc: DecompressPointer r1
    //     0xbbedbc: add             x1, x1, HEAP, lsl #32
    // 0xbbedc0: LoadField: r0 = r1->field_2b
    //     0xbbedc0: ldur            w0, [x1, #0x2b]
    // 0xbbedc4: DecompressPointer r0
    //     0xbbedc4: add             x0, x0, HEAP, lsl #32
    // 0xbbedc8: stur            x0, [fp, #-0x40]
    // 0xbbedcc: r0 = Icon()
    //     0xbbedcc: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xbbedd0: mov             x3, x0
    // 0xbbedd4: r0 = Instance_IconData
    //     0xbbedd4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32410] Obj!IconData@e10071
    //     0xbbedd8: ldr             x0, [x0, #0x410]
    // 0xbbeddc: stur            x3, [fp, #-0x50]
    // 0xbbede0: StoreField: r3->field_b = r0
    //     0xbbede0: stur            w0, [x3, #0xb]
    // 0xbbede4: r0 = 16.000000
    //     0xbbede4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xbbede8: ldr             x0, [x0, #0x80]
    // 0xbbedec: StoreField: r3->field_f = r0
    //     0xbbedec: stur            w0, [x3, #0xf]
    // 0xbbedf0: ldur            x0, [fp, #-0x40]
    // 0xbbedf4: StoreField: r3->field_23 = r0
    //     0xbbedf4: stur            w0, [x3, #0x23]
    // 0xbbedf8: r1 = Null
    //     0xbbedf8: mov             x1, NULL
    // 0xbbedfc: r2 = 6
    //     0xbbedfc: movz            x2, #0x6
    // 0xbbee00: r0 = AllocateArray()
    //     0xbbee00: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbee04: mov             x2, x0
    // 0xbbee08: ldur            x0, [fp, #-0x58]
    // 0xbbee0c: stur            x2, [fp, #-0x40]
    // 0xbbee10: StoreField: r2->field_f = r0
    //     0xbbee10: stur            w0, [x2, #0xf]
    // 0xbbee14: r16 = Instance_SizedBox
    //     0xbbee14: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xbbee18: ldr             x16, [x16, #0xbd8]
    // 0xbbee1c: StoreField: r2->field_13 = r16
    //     0xbbee1c: stur            w16, [x2, #0x13]
    // 0xbbee20: ldur            x0, [fp, #-0x50]
    // 0xbbee24: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbee24: stur            w0, [x2, #0x17]
    // 0xbbee28: r1 = <Widget>
    //     0xbbee28: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbee2c: r0 = AllocateGrowableArray()
    //     0xbbee2c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbee30: mov             x1, x0
    // 0xbbee34: ldur            x0, [fp, #-0x40]
    // 0xbbee38: stur            x1, [fp, #-0x50]
    // 0xbbee3c: StoreField: r1->field_f = r0
    //     0xbbee3c: stur            w0, [x1, #0xf]
    // 0xbbee40: r2 = 6
    //     0xbbee40: movz            x2, #0x6
    // 0xbbee44: StoreField: r1->field_b = r2
    //     0xbbee44: stur            w2, [x1, #0xb]
    // 0xbbee48: r0 = Row()
    //     0xbbee48: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbee4c: mov             x3, x0
    // 0xbbee50: r0 = Instance_Axis
    //     0xbbee50: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xbbee54: stur            x3, [fp, #-0x40]
    // 0xbbee58: StoreField: r3->field_f = r0
    //     0xbbee58: stur            w0, [x3, #0xf]
    // 0xbbee5c: r0 = Instance_MainAxisAlignment
    //     0xbbee5c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbee60: ldr             x0, [x0, #0x730]
    // 0xbbee64: StoreField: r3->field_13 = r0
    //     0xbbee64: stur            w0, [x3, #0x13]
    // 0xbbee68: r4 = Instance_MainAxisSize
    //     0xbbee68: add             x4, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xbbee6c: ldr             x4, [x4, #0xe88]
    // 0xbbee70: ArrayStore: r3[0] = r4  ; List_4
    //     0xbbee70: stur            w4, [x3, #0x17]
    // 0xbbee74: r5 = Instance_CrossAxisAlignment
    //     0xbbee74: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbbee78: ldr             x5, [x5, #0x740]
    // 0xbbee7c: StoreField: r3->field_1b = r5
    //     0xbbee7c: stur            w5, [x3, #0x1b]
    // 0xbbee80: r6 = Instance_VerticalDirection
    //     0xbbee80: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbee84: ldr             x6, [x6, #0x748]
    // 0xbbee88: StoreField: r3->field_23 = r6
    //     0xbbee88: stur            w6, [x3, #0x23]
    // 0xbbee8c: r7 = Instance_Clip
    //     0xbbee8c: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbee90: ldr             x7, [x7, #0x750]
    // 0xbbee94: StoreField: r3->field_2b = r7
    //     0xbbee94: stur            w7, [x3, #0x2b]
    // 0xbbee98: StoreField: r3->field_2f = rZR
    //     0xbbee98: stur            xzr, [x3, #0x2f]
    // 0xbbee9c: ldur            x1, [fp, #-0x50]
    // 0xbbeea0: StoreField: r3->field_b = r1
    //     0xbbeea0: stur            w1, [x3, #0xb]
    // 0xbbeea4: r1 = Null
    //     0xbbeea4: mov             x1, NULL
    // 0xbbeea8: r2 = 6
    //     0xbbeea8: movz            x2, #0x6
    // 0xbbeeac: r0 = AllocateArray()
    //     0xbbeeac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbeeb0: mov             x2, x0
    // 0xbbeeb4: ldur            x0, [fp, #-0x48]
    // 0xbbeeb8: stur            x2, [fp, #-0x50]
    // 0xbbeebc: StoreField: r2->field_f = r0
    //     0xbbeebc: stur            w0, [x2, #0xf]
    // 0xbbeec0: r16 = Instance_SizedBox
    //     0xbbeec0: add             x16, PP, #0x41, lsl #12  ; [pp+0x41238] Obj!SizedBox@e1e4c1
    //     0xbbeec4: ldr             x16, [x16, #0x238]
    // 0xbbeec8: StoreField: r2->field_13 = r16
    //     0xbbeec8: stur            w16, [x2, #0x13]
    // 0xbbeecc: ldur            x0, [fp, #-0x40]
    // 0xbbeed0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbeed0: stur            w0, [x2, #0x17]
    // 0xbbeed4: r1 = <Widget>
    //     0xbbeed4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbeed8: r0 = AllocateGrowableArray()
    //     0xbbeed8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbeedc: mov             x3, x0
    // 0xbbeee0: ldur            x0, [fp, #-0x50]
    // 0xbbeee4: stur            x3, [fp, #-0x40]
    // 0xbbeee8: StoreField: r3->field_f = r0
    //     0xbbeee8: stur            w0, [x3, #0xf]
    // 0xbbeeec: r0 = 6
    //     0xbbeeec: movz            x0, #0x6
    // 0xbbeef0: StoreField: r3->field_b = r0
    //     0xbbeef0: stur            w0, [x3, #0xb]
    // 0xbbeef4: ldur            x1, [fp, #-0x20]
    // 0xbbeef8: ldur            x2, [fp, #-8]
    // 0xbbeefc: r0 = []()
    //     0xbbeefc: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbef00: ldur            x2, [fp, #-0x28]
    // 0xbbef04: mov             x3, x0
    // 0xbbef08: r1 = Null
    //     0xbbef08: mov             x1, NULL
    // 0xbbef0c: stur            x3, [fp, #-0x48]
    // 0xbbef10: cmp             w2, NULL
    // 0xbbef14: b.eq            #0xbbef38
    // 0xbbef18: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbef18: ldur            w4, [x2, #0x17]
    // 0xbbef1c: DecompressPointer r4
    //     0xbbef1c: add             x4, x4, HEAP, lsl #32
    // 0xbbef20: r8 = X0 bound GetLifeCycleBase?
    //     0xbbef20: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbef24: ldr             x8, [x8, #0xb98]
    // 0xbbef28: LoadField: r9 = r4->field_7
    //     0xbbef28: ldur            x9, [x4, #7]
    // 0xbbef2c: r3 = Null
    //     0xbbef2c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41240] Null
    //     0xbbef30: ldr             x3, [x3, #0x240]
    // 0xbbef34: blr             x9
    // 0xbbef38: ldur            x0, [fp, #-0x48]
    // 0xbbef3c: LoadField: r1 = r0->field_3b
    //     0xbbef3c: ldur            w1, [x0, #0x3b]
    // 0xbbef40: DecompressPointer r1
    //     0xbbef40: add             x1, x1, HEAP, lsl #32
    // 0xbbef44: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbbef44: ldur            w0, [x1, #0x17]
    // 0xbbef48: DecompressPointer r0
    //     0xbbef48: add             x0, x0, HEAP, lsl #32
    // 0xbbef4c: cmp             w0, NULL
    // 0xbbef50: b.eq            #0xbbf0b4
    // 0xbbef54: ldur            x0, [fp, #-0x40]
    // 0xbbef58: ldur            x1, [fp, #-0x20]
    // 0xbbef5c: ldur            x2, [fp, #-8]
    // 0xbbef60: r0 = []()
    //     0xbbef60: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbef64: ldur            x2, [fp, #-0x28]
    // 0xbbef68: mov             x3, x0
    // 0xbbef6c: r1 = Null
    //     0xbbef6c: mov             x1, NULL
    // 0xbbef70: stur            x3, [fp, #-8]
    // 0xbbef74: cmp             w2, NULL
    // 0xbbef78: b.eq            #0xbbef9c
    // 0xbbef7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbef7c: ldur            w4, [x2, #0x17]
    // 0xbbef80: DecompressPointer r4
    //     0xbbef80: add             x4, x4, HEAP, lsl #32
    // 0xbbef84: r8 = X0 bound GetLifeCycleBase?
    //     0xbbef84: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbef88: ldr             x8, [x8, #0xb98]
    // 0xbbef8c: LoadField: r9 = r4->field_7
    //     0xbbef8c: ldur            x9, [x4, #7]
    // 0xbbef90: r3 = Null
    //     0xbbef90: add             x3, PP, #0x41, lsl #12  ; [pp+0x41250] Null
    //     0xbbef94: ldr             x3, [x3, #0x250]
    // 0xbbef98: blr             x9
    // 0xbbef9c: ldur            x0, [fp, #-8]
    // 0xbbefa0: LoadField: r1 = r0->field_3b
    //     0xbbefa0: ldur            w1, [x0, #0x3b]
    // 0xbbefa4: DecompressPointer r1
    //     0xbbefa4: add             x1, x1, HEAP, lsl #32
    // 0xbbefa8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbbefa8: ldur            w0, [x1, #0x17]
    // 0xbbefac: DecompressPointer r0
    //     0xbbefac: add             x0, x0, HEAP, lsl #32
    // 0xbbefb0: stur            x0, [fp, #-8]
    // 0xbbefb4: cmp             w0, NULL
    // 0xbbefb8: b.eq            #0xbbf4c0
    // 0xbbefbc: r0 = GetNavigation.textTheme()
    //     0xbbefbc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbbefc0: LoadField: r1 = r0->field_27
    //     0xbbefc0: ldur            w1, [x0, #0x27]
    // 0xbbefc4: DecompressPointer r1
    //     0xbbefc4: add             x1, x1, HEAP, lsl #32
    // 0xbbefc8: cmp             w1, NULL
    // 0xbbefcc: b.eq            #0xbbf4c4
    // 0xbbefd0: r16 = 14.000000
    //     0xbbefd0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xbbefd4: ldr             x16, [x16, #0x9a0]
    // 0xbbefd8: r30 = 1.500000
    //     0xbbefd8: add             lr, PP, #0x23, lsl #12  ; [pp+0x23c58] 1.5
    //     0xbbefdc: ldr             lr, [lr, #0xc58]
    // 0xbbefe0: stp             lr, x16, [SP]
    // 0xbbefe4: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x1, height, 0x2, null]
    //     0xbbefe4: add             x4, PP, #0x27, lsl #12  ; [pp+0x274b0] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x1, "height", 0x2, Null]
    //     0xbbefe8: ldr             x4, [x4, #0x4b0]
    // 0xbbefec: r0 = copyWith()
    //     0xbbefec: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbeff0: stur            x0, [fp, #-0x20]
    // 0xbbeff4: r0 = Text()
    //     0xbbeff4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbeff8: mov             x1, x0
    // 0xbbeffc: ldur            x0, [fp, #-8]
    // 0xbbf000: stur            x1, [fp, #-0x28]
    // 0xbbf004: StoreField: r1->field_b = r0
    //     0xbbf004: stur            w0, [x1, #0xb]
    // 0xbbf008: ldur            x0, [fp, #-0x20]
    // 0xbbf00c: StoreField: r1->field_13 = r0
    //     0xbbf00c: stur            w0, [x1, #0x13]
    // 0xbbf010: r0 = Instance_TextAlign
    //     0xbbf010: ldr             x0, [PP, #0x4920]  ; [pp+0x4920] Obj!TextAlign@e39441
    // 0xbbf014: StoreField: r1->field_1b = r0
    //     0xbbf014: stur            w0, [x1, #0x1b]
    // 0xbbf018: r0 = Padding()
    //     0xbbf018: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbf01c: mov             x2, x0
    // 0xbbf020: r0 = Instance_EdgeInsets
    //     0xbbf020: add             x0, PP, #0x41, lsl #12  ; [pp+0x41260] Obj!EdgeInsets@e13841
    //     0xbbf024: ldr             x0, [x0, #0x260]
    // 0xbbf028: stur            x2, [fp, #-8]
    // 0xbbf02c: StoreField: r2->field_f = r0
    //     0xbbf02c: stur            w0, [x2, #0xf]
    // 0xbbf030: ldur            x0, [fp, #-0x28]
    // 0xbbf034: StoreField: r2->field_b = r0
    //     0xbbf034: stur            w0, [x2, #0xb]
    // 0xbbf038: ldur            x0, [fp, #-0x40]
    // 0xbbf03c: LoadField: r1 = r0->field_b
    //     0xbbf03c: ldur            w1, [x0, #0xb]
    // 0xbbf040: LoadField: r3 = r0->field_f
    //     0xbbf040: ldur            w3, [x0, #0xf]
    // 0xbbf044: DecompressPointer r3
    //     0xbbf044: add             x3, x3, HEAP, lsl #32
    // 0xbbf048: LoadField: r4 = r3->field_b
    //     0xbbf048: ldur            w4, [x3, #0xb]
    // 0xbbf04c: r3 = LoadInt32Instr(r1)
    //     0xbbf04c: sbfx            x3, x1, #1, #0x1f
    // 0xbbf050: stur            x3, [fp, #-0x60]
    // 0xbbf054: r1 = LoadInt32Instr(r4)
    //     0xbbf054: sbfx            x1, x4, #1, #0x1f
    // 0xbbf058: cmp             x3, x1
    // 0xbbf05c: b.ne            #0xbbf068
    // 0xbbf060: mov             x1, x0
    // 0xbbf064: r0 = _growToNextCapacity()
    //     0xbbf064: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbf068: ldur            x2, [fp, #-0x40]
    // 0xbbf06c: ldur            x3, [fp, #-0x60]
    // 0xbbf070: add             x0, x3, #1
    // 0xbbf074: lsl             x1, x0, #1
    // 0xbbf078: StoreField: r2->field_b = r1
    //     0xbbf078: stur            w1, [x2, #0xb]
    // 0xbbf07c: LoadField: r1 = r2->field_f
    //     0xbbf07c: ldur            w1, [x2, #0xf]
    // 0xbbf080: DecompressPointer r1
    //     0xbbf080: add             x1, x1, HEAP, lsl #32
    // 0xbbf084: ldur            x0, [fp, #-8]
    // 0xbbf088: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbf088: add             x25, x1, x3, lsl #2
    //     0xbbf08c: add             x25, x25, #0xf
    //     0xbbf090: str             w0, [x25]
    //     0xbbf094: tbz             w0, #0, #0xbbf0b0
    //     0xbbf098: ldurb           w16, [x1, #-1]
    //     0xbbf09c: ldurb           w17, [x0, #-1]
    //     0xbbf0a0: and             x16, x17, x16, lsr #2
    //     0xbbf0a4: tst             x16, HEAP, lsr #32
    //     0xbbf0a8: b.eq            #0xbbf0b0
    //     0xbbf0ac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbf0b0: b               #0xbbf0b8
    // 0xbbf0b4: ldur            x2, [fp, #-0x40]
    // 0xbbf0b8: LoadField: r0 = r2->field_b
    //     0xbbf0b8: ldur            w0, [x2, #0xb]
    // 0xbbf0bc: LoadField: r1 = r2->field_f
    //     0xbbf0bc: ldur            w1, [x2, #0xf]
    // 0xbbf0c0: DecompressPointer r1
    //     0xbbf0c0: add             x1, x1, HEAP, lsl #32
    // 0xbbf0c4: LoadField: r3 = r1->field_b
    //     0xbbf0c4: ldur            w3, [x1, #0xb]
    // 0xbbf0c8: r4 = LoadInt32Instr(r0)
    //     0xbbf0c8: sbfx            x4, x0, #1, #0x1f
    // 0xbbf0cc: stur            x4, [fp, #-0x60]
    // 0xbbf0d0: r0 = LoadInt32Instr(r3)
    //     0xbbf0d0: sbfx            x0, x3, #1, #0x1f
    // 0xbbf0d4: cmp             x4, x0
    // 0xbbf0d8: b.ne            #0xbbf0e4
    // 0xbbf0dc: mov             x1, x2
    // 0xbbf0e0: r0 = _growToNextCapacity()
    //     0xbbf0e0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbf0e4: ldur            x0, [fp, #-0x40]
    // 0xbbf0e8: ldur            x1, [fp, #-0x60]
    // 0xbbf0ec: add             x2, x1, #1
    // 0xbbf0f0: lsl             x3, x2, #1
    // 0xbbf0f4: StoreField: r0->field_b = r3
    //     0xbbf0f4: stur            w3, [x0, #0xb]
    // 0xbbf0f8: LoadField: r2 = r0->field_f
    //     0xbbf0f8: ldur            w2, [x0, #0xf]
    // 0xbbf0fc: DecompressPointer r2
    //     0xbbf0fc: add             x2, x2, HEAP, lsl #32
    // 0xbbf100: add             x3, x2, x1, lsl #2
    // 0xbbf104: r16 = Instance_SizedBox
    //     0xbbf104: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xbbf108: ldr             x16, [x16, #0xfe8]
    // 0xbbf10c: StoreField: r3->field_f = r16
    //     0xbbf10c: stur            w16, [x3, #0xf]
    // 0xbbf110: r0 = Column()
    //     0xbbf110: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbf114: mov             x1, x0
    // 0xbbf118: r0 = Instance_Axis
    //     0xbbf118: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbbf11c: stur            x1, [fp, #-8]
    // 0xbbf120: StoreField: r1->field_f = r0
    //     0xbbf120: stur            w0, [x1, #0xf]
    // 0xbbf124: r2 = Instance_MainAxisAlignment
    //     0xbbf124: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbf128: ldr             x2, [x2, #0x730]
    // 0xbbf12c: StoreField: r1->field_13 = r2
    //     0xbbf12c: stur            w2, [x1, #0x13]
    // 0xbbf130: r3 = Instance_MainAxisSize
    //     0xbbf130: add             x3, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xbbf134: ldr             x3, [x3, #0xe88]
    // 0xbbf138: ArrayStore: r1[0] = r3  ; List_4
    //     0xbbf138: stur            w3, [x1, #0x17]
    // 0xbbf13c: r3 = Instance_CrossAxisAlignment
    //     0xbbf13c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbbf140: ldr             x3, [x3, #0x740]
    // 0xbbf144: StoreField: r1->field_1b = r3
    //     0xbbf144: stur            w3, [x1, #0x1b]
    // 0xbbf148: r4 = Instance_VerticalDirection
    //     0xbbf148: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbf14c: ldr             x4, [x4, #0x748]
    // 0xbbf150: StoreField: r1->field_23 = r4
    //     0xbbf150: stur            w4, [x1, #0x23]
    // 0xbbf154: r5 = Instance_Clip
    //     0xbbf154: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbf158: ldr             x5, [x5, #0x750]
    // 0xbbf15c: StoreField: r1->field_2b = r5
    //     0xbbf15c: stur            w5, [x1, #0x2b]
    // 0xbbf160: StoreField: r1->field_2f = rZR
    //     0xbbf160: stur            xzr, [x1, #0x2f]
    // 0xbbf164: ldur            x6, [fp, #-0x40]
    // 0xbbf168: StoreField: r1->field_b = r6
    //     0xbbf168: stur            w6, [x1, #0xb]
    // 0xbbf16c: r0 = Obx()
    //     0xbbf16c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbbf170: ldur            x2, [fp, #-0x10]
    // 0xbbf174: r1 = Function '<anonymous closure>':.
    //     0xbbf174: add             x1, PP, #0x41, lsl #12  ; [pp+0x41268] AnonymousClosure: (0xbbf570), in [package:nuonline/app/modules/article/article_author/views/article_author_view.dart] ArticleAuthorView::build (0xbbeaa4)
    //     0xbbf178: ldr             x1, [x1, #0x268]
    // 0xbbf17c: stur            x0, [fp, #-0x10]
    // 0xbbf180: r0 = AllocateClosure()
    //     0xbbf180: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbf184: mov             x1, x0
    // 0xbbf188: ldur            x0, [fp, #-0x10]
    // 0xbbf18c: StoreField: r0->field_b = r1
    //     0xbbf18c: stur            w1, [x0, #0xb]
    // 0xbbf190: r1 = Null
    //     0xbbf190: mov             x1, NULL
    // 0xbbf194: r2 = 4
    //     0xbbf194: movz            x2, #0x4
    // 0xbbf198: r0 = AllocateArray()
    //     0xbbf198: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbf19c: mov             x2, x0
    // 0xbbf1a0: ldur            x0, [fp, #-8]
    // 0xbbf1a4: stur            x2, [fp, #-0x20]
    // 0xbbf1a8: StoreField: r2->field_f = r0
    //     0xbbf1a8: stur            w0, [x2, #0xf]
    // 0xbbf1ac: ldur            x0, [fp, #-0x10]
    // 0xbbf1b0: StoreField: r2->field_13 = r0
    //     0xbbf1b0: stur            w0, [x2, #0x13]
    // 0xbbf1b4: r1 = <Widget>
    //     0xbbf1b4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbf1b8: r0 = AllocateGrowableArray()
    //     0xbbf1b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbf1bc: mov             x1, x0
    // 0xbbf1c0: ldur            x0, [fp, #-0x20]
    // 0xbbf1c4: stur            x1, [fp, #-8]
    // 0xbbf1c8: StoreField: r1->field_f = r0
    //     0xbbf1c8: stur            w0, [x1, #0xf]
    // 0xbbf1cc: r0 = 4
    //     0xbbf1cc: movz            x0, #0x4
    // 0xbbf1d0: StoreField: r1->field_b = r0
    //     0xbbf1d0: stur            w0, [x1, #0xb]
    // 0xbbf1d4: r0 = ListView()
    //     0xbbf1d4: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbbf1d8: stur            x0, [fp, #-0x10]
    // 0xbbf1dc: r16 = true
    //     0xbbf1dc: add             x16, NULL, #0x20  ; true
    // 0xbbf1e0: r30 = Instance_EdgeInsets
    //     0xbbf1e0: add             lr, PP, #0x41, lsl #12  ; [pp+0x41270] Obj!EdgeInsets@e13901
    //     0xbbf1e4: ldr             lr, [lr, #0x270]
    // 0xbbf1e8: stp             lr, x16, [SP]
    // 0xbbf1ec: mov             x1, x0
    // 0xbbf1f0: ldur            x2, [fp, #-8]
    // 0xbbf1f4: r4 = const [0, 0x4, 0x2, 0x2, padding, 0x3, shrinkWrap, 0x2, null]
    //     0xbbf1f4: add             x4, PP, #0x41, lsl #12  ; [pp+0x41278] List(9) [0, 0x4, 0x2, 0x2, "padding", 0x3, "shrinkWrap", 0x2, Null]
    //     0xbbf1f8: ldr             x4, [x4, #0x278]
    // 0xbbf1fc: r0 = ListView()
    //     0xbbf1fc: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xbbf200: ldur            x2, [fp, #-0x38]
    // 0xbbf204: r1 = Function 'onPageScrolled':.
    //     0xbbf204: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xbbf208: ldr             x1, [x1, #0xa88]
    // 0xbbf20c: r0 = AllocateClosure()
    //     0xbbf20c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbf210: r1 = <ScrollNotification>
    //     0xbbf210: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbbf214: ldr             x1, [x1, #0x110]
    // 0xbbf218: stur            x0, [fp, #-8]
    // 0xbbf21c: r0 = NotificationListener()
    //     0xbbf21c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbbf220: mov             x1, x0
    // 0xbbf224: ldur            x0, [fp, #-8]
    // 0xbbf228: stur            x1, [fp, #-0x20]
    // 0xbbf22c: StoreField: r1->field_13 = r0
    //     0xbbf22c: stur            w0, [x1, #0x13]
    // 0xbbf230: ldur            x0, [fp, #-0x10]
    // 0xbbf234: StoreField: r1->field_b = r0
    //     0xbbf234: stur            w0, [x1, #0xb]
    // 0xbbf238: r0 = RefreshIndicator()
    //     0xbbf238: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbbf23c: mov             x3, x0
    // 0xbbf240: ldur            x0, [fp, #-0x20]
    // 0xbbf244: stur            x3, [fp, #-8]
    // 0xbbf248: StoreField: r3->field_b = r0
    //     0xbbf248: stur            w0, [x3, #0xb]
    // 0xbbf24c: d0 = 40.000000
    //     0xbbf24c: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbbf250: StoreField: r3->field_f = d0
    //     0xbbf250: stur            d0, [x3, #0xf]
    // 0xbbf254: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbbf254: stur            xzr, [x3, #0x17]
    // 0xbbf258: ldur            x2, [fp, #-0x30]
    // 0xbbf25c: r1 = Function 'onPageRefresh':.
    //     0xbbf25c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xbbf260: ldr             x1, [x1, #0xa60]
    // 0xbbf264: r0 = AllocateClosure()
    //     0xbbf264: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbf268: mov             x1, x0
    // 0xbbf26c: ldur            x0, [fp, #-8]
    // 0xbbf270: StoreField: r0->field_1f = r1
    //     0xbbf270: stur            w1, [x0, #0x1f]
    // 0xbbf274: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbbf274: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbbf278: ldr             x1, [x1, #0xf58]
    // 0xbbf27c: StoreField: r0->field_2f = r1
    //     0xbbf27c: stur            w1, [x0, #0x2f]
    // 0xbbf280: d0 = 2.500000
    //     0xbbf280: fmov            d0, #2.50000000
    // 0xbbf284: StoreField: r0->field_3b = d0
    //     0xbbf284: stur            d0, [x0, #0x3b]
    // 0xbbf288: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbbf288: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbbf28c: ldr             x1, [x1, #0xa68]
    // 0xbbf290: StoreField: r0->field_47 = r1
    //     0xbbf290: stur            w1, [x0, #0x47]
    // 0xbbf294: d0 = 2.000000
    //     0xbbf294: fmov            d0, #2.00000000
    // 0xbbf298: StoreField: r0->field_4b = d0
    //     0xbbf298: stur            d0, [x0, #0x4b]
    // 0xbbf29c: r1 = Instance__IndicatorType
    //     0xbbf29c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbbf2a0: ldr             x1, [x1, #0xa70]
    // 0xbbf2a4: StoreField: r0->field_43 = r1
    //     0xbbf2a4: stur            w1, [x0, #0x43]
    // 0xbbf2a8: r1 = <FlexParentData>
    //     0xbbf2a8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbbf2ac: ldr             x1, [x1, #0x720]
    // 0xbbf2b0: r0 = Expanded()
    //     0xbbf2b0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbf2b4: mov             x3, x0
    // 0xbbf2b8: r0 = 1
    //     0xbbf2b8: movz            x0, #0x1
    // 0xbbf2bc: stur            x3, [fp, #-0x10]
    // 0xbbf2c0: StoreField: r3->field_13 = r0
    //     0xbbf2c0: stur            x0, [x3, #0x13]
    // 0xbbf2c4: r0 = Instance_FlexFit
    //     0xbbf2c4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbbf2c8: ldr             x0, [x0, #0x728]
    // 0xbbf2cc: StoreField: r3->field_1b = r0
    //     0xbbf2cc: stur            w0, [x3, #0x1b]
    // 0xbbf2d0: ldur            x0, [fp, #-8]
    // 0xbbf2d4: StoreField: r3->field_b = r0
    //     0xbbf2d4: stur            w0, [x3, #0xb]
    // 0xbbf2d8: r1 = Null
    //     0xbbf2d8: mov             x1, NULL
    // 0xbbf2dc: r2 = 2
    //     0xbbf2dc: movz            x2, #0x2
    // 0xbbf2e0: r0 = AllocateArray()
    //     0xbbf2e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbf2e4: mov             x2, x0
    // 0xbbf2e8: ldur            x0, [fp, #-0x10]
    // 0xbbf2ec: stur            x2, [fp, #-8]
    // 0xbbf2f0: StoreField: r2->field_f = r0
    //     0xbbf2f0: stur            w0, [x2, #0xf]
    // 0xbbf2f4: r1 = <Widget>
    //     0xbbf2f4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbf2f8: r0 = AllocateGrowableArray()
    //     0xbbf2f8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbf2fc: mov             x1, x0
    // 0xbbf300: ldur            x0, [fp, #-8]
    // 0xbbf304: stur            x1, [fp, #-0x10]
    // 0xbbf308: StoreField: r1->field_f = r0
    //     0xbbf308: stur            w0, [x1, #0xf]
    // 0xbbf30c: r0 = 2
    //     0xbbf30c: movz            x0, #0x2
    // 0xbbf310: StoreField: r1->field_b = r0
    //     0xbbf310: stur            w0, [x1, #0xb]
    // 0xbbf314: r0 = find()
    //     0xbbf314: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbf318: mov             x1, x0
    // 0xbbf31c: r0 = _adsVisibility()
    //     0xbbf31c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbf320: mov             x2, x0
    // 0xbbf324: r1 = Null
    //     0xbbf324: mov             x1, NULL
    // 0xbbf328: r0 = AdsConfig.fromJson()
    //     0xbbf328: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbf32c: LoadField: r1 = r0->field_f
    //     0xbbf32c: ldur            w1, [x0, #0xf]
    // 0xbbf330: DecompressPointer r1
    //     0xbbf330: add             x1, x1, HEAP, lsl #32
    // 0xbbf334: LoadField: r0 = r1->field_7
    //     0xbbf334: ldur            w0, [x1, #7]
    // 0xbbf338: DecompressPointer r0
    //     0xbbf338: add             x0, x0, HEAP, lsl #32
    // 0xbbf33c: tbnz            w0, #4, #0xbbf400
    // 0xbbf340: ldur            x1, [fp, #-0x10]
    // 0xbbf344: r0 = find()
    //     0xbbf344: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbf348: mov             x1, x0
    // 0xbbf34c: r0 = _adsVisibility()
    //     0xbbf34c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbf350: mov             x2, x0
    // 0xbbf354: r1 = Null
    //     0xbbf354: mov             x1, NULL
    // 0xbbf358: r0 = AdsConfig.fromJson()
    //     0xbbf358: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbf35c: LoadField: r1 = r0->field_f
    //     0xbbf35c: ldur            w1, [x0, #0xf]
    // 0xbbf360: DecompressPointer r1
    //     0xbbf360: add             x1, x1, HEAP, lsl #32
    // 0xbbf364: LoadField: r0 = r1->field_f
    //     0xbbf364: ldur            w0, [x1, #0xf]
    // 0xbbf368: DecompressPointer r0
    //     0xbbf368: add             x0, x0, HEAP, lsl #32
    // 0xbbf36c: stur            x0, [fp, #-8]
    // 0xbbf370: r0 = AdmobBannerWidget()
    //     0xbbf370: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xbbf374: mov             x2, x0
    // 0xbbf378: ldur            x0, [fp, #-8]
    // 0xbbf37c: stur            x2, [fp, #-0x20]
    // 0xbbf380: StoreField: r2->field_b = r0
    //     0xbbf380: stur            w0, [x2, #0xb]
    // 0xbbf384: ldur            x0, [fp, #-0x10]
    // 0xbbf388: LoadField: r1 = r0->field_b
    //     0xbbf388: ldur            w1, [x0, #0xb]
    // 0xbbf38c: LoadField: r3 = r0->field_f
    //     0xbbf38c: ldur            w3, [x0, #0xf]
    // 0xbbf390: DecompressPointer r3
    //     0xbbf390: add             x3, x3, HEAP, lsl #32
    // 0xbbf394: LoadField: r4 = r3->field_b
    //     0xbbf394: ldur            w4, [x3, #0xb]
    // 0xbbf398: r3 = LoadInt32Instr(r1)
    //     0xbbf398: sbfx            x3, x1, #1, #0x1f
    // 0xbbf39c: stur            x3, [fp, #-0x60]
    // 0xbbf3a0: r1 = LoadInt32Instr(r4)
    //     0xbbf3a0: sbfx            x1, x4, #1, #0x1f
    // 0xbbf3a4: cmp             x3, x1
    // 0xbbf3a8: b.ne            #0xbbf3b4
    // 0xbbf3ac: mov             x1, x0
    // 0xbbf3b0: r0 = _growToNextCapacity()
    //     0xbbf3b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbf3b4: ldur            x2, [fp, #-0x10]
    // 0xbbf3b8: ldur            x3, [fp, #-0x60]
    // 0xbbf3bc: add             x0, x3, #1
    // 0xbbf3c0: lsl             x1, x0, #1
    // 0xbbf3c4: StoreField: r2->field_b = r1
    //     0xbbf3c4: stur            w1, [x2, #0xb]
    // 0xbbf3c8: LoadField: r1 = r2->field_f
    //     0xbbf3c8: ldur            w1, [x2, #0xf]
    // 0xbbf3cc: DecompressPointer r1
    //     0xbbf3cc: add             x1, x1, HEAP, lsl #32
    // 0xbbf3d0: ldur            x0, [fp, #-0x20]
    // 0xbbf3d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbf3d4: add             x25, x1, x3, lsl #2
    //     0xbbf3d8: add             x25, x25, #0xf
    //     0xbbf3dc: str             w0, [x25]
    //     0xbbf3e0: tbz             w0, #0, #0xbbf3fc
    //     0xbbf3e4: ldurb           w16, [x1, #-1]
    //     0xbbf3e8: ldurb           w17, [x0, #-1]
    //     0xbbf3ec: and             x16, x17, x16, lsr #2
    //     0xbbf3f0: tst             x16, HEAP, lsr #32
    //     0xbbf3f4: b.eq            #0xbbf3fc
    //     0xbbf3f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbf3fc: b               #0xbbf404
    // 0xbbf400: ldur            x2, [fp, #-0x10]
    // 0xbbf404: ldur            x0, [fp, #-0x18]
    // 0xbbf408: r0 = Column()
    //     0xbbf408: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbf40c: mov             x1, x0
    // 0xbbf410: r0 = Instance_Axis
    //     0xbbf410: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbbf414: stur            x1, [fp, #-8]
    // 0xbbf418: StoreField: r1->field_f = r0
    //     0xbbf418: stur            w0, [x1, #0xf]
    // 0xbbf41c: r0 = Instance_MainAxisAlignment
    //     0xbbf41c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbf420: ldr             x0, [x0, #0x730]
    // 0xbbf424: StoreField: r1->field_13 = r0
    //     0xbbf424: stur            w0, [x1, #0x13]
    // 0xbbf428: r0 = Instance_MainAxisSize
    //     0xbbf428: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbbf42c: ldr             x0, [x0, #0x738]
    // 0xbbf430: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbf430: stur            w0, [x1, #0x17]
    // 0xbbf434: r0 = Instance_CrossAxisAlignment
    //     0xbbf434: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbbf438: ldr             x0, [x0, #0x740]
    // 0xbbf43c: StoreField: r1->field_1b = r0
    //     0xbbf43c: stur            w0, [x1, #0x1b]
    // 0xbbf440: r0 = Instance_VerticalDirection
    //     0xbbf440: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbf444: ldr             x0, [x0, #0x748]
    // 0xbbf448: StoreField: r1->field_23 = r0
    //     0xbbf448: stur            w0, [x1, #0x23]
    // 0xbbf44c: r0 = Instance_Clip
    //     0xbbf44c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbf450: ldr             x0, [x0, #0x750]
    // 0xbbf454: StoreField: r1->field_2b = r0
    //     0xbbf454: stur            w0, [x1, #0x2b]
    // 0xbbf458: StoreField: r1->field_2f = rZR
    //     0xbbf458: stur            xzr, [x1, #0x2f]
    // 0xbbf45c: ldur            x0, [fp, #-0x10]
    // 0xbbf460: StoreField: r1->field_b = r0
    //     0xbbf460: stur            w0, [x1, #0xb]
    // 0xbbf464: r0 = Scaffold()
    //     0xbbf464: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xbbf468: ldur            x1, [fp, #-0x18]
    // 0xbbf46c: StoreField: r0->field_13 = r1
    //     0xbbf46c: stur            w1, [x0, #0x13]
    // 0xbbf470: ldur            x1, [fp, #-8]
    // 0xbbf474: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbf474: stur            w1, [x0, #0x17]
    // 0xbbf478: r1 = Instance_AlignmentDirectional
    //     0xbbf478: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xbbf47c: ldr             x1, [x1, #0x758]
    // 0xbbf480: StoreField: r0->field_2b = r1
    //     0xbbf480: stur            w1, [x0, #0x2b]
    // 0xbbf484: r1 = true
    //     0xbbf484: add             x1, NULL, #0x20  ; true
    // 0xbbf488: StoreField: r0->field_53 = r1
    //     0xbbf488: stur            w1, [x0, #0x53]
    // 0xbbf48c: r2 = Instance_DragStartBehavior
    //     0xbbf48c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbbf490: StoreField: r0->field_57 = r2
    //     0xbbf490: stur            w2, [x0, #0x57]
    // 0xbbf494: r2 = false
    //     0xbbf494: add             x2, NULL, #0x30  ; false
    // 0xbbf498: StoreField: r0->field_b = r2
    //     0xbbf498: stur            w2, [x0, #0xb]
    // 0xbbf49c: StoreField: r0->field_f = r2
    //     0xbbf49c: stur            w2, [x0, #0xf]
    // 0xbbf4a0: StoreField: r0->field_5f = r1
    //     0xbbf4a0: stur            w1, [x0, #0x5f]
    // 0xbbf4a4: StoreField: r0->field_63 = r1
    //     0xbbf4a4: stur            w1, [x0, #0x63]
    // 0xbbf4a8: LeaveFrame
    //     0xbbf4a8: mov             SP, fp
    //     0xbbf4ac: ldp             fp, lr, [SP], #0x10
    // 0xbbf4b0: ret
    //     0xbbf4b0: ret             
    // 0xbbf4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf4b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf4b8: b               #0xbbeac4
    // 0xbbf4bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf4bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbf4c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf4c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbf4c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf4c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0xbbf570, size: 0x1f4
    // 0xbbf570: EnterFrame
    //     0xbbf570: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf574: mov             fp, SP
    // 0xbbf578: AllocStack(0x40)
    //     0xbbf578: sub             SP, SP, #0x40
    // 0xbbf57c: SetupParameters()
    //     0xbbf57c: ldr             x0, [fp, #0x10]
    //     0xbbf580: ldur            w2, [x0, #0x17]
    //     0xbbf584: add             x2, x2, HEAP, lsl #32
    //     0xbbf588: stur            x2, [fp, #-0x10]
    // 0xbbf58c: CheckStackOverflow
    //     0xbbf58c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf590: cmp             SP, x16
    //     0xbbf594: b.ls            #0xbbf73c
    // 0xbbf598: LoadField: r0 = r2->field_f
    //     0xbbf598: ldur            w0, [x2, #0xf]
    // 0xbbf59c: DecompressPointer r0
    //     0xbbf59c: add             x0, x0, HEAP, lsl #32
    // 0xbbf5a0: stur            x0, [fp, #-8]
    // 0xbbf5a4: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbbf5a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbf5a8: ldr             x0, [x0, #0x26d0]
    //     0xbbf5ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbf5b0: cmp             w0, w16
    //     0xbbf5b4: b.ne            #0xbbf5c4
    //     0xbbf5b8: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbbf5bc: ldr             x2, [x2, #0xb90]
    //     0xbbf5c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbf5c4: mov             x1, x0
    // 0xbbf5c8: ldur            x2, [fp, #-8]
    // 0xbbf5cc: stur            x0, [fp, #-0x18]
    // 0xbbf5d0: r0 = []()
    //     0xbbf5d0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbf5d4: mov             x3, x0
    // 0xbbf5d8: ldur            x0, [fp, #-8]
    // 0xbbf5dc: stur            x3, [fp, #-0x20]
    // 0xbbf5e0: LoadField: r2 = r0->field_b
    //     0xbbf5e0: ldur            w2, [x0, #0xb]
    // 0xbbf5e4: DecompressPointer r2
    //     0xbbf5e4: add             x2, x2, HEAP, lsl #32
    // 0xbbf5e8: mov             x0, x3
    // 0xbbf5ec: r1 = Null
    //     0xbbf5ec: mov             x1, NULL
    // 0xbbf5f0: cmp             w2, NULL
    // 0xbbf5f4: b.eq            #0xbbf618
    // 0xbbf5f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbf5f8: ldur            w4, [x2, #0x17]
    // 0xbbf5fc: DecompressPointer r4
    //     0xbbf5fc: add             x4, x4, HEAP, lsl #32
    // 0xbbf600: r8 = X0 bound GetLifeCycleBase?
    //     0xbbf600: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbf604: ldr             x8, [x8, #0xb98]
    // 0xbbf608: LoadField: r9 = r4->field_7
    //     0xbbf608: ldur            x9, [x4, #7]
    // 0xbbf60c: r3 = Null
    //     0xbbf60c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41280] Null
    //     0xbbf610: ldr             x3, [x3, #0x280]
    // 0xbbf614: blr             x9
    // 0xbbf618: ldur            x1, [fp, #-0x20]
    // 0xbbf61c: r0 = itemsCount()
    //     0xbbf61c: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbbf620: mov             x3, x0
    // 0xbbf624: ldur            x0, [fp, #-0x10]
    // 0xbbf628: stur            x3, [fp, #-0x28]
    // 0xbbf62c: LoadField: r4 = r0->field_f
    //     0xbbf62c: ldur            w4, [x0, #0xf]
    // 0xbbf630: DecompressPointer r4
    //     0xbbf630: add             x4, x4, HEAP, lsl #32
    // 0xbbf634: ldur            x1, [fp, #-0x18]
    // 0xbbf638: mov             x2, x4
    // 0xbbf63c: stur            x4, [fp, #-8]
    // 0xbbf640: r0 = []()
    //     0xbbf640: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbf644: mov             x3, x0
    // 0xbbf648: ldur            x0, [fp, #-8]
    // 0xbbf64c: stur            x3, [fp, #-0x18]
    // 0xbbf650: LoadField: r2 = r0->field_b
    //     0xbbf650: ldur            w2, [x0, #0xb]
    // 0xbbf654: DecompressPointer r2
    //     0xbbf654: add             x2, x2, HEAP, lsl #32
    // 0xbbf658: mov             x0, x3
    // 0xbbf65c: r1 = Null
    //     0xbbf65c: mov             x1, NULL
    // 0xbbf660: cmp             w2, NULL
    // 0xbbf664: b.eq            #0xbbf688
    // 0xbbf668: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbf668: ldur            w4, [x2, #0x17]
    // 0xbbf66c: DecompressPointer r4
    //     0xbbf66c: add             x4, x4, HEAP, lsl #32
    // 0xbbf670: r8 = X0 bound GetLifeCycleBase?
    //     0xbbf670: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbf674: ldr             x8, [x8, #0xb98]
    // 0xbbf678: LoadField: r9 = r4->field_7
    //     0xbbf678: ldur            x9, [x4, #7]
    // 0xbbf67c: r3 = Null
    //     0xbbf67c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41290] Null
    //     0xbbf680: ldr             x3, [x3, #0x290]
    // 0xbbf684: blr             x9
    // 0xbbf688: ldur            x1, [fp, #-0x18]
    // 0xbbf68c: r0 = itemsCount()
    //     0xbbf68c: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbbf690: scvtf           d0, x0
    // 0xbbf694: d1 = 10.000000
    //     0xbbf694: fmov            d1, #10.00000000
    // 0xbbf698: fdiv            d2, d0, d1
    // 0xbbf69c: fcmp            d2, d2
    // 0xbbf6a0: b.vs            #0xbbf744
    // 0xbbf6a4: fcvtms          x0, d2
    // 0xbbf6a8: asr             x16, x0, #0x1e
    // 0xbbf6ac: cmp             x16, x0, asr #63
    // 0xbbf6b0: b.ne            #0xbbf744
    // 0xbbf6b4: lsl             x0, x0, #1
    // 0xbbf6b8: r1 = LoadInt32Instr(r0)
    //     0xbbf6b8: sbfx            x1, x0, #1, #0x1f
    //     0xbbf6bc: tbz             w0, #0, #0xbbf6c4
    //     0xbbf6c0: ldur            x1, [x0, #7]
    // 0xbbf6c4: ldur            x0, [fp, #-0x28]
    // 0xbbf6c8: add             x3, x0, x1
    // 0xbbf6cc: stur            x3, [fp, #-0x30]
    // 0xbbf6d0: r1 = Function '<anonymous closure>':.
    //     0xbbf6d0: add             x1, PP, #0x41, lsl #12  ; [pp+0x412a0] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbbf6d4: ldr             x1, [x1, #0x2a0]
    // 0xbbf6d8: r2 = Null
    //     0xbbf6d8: mov             x2, NULL
    // 0xbbf6dc: r0 = AllocateClosure()
    //     0xbbf6dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbf6e0: ldur            x2, [fp, #-0x10]
    // 0xbbf6e4: r1 = Function '<anonymous closure>':.
    //     0xbbf6e4: add             x1, PP, #0x41, lsl #12  ; [pp+0x412a8] AnonymousClosure: (0xbbf764), in [package:nuonline/app/modules/article/article_author/views/article_author_view.dart] ArticleAuthorView::build (0xbbeaa4)
    //     0xbbf6e8: ldr             x1, [x1, #0x2a8]
    // 0xbbf6ec: stur            x0, [fp, #-8]
    // 0xbbf6f0: r0 = AllocateClosure()
    //     0xbbf6f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbf6f4: stur            x0, [fp, #-0x10]
    // 0xbbf6f8: r0 = ListView()
    //     0xbbf6f8: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbbf6fc: stur            x0, [fp, #-0x18]
    // 0xbbf700: r16 = true
    //     0xbbf700: add             x16, NULL, #0x20  ; true
    // 0xbbf704: r30 = Instance_NeverScrollableScrollPhysics
    //     0xbbf704: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xbbf708: ldr             lr, [lr, #0x290]
    // 0xbbf70c: stp             lr, x16, [SP]
    // 0xbbf710: mov             x1, x0
    // 0xbbf714: ldur            x2, [fp, #-0x10]
    // 0xbbf718: ldur            x3, [fp, #-0x30]
    // 0xbbf71c: ldur            x5, [fp, #-8]
    // 0xbbf720: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xbbf720: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbbf724: ldr             x4, [x4, #0x298]
    // 0xbbf728: r0 = ListView.separated()
    //     0xbbf728: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbbf72c: ldur            x0, [fp, #-0x18]
    // 0xbbf730: LeaveFrame
    //     0xbbf730: mov             SP, fp
    //     0xbbf734: ldp             fp, lr, [SP], #0x10
    // 0xbbf738: ret
    //     0xbbf738: ret             
    // 0xbbf73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf740: b               #0xbbf598
    // 0xbbf744: SaveReg d2
    //     0xbbf744: str             q2, [SP, #-0x10]!
    // 0xbbf748: d0 = 0.000000
    //     0xbbf748: fmov            d0, d2
    // 0xbbf74c: r0 = 68
    //     0xbbf74c: movz            x0, #0x44
    // 0xbbf750: r30 = DoubleToIntegerStub
    //     0xbbf750: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbbf754: LoadField: r30 = r30->field_7
    //     0xbbf754: ldur            lr, [lr, #7]
    // 0xbbf758: blr             lr
    // 0xbbf75c: RestoreReg d2
    //     0xbbf75c: ldr             q2, [SP], #0x10
    // 0xbbf760: b               #0xbbf6b8
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbf764, size: 0x200
    // 0xbbf764: EnterFrame
    //     0xbbf764: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf768: mov             fp, SP
    // 0xbbf76c: AllocStack(0x20)
    //     0xbbf76c: sub             SP, SP, #0x20
    // 0xbbf770: SetupParameters()
    //     0xbbf770: fmov            d0, #10.00000000
    //     0xbbf774: ldr             x0, [fp, #0x20]
    //     0xbbf778: ldur            w1, [x0, #0x17]
    //     0xbbf77c: add             x1, x1, HEAP, lsl #32
    // 0xbbf770: d0 = 10.000000
    // 0xbbf780: CheckStackOverflow
    //     0xbbf780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf784: cmp             SP, x16
    //     0xbbf788: b.ls            #0xbbf92c
    // 0xbbf78c: ldr             x0, [fp, #0x10]
    // 0xbbf790: r2 = LoadInt32Instr(r0)
    //     0xbbf790: sbfx            x2, x0, #1, #0x1f
    //     0xbbf794: tbz             w0, #0, #0xbbf79c
    //     0xbbf798: ldur            x2, [x0, #7]
    // 0xbbf79c: stur            x2, [fp, #-0x18]
    // 0xbbf7a0: add             x0, x2, #5
    // 0xbbf7a4: scvtf           d1, x0
    // 0xbbf7a8: fdiv            d2, d1, d0
    // 0xbbf7ac: fcmp            d2, d2
    // 0xbbf7b0: b.vs            #0xbbf934
    // 0xbbf7b4: fcvtms          x0, d2
    // 0xbbf7b8: asr             x16, x0, #0x1e
    // 0xbbf7bc: cmp             x16, x0, asr #63
    // 0xbbf7c0: b.ne            #0xbbf934
    // 0xbbf7c4: lsl             x0, x0, #1
    // 0xbbf7c8: r3 = LoadInt32Instr(r0)
    //     0xbbf7c8: sbfx            x3, x0, #1, #0x1f
    //     0xbbf7cc: tbz             w0, #0, #0xbbf7d4
    //     0xbbf7d0: ldur            x3, [x0, #7]
    // 0xbbf7d4: sub             x0, x2, x3
    // 0xbbf7d8: stur            x0, [fp, #-0x10]
    // 0xbbf7dc: LoadField: r3 = r1->field_f
    //     0xbbf7dc: ldur            w3, [x1, #0xf]
    // 0xbbf7e0: DecompressPointer r3
    //     0xbbf7e0: add             x3, x3, HEAP, lsl #32
    // 0xbbf7e4: stur            x3, [fp, #-8]
    // 0xbbf7e8: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbbf7e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbf7ec: ldr             x0, [x0, #0x26d0]
    //     0xbbf7f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbf7f4: cmp             w0, w16
    //     0xbbf7f8: b.ne            #0xbbf808
    //     0xbbf7fc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbbf800: ldr             x2, [x2, #0xb90]
    //     0xbbf804: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbf808: mov             x1, x0
    // 0xbbf80c: ldur            x2, [fp, #-8]
    // 0xbbf810: r0 = []()
    //     0xbbf810: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbf814: mov             x3, x0
    // 0xbbf818: ldur            x0, [fp, #-8]
    // 0xbbf81c: stur            x3, [fp, #-0x20]
    // 0xbbf820: LoadField: r2 = r0->field_b
    //     0xbbf820: ldur            w2, [x0, #0xb]
    // 0xbbf824: DecompressPointer r2
    //     0xbbf824: add             x2, x2, HEAP, lsl #32
    // 0xbbf828: mov             x0, x3
    // 0xbbf82c: r1 = Null
    //     0xbbf82c: mov             x1, NULL
    // 0xbbf830: cmp             w2, NULL
    // 0xbbf834: b.eq            #0xbbf858
    // 0xbbf838: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbf838: ldur            w4, [x2, #0x17]
    // 0xbbf83c: DecompressPointer r4
    //     0xbbf83c: add             x4, x4, HEAP, lsl #32
    // 0xbbf840: r8 = X0 bound GetLifeCycleBase?
    //     0xbbf840: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbf844: ldr             x8, [x8, #0xb98]
    // 0xbbf848: LoadField: r9 = r4->field_7
    //     0xbbf848: ldur            x9, [x4, #7]
    // 0xbbf84c: r3 = Null
    //     0xbbf84c: add             x3, PP, #0x41, lsl #12  ; [pp+0x412b0] Null
    //     0xbbf850: ldr             x3, [x3, #0x2b0]
    // 0xbbf854: blr             x9
    // 0xbbf858: ldur            x1, [fp, #-0x20]
    // 0xbbf85c: ldur            x2, [fp, #-0x10]
    // 0xbbf860: r0 = find()
    //     0xbbf860: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xbbf864: stur            x0, [fp, #-8]
    // 0xbbf868: cmp             w0, NULL
    // 0xbbf86c: b.ne            #0xbbf884
    // 0xbbf870: r0 = Instance_NArticleListTile
    //     0xbbf870: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xbbf874: ldr             x0, [x0, #0xa90]
    // 0xbbf878: LeaveFrame
    //     0xbbf878: mov             SP, fp
    //     0xbbf87c: ldp             fp, lr, [SP], #0x10
    // 0xbbf880: ret
    //     0xbbf880: ret             
    // 0xbbf884: ldur            x1, [fp, #-0x18]
    // 0xbbf888: r2 = 10
    //     0xbbf888: movz            x2, #0xa
    // 0xbbf88c: sdiv            x4, x1, x2
    // 0xbbf890: msub            x3, x4, x2, x1
    // 0xbbf894: cmp             x3, xzr
    // 0xbbf898: b.lt            #0xbbf95c
    // 0xbbf89c: cmp             x3, #4
    // 0xbbf8a0: b.ne            #0xbbf90c
    // 0xbbf8a4: r0 = find()
    //     0xbbf8a4: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbf8a8: mov             x1, x0
    // 0xbbf8ac: r0 = _adsVisibility()
    //     0xbbf8ac: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbf8b0: mov             x2, x0
    // 0xbbf8b4: r1 = Null
    //     0xbbf8b4: mov             x1, NULL
    // 0xbbf8b8: r0 = AdsConfig.fromJson()
    //     0xbbf8b8: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbf8bc: LoadField: r1 = r0->field_b
    //     0xbbf8bc: ldur            w1, [x0, #0xb]
    // 0xbbf8c0: DecompressPointer r1
    //     0xbbf8c0: add             x1, x1, HEAP, lsl #32
    // 0xbbf8c4: LoadField: r0 = r1->field_7
    //     0xbbf8c4: ldur            w0, [x1, #7]
    // 0xbbf8c8: DecompressPointer r0
    //     0xbbf8c8: add             x0, x0, HEAP, lsl #32
    // 0xbbf8cc: tbnz            w0, #4, #0xbbf8f8
    // 0xbbf8d0: r0 = find()
    //     0xbbf8d0: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbf8d4: mov             x1, x0
    // 0xbbf8d8: r0 = _adsVisibility()
    //     0xbbf8d8: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbf8dc: mov             x2, x0
    // 0xbbf8e0: r1 = Null
    //     0xbbf8e0: mov             x1, NULL
    // 0xbbf8e4: r0 = AdsConfig.fromJson()
    //     0xbbf8e4: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbf8e8: r0 = AdmobArticleNativeWidget()
    //     0xbbf8e8: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xbbf8ec: LeaveFrame
    //     0xbbf8ec: mov             SP, fp
    //     0xbbf8f0: ldp             fp, lr, [SP], #0x10
    // 0xbbf8f4: ret
    //     0xbbf8f4: ret             
    // 0xbbf8f8: r0 = Instance_SizedBox
    //     0xbbf8f8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xbbf8fc: ldr             x0, [x0, #0xc40]
    // 0xbbf900: LeaveFrame
    //     0xbbf900: mov             SP, fp
    //     0xbbf904: ldp             fp, lr, [SP], #0x10
    // 0xbbf908: ret
    //     0xbbf908: ret             
    // 0xbbf90c: r0 = ArticleItem()
    //     0xbbf90c: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xbbf910: ldur            x1, [fp, #-8]
    // 0xbbf914: StoreField: r0->field_b = r1
    //     0xbbf914: stur            w1, [x0, #0xb]
    // 0xbbf918: r1 = false
    //     0xbbf918: add             x1, NULL, #0x30  ; false
    // 0xbbf91c: StoreField: r0->field_13 = r1
    //     0xbbf91c: stur            w1, [x0, #0x13]
    // 0xbbf920: LeaveFrame
    //     0xbbf920: mov             SP, fp
    //     0xbbf924: ldp             fp, lr, [SP], #0x10
    // 0xbbf928: ret
    //     0xbbf928: ret             
    // 0xbbf92c: r0 = StackOverflowSharedWithFPURegs()
    //     0xbbf92c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbbf930: b               #0xbbf78c
    // 0xbbf934: SaveReg d2
    //     0xbbf934: str             q2, [SP, #-0x10]!
    // 0xbbf938: stp             x1, x2, [SP, #-0x10]!
    // 0xbbf93c: d0 = 0.000000
    //     0xbbf93c: fmov            d0, d2
    // 0xbbf940: r0 = 68
    //     0xbbf940: movz            x0, #0x44
    // 0xbbf944: r30 = DoubleToIntegerStub
    //     0xbbf944: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbbf948: LoadField: r30 = r30->field_7
    //     0xbbf948: ldur            lr, [lr, #7]
    // 0xbbf94c: blr             lr
    // 0xbbf950: ldp             x1, x2, [SP], #0x10
    // 0xbbf954: RestoreReg d2
    //     0xbbf954: ldr             q2, [SP], #0x10
    // 0xbbf958: b               #0xbbf7c8
    // 0xbbf95c: add             x3, x3, x2
    // 0xbbf960: b               #0xbbf89c
  }
}
