// lib: , url: package:nuonline/app/modules/article/article_bookmark/bindings/article_bookmark_binding.dart

// class id: 1050117, size: 0x8
class :: {
}

// class id: 2198, size: 0x8, field offset: 0x8
class ArticleBookmarkBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80cd8c, size: 0x70
    // 0x80cd8c: EnterFrame
    //     0x80cd8c: stp             fp, lr, [SP, #-0x10]!
    //     0x80cd90: mov             fp, SP
    // 0x80cd94: AllocStack(0x10)
    //     0x80cd94: sub             SP, SP, #0x10
    // 0x80cd98: CheckStackOverflow
    //     0x80cd98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80cd9c: cmp             SP, x16
    //     0x80cda0: b.ls            #0x80cdf4
    // 0x80cda4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80cda4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80cda8: ldr             x0, [x0, #0x2670]
    //     0x80cdac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80cdb0: cmp             w0, w16
    //     0x80cdb4: b.ne            #0x80cdc0
    //     0x80cdb8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80cdbc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80cdc0: r1 = Function '<anonymous closure>':.
    //     0x80cdc0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36aa0] AnonymousClosure: (0x80cdfc), in [package:nuonline/app/modules/article/article_bookmark/bindings/article_bookmark_binding.dart] ArticleBookmarkBinding::dependencies (0x80cd8c)
    //     0x80cdc4: ldr             x1, [x1, #0xaa0]
    // 0x80cdc8: r2 = Null
    //     0x80cdc8: mov             x2, NULL
    // 0x80cdcc: r0 = AllocateClosure()
    //     0x80cdcc: bl              #0xec1630  ; AllocateClosureStub
    // 0x80cdd0: r16 = <ArticleBookmarkController>
    //     0x80cdd0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d20] TypeArguments: <ArticleBookmarkController>
    //     0x80cdd4: ldr             x16, [x16, #0xd20]
    // 0x80cdd8: stp             x0, x16, [SP]
    // 0x80cddc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80cddc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80cde0: r0 = Inst.lazyPut()
    //     0x80cde0: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80cde4: r0 = Null
    //     0x80cde4: mov             x0, NULL
    // 0x80cde8: LeaveFrame
    //     0x80cde8: mov             SP, fp
    //     0x80cdec: ldp             fp, lr, [SP], #0x10
    // 0x80cdf0: ret
    //     0x80cdf0: ret             
    // 0x80cdf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80cdf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80cdf8: b               #0x80cda4
  }
  [closure] ArticleBookmarkController <anonymous closure>(dynamic) {
    // ** addr: 0x80cdfc, size: 0x88
    // 0x80cdfc: EnterFrame
    //     0x80cdfc: stp             fp, lr, [SP, #-0x10]!
    //     0x80ce00: mov             fp, SP
    // 0x80ce04: AllocStack(0x20)
    //     0x80ce04: sub             SP, SP, #0x20
    // 0x80ce08: CheckStackOverflow
    //     0x80ce08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80ce0c: cmp             SP, x16
    //     0x80ce10: b.ls            #0x80ce7c
    // 0x80ce14: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80ce14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80ce18: ldr             x0, [x0, #0x2670]
    //     0x80ce1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80ce20: cmp             w0, w16
    //     0x80ce24: b.ne            #0x80ce30
    //     0x80ce28: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80ce2c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80ce30: r16 = <ArticleRepository>
    //     0x80ce30: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80ce34: ldr             x16, [x16, #0x98]
    // 0x80ce38: r30 = "article_repo"
    //     0x80ce38: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80ce3c: ldr             lr, [lr, #0xa0]
    // 0x80ce40: stp             lr, x16, [SP]
    // 0x80ce44: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80ce44: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80ce48: r0 = Inst.find()
    //     0x80ce48: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80ce4c: stur            x0, [fp, #-8]
    // 0x80ce50: r0 = ArticleBookmarkController()
    //     0x80ce50: bl              #0x80ce84  ; AllocateArticleBookmarkControllerStub -> ArticleBookmarkController (size=0x2c)
    // 0x80ce54: mov             x2, x0
    // 0x80ce58: ldur            x0, [fp, #-8]
    // 0x80ce5c: stur            x2, [fp, #-0x10]
    // 0x80ce60: StoreField: r2->field_27 = r0
    //     0x80ce60: stur            w0, [x2, #0x27]
    // 0x80ce64: mov             x1, x2
    // 0x80ce68: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80ce68: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80ce6c: ldur            x0, [fp, #-0x10]
    // 0x80ce70: LeaveFrame
    //     0x80ce70: mov             SP, fp
    //     0x80ce74: ldp             fp, lr, [SP], #0x10
    // 0x80ce78: ret
    //     0x80ce78: ret             
    // 0x80ce7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80ce7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80ce80: b               #0x80ce14
  }
}
