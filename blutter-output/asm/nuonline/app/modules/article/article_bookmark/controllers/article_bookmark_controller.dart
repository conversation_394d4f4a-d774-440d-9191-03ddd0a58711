// lib: , url: package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart

// class id: 1050118, size: 0x8
class :: {
}

// class id: 2041, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _ArticleBookmarkController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x8aa078, size: 0x90
    // 0x8aa078: EnterFrame
    //     0x8aa078: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa07c: mov             fp, SP
    // 0x8aa080: mov             x16, x2
    // 0x8aa084: mov             x2, x1
    // 0x8aa088: mov             x1, x16
    // 0x8aa08c: mov             x0, x3
    // 0x8aa090: CheckStackOverflow
    //     0x8aa090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa094: cmp             SP, x16
    //     0x8aa098: b.ls            #0x8aa100
    // 0x8aa09c: StoreField: r2->field_23 = r0
    //     0x8aa09c: stur            w0, [x2, #0x23]
    //     0x8aa0a0: ldurb           w16, [x2, #-1]
    //     0x8aa0a4: ldurb           w17, [x0, #-1]
    //     0x8aa0a8: and             x16, x17, x16, lsr #2
    //     0x8aa0ac: tst             x16, HEAP, lsr #32
    //     0x8aa0b0: b.eq            #0x8aa0b8
    //     0x8aa0b4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8aa0b8: LoadField: r0 = r2->field_1f
    //     0x8aa0b8: ldur            w0, [x2, #0x1f]
    // 0x8aa0bc: DecompressPointer r0
    //     0x8aa0bc: add             x0, x0, HEAP, lsl #32
    // 0x8aa0c0: cmp             w1, w0
    // 0x8aa0c4: b.eq            #0x8aa0e8
    // 0x8aa0c8: mov             x0, x1
    // 0x8aa0cc: StoreField: r2->field_1f = r0
    //     0x8aa0cc: stur            w0, [x2, #0x1f]
    //     0x8aa0d0: ldurb           w16, [x2, #-1]
    //     0x8aa0d4: ldurb           w17, [x0, #-1]
    //     0x8aa0d8: and             x16, x17, x16, lsr #2
    //     0x8aa0dc: tst             x16, HEAP, lsr #32
    //     0x8aa0e0: b.eq            #0x8aa0e8
    //     0x8aa0e4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8aa0e8: mov             x1, x2
    // 0x8aa0ec: r0 = _notifyUpdate()
    //     0x8aa0ec: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x8aa0f0: r0 = Null
    //     0x8aa0f0: mov             x0, NULL
    // 0x8aa0f4: LeaveFrame
    //     0x8aa0f4: mov             SP, fp
    //     0x8aa0f8: ldp             fp, lr, [SP], #0x10
    // 0x8aa0fc: ret
    //     0x8aa0fc: ret             
    // 0x8aa100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa104: b               #0x8aa09c
  }
}

// class id: 2042, size: 0x2c, field offset: 0x28
class ArticleBookmarkController extends _ArticleBookmarkController&GetxController&StateMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8a9f90, size: 0x48
    // 0x8a9f90: EnterFrame
    //     0x8a9f90: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9f94: mov             fp, SP
    // 0x8a9f98: AllocStack(0x8)
    //     0x8a9f98: sub             SP, SP, #8
    // 0x8a9f9c: SetupParameters(ArticleBookmarkController this /* r1 => r0, fp-0x8 */)
    //     0x8a9f9c: mov             x0, x1
    //     0x8a9fa0: stur            x1, [fp, #-8]
    // 0x8a9fa4: CheckStackOverflow
    //     0x8a9fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9fa8: cmp             SP, x16
    //     0x8a9fac: b.ls            #0x8a9fd0
    // 0x8a9fb0: mov             x1, x0
    // 0x8a9fb4: r0 = onInit()
    //     0x8a9fb4: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8a9fb8: ldur            x1, [fp, #-8]
    // 0x8a9fbc: r0 = _loadBookmarks()
    //     0x8a9fbc: bl              #0x8a9fd8  ; [package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart] ArticleBookmarkController::_loadBookmarks
    // 0x8a9fc0: r0 = Null
    //     0x8a9fc0: mov             x0, NULL
    // 0x8a9fc4: LeaveFrame
    //     0x8a9fc4: mov             SP, fp
    //     0x8a9fc8: ldp             fp, lr, [SP], #0x10
    // 0x8a9fcc: ret
    //     0x8a9fcc: ret             
    // 0x8a9fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9fd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9fd4: b               #0x8a9fb0
  }
  _ _loadBookmarks(/* No info */) {
    // ** addr: 0x8a9fd8, size: 0xa0
    // 0x8a9fd8: EnterFrame
    //     0x8a9fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9fdc: mov             fp, SP
    // 0x8a9fe0: AllocStack(0x10)
    //     0x8a9fe0: sub             SP, SP, #0x10
    // 0x8a9fe4: SetupParameters(ArticleBookmarkController this /* r1 => r0, fp-0x8 */)
    //     0x8a9fe4: mov             x0, x1
    //     0x8a9fe8: stur            x1, [fp, #-8]
    // 0x8a9fec: CheckStackOverflow
    //     0x8a9fec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9ff0: cmp             SP, x16
    //     0x8a9ff4: b.ls            #0x8aa070
    // 0x8a9ff8: LoadField: r1 = r0->field_27
    //     0x8a9ff8: ldur            w1, [x0, #0x27]
    // 0x8a9ffc: DecompressPointer r1
    //     0x8a9ffc: add             x1, x1, HEAP, lsl #32
    // 0x8aa000: r0 = findAllFromBookmark()
    //     0x8aa000: bl              #0x8aa108  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAllFromBookmark
    // 0x8aa004: stur            x0, [fp, #-0x10]
    // 0x8aa008: LoadField: r1 = r0->field_b
    //     0x8aa008: ldur            w1, [x0, #0xb]
    // 0x8aa00c: cbnz            w1, #0x8aa034
    // 0x8aa010: r0 = RxStatus()
    //     0x8aa010: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8aa014: mov             x1, x0
    // 0x8aa018: r0 = true
    //     0x8aa018: add             x0, NULL, #0x20  ; true
    // 0x8aa01c: StoreField: r1->field_f = r0
    //     0x8aa01c: stur            w0, [x1, #0xf]
    // 0x8aa020: r0 = false
    //     0x8aa020: add             x0, NULL, #0x30  ; false
    // 0x8aa024: StoreField: r1->field_7 = r0
    //     0x8aa024: stur            w0, [x1, #7]
    // 0x8aa028: StoreField: r1->field_b = r0
    //     0x8aa028: stur            w0, [x1, #0xb]
    // 0x8aa02c: mov             x3, x1
    // 0x8aa030: b               #0x8aa054
    // 0x8aa034: r0 = false
    //     0x8aa034: add             x0, NULL, #0x30  ; false
    // 0x8aa038: r0 = RxStatus()
    //     0x8aa038: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8aa03c: mov             x1, x0
    // 0x8aa040: r0 = false
    //     0x8aa040: add             x0, NULL, #0x30  ; false
    // 0x8aa044: StoreField: r1->field_f = r0
    //     0x8aa044: stur            w0, [x1, #0xf]
    // 0x8aa048: StoreField: r1->field_7 = r0
    //     0x8aa048: stur            w0, [x1, #7]
    // 0x8aa04c: StoreField: r1->field_b = r0
    //     0x8aa04c: stur            w0, [x1, #0xb]
    // 0x8aa050: mov             x3, x1
    // 0x8aa054: ldur            x1, [fp, #-8]
    // 0x8aa058: ldur            x2, [fp, #-0x10]
    // 0x8aa05c: r0 = change()
    //     0x8aa05c: bl              #0x8aa078  ; [package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart] _ArticleBookmarkController&GetxController&StateMixin::change
    // 0x8aa060: r0 = Null
    //     0x8aa060: mov             x0, NULL
    // 0x8aa064: LeaveFrame
    //     0x8aa064: mov             SP, fp
    //     0x8aa068: ldp             fp, lr, [SP], #0x10
    // 0x8aa06c: ret
    //     0x8aa06c: ret             
    // 0x8aa070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa070: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa074: b               #0x8a9ff8
  }
  _ onPressed(/* No info */) async {
    // ** addr: 0xacfdf8, size: 0xcc
    // 0xacfdf8: EnterFrame
    //     0xacfdf8: stp             fp, lr, [SP, #-0x10]!
    //     0xacfdfc: mov             fp, SP
    // 0xacfe00: AllocStack(0x38)
    //     0xacfe00: sub             SP, SP, #0x38
    // 0xacfe04: SetupParameters(ArticleBookmarkController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xacfe04: stur            NULL, [fp, #-8]
    //     0xacfe08: stur            x1, [fp, #-0x10]
    //     0xacfe0c: stur            x2, [fp, #-0x18]
    // 0xacfe10: CheckStackOverflow
    //     0xacfe10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfe14: cmp             SP, x16
    //     0xacfe18: b.ls            #0xacfebc
    // 0xacfe1c: InitAsync() -> Future<void?>
    //     0xacfe1c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xacfe20: bl              #0x661298  ; InitAsyncStub
    // 0xacfe24: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xacfe24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xacfe28: ldr             x0, [x0, #0x2670]
    //     0xacfe2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xacfe30: cmp             w0, w16
    //     0xacfe34: b.ne            #0xacfe40
    //     0xacfe38: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xacfe3c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xacfe40: r1 = Null
    //     0xacfe40: mov             x1, NULL
    // 0xacfe44: r2 = 4
    //     0xacfe44: movz            x2, #0x4
    // 0xacfe48: r0 = AllocateArray()
    //     0xacfe48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xacfe4c: mov             x2, x0
    // 0xacfe50: r16 = "id"
    //     0xacfe50: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xacfe54: ldr             x16, [x16, #0x740]
    // 0xacfe58: StoreField: r2->field_f = r16
    //     0xacfe58: stur            w16, [x2, #0xf]
    // 0xacfe5c: ldur            x3, [fp, #-0x18]
    // 0xacfe60: r0 = BoxInt64Instr(r3)
    //     0xacfe60: sbfiz           x0, x3, #1, #0x1f
    //     0xacfe64: cmp             x3, x0, asr #1
    //     0xacfe68: b.eq            #0xacfe74
    //     0xacfe6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xacfe70: stur            x3, [x0, #7]
    // 0xacfe74: StoreField: r2->field_13 = r0
    //     0xacfe74: stur            w0, [x2, #0x13]
    // 0xacfe78: r16 = <String, int>
    //     0xacfe78: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xacfe7c: stp             x2, x16, [SP]
    // 0xacfe80: r0 = Map._fromLiteral()
    //     0xacfe80: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xacfe84: r16 = "/article/article-detail"
    //     0xacfe84: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xacfe88: ldr             x16, [x16, #0x3d0]
    // 0xacfe8c: stp             x16, NULL, [SP, #8]
    // 0xacfe90: str             x0, [SP]
    // 0xacfe94: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xacfe94: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xacfe98: ldr             x4, [x4, #0x478]
    // 0xacfe9c: r0 = GetNavigation.toNamed()
    //     0xacfe9c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xacfea0: mov             x1, x0
    // 0xacfea4: stur            x1, [fp, #-0x20]
    // 0xacfea8: r0 = Await()
    //     0xacfea8: bl              #0x661044  ; AwaitStub
    // 0xacfeac: ldur            x1, [fp, #-0x10]
    // 0xacfeb0: r0 = _loadBookmarks()
    //     0xacfeb0: bl              #0x8a9fd8  ; [package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart] ArticleBookmarkController::_loadBookmarks
    // 0xacfeb4: r0 = Null
    //     0xacfeb4: mov             x0, NULL
    // 0xacfeb8: r0 = ReturnAsyncNotFuture()
    //     0xacfeb8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xacfebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacfebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacfec0: b               #0xacfe1c
  }
}
