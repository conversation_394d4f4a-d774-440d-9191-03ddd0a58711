// lib: , url: package:nuonline/app/modules/article/article_bookmark/views/article_bookmark_view.dart

// class id: 1050119, size: 0x8
class :: {
}

// class id: 5311, size: 0x14, field offset: 0x14
class ArticleBookmarkView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaba8e8, size: 0x164
    // 0xaba8e8: EnterFrame
    //     0xaba8e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaba8ec: mov             fp, SP
    // 0xaba8f0: AllocStack(0x58)
    //     0xaba8f0: sub             SP, SP, #0x58
    // 0xaba8f4: SetupParameters(ArticleBookmarkView this /* r1 => r1, fp-0x8 */)
    //     0xaba8f4: stur            x1, [fp, #-8]
    // 0xaba8f8: CheckStackOverflow
    //     0xaba8f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaba8fc: cmp             SP, x16
    //     0xaba900: b.ls            #0xabaa44
    // 0xaba904: r1 = 1
    //     0xaba904: movz            x1, #0x1
    // 0xaba908: r0 = AllocateContext()
    //     0xaba908: bl              #0xec126c  ; AllocateContextStub
    // 0xaba90c: ldur            x1, [fp, #-8]
    // 0xaba910: stur            x0, [fp, #-0x10]
    // 0xaba914: StoreField: r0->field_f = r1
    //     0xaba914: stur            w1, [x0, #0xf]
    // 0xaba918: r0 = AppBar()
    //     0xaba918: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xaba91c: stur            x0, [fp, #-0x18]
    // 0xaba920: r16 = Instance_Text
    //     0xaba920: add             x16, PP, #0x30, lsl #12  ; [pp+0x30bc0] Obj!Text@e21911
    //     0xaba924: ldr             x16, [x16, #0xbc0]
    // 0xaba928: str             x16, [SP]
    // 0xaba92c: mov             x1, x0
    // 0xaba930: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xaba930: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xaba934: ldr             x4, [x4, #0x6e8]
    // 0xaba938: r0 = AppBar()
    //     0xaba938: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xaba93c: ldur            x1, [fp, #-8]
    // 0xaba940: r0 = controller()
    //     0xaba940: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaba944: stur            x0, [fp, #-8]
    // 0xaba948: r0 = NEmptyState()
    //     0xaba948: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xaba94c: mov             x1, x0
    // 0xaba950: r2 = "Artikel yang telah kamu tambahkan ke bookmark tetap dapat dibaca meski tanpa jaringan internet."
    //     0xaba950: add             x2, PP, #0x30, lsl #12  ; [pp+0x30bc8] "Artikel yang telah kamu tambahkan ke bookmark tetap dapat dibaca meski tanpa jaringan internet."
    //     0xaba954: ldr             x2, [x2, #0xbc8]
    // 0xaba958: r3 = "assets/images/illustration/no_bookmark.svg"
    //     0xaba958: add             x3, PP, #0x29, lsl #12  ; [pp+0x29670] "assets/images/illustration/no_bookmark.svg"
    //     0xaba95c: ldr             x3, [x3, #0x670]
    // 0xaba960: r5 = "Belum Ada Artikel yang Kamu Simpan"
    //     0xaba960: add             x5, PP, #0x30, lsl #12  ; [pp+0x30bd0] "Belum Ada Artikel yang Kamu Simpan"
    //     0xaba964: ldr             x5, [x5, #0xbd0]
    // 0xaba968: stur            x0, [fp, #-0x20]
    // 0xaba96c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xaba96c: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xaba970: r0 = NEmptyState.svg()
    //     0xaba970: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xaba974: r1 = Function '<anonymous closure>':.
    //     0xaba974: add             x1, PP, #0x30, lsl #12  ; [pp+0x30bd8] AnonymousClosure: (0xacfec4), in [package:nuonline/app/modules/muktamar/views/muktamar_view.dart] MuktamarView::build (0xb09a98)
    //     0xaba978: ldr             x1, [x1, #0xbd8]
    // 0xaba97c: r2 = Null
    //     0xaba97c: mov             x2, NULL
    // 0xaba980: r0 = AllocateClosure()
    //     0xaba980: bl              #0xec1630  ; AllocateClosureStub
    // 0xaba984: stur            x0, [fp, #-0x28]
    // 0xaba988: r0 = ListView()
    //     0xaba988: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xaba98c: stur            x0, [fp, #-0x30]
    // 0xaba990: r16 = Instance_EdgeInsets
    //     0xaba990: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xaba994: ldr             x16, [x16, #0x360]
    // 0xaba998: str             x16, [SP]
    // 0xaba99c: mov             x1, x0
    // 0xaba9a0: ldur            x2, [fp, #-0x28]
    // 0xaba9a4: r3 = 5
    //     0xaba9a4: movz            x3, #0x5
    // 0xaba9a8: r4 = const [0, 0x4, 0x1, 0x3, padding, 0x3, null]
    //     0xaba9a8: add             x4, PP, #0x29, lsl #12  ; [pp+0x29660] List(7) [0, 0x4, 0x1, 0x3, "padding", 0x3, Null]
    //     0xaba9ac: ldr             x4, [x4, #0x660]
    // 0xaba9b0: r0 = ListView.builder()
    //     0xaba9b0: bl              #0xa2f6f8  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaba9b4: ldur            x2, [fp, #-0x10]
    // 0xaba9b8: r1 = Function '<anonymous closure>':.
    //     0xaba9b8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30be0] AnonymousClosure: (0xacfaec), in [package:nuonline/app/modules/article/article_bookmark/views/article_bookmark_view.dart] ArticleBookmarkView::build (0xaba8e8)
    //     0xaba9bc: ldr             x1, [x1, #0xbe0]
    // 0xaba9c0: r0 = AllocateClosure()
    //     0xaba9c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaba9c4: r16 = <List<ArticleDetail>>
    //     0xaba9c4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30be8] TypeArguments: <List<ArticleDetail>>
    //     0xaba9c8: ldr             x16, [x16, #0xbe8]
    // 0xaba9cc: ldur            lr, [fp, #-8]
    // 0xaba9d0: stp             lr, x16, [SP, #0x18]
    // 0xaba9d4: ldur            x16, [fp, #-0x20]
    // 0xaba9d8: stp             x16, x0, [SP, #8]
    // 0xaba9dc: ldur            x16, [fp, #-0x30]
    // 0xaba9e0: str             x16, [SP]
    // 0xaba9e4: r4 = const [0x1, 0x4, 0x4, 0x2, onEmpty, 0x2, onLoading, 0x3, null]
    //     0xaba9e4: add             x4, PP, #0x30, lsl #12  ; [pp+0x30bf0] List(9) [0x1, 0x4, 0x4, 0x2, "onEmpty", 0x2, "onLoading", 0x3, Null]
    //     0xaba9e8: ldr             x4, [x4, #0xbf0]
    // 0xaba9ec: r0 = StateExt.obx()
    //     0xaba9ec: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xaba9f0: stur            x0, [fp, #-8]
    // 0xaba9f4: r0 = Scaffold()
    //     0xaba9f4: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xaba9f8: ldur            x1, [fp, #-0x18]
    // 0xaba9fc: StoreField: r0->field_13 = r1
    //     0xaba9fc: stur            w1, [x0, #0x13]
    // 0xabaa00: ldur            x1, [fp, #-8]
    // 0xabaa04: ArrayStore: r0[0] = r1  ; List_4
    //     0xabaa04: stur            w1, [x0, #0x17]
    // 0xabaa08: r1 = Instance_AlignmentDirectional
    //     0xabaa08: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xabaa0c: ldr             x1, [x1, #0x758]
    // 0xabaa10: StoreField: r0->field_2b = r1
    //     0xabaa10: stur            w1, [x0, #0x2b]
    // 0xabaa14: r1 = true
    //     0xabaa14: add             x1, NULL, #0x20  ; true
    // 0xabaa18: StoreField: r0->field_53 = r1
    //     0xabaa18: stur            w1, [x0, #0x53]
    // 0xabaa1c: r2 = Instance_DragStartBehavior
    //     0xabaa1c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xabaa20: StoreField: r0->field_57 = r2
    //     0xabaa20: stur            w2, [x0, #0x57]
    // 0xabaa24: r2 = false
    //     0xabaa24: add             x2, NULL, #0x30  ; false
    // 0xabaa28: StoreField: r0->field_b = r2
    //     0xabaa28: stur            w2, [x0, #0xb]
    // 0xabaa2c: StoreField: r0->field_f = r2
    //     0xabaa2c: stur            w2, [x0, #0xf]
    // 0xabaa30: StoreField: r0->field_5f = r1
    //     0xabaa30: stur            w1, [x0, #0x5f]
    // 0xabaa34: StoreField: r0->field_63 = r1
    //     0xabaa34: stur            w1, [x0, #0x63]
    // 0xabaa38: LeaveFrame
    //     0xabaa38: mov             SP, fp
    //     0xabaa3c: ldp             fp, lr, [SP], #0x10
    // 0xabaa40: ret
    //     0xabaa40: ret             
    // 0xabaa44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabaa44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabaa48: b               #0xaba904
  }
  [closure] ListView <anonymous closure>(dynamic, List<ArticleDetail>?) {
    // ** addr: 0xacfaec, size: 0xf4
    // 0xacfaec: EnterFrame
    //     0xacfaec: stp             fp, lr, [SP, #-0x10]!
    //     0xacfaf0: mov             fp, SP
    // 0xacfaf4: AllocStack(0x28)
    //     0xacfaf4: sub             SP, SP, #0x28
    // 0xacfaf8: SetupParameters()
    //     0xacfaf8: ldr             x0, [fp, #0x18]
    //     0xacfafc: ldur            w1, [x0, #0x17]
    //     0xacfb00: add             x1, x1, HEAP, lsl #32
    //     0xacfb04: stur            x1, [fp, #-8]
    // 0xacfb08: CheckStackOverflow
    //     0xacfb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfb0c: cmp             SP, x16
    //     0xacfb10: b.ls            #0xacfbd4
    // 0xacfb14: r1 = 1
    //     0xacfb14: movz            x1, #0x1
    // 0xacfb18: r0 = AllocateContext()
    //     0xacfb18: bl              #0xec126c  ; AllocateContextStub
    // 0xacfb1c: mov             x1, x0
    // 0xacfb20: ldur            x0, [fp, #-8]
    // 0xacfb24: stur            x1, [fp, #-0x10]
    // 0xacfb28: StoreField: r1->field_b = r0
    //     0xacfb28: stur            w0, [x1, #0xb]
    // 0xacfb2c: ldr             x0, [fp, #0x10]
    // 0xacfb30: StoreField: r1->field_f = r0
    //     0xacfb30: stur            w0, [x1, #0xf]
    // 0xacfb34: cmp             w0, NULL
    // 0xacfb38: b.eq            #0xacfbdc
    // 0xacfb3c: r2 = LoadClassIdInstr(r0)
    //     0xacfb3c: ldur            x2, [x0, #-1]
    //     0xacfb40: ubfx            x2, x2, #0xc, #0x14
    // 0xacfb44: str             x0, [SP]
    // 0xacfb48: mov             x0, x2
    // 0xacfb4c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xacfb4c: movz            x17, #0xc834
    //     0xacfb50: add             lr, x0, x17
    //     0xacfb54: ldr             lr, [x21, lr, lsl #3]
    //     0xacfb58: blr             lr
    // 0xacfb5c: r3 = LoadInt32Instr(r0)
    //     0xacfb5c: sbfx            x3, x0, #1, #0x1f
    //     0xacfb60: tbz             w0, #0, #0xacfb68
    //     0xacfb64: ldur            x3, [x0, #7]
    // 0xacfb68: stur            x3, [fp, #-0x18]
    // 0xacfb6c: r1 = Function '<anonymous closure>':.
    //     0xacfb6c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30bf8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xacfb70: ldr             x1, [x1, #0xbf8]
    // 0xacfb74: r2 = Null
    //     0xacfb74: mov             x2, NULL
    // 0xacfb78: r0 = AllocateClosure()
    //     0xacfb78: bl              #0xec1630  ; AllocateClosureStub
    // 0xacfb7c: ldur            x2, [fp, #-0x10]
    // 0xacfb80: r1 = Function '<anonymous closure>':.
    //     0xacfb80: add             x1, PP, #0x30, lsl #12  ; [pp+0x30c00] AnonymousClosure: (0xacfbe0), in [package:nuonline/app/modules/article/article_bookmark/views/article_bookmark_view.dart] ArticleBookmarkView::build (0xaba8e8)
    //     0xacfb84: ldr             x1, [x1, #0xc00]
    // 0xacfb88: stur            x0, [fp, #-8]
    // 0xacfb8c: r0 = AllocateClosure()
    //     0xacfb8c: bl              #0xec1630  ; AllocateClosureStub
    // 0xacfb90: stur            x0, [fp, #-0x10]
    // 0xacfb94: r0 = ListView()
    //     0xacfb94: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xacfb98: stur            x0, [fp, #-0x20]
    // 0xacfb9c: r16 = Instance_EdgeInsets
    //     0xacfb9c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xacfba0: ldr             x16, [x16, #0x360]
    // 0xacfba4: str             x16, [SP]
    // 0xacfba8: mov             x1, x0
    // 0xacfbac: ldur            x2, [fp, #-0x10]
    // 0xacfbb0: ldur            x3, [fp, #-0x18]
    // 0xacfbb4: ldur            x5, [fp, #-8]
    // 0xacfbb8: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xacfbb8: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xacfbbc: ldr             x4, [x4, #0x700]
    // 0xacfbc0: r0 = ListView.separated()
    //     0xacfbc0: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xacfbc4: ldur            x0, [fp, #-0x20]
    // 0xacfbc8: LeaveFrame
    //     0xacfbc8: mov             SP, fp
    //     0xacfbcc: ldp             fp, lr, [SP], #0x10
    // 0xacfbd0: ret
    //     0xacfbd0: ret             
    // 0xacfbd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacfbd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacfbd8: b               #0xacfb14
    // 0xacfbdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xacfbdc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ArticleItem <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xacfbe0, size: 0xc8
    // 0xacfbe0: EnterFrame
    //     0xacfbe0: stp             fp, lr, [SP, #-0x10]!
    //     0xacfbe4: mov             fp, SP
    // 0xacfbe8: AllocStack(0x28)
    //     0xacfbe8: sub             SP, SP, #0x28
    // 0xacfbec: SetupParameters()
    //     0xacfbec: ldr             x0, [fp, #0x20]
    //     0xacfbf0: ldur            w1, [x0, #0x17]
    //     0xacfbf4: add             x1, x1, HEAP, lsl #32
    //     0xacfbf8: stur            x1, [fp, #-8]
    // 0xacfbfc: CheckStackOverflow
    //     0xacfbfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfc00: cmp             SP, x16
    //     0xacfc04: b.ls            #0xacfca0
    // 0xacfc08: r1 = 1
    //     0xacfc08: movz            x1, #0x1
    // 0xacfc0c: r0 = AllocateContext()
    //     0xacfc0c: bl              #0xec126c  ; AllocateContextStub
    // 0xacfc10: mov             x1, x0
    // 0xacfc14: ldur            x0, [fp, #-8]
    // 0xacfc18: stur            x1, [fp, #-0x10]
    // 0xacfc1c: StoreField: r1->field_b = r0
    //     0xacfc1c: stur            w0, [x1, #0xb]
    // 0xacfc20: ldr             x2, [fp, #0x10]
    // 0xacfc24: StoreField: r1->field_f = r2
    //     0xacfc24: stur            w2, [x1, #0xf]
    // 0xacfc28: LoadField: r3 = r0->field_f
    //     0xacfc28: ldur            w3, [x0, #0xf]
    // 0xacfc2c: DecompressPointer r3
    //     0xacfc2c: add             x3, x3, HEAP, lsl #32
    // 0xacfc30: r0 = LoadClassIdInstr(r3)
    //     0xacfc30: ldur            x0, [x3, #-1]
    //     0xacfc34: ubfx            x0, x0, #0xc, #0x14
    // 0xacfc38: stp             x2, x3, [SP]
    // 0xacfc3c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xacfc3c: movz            x17, #0x3037
    //     0xacfc40: movk            x17, #0x1, lsl #16
    //     0xacfc44: add             lr, x0, x17
    //     0xacfc48: ldr             lr, [x21, lr, lsl #3]
    //     0xacfc4c: blr             lr
    // 0xacfc50: mov             x1, x0
    // 0xacfc54: r0 = toArticle()
    //     0xacfc54: bl              #0xacfca8  ; [package:nuonline/app/data/models/article.dart] ArticleDetail::toArticle
    // 0xacfc58: stur            x0, [fp, #-8]
    // 0xacfc5c: r0 = ArticleItem()
    //     0xacfc5c: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xacfc60: mov             x3, x0
    // 0xacfc64: ldur            x0, [fp, #-8]
    // 0xacfc68: stur            x3, [fp, #-0x18]
    // 0xacfc6c: StoreField: r3->field_b = r0
    //     0xacfc6c: stur            w0, [x3, #0xb]
    // 0xacfc70: ldur            x2, [fp, #-0x10]
    // 0xacfc74: r1 = Function '<anonymous closure>':.
    //     0xacfc74: add             x1, PP, #0x30, lsl #12  ; [pp+0x30c08] AnonymousClosure: (0xacfd4c), in [package:nuonline/app/modules/article/article_bookmark/views/article_bookmark_view.dart] ArticleBookmarkView::build (0xaba8e8)
    //     0xacfc78: ldr             x1, [x1, #0xc08]
    // 0xacfc7c: r0 = AllocateClosure()
    //     0xacfc7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xacfc80: mov             x1, x0
    // 0xacfc84: ldur            x0, [fp, #-0x18]
    // 0xacfc88: StoreField: r0->field_f = r1
    //     0xacfc88: stur            w1, [x0, #0xf]
    // 0xacfc8c: r1 = false
    //     0xacfc8c: add             x1, NULL, #0x30  ; false
    // 0xacfc90: StoreField: r0->field_13 = r1
    //     0xacfc90: stur            w1, [x0, #0x13]
    // 0xacfc94: LeaveFrame
    //     0xacfc94: mov             SP, fp
    //     0xacfc98: ldp             fp, lr, [SP], #0x10
    // 0xacfc9c: ret
    //     0xacfc9c: ret             
    // 0xacfca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacfca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacfca4: b               #0xacfc08
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xacfd4c, size: 0xac
    // 0xacfd4c: EnterFrame
    //     0xacfd4c: stp             fp, lr, [SP, #-0x10]!
    //     0xacfd50: mov             fp, SP
    // 0xacfd54: AllocStack(0x28)
    //     0xacfd54: sub             SP, SP, #0x28
    // 0xacfd58: SetupParameters()
    //     0xacfd58: ldr             x0, [fp, #0x10]
    //     0xacfd5c: ldur            w2, [x0, #0x17]
    //     0xacfd60: add             x2, x2, HEAP, lsl #32
    //     0xacfd64: stur            x2, [fp, #-0x10]
    // 0xacfd68: CheckStackOverflow
    //     0xacfd68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfd6c: cmp             SP, x16
    //     0xacfd70: b.ls            #0xacfdf0
    // 0xacfd74: LoadField: r0 = r2->field_b
    //     0xacfd74: ldur            w0, [x2, #0xb]
    // 0xacfd78: DecompressPointer r0
    //     0xacfd78: add             x0, x0, HEAP, lsl #32
    // 0xacfd7c: stur            x0, [fp, #-8]
    // 0xacfd80: LoadField: r1 = r0->field_b
    //     0xacfd80: ldur            w1, [x0, #0xb]
    // 0xacfd84: DecompressPointer r1
    //     0xacfd84: add             x1, x1, HEAP, lsl #32
    // 0xacfd88: LoadField: r3 = r1->field_f
    //     0xacfd88: ldur            w3, [x1, #0xf]
    // 0xacfd8c: DecompressPointer r3
    //     0xacfd8c: add             x3, x3, HEAP, lsl #32
    // 0xacfd90: mov             x1, x3
    // 0xacfd94: r0 = controller()
    //     0xacfd94: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xacfd98: mov             x1, x0
    // 0xacfd9c: ldur            x0, [fp, #-8]
    // 0xacfda0: stur            x1, [fp, #-0x18]
    // 0xacfda4: LoadField: r2 = r0->field_f
    //     0xacfda4: ldur            w2, [x0, #0xf]
    // 0xacfda8: DecompressPointer r2
    //     0xacfda8: add             x2, x2, HEAP, lsl #32
    // 0xacfdac: ldur            x0, [fp, #-0x10]
    // 0xacfdb0: LoadField: r3 = r0->field_f
    //     0xacfdb0: ldur            w3, [x0, #0xf]
    // 0xacfdb4: DecompressPointer r3
    //     0xacfdb4: add             x3, x3, HEAP, lsl #32
    // 0xacfdb8: r0 = LoadClassIdInstr(r2)
    //     0xacfdb8: ldur            x0, [x2, #-1]
    //     0xacfdbc: ubfx            x0, x0, #0xc, #0x14
    // 0xacfdc0: stp             x3, x2, [SP]
    // 0xacfdc4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xacfdc4: movz            x17, #0x3037
    //     0xacfdc8: movk            x17, #0x1, lsl #16
    //     0xacfdcc: add             lr, x0, x17
    //     0xacfdd0: ldr             lr, [x21, lr, lsl #3]
    //     0xacfdd4: blr             lr
    // 0xacfdd8: LoadField: r2 = r0->field_7
    //     0xacfdd8: ldur            x2, [x0, #7]
    // 0xacfddc: ldur            x1, [fp, #-0x18]
    // 0xacfde0: r0 = onPressed()
    //     0xacfde0: bl              #0xacfdf8  ; [package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart] ArticleBookmarkController::onPressed
    // 0xacfde4: LeaveFrame
    //     0xacfde4: mov             SP, fp
    //     0xacfde8: ldp             fp, lr, [SP], #0x10
    // 0xacfdec: ret
    //     0xacfdec: ret             
    // 0xacfdf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacfdf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacfdf4: b               #0xacfd74
  }
}
