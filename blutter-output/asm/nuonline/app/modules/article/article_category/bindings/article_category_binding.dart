// lib: , url: package:nuonline/app/modules/article/article_category/bindings/article_category_binding.dart

// class id: 1050120, size: 0x8
class :: {
}

// class id: 2197, size: 0x8, field offset: 0x8
class ArticleCategoryBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80ceb0, size: 0x1c0
    // 0x80ceb0: EnterFrame
    //     0x80ceb0: stp             fp, lr, [SP, #-0x10]!
    //     0x80ceb4: mov             fp, SP
    // 0x80ceb8: AllocStack(0x20)
    //     0x80ceb8: sub             SP, SP, #0x20
    // 0x80cebc: CheckStackOverflow
    //     0x80cebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80cec0: cmp             SP, x16
    //     0x80cec4: b.ls            #0x80d068
    // 0x80cec8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80cec8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80cecc: ldr             x0, [x0, #0x2670]
    //     0x80ced0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80ced4: cmp             w0, w16
    //     0x80ced8: b.ne            #0x80cee4
    //     0x80cedc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80cee0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80cee4: r0 = GetNavigation.arguments()
    //     0x80cee4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80cee8: r16 = "id"
    //     0x80cee8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80ceec: ldr             x16, [x16, #0x740]
    // 0x80cef0: stp             x16, x0, [SP]
    // 0x80cef4: r4 = 0
    //     0x80cef4: movz            x4, #0
    // 0x80cef8: ldr             x0, [SP, #8]
    // 0x80cefc: r16 = UnlinkedCall_0x5f3c08
    //     0x80cefc: add             x16, PP, #0x36, lsl #12  ; [pp+0x369d8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80cf00: add             x16, x16, #0x9d8
    // 0x80cf04: ldp             x5, lr, [x16]
    // 0x80cf08: blr             lr
    // 0x80cf0c: r1 = 60
    //     0x80cf0c: movz            x1, #0x3c
    // 0x80cf10: branchIfSmi(r0, 0x80cf1c)
    //     0x80cf10: tbz             w0, #0, #0x80cf1c
    // 0x80cf14: r1 = LoadClassIdInstr(r0)
    //     0x80cf14: ldur            x1, [x0, #-1]
    //     0x80cf18: ubfx            x1, x1, #0xc, #0x14
    // 0x80cf1c: sub             x16, x1, #0x5e
    // 0x80cf20: cmp             x16, #1
    // 0x80cf24: b.hi            #0x80cf94
    // 0x80cf28: r0 = GetNavigation.arguments()
    //     0x80cf28: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80cf2c: r16 = "id"
    //     0x80cf2c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80cf30: ldr             x16, [x16, #0x740]
    // 0x80cf34: stp             x16, x0, [SP]
    // 0x80cf38: r4 = 0
    //     0x80cf38: movz            x4, #0
    // 0x80cf3c: ldr             x0, [SP, #8]
    // 0x80cf40: r16 = UnlinkedCall_0x5f3c08
    //     0x80cf40: add             x16, PP, #0x36, lsl #12  ; [pp+0x369e8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80cf44: add             x16, x16, #0x9e8
    // 0x80cf48: ldp             x5, lr, [x16]
    // 0x80cf4c: blr             lr
    // 0x80cf50: mov             x3, x0
    // 0x80cf54: r2 = Null
    //     0x80cf54: mov             x2, NULL
    // 0x80cf58: r1 = Null
    //     0x80cf58: mov             x1, NULL
    // 0x80cf5c: stur            x3, [fp, #-8]
    // 0x80cf60: r4 = 60
    //     0x80cf60: movz            x4, #0x3c
    // 0x80cf64: branchIfSmi(r0, 0x80cf70)
    //     0x80cf64: tbz             w0, #0, #0x80cf70
    // 0x80cf68: r4 = LoadClassIdInstr(r0)
    //     0x80cf68: ldur            x4, [x0, #-1]
    //     0x80cf6c: ubfx            x4, x4, #0xc, #0x14
    // 0x80cf70: sub             x4, x4, #0x5e
    // 0x80cf74: cmp             x4, #1
    // 0x80cf78: b.ls            #0x80cf8c
    // 0x80cf7c: r8 = String
    //     0x80cf7c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80cf80: r3 = Null
    //     0x80cf80: add             x3, PP, #0x36, lsl #12  ; [pp+0x369f8] Null
    //     0x80cf84: ldr             x3, [x3, #0x9f8]
    // 0x80cf88: r0 = String()
    //     0x80cf88: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80cf8c: ldur            x0, [fp, #-8]
    // 0x80cf90: b               #0x80d024
    // 0x80cf94: r0 = GetNavigation.arguments()
    //     0x80cf94: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80cf98: r16 = "id"
    //     0x80cf98: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80cf9c: ldr             x16, [x16, #0x740]
    // 0x80cfa0: stp             x16, x0, [SP]
    // 0x80cfa4: r4 = 0
    //     0x80cfa4: movz            x4, #0
    // 0x80cfa8: ldr             x0, [SP, #8]
    // 0x80cfac: r16 = UnlinkedCall_0x5f3c08
    //     0x80cfac: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a08] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80cfb0: add             x16, x16, #0xa08
    // 0x80cfb4: ldp             x5, lr, [x16]
    // 0x80cfb8: blr             lr
    // 0x80cfbc: mov             x3, x0
    // 0x80cfc0: r2 = Null
    //     0x80cfc0: mov             x2, NULL
    // 0x80cfc4: r1 = Null
    //     0x80cfc4: mov             x1, NULL
    // 0x80cfc8: stur            x3, [fp, #-8]
    // 0x80cfcc: branchIfSmi(r0, 0x80cff4)
    //     0x80cfcc: tbz             w0, #0, #0x80cff4
    // 0x80cfd0: r4 = LoadClassIdInstr(r0)
    //     0x80cfd0: ldur            x4, [x0, #-1]
    //     0x80cfd4: ubfx            x4, x4, #0xc, #0x14
    // 0x80cfd8: sub             x4, x4, #0x3c
    // 0x80cfdc: cmp             x4, #1
    // 0x80cfe0: b.ls            #0x80cff4
    // 0x80cfe4: r8 = int
    //     0x80cfe4: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80cfe8: r3 = Null
    //     0x80cfe8: add             x3, PP, #0x36, lsl #12  ; [pp+0x36a18] Null
    //     0x80cfec: ldr             x3, [x3, #0xa18]
    // 0x80cff0: r0 = int()
    //     0x80cff0: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80cff4: ldur            x0, [fp, #-8]
    // 0x80cff8: r1 = 60
    //     0x80cff8: movz            x1, #0x3c
    // 0x80cffc: branchIfSmi(r0, 0x80d008)
    //     0x80cffc: tbz             w0, #0, #0x80d008
    // 0x80d000: r1 = LoadClassIdInstr(r0)
    //     0x80d000: ldur            x1, [x0, #-1]
    //     0x80d004: ubfx            x1, x1, #0xc, #0x14
    // 0x80d008: str             x0, [SP]
    // 0x80d00c: mov             x0, x1
    // 0x80d010: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x80d010: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x80d014: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x80d014: movz            x17, #0x2b03
    //     0x80d018: add             lr, x0, x17
    //     0x80d01c: ldr             lr, [x21, lr, lsl #3]
    //     0x80d020: blr             lr
    // 0x80d024: stur            x0, [fp, #-8]
    // 0x80d028: r1 = Function '<anonymous closure>':.
    //     0x80d028: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a28] AnonymousClosure: (0x80d070), in [package:nuonline/app/modules/article/article_category/bindings/article_category_binding.dart] ArticleCategoryBinding::dependencies (0x80ceb0)
    //     0x80d02c: ldr             x1, [x1, #0xa28]
    // 0x80d030: r2 = Null
    //     0x80d030: mov             x2, NULL
    // 0x80d034: r0 = AllocateClosure()
    //     0x80d034: bl              #0xec1630  ; AllocateClosureStub
    // 0x80d038: r16 = <ArticleCategoryController>
    //     0x80d038: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d30] TypeArguments: <ArticleCategoryController>
    //     0x80d03c: ldr             x16, [x16, #0xd30]
    // 0x80d040: stp             x0, x16, [SP, #8]
    // 0x80d044: ldur            x16, [fp, #-8]
    // 0x80d048: str             x16, [SP]
    // 0x80d04c: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x80d04c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb630] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x80d050: ldr             x4, [x4, #0x630]
    // 0x80d054: r0 = Inst.lazyPut()
    //     0x80d054: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80d058: r0 = Null
    //     0x80d058: mov             x0, NULL
    // 0x80d05c: LeaveFrame
    //     0x80d05c: mov             SP, fp
    //     0x80d060: ldp             fp, lr, [SP], #0x10
    // 0x80d064: ret
    //     0x80d064: ret             
    // 0x80d068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d06c: b               #0x80cec8
  }
  [closure] ArticleCategoryController <anonymous closure>(dynamic) {
    // ** addr: 0x80d070, size: 0x40
    // 0x80d070: EnterFrame
    //     0x80d070: stp             fp, lr, [SP, #-0x10]!
    //     0x80d074: mov             fp, SP
    // 0x80d078: AllocStack(0x8)
    //     0x80d078: sub             SP, SP, #8
    // 0x80d07c: CheckStackOverflow
    //     0x80d07c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d080: cmp             SP, x16
    //     0x80d084: b.ls            #0x80d0a8
    // 0x80d088: r0 = ArticleCategoryController()
    //     0x80d088: bl              #0x80d348  ; AllocateArticleCategoryControllerStub -> ArticleCategoryController (size=0x2c)
    // 0x80d08c: mov             x1, x0
    // 0x80d090: stur            x0, [fp, #-8]
    // 0x80d094: r0 = ArticleCategoryController()
    //     0x80d094: bl              #0x80d0b0  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_controller.dart] ArticleCategoryController::ArticleCategoryController
    // 0x80d098: ldur            x0, [fp, #-8]
    // 0x80d09c: LeaveFrame
    //     0x80d09c: mov             SP, fp
    //     0x80d0a0: ldp             fp, lr, [SP], #0x10
    // 0x80d0a4: ret
    //     0x80d0a4: ret             
    // 0x80d0a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d0a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d0ac: b               #0x80d088
  }
}
