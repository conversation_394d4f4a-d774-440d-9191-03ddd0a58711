// lib: , url: package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart

// class id: 1050121, size: 0x8
class :: {
}

// class id: 2050, size: 0x54, field offset: 0x38
class ArticleCategoryBuilderController extends _ArticleAuthorController&GetxController&PagingMixin {

  late TextEditingController textController; // offset: 0x44

  const int dyn:get:id(ArticleCategoryBuilderController) {
    // ** addr: 0x80f744, size: 0x48
    // 0x80f744: ldr             x2, [SP]
    // 0x80f748: LoadField: r3 = r2->field_37
    //     0x80f748: ldur            x3, [x2, #0x37]
    // 0x80f74c: r0 = BoxInt64Instr(r3)
    //     0x80f74c: sbfiz           x0, x3, #1, #0x1f
    //     0x80f750: cmp             x3, x0, asr #1
    //     0x80f754: b.eq            #0x80f770
    //     0x80f758: stp             fp, lr, [SP, #-0x10]!
    //     0x80f75c: mov             fp, SP
    //     0x80f760: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80f764: mov             SP, fp
    //     0x80f768: ldp             fp, lr, [SP], #0x10
    //     0x80f76c: stur            x3, [x0, #7]
    // 0x80f770: ret
    //     0x80f770: ret             
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8a9844, size: 0xd0
    // 0x8a9844: EnterFrame
    //     0x8a9844: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9848: mov             fp, SP
    // 0x8a984c: AllocStack(0x30)
    //     0x8a984c: sub             SP, SP, #0x30
    // 0x8a9850: SetupParameters(ArticleCategoryBuilderController this /* r1 => r1, fp-0x8 */)
    //     0x8a9850: stur            x1, [fp, #-8]
    // 0x8a9854: CheckStackOverflow
    //     0x8a9854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9858: cmp             SP, x16
    //     0x8a985c: b.ls            #0x8a990c
    // 0x8a9860: r1 = 1
    //     0x8a9860: movz            x1, #0x1
    // 0x8a9864: r0 = AllocateContext()
    //     0x8a9864: bl              #0xec126c  ; AllocateContextStub
    // 0x8a9868: mov             x2, x0
    // 0x8a986c: ldur            x0, [fp, #-8]
    // 0x8a9870: stur            x2, [fp, #-0x10]
    // 0x8a9874: StoreField: r2->field_f = r0
    //     0x8a9874: stur            w0, [x2, #0xf]
    // 0x8a9878: mov             x1, x0
    // 0x8a987c: r0 = onInit()
    //     0x8a987c: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8a9880: r1 = <TextEditingValue>
    //     0x8a9880: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8a9884: r0 = TextEditingController()
    //     0x8a9884: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8a9888: mov             x1, x0
    // 0x8a988c: stur            x0, [fp, #-0x18]
    // 0x8a9890: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a9890: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a9894: r0 = TextEditingController()
    //     0x8a9894: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8a9898: ldur            x0, [fp, #-0x18]
    // 0x8a989c: ldur            x1, [fp, #-8]
    // 0x8a98a0: StoreField: r1->field_43 = r0
    //     0x8a98a0: stur            w0, [x1, #0x43]
    //     0x8a98a4: ldurb           w16, [x1, #-1]
    //     0x8a98a8: ldurb           w17, [x0, #-1]
    //     0x8a98ac: and             x16, x17, x16, lsr #2
    //     0x8a98b0: tst             x16, HEAP, lsr #32
    //     0x8a98b4: b.eq            #0x8a98bc
    //     0x8a98b8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8a98bc: LoadField: r0 = r1->field_3f
    //     0x8a98bc: ldur            w0, [x1, #0x3f]
    // 0x8a98c0: DecompressPointer r0
    //     0x8a98c0: add             x0, x0, HEAP, lsl #32
    // 0x8a98c4: mov             x1, x0
    // 0x8a98c8: r0 = findAllTag()
    //     0x8a98c8: bl              #0x8a9914  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAllTag
    // 0x8a98cc: ldur            x2, [fp, #-0x10]
    // 0x8a98d0: r1 = Function '<anonymous closure>':.
    //     0x8a98d0: add             x1, PP, #0x41, lsl #12  ; [pp+0x411c8] AnonymousClosure: (0x8a9d94), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onInit (0x8a9844)
    //     0x8a98d4: ldr             x1, [x1, #0x1c8]
    // 0x8a98d8: stur            x0, [fp, #-8]
    // 0x8a98dc: r0 = AllocateClosure()
    //     0x8a98dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a98e0: r16 = <List<Tag>?>
    //     0x8a98e0: add             x16, PP, #0x40, lsl #12  ; [pp+0x409b0] TypeArguments: <List<Tag>?>
    //     0x8a98e4: ldr             x16, [x16, #0x9b0]
    // 0x8a98e8: ldur            lr, [fp, #-8]
    // 0x8a98ec: stp             lr, x16, [SP, #8]
    // 0x8a98f0: str             x0, [SP]
    // 0x8a98f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8a98f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8a98f8: r0 = then()
    //     0x8a98f8: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8a98fc: r0 = Null
    //     0x8a98fc: mov             x0, NULL
    // 0x8a9900: LeaveFrame
    //     0x8a9900: mov             SP, fp
    //     0x8a9904: ldp             fp, lr, [SP], #0x10
    // 0x8a9908: ret
    //     0x8a9908: ret             
    // 0x8a990c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a990c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9910: b               #0x8a9860
  }
  [closure] List<Tag>? <anonymous closure>(dynamic, ApiResult<List<Tag>>) {
    // ** addr: 0x8a9d94, size: 0x98
    // 0x8a9d94: EnterFrame
    //     0x8a9d94: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9d98: mov             fp, SP
    // 0x8a9d9c: AllocStack(0x28)
    //     0x8a9d9c: sub             SP, SP, #0x28
    // 0x8a9da0: SetupParameters()
    //     0x8a9da0: ldr             x0, [fp, #0x18]
    //     0x8a9da4: ldur            w3, [x0, #0x17]
    //     0x8a9da8: add             x3, x3, HEAP, lsl #32
    //     0x8a9dac: stur            x3, [fp, #-8]
    // 0x8a9db0: CheckStackOverflow
    //     0x8a9db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9db4: cmp             SP, x16
    //     0x8a9db8: b.ls            #0x8a9e24
    // 0x8a9dbc: r1 = Function '<anonymous closure>':.
    //     0x8a9dbc: add             x1, PP, #0x41, lsl #12  ; [pp+0x411d0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8a9dc0: ldr             x1, [x1, #0x1d0]
    // 0x8a9dc4: r2 = Null
    //     0x8a9dc4: mov             x2, NULL
    // 0x8a9dc8: r0 = AllocateClosure()
    //     0x8a9dc8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a9dcc: ldur            x2, [fp, #-8]
    // 0x8a9dd0: r1 = Function '<anonymous closure>':.
    //     0x8a9dd0: add             x1, PP, #0x41, lsl #12  ; [pp+0x411d8] AnonymousClosure: (0x8a9e2c), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onReady (0x916180)
    //     0x8a9dd4: ldr             x1, [x1, #0x1d8]
    // 0x8a9dd8: stur            x0, [fp, #-8]
    // 0x8a9ddc: r0 = AllocateClosure()
    //     0x8a9ddc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a9de0: mov             x1, x0
    // 0x8a9de4: ldr             x0, [fp, #0x10]
    // 0x8a9de8: r2 = LoadClassIdInstr(r0)
    //     0x8a9de8: ldur            x2, [x0, #-1]
    //     0x8a9dec: ubfx            x2, x2, #0xc, #0x14
    // 0x8a9df0: r16 = <List<Tag>?>
    //     0x8a9df0: add             x16, PP, #0x40, lsl #12  ; [pp+0x409b0] TypeArguments: <List<Tag>?>
    //     0x8a9df4: ldr             x16, [x16, #0x9b0]
    // 0x8a9df8: stp             x0, x16, [SP, #0x10]
    // 0x8a9dfc: ldur            x16, [fp, #-8]
    // 0x8a9e00: stp             x1, x16, [SP]
    // 0x8a9e04: mov             x0, x2
    // 0x8a9e08: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8a9e08: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8a9e0c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8a9e0c: sub             lr, x0, #1, lsl #12
    //     0x8a9e10: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9e14: blr             lr
    // 0x8a9e18: LeaveFrame
    //     0x8a9e18: mov             SP, fp
    //     0x8a9e1c: ldp             fp, lr, [SP], #0x10
    // 0x8a9e20: ret
    //     0x8a9e20: ret             
    // 0x8a9e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9e24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9e28: b               #0x8a9dbc
  }
  _ ArticleCategoryBuilderController(/* No info */) {
    // ** addr: 0xad4334, size: 0x1a0
    // 0xad4334: EnterFrame
    //     0xad4334: stp             fp, lr, [SP, #-0x10]!
    //     0xad4338: mov             fp, SP
    // 0xad433c: AllocStack(0x30)
    //     0xad433c: sub             SP, SP, #0x30
    // 0xad4340: r0 = Sentinel
    //     0xad4340: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad4344: mov             x4, x1
    // 0xad4348: stur            x2, [fp, #-0x10]
    // 0xad434c: mov             x16, x3
    // 0xad4350: mov             x3, x2
    // 0xad4354: mov             x2, x16
    // 0xad4358: stur            x1, [fp, #-8]
    // 0xad435c: stur            x2, [fp, #-0x18]
    // 0xad4360: CheckStackOverflow
    //     0xad4360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4364: cmp             SP, x16
    //     0xad4368: b.ls            #0xad44cc
    // 0xad436c: StoreField: r4->field_43 = r0
    //     0xad436c: stur            w0, [x4, #0x43]
    // 0xad4370: r1 = ""
    //     0xad4370: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xad4374: r0 = StringExtension.obs()
    //     0xad4374: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0xad4378: ldur            x1, [fp, #-8]
    // 0xad437c: StoreField: r1->field_47 = r0
    //     0xad437c: stur            w0, [x1, #0x47]
    //     0xad4380: ldurb           w16, [x1, #-1]
    //     0xad4384: ldurb           w17, [x0, #-1]
    //     0xad4388: and             x16, x17, x16, lsr #2
    //     0xad438c: tst             x16, HEAP, lsr #32
    //     0xad4390: b.eq            #0xad4398
    //     0xad4394: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xad4398: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4398: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad439c: ldr             x0, [x0, #0x2670]
    //     0xad43a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad43a4: cmp             w0, w16
    //     0xad43a8: b.ne            #0xad43b4
    //     0xad43ac: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad43b0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad43b4: r0 = GetNavigation.arguments()
    //     0xad43b4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0xad43b8: cmp             w0, NULL
    // 0xad43bc: b.eq            #0xad442c
    // 0xad43c0: r0 = GetNavigation.arguments()
    //     0xad43c0: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0xad43c4: r16 = "title"
    //     0xad43c4: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xad43c8: ldr             x16, [x16, #0x748]
    // 0xad43cc: stp             x16, x0, [SP]
    // 0xad43d0: r4 = 0
    //     0xad43d0: movz            x4, #0
    // 0xad43d4: ldr             x0, [SP, #8]
    // 0xad43d8: r16 = UnlinkedCall_0x5f3c08
    //     0xad43d8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36348] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0xad43dc: add             x16, x16, #0x348
    // 0xad43e0: ldp             x5, lr, [x16]
    // 0xad43e4: blr             lr
    // 0xad43e8: mov             x3, x0
    // 0xad43ec: r2 = Null
    //     0xad43ec: mov             x2, NULL
    // 0xad43f0: r1 = Null
    //     0xad43f0: mov             x1, NULL
    // 0xad43f4: stur            x3, [fp, #-0x20]
    // 0xad43f8: r4 = 60
    //     0xad43f8: movz            x4, #0x3c
    // 0xad43fc: branchIfSmi(r0, 0xad4408)
    //     0xad43fc: tbz             w0, #0, #0xad4408
    // 0xad4400: r4 = LoadClassIdInstr(r0)
    //     0xad4400: ldur            x4, [x0, #-1]
    //     0xad4404: ubfx            x4, x4, #0xc, #0x14
    // 0xad4408: sub             x4, x4, #0x5e
    // 0xad440c: cmp             x4, #1
    // 0xad4410: b.ls            #0xad4424
    // 0xad4414: r8 = String
    //     0xad4414: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xad4418: r3 = Null
    //     0xad4418: add             x3, PP, #0x36, lsl #12  ; [pp+0x36358] Null
    //     0xad441c: ldr             x3, [x3, #0x358]
    // 0xad4420: r0 = String()
    //     0xad4420: bl              #0xed43b0  ; IsType_String_Stub
    // 0xad4424: ldur            x0, [fp, #-0x20]
    // 0xad4428: b               #0xad4430
    // 0xad442c: r0 = ""
    //     0xad442c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xad4430: ldur            x3, [fp, #-8]
    // 0xad4434: ldur            x4, [fp, #-0x10]
    // 0xad4438: StoreField: r3->field_4b = r0
    //     0xad4438: stur            w0, [x3, #0x4b]
    //     0xad443c: ldurb           w16, [x3, #-1]
    //     0xad4440: ldurb           w17, [x0, #-1]
    //     0xad4444: and             x16, x17, x16, lsr #2
    //     0xad4448: tst             x16, HEAP, lsr #32
    //     0xad444c: b.eq            #0xad4454
    //     0xad4450: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xad4454: r1 = <Tag>
    //     0xad4454: ldr             x1, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0xad4458: r2 = 0
    //     0xad4458: movz            x2, #0
    // 0xad445c: r0 = _GrowableList()
    //     0xad445c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xad4460: r16 = <Tag>
    //     0xad4460: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0xad4464: stp             x0, x16, [SP]
    // 0xad4468: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad4468: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad446c: r0 = ListExtension.obs()
    //     0xad446c: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0xad4470: ldur            x1, [fp, #-8]
    // 0xad4474: StoreField: r1->field_4f = r0
    //     0xad4474: stur            w0, [x1, #0x4f]
    //     0xad4478: ldurb           w16, [x1, #-1]
    //     0xad447c: ldurb           w17, [x0, #-1]
    //     0xad4480: and             x16, x17, x16, lsr #2
    //     0xad4484: tst             x16, HEAP, lsr #32
    //     0xad4488: b.eq            #0xad4490
    //     0xad448c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xad4490: ldur            x0, [fp, #-0x10]
    // 0xad4494: StoreField: r1->field_37 = r0
    //     0xad4494: stur            x0, [x1, #0x37]
    // 0xad4498: ldur            x0, [fp, #-0x18]
    // 0xad449c: StoreField: r1->field_3f = r0
    //     0xad449c: stur            w0, [x1, #0x3f]
    //     0xad44a0: ldurb           w16, [x1, #-1]
    //     0xad44a4: ldurb           w17, [x0, #-1]
    //     0xad44a8: and             x16, x17, x16, lsr #2
    //     0xad44ac: tst             x16, HEAP, lsr #32
    //     0xad44b0: b.eq            #0xad44b8
    //     0xad44b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xad44b8: r0 = _ArticleAuthorController&GetxController&PagingMixin()
    //     0xad44b8: bl              #0x80c3d4  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::_ArticleAuthorController&GetxController&PagingMixin
    // 0xad44bc: r0 = Null
    //     0xad44bc: mov             x0, NULL
    // 0xad44c0: LeaveFrame
    //     0xad44c0: mov             SP, fp
    //     0xad44c4: ldp             fp, lr, [SP], #0x10
    // 0xad44c8: ret
    //     0xad44c8: ret             
    // 0xad44cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad44cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad44d0: b               #0xad436c
  }
  _ search(/* No info */) {
    // ** addr: 0xbc0ad8, size: 0x88
    // 0xbc0ad8: EnterFrame
    //     0xbc0ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0adc: mov             fp, SP
    // 0xbc0ae0: AllocStack(0x10)
    //     0xbc0ae0: sub             SP, SP, #0x10
    // 0xbc0ae4: SetupParameters(ArticleCategoryBuilderController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbc0ae4: mov             x3, x1
    //     0xbc0ae8: mov             x0, x2
    //     0xbc0aec: stur            x1, [fp, #-8]
    //     0xbc0af0: stur            x2, [fp, #-0x10]
    // 0xbc0af4: CheckStackOverflow
    //     0xbc0af4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0af8: cmp             SP, x16
    //     0xbc0afc: b.ls            #0xbc0b4c
    // 0xbc0b00: LoadField: r1 = r3->field_43
    //     0xbc0b00: ldur            w1, [x3, #0x43]
    // 0xbc0b04: DecompressPointer r1
    //     0xbc0b04: add             x1, x1, HEAP, lsl #32
    // 0xbc0b08: r16 = Sentinel
    //     0xbc0b08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbc0b0c: cmp             w1, w16
    // 0xbc0b10: b.eq            #0xbc0b54
    // 0xbc0b14: mov             x2, x0
    // 0xbc0b18: r0 = text=()
    //     0xbc0b18: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xbc0b1c: ldur            x0, [fp, #-8]
    // 0xbc0b20: LoadField: r1 = r0->field_47
    //     0xbc0b20: ldur            w1, [x0, #0x47]
    // 0xbc0b24: DecompressPointer r1
    //     0xbc0b24: add             x1, x1, HEAP, lsl #32
    // 0xbc0b28: ldur            x2, [fp, #-0x10]
    // 0xbc0b2c: r0 = value=()
    //     0xbc0b2c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbc0b30: ldur            x1, [fp, #-8]
    // 0xbc0b34: ldur            x2, [fp, #-0x10]
    // 0xbc0b38: r0 = onSubmitted()
    //     0xbc0b38: bl              #0xbc0b60  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onSubmitted
    // 0xbc0b3c: r0 = Null
    //     0xbc0b3c: mov             x0, NULL
    // 0xbc0b40: LeaveFrame
    //     0xbc0b40: mov             SP, fp
    //     0xbc0b44: ldp             fp, lr, [SP], #0x10
    // 0xbc0b48: ret
    //     0xbc0b48: ret             
    // 0xbc0b4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0b4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0b50: b               #0xbc0b00
    // 0xbc0b54: r9 = textController
    //     0xbc0b54: add             x9, PP, #0x41, lsl #12  ; [pp+0x41060] Field <ArticleCategoryBuilderController.textController>: late (offset: 0x44)
    //     0xbc0b58: ldr             x9, [x9, #0x60]
    // 0xbc0b5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbc0b5c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ onSubmitted(/* No info */) {
    // ** addr: 0xbc0b60, size: 0x48
    // 0xbc0b60: EnterFrame
    //     0xbc0b60: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0b64: mov             fp, SP
    // 0xbc0b68: CheckStackOverflow
    //     0xbc0b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0b6c: cmp             SP, x16
    //     0xbc0b70: b.ls            #0xbc0ba0
    // 0xbc0b74: LoadField: r0 = r2->field_7
    //     0xbc0b74: ldur            w0, [x2, #7]
    // 0xbc0b78: cbnz            w0, #0xbc0b8c
    // 0xbc0b7c: r0 = Null
    //     0xbc0b7c: mov             x0, NULL
    // 0xbc0b80: LeaveFrame
    //     0xbc0b80: mov             SP, fp
    //     0xbc0b84: ldp             fp, lr, [SP], #0x10
    // 0xbc0b88: ret
    //     0xbc0b88: ret             
    // 0xbc0b8c: r0 = onPageRefresh()
    //     0xbc0b8c: bl              #0xad1fcc  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh
    // 0xbc0b90: r0 = Null
    //     0xbc0b90: mov             x0, NULL
    // 0xbc0b94: LeaveFrame
    //     0xbc0b94: mov             SP, fp
    //     0xbc0b98: ldp             fp, lr, [SP], #0x10
    // 0xbc0b9c: ret
    //     0xbc0b9c: ret             
    // 0xbc0ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0ba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0ba4: b               #0xbc0b74
  }
  [closure] void onSubmitted(dynamic, String) {
    // ** addr: 0xbc0ba8, size: 0x3c
    // 0xbc0ba8: EnterFrame
    //     0xbc0ba8: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0bac: mov             fp, SP
    // 0xbc0bb0: ldr             x0, [fp, #0x18]
    // 0xbc0bb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc0bb4: ldur            w1, [x0, #0x17]
    // 0xbc0bb8: DecompressPointer r1
    //     0xbc0bb8: add             x1, x1, HEAP, lsl #32
    // 0xbc0bbc: CheckStackOverflow
    //     0xbc0bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0bc0: cmp             SP, x16
    //     0xbc0bc4: b.ls            #0xbc0bdc
    // 0xbc0bc8: ldr             x2, [fp, #0x10]
    // 0xbc0bcc: r0 = onSubmitted()
    //     0xbc0bcc: bl              #0xbc0b60  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onSubmitted
    // 0xbc0bd0: LeaveFrame
    //     0xbc0bd0: mov             SP, fp
    //     0xbc0bd4: ldp             fp, lr, [SP], #0x10
    // 0xbc0bd8: ret
    //     0xbc0bd8: ret             
    // 0xbc0bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0be0: b               #0xbc0bc8
  }
  [closure] void onCloseTap(dynamic) {
    // ** addr: 0xbc1300, size: 0x38
    // 0xbc1300: EnterFrame
    //     0xbc1300: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1304: mov             fp, SP
    // 0xbc1308: ldr             x0, [fp, #0x10]
    // 0xbc130c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc130c: ldur            w1, [x0, #0x17]
    // 0xbc1310: DecompressPointer r1
    //     0xbc1310: add             x1, x1, HEAP, lsl #32
    // 0xbc1314: CheckStackOverflow
    //     0xbc1314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1318: cmp             SP, x16
    //     0xbc131c: b.ls            #0xbc1330
    // 0xbc1320: r0 = onCloseTap()
    //     0xbc1320: bl              #0xbc1338  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onCloseTap
    // 0xbc1324: LeaveFrame
    //     0xbc1324: mov             SP, fp
    //     0xbc1328: ldp             fp, lr, [SP], #0x10
    // 0xbc132c: ret
    //     0xbc132c: ret             
    // 0xbc1330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1330: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1334: b               #0xbc1320
  }
  _ onCloseTap(/* No info */) {
    // ** addr: 0xbc1338, size: 0x50
    // 0xbc1338: EnterFrame
    //     0xbc1338: stp             fp, lr, [SP, #-0x10]!
    //     0xbc133c: mov             fp, SP
    // 0xbc1340: AllocStack(0x8)
    //     0xbc1340: sub             SP, SP, #8
    // 0xbc1344: SetupParameters(ArticleCategoryBuilderController this /* r1 => r0, fp-0x8 */)
    //     0xbc1344: mov             x0, x1
    //     0xbc1348: stur            x1, [fp, #-8]
    // 0xbc134c: CheckStackOverflow
    //     0xbc134c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1350: cmp             SP, x16
    //     0xbc1354: b.ls            #0xbc1380
    // 0xbc1358: LoadField: r1 = r0->field_47
    //     0xbc1358: ldur            w1, [x0, #0x47]
    // 0xbc135c: DecompressPointer r1
    //     0xbc135c: add             x1, x1, HEAP, lsl #32
    // 0xbc1360: r2 = ""
    //     0xbc1360: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbc1364: r0 = value=()
    //     0xbc1364: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbc1368: ldur            x1, [fp, #-8]
    // 0xbc136c: r0 = onPageRefresh()
    //     0xbc136c: bl              #0xad1fcc  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh
    // 0xbc1370: r0 = Null
    //     0xbc1370: mov             x0, NULL
    // 0xbc1374: LeaveFrame
    //     0xbc1374: mov             SP, fp
    //     0xbc1378: ldp             fp, lr, [SP], #0x10
    // 0xbc137c: ret
    //     0xbc137c: ret             
    // 0xbc1380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1384: b               #0xbc1358
  }
  [closure] void onChanged(dynamic, String) {
    // ** addr: 0xbc1388, size: 0x3c
    // 0xbc1388: EnterFrame
    //     0xbc1388: stp             fp, lr, [SP, #-0x10]!
    //     0xbc138c: mov             fp, SP
    // 0xbc1390: ldr             x0, [fp, #0x18]
    // 0xbc1394: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc1394: ldur            w1, [x0, #0x17]
    // 0xbc1398: DecompressPointer r1
    //     0xbc1398: add             x1, x1, HEAP, lsl #32
    // 0xbc139c: CheckStackOverflow
    //     0xbc139c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc13a0: cmp             SP, x16
    //     0xbc13a4: b.ls            #0xbc13bc
    // 0xbc13a8: ldr             x2, [fp, #0x10]
    // 0xbc13ac: r0 = onChanged()
    //     0xbc13ac: bl              #0xbc13c4  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onChanged
    // 0xbc13b0: LeaveFrame
    //     0xbc13b0: mov             SP, fp
    //     0xbc13b4: ldp             fp, lr, [SP], #0x10
    // 0xbc13b8: ret
    //     0xbc13b8: ret             
    // 0xbc13bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc13bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc13c0: b               #0xbc13a8
  }
  _ onChanged(/* No info */) {
    // ** addr: 0xbc13c4, size: 0x80
    // 0xbc13c4: EnterFrame
    //     0xbc13c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc13c8: mov             fp, SP
    // 0xbc13cc: AllocStack(0x18)
    //     0xbc13cc: sub             SP, SP, #0x18
    // 0xbc13d0: SetupParameters(ArticleCategoryBuilderController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbc13d0: stur            x1, [fp, #-8]
    //     0xbc13d4: stur            x2, [fp, #-0x10]
    // 0xbc13d8: CheckStackOverflow
    //     0xbc13d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc13dc: cmp             SP, x16
    //     0xbc13e0: b.ls            #0xbc143c
    // 0xbc13e4: r1 = 1
    //     0xbc13e4: movz            x1, #0x1
    // 0xbc13e8: r0 = AllocateContext()
    //     0xbc13e8: bl              #0xec126c  ; AllocateContextStub
    // 0xbc13ec: mov             x3, x0
    // 0xbc13f0: ldur            x0, [fp, #-8]
    // 0xbc13f4: stur            x3, [fp, #-0x18]
    // 0xbc13f8: StoreField: r3->field_f = r0
    //     0xbc13f8: stur            w0, [x3, #0xf]
    // 0xbc13fc: LoadField: r1 = r0->field_47
    //     0xbc13fc: ldur            w1, [x0, #0x47]
    // 0xbc1400: DecompressPointer r1
    //     0xbc1400: add             x1, x1, HEAP, lsl #32
    // 0xbc1404: ldur            x2, [fp, #-0x10]
    // 0xbc1408: r0 = value=()
    //     0xbc1408: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbc140c: ldur            x2, [fp, #-0x18]
    // 0xbc1410: r1 = Function '<anonymous closure>':.
    //     0xbc1410: add             x1, PP, #0x41, lsl #12  ; [pp+0x410e8] AnonymousClosure: (0xbc1444), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onChanged (0xbc13c4)
    //     0xbc1414: ldr             x1, [x1, #0xe8]
    // 0xbc1418: r0 = AllocateClosure()
    //     0xbc1418: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc141c: mov             x2, x0
    // 0xbc1420: r1 = "totorial-search"
    //     0xbc1420: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7b8] "totorial-search"
    //     0xbc1424: ldr             x1, [x1, #0x7b8]
    // 0xbc1428: r0 = debounce()
    //     0xbc1428: bl              #0xb03be4  ; [package:easy_debounce/easy_debounce.dart] EasyDebounce::debounce
    // 0xbc142c: r0 = Null
    //     0xbc142c: mov             x0, NULL
    // 0xbc1430: LeaveFrame
    //     0xbc1430: mov             SP, fp
    //     0xbc1434: ldp             fp, lr, [SP], #0x10
    // 0xbc1438: ret
    //     0xbc1438: ret             
    // 0xbc143c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc143c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1440: b               #0xbc13e4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc1444, size: 0x44
    // 0xbc1444: EnterFrame
    //     0xbc1444: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1448: mov             fp, SP
    // 0xbc144c: ldr             x0, [fp, #0x10]
    // 0xbc1450: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc1450: ldur            w1, [x0, #0x17]
    // 0xbc1454: DecompressPointer r1
    //     0xbc1454: add             x1, x1, HEAP, lsl #32
    // 0xbc1458: CheckStackOverflow
    //     0xbc1458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc145c: cmp             SP, x16
    //     0xbc1460: b.ls            #0xbc1480
    // 0xbc1464: LoadField: r0 = r1->field_f
    //     0xbc1464: ldur            w0, [x1, #0xf]
    // 0xbc1468: DecompressPointer r0
    //     0xbc1468: add             x0, x0, HEAP, lsl #32
    // 0xbc146c: mov             x1, x0
    // 0xbc1470: r0 = onPageRefresh()
    //     0xbc1470: bl              #0xad1fcc  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh
    // 0xbc1474: LeaveFrame
    //     0xbc1474: mov             SP, fp
    //     0xbc1478: ldp             fp, lr, [SP], #0x10
    // 0xbc147c: ret
    //     0xbc147c: ret             
    // 0xbc1480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1484: b               #0xbc1464
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe32638, size: 0x208
    // 0xe32638: EnterFrame
    //     0xe32638: stp             fp, lr, [SP, #-0x10]!
    //     0xe3263c: mov             fp, SP
    // 0xe32640: AllocStack(0x40)
    //     0xe32640: sub             SP, SP, #0x40
    // 0xe32644: SetupParameters(ArticleCategoryBuilderController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe32644: stur            x1, [fp, #-8]
    //     0xe32648: stur            x2, [fp, #-0x10]
    // 0xe3264c: CheckStackOverflow
    //     0xe3264c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32650: cmp             SP, x16
    //     0xe32654: b.ls            #0xe32838
    // 0xe32658: r1 = 1
    //     0xe32658: movz            x1, #0x1
    // 0xe3265c: r0 = AllocateContext()
    //     0xe3265c: bl              #0xec126c  ; AllocateContextStub
    // 0xe32660: mov             x3, x0
    // 0xe32664: ldur            x0, [fp, #-8]
    // 0xe32668: stur            x3, [fp, #-0x20]
    // 0xe3266c: StoreField: r3->field_f = r0
    //     0xe3266c: stur            w0, [x3, #0xf]
    // 0xe32670: LoadField: r4 = r0->field_3f
    //     0xe32670: ldur            w4, [x0, #0x3f]
    // 0xe32674: DecompressPointer r4
    //     0xe32674: add             x4, x4, HEAP, lsl #32
    // 0xe32678: stur            x4, [fp, #-0x18]
    // 0xe3267c: r1 = Null
    //     0xe3267c: mov             x1, NULL
    // 0xe32680: r2 = 12
    //     0xe32680: movz            x2, #0xc
    // 0xe32684: r0 = AllocateArray()
    //     0xe32684: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe32688: mov             x2, x0
    // 0xe3268c: stur            x2, [fp, #-0x28]
    // 0xe32690: r16 = "category_id"
    //     0xe32690: add             x16, PP, #0x17, lsl #12  ; [pp+0x17e70] "category_id"
    //     0xe32694: ldr             x16, [x16, #0xe70]
    // 0xe32698: StoreField: r2->field_f = r16
    //     0xe32698: stur            w16, [x2, #0xf]
    // 0xe3269c: ldur            x3, [fp, #-8]
    // 0xe326a0: LoadField: r4 = r3->field_37
    //     0xe326a0: ldur            x4, [x3, #0x37]
    // 0xe326a4: r0 = BoxInt64Instr(r4)
    //     0xe326a4: sbfiz           x0, x4, #1, #0x1f
    //     0xe326a8: cmp             x4, x0, asr #1
    //     0xe326ac: b.eq            #0xe326b8
    //     0xe326b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe326b4: stur            x4, [x0, #7]
    // 0xe326b8: r1 = 60
    //     0xe326b8: movz            x1, #0x3c
    // 0xe326bc: branchIfSmi(r0, 0xe326c8)
    //     0xe326bc: tbz             w0, #0, #0xe326c8
    // 0xe326c0: r1 = LoadClassIdInstr(r0)
    //     0xe326c0: ldur            x1, [x0, #-1]
    //     0xe326c4: ubfx            x1, x1, #0xc, #0x14
    // 0xe326c8: str             x0, [SP]
    // 0xe326cc: mov             x0, x1
    // 0xe326d0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe326d0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe326d4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe326d4: movz            x17, #0x2b03
    //     0xe326d8: add             lr, x0, x17
    //     0xe326dc: ldr             lr, [x21, lr, lsl #3]
    //     0xe326e0: blr             lr
    // 0xe326e4: ldur            x1, [fp, #-0x28]
    // 0xe326e8: ArrayStore: r1[1] = r0  ; List_4
    //     0xe326e8: add             x25, x1, #0x13
    //     0xe326ec: str             w0, [x25]
    //     0xe326f0: tbz             w0, #0, #0xe3270c
    //     0xe326f4: ldurb           w16, [x1, #-1]
    //     0xe326f8: ldurb           w17, [x0, #-1]
    //     0xe326fc: and             x16, x17, x16, lsr #2
    //     0xe32700: tst             x16, HEAP, lsr #32
    //     0xe32704: b.eq            #0xe3270c
    //     0xe32708: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe3270c: ldur            x2, [fp, #-0x28]
    // 0xe32710: r16 = "page"
    //     0xe32710: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe32714: ldr             x16, [x16, #0x300]
    // 0xe32718: ArrayStore: r2[0] = r16  ; List_4
    //     0xe32718: stur            w16, [x2, #0x17]
    // 0xe3271c: ldur            x3, [fp, #-0x10]
    // 0xe32720: r0 = BoxInt64Instr(r3)
    //     0xe32720: sbfiz           x0, x3, #1, #0x1f
    //     0xe32724: cmp             x3, x0, asr #1
    //     0xe32728: b.eq            #0xe32734
    //     0xe3272c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe32730: stur            x3, [x0, #7]
    // 0xe32734: r1 = 60
    //     0xe32734: movz            x1, #0x3c
    // 0xe32738: branchIfSmi(r0, 0xe32744)
    //     0xe32738: tbz             w0, #0, #0xe32744
    // 0xe3273c: r1 = LoadClassIdInstr(r0)
    //     0xe3273c: ldur            x1, [x0, #-1]
    //     0xe32740: ubfx            x1, x1, #0xc, #0x14
    // 0xe32744: str             x0, [SP]
    // 0xe32748: mov             x0, x1
    // 0xe3274c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe3274c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe32750: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe32750: movz            x17, #0x2b03
    //     0xe32754: add             lr, x0, x17
    //     0xe32758: ldr             lr, [x21, lr, lsl #3]
    //     0xe3275c: blr             lr
    // 0xe32760: ldur            x1, [fp, #-0x28]
    // 0xe32764: ArrayStore: r1[3] = r0  ; List_4
    //     0xe32764: add             x25, x1, #0x1b
    //     0xe32768: str             w0, [x25]
    //     0xe3276c: tbz             w0, #0, #0xe32788
    //     0xe32770: ldurb           w16, [x1, #-1]
    //     0xe32774: ldurb           w17, [x0, #-1]
    //     0xe32778: and             x16, x17, x16, lsr #2
    //     0xe3277c: tst             x16, HEAP, lsr #32
    //     0xe32780: b.eq            #0xe32788
    //     0xe32784: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe32788: ldur            x0, [fp, #-0x28]
    // 0xe3278c: r16 = "q"
    //     0xe3278c: add             x16, PP, #0x29, lsl #12  ; [pp+0x291d0] "q"
    //     0xe32790: ldr             x16, [x16, #0x1d0]
    // 0xe32794: StoreField: r0->field_1f = r16
    //     0xe32794: stur            w16, [x0, #0x1f]
    // 0xe32798: ldur            x2, [fp, #-8]
    // 0xe3279c: LoadField: r1 = r2->field_47
    //     0xe3279c: ldur            w1, [x2, #0x47]
    // 0xe327a0: DecompressPointer r1
    //     0xe327a0: add             x1, x1, HEAP, lsl #32
    // 0xe327a4: r0 = value()
    //     0xe327a4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe327a8: ldur            x1, [fp, #-0x28]
    // 0xe327ac: ArrayStore: r1[5] = r0  ; List_4
    //     0xe327ac: add             x25, x1, #0x23
    //     0xe327b0: str             w0, [x25]
    //     0xe327b4: tbz             w0, #0, #0xe327d0
    //     0xe327b8: ldurb           w16, [x1, #-1]
    //     0xe327bc: ldurb           w17, [x0, #-1]
    //     0xe327c0: and             x16, x17, x16, lsr #2
    //     0xe327c4: tst             x16, HEAP, lsr #32
    //     0xe327c8: b.eq            #0xe327d0
    //     0xe327cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe327d0: r16 = <String, String?>
    //     0xe327d0: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe327d4: ldr             x16, [x16, #0x198]
    // 0xe327d8: ldur            lr, [fp, #-0x28]
    // 0xe327dc: stp             lr, x16, [SP]
    // 0xe327e0: r0 = Map._fromLiteral()
    //     0xe327e0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe327e4: ldur            x1, [fp, #-0x18]
    // 0xe327e8: mov             x2, x0
    // 0xe327ec: r0 = findAll()
    //     0xe327ec: bl              #0x8ff2cc  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAll
    // 0xe327f0: ldur            x2, [fp, #-0x20]
    // 0xe327f4: r1 = Function '<anonymous closure>':.
    //     0xe327f4: add             x1, PP, #0x41, lsl #12  ; [pp+0x411b0] AnonymousClosure: (0xe32840), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onPageRequest (0xe32638)
    //     0xe327f8: ldr             x1, [x1, #0x1b0]
    // 0xe327fc: stur            x0, [fp, #-0x18]
    // 0xe32800: r0 = AllocateClosure()
    //     0xe32800: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32804: r16 = <void?>
    //     0xe32804: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32808: ldur            lr, [fp, #-0x18]
    // 0xe3280c: stp             lr, x16, [SP, #8]
    // 0xe32810: str             x0, [SP]
    // 0xe32814: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe32814: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe32818: r0 = then()
    //     0xe32818: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe3281c: ldur            x1, [fp, #-8]
    // 0xe32820: ldur            x2, [fp, #-0x10]
    // 0xe32824: r0 = onPageRequest()
    //     0xe32824: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe32828: r0 = Null
    //     0xe32828: mov             x0, NULL
    // 0xe3282c: LeaveFrame
    //     0xe3282c: mov             SP, fp
    //     0xe32830: ldp             fp, lr, [SP], #0x10
    // 0xe32834: ret
    //     0xe32834: ret             
    // 0xe32838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32838: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3283c: b               #0xe32658
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Article>>) {
    // ** addr: 0xe32840, size: 0xa0
    // 0xe32840: EnterFrame
    //     0xe32840: stp             fp, lr, [SP, #-0x10]!
    //     0xe32844: mov             fp, SP
    // 0xe32848: AllocStack(0x28)
    //     0xe32848: sub             SP, SP, #0x28
    // 0xe3284c: SetupParameters()
    //     0xe3284c: ldr             x0, [fp, #0x18]
    //     0xe32850: ldur            w1, [x0, #0x17]
    //     0xe32854: add             x1, x1, HEAP, lsl #32
    // 0xe32858: CheckStackOverflow
    //     0xe32858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3285c: cmp             SP, x16
    //     0xe32860: b.ls            #0xe328d8
    // 0xe32864: LoadField: r0 = r1->field_f
    //     0xe32864: ldur            w0, [x1, #0xf]
    // 0xe32868: DecompressPointer r0
    //     0xe32868: add             x0, x0, HEAP, lsl #32
    // 0xe3286c: mov             x2, x0
    // 0xe32870: stur            x0, [fp, #-8]
    // 0xe32874: r1 = Function '_onPageRequestSuccess@1855011465':.
    //     0xe32874: add             x1, PP, #0x41, lsl #12  ; [pp+0x411b8] AnonymousClosure: (0xe3291c), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::_onPageRequestSuccess (0xe3295c)
    //     0xe32878: ldr             x1, [x1, #0x1b8]
    // 0xe3287c: r0 = AllocateClosure()
    //     0xe3287c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32880: ldur            x2, [fp, #-8]
    // 0xe32884: r1 = Function '_onPageRequestError@1855011465':.
    //     0xe32884: add             x1, PP, #0x41, lsl #12  ; [pp+0x411c0] AnonymousClosure: (0xe328e0), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError (0xe32524)
    //     0xe32888: ldr             x1, [x1, #0x1c0]
    // 0xe3288c: stur            x0, [fp, #-8]
    // 0xe32890: r0 = AllocateClosure()
    //     0xe32890: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32894: mov             x1, x0
    // 0xe32898: ldr             x0, [fp, #0x10]
    // 0xe3289c: r2 = LoadClassIdInstr(r0)
    //     0xe3289c: ldur            x2, [x0, #-1]
    //     0xe328a0: ubfx            x2, x2, #0xc, #0x14
    // 0xe328a4: r16 = <void?>
    //     0xe328a4: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe328a8: stp             x0, x16, [SP, #0x10]
    // 0xe328ac: ldur            x16, [fp, #-8]
    // 0xe328b0: stp             x16, x1, [SP]
    // 0xe328b4: mov             x0, x2
    // 0xe328b8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe328b8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe328bc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe328bc: sub             lr, x0, #1, lsl #12
    //     0xe328c0: ldr             lr, [x21, lr, lsl #3]
    //     0xe328c4: blr             lr
    // 0xe328c8: r0 = Null
    //     0xe328c8: mov             x0, NULL
    // 0xe328cc: LeaveFrame
    //     0xe328cc: mov             SP, fp
    //     0xe328d0: ldp             fp, lr, [SP], #0x10
    // 0xe328d4: ret
    //     0xe328d4: ret             
    // 0xe328d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe328d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe328dc: b               #0xe32864
  }
  [closure] void _onPageRequestError(dynamic, NetworkExceptions) {
    // ** addr: 0xe328e0, size: 0x3c
    // 0xe328e0: EnterFrame
    //     0xe328e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe328e4: mov             fp, SP
    // 0xe328e8: ldr             x0, [fp, #0x18]
    // 0xe328ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe328ec: ldur            w1, [x0, #0x17]
    // 0xe328f0: DecompressPointer r1
    //     0xe328f0: add             x1, x1, HEAP, lsl #32
    // 0xe328f4: CheckStackOverflow
    //     0xe328f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe328f8: cmp             SP, x16
    //     0xe328fc: b.ls            #0xe32914
    // 0xe32900: ldr             x2, [fp, #0x10]
    // 0xe32904: r0 = _onError()
    //     0xe32904: bl              #0xe32524  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError
    // 0xe32908: LeaveFrame
    //     0xe32908: mov             SP, fp
    //     0xe3290c: ldp             fp, lr, [SP], #0x10
    // 0xe32910: ret
    //     0xe32910: ret             
    // 0xe32914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32914: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32918: b               #0xe32900
  }
  [closure] void _onPageRequestSuccess(dynamic, List<Article>, Pagination?) {
    // ** addr: 0xe3291c, size: 0x40
    // 0xe3291c: EnterFrame
    //     0xe3291c: stp             fp, lr, [SP, #-0x10]!
    //     0xe32920: mov             fp, SP
    // 0xe32924: ldr             x0, [fp, #0x20]
    // 0xe32928: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe32928: ldur            w1, [x0, #0x17]
    // 0xe3292c: DecompressPointer r1
    //     0xe3292c: add             x1, x1, HEAP, lsl #32
    // 0xe32930: CheckStackOverflow
    //     0xe32930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32934: cmp             SP, x16
    //     0xe32938: b.ls            #0xe32954
    // 0xe3293c: ldr             x2, [fp, #0x18]
    // 0xe32940: ldr             x3, [fp, #0x10]
    // 0xe32944: r0 = _onPageRequestSuccess()
    //     0xe32944: bl              #0xe3295c  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::_onPageRequestSuccess
    // 0xe32948: LeaveFrame
    //     0xe32948: mov             SP, fp
    //     0xe3294c: ldp             fp, lr, [SP], #0x10
    // 0xe32950: ret
    //     0xe32950: ret             
    // 0xe32954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32954: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32958: b               #0xe3293c
  }
  _ _onPageRequestSuccess(/* No info */) {
    // ** addr: 0xe3295c, size: 0xec
    // 0xe3295c: EnterFrame
    //     0xe3295c: stp             fp, lr, [SP, #-0x10]!
    //     0xe32960: mov             fp, SP
    // 0xe32964: AllocStack(0x18)
    //     0xe32964: sub             SP, SP, #0x18
    // 0xe32968: SetupParameters(ArticleCategoryBuilderController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe32968: mov             x0, x2
    //     0xe3296c: stur            x2, [fp, #-0x10]
    //     0xe32970: mov             x2, x1
    //     0xe32974: stur            x1, [fp, #-8]
    //     0xe32978: stur            x3, [fp, #-0x18]
    // 0xe3297c: CheckStackOverflow
    //     0xe3297c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32980: cmp             SP, x16
    //     0xe32984: b.ls            #0xe32a40
    // 0xe32988: LoadField: r1 = r2->field_1f
    //     0xe32988: ldur            w1, [x2, #0x1f]
    // 0xe3298c: DecompressPointer r1
    //     0xe3298c: add             x1, x1, HEAP, lsl #32
    // 0xe32990: r0 = value()
    //     0xe32990: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe32994: cmp             w0, #2
    // 0xe32998: b.ne            #0xe329e4
    // 0xe3299c: ldur            x2, [fp, #-0x10]
    // 0xe329a0: r0 = LoadClassIdInstr(r2)
    //     0xe329a0: ldur            x0, [x2, #-1]
    //     0xe329a4: ubfx            x0, x0, #0xc, #0x14
    // 0xe329a8: mov             x1, x2
    // 0xe329ac: r0 = GDT[cid_x0 + 0xe879]()
    //     0xe329ac: movz            x17, #0xe879
    //     0xe329b0: add             lr, x0, x17
    //     0xe329b4: ldr             lr, [x21, lr, lsl #3]
    //     0xe329b8: blr             lr
    // 0xe329bc: tbnz            w0, #4, #0xe329dc
    // 0xe329c0: ldur            x1, [fp, #-8]
    // 0xe329c4: LoadField: r0 = r1->field_2b
    //     0xe329c4: ldur            w0, [x1, #0x2b]
    // 0xe329c8: DecompressPointer r0
    //     0xe329c8: add             x0, x0, HEAP, lsl #32
    // 0xe329cc: mov             x1, x0
    // 0xe329d0: r2 = true
    //     0xe329d0: add             x2, NULL, #0x20  ; true
    // 0xe329d4: r0 = value=()
    //     0xe329d4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xe329d8: b               #0xe32a30
    // 0xe329dc: ldur            x1, [fp, #-8]
    // 0xe329e0: b               #0xe329e8
    // 0xe329e4: ldur            x1, [fp, #-8]
    // 0xe329e8: ldur            x0, [fp, #-0x18]
    // 0xe329ec: cmp             w0, NULL
    // 0xe329f0: b.ne            #0xe329fc
    // 0xe329f4: r0 = Null
    //     0xe329f4: mov             x0, NULL
    // 0xe329f8: b               #0xe32a14
    // 0xe329fc: LoadField: r2 = r0->field_7
    //     0xe329fc: ldur            x2, [x0, #7]
    // 0xe32a00: LoadField: r3 = r0->field_f
    //     0xe32a00: ldur            x3, [x0, #0xf]
    // 0xe32a04: cmp             x2, x3
    // 0xe32a08: r16 = true
    //     0xe32a08: add             x16, NULL, #0x20  ; true
    // 0xe32a0c: r17 = false
    //     0xe32a0c: add             x17, NULL, #0x30  ; false
    // 0xe32a10: csel            x0, x16, x17, lt
    // 0xe32a14: cmp             w0, NULL
    // 0xe32a18: b.ne            #0xe32a24
    // 0xe32a1c: r3 = false
    //     0xe32a1c: add             x3, NULL, #0x30  ; false
    // 0xe32a20: b               #0xe32a28
    // 0xe32a24: mov             x3, x0
    // 0xe32a28: ldur            x2, [fp, #-0x10]
    // 0xe32a2c: r0 = appendToPage()
    //     0xe32a2c: bl              #0x7f8b40  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::appendToPage
    // 0xe32a30: r0 = Null
    //     0xe32a30: mov             x0, NULL
    // 0xe32a34: LeaveFrame
    //     0xe32a34: mov             SP, fp
    //     0xe32a38: ldp             fp, lr, [SP], #0x10
    // 0xe32a3c: ret
    //     0xe32a3c: ret             
    // 0xe32a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32a40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32a44: b               #0xe32988
  }
}
