// lib: , url: package:nuonline/app/modules/article/article_category/controllers/article_category_controller.dart

// class id: 1050122, size: 0x8
class :: {
}

// class id: 2040, size: 0x2c, field offset: 0x20
class ArticleCategoryController extends GetxController {

  _ ArticleCategoryController(/* No info */) {
    // ** addr: 0x80d0b0, size: 0x230
    // 0x80d0b0: EnterFrame
    //     0x80d0b0: stp             fp, lr, [SP, #-0x10]!
    //     0x80d0b4: mov             fp, SP
    // 0x80d0b8: AllocStack(0x20)
    //     0x80d0b8: sub             SP, SP, #0x20
    // 0x80d0bc: SetupParameters(ArticleCategoryController this /* r1 => r1, fp-0x8 */)
    //     0x80d0bc: stur            x1, [fp, #-8]
    // 0x80d0c0: CheckStackOverflow
    //     0x80d0c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d0c4: cmp             SP, x16
    //     0x80d0c8: b.ls            #0x80d2d8
    // 0x80d0cc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d0cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d0d0: ldr             x0, [x0, #0x2670]
    //     0x80d0d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d0d8: cmp             w0, w16
    //     0x80d0dc: b.ne            #0x80d0e8
    //     0x80d0e0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d0e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d0e8: r0 = GetNavigation.arguments()
    //     0x80d0e8: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d0ec: r16 = "id"
    //     0x80d0ec: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80d0f0: ldr             x16, [x16, #0x740]
    // 0x80d0f4: stp             x16, x0, [SP]
    // 0x80d0f8: r4 = 0
    //     0x80d0f8: movz            x4, #0
    // 0x80d0fc: ldr             x0, [SP, #8]
    // 0x80d100: r16 = UnlinkedCall_0x5f3c08
    //     0x80d100: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a30] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d104: add             x16, x16, #0xa30
    // 0x80d108: ldp             x5, lr, [x16]
    // 0x80d10c: blr             lr
    // 0x80d110: r1 = 60
    //     0x80d110: movz            x1, #0x3c
    // 0x80d114: branchIfSmi(r0, 0x80d120)
    //     0x80d114: tbz             w0, #0, #0x80d120
    // 0x80d118: r1 = LoadClassIdInstr(r0)
    //     0x80d118: ldur            x1, [x0, #-1]
    //     0x80d11c: ubfx            x1, x1, #0xc, #0x14
    // 0x80d120: sub             x16, x1, #0x3c
    // 0x80d124: cmp             x16, #1
    // 0x80d128: b.hi            #0x80d1a4
    // 0x80d12c: r0 = GetNavigation.arguments()
    //     0x80d12c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d130: r16 = "id"
    //     0x80d130: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80d134: ldr             x16, [x16, #0x740]
    // 0x80d138: stp             x16, x0, [SP]
    // 0x80d13c: r4 = 0
    //     0x80d13c: movz            x4, #0
    // 0x80d140: ldr             x0, [SP, #8]
    // 0x80d144: r16 = UnlinkedCall_0x5f3c08
    //     0x80d144: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a40] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d148: add             x16, x16, #0xa40
    // 0x80d14c: ldp             x5, lr, [x16]
    // 0x80d150: blr             lr
    // 0x80d154: mov             x3, x0
    // 0x80d158: r2 = Null
    //     0x80d158: mov             x2, NULL
    // 0x80d15c: r1 = Null
    //     0x80d15c: mov             x1, NULL
    // 0x80d160: stur            x3, [fp, #-0x10]
    // 0x80d164: branchIfSmi(r0, 0x80d18c)
    //     0x80d164: tbz             w0, #0, #0x80d18c
    // 0x80d168: r4 = LoadClassIdInstr(r0)
    //     0x80d168: ldur            x4, [x0, #-1]
    //     0x80d16c: ubfx            x4, x4, #0xc, #0x14
    // 0x80d170: sub             x4, x4, #0x3c
    // 0x80d174: cmp             x4, #1
    // 0x80d178: b.ls            #0x80d18c
    // 0x80d17c: r8 = int
    //     0x80d17c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80d180: r3 = Null
    //     0x80d180: add             x3, PP, #0x36, lsl #12  ; [pp+0x36a50] Null
    //     0x80d184: ldr             x3, [x3, #0xa50]
    // 0x80d188: r0 = int()
    //     0x80d188: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80d18c: ldur            x0, [fp, #-0x10]
    // 0x80d190: r1 = LoadInt32Instr(r0)
    //     0x80d190: sbfx            x1, x0, #1, #0x1f
    //     0x80d194: tbz             w0, #0, #0x80d19c
    //     0x80d198: ldur            x1, [x0, #7]
    // 0x80d19c: mov             x0, x1
    // 0x80d1a0: b               #0x80d234
    // 0x80d1a4: r0 = GetNavigation.arguments()
    //     0x80d1a4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d1a8: r16 = "id"
    //     0x80d1a8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80d1ac: ldr             x16, [x16, #0x740]
    // 0x80d1b0: stp             x16, x0, [SP]
    // 0x80d1b4: r4 = 0
    //     0x80d1b4: movz            x4, #0
    // 0x80d1b8: ldr             x0, [SP, #8]
    // 0x80d1bc: r16 = UnlinkedCall_0x5f3c08
    //     0x80d1bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a60] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d1c0: add             x16, x16, #0xa60
    // 0x80d1c4: ldp             x5, lr, [x16]
    // 0x80d1c8: blr             lr
    // 0x80d1cc: mov             x3, x0
    // 0x80d1d0: r2 = Null
    //     0x80d1d0: mov             x2, NULL
    // 0x80d1d4: r1 = Null
    //     0x80d1d4: mov             x1, NULL
    // 0x80d1d8: stur            x3, [fp, #-0x10]
    // 0x80d1dc: r4 = 60
    //     0x80d1dc: movz            x4, #0x3c
    // 0x80d1e0: branchIfSmi(r0, 0x80d1ec)
    //     0x80d1e0: tbz             w0, #0, #0x80d1ec
    // 0x80d1e4: r4 = LoadClassIdInstr(r0)
    //     0x80d1e4: ldur            x4, [x0, #-1]
    //     0x80d1e8: ubfx            x4, x4, #0xc, #0x14
    // 0x80d1ec: sub             x4, x4, #0x5e
    // 0x80d1f0: cmp             x4, #1
    // 0x80d1f4: b.ls            #0x80d208
    // 0x80d1f8: r8 = String
    //     0x80d1f8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80d1fc: r3 = Null
    //     0x80d1fc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36a70] Null
    //     0x80d200: ldr             x3, [x3, #0xa70]
    // 0x80d204: r0 = String()
    //     0x80d204: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80d208: ldur            x1, [fp, #-0x10]
    // 0x80d20c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80d20c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80d210: r0 = tryParse()
    //     0x80d210: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0x80d214: cmp             w0, NULL
    // 0x80d218: b.ne            #0x80d224
    // 0x80d21c: r0 = 0
    //     0x80d21c: movz            x0, #0
    // 0x80d220: b               #0x80d234
    // 0x80d224: r1 = LoadInt32Instr(r0)
    //     0x80d224: sbfx            x1, x0, #1, #0x1f
    //     0x80d228: tbz             w0, #0, #0x80d230
    //     0x80d22c: ldur            x1, [x0, #7]
    // 0x80d230: mov             x0, x1
    // 0x80d234: ldur            x1, [fp, #-8]
    // 0x80d238: StoreField: r1->field_1f = r0
    //     0x80d238: stur            x0, [x1, #0x1f]
    // 0x80d23c: r0 = GetNavigation.arguments()
    //     0x80d23c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d240: r16 = "title"
    //     0x80d240: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x80d244: ldr             x16, [x16, #0x748]
    // 0x80d248: stp             x16, x0, [SP]
    // 0x80d24c: r4 = 0
    //     0x80d24c: movz            x4, #0
    // 0x80d250: ldr             x0, [SP, #8]
    // 0x80d254: r16 = UnlinkedCall_0x5f3c08
    //     0x80d254: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a80] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d258: add             x16, x16, #0xa80
    // 0x80d25c: ldp             x5, lr, [x16]
    // 0x80d260: blr             lr
    // 0x80d264: mov             x3, x0
    // 0x80d268: r2 = Null
    //     0x80d268: mov             x2, NULL
    // 0x80d26c: r1 = Null
    //     0x80d26c: mov             x1, NULL
    // 0x80d270: stur            x3, [fp, #-0x10]
    // 0x80d274: r4 = 60
    //     0x80d274: movz            x4, #0x3c
    // 0x80d278: branchIfSmi(r0, 0x80d284)
    //     0x80d278: tbz             w0, #0, #0x80d284
    // 0x80d27c: r4 = LoadClassIdInstr(r0)
    //     0x80d27c: ldur            x4, [x0, #-1]
    //     0x80d280: ubfx            x4, x4, #0xc, #0x14
    // 0x80d284: sub             x4, x4, #0x5e
    // 0x80d288: cmp             x4, #1
    // 0x80d28c: b.ls            #0x80d2a0
    // 0x80d290: r8 = String
    //     0x80d290: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80d294: r3 = Null
    //     0x80d294: add             x3, PP, #0x36, lsl #12  ; [pp+0x36a90] Null
    //     0x80d298: ldr             x3, [x3, #0xa90]
    // 0x80d29c: r0 = String()
    //     0x80d29c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80d2a0: ldur            x0, [fp, #-0x10]
    // 0x80d2a4: ldur            x1, [fp, #-8]
    // 0x80d2a8: StoreField: r1->field_27 = r0
    //     0x80d2a8: stur            w0, [x1, #0x27]
    //     0x80d2ac: ldurb           w16, [x1, #-1]
    //     0x80d2b0: ldurb           w17, [x0, #-1]
    //     0x80d2b4: and             x16, x17, x16, lsr #2
    //     0x80d2b8: tst             x16, HEAP, lsr #32
    //     0x80d2bc: b.eq            #0x80d2c4
    //     0x80d2c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80d2c4: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80d2c4: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80d2c8: r0 = Null
    //     0x80d2c8: mov             x0, NULL
    // 0x80d2cc: LeaveFrame
    //     0x80d2cc: mov             SP, fp
    //     0x80d2d0: ldp             fp, lr, [SP], #0x10
    // 0x80d2d4: ret
    //     0x80d2d4: ret             
    // 0x80d2d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d2d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d2dc: b               #0x80d0cc
  }
  const int dyn:get:id(ArticleCategoryController) {
    // ** addr: 0x80d2f8, size: 0x48
    // 0x80d2f8: ldr             x2, [SP]
    // 0x80d2fc: LoadField: r3 = r2->field_1f
    //     0x80d2fc: ldur            x3, [x2, #0x1f]
    // 0x80d300: r0 = BoxInt64Instr(r3)
    //     0x80d300: sbfiz           x0, x3, #1, #0x1f
    //     0x80d304: cmp             x3, x0, asr #1
    //     0x80d308: b.eq            #0x80d324
    //     0x80d30c: stp             fp, lr, [SP, #-0x10]!
    //     0x80d310: mov             fp, SP
    //     0x80d314: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80d318: mov             SP, fp
    //     0x80d31c: ldp             fp, lr, [SP], #0x10
    //     0x80d320: stur            x3, [x0, #7]
    // 0x80d324: ret
    //     0x80d324: ret             
  }
  get _ newestTitle(/* No info */) {
    // ** addr: 0xbbfeac, size: 0x70
    // 0xbbfeac: EnterFrame
    //     0xbbfeac: stp             fp, lr, [SP, #-0x10]!
    //     0xbbfeb0: mov             fp, SP
    // 0xbbfeb4: AllocStack(0x10)
    //     0xbbfeb4: sub             SP, SP, #0x10
    // 0xbbfeb8: SetupParameters(ArticleCategoryController this /* r1 => r0, fp-0x8 */)
    //     0xbbfeb8: mov             x0, x1
    //     0xbbfebc: stur            x1, [fp, #-8]
    // 0xbbfec0: CheckStackOverflow
    //     0xbbfec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbfec4: cmp             SP, x16
    //     0xbbfec8: b.ls            #0xbbff14
    // 0xbbfecc: r1 = Null
    //     0xbbfecc: mov             x1, NULL
    // 0xbbfed0: r2 = 6
    //     0xbbfed0: movz            x2, #0x6
    // 0xbbfed4: r0 = AllocateArray()
    //     0xbbfed4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbfed8: r16 = "Artikel "
    //     0xbbfed8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xbbfedc: ldr             x16, [x16, #0xb90]
    // 0xbbfee0: StoreField: r0->field_f = r16
    //     0xbbfee0: stur            w16, [x0, #0xf]
    // 0xbbfee4: ldur            x1, [fp, #-8]
    // 0xbbfee8: LoadField: r2 = r1->field_27
    //     0xbbfee8: ldur            w2, [x1, #0x27]
    // 0xbbfeec: DecompressPointer r2
    //     0xbbfeec: add             x2, x2, HEAP, lsl #32
    // 0xbbfef0: StoreField: r0->field_13 = r2
    //     0xbbfef0: stur            w2, [x0, #0x13]
    // 0xbbfef4: r16 = " Terbaru"
    //     0xbbfef4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36328] " Terbaru"
    //     0xbbfef8: ldr             x16, [x16, #0x328]
    // 0xbbfefc: ArrayStore: r0[0] = r16  ; List_4
    //     0xbbfefc: stur            w16, [x0, #0x17]
    // 0xbbff00: str             x0, [SP]
    // 0xbbff04: r0 = _interpolate()
    //     0xbbff04: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbbff08: LeaveFrame
    //     0xbbff08: mov             SP, fp
    //     0xbbff0c: ldp             fp, lr, [SP], #0x10
    // 0xbbff10: ret
    //     0xbbff10: ret             
    // 0xbbff14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbff14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbff18: b               #0xbbfecc
  }
  get _ popularTitle(/* No info */) {
    // ** addr: 0xbbff1c, size: 0x70
    // 0xbbff1c: EnterFrame
    //     0xbbff1c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbff20: mov             fp, SP
    // 0xbbff24: AllocStack(0x10)
    //     0xbbff24: sub             SP, SP, #0x10
    // 0xbbff28: SetupParameters(ArticleCategoryController this /* r1 => r0, fp-0x8 */)
    //     0xbbff28: mov             x0, x1
    //     0xbbff2c: stur            x1, [fp, #-8]
    // 0xbbff30: CheckStackOverflow
    //     0xbbff30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbff34: cmp             SP, x16
    //     0xbbff38: b.ls            #0xbbff84
    // 0xbbff3c: r1 = Null
    //     0xbbff3c: mov             x1, NULL
    // 0xbbff40: r2 = 6
    //     0xbbff40: movz            x2, #0x6
    // 0xbbff44: r0 = AllocateArray()
    //     0xbbff44: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbff48: r16 = "Artikel "
    //     0xbbff48: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xbbff4c: ldr             x16, [x16, #0xb90]
    // 0xbbff50: StoreField: r0->field_f = r16
    //     0xbbff50: stur            w16, [x0, #0xf]
    // 0xbbff54: ldur            x1, [fp, #-8]
    // 0xbbff58: LoadField: r2 = r1->field_27
    //     0xbbff58: ldur            w2, [x1, #0x27]
    // 0xbbff5c: DecompressPointer r2
    //     0xbbff5c: add             x2, x2, HEAP, lsl #32
    // 0xbbff60: StoreField: r0->field_13 = r2
    //     0xbbff60: stur            w2, [x0, #0x13]
    // 0xbbff64: r16 = " Terpopuler"
    //     0xbbff64: add             x16, PP, #0x36, lsl #12  ; [pp+0x36320] " Terpopuler"
    //     0xbbff68: ldr             x16, [x16, #0x320]
    // 0xbbff6c: ArrayStore: r0[0] = r16  ; List_4
    //     0xbbff6c: stur            w16, [x0, #0x17]
    // 0xbbff70: str             x0, [SP]
    // 0xbbff74: r0 = _interpolate()
    //     0xbbff74: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbbff78: LeaveFrame
    //     0xbbff78: mov             SP, fp
    //     0xbbff7c: ldp             fp, lr, [SP], #0x10
    // 0xbbff80: ret
    //     0xbbff80: ret             
    // 0xbbff84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbff84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbff88: b               #0xbbff3c
  }
}
