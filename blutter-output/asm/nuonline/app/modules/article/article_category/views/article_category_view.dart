// lib: , url: package:nuonline/app/modules/article/article_category/views/article_category_view.dart

// class id: 1050123, size: 0x8
class :: {
}

// class id: 4452, size: 0x14, field offset: 0x14
class ArticleCategoryView extends GetWidget<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0x862340, size: 0x17c
    // 0x862340: EnterFrame
    //     0x862340: stp             fp, lr, [SP, #-0x10]!
    //     0x862344: mov             fp, SP
    // 0x862348: AllocStack(0x18)
    //     0x862348: sub             SP, SP, #0x18
    // 0x86234c: CheckStackOverflow
    //     0x86234c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862350: cmp             SP, x16
    //     0x862354: b.ls            #0x8624b4
    // 0x862358: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x862358: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86235c: ldr             x0, [x0, #0x2670]
    //     0x862360: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x862364: cmp             w0, w16
    //     0x862368: b.ne            #0x862374
    //     0x86236c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x862370: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x862374: r0 = GetNavigation.arguments()
    //     0x862374: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x862378: r16 = "id"
    //     0x862378: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x86237c: ldr             x16, [x16, #0x740]
    // 0x862380: stp             x16, x0, [SP]
    // 0x862384: r4 = 0
    //     0x862384: movz            x4, #0
    // 0x862388: ldr             x0, [SP, #8]
    // 0x86238c: r5 = UnlinkedCall_0x5f3c08
    //     0x86238c: add             x16, PP, #0x41, lsl #12  ; [pp+0x41160] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x862390: ldp             x5, lr, [x16, #0x160]
    // 0x862394: blr             lr
    // 0x862398: r1 = 60
    //     0x862398: movz            x1, #0x3c
    // 0x86239c: branchIfSmi(r0, 0x8623a8)
    //     0x86239c: tbz             w0, #0, #0x8623a8
    // 0x8623a0: r1 = LoadClassIdInstr(r0)
    //     0x8623a0: ldur            x1, [x0, #-1]
    //     0x8623a4: ubfx            x1, x1, #0xc, #0x14
    // 0x8623a8: sub             x16, x1, #0x5e
    // 0x8623ac: cmp             x16, #1
    // 0x8623b0: b.hi            #0x86241c
    // 0x8623b4: r0 = GetNavigation.arguments()
    //     0x8623b4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8623b8: r16 = "id"
    //     0x8623b8: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8623bc: ldr             x16, [x16, #0x740]
    // 0x8623c0: stp             x16, x0, [SP]
    // 0x8623c4: r4 = 0
    //     0x8623c4: movz            x4, #0
    // 0x8623c8: ldr             x0, [SP, #8]
    // 0x8623cc: r5 = UnlinkedCall_0x5f3c08
    //     0x8623cc: add             x16, PP, #0x41, lsl #12  ; [pp+0x41170] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8623d0: ldp             x5, lr, [x16, #0x170]
    // 0x8623d4: blr             lr
    // 0x8623d8: mov             x3, x0
    // 0x8623dc: r2 = Null
    //     0x8623dc: mov             x2, NULL
    // 0x8623e0: r1 = Null
    //     0x8623e0: mov             x1, NULL
    // 0x8623e4: stur            x3, [fp, #-8]
    // 0x8623e8: r4 = 60
    //     0x8623e8: movz            x4, #0x3c
    // 0x8623ec: branchIfSmi(r0, 0x8623f8)
    //     0x8623ec: tbz             w0, #0, #0x8623f8
    // 0x8623f0: r4 = LoadClassIdInstr(r0)
    //     0x8623f0: ldur            x4, [x0, #-1]
    //     0x8623f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8623f8: sub             x4, x4, #0x5e
    // 0x8623fc: cmp             x4, #1
    // 0x862400: b.ls            #0x862414
    // 0x862404: r8 = String
    //     0x862404: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x862408: r3 = Null
    //     0x862408: add             x3, PP, #0x41, lsl #12  ; [pp+0x41180] Null
    //     0x86240c: ldr             x3, [x3, #0x180]
    // 0x862410: r0 = String()
    //     0x862410: bl              #0xed43b0  ; IsType_String_Stub
    // 0x862414: ldur            x0, [fp, #-8]
    // 0x862418: b               #0x8624a8
    // 0x86241c: r0 = GetNavigation.arguments()
    //     0x86241c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x862420: r16 = "id"
    //     0x862420: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x862424: ldr             x16, [x16, #0x740]
    // 0x862428: stp             x16, x0, [SP]
    // 0x86242c: r4 = 0
    //     0x86242c: movz            x4, #0
    // 0x862430: ldr             x0, [SP, #8]
    // 0x862434: r5 = UnlinkedCall_0x5f3c08
    //     0x862434: add             x16, PP, #0x41, lsl #12  ; [pp+0x41190] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x862438: ldp             x5, lr, [x16, #0x190]
    // 0x86243c: blr             lr
    // 0x862440: mov             x3, x0
    // 0x862444: r2 = Null
    //     0x862444: mov             x2, NULL
    // 0x862448: r1 = Null
    //     0x862448: mov             x1, NULL
    // 0x86244c: stur            x3, [fp, #-8]
    // 0x862450: branchIfSmi(r0, 0x862478)
    //     0x862450: tbz             w0, #0, #0x862478
    // 0x862454: r4 = LoadClassIdInstr(r0)
    //     0x862454: ldur            x4, [x0, #-1]
    //     0x862458: ubfx            x4, x4, #0xc, #0x14
    // 0x86245c: sub             x4, x4, #0x3c
    // 0x862460: cmp             x4, #1
    // 0x862464: b.ls            #0x862478
    // 0x862468: r8 = int
    //     0x862468: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x86246c: r3 = Null
    //     0x86246c: add             x3, PP, #0x41, lsl #12  ; [pp+0x411a0] Null
    //     0x862470: ldr             x3, [x3, #0x1a0]
    // 0x862474: r0 = int()
    //     0x862474: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x862478: ldur            x0, [fp, #-8]
    // 0x86247c: r1 = 60
    //     0x86247c: movz            x1, #0x3c
    // 0x862480: branchIfSmi(r0, 0x86248c)
    //     0x862480: tbz             w0, #0, #0x86248c
    // 0x862484: r1 = LoadClassIdInstr(r0)
    //     0x862484: ldur            x1, [x0, #-1]
    //     0x862488: ubfx            x1, x1, #0xc, #0x14
    // 0x86248c: str             x0, [SP]
    // 0x862490: mov             x0, x1
    // 0x862494: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x862494: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x862498: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x862498: movz            x17, #0x2b03
    //     0x86249c: add             lr, x0, x17
    //     0x8624a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8624a4: blr             lr
    // 0x8624a8: LeaveFrame
    //     0x8624a8: mov             SP, fp
    //     0x8624ac: ldp             fp, lr, [SP], #0x10
    // 0x8624b0: ret
    //     0x8624b0: ret             
    // 0x8624b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8624b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8624b8: b               #0x862358
  }
  _ build(/* No info */) {
    // ** addr: 0xbbf964, size: 0x548
    // 0xbbf964: EnterFrame
    //     0xbbf964: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf968: mov             fp, SP
    // 0xbbf96c: AllocStack(0x48)
    //     0xbbf96c: sub             SP, SP, #0x48
    // 0xbbf970: SetupParameters(ArticleCategoryView this /* r1 => r0, fp-0x8 */)
    //     0xbbf970: mov             x0, x1
    //     0xbbf974: stur            x1, [fp, #-8]
    // 0xbbf978: CheckStackOverflow
    //     0xbbf978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf97c: cmp             SP, x16
    //     0xbbf980: b.ls            #0xbbfea4
    // 0xbbf984: r1 = 1
    //     0xbbf984: movz            x1, #0x1
    // 0xbbf988: r0 = AllocateContext()
    //     0xbbf988: bl              #0xec126c  ; AllocateContextStub
    // 0xbbf98c: ldur            x2, [fp, #-8]
    // 0xbbf990: stur            x0, [fp, #-0x10]
    // 0xbbf994: StoreField: r0->field_f = r2
    //     0xbbf994: stur            w2, [x0, #0xf]
    // 0xbbf998: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbbf998: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbf99c: ldr             x0, [x0, #0x26d0]
    //     0xbbf9a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbf9a4: cmp             w0, w16
    //     0xbbf9a8: b.ne            #0xbbf9b8
    //     0xbbf9ac: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbbf9b0: ldr             x2, [x2, #0xb90]
    //     0xbbf9b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbf9b8: mov             x1, x0
    // 0xbbf9bc: ldur            x2, [fp, #-8]
    // 0xbbf9c0: stur            x0, [fp, #-0x18]
    // 0xbbf9c4: r0 = []()
    //     0xbbf9c4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbf9c8: mov             x4, x0
    // 0xbbf9cc: ldur            x3, [fp, #-8]
    // 0xbbf9d0: stur            x4, [fp, #-0x28]
    // 0xbbf9d4: LoadField: r5 = r3->field_b
    //     0xbbf9d4: ldur            w5, [x3, #0xb]
    // 0xbbf9d8: DecompressPointer r5
    //     0xbbf9d8: add             x5, x5, HEAP, lsl #32
    // 0xbbf9dc: mov             x0, x4
    // 0xbbf9e0: mov             x2, x5
    // 0xbbf9e4: stur            x5, [fp, #-0x20]
    // 0xbbf9e8: r1 = Null
    //     0xbbf9e8: mov             x1, NULL
    // 0xbbf9ec: cmp             w2, NULL
    // 0xbbf9f0: b.eq            #0xbbfa14
    // 0xbbf9f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbf9f4: ldur            w4, [x2, #0x17]
    // 0xbbf9f8: DecompressPointer r4
    //     0xbbf9f8: add             x4, x4, HEAP, lsl #32
    // 0xbbf9fc: r8 = X0 bound GetLifeCycleBase?
    //     0xbbf9fc: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbfa00: ldr             x8, [x8, #0xb98]
    // 0xbbfa04: LoadField: r9 = r4->field_7
    //     0xbbfa04: ldur            x9, [x4, #7]
    // 0xbbfa08: r3 = Null
    //     0xbbfa08: add             x3, PP, #0x41, lsl #12  ; [pp+0x410f0] Null
    //     0xbbfa0c: ldr             x3, [x3, #0xf0]
    // 0xbbfa10: blr             x9
    // 0xbbfa14: ldur            x0, [fp, #-0x28]
    // 0xbbfa18: LoadField: r1 = r0->field_27
    //     0xbbfa18: ldur            w1, [x0, #0x27]
    // 0xbbfa1c: DecompressPointer r1
    //     0xbbfa1c: add             x1, x1, HEAP, lsl #32
    // 0xbbfa20: stur            x1, [fp, #-0x30]
    // 0xbbfa24: r0 = Text()
    //     0xbbfa24: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbfa28: mov             x1, x0
    // 0xbbfa2c: ldur            x0, [fp, #-0x30]
    // 0xbbfa30: stur            x1, [fp, #-0x28]
    // 0xbbfa34: StoreField: r1->field_b = r0
    //     0xbbfa34: stur            w0, [x1, #0xb]
    // 0xbbfa38: r0 = AppBar()
    //     0xbbfa38: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xbbfa3c: stur            x0, [fp, #-0x30]
    // 0xbbfa40: ldur            x16, [fp, #-0x28]
    // 0xbbfa44: str             x16, [SP]
    // 0xbbfa48: mov             x1, x0
    // 0xbbfa4c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xbbfa4c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xbbfa50: ldr             x4, [x4, #0x6e8]
    // 0xbbfa54: r0 = AppBar()
    //     0xbbfa54: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xbbfa58: ldur            x1, [fp, #-0x18]
    // 0xbbfa5c: ldur            x2, [fp, #-8]
    // 0xbbfa60: r0 = []()
    //     0xbbfa60: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbfa64: ldur            x2, [fp, #-0x20]
    // 0xbbfa68: mov             x3, x0
    // 0xbbfa6c: r1 = Null
    //     0xbbfa6c: mov             x1, NULL
    // 0xbbfa70: stur            x3, [fp, #-0x28]
    // 0xbbfa74: cmp             w2, NULL
    // 0xbbfa78: b.eq            #0xbbfa9c
    // 0xbbfa7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbfa7c: ldur            w4, [x2, #0x17]
    // 0xbbfa80: DecompressPointer r4
    //     0xbbfa80: add             x4, x4, HEAP, lsl #32
    // 0xbbfa84: r8 = X0 bound GetLifeCycleBase?
    //     0xbbfa84: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbfa88: ldr             x8, [x8, #0xb98]
    // 0xbbfa8c: LoadField: r9 = r4->field_7
    //     0xbbfa8c: ldur            x9, [x4, #7]
    // 0xbbfa90: r3 = Null
    //     0xbbfa90: add             x3, PP, #0x41, lsl #12  ; [pp+0x41100] Null
    //     0xbbfa94: ldr             x3, [x3, #0x100]
    // 0xbbfa98: blr             x9
    // 0xbbfa9c: ldur            x0, [fp, #-0x28]
    // 0xbbfaa0: LoadField: r3 = r0->field_1f
    //     0xbbfaa0: ldur            x3, [x0, #0x1f]
    // 0xbbfaa4: ldur            x1, [fp, #-0x18]
    // 0xbbfaa8: ldur            x2, [fp, #-8]
    // 0xbbfaac: stur            x3, [fp, #-0x38]
    // 0xbbfab0: r0 = []()
    //     0xbbfab0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbfab4: ldur            x2, [fp, #-0x20]
    // 0xbbfab8: mov             x3, x0
    // 0xbbfabc: r1 = Null
    //     0xbbfabc: mov             x1, NULL
    // 0xbbfac0: stur            x3, [fp, #-0x28]
    // 0xbbfac4: cmp             w2, NULL
    // 0xbbfac8: b.eq            #0xbbfaec
    // 0xbbfacc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbfacc: ldur            w4, [x2, #0x17]
    // 0xbbfad0: DecompressPointer r4
    //     0xbbfad0: add             x4, x4, HEAP, lsl #32
    // 0xbbfad4: r8 = X0 bound GetLifeCycleBase?
    //     0xbbfad4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbfad8: ldr             x8, [x8, #0xb98]
    // 0xbbfadc: LoadField: r9 = r4->field_7
    //     0xbbfadc: ldur            x9, [x4, #7]
    // 0xbbfae0: r3 = Null
    //     0xbbfae0: add             x3, PP, #0x41, lsl #12  ; [pp+0x41110] Null
    //     0xbbfae4: ldr             x3, [x3, #0x110]
    // 0xbbfae8: blr             x9
    // 0xbbfaec: ldur            x1, [fp, #-0x28]
    // 0xbbfaf0: r0 = popularTitle()
    //     0xbbfaf0: bl              #0xbbff1c  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_controller.dart] ArticleCategoryController::popularTitle
    // 0xbbfaf4: ldur            x1, [fp, #-0x18]
    // 0xbbfaf8: ldur            x2, [fp, #-8]
    // 0xbbfafc: r0 = []()
    //     0xbbfafc: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbfb00: ldur            x2, [fp, #-0x20]
    // 0xbbfb04: mov             x3, x0
    // 0xbbfb08: r1 = Null
    //     0xbbfb08: mov             x1, NULL
    // 0xbbfb0c: stur            x3, [fp, #-0x28]
    // 0xbbfb10: cmp             w2, NULL
    // 0xbbfb14: b.eq            #0xbbfb38
    // 0xbbfb18: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbfb18: ldur            w4, [x2, #0x17]
    // 0xbbfb1c: DecompressPointer r4
    //     0xbbfb1c: add             x4, x4, HEAP, lsl #32
    // 0xbbfb20: r8 = X0 bound GetLifeCycleBase?
    //     0xbbfb20: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbfb24: ldr             x8, [x8, #0xb98]
    // 0xbbfb28: LoadField: r9 = r4->field_7
    //     0xbbfb28: ldur            x9, [x4, #7]
    // 0xbbfb2c: r3 = Null
    //     0xbbfb2c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41120] Null
    //     0xbbfb30: ldr             x3, [x3, #0x120]
    // 0xbbfb34: blr             x9
    // 0xbbfb38: ldur            x1, [fp, #-0x28]
    // 0xbbfb3c: r0 = newestTitle()
    //     0xbbfb3c: bl              #0xbbfeac  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_controller.dart] ArticleCategoryController::newestTitle
    // 0xbbfb40: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbbfb40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbfb44: ldr             x0, [x0, #0x2670]
    //     0xbbfb48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbfb4c: cmp             w0, w16
    //     0xbbfb50: b.ne            #0xbbfb5c
    //     0xbbfb54: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbbfb58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbfb5c: r16 = <ArticleRepository>
    //     0xbbfb5c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0xbbfb60: ldr             x16, [x16, #0x98]
    // 0xbbfb64: r30 = "article_repo"
    //     0xbbfb64: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0xbbfb68: ldr             lr, [lr, #0xa0]
    // 0xbbfb6c: stp             lr, x16, [SP]
    // 0xbbfb70: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xbbfb70: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xbbfb74: r0 = Inst.find()
    //     0xbbfb74: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbbfb78: stur            x0, [fp, #-0x28]
    // 0xbbfb7c: r0 = ArticleCategoryBuilderController()
    //     0xbbfb7c: bl              #0xad44d4  ; AllocateArticleCategoryBuilderControllerStub -> ArticleCategoryBuilderController (size=0x54)
    // 0xbbfb80: mov             x1, x0
    // 0xbbfb84: ldur            x2, [fp, #-0x38]
    // 0xbbfb88: ldur            x3, [fp, #-0x28]
    // 0xbbfb8c: stur            x0, [fp, #-0x28]
    // 0xbbfb90: r0 = ArticleCategoryBuilderController()
    //     0xbbfb90: bl              #0xad4334  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::ArticleCategoryBuilderController
    // 0xbbfb94: ldur            x1, [fp, #-0x18]
    // 0xbbfb98: ldur            x2, [fp, #-8]
    // 0xbbfb9c: r0 = []()
    //     0xbbfb9c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbfba0: ldur            x2, [fp, #-0x20]
    // 0xbbfba4: mov             x3, x0
    // 0xbbfba8: r1 = Null
    //     0xbbfba8: mov             x1, NULL
    // 0xbbfbac: stur            x3, [fp, #-8]
    // 0xbbfbb0: cmp             w2, NULL
    // 0xbbfbb4: b.eq            #0xbbfbd8
    // 0xbbfbb8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbfbb8: ldur            w4, [x2, #0x17]
    // 0xbbfbbc: DecompressPointer r4
    //     0xbbfbbc: add             x4, x4, HEAP, lsl #32
    // 0xbbfbc0: r8 = X0 bound GetLifeCycleBase?
    //     0xbbfbc0: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbbfbc4: ldr             x8, [x8, #0xb98]
    // 0xbbfbc8: LoadField: r9 = r4->field_7
    //     0xbbfbc8: ldur            x9, [x4, #7]
    // 0xbbfbcc: r3 = Null
    //     0xbbfbcc: add             x3, PP, #0x41, lsl #12  ; [pp+0x41130] Null
    //     0xbbfbd0: ldr             x3, [x3, #0x130]
    // 0xbbfbd4: blr             x9
    // 0xbbfbd8: ldur            x0, [fp, #-8]
    // 0xbbfbdc: LoadField: r2 = r0->field_1f
    //     0xbbfbdc: ldur            x2, [x0, #0x1f]
    // 0xbbfbe0: r0 = BoxInt64Instr(r2)
    //     0xbbfbe0: sbfiz           x0, x2, #1, #0x1f
    //     0xbbfbe4: cmp             x2, x0, asr #1
    //     0xbbfbe8: b.eq            #0xbbfbf4
    //     0xbbfbec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbfbf0: stur            x2, [x0, #7]
    // 0xbbfbf4: r1 = 60
    //     0xbbfbf4: movz            x1, #0x3c
    // 0xbbfbf8: branchIfSmi(r0, 0xbbfc04)
    //     0xbbfbf8: tbz             w0, #0, #0xbbfc04
    // 0xbbfbfc: r1 = LoadClassIdInstr(r0)
    //     0xbbfbfc: ldur            x1, [x0, #-1]
    //     0xbbfc00: ubfx            x1, x1, #0xc, #0x14
    // 0xbbfc04: str             x0, [SP]
    // 0xbbfc08: mov             x0, x1
    // 0xbbfc0c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbbfc0c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbbfc10: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbbfc10: movz            x17, #0x2b03
    //     0xbbfc14: add             lr, x0, x17
    //     0xbbfc18: ldr             lr, [x21, lr, lsl #3]
    //     0xbbfc1c: blr             lr
    // 0xbbfc20: r1 = <ArticleCategoryBuilderController>
    //     0xbbfc20: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xbbfc24: ldr             x1, [x1, #0x300]
    // 0xbbfc28: stur            x0, [fp, #-8]
    // 0xbbfc2c: r0 = GetBuilder()
    //     0xbbfc2c: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xbbfc30: mov             x3, x0
    // 0xbbfc34: ldur            x0, [fp, #-0x28]
    // 0xbbfc38: stur            x3, [fp, #-0x18]
    // 0xbbfc3c: StoreField: r3->field_3b = r0
    //     0xbbfc3c: stur            w0, [x3, #0x3b]
    // 0xbbfc40: r0 = true
    //     0xbbfc40: add             x0, NULL, #0x20  ; true
    // 0xbbfc44: StoreField: r3->field_13 = r0
    //     0xbbfc44: stur            w0, [x3, #0x13]
    // 0xbbfc48: ldur            x2, [fp, #-0x10]
    // 0xbbfc4c: r1 = Function '<anonymous closure>':.
    //     0xbbfc4c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41140] AnonymousClosure: (0xbbff8c), in [package:nuonline/app/modules/article/article_category/views/article_category_view.dart] ArticleCategoryView::build (0xbbf964)
    //     0xbbfc50: ldr             x1, [x1, #0x140]
    // 0xbbfc54: r0 = AllocateClosure()
    //     0xbbfc54: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbfc58: mov             x1, x0
    // 0xbbfc5c: ldur            x0, [fp, #-0x18]
    // 0xbbfc60: StoreField: r0->field_f = r1
    //     0xbbfc60: stur            w1, [x0, #0xf]
    // 0xbbfc64: r3 = true
    //     0xbbfc64: add             x3, NULL, #0x20  ; true
    // 0xbbfc68: StoreField: r0->field_1f = r3
    //     0xbbfc68: stur            w3, [x0, #0x1f]
    // 0xbbfc6c: r4 = false
    //     0xbbfc6c: add             x4, NULL, #0x30  ; false
    // 0xbbfc70: StoreField: r0->field_23 = r4
    //     0xbbfc70: stur            w4, [x0, #0x23]
    // 0xbbfc74: r1 = Function '<anonymous closure>':.
    //     0xbbfc74: add             x1, PP, #0x41, lsl #12  ; [pp+0x41148] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xbbfc78: ldr             x1, [x1, #0x148]
    // 0xbbfc7c: r2 = Null
    //     0xbbfc7c: mov             x2, NULL
    // 0xbbfc80: r0 = AllocateClosure()
    //     0xbbfc80: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbfc84: mov             x1, x0
    // 0xbbfc88: ldur            x0, [fp, #-0x18]
    // 0xbbfc8c: StoreField: r0->field_2b = r1
    //     0xbbfc8c: stur            w1, [x0, #0x2b]
    // 0xbbfc90: ldur            x1, [fp, #-8]
    // 0xbbfc94: StoreField: r0->field_1b = r1
    //     0xbbfc94: stur            w1, [x0, #0x1b]
    // 0xbbfc98: r1 = <FlexParentData>
    //     0xbbfc98: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbbfc9c: ldr             x1, [x1, #0x720]
    // 0xbbfca0: r0 = Expanded()
    //     0xbbfca0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbfca4: mov             x3, x0
    // 0xbbfca8: r0 = 1
    //     0xbbfca8: movz            x0, #0x1
    // 0xbbfcac: stur            x3, [fp, #-8]
    // 0xbbfcb0: StoreField: r3->field_13 = r0
    //     0xbbfcb0: stur            x0, [x3, #0x13]
    // 0xbbfcb4: r0 = Instance_FlexFit
    //     0xbbfcb4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbbfcb8: ldr             x0, [x0, #0x728]
    // 0xbbfcbc: StoreField: r3->field_1b = r0
    //     0xbbfcbc: stur            w0, [x3, #0x1b]
    // 0xbbfcc0: ldur            x0, [fp, #-0x18]
    // 0xbbfcc4: StoreField: r3->field_b = r0
    //     0xbbfcc4: stur            w0, [x3, #0xb]
    // 0xbbfcc8: r1 = Null
    //     0xbbfcc8: mov             x1, NULL
    // 0xbbfccc: r2 = 2
    //     0xbbfccc: movz            x2, #0x2
    // 0xbbfcd0: r0 = AllocateArray()
    //     0xbbfcd0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbfcd4: mov             x2, x0
    // 0xbbfcd8: ldur            x0, [fp, #-8]
    // 0xbbfcdc: stur            x2, [fp, #-0x10]
    // 0xbbfce0: StoreField: r2->field_f = r0
    //     0xbbfce0: stur            w0, [x2, #0xf]
    // 0xbbfce4: r1 = <Widget>
    //     0xbbfce4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbfce8: r0 = AllocateGrowableArray()
    //     0xbbfce8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbfcec: mov             x1, x0
    // 0xbbfcf0: ldur            x0, [fp, #-0x10]
    // 0xbbfcf4: stur            x1, [fp, #-8]
    // 0xbbfcf8: StoreField: r1->field_f = r0
    //     0xbbfcf8: stur            w0, [x1, #0xf]
    // 0xbbfcfc: r0 = 2
    //     0xbbfcfc: movz            x0, #0x2
    // 0xbbfd00: StoreField: r1->field_b = r0
    //     0xbbfd00: stur            w0, [x1, #0xb]
    // 0xbbfd04: r0 = find()
    //     0xbbfd04: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbfd08: mov             x1, x0
    // 0xbbfd0c: r0 = _adsVisibility()
    //     0xbbfd0c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbfd10: mov             x2, x0
    // 0xbbfd14: r1 = Null
    //     0xbbfd14: mov             x1, NULL
    // 0xbbfd18: r0 = AdsConfig.fromJson()
    //     0xbbfd18: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbfd1c: LoadField: r1 = r0->field_f
    //     0xbbfd1c: ldur            w1, [x0, #0xf]
    // 0xbbfd20: DecompressPointer r1
    //     0xbbfd20: add             x1, x1, HEAP, lsl #32
    // 0xbbfd24: LoadField: r0 = r1->field_7
    //     0xbbfd24: ldur            w0, [x1, #7]
    // 0xbbfd28: DecompressPointer r0
    //     0xbbfd28: add             x0, x0, HEAP, lsl #32
    // 0xbbfd2c: tbnz            w0, #4, #0xbbfdf0
    // 0xbbfd30: ldur            x1, [fp, #-8]
    // 0xbbfd34: r0 = find()
    //     0xbbfd34: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbbfd38: mov             x1, x0
    // 0xbbfd3c: r0 = _adsVisibility()
    //     0xbbfd3c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbbfd40: mov             x2, x0
    // 0xbbfd44: r1 = Null
    //     0xbbfd44: mov             x1, NULL
    // 0xbbfd48: r0 = AdsConfig.fromJson()
    //     0xbbfd48: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbbfd4c: LoadField: r1 = r0->field_f
    //     0xbbfd4c: ldur            w1, [x0, #0xf]
    // 0xbbfd50: DecompressPointer r1
    //     0xbbfd50: add             x1, x1, HEAP, lsl #32
    // 0xbbfd54: LoadField: r0 = r1->field_f
    //     0xbbfd54: ldur            w0, [x1, #0xf]
    // 0xbbfd58: DecompressPointer r0
    //     0xbbfd58: add             x0, x0, HEAP, lsl #32
    // 0xbbfd5c: stur            x0, [fp, #-0x10]
    // 0xbbfd60: r0 = AdmobBannerWidget()
    //     0xbbfd60: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xbbfd64: mov             x2, x0
    // 0xbbfd68: ldur            x0, [fp, #-0x10]
    // 0xbbfd6c: stur            x2, [fp, #-0x18]
    // 0xbbfd70: StoreField: r2->field_b = r0
    //     0xbbfd70: stur            w0, [x2, #0xb]
    // 0xbbfd74: ldur            x0, [fp, #-8]
    // 0xbbfd78: LoadField: r1 = r0->field_b
    //     0xbbfd78: ldur            w1, [x0, #0xb]
    // 0xbbfd7c: LoadField: r3 = r0->field_f
    //     0xbbfd7c: ldur            w3, [x0, #0xf]
    // 0xbbfd80: DecompressPointer r3
    //     0xbbfd80: add             x3, x3, HEAP, lsl #32
    // 0xbbfd84: LoadField: r4 = r3->field_b
    //     0xbbfd84: ldur            w4, [x3, #0xb]
    // 0xbbfd88: r3 = LoadInt32Instr(r1)
    //     0xbbfd88: sbfx            x3, x1, #1, #0x1f
    // 0xbbfd8c: stur            x3, [fp, #-0x38]
    // 0xbbfd90: r1 = LoadInt32Instr(r4)
    //     0xbbfd90: sbfx            x1, x4, #1, #0x1f
    // 0xbbfd94: cmp             x3, x1
    // 0xbbfd98: b.ne            #0xbbfda4
    // 0xbbfd9c: mov             x1, x0
    // 0xbbfda0: r0 = _growToNextCapacity()
    //     0xbbfda0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbfda4: ldur            x2, [fp, #-8]
    // 0xbbfda8: ldur            x3, [fp, #-0x38]
    // 0xbbfdac: add             x0, x3, #1
    // 0xbbfdb0: lsl             x1, x0, #1
    // 0xbbfdb4: StoreField: r2->field_b = r1
    //     0xbbfdb4: stur            w1, [x2, #0xb]
    // 0xbbfdb8: LoadField: r1 = r2->field_f
    //     0xbbfdb8: ldur            w1, [x2, #0xf]
    // 0xbbfdbc: DecompressPointer r1
    //     0xbbfdbc: add             x1, x1, HEAP, lsl #32
    // 0xbbfdc0: ldur            x0, [fp, #-0x18]
    // 0xbbfdc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbfdc4: add             x25, x1, x3, lsl #2
    //     0xbbfdc8: add             x25, x25, #0xf
    //     0xbbfdcc: str             w0, [x25]
    //     0xbbfdd0: tbz             w0, #0, #0xbbfdec
    //     0xbbfdd4: ldurb           w16, [x1, #-1]
    //     0xbbfdd8: ldurb           w17, [x0, #-1]
    //     0xbbfddc: and             x16, x17, x16, lsr #2
    //     0xbbfde0: tst             x16, HEAP, lsr #32
    //     0xbbfde4: b.eq            #0xbbfdec
    //     0xbbfde8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbfdec: b               #0xbbfdf4
    // 0xbbfdf0: ldur            x2, [fp, #-8]
    // 0xbbfdf4: ldur            x0, [fp, #-0x30]
    // 0xbbfdf8: r0 = Column()
    //     0xbbfdf8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbfdfc: mov             x1, x0
    // 0xbbfe00: r0 = Instance_Axis
    //     0xbbfe00: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbbfe04: stur            x1, [fp, #-0x10]
    // 0xbbfe08: StoreField: r1->field_f = r0
    //     0xbbfe08: stur            w0, [x1, #0xf]
    // 0xbbfe0c: r0 = Instance_MainAxisAlignment
    //     0xbbfe0c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbfe10: ldr             x0, [x0, #0x730]
    // 0xbbfe14: StoreField: r1->field_13 = r0
    //     0xbbfe14: stur            w0, [x1, #0x13]
    // 0xbbfe18: r0 = Instance_MainAxisSize
    //     0xbbfe18: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbbfe1c: ldr             x0, [x0, #0x738]
    // 0xbbfe20: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbfe20: stur            w0, [x1, #0x17]
    // 0xbbfe24: r0 = Instance_CrossAxisAlignment
    //     0xbbfe24: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbbfe28: ldr             x0, [x0, #0x740]
    // 0xbbfe2c: StoreField: r1->field_1b = r0
    //     0xbbfe2c: stur            w0, [x1, #0x1b]
    // 0xbbfe30: r0 = Instance_VerticalDirection
    //     0xbbfe30: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbfe34: ldr             x0, [x0, #0x748]
    // 0xbbfe38: StoreField: r1->field_23 = r0
    //     0xbbfe38: stur            w0, [x1, #0x23]
    // 0xbbfe3c: r0 = Instance_Clip
    //     0xbbfe3c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbfe40: ldr             x0, [x0, #0x750]
    // 0xbbfe44: StoreField: r1->field_2b = r0
    //     0xbbfe44: stur            w0, [x1, #0x2b]
    // 0xbbfe48: StoreField: r1->field_2f = rZR
    //     0xbbfe48: stur            xzr, [x1, #0x2f]
    // 0xbbfe4c: ldur            x0, [fp, #-8]
    // 0xbbfe50: StoreField: r1->field_b = r0
    //     0xbbfe50: stur            w0, [x1, #0xb]
    // 0xbbfe54: r0 = Scaffold()
    //     0xbbfe54: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xbbfe58: ldur            x1, [fp, #-0x30]
    // 0xbbfe5c: StoreField: r0->field_13 = r1
    //     0xbbfe5c: stur            w1, [x0, #0x13]
    // 0xbbfe60: ldur            x1, [fp, #-0x10]
    // 0xbbfe64: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbfe64: stur            w1, [x0, #0x17]
    // 0xbbfe68: r1 = Instance_AlignmentDirectional
    //     0xbbfe68: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xbbfe6c: ldr             x1, [x1, #0x758]
    // 0xbbfe70: StoreField: r0->field_2b = r1
    //     0xbbfe70: stur            w1, [x0, #0x2b]
    // 0xbbfe74: r1 = true
    //     0xbbfe74: add             x1, NULL, #0x20  ; true
    // 0xbbfe78: StoreField: r0->field_53 = r1
    //     0xbbfe78: stur            w1, [x0, #0x53]
    // 0xbbfe7c: r2 = Instance_DragStartBehavior
    //     0xbbfe7c: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbbfe80: StoreField: r0->field_57 = r2
    //     0xbbfe80: stur            w2, [x0, #0x57]
    // 0xbbfe84: r2 = false
    //     0xbbfe84: add             x2, NULL, #0x30  ; false
    // 0xbbfe88: StoreField: r0->field_b = r2
    //     0xbbfe88: stur            w2, [x0, #0xb]
    // 0xbbfe8c: StoreField: r0->field_f = r2
    //     0xbbfe8c: stur            w2, [x0, #0xf]
    // 0xbbfe90: StoreField: r0->field_5f = r1
    //     0xbbfe90: stur            w1, [x0, #0x5f]
    // 0xbbfe94: StoreField: r0->field_63 = r1
    //     0xbbfe94: stur            w1, [x0, #0x63]
    // 0xbbfe98: LeaveFrame
    //     0xbbfe98: mov             SP, fp
    //     0xbbfe9c: ldp             fp, lr, [SP], #0x10
    // 0xbbfea0: ret
    //     0xbbfea0: ret             
    // 0xbbfea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbfea4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbfea8: b               #0xbbf984
  }
  [closure] ArticleCategoryBuilderWidget <anonymous closure>(dynamic, ArticleCategoryBuilderController) {
    // ** addr: 0xbbff8c, size: 0x11c
    // 0xbbff8c: EnterFrame
    //     0xbbff8c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbff90: mov             fp, SP
    // 0xbbff94: AllocStack(0x18)
    //     0xbbff94: sub             SP, SP, #0x18
    // 0xbbff98: SetupParameters()
    //     0xbbff98: ldr             x0, [fp, #0x18]
    //     0xbbff9c: ldur            w1, [x0, #0x17]
    //     0xbbffa0: add             x1, x1, HEAP, lsl #32
    // 0xbbffa4: CheckStackOverflow
    //     0xbbffa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbffa8: cmp             SP, x16
    //     0xbbffac: b.ls            #0xbc00a0
    // 0xbbffb0: LoadField: r2 = r1->field_f
    //     0xbbffb0: ldur            w2, [x1, #0xf]
    // 0xbbffb4: DecompressPointer r2
    //     0xbbffb4: add             x2, x2, HEAP, lsl #32
    // 0xbbffb8: stur            x2, [fp, #-8]
    // 0xbbffbc: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbbffbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbffc0: ldr             x0, [x0, #0x26d0]
    //     0xbbffc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbffc8: cmp             w0, w16
    //     0xbbffcc: b.ne            #0xbbffdc
    //     0xbbffd0: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbbffd4: ldr             x2, [x2, #0xb90]
    //     0xbbffd8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbbffdc: mov             x1, x0
    // 0xbbffe0: ldur            x2, [fp, #-8]
    // 0xbbffe4: r0 = []()
    //     0xbbffe4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbbffe8: mov             x3, x0
    // 0xbbffec: ldur            x0, [fp, #-8]
    // 0xbbfff0: stur            x3, [fp, #-0x10]
    // 0xbbfff4: LoadField: r2 = r0->field_b
    //     0xbbfff4: ldur            w2, [x0, #0xb]
    // 0xbbfff8: DecompressPointer r2
    //     0xbbfff8: add             x2, x2, HEAP, lsl #32
    // 0xbbfffc: mov             x0, x3
    // 0xbc0000: r1 = Null
    //     0xbc0000: mov             x1, NULL
    // 0xbc0004: cmp             w2, NULL
    // 0xbc0008: b.eq            #0xbc002c
    // 0xbc000c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc000c: ldur            w4, [x2, #0x17]
    // 0xbc0010: DecompressPointer r4
    //     0xbc0010: add             x4, x4, HEAP, lsl #32
    // 0xbc0014: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0014: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0018: ldr             x8, [x8, #0xb98]
    // 0xbc001c: LoadField: r9 = r4->field_7
    //     0xbc001c: ldur            x9, [x4, #7]
    // 0xbc0020: r3 = Null
    //     0xbc0020: add             x3, PP, #0x41, lsl #12  ; [pp+0x41150] Null
    //     0xbc0024: ldr             x3, [x3, #0x150]
    // 0xbc0028: blr             x9
    // 0xbc002c: ldur            x0, [fp, #-0x10]
    // 0xbc0030: LoadField: r2 = r0->field_1f
    //     0xbc0030: ldur            x2, [x0, #0x1f]
    // 0xbc0034: r0 = BoxInt64Instr(r2)
    //     0xbc0034: sbfiz           x0, x2, #1, #0x1f
    //     0xbc0038: cmp             x2, x0, asr #1
    //     0xbc003c: b.eq            #0xbc0048
    //     0xbc0040: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbc0044: stur            x2, [x0, #7]
    // 0xbc0048: r1 = 60
    //     0xbc0048: movz            x1, #0x3c
    // 0xbc004c: branchIfSmi(r0, 0xbc0058)
    //     0xbc004c: tbz             w0, #0, #0xbc0058
    // 0xbc0050: r1 = LoadClassIdInstr(r0)
    //     0xbc0050: ldur            x1, [x0, #-1]
    //     0xbc0054: ubfx            x1, x1, #0xc, #0x14
    // 0xbc0058: str             x0, [SP]
    // 0xbc005c: mov             x0, x1
    // 0xbc0060: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbc0060: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbc0064: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xbc0064: movz            x17, #0x2b03
    //     0xbc0068: add             lr, x0, x17
    //     0xbc006c: ldr             lr, [x21, lr, lsl #3]
    //     0xbc0070: blr             lr
    // 0xbc0074: r1 = <ArticleCategoryBuilderController>
    //     0xbc0074: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xbc0078: ldr             x1, [x1, #0x300]
    // 0xbc007c: stur            x0, [fp, #-8]
    // 0xbc0080: r0 = ArticleCategoryBuilderWidget()
    //     0xbc0080: bl              #0xad4584  ; AllocateArticleCategoryBuilderWidgetStub -> ArticleCategoryBuilderWidget (size=0x1c)
    // 0xbc0084: ldur            x1, [fp, #-8]
    // 0xbc0088: StoreField: r0->field_13 = r1
    //     0xbc0088: stur            w1, [x0, #0x13]
    // 0xbc008c: r1 = true
    //     0xbc008c: add             x1, NULL, #0x20  ; true
    // 0xbc0090: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc0090: stur            w1, [x0, #0x17]
    // 0xbc0094: LeaveFrame
    //     0xbc0094: mov             SP, fp
    //     0xbc0098: ldp             fp, lr, [SP], #0x10
    // 0xbc009c: ret
    //     0xbc009c: ret             
    // 0xbc00a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc00a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc00a4: b               #0xbbffb0
  }
}
