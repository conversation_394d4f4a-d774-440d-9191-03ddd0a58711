// lib: , url: package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart

// class id: 1050124, size: 0x8
class :: {
}

// class id: 4451, size: 0x1c, field offset: 0x14
//   const constructor, 
class ArticleCategoryBuilderWidget extends GetWidget<dynamic> {

  _OneByteString field_14;
  bool field_18;

  _ buildEmptyResult(/* No info */) {
    // ** addr: 0xad1990, size: 0xe0
    // 0xad1990: EnterFrame
    //     0xad1990: stp             fp, lr, [SP, #-0x10]!
    //     0xad1994: mov             fp, SP
    // 0xad1998: AllocStack(0x18)
    //     0xad1998: sub             SP, SP, #0x18
    // 0xad199c: CheckStackOverflow
    //     0xad199c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad19a0: cmp             SP, x16
    //     0xad19a4: b.ls            #0xad1a58
    // 0xad19a8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad19a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad19ac: ldr             x0, [x0, #0x2670]
    //     0xad19b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad19b4: cmp             w0, w16
    //     0xad19b8: b.ne            #0xad19c4
    //     0xad19bc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad19c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad19c4: r0 = GetNavigation.height()
    //     0xad19c4: bl              #0xad1a70  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.height
    // 0xad19c8: mov             v1.16b, v0.16b
    // 0xad19cc: d0 = 0.390000
    //     0xad19cc: add             x17, PP, #0x2d, lsl #12  ; [pp+0x2d9f8] IMM: double(0.39) from 0x3fd8f5c28f5c28f6
    //     0xad19d0: ldr             d0, [x17, #0x9f8]
    // 0xad19d4: fmul            d2, d1, d0
    // 0xad19d8: stur            d2, [fp, #-0x18]
    // 0xad19dc: r0 = NEmptyState()
    //     0xad19dc: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xad19e0: mov             x1, x0
    // 0xad19e4: r2 = "Periksa kembali kata yang anda cari atau coba cari dengan pencarian populer dibawah ini."
    //     0xad19e4: add             x2, PP, #0x30, lsl #12  ; [pp+0x30950] "Periksa kembali kata yang anda cari atau coba cari dengan pencarian populer dibawah ini."
    //     0xad19e8: ldr             x2, [x2, #0x950]
    // 0xad19ec: r3 = "assets/images/illustration/no_search.svg"
    //     0xad19ec: add             x3, PP, #0x29, lsl #12  ; [pp+0x29138] "assets/images/illustration/no_search.svg"
    //     0xad19f0: ldr             x3, [x3, #0x138]
    // 0xad19f4: r5 = "Artikel yang Dicari Tidak Ditemukan"
    //     0xad19f4: add             x5, PP, #0x30, lsl #12  ; [pp+0x30958] "Artikel yang Dicari Tidak Ditemukan"
    //     0xad19f8: ldr             x5, [x5, #0x958]
    // 0xad19fc: stur            x0, [fp, #-8]
    // 0xad1a00: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xad1a00: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xad1a04: r0 = NEmptyState.svg()
    //     0xad1a04: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xad1a08: ldur            d0, [fp, #-0x18]
    // 0xad1a0c: r0 = inline_Allocate_Double()
    //     0xad1a0c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xad1a10: add             x0, x0, #0x10
    //     0xad1a14: cmp             x1, x0
    //     0xad1a18: b.ls            #0xad1a60
    //     0xad1a1c: str             x0, [THR, #0x50]  ; THR::top
    //     0xad1a20: sub             x0, x0, #0xf
    //     0xad1a24: movz            x1, #0xe15c
    //     0xad1a28: movk            x1, #0x3, lsl #16
    //     0xad1a2c: stur            x1, [x0, #-1]
    // 0xad1a30: StoreField: r0->field_7 = d0
    //     0xad1a30: stur            d0, [x0, #7]
    // 0xad1a34: stur            x0, [fp, #-0x10]
    // 0xad1a38: r0 = SizedBox()
    //     0xad1a38: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad1a3c: ldur            x1, [fp, #-0x10]
    // 0xad1a40: StoreField: r0->field_13 = r1
    //     0xad1a40: stur            w1, [x0, #0x13]
    // 0xad1a44: ldur            x1, [fp, #-8]
    // 0xad1a48: StoreField: r0->field_b = r1
    //     0xad1a48: stur            w1, [x0, #0xb]
    // 0xad1a4c: LeaveFrame
    //     0xad1a4c: mov             SP, fp
    //     0xad1a50: ldp             fp, lr, [SP], #0x10
    // 0xad1a54: ret
    //     0xad1a54: ret             
    // 0xad1a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1a58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1a5c: b               #0xad19a8
    // 0xad1a60: SaveReg d0
    //     0xad1a60: str             q0, [SP, #-0x10]!
    // 0xad1a64: r0 = AllocateDouble()
    //     0xad1a64: bl              #0xec2254  ; AllocateDoubleStub
    // 0xad1a68: RestoreReg d0
    //     0xad1a68: ldr             q0, [SP], #0x10
    // 0xad1a6c: b               #0xad1a30
  }
  _ build(/* No info */) {
    // ** addr: 0xbc00a8, size: 0x39c
    // 0xbc00a8: EnterFrame
    //     0xbc00a8: stp             fp, lr, [SP, #-0x10]!
    //     0xbc00ac: mov             fp, SP
    // 0xbc00b0: AllocStack(0x38)
    //     0xbc00b0: sub             SP, SP, #0x38
    // 0xbc00b4: SetupParameters(ArticleCategoryBuilderWidget this /* r1 => r0, fp-0x8 */)
    //     0xbc00b4: mov             x0, x1
    //     0xbc00b8: stur            x1, [fp, #-8]
    // 0xbc00bc: CheckStackOverflow
    //     0xbc00bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc00c0: cmp             SP, x16
    //     0xbc00c4: b.ls            #0xbc043c
    // 0xbc00c8: r1 = 1
    //     0xbc00c8: movz            x1, #0x1
    // 0xbc00cc: r0 = AllocateContext()
    //     0xbc00cc: bl              #0xec126c  ; AllocateContextStub
    // 0xbc00d0: mov             x3, x0
    // 0xbc00d4: ldur            x0, [fp, #-8]
    // 0xbc00d8: stur            x3, [fp, #-0x10]
    // 0xbc00dc: StoreField: r3->field_f = r0
    //     0xbc00dc: stur            w0, [x3, #0xf]
    // 0xbc00e0: r1 = <Widget>
    //     0xbc00e0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc00e4: r2 = 0
    //     0xbc00e4: movz            x2, #0
    // 0xbc00e8: r0 = _GrowableList()
    //     0xbc00e8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbc00ec: ldur            x2, [fp, #-8]
    // 0xbc00f0: stur            x0, [fp, #-0x18]
    // 0xbc00f4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbc00f4: ldur            w1, [x2, #0x17]
    // 0xbc00f8: DecompressPointer r1
    //     0xbc00f8: add             x1, x1, HEAP, lsl #32
    // 0xbc00fc: tbnz            w1, #4, #0xbc01a0
    // 0xbc0100: r0 = Obx()
    //     0xbc0100: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbc0104: ldur            x2, [fp, #-0x10]
    // 0xbc0108: r1 = Function '<anonymous closure>':.
    //     0xbc0108: add             x1, PP, #0x40, lsl #12  ; [pp+0x40fb0] AnonymousClosure: (0xbc0f38), in [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::build (0xbc00a8)
    //     0xbc010c: ldr             x1, [x1, #0xfb0]
    // 0xbc0110: stur            x0, [fp, #-0x20]
    // 0xbc0114: r0 = AllocateClosure()
    //     0xbc0114: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc0118: mov             x1, x0
    // 0xbc011c: ldur            x0, [fp, #-0x20]
    // 0xbc0120: StoreField: r0->field_b = r1
    //     0xbc0120: stur            w1, [x0, #0xb]
    // 0xbc0124: ldur            x2, [fp, #-0x18]
    // 0xbc0128: LoadField: r1 = r2->field_b
    //     0xbc0128: ldur            w1, [x2, #0xb]
    // 0xbc012c: LoadField: r3 = r2->field_f
    //     0xbc012c: ldur            w3, [x2, #0xf]
    // 0xbc0130: DecompressPointer r3
    //     0xbc0130: add             x3, x3, HEAP, lsl #32
    // 0xbc0134: LoadField: r4 = r3->field_b
    //     0xbc0134: ldur            w4, [x3, #0xb]
    // 0xbc0138: r3 = LoadInt32Instr(r1)
    //     0xbc0138: sbfx            x3, x1, #1, #0x1f
    // 0xbc013c: stur            x3, [fp, #-0x28]
    // 0xbc0140: r1 = LoadInt32Instr(r4)
    //     0xbc0140: sbfx            x1, x4, #1, #0x1f
    // 0xbc0144: cmp             x3, x1
    // 0xbc0148: b.ne            #0xbc0154
    // 0xbc014c: mov             x1, x2
    // 0xbc0150: r0 = _growToNextCapacity()
    //     0xbc0150: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc0154: ldur            x2, [fp, #-0x18]
    // 0xbc0158: ldur            x3, [fp, #-0x28]
    // 0xbc015c: add             x0, x3, #1
    // 0xbc0160: lsl             x1, x0, #1
    // 0xbc0164: StoreField: r2->field_b = r1
    //     0xbc0164: stur            w1, [x2, #0xb]
    // 0xbc0168: LoadField: r1 = r2->field_f
    //     0xbc0168: ldur            w1, [x2, #0xf]
    // 0xbc016c: DecompressPointer r1
    //     0xbc016c: add             x1, x1, HEAP, lsl #32
    // 0xbc0170: ldur            x0, [fp, #-0x20]
    // 0xbc0174: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbc0174: add             x25, x1, x3, lsl #2
    //     0xbc0178: add             x25, x25, #0xf
    //     0xbc017c: str             w0, [x25]
    //     0xbc0180: tbz             w0, #0, #0xbc019c
    //     0xbc0184: ldurb           w16, [x1, #-1]
    //     0xbc0188: ldurb           w17, [x0, #-1]
    //     0xbc018c: and             x16, x17, x16, lsr #2
    //     0xbc0190: tst             x16, HEAP, lsr #32
    //     0xbc0194: b.eq            #0xbc019c
    //     0xbc0198: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc019c: b               #0xbc01a4
    // 0xbc01a0: mov             x2, x0
    // 0xbc01a4: ldur            x0, [fp, #-8]
    // 0xbc01a8: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc01a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc01ac: ldr             x0, [x0, #0x26d0]
    //     0xbc01b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc01b4: cmp             w0, w16
    //     0xbc01b8: b.ne            #0xbc01c8
    //     0xbc01bc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc01c0: ldr             x2, [x2, #0xb90]
    //     0xbc01c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc01c8: mov             x1, x0
    // 0xbc01cc: ldur            x2, [fp, #-8]
    // 0xbc01d0: stur            x0, [fp, #-0x20]
    // 0xbc01d4: r0 = []()
    //     0xbc01d4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc01d8: mov             x4, x0
    // 0xbc01dc: ldur            x3, [fp, #-8]
    // 0xbc01e0: stur            x4, [fp, #-0x38]
    // 0xbc01e4: LoadField: r5 = r3->field_b
    //     0xbc01e4: ldur            w5, [x3, #0xb]
    // 0xbc01e8: DecompressPointer r5
    //     0xbc01e8: add             x5, x5, HEAP, lsl #32
    // 0xbc01ec: mov             x0, x4
    // 0xbc01f0: mov             x2, x5
    // 0xbc01f4: stur            x5, [fp, #-0x30]
    // 0xbc01f8: r1 = Null
    //     0xbc01f8: mov             x1, NULL
    // 0xbc01fc: cmp             w2, NULL
    // 0xbc0200: b.eq            #0xbc0224
    // 0xbc0204: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0204: ldur            w4, [x2, #0x17]
    // 0xbc0208: DecompressPointer r4
    //     0xbc0208: add             x4, x4, HEAP, lsl #32
    // 0xbc020c: r8 = X0 bound GetLifeCycleBase?
    //     0xbc020c: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0210: ldr             x8, [x8, #0xb98]
    // 0xbc0214: LoadField: r9 = r4->field_7
    //     0xbc0214: ldur            x9, [x4, #7]
    // 0xbc0218: r3 = Null
    //     0xbc0218: add             x3, PP, #0x40, lsl #12  ; [pp+0x40fb8] Null
    //     0xbc021c: ldr             x3, [x3, #0xfb8]
    // 0xbc0220: blr             x9
    // 0xbc0224: ldur            x1, [fp, #-0x20]
    // 0xbc0228: ldur            x2, [fp, #-8]
    // 0xbc022c: r0 = []()
    //     0xbc022c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc0230: ldur            x2, [fp, #-0x30]
    // 0xbc0234: mov             x3, x0
    // 0xbc0238: r1 = Null
    //     0xbc0238: mov             x1, NULL
    // 0xbc023c: stur            x3, [fp, #-8]
    // 0xbc0240: cmp             w2, NULL
    // 0xbc0244: b.eq            #0xbc0268
    // 0xbc0248: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0248: ldur            w4, [x2, #0x17]
    // 0xbc024c: DecompressPointer r4
    //     0xbc024c: add             x4, x4, HEAP, lsl #32
    // 0xbc0250: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0250: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0254: ldr             x8, [x8, #0xb98]
    // 0xbc0258: LoadField: r9 = r4->field_7
    //     0xbc0258: ldur            x9, [x4, #7]
    // 0xbc025c: r3 = Null
    //     0xbc025c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40fc8] Null
    //     0xbc0260: ldr             x3, [x3, #0xfc8]
    // 0xbc0264: blr             x9
    // 0xbc0268: r0 = Obx()
    //     0xbc0268: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbc026c: ldur            x2, [fp, #-0x10]
    // 0xbc0270: r1 = Function '<anonymous closure>':.
    //     0xbc0270: add             x1, PP, #0x40, lsl #12  ; [pp+0x40fd8] AnonymousClosure: (0xbc0444), in [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::build (0xbc00a8)
    //     0xbc0274: ldr             x1, [x1, #0xfd8]
    // 0xbc0278: stur            x0, [fp, #-0x10]
    // 0xbc027c: r0 = AllocateClosure()
    //     0xbc027c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc0280: mov             x1, x0
    // 0xbc0284: ldur            x0, [fp, #-0x10]
    // 0xbc0288: StoreField: r0->field_b = r1
    //     0xbc0288: stur            w1, [x0, #0xb]
    // 0xbc028c: ldur            x2, [fp, #-8]
    // 0xbc0290: r1 = Function 'onPageScrolled':.
    //     0xbc0290: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xbc0294: ldr             x1, [x1, #0xa88]
    // 0xbc0298: r0 = AllocateClosure()
    //     0xbc0298: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc029c: r1 = <ScrollNotification>
    //     0xbc029c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbc02a0: ldr             x1, [x1, #0x110]
    // 0xbc02a4: stur            x0, [fp, #-8]
    // 0xbc02a8: r0 = NotificationListener()
    //     0xbc02a8: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbc02ac: mov             x1, x0
    // 0xbc02b0: ldur            x0, [fp, #-8]
    // 0xbc02b4: stur            x1, [fp, #-0x20]
    // 0xbc02b8: StoreField: r1->field_13 = r0
    //     0xbc02b8: stur            w0, [x1, #0x13]
    // 0xbc02bc: ldur            x0, [fp, #-0x10]
    // 0xbc02c0: StoreField: r1->field_b = r0
    //     0xbc02c0: stur            w0, [x1, #0xb]
    // 0xbc02c4: r0 = RefreshIndicator()
    //     0xbc02c4: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbc02c8: mov             x3, x0
    // 0xbc02cc: ldur            x0, [fp, #-0x20]
    // 0xbc02d0: stur            x3, [fp, #-8]
    // 0xbc02d4: StoreField: r3->field_b = r0
    //     0xbc02d4: stur            w0, [x3, #0xb]
    // 0xbc02d8: d0 = 40.000000
    //     0xbc02d8: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbc02dc: StoreField: r3->field_f = d0
    //     0xbc02dc: stur            d0, [x3, #0xf]
    // 0xbc02e0: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbc02e0: stur            xzr, [x3, #0x17]
    // 0xbc02e4: ldur            x2, [fp, #-0x38]
    // 0xbc02e8: r1 = Function 'onPageRefresh':.
    //     0xbc02e8: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xbc02ec: ldr             x1, [x1, #0xa60]
    // 0xbc02f0: r0 = AllocateClosure()
    //     0xbc02f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc02f4: mov             x1, x0
    // 0xbc02f8: ldur            x0, [fp, #-8]
    // 0xbc02fc: StoreField: r0->field_1f = r1
    //     0xbc02fc: stur            w1, [x0, #0x1f]
    // 0xbc0300: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbc0300: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbc0304: ldr             x1, [x1, #0xf58]
    // 0xbc0308: StoreField: r0->field_2f = r1
    //     0xbc0308: stur            w1, [x0, #0x2f]
    // 0xbc030c: d0 = 2.500000
    //     0xbc030c: fmov            d0, #2.50000000
    // 0xbc0310: StoreField: r0->field_3b = d0
    //     0xbc0310: stur            d0, [x0, #0x3b]
    // 0xbc0314: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbc0314: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbc0318: ldr             x1, [x1, #0xa68]
    // 0xbc031c: StoreField: r0->field_47 = r1
    //     0xbc031c: stur            w1, [x0, #0x47]
    // 0xbc0320: d0 = 2.000000
    //     0xbc0320: fmov            d0, #2.00000000
    // 0xbc0324: StoreField: r0->field_4b = d0
    //     0xbc0324: stur            d0, [x0, #0x4b]
    // 0xbc0328: r1 = Instance__IndicatorType
    //     0xbc0328: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbc032c: ldr             x1, [x1, #0xa70]
    // 0xbc0330: StoreField: r0->field_43 = r1
    //     0xbc0330: stur            w1, [x0, #0x43]
    // 0xbc0334: r1 = <FlexParentData>
    //     0xbc0334: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbc0338: ldr             x1, [x1, #0x720]
    // 0xbc033c: r0 = Expanded()
    //     0xbc033c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc0340: mov             x2, x0
    // 0xbc0344: r0 = 1
    //     0xbc0344: movz            x0, #0x1
    // 0xbc0348: stur            x2, [fp, #-0x10]
    // 0xbc034c: StoreField: r2->field_13 = r0
    //     0xbc034c: stur            x0, [x2, #0x13]
    // 0xbc0350: r0 = Instance_FlexFit
    //     0xbc0350: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbc0354: ldr             x0, [x0, #0x728]
    // 0xbc0358: StoreField: r2->field_1b = r0
    //     0xbc0358: stur            w0, [x2, #0x1b]
    // 0xbc035c: ldur            x0, [fp, #-8]
    // 0xbc0360: StoreField: r2->field_b = r0
    //     0xbc0360: stur            w0, [x2, #0xb]
    // 0xbc0364: ldur            x0, [fp, #-0x18]
    // 0xbc0368: LoadField: r1 = r0->field_b
    //     0xbc0368: ldur            w1, [x0, #0xb]
    // 0xbc036c: LoadField: r3 = r0->field_f
    //     0xbc036c: ldur            w3, [x0, #0xf]
    // 0xbc0370: DecompressPointer r3
    //     0xbc0370: add             x3, x3, HEAP, lsl #32
    // 0xbc0374: LoadField: r4 = r3->field_b
    //     0xbc0374: ldur            w4, [x3, #0xb]
    // 0xbc0378: r3 = LoadInt32Instr(r1)
    //     0xbc0378: sbfx            x3, x1, #1, #0x1f
    // 0xbc037c: stur            x3, [fp, #-0x28]
    // 0xbc0380: r1 = LoadInt32Instr(r4)
    //     0xbc0380: sbfx            x1, x4, #1, #0x1f
    // 0xbc0384: cmp             x3, x1
    // 0xbc0388: b.ne            #0xbc0394
    // 0xbc038c: mov             x1, x0
    // 0xbc0390: r0 = _growToNextCapacity()
    //     0xbc0390: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc0394: ldur            x2, [fp, #-0x18]
    // 0xbc0398: ldur            x3, [fp, #-0x28]
    // 0xbc039c: add             x0, x3, #1
    // 0xbc03a0: lsl             x1, x0, #1
    // 0xbc03a4: StoreField: r2->field_b = r1
    //     0xbc03a4: stur            w1, [x2, #0xb]
    // 0xbc03a8: LoadField: r1 = r2->field_f
    //     0xbc03a8: ldur            w1, [x2, #0xf]
    // 0xbc03ac: DecompressPointer r1
    //     0xbc03ac: add             x1, x1, HEAP, lsl #32
    // 0xbc03b0: ldur            x0, [fp, #-0x10]
    // 0xbc03b4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbc03b4: add             x25, x1, x3, lsl #2
    //     0xbc03b8: add             x25, x25, #0xf
    //     0xbc03bc: str             w0, [x25]
    //     0xbc03c0: tbz             w0, #0, #0xbc03dc
    //     0xbc03c4: ldurb           w16, [x1, #-1]
    //     0xbc03c8: ldurb           w17, [x0, #-1]
    //     0xbc03cc: and             x16, x17, x16, lsr #2
    //     0xbc03d0: tst             x16, HEAP, lsr #32
    //     0xbc03d4: b.eq            #0xbc03dc
    //     0xbc03d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc03dc: r0 = Column()
    //     0xbc03dc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc03e0: r1 = Instance_Axis
    //     0xbc03e0: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc03e4: StoreField: r0->field_f = r1
    //     0xbc03e4: stur            w1, [x0, #0xf]
    // 0xbc03e8: r1 = Instance_MainAxisAlignment
    //     0xbc03e8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc03ec: ldr             x1, [x1, #0x730]
    // 0xbc03f0: StoreField: r0->field_13 = r1
    //     0xbc03f0: stur            w1, [x0, #0x13]
    // 0xbc03f4: r1 = Instance_MainAxisSize
    //     0xbc03f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc03f8: ldr             x1, [x1, #0x738]
    // 0xbc03fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc03fc: stur            w1, [x0, #0x17]
    // 0xbc0400: r1 = Instance_CrossAxisAlignment
    //     0xbc0400: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc0404: ldr             x1, [x1, #0x740]
    // 0xbc0408: StoreField: r0->field_1b = r1
    //     0xbc0408: stur            w1, [x0, #0x1b]
    // 0xbc040c: r1 = Instance_VerticalDirection
    //     0xbc040c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc0410: ldr             x1, [x1, #0x748]
    // 0xbc0414: StoreField: r0->field_23 = r1
    //     0xbc0414: stur            w1, [x0, #0x23]
    // 0xbc0418: r1 = Instance_Clip
    //     0xbc0418: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc041c: ldr             x1, [x1, #0x750]
    // 0xbc0420: StoreField: r0->field_2b = r1
    //     0xbc0420: stur            w1, [x0, #0x2b]
    // 0xbc0424: StoreField: r0->field_2f = rZR
    //     0xbc0424: stur            xzr, [x0, #0x2f]
    // 0xbc0428: ldur            x1, [fp, #-0x18]
    // 0xbc042c: StoreField: r0->field_b = r1
    //     0xbc042c: stur            w1, [x0, #0xb]
    // 0xbc0430: LeaveFrame
    //     0xbc0430: mov             SP, fp
    //     0xbc0434: ldp             fp, lr, [SP], #0x10
    // 0xbc0438: ret
    //     0xbc0438: ret             
    // 0xbc043c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc043c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0440: b               #0xbc00c8
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xbc0444, size: 0x29c
    // 0xbc0444: EnterFrame
    //     0xbc0444: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0448: mov             fp, SP
    // 0xbc044c: AllocStack(0x40)
    //     0xbc044c: sub             SP, SP, #0x40
    // 0xbc0450: SetupParameters()
    //     0xbc0450: ldr             x0, [fp, #0x10]
    //     0xbc0454: ldur            w2, [x0, #0x17]
    //     0xbc0458: add             x2, x2, HEAP, lsl #32
    //     0xbc045c: stur            x2, [fp, #-0x10]
    // 0xbc0460: CheckStackOverflow
    //     0xbc0460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0464: cmp             SP, x16
    //     0xbc0468: b.ls            #0xbc06d8
    // 0xbc046c: LoadField: r0 = r2->field_f
    //     0xbc046c: ldur            w0, [x2, #0xf]
    // 0xbc0470: DecompressPointer r0
    //     0xbc0470: add             x0, x0, HEAP, lsl #32
    // 0xbc0474: stur            x0, [fp, #-8]
    // 0xbc0478: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc0478: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc047c: ldr             x0, [x0, #0x26d0]
    //     0xbc0480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc0484: cmp             w0, w16
    //     0xbc0488: b.ne            #0xbc0498
    //     0xbc048c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc0490: ldr             x2, [x2, #0xb90]
    //     0xbc0494: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc0498: mov             x1, x0
    // 0xbc049c: ldur            x2, [fp, #-8]
    // 0xbc04a0: stur            x0, [fp, #-0x18]
    // 0xbc04a4: r0 = []()
    //     0xbc04a4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc04a8: mov             x3, x0
    // 0xbc04ac: ldur            x0, [fp, #-8]
    // 0xbc04b0: stur            x3, [fp, #-0x20]
    // 0xbc04b4: LoadField: r2 = r0->field_b
    //     0xbc04b4: ldur            w2, [x0, #0xb]
    // 0xbc04b8: DecompressPointer r2
    //     0xbc04b8: add             x2, x2, HEAP, lsl #32
    // 0xbc04bc: mov             x0, x3
    // 0xbc04c0: r1 = Null
    //     0xbc04c0: mov             x1, NULL
    // 0xbc04c4: cmp             w2, NULL
    // 0xbc04c8: b.eq            #0xbc04ec
    // 0xbc04cc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc04cc: ldur            w4, [x2, #0x17]
    // 0xbc04d0: DecompressPointer r4
    //     0xbc04d0: add             x4, x4, HEAP, lsl #32
    // 0xbc04d4: r8 = X0 bound GetLifeCycleBase?
    //     0xbc04d4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc04d8: ldr             x8, [x8, #0xb98]
    // 0xbc04dc: LoadField: r9 = r4->field_7
    //     0xbc04dc: ldur            x9, [x4, #7]
    // 0xbc04e0: r3 = Null
    //     0xbc04e0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40fe0] Null
    //     0xbc04e4: ldr             x3, [x3, #0xfe0]
    // 0xbc04e8: blr             x9
    // 0xbc04ec: ldur            x0, [fp, #-0x20]
    // 0xbc04f0: LoadField: r1 = r0->field_2b
    //     0xbc04f0: ldur            w1, [x0, #0x2b]
    // 0xbc04f4: DecompressPointer r1
    //     0xbc04f4: add             x1, x1, HEAP, lsl #32
    // 0xbc04f8: r0 = value()
    //     0xbc04f8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbc04fc: tbnz            w0, #4, #0xbc0600
    // 0xbc0500: ldur            x2, [fp, #-0x10]
    // 0xbc0504: LoadField: r1 = r2->field_f
    //     0xbc0504: ldur            w1, [x2, #0xf]
    // 0xbc0508: DecompressPointer r1
    //     0xbc0508: add             x1, x1, HEAP, lsl #32
    // 0xbc050c: r0 = buildEmptyResult()
    //     0xbc050c: bl              #0xad1990  ; [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::buildEmptyResult
    // 0xbc0510: mov             x2, x0
    // 0xbc0514: ldur            x0, [fp, #-0x10]
    // 0xbc0518: stur            x2, [fp, #-8]
    // 0xbc051c: LoadField: r1 = r0->field_f
    //     0xbc051c: ldur            w1, [x0, #0xf]
    // 0xbc0520: DecompressPointer r1
    //     0xbc0520: add             x1, x1, HEAP, lsl #32
    // 0xbc0524: r0 = buildSearchRecomendation()
    //     0xbc0524: bl              #0xbc06e0  ; [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::buildSearchRecomendation
    // 0xbc0528: r1 = Null
    //     0xbc0528: mov             x1, NULL
    // 0xbc052c: r2 = 6
    //     0xbc052c: movz            x2, #0x6
    // 0xbc0530: stur            x0, [fp, #-0x20]
    // 0xbc0534: r0 = AllocateArray()
    //     0xbc0534: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc0538: mov             x2, x0
    // 0xbc053c: ldur            x0, [fp, #-8]
    // 0xbc0540: stur            x2, [fp, #-0x28]
    // 0xbc0544: StoreField: r2->field_f = r0
    //     0xbc0544: stur            w0, [x2, #0xf]
    // 0xbc0548: r16 = Instance_SizedBox
    //     0xbc0548: add             x16, PP, #0x29, lsl #12  ; [pp+0x297f8] Obj!SizedBox@e1e1e1
    //     0xbc054c: ldr             x16, [x16, #0x7f8]
    // 0xbc0550: StoreField: r2->field_13 = r16
    //     0xbc0550: stur            w16, [x2, #0x13]
    // 0xbc0554: ldur            x0, [fp, #-0x20]
    // 0xbc0558: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc0558: stur            w0, [x2, #0x17]
    // 0xbc055c: r1 = <Widget>
    //     0xbc055c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc0560: r0 = AllocateGrowableArray()
    //     0xbc0560: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc0564: mov             x1, x0
    // 0xbc0568: ldur            x0, [fp, #-0x28]
    // 0xbc056c: stur            x1, [fp, #-8]
    // 0xbc0570: StoreField: r1->field_f = r0
    //     0xbc0570: stur            w0, [x1, #0xf]
    // 0xbc0574: r0 = 6
    //     0xbc0574: movz            x0, #0x6
    // 0xbc0578: StoreField: r1->field_b = r0
    //     0xbc0578: stur            w0, [x1, #0xb]
    // 0xbc057c: r0 = Column()
    //     0xbc057c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc0580: mov             x1, x0
    // 0xbc0584: r0 = Instance_Axis
    //     0xbc0584: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc0588: stur            x1, [fp, #-0x20]
    // 0xbc058c: StoreField: r1->field_f = r0
    //     0xbc058c: stur            w0, [x1, #0xf]
    // 0xbc0590: r0 = Instance_MainAxisAlignment
    //     0xbc0590: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc0594: ldr             x0, [x0, #0x730]
    // 0xbc0598: StoreField: r1->field_13 = r0
    //     0xbc0598: stur            w0, [x1, #0x13]
    // 0xbc059c: r0 = Instance_MainAxisSize
    //     0xbc059c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc05a0: ldr             x0, [x0, #0x738]
    // 0xbc05a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc05a4: stur            w0, [x1, #0x17]
    // 0xbc05a8: r0 = Instance_CrossAxisAlignment
    //     0xbc05a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc05ac: ldr             x0, [x0, #0x740]
    // 0xbc05b0: StoreField: r1->field_1b = r0
    //     0xbc05b0: stur            w0, [x1, #0x1b]
    // 0xbc05b4: r0 = Instance_VerticalDirection
    //     0xbc05b4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc05b8: ldr             x0, [x0, #0x748]
    // 0xbc05bc: StoreField: r1->field_23 = r0
    //     0xbc05bc: stur            w0, [x1, #0x23]
    // 0xbc05c0: r0 = Instance_Clip
    //     0xbc05c0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc05c4: ldr             x0, [x0, #0x750]
    // 0xbc05c8: StoreField: r1->field_2b = r0
    //     0xbc05c8: stur            w0, [x1, #0x2b]
    // 0xbc05cc: StoreField: r1->field_2f = rZR
    //     0xbc05cc: stur            xzr, [x1, #0x2f]
    // 0xbc05d0: ldur            x0, [fp, #-8]
    // 0xbc05d4: StoreField: r1->field_b = r0
    //     0xbc05d4: stur            w0, [x1, #0xb]
    // 0xbc05d8: r0 = Padding()
    //     0xbc05d8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc05dc: mov             x1, x0
    // 0xbc05e0: r0 = Instance_EdgeInsets
    //     0xbc05e0: ldr             x0, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xbc05e4: StoreField: r1->field_f = r0
    //     0xbc05e4: stur            w0, [x1, #0xf]
    // 0xbc05e8: ldur            x0, [fp, #-0x20]
    // 0xbc05ec: StoreField: r1->field_b = r0
    //     0xbc05ec: stur            w0, [x1, #0xb]
    // 0xbc05f0: mov             x0, x1
    // 0xbc05f4: LeaveFrame
    //     0xbc05f4: mov             SP, fp
    //     0xbc05f8: ldp             fp, lr, [SP], #0x10
    // 0xbc05fc: ret
    //     0xbc05fc: ret             
    // 0xbc0600: ldur            x0, [fp, #-0x10]
    // 0xbc0604: LoadField: r3 = r0->field_f
    //     0xbc0604: ldur            w3, [x0, #0xf]
    // 0xbc0608: DecompressPointer r3
    //     0xbc0608: add             x3, x3, HEAP, lsl #32
    // 0xbc060c: ldur            x1, [fp, #-0x18]
    // 0xbc0610: mov             x2, x3
    // 0xbc0614: stur            x3, [fp, #-8]
    // 0xbc0618: r0 = []()
    //     0xbc0618: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc061c: mov             x3, x0
    // 0xbc0620: ldur            x0, [fp, #-8]
    // 0xbc0624: stur            x3, [fp, #-0x18]
    // 0xbc0628: LoadField: r2 = r0->field_b
    //     0xbc0628: ldur            w2, [x0, #0xb]
    // 0xbc062c: DecompressPointer r2
    //     0xbc062c: add             x2, x2, HEAP, lsl #32
    // 0xbc0630: mov             x0, x3
    // 0xbc0634: r1 = Null
    //     0xbc0634: mov             x1, NULL
    // 0xbc0638: cmp             w2, NULL
    // 0xbc063c: b.eq            #0xbc0660
    // 0xbc0640: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0640: ldur            w4, [x2, #0x17]
    // 0xbc0644: DecompressPointer r4
    //     0xbc0644: add             x4, x4, HEAP, lsl #32
    // 0xbc0648: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0648: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc064c: ldr             x8, [x8, #0xb98]
    // 0xbc0650: LoadField: r9 = r4->field_7
    //     0xbc0650: ldur            x9, [x4, #7]
    // 0xbc0654: r3 = Null
    //     0xbc0654: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ff0] Null
    //     0xbc0658: ldr             x3, [x3, #0xff0]
    // 0xbc065c: blr             x9
    // 0xbc0660: ldur            x1, [fp, #-0x18]
    // 0xbc0664: r0 = itemsCount()
    //     0xbc0664: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbc0668: r1 = Function '<anonymous closure>':.
    //     0xbc0668: add             x1, PP, #0x41, lsl #12  ; [pp+0x41000] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbc066c: ldr             x1, [x1]
    // 0xbc0670: r2 = Null
    //     0xbc0670: mov             x2, NULL
    // 0xbc0674: stur            x0, [fp, #-0x30]
    // 0xbc0678: r0 = AllocateClosure()
    //     0xbc0678: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc067c: ldur            x2, [fp, #-0x10]
    // 0xbc0680: r1 = Function '<anonymous closure>':.
    //     0xbc0680: add             x1, PP, #0x41, lsl #12  ; [pp+0x41008] AnonymousClosure: (0xbc0be4), in [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::build (0xbc00a8)
    //     0xbc0684: ldr             x1, [x1, #8]
    // 0xbc0688: stur            x0, [fp, #-8]
    // 0xbc068c: r0 = AllocateClosure()
    //     0xbc068c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc0690: stur            x0, [fp, #-0x10]
    // 0xbc0694: r0 = ListView()
    //     0xbc0694: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbc0698: stur            x0, [fp, #-0x18]
    // 0xbc069c: r16 = true
    //     0xbc069c: add             x16, NULL, #0x20  ; true
    // 0xbc06a0: r30 = Instance_EdgeInsets
    //     0xbc06a0: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xbc06a4: ldr             lr, [lr, #0x360]
    // 0xbc06a8: stp             lr, x16, [SP]
    // 0xbc06ac: mov             x1, x0
    // 0xbc06b0: ldur            x2, [fp, #-0x10]
    // 0xbc06b4: ldur            x3, [fp, #-0x30]
    // 0xbc06b8: ldur            x5, [fp, #-8]
    // 0xbc06bc: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xbc06bc: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbc06c0: ldr             x4, [x4, #0x100]
    // 0xbc06c4: r0 = ListView.separated()
    //     0xbc06c4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbc06c8: ldur            x0, [fp, #-0x18]
    // 0xbc06cc: LeaveFrame
    //     0xbc06cc: mov             SP, fp
    //     0xbc06d0: ldp             fp, lr, [SP], #0x10
    // 0xbc06d4: ret
    //     0xbc06d4: ret             
    // 0xbc06d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc06d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc06dc: b               #0xbc046c
  }
  _ buildSearchRecomendation(/* No info */) {
    // ** addr: 0xbc06e0, size: 0x294
    // 0xbc06e0: EnterFrame
    //     0xbc06e0: stp             fp, lr, [SP, #-0x10]!
    //     0xbc06e4: mov             fp, SP
    // 0xbc06e8: AllocStack(0x40)
    //     0xbc06e8: sub             SP, SP, #0x40
    // 0xbc06ec: SetupParameters(ArticleCategoryBuilderWidget this /* r1 => r2, fp-0x8 */)
    //     0xbc06ec: mov             x2, x1
    //     0xbc06f0: stur            x1, [fp, #-8]
    // 0xbc06f4: CheckStackOverflow
    //     0xbc06f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc06f8: cmp             SP, x16
    //     0xbc06fc: b.ls            #0xbc096c
    // 0xbc0700: r1 = 1
    //     0xbc0700: movz            x1, #0x1
    // 0xbc0704: r0 = AllocateContext()
    //     0xbc0704: bl              #0xec126c  ; AllocateContextStub
    // 0xbc0708: ldur            x2, [fp, #-8]
    // 0xbc070c: stur            x0, [fp, #-0x10]
    // 0xbc0710: StoreField: r0->field_f = r2
    //     0xbc0710: stur            w2, [x0, #0xf]
    // 0xbc0714: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc0714: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc0718: ldr             x0, [x0, #0x26d0]
    //     0xbc071c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc0720: cmp             w0, w16
    //     0xbc0724: b.ne            #0xbc0734
    //     0xbc0728: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc072c: ldr             x2, [x2, #0xb90]
    //     0xbc0730: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc0734: mov             x1, x0
    // 0xbc0738: ldur            x2, [fp, #-8]
    // 0xbc073c: stur            x0, [fp, #-0x18]
    // 0xbc0740: r0 = []()
    //     0xbc0740: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc0744: mov             x4, x0
    // 0xbc0748: ldur            x3, [fp, #-8]
    // 0xbc074c: stur            x4, [fp, #-0x28]
    // 0xbc0750: LoadField: r5 = r3->field_b
    //     0xbc0750: ldur            w5, [x3, #0xb]
    // 0xbc0754: DecompressPointer r5
    //     0xbc0754: add             x5, x5, HEAP, lsl #32
    // 0xbc0758: mov             x0, x4
    // 0xbc075c: mov             x2, x5
    // 0xbc0760: stur            x5, [fp, #-0x20]
    // 0xbc0764: r1 = Null
    //     0xbc0764: mov             x1, NULL
    // 0xbc0768: cmp             w2, NULL
    // 0xbc076c: b.eq            #0xbc0790
    // 0xbc0770: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0770: ldur            w4, [x2, #0x17]
    // 0xbc0774: DecompressPointer r4
    //     0xbc0774: add             x4, x4, HEAP, lsl #32
    // 0xbc0778: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0778: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc077c: ldr             x8, [x8, #0xb98]
    // 0xbc0780: LoadField: r9 = r4->field_7
    //     0xbc0780: ldur            x9, [x4, #7]
    // 0xbc0784: r3 = Null
    //     0xbc0784: add             x3, PP, #0x41, lsl #12  ; [pp+0x41020] Null
    //     0xbc0788: ldr             x3, [x3, #0x20]
    // 0xbc078c: blr             x9
    // 0xbc0790: ldur            x0, [fp, #-0x28]
    // 0xbc0794: LoadField: r1 = r0->field_4f
    //     0xbc0794: ldur            w1, [x0, #0x4f]
    // 0xbc0798: DecompressPointer r1
    //     0xbc0798: add             x1, x1, HEAP, lsl #32
    // 0xbc079c: r0 = value()
    //     0xbc079c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xbc07a0: r1 = LoadClassIdInstr(r0)
    //     0xbc07a0: ldur            x1, [x0, #-1]
    //     0xbc07a4: ubfx            x1, x1, #0xc, #0x14
    // 0xbc07a8: str             x0, [SP]
    // 0xbc07ac: mov             x0, x1
    // 0xbc07b0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xbc07b0: movz            x17, #0xc834
    //     0xbc07b4: add             lr, x0, x17
    //     0xbc07b8: ldr             lr, [x21, lr, lsl #3]
    //     0xbc07bc: blr             lr
    // 0xbc07c0: cbnz            w0, #0xbc07d8
    // 0xbc07c4: r0 = Instance_SizedBox
    //     0xbc07c4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xbc07c8: ldr             x0, [x0, #0xc40]
    // 0xbc07cc: LeaveFrame
    //     0xbc07cc: mov             SP, fp
    //     0xbc07d0: ldp             fp, lr, [SP], #0x10
    // 0xbc07d4: ret
    //     0xbc07d4: ret             
    // 0xbc07d8: ldur            x1, [fp, #-0x18]
    // 0xbc07dc: ldur            x2, [fp, #-8]
    // 0xbc07e0: r0 = []()
    //     0xbc07e0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc07e4: ldur            x2, [fp, #-0x20]
    // 0xbc07e8: mov             x3, x0
    // 0xbc07ec: r1 = Null
    //     0xbc07ec: mov             x1, NULL
    // 0xbc07f0: stur            x3, [fp, #-8]
    // 0xbc07f4: cmp             w2, NULL
    // 0xbc07f8: b.eq            #0xbc081c
    // 0xbc07fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc07fc: ldur            w4, [x2, #0x17]
    // 0xbc0800: DecompressPointer r4
    //     0xbc0800: add             x4, x4, HEAP, lsl #32
    // 0xbc0804: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0804: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0808: ldr             x8, [x8, #0xb98]
    // 0xbc080c: LoadField: r9 = r4->field_7
    //     0xbc080c: ldur            x9, [x4, #7]
    // 0xbc0810: r3 = Null
    //     0xbc0810: add             x3, PP, #0x41, lsl #12  ; [pp+0x41030] Null
    //     0xbc0814: ldr             x3, [x3, #0x30]
    // 0xbc0818: blr             x9
    // 0xbc081c: ldur            x0, [fp, #-8]
    // 0xbc0820: LoadField: r3 = r0->field_4f
    //     0xbc0820: ldur            w3, [x0, #0x4f]
    // 0xbc0824: DecompressPointer r3
    //     0xbc0824: add             x3, x3, HEAP, lsl #32
    // 0xbc0828: ldur            x2, [fp, #-0x10]
    // 0xbc082c: stur            x3, [fp, #-0x18]
    // 0xbc0830: r1 = Function '<anonymous closure>':.
    //     0xbc0830: add             x1, PP, #0x41, lsl #12  ; [pp+0x41040] AnonymousClosure: (0xbc0974), in [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::buildSearchRecomendation (0xbc06e0)
    //     0xbc0834: ldr             x1, [x1, #0x40]
    // 0xbc0838: r0 = AllocateClosure()
    //     0xbc0838: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc083c: r16 = <NTagButton>
    //     0xbc083c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30968] TypeArguments: <NTagButton>
    //     0xbc0840: ldr             x16, [x16, #0x968]
    // 0xbc0844: ldur            lr, [fp, #-0x18]
    // 0xbc0848: stp             lr, x16, [SP, #8]
    // 0xbc084c: str             x0, [SP]
    // 0xbc0850: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbc0850: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbc0854: r0 = map()
    //     0xbc0854: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xbc0858: LoadField: r1 = r0->field_7
    //     0xbc0858: ldur            w1, [x0, #7]
    // 0xbc085c: DecompressPointer r1
    //     0xbc085c: add             x1, x1, HEAP, lsl #32
    // 0xbc0860: mov             x2, x0
    // 0xbc0864: r0 = _GrowableList.of()
    //     0xbc0864: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xbc0868: stur            x0, [fp, #-8]
    // 0xbc086c: r0 = Wrap()
    //     0xbc086c: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xbc0870: mov             x3, x0
    // 0xbc0874: r0 = Instance_Axis
    //     0xbc0874: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xbc0878: stur            x3, [fp, #-0x10]
    // 0xbc087c: StoreField: r3->field_f = r0
    //     0xbc087c: stur            w0, [x3, #0xf]
    // 0xbc0880: r0 = Instance_WrapAlignment
    //     0xbc0880: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xbc0884: ldr             x0, [x0, #0x610]
    // 0xbc0888: StoreField: r3->field_13 = r0
    //     0xbc0888: stur            w0, [x3, #0x13]
    // 0xbc088c: d0 = 12.000000
    //     0xbc088c: fmov            d0, #12.00000000
    // 0xbc0890: ArrayStore: r3[0] = d0  ; List_8
    //     0xbc0890: stur            d0, [x3, #0x17]
    // 0xbc0894: StoreField: r3->field_1f = r0
    //     0xbc0894: stur            w0, [x3, #0x1f]
    // 0xbc0898: StoreField: r3->field_23 = d0
    //     0xbc0898: stur            d0, [x3, #0x23]
    // 0xbc089c: r0 = Instance_WrapCrossAlignment
    //     0xbc089c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xbc08a0: ldr             x0, [x0, #0x618]
    // 0xbc08a4: StoreField: r3->field_2b = r0
    //     0xbc08a4: stur            w0, [x3, #0x2b]
    // 0xbc08a8: r0 = Instance_VerticalDirection
    //     0xbc08a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc08ac: ldr             x0, [x0, #0x748]
    // 0xbc08b0: StoreField: r3->field_33 = r0
    //     0xbc08b0: stur            w0, [x3, #0x33]
    // 0xbc08b4: r4 = Instance_Clip
    //     0xbc08b4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc08b8: ldr             x4, [x4, #0x750]
    // 0xbc08bc: StoreField: r3->field_37 = r4
    //     0xbc08bc: stur            w4, [x3, #0x37]
    // 0xbc08c0: ldur            x1, [fp, #-8]
    // 0xbc08c4: StoreField: r3->field_b = r1
    //     0xbc08c4: stur            w1, [x3, #0xb]
    // 0xbc08c8: r1 = Null
    //     0xbc08c8: mov             x1, NULL
    // 0xbc08cc: r2 = 4
    //     0xbc08cc: movz            x2, #0x4
    // 0xbc08d0: r0 = AllocateArray()
    //     0xbc08d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc08d4: stur            x0, [fp, #-8]
    // 0xbc08d8: r16 = Instance_Padding
    //     0xbc08d8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30970] Obj!Padding@e1e7c1
    //     0xbc08dc: ldr             x16, [x16, #0x970]
    // 0xbc08e0: StoreField: r0->field_f = r16
    //     0xbc08e0: stur            w16, [x0, #0xf]
    // 0xbc08e4: ldur            x1, [fp, #-0x10]
    // 0xbc08e8: StoreField: r0->field_13 = r1
    //     0xbc08e8: stur            w1, [x0, #0x13]
    // 0xbc08ec: r1 = <Widget>
    //     0xbc08ec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc08f0: r0 = AllocateGrowableArray()
    //     0xbc08f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc08f4: mov             x1, x0
    // 0xbc08f8: ldur            x0, [fp, #-8]
    // 0xbc08fc: stur            x1, [fp, #-0x10]
    // 0xbc0900: StoreField: r1->field_f = r0
    //     0xbc0900: stur            w0, [x1, #0xf]
    // 0xbc0904: r0 = 4
    //     0xbc0904: movz            x0, #0x4
    // 0xbc0908: StoreField: r1->field_b = r0
    //     0xbc0908: stur            w0, [x1, #0xb]
    // 0xbc090c: r0 = Column()
    //     0xbc090c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc0910: r1 = Instance_Axis
    //     0xbc0910: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc0914: StoreField: r0->field_f = r1
    //     0xbc0914: stur            w1, [x0, #0xf]
    // 0xbc0918: r1 = Instance_MainAxisAlignment
    //     0xbc0918: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc091c: ldr             x1, [x1, #0x730]
    // 0xbc0920: StoreField: r0->field_13 = r1
    //     0xbc0920: stur            w1, [x0, #0x13]
    // 0xbc0924: r1 = Instance_MainAxisSize
    //     0xbc0924: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc0928: ldr             x1, [x1, #0x738]
    // 0xbc092c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc092c: stur            w1, [x0, #0x17]
    // 0xbc0930: r1 = Instance_CrossAxisAlignment
    //     0xbc0930: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xbc0934: ldr             x1, [x1, #0x68]
    // 0xbc0938: StoreField: r0->field_1b = r1
    //     0xbc0938: stur            w1, [x0, #0x1b]
    // 0xbc093c: r1 = Instance_VerticalDirection
    //     0xbc093c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc0940: ldr             x1, [x1, #0x748]
    // 0xbc0944: StoreField: r0->field_23 = r1
    //     0xbc0944: stur            w1, [x0, #0x23]
    // 0xbc0948: r1 = Instance_Clip
    //     0xbc0948: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc094c: ldr             x1, [x1, #0x750]
    // 0xbc0950: StoreField: r0->field_2b = r1
    //     0xbc0950: stur            w1, [x0, #0x2b]
    // 0xbc0954: StoreField: r0->field_2f = rZR
    //     0xbc0954: stur            xzr, [x0, #0x2f]
    // 0xbc0958: ldur            x1, [fp, #-0x10]
    // 0xbc095c: StoreField: r0->field_b = r1
    //     0xbc095c: stur            w1, [x0, #0xb]
    // 0xbc0960: LeaveFrame
    //     0xbc0960: mov             SP, fp
    //     0xbc0964: ldp             fp, lr, [SP], #0x10
    // 0xbc0968: ret
    //     0xbc0968: ret             
    // 0xbc096c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc096c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0970: b               #0xbc0700
  }
  [closure] NTagButton <anonymous closure>(dynamic, Tag) {
    // ** addr: 0xbc0974, size: 0x84
    // 0xbc0974: EnterFrame
    //     0xbc0974: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0978: mov             fp, SP
    // 0xbc097c: AllocStack(0x18)
    //     0xbc097c: sub             SP, SP, #0x18
    // 0xbc0980: SetupParameters()
    //     0xbc0980: ldr             x0, [fp, #0x18]
    //     0xbc0984: ldur            w1, [x0, #0x17]
    //     0xbc0988: add             x1, x1, HEAP, lsl #32
    //     0xbc098c: stur            x1, [fp, #-8]
    // 0xbc0990: r1 = 1
    //     0xbc0990: movz            x1, #0x1
    // 0xbc0994: r0 = AllocateContext()
    //     0xbc0994: bl              #0xec126c  ; AllocateContextStub
    // 0xbc0998: mov             x1, x0
    // 0xbc099c: ldur            x0, [fp, #-8]
    // 0xbc09a0: stur            x1, [fp, #-0x10]
    // 0xbc09a4: StoreField: r1->field_b = r0
    //     0xbc09a4: stur            w0, [x1, #0xb]
    // 0xbc09a8: ldr             x0, [fp, #0x10]
    // 0xbc09ac: StoreField: r1->field_f = r0
    //     0xbc09ac: stur            w0, [x1, #0xf]
    // 0xbc09b0: LoadField: r2 = r0->field_f
    //     0xbc09b0: ldur            w2, [x0, #0xf]
    // 0xbc09b4: DecompressPointer r2
    //     0xbc09b4: add             x2, x2, HEAP, lsl #32
    // 0xbc09b8: stur            x2, [fp, #-8]
    // 0xbc09bc: r0 = NTagButton()
    //     0xbc09bc: bl              #0xa36a20  ; AllocateNTagButtonStub -> NTagButton (size=0x18)
    // 0xbc09c0: mov             x3, x0
    // 0xbc09c4: ldur            x0, [fp, #-8]
    // 0xbc09c8: stur            x3, [fp, #-0x18]
    // 0xbc09cc: StoreField: r3->field_b = r0
    //     0xbc09cc: stur            w0, [x3, #0xb]
    // 0xbc09d0: ldur            x2, [fp, #-0x10]
    // 0xbc09d4: r1 = Function '<anonymous closure>':.
    //     0xbc09d4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41048] AnonymousClosure: (0xbc09f8), in [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::buildSearchRecomendation (0xbc06e0)
    //     0xbc09d8: ldr             x1, [x1, #0x48]
    // 0xbc09dc: r0 = AllocateClosure()
    //     0xbc09dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc09e0: mov             x1, x0
    // 0xbc09e4: ldur            x0, [fp, #-0x18]
    // 0xbc09e8: StoreField: r0->field_f = r1
    //     0xbc09e8: stur            w1, [x0, #0xf]
    // 0xbc09ec: LeaveFrame
    //     0xbc09ec: mov             SP, fp
    //     0xbc09f0: ldp             fp, lr, [SP], #0x10
    // 0xbc09f4: ret
    //     0xbc09f4: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc09f8, size: 0xe0
    // 0xbc09f8: EnterFrame
    //     0xbc09f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbc09fc: mov             fp, SP
    // 0xbc0a00: AllocStack(0x18)
    //     0xbc0a00: sub             SP, SP, #0x18
    // 0xbc0a04: SetupParameters()
    //     0xbc0a04: ldr             x0, [fp, #0x10]
    //     0xbc0a08: ldur            w1, [x0, #0x17]
    //     0xbc0a0c: add             x1, x1, HEAP, lsl #32
    //     0xbc0a10: stur            x1, [fp, #-0x10]
    // 0xbc0a14: CheckStackOverflow
    //     0xbc0a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0a18: cmp             SP, x16
    //     0xbc0a1c: b.ls            #0xbc0ad0
    // 0xbc0a20: LoadField: r0 = r1->field_b
    //     0xbc0a20: ldur            w0, [x1, #0xb]
    // 0xbc0a24: DecompressPointer r0
    //     0xbc0a24: add             x0, x0, HEAP, lsl #32
    // 0xbc0a28: LoadField: r2 = r0->field_f
    //     0xbc0a28: ldur            w2, [x0, #0xf]
    // 0xbc0a2c: DecompressPointer r2
    //     0xbc0a2c: add             x2, x2, HEAP, lsl #32
    // 0xbc0a30: stur            x2, [fp, #-8]
    // 0xbc0a34: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc0a34: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc0a38: ldr             x0, [x0, #0x26d0]
    //     0xbc0a3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc0a40: cmp             w0, w16
    //     0xbc0a44: b.ne            #0xbc0a54
    //     0xbc0a48: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc0a4c: ldr             x2, [x2, #0xb90]
    //     0xbc0a50: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc0a54: mov             x1, x0
    // 0xbc0a58: ldur            x2, [fp, #-8]
    // 0xbc0a5c: r0 = []()
    //     0xbc0a5c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc0a60: mov             x3, x0
    // 0xbc0a64: ldur            x0, [fp, #-8]
    // 0xbc0a68: stur            x3, [fp, #-0x18]
    // 0xbc0a6c: LoadField: r2 = r0->field_b
    //     0xbc0a6c: ldur            w2, [x0, #0xb]
    // 0xbc0a70: DecompressPointer r2
    //     0xbc0a70: add             x2, x2, HEAP, lsl #32
    // 0xbc0a74: mov             x0, x3
    // 0xbc0a78: r1 = Null
    //     0xbc0a78: mov             x1, NULL
    // 0xbc0a7c: cmp             w2, NULL
    // 0xbc0a80: b.eq            #0xbc0aa4
    // 0xbc0a84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0a84: ldur            w4, [x2, #0x17]
    // 0xbc0a88: DecompressPointer r4
    //     0xbc0a88: add             x4, x4, HEAP, lsl #32
    // 0xbc0a8c: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0a8c: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0a90: ldr             x8, [x8, #0xb98]
    // 0xbc0a94: LoadField: r9 = r4->field_7
    //     0xbc0a94: ldur            x9, [x4, #7]
    // 0xbc0a98: r3 = Null
    //     0xbc0a98: add             x3, PP, #0x41, lsl #12  ; [pp+0x41050] Null
    //     0xbc0a9c: ldr             x3, [x3, #0x50]
    // 0xbc0aa0: blr             x9
    // 0xbc0aa4: ldur            x0, [fp, #-0x10]
    // 0xbc0aa8: LoadField: r1 = r0->field_f
    //     0xbc0aa8: ldur            w1, [x0, #0xf]
    // 0xbc0aac: DecompressPointer r1
    //     0xbc0aac: add             x1, x1, HEAP, lsl #32
    // 0xbc0ab0: LoadField: r2 = r1->field_f
    //     0xbc0ab0: ldur            w2, [x1, #0xf]
    // 0xbc0ab4: DecompressPointer r2
    //     0xbc0ab4: add             x2, x2, HEAP, lsl #32
    // 0xbc0ab8: ldur            x1, [fp, #-0x18]
    // 0xbc0abc: r0 = search()
    //     0xbc0abc: bl              #0xbc0ad8  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::search
    // 0xbc0ac0: r0 = Null
    //     0xbc0ac0: mov             x0, NULL
    // 0xbc0ac4: LeaveFrame
    //     0xbc0ac4: mov             SP, fp
    //     0xbc0ac8: ldp             fp, lr, [SP], #0x10
    // 0xbc0acc: ret
    //     0xbc0acc: ret             
    // 0xbc0ad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0ad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0ad4: b               #0xbc0a20
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbc0be4, size: 0x354
    // 0xbc0be4: EnterFrame
    //     0xbc0be4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0be8: mov             fp, SP
    // 0xbc0bec: AllocStack(0x20)
    //     0xbc0bec: sub             SP, SP, #0x20
    // 0xbc0bf0: SetupParameters()
    //     0xbc0bf0: ldr             x0, [fp, #0x20]
    //     0xbc0bf4: ldur            w1, [x0, #0x17]
    //     0xbc0bf8: add             x1, x1, HEAP, lsl #32
    // 0xbc0bfc: CheckStackOverflow
    //     0xbc0bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0c00: cmp             SP, x16
    //     0xbc0c04: b.ls            #0xbc0f28
    // 0xbc0c08: LoadField: r2 = r1->field_f
    //     0xbc0c08: ldur            w2, [x1, #0xf]
    // 0xbc0c0c: DecompressPointer r2
    //     0xbc0c0c: add             x2, x2, HEAP, lsl #32
    // 0xbc0c10: stur            x2, [fp, #-8]
    // 0xbc0c14: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc0c14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc0c18: ldr             x0, [x0, #0x26d0]
    //     0xbc0c1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc0c20: cmp             w0, w16
    //     0xbc0c24: b.ne            #0xbc0c34
    //     0xbc0c28: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc0c2c: ldr             x2, [x2, #0xb90]
    //     0xbc0c30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc0c34: mov             x1, x0
    // 0xbc0c38: ldur            x2, [fp, #-8]
    // 0xbc0c3c: r0 = []()
    //     0xbc0c3c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc0c40: mov             x3, x0
    // 0xbc0c44: ldur            x0, [fp, #-8]
    // 0xbc0c48: stur            x3, [fp, #-0x10]
    // 0xbc0c4c: LoadField: r2 = r0->field_b
    //     0xbc0c4c: ldur            w2, [x0, #0xb]
    // 0xbc0c50: DecompressPointer r2
    //     0xbc0c50: add             x2, x2, HEAP, lsl #32
    // 0xbc0c54: mov             x0, x3
    // 0xbc0c58: r1 = Null
    //     0xbc0c58: mov             x1, NULL
    // 0xbc0c5c: cmp             w2, NULL
    // 0xbc0c60: b.eq            #0xbc0c84
    // 0xbc0c64: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0c64: ldur            w4, [x2, #0x17]
    // 0xbc0c68: DecompressPointer r4
    //     0xbc0c68: add             x4, x4, HEAP, lsl #32
    // 0xbc0c6c: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0c6c: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0c70: ldr             x8, [x8, #0xb98]
    // 0xbc0c74: LoadField: r9 = r4->field_7
    //     0xbc0c74: ldur            x9, [x4, #7]
    // 0xbc0c78: r3 = Null
    //     0xbc0c78: add             x3, PP, #0x41, lsl #12  ; [pp+0x41010] Null
    //     0xbc0c7c: ldr             x3, [x3, #0x10]
    // 0xbc0c80: blr             x9
    // 0xbc0c84: ldr             x0, [fp, #0x10]
    // 0xbc0c88: r3 = LoadInt32Instr(r0)
    //     0xbc0c88: sbfx            x3, x0, #1, #0x1f
    //     0xbc0c8c: tbz             w0, #0, #0xbc0c94
    //     0xbc0c90: ldur            x3, [x0, #7]
    // 0xbc0c94: ldur            x1, [fp, #-0x10]
    // 0xbc0c98: mov             x2, x3
    // 0xbc0c9c: stur            x3, [fp, #-0x18]
    // 0xbc0ca0: r0 = find()
    //     0xbc0ca0: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xbc0ca4: stur            x0, [fp, #-8]
    // 0xbc0ca8: cmp             w0, NULL
    // 0xbc0cac: b.ne            #0xbc0cc4
    // 0xbc0cb0: r0 = Instance_NArticleListTile
    //     0xbc0cb0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xbc0cb4: ldr             x0, [x0, #0xa90]
    // 0xbc0cb8: LeaveFrame
    //     0xbc0cb8: mov             SP, fp
    //     0xbc0cbc: ldp             fp, lr, [SP], #0x10
    // 0xbc0cc0: ret
    //     0xbc0cc0: ret             
    // 0xbc0cc4: ldur            x1, [fp, #-0x18]
    // 0xbc0cc8: r2 = 10
    //     0xbc0cc8: movz            x2, #0xa
    // 0xbc0ccc: sdiv            x4, x1, x2
    // 0xbc0cd0: msub            x3, x4, x2, x1
    // 0xbc0cd4: cmp             x3, xzr
    // 0xbc0cd8: b.lt            #0xbc0f30
    // 0xbc0cdc: cmp             x3, #4
    // 0xbc0ce0: b.ne            #0xbc0f04
    // 0xbc0ce4: r0 = ArticleItem()
    //     0xbc0ce4: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xbc0ce8: mov             x3, x0
    // 0xbc0cec: ldur            x0, [fp, #-8]
    // 0xbc0cf0: stur            x3, [fp, #-0x10]
    // 0xbc0cf4: StoreField: r3->field_b = r0
    //     0xbc0cf4: stur            w0, [x3, #0xb]
    // 0xbc0cf8: r1 = false
    //     0xbc0cf8: add             x1, NULL, #0x30  ; false
    // 0xbc0cfc: StoreField: r3->field_13 = r1
    //     0xbc0cfc: stur            w1, [x3, #0x13]
    // 0xbc0d00: r1 = Null
    //     0xbc0d00: mov             x1, NULL
    // 0xbc0d04: r2 = 2
    //     0xbc0d04: movz            x2, #0x2
    // 0xbc0d08: r0 = AllocateArray()
    //     0xbc0d08: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc0d0c: mov             x2, x0
    // 0xbc0d10: ldur            x0, [fp, #-0x10]
    // 0xbc0d14: stur            x2, [fp, #-0x20]
    // 0xbc0d18: StoreField: r2->field_f = r0
    //     0xbc0d18: stur            w0, [x2, #0xf]
    // 0xbc0d1c: r1 = <Widget>
    //     0xbc0d1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc0d20: r0 = AllocateGrowableArray()
    //     0xbc0d20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc0d24: mov             x1, x0
    // 0xbc0d28: ldur            x0, [fp, #-0x20]
    // 0xbc0d2c: stur            x1, [fp, #-0x10]
    // 0xbc0d30: StoreField: r1->field_f = r0
    //     0xbc0d30: stur            w0, [x1, #0xf]
    // 0xbc0d34: r0 = 2
    //     0xbc0d34: movz            x0, #0x2
    // 0xbc0d38: StoreField: r1->field_b = r0
    //     0xbc0d38: stur            w0, [x1, #0xb]
    // 0xbc0d3c: r0 = find()
    //     0xbc0d3c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc0d40: mov             x1, x0
    // 0xbc0d44: r0 = _adsVisibility()
    //     0xbc0d44: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc0d48: mov             x2, x0
    // 0xbc0d4c: r1 = Null
    //     0xbc0d4c: mov             x1, NULL
    // 0xbc0d50: r0 = AdsConfig.fromJson()
    //     0xbc0d50: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc0d54: LoadField: r1 = r0->field_b
    //     0xbc0d54: ldur            w1, [x0, #0xb]
    // 0xbc0d58: DecompressPointer r1
    //     0xbc0d58: add             x1, x1, HEAP, lsl #32
    // 0xbc0d5c: LoadField: r0 = r1->field_7
    //     0xbc0d5c: ldur            w0, [x1, #7]
    // 0xbc0d60: DecompressPointer r0
    //     0xbc0d60: add             x0, x0, HEAP, lsl #32
    // 0xbc0d64: tbnz            w0, #4, #0xbc0dc8
    // 0xbc0d68: ldur            x0, [fp, #-0x10]
    // 0xbc0d6c: LoadField: r1 = r0->field_b
    //     0xbc0d6c: ldur            w1, [x0, #0xb]
    // 0xbc0d70: LoadField: r2 = r0->field_f
    //     0xbc0d70: ldur            w2, [x0, #0xf]
    // 0xbc0d74: DecompressPointer r2
    //     0xbc0d74: add             x2, x2, HEAP, lsl #32
    // 0xbc0d78: LoadField: r3 = r2->field_b
    //     0xbc0d78: ldur            w3, [x2, #0xb]
    // 0xbc0d7c: r2 = LoadInt32Instr(r1)
    //     0xbc0d7c: sbfx            x2, x1, #1, #0x1f
    // 0xbc0d80: stur            x2, [fp, #-0x18]
    // 0xbc0d84: r1 = LoadInt32Instr(r3)
    //     0xbc0d84: sbfx            x1, x3, #1, #0x1f
    // 0xbc0d88: cmp             x2, x1
    // 0xbc0d8c: b.ne            #0xbc0d98
    // 0xbc0d90: mov             x1, x0
    // 0xbc0d94: r0 = _growToNextCapacity()
    //     0xbc0d94: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc0d98: ldur            x1, [fp, #-0x10]
    // 0xbc0d9c: ldur            x0, [fp, #-0x18]
    // 0xbc0da0: add             x2, x0, #1
    // 0xbc0da4: lsl             x3, x2, #1
    // 0xbc0da8: StoreField: r1->field_b = r3
    //     0xbc0da8: stur            w3, [x1, #0xb]
    // 0xbc0dac: LoadField: r2 = r1->field_f
    //     0xbc0dac: ldur            w2, [x1, #0xf]
    // 0xbc0db0: DecompressPointer r2
    //     0xbc0db0: add             x2, x2, HEAP, lsl #32
    // 0xbc0db4: add             x3, x2, x0, lsl #2
    // 0xbc0db8: r16 = Instance_Divider
    //     0xbc0db8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xbc0dbc: ldr             x16, [x16, #0xc28]
    // 0xbc0dc0: StoreField: r3->field_f = r16
    //     0xbc0dc0: stur            w16, [x3, #0xf]
    // 0xbc0dc4: b               #0xbc0dcc
    // 0xbc0dc8: ldur            x1, [fp, #-0x10]
    // 0xbc0dcc: r0 = find()
    //     0xbc0dcc: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc0dd0: mov             x1, x0
    // 0xbc0dd4: r0 = _adsVisibility()
    //     0xbc0dd4: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc0dd8: mov             x2, x0
    // 0xbc0ddc: r1 = Null
    //     0xbc0ddc: mov             x1, NULL
    // 0xbc0de0: r0 = AdsConfig.fromJson()
    //     0xbc0de0: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc0de4: LoadField: r1 = r0->field_b
    //     0xbc0de4: ldur            w1, [x0, #0xb]
    // 0xbc0de8: DecompressPointer r1
    //     0xbc0de8: add             x1, x1, HEAP, lsl #32
    // 0xbc0dec: LoadField: r0 = r1->field_7
    //     0xbc0dec: ldur            w0, [x1, #7]
    // 0xbc0df0: DecompressPointer r0
    //     0xbc0df0: add             x0, x0, HEAP, lsl #32
    // 0xbc0df4: tbnz            w0, #4, #0xbc0e98
    // 0xbc0df8: ldur            x1, [fp, #-0x10]
    // 0xbc0dfc: r0 = find()
    //     0xbc0dfc: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc0e00: mov             x1, x0
    // 0xbc0e04: r0 = _adsVisibility()
    //     0xbc0e04: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc0e08: mov             x2, x0
    // 0xbc0e0c: r1 = Null
    //     0xbc0e0c: mov             x1, NULL
    // 0xbc0e10: r0 = AdsConfig.fromJson()
    //     0xbc0e10: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc0e14: ldur            x0, [fp, #-0x10]
    // 0xbc0e18: LoadField: r1 = r0->field_b
    //     0xbc0e18: ldur            w1, [x0, #0xb]
    // 0xbc0e1c: LoadField: r2 = r0->field_f
    //     0xbc0e1c: ldur            w2, [x0, #0xf]
    // 0xbc0e20: DecompressPointer r2
    //     0xbc0e20: add             x2, x2, HEAP, lsl #32
    // 0xbc0e24: LoadField: r3 = r2->field_b
    //     0xbc0e24: ldur            w3, [x2, #0xb]
    // 0xbc0e28: r2 = LoadInt32Instr(r1)
    //     0xbc0e28: sbfx            x2, x1, #1, #0x1f
    // 0xbc0e2c: stur            x2, [fp, #-0x18]
    // 0xbc0e30: r1 = LoadInt32Instr(r3)
    //     0xbc0e30: sbfx            x1, x3, #1, #0x1f
    // 0xbc0e34: cmp             x2, x1
    // 0xbc0e38: b.ne            #0xbc0e44
    // 0xbc0e3c: mov             x1, x0
    // 0xbc0e40: r0 = _growToNextCapacity()
    //     0xbc0e40: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc0e44: ldur            x0, [fp, #-0x10]
    // 0xbc0e48: ldur            x1, [fp, #-0x18]
    // 0xbc0e4c: add             x2, x1, #1
    // 0xbc0e50: lsl             x3, x2, #1
    // 0xbc0e54: StoreField: r0->field_b = r3
    //     0xbc0e54: stur            w3, [x0, #0xb]
    // 0xbc0e58: LoadField: r2 = r0->field_f
    //     0xbc0e58: ldur            w2, [x0, #0xf]
    // 0xbc0e5c: DecompressPointer r2
    //     0xbc0e5c: add             x2, x2, HEAP, lsl #32
    // 0xbc0e60: stur            x2, [fp, #-0x20]
    // 0xbc0e64: r0 = AdmobArticleNativeWidget()
    //     0xbc0e64: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xbc0e68: ldur            x1, [fp, #-0x20]
    // 0xbc0e6c: ldur            x2, [fp, #-0x18]
    // 0xbc0e70: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbc0e70: add             x25, x1, x2, lsl #2
    //     0xbc0e74: add             x25, x25, #0xf
    //     0xbc0e78: str             w0, [x25]
    //     0xbc0e7c: tbz             w0, #0, #0xbc0e98
    //     0xbc0e80: ldurb           w16, [x1, #-1]
    //     0xbc0e84: ldurb           w17, [x0, #-1]
    //     0xbc0e88: and             x16, x17, x16, lsr #2
    //     0xbc0e8c: tst             x16, HEAP, lsr #32
    //     0xbc0e90: b.eq            #0xbc0e98
    //     0xbc0e94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc0e98: ldur            x0, [fp, #-0x10]
    // 0xbc0e9c: r0 = Column()
    //     0xbc0e9c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc0ea0: mov             x1, x0
    // 0xbc0ea4: r0 = Instance_Axis
    //     0xbc0ea4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc0ea8: StoreField: r1->field_f = r0
    //     0xbc0ea8: stur            w0, [x1, #0xf]
    // 0xbc0eac: r0 = Instance_MainAxisAlignment
    //     0xbc0eac: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc0eb0: ldr             x0, [x0, #0x730]
    // 0xbc0eb4: StoreField: r1->field_13 = r0
    //     0xbc0eb4: stur            w0, [x1, #0x13]
    // 0xbc0eb8: r0 = Instance_MainAxisSize
    //     0xbc0eb8: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xbc0ebc: ldr             x0, [x0, #0xe88]
    // 0xbc0ec0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc0ec0: stur            w0, [x1, #0x17]
    // 0xbc0ec4: r0 = Instance_CrossAxisAlignment
    //     0xbc0ec4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc0ec8: ldr             x0, [x0, #0x740]
    // 0xbc0ecc: StoreField: r1->field_1b = r0
    //     0xbc0ecc: stur            w0, [x1, #0x1b]
    // 0xbc0ed0: r0 = Instance_VerticalDirection
    //     0xbc0ed0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc0ed4: ldr             x0, [x0, #0x748]
    // 0xbc0ed8: StoreField: r1->field_23 = r0
    //     0xbc0ed8: stur            w0, [x1, #0x23]
    // 0xbc0edc: r0 = Instance_Clip
    //     0xbc0edc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc0ee0: ldr             x0, [x0, #0x750]
    // 0xbc0ee4: StoreField: r1->field_2b = r0
    //     0xbc0ee4: stur            w0, [x1, #0x2b]
    // 0xbc0ee8: StoreField: r1->field_2f = rZR
    //     0xbc0ee8: stur            xzr, [x1, #0x2f]
    // 0xbc0eec: ldur            x0, [fp, #-0x10]
    // 0xbc0ef0: StoreField: r1->field_b = r0
    //     0xbc0ef0: stur            w0, [x1, #0xb]
    // 0xbc0ef4: mov             x0, x1
    // 0xbc0ef8: LeaveFrame
    //     0xbc0ef8: mov             SP, fp
    //     0xbc0efc: ldp             fp, lr, [SP], #0x10
    // 0xbc0f00: ret
    //     0xbc0f00: ret             
    // 0xbc0f04: r1 = false
    //     0xbc0f04: add             x1, NULL, #0x30  ; false
    // 0xbc0f08: r0 = ArticleItem()
    //     0xbc0f08: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xbc0f0c: ldur            x1, [fp, #-8]
    // 0xbc0f10: StoreField: r0->field_b = r1
    //     0xbc0f10: stur            w1, [x0, #0xb]
    // 0xbc0f14: r1 = false
    //     0xbc0f14: add             x1, NULL, #0x30  ; false
    // 0xbc0f18: StoreField: r0->field_13 = r1
    //     0xbc0f18: stur            w1, [x0, #0x13]
    // 0xbc0f1c: LeaveFrame
    //     0xbc0f1c: mov             SP, fp
    //     0xbc0f20: ldp             fp, lr, [SP], #0x10
    // 0xbc0f24: ret
    //     0xbc0f24: ret             
    // 0xbc0f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc0f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc0f2c: b               #0xbc0c08
    // 0xbc0f30: add             x3, x3, x2
    // 0xbc0f34: b               #0xbc0cdc
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xbc0f38, size: 0x3c8
    // 0xbc0f38: EnterFrame
    //     0xbc0f38: stp             fp, lr, [SP, #-0x10]!
    //     0xbc0f3c: mov             fp, SP
    // 0xbc0f40: AllocStack(0x48)
    //     0xbc0f40: sub             SP, SP, #0x48
    // 0xbc0f44: SetupParameters()
    //     0xbc0f44: ldr             x0, [fp, #0x10]
    //     0xbc0f48: ldur            w3, [x0, #0x17]
    //     0xbc0f4c: add             x3, x3, HEAP, lsl #32
    //     0xbc0f50: stur            x3, [fp, #-8]
    // 0xbc0f54: CheckStackOverflow
    //     0xbc0f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc0f58: cmp             SP, x16
    //     0xbc0f5c: b.ls            #0xbc12ec
    // 0xbc0f60: r1 = Null
    //     0xbc0f60: mov             x1, NULL
    // 0xbc0f64: r2 = 4
    //     0xbc0f64: movz            x2, #0x4
    // 0xbc0f68: r0 = AllocateArray()
    //     0xbc0f68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc0f6c: stur            x0, [fp, #-0x18]
    // 0xbc0f70: r16 = "Cari artikel "
    //     0xbc0f70: add             x16, PP, #0x41, lsl #12  ; [pp+0x41068] "Cari artikel "
    //     0xbc0f74: ldr             x16, [x16, #0x68]
    // 0xbc0f78: StoreField: r0->field_f = r16
    //     0xbc0f78: stur            w16, [x0, #0xf]
    // 0xbc0f7c: ldur            x1, [fp, #-8]
    // 0xbc0f80: LoadField: r2 = r1->field_f
    //     0xbc0f80: ldur            w2, [x1, #0xf]
    // 0xbc0f84: DecompressPointer r2
    //     0xbc0f84: add             x2, x2, HEAP, lsl #32
    // 0xbc0f88: stur            x2, [fp, #-0x10]
    // 0xbc0f8c: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc0f8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc0f90: ldr             x0, [x0, #0x26d0]
    //     0xbc0f94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc0f98: cmp             w0, w16
    //     0xbc0f9c: b.ne            #0xbc0fac
    //     0xbc0fa0: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc0fa4: ldr             x2, [x2, #0xb90]
    //     0xbc0fa8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc0fac: mov             x1, x0
    // 0xbc0fb0: ldur            x2, [fp, #-0x10]
    // 0xbc0fb4: stur            x0, [fp, #-0x20]
    // 0xbc0fb8: r0 = []()
    //     0xbc0fb8: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc0fbc: mov             x3, x0
    // 0xbc0fc0: ldur            x0, [fp, #-0x10]
    // 0xbc0fc4: stur            x3, [fp, #-0x28]
    // 0xbc0fc8: LoadField: r2 = r0->field_b
    //     0xbc0fc8: ldur            w2, [x0, #0xb]
    // 0xbc0fcc: DecompressPointer r2
    //     0xbc0fcc: add             x2, x2, HEAP, lsl #32
    // 0xbc0fd0: mov             x0, x3
    // 0xbc0fd4: r1 = Null
    //     0xbc0fd4: mov             x1, NULL
    // 0xbc0fd8: cmp             w2, NULL
    // 0xbc0fdc: b.eq            #0xbc1000
    // 0xbc0fe0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc0fe0: ldur            w4, [x2, #0x17]
    // 0xbc0fe4: DecompressPointer r4
    //     0xbc0fe4: add             x4, x4, HEAP, lsl #32
    // 0xbc0fe8: r8 = X0 bound GetLifeCycleBase?
    //     0xbc0fe8: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc0fec: ldr             x8, [x8, #0xb98]
    // 0xbc0ff0: LoadField: r9 = r4->field_7
    //     0xbc0ff0: ldur            x9, [x4, #7]
    // 0xbc0ff4: r3 = Null
    //     0xbc0ff4: add             x3, PP, #0x41, lsl #12  ; [pp+0x41070] Null
    //     0xbc0ff8: ldr             x3, [x3, #0x70]
    // 0xbc0ffc: blr             x9
    // 0xbc1000: ldur            x0, [fp, #-0x28]
    // 0xbc1004: LoadField: r1 = r0->field_4b
    //     0xbc1004: ldur            w1, [x0, #0x4b]
    // 0xbc1008: DecompressPointer r1
    //     0xbc1008: add             x1, x1, HEAP, lsl #32
    // 0xbc100c: mov             x0, x1
    // 0xbc1010: ldur            x1, [fp, #-0x18]
    // 0xbc1014: ArrayStore: r1[1] = r0  ; List_4
    //     0xbc1014: add             x25, x1, #0x13
    //     0xbc1018: str             w0, [x25]
    //     0xbc101c: tbz             w0, #0, #0xbc1038
    //     0xbc1020: ldurb           w16, [x1, #-1]
    //     0xbc1024: ldurb           w17, [x0, #-1]
    //     0xbc1028: and             x16, x17, x16, lsr #2
    //     0xbc102c: tst             x16, HEAP, lsr #32
    //     0xbc1030: b.eq            #0xbc1038
    //     0xbc1034: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc1038: ldur            x16, [fp, #-0x18]
    // 0xbc103c: str             x16, [SP]
    // 0xbc1040: r0 = _interpolate()
    //     0xbc1040: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc1044: mov             x3, x0
    // 0xbc1048: ldur            x0, [fp, #-8]
    // 0xbc104c: stur            x3, [fp, #-0x18]
    // 0xbc1050: LoadField: r4 = r0->field_f
    //     0xbc1050: ldur            w4, [x0, #0xf]
    // 0xbc1054: DecompressPointer r4
    //     0xbc1054: add             x4, x4, HEAP, lsl #32
    // 0xbc1058: ldur            x1, [fp, #-0x20]
    // 0xbc105c: mov             x2, x4
    // 0xbc1060: stur            x4, [fp, #-0x10]
    // 0xbc1064: r0 = []()
    //     0xbc1064: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1068: mov             x3, x0
    // 0xbc106c: ldur            x0, [fp, #-0x10]
    // 0xbc1070: stur            x3, [fp, #-0x28]
    // 0xbc1074: LoadField: r2 = r0->field_b
    //     0xbc1074: ldur            w2, [x0, #0xb]
    // 0xbc1078: DecompressPointer r2
    //     0xbc1078: add             x2, x2, HEAP, lsl #32
    // 0xbc107c: mov             x0, x3
    // 0xbc1080: r1 = Null
    //     0xbc1080: mov             x1, NULL
    // 0xbc1084: cmp             w2, NULL
    // 0xbc1088: b.eq            #0xbc10ac
    // 0xbc108c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc108c: ldur            w4, [x2, #0x17]
    // 0xbc1090: DecompressPointer r4
    //     0xbc1090: add             x4, x4, HEAP, lsl #32
    // 0xbc1094: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1094: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1098: ldr             x8, [x8, #0xb98]
    // 0xbc109c: LoadField: r9 = r4->field_7
    //     0xbc109c: ldur            x9, [x4, #7]
    // 0xbc10a0: r3 = Null
    //     0xbc10a0: add             x3, PP, #0x41, lsl #12  ; [pp+0x41080] Null
    //     0xbc10a4: ldr             x3, [x3, #0x80]
    // 0xbc10a8: blr             x9
    // 0xbc10ac: ldur            x0, [fp, #-0x28]
    // 0xbc10b0: LoadField: r3 = r0->field_43
    //     0xbc10b0: ldur            w3, [x0, #0x43]
    // 0xbc10b4: DecompressPointer r3
    //     0xbc10b4: add             x3, x3, HEAP, lsl #32
    // 0xbc10b8: r16 = Sentinel
    //     0xbc10b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbc10bc: cmp             w3, w16
    // 0xbc10c0: b.eq            #0xbc12f4
    // 0xbc10c4: ldur            x0, [fp, #-8]
    // 0xbc10c8: stur            x3, [fp, #-0x30]
    // 0xbc10cc: LoadField: r4 = r0->field_f
    //     0xbc10cc: ldur            w4, [x0, #0xf]
    // 0xbc10d0: DecompressPointer r4
    //     0xbc10d0: add             x4, x4, HEAP, lsl #32
    // 0xbc10d4: ldur            x1, [fp, #-0x20]
    // 0xbc10d8: mov             x2, x4
    // 0xbc10dc: stur            x4, [fp, #-0x10]
    // 0xbc10e0: r0 = []()
    //     0xbc10e0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc10e4: mov             x3, x0
    // 0xbc10e8: ldur            x0, [fp, #-0x10]
    // 0xbc10ec: stur            x3, [fp, #-0x28]
    // 0xbc10f0: LoadField: r2 = r0->field_b
    //     0xbc10f0: ldur            w2, [x0, #0xb]
    // 0xbc10f4: DecompressPointer r2
    //     0xbc10f4: add             x2, x2, HEAP, lsl #32
    // 0xbc10f8: mov             x0, x3
    // 0xbc10fc: r1 = Null
    //     0xbc10fc: mov             x1, NULL
    // 0xbc1100: cmp             w2, NULL
    // 0xbc1104: b.eq            #0xbc1128
    // 0xbc1108: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1108: ldur            w4, [x2, #0x17]
    // 0xbc110c: DecompressPointer r4
    //     0xbc110c: add             x4, x4, HEAP, lsl #32
    // 0xbc1110: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1110: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1114: ldr             x8, [x8, #0xb98]
    // 0xbc1118: LoadField: r9 = r4->field_7
    //     0xbc1118: ldur            x9, [x4, #7]
    // 0xbc111c: r3 = Null
    //     0xbc111c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41090] Null
    //     0xbc1120: ldr             x3, [x3, #0x90]
    // 0xbc1124: blr             x9
    // 0xbc1128: ldur            x0, [fp, #-8]
    // 0xbc112c: LoadField: r3 = r0->field_f
    //     0xbc112c: ldur            w3, [x0, #0xf]
    // 0xbc1130: DecompressPointer r3
    //     0xbc1130: add             x3, x3, HEAP, lsl #32
    // 0xbc1134: ldur            x1, [fp, #-0x20]
    // 0xbc1138: mov             x2, x3
    // 0xbc113c: stur            x3, [fp, #-0x10]
    // 0xbc1140: r0 = []()
    //     0xbc1140: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1144: mov             x3, x0
    // 0xbc1148: ldur            x0, [fp, #-0x10]
    // 0xbc114c: stur            x3, [fp, #-0x38]
    // 0xbc1150: LoadField: r2 = r0->field_b
    //     0xbc1150: ldur            w2, [x0, #0xb]
    // 0xbc1154: DecompressPointer r2
    //     0xbc1154: add             x2, x2, HEAP, lsl #32
    // 0xbc1158: mov             x0, x3
    // 0xbc115c: r1 = Null
    //     0xbc115c: mov             x1, NULL
    // 0xbc1160: cmp             w2, NULL
    // 0xbc1164: b.eq            #0xbc1188
    // 0xbc1168: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1168: ldur            w4, [x2, #0x17]
    // 0xbc116c: DecompressPointer r4
    //     0xbc116c: add             x4, x4, HEAP, lsl #32
    // 0xbc1170: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1170: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1174: ldr             x8, [x8, #0xb98]
    // 0xbc1178: LoadField: r9 = r4->field_7
    //     0xbc1178: ldur            x9, [x4, #7]
    // 0xbc117c: r3 = Null
    //     0xbc117c: add             x3, PP, #0x41, lsl #12  ; [pp+0x410a0] Null
    //     0xbc1180: ldr             x3, [x3, #0xa0]
    // 0xbc1184: blr             x9
    // 0xbc1188: ldur            x0, [fp, #-0x38]
    // 0xbc118c: LoadField: r1 = r0->field_47
    //     0xbc118c: ldur            w1, [x0, #0x47]
    // 0xbc1190: DecompressPointer r1
    //     0xbc1190: add             x1, x1, HEAP, lsl #32
    // 0xbc1194: r0 = value()
    //     0xbc1194: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbc1198: mov             x3, x0
    // 0xbc119c: ldur            x0, [fp, #-8]
    // 0xbc11a0: stur            x3, [fp, #-0x38]
    // 0xbc11a4: LoadField: r4 = r0->field_f
    //     0xbc11a4: ldur            w4, [x0, #0xf]
    // 0xbc11a8: DecompressPointer r4
    //     0xbc11a8: add             x4, x4, HEAP, lsl #32
    // 0xbc11ac: ldur            x1, [fp, #-0x20]
    // 0xbc11b0: mov             x2, x4
    // 0xbc11b4: stur            x4, [fp, #-0x10]
    // 0xbc11b8: r0 = []()
    //     0xbc11b8: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc11bc: mov             x3, x0
    // 0xbc11c0: ldur            x0, [fp, #-0x10]
    // 0xbc11c4: stur            x3, [fp, #-0x40]
    // 0xbc11c8: LoadField: r2 = r0->field_b
    //     0xbc11c8: ldur            w2, [x0, #0xb]
    // 0xbc11cc: DecompressPointer r2
    //     0xbc11cc: add             x2, x2, HEAP, lsl #32
    // 0xbc11d0: mov             x0, x3
    // 0xbc11d4: r1 = Null
    //     0xbc11d4: mov             x1, NULL
    // 0xbc11d8: cmp             w2, NULL
    // 0xbc11dc: b.eq            #0xbc1200
    // 0xbc11e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc11e0: ldur            w4, [x2, #0x17]
    // 0xbc11e4: DecompressPointer r4
    //     0xbc11e4: add             x4, x4, HEAP, lsl #32
    // 0xbc11e8: r8 = X0 bound GetLifeCycleBase?
    //     0xbc11e8: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc11ec: ldr             x8, [x8, #0xb98]
    // 0xbc11f0: LoadField: r9 = r4->field_7
    //     0xbc11f0: ldur            x9, [x4, #7]
    // 0xbc11f4: r3 = Null
    //     0xbc11f4: add             x3, PP, #0x41, lsl #12  ; [pp+0x410b0] Null
    //     0xbc11f8: ldr             x3, [x3, #0xb0]
    // 0xbc11fc: blr             x9
    // 0xbc1200: ldur            x0, [fp, #-8]
    // 0xbc1204: LoadField: r3 = r0->field_f
    //     0xbc1204: ldur            w3, [x0, #0xf]
    // 0xbc1208: DecompressPointer r3
    //     0xbc1208: add             x3, x3, HEAP, lsl #32
    // 0xbc120c: ldur            x1, [fp, #-0x20]
    // 0xbc1210: mov             x2, x3
    // 0xbc1214: stur            x3, [fp, #-0x10]
    // 0xbc1218: r0 = []()
    //     0xbc1218: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc121c: mov             x3, x0
    // 0xbc1220: ldur            x0, [fp, #-0x10]
    // 0xbc1224: stur            x3, [fp, #-8]
    // 0xbc1228: LoadField: r2 = r0->field_b
    //     0xbc1228: ldur            w2, [x0, #0xb]
    // 0xbc122c: DecompressPointer r2
    //     0xbc122c: add             x2, x2, HEAP, lsl #32
    // 0xbc1230: mov             x0, x3
    // 0xbc1234: r1 = Null
    //     0xbc1234: mov             x1, NULL
    // 0xbc1238: cmp             w2, NULL
    // 0xbc123c: b.eq            #0xbc1260
    // 0xbc1240: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1240: ldur            w4, [x2, #0x17]
    // 0xbc1244: DecompressPointer r4
    //     0xbc1244: add             x4, x4, HEAP, lsl #32
    // 0xbc1248: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1248: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc124c: ldr             x8, [x8, #0xb98]
    // 0xbc1250: LoadField: r9 = r4->field_7
    //     0xbc1250: ldur            x9, [x4, #7]
    // 0xbc1254: r3 = Null
    //     0xbc1254: add             x3, PP, #0x41, lsl #12  ; [pp+0x410c0] Null
    //     0xbc1258: ldr             x3, [x3, #0xc0]
    // 0xbc125c: blr             x9
    // 0xbc1260: r0 = NSearchTextField()
    //     0xbc1260: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xbc1264: mov             x3, x0
    // 0xbc1268: ldur            x0, [fp, #-0x30]
    // 0xbc126c: stur            x3, [fp, #-0x10]
    // 0xbc1270: StoreField: r3->field_f = r0
    //     0xbc1270: stur            w0, [x3, #0xf]
    // 0xbc1274: ldur            x0, [fp, #-0x38]
    // 0xbc1278: StoreField: r3->field_b = r0
    //     0xbc1278: stur            w0, [x3, #0xb]
    // 0xbc127c: ldur            x2, [fp, #-0x28]
    // 0xbc1280: r1 = Function 'onChanged':.
    //     0xbc1280: add             x1, PP, #0x41, lsl #12  ; [pp+0x410d0] AnonymousClosure: (0xbc1388), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onChanged (0xbc13c4)
    //     0xbc1284: ldr             x1, [x1, #0xd0]
    // 0xbc1288: r0 = AllocateClosure()
    //     0xbc1288: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc128c: mov             x1, x0
    // 0xbc1290: ldur            x0, [fp, #-0x10]
    // 0xbc1294: StoreField: r0->field_13 = r1
    //     0xbc1294: stur            w1, [x0, #0x13]
    // 0xbc1298: ldur            x2, [fp, #-0x40]
    // 0xbc129c: r1 = Function 'onCloseTap':.
    //     0xbc129c: add             x1, PP, #0x41, lsl #12  ; [pp+0x410d8] AnonymousClosure: (0xbc1300), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onCloseTap (0xbc1338)
    //     0xbc12a0: ldr             x1, [x1, #0xd8]
    // 0xbc12a4: r0 = AllocateClosure()
    //     0xbc12a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc12a8: mov             x1, x0
    // 0xbc12ac: ldur            x0, [fp, #-0x10]
    // 0xbc12b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc12b0: stur            w1, [x0, #0x17]
    // 0xbc12b4: ldur            x1, [fp, #-0x18]
    // 0xbc12b8: StoreField: r0->field_23 = r1
    //     0xbc12b8: stur            w1, [x0, #0x23]
    // 0xbc12bc: ldur            x2, [fp, #-8]
    // 0xbc12c0: r1 = Function 'onSubmitted':.
    //     0xbc12c0: add             x1, PP, #0x41, lsl #12  ; [pp+0x410e0] AnonymousClosure: (0xbc0ba8), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::onSubmitted (0xbc0b60)
    //     0xbc12c4: ldr             x1, [x1, #0xe0]
    // 0xbc12c8: r0 = AllocateClosure()
    //     0xbc12c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc12cc: mov             x1, x0
    // 0xbc12d0: ldur            x0, [fp, #-0x10]
    // 0xbc12d4: StoreField: r0->field_1b = r1
    //     0xbc12d4: stur            w1, [x0, #0x1b]
    // 0xbc12d8: r1 = false
    //     0xbc12d8: add             x1, NULL, #0x30  ; false
    // 0xbc12dc: StoreField: r0->field_27 = r1
    //     0xbc12dc: stur            w1, [x0, #0x27]
    // 0xbc12e0: LeaveFrame
    //     0xbc12e0: mov             SP, fp
    //     0xbc12e4: ldp             fp, lr, [SP], #0x10
    // 0xbc12e8: ret
    //     0xbc12e8: ret             
    // 0xbc12ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc12ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc12f0: b               #0xbc0f60
    // 0xbc12f4: r9 = textController
    //     0xbc12f4: add             x9, PP, #0x41, lsl #12  ; [pp+0x41060] Field <ArticleCategoryBuilderController.textController>: late (offset: 0x44)
    //     0xbc12f8: ldr             x9, [x9, #0x60]
    // 0xbc12fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbc12fc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
