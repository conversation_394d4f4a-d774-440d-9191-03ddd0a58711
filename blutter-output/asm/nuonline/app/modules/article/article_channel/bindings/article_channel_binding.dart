// lib: , url: package:nuonline/app/modules/article/article_channel/bindings/article_channel_binding.dart

// class id: 1050125, size: 0x8
class :: {
}

// class id: 2196, size: 0x8, field offset: 0x8
class ArticleChannelBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80d354, size: 0x70
    // 0x80d354: EnterFrame
    //     0x80d354: stp             fp, lr, [SP, #-0x10]!
    //     0x80d358: mov             fp, SP
    // 0x80d35c: AllocStack(0x10)
    //     0x80d35c: sub             SP, SP, #0x10
    // 0x80d360: CheckStackOverflow
    //     0x80d360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d364: cmp             SP, x16
    //     0x80d368: b.ls            #0x80d3bc
    // 0x80d36c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d36c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d370: ldr             x0, [x0, #0x2670]
    //     0x80d374: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d378: cmp             w0, w16
    //     0x80d37c: b.ne            #0x80d388
    //     0x80d380: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d384: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d388: r1 = Function '<anonymous closure>':.
    //     0x80d388: add             x1, PP, #0x36, lsl #12  ; [pp+0x369b0] AnonymousClosure: (0x80d3c4), in [package:nuonline/app/modules/article/article_channel/bindings/article_channel_binding.dart] ArticleChannelBinding::dependencies (0x80d354)
    //     0x80d38c: ldr             x1, [x1, #0x9b0]
    // 0x80d390: r2 = Null
    //     0x80d390: mov             x2, NULL
    // 0x80d394: r0 = AllocateClosure()
    //     0x80d394: bl              #0xec1630  ; AllocateClosureStub
    // 0x80d398: r16 = <ArticleChannelController>
    //     0x80d398: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d18] TypeArguments: <ArticleChannelController>
    //     0x80d39c: ldr             x16, [x16, #0xd18]
    // 0x80d3a0: stp             x0, x16, [SP]
    // 0x80d3a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80d3a4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80d3a8: r0 = Inst.lazyPut()
    //     0x80d3a8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80d3ac: r0 = Null
    //     0x80d3ac: mov             x0, NULL
    // 0x80d3b0: LeaveFrame
    //     0x80d3b0: mov             SP, fp
    //     0x80d3b4: ldp             fp, lr, [SP], #0x10
    // 0x80d3b8: ret
    //     0x80d3b8: ret             
    // 0x80d3bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d3bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d3c0: b               #0x80d36c
  }
  [closure] ArticleChannelController <anonymous closure>(dynamic) {
    // ** addr: 0x80d3c4, size: 0x80
    // 0x80d3c4: EnterFrame
    //     0x80d3c4: stp             fp, lr, [SP, #-0x10]!
    //     0x80d3c8: mov             fp, SP
    // 0x80d3cc: AllocStack(0x18)
    //     0x80d3cc: sub             SP, SP, #0x18
    // 0x80d3d0: CheckStackOverflow
    //     0x80d3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d3d4: cmp             SP, x16
    //     0x80d3d8: b.ls            #0x80d43c
    // 0x80d3dc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d3dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d3e0: ldr             x0, [x0, #0x2670]
    //     0x80d3e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d3e8: cmp             w0, w16
    //     0x80d3ec: b.ne            #0x80d3f8
    //     0x80d3f0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d3f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d3f8: r16 = <CategoryRepository>
    //     0x80d3f8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10068] TypeArguments: <CategoryRepository>
    //     0x80d3fc: ldr             x16, [x16, #0x68]
    // 0x80d400: r30 = "category_repo"
    //     0x80d400: add             lr, PP, #0x10, lsl #12  ; [pp+0x10070] "category_repo"
    //     0x80d404: ldr             lr, [lr, #0x70]
    // 0x80d408: stp             lr, x16, [SP]
    // 0x80d40c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80d40c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80d410: r0 = Inst.find()
    //     0x80d410: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80d414: stur            x0, [fp, #-8]
    // 0x80d418: r0 = ArticleChannelController()
    //     0x80d418: bl              #0x80d658  ; AllocateArticleChannelControllerStub -> ArticleChannelController (size=0x30)
    // 0x80d41c: mov             x1, x0
    // 0x80d420: ldur            x2, [fp, #-8]
    // 0x80d424: stur            x0, [fp, #-8]
    // 0x80d428: r0 = ArticleChannelController()
    //     0x80d428: bl              #0x80d444  ; [package:nuonline/app/modules/article/article_channel/controllers/article_channel_controller.dart] ArticleChannelController::ArticleChannelController
    // 0x80d42c: ldur            x0, [fp, #-8]
    // 0x80d430: LeaveFrame
    //     0x80d430: mov             SP, fp
    //     0x80d434: ldp             fp, lr, [SP], #0x10
    // 0x80d438: ret
    //     0x80d438: ret             
    // 0x80d43c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d43c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d440: b               #0x80d3dc
  }
}
