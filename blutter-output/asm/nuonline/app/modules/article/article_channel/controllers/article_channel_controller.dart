// lib: , url: package:nuonline/app/modules/article/article_channel/controllers/article_channel_controller.dart

// class id: 1050126, size: 0x8
class :: {
}

// class id: 2038, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _ArticleChannelController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {
}

// class id: 2039, size: 0x30, field offset: 0x28
class ArticleChannelController extends _ArticleChannelController&GetxController&StateMixin {

  _ ArticleChannelController(/* No info */) {
    // ** addr: 0x80d444, size: 0xe8
    // 0x80d444: EnterFrame
    //     0x80d444: stp             fp, lr, [SP, #-0x10]!
    //     0x80d448: mov             fp, SP
    // 0x80d44c: AllocStack(0x28)
    //     0x80d44c: sub             SP, SP, #0x28
    // 0x80d450: SetupParameters(ArticleChannelController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x80d450: mov             x0, x2
    //     0x80d454: stur            x1, [fp, #-8]
    //     0x80d458: stur            x2, [fp, #-0x10]
    // 0x80d45c: CheckStackOverflow
    //     0x80d45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d460: cmp             SP, x16
    //     0x80d464: b.ls            #0x80d524
    // 0x80d468: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d468: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d46c: ldr             x0, [x0, #0x2670]
    //     0x80d470: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d474: cmp             w0, w16
    //     0x80d478: b.ne            #0x80d484
    //     0x80d47c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d480: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d484: r0 = GetNavigation.arguments()
    //     0x80d484: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d488: r16 = "islamic"
    //     0x80d488: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0x80d48c: ldr             x16, [x16, #0xca0]
    // 0x80d490: stp             x16, x0, [SP]
    // 0x80d494: r4 = 0
    //     0x80d494: movz            x4, #0
    // 0x80d498: ldr             x0, [SP, #8]
    // 0x80d49c: r16 = UnlinkedCall_0x5f3c08
    //     0x80d49c: add             x16, PP, #0x36, lsl #12  ; [pp+0x369b8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d4a0: add             x16, x16, #0x9b8
    // 0x80d4a4: ldp             x5, lr, [x16]
    // 0x80d4a8: blr             lr
    // 0x80d4ac: mov             x3, x0
    // 0x80d4b0: r2 = Null
    //     0x80d4b0: mov             x2, NULL
    // 0x80d4b4: r1 = Null
    //     0x80d4b4: mov             x1, NULL
    // 0x80d4b8: stur            x3, [fp, #-0x18]
    // 0x80d4bc: r4 = 60
    //     0x80d4bc: movz            x4, #0x3c
    // 0x80d4c0: branchIfSmi(r0, 0x80d4cc)
    //     0x80d4c0: tbz             w0, #0, #0x80d4cc
    // 0x80d4c4: r4 = LoadClassIdInstr(r0)
    //     0x80d4c4: ldur            x4, [x0, #-1]
    //     0x80d4c8: ubfx            x4, x4, #0xc, #0x14
    // 0x80d4cc: cmp             x4, #0x3f
    // 0x80d4d0: b.eq            #0x80d4e4
    // 0x80d4d4: r8 = bool
    //     0x80d4d4: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x80d4d8: r3 = Null
    //     0x80d4d8: add             x3, PP, #0x36, lsl #12  ; [pp+0x369c8] Null
    //     0x80d4dc: ldr             x3, [x3, #0x9c8]
    // 0x80d4e0: r0 = bool()
    //     0x80d4e0: bl              #0xed4390  ; IsType_bool_Stub
    // 0x80d4e4: ldur            x1, [fp, #-8]
    // 0x80d4e8: ldur            x0, [fp, #-0x18]
    // 0x80d4ec: StoreField: r1->field_2b = r0
    //     0x80d4ec: stur            w0, [x1, #0x2b]
    // 0x80d4f0: ldur            x0, [fp, #-0x10]
    // 0x80d4f4: StoreField: r1->field_27 = r0
    //     0x80d4f4: stur            w0, [x1, #0x27]
    //     0x80d4f8: ldurb           w16, [x1, #-1]
    //     0x80d4fc: ldurb           w17, [x0, #-1]
    //     0x80d500: and             x16, x17, x16, lsr #2
    //     0x80d504: tst             x16, HEAP, lsr #32
    //     0x80d508: b.eq            #0x80d510
    //     0x80d50c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80d510: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80d510: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80d514: r0 = Null
    //     0x80d514: mov             x0, NULL
    // 0x80d518: LeaveFrame
    //     0x80d518: mov             SP, fp
    //     0x80d51c: ldp             fp, lr, [SP], #0x10
    // 0x80d520: ret
    //     0x80d520: ret             
    // 0x80d524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d524: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d528: b               #0x80d468
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8aa1e4, size: 0x90
    // 0x8aa1e4: EnterFrame
    //     0x8aa1e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa1e8: mov             fp, SP
    // 0x8aa1ec: AllocStack(0x28)
    //     0x8aa1ec: sub             SP, SP, #0x28
    // 0x8aa1f0: SetupParameters(ArticleChannelController this /* r1 => r1, fp-0x8 */)
    //     0x8aa1f0: stur            x1, [fp, #-8]
    // 0x8aa1f4: CheckStackOverflow
    //     0x8aa1f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa1f8: cmp             SP, x16
    //     0x8aa1fc: b.ls            #0x8aa26c
    // 0x8aa200: r1 = 1
    //     0x8aa200: movz            x1, #0x1
    // 0x8aa204: r0 = AllocateContext()
    //     0x8aa204: bl              #0xec126c  ; AllocateContextStub
    // 0x8aa208: mov             x2, x0
    // 0x8aa20c: ldur            x0, [fp, #-8]
    // 0x8aa210: stur            x2, [fp, #-0x10]
    // 0x8aa214: StoreField: r2->field_f = r0
    //     0x8aa214: stur            w0, [x2, #0xf]
    // 0x8aa218: mov             x1, x0
    // 0x8aa21c: r0 = onInit()
    //     0x8aa21c: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8aa220: ldur            x0, [fp, #-8]
    // 0x8aa224: LoadField: r1 = r0->field_27
    //     0x8aa224: ldur            w1, [x0, #0x27]
    // 0x8aa228: DecompressPointer r1
    //     0x8aa228: add             x1, x1, HEAP, lsl #32
    // 0x8aa22c: r0 = findAll()
    //     0x8aa22c: bl              #0x8aa274  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::findAll
    // 0x8aa230: ldur            x2, [fp, #-0x10]
    // 0x8aa234: r1 = Function '<anonymous closure>':.
    //     0x8aa234: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f90] AnonymousClosure: (0x8ab2ec), in [package:nuonline/app/modules/article/article_channel/controllers/article_channel_controller.dart] ArticleChannelController::onInit (0x8aa1e4)
    //     0x8aa238: ldr             x1, [x1, #0xf90]
    // 0x8aa23c: stur            x0, [fp, #-8]
    // 0x8aa240: r0 = AllocateClosure()
    //     0x8aa240: bl              #0xec1630  ; AllocateClosureStub
    // 0x8aa244: r16 = <void?>
    //     0x8aa244: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8aa248: ldur            lr, [fp, #-8]
    // 0x8aa24c: stp             lr, x16, [SP, #8]
    // 0x8aa250: str             x0, [SP]
    // 0x8aa254: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa254: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa258: r0 = then()
    //     0x8aa258: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8aa25c: r0 = Null
    //     0x8aa25c: mov             x0, NULL
    // 0x8aa260: LeaveFrame
    //     0x8aa260: mov             SP, fp
    //     0x8aa264: ldp             fp, lr, [SP], #0x10
    // 0x8aa268: ret
    //     0x8aa268: ret             
    // 0x8aa26c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa26c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa270: b               #0x8aa200
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Category>>) {
    // ** addr: 0x8ab2ec, size: 0xa0
    // 0x8ab2ec: EnterFrame
    //     0x8ab2ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab2f0: mov             fp, SP
    // 0x8ab2f4: AllocStack(0x28)
    //     0x8ab2f4: sub             SP, SP, #0x28
    // 0x8ab2f8: SetupParameters()
    //     0x8ab2f8: ldr             x0, [fp, #0x18]
    //     0x8ab2fc: ldur            w1, [x0, #0x17]
    //     0x8ab300: add             x1, x1, HEAP, lsl #32
    // 0x8ab304: CheckStackOverflow
    //     0x8ab304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab308: cmp             SP, x16
    //     0x8ab30c: b.ls            #0x8ab384
    // 0x8ab310: LoadField: r0 = r1->field_f
    //     0x8ab310: ldur            w0, [x1, #0xf]
    // 0x8ab314: DecompressPointer r0
    //     0x8ab314: add             x0, x0, HEAP, lsl #32
    // 0x8ab318: mov             x2, x0
    // 0x8ab31c: stur            x0, [fp, #-8]
    // 0x8ab320: r1 = Function '_onSuccess@1859118640':.
    //     0x8ab320: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f98] AnonymousClosure: (0x8ab38c), in [package:nuonline/app/modules/article/article_channel/controllers/article_channel_controller.dart] ArticleChannelController::_onSuccess (0x8ab3cc)
    //     0x8ab324: ldr             x1, [x1, #0xf98]
    // 0x8ab328: r0 = AllocateClosure()
    //     0x8ab328: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab32c: ldur            x2, [fp, #-8]
    // 0x8ab330: r1 = Function '_onError@1859118640':.
    //     0x8ab330: add             x1, PP, #0x40, lsl #12  ; [pp+0x40fa0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8ab334: ldr             x1, [x1, #0xfa0]
    // 0x8ab338: stur            x0, [fp, #-8]
    // 0x8ab33c: r0 = AllocateClosure()
    //     0x8ab33c: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab340: mov             x1, x0
    // 0x8ab344: ldr             x0, [fp, #0x10]
    // 0x8ab348: r2 = LoadClassIdInstr(r0)
    //     0x8ab348: ldur            x2, [x0, #-1]
    //     0x8ab34c: ubfx            x2, x2, #0xc, #0x14
    // 0x8ab350: r16 = <void?>
    //     0x8ab350: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8ab354: stp             x0, x16, [SP, #0x10]
    // 0x8ab358: ldur            x16, [fp, #-8]
    // 0x8ab35c: stp             x16, x1, [SP]
    // 0x8ab360: mov             x0, x2
    // 0x8ab364: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8ab364: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8ab368: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8ab368: sub             lr, x0, #1, lsl #12
    //     0x8ab36c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ab370: blr             lr
    // 0x8ab374: r0 = Null
    //     0x8ab374: mov             x0, NULL
    // 0x8ab378: LeaveFrame
    //     0x8ab378: mov             SP, fp
    //     0x8ab37c: ldp             fp, lr, [SP], #0x10
    // 0x8ab380: ret
    //     0x8ab380: ret             
    // 0x8ab384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab388: b               #0x8ab310
  }
  [closure] void _onSuccess(dynamic, List<Category>, dynamic) {
    // ** addr: 0x8ab38c, size: 0x40
    // 0x8ab38c: EnterFrame
    //     0x8ab38c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab390: mov             fp, SP
    // 0x8ab394: ldr             x0, [fp, #0x20]
    // 0x8ab398: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ab398: ldur            w1, [x0, #0x17]
    // 0x8ab39c: DecompressPointer r1
    //     0x8ab39c: add             x1, x1, HEAP, lsl #32
    // 0x8ab3a0: CheckStackOverflow
    //     0x8ab3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab3a4: cmp             SP, x16
    //     0x8ab3a8: b.ls            #0x8ab3c4
    // 0x8ab3ac: ldr             x2, [fp, #0x18]
    // 0x8ab3b0: ldr             x3, [fp, #0x10]
    // 0x8ab3b4: r0 = _onSuccess()
    //     0x8ab3b4: bl              #0x8ab3cc  ; [package:nuonline/app/modules/article/article_channel/controllers/article_channel_controller.dart] ArticleChannelController::_onSuccess
    // 0x8ab3b8: LeaveFrame
    //     0x8ab3b8: mov             SP, fp
    //     0x8ab3bc: ldp             fp, lr, [SP], #0x10
    // 0x8ab3c0: ret
    //     0x8ab3c0: ret             
    // 0x8ab3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab3c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab3c8: b               #0x8ab3ac
  }
  _ _onSuccess(/* No info */) {
    // ** addr: 0x8ab3cc, size: 0xc8
    // 0x8ab3cc: EnterFrame
    //     0x8ab3cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab3d0: mov             fp, SP
    // 0x8ab3d4: AllocStack(0x10)
    //     0x8ab3d4: sub             SP, SP, #0x10
    // 0x8ab3d8: SetupParameters(ArticleChannelController this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x8ab3d8: mov             x0, x1
    //     0x8ab3dc: stur            x1, [fp, #-8]
    //     0x8ab3e0: mov             x1, x2
    //     0x8ab3e4: stur            x2, [fp, #-0x10]
    // 0x8ab3e8: CheckStackOverflow
    //     0x8ab3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab3ec: cmp             SP, x16
    //     0x8ab3f0: b.ls            #0x8ab48c
    // 0x8ab3f4: r1 = 1
    //     0x8ab3f4: movz            x1, #0x1
    // 0x8ab3f8: r0 = AllocateContext()
    //     0x8ab3f8: bl              #0xec126c  ; AllocateContextStub
    // 0x8ab3fc: mov             x1, x0
    // 0x8ab400: ldur            x0, [fp, #-8]
    // 0x8ab404: StoreField: r1->field_f = r0
    //     0x8ab404: stur            w0, [x1, #0xf]
    // 0x8ab408: mov             x2, x1
    // 0x8ab40c: r1 = Function '<anonymous closure>':.
    //     0x8ab40c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40fa8] AnonymousClosure: (0x8ab494), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::_onSuccess (0x8ab4d0)
    //     0x8ab410: ldr             x1, [x1, #0xfa8]
    // 0x8ab414: r0 = AllocateClosure()
    //     0x8ab414: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab418: ldur            x1, [fp, #-0x10]
    // 0x8ab41c: r2 = LoadClassIdInstr(r1)
    //     0x8ab41c: ldur            x2, [x1, #-1]
    //     0x8ab420: ubfx            x2, x2, #0xc, #0x14
    // 0x8ab424: mov             x16, x0
    // 0x8ab428: mov             x0, x2
    // 0x8ab42c: mov             x2, x16
    // 0x8ab430: r0 = GDT[cid_x0 + 0xea28]()
    //     0x8ab430: movz            x17, #0xea28
    //     0x8ab434: add             lr, x0, x17
    //     0x8ab438: ldr             lr, [x21, lr, lsl #3]
    //     0x8ab43c: blr             lr
    // 0x8ab440: LoadField: r1 = r0->field_7
    //     0x8ab440: ldur            w1, [x0, #7]
    // 0x8ab444: DecompressPointer r1
    //     0x8ab444: add             x1, x1, HEAP, lsl #32
    // 0x8ab448: mov             x2, x0
    // 0x8ab44c: r0 = _GrowableList.of()
    //     0x8ab44c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8ab450: stur            x0, [fp, #-0x10]
    // 0x8ab454: r0 = RxStatus()
    //     0x8ab454: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8ab458: mov             x1, x0
    // 0x8ab45c: r0 = false
    //     0x8ab45c: add             x0, NULL, #0x30  ; false
    // 0x8ab460: StoreField: r1->field_f = r0
    //     0x8ab460: stur            w0, [x1, #0xf]
    // 0x8ab464: StoreField: r1->field_7 = r0
    //     0x8ab464: stur            w0, [x1, #7]
    // 0x8ab468: StoreField: r1->field_b = r0
    //     0x8ab468: stur            w0, [x1, #0xb]
    // 0x8ab46c: mov             x3, x1
    // 0x8ab470: ldur            x1, [fp, #-8]
    // 0x8ab474: ldur            x2, [fp, #-0x10]
    // 0x8ab478: r0 = change()
    //     0x8ab478: bl              #0x8aa078  ; [package:nuonline/app/modules/article/article_bookmark/controllers/article_bookmark_controller.dart] _ArticleBookmarkController&GetxController&StateMixin::change
    // 0x8ab47c: r0 = Null
    //     0x8ab47c: mov             x0, NULL
    // 0x8ab480: LeaveFrame
    //     0x8ab480: mov             SP, fp
    //     0x8ab484: ldp             fp, lr, [SP], #0x10
    // 0x8ab488: ret
    //     0x8ab488: ret             
    // 0x8ab48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab48c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab490: b               #0x8ab3f4
  }
}
