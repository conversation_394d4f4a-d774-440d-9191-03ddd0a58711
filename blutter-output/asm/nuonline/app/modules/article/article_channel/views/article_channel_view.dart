// lib: , url: package:nuonline/app/modules/article/article_channel/views/article_channel_view.dart

// class id: 1050127, size: 0x8
class :: {
}

// class id: 5310, size: 0x14, field offset: 0x14
class ArticleChannelView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xacfed0, size: 0x114
    // 0xacfed0: EnterFrame
    //     0xacfed0: stp             fp, lr, [SP, #-0x10]!
    //     0xacfed4: mov             fp, SP
    // 0xacfed8: AllocStack(0x30)
    //     0xacfed8: sub             SP, SP, #0x30
    // 0xacfedc: SetupParameters(ArticleChannelView this /* r1 => r0, fp-0x8 */)
    //     0xacfedc: mov             x0, x1
    //     0xacfee0: stur            x1, [fp, #-8]
    // 0xacfee4: CheckStackOverflow
    //     0xacfee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfee8: cmp             SP, x16
    //     0xacfeec: b.ls            #0xacffdc
    // 0xacfef0: mov             x1, x0
    // 0xacfef4: r0 = controller()
    //     0xacfef4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xacfef8: LoadField: r1 = r0->field_2b
    //     0xacfef8: ldur            w1, [x0, #0x2b]
    // 0xacfefc: DecompressPointer r1
    //     0xacfefc: add             x1, x1, HEAP, lsl #32
    // 0xacff00: tbnz            w1, #4, #0xacff10
    // 0xacff04: r0 = "Kanal Keislaman"
    //     0xacff04: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b88] "Kanal Keislaman"
    //     0xacff08: ldr             x0, [x0, #0xb88]
    // 0xacff0c: b               #0xacff18
    // 0xacff10: r0 = "Kanal Utama"
    //     0xacff10: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b90] "Kanal Utama"
    //     0xacff14: ldr             x0, [x0, #0xb90]
    // 0xacff18: stur            x0, [fp, #-0x10]
    // 0xacff1c: r0 = Text()
    //     0xacff1c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xacff20: mov             x1, x0
    // 0xacff24: ldur            x0, [fp, #-0x10]
    // 0xacff28: stur            x1, [fp, #-0x18]
    // 0xacff2c: StoreField: r1->field_b = r0
    //     0xacff2c: stur            w0, [x1, #0xb]
    // 0xacff30: r0 = AppBar()
    //     0xacff30: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xacff34: stur            x0, [fp, #-0x10]
    // 0xacff38: ldur            x16, [fp, #-0x18]
    // 0xacff3c: str             x16, [SP]
    // 0xacff40: mov             x1, x0
    // 0xacff44: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xacff44: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xacff48: ldr             x4, [x4, #0x6e8]
    // 0xacff4c: r0 = AppBar()
    //     0xacff4c: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xacff50: ldur            x1, [fp, #-8]
    // 0xacff54: r0 = controller()
    //     0xacff54: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xacff58: r1 = Function '<anonymous closure>':.
    //     0xacff58: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b98] AnonymousClosure: (0xacffe4), in [package:nuonline/app/modules/article/article_channel/views/article_channel_view.dart] ArticleChannelView::build (0xacfed0)
    //     0xacff5c: ldr             x1, [x1, #0xb98]
    // 0xacff60: r2 = Null
    //     0xacff60: mov             x2, NULL
    // 0xacff64: stur            x0, [fp, #-8]
    // 0xacff68: r0 = AllocateClosure()
    //     0xacff68: bl              #0xec1630  ; AllocateClosureStub
    // 0xacff6c: r16 = <List<Category>>
    //     0xacff6c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30ba0] TypeArguments: <List<Category>>
    //     0xacff70: ldr             x16, [x16, #0xba0]
    // 0xacff74: ldur            lr, [fp, #-8]
    // 0xacff78: stp             lr, x16, [SP, #8]
    // 0xacff7c: str             x0, [SP]
    // 0xacff80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xacff80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xacff84: r0 = StateExt.obx()
    //     0xacff84: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xacff88: stur            x0, [fp, #-8]
    // 0xacff8c: r0 = Scaffold()
    //     0xacff8c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xacff90: ldur            x1, [fp, #-0x10]
    // 0xacff94: StoreField: r0->field_13 = r1
    //     0xacff94: stur            w1, [x0, #0x13]
    // 0xacff98: ldur            x1, [fp, #-8]
    // 0xacff9c: ArrayStore: r0[0] = r1  ; List_4
    //     0xacff9c: stur            w1, [x0, #0x17]
    // 0xacffa0: r1 = Instance_AlignmentDirectional
    //     0xacffa0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xacffa4: ldr             x1, [x1, #0x758]
    // 0xacffa8: StoreField: r0->field_2b = r1
    //     0xacffa8: stur            w1, [x0, #0x2b]
    // 0xacffac: r1 = true
    //     0xacffac: add             x1, NULL, #0x20  ; true
    // 0xacffb0: StoreField: r0->field_53 = r1
    //     0xacffb0: stur            w1, [x0, #0x53]
    // 0xacffb4: r2 = Instance_DragStartBehavior
    //     0xacffb4: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xacffb8: StoreField: r0->field_57 = r2
    //     0xacffb8: stur            w2, [x0, #0x57]
    // 0xacffbc: r2 = false
    //     0xacffbc: add             x2, NULL, #0x30  ; false
    // 0xacffc0: StoreField: r0->field_b = r2
    //     0xacffc0: stur            w2, [x0, #0xb]
    // 0xacffc4: StoreField: r0->field_f = r2
    //     0xacffc4: stur            w2, [x0, #0xf]
    // 0xacffc8: StoreField: r0->field_5f = r1
    //     0xacffc8: stur            w1, [x0, #0x5f]
    // 0xacffcc: StoreField: r0->field_63 = r1
    //     0xacffcc: stur            w1, [x0, #0x63]
    // 0xacffd0: LeaveFrame
    //     0xacffd0: mov             SP, fp
    //     0xacffd4: ldp             fp, lr, [SP], #0x10
    // 0xacffd8: ret
    //     0xacffd8: ret             
    // 0xacffdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacffdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacffe0: b               #0xacfef0
  }
  [closure] ListTileTheme <anonymous closure>(dynamic, List<Category>?) {
    // ** addr: 0xacffe4, size: 0x100
    // 0xacffe4: EnterFrame
    //     0xacffe4: stp             fp, lr, [SP, #-0x10]!
    //     0xacffe8: mov             fp, SP
    // 0xacffec: AllocStack(0x28)
    //     0xacffec: sub             SP, SP, #0x28
    // 0xacfff0: SetupParameters()
    //     0xacfff0: ldr             x0, [fp, #0x18]
    //     0xacfff4: ldur            w1, [x0, #0x17]
    //     0xacfff8: add             x1, x1, HEAP, lsl #32
    //     0xacfffc: stur            x1, [fp, #-8]
    // 0xad0000: CheckStackOverflow
    //     0xad0000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0004: cmp             SP, x16
    //     0xad0008: b.ls            #0xad00d8
    // 0xad000c: r1 = 1
    //     0xad000c: movz            x1, #0x1
    // 0xad0010: r0 = AllocateContext()
    //     0xad0010: bl              #0xec126c  ; AllocateContextStub
    // 0xad0014: mov             x1, x0
    // 0xad0018: ldur            x0, [fp, #-8]
    // 0xad001c: stur            x1, [fp, #-0x10]
    // 0xad0020: StoreField: r1->field_b = r0
    //     0xad0020: stur            w0, [x1, #0xb]
    // 0xad0024: ldr             x0, [fp, #0x10]
    // 0xad0028: StoreField: r1->field_f = r0
    //     0xad0028: stur            w0, [x1, #0xf]
    // 0xad002c: cmp             w0, NULL
    // 0xad0030: b.eq            #0xad00e0
    // 0xad0034: r2 = LoadClassIdInstr(r0)
    //     0xad0034: ldur            x2, [x0, #-1]
    //     0xad0038: ubfx            x2, x2, #0xc, #0x14
    // 0xad003c: str             x0, [SP]
    // 0xad0040: mov             x0, x2
    // 0xad0044: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad0044: movz            x17, #0xc834
    //     0xad0048: add             lr, x0, x17
    //     0xad004c: ldr             lr, [x21, lr, lsl #3]
    //     0xad0050: blr             lr
    // 0xad0054: r3 = LoadInt32Instr(r0)
    //     0xad0054: sbfx            x3, x0, #1, #0x1f
    //     0xad0058: tbz             w0, #0, #0xad0060
    //     0xad005c: ldur            x3, [x0, #7]
    // 0xad0060: ldur            x2, [fp, #-0x10]
    // 0xad0064: stur            x3, [fp, #-0x18]
    // 0xad0068: r1 = Function '<anonymous closure>':.
    //     0xad0068: add             x1, PP, #0x30, lsl #12  ; [pp+0x30ba8] AnonymousClosure: (0xad00e4), in [package:nuonline/app/modules/article/article_channel/views/article_channel_view.dart] ArticleChannelView::build (0xacfed0)
    //     0xad006c: ldr             x1, [x1, #0xba8]
    // 0xad0070: r0 = AllocateClosure()
    //     0xad0070: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0074: r1 = Function '<anonymous closure>':.
    //     0xad0074: add             x1, PP, #0x30, lsl #12  ; [pp+0x30bb0] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad0078: ldr             x1, [x1, #0xbb0]
    // 0xad007c: r2 = Null
    //     0xad007c: mov             x2, NULL
    // 0xad0080: stur            x0, [fp, #-8]
    // 0xad0084: r0 = AllocateClosure()
    //     0xad0084: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0088: stur            x0, [fp, #-0x10]
    // 0xad008c: r0 = ListView()
    //     0xad008c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad0090: stur            x0, [fp, #-0x20]
    // 0xad0094: r16 = Instance_EdgeInsets
    //     0xad0094: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad0098: str             x16, [SP]
    // 0xad009c: mov             x1, x0
    // 0xad00a0: ldur            x2, [fp, #-8]
    // 0xad00a4: ldur            x3, [fp, #-0x18]
    // 0xad00a8: ldur            x5, [fp, #-0x10]
    // 0xad00ac: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xad00ac: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xad00b0: ldr             x4, [x4, #0x700]
    // 0xad00b4: r0 = ListView.separated()
    //     0xad00b4: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad00b8: r0 = ListTileTheme()
    //     0xad00b8: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xad00bc: r1 = Instance_EdgeInsets
    //     0xad00bc: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad00c0: StoreField: r0->field_2b = r1
    //     0xad00c0: stur            w1, [x0, #0x2b]
    // 0xad00c4: ldur            x1, [fp, #-0x20]
    // 0xad00c8: StoreField: r0->field_b = r1
    //     0xad00c8: stur            w1, [x0, #0xb]
    // 0xad00cc: LeaveFrame
    //     0xad00cc: mov             SP, fp
    //     0xad00d0: ldp             fp, lr, [SP], #0x10
    // 0xad00d4: ret
    //     0xad00d4: ret             
    // 0xad00d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad00d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad00dc: b               #0xad000c
    // 0xad00e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad00e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad00e4, size: 0x170
    // 0xad00e4: EnterFrame
    //     0xad00e4: stp             fp, lr, [SP, #-0x10]!
    //     0xad00e8: mov             fp, SP
    // 0xad00ec: AllocStack(0x38)
    //     0xad00ec: sub             SP, SP, #0x38
    // 0xad00f0: SetupParameters()
    //     0xad00f0: ldr             x0, [fp, #0x20]
    //     0xad00f4: ldur            w1, [x0, #0x17]
    //     0xad00f8: add             x1, x1, HEAP, lsl #32
    //     0xad00fc: stur            x1, [fp, #-8]
    // 0xad0100: CheckStackOverflow
    //     0xad0100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0104: cmp             SP, x16
    //     0xad0108: b.ls            #0xad024c
    // 0xad010c: r1 = 1
    //     0xad010c: movz            x1, #0x1
    // 0xad0110: r0 = AllocateContext()
    //     0xad0110: bl              #0xec126c  ; AllocateContextStub
    // 0xad0114: mov             x1, x0
    // 0xad0118: ldur            x0, [fp, #-8]
    // 0xad011c: stur            x1, [fp, #-0x10]
    // 0xad0120: StoreField: r1->field_b = r0
    //     0xad0120: stur            w0, [x1, #0xb]
    // 0xad0124: ldr             x2, [fp, #0x10]
    // 0xad0128: StoreField: r1->field_f = r2
    //     0xad0128: stur            w2, [x1, #0xf]
    // 0xad012c: LoadField: r3 = r0->field_f
    //     0xad012c: ldur            w3, [x0, #0xf]
    // 0xad0130: DecompressPointer r3
    //     0xad0130: add             x3, x3, HEAP, lsl #32
    // 0xad0134: r0 = LoadClassIdInstr(r3)
    //     0xad0134: ldur            x0, [x3, #-1]
    //     0xad0138: ubfx            x0, x0, #0xc, #0x14
    // 0xad013c: stp             x2, x3, [SP]
    // 0xad0140: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad0140: movz            x17, #0x3037
    //     0xad0144: movk            x17, #0x1, lsl #16
    //     0xad0148: add             lr, x0, x17
    //     0xad014c: ldr             lr, [x21, lr, lsl #3]
    //     0xad0150: blr             lr
    // 0xad0154: LoadField: r1 = r0->field_1b
    //     0xad0154: ldur            w1, [x0, #0x1b]
    // 0xad0158: DecompressPointer r1
    //     0xad0158: add             x1, x1, HEAP, lsl #32
    // 0xad015c: stur            x1, [fp, #-8]
    // 0xad0160: r0 = Text()
    //     0xad0160: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad0164: mov             x3, x0
    // 0xad0168: ldur            x0, [fp, #-8]
    // 0xad016c: stur            x3, [fp, #-0x18]
    // 0xad0170: StoreField: r3->field_b = r0
    //     0xad0170: stur            w0, [x3, #0xb]
    // 0xad0174: r1 = _ConstMap len:6
    //     0xad0174: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xad0178: ldr             x1, [x1, #0xc20]
    // 0xad017c: r2 = 6
    //     0xad017c: movz            x2, #0x6
    // 0xad0180: r0 = []()
    //     0xad0180: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xad0184: r1 = _ConstMap len:6
    //     0xad0184: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xad0188: ldr             x1, [x1, #0xc20]
    // 0xad018c: r2 = 8
    //     0xad018c: movz            x2, #0x8
    // 0xad0190: stur            x0, [fp, #-8]
    // 0xad0194: r0 = []()
    //     0xad0194: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xad0198: r16 = <Color?>
    //     0xad0198: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xad019c: ldr             x16, [x16, #0x98]
    // 0xad01a0: stp             x0, x16, [SP, #8]
    // 0xad01a4: ldur            x16, [fp, #-8]
    // 0xad01a8: str             x16, [SP]
    // 0xad01ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad01ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad01b0: r0 = mode()
    //     0xad01b0: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xad01b4: stur            x0, [fp, #-8]
    // 0xad01b8: r0 = Icon()
    //     0xad01b8: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xad01bc: mov             x1, x0
    // 0xad01c0: r0 = Instance_IconData
    //     0xad01c0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29fa8] Obj!IconData@e0fe71
    //     0xad01c4: ldr             x0, [x0, #0xfa8]
    // 0xad01c8: stur            x1, [fp, #-0x20]
    // 0xad01cc: StoreField: r1->field_b = r0
    //     0xad01cc: stur            w0, [x1, #0xb]
    // 0xad01d0: r0 = 20.000000
    //     0xad01d0: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xad01d4: ldr             x0, [x0, #0x430]
    // 0xad01d8: StoreField: r1->field_f = r0
    //     0xad01d8: stur            w0, [x1, #0xf]
    // 0xad01dc: ldur            x0, [fp, #-8]
    // 0xad01e0: StoreField: r1->field_23 = r0
    //     0xad01e0: stur            w0, [x1, #0x23]
    // 0xad01e4: r0 = ListTile()
    //     0xad01e4: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xad01e8: mov             x3, x0
    // 0xad01ec: ldur            x0, [fp, #-0x18]
    // 0xad01f0: stur            x3, [fp, #-8]
    // 0xad01f4: StoreField: r3->field_f = r0
    //     0xad01f4: stur            w0, [x3, #0xf]
    // 0xad01f8: ldur            x0, [fp, #-0x20]
    // 0xad01fc: ArrayStore: r3[0] = r0  ; List_4
    //     0xad01fc: stur            w0, [x3, #0x17]
    // 0xad0200: r0 = false
    //     0xad0200: add             x0, NULL, #0x30  ; false
    // 0xad0204: StoreField: r3->field_1b = r0
    //     0xad0204: stur            w0, [x3, #0x1b]
    // 0xad0208: r4 = true
    //     0xad0208: add             x4, NULL, #0x20  ; true
    // 0xad020c: StoreField: r3->field_4b = r4
    //     0xad020c: stur            w4, [x3, #0x4b]
    // 0xad0210: ldur            x2, [fp, #-0x10]
    // 0xad0214: r1 = Function '<anonymous closure>':.
    //     0xad0214: add             x1, PP, #0x30, lsl #12  ; [pp+0x30bb8] AnonymousClosure: (0xad0254), in [package:nuonline/app/modules/article/article_channel/views/article_channel_view.dart] ArticleChannelView::build (0xacfed0)
    //     0xad0218: ldr             x1, [x1, #0xbb8]
    // 0xad021c: r0 = AllocateClosure()
    //     0xad021c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0220: mov             x1, x0
    // 0xad0224: ldur            x0, [fp, #-8]
    // 0xad0228: StoreField: r0->field_4f = r1
    //     0xad0228: stur            w1, [x0, #0x4f]
    // 0xad022c: r1 = false
    //     0xad022c: add             x1, NULL, #0x30  ; false
    // 0xad0230: StoreField: r0->field_5f = r1
    //     0xad0230: stur            w1, [x0, #0x5f]
    // 0xad0234: StoreField: r0->field_73 = r1
    //     0xad0234: stur            w1, [x0, #0x73]
    // 0xad0238: r1 = true
    //     0xad0238: add             x1, NULL, #0x20  ; true
    // 0xad023c: StoreField: r0->field_97 = r1
    //     0xad023c: stur            w1, [x0, #0x97]
    // 0xad0240: LeaveFrame
    //     0xad0240: mov             SP, fp
    //     0xad0244: ldp             fp, lr, [SP], #0x10
    // 0xad0248: ret
    //     0xad0248: ret             
    // 0xad024c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad024c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0250: b               #0xad010c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad0254, size: 0x1a8
    // 0xad0254: EnterFrame
    //     0xad0254: stp             fp, lr, [SP, #-0x10]!
    //     0xad0258: mov             fp, SP
    // 0xad025c: AllocStack(0x30)
    //     0xad025c: sub             SP, SP, #0x30
    // 0xad0260: SetupParameters()
    //     0xad0260: ldr             x0, [fp, #0x10]
    //     0xad0264: ldur            w1, [x0, #0x17]
    //     0xad0268: add             x1, x1, HEAP, lsl #32
    //     0xad026c: stur            x1, [fp, #-8]
    // 0xad0270: CheckStackOverflow
    //     0xad0270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0274: cmp             SP, x16
    //     0xad0278: b.ls            #0xad03f4
    // 0xad027c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad027c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad0280: ldr             x0, [x0, #0x2670]
    //     0xad0284: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad0288: cmp             w0, w16
    //     0xad028c: b.ne            #0xad0298
    //     0xad0290: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad0294: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad0298: r1 = Null
    //     0xad0298: mov             x1, NULL
    // 0xad029c: r2 = 8
    //     0xad029c: movz            x2, #0x8
    // 0xad02a0: r0 = AllocateArray()
    //     0xad02a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad02a4: mov             x1, x0
    // 0xad02a8: stur            x1, [fp, #-0x18]
    // 0xad02ac: r16 = "id"
    //     0xad02ac: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xad02b0: ldr             x16, [x16, #0x740]
    // 0xad02b4: StoreField: r1->field_f = r16
    //     0xad02b4: stur            w16, [x1, #0xf]
    // 0xad02b8: ldur            x2, [fp, #-8]
    // 0xad02bc: LoadField: r3 = r2->field_b
    //     0xad02bc: ldur            w3, [x2, #0xb]
    // 0xad02c0: DecompressPointer r3
    //     0xad02c0: add             x3, x3, HEAP, lsl #32
    // 0xad02c4: stur            x3, [fp, #-0x10]
    // 0xad02c8: LoadField: r0 = r3->field_f
    //     0xad02c8: ldur            w0, [x3, #0xf]
    // 0xad02cc: DecompressPointer r0
    //     0xad02cc: add             x0, x0, HEAP, lsl #32
    // 0xad02d0: LoadField: r4 = r2->field_f
    //     0xad02d0: ldur            w4, [x2, #0xf]
    // 0xad02d4: DecompressPointer r4
    //     0xad02d4: add             x4, x4, HEAP, lsl #32
    // 0xad02d8: r5 = LoadClassIdInstr(r0)
    //     0xad02d8: ldur            x5, [x0, #-1]
    //     0xad02dc: ubfx            x5, x5, #0xc, #0x14
    // 0xad02e0: stp             x4, x0, [SP]
    // 0xad02e4: mov             x0, x5
    // 0xad02e8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad02e8: movz            x17, #0x3037
    //     0xad02ec: movk            x17, #0x1, lsl #16
    //     0xad02f0: add             lr, x0, x17
    //     0xad02f4: ldr             lr, [x21, lr, lsl #3]
    //     0xad02f8: blr             lr
    // 0xad02fc: LoadField: r2 = r0->field_13
    //     0xad02fc: ldur            x2, [x0, #0x13]
    // 0xad0300: r0 = BoxInt64Instr(r2)
    //     0xad0300: sbfiz           x0, x2, #1, #0x1f
    //     0xad0304: cmp             x2, x0, asr #1
    //     0xad0308: b.eq            #0xad0314
    //     0xad030c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad0310: stur            x2, [x0, #7]
    // 0xad0314: ldur            x1, [fp, #-0x18]
    // 0xad0318: ArrayStore: r1[1] = r0  ; List_4
    //     0xad0318: add             x25, x1, #0x13
    //     0xad031c: str             w0, [x25]
    //     0xad0320: tbz             w0, #0, #0xad033c
    //     0xad0324: ldurb           w16, [x1, #-1]
    //     0xad0328: ldurb           w17, [x0, #-1]
    //     0xad032c: and             x16, x17, x16, lsr #2
    //     0xad0330: tst             x16, HEAP, lsr #32
    //     0xad0334: b.eq            #0xad033c
    //     0xad0338: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad033c: ldur            x1, [fp, #-0x18]
    // 0xad0340: r16 = "title"
    //     0xad0340: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xad0344: ldr             x16, [x16, #0x748]
    // 0xad0348: ArrayStore: r1[0] = r16  ; List_4
    //     0xad0348: stur            w16, [x1, #0x17]
    // 0xad034c: ldur            x0, [fp, #-0x10]
    // 0xad0350: LoadField: r2 = r0->field_f
    //     0xad0350: ldur            w2, [x0, #0xf]
    // 0xad0354: DecompressPointer r2
    //     0xad0354: add             x2, x2, HEAP, lsl #32
    // 0xad0358: ldur            x0, [fp, #-8]
    // 0xad035c: LoadField: r3 = r0->field_f
    //     0xad035c: ldur            w3, [x0, #0xf]
    // 0xad0360: DecompressPointer r3
    //     0xad0360: add             x3, x3, HEAP, lsl #32
    // 0xad0364: r0 = LoadClassIdInstr(r2)
    //     0xad0364: ldur            x0, [x2, #-1]
    //     0xad0368: ubfx            x0, x0, #0xc, #0x14
    // 0xad036c: stp             x3, x2, [SP]
    // 0xad0370: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad0370: movz            x17, #0x3037
    //     0xad0374: movk            x17, #0x1, lsl #16
    //     0xad0378: add             lr, x0, x17
    //     0xad037c: ldr             lr, [x21, lr, lsl #3]
    //     0xad0380: blr             lr
    // 0xad0384: LoadField: r1 = r0->field_1b
    //     0xad0384: ldur            w1, [x0, #0x1b]
    // 0xad0388: DecompressPointer r1
    //     0xad0388: add             x1, x1, HEAP, lsl #32
    // 0xad038c: mov             x0, x1
    // 0xad0390: ldur            x1, [fp, #-0x18]
    // 0xad0394: ArrayStore: r1[3] = r0  ; List_4
    //     0xad0394: add             x25, x1, #0x1b
    //     0xad0398: str             w0, [x25]
    //     0xad039c: tbz             w0, #0, #0xad03b8
    //     0xad03a0: ldurb           w16, [x1, #-1]
    //     0xad03a4: ldurb           w17, [x0, #-1]
    //     0xad03a8: and             x16, x17, x16, lsr #2
    //     0xad03ac: tst             x16, HEAP, lsr #32
    //     0xad03b0: b.eq            #0xad03b8
    //     0xad03b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad03b8: r16 = <String, Object>
    //     0xad03b8: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xad03bc: ldr             x16, [x16, #0x790]
    // 0xad03c0: ldur            lr, [fp, #-0x18]
    // 0xad03c4: stp             lr, x16, [SP]
    // 0xad03c8: r0 = Map._fromLiteral()
    //     0xad03c8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xad03cc: r16 = "/article/article-category"
    //     0xad03cc: add             x16, PP, #0xf, lsl #12  ; [pp+0xfcd8] "/article/article-category"
    //     0xad03d0: ldr             x16, [x16, #0xcd8]
    // 0xad03d4: stp             x16, NULL, [SP, #8]
    // 0xad03d8: str             x0, [SP]
    // 0xad03dc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad03dc: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad03e0: ldr             x4, [x4, #0x478]
    // 0xad03e4: r0 = GetNavigation.toNamed()
    //     0xad03e4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad03e8: LeaveFrame
    //     0xad03e8: mov             SP, fp
    //     0xad03ec: ldp             fp, lr, [SP], #0x10
    // 0xad03f0: ret
    //     0xad03f0: ret             
    // 0xad03f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad03f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad03f8: b               #0xad027c
  }
}
