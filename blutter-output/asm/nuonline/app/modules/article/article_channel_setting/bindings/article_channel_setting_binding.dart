// lib: , url: package:nuonline/app/modules/article/article_channel_setting/bindings/article_channel_setting_binding.dart

// class id: 1050128, size: 0x8
class :: {
}

// class id: 2195, size: 0x8, field offset: 0x8
class ArticleChannelSettingBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80d664, size: 0x70
    // 0x80d664: EnterFrame
    //     0x80d664: stp             fp, lr, [SP, #-0x10]!
    //     0x80d668: mov             fp, SP
    // 0x80d66c: AllocStack(0x10)
    //     0x80d66c: sub             SP, SP, #0x10
    // 0x80d670: CheckStackOverflow
    //     0x80d670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d674: cmp             SP, x16
    //     0x80d678: b.ls            #0x80d6cc
    // 0x80d67c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d67c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d680: ldr             x0, [x0, #0x2670]
    //     0x80d684: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d688: cmp             w0, w16
    //     0x80d68c: b.ne            #0x80d698
    //     0x80d690: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d694: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d698: r1 = Function '<anonymous closure>':.
    //     0x80d698: add             x1, PP, #0x36, lsl #12  ; [pp+0x36988] AnonymousClosure: (0x80d6d4), in [package:nuonline/app/modules/article/article_channel_setting/bindings/article_channel_setting_binding.dart] ArticleChannelSettingBinding::dependencies (0x80d664)
    //     0x80d69c: ldr             x1, [x1, #0x988]
    // 0x80d6a0: r2 = Null
    //     0x80d6a0: mov             x2, NULL
    // 0x80d6a4: r0 = AllocateClosure()
    //     0x80d6a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x80d6a8: r16 = <ArticleChannelSettingController>
    //     0x80d6a8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d28] TypeArguments: <ArticleChannelSettingController>
    //     0x80d6ac: ldr             x16, [x16, #0xd28]
    // 0x80d6b0: stp             x0, x16, [SP]
    // 0x80d6b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80d6b4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80d6b8: r0 = Inst.lazyPut()
    //     0x80d6b8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80d6bc: r0 = Null
    //     0x80d6bc: mov             x0, NULL
    // 0x80d6c0: LeaveFrame
    //     0x80d6c0: mov             SP, fp
    //     0x80d6c4: ldp             fp, lr, [SP], #0x10
    // 0x80d6c8: ret
    //     0x80d6c8: ret             
    // 0x80d6cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d6cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d6d0: b               #0x80d67c
  }
  [closure] ArticleChannelSettingController <anonymous closure>(dynamic) {
    // ** addr: 0x80d6d4, size: 0x80
    // 0x80d6d4: EnterFrame
    //     0x80d6d4: stp             fp, lr, [SP, #-0x10]!
    //     0x80d6d8: mov             fp, SP
    // 0x80d6dc: AllocStack(0x18)
    //     0x80d6dc: sub             SP, SP, #0x18
    // 0x80d6e0: CheckStackOverflow
    //     0x80d6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d6e4: cmp             SP, x16
    //     0x80d6e8: b.ls            #0x80d74c
    // 0x80d6ec: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d6ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d6f0: ldr             x0, [x0, #0x2670]
    //     0x80d6f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d6f8: cmp             w0, w16
    //     0x80d6fc: b.ne            #0x80d708
    //     0x80d700: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d704: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d708: r16 = <CategoryRepository>
    //     0x80d708: add             x16, PP, #0x10, lsl #12  ; [pp+0x10068] TypeArguments: <CategoryRepository>
    //     0x80d70c: ldr             x16, [x16, #0x68]
    // 0x80d710: r30 = "category_repo"
    //     0x80d710: add             lr, PP, #0x10, lsl #12  ; [pp+0x10070] "category_repo"
    //     0x80d714: ldr             lr, [lr, #0x70]
    // 0x80d718: stp             lr, x16, [SP]
    // 0x80d71c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80d71c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80d720: r0 = Inst.find()
    //     0x80d720: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80d724: stur            x0, [fp, #-8]
    // 0x80d728: r0 = ArticleChannelSettingController()
    //     0x80d728: bl              #0x80d8e4  ; AllocateArticleChannelSettingControllerStub -> ArticleChannelSettingController (size=0x38)
    // 0x80d72c: mov             x1, x0
    // 0x80d730: ldur            x2, [fp, #-8]
    // 0x80d734: stur            x0, [fp, #-8]
    // 0x80d738: r0 = ArticleChannelSettingController()
    //     0x80d738: bl              #0x80d754  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::ArticleChannelSettingController
    // 0x80d73c: ldur            x0, [fp, #-8]
    // 0x80d740: LeaveFrame
    //     0x80d740: mov             SP, fp
    //     0x80d744: ldp             fp, lr, [SP], #0x10
    // 0x80d748: ret
    //     0x80d748: ret             
    // 0x80d74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d74c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d750: b               #0x80d6ec
  }
}
