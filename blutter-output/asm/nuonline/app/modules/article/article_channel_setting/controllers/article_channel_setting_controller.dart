// lib: , url: package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart

// class id: 1050129, size: 0x8
class :: {
}

// class id: 2003, size: 0x24, field offset: 0x20
//   transformed mixin,
abstract class _ArticleChannelSettingController&GetxController&GetSingleTickerProviderStateMixin extends GetxController
     with GetSingleTickerProviderStateMixin {

  _ createTicker(/* No info */) {
    // ** addr: 0x760744, size: 0x60
    // 0x760744: EnterFrame
    //     0x760744: stp             fp, lr, [SP, #-0x10]!
    //     0x760748: mov             fp, SP
    // 0x76074c: AllocStack(0x10)
    //     0x76074c: sub             SP, SP, #0x10
    // 0x760750: SetupParameters(_ArticleChannelSettingController&Getx<PERSON>ontroll<PERSON>&GetSingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x760750: stur            x1, [fp, #-8]
    //     0x760754: stur            x2, [fp, #-0x10]
    // 0x760758: r0 = Ticker()
    //     0x760758: bl              #0x6efe64  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x76075c: mov             x2, x0
    // 0x760760: r1 = false
    //     0x760760: add             x1, NULL, #0x30  ; false
    // 0x760764: StoreField: r2->field_b = r1
    //     0x760764: stur            w1, [x2, #0xb]
    // 0x760768: ldur            x1, [fp, #-0x10]
    // 0x76076c: StoreField: r2->field_13 = r1
    //     0x76076c: stur            w1, [x2, #0x13]
    // 0x760770: mov             x0, x2
    // 0x760774: ldur            x1, [fp, #-8]
    // 0x760778: StoreField: r1->field_1f = r0
    //     0x760778: stur            w0, [x1, #0x1f]
    //     0x76077c: ldurb           w16, [x1, #-1]
    //     0x760780: ldurb           w17, [x0, #-1]
    //     0x760784: and             x16, x17, x16, lsr #2
    //     0x760788: tst             x16, HEAP, lsr #32
    //     0x76078c: b.eq            #0x760794
    //     0x760790: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x760794: mov             x0, x2
    // 0x760798: LeaveFrame
    //     0x760798: mov             SP, fp
    //     0x76079c: ldp             fp, lr, [SP], #0x10
    // 0x7607a0: ret
    //     0x7607a0: ret             
  }
}

// class id: 2037, size: 0x38, field offset: 0x24
class ArticleChannelSettingController extends _ArticleChannelSettingController&GetxController&GetSingleTickerProviderStateMixin {

  late TabController tabController; // offset: 0x28

  _ ArticleChannelSettingController(/* No info */) {
    // ** addr: 0x80d754, size: 0x170
    // 0x80d754: EnterFrame
    //     0x80d754: stp             fp, lr, [SP, #-0x10]!
    //     0x80d758: mov             fp, SP
    // 0x80d75c: AllocStack(0x28)
    //     0x80d75c: sub             SP, SP, #0x28
    // 0x80d760: r0 = Sentinel
    //     0x80d760: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80d764: stur            x1, [fp, #-8]
    // 0x80d768: mov             x16, x2
    // 0x80d76c: mov             x2, x1
    // 0x80d770: mov             x1, x16
    // 0x80d774: stur            x1, [fp, #-0x10]
    // 0x80d778: CheckStackOverflow
    //     0x80d778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d77c: cmp             SP, x16
    //     0x80d780: b.ls            #0x80d8bc
    // 0x80d784: StoreField: r2->field_27 = r0
    //     0x80d784: stur            w0, [x2, #0x27]
    // 0x80d788: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d78c: ldr             x0, [x0, #0x2670]
    //     0x80d790: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d794: cmp             w0, w16
    //     0x80d798: b.ne            #0x80d7a4
    //     0x80d79c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d7a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d7a4: r0 = GetNavigation.arguments()
    //     0x80d7a4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d7a8: r16 = "islamic"
    //     0x80d7a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0x80d7ac: ldr             x16, [x16, #0xca0]
    // 0x80d7b0: stp             x16, x0, [SP]
    // 0x80d7b4: r4 = 0
    //     0x80d7b4: movz            x4, #0
    // 0x80d7b8: ldr             x0, [SP, #8]
    // 0x80d7bc: r16 = UnlinkedCall_0x5f3c08
    //     0x80d7bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36990] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d7c0: add             x16, x16, #0x990
    // 0x80d7c4: ldp             x5, lr, [x16]
    // 0x80d7c8: blr             lr
    // 0x80d7cc: mov             x3, x0
    // 0x80d7d0: r2 = Null
    //     0x80d7d0: mov             x2, NULL
    // 0x80d7d4: r1 = Null
    //     0x80d7d4: mov             x1, NULL
    // 0x80d7d8: stur            x3, [fp, #-0x18]
    // 0x80d7dc: r4 = 60
    //     0x80d7dc: movz            x4, #0x3c
    // 0x80d7e0: branchIfSmi(r0, 0x80d7ec)
    //     0x80d7e0: tbz             w0, #0, #0x80d7ec
    // 0x80d7e4: r4 = LoadClassIdInstr(r0)
    //     0x80d7e4: ldur            x4, [x0, #-1]
    //     0x80d7e8: ubfx            x4, x4, #0xc, #0x14
    // 0x80d7ec: cmp             x4, #0x3f
    // 0x80d7f0: b.eq            #0x80d804
    // 0x80d7f4: r8 = bool
    //     0x80d7f4: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x80d7f8: r3 = Null
    //     0x80d7f8: add             x3, PP, #0x36, lsl #12  ; [pp+0x369a0] Null
    //     0x80d7fc: ldr             x3, [x3, #0x9a0]
    // 0x80d800: r0 = bool()
    //     0x80d800: bl              #0xed4390  ; IsType_bool_Stub
    // 0x80d804: ldur            x3, [fp, #-8]
    // 0x80d808: ldur            x0, [fp, #-0x18]
    // 0x80d80c: StoreField: r3->field_2b = r0
    //     0x80d80c: stur            w0, [x3, #0x2b]
    // 0x80d810: r1 = <Category>
    //     0x80d810: ldr             x1, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80d814: r2 = 0
    //     0x80d814: movz            x2, #0
    // 0x80d818: r0 = _GrowableList()
    //     0x80d818: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80d81c: r16 = <Category>
    //     0x80d81c: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80d820: stp             x0, x16, [SP]
    // 0x80d824: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80d824: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80d828: r0 = ListExtension.obs()
    //     0x80d828: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80d82c: ldur            x3, [fp, #-8]
    // 0x80d830: StoreField: r3->field_2f = r0
    //     0x80d830: stur            w0, [x3, #0x2f]
    //     0x80d834: ldurb           w16, [x3, #-1]
    //     0x80d838: ldurb           w17, [x0, #-1]
    //     0x80d83c: and             x16, x17, x16, lsr #2
    //     0x80d840: tst             x16, HEAP, lsr #32
    //     0x80d844: b.eq            #0x80d84c
    //     0x80d848: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80d84c: r1 = <Category>
    //     0x80d84c: ldr             x1, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80d850: r2 = 0
    //     0x80d850: movz            x2, #0
    // 0x80d854: r0 = _GrowableList()
    //     0x80d854: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80d858: r16 = <Category>
    //     0x80d858: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80d85c: stp             x0, x16, [SP]
    // 0x80d860: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80d860: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80d864: r0 = ListExtension.obs()
    //     0x80d864: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80d868: ldur            x1, [fp, #-8]
    // 0x80d86c: StoreField: r1->field_33 = r0
    //     0x80d86c: stur            w0, [x1, #0x33]
    //     0x80d870: ldurb           w16, [x1, #-1]
    //     0x80d874: ldurb           w17, [x0, #-1]
    //     0x80d878: and             x16, x17, x16, lsr #2
    //     0x80d87c: tst             x16, HEAP, lsr #32
    //     0x80d880: b.eq            #0x80d888
    //     0x80d884: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80d888: ldur            x0, [fp, #-0x10]
    // 0x80d88c: StoreField: r1->field_23 = r0
    //     0x80d88c: stur            w0, [x1, #0x23]
    //     0x80d890: ldurb           w16, [x1, #-1]
    //     0x80d894: ldurb           w17, [x0, #-1]
    //     0x80d898: and             x16, x17, x16, lsr #2
    //     0x80d89c: tst             x16, HEAP, lsr #32
    //     0x80d8a0: b.eq            #0x80d8a8
    //     0x80d8a4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80d8a8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80d8a8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80d8ac: r0 = Null
    //     0x80d8ac: mov             x0, NULL
    // 0x80d8b0: LeaveFrame
    //     0x80d8b0: mov             SP, fp
    //     0x80d8b4: ldp             fp, lr, [SP], #0x10
    // 0x80d8b8: ret
    //     0x80d8b8: ret             
    // 0x80d8bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80d8bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80d8c0: b               #0x80d784
  }
  [closure] bool <anonymous closure>(dynamic, Category) {
    // ** addr: 0x8ab494, size: 0x3c
    // 0x8ab494: ldr             x1, [SP, #8]
    // 0x8ab498: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x8ab498: ldur            w2, [x1, #0x17]
    // 0x8ab49c: DecompressPointer r2
    //     0x8ab49c: add             x2, x2, HEAP, lsl #32
    // 0x8ab4a0: ldr             x1, [SP]
    // 0x8ab4a4: LoadField: r3 = r1->field_1f
    //     0x8ab4a4: ldur            w3, [x1, #0x1f]
    // 0x8ab4a8: DecompressPointer r3
    //     0x8ab4a8: add             x3, x3, HEAP, lsl #32
    // 0x8ab4ac: LoadField: r1 = r2->field_f
    //     0x8ab4ac: ldur            w1, [x2, #0xf]
    // 0x8ab4b0: DecompressPointer r1
    //     0x8ab4b0: add             x1, x1, HEAP, lsl #32
    // 0x8ab4b4: LoadField: r2 = r1->field_2b
    //     0x8ab4b4: ldur            w2, [x1, #0x2b]
    // 0x8ab4b8: DecompressPointer r2
    //     0x8ab4b8: add             x2, x2, HEAP, lsl #32
    // 0x8ab4bc: cmp             w3, w2
    // 0x8ab4c0: r16 = true
    //     0x8ab4c0: add             x16, NULL, #0x20  ; true
    // 0x8ab4c4: r17 = false
    //     0x8ab4c4: add             x17, NULL, #0x30  ; false
    // 0x8ab4c8: csel            x0, x16, x17, eq
    // 0x8ab4cc: ret
    //     0x8ab4cc: ret             
  }
  _ _onSuccess(/* No info */) {
    // ** addr: 0x8ab4d0, size: 0x164
    // 0x8ab4d0: EnterFrame
    //     0x8ab4d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab4d4: mov             fp, SP
    // 0x8ab4d8: AllocStack(0x20)
    //     0x8ab4d8: sub             SP, SP, #0x20
    // 0x8ab4dc: SetupParameters(ArticleChannelSettingController this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x8ab4dc: mov             x0, x1
    //     0x8ab4e0: stur            x1, [fp, #-8]
    //     0x8ab4e4: mov             x1, x2
    //     0x8ab4e8: stur            x2, [fp, #-0x10]
    // 0x8ab4ec: CheckStackOverflow
    //     0x8ab4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab4f0: cmp             SP, x16
    //     0x8ab4f4: b.ls            #0x8ab62c
    // 0x8ab4f8: r1 = 1
    //     0x8ab4f8: movz            x1, #0x1
    // 0x8ab4fc: r0 = AllocateContext()
    //     0x8ab4fc: bl              #0xec126c  ; AllocateContextStub
    // 0x8ab500: mov             x1, x0
    // 0x8ab504: ldur            x0, [fp, #-8]
    // 0x8ab508: StoreField: r1->field_f = r0
    //     0x8ab508: stur            w0, [x1, #0xf]
    // 0x8ab50c: mov             x2, x1
    // 0x8ab510: r1 = Function '<anonymous closure>':.
    //     0x8ab510: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f68] AnonymousClosure: (0x8ab494), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::_onSuccess (0x8ab4d0)
    //     0x8ab514: ldr             x1, [x1, #0xf68]
    // 0x8ab518: r0 = AllocateClosure()
    //     0x8ab518: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab51c: ldur            x1, [fp, #-0x10]
    // 0x8ab520: r2 = LoadClassIdInstr(r1)
    //     0x8ab520: ldur            x2, [x1, #-1]
    //     0x8ab524: ubfx            x2, x2, #0xc, #0x14
    // 0x8ab528: mov             x16, x0
    // 0x8ab52c: mov             x0, x2
    // 0x8ab530: mov             x2, x16
    // 0x8ab534: r0 = GDT[cid_x0 + 0xea28]()
    //     0x8ab534: movz            x17, #0xea28
    //     0x8ab538: add             lr, x0, x17
    //     0x8ab53c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ab540: blr             lr
    // 0x8ab544: mov             x3, x0
    // 0x8ab548: ldur            x0, [fp, #-8]
    // 0x8ab54c: stur            x3, [fp, #-0x18]
    // 0x8ab550: LoadField: r4 = r0->field_2f
    //     0x8ab550: ldur            w4, [x0, #0x2f]
    // 0x8ab554: DecompressPointer r4
    //     0x8ab554: add             x4, x4, HEAP, lsl #32
    // 0x8ab558: stur            x4, [fp, #-0x10]
    // 0x8ab55c: r1 = Function '<anonymous closure>':.
    //     0x8ab55c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f70] AnonymousClosure: (0x8ab750), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::_onSuccess (0x8ab4d0)
    //     0x8ab560: ldr             x1, [x1, #0xf70]
    // 0x8ab564: r2 = Null
    //     0x8ab564: mov             x2, NULL
    // 0x8ab568: r0 = AllocateClosure()
    //     0x8ab568: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab56c: ldur            x1, [fp, #-0x18]
    // 0x8ab570: mov             x2, x0
    // 0x8ab574: r0 = where()
    //     0x8ab574: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x8ab578: LoadField: r1 = r0->field_7
    //     0x8ab578: ldur            w1, [x0, #7]
    // 0x8ab57c: DecompressPointer r1
    //     0x8ab57c: add             x1, x1, HEAP, lsl #32
    // 0x8ab580: mov             x2, x0
    // 0x8ab584: r0 = _GrowableList.of()
    //     0x8ab584: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8ab588: ldur            x1, [fp, #-0x10]
    // 0x8ab58c: mov             x2, x0
    // 0x8ab590: r0 = value=()
    //     0x8ab590: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8ab594: r1 = Function '<anonymous closure>':.
    //     0x8ab594: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f78] AnonymousClosure: (0x8ab634), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels (0x8ab6ac)
    //     0x8ab598: ldr             x1, [x1, #0xf78]
    // 0x8ab59c: r2 = Null
    //     0x8ab59c: mov             x2, NULL
    // 0x8ab5a0: r0 = AllocateClosure()
    //     0x8ab5a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab5a4: str             x0, [SP]
    // 0x8ab5a8: ldur            x1, [fp, #-0x10]
    // 0x8ab5ac: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8ab5ac: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8ab5b0: r0 = sort()
    //     0x8ab5b0: bl              #0x66a8a4  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::sort
    // 0x8ab5b4: ldur            x0, [fp, #-8]
    // 0x8ab5b8: LoadField: r3 = r0->field_33
    //     0x8ab5b8: ldur            w3, [x0, #0x33]
    // 0x8ab5bc: DecompressPointer r3
    //     0x8ab5bc: add             x3, x3, HEAP, lsl #32
    // 0x8ab5c0: stur            x3, [fp, #-0x10]
    // 0x8ab5c4: r1 = Function '<anonymous closure>':.
    //     0x8ab5c4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f80] AnonymousClosure: static (0xc4169c), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onTertiaryFixedVariant (0x636dc4)
    //     0x8ab5c8: ldr             x1, [x1, #0xf80]
    // 0x8ab5cc: r2 = Null
    //     0x8ab5cc: mov             x2, NULL
    // 0x8ab5d0: r0 = AllocateClosure()
    //     0x8ab5d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab5d4: ldur            x1, [fp, #-0x18]
    // 0x8ab5d8: mov             x2, x0
    // 0x8ab5dc: r0 = where()
    //     0x8ab5dc: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x8ab5e0: LoadField: r1 = r0->field_7
    //     0x8ab5e0: ldur            w1, [x0, #7]
    // 0x8ab5e4: DecompressPointer r1
    //     0x8ab5e4: add             x1, x1, HEAP, lsl #32
    // 0x8ab5e8: mov             x2, x0
    // 0x8ab5ec: r0 = _GrowableList.of()
    //     0x8ab5ec: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x8ab5f0: ldur            x1, [fp, #-0x10]
    // 0x8ab5f4: mov             x2, x0
    // 0x8ab5f8: r0 = value=()
    //     0x8ab5f8: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8ab5fc: r1 = Function '<anonymous closure>':.
    //     0x8ab5fc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f88] AnonymousClosure: (0x8ab634), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels (0x8ab6ac)
    //     0x8ab600: ldr             x1, [x1, #0xf88]
    // 0x8ab604: r2 = Null
    //     0x8ab604: mov             x2, NULL
    // 0x8ab608: r0 = AllocateClosure()
    //     0x8ab608: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab60c: str             x0, [SP]
    // 0x8ab610: ldur            x1, [fp, #-0x10]
    // 0x8ab614: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8ab614: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8ab618: r0 = sort()
    //     0x8ab618: bl              #0x66a8a4  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::sort
    // 0x8ab61c: r0 = Null
    //     0x8ab61c: mov             x0, NULL
    // 0x8ab620: LeaveFrame
    //     0x8ab620: mov             SP, fp
    //     0x8ab624: ldp             fp, lr, [SP], #0x10
    // 0x8ab628: ret
    //     0x8ab628: ret             
    // 0x8ab62c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab62c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab630: b               #0x8ab4f8
  }
  [closure] bool <anonymous closure>(dynamic, Category) {
    // ** addr: 0x8ab750, size: 0x14
    // 0x8ab750: ldr             x1, [SP]
    // 0x8ab754: LoadField: r2 = r1->field_23
    //     0x8ab754: ldur            w2, [x1, #0x23]
    // 0x8ab758: DecompressPointer r2
    //     0x8ab758: add             x2, x2, HEAP, lsl #32
    // 0x8ab75c: eor             x0, x2, #0x10
    // 0x8ab760: ret
    //     0x8ab760: ret             
  }
  [closure] void _onSuccess(dynamic, List<Category>, dynamic) {
    // ** addr: 0x8ab764, size: 0x40
    // 0x8ab764: EnterFrame
    //     0x8ab764: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab768: mov             fp, SP
    // 0x8ab76c: ldr             x0, [fp, #0x20]
    // 0x8ab770: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ab770: ldur            w1, [x0, #0x17]
    // 0x8ab774: DecompressPointer r1
    //     0x8ab774: add             x1, x1, HEAP, lsl #32
    // 0x8ab778: CheckStackOverflow
    //     0x8ab778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab77c: cmp             SP, x16
    //     0x8ab780: b.ls            #0x8ab79c
    // 0x8ab784: ldr             x2, [fp, #0x18]
    // 0x8ab788: ldr             x3, [fp, #0x10]
    // 0x8ab78c: r0 = _onSuccess()
    //     0x8ab78c: bl              #0x8ab4d0  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::_onSuccess
    // 0x8ab790: LeaveFrame
    //     0x8ab790: mov             SP, fp
    //     0x8ab794: ldp             fp, lr, [SP], #0x10
    // 0x8ab798: ret
    //     0x8ab798: ret             
    // 0x8ab79c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab79c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab7a0: b               #0x8ab784
  }
  _ onInit(/* No info */) async {
    // ** addr: 0x8ab7a4, size: 0xd0
    // 0x8ab7a4: EnterFrame
    //     0x8ab7a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab7a8: mov             fp, SP
    // 0x8ab7ac: AllocStack(0x38)
    //     0x8ab7ac: sub             SP, SP, #0x38
    // 0x8ab7b0: SetupParameters(ArticleChannelSettingController this /* r1 => r1, fp-0x10 */)
    //     0x8ab7b0: stur            NULL, [fp, #-8]
    //     0x8ab7b4: stur            x1, [fp, #-0x10]
    // 0x8ab7b8: CheckStackOverflow
    //     0x8ab7b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab7bc: cmp             SP, x16
    //     0x8ab7c0: b.ls            #0x8ab86c
    // 0x8ab7c4: r1 = 1
    //     0x8ab7c4: movz            x1, #0x1
    // 0x8ab7c8: r0 = AllocateContext()
    //     0x8ab7c8: bl              #0xec126c  ; AllocateContextStub
    // 0x8ab7cc: mov             x2, x0
    // 0x8ab7d0: ldur            x1, [fp, #-0x10]
    // 0x8ab7d4: stur            x2, [fp, #-0x18]
    // 0x8ab7d8: StoreField: r2->field_f = r1
    //     0x8ab7d8: stur            w1, [x2, #0xf]
    // 0x8ab7dc: InitAsync() -> Future<void?>
    //     0x8ab7dc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ab7e0: bl              #0x661298  ; InitAsyncStub
    // 0x8ab7e4: ldur            x1, [fp, #-0x10]
    // 0x8ab7e8: r0 = onInit()
    //     0x8ab7e8: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8ab7ec: r0 = TabController()
    //     0x8ab7ec: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8ab7f0: mov             x1, x0
    // 0x8ab7f4: ldur            x3, [fp, #-0x10]
    // 0x8ab7f8: r2 = 2
    //     0x8ab7f8: movz            x2, #0x2
    // 0x8ab7fc: stur            x0, [fp, #-0x20]
    // 0x8ab800: r0 = TabController()
    //     0x8ab800: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8ab804: ldur            x0, [fp, #-0x20]
    // 0x8ab808: ldur            x1, [fp, #-0x10]
    // 0x8ab80c: StoreField: r1->field_27 = r0
    //     0x8ab80c: stur            w0, [x1, #0x27]
    //     0x8ab810: ldurb           w16, [x1, #-1]
    //     0x8ab814: ldurb           w17, [x0, #-1]
    //     0x8ab818: and             x16, x17, x16, lsr #2
    //     0x8ab81c: tst             x16, HEAP, lsr #32
    //     0x8ab820: b.eq            #0x8ab828
    //     0x8ab824: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ab828: LoadField: r0 = r1->field_23
    //     0x8ab828: ldur            w0, [x1, #0x23]
    // 0x8ab82c: DecompressPointer r0
    //     0x8ab82c: add             x0, x0, HEAP, lsl #32
    // 0x8ab830: mov             x1, x0
    // 0x8ab834: r0 = findAll()
    //     0x8ab834: bl              #0x8aa274  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::findAll
    // 0x8ab838: ldur            x2, [fp, #-0x18]
    // 0x8ab83c: r1 = Function '<anonymous closure>':.
    //     0x8ab83c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f50] AnonymousClosure: (0x8ab874), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::onInit (0x8ab7a4)
    //     0x8ab840: ldr             x1, [x1, #0xf50]
    // 0x8ab844: stur            x0, [fp, #-0x10]
    // 0x8ab848: r0 = AllocateClosure()
    //     0x8ab848: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab84c: r16 = <void?>
    //     0x8ab84c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8ab850: ldur            lr, [fp, #-0x10]
    // 0x8ab854: stp             lr, x16, [SP, #8]
    // 0x8ab858: str             x0, [SP]
    // 0x8ab85c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ab85c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ab860: r0 = then()
    //     0x8ab860: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8ab864: r0 = Null
    //     0x8ab864: mov             x0, NULL
    // 0x8ab868: r0 = ReturnAsyncNotFuture()
    //     0x8ab868: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ab86c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab86c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab870: b               #0x8ab7c4
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Category>>) {
    // ** addr: 0x8ab874, size: 0xa0
    // 0x8ab874: EnterFrame
    //     0x8ab874: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab878: mov             fp, SP
    // 0x8ab87c: AllocStack(0x28)
    //     0x8ab87c: sub             SP, SP, #0x28
    // 0x8ab880: SetupParameters()
    //     0x8ab880: ldr             x0, [fp, #0x18]
    //     0x8ab884: ldur            w1, [x0, #0x17]
    //     0x8ab888: add             x1, x1, HEAP, lsl #32
    // 0x8ab88c: CheckStackOverflow
    //     0x8ab88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab890: cmp             SP, x16
    //     0x8ab894: b.ls            #0x8ab90c
    // 0x8ab898: LoadField: r0 = r1->field_f
    //     0x8ab898: ldur            w0, [x1, #0xf]
    // 0x8ab89c: DecompressPointer r0
    //     0x8ab89c: add             x0, x0, HEAP, lsl #32
    // 0x8ab8a0: mov             x2, x0
    // 0x8ab8a4: stur            x0, [fp, #-8]
    // 0x8ab8a8: r1 = Function '_onSuccess@1862304526':.
    //     0x8ab8a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f58] AnonymousClosure: (0x8ab764), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::_onSuccess (0x8ab4d0)
    //     0x8ab8ac: ldr             x1, [x1, #0xf58]
    // 0x8ab8b0: r0 = AllocateClosure()
    //     0x8ab8b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab8b4: ldur            x2, [fp, #-8]
    // 0x8ab8b8: r1 = Function '_onError@1862304526':.
    //     0x8ab8b8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f60] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8ab8bc: ldr             x1, [x1, #0xf60]
    // 0x8ab8c0: stur            x0, [fp, #-8]
    // 0x8ab8c4: r0 = AllocateClosure()
    //     0x8ab8c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab8c8: mov             x1, x0
    // 0x8ab8cc: ldr             x0, [fp, #0x10]
    // 0x8ab8d0: r2 = LoadClassIdInstr(r0)
    //     0x8ab8d0: ldur            x2, [x0, #-1]
    //     0x8ab8d4: ubfx            x2, x2, #0xc, #0x14
    // 0x8ab8d8: r16 = <void?>
    //     0x8ab8d8: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8ab8dc: stp             x0, x16, [SP, #0x10]
    // 0x8ab8e0: ldur            x16, [fp, #-8]
    // 0x8ab8e4: stp             x16, x1, [SP]
    // 0x8ab8e8: mov             x0, x2
    // 0x8ab8ec: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8ab8ec: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8ab8f0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8ab8f0: sub             lr, x0, #1, lsl #12
    //     0x8ab8f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8ab8f8: blr             lr
    // 0x8ab8fc: r0 = Null
    //     0x8ab8fc: mov             x0, NULL
    // 0x8ab900: LeaveFrame
    //     0x8ab900: mov             SP, fp
    //     0x8ab904: ldp             fp, lr, [SP], #0x10
    // 0x8ab908: ret
    //     0x8ab908: ret             
    // 0x8ab90c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab90c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab910: b               #0x8ab898
  }
  _ onClose(/* No info */) {
    // ** addr: 0x926770, size: 0x188
    // 0x926770: EnterFrame
    //     0x926770: stp             fp, lr, [SP, #-0x10]!
    //     0x926774: mov             fp, SP
    // 0x926778: AllocStack(0x30)
    //     0x926778: sub             SP, SP, #0x30
    // 0x92677c: SetupParameters(ArticleChannelSettingController this /* r1 => r2, fp-0x8 */)
    //     0x92677c: mov             x2, x1
    //     0x926780: stur            x1, [fp, #-8]
    // 0x926784: CheckStackOverflow
    //     0x926784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926788: cmp             SP, x16
    //     0x92678c: b.ls            #0x9268dc
    // 0x926790: LoadField: r1 = r2->field_27
    //     0x926790: ldur            w1, [x2, #0x27]
    // 0x926794: DecompressPointer r1
    //     0x926794: add             x1, x1, HEAP, lsl #32
    // 0x926798: r16 = Sentinel
    //     0x926798: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x92679c: cmp             w1, w16
    // 0x9267a0: b.eq            #0x9268e4
    // 0x9267a4: r0 = dispose()
    //     0x9267a4: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x9267a8: ldur            x2, [fp, #-8]
    // 0x9267ac: LoadField: r0 = r2->field_33
    //     0x9267ac: ldur            w0, [x2, #0x33]
    // 0x9267b0: DecompressPointer r0
    //     0x9267b0: add             x0, x0, HEAP, lsl #32
    // 0x9267b4: stur            x0, [fp, #-0x18]
    // 0x9267b8: r3 = 0
    //     0x9267b8: movz            x3, #0
    // 0x9267bc: stur            x3, [fp, #-0x10]
    // 0x9267c0: CheckStackOverflow
    //     0x9267c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9267c4: cmp             SP, x16
    //     0x9267c8: b.ls            #0x9268f0
    // 0x9267cc: mov             x1, x0
    // 0x9267d0: r0 = value()
    //     0x9267d0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x9267d4: r1 = LoadClassIdInstr(r0)
    //     0x9267d4: ldur            x1, [x0, #-1]
    //     0x9267d8: ubfx            x1, x1, #0xc, #0x14
    // 0x9267dc: str             x0, [SP]
    // 0x9267e0: mov             x0, x1
    // 0x9267e4: r0 = GDT[cid_x0 + 0xc834]()
    //     0x9267e4: movz            x17, #0xc834
    //     0x9267e8: add             lr, x0, x17
    //     0x9267ec: ldr             lr, [x21, lr, lsl #3]
    //     0x9267f0: blr             lr
    // 0x9267f4: r1 = LoadInt32Instr(r0)
    //     0x9267f4: sbfx            x1, x0, #1, #0x1f
    //     0x9267f8: tbz             w0, #0, #0x926800
    //     0x9267fc: ldur            x1, [x0, #7]
    // 0x926800: ldur            x0, [fp, #-0x10]
    // 0x926804: cmp             x0, x1
    // 0x926808: b.ge            #0x926870
    // 0x92680c: ldur            x1, [fp, #-0x18]
    // 0x926810: r0 = value()
    //     0x926810: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x926814: mov             x3, x0
    // 0x926818: ldur            x2, [fp, #-0x10]
    // 0x92681c: r0 = BoxInt64Instr(r2)
    //     0x92681c: sbfiz           x0, x2, #1, #0x1f
    //     0x926820: cmp             x2, x0, asr #1
    //     0x926824: b.eq            #0x926830
    //     0x926828: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92682c: stur            x2, [x0, #7]
    // 0x926830: r1 = LoadClassIdInstr(r3)
    //     0x926830: ldur            x1, [x3, #-1]
    //     0x926834: ubfx            x1, x1, #0xc, #0x14
    // 0x926838: stp             x0, x3, [SP]
    // 0x92683c: mov             x0, x1
    // 0x926840: r0 = GDT[cid_x0 + 0x13037]()
    //     0x926840: movz            x17, #0x3037
    //     0x926844: movk            x17, #0x1, lsl #16
    //     0x926848: add             lr, x0, x17
    //     0x92684c: ldr             lr, [x21, lr, lsl #3]
    //     0x926850: blr             lr
    // 0x926854: mov             x1, x0
    // 0x926858: ldur            x0, [fp, #-0x10]
    // 0x92685c: StoreField: r1->field_27 = r0
    //     0x92685c: stur            x0, [x1, #0x27]
    // 0x926860: add             x3, x0, #1
    // 0x926864: ldur            x2, [fp, #-8]
    // 0x926868: ldur            x0, [fp, #-0x18]
    // 0x92686c: b               #0x9267bc
    // 0x926870: ldur            x0, [fp, #-8]
    // 0x926874: LoadField: r3 = r0->field_23
    //     0x926874: ldur            w3, [x0, #0x23]
    // 0x926878: DecompressPointer r3
    //     0x926878: add             x3, x3, HEAP, lsl #32
    // 0x92687c: stur            x3, [fp, #-0x20]
    // 0x926880: LoadField: r2 = r0->field_2f
    //     0x926880: ldur            w2, [x0, #0x2f]
    // 0x926884: DecompressPointer r2
    //     0x926884: add             x2, x2, HEAP, lsl #32
    // 0x926888: r1 = <Category>
    //     0x926888: ldr             x1, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x92688c: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0x92688c: bl              #0x60b858  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0x926890: mov             x1, x0
    // 0x926894: ldur            x2, [fp, #-0x18]
    // 0x926898: stur            x0, [fp, #-0x18]
    // 0x92689c: r0 = addAll()
    //     0x92689c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x9268a0: ldur            x1, [fp, #-0x20]
    // 0x9268a4: ldur            x2, [fp, #-0x18]
    // 0x9268a8: r0 = updateAll()
    //     0x9268a8: bl              #0x8aa65c  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::updateAll
    // 0x9268ac: ldur            x2, [fp, #-8]
    // 0x9268b0: r1 = Function 'onClose':.
    //     0x9268b0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40f48] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9268b4: ldr             x1, [x1, #0xf48]
    // 0x9268b8: stur            x0, [fp, #-8]
    // 0x9268bc: r0 = AllocateClosure()
    //     0x9268bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9268c0: ldur            x1, [fp, #-8]
    // 0x9268c4: mov             x2, x0
    // 0x9268c8: r0 = whenComplete()
    //     0x9268c8: bl              #0xd69e44  ; [dart:async] _Future::whenComplete
    // 0x9268cc: r0 = Null
    //     0x9268cc: mov             x0, NULL
    // 0x9268d0: LeaveFrame
    //     0x9268d0: mov             SP, fp
    //     0x9268d4: ldp             fp, lr, [SP], #0x10
    // 0x9268d8: ret
    //     0x9268d8: ret             
    // 0x9268dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9268dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9268e0: b               #0x926790
    // 0x9268e4: r9 = tabController
    //     0x9268e4: add             x9, PP, #0x30, lsl #12  ; [pp+0x30b00] Field <ArticleChannelSettingController.tabController>: late (offset: 0x28)
    //     0x9268e8: ldr             x9, [x9, #0xb00]
    // 0x9268ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9268ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9268f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9268f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9268f4: b               #0x9267cc
  }
  get _ title(/* No info */) {
    // ** addr: 0xad05b0, size: 0x7c
    // 0xad05b0: EnterFrame
    //     0xad05b0: stp             fp, lr, [SP, #-0x10]!
    //     0xad05b4: mov             fp, SP
    // 0xad05b8: AllocStack(0x10)
    //     0xad05b8: sub             SP, SP, #0x10
    // 0xad05bc: SetupParameters(ArticleChannelSettingController this /* r1 => r0, fp-0x8 */)
    //     0xad05bc: mov             x0, x1
    //     0xad05c0: stur            x1, [fp, #-8]
    // 0xad05c4: CheckStackOverflow
    //     0xad05c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad05c8: cmp             SP, x16
    //     0xad05cc: b.ls            #0xad0624
    // 0xad05d0: r1 = Null
    //     0xad05d0: mov             x1, NULL
    // 0xad05d4: r2 = 4
    //     0xad05d4: movz            x2, #0x4
    // 0xad05d8: r0 = AllocateArray()
    //     0xad05d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad05dc: r16 = "Atur Kanal "
    //     0xad05dc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30b70] "Atur Kanal "
    //     0xad05e0: ldr             x16, [x16, #0xb70]
    // 0xad05e4: StoreField: r0->field_f = r16
    //     0xad05e4: stur            w16, [x0, #0xf]
    // 0xad05e8: ldur            x1, [fp, #-8]
    // 0xad05ec: LoadField: r2 = r1->field_2b
    //     0xad05ec: ldur            w2, [x1, #0x2b]
    // 0xad05f0: DecompressPointer r2
    //     0xad05f0: add             x2, x2, HEAP, lsl #32
    // 0xad05f4: tbnz            w2, #4, #0xad0604
    // 0xad05f8: r1 = "Keislaman"
    //     0xad05f8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b78] "Keislaman"
    //     0xad05fc: ldr             x1, [x1, #0xb78]
    // 0xad0600: b               #0xad060c
    // 0xad0604: r1 = "Utama"
    //     0xad0604: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b80] "Utama"
    //     0xad0608: ldr             x1, [x1, #0xb80]
    // 0xad060c: StoreField: r0->field_13 = r1
    //     0xad060c: stur            w1, [x0, #0x13]
    // 0xad0610: str             x0, [SP]
    // 0xad0614: r0 = _interpolate()
    //     0xad0614: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad0618: LeaveFrame
    //     0xad0618: mov             SP, fp
    //     0xad061c: ldp             fp, lr, [SP], #0x10
    // 0xad0620: ret
    //     0xad0620: ret             
    // 0xad0624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0628: b               #0xad05d0
  }
  [closure] void onReorder(dynamic, int, int) {
    // ** addr: 0xad0acc, size: 0x40
    // 0xad0acc: EnterFrame
    //     0xad0acc: stp             fp, lr, [SP, #-0x10]!
    //     0xad0ad0: mov             fp, SP
    // 0xad0ad4: ldr             x0, [fp, #0x20]
    // 0xad0ad8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad0ad8: ldur            w1, [x0, #0x17]
    // 0xad0adc: DecompressPointer r1
    //     0xad0adc: add             x1, x1, HEAP, lsl #32
    // 0xad0ae0: CheckStackOverflow
    //     0xad0ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0ae4: cmp             SP, x16
    //     0xad0ae8: b.ls            #0xad0b04
    // 0xad0aec: ldr             x2, [fp, #0x18]
    // 0xad0af0: ldr             x3, [fp, #0x10]
    // 0xad0af4: r0 = onReorder()
    //     0xad0af4: bl              #0xad0b0c  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::onReorder
    // 0xad0af8: LeaveFrame
    //     0xad0af8: mov             SP, fp
    //     0xad0afc: ldp             fp, lr, [SP], #0x10
    // 0xad0b00: ret
    //     0xad0b00: ret             
    // 0xad0b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0b08: b               #0xad0aec
  }
  _ onReorder(/* No info */) {
    // ** addr: 0xad0b0c, size: 0x8c
    // 0xad0b0c: EnterFrame
    //     0xad0b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xad0b10: mov             fp, SP
    // 0xad0b14: AllocStack(0x10)
    //     0xad0b14: sub             SP, SP, #0x10
    // 0xad0b18: CheckStackOverflow
    //     0xad0b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0b1c: cmp             SP, x16
    //     0xad0b20: b.ls            #0xad0b90
    // 0xad0b24: r0 = LoadInt32Instr(r2)
    //     0xad0b24: sbfx            x0, x2, #1, #0x1f
    //     0xad0b28: tbz             w2, #0, #0xad0b30
    //     0xad0b2c: ldur            x0, [x2, #7]
    // 0xad0b30: r2 = LoadInt32Instr(r3)
    //     0xad0b30: sbfx            x2, x3, #1, #0x1f
    //     0xad0b34: tbz             w3, #0, #0xad0b3c
    //     0xad0b38: ldur            x2, [x3, #7]
    // 0xad0b3c: cmp             x0, x2
    // 0xad0b40: b.ge            #0xad0b4c
    // 0xad0b44: sub             x3, x2, #1
    // 0xad0b48: b               #0xad0b50
    // 0xad0b4c: mov             x3, x2
    // 0xad0b50: stur            x3, [fp, #-0x10]
    // 0xad0b54: LoadField: r4 = r1->field_33
    //     0xad0b54: ldur            w4, [x1, #0x33]
    // 0xad0b58: DecompressPointer r4
    //     0xad0b58: add             x4, x4, HEAP, lsl #32
    // 0xad0b5c: mov             x1, x4
    // 0xad0b60: mov             x2, x0
    // 0xad0b64: stur            x4, [fp, #-8]
    // 0xad0b68: r0 = removeAt()
    //     0xad0b68: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xad0b6c: ldur            x2, [fp, #-0x10]
    // 0xad0b70: StoreField: r0->field_27 = r2
    //     0xad0b70: stur            x2, [x0, #0x27]
    // 0xad0b74: ldur            x1, [fp, #-8]
    // 0xad0b78: mov             x3, x0
    // 0xad0b7c: r0 = insert()
    //     0xad0b7c: bl              #0x66a024  ; [dart:collection] ListBase::insert
    // 0xad0b80: r0 = Null
    //     0xad0b80: mov             x0, NULL
    // 0xad0b84: LeaveFrame
    //     0xad0b84: mov             SP, fp
    //     0xad0b88: ldp             fp, lr, [SP], #0x10
    // 0xad0b8c: ret
    //     0xad0b8c: ret             
    // 0xad0b90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0b90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0b94: b               #0xad0b24
  }
  _ removeChannel(/* No info */) {
    // ** addr: 0xad0ec0, size: 0xac
    // 0xad0ec0: EnterFrame
    //     0xad0ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xad0ec4: mov             fp, SP
    // 0xad0ec8: AllocStack(0x28)
    //     0xad0ec8: sub             SP, SP, #0x28
    // 0xad0ecc: SetupParameters(ArticleChannelSettingController this /* r1 => r0, fp-0x8 */)
    //     0xad0ecc: mov             x0, x1
    //     0xad0ed0: stur            x1, [fp, #-8]
    // 0xad0ed4: CheckStackOverflow
    //     0xad0ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0ed8: cmp             SP, x16
    //     0xad0edc: b.ls            #0xad0f64
    // 0xad0ee0: LoadField: r1 = r0->field_33
    //     0xad0ee0: ldur            w1, [x0, #0x33]
    // 0xad0ee4: DecompressPointer r1
    //     0xad0ee4: add             x1, x1, HEAP, lsl #32
    // 0xad0ee8: r0 = removeAt()
    //     0xad0ee8: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xad0eec: mov             x2, x0
    // 0xad0ef0: r0 = false
    //     0xad0ef0: add             x0, NULL, #0x30  ; false
    // 0xad0ef4: stur            x2, [fp, #-0x18]
    // 0xad0ef8: StoreField: r2->field_23 = r0
    //     0xad0ef8: stur            w0, [x2, #0x23]
    // 0xad0efc: ldur            x0, [fp, #-8]
    // 0xad0f00: LoadField: r3 = r0->field_2f
    //     0xad0f00: ldur            w3, [x0, #0x2f]
    // 0xad0f04: DecompressPointer r3
    //     0xad0f04: add             x3, x3, HEAP, lsl #32
    // 0xad0f08: mov             x1, x3
    // 0xad0f0c: stur            x3, [fp, #-0x10]
    // 0xad0f10: r0 = value()
    //     0xad0f10: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0f14: r1 = LoadClassIdInstr(r0)
    //     0xad0f14: ldur            x1, [x0, #-1]
    //     0xad0f18: ubfx            x1, x1, #0xc, #0x14
    // 0xad0f1c: str             x0, [SP]
    // 0xad0f20: mov             x0, x1
    // 0xad0f24: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad0f24: movz            x17, #0xc834
    //     0xad0f28: add             lr, x0, x17
    //     0xad0f2c: ldr             lr, [x21, lr, lsl #3]
    //     0xad0f30: blr             lr
    // 0xad0f34: r1 = LoadInt32Instr(r0)
    //     0xad0f34: sbfx            x1, x0, #1, #0x1f
    //     0xad0f38: tbz             w0, #0, #0xad0f40
    //     0xad0f3c: ldur            x1, [x0, #7]
    // 0xad0f40: ldur            x0, [fp, #-0x18]
    // 0xad0f44: StoreField: r0->field_27 = r1
    //     0xad0f44: stur            x1, [x0, #0x27]
    // 0xad0f48: ldur            x16, [fp, #-0x10]
    // 0xad0f4c: stp             x0, x16, [SP]
    // 0xad0f50: r0 = add()
    //     0xad0f50: bl              #0x66b5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0xad0f54: r0 = Null
    //     0xad0f54: mov             x0, NULL
    // 0xad0f58: LeaveFrame
    //     0xad0f58: mov             SP, fp
    //     0xad0f5c: ldp             fp, lr, [SP], #0x10
    // 0xad0f60: ret
    //     0xad0f60: ret             
    // 0xad0f64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0f64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0f68: b               #0xad0ee0
  }
  _ addChannel(/* No info */) {
    // ** addr: 0xad119c, size: 0xac
    // 0xad119c: EnterFrame
    //     0xad119c: stp             fp, lr, [SP, #-0x10]!
    //     0xad11a0: mov             fp, SP
    // 0xad11a4: AllocStack(0x28)
    //     0xad11a4: sub             SP, SP, #0x28
    // 0xad11a8: SetupParameters(ArticleChannelSettingController this /* r1 => r0, fp-0x8 */)
    //     0xad11a8: mov             x0, x1
    //     0xad11ac: stur            x1, [fp, #-8]
    // 0xad11b0: CheckStackOverflow
    //     0xad11b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad11b4: cmp             SP, x16
    //     0xad11b8: b.ls            #0xad1240
    // 0xad11bc: LoadField: r1 = r0->field_2f
    //     0xad11bc: ldur            w1, [x0, #0x2f]
    // 0xad11c0: DecompressPointer r1
    //     0xad11c0: add             x1, x1, HEAP, lsl #32
    // 0xad11c4: r0 = removeAt()
    //     0xad11c4: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xad11c8: mov             x2, x0
    // 0xad11cc: r0 = true
    //     0xad11cc: add             x0, NULL, #0x20  ; true
    // 0xad11d0: stur            x2, [fp, #-0x18]
    // 0xad11d4: StoreField: r2->field_23 = r0
    //     0xad11d4: stur            w0, [x2, #0x23]
    // 0xad11d8: ldur            x0, [fp, #-8]
    // 0xad11dc: LoadField: r3 = r0->field_33
    //     0xad11dc: ldur            w3, [x0, #0x33]
    // 0xad11e0: DecompressPointer r3
    //     0xad11e0: add             x3, x3, HEAP, lsl #32
    // 0xad11e4: mov             x1, x3
    // 0xad11e8: stur            x3, [fp, #-0x10]
    // 0xad11ec: r0 = value()
    //     0xad11ec: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad11f0: r1 = LoadClassIdInstr(r0)
    //     0xad11f0: ldur            x1, [x0, #-1]
    //     0xad11f4: ubfx            x1, x1, #0xc, #0x14
    // 0xad11f8: str             x0, [SP]
    // 0xad11fc: mov             x0, x1
    // 0xad1200: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad1200: movz            x17, #0xc834
    //     0xad1204: add             lr, x0, x17
    //     0xad1208: ldr             lr, [x21, lr, lsl #3]
    //     0xad120c: blr             lr
    // 0xad1210: r1 = LoadInt32Instr(r0)
    //     0xad1210: sbfx            x1, x0, #1, #0x1f
    //     0xad1214: tbz             w0, #0, #0xad121c
    //     0xad1218: ldur            x1, [x0, #7]
    // 0xad121c: ldur            x0, [fp, #-0x18]
    // 0xad1220: StoreField: r0->field_27 = r1
    //     0xad1220: stur            x1, [x0, #0x27]
    // 0xad1224: ldur            x16, [fp, #-0x10]
    // 0xad1228: stp             x0, x16, [SP]
    // 0xad122c: r0 = add()
    //     0xad122c: bl              #0x66b5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0xad1230: r0 = Null
    //     0xad1230: mov             x0, NULL
    // 0xad1234: LeaveFrame
    //     0xad1234: mov             SP, fp
    //     0xad1238: ldp             fp, lr, [SP], #0x10
    // 0xad123c: ret
    //     0xad123c: ret             
    // 0xad1240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1244: b               #0xad11bc
  }
}
