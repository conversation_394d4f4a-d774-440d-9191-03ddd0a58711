// lib: , url: package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart

// class id: 1050130, size: 0x8
class :: {
}

// class id: 5309, size: 0x14, field offset: 0x14
class ArticleChannelSettingView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad03fc, size: 0x1a8
    // 0xad03fc: EnterFrame
    //     0xad03fc: stp             fp, lr, [SP, #-0x10]!
    //     0xad0400: mov             fp, SP
    // 0xad0404: AllocStack(0x30)
    //     0xad0404: sub             SP, SP, #0x30
    // 0xad0408: SetupParameters(ArticleChannelSettingView this /* r1 => r1, fp-0x8 */)
    //     0xad0408: stur            x1, [fp, #-8]
    // 0xad040c: CheckStackOverflow
    //     0xad040c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0410: cmp             SP, x16
    //     0xad0414: b.ls            #0xad0590
    // 0xad0418: r1 = 1
    //     0xad0418: movz            x1, #0x1
    // 0xad041c: r0 = AllocateContext()
    //     0xad041c: bl              #0xec126c  ; AllocateContextStub
    // 0xad0420: mov             x2, x0
    // 0xad0424: ldur            x0, [fp, #-8]
    // 0xad0428: stur            x2, [fp, #-0x10]
    // 0xad042c: StoreField: r2->field_f = r0
    //     0xad042c: stur            w0, [x2, #0xf]
    // 0xad0430: mov             x1, x0
    // 0xad0434: r0 = controller()
    //     0xad0434: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0438: mov             x1, x0
    // 0xad043c: r0 = title()
    //     0xad043c: bl              #0xad05b0  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::title
    // 0xad0440: stur            x0, [fp, #-0x18]
    // 0xad0444: r0 = Text()
    //     0xad0444: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad0448: mov             x2, x0
    // 0xad044c: ldur            x0, [fp, #-0x18]
    // 0xad0450: stur            x2, [fp, #-0x20]
    // 0xad0454: StoreField: r2->field_b = r0
    //     0xad0454: stur            w0, [x2, #0xb]
    // 0xad0458: ldur            x1, [fp, #-8]
    // 0xad045c: r0 = controller()
    //     0xad045c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0460: LoadField: r1 = r0->field_27
    //     0xad0460: ldur            w1, [x0, #0x27]
    // 0xad0464: DecompressPointer r1
    //     0xad0464: add             x1, x1, HEAP, lsl #32
    // 0xad0468: r16 = Sentinel
    //     0xad0468: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad046c: cmp             w1, w16
    // 0xad0470: b.eq            #0xad0598
    // 0xad0474: stur            x1, [fp, #-8]
    // 0xad0478: r0 = TabBar()
    //     0xad0478: bl              #0xa42240  ; AllocateTabBarStub -> TabBar (size=0x84)
    // 0xad047c: mov             x1, x0
    // 0xad0480: r0 = const [Instance of 'Tab', Instance of 'Tab']
    //     0xad0480: add             x0, PP, #0x30, lsl #12  ; [pp+0x30af0] List<Widget>(2)
    //     0xad0484: ldr             x0, [x0, #0xaf0]
    // 0xad0488: stur            x1, [fp, #-0x18]
    // 0xad048c: StoreField: r1->field_b = r0
    //     0xad048c: stur            w0, [x1, #0xb]
    // 0xad0490: ldur            x0, [fp, #-8]
    // 0xad0494: StoreField: r1->field_f = r0
    //     0xad0494: stur            w0, [x1, #0xf]
    // 0xad0498: r0 = false
    //     0xad0498: add             x0, NULL, #0x30  ; false
    // 0xad049c: StoreField: r1->field_13 = r0
    //     0xad049c: stur            w0, [x1, #0x13]
    // 0xad04a0: r2 = true
    //     0xad04a0: add             x2, NULL, #0x20  ; true
    // 0xad04a4: StoreField: r1->field_2f = r2
    //     0xad04a4: stur            w2, [x1, #0x2f]
    // 0xad04a8: d0 = 2.000000
    //     0xad04a8: fmov            d0, #2.00000000
    // 0xad04ac: StoreField: r1->field_1f = d0
    //     0xad04ac: stur            d0, [x1, #0x1f]
    // 0xad04b0: r3 = Instance_EdgeInsets
    //     0xad04b0: ldr             x3, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad04b4: StoreField: r1->field_27 = r3
    //     0xad04b4: stur            w3, [x1, #0x27]
    // 0xad04b8: r3 = Instance_DragStartBehavior
    //     0xad04b8: ldr             x3, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad04bc: StoreField: r1->field_57 = r3
    //     0xad04bc: stur            w3, [x1, #0x57]
    // 0xad04c0: StoreField: r1->field_7f = r2
    //     0xad04c0: stur            w2, [x1, #0x7f]
    // 0xad04c4: r0 = NTabBarThemes()
    //     0xad04c4: bl              #0xa42234  ; AllocateNTabBarThemesStub -> NTabBarThemes (size=0x10)
    // 0xad04c8: mov             x1, x0
    // 0xad04cc: ldur            x0, [fp, #-0x18]
    // 0xad04d0: stur            x1, [fp, #-8]
    // 0xad04d4: StoreField: r1->field_b = r0
    //     0xad04d4: stur            w0, [x1, #0xb]
    // 0xad04d8: r0 = PreferredSize()
    //     0xad04d8: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xad04dc: mov             x1, x0
    // 0xad04e0: r0 = Instance_Size
    //     0xad04e0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xad04e4: ldr             x0, [x0, #0xc88]
    // 0xad04e8: stur            x1, [fp, #-0x18]
    // 0xad04ec: StoreField: r1->field_f = r0
    //     0xad04ec: stur            w0, [x1, #0xf]
    // 0xad04f0: ldur            x0, [fp, #-8]
    // 0xad04f4: StoreField: r1->field_b = r0
    //     0xad04f4: stur            w0, [x1, #0xb]
    // 0xad04f8: r0 = AppBar()
    //     0xad04f8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad04fc: stur            x0, [fp, #-8]
    // 0xad0500: ldur            x16, [fp, #-0x20]
    // 0xad0504: ldur            lr, [fp, #-0x18]
    // 0xad0508: stp             lr, x16, [SP]
    // 0xad050c: mov             x1, x0
    // 0xad0510: r4 = const [0, 0x3, 0x2, 0x1, bottom, 0x2, title, 0x1, null]
    //     0xad0510: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2def0] List(9) [0, 0x3, 0x2, 0x1, "bottom", 0x2, "title", 0x1, Null]
    //     0xad0514: ldr             x4, [x4, #0xef0]
    // 0xad0518: r0 = AppBar()
    //     0xad0518: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad051c: r0 = Obx()
    //     0xad051c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad0520: ldur            x2, [fp, #-0x10]
    // 0xad0524: r1 = Function '<anonymous closure>':.
    //     0xad0524: add             x1, PP, #0x30, lsl #12  ; [pp+0x30af8] AnonymousClosure: (0xad062c), in [package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart] ArticleChannelSettingView::build (0xad03fc)
    //     0xad0528: ldr             x1, [x1, #0xaf8]
    // 0xad052c: stur            x0, [fp, #-0x10]
    // 0xad0530: r0 = AllocateClosure()
    //     0xad0530: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0534: mov             x1, x0
    // 0xad0538: ldur            x0, [fp, #-0x10]
    // 0xad053c: StoreField: r0->field_b = r1
    //     0xad053c: stur            w1, [x0, #0xb]
    // 0xad0540: r0 = Scaffold()
    //     0xad0540: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad0544: ldur            x1, [fp, #-8]
    // 0xad0548: StoreField: r0->field_13 = r1
    //     0xad0548: stur            w1, [x0, #0x13]
    // 0xad054c: ldur            x1, [fp, #-0x10]
    // 0xad0550: ArrayStore: r0[0] = r1  ; List_4
    //     0xad0550: stur            w1, [x0, #0x17]
    // 0xad0554: r1 = Instance_AlignmentDirectional
    //     0xad0554: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad0558: ldr             x1, [x1, #0x758]
    // 0xad055c: StoreField: r0->field_2b = r1
    //     0xad055c: stur            w1, [x0, #0x2b]
    // 0xad0560: r1 = true
    //     0xad0560: add             x1, NULL, #0x20  ; true
    // 0xad0564: StoreField: r0->field_53 = r1
    //     0xad0564: stur            w1, [x0, #0x53]
    // 0xad0568: r2 = Instance_DragStartBehavior
    //     0xad0568: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad056c: StoreField: r0->field_57 = r2
    //     0xad056c: stur            w2, [x0, #0x57]
    // 0xad0570: r2 = false
    //     0xad0570: add             x2, NULL, #0x30  ; false
    // 0xad0574: StoreField: r0->field_b = r2
    //     0xad0574: stur            w2, [x0, #0xb]
    // 0xad0578: StoreField: r0->field_f = r2
    //     0xad0578: stur            w2, [x0, #0xf]
    // 0xad057c: StoreField: r0->field_5f = r1
    //     0xad057c: stur            w1, [x0, #0x5f]
    // 0xad0580: StoreField: r0->field_63 = r1
    //     0xad0580: stur            w1, [x0, #0x63]
    // 0xad0584: LeaveFrame
    //     0xad0584: mov             SP, fp
    //     0xad0588: ldp             fp, lr, [SP], #0x10
    // 0xad058c: ret
    //     0xad058c: ret             
    // 0xad0590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0590: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0594: b               #0xad0418
    // 0xad0598: r9 = tabController
    //     0xad0598: add             x9, PP, #0x30, lsl #12  ; [pp+0x30b00] Field <ArticleChannelSettingController.tabController>: late (offset: 0x28)
    //     0xad059c: ldr             x9, [x9, #0xb00]
    // 0xad05a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad05a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] ListTileTheme <anonymous closure>(dynamic) {
    // ** addr: 0xad062c, size: 0x494
    // 0xad062c: EnterFrame
    //     0xad062c: stp             fp, lr, [SP, #-0x10]!
    //     0xad0630: mov             fp, SP
    // 0xad0634: AllocStack(0x48)
    //     0xad0634: sub             SP, SP, #0x48
    // 0xad0638: SetupParameters()
    //     0xad0638: ldr             x0, [fp, #0x10]
    //     0xad063c: ldur            w2, [x0, #0x17]
    //     0xad0640: add             x2, x2, HEAP, lsl #32
    //     0xad0644: stur            x2, [fp, #-8]
    // 0xad0648: CheckStackOverflow
    //     0xad0648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad064c: cmp             SP, x16
    //     0xad0650: b.ls            #0xad0aac
    // 0xad0654: LoadField: r1 = r2->field_f
    //     0xad0654: ldur            w1, [x2, #0xf]
    // 0xad0658: DecompressPointer r1
    //     0xad0658: add             x1, x1, HEAP, lsl #32
    // 0xad065c: r0 = controller()
    //     0xad065c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0660: LoadField: r2 = r0->field_27
    //     0xad0660: ldur            w2, [x0, #0x27]
    // 0xad0664: DecompressPointer r2
    //     0xad0664: add             x2, x2, HEAP, lsl #32
    // 0xad0668: r16 = Sentinel
    //     0xad0668: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad066c: cmp             w2, w16
    // 0xad0670: b.eq            #0xad0ab4
    // 0xad0674: ldur            x0, [fp, #-8]
    // 0xad0678: stur            x2, [fp, #-0x10]
    // 0xad067c: LoadField: r1 = r0->field_f
    //     0xad067c: ldur            w1, [x0, #0xf]
    // 0xad0680: DecompressPointer r1
    //     0xad0680: add             x1, x1, HEAP, lsl #32
    // 0xad0684: r0 = controller()
    //     0xad0684: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0688: LoadField: r1 = r0->field_2f
    //     0xad0688: ldur            w1, [x0, #0x2f]
    // 0xad068c: DecompressPointer r1
    //     0xad068c: add             x1, x1, HEAP, lsl #32
    // 0xad0690: r0 = value()
    //     0xad0690: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0694: r1 = LoadClassIdInstr(r0)
    //     0xad0694: ldur            x1, [x0, #-1]
    //     0xad0698: ubfx            x1, x1, #0xc, #0x14
    // 0xad069c: str             x0, [SP]
    // 0xad06a0: mov             x0, x1
    // 0xad06a4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad06a4: movz            x17, #0xc834
    //     0xad06a8: add             lr, x0, x17
    //     0xad06ac: ldr             lr, [x21, lr, lsl #3]
    //     0xad06b0: blr             lr
    // 0xad06b4: stur            x0, [fp, #-0x18]
    // 0xad06b8: r0 = NEmptyState()
    //     0xad06b8: bl              #0xacfae0  ; AllocateNEmptyStateStub -> NEmptyState (size=0x1c)
    // 0xad06bc: mov             x1, x0
    // 0xad06c0: r2 = "Kanal yang kamu tambahkan akan tampil pada halaman utama artikel. Kamu dapat mengurutkan kanal favorit kamu."
    //     0xad06c0: add             x2, PP, #0x30, lsl #12  ; [pp+0x30b08] "Kanal yang kamu tambahkan akan tampil pada halaman utama artikel. Kamu dapat mengurutkan kanal favorit kamu."
    //     0xad06c4: ldr             x2, [x2, #0xb08]
    // 0xad06c8: r3 = "assets/images/illustration/no_category.svg"
    //     0xad06c8: add             x3, PP, #0x30, lsl #12  ; [pp+0x30b10] "assets/images/illustration/no_category.svg"
    //     0xad06cc: ldr             x3, [x3, #0xb10]
    // 0xad06d0: r5 = "Semua Kanal Telah Ditambahkan"
    //     0xad06d0: add             x5, PP, #0x30, lsl #12  ; [pp+0x30b18] "Semua Kanal Telah Ditambahkan"
    //     0xad06d4: ldr             x5, [x5, #0xb18]
    // 0xad06d8: stur            x0, [fp, #-0x20]
    // 0xad06dc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xad06dc: ldr             x4, [PP, #0xbf0]  ; [pp+0xbf0] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xad06e0: r0 = NEmptyState.svg()
    //     0xad06e0: bl              #0xabaa4c  ; [package:nuikit/src/widgets/empty_state/empty_state.dart] NEmptyState::NEmptyState.svg
    // 0xad06e4: ldur            x2, [fp, #-8]
    // 0xad06e8: LoadField: r1 = r2->field_f
    //     0xad06e8: ldur            w1, [x2, #0xf]
    // 0xad06ec: DecompressPointer r1
    //     0xad06ec: add             x1, x1, HEAP, lsl #32
    // 0xad06f0: r0 = controller()
    //     0xad06f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad06f4: LoadField: r1 = r0->field_2f
    //     0xad06f4: ldur            w1, [x0, #0x2f]
    // 0xad06f8: DecompressPointer r1
    //     0xad06f8: add             x1, x1, HEAP, lsl #32
    // 0xad06fc: r0 = value()
    //     0xad06fc: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0700: r1 = LoadClassIdInstr(r0)
    //     0xad0700: ldur            x1, [x0, #-1]
    //     0xad0704: ubfx            x1, x1, #0xc, #0x14
    // 0xad0708: str             x0, [SP]
    // 0xad070c: mov             x0, x1
    // 0xad0710: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad0710: movz            x17, #0xc834
    //     0xad0714: add             lr, x0, x17
    //     0xad0718: ldr             lr, [x21, lr, lsl #3]
    //     0xad071c: blr             lr
    // 0xad0720: r3 = LoadInt32Instr(r0)
    //     0xad0720: sbfx            x3, x0, #1, #0x1f
    //     0xad0724: tbz             w0, #0, #0xad072c
    //     0xad0728: ldur            x3, [x0, #7]
    // 0xad072c: ldur            x2, [fp, #-8]
    // 0xad0730: stur            x3, [fp, #-0x28]
    // 0xad0734: r1 = Function '<anonymous closure>':.
    //     0xad0734: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b20] AnonymousClosure: (0xad0f6c), in [package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart] ArticleChannelSettingView::build (0xad03fc)
    //     0xad0738: ldr             x1, [x1, #0xb20]
    // 0xad073c: r0 = AllocateClosure()
    //     0xad073c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0740: r1 = Function '<anonymous closure>':.
    //     0xad0740: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b28] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad0744: ldr             x1, [x1, #0xb28]
    // 0xad0748: r2 = Null
    //     0xad0748: mov             x2, NULL
    // 0xad074c: stur            x0, [fp, #-0x30]
    // 0xad0750: r0 = AllocateClosure()
    //     0xad0750: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0754: stur            x0, [fp, #-0x38]
    // 0xad0758: r0 = ListView()
    //     0xad0758: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad075c: stur            x0, [fp, #-0x40]
    // 0xad0760: r16 = Instance_EdgeInsets
    //     0xad0760: ldr             x16, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad0764: str             x16, [SP]
    // 0xad0768: mov             x1, x0
    // 0xad076c: ldur            x2, [fp, #-0x30]
    // 0xad0770: ldur            x3, [fp, #-0x28]
    // 0xad0774: ldur            x5, [fp, #-0x38]
    // 0xad0778: r4 = const [0, 0x5, 0x1, 0x4, padding, 0x4, null]
    //     0xad0778: add             x4, PP, #0x25, lsl #12  ; [pp+0x25700] List(7) [0, 0x5, 0x1, 0x4, "padding", 0x4, Null]
    //     0xad077c: ldr             x4, [x4, #0x700]
    // 0xad0780: r0 = ListView.separated()
    //     0xad0780: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad0784: ldur            x0, [fp, #-0x18]
    // 0xad0788: cbnz            w0, #0xad0794
    // 0xad078c: ldur            x0, [fp, #-0x20]
    // 0xad0790: b               #0xad0798
    // 0xad0794: ldur            x0, [fp, #-0x40]
    // 0xad0798: stur            x0, [fp, #-0x18]
    // 0xad079c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad079c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad07a0: ldr             x0, [x0, #0x2670]
    //     0xad07a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad07a8: cmp             w0, w16
    //     0xad07ac: b.ne            #0xad07b8
    //     0xad07b0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad07b4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad07b8: r0 = GetNavigation.textTheme()
    //     0xad07b8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xad07bc: LoadField: r1 = r0->field_27
    //     0xad07bc: ldur            w1, [x0, #0x27]
    // 0xad07c0: DecompressPointer r1
    //     0xad07c0: add             x1, x1, HEAP, lsl #32
    // 0xad07c4: cmp             w1, NULL
    // 0xad07c8: b.ne            #0xad07d4
    // 0xad07cc: r3 = Null
    //     0xad07cc: mov             x3, NULL
    // 0xad07d0: b               #0xad07f0
    // 0xad07d4: r16 = 14.000000
    //     0xad07d4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xad07d8: ldr             x16, [x16, #0x9a0]
    // 0xad07dc: str             x16, [SP]
    // 0xad07e0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xad07e0: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xad07e4: ldr             x4, [x4, #0x88]
    // 0xad07e8: r0 = copyWith()
    //     0xad07e8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad07ec: mov             x3, x0
    // 0xad07f0: ldur            x2, [fp, #-8]
    // 0xad07f4: ldur            x1, [fp, #-0x10]
    // 0xad07f8: ldur            x0, [fp, #-0x18]
    // 0xad07fc: stur            x3, [fp, #-0x20]
    // 0xad0800: r0 = Text()
    //     0xad0800: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad0804: mov             x1, x0
    // 0xad0808: r0 = "Tahan dan geser atas-bawah untuk mengurutkan"
    //     0xad0808: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b30] "Tahan dan geser atas-bawah untuk mengurutkan"
    //     0xad080c: ldr             x0, [x0, #0xb30]
    // 0xad0810: stur            x1, [fp, #-0x30]
    // 0xad0814: StoreField: r1->field_b = r0
    //     0xad0814: stur            w0, [x1, #0xb]
    // 0xad0818: ldur            x0, [fp, #-0x20]
    // 0xad081c: StoreField: r1->field_13 = r0
    //     0xad081c: stur            w0, [x1, #0x13]
    // 0xad0820: r0 = Padding()
    //     0xad0820: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad0824: mov             x2, x0
    // 0xad0828: r0 = Instance_EdgeInsets
    //     0xad0828: ldr             x0, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad082c: stur            x2, [fp, #-0x20]
    // 0xad0830: StoreField: r2->field_f = r0
    //     0xad0830: stur            w0, [x2, #0xf]
    // 0xad0834: ldur            x0, [fp, #-0x30]
    // 0xad0838: StoreField: r2->field_b = r0
    //     0xad0838: stur            w0, [x2, #0xb]
    // 0xad083c: ldur            x0, [fp, #-8]
    // 0xad0840: LoadField: r1 = r0->field_f
    //     0xad0840: ldur            w1, [x0, #0xf]
    // 0xad0844: DecompressPointer r1
    //     0xad0844: add             x1, x1, HEAP, lsl #32
    // 0xad0848: r0 = controller()
    //     0xad0848: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad084c: LoadField: r1 = r0->field_33
    //     0xad084c: ldur            w1, [x0, #0x33]
    // 0xad0850: DecompressPointer r1
    //     0xad0850: add             x1, x1, HEAP, lsl #32
    // 0xad0854: r0 = value()
    //     0xad0854: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0858: r1 = LoadClassIdInstr(r0)
    //     0xad0858: ldur            x1, [x0, #-1]
    //     0xad085c: ubfx            x1, x1, #0xc, #0x14
    // 0xad0860: str             x0, [SP]
    // 0xad0864: mov             x0, x1
    // 0xad0868: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad0868: movz            x17, #0xc834
    //     0xad086c: add             lr, x0, x17
    //     0xad0870: ldr             lr, [x21, lr, lsl #3]
    //     0xad0874: blr             lr
    // 0xad0878: ldur            x2, [fp, #-8]
    // 0xad087c: stur            x0, [fp, #-0x30]
    // 0xad0880: LoadField: r1 = r2->field_f
    //     0xad0880: ldur            w1, [x2, #0xf]
    // 0xad0884: DecompressPointer r1
    //     0xad0884: add             x1, x1, HEAP, lsl #32
    // 0xad0888: r0 = controller()
    //     0xad0888: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad088c: ldur            x2, [fp, #-8]
    // 0xad0890: r1 = Function '<anonymous closure>':.
    //     0xad0890: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b38] AnonymousClosure: (0xad0b98), in [package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart] ArticleChannelSettingView::build (0xad03fc)
    //     0xad0894: ldr             x1, [x1, #0xb38]
    // 0xad0898: stur            x0, [fp, #-8]
    // 0xad089c: r0 = AllocateClosure()
    //     0xad089c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad08a0: stur            x0, [fp, #-0x38]
    // 0xad08a4: r0 = ReorderableListView()
    //     0xad08a4: bl              #0xad0ac0  ; AllocateReorderableListViewStub -> ReorderableListView (size=0x7c)
    // 0xad08a8: mov             x3, x0
    // 0xad08ac: ldur            x0, [fp, #-0x38]
    // 0xad08b0: stur            x3, [fp, #-0x40]
    // 0xad08b4: StoreField: r3->field_b = r0
    //     0xad08b4: stur            w0, [x3, #0xb]
    // 0xad08b8: ldur            x0, [fp, #-0x30]
    // 0xad08bc: r1 = LoadInt32Instr(r0)
    //     0xad08bc: sbfx            x1, x0, #1, #0x1f
    //     0xad08c0: tbz             w0, #0, #0xad08c8
    //     0xad08c4: ldur            x1, [x0, #7]
    // 0xad08c8: StoreField: r3->field_f = r1
    //     0xad08c8: stur            x1, [x3, #0xf]
    // 0xad08cc: ldur            x2, [fp, #-8]
    // 0xad08d0: r1 = Function 'onReorder':.
    //     0xad08d0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b40] AnonymousClosure: (0xad0acc), in [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::onReorder (0xad0b0c)
    //     0xad08d4: ldr             x1, [x1, #0xb40]
    // 0xad08d8: r0 = AllocateClosure()
    //     0xad08d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad08dc: mov             x1, x0
    // 0xad08e0: ldur            x0, [fp, #-0x40]
    // 0xad08e4: ArrayStore: r0[0] = r1  ; List_4
    //     0xad08e4: stur            w1, [x0, #0x17]
    // 0xad08e8: r1 = true
    //     0xad08e8: add             x1, NULL, #0x20  ; true
    // 0xad08ec: StoreField: r0->field_27 = r1
    //     0xad08ec: stur            w1, [x0, #0x27]
    // 0xad08f0: r1 = Instance_EdgeInsets
    //     0xad08f0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xad08f4: ldr             x1, [x1, #0x360]
    // 0xad08f8: StoreField: r0->field_2b = r1
    //     0xad08f8: stur            w1, [x0, #0x2b]
    // 0xad08fc: r2 = Instance_Axis
    //     0xad08fc: ldr             x2, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad0900: StoreField: r0->field_37 = r2
    //     0xad0900: stur            w2, [x0, #0x37]
    // 0xad0904: r1 = false
    //     0xad0904: add             x1, NULL, #0x30  ; false
    // 0xad0908: StoreField: r0->field_3b = r1
    //     0xad0908: stur            w1, [x0, #0x3b]
    // 0xad090c: StoreField: r0->field_4b = r1
    //     0xad090c: stur            w1, [x0, #0x4b]
    // 0xad0910: StoreField: r0->field_4f = rZR
    //     0xad0910: stur            xzr, [x0, #0x4f]
    // 0xad0914: r3 = Instance_DragStartBehavior
    //     0xad0914: ldr             x3, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad0918: StoreField: r0->field_5b = r3
    //     0xad0918: stur            w3, [x0, #0x5b]
    // 0xad091c: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xad091c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xad0920: ldr             x1, [x1, #0xf00]
    // 0xad0924: StoreField: r0->field_5f = r1
    //     0xad0924: stur            w1, [x0, #0x5f]
    // 0xad0928: r4 = Instance_Clip
    //     0xad0928: add             x4, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xad092c: ldr             x4, [x4, #0x7c0]
    // 0xad0930: StoreField: r0->field_67 = r4
    //     0xad0930: stur            w4, [x0, #0x67]
    // 0xad0934: r1 = <FlexParentData>
    //     0xad0934: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad0938: ldr             x1, [x1, #0x720]
    // 0xad093c: r0 = Expanded()
    //     0xad093c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad0940: mov             x3, x0
    // 0xad0944: r0 = 1
    //     0xad0944: movz            x0, #0x1
    // 0xad0948: stur            x3, [fp, #-8]
    // 0xad094c: StoreField: r3->field_13 = r0
    //     0xad094c: stur            x0, [x3, #0x13]
    // 0xad0950: r0 = Instance_FlexFit
    //     0xad0950: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad0954: ldr             x0, [x0, #0x728]
    // 0xad0958: StoreField: r3->field_1b = r0
    //     0xad0958: stur            w0, [x3, #0x1b]
    // 0xad095c: ldur            x0, [fp, #-0x40]
    // 0xad0960: StoreField: r3->field_b = r0
    //     0xad0960: stur            w0, [x3, #0xb]
    // 0xad0964: r1 = Null
    //     0xad0964: mov             x1, NULL
    // 0xad0968: r2 = 4
    //     0xad0968: movz            x2, #0x4
    // 0xad096c: r0 = AllocateArray()
    //     0xad096c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad0970: mov             x2, x0
    // 0xad0974: ldur            x0, [fp, #-0x20]
    // 0xad0978: stur            x2, [fp, #-0x30]
    // 0xad097c: StoreField: r2->field_f = r0
    //     0xad097c: stur            w0, [x2, #0xf]
    // 0xad0980: ldur            x0, [fp, #-8]
    // 0xad0984: StoreField: r2->field_13 = r0
    //     0xad0984: stur            w0, [x2, #0x13]
    // 0xad0988: r1 = <Widget>
    //     0xad0988: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad098c: r0 = AllocateGrowableArray()
    //     0xad098c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad0990: mov             x1, x0
    // 0xad0994: ldur            x0, [fp, #-0x30]
    // 0xad0998: stur            x1, [fp, #-8]
    // 0xad099c: StoreField: r1->field_f = r0
    //     0xad099c: stur            w0, [x1, #0xf]
    // 0xad09a0: r2 = 4
    //     0xad09a0: movz            x2, #0x4
    // 0xad09a4: StoreField: r1->field_b = r2
    //     0xad09a4: stur            w2, [x1, #0xb]
    // 0xad09a8: r0 = Column()
    //     0xad09a8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad09ac: mov             x3, x0
    // 0xad09b0: r0 = Instance_Axis
    //     0xad09b0: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad09b4: stur            x3, [fp, #-0x20]
    // 0xad09b8: StoreField: r3->field_f = r0
    //     0xad09b8: stur            w0, [x3, #0xf]
    // 0xad09bc: r0 = Instance_MainAxisAlignment
    //     0xad09bc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad09c0: ldr             x0, [x0, #0x730]
    // 0xad09c4: StoreField: r3->field_13 = r0
    //     0xad09c4: stur            w0, [x3, #0x13]
    // 0xad09c8: r0 = Instance_MainAxisSize
    //     0xad09c8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad09cc: ldr             x0, [x0, #0x738]
    // 0xad09d0: ArrayStore: r3[0] = r0  ; List_4
    //     0xad09d0: stur            w0, [x3, #0x17]
    // 0xad09d4: r0 = Instance_CrossAxisAlignment
    //     0xad09d4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xad09d8: ldr             x0, [x0, #0x68]
    // 0xad09dc: StoreField: r3->field_1b = r0
    //     0xad09dc: stur            w0, [x3, #0x1b]
    // 0xad09e0: r0 = Instance_VerticalDirection
    //     0xad09e0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad09e4: ldr             x0, [x0, #0x748]
    // 0xad09e8: StoreField: r3->field_23 = r0
    //     0xad09e8: stur            w0, [x3, #0x23]
    // 0xad09ec: r0 = Instance_Clip
    //     0xad09ec: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad09f0: ldr             x0, [x0, #0x750]
    // 0xad09f4: StoreField: r3->field_2b = r0
    //     0xad09f4: stur            w0, [x3, #0x2b]
    // 0xad09f8: StoreField: r3->field_2f = rZR
    //     0xad09f8: stur            xzr, [x3, #0x2f]
    // 0xad09fc: ldur            x0, [fp, #-8]
    // 0xad0a00: StoreField: r3->field_b = r0
    //     0xad0a00: stur            w0, [x3, #0xb]
    // 0xad0a04: r1 = Null
    //     0xad0a04: mov             x1, NULL
    // 0xad0a08: r2 = 4
    //     0xad0a08: movz            x2, #0x4
    // 0xad0a0c: r0 = AllocateArray()
    //     0xad0a0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad0a10: mov             x2, x0
    // 0xad0a14: ldur            x0, [fp, #-0x18]
    // 0xad0a18: stur            x2, [fp, #-8]
    // 0xad0a1c: StoreField: r2->field_f = r0
    //     0xad0a1c: stur            w0, [x2, #0xf]
    // 0xad0a20: ldur            x0, [fp, #-0x20]
    // 0xad0a24: StoreField: r2->field_13 = r0
    //     0xad0a24: stur            w0, [x2, #0x13]
    // 0xad0a28: r1 = <Widget>
    //     0xad0a28: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad0a2c: r0 = AllocateGrowableArray()
    //     0xad0a2c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad0a30: mov             x1, x0
    // 0xad0a34: ldur            x0, [fp, #-8]
    // 0xad0a38: stur            x1, [fp, #-0x18]
    // 0xad0a3c: StoreField: r1->field_f = r0
    //     0xad0a3c: stur            w0, [x1, #0xf]
    // 0xad0a40: r0 = 4
    //     0xad0a40: movz            x0, #0x4
    // 0xad0a44: StoreField: r1->field_b = r0
    //     0xad0a44: stur            w0, [x1, #0xb]
    // 0xad0a48: r0 = TabBarView()
    //     0xad0a48: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xad0a4c: mov             x1, x0
    // 0xad0a50: ldur            x0, [fp, #-0x18]
    // 0xad0a54: stur            x1, [fp, #-8]
    // 0xad0a58: StoreField: r1->field_f = r0
    //     0xad0a58: stur            w0, [x1, #0xf]
    // 0xad0a5c: ldur            x0, [fp, #-0x10]
    // 0xad0a60: StoreField: r1->field_b = r0
    //     0xad0a60: stur            w0, [x1, #0xb]
    // 0xad0a64: r0 = Instance_DragStartBehavior
    //     0xad0a64: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad0a68: ArrayStore: r1[0] = r0  ; List_4
    //     0xad0a68: stur            w0, [x1, #0x17]
    // 0xad0a6c: d0 = 1.000000
    //     0xad0a6c: fmov            d0, #1.00000000
    // 0xad0a70: StoreField: r1->field_1b = d0
    //     0xad0a70: stur            d0, [x1, #0x1b]
    // 0xad0a74: r0 = Instance_Clip
    //     0xad0a74: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xad0a78: ldr             x0, [x0, #0x7c0]
    // 0xad0a7c: StoreField: r1->field_23 = r0
    //     0xad0a7c: stur            w0, [x1, #0x23]
    // 0xad0a80: r0 = ListTileTheme()
    //     0xad0a80: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xad0a84: r1 = Instance_EdgeInsets
    //     0xad0a84: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad0a88: StoreField: r0->field_2b = r1
    //     0xad0a88: stur            w1, [x0, #0x2b]
    // 0xad0a8c: r1 = 12.000000
    //     0xad0a8c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c60] 12
    //     0xad0a90: ldr             x1, [x1, #0xc60]
    // 0xad0a94: StoreField: r0->field_37 = r1
    //     0xad0a94: stur            w1, [x0, #0x37]
    // 0xad0a98: ldur            x1, [fp, #-8]
    // 0xad0a9c: StoreField: r0->field_b = r1
    //     0xad0a9c: stur            w1, [x0, #0xb]
    // 0xad0aa0: LeaveFrame
    //     0xad0aa0: mov             SP, fp
    //     0xad0aa4: ldp             fp, lr, [SP], #0x10
    // 0xad0aa8: ret
    //     0xad0aa8: ret             
    // 0xad0aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0ab0: b               #0xad0654
    // 0xad0ab4: r9 = tabController
    //     0xad0ab4: add             x9, PP, #0x30, lsl #12  ; [pp+0x30b00] Field <ArticleChannelSettingController.tabController>: late (offset: 0x28)
    //     0xad0ab8: ldr             x9, [x9, #0xb00]
    // 0xad0abc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad0abc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad0b98, size: 0x2b0
    // 0xad0b98: EnterFrame
    //     0xad0b98: stp             fp, lr, [SP, #-0x10]!
    //     0xad0b9c: mov             fp, SP
    // 0xad0ba0: AllocStack(0x40)
    //     0xad0ba0: sub             SP, SP, #0x40
    // 0xad0ba4: SetupParameters()
    //     0xad0ba4: ldr             x0, [fp, #0x20]
    //     0xad0ba8: ldur            w1, [x0, #0x17]
    //     0xad0bac: add             x1, x1, HEAP, lsl #32
    //     0xad0bb0: stur            x1, [fp, #-8]
    // 0xad0bb4: CheckStackOverflow
    //     0xad0bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0bb8: cmp             SP, x16
    //     0xad0bbc: b.ls            #0xad0e40
    // 0xad0bc0: r1 = 1
    //     0xad0bc0: movz            x1, #0x1
    // 0xad0bc4: r0 = AllocateContext()
    //     0xad0bc4: bl              #0xec126c  ; AllocateContextStub
    // 0xad0bc8: mov             x2, x0
    // 0xad0bcc: ldur            x0, [fp, #-8]
    // 0xad0bd0: stur            x2, [fp, #-0x10]
    // 0xad0bd4: StoreField: r2->field_b = r0
    //     0xad0bd4: stur            w0, [x2, #0xb]
    // 0xad0bd8: ldr             x3, [fp, #0x10]
    // 0xad0bdc: StoreField: r2->field_f = r3
    //     0xad0bdc: stur            w3, [x2, #0xf]
    // 0xad0be0: r1 = <int>
    //     0xad0be0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xad0be4: r0 = ValueKey()
    //     0xad0be4: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xad0be8: mov             x2, x0
    // 0xad0bec: ldr             x0, [fp, #0x10]
    // 0xad0bf0: stur            x2, [fp, #-0x18]
    // 0xad0bf4: StoreField: r2->field_b = r0
    //     0xad0bf4: stur            w0, [x2, #0xb]
    // 0xad0bf8: ldur            x0, [fp, #-8]
    // 0xad0bfc: LoadField: r1 = r0->field_f
    //     0xad0bfc: ldur            w1, [x0, #0xf]
    // 0xad0c00: DecompressPointer r1
    //     0xad0c00: add             x1, x1, HEAP, lsl #32
    // 0xad0c04: r0 = controller()
    //     0xad0c04: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0c08: LoadField: r1 = r0->field_33
    //     0xad0c08: ldur            w1, [x0, #0x33]
    // 0xad0c0c: DecompressPointer r1
    //     0xad0c0c: add             x1, x1, HEAP, lsl #32
    // 0xad0c10: ldur            x2, [fp, #-0x10]
    // 0xad0c14: LoadField: r0 = r2->field_f
    //     0xad0c14: ldur            w0, [x2, #0xf]
    // 0xad0c18: DecompressPointer r0
    //     0xad0c18: add             x0, x0, HEAP, lsl #32
    // 0xad0c1c: stur            x0, [fp, #-0x20]
    // 0xad0c20: r0 = value()
    //     0xad0c20: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0c24: r1 = LoadClassIdInstr(r0)
    //     0xad0c24: ldur            x1, [x0, #-1]
    //     0xad0c28: ubfx            x1, x1, #0xc, #0x14
    // 0xad0c2c: ldur            x16, [fp, #-0x20]
    // 0xad0c30: stp             x16, x0, [SP]
    // 0xad0c34: mov             x0, x1
    // 0xad0c38: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad0c38: movz            x17, #0x3037
    //     0xad0c3c: movk            x17, #0x1, lsl #16
    //     0xad0c40: add             lr, x0, x17
    //     0xad0c44: ldr             lr, [x21, lr, lsl #3]
    //     0xad0c48: blr             lr
    // 0xad0c4c: LoadField: r1 = r0->field_1b
    //     0xad0c4c: ldur            w1, [x0, #0x1b]
    // 0xad0c50: DecompressPointer r1
    //     0xad0c50: add             x1, x1, HEAP, lsl #32
    // 0xad0c54: stur            x1, [fp, #-0x20]
    // 0xad0c58: r0 = Text()
    //     0xad0c58: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad0c5c: mov             x2, x0
    // 0xad0c60: ldur            x0, [fp, #-0x20]
    // 0xad0c64: stur            x2, [fp, #-0x28]
    // 0xad0c68: StoreField: r2->field_b = r0
    //     0xad0c68: stur            w0, [x2, #0xb]
    // 0xad0c6c: ldur            x0, [fp, #-8]
    // 0xad0c70: LoadField: r1 = r0->field_f
    //     0xad0c70: ldur            w1, [x0, #0xf]
    // 0xad0c74: DecompressPointer r1
    //     0xad0c74: add             x1, x1, HEAP, lsl #32
    // 0xad0c78: r0 = controller()
    //     0xad0c78: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0c7c: LoadField: r1 = r0->field_33
    //     0xad0c7c: ldur            w1, [x0, #0x33]
    // 0xad0c80: DecompressPointer r1
    //     0xad0c80: add             x1, x1, HEAP, lsl #32
    // 0xad0c84: r0 = value()
    //     0xad0c84: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0c88: r1 = LoadClassIdInstr(r0)
    //     0xad0c88: ldur            x1, [x0, #-1]
    //     0xad0c8c: ubfx            x1, x1, #0xc, #0x14
    // 0xad0c90: str             x0, [SP]
    // 0xad0c94: mov             x0, x1
    // 0xad0c98: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad0c98: movz            x17, #0xc834
    //     0xad0c9c: add             lr, x0, x17
    //     0xad0ca0: ldr             lr, [x21, lr, lsl #3]
    //     0xad0ca4: blr             lr
    // 0xad0ca8: r1 = LoadInt32Instr(r0)
    //     0xad0ca8: sbfx            x1, x0, #1, #0x1f
    //     0xad0cac: tbz             w0, #0, #0xad0cb4
    //     0xad0cb0: ldur            x1, [x0, #7]
    // 0xad0cb4: stur            x1, [fp, #-0x30]
    // 0xad0cb8: r0 = IconButton()
    //     0xad0cb8: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad0cbc: mov             x3, x0
    // 0xad0cc0: r0 = Instance_EdgeInsets
    //     0xad0cc0: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad0cc4: stur            x3, [fp, #-8]
    // 0xad0cc8: StoreField: r3->field_13 = r0
    //     0xad0cc8: stur            w0, [x3, #0x13]
    // 0xad0ccc: ldur            x2, [fp, #-0x10]
    // 0xad0cd0: r1 = Function '<anonymous closure>':.
    //     0xad0cd0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b48] AnonymousClosure: (0xad0e48), in [package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart] ArticleChannelSettingView::build (0xad03fc)
    //     0xad0cd4: ldr             x1, [x1, #0xb48]
    // 0xad0cd8: r0 = AllocateClosure()
    //     0xad0cd8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad0cdc: mov             x1, x0
    // 0xad0ce0: ldur            x0, [fp, #-8]
    // 0xad0ce4: StoreField: r0->field_3b = r1
    //     0xad0ce4: stur            w1, [x0, #0x3b]
    // 0xad0ce8: r1 = false
    //     0xad0ce8: add             x1, NULL, #0x30  ; false
    // 0xad0cec: StoreField: r0->field_47 = r1
    //     0xad0cec: stur            w1, [x0, #0x47]
    // 0xad0cf0: r2 = Instance_Icon
    //     0xad0cf0: add             x2, PP, #0x30, lsl #12  ; [pp+0x30b50] Obj!Icon@e242b1
    //     0xad0cf4: ldr             x2, [x2, #0xb50]
    // 0xad0cf8: StoreField: r0->field_1f = r2
    //     0xad0cf8: stur            w2, [x0, #0x1f]
    // 0xad0cfc: r2 = Instance__IconButtonVariant
    //     0xad0cfc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad0d00: ldr             x2, [x2, #0xf78]
    // 0xad0d04: StoreField: r0->field_63 = r2
    //     0xad0d04: stur            w2, [x0, #0x63]
    // 0xad0d08: ldur            x2, [fp, #-0x30]
    // 0xad0d0c: cmp             x2, #3
    // 0xad0d10: b.gt            #0xad0d1c
    // 0xad0d14: r3 = Null
    //     0xad0d14: mov             x3, NULL
    // 0xad0d18: b               #0xad0d20
    // 0xad0d1c: mov             x3, x0
    // 0xad0d20: ldur            x2, [fp, #-0x18]
    // 0xad0d24: ldur            x0, [fp, #-0x28]
    // 0xad0d28: stur            x3, [fp, #-8]
    // 0xad0d2c: r0 = SizedBox()
    //     0xad0d2c: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad0d30: mov             x1, x0
    // 0xad0d34: r0 = 20.000000
    //     0xad0d34: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xad0d38: ldr             x0, [x0, #0x430]
    // 0xad0d3c: stur            x1, [fp, #-0x10]
    // 0xad0d40: StoreField: r1->field_f = r0
    //     0xad0d40: stur            w0, [x1, #0xf]
    // 0xad0d44: ldur            x0, [fp, #-8]
    // 0xad0d48: StoreField: r1->field_b = r0
    //     0xad0d48: stur            w0, [x1, #0xb]
    // 0xad0d4c: r0 = ListTile()
    //     0xad0d4c: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xad0d50: mov             x3, x0
    // 0xad0d54: r0 = Instance_Icon
    //     0xad0d54: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b58] Obj!Icon@e24271
    //     0xad0d58: ldr             x0, [x0, #0xb58]
    // 0xad0d5c: stur            x3, [fp, #-8]
    // 0xad0d60: StoreField: r3->field_b = r0
    //     0xad0d60: stur            w0, [x3, #0xb]
    // 0xad0d64: ldur            x0, [fp, #-0x28]
    // 0xad0d68: StoreField: r3->field_f = r0
    //     0xad0d68: stur            w0, [x3, #0xf]
    // 0xad0d6c: ldur            x0, [fp, #-0x10]
    // 0xad0d70: ArrayStore: r3[0] = r0  ; List_4
    //     0xad0d70: stur            w0, [x3, #0x17]
    // 0xad0d74: r0 = false
    //     0xad0d74: add             x0, NULL, #0x30  ; false
    // 0xad0d78: StoreField: r3->field_1b = r0
    //     0xad0d78: stur            w0, [x3, #0x1b]
    // 0xad0d7c: r1 = true
    //     0xad0d7c: add             x1, NULL, #0x20  ; true
    // 0xad0d80: StoreField: r3->field_4b = r1
    //     0xad0d80: stur            w1, [x3, #0x4b]
    // 0xad0d84: StoreField: r3->field_5f = r0
    //     0xad0d84: stur            w0, [x3, #0x5f]
    // 0xad0d88: StoreField: r3->field_73 = r0
    //     0xad0d88: stur            w0, [x3, #0x73]
    // 0xad0d8c: StoreField: r3->field_97 = r1
    //     0xad0d8c: stur            w1, [x3, #0x97]
    // 0xad0d90: r1 = Null
    //     0xad0d90: mov             x1, NULL
    // 0xad0d94: r2 = 4
    //     0xad0d94: movz            x2, #0x4
    // 0xad0d98: r0 = AllocateArray()
    //     0xad0d98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad0d9c: mov             x2, x0
    // 0xad0da0: ldur            x0, [fp, #-8]
    // 0xad0da4: stur            x2, [fp, #-0x10]
    // 0xad0da8: StoreField: r2->field_f = r0
    //     0xad0da8: stur            w0, [x2, #0xf]
    // 0xad0dac: r16 = Instance_Divider
    //     0xad0dac: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xad0db0: ldr             x16, [x16, #0xc28]
    // 0xad0db4: StoreField: r2->field_13 = r16
    //     0xad0db4: stur            w16, [x2, #0x13]
    // 0xad0db8: r1 = <Widget>
    //     0xad0db8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad0dbc: r0 = AllocateGrowableArray()
    //     0xad0dbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad0dc0: mov             x1, x0
    // 0xad0dc4: ldur            x0, [fp, #-0x10]
    // 0xad0dc8: stur            x1, [fp, #-8]
    // 0xad0dcc: StoreField: r1->field_f = r0
    //     0xad0dcc: stur            w0, [x1, #0xf]
    // 0xad0dd0: r0 = 4
    //     0xad0dd0: movz            x0, #0x4
    // 0xad0dd4: StoreField: r1->field_b = r0
    //     0xad0dd4: stur            w0, [x1, #0xb]
    // 0xad0dd8: r0 = Column()
    //     0xad0dd8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad0ddc: r1 = Instance_Axis
    //     0xad0ddc: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad0de0: StoreField: r0->field_f = r1
    //     0xad0de0: stur            w1, [x0, #0xf]
    // 0xad0de4: r1 = Instance_MainAxisAlignment
    //     0xad0de4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad0de8: ldr             x1, [x1, #0x730]
    // 0xad0dec: StoreField: r0->field_13 = r1
    //     0xad0dec: stur            w1, [x0, #0x13]
    // 0xad0df0: r1 = Instance_MainAxisSize
    //     0xad0df0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xad0df4: ldr             x1, [x1, #0xe88]
    // 0xad0df8: ArrayStore: r0[0] = r1  ; List_4
    //     0xad0df8: stur            w1, [x0, #0x17]
    // 0xad0dfc: r1 = Instance_CrossAxisAlignment
    //     0xad0dfc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad0e00: ldr             x1, [x1, #0x740]
    // 0xad0e04: StoreField: r0->field_1b = r1
    //     0xad0e04: stur            w1, [x0, #0x1b]
    // 0xad0e08: r1 = Instance_VerticalDirection
    //     0xad0e08: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad0e0c: ldr             x1, [x1, #0x748]
    // 0xad0e10: StoreField: r0->field_23 = r1
    //     0xad0e10: stur            w1, [x0, #0x23]
    // 0xad0e14: r1 = Instance_Clip
    //     0xad0e14: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad0e18: ldr             x1, [x1, #0x750]
    // 0xad0e1c: StoreField: r0->field_2b = r1
    //     0xad0e1c: stur            w1, [x0, #0x2b]
    // 0xad0e20: StoreField: r0->field_2f = rZR
    //     0xad0e20: stur            xzr, [x0, #0x2f]
    // 0xad0e24: ldur            x1, [fp, #-8]
    // 0xad0e28: StoreField: r0->field_b = r1
    //     0xad0e28: stur            w1, [x0, #0xb]
    // 0xad0e2c: ldur            x1, [fp, #-0x18]
    // 0xad0e30: StoreField: r0->field_7 = r1
    //     0xad0e30: stur            w1, [x0, #7]
    // 0xad0e34: LeaveFrame
    //     0xad0e34: mov             SP, fp
    //     0xad0e38: ldp             fp, lr, [SP], #0x10
    // 0xad0e3c: ret
    //     0xad0e3c: ret             
    // 0xad0e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0e40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0e44: b               #0xad0bc0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad0e48, size: 0x78
    // 0xad0e48: EnterFrame
    //     0xad0e48: stp             fp, lr, [SP, #-0x10]!
    //     0xad0e4c: mov             fp, SP
    // 0xad0e50: AllocStack(0x8)
    //     0xad0e50: sub             SP, SP, #8
    // 0xad0e54: SetupParameters()
    //     0xad0e54: ldr             x0, [fp, #0x10]
    //     0xad0e58: ldur            w2, [x0, #0x17]
    //     0xad0e5c: add             x2, x2, HEAP, lsl #32
    //     0xad0e60: stur            x2, [fp, #-8]
    // 0xad0e64: CheckStackOverflow
    //     0xad0e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0e68: cmp             SP, x16
    //     0xad0e6c: b.ls            #0xad0eb8
    // 0xad0e70: LoadField: r0 = r2->field_b
    //     0xad0e70: ldur            w0, [x2, #0xb]
    // 0xad0e74: DecompressPointer r0
    //     0xad0e74: add             x0, x0, HEAP, lsl #32
    // 0xad0e78: LoadField: r1 = r0->field_f
    //     0xad0e78: ldur            w1, [x0, #0xf]
    // 0xad0e7c: DecompressPointer r1
    //     0xad0e7c: add             x1, x1, HEAP, lsl #32
    // 0xad0e80: r0 = controller()
    //     0xad0e80: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0e84: mov             x1, x0
    // 0xad0e88: ldur            x0, [fp, #-8]
    // 0xad0e8c: LoadField: r2 = r0->field_f
    //     0xad0e8c: ldur            w2, [x0, #0xf]
    // 0xad0e90: DecompressPointer r2
    //     0xad0e90: add             x2, x2, HEAP, lsl #32
    // 0xad0e94: r0 = LoadInt32Instr(r2)
    //     0xad0e94: sbfx            x0, x2, #1, #0x1f
    //     0xad0e98: tbz             w2, #0, #0xad0ea0
    //     0xad0e9c: ldur            x0, [x2, #7]
    // 0xad0ea0: mov             x2, x0
    // 0xad0ea4: r0 = removeChannel()
    //     0xad0ea4: bl              #0xad0ec0  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::removeChannel
    // 0xad0ea8: r0 = Null
    //     0xad0ea8: mov             x0, NULL
    // 0xad0eac: LeaveFrame
    //     0xad0eac: mov             SP, fp
    //     0xad0eb0: ldp             fp, lr, [SP], #0x10
    // 0xad0eb4: ret
    //     0xad0eb4: ret             
    // 0xad0eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad0eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad0ebc: b               #0xad0e70
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad0f6c, size: 0x1b8
    // 0xad0f6c: EnterFrame
    //     0xad0f6c: stp             fp, lr, [SP, #-0x10]!
    //     0xad0f70: mov             fp, SP
    // 0xad0f74: AllocStack(0x30)
    //     0xad0f74: sub             SP, SP, #0x30
    // 0xad0f78: SetupParameters()
    //     0xad0f78: ldr             x0, [fp, #0x20]
    //     0xad0f7c: ldur            w1, [x0, #0x17]
    //     0xad0f80: add             x1, x1, HEAP, lsl #32
    //     0xad0f84: stur            x1, [fp, #-8]
    // 0xad0f88: CheckStackOverflow
    //     0xad0f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad0f8c: cmp             SP, x16
    //     0xad0f90: b.ls            #0xad111c
    // 0xad0f94: r1 = 1
    //     0xad0f94: movz            x1, #0x1
    // 0xad0f98: r0 = AllocateContext()
    //     0xad0f98: bl              #0xec126c  ; AllocateContextStub
    // 0xad0f9c: mov             x2, x0
    // 0xad0fa0: ldur            x0, [fp, #-8]
    // 0xad0fa4: stur            x2, [fp, #-0x10]
    // 0xad0fa8: StoreField: r2->field_b = r0
    //     0xad0fa8: stur            w0, [x2, #0xb]
    // 0xad0fac: ldr             x3, [fp, #0x10]
    // 0xad0fb0: StoreField: r2->field_f = r3
    //     0xad0fb0: stur            w3, [x2, #0xf]
    // 0xad0fb4: LoadField: r1 = r0->field_f
    //     0xad0fb4: ldur            w1, [x0, #0xf]
    // 0xad0fb8: DecompressPointer r1
    //     0xad0fb8: add             x1, x1, HEAP, lsl #32
    // 0xad0fbc: r0 = controller()
    //     0xad0fbc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad0fc0: LoadField: r1 = r0->field_2f
    //     0xad0fc0: ldur            w1, [x0, #0x2f]
    // 0xad0fc4: DecompressPointer r1
    //     0xad0fc4: add             x1, x1, HEAP, lsl #32
    // 0xad0fc8: r0 = value()
    //     0xad0fc8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad0fcc: r1 = LoadClassIdInstr(r0)
    //     0xad0fcc: ldur            x1, [x0, #-1]
    //     0xad0fd0: ubfx            x1, x1, #0xc, #0x14
    // 0xad0fd4: ldr             x16, [fp, #0x10]
    // 0xad0fd8: stp             x16, x0, [SP]
    // 0xad0fdc: mov             x0, x1
    // 0xad0fe0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad0fe0: movz            x17, #0x3037
    //     0xad0fe4: movk            x17, #0x1, lsl #16
    //     0xad0fe8: add             lr, x0, x17
    //     0xad0fec: ldr             lr, [x21, lr, lsl #3]
    //     0xad0ff0: blr             lr
    // 0xad0ff4: LoadField: r1 = r0->field_1b
    //     0xad0ff4: ldur            w1, [x0, #0x1b]
    // 0xad0ff8: DecompressPointer r1
    //     0xad0ff8: add             x1, x1, HEAP, lsl #32
    // 0xad0ffc: stur            x1, [fp, #-8]
    // 0xad1000: r0 = Text()
    //     0xad1000: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad1004: mov             x1, x0
    // 0xad1008: ldur            x0, [fp, #-8]
    // 0xad100c: stur            x1, [fp, #-0x18]
    // 0xad1010: StoreField: r1->field_b = r0
    //     0xad1010: stur            w0, [x1, #0xb]
    // 0xad1014: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad1014: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad1018: ldr             x0, [x0, #0x2670]
    //     0xad101c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad1020: cmp             w0, w16
    //     0xad1024: b.ne            #0xad1030
    //     0xad1028: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad102c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad1030: r0 = GetNavigation.theme()
    //     0xad1030: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xad1034: LoadField: r1 = r0->field_3f
    //     0xad1034: ldur            w1, [x0, #0x3f]
    // 0xad1038: DecompressPointer r1
    //     0xad1038: add             x1, x1, HEAP, lsl #32
    // 0xad103c: LoadField: r0 = r1->field_2b
    //     0xad103c: ldur            w0, [x1, #0x2b]
    // 0xad1040: DecompressPointer r0
    //     0xad1040: add             x0, x0, HEAP, lsl #32
    // 0xad1044: stur            x0, [fp, #-8]
    // 0xad1048: r0 = Icon()
    //     0xad1048: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xad104c: mov             x1, x0
    // 0xad1050: r0 = Instance_IconData
    //     0xad1050: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b60] Obj!IconData@e101b1
    //     0xad1054: ldr             x0, [x0, #0xb60]
    // 0xad1058: stur            x1, [fp, #-0x20]
    // 0xad105c: StoreField: r1->field_b = r0
    //     0xad105c: stur            w0, [x1, #0xb]
    // 0xad1060: r0 = 20.000000
    //     0xad1060: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xad1064: ldr             x0, [x0, #0x430]
    // 0xad1068: StoreField: r1->field_f = r0
    //     0xad1068: stur            w0, [x1, #0xf]
    // 0xad106c: ldur            x2, [fp, #-8]
    // 0xad1070: StoreField: r1->field_23 = r2
    //     0xad1070: stur            w2, [x1, #0x23]
    // 0xad1074: r0 = IconButton()
    //     0xad1074: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad1078: mov             x3, x0
    // 0xad107c: r0 = Instance_EdgeInsets
    //     0xad107c: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad1080: stur            x3, [fp, #-8]
    // 0xad1084: StoreField: r3->field_13 = r0
    //     0xad1084: stur            w0, [x3, #0x13]
    // 0xad1088: ldur            x2, [fp, #-0x10]
    // 0xad108c: r1 = Function '<anonymous closure>':.
    //     0xad108c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30b68] AnonymousClosure: (0xad1124), in [package:nuonline/app/modules/article/article_channel_setting/views/article_channel_setting_view.dart] ArticleChannelSettingView::build (0xad03fc)
    //     0xad1090: ldr             x1, [x1, #0xb68]
    // 0xad1094: r0 = AllocateClosure()
    //     0xad1094: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1098: mov             x1, x0
    // 0xad109c: ldur            x0, [fp, #-8]
    // 0xad10a0: StoreField: r0->field_3b = r1
    //     0xad10a0: stur            w1, [x0, #0x3b]
    // 0xad10a4: r1 = false
    //     0xad10a4: add             x1, NULL, #0x30  ; false
    // 0xad10a8: StoreField: r0->field_47 = r1
    //     0xad10a8: stur            w1, [x0, #0x47]
    // 0xad10ac: ldur            x2, [fp, #-0x20]
    // 0xad10b0: StoreField: r0->field_1f = r2
    //     0xad10b0: stur            w2, [x0, #0x1f]
    // 0xad10b4: r2 = Instance__IconButtonVariant
    //     0xad10b4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad10b8: ldr             x2, [x2, #0xf78]
    // 0xad10bc: StoreField: r0->field_63 = r2
    //     0xad10bc: stur            w2, [x0, #0x63]
    // 0xad10c0: r0 = SizedBox()
    //     0xad10c0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad10c4: mov             x1, x0
    // 0xad10c8: r0 = 20.000000
    //     0xad10c8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xad10cc: ldr             x0, [x0, #0x430]
    // 0xad10d0: stur            x1, [fp, #-0x10]
    // 0xad10d4: StoreField: r1->field_f = r0
    //     0xad10d4: stur            w0, [x1, #0xf]
    // 0xad10d8: ldur            x0, [fp, #-8]
    // 0xad10dc: StoreField: r1->field_b = r0
    //     0xad10dc: stur            w0, [x1, #0xb]
    // 0xad10e0: r0 = ListTile()
    //     0xad10e0: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xad10e4: ldur            x1, [fp, #-0x18]
    // 0xad10e8: StoreField: r0->field_f = r1
    //     0xad10e8: stur            w1, [x0, #0xf]
    // 0xad10ec: ldur            x1, [fp, #-0x10]
    // 0xad10f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad10f0: stur            w1, [x0, #0x17]
    // 0xad10f4: r1 = false
    //     0xad10f4: add             x1, NULL, #0x30  ; false
    // 0xad10f8: StoreField: r0->field_1b = r1
    //     0xad10f8: stur            w1, [x0, #0x1b]
    // 0xad10fc: r2 = true
    //     0xad10fc: add             x2, NULL, #0x20  ; true
    // 0xad1100: StoreField: r0->field_4b = r2
    //     0xad1100: stur            w2, [x0, #0x4b]
    // 0xad1104: StoreField: r0->field_5f = r1
    //     0xad1104: stur            w1, [x0, #0x5f]
    // 0xad1108: StoreField: r0->field_73 = r1
    //     0xad1108: stur            w1, [x0, #0x73]
    // 0xad110c: StoreField: r0->field_97 = r2
    //     0xad110c: stur            w2, [x0, #0x97]
    // 0xad1110: LeaveFrame
    //     0xad1110: mov             SP, fp
    //     0xad1114: ldp             fp, lr, [SP], #0x10
    // 0xad1118: ret
    //     0xad1118: ret             
    // 0xad111c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad111c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1120: b               #0xad0f94
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad1124, size: 0x78
    // 0xad1124: EnterFrame
    //     0xad1124: stp             fp, lr, [SP, #-0x10]!
    //     0xad1128: mov             fp, SP
    // 0xad112c: AllocStack(0x8)
    //     0xad112c: sub             SP, SP, #8
    // 0xad1130: SetupParameters()
    //     0xad1130: ldr             x0, [fp, #0x10]
    //     0xad1134: ldur            w2, [x0, #0x17]
    //     0xad1138: add             x2, x2, HEAP, lsl #32
    //     0xad113c: stur            x2, [fp, #-8]
    // 0xad1140: CheckStackOverflow
    //     0xad1140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1144: cmp             SP, x16
    //     0xad1148: b.ls            #0xad1194
    // 0xad114c: LoadField: r0 = r2->field_b
    //     0xad114c: ldur            w0, [x2, #0xb]
    // 0xad1150: DecompressPointer r0
    //     0xad1150: add             x0, x0, HEAP, lsl #32
    // 0xad1154: LoadField: r1 = r0->field_f
    //     0xad1154: ldur            w1, [x0, #0xf]
    // 0xad1158: DecompressPointer r1
    //     0xad1158: add             x1, x1, HEAP, lsl #32
    // 0xad115c: r0 = controller()
    //     0xad115c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad1160: mov             x1, x0
    // 0xad1164: ldur            x0, [fp, #-8]
    // 0xad1168: LoadField: r2 = r0->field_f
    //     0xad1168: ldur            w2, [x0, #0xf]
    // 0xad116c: DecompressPointer r2
    //     0xad116c: add             x2, x2, HEAP, lsl #32
    // 0xad1170: r0 = LoadInt32Instr(r2)
    //     0xad1170: sbfx            x0, x2, #1, #0x1f
    //     0xad1174: tbz             w2, #0, #0xad117c
    //     0xad1178: ldur            x0, [x2, #7]
    // 0xad117c: mov             x2, x0
    // 0xad1180: r0 = addChannel()
    //     0xad1180: bl              #0xad119c  ; [package:nuonline/app/modules/article/article_channel_setting/controllers/article_channel_setting_controller.dart] ArticleChannelSettingController::addChannel
    // 0xad1184: r0 = Null
    //     0xad1184: mov             x0, NULL
    // 0xad1188: LeaveFrame
    //     0xad1188: mov             SP, fp
    //     0xad118c: ldp             fp, lr, [SP], #0x10
    // 0xad1190: ret
    //     0xad1190: ret             
    // 0xad1194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1194: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1198: b               #0xad114c
  }
}
