// lib: , url: package:nuonline/app/modules/article/article_detail/bindings/article_detail_binding.dart

// class id: 1050131, size: 0x8
class :: {
}

// class id: 2194, size: 0x8, field offset: 0x8
class ArticleDetailBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80d8f0, size: 0x11c
    // 0x80d8f0: EnterFrame
    //     0x80d8f0: stp             fp, lr, [SP, #-0x10]!
    //     0x80d8f4: mov             fp, SP
    // 0x80d8f8: AllocStack(0x20)
    //     0x80d8f8: sub             SP, SP, #0x20
    // 0x80d8fc: CheckStackOverflow
    //     0x80d8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80d900: cmp             SP, x16
    //     0x80d904: b.ls            #0x80da04
    // 0x80d908: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80d908: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80d90c: ldr             x0, [x0, #0x2670]
    //     0x80d910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80d914: cmp             w0, w16
    //     0x80d918: b.ne            #0x80d924
    //     0x80d91c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80d920: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80d924: r0 = GetNavigation.arguments()
    //     0x80d924: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80d928: r16 = "id"
    //     0x80d928: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80d92c: ldr             x16, [x16, #0x740]
    // 0x80d930: stp             x16, x0, [SP]
    // 0x80d934: r4 = 0
    //     0x80d934: movz            x4, #0
    // 0x80d938: ldr             x0, [SP, #8]
    // 0x80d93c: r16 = UnlinkedCall_0x5f3c08
    //     0x80d93c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36940] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80d940: add             x16, x16, #0x940
    // 0x80d944: ldp             x5, lr, [x16]
    // 0x80d948: blr             lr
    // 0x80d94c: mov             x3, x0
    // 0x80d950: r2 = Null
    //     0x80d950: mov             x2, NULL
    // 0x80d954: r1 = Null
    //     0x80d954: mov             x1, NULL
    // 0x80d958: stur            x3, [fp, #-8]
    // 0x80d95c: branchIfSmi(r0, 0x80d984)
    //     0x80d95c: tbz             w0, #0, #0x80d984
    // 0x80d960: r4 = LoadClassIdInstr(r0)
    //     0x80d960: ldur            x4, [x0, #-1]
    //     0x80d964: ubfx            x4, x4, #0xc, #0x14
    // 0x80d968: sub             x4, x4, #0x3c
    // 0x80d96c: cmp             x4, #1
    // 0x80d970: b.ls            #0x80d984
    // 0x80d974: r8 = int
    //     0x80d974: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80d978: r3 = Null
    //     0x80d978: add             x3, PP, #0x36, lsl #12  ; [pp+0x36950] Null
    //     0x80d97c: ldr             x3, [x3, #0x950]
    // 0x80d980: r0 = int()
    //     0x80d980: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80d984: ldur            x0, [fp, #-8]
    // 0x80d988: r1 = 60
    //     0x80d988: movz            x1, #0x3c
    // 0x80d98c: branchIfSmi(r0, 0x80d998)
    //     0x80d98c: tbz             w0, #0, #0x80d998
    // 0x80d990: r1 = LoadClassIdInstr(r0)
    //     0x80d990: ldur            x1, [x0, #-1]
    //     0x80d994: ubfx            x1, x1, #0xc, #0x14
    // 0x80d998: str             x0, [SP]
    // 0x80d99c: mov             x0, x1
    // 0x80d9a0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x80d9a0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x80d9a4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x80d9a4: movz            x17, #0x2b03
    //     0x80d9a8: add             lr, x0, x17
    //     0x80d9ac: ldr             lr, [x21, lr, lsl #3]
    //     0x80d9b0: blr             lr
    // 0x80d9b4: r1 = Function '<anonymous closure>':.
    //     0x80d9b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36960] AnonymousClosure: (0x80da18), in [package:nuonline/app/modules/article/article_detail/bindings/article_detail_binding.dart] ArticleDetailBinding::dependencies (0x80d8f0)
    //     0x80d9b8: ldr             x1, [x1, #0x960]
    // 0x80d9bc: r2 = Null
    //     0x80d9bc: mov             x2, NULL
    // 0x80d9c0: stur            x0, [fp, #-8]
    // 0x80d9c4: r0 = AllocateClosure()
    //     0x80d9c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x80d9c8: r16 = <ArticleDetailController>
    //     0x80d9c8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d40] TypeArguments: <ArticleDetailController>
    //     0x80d9cc: ldr             x16, [x16, #0xd40]
    // 0x80d9d0: stp             x0, x16, [SP, #8]
    // 0x80d9d4: ldur            x16, [fp, #-8]
    // 0x80d9d8: str             x16, [SP]
    // 0x80d9dc: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x80d9dc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb630] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x80d9e0: ldr             x4, [x4, #0x630]
    // 0x80d9e4: r0 = Inst.lazyPut()
    //     0x80d9e4: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80d9e8: r0 = ReadingPreferenceBinding()
    //     0x80d9e8: bl              #0x80da0c  ; AllocateReadingPreferenceBindingStub -> ReadingPreferenceBinding (size=0x8)
    // 0x80d9ec: mov             x1, x0
    // 0x80d9f0: r0 = dependencies()
    //     0x80d9f0: bl              #0x8423bc  ; [package:nuonline/app/modules/setting/reading_preference/bindings/reading_preference_binding.dart] ReadingPreferenceBinding::dependencies
    // 0x80d9f4: r0 = Null
    //     0x80d9f4: mov             x0, NULL
    // 0x80d9f8: LeaveFrame
    //     0x80d9f8: mov             SP, fp
    //     0x80d9fc: ldp             fp, lr, [SP], #0x10
    // 0x80da00: ret
    //     0x80da00: ret             
    // 0x80da04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80da04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80da08: b               #0x80d908
  }
  [closure] ArticleDetailController <anonymous closure>(dynamic) {
    // ** addr: 0x80da18, size: 0xc0
    // 0x80da18: EnterFrame
    //     0x80da18: stp             fp, lr, [SP, #-0x10]!
    //     0x80da1c: mov             fp, SP
    // 0x80da20: AllocStack(0x28)
    //     0x80da20: sub             SP, SP, #0x28
    // 0x80da24: CheckStackOverflow
    //     0x80da24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80da28: cmp             SP, x16
    //     0x80da2c: b.ls            #0x80dad0
    // 0x80da30: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80da30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80da34: ldr             x0, [x0, #0x2670]
    //     0x80da38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80da3c: cmp             w0, w16
    //     0x80da40: b.ne            #0x80da4c
    //     0x80da44: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80da48: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80da4c: r16 = <ArticleRepository>
    //     0x80da4c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80da50: ldr             x16, [x16, #0x98]
    // 0x80da54: r30 = "article_repo"
    //     0x80da54: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80da58: ldr             lr, [lr, #0xa0]
    // 0x80da5c: stp             lr, x16, [SP]
    // 0x80da60: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80da60: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80da64: r0 = Inst.find()
    //     0x80da64: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80da68: stur            x0, [fp, #-8]
    // 0x80da6c: r16 = <PdfService>
    //     0x80da6c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdc30] TypeArguments: <PdfService>
    //     0x80da70: ldr             x16, [x16, #0xc30]
    // 0x80da74: r30 = "pdf_service"
    //     0x80da74: add             lr, PP, #0xd, lsl #12  ; [pp+0xdc38] "pdf_service"
    //     0x80da78: ldr             lr, [lr, #0xc38]
    // 0x80da7c: stp             lr, x16, [SP]
    // 0x80da80: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80da80: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80da84: r0 = Inst.find()
    //     0x80da84: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80da88: stur            x0, [fp, #-0x10]
    // 0x80da8c: r16 = <AppStorage>
    //     0x80da8c: ldr             x16, [PP, #0x110]  ; [pp+0x110] TypeArguments: <AppStorage>
    // 0x80da90: r30 = "app_storage"
    //     0x80da90: ldr             lr, [PP, #0x100]  ; [pp+0x100] "app_storage"
    // 0x80da94: stp             lr, x16, [SP]
    // 0x80da98: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80da98: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80da9c: r0 = Inst.find()
    //     0x80da9c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80daa0: stur            x0, [fp, #-0x18]
    // 0x80daa4: r0 = ArticleDetailController()
    //     0x80daa4: bl              #0x80dc48  ; AllocateArticleDetailControllerStub -> ArticleDetailController (size=0x40)
    // 0x80daa8: mov             x1, x0
    // 0x80daac: ldur            x2, [fp, #-8]
    // 0x80dab0: ldur            x3, [fp, #-0x10]
    // 0x80dab4: ldur            x5, [fp, #-0x18]
    // 0x80dab8: stur            x0, [fp, #-8]
    // 0x80dabc: r0 = ArticleDetailController()
    //     0x80dabc: bl              #0x80dad8  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::ArticleDetailController
    // 0x80dac0: ldur            x0, [fp, #-8]
    // 0x80dac4: LeaveFrame
    //     0x80dac4: mov             SP, fp
    //     0x80dac8: ldp             fp, lr, [SP], #0x10
    // 0x80dacc: ret
    //     0x80dacc: ret             
    // 0x80dad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80dad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80dad4: b               #0x80da30
  }
}
