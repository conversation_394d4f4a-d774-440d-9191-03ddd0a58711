// lib: , url: package:nuonline/app/modules/article/article_detail/controllers/article_detail_builder_controller.dart

// class id: 1050132, size: 0x8
class :: {
}

// class id: 2002, size: 0x34, field offset: 0x28
class ArticleDetailBuilderController extends _ArticleDetailController&GetxController&StateMixin {

  const int dyn:get:id(ArticleDetailBuilderController) {
    // ** addr: 0x72a968, size: 0x48
    // 0x72a968: ldr             x2, [SP]
    // 0x72a96c: LoadField: r3 = r2->field_27
    //     0x72a96c: ldur            x3, [x2, #0x27]
    // 0x72a970: r0 = BoxInt64Instr(r3)
    //     0x72a970: sbfiz           x0, x3, #1, #0x1f
    //     0x72a974: cmp             x3, x0, asr #1
    //     0x72a978: b.eq            #0x72a994
    //     0x72a97c: stp             fp, lr, [SP, #-0x10]!
    //     0x72a980: mov             fp, SP
    //     0x72a984: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x72a988: mov             SP, fp
    //     0x72a98c: ldp             fp, lr, [SP], #0x10
    //     0x72a990: stur            x3, [x0, #7]
    // 0x72a994: ret
    //     0x72a994: ret             
  }
  _ onReady(/* No info */) {
    // ** addr: 0x91de10, size: 0x30
    // 0x91de10: EnterFrame
    //     0x91de10: stp             fp, lr, [SP, #-0x10]!
    //     0x91de14: mov             fp, SP
    // 0x91de18: CheckStackOverflow
    //     0x91de18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91de1c: cmp             SP, x16
    //     0x91de20: b.ls            #0x91de38
    // 0x91de24: r0 = _load()
    //     0x91de24: bl              #0x91de40  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_builder_controller.dart] ArticleDetailBuilderController::_load
    // 0x91de28: r0 = Null
    //     0x91de28: mov             x0, NULL
    // 0x91de2c: LeaveFrame
    //     0x91de2c: mov             SP, fp
    //     0x91de30: ldp             fp, lr, [SP], #0x10
    // 0x91de34: ret
    //     0x91de34: ret             
    // 0x91de38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91de38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91de3c: b               #0x91de24
  }
  _ _load(/* No info */) {
    // ** addr: 0x91de40, size: 0xdc
    // 0x91de40: EnterFrame
    //     0x91de40: stp             fp, lr, [SP, #-0x10]!
    //     0x91de44: mov             fp, SP
    // 0x91de48: AllocStack(0x40)
    //     0x91de48: sub             SP, SP, #0x40
    // 0x91de4c: SetupParameters(ArticleDetailBuilderController this /* r1 => r1, fp-0x8 */)
    //     0x91de4c: stur            x1, [fp, #-8]
    // 0x91de50: CheckStackOverflow
    //     0x91de50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91de54: cmp             SP, x16
    //     0x91de58: b.ls            #0x91df14
    // 0x91de5c: r1 = 1
    //     0x91de5c: movz            x1, #0x1
    // 0x91de60: r0 = AllocateContext()
    //     0x91de60: bl              #0xec126c  ; AllocateContextStub
    // 0x91de64: mov             x3, x0
    // 0x91de68: ldur            x0, [fp, #-8]
    // 0x91de6c: stur            x3, [fp, #-0x20]
    // 0x91de70: StoreField: r3->field_f = r0
    //     0x91de70: stur            w0, [x3, #0xf]
    // 0x91de74: LoadField: r4 = r0->field_2f
    //     0x91de74: ldur            w4, [x0, #0x2f]
    // 0x91de78: DecompressPointer r4
    //     0x91de78: add             x4, x4, HEAP, lsl #32
    // 0x91de7c: stur            x4, [fp, #-0x18]
    // 0x91de80: LoadField: r5 = r0->field_27
    //     0x91de80: ldur            x5, [x0, #0x27]
    // 0x91de84: mov             x1, x4
    // 0x91de88: mov             x2, x5
    // 0x91de8c: stur            x5, [fp, #-0x10]
    // 0x91de90: r0 = findByIdFromBookmark()
    //     0x91de90: bl              #0x8ecf00  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findByIdFromBookmark
    // 0x91de94: stur            x0, [fp, #-0x28]
    // 0x91de98: cmp             w0, NULL
    // 0x91de9c: b.eq            #0x91decc
    // 0x91dea0: r0 = RxStatus()
    //     0x91dea0: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x91dea4: mov             x1, x0
    // 0x91dea8: r0 = false
    //     0x91dea8: add             x0, NULL, #0x30  ; false
    // 0x91deac: StoreField: r1->field_f = r0
    //     0x91deac: stur            w0, [x1, #0xf]
    // 0x91deb0: StoreField: r1->field_7 = r0
    //     0x91deb0: stur            w0, [x1, #7]
    // 0x91deb4: StoreField: r1->field_b = r0
    //     0x91deb4: stur            w0, [x1, #0xb]
    // 0x91deb8: mov             x3, x1
    // 0x91debc: ldur            x1, [fp, #-8]
    // 0x91dec0: ldur            x2, [fp, #-0x28]
    // 0x91dec4: r0 = change()
    //     0x91dec4: bl              #0x8ece58  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin::change
    // 0x91dec8: b               #0x91df04
    // 0x91decc: ldur            x1, [fp, #-0x18]
    // 0x91ded0: ldur            x2, [fp, #-0x10]
    // 0x91ded4: r0 = findById()
    //     0x91ded4: bl              #0x8eba1c  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findById
    // 0x91ded8: ldur            x2, [fp, #-0x20]
    // 0x91dedc: r1 = Function '<anonymous closure>':.
    //     0x91dedc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49e90] AnonymousClosure: (0x91df1c), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_builder_controller.dart] ArticleDetailBuilderController::_load (0x91de40)
    //     0x91dee0: ldr             x1, [x1, #0xe90]
    // 0x91dee4: stur            x0, [fp, #-8]
    // 0x91dee8: r0 = AllocateClosure()
    //     0x91dee8: bl              #0xec1630  ; AllocateClosureStub
    // 0x91deec: r16 = <void?>
    //     0x91deec: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x91def0: ldur            lr, [fp, #-8]
    // 0x91def4: stp             lr, x16, [SP, #8]
    // 0x91def8: str             x0, [SP]
    // 0x91defc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91defc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91df00: r0 = then()
    //     0x91df00: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x91df04: r0 = Null
    //     0x91df04: mov             x0, NULL
    // 0x91df08: LeaveFrame
    //     0x91df08: mov             SP, fp
    //     0x91df0c: ldp             fp, lr, [SP], #0x10
    // 0x91df10: ret
    //     0x91df10: ret             
    // 0x91df14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91df14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91df18: b               #0x91de5c
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<ArticleDetail>) {
    // ** addr: 0x91df1c, size: 0xa0
    // 0x91df1c: EnterFrame
    //     0x91df1c: stp             fp, lr, [SP, #-0x10]!
    //     0x91df20: mov             fp, SP
    // 0x91df24: AllocStack(0x28)
    //     0x91df24: sub             SP, SP, #0x28
    // 0x91df28: SetupParameters()
    //     0x91df28: ldr             x0, [fp, #0x18]
    //     0x91df2c: ldur            w1, [x0, #0x17]
    //     0x91df30: add             x1, x1, HEAP, lsl #32
    // 0x91df34: CheckStackOverflow
    //     0x91df34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91df38: cmp             SP, x16
    //     0x91df3c: b.ls            #0x91dfb4
    // 0x91df40: LoadField: r0 = r1->field_f
    //     0x91df40: ldur            w0, [x1, #0xf]
    // 0x91df44: DecompressPointer r0
    //     0x91df44: add             x0, x0, HEAP, lsl #32
    // 0x91df48: mov             x2, x0
    // 0x91df4c: stur            x0, [fp, #-8]
    // 0x91df50: r1 = Function '_onSuccess@1870176909':.
    //     0x91df50: add             x1, PP, #0x49, lsl #12  ; [pp+0x49e98] AnonymousClosure: (0x91dff8), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onSuccess (0x8ed0f0)
    //     0x91df54: ldr             x1, [x1, #0xe98]
    // 0x91df58: r0 = AllocateClosure()
    //     0x91df58: bl              #0xec1630  ; AllocateClosureStub
    // 0x91df5c: ldur            x2, [fp, #-8]
    // 0x91df60: r1 = Function '_onError@1870176909':.
    //     0x91df60: add             x1, PP, #0x49, lsl #12  ; [pp+0x49ea0] AnonymousClosure: (0x91dfbc), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onError (0x8ed068)
    //     0x91df64: ldr             x1, [x1, #0xea0]
    // 0x91df68: stur            x0, [fp, #-8]
    // 0x91df6c: r0 = AllocateClosure()
    //     0x91df6c: bl              #0xec1630  ; AllocateClosureStub
    // 0x91df70: mov             x1, x0
    // 0x91df74: ldr             x0, [fp, #0x10]
    // 0x91df78: r2 = LoadClassIdInstr(r0)
    //     0x91df78: ldur            x2, [x0, #-1]
    //     0x91df7c: ubfx            x2, x2, #0xc, #0x14
    // 0x91df80: r16 = <void?>
    //     0x91df80: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x91df84: stp             x0, x16, [SP, #0x10]
    // 0x91df88: ldur            x16, [fp, #-8]
    // 0x91df8c: stp             x16, x1, [SP]
    // 0x91df90: mov             x0, x2
    // 0x91df94: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x91df94: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x91df98: r0 = GDT[cid_x0 + -0x1000]()
    //     0x91df98: sub             lr, x0, #1, lsl #12
    //     0x91df9c: ldr             lr, [x21, lr, lsl #3]
    //     0x91dfa0: blr             lr
    // 0x91dfa4: r0 = Null
    //     0x91dfa4: mov             x0, NULL
    // 0x91dfa8: LeaveFrame
    //     0x91dfa8: mov             SP, fp
    //     0x91dfac: ldp             fp, lr, [SP], #0x10
    // 0x91dfb0: ret
    //     0x91dfb0: ret             
    // 0x91dfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91dfb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91dfb8: b               #0x91df40
  }
  [closure] void _onError(dynamic, NetworkExceptions) {
    // ** addr: 0x91dfbc, size: 0x3c
    // 0x91dfbc: EnterFrame
    //     0x91dfbc: stp             fp, lr, [SP, #-0x10]!
    //     0x91dfc0: mov             fp, SP
    // 0x91dfc4: ldr             x0, [fp, #0x18]
    // 0x91dfc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91dfc8: ldur            w1, [x0, #0x17]
    // 0x91dfcc: DecompressPointer r1
    //     0x91dfcc: add             x1, x1, HEAP, lsl #32
    // 0x91dfd0: CheckStackOverflow
    //     0x91dfd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91dfd4: cmp             SP, x16
    //     0x91dfd8: b.ls            #0x91dff0
    // 0x91dfdc: ldr             x2, [fp, #0x10]
    // 0x91dfe0: r0 = _onError()
    //     0x91dfe0: bl              #0x8ed068  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onError
    // 0x91dfe4: LeaveFrame
    //     0x91dfe4: mov             SP, fp
    //     0x91dfe8: ldp             fp, lr, [SP], #0x10
    // 0x91dfec: ret
    //     0x91dfec: ret             
    // 0x91dff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91dff0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91dff4: b               #0x91dfdc
  }
  [closure] void _onSuccess(dynamic, ArticleDetail, dynamic) {
    // ** addr: 0x91dff8, size: 0x40
    // 0x91dff8: EnterFrame
    //     0x91dff8: stp             fp, lr, [SP, #-0x10]!
    //     0x91dffc: mov             fp, SP
    // 0x91e000: ldr             x0, [fp, #0x20]
    // 0x91e004: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91e004: ldur            w1, [x0, #0x17]
    // 0x91e008: DecompressPointer r1
    //     0x91e008: add             x1, x1, HEAP, lsl #32
    // 0x91e00c: CheckStackOverflow
    //     0x91e00c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e010: cmp             SP, x16
    //     0x91e014: b.ls            #0x91e030
    // 0x91e018: ldr             x2, [fp, #0x18]
    // 0x91e01c: ldr             x3, [fp, #0x10]
    // 0x91e020: r0 = _onSuccess()
    //     0x91e020: bl              #0x8ed0f0  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onSuccess
    // 0x91e024: LeaveFrame
    //     0x91e024: mov             SP, fp
    //     0x91e028: ldp             fp, lr, [SP], #0x10
    // 0x91e02c: ret
    //     0x91e02c: ret             
    // 0x91e030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e030: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e034: b               #0x91e018
  }
  _ bookmark(/* No info */) {
    // ** addr: 0xa429a4, size: 0x8c
    // 0xa429a4: EnterFrame
    //     0xa429a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa429a8: mov             fp, SP
    // 0xa429ac: AllocStack(0x10)
    //     0xa429ac: sub             SP, SP, #0x10
    // 0xa429b0: SetupParameters(ArticleDetailBuilderController this /* r1 => r0, fp-0x8 */)
    //     0xa429b0: mov             x0, x1
    //     0xa429b4: stur            x1, [fp, #-8]
    // 0xa429b8: CheckStackOverflow
    //     0xa429b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa429bc: cmp             SP, x16
    //     0xa429c0: b.ls            #0xa42a24
    // 0xa429c4: mov             x1, x0
    // 0xa429c8: r0 = notifyChildrens()
    //     0xa429c8: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xa429cc: ldur            x1, [fp, #-8]
    // 0xa429d0: LoadField: r0 = r1->field_1f
    //     0xa429d0: ldur            w0, [x1, #0x1f]
    // 0xa429d4: DecompressPointer r0
    //     0xa429d4: add             x0, x0, HEAP, lsl #32
    // 0xa429d8: cmp             w0, NULL
    // 0xa429dc: b.ne            #0xa429f0
    // 0xa429e0: r0 = Null
    //     0xa429e0: mov             x0, NULL
    // 0xa429e4: LeaveFrame
    //     0xa429e4: mov             SP, fp
    //     0xa429e8: ldp             fp, lr, [SP], #0x10
    // 0xa429ec: ret
    //     0xa429ec: ret             
    // 0xa429f0: LoadField: r0 = r1->field_2f
    //     0xa429f0: ldur            w0, [x1, #0x2f]
    // 0xa429f4: DecompressPointer r0
    //     0xa429f4: add             x0, x0, HEAP, lsl #32
    // 0xa429f8: stur            x0, [fp, #-0x10]
    // 0xa429fc: r0 = value()
    //     0xa429fc: bl              #0x72cf78  ; [package:nuonline/app/modules/zakat/controllers/zakat_controller.dart] _ZakatController&GetxController&StateMixin::value
    // 0xa42a00: cmp             w0, NULL
    // 0xa42a04: b.eq            #0xa42a2c
    // 0xa42a08: ldur            x1, [fp, #-0x10]
    // 0xa42a0c: mov             x2, x0
    // 0xa42a10: r0 = saveToBookmark()
    //     0xa42a10: bl              #0xa42a30  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::saveToBookmark
    // 0xa42a14: r0 = Null
    //     0xa42a14: mov             x0, NULL
    // 0xa42a18: LeaveFrame
    //     0xa42a18: mov             SP, fp
    //     0xa42a1c: ldp             fp, lr, [SP], #0x10
    // 0xa42a20: ret
    //     0xa42a20: ret             
    // 0xa42a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42a24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42a28: b               #0xa429c4
    // 0xa42a2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa42a2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ unbookmark(/* No info */) {
    // ** addr: 0xa42b44, size: 0x40
    // 0xa42b44: EnterFrame
    //     0xa42b44: stp             fp, lr, [SP, #-0x10]!
    //     0xa42b48: mov             fp, SP
    // 0xa42b4c: CheckStackOverflow
    //     0xa42b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa42b50: cmp             SP, x16
    //     0xa42b54: b.ls            #0xa42b7c
    // 0xa42b58: LoadField: r0 = r1->field_2f
    //     0xa42b58: ldur            w0, [x1, #0x2f]
    // 0xa42b5c: DecompressPointer r0
    //     0xa42b5c: add             x0, x0, HEAP, lsl #32
    // 0xa42b60: LoadField: r2 = r1->field_27
    //     0xa42b60: ldur            x2, [x1, #0x27]
    // 0xa42b64: mov             x1, x0
    // 0xa42b68: r0 = deleteFromBookmark()
    //     0xa42b68: bl              #0xa42b84  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::deleteFromBookmark
    // 0xa42b6c: r0 = Null
    //     0xa42b6c: mov             x0, NULL
    // 0xa42b70: LeaveFrame
    //     0xa42b70: mov             SP, fp
    //     0xa42b74: ldp             fp, lr, [SP], #0x10
    // 0xa42b78: ret
    //     0xa42b78: ret             
    // 0xa42b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa42b7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa42b80: b               #0xa42b58
  }
}
