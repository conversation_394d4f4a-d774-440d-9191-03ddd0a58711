// lib: , url: package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart

// class id: 1050133, size: 0x8
class :: {
}

// class id: 1999, size: 0x28, field offset: 0x20
//   transformed mixin,
abstract class _ArticleDetailController&GetxController&StateMixin extends GetxController
     with StateMixin<X0> {

  _ change(/* No info */) {
    // ** addr: 0x8ece58, size: 0xa8
    // 0x8ece58: EnterFrame
    //     0x8ece58: stp             fp, lr, [SP, #-0x10]!
    //     0x8ece5c: mov             fp, SP
    // 0x8ece60: AllocStack(0x20)
    //     0x8ece60: sub             SP, SP, #0x20
    // 0x8ece64: SetupParameters(_ArticleDetailController&GetxController&StateMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x8ece64: stur            x1, [fp, #-8]
    //     0x8ece68: mov             x16, x2
    //     0x8ece6c: mov             x2, x1
    //     0x8ece70: mov             x1, x16
    //     0x8ece74: mov             x0, x3
    //     0x8ece78: stur            x1, [fp, #-0x10]
    // 0x8ece7c: CheckStackOverflow
    //     0x8ece7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ece80: cmp             SP, x16
    //     0x8ece84: b.ls            #0x8ecef8
    // 0x8ece88: StoreField: r2->field_23 = r0
    //     0x8ece88: stur            w0, [x2, #0x23]
    //     0x8ece8c: ldurb           w16, [x2, #-1]
    //     0x8ece90: ldurb           w17, [x0, #-1]
    //     0x8ece94: and             x16, x17, x16, lsr #2
    //     0x8ece98: tst             x16, HEAP, lsr #32
    //     0x8ece9c: b.eq            #0x8ecea4
    //     0x8ecea0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8ecea4: LoadField: r0 = r2->field_1f
    //     0x8ecea4: ldur            w0, [x2, #0x1f]
    // 0x8ecea8: DecompressPointer r0
    //     0x8ecea8: add             x0, x0, HEAP, lsl #32
    // 0x8eceac: stp             x0, x1, [SP]
    // 0x8eceb0: r0 = ==()
    //     0x8eceb0: bl              #0xd3f134  ; [package:equatable/src/equatable.dart] Equatable::==
    // 0x8eceb4: tbz             w0, #4, #0x8ecee0
    // 0x8eceb8: ldur            x1, [fp, #-8]
    // 0x8ecebc: ldur            x0, [fp, #-0x10]
    // 0x8ecec0: StoreField: r1->field_1f = r0
    //     0x8ecec0: stur            w0, [x1, #0x1f]
    //     0x8ecec4: ldurb           w16, [x1, #-1]
    //     0x8ecec8: ldurb           w17, [x0, #-1]
    //     0x8ececc: and             x16, x17, x16, lsr #2
    //     0x8eced0: tst             x16, HEAP, lsr #32
    //     0x8eced4: b.eq            #0x8ecedc
    //     0x8eced8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ecedc: b               #0x8ecee4
    // 0x8ecee0: ldur            x1, [fp, #-8]
    // 0x8ecee4: r0 = _notifyUpdate()
    //     0x8ecee4: bl              #0x72a79c  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_notifyUpdate
    // 0x8ecee8: r0 = Null
    //     0x8ecee8: mov             x0, NULL
    // 0x8eceec: LeaveFrame
    //     0x8eceec: mov             SP, fp
    //     0x8ecef0: ldp             fp, lr, [SP], #0x10
    // 0x8ecef4: ret
    //     0x8ecef4: ret             
    // 0x8ecef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecef8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecefc: b               #0x8ece88
  }
}

// class id: 2000, size: 0x28, field offset: 0x28
//   transformed mixin,
abstract class _ArticleDetailController&GetxController&StateMixin&WakelockMixin extends _ArticleDetailController&GetxController&StateMixin
     with WakelockMixin {

  _ onInit(/* No info */) {
    // ** addr: 0x8ed150, size: 0xb4
    // 0x8ed150: EnterFrame
    //     0x8ed150: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed154: mov             fp, SP
    // 0x8ed158: AllocStack(0x10)
    //     0x8ed158: sub             SP, SP, #0x10
    // 0x8ed15c: SetupParameters(_ArticleDetailController&GetxController&StateMixin&WakelockMixin this /* r1 => r1, fp-0x8 */)
    //     0x8ed15c: stur            x1, [fp, #-8]
    // 0x8ed160: CheckStackOverflow
    //     0x8ed160: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed164: cmp             SP, x16
    //     0x8ed168: b.ls            #0x8ed1fc
    // 0x8ed16c: r1 = 1
    //     0x8ed16c: movz            x1, #0x1
    // 0x8ed170: r0 = AllocateContext()
    //     0x8ed170: bl              #0xec126c  ; AllocateContextStub
    // 0x8ed174: mov             x2, x0
    // 0x8ed178: ldur            x0, [fp, #-8]
    // 0x8ed17c: stur            x2, [fp, #-0x10]
    // 0x8ed180: StoreField: r2->field_f = r0
    //     0x8ed180: stur            w0, [x2, #0xf]
    // 0x8ed184: mov             x1, x0
    // 0x8ed188: r0 = onInit()
    //     0x8ed188: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8ed18c: ldur            x0, [fp, #-8]
    // 0x8ed190: LoadField: r1 = r0->field_2f
    //     0x8ed190: ldur            w1, [x0, #0x2f]
    // 0x8ed194: DecompressPointer r1
    //     0x8ed194: add             x1, x1, HEAP, lsl #32
    // 0x8ed198: r0 = _readingPref()
    //     0x8ed198: bl              #0x8add5c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_readingPref
    // 0x8ed19c: mov             x1, x0
    // 0x8ed1a0: r0 = val()
    //     0x8ed1a0: bl              #0x7ec97c  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val
    // 0x8ed1a4: mov             x2, x0
    // 0x8ed1a8: r1 = Null
    //     0x8ed1a8: mov             x1, NULL
    // 0x8ed1ac: r0 = ReadingPref.fromMap()
    //     0x8ed1ac: bl              #0x8adb1c  ; [package:nuonline/app/data/models/reading_pref.dart] ReadingPref::ReadingPref.fromMap
    // 0x8ed1b0: LoadField: r1 = r0->field_1f
    //     0x8ed1b0: ldur            w1, [x0, #0x1f]
    // 0x8ed1b4: DecompressPointer r1
    //     0x8ed1b4: add             x1, x1, HEAP, lsl #32
    // 0x8ed1b8: r0 = toggle()
    //     0x8ed1b8: bl              #0x8ad644  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::toggle
    // 0x8ed1bc: r1 = Null
    //     0x8ed1bc: mov             x1, NULL
    // 0x8ed1c0: r0 = GetStorage()
    //     0x8ed1c0: bl              #0x7ecca0  ; [package:get_storage/src/storage_impl.dart] GetStorage::GetStorage
    // 0x8ed1c4: ldur            x2, [fp, #-0x10]
    // 0x8ed1c8: r1 = Function '<anonymous closure>':.
    //     0x8ed1c8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40cd8] AnonymousClosure: (0x8ed204), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin&WakelockMixin::onInit (0x8ed150)
    //     0x8ed1cc: ldr             x1, [x1, #0xcd8]
    // 0x8ed1d0: stur            x0, [fp, #-8]
    // 0x8ed1d4: r0 = AllocateClosure()
    //     0x8ed1d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ed1d8: ldur            x1, [fp, #-8]
    // 0x8ed1dc: mov             x3, x0
    // 0x8ed1e0: r2 = "readingPref"
    //     0x8ed1e0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c400] "readingPref"
    //     0x8ed1e4: ldr             x2, [x2, #0x400]
    // 0x8ed1e8: r0 = listenKey()
    //     0x8ed1e8: bl              #0x8ad200  ; [package:get_storage/src/storage_impl.dart] GetStorage::listenKey
    // 0x8ed1ec: r0 = Null
    //     0x8ed1ec: mov             x0, NULL
    // 0x8ed1f0: LeaveFrame
    //     0x8ed1f0: mov             SP, fp
    //     0x8ed1f4: ldp             fp, lr, [SP], #0x10
    // 0x8ed1f8: ret
    //     0x8ed1f8: ret             
    // 0x8ed1fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed1fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed200: b               #0x8ed16c
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8ed204, size: 0x6c
    // 0x8ed204: EnterFrame
    //     0x8ed204: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed208: mov             fp, SP
    // 0x8ed20c: ldr             x0, [fp, #0x18]
    // 0x8ed210: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ed210: ldur            w1, [x0, #0x17]
    // 0x8ed214: DecompressPointer r1
    //     0x8ed214: add             x1, x1, HEAP, lsl #32
    // 0x8ed218: CheckStackOverflow
    //     0x8ed218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed21c: cmp             SP, x16
    //     0x8ed220: b.ls            #0x8ed268
    // 0x8ed224: LoadField: r0 = r1->field_f
    //     0x8ed224: ldur            w0, [x1, #0xf]
    // 0x8ed228: DecompressPointer r0
    //     0x8ed228: add             x0, x0, HEAP, lsl #32
    // 0x8ed22c: LoadField: r1 = r0->field_2f
    //     0x8ed22c: ldur            w1, [x0, #0x2f]
    // 0x8ed230: DecompressPointer r1
    //     0x8ed230: add             x1, x1, HEAP, lsl #32
    // 0x8ed234: r0 = _readingPref()
    //     0x8ed234: bl              #0x8add5c  ; [package:nuonline/services/storage_service/app_storage.dart] AppStorage::_readingPref
    // 0x8ed238: mov             x1, x0
    // 0x8ed23c: r0 = val()
    //     0x8ed23c: bl              #0x7ec97c  ; [package:get_storage/src/read_write_value.dart] ReadWriteValue::val
    // 0x8ed240: mov             x2, x0
    // 0x8ed244: r1 = Null
    //     0x8ed244: mov             x1, NULL
    // 0x8ed248: r0 = ReadingPref.fromMap()
    //     0x8ed248: bl              #0x8adb1c  ; [package:nuonline/app/data/models/reading_pref.dart] ReadingPref::ReadingPref.fromMap
    // 0x8ed24c: LoadField: r1 = r0->field_1f
    //     0x8ed24c: ldur            w1, [x0, #0x1f]
    // 0x8ed250: DecompressPointer r1
    //     0x8ed250: add             x1, x1, HEAP, lsl #32
    // 0x8ed254: r0 = toggle()
    //     0x8ed254: bl              #0x8ad644  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::toggle
    // 0x8ed258: r0 = Null
    //     0x8ed258: mov             x0, NULL
    // 0x8ed25c: LeaveFrame
    //     0x8ed25c: mov             SP, fp
    //     0x8ed260: ldp             fp, lr, [SP], #0x10
    // 0x8ed264: ret
    //     0x8ed264: ret             
    // 0x8ed268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed26c: b               #0x8ed224
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9270c0, size: 0x30
    // 0x9270c0: EnterFrame
    //     0x9270c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9270c4: mov             fp, SP
    // 0x9270c8: CheckStackOverflow
    //     0x9270c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9270cc: cmp             SP, x16
    //     0x9270d0: b.ls            #0x9270e8
    // 0x9270d4: r0 = disable()
    //     0x9270d4: bl              #0x9270f0  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::disable
    // 0x9270d8: r0 = Null
    //     0x9270d8: mov             x0, NULL
    // 0x9270dc: LeaveFrame
    //     0x9270dc: mov             SP, fp
    //     0x9270e0: ldp             fp, lr, [SP], #0x10
    // 0x9270e4: ret
    //     0x9270e4: ret             
    // 0x9270e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9270e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9270ec: b               #0x9270d4
  }
}

// class id: 2001, size: 0x40, field offset: 0x28
class ArticleDetailController extends _ArticleDetailController&GetxController&StateMixin&WakelockMixin {

  const int dyn:get:id(ArticleDetailController) {
    // ** addr: 0x6fb4ec, size: 0x48
    // 0x6fb4ec: ldr             x2, [SP]
    // 0x6fb4f0: LoadField: r3 = r2->field_33
    //     0x6fb4f0: ldur            x3, [x2, #0x33]
    // 0x6fb4f4: r0 = BoxInt64Instr(r3)
    //     0x6fb4f4: sbfiz           x0, x3, #1, #0x1f
    //     0x6fb4f8: cmp             x3, x0, asr #1
    //     0x6fb4fc: b.eq            #0x6fb518
    //     0x6fb500: stp             fp, lr, [SP, #-0x10]!
    //     0x6fb504: mov             fp, SP
    //     0x6fb508: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6fb50c: mov             SP, fp
    //     0x6fb510: ldp             fp, lr, [SP], #0x10
    //     0x6fb514: stur            x3, [x0, #7]
    // 0x6fb518: ret
    //     0x6fb518: ret             
  }
  _ ArticleDetailController(/* No info */) {
    // ** addr: 0x80dad8, size: 0x170
    // 0x80dad8: EnterFrame
    //     0x80dad8: stp             fp, lr, [SP, #-0x10]!
    //     0x80dadc: mov             fp, SP
    // 0x80dae0: AllocStack(0x38)
    //     0x80dae0: sub             SP, SP, #0x38
    // 0x80dae4: SetupParameters(ArticleDetailController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x80dae4: stur            x1, [fp, #-8]
    //     0x80dae8: mov             x16, x3
    //     0x80daec: mov             x3, x1
    //     0x80daf0: mov             x1, x16
    //     0x80daf4: mov             x0, x5
    //     0x80daf8: stur            x2, [fp, #-0x10]
    //     0x80dafc: stur            x1, [fp, #-0x18]
    //     0x80db00: stur            x5, [fp, #-0x20]
    // 0x80db04: CheckStackOverflow
    //     0x80db04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80db08: cmp             SP, x16
    //     0x80db0c: b.ls            #0x80dc40
    // 0x80db10: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80db10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80db14: ldr             x0, [x0, #0x2670]
    //     0x80db18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80db1c: cmp             w0, w16
    //     0x80db20: b.ne            #0x80db2c
    //     0x80db24: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80db28: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80db2c: r0 = GetNavigation.arguments()
    //     0x80db2c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80db30: r16 = "id"
    //     0x80db30: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80db34: ldr             x16, [x16, #0x740]
    // 0x80db38: stp             x16, x0, [SP]
    // 0x80db3c: r4 = 0
    //     0x80db3c: movz            x4, #0
    // 0x80db40: ldr             x0, [SP, #8]
    // 0x80db44: r16 = UnlinkedCall_0x5f3c08
    //     0x80db44: add             x16, PP, #0x36, lsl #12  ; [pp+0x36968] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80db48: add             x16, x16, #0x968
    // 0x80db4c: ldp             x5, lr, [x16]
    // 0x80db50: blr             lr
    // 0x80db54: mov             x3, x0
    // 0x80db58: r2 = Null
    //     0x80db58: mov             x2, NULL
    // 0x80db5c: r1 = Null
    //     0x80db5c: mov             x1, NULL
    // 0x80db60: stur            x3, [fp, #-0x28]
    // 0x80db64: branchIfSmi(r0, 0x80db8c)
    //     0x80db64: tbz             w0, #0, #0x80db8c
    // 0x80db68: r4 = LoadClassIdInstr(r0)
    //     0x80db68: ldur            x4, [x0, #-1]
    //     0x80db6c: ubfx            x4, x4, #0xc, #0x14
    // 0x80db70: sub             x4, x4, #0x3c
    // 0x80db74: cmp             x4, #1
    // 0x80db78: b.ls            #0x80db8c
    // 0x80db7c: r8 = int
    //     0x80db7c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80db80: r3 = Null
    //     0x80db80: add             x3, PP, #0x36, lsl #12  ; [pp+0x36978] Null
    //     0x80db84: ldr             x3, [x3, #0x978]
    // 0x80db88: r0 = int()
    //     0x80db88: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80db8c: ldur            x0, [fp, #-0x28]
    // 0x80db90: r1 = LoadInt32Instr(r0)
    //     0x80db90: sbfx            x1, x0, #1, #0x1f
    //     0x80db94: tbz             w0, #0, #0x80db9c
    //     0x80db98: ldur            x1, [x0, #7]
    // 0x80db9c: ldur            x0, [fp, #-8]
    // 0x80dba0: StoreField: r0->field_33 = r1
    //     0x80dba0: stur            x1, [x0, #0x33]
    // 0x80dba4: r1 = false
    //     0x80dba4: add             x1, NULL, #0x30  ; false
    // 0x80dba8: r0 = BoolExtension.obs()
    //     0x80dba8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80dbac: ldur            x1, [fp, #-8]
    // 0x80dbb0: StoreField: r1->field_3b = r0
    //     0x80dbb0: stur            w0, [x1, #0x3b]
    //     0x80dbb4: ldurb           w16, [x1, #-1]
    //     0x80dbb8: ldurb           w17, [x0, #-1]
    //     0x80dbbc: and             x16, x17, x16, lsr #2
    //     0x80dbc0: tst             x16, HEAP, lsr #32
    //     0x80dbc4: b.eq            #0x80dbcc
    //     0x80dbc8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80dbcc: ldur            x0, [fp, #-0x10]
    // 0x80dbd0: StoreField: r1->field_27 = r0
    //     0x80dbd0: stur            w0, [x1, #0x27]
    //     0x80dbd4: ldurb           w16, [x1, #-1]
    //     0x80dbd8: ldurb           w17, [x0, #-1]
    //     0x80dbdc: and             x16, x17, x16, lsr #2
    //     0x80dbe0: tst             x16, HEAP, lsr #32
    //     0x80dbe4: b.eq            #0x80dbec
    //     0x80dbe8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80dbec: ldur            x0, [fp, #-0x18]
    // 0x80dbf0: StoreField: r1->field_2b = r0
    //     0x80dbf0: stur            w0, [x1, #0x2b]
    //     0x80dbf4: ldurb           w16, [x1, #-1]
    //     0x80dbf8: ldurb           w17, [x0, #-1]
    //     0x80dbfc: and             x16, x17, x16, lsr #2
    //     0x80dc00: tst             x16, HEAP, lsr #32
    //     0x80dc04: b.eq            #0x80dc0c
    //     0x80dc08: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80dc0c: ldur            x0, [fp, #-0x20]
    // 0x80dc10: StoreField: r1->field_2f = r0
    //     0x80dc10: stur            w0, [x1, #0x2f]
    //     0x80dc14: ldurb           w16, [x1, #-1]
    //     0x80dc18: ldurb           w17, [x0, #-1]
    //     0x80dc1c: and             x16, x17, x16, lsr #2
    //     0x80dc20: tst             x16, HEAP, lsr #32
    //     0x80dc24: b.eq            #0x80dc2c
    //     0x80dc28: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80dc2c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80dc2c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80dc30: r0 = Null
    //     0x80dc30: mov             x0, NULL
    // 0x80dc34: LeaveFrame
    //     0x80dc34: mov             SP, fp
    //     0x80dc38: ldp             fp, lr, [SP], #0x10
    // 0x80dc3c: ret
    //     0x80dc3c: ret             
    // 0x80dc40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80dc40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80dc44: b               #0x80db10
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8eb7c4, size: 0x48
    // 0x8eb7c4: EnterFrame
    //     0x8eb7c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8eb7c8: mov             fp, SP
    // 0x8eb7cc: AllocStack(0x8)
    //     0x8eb7cc: sub             SP, SP, #8
    // 0x8eb7d0: SetupParameters(ArticleDetailController this /* r1 => r0, fp-0x8 */)
    //     0x8eb7d0: mov             x0, x1
    //     0x8eb7d4: stur            x1, [fp, #-8]
    // 0x8eb7d8: CheckStackOverflow
    //     0x8eb7d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb7dc: cmp             SP, x16
    //     0x8eb7e0: b.ls            #0x8eb804
    // 0x8eb7e4: mov             x1, x0
    // 0x8eb7e8: r0 = onInit()
    //     0x8eb7e8: bl              #0x8ed150  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin&WakelockMixin::onInit
    // 0x8eb7ec: ldur            x1, [fp, #-8]
    // 0x8eb7f0: r0 = _load()
    //     0x8eb7f0: bl              #0x8eb80c  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_load
    // 0x8eb7f4: r0 = Null
    //     0x8eb7f4: mov             x0, NULL
    // 0x8eb7f8: LeaveFrame
    //     0x8eb7f8: mov             SP, fp
    //     0x8eb7fc: ldp             fp, lr, [SP], #0x10
    // 0x8eb800: ret
    //     0x8eb800: ret             
    // 0x8eb804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eb804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eb808: b               #0x8eb7e4
  }
  _ _load(/* No info */) {
    // ** addr: 0x8eb80c, size: 0xfc
    // 0x8eb80c: EnterFrame
    //     0x8eb80c: stp             fp, lr, [SP, #-0x10]!
    //     0x8eb810: mov             fp, SP
    // 0x8eb814: AllocStack(0x40)
    //     0x8eb814: sub             SP, SP, #0x40
    // 0x8eb818: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x8 */)
    //     0x8eb818: stur            x1, [fp, #-8]
    // 0x8eb81c: CheckStackOverflow
    //     0x8eb81c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb820: cmp             SP, x16
    //     0x8eb824: b.ls            #0x8eb900
    // 0x8eb828: r1 = 1
    //     0x8eb828: movz            x1, #0x1
    // 0x8eb82c: r0 = AllocateContext()
    //     0x8eb82c: bl              #0xec126c  ; AllocateContextStub
    // 0x8eb830: mov             x3, x0
    // 0x8eb834: ldur            x0, [fp, #-8]
    // 0x8eb838: stur            x3, [fp, #-0x20]
    // 0x8eb83c: StoreField: r3->field_f = r0
    //     0x8eb83c: stur            w0, [x3, #0xf]
    // 0x8eb840: LoadField: r4 = r0->field_27
    //     0x8eb840: ldur            w4, [x0, #0x27]
    // 0x8eb844: DecompressPointer r4
    //     0x8eb844: add             x4, x4, HEAP, lsl #32
    // 0x8eb848: stur            x4, [fp, #-0x18]
    // 0x8eb84c: LoadField: r5 = r0->field_33
    //     0x8eb84c: ldur            x5, [x0, #0x33]
    // 0x8eb850: mov             x1, x4
    // 0x8eb854: mov             x2, x5
    // 0x8eb858: stur            x5, [fp, #-0x10]
    // 0x8eb85c: r0 = findByIdFromBookmark()
    //     0x8eb85c: bl              #0x8ecf00  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findByIdFromBookmark
    // 0x8eb860: stur            x0, [fp, #-0x28]
    // 0x8eb864: cmp             w0, NULL
    // 0x8eb868: b.eq            #0x8eb8ac
    // 0x8eb86c: ldur            x3, [fp, #-8]
    // 0x8eb870: LoadField: r1 = r3->field_3b
    //     0x8eb870: ldur            w1, [x3, #0x3b]
    // 0x8eb874: DecompressPointer r1
    //     0x8eb874: add             x1, x1, HEAP, lsl #32
    // 0x8eb878: r2 = true
    //     0x8eb878: add             x2, NULL, #0x20  ; true
    // 0x8eb87c: r0 = value=()
    //     0x8eb87c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8eb880: r0 = RxStatus()
    //     0x8eb880: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8eb884: mov             x1, x0
    // 0x8eb888: r0 = false
    //     0x8eb888: add             x0, NULL, #0x30  ; false
    // 0x8eb88c: StoreField: r1->field_f = r0
    //     0x8eb88c: stur            w0, [x1, #0xf]
    // 0x8eb890: StoreField: r1->field_7 = r0
    //     0x8eb890: stur            w0, [x1, #7]
    // 0x8eb894: StoreField: r1->field_b = r0
    //     0x8eb894: stur            w0, [x1, #0xb]
    // 0x8eb898: mov             x3, x1
    // 0x8eb89c: ldur            x1, [fp, #-8]
    // 0x8eb8a0: ldur            x2, [fp, #-0x28]
    // 0x8eb8a4: r0 = change()
    //     0x8eb8a4: bl              #0x8ece58  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin::change
    // 0x8eb8a8: b               #0x8eb8e4
    // 0x8eb8ac: ldur            x1, [fp, #-0x18]
    // 0x8eb8b0: ldur            x2, [fp, #-0x10]
    // 0x8eb8b4: r0 = findById()
    //     0x8eb8b4: bl              #0x8eba1c  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findById
    // 0x8eb8b8: ldur            x2, [fp, #-0x20]
    // 0x8eb8bc: r1 = Function '<anonymous closure>':.
    //     0x8eb8bc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ce0] AnonymousClosure: (0x8ecf8c), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_load (0x8eb80c)
    //     0x8eb8c0: ldr             x1, [x1, #0xce0]
    // 0x8eb8c4: stur            x0, [fp, #-8]
    // 0x8eb8c8: r0 = AllocateClosure()
    //     0x8eb8c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8eb8cc: r16 = <void?>
    //     0x8eb8cc: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8eb8d0: ldur            lr, [fp, #-8]
    // 0x8eb8d4: stp             lr, x16, [SP, #8]
    // 0x8eb8d8: str             x0, [SP]
    // 0x8eb8dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8eb8dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8eb8e0: r0 = then()
    //     0x8eb8e0: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x8eb8e4: ldur            x1, [fp, #-0x18]
    // 0x8eb8e8: ldur            x2, [fp, #-0x10]
    // 0x8eb8ec: r0 = counter()
    //     0x8eb8ec: bl              #0x8eb908  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::counter
    // 0x8eb8f0: r0 = Null
    //     0x8eb8f0: mov             x0, NULL
    // 0x8eb8f4: LeaveFrame
    //     0x8eb8f4: mov             SP, fp
    //     0x8eb8f8: ldp             fp, lr, [SP], #0x10
    // 0x8eb8fc: ret
    //     0x8eb8fc: ret             
    // 0x8eb900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eb900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eb904: b               #0x8eb828
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<ArticleDetail>) {
    // ** addr: 0x8ecf8c, size: 0xa0
    // 0x8ecf8c: EnterFrame
    //     0x8ecf8c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecf90: mov             fp, SP
    // 0x8ecf94: AllocStack(0x28)
    //     0x8ecf94: sub             SP, SP, #0x28
    // 0x8ecf98: SetupParameters()
    //     0x8ecf98: ldr             x0, [fp, #0x18]
    //     0x8ecf9c: ldur            w1, [x0, #0x17]
    //     0x8ecfa0: add             x1, x1, HEAP, lsl #32
    // 0x8ecfa4: CheckStackOverflow
    //     0x8ecfa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecfa8: cmp             SP, x16
    //     0x8ecfac: b.ls            #0x8ed024
    // 0x8ecfb0: LoadField: r0 = r1->field_f
    //     0x8ecfb0: ldur            w0, [x1, #0xf]
    // 0x8ecfb4: DecompressPointer r0
    //     0x8ecfb4: add             x0, x0, HEAP, lsl #32
    // 0x8ecfb8: mov             x2, x0
    // 0x8ecfbc: stur            x0, [fp, #-8]
    // 0x8ecfc0: r1 = Function '_onSuccess@1865171564':.
    //     0x8ecfc0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ce8] AnonymousClosure: (0x8ed0b0), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onSuccess (0x8ed0f0)
    //     0x8ecfc4: ldr             x1, [x1, #0xce8]
    // 0x8ecfc8: r0 = AllocateClosure()
    //     0x8ecfc8: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ecfcc: ldur            x2, [fp, #-8]
    // 0x8ecfd0: r1 = Function '_onError@1865171564':.
    //     0x8ecfd0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40cf0] AnonymousClosure: (0x8ed02c), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onError (0x8ed068)
    //     0x8ecfd4: ldr             x1, [x1, #0xcf0]
    // 0x8ecfd8: stur            x0, [fp, #-8]
    // 0x8ecfdc: r0 = AllocateClosure()
    //     0x8ecfdc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ecfe0: mov             x1, x0
    // 0x8ecfe4: ldr             x0, [fp, #0x10]
    // 0x8ecfe8: r2 = LoadClassIdInstr(r0)
    //     0x8ecfe8: ldur            x2, [x0, #-1]
    //     0x8ecfec: ubfx            x2, x2, #0xc, #0x14
    // 0x8ecff0: r16 = <void?>
    //     0x8ecff0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8ecff4: stp             x0, x16, [SP, #0x10]
    // 0x8ecff8: ldur            x16, [fp, #-8]
    // 0x8ecffc: stp             x16, x1, [SP]
    // 0x8ed000: mov             x0, x2
    // 0x8ed004: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8ed004: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8ed008: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8ed008: sub             lr, x0, #1, lsl #12
    //     0x8ed00c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ed010: blr             lr
    // 0x8ed014: r0 = Null
    //     0x8ed014: mov             x0, NULL
    // 0x8ed018: LeaveFrame
    //     0x8ed018: mov             SP, fp
    //     0x8ed01c: ldp             fp, lr, [SP], #0x10
    // 0x8ed020: ret
    //     0x8ed020: ret             
    // 0x8ed024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed024: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed028: b               #0x8ecfb0
  }
  [closure] void _onError(dynamic, NetworkExceptions) {
    // ** addr: 0x8ed02c, size: 0x3c
    // 0x8ed02c: EnterFrame
    //     0x8ed02c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed030: mov             fp, SP
    // 0x8ed034: ldr             x0, [fp, #0x18]
    // 0x8ed038: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ed038: ldur            w1, [x0, #0x17]
    // 0x8ed03c: DecompressPointer r1
    //     0x8ed03c: add             x1, x1, HEAP, lsl #32
    // 0x8ed040: CheckStackOverflow
    //     0x8ed040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed044: cmp             SP, x16
    //     0x8ed048: b.ls            #0x8ed060
    // 0x8ed04c: ldr             x2, [fp, #0x10]
    // 0x8ed050: r0 = _onError()
    //     0x8ed050: bl              #0x8ed068  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onError
    // 0x8ed054: LeaveFrame
    //     0x8ed054: mov             SP, fp
    //     0x8ed058: ldp             fp, lr, [SP], #0x10
    // 0x8ed05c: ret
    //     0x8ed05c: ret             
    // 0x8ed060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed064: b               #0x8ed04c
  }
  _ _onError(/* No info */) {
    // ** addr: 0x8ed068, size: 0x48
    // 0x8ed068: EnterFrame
    //     0x8ed068: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed06c: mov             fp, SP
    // 0x8ed070: mov             x0, x1
    // 0x8ed074: mov             x1, x2
    // 0x8ed078: CheckStackOverflow
    //     0x8ed078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed07c: cmp             SP, x16
    //     0x8ed080: b.ls            #0x8ed0a8
    // 0x8ed084: r0 = getErrorMessage()
    //     0x8ed084: bl              #0x8bfdd0  ; [package:nuonline/services/api_service/network_exceptions.dart] NetworkExceptions::getErrorMessage
    // 0x8ed088: mov             x1, x0
    // 0x8ed08c: r2 = Instance_IconData
    //     0x8ed08c: add             x2, PP, #0x28, lsl #12  ; [pp+0x28080] Obj!IconData@e0fe91
    //     0x8ed090: ldr             x2, [x2, #0x80]
    // 0x8ed094: r0 = show()
    //     0x8ed094: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0x8ed098: r0 = Null
    //     0x8ed098: mov             x0, NULL
    // 0x8ed09c: LeaveFrame
    //     0x8ed09c: mov             SP, fp
    //     0x8ed0a0: ldp             fp, lr, [SP], #0x10
    // 0x8ed0a4: ret
    //     0x8ed0a4: ret             
    // 0x8ed0a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed0a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed0ac: b               #0x8ed084
  }
  [closure] void _onSuccess(dynamic, ArticleDetail, dynamic) {
    // ** addr: 0x8ed0b0, size: 0x40
    // 0x8ed0b0: EnterFrame
    //     0x8ed0b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed0b4: mov             fp, SP
    // 0x8ed0b8: ldr             x0, [fp, #0x20]
    // 0x8ed0bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ed0bc: ldur            w1, [x0, #0x17]
    // 0x8ed0c0: DecompressPointer r1
    //     0x8ed0c0: add             x1, x1, HEAP, lsl #32
    // 0x8ed0c4: CheckStackOverflow
    //     0x8ed0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed0c8: cmp             SP, x16
    //     0x8ed0cc: b.ls            #0x8ed0e8
    // 0x8ed0d0: ldr             x2, [fp, #0x18]
    // 0x8ed0d4: ldr             x3, [fp, #0x10]
    // 0x8ed0d8: r0 = _onSuccess()
    //     0x8ed0d8: bl              #0x8ed0f0  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::_onSuccess
    // 0x8ed0dc: LeaveFrame
    //     0x8ed0dc: mov             SP, fp
    //     0x8ed0e0: ldp             fp, lr, [SP], #0x10
    // 0x8ed0e4: ret
    //     0x8ed0e4: ret             
    // 0x8ed0e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed0e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed0ec: b               #0x8ed0d0
  }
  _ _onSuccess(/* No info */) {
    // ** addr: 0x8ed0f0, size: 0x60
    // 0x8ed0f0: EnterFrame
    //     0x8ed0f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed0f4: mov             fp, SP
    // 0x8ed0f8: AllocStack(0x10)
    //     0x8ed0f8: sub             SP, SP, #0x10
    // 0x8ed0fc: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8ed0fc: stur            x1, [fp, #-8]
    //     0x8ed100: stur            x2, [fp, #-0x10]
    // 0x8ed104: CheckStackOverflow
    //     0x8ed104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed108: cmp             SP, x16
    //     0x8ed10c: b.ls            #0x8ed148
    // 0x8ed110: r0 = RxStatus()
    //     0x8ed110: bl              #0x72a8cc  ; AllocateRxStatusStub -> RxStatus (size=0x18)
    // 0x8ed114: mov             x1, x0
    // 0x8ed118: r0 = false
    //     0x8ed118: add             x0, NULL, #0x30  ; false
    // 0x8ed11c: StoreField: r1->field_f = r0
    //     0x8ed11c: stur            w0, [x1, #0xf]
    // 0x8ed120: StoreField: r1->field_7 = r0
    //     0x8ed120: stur            w0, [x1, #7]
    // 0x8ed124: StoreField: r1->field_b = r0
    //     0x8ed124: stur            w0, [x1, #0xb]
    // 0x8ed128: mov             x3, x1
    // 0x8ed12c: ldur            x1, [fp, #-8]
    // 0x8ed130: ldur            x2, [fp, #-0x10]
    // 0x8ed134: r0 = change()
    //     0x8ed134: bl              #0x8ece58  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] _ArticleDetailController&GetxController&StateMixin::change
    // 0x8ed138: r0 = Null
    //     0x8ed138: mov             x0, NULL
    // 0x8ed13c: LeaveFrame
    //     0x8ed13c: mov             SP, fp
    //     0x8ed140: ldp             fp, lr, [SP], #0x10
    // 0x8ed144: ret
    //     0x8ed144: ret             
    // 0x8ed148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed14c: b               #0x8ed110
  }
  [closure] Future<void> toggleBookmark(dynamic) {
    // ** addr: 0xbc1bac, size: 0x38
    // 0xbc1bac: EnterFrame
    //     0xbc1bac: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1bb0: mov             fp, SP
    // 0xbc1bb4: ldr             x0, [fp, #0x10]
    // 0xbc1bb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc1bb8: ldur            w1, [x0, #0x17]
    // 0xbc1bbc: DecompressPointer r1
    //     0xbc1bbc: add             x1, x1, HEAP, lsl #32
    // 0xbc1bc0: CheckStackOverflow
    //     0xbc1bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1bc4: cmp             SP, x16
    //     0xbc1bc8: b.ls            #0xbc1bdc
    // 0xbc1bcc: r0 = toggleBookmark()
    //     0xbc1bcc: bl              #0xbc1be4  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::toggleBookmark
    // 0xbc1bd0: LeaveFrame
    //     0xbc1bd0: mov             SP, fp
    //     0xbc1bd4: ldp             fp, lr, [SP], #0x10
    // 0xbc1bd8: ret
    //     0xbc1bd8: ret             
    // 0xbc1bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1be0: b               #0xbc1bcc
  }
  _ toggleBookmark(/* No info */) async {
    // ** addr: 0xbc1be4, size: 0x10c
    // 0xbc1be4: EnterFrame
    //     0xbc1be4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1be8: mov             fp, SP
    // 0xbc1bec: AllocStack(0x20)
    //     0xbc1bec: sub             SP, SP, #0x20
    // 0xbc1bf0: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbc1bf0: stur            NULL, [fp, #-8]
    //     0xbc1bf4: stur            x1, [fp, #-0x10]
    // 0xbc1bf8: CheckStackOverflow
    //     0xbc1bf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1bfc: cmp             SP, x16
    //     0xbc1c00: b.ls            #0xbc1ce4
    // 0xbc1c04: InitAsync() -> Future<void?>
    //     0xbc1c04: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc1c08: bl              #0x661298  ; InitAsyncStub
    // 0xbc1c0c: ldur            x1, [fp, #-0x10]
    // 0xbc1c10: r0 = notifyChildrens()
    //     0xbc1c10: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc1c14: ldur            x0, [fp, #-0x10]
    // 0xbc1c18: LoadField: r1 = r0->field_1f
    //     0xbc1c18: ldur            w1, [x0, #0x1f]
    // 0xbc1c1c: DecompressPointer r1
    //     0xbc1c1c: add             x1, x1, HEAP, lsl #32
    // 0xbc1c20: cmp             w1, NULL
    // 0xbc1c24: b.ne            #0xbc1c30
    // 0xbc1c28: r0 = Null
    //     0xbc1c28: mov             x0, NULL
    // 0xbc1c2c: r0 = ReturnAsyncNotFuture()
    //     0xbc1c2c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc1c30: LoadField: r2 = r0->field_3b
    //     0xbc1c30: ldur            w2, [x0, #0x3b]
    // 0xbc1c34: DecompressPointer r2
    //     0xbc1c34: add             x2, x2, HEAP, lsl #32
    // 0xbc1c38: mov             x1, x2
    // 0xbc1c3c: stur            x2, [fp, #-0x18]
    // 0xbc1c40: r0 = RxBoolExt.isFalse()
    //     0xbc1c40: bl              #0x91eb1c  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.isFalse
    // 0xbc1c44: tbnz            w0, #4, #0xbc1c9c
    // 0xbc1c48: ldur            x0, [fp, #-0x10]
    // 0xbc1c4c: ldur            x1, [fp, #-0x18]
    // 0xbc1c50: r0 = RxBoolExt.toggle()
    //     0xbc1c50: bl              #0xa428f0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.toggle
    // 0xbc1c54: ldur            x0, [fp, #-0x10]
    // 0xbc1c58: LoadField: r2 = r0->field_27
    //     0xbc1c58: ldur            w2, [x0, #0x27]
    // 0xbc1c5c: DecompressPointer r2
    //     0xbc1c5c: add             x2, x2, HEAP, lsl #32
    // 0xbc1c60: mov             x1, x0
    // 0xbc1c64: stur            x2, [fp, #-0x20]
    // 0xbc1c68: r0 = notifyChildrens()
    //     0xbc1c68: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc1c6c: ldur            x0, [fp, #-0x10]
    // 0xbc1c70: LoadField: r2 = r0->field_1f
    //     0xbc1c70: ldur            w2, [x0, #0x1f]
    // 0xbc1c74: DecompressPointer r2
    //     0xbc1c74: add             x2, x2, HEAP, lsl #32
    // 0xbc1c78: cmp             w2, NULL
    // 0xbc1c7c: b.eq            #0xbc1cec
    // 0xbc1c80: ldur            x1, [fp, #-0x20]
    // 0xbc1c84: r0 = saveToBookmark()
    //     0xbc1c84: bl              #0xa42a30  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::saveToBookmark
    // 0xbc1c88: mov             x1, x0
    // 0xbc1c8c: stur            x1, [fp, #-0x20]
    // 0xbc1c90: r0 = Await()
    //     0xbc1c90: bl              #0x661044  ; AwaitStub
    // 0xbc1c94: r0 = bookmark()
    //     0xbc1c94: bl              #0xa427d4  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::bookmark
    // 0xbc1c98: b               #0xbc1cdc
    // 0xbc1c9c: ldur            x0, [fp, #-0x10]
    // 0xbc1ca0: ldur            x1, [fp, #-0x18]
    // 0xbc1ca4: r0 = RxBoolExt.toggle()
    //     0xbc1ca4: bl              #0xa428f0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.toggle
    // 0xbc1ca8: ldur            x0, [fp, #-0x10]
    // 0xbc1cac: LoadField: r1 = r0->field_27
    //     0xbc1cac: ldur            w1, [x0, #0x27]
    // 0xbc1cb0: DecompressPointer r1
    //     0xbc1cb0: add             x1, x1, HEAP, lsl #32
    // 0xbc1cb4: LoadField: r2 = r0->field_33
    //     0xbc1cb4: ldur            x2, [x0, #0x33]
    // 0xbc1cb8: r0 = deleteFromBookmark()
    //     0xbc1cb8: bl              #0xa42b84  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::deleteFromBookmark
    // 0xbc1cbc: mov             x1, x0
    // 0xbc1cc0: stur            x1, [fp, #-0x10]
    // 0xbc1cc4: r0 = Await()
    //     0xbc1cc4: bl              #0x661044  ; AwaitStub
    // 0xbc1cc8: r1 = "Artikel dihapus dari bookmark!"
    //     0xbc1cc8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b38] "Artikel dihapus dari bookmark!"
    //     0xbc1ccc: ldr             x1, [x1, #0xb38]
    // 0xbc1cd0: r2 = Instance_IconData
    //     0xbc1cd0: add             x2, PP, #0x29, lsl #12  ; [pp+0x29890] Obj!IconData@e100b1
    //     0xbc1cd4: ldr             x2, [x2, #0x890]
    // 0xbc1cd8: r0 = show()
    //     0xbc1cd8: bl              #0x7e2814  ; [package:nuikit/src/widgets/snackbar/snackbar.dart] NSnackBar::show
    // 0xbc1cdc: r0 = Null
    //     0xbc1cdc: mov             x0, NULL
    // 0xbc1ce0: r0 = ReturnAsyncNotFuture()
    //     0xbc1ce0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc1ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1ce8: b               #0xbc1c04
    // 0xbc1cec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc1cec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> openInWeb(dynamic) {
    // ** addr: 0xbc1ff0, size: 0x38
    // 0xbc1ff0: EnterFrame
    //     0xbc1ff0: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1ff4: mov             fp, SP
    // 0xbc1ff8: ldr             x0, [fp, #0x10]
    // 0xbc1ffc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc1ffc: ldur            w1, [x0, #0x17]
    // 0xbc2000: DecompressPointer r1
    //     0xbc2000: add             x1, x1, HEAP, lsl #32
    // 0xbc2004: CheckStackOverflow
    //     0xbc2004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2008: cmp             SP, x16
    //     0xbc200c: b.ls            #0xbc2020
    // 0xbc2010: r0 = openInWeb()
    //     0xbc2010: bl              #0xbc2028  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::openInWeb
    // 0xbc2014: LeaveFrame
    //     0xbc2014: mov             SP, fp
    //     0xbc2018: ldp             fp, lr, [SP], #0x10
    // 0xbc201c: ret
    //     0xbc201c: ret             
    // 0xbc2020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2024: b               #0xbc2010
  }
  _ openInWeb(/* No info */) async {
    // ** addr: 0xbc2028, size: 0xa8
    // 0xbc2028: EnterFrame
    //     0xbc2028: stp             fp, lr, [SP, #-0x10]!
    //     0xbc202c: mov             fp, SP
    // 0xbc2030: AllocStack(0x20)
    //     0xbc2030: sub             SP, SP, #0x20
    // 0xbc2034: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbc2034: stur            NULL, [fp, #-8]
    //     0xbc2038: stur            x1, [fp, #-0x10]
    // 0xbc203c: CheckStackOverflow
    //     0xbc203c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2040: cmp             SP, x16
    //     0xbc2044: b.ls            #0xbc20c4
    // 0xbc2048: InitAsync() -> Future<void?>
    //     0xbc2048: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc204c: bl              #0x661298  ; InitAsyncStub
    // 0xbc2050: ldur            x1, [fp, #-0x10]
    // 0xbc2054: r0 = notifyChildrens()
    //     0xbc2054: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2058: ldur            x0, [fp, #-0x10]
    // 0xbc205c: LoadField: r1 = r0->field_1f
    //     0xbc205c: ldur            w1, [x0, #0x1f]
    // 0xbc2060: DecompressPointer r1
    //     0xbc2060: add             x1, x1, HEAP, lsl #32
    // 0xbc2064: cmp             w1, NULL
    // 0xbc2068: b.eq            #0xbc20b4
    // 0xbc206c: mov             x1, x0
    // 0xbc2070: r0 = notifyChildrens()
    //     0xbc2070: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2074: ldur            x0, [fp, #-0x10]
    // 0xbc2078: LoadField: r1 = r0->field_1f
    //     0xbc2078: ldur            w1, [x0, #0x1f]
    // 0xbc207c: DecompressPointer r1
    //     0xbc207c: add             x1, x1, HEAP, lsl #32
    // 0xbc2080: cmp             w1, NULL
    // 0xbc2084: b.eq            #0xbc20cc
    // 0xbc2088: LoadField: r2 = r1->field_23
    //     0xbc2088: ldur            w2, [x1, #0x23]
    // 0xbc208c: DecompressPointer r2
    //     0xbc208c: add             x2, x2, HEAP, lsl #32
    // 0xbc2090: r16 = Instance_LaunchMode
    //     0xbc2090: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a470] Obj!LaunchMode@e2dee1
    //     0xbc2094: ldr             x16, [x16, #0x470]
    // 0xbc2098: str             x16, [SP]
    // 0xbc209c: mov             x1, x2
    // 0xbc20a0: r4 = const [0, 0x2, 0x1, 0x1, mode, 0x1, null]
    //     0xbc20a0: ldr             x4, [PP, #0x1cd8]  ; [pp+0x1cd8] List(7) [0, 0x2, 0x1, 0x1, "mode", 0x1, Null]
    // 0xbc20a4: r0 = launchUrlString()
    //     0xbc20a4: bl              #0x7d9cc8  ; [package:url_launcher/src/url_launcher_string.dart] ::launchUrlString
    // 0xbc20a8: mov             x1, x0
    // 0xbc20ac: stur            x1, [fp, #-0x18]
    // 0xbc20b0: r0 = Await()
    //     0xbc20b0: bl              #0x661044  ; AwaitStub
    // 0xbc20b4: ldur            x1, [fp, #-0x10]
    // 0xbc20b8: r0 = closeBottomSheet()
    //     0xbc20b8: bl              #0xbc20d0  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::closeBottomSheet
    // 0xbc20bc: r0 = Null
    //     0xbc20bc: mov             x0, NULL
    // 0xbc20c0: r0 = ReturnAsyncNotFuture()
    //     0xbc20c0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc20c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc20c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc20c8: b               #0xbc2048
    // 0xbc20cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc20cc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ closeBottomSheet(/* No info */) {
    // ** addr: 0xbc20d0, size: 0x68
    // 0xbc20d0: EnterFrame
    //     0xbc20d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbc20d4: mov             fp, SP
    // 0xbc20d8: AllocStack(0x8)
    //     0xbc20d8: sub             SP, SP, #8
    // 0xbc20dc: CheckStackOverflow
    //     0xbc20dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc20e0: cmp             SP, x16
    //     0xbc20e4: b.ls            #0xbc2130
    // 0xbc20e8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc20e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc20ec: ldr             x0, [x0, #0x2670]
    //     0xbc20f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc20f4: cmp             w0, w16
    //     0xbc20f8: b.ne            #0xbc2104
    //     0xbc20fc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbc2100: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc2104: r0 = GetNavigation.isBottomSheetOpen()
    //     0xbc2104: bl              #0x6591c4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isBottomSheetOpen
    // 0xbc2108: r16 = true
    //     0xbc2108: add             x16, NULL, #0x20  ; true
    // 0xbc210c: cmp             w0, w16
    // 0xbc2110: b.ne            #0xbc2120
    // 0xbc2114: str             NULL, [SP]
    // 0xbc2118: r4 = const [0x1, 0, 0, 0, null]
    //     0xbc2118: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xbc211c: r0 = GetNavigation.back()
    //     0xbc211c: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbc2120: r0 = Null
    //     0xbc2120: mov             x0, NULL
    // 0xbc2124: LeaveFrame
    //     0xbc2124: mov             SP, fp
    //     0xbc2128: ldp             fp, lr, [SP], #0x10
    // 0xbc212c: ret
    //     0xbc212c: ret             
    // 0xbc2130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2130: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2134: b               #0xbc20e8
  }
  [closure] Future<void> savePDF(dynamic) {
    // ** addr: 0xbc2138, size: 0x38
    // 0xbc2138: EnterFrame
    //     0xbc2138: stp             fp, lr, [SP, #-0x10]!
    //     0xbc213c: mov             fp, SP
    // 0xbc2140: ldr             x0, [fp, #0x10]
    // 0xbc2144: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc2144: ldur            w1, [x0, #0x17]
    // 0xbc2148: DecompressPointer r1
    //     0xbc2148: add             x1, x1, HEAP, lsl #32
    // 0xbc214c: CheckStackOverflow
    //     0xbc214c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2150: cmp             SP, x16
    //     0xbc2154: b.ls            #0xbc2168
    // 0xbc2158: r0 = savePDF()
    //     0xbc2158: bl              #0xbc2170  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::savePDF
    // 0xbc215c: LeaveFrame
    //     0xbc215c: mov             SP, fp
    //     0xbc2160: ldp             fp, lr, [SP], #0x10
    // 0xbc2164: ret
    //     0xbc2164: ret             
    // 0xbc2168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc216c: b               #0xbc2158
  }
  _ savePDF(/* No info */) async {
    // ** addr: 0xbc2170, size: 0x2ac
    // 0xbc2170: EnterFrame
    //     0xbc2170: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2174: mov             fp, SP
    // 0xbc2178: AllocStack(0x40)
    //     0xbc2178: sub             SP, SP, #0x40
    // 0xbc217c: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbc217c: stur            NULL, [fp, #-8]
    //     0xbc2180: stur            x1, [fp, #-0x10]
    // 0xbc2184: CheckStackOverflow
    //     0xbc2184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2188: cmp             SP, x16
    //     0xbc218c: b.ls            #0xbc2400
    // 0xbc2190: InitAsync() -> Future<void?>
    //     0xbc2190: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc2194: bl              #0x661298  ; InitAsyncStub
    // 0xbc2198: ldur            x1, [fp, #-0x10]
    // 0xbc219c: r0 = closeBottomSheet()
    //     0xbc219c: bl              #0xbc20d0  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::closeBottomSheet
    // 0xbc21a0: ldur            x1, [fp, #-0x10]
    // 0xbc21a4: r0 = notifyChildrens()
    //     0xbc21a4: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc21a8: ldur            x0, [fp, #-0x10]
    // 0xbc21ac: LoadField: r1 = r0->field_1f
    //     0xbc21ac: ldur            w1, [x0, #0x1f]
    // 0xbc21b0: DecompressPointer r1
    //     0xbc21b0: add             x1, x1, HEAP, lsl #32
    // 0xbc21b4: cmp             w1, NULL
    // 0xbc21b8: b.ne            #0xbc21c4
    // 0xbc21bc: r0 = Null
    //     0xbc21bc: mov             x0, NULL
    // 0xbc21c0: r0 = ReturnAsyncNotFuture()
    //     0xbc21c0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc21c4: mov             x1, x0
    // 0xbc21c8: r0 = notifyChildrens()
    //     0xbc21c8: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc21cc: ldur            x0, [fp, #-0x10]
    // 0xbc21d0: LoadField: r2 = r0->field_1f
    //     0xbc21d0: ldur            w2, [x0, #0x1f]
    // 0xbc21d4: DecompressPointer r2
    //     0xbc21d4: add             x2, x2, HEAP, lsl #32
    // 0xbc21d8: stur            x2, [fp, #-0x18]
    // 0xbc21dc: cmp             w2, NULL
    // 0xbc21e0: b.eq            #0xbc2408
    // 0xbc21e4: mov             x1, x0
    // 0xbc21e8: r0 = notifyChildrens()
    //     0xbc21e8: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc21ec: ldur            x0, [fp, #-0x10]
    // 0xbc21f0: LoadField: r1 = r0->field_1f
    //     0xbc21f0: ldur            w1, [x0, #0x1f]
    // 0xbc21f4: DecompressPointer r1
    //     0xbc21f4: add             x1, x1, HEAP, lsl #32
    // 0xbc21f8: cmp             w1, NULL
    // 0xbc21fc: b.eq            #0xbc240c
    // 0xbc2200: LoadField: r2 = r1->field_1b
    //     0xbc2200: ldur            w2, [x1, #0x1b]
    // 0xbc2204: DecompressPointer r2
    //     0xbc2204: add             x2, x2, HEAP, lsl #32
    // 0xbc2208: LoadField: r1 = r2->field_13
    //     0xbc2208: ldur            x1, [x2, #0x13]
    // 0xbc220c: cmp             x1, #9
    // 0xbc2210: b.ne            #0xbc2220
    // 0xbc2214: mov             x1, x0
    // 0xbc2218: r2 = true
    //     0xbc2218: add             x2, NULL, #0x20  ; true
    // 0xbc221c: b               #0xbc2278
    // 0xbc2220: mov             x1, x0
    // 0xbc2224: r0 = notifyChildrens()
    //     0xbc2224: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2228: ldur            x1, [fp, #-0x10]
    // 0xbc222c: LoadField: r0 = r1->field_1f
    //     0xbc222c: ldur            w0, [x1, #0x1f]
    // 0xbc2230: DecompressPointer r0
    //     0xbc2230: add             x0, x0, HEAP, lsl #32
    // 0xbc2234: cmp             w0, NULL
    // 0xbc2238: b.eq            #0xbc2410
    // 0xbc223c: LoadField: r2 = r0->field_1b
    //     0xbc223c: ldur            w2, [x0, #0x1b]
    // 0xbc2240: DecompressPointer r2
    //     0xbc2240: add             x2, x2, HEAP, lsl #32
    // 0xbc2244: LoadField: r0 = r2->field_1b
    //     0xbc2244: ldur            w0, [x2, #0x1b]
    // 0xbc2248: DecompressPointer r0
    //     0xbc2248: add             x0, x0, HEAP, lsl #32
    // 0xbc224c: r2 = LoadClassIdInstr(r0)
    //     0xbc224c: ldur            x2, [x0, #-1]
    //     0xbc2250: ubfx            x2, x2, #0xc, #0x14
    // 0xbc2254: r16 = "Khutbah"
    //     0xbc2254: add             x16, PP, #0xf, lsl #12  ; [pp+0xfcc8] "Khutbah"
    //     0xbc2258: ldr             x16, [x16, #0xcc8]
    // 0xbc225c: stp             x16, x0, [SP]
    // 0xbc2260: mov             x0, x2
    // 0xbc2264: mov             lr, x0
    // 0xbc2268: ldr             lr, [x21, lr, lsl #3]
    // 0xbc226c: blr             lr
    // 0xbc2270: mov             x2, x0
    // 0xbc2274: ldur            x1, [fp, #-0x10]
    // 0xbc2278: ldur            x0, [fp, #-0x18]
    // 0xbc227c: stur            x2, [fp, #-0x20]
    // 0xbc2280: r0 = ArticleHtmlTemplate()
    //     0xbc2280: bl              #0xbc2ae8  ; AllocateArticleHtmlTemplateStub -> ArticleHtmlTemplate (size=0x10)
    // 0xbc2284: mov             x1, x0
    // 0xbc2288: ldur            x0, [fp, #-0x18]
    // 0xbc228c: StoreField: r1->field_7 = r0
    //     0xbc228c: stur            w0, [x1, #7]
    // 0xbc2290: ldur            x0, [fp, #-0x20]
    // 0xbc2294: StoreField: r1->field_b = r0
    //     0xbc2294: stur            w0, [x1, #0xb]
    // 0xbc2298: r0 = build()
    //     0xbc2298: bl              #0xbc2428  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::build
    // 0xbc229c: mov             x3, x0
    // 0xbc22a0: ldur            x0, [fp, #-0x10]
    // 0xbc22a4: stur            x3, [fp, #-0x20]
    // 0xbc22a8: LoadField: r4 = r0->field_2b
    //     0xbc22a8: ldur            w4, [x0, #0x2b]
    // 0xbc22ac: DecompressPointer r4
    //     0xbc22ac: add             x4, x4, HEAP, lsl #32
    // 0xbc22b0: stur            x4, [fp, #-0x18]
    // 0xbc22b4: r1 = Null
    //     0xbc22b4: mov             x1, NULL
    // 0xbc22b8: r2 = 4
    //     0xbc22b8: movz            x2, #0x4
    // 0xbc22bc: r0 = AllocateArray()
    //     0xbc22bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc22c0: stur            x0, [fp, #-0x28]
    // 0xbc22c4: r16 = "nu-online-article-"
    //     0xbc22c4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40ba0] "nu-online-article-"
    //     0xbc22c8: ldr             x16, [x16, #0xba0]
    // 0xbc22cc: StoreField: r0->field_f = r16
    //     0xbc22cc: stur            w16, [x0, #0xf]
    // 0xbc22d0: ldur            x1, [fp, #-0x10]
    // 0xbc22d4: r0 = notifyChildrens()
    //     0xbc22d4: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc22d8: ldur            x2, [fp, #-0x10]
    // 0xbc22dc: LoadField: r0 = r2->field_1f
    //     0xbc22dc: ldur            w0, [x2, #0x1f]
    // 0xbc22e0: DecompressPointer r0
    //     0xbc22e0: add             x0, x0, HEAP, lsl #32
    // 0xbc22e4: cmp             w0, NULL
    // 0xbc22e8: b.eq            #0xbc2414
    // 0xbc22ec: LoadField: r3 = r0->field_7
    //     0xbc22ec: ldur            x3, [x0, #7]
    // 0xbc22f0: r0 = BoxInt64Instr(r3)
    //     0xbc22f0: sbfiz           x0, x3, #1, #0x1f
    //     0xbc22f4: cmp             x3, x0, asr #1
    //     0xbc22f8: b.eq            #0xbc2304
    //     0xbc22fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbc2300: stur            x3, [x0, #7]
    // 0xbc2304: ldur            x1, [fp, #-0x28]
    // 0xbc2308: ArrayStore: r1[1] = r0  ; List_4
    //     0xbc2308: add             x25, x1, #0x13
    //     0xbc230c: str             w0, [x25]
    //     0xbc2310: tbz             w0, #0, #0xbc232c
    //     0xbc2314: ldurb           w16, [x1, #-1]
    //     0xbc2318: ldurb           w17, [x0, #-1]
    //     0xbc231c: and             x16, x17, x16, lsr #2
    //     0xbc2320: tst             x16, HEAP, lsr #32
    //     0xbc2324: b.eq            #0xbc232c
    //     0xbc2328: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc232c: ldur            x16, [fp, #-0x28]
    // 0xbc2330: str             x16, [SP]
    // 0xbc2334: r0 = _interpolate()
    //     0xbc2334: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2338: r1 = Null
    //     0xbc2338: mov             x1, NULL
    // 0xbc233c: r2 = 4
    //     0xbc233c: movz            x2, #0x4
    // 0xbc2340: stur            x0, [fp, #-0x28]
    // 0xbc2344: r0 = AllocateArray()
    //     0xbc2344: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2348: stur            x0, [fp, #-0x30]
    // 0xbc234c: r16 = "nu-online-article-"
    //     0xbc234c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40ba0] "nu-online-article-"
    //     0xbc2350: ldr             x16, [x16, #0xba0]
    // 0xbc2354: StoreField: r0->field_f = r16
    //     0xbc2354: stur            w16, [x0, #0xf]
    // 0xbc2358: ldur            x1, [fp, #-0x10]
    // 0xbc235c: r0 = notifyChildrens()
    //     0xbc235c: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2360: ldur            x0, [fp, #-0x10]
    // 0xbc2364: LoadField: r1 = r0->field_1f
    //     0xbc2364: ldur            w1, [x0, #0x1f]
    // 0xbc2368: DecompressPointer r1
    //     0xbc2368: add             x1, x1, HEAP, lsl #32
    // 0xbc236c: cmp             w1, NULL
    // 0xbc2370: b.eq            #0xbc2418
    // 0xbc2374: LoadField: r2 = r1->field_7
    //     0xbc2374: ldur            x2, [x1, #7]
    // 0xbc2378: r0 = BoxInt64Instr(r2)
    //     0xbc2378: sbfiz           x0, x2, #1, #0x1f
    //     0xbc237c: cmp             x2, x0, asr #1
    //     0xbc2380: b.eq            #0xbc238c
    //     0xbc2384: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbc2388: stur            x2, [x0, #7]
    // 0xbc238c: ldur            x1, [fp, #-0x30]
    // 0xbc2390: ArrayStore: r1[1] = r0  ; List_4
    //     0xbc2390: add             x25, x1, #0x13
    //     0xbc2394: str             w0, [x25]
    //     0xbc2398: tbz             w0, #0, #0xbc23b4
    //     0xbc239c: ldurb           w16, [x1, #-1]
    //     0xbc23a0: ldurb           w17, [x0, #-1]
    //     0xbc23a4: and             x16, x17, x16, lsr #2
    //     0xbc23a8: tst             x16, HEAP, lsr #32
    //     0xbc23ac: b.eq            #0xbc23b4
    //     0xbc23b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc23b4: ldur            x16, [fp, #-0x30]
    // 0xbc23b8: str             x16, [SP]
    // 0xbc23bc: r0 = _interpolate()
    //     0xbc23bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc23c0: stur            x0, [fp, #-0x10]
    // 0xbc23c4: r0 = AndroidPdfServiceHtmlContent()
    //     0xbc23c4: bl              #0xbc241c  ; AllocateAndroidPdfServiceHtmlContentStub -> AndroidPdfServiceHtmlContent (size=0x10)
    // 0xbc23c8: mov             x1, x0
    // 0xbc23cc: ldur            x0, [fp, #-0x20]
    // 0xbc23d0: StoreField: r1->field_7 = r0
    //     0xbc23d0: stur            w0, [x1, #7]
    // 0xbc23d4: ldur            x0, [fp, #-0x10]
    // 0xbc23d8: StoreField: r1->field_b = r0
    //     0xbc23d8: stur            w0, [x1, #0xb]
    // 0xbc23dc: mov             x3, x1
    // 0xbc23e0: ldur            x1, [fp, #-0x18]
    // 0xbc23e4: ldur            x2, [fp, #-0x28]
    // 0xbc23e8: r0 = share()
    //     0xbc23e8: bl              #0xb0d024  ; [package:nuonline/services/pdf_service.dart] PdfService::share
    // 0xbc23ec: mov             x1, x0
    // 0xbc23f0: stur            x1, [fp, #-0x10]
    // 0xbc23f4: r0 = Await()
    //     0xbc23f4: bl              #0x661044  ; AwaitStub
    // 0xbc23f8: r0 = Null
    //     0xbc23f8: mov             x0, NULL
    // 0xbc23fc: r0 = ReturnAsyncNotFuture()
    //     0xbc23fc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc2400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2404: b               #0xbc2190
    // 0xbc2408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2408: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc240c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc240c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc2410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2410: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc2414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2414: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc2418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2418: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> share(dynamic) {
    // ** addr: 0xbc2af4, size: 0x38
    // 0xbc2af4: EnterFrame
    //     0xbc2af4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2af8: mov             fp, SP
    // 0xbc2afc: ldr             x0, [fp, #0x10]
    // 0xbc2b00: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc2b00: ldur            w1, [x0, #0x17]
    // 0xbc2b04: DecompressPointer r1
    //     0xbc2b04: add             x1, x1, HEAP, lsl #32
    // 0xbc2b08: CheckStackOverflow
    //     0xbc2b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2b0c: cmp             SP, x16
    //     0xbc2b10: b.ls            #0xbc2b24
    // 0xbc2b14: r0 = share()
    //     0xbc2b14: bl              #0xbc2b2c  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::share
    // 0xbc2b18: LeaveFrame
    //     0xbc2b18: mov             SP, fp
    //     0xbc2b1c: ldp             fp, lr, [SP], #0x10
    // 0xbc2b20: ret
    //     0xbc2b20: ret             
    // 0xbc2b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2b24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2b28: b               #0xbc2b14
  }
  _ share(/* No info */) async {
    // ** addr: 0xbc2b2c, size: 0x10c
    // 0xbc2b2c: EnterFrame
    //     0xbc2b2c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2b30: mov             fp, SP
    // 0xbc2b34: AllocStack(0x28)
    //     0xbc2b34: sub             SP, SP, #0x28
    // 0xbc2b38: SetupParameters(ArticleDetailController this /* r1 => r1, fp-0x10 */)
    //     0xbc2b38: stur            NULL, [fp, #-8]
    //     0xbc2b3c: stur            x1, [fp, #-0x10]
    // 0xbc2b40: CheckStackOverflow
    //     0xbc2b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2b44: cmp             SP, x16
    //     0xbc2b48: b.ls            #0xbc2c2c
    // 0xbc2b4c: InitAsync() -> Future<void?>
    //     0xbc2b4c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbc2b50: bl              #0x661298  ; InitAsyncStub
    // 0xbc2b54: ldur            x1, [fp, #-0x10]
    // 0xbc2b58: r0 = notifyChildrens()
    //     0xbc2b58: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2b5c: ldur            x0, [fp, #-0x10]
    // 0xbc2b60: LoadField: r1 = r0->field_1f
    //     0xbc2b60: ldur            w1, [x0, #0x1f]
    // 0xbc2b64: DecompressPointer r1
    //     0xbc2b64: add             x1, x1, HEAP, lsl #32
    // 0xbc2b68: cmp             w1, NULL
    // 0xbc2b6c: b.eq            #0xbc2c1c
    // 0xbc2b70: mov             x1, x0
    // 0xbc2b74: r0 = notifyChildrens()
    //     0xbc2b74: bl              #0x6fb1d8  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::notifyChildrens
    // 0xbc2b78: ldur            x0, [fp, #-0x10]
    // 0xbc2b7c: LoadField: r1 = r0->field_1f
    //     0xbc2b7c: ldur            w1, [x0, #0x1f]
    // 0xbc2b80: DecompressPointer r1
    //     0xbc2b80: add             x1, x1, HEAP, lsl #32
    // 0xbc2b84: cmp             w1, NULL
    // 0xbc2b88: b.eq            #0xbc2c34
    // 0xbc2b8c: r0 = shareableText()
    //     0xbc2b8c: bl              #0xbc2c38  ; [package:nuonline/app/data/models/article.dart] ArticleDetail::shareableText
    // 0xbc2b90: stur            x0, [fp, #-0x18]
    // 0xbc2b94: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc2b94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc2b98: ldr             x0, [x0, #0x2670]
    //     0xbc2b9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc2ba0: cmp             w0, w16
    //     0xbc2ba4: b.ne            #0xbc2bb0
    //     0xbc2ba8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbc2bac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc2bb0: r0 = GetNavigation.size()
    //     0xbc2bb0: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc2bb4: LoadField: d0 = r0->field_7
    //     0xbc2bb4: ldur            d0, [x0, #7]
    // 0xbc2bb8: stur            d0, [fp, #-0x20]
    // 0xbc2bbc: r0 = GetNavigation.size()
    //     0xbc2bbc: bl              #0x7daa90  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc2bc0: LoadField: d0 = r0->field_f
    //     0xbc2bc0: ldur            d0, [x0, #0xf]
    // 0xbc2bc4: d1 = 2.000000
    //     0xbc2bc4: fmov            d1, #2.00000000
    // 0xbc2bc8: fdiv            d2, d0, d1
    // 0xbc2bcc: ldur            d0, [fp, #-0x20]
    // 0xbc2bd0: d1 = 0.000000
    //     0xbc2bd0: eor             v1.16b, v1.16b, v1.16b
    // 0xbc2bd4: fadd            d3, d0, d1
    // 0xbc2bd8: stur            d3, [fp, #-0x28]
    // 0xbc2bdc: fadd            d0, d2, d1
    // 0xbc2be0: stur            d0, [fp, #-0x20]
    // 0xbc2be4: r0 = Rect()
    //     0xbc2be4: bl              #0x618734  ; AllocateRectStub -> Rect (size=0x28)
    // 0xbc2be8: StoreField: r0->field_7 = rZR
    //     0xbc2be8: stur            xzr, [x0, #7]
    // 0xbc2bec: StoreField: r0->field_f = rZR
    //     0xbc2bec: stur            xzr, [x0, #0xf]
    // 0xbc2bf0: ldur            d0, [fp, #-0x28]
    // 0xbc2bf4: ArrayStore: r0[0] = d0  ; List_8
    //     0xbc2bf4: stur            d0, [x0, #0x17]
    // 0xbc2bf8: ldur            d0, [fp, #-0x20]
    // 0xbc2bfc: StoreField: r0->field_1f = d0
    //     0xbc2bfc: stur            d0, [x0, #0x1f]
    // 0xbc2c00: ldur            x1, [fp, #-0x18]
    // 0xbc2c04: mov             x2, x0
    // 0xbc2c08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbc2c08: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbc2c0c: r0 = share()
    //     0xbc2c0c: bl              #0xb0217c  ; [package:share_plus/share_plus.dart] Share::share
    // 0xbc2c10: mov             x1, x0
    // 0xbc2c14: stur            x1, [fp, #-0x18]
    // 0xbc2c18: r0 = Await()
    //     0xbc2c18: bl              #0x661044  ; AwaitStub
    // 0xbc2c1c: ldur            x1, [fp, #-0x10]
    // 0xbc2c20: r0 = closeBottomSheet()
    //     0xbc2c20: bl              #0xbc20d0  ; [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::closeBottomSheet
    // 0xbc2c24: r0 = Null
    //     0xbc2c24: mov             x0, NULL
    // 0xbc2c28: r0 = ReturnAsyncNotFuture()
    //     0xbc2c28: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbc2c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2c30: b               #0xbc2b4c
    // 0xbc2c34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2c34: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
