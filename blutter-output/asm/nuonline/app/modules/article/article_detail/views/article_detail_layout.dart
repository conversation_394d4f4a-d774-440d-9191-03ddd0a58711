// lib: , url: package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart

// class id: 1050134, size: 0x8
class :: {
}

// class id: 4120, size: 0x18, field offset: 0x14
class _ArticleDetailLayoutState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xa33ddc, size: 0x133c
    // 0xa33ddc: EnterFrame
    //     0xa33ddc: stp             fp, lr, [SP, #-0x10]!
    //     0xa33de0: mov             fp, SP
    // 0xa33de4: AllocStack(0x60)
    //     0xa33de4: sub             SP, SP, #0x60
    // 0xa33de8: SetupParameters(_ArticleDetailLayoutState this /* r1 => r0, fp-0x8 */)
    //     0xa33de8: mov             x0, x1
    //     0xa33dec: stur            x1, [fp, #-8]
    // 0xa33df0: CheckStackOverflow
    //     0xa33df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa33df4: cmp             SP, x16
    //     0xa33df8: b.ls            #0xa350d0
    // 0xa33dfc: r1 = 1
    //     0xa33dfc: movz            x1, #0x1
    // 0xa33e00: r0 = AllocateContext()
    //     0xa33e00: bl              #0xec126c  ; AllocateContextStub
    // 0xa33e04: mov             x1, x0
    // 0xa33e08: ldur            x2, [fp, #-8]
    // 0xa33e0c: stur            x1, [fp, #-0x10]
    // 0xa33e10: StoreField: r1->field_f = r2
    //     0xa33e10: stur            w2, [x1, #0xf]
    // 0xa33e14: LoadField: r0 = r2->field_b
    //     0xa33e14: ldur            w0, [x2, #0xb]
    // 0xa33e18: DecompressPointer r0
    //     0xa33e18: add             x0, x0, HEAP, lsl #32
    // 0xa33e1c: cmp             w0, NULL
    // 0xa33e20: b.eq            #0xa350d8
    // 0xa33e24: LoadField: r3 = r0->field_b
    //     0xa33e24: ldur            w3, [x0, #0xb]
    // 0xa33e28: DecompressPointer r3
    //     0xa33e28: add             x3, x3, HEAP, lsl #32
    // 0xa33e2c: LoadField: r0 = r3->field_1b
    //     0xa33e2c: ldur            w0, [x3, #0x1b]
    // 0xa33e30: DecompressPointer r0
    //     0xa33e30: add             x0, x0, HEAP, lsl #32
    // 0xa33e34: LoadField: r3 = r0->field_1b
    //     0xa33e34: ldur            w3, [x0, #0x1b]
    // 0xa33e38: DecompressPointer r3
    //     0xa33e38: add             x3, x3, HEAP, lsl #32
    // 0xa33e3c: r0 = LoadClassIdInstr(r3)
    //     0xa33e3c: ldur            x0, [x3, #-1]
    //     0xa33e40: ubfx            x0, x0, #0xc, #0x14
    // 0xa33e44: str             x3, [SP]
    // 0xa33e48: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa33e48: sub             lr, x0, #0xff6
    //     0xa33e4c: ldr             lr, [x21, lr, lsl #3]
    //     0xa33e50: blr             lr
    // 0xa33e54: stur            x0, [fp, #-0x18]
    // 0xa33e58: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa33e58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa33e5c: ldr             x0, [x0, #0x2670]
    //     0xa33e60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa33e64: cmp             w0, w16
    //     0xa33e68: b.ne            #0xa33e74
    //     0xa33e6c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa33e70: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa33e74: r0 = GetNavigation.textTheme()
    //     0xa33e74: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa33e78: LoadField: r1 = r0->field_f
    //     0xa33e78: ldur            w1, [x0, #0xf]
    // 0xa33e7c: DecompressPointer r1
    //     0xa33e7c: add             x1, x1, HEAP, lsl #32
    // 0xa33e80: stur            x1, [fp, #-0x20]
    // 0xa33e84: cmp             w1, NULL
    // 0xa33e88: b.eq            #0xa350dc
    // 0xa33e8c: r0 = GetNavigation.theme()
    //     0xa33e8c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xa33e90: LoadField: r1 = r0->field_3f
    //     0xa33e90: ldur            w1, [x0, #0x3f]
    // 0xa33e94: DecompressPointer r1
    //     0xa33e94: add             x1, x1, HEAP, lsl #32
    // 0xa33e98: LoadField: r0 = r1->field_2b
    //     0xa33e98: ldur            w0, [x1, #0x2b]
    // 0xa33e9c: DecompressPointer r0
    //     0xa33e9c: add             x0, x0, HEAP, lsl #32
    // 0xa33ea0: r16 = 14.000000
    //     0xa33ea0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xa33ea4: ldr             x16, [x16, #0x9a0]
    // 0xa33ea8: stp             x0, x16, [SP]
    // 0xa33eac: ldur            x1, [fp, #-0x20]
    // 0xa33eb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa33eb0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24aa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa33eb4: ldr             x4, [x4, #0xaa0]
    // 0xa33eb8: r0 = copyWith()
    //     0xa33eb8: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa33ebc: stur            x0, [fp, #-0x20]
    // 0xa33ec0: r0 = Text()
    //     0xa33ec0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa33ec4: mov             x1, x0
    // 0xa33ec8: ldur            x0, [fp, #-0x18]
    // 0xa33ecc: stur            x1, [fp, #-0x28]
    // 0xa33ed0: StoreField: r1->field_b = r0
    //     0xa33ed0: stur            w0, [x1, #0xb]
    // 0xa33ed4: ldur            x0, [fp, #-0x20]
    // 0xa33ed8: StoreField: r1->field_13 = r0
    //     0xa33ed8: stur            w0, [x1, #0x13]
    // 0xa33edc: r0 = InkWell()
    //     0xa33edc: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa33ee0: mov             x3, x0
    // 0xa33ee4: ldur            x0, [fp, #-0x28]
    // 0xa33ee8: stur            x3, [fp, #-0x18]
    // 0xa33eec: StoreField: r3->field_b = r0
    //     0xa33eec: stur            w0, [x3, #0xb]
    // 0xa33ef0: ldur            x2, [fp, #-0x10]
    // 0xa33ef4: r1 = Function '<anonymous closure>':.
    //     0xa33ef4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51558] AnonymousClosure: (0xa37044), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa33ef8: ldr             x1, [x1, #0x558]
    // 0xa33efc: r0 = AllocateClosure()
    //     0xa33efc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa33f00: mov             x1, x0
    // 0xa33f04: ldur            x0, [fp, #-0x18]
    // 0xa33f08: StoreField: r0->field_f = r1
    //     0xa33f08: stur            w1, [x0, #0xf]
    // 0xa33f0c: r2 = true
    //     0xa33f0c: add             x2, NULL, #0x20  ; true
    // 0xa33f10: StoreField: r0->field_43 = r2
    //     0xa33f10: stur            w2, [x0, #0x43]
    // 0xa33f14: r3 = Instance_BoxShape
    //     0xa33f14: add             x3, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa33f18: ldr             x3, [x3, #0xca8]
    // 0xa33f1c: StoreField: r0->field_47 = r3
    //     0xa33f1c: stur            w3, [x0, #0x47]
    // 0xa33f20: StoreField: r0->field_6f = r2
    //     0xa33f20: stur            w2, [x0, #0x6f]
    // 0xa33f24: r4 = false
    //     0xa33f24: add             x4, NULL, #0x30  ; false
    // 0xa33f28: StoreField: r0->field_73 = r4
    //     0xa33f28: stur            w4, [x0, #0x73]
    // 0xa33f2c: StoreField: r0->field_83 = r2
    //     0xa33f2c: stur            w2, [x0, #0x83]
    // 0xa33f30: StoreField: r0->field_7b = r4
    //     0xa33f30: stur            w4, [x0, #0x7b]
    // 0xa33f34: ldur            x5, [fp, #-8]
    // 0xa33f38: LoadField: r1 = r5->field_b
    //     0xa33f38: ldur            w1, [x5, #0xb]
    // 0xa33f3c: DecompressPointer r1
    //     0xa33f3c: add             x1, x1, HEAP, lsl #32
    // 0xa33f40: cmp             w1, NULL
    // 0xa33f44: b.eq            #0xa350e0
    // 0xa33f48: LoadField: r6 = r1->field_b
    //     0xa33f48: ldur            w6, [x1, #0xb]
    // 0xa33f4c: DecompressPointer r6
    //     0xa33f4c: add             x6, x6, HEAP, lsl #32
    // 0xa33f50: mov             x1, x6
    // 0xa33f54: r0 = publishedAt()
    //     0xa33f54: bl              #0xa35938  ; [package:nuonline/app/data/models/article.dart] ArticleDetail::publishedAt
    // 0xa33f58: mov             x1, x0
    // 0xa33f5c: r2 = "Minggu"
    //     0xa33f5c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e190] "Minggu"
    //     0xa33f60: ldr             x2, [x2, #0x190]
    // 0xa33f64: r3 = "Ahad"
    //     0xa33f64: add             x3, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xa33f68: ldr             x3, [x3, #0x128]
    // 0xa33f6c: r0 = replaceAll()
    //     0xa33f6c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xa33f70: stur            x0, [fp, #-0x20]
    // 0xa33f74: r0 = GetNavigation.textTheme()
    //     0xa33f74: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa33f78: LoadField: r1 = r0->field_27
    //     0xa33f78: ldur            w1, [x0, #0x27]
    // 0xa33f7c: DecompressPointer r1
    //     0xa33f7c: add             x1, x1, HEAP, lsl #32
    // 0xa33f80: stur            x1, [fp, #-0x28]
    // 0xa33f84: r0 = Text()
    //     0xa33f84: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa33f88: mov             x3, x0
    // 0xa33f8c: ldur            x0, [fp, #-0x20]
    // 0xa33f90: stur            x3, [fp, #-0x30]
    // 0xa33f94: StoreField: r3->field_b = r0
    //     0xa33f94: stur            w0, [x3, #0xb]
    // 0xa33f98: ldur            x0, [fp, #-0x28]
    // 0xa33f9c: StoreField: r3->field_13 = r0
    //     0xa33f9c: stur            w0, [x3, #0x13]
    // 0xa33fa0: r1 = Null
    //     0xa33fa0: mov             x1, NULL
    // 0xa33fa4: r2 = 6
    //     0xa33fa4: movz            x2, #0x6
    // 0xa33fa8: r0 = AllocateArray()
    //     0xa33fa8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa33fac: mov             x2, x0
    // 0xa33fb0: ldur            x0, [fp, #-0x18]
    // 0xa33fb4: stur            x2, [fp, #-0x20]
    // 0xa33fb8: StoreField: r2->field_f = r0
    //     0xa33fb8: stur            w0, [x2, #0xf]
    // 0xa33fbc: r16 = Instance_SizedBox
    //     0xa33fbc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28340] Obj!SizedBox@e1e101
    //     0xa33fc0: ldr             x16, [x16, #0x340]
    // 0xa33fc4: StoreField: r2->field_13 = r16
    //     0xa33fc4: stur            w16, [x2, #0x13]
    // 0xa33fc8: ldur            x0, [fp, #-0x30]
    // 0xa33fcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa33fcc: stur            w0, [x2, #0x17]
    // 0xa33fd0: r1 = <Widget>
    //     0xa33fd0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa33fd4: r0 = AllocateGrowableArray()
    //     0xa33fd4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa33fd8: mov             x1, x0
    // 0xa33fdc: ldur            x0, [fp, #-0x20]
    // 0xa33fe0: stur            x1, [fp, #-0x18]
    // 0xa33fe4: StoreField: r1->field_f = r0
    //     0xa33fe4: stur            w0, [x1, #0xf]
    // 0xa33fe8: r0 = 6
    //     0xa33fe8: movz            x0, #0x6
    // 0xa33fec: StoreField: r1->field_b = r0
    //     0xa33fec: stur            w0, [x1, #0xb]
    // 0xa33ff0: r0 = Row()
    //     0xa33ff0: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa33ff4: mov             x3, x0
    // 0xa33ff8: r0 = Instance_Axis
    //     0xa33ff8: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa33ffc: stur            x3, [fp, #-0x20]
    // 0xa34000: StoreField: r3->field_f = r0
    //     0xa34000: stur            w0, [x3, #0xf]
    // 0xa34004: r4 = Instance_MainAxisAlignment
    //     0xa34004: add             x4, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa34008: ldr             x4, [x4, #0x730]
    // 0xa3400c: StoreField: r3->field_13 = r4
    //     0xa3400c: stur            w4, [x3, #0x13]
    // 0xa34010: r5 = Instance_MainAxisSize
    //     0xa34010: add             x5, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa34014: ldr             x5, [x5, #0x738]
    // 0xa34018: ArrayStore: r3[0] = r5  ; List_4
    //     0xa34018: stur            w5, [x3, #0x17]
    // 0xa3401c: r6 = Instance_CrossAxisAlignment
    //     0xa3401c: add             x6, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa34020: ldr             x6, [x6, #0x740]
    // 0xa34024: StoreField: r3->field_1b = r6
    //     0xa34024: stur            w6, [x3, #0x1b]
    // 0xa34028: r7 = Instance_VerticalDirection
    //     0xa34028: add             x7, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa3402c: ldr             x7, [x7, #0x748]
    // 0xa34030: StoreField: r3->field_23 = r7
    //     0xa34030: stur            w7, [x3, #0x23]
    // 0xa34034: r8 = Instance_Clip
    //     0xa34034: add             x8, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa34038: ldr             x8, [x8, #0x750]
    // 0xa3403c: StoreField: r3->field_2b = r8
    //     0xa3403c: stur            w8, [x3, #0x2b]
    // 0xa34040: StoreField: r3->field_2f = rZR
    //     0xa34040: stur            xzr, [x3, #0x2f]
    // 0xa34044: ldur            x1, [fp, #-0x18]
    // 0xa34048: StoreField: r3->field_b = r1
    //     0xa34048: stur            w1, [x3, #0xb]
    // 0xa3404c: r1 = Null
    //     0xa3404c: mov             x1, NULL
    // 0xa34050: r2 = 2
    //     0xa34050: movz            x2, #0x2
    // 0xa34054: r0 = AllocateArray()
    //     0xa34054: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34058: mov             x2, x0
    // 0xa3405c: ldur            x0, [fp, #-0x20]
    // 0xa34060: stur            x2, [fp, #-0x18]
    // 0xa34064: StoreField: r2->field_f = r0
    //     0xa34064: stur            w0, [x2, #0xf]
    // 0xa34068: r1 = <Widget>
    //     0xa34068: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3406c: r0 = AllocateGrowableArray()
    //     0xa3406c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34070: mov             x1, x0
    // 0xa34074: ldur            x0, [fp, #-0x18]
    // 0xa34078: stur            x1, [fp, #-0x20]
    // 0xa3407c: StoreField: r1->field_f = r0
    //     0xa3407c: stur            w0, [x1, #0xf]
    // 0xa34080: r0 = 2
    //     0xa34080: movz            x0, #0x2
    // 0xa34084: StoreField: r1->field_b = r0
    //     0xa34084: stur            w0, [x1, #0xb]
    // 0xa34088: ldur            x2, [fp, #-8]
    // 0xa3408c: LoadField: r0 = r2->field_b
    //     0xa3408c: ldur            w0, [x2, #0xb]
    // 0xa34090: DecompressPointer r0
    //     0xa34090: add             x0, x0, HEAP, lsl #32
    // 0xa34094: cmp             w0, NULL
    // 0xa34098: b.eq            #0xa350e4
    // 0xa3409c: LoadField: r3 = r0->field_b
    //     0xa3409c: ldur            w3, [x0, #0xb]
    // 0xa340a0: DecompressPointer r3
    //     0xa340a0: add             x3, x3, HEAP, lsl #32
    // 0xa340a4: LoadField: r0 = r3->field_37
    //     0xa340a4: ldur            w0, [x3, #0x37]
    // 0xa340a8: DecompressPointer r0
    //     0xa340a8: add             x0, x0, HEAP, lsl #32
    // 0xa340ac: cmp             w0, NULL
    // 0xa340b0: b.ne            #0xa340bc
    // 0xa340b4: r3 = Null
    //     0xa340b4: mov             x3, NULL
    // 0xa340b8: b               #0xa340d4
    // 0xa340bc: LoadField: r3 = r0->field_7
    //     0xa340bc: ldur            w3, [x0, #7]
    // 0xa340c0: cbnz            w3, #0xa340cc
    // 0xa340c4: r4 = false
    //     0xa340c4: add             x4, NULL, #0x30  ; false
    // 0xa340c8: b               #0xa340d0
    // 0xa340cc: r4 = true
    //     0xa340cc: add             x4, NULL, #0x20  ; true
    // 0xa340d0: mov             x3, x4
    // 0xa340d4: cmp             w3, NULL
    // 0xa340d8: b.eq            #0xa34260
    // 0xa340dc: tbnz            w3, #4, #0xa34260
    // 0xa340e0: cmp             w0, NULL
    // 0xa340e4: b.eq            #0xa350e8
    // 0xa340e8: r3 = LoadClassIdInstr(r0)
    //     0xa340e8: ldur            x3, [x0, #-1]
    //     0xa340ec: ubfx            x3, x3, #0xc, #0x14
    // 0xa340f0: str             x0, [SP]
    // 0xa340f4: mov             x0, x3
    // 0xa340f8: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa340f8: sub             lr, x0, #0xff6
    //     0xa340fc: ldr             lr, [x21, lr, lsl #3]
    //     0xa34100: blr             lr
    // 0xa34104: mov             x1, x0
    // 0xa34108: r2 = "&RSQUO;"
    //     0xa34108: add             x2, PP, #0x38, lsl #12  ; [pp+0x38290] "&RSQUO;"
    //     0xa3410c: ldr             x2, [x2, #0x290]
    // 0xa34110: r3 = "\'"
    //     0xa34110: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xa34114: r0 = replaceAll()
    //     0xa34114: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xa34118: mov             x1, x0
    // 0xa3411c: r2 = "&#039;"
    //     0xa3411c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38298] "&#039;"
    //     0xa34120: ldr             x2, [x2, #0x298]
    // 0xa34124: r3 = "\'"
    //     0xa34124: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xa34128: r0 = replaceAll()
    //     0xa34128: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xa3412c: stur            x0, [fp, #-0x18]
    // 0xa34130: r0 = GetNavigation.textTheme()
    //     0xa34130: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa34134: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa34134: ldur            w1, [x0, #0x17]
    // 0xa34138: DecompressPointer r1
    //     0xa34138: add             x1, x1, HEAP, lsl #32
    // 0xa3413c: stur            x1, [fp, #-0x28]
    // 0xa34140: cmp             w1, NULL
    // 0xa34144: b.eq            #0xa350ec
    // 0xa34148: r0 = GetNavigation.isDarkMode()
    //     0xa34148: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xa3414c: tbnz            w0, #4, #0xa34168
    // 0xa34150: r1 = _ConstMap len:10
    //     0xa34150: add             x1, PP, #0x29, lsl #12  ; [pp+0x296c8] Map<int, Color>(10)
    //     0xa34154: ldr             x1, [x1, #0x6c8]
    // 0xa34158: r2 = 600
    //     0xa34158: movz            x2, #0x258
    // 0xa3415c: r0 = []()
    //     0xa3415c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xa34160: mov             x1, x0
    // 0xa34164: b               #0xa34170
    // 0xa34168: r1 = Instance_MaterialColor
    //     0xa34168: add             x1, PP, #0x29, lsl #12  ; [pp+0x296d0] Obj!MaterialColor@e2bbb1
    //     0xa3416c: ldr             x1, [x1, #0x6d0]
    // 0xa34170: ldur            x0, [fp, #-0x18]
    // 0xa34174: r16 = 14.000000
    //     0xa34174: add             x16, PP, #0xb, lsl #12  ; [pp+0xb9a0] 14
    //     0xa34178: ldr             x16, [x16, #0x9a0]
    // 0xa3417c: r30 = Instance_FontWeight
    //     0xa3417c: add             lr, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xa34180: ldr             lr, [lr, #0xe20]
    // 0xa34184: stp             lr, x16, [SP, #8]
    // 0xa34188: str             x1, [SP]
    // 0xa3418c: ldur            x1, [fp, #-0x28]
    // 0xa34190: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x1, fontWeight, 0x2, null]
    //     0xa34190: add             x4, PP, #0x31, lsl #12  ; [pp+0x31588] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0xa34194: ldr             x4, [x4, #0x588]
    // 0xa34198: r0 = copyWith()
    //     0xa34198: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa3419c: stur            x0, [fp, #-0x28]
    // 0xa341a0: r0 = Text()
    //     0xa341a0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa341a4: mov             x1, x0
    // 0xa341a8: ldur            x0, [fp, #-0x18]
    // 0xa341ac: stur            x1, [fp, #-0x30]
    // 0xa341b0: StoreField: r1->field_b = r0
    //     0xa341b0: stur            w0, [x1, #0xb]
    // 0xa341b4: ldur            x0, [fp, #-0x28]
    // 0xa341b8: StoreField: r1->field_13 = r0
    //     0xa341b8: stur            w0, [x1, #0x13]
    // 0xa341bc: r0 = InkWell()
    //     0xa341bc: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa341c0: mov             x3, x0
    // 0xa341c4: ldur            x0, [fp, #-0x30]
    // 0xa341c8: stur            x3, [fp, #-0x18]
    // 0xa341cc: StoreField: r3->field_b = r0
    //     0xa341cc: stur            w0, [x3, #0xb]
    // 0xa341d0: ldur            x2, [fp, #-0x10]
    // 0xa341d4: r1 = Function '<anonymous closure>':.
    //     0xa341d4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51560] AnonymousClosure: (0xa36f74), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa341d8: ldr             x1, [x1, #0x560]
    // 0xa341dc: r0 = AllocateClosure()
    //     0xa341dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa341e0: mov             x1, x0
    // 0xa341e4: ldur            x0, [fp, #-0x18]
    // 0xa341e8: StoreField: r0->field_f = r1
    //     0xa341e8: stur            w1, [x0, #0xf]
    // 0xa341ec: r3 = true
    //     0xa341ec: add             x3, NULL, #0x20  ; true
    // 0xa341f0: StoreField: r0->field_43 = r3
    //     0xa341f0: stur            w3, [x0, #0x43]
    // 0xa341f4: r4 = Instance_BoxShape
    //     0xa341f4: add             x4, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa341f8: ldr             x4, [x4, #0xca8]
    // 0xa341fc: StoreField: r0->field_47 = r4
    //     0xa341fc: stur            w4, [x0, #0x47]
    // 0xa34200: StoreField: r0->field_6f = r3
    //     0xa34200: stur            w3, [x0, #0x6f]
    // 0xa34204: r5 = false
    //     0xa34204: add             x5, NULL, #0x30  ; false
    // 0xa34208: StoreField: r0->field_73 = r5
    //     0xa34208: stur            w5, [x0, #0x73]
    // 0xa3420c: StoreField: r0->field_83 = r3
    //     0xa3420c: stur            w3, [x0, #0x83]
    // 0xa34210: StoreField: r0->field_7b = r5
    //     0xa34210: stur            w5, [x0, #0x7b]
    // 0xa34214: r1 = Null
    //     0xa34214: mov             x1, NULL
    // 0xa34218: r2 = 4
    //     0xa34218: movz            x2, #0x4
    // 0xa3421c: r0 = AllocateArray()
    //     0xa3421c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34220: stur            x0, [fp, #-0x28]
    // 0xa34224: r16 = Instance_SizedBox
    //     0xa34224: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xa34228: ldr             x16, [x16, #0x950]
    // 0xa3422c: StoreField: r0->field_f = r16
    //     0xa3422c: stur            w16, [x0, #0xf]
    // 0xa34230: ldur            x1, [fp, #-0x18]
    // 0xa34234: StoreField: r0->field_13 = r1
    //     0xa34234: stur            w1, [x0, #0x13]
    // 0xa34238: r1 = <Widget>
    //     0xa34238: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa3423c: r0 = AllocateGrowableArray()
    //     0xa3423c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34240: mov             x1, x0
    // 0xa34244: ldur            x0, [fp, #-0x28]
    // 0xa34248: StoreField: r1->field_f = r0
    //     0xa34248: stur            w0, [x1, #0xf]
    // 0xa3424c: r0 = 4
    //     0xa3424c: movz            x0, #0x4
    // 0xa34250: StoreField: r1->field_b = r0
    //     0xa34250: stur            w0, [x1, #0xb]
    // 0xa34254: mov             x2, x1
    // 0xa34258: ldur            x1, [fp, #-0x20]
    // 0xa3425c: r0 = addAll()
    //     0xa3425c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa34260: ldur            x0, [fp, #-0x20]
    // 0xa34264: LoadField: r1 = r0->field_b
    //     0xa34264: ldur            w1, [x0, #0xb]
    // 0xa34268: LoadField: r2 = r0->field_f
    //     0xa34268: ldur            w2, [x0, #0xf]
    // 0xa3426c: DecompressPointer r2
    //     0xa3426c: add             x2, x2, HEAP, lsl #32
    // 0xa34270: LoadField: r3 = r2->field_b
    //     0xa34270: ldur            w3, [x2, #0xb]
    // 0xa34274: r2 = LoadInt32Instr(r1)
    //     0xa34274: sbfx            x2, x1, #1, #0x1f
    // 0xa34278: stur            x2, [fp, #-0x38]
    // 0xa3427c: r1 = LoadInt32Instr(r3)
    //     0xa3427c: sbfx            x1, x3, #1, #0x1f
    // 0xa34280: cmp             x2, x1
    // 0xa34284: b.ne            #0xa34290
    // 0xa34288: mov             x1, x0
    // 0xa3428c: r0 = _growToNextCapacity()
    //     0xa3428c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa34290: ldur            x0, [fp, #-0x20]
    // 0xa34294: ldur            x1, [fp, #-0x38]
    // 0xa34298: add             x3, x1, #1
    // 0xa3429c: stur            x3, [fp, #-0x40]
    // 0xa342a0: lsl             x2, x3, #1
    // 0xa342a4: StoreField: r0->field_b = r2
    //     0xa342a4: stur            w2, [x0, #0xb]
    // 0xa342a8: LoadField: r4 = r0->field_f
    //     0xa342a8: ldur            w4, [x0, #0xf]
    // 0xa342ac: DecompressPointer r4
    //     0xa342ac: add             x4, x4, HEAP, lsl #32
    // 0xa342b0: stur            x4, [fp, #-0x18]
    // 0xa342b4: add             x2, x4, x1, lsl #2
    // 0xa342b8: r16 = Instance_SizedBox
    //     0xa342b8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24950] Obj!SizedBox@e1e0e1
    //     0xa342bc: ldr             x16, [x16, #0x950]
    // 0xa342c0: StoreField: r2->field_f = r16
    //     0xa342c0: stur            w16, [x2, #0xf]
    // 0xa342c4: ldur            x2, [fp, #-0x10]
    // 0xa342c8: r1 = Function '<anonymous closure>':.
    //     0xa342c8: add             x1, PP, #0x51, lsl #12  ; [pp+0x51568] AnonymousClosure: (0xa36e5c), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa342cc: ldr             x1, [x1, #0x568]
    // 0xa342d0: r0 = AllocateClosure()
    //     0xa342d0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa342d4: r1 = <ReadingPreferenceController>
    //     0xa342d4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xa342d8: ldr             x1, [x1, #0xe10]
    // 0xa342dc: stur            x0, [fp, #-0x28]
    // 0xa342e0: r0 = ReadingPreferenceWidget()
    //     0xa342e0: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xa342e4: mov             x2, x0
    // 0xa342e8: ldur            x0, [fp, #-0x28]
    // 0xa342ec: stur            x2, [fp, #-0x30]
    // 0xa342f0: StoreField: r2->field_13 = r0
    //     0xa342f0: stur            w0, [x2, #0x13]
    // 0xa342f4: ldur            x0, [fp, #-0x18]
    // 0xa342f8: LoadField: r1 = r0->field_b
    //     0xa342f8: ldur            w1, [x0, #0xb]
    // 0xa342fc: r0 = LoadInt32Instr(r1)
    //     0xa342fc: sbfx            x0, x1, #1, #0x1f
    // 0xa34300: ldur            x3, [fp, #-0x40]
    // 0xa34304: cmp             x3, x0
    // 0xa34308: b.ne            #0xa34314
    // 0xa3430c: ldur            x1, [fp, #-0x20]
    // 0xa34310: r0 = _growToNextCapacity()
    //     0xa34310: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa34314: ldur            x4, [fp, #-8]
    // 0xa34318: ldur            x2, [fp, #-0x40]
    // 0xa3431c: ldur            x3, [fp, #-0x20]
    // 0xa34320: add             x0, x2, #1
    // 0xa34324: lsl             x1, x0, #1
    // 0xa34328: StoreField: r3->field_b = r1
    //     0xa34328: stur            w1, [x3, #0xb]
    // 0xa3432c: LoadField: r1 = r3->field_f
    //     0xa3432c: ldur            w1, [x3, #0xf]
    // 0xa34330: DecompressPointer r1
    //     0xa34330: add             x1, x1, HEAP, lsl #32
    // 0xa34334: ldur            x0, [fp, #-0x30]
    // 0xa34338: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa34338: add             x25, x1, x2, lsl #2
    //     0xa3433c: add             x25, x25, #0xf
    //     0xa34340: str             w0, [x25]
    //     0xa34344: tbz             w0, #0, #0xa34360
    //     0xa34348: ldurb           w16, [x1, #-1]
    //     0xa3434c: ldurb           w17, [x0, #-1]
    //     0xa34350: and             x16, x17, x16, lsr #2
    //     0xa34354: tst             x16, HEAP, lsr #32
    //     0xa34358: b.eq            #0xa34360
    //     0xa3435c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa34360: LoadField: r0 = r4->field_b
    //     0xa34360: ldur            w0, [x4, #0xb]
    // 0xa34364: DecompressPointer r0
    //     0xa34364: add             x0, x0, HEAP, lsl #32
    // 0xa34368: cmp             w0, NULL
    // 0xa3436c: b.eq            #0xa350f0
    // 0xa34370: LoadField: r1 = r0->field_b
    //     0xa34370: ldur            w1, [x0, #0xb]
    // 0xa34374: DecompressPointer r1
    //     0xa34374: add             x1, x1, HEAP, lsl #32
    // 0xa34378: LoadField: r0 = r1->field_33
    //     0xa34378: ldur            w0, [x1, #0x33]
    // 0xa3437c: DecompressPointer r0
    //     0xa3437c: add             x0, x0, HEAP, lsl #32
    // 0xa34380: stur            x0, [fp, #-0x18]
    // 0xa34384: cmp             w0, NULL
    // 0xa34388: b.eq            #0xa3463c
    // 0xa3438c: r0 = Radius()
    //     0xa3438c: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa34390: d0 = 20.000000
    //     0xa34390: fmov            d0, #20.00000000
    // 0xa34394: stur            x0, [fp, #-0x28]
    // 0xa34398: StoreField: r0->field_7 = d0
    //     0xa34398: stur            d0, [x0, #7]
    // 0xa3439c: StoreField: r0->field_f = d0
    //     0xa3439c: stur            d0, [x0, #0xf]
    // 0xa343a0: r0 = BorderRadius()
    //     0xa343a0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa343a4: mov             x1, x0
    // 0xa343a8: ldur            x0, [fp, #-0x28]
    // 0xa343ac: stur            x1, [fp, #-0x30]
    // 0xa343b0: StoreField: r1->field_7 = r0
    //     0xa343b0: stur            w0, [x1, #7]
    // 0xa343b4: StoreField: r1->field_b = r0
    //     0xa343b4: stur            w0, [x1, #0xb]
    // 0xa343b8: StoreField: r1->field_f = r0
    //     0xa343b8: stur            w0, [x1, #0xf]
    // 0xa343bc: StoreField: r1->field_13 = r0
    //     0xa343bc: stur            w0, [x1, #0x13]
    // 0xa343c0: ldur            x0, [fp, #-0x18]
    // 0xa343c4: LoadField: r2 = r0->field_13
    //     0xa343c4: ldur            w2, [x0, #0x13]
    // 0xa343c8: DecompressPointer r2
    //     0xa343c8: add             x2, x2, HEAP, lsl #32
    // 0xa343cc: stur            x2, [fp, #-0x28]
    // 0xa343d0: r0 = NFadeInImageNetwork()
    //     0xa343d0: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xa343d4: mov             x1, x0
    // 0xa343d8: ldur            x0, [fp, #-0x28]
    // 0xa343dc: stur            x1, [fp, #-0x48]
    // 0xa343e0: StoreField: r1->field_b = r0
    //     0xa343e0: stur            w0, [x1, #0xb]
    // 0xa343e4: r0 = "packages/nuikit/assets/images/icons/defaultPicture.svg"
    //     0xa343e4: add             x0, PP, #0x41, lsl #12  ; [pp+0x41218] "packages/nuikit/assets/images/icons/defaultPicture.svg"
    //     0xa343e8: ldr             x0, [x0, #0x218]
    // 0xa343ec: StoreField: r1->field_f = r0
    //     0xa343ec: stur            w0, [x1, #0xf]
    // 0xa343f0: StoreField: r1->field_13 = r0
    //     0xa343f0: stur            w0, [x1, #0x13]
    // 0xa343f4: r0 = Instance_BoxFit
    //     0xa343f4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xa343f8: ldr             x0, [x0, #0xa28]
    // 0xa343fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa343fc: stur            w0, [x1, #0x17]
    // 0xa34400: r0 = ClipRRect()
    //     0xa34400: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa34404: mov             x1, x0
    // 0xa34408: ldur            x0, [fp, #-0x30]
    // 0xa3440c: stur            x1, [fp, #-0x28]
    // 0xa34410: StoreField: r1->field_f = r0
    //     0xa34410: stur            w0, [x1, #0xf]
    // 0xa34414: r0 = Instance_Clip
    //     0xa34414: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xa34418: ldr             x0, [x0, #0x4f8]
    // 0xa3441c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3441c: stur            w0, [x1, #0x17]
    // 0xa34420: ldur            x2, [fp, #-0x48]
    // 0xa34424: StoreField: r1->field_b = r2
    //     0xa34424: stur            w2, [x1, #0xb]
    // 0xa34428: r0 = SizedBox()
    //     0xa34428: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa3442c: mov             x4, x0
    // 0xa34430: r0 = 20.000000
    //     0xa34430: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d430] 20
    //     0xa34434: ldr             x0, [x0, #0x430]
    // 0xa34438: stur            x4, [fp, #-0x30]
    // 0xa3443c: StoreField: r4->field_f = r0
    //     0xa3443c: stur            w0, [x4, #0xf]
    // 0xa34440: StoreField: r4->field_13 = r0
    //     0xa34440: stur            w0, [x4, #0x13]
    // 0xa34444: ldur            x0, [fp, #-0x28]
    // 0xa34448: StoreField: r4->field_b = r0
    //     0xa34448: stur            w0, [x4, #0xb]
    // 0xa3444c: ldur            x0, [fp, #-0x18]
    // 0xa34450: LoadField: r1 = r0->field_f
    //     0xa34450: ldur            w1, [x0, #0xf]
    // 0xa34454: DecompressPointer r1
    //     0xa34454: add             x1, x1, HEAP, lsl #32
    // 0xa34458: r2 = "&RSQUO;"
    //     0xa34458: add             x2, PP, #0x38, lsl #12  ; [pp+0x38290] "&RSQUO;"
    //     0xa3445c: ldr             x2, [x2, #0x290]
    // 0xa34460: r3 = "\'"
    //     0xa34460: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xa34464: r0 = replaceAll()
    //     0xa34464: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xa34468: mov             x1, x0
    // 0xa3446c: r2 = "&#039;"
    //     0xa3446c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38298] "&#039;"
    //     0xa34470: ldr             x2, [x2, #0x298]
    // 0xa34474: r3 = "\'"
    //     0xa34474: ldr             x3, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xa34478: r0 = replaceAll()
    //     0xa34478: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xa3447c: stur            x0, [fp, #-0x18]
    // 0xa34480: r0 = Text()
    //     0xa34480: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa34484: mov             x1, x0
    // 0xa34488: ldur            x0, [fp, #-0x18]
    // 0xa3448c: stur            x1, [fp, #-0x28]
    // 0xa34490: StoreField: r1->field_b = r0
    //     0xa34490: stur            w0, [x1, #0xb]
    // 0xa34494: r0 = GetNavigation.theme()
    //     0xa34494: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xa34498: LoadField: r1 = r0->field_3f
    //     0xa34498: ldur            w1, [x0, #0x3f]
    // 0xa3449c: DecompressPointer r1
    //     0xa3449c: add             x1, x1, HEAP, lsl #32
    // 0xa344a0: LoadField: r0 = r1->field_2b
    //     0xa344a0: ldur            w0, [x1, #0x2b]
    // 0xa344a4: DecompressPointer r0
    //     0xa344a4: add             x0, x0, HEAP, lsl #32
    // 0xa344a8: stur            x0, [fp, #-0x18]
    // 0xa344ac: r0 = Icon()
    //     0xa344ac: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xa344b0: mov             x3, x0
    // 0xa344b4: r0 = Instance_IconData
    //     0xa344b4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32410] Obj!IconData@e10071
    //     0xa344b8: ldr             x0, [x0, #0x410]
    // 0xa344bc: stur            x3, [fp, #-0x48]
    // 0xa344c0: StoreField: r3->field_b = r0
    //     0xa344c0: stur            w0, [x3, #0xb]
    // 0xa344c4: r0 = 16.000000
    //     0xa344c4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xa344c8: ldr             x0, [x0, #0x80]
    // 0xa344cc: StoreField: r3->field_f = r0
    //     0xa344cc: stur            w0, [x3, #0xf]
    // 0xa344d0: ldur            x0, [fp, #-0x18]
    // 0xa344d4: StoreField: r3->field_23 = r0
    //     0xa344d4: stur            w0, [x3, #0x23]
    // 0xa344d8: r1 = Null
    //     0xa344d8: mov             x1, NULL
    // 0xa344dc: r2 = 10
    //     0xa344dc: movz            x2, #0xa
    // 0xa344e0: r0 = AllocateArray()
    //     0xa344e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa344e4: mov             x2, x0
    // 0xa344e8: ldur            x0, [fp, #-0x30]
    // 0xa344ec: stur            x2, [fp, #-0x18]
    // 0xa344f0: StoreField: r2->field_f = r0
    //     0xa344f0: stur            w0, [x2, #0xf]
    // 0xa344f4: r16 = Instance_SizedBox
    //     0xa344f4: add             x16, PP, #0x29, lsl #12  ; [pp+0x29538] Obj!SizedBox@e1e0c1
    //     0xa344f8: ldr             x16, [x16, #0x538]
    // 0xa344fc: StoreField: r2->field_13 = r16
    //     0xa344fc: stur            w16, [x2, #0x13]
    // 0xa34500: ldur            x0, [fp, #-0x28]
    // 0xa34504: ArrayStore: r2[0] = r0  ; List_4
    //     0xa34504: stur            w0, [x2, #0x17]
    // 0xa34508: r16 = Instance_SizedBox
    //     0xa34508: add             x16, PP, #0x27, lsl #12  ; [pp+0x27bd8] Obj!SizedBox@e1e0a1
    //     0xa3450c: ldr             x16, [x16, #0xbd8]
    // 0xa34510: StoreField: r2->field_1b = r16
    //     0xa34510: stur            w16, [x2, #0x1b]
    // 0xa34514: ldur            x0, [fp, #-0x48]
    // 0xa34518: StoreField: r2->field_1f = r0
    //     0xa34518: stur            w0, [x2, #0x1f]
    // 0xa3451c: r1 = <Widget>
    //     0xa3451c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34520: r0 = AllocateGrowableArray()
    //     0xa34520: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34524: mov             x1, x0
    // 0xa34528: ldur            x0, [fp, #-0x18]
    // 0xa3452c: stur            x1, [fp, #-0x28]
    // 0xa34530: StoreField: r1->field_f = r0
    //     0xa34530: stur            w0, [x1, #0xf]
    // 0xa34534: r0 = 10
    //     0xa34534: movz            x0, #0xa
    // 0xa34538: StoreField: r1->field_b = r0
    //     0xa34538: stur            w0, [x1, #0xb]
    // 0xa3453c: r0 = Row()
    //     0xa3453c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa34540: mov             x1, x0
    // 0xa34544: r0 = Instance_Axis
    //     0xa34544: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa34548: stur            x1, [fp, #-0x18]
    // 0xa3454c: StoreField: r1->field_f = r0
    //     0xa3454c: stur            w0, [x1, #0xf]
    // 0xa34550: r2 = Instance_MainAxisAlignment
    //     0xa34550: add             x2, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa34554: ldr             x2, [x2, #0x730]
    // 0xa34558: StoreField: r1->field_13 = r2
    //     0xa34558: stur            w2, [x1, #0x13]
    // 0xa3455c: r2 = Instance_MainAxisSize
    //     0xa3455c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa34560: ldr             x2, [x2, #0x738]
    // 0xa34564: ArrayStore: r1[0] = r2  ; List_4
    //     0xa34564: stur            w2, [x1, #0x17]
    // 0xa34568: r3 = Instance_CrossAxisAlignment
    //     0xa34568: add             x3, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa3456c: ldr             x3, [x3, #0x740]
    // 0xa34570: StoreField: r1->field_1b = r3
    //     0xa34570: stur            w3, [x1, #0x1b]
    // 0xa34574: r4 = Instance_VerticalDirection
    //     0xa34574: add             x4, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa34578: ldr             x4, [x4, #0x748]
    // 0xa3457c: StoreField: r1->field_23 = r4
    //     0xa3457c: stur            w4, [x1, #0x23]
    // 0xa34580: r5 = Instance_Clip
    //     0xa34580: add             x5, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa34584: ldr             x5, [x5, #0x750]
    // 0xa34588: StoreField: r1->field_2b = r5
    //     0xa34588: stur            w5, [x1, #0x2b]
    // 0xa3458c: StoreField: r1->field_2f = rZR
    //     0xa3458c: stur            xzr, [x1, #0x2f]
    // 0xa34590: ldur            x6, [fp, #-0x28]
    // 0xa34594: StoreField: r1->field_b = r6
    //     0xa34594: stur            w6, [x1, #0xb]
    // 0xa34598: r0 = InkWell()
    //     0xa34598: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa3459c: mov             x3, x0
    // 0xa345a0: ldur            x0, [fp, #-0x18]
    // 0xa345a4: stur            x3, [fp, #-0x28]
    // 0xa345a8: StoreField: r3->field_b = r0
    //     0xa345a8: stur            w0, [x3, #0xb]
    // 0xa345ac: ldur            x2, [fp, #-0x10]
    // 0xa345b0: r1 = Function '<anonymous closure>':.
    //     0xa345b0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51570] AnonymousClosure: (0xa36d5c), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa345b4: ldr             x1, [x1, #0x570]
    // 0xa345b8: r0 = AllocateClosure()
    //     0xa345b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa345bc: mov             x1, x0
    // 0xa345c0: ldur            x0, [fp, #-0x28]
    // 0xa345c4: StoreField: r0->field_f = r1
    //     0xa345c4: stur            w1, [x0, #0xf]
    // 0xa345c8: r3 = true
    //     0xa345c8: add             x3, NULL, #0x20  ; true
    // 0xa345cc: StoreField: r0->field_43 = r3
    //     0xa345cc: stur            w3, [x0, #0x43]
    // 0xa345d0: r4 = Instance_BoxShape
    //     0xa345d0: add             x4, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa345d4: ldr             x4, [x4, #0xca8]
    // 0xa345d8: StoreField: r0->field_47 = r4
    //     0xa345d8: stur            w4, [x0, #0x47]
    // 0xa345dc: StoreField: r0->field_6f = r3
    //     0xa345dc: stur            w3, [x0, #0x6f]
    // 0xa345e0: r5 = false
    //     0xa345e0: add             x5, NULL, #0x30  ; false
    // 0xa345e4: StoreField: r0->field_73 = r5
    //     0xa345e4: stur            w5, [x0, #0x73]
    // 0xa345e8: StoreField: r0->field_83 = r3
    //     0xa345e8: stur            w3, [x0, #0x83]
    // 0xa345ec: StoreField: r0->field_7b = r5
    //     0xa345ec: stur            w5, [x0, #0x7b]
    // 0xa345f0: r1 = Null
    //     0xa345f0: mov             x1, NULL
    // 0xa345f4: r2 = 4
    //     0xa345f4: movz            x2, #0x4
    // 0xa345f8: r0 = AllocateArray()
    //     0xa345f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa345fc: stur            x0, [fp, #-0x18]
    // 0xa34600: r16 = Instance_SizedBox
    //     0xa34600: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xa34604: ldr             x16, [x16, #0xfe8]
    // 0xa34608: StoreField: r0->field_f = r16
    //     0xa34608: stur            w16, [x0, #0xf]
    // 0xa3460c: ldur            x1, [fp, #-0x28]
    // 0xa34610: StoreField: r0->field_13 = r1
    //     0xa34610: stur            w1, [x0, #0x13]
    // 0xa34614: r1 = <Widget>
    //     0xa34614: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34618: r0 = AllocateGrowableArray()
    //     0xa34618: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa3461c: mov             x1, x0
    // 0xa34620: ldur            x0, [fp, #-0x18]
    // 0xa34624: StoreField: r1->field_f = r0
    //     0xa34624: stur            w0, [x1, #0xf]
    // 0xa34628: r0 = 4
    //     0xa34628: movz            x0, #0x4
    // 0xa3462c: StoreField: r1->field_b = r0
    //     0xa3462c: stur            w0, [x1, #0xb]
    // 0xa34630: mov             x2, x1
    // 0xa34634: ldur            x1, [fp, #-0x20]
    // 0xa34638: r0 = addAll()
    //     0xa34638: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa3463c: ldur            x0, [fp, #-0x20]
    // 0xa34640: LoadField: r1 = r0->field_b
    //     0xa34640: ldur            w1, [x0, #0xb]
    // 0xa34644: LoadField: r2 = r0->field_f
    //     0xa34644: ldur            w2, [x0, #0xf]
    // 0xa34648: DecompressPointer r2
    //     0xa34648: add             x2, x2, HEAP, lsl #32
    // 0xa3464c: LoadField: r3 = r2->field_b
    //     0xa3464c: ldur            w3, [x2, #0xb]
    // 0xa34650: r2 = LoadInt32Instr(r1)
    //     0xa34650: sbfx            x2, x1, #1, #0x1f
    // 0xa34654: stur            x2, [fp, #-0x38]
    // 0xa34658: r1 = LoadInt32Instr(r3)
    //     0xa34658: sbfx            x1, x3, #1, #0x1f
    // 0xa3465c: cmp             x2, x1
    // 0xa34660: b.ne            #0xa3466c
    // 0xa34664: mov             x1, x0
    // 0xa34668: r0 = _growToNextCapacity()
    //     0xa34668: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3466c: ldur            x2, [fp, #-8]
    // 0xa34670: ldur            x1, [fp, #-0x20]
    // 0xa34674: ldur            x0, [fp, #-0x38]
    // 0xa34678: add             x3, x0, #1
    // 0xa3467c: stur            x3, [fp, #-0x40]
    // 0xa34680: lsl             x4, x3, #1
    // 0xa34684: StoreField: r1->field_b = r4
    //     0xa34684: stur            w4, [x1, #0xb]
    // 0xa34688: LoadField: r4 = r1->field_f
    //     0xa34688: ldur            w4, [x1, #0xf]
    // 0xa3468c: DecompressPointer r4
    //     0xa3468c: add             x4, x4, HEAP, lsl #32
    // 0xa34690: stur            x4, [fp, #-0x18]
    // 0xa34694: add             x5, x4, x0, lsl #2
    // 0xa34698: r16 = Instance_SizedBox
    //     0xa34698: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xa3469c: ldr             x16, [x16, #0x448]
    // 0xa346a0: StoreField: r5->field_f = r16
    //     0xa346a0: stur            w16, [x5, #0xf]
    // 0xa346a4: r0 = Radius()
    //     0xa346a4: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa346a8: d0 = 8.000000
    //     0xa346a8: fmov            d0, #8.00000000
    // 0xa346ac: stur            x0, [fp, #-0x28]
    // 0xa346b0: StoreField: r0->field_7 = d0
    //     0xa346b0: stur            d0, [x0, #7]
    // 0xa346b4: StoreField: r0->field_f = d0
    //     0xa346b4: stur            d0, [x0, #0xf]
    // 0xa346b8: r0 = BorderRadius()
    //     0xa346b8: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa346bc: mov             x1, x0
    // 0xa346c0: ldur            x0, [fp, #-0x28]
    // 0xa346c4: stur            x1, [fp, #-0x30]
    // 0xa346c8: StoreField: r1->field_7 = r0
    //     0xa346c8: stur            w0, [x1, #7]
    // 0xa346cc: StoreField: r1->field_b = r0
    //     0xa346cc: stur            w0, [x1, #0xb]
    // 0xa346d0: StoreField: r1->field_f = r0
    //     0xa346d0: stur            w0, [x1, #0xf]
    // 0xa346d4: StoreField: r1->field_13 = r0
    //     0xa346d4: stur            w0, [x1, #0x13]
    // 0xa346d8: ldur            x2, [fp, #-8]
    // 0xa346dc: LoadField: r0 = r2->field_b
    //     0xa346dc: ldur            w0, [x2, #0xb]
    // 0xa346e0: DecompressPointer r0
    //     0xa346e0: add             x0, x0, HEAP, lsl #32
    // 0xa346e4: cmp             w0, NULL
    // 0xa346e8: b.eq            #0xa350f4
    // 0xa346ec: LoadField: r3 = r0->field_b
    //     0xa346ec: ldur            w3, [x0, #0xb]
    // 0xa346f0: DecompressPointer r3
    //     0xa346f0: add             x3, x3, HEAP, lsl #32
    // 0xa346f4: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa346f4: ldur            w0, [x3, #0x17]
    // 0xa346f8: DecompressPointer r0
    //     0xa346f8: add             x0, x0, HEAP, lsl #32
    // 0xa346fc: LoadField: r3 = r0->field_f
    //     0xa346fc: ldur            w3, [x0, #0xf]
    // 0xa34700: DecompressPointer r3
    //     0xa34700: add             x3, x3, HEAP, lsl #32
    // 0xa34704: stur            x3, [fp, #-0x28]
    // 0xa34708: r0 = NFadeInImageNetwork()
    //     0xa34708: bl              #0xa32b20  ; AllocateNFadeInImageNetworkStub -> NFadeInImageNetwork (size=0x20)
    // 0xa3470c: mov             x1, x0
    // 0xa34710: ldur            x0, [fp, #-0x28]
    // 0xa34714: stur            x1, [fp, #-0x48]
    // 0xa34718: StoreField: r1->field_b = r0
    //     0xa34718: stur            w0, [x1, #0xb]
    // 0xa3471c: r0 = "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xa3471c: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a18] "packages/nuikit/assets/images/icons/image_slide_load_light.png"
    //     0xa34720: ldr             x0, [x0, #0xa18]
    // 0xa34724: StoreField: r1->field_f = r0
    //     0xa34724: stur            w0, [x1, #0xf]
    // 0xa34728: r0 = "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xa34728: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a20] "packages/nuikit/assets/images/icons/image_slide_load_dark.png"
    //     0xa3472c: ldr             x0, [x0, #0xa20]
    // 0xa34730: StoreField: r1->field_13 = r0
    //     0xa34730: stur            w0, [x1, #0x13]
    // 0xa34734: r0 = Instance_BoxFit
    //     0xa34734: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a28] Obj!BoxFit@e35d61
    //     0xa34738: ldr             x0, [x0, #0xa28]
    // 0xa3473c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3473c: stur            w0, [x1, #0x17]
    // 0xa34740: r0 = ClipRRect()
    //     0xa34740: bl              #0xa2f04c  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa34744: mov             x1, x0
    // 0xa34748: ldur            x0, [fp, #-0x30]
    // 0xa3474c: stur            x1, [fp, #-0x28]
    // 0xa34750: StoreField: r1->field_f = r0
    //     0xa34750: stur            w0, [x1, #0xf]
    // 0xa34754: r0 = Instance_Clip
    //     0xa34754: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d4f8] Obj!Clip@e39b21
    //     0xa34758: ldr             x0, [x0, #0x4f8]
    // 0xa3475c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3475c: stur            w0, [x1, #0x17]
    // 0xa34760: ldur            x0, [fp, #-0x48]
    // 0xa34764: StoreField: r1->field_b = r0
    //     0xa34764: stur            w0, [x1, #0xb]
    // 0xa34768: r0 = AspectRatio()
    //     0xa34768: bl              #0x9d2c98  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa3476c: d0 = 1.777778
    //     0xa3476c: add             x17, PP, #0x38, lsl #12  ; [pp+0x38120] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0xa34770: ldr             d0, [x17, #0x120]
    // 0xa34774: stur            x0, [fp, #-0x30]
    // 0xa34778: StoreField: r0->field_f = d0
    //     0xa34778: stur            d0, [x0, #0xf]
    // 0xa3477c: ldur            x1, [fp, #-0x28]
    // 0xa34780: StoreField: r0->field_b = r1
    //     0xa34780: stur            w1, [x0, #0xb]
    // 0xa34784: r0 = IconButton()
    //     0xa34784: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xa34788: mov             x3, x0
    // 0xa3478c: r0 = Instance_EdgeInsets
    //     0xa3478c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xa34790: ldr             x0, [x0, #0x768]
    // 0xa34794: stur            x3, [fp, #-0x28]
    // 0xa34798: StoreField: r3->field_13 = r0
    //     0xa34798: stur            w0, [x3, #0x13]
    // 0xa3479c: ldur            x2, [fp, #-8]
    // 0xa347a0: r1 = Function '_toggleCaption@1872489413':.
    //     0xa347a0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51578] AnonymousClosure: (0xa36cc0), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::_toggleCaption (0xa36cf8)
    //     0xa347a4: ldr             x1, [x1, #0x578]
    // 0xa347a8: r0 = AllocateClosure()
    //     0xa347a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa347ac: mov             x1, x0
    // 0xa347b0: ldur            x0, [fp, #-0x28]
    // 0xa347b4: StoreField: r0->field_3b = r1
    //     0xa347b4: stur            w1, [x0, #0x3b]
    // 0xa347b8: r1 = false
    //     0xa347b8: add             x1, NULL, #0x30  ; false
    // 0xa347bc: StoreField: r0->field_47 = r1
    //     0xa347bc: stur            w1, [x0, #0x47]
    // 0xa347c0: r2 = Instance_Icon
    //     0xa347c0: add             x2, PP, #0x41, lsl #12  ; [pp+0x41488] Obj!Icon@e240b1
    //     0xa347c4: ldr             x2, [x2, #0x488]
    // 0xa347c8: StoreField: r0->field_1f = r2
    //     0xa347c8: stur            w2, [x0, #0x1f]
    // 0xa347cc: r2 = Instance__IconButtonVariant
    //     0xa347cc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xa347d0: ldr             x2, [x2, #0xf78]
    // 0xa347d4: StoreField: r0->field_63 = r2
    //     0xa347d4: stur            w2, [x0, #0x63]
    // 0xa347d8: r0 = Align()
    //     0xa347d8: bl              #0x9d4ff8  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa347dc: mov             x2, x0
    // 0xa347e0: r0 = Instance_Alignment
    //     0xa347e0: add             x0, PP, #0x28, lsl #12  ; [pp+0x281e0] Obj!Alignment@e13e31
    //     0xa347e4: ldr             x0, [x0, #0x1e0]
    // 0xa347e8: stur            x2, [fp, #-0x48]
    // 0xa347ec: StoreField: r2->field_f = r0
    //     0xa347ec: stur            w0, [x2, #0xf]
    // 0xa347f0: ldur            x0, [fp, #-0x28]
    // 0xa347f4: StoreField: r2->field_b = r0
    //     0xa347f4: stur            w0, [x2, #0xb]
    // 0xa347f8: r1 = <StackParentData>
    //     0xa347f8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25780] TypeArguments: <StackParentData>
    //     0xa347fc: ldr             x1, [x1, #0x780]
    // 0xa34800: r0 = Positioned()
    //     0xa34800: bl              #0x9f19f8  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xa34804: mov             x3, x0
    // 0xa34808: r0 = 0.000000
    //     0xa34808: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xa3480c: stur            x3, [fp, #-0x28]
    // 0xa34810: StoreField: r3->field_13 = r0
    //     0xa34810: stur            w0, [x3, #0x13]
    // 0xa34814: ArrayStore: r3[0] = r0  ; List_4
    //     0xa34814: stur            w0, [x3, #0x17]
    // 0xa34818: StoreField: r3->field_1b = r0
    //     0xa34818: stur            w0, [x3, #0x1b]
    // 0xa3481c: StoreField: r3->field_1f = r0
    //     0xa3481c: stur            w0, [x3, #0x1f]
    // 0xa34820: ldur            x0, [fp, #-0x48]
    // 0xa34824: StoreField: r3->field_b = r0
    //     0xa34824: stur            w0, [x3, #0xb]
    // 0xa34828: r1 = Null
    //     0xa34828: mov             x1, NULL
    // 0xa3482c: r2 = 4
    //     0xa3482c: movz            x2, #0x4
    // 0xa34830: r0 = AllocateArray()
    //     0xa34830: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34834: mov             x2, x0
    // 0xa34838: ldur            x0, [fp, #-0x30]
    // 0xa3483c: stur            x2, [fp, #-0x48]
    // 0xa34840: StoreField: r2->field_f = r0
    //     0xa34840: stur            w0, [x2, #0xf]
    // 0xa34844: ldur            x0, [fp, #-0x28]
    // 0xa34848: StoreField: r2->field_13 = r0
    //     0xa34848: stur            w0, [x2, #0x13]
    // 0xa3484c: r1 = <Widget>
    //     0xa3484c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34850: r0 = AllocateGrowableArray()
    //     0xa34850: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34854: mov             x1, x0
    // 0xa34858: ldur            x0, [fp, #-0x48]
    // 0xa3485c: stur            x1, [fp, #-0x28]
    // 0xa34860: StoreField: r1->field_f = r0
    //     0xa34860: stur            w0, [x1, #0xf]
    // 0xa34864: r2 = 4
    //     0xa34864: movz            x2, #0x4
    // 0xa34868: StoreField: r1->field_b = r2
    //     0xa34868: stur            w2, [x1, #0xb]
    // 0xa3486c: r0 = Stack()
    //     0xa3486c: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa34870: mov             x2, x0
    // 0xa34874: r0 = Instance_AlignmentDirectional
    //     0xa34874: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xa34878: ldr             x0, [x0, #0x7b0]
    // 0xa3487c: stur            x2, [fp, #-0x30]
    // 0xa34880: StoreField: r2->field_f = r0
    //     0xa34880: stur            w0, [x2, #0xf]
    // 0xa34884: r0 = Instance_StackFit
    //     0xa34884: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa34888: ldr             x0, [x0, #0x7b8]
    // 0xa3488c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3488c: stur            w0, [x2, #0x17]
    // 0xa34890: r0 = Instance_Clip
    //     0xa34890: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa34894: ldr             x0, [x0, #0x7c0]
    // 0xa34898: StoreField: r2->field_1b = r0
    //     0xa34898: stur            w0, [x2, #0x1b]
    // 0xa3489c: ldur            x0, [fp, #-0x28]
    // 0xa348a0: StoreField: r2->field_b = r0
    //     0xa348a0: stur            w0, [x2, #0xb]
    // 0xa348a4: ldur            x0, [fp, #-0x18]
    // 0xa348a8: LoadField: r1 = r0->field_b
    //     0xa348a8: ldur            w1, [x0, #0xb]
    // 0xa348ac: r0 = LoadInt32Instr(r1)
    //     0xa348ac: sbfx            x0, x1, #1, #0x1f
    // 0xa348b0: ldur            x3, [fp, #-0x40]
    // 0xa348b4: cmp             x3, x0
    // 0xa348b8: b.ne            #0xa348c4
    // 0xa348bc: ldur            x1, [fp, #-0x20]
    // 0xa348c0: r0 = _growToNextCapacity()
    //     0xa348c0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa348c4: ldur            x3, [fp, #-8]
    // 0xa348c8: ldur            x2, [fp, #-0x40]
    // 0xa348cc: ldur            x4, [fp, #-0x20]
    // 0xa348d0: add             x5, x2, #1
    // 0xa348d4: lsl             x0, x5, #1
    // 0xa348d8: StoreField: r4->field_b = r0
    //     0xa348d8: stur            w0, [x4, #0xb]
    // 0xa348dc: LoadField: r6 = r4->field_f
    //     0xa348dc: ldur            w6, [x4, #0xf]
    // 0xa348e0: DecompressPointer r6
    //     0xa348e0: add             x6, x6, HEAP, lsl #32
    // 0xa348e4: mov             x1, x6
    // 0xa348e8: ldur            x0, [fp, #-0x30]
    // 0xa348ec: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa348ec: add             x25, x1, x2, lsl #2
    //     0xa348f0: add             x25, x25, #0xf
    //     0xa348f4: str             w0, [x25]
    //     0xa348f8: tbz             w0, #0, #0xa34914
    //     0xa348fc: ldurb           w16, [x1, #-1]
    //     0xa34900: ldurb           w17, [x0, #-1]
    //     0xa34904: and             x16, x17, x16, lsr #2
    //     0xa34908: tst             x16, HEAP, lsr #32
    //     0xa3490c: b.eq            #0xa34914
    //     0xa34910: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa34914: LoadField: r0 = r3->field_13
    //     0xa34914: ldur            w0, [x3, #0x13]
    // 0xa34918: DecompressPointer r0
    //     0xa34918: add             x0, x0, HEAP, lsl #32
    // 0xa3491c: tbnz            w0, #4, #0xa34a30
    // 0xa34920: LoadField: r0 = r3->field_b
    //     0xa34920: ldur            w0, [x3, #0xb]
    // 0xa34924: DecompressPointer r0
    //     0xa34924: add             x0, x0, HEAP, lsl #32
    // 0xa34928: cmp             w0, NULL
    // 0xa3492c: b.eq            #0xa350f8
    // 0xa34930: LoadField: r1 = r0->field_b
    //     0xa34930: ldur            w1, [x0, #0xb]
    // 0xa34934: DecompressPointer r1
    //     0xa34934: add             x1, x1, HEAP, lsl #32
    // 0xa34938: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa34938: ldur            w0, [x1, #0x17]
    // 0xa3493c: DecompressPointer r0
    //     0xa3493c: add             x0, x0, HEAP, lsl #32
    // 0xa34940: LoadField: r1 = r0->field_13
    //     0xa34940: ldur            w1, [x0, #0x13]
    // 0xa34944: DecompressPointer r1
    //     0xa34944: add             x1, x1, HEAP, lsl #32
    // 0xa34948: stur            x1, [fp, #-0x18]
    // 0xa3494c: cmp             w1, NULL
    // 0xa34950: b.eq            #0xa34a28
    // 0xa34954: r0 = GetNavigation.textTheme()
    //     0xa34954: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa34958: LoadField: r1 = r0->field_27
    //     0xa34958: ldur            w1, [x0, #0x27]
    // 0xa3495c: DecompressPointer r1
    //     0xa3495c: add             x1, x1, HEAP, lsl #32
    // 0xa34960: stur            x1, [fp, #-0x28]
    // 0xa34964: r0 = Text()
    //     0xa34964: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa34968: mov             x1, x0
    // 0xa3496c: ldur            x0, [fp, #-0x18]
    // 0xa34970: stur            x1, [fp, #-0x30]
    // 0xa34974: StoreField: r1->field_b = r0
    //     0xa34974: stur            w0, [x1, #0xb]
    // 0xa34978: ldur            x0, [fp, #-0x28]
    // 0xa3497c: StoreField: r1->field_13 = r0
    //     0xa3497c: stur            w0, [x1, #0x13]
    // 0xa34980: r0 = Padding()
    //     0xa34980: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa34984: mov             x2, x0
    // 0xa34988: r0 = Instance_EdgeInsets
    //     0xa34988: add             x0, PP, #0x35, lsl #12  ; [pp+0x35d48] Obj!EdgeInsets@e12281
    //     0xa3498c: ldr             x0, [x0, #0xd48]
    // 0xa34990: stur            x2, [fp, #-0x18]
    // 0xa34994: StoreField: r2->field_f = r0
    //     0xa34994: stur            w0, [x2, #0xf]
    // 0xa34998: ldur            x0, [fp, #-0x30]
    // 0xa3499c: StoreField: r2->field_b = r0
    //     0xa3499c: stur            w0, [x2, #0xb]
    // 0xa349a0: ldur            x0, [fp, #-0x20]
    // 0xa349a4: LoadField: r1 = r0->field_b
    //     0xa349a4: ldur            w1, [x0, #0xb]
    // 0xa349a8: LoadField: r3 = r0->field_f
    //     0xa349a8: ldur            w3, [x0, #0xf]
    // 0xa349ac: DecompressPointer r3
    //     0xa349ac: add             x3, x3, HEAP, lsl #32
    // 0xa349b0: LoadField: r4 = r3->field_b
    //     0xa349b0: ldur            w4, [x3, #0xb]
    // 0xa349b4: r3 = LoadInt32Instr(r1)
    //     0xa349b4: sbfx            x3, x1, #1, #0x1f
    // 0xa349b8: stur            x3, [fp, #-0x38]
    // 0xa349bc: r1 = LoadInt32Instr(r4)
    //     0xa349bc: sbfx            x1, x4, #1, #0x1f
    // 0xa349c0: cmp             x3, x1
    // 0xa349c4: b.ne            #0xa349d0
    // 0xa349c8: mov             x1, x0
    // 0xa349cc: r0 = _growToNextCapacity()
    //     0xa349cc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa349d0: ldur            x2, [fp, #-0x20]
    // 0xa349d4: ldur            x3, [fp, #-0x38]
    // 0xa349d8: add             x4, x3, #1
    // 0xa349dc: lsl             x0, x4, #1
    // 0xa349e0: StoreField: r2->field_b = r0
    //     0xa349e0: stur            w0, [x2, #0xb]
    // 0xa349e4: LoadField: r5 = r2->field_f
    //     0xa349e4: ldur            w5, [x2, #0xf]
    // 0xa349e8: DecompressPointer r5
    //     0xa349e8: add             x5, x5, HEAP, lsl #32
    // 0xa349ec: mov             x1, x5
    // 0xa349f0: ldur            x0, [fp, #-0x18]
    // 0xa349f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa349f4: add             x25, x1, x3, lsl #2
    //     0xa349f8: add             x25, x25, #0xf
    //     0xa349fc: str             w0, [x25]
    //     0xa34a00: tbz             w0, #0, #0xa34a1c
    //     0xa34a04: ldurb           w16, [x1, #-1]
    //     0xa34a08: ldurb           w17, [x0, #-1]
    //     0xa34a0c: and             x16, x17, x16, lsr #2
    //     0xa34a10: tst             x16, HEAP, lsr #32
    //     0xa34a14: b.eq            #0xa34a1c
    //     0xa34a18: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa34a1c: mov             x3, x4
    // 0xa34a20: mov             x0, x5
    // 0xa34a24: b               #0xa34a3c
    // 0xa34a28: mov             x2, x4
    // 0xa34a2c: b               #0xa34a34
    // 0xa34a30: mov             x2, x4
    // 0xa34a34: mov             x3, x5
    // 0xa34a38: mov             x0, x6
    // 0xa34a3c: stur            x3, [fp, #-0x38]
    // 0xa34a40: LoadField: r1 = r0->field_b
    //     0xa34a40: ldur            w1, [x0, #0xb]
    // 0xa34a44: r0 = LoadInt32Instr(r1)
    //     0xa34a44: sbfx            x0, x1, #1, #0x1f
    // 0xa34a48: cmp             x3, x0
    // 0xa34a4c: b.ne            #0xa34a58
    // 0xa34a50: mov             x1, x2
    // 0xa34a54: r0 = _growToNextCapacity()
    //     0xa34a54: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa34a58: ldur            x3, [fp, #-0x20]
    // 0xa34a5c: ldur            x2, [fp, #-0x38]
    // 0xa34a60: add             x4, x2, #1
    // 0xa34a64: stur            x4, [fp, #-0x40]
    // 0xa34a68: lsl             x0, x4, #1
    // 0xa34a6c: StoreField: r3->field_b = r0
    //     0xa34a6c: stur            w0, [x3, #0xb]
    // 0xa34a70: mov             x0, x4
    // 0xa34a74: mov             x1, x2
    // 0xa34a78: cmp             x1, x0
    // 0xa34a7c: b.hs            #0xa350fc
    // 0xa34a80: LoadField: r0 = r3->field_f
    //     0xa34a80: ldur            w0, [x3, #0xf]
    // 0xa34a84: DecompressPointer r0
    //     0xa34a84: add             x0, x0, HEAP, lsl #32
    // 0xa34a88: stur            x0, [fp, #-0x18]
    // 0xa34a8c: add             x1, x0, x2, lsl #2
    // 0xa34a90: r16 = Instance_SizedBox
    //     0xa34a90: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xa34a94: ldr             x16, [x16, #0x540]
    // 0xa34a98: StoreField: r1->field_f = r16
    //     0xa34a98: stur            w16, [x1, #0xf]
    // 0xa34a9c: ldur            x2, [fp, #-0x10]
    // 0xa34aa0: r1 = Function '<anonymous closure>':.
    //     0xa34aa0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51580] AnonymousClosure: (0xa36b80), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa34aa4: ldr             x1, [x1, #0x580]
    // 0xa34aa8: r0 = AllocateClosure()
    //     0xa34aa8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa34aac: r1 = <ReadingPreferenceController>
    //     0xa34aac: add             x1, PP, #0x24, lsl #12  ; [pp+0x24e10] TypeArguments: <ReadingPreferenceController>
    //     0xa34ab0: ldr             x1, [x1, #0xe10]
    // 0xa34ab4: stur            x0, [fp, #-0x28]
    // 0xa34ab8: r0 = ReadingPreferenceWidget()
    //     0xa34ab8: bl              #0xa3592c  ; AllocateReadingPreferenceWidgetStub -> ReadingPreferenceWidget (size=0x18)
    // 0xa34abc: mov             x2, x0
    // 0xa34ac0: ldur            x0, [fp, #-0x28]
    // 0xa34ac4: stur            x2, [fp, #-0x30]
    // 0xa34ac8: StoreField: r2->field_13 = r0
    //     0xa34ac8: stur            w0, [x2, #0x13]
    // 0xa34acc: ldur            x0, [fp, #-0x18]
    // 0xa34ad0: LoadField: r1 = r0->field_b
    //     0xa34ad0: ldur            w1, [x0, #0xb]
    // 0xa34ad4: r0 = LoadInt32Instr(r1)
    //     0xa34ad4: sbfx            x0, x1, #1, #0x1f
    // 0xa34ad8: ldur            x3, [fp, #-0x40]
    // 0xa34adc: cmp             x3, x0
    // 0xa34ae0: b.ne            #0xa34aec
    // 0xa34ae4: ldur            x1, [fp, #-0x20]
    // 0xa34ae8: r0 = _growToNextCapacity()
    //     0xa34ae8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa34aec: ldur            x2, [fp, #-0x40]
    // 0xa34af0: ldur            x3, [fp, #-0x20]
    // 0xa34af4: add             x4, x2, #1
    // 0xa34af8: stur            x4, [fp, #-0x38]
    // 0xa34afc: lsl             x0, x4, #1
    // 0xa34b00: StoreField: r3->field_b = r0
    //     0xa34b00: stur            w0, [x3, #0xb]
    // 0xa34b04: mov             x0, x4
    // 0xa34b08: mov             x1, x2
    // 0xa34b0c: cmp             x1, x0
    // 0xa34b10: b.hs            #0xa35100
    // 0xa34b14: LoadField: r5 = r3->field_f
    //     0xa34b14: ldur            w5, [x3, #0xf]
    // 0xa34b18: DecompressPointer r5
    //     0xa34b18: add             x5, x5, HEAP, lsl #32
    // 0xa34b1c: mov             x1, x5
    // 0xa34b20: ldur            x0, [fp, #-0x30]
    // 0xa34b24: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa34b24: add             x25, x1, x2, lsl #2
    //     0xa34b28: add             x25, x25, #0xf
    //     0xa34b2c: str             w0, [x25]
    //     0xa34b30: tbz             w0, #0, #0xa34b4c
    //     0xa34b34: ldurb           w16, [x1, #-1]
    //     0xa34b38: ldurb           w17, [x0, #-1]
    //     0xa34b3c: and             x16, x17, x16, lsr #2
    //     0xa34b40: tst             x16, HEAP, lsr #32
    //     0xa34b44: b.eq            #0xa34b4c
    //     0xa34b48: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa34b4c: LoadField: r0 = r5->field_b
    //     0xa34b4c: ldur            w0, [x5, #0xb]
    // 0xa34b50: r1 = LoadInt32Instr(r0)
    //     0xa34b50: sbfx            x1, x0, #1, #0x1f
    // 0xa34b54: cmp             x4, x1
    // 0xa34b58: b.ne            #0xa34b64
    // 0xa34b5c: mov             x1, x3
    // 0xa34b60: r0 = _growToNextCapacity()
    //     0xa34b60: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa34b64: ldur            x4, [fp, #-8]
    // 0xa34b68: ldur            x3, [fp, #-0x38]
    // 0xa34b6c: ldur            x2, [fp, #-0x20]
    // 0xa34b70: add             x0, x3, #1
    // 0xa34b74: lsl             x1, x0, #1
    // 0xa34b78: StoreField: r2->field_b = r1
    //     0xa34b78: stur            w1, [x2, #0xb]
    // 0xa34b7c: mov             x1, x3
    // 0xa34b80: cmp             x1, x0
    // 0xa34b84: b.hs            #0xa35104
    // 0xa34b88: LoadField: r0 = r2->field_f
    //     0xa34b88: ldur            w0, [x2, #0xf]
    // 0xa34b8c: DecompressPointer r0
    //     0xa34b8c: add             x0, x0, HEAP, lsl #32
    // 0xa34b90: add             x1, x0, x3, lsl #2
    // 0xa34b94: r16 = Instance_SizedBox
    //     0xa34b94: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xa34b98: ldr             x16, [x16, #0x540]
    // 0xa34b9c: StoreField: r1->field_f = r16
    //     0xa34b9c: stur            w16, [x1, #0xf]
    // 0xa34ba0: LoadField: r0 = r4->field_b
    //     0xa34ba0: ldur            w0, [x4, #0xb]
    // 0xa34ba4: DecompressPointer r0
    //     0xa34ba4: add             x0, x0, HEAP, lsl #32
    // 0xa34ba8: cmp             w0, NULL
    // 0xa34bac: b.eq            #0xa35108
    // 0xa34bb0: LoadField: r1 = r0->field_b
    //     0xa34bb0: ldur            w1, [x0, #0xb]
    // 0xa34bb4: DecompressPointer r1
    //     0xa34bb4: add             x1, x1, HEAP, lsl #32
    // 0xa34bb8: LoadField: r0 = r1->field_1b
    //     0xa34bb8: ldur            w0, [x1, #0x1b]
    // 0xa34bbc: DecompressPointer r0
    //     0xa34bbc: add             x0, x0, HEAP, lsl #32
    // 0xa34bc0: LoadField: r1 = r0->field_13
    //     0xa34bc0: ldur            x1, [x0, #0x13]
    // 0xa34bc4: cmp             x1, #9
    // 0xa34bc8: b.ne            #0xa34dd0
    // 0xa34bcc: r0 = Radius()
    //     0xa34bcc: bl              #0x63cc98  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa34bd0: d0 = 12.000000
    //     0xa34bd0: fmov            d0, #12.00000000
    // 0xa34bd4: stur            x0, [fp, #-0x18]
    // 0xa34bd8: StoreField: r0->field_7 = d0
    //     0xa34bd8: stur            d0, [x0, #7]
    // 0xa34bdc: StoreField: r0->field_f = d0
    //     0xa34bdc: stur            d0, [x0, #0xf]
    // 0xa34be0: r0 = BorderRadius()
    //     0xa34be0: bl              #0x63cf74  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa34be4: mov             x1, x0
    // 0xa34be8: ldur            x0, [fp, #-0x18]
    // 0xa34bec: stur            x1, [fp, #-0x28]
    // 0xa34bf0: StoreField: r1->field_7 = r0
    //     0xa34bf0: stur            w0, [x1, #7]
    // 0xa34bf4: StoreField: r1->field_b = r0
    //     0xa34bf4: stur            w0, [x1, #0xb]
    // 0xa34bf8: StoreField: r1->field_f = r0
    //     0xa34bf8: stur            w0, [x1, #0xf]
    // 0xa34bfc: StoreField: r1->field_13 = r0
    //     0xa34bfc: stur            w0, [x1, #0x13]
    // 0xa34c00: r16 = <Color>
    //     0xa34c00: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xa34c04: ldr             x16, [x16, #0x158]
    // 0xa34c08: r30 = Instance_Color
    //     0xa34c08: add             lr, PP, #0x51, lsl #12  ; [pp+0x51588] Obj!Color@e28021
    //     0xa34c0c: ldr             lr, [lr, #0x588]
    // 0xa34c10: stp             lr, x16, [SP, #8]
    // 0xa34c14: r16 = Instance_Color
    //     0xa34c14: add             x16, PP, #0x51, lsl #12  ; [pp+0x51590] Obj!Color@e28081
    //     0xa34c18: ldr             x16, [x16, #0x590]
    // 0xa34c1c: str             x16, [SP]
    // 0xa34c20: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa34c20: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa34c24: r0 = mode()
    //     0xa34c24: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xa34c28: r16 = 1.000000
    //     0xa34c28: ldr             x16, [PP, #0x4918]  ; [pp+0x4918] 1
    // 0xa34c2c: stp             x16, x0, [SP]
    // 0xa34c30: r1 = Null
    //     0xa34c30: mov             x1, NULL
    // 0xa34c34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, width, 0x2, null]
    //     0xa34c34: add             x4, PP, #0x33, lsl #12  ; [pp+0x333a8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "width", 0x2, Null]
    //     0xa34c38: ldr             x4, [x4, #0x3a8]
    // 0xa34c3c: r0 = Border.all()
    //     0xa34c3c: bl              #0xa35838  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa34c40: stur            x0, [fp, #-0x18]
    // 0xa34c44: r0 = BoxDecoration()
    //     0xa34c44: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa34c48: mov             x3, x0
    // 0xa34c4c: ldur            x0, [fp, #-0x18]
    // 0xa34c50: stur            x3, [fp, #-0x30]
    // 0xa34c54: StoreField: r3->field_f = r0
    //     0xa34c54: stur            w0, [x3, #0xf]
    // 0xa34c58: ldur            x0, [fp, #-0x28]
    // 0xa34c5c: StoreField: r3->field_13 = r0
    //     0xa34c5c: stur            w0, [x3, #0x13]
    // 0xa34c60: r0 = Instance_BoxShape
    //     0xa34c60: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xa34c64: ldr             x0, [x0, #0xca8]
    // 0xa34c68: StoreField: r3->field_23 = r0
    //     0xa34c68: stur            w0, [x3, #0x23]
    // 0xa34c6c: r1 = Function '<anonymous closure>':.
    //     0xa34c6c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51598] AnonymousClosure: (0xa36b0c), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa34c70: ldr             x1, [x1, #0x598]
    // 0xa34c74: r2 = Null
    //     0xa34c74: mov             x2, NULL
    // 0xa34c78: r0 = AllocateClosure()
    //     0xa34c78: bl              #0xec1630  ; AllocateClosureStub
    // 0xa34c7c: stur            x0, [fp, #-0x18]
    // 0xa34c80: r0 = TextButton()
    //     0xa34c80: bl              #0x925f0c  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa34c84: mov             x3, x0
    // 0xa34c88: ldur            x0, [fp, #-0x18]
    // 0xa34c8c: stur            x3, [fp, #-0x28]
    // 0xa34c90: StoreField: r3->field_b = r0
    //     0xa34c90: stur            w0, [x3, #0xb]
    // 0xa34c94: r0 = false
    //     0xa34c94: add             x0, NULL, #0x30  ; false
    // 0xa34c98: StoreField: r3->field_27 = r0
    //     0xa34c98: stur            w0, [x3, #0x27]
    // 0xa34c9c: r0 = true
    //     0xa34c9c: add             x0, NULL, #0x20  ; true
    // 0xa34ca0: StoreField: r3->field_2f = r0
    //     0xa34ca0: stur            w0, [x3, #0x2f]
    // 0xa34ca4: r0 = Instance_Text
    //     0xa34ca4: add             x0, PP, #0x51, lsl #12  ; [pp+0x515a0] Obj!Text@e21501
    //     0xa34ca8: ldr             x0, [x0, #0x5a0]
    // 0xa34cac: StoreField: r3->field_37 = r0
    //     0xa34cac: stur            w0, [x3, #0x37]
    // 0xa34cb0: r1 = Null
    //     0xa34cb0: mov             x1, NULL
    // 0xa34cb4: r2 = 4
    //     0xa34cb4: movz            x2, #0x4
    // 0xa34cb8: r0 = AllocateArray()
    //     0xa34cb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34cbc: stur            x0, [fp, #-0x18]
    // 0xa34cc0: r16 = Instance_Flexible
    //     0xa34cc0: add             x16, PP, #0x51, lsl #12  ; [pp+0x515a8] Obj!Flexible@e1f3e1
    //     0xa34cc4: ldr             x16, [x16, #0x5a8]
    // 0xa34cc8: StoreField: r0->field_f = r16
    //     0xa34cc8: stur            w16, [x0, #0xf]
    // 0xa34ccc: ldur            x1, [fp, #-0x28]
    // 0xa34cd0: StoreField: r0->field_13 = r1
    //     0xa34cd0: stur            w1, [x0, #0x13]
    // 0xa34cd4: r1 = <Widget>
    //     0xa34cd4: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34cd8: r0 = AllocateGrowableArray()
    //     0xa34cd8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34cdc: mov             x1, x0
    // 0xa34ce0: ldur            x0, [fp, #-0x18]
    // 0xa34ce4: stur            x1, [fp, #-0x28]
    // 0xa34ce8: StoreField: r1->field_f = r0
    //     0xa34ce8: stur            w0, [x1, #0xf]
    // 0xa34cec: r2 = 4
    //     0xa34cec: movz            x2, #0x4
    // 0xa34cf0: StoreField: r1->field_b = r2
    //     0xa34cf0: stur            w2, [x1, #0xb]
    // 0xa34cf4: r0 = Row()
    //     0xa34cf4: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa34cf8: mov             x1, x0
    // 0xa34cfc: r0 = Instance_Axis
    //     0xa34cfc: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa34d00: stur            x1, [fp, #-0x18]
    // 0xa34d04: StoreField: r1->field_f = r0
    //     0xa34d04: stur            w0, [x1, #0xf]
    // 0xa34d08: r2 = Instance_MainAxisAlignment
    //     0xa34d08: add             x2, PP, #0x27, lsl #12  ; [pp+0x27ae8] Obj!MainAxisAlignment@e35aa1
    //     0xa34d0c: ldr             x2, [x2, #0xae8]
    // 0xa34d10: StoreField: r1->field_13 = r2
    //     0xa34d10: stur            w2, [x1, #0x13]
    // 0xa34d14: r2 = Instance_MainAxisSize
    //     0xa34d14: add             x2, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa34d18: ldr             x2, [x2, #0x738]
    // 0xa34d1c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa34d1c: stur            w2, [x1, #0x17]
    // 0xa34d20: r2 = Instance_CrossAxisAlignment
    //     0xa34d20: add             x2, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa34d24: ldr             x2, [x2, #0x740]
    // 0xa34d28: StoreField: r1->field_1b = r2
    //     0xa34d28: stur            w2, [x1, #0x1b]
    // 0xa34d2c: r2 = Instance_VerticalDirection
    //     0xa34d2c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa34d30: ldr             x2, [x2, #0x748]
    // 0xa34d34: StoreField: r1->field_23 = r2
    //     0xa34d34: stur            w2, [x1, #0x23]
    // 0xa34d38: r3 = Instance_Clip
    //     0xa34d38: add             x3, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa34d3c: ldr             x3, [x3, #0x750]
    // 0xa34d40: StoreField: r1->field_2b = r3
    //     0xa34d40: stur            w3, [x1, #0x2b]
    // 0xa34d44: StoreField: r1->field_2f = rZR
    //     0xa34d44: stur            xzr, [x1, #0x2f]
    // 0xa34d48: ldur            x4, [fp, #-0x28]
    // 0xa34d4c: StoreField: r1->field_b = r4
    //     0xa34d4c: stur            w4, [x1, #0xb]
    // 0xa34d50: r0 = Container()
    //     0xa34d50: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa34d54: stur            x0, [fp, #-0x28]
    // 0xa34d58: r16 = Instance_EdgeInsets
    //     0xa34d58: add             x16, PP, #0x25, lsl #12  ; [pp+0x25768] Obj!EdgeInsets@e120a1
    //     0xa34d5c: ldr             x16, [x16, #0x768]
    // 0xa34d60: ldur            lr, [fp, #-0x30]
    // 0xa34d64: stp             lr, x16, [SP, #8]
    // 0xa34d68: ldur            x16, [fp, #-0x18]
    // 0xa34d6c: str             x16, [SP]
    // 0xa34d70: mov             x1, x0
    // 0xa34d74: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xa34d74: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c0e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xa34d78: ldr             x4, [x4, #0xe8]
    // 0xa34d7c: r0 = Container()
    //     0xa34d7c: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa34d80: r1 = Null
    //     0xa34d80: mov             x1, NULL
    // 0xa34d84: r2 = 4
    //     0xa34d84: movz            x2, #0x4
    // 0xa34d88: r0 = AllocateArray()
    //     0xa34d88: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34d8c: mov             x2, x0
    // 0xa34d90: ldur            x0, [fp, #-0x28]
    // 0xa34d94: stur            x2, [fp, #-0x18]
    // 0xa34d98: StoreField: r2->field_f = r0
    //     0xa34d98: stur            w0, [x2, #0xf]
    // 0xa34d9c: r16 = Instance_SizedBox
    //     0xa34d9c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xa34da0: ldr             x16, [x16, #0x540]
    // 0xa34da4: StoreField: r2->field_13 = r16
    //     0xa34da4: stur            w16, [x2, #0x13]
    // 0xa34da8: r1 = <Widget>
    //     0xa34da8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34dac: r0 = AllocateGrowableArray()
    //     0xa34dac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34db0: mov             x1, x0
    // 0xa34db4: ldur            x0, [fp, #-0x18]
    // 0xa34db8: StoreField: r1->field_f = r0
    //     0xa34db8: stur            w0, [x1, #0xf]
    // 0xa34dbc: r0 = 4
    //     0xa34dbc: movz            x0, #0x4
    // 0xa34dc0: StoreField: r1->field_b = r0
    //     0xa34dc0: stur            w0, [x1, #0xb]
    // 0xa34dc4: mov             x2, x1
    // 0xa34dc8: ldur            x1, [fp, #-0x20]
    // 0xa34dcc: r0 = addAll()
    //     0xa34dcc: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa34dd0: ldur            x2, [fp, #-8]
    // 0xa34dd4: LoadField: r0 = r2->field_b
    //     0xa34dd4: ldur            w0, [x2, #0xb]
    // 0xa34dd8: DecompressPointer r0
    //     0xa34dd8: add             x0, x0, HEAP, lsl #32
    // 0xa34ddc: cmp             w0, NULL
    // 0xa34de0: b.eq            #0xa3510c
    // 0xa34de4: LoadField: r1 = r0->field_b
    //     0xa34de4: ldur            w1, [x0, #0xb]
    // 0xa34de8: DecompressPointer r1
    //     0xa34de8: add             x1, x1, HEAP, lsl #32
    // 0xa34dec: LoadField: r0 = r1->field_27
    //     0xa34dec: ldur            w0, [x1, #0x27]
    // 0xa34df0: DecompressPointer r0
    //     0xa34df0: add             x0, x0, HEAP, lsl #32
    // 0xa34df4: r1 = LoadClassIdInstr(r0)
    //     0xa34df4: ldur            x1, [x0, #-1]
    //     0xa34df8: ubfx            x1, x1, #0xc, #0x14
    // 0xa34dfc: mov             x16, x0
    // 0xa34e00: mov             x0, x1
    // 0xa34e04: mov             x1, x16
    // 0xa34e08: r0 = GDT[cid_x0 + 0xd488]()
    //     0xa34e08: movz            x17, #0xd488
    //     0xa34e0c: add             lr, x0, x17
    //     0xa34e10: ldr             lr, [x21, lr, lsl #3]
    //     0xa34e14: blr             lr
    // 0xa34e18: tbnz            w0, #4, #0xa34f4c
    // 0xa34e1c: ldur            x0, [fp, #-8]
    // 0xa34e20: LoadField: r1 = r0->field_b
    //     0xa34e20: ldur            w1, [x0, #0xb]
    // 0xa34e24: DecompressPointer r1
    //     0xa34e24: add             x1, x1, HEAP, lsl #32
    // 0xa34e28: cmp             w1, NULL
    // 0xa34e2c: b.eq            #0xa35110
    // 0xa34e30: LoadField: r2 = r1->field_b
    //     0xa34e30: ldur            w2, [x1, #0xb]
    // 0xa34e34: DecompressPointer r2
    //     0xa34e34: add             x2, x2, HEAP, lsl #32
    // 0xa34e38: LoadField: r3 = r2->field_27
    //     0xa34e38: ldur            w3, [x2, #0x27]
    // 0xa34e3c: DecompressPointer r3
    //     0xa34e3c: add             x3, x3, HEAP, lsl #32
    // 0xa34e40: stur            x3, [fp, #-0x18]
    // 0xa34e44: r1 = Function '<anonymous closure>':.
    //     0xa34e44: add             x1, PP, #0x51, lsl #12  ; [pp+0x515b0] AnonymousClosure: (0xa3699c), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa34e48: ldr             x1, [x1, #0x5b0]
    // 0xa34e4c: r2 = Null
    //     0xa34e4c: mov             x2, NULL
    // 0xa34e50: r0 = AllocateClosure()
    //     0xa34e50: bl              #0xec1630  ; AllocateClosureStub
    // 0xa34e54: mov             x1, x0
    // 0xa34e58: ldur            x0, [fp, #-0x18]
    // 0xa34e5c: r2 = LoadClassIdInstr(r0)
    //     0xa34e5c: ldur            x2, [x0, #-1]
    //     0xa34e60: ubfx            x2, x2, #0xc, #0x14
    // 0xa34e64: r16 = <NTagButton>
    //     0xa34e64: add             x16, PP, #0x30, lsl #12  ; [pp+0x30968] TypeArguments: <NTagButton>
    //     0xa34e68: ldr             x16, [x16, #0x968]
    // 0xa34e6c: stp             x0, x16, [SP, #8]
    // 0xa34e70: str             x1, [SP]
    // 0xa34e74: mov             x0, x2
    // 0xa34e78: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa34e78: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa34e7c: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xa34e7c: movz            x17, #0xf28c
    //     0xa34e80: add             lr, x0, x17
    //     0xa34e84: ldr             lr, [x21, lr, lsl #3]
    //     0xa34e88: blr             lr
    // 0xa34e8c: LoadField: r1 = r0->field_7
    //     0xa34e8c: ldur            w1, [x0, #7]
    // 0xa34e90: DecompressPointer r1
    //     0xa34e90: add             x1, x1, HEAP, lsl #32
    // 0xa34e94: mov             x2, x0
    // 0xa34e98: r0 = _GrowableList.of()
    //     0xa34e98: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa34e9c: stur            x0, [fp, #-0x18]
    // 0xa34ea0: r0 = Wrap()
    //     0xa34ea0: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xa34ea4: mov             x3, x0
    // 0xa34ea8: r0 = Instance_Axis
    //     0xa34ea8: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa34eac: stur            x3, [fp, #-0x28]
    // 0xa34eb0: StoreField: r3->field_f = r0
    //     0xa34eb0: stur            w0, [x3, #0xf]
    // 0xa34eb4: r0 = Instance_WrapAlignment
    //     0xa34eb4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xa34eb8: ldr             x0, [x0, #0x610]
    // 0xa34ebc: StoreField: r3->field_13 = r0
    //     0xa34ebc: stur            w0, [x3, #0x13]
    // 0xa34ec0: d0 = 12.000000
    //     0xa34ec0: fmov            d0, #12.00000000
    // 0xa34ec4: ArrayStore: r3[0] = d0  ; List_8
    //     0xa34ec4: stur            d0, [x3, #0x17]
    // 0xa34ec8: StoreField: r3->field_1f = r0
    //     0xa34ec8: stur            w0, [x3, #0x1f]
    // 0xa34ecc: StoreField: r3->field_23 = d0
    //     0xa34ecc: stur            d0, [x3, #0x23]
    // 0xa34ed0: r0 = Instance_WrapCrossAlignment
    //     0xa34ed0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xa34ed4: ldr             x0, [x0, #0x618]
    // 0xa34ed8: StoreField: r3->field_2b = r0
    //     0xa34ed8: stur            w0, [x3, #0x2b]
    // 0xa34edc: r0 = Instance_VerticalDirection
    //     0xa34edc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa34ee0: ldr             x0, [x0, #0x748]
    // 0xa34ee4: StoreField: r3->field_33 = r0
    //     0xa34ee4: stur            w0, [x3, #0x33]
    // 0xa34ee8: r0 = Instance_Clip
    //     0xa34ee8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa34eec: ldr             x0, [x0, #0x750]
    // 0xa34ef0: StoreField: r3->field_37 = r0
    //     0xa34ef0: stur            w0, [x3, #0x37]
    // 0xa34ef4: ldur            x0, [fp, #-0x18]
    // 0xa34ef8: StoreField: r3->field_b = r0
    //     0xa34ef8: stur            w0, [x3, #0xb]
    // 0xa34efc: r1 = Null
    //     0xa34efc: mov             x1, NULL
    // 0xa34f00: r2 = 4
    //     0xa34f00: movz            x2, #0x4
    // 0xa34f04: r0 = AllocateArray()
    //     0xa34f04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa34f08: mov             x2, x0
    // 0xa34f0c: ldur            x0, [fp, #-0x28]
    // 0xa34f10: stur            x2, [fp, #-0x18]
    // 0xa34f14: StoreField: r2->field_f = r0
    //     0xa34f14: stur            w0, [x2, #0xf]
    // 0xa34f18: r16 = Instance_SizedBox
    //     0xa34f18: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c920] Obj!SizedBox@e1e061
    //     0xa34f1c: ldr             x16, [x16, #0x920]
    // 0xa34f20: StoreField: r2->field_13 = r16
    //     0xa34f20: stur            w16, [x2, #0x13]
    // 0xa34f24: r1 = <Widget>
    //     0xa34f24: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa34f28: r0 = AllocateGrowableArray()
    //     0xa34f28: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa34f2c: mov             x1, x0
    // 0xa34f30: ldur            x0, [fp, #-0x18]
    // 0xa34f34: StoreField: r1->field_f = r0
    //     0xa34f34: stur            w0, [x1, #0xf]
    // 0xa34f38: r0 = 4
    //     0xa34f38: movz            x0, #0x4
    // 0xa34f3c: StoreField: r1->field_b = r0
    //     0xa34f3c: stur            w0, [x1, #0xb]
    // 0xa34f40: mov             x2, x1
    // 0xa34f44: ldur            x1, [fp, #-0x20]
    // 0xa34f48: r0 = addAll()
    //     0xa34f48: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa34f4c: ldur            x2, [fp, #-8]
    // 0xa34f50: LoadField: r0 = r2->field_b
    //     0xa34f50: ldur            w0, [x2, #0xb]
    // 0xa34f54: DecompressPointer r0
    //     0xa34f54: add             x0, x0, HEAP, lsl #32
    // 0xa34f58: cmp             w0, NULL
    // 0xa34f5c: b.eq            #0xa35114
    // 0xa34f60: LoadField: r1 = r0->field_b
    //     0xa34f60: ldur            w1, [x0, #0xb]
    // 0xa34f64: DecompressPointer r1
    //     0xa34f64: add             x1, x1, HEAP, lsl #32
    // 0xa34f68: LoadField: r0 = r1->field_2f
    //     0xa34f68: ldur            w0, [x1, #0x2f]
    // 0xa34f6c: DecompressPointer r0
    //     0xa34f6c: add             x0, x0, HEAP, lsl #32
    // 0xa34f70: r1 = LoadClassIdInstr(r0)
    //     0xa34f70: ldur            x1, [x0, #-1]
    //     0xa34f74: ubfx            x1, x1, #0xc, #0x14
    // 0xa34f78: mov             x16, x0
    // 0xa34f7c: mov             x0, x1
    // 0xa34f80: mov             x1, x16
    // 0xa34f84: r0 = GDT[cid_x0 + 0xd488]()
    //     0xa34f84: movz            x17, #0xd488
    //     0xa34f88: add             lr, x0, x17
    //     0xa34f8c: ldr             lr, [x21, lr, lsl #3]
    //     0xa34f90: blr             lr
    // 0xa34f94: tbnz            w0, #4, #0xa35098
    // 0xa34f98: ldur            x1, [fp, #-8]
    // 0xa34f9c: r0 = build()
    //     0xa34f9c: bl              #0xa23a24  ; [package:flutter/src/widgets/will_pop_scope.dart] _WillPopScopeState::build
    // 0xa34fa0: LoadField: r1 = r0->field_2f
    //     0xa34fa0: ldur            w1, [x0, #0x2f]
    // 0xa34fa4: DecompressPointer r1
    //     0xa34fa4: add             x1, x1, HEAP, lsl #32
    // 0xa34fa8: r0 = LoadClassIdInstr(r1)
    //     0xa34fa8: ldur            x0, [x1, #-1]
    //     0xa34fac: ubfx            x0, x0, #0xc, #0x14
    // 0xa34fb0: str             x1, [SP]
    // 0xa34fb4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa34fb4: movz            x17, #0xc834
    //     0xa34fb8: add             lr, x0, x17
    //     0xa34fbc: ldr             lr, [x21, lr, lsl #3]
    //     0xa34fc0: blr             lr
    // 0xa34fc4: r1 = LoadInt32Instr(r0)
    //     0xa34fc4: sbfx            x1, x0, #1, #0x1f
    //     0xa34fc8: tbz             w0, #0, #0xa34fd0
    //     0xa34fcc: ldur            x1, [x0, #7]
    // 0xa34fd0: add             x3, x1, #1
    // 0xa34fd4: ldur            x2, [fp, #-0x10]
    // 0xa34fd8: stur            x3, [fp, #-0x38]
    // 0xa34fdc: r1 = Function '<anonymous closure>':.
    //     0xa34fdc: add             x1, PP, #0x51, lsl #12  ; [pp+0x515b8] AnonymousClosure: (0xa35a38), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa34fe0: ldr             x1, [x1, #0x5b8]
    // 0xa34fe4: r0 = AllocateClosure()
    //     0xa34fe4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa34fe8: r1 = Function '<anonymous closure>':.
    //     0xa34fe8: add             x1, PP, #0x51, lsl #12  ; [pp+0x515c0] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xa34fec: ldr             x1, [x1, #0x5c0]
    // 0xa34ff0: r2 = Null
    //     0xa34ff0: mov             x2, NULL
    // 0xa34ff4: stur            x0, [fp, #-8]
    // 0xa34ff8: r0 = AllocateClosure()
    //     0xa34ff8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa34ffc: stur            x0, [fp, #-0x10]
    // 0xa35000: r0 = ListView()
    //     0xa35000: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xa35004: stur            x0, [fp, #-0x18]
    // 0xa35008: r16 = Instance_NeverScrollableScrollPhysics
    //     0xa35008: add             x16, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xa3500c: ldr             x16, [x16, #0x290]
    // 0xa35010: r30 = true
    //     0xa35010: add             lr, NULL, #0x20  ; true
    // 0xa35014: stp             lr, x16, [SP]
    // 0xa35018: mov             x1, x0
    // 0xa3501c: ldur            x2, [fp, #-8]
    // 0xa35020: ldur            x3, [fp, #-0x38]
    // 0xa35024: ldur            x5, [fp, #-0x10]
    // 0xa35028: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xa35028: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c8f8] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xa3502c: ldr             x4, [x4, #0x8f8]
    // 0xa35030: r0 = ListView.separated()
    //     0xa35030: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xa35034: r1 = Null
    //     0xa35034: mov             x1, NULL
    // 0xa35038: r2 = 8
    //     0xa35038: movz            x2, #0x8
    // 0xa3503c: r0 = AllocateArray()
    //     0xa3503c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa35040: stur            x0, [fp, #-8]
    // 0xa35044: r16 = Instance_NArticleHeader
    //     0xa35044: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2ca38] Obj!NArticleHeader@e20e91
    //     0xa35048: ldr             x16, [x16, #0xa38]
    // 0xa3504c: StoreField: r0->field_f = r16
    //     0xa3504c: stur            w16, [x0, #0xf]
    // 0xa35050: r16 = Instance_SizedBox
    //     0xa35050: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xa35054: ldr             x16, [x16, #0xfe8]
    // 0xa35058: StoreField: r0->field_13 = r16
    //     0xa35058: stur            w16, [x0, #0x13]
    // 0xa3505c: r16 = Instance_Divider
    //     0xa3505c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xa35060: ldr             x16, [x16, #0xc28]
    // 0xa35064: ArrayStore: r0[0] = r16  ; List_4
    //     0xa35064: stur            w16, [x0, #0x17]
    // 0xa35068: ldur            x1, [fp, #-0x18]
    // 0xa3506c: StoreField: r0->field_1b = r1
    //     0xa3506c: stur            w1, [x0, #0x1b]
    // 0xa35070: r1 = <Widget>
    //     0xa35070: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa35074: r0 = AllocateGrowableArray()
    //     0xa35074: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xa35078: mov             x1, x0
    // 0xa3507c: ldur            x0, [fp, #-8]
    // 0xa35080: StoreField: r1->field_f = r0
    //     0xa35080: stur            w0, [x1, #0xf]
    // 0xa35084: r0 = 8
    //     0xa35084: movz            x0, #0x8
    // 0xa35088: StoreField: r1->field_b = r0
    //     0xa35088: stur            w0, [x1, #0xb]
    // 0xa3508c: mov             x2, x1
    // 0xa35090: ldur            x1, [fp, #-0x20]
    // 0xa35094: r0 = addAll()
    //     0xa35094: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xa35098: r0 = ListView()
    //     0xa35098: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xa3509c: stur            x0, [fp, #-8]
    // 0xa350a0: r16 = Instance_EdgeInsets
    //     0xa350a0: add             x16, PP, #0x51, lsl #12  ; [pp+0x515c8] Obj!EdgeInsets@e12311
    //     0xa350a4: ldr             x16, [x16, #0x5c8]
    // 0xa350a8: str             x16, [SP]
    // 0xa350ac: mov             x1, x0
    // 0xa350b0: ldur            x2, [fp, #-0x20]
    // 0xa350b4: r4 = const [0, 0x3, 0x1, 0x2, padding, 0x2, null]
    //     0xa350b4: add             x4, PP, #0x27, lsl #12  ; [pp+0x270a0] List(7) [0, 0x3, 0x1, 0x2, "padding", 0x2, Null]
    //     0xa350b8: ldr             x4, [x4, #0xa0]
    // 0xa350bc: r0 = ListView()
    //     0xa350bc: bl              #0xa3513c  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xa350c0: ldur            x0, [fp, #-8]
    // 0xa350c4: LeaveFrame
    //     0xa350c4: mov             SP, fp
    //     0xa350c8: ldp             fp, lr, [SP], #0x10
    // 0xa350cc: ret
    //     0xa350cc: ret             
    // 0xa350d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa350d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa350d4: b               #0xa33dfc
    // 0xa350d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa350f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa350fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa350fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa35100: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa35100: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa35104: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa35104: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa35108: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa35108: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3510c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3510c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa35110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa35110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa35114: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa35114: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa35a38, size: 0x180
    // 0xa35a38: EnterFrame
    //     0xa35a38: stp             fp, lr, [SP, #-0x10]!
    //     0xa35a3c: mov             fp, SP
    // 0xa35a40: AllocStack(0x18)
    //     0xa35a40: sub             SP, SP, #0x18
    // 0xa35a44: SetupParameters()
    //     0xa35a44: ldr             x0, [fp, #0x20]
    //     0xa35a48: ldur            w1, [x0, #0x17]
    //     0xa35a4c: add             x1, x1, HEAP, lsl #32
    //     0xa35a50: stur            x1, [fp, #-8]
    // 0xa35a54: CheckStackOverflow
    //     0xa35a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa35a58: cmp             SP, x16
    //     0xa35a5c: b.ls            #0xa35ba8
    // 0xa35a60: LoadField: r0 = r1->field_f
    //     0xa35a60: ldur            w0, [x1, #0xf]
    // 0xa35a64: DecompressPointer r0
    //     0xa35a64: add             x0, x0, HEAP, lsl #32
    // 0xa35a68: LoadField: r2 = r0->field_b
    //     0xa35a68: ldur            w2, [x0, #0xb]
    // 0xa35a6c: DecompressPointer r2
    //     0xa35a6c: add             x2, x2, HEAP, lsl #32
    // 0xa35a70: cmp             w2, NULL
    // 0xa35a74: b.eq            #0xa35bb0
    // 0xa35a78: LoadField: r0 = r2->field_b
    //     0xa35a78: ldur            w0, [x2, #0xb]
    // 0xa35a7c: DecompressPointer r0
    //     0xa35a7c: add             x0, x0, HEAP, lsl #32
    // 0xa35a80: LoadField: r2 = r0->field_2f
    //     0xa35a80: ldur            w2, [x0, #0x2f]
    // 0xa35a84: DecompressPointer r2
    //     0xa35a84: add             x2, x2, HEAP, lsl #32
    // 0xa35a88: r0 = LoadClassIdInstr(r2)
    //     0xa35a88: ldur            x0, [x2, #-1]
    //     0xa35a8c: ubfx            x0, x0, #0xc, #0x14
    // 0xa35a90: str             x2, [SP]
    // 0xa35a94: r0 = GDT[cid_x0 + 0xc834]()
    //     0xa35a94: movz            x17, #0xc834
    //     0xa35a98: add             lr, x0, x17
    //     0xa35a9c: ldr             lr, [x21, lr, lsl #3]
    //     0xa35aa0: blr             lr
    // 0xa35aa4: mov             x1, x0
    // 0xa35aa8: ldr             x0, [fp, #0x10]
    // 0xa35aac: r2 = LoadInt32Instr(r0)
    //     0xa35aac: sbfx            x2, x0, #1, #0x1f
    //     0xa35ab0: tbz             w0, #0, #0xa35ab8
    //     0xa35ab4: ldur            x2, [x0, #7]
    // 0xa35ab8: r3 = LoadInt32Instr(r1)
    //     0xa35ab8: sbfx            x3, x1, #1, #0x1f
    //     0xa35abc: tbz             w1, #0, #0xa35ac4
    //     0xa35ac0: ldur            x3, [x1, #7]
    // 0xa35ac4: cmp             x3, x2
    // 0xa35ac8: b.ne            #0xa35b34
    // 0xa35acc: r0 = find()
    //     0xa35acc: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xa35ad0: mov             x1, x0
    // 0xa35ad4: r0 = _adsVisibility()
    //     0xa35ad4: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xa35ad8: mov             x2, x0
    // 0xa35adc: r1 = Null
    //     0xa35adc: mov             x1, NULL
    // 0xa35ae0: r0 = AdsConfig.fromJson()
    //     0xa35ae0: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xa35ae4: LoadField: r1 = r0->field_b
    //     0xa35ae4: ldur            w1, [x0, #0xb]
    // 0xa35ae8: DecompressPointer r1
    //     0xa35ae8: add             x1, x1, HEAP, lsl #32
    // 0xa35aec: LoadField: r0 = r1->field_7
    //     0xa35aec: ldur            w0, [x1, #7]
    // 0xa35af0: DecompressPointer r0
    //     0xa35af0: add             x0, x0, HEAP, lsl #32
    // 0xa35af4: tbnz            w0, #4, #0xa35b20
    // 0xa35af8: r0 = find()
    //     0xa35af8: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xa35afc: mov             x1, x0
    // 0xa35b00: r0 = _adsVisibility()
    //     0xa35b00: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xa35b04: mov             x2, x0
    // 0xa35b08: r1 = Null
    //     0xa35b08: mov             x1, NULL
    // 0xa35b0c: r0 = AdsConfig.fromJson()
    //     0xa35b0c: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xa35b10: r0 = AdmobArticleNativeWidget()
    //     0xa35b10: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xa35b14: LeaveFrame
    //     0xa35b14: mov             SP, fp
    //     0xa35b18: ldp             fp, lr, [SP], #0x10
    // 0xa35b1c: ret
    //     0xa35b1c: ret             
    // 0xa35b20: r0 = Instance_SizedBox
    //     0xa35b20: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xa35b24: ldr             x0, [x0, #0xc40]
    // 0xa35b28: LeaveFrame
    //     0xa35b28: mov             SP, fp
    //     0xa35b2c: ldp             fp, lr, [SP], #0x10
    // 0xa35b30: ret
    //     0xa35b30: ret             
    // 0xa35b34: ldur            x1, [fp, #-8]
    // 0xa35b38: LoadField: r2 = r1->field_f
    //     0xa35b38: ldur            w2, [x1, #0xf]
    // 0xa35b3c: DecompressPointer r2
    //     0xa35b3c: add             x2, x2, HEAP, lsl #32
    // 0xa35b40: LoadField: r1 = r2->field_b
    //     0xa35b40: ldur            w1, [x2, #0xb]
    // 0xa35b44: DecompressPointer r1
    //     0xa35b44: add             x1, x1, HEAP, lsl #32
    // 0xa35b48: cmp             w1, NULL
    // 0xa35b4c: b.eq            #0xa35bb4
    // 0xa35b50: LoadField: r2 = r1->field_b
    //     0xa35b50: ldur            w2, [x1, #0xb]
    // 0xa35b54: DecompressPointer r2
    //     0xa35b54: add             x2, x2, HEAP, lsl #32
    // 0xa35b58: LoadField: r1 = r2->field_2f
    //     0xa35b58: ldur            w1, [x2, #0x2f]
    // 0xa35b5c: DecompressPointer r1
    //     0xa35b5c: add             x1, x1, HEAP, lsl #32
    // 0xa35b60: r2 = LoadClassIdInstr(r1)
    //     0xa35b60: ldur            x2, [x1, #-1]
    //     0xa35b64: ubfx            x2, x2, #0xc, #0x14
    // 0xa35b68: stp             x0, x1, [SP]
    // 0xa35b6c: mov             x0, x2
    // 0xa35b70: r0 = GDT[cid_x0 + 0x13037]()
    //     0xa35b70: movz            x17, #0x3037
    //     0xa35b74: movk            x17, #0x1, lsl #16
    //     0xa35b78: add             lr, x0, x17
    //     0xa35b7c: ldr             lr, [x21, lr, lsl #3]
    //     0xa35b80: blr             lr
    // 0xa35b84: stur            x0, [fp, #-8]
    // 0xa35b88: r0 = ArticleItem()
    //     0xa35b88: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xa35b8c: ldur            x1, [fp, #-8]
    // 0xa35b90: StoreField: r0->field_b = r1
    //     0xa35b90: stur            w1, [x0, #0xb]
    // 0xa35b94: r1 = false
    //     0xa35b94: add             x1, NULL, #0x30  ; false
    // 0xa35b98: StoreField: r0->field_13 = r1
    //     0xa35b98: stur            w1, [x0, #0x13]
    // 0xa35b9c: LeaveFrame
    //     0xa35b9c: mov             SP, fp
    //     0xa35ba0: ldp             fp, lr, [SP], #0x10
    // 0xa35ba4: ret
    //     0xa35ba4: ret             
    // 0xa35ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa35ba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa35bac: b               #0xa35a60
    // 0xa35bb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa35bb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa35bb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa35bb4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] NTagButton <anonymous closure>(dynamic, Tag) {
    // ** addr: 0xa3699c, size: 0x84
    // 0xa3699c: EnterFrame
    //     0xa3699c: stp             fp, lr, [SP, #-0x10]!
    //     0xa369a0: mov             fp, SP
    // 0xa369a4: AllocStack(0x18)
    //     0xa369a4: sub             SP, SP, #0x18
    // 0xa369a8: SetupParameters()
    //     0xa369a8: ldr             x0, [fp, #0x18]
    //     0xa369ac: ldur            w1, [x0, #0x17]
    //     0xa369b0: add             x1, x1, HEAP, lsl #32
    //     0xa369b4: stur            x1, [fp, #-8]
    // 0xa369b8: r1 = 1
    //     0xa369b8: movz            x1, #0x1
    // 0xa369bc: r0 = AllocateContext()
    //     0xa369bc: bl              #0xec126c  ; AllocateContextStub
    // 0xa369c0: mov             x1, x0
    // 0xa369c4: ldur            x0, [fp, #-8]
    // 0xa369c8: stur            x1, [fp, #-0x10]
    // 0xa369cc: StoreField: r1->field_b = r0
    //     0xa369cc: stur            w0, [x1, #0xb]
    // 0xa369d0: ldr             x0, [fp, #0x10]
    // 0xa369d4: StoreField: r1->field_f = r0
    //     0xa369d4: stur            w0, [x1, #0xf]
    // 0xa369d8: LoadField: r2 = r0->field_f
    //     0xa369d8: ldur            w2, [x0, #0xf]
    // 0xa369dc: DecompressPointer r2
    //     0xa369dc: add             x2, x2, HEAP, lsl #32
    // 0xa369e0: stur            x2, [fp, #-8]
    // 0xa369e4: r0 = NTagButton()
    //     0xa369e4: bl              #0xa36a20  ; AllocateNTagButtonStub -> NTagButton (size=0x18)
    // 0xa369e8: mov             x3, x0
    // 0xa369ec: ldur            x0, [fp, #-8]
    // 0xa369f0: stur            x3, [fp, #-0x18]
    // 0xa369f4: StoreField: r3->field_b = r0
    //     0xa369f4: stur            w0, [x3, #0xb]
    // 0xa369f8: ldur            x2, [fp, #-0x10]
    // 0xa369fc: r1 = Function '<anonymous closure>':.
    //     0xa369fc: add             x1, PP, #0x51, lsl #12  ; [pp+0x515d0] AnonymousClosure: (0xa36a2c), in [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::build (0xa33ddc)
    //     0xa36a00: ldr             x1, [x1, #0x5d0]
    // 0xa36a04: r0 = AllocateClosure()
    //     0xa36a04: bl              #0xec1630  ; AllocateClosureStub
    // 0xa36a08: mov             x1, x0
    // 0xa36a0c: ldur            x0, [fp, #-0x18]
    // 0xa36a10: StoreField: r0->field_f = r1
    //     0xa36a10: stur            w1, [x0, #0xf]
    // 0xa36a14: LeaveFrame
    //     0xa36a14: mov             SP, fp
    //     0xa36a18: ldp             fp, lr, [SP], #0x10
    // 0xa36a1c: ret
    //     0xa36a1c: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa36a2c, size: 0xe0
    // 0xa36a2c: EnterFrame
    //     0xa36a2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa36a30: mov             fp, SP
    // 0xa36a34: AllocStack(0x20)
    //     0xa36a34: sub             SP, SP, #0x20
    // 0xa36a38: SetupParameters()
    //     0xa36a38: ldr             x0, [fp, #0x10]
    //     0xa36a3c: ldur            w1, [x0, #0x17]
    //     0xa36a40: add             x1, x1, HEAP, lsl #32
    //     0xa36a44: stur            x1, [fp, #-8]
    // 0xa36a48: CheckStackOverflow
    //     0xa36a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36a4c: cmp             SP, x16
    //     0xa36a50: b.ls            #0xa36b04
    // 0xa36a54: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa36a54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa36a58: ldr             x0, [x0, #0x2670]
    //     0xa36a5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa36a60: cmp             w0, w16
    //     0xa36a64: b.ne            #0xa36a70
    //     0xa36a68: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa36a6c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa36a70: r1 = Null
    //     0xa36a70: mov             x1, NULL
    // 0xa36a74: r2 = 8
    //     0xa36a74: movz            x2, #0x8
    // 0xa36a78: r0 = AllocateArray()
    //     0xa36a78: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa36a7c: mov             x2, x0
    // 0xa36a80: r16 = "id"
    //     0xa36a80: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xa36a84: ldr             x16, [x16, #0x740]
    // 0xa36a88: StoreField: r2->field_f = r16
    //     0xa36a88: stur            w16, [x2, #0xf]
    // 0xa36a8c: ldur            x0, [fp, #-8]
    // 0xa36a90: LoadField: r3 = r0->field_f
    //     0xa36a90: ldur            w3, [x0, #0xf]
    // 0xa36a94: DecompressPointer r3
    //     0xa36a94: add             x3, x3, HEAP, lsl #32
    // 0xa36a98: LoadField: r4 = r3->field_7
    //     0xa36a98: ldur            x4, [x3, #7]
    // 0xa36a9c: r0 = BoxInt64Instr(r4)
    //     0xa36a9c: sbfiz           x0, x4, #1, #0x1f
    //     0xa36aa0: cmp             x4, x0, asr #1
    //     0xa36aa4: b.eq            #0xa36ab0
    //     0xa36aa8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa36aac: stur            x4, [x0, #7]
    // 0xa36ab0: StoreField: r2->field_13 = r0
    //     0xa36ab0: stur            w0, [x2, #0x13]
    // 0xa36ab4: r16 = "title"
    //     0xa36ab4: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xa36ab8: ldr             x16, [x16, #0x748]
    // 0xa36abc: ArrayStore: r2[0] = r16  ; List_4
    //     0xa36abc: stur            w16, [x2, #0x17]
    // 0xa36ac0: LoadField: r0 = r3->field_f
    //     0xa36ac0: ldur            w0, [x3, #0xf]
    // 0xa36ac4: DecompressPointer r0
    //     0xa36ac4: add             x0, x0, HEAP, lsl #32
    // 0xa36ac8: StoreField: r2->field_1b = r0
    //     0xa36ac8: stur            w0, [x2, #0x1b]
    // 0xa36acc: r16 = <String, Object>
    //     0xa36acc: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xa36ad0: ldr             x16, [x16, #0x790]
    // 0xa36ad4: stp             x2, x16, [SP]
    // 0xa36ad8: r0 = Map._fromLiteral()
    //     0xa36ad8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa36adc: r16 = "/article/article-tag"
    //     0xa36adc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c930] "/article/article-tag"
    //     0xa36ae0: ldr             x16, [x16, #0x930]
    // 0xa36ae4: stp             x16, NULL, [SP, #8]
    // 0xa36ae8: str             x0, [SP]
    // 0xa36aec: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa36aec: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa36af0: ldr             x4, [x4, #0x478]
    // 0xa36af4: r0 = GetNavigation.toNamed()
    //     0xa36af4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa36af8: LeaveFrame
    //     0xa36af8: mov             SP, fp
    //     0xa36afc: ldp             fp, lr, [SP], #0x10
    // 0xa36b00: ret
    //     0xa36b00: ret             
    // 0xa36b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36b04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36b08: b               #0xa36a54
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa36b0c, size: 0x74
    // 0xa36b0c: EnterFrame
    //     0xa36b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa36b10: mov             fp, SP
    // 0xa36b14: AllocStack(0x10)
    //     0xa36b14: sub             SP, SP, #0x10
    // 0xa36b18: CheckStackOverflow
    //     0xa36b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36b1c: cmp             SP, x16
    //     0xa36b20: b.ls            #0xa36b78
    // 0xa36b24: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa36b24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa36b28: ldr             x0, [x0, #0x2670]
    //     0xa36b2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa36b30: cmp             w0, w16
    //     0xa36b34: b.ne            #0xa36b40
    //     0xa36b38: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa36b3c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa36b40: r16 = "/donation"
    //     0xa36b40: add             x16, PP, #0xf, lsl #12  ; [pp+0xfc30] "/donation"
    //     0xa36b44: ldr             x16, [x16, #0xc30]
    // 0xa36b48: stp             x16, NULL, [SP]
    // 0xa36b4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa36b4c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa36b50: r0 = GetNavigation.toNamed()
    //     0xa36b50: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa36b54: r16 = "/donation/pay-donation"
    //     0xa36b54: add             x16, PP, #0x35, lsl #12  ; [pp+0x35560] "/donation/pay-donation"
    //     0xa36b58: ldr             x16, [x16, #0x560]
    // 0xa36b5c: stp             x16, NULL, [SP]
    // 0xa36b60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa36b60: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa36b64: r0 = GetNavigation.toNamed()
    //     0xa36b64: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa36b68: r0 = Null
    //     0xa36b68: mov             x0, NULL
    // 0xa36b6c: LeaveFrame
    //     0xa36b6c: mov             SP, fp
    //     0xa36b70: ldp             fp, lr, [SP], #0x10
    // 0xa36b74: ret
    //     0xa36b74: ret             
    // 0xa36b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36b7c: b               #0xa36b24
  }
  [closure] ArticleContentHtml <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xa36b80, size: 0x134
    // 0xa36b80: EnterFrame
    //     0xa36b80: stp             fp, lr, [SP, #-0x10]!
    //     0xa36b84: mov             fp, SP
    // 0xa36b88: AllocStack(0x28)
    //     0xa36b88: sub             SP, SP, #0x28
    // 0xa36b8c: SetupParameters()
    //     0xa36b8c: ldr             x0, [fp, #0x20]
    //     0xa36b90: ldur            w1, [x0, #0x17]
    //     0xa36b94: add             x1, x1, HEAP, lsl #32
    // 0xa36b98: CheckStackOverflow
    //     0xa36b98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36b9c: cmp             SP, x16
    //     0xa36ba0: b.ls            #0xa36ca8
    // 0xa36ba4: LoadField: r0 = r1->field_f
    //     0xa36ba4: ldur            w0, [x1, #0xf]
    // 0xa36ba8: DecompressPointer r0
    //     0xa36ba8: add             x0, x0, HEAP, lsl #32
    // 0xa36bac: LoadField: r1 = r0->field_b
    //     0xa36bac: ldur            w1, [x0, #0xb]
    // 0xa36bb0: DecompressPointer r1
    //     0xa36bb0: add             x1, x1, HEAP, lsl #32
    // 0xa36bb4: cmp             w1, NULL
    // 0xa36bb8: b.eq            #0xa36cb0
    // 0xa36bbc: LoadField: r0 = r1->field_b
    //     0xa36bbc: ldur            w0, [x1, #0xb]
    // 0xa36bc0: DecompressPointer r0
    //     0xa36bc0: add             x0, x0, HEAP, lsl #32
    // 0xa36bc4: LoadField: r3 = r0->field_2b
    //     0xa36bc4: ldur            w3, [x0, #0x2b]
    // 0xa36bc8: DecompressPointer r3
    //     0xa36bc8: add             x3, x3, HEAP, lsl #32
    // 0xa36bcc: stur            x3, [fp, #-0x20]
    // 0xa36bd0: LoadField: r4 = r0->field_3b
    //     0xa36bd0: ldur            w4, [x0, #0x3b]
    // 0xa36bd4: DecompressPointer r4
    //     0xa36bd4: add             x4, x4, HEAP, lsl #32
    // 0xa36bd8: stur            x4, [fp, #-0x18]
    // 0xa36bdc: LoadField: r5 = r0->field_3f
    //     0xa36bdc: ldur            w5, [x0, #0x3f]
    // 0xa36be0: DecompressPointer r5
    //     0xa36be0: add             x5, x5, HEAP, lsl #32
    // 0xa36be4: stur            x5, [fp, #-0x10]
    // 0xa36be8: LoadField: r6 = r0->field_43
    //     0xa36be8: ldur            w6, [x0, #0x43]
    // 0xa36bec: DecompressPointer r6
    //     0xa36bec: add             x6, x6, HEAP, lsl #32
    // 0xa36bf0: stur            x6, [fp, #-8]
    // 0xa36bf4: LoadField: r1 = r0->field_7
    //     0xa36bf4: ldur            x1, [x0, #7]
    // 0xa36bf8: r17 = 145302
    //     0xa36bf8: movz            x17, #0x3796
    //     0xa36bfc: movk            x17, #0x2, lsl #16
    // 0xa36c00: cmp             x1, x17
    // 0xa36c04: b.le            #0xa36c34
    // 0xa36c08: LoadField: r1 = r0->field_47
    //     0xa36c08: ldur            w1, [x0, #0x47]
    // 0xa36c0c: DecompressPointer r1
    //     0xa36c0c: add             x1, x1, HEAP, lsl #32
    // 0xa36c10: cmp             w1, NULL
    // 0xa36c14: b.ne            #0xa36c28
    // 0xa36c18: r1 = <Author>
    //     0xa36c18: ldr             x1, [PP, #0x7b70]  ; [pp+0x7b70] TypeArguments: <Author>
    // 0xa36c1c: r2 = 0
    //     0xa36c1c: movz            x2, #0
    // 0xa36c20: r0 = _GrowableList()
    //     0xa36c20: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa36c24: b               #0xa36c2c
    // 0xa36c28: mov             x0, x1
    // 0xa36c2c: mov             x5, x0
    // 0xa36c30: b               #0xa36c3c
    // 0xa36c34: r5 = const []
    //     0xa36c34: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2d538] List<Author>(0)
    //     0xa36c38: ldr             x5, [x5, #0x538]
    // 0xa36c3c: ldr             x4, [fp, #0x18]
    // 0xa36c40: ldur            x0, [fp, #-0x20]
    // 0xa36c44: ldur            x1, [fp, #-0x18]
    // 0xa36c48: ldur            x2, [fp, #-0x10]
    // 0xa36c4c: ldur            x3, [fp, #-8]
    // 0xa36c50: stur            x5, [fp, #-0x28]
    // 0xa36c54: r0 = ArticleContentHtml()
    //     0xa36c54: bl              #0xa36cb4  ; AllocateArticleContentHtmlStub -> ArticleContentHtml (size=0x30)
    // 0xa36c58: ldur            x1, [fp, #-0x20]
    // 0xa36c5c: StoreField: r0->field_b = r1
    //     0xa36c5c: stur            w1, [x0, #0xb]
    // 0xa36c60: ldr             x1, [fp, #0x18]
    // 0xa36c64: StoreField: r0->field_f = r1
    //     0xa36c64: stur            w1, [x0, #0xf]
    // 0xa36c68: r1 = true
    //     0xa36c68: add             x1, NULL, #0x20  ; true
    // 0xa36c6c: StoreField: r0->field_13 = r1
    //     0xa36c6c: stur            w1, [x0, #0x13]
    // 0xa36c70: ArrayStore: r0[0] = r1  ; List_4
    //     0xa36c70: stur            w1, [x0, #0x17]
    // 0xa36c74: ldur            x1, [fp, #-0x18]
    // 0xa36c78: StoreField: r0->field_23 = r1
    //     0xa36c78: stur            w1, [x0, #0x23]
    // 0xa36c7c: ldur            x1, [fp, #-0x10]
    // 0xa36c80: StoreField: r0->field_27 = r1
    //     0xa36c80: stur            w1, [x0, #0x27]
    // 0xa36c84: ldur            x1, [fp, #-8]
    // 0xa36c88: StoreField: r0->field_1b = r1
    //     0xa36c88: stur            w1, [x0, #0x1b]
    // 0xa36c8c: ldur            x1, [fp, #-0x28]
    // 0xa36c90: StoreField: r0->field_1f = r1
    //     0xa36c90: stur            w1, [x0, #0x1f]
    // 0xa36c94: r1 = false
    //     0xa36c94: add             x1, NULL, #0x30  ; false
    // 0xa36c98: StoreField: r0->field_2b = r1
    //     0xa36c98: stur            w1, [x0, #0x2b]
    // 0xa36c9c: LeaveFrame
    //     0xa36c9c: mov             SP, fp
    //     0xa36ca0: ldp             fp, lr, [SP], #0x10
    // 0xa36ca4: ret
    //     0xa36ca4: ret             
    // 0xa36ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36ca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36cac: b               #0xa36ba4
    // 0xa36cb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa36cb0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _toggleCaption(dynamic) {
    // ** addr: 0xa36cc0, size: 0x38
    // 0xa36cc0: EnterFrame
    //     0xa36cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa36cc4: mov             fp, SP
    // 0xa36cc8: ldr             x0, [fp, #0x10]
    // 0xa36ccc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa36ccc: ldur            w1, [x0, #0x17]
    // 0xa36cd0: DecompressPointer r1
    //     0xa36cd0: add             x1, x1, HEAP, lsl #32
    // 0xa36cd4: CheckStackOverflow
    //     0xa36cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36cd8: cmp             SP, x16
    //     0xa36cdc: b.ls            #0xa36cf0
    // 0xa36ce0: r0 = _toggleCaption()
    //     0xa36ce0: bl              #0xa36cf8  ; [package:nuonline/app/modules/article/article_detail/views/article_detail_layout.dart] _ArticleDetailLayoutState::_toggleCaption
    // 0xa36ce4: LeaveFrame
    //     0xa36ce4: mov             SP, fp
    //     0xa36ce8: ldp             fp, lr, [SP], #0x10
    // 0xa36cec: ret
    //     0xa36cec: ret             
    // 0xa36cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36cf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36cf4: b               #0xa36ce0
  }
  _ _toggleCaption(/* No info */) {
    // ** addr: 0xa36cf8, size: 0x64
    // 0xa36cf8: EnterFrame
    //     0xa36cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xa36cfc: mov             fp, SP
    // 0xa36d00: AllocStack(0x8)
    //     0xa36d00: sub             SP, SP, #8
    // 0xa36d04: SetupParameters(_ArticleDetailLayoutState this /* r1 => r1, fp-0x8 */)
    //     0xa36d04: stur            x1, [fp, #-8]
    // 0xa36d08: CheckStackOverflow
    //     0xa36d08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36d0c: cmp             SP, x16
    //     0xa36d10: b.ls            #0xa36d54
    // 0xa36d14: r1 = 1
    //     0xa36d14: movz            x1, #0x1
    // 0xa36d18: r0 = AllocateContext()
    //     0xa36d18: bl              #0xec126c  ; AllocateContextStub
    // 0xa36d1c: mov             x1, x0
    // 0xa36d20: ldur            x0, [fp, #-8]
    // 0xa36d24: StoreField: r1->field_f = r0
    //     0xa36d24: stur            w0, [x1, #0xf]
    // 0xa36d28: mov             x2, x1
    // 0xa36d2c: r1 = Function '<anonymous closure>':.
    //     0xa36d2c: add             x1, PP, #0x51, lsl #12  ; [pp+0x515d8] AnonymousClosure: (0xa32bc8), in [package:nuikit/src/widgets/image/image_caption.dart] _NImageCaptionState::_toggleCaption (0xa32b64)
    //     0xa36d30: ldr             x1, [x1, #0x5d8]
    // 0xa36d34: r0 = AllocateClosure()
    //     0xa36d34: bl              #0xec1630  ; AllocateClosureStub
    // 0xa36d38: ldur            x1, [fp, #-8]
    // 0xa36d3c: mov             x2, x0
    // 0xa36d40: r0 = setState()
    //     0xa36d40: bl              #0x649804  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa36d44: r0 = Null
    //     0xa36d44: mov             x0, NULL
    // 0xa36d48: LeaveFrame
    //     0xa36d48: mov             SP, fp
    //     0xa36d4c: ldp             fp, lr, [SP], #0x10
    // 0xa36d50: ret
    //     0xa36d50: ret             
    // 0xa36d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36d54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36d58: b               #0xa36d14
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa36d5c, size: 0xd0
    // 0xa36d5c: EnterFrame
    //     0xa36d5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa36d60: mov             fp, SP
    // 0xa36d64: AllocStack(0x20)
    //     0xa36d64: sub             SP, SP, #0x20
    // 0xa36d68: SetupParameters()
    //     0xa36d68: ldr             x0, [fp, #0x10]
    //     0xa36d6c: ldur            w1, [x0, #0x17]
    //     0xa36d70: add             x1, x1, HEAP, lsl #32
    //     0xa36d74: stur            x1, [fp, #-8]
    // 0xa36d78: CheckStackOverflow
    //     0xa36d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36d7c: cmp             SP, x16
    //     0xa36d80: b.ls            #0xa36e20
    // 0xa36d84: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa36d84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa36d88: ldr             x0, [x0, #0x2670]
    //     0xa36d8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa36d90: cmp             w0, w16
    //     0xa36d94: b.ne            #0xa36da0
    //     0xa36d98: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa36d9c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa36da0: r1 = Null
    //     0xa36da0: mov             x1, NULL
    // 0xa36da4: r2 = 4
    //     0xa36da4: movz            x2, #0x4
    // 0xa36da8: r0 = AllocateArray()
    //     0xa36da8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa36dac: r16 = "author"
    //     0xa36dac: add             x16, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0xa36db0: ldr             x16, [x16, #0xbf8]
    // 0xa36db4: StoreField: r0->field_f = r16
    //     0xa36db4: stur            w16, [x0, #0xf]
    // 0xa36db8: ldur            x1, [fp, #-8]
    // 0xa36dbc: LoadField: r2 = r1->field_f
    //     0xa36dbc: ldur            w2, [x1, #0xf]
    // 0xa36dc0: DecompressPointer r2
    //     0xa36dc0: add             x2, x2, HEAP, lsl #32
    // 0xa36dc4: LoadField: r1 = r2->field_b
    //     0xa36dc4: ldur            w1, [x2, #0xb]
    // 0xa36dc8: DecompressPointer r1
    //     0xa36dc8: add             x1, x1, HEAP, lsl #32
    // 0xa36dcc: cmp             w1, NULL
    // 0xa36dd0: b.eq            #0xa36e28
    // 0xa36dd4: LoadField: r2 = r1->field_b
    //     0xa36dd4: ldur            w2, [x1, #0xb]
    // 0xa36dd8: DecompressPointer r2
    //     0xa36dd8: add             x2, x2, HEAP, lsl #32
    // 0xa36ddc: LoadField: r1 = r2->field_33
    //     0xa36ddc: ldur            w1, [x2, #0x33]
    // 0xa36de0: DecompressPointer r1
    //     0xa36de0: add             x1, x1, HEAP, lsl #32
    // 0xa36de4: StoreField: r0->field_13 = r1
    //     0xa36de4: stur            w1, [x0, #0x13]
    // 0xa36de8: r16 = <String, Author?>
    //     0xa36de8: add             x16, PP, #0x51, lsl #12  ; [pp+0x515e0] TypeArguments: <String, Author?>
    //     0xa36dec: ldr             x16, [x16, #0x5e0]
    // 0xa36df0: stp             x0, x16, [SP]
    // 0xa36df4: r0 = Map._fromLiteral()
    //     0xa36df4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa36df8: r16 = "/article/article-author"
    //     0xa36df8: add             x16, PP, #0x51, lsl #12  ; [pp+0x515e8] "/article/article-author"
    //     0xa36dfc: ldr             x16, [x16, #0x5e8]
    // 0xa36e00: stp             x16, NULL, [SP, #8]
    // 0xa36e04: str             x0, [SP]
    // 0xa36e08: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa36e08: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa36e0c: ldr             x4, [x4, #0x478]
    // 0xa36e10: r0 = GetNavigation.toNamed()
    //     0xa36e10: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa36e14: LeaveFrame
    //     0xa36e14: mov             SP, fp
    //     0xa36e18: ldp             fp, lr, [SP], #0x10
    // 0xa36e1c: ret
    //     0xa36e1c: ret             
    // 0xa36e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36e24: b               #0xa36d84
    // 0xa36e28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa36e28: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Text <anonymous closure>(dynamic, ReadingPref, QuranTranslationLanguage) {
    // ** addr: 0xa36e5c, size: 0x118
    // 0xa36e5c: EnterFrame
    //     0xa36e5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa36e60: mov             fp, SP
    // 0xa36e64: AllocStack(0x18)
    //     0xa36e64: sub             SP, SP, #0x18
    // 0xa36e68: SetupParameters()
    //     0xa36e68: ldr             x0, [fp, #0x20]
    //     0xa36e6c: ldur            w1, [x0, #0x17]
    //     0xa36e70: add             x1, x1, HEAP, lsl #32
    // 0xa36e74: CheckStackOverflow
    //     0xa36e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36e78: cmp             SP, x16
    //     0xa36e7c: b.ls            #0xa36f4c
    // 0xa36e80: LoadField: r0 = r1->field_f
    //     0xa36e80: ldur            w0, [x1, #0xf]
    // 0xa36e84: DecompressPointer r0
    //     0xa36e84: add             x0, x0, HEAP, lsl #32
    // 0xa36e88: LoadField: r1 = r0->field_b
    //     0xa36e88: ldur            w1, [x0, #0xb]
    // 0xa36e8c: DecompressPointer r1
    //     0xa36e8c: add             x1, x1, HEAP, lsl #32
    // 0xa36e90: cmp             w1, NULL
    // 0xa36e94: b.eq            #0xa36f54
    // 0xa36e98: LoadField: r0 = r1->field_b
    //     0xa36e98: ldur            w0, [x1, #0xb]
    // 0xa36e9c: DecompressPointer r0
    //     0xa36e9c: add             x0, x0, HEAP, lsl #32
    // 0xa36ea0: LoadField: r1 = r0->field_f
    //     0xa36ea0: ldur            w1, [x0, #0xf]
    // 0xa36ea4: DecompressPointer r1
    //     0xa36ea4: add             x1, x1, HEAP, lsl #32
    // 0xa36ea8: stur            x1, [fp, #-8]
    // 0xa36eac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa36eac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa36eb0: ldr             x0, [x0, #0x2670]
    //     0xa36eb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa36eb8: cmp             w0, w16
    //     0xa36ebc: b.ne            #0xa36ec8
    //     0xa36ec0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa36ec4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa36ec8: r0 = GetNavigation.textTheme()
    //     0xa36ec8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xa36ecc: LoadField: r1 = r0->field_f
    //     0xa36ecc: ldur            w1, [x0, #0xf]
    // 0xa36ed0: DecompressPointer r1
    //     0xa36ed0: add             x1, x1, HEAP, lsl #32
    // 0xa36ed4: cmp             w1, NULL
    // 0xa36ed8: b.eq            #0xa36f58
    // 0xa36edc: ldr             x0, [fp, #0x18]
    // 0xa36ee0: LoadField: d0 = r0->field_f
    //     0xa36ee0: ldur            d0, [x0, #0xf]
    // 0xa36ee4: d1 = 1.200000
    //     0xa36ee4: add             x17, PP, #0x25, lsl #12  ; [pp+0x25d28] IMM: double(1.2) from 0x3ff3333333333333
    //     0xa36ee8: ldr             d1, [x17, #0xd28]
    // 0xa36eec: fmul            d2, d0, d1
    // 0xa36ef0: r0 = inline_Allocate_Double()
    //     0xa36ef0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa36ef4: add             x0, x0, #0x10
    //     0xa36ef8: cmp             x2, x0
    //     0xa36efc: b.ls            #0xa36f5c
    //     0xa36f00: str             x0, [THR, #0x50]  ; THR::top
    //     0xa36f04: sub             x0, x0, #0xf
    //     0xa36f08: movz            x2, #0xe15c
    //     0xa36f0c: movk            x2, #0x3, lsl #16
    //     0xa36f10: stur            x2, [x0, #-1]
    // 0xa36f14: StoreField: r0->field_7 = d2
    //     0xa36f14: stur            d2, [x0, #7]
    // 0xa36f18: str             x0, [SP]
    // 0xa36f1c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xa36f1c: add             x4, PP, #0x27, lsl #12  ; [pp+0x27088] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xa36f20: ldr             x4, [x4, #0x88]
    // 0xa36f24: r0 = copyWith()
    //     0xa36f24: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa36f28: stur            x0, [fp, #-0x10]
    // 0xa36f2c: r0 = Text()
    //     0xa36f2c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa36f30: ldur            x1, [fp, #-8]
    // 0xa36f34: StoreField: r0->field_b = r1
    //     0xa36f34: stur            w1, [x0, #0xb]
    // 0xa36f38: ldur            x1, [fp, #-0x10]
    // 0xa36f3c: StoreField: r0->field_13 = r1
    //     0xa36f3c: stur            w1, [x0, #0x13]
    // 0xa36f40: LeaveFrame
    //     0xa36f40: mov             SP, fp
    //     0xa36f44: ldp             fp, lr, [SP], #0x10
    // 0xa36f48: ret
    //     0xa36f48: ret             
    // 0xa36f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa36f4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa36f50: b               #0xa36e80
    // 0xa36f54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa36f54: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa36f58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa36f58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa36f5c: SaveReg d2
    //     0xa36f5c: str             q2, [SP, #-0x10]!
    // 0xa36f60: SaveReg r1
    //     0xa36f60: str             x1, [SP, #-8]!
    // 0xa36f64: r0 = AllocateDouble()
    //     0xa36f64: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa36f68: RestoreReg r1
    //     0xa36f68: ldr             x1, [SP], #8
    // 0xa36f6c: RestoreReg d2
    //     0xa36f6c: ldr             q2, [SP], #0x10
    // 0xa36f70: b               #0xa36f14
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa36f74, size: 0xd0
    // 0xa36f74: EnterFrame
    //     0xa36f74: stp             fp, lr, [SP, #-0x10]!
    //     0xa36f78: mov             fp, SP
    // 0xa36f7c: AllocStack(0x20)
    //     0xa36f7c: sub             SP, SP, #0x20
    // 0xa36f80: SetupParameters()
    //     0xa36f80: ldr             x0, [fp, #0x10]
    //     0xa36f84: ldur            w1, [x0, #0x17]
    //     0xa36f88: add             x1, x1, HEAP, lsl #32
    //     0xa36f8c: stur            x1, [fp, #-8]
    // 0xa36f90: CheckStackOverflow
    //     0xa36f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa36f94: cmp             SP, x16
    //     0xa36f98: b.ls            #0xa37038
    // 0xa36f9c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa36f9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa36fa0: ldr             x0, [x0, #0x2670]
    //     0xa36fa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa36fa8: cmp             w0, w16
    //     0xa36fac: b.ne            #0xa36fb8
    //     0xa36fb0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa36fb4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa36fb8: r1 = Null
    //     0xa36fb8: mov             x1, NULL
    // 0xa36fbc: r2 = 4
    //     0xa36fbc: movz            x2, #0x4
    // 0xa36fc0: r0 = AllocateArray()
    //     0xa36fc0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa36fc4: r16 = "prefix"
    //     0xa36fc4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0xa36fc8: ldr             x16, [x16, #0x970]
    // 0xa36fcc: StoreField: r0->field_f = r16
    //     0xa36fcc: stur            w16, [x0, #0xf]
    // 0xa36fd0: ldur            x1, [fp, #-8]
    // 0xa36fd4: LoadField: r2 = r1->field_f
    //     0xa36fd4: ldur            w2, [x1, #0xf]
    // 0xa36fd8: DecompressPointer r2
    //     0xa36fd8: add             x2, x2, HEAP, lsl #32
    // 0xa36fdc: LoadField: r1 = r2->field_b
    //     0xa36fdc: ldur            w1, [x2, #0xb]
    // 0xa36fe0: DecompressPointer r1
    //     0xa36fe0: add             x1, x1, HEAP, lsl #32
    // 0xa36fe4: cmp             w1, NULL
    // 0xa36fe8: b.eq            #0xa37040
    // 0xa36fec: LoadField: r2 = r1->field_b
    //     0xa36fec: ldur            w2, [x1, #0xb]
    // 0xa36ff0: DecompressPointer r2
    //     0xa36ff0: add             x2, x2, HEAP, lsl #32
    // 0xa36ff4: LoadField: r1 = r2->field_37
    //     0xa36ff4: ldur            w1, [x2, #0x37]
    // 0xa36ff8: DecompressPointer r1
    //     0xa36ff8: add             x1, x1, HEAP, lsl #32
    // 0xa36ffc: StoreField: r0->field_13 = r1
    //     0xa36ffc: stur            w1, [x0, #0x13]
    // 0xa37000: r16 = <String, String?>
    //     0xa37000: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xa37004: ldr             x16, [x16, #0x198]
    // 0xa37008: stp             x0, x16, [SP]
    // 0xa3700c: r0 = Map._fromLiteral()
    //     0xa3700c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa37010: r16 = "/article/article-prefix"
    //     0xa37010: add             x16, PP, #0x51, lsl #12  ; [pp+0x515f0] "/article/article-prefix"
    //     0xa37014: ldr             x16, [x16, #0x5f0]
    // 0xa37018: stp             x16, NULL, [SP, #8]
    // 0xa3701c: str             x0, [SP]
    // 0xa37020: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa37020: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa37024: ldr             x4, [x4, #0x478]
    // 0xa37028: r0 = GetNavigation.toNamed()
    //     0xa37028: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa3702c: LeaveFrame
    //     0xa3702c: mov             SP, fp
    //     0xa37030: ldp             fp, lr, [SP], #0x10
    // 0xa37034: ret
    //     0xa37034: ret             
    // 0xa37038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa37038: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3703c: b               #0xa36f9c
    // 0xa37040: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa37040: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa37044, size: 0x104
    // 0xa37044: EnterFrame
    //     0xa37044: stp             fp, lr, [SP, #-0x10]!
    //     0xa37048: mov             fp, SP
    // 0xa3704c: AllocStack(0x20)
    //     0xa3704c: sub             SP, SP, #0x20
    // 0xa37050: SetupParameters()
    //     0xa37050: ldr             x0, [fp, #0x10]
    //     0xa37054: ldur            w1, [x0, #0x17]
    //     0xa37058: add             x1, x1, HEAP, lsl #32
    //     0xa3705c: stur            x1, [fp, #-8]
    // 0xa37060: CheckStackOverflow
    //     0xa37060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa37064: cmp             SP, x16
    //     0xa37068: b.ls            #0xa3713c
    // 0xa3706c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa3706c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa37070: ldr             x0, [x0, #0x2670]
    //     0xa37074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa37078: cmp             w0, w16
    //     0xa3707c: b.ne            #0xa37088
    //     0xa37080: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xa37084: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa37088: r1 = Null
    //     0xa37088: mov             x1, NULL
    // 0xa3708c: r2 = 8
    //     0xa3708c: movz            x2, #0x8
    // 0xa37090: r0 = AllocateArray()
    //     0xa37090: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa37094: mov             x2, x0
    // 0xa37098: r16 = "id"
    //     0xa37098: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xa3709c: ldr             x16, [x16, #0x740]
    // 0xa370a0: StoreField: r2->field_f = r16
    //     0xa370a0: stur            w16, [x2, #0xf]
    // 0xa370a4: ldur            x0, [fp, #-8]
    // 0xa370a8: LoadField: r1 = r0->field_f
    //     0xa370a8: ldur            w1, [x0, #0xf]
    // 0xa370ac: DecompressPointer r1
    //     0xa370ac: add             x1, x1, HEAP, lsl #32
    // 0xa370b0: LoadField: r0 = r1->field_b
    //     0xa370b0: ldur            w0, [x1, #0xb]
    // 0xa370b4: DecompressPointer r0
    //     0xa370b4: add             x0, x0, HEAP, lsl #32
    // 0xa370b8: cmp             w0, NULL
    // 0xa370bc: b.eq            #0xa37144
    // 0xa370c0: LoadField: r1 = r0->field_b
    //     0xa370c0: ldur            w1, [x0, #0xb]
    // 0xa370c4: DecompressPointer r1
    //     0xa370c4: add             x1, x1, HEAP, lsl #32
    // 0xa370c8: LoadField: r3 = r1->field_1b
    //     0xa370c8: ldur            w3, [x1, #0x1b]
    // 0xa370cc: DecompressPointer r3
    //     0xa370cc: add             x3, x3, HEAP, lsl #32
    // 0xa370d0: LoadField: r4 = r3->field_13
    //     0xa370d0: ldur            x4, [x3, #0x13]
    // 0xa370d4: r0 = BoxInt64Instr(r4)
    //     0xa370d4: sbfiz           x0, x4, #1, #0x1f
    //     0xa370d8: cmp             x4, x0, asr #1
    //     0xa370dc: b.eq            #0xa370e8
    //     0xa370e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa370e4: stur            x4, [x0, #7]
    // 0xa370e8: StoreField: r2->field_13 = r0
    //     0xa370e8: stur            w0, [x2, #0x13]
    // 0xa370ec: r16 = "title"
    //     0xa370ec: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xa370f0: ldr             x16, [x16, #0x748]
    // 0xa370f4: ArrayStore: r2[0] = r16  ; List_4
    //     0xa370f4: stur            w16, [x2, #0x17]
    // 0xa370f8: LoadField: r0 = r3->field_1b
    //     0xa370f8: ldur            w0, [x3, #0x1b]
    // 0xa370fc: DecompressPointer r0
    //     0xa370fc: add             x0, x0, HEAP, lsl #32
    // 0xa37100: StoreField: r2->field_1b = r0
    //     0xa37100: stur            w0, [x2, #0x1b]
    // 0xa37104: r16 = <String, Object>
    //     0xa37104: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xa37108: ldr             x16, [x16, #0x790]
    // 0xa3710c: stp             x2, x16, [SP]
    // 0xa37110: r0 = Map._fromLiteral()
    //     0xa37110: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xa37114: r16 = "/article/article-category"
    //     0xa37114: add             x16, PP, #0xf, lsl #12  ; [pp+0xfcd8] "/article/article-category"
    //     0xa37118: ldr             x16, [x16, #0xcd8]
    // 0xa3711c: stp             x16, NULL, [SP, #8]
    // 0xa37120: str             x0, [SP]
    // 0xa37124: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa37124: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa37128: ldr             x4, [x4, #0x478]
    // 0xa3712c: r0 = GetNavigation.toNamed()
    //     0xa3712c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa37130: LeaveFrame
    //     0xa37130: mov             SP, fp
    //     0xa37134: ldp             fp, lr, [SP], #0x10
    // 0xa37138: ret
    //     0xa37138: ret             
    // 0xa3713c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3713c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa37140: b               #0xa3706c
    // 0xa37144: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa37144: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4716, size: 0x10, field offset: 0xc
//   const constructor, 
class ArticleDetailLayout extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa94504, size: 0x2c
    // 0xa94504: EnterFrame
    //     0xa94504: stp             fp, lr, [SP, #-0x10]!
    //     0xa94508: mov             fp, SP
    // 0xa9450c: mov             x0, x1
    // 0xa94510: r1 = <ArticleDetailLayout>
    //     0xa94510: add             x1, PP, #0x49, lsl #12  ; [pp+0x49d88] TypeArguments: <ArticleDetailLayout>
    //     0xa94514: ldr             x1, [x1, #0xd88]
    // 0xa94518: r0 = _ArticleDetailLayoutState()
    //     0xa94518: bl              #0xa94530  ; Allocate_ArticleDetailLayoutStateStub -> _ArticleDetailLayoutState (size=0x18)
    // 0xa9451c: r1 = false
    //     0xa9451c: add             x1, NULL, #0x30  ; false
    // 0xa94520: StoreField: r0->field_13 = r1
    //     0xa94520: stur            w1, [x0, #0x13]
    // 0xa94524: LeaveFrame
    //     0xa94524: mov             SP, fp
    //     0xa94528: ldp             fp, lr, [SP], #0x10
    // 0xa9452c: ret
    //     0xa9452c: ret             
  }
}
