// lib: , url: package:nuonline/app/modules/article/article_detail/views/article_detail_view.dart

// class id: 1050135, size: 0x8
class :: {
}

// class id: 4450, size: 0x14, field offset: 0x14
class ArticleDetailView extends GetWidget<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0x8624bc, size: 0xd8
    // 0x8624bc: EnterFrame
    //     0x8624bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8624c0: mov             fp, SP
    // 0x8624c4: AllocStack(0x18)
    //     0x8624c4: sub             SP, SP, #0x18
    // 0x8624c8: CheckStackOverflow
    //     0x8624c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8624cc: cmp             SP, x16
    //     0x8624d0: b.ls            #0x86258c
    // 0x8624d4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8624d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8624d8: ldr             x0, [x0, #0x2670]
    //     0x8624dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8624e0: cmp             w0, w16
    //     0x8624e4: b.ne            #0x8624f0
    //     0x8624e8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8624ec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8624f0: r0 = GetNavigation.arguments()
    //     0x8624f0: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8624f4: r16 = "id"
    //     0x8624f4: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x8624f8: ldr             x16, [x16, #0x740]
    // 0x8624fc: stp             x16, x0, [SP]
    // 0x862500: r4 = 0
    //     0x862500: movz            x4, #0
    // 0x862504: ldr             x0, [SP, #8]
    // 0x862508: r16 = UnlinkedCall_0x5f3c08
    //     0x862508: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c78] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x86250c: add             x16, x16, #0xc78
    // 0x862510: ldp             x5, lr, [x16]
    // 0x862514: blr             lr
    // 0x862518: mov             x3, x0
    // 0x86251c: r2 = Null
    //     0x86251c: mov             x2, NULL
    // 0x862520: r1 = Null
    //     0x862520: mov             x1, NULL
    // 0x862524: stur            x3, [fp, #-8]
    // 0x862528: branchIfSmi(r0, 0x862550)
    //     0x862528: tbz             w0, #0, #0x862550
    // 0x86252c: r4 = LoadClassIdInstr(r0)
    //     0x86252c: ldur            x4, [x0, #-1]
    //     0x862530: ubfx            x4, x4, #0xc, #0x14
    // 0x862534: sub             x4, x4, #0x3c
    // 0x862538: cmp             x4, #1
    // 0x86253c: b.ls            #0x862550
    // 0x862540: r8 = int
    //     0x862540: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x862544: r3 = Null
    //     0x862544: add             x3, PP, #0x40, lsl #12  ; [pp+0x40c88] Null
    //     0x862548: ldr             x3, [x3, #0xc88]
    // 0x86254c: r0 = int()
    //     0x86254c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x862550: ldur            x0, [fp, #-8]
    // 0x862554: r1 = 60
    //     0x862554: movz            x1, #0x3c
    // 0x862558: branchIfSmi(r0, 0x862564)
    //     0x862558: tbz             w0, #0, #0x862564
    // 0x86255c: r1 = LoadClassIdInstr(r0)
    //     0x86255c: ldur            x1, [x0, #-1]
    //     0x862560: ubfx            x1, x1, #0xc, #0x14
    // 0x862564: str             x0, [SP]
    // 0x862568: mov             x0, x1
    // 0x86256c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x86256c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x862570: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x862570: movz            x17, #0x2b03
    //     0x862574: add             lr, x0, x17
    //     0x862578: ldr             lr, [x21, lr, lsl #3]
    //     0x86257c: blr             lr
    // 0x862580: LeaveFrame
    //     0x862580: mov             SP, fp
    //     0x862584: ldp             fp, lr, [SP], #0x10
    // 0x862588: ret
    //     0x862588: ret             
    // 0x86258c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86258c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862590: b               #0x8624d4
  }
  _ build(/* No info */) {
    // ** addr: 0xbc1488, size: 0x524
    // 0xbc1488: EnterFrame
    //     0xbc1488: stp             fp, lr, [SP, #-0x10]!
    //     0xbc148c: mov             fp, SP
    // 0xbc1490: AllocStack(0x88)
    //     0xbc1490: sub             SP, SP, #0x88
    // 0xbc1494: SetupParameters(ArticleDetailView this /* r1 => r0, fp-0x8 */)
    //     0xbc1494: mov             x0, x1
    //     0xbc1498: stur            x1, [fp, #-8]
    // 0xbc149c: CheckStackOverflow
    //     0xbc149c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc14a0: cmp             SP, x16
    //     0xbc14a4: b.ls            #0xbc19a0
    // 0xbc14a8: r1 = 1
    //     0xbc14a8: movz            x1, #0x1
    // 0xbc14ac: r0 = AllocateContext()
    //     0xbc14ac: bl              #0xec126c  ; AllocateContextStub
    // 0xbc14b0: ldur            x2, [fp, #-8]
    // 0xbc14b4: stur            x0, [fp, #-0x10]
    // 0xbc14b8: StoreField: r0->field_f = r2
    //     0xbc14b8: stur            w2, [x0, #0xf]
    // 0xbc14bc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc14bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc14c0: ldr             x0, [x0, #0x2670]
    //     0xbc14c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc14c8: cmp             w0, w16
    //     0xbc14cc: b.ne            #0xbc14d8
    //     0xbc14d0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbc14d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc14d8: r0 = GetNavigation.isDarkMode()
    //     0xbc14d8: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xbc14dc: tbnz            w0, #4, #0xbc14f4
    // 0xbc14e0: r1 = _ConstMap len:3
    //     0xbc14e0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xbc14e4: ldr             x1, [x1, #0xbe8]
    // 0xbc14e8: r2 = 6
    //     0xbc14e8: movz            x2, #0x6
    // 0xbc14ec: r0 = []()
    //     0xbc14ec: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbc14f0: b               #0xbc14f8
    // 0xbc14f4: r0 = Instance_Color
    //     0xbc14f4: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xbc14f8: stur            x0, [fp, #-0x18]
    // 0xbc14fc: r0 = GetNavigation.textTheme()
    //     0xbc14fc: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xbc1500: LoadField: r1 = r0->field_f
    //     0xbc1500: ldur            w1, [x0, #0xf]
    // 0xbc1504: DecompressPointer r1
    //     0xbc1504: add             x1, x1, HEAP, lsl #32
    // 0xbc1508: cmp             w1, NULL
    // 0xbc150c: b.eq            #0xbc19a8
    // 0xbc1510: LoadField: r0 = r1->field_b
    //     0xbc1510: ldur            w0, [x1, #0xb]
    // 0xbc1514: DecompressPointer r0
    //     0xbc1514: add             x0, x0, HEAP, lsl #32
    // 0xbc1518: stur            x0, [fp, #-0x20]
    // 0xbc151c: r0 = GetNavigation.theme()
    //     0xbc151c: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xbc1520: r0 = GetNavigation.isDarkMode()
    //     0xbc1520: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xbc1524: tbnz            w0, #4, #0xbc1530
    // 0xbc1528: r0 = Instance_Color
    //     0xbc1528: ldr             x0, [PP, #0x5450]  ; [pp+0x5450] Obj!Color@e280e1
    // 0xbc152c: b               #0xbc1540
    // 0xbc1530: r1 = _ConstMap len:3
    //     0xbc1530: add             x1, PP, #0x23, lsl #12  ; [pp+0x23be8] Map<int, Color>(3)
    //     0xbc1534: ldr             x1, [x1, #0xbe8]
    // 0xbc1538: r2 = 6
    //     0xbc1538: movz            x2, #0x6
    // 0xbc153c: r0 = []()
    //     0xbc153c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xbc1540: stur            x0, [fp, #-0x28]
    // 0xbc1544: r0 = IconThemeData()
    //     0xbc1544: bl              #0x63d1c0  ; AllocateIconThemeDataStub -> IconThemeData (size=0x2c)
    // 0xbc1548: mov             x1, x0
    // 0xbc154c: ldur            x0, [fp, #-0x28]
    // 0xbc1550: stur            x1, [fp, #-0x30]
    // 0xbc1554: StoreField: r1->field_1b = r0
    //     0xbc1554: stur            w0, [x1, #0x1b]
    // 0xbc1558: r0 = GetNavigation.isDarkMode()
    //     0xbc1558: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xbc155c: tbnz            w0, #4, #0xbc156c
    // 0xbc1560: r0 = "assets/images/logo/main_logo_dark.svg"
    //     0xbc1560: add             x0, PP, #0x38, lsl #12  ; [pp+0x382d8] "assets/images/logo/main_logo_dark.svg"
    //     0xbc1564: ldr             x0, [x0, #0x2d8]
    // 0xbc1568: b               #0xbc1574
    // 0xbc156c: r0 = "assets/images/logo/main_logo.svg"
    //     0xbc156c: add             x0, PP, #0x38, lsl #12  ; [pp+0x382d0] "assets/images/logo/main_logo.svg"
    //     0xbc1570: ldr             x0, [x0, #0x2d0]
    // 0xbc1574: ldur            x2, [fp, #-8]
    // 0xbc1578: stur            x0, [fp, #-0x28]
    // 0xbc157c: r0 = SvgPicture()
    //     0xbc157c: bl              #0xacfad4  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbc1580: stur            x0, [fp, #-0x38]
    // 0xbc1584: r16 = 24.000000
    //     0xbc1584: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xbc1588: ldr             x16, [x16, #0x368]
    // 0xbc158c: r30 = "nuikit"
    //     0xbc158c: add             lr, PP, #0x25, lsl #12  ; [pp+0x25798] "nuikit"
    //     0xbc1590: ldr             lr, [lr, #0x798]
    // 0xbc1594: stp             lr, x16, [SP]
    // 0xbc1598: mov             x1, x0
    // 0xbc159c: ldur            x2, [fp, #-0x28]
    // 0xbc15a0: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, package, 0x3, null]
    //     0xbc15a0: add             x4, PP, #0x38, lsl #12  ; [pp+0x382e0] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "package", 0x3, Null]
    //     0xbc15a4: ldr             x4, [x4, #0x2e0]
    // 0xbc15a8: r0 = SvgPicture.asset()
    //     0xbc15a8: bl              #0xabab8c  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbc15ac: ldur            x2, [fp, #-0x10]
    // 0xbc15b0: r1 = Function '<anonymous closure>':.
    //     0xbc15b0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ad8] AnonymousClosure: (0xbc1cf0), in [package:nuonline/app/modules/article/article_detail/views/article_detail_view.dart] ArticleDetailView::build (0xbc1488)
    //     0xbc15b4: ldr             x1, [x1, #0xad8]
    // 0xbc15b8: r0 = AllocateClosure()
    //     0xbc15b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc15bc: stur            x0, [fp, #-0x28]
    // 0xbc15c0: r0 = IconButton()
    //     0xbc15c0: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xbc15c4: mov             x1, x0
    // 0xbc15c8: ldur            x0, [fp, #-0x28]
    // 0xbc15cc: stur            x1, [fp, #-0x40]
    // 0xbc15d0: StoreField: r1->field_3b = r0
    //     0xbc15d0: stur            w0, [x1, #0x3b]
    // 0xbc15d4: r0 = false
    //     0xbc15d4: add             x0, NULL, #0x30  ; false
    // 0xbc15d8: StoreField: r1->field_47 = r0
    //     0xbc15d8: stur            w0, [x1, #0x47]
    // 0xbc15dc: r2 = Instance_NAdaptiveIcon
    //     0xbc15dc: add             x2, PP, #0x29, lsl #12  ; [pp+0x29a48] Obj!NAdaptiveIcon@e20cf1
    //     0xbc15e0: ldr             x2, [x2, #0xa48]
    // 0xbc15e4: StoreField: r1->field_1f = r2
    //     0xbc15e4: stur            w2, [x1, #0x1f]
    // 0xbc15e8: r2 = Instance__IconButtonVariant
    //     0xbc15e8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xbc15ec: ldr             x2, [x2, #0xf78]
    // 0xbc15f0: StoreField: r1->field_63 = r2
    //     0xbc15f0: stur            w2, [x1, #0x63]
    // 0xbc15f4: r0 = Obx()
    //     0xbc15f4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbc15f8: ldur            x2, [fp, #-0x10]
    // 0xbc15fc: r1 = Function '<anonymous closure>':.
    //     0xbc15fc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ae0] AnonymousClosure: (0xbc1a28), in [package:nuonline/app/modules/article/article_detail/views/article_detail_view.dart] ArticleDetailView::build (0xbc1488)
    //     0xbc1600: ldr             x1, [x1, #0xae0]
    // 0xbc1604: stur            x0, [fp, #-0x10]
    // 0xbc1608: r0 = AllocateClosure()
    //     0xbc1608: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc160c: mov             x1, x0
    // 0xbc1610: ldur            x0, [fp, #-0x10]
    // 0xbc1614: StoreField: r0->field_b = r1
    //     0xbc1614: stur            w1, [x0, #0xb]
    // 0xbc1618: r1 = Function '<anonymous closure>':.
    //     0xbc1618: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ae8] AnonymousClosure: (0xbc19ac), in [package:nuonline/app/modules/article/article_detail/views/article_detail_view.dart] ArticleDetailView::build (0xbc1488)
    //     0xbc161c: ldr             x1, [x1, #0xae8]
    // 0xbc1620: r2 = Null
    //     0xbc1620: mov             x2, NULL
    // 0xbc1624: r0 = AllocateClosure()
    //     0xbc1624: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc1628: stur            x0, [fp, #-0x28]
    // 0xbc162c: r0 = IconButton()
    //     0xbc162c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xbc1630: mov             x3, x0
    // 0xbc1634: ldur            x0, [fp, #-0x28]
    // 0xbc1638: stur            x3, [fp, #-0x48]
    // 0xbc163c: StoreField: r3->field_3b = r0
    //     0xbc163c: stur            w0, [x3, #0x3b]
    // 0xbc1640: r0 = false
    //     0xbc1640: add             x0, NULL, #0x30  ; false
    // 0xbc1644: StoreField: r3->field_47 = r0
    //     0xbc1644: stur            w0, [x3, #0x47]
    // 0xbc1648: r1 = Instance_Icon
    //     0xbc1648: add             x1, PP, #0x27, lsl #12  ; [pp+0x27c78] Obj!Icon@e24631
    //     0xbc164c: ldr             x1, [x1, #0xc78]
    // 0xbc1650: StoreField: r3->field_1f = r1
    //     0xbc1650: stur            w1, [x3, #0x1f]
    // 0xbc1654: r1 = Instance__IconButtonVariant
    //     0xbc1654: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xbc1658: ldr             x1, [x1, #0xf78]
    // 0xbc165c: StoreField: r3->field_63 = r1
    //     0xbc165c: stur            w1, [x3, #0x63]
    // 0xbc1660: r1 = Null
    //     0xbc1660: mov             x1, NULL
    // 0xbc1664: r2 = 6
    //     0xbc1664: movz            x2, #0x6
    // 0xbc1668: r0 = AllocateArray()
    //     0xbc1668: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc166c: mov             x2, x0
    // 0xbc1670: ldur            x0, [fp, #-0x40]
    // 0xbc1674: stur            x2, [fp, #-0x28]
    // 0xbc1678: StoreField: r2->field_f = r0
    //     0xbc1678: stur            w0, [x2, #0xf]
    // 0xbc167c: ldur            x0, [fp, #-0x10]
    // 0xbc1680: StoreField: r2->field_13 = r0
    //     0xbc1680: stur            w0, [x2, #0x13]
    // 0xbc1684: ldur            x0, [fp, #-0x48]
    // 0xbc1688: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc1688: stur            w0, [x2, #0x17]
    // 0xbc168c: r1 = <Widget>
    //     0xbc168c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc1690: r0 = AllocateGrowableArray()
    //     0xbc1690: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc1694: mov             x1, x0
    // 0xbc1698: ldur            x0, [fp, #-0x28]
    // 0xbc169c: stur            x1, [fp, #-0x10]
    // 0xbc16a0: StoreField: r1->field_f = r0
    //     0xbc16a0: stur            w0, [x1, #0xf]
    // 0xbc16a4: r0 = 6
    //     0xbc16a4: movz            x0, #0x6
    // 0xbc16a8: StoreField: r1->field_b = r0
    //     0xbc16a8: stur            w0, [x1, #0xb]
    // 0xbc16ac: r0 = AppBar()
    //     0xbc16ac: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xbc16b0: stur            x0, [fp, #-0x28]
    // 0xbc16b4: r16 = Instance_Color
    //     0xbc16b4: ldr             x16, [PP, #0x56f8]  ; [pp+0x56f8] Obj!Color@e26f41
    // 0xbc16b8: ldur            lr, [fp, #-0x18]
    // 0xbc16bc: stp             lr, x16, [SP, #0x28]
    // 0xbc16c0: ldur            x16, [fp, #-0x20]
    // 0xbc16c4: stp             NULL, x16, [SP, #0x18]
    // 0xbc16c8: ldur            x16, [fp, #-0x30]
    // 0xbc16cc: ldur            lr, [fp, #-0x38]
    // 0xbc16d0: stp             lr, x16, [SP, #8]
    // 0xbc16d4: ldur            x16, [fp, #-0x10]
    // 0xbc16d8: str             x16, [SP]
    // 0xbc16dc: mov             x1, x0
    // 0xbc16e0: r4 = const [0, 0x8, 0x7, 0x1, actions, 0x7, backgroundColor, 0x2, foregroundColor, 0x3, iconTheme, 0x5, surfaceTintColor, 0x1, systemOverlayStyle, 0x4, title, 0x6, null]
    //     0xbc16e0: add             x4, PP, #0x40, lsl #12  ; [pp+0x40af0] List(19) [0, 0x8, 0x7, 0x1, "actions", 0x7, "backgroundColor", 0x2, "foregroundColor", 0x3, "iconTheme", 0x5, "surfaceTintColor", 0x1, "systemOverlayStyle", 0x4, "title", 0x6, Null]
    //     0xbc16e4: ldr             x4, [x4, #0xaf0]
    // 0xbc16e8: r0 = AppBar()
    //     0xbc16e8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xbc16ec: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc16ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc16f0: ldr             x0, [x0, #0x26d0]
    //     0xbc16f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc16f8: cmp             w0, w16
    //     0xbc16fc: b.ne            #0xbc170c
    //     0xbc1700: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc1704: ldr             x2, [x2, #0xb90]
    //     0xbc1708: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc170c: mov             x1, x0
    // 0xbc1710: ldur            x2, [fp, #-8]
    // 0xbc1714: r0 = []()
    //     0xbc1714: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1718: mov             x3, x0
    // 0xbc171c: ldur            x0, [fp, #-8]
    // 0xbc1720: stur            x3, [fp, #-0x10]
    // 0xbc1724: LoadField: r2 = r0->field_b
    //     0xbc1724: ldur            w2, [x0, #0xb]
    // 0xbc1728: DecompressPointer r2
    //     0xbc1728: add             x2, x2, HEAP, lsl #32
    // 0xbc172c: mov             x0, x3
    // 0xbc1730: r1 = Null
    //     0xbc1730: mov             x1, NULL
    // 0xbc1734: cmp             w2, NULL
    // 0xbc1738: b.eq            #0xbc175c
    // 0xbc173c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc173c: ldur            w4, [x2, #0x17]
    // 0xbc1740: DecompressPointer r4
    //     0xbc1740: add             x4, x4, HEAP, lsl #32
    // 0xbc1744: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1744: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1748: ldr             x8, [x8, #0xb98]
    // 0xbc174c: LoadField: r9 = r4->field_7
    //     0xbc174c: ldur            x9, [x4, #7]
    // 0xbc1750: r3 = Null
    //     0xbc1750: add             x3, PP, #0x40, lsl #12  ; [pp+0x40af8] Null
    //     0xbc1754: ldr             x3, [x3, #0xaf8]
    // 0xbc1758: blr             x9
    // 0xbc175c: r1 = Function '<anonymous closure>':.
    //     0xbc175c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b08] AnonymousClosure: (0xa42120), in [package:nuonline/app/modules/tutorial/tutorial_detail/views/tutorial_detail_view.dart] __TutorialDetailLayoutState::buildArticle (0xa41eec)
    //     0xbc1760: ldr             x1, [x1, #0xb08]
    // 0xbc1764: r2 = Null
    //     0xbc1764: mov             x2, NULL
    // 0xbc1768: r0 = AllocateClosure()
    //     0xbc1768: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc176c: r16 = <ArticleDetail>
    //     0xbc176c: ldr             x16, [PP, #0x7b80]  ; [pp+0x7b80] TypeArguments: <ArticleDetail>
    // 0xbc1770: ldur            lr, [fp, #-0x10]
    // 0xbc1774: stp             lr, x16, [SP, #0x10]
    // 0xbc1778: r16 = Instance_NSkeleton
    //     0xbc1778: add             x16, PP, #0x29, lsl #12  ; [pp+0x29868] Obj!NSkeleton@e20921
    //     0xbc177c: ldr             x16, [x16, #0x868]
    // 0xbc1780: stp             x16, x0, [SP]
    // 0xbc1784: r4 = const [0x1, 0x3, 0x3, 0x2, onLoading, 0x2, null]
    //     0xbc1784: add             x4, PP, #0x25, lsl #12  ; [pp+0x25718] List(7) [0x1, 0x3, 0x3, 0x2, "onLoading", 0x2, Null]
    //     0xbc1788: ldr             x4, [x4, #0x718]
    // 0xbc178c: r0 = StateExt.obx()
    //     0xbc178c: bl              #0xa41a60  ; [package:get/get_state_manager/src/rx_flutter/rx_notifier.dart] ::StateExt.obx
    // 0xbc1790: r1 = <FlexParentData>
    //     0xbc1790: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbc1794: ldr             x1, [x1, #0x720]
    // 0xbc1798: stur            x0, [fp, #-8]
    // 0xbc179c: r0 = Expanded()
    //     0xbc179c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc17a0: mov             x3, x0
    // 0xbc17a4: r0 = 1
    //     0xbc17a4: movz            x0, #0x1
    // 0xbc17a8: stur            x3, [fp, #-0x10]
    // 0xbc17ac: StoreField: r3->field_13 = r0
    //     0xbc17ac: stur            x0, [x3, #0x13]
    // 0xbc17b0: r0 = Instance_FlexFit
    //     0xbc17b0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbc17b4: ldr             x0, [x0, #0x728]
    // 0xbc17b8: StoreField: r3->field_1b = r0
    //     0xbc17b8: stur            w0, [x3, #0x1b]
    // 0xbc17bc: ldur            x0, [fp, #-8]
    // 0xbc17c0: StoreField: r3->field_b = r0
    //     0xbc17c0: stur            w0, [x3, #0xb]
    // 0xbc17c4: r1 = Null
    //     0xbc17c4: mov             x1, NULL
    // 0xbc17c8: r2 = 2
    //     0xbc17c8: movz            x2, #0x2
    // 0xbc17cc: r0 = AllocateArray()
    //     0xbc17cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc17d0: mov             x2, x0
    // 0xbc17d4: ldur            x0, [fp, #-0x10]
    // 0xbc17d8: stur            x2, [fp, #-8]
    // 0xbc17dc: StoreField: r2->field_f = r0
    //     0xbc17dc: stur            w0, [x2, #0xf]
    // 0xbc17e0: r1 = <Widget>
    //     0xbc17e0: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc17e4: r0 = AllocateGrowableArray()
    //     0xbc17e4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc17e8: mov             x1, x0
    // 0xbc17ec: ldur            x0, [fp, #-8]
    // 0xbc17f0: stur            x1, [fp, #-0x10]
    // 0xbc17f4: StoreField: r1->field_f = r0
    //     0xbc17f4: stur            w0, [x1, #0xf]
    // 0xbc17f8: r0 = 2
    //     0xbc17f8: movz            x0, #0x2
    // 0xbc17fc: StoreField: r1->field_b = r0
    //     0xbc17fc: stur            w0, [x1, #0xb]
    // 0xbc1800: r0 = find()
    //     0xbc1800: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc1804: mov             x1, x0
    // 0xbc1808: r0 = _adsVisibility()
    //     0xbc1808: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc180c: mov             x2, x0
    // 0xbc1810: r1 = Null
    //     0xbc1810: mov             x1, NULL
    // 0xbc1814: r0 = AdsConfig.fromJson()
    //     0xbc1814: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc1818: LoadField: r1 = r0->field_f
    //     0xbc1818: ldur            w1, [x0, #0xf]
    // 0xbc181c: DecompressPointer r1
    //     0xbc181c: add             x1, x1, HEAP, lsl #32
    // 0xbc1820: LoadField: r0 = r1->field_7
    //     0xbc1820: ldur            w0, [x1, #7]
    // 0xbc1824: DecompressPointer r0
    //     0xbc1824: add             x0, x0, HEAP, lsl #32
    // 0xbc1828: tbnz            w0, #4, #0xbc18ec
    // 0xbc182c: ldur            x1, [fp, #-0x10]
    // 0xbc1830: r0 = find()
    //     0xbc1830: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc1834: mov             x1, x0
    // 0xbc1838: r0 = _adsVisibility()
    //     0xbc1838: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc183c: mov             x2, x0
    // 0xbc1840: r1 = Null
    //     0xbc1840: mov             x1, NULL
    // 0xbc1844: r0 = AdsConfig.fromJson()
    //     0xbc1844: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc1848: LoadField: r1 = r0->field_f
    //     0xbc1848: ldur            w1, [x0, #0xf]
    // 0xbc184c: DecompressPointer r1
    //     0xbc184c: add             x1, x1, HEAP, lsl #32
    // 0xbc1850: LoadField: r0 = r1->field_f
    //     0xbc1850: ldur            w0, [x1, #0xf]
    // 0xbc1854: DecompressPointer r0
    //     0xbc1854: add             x0, x0, HEAP, lsl #32
    // 0xbc1858: stur            x0, [fp, #-8]
    // 0xbc185c: r0 = AdmobBannerWidget()
    //     0xbc185c: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xbc1860: mov             x2, x0
    // 0xbc1864: ldur            x0, [fp, #-8]
    // 0xbc1868: stur            x2, [fp, #-0x18]
    // 0xbc186c: StoreField: r2->field_b = r0
    //     0xbc186c: stur            w0, [x2, #0xb]
    // 0xbc1870: ldur            x0, [fp, #-0x10]
    // 0xbc1874: LoadField: r1 = r0->field_b
    //     0xbc1874: ldur            w1, [x0, #0xb]
    // 0xbc1878: LoadField: r3 = r0->field_f
    //     0xbc1878: ldur            w3, [x0, #0xf]
    // 0xbc187c: DecompressPointer r3
    //     0xbc187c: add             x3, x3, HEAP, lsl #32
    // 0xbc1880: LoadField: r4 = r3->field_b
    //     0xbc1880: ldur            w4, [x3, #0xb]
    // 0xbc1884: r3 = LoadInt32Instr(r1)
    //     0xbc1884: sbfx            x3, x1, #1, #0x1f
    // 0xbc1888: stur            x3, [fp, #-0x50]
    // 0xbc188c: r1 = LoadInt32Instr(r4)
    //     0xbc188c: sbfx            x1, x4, #1, #0x1f
    // 0xbc1890: cmp             x3, x1
    // 0xbc1894: b.ne            #0xbc18a0
    // 0xbc1898: mov             x1, x0
    // 0xbc189c: r0 = _growToNextCapacity()
    //     0xbc189c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc18a0: ldur            x2, [fp, #-0x10]
    // 0xbc18a4: ldur            x3, [fp, #-0x50]
    // 0xbc18a8: add             x0, x3, #1
    // 0xbc18ac: lsl             x1, x0, #1
    // 0xbc18b0: StoreField: r2->field_b = r1
    //     0xbc18b0: stur            w1, [x2, #0xb]
    // 0xbc18b4: LoadField: r1 = r2->field_f
    //     0xbc18b4: ldur            w1, [x2, #0xf]
    // 0xbc18b8: DecompressPointer r1
    //     0xbc18b8: add             x1, x1, HEAP, lsl #32
    // 0xbc18bc: ldur            x0, [fp, #-0x18]
    // 0xbc18c0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbc18c0: add             x25, x1, x3, lsl #2
    //     0xbc18c4: add             x25, x25, #0xf
    //     0xbc18c8: str             w0, [x25]
    //     0xbc18cc: tbz             w0, #0, #0xbc18e8
    //     0xbc18d0: ldurb           w16, [x1, #-1]
    //     0xbc18d4: ldurb           w17, [x0, #-1]
    //     0xbc18d8: and             x16, x17, x16, lsr #2
    //     0xbc18dc: tst             x16, HEAP, lsr #32
    //     0xbc18e0: b.eq            #0xbc18e8
    //     0xbc18e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc18e8: b               #0xbc18f0
    // 0xbc18ec: ldur            x2, [fp, #-0x10]
    // 0xbc18f0: ldur            x0, [fp, #-0x28]
    // 0xbc18f4: r0 = Column()
    //     0xbc18f4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc18f8: mov             x1, x0
    // 0xbc18fc: r0 = Instance_Axis
    //     0xbc18fc: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc1900: stur            x1, [fp, #-8]
    // 0xbc1904: StoreField: r1->field_f = r0
    //     0xbc1904: stur            w0, [x1, #0xf]
    // 0xbc1908: r0 = Instance_MainAxisAlignment
    //     0xbc1908: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc190c: ldr             x0, [x0, #0x730]
    // 0xbc1910: StoreField: r1->field_13 = r0
    //     0xbc1910: stur            w0, [x1, #0x13]
    // 0xbc1914: r0 = Instance_MainAxisSize
    //     0xbc1914: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc1918: ldr             x0, [x0, #0x738]
    // 0xbc191c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc191c: stur            w0, [x1, #0x17]
    // 0xbc1920: r0 = Instance_CrossAxisAlignment
    //     0xbc1920: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc1924: ldr             x0, [x0, #0x740]
    // 0xbc1928: StoreField: r1->field_1b = r0
    //     0xbc1928: stur            w0, [x1, #0x1b]
    // 0xbc192c: r0 = Instance_VerticalDirection
    //     0xbc192c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc1930: ldr             x0, [x0, #0x748]
    // 0xbc1934: StoreField: r1->field_23 = r0
    //     0xbc1934: stur            w0, [x1, #0x23]
    // 0xbc1938: r0 = Instance_Clip
    //     0xbc1938: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc193c: ldr             x0, [x0, #0x750]
    // 0xbc1940: StoreField: r1->field_2b = r0
    //     0xbc1940: stur            w0, [x1, #0x2b]
    // 0xbc1944: StoreField: r1->field_2f = rZR
    //     0xbc1944: stur            xzr, [x1, #0x2f]
    // 0xbc1948: ldur            x0, [fp, #-0x10]
    // 0xbc194c: StoreField: r1->field_b = r0
    //     0xbc194c: stur            w0, [x1, #0xb]
    // 0xbc1950: r0 = Scaffold()
    //     0xbc1950: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xbc1954: ldur            x1, [fp, #-0x28]
    // 0xbc1958: StoreField: r0->field_13 = r1
    //     0xbc1958: stur            w1, [x0, #0x13]
    // 0xbc195c: ldur            x1, [fp, #-8]
    // 0xbc1960: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc1960: stur            w1, [x0, #0x17]
    // 0xbc1964: r1 = Instance_AlignmentDirectional
    //     0xbc1964: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xbc1968: ldr             x1, [x1, #0x758]
    // 0xbc196c: StoreField: r0->field_2b = r1
    //     0xbc196c: stur            w1, [x0, #0x2b]
    // 0xbc1970: r1 = true
    //     0xbc1970: add             x1, NULL, #0x20  ; true
    // 0xbc1974: StoreField: r0->field_53 = r1
    //     0xbc1974: stur            w1, [x0, #0x53]
    // 0xbc1978: r2 = Instance_DragStartBehavior
    //     0xbc1978: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbc197c: StoreField: r0->field_57 = r2
    //     0xbc197c: stur            w2, [x0, #0x57]
    // 0xbc1980: r2 = false
    //     0xbc1980: add             x2, NULL, #0x30  ; false
    // 0xbc1984: StoreField: r0->field_b = r2
    //     0xbc1984: stur            w2, [x0, #0xb]
    // 0xbc1988: StoreField: r0->field_f = r2
    //     0xbc1988: stur            w2, [x0, #0xf]
    // 0xbc198c: StoreField: r0->field_5f = r1
    //     0xbc198c: stur            w1, [x0, #0x5f]
    // 0xbc1990: StoreField: r0->field_63 = r1
    //     0xbc1990: stur            w1, [x0, #0x63]
    // 0xbc1994: LeaveFrame
    //     0xbc1994: mov             SP, fp
    //     0xbc1998: ldp             fp, lr, [SP], #0x10
    // 0xbc199c: ret
    //     0xbc199c: ret             
    // 0xbc19a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc19a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc19a4: b               #0xbc14a8
    // 0xbc19a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc19a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc19ac, size: 0x7c
    // 0xbc19ac: EnterFrame
    //     0xbc19ac: stp             fp, lr, [SP, #-0x10]!
    //     0xbc19b0: mov             fp, SP
    // 0xbc19b4: AllocStack(0x18)
    //     0xbc19b4: sub             SP, SP, #0x18
    // 0xbc19b8: CheckStackOverflow
    //     0xbc19b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc19bc: cmp             SP, x16
    //     0xbc19c0: b.ls            #0xbc1a20
    // 0xbc19c4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc19c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc19c8: ldr             x0, [x0, #0x2670]
    //     0xbc19cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc19d0: cmp             w0, w16
    //     0xbc19d4: b.ne            #0xbc19e0
    //     0xbc19d8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbc19dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc19e0: r0 = ReadingPreferenceArgs()
    //     0xbc19e0: bl              #0x8425e8  ; AllocateReadingPreferenceArgsStub -> ReadingPreferenceArgs (size=0x14)
    // 0xbc19e4: mov             x1, x0
    // 0xbc19e8: r0 = true
    //     0xbc19e8: add             x0, NULL, #0x20  ; true
    // 0xbc19ec: StoreField: r1->field_7 = r0
    //     0xbc19ec: stur            w0, [x1, #7]
    // 0xbc19f0: StoreField: r1->field_b = r0
    //     0xbc19f0: stur            w0, [x1, #0xb]
    // 0xbc19f4: StoreField: r1->field_f = r0
    //     0xbc19f4: stur            w0, [x1, #0xf]
    // 0xbc19f8: r16 = "/setting/reading-preference"
    //     0xbc19f8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ca0] "/setting/reading-preference"
    //     0xbc19fc: ldr             x16, [x16, #0xca0]
    // 0xbc1a00: stp             x16, NULL, [SP, #8]
    // 0xbc1a04: str             x1, [SP]
    // 0xbc1a08: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbc1a08: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbc1a0c: ldr             x4, [x4, #0x478]
    // 0xbc1a10: r0 = GetNavigation.toNamed()
    //     0xbc1a10: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbc1a14: LeaveFrame
    //     0xbc1a14: mov             SP, fp
    //     0xbc1a18: ldp             fp, lr, [SP], #0x10
    // 0xbc1a1c: ret
    //     0xbc1a1c: ret             
    // 0xbc1a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1a24: b               #0xbc19c4
  }
  [closure] IconButton <anonymous closure>(dynamic) {
    // ** addr: 0xbc1a28, size: 0x184
    // 0xbc1a28: EnterFrame
    //     0xbc1a28: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1a2c: mov             fp, SP
    // 0xbc1a30: AllocStack(0x20)
    //     0xbc1a30: sub             SP, SP, #0x20
    // 0xbc1a34: SetupParameters()
    //     0xbc1a34: ldr             x0, [fp, #0x10]
    //     0xbc1a38: ldur            w1, [x0, #0x17]
    //     0xbc1a3c: add             x1, x1, HEAP, lsl #32
    //     0xbc1a40: stur            x1, [fp, #-0x10]
    // 0xbc1a44: CheckStackOverflow
    //     0xbc1a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1a48: cmp             SP, x16
    //     0xbc1a4c: b.ls            #0xbc1ba4
    // 0xbc1a50: LoadField: r2 = r1->field_f
    //     0xbc1a50: ldur            w2, [x1, #0xf]
    // 0xbc1a54: DecompressPointer r2
    //     0xbc1a54: add             x2, x2, HEAP, lsl #32
    // 0xbc1a58: stur            x2, [fp, #-8]
    // 0xbc1a5c: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc1a5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1a60: ldr             x0, [x0, #0x26d0]
    //     0xbc1a64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1a68: cmp             w0, w16
    //     0xbc1a6c: b.ne            #0xbc1a7c
    //     0xbc1a70: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc1a74: ldr             x2, [x2, #0xb90]
    //     0xbc1a78: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc1a7c: mov             x1, x0
    // 0xbc1a80: ldur            x2, [fp, #-8]
    // 0xbc1a84: stur            x0, [fp, #-0x18]
    // 0xbc1a88: r0 = []()
    //     0xbc1a88: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1a8c: mov             x3, x0
    // 0xbc1a90: ldur            x0, [fp, #-8]
    // 0xbc1a94: stur            x3, [fp, #-0x20]
    // 0xbc1a98: LoadField: r2 = r0->field_b
    //     0xbc1a98: ldur            w2, [x0, #0xb]
    // 0xbc1a9c: DecompressPointer r2
    //     0xbc1a9c: add             x2, x2, HEAP, lsl #32
    // 0xbc1aa0: mov             x0, x3
    // 0xbc1aa4: r1 = Null
    //     0xbc1aa4: mov             x1, NULL
    // 0xbc1aa8: cmp             w2, NULL
    // 0xbc1aac: b.eq            #0xbc1ad0
    // 0xbc1ab0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1ab0: ldur            w4, [x2, #0x17]
    // 0xbc1ab4: DecompressPointer r4
    //     0xbc1ab4: add             x4, x4, HEAP, lsl #32
    // 0xbc1ab8: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1ab8: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1abc: ldr             x8, [x8, #0xb98]
    // 0xbc1ac0: LoadField: r9 = r4->field_7
    //     0xbc1ac0: ldur            x9, [x4, #7]
    // 0xbc1ac4: r3 = Null
    //     0xbc1ac4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40b10] Null
    //     0xbc1ac8: ldr             x3, [x3, #0xb10]
    // 0xbc1acc: blr             x9
    // 0xbc1ad0: ldur            x0, [fp, #-0x10]
    // 0xbc1ad4: LoadField: r3 = r0->field_f
    //     0xbc1ad4: ldur            w3, [x0, #0xf]
    // 0xbc1ad8: DecompressPointer r3
    //     0xbc1ad8: add             x3, x3, HEAP, lsl #32
    // 0xbc1adc: ldur            x1, [fp, #-0x18]
    // 0xbc1ae0: mov             x2, x3
    // 0xbc1ae4: stur            x3, [fp, #-8]
    // 0xbc1ae8: r0 = []()
    //     0xbc1ae8: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1aec: mov             x3, x0
    // 0xbc1af0: ldur            x0, [fp, #-8]
    // 0xbc1af4: stur            x3, [fp, #-0x10]
    // 0xbc1af8: LoadField: r2 = r0->field_b
    //     0xbc1af8: ldur            w2, [x0, #0xb]
    // 0xbc1afc: DecompressPointer r2
    //     0xbc1afc: add             x2, x2, HEAP, lsl #32
    // 0xbc1b00: mov             x0, x3
    // 0xbc1b04: r1 = Null
    //     0xbc1b04: mov             x1, NULL
    // 0xbc1b08: cmp             w2, NULL
    // 0xbc1b0c: b.eq            #0xbc1b30
    // 0xbc1b10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1b10: ldur            w4, [x2, #0x17]
    // 0xbc1b14: DecompressPointer r4
    //     0xbc1b14: add             x4, x4, HEAP, lsl #32
    // 0xbc1b18: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1b18: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1b1c: ldr             x8, [x8, #0xb98]
    // 0xbc1b20: LoadField: r9 = r4->field_7
    //     0xbc1b20: ldur            x9, [x4, #7]
    // 0xbc1b24: r3 = Null
    //     0xbc1b24: add             x3, PP, #0x40, lsl #12  ; [pp+0x40b20] Null
    //     0xbc1b28: ldr             x3, [x3, #0xb20]
    // 0xbc1b2c: blr             x9
    // 0xbc1b30: ldur            x0, [fp, #-0x10]
    // 0xbc1b34: LoadField: r1 = r0->field_3b
    //     0xbc1b34: ldur            w1, [x0, #0x3b]
    // 0xbc1b38: DecompressPointer r1
    //     0xbc1b38: add             x1, x1, HEAP, lsl #32
    // 0xbc1b3c: r0 = value()
    //     0xbc1b3c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbc1b40: tbnz            w0, #4, #0xbc1b50
    // 0xbc1b44: r0 = Instance_Icon
    //     0xbc1b44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!Icon@e241f1
    //     0xbc1b48: ldr             x0, [x0, #0x990]
    // 0xbc1b4c: b               #0xbc1b58
    // 0xbc1b50: r0 = Instance_Icon
    //     0xbc1b50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!Icon@e241b1
    //     0xbc1b54: ldr             x0, [x0, #0x998]
    // 0xbc1b58: ldur            x2, [fp, #-0x20]
    // 0xbc1b5c: stur            x0, [fp, #-8]
    // 0xbc1b60: r1 = Function 'toggleBookmark':.
    //     0xbc1b60: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b30] AnonymousClosure: (0xbc1bac), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::toggleBookmark (0xbc1be4)
    //     0xbc1b64: ldr             x1, [x1, #0xb30]
    // 0xbc1b68: r0 = AllocateClosure()
    //     0xbc1b68: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc1b6c: stur            x0, [fp, #-0x10]
    // 0xbc1b70: r0 = IconButton()
    //     0xbc1b70: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xbc1b74: ldur            x1, [fp, #-0x10]
    // 0xbc1b78: StoreField: r0->field_3b = r1
    //     0xbc1b78: stur            w1, [x0, #0x3b]
    // 0xbc1b7c: r1 = false
    //     0xbc1b7c: add             x1, NULL, #0x30  ; false
    // 0xbc1b80: StoreField: r0->field_47 = r1
    //     0xbc1b80: stur            w1, [x0, #0x47]
    // 0xbc1b84: ldur            x1, [fp, #-8]
    // 0xbc1b88: StoreField: r0->field_1f = r1
    //     0xbc1b88: stur            w1, [x0, #0x1f]
    // 0xbc1b8c: r1 = Instance__IconButtonVariant
    //     0xbc1b8c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xbc1b90: ldr             x1, [x1, #0xf78]
    // 0xbc1b94: StoreField: r0->field_63 = r1
    //     0xbc1b94: stur            w1, [x0, #0x63]
    // 0xbc1b98: LeaveFrame
    //     0xbc1b98: mov             SP, fp
    //     0xbc1b9c: ldp             fp, lr, [SP], #0x10
    // 0xbc1ba0: ret
    //     0xbc1ba0: ret             
    // 0xbc1ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1ba8: b               #0xbc1a50
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc1cf0, size: 0x74
    // 0xbc1cf0: EnterFrame
    //     0xbc1cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1cf4: mov             fp, SP
    // 0xbc1cf8: AllocStack(0x18)
    //     0xbc1cf8: sub             SP, SP, #0x18
    // 0xbc1cfc: SetupParameters()
    //     0xbc1cfc: ldr             x0, [fp, #0x10]
    //     0xbc1d00: ldur            w1, [x0, #0x17]
    //     0xbc1d04: add             x1, x1, HEAP, lsl #32
    //     0xbc1d08: stur            x1, [fp, #-8]
    // 0xbc1d0c: CheckStackOverflow
    //     0xbc1d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1d10: cmp             SP, x16
    //     0xbc1d14: b.ls            #0xbc1d5c
    // 0xbc1d18: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc1d18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1d1c: ldr             x0, [x0, #0x2670]
    //     0xbc1d20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1d24: cmp             w0, w16
    //     0xbc1d28: b.ne            #0xbc1d34
    //     0xbc1d2c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xbc1d30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc1d34: ldur            x0, [fp, #-8]
    // 0xbc1d38: LoadField: r1 = r0->field_f
    //     0xbc1d38: ldur            w1, [x0, #0xf]
    // 0xbc1d3c: DecompressPointer r1
    //     0xbc1d3c: add             x1, x1, HEAP, lsl #32
    // 0xbc1d40: r0 = buildShareOptions()
    //     0xbc1d40: bl              #0xbc1d64  ; [package:nuonline/app/modules/article/article_detail/views/article_detail_view.dart] ArticleDetailView::buildShareOptions
    // 0xbc1d44: stp             x0, NULL, [SP]
    // 0xbc1d48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc1d48: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc1d4c: r0 = ExtensionBottomSheet.bottomSheet()
    //     0xbc1d4c: bl              #0x917724  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionBottomSheet.bottomSheet
    // 0xbc1d50: LeaveFrame
    //     0xbc1d50: mov             SP, fp
    //     0xbc1d54: ldp             fp, lr, [SP], #0x10
    // 0xbc1d58: ret
    //     0xbc1d58: ret             
    // 0xbc1d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1d60: b               #0xbc1d18
  }
  _ buildShareOptions(/* No info */) {
    // ** addr: 0xbc1d64, size: 0x28c
    // 0xbc1d64: EnterFrame
    //     0xbc1d64: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1d68: mov             fp, SP
    // 0xbc1d6c: AllocStack(0x30)
    //     0xbc1d6c: sub             SP, SP, #0x30
    // 0xbc1d70: SetupParameters(ArticleDetailView this /* r1 => r2, fp-0x8 */)
    //     0xbc1d70: mov             x2, x1
    //     0xbc1d74: stur            x1, [fp, #-8]
    // 0xbc1d78: CheckStackOverflow
    //     0xbc1d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1d7c: cmp             SP, x16
    //     0xbc1d80: b.ls            #0xbc1fe8
    // 0xbc1d84: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc1d84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1d88: ldr             x0, [x0, #0x26d0]
    //     0xbc1d8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1d90: cmp             w0, w16
    //     0xbc1d94: b.ne            #0xbc1da4
    //     0xbc1d98: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc1d9c: ldr             x2, [x2, #0xb90]
    //     0xbc1da0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc1da4: mov             x1, x0
    // 0xbc1da8: ldur            x2, [fp, #-8]
    // 0xbc1dac: stur            x0, [fp, #-0x10]
    // 0xbc1db0: r0 = []()
    //     0xbc1db0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1db4: mov             x4, x0
    // 0xbc1db8: ldur            x3, [fp, #-8]
    // 0xbc1dbc: stur            x4, [fp, #-0x20]
    // 0xbc1dc0: LoadField: r5 = r3->field_b
    //     0xbc1dc0: ldur            w5, [x3, #0xb]
    // 0xbc1dc4: DecompressPointer r5
    //     0xbc1dc4: add             x5, x5, HEAP, lsl #32
    // 0xbc1dc8: mov             x0, x4
    // 0xbc1dcc: mov             x2, x5
    // 0xbc1dd0: stur            x5, [fp, #-0x18]
    // 0xbc1dd4: r1 = Null
    //     0xbc1dd4: mov             x1, NULL
    // 0xbc1dd8: cmp             w2, NULL
    // 0xbc1ddc: b.eq            #0xbc1e00
    // 0xbc1de0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1de0: ldur            w4, [x2, #0x17]
    // 0xbc1de4: DecompressPointer r4
    //     0xbc1de4: add             x4, x4, HEAP, lsl #32
    // 0xbc1de8: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1de8: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1dec: ldr             x8, [x8, #0xb98]
    // 0xbc1df0: LoadField: r9 = r4->field_7
    //     0xbc1df0: ldur            x9, [x4, #7]
    // 0xbc1df4: r3 = Null
    //     0xbc1df4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40b40] Null
    //     0xbc1df8: ldr             x3, [x3, #0xb40]
    // 0xbc1dfc: blr             x9
    // 0xbc1e00: r0 = NBottomSheetItem()
    //     0xbc1e00: bl              #0xb09814  ; AllocateNBottomSheetItemStub -> NBottomSheetItem (size=0x1c)
    // 0xbc1e04: mov             x3, x0
    // 0xbc1e08: r0 = Instance_IconData
    //     0xbc1e08: add             x0, PP, #0x29, lsl #12  ; [pp+0x295a0] Obj!IconData@e102f1
    //     0xbc1e0c: ldr             x0, [x0, #0x5a0]
    // 0xbc1e10: stur            x3, [fp, #-0x28]
    // 0xbc1e14: StoreField: r3->field_b = r0
    //     0xbc1e14: stur            w0, [x3, #0xb]
    // 0xbc1e18: r0 = "Bagikan Tautan Artikel"
    //     0xbc1e18: add             x0, PP, #0x40, lsl #12  ; [pp+0x40b50] "Bagikan Tautan Artikel"
    //     0xbc1e1c: ldr             x0, [x0, #0xb50]
    // 0xbc1e20: StoreField: r3->field_f = r0
    //     0xbc1e20: stur            w0, [x3, #0xf]
    // 0xbc1e24: ldur            x2, [fp, #-0x20]
    // 0xbc1e28: r1 = Function 'share':.
    //     0xbc1e28: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b58] AnonymousClosure: (0xbc2af4), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::share (0xbc2b2c)
    //     0xbc1e2c: ldr             x1, [x1, #0xb58]
    // 0xbc1e30: r0 = AllocateClosure()
    //     0xbc1e30: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc1e34: mov             x1, x0
    // 0xbc1e38: ldur            x0, [fp, #-0x28]
    // 0xbc1e3c: StoreField: r0->field_13 = r1
    //     0xbc1e3c: stur            w1, [x0, #0x13]
    // 0xbc1e40: r3 = true
    //     0xbc1e40: add             x3, NULL, #0x20  ; true
    // 0xbc1e44: ArrayStore: r0[0] = r3  ; List_4
    //     0xbc1e44: stur            w3, [x0, #0x17]
    // 0xbc1e48: ldur            x1, [fp, #-0x10]
    // 0xbc1e4c: ldur            x2, [fp, #-8]
    // 0xbc1e50: r0 = []()
    //     0xbc1e50: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1e54: ldur            x2, [fp, #-0x18]
    // 0xbc1e58: mov             x3, x0
    // 0xbc1e5c: r1 = Null
    //     0xbc1e5c: mov             x1, NULL
    // 0xbc1e60: stur            x3, [fp, #-0x20]
    // 0xbc1e64: cmp             w2, NULL
    // 0xbc1e68: b.eq            #0xbc1e8c
    // 0xbc1e6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1e6c: ldur            w4, [x2, #0x17]
    // 0xbc1e70: DecompressPointer r4
    //     0xbc1e70: add             x4, x4, HEAP, lsl #32
    // 0xbc1e74: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1e74: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1e78: ldr             x8, [x8, #0xb98]
    // 0xbc1e7c: LoadField: r9 = r4->field_7
    //     0xbc1e7c: ldur            x9, [x4, #7]
    // 0xbc1e80: r3 = Null
    //     0xbc1e80: add             x3, PP, #0x40, lsl #12  ; [pp+0x40b60] Null
    //     0xbc1e84: ldr             x3, [x3, #0xb60]
    // 0xbc1e88: blr             x9
    // 0xbc1e8c: r0 = NBottomSheetItem()
    //     0xbc1e8c: bl              #0xb09814  ; AllocateNBottomSheetItemStub -> NBottomSheetItem (size=0x1c)
    // 0xbc1e90: mov             x3, x0
    // 0xbc1e94: r0 = Instance_IconData
    //     0xbc1e94: add             x0, PP, #0x34, lsl #12  ; [pp+0x34db0] Obj!IconData@e10a71
    //     0xbc1e98: ldr             x0, [x0, #0xdb0]
    // 0xbc1e9c: stur            x3, [fp, #-0x30]
    // 0xbc1ea0: StoreField: r3->field_b = r0
    //     0xbc1ea0: stur            w0, [x3, #0xb]
    // 0xbc1ea4: r0 = "Simpan Sebagai PDF"
    //     0xbc1ea4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40b70] "Simpan Sebagai PDF"
    //     0xbc1ea8: ldr             x0, [x0, #0xb70]
    // 0xbc1eac: StoreField: r3->field_f = r0
    //     0xbc1eac: stur            w0, [x3, #0xf]
    // 0xbc1eb0: ldur            x2, [fp, #-0x20]
    // 0xbc1eb4: r1 = Function 'savePDF':.
    //     0xbc1eb4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b78] AnonymousClosure: (0xbc2138), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::savePDF (0xbc2170)
    //     0xbc1eb8: ldr             x1, [x1, #0xb78]
    // 0xbc1ebc: r0 = AllocateClosure()
    //     0xbc1ebc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc1ec0: mov             x1, x0
    // 0xbc1ec4: ldur            x0, [fp, #-0x30]
    // 0xbc1ec8: StoreField: r0->field_13 = r1
    //     0xbc1ec8: stur            w1, [x0, #0x13]
    // 0xbc1ecc: r1 = true
    //     0xbc1ecc: add             x1, NULL, #0x20  ; true
    // 0xbc1ed0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc1ed0: stur            w1, [x0, #0x17]
    // 0xbc1ed4: ldur            x1, [fp, #-0x10]
    // 0xbc1ed8: ldur            x2, [fp, #-8]
    // 0xbc1edc: r0 = []()
    //     0xbc1edc: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc1ee0: ldur            x2, [fp, #-0x18]
    // 0xbc1ee4: mov             x3, x0
    // 0xbc1ee8: r1 = Null
    //     0xbc1ee8: mov             x1, NULL
    // 0xbc1eec: stur            x3, [fp, #-8]
    // 0xbc1ef0: cmp             w2, NULL
    // 0xbc1ef4: b.eq            #0xbc1f18
    // 0xbc1ef8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc1ef8: ldur            w4, [x2, #0x17]
    // 0xbc1efc: DecompressPointer r4
    //     0xbc1efc: add             x4, x4, HEAP, lsl #32
    // 0xbc1f00: r8 = X0 bound GetLifeCycleBase?
    //     0xbc1f00: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc1f04: ldr             x8, [x8, #0xb98]
    // 0xbc1f08: LoadField: r9 = r4->field_7
    //     0xbc1f08: ldur            x9, [x4, #7]
    // 0xbc1f0c: r3 = Null
    //     0xbc1f0c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40b80] Null
    //     0xbc1f10: ldr             x3, [x3, #0xb80]
    // 0xbc1f14: blr             x9
    // 0xbc1f18: r0 = NBottomSheetItem()
    //     0xbc1f18: bl              #0xb09814  ; AllocateNBottomSheetItemStub -> NBottomSheetItem (size=0x1c)
    // 0xbc1f1c: mov             x3, x0
    // 0xbc1f20: r0 = Instance_IconData
    //     0xbc1f20: add             x0, PP, #0x33, lsl #12  ; [pp+0x33e48] Obj!IconData@e10991
    //     0xbc1f24: ldr             x0, [x0, #0xe48]
    // 0xbc1f28: stur            x3, [fp, #-0x10]
    // 0xbc1f2c: StoreField: r3->field_b = r0
    //     0xbc1f2c: stur            w0, [x3, #0xb]
    // 0xbc1f30: r0 = "Baca Artikel di Browser"
    //     0xbc1f30: add             x0, PP, #0x40, lsl #12  ; [pp+0x40b90] "Baca Artikel di Browser"
    //     0xbc1f34: ldr             x0, [x0, #0xb90]
    // 0xbc1f38: StoreField: r3->field_f = r0
    //     0xbc1f38: stur            w0, [x3, #0xf]
    // 0xbc1f3c: ldur            x2, [fp, #-8]
    // 0xbc1f40: r1 = Function 'openInWeb':.
    //     0xbc1f40: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b98] AnonymousClosure: (0xbc1ff0), in [package:nuonline/app/modules/article/article_detail/controllers/article_detail_controller.dart] ArticleDetailController::openInWeb (0xbc2028)
    //     0xbc1f44: ldr             x1, [x1, #0xb98]
    // 0xbc1f48: r0 = AllocateClosure()
    //     0xbc1f48: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc1f4c: mov             x1, x0
    // 0xbc1f50: ldur            x0, [fp, #-0x10]
    // 0xbc1f54: StoreField: r0->field_13 = r1
    //     0xbc1f54: stur            w1, [x0, #0x13]
    // 0xbc1f58: r1 = false
    //     0xbc1f58: add             x1, NULL, #0x30  ; false
    // 0xbc1f5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc1f5c: stur            w1, [x0, #0x17]
    // 0xbc1f60: r1 = Null
    //     0xbc1f60: mov             x1, NULL
    // 0xbc1f64: r2 = 6
    //     0xbc1f64: movz            x2, #0x6
    // 0xbc1f68: r0 = AllocateArray()
    //     0xbc1f68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc1f6c: mov             x2, x0
    // 0xbc1f70: ldur            x0, [fp, #-0x28]
    // 0xbc1f74: stur            x2, [fp, #-8]
    // 0xbc1f78: StoreField: r2->field_f = r0
    //     0xbc1f78: stur            w0, [x2, #0xf]
    // 0xbc1f7c: ldur            x0, [fp, #-0x30]
    // 0xbc1f80: StoreField: r2->field_13 = r0
    //     0xbc1f80: stur            w0, [x2, #0x13]
    // 0xbc1f84: ldur            x0, [fp, #-0x10]
    // 0xbc1f88: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc1f88: stur            w0, [x2, #0x17]
    // 0xbc1f8c: r1 = <Widget>
    //     0xbc1f8c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc1f90: r0 = AllocateGrowableArray()
    //     0xbc1f90: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc1f94: mov             x1, x0
    // 0xbc1f98: ldur            x0, [fp, #-8]
    // 0xbc1f9c: stur            x1, [fp, #-0x10]
    // 0xbc1fa0: StoreField: r1->field_f = r0
    //     0xbc1fa0: stur            w0, [x1, #0xf]
    // 0xbc1fa4: r0 = 6
    //     0xbc1fa4: movz            x0, #0x6
    // 0xbc1fa8: StoreField: r1->field_b = r0
    //     0xbc1fa8: stur            w0, [x1, #0xb]
    // 0xbc1fac: r0 = NBottomSheet()
    //     0xbc1fac: bl              #0x925ef4  ; AllocateNBottomSheetStub -> NBottomSheet (size=0x10)
    // 0xbc1fb0: mov             x1, x0
    // 0xbc1fb4: ldur            x0, [fp, #-0x10]
    // 0xbc1fb8: stur            x1, [fp, #-8]
    // 0xbc1fbc: StoreField: r1->field_b = r0
    //     0xbc1fbc: stur            w0, [x1, #0xb]
    // 0xbc1fc0: r0 = ListTileTheme()
    //     0xbc1fc0: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xbc1fc4: r1 = Instance_EdgeInsets
    //     0xbc1fc4: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbc1fc8: StoreField: r0->field_2b = r1
    //     0xbc1fc8: stur            w1, [x0, #0x2b]
    // 0xbc1fcc: r1 = 0.000000
    //     0xbc1fcc: ldr             x1, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xbc1fd0: StoreField: r0->field_37 = r1
    //     0xbc1fd0: stur            w1, [x0, #0x37]
    // 0xbc1fd4: ldur            x1, [fp, #-8]
    // 0xbc1fd8: StoreField: r0->field_b = r1
    //     0xbc1fd8: stur            w1, [x0, #0xb]
    // 0xbc1fdc: LeaveFrame
    //     0xbc1fdc: mov             SP, fp
    //     0xbc1fe0: ldp             fp, lr, [SP], #0x10
    // 0xbc1fe4: ret
    //     0xbc1fe4: ret             
    // 0xbc1fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc1fe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc1fec: b               #0xbc1d84
  }
}
