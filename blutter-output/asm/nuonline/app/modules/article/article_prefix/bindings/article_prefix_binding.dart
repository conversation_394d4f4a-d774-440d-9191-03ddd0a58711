// lib: , url: package:nuonline/app/modules/article/article_prefix/bindings/article_prefix_binding.dart

// class id: 1050136, size: 0x8
class :: {
}

// class id: 2193, size: 0x8, field offset: 0x8
class ArticlePrefixBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80dc54, size: 0xe0
    // 0x80dc54: EnterFrame
    //     0x80dc54: stp             fp, lr, [SP, #-0x10]!
    //     0x80dc58: mov             fp, SP
    // 0x80dc5c: AllocStack(0x20)
    //     0x80dc5c: sub             SP, SP, #0x20
    // 0x80dc60: CheckStackOverflow
    //     0x80dc60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80dc64: cmp             SP, x16
    //     0x80dc68: b.ls            #0x80dd2c
    // 0x80dc6c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80dc6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80dc70: ldr             x0, [x0, #0x2670]
    //     0x80dc74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80dc78: cmp             w0, w16
    //     0x80dc7c: b.ne            #0x80dc88
    //     0x80dc80: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80dc84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80dc88: r0 = GetNavigation.arguments()
    //     0x80dc88: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80dc8c: r16 = "prefix"
    //     0x80dc8c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x80dc90: ldr             x16, [x16, #0x970]
    // 0x80dc94: stp             x16, x0, [SP]
    // 0x80dc98: r4 = 0
    //     0x80dc98: movz            x4, #0
    // 0x80dc9c: ldr             x0, [SP, #8]
    // 0x80dca0: r16 = UnlinkedCall_0x5f3c08
    //     0x80dca0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36530] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80dca4: add             x16, x16, #0x530
    // 0x80dca8: ldp             x5, lr, [x16]
    // 0x80dcac: blr             lr
    // 0x80dcb0: mov             x3, x0
    // 0x80dcb4: r2 = Null
    //     0x80dcb4: mov             x2, NULL
    // 0x80dcb8: r1 = Null
    //     0x80dcb8: mov             x1, NULL
    // 0x80dcbc: stur            x3, [fp, #-8]
    // 0x80dcc0: r4 = 60
    //     0x80dcc0: movz            x4, #0x3c
    // 0x80dcc4: branchIfSmi(r0, 0x80dcd0)
    //     0x80dcc4: tbz             w0, #0, #0x80dcd0
    // 0x80dcc8: r4 = LoadClassIdInstr(r0)
    //     0x80dcc8: ldur            x4, [x0, #-1]
    //     0x80dccc: ubfx            x4, x4, #0xc, #0x14
    // 0x80dcd0: sub             x4, x4, #0x5e
    // 0x80dcd4: cmp             x4, #1
    // 0x80dcd8: b.ls            #0x80dcec
    // 0x80dcdc: r8 = String
    //     0x80dcdc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80dce0: r3 = Null
    //     0x80dce0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36540] Null
    //     0x80dce4: ldr             x3, [x3, #0x540]
    // 0x80dce8: r0 = String()
    //     0x80dce8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80dcec: r1 = Function '<anonymous closure>':.
    //     0x80dcec: add             x1, PP, #0x36, lsl #12  ; [pp+0x36550] AnonymousClosure: (0x80dd34), in [package:nuonline/app/modules/article/article_prefix/bindings/article_prefix_binding.dart] ArticlePrefixBinding::dependencies (0x80dc54)
    //     0x80dcf0: ldr             x1, [x1, #0x550]
    // 0x80dcf4: r2 = Null
    //     0x80dcf4: mov             x2, NULL
    // 0x80dcf8: r0 = AllocateClosure()
    //     0x80dcf8: bl              #0xec1630  ; AllocateClosureStub
    // 0x80dcfc: r16 = <ArticlePrefixController>
    //     0x80dcfc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d48] TypeArguments: <ArticlePrefixController>
    //     0x80dd00: ldr             x16, [x16, #0xd48]
    // 0x80dd04: stp             x0, x16, [SP, #8]
    // 0x80dd08: ldur            x16, [fp, #-8]
    // 0x80dd0c: str             x16, [SP]
    // 0x80dd10: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x80dd10: add             x4, PP, #0xb, lsl #12  ; [pp+0xb630] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x80dd14: ldr             x4, [x4, #0x630]
    // 0x80dd18: r0 = Inst.lazyPut()
    //     0x80dd18: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80dd1c: r0 = Null
    //     0x80dd1c: mov             x0, NULL
    // 0x80dd20: LeaveFrame
    //     0x80dd20: mov             SP, fp
    //     0x80dd24: ldp             fp, lr, [SP], #0x10
    // 0x80dd28: ret
    //     0x80dd28: ret             
    // 0x80dd2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80dd2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80dd30: b               #0x80dc6c
  }
  [closure] ArticlePrefixController <anonymous closure>(dynamic) {
    // ** addr: 0x80dd34, size: 0xf4
    // 0x80dd34: EnterFrame
    //     0x80dd34: stp             fp, lr, [SP, #-0x10]!
    //     0x80dd38: mov             fp, SP
    // 0x80dd3c: AllocStack(0x28)
    //     0x80dd3c: sub             SP, SP, #0x28
    // 0x80dd40: CheckStackOverflow
    //     0x80dd40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80dd44: cmp             SP, x16
    //     0x80dd48: b.ls            #0x80de20
    // 0x80dd4c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80dd4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80dd50: ldr             x0, [x0, #0x2670]
    //     0x80dd54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80dd58: cmp             w0, w16
    //     0x80dd5c: b.ne            #0x80dd68
    //     0x80dd60: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80dd64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80dd68: r0 = GetNavigation.arguments()
    //     0x80dd68: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80dd6c: r16 = "prefix"
    //     0x80dd6c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x80dd70: ldr             x16, [x16, #0x970]
    // 0x80dd74: stp             x16, x0, [SP]
    // 0x80dd78: r4 = 0
    //     0x80dd78: movz            x4, #0
    // 0x80dd7c: ldr             x0, [SP, #8]
    // 0x80dd80: r16 = UnlinkedCall_0x5f3c08
    //     0x80dd80: add             x16, PP, #0x36, lsl #12  ; [pp+0x36558] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80dd84: add             x16, x16, #0x558
    // 0x80dd88: ldp             x5, lr, [x16]
    // 0x80dd8c: blr             lr
    // 0x80dd90: mov             x3, x0
    // 0x80dd94: r2 = Null
    //     0x80dd94: mov             x2, NULL
    // 0x80dd98: r1 = Null
    //     0x80dd98: mov             x1, NULL
    // 0x80dd9c: stur            x3, [fp, #-8]
    // 0x80dda0: r4 = 60
    //     0x80dda0: movz            x4, #0x3c
    // 0x80dda4: branchIfSmi(r0, 0x80ddb0)
    //     0x80dda4: tbz             w0, #0, #0x80ddb0
    // 0x80dda8: r4 = LoadClassIdInstr(r0)
    //     0x80dda8: ldur            x4, [x0, #-1]
    //     0x80ddac: ubfx            x4, x4, #0xc, #0x14
    // 0x80ddb0: sub             x4, x4, #0x5e
    // 0x80ddb4: cmp             x4, #1
    // 0x80ddb8: b.ls            #0x80ddcc
    // 0x80ddbc: r8 = String
    //     0x80ddbc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80ddc0: r3 = Null
    //     0x80ddc0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36568] Null
    //     0x80ddc4: ldr             x3, [x3, #0x568]
    // 0x80ddc8: r0 = String()
    //     0x80ddc8: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80ddcc: r16 = <ArticleRepository>
    //     0x80ddcc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80ddd0: ldr             x16, [x16, #0x98]
    // 0x80ddd4: r30 = "article_repo"
    //     0x80ddd4: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80ddd8: ldr             lr, [lr, #0xa0]
    // 0x80dddc: stp             lr, x16, [SP]
    // 0x80dde0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80dde0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80dde4: r0 = Inst.find()
    //     0x80dde4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80dde8: stur            x0, [fp, #-0x10]
    // 0x80ddec: r0 = ArticlePrefixController()
    //     0x80ddec: bl              #0x80de28  ; AllocateArticlePrefixControllerStub -> ArticlePrefixController (size=0x40)
    // 0x80ddf0: mov             x2, x0
    // 0x80ddf4: ldur            x0, [fp, #-8]
    // 0x80ddf8: stur            x2, [fp, #-0x18]
    // 0x80ddfc: StoreField: r2->field_37 = r0
    //     0x80ddfc: stur            w0, [x2, #0x37]
    // 0x80de00: ldur            x0, [fp, #-0x10]
    // 0x80de04: StoreField: r2->field_3b = r0
    //     0x80de04: stur            w0, [x2, #0x3b]
    // 0x80de08: mov             x1, x2
    // 0x80de0c: r0 = _ArticleAuthorController&GetxController&PagingMixin()
    //     0x80de0c: bl              #0x80c3d4  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::_ArticleAuthorController&GetxController&PagingMixin
    // 0x80de10: ldur            x0, [fp, #-0x18]
    // 0x80de14: LeaveFrame
    //     0x80de14: mov             SP, fp
    //     0x80de18: ldp             fp, lr, [SP], #0x10
    // 0x80de1c: ret
    //     0x80de1c: ret             
    // 0x80de20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80de20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80de24: b               #0x80dd4c
  }
}
