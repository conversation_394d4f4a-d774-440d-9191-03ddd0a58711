// lib: , url: package:nuonline/app/modules/article/article_prefix/controllers/article_prefix_controller.dart

// class id: 1050137, size: 0x8
class :: {
}

// class id: 2049, size: 0x40, field offset: 0x38
class ArticlePrefixController extends _ArticleAuthorController&GetxController&PagingMixin {

  _ onPageRequest(/* No info */) {
    // ** addr: 0xe32a48, size: 0x15c
    // 0xe32a48: EnterFrame
    //     0xe32a48: stp             fp, lr, [SP, #-0x10]!
    //     0xe32a4c: mov             fp, SP
    // 0xe32a50: AllocStack(0x40)
    //     0xe32a50: sub             SP, SP, #0x40
    // 0xe32a54: SetupParameters(ArticlePrefixController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe32a54: stur            x1, [fp, #-8]
    //     0xe32a58: stur            x2, [fp, #-0x10]
    // 0xe32a5c: CheckStackOverflow
    //     0xe32a5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32a60: cmp             SP, x16
    //     0xe32a64: b.ls            #0xe32b9c
    // 0xe32a68: r1 = 1
    //     0xe32a68: movz            x1, #0x1
    // 0xe32a6c: r0 = AllocateContext()
    //     0xe32a6c: bl              #0xec126c  ; AllocateContextStub
    // 0xe32a70: mov             x3, x0
    // 0xe32a74: ldur            x0, [fp, #-8]
    // 0xe32a78: stur            x3, [fp, #-0x20]
    // 0xe32a7c: StoreField: r3->field_f = r0
    //     0xe32a7c: stur            w0, [x3, #0xf]
    // 0xe32a80: LoadField: r4 = r0->field_3b
    //     0xe32a80: ldur            w4, [x0, #0x3b]
    // 0xe32a84: DecompressPointer r4
    //     0xe32a84: add             x4, x4, HEAP, lsl #32
    // 0xe32a88: stur            x4, [fp, #-0x18]
    // 0xe32a8c: r1 = Null
    //     0xe32a8c: mov             x1, NULL
    // 0xe32a90: r2 = 8
    //     0xe32a90: movz            x2, #0x8
    // 0xe32a94: r0 = AllocateArray()
    //     0xe32a94: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe32a98: mov             x2, x0
    // 0xe32a9c: stur            x2, [fp, #-0x28]
    // 0xe32aa0: r16 = "prefix"
    //     0xe32aa0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0xe32aa4: ldr             x16, [x16, #0x970]
    // 0xe32aa8: StoreField: r2->field_f = r16
    //     0xe32aa8: stur            w16, [x2, #0xf]
    // 0xe32aac: ldur            x3, [fp, #-8]
    // 0xe32ab0: LoadField: r0 = r3->field_37
    //     0xe32ab0: ldur            w0, [x3, #0x37]
    // 0xe32ab4: DecompressPointer r0
    //     0xe32ab4: add             x0, x0, HEAP, lsl #32
    // 0xe32ab8: StoreField: r2->field_13 = r0
    //     0xe32ab8: stur            w0, [x2, #0x13]
    // 0xe32abc: r16 = "page"
    //     0xe32abc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe32ac0: ldr             x16, [x16, #0x300]
    // 0xe32ac4: ArrayStore: r2[0] = r16  ; List_4
    //     0xe32ac4: stur            w16, [x2, #0x17]
    // 0xe32ac8: ldur            x4, [fp, #-0x10]
    // 0xe32acc: r0 = BoxInt64Instr(r4)
    //     0xe32acc: sbfiz           x0, x4, #1, #0x1f
    //     0xe32ad0: cmp             x4, x0, asr #1
    //     0xe32ad4: b.eq            #0xe32ae0
    //     0xe32ad8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe32adc: stur            x4, [x0, #7]
    // 0xe32ae0: r1 = 60
    //     0xe32ae0: movz            x1, #0x3c
    // 0xe32ae4: branchIfSmi(r0, 0xe32af0)
    //     0xe32ae4: tbz             w0, #0, #0xe32af0
    // 0xe32ae8: r1 = LoadClassIdInstr(r0)
    //     0xe32ae8: ldur            x1, [x0, #-1]
    //     0xe32aec: ubfx            x1, x1, #0xc, #0x14
    // 0xe32af0: str             x0, [SP]
    // 0xe32af4: mov             x0, x1
    // 0xe32af8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe32af8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe32afc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe32afc: movz            x17, #0x2b03
    //     0xe32b00: add             lr, x0, x17
    //     0xe32b04: ldr             lr, [x21, lr, lsl #3]
    //     0xe32b08: blr             lr
    // 0xe32b0c: ldur            x1, [fp, #-0x28]
    // 0xe32b10: ArrayStore: r1[3] = r0  ; List_4
    //     0xe32b10: add             x25, x1, #0x1b
    //     0xe32b14: str             w0, [x25]
    //     0xe32b18: tbz             w0, #0, #0xe32b34
    //     0xe32b1c: ldurb           w16, [x1, #-1]
    //     0xe32b20: ldurb           w17, [x0, #-1]
    //     0xe32b24: and             x16, x17, x16, lsr #2
    //     0xe32b28: tst             x16, HEAP, lsr #32
    //     0xe32b2c: b.eq            #0xe32b34
    //     0xe32b30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe32b34: r16 = <String, String?>
    //     0xe32b34: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe32b38: ldr             x16, [x16, #0x198]
    // 0xe32b3c: ldur            lr, [fp, #-0x28]
    // 0xe32b40: stp             lr, x16, [SP]
    // 0xe32b44: r0 = Map._fromLiteral()
    //     0xe32b44: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe32b48: ldur            x1, [fp, #-0x18]
    // 0xe32b4c: mov             x2, x0
    // 0xe32b50: r0 = findAll()
    //     0xe32b50: bl              #0x8ff2cc  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAll
    // 0xe32b54: ldur            x2, [fp, #-0x20]
    // 0xe32b58: r1 = Function '<anonymous closure>':.
    //     0xe32b58: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ac0] AnonymousClosure: (0xe32ba4), in [package:nuonline/app/modules/article/article_prefix/controllers/article_prefix_controller.dart] ArticlePrefixController::onPageRequest (0xe32a48)
    //     0xe32b5c: ldr             x1, [x1, #0xac0]
    // 0xe32b60: stur            x0, [fp, #-0x18]
    // 0xe32b64: r0 = AllocateClosure()
    //     0xe32b64: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32b68: r16 = <void?>
    //     0xe32b68: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32b6c: ldur            lr, [fp, #-0x18]
    // 0xe32b70: stp             lr, x16, [SP, #8]
    // 0xe32b74: str             x0, [SP]
    // 0xe32b78: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe32b78: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe32b7c: r0 = then()
    //     0xe32b7c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe32b80: ldur            x1, [fp, #-8]
    // 0xe32b84: ldur            x2, [fp, #-0x10]
    // 0xe32b88: r0 = onPageRequest()
    //     0xe32b88: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe32b8c: r0 = Null
    //     0xe32b8c: mov             x0, NULL
    // 0xe32b90: LeaveFrame
    //     0xe32b90: mov             SP, fp
    //     0xe32b94: ldp             fp, lr, [SP], #0x10
    // 0xe32b98: ret
    //     0xe32b98: ret             
    // 0xe32b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32ba0: b               #0xe32a68
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Article>>) {
    // ** addr: 0xe32ba4, size: 0xa0
    // 0xe32ba4: EnterFrame
    //     0xe32ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xe32ba8: mov             fp, SP
    // 0xe32bac: AllocStack(0x28)
    //     0xe32bac: sub             SP, SP, #0x28
    // 0xe32bb0: SetupParameters()
    //     0xe32bb0: ldr             x0, [fp, #0x18]
    //     0xe32bb4: ldur            w1, [x0, #0x17]
    //     0xe32bb8: add             x1, x1, HEAP, lsl #32
    // 0xe32bbc: CheckStackOverflow
    //     0xe32bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32bc0: cmp             SP, x16
    //     0xe32bc4: b.ls            #0xe32c3c
    // 0xe32bc8: LoadField: r0 = r1->field_f
    //     0xe32bc8: ldur            w0, [x1, #0xf]
    // 0xe32bcc: DecompressPointer r0
    //     0xe32bcc: add             x0, x0, HEAP, lsl #32
    // 0xe32bd0: mov             x2, x0
    // 0xe32bd4: stur            x0, [fp, #-8]
    // 0xe32bd8: r1 = Function '_onSuccess@1877330116':.
    //     0xe32bd8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ac8] AnonymousClosure: (0xe32c80), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess (0xe325c8)
    //     0xe32bdc: ldr             x1, [x1, #0xac8]
    // 0xe32be0: r0 = AllocateClosure()
    //     0xe32be0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32be4: ldur            x2, [fp, #-8]
    // 0xe32be8: r1 = Function '_onError@1877330116':.
    //     0xe32be8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ad0] AnonymousClosure: (0xe32c44), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError (0xe32524)
    //     0xe32bec: ldr             x1, [x1, #0xad0]
    // 0xe32bf0: stur            x0, [fp, #-8]
    // 0xe32bf4: r0 = AllocateClosure()
    //     0xe32bf4: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32bf8: mov             x1, x0
    // 0xe32bfc: ldr             x0, [fp, #0x10]
    // 0xe32c00: r2 = LoadClassIdInstr(r0)
    //     0xe32c00: ldur            x2, [x0, #-1]
    //     0xe32c04: ubfx            x2, x2, #0xc, #0x14
    // 0xe32c08: r16 = <void?>
    //     0xe32c08: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32c0c: stp             x0, x16, [SP, #0x10]
    // 0xe32c10: ldur            x16, [fp, #-8]
    // 0xe32c14: stp             x16, x1, [SP]
    // 0xe32c18: mov             x0, x2
    // 0xe32c1c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe32c1c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe32c20: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe32c20: sub             lr, x0, #1, lsl #12
    //     0xe32c24: ldr             lr, [x21, lr, lsl #3]
    //     0xe32c28: blr             lr
    // 0xe32c2c: r0 = Null
    //     0xe32c2c: mov             x0, NULL
    // 0xe32c30: LeaveFrame
    //     0xe32c30: mov             SP, fp
    //     0xe32c34: ldp             fp, lr, [SP], #0x10
    // 0xe32c38: ret
    //     0xe32c38: ret             
    // 0xe32c3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32c3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32c40: b               #0xe32bc8
  }
  [closure] void _onError(dynamic, NetworkExceptions) {
    // ** addr: 0xe32c44, size: 0x3c
    // 0xe32c44: EnterFrame
    //     0xe32c44: stp             fp, lr, [SP, #-0x10]!
    //     0xe32c48: mov             fp, SP
    // 0xe32c4c: ldr             x0, [fp, #0x18]
    // 0xe32c50: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe32c50: ldur            w1, [x0, #0x17]
    // 0xe32c54: DecompressPointer r1
    //     0xe32c54: add             x1, x1, HEAP, lsl #32
    // 0xe32c58: CheckStackOverflow
    //     0xe32c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32c5c: cmp             SP, x16
    //     0xe32c60: b.ls            #0xe32c78
    // 0xe32c64: ldr             x2, [fp, #0x10]
    // 0xe32c68: r0 = _onError()
    //     0xe32c68: bl              #0xe32524  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError
    // 0xe32c6c: LeaveFrame
    //     0xe32c6c: mov             SP, fp
    //     0xe32c70: ldp             fp, lr, [SP], #0x10
    // 0xe32c74: ret
    //     0xe32c74: ret             
    // 0xe32c78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32c78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32c7c: b               #0xe32c64
  }
  [closure] void _onSuccess(dynamic, List<Article>, Pagination?) {
    // ** addr: 0xe32c80, size: 0x40
    // 0xe32c80: EnterFrame
    //     0xe32c80: stp             fp, lr, [SP, #-0x10]!
    //     0xe32c84: mov             fp, SP
    // 0xe32c88: ldr             x0, [fp, #0x20]
    // 0xe32c8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe32c8c: ldur            w1, [x0, #0x17]
    // 0xe32c90: DecompressPointer r1
    //     0xe32c90: add             x1, x1, HEAP, lsl #32
    // 0xe32c94: CheckStackOverflow
    //     0xe32c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32c98: cmp             SP, x16
    //     0xe32c9c: b.ls            #0xe32cb8
    // 0xe32ca0: ldr             x2, [fp, #0x18]
    // 0xe32ca4: ldr             x3, [fp, #0x10]
    // 0xe32ca8: r0 = _onSuccess()
    //     0xe32ca8: bl              #0xe325c8  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess
    // 0xe32cac: LeaveFrame
    //     0xe32cac: mov             SP, fp
    //     0xe32cb0: ldp             fp, lr, [SP], #0x10
    // 0xe32cb4: ret
    //     0xe32cb4: ret             
    // 0xe32cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32cbc: b               #0xe32ca0
  }
}
