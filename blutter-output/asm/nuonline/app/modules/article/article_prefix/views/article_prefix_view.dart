// lib: , url: package:nuonline/app/modules/article/article_prefix/views/article_prefix_view.dart

// class id: 1050138, size: 0x8
class :: {
}

// class id: 4449, size: 0x14, field offset: 0x14
class ArticlePrefixView extends GetWidget<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0x862594, size: 0xb0
    // 0x862594: EnterFrame
    //     0x862594: stp             fp, lr, [SP, #-0x10]!
    //     0x862598: mov             fp, SP
    // 0x86259c: AllocStack(0x18)
    //     0x86259c: sub             SP, SP, #0x18
    // 0x8625a0: CheckStackOverflow
    //     0x8625a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8625a4: cmp             SP, x16
    //     0x8625a8: b.ls            #0x86263c
    // 0x8625ac: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8625ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8625b0: ldr             x0, [x0, #0x2670]
    //     0x8625b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8625b8: cmp             w0, w16
    //     0x8625bc: b.ne            #0x8625c8
    //     0x8625c0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8625c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8625c8: r0 = GetNavigation.arguments()
    //     0x8625c8: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x8625cc: r16 = "prefix"
    //     0x8625cc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d970] "prefix"
    //     0x8625d0: ldr             x16, [x16, #0x970]
    // 0x8625d4: stp             x16, x0, [SP]
    // 0x8625d8: r4 = 0
    //     0x8625d8: movz            x4, #0
    // 0x8625dc: ldr             x0, [SP, #8]
    // 0x8625e0: r16 = UnlinkedCall_0x5f3c08
    //     0x8625e0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40aa0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x8625e4: add             x16, x16, #0xaa0
    // 0x8625e8: ldp             x5, lr, [x16]
    // 0x8625ec: blr             lr
    // 0x8625f0: mov             x3, x0
    // 0x8625f4: r2 = Null
    //     0x8625f4: mov             x2, NULL
    // 0x8625f8: r1 = Null
    //     0x8625f8: mov             x1, NULL
    // 0x8625fc: stur            x3, [fp, #-8]
    // 0x862600: r4 = 60
    //     0x862600: movz            x4, #0x3c
    // 0x862604: branchIfSmi(r0, 0x862610)
    //     0x862604: tbz             w0, #0, #0x862610
    // 0x862608: r4 = LoadClassIdInstr(r0)
    //     0x862608: ldur            x4, [x0, #-1]
    //     0x86260c: ubfx            x4, x4, #0xc, #0x14
    // 0x862610: sub             x4, x4, #0x5e
    // 0x862614: cmp             x4, #1
    // 0x862618: b.ls            #0x86262c
    // 0x86261c: r8 = String
    //     0x86261c: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x862620: r3 = Null
    //     0x862620: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ab0] Null
    //     0x862624: ldr             x3, [x3, #0xab0]
    // 0x862628: r0 = String()
    //     0x862628: bl              #0xed43b0  ; IsType_String_Stub
    // 0x86262c: ldur            x0, [fp, #-8]
    // 0x862630: LeaveFrame
    //     0x862630: mov             SP, fp
    //     0x862634: ldp             fp, lr, [SP], #0x10
    // 0x862638: ret
    //     0x862638: ret             
    // 0x86263c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86263c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862640: b               #0x8625ac
  }
  _ build(/* No info */) {
    // ** addr: 0xbc2cbc, size: 0x3f8
    // 0xbc2cbc: EnterFrame
    //     0xbc2cbc: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2cc0: mov             fp, SP
    // 0xbc2cc4: AllocStack(0x40)
    //     0xbc2cc4: sub             SP, SP, #0x40
    // 0xbc2cc8: SetupParameters(ArticlePrefixView this /* r1 => r0, fp-0x8 */)
    //     0xbc2cc8: mov             x0, x1
    //     0xbc2ccc: stur            x1, [fp, #-8]
    // 0xbc2cd0: CheckStackOverflow
    //     0xbc2cd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2cd4: cmp             SP, x16
    //     0xbc2cd8: b.ls            #0xbc30ac
    // 0xbc2cdc: r1 = 1
    //     0xbc2cdc: movz            x1, #0x1
    // 0xbc2ce0: r0 = AllocateContext()
    //     0xbc2ce0: bl              #0xec126c  ; AllocateContextStub
    // 0xbc2ce4: ldur            x2, [fp, #-8]
    // 0xbc2ce8: stur            x0, [fp, #-0x10]
    // 0xbc2cec: StoreField: r0->field_f = r2
    //     0xbc2cec: stur            w2, [x0, #0xf]
    // 0xbc2cf0: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc2cf0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc2cf4: ldr             x0, [x0, #0x26d0]
    //     0xbc2cf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc2cfc: cmp             w0, w16
    //     0xbc2d00: b.ne            #0xbc2d10
    //     0xbc2d04: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc2d08: ldr             x2, [x2, #0xb90]
    //     0xbc2d0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc2d10: mov             x1, x0
    // 0xbc2d14: ldur            x2, [fp, #-8]
    // 0xbc2d18: stur            x0, [fp, #-0x18]
    // 0xbc2d1c: r0 = []()
    //     0xbc2d1c: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc2d20: mov             x4, x0
    // 0xbc2d24: ldur            x3, [fp, #-8]
    // 0xbc2d28: stur            x4, [fp, #-0x28]
    // 0xbc2d2c: LoadField: r5 = r3->field_b
    //     0xbc2d2c: ldur            w5, [x3, #0xb]
    // 0xbc2d30: DecompressPointer r5
    //     0xbc2d30: add             x5, x5, HEAP, lsl #32
    // 0xbc2d34: mov             x0, x4
    // 0xbc2d38: mov             x2, x5
    // 0xbc2d3c: stur            x5, [fp, #-0x20]
    // 0xbc2d40: r1 = Null
    //     0xbc2d40: mov             x1, NULL
    // 0xbc2d44: cmp             w2, NULL
    // 0xbc2d48: b.eq            #0xbc2d6c
    // 0xbc2d4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc2d4c: ldur            w4, [x2, #0x17]
    // 0xbc2d50: DecompressPointer r4
    //     0xbc2d50: add             x4, x4, HEAP, lsl #32
    // 0xbc2d54: r8 = X0 bound GetLifeCycleBase?
    //     0xbc2d54: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc2d58: ldr             x8, [x8, #0xb98]
    // 0xbc2d5c: LoadField: r9 = r4->field_7
    //     0xbc2d5c: ldur            x9, [x4, #7]
    // 0xbc2d60: r3 = Null
    //     0xbc2d60: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a28] Null
    //     0xbc2d64: ldr             x3, [x3, #0xa28]
    // 0xbc2d68: blr             x9
    // 0xbc2d6c: ldur            x0, [fp, #-0x28]
    // 0xbc2d70: LoadField: r1 = r0->field_37
    //     0xbc2d70: ldur            w1, [x0, #0x37]
    // 0xbc2d74: DecompressPointer r1
    //     0xbc2d74: add             x1, x1, HEAP, lsl #32
    // 0xbc2d78: r0 = LoadClassIdInstr(r1)
    //     0xbc2d78: ldur            x0, [x1, #-1]
    //     0xbc2d7c: ubfx            x0, x0, #0xc, #0x14
    // 0xbc2d80: str             x1, [SP]
    // 0xbc2d84: r0 = GDT[cid_x0 + -0xff6]()
    //     0xbc2d84: sub             lr, x0, #0xff6
    //     0xbc2d88: ldr             lr, [x21, lr, lsl #3]
    //     0xbc2d8c: blr             lr
    // 0xbc2d90: stur            x0, [fp, #-0x28]
    // 0xbc2d94: r0 = Text()
    //     0xbc2d94: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbc2d98: mov             x1, x0
    // 0xbc2d9c: ldur            x0, [fp, #-0x28]
    // 0xbc2da0: stur            x1, [fp, #-0x30]
    // 0xbc2da4: StoreField: r1->field_b = r0
    //     0xbc2da4: stur            w0, [x1, #0xb]
    // 0xbc2da8: r0 = AppBar()
    //     0xbc2da8: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xbc2dac: stur            x0, [fp, #-0x28]
    // 0xbc2db0: ldur            x16, [fp, #-0x30]
    // 0xbc2db4: str             x16, [SP]
    // 0xbc2db8: mov             x1, x0
    // 0xbc2dbc: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xbc2dbc: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xbc2dc0: ldr             x4, [x4, #0x6e8]
    // 0xbc2dc4: r0 = AppBar()
    //     0xbc2dc4: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xbc2dc8: ldur            x1, [fp, #-0x18]
    // 0xbc2dcc: ldur            x2, [fp, #-8]
    // 0xbc2dd0: r0 = []()
    //     0xbc2dd0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc2dd4: ldur            x2, [fp, #-0x20]
    // 0xbc2dd8: mov             x3, x0
    // 0xbc2ddc: r1 = Null
    //     0xbc2ddc: mov             x1, NULL
    // 0xbc2de0: stur            x3, [fp, #-8]
    // 0xbc2de4: cmp             w2, NULL
    // 0xbc2de8: b.eq            #0xbc2e0c
    // 0xbc2dec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc2dec: ldur            w4, [x2, #0x17]
    // 0xbc2df0: DecompressPointer r4
    //     0xbc2df0: add             x4, x4, HEAP, lsl #32
    // 0xbc2df4: r8 = X0 bound GetLifeCycleBase?
    //     0xbc2df4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc2df8: ldr             x8, [x8, #0xb98]
    // 0xbc2dfc: LoadField: r9 = r4->field_7
    //     0xbc2dfc: ldur            x9, [x4, #7]
    // 0xbc2e00: r3 = Null
    //     0xbc2e00: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a38] Null
    //     0xbc2e04: ldr             x3, [x3, #0xa38]
    // 0xbc2e08: blr             x9
    // 0xbc2e0c: r0 = Obx()
    //     0xbc2e0c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbc2e10: ldur            x2, [fp, #-0x10]
    // 0xbc2e14: r1 = Function '<anonymous closure>':.
    //     0xbc2e14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a48] AnonymousClosure: (0xbc30b4), in [package:nuonline/app/modules/article/article_prefix/views/article_prefix_view.dart] ArticlePrefixView::build (0xbc2cbc)
    //     0xbc2e18: ldr             x1, [x1, #0xa48]
    // 0xbc2e1c: stur            x0, [fp, #-0x10]
    // 0xbc2e20: r0 = AllocateClosure()
    //     0xbc2e20: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc2e24: mov             x1, x0
    // 0xbc2e28: ldur            x0, [fp, #-0x10]
    // 0xbc2e2c: StoreField: r0->field_b = r1
    //     0xbc2e2c: stur            w1, [x0, #0xb]
    // 0xbc2e30: r0 = RefreshIndicator()
    //     0xbc2e30: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbc2e34: mov             x3, x0
    // 0xbc2e38: ldur            x0, [fp, #-0x10]
    // 0xbc2e3c: stur            x3, [fp, #-0x18]
    // 0xbc2e40: StoreField: r3->field_b = r0
    //     0xbc2e40: stur            w0, [x3, #0xb]
    // 0xbc2e44: d0 = 40.000000
    //     0xbc2e44: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbc2e48: StoreField: r3->field_f = d0
    //     0xbc2e48: stur            d0, [x3, #0xf]
    // 0xbc2e4c: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbc2e4c: stur            xzr, [x3, #0x17]
    // 0xbc2e50: ldur            x2, [fp, #-8]
    // 0xbc2e54: r1 = Function 'onPageRefresh':.
    //     0xbc2e54: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xbc2e58: ldr             x1, [x1, #0xa60]
    // 0xbc2e5c: r0 = AllocateClosure()
    //     0xbc2e5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc2e60: mov             x1, x0
    // 0xbc2e64: ldur            x0, [fp, #-0x18]
    // 0xbc2e68: StoreField: r0->field_1f = r1
    //     0xbc2e68: stur            w1, [x0, #0x1f]
    // 0xbc2e6c: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbc2e6c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbc2e70: ldr             x1, [x1, #0xf58]
    // 0xbc2e74: StoreField: r0->field_2f = r1
    //     0xbc2e74: stur            w1, [x0, #0x2f]
    // 0xbc2e78: d0 = 2.500000
    //     0xbc2e78: fmov            d0, #2.50000000
    // 0xbc2e7c: StoreField: r0->field_3b = d0
    //     0xbc2e7c: stur            d0, [x0, #0x3b]
    // 0xbc2e80: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbc2e80: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbc2e84: ldr             x1, [x1, #0xa68]
    // 0xbc2e88: StoreField: r0->field_47 = r1
    //     0xbc2e88: stur            w1, [x0, #0x47]
    // 0xbc2e8c: d0 = 2.000000
    //     0xbc2e8c: fmov            d0, #2.00000000
    // 0xbc2e90: StoreField: r0->field_4b = d0
    //     0xbc2e90: stur            d0, [x0, #0x4b]
    // 0xbc2e94: r1 = Instance__IndicatorType
    //     0xbc2e94: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbc2e98: ldr             x1, [x1, #0xa70]
    // 0xbc2e9c: StoreField: r0->field_43 = r1
    //     0xbc2e9c: stur            w1, [x0, #0x43]
    // 0xbc2ea0: r1 = <FlexParentData>
    //     0xbc2ea0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbc2ea4: ldr             x1, [x1, #0x720]
    // 0xbc2ea8: r0 = Expanded()
    //     0xbc2ea8: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc2eac: mov             x3, x0
    // 0xbc2eb0: r0 = 1
    //     0xbc2eb0: movz            x0, #0x1
    // 0xbc2eb4: stur            x3, [fp, #-8]
    // 0xbc2eb8: StoreField: r3->field_13 = r0
    //     0xbc2eb8: stur            x0, [x3, #0x13]
    // 0xbc2ebc: r0 = Instance_FlexFit
    //     0xbc2ebc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbc2ec0: ldr             x0, [x0, #0x728]
    // 0xbc2ec4: StoreField: r3->field_1b = r0
    //     0xbc2ec4: stur            w0, [x3, #0x1b]
    // 0xbc2ec8: ldur            x0, [fp, #-0x18]
    // 0xbc2ecc: StoreField: r3->field_b = r0
    //     0xbc2ecc: stur            w0, [x3, #0xb]
    // 0xbc2ed0: r1 = Null
    //     0xbc2ed0: mov             x1, NULL
    // 0xbc2ed4: r2 = 2
    //     0xbc2ed4: movz            x2, #0x2
    // 0xbc2ed8: r0 = AllocateArray()
    //     0xbc2ed8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2edc: mov             x2, x0
    // 0xbc2ee0: ldur            x0, [fp, #-8]
    // 0xbc2ee4: stur            x2, [fp, #-0x10]
    // 0xbc2ee8: StoreField: r2->field_f = r0
    //     0xbc2ee8: stur            w0, [x2, #0xf]
    // 0xbc2eec: r1 = <Widget>
    //     0xbc2eec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc2ef0: r0 = AllocateGrowableArray()
    //     0xbc2ef0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc2ef4: mov             x1, x0
    // 0xbc2ef8: ldur            x0, [fp, #-0x10]
    // 0xbc2efc: stur            x1, [fp, #-8]
    // 0xbc2f00: StoreField: r1->field_f = r0
    //     0xbc2f00: stur            w0, [x1, #0xf]
    // 0xbc2f04: r0 = 2
    //     0xbc2f04: movz            x0, #0x2
    // 0xbc2f08: StoreField: r1->field_b = r0
    //     0xbc2f08: stur            w0, [x1, #0xb]
    // 0xbc2f0c: r0 = find()
    //     0xbc2f0c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc2f10: mov             x1, x0
    // 0xbc2f14: r0 = _adsVisibility()
    //     0xbc2f14: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc2f18: mov             x2, x0
    // 0xbc2f1c: r1 = Null
    //     0xbc2f1c: mov             x1, NULL
    // 0xbc2f20: r0 = AdsConfig.fromJson()
    //     0xbc2f20: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc2f24: LoadField: r1 = r0->field_f
    //     0xbc2f24: ldur            w1, [x0, #0xf]
    // 0xbc2f28: DecompressPointer r1
    //     0xbc2f28: add             x1, x1, HEAP, lsl #32
    // 0xbc2f2c: LoadField: r0 = r1->field_7
    //     0xbc2f2c: ldur            w0, [x1, #7]
    // 0xbc2f30: DecompressPointer r0
    //     0xbc2f30: add             x0, x0, HEAP, lsl #32
    // 0xbc2f34: tbnz            w0, #4, #0xbc2ff8
    // 0xbc2f38: ldur            x1, [fp, #-8]
    // 0xbc2f3c: r0 = find()
    //     0xbc2f3c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc2f40: mov             x1, x0
    // 0xbc2f44: r0 = _adsVisibility()
    //     0xbc2f44: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc2f48: mov             x2, x0
    // 0xbc2f4c: r1 = Null
    //     0xbc2f4c: mov             x1, NULL
    // 0xbc2f50: r0 = AdsConfig.fromJson()
    //     0xbc2f50: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc2f54: LoadField: r1 = r0->field_f
    //     0xbc2f54: ldur            w1, [x0, #0xf]
    // 0xbc2f58: DecompressPointer r1
    //     0xbc2f58: add             x1, x1, HEAP, lsl #32
    // 0xbc2f5c: LoadField: r0 = r1->field_f
    //     0xbc2f5c: ldur            w0, [x1, #0xf]
    // 0xbc2f60: DecompressPointer r0
    //     0xbc2f60: add             x0, x0, HEAP, lsl #32
    // 0xbc2f64: stur            x0, [fp, #-0x10]
    // 0xbc2f68: r0 = AdmobBannerWidget()
    //     0xbc2f68: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xbc2f6c: mov             x2, x0
    // 0xbc2f70: ldur            x0, [fp, #-0x10]
    // 0xbc2f74: stur            x2, [fp, #-0x18]
    // 0xbc2f78: StoreField: r2->field_b = r0
    //     0xbc2f78: stur            w0, [x2, #0xb]
    // 0xbc2f7c: ldur            x0, [fp, #-8]
    // 0xbc2f80: LoadField: r1 = r0->field_b
    //     0xbc2f80: ldur            w1, [x0, #0xb]
    // 0xbc2f84: LoadField: r3 = r0->field_f
    //     0xbc2f84: ldur            w3, [x0, #0xf]
    // 0xbc2f88: DecompressPointer r3
    //     0xbc2f88: add             x3, x3, HEAP, lsl #32
    // 0xbc2f8c: LoadField: r4 = r3->field_b
    //     0xbc2f8c: ldur            w4, [x3, #0xb]
    // 0xbc2f90: r3 = LoadInt32Instr(r1)
    //     0xbc2f90: sbfx            x3, x1, #1, #0x1f
    // 0xbc2f94: stur            x3, [fp, #-0x38]
    // 0xbc2f98: r1 = LoadInt32Instr(r4)
    //     0xbc2f98: sbfx            x1, x4, #1, #0x1f
    // 0xbc2f9c: cmp             x3, x1
    // 0xbc2fa0: b.ne            #0xbc2fac
    // 0xbc2fa4: mov             x1, x0
    // 0xbc2fa8: r0 = _growToNextCapacity()
    //     0xbc2fa8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc2fac: ldur            x2, [fp, #-8]
    // 0xbc2fb0: ldur            x3, [fp, #-0x38]
    // 0xbc2fb4: add             x0, x3, #1
    // 0xbc2fb8: lsl             x1, x0, #1
    // 0xbc2fbc: StoreField: r2->field_b = r1
    //     0xbc2fbc: stur            w1, [x2, #0xb]
    // 0xbc2fc0: LoadField: r1 = r2->field_f
    //     0xbc2fc0: ldur            w1, [x2, #0xf]
    // 0xbc2fc4: DecompressPointer r1
    //     0xbc2fc4: add             x1, x1, HEAP, lsl #32
    // 0xbc2fc8: ldur            x0, [fp, #-0x18]
    // 0xbc2fcc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbc2fcc: add             x25, x1, x3, lsl #2
    //     0xbc2fd0: add             x25, x25, #0xf
    //     0xbc2fd4: str             w0, [x25]
    //     0xbc2fd8: tbz             w0, #0, #0xbc2ff4
    //     0xbc2fdc: ldurb           w16, [x1, #-1]
    //     0xbc2fe0: ldurb           w17, [x0, #-1]
    //     0xbc2fe4: and             x16, x17, x16, lsr #2
    //     0xbc2fe8: tst             x16, HEAP, lsr #32
    //     0xbc2fec: b.eq            #0xbc2ff4
    //     0xbc2ff0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc2ff4: b               #0xbc2ffc
    // 0xbc2ff8: ldur            x2, [fp, #-8]
    // 0xbc2ffc: ldur            x0, [fp, #-0x28]
    // 0xbc3000: r0 = Column()
    //     0xbc3000: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc3004: mov             x1, x0
    // 0xbc3008: r0 = Instance_Axis
    //     0xbc3008: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc300c: stur            x1, [fp, #-0x10]
    // 0xbc3010: StoreField: r1->field_f = r0
    //     0xbc3010: stur            w0, [x1, #0xf]
    // 0xbc3014: r0 = Instance_MainAxisAlignment
    //     0xbc3014: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc3018: ldr             x0, [x0, #0x730]
    // 0xbc301c: StoreField: r1->field_13 = r0
    //     0xbc301c: stur            w0, [x1, #0x13]
    // 0xbc3020: r0 = Instance_MainAxisSize
    //     0xbc3020: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc3024: ldr             x0, [x0, #0x738]
    // 0xbc3028: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc3028: stur            w0, [x1, #0x17]
    // 0xbc302c: r0 = Instance_CrossAxisAlignment
    //     0xbc302c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc3030: ldr             x0, [x0, #0x740]
    // 0xbc3034: StoreField: r1->field_1b = r0
    //     0xbc3034: stur            w0, [x1, #0x1b]
    // 0xbc3038: r0 = Instance_VerticalDirection
    //     0xbc3038: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc303c: ldr             x0, [x0, #0x748]
    // 0xbc3040: StoreField: r1->field_23 = r0
    //     0xbc3040: stur            w0, [x1, #0x23]
    // 0xbc3044: r0 = Instance_Clip
    //     0xbc3044: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc3048: ldr             x0, [x0, #0x750]
    // 0xbc304c: StoreField: r1->field_2b = r0
    //     0xbc304c: stur            w0, [x1, #0x2b]
    // 0xbc3050: StoreField: r1->field_2f = rZR
    //     0xbc3050: stur            xzr, [x1, #0x2f]
    // 0xbc3054: ldur            x0, [fp, #-8]
    // 0xbc3058: StoreField: r1->field_b = r0
    //     0xbc3058: stur            w0, [x1, #0xb]
    // 0xbc305c: r0 = Scaffold()
    //     0xbc305c: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xbc3060: ldur            x1, [fp, #-0x28]
    // 0xbc3064: StoreField: r0->field_13 = r1
    //     0xbc3064: stur            w1, [x0, #0x13]
    // 0xbc3068: ldur            x1, [fp, #-0x10]
    // 0xbc306c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc306c: stur            w1, [x0, #0x17]
    // 0xbc3070: r1 = Instance_AlignmentDirectional
    //     0xbc3070: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xbc3074: ldr             x1, [x1, #0x758]
    // 0xbc3078: StoreField: r0->field_2b = r1
    //     0xbc3078: stur            w1, [x0, #0x2b]
    // 0xbc307c: r1 = true
    //     0xbc307c: add             x1, NULL, #0x20  ; true
    // 0xbc3080: StoreField: r0->field_53 = r1
    //     0xbc3080: stur            w1, [x0, #0x53]
    // 0xbc3084: r2 = Instance_DragStartBehavior
    //     0xbc3084: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbc3088: StoreField: r0->field_57 = r2
    //     0xbc3088: stur            w2, [x0, #0x57]
    // 0xbc308c: r2 = false
    //     0xbc308c: add             x2, NULL, #0x30  ; false
    // 0xbc3090: StoreField: r0->field_b = r2
    //     0xbc3090: stur            w2, [x0, #0xb]
    // 0xbc3094: StoreField: r0->field_f = r2
    //     0xbc3094: stur            w2, [x0, #0xf]
    // 0xbc3098: StoreField: r0->field_5f = r1
    //     0xbc3098: stur            w1, [x0, #0x5f]
    // 0xbc309c: StoreField: r0->field_63 = r1
    //     0xbc309c: stur            w1, [x0, #0x63]
    // 0xbc30a0: LeaveFrame
    //     0xbc30a0: mov             SP, fp
    //     0xbc30a4: ldp             fp, lr, [SP], #0x10
    // 0xbc30a8: ret
    //     0xbc30a8: ret             
    // 0xbc30ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc30ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc30b0: b               #0xbc2cdc
  }
  [closure] NotificationListener<ScrollNotification> <anonymous closure>(dynamic) {
    // ** addr: 0xbc30b4, size: 0x280
    // 0xbc30b4: EnterFrame
    //     0xbc30b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc30b8: mov             fp, SP
    // 0xbc30bc: AllocStack(0x48)
    //     0xbc30bc: sub             SP, SP, #0x48
    // 0xbc30c0: SetupParameters()
    //     0xbc30c0: ldr             x0, [fp, #0x10]
    //     0xbc30c4: ldur            w2, [x0, #0x17]
    //     0xbc30c8: add             x2, x2, HEAP, lsl #32
    //     0xbc30cc: stur            x2, [fp, #-0x10]
    // 0xbc30d0: CheckStackOverflow
    //     0xbc30d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc30d4: cmp             SP, x16
    //     0xbc30d8: b.ls            #0xbc330c
    // 0xbc30dc: LoadField: r0 = r2->field_f
    //     0xbc30dc: ldur            w0, [x2, #0xf]
    // 0xbc30e0: DecompressPointer r0
    //     0xbc30e0: add             x0, x0, HEAP, lsl #32
    // 0xbc30e4: stur            x0, [fp, #-8]
    // 0xbc30e8: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc30e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc30ec: ldr             x0, [x0, #0x26d0]
    //     0xbc30f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc30f4: cmp             w0, w16
    //     0xbc30f8: b.ne            #0xbc3108
    //     0xbc30fc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc3100: ldr             x2, [x2, #0xb90]
    //     0xbc3104: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc3108: mov             x1, x0
    // 0xbc310c: ldur            x2, [fp, #-8]
    // 0xbc3110: stur            x0, [fp, #-0x18]
    // 0xbc3114: r0 = []()
    //     0xbc3114: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3118: mov             x3, x0
    // 0xbc311c: ldur            x0, [fp, #-8]
    // 0xbc3120: stur            x3, [fp, #-0x20]
    // 0xbc3124: LoadField: r2 = r0->field_b
    //     0xbc3124: ldur            w2, [x0, #0xb]
    // 0xbc3128: DecompressPointer r2
    //     0xbc3128: add             x2, x2, HEAP, lsl #32
    // 0xbc312c: mov             x0, x3
    // 0xbc3130: r1 = Null
    //     0xbc3130: mov             x1, NULL
    // 0xbc3134: cmp             w2, NULL
    // 0xbc3138: b.eq            #0xbc315c
    // 0xbc313c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc313c: ldur            w4, [x2, #0x17]
    // 0xbc3140: DecompressPointer r4
    //     0xbc3140: add             x4, x4, HEAP, lsl #32
    // 0xbc3144: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3144: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3148: ldr             x8, [x8, #0xb98]
    // 0xbc314c: LoadField: r9 = r4->field_7
    //     0xbc314c: ldur            x9, [x4, #7]
    // 0xbc3150: r3 = Null
    //     0xbc3150: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a50] Null
    //     0xbc3154: ldr             x3, [x3, #0xa50]
    // 0xbc3158: blr             x9
    // 0xbc315c: ldur            x0, [fp, #-0x10]
    // 0xbc3160: LoadField: r3 = r0->field_f
    //     0xbc3160: ldur            w3, [x0, #0xf]
    // 0xbc3164: DecompressPointer r3
    //     0xbc3164: add             x3, x3, HEAP, lsl #32
    // 0xbc3168: ldur            x1, [fp, #-0x18]
    // 0xbc316c: mov             x2, x3
    // 0xbc3170: stur            x3, [fp, #-8]
    // 0xbc3174: r0 = []()
    //     0xbc3174: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3178: mov             x3, x0
    // 0xbc317c: ldur            x0, [fp, #-8]
    // 0xbc3180: stur            x3, [fp, #-0x28]
    // 0xbc3184: LoadField: r2 = r0->field_b
    //     0xbc3184: ldur            w2, [x0, #0xb]
    // 0xbc3188: DecompressPointer r2
    //     0xbc3188: add             x2, x2, HEAP, lsl #32
    // 0xbc318c: mov             x0, x3
    // 0xbc3190: r1 = Null
    //     0xbc3190: mov             x1, NULL
    // 0xbc3194: cmp             w2, NULL
    // 0xbc3198: b.eq            #0xbc31bc
    // 0xbc319c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc319c: ldur            w4, [x2, #0x17]
    // 0xbc31a0: DecompressPointer r4
    //     0xbc31a0: add             x4, x4, HEAP, lsl #32
    // 0xbc31a4: r8 = X0 bound GetLifeCycleBase?
    //     0xbc31a4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc31a8: ldr             x8, [x8, #0xb98]
    // 0xbc31ac: LoadField: r9 = r4->field_7
    //     0xbc31ac: ldur            x9, [x4, #7]
    // 0xbc31b0: r3 = Null
    //     0xbc31b0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a60] Null
    //     0xbc31b4: ldr             x3, [x3, #0xa60]
    // 0xbc31b8: blr             x9
    // 0xbc31bc: ldur            x1, [fp, #-0x28]
    // 0xbc31c0: r0 = itemsCount()
    //     0xbc31c0: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbc31c4: mov             x3, x0
    // 0xbc31c8: ldur            x0, [fp, #-0x10]
    // 0xbc31cc: stur            x3, [fp, #-0x30]
    // 0xbc31d0: LoadField: r4 = r0->field_f
    //     0xbc31d0: ldur            w4, [x0, #0xf]
    // 0xbc31d4: DecompressPointer r4
    //     0xbc31d4: add             x4, x4, HEAP, lsl #32
    // 0xbc31d8: ldur            x1, [fp, #-0x18]
    // 0xbc31dc: mov             x2, x4
    // 0xbc31e0: stur            x4, [fp, #-8]
    // 0xbc31e4: r0 = []()
    //     0xbc31e4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc31e8: mov             x3, x0
    // 0xbc31ec: ldur            x0, [fp, #-8]
    // 0xbc31f0: stur            x3, [fp, #-0x18]
    // 0xbc31f4: LoadField: r2 = r0->field_b
    //     0xbc31f4: ldur            w2, [x0, #0xb]
    // 0xbc31f8: DecompressPointer r2
    //     0xbc31f8: add             x2, x2, HEAP, lsl #32
    // 0xbc31fc: mov             x0, x3
    // 0xbc3200: r1 = Null
    //     0xbc3200: mov             x1, NULL
    // 0xbc3204: cmp             w2, NULL
    // 0xbc3208: b.eq            #0xbc322c
    // 0xbc320c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc320c: ldur            w4, [x2, #0x17]
    // 0xbc3210: DecompressPointer r4
    //     0xbc3210: add             x4, x4, HEAP, lsl #32
    // 0xbc3214: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3214: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3218: ldr             x8, [x8, #0xb98]
    // 0xbc321c: LoadField: r9 = r4->field_7
    //     0xbc321c: ldur            x9, [x4, #7]
    // 0xbc3220: r3 = Null
    //     0xbc3220: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a70] Null
    //     0xbc3224: ldr             x3, [x3, #0xa70]
    // 0xbc3228: blr             x9
    // 0xbc322c: ldur            x1, [fp, #-0x18]
    // 0xbc3230: r0 = itemsCount()
    //     0xbc3230: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbc3234: scvtf           d0, x0
    // 0xbc3238: d1 = 10.000000
    //     0xbc3238: fmov            d1, #10.00000000
    // 0xbc323c: fdiv            d2, d0, d1
    // 0xbc3240: fcmp            d2, d2
    // 0xbc3244: b.vs            #0xbc3314
    // 0xbc3248: fcvtms          x0, d2
    // 0xbc324c: asr             x16, x0, #0x1e
    // 0xbc3250: cmp             x16, x0, asr #63
    // 0xbc3254: b.ne            #0xbc3314
    // 0xbc3258: lsl             x0, x0, #1
    // 0xbc325c: r1 = LoadInt32Instr(r0)
    //     0xbc325c: sbfx            x1, x0, #1, #0x1f
    //     0xbc3260: tbz             w0, #0, #0xbc3268
    //     0xbc3264: ldur            x1, [x0, #7]
    // 0xbc3268: ldur            x0, [fp, #-0x30]
    // 0xbc326c: add             x3, x0, x1
    // 0xbc3270: stur            x3, [fp, #-0x38]
    // 0xbc3274: r1 = Function '<anonymous closure>':.
    //     0xbc3274: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a80] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbc3278: ldr             x1, [x1, #0xa80]
    // 0xbc327c: r2 = Null
    //     0xbc327c: mov             x2, NULL
    // 0xbc3280: r0 = AllocateClosure()
    //     0xbc3280: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc3284: ldur            x2, [fp, #-0x10]
    // 0xbc3288: r1 = Function '<anonymous closure>':.
    //     0xbc3288: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a88] AnonymousClosure: (0xbc3334), in [package:nuonline/app/modules/article/article_prefix/views/article_prefix_view.dart] ArticlePrefixView::build (0xbc2cbc)
    //     0xbc328c: ldr             x1, [x1, #0xa88]
    // 0xbc3290: stur            x0, [fp, #-8]
    // 0xbc3294: r0 = AllocateClosure()
    //     0xbc3294: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc3298: stur            x0, [fp, #-0x10]
    // 0xbc329c: r0 = ListView()
    //     0xbc329c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbc32a0: stur            x0, [fp, #-0x18]
    // 0xbc32a4: r16 = true
    //     0xbc32a4: add             x16, NULL, #0x20  ; true
    // 0xbc32a8: r30 = Instance_EdgeInsets
    //     0xbc32a8: add             lr, PP, #0x2a, lsl #12  ; [pp+0x2af58] Obj!EdgeInsets@e125e1
    //     0xbc32ac: ldr             lr, [lr, #0xf58]
    // 0xbc32b0: stp             lr, x16, [SP]
    // 0xbc32b4: mov             x1, x0
    // 0xbc32b8: ldur            x2, [fp, #-0x10]
    // 0xbc32bc: ldur            x3, [fp, #-0x38]
    // 0xbc32c0: ldur            x5, [fp, #-8]
    // 0xbc32c4: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xbc32c4: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbc32c8: ldr             x4, [x4, #0x100]
    // 0xbc32cc: r0 = ListView.separated()
    //     0xbc32cc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbc32d0: ldur            x2, [fp, #-0x20]
    // 0xbc32d4: r1 = Function 'onPageScrolled':.
    //     0xbc32d4: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xbc32d8: ldr             x1, [x1, #0xa88]
    // 0xbc32dc: r0 = AllocateClosure()
    //     0xbc32dc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc32e0: r1 = <ScrollNotification>
    //     0xbc32e0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbc32e4: ldr             x1, [x1, #0x110]
    // 0xbc32e8: stur            x0, [fp, #-8]
    // 0xbc32ec: r0 = NotificationListener()
    //     0xbc32ec: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbc32f0: ldur            x1, [fp, #-8]
    // 0xbc32f4: StoreField: r0->field_13 = r1
    //     0xbc32f4: stur            w1, [x0, #0x13]
    // 0xbc32f8: ldur            x1, [fp, #-0x18]
    // 0xbc32fc: StoreField: r0->field_b = r1
    //     0xbc32fc: stur            w1, [x0, #0xb]
    // 0xbc3300: LeaveFrame
    //     0xbc3300: mov             SP, fp
    //     0xbc3304: ldp             fp, lr, [SP], #0x10
    // 0xbc3308: ret
    //     0xbc3308: ret             
    // 0xbc330c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc330c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc3310: b               #0xbc30dc
    // 0xbc3314: SaveReg d2
    //     0xbc3314: str             q2, [SP, #-0x10]!
    // 0xbc3318: d0 = 0.000000
    //     0xbc3318: fmov            d0, d2
    // 0xbc331c: r0 = 68
    //     0xbc331c: movz            x0, #0x44
    // 0xbc3320: r30 = DoubleToIntegerStub
    //     0xbc3320: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbc3324: LoadField: r30 = r30->field_7
    //     0xbc3324: ldur            lr, [lr, #7]
    // 0xbc3328: blr             lr
    // 0xbc332c: RestoreReg d2
    //     0xbc332c: ldr             q2, [SP], #0x10
    // 0xbc3330: b               #0xbc325c
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbc3334, size: 0x200
    // 0xbc3334: EnterFrame
    //     0xbc3334: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3338: mov             fp, SP
    // 0xbc333c: AllocStack(0x20)
    //     0xbc333c: sub             SP, SP, #0x20
    // 0xbc3340: SetupParameters()
    //     0xbc3340: fmov            d0, #10.00000000
    //     0xbc3344: ldr             x0, [fp, #0x20]
    //     0xbc3348: ldur            w1, [x0, #0x17]
    //     0xbc334c: add             x1, x1, HEAP, lsl #32
    // 0xbc3340: d0 = 10.000000
    // 0xbc3350: CheckStackOverflow
    //     0xbc3350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc3354: cmp             SP, x16
    //     0xbc3358: b.ls            #0xbc34fc
    // 0xbc335c: ldr             x0, [fp, #0x10]
    // 0xbc3360: r2 = LoadInt32Instr(r0)
    //     0xbc3360: sbfx            x2, x0, #1, #0x1f
    //     0xbc3364: tbz             w0, #0, #0xbc336c
    //     0xbc3368: ldur            x2, [x0, #7]
    // 0xbc336c: stur            x2, [fp, #-0x18]
    // 0xbc3370: add             x0, x2, #5
    // 0xbc3374: scvtf           d1, x0
    // 0xbc3378: fdiv            d2, d1, d0
    // 0xbc337c: fcmp            d2, d2
    // 0xbc3380: b.vs            #0xbc3504
    // 0xbc3384: fcvtms          x0, d2
    // 0xbc3388: asr             x16, x0, #0x1e
    // 0xbc338c: cmp             x16, x0, asr #63
    // 0xbc3390: b.ne            #0xbc3504
    // 0xbc3394: lsl             x0, x0, #1
    // 0xbc3398: r3 = LoadInt32Instr(r0)
    //     0xbc3398: sbfx            x3, x0, #1, #0x1f
    //     0xbc339c: tbz             w0, #0, #0xbc33a4
    //     0xbc33a0: ldur            x3, [x0, #7]
    // 0xbc33a4: sub             x0, x2, x3
    // 0xbc33a8: stur            x0, [fp, #-0x10]
    // 0xbc33ac: LoadField: r3 = r1->field_f
    //     0xbc33ac: ldur            w3, [x1, #0xf]
    // 0xbc33b0: DecompressPointer r3
    //     0xbc33b0: add             x3, x3, HEAP, lsl #32
    // 0xbc33b4: stur            x3, [fp, #-8]
    // 0xbc33b8: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc33b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc33bc: ldr             x0, [x0, #0x26d0]
    //     0xbc33c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc33c4: cmp             w0, w16
    //     0xbc33c8: b.ne            #0xbc33d8
    //     0xbc33cc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc33d0: ldr             x2, [x2, #0xb90]
    //     0xbc33d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc33d8: mov             x1, x0
    // 0xbc33dc: ldur            x2, [fp, #-8]
    // 0xbc33e0: r0 = []()
    //     0xbc33e0: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc33e4: mov             x3, x0
    // 0xbc33e8: ldur            x0, [fp, #-8]
    // 0xbc33ec: stur            x3, [fp, #-0x20]
    // 0xbc33f0: LoadField: r2 = r0->field_b
    //     0xbc33f0: ldur            w2, [x0, #0xb]
    // 0xbc33f4: DecompressPointer r2
    //     0xbc33f4: add             x2, x2, HEAP, lsl #32
    // 0xbc33f8: mov             x0, x3
    // 0xbc33fc: r1 = Null
    //     0xbc33fc: mov             x1, NULL
    // 0xbc3400: cmp             w2, NULL
    // 0xbc3404: b.eq            #0xbc3428
    // 0xbc3408: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc3408: ldur            w4, [x2, #0x17]
    // 0xbc340c: DecompressPointer r4
    //     0xbc340c: add             x4, x4, HEAP, lsl #32
    // 0xbc3410: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3410: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3414: ldr             x8, [x8, #0xb98]
    // 0xbc3418: LoadField: r9 = r4->field_7
    //     0xbc3418: ldur            x9, [x4, #7]
    // 0xbc341c: r3 = Null
    //     0xbc341c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a90] Null
    //     0xbc3420: ldr             x3, [x3, #0xa90]
    // 0xbc3424: blr             x9
    // 0xbc3428: ldur            x1, [fp, #-0x20]
    // 0xbc342c: ldur            x2, [fp, #-0x10]
    // 0xbc3430: r0 = find()
    //     0xbc3430: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xbc3434: stur            x0, [fp, #-8]
    // 0xbc3438: cmp             w0, NULL
    // 0xbc343c: b.ne            #0xbc3454
    // 0xbc3440: r0 = Instance_NArticleListTile
    //     0xbc3440: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xbc3444: ldr             x0, [x0, #0xa90]
    // 0xbc3448: LeaveFrame
    //     0xbc3448: mov             SP, fp
    //     0xbc344c: ldp             fp, lr, [SP], #0x10
    // 0xbc3450: ret
    //     0xbc3450: ret             
    // 0xbc3454: ldur            x1, [fp, #-0x18]
    // 0xbc3458: r2 = 10
    //     0xbc3458: movz            x2, #0xa
    // 0xbc345c: sdiv            x4, x1, x2
    // 0xbc3460: msub            x3, x4, x2, x1
    // 0xbc3464: cmp             x3, xzr
    // 0xbc3468: b.lt            #0xbc352c
    // 0xbc346c: cmp             x3, #4
    // 0xbc3470: b.ne            #0xbc34dc
    // 0xbc3474: r0 = find()
    //     0xbc3474: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc3478: mov             x1, x0
    // 0xbc347c: r0 = _adsVisibility()
    //     0xbc347c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc3480: mov             x2, x0
    // 0xbc3484: r1 = Null
    //     0xbc3484: mov             x1, NULL
    // 0xbc3488: r0 = AdsConfig.fromJson()
    //     0xbc3488: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc348c: LoadField: r1 = r0->field_b
    //     0xbc348c: ldur            w1, [x0, #0xb]
    // 0xbc3490: DecompressPointer r1
    //     0xbc3490: add             x1, x1, HEAP, lsl #32
    // 0xbc3494: LoadField: r0 = r1->field_7
    //     0xbc3494: ldur            w0, [x1, #7]
    // 0xbc3498: DecompressPointer r0
    //     0xbc3498: add             x0, x0, HEAP, lsl #32
    // 0xbc349c: tbnz            w0, #4, #0xbc34c8
    // 0xbc34a0: r0 = find()
    //     0xbc34a0: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc34a4: mov             x1, x0
    // 0xbc34a8: r0 = _adsVisibility()
    //     0xbc34a8: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc34ac: mov             x2, x0
    // 0xbc34b0: r1 = Null
    //     0xbc34b0: mov             x1, NULL
    // 0xbc34b4: r0 = AdsConfig.fromJson()
    //     0xbc34b4: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc34b8: r0 = AdmobArticleNativeWidget()
    //     0xbc34b8: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xbc34bc: LeaveFrame
    //     0xbc34bc: mov             SP, fp
    //     0xbc34c0: ldp             fp, lr, [SP], #0x10
    // 0xbc34c4: ret
    //     0xbc34c4: ret             
    // 0xbc34c8: r0 = Instance_SizedBox
    //     0xbc34c8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xbc34cc: ldr             x0, [x0, #0xc40]
    // 0xbc34d0: LeaveFrame
    //     0xbc34d0: mov             SP, fp
    //     0xbc34d4: ldp             fp, lr, [SP], #0x10
    // 0xbc34d8: ret
    //     0xbc34d8: ret             
    // 0xbc34dc: r0 = ArticleItem()
    //     0xbc34dc: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xbc34e0: ldur            x1, [fp, #-8]
    // 0xbc34e4: StoreField: r0->field_b = r1
    //     0xbc34e4: stur            w1, [x0, #0xb]
    // 0xbc34e8: r1 = false
    //     0xbc34e8: add             x1, NULL, #0x30  ; false
    // 0xbc34ec: StoreField: r0->field_13 = r1
    //     0xbc34ec: stur            w1, [x0, #0x13]
    // 0xbc34f0: LeaveFrame
    //     0xbc34f0: mov             SP, fp
    //     0xbc34f4: ldp             fp, lr, [SP], #0x10
    // 0xbc34f8: ret
    //     0xbc34f8: ret             
    // 0xbc34fc: r0 = StackOverflowSharedWithFPURegs()
    //     0xbc34fc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbc3500: b               #0xbc335c
    // 0xbc3504: SaveReg d2
    //     0xbc3504: str             q2, [SP, #-0x10]!
    // 0xbc3508: stp             x1, x2, [SP, #-0x10]!
    // 0xbc350c: d0 = 0.000000
    //     0xbc350c: fmov            d0, d2
    // 0xbc3510: r0 = 68
    //     0xbc3510: movz            x0, #0x44
    // 0xbc3514: r30 = DoubleToIntegerStub
    //     0xbc3514: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbc3518: LoadField: r30 = r30->field_7
    //     0xbc3518: ldur            lr, [lr, #7]
    // 0xbc351c: blr             lr
    // 0xbc3520: ldp             x1, x2, [SP], #0x10
    // 0xbc3524: RestoreReg d2
    //     0xbc3524: ldr             q2, [SP], #0x10
    // 0xbc3528: b               #0xbc3398
    // 0xbc352c: add             x3, x3, x2
    // 0xbc3530: b               #0xbc346c
  }
}
