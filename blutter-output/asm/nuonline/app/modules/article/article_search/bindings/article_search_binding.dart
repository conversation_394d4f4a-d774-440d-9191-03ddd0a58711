// lib: , url: package:nuonline/app/modules/article/article_search/bindings/article_search_binding.dart

// class id: 1050139, size: 0x8
class :: {
}

// class id: 2192, size: 0x8, field offset: 0x8
class ArticleSearchBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80de54, size: 0x70
    // 0x80de54: EnterFrame
    //     0x80de54: stp             fp, lr, [SP, #-0x10]!
    //     0x80de58: mov             fp, SP
    // 0x80de5c: AllocStack(0x10)
    //     0x80de5c: sub             SP, SP, #0x10
    // 0x80de60: CheckStackOverflow
    //     0x80de60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80de64: cmp             SP, x16
    //     0x80de68: b.ls            #0x80debc
    // 0x80de6c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80de6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80de70: ldr             x0, [x0, #0x2670]
    //     0x80de74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80de78: cmp             w0, w16
    //     0x80de7c: b.ne            #0x80de88
    //     0x80de80: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80de84: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80de88: r1 = Function '<anonymous closure>':.
    //     0x80de88: add             x1, PP, #0x36, lsl #12  ; [pp+0x36528] AnonymousClosure: (0x80dec4), in [package:nuonline/app/modules/article/article_search/bindings/article_search_binding.dart] ArticleSearchBinding::dependencies (0x80de54)
    //     0x80de8c: ldr             x1, [x1, #0x528]
    // 0x80de90: r2 = Null
    //     0x80de90: mov             x2, NULL
    // 0x80de94: r0 = AllocateClosure()
    //     0x80de94: bl              #0xec1630  ; AllocateClosureStub
    // 0x80de98: r16 = <ArticleSearchController>
    //     0x80de98: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d10] TypeArguments: <ArticleSearchController>
    //     0x80de9c: ldr             x16, [x16, #0xd10]
    // 0x80dea0: stp             x0, x16, [SP]
    // 0x80dea4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80dea4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80dea8: r0 = Inst.lazyPut()
    //     0x80dea8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80deac: r0 = Null
    //     0x80deac: mov             x0, NULL
    // 0x80deb0: LeaveFrame
    //     0x80deb0: mov             SP, fp
    //     0x80deb4: ldp             fp, lr, [SP], #0x10
    // 0x80deb8: ret
    //     0x80deb8: ret             
    // 0x80debc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80debc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80dec0: b               #0x80de6c
  }
  [closure] ArticleSearchController <anonymous closure>(dynamic) {
    // ** addr: 0x80dec4, size: 0x9c
    // 0x80dec4: EnterFrame
    //     0x80dec4: stp             fp, lr, [SP, #-0x10]!
    //     0x80dec8: mov             fp, SP
    // 0x80decc: AllocStack(0x20)
    //     0x80decc: sub             SP, SP, #0x20
    // 0x80ded0: CheckStackOverflow
    //     0x80ded0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80ded4: cmp             SP, x16
    //     0x80ded8: b.ls            #0x80df58
    // 0x80dedc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80dedc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80dee0: ldr             x0, [x0, #0x2670]
    //     0x80dee4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80dee8: cmp             w0, w16
    //     0x80deec: b.ne            #0x80def8
    //     0x80def0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80def4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80def8: r16 = <ArticleRepository>
    //     0x80def8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80defc: ldr             x16, [x16, #0x98]
    // 0x80df00: r30 = "article_repo"
    //     0x80df00: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80df04: ldr             lr, [lr, #0xa0]
    // 0x80df08: stp             lr, x16, [SP]
    // 0x80df0c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80df0c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80df10: r0 = Inst.find()
    //     0x80df10: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80df14: stur            x0, [fp, #-8]
    // 0x80df18: r16 = <ContentStorage>
    //     0x80df18: ldr             x16, [PP, #0xf8]  ; [pp+0xf8] TypeArguments: <ContentStorage>
    // 0x80df1c: r30 = "content_storage"
    //     0x80df1c: ldr             lr, [PP, #0xe8]  ; [pp+0xe8] "content_storage"
    // 0x80df20: stp             lr, x16, [SP]
    // 0x80df24: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80df24: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80df28: r0 = Inst.find()
    //     0x80df28: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80df2c: stur            x0, [fp, #-0x10]
    // 0x80df30: r0 = ArticleSearchController()
    //     0x80df30: bl              #0x80e160  ; AllocateArticleSearchControllerStub -> ArticleSearchController (size=0x54)
    // 0x80df34: mov             x1, x0
    // 0x80df38: ldur            x2, [fp, #-8]
    // 0x80df3c: ldur            x3, [fp, #-0x10]
    // 0x80df40: stur            x0, [fp, #-8]
    // 0x80df44: r0 = ArticleSearchController()
    //     0x80df44: bl              #0x80df60  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::ArticleSearchController
    // 0x80df48: ldur            x0, [fp, #-8]
    // 0x80df4c: LeaveFrame
    //     0x80df4c: mov             SP, fp
    //     0x80df50: ldp             fp, lr, [SP], #0x10
    // 0x80df54: ret
    //     0x80df54: ret             
    // 0x80df58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80df58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80df5c: b               #0x80dedc
  }
}
