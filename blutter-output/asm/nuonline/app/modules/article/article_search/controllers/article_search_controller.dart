// lib: , url: package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart

// class id: 1050140, size: 0x8
class :: {
}

// class id: 2047, size: 0x38, field offset: 0x38
//   transformed mixin,
abstract class _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin extends _ArticleAuthorController&GetxController&PagingMixin
     with AnalyticMixin {

  _ withAnalytic(/* No info */) {
    // ** addr: 0x8abac8, size: 0x88
    // 0x8abac8: EnterFrame
    //     0x8abac8: stp             fp, lr, [SP, #-0x10]!
    //     0x8abacc: mov             fp, SP
    // 0x8abad0: AllocStack(0x18)
    //     0x8abad0: sub             SP, SP, #0x18
    // 0x8abad4: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x8abad4: mov             x0, x2
    //     0x8abad8: stur            x2, [fp, #-8]
    // 0x8abadc: CheckStackOverflow
    //     0x8abadc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8abae0: cmp             SP, x16
    //     0x8abae4: b.ls            #0x8abb48
    // 0x8abae8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8abae8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8abaec: ldr             x0, [x0, #0x2670]
    //     0x8abaf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8abaf4: cmp             w0, w16
    //     0x8abaf8: b.ne            #0x8abb04
    //     0x8abafc: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x8abb00: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8abb04: r16 = <AnalyticService>
    //     0x8abb04: add             x16, PP, #0xd, lsl #12  ; [pp+0xdc60] TypeArguments: <AnalyticService>
    //     0x8abb08: ldr             x16, [x16, #0xc60]
    // 0x8abb0c: r30 = "analytic_service"
    //     0x8abb0c: add             lr, PP, #0xd, lsl #12  ; [pp+0xdc68] "analytic_service"
    //     0x8abb10: ldr             lr, [lr, #0xc68]
    // 0x8abb14: stp             lr, x16, [SP]
    // 0x8abb18: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x8abb18: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x8abb1c: r0 = Inst.find()
    //     0x8abb1c: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x8abb20: ldur            x16, [fp, #-8]
    // 0x8abb24: stp             x0, x16, [SP]
    // 0x8abb28: ldur            x0, [fp, #-8]
    // 0x8abb2c: ClosureCall
    //     0x8abb2c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8abb30: ldur            x2, [x0, #0x1f]
    //     0x8abb34: blr             x2
    // 0x8abb38: r0 = Null
    //     0x8abb38: mov             x0, NULL
    // 0x8abb3c: LeaveFrame
    //     0x8abb3c: mov             SP, fp
    //     0x8abb40: ldp             fp, lr, [SP], #0x10
    // 0x8abb44: ret
    //     0x8abb44: ret             
    // 0x8abb48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abb48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abb4c: b               #0x8abae8
  }
}

// class id: 2048, size: 0x54, field offset: 0x38
class ArticleSearchController extends _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin {

  late TextEditingController textController; // offset: 0x40

  _ ArticleSearchController(/* No info */) {
    // ** addr: 0x80df60, size: 0x160
    // 0x80df60: EnterFrame
    //     0x80df60: stp             fp, lr, [SP, #-0x10]!
    //     0x80df64: mov             fp, SP
    // 0x80df68: AllocStack(0x28)
    //     0x80df68: sub             SP, SP, #0x28
    // 0x80df6c: r0 = Sentinel
    //     0x80df6c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80df70: mov             x4, x1
    // 0x80df74: stur            x2, [fp, #-0x10]
    // 0x80df78: mov             x16, x3
    // 0x80df7c: mov             x3, x2
    // 0x80df80: mov             x2, x16
    // 0x80df84: stur            x1, [fp, #-8]
    // 0x80df88: stur            x2, [fp, #-0x18]
    // 0x80df8c: CheckStackOverflow
    //     0x80df8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80df90: cmp             SP, x16
    //     0x80df94: b.ls            #0x80e0b8
    // 0x80df98: StoreField: r4->field_3f = r0
    //     0x80df98: stur            w0, [x4, #0x3f]
    // 0x80df9c: r1 = ""
    //     0x80df9c: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x80dfa0: r0 = StringExtension.obs()
    //     0x80dfa0: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x80dfa4: ldur            x2, [fp, #-8]
    // 0x80dfa8: StoreField: r2->field_43 = r0
    //     0x80dfa8: stur            w0, [x2, #0x43]
    //     0x80dfac: ldurb           w16, [x2, #-1]
    //     0x80dfb0: ldurb           w17, [x0, #-1]
    //     0x80dfb4: and             x16, x17, x16, lsr #2
    //     0x80dfb8: tst             x16, HEAP, lsr #32
    //     0x80dfbc: b.eq            #0x80dfc4
    //     0x80dfc0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80dfc4: r1 = true
    //     0x80dfc4: add             x1, NULL, #0x20  ; true
    // 0x80dfc8: r0 = BoolExtension.obs()
    //     0x80dfc8: bl              #0x80c8ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x80dfcc: ldur            x3, [fp, #-8]
    // 0x80dfd0: StoreField: r3->field_47 = r0
    //     0x80dfd0: stur            w0, [x3, #0x47]
    //     0x80dfd4: ldurb           w16, [x3, #-1]
    //     0x80dfd8: ldurb           w17, [x0, #-1]
    //     0x80dfdc: and             x16, x17, x16, lsr #2
    //     0x80dfe0: tst             x16, HEAP, lsr #32
    //     0x80dfe4: b.eq            #0x80dfec
    //     0x80dfe8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80dfec: r1 = <String>
    //     0x80dfec: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x80dff0: r2 = 0
    //     0x80dff0: movz            x2, #0
    // 0x80dff4: r0 = _GrowableList()
    //     0x80dff4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80dff8: r16 = <String>
    //     0x80dff8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x80dffc: stp             x0, x16, [SP]
    // 0x80e000: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80e000: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80e004: r0 = ListExtension.obs()
    //     0x80e004: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80e008: ldur            x3, [fp, #-8]
    // 0x80e00c: StoreField: r3->field_4b = r0
    //     0x80e00c: stur            w0, [x3, #0x4b]
    //     0x80e010: ldurb           w16, [x3, #-1]
    //     0x80e014: ldurb           w17, [x0, #-1]
    //     0x80e018: and             x16, x17, x16, lsr #2
    //     0x80e01c: tst             x16, HEAP, lsr #32
    //     0x80e020: b.eq            #0x80e028
    //     0x80e024: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80e028: r1 = <Tag>
    //     0x80e028: ldr             x1, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0x80e02c: r2 = 0
    //     0x80e02c: movz            x2, #0
    // 0x80e030: r0 = _GrowableList()
    //     0x80e030: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80e034: r16 = <Tag>
    //     0x80e034: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] TypeArguments: <Tag>
    // 0x80e038: stp             x0, x16, [SP]
    // 0x80e03c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80e03c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80e040: r0 = ListExtension.obs()
    //     0x80e040: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80e044: ldur            x1, [fp, #-8]
    // 0x80e048: StoreField: r1->field_4f = r0
    //     0x80e048: stur            w0, [x1, #0x4f]
    //     0x80e04c: ldurb           w16, [x1, #-1]
    //     0x80e050: ldurb           w17, [x0, #-1]
    //     0x80e054: and             x16, x17, x16, lsr #2
    //     0x80e058: tst             x16, HEAP, lsr #32
    //     0x80e05c: b.eq            #0x80e064
    //     0x80e060: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e064: ldur            x0, [fp, #-0x10]
    // 0x80e068: StoreField: r1->field_37 = r0
    //     0x80e068: stur            w0, [x1, #0x37]
    //     0x80e06c: ldurb           w16, [x1, #-1]
    //     0x80e070: ldurb           w17, [x0, #-1]
    //     0x80e074: and             x16, x17, x16, lsr #2
    //     0x80e078: tst             x16, HEAP, lsr #32
    //     0x80e07c: b.eq            #0x80e084
    //     0x80e080: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e084: ldur            x0, [fp, #-0x18]
    // 0x80e088: StoreField: r1->field_3b = r0
    //     0x80e088: stur            w0, [x1, #0x3b]
    //     0x80e08c: ldurb           w16, [x1, #-1]
    //     0x80e090: ldurb           w17, [x0, #-1]
    //     0x80e094: and             x16, x17, x16, lsr #2
    //     0x80e098: tst             x16, HEAP, lsr #32
    //     0x80e09c: b.eq            #0x80e0a4
    //     0x80e0a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e0a4: r0 = _ArticleAuthorController&GetxController&PagingMixin()
    //     0x80e0a4: bl              #0x80c3d4  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::_ArticleAuthorController&GetxController&PagingMixin
    // 0x80e0a8: r0 = Null
    //     0x80e0a8: mov             x0, NULL
    // 0x80e0ac: LeaveFrame
    //     0x80e0ac: mov             SP, fp
    //     0x80e0b0: ldp             fp, lr, [SP], #0x10
    // 0x80e0b4: ret
    //     0x80e0b4: ret             
    // 0x80e0b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e0b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e0bc: b               #0x80df98
  }
  [closure] List<Tag> <anonymous closure>(dynamic, List<Tag>, Pagination?) {
    // ** addr: 0x8a9e2c, size: 0x50
    // 0x8a9e2c: EnterFrame
    //     0x8a9e2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9e30: mov             fp, SP
    // 0x8a9e34: ldr             x0, [fp, #0x20]
    // 0x8a9e38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8a9e38: ldur            w1, [x0, #0x17]
    // 0x8a9e3c: DecompressPointer r1
    //     0x8a9e3c: add             x1, x1, HEAP, lsl #32
    // 0x8a9e40: CheckStackOverflow
    //     0x8a9e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9e44: cmp             SP, x16
    //     0x8a9e48: b.ls            #0x8a9e74
    // 0x8a9e4c: LoadField: r0 = r1->field_f
    //     0x8a9e4c: ldur            w0, [x1, #0xf]
    // 0x8a9e50: DecompressPointer r0
    //     0x8a9e50: add             x0, x0, HEAP, lsl #32
    // 0x8a9e54: LoadField: r1 = r0->field_4f
    //     0x8a9e54: ldur            w1, [x0, #0x4f]
    // 0x8a9e58: DecompressPointer r1
    //     0x8a9e58: add             x1, x1, HEAP, lsl #32
    // 0x8a9e5c: ldr             x2, [fp, #0x18]
    // 0x8a9e60: r0 = value=()
    //     0x8a9e60: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8a9e64: ldr             x0, [fp, #0x18]
    // 0x8a9e68: LeaveFrame
    //     0x8a9e68: mov             SP, fp
    //     0x8a9e6c: ldp             fp, lr, [SP], #0x10
    // 0x8a9e70: ret
    //     0x8a9e70: ret             
    // 0x8a9e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9e74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9e78: b               #0x8a9e4c
  }
  [closure] List<Tag>? <anonymous closure>(dynamic, ApiResult<List<Tag>>) {
    // ** addr: 0x8a9e7c, size: 0x98
    // 0x8a9e7c: EnterFrame
    //     0x8a9e7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9e80: mov             fp, SP
    // 0x8a9e84: AllocStack(0x28)
    //     0x8a9e84: sub             SP, SP, #0x28
    // 0x8a9e88: SetupParameters()
    //     0x8a9e88: ldr             x0, [fp, #0x18]
    //     0x8a9e8c: ldur            w3, [x0, #0x17]
    //     0x8a9e90: add             x3, x3, HEAP, lsl #32
    //     0x8a9e94: stur            x3, [fp, #-8]
    // 0x8a9e98: CheckStackOverflow
    //     0x8a9e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9e9c: cmp             SP, x16
    //     0x8a9ea0: b.ls            #0x8a9f0c
    // 0x8a9ea4: r1 = Function '<anonymous closure>':.
    //     0x8a9ea4: add             x1, PP, #0x40, lsl #12  ; [pp+0x409b8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x8a9ea8: ldr             x1, [x1, #0x9b8]
    // 0x8a9eac: r2 = Null
    //     0x8a9eac: mov             x2, NULL
    // 0x8a9eb0: r0 = AllocateClosure()
    //     0x8a9eb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a9eb4: ldur            x2, [fp, #-8]
    // 0x8a9eb8: r1 = Function '<anonymous closure>':.
    //     0x8a9eb8: add             x1, PP, #0x40, lsl #12  ; [pp+0x409c0] AnonymousClosure: (0x8a9e2c), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onReady (0x916180)
    //     0x8a9ebc: ldr             x1, [x1, #0x9c0]
    // 0x8a9ec0: stur            x0, [fp, #-8]
    // 0x8a9ec4: r0 = AllocateClosure()
    //     0x8a9ec4: bl              #0xec1630  ; AllocateClosureStub
    // 0x8a9ec8: mov             x1, x0
    // 0x8a9ecc: ldr             x0, [fp, #0x10]
    // 0x8a9ed0: r2 = LoadClassIdInstr(r0)
    //     0x8a9ed0: ldur            x2, [x0, #-1]
    //     0x8a9ed4: ubfx            x2, x2, #0xc, #0x14
    // 0x8a9ed8: r16 = <List<Tag>?>
    //     0x8a9ed8: add             x16, PP, #0x40, lsl #12  ; [pp+0x409b0] TypeArguments: <List<Tag>?>
    //     0x8a9edc: ldr             x16, [x16, #0x9b0]
    // 0x8a9ee0: stp             x0, x16, [SP, #0x10]
    // 0x8a9ee4: ldur            x16, [fp, #-8]
    // 0x8a9ee8: stp             x1, x16, [SP]
    // 0x8a9eec: mov             x0, x2
    // 0x8a9ef0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8a9ef0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8a9ef4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x8a9ef4: sub             lr, x0, #1, lsl #12
    //     0x8a9ef8: ldr             lr, [x21, lr, lsl #3]
    //     0x8a9efc: blr             lr
    // 0x8a9f00: LeaveFrame
    //     0x8a9f00: mov             SP, fp
    //     0x8a9f04: ldp             fp, lr, [SP], #0x10
    // 0x8a9f08: ret
    //     0x8a9f08: ret             
    // 0x8a9f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9f10: b               #0x8a9ea4
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8a9f14, size: 0x7c
    // 0x8a9f14: EnterFrame
    //     0x8a9f14: stp             fp, lr, [SP, #-0x10]!
    //     0x8a9f18: mov             fp, SP
    // 0x8a9f1c: AllocStack(0x10)
    //     0x8a9f1c: sub             SP, SP, #0x10
    // 0x8a9f20: SetupParameters(ArticleSearchController this /* r1 => r0, fp-0x8 */)
    //     0x8a9f20: mov             x0, x1
    //     0x8a9f24: stur            x1, [fp, #-8]
    // 0x8a9f28: CheckStackOverflow
    //     0x8a9f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a9f2c: cmp             SP, x16
    //     0x8a9f30: b.ls            #0x8a9f88
    // 0x8a9f34: mov             x1, x0
    // 0x8a9f38: r0 = onInit()
    //     0x8a9f38: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8a9f3c: r1 = <TextEditingValue>
    //     0x8a9f3c: ldr             x1, [PP, #0x6d78]  ; [pp+0x6d78] TypeArguments: <TextEditingValue>
    // 0x8a9f40: r0 = TextEditingController()
    //     0x8a9f40: bl              #0x8130fc  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x8a9f44: mov             x1, x0
    // 0x8a9f48: stur            x0, [fp, #-0x10]
    // 0x8a9f4c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a9f4c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a9f50: r0 = TextEditingController()
    //     0x8a9f50: bl              #0x812fec  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x8a9f54: ldur            x0, [fp, #-0x10]
    // 0x8a9f58: ldur            x1, [fp, #-8]
    // 0x8a9f5c: StoreField: r1->field_3f = r0
    //     0x8a9f5c: stur            w0, [x1, #0x3f]
    //     0x8a9f60: ldurb           w16, [x1, #-1]
    //     0x8a9f64: ldurb           w17, [x0, #-1]
    //     0x8a9f68: and             x16, x17, x16, lsr #2
    //     0x8a9f6c: tst             x16, HEAP, lsr #32
    //     0x8a9f70: b.eq            #0x8a9f78
    //     0x8a9f74: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8a9f78: r0 = Null
    //     0x8a9f78: mov             x0, NULL
    // 0x8a9f7c: LeaveFrame
    //     0x8a9f7c: mov             SP, fp
    //     0x8a9f80: ldp             fp, lr, [SP], #0x10
    // 0x8a9f84: ret
    //     0x8a9f84: ret             
    // 0x8a9f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a9f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a9f8c: b               #0x8a9f34
  }
  _ onReady(/* No info */) {
    // ** addr: 0x916180, size: 0x124
    // 0x916180: EnterFrame
    //     0x916180: stp             fp, lr, [SP, #-0x10]!
    //     0x916184: mov             fp, SP
    // 0x916188: AllocStack(0x38)
    //     0x916188: sub             SP, SP, #0x38
    // 0x91618c: SetupParameters(ArticleSearchController this /* r1 => r1, fp-0x8 */)
    //     0x91618c: stur            x1, [fp, #-8]
    // 0x916190: CheckStackOverflow
    //     0x916190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x916194: cmp             SP, x16
    //     0x916198: b.ls            #0x916294
    // 0x91619c: r1 = 1
    //     0x91619c: movz            x1, #0x1
    // 0x9161a0: r0 = AllocateContext()
    //     0x9161a0: bl              #0xec126c  ; AllocateContextStub
    // 0x9161a4: mov             x2, x0
    // 0x9161a8: ldur            x0, [fp, #-8]
    // 0x9161ac: stur            x2, [fp, #-0x10]
    // 0x9161b0: StoreField: r2->field_f = r0
    //     0x9161b0: stur            w0, [x2, #0xf]
    // 0x9161b4: mov             x1, x0
    // 0x9161b8: r0 = onReady()
    //     0x9161b8: bl              #0x925524  ; [package:nuonline/app/modules/video/video_list/controllers/video_list_builder_controller.dart] _VideoListBuilderController&GetxController&PagingMixin::onReady
    // 0x9161bc: ldur            x0, [fp, #-8]
    // 0x9161c0: LoadField: r1 = r0->field_4b
    //     0x9161c0: ldur            w1, [x0, #0x4b]
    // 0x9161c4: DecompressPointer r1
    //     0x9161c4: add             x1, x1, HEAP, lsl #32
    // 0x9161c8: stur            x1, [fp, #-0x18]
    // 0x9161cc: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x9161cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9161d0: ldr             x0, [x0, #0x2728]
    //     0x9161d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9161d8: cmp             w0, w16
    //     0x9161dc: b.ne            #0x9161e8
    //     0x9161e0: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x9161e4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9161e8: r16 = <String>
    //     0x9161e8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x9161ec: stp             x0, x16, [SP, #8]
    // 0x9161f0: r16 = "v2_search_history"
    //     0x9161f0: ldr             x16, [PP, #0x7bf8]  ; [pp+0x7bf8] "v2_search_history"
    // 0x9161f4: str             x16, [SP]
    // 0x9161f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9161f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9161fc: r0 = box()
    //     0x9161fc: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x916200: mov             x1, x0
    // 0x916204: stur            x0, [fp, #-0x20]
    // 0x916208: r0 = checkOpen()
    //     0x916208: bl              #0x68f838  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::checkOpen
    // 0x91620c: ldur            x0, [fp, #-0x20]
    // 0x916210: LoadField: r1 = r0->field_1b
    //     0x916210: ldur            w1, [x0, #0x1b]
    // 0x916214: DecompressPointer r1
    //     0x916214: add             x1, x1, HEAP, lsl #32
    // 0x916218: r16 = Sentinel
    //     0x916218: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91621c: cmp             w1, w16
    // 0x916220: b.eq            #0x91629c
    // 0x916224: r0 = getValues()
    //     0x916224: bl              #0x7bcd04  ; [package:hive/src/box/keystore.dart] Keystore::getValues
    // 0x916228: LoadField: r1 = r0->field_7
    //     0x916228: ldur            w1, [x0, #7]
    // 0x91622c: DecompressPointer r1
    //     0x91622c: add             x1, x1, HEAP, lsl #32
    // 0x916230: mov             x2, x0
    // 0x916234: r0 = _GrowableList.of()
    //     0x916234: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x916238: ldur            x1, [fp, #-0x18]
    // 0x91623c: mov             x2, x0
    // 0x916240: r0 = value=()
    //     0x916240: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x916244: ldur            x0, [fp, #-8]
    // 0x916248: LoadField: r1 = r0->field_37
    //     0x916248: ldur            w1, [x0, #0x37]
    // 0x91624c: DecompressPointer r1
    //     0x91624c: add             x1, x1, HEAP, lsl #32
    // 0x916250: r0 = findAllTag()
    //     0x916250: bl              #0x8a9914  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAllTag
    // 0x916254: ldur            x2, [fp, #-0x10]
    // 0x916258: r1 = Function '<anonymous closure>':.
    //     0x916258: add             x1, PP, #0x40, lsl #12  ; [pp+0x409a8] AnonymousClosure: (0x8a9e7c), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onReady (0x916180)
    //     0x91625c: ldr             x1, [x1, #0x9a8]
    // 0x916260: stur            x0, [fp, #-8]
    // 0x916264: r0 = AllocateClosure()
    //     0x916264: bl              #0xec1630  ; AllocateClosureStub
    // 0x916268: r16 = <List<Tag>?>
    //     0x916268: add             x16, PP, #0x40, lsl #12  ; [pp+0x409b0] TypeArguments: <List<Tag>?>
    //     0x91626c: ldr             x16, [x16, #0x9b0]
    // 0x916270: ldur            lr, [fp, #-8]
    // 0x916274: stp             lr, x16, [SP, #8]
    // 0x916278: str             x0, [SP]
    // 0x91627c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91627c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x916280: r0 = then()
    //     0x916280: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x916284: r0 = Null
    //     0x916284: mov             x0, NULL
    // 0x916288: LeaveFrame
    //     0x916288: mov             SP, fp
    //     0x91628c: ldp             fp, lr, [SP], #0x10
    // 0x916290: ret
    //     0x916290: ret             
    // 0x916294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916294: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916298: b               #0x91619c
    // 0x91629c: r9 = keystore
    //     0x91629c: ldr             x9, [PP, #0x7cb8]  ; [pp+0x7cb8] Field <BoxBaseImpl.keystore>: late (offset: 0x1c)
    // 0x9162a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9162a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ onClose(/* No info */) async {
    // ** addr: 0x9266c0, size: 0xb0
    // 0x9266c0: EnterFrame
    //     0x9266c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9266c4: mov             fp, SP
    // 0x9266c8: AllocStack(0x30)
    //     0x9266c8: sub             SP, SP, #0x30
    // 0x9266cc: SetupParameters(ArticleSearchController this /* r1 => r1, fp-0x10 */)
    //     0x9266cc: stur            NULL, [fp, #-8]
    //     0x9266d0: stur            x1, [fp, #-0x10]
    // 0x9266d4: CheckStackOverflow
    //     0x9266d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9266d8: cmp             SP, x16
    //     0x9266dc: b.ls            #0x926768
    // 0x9266e0: InitAsync() -> Future<void?>
    //     0x9266e0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x9266e4: bl              #0x661298  ; InitAsyncStub
    // 0x9266e8: ldur            x0, [fp, #-0x10]
    // 0x9266ec: LoadField: r1 = r0->field_3b
    //     0x9266ec: ldur            w1, [x0, #0x3b]
    // 0x9266f0: DecompressPointer r1
    //     0x9266f0: add             x1, x1, HEAP, lsl #32
    // 0x9266f4: r0 = searchHistory()
    //     0x9266f4: bl              #0x9162a4  ; [package:nuonline/services/storage_service/content_storage.dart] ContentStorage::searchHistory
    // 0x9266f8: mov             x1, x0
    // 0x9266fc: r0 = clear()
    //     0x9266fc: bl              #0x8c0db0  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::clear
    // 0x926700: mov             x1, x0
    // 0x926704: stur            x1, [fp, #-0x18]
    // 0x926708: r0 = Await()
    //     0x926708: bl              #0x661044  ; AwaitStub
    // 0x92670c: r0 = InitLateStaticField(0x1394) // [package:hive/hive.dart] ::Hive
    //     0x92670c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x926710: ldr             x0, [x0, #0x2728]
    //     0x926714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x926718: cmp             w0, w16
    //     0x92671c: b.ne            #0x926728
    //     0x926720: ldr             x2, [PP, #0x80]  ; [pp+0x80] Field <::.Hive>: static late final (offset: 0x1394)
    //     0x926724: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x926728: r16 = <String>
    //     0x926728: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x92672c: stp             x0, x16, [SP, #8]
    // 0x926730: r16 = "v2_search_history"
    //     0x926730: ldr             x16, [PP, #0x7bf8]  ; [pp+0x7bf8] "v2_search_history"
    // 0x926734: str             x16, [SP]
    // 0x926738: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x926738: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x92673c: r0 = box()
    //     0x92673c: bl              #0x7bcef0  ; [package:hive/src/hive_impl.dart] HiveImpl::box
    // 0x926740: mov             x1, x0
    // 0x926744: ldur            x0, [fp, #-0x10]
    // 0x926748: LoadField: r2 = r0->field_4b
    //     0x926748: ldur            w2, [x0, #0x4b]
    // 0x92674c: DecompressPointer r2
    //     0x92674c: add             x2, x2, HEAP, lsl #32
    // 0x926750: r0 = addAll()
    //     0x926750: bl              #0x8c0b60  ; [package:hive/src/box/box_base_impl.dart] BoxBaseImpl::addAll
    // 0x926754: mov             x1, x0
    // 0x926758: stur            x1, [fp, #-0x18]
    // 0x92675c: r0 = Await()
    //     0x92675c: bl              #0x661044  ; AwaitStub
    // 0x926760: r0 = Null
    //     0x926760: mov             x0, NULL
    // 0x926764: r0 = ReturnAsyncNotFuture()
    //     0x926764: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x926768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926768: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92676c: b               #0x9266e0
  }
  _ search(/* No info */) {
    // ** addr: 0xad1dac, size: 0x88
    // 0xad1dac: EnterFrame
    //     0xad1dac: stp             fp, lr, [SP, #-0x10]!
    //     0xad1db0: mov             fp, SP
    // 0xad1db4: AllocStack(0x10)
    //     0xad1db4: sub             SP, SP, #0x10
    // 0xad1db8: SetupParameters(ArticleSearchController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xad1db8: mov             x3, x1
    //     0xad1dbc: mov             x0, x2
    //     0xad1dc0: stur            x1, [fp, #-8]
    //     0xad1dc4: stur            x2, [fp, #-0x10]
    // 0xad1dc8: CheckStackOverflow
    //     0xad1dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1dcc: cmp             SP, x16
    //     0xad1dd0: b.ls            #0xad1e20
    // 0xad1dd4: LoadField: r1 = r3->field_3f
    //     0xad1dd4: ldur            w1, [x3, #0x3f]
    // 0xad1dd8: DecompressPointer r1
    //     0xad1dd8: add             x1, x1, HEAP, lsl #32
    // 0xad1ddc: r16 = Sentinel
    //     0xad1ddc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad1de0: cmp             w1, w16
    // 0xad1de4: b.eq            #0xad1e28
    // 0xad1de8: mov             x2, x0
    // 0xad1dec: r0 = text=()
    //     0xad1dec: bl              #0x8c2780  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xad1df0: ldur            x0, [fp, #-8]
    // 0xad1df4: LoadField: r1 = r0->field_43
    //     0xad1df4: ldur            w1, [x0, #0x43]
    // 0xad1df8: DecompressPointer r1
    //     0xad1df8: add             x1, x1, HEAP, lsl #32
    // 0xad1dfc: ldur            x2, [fp, #-0x10]
    // 0xad1e00: r0 = value=()
    //     0xad1e00: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad1e04: ldur            x1, [fp, #-8]
    // 0xad1e08: ldur            x2, [fp, #-0x10]
    // 0xad1e0c: r0 = onSubmitted()
    //     0xad1e0c: bl              #0xad1e34  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onSubmitted
    // 0xad1e10: r0 = Null
    //     0xad1e10: mov             x0, NULL
    // 0xad1e14: LeaveFrame
    //     0xad1e14: mov             SP, fp
    //     0xad1e18: ldp             fp, lr, [SP], #0x10
    // 0xad1e1c: ret
    //     0xad1e1c: ret             
    // 0xad1e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1e24: b               #0xad1dd4
    // 0xad1e28: r9 = textController
    //     0xad1e28: add             x9, PP, #0x30, lsl #12  ; [pp+0x30980] Field <ArticleSearchController.textController>: late (offset: 0x40)
    //     0xad1e2c: ldr             x9, [x9, #0x980]
    // 0xad1e30: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad1e30: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ onSubmitted(/* No info */) {
    // ** addr: 0xad1e34, size: 0x15c
    // 0xad1e34: EnterFrame
    //     0xad1e34: stp             fp, lr, [SP, #-0x10]!
    //     0xad1e38: mov             fp, SP
    // 0xad1e3c: AllocStack(0x20)
    //     0xad1e3c: sub             SP, SP, #0x20
    // 0xad1e40: SetupParameters(ArticleSearchController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xad1e40: stur            x1, [fp, #-8]
    //     0xad1e44: stur            x2, [fp, #-0x10]
    // 0xad1e48: CheckStackOverflow
    //     0xad1e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1e4c: cmp             SP, x16
    //     0xad1e50: b.ls            #0xad1f88
    // 0xad1e54: r1 = 1
    //     0xad1e54: movz            x1, #0x1
    // 0xad1e58: r0 = AllocateContext()
    //     0xad1e58: bl              #0xec126c  ; AllocateContextStub
    // 0xad1e5c: mov             x2, x0
    // 0xad1e60: ldur            x0, [fp, #-0x10]
    // 0xad1e64: stur            x2, [fp, #-0x18]
    // 0xad1e68: StoreField: r2->field_f = r0
    //     0xad1e68: stur            w0, [x2, #0xf]
    // 0xad1e6c: LoadField: r1 = r0->field_7
    //     0xad1e6c: ldur            w1, [x0, #7]
    // 0xad1e70: cbnz            w1, #0xad1e84
    // 0xad1e74: r0 = Null
    //     0xad1e74: mov             x0, NULL
    // 0xad1e78: LeaveFrame
    //     0xad1e78: mov             SP, fp
    //     0xad1e7c: ldp             fp, lr, [SP], #0x10
    // 0xad1e80: ret
    //     0xad1e80: ret             
    // 0xad1e84: ldur            x0, [fp, #-8]
    // 0xad1e88: LoadField: r3 = r0->field_47
    //     0xad1e88: ldur            w3, [x0, #0x47]
    // 0xad1e8c: DecompressPointer r3
    //     0xad1e8c: add             x3, x3, HEAP, lsl #32
    // 0xad1e90: mov             x1, x3
    // 0xad1e94: stur            x3, [fp, #-0x10]
    // 0xad1e98: r0 = value()
    //     0xad1e98: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad1e9c: tbnz            w0, #4, #0xad1ea8
    // 0xad1ea0: ldur            x1, [fp, #-0x10]
    // 0xad1ea4: r0 = RxBoolExt.toggle()
    //     0xad1ea4: bl              #0xa428f0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxBoolExt.toggle
    // 0xad1ea8: ldur            x3, [fp, #-8]
    // 0xad1eac: ldur            x0, [fp, #-0x18]
    // 0xad1eb0: mov             x2, x0
    // 0xad1eb4: r1 = Function '<anonymous closure>':.
    //     0xad1eb4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30988] AnonymousClosure: (0xad2368), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onSubmitted (0xad1e34)
    //     0xad1eb8: ldr             x1, [x1, #0x988]
    // 0xad1ebc: r0 = AllocateClosure()
    //     0xad1ebc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1ec0: ldur            x1, [fp, #-8]
    // 0xad1ec4: mov             x2, x0
    // 0xad1ec8: r0 = withAnalytic()
    //     0xad1ec8: bl              #0x8abac8  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin::withAnalytic
    // 0xad1ecc: ldur            x0, [fp, #-8]
    // 0xad1ed0: LoadField: r3 = r0->field_4b
    //     0xad1ed0: ldur            w3, [x0, #0x4b]
    // 0xad1ed4: DecompressPointer r3
    //     0xad1ed4: add             x3, x3, HEAP, lsl #32
    // 0xad1ed8: ldur            x2, [fp, #-0x18]
    // 0xad1edc: stur            x3, [fp, #-0x10]
    // 0xad1ee0: r1 = Function '<anonymous closure>':.
    //     0xad1ee0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30990] AnonymousClosure: (0xad20cc), in [package:nuonline/app/modules/video/video_search/controllers/video_search_controller.dart] VideoSearchController::onSubmitted (0xad212c)
    //     0xad1ee4: ldr             x1, [x1, #0x990]
    // 0xad1ee8: r0 = AllocateClosure()
    //     0xad1ee8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1eec: ldur            x1, [fp, #-0x10]
    // 0xad1ef0: mov             x2, x0
    // 0xad1ef4: r0 = removeWhere()
    //     0xad1ef4: bl              #0x66afbc  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::removeWhere
    // 0xad1ef8: ldur            x0, [fp, #-0x18]
    // 0xad1efc: LoadField: r3 = r0->field_f
    //     0xad1efc: ldur            w3, [x0, #0xf]
    // 0xad1f00: DecompressPointer r3
    //     0xad1f00: add             x3, x3, HEAP, lsl #32
    // 0xad1f04: ldur            x1, [fp, #-0x10]
    // 0xad1f08: r2 = 0
    //     0xad1f08: movz            x2, #0
    // 0xad1f0c: r0 = insert()
    //     0xad1f0c: bl              #0x66a024  ; [dart:collection] ListBase::insert
    // 0xad1f10: ldur            x1, [fp, #-0x10]
    // 0xad1f14: r0 = value()
    //     0xad1f14: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad1f18: r1 = LoadClassIdInstr(r0)
    //     0xad1f18: ldur            x1, [x0, #-1]
    //     0xad1f1c: ubfx            x1, x1, #0xc, #0x14
    // 0xad1f20: str             x0, [SP]
    // 0xad1f24: mov             x0, x1
    // 0xad1f28: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad1f28: movz            x17, #0xc834
    //     0xad1f2c: add             lr, x0, x17
    //     0xad1f30: ldr             lr, [x21, lr, lsl #3]
    //     0xad1f34: blr             lr
    // 0xad1f38: r1 = LoadInt32Instr(r0)
    //     0xad1f38: sbfx            x1, x0, #1, #0x1f
    //     0xad1f3c: tbz             w0, #0, #0xad1f44
    //     0xad1f40: ldur            x1, [x0, #7]
    // 0xad1f44: cmp             x1, #5
    // 0xad1f48: b.le            #0xad1f70
    // 0xad1f4c: ldur            x1, [fp, #-0x10]
    // 0xad1f50: r2 = 5
    //     0xad1f50: movz            x2, #0x5
    // 0xad1f54: r0 = take()
    //     0xad1f54: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0xad1f58: mov             x1, x0
    // 0xad1f5c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xad1f5c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xad1f60: r0 = toList()
    //     0xad1f60: bl              #0x8633fc  ; [dart:_internal] SubListIterable::toList
    // 0xad1f64: ldur            x1, [fp, #-0x10]
    // 0xad1f68: mov             x2, x0
    // 0xad1f6c: r0 = value=()
    //     0xad1f6c: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0xad1f70: ldur            x1, [fp, #-8]
    // 0xad1f74: r0 = onPageRefresh()
    //     0xad1f74: bl              #0xad1fcc  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh
    // 0xad1f78: r0 = Null
    //     0xad1f78: mov             x0, NULL
    // 0xad1f7c: LeaveFrame
    //     0xad1f7c: mov             SP, fp
    //     0xad1f80: ldp             fp, lr, [SP], #0x10
    // 0xad1f84: ret
    //     0xad1f84: ret             
    // 0xad1f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1f8c: b               #0xad1e54
  }
  [closure] void onSubmitted(dynamic, String) {
    // ** addr: 0xad1f90, size: 0x3c
    // 0xad1f90: EnterFrame
    //     0xad1f90: stp             fp, lr, [SP, #-0x10]!
    //     0xad1f94: mov             fp, SP
    // 0xad1f98: ldr             x0, [fp, #0x18]
    // 0xad1f9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad1f9c: ldur            w1, [x0, #0x17]
    // 0xad1fa0: DecompressPointer r1
    //     0xad1fa0: add             x1, x1, HEAP, lsl #32
    // 0xad1fa4: CheckStackOverflow
    //     0xad1fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1fa8: cmp             SP, x16
    //     0xad1fac: b.ls            #0xad1fc4
    // 0xad1fb0: ldr             x2, [fp, #0x10]
    // 0xad1fb4: r0 = onSubmitted()
    //     0xad1fb4: bl              #0xad1e34  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onSubmitted
    // 0xad1fb8: LeaveFrame
    //     0xad1fb8: mov             SP, fp
    //     0xad1fbc: ldp             fp, lr, [SP], #0x10
    // 0xad1fc0: ret
    //     0xad1fc0: ret             
    // 0xad1fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1fc8: b               #0xad1fb0
  }
  [closure] void <anonymous closure>(dynamic, AnalyticService) {
    // ** addr: 0xad2368, size: 0x48
    // 0xad2368: EnterFrame
    //     0xad2368: stp             fp, lr, [SP, #-0x10]!
    //     0xad236c: mov             fp, SP
    // 0xad2370: ldr             x0, [fp, #0x18]
    // 0xad2374: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad2374: ldur            w1, [x0, #0x17]
    // 0xad2378: DecompressPointer r1
    //     0xad2378: add             x1, x1, HEAP, lsl #32
    // 0xad237c: CheckStackOverflow
    //     0xad237c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2380: cmp             SP, x16
    //     0xad2384: b.ls            #0xad23a8
    // 0xad2388: LoadField: r2 = r1->field_f
    //     0xad2388: ldur            w2, [x1, #0xf]
    // 0xad238c: DecompressPointer r2
    //     0xad238c: add             x2, x2, HEAP, lsl #32
    // 0xad2390: ldr             x1, [fp, #0x10]
    // 0xad2394: r0 = sendArticleSearchLog()
    //     0xad2394: bl              #0xad23b0  ; [package:nuonline/services/analytic_service.dart] AnalyticService::sendArticleSearchLog
    // 0xad2398: r0 = Null
    //     0xad2398: mov             x0, NULL
    // 0xad239c: LeaveFrame
    //     0xad239c: mov             SP, fp
    //     0xad23a0: ldp             fp, lr, [SP], #0x10
    // 0xad23a4: ret
    //     0xad23a4: ret             
    // 0xad23a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad23a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad23ac: b               #0xad2388
  }
  _ deleteHistory(/* No info */) {
    // ** addr: 0xad2cf0, size: 0x3c
    // 0xad2cf0: EnterFrame
    //     0xad2cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xad2cf4: mov             fp, SP
    // 0xad2cf8: CheckStackOverflow
    //     0xad2cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2cfc: cmp             SP, x16
    //     0xad2d00: b.ls            #0xad2d24
    // 0xad2d04: LoadField: r0 = r1->field_4b
    //     0xad2d04: ldur            w0, [x1, #0x4b]
    // 0xad2d08: DecompressPointer r0
    //     0xad2d08: add             x0, x0, HEAP, lsl #32
    // 0xad2d0c: mov             x1, x0
    // 0xad2d10: r0 = removeAt()
    //     0xad2d10: bl              #0xa8da2c  ; [dart:collection] ListBase::removeAt
    // 0xad2d14: r0 = Null
    //     0xad2d14: mov             x0, NULL
    // 0xad2d18: LeaveFrame
    //     0xad2d18: mov             SP, fp
    //     0xad2d1c: ldp             fp, lr, [SP], #0x10
    // 0xad2d20: ret
    //     0xad2d20: ret             
    // 0xad2d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2d24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2d28: b               #0xad2d04
  }
  [closure] void deleteAllHistory(dynamic) {
    // ** addr: 0xad2e04, size: 0x38
    // 0xad2e04: EnterFrame
    //     0xad2e04: stp             fp, lr, [SP, #-0x10]!
    //     0xad2e08: mov             fp, SP
    // 0xad2e0c: ldr             x0, [fp, #0x10]
    // 0xad2e10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad2e10: ldur            w1, [x0, #0x17]
    // 0xad2e14: DecompressPointer r1
    //     0xad2e14: add             x1, x1, HEAP, lsl #32
    // 0xad2e18: CheckStackOverflow
    //     0xad2e18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2e1c: cmp             SP, x16
    //     0xad2e20: b.ls            #0xad2e34
    // 0xad2e24: r0 = deleteAllHistory()
    //     0xad2e24: bl              #0xad2e3c  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::deleteAllHistory
    // 0xad2e28: LeaveFrame
    //     0xad2e28: mov             SP, fp
    //     0xad2e2c: ldp             fp, lr, [SP], #0x10
    // 0xad2e30: ret
    //     0xad2e30: ret             
    // 0xad2e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2e38: b               #0xad2e24
  }
  _ deleteAllHistory(/* No info */) {
    // ** addr: 0xad2e3c, size: 0x68
    // 0xad2e3c: EnterFrame
    //     0xad2e3c: stp             fp, lr, [SP, #-0x10]!
    //     0xad2e40: mov             fp, SP
    // 0xad2e44: AllocStack(0x8)
    //     0xad2e44: sub             SP, SP, #8
    // 0xad2e48: CheckStackOverflow
    //     0xad2e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2e4c: cmp             SP, x16
    //     0xad2e50: b.ls            #0xad2e9c
    // 0xad2e54: LoadField: r0 = r1->field_4b
    //     0xad2e54: ldur            w0, [x1, #0x4b]
    // 0xad2e58: DecompressPointer r0
    //     0xad2e58: add             x0, x0, HEAP, lsl #32
    // 0xad2e5c: mov             x1, x0
    // 0xad2e60: r0 = clear()
    //     0xad2e60: bl              #0xbd9edc  ; [dart:collection] ListBase::clear
    // 0xad2e64: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad2e64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad2e68: ldr             x0, [x0, #0x2670]
    //     0xad2e6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad2e70: cmp             w0, w16
    //     0xad2e74: b.ne            #0xad2e80
    //     0xad2e78: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad2e7c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad2e80: str             NULL, [SP]
    // 0xad2e84: r4 = const [0x1, 0, 0, 0, null]
    //     0xad2e84: ldr             x4, [PP, #0x60]  ; [pp+0x60] List(5) [0x1, 0, 0, 0, Null]
    // 0xad2e88: r0 = GetNavigation.back()
    //     0xad2e88: bl              #0x63e02c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xad2e8c: r0 = Null
    //     0xad2e8c: mov             x0, NULL
    // 0xad2e90: LeaveFrame
    //     0xad2e90: mov             SP, fp
    //     0xad2e94: ldp             fp, lr, [SP], #0x10
    // 0xad2e98: ret
    //     0xad2e98: ret             
    // 0xad2e9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2e9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2ea0: b               #0xad2e54
  }
  [closure] void onCloseTap(dynamic) {
    // ** addr: 0xad330c, size: 0x38
    // 0xad330c: EnterFrame
    //     0xad330c: stp             fp, lr, [SP, #-0x10]!
    //     0xad3310: mov             fp, SP
    // 0xad3314: ldr             x0, [fp, #0x10]
    // 0xad3318: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad3318: ldur            w1, [x0, #0x17]
    // 0xad331c: DecompressPointer r1
    //     0xad331c: add             x1, x1, HEAP, lsl #32
    // 0xad3320: CheckStackOverflow
    //     0xad3320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3324: cmp             SP, x16
    //     0xad3328: b.ls            #0xad333c
    // 0xad332c: r0 = onCloseTap()
    //     0xad332c: bl              #0xad3344  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onCloseTap
    // 0xad3330: LeaveFrame
    //     0xad3330: mov             SP, fp
    //     0xad3334: ldp             fp, lr, [SP], #0x10
    // 0xad3338: ret
    //     0xad3338: ret             
    // 0xad333c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad333c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3340: b               #0xad332c
  }
  _ onCloseTap(/* No info */) {
    // ** addr: 0xad3344, size: 0x40
    // 0xad3344: EnterFrame
    //     0xad3344: stp             fp, lr, [SP, #-0x10]!
    //     0xad3348: mov             fp, SP
    // 0xad334c: CheckStackOverflow
    //     0xad334c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3350: cmp             SP, x16
    //     0xad3354: b.ls            #0xad337c
    // 0xad3358: LoadField: r0 = r1->field_43
    //     0xad3358: ldur            w0, [x1, #0x43]
    // 0xad335c: DecompressPointer r0
    //     0xad335c: add             x0, x0, HEAP, lsl #32
    // 0xad3360: mov             x1, x0
    // 0xad3364: r2 = ""
    //     0xad3364: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xad3368: r0 = value=()
    //     0xad3368: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad336c: r0 = ""
    //     0xad336c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xad3370: LeaveFrame
    //     0xad3370: mov             SP, fp
    //     0xad3374: ldp             fp, lr, [SP], #0x10
    // 0xad3378: ret
    //     0xad3378: ret             
    // 0xad337c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad337c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3380: b               #0xad3358
  }
  [closure] void onChanged(dynamic, String) {
    // ** addr: 0xad3384, size: 0x3c
    // 0xad3384: EnterFrame
    //     0xad3384: stp             fp, lr, [SP, #-0x10]!
    //     0xad3388: mov             fp, SP
    // 0xad338c: ldr             x0, [fp, #0x18]
    // 0xad3390: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad3390: ldur            w1, [x0, #0x17]
    // 0xad3394: DecompressPointer r1
    //     0xad3394: add             x1, x1, HEAP, lsl #32
    // 0xad3398: CheckStackOverflow
    //     0xad3398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad339c: cmp             SP, x16
    //     0xad33a0: b.ls            #0xad33b8
    // 0xad33a4: ldr             x2, [fp, #0x10]
    // 0xad33a8: r0 = onChanged()
    //     0xad33a8: bl              #0xad33c0  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onChanged
    // 0xad33ac: LeaveFrame
    //     0xad33ac: mov             SP, fp
    //     0xad33b0: ldp             fp, lr, [SP], #0x10
    // 0xad33b4: ret
    //     0xad33b4: ret             
    // 0xad33b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad33b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad33bc: b               #0xad33a4
  }
  _ onChanged(/* No info */) {
    // ** addr: 0xad33c0, size: 0x4c
    // 0xad33c0: EnterFrame
    //     0xad33c0: stp             fp, lr, [SP, #-0x10]!
    //     0xad33c4: mov             fp, SP
    // 0xad33c8: AllocStack(0x8)
    //     0xad33c8: sub             SP, SP, #8
    // 0xad33cc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xad33cc: mov             x0, x2
    //     0xad33d0: stur            x2, [fp, #-8]
    // 0xad33d4: CheckStackOverflow
    //     0xad33d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad33d8: cmp             SP, x16
    //     0xad33dc: b.ls            #0xad3404
    // 0xad33e0: LoadField: r2 = r1->field_43
    //     0xad33e0: ldur            w2, [x1, #0x43]
    // 0xad33e4: DecompressPointer r2
    //     0xad33e4: add             x2, x2, HEAP, lsl #32
    // 0xad33e8: mov             x1, x2
    // 0xad33ec: mov             x2, x0
    // 0xad33f0: r0 = value=()
    //     0xad33f0: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad33f4: ldur            x0, [fp, #-8]
    // 0xad33f8: LeaveFrame
    //     0xad33f8: mov             SP, fp
    //     0xad33fc: ldp             fp, lr, [SP], #0x10
    // 0xad3400: ret
    //     0xad3400: ret             
    // 0xad3404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3404: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3408: b               #0xad33e0
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe32cc0, size: 0x1ac
    // 0xe32cc0: EnterFrame
    //     0xe32cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xe32cc4: mov             fp, SP
    // 0xe32cc8: AllocStack(0x48)
    //     0xe32cc8: sub             SP, SP, #0x48
    // 0xe32ccc: SetupParameters(ArticleSearchController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe32ccc: stur            x1, [fp, #-8]
    //     0xe32cd0: stur            x2, [fp, #-0x10]
    // 0xe32cd4: CheckStackOverflow
    //     0xe32cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32cd8: cmp             SP, x16
    //     0xe32cdc: b.ls            #0xe32e64
    // 0xe32ce0: r1 = 1
    //     0xe32ce0: movz            x1, #0x1
    // 0xe32ce4: r0 = AllocateContext()
    //     0xe32ce4: bl              #0xec126c  ; AllocateContextStub
    // 0xe32ce8: mov             x2, x0
    // 0xe32cec: ldur            x0, [fp, #-8]
    // 0xe32cf0: stur            x2, [fp, #-0x20]
    // 0xe32cf4: StoreField: r2->field_f = r0
    //     0xe32cf4: stur            w0, [x2, #0xf]
    // 0xe32cf8: LoadField: r3 = r0->field_43
    //     0xe32cf8: ldur            w3, [x0, #0x43]
    // 0xe32cfc: DecompressPointer r3
    //     0xe32cfc: add             x3, x3, HEAP, lsl #32
    // 0xe32d00: mov             x1, x3
    // 0xe32d04: stur            x3, [fp, #-0x18]
    // 0xe32d08: r0 = RxStringExt.isEmpty()
    //     0xe32d08: bl              #0x615438  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isEmpty
    // 0xe32d0c: tbnz            w0, #4, #0xe32d20
    // 0xe32d10: r0 = Null
    //     0xe32d10: mov             x0, NULL
    // 0xe32d14: LeaveFrame
    //     0xe32d14: mov             SP, fp
    //     0xe32d18: ldp             fp, lr, [SP], #0x10
    // 0xe32d1c: ret
    //     0xe32d1c: ret             
    // 0xe32d20: ldur            x0, [fp, #-8]
    // 0xe32d24: ldur            x3, [fp, #-0x10]
    // 0xe32d28: LoadField: r4 = r0->field_37
    //     0xe32d28: ldur            w4, [x0, #0x37]
    // 0xe32d2c: DecompressPointer r4
    //     0xe32d2c: add             x4, x4, HEAP, lsl #32
    // 0xe32d30: stur            x4, [fp, #-0x28]
    // 0xe32d34: r1 = Null
    //     0xe32d34: mov             x1, NULL
    // 0xe32d38: r2 = 8
    //     0xe32d38: movz            x2, #0x8
    // 0xe32d3c: r0 = AllocateArray()
    //     0xe32d3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe32d40: stur            x0, [fp, #-0x30]
    // 0xe32d44: r16 = "q"
    //     0xe32d44: add             x16, PP, #0x29, lsl #12  ; [pp+0x291d0] "q"
    //     0xe32d48: ldr             x16, [x16, #0x1d0]
    // 0xe32d4c: StoreField: r0->field_f = r16
    //     0xe32d4c: stur            w16, [x0, #0xf]
    // 0xe32d50: ldur            x1, [fp, #-0x18]
    // 0xe32d54: r0 = value()
    //     0xe32d54: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xe32d58: ldur            x1, [fp, #-0x30]
    // 0xe32d5c: ArrayStore: r1[1] = r0  ; List_4
    //     0xe32d5c: add             x25, x1, #0x13
    //     0xe32d60: str             w0, [x25]
    //     0xe32d64: tbz             w0, #0, #0xe32d80
    //     0xe32d68: ldurb           w16, [x1, #-1]
    //     0xe32d6c: ldurb           w17, [x0, #-1]
    //     0xe32d70: and             x16, x17, x16, lsr #2
    //     0xe32d74: tst             x16, HEAP, lsr #32
    //     0xe32d78: b.eq            #0xe32d80
    //     0xe32d7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe32d80: ldur            x2, [fp, #-0x30]
    // 0xe32d84: r16 = "page"
    //     0xe32d84: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe32d88: ldr             x16, [x16, #0x300]
    // 0xe32d8c: ArrayStore: r2[0] = r16  ; List_4
    //     0xe32d8c: stur            w16, [x2, #0x17]
    // 0xe32d90: ldur            x3, [fp, #-0x10]
    // 0xe32d94: r0 = BoxInt64Instr(r3)
    //     0xe32d94: sbfiz           x0, x3, #1, #0x1f
    //     0xe32d98: cmp             x3, x0, asr #1
    //     0xe32d9c: b.eq            #0xe32da8
    //     0xe32da0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe32da4: stur            x3, [x0, #7]
    // 0xe32da8: r1 = 60
    //     0xe32da8: movz            x1, #0x3c
    // 0xe32dac: branchIfSmi(r0, 0xe32db8)
    //     0xe32dac: tbz             w0, #0, #0xe32db8
    // 0xe32db0: r1 = LoadClassIdInstr(r0)
    //     0xe32db0: ldur            x1, [x0, #-1]
    //     0xe32db4: ubfx            x1, x1, #0xc, #0x14
    // 0xe32db8: str             x0, [SP]
    // 0xe32dbc: mov             x0, x1
    // 0xe32dc0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe32dc0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe32dc4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe32dc4: movz            x17, #0x2b03
    //     0xe32dc8: add             lr, x0, x17
    //     0xe32dcc: ldr             lr, [x21, lr, lsl #3]
    //     0xe32dd0: blr             lr
    // 0xe32dd4: ldur            x1, [fp, #-0x30]
    // 0xe32dd8: ArrayStore: r1[3] = r0  ; List_4
    //     0xe32dd8: add             x25, x1, #0x1b
    //     0xe32ddc: str             w0, [x25]
    //     0xe32de0: tbz             w0, #0, #0xe32dfc
    //     0xe32de4: ldurb           w16, [x1, #-1]
    //     0xe32de8: ldurb           w17, [x0, #-1]
    //     0xe32dec: and             x16, x17, x16, lsr #2
    //     0xe32df0: tst             x16, HEAP, lsr #32
    //     0xe32df4: b.eq            #0xe32dfc
    //     0xe32df8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe32dfc: r16 = <String, String?>
    //     0xe32dfc: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe32e00: ldr             x16, [x16, #0x198]
    // 0xe32e04: ldur            lr, [fp, #-0x30]
    // 0xe32e08: stp             lr, x16, [SP]
    // 0xe32e0c: r0 = Map._fromLiteral()
    //     0xe32e0c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe32e10: ldur            x1, [fp, #-0x28]
    // 0xe32e14: mov             x2, x0
    // 0xe32e18: r0 = findAllv4()
    //     0xe32e18: bl              #0xe32e6c  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAllv4
    // 0xe32e1c: ldur            x2, [fp, #-0x20]
    // 0xe32e20: r1 = Function '<anonymous closure>':.
    //     0xe32e20: add             x1, PP, #0x40, lsl #12  ; [pp+0x40990] AnonymousClosure: (0xe32f5c), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onPageRequest (0xe32cc0)
    //     0xe32e24: ldr             x1, [x1, #0x990]
    // 0xe32e28: stur            x0, [fp, #-0x18]
    // 0xe32e2c: r0 = AllocateClosure()
    //     0xe32e2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32e30: r16 = <void?>
    //     0xe32e30: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32e34: ldur            lr, [fp, #-0x18]
    // 0xe32e38: stp             lr, x16, [SP, #8]
    // 0xe32e3c: str             x0, [SP]
    // 0xe32e40: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe32e40: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe32e44: r0 = then()
    //     0xe32e44: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe32e48: ldur            x1, [fp, #-8]
    // 0xe32e4c: ldur            x2, [fp, #-0x10]
    // 0xe32e50: r0 = onPageRequest()
    //     0xe32e50: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe32e54: r0 = Null
    //     0xe32e54: mov             x0, NULL
    // 0xe32e58: LeaveFrame
    //     0xe32e58: mov             SP, fp
    //     0xe32e5c: ldp             fp, lr, [SP], #0x10
    // 0xe32e60: ret
    //     0xe32e60: ret             
    // 0xe32e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32e64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32e68: b               #0xe32ce0
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Article>>) {
    // ** addr: 0xe32f5c, size: 0xa0
    // 0xe32f5c: EnterFrame
    //     0xe32f5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe32f60: mov             fp, SP
    // 0xe32f64: AllocStack(0x28)
    //     0xe32f64: sub             SP, SP, #0x28
    // 0xe32f68: SetupParameters()
    //     0xe32f68: ldr             x0, [fp, #0x18]
    //     0xe32f6c: ldur            w1, [x0, #0x17]
    //     0xe32f70: add             x1, x1, HEAP, lsl #32
    // 0xe32f74: CheckStackOverflow
    //     0xe32f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe32f78: cmp             SP, x16
    //     0xe32f7c: b.ls            #0xe32ff4
    // 0xe32f80: LoadField: r0 = r1->field_f
    //     0xe32f80: ldur            w0, [x1, #0xf]
    // 0xe32f84: DecompressPointer r0
    //     0xe32f84: add             x0, x0, HEAP, lsl #32
    // 0xe32f88: mov             x2, x0
    // 0xe32f8c: stur            x0, [fp, #-8]
    // 0xe32f90: r1 = Function '_onPageRequestError@1880058537':.
    //     0xe32f90: add             x1, PP, #0x40, lsl #12  ; [pp+0x40998] AnonymousClosure: (0xe3303c), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError (0xe32524)
    //     0xe32f94: ldr             x1, [x1, #0x998]
    // 0xe32f98: r0 = AllocateClosure()
    //     0xe32f98: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32f9c: ldur            x2, [fp, #-8]
    // 0xe32fa0: r1 = Function '_onPageRequestSuccess@1880058537':.
    //     0xe32fa0: add             x1, PP, #0x40, lsl #12  ; [pp+0x409a0] AnonymousClosure: (0xe32ffc), in [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::_onPageRequestSuccess (0xe3295c)
    //     0xe32fa4: ldr             x1, [x1, #0x9a0]
    // 0xe32fa8: stur            x0, [fp, #-8]
    // 0xe32fac: r0 = AllocateClosure()
    //     0xe32fac: bl              #0xec1630  ; AllocateClosureStub
    // 0xe32fb0: mov             x1, x0
    // 0xe32fb4: ldr             x0, [fp, #0x10]
    // 0xe32fb8: r2 = LoadClassIdInstr(r0)
    //     0xe32fb8: ldur            x2, [x0, #-1]
    //     0xe32fbc: ubfx            x2, x2, #0xc, #0x14
    // 0xe32fc0: r16 = <void?>
    //     0xe32fc0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe32fc4: stp             x0, x16, [SP, #0x10]
    // 0xe32fc8: ldur            x16, [fp, #-8]
    // 0xe32fcc: stp             x1, x16, [SP]
    // 0xe32fd0: mov             x0, x2
    // 0xe32fd4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe32fd4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe32fd8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe32fd8: sub             lr, x0, #1, lsl #12
    //     0xe32fdc: ldr             lr, [x21, lr, lsl #3]
    //     0xe32fe0: blr             lr
    // 0xe32fe4: r0 = Null
    //     0xe32fe4: mov             x0, NULL
    // 0xe32fe8: LeaveFrame
    //     0xe32fe8: mov             SP, fp
    //     0xe32fec: ldp             fp, lr, [SP], #0x10
    // 0xe32ff0: ret
    //     0xe32ff0: ret             
    // 0xe32ff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe32ff4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe32ff8: b               #0xe32f80
  }
  [closure] void _onPageRequestSuccess(dynamic, List<Article>, Pagination?) {
    // ** addr: 0xe32ffc, size: 0x40
    // 0xe32ffc: EnterFrame
    //     0xe32ffc: stp             fp, lr, [SP, #-0x10]!
    //     0xe33000: mov             fp, SP
    // 0xe33004: ldr             x0, [fp, #0x20]
    // 0xe33008: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe33008: ldur            w1, [x0, #0x17]
    // 0xe3300c: DecompressPointer r1
    //     0xe3300c: add             x1, x1, HEAP, lsl #32
    // 0xe33010: CheckStackOverflow
    //     0xe33010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe33014: cmp             SP, x16
    //     0xe33018: b.ls            #0xe33034
    // 0xe3301c: ldr             x2, [fp, #0x18]
    // 0xe33020: ldr             x3, [fp, #0x10]
    // 0xe33024: r0 = _onPageRequestSuccess()
    //     0xe33024: bl              #0xe3295c  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::_onPageRequestSuccess
    // 0xe33028: LeaveFrame
    //     0xe33028: mov             SP, fp
    //     0xe3302c: ldp             fp, lr, [SP], #0x10
    // 0xe33030: ret
    //     0xe33030: ret             
    // 0xe33034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe33034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe33038: b               #0xe3301c
  }
  [closure] void _onPageRequestError(dynamic, NetworkExceptions) {
    // ** addr: 0xe3303c, size: 0x3c
    // 0xe3303c: EnterFrame
    //     0xe3303c: stp             fp, lr, [SP, #-0x10]!
    //     0xe33040: mov             fp, SP
    // 0xe33044: ldr             x0, [fp, #0x18]
    // 0xe33048: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe33048: ldur            w1, [x0, #0x17]
    // 0xe3304c: DecompressPointer r1
    //     0xe3304c: add             x1, x1, HEAP, lsl #32
    // 0xe33050: CheckStackOverflow
    //     0xe33050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe33054: cmp             SP, x16
    //     0xe33058: b.ls            #0xe33070
    // 0xe3305c: ldr             x2, [fp, #0x10]
    // 0xe33060: r0 = _onError()
    //     0xe33060: bl              #0xe32524  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError
    // 0xe33064: LeaveFrame
    //     0xe33064: mov             SP, fp
    //     0xe33068: ldp             fp, lr, [SP], #0x10
    // 0xe3306c: ret
    //     0xe3306c: ret             
    // 0xe33070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe33070: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe33074: b               #0xe3305c
  }
}
