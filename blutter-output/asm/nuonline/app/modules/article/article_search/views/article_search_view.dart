// lib: , url: package:nuonline/app/modules/article/article_search/views/article_search_view.dart

// class id: 1050141, size: 0x8
class :: {
}

// class id: 5308, size: 0x14, field offset: 0x14
class ArticleSearchView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad1248, size: 0x2d0
    // 0xad1248: EnterFrame
    //     0xad1248: stp             fp, lr, [SP, #-0x10]!
    //     0xad124c: mov             fp, SP
    // 0xad1250: AllocStack(0x30)
    //     0xad1250: sub             SP, SP, #0x30
    // 0xad1254: SetupParameters(ArticleSearchView this /* r1 => r1, fp-0x8 */)
    //     0xad1254: stur            x1, [fp, #-8]
    // 0xad1258: CheckStackOverflow
    //     0xad1258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad125c: cmp             SP, x16
    //     0xad1260: b.ls            #0xad1510
    // 0xad1264: r1 = 1
    //     0xad1264: movz            x1, #0x1
    // 0xad1268: r0 = AllocateContext()
    //     0xad1268: bl              #0xec126c  ; AllocateContextStub
    // 0xad126c: mov             x1, x0
    // 0xad1270: ldur            x0, [fp, #-8]
    // 0xad1274: stur            x1, [fp, #-0x10]
    // 0xad1278: StoreField: r1->field_f = r0
    //     0xad1278: stur            w0, [x1, #0xf]
    // 0xad127c: r0 = Obx()
    //     0xad127c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad1280: ldur            x2, [fp, #-0x10]
    // 0xad1284: r1 = Function '<anonymous closure>':.
    //     0xad1284: add             x1, PP, #0x30, lsl #12  ; [pp+0x30930] AnonymousClosure: (0xad31a8), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::build (0xad1248)
    //     0xad1288: ldr             x1, [x1, #0x930]
    // 0xad128c: stur            x0, [fp, #-8]
    // 0xad1290: r0 = AllocateClosure()
    //     0xad1290: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1294: mov             x1, x0
    // 0xad1298: ldur            x0, [fp, #-8]
    // 0xad129c: StoreField: r0->field_b = r1
    //     0xad129c: stur            w1, [x0, #0xb]
    // 0xad12a0: r0 = AppBar()
    //     0xad12a0: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad12a4: stur            x0, [fp, #-0x18]
    // 0xad12a8: ldur            x16, [fp, #-8]
    // 0xad12ac: str             x16, [SP]
    // 0xad12b0: mov             x1, x0
    // 0xad12b4: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xad12b4: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xad12b8: ldr             x4, [x4, #0x6e8]
    // 0xad12bc: r0 = AppBar()
    //     0xad12bc: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad12c0: r0 = PreferredSize()
    //     0xad12c0: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xad12c4: mov             x1, x0
    // 0xad12c8: r0 = Instance_Size
    //     0xad12c8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xad12cc: ldr             x0, [x0, #0xc88]
    // 0xad12d0: stur            x1, [fp, #-8]
    // 0xad12d4: StoreField: r1->field_f = r0
    //     0xad12d4: stur            w0, [x1, #0xf]
    // 0xad12d8: ldur            x0, [fp, #-0x18]
    // 0xad12dc: StoreField: r1->field_b = r0
    //     0xad12dc: stur            w0, [x1, #0xb]
    // 0xad12e0: r0 = Obx()
    //     0xad12e0: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad12e4: ldur            x2, [fp, #-0x10]
    // 0xad12e8: r1 = Function '<anonymous closure>':.
    //     0xad12e8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30938] AnonymousClosure: (0xad1568), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::build (0xad1248)
    //     0xad12ec: ldr             x1, [x1, #0x938]
    // 0xad12f0: stur            x0, [fp, #-0x10]
    // 0xad12f4: r0 = AllocateClosure()
    //     0xad12f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad12f8: mov             x1, x0
    // 0xad12fc: ldur            x0, [fp, #-0x10]
    // 0xad1300: StoreField: r0->field_b = r1
    //     0xad1300: stur            w1, [x0, #0xb]
    // 0xad1304: r1 = <FlexParentData>
    //     0xad1304: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad1308: ldr             x1, [x1, #0x720]
    // 0xad130c: r0 = Expanded()
    //     0xad130c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad1310: mov             x3, x0
    // 0xad1314: r0 = 1
    //     0xad1314: movz            x0, #0x1
    // 0xad1318: stur            x3, [fp, #-0x18]
    // 0xad131c: StoreField: r3->field_13 = r0
    //     0xad131c: stur            x0, [x3, #0x13]
    // 0xad1320: r0 = Instance_FlexFit
    //     0xad1320: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad1324: ldr             x0, [x0, #0x728]
    // 0xad1328: StoreField: r3->field_1b = r0
    //     0xad1328: stur            w0, [x3, #0x1b]
    // 0xad132c: ldur            x0, [fp, #-0x10]
    // 0xad1330: StoreField: r3->field_b = r0
    //     0xad1330: stur            w0, [x3, #0xb]
    // 0xad1334: r1 = Null
    //     0xad1334: mov             x1, NULL
    // 0xad1338: r2 = 2
    //     0xad1338: movz            x2, #0x2
    // 0xad133c: r0 = AllocateArray()
    //     0xad133c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad1340: mov             x2, x0
    // 0xad1344: ldur            x0, [fp, #-0x18]
    // 0xad1348: stur            x2, [fp, #-0x10]
    // 0xad134c: StoreField: r2->field_f = r0
    //     0xad134c: stur            w0, [x2, #0xf]
    // 0xad1350: r1 = <Widget>
    //     0xad1350: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad1354: r0 = AllocateGrowableArray()
    //     0xad1354: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad1358: mov             x1, x0
    // 0xad135c: ldur            x0, [fp, #-0x10]
    // 0xad1360: stur            x1, [fp, #-0x18]
    // 0xad1364: StoreField: r1->field_f = r0
    //     0xad1364: stur            w0, [x1, #0xf]
    // 0xad1368: r0 = 2
    //     0xad1368: movz            x0, #0x2
    // 0xad136c: StoreField: r1->field_b = r0
    //     0xad136c: stur            w0, [x1, #0xb]
    // 0xad1370: r0 = find()
    //     0xad1370: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad1374: mov             x1, x0
    // 0xad1378: r0 = _adsVisibility()
    //     0xad1378: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xad137c: mov             x2, x0
    // 0xad1380: r1 = Null
    //     0xad1380: mov             x1, NULL
    // 0xad1384: r0 = AdsConfig.fromJson()
    //     0xad1384: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xad1388: LoadField: r1 = r0->field_f
    //     0xad1388: ldur            w1, [x0, #0xf]
    // 0xad138c: DecompressPointer r1
    //     0xad138c: add             x1, x1, HEAP, lsl #32
    // 0xad1390: LoadField: r0 = r1->field_7
    //     0xad1390: ldur            w0, [x1, #7]
    // 0xad1394: DecompressPointer r0
    //     0xad1394: add             x0, x0, HEAP, lsl #32
    // 0xad1398: tbnz            w0, #4, #0xad145c
    // 0xad139c: ldur            x1, [fp, #-0x18]
    // 0xad13a0: r0 = find()
    //     0xad13a0: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad13a4: mov             x1, x0
    // 0xad13a8: r0 = _adsVisibility()
    //     0xad13a8: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xad13ac: mov             x2, x0
    // 0xad13b0: r1 = Null
    //     0xad13b0: mov             x1, NULL
    // 0xad13b4: r0 = AdsConfig.fromJson()
    //     0xad13b4: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xad13b8: LoadField: r1 = r0->field_f
    //     0xad13b8: ldur            w1, [x0, #0xf]
    // 0xad13bc: DecompressPointer r1
    //     0xad13bc: add             x1, x1, HEAP, lsl #32
    // 0xad13c0: LoadField: r0 = r1->field_f
    //     0xad13c0: ldur            w0, [x1, #0xf]
    // 0xad13c4: DecompressPointer r0
    //     0xad13c4: add             x0, x0, HEAP, lsl #32
    // 0xad13c8: stur            x0, [fp, #-0x10]
    // 0xad13cc: r0 = AdmobBannerWidget()
    //     0xad13cc: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xad13d0: mov             x2, x0
    // 0xad13d4: ldur            x0, [fp, #-0x10]
    // 0xad13d8: stur            x2, [fp, #-0x28]
    // 0xad13dc: StoreField: r2->field_b = r0
    //     0xad13dc: stur            w0, [x2, #0xb]
    // 0xad13e0: ldur            x0, [fp, #-0x18]
    // 0xad13e4: LoadField: r1 = r0->field_b
    //     0xad13e4: ldur            w1, [x0, #0xb]
    // 0xad13e8: LoadField: r3 = r0->field_f
    //     0xad13e8: ldur            w3, [x0, #0xf]
    // 0xad13ec: DecompressPointer r3
    //     0xad13ec: add             x3, x3, HEAP, lsl #32
    // 0xad13f0: LoadField: r4 = r3->field_b
    //     0xad13f0: ldur            w4, [x3, #0xb]
    // 0xad13f4: r3 = LoadInt32Instr(r1)
    //     0xad13f4: sbfx            x3, x1, #1, #0x1f
    // 0xad13f8: stur            x3, [fp, #-0x20]
    // 0xad13fc: r1 = LoadInt32Instr(r4)
    //     0xad13fc: sbfx            x1, x4, #1, #0x1f
    // 0xad1400: cmp             x3, x1
    // 0xad1404: b.ne            #0xad1410
    // 0xad1408: mov             x1, x0
    // 0xad140c: r0 = _growToNextCapacity()
    //     0xad140c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad1410: ldur            x2, [fp, #-0x18]
    // 0xad1414: ldur            x3, [fp, #-0x20]
    // 0xad1418: add             x0, x3, #1
    // 0xad141c: lsl             x1, x0, #1
    // 0xad1420: StoreField: r2->field_b = r1
    //     0xad1420: stur            w1, [x2, #0xb]
    // 0xad1424: LoadField: r1 = r2->field_f
    //     0xad1424: ldur            w1, [x2, #0xf]
    // 0xad1428: DecompressPointer r1
    //     0xad1428: add             x1, x1, HEAP, lsl #32
    // 0xad142c: ldur            x0, [fp, #-0x28]
    // 0xad1430: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad1430: add             x25, x1, x3, lsl #2
    //     0xad1434: add             x25, x25, #0xf
    //     0xad1438: str             w0, [x25]
    //     0xad143c: tbz             w0, #0, #0xad1458
    //     0xad1440: ldurb           w16, [x1, #-1]
    //     0xad1444: ldurb           w17, [x0, #-1]
    //     0xad1448: and             x16, x17, x16, lsr #2
    //     0xad144c: tst             x16, HEAP, lsr #32
    //     0xad1450: b.eq            #0xad1458
    //     0xad1454: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad1458: b               #0xad1460
    // 0xad145c: ldur            x2, [fp, #-0x18]
    // 0xad1460: ldur            x0, [fp, #-8]
    // 0xad1464: r0 = Column()
    //     0xad1464: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad1468: mov             x1, x0
    // 0xad146c: r0 = Instance_Axis
    //     0xad146c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad1470: stur            x1, [fp, #-0x10]
    // 0xad1474: StoreField: r1->field_f = r0
    //     0xad1474: stur            w0, [x1, #0xf]
    // 0xad1478: r0 = Instance_MainAxisAlignment
    //     0xad1478: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad147c: ldr             x0, [x0, #0x730]
    // 0xad1480: StoreField: r1->field_13 = r0
    //     0xad1480: stur            w0, [x1, #0x13]
    // 0xad1484: r0 = Instance_MainAxisSize
    //     0xad1484: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad1488: ldr             x0, [x0, #0x738]
    // 0xad148c: ArrayStore: r1[0] = r0  ; List_4
    //     0xad148c: stur            w0, [x1, #0x17]
    // 0xad1490: r0 = Instance_CrossAxisAlignment
    //     0xad1490: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad1494: ldr             x0, [x0, #0x740]
    // 0xad1498: StoreField: r1->field_1b = r0
    //     0xad1498: stur            w0, [x1, #0x1b]
    // 0xad149c: r0 = Instance_VerticalDirection
    //     0xad149c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad14a0: ldr             x0, [x0, #0x748]
    // 0xad14a4: StoreField: r1->field_23 = r0
    //     0xad14a4: stur            w0, [x1, #0x23]
    // 0xad14a8: r0 = Instance_Clip
    //     0xad14a8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad14ac: ldr             x0, [x0, #0x750]
    // 0xad14b0: StoreField: r1->field_2b = r0
    //     0xad14b0: stur            w0, [x1, #0x2b]
    // 0xad14b4: StoreField: r1->field_2f = rZR
    //     0xad14b4: stur            xzr, [x1, #0x2f]
    // 0xad14b8: ldur            x0, [fp, #-0x18]
    // 0xad14bc: StoreField: r1->field_b = r0
    //     0xad14bc: stur            w0, [x1, #0xb]
    // 0xad14c0: r0 = Scaffold()
    //     0xad14c0: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad14c4: ldur            x1, [fp, #-8]
    // 0xad14c8: StoreField: r0->field_13 = r1
    //     0xad14c8: stur            w1, [x0, #0x13]
    // 0xad14cc: ldur            x1, [fp, #-0x10]
    // 0xad14d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad14d0: stur            w1, [x0, #0x17]
    // 0xad14d4: r1 = Instance_AlignmentDirectional
    //     0xad14d4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad14d8: ldr             x1, [x1, #0x758]
    // 0xad14dc: StoreField: r0->field_2b = r1
    //     0xad14dc: stur            w1, [x0, #0x2b]
    // 0xad14e0: r1 = true
    //     0xad14e0: add             x1, NULL, #0x20  ; true
    // 0xad14e4: StoreField: r0->field_53 = r1
    //     0xad14e4: stur            w1, [x0, #0x53]
    // 0xad14e8: r2 = Instance_DragStartBehavior
    //     0xad14e8: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad14ec: StoreField: r0->field_57 = r2
    //     0xad14ec: stur            w2, [x0, #0x57]
    // 0xad14f0: r2 = false
    //     0xad14f0: add             x2, NULL, #0x30  ; false
    // 0xad14f4: StoreField: r0->field_b = r2
    //     0xad14f4: stur            w2, [x0, #0xb]
    // 0xad14f8: StoreField: r0->field_f = r2
    //     0xad14f8: stur            w2, [x0, #0xf]
    // 0xad14fc: StoreField: r0->field_5f = r1
    //     0xad14fc: stur            w1, [x0, #0x5f]
    // 0xad1500: StoreField: r0->field_63 = r1
    //     0xad1500: stur            w1, [x0, #0x63]
    // 0xad1504: LeaveFrame
    //     0xad1504: mov             SP, fp
    //     0xad1508: ldp             fp, lr, [SP], #0x10
    // 0xad150c: ret
    //     0xad150c: ret             
    // 0xad1510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1514: b               #0xad1264
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0xad1568, size: 0x344
    // 0xad1568: EnterFrame
    //     0xad1568: stp             fp, lr, [SP, #-0x10]!
    //     0xad156c: mov             fp, SP
    // 0xad1570: AllocStack(0x38)
    //     0xad1570: sub             SP, SP, #0x38
    // 0xad1574: SetupParameters()
    //     0xad1574: ldr             x0, [fp, #0x10]
    //     0xad1578: ldur            w2, [x0, #0x17]
    //     0xad157c: add             x2, x2, HEAP, lsl #32
    //     0xad1580: stur            x2, [fp, #-8]
    // 0xad1584: CheckStackOverflow
    //     0xad1584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1588: cmp             SP, x16
    //     0xad158c: b.ls            #0xad18a4
    // 0xad1590: LoadField: r1 = r2->field_f
    //     0xad1590: ldur            w1, [x2, #0xf]
    // 0xad1594: DecompressPointer r1
    //     0xad1594: add             x1, x1, HEAP, lsl #32
    // 0xad1598: r0 = controller()
    //     0xad1598: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad159c: LoadField: r1 = r0->field_47
    //     0xad159c: ldur            w1, [x0, #0x47]
    // 0xad15a0: DecompressPointer r1
    //     0xad15a0: add             x1, x1, HEAP, lsl #32
    // 0xad15a4: r0 = value()
    //     0xad15a4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad15a8: tbnz            w0, #4, #0xad1694
    // 0xad15ac: ldur            x2, [fp, #-8]
    // 0xad15b0: LoadField: r1 = r2->field_f
    //     0xad15b0: ldur            w1, [x2, #0xf]
    // 0xad15b4: DecompressPointer r1
    //     0xad15b4: add             x1, x1, HEAP, lsl #32
    // 0xad15b8: r0 = buildSearchHistory()
    //     0xad15b8: bl              #0xad26b4  ; [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchHistory
    // 0xad15bc: ldur            x2, [fp, #-8]
    // 0xad15c0: stur            x0, [fp, #-0x10]
    // 0xad15c4: LoadField: r1 = r2->field_f
    //     0xad15c4: ldur            w1, [x2, #0xf]
    // 0xad15c8: DecompressPointer r1
    //     0xad15c8: add             x1, x1, HEAP, lsl #32
    // 0xad15cc: r0 = buildSearchRecomendation()
    //     0xad15cc: bl              #0xad1ad8  ; [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchRecomendation
    // 0xad15d0: r1 = Null
    //     0xad15d0: mov             x1, NULL
    // 0xad15d4: r2 = 4
    //     0xad15d4: movz            x2, #0x4
    // 0xad15d8: stur            x0, [fp, #-0x18]
    // 0xad15dc: r0 = AllocateArray()
    //     0xad15dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad15e0: mov             x2, x0
    // 0xad15e4: ldur            x0, [fp, #-0x10]
    // 0xad15e8: stur            x2, [fp, #-0x20]
    // 0xad15ec: StoreField: r2->field_f = r0
    //     0xad15ec: stur            w0, [x2, #0xf]
    // 0xad15f0: ldur            x0, [fp, #-0x18]
    // 0xad15f4: StoreField: r2->field_13 = r0
    //     0xad15f4: stur            w0, [x2, #0x13]
    // 0xad15f8: r1 = <Widget>
    //     0xad15f8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad15fc: r0 = AllocateGrowableArray()
    //     0xad15fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad1600: mov             x1, x0
    // 0xad1604: ldur            x0, [fp, #-0x20]
    // 0xad1608: stur            x1, [fp, #-0x10]
    // 0xad160c: StoreField: r1->field_f = r0
    //     0xad160c: stur            w0, [x1, #0xf]
    // 0xad1610: r0 = 4
    //     0xad1610: movz            x0, #0x4
    // 0xad1614: StoreField: r1->field_b = r0
    //     0xad1614: stur            w0, [x1, #0xb]
    // 0xad1618: r0 = Column()
    //     0xad1618: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad161c: mov             x1, x0
    // 0xad1620: r0 = Instance_Axis
    //     0xad1620: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad1624: stur            x1, [fp, #-0x18]
    // 0xad1628: StoreField: r1->field_f = r0
    //     0xad1628: stur            w0, [x1, #0xf]
    // 0xad162c: r3 = Instance_MainAxisAlignment
    //     0xad162c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad1630: ldr             x3, [x3, #0x730]
    // 0xad1634: StoreField: r1->field_13 = r3
    //     0xad1634: stur            w3, [x1, #0x13]
    // 0xad1638: r4 = Instance_MainAxisSize
    //     0xad1638: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad163c: ldr             x4, [x4, #0x738]
    // 0xad1640: ArrayStore: r1[0] = r4  ; List_4
    //     0xad1640: stur            w4, [x1, #0x17]
    // 0xad1644: r5 = Instance_CrossAxisAlignment
    //     0xad1644: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad1648: ldr             x5, [x5, #0x740]
    // 0xad164c: StoreField: r1->field_1b = r5
    //     0xad164c: stur            w5, [x1, #0x1b]
    // 0xad1650: r6 = Instance_VerticalDirection
    //     0xad1650: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad1654: ldr             x6, [x6, #0x748]
    // 0xad1658: StoreField: r1->field_23 = r6
    //     0xad1658: stur            w6, [x1, #0x23]
    // 0xad165c: r7 = Instance_Clip
    //     0xad165c: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad1660: ldr             x7, [x7, #0x750]
    // 0xad1664: StoreField: r1->field_2b = r7
    //     0xad1664: stur            w7, [x1, #0x2b]
    // 0xad1668: StoreField: r1->field_2f = rZR
    //     0xad1668: stur            xzr, [x1, #0x2f]
    // 0xad166c: ldur            x0, [fp, #-0x10]
    // 0xad1670: StoreField: r1->field_b = r0
    //     0xad1670: stur            w0, [x1, #0xb]
    // 0xad1674: r0 = Padding()
    //     0xad1674: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad1678: r8 = Instance_EdgeInsets
    //     0xad1678: ldr             x8, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad167c: StoreField: r0->field_f = r8
    //     0xad167c: stur            w8, [x0, #0xf]
    // 0xad1680: ldur            x1, [fp, #-0x18]
    // 0xad1684: StoreField: r0->field_b = r1
    //     0xad1684: stur            w1, [x0, #0xb]
    // 0xad1688: LeaveFrame
    //     0xad1688: mov             SP, fp
    //     0xad168c: ldp             fp, lr, [SP], #0x10
    // 0xad1690: ret
    //     0xad1690: ret             
    // 0xad1694: ldur            x2, [fp, #-8]
    // 0xad1698: r8 = Instance_EdgeInsets
    //     0xad1698: ldr             x8, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad169c: r5 = Instance_CrossAxisAlignment
    //     0xad169c: add             x5, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad16a0: ldr             x5, [x5, #0x740]
    // 0xad16a4: r3 = Instance_MainAxisAlignment
    //     0xad16a4: add             x3, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad16a8: ldr             x3, [x3, #0x730]
    // 0xad16ac: r4 = Instance_MainAxisSize
    //     0xad16ac: add             x4, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad16b0: ldr             x4, [x4, #0x738]
    // 0xad16b4: r0 = Instance_Axis
    //     0xad16b4: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad16b8: r6 = Instance_VerticalDirection
    //     0xad16b8: add             x6, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad16bc: ldr             x6, [x6, #0x748]
    // 0xad16c0: r7 = Instance_Clip
    //     0xad16c0: add             x7, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad16c4: ldr             x7, [x7, #0x750]
    // 0xad16c8: LoadField: r1 = r2->field_f
    //     0xad16c8: ldur            w1, [x2, #0xf]
    // 0xad16cc: DecompressPointer r1
    //     0xad16cc: add             x1, x1, HEAP, lsl #32
    // 0xad16d0: r0 = controller()
    //     0xad16d0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad16d4: mov             x1, x0
    // 0xad16d8: r0 = hasError()
    //     0xad16d8: bl              #0xad1aa0  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::hasError
    // 0xad16dc: tbnz            w0, #4, #0xad17dc
    // 0xad16e0: ldur            x2, [fp, #-8]
    // 0xad16e4: LoadField: r1 = r2->field_f
    //     0xad16e4: ldur            w1, [x2, #0xf]
    // 0xad16e8: DecompressPointer r1
    //     0xad16e8: add             x1, x1, HEAP, lsl #32
    // 0xad16ec: r0 = buildEmptyResult()
    //     0xad16ec: bl              #0xad1990  ; [package:nuonline/app/modules/article/article_category/widgets/article_category_builder_widget.dart] ArticleCategoryBuilderWidget::buildEmptyResult
    // 0xad16f0: ldur            x2, [fp, #-8]
    // 0xad16f4: stur            x0, [fp, #-0x10]
    // 0xad16f8: LoadField: r1 = r2->field_f
    //     0xad16f8: ldur            w1, [x2, #0xf]
    // 0xad16fc: DecompressPointer r1
    //     0xad16fc: add             x1, x1, HEAP, lsl #32
    // 0xad1700: r0 = buildSearchRecomendation()
    //     0xad1700: bl              #0xad1ad8  ; [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchRecomendation
    // 0xad1704: r1 = Null
    //     0xad1704: mov             x1, NULL
    // 0xad1708: r2 = 6
    //     0xad1708: movz            x2, #0x6
    // 0xad170c: stur            x0, [fp, #-0x18]
    // 0xad1710: r0 = AllocateArray()
    //     0xad1710: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad1714: mov             x2, x0
    // 0xad1718: ldur            x0, [fp, #-0x10]
    // 0xad171c: stur            x2, [fp, #-0x20]
    // 0xad1720: StoreField: r2->field_f = r0
    //     0xad1720: stur            w0, [x2, #0xf]
    // 0xad1724: r16 = Instance_SizedBox
    //     0xad1724: add             x16, PP, #0x29, lsl #12  ; [pp+0x297f8] Obj!SizedBox@e1e1e1
    //     0xad1728: ldr             x16, [x16, #0x7f8]
    // 0xad172c: StoreField: r2->field_13 = r16
    //     0xad172c: stur            w16, [x2, #0x13]
    // 0xad1730: ldur            x0, [fp, #-0x18]
    // 0xad1734: ArrayStore: r2[0] = r0  ; List_4
    //     0xad1734: stur            w0, [x2, #0x17]
    // 0xad1738: r1 = <Widget>
    //     0xad1738: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad173c: r0 = AllocateGrowableArray()
    //     0xad173c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad1740: mov             x1, x0
    // 0xad1744: ldur            x0, [fp, #-0x20]
    // 0xad1748: stur            x1, [fp, #-0x10]
    // 0xad174c: StoreField: r1->field_f = r0
    //     0xad174c: stur            w0, [x1, #0xf]
    // 0xad1750: r0 = 6
    //     0xad1750: movz            x0, #0x6
    // 0xad1754: StoreField: r1->field_b = r0
    //     0xad1754: stur            w0, [x1, #0xb]
    // 0xad1758: r0 = Column()
    //     0xad1758: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad175c: mov             x1, x0
    // 0xad1760: r0 = Instance_Axis
    //     0xad1760: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad1764: stur            x1, [fp, #-0x18]
    // 0xad1768: StoreField: r1->field_f = r0
    //     0xad1768: stur            w0, [x1, #0xf]
    // 0xad176c: r0 = Instance_MainAxisAlignment
    //     0xad176c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad1770: ldr             x0, [x0, #0x730]
    // 0xad1774: StoreField: r1->field_13 = r0
    //     0xad1774: stur            w0, [x1, #0x13]
    // 0xad1778: r0 = Instance_MainAxisSize
    //     0xad1778: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad177c: ldr             x0, [x0, #0x738]
    // 0xad1780: ArrayStore: r1[0] = r0  ; List_4
    //     0xad1780: stur            w0, [x1, #0x17]
    // 0xad1784: r0 = Instance_CrossAxisAlignment
    //     0xad1784: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad1788: ldr             x0, [x0, #0x740]
    // 0xad178c: StoreField: r1->field_1b = r0
    //     0xad178c: stur            w0, [x1, #0x1b]
    // 0xad1790: r0 = Instance_VerticalDirection
    //     0xad1790: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad1794: ldr             x0, [x0, #0x748]
    // 0xad1798: StoreField: r1->field_23 = r0
    //     0xad1798: stur            w0, [x1, #0x23]
    // 0xad179c: r0 = Instance_Clip
    //     0xad179c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad17a0: ldr             x0, [x0, #0x750]
    // 0xad17a4: StoreField: r1->field_2b = r0
    //     0xad17a4: stur            w0, [x1, #0x2b]
    // 0xad17a8: StoreField: r1->field_2f = rZR
    //     0xad17a8: stur            xzr, [x1, #0x2f]
    // 0xad17ac: ldur            x0, [fp, #-0x10]
    // 0xad17b0: StoreField: r1->field_b = r0
    //     0xad17b0: stur            w0, [x1, #0xb]
    // 0xad17b4: r0 = Padding()
    //     0xad17b4: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad17b8: mov             x1, x0
    // 0xad17bc: r0 = Instance_EdgeInsets
    //     0xad17bc: ldr             x0, [PP, #0x6e08]  ; [pp+0x6e08] Obj!EdgeInsets@e11f51
    // 0xad17c0: StoreField: r1->field_f = r0
    //     0xad17c0: stur            w0, [x1, #0xf]
    // 0xad17c4: ldur            x0, [fp, #-0x18]
    // 0xad17c8: StoreField: r1->field_b = r0
    //     0xad17c8: stur            w0, [x1, #0xb]
    // 0xad17cc: mov             x0, x1
    // 0xad17d0: LeaveFrame
    //     0xad17d0: mov             SP, fp
    //     0xad17d4: ldp             fp, lr, [SP], #0x10
    // 0xad17d8: ret
    //     0xad17d8: ret             
    // 0xad17dc: ldur            x2, [fp, #-8]
    // 0xad17e0: LoadField: r1 = r2->field_f
    //     0xad17e0: ldur            w1, [x2, #0xf]
    // 0xad17e4: DecompressPointer r1
    //     0xad17e4: add             x1, x1, HEAP, lsl #32
    // 0xad17e8: r0 = controller()
    //     0xad17e8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad17ec: ldur            x2, [fp, #-8]
    // 0xad17f0: stur            x0, [fp, #-0x10]
    // 0xad17f4: LoadField: r1 = r2->field_f
    //     0xad17f4: ldur            w1, [x2, #0xf]
    // 0xad17f8: DecompressPointer r1
    //     0xad17f8: add             x1, x1, HEAP, lsl #32
    // 0xad17fc: r0 = controller()
    //     0xad17fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad1800: mov             x1, x0
    // 0xad1804: r0 = itemsCount()
    //     0xad1804: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xad1808: r1 = Function '<anonymous closure>':.
    //     0xad1808: add             x1, PP, #0x30, lsl #12  ; [pp+0x30940] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad180c: ldr             x1, [x1, #0x940]
    // 0xad1810: r2 = Null
    //     0xad1810: mov             x2, NULL
    // 0xad1814: stur            x0, [fp, #-0x28]
    // 0xad1818: r0 = AllocateClosure()
    //     0xad1818: bl              #0xec1630  ; AllocateClosureStub
    // 0xad181c: ldur            x2, [fp, #-8]
    // 0xad1820: r1 = Function '<anonymous closure>':.
    //     0xad1820: add             x1, PP, #0x30, lsl #12  ; [pp+0x30948] AnonymousClosure: (0xad2ea4), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::build (0xad1248)
    //     0xad1824: ldr             x1, [x1, #0x948]
    // 0xad1828: stur            x0, [fp, #-8]
    // 0xad182c: r0 = AllocateClosure()
    //     0xad182c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1830: stur            x0, [fp, #-0x18]
    // 0xad1834: r0 = ListView()
    //     0xad1834: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad1838: stur            x0, [fp, #-0x20]
    // 0xad183c: r16 = true
    //     0xad183c: add             x16, NULL, #0x20  ; true
    // 0xad1840: r30 = Instance_EdgeInsets
    //     0xad1840: add             lr, PP, #0x29, lsl #12  ; [pp+0x290f8] Obj!EdgeInsets@e125b1
    //     0xad1844: ldr             lr, [lr, #0xf8]
    // 0xad1848: stp             lr, x16, [SP]
    // 0xad184c: mov             x1, x0
    // 0xad1850: ldur            x2, [fp, #-0x18]
    // 0xad1854: ldur            x3, [fp, #-0x28]
    // 0xad1858: ldur            x5, [fp, #-8]
    // 0xad185c: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xad185c: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xad1860: ldr             x4, [x4, #0x100]
    // 0xad1864: r0 = ListView.separated()
    //     0xad1864: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad1868: ldur            x2, [fp, #-0x10]
    // 0xad186c: r1 = Function 'onPageScrolled':.
    //     0xad186c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xad1870: ldr             x1, [x1, #0xa88]
    // 0xad1874: r0 = AllocateClosure()
    //     0xad1874: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1878: r1 = <ScrollNotification>
    //     0xad1878: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xad187c: ldr             x1, [x1, #0x110]
    // 0xad1880: stur            x0, [fp, #-8]
    // 0xad1884: r0 = NotificationListener()
    //     0xad1884: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xad1888: ldur            x1, [fp, #-8]
    // 0xad188c: StoreField: r0->field_13 = r1
    //     0xad188c: stur            w1, [x0, #0x13]
    // 0xad1890: ldur            x1, [fp, #-0x20]
    // 0xad1894: StoreField: r0->field_b = r1
    //     0xad1894: stur            w1, [x0, #0xb]
    // 0xad1898: LeaveFrame
    //     0xad1898: mov             SP, fp
    //     0xad189c: ldp             fp, lr, [SP], #0x10
    // 0xad18a0: ret
    //     0xad18a0: ret             
    // 0xad18a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad18a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad18a8: b               #0xad1590
  }
  _ buildSearchRecomendation(/* No info */) {
    // ** addr: 0xad1ad8, size: 0x1dc
    // 0xad1ad8: EnterFrame
    //     0xad1ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xad1adc: mov             fp, SP
    // 0xad1ae0: AllocStack(0x28)
    //     0xad1ae0: sub             SP, SP, #0x28
    // 0xad1ae4: SetupParameters(ArticleSearchView this /* r1 => r1, fp-0x8 */)
    //     0xad1ae4: stur            x1, [fp, #-8]
    // 0xad1ae8: CheckStackOverflow
    //     0xad1ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1aec: cmp             SP, x16
    //     0xad1af0: b.ls            #0xad1cac
    // 0xad1af4: r1 = 1
    //     0xad1af4: movz            x1, #0x1
    // 0xad1af8: r0 = AllocateContext()
    //     0xad1af8: bl              #0xec126c  ; AllocateContextStub
    // 0xad1afc: mov             x2, x0
    // 0xad1b00: ldur            x0, [fp, #-8]
    // 0xad1b04: stur            x2, [fp, #-0x10]
    // 0xad1b08: StoreField: r2->field_f = r0
    //     0xad1b08: stur            w0, [x2, #0xf]
    // 0xad1b0c: mov             x1, x0
    // 0xad1b10: r0 = controller()
    //     0xad1b10: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad1b14: LoadField: r1 = r0->field_4f
    //     0xad1b14: ldur            w1, [x0, #0x4f]
    // 0xad1b18: DecompressPointer r1
    //     0xad1b18: add             x1, x1, HEAP, lsl #32
    // 0xad1b1c: r0 = value()
    //     0xad1b1c: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad1b20: r1 = LoadClassIdInstr(r0)
    //     0xad1b20: ldur            x1, [x0, #-1]
    //     0xad1b24: ubfx            x1, x1, #0xc, #0x14
    // 0xad1b28: str             x0, [SP]
    // 0xad1b2c: mov             x0, x1
    // 0xad1b30: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad1b30: movz            x17, #0xc834
    //     0xad1b34: add             lr, x0, x17
    //     0xad1b38: ldr             lr, [x21, lr, lsl #3]
    //     0xad1b3c: blr             lr
    // 0xad1b40: cbnz            w0, #0xad1b58
    // 0xad1b44: r0 = Instance_SizedBox
    //     0xad1b44: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad1b48: ldr             x0, [x0, #0xc40]
    // 0xad1b4c: LeaveFrame
    //     0xad1b4c: mov             SP, fp
    //     0xad1b50: ldp             fp, lr, [SP], #0x10
    // 0xad1b54: ret
    //     0xad1b54: ret             
    // 0xad1b58: ldur            x1, [fp, #-8]
    // 0xad1b5c: r0 = controller()
    //     0xad1b5c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad1b60: LoadField: r3 = r0->field_4f
    //     0xad1b60: ldur            w3, [x0, #0x4f]
    // 0xad1b64: DecompressPointer r3
    //     0xad1b64: add             x3, x3, HEAP, lsl #32
    // 0xad1b68: ldur            x2, [fp, #-0x10]
    // 0xad1b6c: stur            x3, [fp, #-8]
    // 0xad1b70: r1 = Function '<anonymous closure>':.
    //     0xad1b70: add             x1, PP, #0x30, lsl #12  ; [pp+0x30960] AnonymousClosure: (0xad1cb4), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchRecomendation (0xad1ad8)
    //     0xad1b74: ldr             x1, [x1, #0x960]
    // 0xad1b78: r0 = AllocateClosure()
    //     0xad1b78: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1b7c: r16 = <NTagButton>
    //     0xad1b7c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30968] TypeArguments: <NTagButton>
    //     0xad1b80: ldr             x16, [x16, #0x968]
    // 0xad1b84: ldur            lr, [fp, #-8]
    // 0xad1b88: stp             lr, x16, [SP, #8]
    // 0xad1b8c: str             x0, [SP]
    // 0xad1b90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad1b90: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad1b94: r0 = map()
    //     0xad1b94: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xad1b98: LoadField: r1 = r0->field_7
    //     0xad1b98: ldur            w1, [x0, #7]
    // 0xad1b9c: DecompressPointer r1
    //     0xad1b9c: add             x1, x1, HEAP, lsl #32
    // 0xad1ba0: mov             x2, x0
    // 0xad1ba4: r0 = _GrowableList.of()
    //     0xad1ba4: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xad1ba8: stur            x0, [fp, #-8]
    // 0xad1bac: r0 = Wrap()
    //     0xad1bac: bl              #0xa3582c  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xad1bb0: mov             x3, x0
    // 0xad1bb4: r0 = Instance_Axis
    //     0xad1bb4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xad1bb8: stur            x3, [fp, #-0x10]
    // 0xad1bbc: StoreField: r3->field_f = r0
    //     0xad1bbc: stur            w0, [x3, #0xf]
    // 0xad1bc0: r0 = Instance_WrapAlignment
    //     0xad1bc0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27610] Obj!WrapAlignment@e352c1
    //     0xad1bc4: ldr             x0, [x0, #0x610]
    // 0xad1bc8: StoreField: r3->field_13 = r0
    //     0xad1bc8: stur            w0, [x3, #0x13]
    // 0xad1bcc: d0 = 12.000000
    //     0xad1bcc: fmov            d0, #12.00000000
    // 0xad1bd0: ArrayStore: r3[0] = d0  ; List_8
    //     0xad1bd0: stur            d0, [x3, #0x17]
    // 0xad1bd4: StoreField: r3->field_1f = r0
    //     0xad1bd4: stur            w0, [x3, #0x1f]
    // 0xad1bd8: StoreField: r3->field_23 = d0
    //     0xad1bd8: stur            d0, [x3, #0x23]
    // 0xad1bdc: r0 = Instance_WrapCrossAlignment
    //     0xad1bdc: add             x0, PP, #0x27, lsl #12  ; [pp+0x27618] Obj!WrapCrossAlignment@e35201
    //     0xad1be0: ldr             x0, [x0, #0x618]
    // 0xad1be4: StoreField: r3->field_2b = r0
    //     0xad1be4: stur            w0, [x3, #0x2b]
    // 0xad1be8: r0 = Instance_VerticalDirection
    //     0xad1be8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad1bec: ldr             x0, [x0, #0x748]
    // 0xad1bf0: StoreField: r3->field_33 = r0
    //     0xad1bf0: stur            w0, [x3, #0x33]
    // 0xad1bf4: r4 = Instance_Clip
    //     0xad1bf4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad1bf8: ldr             x4, [x4, #0x750]
    // 0xad1bfc: StoreField: r3->field_37 = r4
    //     0xad1bfc: stur            w4, [x3, #0x37]
    // 0xad1c00: ldur            x1, [fp, #-8]
    // 0xad1c04: StoreField: r3->field_b = r1
    //     0xad1c04: stur            w1, [x3, #0xb]
    // 0xad1c08: r1 = Null
    //     0xad1c08: mov             x1, NULL
    // 0xad1c0c: r2 = 4
    //     0xad1c0c: movz            x2, #0x4
    // 0xad1c10: r0 = AllocateArray()
    //     0xad1c10: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad1c14: stur            x0, [fp, #-8]
    // 0xad1c18: r16 = Instance_Padding
    //     0xad1c18: add             x16, PP, #0x30, lsl #12  ; [pp+0x30970] Obj!Padding@e1e7c1
    //     0xad1c1c: ldr             x16, [x16, #0x970]
    // 0xad1c20: StoreField: r0->field_f = r16
    //     0xad1c20: stur            w16, [x0, #0xf]
    // 0xad1c24: ldur            x1, [fp, #-0x10]
    // 0xad1c28: StoreField: r0->field_13 = r1
    //     0xad1c28: stur            w1, [x0, #0x13]
    // 0xad1c2c: r1 = <Widget>
    //     0xad1c2c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad1c30: r0 = AllocateGrowableArray()
    //     0xad1c30: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad1c34: mov             x1, x0
    // 0xad1c38: ldur            x0, [fp, #-8]
    // 0xad1c3c: stur            x1, [fp, #-0x10]
    // 0xad1c40: StoreField: r1->field_f = r0
    //     0xad1c40: stur            w0, [x1, #0xf]
    // 0xad1c44: r0 = 4
    //     0xad1c44: movz            x0, #0x4
    // 0xad1c48: StoreField: r1->field_b = r0
    //     0xad1c48: stur            w0, [x1, #0xb]
    // 0xad1c4c: r0 = Column()
    //     0xad1c4c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad1c50: r1 = Instance_Axis
    //     0xad1c50: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad1c54: StoreField: r0->field_f = r1
    //     0xad1c54: stur            w1, [x0, #0xf]
    // 0xad1c58: r1 = Instance_MainAxisAlignment
    //     0xad1c58: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad1c5c: ldr             x1, [x1, #0x730]
    // 0xad1c60: StoreField: r0->field_13 = r1
    //     0xad1c60: stur            w1, [x0, #0x13]
    // 0xad1c64: r1 = Instance_MainAxisSize
    //     0xad1c64: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad1c68: ldr             x1, [x1, #0x738]
    // 0xad1c6c: ArrayStore: r0[0] = r1  ; List_4
    //     0xad1c6c: stur            w1, [x0, #0x17]
    // 0xad1c70: r1 = Instance_CrossAxisAlignment
    //     0xad1c70: add             x1, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xad1c74: ldr             x1, [x1, #0x68]
    // 0xad1c78: StoreField: r0->field_1b = r1
    //     0xad1c78: stur            w1, [x0, #0x1b]
    // 0xad1c7c: r1 = Instance_VerticalDirection
    //     0xad1c7c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad1c80: ldr             x1, [x1, #0x748]
    // 0xad1c84: StoreField: r0->field_23 = r1
    //     0xad1c84: stur            w1, [x0, #0x23]
    // 0xad1c88: r1 = Instance_Clip
    //     0xad1c88: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad1c8c: ldr             x1, [x1, #0x750]
    // 0xad1c90: StoreField: r0->field_2b = r1
    //     0xad1c90: stur            w1, [x0, #0x2b]
    // 0xad1c94: StoreField: r0->field_2f = rZR
    //     0xad1c94: stur            xzr, [x0, #0x2f]
    // 0xad1c98: ldur            x1, [fp, #-0x10]
    // 0xad1c9c: StoreField: r0->field_b = r1
    //     0xad1c9c: stur            w1, [x0, #0xb]
    // 0xad1ca0: LeaveFrame
    //     0xad1ca0: mov             SP, fp
    //     0xad1ca4: ldp             fp, lr, [SP], #0x10
    // 0xad1ca8: ret
    //     0xad1ca8: ret             
    // 0xad1cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1cac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1cb0: b               #0xad1af4
  }
  [closure] NTagButton <anonymous closure>(dynamic, Tag) {
    // ** addr: 0xad1cb4, size: 0x84
    // 0xad1cb4: EnterFrame
    //     0xad1cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xad1cb8: mov             fp, SP
    // 0xad1cbc: AllocStack(0x18)
    //     0xad1cbc: sub             SP, SP, #0x18
    // 0xad1cc0: SetupParameters()
    //     0xad1cc0: ldr             x0, [fp, #0x18]
    //     0xad1cc4: ldur            w1, [x0, #0x17]
    //     0xad1cc8: add             x1, x1, HEAP, lsl #32
    //     0xad1ccc: stur            x1, [fp, #-8]
    // 0xad1cd0: r1 = 1
    //     0xad1cd0: movz            x1, #0x1
    // 0xad1cd4: r0 = AllocateContext()
    //     0xad1cd4: bl              #0xec126c  ; AllocateContextStub
    // 0xad1cd8: mov             x1, x0
    // 0xad1cdc: ldur            x0, [fp, #-8]
    // 0xad1ce0: stur            x1, [fp, #-0x10]
    // 0xad1ce4: StoreField: r1->field_b = r0
    //     0xad1ce4: stur            w0, [x1, #0xb]
    // 0xad1ce8: ldr             x0, [fp, #0x10]
    // 0xad1cec: StoreField: r1->field_f = r0
    //     0xad1cec: stur            w0, [x1, #0xf]
    // 0xad1cf0: LoadField: r2 = r0->field_f
    //     0xad1cf0: ldur            w2, [x0, #0xf]
    // 0xad1cf4: DecompressPointer r2
    //     0xad1cf4: add             x2, x2, HEAP, lsl #32
    // 0xad1cf8: stur            x2, [fp, #-8]
    // 0xad1cfc: r0 = NTagButton()
    //     0xad1cfc: bl              #0xa36a20  ; AllocateNTagButtonStub -> NTagButton (size=0x18)
    // 0xad1d00: mov             x3, x0
    // 0xad1d04: ldur            x0, [fp, #-8]
    // 0xad1d08: stur            x3, [fp, #-0x18]
    // 0xad1d0c: StoreField: r3->field_b = r0
    //     0xad1d0c: stur            w0, [x3, #0xb]
    // 0xad1d10: ldur            x2, [fp, #-0x10]
    // 0xad1d14: r1 = Function '<anonymous closure>':.
    //     0xad1d14: add             x1, PP, #0x30, lsl #12  ; [pp+0x30978] AnonymousClosure: (0xad1d38), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchRecomendation (0xad1ad8)
    //     0xad1d18: ldr             x1, [x1, #0x978]
    // 0xad1d1c: r0 = AllocateClosure()
    //     0xad1d1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad1d20: mov             x1, x0
    // 0xad1d24: ldur            x0, [fp, #-0x18]
    // 0xad1d28: StoreField: r0->field_f = r1
    //     0xad1d28: stur            w1, [x0, #0xf]
    // 0xad1d2c: LeaveFrame
    //     0xad1d2c: mov             SP, fp
    //     0xad1d30: ldp             fp, lr, [SP], #0x10
    // 0xad1d34: ret
    //     0xad1d34: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad1d38, size: 0x74
    // 0xad1d38: EnterFrame
    //     0xad1d38: stp             fp, lr, [SP, #-0x10]!
    //     0xad1d3c: mov             fp, SP
    // 0xad1d40: AllocStack(0x8)
    //     0xad1d40: sub             SP, SP, #8
    // 0xad1d44: SetupParameters()
    //     0xad1d44: ldr             x0, [fp, #0x10]
    //     0xad1d48: ldur            w2, [x0, #0x17]
    //     0xad1d4c: add             x2, x2, HEAP, lsl #32
    //     0xad1d50: stur            x2, [fp, #-8]
    // 0xad1d54: CheckStackOverflow
    //     0xad1d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad1d58: cmp             SP, x16
    //     0xad1d5c: b.ls            #0xad1da4
    // 0xad1d60: LoadField: r0 = r2->field_b
    //     0xad1d60: ldur            w0, [x2, #0xb]
    // 0xad1d64: DecompressPointer r0
    //     0xad1d64: add             x0, x0, HEAP, lsl #32
    // 0xad1d68: LoadField: r1 = r0->field_f
    //     0xad1d68: ldur            w1, [x0, #0xf]
    // 0xad1d6c: DecompressPointer r1
    //     0xad1d6c: add             x1, x1, HEAP, lsl #32
    // 0xad1d70: r0 = controller()
    //     0xad1d70: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad1d74: mov             x1, x0
    // 0xad1d78: ldur            x0, [fp, #-8]
    // 0xad1d7c: LoadField: r2 = r0->field_f
    //     0xad1d7c: ldur            w2, [x0, #0xf]
    // 0xad1d80: DecompressPointer r2
    //     0xad1d80: add             x2, x2, HEAP, lsl #32
    // 0xad1d84: LoadField: r0 = r2->field_f
    //     0xad1d84: ldur            w0, [x2, #0xf]
    // 0xad1d88: DecompressPointer r0
    //     0xad1d88: add             x0, x0, HEAP, lsl #32
    // 0xad1d8c: mov             x2, x0
    // 0xad1d90: r0 = search()
    //     0xad1d90: bl              #0xad1dac  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::search
    // 0xad1d94: r0 = Null
    //     0xad1d94: mov             x0, NULL
    // 0xad1d98: LeaveFrame
    //     0xad1d98: mov             SP, fp
    //     0xad1d9c: ldp             fp, lr, [SP], #0x10
    // 0xad1da0: ret
    //     0xad1da0: ret             
    // 0xad1da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad1da4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad1da8: b               #0xad1d60
  }
  _ buildSearchHistory(/* No info */) {
    // ** addr: 0xad26b4, size: 0x390
    // 0xad26b4: EnterFrame
    //     0xad26b4: stp             fp, lr, [SP, #-0x10]!
    //     0xad26b8: mov             fp, SP
    // 0xad26bc: AllocStack(0x40)
    //     0xad26bc: sub             SP, SP, #0x40
    // 0xad26c0: SetupParameters(ArticleSearchView this /* r1 => r1, fp-0x8 */)
    //     0xad26c0: stur            x1, [fp, #-8]
    // 0xad26c4: CheckStackOverflow
    //     0xad26c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad26c8: cmp             SP, x16
    //     0xad26cc: b.ls            #0xad2a3c
    // 0xad26d0: r1 = 1
    //     0xad26d0: movz            x1, #0x1
    // 0xad26d4: r0 = AllocateContext()
    //     0xad26d4: bl              #0xec126c  ; AllocateContextStub
    // 0xad26d8: mov             x2, x0
    // 0xad26dc: ldur            x0, [fp, #-8]
    // 0xad26e0: stur            x2, [fp, #-0x10]
    // 0xad26e4: StoreField: r2->field_f = r0
    //     0xad26e4: stur            w0, [x2, #0xf]
    // 0xad26e8: mov             x1, x0
    // 0xad26ec: r0 = controller()
    //     0xad26ec: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad26f0: LoadField: r1 = r0->field_4b
    //     0xad26f0: ldur            w1, [x0, #0x4b]
    // 0xad26f4: DecompressPointer r1
    //     0xad26f4: add             x1, x1, HEAP, lsl #32
    // 0xad26f8: r0 = value()
    //     0xad26f8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad26fc: r1 = LoadClassIdInstr(r0)
    //     0xad26fc: ldur            x1, [x0, #-1]
    //     0xad2700: ubfx            x1, x1, #0xc, #0x14
    // 0xad2704: str             x0, [SP]
    // 0xad2708: mov             x0, x1
    // 0xad270c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad270c: movz            x17, #0xc834
    //     0xad2710: add             lr, x0, x17
    //     0xad2714: ldr             lr, [x21, lr, lsl #3]
    //     0xad2718: blr             lr
    // 0xad271c: cbnz            w0, #0xad2734
    // 0xad2720: r0 = Instance_SizedBox
    //     0xad2720: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad2724: ldr             x0, [x0, #0xc40]
    // 0xad2728: LeaveFrame
    //     0xad2728: mov             SP, fp
    //     0xad272c: ldp             fp, lr, [SP], #0x10
    // 0xad2730: ret
    //     0xad2730: ret             
    // 0xad2734: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad2734: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad2738: ldr             x0, [x0, #0x2670]
    //     0xad273c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad2740: cmp             w0, w16
    //     0xad2744: b.ne            #0xad2750
    //     0xad2748: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad274c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad2750: r0 = GetNavigation.textTheme()
    //     0xad2750: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xad2754: LoadField: r3 = r0->field_2f
    //     0xad2754: ldur            w3, [x0, #0x2f]
    // 0xad2758: DecompressPointer r3
    //     0xad2758: add             x3, x3, HEAP, lsl #32
    // 0xad275c: stur            x3, [fp, #-0x18]
    // 0xad2760: cmp             w3, NULL
    // 0xad2764: b.ne            #0xad2770
    // 0xad2768: r0 = Null
    //     0xad2768: mov             x0, NULL
    // 0xad276c: b               #0xad27b4
    // 0xad2770: r1 = _ConstMap len:3
    //     0xad2770: add             x1, PP, #0x23, lsl #12  ; [pp+0x23cd0] Map<int, Color>(3)
    //     0xad2774: ldr             x1, [x1, #0xcd0]
    // 0xad2778: r2 = 4
    //     0xad2778: movz            x2, #0x4
    // 0xad277c: r0 = []()
    //     0xad277c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xad2780: r16 = <Color?>
    //     0xad2780: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xad2784: ldr             x16, [x16, #0x98]
    // 0xad2788: stp             x0, x16, [SP, #8]
    // 0xad278c: r16 = Instance_MaterialColor
    //     0xad278c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e40] Obj!MaterialColor@e2bbf1
    //     0xad2790: ldr             x16, [x16, #0xe40]
    // 0xad2794: str             x16, [SP]
    // 0xad2798: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xad2798: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xad279c: r0 = mode()
    //     0xad279c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xad27a0: str             x0, [SP]
    // 0xad27a4: ldur            x1, [fp, #-0x18]
    // 0xad27a8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xad27a8: add             x4, PP, #0x1d, lsl #12  ; [pp+0x1d228] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xad27ac: ldr             x4, [x4, #0x228]
    // 0xad27b0: r0 = copyWith()
    //     0xad27b0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad27b4: stur            x0, [fp, #-0x18]
    // 0xad27b8: r0 = Text()
    //     0xad27b8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad27bc: mov             x1, x0
    // 0xad27c0: r0 = "Hapus Semua"
    //     0xad27c0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29158] "Hapus Semua"
    //     0xad27c4: ldr             x0, [x0, #0x158]
    // 0xad27c8: stur            x1, [fp, #-0x20]
    // 0xad27cc: StoreField: r1->field_b = r0
    //     0xad27cc: stur            w0, [x1, #0xb]
    // 0xad27d0: ldur            x0, [fp, #-0x18]
    // 0xad27d4: StoreField: r1->field_13 = r0
    //     0xad27d4: stur            w0, [x1, #0x13]
    // 0xad27d8: r0 = GestureDetector()
    //     0xad27d8: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xad27dc: ldur            x2, [fp, #-0x10]
    // 0xad27e0: r1 = Function '<anonymous closure>':.
    //     0xad27e0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30998] AnonymousClosure: (0xad2d2c), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchHistory (0xad26b4)
    //     0xad27e4: ldr             x1, [x1, #0x998]
    // 0xad27e8: stur            x0, [fp, #-0x18]
    // 0xad27ec: r0 = AllocateClosure()
    //     0xad27ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xad27f0: ldur            x16, [fp, #-0x20]
    // 0xad27f4: stp             x16, x0, [SP]
    // 0xad27f8: ldur            x1, [fp, #-0x18]
    // 0xad27fc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xad27fc: add             x4, PP, #0x25, lsl #12  ; [pp+0x257d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xad2800: ldr             x4, [x4, #0x7d0]
    // 0xad2804: r0 = GestureDetector()
    //     0xad2804: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xad2808: r1 = Null
    //     0xad2808: mov             x1, NULL
    // 0xad280c: r2 = 4
    //     0xad280c: movz            x2, #0x4
    // 0xad2810: r0 = AllocateArray()
    //     0xad2810: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad2814: stur            x0, [fp, #-0x20]
    // 0xad2818: r16 = Instance_Expanded
    //     0xad2818: add             x16, PP, #0x29, lsl #12  ; [pp+0x29168] Obj!Expanded@e1f401
    //     0xad281c: ldr             x16, [x16, #0x168]
    // 0xad2820: StoreField: r0->field_f = r16
    //     0xad2820: stur            w16, [x0, #0xf]
    // 0xad2824: ldur            x1, [fp, #-0x18]
    // 0xad2828: StoreField: r0->field_13 = r1
    //     0xad2828: stur            w1, [x0, #0x13]
    // 0xad282c: r1 = <Widget>
    //     0xad282c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad2830: r0 = AllocateGrowableArray()
    //     0xad2830: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad2834: mov             x1, x0
    // 0xad2838: ldur            x0, [fp, #-0x20]
    // 0xad283c: stur            x1, [fp, #-0x18]
    // 0xad2840: StoreField: r1->field_f = r0
    //     0xad2840: stur            w0, [x1, #0xf]
    // 0xad2844: r0 = 4
    //     0xad2844: movz            x0, #0x4
    // 0xad2848: StoreField: r1->field_b = r0
    //     0xad2848: stur            w0, [x1, #0xb]
    // 0xad284c: r0 = Row()
    //     0xad284c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xad2850: mov             x2, x0
    // 0xad2854: r0 = Instance_Axis
    //     0xad2854: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xad2858: stur            x2, [fp, #-0x20]
    // 0xad285c: StoreField: r2->field_f = r0
    //     0xad285c: stur            w0, [x2, #0xf]
    // 0xad2860: r0 = Instance_MainAxisAlignment
    //     0xad2860: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad2864: ldr             x0, [x0, #0x730]
    // 0xad2868: StoreField: r2->field_13 = r0
    //     0xad2868: stur            w0, [x2, #0x13]
    // 0xad286c: r3 = Instance_MainAxisSize
    //     0xad286c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad2870: ldr             x3, [x3, #0x738]
    // 0xad2874: ArrayStore: r2[0] = r3  ; List_4
    //     0xad2874: stur            w3, [x2, #0x17]
    // 0xad2878: r4 = Instance_CrossAxisAlignment
    //     0xad2878: add             x4, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad287c: ldr             x4, [x4, #0x740]
    // 0xad2880: StoreField: r2->field_1b = r4
    //     0xad2880: stur            w4, [x2, #0x1b]
    // 0xad2884: r5 = Instance_VerticalDirection
    //     0xad2884: add             x5, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad2888: ldr             x5, [x5, #0x748]
    // 0xad288c: StoreField: r2->field_23 = r5
    //     0xad288c: stur            w5, [x2, #0x23]
    // 0xad2890: r6 = Instance_Clip
    //     0xad2890: add             x6, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad2894: ldr             x6, [x6, #0x750]
    // 0xad2898: StoreField: r2->field_2b = r6
    //     0xad2898: stur            w6, [x2, #0x2b]
    // 0xad289c: StoreField: r2->field_2f = rZR
    //     0xad289c: stur            xzr, [x2, #0x2f]
    // 0xad28a0: ldur            x1, [fp, #-0x18]
    // 0xad28a4: StoreField: r2->field_b = r1
    //     0xad28a4: stur            w1, [x2, #0xb]
    // 0xad28a8: ldur            x1, [fp, #-8]
    // 0xad28ac: r0 = controller()
    //     0xad28ac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad28b0: LoadField: r1 = r0->field_4b
    //     0xad28b0: ldur            w1, [x0, #0x4b]
    // 0xad28b4: DecompressPointer r1
    //     0xad28b4: add             x1, x1, HEAP, lsl #32
    // 0xad28b8: r0 = value()
    //     0xad28b8: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad28bc: r1 = LoadClassIdInstr(r0)
    //     0xad28bc: ldur            x1, [x0, #-1]
    //     0xad28c0: ubfx            x1, x1, #0xc, #0x14
    // 0xad28c4: str             x0, [SP]
    // 0xad28c8: mov             x0, x1
    // 0xad28cc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xad28cc: movz            x17, #0xc834
    //     0xad28d0: add             lr, x0, x17
    //     0xad28d4: ldr             lr, [x21, lr, lsl #3]
    //     0xad28d8: blr             lr
    // 0xad28dc: r3 = LoadInt32Instr(r0)
    //     0xad28dc: sbfx            x3, x0, #1, #0x1f
    //     0xad28e0: tbz             w0, #0, #0xad28e8
    //     0xad28e4: ldur            x3, [x0, #7]
    // 0xad28e8: ldur            x2, [fp, #-0x10]
    // 0xad28ec: stur            x3, [fp, #-0x28]
    // 0xad28f0: r1 = Function '<anonymous closure>':.
    //     0xad28f0: add             x1, PP, #0x30, lsl #12  ; [pp+0x309a0] AnonymousClosure: (0xad2a44), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchHistory (0xad26b4)
    //     0xad28f4: ldr             x1, [x1, #0x9a0]
    // 0xad28f8: r0 = AllocateClosure()
    //     0xad28f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad28fc: r1 = Function '<anonymous closure>':.
    //     0xad28fc: add             x1, PP, #0x30, lsl #12  ; [pp+0x309a8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad2900: ldr             x1, [x1, #0x9a8]
    // 0xad2904: r2 = Null
    //     0xad2904: mov             x2, NULL
    // 0xad2908: stur            x0, [fp, #-8]
    // 0xad290c: r0 = AllocateClosure()
    //     0xad290c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad2910: stur            x0, [fp, #-0x10]
    // 0xad2914: r0 = ListView()
    //     0xad2914: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad2918: stur            x0, [fp, #-0x18]
    // 0xad291c: r16 = true
    //     0xad291c: add             x16, NULL, #0x20  ; true
    // 0xad2920: r30 = Instance_NeverScrollableScrollPhysics
    //     0xad2920: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xad2924: ldr             lr, [lr, #0x290]
    // 0xad2928: stp             lr, x16, [SP]
    // 0xad292c: mov             x1, x0
    // 0xad2930: ldur            x2, [fp, #-8]
    // 0xad2934: ldur            x3, [fp, #-0x28]
    // 0xad2938: ldur            x5, [fp, #-0x10]
    // 0xad293c: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xad293c: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xad2940: ldr             x4, [x4, #0x298]
    // 0xad2944: r0 = ListView.separated()
    //     0xad2944: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad2948: r0 = ListTileTheme()
    //     0xad2948: bl              #0x9f0a04  ; AllocateListTileThemeStub -> ListTileTheme (size=0x50)
    // 0xad294c: mov             x3, x0
    // 0xad2950: r0 = Instance_EdgeInsets
    //     0xad2950: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad2954: stur            x3, [fp, #-8]
    // 0xad2958: StoreField: r3->field_2b = r0
    //     0xad2958: stur            w0, [x3, #0x2b]
    // 0xad295c: r0 = 0.000000
    //     0xad295c: ldr             x0, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xad2960: StoreField: r3->field_37 = r0
    //     0xad2960: stur            w0, [x3, #0x37]
    // 0xad2964: ldur            x0, [fp, #-0x18]
    // 0xad2968: StoreField: r3->field_b = r0
    //     0xad2968: stur            w0, [x3, #0xb]
    // 0xad296c: r1 = Null
    //     0xad296c: mov             x1, NULL
    // 0xad2970: r2 = 12
    //     0xad2970: movz            x2, #0xc
    // 0xad2974: r0 = AllocateArray()
    //     0xad2974: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad2978: stur            x0, [fp, #-0x10]
    // 0xad297c: r16 = Instance_SizedBox
    //     0xad297c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bcd8] Obj!SizedBox@e1e1c1
    //     0xad2980: ldr             x16, [x16, #0xcd8]
    // 0xad2984: StoreField: r0->field_f = r16
    //     0xad2984: stur            w16, [x0, #0xf]
    // 0xad2988: ldur            x1, [fp, #-0x20]
    // 0xad298c: StoreField: r0->field_13 = r1
    //     0xad298c: stur            w1, [x0, #0x13]
    // 0xad2990: r16 = Instance_SizedBox
    //     0xad2990: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2bce0] Obj!SizedBox@e1e1a1
    //     0xad2994: ldr             x16, [x16, #0xce0]
    // 0xad2998: ArrayStore: r0[0] = r16  ; List_4
    //     0xad2998: stur            w16, [x0, #0x17]
    // 0xad299c: r16 = Instance_Divider
    //     0xad299c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xad29a0: ldr             x16, [x16, #0xc28]
    // 0xad29a4: StoreField: r0->field_1b = r16
    //     0xad29a4: stur            w16, [x0, #0x1b]
    // 0xad29a8: ldur            x1, [fp, #-8]
    // 0xad29ac: StoreField: r0->field_1f = r1
    //     0xad29ac: stur            w1, [x0, #0x1f]
    // 0xad29b0: r16 = Instance_SizedBox
    //     0xad29b0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xad29b4: ldr             x16, [x16, #0x540]
    // 0xad29b8: StoreField: r0->field_23 = r16
    //     0xad29b8: stur            w16, [x0, #0x23]
    // 0xad29bc: r1 = <Widget>
    //     0xad29bc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad29c0: r0 = AllocateGrowableArray()
    //     0xad29c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad29c4: mov             x1, x0
    // 0xad29c8: ldur            x0, [fp, #-0x10]
    // 0xad29cc: stur            x1, [fp, #-8]
    // 0xad29d0: StoreField: r1->field_f = r0
    //     0xad29d0: stur            w0, [x1, #0xf]
    // 0xad29d4: r0 = 12
    //     0xad29d4: movz            x0, #0xc
    // 0xad29d8: StoreField: r1->field_b = r0
    //     0xad29d8: stur            w0, [x1, #0xb]
    // 0xad29dc: r0 = Column()
    //     0xad29dc: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad29e0: r1 = Instance_Axis
    //     0xad29e0: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad29e4: StoreField: r0->field_f = r1
    //     0xad29e4: stur            w1, [x0, #0xf]
    // 0xad29e8: r1 = Instance_MainAxisAlignment
    //     0xad29e8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad29ec: ldr             x1, [x1, #0x730]
    // 0xad29f0: StoreField: r0->field_13 = r1
    //     0xad29f0: stur            w1, [x0, #0x13]
    // 0xad29f4: r1 = Instance_MainAxisSize
    //     0xad29f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad29f8: ldr             x1, [x1, #0x738]
    // 0xad29fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xad29fc: stur            w1, [x0, #0x17]
    // 0xad2a00: r1 = Instance_CrossAxisAlignment
    //     0xad2a00: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad2a04: ldr             x1, [x1, #0x740]
    // 0xad2a08: StoreField: r0->field_1b = r1
    //     0xad2a08: stur            w1, [x0, #0x1b]
    // 0xad2a0c: r1 = Instance_VerticalDirection
    //     0xad2a0c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad2a10: ldr             x1, [x1, #0x748]
    // 0xad2a14: StoreField: r0->field_23 = r1
    //     0xad2a14: stur            w1, [x0, #0x23]
    // 0xad2a18: r1 = Instance_Clip
    //     0xad2a18: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad2a1c: ldr             x1, [x1, #0x750]
    // 0xad2a20: StoreField: r0->field_2b = r1
    //     0xad2a20: stur            w1, [x0, #0x2b]
    // 0xad2a24: StoreField: r0->field_2f = rZR
    //     0xad2a24: stur            xzr, [x0, #0x2f]
    // 0xad2a28: ldur            x1, [fp, #-8]
    // 0xad2a2c: StoreField: r0->field_b = r1
    //     0xad2a2c: stur            w1, [x0, #0xb]
    // 0xad2a30: LeaveFrame
    //     0xad2a30: mov             SP, fp
    //     0xad2a34: ldp             fp, lr, [SP], #0x10
    // 0xad2a38: ret
    //     0xad2a38: ret             
    // 0xad2a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2a3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2a40: b               #0xad26d0
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad2a44, size: 0x1cc
    // 0xad2a44: EnterFrame
    //     0xad2a44: stp             fp, lr, [SP, #-0x10]!
    //     0xad2a48: mov             fp, SP
    // 0xad2a4c: AllocStack(0x30)
    //     0xad2a4c: sub             SP, SP, #0x30
    // 0xad2a50: SetupParameters()
    //     0xad2a50: ldr             x0, [fp, #0x20]
    //     0xad2a54: ldur            w1, [x0, #0x17]
    //     0xad2a58: add             x1, x1, HEAP, lsl #32
    //     0xad2a5c: stur            x1, [fp, #-8]
    // 0xad2a60: CheckStackOverflow
    //     0xad2a60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2a64: cmp             SP, x16
    //     0xad2a68: b.ls            #0xad2c08
    // 0xad2a6c: r1 = 2
    //     0xad2a6c: movz            x1, #0x2
    // 0xad2a70: r0 = AllocateContext()
    //     0xad2a70: bl              #0xec126c  ; AllocateContextStub
    // 0xad2a74: mov             x2, x0
    // 0xad2a78: ldur            x0, [fp, #-8]
    // 0xad2a7c: stur            x2, [fp, #-0x10]
    // 0xad2a80: StoreField: r2->field_b = r0
    //     0xad2a80: stur            w0, [x2, #0xb]
    // 0xad2a84: ldr             x3, [fp, #0x10]
    // 0xad2a88: StoreField: r2->field_f = r3
    //     0xad2a88: stur            w3, [x2, #0xf]
    // 0xad2a8c: LoadField: r1 = r0->field_f
    //     0xad2a8c: ldur            w1, [x0, #0xf]
    // 0xad2a90: DecompressPointer r1
    //     0xad2a90: add             x1, x1, HEAP, lsl #32
    // 0xad2a94: r0 = controller()
    //     0xad2a94: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad2a98: LoadField: r1 = r0->field_4b
    //     0xad2a98: ldur            w1, [x0, #0x4b]
    // 0xad2a9c: DecompressPointer r1
    //     0xad2a9c: add             x1, x1, HEAP, lsl #32
    // 0xad2aa0: r0 = value()
    //     0xad2aa0: bl              #0xd60e94  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0xad2aa4: r1 = LoadClassIdInstr(r0)
    //     0xad2aa4: ldur            x1, [x0, #-1]
    //     0xad2aa8: ubfx            x1, x1, #0xc, #0x14
    // 0xad2aac: ldr             x16, [fp, #0x10]
    // 0xad2ab0: stp             x16, x0, [SP]
    // 0xad2ab4: mov             x0, x1
    // 0xad2ab8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xad2ab8: movz            x17, #0x3037
    //     0xad2abc: movk            x17, #0x1, lsl #16
    //     0xad2ac0: add             lr, x0, x17
    //     0xad2ac4: ldr             lr, [x21, lr, lsl #3]
    //     0xad2ac8: blr             lr
    // 0xad2acc: mov             x1, x0
    // 0xad2ad0: ldur            x2, [fp, #-0x10]
    // 0xad2ad4: stur            x1, [fp, #-8]
    // 0xad2ad8: StoreField: r2->field_13 = r0
    //     0xad2ad8: stur            w0, [x2, #0x13]
    //     0xad2adc: tbz             w0, #0, #0xad2af8
    //     0xad2ae0: ldurb           w16, [x2, #-1]
    //     0xad2ae4: ldurb           w17, [x0, #-1]
    //     0xad2ae8: and             x16, x17, x16, lsr #2
    //     0xad2aec: tst             x16, HEAP, lsr #32
    //     0xad2af0: b.eq            #0xad2af8
    //     0xad2af4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xad2af8: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad2af8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad2afc: ldr             x0, [x0, #0x2670]
    //     0xad2b00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad2b04: cmp             w0, w16
    //     0xad2b08: b.ne            #0xad2b14
    //     0xad2b0c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad2b10: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad2b14: r0 = GetNavigation.textTheme()
    //     0xad2b14: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xad2b18: LoadField: r1 = r0->field_23
    //     0xad2b18: ldur            w1, [x0, #0x23]
    // 0xad2b1c: DecompressPointer r1
    //     0xad2b1c: add             x1, x1, HEAP, lsl #32
    // 0xad2b20: stur            x1, [fp, #-0x18]
    // 0xad2b24: r0 = Text()
    //     0xad2b24: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad2b28: mov             x3, x0
    // 0xad2b2c: ldur            x0, [fp, #-8]
    // 0xad2b30: stur            x3, [fp, #-0x20]
    // 0xad2b34: StoreField: r3->field_b = r0
    //     0xad2b34: stur            w0, [x3, #0xb]
    // 0xad2b38: ldur            x0, [fp, #-0x18]
    // 0xad2b3c: StoreField: r3->field_13 = r0
    //     0xad2b3c: stur            w0, [x3, #0x13]
    // 0xad2b40: ldur            x2, [fp, #-0x10]
    // 0xad2b44: r1 = Function '<anonymous closure>':.
    //     0xad2b44: add             x1, PP, #0x30, lsl #12  ; [pp+0x309b0] AnonymousClosure: (0xad2c78), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchHistory (0xad26b4)
    //     0xad2b48: ldr             x1, [x1, #0x9b0]
    // 0xad2b4c: r0 = AllocateClosure()
    //     0xad2b4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad2b50: stur            x0, [fp, #-8]
    // 0xad2b54: r0 = IconButton()
    //     0xad2b54: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad2b58: mov             x1, x0
    // 0xad2b5c: ldur            x0, [fp, #-8]
    // 0xad2b60: stur            x1, [fp, #-0x18]
    // 0xad2b64: StoreField: r1->field_3b = r0
    //     0xad2b64: stur            w0, [x1, #0x3b]
    // 0xad2b68: r0 = false
    //     0xad2b68: add             x0, NULL, #0x30  ; false
    // 0xad2b6c: StoreField: r1->field_47 = r0
    //     0xad2b6c: stur            w0, [x1, #0x47]
    // 0xad2b70: r2 = Instance_Icon
    //     0xad2b70: add             x2, PP, #0x29, lsl #12  ; [pp+0x291a0] Obj!Icon@e24331
    //     0xad2b74: ldr             x2, [x2, #0x1a0]
    // 0xad2b78: StoreField: r1->field_1f = r2
    //     0xad2b78: stur            w2, [x1, #0x1f]
    // 0xad2b7c: r2 = Instance__IconButtonVariant
    //     0xad2b7c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad2b80: ldr             x2, [x2, #0xf78]
    // 0xad2b84: StoreField: r1->field_63 = r2
    //     0xad2b84: stur            w2, [x1, #0x63]
    // 0xad2b88: r0 = ListTile()
    //     0xad2b88: bl              #0x624c8c  ; AllocateListTileStub -> ListTile (size=0x9c)
    // 0xad2b8c: mov             x3, x0
    // 0xad2b90: r0 = Instance_Icon
    //     0xad2b90: add             x0, PP, #0x29, lsl #12  ; [pp+0x291a8] Obj!Icon@e242f1
    //     0xad2b94: ldr             x0, [x0, #0x1a8]
    // 0xad2b98: stur            x3, [fp, #-8]
    // 0xad2b9c: StoreField: r3->field_b = r0
    //     0xad2b9c: stur            w0, [x3, #0xb]
    // 0xad2ba0: ldur            x0, [fp, #-0x20]
    // 0xad2ba4: StoreField: r3->field_f = r0
    //     0xad2ba4: stur            w0, [x3, #0xf]
    // 0xad2ba8: ldur            x0, [fp, #-0x18]
    // 0xad2bac: ArrayStore: r3[0] = r0  ; List_4
    //     0xad2bac: stur            w0, [x3, #0x17]
    // 0xad2bb0: r0 = false
    //     0xad2bb0: add             x0, NULL, #0x30  ; false
    // 0xad2bb4: StoreField: r3->field_1b = r0
    //     0xad2bb4: stur            w0, [x3, #0x1b]
    // 0xad2bb8: r4 = true
    //     0xad2bb8: add             x4, NULL, #0x20  ; true
    // 0xad2bbc: StoreField: r3->field_4b = r4
    //     0xad2bbc: stur            w4, [x3, #0x4b]
    // 0xad2bc0: ldur            x2, [fp, #-0x10]
    // 0xad2bc4: r1 = Function '<anonymous closure>':.
    //     0xad2bc4: add             x1, PP, #0x30, lsl #12  ; [pp+0x309b8] AnonymousClosure: (0xad2c10), in [package:nuonline/app/modules/article/article_search/views/article_search_view.dart] ArticleSearchView::buildSearchHistory (0xad26b4)
    //     0xad2bc8: ldr             x1, [x1, #0x9b8]
    // 0xad2bcc: r0 = AllocateClosure()
    //     0xad2bcc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad2bd0: mov             x1, x0
    // 0xad2bd4: ldur            x0, [fp, #-8]
    // 0xad2bd8: StoreField: r0->field_4f = r1
    //     0xad2bd8: stur            w1, [x0, #0x4f]
    // 0xad2bdc: r1 = false
    //     0xad2bdc: add             x1, NULL, #0x30  ; false
    // 0xad2be0: StoreField: r0->field_5f = r1
    //     0xad2be0: stur            w1, [x0, #0x5f]
    // 0xad2be4: StoreField: r0->field_73 = r1
    //     0xad2be4: stur            w1, [x0, #0x73]
    // 0xad2be8: r1 = 40.000000
    //     0xad2be8: add             x1, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xad2bec: ldr             x1, [x1, #0x1b8]
    // 0xad2bf0: StoreField: r0->field_8b = r1
    //     0xad2bf0: stur            w1, [x0, #0x8b]
    // 0xad2bf4: r1 = true
    //     0xad2bf4: add             x1, NULL, #0x20  ; true
    // 0xad2bf8: StoreField: r0->field_97 = r1
    //     0xad2bf8: stur            w1, [x0, #0x97]
    // 0xad2bfc: LeaveFrame
    //     0xad2bfc: mov             SP, fp
    //     0xad2c00: ldp             fp, lr, [SP], #0x10
    // 0xad2c04: ret
    //     0xad2c04: ret             
    // 0xad2c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2c08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2c0c: b               #0xad2a6c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad2c10, size: 0x68
    // 0xad2c10: EnterFrame
    //     0xad2c10: stp             fp, lr, [SP, #-0x10]!
    //     0xad2c14: mov             fp, SP
    // 0xad2c18: AllocStack(0x8)
    //     0xad2c18: sub             SP, SP, #8
    // 0xad2c1c: SetupParameters()
    //     0xad2c1c: ldr             x0, [fp, #0x10]
    //     0xad2c20: ldur            w2, [x0, #0x17]
    //     0xad2c24: add             x2, x2, HEAP, lsl #32
    //     0xad2c28: stur            x2, [fp, #-8]
    // 0xad2c2c: CheckStackOverflow
    //     0xad2c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2c30: cmp             SP, x16
    //     0xad2c34: b.ls            #0xad2c70
    // 0xad2c38: LoadField: r0 = r2->field_b
    //     0xad2c38: ldur            w0, [x2, #0xb]
    // 0xad2c3c: DecompressPointer r0
    //     0xad2c3c: add             x0, x0, HEAP, lsl #32
    // 0xad2c40: LoadField: r1 = r0->field_f
    //     0xad2c40: ldur            w1, [x0, #0xf]
    // 0xad2c44: DecompressPointer r1
    //     0xad2c44: add             x1, x1, HEAP, lsl #32
    // 0xad2c48: r0 = controller()
    //     0xad2c48: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad2c4c: mov             x1, x0
    // 0xad2c50: ldur            x0, [fp, #-8]
    // 0xad2c54: LoadField: r2 = r0->field_13
    //     0xad2c54: ldur            w2, [x0, #0x13]
    // 0xad2c58: DecompressPointer r2
    //     0xad2c58: add             x2, x2, HEAP, lsl #32
    // 0xad2c5c: r0 = search()
    //     0xad2c5c: bl              #0xad1dac  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::search
    // 0xad2c60: r0 = Null
    //     0xad2c60: mov             x0, NULL
    // 0xad2c64: LeaveFrame
    //     0xad2c64: mov             SP, fp
    //     0xad2c68: ldp             fp, lr, [SP], #0x10
    // 0xad2c6c: ret
    //     0xad2c6c: ret             
    // 0xad2c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2c70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2c74: b               #0xad2c38
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad2c78, size: 0x78
    // 0xad2c78: EnterFrame
    //     0xad2c78: stp             fp, lr, [SP, #-0x10]!
    //     0xad2c7c: mov             fp, SP
    // 0xad2c80: AllocStack(0x8)
    //     0xad2c80: sub             SP, SP, #8
    // 0xad2c84: SetupParameters()
    //     0xad2c84: ldr             x0, [fp, #0x10]
    //     0xad2c88: ldur            w2, [x0, #0x17]
    //     0xad2c8c: add             x2, x2, HEAP, lsl #32
    //     0xad2c90: stur            x2, [fp, #-8]
    // 0xad2c94: CheckStackOverflow
    //     0xad2c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2c98: cmp             SP, x16
    //     0xad2c9c: b.ls            #0xad2ce8
    // 0xad2ca0: LoadField: r0 = r2->field_b
    //     0xad2ca0: ldur            w0, [x2, #0xb]
    // 0xad2ca4: DecompressPointer r0
    //     0xad2ca4: add             x0, x0, HEAP, lsl #32
    // 0xad2ca8: LoadField: r1 = r0->field_f
    //     0xad2ca8: ldur            w1, [x0, #0xf]
    // 0xad2cac: DecompressPointer r1
    //     0xad2cac: add             x1, x1, HEAP, lsl #32
    // 0xad2cb0: r0 = controller()
    //     0xad2cb0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad2cb4: mov             x1, x0
    // 0xad2cb8: ldur            x0, [fp, #-8]
    // 0xad2cbc: LoadField: r2 = r0->field_f
    //     0xad2cbc: ldur            w2, [x0, #0xf]
    // 0xad2cc0: DecompressPointer r2
    //     0xad2cc0: add             x2, x2, HEAP, lsl #32
    // 0xad2cc4: r0 = LoadInt32Instr(r2)
    //     0xad2cc4: sbfx            x0, x2, #1, #0x1f
    //     0xad2cc8: tbz             w2, #0, #0xad2cd0
    //     0xad2ccc: ldur            x0, [x2, #7]
    // 0xad2cd0: mov             x2, x0
    // 0xad2cd4: r0 = deleteHistory()
    //     0xad2cd4: bl              #0xad2cf0  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::deleteHistory
    // 0xad2cd8: r0 = Null
    //     0xad2cd8: mov             x0, NULL
    // 0xad2cdc: LeaveFrame
    //     0xad2cdc: mov             SP, fp
    //     0xad2ce0: ldp             fp, lr, [SP], #0x10
    // 0xad2ce4: ret
    //     0xad2ce4: ret             
    // 0xad2ce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2ce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2cec: b               #0xad2ca0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad2d2c, size: 0xd8
    // 0xad2d2c: EnterFrame
    //     0xad2d2c: stp             fp, lr, [SP, #-0x10]!
    //     0xad2d30: mov             fp, SP
    // 0xad2d34: AllocStack(0x20)
    //     0xad2d34: sub             SP, SP, #0x20
    // 0xad2d38: SetupParameters()
    //     0xad2d38: ldr             x0, [fp, #0x10]
    //     0xad2d3c: ldur            w1, [x0, #0x17]
    //     0xad2d40: add             x1, x1, HEAP, lsl #32
    //     0xad2d44: stur            x1, [fp, #-8]
    // 0xad2d48: CheckStackOverflow
    //     0xad2d48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2d4c: cmp             SP, x16
    //     0xad2d50: b.ls            #0xad2dfc
    // 0xad2d54: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad2d54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad2d58: ldr             x0, [x0, #0x2670]
    //     0xad2d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad2d60: cmp             w0, w16
    //     0xad2d64: b.ne            #0xad2d70
    //     0xad2d68: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad2d6c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad2d70: ldur            x0, [fp, #-8]
    // 0xad2d74: LoadField: r1 = r0->field_f
    //     0xad2d74: ldur            w1, [x0, #0xf]
    // 0xad2d78: DecompressPointer r1
    //     0xad2d78: add             x1, x1, HEAP, lsl #32
    // 0xad2d7c: r0 = controller()
    //     0xad2d7c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad2d80: stur            x0, [fp, #-8]
    // 0xad2d84: r0 = NDialog()
    //     0xad2d84: bl              #0x921e38  ; AllocateNDialogStub -> NDialog (size=0x28)
    // 0xad2d88: mov             x3, x0
    // 0xad2d8c: r0 = "Hapus semua riwayat pencarian\?"
    //     0xad2d8c: add             x0, PP, #0x29, lsl #12  ; [pp+0x294c0] "Hapus semua riwayat pencarian\?"
    //     0xad2d90: ldr             x0, [x0, #0x4c0]
    // 0xad2d94: stur            x3, [fp, #-0x10]
    // 0xad2d98: StoreField: r3->field_b = r0
    //     0xad2d98: stur            w0, [x3, #0xb]
    // 0xad2d9c: ldur            x2, [fp, #-8]
    // 0xad2da0: r1 = Function 'deleteAllHistory':.
    //     0xad2da0: add             x1, PP, #0x30, lsl #12  ; [pp+0x309c0] AnonymousClosure: (0xad2e04), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::deleteAllHistory (0xad2e3c)
    //     0xad2da4: ldr             x1, [x1, #0x9c0]
    // 0xad2da8: r0 = AllocateClosure()
    //     0xad2da8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad2dac: mov             x1, x0
    // 0xad2db0: ldur            x0, [fp, #-0x10]
    // 0xad2db4: ArrayStore: r0[0] = r1  ; List_4
    //     0xad2db4: stur            w1, [x0, #0x17]
    // 0xad2db8: r1 = Function '<anonymous closure>':.
    //     0xad2db8: add             x1, PP, #0x30, lsl #12  ; [pp+0x309c8] AnonymousClosure: (0x9221a8), in [package:nuonline/app/modules/waris/controllers/waris_controller.dart] WarisController::calculate (0x922200)
    //     0xad2dbc: ldr             x1, [x1, #0x9c8]
    // 0xad2dc0: r2 = Null
    //     0xad2dc0: mov             x2, NULL
    // 0xad2dc4: r0 = AllocateClosure()
    //     0xad2dc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad2dc8: mov             x1, x0
    // 0xad2dcc: ldur            x0, [fp, #-0x10]
    // 0xad2dd0: StoreField: r0->field_1b = r1
    //     0xad2dd0: stur            w1, [x0, #0x1b]
    // 0xad2dd4: r1 = "Ya, Hapus"
    //     0xad2dd4: add             x1, PP, #0x29, lsl #12  ; [pp+0x294d8] "Ya, Hapus"
    //     0xad2dd8: ldr             x1, [x1, #0x4d8]
    // 0xad2ddc: StoreField: r0->field_13 = r1
    //     0xad2ddc: stur            w1, [x0, #0x13]
    // 0xad2de0: stp             x0, NULL, [SP]
    // 0xad2de4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad2de4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad2de8: r0 = ExtensionDialog.dialog()
    //     0xad2de8: bl              #0x91a184  ; [package:get/get_navigation/src/extension_navigation.dart] ::ExtensionDialog.dialog
    // 0xad2dec: r0 = Null
    //     0xad2dec: mov             x0, NULL
    // 0xad2df0: LeaveFrame
    //     0xad2df0: mov             SP, fp
    //     0xad2df4: ldp             fp, lr, [SP], #0x10
    // 0xad2df8: ret
    //     0xad2df8: ret             
    // 0xad2dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2e00: b               #0xad2d54
  }
  [closure] StatelessWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad2ea4, size: 0x94
    // 0xad2ea4: EnterFrame
    //     0xad2ea4: stp             fp, lr, [SP, #-0x10]!
    //     0xad2ea8: mov             fp, SP
    // 0xad2eac: AllocStack(0x8)
    //     0xad2eac: sub             SP, SP, #8
    // 0xad2eb0: SetupParameters()
    //     0xad2eb0: ldr             x0, [fp, #0x20]
    //     0xad2eb4: ldur            w1, [x0, #0x17]
    //     0xad2eb8: add             x1, x1, HEAP, lsl #32
    // 0xad2ebc: CheckStackOverflow
    //     0xad2ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad2ec0: cmp             SP, x16
    //     0xad2ec4: b.ls            #0xad2f30
    // 0xad2ec8: LoadField: r0 = r1->field_f
    //     0xad2ec8: ldur            w0, [x1, #0xf]
    // 0xad2ecc: DecompressPointer r0
    //     0xad2ecc: add             x0, x0, HEAP, lsl #32
    // 0xad2ed0: mov             x1, x0
    // 0xad2ed4: r0 = controller()
    //     0xad2ed4: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad2ed8: mov             x1, x0
    // 0xad2edc: ldr             x0, [fp, #0x10]
    // 0xad2ee0: r2 = LoadInt32Instr(r0)
    //     0xad2ee0: sbfx            x2, x0, #1, #0x1f
    //     0xad2ee4: tbz             w0, #0, #0xad2eec
    //     0xad2ee8: ldur            x2, [x0, #7]
    // 0xad2eec: r0 = find()
    //     0xad2eec: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xad2ef0: stur            x0, [fp, #-8]
    // 0xad2ef4: cmp             w0, NULL
    // 0xad2ef8: b.ne            #0xad2f10
    // 0xad2efc: r0 = Instance_NArticleListTile
    //     0xad2efc: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xad2f00: ldr             x0, [x0, #0xa90]
    // 0xad2f04: LeaveFrame
    //     0xad2f04: mov             SP, fp
    //     0xad2f08: ldp             fp, lr, [SP], #0x10
    // 0xad2f0c: ret
    //     0xad2f0c: ret             
    // 0xad2f10: r0 = ArticleItem()
    //     0xad2f10: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xad2f14: ldur            x1, [fp, #-8]
    // 0xad2f18: StoreField: r0->field_b = r1
    //     0xad2f18: stur            w1, [x0, #0xb]
    // 0xad2f1c: r1 = false
    //     0xad2f1c: add             x1, NULL, #0x30  ; false
    // 0xad2f20: StoreField: r0->field_13 = r1
    //     0xad2f20: stur            w1, [x0, #0x13]
    // 0xad2f24: LeaveFrame
    //     0xad2f24: mov             SP, fp
    //     0xad2f28: ldp             fp, lr, [SP], #0x10
    // 0xad2f2c: ret
    //     0xad2f2c: ret             
    // 0xad2f30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad2f30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad2f34: b               #0xad2ec8
  }
  [closure] NSearchTextField <anonymous closure>(dynamic) {
    // ** addr: 0xad31a8, size: 0x158
    // 0xad31a8: EnterFrame
    //     0xad31a8: stp             fp, lr, [SP, #-0x10]!
    //     0xad31ac: mov             fp, SP
    // 0xad31b0: AllocStack(0x30)
    //     0xad31b0: sub             SP, SP, #0x30
    // 0xad31b4: SetupParameters()
    //     0xad31b4: ldr             x0, [fp, #0x10]
    //     0xad31b8: ldur            w2, [x0, #0x17]
    //     0xad31bc: add             x2, x2, HEAP, lsl #32
    //     0xad31c0: stur            x2, [fp, #-8]
    // 0xad31c4: CheckStackOverflow
    //     0xad31c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad31c8: cmp             SP, x16
    //     0xad31cc: b.ls            #0xad32ec
    // 0xad31d0: LoadField: r1 = r2->field_f
    //     0xad31d0: ldur            w1, [x2, #0xf]
    // 0xad31d4: DecompressPointer r1
    //     0xad31d4: add             x1, x1, HEAP, lsl #32
    // 0xad31d8: r0 = controller()
    //     0xad31d8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad31dc: LoadField: r2 = r0->field_3f
    //     0xad31dc: ldur            w2, [x0, #0x3f]
    // 0xad31e0: DecompressPointer r2
    //     0xad31e0: add             x2, x2, HEAP, lsl #32
    // 0xad31e4: r16 = Sentinel
    //     0xad31e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad31e8: cmp             w2, w16
    // 0xad31ec: b.eq            #0xad32f4
    // 0xad31f0: ldur            x0, [fp, #-8]
    // 0xad31f4: stur            x2, [fp, #-0x10]
    // 0xad31f8: LoadField: r1 = r0->field_f
    //     0xad31f8: ldur            w1, [x0, #0xf]
    // 0xad31fc: DecompressPointer r1
    //     0xad31fc: add             x1, x1, HEAP, lsl #32
    // 0xad3200: r0 = controller()
    //     0xad3200: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3204: mov             x2, x0
    // 0xad3208: ldur            x0, [fp, #-8]
    // 0xad320c: stur            x2, [fp, #-0x18]
    // 0xad3210: LoadField: r1 = r0->field_f
    //     0xad3210: ldur            w1, [x0, #0xf]
    // 0xad3214: DecompressPointer r1
    //     0xad3214: add             x1, x1, HEAP, lsl #32
    // 0xad3218: r0 = controller()
    //     0xad3218: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad321c: LoadField: r1 = r0->field_43
    //     0xad321c: ldur            w1, [x0, #0x43]
    // 0xad3220: DecompressPointer r1
    //     0xad3220: add             x1, x1, HEAP, lsl #32
    // 0xad3224: r0 = value()
    //     0xad3224: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad3228: mov             x2, x0
    // 0xad322c: ldur            x0, [fp, #-8]
    // 0xad3230: stur            x2, [fp, #-0x20]
    // 0xad3234: LoadField: r1 = r0->field_f
    //     0xad3234: ldur            w1, [x0, #0xf]
    // 0xad3238: DecompressPointer r1
    //     0xad3238: add             x1, x1, HEAP, lsl #32
    // 0xad323c: r0 = controller()
    //     0xad323c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3240: mov             x2, x0
    // 0xad3244: ldur            x0, [fp, #-8]
    // 0xad3248: stur            x2, [fp, #-0x28]
    // 0xad324c: LoadField: r1 = r0->field_f
    //     0xad324c: ldur            w1, [x0, #0xf]
    // 0xad3250: DecompressPointer r1
    //     0xad3250: add             x1, x1, HEAP, lsl #32
    // 0xad3254: r0 = controller()
    //     0xad3254: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3258: stur            x0, [fp, #-8]
    // 0xad325c: r0 = NSearchTextField()
    //     0xad325c: bl              #0xad3300  ; AllocateNSearchTextFieldStub -> NSearchTextField (size=0x34)
    // 0xad3260: mov             x3, x0
    // 0xad3264: ldur            x0, [fp, #-0x10]
    // 0xad3268: stur            x3, [fp, #-0x30]
    // 0xad326c: StoreField: r3->field_f = r0
    //     0xad326c: stur            w0, [x3, #0xf]
    // 0xad3270: ldur            x0, [fp, #-0x20]
    // 0xad3274: StoreField: r3->field_b = r0
    //     0xad3274: stur            w0, [x3, #0xb]
    // 0xad3278: ldur            x2, [fp, #-0x18]
    // 0xad327c: r1 = Function 'onChanged':.
    //     0xad327c: add             x1, PP, #0x30, lsl #12  ; [pp+0x309d0] AnonymousClosure: (0xad3384), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onChanged (0xad33c0)
    //     0xad3280: ldr             x1, [x1, #0x9d0]
    // 0xad3284: r0 = AllocateClosure()
    //     0xad3284: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3288: mov             x1, x0
    // 0xad328c: ldur            x0, [fp, #-0x30]
    // 0xad3290: StoreField: r0->field_13 = r1
    //     0xad3290: stur            w1, [x0, #0x13]
    // 0xad3294: ldur            x2, [fp, #-8]
    // 0xad3298: r1 = Function 'onCloseTap':.
    //     0xad3298: add             x1, PP, #0x30, lsl #12  ; [pp+0x309d8] AnonymousClosure: (0xad330c), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onCloseTap (0xad3344)
    //     0xad329c: ldr             x1, [x1, #0x9d8]
    // 0xad32a0: r0 = AllocateClosure()
    //     0xad32a0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad32a4: mov             x1, x0
    // 0xad32a8: ldur            x0, [fp, #-0x30]
    // 0xad32ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xad32ac: stur            w1, [x0, #0x17]
    // 0xad32b0: r1 = "Cari artikel"
    //     0xad32b0: add             x1, PP, #0x30, lsl #12  ; [pp+0x309e0] "Cari artikel"
    //     0xad32b4: ldr             x1, [x1, #0x9e0]
    // 0xad32b8: StoreField: r0->field_23 = r1
    //     0xad32b8: stur            w1, [x0, #0x23]
    // 0xad32bc: ldur            x2, [fp, #-0x28]
    // 0xad32c0: r1 = Function 'onSubmitted':.
    //     0xad32c0: add             x1, PP, #0x30, lsl #12  ; [pp+0x309e8] AnonymousClosure: (0xad1f90), in [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] ArticleSearchController::onSubmitted (0xad1e34)
    //     0xad32c4: ldr             x1, [x1, #0x9e8]
    // 0xad32c8: r0 = AllocateClosure()
    //     0xad32c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad32cc: mov             x1, x0
    // 0xad32d0: ldur            x0, [fp, #-0x30]
    // 0xad32d4: StoreField: r0->field_1b = r1
    //     0xad32d4: stur            w1, [x0, #0x1b]
    // 0xad32d8: r1 = false
    //     0xad32d8: add             x1, NULL, #0x30  ; false
    // 0xad32dc: StoreField: r0->field_27 = r1
    //     0xad32dc: stur            w1, [x0, #0x27]
    // 0xad32e0: LeaveFrame
    //     0xad32e0: mov             SP, fp
    //     0xad32e4: ldp             fp, lr, [SP], #0x10
    // 0xad32e8: ret
    //     0xad32e8: ret             
    // 0xad32ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad32ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad32f0: b               #0xad31d0
    // 0xad32f4: r9 = textController
    //     0xad32f4: add             x9, PP, #0x30, lsl #12  ; [pp+0x30980] Field <ArticleSearchController.textController>: late (offset: 0x40)
    //     0xad32f8: ldr             x9, [x9, #0x980]
    // 0xad32fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad32fc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
