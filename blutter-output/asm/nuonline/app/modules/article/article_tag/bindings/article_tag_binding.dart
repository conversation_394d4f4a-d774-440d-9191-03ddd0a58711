// lib: , url: package:nuonline/app/modules/article/article_tag/bindings/article_tag_binding.dart

// class id: 1050142, size: 0x8
class :: {
}

// class id: 2191, size: 0x8, field offset: 0x8
class ArticleTagBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80e16c, size: 0x110
    // 0x80e16c: EnterFrame
    //     0x80e16c: stp             fp, lr, [SP, #-0x10]!
    //     0x80e170: mov             fp, SP
    // 0x80e174: AllocStack(0x20)
    //     0x80e174: sub             SP, SP, #0x20
    // 0x80e178: CheckStackOverflow
    //     0x80e178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e17c: cmp             SP, x16
    //     0x80e180: b.ls            #0x80e274
    // 0x80e184: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e184: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e188: ldr             x0, [x0, #0x2670]
    //     0x80e18c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e190: cmp             w0, w16
    //     0x80e194: b.ne            #0x80e1a0
    //     0x80e198: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e19c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e1a0: r0 = GetNavigation.arguments()
    //     0x80e1a0: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80e1a4: r16 = "id"
    //     0x80e1a4: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80e1a8: ldr             x16, [x16, #0x740]
    // 0x80e1ac: stp             x16, x0, [SP]
    // 0x80e1b0: r4 = 0
    //     0x80e1b0: movz            x4, #0
    // 0x80e1b4: ldr             x0, [SP, #8]
    // 0x80e1b8: r16 = UnlinkedCall_0x5f3c08
    //     0x80e1b8: add             x16, PP, #0x36, lsl #12  ; [pp+0x364c0] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80e1bc: add             x16, x16, #0x4c0
    // 0x80e1c0: ldp             x5, lr, [x16]
    // 0x80e1c4: blr             lr
    // 0x80e1c8: mov             x3, x0
    // 0x80e1cc: r2 = Null
    //     0x80e1cc: mov             x2, NULL
    // 0x80e1d0: r1 = Null
    //     0x80e1d0: mov             x1, NULL
    // 0x80e1d4: stur            x3, [fp, #-8]
    // 0x80e1d8: branchIfSmi(r0, 0x80e200)
    //     0x80e1d8: tbz             w0, #0, #0x80e200
    // 0x80e1dc: r4 = LoadClassIdInstr(r0)
    //     0x80e1dc: ldur            x4, [x0, #-1]
    //     0x80e1e0: ubfx            x4, x4, #0xc, #0x14
    // 0x80e1e4: sub             x4, x4, #0x3c
    // 0x80e1e8: cmp             x4, #1
    // 0x80e1ec: b.ls            #0x80e200
    // 0x80e1f0: r8 = int
    //     0x80e1f0: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80e1f4: r3 = Null
    //     0x80e1f4: add             x3, PP, #0x36, lsl #12  ; [pp+0x364d0] Null
    //     0x80e1f8: ldr             x3, [x3, #0x4d0]
    // 0x80e1fc: r0 = int()
    //     0x80e1fc: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80e200: ldur            x0, [fp, #-8]
    // 0x80e204: r1 = 60
    //     0x80e204: movz            x1, #0x3c
    // 0x80e208: branchIfSmi(r0, 0x80e214)
    //     0x80e208: tbz             w0, #0, #0x80e214
    // 0x80e20c: r1 = LoadClassIdInstr(r0)
    //     0x80e20c: ldur            x1, [x0, #-1]
    //     0x80e210: ubfx            x1, x1, #0xc, #0x14
    // 0x80e214: str             x0, [SP]
    // 0x80e218: mov             x0, x1
    // 0x80e21c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x80e21c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x80e220: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x80e220: movz            x17, #0x2b03
    //     0x80e224: add             lr, x0, x17
    //     0x80e228: ldr             lr, [x21, lr, lsl #3]
    //     0x80e22c: blr             lr
    // 0x80e230: r1 = Function '<anonymous closure>':.
    //     0x80e230: add             x1, PP, #0x36, lsl #12  ; [pp+0x364e0] AnonymousClosure: (0x80e27c), in [package:nuonline/app/modules/article/article_tag/bindings/article_tag_binding.dart] ArticleTagBinding::dependencies (0x80e16c)
    //     0x80e234: ldr             x1, [x1, #0x4e0]
    // 0x80e238: r2 = Null
    //     0x80e238: mov             x2, NULL
    // 0x80e23c: stur            x0, [fp, #-8]
    // 0x80e240: r0 = AllocateClosure()
    //     0x80e240: bl              #0xec1630  ; AllocateClosureStub
    // 0x80e244: r16 = <ArticleTagController>
    //     0x80e244: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d50] TypeArguments: <ArticleTagController>
    //     0x80e248: ldr             x16, [x16, #0xd50]
    // 0x80e24c: stp             x0, x16, [SP, #8]
    // 0x80e250: ldur            x16, [fp, #-8]
    // 0x80e254: str             x16, [SP]
    // 0x80e258: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x80e258: add             x4, PP, #0xb, lsl #12  ; [pp+0xb630] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x80e25c: ldr             x4, [x4, #0x630]
    // 0x80e260: r0 = Inst.lazyPut()
    //     0x80e260: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80e264: r0 = Null
    //     0x80e264: mov             x0, NULL
    // 0x80e268: LeaveFrame
    //     0x80e268: mov             SP, fp
    //     0x80e26c: ldp             fp, lr, [SP], #0x10
    // 0x80e270: ret
    //     0x80e270: ret             
    // 0x80e274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e278: b               #0x80e184
  }
  [closure] ArticleTagController <anonymous closure>(dynamic) {
    // ** addr: 0x80e27c, size: 0x80
    // 0x80e27c: EnterFrame
    //     0x80e27c: stp             fp, lr, [SP, #-0x10]!
    //     0x80e280: mov             fp, SP
    // 0x80e284: AllocStack(0x18)
    //     0x80e284: sub             SP, SP, #0x18
    // 0x80e288: CheckStackOverflow
    //     0x80e288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e28c: cmp             SP, x16
    //     0x80e290: b.ls            #0x80e2f4
    // 0x80e294: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e294: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e298: ldr             x0, [x0, #0x2670]
    //     0x80e29c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e2a0: cmp             w0, w16
    //     0x80e2a4: b.ne            #0x80e2b0
    //     0x80e2a8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e2ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e2b0: r16 = <ArticleRepository>
    //     0x80e2b0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80e2b4: ldr             x16, [x16, #0x98]
    // 0x80e2b8: r30 = "article_repo"
    //     0x80e2b8: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80e2bc: ldr             lr, [lr, #0xa0]
    // 0x80e2c0: stp             lr, x16, [SP]
    // 0x80e2c4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80e2c4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80e2c8: r0 = Inst.find()
    //     0x80e2c8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80e2cc: stur            x0, [fp, #-8]
    // 0x80e2d0: r0 = ArticleTagController()
    //     0x80e2d0: bl              #0x80e4e0  ; AllocateArticleTagControllerStub -> ArticleTagController (size=0x48)
    // 0x80e2d4: mov             x1, x0
    // 0x80e2d8: ldur            x2, [fp, #-8]
    // 0x80e2dc: stur            x0, [fp, #-8]
    // 0x80e2e0: r0 = ArticleTagController()
    //     0x80e2e0: bl              #0x80e2fc  ; [package:nuonline/app/modules/article/article_tag/controllers/article_tag_controller.dart] ArticleTagController::ArticleTagController
    // 0x80e2e4: ldur            x0, [fp, #-8]
    // 0x80e2e8: LeaveFrame
    //     0x80e2e8: mov             SP, fp
    //     0x80e2ec: ldp             fp, lr, [SP], #0x10
    // 0x80e2f0: ret
    //     0x80e2f0: ret             
    // 0x80e2f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e2f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e2f8: b               #0x80e294
  }
}
