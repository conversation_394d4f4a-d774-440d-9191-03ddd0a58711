// lib: , url: package:nuonline/app/modules/article/article_tag/controllers/article_tag_controller.dart

// class id: 1050143, size: 0x8
class :: {
}

// class id: 2046, size: 0x48, field offset: 0x38
class ArticleTagController extends _ArticleAuthorController&GetxController&PagingMixin {

  _ ArticleTagController(/* No info */) {
    // ** addr: 0x80e2fc, size: 0x17c
    // 0x80e2fc: EnterFrame
    //     0x80e2fc: stp             fp, lr, [SP, #-0x10]!
    //     0x80e300: mov             fp, SP
    // 0x80e304: AllocStack(0x28)
    //     0x80e304: sub             SP, SP, #0x28
    // 0x80e308: SetupParameters(ArticleTagController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x80e308: mov             x0, x2
    //     0x80e30c: stur            x1, [fp, #-8]
    //     0x80e310: stur            x2, [fp, #-0x10]
    // 0x80e314: CheckStackOverflow
    //     0x80e314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e318: cmp             SP, x16
    //     0x80e31c: b.ls            #0x80e470
    // 0x80e320: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e320: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e324: ldr             x0, [x0, #0x2670]
    //     0x80e328: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e32c: cmp             w0, w16
    //     0x80e330: b.ne            #0x80e33c
    //     0x80e334: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e338: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e33c: r0 = GetNavigation.arguments()
    //     0x80e33c: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80e340: r16 = "id"
    //     0x80e340: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x80e344: ldr             x16, [x16, #0x740]
    // 0x80e348: stp             x16, x0, [SP]
    // 0x80e34c: r4 = 0
    //     0x80e34c: movz            x4, #0
    // 0x80e350: ldr             x0, [SP, #8]
    // 0x80e354: r16 = UnlinkedCall_0x5f3c08
    //     0x80e354: add             x16, PP, #0x36, lsl #12  ; [pp+0x364e8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80e358: add             x16, x16, #0x4e8
    // 0x80e35c: ldp             x5, lr, [x16]
    // 0x80e360: blr             lr
    // 0x80e364: mov             x3, x0
    // 0x80e368: r2 = Null
    //     0x80e368: mov             x2, NULL
    // 0x80e36c: r1 = Null
    //     0x80e36c: mov             x1, NULL
    // 0x80e370: stur            x3, [fp, #-0x18]
    // 0x80e374: branchIfSmi(r0, 0x80e39c)
    //     0x80e374: tbz             w0, #0, #0x80e39c
    // 0x80e378: r4 = LoadClassIdInstr(r0)
    //     0x80e378: ldur            x4, [x0, #-1]
    //     0x80e37c: ubfx            x4, x4, #0xc, #0x14
    // 0x80e380: sub             x4, x4, #0x3c
    // 0x80e384: cmp             x4, #1
    // 0x80e388: b.ls            #0x80e39c
    // 0x80e38c: r8 = int
    //     0x80e38c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x80e390: r3 = Null
    //     0x80e390: add             x3, PP, #0x36, lsl #12  ; [pp+0x364f8] Null
    //     0x80e394: ldr             x3, [x3, #0x4f8]
    // 0x80e398: r0 = int()
    //     0x80e398: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x80e39c: ldur            x0, [fp, #-0x18]
    // 0x80e3a0: r1 = LoadInt32Instr(r0)
    //     0x80e3a0: sbfx            x1, x0, #1, #0x1f
    //     0x80e3a4: tbz             w0, #0, #0x80e3ac
    //     0x80e3a8: ldur            x1, [x0, #7]
    // 0x80e3ac: ldur            x0, [fp, #-8]
    // 0x80e3b0: StoreField: r0->field_3b = r1
    //     0x80e3b0: stur            x1, [x0, #0x3b]
    // 0x80e3b4: r0 = GetNavigation.arguments()
    //     0x80e3b4: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x80e3b8: r16 = "title"
    //     0x80e3b8: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x80e3bc: ldr             x16, [x16, #0x748]
    // 0x80e3c0: stp             x16, x0, [SP]
    // 0x80e3c4: r4 = 0
    //     0x80e3c4: movz            x4, #0
    // 0x80e3c8: ldr             x0, [SP, #8]
    // 0x80e3cc: r16 = UnlinkedCall_0x5f3c08
    //     0x80e3cc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36508] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x80e3d0: add             x16, x16, #0x508
    // 0x80e3d4: ldp             x5, lr, [x16]
    // 0x80e3d8: blr             lr
    // 0x80e3dc: mov             x3, x0
    // 0x80e3e0: r2 = Null
    //     0x80e3e0: mov             x2, NULL
    // 0x80e3e4: r1 = Null
    //     0x80e3e4: mov             x1, NULL
    // 0x80e3e8: stur            x3, [fp, #-0x18]
    // 0x80e3ec: r4 = 60
    //     0x80e3ec: movz            x4, #0x3c
    // 0x80e3f0: branchIfSmi(r0, 0x80e3fc)
    //     0x80e3f0: tbz             w0, #0, #0x80e3fc
    // 0x80e3f4: r4 = LoadClassIdInstr(r0)
    //     0x80e3f4: ldur            x4, [x0, #-1]
    //     0x80e3f8: ubfx            x4, x4, #0xc, #0x14
    // 0x80e3fc: sub             x4, x4, #0x5e
    // 0x80e400: cmp             x4, #1
    // 0x80e404: b.ls            #0x80e418
    // 0x80e408: r8 = String
    //     0x80e408: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x80e40c: r3 = Null
    //     0x80e40c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36518] Null
    //     0x80e410: ldr             x3, [x3, #0x518]
    // 0x80e414: r0 = String()
    //     0x80e414: bl              #0xed43b0  ; IsType_String_Stub
    // 0x80e418: ldur            x0, [fp, #-0x18]
    // 0x80e41c: ldur            x1, [fp, #-8]
    // 0x80e420: StoreField: r1->field_43 = r0
    //     0x80e420: stur            w0, [x1, #0x43]
    //     0x80e424: ldurb           w16, [x1, #-1]
    //     0x80e428: ldurb           w17, [x0, #-1]
    //     0x80e42c: and             x16, x17, x16, lsr #2
    //     0x80e430: tst             x16, HEAP, lsr #32
    //     0x80e434: b.eq            #0x80e43c
    //     0x80e438: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e43c: ldur            x0, [fp, #-0x10]
    // 0x80e440: StoreField: r1->field_37 = r0
    //     0x80e440: stur            w0, [x1, #0x37]
    //     0x80e444: ldurb           w16, [x1, #-1]
    //     0x80e448: ldurb           w17, [x0, #-1]
    //     0x80e44c: and             x16, x17, x16, lsr #2
    //     0x80e450: tst             x16, HEAP, lsr #32
    //     0x80e454: b.eq            #0x80e45c
    //     0x80e458: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e45c: r0 = _ArticleAuthorController&GetxController&PagingMixin()
    //     0x80e45c: bl              #0x80c3d4  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::_ArticleAuthorController&GetxController&PagingMixin
    // 0x80e460: r0 = Null
    //     0x80e460: mov             x0, NULL
    // 0x80e464: LeaveFrame
    //     0x80e464: mov             SP, fp
    //     0x80e468: ldp             fp, lr, [SP], #0x10
    // 0x80e46c: ret
    //     0x80e46c: ret             
    // 0x80e470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e470: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e474: b               #0x80e320
  }
  const int dyn:get:id(ArticleTagController) {
    // ** addr: 0x80e490, size: 0x48
    // 0x80e490: ldr             x2, [SP]
    // 0x80e494: LoadField: r3 = r2->field_3b
    //     0x80e494: ldur            x3, [x2, #0x3b]
    // 0x80e498: r0 = BoxInt64Instr(r3)
    //     0x80e498: sbfiz           x0, x3, #1, #0x1f
    //     0x80e49c: cmp             x3, x0, asr #1
    //     0x80e4a0: b.eq            #0x80e4bc
    //     0x80e4a4: stp             fp, lr, [SP, #-0x10]!
    //     0x80e4a8: mov             fp, SP
    //     0x80e4ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80e4b0: mov             SP, fp
    //     0x80e4b4: ldp             fp, lr, [SP], #0x10
    //     0x80e4b8: stur            x3, [x0, #7]
    // 0x80e4bc: ret
    //     0x80e4bc: ret             
  }
  _ onPageRequest(/* No info */) {
    // ** addr: 0xe33078, size: 0x1c0
    // 0xe33078: EnterFrame
    //     0xe33078: stp             fp, lr, [SP, #-0x10]!
    //     0xe3307c: mov             fp, SP
    // 0xe33080: AllocStack(0x40)
    //     0xe33080: sub             SP, SP, #0x40
    // 0xe33084: SetupParameters(ArticleTagController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe33084: stur            x1, [fp, #-8]
    //     0xe33088: stur            x2, [fp, #-0x10]
    // 0xe3308c: CheckStackOverflow
    //     0xe3308c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe33090: cmp             SP, x16
    //     0xe33094: b.ls            #0xe33230
    // 0xe33098: r1 = 1
    //     0xe33098: movz            x1, #0x1
    // 0xe3309c: r0 = AllocateContext()
    //     0xe3309c: bl              #0xec126c  ; AllocateContextStub
    // 0xe330a0: mov             x3, x0
    // 0xe330a4: ldur            x0, [fp, #-8]
    // 0xe330a8: stur            x3, [fp, #-0x20]
    // 0xe330ac: StoreField: r3->field_f = r0
    //     0xe330ac: stur            w0, [x3, #0xf]
    // 0xe330b0: LoadField: r4 = r0->field_37
    //     0xe330b0: ldur            w4, [x0, #0x37]
    // 0xe330b4: DecompressPointer r4
    //     0xe330b4: add             x4, x4, HEAP, lsl #32
    // 0xe330b8: stur            x4, [fp, #-0x18]
    // 0xe330bc: r1 = Null
    //     0xe330bc: mov             x1, NULL
    // 0xe330c0: r2 = 8
    //     0xe330c0: movz            x2, #0x8
    // 0xe330c4: r0 = AllocateArray()
    //     0xe330c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xe330c8: mov             x2, x0
    // 0xe330cc: stur            x2, [fp, #-0x28]
    // 0xe330d0: r16 = "tag_id"
    //     0xe330d0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3f050] "tag_id"
    //     0xe330d4: ldr             x16, [x16, #0x50]
    // 0xe330d8: StoreField: r2->field_f = r16
    //     0xe330d8: stur            w16, [x2, #0xf]
    // 0xe330dc: ldur            x3, [fp, #-8]
    // 0xe330e0: LoadField: r4 = r3->field_3b
    //     0xe330e0: ldur            x4, [x3, #0x3b]
    // 0xe330e4: r0 = BoxInt64Instr(r4)
    //     0xe330e4: sbfiz           x0, x4, #1, #0x1f
    //     0xe330e8: cmp             x4, x0, asr #1
    //     0xe330ec: b.eq            #0xe330f8
    //     0xe330f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe330f4: stur            x4, [x0, #7]
    // 0xe330f8: r1 = 60
    //     0xe330f8: movz            x1, #0x3c
    // 0xe330fc: branchIfSmi(r0, 0xe33108)
    //     0xe330fc: tbz             w0, #0, #0xe33108
    // 0xe33100: r1 = LoadClassIdInstr(r0)
    //     0xe33100: ldur            x1, [x0, #-1]
    //     0xe33104: ubfx            x1, x1, #0xc, #0x14
    // 0xe33108: str             x0, [SP]
    // 0xe3310c: mov             x0, x1
    // 0xe33110: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe33110: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe33114: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe33114: movz            x17, #0x2b03
    //     0xe33118: add             lr, x0, x17
    //     0xe3311c: ldr             lr, [x21, lr, lsl #3]
    //     0xe33120: blr             lr
    // 0xe33124: ldur            x1, [fp, #-0x28]
    // 0xe33128: ArrayStore: r1[1] = r0  ; List_4
    //     0xe33128: add             x25, x1, #0x13
    //     0xe3312c: str             w0, [x25]
    //     0xe33130: tbz             w0, #0, #0xe3314c
    //     0xe33134: ldurb           w16, [x1, #-1]
    //     0xe33138: ldurb           w17, [x0, #-1]
    //     0xe3313c: and             x16, x17, x16, lsr #2
    //     0xe33140: tst             x16, HEAP, lsr #32
    //     0xe33144: b.eq            #0xe3314c
    //     0xe33148: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe3314c: ldur            x2, [fp, #-0x28]
    // 0xe33150: r16 = "page"
    //     0xe33150: add             x16, PP, #0x10, lsl #12  ; [pp+0x10300] "page"
    //     0xe33154: ldr             x16, [x16, #0x300]
    // 0xe33158: ArrayStore: r2[0] = r16  ; List_4
    //     0xe33158: stur            w16, [x2, #0x17]
    // 0xe3315c: ldur            x3, [fp, #-0x10]
    // 0xe33160: r0 = BoxInt64Instr(r3)
    //     0xe33160: sbfiz           x0, x3, #1, #0x1f
    //     0xe33164: cmp             x3, x0, asr #1
    //     0xe33168: b.eq            #0xe33174
    //     0xe3316c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe33170: stur            x3, [x0, #7]
    // 0xe33174: r1 = 60
    //     0xe33174: movz            x1, #0x3c
    // 0xe33178: branchIfSmi(r0, 0xe33184)
    //     0xe33178: tbz             w0, #0, #0xe33184
    // 0xe3317c: r1 = LoadClassIdInstr(r0)
    //     0xe3317c: ldur            x1, [x0, #-1]
    //     0xe33180: ubfx            x1, x1, #0xc, #0x14
    // 0xe33184: str             x0, [SP]
    // 0xe33188: mov             x0, x1
    // 0xe3318c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xe3318c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xe33190: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xe33190: movz            x17, #0x2b03
    //     0xe33194: add             lr, x0, x17
    //     0xe33198: ldr             lr, [x21, lr, lsl #3]
    //     0xe3319c: blr             lr
    // 0xe331a0: ldur            x1, [fp, #-0x28]
    // 0xe331a4: ArrayStore: r1[3] = r0  ; List_4
    //     0xe331a4: add             x25, x1, #0x1b
    //     0xe331a8: str             w0, [x25]
    //     0xe331ac: tbz             w0, #0, #0xe331c8
    //     0xe331b0: ldurb           w16, [x1, #-1]
    //     0xe331b4: ldurb           w17, [x0, #-1]
    //     0xe331b8: and             x16, x17, x16, lsr #2
    //     0xe331bc: tst             x16, HEAP, lsr #32
    //     0xe331c0: b.eq            #0xe331c8
    //     0xe331c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xe331c8: r16 = <String, String?>
    //     0xe331c8: add             x16, PP, #9, lsl #12  ; [pp+0x9198] TypeArguments: <String, String?>
    //     0xe331cc: ldr             x16, [x16, #0x198]
    // 0xe331d0: ldur            lr, [fp, #-0x28]
    // 0xe331d4: stp             lr, x16, [SP]
    // 0xe331d8: r0 = Map._fromLiteral()
    //     0xe331d8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xe331dc: ldur            x1, [fp, #-0x18]
    // 0xe331e0: mov             x2, x0
    // 0xe331e4: r0 = findAll()
    //     0xe331e4: bl              #0x8ff2cc  ; [package:nuonline/app/data/repositories/article_repository.dart] ArticleRepository::findAll
    // 0xe331e8: ldur            x2, [fp, #-0x20]
    // 0xe331ec: r1 = Function '<anonymous closure>':.
    //     0xe331ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40978] AnonymousClosure: (0xe33238), in [package:nuonline/app/modules/article/article_tag/controllers/article_tag_controller.dart] ArticleTagController::onPageRequest (0xe33078)
    //     0xe331f0: ldr             x1, [x1, #0x978]
    // 0xe331f4: stur            x0, [fp, #-0x18]
    // 0xe331f8: r0 = AllocateClosure()
    //     0xe331f8: bl              #0xec1630  ; AllocateClosureStub
    // 0xe331fc: r16 = <void?>
    //     0xe331fc: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe33200: ldur            lr, [fp, #-0x18]
    // 0xe33204: stp             lr, x16, [SP, #8]
    // 0xe33208: str             x0, [SP]
    // 0xe3320c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe3320c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe33210: r0 = then()
    //     0xe33210: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xe33214: ldur            x1, [fp, #-8]
    // 0xe33218: ldur            x2, [fp, #-0x10]
    // 0xe3321c: r0 = onPageRequest()
    //     0xe3321c: bl              #0xe32258  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRequest
    // 0xe33220: r0 = Null
    //     0xe33220: mov             x0, NULL
    // 0xe33224: LeaveFrame
    //     0xe33224: mov             SP, fp
    //     0xe33228: ldp             fp, lr, [SP], #0x10
    // 0xe3322c: ret
    //     0xe3322c: ret             
    // 0xe33230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe33230: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe33234: b               #0xe33098
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Article>>) {
    // ** addr: 0xe33238, size: 0xa0
    // 0xe33238: EnterFrame
    //     0xe33238: stp             fp, lr, [SP, #-0x10]!
    //     0xe3323c: mov             fp, SP
    // 0xe33240: AllocStack(0x28)
    //     0xe33240: sub             SP, SP, #0x28
    // 0xe33244: SetupParameters()
    //     0xe33244: ldr             x0, [fp, #0x18]
    //     0xe33248: ldur            w1, [x0, #0x17]
    //     0xe3324c: add             x1, x1, HEAP, lsl #32
    // 0xe33250: CheckStackOverflow
    //     0xe33250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe33254: cmp             SP, x16
    //     0xe33258: b.ls            #0xe332d0
    // 0xe3325c: LoadField: r0 = r1->field_f
    //     0xe3325c: ldur            w0, [x1, #0xf]
    // 0xe33260: DecompressPointer r0
    //     0xe33260: add             x0, x0, HEAP, lsl #32
    // 0xe33264: mov             x2, x0
    // 0xe33268: stur            x0, [fp, #-8]
    // 0xe3326c: r1 = Function '_onSuccess@1883265233':.
    //     0xe3326c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40980] AnonymousClosure: (0xe33314), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess (0xe325c8)
    //     0xe33270: ldr             x1, [x1, #0x980]
    // 0xe33274: r0 = AllocateClosure()
    //     0xe33274: bl              #0xec1630  ; AllocateClosureStub
    // 0xe33278: ldur            x2, [fp, #-8]
    // 0xe3327c: r1 = Function '_onError@1883265233':.
    //     0xe3327c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40988] AnonymousClosure: (0xe332d8), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError (0xe32524)
    //     0xe33280: ldr             x1, [x1, #0x988]
    // 0xe33284: stur            x0, [fp, #-8]
    // 0xe33288: r0 = AllocateClosure()
    //     0xe33288: bl              #0xec1630  ; AllocateClosureStub
    // 0xe3328c: mov             x1, x0
    // 0xe33290: ldr             x0, [fp, #0x10]
    // 0xe33294: r2 = LoadClassIdInstr(r0)
    //     0xe33294: ldur            x2, [x0, #-1]
    //     0xe33298: ubfx            x2, x2, #0xc, #0x14
    // 0xe3329c: r16 = <void?>
    //     0xe3329c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xe332a0: stp             x0, x16, [SP, #0x10]
    // 0xe332a4: ldur            x16, [fp, #-8]
    // 0xe332a8: stp             x16, x1, [SP]
    // 0xe332ac: mov             x0, x2
    // 0xe332b0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xe332b0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xe332b4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xe332b4: sub             lr, x0, #1, lsl #12
    //     0xe332b8: ldr             lr, [x21, lr, lsl #3]
    //     0xe332bc: blr             lr
    // 0xe332c0: r0 = Null
    //     0xe332c0: mov             x0, NULL
    // 0xe332c4: LeaveFrame
    //     0xe332c4: mov             SP, fp
    //     0xe332c8: ldp             fp, lr, [SP], #0x10
    // 0xe332cc: ret
    //     0xe332cc: ret             
    // 0xe332d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe332d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe332d4: b               #0xe3325c
  }
  [closure] void _onError(dynamic, NetworkExceptions) {
    // ** addr: 0xe332d8, size: 0x3c
    // 0xe332d8: EnterFrame
    //     0xe332d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe332dc: mov             fp, SP
    // 0xe332e0: ldr             x0, [fp, #0x18]
    // 0xe332e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe332e4: ldur            w1, [x0, #0x17]
    // 0xe332e8: DecompressPointer r1
    //     0xe332e8: add             x1, x1, HEAP, lsl #32
    // 0xe332ec: CheckStackOverflow
    //     0xe332ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe332f0: cmp             SP, x16
    //     0xe332f4: b.ls            #0xe3330c
    // 0xe332f8: ldr             x2, [fp, #0x10]
    // 0xe332fc: r0 = _onError()
    //     0xe332fc: bl              #0xe32524  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onError
    // 0xe33300: LeaveFrame
    //     0xe33300: mov             SP, fp
    //     0xe33304: ldp             fp, lr, [SP], #0x10
    // 0xe33308: ret
    //     0xe33308: ret             
    // 0xe3330c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3330c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe33310: b               #0xe332f8
  }
  [closure] void _onSuccess(dynamic, List<Article>, Pagination?) {
    // ** addr: 0xe33314, size: 0x40
    // 0xe33314: EnterFrame
    //     0xe33314: stp             fp, lr, [SP, #-0x10]!
    //     0xe33318: mov             fp, SP
    // 0xe3331c: ldr             x0, [fp, #0x20]
    // 0xe33320: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe33320: ldur            w1, [x0, #0x17]
    // 0xe33324: DecompressPointer r1
    //     0xe33324: add             x1, x1, HEAP, lsl #32
    // 0xe33328: CheckStackOverflow
    //     0xe33328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3332c: cmp             SP, x16
    //     0xe33330: b.ls            #0xe3334c
    // 0xe33334: ldr             x2, [fp, #0x18]
    // 0xe33338: ldr             x3, [fp, #0x10]
    // 0xe3333c: r0 = _onSuccess()
    //     0xe3333c: bl              #0xe325c8  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] ArticleAuthorController::_onSuccess
    // 0xe33340: LeaveFrame
    //     0xe33340: mov             SP, fp
    //     0xe33344: ldp             fp, lr, [SP], #0x10
    // 0xe33348: ret
    //     0xe33348: ret             
    // 0xe3334c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3334c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe33350: b               #0xe33334
  }
}
