// lib: , url: package:nuonline/app/modules/article/article_tag/views/article_tag_view.dart

// class id: 1050144, size: 0x8
class :: {
}

// class id: 4448, size: 0x14, field offset: 0x14
class ArticleTagView extends GetWidget<dynamic> {

  get _ tag(/* No info */) {
    // ** addr: 0x862644, size: 0xd8
    // 0x862644: EnterFrame
    //     0x862644: stp             fp, lr, [SP, #-0x10]!
    //     0x862648: mov             fp, SP
    // 0x86264c: AllocStack(0x18)
    //     0x86264c: sub             SP, SP, #0x18
    // 0x862650: CheckStackOverflow
    //     0x862650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862654: cmp             SP, x16
    //     0x862658: b.ls            #0x862714
    // 0x86265c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x86265c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x862660: ldr             x0, [x0, #0x2670]
    //     0x862664: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x862668: cmp             w0, w16
    //     0x86266c: b.ne            #0x862678
    //     0x862670: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x862674: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x862678: r0 = GetNavigation.arguments()
    //     0x862678: bl              #0x65c284  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x86267c: r16 = "id"
    //     0x86267c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0x862680: ldr             x16, [x16, #0x740]
    // 0x862684: stp             x16, x0, [SP]
    // 0x862688: r4 = 0
    //     0x862688: movz            x4, #0
    // 0x86268c: ldr             x0, [SP, #8]
    // 0x862690: r16 = UnlinkedCall_0x5f3c08
    //     0x862690: add             x16, PP, #0x40, lsl #12  ; [pp+0x40958] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x862694: add             x16, x16, #0x958
    // 0x862698: ldp             x5, lr, [x16]
    // 0x86269c: blr             lr
    // 0x8626a0: mov             x3, x0
    // 0x8626a4: r2 = Null
    //     0x8626a4: mov             x2, NULL
    // 0x8626a8: r1 = Null
    //     0x8626a8: mov             x1, NULL
    // 0x8626ac: stur            x3, [fp, #-8]
    // 0x8626b0: branchIfSmi(r0, 0x8626d8)
    //     0x8626b0: tbz             w0, #0, #0x8626d8
    // 0x8626b4: r4 = LoadClassIdInstr(r0)
    //     0x8626b4: ldur            x4, [x0, #-1]
    //     0x8626b8: ubfx            x4, x4, #0xc, #0x14
    // 0x8626bc: sub             x4, x4, #0x3c
    // 0x8626c0: cmp             x4, #1
    // 0x8626c4: b.ls            #0x8626d8
    // 0x8626c8: r8 = int
    //     0x8626c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x8626cc: r3 = Null
    //     0x8626cc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40968] Null
    //     0x8626d0: ldr             x3, [x3, #0x968]
    // 0x8626d4: r0 = int()
    //     0x8626d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x8626d8: ldur            x0, [fp, #-8]
    // 0x8626dc: r1 = 60
    //     0x8626dc: movz            x1, #0x3c
    // 0x8626e0: branchIfSmi(r0, 0x8626ec)
    //     0x8626e0: tbz             w0, #0, #0x8626ec
    // 0x8626e4: r1 = LoadClassIdInstr(r0)
    //     0x8626e4: ldur            x1, [x0, #-1]
    //     0x8626e8: ubfx            x1, x1, #0xc, #0x14
    // 0x8626ec: str             x0, [SP]
    // 0x8626f0: mov             x0, x1
    // 0x8626f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8626f4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8626f8: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x8626f8: movz            x17, #0x2b03
    //     0x8626fc: add             lr, x0, x17
    //     0x862700: ldr             lr, [x21, lr, lsl #3]
    //     0x862704: blr             lr
    // 0x862708: LeaveFrame
    //     0x862708: mov             SP, fp
    //     0x86270c: ldp             fp, lr, [SP], #0x10
    // 0x862710: ret
    //     0x862710: ret             
    // 0x862714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862718: b               #0x86265c
  }
  _ build(/* No info */) {
    // ** addr: 0xbc3534, size: 0x420
    // 0xbc3534: EnterFrame
    //     0xbc3534: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3538: mov             fp, SP
    // 0xbc353c: AllocStack(0x40)
    //     0xbc353c: sub             SP, SP, #0x40
    // 0xbc3540: SetupParameters(ArticleTagView this /* r1 => r0, fp-0x8 */)
    //     0xbc3540: mov             x0, x1
    //     0xbc3544: stur            x1, [fp, #-8]
    // 0xbc3548: CheckStackOverflow
    //     0xbc3548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc354c: cmp             SP, x16
    //     0xbc3550: b.ls            #0xbc394c
    // 0xbc3554: r1 = 1
    //     0xbc3554: movz            x1, #0x1
    // 0xbc3558: r0 = AllocateContext()
    //     0xbc3558: bl              #0xec126c  ; AllocateContextStub
    // 0xbc355c: ldur            x2, [fp, #-8]
    // 0xbc3560: stur            x0, [fp, #-0x10]
    // 0xbc3564: StoreField: r0->field_f = r2
    //     0xbc3564: stur            w2, [x0, #0xf]
    // 0xbc3568: r0 = AppBar()
    //     0xbc3568: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xbc356c: stur            x0, [fp, #-0x18]
    // 0xbc3570: r16 = Instance_Text
    //     0xbc3570: add             x16, PP, #0x40, lsl #12  ; [pp+0x408d8] Obj!Text@e23da1
    //     0xbc3574: ldr             x16, [x16, #0x8d8]
    // 0xbc3578: str             x16, [SP]
    // 0xbc357c: mov             x1, x0
    // 0xbc3580: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xbc3580: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xbc3584: ldr             x4, [x4, #0x6e8]
    // 0xbc3588: r0 = AppBar()
    //     0xbc3588: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xbc358c: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc358c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc3590: ldr             x0, [x0, #0x26d0]
    //     0xbc3594: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc3598: cmp             w0, w16
    //     0xbc359c: b.ne            #0xbc35ac
    //     0xbc35a0: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc35a4: ldr             x2, [x2, #0xb90]
    //     0xbc35a8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc35ac: mov             x1, x0
    // 0xbc35b0: ldur            x2, [fp, #-8]
    // 0xbc35b4: stur            x0, [fp, #-0x20]
    // 0xbc35b8: r0 = []()
    //     0xbc35b8: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc35bc: mov             x4, x0
    // 0xbc35c0: ldur            x3, [fp, #-8]
    // 0xbc35c4: stur            x4, [fp, #-0x30]
    // 0xbc35c8: LoadField: r5 = r3->field_b
    //     0xbc35c8: ldur            w5, [x3, #0xb]
    // 0xbc35cc: DecompressPointer r5
    //     0xbc35cc: add             x5, x5, HEAP, lsl #32
    // 0xbc35d0: mov             x0, x4
    // 0xbc35d4: mov             x2, x5
    // 0xbc35d8: stur            x5, [fp, #-0x28]
    // 0xbc35dc: r1 = Null
    //     0xbc35dc: mov             x1, NULL
    // 0xbc35e0: cmp             w2, NULL
    // 0xbc35e4: b.eq            #0xbc3608
    // 0xbc35e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc35e8: ldur            w4, [x2, #0x17]
    // 0xbc35ec: DecompressPointer r4
    //     0xbc35ec: add             x4, x4, HEAP, lsl #32
    // 0xbc35f0: r8 = X0 bound GetLifeCycleBase?
    //     0xbc35f0: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc35f4: ldr             x8, [x8, #0xb98]
    // 0xbc35f8: LoadField: r9 = r4->field_7
    //     0xbc35f8: ldur            x9, [x4, #7]
    // 0xbc35fc: r3 = Null
    //     0xbc35fc: add             x3, PP, #0x40, lsl #12  ; [pp+0x408e0] Null
    //     0xbc3600: ldr             x3, [x3, #0x8e0]
    // 0xbc3604: blr             x9
    // 0xbc3608: ldur            x1, [fp, #-0x20]
    // 0xbc360c: ldur            x2, [fp, #-8]
    // 0xbc3610: r0 = []()
    //     0xbc3610: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3614: ldur            x2, [fp, #-0x28]
    // 0xbc3618: mov             x3, x0
    // 0xbc361c: r1 = Null
    //     0xbc361c: mov             x1, NULL
    // 0xbc3620: stur            x3, [fp, #-8]
    // 0xbc3624: cmp             w2, NULL
    // 0xbc3628: b.eq            #0xbc364c
    // 0xbc362c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc362c: ldur            w4, [x2, #0x17]
    // 0xbc3630: DecompressPointer r4
    //     0xbc3630: add             x4, x4, HEAP, lsl #32
    // 0xbc3634: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3634: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3638: ldr             x8, [x8, #0xb98]
    // 0xbc363c: LoadField: r9 = r4->field_7
    //     0xbc363c: ldur            x9, [x4, #7]
    // 0xbc3640: r3 = Null
    //     0xbc3640: add             x3, PP, #0x40, lsl #12  ; [pp+0x408f0] Null
    //     0xbc3644: ldr             x3, [x3, #0x8f0]
    // 0xbc3648: blr             x9
    // 0xbc364c: ldur            x0, [fp, #-8]
    // 0xbc3650: LoadField: r1 = r0->field_43
    //     0xbc3650: ldur            w1, [x0, #0x43]
    // 0xbc3654: DecompressPointer r1
    //     0xbc3654: add             x1, x1, HEAP, lsl #32
    // 0xbc3658: stur            x1, [fp, #-0x20]
    // 0xbc365c: r0 = NArticleHeader()
    //     0xbc365c: bl              #0xad5974  ; AllocateNArticleHeaderStub -> NArticleHeader (size=0x18)
    // 0xbc3660: mov             x1, x0
    // 0xbc3664: ldur            x0, [fp, #-0x20]
    // 0xbc3668: stur            x1, [fp, #-8]
    // 0xbc366c: StoreField: r1->field_b = r0
    //     0xbc366c: stur            w0, [x1, #0xb]
    // 0xbc3670: r0 = true
    //     0xbc3670: add             x0, NULL, #0x20  ; true
    // 0xbc3674: StoreField: r1->field_13 = r0
    //     0xbc3674: stur            w0, [x1, #0x13]
    // 0xbc3678: r0 = Padding()
    //     0xbc3678: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc367c: mov             x1, x0
    // 0xbc3680: r0 = Instance_EdgeInsets
    //     0xbc3680: add             x0, PP, #0x29, lsl #12  ; [pp+0x296f0] Obj!EdgeInsets@e12791
    //     0xbc3684: ldr             x0, [x0, #0x6f0]
    // 0xbc3688: stur            x1, [fp, #-0x20]
    // 0xbc368c: StoreField: r1->field_f = r0
    //     0xbc368c: stur            w0, [x1, #0xf]
    // 0xbc3690: ldur            x0, [fp, #-8]
    // 0xbc3694: StoreField: r1->field_b = r0
    //     0xbc3694: stur            w0, [x1, #0xb]
    // 0xbc3698: r0 = Obx()
    //     0xbc3698: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbc369c: ldur            x2, [fp, #-0x10]
    // 0xbc36a0: r1 = Function '<anonymous closure>':.
    //     0xbc36a0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40900] AnonymousClosure: (0xbc3954), in [package:nuonline/app/modules/article/article_tag/views/article_tag_view.dart] ArticleTagView::build (0xbc3534)
    //     0xbc36a4: ldr             x1, [x1, #0x900]
    // 0xbc36a8: stur            x0, [fp, #-8]
    // 0xbc36ac: r0 = AllocateClosure()
    //     0xbc36ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc36b0: mov             x1, x0
    // 0xbc36b4: ldur            x0, [fp, #-8]
    // 0xbc36b8: StoreField: r0->field_b = r1
    //     0xbc36b8: stur            w1, [x0, #0xb]
    // 0xbc36bc: r1 = <FlexParentData>
    //     0xbc36bc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbc36c0: ldr             x1, [x1, #0x720]
    // 0xbc36c4: r0 = Expanded()
    //     0xbc36c4: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbc36c8: mov             x3, x0
    // 0xbc36cc: r0 = 1
    //     0xbc36cc: movz            x0, #0x1
    // 0xbc36d0: stur            x3, [fp, #-0x10]
    // 0xbc36d4: StoreField: r3->field_13 = r0
    //     0xbc36d4: stur            x0, [x3, #0x13]
    // 0xbc36d8: r0 = Instance_FlexFit
    //     0xbc36d8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbc36dc: ldr             x0, [x0, #0x728]
    // 0xbc36e0: StoreField: r3->field_1b = r0
    //     0xbc36e0: stur            w0, [x3, #0x1b]
    // 0xbc36e4: ldur            x0, [fp, #-8]
    // 0xbc36e8: StoreField: r3->field_b = r0
    //     0xbc36e8: stur            w0, [x3, #0xb]
    // 0xbc36ec: r1 = Null
    //     0xbc36ec: mov             x1, NULL
    // 0xbc36f0: r2 = 6
    //     0xbc36f0: movz            x2, #0x6
    // 0xbc36f4: r0 = AllocateArray()
    //     0xbc36f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc36f8: mov             x2, x0
    // 0xbc36fc: ldur            x0, [fp, #-0x20]
    // 0xbc3700: stur            x2, [fp, #-8]
    // 0xbc3704: StoreField: r2->field_f = r0
    //     0xbc3704: stur            w0, [x2, #0xf]
    // 0xbc3708: r16 = Instance_Divider
    //     0xbc3708: add             x16, PP, #0x29, lsl #12  ; [pp+0x29190] Obj!Divider@e25781
    //     0xbc370c: ldr             x16, [x16, #0x190]
    // 0xbc3710: StoreField: r2->field_13 = r16
    //     0xbc3710: stur            w16, [x2, #0x13]
    // 0xbc3714: ldur            x0, [fp, #-0x10]
    // 0xbc3718: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc3718: stur            w0, [x2, #0x17]
    // 0xbc371c: r1 = <Widget>
    //     0xbc371c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbc3720: r0 = AllocateGrowableArray()
    //     0xbc3720: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbc3724: mov             x1, x0
    // 0xbc3728: ldur            x0, [fp, #-8]
    // 0xbc372c: stur            x1, [fp, #-0x10]
    // 0xbc3730: StoreField: r1->field_f = r0
    //     0xbc3730: stur            w0, [x1, #0xf]
    // 0xbc3734: r0 = 6
    //     0xbc3734: movz            x0, #0x6
    // 0xbc3738: StoreField: r1->field_b = r0
    //     0xbc3738: stur            w0, [x1, #0xb]
    // 0xbc373c: r0 = find()
    //     0xbc373c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc3740: mov             x1, x0
    // 0xbc3744: r0 = _adsVisibility()
    //     0xbc3744: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc3748: mov             x2, x0
    // 0xbc374c: r1 = Null
    //     0xbc374c: mov             x1, NULL
    // 0xbc3750: r0 = AdsConfig.fromJson()
    //     0xbc3750: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc3754: LoadField: r1 = r0->field_f
    //     0xbc3754: ldur            w1, [x0, #0xf]
    // 0xbc3758: DecompressPointer r1
    //     0xbc3758: add             x1, x1, HEAP, lsl #32
    // 0xbc375c: LoadField: r0 = r1->field_7
    //     0xbc375c: ldur            w0, [x1, #7]
    // 0xbc3760: DecompressPointer r0
    //     0xbc3760: add             x0, x0, HEAP, lsl #32
    // 0xbc3764: tbnz            w0, #4, #0xbc3828
    // 0xbc3768: ldur            x1, [fp, #-0x10]
    // 0xbc376c: r0 = find()
    //     0xbc376c: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc3770: mov             x1, x0
    // 0xbc3774: r0 = _adsVisibility()
    //     0xbc3774: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc3778: mov             x2, x0
    // 0xbc377c: r1 = Null
    //     0xbc377c: mov             x1, NULL
    // 0xbc3780: r0 = AdsConfig.fromJson()
    //     0xbc3780: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc3784: LoadField: r1 = r0->field_f
    //     0xbc3784: ldur            w1, [x0, #0xf]
    // 0xbc3788: DecompressPointer r1
    //     0xbc3788: add             x1, x1, HEAP, lsl #32
    // 0xbc378c: LoadField: r0 = r1->field_f
    //     0xbc378c: ldur            w0, [x1, #0xf]
    // 0xbc3790: DecompressPointer r0
    //     0xbc3790: add             x0, x0, HEAP, lsl #32
    // 0xbc3794: stur            x0, [fp, #-8]
    // 0xbc3798: r0 = AdmobBannerWidget()
    //     0xbc3798: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xbc379c: mov             x2, x0
    // 0xbc37a0: ldur            x0, [fp, #-8]
    // 0xbc37a4: stur            x2, [fp, #-0x20]
    // 0xbc37a8: StoreField: r2->field_b = r0
    //     0xbc37a8: stur            w0, [x2, #0xb]
    // 0xbc37ac: ldur            x0, [fp, #-0x10]
    // 0xbc37b0: LoadField: r1 = r0->field_b
    //     0xbc37b0: ldur            w1, [x0, #0xb]
    // 0xbc37b4: LoadField: r3 = r0->field_f
    //     0xbc37b4: ldur            w3, [x0, #0xf]
    // 0xbc37b8: DecompressPointer r3
    //     0xbc37b8: add             x3, x3, HEAP, lsl #32
    // 0xbc37bc: LoadField: r4 = r3->field_b
    //     0xbc37bc: ldur            w4, [x3, #0xb]
    // 0xbc37c0: r3 = LoadInt32Instr(r1)
    //     0xbc37c0: sbfx            x3, x1, #1, #0x1f
    // 0xbc37c4: stur            x3, [fp, #-0x38]
    // 0xbc37c8: r1 = LoadInt32Instr(r4)
    //     0xbc37c8: sbfx            x1, x4, #1, #0x1f
    // 0xbc37cc: cmp             x3, x1
    // 0xbc37d0: b.ne            #0xbc37dc
    // 0xbc37d4: mov             x1, x0
    // 0xbc37d8: r0 = _growToNextCapacity()
    //     0xbc37d8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbc37dc: ldur            x2, [fp, #-0x10]
    // 0xbc37e0: ldur            x3, [fp, #-0x38]
    // 0xbc37e4: add             x0, x3, #1
    // 0xbc37e8: lsl             x1, x0, #1
    // 0xbc37ec: StoreField: r2->field_b = r1
    //     0xbc37ec: stur            w1, [x2, #0xb]
    // 0xbc37f0: LoadField: r1 = r2->field_f
    //     0xbc37f0: ldur            w1, [x2, #0xf]
    // 0xbc37f4: DecompressPointer r1
    //     0xbc37f4: add             x1, x1, HEAP, lsl #32
    // 0xbc37f8: ldur            x0, [fp, #-0x20]
    // 0xbc37fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbc37fc: add             x25, x1, x3, lsl #2
    //     0xbc3800: add             x25, x25, #0xf
    //     0xbc3804: str             w0, [x25]
    //     0xbc3808: tbz             w0, #0, #0xbc3824
    //     0xbc380c: ldurb           w16, [x1, #-1]
    //     0xbc3810: ldurb           w17, [x0, #-1]
    //     0xbc3814: and             x16, x17, x16, lsr #2
    //     0xbc3818: tst             x16, HEAP, lsr #32
    //     0xbc381c: b.eq            #0xbc3824
    //     0xbc3820: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc3824: b               #0xbc382c
    // 0xbc3828: ldur            x2, [fp, #-0x10]
    // 0xbc382c: ldur            x0, [fp, #-0x18]
    // 0xbc3830: r0 = Column()
    //     0xbc3830: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc3834: mov             x1, x0
    // 0xbc3838: r0 = Instance_Axis
    //     0xbc3838: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xbc383c: stur            x1, [fp, #-8]
    // 0xbc3840: StoreField: r1->field_f = r0
    //     0xbc3840: stur            w0, [x1, #0xf]
    // 0xbc3844: r0 = Instance_MainAxisAlignment
    //     0xbc3844: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbc3848: ldr             x0, [x0, #0x730]
    // 0xbc384c: StoreField: r1->field_13 = r0
    //     0xbc384c: stur            w0, [x1, #0x13]
    // 0xbc3850: r0 = Instance_MainAxisSize
    //     0xbc3850: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbc3854: ldr             x0, [x0, #0x738]
    // 0xbc3858: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc3858: stur            w0, [x1, #0x17]
    // 0xbc385c: r0 = Instance_CrossAxisAlignment
    //     0xbc385c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbc3860: ldr             x0, [x0, #0x740]
    // 0xbc3864: StoreField: r1->field_1b = r0
    //     0xbc3864: stur            w0, [x1, #0x1b]
    // 0xbc3868: r0 = Instance_VerticalDirection
    //     0xbc3868: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbc386c: ldr             x0, [x0, #0x748]
    // 0xbc3870: StoreField: r1->field_23 = r0
    //     0xbc3870: stur            w0, [x1, #0x23]
    // 0xbc3874: r0 = Instance_Clip
    //     0xbc3874: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbc3878: ldr             x0, [x0, #0x750]
    // 0xbc387c: StoreField: r1->field_2b = r0
    //     0xbc387c: stur            w0, [x1, #0x2b]
    // 0xbc3880: StoreField: r1->field_2f = rZR
    //     0xbc3880: stur            xzr, [x1, #0x2f]
    // 0xbc3884: ldur            x0, [fp, #-0x10]
    // 0xbc3888: StoreField: r1->field_b = r0
    //     0xbc3888: stur            w0, [x1, #0xb]
    // 0xbc388c: r0 = RefreshIndicator()
    //     0xbc388c: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xbc3890: mov             x3, x0
    // 0xbc3894: ldur            x0, [fp, #-8]
    // 0xbc3898: stur            x3, [fp, #-0x10]
    // 0xbc389c: StoreField: r3->field_b = r0
    //     0xbc389c: stur            w0, [x3, #0xb]
    // 0xbc38a0: d0 = 40.000000
    //     0xbc38a0: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xbc38a4: StoreField: r3->field_f = d0
    //     0xbc38a4: stur            d0, [x3, #0xf]
    // 0xbc38a8: ArrayStore: r3[0] = rZR  ; List_8
    //     0xbc38a8: stur            xzr, [x3, #0x17]
    // 0xbc38ac: ldur            x2, [fp, #-0x30]
    // 0xbc38b0: r1 = Function 'onPageRefresh':.
    //     0xbc38b0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xbc38b4: ldr             x1, [x1, #0xa60]
    // 0xbc38b8: r0 = AllocateClosure()
    //     0xbc38b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc38bc: mov             x1, x0
    // 0xbc38c0: ldur            x0, [fp, #-0x10]
    // 0xbc38c4: StoreField: r0->field_1f = r1
    //     0xbc38c4: stur            w1, [x0, #0x1f]
    // 0xbc38c8: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xbc38c8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xbc38cc: ldr             x1, [x1, #0xf58]
    // 0xbc38d0: StoreField: r0->field_2f = r1
    //     0xbc38d0: stur            w1, [x0, #0x2f]
    // 0xbc38d4: d0 = 2.500000
    //     0xbc38d4: fmov            d0, #2.50000000
    // 0xbc38d8: StoreField: r0->field_3b = d0
    //     0xbc38d8: stur            d0, [x0, #0x3b]
    // 0xbc38dc: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xbc38dc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xbc38e0: ldr             x1, [x1, #0xa68]
    // 0xbc38e4: StoreField: r0->field_47 = r1
    //     0xbc38e4: stur            w1, [x0, #0x47]
    // 0xbc38e8: d0 = 2.000000
    //     0xbc38e8: fmov            d0, #2.00000000
    // 0xbc38ec: StoreField: r0->field_4b = d0
    //     0xbc38ec: stur            d0, [x0, #0x4b]
    // 0xbc38f0: r1 = Instance__IndicatorType
    //     0xbc38f0: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xbc38f4: ldr             x1, [x1, #0xa70]
    // 0xbc38f8: StoreField: r0->field_43 = r1
    //     0xbc38f8: stur            w1, [x0, #0x43]
    // 0xbc38fc: r0 = Scaffold()
    //     0xbc38fc: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xbc3900: ldur            x1, [fp, #-0x18]
    // 0xbc3904: StoreField: r0->field_13 = r1
    //     0xbc3904: stur            w1, [x0, #0x13]
    // 0xbc3908: ldur            x1, [fp, #-0x10]
    // 0xbc390c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc390c: stur            w1, [x0, #0x17]
    // 0xbc3910: r1 = Instance_AlignmentDirectional
    //     0xbc3910: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xbc3914: ldr             x1, [x1, #0x758]
    // 0xbc3918: StoreField: r0->field_2b = r1
    //     0xbc3918: stur            w1, [x0, #0x2b]
    // 0xbc391c: r1 = true
    //     0xbc391c: add             x1, NULL, #0x20  ; true
    // 0xbc3920: StoreField: r0->field_53 = r1
    //     0xbc3920: stur            w1, [x0, #0x53]
    // 0xbc3924: r2 = Instance_DragStartBehavior
    //     0xbc3924: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xbc3928: StoreField: r0->field_57 = r2
    //     0xbc3928: stur            w2, [x0, #0x57]
    // 0xbc392c: r2 = false
    //     0xbc392c: add             x2, NULL, #0x30  ; false
    // 0xbc3930: StoreField: r0->field_b = r2
    //     0xbc3930: stur            w2, [x0, #0xb]
    // 0xbc3934: StoreField: r0->field_f = r2
    //     0xbc3934: stur            w2, [x0, #0xf]
    // 0xbc3938: StoreField: r0->field_5f = r1
    //     0xbc3938: stur            w1, [x0, #0x5f]
    // 0xbc393c: StoreField: r0->field_63 = r1
    //     0xbc393c: stur            w1, [x0, #0x63]
    // 0xbc3940: LeaveFrame
    //     0xbc3940: mov             SP, fp
    //     0xbc3944: ldp             fp, lr, [SP], #0x10
    // 0xbc3948: ret
    //     0xbc3948: ret             
    // 0xbc394c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc394c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc3950: b               #0xbc3554
  }
  [closure] NotificationListener<ScrollNotification> <anonymous closure>(dynamic) {
    // ** addr: 0xbc3954, size: 0x280
    // 0xbc3954: EnterFrame
    //     0xbc3954: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3958: mov             fp, SP
    // 0xbc395c: AllocStack(0x48)
    //     0xbc395c: sub             SP, SP, #0x48
    // 0xbc3960: SetupParameters()
    //     0xbc3960: ldr             x0, [fp, #0x10]
    //     0xbc3964: ldur            w2, [x0, #0x17]
    //     0xbc3968: add             x2, x2, HEAP, lsl #32
    //     0xbc396c: stur            x2, [fp, #-0x10]
    // 0xbc3970: CheckStackOverflow
    //     0xbc3970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc3974: cmp             SP, x16
    //     0xbc3978: b.ls            #0xbc3bac
    // 0xbc397c: LoadField: r0 = r2->field_f
    //     0xbc397c: ldur            w0, [x2, #0xf]
    // 0xbc3980: DecompressPointer r0
    //     0xbc3980: add             x0, x0, HEAP, lsl #32
    // 0xbc3984: stur            x0, [fp, #-8]
    // 0xbc3988: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc3988: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc398c: ldr             x0, [x0, #0x26d0]
    //     0xbc3990: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc3994: cmp             w0, w16
    //     0xbc3998: b.ne            #0xbc39a8
    //     0xbc399c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc39a0: ldr             x2, [x2, #0xb90]
    //     0xbc39a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc39a8: mov             x1, x0
    // 0xbc39ac: ldur            x2, [fp, #-8]
    // 0xbc39b0: stur            x0, [fp, #-0x18]
    // 0xbc39b4: r0 = []()
    //     0xbc39b4: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc39b8: mov             x3, x0
    // 0xbc39bc: ldur            x0, [fp, #-8]
    // 0xbc39c0: stur            x3, [fp, #-0x20]
    // 0xbc39c4: LoadField: r2 = r0->field_b
    //     0xbc39c4: ldur            w2, [x0, #0xb]
    // 0xbc39c8: DecompressPointer r2
    //     0xbc39c8: add             x2, x2, HEAP, lsl #32
    // 0xbc39cc: mov             x0, x3
    // 0xbc39d0: r1 = Null
    //     0xbc39d0: mov             x1, NULL
    // 0xbc39d4: cmp             w2, NULL
    // 0xbc39d8: b.eq            #0xbc39fc
    // 0xbc39dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc39dc: ldur            w4, [x2, #0x17]
    // 0xbc39e0: DecompressPointer r4
    //     0xbc39e0: add             x4, x4, HEAP, lsl #32
    // 0xbc39e4: r8 = X0 bound GetLifeCycleBase?
    //     0xbc39e4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc39e8: ldr             x8, [x8, #0xb98]
    // 0xbc39ec: LoadField: r9 = r4->field_7
    //     0xbc39ec: ldur            x9, [x4, #7]
    // 0xbc39f0: r3 = Null
    //     0xbc39f0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40908] Null
    //     0xbc39f4: ldr             x3, [x3, #0x908]
    // 0xbc39f8: blr             x9
    // 0xbc39fc: ldur            x0, [fp, #-0x10]
    // 0xbc3a00: LoadField: r3 = r0->field_f
    //     0xbc3a00: ldur            w3, [x0, #0xf]
    // 0xbc3a04: DecompressPointer r3
    //     0xbc3a04: add             x3, x3, HEAP, lsl #32
    // 0xbc3a08: ldur            x1, [fp, #-0x18]
    // 0xbc3a0c: mov             x2, x3
    // 0xbc3a10: stur            x3, [fp, #-8]
    // 0xbc3a14: r0 = []()
    //     0xbc3a14: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3a18: mov             x3, x0
    // 0xbc3a1c: ldur            x0, [fp, #-8]
    // 0xbc3a20: stur            x3, [fp, #-0x28]
    // 0xbc3a24: LoadField: r2 = r0->field_b
    //     0xbc3a24: ldur            w2, [x0, #0xb]
    // 0xbc3a28: DecompressPointer r2
    //     0xbc3a28: add             x2, x2, HEAP, lsl #32
    // 0xbc3a2c: mov             x0, x3
    // 0xbc3a30: r1 = Null
    //     0xbc3a30: mov             x1, NULL
    // 0xbc3a34: cmp             w2, NULL
    // 0xbc3a38: b.eq            #0xbc3a5c
    // 0xbc3a3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc3a3c: ldur            w4, [x2, #0x17]
    // 0xbc3a40: DecompressPointer r4
    //     0xbc3a40: add             x4, x4, HEAP, lsl #32
    // 0xbc3a44: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3a44: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3a48: ldr             x8, [x8, #0xb98]
    // 0xbc3a4c: LoadField: r9 = r4->field_7
    //     0xbc3a4c: ldur            x9, [x4, #7]
    // 0xbc3a50: r3 = Null
    //     0xbc3a50: add             x3, PP, #0x40, lsl #12  ; [pp+0x40918] Null
    //     0xbc3a54: ldr             x3, [x3, #0x918]
    // 0xbc3a58: blr             x9
    // 0xbc3a5c: ldur            x1, [fp, #-0x28]
    // 0xbc3a60: r0 = itemsCount()
    //     0xbc3a60: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbc3a64: mov             x3, x0
    // 0xbc3a68: ldur            x0, [fp, #-0x10]
    // 0xbc3a6c: stur            x3, [fp, #-0x30]
    // 0xbc3a70: LoadField: r4 = r0->field_f
    //     0xbc3a70: ldur            w4, [x0, #0xf]
    // 0xbc3a74: DecompressPointer r4
    //     0xbc3a74: add             x4, x4, HEAP, lsl #32
    // 0xbc3a78: ldur            x1, [fp, #-0x18]
    // 0xbc3a7c: mov             x2, x4
    // 0xbc3a80: stur            x4, [fp, #-8]
    // 0xbc3a84: r0 = []()
    //     0xbc3a84: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3a88: mov             x3, x0
    // 0xbc3a8c: ldur            x0, [fp, #-8]
    // 0xbc3a90: stur            x3, [fp, #-0x18]
    // 0xbc3a94: LoadField: r2 = r0->field_b
    //     0xbc3a94: ldur            w2, [x0, #0xb]
    // 0xbc3a98: DecompressPointer r2
    //     0xbc3a98: add             x2, x2, HEAP, lsl #32
    // 0xbc3a9c: mov             x0, x3
    // 0xbc3aa0: r1 = Null
    //     0xbc3aa0: mov             x1, NULL
    // 0xbc3aa4: cmp             w2, NULL
    // 0xbc3aa8: b.eq            #0xbc3acc
    // 0xbc3aac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc3aac: ldur            w4, [x2, #0x17]
    // 0xbc3ab0: DecompressPointer r4
    //     0xbc3ab0: add             x4, x4, HEAP, lsl #32
    // 0xbc3ab4: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3ab4: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3ab8: ldr             x8, [x8, #0xb98]
    // 0xbc3abc: LoadField: r9 = r4->field_7
    //     0xbc3abc: ldur            x9, [x4, #7]
    // 0xbc3ac0: r3 = Null
    //     0xbc3ac0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40928] Null
    //     0xbc3ac4: ldr             x3, [x3, #0x928]
    // 0xbc3ac8: blr             x9
    // 0xbc3acc: ldur            x1, [fp, #-0x18]
    // 0xbc3ad0: r0 = itemsCount()
    //     0xbc3ad0: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xbc3ad4: scvtf           d0, x0
    // 0xbc3ad8: d1 = 10.000000
    //     0xbc3ad8: fmov            d1, #10.00000000
    // 0xbc3adc: fdiv            d2, d0, d1
    // 0xbc3ae0: fcmp            d2, d2
    // 0xbc3ae4: b.vs            #0xbc3bb4
    // 0xbc3ae8: fcvtms          x0, d2
    // 0xbc3aec: asr             x16, x0, #0x1e
    // 0xbc3af0: cmp             x16, x0, asr #63
    // 0xbc3af4: b.ne            #0xbc3bb4
    // 0xbc3af8: lsl             x0, x0, #1
    // 0xbc3afc: r1 = LoadInt32Instr(r0)
    //     0xbc3afc: sbfx            x1, x0, #1, #0x1f
    //     0xbc3b00: tbz             w0, #0, #0xbc3b08
    //     0xbc3b04: ldur            x1, [x0, #7]
    // 0xbc3b08: ldur            x0, [fp, #-0x30]
    // 0xbc3b0c: add             x3, x0, x1
    // 0xbc3b10: stur            x3, [fp, #-0x38]
    // 0xbc3b14: r1 = Function '<anonymous closure>':.
    //     0xbc3b14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40938] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xbc3b18: ldr             x1, [x1, #0x938]
    // 0xbc3b1c: r2 = Null
    //     0xbc3b1c: mov             x2, NULL
    // 0xbc3b20: r0 = AllocateClosure()
    //     0xbc3b20: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc3b24: ldur            x2, [fp, #-0x10]
    // 0xbc3b28: r1 = Function '<anonymous closure>':.
    //     0xbc3b28: add             x1, PP, #0x40, lsl #12  ; [pp+0x40940] AnonymousClosure: (0xbc3bd4), in [package:nuonline/app/modules/article/article_tag/views/article_tag_view.dart] ArticleTagView::build (0xbc3534)
    //     0xbc3b2c: ldr             x1, [x1, #0x940]
    // 0xbc3b30: stur            x0, [fp, #-8]
    // 0xbc3b34: r0 = AllocateClosure()
    //     0xbc3b34: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc3b38: stur            x0, [fp, #-0x10]
    // 0xbc3b3c: r0 = ListView()
    //     0xbc3b3c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xbc3b40: stur            x0, [fp, #-0x18]
    // 0xbc3b44: r16 = true
    //     0xbc3b44: add             x16, NULL, #0x20  ; true
    // 0xbc3b48: r30 = Instance_EdgeInsets
    //     0xbc3b48: add             lr, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xbc3b4c: ldr             lr, [lr, #0x360]
    // 0xbc3b50: stp             lr, x16, [SP]
    // 0xbc3b54: mov             x1, x0
    // 0xbc3b58: ldur            x2, [fp, #-0x10]
    // 0xbc3b5c: ldur            x3, [fp, #-0x38]
    // 0xbc3b60: ldur            x5, [fp, #-8]
    // 0xbc3b64: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xbc3b64: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbc3b68: ldr             x4, [x4, #0x100]
    // 0xbc3b6c: r0 = ListView.separated()
    //     0xbc3b6c: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbc3b70: ldur            x2, [fp, #-0x20]
    // 0xbc3b74: r1 = Function 'onPageScrolled':.
    //     0xbc3b74: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xbc3b78: ldr             x1, [x1, #0xa88]
    // 0xbc3b7c: r0 = AllocateClosure()
    //     0xbc3b7c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc3b80: r1 = <ScrollNotification>
    //     0xbc3b80: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xbc3b84: ldr             x1, [x1, #0x110]
    // 0xbc3b88: stur            x0, [fp, #-8]
    // 0xbc3b8c: r0 = NotificationListener()
    //     0xbc3b8c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xbc3b90: ldur            x1, [fp, #-8]
    // 0xbc3b94: StoreField: r0->field_13 = r1
    //     0xbc3b94: stur            w1, [x0, #0x13]
    // 0xbc3b98: ldur            x1, [fp, #-0x18]
    // 0xbc3b9c: StoreField: r0->field_b = r1
    //     0xbc3b9c: stur            w1, [x0, #0xb]
    // 0xbc3ba0: LeaveFrame
    //     0xbc3ba0: mov             SP, fp
    //     0xbc3ba4: ldp             fp, lr, [SP], #0x10
    // 0xbc3ba8: ret
    //     0xbc3ba8: ret             
    // 0xbc3bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc3bac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc3bb0: b               #0xbc397c
    // 0xbc3bb4: SaveReg d2
    //     0xbc3bb4: str             q2, [SP, #-0x10]!
    // 0xbc3bb8: d0 = 0.000000
    //     0xbc3bb8: fmov            d0, d2
    // 0xbc3bbc: r0 = 68
    //     0xbc3bbc: movz            x0, #0x44
    // 0xbc3bc0: r30 = DoubleToIntegerStub
    //     0xbc3bc0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbc3bc4: LoadField: r30 = r30->field_7
    //     0xbc3bc4: ldur            lr, [lr, #7]
    // 0xbc3bc8: blr             lr
    // 0xbc3bcc: RestoreReg d2
    //     0xbc3bcc: ldr             q2, [SP], #0x10
    // 0xbc3bd0: b               #0xbc3afc
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbc3bd4, size: 0x200
    // 0xbc3bd4: EnterFrame
    //     0xbc3bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3bd8: mov             fp, SP
    // 0xbc3bdc: AllocStack(0x20)
    //     0xbc3bdc: sub             SP, SP, #0x20
    // 0xbc3be0: SetupParameters()
    //     0xbc3be0: fmov            d0, #10.00000000
    //     0xbc3be4: ldr             x0, [fp, #0x20]
    //     0xbc3be8: ldur            w1, [x0, #0x17]
    //     0xbc3bec: add             x1, x1, HEAP, lsl #32
    // 0xbc3be0: d0 = 10.000000
    // 0xbc3bf0: CheckStackOverflow
    //     0xbc3bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc3bf4: cmp             SP, x16
    //     0xbc3bf8: b.ls            #0xbc3d9c
    // 0xbc3bfc: ldr             x0, [fp, #0x10]
    // 0xbc3c00: r2 = LoadInt32Instr(r0)
    //     0xbc3c00: sbfx            x2, x0, #1, #0x1f
    //     0xbc3c04: tbz             w0, #0, #0xbc3c0c
    //     0xbc3c08: ldur            x2, [x0, #7]
    // 0xbc3c0c: stur            x2, [fp, #-0x18]
    // 0xbc3c10: add             x0, x2, #5
    // 0xbc3c14: scvtf           d1, x0
    // 0xbc3c18: fdiv            d2, d1, d0
    // 0xbc3c1c: fcmp            d2, d2
    // 0xbc3c20: b.vs            #0xbc3da4
    // 0xbc3c24: fcvtms          x0, d2
    // 0xbc3c28: asr             x16, x0, #0x1e
    // 0xbc3c2c: cmp             x16, x0, asr #63
    // 0xbc3c30: b.ne            #0xbc3da4
    // 0xbc3c34: lsl             x0, x0, #1
    // 0xbc3c38: r3 = LoadInt32Instr(r0)
    //     0xbc3c38: sbfx            x3, x0, #1, #0x1f
    //     0xbc3c3c: tbz             w0, #0, #0xbc3c44
    //     0xbc3c40: ldur            x3, [x0, #7]
    // 0xbc3c44: sub             x0, x2, x3
    // 0xbc3c48: stur            x0, [fp, #-0x10]
    // 0xbc3c4c: LoadField: r3 = r1->field_f
    //     0xbc3c4c: ldur            w3, [x1, #0xf]
    // 0xbc3c50: DecompressPointer r3
    //     0xbc3c50: add             x3, x3, HEAP, lsl #32
    // 0xbc3c54: stur            x3, [fp, #-8]
    // 0xbc3c58: r0 = InitLateStaticField(0x1368) // [package:get/get_state_manager/src/simple/get_view.dart] GetWidget<X0 bound GetLifeCycleBase?>::_cache
    //     0xbc3c58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc3c5c: ldr             x0, [x0, #0x26d0]
    //     0xbc3c60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc3c64: cmp             w0, w16
    //     0xbc3c68: b.ne            #0xbc3c78
    //     0xbc3c6c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38b90] Field <GetWidget._cache@1281452723>: static late final (offset: 0x1368)
    //     0xbc3c70: ldr             x2, [x2, #0xb90]
    //     0xbc3c74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbc3c78: mov             x1, x0
    // 0xbc3c7c: ldur            x2, [fp, #-8]
    // 0xbc3c80: r0 = []()
    //     0xbc3c80: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0xbc3c84: mov             x3, x0
    // 0xbc3c88: ldur            x0, [fp, #-8]
    // 0xbc3c8c: stur            x3, [fp, #-0x20]
    // 0xbc3c90: LoadField: r2 = r0->field_b
    //     0xbc3c90: ldur            w2, [x0, #0xb]
    // 0xbc3c94: DecompressPointer r2
    //     0xbc3c94: add             x2, x2, HEAP, lsl #32
    // 0xbc3c98: mov             x0, x3
    // 0xbc3c9c: r1 = Null
    //     0xbc3c9c: mov             x1, NULL
    // 0xbc3ca0: cmp             w2, NULL
    // 0xbc3ca4: b.eq            #0xbc3cc8
    // 0xbc3ca8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbc3ca8: ldur            w4, [x2, #0x17]
    // 0xbc3cac: DecompressPointer r4
    //     0xbc3cac: add             x4, x4, HEAP, lsl #32
    // 0xbc3cb0: r8 = X0 bound GetLifeCycleBase?
    //     0xbc3cb0: add             x8, PP, #0x38, lsl #12  ; [pp+0x38b98] TypeParameter: X0 bound GetLifeCycleBase?
    //     0xbc3cb4: ldr             x8, [x8, #0xb98]
    // 0xbc3cb8: LoadField: r9 = r4->field_7
    //     0xbc3cb8: ldur            x9, [x4, #7]
    // 0xbc3cbc: r3 = Null
    //     0xbc3cbc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40948] Null
    //     0xbc3cc0: ldr             x3, [x3, #0x948]
    // 0xbc3cc4: blr             x9
    // 0xbc3cc8: ldur            x1, [fp, #-0x20]
    // 0xbc3ccc: ldur            x2, [fp, #-0x10]
    // 0xbc3cd0: r0 = find()
    //     0xbc3cd0: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xbc3cd4: stur            x0, [fp, #-8]
    // 0xbc3cd8: cmp             w0, NULL
    // 0xbc3cdc: b.ne            #0xbc3cf4
    // 0xbc3ce0: r0 = Instance_NArticleListTile
    //     0xbc3ce0: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xbc3ce4: ldr             x0, [x0, #0xa90]
    // 0xbc3ce8: LeaveFrame
    //     0xbc3ce8: mov             SP, fp
    //     0xbc3cec: ldp             fp, lr, [SP], #0x10
    // 0xbc3cf0: ret
    //     0xbc3cf0: ret             
    // 0xbc3cf4: ldur            x1, [fp, #-0x18]
    // 0xbc3cf8: r2 = 10
    //     0xbc3cf8: movz            x2, #0xa
    // 0xbc3cfc: sdiv            x4, x1, x2
    // 0xbc3d00: msub            x3, x4, x2, x1
    // 0xbc3d04: cmp             x3, xzr
    // 0xbc3d08: b.lt            #0xbc3dcc
    // 0xbc3d0c: cmp             x3, #4
    // 0xbc3d10: b.ne            #0xbc3d7c
    // 0xbc3d14: r0 = find()
    //     0xbc3d14: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc3d18: mov             x1, x0
    // 0xbc3d1c: r0 = _adsVisibility()
    //     0xbc3d1c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc3d20: mov             x2, x0
    // 0xbc3d24: r1 = Null
    //     0xbc3d24: mov             x1, NULL
    // 0xbc3d28: r0 = AdsConfig.fromJson()
    //     0xbc3d28: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc3d2c: LoadField: r1 = r0->field_b
    //     0xbc3d2c: ldur            w1, [x0, #0xb]
    // 0xbc3d30: DecompressPointer r1
    //     0xbc3d30: add             x1, x1, HEAP, lsl #32
    // 0xbc3d34: LoadField: r0 = r1->field_7
    //     0xbc3d34: ldur            w0, [x1, #7]
    // 0xbc3d38: DecompressPointer r0
    //     0xbc3d38: add             x0, x0, HEAP, lsl #32
    // 0xbc3d3c: tbnz            w0, #4, #0xbc3d68
    // 0xbc3d40: r0 = find()
    //     0xbc3d40: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xbc3d44: mov             x1, x0
    // 0xbc3d48: r0 = _adsVisibility()
    //     0xbc3d48: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xbc3d4c: mov             x2, x0
    // 0xbc3d50: r1 = Null
    //     0xbc3d50: mov             x1, NULL
    // 0xbc3d54: r0 = AdsConfig.fromJson()
    //     0xbc3d54: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xbc3d58: r0 = AdmobArticleNativeWidget()
    //     0xbc3d58: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xbc3d5c: LeaveFrame
    //     0xbc3d5c: mov             SP, fp
    //     0xbc3d60: ldp             fp, lr, [SP], #0x10
    // 0xbc3d64: ret
    //     0xbc3d64: ret             
    // 0xbc3d68: r0 = Instance_SizedBox
    //     0xbc3d68: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xbc3d6c: ldr             x0, [x0, #0xc40]
    // 0xbc3d70: LeaveFrame
    //     0xbc3d70: mov             SP, fp
    //     0xbc3d74: ldp             fp, lr, [SP], #0x10
    // 0xbc3d78: ret
    //     0xbc3d78: ret             
    // 0xbc3d7c: r0 = ArticleItem()
    //     0xbc3d7c: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xbc3d80: ldur            x1, [fp, #-8]
    // 0xbc3d84: StoreField: r0->field_b = r1
    //     0xbc3d84: stur            w1, [x0, #0xb]
    // 0xbc3d88: r1 = false
    //     0xbc3d88: add             x1, NULL, #0x30  ; false
    // 0xbc3d8c: StoreField: r0->field_13 = r1
    //     0xbc3d8c: stur            w1, [x0, #0x13]
    // 0xbc3d90: LeaveFrame
    //     0xbc3d90: mov             SP, fp
    //     0xbc3d94: ldp             fp, lr, [SP], #0x10
    // 0xbc3d98: ret
    //     0xbc3d98: ret             
    // 0xbc3d9c: r0 = StackOverflowSharedWithFPURegs()
    //     0xbc3d9c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xbc3da0: b               #0xbc3bfc
    // 0xbc3da4: SaveReg d2
    //     0xbc3da4: str             q2, [SP, #-0x10]!
    // 0xbc3da8: stp             x1, x2, [SP, #-0x10]!
    // 0xbc3dac: d0 = 0.000000
    //     0xbc3dac: fmov            d0, d2
    // 0xbc3db0: r0 = 68
    //     0xbc3db0: movz            x0, #0x44
    // 0xbc3db4: r30 = DoubleToIntegerStub
    //     0xbc3db4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbc3db8: LoadField: r30 = r30->field_7
    //     0xbc3db8: ldur            lr, [lr, #7]
    // 0xbc3dbc: blr             lr
    // 0xbc3dc0: ldp             x1, x2, [SP], #0x10
    // 0xbc3dc4: RestoreReg d2
    //     0xbc3dc4: ldr             q2, [SP], #0x10
    // 0xbc3dc8: b               #0xbc3c38
    // 0xbc3dcc: add             x3, x3, x2
    // 0xbc3dd0: b               #0xbc3d0c
  }
}
