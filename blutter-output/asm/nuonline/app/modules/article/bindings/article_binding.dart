// lib: , url: package:nuonline/app/modules/article/bindings/article_binding.dart

// class id: 1050145, size: 0x8
class :: {
}

// class id: 2190, size: 0x8, field offset: 0x8
class ArticleBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80e4ec, size: 0x70
    // 0x80e4ec: EnterFrame
    //     0x80e4ec: stp             fp, lr, [SP, #-0x10]!
    //     0x80e4f0: mov             fp, SP
    // 0x80e4f4: AllocStack(0x10)
    //     0x80e4f4: sub             SP, SP, #0x10
    // 0x80e4f8: CheckStackOverflow
    //     0x80e4f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e4fc: cmp             SP, x16
    //     0x80e500: b.ls            #0x80e554
    // 0x80e504: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e504: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e508: ldr             x0, [x0, #0x2670]
    //     0x80e50c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e510: cmp             w0, w16
    //     0x80e514: b.ne            #0x80e520
    //     0x80e518: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e51c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e520: r1 = Function '<anonymous closure>':.
    //     0x80e520: add             x1, PP, #0x36, lsl #12  ; [pp+0x364b8] AnonymousClosure: (0x80e55c), in [package:nuonline/app/modules/article/bindings/article_binding.dart] ArticleBinding::dependencies (0x80e4ec)
    //     0x80e524: ldr             x1, [x1, #0x4b8]
    // 0x80e528: r2 = Null
    //     0x80e528: mov             x2, NULL
    // 0x80e52c: r0 = AllocateClosure()
    //     0x80e52c: bl              #0xec1630  ; AllocateClosureStub
    // 0x80e530: r16 = <ArticleController>
    //     0x80e530: add             x16, PP, #0x24, lsl #12  ; [pp+0x24d08] TypeArguments: <ArticleController>
    //     0x80e534: ldr             x16, [x16, #0xd08]
    // 0x80e538: stp             x0, x16, [SP]
    // 0x80e53c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80e53c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80e540: r0 = Inst.lazyPut()
    //     0x80e540: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80e544: r0 = Null
    //     0x80e544: mov             x0, NULL
    // 0x80e548: LeaveFrame
    //     0x80e548: mov             SP, fp
    //     0x80e54c: ldp             fp, lr, [SP], #0x10
    // 0x80e550: ret
    //     0x80e550: ret             
    // 0x80e554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e554: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e558: b               #0x80e504
  }
  [closure] ArticleController <anonymous closure>(dynamic) {
    // ** addr: 0x80e55c, size: 0x80
    // 0x80e55c: EnterFrame
    //     0x80e55c: stp             fp, lr, [SP, #-0x10]!
    //     0x80e560: mov             fp, SP
    // 0x80e564: AllocStack(0x18)
    //     0x80e564: sub             SP, SP, #0x18
    // 0x80e568: CheckStackOverflow
    //     0x80e568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e56c: cmp             SP, x16
    //     0x80e570: b.ls            #0x80e5d4
    // 0x80e574: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e574: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e578: ldr             x0, [x0, #0x2670]
    //     0x80e57c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e580: cmp             w0, w16
    //     0x80e584: b.ne            #0x80e590
    //     0x80e588: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e58c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e590: r16 = <ArticleRepository>
    //     0x80e590: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0x80e594: ldr             x16, [x16, #0x98]
    // 0x80e598: r30 = "article_repo"
    //     0x80e598: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0x80e59c: ldr             lr, [lr, #0xa0]
    // 0x80e5a0: stp             lr, x16, [SP]
    // 0x80e5a4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80e5a4: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80e5a8: r0 = Inst.find()
    //     0x80e5a8: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80e5ac: stur            x0, [fp, #-8]
    // 0x80e5b0: r0 = ArticleController()
    //     0x80e5b0: bl              #0x80e758  ; AllocateArticleControllerStub -> ArticleController (size=0x44)
    // 0x80e5b4: mov             x1, x0
    // 0x80e5b8: ldur            x2, [fp, #-8]
    // 0x80e5bc: stur            x0, [fp, #-8]
    // 0x80e5c0: r0 = ArticleController()
    //     0x80e5c0: bl              #0x80e5dc  ; [package:nuonline/app/modules/article/controllers/article_controller.dart] ArticleController::ArticleController
    // 0x80e5c4: ldur            x0, [fp, #-8]
    // 0x80e5c8: LeaveFrame
    //     0x80e5c8: mov             SP, fp
    //     0x80e5cc: ldp             fp, lr, [SP], #0x10
    // 0x80e5d0: ret
    //     0x80e5d0: ret             
    // 0x80e5d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e5d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e5d8: b               #0x80e574
  }
}
