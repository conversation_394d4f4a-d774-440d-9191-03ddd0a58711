// lib: , url: package:nuonline/app/modules/article/bindings/main_article_binding.dart

// class id: 1050146, size: 0x8
class :: {
}

// class id: 2189, size: 0x8, field offset: 0x8
class MainArticleBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80e764, size: 0x70
    // 0x80e764: EnterFrame
    //     0x80e764: stp             fp, lr, [SP, #-0x10]!
    //     0x80e768: mov             fp, SP
    // 0x80e76c: AllocStack(0x10)
    //     0x80e76c: sub             SP, SP, #0x10
    // 0x80e770: CheckStackOverflow
    //     0x80e770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e774: cmp             SP, x16
    //     0x80e778: b.ls            #0x80e7cc
    // 0x80e77c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e77c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e780: ldr             x0, [x0, #0x2670]
    //     0x80e784: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e788: cmp             w0, w16
    //     0x80e78c: b.ne            #0x80e798
    //     0x80e790: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e794: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e798: r1 = Function '<anonymous closure>':.
    //     0x80e798: add             x1, PP, #0x33, lsl #12  ; [pp+0x33a68] AnonymousClosure: (0x80e7d4), in [package:nuonline/app/modules/article/bindings/main_article_binding.dart] MainArticleBinding::dependencies (0x80e764)
    //     0x80e79c: ldr             x1, [x1, #0xa68]
    // 0x80e7a0: r2 = Null
    //     0x80e7a0: mov             x2, NULL
    // 0x80e7a4: r0 = AllocateClosure()
    //     0x80e7a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x80e7a8: r16 = <MainArticleController>
    //     0x80e7a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cee0] TypeArguments: <MainArticleController>
    //     0x80e7ac: ldr             x16, [x16, #0xee0]
    // 0x80e7b0: stp             x0, x16, [SP]
    // 0x80e7b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80e7b4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80e7b8: r0 = Inst.lazyPut()
    //     0x80e7b8: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80e7bc: r0 = Null
    //     0x80e7bc: mov             x0, NULL
    // 0x80e7c0: LeaveFrame
    //     0x80e7c0: mov             SP, fp
    //     0x80e7c4: ldp             fp, lr, [SP], #0x10
    // 0x80e7c8: ret
    //     0x80e7c8: ret             
    // 0x80e7cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e7cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e7d0: b               #0x80e77c
  }
  [closure] MainArticleController <anonymous closure>(dynamic) {
    // ** addr: 0x80e7d4, size: 0x80
    // 0x80e7d4: EnterFrame
    //     0x80e7d4: stp             fp, lr, [SP, #-0x10]!
    //     0x80e7d8: mov             fp, SP
    // 0x80e7dc: AllocStack(0x18)
    //     0x80e7dc: sub             SP, SP, #0x18
    // 0x80e7e0: CheckStackOverflow
    //     0x80e7e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e7e4: cmp             SP, x16
    //     0x80e7e8: b.ls            #0x80e84c
    // 0x80e7ec: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80e7ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80e7f0: ldr             x0, [x0, #0x2670]
    //     0x80e7f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80e7f8: cmp             w0, w16
    //     0x80e7fc: b.ne            #0x80e808
    //     0x80e800: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80e804: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80e808: r16 = <CategoryRepository>
    //     0x80e808: add             x16, PP, #0x10, lsl #12  ; [pp+0x10068] TypeArguments: <CategoryRepository>
    //     0x80e80c: ldr             x16, [x16, #0x68]
    // 0x80e810: r30 = "category_repo"
    //     0x80e810: add             lr, PP, #0x10, lsl #12  ; [pp+0x10070] "category_repo"
    //     0x80e814: ldr             lr, [lr, #0x70]
    // 0x80e818: stp             lr, x16, [SP]
    // 0x80e81c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80e81c: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80e820: r0 = Inst.find()
    //     0x80e820: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80e824: stur            x0, [fp, #-8]
    // 0x80e828: r0 = MainArticleController()
    //     0x80e828: bl              #0x80e9f4  ; AllocateMainArticleControllerStub -> MainArticleController (size=0x40)
    // 0x80e82c: mov             x1, x0
    // 0x80e830: ldur            x2, [fp, #-8]
    // 0x80e834: stur            x0, [fp, #-8]
    // 0x80e838: r0 = MainArticleController()
    //     0x80e838: bl              #0x80e854  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::MainArticleController
    // 0x80e83c: ldur            x0, [fp, #-8]
    // 0x80e840: LeaveFrame
    //     0x80e840: mov             SP, fp
    //     0x80e844: ldp             fp, lr, [SP], #0x10
    // 0x80e848: ret
    //     0x80e848: ret             
    // 0x80e84c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e84c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e850: b               #0x80e7ec
  }
}
