// lib: , url: package:nuonline/app/modules/article/controllers/main_article_controller.dart

// class id: 1050148, size: 0x8
class :: {
}

// class id: 2036, size: 0x40, field offset: 0x24
class MainArticleController extends _DoaController&GetxController&GetSingleTickerProviderStateMixin&AnalyticMixin {

  late TabController tabController; // offset: 0x28

  _ MainArticleController(/* No info */) {
    // ** addr: 0x80e854, size: 0x180
    // 0x80e854: EnterFrame
    //     0x80e854: stp             fp, lr, [SP, #-0x10]!
    //     0x80e858: mov             fp, SP
    // 0x80e85c: AllocStack(0x20)
    //     0x80e85c: sub             SP, SP, #0x20
    // 0x80e860: r0 = Sentinel
    //     0x80e860: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80e864: mov             x4, x1
    // 0x80e868: mov             x3, x2
    // 0x80e86c: stur            x1, [fp, #-8]
    // 0x80e870: stur            x2, [fp, #-0x10]
    // 0x80e874: CheckStackOverflow
    //     0x80e874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80e878: cmp             SP, x16
    //     0x80e87c: b.ls            #0x80e9cc
    // 0x80e880: StoreField: r4->field_27 = r0
    //     0x80e880: stur            w0, [x4, #0x27]
    // 0x80e884: r1 = <Category>
    //     0x80e884: ldr             x1, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80e888: r2 = 0
    //     0x80e888: movz            x2, #0
    // 0x80e88c: r0 = _GrowableList()
    //     0x80e88c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x80e890: r16 = <Category>
    //     0x80e890: ldr             x16, [PP, #0x7b58]  ; [pp+0x7b58] TypeArguments: <Category>
    // 0x80e894: stp             x0, x16, [SP]
    // 0x80e898: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80e898: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80e89c: r0 = ListExtension.obs()
    //     0x80e89c: bl              #0x80c514  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x80e8a0: ldur            x2, [fp, #-8]
    // 0x80e8a4: StoreField: r2->field_2b = r0
    //     0x80e8a4: stur            w0, [x2, #0x2b]
    //     0x80e8a8: ldurb           w16, [x2, #-1]
    //     0x80e8ac: ldurb           w17, [x0, #-1]
    //     0x80e8b0: and             x16, x17, x16, lsr #2
    //     0x80e8b4: tst             x16, HEAP, lsr #32
    //     0x80e8b8: b.eq            #0x80e8c0
    //     0x80e8bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80e8c0: r1 = 0
    //     0x80e8c0: movz            x1, #0
    // 0x80e8c4: r0 = IntExtension.obs()
    //     0x80e8c4: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80e8c8: ldur            x2, [fp, #-8]
    // 0x80e8cc: StoreField: r2->field_2f = r0
    //     0x80e8cc: stur            w0, [x2, #0x2f]
    //     0x80e8d0: ldurb           w16, [x2, #-1]
    //     0x80e8d4: ldurb           w17, [x0, #-1]
    //     0x80e8d8: and             x16, x17, x16, lsr #2
    //     0x80e8dc: tst             x16, HEAP, lsr #32
    //     0x80e8e0: b.eq            #0x80e8e8
    //     0x80e8e4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80e8e8: r1 = 0
    //     0x80e8e8: movz            x1, #0
    // 0x80e8ec: r0 = IntExtension.obs()
    //     0x80e8ec: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80e8f0: ldur            x2, [fp, #-8]
    // 0x80e8f4: StoreField: r2->field_33 = r0
    //     0x80e8f4: stur            w0, [x2, #0x33]
    //     0x80e8f8: ldurb           w16, [x2, #-1]
    //     0x80e8fc: ldurb           w17, [x0, #-1]
    //     0x80e900: and             x16, x17, x16, lsr #2
    //     0x80e904: tst             x16, HEAP, lsr #32
    //     0x80e908: b.eq            #0x80e910
    //     0x80e90c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80e910: r1 = 0
    //     0x80e910: movz            x1, #0
    // 0x80e914: r0 = IntExtension.obs()
    //     0x80e914: bl              #0x80cac0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x80e918: ldur            x3, [fp, #-8]
    // 0x80e91c: StoreField: r3->field_37 = r0
    //     0x80e91c: stur            w0, [x3, #0x37]
    //     0x80e920: ldurb           w16, [x3, #-1]
    //     0x80e924: ldurb           w17, [x0, #-1]
    //     0x80e928: and             x16, x17, x16, lsr #2
    //     0x80e92c: tst             x16, HEAP, lsr #32
    //     0x80e930: b.eq            #0x80e938
    //     0x80e934: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x80e938: r1 = Null
    //     0x80e938: mov             x1, NULL
    // 0x80e93c: r2 = 8
    //     0x80e93c: movz            x2, #0x8
    // 0x80e940: r0 = AllocateArray()
    //     0x80e940: bl              #0xec22fc  ; AllocateArrayStub
    // 0x80e944: StoreField: r0->field_f = rZR
    //     0x80e944: stur            wzr, [x0, #0xf]
    // 0x80e948: r16 = "utama"
    //     0x80e948: add             x16, PP, #0x33, lsl #12  ; [pp+0x33a70] "utama"
    //     0x80e94c: ldr             x16, [x16, #0xa70]
    // 0x80e950: StoreField: r0->field_13 = r16
    //     0x80e950: stur            w16, [x0, #0x13]
    // 0x80e954: r16 = 2
    //     0x80e954: movz            x16, #0x2
    // 0x80e958: ArrayStore: r0[0] = r16  ; List_4
    //     0x80e958: stur            w16, [x0, #0x17]
    // 0x80e95c: r16 = "keislaman"
    //     0x80e95c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33a78] "keislaman"
    //     0x80e960: ldr             x16, [x16, #0xa78]
    // 0x80e964: StoreField: r0->field_1b = r16
    //     0x80e964: stur            w16, [x0, #0x1b]
    // 0x80e968: r16 = <int, String>
    //     0x80e968: add             x16, PP, #0x31, lsl #12  ; [pp+0x316d8] TypeArguments: <int, String>
    //     0x80e96c: ldr             x16, [x16, #0x6d8]
    // 0x80e970: stp             x0, x16, [SP]
    // 0x80e974: r0 = Map._fromLiteral()
    //     0x80e974: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x80e978: ldur            x1, [fp, #-8]
    // 0x80e97c: StoreField: r1->field_3b = r0
    //     0x80e97c: stur            w0, [x1, #0x3b]
    //     0x80e980: ldurb           w16, [x1, #-1]
    //     0x80e984: ldurb           w17, [x0, #-1]
    //     0x80e988: and             x16, x17, x16, lsr #2
    //     0x80e98c: tst             x16, HEAP, lsr #32
    //     0x80e990: b.eq            #0x80e998
    //     0x80e994: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e998: ldur            x0, [fp, #-0x10]
    // 0x80e99c: StoreField: r1->field_23 = r0
    //     0x80e99c: stur            w0, [x1, #0x23]
    //     0x80e9a0: ldurb           w16, [x1, #-1]
    //     0x80e9a4: ldurb           w17, [x0, #-1]
    //     0x80e9a8: and             x16, x17, x16, lsr #2
    //     0x80e9ac: tst             x16, HEAP, lsr #32
    //     0x80e9b0: b.eq            #0x80e9b8
    //     0x80e9b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80e9b8: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80e9b8: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80e9bc: r0 = Null
    //     0x80e9bc: mov             x0, NULL
    // 0x80e9c0: LeaveFrame
    //     0x80e9c0: mov             SP, fp
    //     0x80e9c4: ldp             fp, lr, [SP], #0x10
    // 0x80e9c8: ret
    //     0x80e9c8: ret             
    // 0x80e9cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80e9cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80e9d0: b               #0x80e880
  }
  [closure] int <anonymous closure>(dynamic, Category, Category) {
    // ** addr: 0x8ab634, size: 0x78
    // 0x8ab634: EnterFrame
    //     0x8ab634: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab638: mov             fp, SP
    // 0x8ab63c: CheckStackOverflow
    //     0x8ab63c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab640: cmp             SP, x16
    //     0x8ab644: b.ls            #0x8ab6a4
    // 0x8ab648: ldr             x0, [fp, #0x18]
    // 0x8ab64c: LoadField: r2 = r0->field_27
    //     0x8ab64c: ldur            x2, [x0, #0x27]
    // 0x8ab650: ldr             x0, [fp, #0x10]
    // 0x8ab654: LoadField: r3 = r0->field_27
    //     0x8ab654: ldur            x3, [x0, #0x27]
    // 0x8ab658: r0 = BoxInt64Instr(r2)
    //     0x8ab658: sbfiz           x0, x2, #1, #0x1f
    //     0x8ab65c: cmp             x2, x0, asr #1
    //     0x8ab660: b.eq            #0x8ab66c
    //     0x8ab664: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ab668: stur            x2, [x0, #7]
    // 0x8ab66c: mov             x2, x0
    // 0x8ab670: r0 = BoxInt64Instr(r3)
    //     0x8ab670: sbfiz           x0, x3, #1, #0x1f
    //     0x8ab674: cmp             x3, x0, asr #1
    //     0x8ab678: b.eq            #0x8ab684
    //     0x8ab67c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ab680: stur            x3, [x0, #7]
    // 0x8ab684: mov             x1, x2
    // 0x8ab688: mov             x2, x0
    // 0x8ab68c: r0 = compareTo()
    //     0x8ab68c: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0x8ab690: lsl             x1, x0, #1
    // 0x8ab694: mov             x0, x1
    // 0x8ab698: LeaveFrame
    //     0x8ab698: mov             SP, fp
    //     0x8ab69c: ldp             fp, lr, [SP], #0x10
    // 0x8ab6a0: ret
    //     0x8ab6a0: ret             
    // 0x8ab6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab6a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab6a8: b               #0x8ab648
  }
  get _ islamicChannels(/* No info */) {
    // ** addr: 0x8ab6ac, size: 0xa4
    // 0x8ab6ac: EnterFrame
    //     0x8ab6ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab6b0: mov             fp, SP
    // 0x8ab6b4: AllocStack(0x10)
    //     0x8ab6b4: sub             SP, SP, #0x10
    // 0x8ab6b8: CheckStackOverflow
    //     0x8ab6b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab6bc: cmp             SP, x16
    //     0x8ab6c0: b.ls            #0x8ab748
    // 0x8ab6c4: LoadField: r0 = r1->field_2b
    //     0x8ab6c4: ldur            w0, [x1, #0x2b]
    // 0x8ab6c8: DecompressPointer r0
    //     0x8ab6c8: add             x0, x0, HEAP, lsl #32
    // 0x8ab6cc: stur            x0, [fp, #-8]
    // 0x8ab6d0: r1 = Function '<anonymous closure>':.
    //     0x8ab6d0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36368] AnonymousClosure: static (0x6380b8), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onSecondaryFixedVariant (0x637f08)
    //     0x8ab6d4: ldr             x1, [x1, #0x368]
    // 0x8ab6d8: r2 = Null
    //     0x8ab6d8: mov             x2, NULL
    // 0x8ab6dc: r0 = AllocateClosure()
    //     0x8ab6dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab6e0: ldur            x1, [fp, #-8]
    // 0x8ab6e4: mov             x2, x0
    // 0x8ab6e8: r0 = where()
    //     0x8ab6e8: bl              #0x7e5f9c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::where
    // 0x8ab6ec: r1 = LoadClassIdInstr(r0)
    //     0x8ab6ec: ldur            x1, [x0, #-1]
    //     0x8ab6f0: ubfx            x1, x1, #0xc, #0x14
    // 0x8ab6f4: mov             x16, x0
    // 0x8ab6f8: mov             x0, x1
    // 0x8ab6fc: mov             x1, x16
    // 0x8ab700: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ab700: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ab704: r0 = GDT[cid_x0 + 0xd889]()
    //     0x8ab704: movz            x17, #0xd889
    //     0x8ab708: add             lr, x0, x17
    //     0x8ab70c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ab710: blr             lr
    // 0x8ab714: r1 = Function '<anonymous closure>':.
    //     0x8ab714: add             x1, PP, #0x36, lsl #12  ; [pp+0x36370] AnonymousClosure: (0x8ab634), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels (0x8ab6ac)
    //     0x8ab718: ldr             x1, [x1, #0x370]
    // 0x8ab71c: r2 = Null
    //     0x8ab71c: mov             x2, NULL
    // 0x8ab720: stur            x0, [fp, #-8]
    // 0x8ab724: r0 = AllocateClosure()
    //     0x8ab724: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab728: str             x0, [SP]
    // 0x8ab72c: ldur            x1, [fp, #-8]
    // 0x8ab730: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8ab730: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8ab734: r0 = sort()
    //     0x8ab734: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x8ab738: ldur            x0, [fp, #-8]
    // 0x8ab73c: LeaveFrame
    //     0x8ab73c: mov             SP, fp
    //     0x8ab740: ldp             fp, lr, [SP], #0x10
    // 0x8ab744: ret
    //     0x8ab744: ret             
    // 0x8ab748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab748: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab74c: b               #0x8ab6c4
  }
  _ onInit(/* No info */) {
    // ** addr: 0x8ab914, size: 0xac
    // 0x8ab914: EnterFrame
    //     0x8ab914: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab918: mov             fp, SP
    // 0x8ab91c: AllocStack(0x18)
    //     0x8ab91c: sub             SP, SP, #0x18
    // 0x8ab920: SetupParameters(MainArticleController this /* r1 => r1, fp-0x8 */)
    //     0x8ab920: stur            x1, [fp, #-8]
    // 0x8ab924: CheckStackOverflow
    //     0x8ab924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab928: cmp             SP, x16
    //     0x8ab92c: b.ls            #0x8ab9b8
    // 0x8ab930: r1 = 1
    //     0x8ab930: movz            x1, #0x1
    // 0x8ab934: r0 = AllocateContext()
    //     0x8ab934: bl              #0xec126c  ; AllocateContextStub
    // 0x8ab938: mov             x2, x0
    // 0x8ab93c: ldur            x0, [fp, #-8]
    // 0x8ab940: stur            x2, [fp, #-0x10]
    // 0x8ab944: StoreField: r2->field_f = r0
    //     0x8ab944: stur            w0, [x2, #0xf]
    // 0x8ab948: mov             x1, x0
    // 0x8ab94c: r0 = onInit()
    //     0x8ab94c: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8ab950: r0 = TabController()
    //     0x8ab950: bl              #0x8a9838  ; AllocateTabControllerStub -> TabController (size=0x4c)
    // 0x8ab954: mov             x1, x0
    // 0x8ab958: ldur            x3, [fp, #-8]
    // 0x8ab95c: r2 = 2
    //     0x8ab95c: movz            x2, #0x2
    // 0x8ab960: stur            x0, [fp, #-0x18]
    // 0x8ab964: r0 = TabController()
    //     0x8ab964: bl              #0x8a9730  ; [package:flutter/src/material/tab_controller.dart] TabController::TabController
    // 0x8ab968: ldur            x0, [fp, #-0x18]
    // 0x8ab96c: ldur            x1, [fp, #-8]
    // 0x8ab970: StoreField: r1->field_27 = r0
    //     0x8ab970: stur            w0, [x1, #0x27]
    //     0x8ab974: ldurb           w16, [x1, #-1]
    //     0x8ab978: ldurb           w17, [x0, #-1]
    //     0x8ab97c: and             x16, x17, x16, lsr #2
    //     0x8ab980: tst             x16, HEAP, lsr #32
    //     0x8ab984: b.eq            #0x8ab98c
    //     0x8ab988: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8ab98c: ldur            x2, [fp, #-0x10]
    // 0x8ab990: r1 = Function '<anonymous closure>':.
    //     0x8ab990: add             x1, PP, #0x40, lsl #12  ; [pp+0x408a0] AnonymousClosure: (0x8ab9c0), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onInit (0x8ab914)
    //     0x8ab994: ldr             x1, [x1, #0x8a0]
    // 0x8ab998: r0 = AllocateClosure()
    //     0x8ab998: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ab99c: ldur            x1, [fp, #-0x18]
    // 0x8ab9a0: mov             x2, x0
    // 0x8ab9a4: r0 = addListener()
    //     0x8ab9a4: bl              #0xa7a80c  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x8ab9a8: r0 = Null
    //     0x8ab9a8: mov             x0, NULL
    // 0x8ab9ac: LeaveFrame
    //     0x8ab9ac: mov             SP, fp
    //     0x8ab9b0: ldp             fp, lr, [SP], #0x10
    // 0x8ab9b4: ret
    //     0x8ab9b4: ret             
    // 0x8ab9b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab9b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab9bc: b               #0x8ab930
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8ab9c0, size: 0xa4
    // 0x8ab9c0: EnterFrame
    //     0x8ab9c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab9c4: mov             fp, SP
    // 0x8ab9c8: AllocStack(0x8)
    //     0x8ab9c8: sub             SP, SP, #8
    // 0x8ab9cc: SetupParameters()
    //     0x8ab9cc: ldr             x0, [fp, #0x10]
    //     0x8ab9d0: ldur            w3, [x0, #0x17]
    //     0x8ab9d4: add             x3, x3, HEAP, lsl #32
    //     0x8ab9d8: stur            x3, [fp, #-8]
    // 0x8ab9dc: CheckStackOverflow
    //     0x8ab9dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab9e0: cmp             SP, x16
    //     0x8ab9e4: b.ls            #0x8aba50
    // 0x8ab9e8: LoadField: r0 = r3->field_f
    //     0x8ab9e8: ldur            w0, [x3, #0xf]
    // 0x8ab9ec: DecompressPointer r0
    //     0x8ab9ec: add             x0, x0, HEAP, lsl #32
    // 0x8ab9f0: LoadField: r2 = r0->field_37
    //     0x8ab9f0: ldur            w2, [x0, #0x37]
    // 0x8ab9f4: DecompressPointer r2
    //     0x8ab9f4: add             x2, x2, HEAP, lsl #32
    // 0x8ab9f8: LoadField: r1 = r0->field_27
    //     0x8ab9f8: ldur            w1, [x0, #0x27]
    // 0x8ab9fc: DecompressPointer r1
    //     0x8ab9fc: add             x1, x1, HEAP, lsl #32
    // 0x8aba00: r16 = Sentinel
    //     0x8aba00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8aba04: cmp             w1, w16
    // 0x8aba08: b.eq            #0x8aba58
    // 0x8aba0c: LoadField: r4 = r1->field_33
    //     0x8aba0c: ldur            x4, [x1, #0x33]
    // 0x8aba10: r0 = BoxInt64Instr(r4)
    //     0x8aba10: sbfiz           x0, x4, #1, #0x1f
    //     0x8aba14: cmp             x4, x0, asr #1
    //     0x8aba18: b.eq            #0x8aba24
    //     0x8aba1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8aba20: stur            x4, [x0, #7]
    // 0x8aba24: mov             x1, x2
    // 0x8aba28: mov             x2, x0
    // 0x8aba2c: r0 = value=()
    //     0x8aba2c: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8aba30: ldur            x0, [fp, #-8]
    // 0x8aba34: LoadField: r1 = r0->field_f
    //     0x8aba34: ldur            w1, [x0, #0xf]
    // 0x8aba38: DecompressPointer r1
    //     0x8aba38: add             x1, x1, HEAP, lsl #32
    // 0x8aba3c: r0 = logAnalytic()
    //     0x8aba3c: bl              #0x8aba64  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::logAnalytic
    // 0x8aba40: r0 = Null
    //     0x8aba40: mov             x0, NULL
    // 0x8aba44: LeaveFrame
    //     0x8aba44: mov             SP, fp
    //     0x8aba48: ldp             fp, lr, [SP], #0x10
    // 0x8aba4c: ret
    //     0x8aba4c: ret             
    // 0x8aba50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aba50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aba54: b               #0x8ab9e8
    // 0x8aba58: r9 = tabController
    //     0x8aba58: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0x8aba5c: ldr             x9, [x9, #0x2c8]
    // 0x8aba60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8aba60: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ logAnalytic(/* No info */) {
    // ** addr: 0x8aba64, size: 0x64
    // 0x8aba64: EnterFrame
    //     0x8aba64: stp             fp, lr, [SP, #-0x10]!
    //     0x8aba68: mov             fp, SP
    // 0x8aba6c: AllocStack(0x8)
    //     0x8aba6c: sub             SP, SP, #8
    // 0x8aba70: SetupParameters(MainArticleController this /* r1 => r1, fp-0x8 */)
    //     0x8aba70: stur            x1, [fp, #-8]
    // 0x8aba74: CheckStackOverflow
    //     0x8aba74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aba78: cmp             SP, x16
    //     0x8aba7c: b.ls            #0x8abac0
    // 0x8aba80: r1 = 1
    //     0x8aba80: movz            x1, #0x1
    // 0x8aba84: r0 = AllocateContext()
    //     0x8aba84: bl              #0xec126c  ; AllocateContextStub
    // 0x8aba88: mov             x1, x0
    // 0x8aba8c: ldur            x0, [fp, #-8]
    // 0x8aba90: StoreField: r1->field_f = r0
    //     0x8aba90: stur            w0, [x1, #0xf]
    // 0x8aba94: mov             x2, x1
    // 0x8aba98: r1 = Function '<anonymous closure>':.
    //     0x8aba98: add             x1, PP, #0x40, lsl #12  ; [pp+0x408a8] AnonymousClosure: (0x8abb50), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::logAnalytic (0x8aba64)
    //     0x8aba9c: ldr             x1, [x1, #0x8a8]
    // 0x8abaa0: r0 = AllocateClosure()
    //     0x8abaa0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8abaa4: ldur            x1, [fp, #-8]
    // 0x8abaa8: mov             x2, x0
    // 0x8abaac: r0 = withAnalytic()
    //     0x8abaac: bl              #0x8abac8  ; [package:nuonline/app/modules/article/article_search/controllers/article_search_controller.dart] _ArticleSearchController&GetxController&PagingMixin&AnalyticMixin::withAnalytic
    // 0x8abab0: r0 = Null
    //     0x8abab0: mov             x0, NULL
    // 0x8abab4: LeaveFrame
    //     0x8abab4: mov             SP, fp
    //     0x8abab8: ldp             fp, lr, [SP], #0x10
    // 0x8ababc: ret
    //     0x8ababc: ret             
    // 0x8abac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abac0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abac4: b               #0x8aba80
  }
  [closure] void <anonymous closure>(dynamic, AnalyticService) {
    // ** addr: 0x8abb50, size: 0x120
    // 0x8abb50: EnterFrame
    //     0x8abb50: stp             fp, lr, [SP, #-0x10]!
    //     0x8abb54: mov             fp, SP
    // 0x8abb58: AllocStack(0x18)
    //     0x8abb58: sub             SP, SP, #0x18
    // 0x8abb5c: SetupParameters()
    //     0x8abb5c: ldr             x0, [fp, #0x18]
    //     0x8abb60: ldur            w3, [x0, #0x17]
    //     0x8abb64: add             x3, x3, HEAP, lsl #32
    //     0x8abb68: stur            x3, [fp, #-8]
    // 0x8abb6c: CheckStackOverflow
    //     0x8abb6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8abb70: cmp             SP, x16
    //     0x8abb74: b.ls            #0x8abc5c
    // 0x8abb78: r1 = Null
    //     0x8abb78: mov             x1, NULL
    // 0x8abb7c: r2 = 4
    //     0x8abb7c: movz            x2, #0x4
    // 0x8abb80: r0 = AllocateArray()
    //     0x8abb80: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8abb84: mov             x3, x0
    // 0x8abb88: stur            x3, [fp, #-0x10]
    // 0x8abb8c: r16 = "article/"
    //     0x8abb8c: add             x16, PP, #0x40, lsl #12  ; [pp+0x408b0] "article/"
    //     0x8abb90: ldr             x16, [x16, #0x8b0]
    // 0x8abb94: StoreField: r3->field_f = r16
    //     0x8abb94: stur            w16, [x3, #0xf]
    // 0x8abb98: ldur            x0, [fp, #-8]
    // 0x8abb9c: LoadField: r1 = r0->field_f
    //     0x8abb9c: ldur            w1, [x0, #0xf]
    // 0x8abba0: DecompressPointer r1
    //     0x8abba0: add             x1, x1, HEAP, lsl #32
    // 0x8abba4: LoadField: r4 = r1->field_3b
    //     0x8abba4: ldur            w4, [x1, #0x3b]
    // 0x8abba8: DecompressPointer r4
    //     0x8abba8: add             x4, x4, HEAP, lsl #32
    // 0x8abbac: stur            x4, [fp, #-8]
    // 0x8abbb0: LoadField: r0 = r1->field_27
    //     0x8abbb0: ldur            w0, [x1, #0x27]
    // 0x8abbb4: DecompressPointer r0
    //     0x8abbb4: add             x0, x0, HEAP, lsl #32
    // 0x8abbb8: r16 = Sentinel
    //     0x8abbb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8abbbc: cmp             w0, w16
    // 0x8abbc0: b.eq            #0x8abc64
    // 0x8abbc4: LoadField: r2 = r0->field_33
    //     0x8abbc4: ldur            x2, [x0, #0x33]
    // 0x8abbc8: r0 = BoxInt64Instr(r2)
    //     0x8abbc8: sbfiz           x0, x2, #1, #0x1f
    //     0x8abbcc: cmp             x2, x0, asr #1
    //     0x8abbd0: b.eq            #0x8abbdc
    //     0x8abbd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8abbd8: stur            x2, [x0, #7]
    // 0x8abbdc: mov             x1, x4
    // 0x8abbe0: mov             x2, x0
    // 0x8abbe4: r0 = _getValueOrData()
    //     0x8abbe4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8abbe8: mov             x1, x0
    // 0x8abbec: ldur            x0, [fp, #-8]
    // 0x8abbf0: LoadField: r2 = r0->field_f
    //     0x8abbf0: ldur            w2, [x0, #0xf]
    // 0x8abbf4: DecompressPointer r2
    //     0x8abbf4: add             x2, x2, HEAP, lsl #32
    // 0x8abbf8: cmp             w2, w1
    // 0x8abbfc: b.ne            #0x8abc08
    // 0x8abc00: r0 = Null
    //     0x8abc00: mov             x0, NULL
    // 0x8abc04: b               #0x8abc0c
    // 0x8abc08: mov             x0, x1
    // 0x8abc0c: ldur            x1, [fp, #-0x10]
    // 0x8abc10: ArrayStore: r1[1] = r0  ; List_4
    //     0x8abc10: add             x25, x1, #0x13
    //     0x8abc14: str             w0, [x25]
    //     0x8abc18: tbz             w0, #0, #0x8abc34
    //     0x8abc1c: ldurb           w16, [x1, #-1]
    //     0x8abc20: ldurb           w17, [x0, #-1]
    //     0x8abc24: and             x16, x17, x16, lsr #2
    //     0x8abc28: tst             x16, HEAP, lsr #32
    //     0x8abc2c: b.eq            #0x8abc34
    //     0x8abc30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8abc34: ldur            x16, [fp, #-0x10]
    // 0x8abc38: str             x16, [SP]
    // 0x8abc3c: r0 = _interpolate()
    //     0x8abc3c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8abc40: ldr             x1, [fp, #0x10]
    // 0x8abc44: mov             x2, x0
    // 0x8abc48: r0 = sendCurrentScreen()
    //     0x8abc48: bl              #0x8abc70  ; [package:nuonline/services/analytic_service.dart] AnalyticService::sendCurrentScreen
    // 0x8abc4c: r0 = Null
    //     0x8abc4c: mov             x0, NULL
    // 0x8abc50: LeaveFrame
    //     0x8abc50: mov             SP, fp
    //     0x8abc54: ldp             fp, lr, [SP], #0x10
    // 0x8abc58: ret
    //     0x8abc58: ret             
    // 0x8abc5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abc5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abc60: b               #0x8abb78
    // 0x8abc64: r9 = tabController
    //     0x8abc64: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0x8abc68: ldr             x9, [x9, #0x2c8]
    // 0x8abc6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8abc6c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ onReady(/* No info */) {
    // ** addr: 0x916304, size: 0x30
    // 0x916304: EnterFrame
    //     0x916304: stp             fp, lr, [SP, #-0x10]!
    //     0x916308: mov             fp, SP
    // 0x91630c: CheckStackOverflow
    //     0x91630c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x916310: cmp             SP, x16
    //     0x916314: b.ls            #0x91632c
    // 0x916318: r0 = _loadCategories()
    //     0x916318: bl              #0x916334  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::_loadCategories
    // 0x91631c: r0 = Null
    //     0x91631c: mov             x0, NULL
    // 0x916320: LeaveFrame
    //     0x916320: mov             SP, fp
    //     0x916324: ldp             fp, lr, [SP], #0x10
    // 0x916328: ret
    //     0x916328: ret             
    // 0x91632c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91632c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916330: b               #0x916318
  }
  _ _loadCategories(/* No info */) {
    // ** addr: 0x916334, size: 0x84
    // 0x916334: EnterFrame
    //     0x916334: stp             fp, lr, [SP, #-0x10]!
    //     0x916338: mov             fp, SP
    // 0x91633c: AllocStack(0x28)
    //     0x91633c: sub             SP, SP, #0x28
    // 0x916340: SetupParameters(MainArticleController this /* r1 => r1, fp-0x8 */)
    //     0x916340: stur            x1, [fp, #-8]
    // 0x916344: CheckStackOverflow
    //     0x916344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x916348: cmp             SP, x16
    //     0x91634c: b.ls            #0x9163b0
    // 0x916350: r1 = 1
    //     0x916350: movz            x1, #0x1
    // 0x916354: r0 = AllocateContext()
    //     0x916354: bl              #0xec126c  ; AllocateContextStub
    // 0x916358: mov             x2, x0
    // 0x91635c: ldur            x0, [fp, #-8]
    // 0x916360: stur            x2, [fp, #-0x10]
    // 0x916364: StoreField: r2->field_f = r0
    //     0x916364: stur            w0, [x2, #0xf]
    // 0x916368: LoadField: r1 = r0->field_23
    //     0x916368: ldur            w1, [x0, #0x23]
    // 0x91636c: DecompressPointer r1
    //     0x91636c: add             x1, x1, HEAP, lsl #32
    // 0x916370: r0 = findAll()
    //     0x916370: bl              #0x8aa274  ; [package:nuonline/app/data/repositories/category_repository.dart] CategoryRepository::findAll
    // 0x916374: ldur            x2, [fp, #-0x10]
    // 0x916378: r1 = Function '<anonymous closure>':.
    //     0x916378: add             x1, PP, #0x36, lsl #12  ; [pp+0x36398] AnonymousClosure: (0x9163b8), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::_loadCategories (0x916334)
    //     0x91637c: ldr             x1, [x1, #0x398]
    // 0x916380: stur            x0, [fp, #-8]
    // 0x916384: r0 = AllocateClosure()
    //     0x916384: bl              #0xec1630  ; AllocateClosureStub
    // 0x916388: r16 = <void?>
    //     0x916388: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x91638c: ldur            lr, [fp, #-8]
    // 0x916390: stp             lr, x16, [SP, #8]
    // 0x916394: str             x0, [SP]
    // 0x916398: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x916398: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91639c: r0 = then()
    //     0x91639c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x9163a0: r0 = Null
    //     0x9163a0: mov             x0, NULL
    // 0x9163a4: LeaveFrame
    //     0x9163a4: mov             SP, fp
    //     0x9163a8: ldp             fp, lr, [SP], #0x10
    // 0x9163ac: ret
    //     0x9163ac: ret             
    // 0x9163b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9163b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9163b4: b               #0x916350
  }
  [closure] void <anonymous closure>(dynamic, ApiResult<List<Category>>) {
    // ** addr: 0x9163b8, size: 0xa0
    // 0x9163b8: EnterFrame
    //     0x9163b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9163bc: mov             fp, SP
    // 0x9163c0: AllocStack(0x28)
    //     0x9163c0: sub             SP, SP, #0x28
    // 0x9163c4: SetupParameters()
    //     0x9163c4: ldr             x0, [fp, #0x18]
    //     0x9163c8: ldur            w1, [x0, #0x17]
    //     0x9163cc: add             x1, x1, HEAP, lsl #32
    // 0x9163d0: CheckStackOverflow
    //     0x9163d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9163d4: cmp             SP, x16
    //     0x9163d8: b.ls            #0x916450
    // 0x9163dc: LoadField: r0 = r1->field_f
    //     0x9163dc: ldur            w0, [x1, #0xf]
    // 0x9163e0: DecompressPointer r0
    //     0x9163e0: add             x0, x0, HEAP, lsl #32
    // 0x9163e4: mov             x2, x0
    // 0x9163e8: stur            x0, [fp, #-8]
    // 0x9163ec: r1 = Function '_onLoadCategoryError@1888219723':.
    //     0x9163ec: add             x1, PP, #0x36, lsl #12  ; [pp+0x363a0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x9163f0: ldr             x1, [x1, #0x3a0]
    // 0x9163f4: r0 = AllocateClosure()
    //     0x9163f4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9163f8: ldur            x2, [fp, #-8]
    // 0x9163fc: r1 = Function '_onLoadCategorySuccess@1888219723':.
    //     0x9163fc: add             x1, PP, #0x36, lsl #12  ; [pp+0x363a8] AnonymousClosure: (0x916458), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::_onLoadCategorySuccess (0x916498)
    //     0x916400: ldr             x1, [x1, #0x3a8]
    // 0x916404: stur            x0, [fp, #-8]
    // 0x916408: r0 = AllocateClosure()
    //     0x916408: bl              #0xec1630  ; AllocateClosureStub
    // 0x91640c: mov             x1, x0
    // 0x916410: ldr             x0, [fp, #0x10]
    // 0x916414: r2 = LoadClassIdInstr(r0)
    //     0x916414: ldur            x2, [x0, #-1]
    //     0x916418: ubfx            x2, x2, #0xc, #0x14
    // 0x91641c: r16 = <void?>
    //     0x91641c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x916420: stp             x0, x16, [SP, #0x10]
    // 0x916424: ldur            x16, [fp, #-8]
    // 0x916428: stp             x1, x16, [SP]
    // 0x91642c: mov             x0, x2
    // 0x916430: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x916430: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x916434: r0 = GDT[cid_x0 + -0x1000]()
    //     0x916434: sub             lr, x0, #1, lsl #12
    //     0x916438: ldr             lr, [x21, lr, lsl #3]
    //     0x91643c: blr             lr
    // 0x916440: r0 = Null
    //     0x916440: mov             x0, NULL
    // 0x916444: LeaveFrame
    //     0x916444: mov             SP, fp
    //     0x916448: ldp             fp, lr, [SP], #0x10
    // 0x91644c: ret
    //     0x91644c: ret             
    // 0x916450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916454: b               #0x9163dc
  }
  [closure] void _onLoadCategorySuccess(dynamic, List<Category>, dynamic) {
    // ** addr: 0x916458, size: 0x40
    // 0x916458: EnterFrame
    //     0x916458: stp             fp, lr, [SP, #-0x10]!
    //     0x91645c: mov             fp, SP
    // 0x916460: ldr             x0, [fp, #0x20]
    // 0x916464: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x916464: ldur            w1, [x0, #0x17]
    // 0x916468: DecompressPointer r1
    //     0x916468: add             x1, x1, HEAP, lsl #32
    // 0x91646c: CheckStackOverflow
    //     0x91646c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x916470: cmp             SP, x16
    //     0x916474: b.ls            #0x916490
    // 0x916478: ldr             x2, [fp, #0x18]
    // 0x91647c: ldr             x3, [fp, #0x10]
    // 0x916480: r0 = _onLoadCategorySuccess()
    //     0x916480: bl              #0x916498  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::_onLoadCategorySuccess
    // 0x916484: LeaveFrame
    //     0x916484: mov             SP, fp
    //     0x916488: ldp             fp, lr, [SP], #0x10
    // 0x91648c: ret
    //     0x91648c: ret             
    // 0x916490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916490: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916494: b               #0x916478
  }
  _ _onLoadCategorySuccess(/* No info */) {
    // ** addr: 0x916498, size: 0x98
    // 0x916498: EnterFrame
    //     0x916498: stp             fp, lr, [SP, #-0x10]!
    //     0x91649c: mov             fp, SP
    // 0x9164a0: AllocStack(0x10)
    //     0x9164a0: sub             SP, SP, #0x10
    // 0x9164a4: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9164a4: mov             x0, x2
    //     0x9164a8: stur            x2, [fp, #-0x10]
    // 0x9164ac: CheckStackOverflow
    //     0x9164ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9164b0: cmp             SP, x16
    //     0x9164b4: b.ls            #0x916528
    // 0x9164b8: LoadField: r3 = r1->field_2b
    //     0x9164b8: ldur            w3, [x1, #0x2b]
    // 0x9164bc: DecompressPointer r3
    //     0x9164bc: add             x3, x3, HEAP, lsl #32
    // 0x9164c0: stur            x3, [fp, #-8]
    // 0x9164c4: r1 = Function '<anonymous closure>':.
    //     0x9164c4: add             x1, PP, #0x36, lsl #12  ; [pp+0x363b0] AnonymousClosure: static (0xc4169c), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onTertiaryFixedVariant (0x636dc4)
    //     0x9164c8: ldr             x1, [x1, #0x3b0]
    // 0x9164cc: r2 = Null
    //     0x9164cc: mov             x2, NULL
    // 0x9164d0: r0 = AllocateClosure()
    //     0x9164d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9164d4: ldur            x1, [fp, #-0x10]
    // 0x9164d8: r2 = LoadClassIdInstr(r1)
    //     0x9164d8: ldur            x2, [x1, #-1]
    //     0x9164dc: ubfx            x2, x2, #0xc, #0x14
    // 0x9164e0: mov             x16, x0
    // 0x9164e4: mov             x0, x2
    // 0x9164e8: mov             x2, x16
    // 0x9164ec: r0 = GDT[cid_x0 + 0xea28]()
    //     0x9164ec: movz            x17, #0xea28
    //     0x9164f0: add             lr, x0, x17
    //     0x9164f4: ldr             lr, [x21, lr, lsl #3]
    //     0x9164f8: blr             lr
    // 0x9164fc: LoadField: r1 = r0->field_7
    //     0x9164fc: ldur            w1, [x0, #7]
    // 0x916500: DecompressPointer r1
    //     0x916500: add             x1, x1, HEAP, lsl #32
    // 0x916504: mov             x2, x0
    // 0x916508: r0 = _GrowableList.of()
    //     0x916508: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x91650c: ldur            x1, [fp, #-8]
    // 0x916510: mov             x2, x0
    // 0x916514: r0 = value=()
    //     0x916514: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x916518: r0 = Null
    //     0x916518: mov             x0, NULL
    // 0x91651c: LeaveFrame
    //     0x91651c: mov             SP, fp
    //     0x916520: ldp             fp, lr, [SP], #0x10
    // 0x916524: ret
    //     0x916524: ret             
    // 0x916528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91652c: b               #0x9164b8
  }
  _ onClose(/* No info */) {
    // ** addr: 0x9268f8, size: 0x54
    // 0x9268f8: EnterFrame
    //     0x9268f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9268fc: mov             fp, SP
    // 0x926900: CheckStackOverflow
    //     0x926900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926904: cmp             SP, x16
    //     0x926908: b.ls            #0x926938
    // 0x92690c: LoadField: r0 = r1->field_27
    //     0x92690c: ldur            w0, [x1, #0x27]
    // 0x926910: DecompressPointer r0
    //     0x926910: add             x0, x0, HEAP, lsl #32
    // 0x926914: r16 = Sentinel
    //     0x926914: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x926918: cmp             w0, w16
    // 0x92691c: b.eq            #0x926940
    // 0x926920: mov             x1, x0
    // 0x926924: r0 = dispose()
    //     0x926924: bl              #0xa87594  ; [package:flutter/src/material/tab_controller.dart] TabController::dispose
    // 0x926928: r0 = Null
    //     0x926928: mov             x0, NULL
    // 0x92692c: LeaveFrame
    //     0x92692c: mov             SP, fp
    //     0x926930: ldp             fp, lr, [SP], #0x10
    // 0x926934: ret
    //     0x926934: ret             
    // 0x926938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92693c: b               #0x92690c
    // 0x926940: r9 = tabController
    //     0x926940: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0x926944: ldr             x9, [x9, #0x2c8]
    // 0x926948: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x926948: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> onSettingCategoryPressed(dynamic) {
    // ** addr: 0xad4708, size: 0x38
    // 0xad4708: EnterFrame
    //     0xad4708: stp             fp, lr, [SP, #-0x10]!
    //     0xad470c: mov             fp, SP
    // 0xad4710: ldr             x0, [fp, #0x10]
    // 0xad4714: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad4714: ldur            w1, [x0, #0x17]
    // 0xad4718: DecompressPointer r1
    //     0xad4718: add             x1, x1, HEAP, lsl #32
    // 0xad471c: CheckStackOverflow
    //     0xad471c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4720: cmp             SP, x16
    //     0xad4724: b.ls            #0xad4738
    // 0xad4728: r0 = onSettingCategoryPressed()
    //     0xad4728: bl              #0xad4740  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onSettingCategoryPressed
    // 0xad472c: LeaveFrame
    //     0xad472c: mov             SP, fp
    //     0xad4730: ldp             fp, lr, [SP], #0x10
    // 0xad4734: ret
    //     0xad4734: ret             
    // 0xad4738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4738: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad473c: b               #0xad4728
  }
  _ onSettingCategoryPressed(/* No info */) async {
    // ** addr: 0xad4740, size: 0x110
    // 0xad4740: EnterFrame
    //     0xad4740: stp             fp, lr, [SP, #-0x10]!
    //     0xad4744: mov             fp, SP
    // 0xad4748: AllocStack(0x30)
    //     0xad4748: sub             SP, SP, #0x30
    // 0xad474c: SetupParameters(MainArticleController this /* r1 => r1, fp-0x10 */)
    //     0xad474c: stur            NULL, [fp, #-8]
    //     0xad4750: stur            x1, [fp, #-0x10]
    // 0xad4754: CheckStackOverflow
    //     0xad4754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4758: cmp             SP, x16
    //     0xad475c: b.ls            #0xad483c
    // 0xad4760: InitAsync() -> Future<void?>
    //     0xad4760: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad4764: bl              #0x661298  ; InitAsyncStub
    // 0xad4768: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4768: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad476c: ldr             x0, [x0, #0x2670]
    //     0xad4770: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad4774: cmp             w0, w16
    //     0xad4778: b.ne            #0xad4784
    //     0xad477c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad4780: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad4784: r1 = Null
    //     0xad4784: mov             x1, NULL
    // 0xad4788: r2 = 4
    //     0xad4788: movz            x2, #0x4
    // 0xad478c: r0 = AllocateArray()
    //     0xad478c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad4790: r16 = "islamic"
    //     0xad4790: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0xad4794: ldr             x16, [x16, #0xca0]
    // 0xad4798: StoreField: r0->field_f = r16
    //     0xad4798: stur            w16, [x0, #0xf]
    // 0xad479c: ldur            x1, [fp, #-0x10]
    // 0xad47a0: LoadField: r2 = r1->field_27
    //     0xad47a0: ldur            w2, [x1, #0x27]
    // 0xad47a4: DecompressPointer r2
    //     0xad47a4: add             x2, x2, HEAP, lsl #32
    // 0xad47a8: r16 = Sentinel
    //     0xad47a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad47ac: cmp             w2, w16
    // 0xad47b0: b.eq            #0xad4844
    // 0xad47b4: LoadField: r3 = r2->field_33
    //     0xad47b4: ldur            x3, [x2, #0x33]
    // 0xad47b8: cmp             x3, #1
    // 0xad47bc: r16 = true
    //     0xad47bc: add             x16, NULL, #0x20  ; true
    // 0xad47c0: r17 = false
    //     0xad47c0: add             x17, NULL, #0x30  ; false
    // 0xad47c4: csel            x2, x16, x17, eq
    // 0xad47c8: StoreField: r0->field_13 = r2
    //     0xad47c8: stur            w2, [x0, #0x13]
    // 0xad47cc: r16 = <String, bool>
    //     0xad47cc: add             x16, PP, #0xf, lsl #12  ; [pp+0xfb70] TypeArguments: <String, bool>
    //     0xad47d0: ldr             x16, [x16, #0xb70]
    // 0xad47d4: stp             x0, x16, [SP]
    // 0xad47d8: r0 = Map._fromLiteral()
    //     0xad47d8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xad47dc: r16 = "/article/article-channel-setting"
    //     0xad47dc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cca8] "/article/article-channel-setting"
    //     0xad47e0: ldr             x16, [x16, #0xca8]
    // 0xad47e4: stp             x16, NULL, [SP, #8]
    // 0xad47e8: str             x0, [SP]
    // 0xad47ec: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad47ec: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad47f0: ldr             x4, [x4, #0x478]
    // 0xad47f4: r0 = GetNavigation.toNamed()
    //     0xad47f4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad47f8: mov             x1, x0
    // 0xad47fc: stur            x1, [fp, #-0x18]
    // 0xad4800: r0 = Await()
    //     0xad4800: bl              #0x661044  ; AwaitStub
    // 0xad4804: ldur            x0, [fp, #-0x10]
    // 0xad4808: LoadField: r1 = r0->field_2f
    //     0xad4808: ldur            w1, [x0, #0x2f]
    // 0xad480c: DecompressPointer r1
    //     0xad480c: add             x1, x1, HEAP, lsl #32
    // 0xad4810: r2 = 0
    //     0xad4810: movz            x2, #0
    // 0xad4814: r0 = value=()
    //     0xad4814: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad4818: ldur            x0, [fp, #-0x10]
    // 0xad481c: LoadField: r1 = r0->field_33
    //     0xad481c: ldur            w1, [x0, #0x33]
    // 0xad4820: DecompressPointer r1
    //     0xad4820: add             x1, x1, HEAP, lsl #32
    // 0xad4824: r2 = 0
    //     0xad4824: movz            x2, #0
    // 0xad4828: r0 = value=()
    //     0xad4828: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad482c: ldur            x1, [fp, #-0x10]
    // 0xad4830: r0 = _loadCategories()
    //     0xad4830: bl              #0x916334  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::_loadCategories
    // 0xad4834: r0 = Null
    //     0xad4834: mov             x0, NULL
    // 0xad4838: r0 = ReturnAsyncNotFuture()
    //     0xad4838: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad483c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad483c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4840: b               #0xad4760
    // 0xad4844: r9 = tabController
    //     0xad4844: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0xad4848: ldr             x9, [x9, #0x2c8]
    // 0xad484c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad484c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void onMoreCategoryPressed(dynamic) {
    // ** addr: 0xad4850, size: 0x38
    // 0xad4850: EnterFrame
    //     0xad4850: stp             fp, lr, [SP, #-0x10]!
    //     0xad4854: mov             fp, SP
    // 0xad4858: ldr             x0, [fp, #0x10]
    // 0xad485c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad485c: ldur            w1, [x0, #0x17]
    // 0xad4860: DecompressPointer r1
    //     0xad4860: add             x1, x1, HEAP, lsl #32
    // 0xad4864: CheckStackOverflow
    //     0xad4864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4868: cmp             SP, x16
    //     0xad486c: b.ls            #0xad4880
    // 0xad4870: r0 = onMoreCategoryPressed()
    //     0xad4870: bl              #0xad4888  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onMoreCategoryPressed
    // 0xad4874: LeaveFrame
    //     0xad4874: mov             SP, fp
    //     0xad4878: ldp             fp, lr, [SP], #0x10
    // 0xad487c: ret
    //     0xad487c: ret             
    // 0xad4880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4880: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4884: b               #0xad4870
  }
  _ onMoreCategoryPressed(/* No info */) {
    // ** addr: 0xad4888, size: 0xd0
    // 0xad4888: EnterFrame
    //     0xad4888: stp             fp, lr, [SP, #-0x10]!
    //     0xad488c: mov             fp, SP
    // 0xad4890: AllocStack(0x20)
    //     0xad4890: sub             SP, SP, #0x20
    // 0xad4894: SetupParameters(MainArticleController this /* r1 => r1, fp-0x8 */)
    //     0xad4894: stur            x1, [fp, #-8]
    // 0xad4898: CheckStackOverflow
    //     0xad4898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad489c: cmp             SP, x16
    //     0xad48a0: b.ls            #0xad4944
    // 0xad48a4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad48a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad48a8: ldr             x0, [x0, #0x2670]
    //     0xad48ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad48b0: cmp             w0, w16
    //     0xad48b4: b.ne            #0xad48c0
    //     0xad48b8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad48bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad48c0: r1 = Null
    //     0xad48c0: mov             x1, NULL
    // 0xad48c4: r2 = 4
    //     0xad48c4: movz            x2, #0x4
    // 0xad48c8: r0 = AllocateArray()
    //     0xad48c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad48cc: r16 = "islamic"
    //     0xad48cc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0xad48d0: ldr             x16, [x16, #0xca0]
    // 0xad48d4: StoreField: r0->field_f = r16
    //     0xad48d4: stur            w16, [x0, #0xf]
    // 0xad48d8: ldur            x1, [fp, #-8]
    // 0xad48dc: LoadField: r2 = r1->field_27
    //     0xad48dc: ldur            w2, [x1, #0x27]
    // 0xad48e0: DecompressPointer r2
    //     0xad48e0: add             x2, x2, HEAP, lsl #32
    // 0xad48e4: r16 = Sentinel
    //     0xad48e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad48e8: cmp             w2, w16
    // 0xad48ec: b.eq            #0xad494c
    // 0xad48f0: LoadField: r1 = r2->field_33
    //     0xad48f0: ldur            x1, [x2, #0x33]
    // 0xad48f4: cmp             x1, #1
    // 0xad48f8: r16 = true
    //     0xad48f8: add             x16, NULL, #0x20  ; true
    // 0xad48fc: r17 = false
    //     0xad48fc: add             x17, NULL, #0x30  ; false
    // 0xad4900: csel            x2, x16, x17, eq
    // 0xad4904: StoreField: r0->field_13 = r2
    //     0xad4904: stur            w2, [x0, #0x13]
    // 0xad4908: r16 = <String, bool>
    //     0xad4908: add             x16, PP, #0xf, lsl #12  ; [pp+0xfb70] TypeArguments: <String, bool>
    //     0xad490c: ldr             x16, [x16, #0xb70]
    // 0xad4910: stp             x0, x16, [SP]
    // 0xad4914: r0 = Map._fromLiteral()
    //     0xad4914: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xad4918: r16 = "/article/article-channel"
    //     0xad4918: add             x16, PP, #0x36, lsl #12  ; [pp+0x36448] "/article/article-channel"
    //     0xad491c: ldr             x16, [x16, #0x448]
    // 0xad4920: stp             x16, NULL, [SP, #8]
    // 0xad4924: str             x0, [SP]
    // 0xad4928: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad4928: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad492c: ldr             x4, [x4, #0x478]
    // 0xad4930: r0 = GetNavigation.toNamed()
    //     0xad4930: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad4934: r0 = Null
    //     0xad4934: mov             x0, NULL
    // 0xad4938: LeaveFrame
    //     0xad4938: mov             SP, fp
    //     0xad493c: ldp             fp, lr, [SP], #0x10
    // 0xad4940: ret
    //     0xad4940: ret             
    // 0xad4944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4944: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4948: b               #0xad48a4
    // 0xad494c: r9 = tabController
    //     0xad494c: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0xad4950: ldr             x9, [x9, #0x2c8]
    // 0xad4954: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad4954: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ mainChannels(/* No info */) {
    // ** addr: 0xad4bbc, size: 0xa4
    // 0xad4bbc: EnterFrame
    //     0xad4bbc: stp             fp, lr, [SP, #-0x10]!
    //     0xad4bc0: mov             fp, SP
    // 0xad4bc4: AllocStack(0x10)
    //     0xad4bc4: sub             SP, SP, #0x10
    // 0xad4bc8: CheckStackOverflow
    //     0xad4bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4bcc: cmp             SP, x16
    //     0xad4bd0: b.ls            #0xad4c58
    // 0xad4bd4: LoadField: r0 = r1->field_2b
    //     0xad4bd4: ldur            w0, [x1, #0x2b]
    // 0xad4bd8: DecompressPointer r0
    //     0xad4bd8: add             x0, x0, HEAP, lsl #32
    // 0xad4bdc: stur            x0, [fp, #-8]
    // 0xad4be0: r1 = Function '<anonymous closure>':.
    //     0xad4be0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36498] AnonymousClosure: (0xad4c60), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::mainChannels (0xad4bbc)
    //     0xad4be4: ldr             x1, [x1, #0x498]
    // 0xad4be8: r2 = Null
    //     0xad4be8: mov             x2, NULL
    // 0xad4bec: r0 = AllocateClosure()
    //     0xad4bec: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4bf0: ldur            x1, [fp, #-8]
    // 0xad4bf4: mov             x2, x0
    // 0xad4bf8: r0 = where()
    //     0xad4bf8: bl              #0x7e5f9c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::where
    // 0xad4bfc: r1 = LoadClassIdInstr(r0)
    //     0xad4bfc: ldur            x1, [x0, #-1]
    //     0xad4c00: ubfx            x1, x1, #0xc, #0x14
    // 0xad4c04: mov             x16, x0
    // 0xad4c08: mov             x0, x1
    // 0xad4c0c: mov             x1, x16
    // 0xad4c10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xad4c10: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xad4c14: r0 = GDT[cid_x0 + 0xd889]()
    //     0xad4c14: movz            x17, #0xd889
    //     0xad4c18: add             lr, x0, x17
    //     0xad4c1c: ldr             lr, [x21, lr, lsl #3]
    //     0xad4c20: blr             lr
    // 0xad4c24: r1 = Function '<anonymous closure>':.
    //     0xad4c24: add             x1, PP, #0x36, lsl #12  ; [pp+0x364a0] AnonymousClosure: (0x8ab634), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels (0x8ab6ac)
    //     0xad4c28: ldr             x1, [x1, #0x4a0]
    // 0xad4c2c: r2 = Null
    //     0xad4c2c: mov             x2, NULL
    // 0xad4c30: stur            x0, [fp, #-8]
    // 0xad4c34: r0 = AllocateClosure()
    //     0xad4c34: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4c38: str             x0, [SP]
    // 0xad4c3c: ldur            x1, [fp, #-8]
    // 0xad4c40: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xad4c40: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xad4c44: r0 = sort()
    //     0xad4c44: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0xad4c48: ldur            x0, [fp, #-8]
    // 0xad4c4c: LeaveFrame
    //     0xad4c4c: mov             SP, fp
    //     0xad4c50: ldp             fp, lr, [SP], #0x10
    // 0xad4c54: ret
    //     0xad4c54: ret             
    // 0xad4c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4c58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4c5c: b               #0xad4bd4
  }
  [closure] bool <anonymous closure>(dynamic, Category) {
    // ** addr: 0xad4c60, size: 0x14
    // 0xad4c60: ldr             x1, [SP]
    // 0xad4c64: LoadField: r2 = r1->field_1f
    //     0xad4c64: ldur            w2, [x1, #0x1f]
    // 0xad4c68: DecompressPointer r2
    //     0xad4c68: add             x2, x2, HEAP, lsl #32
    // 0xad4c6c: eor             x0, x2, #0x10
    // 0xad4c70: ret
    //     0xad4c70: ret             
  }
  [closure] void onValueChanged(dynamic, int?) {
    // ** addr: 0xad52d8, size: 0x3c
    // 0xad52d8: EnterFrame
    //     0xad52d8: stp             fp, lr, [SP, #-0x10]!
    //     0xad52dc: mov             fp, SP
    // 0xad52e0: ldr             x0, [fp, #0x18]
    // 0xad52e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad52e4: ldur            w1, [x0, #0x17]
    // 0xad52e8: DecompressPointer r1
    //     0xad52e8: add             x1, x1, HEAP, lsl #32
    // 0xad52ec: CheckStackOverflow
    //     0xad52ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad52f0: cmp             SP, x16
    //     0xad52f4: b.ls            #0xad530c
    // 0xad52f8: ldr             x2, [fp, #0x10]
    // 0xad52fc: r0 = onValueChanged()
    //     0xad52fc: bl              #0xad5314  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onValueChanged
    // 0xad5300: LeaveFrame
    //     0xad5300: mov             SP, fp
    //     0xad5304: ldp             fp, lr, [SP], #0x10
    // 0xad5308: ret
    //     0xad5308: ret             
    // 0xad530c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad530c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5310: b               #0xad52f8
  }
  _ onValueChanged(/* No info */) {
    // ** addr: 0xad5314, size: 0x90
    // 0xad5314: EnterFrame
    //     0xad5314: stp             fp, lr, [SP, #-0x10]!
    //     0xad5318: mov             fp, SP
    // 0xad531c: AllocStack(0x10)
    //     0xad531c: sub             SP, SP, #0x10
    // 0xad5320: SetupParameters(MainArticleController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xad5320: mov             x3, x1
    //     0xad5324: mov             x0, x2
    //     0xad5328: stur            x1, [fp, #-8]
    //     0xad532c: stur            x2, [fp, #-0x10]
    // 0xad5330: CheckStackOverflow
    //     0xad5330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5334: cmp             SP, x16
    //     0xad5338: b.ls            #0xad5390
    // 0xad533c: cmp             w0, NULL
    // 0xad5340: b.eq            #0xad5380
    // 0xad5344: LoadField: r1 = r3->field_37
    //     0xad5344: ldur            w1, [x3, #0x37]
    // 0xad5348: DecompressPointer r1
    //     0xad5348: add             x1, x1, HEAP, lsl #32
    // 0xad534c: mov             x2, x0
    // 0xad5350: r0 = value=()
    //     0xad5350: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xad5354: ldur            x0, [fp, #-8]
    // 0xad5358: LoadField: r1 = r0->field_27
    //     0xad5358: ldur            w1, [x0, #0x27]
    // 0xad535c: DecompressPointer r1
    //     0xad535c: add             x1, x1, HEAP, lsl #32
    // 0xad5360: r16 = Sentinel
    //     0xad5360: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad5364: cmp             w1, w16
    // 0xad5368: b.eq            #0xad5398
    // 0xad536c: ldur            x0, [fp, #-0x10]
    // 0xad5370: r2 = LoadInt32Instr(r0)
    //     0xad5370: sbfx            x2, x0, #1, #0x1f
    //     0xad5374: tbz             w0, #0, #0xad537c
    //     0xad5378: ldur            x2, [x0, #7]
    // 0xad537c: r0 = animateTo()
    //     0xad537c: bl              #0xa06e80  ; [package:flutter/src/material/tab_controller.dart] TabController::animateTo
    // 0xad5380: r0 = Null
    //     0xad5380: mov             x0, NULL
    // 0xad5384: LeaveFrame
    //     0xad5384: mov             SP, fp
    //     0xad5388: ldp             fp, lr, [SP], #0x10
    // 0xad538c: ret
    //     0xad538c: ret             
    // 0xad5390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5394: b               #0xad533c
    // 0xad5398: r9 = tabController
    //     0xad5398: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0xad539c: ldr             x9, [x9, #0x2c8]
    // 0xad53a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad53a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
