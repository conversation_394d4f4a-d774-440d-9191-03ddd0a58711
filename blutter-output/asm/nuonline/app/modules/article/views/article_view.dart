// lib: , url: package:nuonline/app/modules/article/views/article_view.dart

// class id: 1050149, size: 0x8
class :: {
}

// class id: 5307, size: 0x14, field offset: 0x14
class ArticleView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad340c, size: 0x31c
    // 0xad340c: EnterFrame
    //     0xad340c: stp             fp, lr, [SP, #-0x10]!
    //     0xad3410: mov             fp, SP
    // 0xad3414: AllocStack(0x30)
    //     0xad3414: sub             SP, SP, #0x30
    // 0xad3418: SetupParameters(ArticleView this /* r1 => r1, fp-0x8 */)
    //     0xad3418: stur            x1, [fp, #-8]
    // 0xad341c: CheckStackOverflow
    //     0xad341c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3420: cmp             SP, x16
    //     0xad3424: b.ls            #0xad3720
    // 0xad3428: r1 = 1
    //     0xad3428: movz            x1, #0x1
    // 0xad342c: r0 = AllocateContext()
    //     0xad342c: bl              #0xec126c  ; AllocateContextStub
    // 0xad3430: mov             x2, x0
    // 0xad3434: ldur            x0, [fp, #-8]
    // 0xad3438: stur            x2, [fp, #-0x10]
    // 0xad343c: StoreField: r2->field_f = r0
    //     0xad343c: stur            w0, [x2, #0xf]
    // 0xad3440: mov             x1, x0
    // 0xad3444: r0 = controller()
    //     0xad3444: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3448: LoadField: r1 = r0->field_3b
    //     0xad3448: ldur            w1, [x0, #0x3b]
    // 0xad344c: DecompressPointer r1
    //     0xad344c: add             x1, x1, HEAP, lsl #32
    // 0xad3450: stur            x1, [fp, #-0x18]
    // 0xad3454: r0 = Text()
    //     0xad3454: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad3458: mov             x1, x0
    // 0xad345c: ldur            x0, [fp, #-0x18]
    // 0xad3460: stur            x1, [fp, #-0x20]
    // 0xad3464: StoreField: r1->field_b = r0
    //     0xad3464: stur            w0, [x1, #0xb]
    // 0xad3468: r0 = AppBar()
    //     0xad3468: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad346c: stur            x0, [fp, #-0x18]
    // 0xad3470: ldur            x16, [fp, #-0x20]
    // 0xad3474: str             x16, [SP]
    // 0xad3478: mov             x1, x0
    // 0xad347c: r4 = const [0, 0x2, 0x1, 0x1, title, 0x1, null]
    //     0xad347c: add             x4, PP, #0x25, lsl #12  ; [pp+0x256e8] List(7) [0, 0x2, 0x1, 0x1, "title", 0x1, Null]
    //     0xad3480: ldr             x4, [x4, #0x6e8]
    // 0xad3484: r0 = AppBar()
    //     0xad3484: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad3488: ldur            x1, [fp, #-8]
    // 0xad348c: r0 = controller()
    //     0xad348c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3490: stur            x0, [fp, #-8]
    // 0xad3494: r0 = Obx()
    //     0xad3494: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad3498: ldur            x2, [fp, #-0x10]
    // 0xad349c: r1 = Function '<anonymous closure>':.
    //     0xad349c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30918] AnonymousClosure: (0xad3728), in [package:nuonline/app/modules/article/views/article_view.dart] ArticleView::build (0xad340c)
    //     0xad34a0: ldr             x1, [x1, #0x918]
    // 0xad34a4: stur            x0, [fp, #-0x10]
    // 0xad34a8: r0 = AllocateClosure()
    //     0xad34a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad34ac: mov             x1, x0
    // 0xad34b0: ldur            x0, [fp, #-0x10]
    // 0xad34b4: StoreField: r0->field_b = r1
    //     0xad34b4: stur            w1, [x0, #0xb]
    // 0xad34b8: r0 = RefreshIndicator()
    //     0xad34b8: bl              #0xa38b9c  ; AllocateRefreshIndicatorStub -> RefreshIndicator (size=0x54)
    // 0xad34bc: mov             x3, x0
    // 0xad34c0: ldur            x0, [fp, #-0x10]
    // 0xad34c4: stur            x3, [fp, #-0x20]
    // 0xad34c8: StoreField: r3->field_b = r0
    //     0xad34c8: stur            w0, [x3, #0xb]
    // 0xad34cc: d0 = 40.000000
    //     0xad34cc: ldr             d0, [PP, #0x5a48]  ; [pp+0x5a48] IMM: double(40) from 0x4044000000000000
    // 0xad34d0: StoreField: r3->field_f = d0
    //     0xad34d0: stur            d0, [x3, #0xf]
    // 0xad34d4: ArrayStore: r3[0] = rZR  ; List_8
    //     0xad34d4: stur            xzr, [x3, #0x17]
    // 0xad34d8: ldur            x2, [fp, #-8]
    // 0xad34dc: r1 = Function 'onPageRefresh':.
    //     0xad34dc: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a60] AnonymousClosure: (0xad2094), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageRefresh (0xad1fcc)
    //     0xad34e0: ldr             x1, [x1, #0xa60]
    // 0xad34e4: r0 = AllocateClosure()
    //     0xad34e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad34e8: mov             x1, x0
    // 0xad34ec: ldur            x0, [fp, #-0x20]
    // 0xad34f0: StoreField: r0->field_1f = r1
    //     0xad34f0: stur            w1, [x0, #0x1f]
    // 0xad34f4: r1 = Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static.
    //     0xad34f4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f58] Closure: (ScrollNotification) => bool from Function 'defaultScrollNotificationPredicate': static. (0x7e54fb3a357c)
    //     0xad34f8: ldr             x1, [x1, #0xf58]
    // 0xad34fc: StoreField: r0->field_2f = r1
    //     0xad34fc: stur            w1, [x0, #0x2f]
    // 0xad3500: d0 = 2.500000
    //     0xad3500: fmov            d0, #2.50000000
    // 0xad3504: StoreField: r0->field_3b = d0
    //     0xad3504: stur            d0, [x0, #0x3b]
    // 0xad3508: r1 = Instance_RefreshIndicatorTriggerMode
    //     0xad3508: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a68] Obj!RefreshIndicatorTriggerMode@e36381
    //     0xad350c: ldr             x1, [x1, #0xa68]
    // 0xad3510: StoreField: r0->field_47 = r1
    //     0xad3510: stur            w1, [x0, #0x47]
    // 0xad3514: d0 = 2.000000
    //     0xad3514: fmov            d0, #2.00000000
    // 0xad3518: StoreField: r0->field_4b = d0
    //     0xad3518: stur            d0, [x0, #0x4b]
    // 0xad351c: r1 = Instance__IndicatorType
    //     0xad351c: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a70] Obj!_IndicatorType@e36341
    //     0xad3520: ldr             x1, [x1, #0xa70]
    // 0xad3524: StoreField: r0->field_43 = r1
    //     0xad3524: stur            w1, [x0, #0x43]
    // 0xad3528: r1 = <FlexParentData>
    //     0xad3528: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad352c: ldr             x1, [x1, #0x720]
    // 0xad3530: r0 = Expanded()
    //     0xad3530: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad3534: mov             x3, x0
    // 0xad3538: r0 = 1
    //     0xad3538: movz            x0, #0x1
    // 0xad353c: stur            x3, [fp, #-8]
    // 0xad3540: StoreField: r3->field_13 = r0
    //     0xad3540: stur            x0, [x3, #0x13]
    // 0xad3544: r0 = Instance_FlexFit
    //     0xad3544: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad3548: ldr             x0, [x0, #0x728]
    // 0xad354c: StoreField: r3->field_1b = r0
    //     0xad354c: stur            w0, [x3, #0x1b]
    // 0xad3550: ldur            x0, [fp, #-0x20]
    // 0xad3554: StoreField: r3->field_b = r0
    //     0xad3554: stur            w0, [x3, #0xb]
    // 0xad3558: r1 = Null
    //     0xad3558: mov             x1, NULL
    // 0xad355c: r2 = 2
    //     0xad355c: movz            x2, #0x2
    // 0xad3560: r0 = AllocateArray()
    //     0xad3560: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad3564: mov             x2, x0
    // 0xad3568: ldur            x0, [fp, #-8]
    // 0xad356c: stur            x2, [fp, #-0x10]
    // 0xad3570: StoreField: r2->field_f = r0
    //     0xad3570: stur            w0, [x2, #0xf]
    // 0xad3574: r1 = <Widget>
    //     0xad3574: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad3578: r0 = AllocateGrowableArray()
    //     0xad3578: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad357c: mov             x1, x0
    // 0xad3580: ldur            x0, [fp, #-0x10]
    // 0xad3584: stur            x1, [fp, #-8]
    // 0xad3588: StoreField: r1->field_f = r0
    //     0xad3588: stur            w0, [x1, #0xf]
    // 0xad358c: r0 = 2
    //     0xad358c: movz            x0, #0x2
    // 0xad3590: StoreField: r1->field_b = r0
    //     0xad3590: stur            w0, [x1, #0xb]
    // 0xad3594: r0 = find()
    //     0xad3594: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad3598: mov             x1, x0
    // 0xad359c: r0 = _adsVisibility()
    //     0xad359c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xad35a0: mov             x2, x0
    // 0xad35a4: r1 = Null
    //     0xad35a4: mov             x1, NULL
    // 0xad35a8: r0 = AdsConfig.fromJson()
    //     0xad35a8: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xad35ac: LoadField: r1 = r0->field_f
    //     0xad35ac: ldur            w1, [x0, #0xf]
    // 0xad35b0: DecompressPointer r1
    //     0xad35b0: add             x1, x1, HEAP, lsl #32
    // 0xad35b4: LoadField: r0 = r1->field_7
    //     0xad35b4: ldur            w0, [x1, #7]
    // 0xad35b8: DecompressPointer r0
    //     0xad35b8: add             x0, x0, HEAP, lsl #32
    // 0xad35bc: tbnz            w0, #4, #0xad366c
    // 0xad35c0: ldur            x1, [fp, #-8]
    // 0xad35c4: r0 = find()
    //     0xad35c4: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad35c8: mov             x1, x0
    // 0xad35cc: r0 = articleBanner()
    //     0xad35cc: bl              #0xad1518  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::articleBanner
    // 0xad35d0: LoadField: r1 = r0->field_f
    //     0xad35d0: ldur            w1, [x0, #0xf]
    // 0xad35d4: DecompressPointer r1
    //     0xad35d4: add             x1, x1, HEAP, lsl #32
    // 0xad35d8: stur            x1, [fp, #-0x10]
    // 0xad35dc: r0 = AdmobBannerWidget()
    //     0xad35dc: bl              #0xad155c  ; AllocateAdmobBannerWidgetStub -> AdmobBannerWidget (size=0x10)
    // 0xad35e0: mov             x2, x0
    // 0xad35e4: ldur            x0, [fp, #-0x10]
    // 0xad35e8: stur            x2, [fp, #-0x20]
    // 0xad35ec: StoreField: r2->field_b = r0
    //     0xad35ec: stur            w0, [x2, #0xb]
    // 0xad35f0: ldur            x0, [fp, #-8]
    // 0xad35f4: LoadField: r1 = r0->field_b
    //     0xad35f4: ldur            w1, [x0, #0xb]
    // 0xad35f8: LoadField: r3 = r0->field_f
    //     0xad35f8: ldur            w3, [x0, #0xf]
    // 0xad35fc: DecompressPointer r3
    //     0xad35fc: add             x3, x3, HEAP, lsl #32
    // 0xad3600: LoadField: r4 = r3->field_b
    //     0xad3600: ldur            w4, [x3, #0xb]
    // 0xad3604: r3 = LoadInt32Instr(r1)
    //     0xad3604: sbfx            x3, x1, #1, #0x1f
    // 0xad3608: stur            x3, [fp, #-0x28]
    // 0xad360c: r1 = LoadInt32Instr(r4)
    //     0xad360c: sbfx            x1, x4, #1, #0x1f
    // 0xad3610: cmp             x3, x1
    // 0xad3614: b.ne            #0xad3620
    // 0xad3618: mov             x1, x0
    // 0xad361c: r0 = _growToNextCapacity()
    //     0xad361c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad3620: ldur            x2, [fp, #-8]
    // 0xad3624: ldur            x3, [fp, #-0x28]
    // 0xad3628: add             x0, x3, #1
    // 0xad362c: lsl             x1, x0, #1
    // 0xad3630: StoreField: r2->field_b = r1
    //     0xad3630: stur            w1, [x2, #0xb]
    // 0xad3634: LoadField: r1 = r2->field_f
    //     0xad3634: ldur            w1, [x2, #0xf]
    // 0xad3638: DecompressPointer r1
    //     0xad3638: add             x1, x1, HEAP, lsl #32
    // 0xad363c: ldur            x0, [fp, #-0x20]
    // 0xad3640: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad3640: add             x25, x1, x3, lsl #2
    //     0xad3644: add             x25, x25, #0xf
    //     0xad3648: str             w0, [x25]
    //     0xad364c: tbz             w0, #0, #0xad3668
    //     0xad3650: ldurb           w16, [x1, #-1]
    //     0xad3654: ldurb           w17, [x0, #-1]
    //     0xad3658: and             x16, x17, x16, lsr #2
    //     0xad365c: tst             x16, HEAP, lsr #32
    //     0xad3660: b.eq            #0xad3668
    //     0xad3664: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad3668: b               #0xad3670
    // 0xad366c: ldur            x2, [fp, #-8]
    // 0xad3670: ldur            x0, [fp, #-0x18]
    // 0xad3674: r0 = Column()
    //     0xad3674: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad3678: mov             x1, x0
    // 0xad367c: r0 = Instance_Axis
    //     0xad367c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad3680: stur            x1, [fp, #-0x10]
    // 0xad3684: StoreField: r1->field_f = r0
    //     0xad3684: stur            w0, [x1, #0xf]
    // 0xad3688: r0 = Instance_MainAxisAlignment
    //     0xad3688: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad368c: ldr             x0, [x0, #0x730]
    // 0xad3690: StoreField: r1->field_13 = r0
    //     0xad3690: stur            w0, [x1, #0x13]
    // 0xad3694: r0 = Instance_MainAxisSize
    //     0xad3694: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad3698: ldr             x0, [x0, #0x738]
    // 0xad369c: ArrayStore: r1[0] = r0  ; List_4
    //     0xad369c: stur            w0, [x1, #0x17]
    // 0xad36a0: r0 = Instance_CrossAxisAlignment
    //     0xad36a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad36a4: ldr             x0, [x0, #0x740]
    // 0xad36a8: StoreField: r1->field_1b = r0
    //     0xad36a8: stur            w0, [x1, #0x1b]
    // 0xad36ac: r0 = Instance_VerticalDirection
    //     0xad36ac: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad36b0: ldr             x0, [x0, #0x748]
    // 0xad36b4: StoreField: r1->field_23 = r0
    //     0xad36b4: stur            w0, [x1, #0x23]
    // 0xad36b8: r0 = Instance_Clip
    //     0xad36b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad36bc: ldr             x0, [x0, #0x750]
    // 0xad36c0: StoreField: r1->field_2b = r0
    //     0xad36c0: stur            w0, [x1, #0x2b]
    // 0xad36c4: StoreField: r1->field_2f = rZR
    //     0xad36c4: stur            xzr, [x1, #0x2f]
    // 0xad36c8: ldur            x0, [fp, #-8]
    // 0xad36cc: StoreField: r1->field_b = r0
    //     0xad36cc: stur            w0, [x1, #0xb]
    // 0xad36d0: r0 = Scaffold()
    //     0xad36d0: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad36d4: ldur            x1, [fp, #-0x18]
    // 0xad36d8: StoreField: r0->field_13 = r1
    //     0xad36d8: stur            w1, [x0, #0x13]
    // 0xad36dc: ldur            x1, [fp, #-0x10]
    // 0xad36e0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad36e0: stur            w1, [x0, #0x17]
    // 0xad36e4: r1 = Instance_AlignmentDirectional
    //     0xad36e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad36e8: ldr             x1, [x1, #0x758]
    // 0xad36ec: StoreField: r0->field_2b = r1
    //     0xad36ec: stur            w1, [x0, #0x2b]
    // 0xad36f0: r1 = true
    //     0xad36f0: add             x1, NULL, #0x20  ; true
    // 0xad36f4: StoreField: r0->field_53 = r1
    //     0xad36f4: stur            w1, [x0, #0x53]
    // 0xad36f8: r2 = Instance_DragStartBehavior
    //     0xad36f8: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad36fc: StoreField: r0->field_57 = r2
    //     0xad36fc: stur            w2, [x0, #0x57]
    // 0xad3700: r2 = false
    //     0xad3700: add             x2, NULL, #0x30  ; false
    // 0xad3704: StoreField: r0->field_b = r2
    //     0xad3704: stur            w2, [x0, #0xb]
    // 0xad3708: StoreField: r0->field_f = r2
    //     0xad3708: stur            w2, [x0, #0xf]
    // 0xad370c: StoreField: r0->field_5f = r1
    //     0xad370c: stur            w1, [x0, #0x5f]
    // 0xad3710: StoreField: r0->field_63 = r1
    //     0xad3710: stur            w1, [x0, #0x63]
    // 0xad3714: LeaveFrame
    //     0xad3714: mov             SP, fp
    //     0xad3718: ldp             fp, lr, [SP], #0x10
    // 0xad371c: ret
    //     0xad371c: ret             
    // 0xad3720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3724: b               #0xad3428
  }
  [closure] NotificationListener<ScrollNotification> <anonymous closure>(dynamic) {
    // ** addr: 0xad3728, size: 0x16c
    // 0xad3728: EnterFrame
    //     0xad3728: stp             fp, lr, [SP, #-0x10]!
    //     0xad372c: mov             fp, SP
    // 0xad3730: AllocStack(0x40)
    //     0xad3730: sub             SP, SP, #0x40
    // 0xad3734: SetupParameters()
    //     0xad3734: ldr             x0, [fp, #0x10]
    //     0xad3738: ldur            w2, [x0, #0x17]
    //     0xad373c: add             x2, x2, HEAP, lsl #32
    //     0xad3740: stur            x2, [fp, #-8]
    // 0xad3744: CheckStackOverflow
    //     0xad3744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3748: cmp             SP, x16
    //     0xad374c: b.ls            #0xad386c
    // 0xad3750: LoadField: r1 = r2->field_f
    //     0xad3750: ldur            w1, [x2, #0xf]
    // 0xad3754: DecompressPointer r1
    //     0xad3754: add             x1, x1, HEAP, lsl #32
    // 0xad3758: r0 = controller()
    //     0xad3758: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad375c: ldur            x2, [fp, #-8]
    // 0xad3760: stur            x0, [fp, #-0x10]
    // 0xad3764: LoadField: r1 = r2->field_f
    //     0xad3764: ldur            w1, [x2, #0xf]
    // 0xad3768: DecompressPointer r1
    //     0xad3768: add             x1, x1, HEAP, lsl #32
    // 0xad376c: r0 = controller()
    //     0xad376c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3770: mov             x1, x0
    // 0xad3774: r0 = itemsCount()
    //     0xad3774: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xad3778: ldur            x2, [fp, #-8]
    // 0xad377c: stur            x0, [fp, #-0x18]
    // 0xad3780: LoadField: r1 = r2->field_f
    //     0xad3780: ldur            w1, [x2, #0xf]
    // 0xad3784: DecompressPointer r1
    //     0xad3784: add             x1, x1, HEAP, lsl #32
    // 0xad3788: r0 = controller()
    //     0xad3788: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad378c: mov             x1, x0
    // 0xad3790: r0 = itemsCount()
    //     0xad3790: bl              #0xad18ac  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::itemsCount
    // 0xad3794: scvtf           d0, x0
    // 0xad3798: d1 = 10.000000
    //     0xad3798: fmov            d1, #10.00000000
    // 0xad379c: fdiv            d2, d0, d1
    // 0xad37a0: fcmp            d2, d2
    // 0xad37a4: b.vs            #0xad3874
    // 0xad37a8: fcvtms          x0, d2
    // 0xad37ac: asr             x16, x0, #0x1e
    // 0xad37b0: cmp             x16, x0, asr #63
    // 0xad37b4: b.ne            #0xad3874
    // 0xad37b8: lsl             x0, x0, #1
    // 0xad37bc: r1 = LoadInt32Instr(r0)
    //     0xad37bc: sbfx            x1, x0, #1, #0x1f
    //     0xad37c0: tbz             w0, #0, #0xad37c8
    //     0xad37c4: ldur            x1, [x0, #7]
    // 0xad37c8: ldur            x0, [fp, #-0x18]
    // 0xad37cc: add             x3, x0, x1
    // 0xad37d0: stur            x3, [fp, #-0x20]
    // 0xad37d4: r1 = Function '<anonymous closure>':.
    //     0xad37d4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30920] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xad37d8: ldr             x1, [x1, #0x920]
    // 0xad37dc: r2 = Null
    //     0xad37dc: mov             x2, NULL
    // 0xad37e0: r0 = AllocateClosure()
    //     0xad37e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad37e4: ldur            x2, [fp, #-8]
    // 0xad37e8: r1 = Function '<anonymous closure>':.
    //     0xad37e8: add             x1, PP, #0x30, lsl #12  ; [pp+0x30928] AnonymousClosure: (0xad3894), in [package:nuonline/app/modules/article/views/article_view.dart] ArticleView::build (0xad340c)
    //     0xad37ec: ldr             x1, [x1, #0x928]
    // 0xad37f0: stur            x0, [fp, #-8]
    // 0xad37f4: r0 = AllocateClosure()
    //     0xad37f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad37f8: stur            x0, [fp, #-0x28]
    // 0xad37fc: r0 = ListView()
    //     0xad37fc: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xad3800: stur            x0, [fp, #-0x30]
    // 0xad3804: r16 = true
    //     0xad3804: add             x16, NULL, #0x20  ; true
    // 0xad3808: r30 = Instance_EdgeInsets
    //     0xad3808: add             lr, PP, #0x2a, lsl #12  ; [pp+0x2af58] Obj!EdgeInsets@e125e1
    //     0xad380c: ldr             lr, [lr, #0xf58]
    // 0xad3810: stp             lr, x16, [SP]
    // 0xad3814: mov             x1, x0
    // 0xad3818: ldur            x2, [fp, #-0x28]
    // 0xad381c: ldur            x3, [fp, #-0x20]
    // 0xad3820: ldur            x5, [fp, #-8]
    // 0xad3824: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x5, shrinkWrap, 0x4, null]
    //     0xad3824: add             x4, PP, #0x29, lsl #12  ; [pp+0x29100] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x5, "shrinkWrap", 0x4, Null]
    //     0xad3828: ldr             x4, [x4, #0x100]
    // 0xad382c: r0 = ListView.separated()
    //     0xad382c: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xad3830: ldur            x2, [fp, #-0x10]
    // 0xad3834: r1 = Function 'onPageScrolled':.
    //     0xad3834: add             x1, PP, #0x29, lsl #12  ; [pp+0x29a88] AnonymousClosure: (0xad3058), in [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::onPageScrolled (0xad3094)
    //     0xad3838: ldr             x1, [x1, #0xa88]
    // 0xad383c: r0 = AllocateClosure()
    //     0xad383c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3840: r1 = <ScrollNotification>
    //     0xad3840: add             x1, PP, #0x29, lsl #12  ; [pp+0x29110] TypeArguments: <ScrollNotification>
    //     0xad3844: ldr             x1, [x1, #0x110]
    // 0xad3848: stur            x0, [fp, #-8]
    // 0xad384c: r0 = NotificationListener()
    //     0xad384c: bl              #0x93e118  ; AllocateNotificationListenerStub -> NotificationListener<X0 bound Notification> (size=0x18)
    // 0xad3850: ldur            x1, [fp, #-8]
    // 0xad3854: StoreField: r0->field_13 = r1
    //     0xad3854: stur            w1, [x0, #0x13]
    // 0xad3858: ldur            x1, [fp, #-0x30]
    // 0xad385c: StoreField: r0->field_b = r1
    //     0xad385c: stur            w1, [x0, #0xb]
    // 0xad3860: LeaveFrame
    //     0xad3860: mov             SP, fp
    //     0xad3864: ldp             fp, lr, [SP], #0x10
    // 0xad3868: ret
    //     0xad3868: ret             
    // 0xad386c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad386c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3870: b               #0xad3750
    // 0xad3874: SaveReg d2
    //     0xad3874: str             q2, [SP, #-0x10]!
    // 0xad3878: d0 = 0.000000
    //     0xad3878: fmov            d0, d2
    // 0xad387c: r0 = 68
    //     0xad387c: movz            x0, #0x44
    // 0xad3880: r30 = DoubleToIntegerStub
    //     0xad3880: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xad3884: LoadField: r30 = r30->field_7
    //     0xad3884: ldur            lr, [lr, #7]
    // 0xad3888: blr             lr
    // 0xad388c: RestoreReg d2
    //     0xad388c: ldr             q2, [SP], #0x10
    // 0xad3890: b               #0xad37bc
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad3894, size: 0x188
    // 0xad3894: EnterFrame
    //     0xad3894: stp             fp, lr, [SP, #-0x10]!
    //     0xad3898: mov             fp, SP
    // 0xad389c: AllocStack(0x18)
    //     0xad389c: sub             SP, SP, #0x18
    // 0xad38a0: SetupParameters()
    //     0xad38a0: fmov            d0, #10.00000000
    //     0xad38a4: ldr             x0, [fp, #0x20]
    //     0xad38a8: ldur            w1, [x0, #0x17]
    //     0xad38ac: add             x1, x1, HEAP, lsl #32
    // 0xad38a0: d0 = 10.000000
    // 0xad38b0: CheckStackOverflow
    //     0xad38b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad38b4: cmp             SP, x16
    //     0xad38b8: b.ls            #0xad39e4
    // 0xad38bc: ldr             x0, [fp, #0x10]
    // 0xad38c0: r2 = LoadInt32Instr(r0)
    //     0xad38c0: sbfx            x2, x0, #1, #0x1f
    //     0xad38c4: tbz             w0, #0, #0xad38cc
    //     0xad38c8: ldur            x2, [x0, #7]
    // 0xad38cc: stur            x2, [fp, #-0x10]
    // 0xad38d0: add             x0, x2, #5
    // 0xad38d4: scvtf           d1, x0
    // 0xad38d8: fdiv            d2, d1, d0
    // 0xad38dc: fcmp            d2, d2
    // 0xad38e0: b.vs            #0xad39ec
    // 0xad38e4: fcvtms          x0, d2
    // 0xad38e8: asr             x16, x0, #0x1e
    // 0xad38ec: cmp             x16, x0, asr #63
    // 0xad38f0: b.ne            #0xad39ec
    // 0xad38f4: lsl             x0, x0, #1
    // 0xad38f8: r3 = LoadInt32Instr(r0)
    //     0xad38f8: sbfx            x3, x0, #1, #0x1f
    //     0xad38fc: tbz             w0, #0, #0xad3904
    //     0xad3900: ldur            x3, [x0, #7]
    // 0xad3904: sub             x0, x2, x3
    // 0xad3908: stur            x0, [fp, #-8]
    // 0xad390c: LoadField: r3 = r1->field_f
    //     0xad390c: ldur            w3, [x1, #0xf]
    // 0xad3910: DecompressPointer r3
    //     0xad3910: add             x3, x3, HEAP, lsl #32
    // 0xad3914: mov             x1, x3
    // 0xad3918: r0 = controller()
    //     0xad3918: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad391c: mov             x1, x0
    // 0xad3920: ldur            x2, [fp, #-8]
    // 0xad3924: r0 = find()
    //     0xad3924: bl              #0xad2f38  ; [package:nuonline/app/modules/article/article_author/controllers/article_author_controller.dart] _ArticleAuthorController&GetxController&PagingMixin::find
    // 0xad3928: stur            x0, [fp, #-0x18]
    // 0xad392c: cmp             w0, NULL
    // 0xad3930: b.ne            #0xad3948
    // 0xad3934: r0 = Instance_NArticleListTile
    //     0xad3934: add             x0, PP, #0x29, lsl #12  ; [pp+0x29a90] Obj!NArticleListTile@e20e61
    //     0xad3938: ldr             x0, [x0, #0xa90]
    // 0xad393c: LeaveFrame
    //     0xad393c: mov             SP, fp
    //     0xad3940: ldp             fp, lr, [SP], #0x10
    // 0xad3944: ret
    //     0xad3944: ret             
    // 0xad3948: ldur            x1, [fp, #-0x10]
    // 0xad394c: r2 = 10
    //     0xad394c: movz            x2, #0xa
    // 0xad3950: sdiv            x4, x1, x2
    // 0xad3954: msub            x3, x4, x2, x1
    // 0xad3958: cmp             x3, xzr
    // 0xad395c: b.lt            #0xad3a14
    // 0xad3960: cmp             x3, #4
    // 0xad3964: b.ne            #0xad39c4
    // 0xad3968: r0 = find()
    //     0xad3968: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad396c: mov             x1, x0
    // 0xad3970: r0 = _adsVisibility()
    //     0xad3970: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xad3974: mov             x2, x0
    // 0xad3978: r1 = Null
    //     0xad3978: mov             x1, NULL
    // 0xad397c: r0 = AdsConfig.fromJson()
    //     0xad397c: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xad3980: LoadField: r1 = r0->field_b
    //     0xad3980: ldur            w1, [x0, #0xb]
    // 0xad3984: DecompressPointer r1
    //     0xad3984: add             x1, x1, HEAP, lsl #32
    // 0xad3988: LoadField: r0 = r1->field_7
    //     0xad3988: ldur            w0, [x1, #7]
    // 0xad398c: DecompressPointer r0
    //     0xad398c: add             x0, x0, HEAP, lsl #32
    // 0xad3990: tbnz            w0, #4, #0xad39b0
    // 0xad3994: r0 = find()
    //     0xad3994: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xad3998: mov             x1, x0
    // 0xad399c: r0 = articleNative()
    //     0xad399c: bl              #0xa35bf0  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::articleNative
    // 0xad39a0: r0 = AdmobArticleNativeWidget()
    //     0xad39a0: bl              #0xa35c40  ; AllocateAdmobArticleNativeWidgetStub -> AdmobArticleNativeWidget (size=0xc)
    // 0xad39a4: LeaveFrame
    //     0xad39a4: mov             SP, fp
    //     0xad39a8: ldp             fp, lr, [SP], #0x10
    // 0xad39ac: ret
    //     0xad39ac: ret             
    // 0xad39b0: r0 = Instance_SizedBox
    //     0xad39b0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23c40] Obj!SizedBox@e1dfa1
    //     0xad39b4: ldr             x0, [x0, #0xc40]
    // 0xad39b8: LeaveFrame
    //     0xad39b8: mov             SP, fp
    //     0xad39bc: ldp             fp, lr, [SP], #0x10
    // 0xad39c0: ret
    //     0xad39c0: ret             
    // 0xad39c4: r0 = ArticleItem()
    //     0xad39c4: bl              #0xa35c34  ; AllocateArticleItemStub -> ArticleItem (size=0x18)
    // 0xad39c8: ldur            x1, [fp, #-0x18]
    // 0xad39cc: StoreField: r0->field_b = r1
    //     0xad39cc: stur            w1, [x0, #0xb]
    // 0xad39d0: r1 = false
    //     0xad39d0: add             x1, NULL, #0x30  ; false
    // 0xad39d4: StoreField: r0->field_13 = r1
    //     0xad39d4: stur            w1, [x0, #0x13]
    // 0xad39d8: LeaveFrame
    //     0xad39d8: mov             SP, fp
    //     0xad39dc: ldp             fp, lr, [SP], #0x10
    // 0xad39e0: ret
    //     0xad39e0: ret             
    // 0xad39e4: r0 = StackOverflowSharedWithFPURegs()
    //     0xad39e4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xad39e8: b               #0xad38bc
    // 0xad39ec: SaveReg d2
    //     0xad39ec: str             q2, [SP, #-0x10]!
    // 0xad39f0: stp             x1, x2, [SP, #-0x10]!
    // 0xad39f4: d0 = 0.000000
    //     0xad39f4: fmov            d0, d2
    // 0xad39f8: r0 = 68
    //     0xad39f8: movz            x0, #0x44
    // 0xad39fc: r30 = DoubleToIntegerStub
    //     0xad39fc: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xad3a00: LoadField: r30 = r30->field_7
    //     0xad3a00: ldur            lr, [lr, #7]
    // 0xad3a04: blr             lr
    // 0xad3a08: ldp             x1, x2, [SP], #0x10
    // 0xad3a0c: RestoreReg d2
    //     0xad3a0c: ldr             q2, [SP], #0x10
    // 0xad3a10: b               #0xad38f8
    // 0xad3a14: add             x3, x3, x2
    // 0xad3a18: b               #0xad3960
  }
}
