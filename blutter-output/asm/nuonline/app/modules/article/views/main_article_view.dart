// lib: , url: package:nuonline/app/modules/article/views/main_article_view.dart

// class id: 1050150, size: 0x8
class :: {
}

// class id: 5306, size: 0x14, field offset: 0x14
class MainArticleView extends GetView<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xad3a1c, size: 0x2f8
    // 0xad3a1c: EnterFrame
    //     0xad3a1c: stp             fp, lr, [SP, #-0x10]!
    //     0xad3a20: mov             fp, SP
    // 0xad3a24: AllocStack(0x48)
    //     0xad3a24: sub             SP, SP, #0x48
    // 0xad3a28: SetupParameters(MainArticleView this /* r1 => r1, fp-0x8 */)
    //     0xad3a28: stur            x1, [fp, #-8]
    // 0xad3a2c: CheckStackOverflow
    //     0xad3a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3a30: cmp             SP, x16
    //     0xad3a34: b.ls            #0xad3d00
    // 0xad3a38: r1 = 1
    //     0xad3a38: movz            x1, #0x1
    // 0xad3a3c: r0 = AllocateContext()
    //     0xad3a3c: bl              #0xec126c  ; AllocateContextStub
    // 0xad3a40: ldur            x1, [fp, #-8]
    // 0xad3a44: stur            x0, [fp, #-0x10]
    // 0xad3a48: StoreField: r0->field_f = r1
    //     0xad3a48: stur            w1, [x0, #0xf]
    // 0xad3a4c: r0 = IconButton()
    //     0xad3a4c: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad3a50: mov             x3, x0
    // 0xad3a54: r0 = Instance_EdgeInsets
    //     0xad3a54: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad3a58: stur            x3, [fp, #-0x18]
    // 0xad3a5c: StoreField: r3->field_13 = r0
    //     0xad3a5c: stur            w0, [x3, #0x13]
    // 0xad3a60: r1 = Function '<anonymous closure>':.
    //     0xad3a60: add             x1, PP, #0x36, lsl #12  ; [pp+0x362a8] AnonymousClosure: (0xad53a4), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::build (0xad3a1c)
    //     0xad3a64: ldr             x1, [x1, #0x2a8]
    // 0xad3a68: r2 = Null
    //     0xad3a68: mov             x2, NULL
    // 0xad3a6c: r0 = AllocateClosure()
    //     0xad3a6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3a70: mov             x1, x0
    // 0xad3a74: ldur            x0, [fp, #-0x18]
    // 0xad3a78: StoreField: r0->field_3b = r1
    //     0xad3a78: stur            w1, [x0, #0x3b]
    // 0xad3a7c: r1 = false
    //     0xad3a7c: add             x1, NULL, #0x30  ; false
    // 0xad3a80: StoreField: r0->field_47 = r1
    //     0xad3a80: stur            w1, [x0, #0x47]
    // 0xad3a84: r2 = Instance_Icon
    //     0xad3a84: add             x2, PP, #0x28, lsl #12  ; [pp+0x28fc8] Obj!Icon@e243b1
    //     0xad3a88: ldr             x2, [x2, #0xfc8]
    // 0xad3a8c: StoreField: r0->field_1f = r2
    //     0xad3a8c: stur            w2, [x0, #0x1f]
    // 0xad3a90: r2 = Instance__IconButtonVariant
    //     0xad3a90: add             x2, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad3a94: ldr             x2, [x2, #0xf78]
    // 0xad3a98: StoreField: r0->field_63 = r2
    //     0xad3a98: stur            w2, [x0, #0x63]
    // 0xad3a9c: r0 = Obx()
    //     0xad3a9c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad3aa0: ldur            x2, [fp, #-0x10]
    // 0xad3aa4: r1 = Function '<anonymous closure>':.
    //     0xad3aa4: add             x1, PP, #0x36, lsl #12  ; [pp+0x362b0] AnonymousClosure: (0xad5214), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::build (0xad3a1c)
    //     0xad3aa8: ldr             x1, [x1, #0x2b0]
    // 0xad3aac: stur            x0, [fp, #-0x10]
    // 0xad3ab0: r0 = AllocateClosure()
    //     0xad3ab0: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3ab4: mov             x1, x0
    // 0xad3ab8: ldur            x0, [fp, #-0x10]
    // 0xad3abc: StoreField: r0->field_b = r1
    //     0xad3abc: stur            w1, [x0, #0xb]
    // 0xad3ac0: r0 = SizedBox()
    //     0xad3ac0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad3ac4: mov             x1, x0
    // 0xad3ac8: r0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xad3ac8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c58] 1.7976931348623157e+308
    //     0xad3acc: ldr             x0, [x0, #0xc58]
    // 0xad3ad0: stur            x1, [fp, #-0x20]
    // 0xad3ad4: StoreField: r1->field_f = r0
    //     0xad3ad4: stur            w0, [x1, #0xf]
    // 0xad3ad8: ldur            x0, [fp, #-0x10]
    // 0xad3adc: StoreField: r1->field_b = r0
    //     0xad3adc: stur            w0, [x1, #0xb]
    // 0xad3ae0: r0 = Padding()
    //     0xad3ae0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad3ae4: mov             x1, x0
    // 0xad3ae8: r0 = Instance_EdgeInsets
    //     0xad3ae8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c60] Obj!EdgeInsets@e12611
    //     0xad3aec: ldr             x0, [x0, #0xc60]
    // 0xad3af0: stur            x1, [fp, #-0x10]
    // 0xad3af4: StoreField: r1->field_f = r0
    //     0xad3af4: stur            w0, [x1, #0xf]
    // 0xad3af8: ldur            x0, [fp, #-0x20]
    // 0xad3afc: StoreField: r1->field_b = r0
    //     0xad3afc: stur            w0, [x1, #0xb]
    // 0xad3b00: r0 = SizedBox()
    //     0xad3b00: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad3b04: mov             x1, x0
    // 0xad3b08: r0 = 56.000000
    //     0xad3b08: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c68] 56
    //     0xad3b0c: ldr             x0, [x0, #0xc68]
    // 0xad3b10: stur            x1, [fp, #-0x20]
    // 0xad3b14: StoreField: r1->field_13 = r0
    //     0xad3b14: stur            w0, [x1, #0x13]
    // 0xad3b18: ldur            x0, [fp, #-0x10]
    // 0xad3b1c: StoreField: r1->field_b = r0
    //     0xad3b1c: stur            w0, [x1, #0xb]
    // 0xad3b20: r0 = IconButton()
    //     0xad3b20: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xad3b24: mov             x3, x0
    // 0xad3b28: r0 = Instance_EdgeInsets
    //     0xad3b28: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xad3b2c: stur            x3, [fp, #-0x10]
    // 0xad3b30: StoreField: r3->field_13 = r0
    //     0xad3b30: stur            w0, [x3, #0x13]
    // 0xad3b34: r1 = Function '<anonymous closure>':.
    //     0xad3b34: add             x1, PP, #0x36, lsl #12  ; [pp+0x362b8] AnonymousClosure: (0xad51b8), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::build (0xad3a1c)
    //     0xad3b38: ldr             x1, [x1, #0x2b8]
    // 0xad3b3c: r2 = Null
    //     0xad3b3c: mov             x2, NULL
    // 0xad3b40: r0 = AllocateClosure()
    //     0xad3b40: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3b44: mov             x1, x0
    // 0xad3b48: ldur            x0, [fp, #-0x10]
    // 0xad3b4c: StoreField: r0->field_3b = r1
    //     0xad3b4c: stur            w1, [x0, #0x3b]
    // 0xad3b50: r3 = false
    //     0xad3b50: add             x3, NULL, #0x30  ; false
    // 0xad3b54: StoreField: r0->field_47 = r3
    //     0xad3b54: stur            w3, [x0, #0x47]
    // 0xad3b58: r1 = Instance_Icon
    //     0xad3b58: add             x1, PP, #0x29, lsl #12  ; [pp+0x296b8] Obj!Icon@e24371
    //     0xad3b5c: ldr             x1, [x1, #0x6b8]
    // 0xad3b60: StoreField: r0->field_1f = r1
    //     0xad3b60: stur            w1, [x0, #0x1f]
    // 0xad3b64: r1 = Instance__IconButtonVariant
    //     0xad3b64: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xad3b68: ldr             x1, [x1, #0xf78]
    // 0xad3b6c: StoreField: r0->field_63 = r1
    //     0xad3b6c: stur            w1, [x0, #0x63]
    // 0xad3b70: r1 = Null
    //     0xad3b70: mov             x1, NULL
    // 0xad3b74: r2 = 2
    //     0xad3b74: movz            x2, #0x2
    // 0xad3b78: r0 = AllocateArray()
    //     0xad3b78: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad3b7c: mov             x2, x0
    // 0xad3b80: ldur            x0, [fp, #-0x10]
    // 0xad3b84: stur            x2, [fp, #-0x28]
    // 0xad3b88: StoreField: r2->field_f = r0
    //     0xad3b88: stur            w0, [x2, #0xf]
    // 0xad3b8c: r1 = <Widget>
    //     0xad3b8c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad3b90: r0 = AllocateGrowableArray()
    //     0xad3b90: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad3b94: mov             x1, x0
    // 0xad3b98: ldur            x0, [fp, #-0x28]
    // 0xad3b9c: stur            x1, [fp, #-0x10]
    // 0xad3ba0: StoreField: r1->field_f = r0
    //     0xad3ba0: stur            w0, [x1, #0xf]
    // 0xad3ba4: r0 = 2
    //     0xad3ba4: movz            x0, #0x2
    // 0xad3ba8: StoreField: r1->field_b = r0
    //     0xad3ba8: stur            w0, [x1, #0xb]
    // 0xad3bac: r0 = AppBar()
    //     0xad3bac: bl              #0xa37fc0  ; AllocateAppBarStub -> AppBar (size=0x8c)
    // 0xad3bb0: stur            x0, [fp, #-0x28]
    // 0xad3bb4: ldur            x16, [fp, #-0x18]
    // 0xad3bb8: r30 = true
    //     0xad3bb8: add             lr, NULL, #0x20  ; true
    // 0xad3bbc: stp             lr, x16, [SP, #0x10]
    // 0xad3bc0: ldur            x16, [fp, #-0x20]
    // 0xad3bc4: ldur            lr, [fp, #-0x10]
    // 0xad3bc8: stp             lr, x16, [SP]
    // 0xad3bcc: mov             x1, x0
    // 0xad3bd0: r4 = const [0, 0x5, 0x4, 0x1, actions, 0x4, centerTitle, 0x2, leading, 0x1, title, 0x3, null]
    //     0xad3bd0: add             x4, PP, #0x36, lsl #12  ; [pp+0x362c0] List(13) [0, 0x5, 0x4, 0x1, "actions", 0x4, "centerTitle", 0x2, "leading", 0x1, "title", 0x3, Null]
    //     0xad3bd4: ldr             x4, [x4, #0x2c0]
    // 0xad3bd8: r0 = AppBar()
    //     0xad3bd8: bl              #0xa37554  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0xad3bdc: r0 = PreferredSize()
    //     0xad3bdc: bl              #0xa3b694  ; AllocatePreferredSizeStub -> PreferredSize (size=0x14)
    // 0xad3be0: mov             x2, x0
    // 0xad3be4: r0 = Instance_Size
    //     0xad3be4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27c88] Obj!Size@e2c1c1
    //     0xad3be8: ldr             x0, [x0, #0xc88]
    // 0xad3bec: stur            x2, [fp, #-0x10]
    // 0xad3bf0: StoreField: r2->field_f = r0
    //     0xad3bf0: stur            w0, [x2, #0xf]
    // 0xad3bf4: ldur            x0, [fp, #-0x28]
    // 0xad3bf8: StoreField: r2->field_b = r0
    //     0xad3bf8: stur            w0, [x2, #0xb]
    // 0xad3bfc: ldur            x1, [fp, #-8]
    // 0xad3c00: r0 = controller()
    //     0xad3c00: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3c04: LoadField: r2 = r0->field_27
    //     0xad3c04: ldur            w2, [x0, #0x27]
    // 0xad3c08: DecompressPointer r2
    //     0xad3c08: add             x2, x2, HEAP, lsl #32
    // 0xad3c0c: r16 = Sentinel
    //     0xad3c0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad3c10: cmp             w2, w16
    // 0xad3c14: b.eq            #0xad3d08
    // 0xad3c18: ldur            x1, [fp, #-8]
    // 0xad3c1c: stur            x2, [fp, #-0x18]
    // 0xad3c20: r0 = _buildMainContent()
    //     0xad3c20: bl              #0xad4958  ; [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildMainContent
    // 0xad3c24: ldur            x1, [fp, #-8]
    // 0xad3c28: stur            x0, [fp, #-8]
    // 0xad3c2c: r0 = _buildIslamicContent()
    //     0xad3c2c: bl              #0xad3d14  ; [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent
    // 0xad3c30: r1 = Null
    //     0xad3c30: mov             x1, NULL
    // 0xad3c34: r2 = 4
    //     0xad3c34: movz            x2, #0x4
    // 0xad3c38: stur            x0, [fp, #-0x20]
    // 0xad3c3c: r0 = AllocateArray()
    //     0xad3c3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad3c40: mov             x2, x0
    // 0xad3c44: ldur            x0, [fp, #-8]
    // 0xad3c48: stur            x2, [fp, #-0x28]
    // 0xad3c4c: StoreField: r2->field_f = r0
    //     0xad3c4c: stur            w0, [x2, #0xf]
    // 0xad3c50: ldur            x0, [fp, #-0x20]
    // 0xad3c54: StoreField: r2->field_13 = r0
    //     0xad3c54: stur            w0, [x2, #0x13]
    // 0xad3c58: r1 = <Widget>
    //     0xad3c58: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad3c5c: r0 = AllocateGrowableArray()
    //     0xad3c5c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad3c60: mov             x1, x0
    // 0xad3c64: ldur            x0, [fp, #-0x28]
    // 0xad3c68: stur            x1, [fp, #-8]
    // 0xad3c6c: StoreField: r1->field_f = r0
    //     0xad3c6c: stur            w0, [x1, #0xf]
    // 0xad3c70: r0 = 4
    //     0xad3c70: movz            x0, #0x4
    // 0xad3c74: StoreField: r1->field_b = r0
    //     0xad3c74: stur            w0, [x1, #0xb]
    // 0xad3c78: r0 = TabBarView()
    //     0xad3c78: bl              #0xa41828  ; AllocateTabBarViewStub -> TabBarView (size=0x28)
    // 0xad3c7c: mov             x1, x0
    // 0xad3c80: ldur            x0, [fp, #-8]
    // 0xad3c84: stur            x1, [fp, #-0x20]
    // 0xad3c88: StoreField: r1->field_f = r0
    //     0xad3c88: stur            w0, [x1, #0xf]
    // 0xad3c8c: ldur            x0, [fp, #-0x18]
    // 0xad3c90: StoreField: r1->field_b = r0
    //     0xad3c90: stur            w0, [x1, #0xb]
    // 0xad3c94: r0 = Instance_DragStartBehavior
    //     0xad3c94: ldr             x0, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad3c98: ArrayStore: r1[0] = r0  ; List_4
    //     0xad3c98: stur            w0, [x1, #0x17]
    // 0xad3c9c: d0 = 1.000000
    //     0xad3c9c: fmov            d0, #1.00000000
    // 0xad3ca0: StoreField: r1->field_1b = d0
    //     0xad3ca0: stur            d0, [x1, #0x1b]
    // 0xad3ca4: r2 = Instance_Clip
    //     0xad3ca4: add             x2, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xad3ca8: ldr             x2, [x2, #0x7c0]
    // 0xad3cac: StoreField: r1->field_23 = r2
    //     0xad3cac: stur            w2, [x1, #0x23]
    // 0xad3cb0: r0 = Scaffold()
    //     0xad3cb0: bl              #0xa3753c  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0xad3cb4: ldur            x1, [fp, #-0x10]
    // 0xad3cb8: StoreField: r0->field_13 = r1
    //     0xad3cb8: stur            w1, [x0, #0x13]
    // 0xad3cbc: ldur            x1, [fp, #-0x20]
    // 0xad3cc0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad3cc0: stur            w1, [x0, #0x17]
    // 0xad3cc4: r1 = Instance_AlignmentDirectional
    //     0xad3cc4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25758] Obj!AlignmentDirectional@e13d71
    //     0xad3cc8: ldr             x1, [x1, #0x758]
    // 0xad3ccc: StoreField: r0->field_2b = r1
    //     0xad3ccc: stur            w1, [x0, #0x2b]
    // 0xad3cd0: r1 = true
    //     0xad3cd0: add             x1, NULL, #0x20  ; true
    // 0xad3cd4: StoreField: r0->field_53 = r1
    //     0xad3cd4: stur            w1, [x0, #0x53]
    // 0xad3cd8: r2 = Instance_DragStartBehavior
    //     0xad3cd8: ldr             x2, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xad3cdc: StoreField: r0->field_57 = r2
    //     0xad3cdc: stur            w2, [x0, #0x57]
    // 0xad3ce0: r2 = false
    //     0xad3ce0: add             x2, NULL, #0x30  ; false
    // 0xad3ce4: StoreField: r0->field_b = r2
    //     0xad3ce4: stur            w2, [x0, #0xb]
    // 0xad3ce8: StoreField: r0->field_f = r2
    //     0xad3ce8: stur            w2, [x0, #0xf]
    // 0xad3cec: StoreField: r0->field_5f = r1
    //     0xad3cec: stur            w1, [x0, #0x5f]
    // 0xad3cf0: StoreField: r0->field_63 = r1
    //     0xad3cf0: stur            w1, [x0, #0x63]
    // 0xad3cf4: LeaveFrame
    //     0xad3cf4: mov             SP, fp
    //     0xad3cf8: ldp             fp, lr, [SP], #0x10
    // 0xad3cfc: ret
    //     0xad3cfc: ret             
    // 0xad3d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3d00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3d04: b               #0xad3a38
    // 0xad3d08: r9 = tabController
    //     0xad3d08: add             x9, PP, #0x36, lsl #12  ; [pp+0x362c8] Field <MainArticleController.tabController>: late (offset: 0x28)
    //     0xad3d0c: ldr             x9, [x9, #0x2c8]
    // 0xad3d10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad3d10: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildIslamicContent(/* No info */) {
    // ** addr: 0xad3d14, size: 0x144
    // 0xad3d14: EnterFrame
    //     0xad3d14: stp             fp, lr, [SP, #-0x10]!
    //     0xad3d18: mov             fp, SP
    // 0xad3d1c: AllocStack(0x18)
    //     0xad3d1c: sub             SP, SP, #0x18
    // 0xad3d20: SetupParameters(MainArticleView this /* r1 => r1, fp-0x8 */)
    //     0xad3d20: stur            x1, [fp, #-8]
    // 0xad3d24: r1 = 1
    //     0xad3d24: movz            x1, #0x1
    // 0xad3d28: r0 = AllocateContext()
    //     0xad3d28: bl              #0xec126c  ; AllocateContextStub
    // 0xad3d2c: mov             x1, x0
    // 0xad3d30: ldur            x0, [fp, #-8]
    // 0xad3d34: stur            x1, [fp, #-0x10]
    // 0xad3d38: StoreField: r1->field_f = r0
    //     0xad3d38: stur            w0, [x1, #0xf]
    // 0xad3d3c: r0 = Obx()
    //     0xad3d3c: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad3d40: ldur            x2, [fp, #-0x10]
    // 0xad3d44: r1 = Function '<anonymous closure>':.
    //     0xad3d44: add             x1, PP, #0x36, lsl #12  ; [pp+0x362e8] AnonymousClosure: (0xad459c), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad3d48: ldr             x1, [x1, #0x2e8]
    // 0xad3d4c: stur            x0, [fp, #-8]
    // 0xad3d50: r0 = AllocateClosure()
    //     0xad3d50: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3d54: mov             x1, x0
    // 0xad3d58: ldur            x0, [fp, #-8]
    // 0xad3d5c: StoreField: r0->field_b = r1
    //     0xad3d5c: stur            w1, [x0, #0xb]
    // 0xad3d60: r0 = Obx()
    //     0xad3d60: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad3d64: ldur            x2, [fp, #-0x10]
    // 0xad3d68: r1 = Function '<anonymous closure>':.
    //     0xad3d68: add             x1, PP, #0x36, lsl #12  ; [pp+0x362f0] AnonymousClosure: (0xad3e58), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad3d6c: ldr             x1, [x1, #0x2f0]
    // 0xad3d70: stur            x0, [fp, #-0x10]
    // 0xad3d74: r0 = AllocateClosure()
    //     0xad3d74: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3d78: mov             x1, x0
    // 0xad3d7c: ldur            x0, [fp, #-0x10]
    // 0xad3d80: StoreField: r0->field_b = r1
    //     0xad3d80: stur            w1, [x0, #0xb]
    // 0xad3d84: r1 = <FlexParentData>
    //     0xad3d84: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad3d88: ldr             x1, [x1, #0x720]
    // 0xad3d8c: r0 = Expanded()
    //     0xad3d8c: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad3d90: mov             x3, x0
    // 0xad3d94: r0 = 1
    //     0xad3d94: movz            x0, #0x1
    // 0xad3d98: stur            x3, [fp, #-0x18]
    // 0xad3d9c: StoreField: r3->field_13 = r0
    //     0xad3d9c: stur            x0, [x3, #0x13]
    // 0xad3da0: r0 = Instance_FlexFit
    //     0xad3da0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad3da4: ldr             x0, [x0, #0x728]
    // 0xad3da8: StoreField: r3->field_1b = r0
    //     0xad3da8: stur            w0, [x3, #0x1b]
    // 0xad3dac: ldur            x0, [fp, #-0x10]
    // 0xad3db0: StoreField: r3->field_b = r0
    //     0xad3db0: stur            w0, [x3, #0xb]
    // 0xad3db4: r1 = Null
    //     0xad3db4: mov             x1, NULL
    // 0xad3db8: r2 = 4
    //     0xad3db8: movz            x2, #0x4
    // 0xad3dbc: r0 = AllocateArray()
    //     0xad3dbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad3dc0: mov             x2, x0
    // 0xad3dc4: ldur            x0, [fp, #-8]
    // 0xad3dc8: stur            x2, [fp, #-0x10]
    // 0xad3dcc: StoreField: r2->field_f = r0
    //     0xad3dcc: stur            w0, [x2, #0xf]
    // 0xad3dd0: ldur            x0, [fp, #-0x18]
    // 0xad3dd4: StoreField: r2->field_13 = r0
    //     0xad3dd4: stur            w0, [x2, #0x13]
    // 0xad3dd8: r1 = <Widget>
    //     0xad3dd8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad3ddc: r0 = AllocateGrowableArray()
    //     0xad3ddc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad3de0: mov             x1, x0
    // 0xad3de4: ldur            x0, [fp, #-0x10]
    // 0xad3de8: stur            x1, [fp, #-8]
    // 0xad3dec: StoreField: r1->field_f = r0
    //     0xad3dec: stur            w0, [x1, #0xf]
    // 0xad3df0: r0 = 4
    //     0xad3df0: movz            x0, #0x4
    // 0xad3df4: StoreField: r1->field_b = r0
    //     0xad3df4: stur            w0, [x1, #0xb]
    // 0xad3df8: r0 = Column()
    //     0xad3df8: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad3dfc: r1 = Instance_Axis
    //     0xad3dfc: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad3e00: StoreField: r0->field_f = r1
    //     0xad3e00: stur            w1, [x0, #0xf]
    // 0xad3e04: r1 = Instance_MainAxisAlignment
    //     0xad3e04: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad3e08: ldr             x1, [x1, #0x730]
    // 0xad3e0c: StoreField: r0->field_13 = r1
    //     0xad3e0c: stur            w1, [x0, #0x13]
    // 0xad3e10: r1 = Instance_MainAxisSize
    //     0xad3e10: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad3e14: ldr             x1, [x1, #0x738]
    // 0xad3e18: ArrayStore: r0[0] = r1  ; List_4
    //     0xad3e18: stur            w1, [x0, #0x17]
    // 0xad3e1c: r1 = Instance_CrossAxisAlignment
    //     0xad3e1c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad3e20: ldr             x1, [x1, #0x740]
    // 0xad3e24: StoreField: r0->field_1b = r1
    //     0xad3e24: stur            w1, [x0, #0x1b]
    // 0xad3e28: r1 = Instance_VerticalDirection
    //     0xad3e28: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad3e2c: ldr             x1, [x1, #0x748]
    // 0xad3e30: StoreField: r0->field_23 = r1
    //     0xad3e30: stur            w1, [x0, #0x23]
    // 0xad3e34: r1 = Instance_Clip
    //     0xad3e34: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad3e38: ldr             x1, [x1, #0x750]
    // 0xad3e3c: StoreField: r0->field_2b = r1
    //     0xad3e3c: stur            w1, [x0, #0x2b]
    // 0xad3e40: StoreField: r0->field_2f = rZR
    //     0xad3e40: stur            xzr, [x0, #0x2f]
    // 0xad3e44: ldur            x1, [fp, #-8]
    // 0xad3e48: StoreField: r0->field_b = r1
    //     0xad3e48: stur            w1, [x0, #0xb]
    // 0xad3e4c: LeaveFrame
    //     0xad3e4c: mov             SP, fp
    //     0xad3e50: ldp             fp, lr, [SP], #0x10
    // 0xad3e54: ret
    //     0xad3e54: ret             
  }
  [closure] NIndexedStack <anonymous closure>(dynamic) {
    // ** addr: 0xad3e58, size: 0x120
    // 0xad3e58: EnterFrame
    //     0xad3e58: stp             fp, lr, [SP, #-0x10]!
    //     0xad3e5c: mov             fp, SP
    // 0xad3e60: AllocStack(0x28)
    //     0xad3e60: sub             SP, SP, #0x28
    // 0xad3e64: SetupParameters()
    //     0xad3e64: ldr             x0, [fp, #0x10]
    //     0xad3e68: ldur            w2, [x0, #0x17]
    //     0xad3e6c: add             x2, x2, HEAP, lsl #32
    //     0xad3e70: stur            x2, [fp, #-8]
    // 0xad3e74: CheckStackOverflow
    //     0xad3e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3e78: cmp             SP, x16
    //     0xad3e7c: b.ls            #0xad3f70
    // 0xad3e80: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad3e80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad3e84: ldr             x0, [x0, #0x2670]
    //     0xad3e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad3e8c: cmp             w0, w16
    //     0xad3e90: b.ne            #0xad3e9c
    //     0xad3e94: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad3e98: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad3e9c: r0 = GetNavigation.isDarkMode()
    //     0xad3e9c: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xad3ea0: r1 = <bool>
    //     0xad3ea0: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xad3ea4: stur            x0, [fp, #-0x10]
    // 0xad3ea8: r0 = ValueKey()
    //     0xad3ea8: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xad3eac: mov             x2, x0
    // 0xad3eb0: ldur            x0, [fp, #-0x10]
    // 0xad3eb4: stur            x2, [fp, #-0x18]
    // 0xad3eb8: StoreField: r2->field_b = r0
    //     0xad3eb8: stur            w0, [x2, #0xb]
    // 0xad3ebc: ldur            x0, [fp, #-8]
    // 0xad3ec0: LoadField: r1 = r0->field_f
    //     0xad3ec0: ldur            w1, [x0, #0xf]
    // 0xad3ec4: DecompressPointer r1
    //     0xad3ec4: add             x1, x1, HEAP, lsl #32
    // 0xad3ec8: r0 = controller()
    //     0xad3ec8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3ecc: LoadField: r1 = r0->field_33
    //     0xad3ecc: ldur            w1, [x0, #0x33]
    // 0xad3ed0: DecompressPointer r1
    //     0xad3ed0: add             x1, x1, HEAP, lsl #32
    // 0xad3ed4: r0 = value()
    //     0xad3ed4: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad3ed8: ldur            x2, [fp, #-8]
    // 0xad3edc: stur            x0, [fp, #-0x10]
    // 0xad3ee0: LoadField: r1 = r2->field_f
    //     0xad3ee0: ldur            w1, [x2, #0xf]
    // 0xad3ee4: DecompressPointer r1
    //     0xad3ee4: add             x1, x1, HEAP, lsl #32
    // 0xad3ee8: r0 = controller()
    //     0xad3ee8: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad3eec: mov             x1, x0
    // 0xad3ef0: r0 = islamicChannels()
    //     0xad3ef0: bl              #0x8ab6ac  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels
    // 0xad3ef4: LoadField: r1 = r0->field_b
    //     0xad3ef4: ldur            w1, [x0, #0xb]
    // 0xad3ef8: r0 = LoadInt32Instr(r1)
    //     0xad3ef8: sbfx            x0, x1, #1, #0x1f
    // 0xad3efc: add             x1, x0, #1
    // 0xad3f00: ldur            x0, [fp, #-0x10]
    // 0xad3f04: stur            x1, [fp, #-0x28]
    // 0xad3f08: r2 = LoadInt32Instr(r0)
    //     0xad3f08: sbfx            x2, x0, #1, #0x1f
    //     0xad3f0c: tbz             w0, #0, #0xad3f14
    //     0xad3f10: ldur            x2, [x0, #7]
    // 0xad3f14: stur            x2, [fp, #-0x20]
    // 0xad3f18: r0 = NIndexedStack()
    //     0xad3f18: bl              #0xad3f78  ; AllocateNIndexedStackStub -> NIndexedStack (size=0x28)
    // 0xad3f1c: mov             x3, x0
    // 0xad3f20: ldur            x0, [fp, #-0x20]
    // 0xad3f24: stur            x3, [fp, #-0x10]
    // 0xad3f28: StoreField: r3->field_b = r0
    //     0xad3f28: stur            x0, [x3, #0xb]
    // 0xad3f2c: ldur            x0, [fp, #-0x28]
    // 0xad3f30: StoreField: r3->field_13 = r0
    //     0xad3f30: stur            x0, [x3, #0x13]
    // 0xad3f34: ldur            x2, [fp, #-8]
    // 0xad3f38: r1 = Function '<anonymous closure>':.
    //     0xad3f38: add             x1, PP, #0x36, lsl #12  ; [pp+0x362f8] AnonymousClosure: (0xad3f84), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad3f3c: ldr             x1, [x1, #0x2f8]
    // 0xad3f40: r0 = AllocateClosure()
    //     0xad3f40: bl              #0xec1630  ; AllocateClosureStub
    // 0xad3f44: mov             x1, x0
    // 0xad3f48: ldur            x0, [fp, #-0x10]
    // 0xad3f4c: StoreField: r0->field_1b = r1
    //     0xad3f4c: stur            w1, [x0, #0x1b]
    // 0xad3f50: r1 = const []
    //     0xad3f50: add             x1, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xad3f54: ldr             x1, [x1, #0x8c0]
    // 0xad3f58: StoreField: r0->field_23 = r1
    //     0xad3f58: stur            w1, [x0, #0x23]
    // 0xad3f5c: ldur            x1, [fp, #-0x18]
    // 0xad3f60: StoreField: r0->field_7 = r1
    //     0xad3f60: stur            w1, [x0, #7]
    // 0xad3f64: LeaveFrame
    //     0xad3f64: mov             SP, fp
    //     0xad3f68: ldp             fp, lr, [SP], #0x10
    // 0xad3f6c: ret
    //     0xad3f6c: ret             
    // 0xad3f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad3f70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad3f74: b               #0xad3e80
  }
  [closure] GetBuilder<ArticleCategoryBuilderController> <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad3f84, size: 0x3b0
    // 0xad3f84: EnterFrame
    //     0xad3f84: stp             fp, lr, [SP, #-0x10]!
    //     0xad3f88: mov             fp, SP
    // 0xad3f8c: AllocStack(0x40)
    //     0xad3f8c: sub             SP, SP, #0x40
    // 0xad3f90: SetupParameters()
    //     0xad3f90: ldr             x0, [fp, #0x20]
    //     0xad3f94: ldur            w1, [x0, #0x17]
    //     0xad3f98: add             x1, x1, HEAP, lsl #32
    //     0xad3f9c: stur            x1, [fp, #-8]
    // 0xad3fa0: CheckStackOverflow
    //     0xad3fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad3fa4: cmp             SP, x16
    //     0xad3fa8: b.ls            #0xad4328
    // 0xad3fac: r1 = 1
    //     0xad3fac: movz            x1, #0x1
    // 0xad3fb0: r0 = AllocateContext()
    //     0xad3fb0: bl              #0xec126c  ; AllocateContextStub
    // 0xad3fb4: mov             x2, x0
    // 0xad3fb8: ldur            x0, [fp, #-8]
    // 0xad3fbc: stur            x2, [fp, #-0x20]
    // 0xad3fc0: StoreField: r2->field_b = r0
    //     0xad3fc0: stur            w0, [x2, #0xb]
    // 0xad3fc4: ldr             x1, [fp, #0x10]
    // 0xad3fc8: cbnz            w1, #0xad40ac
    // 0xad3fcc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad3fcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad3fd0: ldr             x0, [x0, #0x2670]
    //     0xad3fd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad3fd8: cmp             w0, w16
    //     0xad3fdc: b.ne            #0xad3fe8
    //     0xad3fe0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad3fe4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad3fe8: r16 = <ArticleRepository>
    //     0xad3fe8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0xad3fec: ldr             x16, [x16, #0x98]
    // 0xad3ff0: r30 = "article_repo"
    //     0xad3ff0: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0xad3ff4: ldr             lr, [lr, #0xa0]
    // 0xad3ff8: stp             lr, x16, [SP]
    // 0xad3ffc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xad3ffc: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xad4000: r0 = Inst.find()
    //     0xad4000: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xad4004: stur            x0, [fp, #-0x10]
    // 0xad4008: r0 = ArticleCategoryBuilderController()
    //     0xad4008: bl              #0xad44d4  ; AllocateArticleCategoryBuilderControllerStub -> ArticleCategoryBuilderController (size=0x54)
    // 0xad400c: mov             x1, x0
    // 0xad4010: ldur            x3, [fp, #-0x10]
    // 0xad4014: r2 = 63
    //     0xad4014: movz            x2, #0x3f
    // 0xad4018: stur            x0, [fp, #-0x10]
    // 0xad401c: r0 = ArticleCategoryBuilderController()
    //     0xad401c: bl              #0xad4334  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::ArticleCategoryBuilderController
    // 0xad4020: r1 = <ArticleCategoryBuilderController>
    //     0xad4020: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad4024: ldr             x1, [x1, #0x300]
    // 0xad4028: r0 = GetBuilder()
    //     0xad4028: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xad402c: mov             x3, x0
    // 0xad4030: ldur            x0, [fp, #-0x10]
    // 0xad4034: stur            x3, [fp, #-0x18]
    // 0xad4038: StoreField: r3->field_3b = r0
    //     0xad4038: stur            w0, [x3, #0x3b]
    // 0xad403c: r4 = true
    //     0xad403c: add             x4, NULL, #0x20  ; true
    // 0xad4040: StoreField: r3->field_13 = r4
    //     0xad4040: stur            w4, [x3, #0x13]
    // 0xad4044: r1 = Function '<anonymous closure>':.
    //     0xad4044: add             x1, PP, #0x36, lsl #12  ; [pp+0x36308] AnonymousClosure: (0xad4590), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad4048: ldr             x1, [x1, #0x308]
    // 0xad404c: r2 = Null
    //     0xad404c: mov             x2, NULL
    // 0xad4050: r0 = AllocateClosure()
    //     0xad4050: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4054: mov             x1, x0
    // 0xad4058: ldur            x0, [fp, #-0x18]
    // 0xad405c: StoreField: r0->field_f = r1
    //     0xad405c: stur            w1, [x0, #0xf]
    // 0xad4060: r3 = false
    //     0xad4060: add             x3, NULL, #0x30  ; false
    // 0xad4064: StoreField: r0->field_1f = r3
    //     0xad4064: stur            w3, [x0, #0x1f]
    // 0xad4068: StoreField: r0->field_23 = r3
    //     0xad4068: stur            w3, [x0, #0x23]
    // 0xad406c: r1 = Function '<anonymous closure>':.
    //     0xad406c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36310] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xad4070: ldr             x1, [x1, #0x310]
    // 0xad4074: r2 = Null
    //     0xad4074: mov             x2, NULL
    // 0xad4078: r0 = AllocateClosure()
    //     0xad4078: bl              #0xec1630  ; AllocateClosureStub
    // 0xad407c: mov             x1, x0
    // 0xad4080: ldur            x0, [fp, #-0x18]
    // 0xad4084: StoreField: r0->field_2b = r1
    //     0xad4084: stur            w1, [x0, #0x2b]
    // 0xad4088: r1 = "islamic"
    //     0xad4088: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cca0] "islamic"
    //     0xad408c: ldr             x1, [x1, #0xca0]
    // 0xad4090: StoreField: r0->field_1b = r1
    //     0xad4090: stur            w1, [x0, #0x1b]
    // 0xad4094: r1 = Instance_ValueKey
    //     0xad4094: add             x1, PP, #0x36, lsl #12  ; [pp+0x36318] Obj!ValueKey<String>@e14b91
    //     0xad4098: ldr             x1, [x1, #0x318]
    // 0xad409c: StoreField: r0->field_7 = r1
    //     0xad409c: stur            w1, [x0, #7]
    // 0xad40a0: LeaveFrame
    //     0xad40a0: mov             SP, fp
    //     0xad40a4: ldp             fp, lr, [SP], #0x10
    // 0xad40a8: ret
    //     0xad40a8: ret             
    // 0xad40ac: r4 = true
    //     0xad40ac: add             x4, NULL, #0x20  ; true
    // 0xad40b0: r3 = false
    //     0xad40b0: add             x3, NULL, #0x30  ; false
    // 0xad40b4: LoadField: r1 = r0->field_f
    //     0xad40b4: ldur            w1, [x0, #0xf]
    // 0xad40b8: DecompressPointer r1
    //     0xad40b8: add             x1, x1, HEAP, lsl #32
    // 0xad40bc: r0 = controller()
    //     0xad40bc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad40c0: mov             x1, x0
    // 0xad40c4: r0 = islamicChannels()
    //     0xad40c4: bl              #0x8ab6ac  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels
    // 0xad40c8: mov             x2, x0
    // 0xad40cc: ldur            x0, [fp, #-8]
    // 0xad40d0: stur            x2, [fp, #-0x10]
    // 0xad40d4: LoadField: r1 = r0->field_f
    //     0xad40d4: ldur            w1, [x0, #0xf]
    // 0xad40d8: DecompressPointer r1
    //     0xad40d8: add             x1, x1, HEAP, lsl #32
    // 0xad40dc: r0 = controller()
    //     0xad40dc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad40e0: LoadField: r1 = r0->field_33
    //     0xad40e0: ldur            w1, [x0, #0x33]
    // 0xad40e4: DecompressPointer r1
    //     0xad40e4: add             x1, x1, HEAP, lsl #32
    // 0xad40e8: r0 = value()
    //     0xad40e8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad40ec: r1 = LoadInt32Instr(r0)
    //     0xad40ec: sbfx            x1, x0, #1, #0x1f
    //     0xad40f0: tbz             w0, #0, #0xad40f8
    //     0xad40f4: ldur            x1, [x0, #7]
    // 0xad40f8: sub             x2, x1, #1
    // 0xad40fc: ldur            x3, [fp, #-0x10]
    // 0xad4100: LoadField: r0 = r3->field_b
    //     0xad4100: ldur            w0, [x3, #0xb]
    // 0xad4104: r1 = LoadInt32Instr(r0)
    //     0xad4104: sbfx            x1, x0, #1, #0x1f
    // 0xad4108: mov             x0, x1
    // 0xad410c: mov             x1, x2
    // 0xad4110: cmp             x1, x0
    // 0xad4114: b.hs            #0xad4330
    // 0xad4118: LoadField: r0 = r3->field_f
    //     0xad4118: ldur            w0, [x3, #0xf]
    // 0xad411c: DecompressPointer r0
    //     0xad411c: add             x0, x0, HEAP, lsl #32
    // 0xad4120: ArrayLoad: r3 = r0[r2]  ; Unknown_4
    //     0xad4120: add             x16, x0, x2, lsl #2
    //     0xad4124: ldur            w3, [x16, #0xf]
    // 0xad4128: DecompressPointer r3
    //     0xad4128: add             x3, x3, HEAP, lsl #32
    // 0xad412c: mov             x0, x3
    // 0xad4130: ldur            x2, [fp, #-0x20]
    // 0xad4134: stur            x3, [fp, #-0x10]
    // 0xad4138: StoreField: r2->field_f = r0
    //     0xad4138: stur            w0, [x2, #0xf]
    //     0xad413c: ldurb           w16, [x2, #-1]
    //     0xad4140: ldurb           w17, [x0, #-1]
    //     0xad4144: and             x16, x17, x16, lsr #2
    //     0xad4148: tst             x16, HEAP, lsr #32
    //     0xad414c: b.eq            #0xad4154
    //     0xad4150: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xad4154: LoadField: r4 = r3->field_13
    //     0xad4154: ldur            x4, [x3, #0x13]
    // 0xad4158: stur            x4, [fp, #-0x28]
    // 0xad415c: r0 = BoxInt64Instr(r4)
    //     0xad415c: sbfiz           x0, x4, #1, #0x1f
    //     0xad4160: cmp             x4, x0, asr #1
    //     0xad4164: b.eq            #0xad4170
    //     0xad4168: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad416c: stur            x4, [x0, #7]
    // 0xad4170: r1 = <int>
    //     0xad4170: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xad4174: stur            x0, [fp, #-8]
    // 0xad4178: r0 = ValueKey()
    //     0xad4178: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xad417c: mov             x3, x0
    // 0xad4180: ldur            x0, [fp, #-8]
    // 0xad4184: stur            x3, [fp, #-0x18]
    // 0xad4188: StoreField: r3->field_b = r0
    //     0xad4188: stur            w0, [x3, #0xb]
    // 0xad418c: r1 = Null
    //     0xad418c: mov             x1, NULL
    // 0xad4190: r2 = 6
    //     0xad4190: movz            x2, #0x6
    // 0xad4194: r0 = AllocateArray()
    //     0xad4194: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad4198: r16 = "Artikel "
    //     0xad4198: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xad419c: ldr             x16, [x16, #0xb90]
    // 0xad41a0: StoreField: r0->field_f = r16
    //     0xad41a0: stur            w16, [x0, #0xf]
    // 0xad41a4: ldur            x1, [fp, #-0x10]
    // 0xad41a8: LoadField: r2 = r1->field_1b
    //     0xad41a8: ldur            w2, [x1, #0x1b]
    // 0xad41ac: DecompressPointer r2
    //     0xad41ac: add             x2, x2, HEAP, lsl #32
    // 0xad41b0: StoreField: r0->field_13 = r2
    //     0xad41b0: stur            w2, [x0, #0x13]
    // 0xad41b4: r16 = " Terpopuler"
    //     0xad41b4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36320] " Terpopuler"
    //     0xad41b8: ldr             x16, [x16, #0x320]
    // 0xad41bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xad41bc: stur            w16, [x0, #0x17]
    // 0xad41c0: str             x0, [SP]
    // 0xad41c4: r0 = _interpolate()
    //     0xad41c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad41c8: r1 = Null
    //     0xad41c8: mov             x1, NULL
    // 0xad41cc: r2 = 6
    //     0xad41cc: movz            x2, #0x6
    // 0xad41d0: r0 = AllocateArray()
    //     0xad41d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad41d4: r16 = "Artikel "
    //     0xad41d4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xad41d8: ldr             x16, [x16, #0xb90]
    // 0xad41dc: StoreField: r0->field_f = r16
    //     0xad41dc: stur            w16, [x0, #0xf]
    // 0xad41e0: ldur            x1, [fp, #-0x10]
    // 0xad41e4: LoadField: r2 = r1->field_1b
    //     0xad41e4: ldur            w2, [x1, #0x1b]
    // 0xad41e8: DecompressPointer r2
    //     0xad41e8: add             x2, x2, HEAP, lsl #32
    // 0xad41ec: StoreField: r0->field_13 = r2
    //     0xad41ec: stur            w2, [x0, #0x13]
    // 0xad41f0: r16 = " Terbaru"
    //     0xad41f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36328] " Terbaru"
    //     0xad41f4: ldr             x16, [x16, #0x328]
    // 0xad41f8: ArrayStore: r0[0] = r16  ; List_4
    //     0xad41f8: stur            w16, [x0, #0x17]
    // 0xad41fc: str             x0, [SP]
    // 0xad4200: r0 = _interpolate()
    //     0xad4200: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad4204: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4204: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad4208: ldr             x0, [x0, #0x2670]
    //     0xad420c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad4210: cmp             w0, w16
    //     0xad4214: b.ne            #0xad4220
    //     0xad4218: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad421c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad4220: r16 = <ArticleRepository>
    //     0xad4220: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0xad4224: ldr             x16, [x16, #0x98]
    // 0xad4228: r30 = "article_repo"
    //     0xad4228: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0xad422c: ldr             lr, [lr, #0xa0]
    // 0xad4230: stp             lr, x16, [SP]
    // 0xad4234: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xad4234: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xad4238: r0 = Inst.find()
    //     0xad4238: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xad423c: stur            x0, [fp, #-8]
    // 0xad4240: r0 = ArticleCategoryBuilderController()
    //     0xad4240: bl              #0xad44d4  ; AllocateArticleCategoryBuilderControllerStub -> ArticleCategoryBuilderController (size=0x54)
    // 0xad4244: mov             x1, x0
    // 0xad4248: ldur            x2, [fp, #-0x28]
    // 0xad424c: ldur            x3, [fp, #-8]
    // 0xad4250: stur            x0, [fp, #-8]
    // 0xad4254: r0 = ArticleCategoryBuilderController()
    //     0xad4254: bl              #0xad4334  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::ArticleCategoryBuilderController
    // 0xad4258: ldur            x0, [fp, #-0x10]
    // 0xad425c: LoadField: r2 = r0->field_13
    //     0xad425c: ldur            x2, [x0, #0x13]
    // 0xad4260: r0 = BoxInt64Instr(r2)
    //     0xad4260: sbfiz           x0, x2, #1, #0x1f
    //     0xad4264: cmp             x2, x0, asr #1
    //     0xad4268: b.eq            #0xad4274
    //     0xad426c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad4270: stur            x2, [x0, #7]
    // 0xad4274: r1 = 60
    //     0xad4274: movz            x1, #0x3c
    // 0xad4278: branchIfSmi(r0, 0xad4284)
    //     0xad4278: tbz             w0, #0, #0xad4284
    // 0xad427c: r1 = LoadClassIdInstr(r0)
    //     0xad427c: ldur            x1, [x0, #-1]
    //     0xad4280: ubfx            x1, x1, #0xc, #0x14
    // 0xad4284: str             x0, [SP]
    // 0xad4288: mov             x0, x1
    // 0xad428c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xad428c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xad4290: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xad4290: movz            x17, #0x2b03
    //     0xad4294: add             lr, x0, x17
    //     0xad4298: ldr             lr, [x21, lr, lsl #3]
    //     0xad429c: blr             lr
    // 0xad42a0: r1 = <ArticleCategoryBuilderController>
    //     0xad42a0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad42a4: ldr             x1, [x1, #0x300]
    // 0xad42a8: stur            x0, [fp, #-0x10]
    // 0xad42ac: r0 = GetBuilder()
    //     0xad42ac: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xad42b0: mov             x3, x0
    // 0xad42b4: ldur            x0, [fp, #-8]
    // 0xad42b8: stur            x3, [fp, #-0x30]
    // 0xad42bc: StoreField: r3->field_3b = r0
    //     0xad42bc: stur            w0, [x3, #0x3b]
    // 0xad42c0: r0 = true
    //     0xad42c0: add             x0, NULL, #0x20  ; true
    // 0xad42c4: StoreField: r3->field_13 = r0
    //     0xad42c4: stur            w0, [x3, #0x13]
    // 0xad42c8: ldur            x2, [fp, #-0x20]
    // 0xad42cc: r1 = Function '<anonymous closure>':.
    //     0xad42cc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36330] AnonymousClosure: (0xad44e0), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad42d0: ldr             x1, [x1, #0x330]
    // 0xad42d4: r0 = AllocateClosure()
    //     0xad42d4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad42d8: mov             x1, x0
    // 0xad42dc: ldur            x0, [fp, #-0x30]
    // 0xad42e0: StoreField: r0->field_f = r1
    //     0xad42e0: stur            w1, [x0, #0xf]
    // 0xad42e4: r1 = false
    //     0xad42e4: add             x1, NULL, #0x30  ; false
    // 0xad42e8: StoreField: r0->field_1f = r1
    //     0xad42e8: stur            w1, [x0, #0x1f]
    // 0xad42ec: StoreField: r0->field_23 = r1
    //     0xad42ec: stur            w1, [x0, #0x23]
    // 0xad42f0: r1 = Function '<anonymous closure>':.
    //     0xad42f0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36338] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xad42f4: ldr             x1, [x1, #0x338]
    // 0xad42f8: r2 = Null
    //     0xad42f8: mov             x2, NULL
    // 0xad42fc: r0 = AllocateClosure()
    //     0xad42fc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4300: mov             x1, x0
    // 0xad4304: ldur            x0, [fp, #-0x30]
    // 0xad4308: StoreField: r0->field_2b = r1
    //     0xad4308: stur            w1, [x0, #0x2b]
    // 0xad430c: ldur            x1, [fp, #-0x10]
    // 0xad4310: StoreField: r0->field_1b = r1
    //     0xad4310: stur            w1, [x0, #0x1b]
    // 0xad4314: ldur            x1, [fp, #-0x18]
    // 0xad4318: StoreField: r0->field_7 = r1
    //     0xad4318: stur            w1, [x0, #7]
    // 0xad431c: LeaveFrame
    //     0xad431c: mov             SP, fp
    //     0xad4320: ldp             fp, lr, [SP], #0x10
    // 0xad4324: ret
    //     0xad4324: ret             
    // 0xad4328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad432c: b               #0xad3fac
    // 0xad4330: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad4330: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] ArticleCategoryBuilderWidget <anonymous closure>(dynamic, ArticleCategoryBuilderController) {
    // ** addr: 0xad44e0, size: 0xa4
    // 0xad44e0: EnterFrame
    //     0xad44e0: stp             fp, lr, [SP, #-0x10]!
    //     0xad44e4: mov             fp, SP
    // 0xad44e8: AllocStack(0x10)
    //     0xad44e8: sub             SP, SP, #0x10
    // 0xad44ec: SetupParameters()
    //     0xad44ec: ldr             x0, [fp, #0x18]
    //     0xad44f0: ldur            w1, [x0, #0x17]
    //     0xad44f4: add             x1, x1, HEAP, lsl #32
    // 0xad44f8: CheckStackOverflow
    //     0xad44f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad44fc: cmp             SP, x16
    //     0xad4500: b.ls            #0xad457c
    // 0xad4504: LoadField: r0 = r1->field_f
    //     0xad4504: ldur            w0, [x1, #0xf]
    // 0xad4508: DecompressPointer r0
    //     0xad4508: add             x0, x0, HEAP, lsl #32
    // 0xad450c: LoadField: r2 = r0->field_13
    //     0xad450c: ldur            x2, [x0, #0x13]
    // 0xad4510: r0 = BoxInt64Instr(r2)
    //     0xad4510: sbfiz           x0, x2, #1, #0x1f
    //     0xad4514: cmp             x2, x0, asr #1
    //     0xad4518: b.eq            #0xad4524
    //     0xad451c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad4520: stur            x2, [x0, #7]
    // 0xad4524: r1 = 60
    //     0xad4524: movz            x1, #0x3c
    // 0xad4528: branchIfSmi(r0, 0xad4534)
    //     0xad4528: tbz             w0, #0, #0xad4534
    // 0xad452c: r1 = LoadClassIdInstr(r0)
    //     0xad452c: ldur            x1, [x0, #-1]
    //     0xad4530: ubfx            x1, x1, #0xc, #0x14
    // 0xad4534: str             x0, [SP]
    // 0xad4538: mov             x0, x1
    // 0xad453c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xad453c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xad4540: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xad4540: movz            x17, #0x2b03
    //     0xad4544: add             lr, x0, x17
    //     0xad4548: ldr             lr, [x21, lr, lsl #3]
    //     0xad454c: blr             lr
    // 0xad4550: r1 = <ArticleCategoryBuilderController>
    //     0xad4550: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad4554: ldr             x1, [x1, #0x300]
    // 0xad4558: stur            x0, [fp, #-8]
    // 0xad455c: r0 = ArticleCategoryBuilderWidget()
    //     0xad455c: bl              #0xad4584  ; AllocateArticleCategoryBuilderWidgetStub -> ArticleCategoryBuilderWidget (size=0x1c)
    // 0xad4560: ldur            x1, [fp, #-8]
    // 0xad4564: StoreField: r0->field_13 = r1
    //     0xad4564: stur            w1, [x0, #0x13]
    // 0xad4568: r1 = false
    //     0xad4568: add             x1, NULL, #0x30  ; false
    // 0xad456c: ArrayStore: r0[0] = r1  ; List_4
    //     0xad456c: stur            w1, [x0, #0x17]
    // 0xad4570: LeaveFrame
    //     0xad4570: mov             SP, fp
    //     0xad4574: ldp             fp, lr, [SP], #0x10
    // 0xad4578: ret
    //     0xad4578: ret             
    // 0xad457c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad457c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4580: b               #0xad4504
  }
  [closure] ArticleCategoryBuilderWidget <anonymous closure>(dynamic, ArticleCategoryBuilderController) {
    // ** addr: 0xad4590, size: 0xc
    // 0xad4590: r0 = Instance_ArticleCategoryBuilderWidget
    //     0xad4590: add             x0, PP, #0x36, lsl #12  ; [pp+0x36340] Obj!ArticleCategoryBuilderWidget@e1de61
    //     0xad4594: ldr             x0, [x0, #0x340]
    // 0xad4598: ret
    //     0xad4598: ret             
  }
  [closure] ArticleChannelWidget <anonymous closure>(dynamic) {
    // ** addr: 0xad459c, size: 0x160
    // 0xad459c: EnterFrame
    //     0xad459c: stp             fp, lr, [SP, #-0x10]!
    //     0xad45a0: mov             fp, SP
    // 0xad45a4: AllocStack(0x30)
    //     0xad45a4: sub             SP, SP, #0x30
    // 0xad45a8: SetupParameters()
    //     0xad45a8: ldr             x0, [fp, #0x10]
    //     0xad45ac: ldur            w2, [x0, #0x17]
    //     0xad45b0: add             x2, x2, HEAP, lsl #32
    //     0xad45b4: stur            x2, [fp, #-8]
    // 0xad45b8: CheckStackOverflow
    //     0xad45b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad45bc: cmp             SP, x16
    //     0xad45c0: b.ls            #0xad46f4
    // 0xad45c4: LoadField: r1 = r2->field_f
    //     0xad45c4: ldur            w1, [x2, #0xf]
    // 0xad45c8: DecompressPointer r1
    //     0xad45c8: add             x1, x1, HEAP, lsl #32
    // 0xad45cc: r0 = controller()
    //     0xad45cc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad45d0: LoadField: r1 = r0->field_33
    //     0xad45d0: ldur            w1, [x0, #0x33]
    // 0xad45d4: DecompressPointer r1
    //     0xad45d4: add             x1, x1, HEAP, lsl #32
    // 0xad45d8: r0 = value()
    //     0xad45d8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad45dc: mov             x2, x0
    // 0xad45e0: ldur            x0, [fp, #-8]
    // 0xad45e4: stur            x2, [fp, #-0x10]
    // 0xad45e8: LoadField: r1 = r0->field_f
    //     0xad45e8: ldur            w1, [x0, #0xf]
    // 0xad45ec: DecompressPointer r1
    //     0xad45ec: add             x1, x1, HEAP, lsl #32
    // 0xad45f0: r0 = controller()
    //     0xad45f0: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad45f4: LoadField: r2 = r0->field_33
    //     0xad45f4: ldur            w2, [x0, #0x33]
    // 0xad45f8: DecompressPointer r2
    //     0xad45f8: add             x2, x2, HEAP, lsl #32
    // 0xad45fc: LoadField: r3 = r2->field_7
    //     0xad45fc: ldur            w3, [x2, #7]
    // 0xad4600: DecompressPointer r3
    //     0xad4600: add             x3, x3, HEAP, lsl #32
    // 0xad4604: r1 = Function 'call':.
    //     0xad4604: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xad4608: ldr             x1, [x1, #0x310]
    // 0xad460c: r0 = AllocateClosureTA()
    //     0xad460c: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xad4610: mov             x3, x0
    // 0xad4614: r2 = Null
    //     0xad4614: mov             x2, NULL
    // 0xad4618: r1 = Null
    //     0xad4618: mov             x1, NULL
    // 0xad461c: stur            x3, [fp, #-0x18]
    // 0xad4620: r8 = (dynamic this, int?) => int
    //     0xad4620: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a0a0] FunctionType: (dynamic this, int?) => int
    //     0xad4624: ldr             x8, [x8, #0xa0]
    // 0xad4628: r3 = Null
    //     0xad4628: add             x3, PP, #0x36, lsl #12  ; [pp+0x36378] Null
    //     0xad462c: ldr             x3, [x3, #0x378]
    // 0xad4630: r0 = DefaultTypeTest()
    //     0xad4630: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xad4634: ldur            x0, [fp, #-8]
    // 0xad4638: LoadField: r1 = r0->field_f
    //     0xad4638: ldur            w1, [x0, #0xf]
    // 0xad463c: DecompressPointer r1
    //     0xad463c: add             x1, x1, HEAP, lsl #32
    // 0xad4640: r0 = controller()
    //     0xad4640: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4644: mov             x1, x0
    // 0xad4648: r0 = islamicChannels()
    //     0xad4648: bl              #0x8ab6ac  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::islamicChannels
    // 0xad464c: mov             x2, x0
    // 0xad4650: ldur            x0, [fp, #-8]
    // 0xad4654: stur            x2, [fp, #-0x20]
    // 0xad4658: LoadField: r1 = r0->field_f
    //     0xad4658: ldur            w1, [x0, #0xf]
    // 0xad465c: DecompressPointer r1
    //     0xad465c: add             x1, x1, HEAP, lsl #32
    // 0xad4660: r0 = controller()
    //     0xad4660: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4664: mov             x2, x0
    // 0xad4668: ldur            x0, [fp, #-8]
    // 0xad466c: stur            x2, [fp, #-0x28]
    // 0xad4670: LoadField: r1 = r0->field_f
    //     0xad4670: ldur            w1, [x0, #0xf]
    // 0xad4674: DecompressPointer r1
    //     0xad4674: add             x1, x1, HEAP, lsl #32
    // 0xad4678: r0 = controller()
    //     0xad4678: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad467c: stur            x0, [fp, #-8]
    // 0xad4680: r0 = ArticleChannelWidget()
    //     0xad4680: bl              #0xad46fc  ; AllocateArticleChannelWidgetStub -> ArticleChannelWidget (size=0x24)
    // 0xad4684: mov             x3, x0
    // 0xad4688: ldur            x0, [fp, #-0x20]
    // 0xad468c: stur            x3, [fp, #-0x30]
    // 0xad4690: StoreField: r3->field_b = r0
    //     0xad4690: stur            w0, [x3, #0xb]
    // 0xad4694: ldur            x0, [fp, #-0x10]
    // 0xad4698: r1 = LoadInt32Instr(r0)
    //     0xad4698: sbfx            x1, x0, #1, #0x1f
    //     0xad469c: tbz             w0, #0, #0xad46a4
    //     0xad46a0: ldur            x1, [x0, #7]
    // 0xad46a4: StoreField: r3->field_f = r1
    //     0xad46a4: stur            x1, [x3, #0xf]
    // 0xad46a8: ldur            x0, [fp, #-0x18]
    // 0xad46ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xad46ac: stur            w0, [x3, #0x17]
    // 0xad46b0: ldur            x2, [fp, #-0x28]
    // 0xad46b4: r1 = Function 'onMoreCategoryPressed':.
    //     0xad46b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36388] AnonymousClosure: (0xad4850), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onMoreCategoryPressed (0xad4888)
    //     0xad46b8: ldr             x1, [x1, #0x388]
    // 0xad46bc: r0 = AllocateClosure()
    //     0xad46bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xad46c0: mov             x1, x0
    // 0xad46c4: ldur            x0, [fp, #-0x30]
    // 0xad46c8: StoreField: r0->field_1b = r1
    //     0xad46c8: stur            w1, [x0, #0x1b]
    // 0xad46cc: ldur            x2, [fp, #-8]
    // 0xad46d0: r1 = Function 'onSettingCategoryPressed':.
    //     0xad46d0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36390] AnonymousClosure: (0xad4708), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onSettingCategoryPressed (0xad4740)
    //     0xad46d4: ldr             x1, [x1, #0x390]
    // 0xad46d8: r0 = AllocateClosure()
    //     0xad46d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad46dc: mov             x1, x0
    // 0xad46e0: ldur            x0, [fp, #-0x30]
    // 0xad46e4: StoreField: r0->field_1f = r1
    //     0xad46e4: stur            w1, [x0, #0x1f]
    // 0xad46e8: LeaveFrame
    //     0xad46e8: mov             SP, fp
    //     0xad46ec: ldp             fp, lr, [SP], #0x10
    // 0xad46f0: ret
    //     0xad46f0: ret             
    // 0xad46f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad46f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad46f8: b               #0xad45c4
  }
  _ _buildMainContent(/* No info */) {
    // ** addr: 0xad4958, size: 0x144
    // 0xad4958: EnterFrame
    //     0xad4958: stp             fp, lr, [SP, #-0x10]!
    //     0xad495c: mov             fp, SP
    // 0xad4960: AllocStack(0x18)
    //     0xad4960: sub             SP, SP, #0x18
    // 0xad4964: SetupParameters(MainArticleView this /* r1 => r1, fp-0x8 */)
    //     0xad4964: stur            x1, [fp, #-8]
    // 0xad4968: r1 = 1
    //     0xad4968: movz            x1, #0x1
    // 0xad496c: r0 = AllocateContext()
    //     0xad496c: bl              #0xec126c  ; AllocateContextStub
    // 0xad4970: mov             x1, x0
    // 0xad4974: ldur            x0, [fp, #-8]
    // 0xad4978: stur            x1, [fp, #-0x10]
    // 0xad497c: StoreField: r1->field_f = r0
    //     0xad497c: stur            w0, [x1, #0xf]
    // 0xad4980: r0 = Obx()
    //     0xad4980: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad4984: ldur            x2, [fp, #-0x10]
    // 0xad4988: r1 = Function '<anonymous closure>':.
    //     0xad4988: add             x1, PP, #0x36, lsl #12  ; [pp+0x36450] AnonymousClosure: (0xad5058), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildMainContent (0xad4958)
    //     0xad498c: ldr             x1, [x1, #0x450]
    // 0xad4990: stur            x0, [fp, #-8]
    // 0xad4994: r0 = AllocateClosure()
    //     0xad4994: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4998: mov             x1, x0
    // 0xad499c: ldur            x0, [fp, #-8]
    // 0xad49a0: StoreField: r0->field_b = r1
    //     0xad49a0: stur            w1, [x0, #0xb]
    // 0xad49a4: r0 = Obx()
    //     0xad49a4: bl              #0xad05a4  ; AllocateObxStub -> Obx (size=0x10)
    // 0xad49a8: ldur            x2, [fp, #-0x10]
    // 0xad49ac: r1 = Function '<anonymous closure>':.
    //     0xad49ac: add             x1, PP, #0x36, lsl #12  ; [pp+0x36458] AnonymousClosure: (0xad4a9c), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildMainContent (0xad4958)
    //     0xad49b0: ldr             x1, [x1, #0x458]
    // 0xad49b4: stur            x0, [fp, #-0x10]
    // 0xad49b8: r0 = AllocateClosure()
    //     0xad49b8: bl              #0xec1630  ; AllocateClosureStub
    // 0xad49bc: mov             x1, x0
    // 0xad49c0: ldur            x0, [fp, #-0x10]
    // 0xad49c4: StoreField: r0->field_b = r1
    //     0xad49c4: stur            w1, [x0, #0xb]
    // 0xad49c8: r1 = <FlexParentData>
    //     0xad49c8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xad49cc: ldr             x1, [x1, #0x720]
    // 0xad49d0: r0 = Expanded()
    //     0xad49d0: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad49d4: mov             x3, x0
    // 0xad49d8: r0 = 1
    //     0xad49d8: movz            x0, #0x1
    // 0xad49dc: stur            x3, [fp, #-0x18]
    // 0xad49e0: StoreField: r3->field_13 = r0
    //     0xad49e0: stur            x0, [x3, #0x13]
    // 0xad49e4: r0 = Instance_FlexFit
    //     0xad49e4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xad49e8: ldr             x0, [x0, #0x728]
    // 0xad49ec: StoreField: r3->field_1b = r0
    //     0xad49ec: stur            w0, [x3, #0x1b]
    // 0xad49f0: ldur            x0, [fp, #-0x10]
    // 0xad49f4: StoreField: r3->field_b = r0
    //     0xad49f4: stur            w0, [x3, #0xb]
    // 0xad49f8: r1 = Null
    //     0xad49f8: mov             x1, NULL
    // 0xad49fc: r2 = 4
    //     0xad49fc: movz            x2, #0x4
    // 0xad4a00: r0 = AllocateArray()
    //     0xad4a00: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad4a04: mov             x2, x0
    // 0xad4a08: ldur            x0, [fp, #-8]
    // 0xad4a0c: stur            x2, [fp, #-0x10]
    // 0xad4a10: StoreField: r2->field_f = r0
    //     0xad4a10: stur            w0, [x2, #0xf]
    // 0xad4a14: ldur            x0, [fp, #-0x18]
    // 0xad4a18: StoreField: r2->field_13 = r0
    //     0xad4a18: stur            w0, [x2, #0x13]
    // 0xad4a1c: r1 = <Widget>
    //     0xad4a1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xad4a20: r0 = AllocateGrowableArray()
    //     0xad4a20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xad4a24: mov             x1, x0
    // 0xad4a28: ldur            x0, [fp, #-0x10]
    // 0xad4a2c: stur            x1, [fp, #-8]
    // 0xad4a30: StoreField: r1->field_f = r0
    //     0xad4a30: stur            w0, [x1, #0xf]
    // 0xad4a34: r0 = 4
    //     0xad4a34: movz            x0, #0x4
    // 0xad4a38: StoreField: r1->field_b = r0
    //     0xad4a38: stur            w0, [x1, #0xb]
    // 0xad4a3c: r0 = Column()
    //     0xad4a3c: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xad4a40: r1 = Instance_Axis
    //     0xad4a40: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xad4a44: StoreField: r0->field_f = r1
    //     0xad4a44: stur            w1, [x0, #0xf]
    // 0xad4a48: r1 = Instance_MainAxisAlignment
    //     0xad4a48: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xad4a4c: ldr             x1, [x1, #0x730]
    // 0xad4a50: StoreField: r0->field_13 = r1
    //     0xad4a50: stur            w1, [x0, #0x13]
    // 0xad4a54: r1 = Instance_MainAxisSize
    //     0xad4a54: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xad4a58: ldr             x1, [x1, #0x738]
    // 0xad4a5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xad4a5c: stur            w1, [x0, #0x17]
    // 0xad4a60: r1 = Instance_CrossAxisAlignment
    //     0xad4a60: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xad4a64: ldr             x1, [x1, #0x740]
    // 0xad4a68: StoreField: r0->field_1b = r1
    //     0xad4a68: stur            w1, [x0, #0x1b]
    // 0xad4a6c: r1 = Instance_VerticalDirection
    //     0xad4a6c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xad4a70: ldr             x1, [x1, #0x748]
    // 0xad4a74: StoreField: r0->field_23 = r1
    //     0xad4a74: stur            w1, [x0, #0x23]
    // 0xad4a78: r1 = Instance_Clip
    //     0xad4a78: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xad4a7c: ldr             x1, [x1, #0x750]
    // 0xad4a80: StoreField: r0->field_2b = r1
    //     0xad4a80: stur            w1, [x0, #0x2b]
    // 0xad4a84: StoreField: r0->field_2f = rZR
    //     0xad4a84: stur            xzr, [x0, #0x2f]
    // 0xad4a88: ldur            x1, [fp, #-8]
    // 0xad4a8c: StoreField: r0->field_b = r1
    //     0xad4a8c: stur            w1, [x0, #0xb]
    // 0xad4a90: LeaveFrame
    //     0xad4a90: mov             SP, fp
    //     0xad4a94: ldp             fp, lr, [SP], #0x10
    // 0xad4a98: ret
    //     0xad4a98: ret             
  }
  [closure] NIndexedStack <anonymous closure>(dynamic) {
    // ** addr: 0xad4a9c, size: 0x120
    // 0xad4a9c: EnterFrame
    //     0xad4a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xad4aa0: mov             fp, SP
    // 0xad4aa4: AllocStack(0x28)
    //     0xad4aa4: sub             SP, SP, #0x28
    // 0xad4aa8: SetupParameters()
    //     0xad4aa8: ldr             x0, [fp, #0x10]
    //     0xad4aac: ldur            w2, [x0, #0x17]
    //     0xad4ab0: add             x2, x2, HEAP, lsl #32
    //     0xad4ab4: stur            x2, [fp, #-8]
    // 0xad4ab8: CheckStackOverflow
    //     0xad4ab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4abc: cmp             SP, x16
    //     0xad4ac0: b.ls            #0xad4bb4
    // 0xad4ac4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4ac4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad4ac8: ldr             x0, [x0, #0x2670]
    //     0xad4acc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad4ad0: cmp             w0, w16
    //     0xad4ad4: b.ne            #0xad4ae0
    //     0xad4ad8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad4adc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad4ae0: r0 = GetNavigation.isDarkMode()
    //     0xad4ae0: bl              #0x624d84  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.isDarkMode
    // 0xad4ae4: r1 = <bool>
    //     0xad4ae4: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xad4ae8: stur            x0, [fp, #-0x10]
    // 0xad4aec: r0 = ValueKey()
    //     0xad4aec: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xad4af0: mov             x2, x0
    // 0xad4af4: ldur            x0, [fp, #-0x10]
    // 0xad4af8: stur            x2, [fp, #-0x18]
    // 0xad4afc: StoreField: r2->field_b = r0
    //     0xad4afc: stur            w0, [x2, #0xb]
    // 0xad4b00: ldur            x0, [fp, #-8]
    // 0xad4b04: LoadField: r1 = r0->field_f
    //     0xad4b04: ldur            w1, [x0, #0xf]
    // 0xad4b08: DecompressPointer r1
    //     0xad4b08: add             x1, x1, HEAP, lsl #32
    // 0xad4b0c: r0 = controller()
    //     0xad4b0c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4b10: LoadField: r1 = r0->field_2f
    //     0xad4b10: ldur            w1, [x0, #0x2f]
    // 0xad4b14: DecompressPointer r1
    //     0xad4b14: add             x1, x1, HEAP, lsl #32
    // 0xad4b18: r0 = value()
    //     0xad4b18: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad4b1c: ldur            x2, [fp, #-8]
    // 0xad4b20: stur            x0, [fp, #-0x10]
    // 0xad4b24: LoadField: r1 = r2->field_f
    //     0xad4b24: ldur            w1, [x2, #0xf]
    // 0xad4b28: DecompressPointer r1
    //     0xad4b28: add             x1, x1, HEAP, lsl #32
    // 0xad4b2c: r0 = controller()
    //     0xad4b2c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4b30: mov             x1, x0
    // 0xad4b34: r0 = mainChannels()
    //     0xad4b34: bl              #0xad4bbc  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::mainChannels
    // 0xad4b38: LoadField: r1 = r0->field_b
    //     0xad4b38: ldur            w1, [x0, #0xb]
    // 0xad4b3c: r0 = LoadInt32Instr(r1)
    //     0xad4b3c: sbfx            x0, x1, #1, #0x1f
    // 0xad4b40: add             x1, x0, #1
    // 0xad4b44: ldur            x0, [fp, #-0x10]
    // 0xad4b48: stur            x1, [fp, #-0x28]
    // 0xad4b4c: r2 = LoadInt32Instr(r0)
    //     0xad4b4c: sbfx            x2, x0, #1, #0x1f
    //     0xad4b50: tbz             w0, #0, #0xad4b58
    //     0xad4b54: ldur            x2, [x0, #7]
    // 0xad4b58: stur            x2, [fp, #-0x20]
    // 0xad4b5c: r0 = NIndexedStack()
    //     0xad4b5c: bl              #0xad3f78  ; AllocateNIndexedStackStub -> NIndexedStack (size=0x28)
    // 0xad4b60: mov             x3, x0
    // 0xad4b64: ldur            x0, [fp, #-0x20]
    // 0xad4b68: stur            x3, [fp, #-0x10]
    // 0xad4b6c: StoreField: r3->field_b = r0
    //     0xad4b6c: stur            x0, [x3, #0xb]
    // 0xad4b70: ldur            x0, [fp, #-0x28]
    // 0xad4b74: StoreField: r3->field_13 = r0
    //     0xad4b74: stur            x0, [x3, #0x13]
    // 0xad4b78: ldur            x2, [fp, #-8]
    // 0xad4b7c: r1 = Function '<anonymous closure>':.
    //     0xad4b7c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36460] AnonymousClosure: (0xad4c74), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildMainContent (0xad4958)
    //     0xad4b80: ldr             x1, [x1, #0x460]
    // 0xad4b84: r0 = AllocateClosure()
    //     0xad4b84: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4b88: mov             x1, x0
    // 0xad4b8c: ldur            x0, [fp, #-0x10]
    // 0xad4b90: StoreField: r0->field_1b = r1
    //     0xad4b90: stur            w1, [x0, #0x1b]
    // 0xad4b94: r1 = const []
    //     0xad4b94: add             x1, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xad4b98: ldr             x1, [x1, #0x8c0]
    // 0xad4b9c: StoreField: r0->field_23 = r1
    //     0xad4b9c: stur            w1, [x0, #0x23]
    // 0xad4ba0: ldur            x1, [fp, #-0x18]
    // 0xad4ba4: StoreField: r0->field_7 = r1
    //     0xad4ba4: stur            w1, [x0, #7]
    // 0xad4ba8: LeaveFrame
    //     0xad4ba8: mov             SP, fp
    //     0xad4bac: ldp             fp, lr, [SP], #0x10
    // 0xad4bb0: ret
    //     0xad4bb0: ret             
    // 0xad4bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad4bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad4bb8: b               #0xad4ac4
  }
  [closure] GetBuilder<ArticleCategoryBuilderController> <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xad4c74, size: 0x3b0
    // 0xad4c74: EnterFrame
    //     0xad4c74: stp             fp, lr, [SP, #-0x10]!
    //     0xad4c78: mov             fp, SP
    // 0xad4c7c: AllocStack(0x40)
    //     0xad4c7c: sub             SP, SP, #0x40
    // 0xad4c80: SetupParameters()
    //     0xad4c80: ldr             x0, [fp, #0x20]
    //     0xad4c84: ldur            w1, [x0, #0x17]
    //     0xad4c88: add             x1, x1, HEAP, lsl #32
    //     0xad4c8c: stur            x1, [fp, #-8]
    // 0xad4c90: CheckStackOverflow
    //     0xad4c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad4c94: cmp             SP, x16
    //     0xad4c98: b.ls            #0xad5018
    // 0xad4c9c: r1 = 1
    //     0xad4c9c: movz            x1, #0x1
    // 0xad4ca0: r0 = AllocateContext()
    //     0xad4ca0: bl              #0xec126c  ; AllocateContextStub
    // 0xad4ca4: mov             x2, x0
    // 0xad4ca8: ldur            x0, [fp, #-8]
    // 0xad4cac: stur            x2, [fp, #-0x20]
    // 0xad4cb0: StoreField: r2->field_b = r0
    //     0xad4cb0: stur            w0, [x2, #0xb]
    // 0xad4cb4: ldr             x1, [fp, #0x10]
    // 0xad4cb8: cbnz            w1, #0xad4d9c
    // 0xad4cbc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4cbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad4cc0: ldr             x0, [x0, #0x2670]
    //     0xad4cc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad4cc8: cmp             w0, w16
    //     0xad4ccc: b.ne            #0xad4cd8
    //     0xad4cd0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad4cd4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad4cd8: r16 = <ArticleRepository>
    //     0xad4cd8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0xad4cdc: ldr             x16, [x16, #0x98]
    // 0xad4ce0: r30 = "article_repo"
    //     0xad4ce0: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0xad4ce4: ldr             lr, [lr, #0xa0]
    // 0xad4ce8: stp             lr, x16, [SP]
    // 0xad4cec: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xad4cec: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xad4cf0: r0 = Inst.find()
    //     0xad4cf0: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xad4cf4: stur            x0, [fp, #-0x10]
    // 0xad4cf8: r0 = ArticleCategoryBuilderController()
    //     0xad4cf8: bl              #0xad44d4  ; AllocateArticleCategoryBuilderControllerStub -> ArticleCategoryBuilderController (size=0x54)
    // 0xad4cfc: mov             x1, x0
    // 0xad4d00: ldur            x3, [fp, #-0x10]
    // 0xad4d04: r2 = 0
    //     0xad4d04: movz            x2, #0
    // 0xad4d08: stur            x0, [fp, #-0x10]
    // 0xad4d0c: r0 = ArticleCategoryBuilderController()
    //     0xad4d0c: bl              #0xad4334  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::ArticleCategoryBuilderController
    // 0xad4d10: r1 = <ArticleCategoryBuilderController>
    //     0xad4d10: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad4d14: ldr             x1, [x1, #0x300]
    // 0xad4d18: r0 = GetBuilder()
    //     0xad4d18: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xad4d1c: mov             x3, x0
    // 0xad4d20: ldur            x0, [fp, #-0x10]
    // 0xad4d24: stur            x3, [fp, #-0x18]
    // 0xad4d28: StoreField: r3->field_3b = r0
    //     0xad4d28: stur            w0, [x3, #0x3b]
    // 0xad4d2c: r4 = true
    //     0xad4d2c: add             x4, NULL, #0x20  ; true
    // 0xad4d30: StoreField: r3->field_13 = r4
    //     0xad4d30: stur            w4, [x3, #0x13]
    // 0xad4d34: r1 = Function '<anonymous closure>':.
    //     0xad4d34: add             x1, PP, #0x36, lsl #12  ; [pp+0x36468] AnonymousClosure: (0xad5024), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildMainContent (0xad4958)
    //     0xad4d38: ldr             x1, [x1, #0x468]
    // 0xad4d3c: r2 = Null
    //     0xad4d3c: mov             x2, NULL
    // 0xad4d40: r0 = AllocateClosure()
    //     0xad4d40: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4d44: mov             x1, x0
    // 0xad4d48: ldur            x0, [fp, #-0x18]
    // 0xad4d4c: StoreField: r0->field_f = r1
    //     0xad4d4c: stur            w1, [x0, #0xf]
    // 0xad4d50: r3 = false
    //     0xad4d50: add             x3, NULL, #0x30  ; false
    // 0xad4d54: StoreField: r0->field_1f = r3
    //     0xad4d54: stur            w3, [x0, #0x1f]
    // 0xad4d58: StoreField: r0->field_23 = r3
    //     0xad4d58: stur            w3, [x0, #0x23]
    // 0xad4d5c: r1 = Function '<anonymous closure>':.
    //     0xad4d5c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36470] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xad4d60: ldr             x1, [x1, #0x470]
    // 0xad4d64: r2 = Null
    //     0xad4d64: mov             x2, NULL
    // 0xad4d68: r0 = AllocateClosure()
    //     0xad4d68: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4d6c: mov             x1, x0
    // 0xad4d70: ldur            x0, [fp, #-0x18]
    // 0xad4d74: StoreField: r0->field_2b = r1
    //     0xad4d74: stur            w1, [x0, #0x2b]
    // 0xad4d78: r1 = "main"
    //     0xad4d78: add             x1, PP, #0x36, lsl #12  ; [pp+0x36478] "main"
    //     0xad4d7c: ldr             x1, [x1, #0x478]
    // 0xad4d80: StoreField: r0->field_1b = r1
    //     0xad4d80: stur            w1, [x0, #0x1b]
    // 0xad4d84: r1 = Instance_ValueKey
    //     0xad4d84: add             x1, PP, #0x36, lsl #12  ; [pp+0x36480] Obj!ValueKey<String>@e14ba1
    //     0xad4d88: ldr             x1, [x1, #0x480]
    // 0xad4d8c: StoreField: r0->field_7 = r1
    //     0xad4d8c: stur            w1, [x0, #7]
    // 0xad4d90: LeaveFrame
    //     0xad4d90: mov             SP, fp
    //     0xad4d94: ldp             fp, lr, [SP], #0x10
    // 0xad4d98: ret
    //     0xad4d98: ret             
    // 0xad4d9c: r4 = true
    //     0xad4d9c: add             x4, NULL, #0x20  ; true
    // 0xad4da0: r3 = false
    //     0xad4da0: add             x3, NULL, #0x30  ; false
    // 0xad4da4: LoadField: r1 = r0->field_f
    //     0xad4da4: ldur            w1, [x0, #0xf]
    // 0xad4da8: DecompressPointer r1
    //     0xad4da8: add             x1, x1, HEAP, lsl #32
    // 0xad4dac: r0 = controller()
    //     0xad4dac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4db0: mov             x1, x0
    // 0xad4db4: r0 = mainChannels()
    //     0xad4db4: bl              #0xad4bbc  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::mainChannels
    // 0xad4db8: mov             x2, x0
    // 0xad4dbc: ldur            x0, [fp, #-8]
    // 0xad4dc0: stur            x2, [fp, #-0x10]
    // 0xad4dc4: LoadField: r1 = r0->field_f
    //     0xad4dc4: ldur            w1, [x0, #0xf]
    // 0xad4dc8: DecompressPointer r1
    //     0xad4dc8: add             x1, x1, HEAP, lsl #32
    // 0xad4dcc: r0 = controller()
    //     0xad4dcc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad4dd0: LoadField: r1 = r0->field_2f
    //     0xad4dd0: ldur            w1, [x0, #0x2f]
    // 0xad4dd4: DecompressPointer r1
    //     0xad4dd4: add             x1, x1, HEAP, lsl #32
    // 0xad4dd8: r0 = value()
    //     0xad4dd8: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad4ddc: r1 = LoadInt32Instr(r0)
    //     0xad4ddc: sbfx            x1, x0, #1, #0x1f
    //     0xad4de0: tbz             w0, #0, #0xad4de8
    //     0xad4de4: ldur            x1, [x0, #7]
    // 0xad4de8: sub             x2, x1, #1
    // 0xad4dec: ldur            x3, [fp, #-0x10]
    // 0xad4df0: LoadField: r0 = r3->field_b
    //     0xad4df0: ldur            w0, [x3, #0xb]
    // 0xad4df4: r1 = LoadInt32Instr(r0)
    //     0xad4df4: sbfx            x1, x0, #1, #0x1f
    // 0xad4df8: mov             x0, x1
    // 0xad4dfc: mov             x1, x2
    // 0xad4e00: cmp             x1, x0
    // 0xad4e04: b.hs            #0xad5020
    // 0xad4e08: LoadField: r0 = r3->field_f
    //     0xad4e08: ldur            w0, [x3, #0xf]
    // 0xad4e0c: DecompressPointer r0
    //     0xad4e0c: add             x0, x0, HEAP, lsl #32
    // 0xad4e10: ArrayLoad: r3 = r0[r2]  ; Unknown_4
    //     0xad4e10: add             x16, x0, x2, lsl #2
    //     0xad4e14: ldur            w3, [x16, #0xf]
    // 0xad4e18: DecompressPointer r3
    //     0xad4e18: add             x3, x3, HEAP, lsl #32
    // 0xad4e1c: mov             x0, x3
    // 0xad4e20: ldur            x2, [fp, #-0x20]
    // 0xad4e24: stur            x3, [fp, #-0x10]
    // 0xad4e28: StoreField: r2->field_f = r0
    //     0xad4e28: stur            w0, [x2, #0xf]
    //     0xad4e2c: ldurb           w16, [x2, #-1]
    //     0xad4e30: ldurb           w17, [x0, #-1]
    //     0xad4e34: and             x16, x17, x16, lsr #2
    //     0xad4e38: tst             x16, HEAP, lsr #32
    //     0xad4e3c: b.eq            #0xad4e44
    //     0xad4e40: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xad4e44: LoadField: r4 = r3->field_13
    //     0xad4e44: ldur            x4, [x3, #0x13]
    // 0xad4e48: stur            x4, [fp, #-0x28]
    // 0xad4e4c: r0 = BoxInt64Instr(r4)
    //     0xad4e4c: sbfiz           x0, x4, #1, #0x1f
    //     0xad4e50: cmp             x4, x0, asr #1
    //     0xad4e54: b.eq            #0xad4e60
    //     0xad4e58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad4e5c: stur            x4, [x0, #7]
    // 0xad4e60: r1 = <int>
    //     0xad4e60: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xad4e64: stur            x0, [fp, #-8]
    // 0xad4e68: r0 = ValueKey()
    //     0xad4e68: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xad4e6c: mov             x3, x0
    // 0xad4e70: ldur            x0, [fp, #-8]
    // 0xad4e74: stur            x3, [fp, #-0x18]
    // 0xad4e78: StoreField: r3->field_b = r0
    //     0xad4e78: stur            w0, [x3, #0xb]
    // 0xad4e7c: r1 = Null
    //     0xad4e7c: mov             x1, NULL
    // 0xad4e80: r2 = 6
    //     0xad4e80: movz            x2, #0x6
    // 0xad4e84: r0 = AllocateArray()
    //     0xad4e84: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad4e88: r16 = "Artikel "
    //     0xad4e88: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xad4e8c: ldr             x16, [x16, #0xb90]
    // 0xad4e90: StoreField: r0->field_f = r16
    //     0xad4e90: stur            w16, [x0, #0xf]
    // 0xad4e94: ldur            x1, [fp, #-0x10]
    // 0xad4e98: LoadField: r2 = r1->field_1b
    //     0xad4e98: ldur            w2, [x1, #0x1b]
    // 0xad4e9c: DecompressPointer r2
    //     0xad4e9c: add             x2, x2, HEAP, lsl #32
    // 0xad4ea0: StoreField: r0->field_13 = r2
    //     0xad4ea0: stur            w2, [x0, #0x13]
    // 0xad4ea4: r16 = " Terpopuler"
    //     0xad4ea4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36320] " Terpopuler"
    //     0xad4ea8: ldr             x16, [x16, #0x320]
    // 0xad4eac: ArrayStore: r0[0] = r16  ; List_4
    //     0xad4eac: stur            w16, [x0, #0x17]
    // 0xad4eb0: str             x0, [SP]
    // 0xad4eb4: r0 = _interpolate()
    //     0xad4eb4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad4eb8: r1 = Null
    //     0xad4eb8: mov             x1, NULL
    // 0xad4ebc: r2 = 6
    //     0xad4ebc: movz            x2, #0x6
    // 0xad4ec0: r0 = AllocateArray()
    //     0xad4ec0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad4ec4: r16 = "Artikel "
    //     0xad4ec4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35b90] "Artikel "
    //     0xad4ec8: ldr             x16, [x16, #0xb90]
    // 0xad4ecc: StoreField: r0->field_f = r16
    //     0xad4ecc: stur            w16, [x0, #0xf]
    // 0xad4ed0: ldur            x1, [fp, #-0x10]
    // 0xad4ed4: LoadField: r2 = r1->field_1b
    //     0xad4ed4: ldur            w2, [x1, #0x1b]
    // 0xad4ed8: DecompressPointer r2
    //     0xad4ed8: add             x2, x2, HEAP, lsl #32
    // 0xad4edc: StoreField: r0->field_13 = r2
    //     0xad4edc: stur            w2, [x0, #0x13]
    // 0xad4ee0: r16 = " Terbaru"
    //     0xad4ee0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36328] " Terbaru"
    //     0xad4ee4: ldr             x16, [x16, #0x328]
    // 0xad4ee8: ArrayStore: r0[0] = r16  ; List_4
    //     0xad4ee8: stur            w16, [x0, #0x17]
    // 0xad4eec: str             x0, [SP]
    // 0xad4ef0: r0 = _interpolate()
    //     0xad4ef0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad4ef4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad4ef4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad4ef8: ldr             x0, [x0, #0x2670]
    //     0xad4efc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad4f00: cmp             w0, w16
    //     0xad4f04: b.ne            #0xad4f10
    //     0xad4f08: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad4f0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad4f10: r16 = <ArticleRepository>
    //     0xad4f10: add             x16, PP, #0x10, lsl #12  ; [pp+0x10098] TypeArguments: <ArticleRepository>
    //     0xad4f14: ldr             x16, [x16, #0x98]
    // 0xad4f18: r30 = "article_repo"
    //     0xad4f18: add             lr, PP, #0x10, lsl #12  ; [pp+0x100a0] "article_repo"
    //     0xad4f1c: ldr             lr, [lr, #0xa0]
    // 0xad4f20: stp             lr, x16, [SP]
    // 0xad4f24: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xad4f24: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xad4f28: r0 = Inst.find()
    //     0xad4f28: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xad4f2c: stur            x0, [fp, #-8]
    // 0xad4f30: r0 = ArticleCategoryBuilderController()
    //     0xad4f30: bl              #0xad44d4  ; AllocateArticleCategoryBuilderControllerStub -> ArticleCategoryBuilderController (size=0x54)
    // 0xad4f34: mov             x1, x0
    // 0xad4f38: ldur            x2, [fp, #-0x28]
    // 0xad4f3c: ldur            x3, [fp, #-8]
    // 0xad4f40: stur            x0, [fp, #-8]
    // 0xad4f44: r0 = ArticleCategoryBuilderController()
    //     0xad4f44: bl              #0xad4334  ; [package:nuonline/app/modules/article/article_category/controllers/article_category_builder_controller.dart] ArticleCategoryBuilderController::ArticleCategoryBuilderController
    // 0xad4f48: ldur            x0, [fp, #-0x10]
    // 0xad4f4c: LoadField: r2 = r0->field_13
    //     0xad4f4c: ldur            x2, [x0, #0x13]
    // 0xad4f50: r0 = BoxInt64Instr(r2)
    //     0xad4f50: sbfiz           x0, x2, #1, #0x1f
    //     0xad4f54: cmp             x2, x0, asr #1
    //     0xad4f58: b.eq            #0xad4f64
    //     0xad4f5c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad4f60: stur            x2, [x0, #7]
    // 0xad4f64: r1 = 60
    //     0xad4f64: movz            x1, #0x3c
    // 0xad4f68: branchIfSmi(r0, 0xad4f74)
    //     0xad4f68: tbz             w0, #0, #0xad4f74
    // 0xad4f6c: r1 = LoadClassIdInstr(r0)
    //     0xad4f6c: ldur            x1, [x0, #-1]
    //     0xad4f70: ubfx            x1, x1, #0xc, #0x14
    // 0xad4f74: str             x0, [SP]
    // 0xad4f78: mov             x0, x1
    // 0xad4f7c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xad4f7c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xad4f80: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xad4f80: movz            x17, #0x2b03
    //     0xad4f84: add             lr, x0, x17
    //     0xad4f88: ldr             lr, [x21, lr, lsl #3]
    //     0xad4f8c: blr             lr
    // 0xad4f90: r1 = <ArticleCategoryBuilderController>
    //     0xad4f90: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad4f94: ldr             x1, [x1, #0x300]
    // 0xad4f98: stur            x0, [fp, #-0x10]
    // 0xad4f9c: r0 = GetBuilder()
    //     0xad4f9c: bl              #0xa41964  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0xad4fa0: mov             x3, x0
    // 0xad4fa4: ldur            x0, [fp, #-8]
    // 0xad4fa8: stur            x3, [fp, #-0x30]
    // 0xad4fac: StoreField: r3->field_3b = r0
    //     0xad4fac: stur            w0, [x3, #0x3b]
    // 0xad4fb0: r0 = true
    //     0xad4fb0: add             x0, NULL, #0x20  ; true
    // 0xad4fb4: StoreField: r3->field_13 = r0
    //     0xad4fb4: stur            w0, [x3, #0x13]
    // 0xad4fb8: ldur            x2, [fp, #-0x20]
    // 0xad4fbc: r1 = Function '<anonymous closure>':.
    //     0xad4fbc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36488] AnonymousClosure: (0xad44e0), in [package:nuonline/app/modules/article/views/main_article_view.dart] MainArticleView::_buildIslamicContent (0xad3d14)
    //     0xad4fc0: ldr             x1, [x1, #0x488]
    // 0xad4fc4: r0 = AllocateClosure()
    //     0xad4fc4: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4fc8: mov             x1, x0
    // 0xad4fcc: ldur            x0, [fp, #-0x30]
    // 0xad4fd0: StoreField: r0->field_f = r1
    //     0xad4fd0: stur            w1, [x0, #0xf]
    // 0xad4fd4: r1 = false
    //     0xad4fd4: add             x1, NULL, #0x30  ; false
    // 0xad4fd8: StoreField: r0->field_1f = r1
    //     0xad4fd8: stur            w1, [x0, #0x1f]
    // 0xad4fdc: StoreField: r0->field_23 = r1
    //     0xad4fdc: stur            w1, [x0, #0x23]
    // 0xad4fe0: r1 = Function '<anonymous closure>':.
    //     0xad4fe0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36490] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0xad4fe4: ldr             x1, [x1, #0x490]
    // 0xad4fe8: r2 = Null
    //     0xad4fe8: mov             x2, NULL
    // 0xad4fec: r0 = AllocateClosure()
    //     0xad4fec: bl              #0xec1630  ; AllocateClosureStub
    // 0xad4ff0: mov             x1, x0
    // 0xad4ff4: ldur            x0, [fp, #-0x30]
    // 0xad4ff8: StoreField: r0->field_2b = r1
    //     0xad4ff8: stur            w1, [x0, #0x2b]
    // 0xad4ffc: ldur            x1, [fp, #-0x10]
    // 0xad5000: StoreField: r0->field_1b = r1
    //     0xad5000: stur            w1, [x0, #0x1b]
    // 0xad5004: ldur            x1, [fp, #-0x18]
    // 0xad5008: StoreField: r0->field_7 = r1
    //     0xad5008: stur            w1, [x0, #7]
    // 0xad500c: LeaveFrame
    //     0xad500c: mov             SP, fp
    //     0xad5010: ldp             fp, lr, [SP], #0x10
    // 0xad5014: ret
    //     0xad5014: ret             
    // 0xad5018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad501c: b               #0xad4c9c
    // 0xad5020: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad5020: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] ArticleCategoryBuilderWidget <anonymous closure>(dynamic, ArticleCategoryBuilderController) {
    // ** addr: 0xad5024, size: 0x34
    // 0xad5024: EnterFrame
    //     0xad5024: stp             fp, lr, [SP, #-0x10]!
    //     0xad5028: mov             fp, SP
    // 0xad502c: r1 = <ArticleCategoryBuilderController>
    //     0xad502c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36300] TypeArguments: <ArticleCategoryBuilderController>
    //     0xad5030: ldr             x1, [x1, #0x300]
    // 0xad5034: r0 = ArticleCategoryBuilderWidget()
    //     0xad5034: bl              #0xad4584  ; AllocateArticleCategoryBuilderWidgetStub -> ArticleCategoryBuilderWidget (size=0x1c)
    // 0xad5038: r1 = "main"
    //     0xad5038: add             x1, PP, #0x36, lsl #12  ; [pp+0x36478] "main"
    //     0xad503c: ldr             x1, [x1, #0x478]
    // 0xad5040: StoreField: r0->field_13 = r1
    //     0xad5040: stur            w1, [x0, #0x13]
    // 0xad5044: r1 = false
    //     0xad5044: add             x1, NULL, #0x30  ; false
    // 0xad5048: ArrayStore: r0[0] = r1  ; List_4
    //     0xad5048: stur            w1, [x0, #0x17]
    // 0xad504c: LeaveFrame
    //     0xad504c: mov             SP, fp
    //     0xad5050: ldp             fp, lr, [SP], #0x10
    // 0xad5054: ret
    //     0xad5054: ret             
  }
  [closure] ArticleChannelWidget <anonymous closure>(dynamic) {
    // ** addr: 0xad5058, size: 0x160
    // 0xad5058: EnterFrame
    //     0xad5058: stp             fp, lr, [SP, #-0x10]!
    //     0xad505c: mov             fp, SP
    // 0xad5060: AllocStack(0x30)
    //     0xad5060: sub             SP, SP, #0x30
    // 0xad5064: SetupParameters()
    //     0xad5064: ldr             x0, [fp, #0x10]
    //     0xad5068: ldur            w2, [x0, #0x17]
    //     0xad506c: add             x2, x2, HEAP, lsl #32
    //     0xad5070: stur            x2, [fp, #-8]
    // 0xad5074: CheckStackOverflow
    //     0xad5074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5078: cmp             SP, x16
    //     0xad507c: b.ls            #0xad51b0
    // 0xad5080: LoadField: r1 = r2->field_f
    //     0xad5080: ldur            w1, [x2, #0xf]
    // 0xad5084: DecompressPointer r1
    //     0xad5084: add             x1, x1, HEAP, lsl #32
    // 0xad5088: r0 = controller()
    //     0xad5088: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad508c: LoadField: r1 = r0->field_2f
    //     0xad508c: ldur            w1, [x0, #0x2f]
    // 0xad5090: DecompressPointer r1
    //     0xad5090: add             x1, x1, HEAP, lsl #32
    // 0xad5094: r0 = value()
    //     0xad5094: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad5098: mov             x2, x0
    // 0xad509c: ldur            x0, [fp, #-8]
    // 0xad50a0: stur            x2, [fp, #-0x10]
    // 0xad50a4: LoadField: r1 = r0->field_f
    //     0xad50a4: ldur            w1, [x0, #0xf]
    // 0xad50a8: DecompressPointer r1
    //     0xad50a8: add             x1, x1, HEAP, lsl #32
    // 0xad50ac: r0 = controller()
    //     0xad50ac: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad50b0: LoadField: r2 = r0->field_2f
    //     0xad50b0: ldur            w2, [x0, #0x2f]
    // 0xad50b4: DecompressPointer r2
    //     0xad50b4: add             x2, x2, HEAP, lsl #32
    // 0xad50b8: LoadField: r3 = r2->field_7
    //     0xad50b8: ldur            w3, [x2, #7]
    // 0xad50bc: DecompressPointer r3
    //     0xad50bc: add             x3, x3, HEAP, lsl #32
    // 0xad50c0: r1 = Function 'call':.
    //     0xad50c0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28310] AnonymousClosure: (0x8a94e4), in [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call (0x8a9554)
    //     0xad50c4: ldr             x1, [x1, #0x310]
    // 0xad50c8: r0 = AllocateClosureTA()
    //     0xad50c8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xad50cc: mov             x3, x0
    // 0xad50d0: r2 = Null
    //     0xad50d0: mov             x2, NULL
    // 0xad50d4: r1 = Null
    //     0xad50d4: mov             x1, NULL
    // 0xad50d8: stur            x3, [fp, #-0x18]
    // 0xad50dc: r8 = (dynamic this, int?) => int
    //     0xad50dc: add             x8, PP, #0x2a, lsl #12  ; [pp+0x2a0a0] FunctionType: (dynamic this, int?) => int
    //     0xad50e0: ldr             x8, [x8, #0xa0]
    // 0xad50e4: r3 = Null
    //     0xad50e4: add             x3, PP, #0x36, lsl #12  ; [pp+0x364a8] Null
    //     0xad50e8: ldr             x3, [x3, #0x4a8]
    // 0xad50ec: r0 = DefaultTypeTest()
    //     0xad50ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xad50f0: ldur            x0, [fp, #-8]
    // 0xad50f4: LoadField: r1 = r0->field_f
    //     0xad50f4: ldur            w1, [x0, #0xf]
    // 0xad50f8: DecompressPointer r1
    //     0xad50f8: add             x1, x1, HEAP, lsl #32
    // 0xad50fc: r0 = controller()
    //     0xad50fc: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5100: mov             x1, x0
    // 0xad5104: r0 = mainChannels()
    //     0xad5104: bl              #0xad4bbc  ; [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::mainChannels
    // 0xad5108: mov             x2, x0
    // 0xad510c: ldur            x0, [fp, #-8]
    // 0xad5110: stur            x2, [fp, #-0x20]
    // 0xad5114: LoadField: r1 = r0->field_f
    //     0xad5114: ldur            w1, [x0, #0xf]
    // 0xad5118: DecompressPointer r1
    //     0xad5118: add             x1, x1, HEAP, lsl #32
    // 0xad511c: r0 = controller()
    //     0xad511c: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5120: mov             x2, x0
    // 0xad5124: ldur            x0, [fp, #-8]
    // 0xad5128: stur            x2, [fp, #-0x28]
    // 0xad512c: LoadField: r1 = r0->field_f
    //     0xad512c: ldur            w1, [x0, #0xf]
    // 0xad5130: DecompressPointer r1
    //     0xad5130: add             x1, x1, HEAP, lsl #32
    // 0xad5134: r0 = controller()
    //     0xad5134: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5138: stur            x0, [fp, #-8]
    // 0xad513c: r0 = ArticleChannelWidget()
    //     0xad513c: bl              #0xad46fc  ; AllocateArticleChannelWidgetStub -> ArticleChannelWidget (size=0x24)
    // 0xad5140: mov             x3, x0
    // 0xad5144: ldur            x0, [fp, #-0x20]
    // 0xad5148: stur            x3, [fp, #-0x30]
    // 0xad514c: StoreField: r3->field_b = r0
    //     0xad514c: stur            w0, [x3, #0xb]
    // 0xad5150: ldur            x0, [fp, #-0x10]
    // 0xad5154: r1 = LoadInt32Instr(r0)
    //     0xad5154: sbfx            x1, x0, #1, #0x1f
    //     0xad5158: tbz             w0, #0, #0xad5160
    //     0xad515c: ldur            x1, [x0, #7]
    // 0xad5160: StoreField: r3->field_f = r1
    //     0xad5160: stur            x1, [x3, #0xf]
    // 0xad5164: ldur            x0, [fp, #-0x18]
    // 0xad5168: ArrayStore: r3[0] = r0  ; List_4
    //     0xad5168: stur            w0, [x3, #0x17]
    // 0xad516c: ldur            x2, [fp, #-0x28]
    // 0xad5170: r1 = Function 'onMoreCategoryPressed':.
    //     0xad5170: add             x1, PP, #0x36, lsl #12  ; [pp+0x36388] AnonymousClosure: (0xad4850), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onMoreCategoryPressed (0xad4888)
    //     0xad5174: ldr             x1, [x1, #0x388]
    // 0xad5178: r0 = AllocateClosure()
    //     0xad5178: bl              #0xec1630  ; AllocateClosureStub
    // 0xad517c: mov             x1, x0
    // 0xad5180: ldur            x0, [fp, #-0x30]
    // 0xad5184: StoreField: r0->field_1b = r1
    //     0xad5184: stur            w1, [x0, #0x1b]
    // 0xad5188: ldur            x2, [fp, #-8]
    // 0xad518c: r1 = Function 'onSettingCategoryPressed':.
    //     0xad518c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36390] AnonymousClosure: (0xad4708), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onSettingCategoryPressed (0xad4740)
    //     0xad5190: ldr             x1, [x1, #0x390]
    // 0xad5194: r0 = AllocateClosure()
    //     0xad5194: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5198: mov             x1, x0
    // 0xad519c: ldur            x0, [fp, #-0x30]
    // 0xad51a0: StoreField: r0->field_1f = r1
    //     0xad51a0: stur            w1, [x0, #0x1f]
    // 0xad51a4: LeaveFrame
    //     0xad51a4: mov             SP, fp
    //     0xad51a8: ldp             fp, lr, [SP], #0x10
    // 0xad51ac: ret
    //     0xad51ac: ret             
    // 0xad51b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad51b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad51b4: b               #0xad5080
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad51b8, size: 0x5c
    // 0xad51b8: EnterFrame
    //     0xad51b8: stp             fp, lr, [SP, #-0x10]!
    //     0xad51bc: mov             fp, SP
    // 0xad51c0: AllocStack(0x10)
    //     0xad51c0: sub             SP, SP, #0x10
    // 0xad51c4: CheckStackOverflow
    //     0xad51c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad51c8: cmp             SP, x16
    //     0xad51cc: b.ls            #0xad520c
    // 0xad51d0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad51d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad51d4: ldr             x0, [x0, #0x2670]
    //     0xad51d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad51dc: cmp             w0, w16
    //     0xad51e0: b.ne            #0xad51ec
    //     0xad51e4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad51e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad51ec: r16 = "/article/article-bookmark"
    //     0xad51ec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24e78] "/article/article-bookmark"
    //     0xad51f0: ldr             x16, [x16, #0xe78]
    // 0xad51f4: stp             x16, NULL, [SP]
    // 0xad51f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad51f8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad51fc: r0 = GetNavigation.toNamed()
    //     0xad51fc: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad5200: LeaveFrame
    //     0xad5200: mov             SP, fp
    //     0xad5204: ldp             fp, lr, [SP], #0x10
    // 0xad5208: ret
    //     0xad5208: ret             
    // 0xad520c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad520c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5210: b               #0xad51d0
  }
  [closure] NSegmentControl <anonymous closure>(dynamic) {
    // ** addr: 0xad5214, size: 0xb8
    // 0xad5214: EnterFrame
    //     0xad5214: stp             fp, lr, [SP, #-0x10]!
    //     0xad5218: mov             fp, SP
    // 0xad521c: AllocStack(0x18)
    //     0xad521c: sub             SP, SP, #0x18
    // 0xad5220: SetupParameters()
    //     0xad5220: ldr             x0, [fp, #0x10]
    //     0xad5224: ldur            w2, [x0, #0x17]
    //     0xad5228: add             x2, x2, HEAP, lsl #32
    //     0xad522c: stur            x2, [fp, #-8]
    // 0xad5230: CheckStackOverflow
    //     0xad5230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5234: cmp             SP, x16
    //     0xad5238: b.ls            #0xad52c4
    // 0xad523c: LoadField: r1 = r2->field_f
    //     0xad523c: ldur            w1, [x2, #0xf]
    // 0xad5240: DecompressPointer r1
    //     0xad5240: add             x1, x1, HEAP, lsl #32
    // 0xad5244: r0 = controller()
    //     0xad5244: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad5248: LoadField: r1 = r0->field_37
    //     0xad5248: ldur            w1, [x0, #0x37]
    // 0xad524c: DecompressPointer r1
    //     0xad524c: add             x1, x1, HEAP, lsl #32
    // 0xad5250: r0 = value()
    //     0xad5250: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad5254: mov             x2, x0
    // 0xad5258: ldur            x0, [fp, #-8]
    // 0xad525c: stur            x2, [fp, #-0x10]
    // 0xad5260: LoadField: r1 = r0->field_f
    //     0xad5260: ldur            w1, [x0, #0xf]
    // 0xad5264: DecompressPointer r1
    //     0xad5264: add             x1, x1, HEAP, lsl #32
    // 0xad5268: r0 = controller()
    //     0xad5268: bl              #0x6155cc  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xad526c: stur            x0, [fp, #-8]
    // 0xad5270: r0 = NSegmentControl()
    //     0xad5270: bl              #0xad52cc  ; AllocateNSegmentControlStub -> NSegmentControl (size=0x1c)
    // 0xad5274: mov             x3, x0
    // 0xad5278: r0 = const [Utama, Keislaman]
    //     0xad5278: add             x0, PP, #0x36, lsl #12  ; [pp+0x362d0] List<String>(2)
    //     0xad527c: ldr             x0, [x0, #0x2d0]
    // 0xad5280: stur            x3, [fp, #-0x18]
    // 0xad5284: StoreField: r3->field_b = r0
    //     0xad5284: stur            w0, [x3, #0xb]
    // 0xad5288: ldur            x2, [fp, #-8]
    // 0xad528c: r1 = Function 'onValueChanged':.
    //     0xad528c: add             x1, PP, #0x36, lsl #12  ; [pp+0x362d8] AnonymousClosure: (0xad52d8), in [package:nuonline/app/modules/article/controllers/main_article_controller.dart] MainArticleController::onValueChanged (0xad5314)
    //     0xad5290: ldr             x1, [x1, #0x2d8]
    // 0xad5294: r0 = AllocateClosure()
    //     0xad5294: bl              #0xec1630  ; AllocateClosureStub
    // 0xad5298: mov             x1, x0
    // 0xad529c: ldur            x0, [fp, #-0x18]
    // 0xad52a0: ArrayStore: r0[0] = r1  ; List_4
    //     0xad52a0: stur            w1, [x0, #0x17]
    // 0xad52a4: ldur            x1, [fp, #-0x10]
    // 0xad52a8: r2 = LoadInt32Instr(r1)
    //     0xad52a8: sbfx            x2, x1, #1, #0x1f
    //     0xad52ac: tbz             w1, #0, #0xad52b4
    //     0xad52b0: ldur            x2, [x1, #7]
    // 0xad52b4: StoreField: r0->field_f = r2
    //     0xad52b4: stur            x2, [x0, #0xf]
    // 0xad52b8: LeaveFrame
    //     0xad52b8: mov             SP, fp
    //     0xad52bc: ldp             fp, lr, [SP], #0x10
    // 0xad52c0: ret
    //     0xad52c0: ret             
    // 0xad52c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad52c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad52c8: b               #0xad523c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xad53a4, size: 0x5c
    // 0xad53a4: EnterFrame
    //     0xad53a4: stp             fp, lr, [SP, #-0x10]!
    //     0xad53a8: mov             fp, SP
    // 0xad53ac: AllocStack(0x10)
    //     0xad53ac: sub             SP, SP, #0x10
    // 0xad53b0: CheckStackOverflow
    //     0xad53b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad53b4: cmp             SP, x16
    //     0xad53b8: b.ls            #0xad53f8
    // 0xad53bc: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad53bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad53c0: ldr             x0, [x0, #0x2670]
    //     0xad53c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad53c8: cmp             w0, w16
    //     0xad53cc: b.ne            #0xad53d8
    //     0xad53d0: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad53d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad53d8: r16 = "/article/article-search"
    //     0xad53d8: add             x16, PP, #0x36, lsl #12  ; [pp+0x362e0] "/article/article-search"
    //     0xad53dc: ldr             x16, [x16, #0x2e0]
    // 0xad53e0: stp             x16, NULL, [SP]
    // 0xad53e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xad53e4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xad53e8: r0 = GetNavigation.toNamed()
    //     0xad53e8: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad53ec: LeaveFrame
    //     0xad53ec: mov             SP, fp
    //     0xad53f0: ldp             fp, lr, [SP], #0x10
    // 0xad53f4: ret
    //     0xad53f4: ret             
    // 0xad53f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad53f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad53fc: b               #0xad53bc
  }
}
