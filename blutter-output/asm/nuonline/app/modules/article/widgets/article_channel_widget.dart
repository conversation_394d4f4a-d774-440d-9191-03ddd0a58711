// lib: , url: package:nuonline/app/modules/article/widgets/article_channel_widget.dart

// class id: 1050151, size: 0x8
class :: {
}

// class id: 5065, size: 0x24, field offset: 0xc
//   const constructor, 
class ArticleChannelWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb830dc, size: 0x2f0
    // 0xb830dc: EnterFrame
    //     0xb830dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb830e0: mov             fp, SP
    // 0xb830e4: AllocStack(0x48)
    //     0xb830e4: sub             SP, SP, #0x48
    // 0xb830e8: SetupParameters(ArticleChannelWidget this /* r1 => r1, fp-0x8 */)
    //     0xb830e8: stur            x1, [fp, #-8]
    // 0xb830ec: CheckStackOverflow
    //     0xb830ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb830f0: cmp             SP, x16
    //     0xb830f4: b.ls            #0xb833c4
    // 0xb830f8: r1 = 1
    //     0xb830f8: movz            x1, #0x1
    // 0xb830fc: r0 = AllocateContext()
    //     0xb830fc: bl              #0xec126c  ; AllocateContextStub
    // 0xb83100: mov             x1, x0
    // 0xb83104: ldur            x0, [fp, #-8]
    // 0xb83108: stur            x1, [fp, #-0x10]
    // 0xb8310c: StoreField: r1->field_f = r0
    //     0xb8310c: stur            w0, [x1, #0xf]
    // 0xb83110: r16 = <Color?>
    //     0xb83110: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb83114: ldr             x16, [x16, #0x98]
    // 0xb83118: r30 = Instance_Color
    //     0xb83118: add             lr, PP, #0x40, lsl #12  ; [pp+0x40850] Obj!Color@e2b0b1
    //     0xb8311c: ldr             lr, [lr, #0x850]
    // 0xb83120: stp             lr, x16, [SP, #8]
    // 0xb83124: r16 = Instance_MaterialColor
    //     0xb83124: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e38] Obj!MaterialColor@e2bb31
    //     0xb83128: ldr             x16, [x16, #0xe38]
    // 0xb8312c: str             x16, [SP]
    // 0xb83130: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb83130: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb83134: r0 = mode()
    //     0xb83134: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb83138: stur            x0, [fp, #-0x18]
    // 0xb8313c: r0 = BoxDecoration()
    //     0xb8313c: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb83140: mov             x3, x0
    // 0xb83144: ldur            x0, [fp, #-0x18]
    // 0xb83148: stur            x3, [fp, #-0x28]
    // 0xb8314c: StoreField: r3->field_7 = r0
    //     0xb8314c: stur            w0, [x3, #7]
    // 0xb83150: r0 = Instance_BorderRadius
    //     0xb83150: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!BorderRadius@e13cb1
    //     0xb83154: ldr             x0, [x0, #0x858]
    // 0xb83158: StoreField: r3->field_13 = r0
    //     0xb83158: stur            w0, [x3, #0x13]
    // 0xb8315c: r0 = Instance_BoxShape
    //     0xb8315c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb83160: ldr             x0, [x0, #0xca8]
    // 0xb83164: StoreField: r3->field_23 = r0
    //     0xb83164: stur            w0, [x3, #0x23]
    // 0xb83168: ldur            x0, [fp, #-8]
    // 0xb8316c: LoadField: r1 = r0->field_b
    //     0xb8316c: ldur            w1, [x0, #0xb]
    // 0xb83170: DecompressPointer r1
    //     0xb83170: add             x1, x1, HEAP, lsl #32
    // 0xb83174: LoadField: r2 = r1->field_b
    //     0xb83174: ldur            w2, [x1, #0xb]
    // 0xb83178: r1 = LoadInt32Instr(r2)
    //     0xb83178: sbfx            x1, x2, #1, #0x1f
    // 0xb8317c: add             x4, x1, #2
    // 0xb83180: stur            x4, [fp, #-0x20]
    // 0xb83184: r1 = Function '<anonymous closure>':.
    //     0xb83184: add             x1, PP, #0x40, lsl #12  ; [pp+0x40860] AnonymousClosure: (0xb83728), in [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::build (0xb830dc)
    //     0xb83188: ldr             x1, [x1, #0x860]
    // 0xb8318c: r2 = Null
    //     0xb8318c: mov             x2, NULL
    // 0xb83190: r0 = AllocateClosure()
    //     0xb83190: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83194: ldur            x2, [fp, #-0x10]
    // 0xb83198: r1 = Function '<anonymous closure>':.
    //     0xb83198: add             x1, PP, #0x40, lsl #12  ; [pp+0x40868] AnonymousClosure: (0xb833cc), in [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::build (0xb830dc)
    //     0xb8319c: ldr             x1, [x1, #0x868]
    // 0xb831a0: stur            x0, [fp, #-0x10]
    // 0xb831a4: r0 = AllocateClosure()
    //     0xb831a4: bl              #0xec1630  ; AllocateClosureStub
    // 0xb831a8: stur            x0, [fp, #-0x18]
    // 0xb831ac: r0 = ListView()
    //     0xb831ac: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb831b0: stur            x0, [fp, #-0x30]
    // 0xb831b4: r16 = Instance_EdgeInsets
    //     0xb831b4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28360] Obj!EdgeInsets@e121c1
    //     0xb831b8: ldr             x16, [x16, #0x360]
    // 0xb831bc: r30 = Instance_Axis
    //     0xb831bc: ldr             lr, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb831c0: stp             lr, x16, [SP]
    // 0xb831c4: mov             x1, x0
    // 0xb831c8: ldur            x2, [fp, #-0x18]
    // 0xb831cc: ldur            x3, [fp, #-0x20]
    // 0xb831d0: ldur            x5, [fp, #-0x10]
    // 0xb831d4: r4 = const [0, 0x6, 0x2, 0x4, padding, 0x4, scrollDirection, 0x5, null]
    //     0xb831d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33af8] List(9) [0, 0x6, 0x2, 0x4, "padding", 0x4, "scrollDirection", 0x5, Null]
    //     0xb831d8: ldr             x4, [x4, #0xaf8]
    // 0xb831dc: r0 = ListView.separated()
    //     0xb831dc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb831e0: r0 = DecoratedBox()
    //     0xb831e0: bl              #0x9d4fec  ; AllocateDecoratedBoxStub -> DecoratedBox (size=0x18)
    // 0xb831e4: mov             x2, x0
    // 0xb831e8: ldur            x0, [fp, #-0x28]
    // 0xb831ec: stur            x2, [fp, #-0x10]
    // 0xb831f0: StoreField: r2->field_f = r0
    //     0xb831f0: stur            w0, [x2, #0xf]
    // 0xb831f4: r0 = Instance_DecorationPosition
    //     0xb831f4: add             x0, PP, #0x29, lsl #12  ; [pp+0x29b28] Obj!DecorationPosition@e35881
    //     0xb831f8: ldr             x0, [x0, #0xb28]
    // 0xb831fc: StoreField: r2->field_13 = r0
    //     0xb831fc: stur            w0, [x2, #0x13]
    // 0xb83200: ldur            x0, [fp, #-0x30]
    // 0xb83204: StoreField: r2->field_b = r0
    //     0xb83204: stur            w0, [x2, #0xb]
    // 0xb83208: r1 = <FlexParentData>
    //     0xb83208: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xb8320c: ldr             x1, [x1, #0x720]
    // 0xb83210: r0 = Expanded()
    //     0xb83210: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb83214: mov             x1, x0
    // 0xb83218: r0 = 1
    //     0xb83218: movz            x0, #0x1
    // 0xb8321c: stur            x1, [fp, #-0x18]
    // 0xb83220: StoreField: r1->field_13 = r0
    //     0xb83220: stur            x0, [x1, #0x13]
    // 0xb83224: r0 = Instance_FlexFit
    //     0xb83224: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xb83228: ldr             x0, [x0, #0x728]
    // 0xb8322c: StoreField: r1->field_1b = r0
    //     0xb8322c: stur            w0, [x1, #0x1b]
    // 0xb83230: ldur            x0, [fp, #-0x10]
    // 0xb83234: StoreField: r1->field_b = r0
    //     0xb83234: stur            w0, [x1, #0xb]
    // 0xb83238: ldur            x0, [fp, #-8]
    // 0xb8323c: LoadField: r2 = r0->field_1f
    //     0xb8323c: ldur            w2, [x0, #0x1f]
    // 0xb83240: DecompressPointer r2
    //     0xb83240: add             x2, x2, HEAP, lsl #32
    // 0xb83244: stur            x2, [fp, #-0x10]
    // 0xb83248: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb83248: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8324c: ldr             x0, [x0, #0x2670]
    //     0xb83250: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb83254: cmp             w0, w16
    //     0xb83258: b.ne            #0xb83264
    //     0xb8325c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb83260: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb83264: r0 = GetNavigation.theme()
    //     0xb83264: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb83268: LoadField: r1 = r0->field_3f
    //     0xb83268: ldur            w1, [x0, #0x3f]
    // 0xb8326c: DecompressPointer r1
    //     0xb8326c: add             x1, x1, HEAP, lsl #32
    // 0xb83270: LoadField: r0 = r1->field_2b
    //     0xb83270: ldur            w0, [x1, #0x2b]
    // 0xb83274: DecompressPointer r0
    //     0xb83274: add             x0, x0, HEAP, lsl #32
    // 0xb83278: stur            x0, [fp, #-8]
    // 0xb8327c: r0 = Icon()
    //     0xb8327c: bl              #0x7e5f50  ; AllocateIconStub -> Icon (size=0x3c)
    // 0xb83280: mov             x1, x0
    // 0xb83284: r0 = Instance_IconData
    //     0xb83284: add             x0, PP, #0x30, lsl #12  ; [pp+0x30b60] Obj!IconData@e101b1
    //     0xb83288: ldr             x0, [x0, #0xb60]
    // 0xb8328c: stur            x1, [fp, #-0x28]
    // 0xb83290: StoreField: r1->field_b = r0
    //     0xb83290: stur            w0, [x1, #0xb]
    // 0xb83294: r0 = 16.000000
    //     0xb83294: add             x0, PP, #0x27, lsl #12  ; [pp+0x27080] 16
    //     0xb83298: ldr             x0, [x0, #0x80]
    // 0xb8329c: StoreField: r1->field_f = r0
    //     0xb8329c: stur            w0, [x1, #0xf]
    // 0xb832a0: ldur            x0, [fp, #-8]
    // 0xb832a4: StoreField: r1->field_23 = r0
    //     0xb832a4: stur            w0, [x1, #0x23]
    // 0xb832a8: r0 = IconButton()
    //     0xb832a8: bl              #0xa32b14  ; AllocateIconButtonStub -> IconButton (size=0x68)
    // 0xb832ac: mov             x1, x0
    // 0xb832b0: r0 = Instance_EdgeInsets
    //     0xb832b0: ldr             x0, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xb832b4: stur            x1, [fp, #-8]
    // 0xb832b8: StoreField: r1->field_13 = r0
    //     0xb832b8: stur            w0, [x1, #0x13]
    // 0xb832bc: ldur            x0, [fp, #-0x10]
    // 0xb832c0: StoreField: r1->field_3b = r0
    //     0xb832c0: stur            w0, [x1, #0x3b]
    // 0xb832c4: r0 = false
    //     0xb832c4: add             x0, NULL, #0x30  ; false
    // 0xb832c8: StoreField: r1->field_47 = r0
    //     0xb832c8: stur            w0, [x1, #0x47]
    // 0xb832cc: ldur            x0, [fp, #-0x28]
    // 0xb832d0: StoreField: r1->field_1f = r0
    //     0xb832d0: stur            w0, [x1, #0x1f]
    // 0xb832d4: r0 = Instance__IconButtonVariant
    //     0xb832d4: add             x0, PP, #0x26, lsl #12  ; [pp+0x26f78] Obj!_IconButtonVariant@e36961
    //     0xb832d8: ldr             x0, [x0, #0xf78]
    // 0xb832dc: StoreField: r1->field_63 = r0
    //     0xb832dc: stur            w0, [x1, #0x63]
    // 0xb832e0: r0 = SizedBox()
    //     0xb832e0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb832e4: mov             x3, x0
    // 0xb832e8: r0 = 40.000000
    //     0xb832e8: add             x0, PP, #0x29, lsl #12  ; [pp+0x291b8] 40
    //     0xb832ec: ldr             x0, [x0, #0x1b8]
    // 0xb832f0: stur            x3, [fp, #-0x10]
    // 0xb832f4: StoreField: r3->field_f = r0
    //     0xb832f4: stur            w0, [x3, #0xf]
    // 0xb832f8: ldur            x0, [fp, #-8]
    // 0xb832fc: StoreField: r3->field_b = r0
    //     0xb832fc: stur            w0, [x3, #0xb]
    // 0xb83300: r1 = Null
    //     0xb83300: mov             x1, NULL
    // 0xb83304: r2 = 4
    //     0xb83304: movz            x2, #0x4
    // 0xb83308: r0 = AllocateArray()
    //     0xb83308: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8330c: mov             x2, x0
    // 0xb83310: ldur            x0, [fp, #-0x18]
    // 0xb83314: stur            x2, [fp, #-8]
    // 0xb83318: StoreField: r2->field_f = r0
    //     0xb83318: stur            w0, [x2, #0xf]
    // 0xb8331c: ldur            x0, [fp, #-0x10]
    // 0xb83320: StoreField: r2->field_13 = r0
    //     0xb83320: stur            w0, [x2, #0x13]
    // 0xb83324: r1 = <Widget>
    //     0xb83324: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb83328: r0 = AllocateGrowableArray()
    //     0xb83328: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb8332c: mov             x1, x0
    // 0xb83330: ldur            x0, [fp, #-8]
    // 0xb83334: stur            x1, [fp, #-0x10]
    // 0xb83338: StoreField: r1->field_f = r0
    //     0xb83338: stur            w0, [x1, #0xf]
    // 0xb8333c: r0 = 4
    //     0xb8333c: movz            x0, #0x4
    // 0xb83340: StoreField: r1->field_b = r0
    //     0xb83340: stur            w0, [x1, #0xb]
    // 0xb83344: r0 = Row()
    //     0xb83344: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb83348: mov             x1, x0
    // 0xb8334c: r0 = Instance_Axis
    //     0xb8334c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb83350: stur            x1, [fp, #-8]
    // 0xb83354: StoreField: r1->field_f = r0
    //     0xb83354: stur            w0, [x1, #0xf]
    // 0xb83358: r0 = Instance_MainAxisAlignment
    //     0xb83358: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8335c: ldr             x0, [x0, #0x730]
    // 0xb83360: StoreField: r1->field_13 = r0
    //     0xb83360: stur            w0, [x1, #0x13]
    // 0xb83364: r0 = Instance_MainAxisSize
    //     0xb83364: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb83368: ldr             x0, [x0, #0x738]
    // 0xb8336c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8336c: stur            w0, [x1, #0x17]
    // 0xb83370: r0 = Instance_CrossAxisAlignment
    //     0xb83370: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb83374: ldr             x0, [x0, #0x740]
    // 0xb83378: StoreField: r1->field_1b = r0
    //     0xb83378: stur            w0, [x1, #0x1b]
    // 0xb8337c: r0 = Instance_VerticalDirection
    //     0xb8337c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb83380: ldr             x0, [x0, #0x748]
    // 0xb83384: StoreField: r1->field_23 = r0
    //     0xb83384: stur            w0, [x1, #0x23]
    // 0xb83388: r0 = Instance_Clip
    //     0xb83388: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8338c: ldr             x0, [x0, #0x750]
    // 0xb83390: StoreField: r1->field_2b = r0
    //     0xb83390: stur            w0, [x1, #0x2b]
    // 0xb83394: StoreField: r1->field_2f = rZR
    //     0xb83394: stur            xzr, [x1, #0x2f]
    // 0xb83398: ldur            x0, [fp, #-0x10]
    // 0xb8339c: StoreField: r1->field_b = r0
    //     0xb8339c: stur            w0, [x1, #0xb]
    // 0xb833a0: r0 = SizedBox()
    //     0xb833a0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb833a4: r1 = 52.000000
    //     0xb833a4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33cd8] 52
    //     0xb833a8: ldr             x1, [x1, #0xcd8]
    // 0xb833ac: StoreField: r0->field_13 = r1
    //     0xb833ac: stur            w1, [x0, #0x13]
    // 0xb833b0: ldur            x1, [fp, #-8]
    // 0xb833b4: StoreField: r0->field_b = r1
    //     0xb833b4: stur            w1, [x0, #0xb]
    // 0xb833b8: LeaveFrame
    //     0xb833b8: mov             SP, fp
    //     0xb833bc: ldp             fp, lr, [SP], #0x10
    // 0xb833c0: ret
    //     0xb833c0: ret             
    // 0xb833c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb833c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb833c8: b               #0xb830f8
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb833cc, size: 0x10c
    // 0xb833cc: EnterFrame
    //     0xb833cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb833d0: mov             fp, SP
    // 0xb833d4: AllocStack(0x8)
    //     0xb833d4: sub             SP, SP, #8
    // 0xb833d8: SetupParameters()
    //     0xb833d8: ldr             x0, [fp, #0x20]
    //     0xb833dc: ldur            w1, [x0, #0x17]
    //     0xb833e0: add             x1, x1, HEAP, lsl #32
    // 0xb833e4: CheckStackOverflow
    //     0xb833e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb833e8: cmp             SP, x16
    //     0xb833ec: b.ls            #0xb834cc
    // 0xb833f0: ldr             x0, [fp, #0x10]
    // 0xb833f4: r3 = LoadInt32Instr(r0)
    //     0xb833f4: sbfx            x3, x0, #1, #0x1f
    //     0xb833f8: tbz             w0, #0, #0xb83400
    //     0xb833fc: ldur            x3, [x0, #7]
    // 0xb83400: cbnz            x3, #0xb8342c
    // 0xb83404: LoadField: r0 = r1->field_f
    //     0xb83404: ldur            w0, [x1, #0xf]
    // 0xb83408: DecompressPointer r0
    //     0xb83408: add             x0, x0, HEAP, lsl #32
    // 0xb8340c: mov             x1, x0
    // 0xb83410: r2 = "Terkini"
    //     0xb83410: add             x2, PP, #0x40, lsl #12  ; [pp+0x40870] "Terkini"
    //     0xb83414: ldr             x2, [x2, #0x870]
    // 0xb83418: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb83418: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb8341c: r0 = buildTab()
    //     0xb8341c: bl              #0xb834d8  ; [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::buildTab
    // 0xb83420: LeaveFrame
    //     0xb83420: mov             SP, fp
    //     0xb83424: ldp             fp, lr, [SP], #0x10
    // 0xb83428: ret
    //     0xb83428: ret             
    // 0xb8342c: LoadField: r2 = r1->field_f
    //     0xb8342c: ldur            w2, [x1, #0xf]
    // 0xb83430: DecompressPointer r2
    //     0xb83430: add             x2, x2, HEAP, lsl #32
    // 0xb83434: LoadField: r4 = r2->field_b
    //     0xb83434: ldur            w4, [x2, #0xb]
    // 0xb83438: DecompressPointer r4
    //     0xb83438: add             x4, x4, HEAP, lsl #32
    // 0xb8343c: LoadField: r0 = r4->field_b
    //     0xb8343c: ldur            w0, [x4, #0xb]
    // 0xb83440: r1 = LoadInt32Instr(r0)
    //     0xb83440: sbfx            x1, x0, #1, #0x1f
    // 0xb83444: add             x0, x1, #1
    // 0xb83448: cmp             x3, x0
    // 0xb8344c: b.ne            #0xb83480
    // 0xb83450: LoadField: r0 = r2->field_1b
    //     0xb83450: ldur            w0, [x2, #0x1b]
    // 0xb83454: DecompressPointer r0
    //     0xb83454: add             x0, x0, HEAP, lsl #32
    // 0xb83458: str             x0, [SP]
    // 0xb8345c: mov             x1, x2
    // 0xb83460: r2 = "Kanal Lainnya"
    //     0xb83460: add             x2, PP, #0x40, lsl #12  ; [pp+0x40878] "Kanal Lainnya"
    //     0xb83464: ldr             x2, [x2, #0x878]
    // 0xb83468: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0xb83468: add             x4, PP, #0xe, lsl #12  ; [pp+0xe5a8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0xb8346c: ldr             x4, [x4, #0x5a8]
    // 0xb83470: r0 = buildTab()
    //     0xb83470: bl              #0xb834d8  ; [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::buildTab
    // 0xb83474: LeaveFrame
    //     0xb83474: mov             SP, fp
    //     0xb83478: ldp             fp, lr, [SP], #0x10
    // 0xb8347c: ret
    //     0xb8347c: ret             
    // 0xb83480: sub             x5, x3, #1
    // 0xb83484: mov             x0, x1
    // 0xb83488: mov             x1, x5
    // 0xb8348c: cmp             x1, x0
    // 0xb83490: b.hs            #0xb834d4
    // 0xb83494: LoadField: r0 = r4->field_f
    //     0xb83494: ldur            w0, [x4, #0xf]
    // 0xb83498: DecompressPointer r0
    //     0xb83498: add             x0, x0, HEAP, lsl #32
    // 0xb8349c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb8349c: add             x16, x0, x5, lsl #2
    //     0xb834a0: ldur            w1, [x16, #0xf]
    // 0xb834a4: DecompressPointer r1
    //     0xb834a4: add             x1, x1, HEAP, lsl #32
    // 0xb834a8: LoadField: r0 = r1->field_1b
    //     0xb834a8: ldur            w0, [x1, #0x1b]
    // 0xb834ac: DecompressPointer r0
    //     0xb834ac: add             x0, x0, HEAP, lsl #32
    // 0xb834b0: mov             x1, x2
    // 0xb834b4: mov             x2, x0
    // 0xb834b8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb834b8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb834bc: r0 = buildTab()
    //     0xb834bc: bl              #0xb834d8  ; [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::buildTab
    // 0xb834c0: LeaveFrame
    //     0xb834c0: mov             SP, fp
    //     0xb834c4: ldp             fp, lr, [SP], #0x10
    // 0xb834c8: ret
    //     0xb834c8: ret             
    // 0xb834cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb834cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb834d0: b               #0xb833f0
    // 0xb834d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb834d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ buildTab(/* No info */) {
    // ** addr: 0xb834d8, size: 0x1e8
    // 0xb834d8: EnterFrame
    //     0xb834d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb834dc: mov             fp, SP
    // 0xb834e0: AllocStack(0x40)
    //     0xb834e0: sub             SP, SP, #0x40
    // 0xb834e4: SetupParameters(ArticleChannelWidget this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, [dynamic _ = Null /* r0, fp-0x8 */])
    //     0xb834e4: stur            x1, [fp, #-0x10]
    //     0xb834e8: stur            x2, [fp, #-0x18]
    //     0xb834ec: stur            x3, [fp, #-0x20]
    //     0xb834f0: ldur            w0, [x4, #0x13]
    //     0xb834f4: sub             x4, x0, #6
    //     0xb834f8: cmp             w4, #2
    //     0xb834fc: b.lt            #0xb8350c
    //     0xb83500: add             x0, fp, w4, sxtw #2
    //     0xb83504: ldr             x0, [x0, #8]
    //     0xb83508: b               #0xb83510
    //     0xb8350c: mov             x0, NULL
    //     0xb83510: stur            x0, [fp, #-8]
    // 0xb83514: CheckStackOverflow
    //     0xb83514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb83518: cmp             SP, x16
    //     0xb8351c: b.ls            #0xb836b8
    // 0xb83520: r1 = 2
    //     0xb83520: movz            x1, #0x2
    // 0xb83524: r0 = AllocateContext()
    //     0xb83524: bl              #0xec126c  ; AllocateContextStub
    // 0xb83528: mov             x2, x0
    // 0xb8352c: ldur            x3, [fp, #-0x10]
    // 0xb83530: StoreField: r2->field_f = r3
    //     0xb83530: stur            w3, [x2, #0xf]
    // 0xb83534: ldur            x4, [fp, #-0x20]
    // 0xb83538: r0 = BoxInt64Instr(r4)
    //     0xb83538: sbfiz           x0, x4, #1, #0x1f
    //     0xb8353c: cmp             x4, x0, asr #1
    //     0xb83540: b.eq            #0xb8354c
    //     0xb83544: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb83548: stur            x4, [x0, #7]
    // 0xb8354c: StoreField: r2->field_13 = r0
    //     0xb8354c: stur            w0, [x2, #0x13]
    // 0xb83550: ldur            x0, [fp, #-8]
    // 0xb83554: cmp             w0, NULL
    // 0xb83558: b.ne            #0xb83570
    // 0xb8355c: r1 = Function '<anonymous closure>':.
    //     0xb8355c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40880] AnonymousClosure: (0xb836c0), in [package:nuonline/app/modules/article/widgets/article_channel_widget.dart] ArticleChannelWidget::buildTab (0xb834d8)
    //     0xb83560: ldr             x1, [x1, #0x880]
    // 0xb83564: r0 = AllocateClosure()
    //     0xb83564: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83568: mov             x1, x0
    // 0xb8356c: b               #0xb83574
    // 0xb83570: mov             x1, x0
    // 0xb83574: ldur            x0, [fp, #-0x10]
    // 0xb83578: stur            x1, [fp, #-8]
    // 0xb8357c: LoadField: r2 = r0->field_f
    //     0xb8357c: ldur            x2, [x0, #0xf]
    // 0xb83580: stur            x2, [fp, #-0x28]
    // 0xb83584: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb83584: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb83588: ldr             x0, [x0, #0x2670]
    //     0xb8358c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb83590: cmp             w0, w16
    //     0xb83594: b.ne            #0xb835a0
    //     0xb83598: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb8359c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb835a0: r0 = GetNavigation.textTheme()
    //     0xb835a0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb835a4: LoadField: r1 = r0->field_2f
    //     0xb835a4: ldur            w1, [x0, #0x2f]
    // 0xb835a8: DecompressPointer r1
    //     0xb835a8: add             x1, x1, HEAP, lsl #32
    // 0xb835ac: stur            x1, [fp, #-0x10]
    // 0xb835b0: cmp             w1, NULL
    // 0xb835b4: b.ne            #0xb835c0
    // 0xb835b8: r2 = Null
    //     0xb835b8: mov             x2, NULL
    // 0xb835bc: b               #0xb835f4
    // 0xb835c0: r0 = GetNavigation.theme()
    //     0xb835c0: bl              #0x624dd4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.theme
    // 0xb835c4: LoadField: r1 = r0->field_3f
    //     0xb835c4: ldur            w1, [x0, #0x3f]
    // 0xb835c8: DecompressPointer r1
    //     0xb835c8: add             x1, x1, HEAP, lsl #32
    // 0xb835cc: LoadField: r0 = r1->field_2b
    //     0xb835cc: ldur            w0, [x1, #0x2b]
    // 0xb835d0: DecompressPointer r0
    //     0xb835d0: add             x0, x0, HEAP, lsl #32
    // 0xb835d4: r16 = Instance_FontWeight
    //     0xb835d4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb835d8: ldr             x16, [x16, #0xe20]
    // 0xb835dc: stp             x0, x16, [SP]
    // 0xb835e0: ldur            x1, [fp, #-0x10]
    // 0xb835e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontWeight, 0x1, null]
    //     0xb835e4: add             x4, PP, #0x27, lsl #12  ; [pp+0x27ff8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontWeight", 0x1, Null]
    //     0xb835e8: ldr             x4, [x4, #0xff8]
    // 0xb835ec: r0 = copyWith()
    //     0xb835ec: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb835f0: mov             x2, x0
    // 0xb835f4: ldur            x1, [fp, #-0x20]
    // 0xb835f8: ldur            x0, [fp, #-0x28]
    // 0xb835fc: stur            x2, [fp, #-0x10]
    // 0xb83600: r0 = GetNavigation.textTheme()
    //     0xb83600: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb83604: LoadField: r1 = r0->field_2f
    //     0xb83604: ldur            w1, [x0, #0x2f]
    // 0xb83608: DecompressPointer r1
    //     0xb83608: add             x1, x1, HEAP, lsl #32
    // 0xb8360c: ldur            x2, [fp, #-0x20]
    // 0xb83610: ldur            x0, [fp, #-0x28]
    // 0xb83614: cmp             x2, x0
    // 0xb83618: b.ne            #0xb83624
    // 0xb8361c: ldur            x2, [fp, #-0x10]
    // 0xb83620: b               #0xb83628
    // 0xb83624: mov             x2, x1
    // 0xb83628: ldur            x1, [fp, #-0x18]
    // 0xb8362c: ldur            x0, [fp, #-8]
    // 0xb83630: stur            x2, [fp, #-0x10]
    // 0xb83634: r0 = Text()
    //     0xb83634: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb83638: mov             x1, x0
    // 0xb8363c: ldur            x0, [fp, #-0x18]
    // 0xb83640: stur            x1, [fp, #-0x30]
    // 0xb83644: StoreField: r1->field_b = r0
    //     0xb83644: stur            w0, [x1, #0xb]
    // 0xb83648: ldur            x0, [fp, #-0x10]
    // 0xb8364c: StoreField: r1->field_13 = r0
    //     0xb8364c: stur            w0, [x1, #0x13]
    // 0xb83650: r0 = Center()
    //     0xb83650: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb83654: mov             x1, x0
    // 0xb83658: r0 = Instance_Alignment
    //     0xb83658: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xb8365c: ldr             x0, [x0, #0x898]
    // 0xb83660: stur            x1, [fp, #-0x10]
    // 0xb83664: StoreField: r1->field_f = r0
    //     0xb83664: stur            w0, [x1, #0xf]
    // 0xb83668: ldur            x0, [fp, #-0x30]
    // 0xb8366c: StoreField: r1->field_b = r0
    //     0xb8366c: stur            w0, [x1, #0xb]
    // 0xb83670: r0 = InkWell()
    //     0xb83670: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb83674: ldur            x1, [fp, #-0x10]
    // 0xb83678: StoreField: r0->field_b = r1
    //     0xb83678: stur            w1, [x0, #0xb]
    // 0xb8367c: ldur            x1, [fp, #-8]
    // 0xb83680: StoreField: r0->field_f = r1
    //     0xb83680: stur            w1, [x0, #0xf]
    // 0xb83684: r1 = true
    //     0xb83684: add             x1, NULL, #0x20  ; true
    // 0xb83688: StoreField: r0->field_43 = r1
    //     0xb83688: stur            w1, [x0, #0x43]
    // 0xb8368c: r2 = Instance_BoxShape
    //     0xb8368c: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb83690: ldr             x2, [x2, #0xca8]
    // 0xb83694: StoreField: r0->field_47 = r2
    //     0xb83694: stur            w2, [x0, #0x47]
    // 0xb83698: StoreField: r0->field_6f = r1
    //     0xb83698: stur            w1, [x0, #0x6f]
    // 0xb8369c: r2 = false
    //     0xb8369c: add             x2, NULL, #0x30  ; false
    // 0xb836a0: StoreField: r0->field_73 = r2
    //     0xb836a0: stur            w2, [x0, #0x73]
    // 0xb836a4: StoreField: r0->field_83 = r1
    //     0xb836a4: stur            w1, [x0, #0x83]
    // 0xb836a8: StoreField: r0->field_7b = r2
    //     0xb836a8: stur            w2, [x0, #0x7b]
    // 0xb836ac: LeaveFrame
    //     0xb836ac: mov             SP, fp
    //     0xb836b0: ldp             fp, lr, [SP], #0x10
    // 0xb836b4: ret
    //     0xb836b4: ret             
    // 0xb836b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb836b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb836bc: b               #0xb83520
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb836c0, size: 0x68
    // 0xb836c0: EnterFrame
    //     0xb836c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb836c4: mov             fp, SP
    // 0xb836c8: AllocStack(0x8)
    //     0xb836c8: sub             SP, SP, #8
    // 0xb836cc: SetupParameters()
    //     0xb836cc: ldr             x0, [fp, #0x10]
    //     0xb836d0: ldur            w1, [x0, #0x17]
    //     0xb836d4: add             x1, x1, HEAP, lsl #32
    // 0xb836d8: CheckStackOverflow
    //     0xb836d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb836dc: cmp             SP, x16
    //     0xb836e0: b.ls            #0xb83720
    // 0xb836e4: LoadField: r0 = r1->field_13
    //     0xb836e4: ldur            w0, [x1, #0x13]
    // 0xb836e8: DecompressPointer r0
    //     0xb836e8: add             x0, x0, HEAP, lsl #32
    // 0xb836ec: LoadField: r2 = r1->field_f
    //     0xb836ec: ldur            w2, [x1, #0xf]
    // 0xb836f0: DecompressPointer r2
    //     0xb836f0: add             x2, x2, HEAP, lsl #32
    // 0xb836f4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb836f4: ldur            w1, [x2, #0x17]
    // 0xb836f8: DecompressPointer r1
    //     0xb836f8: add             x1, x1, HEAP, lsl #32
    // 0xb836fc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb836fc: ldur            w2, [x1, #0x17]
    // 0xb83700: DecompressPointer r2
    //     0xb83700: add             x2, x2, HEAP, lsl #32
    // 0xb83704: str             x0, [SP]
    // 0xb83708: mov             x1, x2
    // 0xb8370c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb8370c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb83710: r0 = call()
    //     0xb83710: bl              #0x8a9554  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::call
    // 0xb83714: LeaveFrame
    //     0xb83714: mov             SP, fp
    //     0xb83718: ldp             fp, lr, [SP], #0x10
    // 0xb8371c: ret
    //     0xb8371c: ret             
    // 0xb83720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb83720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb83724: b               #0xb836e4
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb83728, size: 0xc
    // 0xb83728: r0 = Instance_SizedBox
    //     0xb83728: add             x0, PP, #0x38, lsl #12  ; [pp+0x38058] Obj!SizedBox@e1e3e1
    //     0xb8372c: ldr             x0, [x0, #0x58]
    // 0xb83730: ret
    //     0xb83730: ret             
  }
}
