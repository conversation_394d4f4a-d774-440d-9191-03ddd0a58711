// lib: , url: package:nuonline/app/modules/article/widgets/article_content_html.dart

// class id: 1050152, size: 0x8
class :: {

  static _ _extension#0.insertAll(/* No info */) {
    // ** addr: 0xb84164, size: 0x898
    // 0xb84164: EnterFrame
    //     0xb84164: stp             fp, lr, [SP, #-0x10]!
    //     0xb84168: mov             fp, SP
    // 0xb8416c: AllocStack(0x78)
    //     0xb8416c: sub             SP, SP, #0x78
    // 0xb84170: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0xb84170: mov             x0, x3
    //     0xb84174: stur            x3, [fp, #-0x10]
    //     0xb84178: mov             x3, x5
    //     0xb8417c: stur            x2, [fp, #-8]
    //     0xb84180: stur            x5, [fp, #-0x18]
    // 0xb84184: CheckStackOverflow
    //     0xb84184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84188: cmp             SP, x16
    //     0xb8418c: b.ls            #0xb849c8
    // 0xb84190: r0 = parse()
    //     0xb84190: bl              #0x9562e4  ; [package:html/parser.dart] ::parse
    // 0xb84194: mov             x1, x0
    // 0xb84198: r2 = "p"
    //     0xb84198: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cac0] "p"
    //     0xb8419c: ldr             x2, [x2, #0xac0]
    // 0xb841a0: stur            x0, [fp, #-0x20]
    // 0xb841a4: r0 = querySelectorAll()
    //     0xb841a4: bl              #0x9d099c  ; [package:html/src/query_selector.dart] ::querySelectorAll
    // 0xb841a8: ldur            x1, [fp, #-0x20]
    // 0xb841ac: r2 = "img"
    //     0xb841ac: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f60] "img"
    //     0xb841b0: ldr             x2, [x2, #0xf60]
    // 0xb841b4: stur            x0, [fp, #-0x28]
    // 0xb841b8: r0 = querySelectorAll()
    //     0xb841b8: bl              #0x9d099c  ; [package:html/src/query_selector.dart] ::querySelectorAll
    // 0xb841bc: stur            x0, [fp, #-0x40]
    // 0xb841c0: LoadField: r1 = r0->field_b
    //     0xb841c0: ldur            w1, [x0, #0xb]
    // 0xb841c4: r3 = LoadInt32Instr(r1)
    //     0xb841c4: sbfx            x3, x1, #1, #0x1f
    // 0xb841c8: stur            x3, [fp, #-0x38]
    // 0xb841cc: r1 = 0
    //     0xb841cc: movz            x1, #0
    // 0xb841d0: CheckStackOverflow
    //     0xb841d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb841d4: cmp             SP, x16
    //     0xb841d8: b.ls            #0xb849d0
    // 0xb841dc: LoadField: r2 = r0->field_b
    //     0xb841dc: ldur            w2, [x0, #0xb]
    // 0xb841e0: r4 = LoadInt32Instr(r2)
    //     0xb841e0: sbfx            x4, x2, #1, #0x1f
    // 0xb841e4: cmp             x3, x4
    // 0xb841e8: b.ne            #0xb849ac
    // 0xb841ec: cmp             x1, x4
    // 0xb841f0: b.ge            #0xb84234
    // 0xb841f4: LoadField: r2 = r0->field_f
    //     0xb841f4: ldur            w2, [x0, #0xf]
    // 0xb841f8: DecompressPointer r2
    //     0xb841f8: add             x2, x2, HEAP, lsl #32
    // 0xb841fc: ArrayLoad: r4 = r2[r1]  ; Unknown_4
    //     0xb841fc: add             x16, x2, x1, lsl #2
    //     0xb84200: ldur            w4, [x16, #0xf]
    // 0xb84204: DecompressPointer r4
    //     0xb84204: add             x4, x4, HEAP, lsl #32
    // 0xb84208: add             x5, x1, #1
    // 0xb8420c: stur            x5, [fp, #-0x30]
    // 0xb84210: LoadField: r1 = r4->field_b
    //     0xb84210: ldur            w1, [x4, #0xb]
    // 0xb84214: DecompressPointer r1
    //     0xb84214: add             x1, x1, HEAP, lsl #32
    // 0xb84218: r2 = "style"
    //     0xb84218: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb8421c: ldr             x2, [x2, #0x9b8]
    // 0xb84220: r0 = remove()
    //     0xb84220: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xb84224: ldur            x1, [fp, #-0x30]
    // 0xb84228: ldur            x0, [fp, #-0x40]
    // 0xb8422c: ldur            x3, [fp, #-0x38]
    // 0xb84230: b               #0xb841d0
    // 0xb84234: ldur            x0, [fp, #-0x28]
    // 0xb84238: LoadField: r1 = r0->field_b
    //     0xb84238: ldur            w1, [x0, #0xb]
    // 0xb8423c: r2 = LoadInt32Instr(r1)
    //     0xb8423c: sbfx            x2, x1, #1, #0x1f
    // 0xb84240: cmp             x2, #3
    // 0xb84244: b.le            #0xb84250
    // 0xb84248: r2 = 3
    //     0xb84248: movz            x2, #0x3
    // 0xb8424c: b               #0xb84258
    // 0xb84250: sub             x1, x2, #1
    // 0xb84254: mov             x2, x1
    // 0xb84258: ldur            x1, [fp, #-0x20]
    // 0xb8425c: stur            x2, [fp, #-0x30]
    // 0xb84260: r0 = createElement()
    //     0xb84260: bl              #0xb84a44  ; [package:html/dom.dart] Document::createElement
    // 0xb84264: mov             x1, x0
    // 0xb84268: r2 = "donation_support"
    //     0xb84268: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f68] "donation_support"
    //     0xb8426c: ldr             x2, [x2, #0xf68]
    // 0xb84270: stur            x0, [fp, #-0x48]
    // 0xb84274: r0 = id=()
    //     0xb84274: bl              #0xb849fc  ; [package:html/dom.dart] Element::id=
    // 0xb84278: ldur            x2, [fp, #-0x48]
    // 0xb8427c: LoadField: r1 = r2->field_b
    //     0xb8427c: ldur            w1, [x2, #0xb]
    // 0xb84280: DecompressPointer r1
    //     0xb84280: add             x1, x1, HEAP, lsl #32
    // 0xb84284: ldur            x0, [fp, #-0x30]
    // 0xb84288: stur            x1, [fp, #-0x50]
    // 0xb8428c: lsl             x3, x0, #1
    // 0xb84290: str             x3, [SP]
    // 0xb84294: r0 = _interpolateSingle()
    //     0xb84294: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb84298: ldur            x1, [fp, #-0x50]
    // 0xb8429c: mov             x3, x0
    // 0xb842a0: r2 = "index"
    //     0xb842a0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb842a4: ldr             x2, [x2, #0x878]
    // 0xb842a8: r0 = []=()
    //     0xb842a8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb842ac: ldur            x4, [fp, #-0x28]
    // 0xb842b0: LoadField: r0 = r4->field_b
    //     0xb842b0: ldur            w0, [x4, #0xb]
    // 0xb842b4: r1 = LoadInt32Instr(r0)
    //     0xb842b4: sbfx            x1, x0, #1, #0x1f
    // 0xb842b8: cbz             x1, #0xb842f4
    // 0xb842bc: ldur            x2, [fp, #-0x30]
    // 0xb842c0: mov             x0, x1
    // 0xb842c4: mov             x1, x2
    // 0xb842c8: cmp             x1, x0
    // 0xb842cc: b.hs            #0xb849d8
    // 0xb842d0: LoadField: r0 = r4->field_f
    //     0xb842d0: ldur            w0, [x4, #0xf]
    // 0xb842d4: DecompressPointer r0
    //     0xb842d4: add             x0, x0, HEAP, lsl #32
    // 0xb842d8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb842d8: add             x16, x0, x2, lsl #2
    //     0xb842dc: ldur            w1, [x16, #0xf]
    // 0xb842e0: DecompressPointer r1
    //     0xb842e0: add             x1, x1, HEAP, lsl #32
    // 0xb842e4: ldur            x2, [fp, #-0x48]
    // 0xb842e8: r3 = Null
    //     0xb842e8: mov             x3, NULL
    // 0xb842ec: r0 = insertBefore()
    //     0xb842ec: bl              #0x896dc8  ; [package:html/dom.dart] Node::insertBefore
    // 0xb842f0: b               #0xb8431c
    // 0xb842f4: ldur            x1, [fp, #-0x20]
    // 0xb842f8: r2 = "body"
    //     0xb842f8: add             x2, PP, #8, lsl #12  ; [pp+0x8750] "body"
    //     0xb842fc: ldr             x2, [x2, #0x750]
    // 0xb84300: r0 = querySelectorAll()
    //     0xb84300: bl              #0x9d099c  ; [package:html/src/query_selector.dart] ::querySelectorAll
    // 0xb84304: mov             x1, x0
    // 0xb84308: r0 = first()
    //     0xb84308: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xb8430c: mov             x1, x0
    // 0xb84310: ldur            x2, [fp, #-0x48]
    // 0xb84314: r3 = Null
    //     0xb84314: mov             x3, NULL
    // 0xb84318: r0 = insertBefore()
    //     0xb84318: bl              #0x896dc8  ; [package:html/dom.dart] Node::insertBefore
    // 0xb8431c: ldur            x2, [fp, #-8]
    // 0xb84320: r0 = LoadClassIdInstr(r2)
    //     0xb84320: ldur            x0, [x2, #-1]
    //     0xb84324: ubfx            x0, x0, #0xc, #0x14
    // 0xb84328: mov             x1, x2
    // 0xb8432c: r0 = GDT[cid_x0 + 0xe879]()
    //     0xb8432c: movz            x17, #0xe879
    //     0xb84330: add             lr, x0, x17
    //     0xb84334: ldr             lr, [x21, lr, lsl #3]
    //     0xb84338: blr             lr
    // 0xb8433c: tbnz            w0, #4, #0xb84378
    // 0xb84340: r0 = StringBuffer()
    //     0xb84340: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xb84344: mov             x1, x0
    // 0xb84348: stur            x0, [fp, #-0x48]
    // 0xb8434c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8434c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb84350: r0 = StringBuffer()
    //     0xb84350: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xb84354: ldur            x1, [fp, #-0x20]
    // 0xb84358: ldur            x2, [fp, #-0x48]
    // 0xb8435c: r0 = _addInnerHtml()
    //     0xb8435c: bl              #0x9d0db4  ; [package:html/dom.dart] Node::_addInnerHtml
    // 0xb84360: ldur            x16, [fp, #-0x48]
    // 0xb84364: str             x16, [SP]
    // 0xb84368: r0 = toString()
    //     0xb84368: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xb8436c: LeaveFrame
    //     0xb8436c: mov             SP, fp
    //     0xb84370: ldp             fp, lr, [SP], #0x10
    // 0xb84374: ret
    //     0xb84374: ret             
    // 0xb84378: r3 = 0
    //     0xb84378: movz            x3, #0
    // 0xb8437c: ldur            x1, [fp, #-8]
    // 0xb84380: ldur            x2, [fp, #-0x28]
    // 0xb84384: stur            x3, [fp, #-0x30]
    // 0xb84388: CheckStackOverflow
    //     0xb84388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8438c: cmp             SP, x16
    //     0xb84390: b.ls            #0xb849dc
    // 0xb84394: r0 = LoadClassIdInstr(r1)
    //     0xb84394: ldur            x0, [x1, #-1]
    //     0xb84398: ubfx            x0, x0, #0xc, #0x14
    // 0xb8439c: str             x1, [SP]
    // 0xb843a0: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb843a0: movz            x17, #0xc834
    //     0xb843a4: add             lr, x0, x17
    //     0xb843a8: ldr             lr, [x21, lr, lsl #3]
    //     0xb843ac: blr             lr
    // 0xb843b0: r1 = LoadInt32Instr(r0)
    //     0xb843b0: sbfx            x1, x0, #1, #0x1f
    //     0xb843b4: tbz             w0, #0, #0xb843bc
    //     0xb843b8: ldur            x1, [x0, #7]
    // 0xb843bc: ldur            x0, [fp, #-0x30]
    // 0xb843c0: cmp             x0, x1
    // 0xb843c4: b.ge            #0xb84650
    // 0xb843c8: ldur            x1, [fp, #-8]
    // 0xb843cc: ldur            x2, [fp, #-0x28]
    // 0xb843d0: r0 = Element()
    //     0xb843d0: bl              #0x896620  ; AllocateElementStub -> Element (size=0x20)
    // 0xb843d4: mov             x2, x0
    // 0xb843d8: r0 = "div"
    //     0xb843d8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36f70] "div"
    //     0xb843dc: ldr             x0, [x0, #0xf70]
    // 0xb843e0: stur            x2, [fp, #-0x48]
    // 0xb843e4: StoreField: r2->field_1b = r0
    //     0xb843e4: stur            w0, [x2, #0x1b]
    // 0xb843e8: r3 = "http://www.w3.org/1999/xhtml"
    //     0xb843e8: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f78] "http://www.w3.org/1999/xhtml"
    //     0xb843ec: ldr             x3, [x3, #0xf78]
    // 0xb843f0: ArrayStore: r2[0] = r3  ; List_4
    //     0xb843f0: stur            w3, [x2, #0x17]
    // 0xb843f4: mov             x1, x2
    // 0xb843f8: r0 = _Document&Node&_ParentNode._()
    //     0xb843f8: bl              #0x9572fc  ; [package:html/dom.dart] _Document&Node&_ParentNode::_Document&Node&_ParentNode._
    // 0xb843fc: ldur            x0, [fp, #-0x48]
    // 0xb84400: LoadField: r1 = r0->field_b
    //     0xb84400: ldur            w1, [x0, #0xb]
    // 0xb84404: DecompressPointer r1
    //     0xb84404: add             x1, x1, HEAP, lsl #32
    // 0xb84408: r2 = "id"
    //     0xb84408: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb8440c: ldr             x2, [x2, #0x740]
    // 0xb84410: r3 = "insertion"
    //     0xb84410: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f80] "insertion"
    //     0xb84414: ldr             x3, [x3, #0xf80]
    // 0xb84418: r0 = []=()
    //     0xb84418: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb8441c: ldur            x2, [fp, #-0x48]
    // 0xb84420: LoadField: r3 = r2->field_b
    //     0xb84420: ldur            w3, [x2, #0xb]
    // 0xb84424: DecompressPointer r3
    //     0xb84424: add             x3, x3, HEAP, lsl #32
    // 0xb84428: ldur            x4, [fp, #-0x30]
    // 0xb8442c: stur            x3, [fp, #-0x58]
    // 0xb84430: r0 = BoxInt64Instr(r4)
    //     0xb84430: sbfiz           x0, x4, #1, #0x1f
    //     0xb84434: cmp             x4, x0, asr #1
    //     0xb84438: b.eq            #0xb84444
    //     0xb8443c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb84440: stur            x4, [x0, #7]
    // 0xb84444: mov             x1, x0
    // 0xb84448: stur            x1, [fp, #-0x50]
    // 0xb8444c: r0 = 60
    //     0xb8444c: movz            x0, #0x3c
    // 0xb84450: branchIfSmi(r1, 0xb8445c)
    //     0xb84450: tbz             w1, #0, #0xb8445c
    // 0xb84454: r0 = LoadClassIdInstr(r1)
    //     0xb84454: ldur            x0, [x1, #-1]
    //     0xb84458: ubfx            x0, x0, #0xc, #0x14
    // 0xb8445c: str             x1, [SP]
    // 0xb84460: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb84460: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb84464: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb84464: movz            x17, #0x2b03
    //     0xb84468: add             lr, x0, x17
    //     0xb8446c: ldr             lr, [x21, lr, lsl #3]
    //     0xb84470: blr             lr
    // 0xb84474: mov             x4, x0
    // 0xb84478: ldur            x3, [fp, #-0x58]
    // 0xb8447c: stur            x4, [fp, #-0x68]
    // 0xb84480: LoadField: r5 = r3->field_7
    //     0xb84480: ldur            w5, [x3, #7]
    // 0xb84484: DecompressPointer r5
    //     0xb84484: add             x5, x5, HEAP, lsl #32
    // 0xb84488: mov             x2, x5
    // 0xb8448c: stur            x5, [fp, #-0x60]
    // 0xb84490: r0 = "index"
    //     0xb84490: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb84494: ldr             x0, [x0, #0x878]
    // 0xb84498: r1 = Null
    //     0xb84498: mov             x1, NULL
    // 0xb8449c: cmp             w2, NULL
    // 0xb844a0: b.eq            #0xb844c0
    // 0xb844a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb844a4: ldur            w4, [x2, #0x17]
    // 0xb844a8: DecompressPointer r4
    //     0xb844a8: add             x4, x4, HEAP, lsl #32
    // 0xb844ac: r8 = X0
    //     0xb844ac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb844b0: LoadField: r9 = r4->field_7
    //     0xb844b0: ldur            x9, [x4, #7]
    // 0xb844b4: r3 = Null
    //     0xb844b4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f88] Null
    //     0xb844b8: ldr             x3, [x3, #0xf88]
    // 0xb844bc: blr             x9
    // 0xb844c0: ldur            x0, [fp, #-0x68]
    // 0xb844c4: ldur            x2, [fp, #-0x60]
    // 0xb844c8: r1 = Null
    //     0xb844c8: mov             x1, NULL
    // 0xb844cc: cmp             w2, NULL
    // 0xb844d0: b.eq            #0xb844f0
    // 0xb844d4: LoadField: r4 = r2->field_1b
    //     0xb844d4: ldur            w4, [x2, #0x1b]
    // 0xb844d8: DecompressPointer r4
    //     0xb844d8: add             x4, x4, HEAP, lsl #32
    // 0xb844dc: r8 = X1
    //     0xb844dc: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xb844e0: LoadField: r9 = r4->field_7
    //     0xb844e0: ldur            x9, [x4, #7]
    // 0xb844e4: r3 = Null
    //     0xb844e4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f98] Null
    //     0xb844e8: ldr             x3, [x3, #0xf98]
    // 0xb844ec: blr             x9
    // 0xb844f0: ldur            x1, [fp, #-0x58]
    // 0xb844f4: r2 = "index"
    //     0xb844f4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb844f8: ldr             x2, [x2, #0x878]
    // 0xb844fc: r0 = _hashCode()
    //     0xb844fc: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xb84500: ldur            x1, [fp, #-0x58]
    // 0xb84504: ldur            x3, [fp, #-0x68]
    // 0xb84508: mov             x5, x0
    // 0xb8450c: r2 = "index"
    //     0xb8450c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb84510: ldr             x2, [x2, #0x878]
    // 0xb84514: r0 = _set()
    //     0xb84514: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xb84518: ldur            x1, [fp, #-8]
    // 0xb8451c: r0 = LoadClassIdInstr(r1)
    //     0xb8451c: ldur            x0, [x1, #-1]
    //     0xb84520: ubfx            x0, x0, #0xc, #0x14
    // 0xb84524: ldur            x16, [fp, #-0x50]
    // 0xb84528: stp             x16, x1, [SP]
    // 0xb8452c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8452c: movz            x17, #0x3037
    //     0xb84530: movk            x17, #0x1, lsl #16
    //     0xb84534: add             lr, x0, x17
    //     0xb84538: ldr             lr, [x21, lr, lsl #3]
    //     0xb8453c: blr             lr
    // 0xb84540: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xb84540: ldur            x1, [x0, #0x17]
    // 0xb84544: ldur            x2, [fp, #-0x28]
    // 0xb84548: LoadField: r0 = r2->field_b
    //     0xb84548: ldur            w0, [x2, #0xb]
    // 0xb8454c: r3 = LoadInt32Instr(r0)
    //     0xb8454c: sbfx            x3, x0, #1, #0x1f
    // 0xb84550: cmp             x1, x3
    // 0xb84554: b.lt            #0xb845b8
    // 0xb84558: cmp             x3, #0
    // 0xb8455c: b.le            #0xb84994
    // 0xb84560: sub             x4, x3, #1
    // 0xb84564: mov             x0, x3
    // 0xb84568: mov             x1, x4
    // 0xb8456c: cmp             x1, x0
    // 0xb84570: b.hs            #0xb849e4
    // 0xb84574: LoadField: r0 = r2->field_f
    //     0xb84574: ldur            w0, [x2, #0xf]
    // 0xb84578: DecompressPointer r0
    //     0xb84578: add             x0, x0, HEAP, lsl #32
    // 0xb8457c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb8457c: add             x16, x0, x4, lsl #2
    //     0xb84580: ldur            w1, [x16, #0xf]
    // 0xb84584: DecompressPointer r1
    //     0xb84584: add             x1, x1, HEAP, lsl #32
    // 0xb84588: LoadField: r0 = r1->field_f
    //     0xb84588: ldur            w0, [x1, #0xf]
    // 0xb8458c: DecompressPointer r0
    //     0xb8458c: add             x0, x0, HEAP, lsl #32
    // 0xb84590: r16 = Sentinel
    //     0xb84590: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb84594: cmp             w0, w16
    // 0xb84598: b.ne            #0xb845a8
    // 0xb8459c: r2 = nodes
    //     0xb8459c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fa8] Field <Node.nodes>: late final (offset: 0x10)
    //     0xb845a0: ldr             x2, [x2, #0xfa8]
    // 0xb845a4: r0 = InitLateFinalInstanceField()
    //     0xb845a4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb845a8: ldur            x16, [fp, #-0x48]
    // 0xb845ac: stp             x16, x0, [SP]
    // 0xb845b0: r0 = add()
    //     0xb845b0: bl              #0x66b6fc  ; [package:html/dom.dart] NodeList::add
    // 0xb845b4: b               #0xb84644
    // 0xb845b8: mov             x1, x2
    // 0xb845bc: ldur            x2, [fp, #-8]
    // 0xb845c0: r0 = LoadClassIdInstr(r2)
    //     0xb845c0: ldur            x0, [x2, #-1]
    //     0xb845c4: ubfx            x0, x0, #0xc, #0x14
    // 0xb845c8: ldur            x16, [fp, #-0x50]
    // 0xb845cc: stp             x16, x2, [SP]
    // 0xb845d0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb845d0: movz            x17, #0x3037
    //     0xb845d4: movk            x17, #0x1, lsl #16
    //     0xb845d8: add             lr, x0, x17
    //     0xb845dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb845e0: blr             lr
    // 0xb845e4: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xb845e4: ldur            x2, [x0, #0x17]
    // 0xb845e8: ldur            x3, [fp, #-0x28]
    // 0xb845ec: LoadField: r0 = r3->field_b
    //     0xb845ec: ldur            w0, [x3, #0xb]
    // 0xb845f0: r1 = LoadInt32Instr(r0)
    //     0xb845f0: sbfx            x1, x0, #1, #0x1f
    // 0xb845f4: mov             x0, x1
    // 0xb845f8: mov             x1, x2
    // 0xb845fc: cmp             x1, x0
    // 0xb84600: b.hs            #0xb849e8
    // 0xb84604: LoadField: r0 = r3->field_f
    //     0xb84604: ldur            w0, [x3, #0xf]
    // 0xb84608: DecompressPointer r0
    //     0xb84608: add             x0, x0, HEAP, lsl #32
    // 0xb8460c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb8460c: add             x16, x0, x2, lsl #2
    //     0xb84610: ldur            w1, [x16, #0xf]
    // 0xb84614: DecompressPointer r1
    //     0xb84614: add             x1, x1, HEAP, lsl #32
    // 0xb84618: LoadField: r0 = r1->field_f
    //     0xb84618: ldur            w0, [x1, #0xf]
    // 0xb8461c: DecompressPointer r0
    //     0xb8461c: add             x0, x0, HEAP, lsl #32
    // 0xb84620: r16 = Sentinel
    //     0xb84620: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb84624: cmp             w0, w16
    // 0xb84628: b.ne            #0xb84638
    // 0xb8462c: r2 = nodes
    //     0xb8462c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fa8] Field <Node.nodes>: late final (offset: 0x10)
    //     0xb84630: ldr             x2, [x2, #0xfa8]
    // 0xb84634: r0 = InitLateFinalInstanceField()
    //     0xb84634: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb84638: ldur            x16, [fp, #-0x48]
    // 0xb8463c: stp             x16, x0, [SP]
    // 0xb84640: r0 = add()
    //     0xb84640: bl              #0x66b6fc  ; [package:html/dom.dart] NodeList::add
    // 0xb84644: ldur            x0, [fp, #-0x30]
    // 0xb84648: add             x3, x0, #1
    // 0xb8464c: b               #0xb8437c
    // 0xb84650: r4 = 0
    //     0xb84650: movz            x4, #0
    // 0xb84654: ldur            x2, [fp, #-8]
    // 0xb84658: ldur            x3, [fp, #-0x10]
    // 0xb8465c: ldur            x1, [fp, #-0x28]
    // 0xb84660: stur            x4, [fp, #-0x30]
    // 0xb84664: CheckStackOverflow
    //     0xb84664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84668: cmp             SP, x16
    //     0xb8466c: b.ls            #0xb849ec
    // 0xb84670: r0 = LoadClassIdInstr(r3)
    //     0xb84670: ldur            x0, [x3, #-1]
    //     0xb84674: ubfx            x0, x0, #0xc, #0x14
    // 0xb84678: str             x3, [SP]
    // 0xb8467c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb8467c: movz            x17, #0xc834
    //     0xb84680: add             lr, x0, x17
    //     0xb84684: ldr             lr, [x21, lr, lsl #3]
    //     0xb84688: blr             lr
    // 0xb8468c: r1 = LoadInt32Instr(r0)
    //     0xb8468c: sbfx            x1, x0, #1, #0x1f
    //     0xb84690: tbz             w0, #0, #0xb84698
    //     0xb84694: ldur            x1, [x0, #7]
    // 0xb84698: ldur            x0, [fp, #-0x30]
    // 0xb8469c: cmp             x0, x1
    // 0xb846a0: b.ge            #0xb8492c
    // 0xb846a4: ldur            x2, [fp, #-8]
    // 0xb846a8: ldur            x1, [fp, #-0x28]
    // 0xb846ac: r0 = Element()
    //     0xb846ac: bl              #0x896620  ; AllocateElementStub -> Element (size=0x20)
    // 0xb846b0: mov             x2, x0
    // 0xb846b4: r0 = "div"
    //     0xb846b4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36f70] "div"
    //     0xb846b8: ldr             x0, [x0, #0xf70]
    // 0xb846bc: stur            x2, [fp, #-0x48]
    // 0xb846c0: StoreField: r2->field_1b = r0
    //     0xb846c0: stur            w0, [x2, #0x1b]
    // 0xb846c4: r3 = "http://www.w3.org/1999/xhtml"
    //     0xb846c4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f78] "http://www.w3.org/1999/xhtml"
    //     0xb846c8: ldr             x3, [x3, #0xf78]
    // 0xb846cc: ArrayStore: r2[0] = r3  ; List_4
    //     0xb846cc: stur            w3, [x2, #0x17]
    // 0xb846d0: mov             x1, x2
    // 0xb846d4: r0 = _Document&Node&_ParentNode._()
    //     0xb846d4: bl              #0x9572fc  ; [package:html/dom.dart] _Document&Node&_ParentNode::_Document&Node&_ParentNode._
    // 0xb846d8: ldur            x0, [fp, #-0x48]
    // 0xb846dc: LoadField: r1 = r0->field_b
    //     0xb846dc: ldur            w1, [x0, #0xb]
    // 0xb846e0: DecompressPointer r1
    //     0xb846e0: add             x1, x1, HEAP, lsl #32
    // 0xb846e4: r2 = "id"
    //     0xb846e4: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb846e8: ldr             x2, [x2, #0x740]
    // 0xb846ec: r3 = "insertTopic"
    //     0xb846ec: add             x3, PP, #0x36, lsl #12  ; [pp+0x36fb0] "insertTopic"
    //     0xb846f0: ldr             x3, [x3, #0xfb0]
    // 0xb846f4: r0 = []=()
    //     0xb846f4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb846f8: ldur            x2, [fp, #-0x48]
    // 0xb846fc: LoadField: r3 = r2->field_b
    //     0xb846fc: ldur            w3, [x2, #0xb]
    // 0xb84700: DecompressPointer r3
    //     0xb84700: add             x3, x3, HEAP, lsl #32
    // 0xb84704: ldur            x4, [fp, #-0x30]
    // 0xb84708: stur            x3, [fp, #-0x58]
    // 0xb8470c: r0 = BoxInt64Instr(r4)
    //     0xb8470c: sbfiz           x0, x4, #1, #0x1f
    //     0xb84710: cmp             x4, x0, asr #1
    //     0xb84714: b.eq            #0xb84720
    //     0xb84718: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8471c: stur            x4, [x0, #7]
    // 0xb84720: mov             x1, x0
    // 0xb84724: stur            x1, [fp, #-0x50]
    // 0xb84728: r0 = 60
    //     0xb84728: movz            x0, #0x3c
    // 0xb8472c: branchIfSmi(r1, 0xb84738)
    //     0xb8472c: tbz             w1, #0, #0xb84738
    // 0xb84730: r0 = LoadClassIdInstr(r1)
    //     0xb84730: ldur            x0, [x1, #-1]
    //     0xb84734: ubfx            x0, x0, #0xc, #0x14
    // 0xb84738: str             x1, [SP]
    // 0xb8473c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb8473c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb84740: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xb84740: movz            x17, #0x2b03
    //     0xb84744: add             lr, x0, x17
    //     0xb84748: ldr             lr, [x21, lr, lsl #3]
    //     0xb8474c: blr             lr
    // 0xb84750: mov             x4, x0
    // 0xb84754: ldur            x3, [fp, #-0x58]
    // 0xb84758: stur            x4, [fp, #-0x68]
    // 0xb8475c: LoadField: r5 = r3->field_7
    //     0xb8475c: ldur            w5, [x3, #7]
    // 0xb84760: DecompressPointer r5
    //     0xb84760: add             x5, x5, HEAP, lsl #32
    // 0xb84764: mov             x2, x5
    // 0xb84768: stur            x5, [fp, #-0x60]
    // 0xb8476c: r0 = "index"
    //     0xb8476c: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb84770: ldr             x0, [x0, #0x878]
    // 0xb84774: r1 = Null
    //     0xb84774: mov             x1, NULL
    // 0xb84778: cmp             w2, NULL
    // 0xb8477c: b.eq            #0xb8479c
    // 0xb84780: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb84780: ldur            w4, [x2, #0x17]
    // 0xb84784: DecompressPointer r4
    //     0xb84784: add             x4, x4, HEAP, lsl #32
    // 0xb84788: r8 = X0
    //     0xb84788: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb8478c: LoadField: r9 = r4->field_7
    //     0xb8478c: ldur            x9, [x4, #7]
    // 0xb84790: r3 = Null
    //     0xb84790: add             x3, PP, #0x36, lsl #12  ; [pp+0x36fb8] Null
    //     0xb84794: ldr             x3, [x3, #0xfb8]
    // 0xb84798: blr             x9
    // 0xb8479c: ldur            x0, [fp, #-0x68]
    // 0xb847a0: ldur            x2, [fp, #-0x60]
    // 0xb847a4: r1 = Null
    //     0xb847a4: mov             x1, NULL
    // 0xb847a8: cmp             w2, NULL
    // 0xb847ac: b.eq            #0xb847cc
    // 0xb847b0: LoadField: r4 = r2->field_1b
    //     0xb847b0: ldur            w4, [x2, #0x1b]
    // 0xb847b4: DecompressPointer r4
    //     0xb847b4: add             x4, x4, HEAP, lsl #32
    // 0xb847b8: r8 = X1
    //     0xb847b8: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0xb847bc: LoadField: r9 = r4->field_7
    //     0xb847bc: ldur            x9, [x4, #7]
    // 0xb847c0: r3 = Null
    //     0xb847c0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36fc8] Null
    //     0xb847c4: ldr             x3, [x3, #0xfc8]
    // 0xb847c8: blr             x9
    // 0xb847cc: ldur            x1, [fp, #-0x58]
    // 0xb847d0: r2 = "index"
    //     0xb847d0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb847d4: ldr             x2, [x2, #0x878]
    // 0xb847d8: r0 = _hashCode()
    //     0xb847d8: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xb847dc: ldur            x1, [fp, #-0x58]
    // 0xb847e0: ldur            x3, [fp, #-0x68]
    // 0xb847e4: mov             x5, x0
    // 0xb847e8: r2 = "index"
    //     0xb847e8: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xb847ec: ldr             x2, [x2, #0x878]
    // 0xb847f0: r0 = _set()
    //     0xb847f0: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xb847f4: ldur            x1, [fp, #-8]
    // 0xb847f8: r0 = LoadClassIdInstr(r1)
    //     0xb847f8: ldur            x0, [x1, #-1]
    //     0xb847fc: ubfx            x0, x0, #0xc, #0x14
    // 0xb84800: ldur            x16, [fp, #-0x50]
    // 0xb84804: stp             x16, x1, [SP]
    // 0xb84808: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb84808: movz            x17, #0x3037
    //     0xb8480c: movk            x17, #0x1, lsl #16
    //     0xb84810: add             lr, x0, x17
    //     0xb84814: ldr             lr, [x21, lr, lsl #3]
    //     0xb84818: blr             lr
    // 0xb8481c: ArrayLoad: r1 = r0[0]  ; List_8
    //     0xb8481c: ldur            x1, [x0, #0x17]
    // 0xb84820: ldur            x2, [fp, #-0x28]
    // 0xb84824: LoadField: r0 = r2->field_b
    //     0xb84824: ldur            w0, [x2, #0xb]
    // 0xb84828: r3 = LoadInt32Instr(r0)
    //     0xb84828: sbfx            x3, x0, #1, #0x1f
    // 0xb8482c: cmp             x1, x3
    // 0xb84830: b.lt            #0xb84894
    // 0xb84834: cmp             x3, #0
    // 0xb84838: b.le            #0xb849a0
    // 0xb8483c: sub             x4, x3, #1
    // 0xb84840: mov             x0, x3
    // 0xb84844: mov             x1, x4
    // 0xb84848: cmp             x1, x0
    // 0xb8484c: b.hs            #0xb849f4
    // 0xb84850: LoadField: r0 = r2->field_f
    //     0xb84850: ldur            w0, [x2, #0xf]
    // 0xb84854: DecompressPointer r0
    //     0xb84854: add             x0, x0, HEAP, lsl #32
    // 0xb84858: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb84858: add             x16, x0, x4, lsl #2
    //     0xb8485c: ldur            w1, [x16, #0xf]
    // 0xb84860: DecompressPointer r1
    //     0xb84860: add             x1, x1, HEAP, lsl #32
    // 0xb84864: LoadField: r0 = r1->field_f
    //     0xb84864: ldur            w0, [x1, #0xf]
    // 0xb84868: DecompressPointer r0
    //     0xb84868: add             x0, x0, HEAP, lsl #32
    // 0xb8486c: r16 = Sentinel
    //     0xb8486c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb84870: cmp             w0, w16
    // 0xb84874: b.ne            #0xb84884
    // 0xb84878: r2 = nodes
    //     0xb84878: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fa8] Field <Node.nodes>: late final (offset: 0x10)
    //     0xb8487c: ldr             x2, [x2, #0xfa8]
    // 0xb84880: r0 = InitLateFinalInstanceField()
    //     0xb84880: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb84884: ldur            x16, [fp, #-0x48]
    // 0xb84888: stp             x16, x0, [SP]
    // 0xb8488c: r0 = add()
    //     0xb8488c: bl              #0x66b6fc  ; [package:html/dom.dart] NodeList::add
    // 0xb84890: b               #0xb84920
    // 0xb84894: mov             x1, x2
    // 0xb84898: ldur            x2, [fp, #-8]
    // 0xb8489c: r0 = LoadClassIdInstr(r2)
    //     0xb8489c: ldur            x0, [x2, #-1]
    //     0xb848a0: ubfx            x0, x0, #0xc, #0x14
    // 0xb848a4: ldur            x16, [fp, #-0x50]
    // 0xb848a8: stp             x16, x2, [SP]
    // 0xb848ac: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb848ac: movz            x17, #0x3037
    //     0xb848b0: movk            x17, #0x1, lsl #16
    //     0xb848b4: add             lr, x0, x17
    //     0xb848b8: ldr             lr, [x21, lr, lsl #3]
    //     0xb848bc: blr             lr
    // 0xb848c0: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xb848c0: ldur            x2, [x0, #0x17]
    // 0xb848c4: ldur            x3, [fp, #-0x28]
    // 0xb848c8: LoadField: r0 = r3->field_b
    //     0xb848c8: ldur            w0, [x3, #0xb]
    // 0xb848cc: r1 = LoadInt32Instr(r0)
    //     0xb848cc: sbfx            x1, x0, #1, #0x1f
    // 0xb848d0: mov             x0, x1
    // 0xb848d4: mov             x1, x2
    // 0xb848d8: cmp             x1, x0
    // 0xb848dc: b.hs            #0xb849f8
    // 0xb848e0: LoadField: r0 = r3->field_f
    //     0xb848e0: ldur            w0, [x3, #0xf]
    // 0xb848e4: DecompressPointer r0
    //     0xb848e4: add             x0, x0, HEAP, lsl #32
    // 0xb848e8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb848e8: add             x16, x0, x2, lsl #2
    //     0xb848ec: ldur            w1, [x16, #0xf]
    // 0xb848f0: DecompressPointer r1
    //     0xb848f0: add             x1, x1, HEAP, lsl #32
    // 0xb848f4: LoadField: r0 = r1->field_f
    //     0xb848f4: ldur            w0, [x1, #0xf]
    // 0xb848f8: DecompressPointer r0
    //     0xb848f8: add             x0, x0, HEAP, lsl #32
    // 0xb848fc: r16 = Sentinel
    //     0xb848fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb84900: cmp             w0, w16
    // 0xb84904: b.ne            #0xb84914
    // 0xb84908: r2 = nodes
    //     0xb84908: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fa8] Field <Node.nodes>: late final (offset: 0x10)
    //     0xb8490c: ldr             x2, [x2, #0xfa8]
    // 0xb84910: r0 = InitLateFinalInstanceField()
    //     0xb84910: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xb84914: ldur            x16, [fp, #-0x48]
    // 0xb84918: stp             x16, x0, [SP]
    // 0xb8491c: r0 = add()
    //     0xb8491c: bl              #0x66b6fc  ; [package:html/dom.dart] NodeList::add
    // 0xb84920: ldur            x0, [fp, #-0x30]
    // 0xb84924: add             x4, x0, #1
    // 0xb84928: b               #0xb84654
    // 0xb8492c: ldur            x3, [fp, #-0x18]
    // 0xb84930: cmp             w3, NULL
    // 0xb84934: b.eq            #0xb84980
    // 0xb84938: ldur            x1, [fp, #-0x20]
    // 0xb8493c: r0 = createElement()
    //     0xb8493c: bl              #0xb84a44  ; [package:html/dom.dart] Document::createElement
    // 0xb84940: mov             x1, x0
    // 0xb84944: r2 = "youtubeVideo"
    //     0xb84944: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fd8] "youtubeVideo"
    //     0xb84948: ldr             x2, [x2, #0xfd8]
    // 0xb8494c: stur            x0, [fp, #-8]
    // 0xb84950: r0 = id=()
    //     0xb84950: bl              #0xb849fc  ; [package:html/dom.dart] Element::id=
    // 0xb84954: ldur            x0, [fp, #-8]
    // 0xb84958: LoadField: r1 = r0->field_b
    //     0xb84958: ldur            w1, [x0, #0xb]
    // 0xb8495c: DecompressPointer r1
    //     0xb8495c: add             x1, x1, HEAP, lsl #32
    // 0xb84960: ldur            x3, [fp, #-0x18]
    // 0xb84964: r2 = "video-id"
    //     0xb84964: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fe0] "video-id"
    //     0xb84968: ldr             x2, [x2, #0xfe0]
    // 0xb8496c: r0 = []=()
    //     0xb8496c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xb84970: ldur            x1, [fp, #-0x20]
    // 0xb84974: ldur            x2, [fp, #-8]
    // 0xb84978: r3 = Null
    //     0xb84978: mov             x3, NULL
    // 0xb8497c: r0 = insertBefore()
    //     0xb8497c: bl              #0x896dc8  ; [package:html/dom.dart] Node::insertBefore
    // 0xb84980: ldur            x1, [fp, #-0x20]
    // 0xb84984: r0 = _outerHtml()
    //     0xb84984: bl              #0x9d0d58  ; [package:html/dom.dart] Node::_outerHtml
    // 0xb84988: LeaveFrame
    //     0xb84988: mov             SP, fp
    //     0xb8498c: ldp             fp, lr, [SP], #0x10
    // 0xb84990: ret
    //     0xb84990: ret             
    // 0xb84994: r0 = noElement()
    //     0xb84994: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xb84998: r0 = Throw()
    //     0xb84998: bl              #0xec04b8  ; ThrowStub
    // 0xb8499c: brk             #0
    // 0xb849a0: r0 = noElement()
    //     0xb849a0: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xb849a4: r0 = Throw()
    //     0xb849a4: bl              #0xec04b8  ; ThrowStub
    // 0xb849a8: brk             #0
    // 0xb849ac: r0 = ConcurrentModificationError()
    //     0xb849ac: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb849b0: mov             x1, x0
    // 0xb849b4: ldur            x0, [fp, #-0x40]
    // 0xb849b8: StoreField: r1->field_b = r0
    //     0xb849b8: stur            w0, [x1, #0xb]
    // 0xb849bc: mov             x0, x1
    // 0xb849c0: r0 = Throw()
    //     0xb849c0: bl              #0xec04b8  ; ThrowStub
    // 0xb849c4: brk             #0
    // 0xb849c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb849c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb849cc: b               #0xb84190
    // 0xb849d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb849d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb849d4: b               #0xb841dc
    // 0xb849d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb849d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb849dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb849dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb849e0: b               #0xb84394
    // 0xb849e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb849e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb849e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb849e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb849ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb849ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb849f0: b               #0xb84670
    // 0xb849f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb849f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb849f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb849f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 2502, size: 0x1c, field offset: 0x8
class TagDivExtension extends HtmlExtension {

  get _ supportedTags(/* No info */) {
    // ** addr: 0xc41258, size: 0xac
    // 0xc41258: EnterFrame
    //     0xc41258: stp             fp, lr, [SP, #-0x10]!
    //     0xc4125c: mov             fp, SP
    // 0xc41260: AllocStack(0x10)
    //     0xc41260: sub             SP, SP, #0x10
    // 0xc41264: CheckStackOverflow
    //     0xc41264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41268: cmp             SP, x16
    //     0xc4126c: b.ls            #0xc412fc
    // 0xc41270: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xc41270: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc41274: ldr             x0, [x0, #0x778]
    //     0xc41278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc4127c: cmp             w0, w16
    //     0xc41280: b.ne            #0xc4128c
    //     0xc41284: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xc41288: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc4128c: r1 = <String>
    //     0xc4128c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc41290: stur            x0, [fp, #-8]
    // 0xc41294: r0 = _Set()
    //     0xc41294: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xc41298: mov             x1, x0
    // 0xc4129c: ldur            x0, [fp, #-8]
    // 0xc412a0: stur            x1, [fp, #-0x10]
    // 0xc412a4: StoreField: r1->field_1b = r0
    //     0xc412a4: stur            w0, [x1, #0x1b]
    // 0xc412a8: StoreField: r1->field_b = rZR
    //     0xc412a8: stur            wzr, [x1, #0xb]
    // 0xc412ac: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xc412ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc412b0: ldr             x0, [x0, #0x780]
    //     0xc412b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc412b8: cmp             w0, w16
    //     0xc412bc: b.ne            #0xc412c8
    //     0xc412c0: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xc412c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc412c8: mov             x1, x0
    // 0xc412cc: ldur            x0, [fp, #-0x10]
    // 0xc412d0: StoreField: r0->field_f = r1
    //     0xc412d0: stur            w1, [x0, #0xf]
    // 0xc412d4: StoreField: r0->field_13 = rZR
    //     0xc412d4: stur            wzr, [x0, #0x13]
    // 0xc412d8: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc412d8: stur            wzr, [x0, #0x17]
    // 0xc412dc: mov             x1, x0
    // 0xc412e0: r2 = "div"
    //     0xc412e0: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f70] "div"
    //     0xc412e4: ldr             x2, [x2, #0xf70]
    // 0xc412e8: r0 = add()
    //     0xc412e8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xc412ec: ldur            x0, [fp, #-0x10]
    // 0xc412f0: LeaveFrame
    //     0xc412f0: mov             SP, fp
    //     0xc412f4: ldp             fp, lr, [SP], #0x10
    // 0xc412f8: ret
    //     0xc412f8: ret             
    // 0xc412fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc412fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41300: b               #0xc41270
  }
  _ build(/* No info */) {
    // ** addr: 0xdbfb20, size: 0xb6c
    // 0xdbfb20: EnterFrame
    //     0xdbfb20: stp             fp, lr, [SP, #-0x10]!
    //     0xdbfb24: mov             fp, SP
    // 0xdbfb28: AllocStack(0x58)
    //     0xdbfb28: sub             SP, SP, #0x58
    // 0xdbfb2c: SetupParameters(TagDivExtension this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xdbfb2c: mov             x0, x1
    //     0xdbfb30: stur            x1, [fp, #-8]
    //     0xdbfb34: mov             x1, x2
    //     0xdbfb38: stur            x2, [fp, #-0x10]
    // 0xdbfb3c: CheckStackOverflow
    //     0xdbfb3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbfb40: cmp             SP, x16
    //     0xdbfb44: b.ls            #0xdc0674
    // 0xdbfb48: r1 = 4
    //     0xdbfb48: movz            x1, #0x4
    // 0xdbfb4c: r0 = AllocateContext()
    //     0xdbfb4c: bl              #0xec126c  ; AllocateContextStub
    // 0xdbfb50: mov             x2, x0
    // 0xdbfb54: ldur            x0, [fp, #-8]
    // 0xdbfb58: stur            x2, [fp, #-0x18]
    // 0xdbfb5c: StoreField: r2->field_f = r0
    //     0xdbfb5c: stur            w0, [x2, #0xf]
    // 0xdbfb60: ldur            x1, [fp, #-0x10]
    // 0xdbfb64: r0 = attributes()
    //     0xdbfb64: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbfb68: mov             x1, x0
    // 0xdbfb6c: r2 = "aria-label"
    //     0xdbfb6c: add             x2, PP, #0x57, lsl #12  ; [pp+0x57eb0] "aria-label"
    //     0xdbfb70: ldr             x2, [x2, #0xeb0]
    // 0xdbfb74: stur            x0, [fp, #-0x20]
    // 0xdbfb78: r0 = _getValueOrData()
    //     0xdbfb78: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbfb7c: ldur            x3, [fp, #-0x20]
    // 0xdbfb80: LoadField: r1 = r3->field_f
    //     0xdbfb80: ldur            w1, [x3, #0xf]
    // 0xdbfb84: DecompressPointer r1
    //     0xdbfb84: add             x1, x1, HEAP, lsl #32
    // 0xdbfb88: cmp             w1, w0
    // 0xdbfb8c: b.ne            #0xdbfb98
    // 0xdbfb90: r1 = Null
    //     0xdbfb90: mov             x1, NULL
    // 0xdbfb94: b               #0xdbfb9c
    // 0xdbfb98: mov             x1, x0
    // 0xdbfb9c: cmp             w1, NULL
    // 0xdbfba0: b.ne            #0xdbfbac
    // 0xdbfba4: r0 = Null
    //     0xdbfba4: mov             x0, NULL
    // 0xdbfba8: b               #0xdbfbcc
    // 0xdbfbac: r0 = LoadClassIdInstr(r1)
    //     0xdbfbac: ldur            x0, [x1, #-1]
    //     0xdbfbb0: ubfx            x0, x0, #0xc, #0x14
    // 0xdbfbb4: r2 = "media object widget"
    //     0xdbfbb4: add             x2, PP, #0x57, lsl #12  ; [pp+0x57eb8] "media object widget"
    //     0xdbfbb8: ldr             x2, [x2, #0xeb8]
    // 0xdbfbbc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdbfbbc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdbfbc0: r0 = GDT[cid_x0 + -0xffc]()
    //     0xdbfbc0: sub             lr, x0, #0xffc
    //     0xdbfbc4: ldr             lr, [x21, lr, lsl #3]
    //     0xdbfbc8: blr             lr
    // 0xdbfbcc: cmp             w0, NULL
    // 0xdbfbd0: b.ne            #0xdbfbe8
    // 0xdbfbd4: r4 = true
    //     0xdbfbd4: add             x4, NULL, #0x20  ; true
    // 0xdbfbd8: r3 = false
    //     0xdbfbd8: add             x3, NULL, #0x30  ; false
    // 0xdbfbdc: r5 = Instance_PlaceholderAlignment
    //     0xdbfbdc: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfbe0: ldr             x5, [x5, #0xde8]
    // 0xdbfbe4: b               #0xdbfdbc
    // 0xdbfbe8: tbnz            w0, #4, #0xdbfdac
    // 0xdbfbec: ldur            x3, [fp, #-0x10]
    // 0xdbfbf0: LoadField: r4 = r3->field_7
    //     0xdbfbf0: ldur            w4, [x3, #7]
    // 0xdbfbf4: DecompressPointer r4
    //     0xdbfbf4: add             x4, x4, HEAP, lsl #32
    // 0xdbfbf8: stur            x4, [fp, #-0x28]
    // 0xdbfbfc: r0 = LoadClassIdInstr(r4)
    //     0xdbfbfc: ldur            x0, [x4, #-1]
    //     0xdbfc00: ubfx            x0, x0, #0xc, #0x14
    // 0xdbfc04: cmp             x0, #0x62f
    // 0xdbfc08: b.ne            #0xdbfc44
    // 0xdbfc0c: mov             x0, x4
    // 0xdbfc10: r2 = Null
    //     0xdbfc10: mov             x2, NULL
    // 0xdbfc14: r1 = Null
    //     0xdbfc14: mov             x1, NULL
    // 0xdbfc18: r4 = LoadClassIdInstr(r0)
    //     0xdbfc18: ldur            x4, [x0, #-1]
    //     0xdbfc1c: ubfx            x4, x4, #0xc, #0x14
    // 0xdbfc20: cmp             x4, #0x62f
    // 0xdbfc24: b.eq            #0xdbfc3c
    // 0xdbfc28: r8 = Element
    //     0xdbfc28: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdbfc2c: ldr             x8, [x8, #0xf80]
    // 0xdbfc30: r3 = Null
    //     0xdbfc30: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ec0] Null
    //     0xdbfc34: ldr             x3, [x3, #0xec0]
    // 0xdbfc38: r0 = Element()
    //     0xdbfc38: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdbfc3c: ldur            x0, [fp, #-0x28]
    // 0xdbfc40: b               #0xdbfc48
    // 0xdbfc44: r0 = Null
    //     0xdbfc44: mov             x0, NULL
    // 0xdbfc48: stur            x0, [fp, #-0x28]
    // 0xdbfc4c: cmp             w0, NULL
    // 0xdbfc50: b.ne            #0xdbfc5c
    // 0xdbfc54: r0 = Null
    //     0xdbfc54: mov             x0, NULL
    // 0xdbfc58: b               #0xdbfc90
    // 0xdbfc5c: mov             x1, x0
    // 0xdbfc60: r0 = _outerHtml()
    //     0xdbfc60: bl              #0x9d0d58  ; [package:html/dom.dart] Node::_outerHtml
    // 0xdbfc64: r1 = LoadClassIdInstr(r0)
    //     0xdbfc64: ldur            x1, [x0, #-1]
    //     0xdbfc68: ubfx            x1, x1, #0xc, #0x14
    // 0xdbfc6c: mov             x16, x0
    // 0xdbfc70: mov             x0, x1
    // 0xdbfc74: mov             x1, x16
    // 0xdbfc78: r2 = "youtube.com/embed"
    //     0xdbfc78: add             x2, PP, #0x57, lsl #12  ; [pp+0x57ed0] "youtube.com/embed"
    //     0xdbfc7c: ldr             x2, [x2, #0xed0]
    // 0xdbfc80: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdbfc80: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdbfc84: r0 = GDT[cid_x0 + -0xffc]()
    //     0xdbfc84: sub             lr, x0, #0xffc
    //     0xdbfc88: ldr             lr, [x21, lr, lsl #3]
    //     0xdbfc8c: blr             lr
    // 0xdbfc90: cmp             w0, NULL
    // 0xdbfc94: b.ne            #0xdbfcac
    // 0xdbfc98: r4 = true
    //     0xdbfc98: add             x4, NULL, #0x20  ; true
    // 0xdbfc9c: r3 = false
    //     0xdbfc9c: add             x3, NULL, #0x30  ; false
    // 0xdbfca0: r5 = Instance_PlaceholderAlignment
    //     0xdbfca0: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfca4: ldr             x5, [x5, #0xde8]
    // 0xdbfca8: b               #0xdbfdbc
    // 0xdbfcac: tbnz            w0, #4, #0xdbfd98
    // 0xdbfcb0: ldur            x1, [fp, #-0x28]
    // 0xdbfcb4: cmp             w1, NULL
    // 0xdbfcb8: b.eq            #0xdc067c
    // 0xdbfcbc: r0 = _outerHtml()
    //     0xdbfcbc: bl              #0x9d0d58  ; [package:html/dom.dart] Node::_outerHtml
    // 0xdbfcc0: mov             x1, x0
    // 0xdbfcc4: r0 = parse()
    //     0xdbfcc4: bl              #0x9562e4  ; [package:html/parser.dart] ::parse
    // 0xdbfcc8: mov             x1, x0
    // 0xdbfccc: r2 = "iframe"
    //     0xdbfccc: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4cd88] "iframe"
    //     0xdbfcd0: ldr             x2, [x2, #0xd88]
    // 0xdbfcd4: r0 = querySelectorAll()
    //     0xdbfcd4: bl              #0x9d099c  ; [package:html/src/query_selector.dart] ::querySelectorAll
    // 0xdbfcd8: mov             x1, x0
    // 0xdbfcdc: r0 = first()
    //     0xdbfcdc: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xdbfce0: LoadField: r3 = r0->field_b
    //     0xdbfce0: ldur            w3, [x0, #0xb]
    // 0xdbfce4: DecompressPointer r3
    //     0xdbfce4: add             x3, x3, HEAP, lsl #32
    // 0xdbfce8: mov             x1, x3
    // 0xdbfcec: stur            x3, [fp, #-0x28]
    // 0xdbfcf0: r2 = "src"
    //     0xdbfcf0: add             x2, PP, #0x57, lsl #12  ; [pp+0x57ed8] "src"
    //     0xdbfcf4: ldr             x2, [x2, #0xed8]
    // 0xdbfcf8: r0 = _getValueOrData()
    //     0xdbfcf8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbfcfc: mov             x1, x0
    // 0xdbfd00: ldur            x0, [fp, #-0x28]
    // 0xdbfd04: LoadField: r2 = r0->field_f
    //     0xdbfd04: ldur            w2, [x0, #0xf]
    // 0xdbfd08: DecompressPointer r2
    //     0xdbfd08: add             x2, x2, HEAP, lsl #32
    // 0xdbfd0c: cmp             w2, w1
    // 0xdbfd10: b.ne            #0xdbfd18
    // 0xdbfd14: r1 = Null
    //     0xdbfd14: mov             x1, NULL
    // 0xdbfd18: cmp             w1, NULL
    // 0xdbfd1c: b.eq            #0xdc0680
    // 0xdbfd20: r0 = convertUrlToId()
    //     0xdbfd20: bl              #0xdc06a4  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::convertUrlToId
    // 0xdbfd24: stur            x0, [fp, #-0x28]
    // 0xdbfd28: cmp             w0, NULL
    // 0xdbfd2c: b.eq            #0xdbfd84
    // 0xdbfd30: r0 = YoutubeVideoPlayer()
    //     0xdbfd30: bl              #0xbaea70  ; AllocateYoutubeVideoPlayerStub -> YoutubeVideoPlayer (size=0x1c)
    // 0xdbfd34: mov             x1, x0
    // 0xdbfd38: ldur            x0, [fp, #-0x28]
    // 0xdbfd3c: stur            x1, [fp, #-0x30]
    // 0xdbfd40: StoreField: r1->field_b = r0
    //     0xdbfd40: stur            w0, [x1, #0xb]
    // 0xdbfd44: r3 = false
    //     0xdbfd44: add             x3, NULL, #0x30  ; false
    // 0xdbfd48: StoreField: r1->field_f = r3
    //     0xdbfd48: stur            w3, [x1, #0xf]
    // 0xdbfd4c: StoreField: r1->field_13 = r3
    //     0xdbfd4c: stur            w3, [x1, #0x13]
    // 0xdbfd50: r4 = true
    //     0xdbfd50: add             x4, NULL, #0x20  ; true
    // 0xdbfd54: ArrayStore: r1[0] = r4  ; List_4
    //     0xdbfd54: stur            w4, [x1, #0x17]
    // 0xdbfd58: r0 = WidgetSpan()
    //     0xdbfd58: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbfd5c: mov             x1, x0
    // 0xdbfd60: ldur            x0, [fp, #-0x30]
    // 0xdbfd64: StoreField: r1->field_13 = r0
    //     0xdbfd64: stur            w0, [x1, #0x13]
    // 0xdbfd68: r5 = Instance_PlaceholderAlignment
    //     0xdbfd68: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfd6c: ldr             x5, [x5, #0xde8]
    // 0xdbfd70: StoreField: r1->field_b = r5
    //     0xdbfd70: stur            w5, [x1, #0xb]
    // 0xdbfd74: mov             x0, x1
    // 0xdbfd78: LeaveFrame
    //     0xdbfd78: mov             SP, fp
    //     0xdbfd7c: ldp             fp, lr, [SP], #0x10
    // 0xdbfd80: ret
    //     0xdbfd80: ret             
    // 0xdbfd84: r4 = true
    //     0xdbfd84: add             x4, NULL, #0x20  ; true
    // 0xdbfd88: r3 = false
    //     0xdbfd88: add             x3, NULL, #0x30  ; false
    // 0xdbfd8c: r5 = Instance_PlaceholderAlignment
    //     0xdbfd8c: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfd90: ldr             x5, [x5, #0xde8]
    // 0xdbfd94: b               #0xdbfdbc
    // 0xdbfd98: r4 = true
    //     0xdbfd98: add             x4, NULL, #0x20  ; true
    // 0xdbfd9c: r3 = false
    //     0xdbfd9c: add             x3, NULL, #0x30  ; false
    // 0xdbfda0: r5 = Instance_PlaceholderAlignment
    //     0xdbfda0: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfda4: ldr             x5, [x5, #0xde8]
    // 0xdbfda8: b               #0xdbfdbc
    // 0xdbfdac: r4 = true
    //     0xdbfdac: add             x4, NULL, #0x20  ; true
    // 0xdbfdb0: r3 = false
    //     0xdbfdb0: add             x3, NULL, #0x30  ; false
    // 0xdbfdb4: r5 = Instance_PlaceholderAlignment
    //     0xdbfdb4: add             x5, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfdb8: ldr             x5, [x5, #0xde8]
    // 0xdbfdbc: ldur            x6, [fp, #-0x10]
    // 0xdbfdc0: LoadField: r7 = r6->field_7
    //     0xdbfdc0: ldur            w7, [x6, #7]
    // 0xdbfdc4: DecompressPointer r7
    //     0xdbfdc4: add             x7, x7, HEAP, lsl #32
    // 0xdbfdc8: stur            x7, [fp, #-0x28]
    // 0xdbfdcc: r8 = LoadClassIdInstr(r7)
    //     0xdbfdcc: ldur            x8, [x7, #-1]
    //     0xdbfdd0: ubfx            x8, x8, #0xc, #0x14
    // 0xdbfdd4: stur            x8, [fp, #-0x38]
    // 0xdbfdd8: cmp             x8, #0x62f
    // 0xdbfddc: b.ne            #0xdbfe64
    // 0xdbfde0: mov             x0, x7
    // 0xdbfde4: r2 = Null
    //     0xdbfde4: mov             x2, NULL
    // 0xdbfde8: r1 = Null
    //     0xdbfde8: mov             x1, NULL
    // 0xdbfdec: r4 = LoadClassIdInstr(r0)
    //     0xdbfdec: ldur            x4, [x0, #-1]
    //     0xdbfdf0: ubfx            x4, x4, #0xc, #0x14
    // 0xdbfdf4: cmp             x4, #0x62f
    // 0xdbfdf8: b.eq            #0xdbfe10
    // 0xdbfdfc: r8 = Element
    //     0xdbfdfc: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdbfe00: ldr             x8, [x8, #0xf80]
    // 0xdbfe04: r3 = Null
    //     0xdbfe04: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ee0] Null
    //     0xdbfe08: ldr             x3, [x3, #0xee0]
    // 0xdbfe0c: r0 = Element()
    //     0xdbfe0c: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdbfe10: ldur            x0, [fp, #-0x28]
    // 0xdbfe14: LoadField: r3 = r0->field_b
    //     0xdbfe14: ldur            w3, [x0, #0xb]
    // 0xdbfe18: DecompressPointer r3
    //     0xdbfe18: add             x3, x3, HEAP, lsl #32
    // 0xdbfe1c: mov             x1, x3
    // 0xdbfe20: stur            x3, [fp, #-0x30]
    // 0xdbfe24: r2 = "id"
    //     0xdbfe24: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdbfe28: ldr             x2, [x2, #0x740]
    // 0xdbfe2c: r0 = _getValueOrData()
    //     0xdbfe2c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbfe30: mov             x1, x0
    // 0xdbfe34: ldur            x0, [fp, #-0x30]
    // 0xdbfe38: LoadField: r2 = r0->field_f
    //     0xdbfe38: ldur            w2, [x0, #0xf]
    // 0xdbfe3c: DecompressPointer r2
    //     0xdbfe3c: add             x2, x2, HEAP, lsl #32
    // 0xdbfe40: cmp             w2, w1
    // 0xdbfe44: b.ne            #0xdbfe50
    // 0xdbfe48: r0 = Null
    //     0xdbfe48: mov             x0, NULL
    // 0xdbfe4c: b               #0xdbfe54
    // 0xdbfe50: mov             x0, x1
    // 0xdbfe54: cmp             w0, NULL
    // 0xdbfe58: b.ne            #0xdbfe68
    // 0xdbfe5c: r0 = ""
    //     0xdbfe5c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdbfe60: b               #0xdbfe68
    // 0xdbfe64: r0 = ""
    //     0xdbfe64: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdbfe68: r1 = LoadClassIdInstr(r0)
    //     0xdbfe68: ldur            x1, [x0, #-1]
    //     0xdbfe6c: ubfx            x1, x1, #0xc, #0x14
    // 0xdbfe70: r16 = "youtubeVideo"
    //     0xdbfe70: add             x16, PP, #0x36, lsl #12  ; [pp+0x36fd8] "youtubeVideo"
    //     0xdbfe74: ldr             x16, [x16, #0xfd8]
    // 0xdbfe78: stp             x16, x0, [SP]
    // 0xdbfe7c: mov             x0, x1
    // 0xdbfe80: mov             lr, x0
    // 0xdbfe84: ldr             lr, [x21, lr, lsl #3]
    // 0xdbfe88: blr             lr
    // 0xdbfe8c: tbnz            w0, #4, #0xdbff6c
    // 0xdbfe90: ldur            x0, [fp, #-0x20]
    // 0xdbfe94: mov             x1, x0
    // 0xdbfe98: r2 = "video-id"
    //     0xdbfe98: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fe0] "video-id"
    //     0xdbfe9c: ldr             x2, [x2, #0xfe0]
    // 0xdbfea0: r0 = _getValueOrData()
    //     0xdbfea0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbfea4: mov             x1, x0
    // 0xdbfea8: ldur            x0, [fp, #-0x20]
    // 0xdbfeac: LoadField: r2 = r0->field_f
    //     0xdbfeac: ldur            w2, [x0, #0xf]
    // 0xdbfeb0: DecompressPointer r2
    //     0xdbfeb0: add             x2, x2, HEAP, lsl #32
    // 0xdbfeb4: cmp             w2, w1
    // 0xdbfeb8: b.ne            #0xdbfecc
    // 0xdbfebc: mov             x3, x0
    // 0xdbfec0: r4 = Instance_PlaceholderAlignment
    //     0xdbfec0: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfec4: ldr             x4, [x4, #0xde8]
    // 0xdbfec8: b               #0xdbff78
    // 0xdbfecc: cmp             w1, NULL
    // 0xdbfed0: b.eq            #0xdbff5c
    // 0xdbfed4: mov             x1, x0
    // 0xdbfed8: r2 = "video-id"
    //     0xdbfed8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36fe0] "video-id"
    //     0xdbfedc: ldr             x2, [x2, #0xfe0]
    // 0xdbfee0: r0 = _getValueOrData()
    //     0xdbfee0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbfee4: ldur            x3, [fp, #-0x20]
    // 0xdbfee8: LoadField: r1 = r3->field_f
    //     0xdbfee8: ldur            w1, [x3, #0xf]
    // 0xdbfeec: DecompressPointer r1
    //     0xdbfeec: add             x1, x1, HEAP, lsl #32
    // 0xdbfef0: cmp             w1, w0
    // 0xdbfef4: b.ne            #0xdbfefc
    // 0xdbfef8: r0 = Null
    //     0xdbfef8: mov             x0, NULL
    // 0xdbfefc: stur            x0, [fp, #-0x30]
    // 0xdbff00: cmp             w0, NULL
    // 0xdbff04: b.eq            #0xdc0684
    // 0xdbff08: r0 = YoutubeVideoPlayer()
    //     0xdbff08: bl              #0xbaea70  ; AllocateYoutubeVideoPlayerStub -> YoutubeVideoPlayer (size=0x1c)
    // 0xdbff0c: mov             x1, x0
    // 0xdbff10: ldur            x0, [fp, #-0x30]
    // 0xdbff14: stur            x1, [fp, #-0x40]
    // 0xdbff18: StoreField: r1->field_b = r0
    //     0xdbff18: stur            w0, [x1, #0xb]
    // 0xdbff1c: r0 = false
    //     0xdbff1c: add             x0, NULL, #0x30  ; false
    // 0xdbff20: StoreField: r1->field_f = r0
    //     0xdbff20: stur            w0, [x1, #0xf]
    // 0xdbff24: StoreField: r1->field_13 = r0
    //     0xdbff24: stur            w0, [x1, #0x13]
    // 0xdbff28: r0 = true
    //     0xdbff28: add             x0, NULL, #0x20  ; true
    // 0xdbff2c: ArrayStore: r1[0] = r0  ; List_4
    //     0xdbff2c: stur            w0, [x1, #0x17]
    // 0xdbff30: r0 = WidgetSpan()
    //     0xdbff30: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbff34: mov             x1, x0
    // 0xdbff38: ldur            x0, [fp, #-0x40]
    // 0xdbff3c: StoreField: r1->field_13 = r0
    //     0xdbff3c: stur            w0, [x1, #0x13]
    // 0xdbff40: r4 = Instance_PlaceholderAlignment
    //     0xdbff40: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbff44: ldr             x4, [x4, #0xde8]
    // 0xdbff48: StoreField: r1->field_b = r4
    //     0xdbff48: stur            w4, [x1, #0xb]
    // 0xdbff4c: mov             x0, x1
    // 0xdbff50: LeaveFrame
    //     0xdbff50: mov             SP, fp
    //     0xdbff54: ldp             fp, lr, [SP], #0x10
    // 0xdbff58: ret
    //     0xdbff58: ret             
    // 0xdbff5c: mov             x3, x0
    // 0xdbff60: r4 = Instance_PlaceholderAlignment
    //     0xdbff60: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbff64: ldr             x4, [x4, #0xde8]
    // 0xdbff68: b               #0xdbff78
    // 0xdbff6c: ldur            x3, [fp, #-0x20]
    // 0xdbff70: r4 = Instance_PlaceholderAlignment
    //     0xdbff70: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbff74: ldr             x4, [x4, #0xde8]
    // 0xdbff78: ldur            x5, [fp, #-0x38]
    // 0xdbff7c: cmp             x5, #0x62f
    // 0xdbff80: b.ne            #0xdc000c
    // 0xdbff84: ldur            x6, [fp, #-0x28]
    // 0xdbff88: mov             x0, x6
    // 0xdbff8c: r2 = Null
    //     0xdbff8c: mov             x2, NULL
    // 0xdbff90: r1 = Null
    //     0xdbff90: mov             x1, NULL
    // 0xdbff94: r4 = LoadClassIdInstr(r0)
    //     0xdbff94: ldur            x4, [x0, #-1]
    //     0xdbff98: ubfx            x4, x4, #0xc, #0x14
    // 0xdbff9c: cmp             x4, #0x62f
    // 0xdbffa0: b.eq            #0xdbffb8
    // 0xdbffa4: r8 = Element
    //     0xdbffa4: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdbffa8: ldr             x8, [x8, #0xf80]
    // 0xdbffac: r3 = Null
    //     0xdbffac: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ef0] Null
    //     0xdbffb0: ldr             x3, [x3, #0xef0]
    // 0xdbffb4: r0 = Element()
    //     0xdbffb4: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdbffb8: ldur            x0, [fp, #-0x28]
    // 0xdbffbc: LoadField: r3 = r0->field_b
    //     0xdbffbc: ldur            w3, [x0, #0xb]
    // 0xdbffc0: DecompressPointer r3
    //     0xdbffc0: add             x3, x3, HEAP, lsl #32
    // 0xdbffc4: mov             x1, x3
    // 0xdbffc8: stur            x3, [fp, #-0x30]
    // 0xdbffcc: r2 = "id"
    //     0xdbffcc: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdbffd0: ldr             x2, [x2, #0x740]
    // 0xdbffd4: r0 = _getValueOrData()
    //     0xdbffd4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbffd8: mov             x1, x0
    // 0xdbffdc: ldur            x0, [fp, #-0x30]
    // 0xdbffe0: LoadField: r2 = r0->field_f
    //     0xdbffe0: ldur            w2, [x0, #0xf]
    // 0xdbffe4: DecompressPointer r2
    //     0xdbffe4: add             x2, x2, HEAP, lsl #32
    // 0xdbffe8: cmp             w2, w1
    // 0xdbffec: b.ne            #0xdbfff8
    // 0xdbfff0: r0 = Null
    //     0xdbfff0: mov             x0, NULL
    // 0xdbfff4: b               #0xdbfffc
    // 0xdbfff8: mov             x0, x1
    // 0xdbfffc: cmp             w0, NULL
    // 0xdc0000: b.ne            #0xdc0010
    // 0xdc0004: r0 = ""
    //     0xdc0004: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc0008: b               #0xdc0010
    // 0xdc000c: r0 = ""
    //     0xdc000c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc0010: r1 = LoadClassIdInstr(r0)
    //     0xdc0010: ldur            x1, [x0, #-1]
    //     0xdc0014: ubfx            x1, x1, #0xc, #0x14
    // 0xdc0018: r16 = "ads"
    //     0xdc0018: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dcd0] "ads"
    //     0xdc001c: ldr             x16, [x16, #0xcd0]
    // 0xdc0020: stp             x16, x0, [SP]
    // 0xdc0024: mov             x0, x1
    // 0xdc0028: mov             lr, x0
    // 0xdc002c: ldr             lr, [x21, lr, lsl #3]
    // 0xdc0030: blr             lr
    // 0xdc0034: tbnz            w0, #4, #0xdc00e0
    // 0xdc0038: ldur            x3, [fp, #-8]
    // 0xdc003c: LoadField: r0 = r3->field_13
    //     0xdc003c: ldur            w0, [x3, #0x13]
    // 0xdc0040: DecompressPointer r0
    //     0xdc0040: add             x0, x0, HEAP, lsl #32
    // 0xdc0044: tbnz            w0, #4, #0xdc00d4
    // 0xdc0048: r0 = find()
    //     0xdc0048: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xdc004c: mov             x1, x0
    // 0xdc0050: r0 = _adsVisibility()
    //     0xdc0050: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xdc0054: mov             x2, x0
    // 0xdc0058: r1 = Null
    //     0xdc0058: mov             x1, NULL
    // 0xdc005c: r0 = AdsConfig.fromJson()
    //     0xdc005c: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xdc0060: LoadField: r1 = r0->field_b
    //     0xdc0060: ldur            w1, [x0, #0xb]
    // 0xdc0064: DecompressPointer r1
    //     0xdc0064: add             x1, x1, HEAP, lsl #32
    // 0xdc0068: LoadField: r0 = r1->field_7
    //     0xdc0068: ldur            w0, [x1, #7]
    // 0xdc006c: DecompressPointer r0
    //     0xdc006c: add             x0, x0, HEAP, lsl #32
    // 0xdc0070: tbnz            w0, #4, #0xdc00c0
    // 0xdc0074: r0 = find()
    //     0xdc0074: bl              #0x812084  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::find
    // 0xdc0078: mov             x1, x0
    // 0xdc007c: r0 = _adsVisibility()
    //     0xdc007c: bl              #0xa3690c  ; [package:nuonline/services/remote_config_service.dart] RemoteConfigService::_adsVisibility
    // 0xdc0080: mov             x2, x0
    // 0xdc0084: r1 = Null
    //     0xdc0084: mov             x1, NULL
    // 0xdc0088: r0 = AdsConfig.fromJson()
    //     0xdc0088: bl              #0xa35c4c  ; [package:nuonline/app/data/models/ads_config.dart] AdsConfig::AdsConfig.fromJson
    // 0xdc008c: r0 = AdmobNativeWidget()
    //     0xdc008c: bl              #0xdc0698  ; AllocateAdmobNativeWidgetStub -> AdmobNativeWidget (size=0xc)
    // 0xdc0090: stur            x0, [fp, #-0x30]
    // 0xdc0094: r0 = WidgetSpan()
    //     0xdc0094: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdc0098: mov             x1, x0
    // 0xdc009c: ldur            x0, [fp, #-0x30]
    // 0xdc00a0: StoreField: r1->field_13 = r0
    //     0xdc00a0: stur            w0, [x1, #0x13]
    // 0xdc00a4: r4 = Instance_PlaceholderAlignment
    //     0xdc00a4: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc00a8: ldr             x4, [x4, #0xde8]
    // 0xdc00ac: StoreField: r1->field_b = r4
    //     0xdc00ac: stur            w4, [x1, #0xb]
    // 0xdc00b0: mov             x0, x1
    // 0xdc00b4: LeaveFrame
    //     0xdc00b4: mov             SP, fp
    //     0xdc00b8: ldp             fp, lr, [SP], #0x10
    // 0xdc00bc: ret
    //     0xdc00bc: ret             
    // 0xdc00c0: r0 = Instance_WidgetSpan
    //     0xdc00c0: add             x0, PP, #0x57, lsl #12  ; [pp+0x57f00] Obj!WidgetSpan@e1dd01
    //     0xdc00c4: ldr             x0, [x0, #0xf00]
    // 0xdc00c8: LeaveFrame
    //     0xdc00c8: mov             SP, fp
    //     0xdc00cc: ldp             fp, lr, [SP], #0x10
    // 0xdc00d0: ret
    //     0xdc00d0: ret             
    // 0xdc00d4: r4 = Instance_PlaceholderAlignment
    //     0xdc00d4: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc00d8: ldr             x4, [x4, #0xde8]
    // 0xdc00dc: b               #0xdc00ec
    // 0xdc00e0: ldur            x3, [fp, #-8]
    // 0xdc00e4: r4 = Instance_PlaceholderAlignment
    //     0xdc00e4: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc00e8: ldr             x4, [x4, #0xde8]
    // 0xdc00ec: ldur            x5, [fp, #-0x38]
    // 0xdc00f0: cmp             x5, #0x62f
    // 0xdc00f4: b.ne            #0xdc0180
    // 0xdc00f8: ldur            x6, [fp, #-0x28]
    // 0xdc00fc: mov             x0, x6
    // 0xdc0100: r2 = Null
    //     0xdc0100: mov             x2, NULL
    // 0xdc0104: r1 = Null
    //     0xdc0104: mov             x1, NULL
    // 0xdc0108: r4 = LoadClassIdInstr(r0)
    //     0xdc0108: ldur            x4, [x0, #-1]
    //     0xdc010c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc0110: cmp             x4, #0x62f
    // 0xdc0114: b.eq            #0xdc012c
    // 0xdc0118: r8 = Element
    //     0xdc0118: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdc011c: ldr             x8, [x8, #0xf80]
    // 0xdc0120: r3 = Null
    //     0xdc0120: add             x3, PP, #0x57, lsl #12  ; [pp+0x57f08] Null
    //     0xdc0124: ldr             x3, [x3, #0xf08]
    // 0xdc0128: r0 = Element()
    //     0xdc0128: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdc012c: ldur            x0, [fp, #-0x28]
    // 0xdc0130: LoadField: r3 = r0->field_b
    //     0xdc0130: ldur            w3, [x0, #0xb]
    // 0xdc0134: DecompressPointer r3
    //     0xdc0134: add             x3, x3, HEAP, lsl #32
    // 0xdc0138: mov             x1, x3
    // 0xdc013c: stur            x3, [fp, #-0x30]
    // 0xdc0140: r2 = "id"
    //     0xdc0140: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc0144: ldr             x2, [x2, #0x740]
    // 0xdc0148: r0 = _getValueOrData()
    //     0xdc0148: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc014c: mov             x1, x0
    // 0xdc0150: ldur            x0, [fp, #-0x30]
    // 0xdc0154: LoadField: r2 = r0->field_f
    //     0xdc0154: ldur            w2, [x0, #0xf]
    // 0xdc0158: DecompressPointer r2
    //     0xdc0158: add             x2, x2, HEAP, lsl #32
    // 0xdc015c: cmp             w2, w1
    // 0xdc0160: b.ne            #0xdc016c
    // 0xdc0164: r0 = Null
    //     0xdc0164: mov             x0, NULL
    // 0xdc0168: b               #0xdc0170
    // 0xdc016c: mov             x0, x1
    // 0xdc0170: cmp             w0, NULL
    // 0xdc0174: b.ne            #0xdc0184
    // 0xdc0178: r0 = ""
    //     0xdc0178: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc017c: b               #0xdc0184
    // 0xdc0180: r0 = ""
    //     0xdc0180: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc0184: r1 = LoadClassIdInstr(r0)
    //     0xdc0184: ldur            x1, [x0, #-1]
    //     0xdc0188: ubfx            x1, x1, #0xc, #0x14
    // 0xdc018c: r16 = "insertion"
    //     0xdc018c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f80] "insertion"
    //     0xdc0190: ldr             x16, [x16, #0xf80]
    // 0xdc0194: stp             x16, x0, [SP]
    // 0xdc0198: mov             x0, x1
    // 0xdc019c: mov             lr, x0
    // 0xdc01a0: ldr             lr, [x21, lr, lsl #3]
    // 0xdc01a4: blr             lr
    // 0xdc01a8: tbnz            w0, #4, #0xdc02e4
    // 0xdc01ac: ldur            x0, [fp, #-0x20]
    // 0xdc01b0: mov             x1, x0
    // 0xdc01b4: r2 = "index"
    //     0xdc01b4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xdc01b8: ldr             x2, [x2, #0x878]
    // 0xdc01bc: r0 = _getValueOrData()
    //     0xdc01bc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc01c0: mov             x1, x0
    // 0xdc01c4: ldur            x0, [fp, #-0x20]
    // 0xdc01c8: LoadField: r2 = r0->field_f
    //     0xdc01c8: ldur            w2, [x0, #0xf]
    // 0xdc01cc: DecompressPointer r2
    //     0xdc01cc: add             x2, x2, HEAP, lsl #32
    // 0xdc01d0: cmp             w2, w1
    // 0xdc01d4: b.ne            #0xdc01dc
    // 0xdc01d8: r1 = Null
    //     0xdc01d8: mov             x1, NULL
    // 0xdc01dc: cmp             w1, NULL
    // 0xdc01e0: b.ne            #0xdc01e8
    // 0xdc01e4: r1 = ""
    //     0xdc01e4: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc01e8: ldur            x2, [fp, #-0x18]
    // 0xdc01ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc01ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc01f0: r0 = tryParse()
    //     0xdc01f0: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0xdc01f4: mov             x1, x0
    // 0xdc01f8: ldur            x2, [fp, #-0x18]
    // 0xdc01fc: StoreField: r2->field_1b = r0
    //     0xdc01fc: stur            w0, [x2, #0x1b]
    //     0xdc0200: tbz             w0, #0, #0xdc021c
    //     0xdc0204: ldurb           w16, [x2, #-1]
    //     0xdc0208: ldurb           w17, [x0, #-1]
    //     0xdc020c: and             x16, x17, x16, lsr #2
    //     0xdc0210: tst             x16, HEAP, lsr #32
    //     0xdc0214: b.eq            #0xdc021c
    //     0xdc0218: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdc021c: cmp             w1, NULL
    // 0xdc0220: b.eq            #0xdc02d4
    // 0xdc0224: ldur            x3, [fp, #-8]
    // 0xdc0228: LoadField: r4 = r3->field_7
    //     0xdc0228: ldur            w4, [x3, #7]
    // 0xdc022c: DecompressPointer r4
    //     0xdc022c: add             x4, x4, HEAP, lsl #32
    // 0xdc0230: stur            x4, [fp, #-0x30]
    // 0xdc0234: LoadField: r0 = r3->field_b
    //     0xdc0234: ldur            w0, [x3, #0xb]
    // 0xdc0238: DecompressPointer r0
    //     0xdc0238: add             x0, x0, HEAP, lsl #32
    // 0xdc023c: r3 = LoadClassIdInstr(r0)
    //     0xdc023c: ldur            x3, [x0, #-1]
    //     0xdc0240: ubfx            x3, x3, #0xc, #0x14
    // 0xdc0244: stp             x1, x0, [SP]
    // 0xdc0248: mov             x0, x3
    // 0xdc024c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc024c: movz            x17, #0x3037
    //     0xdc0250: movk            x17, #0x1, lsl #16
    //     0xdc0254: add             lr, x0, x17
    //     0xdc0258: ldr             lr, [x21, lr, lsl #3]
    //     0xdc025c: blr             lr
    // 0xdc0260: stur            x0, [fp, #-0x40]
    // 0xdc0264: r0 = BacaJugaWidget()
    //     0xdc0264: bl              #0xdc068c  ; AllocateBacaJugaWidgetStub -> BacaJugaWidget (size=0x1c)
    // 0xdc0268: mov             x3, x0
    // 0xdc026c: r0 = "Baca Juga:"
    //     0xdc026c: add             x0, PP, #0x57, lsl #12  ; [pp+0x57f18] "Baca Juga:"
    //     0xdc0270: ldr             x0, [x0, #0xf18]
    // 0xdc0274: stur            x3, [fp, #-0x48]
    // 0xdc0278: StoreField: r3->field_b = r0
    //     0xdc0278: stur            w0, [x3, #0xb]
    // 0xdc027c: ldur            x0, [fp, #-0x40]
    // 0xdc0280: StoreField: r3->field_f = r0
    //     0xdc0280: stur            w0, [x3, #0xf]
    // 0xdc0284: ldur            x2, [fp, #-0x18]
    // 0xdc0288: r1 = Function '<anonymous closure>':.
    //     0xdc0288: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f20] AnonymousClosure: (0xdc0ab8), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] TagDivExtension::build (0xdbfb20)
    //     0xdc028c: ldr             x1, [x1, #0xf20]
    // 0xdc0290: r0 = AllocateClosure()
    //     0xdc0290: bl              #0xec1630  ; AllocateClosureStub
    // 0xdc0294: mov             x1, x0
    // 0xdc0298: ldur            x0, [fp, #-0x48]
    // 0xdc029c: StoreField: r0->field_13 = r1
    //     0xdc029c: stur            w1, [x0, #0x13]
    // 0xdc02a0: ldur            x1, [fp, #-0x30]
    // 0xdc02a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xdc02a4: stur            w1, [x0, #0x17]
    // 0xdc02a8: r0 = WidgetSpan()
    //     0xdc02a8: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdc02ac: mov             x1, x0
    // 0xdc02b0: ldur            x0, [fp, #-0x48]
    // 0xdc02b4: StoreField: r1->field_13 = r0
    //     0xdc02b4: stur            w0, [x1, #0x13]
    // 0xdc02b8: r4 = Instance_PlaceholderAlignment
    //     0xdc02b8: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc02bc: ldr             x4, [x4, #0xde8]
    // 0xdc02c0: StoreField: r1->field_b = r4
    //     0xdc02c0: stur            w4, [x1, #0xb]
    // 0xdc02c4: mov             x0, x1
    // 0xdc02c8: LeaveFrame
    //     0xdc02c8: mov             SP, fp
    //     0xdc02cc: ldp             fp, lr, [SP], #0x10
    // 0xdc02d0: ret
    //     0xdc02d0: ret             
    // 0xdc02d4: ldur            x3, [fp, #-8]
    // 0xdc02d8: r4 = Instance_PlaceholderAlignment
    //     0xdc02d8: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc02dc: ldr             x4, [x4, #0xde8]
    // 0xdc02e0: b               #0xdc02f0
    // 0xdc02e4: ldur            x3, [fp, #-8]
    // 0xdc02e8: r4 = Instance_PlaceholderAlignment
    //     0xdc02e8: add             x4, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc02ec: ldr             x4, [x4, #0xde8]
    // 0xdc02f0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xdc02f0: ldur            w0, [x3, #0x17]
    // 0xdc02f4: DecompressPointer r0
    //     0xdc02f4: add             x0, x0, HEAP, lsl #32
    // 0xdc02f8: tbz             w0, #4, #0xdc03d0
    // 0xdc02fc: ldur            x0, [fp, #-0x38]
    // 0xdc0300: cmp             x0, #0x62f
    // 0xdc0304: b.ne            #0xdc0390
    // 0xdc0308: ldur            x5, [fp, #-0x28]
    // 0xdc030c: mov             x0, x5
    // 0xdc0310: r2 = Null
    //     0xdc0310: mov             x2, NULL
    // 0xdc0314: r1 = Null
    //     0xdc0314: mov             x1, NULL
    // 0xdc0318: r4 = LoadClassIdInstr(r0)
    //     0xdc0318: ldur            x4, [x0, #-1]
    //     0xdc031c: ubfx            x4, x4, #0xc, #0x14
    // 0xdc0320: cmp             x4, #0x62f
    // 0xdc0324: b.eq            #0xdc033c
    // 0xdc0328: r8 = Element
    //     0xdc0328: add             x8, PP, #0x51, lsl #12  ; [pp+0x51f80] Type: Element
    //     0xdc032c: ldr             x8, [x8, #0xf80]
    // 0xdc0330: r3 = Null
    //     0xdc0330: add             x3, PP, #0x57, lsl #12  ; [pp+0x57f28] Null
    //     0xdc0334: ldr             x3, [x3, #0xf28]
    // 0xdc0338: r0 = Element()
    //     0xdc0338: bl              #0x66c0f0  ; IsType_Element_Stub
    // 0xdc033c: ldur            x0, [fp, #-0x28]
    // 0xdc0340: LoadField: r3 = r0->field_b
    //     0xdc0340: ldur            w3, [x0, #0xb]
    // 0xdc0344: DecompressPointer r3
    //     0xdc0344: add             x3, x3, HEAP, lsl #32
    // 0xdc0348: mov             x1, x3
    // 0xdc034c: stur            x3, [fp, #-0x30]
    // 0xdc0350: r2 = "id"
    //     0xdc0350: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc0354: ldr             x2, [x2, #0x740]
    // 0xdc0358: r0 = _getValueOrData()
    //     0xdc0358: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc035c: mov             x1, x0
    // 0xdc0360: ldur            x0, [fp, #-0x30]
    // 0xdc0364: LoadField: r2 = r0->field_f
    //     0xdc0364: ldur            w2, [x0, #0xf]
    // 0xdc0368: DecompressPointer r2
    //     0xdc0368: add             x2, x2, HEAP, lsl #32
    // 0xdc036c: cmp             w2, w1
    // 0xdc0370: b.ne            #0xdc037c
    // 0xdc0374: r0 = Null
    //     0xdc0374: mov             x0, NULL
    // 0xdc0378: b               #0xdc0380
    // 0xdc037c: mov             x0, x1
    // 0xdc0380: cmp             w0, NULL
    // 0xdc0384: b.ne            #0xdc0394
    // 0xdc0388: r0 = ""
    //     0xdc0388: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc038c: b               #0xdc0394
    // 0xdc0390: r0 = ""
    //     0xdc0390: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc0394: r1 = LoadClassIdInstr(r0)
    //     0xdc0394: ldur            x1, [x0, #-1]
    //     0xdc0398: ubfx            x1, x1, #0xc, #0x14
    // 0xdc039c: r16 = "donation_support"
    //     0xdc039c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f68] "donation_support"
    //     0xdc03a0: ldr             x16, [x16, #0xf68]
    // 0xdc03a4: stp             x16, x0, [SP]
    // 0xdc03a8: mov             x0, x1
    // 0xdc03ac: mov             lr, x0
    // 0xdc03b0: ldr             lr, [x21, lr, lsl #3]
    // 0xdc03b4: blr             lr
    // 0xdc03b8: tbnz            w0, #4, #0xdc03d0
    // 0xdc03bc: r0 = Instance_WidgetSpan
    //     0xdc03bc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57f38] Obj!WidgetSpan@e1dce1
    //     0xdc03c0: ldr             x0, [x0, #0xf38]
    // 0xdc03c4: LeaveFrame
    //     0xdc03c4: mov             SP, fp
    //     0xdc03c8: ldp             fp, lr, [SP], #0x10
    // 0xdc03cc: ret
    //     0xdc03cc: ret             
    // 0xdc03d0: ldur            x1, [fp, #-0x10]
    // 0xdc03d4: r0 = id()
    //     0xdc03d4: bl              #0x9ba940  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::id
    // 0xdc03d8: r1 = LoadClassIdInstr(r0)
    //     0xdc03d8: ldur            x1, [x0, #-1]
    //     0xdc03dc: ubfx            x1, x1, #0xc, #0x14
    // 0xdc03e0: r16 = "insertTopic"
    //     0xdc03e0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36fb0] "insertTopic"
    //     0xdc03e4: ldr             x16, [x16, #0xfb0]
    // 0xdc03e8: stp             x16, x0, [SP]
    // 0xdc03ec: mov             x0, x1
    // 0xdc03f0: mov             lr, x0
    // 0xdc03f4: ldr             lr, [x21, lr, lsl #3]
    // 0xdc03f8: blr             lr
    // 0xdc03fc: tbnz            w0, #4, #0xdc05c4
    // 0xdc0400: ldur            x0, [fp, #-0x20]
    // 0xdc0404: mov             x1, x0
    // 0xdc0408: r2 = "index"
    //     0xdc0408: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xdc040c: ldr             x2, [x2, #0x878]
    // 0xdc0410: r0 = _getValueOrData()
    //     0xdc0410: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc0414: mov             x1, x0
    // 0xdc0418: ldur            x0, [fp, #-0x20]
    // 0xdc041c: LoadField: r2 = r0->field_f
    //     0xdc041c: ldur            w2, [x0, #0xf]
    // 0xdc0420: DecompressPointer r2
    //     0xdc0420: add             x2, x2, HEAP, lsl #32
    // 0xdc0424: cmp             w2, w1
    // 0xdc0428: b.ne            #0xdc0434
    // 0xdc042c: r0 = Null
    //     0xdc042c: mov             x0, NULL
    // 0xdc0430: b               #0xdc0438
    // 0xdc0434: mov             x0, x1
    // 0xdc0438: cmp             w0, NULL
    // 0xdc043c: b.ne            #0xdc0448
    // 0xdc0440: r1 = ""
    //     0xdc0440: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc0444: b               #0xdc044c
    // 0xdc0448: mov             x1, x0
    // 0xdc044c: ldur            x2, [fp, #-0x18]
    // 0xdc0450: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc0450: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc0454: r0 = tryParse()
    //     0xdc0454: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0xdc0458: mov             x1, x0
    // 0xdc045c: ldur            x2, [fp, #-0x18]
    // 0xdc0460: stur            x1, [fp, #-0x28]
    // 0xdc0464: StoreField: r2->field_13 = r0
    //     0xdc0464: stur            w0, [x2, #0x13]
    //     0xdc0468: tbz             w0, #0, #0xdc0484
    //     0xdc046c: ldurb           w16, [x2, #-1]
    //     0xdc0470: ldurb           w17, [x0, #-1]
    //     0xdc0474: and             x16, x17, x16, lsr #2
    //     0xdc0478: tst             x16, HEAP, lsr #32
    //     0xdc047c: b.eq            #0xdc0484
    //     0xdc0480: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdc0484: cmp             w1, NULL
    // 0xdc0488: b.eq            #0xdc05b8
    // 0xdc048c: ldur            x3, [fp, #-8]
    // 0xdc0490: LoadField: r4 = r3->field_f
    //     0xdc0490: ldur            w4, [x3, #0xf]
    // 0xdc0494: DecompressPointer r4
    //     0xdc0494: add             x4, x4, HEAP, lsl #32
    // 0xdc0498: stur            x4, [fp, #-0x20]
    // 0xdc049c: r0 = LoadClassIdInstr(r4)
    //     0xdc049c: ldur            x0, [x4, #-1]
    //     0xdc04a0: ubfx            x0, x0, #0xc, #0x14
    // 0xdc04a4: stp             x1, x4, [SP]
    // 0xdc04a8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc04a8: movz            x17, #0x3037
    //     0xdc04ac: movk            x17, #0x1, lsl #16
    //     0xdc04b0: add             lr, x0, x17
    //     0xdc04b4: ldr             lr, [x21, lr, lsl #3]
    //     0xdc04b8: blr             lr
    // 0xdc04bc: LoadField: r1 = r0->field_13
    //     0xdc04bc: ldur            w1, [x0, #0x13]
    // 0xdc04c0: DecompressPointer r1
    //     0xdc04c0: add             x1, x1, HEAP, lsl #32
    // 0xdc04c4: r0 = LoadClassIdInstr(r1)
    //     0xdc04c4: ldur            x0, [x1, #-1]
    //     0xdc04c8: ubfx            x0, x0, #0xc, #0x14
    // 0xdc04cc: r2 = "/"
    //     0xdc04cc: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xdc04d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xdc04d0: sub             lr, x0, #1, lsl #12
    //     0xdc04d4: ldr             lr, [x21, lr, lsl #3]
    //     0xdc04d8: blr             lr
    // 0xdc04dc: mov             x1, x0
    // 0xdc04e0: r0 = last()
    //     0xdc04e0: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xdc04e4: ldur            x2, [fp, #-0x18]
    // 0xdc04e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xdc04e8: stur            w0, [x2, #0x17]
    //     0xdc04ec: tbz             w0, #0, #0xdc0508
    //     0xdc04f0: ldurb           w16, [x2, #-1]
    //     0xdc04f4: ldurb           w17, [x0, #-1]
    //     0xdc04f8: and             x16, x17, x16, lsr #2
    //     0xdc04fc: tst             x16, HEAP, lsr #32
    //     0xdc0500: b.eq            #0xdc0508
    //     0xdc0504: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xdc0508: ldur            x0, [fp, #-8]
    // 0xdc050c: LoadField: r1 = r0->field_7
    //     0xdc050c: ldur            w1, [x0, #7]
    // 0xdc0510: DecompressPointer r1
    //     0xdc0510: add             x1, x1, HEAP, lsl #32
    // 0xdc0514: ldur            x0, [fp, #-0x20]
    // 0xdc0518: stur            x1, [fp, #-0x30]
    // 0xdc051c: r3 = LoadClassIdInstr(r0)
    //     0xdc051c: ldur            x3, [x0, #-1]
    //     0xdc0520: ubfx            x3, x3, #0xc, #0x14
    // 0xdc0524: ldur            x16, [fp, #-0x28]
    // 0xdc0528: stp             x16, x0, [SP]
    // 0xdc052c: mov             x0, x3
    // 0xdc0530: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc0530: movz            x17, #0x3037
    //     0xdc0534: movk            x17, #0x1, lsl #16
    //     0xdc0538: add             lr, x0, x17
    //     0xdc053c: ldr             lr, [x21, lr, lsl #3]
    //     0xdc0540: blr             lr
    // 0xdc0544: stur            x0, [fp, #-8]
    // 0xdc0548: r0 = BacaJugaWidget()
    //     0xdc0548: bl              #0xdc068c  ; AllocateBacaJugaWidgetStub -> BacaJugaWidget (size=0x1c)
    // 0xdc054c: mov             x3, x0
    // 0xdc0550: r0 = "Baca Juga Topik Terkait:"
    //     0xdc0550: add             x0, PP, #0x57, lsl #12  ; [pp+0x57f40] "Baca Juga Topik Terkait:"
    //     0xdc0554: ldr             x0, [x0, #0xf40]
    // 0xdc0558: stur            x3, [fp, #-0x20]
    // 0xdc055c: StoreField: r3->field_b = r0
    //     0xdc055c: stur            w0, [x3, #0xb]
    // 0xdc0560: ldur            x0, [fp, #-8]
    // 0xdc0564: StoreField: r3->field_f = r0
    //     0xdc0564: stur            w0, [x3, #0xf]
    // 0xdc0568: ldur            x2, [fp, #-0x18]
    // 0xdc056c: r1 = Function '<anonymous closure>':.
    //     0xdc056c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f48] AnonymousClosure: (0xdc08c8), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] TagDivExtension::build (0xdbfb20)
    //     0xdc0570: ldr             x1, [x1, #0xf48]
    // 0xdc0574: r0 = AllocateClosure()
    //     0xdc0574: bl              #0xec1630  ; AllocateClosureStub
    // 0xdc0578: mov             x1, x0
    // 0xdc057c: ldur            x0, [fp, #-0x20]
    // 0xdc0580: StoreField: r0->field_13 = r1
    //     0xdc0580: stur            w1, [x0, #0x13]
    // 0xdc0584: ldur            x1, [fp, #-0x30]
    // 0xdc0588: ArrayStore: r0[0] = r1  ; List_4
    //     0xdc0588: stur            w1, [x0, #0x17]
    // 0xdc058c: r0 = WidgetSpan()
    //     0xdc058c: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdc0590: mov             x1, x0
    // 0xdc0594: ldur            x0, [fp, #-0x20]
    // 0xdc0598: StoreField: r1->field_13 = r0
    //     0xdc0598: stur            w0, [x1, #0x13]
    // 0xdc059c: r0 = Instance_PlaceholderAlignment
    //     0xdc059c: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc05a0: ldr             x0, [x0, #0xde8]
    // 0xdc05a4: StoreField: r1->field_b = r0
    //     0xdc05a4: stur            w0, [x1, #0xb]
    // 0xdc05a8: mov             x0, x1
    // 0xdc05ac: LeaveFrame
    //     0xdc05ac: mov             SP, fp
    //     0xdc05b0: ldp             fp, lr, [SP], #0x10
    // 0xdc05b4: ret
    //     0xdc05b4: ret             
    // 0xdc05b8: r0 = Instance_PlaceholderAlignment
    //     0xdc05b8: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc05bc: ldr             x0, [x0, #0xde8]
    // 0xdc05c0: b               #0xdc05cc
    // 0xdc05c4: r0 = Instance_PlaceholderAlignment
    //     0xdc05c4: add             x0, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc05c8: ldr             x0, [x0, #0xde8]
    // 0xdc05cc: ldur            x2, [fp, #-0x10]
    // 0xdc05d0: mov             x1, x2
    // 0xdc05d4: r0 = inlineSpanChildren()
    //     0xdc05d4: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdc05d8: stur            x0, [fp, #-8]
    // 0xdc05dc: cmp             w0, NULL
    // 0xdc05e0: b.eq            #0xdc0688
    // 0xdc05e4: ldur            x1, [fp, #-0x10]
    // 0xdc05e8: LoadField: r2 = r1->field_f
    //     0xdc05e8: ldur            w2, [x1, #0xf]
    // 0xdc05ec: DecompressPointer r2
    //     0xdc05ec: add             x2, x2, HEAP, lsl #32
    // 0xdc05f0: cmp             w2, NULL
    // 0xdc05f4: b.ne            #0xdc0600
    // 0xdc05f8: r1 = Null
    //     0xdc05f8: mov             x1, NULL
    // 0xdc05fc: b               #0xdc0608
    // 0xdc0600: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xdc0600: ldur            w1, [x2, #0x17]
    // 0xdc0604: DecompressPointer r1
    //     0xdc0604: add             x1, x1, HEAP, lsl #32
    // 0xdc0608: cmp             w1, NULL
    // 0xdc060c: b.ne            #0xdc062c
    // 0xdc0610: r0 = Style()
    //     0xdc0610: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xdc0614: mov             x1, x0
    // 0xdc0618: stur            x0, [fp, #-0x10]
    // 0xdc061c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc061c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc0620: r0 = Style()
    //     0xdc0620: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xdc0624: ldur            x3, [fp, #-0x10]
    // 0xdc0628: b               #0xdc0630
    // 0xdc062c: mov             x3, x1
    // 0xdc0630: stur            x3, [fp, #-0x10]
    // 0xdc0634: r0 = CssBoxWidget()
    //     0xdc0634: bl              #0xa249dc  ; AllocateCssBoxWidgetStub -> CssBoxWidget (size=0x24)
    // 0xdc0638: mov             x1, x0
    // 0xdc063c: ldur            x2, [fp, #-8]
    // 0xdc0640: ldur            x3, [fp, #-0x10]
    // 0xdc0644: stur            x0, [fp, #-8]
    // 0xdc0648: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xdc0648: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xdc064c: r0 = CssBoxWidget.withInlineSpanChildren()
    //     0xdc064c: bl              #0xa23eec  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::CssBoxWidget.withInlineSpanChildren
    // 0xdc0650: r0 = WidgetSpan()
    //     0xdc0650: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdc0654: ldur            x1, [fp, #-8]
    // 0xdc0658: StoreField: r0->field_13 = r1
    //     0xdc0658: stur            w1, [x0, #0x13]
    // 0xdc065c: r1 = Instance_PlaceholderAlignment
    //     0xdc065c: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdc0660: ldr             x1, [x1, #0xde8]
    // 0xdc0664: StoreField: r0->field_b = r1
    //     0xdc0664: stur            w1, [x0, #0xb]
    // 0xdc0668: LeaveFrame
    //     0xdc0668: mov             SP, fp
    //     0xdc066c: ldp             fp, lr, [SP], #0x10
    // 0xdc0670: ret
    //     0xdc0670: ret             
    // 0xdc0674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0674: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0678: b               #0xdbfb48
    // 0xdc067c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdc067c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdc0680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdc0680: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdc0684: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdc0684: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xdc0688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdc0688: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xdc08c8, size: 0x1f0
    // 0xdc08c8: EnterFrame
    //     0xdc08c8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc08cc: mov             fp, SP
    // 0xdc08d0: AllocStack(0x38)
    //     0xdc08d0: sub             SP, SP, #0x38
    // 0xdc08d4: SetupParameters()
    //     0xdc08d4: ldr             x0, [fp, #0x10]
    //     0xdc08d8: ldur            w1, [x0, #0x17]
    //     0xdc08dc: add             x1, x1, HEAP, lsl #32
    //     0xdc08e0: stur            x1, [fp, #-8]
    // 0xdc08e4: CheckStackOverflow
    //     0xdc08e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc08e8: cmp             SP, x16
    //     0xdc08ec: b.ls            #0xdc0ab0
    // 0xdc08f0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xdc08f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc08f4: ldr             x0, [x0, #0x2670]
    //     0xdc08f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdc08fc: cmp             w0, w16
    //     0xdc0900: b.ne            #0xdc090c
    //     0xdc0904: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xdc0908: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xdc090c: r1 = Null
    //     0xdc090c: mov             x1, NULL
    // 0xdc0910: r2 = 12
    //     0xdc0910: movz            x2, #0xc
    // 0xdc0914: r0 = AllocateArray()
    //     0xdc0914: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc0918: mov             x1, x0
    // 0xdc091c: stur            x1, [fp, #-0x18]
    // 0xdc0920: r16 = "id"
    //     0xdc0920: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc0924: ldr             x16, [x16, #0x740]
    // 0xdc0928: StoreField: r1->field_f = r16
    //     0xdc0928: stur            w16, [x1, #0xf]
    // 0xdc092c: ldur            x2, [fp, #-8]
    // 0xdc0930: LoadField: r0 = r2->field_f
    //     0xdc0930: ldur            w0, [x2, #0xf]
    // 0xdc0934: DecompressPointer r0
    //     0xdc0934: add             x0, x0, HEAP, lsl #32
    // 0xdc0938: LoadField: r3 = r0->field_f
    //     0xdc0938: ldur            w3, [x0, #0xf]
    // 0xdc093c: DecompressPointer r3
    //     0xdc093c: add             x3, x3, HEAP, lsl #32
    // 0xdc0940: LoadField: r4 = r2->field_13
    //     0xdc0940: ldur            w4, [x2, #0x13]
    // 0xdc0944: DecompressPointer r4
    //     0xdc0944: add             x4, x4, HEAP, lsl #32
    // 0xdc0948: stur            x4, [fp, #-0x10]
    // 0xdc094c: r0 = LoadClassIdInstr(r3)
    //     0xdc094c: ldur            x0, [x3, #-1]
    //     0xdc0950: ubfx            x0, x0, #0xc, #0x14
    // 0xdc0954: stp             x4, x3, [SP]
    // 0xdc0958: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc0958: movz            x17, #0x3037
    //     0xdc095c: movk            x17, #0x1, lsl #16
    //     0xdc0960: add             lr, x0, x17
    //     0xdc0964: ldr             lr, [x21, lr, lsl #3]
    //     0xdc0968: blr             lr
    // 0xdc096c: LoadField: r2 = r0->field_7
    //     0xdc096c: ldur            x2, [x0, #7]
    // 0xdc0970: r0 = BoxInt64Instr(r2)
    //     0xdc0970: sbfiz           x0, x2, #1, #0x1f
    //     0xdc0974: cmp             x2, x0, asr #1
    //     0xdc0978: b.eq            #0xdc0984
    //     0xdc097c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xdc0980: stur            x2, [x0, #7]
    // 0xdc0984: ldur            x1, [fp, #-0x18]
    // 0xdc0988: ArrayStore: r1[1] = r0  ; List_4
    //     0xdc0988: add             x25, x1, #0x13
    //     0xdc098c: str             w0, [x25]
    //     0xdc0990: tbz             w0, #0, #0xdc09ac
    //     0xdc0994: ldurb           w16, [x1, #-1]
    //     0xdc0998: ldurb           w17, [x0, #-1]
    //     0xdc099c: and             x16, x17, x16, lsr #2
    //     0xdc09a0: tst             x16, HEAP, lsr #32
    //     0xdc09a4: b.eq            #0xdc09ac
    //     0xdc09a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc09ac: ldur            x1, [fp, #-0x18]
    // 0xdc09b0: r16 = "title"
    //     0xdc09b0: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0xdc09b4: ldr             x16, [x16, #0x748]
    // 0xdc09b8: ArrayStore: r1[0] = r16  ; List_4
    //     0xdc09b8: stur            w16, [x1, #0x17]
    // 0xdc09bc: ldur            x2, [fp, #-8]
    // 0xdc09c0: LoadField: r0 = r2->field_f
    //     0xdc09c0: ldur            w0, [x2, #0xf]
    // 0xdc09c4: DecompressPointer r0
    //     0xdc09c4: add             x0, x0, HEAP, lsl #32
    // 0xdc09c8: LoadField: r3 = r0->field_f
    //     0xdc09c8: ldur            w3, [x0, #0xf]
    // 0xdc09cc: DecompressPointer r3
    //     0xdc09cc: add             x3, x3, HEAP, lsl #32
    // 0xdc09d0: r0 = LoadClassIdInstr(r3)
    //     0xdc09d0: ldur            x0, [x3, #-1]
    //     0xdc09d4: ubfx            x0, x0, #0xc, #0x14
    // 0xdc09d8: ldur            x16, [fp, #-0x10]
    // 0xdc09dc: stp             x16, x3, [SP]
    // 0xdc09e0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc09e0: movz            x17, #0x3037
    //     0xdc09e4: movk            x17, #0x1, lsl #16
    //     0xdc09e8: add             lr, x0, x17
    //     0xdc09ec: ldr             lr, [x21, lr, lsl #3]
    //     0xdc09f0: blr             lr
    // 0xdc09f4: LoadField: r1 = r0->field_f
    //     0xdc09f4: ldur            w1, [x0, #0xf]
    // 0xdc09f8: DecompressPointer r1
    //     0xdc09f8: add             x1, x1, HEAP, lsl #32
    // 0xdc09fc: mov             x0, x1
    // 0xdc0a00: ldur            x1, [fp, #-0x18]
    // 0xdc0a04: ArrayStore: r1[3] = r0  ; List_4
    //     0xdc0a04: add             x25, x1, #0x1b
    //     0xdc0a08: str             w0, [x25]
    //     0xdc0a0c: tbz             w0, #0, #0xdc0a28
    //     0xdc0a10: ldurb           w16, [x1, #-1]
    //     0xdc0a14: ldurb           w17, [x0, #-1]
    //     0xdc0a18: and             x16, x17, x16, lsr #2
    //     0xdc0a1c: tst             x16, HEAP, lsr #32
    //     0xdc0a20: b.eq            #0xdc0a28
    //     0xdc0a24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc0a28: ldur            x2, [fp, #-0x18]
    // 0xdc0a2c: r16 = "slug"
    //     0xdc0a2c: add             x16, PP, #0x24, lsl #12  ; [pp+0x249a8] "slug"
    //     0xdc0a30: ldr             x16, [x16, #0x9a8]
    // 0xdc0a34: StoreField: r2->field_1f = r16
    //     0xdc0a34: stur            w16, [x2, #0x1f]
    // 0xdc0a38: ldur            x0, [fp, #-8]
    // 0xdc0a3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdc0a3c: ldur            w1, [x0, #0x17]
    // 0xdc0a40: DecompressPointer r1
    //     0xdc0a40: add             x1, x1, HEAP, lsl #32
    // 0xdc0a44: mov             x0, x1
    // 0xdc0a48: mov             x1, x2
    // 0xdc0a4c: ArrayStore: r1[5] = r0  ; List_4
    //     0xdc0a4c: add             x25, x1, #0x23
    //     0xdc0a50: str             w0, [x25]
    //     0xdc0a54: tbz             w0, #0, #0xdc0a70
    //     0xdc0a58: ldurb           w16, [x1, #-1]
    //     0xdc0a5c: ldurb           w17, [x0, #-1]
    //     0xdc0a60: and             x16, x17, x16, lsr #2
    //     0xdc0a64: tst             x16, HEAP, lsr #32
    //     0xdc0a68: b.eq            #0xdc0a70
    //     0xdc0a6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc0a70: r16 = <String, Object>
    //     0xdc0a70: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xdc0a74: ldr             x16, [x16, #0x790]
    // 0xdc0a78: stp             x2, x16, [SP]
    // 0xdc0a7c: r0 = Map._fromLiteral()
    //     0xdc0a7c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xdc0a80: r16 = "/topic/topic-detail"
    //     0xdc0a80: add             x16, PP, #0x24, lsl #12  ; [pp+0x24998] "/topic/topic-detail"
    //     0xdc0a84: ldr             x16, [x16, #0x998]
    // 0xdc0a88: stp             x16, NULL, [SP, #0x10]
    // 0xdc0a8c: r16 = false
    //     0xdc0a8c: add             x16, NULL, #0x30  ; false
    // 0xdc0a90: stp             x16, x0, [SP]
    // 0xdc0a94: r4 = const [0x1, 0x3, 0x3, 0x1, arguments, 0x1, preventDuplicates, 0x2, null]
    //     0xdc0a94: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f48] List(9) [0x1, 0x3, 0x3, 0x1, "arguments", 0x1, "preventDuplicates", 0x2, Null]
    //     0xdc0a98: ldr             x4, [x4, #0xf48]
    // 0xdc0a9c: r0 = GetNavigation.toNamed()
    //     0xdc0a9c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xdc0aa0: r0 = Null
    //     0xdc0aa0: mov             x0, NULL
    // 0xdc0aa4: LeaveFrame
    //     0xdc0aa4: mov             SP, fp
    //     0xdc0aa8: ldp             fp, lr, [SP], #0x10
    // 0xdc0aac: ret
    //     0xdc0aac: ret             
    // 0xdc0ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0ab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0ab4: b               #0xdc08f0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xdc0ab8, size: 0x128
    // 0xdc0ab8: EnterFrame
    //     0xdc0ab8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0abc: mov             fp, SP
    // 0xdc0ac0: AllocStack(0x30)
    //     0xdc0ac0: sub             SP, SP, #0x30
    // 0xdc0ac4: SetupParameters()
    //     0xdc0ac4: ldr             x0, [fp, #0x10]
    //     0xdc0ac8: ldur            w1, [x0, #0x17]
    //     0xdc0acc: add             x1, x1, HEAP, lsl #32
    //     0xdc0ad0: stur            x1, [fp, #-8]
    // 0xdc0ad4: CheckStackOverflow
    //     0xdc0ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0ad8: cmp             SP, x16
    //     0xdc0adc: b.ls            #0xdc0bd8
    // 0xdc0ae0: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xdc0ae0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc0ae4: ldr             x0, [x0, #0x2670]
    //     0xdc0ae8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdc0aec: cmp             w0, w16
    //     0xdc0af0: b.ne            #0xdc0afc
    //     0xdc0af4: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xdc0af8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xdc0afc: r1 = Null
    //     0xdc0afc: mov             x1, NULL
    // 0xdc0b00: r2 = 4
    //     0xdc0b00: movz            x2, #0x4
    // 0xdc0b04: r0 = AllocateArray()
    //     0xdc0b04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc0b08: mov             x1, x0
    // 0xdc0b0c: stur            x1, [fp, #-0x10]
    // 0xdc0b10: r16 = "id"
    //     0xdc0b10: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xdc0b14: ldr             x16, [x16, #0x740]
    // 0xdc0b18: StoreField: r1->field_f = r16
    //     0xdc0b18: stur            w16, [x1, #0xf]
    // 0xdc0b1c: ldur            x0, [fp, #-8]
    // 0xdc0b20: LoadField: r2 = r0->field_f
    //     0xdc0b20: ldur            w2, [x0, #0xf]
    // 0xdc0b24: DecompressPointer r2
    //     0xdc0b24: add             x2, x2, HEAP, lsl #32
    // 0xdc0b28: LoadField: r3 = r2->field_b
    //     0xdc0b28: ldur            w3, [x2, #0xb]
    // 0xdc0b2c: DecompressPointer r3
    //     0xdc0b2c: add             x3, x3, HEAP, lsl #32
    // 0xdc0b30: LoadField: r2 = r0->field_1b
    //     0xdc0b30: ldur            w2, [x0, #0x1b]
    // 0xdc0b34: DecompressPointer r2
    //     0xdc0b34: add             x2, x2, HEAP, lsl #32
    // 0xdc0b38: r0 = LoadClassIdInstr(r3)
    //     0xdc0b38: ldur            x0, [x3, #-1]
    //     0xdc0b3c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc0b40: stp             x2, x3, [SP]
    // 0xdc0b44: r0 = GDT[cid_x0 + 0x13037]()
    //     0xdc0b44: movz            x17, #0x3037
    //     0xdc0b48: movk            x17, #0x1, lsl #16
    //     0xdc0b4c: add             lr, x0, x17
    //     0xdc0b50: ldr             lr, [x21, lr, lsl #3]
    //     0xdc0b54: blr             lr
    // 0xdc0b58: LoadField: r2 = r0->field_7
    //     0xdc0b58: ldur            x2, [x0, #7]
    // 0xdc0b5c: r0 = BoxInt64Instr(r2)
    //     0xdc0b5c: sbfiz           x0, x2, #1, #0x1f
    //     0xdc0b60: cmp             x2, x0, asr #1
    //     0xdc0b64: b.eq            #0xdc0b70
    //     0xdc0b68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xdc0b6c: stur            x2, [x0, #7]
    // 0xdc0b70: ldur            x1, [fp, #-0x10]
    // 0xdc0b74: ArrayStore: r1[1] = r0  ; List_4
    //     0xdc0b74: add             x25, x1, #0x13
    //     0xdc0b78: str             w0, [x25]
    //     0xdc0b7c: tbz             w0, #0, #0xdc0b98
    //     0xdc0b80: ldurb           w16, [x1, #-1]
    //     0xdc0b84: ldurb           w17, [x0, #-1]
    //     0xdc0b88: and             x16, x17, x16, lsr #2
    //     0xdc0b8c: tst             x16, HEAP, lsr #32
    //     0xdc0b90: b.eq            #0xdc0b98
    //     0xdc0b94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc0b98: r16 = <String, int>
    //     0xdc0b98: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xdc0b9c: ldur            lr, [fp, #-0x10]
    // 0xdc0ba0: stp             lr, x16, [SP]
    // 0xdc0ba4: r0 = Map._fromLiteral()
    //     0xdc0ba4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xdc0ba8: r16 = "/article/article-detail"
    //     0xdc0ba8: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xdc0bac: ldr             x16, [x16, #0x3d0]
    // 0xdc0bb0: stp             x16, NULL, [SP, #0x10]
    // 0xdc0bb4: r16 = false
    //     0xdc0bb4: add             x16, NULL, #0x30  ; false
    // 0xdc0bb8: stp             x16, x0, [SP]
    // 0xdc0bbc: r4 = const [0x1, 0x3, 0x3, 0x1, arguments, 0x1, preventDuplicates, 0x2, null]
    //     0xdc0bbc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f48] List(9) [0x1, 0x3, 0x3, 0x1, "arguments", 0x1, "preventDuplicates", 0x2, Null]
    //     0xdc0bc0: ldr             x4, [x4, #0xf48]
    // 0xdc0bc4: r0 = GetNavigation.toNamed()
    //     0xdc0bc4: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xdc0bc8: r0 = Null
    //     0xdc0bc8: mov             x0, NULL
    // 0xdc0bcc: LeaveFrame
    //     0xdc0bcc: mov             SP, fp
    //     0xdc0bd0: ldp             fp, lr, [SP], #0x10
    // 0xdc0bd4: ret
    //     0xdc0bd4: ret             
    // 0xdc0bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0bd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0bdc: b               #0xdc0ae0
  }
}

// class id: 2503, size: 0x10, field offset: 0x8
class TagWrapperExtension extends HtmlExtension {

  _ build(/* No info */) {
    // ** addr: 0xdbfa10, size: 0x110
    // 0xdbfa10: EnterFrame
    //     0xdbfa10: stp             fp, lr, [SP, #-0x10]!
    //     0xdbfa14: mov             fp, SP
    // 0xdbfa18: AllocStack(0x38)
    //     0xdbfa18: sub             SP, SP, #0x38
    // 0xdbfa1c: SetupParameters(TagWrapperExtension this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdbfa1c: mov             x0, x2
    //     0xdbfa20: stur            x2, [fp, #-0x10]
    //     0xdbfa24: mov             x2, x1
    //     0xdbfa28: stur            x1, [fp, #-8]
    // 0xdbfa2c: CheckStackOverflow
    //     0xdbfa2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbfa30: cmp             SP, x16
    //     0xdbfa34: b.ls            #0xdbfb14
    // 0xdbfa38: mov             x1, x0
    // 0xdbfa3c: r0 = inlineSpanChildren()
    //     0xdbfa3c: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdbfa40: stur            x0, [fp, #-0x18]
    // 0xdbfa44: cmp             w0, NULL
    // 0xdbfa48: b.eq            #0xdbfb1c
    // 0xdbfa4c: ldur            x1, [fp, #-0x10]
    // 0xdbfa50: LoadField: r2 = r1->field_f
    //     0xdbfa50: ldur            w2, [x1, #0xf]
    // 0xdbfa54: DecompressPointer r2
    //     0xdbfa54: add             x2, x2, HEAP, lsl #32
    // 0xdbfa58: cmp             w2, NULL
    // 0xdbfa5c: b.ne            #0xdbfa68
    // 0xdbfa60: r2 = Null
    //     0xdbfa60: mov             x2, NULL
    // 0xdbfa64: b               #0xdbfa74
    // 0xdbfa68: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xdbfa68: ldur            w3, [x2, #0x17]
    // 0xdbfa6c: DecompressPointer r3
    //     0xdbfa6c: add             x3, x3, HEAP, lsl #32
    // 0xdbfa70: mov             x2, x3
    // 0xdbfa74: cmp             w2, NULL
    // 0xdbfa78: b.ne            #0xdbfa98
    // 0xdbfa7c: r0 = Style()
    //     0xdbfa7c: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xdbfa80: mov             x1, x0
    // 0xdbfa84: stur            x0, [fp, #-0x20]
    // 0xdbfa88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdbfa88: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdbfa8c: r0 = Style()
    //     0xdbfa8c: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xdbfa90: ldur            x3, [fp, #-0x20]
    // 0xdbfa94: b               #0xdbfa9c
    // 0xdbfa98: mov             x3, x2
    // 0xdbfa9c: ldur            x0, [fp, #-8]
    // 0xdbfaa0: stur            x3, [fp, #-0x20]
    // 0xdbfaa4: r0 = CssBoxWidget()
    //     0xdbfaa4: bl              #0xa249dc  ; AllocateCssBoxWidgetStub -> CssBoxWidget (size=0x24)
    // 0xdbfaa8: mov             x1, x0
    // 0xdbfaac: ldur            x2, [fp, #-0x18]
    // 0xdbfab0: ldur            x3, [fp, #-0x20]
    // 0xdbfab4: stur            x0, [fp, #-0x18]
    // 0xdbfab8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xdbfab8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xdbfabc: r0 = CssBoxWidget.withInlineSpanChildren()
    //     0xdbfabc: bl              #0xa23eec  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::CssBoxWidget.withInlineSpanChildren
    // 0xdbfac0: ldur            x0, [fp, #-8]
    // 0xdbfac4: LoadField: r1 = r0->field_b
    //     0xdbfac4: ldur            w1, [x0, #0xb]
    // 0xdbfac8: DecompressPointer r1
    //     0xdbfac8: add             x1, x1, HEAP, lsl #32
    // 0xdbfacc: ldur            x16, [fp, #-0x10]
    // 0xdbfad0: stp             x16, x1, [SP, #8]
    // 0xdbfad4: ldur            x16, [fp, #-0x18]
    // 0xdbfad8: str             x16, [SP]
    // 0xdbfadc: mov             x0, x1
    // 0xdbfae0: ClosureCall
    //     0xdbfae0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xdbfae4: ldur            x2, [x0, #0x1f]
    //     0xdbfae8: blr             x2
    // 0xdbfaec: stur            x0, [fp, #-8]
    // 0xdbfaf0: r0 = WidgetSpan()
    //     0xdbfaf0: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbfaf4: ldur            x1, [fp, #-8]
    // 0xdbfaf8: StoreField: r0->field_13 = r1
    //     0xdbfaf8: stur            w1, [x0, #0x13]
    // 0xdbfafc: r1 = Instance_PlaceholderAlignment
    //     0xdbfafc: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbfb00: ldr             x1, [x1, #0xde8]
    // 0xdbfb04: StoreField: r0->field_b = r1
    //     0xdbfb04: stur            w1, [x0, #0xb]
    // 0xdbfb08: LeaveFrame
    //     0xdbfb08: mov             SP, fp
    //     0xdbfb0c: ldp             fp, lr, [SP], #0x10
    // 0xdbfb10: ret
    //     0xdbfb10: ret             
    // 0xdbfb14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbfb14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbfb18: b               #0xdbfa38
    // 0xdbfb1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdbfb1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 2504, size: 0xc, field offset: 0x8
class SpanStyleExtension extends HtmlExtension {

  get _ supportedTags(/* No info */) {
    // ** addr: 0xc411ac, size: 0xac
    // 0xc411ac: EnterFrame
    //     0xc411ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc411b0: mov             fp, SP
    // 0xc411b4: AllocStack(0x10)
    //     0xc411b4: sub             SP, SP, #0x10
    // 0xc411b8: CheckStackOverflow
    //     0xc411b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc411bc: cmp             SP, x16
    //     0xc411c0: b.ls            #0xc41250
    // 0xc411c4: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xc411c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc411c8: ldr             x0, [x0, #0x778]
    //     0xc411cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc411d0: cmp             w0, w16
    //     0xc411d4: b.ne            #0xc411e0
    //     0xc411d8: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xc411dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc411e0: r1 = <String>
    //     0xc411e0: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc411e4: stur            x0, [fp, #-8]
    // 0xc411e8: r0 = _Set()
    //     0xc411e8: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xc411ec: mov             x1, x0
    // 0xc411f0: ldur            x0, [fp, #-8]
    // 0xc411f4: stur            x1, [fp, #-0x10]
    // 0xc411f8: StoreField: r1->field_1b = r0
    //     0xc411f8: stur            w0, [x1, #0x1b]
    // 0xc411fc: StoreField: r1->field_b = rZR
    //     0xc411fc: stur            wzr, [x1, #0xb]
    // 0xc41200: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xc41200: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc41204: ldr             x0, [x0, #0x780]
    //     0xc41208: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc4120c: cmp             w0, w16
    //     0xc41210: b.ne            #0xc4121c
    //     0xc41214: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xc41218: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xc4121c: mov             x1, x0
    // 0xc41220: ldur            x0, [fp, #-0x10]
    // 0xc41224: StoreField: r0->field_f = r1
    //     0xc41224: stur            w1, [x0, #0xf]
    // 0xc41228: StoreField: r0->field_13 = rZR
    //     0xc41228: stur            wzr, [x0, #0x13]
    // 0xc4122c: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc4122c: stur            wzr, [x0, #0x17]
    // 0xc41230: mov             x1, x0
    // 0xc41234: r2 = "span"
    //     0xc41234: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cad0] "span"
    //     0xc41238: ldr             x2, [x2, #0xad0]
    // 0xc4123c: r0 = add()
    //     0xc4123c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xc41240: ldur            x0, [fp, #-0x10]
    // 0xc41244: LeaveFrame
    //     0xc41244: mov             SP, fp
    //     0xc41248: ldp             fp, lr, [SP], #0x10
    // 0xc4124c: ret
    //     0xc4124c: ret             
    // 0xc41250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41250: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41254: b               #0xc411c4
  }
  _ build(/* No info */) {
    // ** addr: 0xdbf570, size: 0x470
    // 0xdbf570: EnterFrame
    //     0xdbf570: stp             fp, lr, [SP, #-0x10]!
    //     0xdbf574: mov             fp, SP
    // 0xdbf578: AllocStack(0x50)
    //     0xdbf578: sub             SP, SP, #0x50
    // 0xdbf57c: SetupParameters(SpanStyleExtension this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdbf57c: mov             x0, x2
    //     0xdbf580: stur            x2, [fp, #-0x10]
    //     0xdbf584: mov             x2, x1
    //     0xdbf588: stur            x1, [fp, #-8]
    // 0xdbf58c: CheckStackOverflow
    //     0xdbf58c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbf590: cmp             SP, x16
    //     0xdbf594: b.ls            #0xdbf9d8
    // 0xdbf598: mov             x1, x0
    // 0xdbf59c: r0 = attributes()
    //     0xdbf59c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf5a0: mov             x1, x0
    // 0xdbf5a4: r2 = "style"
    //     0xdbf5a4: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xdbf5a8: ldr             x2, [x2, #0x9b8]
    // 0xdbf5ac: stur            x0, [fp, #-0x18]
    // 0xdbf5b0: r0 = _getValueOrData()
    //     0xdbf5b0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf5b4: mov             x1, x0
    // 0xdbf5b8: ldur            x0, [fp, #-0x18]
    // 0xdbf5bc: LoadField: r2 = r0->field_f
    //     0xdbf5bc: ldur            w2, [x0, #0xf]
    // 0xdbf5c0: DecompressPointer r2
    //     0xdbf5c0: add             x2, x2, HEAP, lsl #32
    // 0xdbf5c4: cmp             w2, w1
    // 0xdbf5c8: b.ne            #0xdbf5d4
    // 0xdbf5cc: r0 = Null
    //     0xdbf5cc: mov             x0, NULL
    // 0xdbf5d0: b               #0xdbf5d8
    // 0xdbf5d4: mov             x0, x1
    // 0xdbf5d8: r1 = LoadClassIdInstr(r0)
    //     0xdbf5d8: ldur            x1, [x0, #-1]
    //     0xdbf5dc: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf5e0: r16 = "line-height:2;"
    //     0xdbf5e0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ef8] "line-height:2;"
    //     0xdbf5e4: ldr             x16, [x16, #0xef8]
    // 0xdbf5e8: stp             x16, x0, [SP]
    // 0xdbf5ec: mov             x0, x1
    // 0xdbf5f0: mov             lr, x0
    // 0xdbf5f4: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf5f8: blr             lr
    // 0xdbf5fc: tbz             w0, #4, #0xdbf808
    // 0xdbf600: ldur            x1, [fp, #-0x10]
    // 0xdbf604: r0 = attributes()
    //     0xdbf604: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf608: mov             x1, x0
    // 0xdbf60c: r2 = "style"
    //     0xdbf60c: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xdbf610: ldr             x2, [x2, #0x9b8]
    // 0xdbf614: stur            x0, [fp, #-0x18]
    // 0xdbf618: r0 = _getValueOrData()
    //     0xdbf618: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf61c: mov             x1, x0
    // 0xdbf620: ldur            x0, [fp, #-0x18]
    // 0xdbf624: LoadField: r2 = r0->field_f
    //     0xdbf624: ldur            w2, [x0, #0xf]
    // 0xdbf628: DecompressPointer r2
    //     0xdbf628: add             x2, x2, HEAP, lsl #32
    // 0xdbf62c: cmp             w2, w1
    // 0xdbf630: b.ne            #0xdbf63c
    // 0xdbf634: r0 = Null
    //     0xdbf634: mov             x0, NULL
    // 0xdbf638: b               #0xdbf640
    // 0xdbf63c: mov             x0, x1
    // 0xdbf640: r1 = LoadClassIdInstr(r0)
    //     0xdbf640: ldur            x1, [x0, #-1]
    //     0xdbf644: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf648: r16 = "font-size:18px;"
    //     0xdbf648: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f00] "font-size:18px;"
    //     0xdbf64c: ldr             x16, [x16, #0xf00]
    // 0xdbf650: stp             x16, x0, [SP]
    // 0xdbf654: mov             x0, x1
    // 0xdbf658: mov             lr, x0
    // 0xdbf65c: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf660: blr             lr
    // 0xdbf664: tbz             w0, #4, #0xdbf808
    // 0xdbf668: ldur            x1, [fp, #-0x10]
    // 0xdbf66c: r0 = attributes()
    //     0xdbf66c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf670: mov             x1, x0
    // 0xdbf674: r2 = "style"
    //     0xdbf674: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xdbf678: ldr             x2, [x2, #0x9b8]
    // 0xdbf67c: stur            x0, [fp, #-0x18]
    // 0xdbf680: r0 = _getValueOrData()
    //     0xdbf680: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf684: mov             x1, x0
    // 0xdbf688: ldur            x0, [fp, #-0x18]
    // 0xdbf68c: LoadField: r2 = r0->field_f
    //     0xdbf68c: ldur            w2, [x0, #0xf]
    // 0xdbf690: DecompressPointer r2
    //     0xdbf690: add             x2, x2, HEAP, lsl #32
    // 0xdbf694: cmp             w2, w1
    // 0xdbf698: b.ne            #0xdbf6a4
    // 0xdbf69c: r0 = Null
    //     0xdbf69c: mov             x0, NULL
    // 0xdbf6a0: b               #0xdbf6a8
    // 0xdbf6a4: mov             x0, x1
    // 0xdbf6a8: r1 = LoadClassIdInstr(r0)
    //     0xdbf6a8: ldur            x1, [x0, #-1]
    //     0xdbf6ac: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf6b0: r16 = "font-size: 24px;"
    //     0xdbf6b0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f08] "font-size: 24px;"
    //     0xdbf6b4: ldr             x16, [x16, #0xf08]
    // 0xdbf6b8: stp             x16, x0, [SP]
    // 0xdbf6bc: mov             x0, x1
    // 0xdbf6c0: mov             lr, x0
    // 0xdbf6c4: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf6c8: blr             lr
    // 0xdbf6cc: tbz             w0, #4, #0xdbf808
    // 0xdbf6d0: ldur            x1, [fp, #-0x10]
    // 0xdbf6d4: r0 = attributes()
    //     0xdbf6d4: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf6d8: mov             x1, x0
    // 0xdbf6dc: r2 = "style"
    //     0xdbf6dc: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xdbf6e0: ldr             x2, [x2, #0x9b8]
    // 0xdbf6e4: stur            x0, [fp, #-0x18]
    // 0xdbf6e8: r0 = _getValueOrData()
    //     0xdbf6e8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf6ec: mov             x1, x0
    // 0xdbf6f0: ldur            x0, [fp, #-0x18]
    // 0xdbf6f4: LoadField: r2 = r0->field_f
    //     0xdbf6f4: ldur            w2, [x0, #0xf]
    // 0xdbf6f8: DecompressPointer r2
    //     0xdbf6f8: add             x2, x2, HEAP, lsl #32
    // 0xdbf6fc: cmp             w2, w1
    // 0xdbf700: b.ne            #0xdbf70c
    // 0xdbf704: r0 = Null
    //     0xdbf704: mov             x0, NULL
    // 0xdbf708: b               #0xdbf710
    // 0xdbf70c: mov             x0, x1
    // 0xdbf710: r1 = LoadClassIdInstr(r0)
    //     0xdbf710: ldur            x1, [x0, #-1]
    //     0xdbf714: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf718: r16 = "font-size:24px;"
    //     0xdbf718: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f10] "font-size:24px;"
    //     0xdbf71c: ldr             x16, [x16, #0xf10]
    // 0xdbf720: stp             x16, x0, [SP]
    // 0xdbf724: mov             x0, x1
    // 0xdbf728: mov             lr, x0
    // 0xdbf72c: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf730: blr             lr
    // 0xdbf734: tbz             w0, #4, #0xdbf808
    // 0xdbf738: ldur            x1, [fp, #-0x10]
    // 0xdbf73c: r0 = attributes()
    //     0xdbf73c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf740: mov             x1, x0
    // 0xdbf744: r2 = "style"
    //     0xdbf744: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xdbf748: ldr             x2, [x2, #0x9b8]
    // 0xdbf74c: stur            x0, [fp, #-0x18]
    // 0xdbf750: r0 = _getValueOrData()
    //     0xdbf750: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf754: mov             x1, x0
    // 0xdbf758: ldur            x0, [fp, #-0x18]
    // 0xdbf75c: LoadField: r2 = r0->field_f
    //     0xdbf75c: ldur            w2, [x0, #0xf]
    // 0xdbf760: DecompressPointer r2
    //     0xdbf760: add             x2, x2, HEAP, lsl #32
    // 0xdbf764: cmp             w2, w1
    // 0xdbf768: b.ne            #0xdbf774
    // 0xdbf76c: r0 = Null
    //     0xdbf76c: mov             x0, NULL
    // 0xdbf770: b               #0xdbf778
    // 0xdbf774: mov             x0, x1
    // 0xdbf778: r1 = LoadClassIdInstr(r0)
    //     0xdbf778: ldur            x1, [x0, #-1]
    //     0xdbf77c: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf780: r16 = "font-size:24px"
    //     0xdbf780: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f18] "font-size:24px"
    //     0xdbf784: ldr             x16, [x16, #0xf18]
    // 0xdbf788: stp             x16, x0, [SP]
    // 0xdbf78c: mov             x0, x1
    // 0xdbf790: mov             lr, x0
    // 0xdbf794: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf798: blr             lr
    // 0xdbf79c: tbz             w0, #4, #0xdbf808
    // 0xdbf7a0: ldur            x1, [fp, #-0x10]
    // 0xdbf7a4: r0 = attributes()
    //     0xdbf7a4: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xdbf7a8: mov             x1, x0
    // 0xdbf7ac: r2 = "dir"
    //     0xdbf7ac: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f20] "dir"
    //     0xdbf7b0: ldr             x2, [x2, #0xf20]
    // 0xdbf7b4: stur            x0, [fp, #-0x18]
    // 0xdbf7b8: r0 = _getValueOrData()
    //     0xdbf7b8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdbf7bc: mov             x1, x0
    // 0xdbf7c0: ldur            x0, [fp, #-0x18]
    // 0xdbf7c4: LoadField: r2 = r0->field_f
    //     0xdbf7c4: ldur            w2, [x0, #0xf]
    // 0xdbf7c8: DecompressPointer r2
    //     0xdbf7c8: add             x2, x2, HEAP, lsl #32
    // 0xdbf7cc: cmp             w2, w1
    // 0xdbf7d0: b.ne            #0xdbf7dc
    // 0xdbf7d4: r0 = Null
    //     0xdbf7d4: mov             x0, NULL
    // 0xdbf7d8: b               #0xdbf7e0
    // 0xdbf7dc: mov             x0, x1
    // 0xdbf7e0: r1 = LoadClassIdInstr(r0)
    //     0xdbf7e0: ldur            x1, [x0, #-1]
    //     0xdbf7e4: ubfx            x1, x1, #0xc, #0x14
    // 0xdbf7e8: r16 = "rtl"
    //     0xdbf7e8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f28] "rtl"
    //     0xdbf7ec: ldr             x16, [x16, #0xf28]
    // 0xdbf7f0: stp             x16, x0, [SP]
    // 0xdbf7f4: mov             x0, x1
    // 0xdbf7f8: mov             lr, x0
    // 0xdbf7fc: ldr             lr, [x21, lr, lsl #3]
    // 0xdbf800: blr             lr
    // 0xdbf804: tbnz            w0, #4, #0xdbf954
    // 0xdbf808: ldur            x1, [fp, #-0x10]
    // 0xdbf80c: r0 = inlineSpanChildren()
    //     0xdbf80c: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdbf810: stur            x0, [fp, #-0x18]
    // 0xdbf814: cmp             w0, NULL
    // 0xdbf818: b.ne            #0xdbf824
    // 0xdbf81c: r2 = Null
    //     0xdbf81c: mov             x2, NULL
    // 0xdbf820: b               #0xdbf85c
    // 0xdbf824: r1 = Function '<anonymous closure>':.
    //     0xdbf824: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ea8] AnonymousClosure: (0xdbf9e0), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] SpanStyleExtension::build (0xdbf570)
    //     0xdbf828: ldr             x1, [x1, #0xea8]
    // 0xdbf82c: r2 = Null
    //     0xdbf82c: mov             x2, NULL
    // 0xdbf830: r0 = AllocateClosure()
    //     0xdbf830: bl              #0xec1630  ; AllocateClosureStub
    // 0xdbf834: r16 = <String>
    //     0xdbf834: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xdbf838: ldur            lr, [fp, #-0x18]
    // 0xdbf83c: stp             lr, x16, [SP, #8]
    // 0xdbf840: str             x0, [SP]
    // 0xdbf844: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xdbf844: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xdbf848: r0 = map()
    //     0xdbf848: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xdbf84c: mov             x1, x0
    // 0xdbf850: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdbf850: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdbf854: r0 = join()
    //     0xdbf854: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xdbf858: mov             x2, x0
    // 0xdbf85c: ldur            x1, [fp, #-0x10]
    // 0xdbf860: stur            x2, [fp, #-0x20]
    // 0xdbf864: LoadField: r0 = r1->field_f
    //     0xdbf864: ldur            w0, [x1, #0xf]
    // 0xdbf868: DecompressPointer r0
    //     0xdbf868: add             x0, x0, HEAP, lsl #32
    // 0xdbf86c: cmp             w0, NULL
    // 0xdbf870: b.ne            #0xdbf87c
    // 0xdbf874: r1 = Null
    //     0xdbf874: mov             x1, NULL
    // 0xdbf878: b               #0xdbf884
    // 0xdbf87c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xdbf87c: ldur            w1, [x0, #0x17]
    // 0xdbf880: DecompressPointer r1
    //     0xdbf880: add             x1, x1, HEAP, lsl #32
    // 0xdbf884: stur            x1, [fp, #-0x18]
    // 0xdbf888: cmp             w1, NULL
    // 0xdbf88c: b.ne            #0xdbf89c
    // 0xdbf890: mov             x0, x2
    // 0xdbf894: r1 = Null
    //     0xdbf894: mov             x1, NULL
    // 0xdbf898: b               #0xdbf920
    // 0xdbf89c: ldur            x0, [fp, #-8]
    // 0xdbf8a0: LoadField: r3 = r0->field_7
    //     0xdbf8a0: ldur            w3, [x0, #7]
    // 0xdbf8a4: DecompressPointer r3
    //     0xdbf8a4: add             x3, x3, HEAP, lsl #32
    // 0xdbf8a8: str             x3, [SP]
    // 0xdbf8ac: mov             x0, x3
    // 0xdbf8b0: ClosureCall
    //     0xdbf8b0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xdbf8b4: ldur            x2, [x0, #0x1f]
    //     0xdbf8b8: blr             x2
    // 0xdbf8bc: LoadField: d0 = r0->field_7
    //     0xdbf8bc: ldur            d0, [x0, #7]
    // 0xdbf8c0: stur            d0, [fp, #-0x28]
    // 0xdbf8c4: r0 = FontSize()
    //     0xdbf8c4: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xdbf8c8: ldur            d0, [fp, #-0x28]
    // 0xdbf8cc: StoreField: r0->field_7 = d0
    //     0xdbf8cc: stur            d0, [x0, #7]
    // 0xdbf8d0: r1 = Instance_Unit
    //     0xdbf8d0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xdbf8d4: ldr             x1, [x1, #0xa98]
    // 0xdbf8d8: StoreField: r0->field_f = r1
    //     0xdbf8d8: stur            w1, [x0, #0xf]
    // 0xdbf8dc: r16 = Instance_TextDirection
    //     0xdbf8dc: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xdbf8e0: r30 = Instance_TextAlign
    //     0xdbf8e0: ldr             lr, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0xdbf8e4: stp             lr, x16, [SP, #0x18]
    // 0xdbf8e8: r16 = "OmarNaskh"
    //     0xdbf8e8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xdbf8ec: ldr             x16, [x16, #0xbc8]
    // 0xdbf8f0: stp             x0, x16, [SP, #8]
    // 0xdbf8f4: r16 = Instance_LineHeight
    //     0xdbf8f4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f30] Obj!LineHeight@e0fb01
    //     0xdbf8f8: ldr             x16, [x16, #0xf30]
    // 0xdbf8fc: str             x16, [SP]
    // 0xdbf900: ldur            x1, [fp, #-0x18]
    // 0xdbf904: r4 = const [0, 0x6, 0x5, 0x1, direction, 0x1, fontFamily, 0x3, fontSize, 0x4, lineHeight, 0x5, textAlign, 0x2, null]
    //     0xdbf904: add             x4, PP, #0x36, lsl #12  ; [pp+0x36f38] List(15) [0, 0x6, 0x5, 0x1, "direction", 0x1, "fontFamily", 0x3, "fontSize", 0x4, "lineHeight", 0x5, "textAlign", 0x2, Null]
    //     0xdbf908: ldr             x4, [x4, #0xf38]
    // 0xdbf90c: r0 = copyWith()
    //     0xdbf90c: bl              #0x9ab1ac  ; [package:flutter_html/src/style.dart] Style::copyWith
    // 0xdbf910: mov             x1, x0
    // 0xdbf914: r0 = generateTextStyle()
    //     0xdbf914: bl              #0xa241c0  ; [package:flutter_html/src/style.dart] Style::generateTextStyle
    // 0xdbf918: mov             x1, x0
    // 0xdbf91c: ldur            x0, [fp, #-0x20]
    // 0xdbf920: stur            x1, [fp, #-8]
    // 0xdbf924: r0 = TextSpan()
    //     0xdbf924: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xdbf928: mov             x1, x0
    // 0xdbf92c: ldur            x0, [fp, #-0x20]
    // 0xdbf930: StoreField: r1->field_b = r0
    //     0xdbf930: stur            w0, [x1, #0xb]
    // 0xdbf934: r0 = Instance__DeferringMouseCursor
    //     0xdbf934: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xdbf938: ArrayStore: r1[0] = r0  ; List_4
    //     0xdbf938: stur            w0, [x1, #0x17]
    // 0xdbf93c: ldur            x0, [fp, #-8]
    // 0xdbf940: StoreField: r1->field_7 = r0
    //     0xdbf940: stur            w0, [x1, #7]
    // 0xdbf944: mov             x0, x1
    // 0xdbf948: LeaveFrame
    //     0xdbf948: mov             SP, fp
    //     0xdbf94c: ldp             fp, lr, [SP], #0x10
    // 0xdbf950: ret
    //     0xdbf950: ret             
    // 0xdbf954: ldur            x1, [fp, #-0x10]
    // 0xdbf958: r0 = inlineSpanChildren()
    //     0xdbf958: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdbf95c: cmp             w0, NULL
    // 0xdbf960: b.ne            #0xdbf97c
    // 0xdbf964: r1 = <InlineSpan>
    //     0xdbf964: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xdbf968: ldr             x1, [x1, #0x5f0]
    // 0xdbf96c: r2 = 0
    //     0xdbf96c: movz            x2, #0
    // 0xdbf970: r0 = _GrowableList()
    //     0xdbf970: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xdbf974: mov             x2, x0
    // 0xdbf978: b               #0xdbf980
    // 0xdbf97c: mov             x2, x0
    // 0xdbf980: stur            x2, [fp, #-8]
    // 0xdbf984: r0 = Style()
    //     0xdbf984: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xdbf988: mov             x1, x0
    // 0xdbf98c: stur            x0, [fp, #-0x10]
    // 0xdbf990: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdbf990: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdbf994: r0 = Style()
    //     0xdbf994: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xdbf998: r0 = CssBoxWidget()
    //     0xdbf998: bl              #0xa249dc  ; AllocateCssBoxWidgetStub -> CssBoxWidget (size=0x24)
    // 0xdbf99c: mov             x1, x0
    // 0xdbf9a0: ldur            x2, [fp, #-8]
    // 0xdbf9a4: ldur            x3, [fp, #-0x10]
    // 0xdbf9a8: stur            x0, [fp, #-8]
    // 0xdbf9ac: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xdbf9ac: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xdbf9b0: r0 = CssBoxWidget.withInlineSpanChildren()
    //     0xdbf9b0: bl              #0xa23eec  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::CssBoxWidget.withInlineSpanChildren
    // 0xdbf9b4: r0 = WidgetSpan()
    //     0xdbf9b4: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbf9b8: ldur            x1, [fp, #-8]
    // 0xdbf9bc: StoreField: r0->field_13 = r1
    //     0xdbf9bc: stur            w1, [x0, #0x13]
    // 0xdbf9c0: r1 = Instance_PlaceholderAlignment
    //     0xdbf9c0: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbf9c4: ldr             x1, [x1, #0xde8]
    // 0xdbf9c8: StoreField: r0->field_b = r1
    //     0xdbf9c8: stur            w1, [x0, #0xb]
    // 0xdbf9cc: LeaveFrame
    //     0xdbf9cc: mov             SP, fp
    //     0xdbf9d0: ldp             fp, lr, [SP], #0x10
    // 0xdbf9d4: ret
    //     0xdbf9d4: ret             
    // 0xdbf9d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbf9d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbf9dc: b               #0xdbf598
  }
  [closure] String <anonymous closure>(dynamic, InlineSpan) {
    // ** addr: 0xdbf9e0, size: 0x30
    // 0xdbf9e0: EnterFrame
    //     0xdbf9e0: stp             fp, lr, [SP, #-0x10]!
    //     0xdbf9e4: mov             fp, SP
    // 0xdbf9e8: CheckStackOverflow
    //     0xdbf9e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbf9ec: cmp             SP, x16
    //     0xdbf9f0: b.ls            #0xdbfa08
    // 0xdbf9f4: ldr             x1, [fp, #0x10]
    // 0xdbf9f8: r0 = toPlainText()
    //     0xdbf9f8: bl              #0x684190  ; [package:flutter/src/painting/inline_span.dart] InlineSpan::toPlainText
    // 0xdbf9fc: LeaveFrame
    //     0xdbf9fc: mov             SP, fp
    //     0xdbfa00: ldp             fp, lr, [SP], #0x10
    // 0xdbfa04: ret
    //     0xdbfa04: ret             
    // 0xdbfa08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbfa08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbfa0c: b               #0xdbf9f4
  }
}

// class id: 2505, size: 0x10, field offset: 0x8
class TagStyleExtension extends HtmlExtension {

  _ build(/* No info */) {
    // ** addr: 0xdbf43c, size: 0x134
    // 0xdbf43c: EnterFrame
    //     0xdbf43c: stp             fp, lr, [SP, #-0x10]!
    //     0xdbf440: mov             fp, SP
    // 0xdbf444: AllocStack(0x38)
    //     0xdbf444: sub             SP, SP, #0x38
    // 0xdbf448: SetupParameters(TagStyleExtension this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xdbf448: mov             x0, x2
    //     0xdbf44c: stur            x2, [fp, #-0x10]
    //     0xdbf450: mov             x2, x1
    //     0xdbf454: stur            x1, [fp, #-8]
    // 0xdbf458: CheckStackOverflow
    //     0xdbf458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdbf45c: cmp             SP, x16
    //     0xdbf460: b.ls            #0xdbf568
    // 0xdbf464: mov             x1, x0
    // 0xdbf468: r0 = inlineSpanChildren()
    //     0xdbf468: bl              #0xdbd3a4  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::inlineSpanChildren
    // 0xdbf46c: cmp             w0, NULL
    // 0xdbf470: b.ne            #0xdbf48c
    // 0xdbf474: r1 = <InlineSpan>
    //     0xdbf474: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xdbf478: ldr             x1, [x1, #0x5f0]
    // 0xdbf47c: r2 = 0
    //     0xdbf47c: movz            x2, #0
    // 0xdbf480: r0 = _GrowableList()
    //     0xdbf480: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xdbf484: mov             x2, x0
    // 0xdbf488: b               #0xdbf490
    // 0xdbf48c: mov             x2, x0
    // 0xdbf490: ldur            x0, [fp, #-0x10]
    // 0xdbf494: stur            x2, [fp, #-0x18]
    // 0xdbf498: LoadField: r1 = r0->field_f
    //     0xdbf498: ldur            w1, [x0, #0xf]
    // 0xdbf49c: DecompressPointer r1
    //     0xdbf49c: add             x1, x1, HEAP, lsl #32
    // 0xdbf4a0: cmp             w1, NULL
    // 0xdbf4a4: b.ne            #0xdbf4b0
    // 0xdbf4a8: r1 = Null
    //     0xdbf4a8: mov             x1, NULL
    // 0xdbf4ac: b               #0xdbf4bc
    // 0xdbf4b0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xdbf4b0: ldur            w3, [x1, #0x17]
    // 0xdbf4b4: DecompressPointer r3
    //     0xdbf4b4: add             x3, x3, HEAP, lsl #32
    // 0xdbf4b8: mov             x1, x3
    // 0xdbf4bc: cmp             w1, NULL
    // 0xdbf4c0: b.ne            #0xdbf4dc
    // 0xdbf4c4: r0 = Style()
    //     0xdbf4c4: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xdbf4c8: mov             x1, x0
    // 0xdbf4cc: stur            x0, [fp, #-0x20]
    // 0xdbf4d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdbf4d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdbf4d4: r0 = Style()
    //     0xdbf4d4: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xdbf4d8: ldur            x1, [fp, #-0x20]
    // 0xdbf4dc: ldur            x0, [fp, #-8]
    // 0xdbf4e0: LoadField: r2 = r0->field_b
    //     0xdbf4e0: ldur            w2, [x0, #0xb]
    // 0xdbf4e4: DecompressPointer r2
    //     0xdbf4e4: add             x2, x2, HEAP, lsl #32
    // 0xdbf4e8: ldur            x16, [fp, #-0x10]
    // 0xdbf4ec: stp             x16, x2, [SP, #8]
    // 0xdbf4f0: str             x1, [SP]
    // 0xdbf4f4: mov             x0, x2
    // 0xdbf4f8: ClosureCall
    //     0xdbf4f8: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xdbf4fc: ldur            x2, [x0, #0x1f]
    //     0xdbf500: blr             x2
    // 0xdbf504: stur            x0, [fp, #-8]
    // 0xdbf508: r0 = CssBoxWidget()
    //     0xdbf508: bl              #0xa249dc  ; AllocateCssBoxWidgetStub -> CssBoxWidget (size=0x24)
    // 0xdbf50c: mov             x1, x0
    // 0xdbf510: ldur            x2, [fp, #-0x18]
    // 0xdbf514: ldur            x3, [fp, #-8]
    // 0xdbf518: stur            x0, [fp, #-8]
    // 0xdbf51c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xdbf51c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xdbf520: r0 = CssBoxWidget.withInlineSpanChildren()
    //     0xdbf520: bl              #0xa23eec  ; [package:flutter_html/src/css_box_widget.dart] CssBoxWidget::CssBoxWidget.withInlineSpanChildren
    // 0xdbf524: r0 = Padding()
    //     0xdbf524: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xdbf528: mov             x1, x0
    // 0xdbf52c: r0 = Instance_EdgeInsets
    //     0xdbf52c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40318] Obj!EdgeInsets@e13571
    //     0xdbf530: ldr             x0, [x0, #0x318]
    // 0xdbf534: stur            x1, [fp, #-0x10]
    // 0xdbf538: StoreField: r1->field_f = r0
    //     0xdbf538: stur            w0, [x1, #0xf]
    // 0xdbf53c: ldur            x0, [fp, #-8]
    // 0xdbf540: StoreField: r1->field_b = r0
    //     0xdbf540: stur            w0, [x1, #0xb]
    // 0xdbf544: r0 = WidgetSpan()
    //     0xdbf544: bl              #0xa245c8  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xdbf548: ldur            x1, [fp, #-0x10]
    // 0xdbf54c: StoreField: r0->field_13 = r1
    //     0xdbf54c: stur            w1, [x0, #0x13]
    // 0xdbf550: r1 = Instance_PlaceholderAlignment
    //     0xdbf550: add             x1, PP, #0x4e, lsl #12  ; [pp+0x4ede8] Obj!PlaceholderAlignment@e391c1
    //     0xdbf554: ldr             x1, [x1, #0xde8]
    // 0xdbf558: StoreField: r0->field_b = r1
    //     0xdbf558: stur            w1, [x0, #0xb]
    // 0xdbf55c: LeaveFrame
    //     0xdbf55c: mov             SP, fp
    //     0xdbf560: ldp             fp, lr, [SP], #0x10
    // 0xdbf564: ret
    //     0xdbf564: ret             
    // 0xdbf568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdbf568: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdbf56c: b               #0xdbf464
  }
}

// class id: 5063, size: 0x1c, field offset: 0xc
//   const constructor, 
class BacaJugaWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb856a4, size: 0x3e8
    // 0xb856a4: EnterFrame
    //     0xb856a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb856a8: mov             fp, SP
    // 0xb856ac: AllocStack(0x50)
    //     0xb856ac: sub             SP, SP, #0x50
    // 0xb856b0: SetupParameters(BacaJugaWidget this /* r1 => r0, fp-0x8 */)
    //     0xb856b0: mov             x0, x1
    //     0xb856b4: stur            x1, [fp, #-8]
    // 0xb856b8: CheckStackOverflow
    //     0xb856b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb856bc: cmp             SP, x16
    //     0xb856c0: b.ls            #0xb85a5c
    // 0xb856c4: r1 = _ConstMap len:10
    //     0xb856c4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb856c8: ldr             x1, [x1, #0xc08]
    // 0xb856cc: r2 = 600
    //     0xb856cc: movz            x2, #0x258
    // 0xb856d0: r0 = []()
    //     0xb856d0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb856d4: cmp             w0, NULL
    // 0xb856d8: b.eq            #0xb85a64
    // 0xb856dc: r16 = <Color>
    //     0xb856dc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d158] TypeArguments: <Color>
    //     0xb856e0: ldr             x16, [x16, #0x158]
    // 0xb856e4: stp             x0, x16, [SP, #8]
    // 0xb856e8: r16 = Instance_MaterialColor
    //     0xb856e8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb856ec: ldr             x16, [x16, #0xcf0]
    // 0xb856f0: str             x16, [SP]
    // 0xb856f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb856f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb856f8: r0 = mode()
    //     0xb856f8: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb856fc: stur            x0, [fp, #-0x10]
    // 0xb85700: r0 = BorderSide()
    //     0xb85700: bl              #0x7f5748  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb85704: mov             x1, x0
    // 0xb85708: ldur            x0, [fp, #-0x10]
    // 0xb8570c: stur            x1, [fp, #-0x18]
    // 0xb85710: StoreField: r1->field_7 = r0
    //     0xb85710: stur            w0, [x1, #7]
    // 0xb85714: d0 = 4.000000
    //     0xb85714: fmov            d0, #4.00000000
    // 0xb85718: StoreField: r1->field_b = d0
    //     0xb85718: stur            d0, [x1, #0xb]
    // 0xb8571c: r0 = Instance_BorderStyle
    //     0xb8571c: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d2d0] Obj!BorderStyle@e35e61
    //     0xb85720: ldr             x0, [x0, #0x2d0]
    // 0xb85724: StoreField: r1->field_13 = r0
    //     0xb85724: stur            w0, [x1, #0x13]
    // 0xb85728: d0 = -1.000000
    //     0xb85728: fmov            d0, #-1.00000000
    // 0xb8572c: ArrayStore: r1[0] = d0  ; List_8
    //     0xb8572c: stur            d0, [x1, #0x17]
    // 0xb85730: r0 = Border()
    //     0xb85730: bl              #0x87dce8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xb85734: mov             x1, x0
    // 0xb85738: r0 = Instance_BorderSide
    //     0xb85738: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca0] Obj!BorderSide@e1c111
    //     0xb8573c: ldr             x0, [x0, #0xca0]
    // 0xb85740: stur            x1, [fp, #-0x10]
    // 0xb85744: StoreField: r1->field_7 = r0
    //     0xb85744: stur            w0, [x1, #7]
    // 0xb85748: StoreField: r1->field_b = r0
    //     0xb85748: stur            w0, [x1, #0xb]
    // 0xb8574c: StoreField: r1->field_f = r0
    //     0xb8574c: stur            w0, [x1, #0xf]
    // 0xb85750: ldur            x0, [fp, #-0x18]
    // 0xb85754: StoreField: r1->field_13 = r0
    //     0xb85754: stur            w0, [x1, #0x13]
    // 0xb85758: r0 = BoxDecoration()
    //     0xb85758: bl              #0x87c70c  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8575c: mov             x1, x0
    // 0xb85760: ldur            x0, [fp, #-0x10]
    // 0xb85764: stur            x1, [fp, #-0x18]
    // 0xb85768: StoreField: r1->field_f = r0
    //     0xb85768: stur            w0, [x1, #0xf]
    // 0xb8576c: r0 = Instance_BoxShape
    //     0xb8576c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb85770: ldr             x0, [x0, #0xca8]
    // 0xb85774: StoreField: r1->field_23 = r0
    //     0xb85774: stur            w0, [x1, #0x23]
    // 0xb85778: ldur            x2, [fp, #-8]
    // 0xb8577c: LoadField: r3 = r2->field_b
    //     0xb8577c: ldur            w3, [x2, #0xb]
    // 0xb85780: DecompressPointer r3
    //     0xb85780: add             x3, x3, HEAP, lsl #32
    // 0xb85784: stur            x3, [fp, #-0x10]
    // 0xb85788: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb85788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8578c: ldr             x0, [x0, #0x2670]
    //     0xb85790: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb85794: cmp             w0, w16
    //     0xb85798: b.ne            #0xb857a4
    //     0xb8579c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb857a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb857a4: r0 = GetNavigation.textTheme()
    //     0xb857a4: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb857a8: LoadField: r1 = r0->field_23
    //     0xb857a8: ldur            w1, [x0, #0x23]
    // 0xb857ac: DecompressPointer r1
    //     0xb857ac: add             x1, x1, HEAP, lsl #32
    // 0xb857b0: cmp             w1, NULL
    // 0xb857b4: b.eq            #0xb85a68
    // 0xb857b8: ldur            x0, [fp, #-8]
    // 0xb857bc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb857bc: ldur            w2, [x0, #0x17]
    // 0xb857c0: DecompressPointer r2
    //     0xb857c0: add             x2, x2, HEAP, lsl #32
    // 0xb857c4: LoadField: d0 = r2->field_f
    //     0xb857c4: ldur            d0, [x2, #0xf]
    // 0xb857c8: r2 = inline_Allocate_Double()
    //     0xb857c8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb857cc: add             x2, x2, #0x10
    //     0xb857d0: cmp             x3, x2
    //     0xb857d4: b.ls            #0xb85a6c
    //     0xb857d8: str             x2, [THR, #0x50]  ; THR::top
    //     0xb857dc: sub             x2, x2, #0xf
    //     0xb857e0: movz            x3, #0xe15c
    //     0xb857e4: movk            x3, #0x3, lsl #16
    //     0xb857e8: stur            x3, [x2, #-1]
    // 0xb857ec: StoreField: r2->field_7 = d0
    //     0xb857ec: stur            d0, [x2, #7]
    // 0xb857f0: stur            x2, [fp, #-0x20]
    // 0xb857f4: r16 = Instance_FontWeight
    //     0xb857f4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb857f8: ldr             x16, [x16, #0xe20]
    // 0xb857fc: stp             x2, x16, [SP]
    // 0xb85800: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb85800: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e1a0] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb85804: ldr             x4, [x4, #0x1a0]
    // 0xb85808: r0 = copyWith()
    //     0xb85808: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8580c: stur            x0, [fp, #-0x28]
    // 0xb85810: r0 = Text()
    //     0xb85810: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb85814: mov             x1, x0
    // 0xb85818: ldur            x0, [fp, #-0x10]
    // 0xb8581c: stur            x1, [fp, #-0x30]
    // 0xb85820: StoreField: r1->field_b = r0
    //     0xb85820: stur            w0, [x1, #0xb]
    // 0xb85824: ldur            x0, [fp, #-0x28]
    // 0xb85828: StoreField: r1->field_13 = r0
    //     0xb85828: stur            w0, [x1, #0x13]
    // 0xb8582c: r0 = Instance__LinearTextScaler
    //     0xb8582c: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb85830: StoreField: r1->field_33 = r0
    //     0xb85830: stur            w0, [x1, #0x33]
    // 0xb85834: ldur            x2, [fp, #-8]
    // 0xb85838: LoadField: r3 = r2->field_13
    //     0xb85838: ldur            w3, [x2, #0x13]
    // 0xb8583c: DecompressPointer r3
    //     0xb8583c: add             x3, x3, HEAP, lsl #32
    // 0xb85840: stur            x3, [fp, #-0x10]
    // 0xb85844: LoadField: r4 = r2->field_f
    //     0xb85844: ldur            w4, [x2, #0xf]
    // 0xb85848: DecompressPointer r4
    //     0xb85848: add             x4, x4, HEAP, lsl #32
    // 0xb8584c: LoadField: r2 = r4->field_f
    //     0xb8584c: ldur            w2, [x4, #0xf]
    // 0xb85850: DecompressPointer r2
    //     0xb85850: add             x2, x2, HEAP, lsl #32
    // 0xb85854: stur            x2, [fp, #-8]
    // 0xb85858: r0 = GetNavigation.textTheme()
    //     0xb85858: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb8585c: LoadField: r3 = r0->field_23
    //     0xb8585c: ldur            w3, [x0, #0x23]
    // 0xb85860: DecompressPointer r3
    //     0xb85860: add             x3, x3, HEAP, lsl #32
    // 0xb85864: stur            x3, [fp, #-0x28]
    // 0xb85868: cmp             w3, NULL
    // 0xb8586c: b.eq            #0xb85a88
    // 0xb85870: r1 = _ConstMap len:10
    //     0xb85870: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c08] Map<int, Color>(10)
    //     0xb85874: ldr             x1, [x1, #0xc08]
    // 0xb85878: r2 = 600
    //     0xb85878: movz            x2, #0x258
    // 0xb8587c: r0 = []()
    //     0xb8587c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb85880: r16 = <Color?>
    //     0xb85880: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d098] TypeArguments: <Color?>
    //     0xb85884: ldr             x16, [x16, #0x98]
    // 0xb85888: stp             x0, x16, [SP, #8]
    // 0xb8588c: r16 = Instance_MaterialColor
    //     0xb8588c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23cf0] Obj!MaterialColor@e2bab1
    //     0xb85890: ldr             x16, [x16, #0xcf0]
    // 0xb85894: str             x16, [SP]
    // 0xb85898: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb85898: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8589c: r0 = mode()
    //     0xb8589c: bl              #0x624d24  ; [package:nuikit/src/utils/utils.dart] ::mode
    // 0xb858a0: r16 = Instance_FontWeight
    //     0xb858a0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23e20] Obj!FontWeight@e26511
    //     0xb858a4: ldr             x16, [x16, #0xe20]
    // 0xb858a8: ldur            lr, [fp, #-0x20]
    // 0xb858ac: stp             lr, x16, [SP, #8]
    // 0xb858b0: str             x0, [SP]
    // 0xb858b4: ldur            x1, [fp, #-0x28]
    // 0xb858b8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb858b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e998] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb858bc: ldr             x4, [x4, #0x998]
    // 0xb858c0: r0 = copyWith()
    //     0xb858c0: bl              #0x627118  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb858c4: stur            x0, [fp, #-0x20]
    // 0xb858c8: r0 = Text()
    //     0xb858c8: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb858cc: mov             x1, x0
    // 0xb858d0: ldur            x0, [fp, #-8]
    // 0xb858d4: stur            x1, [fp, #-0x28]
    // 0xb858d8: StoreField: r1->field_b = r0
    //     0xb858d8: stur            w0, [x1, #0xb]
    // 0xb858dc: ldur            x0, [fp, #-0x20]
    // 0xb858e0: StoreField: r1->field_13 = r0
    //     0xb858e0: stur            w0, [x1, #0x13]
    // 0xb858e4: r0 = Instance__LinearTextScaler
    //     0xb858e4: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xb858e8: StoreField: r1->field_33 = r0
    //     0xb858e8: stur            w0, [x1, #0x33]
    // 0xb858ec: r0 = InkWell()
    //     0xb858ec: bl              #0x9ec41c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb858f0: mov             x3, x0
    // 0xb858f4: ldur            x0, [fp, #-0x28]
    // 0xb858f8: stur            x3, [fp, #-8]
    // 0xb858fc: StoreField: r3->field_b = r0
    //     0xb858fc: stur            w0, [x3, #0xb]
    // 0xb85900: ldur            x0, [fp, #-0x10]
    // 0xb85904: StoreField: r3->field_f = r0
    //     0xb85904: stur            w0, [x3, #0xf]
    // 0xb85908: r0 = true
    //     0xb85908: add             x0, NULL, #0x20  ; true
    // 0xb8590c: StoreField: r3->field_43 = r0
    //     0xb8590c: stur            w0, [x3, #0x43]
    // 0xb85910: r1 = Instance_BoxShape
    //     0xb85910: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ca8] Obj!BoxShape@e35e01
    //     0xb85914: ldr             x1, [x1, #0xca8]
    // 0xb85918: StoreField: r3->field_47 = r1
    //     0xb85918: stur            w1, [x3, #0x47]
    // 0xb8591c: StoreField: r3->field_6f = r0
    //     0xb8591c: stur            w0, [x3, #0x6f]
    // 0xb85920: r1 = false
    //     0xb85920: add             x1, NULL, #0x30  ; false
    // 0xb85924: StoreField: r3->field_73 = r1
    //     0xb85924: stur            w1, [x3, #0x73]
    // 0xb85928: StoreField: r3->field_83 = r0
    //     0xb85928: stur            w0, [x3, #0x83]
    // 0xb8592c: StoreField: r3->field_7b = r1
    //     0xb8592c: stur            w1, [x3, #0x7b]
    // 0xb85930: r1 = Null
    //     0xb85930: mov             x1, NULL
    // 0xb85934: r2 = 6
    //     0xb85934: movz            x2, #0x6
    // 0xb85938: r0 = AllocateArray()
    //     0xb85938: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8593c: mov             x2, x0
    // 0xb85940: ldur            x0, [fp, #-0x30]
    // 0xb85944: stur            x2, [fp, #-0x10]
    // 0xb85948: StoreField: r2->field_f = r0
    //     0xb85948: stur            w0, [x2, #0xf]
    // 0xb8594c: r16 = Instance_SizedBox
    //     0xb8594c: add             x16, PP, #0x27, lsl #12  ; [pp+0x274a0] Obj!SizedBox@e1e181
    //     0xb85950: ldr             x16, [x16, #0x4a0]
    // 0xb85954: StoreField: r2->field_13 = r16
    //     0xb85954: stur            w16, [x2, #0x13]
    // 0xb85958: ldur            x0, [fp, #-8]
    // 0xb8595c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8595c: stur            w0, [x2, #0x17]
    // 0xb85960: r1 = <Widget>
    //     0xb85960: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb85964: r0 = AllocateGrowableArray()
    //     0xb85964: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb85968: mov             x1, x0
    // 0xb8596c: ldur            x0, [fp, #-0x10]
    // 0xb85970: stur            x1, [fp, #-8]
    // 0xb85974: StoreField: r1->field_f = r0
    //     0xb85974: stur            w0, [x1, #0xf]
    // 0xb85978: r0 = 6
    //     0xb85978: movz            x0, #0x6
    // 0xb8597c: StoreField: r1->field_b = r0
    //     0xb8597c: stur            w0, [x1, #0xb]
    // 0xb85980: r0 = Column()
    //     0xb85980: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb85984: mov             x1, x0
    // 0xb85988: r0 = Instance_Axis
    //     0xb85988: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb8598c: stur            x1, [fp, #-0x10]
    // 0xb85990: StoreField: r1->field_f = r0
    //     0xb85990: stur            w0, [x1, #0xf]
    // 0xb85994: r0 = Instance_MainAxisAlignment
    //     0xb85994: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb85998: ldr             x0, [x0, #0x730]
    // 0xb8599c: StoreField: r1->field_13 = r0
    //     0xb8599c: stur            w0, [x1, #0x13]
    // 0xb859a0: r0 = Instance_MainAxisSize
    //     0xb859a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb859a4: ldr             x0, [x0, #0x738]
    // 0xb859a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb859a8: stur            w0, [x1, #0x17]
    // 0xb859ac: r0 = Instance_CrossAxisAlignment
    //     0xb859ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xb859b0: ldr             x0, [x0, #0xf50]
    // 0xb859b4: StoreField: r1->field_1b = r0
    //     0xb859b4: stur            w0, [x1, #0x1b]
    // 0xb859b8: r0 = Instance_VerticalDirection
    //     0xb859b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb859bc: ldr             x0, [x0, #0x748]
    // 0xb859c0: StoreField: r1->field_23 = r0
    //     0xb859c0: stur            w0, [x1, #0x23]
    // 0xb859c4: r0 = Instance_Clip
    //     0xb859c4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb859c8: ldr             x0, [x0, #0x750]
    // 0xb859cc: StoreField: r1->field_2b = r0
    //     0xb859cc: stur            w0, [x1, #0x2b]
    // 0xb859d0: StoreField: r1->field_2f = rZR
    //     0xb859d0: stur            xzr, [x1, #0x2f]
    // 0xb859d4: ldur            x2, [fp, #-8]
    // 0xb859d8: StoreField: r1->field_b = r2
    //     0xb859d8: stur            w2, [x1, #0xb]
    // 0xb859dc: r0 = Container()
    //     0xb859dc: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb859e0: stur            x0, [fp, #-8]
    // 0xb859e4: r16 = Instance_EdgeInsets
    //     0xb859e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33ef0] Obj!EdgeInsets@e129d1
    //     0xb859e8: ldr             x16, [x16, #0xef0]
    // 0xb859ec: r30 = Instance_EdgeInsets
    //     0xb859ec: add             lr, PP, #0x5b, lsl #12  ; [pp+0x5b250] Obj!EdgeInsets@e133c1
    //     0xb859f0: ldr             lr, [lr, #0x250]
    // 0xb859f4: stp             lr, x16, [SP, #0x10]
    // 0xb859f8: ldur            x16, [fp, #-0x18]
    // 0xb859fc: ldur            lr, [fp, #-0x10]
    // 0xb85a00: stp             lr, x16, [SP]
    // 0xb85a04: mov             x1, x0
    // 0xb85a08: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, margin, 0x1, padding, 0x2, null]
    //     0xb85a08: add             x4, PP, #0x5b, lsl #12  ; [pp+0x5b258] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "margin", 0x1, "padding", 0x2, Null]
    //     0xb85a0c: ldr             x4, [x4, #0x258]
    // 0xb85a10: r0 = Container()
    //     0xb85a10: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb85a14: r0 = Material()
    //     0xb85a14: bl              #0x9e6a2c  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xb85a18: r1 = Instance_MaterialType
    //     0xb85a18: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2ce18] Obj!MaterialType@e36501
    //     0xb85a1c: ldr             x1, [x1, #0xe18]
    // 0xb85a20: StoreField: r0->field_f = r1
    //     0xb85a20: stur            w1, [x0, #0xf]
    // 0xb85a24: StoreField: r0->field_13 = rZR
    //     0xb85a24: stur            xzr, [x0, #0x13]
    // 0xb85a28: r1 = true
    //     0xb85a28: add             x1, NULL, #0x20  ; true
    // 0xb85a2c: StoreField: r0->field_2f = r1
    //     0xb85a2c: stur            w1, [x0, #0x2f]
    // 0xb85a30: r1 = Instance_Clip
    //     0xb85a30: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb85a34: ldr             x1, [x1, #0x750]
    // 0xb85a38: StoreField: r0->field_33 = r1
    //     0xb85a38: stur            w1, [x0, #0x33]
    // 0xb85a3c: r1 = Instance_Duration
    //     0xb85a3c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xb85a40: ldr             x1, [x1, #0x368]
    // 0xb85a44: StoreField: r0->field_37 = r1
    //     0xb85a44: stur            w1, [x0, #0x37]
    // 0xb85a48: ldur            x1, [fp, #-8]
    // 0xb85a4c: StoreField: r0->field_b = r1
    //     0xb85a4c: stur            w1, [x0, #0xb]
    // 0xb85a50: LeaveFrame
    //     0xb85a50: mov             SP, fp
    //     0xb85a54: ldp             fp, lr, [SP], #0x10
    // 0xb85a58: ret
    //     0xb85a58: ret             
    // 0xb85a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85a60: b               #0xb856c4
    // 0xb85a64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb85a64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb85a68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb85a68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb85a6c: SaveReg d0
    //     0xb85a6c: str             q0, [SP, #-0x10]!
    // 0xb85a70: stp             x0, x1, [SP, #-0x10]!
    // 0xb85a74: r0 = AllocateDouble()
    //     0xb85a74: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb85a78: mov             x2, x0
    // 0xb85a7c: ldp             x0, x1, [SP], #0x10
    // 0xb85a80: RestoreReg d0
    //     0xb85a80: ldr             q0, [SP], #0x10
    // 0xb85a84: b               #0xb857ec
    // 0xb85a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb85a88: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5064, size: 0x30, field offset: 0xc
//   const constructor, 
class ArticleContentHtml extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb83734, size: 0x87c
    // 0xb83734: EnterFrame
    //     0xb83734: stp             fp, lr, [SP, #-0x10]!
    //     0xb83738: mov             fp, SP
    // 0xb8373c: AllocStack(0xc0)
    //     0xb8373c: sub             SP, SP, #0xc0
    // 0xb83740: SetupParameters(ArticleContentHtml this /* r1 => r1, fp-0x8 */)
    //     0xb83740: stur            x1, [fp, #-8]
    // 0xb83744: CheckStackOverflow
    //     0xb83744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb83748: cmp             SP, x16
    //     0xb8374c: b.ls            #0xb83fa8
    // 0xb83750: r1 = 1
    //     0xb83750: movz            x1, #0x1
    // 0xb83754: r0 = AllocateContext()
    //     0xb83754: bl              #0xec126c  ; AllocateContextStub
    // 0xb83758: mov             x4, x0
    // 0xb8375c: ldur            x0, [fp, #-8]
    // 0xb83760: stur            x4, [fp, #-0x20]
    // 0xb83764: StoreField: r4->field_f = r0
    //     0xb83764: stur            w0, [x4, #0xf]
    // 0xb83768: LoadField: r1 = r0->field_b
    //     0xb83768: ldur            w1, [x0, #0xb]
    // 0xb8376c: DecompressPointer r1
    //     0xb8376c: add             x1, x1, HEAP, lsl #32
    // 0xb83770: LoadField: r6 = r0->field_23
    //     0xb83770: ldur            w6, [x0, #0x23]
    // 0xb83774: DecompressPointer r6
    //     0xb83774: add             x6, x6, HEAP, lsl #32
    // 0xb83778: stur            x6, [fp, #-0x18]
    // 0xb8377c: LoadField: r7 = r0->field_27
    //     0xb8377c: ldur            w7, [x0, #0x27]
    // 0xb83780: DecompressPointer r7
    //     0xb83780: add             x7, x7, HEAP, lsl #32
    // 0xb83784: stur            x7, [fp, #-0x10]
    // 0xb83788: LoadField: r5 = r0->field_1b
    //     0xb83788: ldur            w5, [x0, #0x1b]
    // 0xb8378c: DecompressPointer r5
    //     0xb8378c: add             x5, x5, HEAP, lsl #32
    // 0xb83790: mov             x2, x6
    // 0xb83794: mov             x3, x7
    // 0xb83798: r0 = _extension#0.insertAll()
    //     0xb83798: bl              #0xb84164  ; [package:nuonline/app/modules/article/widgets/article_content_html.dart] ::_extension#0.insertAll
    // 0xb8379c: mov             x1, x0
    // 0xb837a0: r0 = serialize()
    //     0xb837a0: bl              #0xb84128  ; [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::serialize
    // 0xb837a4: r1 = Null
    //     0xb837a4: mov             x1, NULL
    // 0xb837a8: r2 = 8
    //     0xb837a8: movz            x2, #0x8
    // 0xb837ac: stur            x0, [fp, #-0x28]
    // 0xb837b0: r0 = AllocateArray()
    //     0xb837b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb837b4: stur            x0, [fp, #-0x30]
    // 0xb837b8: r16 = "body"
    //     0xb837b8: add             x16, PP, #8, lsl #12  ; [pp+0x8750] "body"
    //     0xb837bc: ldr             x16, [x16, #0x750]
    // 0xb837c0: StoreField: r0->field_f = r16
    //     0xb837c0: stur            w16, [x0, #0xf]
    // 0xb837c4: r0 = zero()
    //     0xb837c4: bl              #0x9a9edc  ; [package:flutter_html/src/style/margin.dart] Margins::zero
    // 0xb837c8: stur            x0, [fp, #-0x38]
    // 0xb837cc: r0 = zero()
    //     0xb837cc: bl              #0xb83ff8  ; [package:flutter_html/src/style/padding.dart] HtmlPaddings::zero
    // 0xb837d0: mov             x1, x0
    // 0xb837d4: ldur            x0, [fp, #-8]
    // 0xb837d8: stur            x1, [fp, #-0x48]
    // 0xb837dc: LoadField: r2 = r0->field_f
    //     0xb837dc: ldur            w2, [x0, #0xf]
    // 0xb837e0: DecompressPointer r2
    //     0xb837e0: add             x2, x2, HEAP, lsl #32
    // 0xb837e4: stur            x2, [fp, #-0x40]
    // 0xb837e8: LoadField: d0 = r2->field_f
    //     0xb837e8: ldur            d0, [x2, #0xf]
    // 0xb837ec: stur            d0, [fp, #-0x98]
    // 0xb837f0: r0 = FontSize()
    //     0xb837f0: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb837f4: ldur            d0, [fp, #-0x98]
    // 0xb837f8: stur            x0, [fp, #-0x50]
    // 0xb837fc: StoreField: r0->field_7 = d0
    //     0xb837fc: stur            d0, [x0, #7]
    // 0xb83800: r1 = Instance_Unit
    //     0xb83800: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb83804: ldr             x1, [x1, #0xa98]
    // 0xb83808: StoreField: r0->field_f = r1
    //     0xb83808: stur            w1, [x0, #0xf]
    // 0xb8380c: r0 = Style()
    //     0xb8380c: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xb83810: stur            x0, [fp, #-0x58]
    // 0xb83814: ldur            x16, [fp, #-0x38]
    // 0xb83818: ldur            lr, [fp, #-0x48]
    // 0xb8381c: stp             lr, x16, [SP, #0x18]
    // 0xb83820: ldur            x16, [fp, #-0x50]
    // 0xb83824: r30 = "Inter"
    //     0xb83824: add             lr, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb83828: ldr             lr, [lr, #0xcb0]
    // 0xb8382c: stp             lr, x16, [SP, #8]
    // 0xb83830: r16 = Instance_LineHeight
    //     0xb83830: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ae0] Obj!LineHeight@e0fb11
    //     0xb83834: ldr             x16, [x16, #0xae0]
    // 0xb83838: str             x16, [SP]
    // 0xb8383c: mov             x1, x0
    // 0xb83840: r4 = const [0, 0x6, 0x5, 0x1, fontFamily, 0x4, fontSize, 0x3, lineHeight, 0x5, margin, 0x1, padding, 0x2, null]
    //     0xb83840: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ae8] List(15) [0, 0x6, 0x5, 0x1, "fontFamily", 0x4, "fontSize", 0x3, "lineHeight", 0x5, "margin", 0x1, "padding", 0x2, Null]
    //     0xb83844: ldr             x4, [x4, #0xae8]
    // 0xb83848: r0 = Style()
    //     0xb83848: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xb8384c: ldur            x1, [fp, #-0x30]
    // 0xb83850: ldur            x0, [fp, #-0x58]
    // 0xb83854: ArrayStore: r1[1] = r0  ; List_4
    //     0xb83854: add             x25, x1, #0x13
    //     0xb83858: str             w0, [x25]
    //     0xb8385c: tbz             w0, #0, #0xb83878
    //     0xb83860: ldurb           w16, [x1, #-1]
    //     0xb83864: ldurb           w17, [x0, #-1]
    //     0xb83868: and             x16, x17, x16, lsr #2
    //     0xb8386c: tst             x16, HEAP, lsr #32
    //     0xb83870: b.eq            #0xb83878
    //     0xb83874: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb83878: ldur            x1, [fp, #-0x30]
    // 0xb8387c: r16 = "h1,h2,h3,h4,h5,h6,p,li,div,span"
    //     0xb8387c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36af0] "h1,h2,h3,h4,h5,h6,p,li,div,span"
    //     0xb83880: ldr             x16, [x16, #0xaf0]
    // 0xb83884: ArrayStore: r1[0] = r16  ; List_4
    //     0xb83884: stur            w16, [x1, #0x17]
    // 0xb83888: r0 = FontSize()
    //     0xb83888: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb8388c: ldur            d0, [fp, #-0x98]
    // 0xb83890: stur            x0, [fp, #-0x38]
    // 0xb83894: StoreField: r0->field_7 = d0
    //     0xb83894: stur            d0, [x0, #7]
    // 0xb83898: r1 = Instance_Unit
    //     0xb83898: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb8389c: ldr             x1, [x1, #0xa98]
    // 0xb838a0: StoreField: r0->field_f = r1
    //     0xb838a0: stur            w1, [x0, #0xf]
    // 0xb838a4: r0 = Style()
    //     0xb838a4: bl              #0x9ad630  ; AllocateStyleStub -> Style (size=0xa0)
    // 0xb838a8: stur            x0, [fp, #-0x48]
    // 0xb838ac: ldur            x16, [fp, #-0x38]
    // 0xb838b0: r30 = "Inter"
    //     0xb838b0: add             lr, PP, #0x23, lsl #12  ; [pp+0x23cb0] "Inter"
    //     0xb838b4: ldr             lr, [lr, #0xcb0]
    // 0xb838b8: stp             lr, x16, [SP, #8]
    // 0xb838bc: r16 = Instance_LineHeight
    //     0xb838bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ae0] Obj!LineHeight@e0fb11
    //     0xb838c0: ldr             x16, [x16, #0xae0]
    // 0xb838c4: str             x16, [SP]
    // 0xb838c8: mov             x1, x0
    // 0xb838cc: r4 = const [0, 0x4, 0x3, 0x1, fontFamily, 0x2, fontSize, 0x1, lineHeight, 0x3, null]
    //     0xb838cc: add             x4, PP, #0x36, lsl #12  ; [pp+0x36af8] List(11) [0, 0x4, 0x3, 0x1, "fontFamily", 0x2, "fontSize", 0x1, "lineHeight", 0x3, Null]
    //     0xb838d0: ldr             x4, [x4, #0xaf8]
    // 0xb838d4: r0 = Style()
    //     0xb838d4: bl              #0x9ac464  ; [package:flutter_html/src/style.dart] Style::Style
    // 0xb838d8: ldur            x1, [fp, #-0x30]
    // 0xb838dc: ldur            x0, [fp, #-0x48]
    // 0xb838e0: ArrayStore: r1[3] = r0  ; List_4
    //     0xb838e0: add             x25, x1, #0x1b
    //     0xb838e4: str             w0, [x25]
    //     0xb838e8: tbz             w0, #0, #0xb83904
    //     0xb838ec: ldurb           w16, [x1, #-1]
    //     0xb838f0: ldurb           w17, [x0, #-1]
    //     0xb838f4: and             x16, x17, x16, lsr #2
    //     0xb838f8: tst             x16, HEAP, lsr #32
    //     0xb838fc: b.eq            #0xb83904
    //     0xb83900: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb83904: r16 = <String, Style>
    //     0xb83904: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cad8] TypeArguments: <String, Style>
    //     0xb83908: ldr             x16, [x16, #0xad8]
    // 0xb8390c: ldur            lr, [fp, #-0x30]
    // 0xb83910: stp             lr, x16, [SP]
    // 0xb83914: r0 = Map._fromLiteral()
    //     0xb83914: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb83918: stur            x0, [fp, #-0x30]
    // 0xb8391c: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xb8391c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb83920: ldr             x0, [x0, #0x778]
    //     0xb83924: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb83928: cmp             w0, w16
    //     0xb8392c: b.ne            #0xb83938
    //     0xb83930: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xb83934: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb83938: r1 = <String>
    //     0xb83938: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb8393c: stur            x0, [fp, #-0x38]
    // 0xb83940: r0 = _Set()
    //     0xb83940: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xb83944: mov             x1, x0
    // 0xb83948: ldur            x0, [fp, #-0x38]
    // 0xb8394c: stur            x1, [fp, #-0x48]
    // 0xb83950: StoreField: r1->field_1b = r0
    //     0xb83950: stur            w0, [x1, #0x1b]
    // 0xb83954: StoreField: r1->field_b = rZR
    //     0xb83954: stur            wzr, [x1, #0xb]
    // 0xb83958: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xb83958: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8395c: ldr             x0, [x0, #0x780]
    //     0xb83960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb83964: cmp             w0, w16
    //     0xb83968: b.ne            #0xb83974
    //     0xb8396c: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xb83970: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb83974: mov             x3, x0
    // 0xb83978: ldur            x0, [fp, #-0x48]
    // 0xb8397c: stur            x3, [fp, #-0x50]
    // 0xb83980: StoreField: r0->field_f = r3
    //     0xb83980: stur            w3, [x0, #0xf]
    // 0xb83984: StoreField: r0->field_13 = rZR
    //     0xb83984: stur            wzr, [x0, #0x13]
    // 0xb83988: ArrayStore: r0[0] = rZR  ; List_4
    //     0xb83988: stur            wzr, [x0, #0x17]
    // 0xb8398c: mov             x1, x0
    // 0xb83990: r2 = "p"
    //     0xb83990: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cac0] "p"
    //     0xb83994: ldr             x2, [x2, #0xac0]
    // 0xb83998: r0 = add()
    //     0xb83998: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb8399c: r0 = TagStyleExtension()
    //     0xb8399c: bl              #0xb83fec  ; AllocateTagStyleExtensionStub -> TagStyleExtension (size=0x10)
    // 0xb839a0: mov             x3, x0
    // 0xb839a4: ldur            x0, [fp, #-0x48]
    // 0xb839a8: stur            x3, [fp, #-0x58]
    // 0xb839ac: StoreField: r3->field_7 = r0
    //     0xb839ac: stur            w0, [x3, #7]
    // 0xb839b0: ldur            x2, [fp, #-0x20]
    // 0xb839b4: r1 = Function '<anonymous closure>':.
    //     0xb839b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] AnonymousClosure: (0xb85380), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb839b8: ldr             x1, [x1, #0xb00]
    // 0xb839bc: r0 = AllocateClosure()
    //     0xb839bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb839c0: mov             x1, x0
    // 0xb839c4: ldur            x0, [fp, #-0x58]
    // 0xb839c8: StoreField: r0->field_b = r1
    //     0xb839c8: stur            w1, [x0, #0xb]
    // 0xb839cc: ldur            x2, [fp, #-0x20]
    // 0xb839d0: r1 = Function '<anonymous closure>':.
    //     0xb839d0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b08] AnonymousClosure: (0xb85314), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb839d4: ldr             x1, [x1, #0xb08]
    // 0xb839d8: r0 = AllocateClosure()
    //     0xb839d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb839dc: stur            x0, [fp, #-0x48]
    // 0xb839e0: r0 = SpanStyleExtension()
    //     0xb839e0: bl              #0xb83fe0  ; AllocateSpanStyleExtensionStub -> SpanStyleExtension (size=0xc)
    // 0xb839e4: mov             x2, x0
    // 0xb839e8: ldur            x0, [fp, #-0x48]
    // 0xb839ec: stur            x2, [fp, #-0x60]
    // 0xb839f0: StoreField: r2->field_7 = r0
    //     0xb839f0: stur            w0, [x2, #7]
    // 0xb839f4: r1 = <String>
    //     0xb839f4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb839f8: r0 = _Set()
    //     0xb839f8: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xb839fc: mov             x3, x0
    // 0xb83a00: ldur            x0, [fp, #-0x38]
    // 0xb83a04: stur            x3, [fp, #-0x48]
    // 0xb83a08: StoreField: r3->field_1b = r0
    //     0xb83a08: stur            w0, [x3, #0x1b]
    // 0xb83a0c: StoreField: r3->field_b = rZR
    //     0xb83a0c: stur            wzr, [x3, #0xb]
    // 0xb83a10: ldur            x4, [fp, #-0x50]
    // 0xb83a14: StoreField: r3->field_f = r4
    //     0xb83a14: stur            w4, [x3, #0xf]
    // 0xb83a18: StoreField: r3->field_13 = rZR
    //     0xb83a18: stur            wzr, [x3, #0x13]
    // 0xb83a1c: ArrayStore: r3[0] = rZR  ; List_4
    //     0xb83a1c: stur            wzr, [x3, #0x17]
    // 0xb83a20: mov             x1, x3
    // 0xb83a24: r2 = "span"
    //     0xb83a24: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cad0] "span"
    //     0xb83a28: ldr             x2, [x2, #0xad0]
    // 0xb83a2c: r0 = add()
    //     0xb83a2c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb83a30: r0 = TagStyleExtension()
    //     0xb83a30: bl              #0xb83fec  ; AllocateTagStyleExtensionStub -> TagStyleExtension (size=0x10)
    // 0xb83a34: mov             x3, x0
    // 0xb83a38: ldur            x0, [fp, #-0x48]
    // 0xb83a3c: stur            x3, [fp, #-0x68]
    // 0xb83a40: StoreField: r3->field_7 = r0
    //     0xb83a40: stur            w0, [x3, #7]
    // 0xb83a44: ldur            x2, [fp, #-0x20]
    // 0xb83a48: r1 = Function '<anonymous closure>':.
    //     0xb83a48: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b10] AnonymousClosure: (0xb84ff0), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83a4c: ldr             x1, [x1, #0xb10]
    // 0xb83a50: r0 = AllocateClosure()
    //     0xb83a50: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83a54: mov             x1, x0
    // 0xb83a58: ldur            x0, [fp, #-0x68]
    // 0xb83a5c: StoreField: r0->field_b = r1
    //     0xb83a5c: stur            w1, [x0, #0xb]
    // 0xb83a60: r1 = <String>
    //     0xb83a60: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb83a64: r0 = _Set()
    //     0xb83a64: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xb83a68: mov             x3, x0
    // 0xb83a6c: ldur            x0, [fp, #-0x38]
    // 0xb83a70: stur            x3, [fp, #-0x48]
    // 0xb83a74: StoreField: r3->field_1b = r0
    //     0xb83a74: stur            w0, [x3, #0x1b]
    // 0xb83a78: StoreField: r3->field_b = rZR
    //     0xb83a78: stur            wzr, [x3, #0xb]
    // 0xb83a7c: ldur            x4, [fp, #-0x50]
    // 0xb83a80: StoreField: r3->field_f = r4
    //     0xb83a80: stur            w4, [x3, #0xf]
    // 0xb83a84: StoreField: r3->field_13 = rZR
    //     0xb83a84: stur            wzr, [x3, #0x13]
    // 0xb83a88: ArrayStore: r3[0] = rZR  ; List_4
    //     0xb83a88: stur            wzr, [x3, #0x17]
    // 0xb83a8c: mov             x1, x3
    // 0xb83a90: r2 = "figure"
    //     0xb83a90: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b18] "figure"
    //     0xb83a94: ldr             x2, [x2, #0xb18]
    // 0xb83a98: r0 = add()
    //     0xb83a98: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb83a9c: r0 = TagStyleExtension()
    //     0xb83a9c: bl              #0xb83fec  ; AllocateTagStyleExtensionStub -> TagStyleExtension (size=0x10)
    // 0xb83aa0: mov             x3, x0
    // 0xb83aa4: ldur            x0, [fp, #-0x48]
    // 0xb83aa8: stur            x3, [fp, #-0x70]
    // 0xb83aac: StoreField: r3->field_7 = r0
    //     0xb83aac: stur            w0, [x3, #7]
    // 0xb83ab0: r1 = Function '<anonymous closure>':.
    //     0xb83ab0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b20] AnonymousClosure: (0xb84e34), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83ab4: ldr             x1, [x1, #0xb20]
    // 0xb83ab8: r2 = Null
    //     0xb83ab8: mov             x2, NULL
    // 0xb83abc: r0 = AllocateClosure()
    //     0xb83abc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83ac0: mov             x1, x0
    // 0xb83ac4: ldur            x0, [fp, #-0x70]
    // 0xb83ac8: StoreField: r0->field_b = r1
    //     0xb83ac8: stur            w1, [x0, #0xb]
    // 0xb83acc: r1 = <String>
    //     0xb83acc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb83ad0: r0 = _Set()
    //     0xb83ad0: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xb83ad4: mov             x3, x0
    // 0xb83ad8: ldur            x0, [fp, #-0x38]
    // 0xb83adc: stur            x3, [fp, #-0x48]
    // 0xb83ae0: StoreField: r3->field_1b = r0
    //     0xb83ae0: stur            w0, [x3, #0x1b]
    // 0xb83ae4: StoreField: r3->field_b = rZR
    //     0xb83ae4: stur            wzr, [x3, #0xb]
    // 0xb83ae8: ldur            x4, [fp, #-0x50]
    // 0xb83aec: StoreField: r3->field_f = r4
    //     0xb83aec: stur            w4, [x3, #0xf]
    // 0xb83af0: StoreField: r3->field_13 = rZR
    //     0xb83af0: stur            wzr, [x3, #0x13]
    // 0xb83af4: ArrayStore: r3[0] = rZR  ; List_4
    //     0xb83af4: stur            wzr, [x3, #0x17]
    // 0xb83af8: mov             x1, x3
    // 0xb83afc: r2 = "figcaption"
    //     0xb83afc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b28] "figcaption"
    //     0xb83b00: ldr             x2, [x2, #0xb28]
    // 0xb83b04: r0 = add()
    //     0xb83b04: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb83b08: r0 = TagStyleExtension()
    //     0xb83b08: bl              #0xb83fec  ; AllocateTagStyleExtensionStub -> TagStyleExtension (size=0x10)
    // 0xb83b0c: mov             x3, x0
    // 0xb83b10: ldur            x0, [fp, #-0x48]
    // 0xb83b14: stur            x3, [fp, #-0x78]
    // 0xb83b18: StoreField: r3->field_7 = r0
    //     0xb83b18: stur            w0, [x3, #7]
    // 0xb83b1c: r1 = Function '<anonymous closure>':.
    //     0xb83b1c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b30] AnonymousClosure: (0xb84d04), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83b20: ldr             x1, [x1, #0xb30]
    // 0xb83b24: r2 = Null
    //     0xb83b24: mov             x2, NULL
    // 0xb83b28: r0 = AllocateClosure()
    //     0xb83b28: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83b2c: mov             x1, x0
    // 0xb83b30: ldur            x0, [fp, #-0x78]
    // 0xb83b34: StoreField: r0->field_b = r1
    //     0xb83b34: stur            w1, [x0, #0xb]
    // 0xb83b38: ldur            x1, [fp, #-8]
    // 0xb83b3c: LoadField: r2 = r1->field_2b
    //     0xb83b3c: ldur            w2, [x1, #0x2b]
    // 0xb83b40: DecompressPointer r2
    //     0xb83b40: add             x2, x2, HEAP, lsl #32
    // 0xb83b44: stur            x2, [fp, #-0x80]
    // 0xb83b48: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb83b48: ldur            w3, [x1, #0x17]
    // 0xb83b4c: DecompressPointer r3
    //     0xb83b4c: add             x3, x3, HEAP, lsl #32
    // 0xb83b50: stur            x3, [fp, #-0x48]
    // 0xb83b54: r0 = TagDivExtension()
    //     0xb83b54: bl              #0xb83fd4  ; AllocateTagDivExtensionStub -> TagDivExtension (size=0x1c)
    // 0xb83b58: mov             x3, x0
    // 0xb83b5c: ldur            x0, [fp, #-0x40]
    // 0xb83b60: stur            x3, [fp, #-0x88]
    // 0xb83b64: StoreField: r3->field_7 = r0
    //     0xb83b64: stur            w0, [x3, #7]
    // 0xb83b68: ldur            x0, [fp, #-0x18]
    // 0xb83b6c: StoreField: r3->field_b = r0
    //     0xb83b6c: stur            w0, [x3, #0xb]
    // 0xb83b70: ldur            x0, [fp, #-0x10]
    // 0xb83b74: StoreField: r3->field_f = r0
    //     0xb83b74: stur            w0, [x3, #0xf]
    // 0xb83b78: ldur            x0, [fp, #-0x48]
    // 0xb83b7c: StoreField: r3->field_13 = r0
    //     0xb83b7c: stur            w0, [x3, #0x13]
    // 0xb83b80: ldur            x0, [fp, #-0x80]
    // 0xb83b84: ArrayStore: r3[0] = r0  ; List_4
    //     0xb83b84: stur            w0, [x3, #0x17]
    // 0xb83b88: r1 = Null
    //     0xb83b88: mov             x1, NULL
    // 0xb83b8c: r2 = 12
    //     0xb83b8c: movz            x2, #0xc
    // 0xb83b90: r0 = AllocateArray()
    //     0xb83b90: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb83b94: mov             x2, x0
    // 0xb83b98: ldur            x0, [fp, #-0x58]
    // 0xb83b9c: stur            x2, [fp, #-0x10]
    // 0xb83ba0: StoreField: r2->field_f = r0
    //     0xb83ba0: stur            w0, [x2, #0xf]
    // 0xb83ba4: ldur            x0, [fp, #-0x60]
    // 0xb83ba8: StoreField: r2->field_13 = r0
    //     0xb83ba8: stur            w0, [x2, #0x13]
    // 0xb83bac: ldur            x0, [fp, #-0x68]
    // 0xb83bb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb83bb0: stur            w0, [x2, #0x17]
    // 0xb83bb4: ldur            x0, [fp, #-0x70]
    // 0xb83bb8: StoreField: r2->field_1b = r0
    //     0xb83bb8: stur            w0, [x2, #0x1b]
    // 0xb83bbc: ldur            x0, [fp, #-0x78]
    // 0xb83bc0: StoreField: r2->field_1f = r0
    //     0xb83bc0: stur            w0, [x2, #0x1f]
    // 0xb83bc4: ldur            x0, [fp, #-0x88]
    // 0xb83bc8: StoreField: r2->field_23 = r0
    //     0xb83bc8: stur            w0, [x2, #0x23]
    // 0xb83bcc: r1 = <HtmlExtension>
    //     0xb83bcc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b38] TypeArguments: <HtmlExtension>
    //     0xb83bd0: ldr             x1, [x1, #0xb38]
    // 0xb83bd4: r0 = AllocateGrowableArray()
    //     0xb83bd4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb83bd8: mov             x2, x0
    // 0xb83bdc: ldur            x0, [fp, #-0x10]
    // 0xb83be0: stur            x2, [fp, #-0x18]
    // 0xb83be4: StoreField: r2->field_f = r0
    //     0xb83be4: stur            w0, [x2, #0xf]
    // 0xb83be8: r0 = 12
    //     0xb83be8: movz            x0, #0xc
    // 0xb83bec: StoreField: r2->field_b = r0
    //     0xb83bec: stur            w0, [x2, #0xb]
    // 0xb83bf0: ldur            x0, [fp, #-8]
    // 0xb83bf4: LoadField: r3 = r0->field_13
    //     0xb83bf4: ldur            w3, [x0, #0x13]
    // 0xb83bf8: DecompressPointer r3
    //     0xb83bf8: add             x3, x3, HEAP, lsl #32
    // 0xb83bfc: stur            x3, [fp, #-0x10]
    // 0xb83c00: r1 = <String>
    //     0xb83c00: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb83c04: r0 = _Set()
    //     0xb83c04: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xb83c08: mov             x3, x0
    // 0xb83c0c: ldur            x0, [fp, #-0x38]
    // 0xb83c10: stur            x3, [fp, #-0x40]
    // 0xb83c14: StoreField: r3->field_1b = r0
    //     0xb83c14: stur            w0, [x3, #0x1b]
    // 0xb83c18: StoreField: r3->field_b = rZR
    //     0xb83c18: stur            wzr, [x3, #0xb]
    // 0xb83c1c: ldur            x0, [fp, #-0x50]
    // 0xb83c20: StoreField: r3->field_f = r0
    //     0xb83c20: stur            w0, [x3, #0xf]
    // 0xb83c24: StoreField: r3->field_13 = rZR
    //     0xb83c24: stur            wzr, [x3, #0x13]
    // 0xb83c28: ArrayStore: r3[0] = rZR  ; List_4
    //     0xb83c28: stur            wzr, [x3, #0x17]
    // 0xb83c2c: mov             x1, x3
    // 0xb83c30: r2 = "table"
    //     0xb83c30: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b40] "table"
    //     0xb83c34: ldr             x2, [x2, #0xb40]
    // 0xb83c38: r0 = add()
    //     0xb83c38: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb83c3c: r0 = TagWrapperExtension()
    //     0xb83c3c: bl              #0xb83fc8  ; AllocateTagWrapperExtensionStub -> TagWrapperExtension (size=0x10)
    // 0xb83c40: mov             x3, x0
    // 0xb83c44: ldur            x0, [fp, #-0x40]
    // 0xb83c48: stur            x3, [fp, #-0x38]
    // 0xb83c4c: StoreField: r3->field_7 = r0
    //     0xb83c4c: stur            w0, [x3, #7]
    // 0xb83c50: r1 = Function '<anonymous closure>':.
    //     0xb83c50: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b48] AnonymousClosure: (0xb84ca8), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83c54: ldr             x1, [x1, #0xb48]
    // 0xb83c58: r2 = Null
    //     0xb83c58: mov             x2, NULL
    // 0xb83c5c: r0 = AllocateClosure()
    //     0xb83c5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83c60: mov             x1, x0
    // 0xb83c64: ldur            x0, [fp, #-0x38]
    // 0xb83c68: StoreField: r0->field_b = r1
    //     0xb83c68: stur            w1, [x0, #0xb]
    // 0xb83c6c: ldur            x1, [fp, #-0x10]
    // 0xb83c70: tbnz            w1, #4, #0xb83cf0
    // 0xb83c74: ldur            x2, [fp, #-0x18]
    // 0xb83c78: LoadField: r1 = r2->field_b
    //     0xb83c78: ldur            w1, [x2, #0xb]
    // 0xb83c7c: LoadField: r3 = r2->field_f
    //     0xb83c7c: ldur            w3, [x2, #0xf]
    // 0xb83c80: DecompressPointer r3
    //     0xb83c80: add             x3, x3, HEAP, lsl #32
    // 0xb83c84: LoadField: r4 = r3->field_b
    //     0xb83c84: ldur            w4, [x3, #0xb]
    // 0xb83c88: r3 = LoadInt32Instr(r1)
    //     0xb83c88: sbfx            x3, x1, #1, #0x1f
    // 0xb83c8c: stur            x3, [fp, #-0x90]
    // 0xb83c90: r1 = LoadInt32Instr(r4)
    //     0xb83c90: sbfx            x1, x4, #1, #0x1f
    // 0xb83c94: cmp             x3, x1
    // 0xb83c98: b.ne            #0xb83ca4
    // 0xb83c9c: mov             x1, x2
    // 0xb83ca0: r0 = _growToNextCapacity()
    //     0xb83ca0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb83ca4: ldur            x2, [fp, #-0x18]
    // 0xb83ca8: ldur            x3, [fp, #-0x90]
    // 0xb83cac: add             x0, x3, #1
    // 0xb83cb0: lsl             x1, x0, #1
    // 0xb83cb4: StoreField: r2->field_b = r1
    //     0xb83cb4: stur            w1, [x2, #0xb]
    // 0xb83cb8: LoadField: r1 = r2->field_f
    //     0xb83cb8: ldur            w1, [x2, #0xf]
    // 0xb83cbc: DecompressPointer r1
    //     0xb83cbc: add             x1, x1, HEAP, lsl #32
    // 0xb83cc0: ldur            x0, [fp, #-0x38]
    // 0xb83cc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb83cc4: add             x25, x1, x3, lsl #2
    //     0xb83cc8: add             x25, x25, #0xf
    //     0xb83ccc: str             w0, [x25]
    //     0xb83cd0: tbz             w0, #0, #0xb83cec
    //     0xb83cd4: ldurb           w16, [x1, #-1]
    //     0xb83cd8: ldurb           w17, [x0, #-1]
    //     0xb83cdc: and             x16, x17, x16, lsr #2
    //     0xb83ce0: tst             x16, HEAP, lsr #32
    //     0xb83ce4: b.eq            #0xb83cec
    //     0xb83ce8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xb83cec: b               #0xb83cf4
    // 0xb83cf0: ldur            x2, [fp, #-0x18]
    // 0xb83cf4: ldur            x0, [fp, #-8]
    // 0xb83cf8: ldur            x3, [fp, #-0x28]
    // 0xb83cfc: ldur            x1, [fp, #-0x30]
    // 0xb83d00: r0 = Html()
    //     0xb83d00: bl              #0xaf1fd0  ; AllocateHtmlStub -> Html (size=0x38)
    // 0xb83d04: mov             x3, x0
    // 0xb83d08: ldur            x0, [fp, #-0x28]
    // 0xb83d0c: stur            x3, [fp, #-0x10]
    // 0xb83d10: StoreField: r3->field_f = r0
    //     0xb83d10: stur            w0, [x3, #0xf]
    // 0xb83d14: r1 = Function '<anonymous closure>':.
    //     0xb83d14: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b50] AnonymousClosure: (0xb84c44), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83d18: ldr             x1, [x1, #0xb50]
    // 0xb83d1c: r2 = Null
    //     0xb83d1c: mov             x2, NULL
    // 0xb83d20: r0 = AllocateClosure()
    //     0xb83d20: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83d24: mov             x1, x0
    // 0xb83d28: ldur            x0, [fp, #-0x10]
    // 0xb83d2c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb83d2c: stur            w1, [x0, #0x17]
    // 0xb83d30: ldur            x1, [fp, #-0x18]
    // 0xb83d34: StoreField: r0->field_2f = r1
    //     0xb83d34: stur            w1, [x0, #0x2f]
    // 0xb83d38: r1 = false
    //     0xb83d38: add             x1, NULL, #0x30  ; false
    // 0xb83d3c: StoreField: r0->field_23 = r1
    //     0xb83d3c: stur            w1, [x0, #0x23]
    // 0xb83d40: ldur            x1, [fp, #-0x30]
    // 0xb83d44: StoreField: r0->field_33 = r1
    //     0xb83d44: stur            w1, [x0, #0x33]
    // 0xb83d48: r1 = <State<StatefulWidget>>
    //     0xb83d48: ldr             x1, [PP, #0x4ad0]  ; [pp+0x4ad0] TypeArguments: <State<StatefulWidget>>
    // 0xb83d4c: r0 = LabeledGlobalKey()
    //     0xb83d4c: bl              #0x63a440  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xb83d50: mov             x1, x0
    // 0xb83d54: ldur            x0, [fp, #-0x10]
    // 0xb83d58: StoreField: r0->field_b = r1
    //     0xb83d58: stur            w1, [x0, #0xb]
    // 0xb83d5c: r1 = Null
    //     0xb83d5c: mov             x1, NULL
    // 0xb83d60: r2 = 2
    //     0xb83d60: movz            x2, #0x2
    // 0xb83d64: r0 = AllocateArray()
    //     0xb83d64: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb83d68: mov             x2, x0
    // 0xb83d6c: ldur            x0, [fp, #-0x10]
    // 0xb83d70: stur            x2, [fp, #-0x18]
    // 0xb83d74: StoreField: r2->field_f = r0
    //     0xb83d74: stur            w0, [x2, #0xf]
    // 0xb83d78: r1 = <Widget>
    //     0xb83d78: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb83d7c: r0 = AllocateGrowableArray()
    //     0xb83d7c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb83d80: mov             x2, x0
    // 0xb83d84: ldur            x0, [fp, #-0x18]
    // 0xb83d88: stur            x2, [fp, #-0x28]
    // 0xb83d8c: StoreField: r2->field_f = r0
    //     0xb83d8c: stur            w0, [x2, #0xf]
    // 0xb83d90: r0 = 2
    //     0xb83d90: movz            x0, #0x2
    // 0xb83d94: StoreField: r2->field_b = r0
    //     0xb83d94: stur            w0, [x2, #0xb]
    // 0xb83d98: ldur            x0, [fp, #-8]
    // 0xb83d9c: LoadField: r3 = r0->field_1f
    //     0xb83d9c: ldur            w3, [x0, #0x1f]
    // 0xb83da0: DecompressPointer r3
    //     0xb83da0: add             x3, x3, HEAP, lsl #32
    // 0xb83da4: stur            x3, [fp, #-0x10]
    // 0xb83da8: r0 = LoadClassIdInstr(r3)
    //     0xb83da8: ldur            x0, [x3, #-1]
    //     0xb83dac: ubfx            x0, x0, #0xc, #0x14
    // 0xb83db0: mov             x1, x3
    // 0xb83db4: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb83db4: movz            x17, #0xd488
    //     0xb83db8: add             lr, x0, x17
    //     0xb83dbc: ldr             lr, [x21, lr, lsl #3]
    //     0xb83dc0: blr             lr
    // 0xb83dc4: tbnz            w0, #4, #0xb83f14
    // 0xb83dc8: ldur            x0, [fp, #-0x10]
    // 0xb83dcc: r1 = _ConstMap len:6
    //     0xb83dcc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23c20] Map<int, Color>(6)
    //     0xb83dd0: ldr             x1, [x1, #0xc20]
    // 0xb83dd4: r2 = 600
    //     0xb83dd4: movz            x2, #0x258
    // 0xb83dd8: r0 = []()
    //     0xb83dd8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb83ddc: stur            x0, [fp, #-8]
    // 0xb83de0: r0 = Divider()
    //     0xb83de0: bl              #0xad5980  ; AllocateDividerStub -> Divider (size=0x20)
    // 0xb83de4: mov             x2, x0
    // 0xb83de8: r0 = 24.000000
    //     0xb83de8: add             x0, PP, #0x1d, lsl #12  ; [pp+0x1d368] 24
    //     0xb83dec: ldr             x0, [x0, #0x368]
    // 0xb83df0: stur            x2, [fp, #-0x18]
    // 0xb83df4: StoreField: r2->field_b = r0
    //     0xb83df4: stur            w0, [x2, #0xb]
    // 0xb83df8: ldur            x0, [fp, #-8]
    // 0xb83dfc: StoreField: r2->field_1b = r0
    //     0xb83dfc: stur            w0, [x2, #0x1b]
    // 0xb83e00: ldur            x1, [fp, #-0x10]
    // 0xb83e04: r0 = LoadClassIdInstr(r1)
    //     0xb83e04: ldur            x0, [x1, #-1]
    //     0xb83e08: ubfx            x0, x0, #0xc, #0x14
    // 0xb83e0c: r0 = GDT[cid_x0 + 0x131af]()
    //     0xb83e0c: movz            x17, #0x31af
    //     0xb83e10: movk            x17, #0x1, lsl #16
    //     0xb83e14: add             lr, x0, x17
    //     0xb83e18: ldr             lr, [x21, lr, lsl #3]
    //     0xb83e1c: blr             lr
    // 0xb83e20: ldur            x2, [fp, #-0x20]
    // 0xb83e24: r1 = Function '<anonymous closure>':.
    //     0xb83e24: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b58] AnonymousClosure: (0xb84aa0), in [package:nuonline/app/modules/article/widgets/article_content_html.dart] ArticleContentHtml::build (0xb83734)
    //     0xb83e28: ldr             x1, [x1, #0xb58]
    // 0xb83e2c: stur            x0, [fp, #-8]
    // 0xb83e30: r0 = AllocateClosure()
    //     0xb83e30: bl              #0xec1630  ; AllocateClosureStub
    // 0xb83e34: r16 = <Text>
    //     0xb83e34: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b60] TypeArguments: <Text>
    //     0xb83e38: ldr             x16, [x16, #0xb60]
    // 0xb83e3c: ldur            lr, [fp, #-8]
    // 0xb83e40: stp             lr, x16, [SP, #8]
    // 0xb83e44: str             x0, [SP]
    // 0xb83e48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb83e48: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb83e4c: r0 = map()
    //     0xb83e4c: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0xb83e50: LoadField: r1 = r0->field_7
    //     0xb83e50: ldur            w1, [x0, #7]
    // 0xb83e54: DecompressPointer r1
    //     0xb83e54: add             x1, x1, HEAP, lsl #32
    // 0xb83e58: mov             x2, x0
    // 0xb83e5c: r0 = _GrowableList.of()
    //     0xb83e5c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb83e60: stur            x0, [fp, #-8]
    // 0xb83e64: r0 = Column()
    //     0xb83e64: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb83e68: mov             x3, x0
    // 0xb83e6c: r0 = Instance_Axis
    //     0xb83e6c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb83e70: stur            x3, [fp, #-0x10]
    // 0xb83e74: StoreField: r3->field_f = r0
    //     0xb83e74: stur            w0, [x3, #0xf]
    // 0xb83e78: r4 = Instance_MainAxisAlignment
    //     0xb83e78: add             x4, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb83e7c: ldr             x4, [x4, #0x730]
    // 0xb83e80: StoreField: r3->field_13 = r4
    //     0xb83e80: stur            w4, [x3, #0x13]
    // 0xb83e84: r5 = Instance_MainAxisSize
    //     0xb83e84: add             x5, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb83e88: ldr             x5, [x5, #0x738]
    // 0xb83e8c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb83e8c: stur            w5, [x3, #0x17]
    // 0xb83e90: r6 = Instance_CrossAxisAlignment
    //     0xb83e90: add             x6, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb83e94: ldr             x6, [x6, #0x68]
    // 0xb83e98: StoreField: r3->field_1b = r6
    //     0xb83e98: stur            w6, [x3, #0x1b]
    // 0xb83e9c: r7 = Instance_VerticalDirection
    //     0xb83e9c: add             x7, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb83ea0: ldr             x7, [x7, #0x748]
    // 0xb83ea4: StoreField: r3->field_23 = r7
    //     0xb83ea4: stur            w7, [x3, #0x23]
    // 0xb83ea8: r8 = Instance_Clip
    //     0xb83ea8: add             x8, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb83eac: ldr             x8, [x8, #0x750]
    // 0xb83eb0: StoreField: r3->field_2b = r8
    //     0xb83eb0: stur            w8, [x3, #0x2b]
    // 0xb83eb4: StoreField: r3->field_2f = rZR
    //     0xb83eb4: stur            xzr, [x3, #0x2f]
    // 0xb83eb8: ldur            x1, [fp, #-8]
    // 0xb83ebc: StoreField: r3->field_b = r1
    //     0xb83ebc: stur            w1, [x3, #0xb]
    // 0xb83ec0: r1 = Null
    //     0xb83ec0: mov             x1, NULL
    // 0xb83ec4: r2 = 6
    //     0xb83ec4: movz            x2, #0x6
    // 0xb83ec8: r0 = AllocateArray()
    //     0xb83ec8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb83ecc: stur            x0, [fp, #-8]
    // 0xb83ed0: r16 = Instance_SizedBox
    //     0xb83ed0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27540] Obj!SizedBox@e1dfe1
    //     0xb83ed4: ldr             x16, [x16, #0x540]
    // 0xb83ed8: StoreField: r0->field_f = r16
    //     0xb83ed8: stur            w16, [x0, #0xf]
    // 0xb83edc: ldur            x1, [fp, #-0x18]
    // 0xb83ee0: StoreField: r0->field_13 = r1
    //     0xb83ee0: stur            w1, [x0, #0x13]
    // 0xb83ee4: ldur            x1, [fp, #-0x10]
    // 0xb83ee8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb83ee8: stur            w1, [x0, #0x17]
    // 0xb83eec: r1 = <Widget>
    //     0xb83eec: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb83ef0: r0 = AllocateGrowableArray()
    //     0xb83ef0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb83ef4: mov             x1, x0
    // 0xb83ef8: ldur            x0, [fp, #-8]
    // 0xb83efc: StoreField: r1->field_f = r0
    //     0xb83efc: stur            w0, [x1, #0xf]
    // 0xb83f00: r0 = 6
    //     0xb83f00: movz            x0, #0x6
    // 0xb83f04: StoreField: r1->field_b = r0
    //     0xb83f04: stur            w0, [x1, #0xb]
    // 0xb83f08: mov             x2, x1
    // 0xb83f0c: ldur            x1, [fp, #-0x28]
    // 0xb83f10: r0 = addAll()
    //     0xb83f10: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb83f14: ldur            x0, [fp, #-0x28]
    // 0xb83f18: r0 = Column()
    //     0xb83f18: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb83f1c: mov             x1, x0
    // 0xb83f20: r0 = Instance_Axis
    //     0xb83f20: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb83f24: stur            x1, [fp, #-8]
    // 0xb83f28: StoreField: r1->field_f = r0
    //     0xb83f28: stur            w0, [x1, #0xf]
    // 0xb83f2c: r0 = Instance_MainAxisAlignment
    //     0xb83f2c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb83f30: ldr             x0, [x0, #0x730]
    // 0xb83f34: StoreField: r1->field_13 = r0
    //     0xb83f34: stur            w0, [x1, #0x13]
    // 0xb83f38: r0 = Instance_MainAxisSize
    //     0xb83f38: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb83f3c: ldr             x0, [x0, #0x738]
    // 0xb83f40: ArrayStore: r1[0] = r0  ; List_4
    //     0xb83f40: stur            w0, [x1, #0x17]
    // 0xb83f44: r0 = Instance_CrossAxisAlignment
    //     0xb83f44: add             x0, PP, #0x27, lsl #12  ; [pp+0x27068] Obj!CrossAxisAlignment@e35a01
    //     0xb83f48: ldr             x0, [x0, #0x68]
    // 0xb83f4c: StoreField: r1->field_1b = r0
    //     0xb83f4c: stur            w0, [x1, #0x1b]
    // 0xb83f50: r0 = Instance_VerticalDirection
    //     0xb83f50: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb83f54: ldr             x0, [x0, #0x748]
    // 0xb83f58: StoreField: r1->field_23 = r0
    //     0xb83f58: stur            w0, [x1, #0x23]
    // 0xb83f5c: r0 = Instance_Clip
    //     0xb83f5c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb83f60: ldr             x0, [x0, #0x750]
    // 0xb83f64: StoreField: r1->field_2b = r0
    //     0xb83f64: stur            w0, [x1, #0x2b]
    // 0xb83f68: StoreField: r1->field_2f = rZR
    //     0xb83f68: stur            xzr, [x1, #0x2f]
    // 0xb83f6c: ldur            x0, [fp, #-0x28]
    // 0xb83f70: StoreField: r1->field_b = r0
    //     0xb83f70: stur            w0, [x1, #0xb]
    // 0xb83f74: r0 = NTextSelectionControls()
    //     0xb83f74: bl              #0xb83fbc  ; AllocateNTextSelectionControlsStub -> NTextSelectionControls (size=0x8)
    // 0xb83f78: stur            x0, [fp, #-0x10]
    // 0xb83f7c: r0 = SelectionArea()
    //     0xb83f7c: bl              #0xb83fb0  ; AllocateSelectionAreaStub -> SelectionArea (size=0x24)
    // 0xb83f80: ldur            x1, [fp, #-0x10]
    // 0xb83f84: StoreField: r0->field_13 = r1
    //     0xb83f84: stur            w1, [x0, #0x13]
    // 0xb83f88: r1 = Closure: (BuildContext, SelectableRegionState) => Widget from Function '_defaultContextMenuBuilder@596476716': static.
    //     0xb83f88: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b68] Closure: (BuildContext, SelectableRegionState) => Widget from Function '_defaultContextMenuBuilder@596476716': static. (0x7e54fb40432c)
    //     0xb83f8c: ldr             x1, [x1, #0xb68]
    // 0xb83f90: ArrayStore: r0[0] = r1  ; List_4
    //     0xb83f90: stur            w1, [x0, #0x17]
    // 0xb83f94: ldur            x1, [fp, #-8]
    // 0xb83f98: StoreField: r0->field_1f = r1
    //     0xb83f98: stur            w1, [x0, #0x1f]
    // 0xb83f9c: LeaveFrame
    //     0xb83f9c: mov             SP, fp
    //     0xb83fa0: ldp             fp, lr, [SP], #0x10
    // 0xb83fa4: ret
    //     0xb83fa4: ret             
    // 0xb83fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb83fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb83fac: b               #0xb83750
  }
  static _ serialize(/* No info */) {
    // ** addr: 0xb84128, size: 0x3c
    // 0xb84128: EnterFrame
    //     0xb84128: stp             fp, lr, [SP, #-0x10]!
    //     0xb8412c: mov             fp, SP
    // 0xb84130: CheckStackOverflow
    //     0xb84130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84134: cmp             SP, x16
    //     0xb84138: b.ls            #0xb8415c
    // 0xb8413c: r2 = "</p>\r\n\r\n<p>"
    //     0xb8413c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f50] "</p>\r\n\r\n<p>"
    //     0xb84140: ldr             x2, [x2, #0xf50]
    // 0xb84144: r3 = "</p><span><p>"
    //     0xb84144: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f58] "</p><span><p>"
    //     0xb84148: ldr             x3, [x3, #0xf58]
    // 0xb8414c: r0 = replaceAll()
    //     0xb8414c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xb84150: LeaveFrame
    //     0xb84150: mov             SP, fp
    //     0xb84154: ldp             fp, lr, [SP], #0x10
    // 0xb84158: ret
    //     0xb84158: ret             
    // 0xb8415c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8415c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84160: b               #0xb8413c
  }
  [closure] Text <anonymous closure>(dynamic, Author) {
    // ** addr: 0xb84aa0, size: 0x1a4
    // 0xb84aa0: EnterFrame
    //     0xb84aa0: stp             fp, lr, [SP, #-0x10]!
    //     0xb84aa4: mov             fp, SP
    // 0xb84aa8: AllocStack(0x30)
    //     0xb84aa8: sub             SP, SP, #0x30
    // 0xb84aac: SetupParameters()
    //     0xb84aac: ldr             x0, [fp, #0x18]
    //     0xb84ab0: ldur            w3, [x0, #0x17]
    //     0xb84ab4: add             x3, x3, HEAP, lsl #32
    //     0xb84ab8: stur            x3, [fp, #-0x10]
    // 0xb84abc: CheckStackOverflow
    //     0xb84abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84ac0: cmp             SP, x16
    //     0xb84ac4: b.ls            #0xb84c24
    // 0xb84ac8: ldr             x0, [fp, #0x10]
    // 0xb84acc: LoadField: r4 = r0->field_1b
    //     0xb84acc: ldur            w4, [x0, #0x1b]
    // 0xb84ad0: DecompressPointer r4
    //     0xb84ad0: add             x4, x4, HEAP, lsl #32
    // 0xb84ad4: stur            x4, [fp, #-8]
    // 0xb84ad8: r1 = Null
    //     0xb84ad8: mov             x1, NULL
    // 0xb84adc: r2 = 4
    //     0xb84adc: movz            x2, #0x4
    // 0xb84ae0: r0 = AllocateArray()
    //     0xb84ae0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb84ae4: mov             x1, x0
    // 0xb84ae8: ldur            x0, [fp, #-8]
    // 0xb84aec: StoreField: r1->field_f = r0
    //     0xb84aec: stur            w0, [x1, #0xf]
    // 0xb84af0: r16 = ": "
    //     0xb84af0: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xb84af4: StoreField: r1->field_13 = r16
    //     0xb84af4: stur            w16, [x1, #0x13]
    // 0xb84af8: str             x1, [SP]
    // 0xb84afc: r0 = _interpolate()
    //     0xb84afc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb84b00: mov             x1, x0
    // 0xb84b04: ldur            x0, [fp, #-0x10]
    // 0xb84b08: stur            x1, [fp, #-8]
    // 0xb84b0c: LoadField: r2 = r0->field_f
    //     0xb84b0c: ldur            w2, [x0, #0xf]
    // 0xb84b10: DecompressPointer r2
    //     0xb84b10: add             x2, x2, HEAP, lsl #32
    // 0xb84b14: LoadField: r0 = r2->field_f
    //     0xb84b14: ldur            w0, [x2, #0xf]
    // 0xb84b18: DecompressPointer r0
    //     0xb84b18: add             x0, x0, HEAP, lsl #32
    // 0xb84b1c: LoadField: d0 = r0->field_f
    //     0xb84b1c: ldur            d0, [x0, #0xf]
    // 0xb84b20: stur            d0, [fp, #-0x28]
    // 0xb84b24: r0 = TextStyle()
    //     0xb84b24: bl              #0x624cf4  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb84b28: mov             x1, x0
    // 0xb84b2c: r0 = true
    //     0xb84b2c: add             x0, NULL, #0x20  ; true
    // 0xb84b30: stur            x1, [fp, #-0x18]
    // 0xb84b34: StoreField: r1->field_7 = r0
    //     0xb84b34: stur            w0, [x1, #7]
    // 0xb84b38: ldur            d0, [fp, #-0x28]
    // 0xb84b3c: r0 = inline_Allocate_Double()
    //     0xb84b3c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb84b40: add             x0, x0, #0x10
    //     0xb84b44: cmp             x2, x0
    //     0xb84b48: b.ls            #0xb84c2c
    //     0xb84b4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb84b50: sub             x0, x0, #0xf
    //     0xb84b54: movz            x2, #0xe15c
    //     0xb84b58: movk            x2, #0x3, lsl #16
    //     0xb84b5c: stur            x2, [x0, #-1]
    // 0xb84b60: StoreField: r0->field_7 = d0
    //     0xb84b60: stur            d0, [x0, #7]
    // 0xb84b64: StoreField: r1->field_1f = r0
    //     0xb84b64: stur            w0, [x1, #0x1f]
    // 0xb84b68: ldr             x0, [fp, #0x10]
    // 0xb84b6c: LoadField: r2 = r0->field_f
    //     0xb84b6c: ldur            w2, [x0, #0xf]
    // 0xb84b70: DecompressPointer r2
    //     0xb84b70: add             x2, x2, HEAP, lsl #32
    // 0xb84b74: stur            x2, [fp, #-0x10]
    // 0xb84b78: r0 = TextSpan()
    //     0xb84b78: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb84b7c: mov             x3, x0
    // 0xb84b80: ldur            x0, [fp, #-0x10]
    // 0xb84b84: stur            x3, [fp, #-0x20]
    // 0xb84b88: StoreField: r3->field_b = r0
    //     0xb84b88: stur            w0, [x3, #0xb]
    // 0xb84b8c: r0 = Instance__DeferringMouseCursor
    //     0xb84b8c: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb84b90: ArrayStore: r3[0] = r0  ; List_4
    //     0xb84b90: stur            w0, [x3, #0x17]
    // 0xb84b94: r1 = Instance_TextStyle
    //     0xb84b94: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ec8] Obj!TextStyle@e1b181
    //     0xb84b98: ldr             x1, [x1, #0xec8]
    // 0xb84b9c: StoreField: r3->field_7 = r1
    //     0xb84b9c: stur            w1, [x3, #7]
    // 0xb84ba0: r1 = Null
    //     0xb84ba0: mov             x1, NULL
    // 0xb84ba4: r2 = 2
    //     0xb84ba4: movz            x2, #0x2
    // 0xb84ba8: r0 = AllocateArray()
    //     0xb84ba8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb84bac: mov             x2, x0
    // 0xb84bb0: ldur            x0, [fp, #-0x20]
    // 0xb84bb4: stur            x2, [fp, #-0x10]
    // 0xb84bb8: StoreField: r2->field_f = r0
    //     0xb84bb8: stur            w0, [x2, #0xf]
    // 0xb84bbc: r1 = <InlineSpan>
    //     0xb84bbc: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xb84bc0: ldr             x1, [x1, #0x5f0]
    // 0xb84bc4: r0 = AllocateGrowableArray()
    //     0xb84bc4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb84bc8: mov             x1, x0
    // 0xb84bcc: ldur            x0, [fp, #-0x10]
    // 0xb84bd0: stur            x1, [fp, #-0x20]
    // 0xb84bd4: StoreField: r1->field_f = r0
    //     0xb84bd4: stur            w0, [x1, #0xf]
    // 0xb84bd8: r0 = 2
    //     0xb84bd8: movz            x0, #0x2
    // 0xb84bdc: StoreField: r1->field_b = r0
    //     0xb84bdc: stur            w0, [x1, #0xb]
    // 0xb84be0: r0 = TextSpan()
    //     0xb84be0: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xb84be4: mov             x1, x0
    // 0xb84be8: ldur            x0, [fp, #-8]
    // 0xb84bec: stur            x1, [fp, #-0x10]
    // 0xb84bf0: StoreField: r1->field_b = r0
    //     0xb84bf0: stur            w0, [x1, #0xb]
    // 0xb84bf4: ldur            x0, [fp, #-0x20]
    // 0xb84bf8: StoreField: r1->field_f = r0
    //     0xb84bf8: stur            w0, [x1, #0xf]
    // 0xb84bfc: r0 = Instance__DeferringMouseCursor
    //     0xb84bfc: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xb84c00: ArrayStore: r1[0] = r0  ; List_4
    //     0xb84c00: stur            w0, [x1, #0x17]
    // 0xb84c04: ldur            x0, [fp, #-0x18]
    // 0xb84c08: StoreField: r1->field_7 = r0
    //     0xb84c08: stur            w0, [x1, #7]
    // 0xb84c0c: r0 = Text()
    //     0xb84c0c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xb84c10: ldur            x1, [fp, #-0x10]
    // 0xb84c14: StoreField: r0->field_f = r1
    //     0xb84c14: stur            w1, [x0, #0xf]
    // 0xb84c18: LeaveFrame
    //     0xb84c18: mov             SP, fp
    //     0xb84c1c: ldp             fp, lr, [SP], #0x10
    // 0xb84c20: ret
    //     0xb84c20: ret             
    // 0xb84c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84c24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84c28: b               #0xb84ac8
    // 0xb84c2c: SaveReg d0
    //     0xb84c2c: str             q0, [SP, #-0x10]!
    // 0xb84c30: SaveReg r1
    //     0xb84c30: str             x1, [SP, #-8]!
    // 0xb84c34: r0 = AllocateDouble()
    //     0xb84c34: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb84c38: RestoreReg r1
    //     0xb84c38: ldr             x1, [SP], #8
    // 0xb84c3c: RestoreReg d0
    //     0xb84c3c: ldr             q0, [SP], #0x10
    // 0xb84c40: b               #0xb84b60
  }
  [closure] void <anonymous closure>(dynamic, String?, Map<String, String>, Element?) {
    // ** addr: 0xb84c44, size: 0x64
    // 0xb84c44: EnterFrame
    //     0xb84c44: stp             fp, lr, [SP, #-0x10]!
    //     0xb84c48: mov             fp, SP
    // 0xb84c4c: CheckStackOverflow
    //     0xb84c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84c50: cmp             SP, x16
    //     0xb84c54: b.ls            #0xb84ca0
    // 0xb84c58: ldr             x1, [fp, #0x20]
    // 0xb84c5c: cmp             w1, NULL
    // 0xb84c60: b.ne            #0xb84c74
    // 0xb84c64: r0 = Null
    //     0xb84c64: mov             x0, NULL
    // 0xb84c68: LeaveFrame
    //     0xb84c68: mov             SP, fp
    //     0xb84c6c: ldp             fp, lr, [SP], #0x10
    // 0xb84c70: ret
    //     0xb84c70: ret             
    // 0xb84c74: LoadField: r0 = r1->field_7
    //     0xb84c74: ldur            w0, [x1, #7]
    // 0xb84c78: cbnz            w0, #0xb84c8c
    // 0xb84c7c: r0 = Null
    //     0xb84c7c: mov             x0, NULL
    // 0xb84c80: LeaveFrame
    //     0xb84c80: mov             SP, fp
    //     0xb84c84: ldp             fp, lr, [SP], #0x10
    // 0xb84c88: ret
    //     0xb84c88: ret             
    // 0xb84c8c: r0 = handle()
    //     0xb84c8c: bl              #0xa3d0f4  ; [package:nuonline/app/modules/url_redirector/url_redirector.dart] UrlRedirector::handle
    // 0xb84c90: r0 = Null
    //     0xb84c90: mov             x0, NULL
    // 0xb84c94: LeaveFrame
    //     0xb84c94: mov             SP, fp
    //     0xb84c98: ldp             fp, lr, [SP], #0x10
    // 0xb84c9c: ret
    //     0xb84c9c: ret             
    // 0xb84ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84ca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84ca4: b               #0xb84c58
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic, ExtensionContext, Widget) {
    // ** addr: 0xb84ca8, size: 0x5c
    // 0xb84ca8: EnterFrame
    //     0xb84ca8: stp             fp, lr, [SP, #-0x10]!
    //     0xb84cac: mov             fp, SP
    // 0xb84cb0: r0 = SingleChildScrollView()
    //     0xb84cb0: bl              #0xa06cec  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb84cb4: r1 = Instance_Axis
    //     0xb84cb4: ldr             x1, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xb84cb8: StoreField: r0->field_b = r1
    //     0xb84cb8: stur            w1, [x0, #0xb]
    // 0xb84cbc: r1 = false
    //     0xb84cbc: add             x1, NULL, #0x30  ; false
    // 0xb84cc0: StoreField: r0->field_f = r1
    //     0xb84cc0: stur            w1, [x0, #0xf]
    // 0xb84cc4: ldr             x1, [fp, #0x10]
    // 0xb84cc8: StoreField: r0->field_23 = r1
    //     0xb84cc8: stur            w1, [x0, #0x23]
    // 0xb84ccc: r1 = Instance_DragStartBehavior
    //     0xb84ccc: ldr             x1, [PP, #0x6c38]  ; [pp+0x6c38] Obj!DragStartBehavior@e36d41
    // 0xb84cd0: StoreField: r0->field_27 = r1
    //     0xb84cd0: stur            w1, [x0, #0x27]
    // 0xb84cd4: r1 = Instance_Clip
    //     0xb84cd4: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xb84cd8: ldr             x1, [x1, #0x7c0]
    // 0xb84cdc: StoreField: r0->field_2b = r1
    //     0xb84cdc: stur            w1, [x0, #0x2b]
    // 0xb84ce0: r1 = Instance_HitTestBehavior
    //     0xb84ce0: add             x1, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xb84ce4: ldr             x1, [x1, #0x1c8]
    // 0xb84ce8: StoreField: r0->field_2f = r1
    //     0xb84ce8: stur            w1, [x0, #0x2f]
    // 0xb84cec: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xb84cec: add             x1, PP, #0x26, lsl #12  ; [pp+0x26f00] Obj!ScrollViewKeyboardDismissBehavior@e33b61
    //     0xb84cf0: ldr             x1, [x1, #0xf00]
    // 0xb84cf4: StoreField: r0->field_37 = r1
    //     0xb84cf4: stur            w1, [x0, #0x37]
    // 0xb84cf8: LeaveFrame
    //     0xb84cf8: mov             SP, fp
    //     0xb84cfc: ldp             fp, lr, [SP], #0x10
    // 0xb84d00: ret
    //     0xb84d00: ret             
  }
  [closure] Style <anonymous closure>(dynamic, ExtensionContext, Style) {
    // ** addr: 0xb84d04, size: 0x130
    // 0xb84d04: EnterFrame
    //     0xb84d04: stp             fp, lr, [SP, #-0x10]!
    //     0xb84d08: mov             fp, SP
    // 0xb84d0c: AllocStack(0x40)
    //     0xb84d0c: sub             SP, SP, #0x40
    // 0xb84d10: CheckStackOverflow
    //     0xb84d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84d14: cmp             SP, x16
    //     0xb84d18: b.ls            #0xb84e24
    // 0xb84d1c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb84d1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb84d20: ldr             x0, [x0, #0x2670]
    //     0xb84d24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb84d28: cmp             w0, w16
    //     0xb84d2c: b.ne            #0xb84d38
    //     0xb84d30: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb84d34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb84d38: r0 = GetNavigation.textTheme()
    //     0xb84d38: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb84d3c: LoadField: r1 = r0->field_27
    //     0xb84d3c: ldur            w1, [x0, #0x27]
    // 0xb84d40: DecompressPointer r1
    //     0xb84d40: add             x1, x1, HEAP, lsl #32
    // 0xb84d44: cmp             w1, NULL
    // 0xb84d48: b.ne            #0xb84d54
    // 0xb84d4c: r0 = Null
    //     0xb84d4c: mov             x0, NULL
    // 0xb84d50: b               #0xb84d5c
    // 0xb84d54: LoadField: r0 = r1->field_b
    //     0xb84d54: ldur            w0, [x1, #0xb]
    // 0xb84d58: DecompressPointer r0
    //     0xb84d58: add             x0, x0, HEAP, lsl #32
    // 0xb84d5c: stur            x0, [fp, #-8]
    // 0xb84d60: r0 = GetNavigation.textTheme()
    //     0xb84d60: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb84d64: LoadField: r1 = r0->field_27
    //     0xb84d64: ldur            w1, [x0, #0x27]
    // 0xb84d68: DecompressPointer r1
    //     0xb84d68: add             x1, x1, HEAP, lsl #32
    // 0xb84d6c: cmp             w1, NULL
    // 0xb84d70: b.eq            #0xb84e2c
    // 0xb84d74: LoadField: r0 = r1->field_1f
    //     0xb84d74: ldur            w0, [x1, #0x1f]
    // 0xb84d78: DecompressPointer r0
    //     0xb84d78: add             x0, x0, HEAP, lsl #32
    // 0xb84d7c: cmp             w0, NULL
    // 0xb84d80: b.eq            #0xb84e30
    // 0xb84d84: LoadField: d0 = r0->field_7
    //     0xb84d84: ldur            d0, [x0, #7]
    // 0xb84d88: stur            d0, [fp, #-0x20]
    // 0xb84d8c: r0 = FontSize()
    //     0xb84d8c: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb84d90: ldur            d0, [fp, #-0x20]
    // 0xb84d94: stur            x0, [fp, #-0x10]
    // 0xb84d98: StoreField: r0->field_7 = d0
    //     0xb84d98: stur            d0, [x0, #7]
    // 0xb84d9c: r1 = Instance_Unit
    //     0xb84d9c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb84da0: ldr             x1, [x1, #0xa98]
    // 0xb84da4: StoreField: r0->field_f = r1
    //     0xb84da4: stur            w1, [x0, #0xf]
    // 0xb84da8: r0 = GetNavigation.textTheme()
    //     0xb84da8: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb84dac: LoadField: r1 = r0->field_27
    //     0xb84dac: ldur            w1, [x0, #0x27]
    // 0xb84db0: DecompressPointer r1
    //     0xb84db0: add             x1, x1, HEAP, lsl #32
    // 0xb84db4: cmp             w1, NULL
    // 0xb84db8: b.ne            #0xb84dc4
    // 0xb84dbc: r0 = Null
    //     0xb84dbc: mov             x0, NULL
    // 0xb84dc0: b               #0xb84dcc
    // 0xb84dc4: LoadField: r0 = r1->field_23
    //     0xb84dc4: ldur            w0, [x1, #0x23]
    // 0xb84dc8: DecompressPointer r0
    //     0xb84dc8: add             x0, x0, HEAP, lsl #32
    // 0xb84dcc: stur            x0, [fp, #-0x18]
    // 0xb84dd0: r0 = GetNavigation.textTheme()
    //     0xb84dd0: bl              #0x65dc58  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.textTheme
    // 0xb84dd4: LoadField: r1 = r0->field_27
    //     0xb84dd4: ldur            w1, [x0, #0x27]
    // 0xb84dd8: DecompressPointer r1
    //     0xb84dd8: add             x1, x1, HEAP, lsl #32
    // 0xb84ddc: cmp             w1, NULL
    // 0xb84de0: b.ne            #0xb84dec
    // 0xb84de4: r0 = Null
    //     0xb84de4: mov             x0, NULL
    // 0xb84de8: b               #0xb84df4
    // 0xb84dec: LoadField: r0 = r1->field_2b
    //     0xb84dec: ldur            w0, [x1, #0x2b]
    // 0xb84df0: DecompressPointer r0
    //     0xb84df0: add             x0, x0, HEAP, lsl #32
    // 0xb84df4: ldur            x16, [fp, #-8]
    // 0xb84df8: ldur            lr, [fp, #-0x10]
    // 0xb84dfc: stp             lr, x16, [SP, #0x10]
    // 0xb84e00: ldur            x16, [fp, #-0x18]
    // 0xb84e04: stp             x0, x16, [SP]
    // 0xb84e08: ldr             x1, [fp, #0x10]
    // 0xb84e0c: r4 = const [0, 0x5, 0x4, 0x1, color, 0x1, fontSize, 0x2, fontWeight, 0x3, letterSpacing, 0x4, null]
    //     0xb84e0c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ed0] List(13) [0, 0x5, 0x4, 0x1, "color", 0x1, "fontSize", 0x2, "fontWeight", 0x3, "letterSpacing", 0x4, Null]
    //     0xb84e10: ldr             x4, [x4, #0xed0]
    // 0xb84e14: r0 = copyWith()
    //     0xb84e14: bl              #0x9ab1ac  ; [package:flutter_html/src/style.dart] Style::copyWith
    // 0xb84e18: LeaveFrame
    //     0xb84e18: mov             SP, fp
    //     0xb84e1c: ldp             fp, lr, [SP], #0x10
    // 0xb84e20: ret
    //     0xb84e20: ret             
    // 0xb84e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84e24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84e28: b               #0xb84d1c
    // 0xb84e2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84e2c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84e30: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Style <anonymous closure>(dynamic, ExtensionContext, Style) {
    // ** addr: 0xb84e34, size: 0xd0
    // 0xb84e34: EnterFrame
    //     0xb84e34: stp             fp, lr, [SP, #-0x10]!
    //     0xb84e38: mov             fp, SP
    // 0xb84e3c: AllocStack(0x18)
    //     0xb84e3c: sub             SP, SP, #0x18
    // 0xb84e40: CheckStackOverflow
    //     0xb84e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84e44: cmp             SP, x16
    //     0xb84e48: b.ls            #0xb84efc
    // 0xb84e4c: ldr             x1, [fp, #0x18]
    // 0xb84e50: r0 = attributes()
    //     0xb84e50: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb84e54: mov             x1, x0
    // 0xb84e58: r2 = "class"
    //     0xb84e58: add             x2, PP, #0xd, lsl #12  ; [pp+0xd670] "class"
    //     0xb84e5c: ldr             x2, [x2, #0x670]
    // 0xb84e60: stur            x0, [fp, #-8]
    // 0xb84e64: r0 = _getValueOrData()
    //     0xb84e64: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb84e68: mov             x1, x0
    // 0xb84e6c: ldur            x0, [fp, #-8]
    // 0xb84e70: LoadField: r2 = r0->field_f
    //     0xb84e70: ldur            w2, [x0, #0xf]
    // 0xb84e74: DecompressPointer r2
    //     0xb84e74: add             x2, x2, HEAP, lsl #32
    // 0xb84e78: cmp             w2, w1
    // 0xb84e7c: b.ne            #0xb84e88
    // 0xb84e80: r0 = Null
    //     0xb84e80: mov             x0, NULL
    // 0xb84e84: b               #0xb84e8c
    // 0xb84e88: mov             x0, x1
    // 0xb84e8c: r1 = LoadClassIdInstr(r0)
    //     0xb84e8c: ldur            x1, [x0, #-1]
    //     0xb84e90: ubfx            x1, x1, #0xc, #0x14
    // 0xb84e94: r16 = "image"
    //     0xb84e94: add             x16, PP, #0xc, lsl #12  ; [pp+0xc520] "image"
    //     0xb84e98: ldr             x16, [x16, #0x520]
    // 0xb84e9c: stp             x16, x0, [SP]
    // 0xb84ea0: mov             x0, x1
    // 0xb84ea4: mov             lr, x0
    // 0xb84ea8: ldr             lr, [x21, lr, lsl #3]
    // 0xb84eac: blr             lr
    // 0xb84eb0: tbnz            w0, #4, #0xb84eec
    // 0xb84eb4: r0 = Margins()
    //     0xb84eb4: bl              #0x7582d4  ; AllocateMarginsStub -> Margins (size=0x28)
    // 0xb84eb8: mov             x1, x0
    // 0xb84ebc: d0 = 0.000000
    //     0xb84ebc: eor             v0.16b, v0.16b, v0.16b
    // 0xb84ec0: stur            x0, [fp, #-8]
    // 0xb84ec4: r0 = Margins.all()
    //     0xb84ec4: bl              #0x9aa758  ; [package:flutter_html/src/style/margin.dart] Margins::Margins.all
    // 0xb84ec8: ldur            x16, [fp, #-8]
    // 0xb84ecc: str             x16, [SP]
    // 0xb84ed0: ldr             x1, [fp, #0x10]
    // 0xb84ed4: r4 = const [0, 0x2, 0x1, 0x1, margin, 0x1, null]
    //     0xb84ed4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ee8] List(7) [0, 0x2, 0x1, 0x1, "margin", 0x1, Null]
    //     0xb84ed8: ldr             x4, [x4, #0xee8]
    // 0xb84edc: r0 = copyWith()
    //     0xb84edc: bl              #0x9ab1ac  ; [package:flutter_html/src/style.dart] Style::copyWith
    // 0xb84ee0: LeaveFrame
    //     0xb84ee0: mov             SP, fp
    //     0xb84ee4: ldp             fp, lr, [SP], #0x10
    // 0xb84ee8: ret
    //     0xb84ee8: ret             
    // 0xb84eec: ldr             x0, [fp, #0x10]
    // 0xb84ef0: LeaveFrame
    //     0xb84ef0: mov             SP, fp
    //     0xb84ef4: ldp             fp, lr, [SP], #0x10
    // 0xb84ef8: ret
    //     0xb84ef8: ret             
    // 0xb84efc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84efc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84f00: b               #0xb84e4c
  }
  [closure] Style <anonymous closure>(dynamic, ExtensionContext, Style) {
    // ** addr: 0xb84ff0, size: 0x324
    // 0xb84ff0: EnterFrame
    //     0xb84ff0: stp             fp, lr, [SP, #-0x10]!
    //     0xb84ff4: mov             fp, SP
    // 0xb84ff8: AllocStack(0x40)
    //     0xb84ff8: sub             SP, SP, #0x40
    // 0xb84ffc: SetupParameters()
    //     0xb84ffc: ldr             x0, [fp, #0x20]
    //     0xb85000: ldur            w2, [x0, #0x17]
    //     0xb85004: add             x2, x2, HEAP, lsl #32
    //     0xb85008: stur            x2, [fp, #-8]
    // 0xb8500c: CheckStackOverflow
    //     0xb8500c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85010: cmp             SP, x16
    //     0xb85014: b.ls            #0xb8530c
    // 0xb85018: ldr             x1, [fp, #0x18]
    // 0xb8501c: r0 = attributes()
    //     0xb8501c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85020: mov             x1, x0
    // 0xb85024: r2 = "style"
    //     0xb85024: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85028: ldr             x2, [x2, #0x9b8]
    // 0xb8502c: stur            x0, [fp, #-0x10]
    // 0xb85030: r0 = _getValueOrData()
    //     0xb85030: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb85034: mov             x1, x0
    // 0xb85038: ldur            x0, [fp, #-0x10]
    // 0xb8503c: LoadField: r2 = r0->field_f
    //     0xb8503c: ldur            w2, [x0, #0xf]
    // 0xb85040: DecompressPointer r2
    //     0xb85040: add             x2, x2, HEAP, lsl #32
    // 0xb85044: cmp             w2, w1
    // 0xb85048: b.ne            #0xb85054
    // 0xb8504c: r0 = Null
    //     0xb8504c: mov             x0, NULL
    // 0xb85050: b               #0xb85058
    // 0xb85054: mov             x0, x1
    // 0xb85058: r1 = LoadClassIdInstr(r0)
    //     0xb85058: ldur            x1, [x0, #-1]
    //     0xb8505c: ubfx            x1, x1, #0xc, #0x14
    // 0xb85060: r16 = "line-height:2;"
    //     0xb85060: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ef8] "line-height:2;"
    //     0xb85064: ldr             x16, [x16, #0xef8]
    // 0xb85068: stp             x16, x0, [SP]
    // 0xb8506c: mov             x0, x1
    // 0xb85070: mov             lr, x0
    // 0xb85074: ldr             lr, [x21, lr, lsl #3]
    // 0xb85078: blr             lr
    // 0xb8507c: tbz             w0, #4, #0xb85288
    // 0xb85080: ldr             x1, [fp, #0x18]
    // 0xb85084: r0 = attributes()
    //     0xb85084: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85088: mov             x1, x0
    // 0xb8508c: r2 = "style"
    //     0xb8508c: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85090: ldr             x2, [x2, #0x9b8]
    // 0xb85094: stur            x0, [fp, #-0x10]
    // 0xb85098: r0 = _getValueOrData()
    //     0xb85098: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb8509c: mov             x1, x0
    // 0xb850a0: ldur            x0, [fp, #-0x10]
    // 0xb850a4: LoadField: r2 = r0->field_f
    //     0xb850a4: ldur            w2, [x0, #0xf]
    // 0xb850a8: DecompressPointer r2
    //     0xb850a8: add             x2, x2, HEAP, lsl #32
    // 0xb850ac: cmp             w2, w1
    // 0xb850b0: b.ne            #0xb850bc
    // 0xb850b4: r0 = Null
    //     0xb850b4: mov             x0, NULL
    // 0xb850b8: b               #0xb850c0
    // 0xb850bc: mov             x0, x1
    // 0xb850c0: r1 = LoadClassIdInstr(r0)
    //     0xb850c0: ldur            x1, [x0, #-1]
    //     0xb850c4: ubfx            x1, x1, #0xc, #0x14
    // 0xb850c8: r16 = "font-size:18px;"
    //     0xb850c8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f00] "font-size:18px;"
    //     0xb850cc: ldr             x16, [x16, #0xf00]
    // 0xb850d0: stp             x16, x0, [SP]
    // 0xb850d4: mov             x0, x1
    // 0xb850d8: mov             lr, x0
    // 0xb850dc: ldr             lr, [x21, lr, lsl #3]
    // 0xb850e0: blr             lr
    // 0xb850e4: tbz             w0, #4, #0xb85288
    // 0xb850e8: ldr             x1, [fp, #0x18]
    // 0xb850ec: r0 = attributes()
    //     0xb850ec: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb850f0: mov             x1, x0
    // 0xb850f4: r2 = "style"
    //     0xb850f4: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb850f8: ldr             x2, [x2, #0x9b8]
    // 0xb850fc: stur            x0, [fp, #-0x10]
    // 0xb85100: r0 = _getValueOrData()
    //     0xb85100: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb85104: mov             x1, x0
    // 0xb85108: ldur            x0, [fp, #-0x10]
    // 0xb8510c: LoadField: r2 = r0->field_f
    //     0xb8510c: ldur            w2, [x0, #0xf]
    // 0xb85110: DecompressPointer r2
    //     0xb85110: add             x2, x2, HEAP, lsl #32
    // 0xb85114: cmp             w2, w1
    // 0xb85118: b.ne            #0xb85124
    // 0xb8511c: r0 = Null
    //     0xb8511c: mov             x0, NULL
    // 0xb85120: b               #0xb85128
    // 0xb85124: mov             x0, x1
    // 0xb85128: r1 = LoadClassIdInstr(r0)
    //     0xb85128: ldur            x1, [x0, #-1]
    //     0xb8512c: ubfx            x1, x1, #0xc, #0x14
    // 0xb85130: r16 = "font-size: 24px;"
    //     0xb85130: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f08] "font-size: 24px;"
    //     0xb85134: ldr             x16, [x16, #0xf08]
    // 0xb85138: stp             x16, x0, [SP]
    // 0xb8513c: mov             x0, x1
    // 0xb85140: mov             lr, x0
    // 0xb85144: ldr             lr, [x21, lr, lsl #3]
    // 0xb85148: blr             lr
    // 0xb8514c: tbz             w0, #4, #0xb85288
    // 0xb85150: ldr             x1, [fp, #0x18]
    // 0xb85154: r0 = attributes()
    //     0xb85154: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85158: mov             x1, x0
    // 0xb8515c: r2 = "style"
    //     0xb8515c: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85160: ldr             x2, [x2, #0x9b8]
    // 0xb85164: stur            x0, [fp, #-0x10]
    // 0xb85168: r0 = _getValueOrData()
    //     0xb85168: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb8516c: mov             x1, x0
    // 0xb85170: ldur            x0, [fp, #-0x10]
    // 0xb85174: LoadField: r2 = r0->field_f
    //     0xb85174: ldur            w2, [x0, #0xf]
    // 0xb85178: DecompressPointer r2
    //     0xb85178: add             x2, x2, HEAP, lsl #32
    // 0xb8517c: cmp             w2, w1
    // 0xb85180: b.ne            #0xb8518c
    // 0xb85184: r0 = Null
    //     0xb85184: mov             x0, NULL
    // 0xb85188: b               #0xb85190
    // 0xb8518c: mov             x0, x1
    // 0xb85190: r1 = LoadClassIdInstr(r0)
    //     0xb85190: ldur            x1, [x0, #-1]
    //     0xb85194: ubfx            x1, x1, #0xc, #0x14
    // 0xb85198: r16 = "font-size:24px;"
    //     0xb85198: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f10] "font-size:24px;"
    //     0xb8519c: ldr             x16, [x16, #0xf10]
    // 0xb851a0: stp             x16, x0, [SP]
    // 0xb851a4: mov             x0, x1
    // 0xb851a8: mov             lr, x0
    // 0xb851ac: ldr             lr, [x21, lr, lsl #3]
    // 0xb851b0: blr             lr
    // 0xb851b4: tbz             w0, #4, #0xb85288
    // 0xb851b8: ldr             x1, [fp, #0x18]
    // 0xb851bc: r0 = attributes()
    //     0xb851bc: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb851c0: mov             x1, x0
    // 0xb851c4: r2 = "style"
    //     0xb851c4: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb851c8: ldr             x2, [x2, #0x9b8]
    // 0xb851cc: stur            x0, [fp, #-0x10]
    // 0xb851d0: r0 = _getValueOrData()
    //     0xb851d0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb851d4: mov             x1, x0
    // 0xb851d8: ldur            x0, [fp, #-0x10]
    // 0xb851dc: LoadField: r2 = r0->field_f
    //     0xb851dc: ldur            w2, [x0, #0xf]
    // 0xb851e0: DecompressPointer r2
    //     0xb851e0: add             x2, x2, HEAP, lsl #32
    // 0xb851e4: cmp             w2, w1
    // 0xb851e8: b.ne            #0xb851f4
    // 0xb851ec: r0 = Null
    //     0xb851ec: mov             x0, NULL
    // 0xb851f0: b               #0xb851f8
    // 0xb851f4: mov             x0, x1
    // 0xb851f8: r1 = LoadClassIdInstr(r0)
    //     0xb851f8: ldur            x1, [x0, #-1]
    //     0xb851fc: ubfx            x1, x1, #0xc, #0x14
    // 0xb85200: r16 = "font-size:24px"
    //     0xb85200: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f18] "font-size:24px"
    //     0xb85204: ldr             x16, [x16, #0xf18]
    // 0xb85208: stp             x16, x0, [SP]
    // 0xb8520c: mov             x0, x1
    // 0xb85210: mov             lr, x0
    // 0xb85214: ldr             lr, [x21, lr, lsl #3]
    // 0xb85218: blr             lr
    // 0xb8521c: tbz             w0, #4, #0xb85288
    // 0xb85220: ldr             x1, [fp, #0x18]
    // 0xb85224: r0 = attributes()
    //     0xb85224: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85228: mov             x1, x0
    // 0xb8522c: r2 = "dir"
    //     0xb8522c: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f20] "dir"
    //     0xb85230: ldr             x2, [x2, #0xf20]
    // 0xb85234: stur            x0, [fp, #-0x10]
    // 0xb85238: r0 = _getValueOrData()
    //     0xb85238: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb8523c: mov             x1, x0
    // 0xb85240: ldur            x0, [fp, #-0x10]
    // 0xb85244: LoadField: r2 = r0->field_f
    //     0xb85244: ldur            w2, [x0, #0xf]
    // 0xb85248: DecompressPointer r2
    //     0xb85248: add             x2, x2, HEAP, lsl #32
    // 0xb8524c: cmp             w2, w1
    // 0xb85250: b.ne            #0xb8525c
    // 0xb85254: r0 = Null
    //     0xb85254: mov             x0, NULL
    // 0xb85258: b               #0xb85260
    // 0xb8525c: mov             x0, x1
    // 0xb85260: r1 = LoadClassIdInstr(r0)
    //     0xb85260: ldur            x1, [x0, #-1]
    //     0xb85264: ubfx            x1, x1, #0xc, #0x14
    // 0xb85268: r16 = "rtl"
    //     0xb85268: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f28] "rtl"
    //     0xb8526c: ldr             x16, [x16, #0xf28]
    // 0xb85270: stp             x16, x0, [SP]
    // 0xb85274: mov             x0, x1
    // 0xb85278: mov             lr, x0
    // 0xb8527c: ldr             lr, [x21, lr, lsl #3]
    // 0xb85280: blr             lr
    // 0xb85284: tbnz            w0, #4, #0xb852fc
    // 0xb85288: ldur            x0, [fp, #-8]
    // 0xb8528c: LoadField: r1 = r0->field_f
    //     0xb8528c: ldur            w1, [x0, #0xf]
    // 0xb85290: DecompressPointer r1
    //     0xb85290: add             x1, x1, HEAP, lsl #32
    // 0xb85294: LoadField: r0 = r1->field_f
    //     0xb85294: ldur            w0, [x1, #0xf]
    // 0xb85298: DecompressPointer r0
    //     0xb85298: add             x0, x0, HEAP, lsl #32
    // 0xb8529c: LoadField: d0 = r0->field_7
    //     0xb8529c: ldur            d0, [x0, #7]
    // 0xb852a0: stur            d0, [fp, #-0x18]
    // 0xb852a4: r0 = FontSize()
    //     0xb852a4: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb852a8: ldur            d0, [fp, #-0x18]
    // 0xb852ac: StoreField: r0->field_7 = d0
    //     0xb852ac: stur            d0, [x0, #7]
    // 0xb852b0: r1 = Instance_Unit
    //     0xb852b0: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb852b4: ldr             x1, [x1, #0xa98]
    // 0xb852b8: StoreField: r0->field_f = r1
    //     0xb852b8: stur            w1, [x0, #0xf]
    // 0xb852bc: r16 = Instance_TextDirection
    //     0xb852bc: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xb852c0: r30 = Instance_TextAlign
    //     0xb852c0: ldr             lr, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0xb852c4: stp             lr, x16, [SP, #0x18]
    // 0xb852c8: r16 = "OmarNaskh"
    //     0xb852c8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24bc8] "OmarNaskh"
    //     0xb852cc: ldr             x16, [x16, #0xbc8]
    // 0xb852d0: stp             x0, x16, [SP, #8]
    // 0xb852d4: r16 = Instance_LineHeight
    //     0xb852d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f30] Obj!LineHeight@e0fb01
    //     0xb852d8: ldr             x16, [x16, #0xf30]
    // 0xb852dc: str             x16, [SP]
    // 0xb852e0: ldr             x1, [fp, #0x10]
    // 0xb852e4: r4 = const [0, 0x6, 0x5, 0x1, direction, 0x1, fontFamily, 0x3, fontSize, 0x4, lineHeight, 0x5, textAlign, 0x2, null]
    //     0xb852e4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36f38] List(15) [0, 0x6, 0x5, 0x1, "direction", 0x1, "fontFamily", 0x3, "fontSize", 0x4, "lineHeight", 0x5, "textAlign", 0x2, Null]
    //     0xb852e8: ldr             x4, [x4, #0xf38]
    // 0xb852ec: r0 = copyWith()
    //     0xb852ec: bl              #0x9ab1ac  ; [package:flutter_html/src/style.dart] Style::copyWith
    // 0xb852f0: LeaveFrame
    //     0xb852f0: mov             SP, fp
    //     0xb852f4: ldp             fp, lr, [SP], #0x10
    // 0xb852f8: ret
    //     0xb852f8: ret             
    // 0xb852fc: ldr             x0, [fp, #0x10]
    // 0xb85300: LeaveFrame
    //     0xb85300: mov             SP, fp
    //     0xb85304: ldp             fp, lr, [SP], #0x10
    // 0xb85308: ret
    //     0xb85308: ret             
    // 0xb8530c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8530c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85310: b               #0xb85018
  }
  [closure] double <anonymous closure>(dynamic) {
    // ** addr: 0xb85314, size: 0x6c
    // 0xb85314: EnterFrame
    //     0xb85314: stp             fp, lr, [SP, #-0x10]!
    //     0xb85318: mov             fp, SP
    // 0xb8531c: ldr             x1, [fp, #0x10]
    // 0xb85320: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb85320: ldur            w2, [x1, #0x17]
    // 0xb85324: DecompressPointer r2
    //     0xb85324: add             x2, x2, HEAP, lsl #32
    // 0xb85328: LoadField: r1 = r2->field_f
    //     0xb85328: ldur            w1, [x2, #0xf]
    // 0xb8532c: DecompressPointer r1
    //     0xb8532c: add             x1, x1, HEAP, lsl #32
    // 0xb85330: LoadField: r2 = r1->field_f
    //     0xb85330: ldur            w2, [x1, #0xf]
    // 0xb85334: DecompressPointer r2
    //     0xb85334: add             x2, x2, HEAP, lsl #32
    // 0xb85338: LoadField: d0 = r2->field_7
    //     0xb85338: ldur            d0, [x2, #7]
    // 0xb8533c: r0 = inline_Allocate_Double()
    //     0xb8533c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb85340: add             x0, x0, #0x10
    //     0xb85344: cmp             x1, x0
    //     0xb85348: b.ls            #0xb85370
    //     0xb8534c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb85350: sub             x0, x0, #0xf
    //     0xb85354: movz            x1, #0xe15c
    //     0xb85358: movk            x1, #0x3, lsl #16
    //     0xb8535c: stur            x1, [x0, #-1]
    // 0xb85360: StoreField: r0->field_7 = d0
    //     0xb85360: stur            d0, [x0, #7]
    // 0xb85364: LeaveFrame
    //     0xb85364: mov             SP, fp
    //     0xb85368: ldp             fp, lr, [SP], #0x10
    // 0xb8536c: ret
    //     0xb8536c: ret             
    // 0xb85370: SaveReg d0
    //     0xb85370: str             q0, [SP, #-0x10]!
    // 0xb85374: r0 = AllocateDouble()
    //     0xb85374: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb85378: RestoreReg d0
    //     0xb85378: ldr             q0, [SP], #0x10
    // 0xb8537c: b               #0xb85360
  }
  [closure] Style <anonymous closure>(dynamic, ExtensionContext, Style) {
    // ** addr: 0xb85380, size: 0x324
    // 0xb85380: EnterFrame
    //     0xb85380: stp             fp, lr, [SP, #-0x10]!
    //     0xb85384: mov             fp, SP
    // 0xb85388: AllocStack(0x40)
    //     0xb85388: sub             SP, SP, #0x40
    // 0xb8538c: SetupParameters()
    //     0xb8538c: ldr             x0, [fp, #0x20]
    //     0xb85390: ldur            w2, [x0, #0x17]
    //     0xb85394: add             x2, x2, HEAP, lsl #32
    //     0xb85398: stur            x2, [fp, #-8]
    // 0xb8539c: CheckStackOverflow
    //     0xb8539c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb853a0: cmp             SP, x16
    //     0xb853a4: b.ls            #0xb8569c
    // 0xb853a8: ldr             x1, [fp, #0x18]
    // 0xb853ac: r0 = attributes()
    //     0xb853ac: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb853b0: mov             x1, x0
    // 0xb853b4: r2 = "style"
    //     0xb853b4: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb853b8: ldr             x2, [x2, #0x9b8]
    // 0xb853bc: stur            x0, [fp, #-0x10]
    // 0xb853c0: r0 = _getValueOrData()
    //     0xb853c0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb853c4: mov             x1, x0
    // 0xb853c8: ldur            x0, [fp, #-0x10]
    // 0xb853cc: LoadField: r2 = r0->field_f
    //     0xb853cc: ldur            w2, [x0, #0xf]
    // 0xb853d0: DecompressPointer r2
    //     0xb853d0: add             x2, x2, HEAP, lsl #32
    // 0xb853d4: cmp             w2, w1
    // 0xb853d8: b.ne            #0xb853e4
    // 0xb853dc: r0 = Null
    //     0xb853dc: mov             x0, NULL
    // 0xb853e0: b               #0xb853e8
    // 0xb853e4: mov             x0, x1
    // 0xb853e8: r1 = LoadClassIdInstr(r0)
    //     0xb853e8: ldur            x1, [x0, #-1]
    //     0xb853ec: ubfx            x1, x1, #0xc, #0x14
    // 0xb853f0: r16 = "text-align: right;"
    //     0xb853f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f40] "text-align: right;"
    //     0xb853f4: ldr             x16, [x16, #0xf40]
    // 0xb853f8: stp             x16, x0, [SP]
    // 0xb853fc: mov             x0, x1
    // 0xb85400: mov             lr, x0
    // 0xb85404: ldr             lr, [x21, lr, lsl #3]
    // 0xb85408: blr             lr
    // 0xb8540c: tbz             w0, #4, #0xb85618
    // 0xb85410: ldr             x1, [fp, #0x18]
    // 0xb85414: r0 = attributes()
    //     0xb85414: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85418: mov             x1, x0
    // 0xb8541c: r2 = "style"
    //     0xb8541c: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85420: ldr             x2, [x2, #0x9b8]
    // 0xb85424: stur            x0, [fp, #-0x10]
    // 0xb85428: r0 = _getValueOrData()
    //     0xb85428: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb8542c: mov             x1, x0
    // 0xb85430: ldur            x0, [fp, #-0x10]
    // 0xb85434: LoadField: r2 = r0->field_f
    //     0xb85434: ldur            w2, [x0, #0xf]
    // 0xb85438: DecompressPointer r2
    //     0xb85438: add             x2, x2, HEAP, lsl #32
    // 0xb8543c: cmp             w2, w1
    // 0xb85440: b.ne            #0xb8544c
    // 0xb85444: r0 = Null
    //     0xb85444: mov             x0, NULL
    // 0xb85448: b               #0xb85450
    // 0xb8544c: mov             x0, x1
    // 0xb85450: r1 = LoadClassIdInstr(r0)
    //     0xb85450: ldur            x1, [x0, #-1]
    //     0xb85454: ubfx            x1, x1, #0xc, #0x14
    // 0xb85458: r16 = "font-size:18px;"
    //     0xb85458: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f00] "font-size:18px;"
    //     0xb8545c: ldr             x16, [x16, #0xf00]
    // 0xb85460: stp             x16, x0, [SP]
    // 0xb85464: mov             x0, x1
    // 0xb85468: mov             lr, x0
    // 0xb8546c: ldr             lr, [x21, lr, lsl #3]
    // 0xb85470: blr             lr
    // 0xb85474: tbz             w0, #4, #0xb85618
    // 0xb85478: ldr             x1, [fp, #0x18]
    // 0xb8547c: r0 = attributes()
    //     0xb8547c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85480: mov             x1, x0
    // 0xb85484: r2 = "style"
    //     0xb85484: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85488: ldr             x2, [x2, #0x9b8]
    // 0xb8548c: stur            x0, [fp, #-0x10]
    // 0xb85490: r0 = _getValueOrData()
    //     0xb85490: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb85494: mov             x1, x0
    // 0xb85498: ldur            x0, [fp, #-0x10]
    // 0xb8549c: LoadField: r2 = r0->field_f
    //     0xb8549c: ldur            w2, [x0, #0xf]
    // 0xb854a0: DecompressPointer r2
    //     0xb854a0: add             x2, x2, HEAP, lsl #32
    // 0xb854a4: cmp             w2, w1
    // 0xb854a8: b.ne            #0xb854b4
    // 0xb854ac: r0 = Null
    //     0xb854ac: mov             x0, NULL
    // 0xb854b0: b               #0xb854b8
    // 0xb854b4: mov             x0, x1
    // 0xb854b8: r1 = LoadClassIdInstr(r0)
    //     0xb854b8: ldur            x1, [x0, #-1]
    //     0xb854bc: ubfx            x1, x1, #0xc, #0x14
    // 0xb854c0: r16 = "font-size: 24px;"
    //     0xb854c0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f08] "font-size: 24px;"
    //     0xb854c4: ldr             x16, [x16, #0xf08]
    // 0xb854c8: stp             x16, x0, [SP]
    // 0xb854cc: mov             x0, x1
    // 0xb854d0: mov             lr, x0
    // 0xb854d4: ldr             lr, [x21, lr, lsl #3]
    // 0xb854d8: blr             lr
    // 0xb854dc: tbz             w0, #4, #0xb85618
    // 0xb854e0: ldr             x1, [fp, #0x18]
    // 0xb854e4: r0 = attributes()
    //     0xb854e4: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb854e8: mov             x1, x0
    // 0xb854ec: r2 = "style"
    //     0xb854ec: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb854f0: ldr             x2, [x2, #0x9b8]
    // 0xb854f4: stur            x0, [fp, #-0x10]
    // 0xb854f8: r0 = _getValueOrData()
    //     0xb854f8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb854fc: mov             x1, x0
    // 0xb85500: ldur            x0, [fp, #-0x10]
    // 0xb85504: LoadField: r2 = r0->field_f
    //     0xb85504: ldur            w2, [x0, #0xf]
    // 0xb85508: DecompressPointer r2
    //     0xb85508: add             x2, x2, HEAP, lsl #32
    // 0xb8550c: cmp             w2, w1
    // 0xb85510: b.ne            #0xb8551c
    // 0xb85514: r0 = Null
    //     0xb85514: mov             x0, NULL
    // 0xb85518: b               #0xb85520
    // 0xb8551c: mov             x0, x1
    // 0xb85520: r1 = LoadClassIdInstr(r0)
    //     0xb85520: ldur            x1, [x0, #-1]
    //     0xb85524: ubfx            x1, x1, #0xc, #0x14
    // 0xb85528: r16 = "font-size:24px;"
    //     0xb85528: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f10] "font-size:24px;"
    //     0xb8552c: ldr             x16, [x16, #0xf10]
    // 0xb85530: stp             x16, x0, [SP]
    // 0xb85534: mov             x0, x1
    // 0xb85538: mov             lr, x0
    // 0xb8553c: ldr             lr, [x21, lr, lsl #3]
    // 0xb85540: blr             lr
    // 0xb85544: tbz             w0, #4, #0xb85618
    // 0xb85548: ldr             x1, [fp, #0x18]
    // 0xb8554c: r0 = attributes()
    //     0xb8554c: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb85550: mov             x1, x0
    // 0xb85554: r2 = "style"
    //     0xb85554: add             x2, PP, #8, lsl #12  ; [pp+0x89b8] "style"
    //     0xb85558: ldr             x2, [x2, #0x9b8]
    // 0xb8555c: stur            x0, [fp, #-0x10]
    // 0xb85560: r0 = _getValueOrData()
    //     0xb85560: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb85564: mov             x1, x0
    // 0xb85568: ldur            x0, [fp, #-0x10]
    // 0xb8556c: LoadField: r2 = r0->field_f
    //     0xb8556c: ldur            w2, [x0, #0xf]
    // 0xb85570: DecompressPointer r2
    //     0xb85570: add             x2, x2, HEAP, lsl #32
    // 0xb85574: cmp             w2, w1
    // 0xb85578: b.ne            #0xb85584
    // 0xb8557c: r0 = Null
    //     0xb8557c: mov             x0, NULL
    // 0xb85580: b               #0xb85588
    // 0xb85584: mov             x0, x1
    // 0xb85588: r1 = LoadClassIdInstr(r0)
    //     0xb85588: ldur            x1, [x0, #-1]
    //     0xb8558c: ubfx            x1, x1, #0xc, #0x14
    // 0xb85590: r16 = "font-size:24px"
    //     0xb85590: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f18] "font-size:24px"
    //     0xb85594: ldr             x16, [x16, #0xf18]
    // 0xb85598: stp             x16, x0, [SP]
    // 0xb8559c: mov             x0, x1
    // 0xb855a0: mov             lr, x0
    // 0xb855a4: ldr             lr, [x21, lr, lsl #3]
    // 0xb855a8: blr             lr
    // 0xb855ac: tbz             w0, #4, #0xb85618
    // 0xb855b0: ldr             x1, [fp, #0x18]
    // 0xb855b4: r0 = attributes()
    //     0xb855b4: bl              #0xb84f04  ; [package:flutter_html/src/extension/extension_context.dart] ExtensionContext::attributes
    // 0xb855b8: mov             x1, x0
    // 0xb855bc: r2 = "dir"
    //     0xb855bc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f20] "dir"
    //     0xb855c0: ldr             x2, [x2, #0xf20]
    // 0xb855c4: stur            x0, [fp, #-0x10]
    // 0xb855c8: r0 = _getValueOrData()
    //     0xb855c8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb855cc: mov             x1, x0
    // 0xb855d0: ldur            x0, [fp, #-0x10]
    // 0xb855d4: LoadField: r2 = r0->field_f
    //     0xb855d4: ldur            w2, [x0, #0xf]
    // 0xb855d8: DecompressPointer r2
    //     0xb855d8: add             x2, x2, HEAP, lsl #32
    // 0xb855dc: cmp             w2, w1
    // 0xb855e0: b.ne            #0xb855ec
    // 0xb855e4: r0 = Null
    //     0xb855e4: mov             x0, NULL
    // 0xb855e8: b               #0xb855f0
    // 0xb855ec: mov             x0, x1
    // 0xb855f0: r1 = LoadClassIdInstr(r0)
    //     0xb855f0: ldur            x1, [x0, #-1]
    //     0xb855f4: ubfx            x1, x1, #0xc, #0x14
    // 0xb855f8: r16 = "rtl"
    //     0xb855f8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f28] "rtl"
    //     0xb855fc: ldr             x16, [x16, #0xf28]
    // 0xb85600: stp             x16, x0, [SP]
    // 0xb85604: mov             x0, x1
    // 0xb85608: mov             lr, x0
    // 0xb8560c: ldr             lr, [x21, lr, lsl #3]
    // 0xb85610: blr             lr
    // 0xb85614: tbnz            w0, #4, #0xb8568c
    // 0xb85618: ldur            x0, [fp, #-8]
    // 0xb8561c: LoadField: r1 = r0->field_f
    //     0xb8561c: ldur            w1, [x0, #0xf]
    // 0xb85620: DecompressPointer r1
    //     0xb85620: add             x1, x1, HEAP, lsl #32
    // 0xb85624: LoadField: r0 = r1->field_f
    //     0xb85624: ldur            w0, [x1, #0xf]
    // 0xb85628: DecompressPointer r0
    //     0xb85628: add             x0, x0, HEAP, lsl #32
    // 0xb8562c: LoadField: d0 = r0->field_7
    //     0xb8562c: ldur            d0, [x0, #7]
    // 0xb85630: stur            d0, [fp, #-0x18]
    // 0xb85634: r0 = FontSize()
    //     0xb85634: bl              #0x9b75e0  ; AllocateFontSizeStub -> FontSize (size=0x14)
    // 0xb85638: ldur            d0, [fp, #-0x18]
    // 0xb8563c: StoreField: r0->field_7 = d0
    //     0xb8563c: stur            d0, [x0, #7]
    // 0xb85640: r1 = Instance_Unit
    //     0xb85640: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a98] Obj!Unit@e32de1
    //     0xb85644: ldr             x1, [x1, #0xa98]
    // 0xb85648: StoreField: r0->field_f = r1
    //     0xb85648: stur            w1, [x0, #0xf]
    // 0xb8564c: r16 = Instance_TextDirection
    //     0xb8564c: ldr             x16, [PP, #0x2898]  ; [pp+0x2898] Obj!TextDirection@e392e1
    // 0xb85650: r30 = Instance_TextAlign
    //     0xb85650: ldr             lr, [PP, #0x4910]  ; [pp+0x4910] Obj!TextAlign@e394c1
    // 0xb85654: stp             lr, x16, [SP, #0x18]
    // 0xb85658: r16 = "ArefRuqaa"
    //     0xb85658: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f48] "ArefRuqaa"
    //     0xb8565c: ldr             x16, [x16, #0xf48]
    // 0xb85660: stp             x0, x16, [SP, #8]
    // 0xb85664: r16 = Instance_LineHeight
    //     0xb85664: add             x16, PP, #0x36, lsl #12  ; [pp+0x36f30] Obj!LineHeight@e0fb01
    //     0xb85668: ldr             x16, [x16, #0xf30]
    // 0xb8566c: str             x16, [SP]
    // 0xb85670: ldr             x1, [fp, #0x10]
    // 0xb85674: r4 = const [0, 0x6, 0x5, 0x1, direction, 0x1, fontFamily, 0x3, fontSize, 0x4, lineHeight, 0x5, textAlign, 0x2, null]
    //     0xb85674: add             x4, PP, #0x36, lsl #12  ; [pp+0x36f38] List(15) [0, 0x6, 0x5, 0x1, "direction", 0x1, "fontFamily", 0x3, "fontSize", 0x4, "lineHeight", 0x5, "textAlign", 0x2, Null]
    //     0xb85678: ldr             x4, [x4, #0xf38]
    // 0xb8567c: r0 = copyWith()
    //     0xb8567c: bl              #0x9ab1ac  ; [package:flutter_html/src/style.dart] Style::copyWith
    // 0xb85680: LeaveFrame
    //     0xb85680: mov             SP, fp
    //     0xb85684: ldp             fp, lr, [SP], #0x10
    // 0xb85688: ret
    //     0xb85688: ret             
    // 0xb8568c: ldr             x0, [fp, #0x10]
    // 0xb85690: LeaveFrame
    //     0xb85690: mov             SP, fp
    //     0xb85694: ldp             fp, lr, [SP], #0x10
    // 0xb85698: ret
    //     0xb85698: ret             
    // 0xb8569c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8569c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb856a0: b               #0xb853a8
  }
}
