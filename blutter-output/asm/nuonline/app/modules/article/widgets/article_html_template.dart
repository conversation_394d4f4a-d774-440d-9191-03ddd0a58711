// lib: , url: package:nuonline/app/modules/article/widgets/article_html_template.dart

// class id: 1050153, size: 0x8
class :: {
}

// class id: 1066, size: 0x10, field offset: 0x8
class ArticleHtmlTemplate extends Object {

  _ build(/* No info */) {
    // ** addr: 0xbc2428, size: 0x20c
    // 0xbc2428: EnterFrame
    //     0xbc2428: stp             fp, lr, [SP, #-0x10]!
    //     0xbc242c: mov             fp, SP
    // 0xbc2430: AllocStack(0x18)
    //     0xbc2430: sub             SP, SP, #0x18
    // 0xbc2434: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc2434: mov             x0, x1
    //     0xbc2438: stur            x1, [fp, #-8]
    // 0xbc243c: CheckStackOverflow
    //     0xbc243c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2440: cmp             SP, x16
    //     0xbc2444: b.ls            #0xbc262c
    // 0xbc2448: r1 = Null
    //     0xbc2448: mov             x1, NULL
    // 0xbc244c: r2 = 30
    //     0xbc244c: movz            x2, #0x1e
    // 0xbc2450: r0 = AllocateArray()
    //     0xbc2450: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2454: stur            x0, [fp, #-0x10]
    // 0xbc2458: r16 = "<html>\n  "
    //     0xbc2458: add             x16, PP, #0x40, lsl #12  ; [pp+0x40ba8] "<html>\n  "
    //     0xbc245c: ldr             x16, [x16, #0xba8]
    // 0xbc2460: StoreField: r0->field_f = r16
    //     0xbc2460: stur            w16, [x0, #0xf]
    // 0xbc2464: r16 = "<head>\n  <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n  <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n  <link href=\"https://fonts.googleapis.com/css2\?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap\" rel=\"stylesheet\">\n  <style>\n    body {\n      padding: 16px;\n    }\n    .header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .category {\n      font-size: 14px;\n      font-weight: bold;\n      color: #009788;\n      text-transform: uppercase;\n      padding-right: 12px;\n    }\n\n    .date {\n      font-size: 12px;\n      color: #65676b;\n    }\n\n    .author {\n      font-size: 14px;\n      color: #65676b;\n      padding-bottom: 20px;\n    }\n\n    h1,h2,h3,h4 {\n      font-size: 18px;\n      font-weight: bold;\n      line-height: 27px;\n    }\n\n    .image {\n      border-radius: 8px;\n    }\n\n    .content {\n      font-family: Amiri;\n      font-size: 20px;\n      line-height: 30px;\n      color: #2e2e2e;\n    }\n\n    .tag span {\n      padding: 9px 10px;\n      line-height: 1;\n      margin: 0 6px 6px 0;\n      font-size: 12px;\n      background-color: #fafcfc;\n      border: 1px solid #009788;\n      border-radius: 5px;\n      color: #009788;\n      display: inline-block;\n    }\n  </style>\n</head>\n  "
    //     0xbc2464: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bb0] "<head>\n  <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n  <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n  <link href=\"https://fonts.googleapis.com/css2\?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap\" rel=\"stylesheet\">\n  <style>\n    body {\n      padding: 16px;\n    }\n    .header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .category {\n      font-size: 14px;\n      font-weight: bold;\n      color: #009788;\n      text-transform: uppercase;\n      padding-right: 12px;\n    }\n\n    .date {\n      font-size: 12px;\n      color: #65676b;\n    }\n\n    .author {\n      font-size: 14px;\n      color: #65676b;\n      padding-bottom: 20px;\n    }\n\n    h1,h2,h3,h4 {\n      font-size: 18px;\n      font-weight: bold;\n      line-height: 27px;\n    }\n\n    .image {\n      border-radius: 8px;\n    }\n\n    .content {\n      font-family: Amiri;\n      font-size: 20px;\n      line-height: 30px;\n      color: #2e2e2e;\n    }\n\n    .tag span {\n      padding: 9px 10px;\n      line-height: 1;\n      margin: 0 6px 6px 0;\n      font-size: 12px;\n      background-color: #fafcfc;\n      border: 1px solid #009788;\n      border-radius: 5px;\n      color: #009788;\n      display: inline-block;\n    }\n  </style>\n</head>\n  "
    //     0xbc2468: ldr             x16, [x16, #0xbb0]
    // 0xbc246c: StoreField: r0->field_13 = r16
    //     0xbc246c: stur            w16, [x0, #0x13]
    // 0xbc2470: r16 = "\n  <body>\n    "
    //     0xbc2470: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bb8] "\n  <body>\n    "
    //     0xbc2474: ldr             x16, [x16, #0xbb8]
    // 0xbc2478: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc2478: stur            w16, [x0, #0x17]
    // 0xbc247c: ldur            x1, [fp, #-8]
    // 0xbc2480: r0 = header()
    //     0xbc2480: bl              #0xbc2a0c  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::header
    // 0xbc2484: ldur            x1, [fp, #-0x10]
    // 0xbc2488: ArrayStore: r1[3] = r0  ; List_4
    //     0xbc2488: add             x25, x1, #0x1b
    //     0xbc248c: str             w0, [x25]
    //     0xbc2490: tbz             w0, #0, #0xbc24ac
    //     0xbc2494: ldurb           w16, [x1, #-1]
    //     0xbc2498: ldurb           w17, [x0, #-1]
    //     0xbc249c: and             x16, x17, x16, lsr #2
    //     0xbc24a0: tst             x16, HEAP, lsr #32
    //     0xbc24a4: b.eq            #0xbc24ac
    //     0xbc24a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc24ac: ldur            x0, [fp, #-0x10]
    // 0xbc24b0: r16 = "\n    "
    //     0xbc24b0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc0] "\n    "
    //     0xbc24b4: ldr             x16, [x16, #0xbc0]
    // 0xbc24b8: StoreField: r0->field_1f = r16
    //     0xbc24b8: stur            w16, [x0, #0x1f]
    // 0xbc24bc: ldur            x1, [fp, #-8]
    // 0xbc24c0: r0 = title()
    //     0xbc24c0: bl              #0xbc2994  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::title
    // 0xbc24c4: ldur            x1, [fp, #-0x10]
    // 0xbc24c8: ArrayStore: r1[5] = r0  ; List_4
    //     0xbc24c8: add             x25, x1, #0x23
    //     0xbc24cc: str             w0, [x25]
    //     0xbc24d0: tbz             w0, #0, #0xbc24ec
    //     0xbc24d4: ldurb           w16, [x1, #-1]
    //     0xbc24d8: ldurb           w17, [x0, #-1]
    //     0xbc24dc: and             x16, x17, x16, lsr #2
    //     0xbc24e0: tst             x16, HEAP, lsr #32
    //     0xbc24e4: b.eq            #0xbc24ec
    //     0xbc24e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc24ec: ldur            x0, [fp, #-0x10]
    // 0xbc24f0: r16 = "\n    "
    //     0xbc24f0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc0] "\n    "
    //     0xbc24f4: ldr             x16, [x16, #0xbc0]
    // 0xbc24f8: StoreField: r0->field_27 = r16
    //     0xbc24f8: stur            w16, [x0, #0x27]
    // 0xbc24fc: ldur            x1, [fp, #-8]
    // 0xbc2500: r0 = author()
    //     0xbc2500: bl              #0xbc28f4  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::author
    // 0xbc2504: ldur            x1, [fp, #-0x10]
    // 0xbc2508: ArrayStore: r1[7] = r0  ; List_4
    //     0xbc2508: add             x25, x1, #0x2b
    //     0xbc250c: str             w0, [x25]
    //     0xbc2510: tbz             w0, #0, #0xbc252c
    //     0xbc2514: ldurb           w16, [x1, #-1]
    //     0xbc2518: ldurb           w17, [x0, #-1]
    //     0xbc251c: and             x16, x17, x16, lsr #2
    //     0xbc2520: tst             x16, HEAP, lsr #32
    //     0xbc2524: b.eq            #0xbc252c
    //     0xbc2528: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc252c: ldur            x0, [fp, #-0x10]
    // 0xbc2530: r16 = "\n    "
    //     0xbc2530: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc0] "\n    "
    //     0xbc2534: ldr             x16, [x16, #0xbc0]
    // 0xbc2538: StoreField: r0->field_2f = r16
    //     0xbc2538: stur            w16, [x0, #0x2f]
    // 0xbc253c: ldur            x2, [fp, #-8]
    // 0xbc2540: LoadField: r1 = r2->field_b
    //     0xbc2540: ldur            w1, [x2, #0xb]
    // 0xbc2544: DecompressPointer r1
    //     0xbc2544: add             x1, x1, HEAP, lsl #32
    // 0xbc2548: tbnz            w1, #4, #0xbc2558
    // 0xbc254c: mov             x2, x0
    // 0xbc2550: r0 = ""
    //     0xbc2550: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbc2554: b               #0xbc2564
    // 0xbc2558: mov             x1, x2
    // 0xbc255c: r0 = image()
    //     0xbc255c: bl              #0xbc285c  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::image
    // 0xbc2560: ldur            x2, [fp, #-0x10]
    // 0xbc2564: mov             x1, x2
    // 0xbc2568: ArrayStore: r1[9] = r0  ; List_4
    //     0xbc2568: add             x25, x1, #0x33
    //     0xbc256c: str             w0, [x25]
    //     0xbc2570: tbz             w0, #0, #0xbc258c
    //     0xbc2574: ldurb           w16, [x1, #-1]
    //     0xbc2578: ldurb           w17, [x0, #-1]
    //     0xbc257c: and             x16, x17, x16, lsr #2
    //     0xbc2580: tst             x16, HEAP, lsr #32
    //     0xbc2584: b.eq            #0xbc258c
    //     0xbc2588: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc258c: r16 = "\n    "
    //     0xbc258c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc0] "\n    "
    //     0xbc2590: ldr             x16, [x16, #0xbc0]
    // 0xbc2594: StoreField: r2->field_37 = r16
    //     0xbc2594: stur            w16, [x2, #0x37]
    // 0xbc2598: ldur            x1, [fp, #-8]
    // 0xbc259c: r0 = content()
    //     0xbc259c: bl              #0xbc2794  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::content
    // 0xbc25a0: ldur            x1, [fp, #-0x10]
    // 0xbc25a4: ArrayStore: r1[11] = r0  ; List_4
    //     0xbc25a4: add             x25, x1, #0x3b
    //     0xbc25a8: str             w0, [x25]
    //     0xbc25ac: tbz             w0, #0, #0xbc25c8
    //     0xbc25b0: ldurb           w16, [x1, #-1]
    //     0xbc25b4: ldurb           w17, [x0, #-1]
    //     0xbc25b8: and             x16, x17, x16, lsr #2
    //     0xbc25bc: tst             x16, HEAP, lsr #32
    //     0xbc25c0: b.eq            #0xbc25c8
    //     0xbc25c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc25c8: ldur            x0, [fp, #-0x10]
    // 0xbc25cc: r16 = "\n    "
    //     0xbc25cc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc0] "\n    "
    //     0xbc25d0: ldr             x16, [x16, #0xbc0]
    // 0xbc25d4: StoreField: r0->field_3f = r16
    //     0xbc25d4: stur            w16, [x0, #0x3f]
    // 0xbc25d8: ldur            x1, [fp, #-8]
    // 0xbc25dc: r0 = tags()
    //     0xbc25dc: bl              #0xbc2634  ; [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::tags
    // 0xbc25e0: ldur            x1, [fp, #-0x10]
    // 0xbc25e4: ArrayStore: r1[13] = r0  ; List_4
    //     0xbc25e4: add             x25, x1, #0x43
    //     0xbc25e8: str             w0, [x25]
    //     0xbc25ec: tbz             w0, #0, #0xbc2608
    //     0xbc25f0: ldurb           w16, [x1, #-1]
    //     0xbc25f4: ldurb           w17, [x0, #-1]
    //     0xbc25f8: and             x16, x17, x16, lsr #2
    //     0xbc25fc: tst             x16, HEAP, lsr #32
    //     0xbc2600: b.eq            #0xbc2608
    //     0xbc2604: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc2608: ldur            x0, [fp, #-0x10]
    // 0xbc260c: r16 = "\n  </body>\n</html>\n  "
    //     0xbc260c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bc8] "\n  </body>\n</html>\n  "
    //     0xbc2610: ldr             x16, [x16, #0xbc8]
    // 0xbc2614: StoreField: r0->field_47 = r16
    //     0xbc2614: stur            w16, [x0, #0x47]
    // 0xbc2618: str             x0, [SP]
    // 0xbc261c: r0 = _interpolate()
    //     0xbc261c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2620: LeaveFrame
    //     0xbc2620: mov             SP, fp
    //     0xbc2624: ldp             fp, lr, [SP], #0x10
    // 0xbc2628: ret
    //     0xbc2628: ret             
    // 0xbc262c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc262c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2630: b               #0xbc2448
  }
  get _ tags(/* No info */) {
    // ** addr: 0xbc2634, size: 0xf8
    // 0xbc2634: EnterFrame
    //     0xbc2634: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2638: mov             fp, SP
    // 0xbc263c: AllocStack(0x28)
    //     0xbc263c: sub             SP, SP, #0x28
    // 0xbc2640: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc2640: mov             x0, x1
    //     0xbc2644: stur            x1, [fp, #-8]
    // 0xbc2648: CheckStackOverflow
    //     0xbc2648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc264c: cmp             SP, x16
    //     0xbc2650: b.ls            #0xbc2724
    // 0xbc2654: r1 = Null
    //     0xbc2654: mov             x1, NULL
    // 0xbc2658: r2 = 6
    //     0xbc2658: movz            x2, #0x6
    // 0xbc265c: r0 = AllocateArray()
    //     0xbc265c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2660: stur            x0, [fp, #-0x10]
    // 0xbc2664: r16 = "<div class=\"tag\">\n  "
    //     0xbc2664: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bd0] "<div class=\"tag\">\n  "
    //     0xbc2668: ldr             x16, [x16, #0xbd0]
    // 0xbc266c: StoreField: r0->field_f = r16
    //     0xbc266c: stur            w16, [x0, #0xf]
    // 0xbc2670: ldur            x1, [fp, #-8]
    // 0xbc2674: LoadField: r2 = r1->field_7
    //     0xbc2674: ldur            w2, [x1, #7]
    // 0xbc2678: DecompressPointer r2
    //     0xbc2678: add             x2, x2, HEAP, lsl #32
    // 0xbc267c: LoadField: r3 = r2->field_27
    //     0xbc267c: ldur            w3, [x2, #0x27]
    // 0xbc2680: DecompressPointer r3
    //     0xbc2680: add             x3, x3, HEAP, lsl #32
    // 0xbc2684: stur            x3, [fp, #-8]
    // 0xbc2688: r1 = Function '<anonymous closure>':.
    //     0xbc2688: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bd8] AnonymousClosure: (0xbc272c), in [package:nuonline/app/modules/article/widgets/article_html_template.dart] ArticleHtmlTemplate::tags (0xbc2634)
    //     0xbc268c: ldr             x1, [x1, #0xbd8]
    // 0xbc2690: r2 = Null
    //     0xbc2690: mov             x2, NULL
    // 0xbc2694: r0 = AllocateClosure()
    //     0xbc2694: bl              #0xec1630  ; AllocateClosureStub
    // 0xbc2698: mov             x1, x0
    // 0xbc269c: ldur            x0, [fp, #-8]
    // 0xbc26a0: r2 = LoadClassIdInstr(r0)
    //     0xbc26a0: ldur            x2, [x0, #-1]
    //     0xbc26a4: ubfx            x2, x2, #0xc, #0x14
    // 0xbc26a8: r16 = <String>
    //     0xbc26a8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbc26ac: stp             x0, x16, [SP, #8]
    // 0xbc26b0: str             x1, [SP]
    // 0xbc26b4: mov             x0, x2
    // 0xbc26b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbc26b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbc26bc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xbc26bc: movz            x17, #0xf28c
    //     0xbc26c0: add             lr, x0, x17
    //     0xbc26c4: ldr             lr, [x21, lr, lsl #3]
    //     0xbc26c8: blr             lr
    // 0xbc26cc: mov             x1, x0
    // 0xbc26d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbc26d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbc26d4: r0 = join()
    //     0xbc26d4: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xbc26d8: ldur            x1, [fp, #-0x10]
    // 0xbc26dc: ArrayStore: r1[1] = r0  ; List_4
    //     0xbc26dc: add             x25, x1, #0x13
    //     0xbc26e0: str             w0, [x25]
    //     0xbc26e4: tbz             w0, #0, #0xbc2700
    //     0xbc26e8: ldurb           w16, [x1, #-1]
    //     0xbc26ec: ldurb           w17, [x0, #-1]
    //     0xbc26f0: and             x16, x17, x16, lsr #2
    //     0xbc26f4: tst             x16, HEAP, lsr #32
    //     0xbc26f8: b.eq            #0xbc2700
    //     0xbc26fc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc2700: ldur            x0, [fp, #-0x10]
    // 0xbc2704: r16 = "\n</div>\n  "
    //     0xbc2704: add             x16, PP, #0x40, lsl #12  ; [pp+0x40be0] "\n</div>\n  "
    //     0xbc2708: ldr             x16, [x16, #0xbe0]
    // 0xbc270c: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc270c: stur            w16, [x0, #0x17]
    // 0xbc2710: str             x0, [SP]
    // 0xbc2714: r0 = _interpolate()
    //     0xbc2714: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2718: LeaveFrame
    //     0xbc2718: mov             SP, fp
    //     0xbc271c: ldp             fp, lr, [SP], #0x10
    // 0xbc2720: ret
    //     0xbc2720: ret             
    // 0xbc2724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2724: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2728: b               #0xbc2654
  }
  [closure] String <anonymous closure>(dynamic, Tag) {
    // ** addr: 0xbc272c, size: 0x68
    // 0xbc272c: EnterFrame
    //     0xbc272c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2730: mov             fp, SP
    // 0xbc2734: AllocStack(0x8)
    //     0xbc2734: sub             SP, SP, #8
    // 0xbc2738: CheckStackOverflow
    //     0xbc2738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc273c: cmp             SP, x16
    //     0xbc2740: b.ls            #0xbc278c
    // 0xbc2744: r1 = Null
    //     0xbc2744: mov             x1, NULL
    // 0xbc2748: r2 = 6
    //     0xbc2748: movz            x2, #0x6
    // 0xbc274c: r0 = AllocateArray()
    //     0xbc274c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2750: r16 = "<span>"
    //     0xbc2750: add             x16, PP, #0x40, lsl #12  ; [pp+0x40be8] "<span>"
    //     0xbc2754: ldr             x16, [x16, #0xbe8]
    // 0xbc2758: StoreField: r0->field_f = r16
    //     0xbc2758: stur            w16, [x0, #0xf]
    // 0xbc275c: ldr             x1, [fp, #0x10]
    // 0xbc2760: LoadField: r2 = r1->field_f
    //     0xbc2760: ldur            w2, [x1, #0xf]
    // 0xbc2764: DecompressPointer r2
    //     0xbc2764: add             x2, x2, HEAP, lsl #32
    // 0xbc2768: StoreField: r0->field_13 = r2
    //     0xbc2768: stur            w2, [x0, #0x13]
    // 0xbc276c: r16 = "</span>"
    //     0xbc276c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bf0] "</span>"
    //     0xbc2770: ldr             x16, [x16, #0xbf0]
    // 0xbc2774: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc2774: stur            w16, [x0, #0x17]
    // 0xbc2778: str             x0, [SP]
    // 0xbc277c: r0 = _interpolate()
    //     0xbc277c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2780: LeaveFrame
    //     0xbc2780: mov             SP, fp
    //     0xbc2784: ldp             fp, lr, [SP], #0x10
    // 0xbc2788: ret
    //     0xbc2788: ret             
    // 0xbc278c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc278c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2790: b               #0xbc2744
  }
  get _ content(/* No info */) {
    // ** addr: 0xbc2794, size: 0xc8
    // 0xbc2794: EnterFrame
    //     0xbc2794: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2798: mov             fp, SP
    // 0xbc279c: AllocStack(0x38)
    //     0xbc279c: sub             SP, SP, #0x38
    // 0xbc27a0: CheckStackOverflow
    //     0xbc27a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc27a4: cmp             SP, x16
    //     0xbc27a8: b.ls            #0xbc2854
    // 0xbc27ac: LoadField: r0 = r1->field_7
    //     0xbc27ac: ldur            w0, [x1, #7]
    // 0xbc27b0: DecompressPointer r0
    //     0xbc27b0: add             x0, x0, HEAP, lsl #32
    // 0xbc27b4: LoadField: r1 = r0->field_2b
    //     0xbc27b4: ldur            w1, [x0, #0x2b]
    // 0xbc27b8: DecompressPointer r1
    //     0xbc27b8: add             x1, x1, HEAP, lsl #32
    // 0xbc27bc: r2 = "</p>\r\n\r\n<p>"
    //     0xbc27bc: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f50] "</p>\r\n\r\n<p>"
    //     0xbc27c0: ldr             x2, [x2, #0xf50]
    // 0xbc27c4: r3 = "</p><span><p>"
    //     0xbc27c4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f58] "</p><span><p>"
    //     0xbc27c8: ldr             x3, [x3, #0xf58]
    // 0xbc27cc: r0 = replaceAll()
    //     0xbc27cc: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbc27d0: stur            x0, [fp, #-8]
    // 0xbc27d4: r16 = "<div aria-label=\"media object widget\".+<iframe.+src.+youtube.+</span></div>"
    //     0xbc27d4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bf8] "<div aria-label=\"media object widget\".+<iframe.+src.+youtube.+</span></div>"
    //     0xbc27d8: ldr             x16, [x16, #0xbf8]
    // 0xbc27dc: stp             x16, NULL, [SP, #0x20]
    // 0xbc27e0: r16 = true
    //     0xbc27e0: add             x16, NULL, #0x20  ; true
    // 0xbc27e4: r30 = true
    //     0xbc27e4: add             lr, NULL, #0x20  ; true
    // 0xbc27e8: stp             lr, x16, [SP, #0x10]
    // 0xbc27ec: r16 = false
    //     0xbc27ec: add             x16, NULL, #0x30  ; false
    // 0xbc27f0: r30 = true
    //     0xbc27f0: add             lr, NULL, #0x20  ; true
    // 0xbc27f4: stp             lr, x16, [SP]
    // 0xbc27f8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbc27f8: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbc27fc: r0 = _RegExp()
    //     0xbc27fc: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xbc2800: ldur            x1, [fp, #-8]
    // 0xbc2804: mov             x2, x0
    // 0xbc2808: r3 = ""
    //     0xbc2808: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbc280c: r0 = replaceAll()
    //     0xbc280c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xbc2810: r1 = Null
    //     0xbc2810: mov             x1, NULL
    // 0xbc2814: r2 = 6
    //     0xbc2814: movz            x2, #0x6
    // 0xbc2818: stur            x0, [fp, #-8]
    // 0xbc281c: r0 = AllocateArray()
    //     0xbc281c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2820: r16 = "<div class=\"content\">"
    //     0xbc2820: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c00] "<div class=\"content\">"
    //     0xbc2824: ldr             x16, [x16, #0xc00]
    // 0xbc2828: StoreField: r0->field_f = r16
    //     0xbc2828: stur            w16, [x0, #0xf]
    // 0xbc282c: ldur            x1, [fp, #-8]
    // 0xbc2830: StoreField: r0->field_13 = r1
    //     0xbc2830: stur            w1, [x0, #0x13]
    // 0xbc2834: r16 = "</div>"
    //     0xbc2834: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c08] "</div>"
    //     0xbc2838: ldr             x16, [x16, #0xc08]
    // 0xbc283c: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc283c: stur            w16, [x0, #0x17]
    // 0xbc2840: str             x0, [SP]
    // 0xbc2844: r0 = _interpolate()
    //     0xbc2844: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2848: LeaveFrame
    //     0xbc2848: mov             SP, fp
    //     0xbc284c: ldp             fp, lr, [SP], #0x10
    // 0xbc2850: ret
    //     0xbc2850: ret             
    // 0xbc2854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2854: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2858: b               #0xbc27ac
  }
  get _ image(/* No info */) {
    // ** addr: 0xbc285c, size: 0x98
    // 0xbc285c: EnterFrame
    //     0xbc285c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2860: mov             fp, SP
    // 0xbc2864: AllocStack(0x10)
    //     0xbc2864: sub             SP, SP, #0x10
    // 0xbc2868: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc2868: mov             x0, x1
    //     0xbc286c: stur            x1, [fp, #-8]
    // 0xbc2870: CheckStackOverflow
    //     0xbc2870: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2874: cmp             SP, x16
    //     0xbc2878: b.ls            #0xbc28ec
    // 0xbc287c: r1 = Null
    //     0xbc287c: mov             x1, NULL
    // 0xbc2880: r2 = 10
    //     0xbc2880: movz            x2, #0xa
    // 0xbc2884: r0 = AllocateArray()
    //     0xbc2884: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2888: r16 = "<img src=\""
    //     0xbc2888: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c10] "<img src=\""
    //     0xbc288c: ldr             x16, [x16, #0xc10]
    // 0xbc2890: StoreField: r0->field_f = r16
    //     0xbc2890: stur            w16, [x0, #0xf]
    // 0xbc2894: ldur            x1, [fp, #-8]
    // 0xbc2898: LoadField: r2 = r1->field_7
    //     0xbc2898: ldur            w2, [x1, #7]
    // 0xbc289c: DecompressPointer r2
    //     0xbc289c: add             x2, x2, HEAP, lsl #32
    // 0xbc28a0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xbc28a0: ldur            w1, [x2, #0x17]
    // 0xbc28a4: DecompressPointer r1
    //     0xbc28a4: add             x1, x1, HEAP, lsl #32
    // 0xbc28a8: LoadField: r2 = r1->field_f
    //     0xbc28a8: ldur            w2, [x1, #0xf]
    // 0xbc28ac: DecompressPointer r2
    //     0xbc28ac: add             x2, x2, HEAP, lsl #32
    // 0xbc28b0: StoreField: r0->field_13 = r2
    //     0xbc28b0: stur            w2, [x0, #0x13]
    // 0xbc28b4: r16 = "\" alt=\""
    //     0xbc28b4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c18] "\" alt=\""
    //     0xbc28b8: ldr             x16, [x16, #0xc18]
    // 0xbc28bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc28bc: stur            w16, [x0, #0x17]
    // 0xbc28c0: LoadField: r2 = r1->field_13
    //     0xbc28c0: ldur            w2, [x1, #0x13]
    // 0xbc28c4: DecompressPointer r2
    //     0xbc28c4: add             x2, x2, HEAP, lsl #32
    // 0xbc28c8: StoreField: r0->field_1b = r2
    //     0xbc28c8: stur            w2, [x0, #0x1b]
    // 0xbc28cc: r16 = "\" class=\"image\">\n  "
    //     0xbc28cc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c20] "\" class=\"image\">\n  "
    //     0xbc28d0: ldr             x16, [x16, #0xc20]
    // 0xbc28d4: StoreField: r0->field_1f = r16
    //     0xbc28d4: stur            w16, [x0, #0x1f]
    // 0xbc28d8: str             x0, [SP]
    // 0xbc28dc: r0 = _interpolate()
    //     0xbc28dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc28e0: LeaveFrame
    //     0xbc28e0: mov             SP, fp
    //     0xbc28e4: ldp             fp, lr, [SP], #0x10
    // 0xbc28e8: ret
    //     0xbc28e8: ret             
    // 0xbc28ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc28ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc28f0: b               #0xbc287c
  }
  get _ author(/* No info */) {
    // ** addr: 0xbc28f4, size: 0xa0
    // 0xbc28f4: EnterFrame
    //     0xbc28f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc28f8: mov             fp, SP
    // 0xbc28fc: AllocStack(0x10)
    //     0xbc28fc: sub             SP, SP, #0x10
    // 0xbc2900: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc2900: mov             x0, x1
    //     0xbc2904: stur            x1, [fp, #-8]
    // 0xbc2908: CheckStackOverflow
    //     0xbc2908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc290c: cmp             SP, x16
    //     0xbc2910: b.ls            #0xbc298c
    // 0xbc2914: r1 = Null
    //     0xbc2914: mov             x1, NULL
    // 0xbc2918: r2 = 6
    //     0xbc2918: movz            x2, #0x6
    // 0xbc291c: r0 = AllocateArray()
    //     0xbc291c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2920: r16 = "<div class=\"author\">\n  <p>"
    //     0xbc2920: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c28] "<div class=\"author\">\n  <p>"
    //     0xbc2924: ldr             x16, [x16, #0xc28]
    // 0xbc2928: StoreField: r0->field_f = r16
    //     0xbc2928: stur            w16, [x0, #0xf]
    // 0xbc292c: ldur            x1, [fp, #-8]
    // 0xbc2930: LoadField: r2 = r1->field_7
    //     0xbc2930: ldur            w2, [x1, #7]
    // 0xbc2934: DecompressPointer r2
    //     0xbc2934: add             x2, x2, HEAP, lsl #32
    // 0xbc2938: LoadField: r1 = r2->field_33
    //     0xbc2938: ldur            w1, [x2, #0x33]
    // 0xbc293c: DecompressPointer r1
    //     0xbc293c: add             x1, x1, HEAP, lsl #32
    // 0xbc2940: cmp             w1, NULL
    // 0xbc2944: b.ne            #0xbc2950
    // 0xbc2948: r1 = Null
    //     0xbc2948: mov             x1, NULL
    // 0xbc294c: b               #0xbc295c
    // 0xbc2950: LoadField: r2 = r1->field_f
    //     0xbc2950: ldur            w2, [x1, #0xf]
    // 0xbc2954: DecompressPointer r2
    //     0xbc2954: add             x2, x2, HEAP, lsl #32
    // 0xbc2958: mov             x1, x2
    // 0xbc295c: cmp             w1, NULL
    // 0xbc2960: b.ne            #0xbc2968
    // 0xbc2964: r1 = ""
    //     0xbc2964: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbc2968: StoreField: r0->field_13 = r1
    //     0xbc2968: stur            w1, [x0, #0x13]
    // 0xbc296c: r16 = "</p>\n</div>\n  "
    //     0xbc296c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c30] "</p>\n</div>\n  "
    //     0xbc2970: ldr             x16, [x16, #0xc30]
    // 0xbc2974: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc2974: stur            w16, [x0, #0x17]
    // 0xbc2978: str             x0, [SP]
    // 0xbc297c: r0 = _interpolate()
    //     0xbc297c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2980: LeaveFrame
    //     0xbc2980: mov             SP, fp
    //     0xbc2984: ldp             fp, lr, [SP], #0x10
    // 0xbc2988: ret
    //     0xbc2988: ret             
    // 0xbc298c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc298c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2990: b               #0xbc2914
  }
  get _ title(/* No info */) {
    // ** addr: 0xbc2994, size: 0x78
    // 0xbc2994: EnterFrame
    //     0xbc2994: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2998: mov             fp, SP
    // 0xbc299c: AllocStack(0x10)
    //     0xbc299c: sub             SP, SP, #0x10
    // 0xbc29a0: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc29a0: mov             x0, x1
    //     0xbc29a4: stur            x1, [fp, #-8]
    // 0xbc29a8: CheckStackOverflow
    //     0xbc29a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc29ac: cmp             SP, x16
    //     0xbc29b0: b.ls            #0xbc2a04
    // 0xbc29b4: r1 = Null
    //     0xbc29b4: mov             x1, NULL
    // 0xbc29b8: r2 = 6
    //     0xbc29b8: movz            x2, #0x6
    // 0xbc29bc: r0 = AllocateArray()
    //     0xbc29bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc29c0: r16 = "<h1>"
    //     0xbc29c0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c38] "<h1>"
    //     0xbc29c4: ldr             x16, [x16, #0xc38]
    // 0xbc29c8: StoreField: r0->field_f = r16
    //     0xbc29c8: stur            w16, [x0, #0xf]
    // 0xbc29cc: ldur            x1, [fp, #-8]
    // 0xbc29d0: LoadField: r2 = r1->field_7
    //     0xbc29d0: ldur            w2, [x1, #7]
    // 0xbc29d4: DecompressPointer r2
    //     0xbc29d4: add             x2, x2, HEAP, lsl #32
    // 0xbc29d8: LoadField: r1 = r2->field_f
    //     0xbc29d8: ldur            w1, [x2, #0xf]
    // 0xbc29dc: DecompressPointer r1
    //     0xbc29dc: add             x1, x1, HEAP, lsl #32
    // 0xbc29e0: StoreField: r0->field_13 = r1
    //     0xbc29e0: stur            w1, [x0, #0x13]
    // 0xbc29e4: r16 = "</h1>\n  "
    //     0xbc29e4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c40] "</h1>\n  "
    //     0xbc29e8: ldr             x16, [x16, #0xc40]
    // 0xbc29ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc29ec: stur            w16, [x0, #0x17]
    // 0xbc29f0: str             x0, [SP]
    // 0xbc29f4: r0 = _interpolate()
    //     0xbc29f4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc29f8: LeaveFrame
    //     0xbc29f8: mov             SP, fp
    //     0xbc29fc: ldp             fp, lr, [SP], #0x10
    // 0xbc2a00: ret
    //     0xbc2a00: ret             
    // 0xbc2a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2a04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2a08: b               #0xbc29b4
  }
  get _ header(/* No info */) {
    // ** addr: 0xbc2a0c, size: 0xdc
    // 0xbc2a0c: EnterFrame
    //     0xbc2a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2a10: mov             fp, SP
    // 0xbc2a14: AllocStack(0x18)
    //     0xbc2a14: sub             SP, SP, #0x18
    // 0xbc2a18: SetupParameters(ArticleHtmlTemplate this /* r1 => r0, fp-0x8 */)
    //     0xbc2a18: mov             x0, x1
    //     0xbc2a1c: stur            x1, [fp, #-8]
    // 0xbc2a20: CheckStackOverflow
    //     0xbc2a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2a24: cmp             SP, x16
    //     0xbc2a28: b.ls            #0xbc2ae0
    // 0xbc2a2c: r1 = Null
    //     0xbc2a2c: mov             x1, NULL
    // 0xbc2a30: r2 = 14
    //     0xbc2a30: movz            x2, #0xe
    // 0xbc2a34: r0 = AllocateArray()
    //     0xbc2a34: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbc2a38: stur            x0, [fp, #-0x10]
    // 0xbc2a3c: r16 = "<div class=\"header\">\n  "
    //     0xbc2a3c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c48] "<div class=\"header\">\n  "
    //     0xbc2a40: ldr             x16, [x16, #0xc48]
    // 0xbc2a44: StoreField: r0->field_f = r16
    //     0xbc2a44: stur            w16, [x0, #0xf]
    // 0xbc2a48: r16 = "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"126\" height=\"30\" viewBox=\"0 0 126 30\">\n  <g fill=\"none\" fill-rule=\"evenodd\">\n    <g>\n      <g>\n        <g>\n          <g>\n            <path fill=\"#02978A\" d=\"M85.194 0L82.282 2.913 85.194 5.825 88.107 2.913z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n            <path fill=\"#323131\"\n              d=\"M117.26 6.767c-4.828 0-8.738 3.904-8.738 8.712 0 4.809 3.91 8.713 8.738 8.713 3.512 0 6.543-2.082 7.929-5.082h-4.733c-.85.753-1.975 1.219-3.196 1.219-2.085 0-3.854-1.315-4.554-3.15h13.1c.11-.549.165-1.124.165-1.7 0-4.808-3.896-8.712-8.71-8.712zm-69.809 0c-4.814 0-8.724 3.904-8.724 8.712 0 4.809 3.91 8.713 8.724 8.713 4.829 0 8.725-3.904 8.725-8.713 0-4.808-3.896-8.712-8.725-8.712zm0 3.877c-2.675 0-4.842 2.164-4.842 4.835 0 2.672 2.167 4.85 4.842 4.85s4.856-2.178 4.856-4.85c0-2.67-2.18-4.835-4.856-4.835zm19.397-3.877c4.815 0 8.724 3.904 8.724 8.712v8.713H71.69v-8.713c0-2.67-2.167-4.835-4.842-4.835s-4.856 2.164-4.856 4.835C61.992 23.507 55.49 30 47.452 30c-5.117 0-9.617-2.644-12.21-6.644 1.057-.959 1.921-2.123 2.525-3.438 1.673 3.671 5.39 6.219 9.684 6.219 5.899 0 10.673-4.767 10.673-10.658 0-4.808 3.895-8.712 8.724-8.712zM81.388 0h-3.881v24.192h3.882V0zm5.817 6.767h-3.882v17.425h3.882V6.767zm10.659 0c4.814 0 8.724 3.904 8.724 8.712v8.713h-3.868v-8.713c0-2.67-2.182-4.835-4.856-4.835-2.675 0-4.843 2.164-4.843 4.835v8.713H89.14v-8.713c0-4.808 3.91-8.712 8.725-8.712zm14.842 7.028c.7-1.85 2.47-3.151 4.554-3.151 2.072 0 3.841 1.301 4.54 3.15h-9.094z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n            <path fill=\"#02978A\"\n              d=\"M8.736 6.699c4.813 0 8.708 3.916 8.708 8.738v8.738h-3.868v-8.738c0-2.68-2.166-4.85-4.84-4.85-2.688 0-4.855 2.17-4.855 4.85v8.738H0v-8.738c0-4.822 3.908-8.738 8.736-8.738zm24.218 8.738C32.954 7.4 39.468.874 47.49.874c5.129 0 9.627 2.651 12.219 6.663-1.056.962-1.92 2.13-2.524 3.449-1.686-3.669-5.389-6.224-9.695-6.224-5.883 0-10.655 4.781-10.655 10.675 0 4.822-3.909 8.738-8.722 8.738-4.814 0-8.722-3.916-8.722-8.738V6.699h3.88v8.738c0 2.679 2.168 4.863 4.842 4.863 2.674 0 4.84-2.184 4.84-4.863z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n          </g>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n  "
    //     0xbc2a48: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c50] "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"126\" height=\"30\" viewBox=\"0 0 126 30\">\n  <g fill=\"none\" fill-rule=\"evenodd\">\n    <g>\n      <g>\n        <g>\n          <g>\n            <path fill=\"#02978A\" d=\"M85.194 0L82.282 2.913 85.194 5.825 88.107 2.913z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n            <path fill=\"#323131\"\n              d=\"M117.26 6.767c-4.828 0-8.738 3.904-8.738 8.712 0 4.809 3.91 8.713 8.738 8.713 3.512 0 6.543-2.082 7.929-5.082h-4.733c-.85.753-1.975 1.219-3.196 1.219-2.085 0-3.854-1.315-4.554-3.15h13.1c.11-.549.165-1.124.165-1.7 0-4.808-3.896-8.712-8.71-8.712zm-69.809 0c-4.814 0-8.724 3.904-8.724 8.712 0 4.809 3.91 8.713 8.724 8.713 4.829 0 8.725-3.904 8.725-8.713 0-4.808-3.896-8.712-8.725-8.712zm0 3.877c-2.675 0-4.842 2.164-4.842 4.835 0 2.672 2.167 4.85 4.842 4.85s4.856-2.178 4.856-4.85c0-2.67-2.18-4.835-4.856-4.835zm19.397-3.877c4.815 0 8.724 3.904 8.724 8.712v8.713H71.69v-8.713c0-2.67-2.167-4.835-4.842-4.835s-4.856 2.164-4.856 4.835C61.992 23.507 55.49 30 47.452 30c-5.117 0-9.617-2.644-12.21-6.644 1.057-.959 1.921-2.123 2.525-3.438 1.673 3.671 5.39 6.219 9.684 6.219 5.899 0 10.673-4.767 10.673-10.658 0-4.808 3.895-8.712 8.724-8.712zM81.388 0h-3.881v24.192h3.882V0zm5.817 6.767h-3.882v17.425h3.882V6.767zm10.659 0c4.814 0 8.724 3.904 8.724 8.712v8.713h-3.868v-8.713c0-2.67-2.182-4.835-4.856-4.835-2.675 0-4.843 2.164-4.843 4.835v8.713H89.14v-8.713c0-4.808 3.91-8.712 8.725-8.712zm14.842 7.028c.7-1.85 2.47-3.151 4.554-3.151 2.072 0 3.841 1.301 4.54 3.15h-9.094z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n            <path fill=\"#02978A\"\n              d=\"M8.736 6.699c4.813 0 8.708 3.916 8.708 8.738v8.738h-3.868v-8.738c0-2.68-2.166-4.85-4.84-4.85-2.688 0-4.855 2.17-4.855 4.85v8.738H0v-8.738c0-4.822 3.908-8.738 8.736-8.738zm24.218 8.738C32.954 7.4 39.468.874 47.49.874c5.129 0 9.627 2.651 12.219 6.663-1.056.962-1.92 2.13-2.524 3.449-1.686-3.669-5.389-6.224-9.695-6.224-5.883 0-10.655 4.781-10.655 10.675 0 4.822-3.909 8.738-8.722 8.738-4.814 0-8.722-3.916-8.722-8.738V6.699h3.88v8.738c0 2.679 2.168 4.863 4.842 4.863 2.674 0 4.84-2.184 4.84-4.863z\"\n              transform=\"translate(-144 -138) translate(106 138) translate(38)\" />\n          </g>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n  "
    //     0xbc2a4c: ldr             x16, [x16, #0xc50]
    // 0xbc2a50: StoreField: r0->field_13 = r16
    //     0xbc2a50: stur            w16, [x0, #0x13]
    // 0xbc2a54: r16 = "\n  <p>\n    <span class=\"category\">"
    //     0xbc2a54: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c58] "\n  <p>\n    <span class=\"category\">"
    //     0xbc2a58: ldr             x16, [x16, #0xc58]
    // 0xbc2a5c: ArrayStore: r0[0] = r16  ; List_4
    //     0xbc2a5c: stur            w16, [x0, #0x17]
    // 0xbc2a60: ldur            x1, [fp, #-8]
    // 0xbc2a64: LoadField: r2 = r1->field_7
    //     0xbc2a64: ldur            w2, [x1, #7]
    // 0xbc2a68: DecompressPointer r2
    //     0xbc2a68: add             x2, x2, HEAP, lsl #32
    // 0xbc2a6c: LoadField: r1 = r2->field_1b
    //     0xbc2a6c: ldur            w1, [x2, #0x1b]
    // 0xbc2a70: DecompressPointer r1
    //     0xbc2a70: add             x1, x1, HEAP, lsl #32
    // 0xbc2a74: LoadField: r3 = r1->field_1b
    //     0xbc2a74: ldur            w3, [x1, #0x1b]
    // 0xbc2a78: DecompressPointer r3
    //     0xbc2a78: add             x3, x3, HEAP, lsl #32
    // 0xbc2a7c: StoreField: r0->field_1b = r3
    //     0xbc2a7c: stur            w3, [x0, #0x1b]
    // 0xbc2a80: r16 = "</span>\n    <span class=\"date\">"
    //     0xbc2a80: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c60] "</span>\n    <span class=\"date\">"
    //     0xbc2a84: ldr             x16, [x16, #0xc60]
    // 0xbc2a88: StoreField: r0->field_1f = r16
    //     0xbc2a88: stur            w16, [x0, #0x1f]
    // 0xbc2a8c: mov             x1, x2
    // 0xbc2a90: r0 = publishedAt()
    //     0xbc2a90: bl              #0xa35938  ; [package:nuonline/app/data/models/article.dart] ArticleDetail::publishedAt
    // 0xbc2a94: ldur            x1, [fp, #-0x10]
    // 0xbc2a98: ArrayStore: r1[5] = r0  ; List_4
    //     0xbc2a98: add             x25, x1, #0x23
    //     0xbc2a9c: str             w0, [x25]
    //     0xbc2aa0: tbz             w0, #0, #0xbc2abc
    //     0xbc2aa4: ldurb           w16, [x1, #-1]
    //     0xbc2aa8: ldurb           w17, [x0, #-1]
    //     0xbc2aac: and             x16, x17, x16, lsr #2
    //     0xbc2ab0: tst             x16, HEAP, lsr #32
    //     0xbc2ab4: b.eq            #0xbc2abc
    //     0xbc2ab8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbc2abc: ldur            x0, [fp, #-0x10]
    // 0xbc2ac0: r16 = "</span>\n  </p>\n</div>\n  "
    //     0xbc2ac0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40c68] "</span>\n  </p>\n</div>\n  "
    //     0xbc2ac4: ldr             x16, [x16, #0xc68]
    // 0xbc2ac8: StoreField: r0->field_27 = r16
    //     0xbc2ac8: stur            w16, [x0, #0x27]
    // 0xbc2acc: str             x0, [SP]
    // 0xbc2ad0: r0 = _interpolate()
    //     0xbc2ad0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbc2ad4: LeaveFrame
    //     0xbc2ad4: mov             SP, fp
    //     0xbc2ad8: ldp             fp, lr, [SP], #0x10
    // 0xbc2adc: ret
    //     0xbc2adc: ret             
    // 0xbc2ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2ae0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2ae4: b               #0xbc2a2c
  }
}
