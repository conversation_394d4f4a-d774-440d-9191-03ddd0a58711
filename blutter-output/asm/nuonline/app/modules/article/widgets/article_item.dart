// lib: , url: package:nuonline/app/modules/article/widgets/article_item.dart

// class id: 1050154, size: 0x8
class :: {
}

// class id: 5062, size: 0x18, field offset: 0xc
//   const constructor, 
class ArticleItem extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb85a8c, size: 0x18c
    // 0xb85a8c: EnterFrame
    //     0xb85a8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb85a90: mov             fp, SP
    // 0xb85a94: AllocStack(0x40)
    //     0xb85a94: sub             SP, SP, #0x40
    // 0xb85a98: SetupParameters(ArticleItem this /* r1 => r1, fp-0x8 */)
    //     0xb85a98: stur            x1, [fp, #-8]
    // 0xb85a9c: CheckStackOverflow
    //     0xb85a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85aa0: cmp             SP, x16
    //     0xb85aa4: b.ls            #0xb85c10
    // 0xb85aa8: r1 = 1
    //     0xb85aa8: movz            x1, #0x1
    // 0xb85aac: r0 = AllocateContext()
    //     0xb85aac: bl              #0xec126c  ; AllocateContextStub
    // 0xb85ab0: mov             x3, x0
    // 0xb85ab4: ldur            x2, [fp, #-8]
    // 0xb85ab8: stur            x3, [fp, #-0x20]
    // 0xb85abc: StoreField: r3->field_f = r2
    //     0xb85abc: stur            w2, [x3, #0xf]
    // 0xb85ac0: LoadField: r4 = r2->field_b
    //     0xb85ac0: ldur            w4, [x2, #0xb]
    // 0xb85ac4: DecompressPointer r4
    //     0xb85ac4: add             x4, x4, HEAP, lsl #32
    // 0xb85ac8: stur            x4, [fp, #-0x18]
    // 0xb85acc: LoadField: r5 = r4->field_7
    //     0xb85acc: ldur            x5, [x4, #7]
    // 0xb85ad0: r0 = BoxInt64Instr(r5)
    //     0xb85ad0: sbfiz           x0, x5, #1, #0x1f
    //     0xb85ad4: cmp             x5, x0, asr #1
    //     0xb85ad8: b.eq            #0xb85ae4
    //     0xb85adc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb85ae0: stur            x5, [x0, #7]
    // 0xb85ae4: r1 = <int>
    //     0xb85ae4: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xb85ae8: stur            x0, [fp, #-0x10]
    // 0xb85aec: r0 = ValueKey()
    //     0xb85aec: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xb85af0: mov             x2, x0
    // 0xb85af4: ldur            x0, [fp, #-0x10]
    // 0xb85af8: stur            x2, [fp, #-0x38]
    // 0xb85afc: StoreField: r2->field_b = r0
    //     0xb85afc: stur            w0, [x2, #0xb]
    // 0xb85b00: ldur            x0, [fp, #-0x18]
    // 0xb85b04: LoadField: r1 = r0->field_1b
    //     0xb85b04: ldur            w1, [x0, #0x1b]
    // 0xb85b08: DecompressPointer r1
    //     0xb85b08: add             x1, x1, HEAP, lsl #32
    // 0xb85b0c: LoadField: r3 = r1->field_1b
    //     0xb85b0c: ldur            w3, [x1, #0x1b]
    // 0xb85b10: DecompressPointer r3
    //     0xb85b10: add             x3, x3, HEAP, lsl #32
    // 0xb85b14: stur            x3, [fp, #-0x30]
    // 0xb85b18: LoadField: r4 = r0->field_f
    //     0xb85b18: ldur            w4, [x0, #0xf]
    // 0xb85b1c: DecompressPointer r4
    //     0xb85b1c: add             x4, x4, HEAP, lsl #32
    // 0xb85b20: stur            x4, [fp, #-0x28]
    // 0xb85b24: LoadField: r5 = r0->field_23
    //     0xb85b24: ldur            w5, [x0, #0x23]
    // 0xb85b28: DecompressPointer r5
    //     0xb85b28: add             x5, x5, HEAP, lsl #32
    // 0xb85b2c: ldur            x6, [fp, #-8]
    // 0xb85b30: stur            x5, [fp, #-0x10]
    // 0xb85b34: LoadField: r1 = r6->field_13
    //     0xb85b34: ldur            w1, [x6, #0x13]
    // 0xb85b38: DecompressPointer r1
    //     0xb85b38: add             x1, x1, HEAP, lsl #32
    // 0xb85b3c: tbnz            w1, #4, #0xb85b4c
    // 0xb85b40: mov             x1, x6
    // 0xb85b44: r3 = Null
    //     0xb85b44: mov             x3, NULL
    // 0xb85b48: b               #0xb85b60
    // 0xb85b4c: mov             x1, x0
    // 0xb85b50: r0 = publishedAt()
    //     0xb85b50: bl              #0xb585c0  ; [package:nuonline/app/data/models/article.dart] Article::publishedAt
    // 0xb85b54: mov             x3, x0
    // 0xb85b58: ldur            x1, [fp, #-8]
    // 0xb85b5c: ldur            x0, [fp, #-0x18]
    // 0xb85b60: stur            x3, [fp, #-0x40]
    // 0xb85b64: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb85b64: ldur            w2, [x0, #0x17]
    // 0xb85b68: DecompressPointer r2
    //     0xb85b68: add             x2, x2, HEAP, lsl #32
    // 0xb85b6c: LoadField: r0 = r2->field_7
    //     0xb85b6c: ldur            w0, [x2, #7]
    // 0xb85b70: DecompressPointer r0
    //     0xb85b70: add             x0, x0, HEAP, lsl #32
    // 0xb85b74: stur            x0, [fp, #-0x18]
    // 0xb85b78: LoadField: r2 = r1->field_f
    //     0xb85b78: ldur            w2, [x1, #0xf]
    // 0xb85b7c: DecompressPointer r2
    //     0xb85b7c: add             x2, x2, HEAP, lsl #32
    // 0xb85b80: cmp             w2, NULL
    // 0xb85b84: b.ne            #0xb85ba0
    // 0xb85b88: ldur            x2, [fp, #-0x20]
    // 0xb85b8c: r1 = Function '<anonymous closure>':.
    //     0xb85b8c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ad8] AnonymousClosure: (0xb85c18), in [package:nuonline/app/modules/article/widgets/article_item.dart] ArticleItem::build (0xb85a8c)
    //     0xb85b90: ldr             x1, [x1, #0xad8]
    // 0xb85b94: r0 = AllocateClosure()
    //     0xb85b94: bl              #0xec1630  ; AllocateClosureStub
    // 0xb85b98: mov             x6, x0
    // 0xb85b9c: b               #0xb85ba4
    // 0xb85ba0: mov             x6, x2
    // 0xb85ba4: ldur            x2, [fp, #-0x38]
    // 0xb85ba8: ldur            x3, [fp, #-0x30]
    // 0xb85bac: ldur            x4, [fp, #-0x28]
    // 0xb85bb0: ldur            x5, [fp, #-0x10]
    // 0xb85bb4: ldur            x0, [fp, #-0x40]
    // 0xb85bb8: ldur            x1, [fp, #-0x18]
    // 0xb85bbc: stur            x6, [fp, #-8]
    // 0xb85bc0: r0 = NArticleListTile()
    //     0xb85bc0: bl              #0xb07fe4  ; AllocateNArticleListTileStub -> NArticleListTile (size=0x28)
    // 0xb85bc4: ldur            x1, [fp, #-0x28]
    // 0xb85bc8: StoreField: r0->field_b = r1
    //     0xb85bc8: stur            w1, [x0, #0xb]
    // 0xb85bcc: ldur            x1, [fp, #-0x30]
    // 0xb85bd0: StoreField: r0->field_f = r1
    //     0xb85bd0: stur            w1, [x0, #0xf]
    // 0xb85bd4: ldur            x1, [fp, #-0x18]
    // 0xb85bd8: StoreField: r0->field_13 = r1
    //     0xb85bd8: stur            w1, [x0, #0x13]
    // 0xb85bdc: ldur            x1, [fp, #-0x40]
    // 0xb85be0: StoreField: r0->field_1b = r1
    //     0xb85be0: stur            w1, [x0, #0x1b]
    // 0xb85be4: ldur            x1, [fp, #-8]
    // 0xb85be8: StoreField: r0->field_1f = r1
    //     0xb85be8: stur            w1, [x0, #0x1f]
    // 0xb85bec: r1 = false
    //     0xb85bec: add             x1, NULL, #0x30  ; false
    // 0xb85bf0: StoreField: r0->field_23 = r1
    //     0xb85bf0: stur            w1, [x0, #0x23]
    // 0xb85bf4: ldur            x1, [fp, #-0x10]
    // 0xb85bf8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb85bf8: stur            w1, [x0, #0x17]
    // 0xb85bfc: ldur            x1, [fp, #-0x38]
    // 0xb85c00: StoreField: r0->field_7 = r1
    //     0xb85c00: stur            w1, [x0, #7]
    // 0xb85c04: LeaveFrame
    //     0xb85c04: mov             SP, fp
    //     0xb85c08: ldp             fp, lr, [SP], #0x10
    // 0xb85c0c: ret
    //     0xb85c0c: ret             
    // 0xb85c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85c10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85c14: b               #0xb85aa8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb85c18, size: 0xd0
    // 0xb85c18: EnterFrame
    //     0xb85c18: stp             fp, lr, [SP, #-0x10]!
    //     0xb85c1c: mov             fp, SP
    // 0xb85c20: AllocStack(0x28)
    //     0xb85c20: sub             SP, SP, #0x28
    // 0xb85c24: SetupParameters()
    //     0xb85c24: ldr             x0, [fp, #0x10]
    //     0xb85c28: ldur            w1, [x0, #0x17]
    //     0xb85c2c: add             x1, x1, HEAP, lsl #32
    //     0xb85c30: stur            x1, [fp, #-8]
    // 0xb85c34: CheckStackOverflow
    //     0xb85c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85c38: cmp             SP, x16
    //     0xb85c3c: b.ls            #0xb85ce0
    // 0xb85c40: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb85c40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb85c44: ldr             x0, [x0, #0x2670]
    //     0xb85c48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb85c4c: cmp             w0, w16
    //     0xb85c50: b.ne            #0xb85c5c
    //     0xb85c54: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb85c58: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb85c5c: r1 = Null
    //     0xb85c5c: mov             x1, NULL
    // 0xb85c60: r2 = 4
    //     0xb85c60: movz            x2, #0x4
    // 0xb85c64: r0 = AllocateArray()
    //     0xb85c64: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb85c68: mov             x2, x0
    // 0xb85c6c: r16 = "id"
    //     0xb85c6c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb85c70: ldr             x16, [x16, #0x740]
    // 0xb85c74: StoreField: r2->field_f = r16
    //     0xb85c74: stur            w16, [x2, #0xf]
    // 0xb85c78: ldur            x0, [fp, #-8]
    // 0xb85c7c: LoadField: r1 = r0->field_f
    //     0xb85c7c: ldur            w1, [x0, #0xf]
    // 0xb85c80: DecompressPointer r1
    //     0xb85c80: add             x1, x1, HEAP, lsl #32
    // 0xb85c84: LoadField: r0 = r1->field_b
    //     0xb85c84: ldur            w0, [x1, #0xb]
    // 0xb85c88: DecompressPointer r0
    //     0xb85c88: add             x0, x0, HEAP, lsl #32
    // 0xb85c8c: LoadField: r3 = r0->field_7
    //     0xb85c8c: ldur            x3, [x0, #7]
    // 0xb85c90: r0 = BoxInt64Instr(r3)
    //     0xb85c90: sbfiz           x0, x3, #1, #0x1f
    //     0xb85c94: cmp             x3, x0, asr #1
    //     0xb85c98: b.eq            #0xb85ca4
    //     0xb85c9c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb85ca0: stur            x3, [x0, #7]
    // 0xb85ca4: StoreField: r2->field_13 = r0
    //     0xb85ca4: stur            w0, [x2, #0x13]
    // 0xb85ca8: r16 = <String, int>
    //     0xb85ca8: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb85cac: stp             x2, x16, [SP]
    // 0xb85cb0: r0 = Map._fromLiteral()
    //     0xb85cb0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb85cb4: r16 = "/article/article-detail"
    //     0xb85cb4: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xb85cb8: ldr             x16, [x16, #0x3d0]
    // 0xb85cbc: stp             x16, NULL, [SP, #0x10]
    // 0xb85cc0: r16 = false
    //     0xb85cc0: add             x16, NULL, #0x30  ; false
    // 0xb85cc4: stp             x16, x0, [SP]
    // 0xb85cc8: r4 = const [0x1, 0x3, 0x3, 0x1, arguments, 0x1, preventDuplicates, 0x2, null]
    //     0xb85cc8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f48] List(9) [0x1, 0x3, 0x3, 0x1, "arguments", 0x1, "preventDuplicates", 0x2, Null]
    //     0xb85ccc: ldr             x4, [x4, #0xf48]
    // 0xb85cd0: r0 = GetNavigation.toNamed()
    //     0xb85cd0: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb85cd4: LeaveFrame
    //     0xb85cd4: mov             SP, fp
    //     0xb85cd8: ldp             fp, lr, [SP], #0x10
    // 0xb85cdc: ret
    //     0xb85cdc: ret             
    // 0xb85ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85ce0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85ce4: b               #0xb85c40
  }
}
