// lib: , url: package:nuonline/app/modules/article/widgets/related_content_widget.dart

// class id: 1050155, size: 0x8
class :: {
}

// class id: 5061, size: 0x18, field offset: 0xc
//   const constructor, 
class RelatedContentWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xb85ce8, size: 0x3f0
    // 0xb85ce8: EnterFrame
    //     0xb85ce8: stp             fp, lr, [SP, #-0x10]!
    //     0xb85cec: mov             fp, SP
    // 0xb85cf0: AllocStack(0x50)
    //     0xb85cf0: sub             SP, SP, #0x50
    // 0xb85cf4: SetupParameters(RelatedContentWidget this /* r1 => r1, fp-0x8 */)
    //     0xb85cf4: stur            x1, [fp, #-8]
    // 0xb85cf8: CheckStackOverflow
    //     0xb85cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85cfc: cmp             SP, x16
    //     0xb85d00: b.ls            #0xb860d0
    // 0xb85d04: r1 = 1
    //     0xb85d04: movz            x1, #0x1
    // 0xb85d08: r0 = AllocateContext()
    //     0xb85d08: bl              #0xec126c  ; AllocateContextStub
    // 0xb85d0c: mov             x3, x0
    // 0xb85d10: ldur            x0, [fp, #-8]
    // 0xb85d14: stur            x3, [fp, #-0x10]
    // 0xb85d18: StoreField: r3->field_f = r0
    //     0xb85d18: stur            w0, [x3, #0xf]
    // 0xb85d1c: r1 = <Widget>
    //     0xb85d1c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb85d20: r2 = 0
    //     0xb85d20: movz            x2, #0
    // 0xb85d24: r0 = _GrowableList()
    //     0xb85d24: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb85d28: mov             x3, x0
    // 0xb85d2c: ldur            x2, [fp, #-8]
    // 0xb85d30: stur            x3, [fp, #-0x20]
    // 0xb85d34: LoadField: r4 = r2->field_b
    //     0xb85d34: ldur            w4, [x2, #0xb]
    // 0xb85d38: DecompressPointer r4
    //     0xb85d38: add             x4, x4, HEAP, lsl #32
    // 0xb85d3c: stur            x4, [fp, #-0x18]
    // 0xb85d40: r0 = LoadClassIdInstr(r4)
    //     0xb85d40: ldur            x0, [x4, #-1]
    //     0xb85d44: ubfx            x0, x0, #0xc, #0x14
    // 0xb85d48: mov             x1, x4
    // 0xb85d4c: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb85d4c: movz            x17, #0xd488
    //     0xb85d50: add             lr, x0, x17
    //     0xb85d54: ldr             lr, [x21, lr, lsl #3]
    //     0xb85d58: blr             lr
    // 0xb85d5c: tbnz            w0, #4, #0xb85e5c
    // 0xb85d60: ldur            x1, [fp, #-0x18]
    // 0xb85d64: r0 = LoadClassIdInstr(r1)
    //     0xb85d64: ldur            x0, [x1, #-1]
    //     0xb85d68: ubfx            x0, x0, #0xc, #0x14
    // 0xb85d6c: str             x1, [SP]
    // 0xb85d70: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb85d70: movz            x17, #0xc834
    //     0xb85d74: add             lr, x0, x17
    //     0xb85d78: ldr             lr, [x21, lr, lsl #3]
    //     0xb85d7c: blr             lr
    // 0xb85d80: r3 = LoadInt32Instr(r0)
    //     0xb85d80: sbfx            x3, x0, #1, #0x1f
    //     0xb85d84: tbz             w0, #0, #0xb85d8c
    //     0xb85d88: ldur            x3, [x0, #7]
    // 0xb85d8c: stur            x3, [fp, #-0x28]
    // 0xb85d90: r1 = Function '<anonymous closure>':.
    //     0xb85d90: add             x1, PP, #0x36, lsl #12  ; [pp+0x36aa8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb85d94: ldr             x1, [x1, #0xaa8]
    // 0xb85d98: r2 = Null
    //     0xb85d98: mov             x2, NULL
    // 0xb85d9c: r0 = AllocateClosure()
    //     0xb85d9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb85da0: ldur            x2, [fp, #-0x10]
    // 0xb85da4: r1 = Function '<anonymous closure>':.
    //     0xb85da4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ab0] AnonymousClosure: (0xb86294), in [package:nuonline/app/modules/article/widgets/related_content_widget.dart] RelatedContentWidget::build (0xb85ce8)
    //     0xb85da8: ldr             x1, [x1, #0xab0]
    // 0xb85dac: stur            x0, [fp, #-0x30]
    // 0xb85db0: r0 = AllocateClosure()
    //     0xb85db0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb85db4: stur            x0, [fp, #-0x38]
    // 0xb85db8: r0 = ListView()
    //     0xb85db8: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb85dbc: stur            x0, [fp, #-0x40]
    // 0xb85dc0: r16 = true
    //     0xb85dc0: add             x16, NULL, #0x20  ; true
    // 0xb85dc4: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb85dc4: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xb85dc8: ldr             lr, [lr, #0x290]
    // 0xb85dcc: stp             lr, x16, [SP]
    // 0xb85dd0: mov             x1, x0
    // 0xb85dd4: ldur            x2, [fp, #-0x38]
    // 0xb85dd8: ldur            x3, [fp, #-0x28]
    // 0xb85ddc: ldur            x5, [fp, #-0x30]
    // 0xb85de0: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xb85de0: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb85de4: ldr             x4, [x4, #0x298]
    // 0xb85de8: r0 = ListView.separated()
    //     0xb85de8: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb85dec: r1 = Null
    //     0xb85dec: mov             x1, NULL
    // 0xb85df0: r2 = 10
    //     0xb85df0: movz            x2, #0xa
    // 0xb85df4: r0 = AllocateArray()
    //     0xb85df4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb85df8: stur            x0, [fp, #-0x30]
    // 0xb85dfc: r16 = Instance_SizedBox
    //     0xb85dfc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb85e00: ldr             x16, [x16, #0x448]
    // 0xb85e04: StoreField: r0->field_f = r16
    //     0xb85e04: stur            w16, [x0, #0xf]
    // 0xb85e08: r16 = Instance_NArticleHeader
    //     0xb85e08: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2ca38] Obj!NArticleHeader@e20e91
    //     0xb85e0c: ldr             x16, [x16, #0xa38]
    // 0xb85e10: StoreField: r0->field_13 = r16
    //     0xb85e10: stur            w16, [x0, #0x13]
    // 0xb85e14: r16 = Instance_SizedBox
    //     0xb85e14: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb85e18: ldr             x16, [x16, #0xfe8]
    // 0xb85e1c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb85e1c: stur            w16, [x0, #0x17]
    // 0xb85e20: r16 = Instance_Divider
    //     0xb85e20: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb85e24: ldr             x16, [x16, #0xc28]
    // 0xb85e28: StoreField: r0->field_1b = r16
    //     0xb85e28: stur            w16, [x0, #0x1b]
    // 0xb85e2c: ldur            x1, [fp, #-0x40]
    // 0xb85e30: StoreField: r0->field_1f = r1
    //     0xb85e30: stur            w1, [x0, #0x1f]
    // 0xb85e34: r1 = <Widget>
    //     0xb85e34: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb85e38: r0 = AllocateGrowableArray()
    //     0xb85e38: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb85e3c: mov             x1, x0
    // 0xb85e40: ldur            x0, [fp, #-0x30]
    // 0xb85e44: StoreField: r1->field_f = r0
    //     0xb85e44: stur            w0, [x1, #0xf]
    // 0xb85e48: r0 = 10
    //     0xb85e48: movz            x0, #0xa
    // 0xb85e4c: StoreField: r1->field_b = r0
    //     0xb85e4c: stur            w0, [x1, #0xb]
    // 0xb85e50: mov             x2, x1
    // 0xb85e54: ldur            x1, [fp, #-0x20]
    // 0xb85e58: r0 = addAll()
    //     0xb85e58: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb85e5c: ldur            x1, [fp, #-0x18]
    // 0xb85e60: r0 = LoadClassIdInstr(r1)
    //     0xb85e60: ldur            x0, [x1, #-1]
    //     0xb85e64: ubfx            x0, x0, #0xc, #0x14
    // 0xb85e68: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb85e68: movz            x17, #0xd488
    //     0xb85e6c: add             lr, x0, x17
    //     0xb85e70: ldr             lr, [x21, lr, lsl #3]
    //     0xb85e74: blr             lr
    // 0xb85e78: tbnz            w0, #4, #0xb85f0c
    // 0xb85e7c: ldur            x2, [fp, #-8]
    // 0xb85e80: LoadField: r1 = r2->field_f
    //     0xb85e80: ldur            w1, [x2, #0xf]
    // 0xb85e84: DecompressPointer r1
    //     0xb85e84: add             x1, x1, HEAP, lsl #32
    // 0xb85e88: r0 = LoadClassIdInstr(r1)
    //     0xb85e88: ldur            x0, [x1, #-1]
    //     0xb85e8c: ubfx            x0, x0, #0xc, #0x14
    // 0xb85e90: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb85e90: movz            x17, #0xd488
    //     0xb85e94: add             lr, x0, x17
    //     0xb85e98: ldr             lr, [x21, lr, lsl #3]
    //     0xb85e9c: blr             lr
    // 0xb85ea0: tbnz            w0, #4, #0xb85f04
    // 0xb85ea4: ldur            x0, [fp, #-0x20]
    // 0xb85ea8: LoadField: r1 = r0->field_b
    //     0xb85ea8: ldur            w1, [x0, #0xb]
    // 0xb85eac: LoadField: r2 = r0->field_f
    //     0xb85eac: ldur            w2, [x0, #0xf]
    // 0xb85eb0: DecompressPointer r2
    //     0xb85eb0: add             x2, x2, HEAP, lsl #32
    // 0xb85eb4: LoadField: r3 = r2->field_b
    //     0xb85eb4: ldur            w3, [x2, #0xb]
    // 0xb85eb8: r2 = LoadInt32Instr(r1)
    //     0xb85eb8: sbfx            x2, x1, #1, #0x1f
    // 0xb85ebc: stur            x2, [fp, #-0x28]
    // 0xb85ec0: r1 = LoadInt32Instr(r3)
    //     0xb85ec0: sbfx            x1, x3, #1, #0x1f
    // 0xb85ec4: cmp             x2, x1
    // 0xb85ec8: b.ne            #0xb85ed4
    // 0xb85ecc: mov             x1, x0
    // 0xb85ed0: r0 = _growToNextCapacity()
    //     0xb85ed0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb85ed4: ldur            x2, [fp, #-0x20]
    // 0xb85ed8: ldur            x0, [fp, #-0x28]
    // 0xb85edc: add             x1, x0, #1
    // 0xb85ee0: lsl             x3, x1, #1
    // 0xb85ee4: StoreField: r2->field_b = r3
    //     0xb85ee4: stur            w3, [x2, #0xb]
    // 0xb85ee8: LoadField: r1 = r2->field_f
    //     0xb85ee8: ldur            w1, [x2, #0xf]
    // 0xb85eec: DecompressPointer r1
    //     0xb85eec: add             x1, x1, HEAP, lsl #32
    // 0xb85ef0: add             x3, x1, x0, lsl #2
    // 0xb85ef4: r16 = Instance_NSectionDivider
    //     0xb85ef4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28038] Obj!NSectionDivider@e20aa1
    //     0xb85ef8: ldr             x16, [x16, #0x38]
    // 0xb85efc: StoreField: r3->field_f = r16
    //     0xb85efc: stur            w16, [x3, #0xf]
    // 0xb85f00: b               #0xb85f10
    // 0xb85f04: ldur            x2, [fp, #-0x20]
    // 0xb85f08: b               #0xb85f10
    // 0xb85f0c: ldur            x2, [fp, #-0x20]
    // 0xb85f10: ldur            x3, [fp, #-8]
    // 0xb85f14: LoadField: r4 = r3->field_f
    //     0xb85f14: ldur            w4, [x3, #0xf]
    // 0xb85f18: DecompressPointer r4
    //     0xb85f18: add             x4, x4, HEAP, lsl #32
    // 0xb85f1c: stur            x4, [fp, #-0x18]
    // 0xb85f20: r0 = LoadClassIdInstr(r4)
    //     0xb85f20: ldur            x0, [x4, #-1]
    //     0xb85f24: ubfx            x0, x0, #0xc, #0x14
    // 0xb85f28: mov             x1, x4
    // 0xb85f2c: r0 = GDT[cid_x0 + 0xd488]()
    //     0xb85f2c: movz            x17, #0xd488
    //     0xb85f30: add             lr, x0, x17
    //     0xb85f34: ldr             lr, [x21, lr, lsl #3]
    //     0xb85f38: blr             lr
    // 0xb85f3c: tbnz            w0, #4, #0xb86040
    // 0xb85f40: ldur            x0, [fp, #-0x18]
    // 0xb85f44: r1 = LoadClassIdInstr(r0)
    //     0xb85f44: ldur            x1, [x0, #-1]
    //     0xb85f48: ubfx            x1, x1, #0xc, #0x14
    // 0xb85f4c: str             x0, [SP]
    // 0xb85f50: mov             x0, x1
    // 0xb85f54: r0 = GDT[cid_x0 + 0xc834]()
    //     0xb85f54: movz            x17, #0xc834
    //     0xb85f58: add             lr, x0, x17
    //     0xb85f5c: ldr             lr, [x21, lr, lsl #3]
    //     0xb85f60: blr             lr
    // 0xb85f64: r3 = LoadInt32Instr(r0)
    //     0xb85f64: sbfx            x3, x0, #1, #0x1f
    //     0xb85f68: tbz             w0, #0, #0xb85f70
    //     0xb85f6c: ldur            x3, [x0, #7]
    // 0xb85f70: stur            x3, [fp, #-0x28]
    // 0xb85f74: r1 = Function '<anonymous closure>':.
    //     0xb85f74: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ab8] AnonymousClosure: (0xa35a2c), in [package:nuonline/app/modules/zakat/views/select_pertanian_view.dart] SelectPertanianView::build (0xb62588)
    //     0xb85f78: ldr             x1, [x1, #0xab8]
    // 0xb85f7c: r2 = Null
    //     0xb85f7c: mov             x2, NULL
    // 0xb85f80: r0 = AllocateClosure()
    //     0xb85f80: bl              #0xec1630  ; AllocateClosureStub
    // 0xb85f84: ldur            x2, [fp, #-0x10]
    // 0xb85f88: r1 = Function '<anonymous closure>':.
    //     0xb85f88: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ac0] AnonymousClosure: (0xb860d8), in [package:nuonline/app/modules/article/widgets/related_content_widget.dart] RelatedContentWidget::build (0xb85ce8)
    //     0xb85f8c: ldr             x1, [x1, #0xac0]
    // 0xb85f90: stur            x0, [fp, #-0x10]
    // 0xb85f94: r0 = AllocateClosure()
    //     0xb85f94: bl              #0xec1630  ; AllocateClosureStub
    // 0xb85f98: stur            x0, [fp, #-0x18]
    // 0xb85f9c: r0 = ListView()
    //     0xb85f9c: bl              #0xa2fa60  ; AllocateListViewStub -> ListView (size=0x64)
    // 0xb85fa0: stur            x0, [fp, #-0x30]
    // 0xb85fa4: r16 = true
    //     0xb85fa4: add             x16, NULL, #0x20  ; true
    // 0xb85fa8: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb85fa8: add             lr, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xb85fac: ldr             lr, [lr, #0x290]
    // 0xb85fb0: stp             lr, x16, [SP]
    // 0xb85fb4: mov             x1, x0
    // 0xb85fb8: ldur            x2, [fp, #-0x18]
    // 0xb85fbc: ldur            x3, [fp, #-0x28]
    // 0xb85fc0: ldur            x5, [fp, #-0x10]
    // 0xb85fc4: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xb85fc4: add             x4, PP, #0x28, lsl #12  ; [pp+0x28298] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb85fc8: ldr             x4, [x4, #0x298]
    // 0xb85fcc: r0 = ListView.separated()
    //     0xb85fcc: bl              #0xa35350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb85fd0: r1 = Null
    //     0xb85fd0: mov             x1, NULL
    // 0xb85fd4: r2 = 10
    //     0xb85fd4: movz            x2, #0xa
    // 0xb85fd8: r0 = AllocateArray()
    //     0xb85fd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb85fdc: stur            x0, [fp, #-0x10]
    // 0xb85fe0: r16 = Instance_SizedBox
    //     0xb85fe0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27448] Obj!SizedBox@e1e081
    //     0xb85fe4: ldr             x16, [x16, #0x448]
    // 0xb85fe8: StoreField: r0->field_f = r16
    //     0xb85fe8: stur            w16, [x0, #0xf]
    // 0xb85fec: r16 = Instance_NArticleHeader
    //     0xb85fec: add             x16, PP, #0x35, lsl #12  ; [pp+0x35cd8] Obj!NArticleHeader@e20f51
    //     0xb85ff0: ldr             x16, [x16, #0xcd8]
    // 0xb85ff4: StoreField: r0->field_13 = r16
    //     0xb85ff4: stur            w16, [x0, #0x13]
    // 0xb85ff8: r16 = Instance_SizedBox
    //     0xb85ff8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26fe8] Obj!SizedBox@e1dfc1
    //     0xb85ffc: ldr             x16, [x16, #0xfe8]
    // 0xb86000: ArrayStore: r0[0] = r16  ; List_4
    //     0xb86000: stur            w16, [x0, #0x17]
    // 0xb86004: r16 = Instance_Divider
    //     0xb86004: add             x16, PP, #0x27, lsl #12  ; [pp+0x27c28] Obj!Divider@e25721
    //     0xb86008: ldr             x16, [x16, #0xc28]
    // 0xb8600c: StoreField: r0->field_1b = r16
    //     0xb8600c: stur            w16, [x0, #0x1b]
    // 0xb86010: ldur            x1, [fp, #-0x30]
    // 0xb86014: StoreField: r0->field_1f = r1
    //     0xb86014: stur            w1, [x0, #0x1f]
    // 0xb86018: r1 = <Widget>
    //     0xb86018: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xb8601c: r0 = AllocateGrowableArray()
    //     0xb8601c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb86020: mov             x1, x0
    // 0xb86024: ldur            x0, [fp, #-0x10]
    // 0xb86028: StoreField: r1->field_f = r0
    //     0xb86028: stur            w0, [x1, #0xf]
    // 0xb8602c: r0 = 10
    //     0xb8602c: movz            x0, #0xa
    // 0xb86030: StoreField: r1->field_b = r0
    //     0xb86030: stur            w0, [x1, #0xb]
    // 0xb86034: mov             x2, x1
    // 0xb86038: ldur            x1, [fp, #-0x20]
    // 0xb8603c: r0 = addAll()
    //     0xb8603c: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xb86040: ldur            x1, [fp, #-8]
    // 0xb86044: ldur            x0, [fp, #-0x20]
    // 0xb86048: LoadField: r2 = r1->field_13
    //     0xb86048: ldur            w2, [x1, #0x13]
    // 0xb8604c: DecompressPointer r2
    //     0xb8604c: add             x2, x2, HEAP, lsl #32
    // 0xb86050: stur            x2, [fp, #-0x10]
    // 0xb86054: r0 = Column()
    //     0xb86054: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb86058: mov             x1, x0
    // 0xb8605c: r0 = Instance_Axis
    //     0xb8605c: ldr             x0, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xb86060: stur            x1, [fp, #-8]
    // 0xb86064: StoreField: r1->field_f = r0
    //     0xb86064: stur            w0, [x1, #0xf]
    // 0xb86068: r0 = Instance_MainAxisAlignment
    //     0xb86068: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xb8606c: ldr             x0, [x0, #0x730]
    // 0xb86070: StoreField: r1->field_13 = r0
    //     0xb86070: stur            w0, [x1, #0x13]
    // 0xb86074: r0 = Instance_MainAxisSize
    //     0xb86074: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xb86078: ldr             x0, [x0, #0x738]
    // 0xb8607c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8607c: stur            w0, [x1, #0x17]
    // 0xb86080: r0 = Instance_CrossAxisAlignment
    //     0xb86080: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xb86084: ldr             x0, [x0, #0x740]
    // 0xb86088: StoreField: r1->field_1b = r0
    //     0xb86088: stur            w0, [x1, #0x1b]
    // 0xb8608c: r0 = Instance_VerticalDirection
    //     0xb8608c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xb86090: ldr             x0, [x0, #0x748]
    // 0xb86094: StoreField: r1->field_23 = r0
    //     0xb86094: stur            w0, [x1, #0x23]
    // 0xb86098: r0 = Instance_Clip
    //     0xb86098: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xb8609c: ldr             x0, [x0, #0x750]
    // 0xb860a0: StoreField: r1->field_2b = r0
    //     0xb860a0: stur            w0, [x1, #0x2b]
    // 0xb860a4: StoreField: r1->field_2f = rZR
    //     0xb860a4: stur            xzr, [x1, #0x2f]
    // 0xb860a8: ldur            x0, [fp, #-0x20]
    // 0xb860ac: StoreField: r1->field_b = r0
    //     0xb860ac: stur            w0, [x1, #0xb]
    // 0xb860b0: r0 = Padding()
    //     0xb860b0: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb860b4: ldur            x1, [fp, #-0x10]
    // 0xb860b8: StoreField: r0->field_f = r1
    //     0xb860b8: stur            w1, [x0, #0xf]
    // 0xb860bc: ldur            x1, [fp, #-8]
    // 0xb860c0: StoreField: r0->field_b = r1
    //     0xb860c0: stur            w1, [x0, #0xb]
    // 0xb860c4: LeaveFrame
    //     0xb860c4: mov             SP, fp
    //     0xb860c8: ldp             fp, lr, [SP], #0x10
    // 0xb860cc: ret
    //     0xb860cc: ret             
    // 0xb860d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb860d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb860d4: b               #0xb85d04
  }
  [closure] NVideoListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb860d8, size: 0x108
    // 0xb860d8: EnterFrame
    //     0xb860d8: stp             fp, lr, [SP, #-0x10]!
    //     0xb860dc: mov             fp, SP
    // 0xb860e0: AllocStack(0x30)
    //     0xb860e0: sub             SP, SP, #0x30
    // 0xb860e4: SetupParameters()
    //     0xb860e4: ldr             x0, [fp, #0x20]
    //     0xb860e8: ldur            w1, [x0, #0x17]
    //     0xb860ec: add             x1, x1, HEAP, lsl #32
    //     0xb860f0: stur            x1, [fp, #-8]
    // 0xb860f4: CheckStackOverflow
    //     0xb860f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb860f8: cmp             SP, x16
    //     0xb860fc: b.ls            #0xb861d8
    // 0xb86100: r1 = 1
    //     0xb86100: movz            x1, #0x1
    // 0xb86104: r0 = AllocateContext()
    //     0xb86104: bl              #0xec126c  ; AllocateContextStub
    // 0xb86108: mov             x1, x0
    // 0xb8610c: ldur            x0, [fp, #-8]
    // 0xb86110: stur            x1, [fp, #-0x10]
    // 0xb86114: StoreField: r1->field_b = r0
    //     0xb86114: stur            w0, [x1, #0xb]
    // 0xb86118: LoadField: r2 = r0->field_f
    //     0xb86118: ldur            w2, [x0, #0xf]
    // 0xb8611c: DecompressPointer r2
    //     0xb8611c: add             x2, x2, HEAP, lsl #32
    // 0xb86120: LoadField: r0 = r2->field_f
    //     0xb86120: ldur            w0, [x2, #0xf]
    // 0xb86124: DecompressPointer r0
    //     0xb86124: add             x0, x0, HEAP, lsl #32
    // 0xb86128: r2 = LoadClassIdInstr(r0)
    //     0xb86128: ldur            x2, [x0, #-1]
    //     0xb8612c: ubfx            x2, x2, #0xc, #0x14
    // 0xb86130: ldr             x16, [fp, #0x10]
    // 0xb86134: stp             x16, x0, [SP]
    // 0xb86138: mov             x0, x2
    // 0xb8613c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb8613c: movz            x17, #0x3037
    //     0xb86140: movk            x17, #0x1, lsl #16
    //     0xb86144: add             lr, x0, x17
    //     0xb86148: ldr             lr, [x21, lr, lsl #3]
    //     0xb8614c: blr             lr
    // 0xb86150: mov             x1, x0
    // 0xb86154: ldur            x2, [fp, #-0x10]
    // 0xb86158: StoreField: r2->field_f = r0
    //     0xb86158: stur            w0, [x2, #0xf]
    //     0xb8615c: ldurb           w16, [x2, #-1]
    //     0xb86160: ldurb           w17, [x0, #-1]
    //     0xb86164: and             x16, x17, x16, lsr #2
    //     0xb86168: tst             x16, HEAP, lsr #32
    //     0xb8616c: b.eq            #0xb86174
    //     0xb86170: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb86174: LoadField: r0 = r1->field_f
    //     0xb86174: ldur            w0, [x1, #0xf]
    // 0xb86178: DecompressPointer r0
    //     0xb86178: add             x0, x0, HEAP, lsl #32
    // 0xb8617c: stur            x0, [fp, #-0x18]
    // 0xb86180: LoadField: r3 = r1->field_13
    //     0xb86180: ldur            w3, [x1, #0x13]
    // 0xb86184: DecompressPointer r3
    //     0xb86184: add             x3, x3, HEAP, lsl #32
    // 0xb86188: stur            x3, [fp, #-8]
    // 0xb8618c: r0 = NVideoListTile()
    //     0xb8618c: bl              #0xb5a5c4  ; AllocateNVideoListTileStub -> NVideoListTile (size=0x20)
    // 0xb86190: mov             x3, x0
    // 0xb86194: ldur            x0, [fp, #-8]
    // 0xb86198: stur            x3, [fp, #-0x20]
    // 0xb8619c: StoreField: r3->field_b = r0
    //     0xb8619c: stur            w0, [x3, #0xb]
    // 0xb861a0: ldur            x0, [fp, #-0x18]
    // 0xb861a4: StoreField: r3->field_f = r0
    //     0xb861a4: stur            w0, [x3, #0xf]
    // 0xb861a8: r0 = false
    //     0xb861a8: add             x0, NULL, #0x30  ; false
    // 0xb861ac: StoreField: r3->field_13 = r0
    //     0xb861ac: stur            w0, [x3, #0x13]
    // 0xb861b0: ldur            x2, [fp, #-0x10]
    // 0xb861b4: r1 = Function '<anonymous closure>':.
    //     0xb861b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ac8] AnonymousClosure: (0xb861e0), in [package:nuonline/app/modules/article/widgets/related_content_widget.dart] RelatedContentWidget::build (0xb85ce8)
    //     0xb861b8: ldr             x1, [x1, #0xac8]
    // 0xb861bc: r0 = AllocateClosure()
    //     0xb861bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xb861c0: mov             x1, x0
    // 0xb861c4: ldur            x0, [fp, #-0x20]
    // 0xb861c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb861c8: stur            w1, [x0, #0x17]
    // 0xb861cc: LeaveFrame
    //     0xb861cc: mov             SP, fp
    //     0xb861d0: ldp             fp, lr, [SP], #0x10
    // 0xb861d4: ret
    //     0xb861d4: ret             
    // 0xb861d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb861d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb861dc: b               #0xb86100
  }
  [closure] Future<dynamic>? <anonymous closure>(dynamic) {
    // ** addr: 0xb861e0, size: 0xb4
    // 0xb861e0: EnterFrame
    //     0xb861e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb861e4: mov             fp, SP
    // 0xb861e8: AllocStack(0x20)
    //     0xb861e8: sub             SP, SP, #0x20
    // 0xb861ec: SetupParameters()
    //     0xb861ec: ldr             x0, [fp, #0x10]
    //     0xb861f0: ldur            w1, [x0, #0x17]
    //     0xb861f4: add             x1, x1, HEAP, lsl #32
    //     0xb861f8: stur            x1, [fp, #-8]
    // 0xb861fc: CheckStackOverflow
    //     0xb861fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86200: cmp             SP, x16
    //     0xb86204: b.ls            #0xb8628c
    // 0xb86208: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb86208: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8620c: ldr             x0, [x0, #0x2670]
    //     0xb86210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb86214: cmp             w0, w16
    //     0xb86218: b.ne            #0xb86224
    //     0xb8621c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb86220: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb86224: r1 = Null
    //     0xb86224: mov             x1, NULL
    // 0xb86228: r2 = 4
    //     0xb86228: movz            x2, #0x4
    // 0xb8622c: r0 = AllocateArray()
    //     0xb8622c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb86230: r16 = "youtube_id"
    //     0xb86230: add             x16, PP, #0x29, lsl #12  ; [pp+0x293f0] "youtube_id"
    //     0xb86234: ldr             x16, [x16, #0x3f0]
    // 0xb86238: StoreField: r0->field_f = r16
    //     0xb86238: stur            w16, [x0, #0xf]
    // 0xb8623c: ldur            x1, [fp, #-8]
    // 0xb86240: LoadField: r2 = r1->field_f
    //     0xb86240: ldur            w2, [x1, #0xf]
    // 0xb86244: DecompressPointer r2
    //     0xb86244: add             x2, x2, HEAP, lsl #32
    // 0xb86248: LoadField: r1 = r2->field_b
    //     0xb86248: ldur            w1, [x2, #0xb]
    // 0xb8624c: DecompressPointer r1
    //     0xb8624c: add             x1, x1, HEAP, lsl #32
    // 0xb86250: StoreField: r0->field_13 = r1
    //     0xb86250: stur            w1, [x0, #0x13]
    // 0xb86254: r16 = <String, String>
    //     0xb86254: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xb86258: ldr             x16, [x16, #0x668]
    // 0xb8625c: stp             x0, x16, [SP]
    // 0xb86260: r0 = Map._fromLiteral()
    //     0xb86260: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb86264: r16 = "/video/video-detail"
    //     0xb86264: add             x16, PP, #0x29, lsl #12  ; [pp+0x29090] "/video/video-detail"
    //     0xb86268: ldr             x16, [x16, #0x90]
    // 0xb8626c: stp             x16, NULL, [SP, #8]
    // 0xb86270: str             x0, [SP]
    // 0xb86274: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb86274: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb86278: ldr             x4, [x4, #0x478]
    // 0xb8627c: r0 = GetNavigation.toNamed()
    //     0xb8627c: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb86280: LeaveFrame
    //     0xb86280: mov             SP, fp
    //     0xb86284: ldp             fp, lr, [SP], #0x10
    // 0xb86288: ret
    //     0xb86288: ret             
    // 0xb8628c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8628c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86290: b               #0xb86208
  }
  [closure] NArticleListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb86294, size: 0x128
    // 0xb86294: EnterFrame
    //     0xb86294: stp             fp, lr, [SP, #-0x10]!
    //     0xb86298: mov             fp, SP
    // 0xb8629c: AllocStack(0x38)
    //     0xb8629c: sub             SP, SP, #0x38
    // 0xb862a0: SetupParameters()
    //     0xb862a0: ldr             x0, [fp, #0x20]
    //     0xb862a4: ldur            w1, [x0, #0x17]
    //     0xb862a8: add             x1, x1, HEAP, lsl #32
    //     0xb862ac: stur            x1, [fp, #-8]
    // 0xb862b0: CheckStackOverflow
    //     0xb862b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb862b4: cmp             SP, x16
    //     0xb862b8: b.ls            #0xb863b4
    // 0xb862bc: r1 = 1
    //     0xb862bc: movz            x1, #0x1
    // 0xb862c0: r0 = AllocateContext()
    //     0xb862c0: bl              #0xec126c  ; AllocateContextStub
    // 0xb862c4: mov             x1, x0
    // 0xb862c8: ldur            x0, [fp, #-8]
    // 0xb862cc: stur            x1, [fp, #-0x10]
    // 0xb862d0: StoreField: r1->field_b = r0
    //     0xb862d0: stur            w0, [x1, #0xb]
    // 0xb862d4: LoadField: r2 = r0->field_f
    //     0xb862d4: ldur            w2, [x0, #0xf]
    // 0xb862d8: DecompressPointer r2
    //     0xb862d8: add             x2, x2, HEAP, lsl #32
    // 0xb862dc: LoadField: r0 = r2->field_b
    //     0xb862dc: ldur            w0, [x2, #0xb]
    // 0xb862e0: DecompressPointer r0
    //     0xb862e0: add             x0, x0, HEAP, lsl #32
    // 0xb862e4: r2 = LoadClassIdInstr(r0)
    //     0xb862e4: ldur            x2, [x0, #-1]
    //     0xb862e8: ubfx            x2, x2, #0xc, #0x14
    // 0xb862ec: ldr             x16, [fp, #0x10]
    // 0xb862f0: stp             x16, x0, [SP]
    // 0xb862f4: mov             x0, x2
    // 0xb862f8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xb862f8: movz            x17, #0x3037
    //     0xb862fc: movk            x17, #0x1, lsl #16
    //     0xb86300: add             lr, x0, x17
    //     0xb86304: ldr             lr, [x21, lr, lsl #3]
    //     0xb86308: blr             lr
    // 0xb8630c: mov             x1, x0
    // 0xb86310: ldur            x2, [fp, #-0x10]
    // 0xb86314: StoreField: r2->field_f = r0
    //     0xb86314: stur            w0, [x2, #0xf]
    //     0xb86318: ldurb           w16, [x2, #-1]
    //     0xb8631c: ldurb           w17, [x0, #-1]
    //     0xb86320: and             x16, x17, x16, lsr #2
    //     0xb86324: tst             x16, HEAP, lsr #32
    //     0xb86328: b.eq            #0xb86330
    //     0xb8632c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb86330: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb86330: ldur            w0, [x1, #0x17]
    // 0xb86334: DecompressPointer r0
    //     0xb86334: add             x0, x0, HEAP, lsl #32
    // 0xb86338: cmp             w0, NULL
    // 0xb8633c: b.ne            #0xb86344
    // 0xb86340: r0 = "-"
    //     0xb86340: ldr             x0, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xb86344: stur            x0, [fp, #-0x20]
    // 0xb86348: LoadField: r3 = r1->field_f
    //     0xb86348: ldur            w3, [x1, #0xf]
    // 0xb8634c: DecompressPointer r3
    //     0xb8634c: add             x3, x3, HEAP, lsl #32
    // 0xb86350: stur            x3, [fp, #-0x18]
    // 0xb86354: LoadField: r4 = r1->field_13
    //     0xb86354: ldur            w4, [x1, #0x13]
    // 0xb86358: DecompressPointer r4
    //     0xb86358: add             x4, x4, HEAP, lsl #32
    // 0xb8635c: stur            x4, [fp, #-8]
    // 0xb86360: r0 = NArticleListTile()
    //     0xb86360: bl              #0xb07fe4  ; AllocateNArticleListTileStub -> NArticleListTile (size=0x28)
    // 0xb86364: mov             x3, x0
    // 0xb86368: ldur            x0, [fp, #-0x18]
    // 0xb8636c: stur            x3, [fp, #-0x28]
    // 0xb86370: StoreField: r3->field_b = r0
    //     0xb86370: stur            w0, [x3, #0xb]
    // 0xb86374: ldur            x0, [fp, #-0x20]
    // 0xb86378: StoreField: r3->field_f = r0
    //     0xb86378: stur            w0, [x3, #0xf]
    // 0xb8637c: ldur            x0, [fp, #-8]
    // 0xb86380: StoreField: r3->field_13 = r0
    //     0xb86380: stur            w0, [x3, #0x13]
    // 0xb86384: ldur            x2, [fp, #-0x10]
    // 0xb86388: r1 = Function '<anonymous closure>':.
    //     0xb86388: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ad0] AnonymousClosure: (0xb863bc), in [package:nuonline/app/modules/article/widgets/related_content_widget.dart] RelatedContentWidget::build (0xb85ce8)
    //     0xb8638c: ldr             x1, [x1, #0xad0]
    // 0xb86390: r0 = AllocateClosure()
    //     0xb86390: bl              #0xec1630  ; AllocateClosureStub
    // 0xb86394: mov             x1, x0
    // 0xb86398: ldur            x0, [fp, #-0x28]
    // 0xb8639c: StoreField: r0->field_1f = r1
    //     0xb8639c: stur            w1, [x0, #0x1f]
    // 0xb863a0: r1 = false
    //     0xb863a0: add             x1, NULL, #0x30  ; false
    // 0xb863a4: StoreField: r0->field_23 = r1
    //     0xb863a4: stur            w1, [x0, #0x23]
    // 0xb863a8: LeaveFrame
    //     0xb863a8: mov             SP, fp
    //     0xb863ac: ldp             fp, lr, [SP], #0x10
    // 0xb863b0: ret
    //     0xb863b0: ret             
    // 0xb863b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb863b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb863b8: b               #0xb862bc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb863bc, size: 0xb0
    // 0xb863bc: EnterFrame
    //     0xb863bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb863c0: mov             fp, SP
    // 0xb863c4: AllocStack(0x20)
    //     0xb863c4: sub             SP, SP, #0x20
    // 0xb863c8: SetupParameters()
    //     0xb863c8: ldr             x0, [fp, #0x10]
    //     0xb863cc: ldur            w1, [x0, #0x17]
    //     0xb863d0: add             x1, x1, HEAP, lsl #32
    //     0xb863d4: stur            x1, [fp, #-8]
    // 0xb863d8: CheckStackOverflow
    //     0xb863d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb863dc: cmp             SP, x16
    //     0xb863e0: b.ls            #0xb86464
    // 0xb863e4: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb863e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb863e8: ldr             x0, [x0, #0x2670]
    //     0xb863ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb863f0: cmp             w0, w16
    //     0xb863f4: b.ne            #0xb86400
    //     0xb863f8: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xb863fc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb86400: r1 = Null
    //     0xb86400: mov             x1, NULL
    // 0xb86404: r2 = 4
    //     0xb86404: movz            x2, #0x4
    // 0xb86408: r0 = AllocateArray()
    //     0xb86408: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb8640c: r16 = "id"
    //     0xb8640c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb86410: ldr             x16, [x16, #0x740]
    // 0xb86414: StoreField: r0->field_f = r16
    //     0xb86414: stur            w16, [x0, #0xf]
    // 0xb86418: ldur            x1, [fp, #-8]
    // 0xb8641c: LoadField: r2 = r1->field_f
    //     0xb8641c: ldur            w2, [x1, #0xf]
    // 0xb86420: DecompressPointer r2
    //     0xb86420: add             x2, x2, HEAP, lsl #32
    // 0xb86424: LoadField: r1 = r2->field_b
    //     0xb86424: ldur            w1, [x2, #0xb]
    // 0xb86428: DecompressPointer r1
    //     0xb86428: add             x1, x1, HEAP, lsl #32
    // 0xb8642c: StoreField: r0->field_13 = r1
    //     0xb8642c: stur            w1, [x0, #0x13]
    // 0xb86430: r16 = <String, int>
    //     0xb86430: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xb86434: stp             x0, x16, [SP]
    // 0xb86438: r0 = Map._fromLiteral()
    //     0xb86438: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb8643c: r16 = "/article/article-detail"
    //     0xb8643c: add             x16, PP, #0x27, lsl #12  ; [pp+0x273d0] "/article/article-detail"
    //     0xb86440: ldr             x16, [x16, #0x3d0]
    // 0xb86444: stp             x16, NULL, [SP, #8]
    // 0xb86448: str             x0, [SP]
    // 0xb8644c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8644c: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb86450: ldr             x4, [x4, #0x478]
    // 0xb86454: r0 = GetNavigation.toNamed()
    //     0xb86454: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb86458: LeaveFrame
    //     0xb86458: mov             SP, fp
    //     0xb8645c: ldp             fp, lr, [SP], #0x10
    // 0xb86460: ret
    //     0xb86460: ret             
    // 0xb86464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86468: b               #0xb863e4
  }
}
