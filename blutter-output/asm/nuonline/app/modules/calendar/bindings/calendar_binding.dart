// lib: , url: package:nuonline/app/modules/calendar/bindings/calendar_binding.dart

// class id: 1050156, size: 0x8
class :: {
}

// class id: 2188, size: 0x8, field offset: 0x8
class CalendarBinding extends Bindings {

  _ dependencies(/* No info */) {
    // ** addr: 0x80ea00, size: 0x70
    // 0x80ea00: EnterFrame
    //     0x80ea00: stp             fp, lr, [SP, #-0x10]!
    //     0x80ea04: mov             fp, SP
    // 0x80ea08: AllocStack(0x10)
    //     0x80ea08: sub             SP, SP, #0x10
    // 0x80ea0c: CheckStackOverflow
    //     0x80ea0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80ea10: cmp             SP, x16
    //     0x80ea14: b.ls            #0x80ea68
    // 0x80ea18: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80ea18: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80ea1c: ldr             x0, [x0, #0x2670]
    //     0x80ea20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80ea24: cmp             w0, w16
    //     0x80ea28: b.ne            #0x80ea34
    //     0x80ea2c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80ea30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80ea34: r1 = Function '<anonymous closure>':.
    //     0x80ea34: add             x1, PP, #0x35, lsl #12  ; [pp+0x35fd0] AnonymousClosure: (0x80ea70), in [package:nuonline/app/modules/calendar/bindings/calendar_binding.dart] CalendarBinding::dependencies (0x80ea00)
    //     0x80ea38: ldr             x1, [x1, #0xfd0]
    // 0x80ea3c: r2 = Null
    //     0x80ea3c: mov             x2, NULL
    // 0x80ea40: r0 = AllocateClosure()
    //     0x80ea40: bl              #0xec1630  ; AllocateClosureStub
    // 0x80ea44: r16 = <CalendarController>
    //     0x80ea44: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a18] TypeArguments: <CalendarController>
    //     0x80ea48: ldr             x16, [x16, #0xa18]
    // 0x80ea4c: stp             x0, x16, [SP]
    // 0x80ea50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80ea50: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80ea54: r0 = Inst.lazyPut()
    //     0x80ea54: bl              #0x80bc28  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x80ea58: r0 = Null
    //     0x80ea58: mov             x0, NULL
    // 0x80ea5c: LeaveFrame
    //     0x80ea5c: mov             SP, fp
    //     0x80ea60: ldp             fp, lr, [SP], #0x10
    // 0x80ea64: ret
    //     0x80ea64: ret             
    // 0x80ea68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80ea68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80ea6c: b               #0x80ea18
  }
  [closure] CalendarController <anonymous closure>(dynamic) {
    // ** addr: 0x80ea70, size: 0xc0
    // 0x80ea70: EnterFrame
    //     0x80ea70: stp             fp, lr, [SP, #-0x10]!
    //     0x80ea74: mov             fp, SP
    // 0x80ea78: AllocStack(0x28)
    //     0x80ea78: sub             SP, SP, #0x28
    // 0x80ea7c: CheckStackOverflow
    //     0x80ea7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80ea80: cmp             SP, x16
    //     0x80ea84: b.ls            #0x80eb28
    // 0x80ea88: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x80ea88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80ea8c: ldr             x0, [x0, #0x2670]
    //     0x80ea90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x80ea94: cmp             w0, w16
    //     0x80ea98: b.ne            #0x80eaa4
    //     0x80ea9c: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0x80eaa0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x80eaa4: r16 = <EventRepository>
    //     0x80eaa4: add             x16, PP, #0xf, lsl #12  ; [pp+0xff28] TypeArguments: <EventRepository>
    //     0x80eaa8: ldr             x16, [x16, #0xf28]
    // 0x80eaac: r30 = "event_repo"
    //     0x80eaac: add             lr, PP, #0xf, lsl #12  ; [pp+0xff30] "event_repo"
    //     0x80eab0: ldr             lr, [lr, #0xf30]
    // 0x80eab4: stp             lr, x16, [SP]
    // 0x80eab8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80eab8: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80eabc: r0 = Inst.find()
    //     0x80eabc: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80eac0: stur            x0, [fp, #-8]
    // 0x80eac4: r16 = <HijriService>
    //     0x80eac4: ldr             x16, [PP, #0x120]  ; [pp+0x120] TypeArguments: <HijriService>
    // 0x80eac8: r30 = "hijri_service"
    //     0x80eac8: ldr             lr, [PP, #0x118]  ; [pp+0x118] "hijri_service"
    // 0x80eacc: stp             lr, x16, [SP]
    // 0x80ead0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80ead0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80ead4: r0 = Inst.find()
    //     0x80ead4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80ead8: stur            x0, [fp, #-0x10]
    // 0x80eadc: r16 = <CalendarRepository>
    //     0x80eadc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10218] TypeArguments: <CalendarRepository>
    //     0x80eae0: ldr             x16, [x16, #0x218]
    // 0x80eae4: r30 = "calendar_repo"
    //     0x80eae4: add             lr, PP, #0x10, lsl #12  ; [pp+0x10220] "calendar_repo"
    //     0x80eae8: ldr             lr, [lr, #0x220]
    // 0x80eaec: stp             lr, x16, [SP]
    // 0x80eaf0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x80eaf0: ldr             x4, [PP, #0x98]  ; [pp+0x98] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x80eaf4: r0 = Inst.find()
    //     0x80eaf4: bl              #0x80ccac  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x80eaf8: stur            x0, [fp, #-0x18]
    // 0x80eafc: r0 = CalendarController()
    //     0x80eafc: bl              #0x80f0bc  ; AllocateCalendarControllerStub -> CalendarController (size=0x60)
    // 0x80eb00: mov             x1, x0
    // 0x80eb04: ldur            x2, [fp, #-0x18]
    // 0x80eb08: ldur            x3, [fp, #-0x10]
    // 0x80eb0c: ldur            x5, [fp, #-8]
    // 0x80eb10: stur            x0, [fp, #-8]
    // 0x80eb14: r0 = CalendarController()
    //     0x80eb14: bl              #0x80eb30  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::CalendarController
    // 0x80eb18: ldur            x0, [fp, #-8]
    // 0x80eb1c: LeaveFrame
    //     0x80eb1c: mov             SP, fp
    //     0x80eb20: ldp             fp, lr, [SP], #0x10
    // 0x80eb24: ret
    //     0x80eb24: ret             
    // 0x80eb28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80eb28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80eb2c: b               #0x80ea88
  }
}
