// lib: , url: package:nuonline/app/modules/calendar/controllers/calendar_controller.dart

// class id: 1050157, size: 0x8
class :: {
}

// class id: 1996, size: 0x60, field offset: 0x20
class CalendarController extends GetxController {

  late final DateTime firstDay; // offset: 0x2c
  late final DateTime lastDay; // offset: 0x30
  late PageController _calendarController; // offset: 0x58

  _ CalendarController(/* No info */) {
    // ** addr: 0x80eb30, size: 0x368
    // 0x80eb30: EnterFrame
    //     0x80eb30: stp             fp, lr, [SP, #-0x10]!
    //     0x80eb34: mov             fp, SP
    // 0x80eb38: AllocStack(0x38)
    //     0x80eb38: sub             SP, SP, #0x38
    // 0x80eb3c: r0 = Sentinel
    //     0x80eb3c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80eb40: mov             x4, x1
    // 0x80eb44: stur            x2, [fp, #-0x10]
    // 0x80eb48: mov             x16, x3
    // 0x80eb4c: mov             x3, x2
    // 0x80eb50: mov             x2, x16
    // 0x80eb54: stur            x1, [fp, #-8]
    // 0x80eb58: mov             x1, x5
    // 0x80eb5c: stur            x2, [fp, #-0x18]
    // 0x80eb60: stur            x5, [fp, #-0x20]
    // 0x80eb64: CheckStackOverflow
    //     0x80eb64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80eb68: cmp             SP, x16
    //     0x80eb6c: b.ls            #0x80ee90
    // 0x80eb70: StoreField: r4->field_2b = r0
    //     0x80eb70: stur            w0, [x4, #0x2b]
    // 0x80eb74: StoreField: r4->field_2f = r0
    //     0x80eb74: stur            w0, [x4, #0x2f]
    // 0x80eb78: StoreField: r4->field_57 = r0
    //     0x80eb78: stur            w0, [x4, #0x57]
    // 0x80eb7c: r0 = _getCurrentMicros()
    //     0x80eb7c: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x80eb80: r1 = <Event>
    //     0x80eb80: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x80eb84: ldr             x1, [x1, #0xd0]
    // 0x80eb88: r0 = RxList()
    //     0x80eb88: bl              #0x80c8a0  ; AllocateRxListStub -> RxList<X0> (size=0x1c)
    // 0x80eb8c: mov             x1, x0
    // 0x80eb90: stur            x0, [fp, #-0x28]
    // 0x80eb94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80eb94: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80eb98: r0 = RxList()
    //     0x80eb98: bl              #0x80c57c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList
    // 0x80eb9c: ldur            x0, [fp, #-0x28]
    // 0x80eba0: ldur            x2, [fp, #-8]
    // 0x80eba4: StoreField: r2->field_33 = r0
    //     0x80eba4: stur            w0, [x2, #0x33]
    //     0x80eba8: ldurb           w16, [x2, #-1]
    //     0x80ebac: ldurb           w17, [x0, #-1]
    //     0x80ebb0: and             x16, x17, x16, lsr #2
    //     0x80ebb4: tst             x16, HEAP, lsr #32
    //     0x80ebb8: b.eq            #0x80ebc0
    //     0x80ebbc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80ebc0: r1 = <Event>
    //     0x80ebc0: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x80ebc4: ldr             x1, [x1, #0xd0]
    // 0x80ebc8: r0 = RxList()
    //     0x80ebc8: bl              #0x80c8a0  ; AllocateRxListStub -> RxList<X0> (size=0x1c)
    // 0x80ebcc: mov             x1, x0
    // 0x80ebd0: stur            x0, [fp, #-0x28]
    // 0x80ebd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80ebd4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80ebd8: r0 = RxList()
    //     0x80ebd8: bl              #0x80c57c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList
    // 0x80ebdc: ldur            x0, [fp, #-0x28]
    // 0x80ebe0: ldur            x2, [fp, #-8]
    // 0x80ebe4: StoreField: r2->field_37 = r0
    //     0x80ebe4: stur            w0, [x2, #0x37]
    //     0x80ebe8: ldurb           w16, [x2, #-1]
    //     0x80ebec: ldurb           w17, [x0, #-1]
    //     0x80ebf0: and             x16, x17, x16, lsr #2
    //     0x80ebf4: tst             x16, HEAP, lsr #32
    //     0x80ebf8: b.eq            #0x80ec00
    //     0x80ebfc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80ec00: r1 = <DateTime>
    //     0x80ec00: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x80ec04: ldr             x1, [x1, #0xdd8]
    // 0x80ec08: r0 = RxList()
    //     0x80ec08: bl              #0x80c8a0  ; AllocateRxListStub -> RxList<X0> (size=0x1c)
    // 0x80ec0c: mov             x1, x0
    // 0x80ec10: stur            x0, [fp, #-0x28]
    // 0x80ec14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80ec14: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80ec18: r0 = RxList()
    //     0x80ec18: bl              #0x80c57c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList
    // 0x80ec1c: ldur            x0, [fp, #-0x28]
    // 0x80ec20: ldur            x2, [fp, #-8]
    // 0x80ec24: StoreField: r2->field_3b = r0
    //     0x80ec24: stur            w0, [x2, #0x3b]
    //     0x80ec28: ldurb           w16, [x2, #-1]
    //     0x80ec2c: ldurb           w17, [x0, #-1]
    //     0x80ec30: and             x16, x17, x16, lsr #2
    //     0x80ec34: tst             x16, HEAP, lsr #32
    //     0x80ec38: b.eq            #0x80ec40
    //     0x80ec3c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80ec40: r1 = <Event>
    //     0x80ec40: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x80ec44: ldr             x1, [x1, #0xd0]
    // 0x80ec48: r0 = RxList()
    //     0x80ec48: bl              #0x80c8a0  ; AllocateRxListStub -> RxList<X0> (size=0x1c)
    // 0x80ec4c: mov             x1, x0
    // 0x80ec50: stur            x0, [fp, #-0x28]
    // 0x80ec54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80ec54: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x80ec58: r0 = RxList()
    //     0x80ec58: bl              #0x80c57c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList
    // 0x80ec5c: ldur            x0, [fp, #-0x28]
    // 0x80ec60: ldur            x1, [fp, #-8]
    // 0x80ec64: StoreField: r1->field_3f = r0
    //     0x80ec64: stur            w0, [x1, #0x3f]
    //     0x80ec68: ldurb           w16, [x1, #-1]
    //     0x80ec6c: ldurb           w17, [x0, #-1]
    //     0x80ec70: and             x16, x17, x16, lsr #2
    //     0x80ec74: tst             x16, HEAP, lsr #32
    //     0x80ec78: b.eq            #0x80ec80
    //     0x80ec7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ec80: r16 = <String, List<EventCategory>>
    //     0x80ec80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fae8] TypeArguments: <String, List<EventCategory>>
    //     0x80ec84: ldr             x16, [x16, #0xae8]
    // 0x80ec88: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x80ec8c: stp             lr, x16, [SP]
    // 0x80ec90: r0 = Map._fromLiteral()
    //     0x80ec90: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x80ec94: r1 = <Map<String, List<EventCategory>>>
    //     0x80ec94: add             x1, PP, #0x35, lsl #12  ; [pp+0x35fd8] TypeArguments: <Map<String, List<EventCategory>>>
    //     0x80ec98: ldr             x1, [x1, #0xfd8]
    // 0x80ec9c: stur            x0, [fp, #-0x28]
    // 0x80eca0: r0 = Rx()
    //     0x80eca0: bl              #0x80ef18  ; AllocateRxStub -> Rx<X0> (size=0x20)
    // 0x80eca4: mov             x1, x0
    // 0x80eca8: ldur            x2, [fp, #-0x28]
    // 0x80ecac: stur            x0, [fp, #-0x28]
    // 0x80ecb0: r0 = _RxImpl()
    //     0x80ecb0: bl              #0x80c8fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxImpl::_RxImpl
    // 0x80ecb4: ldur            x0, [fp, #-0x28]
    // 0x80ecb8: ldur            x1, [fp, #-8]
    // 0x80ecbc: StoreField: r1->field_43 = r0
    //     0x80ecbc: stur            w0, [x1, #0x43]
    //     0x80ecc0: ldurb           w16, [x1, #-1]
    //     0x80ecc4: ldurb           w17, [x0, #-1]
    //     0x80ecc8: and             x16, x17, x16, lsr #2
    //     0x80eccc: tst             x16, HEAP, lsr #32
    //     0x80ecd0: b.eq            #0x80ecd8
    //     0x80ecd4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ecd8: r0 = DateTime()
    //     0x80ecd8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x80ecdc: mov             x1, x0
    // 0x80ece0: r0 = false
    //     0x80ece0: add             x0, NULL, #0x30  ; false
    // 0x80ece4: stur            x1, [fp, #-0x28]
    // 0x80ece8: StoreField: r1->field_13 = r0
    //     0x80ece8: stur            w0, [x1, #0x13]
    // 0x80ecec: r0 = _getCurrentMicros()
    //     0x80ecec: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x80ecf0: r1 = LoadInt32Instr(r0)
    //     0x80ecf0: sbfx            x1, x0, #1, #0x1f
    //     0x80ecf4: tbz             w0, #0, #0x80ecfc
    //     0x80ecf8: ldur            x1, [x0, #7]
    // 0x80ecfc: ldur            x0, [fp, #-0x28]
    // 0x80ed00: StoreField: r0->field_7 = r1
    //     0x80ed00: stur            x1, [x0, #7]
    // 0x80ed04: r16 = <DateTime>
    //     0x80ed04: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x80ed08: ldr             x16, [x16, #0xdd8]
    // 0x80ed0c: stp             x0, x16, [SP]
    // 0x80ed10: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80ed10: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80ed14: r0 = RxT.obs()
    //     0x80ed14: bl              #0x80eeb8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x80ed18: ldur            x1, [fp, #-8]
    // 0x80ed1c: StoreField: r1->field_47 = r0
    //     0x80ed1c: stur            w0, [x1, #0x47]
    //     0x80ed20: ldurb           w16, [x1, #-1]
    //     0x80ed24: ldurb           w17, [x0, #-1]
    //     0x80ed28: and             x16, x17, x16, lsr #2
    //     0x80ed2c: tst             x16, HEAP, lsr #32
    //     0x80ed30: b.eq            #0x80ed38
    //     0x80ed34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ed38: r0 = DateTime()
    //     0x80ed38: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x80ed3c: mov             x1, x0
    // 0x80ed40: r0 = false
    //     0x80ed40: add             x0, NULL, #0x30  ; false
    // 0x80ed44: stur            x1, [fp, #-0x28]
    // 0x80ed48: StoreField: r1->field_13 = r0
    //     0x80ed48: stur            w0, [x1, #0x13]
    // 0x80ed4c: r0 = _getCurrentMicros()
    //     0x80ed4c: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x80ed50: r1 = LoadInt32Instr(r0)
    //     0x80ed50: sbfx            x1, x0, #1, #0x1f
    //     0x80ed54: tbz             w0, #0, #0x80ed5c
    //     0x80ed58: ldur            x1, [x0, #7]
    // 0x80ed5c: ldur            x0, [fp, #-0x28]
    // 0x80ed60: StoreField: r0->field_7 = r1
    //     0x80ed60: stur            x1, [x0, #7]
    // 0x80ed64: r16 = <DateTime>
    //     0x80ed64: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x80ed68: ldr             x16, [x16, #0xdd8]
    // 0x80ed6c: stp             x0, x16, [SP]
    // 0x80ed70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x80ed70: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x80ed74: r0 = RxT.obs()
    //     0x80ed74: bl              #0x80eeb8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x80ed78: ldur            x2, [fp, #-8]
    // 0x80ed7c: StoreField: r2->field_4b = r0
    //     0x80ed7c: stur            w0, [x2, #0x4b]
    //     0x80ed80: ldurb           w16, [x2, #-1]
    //     0x80ed84: ldurb           w17, [x0, #-1]
    //     0x80ed88: and             x16, x17, x16, lsr #2
    //     0x80ed8c: tst             x16, HEAP, lsr #32
    //     0x80ed90: b.eq            #0x80ed98
    //     0x80ed94: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80ed98: r1 = ""
    //     0x80ed98: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x80ed9c: r0 = StringExtension.obs()
    //     0x80ed9c: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x80eda0: ldur            x2, [fp, #-8]
    // 0x80eda4: StoreField: r2->field_4f = r0
    //     0x80eda4: stur            w0, [x2, #0x4f]
    //     0x80eda8: ldurb           w16, [x2, #-1]
    //     0x80edac: ldurb           w17, [x0, #-1]
    //     0x80edb0: and             x16, x17, x16, lsr #2
    //     0x80edb4: tst             x16, HEAP, lsr #32
    //     0x80edb8: b.eq            #0x80edc0
    //     0x80edbc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x80edc0: r1 = ""
    //     0x80edc0: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x80edc4: r0 = StringExtension.obs()
    //     0x80edc4: bl              #0x80e0e0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x80edc8: ldur            x1, [fp, #-8]
    // 0x80edcc: StoreField: r1->field_53 = r0
    //     0x80edcc: stur            w0, [x1, #0x53]
    //     0x80edd0: ldurb           w16, [x1, #-1]
    //     0x80edd4: ldurb           w17, [x0, #-1]
    //     0x80edd8: and             x16, x17, x16, lsr #2
    //     0x80eddc: tst             x16, HEAP, lsr #32
    //     0x80ede0: b.eq            #0x80ede8
    //     0x80ede4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ede8: r16 = <String, List<Event>>
    //     0x80ede8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fe0] TypeArguments: <String, List<Event>>
    //     0x80edec: ldr             x16, [x16, #0xfe0]
    // 0x80edf0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x80edf4: stp             lr, x16, [SP]
    // 0x80edf8: r0 = Map._fromLiteral()
    //     0x80edf8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x80edfc: ldur            x1, [fp, #-8]
    // 0x80ee00: StoreField: r1->field_5b = r0
    //     0x80ee00: stur            w0, [x1, #0x5b]
    //     0x80ee04: ldurb           w16, [x1, #-1]
    //     0x80ee08: ldurb           w17, [x0, #-1]
    //     0x80ee0c: and             x16, x17, x16, lsr #2
    //     0x80ee10: tst             x16, HEAP, lsr #32
    //     0x80ee14: b.eq            #0x80ee1c
    //     0x80ee18: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ee1c: ldur            x0, [fp, #-0x20]
    // 0x80ee20: StoreField: r1->field_1f = r0
    //     0x80ee20: stur            w0, [x1, #0x1f]
    //     0x80ee24: ldurb           w16, [x1, #-1]
    //     0x80ee28: ldurb           w17, [x0, #-1]
    //     0x80ee2c: and             x16, x17, x16, lsr #2
    //     0x80ee30: tst             x16, HEAP, lsr #32
    //     0x80ee34: b.eq            #0x80ee3c
    //     0x80ee38: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ee3c: ldur            x0, [fp, #-0x18]
    // 0x80ee40: StoreField: r1->field_27 = r0
    //     0x80ee40: stur            w0, [x1, #0x27]
    //     0x80ee44: ldurb           w16, [x1, #-1]
    //     0x80ee48: ldurb           w17, [x0, #-1]
    //     0x80ee4c: and             x16, x17, x16, lsr #2
    //     0x80ee50: tst             x16, HEAP, lsr #32
    //     0x80ee54: b.eq            #0x80ee5c
    //     0x80ee58: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ee5c: ldur            x0, [fp, #-0x10]
    // 0x80ee60: StoreField: r1->field_23 = r0
    //     0x80ee60: stur            w0, [x1, #0x23]
    //     0x80ee64: ldurb           w16, [x1, #-1]
    //     0x80ee68: ldurb           w17, [x0, #-1]
    //     0x80ee6c: and             x16, x17, x16, lsr #2
    //     0x80ee70: tst             x16, HEAP, lsr #32
    //     0x80ee74: b.eq            #0x80ee7c
    //     0x80ee78: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x80ee7c: r0 = _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin()
    //     0x80ee7c: bl              #0x639fdc  ; [package:get/get_state_manager/src/simple/get_controllers.dart] _GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin::_GetxController&DisposableInterface&ListenableMixin&ListNotifierMixin
    // 0x80ee80: r0 = Null
    //     0x80ee80: mov             x0, NULL
    // 0x80ee84: LeaveFrame
    //     0x80ee84: mov             SP, fp
    //     0x80ee88: ldp             fp, lr, [SP], #0x10
    // 0x80ee8c: ret
    //     0x80ee8c: ret             
    // 0x80ee90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80ee90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80ee94: b               #0x80eb70
  }
  _ onInit(/* No info */) async {
    // ** addr: 0x8ed270, size: 0xc0
    // 0x8ed270: EnterFrame
    //     0x8ed270: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed274: mov             fp, SP
    // 0x8ed278: AllocStack(0x20)
    //     0x8ed278: sub             SP, SP, #0x20
    // 0x8ed27c: SetupParameters(CalendarController this /* r1 => r1, fp-0x10 */)
    //     0x8ed27c: stur            NULL, [fp, #-8]
    //     0x8ed280: stur            x1, [fp, #-0x10]
    // 0x8ed284: CheckStackOverflow
    //     0x8ed284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed288: cmp             SP, x16
    //     0x8ed28c: b.ls            #0x8ed328
    // 0x8ed290: r1 = 1
    //     0x8ed290: movz            x1, #0x1
    // 0x8ed294: r0 = AllocateContext()
    //     0x8ed294: bl              #0xec126c  ; AllocateContextStub
    // 0x8ed298: mov             x2, x0
    // 0x8ed29c: ldur            x1, [fp, #-0x10]
    // 0x8ed2a0: stur            x2, [fp, #-0x18]
    // 0x8ed2a4: StoreField: r2->field_f = r1
    //     0x8ed2a4: stur            w1, [x2, #0xf]
    // 0x8ed2a8: InitAsync() -> Future<void?>
    //     0x8ed2a8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ed2ac: bl              #0x661298  ; InitAsyncStub
    // 0x8ed2b0: ldur            x1, [fp, #-0x10]
    // 0x8ed2b4: r0 = onInit()
    //     0x8ed2b4: bl              #0x912f78  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] DisposableInterface::onInit
    // 0x8ed2b8: ldur            x0, [fp, #-0x10]
    // 0x8ed2bc: LoadField: r1 = r0->field_27
    //     0x8ed2bc: ldur            w1, [x0, #0x27]
    // 0x8ed2c0: DecompressPointer r1
    //     0x8ed2c0: add             x1, x1, HEAP, lsl #32
    // 0x8ed2c4: LoadField: r3 = r1->field_23
    //     0x8ed2c4: ldur            w3, [x1, #0x23]
    // 0x8ed2c8: DecompressPointer r3
    //     0x8ed2c8: add             x3, x3, HEAP, lsl #32
    // 0x8ed2cc: ldur            x2, [fp, #-0x18]
    // 0x8ed2d0: stur            x3, [fp, #-0x20]
    // 0x8ed2d4: r1 = Function '<anonymous closure>':.
    //     0x8ed2d4: add             x1, PP, #0x40, lsl #12  ; [pp+0x406f0] AnonymousClosure: (0x8ef3bc), in [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onInit (0x8ed270)
    //     0x8ed2d8: ldr             x1, [x1, #0x6f0]
    // 0x8ed2dc: r0 = AllocateClosure()
    //     0x8ed2dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x8ed2e0: ldur            x1, [fp, #-0x20]
    // 0x8ed2e4: mov             x2, x0
    // 0x8ed2e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8ed2e8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8ed2ec: r0 = listen()
    //     0x8ed2ec: bl              #0x8a65ac  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::listen
    // 0x8ed2f0: ldur            x1, [fp, #-0x10]
    // 0x8ed2f4: r0 = setCalenderHeader()
    //     0x8ed2f4: bl              #0x8eed60  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::setCalenderHeader
    // 0x8ed2f8: ldur            x1, [fp, #-0x10]
    // 0x8ed2fc: r0 = checkUpdate()
    //     0x8ed2fc: bl              #0x8eda34  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::checkUpdate
    // 0x8ed300: mov             x1, x0
    // 0x8ed304: stur            x1, [fp, #-0x20]
    // 0x8ed308: r0 = Await()
    //     0x8ed308: bl              #0x661044  ; AwaitStub
    // 0x8ed30c: ldur            x1, [fp, #-0x10]
    // 0x8ed310: r0 = loadEvent()
    //     0x8ed310: bl              #0x8ed330  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::loadEvent
    // 0x8ed314: mov             x1, x0
    // 0x8ed318: stur            x1, [fp, #-0x10]
    // 0x8ed31c: r0 = Await()
    //     0x8ed31c: bl              #0x661044  ; AwaitStub
    // 0x8ed320: r0 = Null
    //     0x8ed320: mov             x0, NULL
    // 0x8ed324: r0 = ReturnAsyncNotFuture()
    //     0x8ed324: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ed328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed328: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed32c: b               #0x8ed290
  }
  _ loadEvent(/* No info */) async {
    // ** addr: 0x8ed330, size: 0x140
    // 0x8ed330: EnterFrame
    //     0x8ed330: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed334: mov             fp, SP
    // 0x8ed338: AllocStack(0x28)
    //     0x8ed338: sub             SP, SP, #0x28
    // 0x8ed33c: SetupParameters(CalendarController this /* r1 => r1, fp-0x10 */)
    //     0x8ed33c: stur            NULL, [fp, #-8]
    //     0x8ed340: stur            x1, [fp, #-0x10]
    // 0x8ed344: CheckStackOverflow
    //     0x8ed344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed348: cmp             SP, x16
    //     0x8ed34c: b.ls            #0x8ed464
    // 0x8ed350: InitAsync() -> Future<void?>
    //     0x8ed350: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ed354: bl              #0x661298  ; InitAsyncStub
    // 0x8ed358: ldur            x0, [fp, #-0x10]
    // 0x8ed35c: LoadField: r1 = r0->field_47
    //     0x8ed35c: ldur            w1, [x0, #0x47]
    // 0x8ed360: DecompressPointer r1
    //     0x8ed360: add             x1, x1, HEAP, lsl #32
    // 0x8ed364: r0 = value()
    //     0x8ed364: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8ed368: mov             x1, x0
    // 0x8ed36c: r0 = DateTimeExtensions.toUniqueKey()
    //     0x8ed36c: bl              #0x8ed9bc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.toUniqueKey
    // 0x8ed370: mov             x3, x0
    // 0x8ed374: ldur            x0, [fp, #-0x10]
    // 0x8ed378: stur            x3, [fp, #-0x20]
    // 0x8ed37c: LoadField: r4 = r0->field_5b
    //     0x8ed37c: ldur            w4, [x0, #0x5b]
    // 0x8ed380: DecompressPointer r4
    //     0x8ed380: add             x4, x4, HEAP, lsl #32
    // 0x8ed384: mov             x1, x4
    // 0x8ed388: mov             x2, x3
    // 0x8ed38c: stur            x4, [fp, #-0x18]
    // 0x8ed390: r0 = containsKey()
    //     0x8ed390: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x8ed394: tbnz            w0, #4, #0x8ed3f0
    // 0x8ed398: ldur            x0, [fp, #-0x10]
    // 0x8ed39c: ldur            x3, [fp, #-0x18]
    // 0x8ed3a0: LoadField: r4 = r0->field_33
    //     0x8ed3a0: ldur            w4, [x0, #0x33]
    // 0x8ed3a4: DecompressPointer r4
    //     0x8ed3a4: add             x4, x4, HEAP, lsl #32
    // 0x8ed3a8: mov             x1, x3
    // 0x8ed3ac: ldur            x2, [fp, #-0x20]
    // 0x8ed3b0: stur            x4, [fp, #-0x28]
    // 0x8ed3b4: r0 = _getValueOrData()
    //     0x8ed3b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8ed3b8: mov             x1, x0
    // 0x8ed3bc: ldur            x0, [fp, #-0x18]
    // 0x8ed3c0: LoadField: r2 = r0->field_f
    //     0x8ed3c0: ldur            w2, [x0, #0xf]
    // 0x8ed3c4: DecompressPointer r2
    //     0x8ed3c4: add             x2, x2, HEAP, lsl #32
    // 0x8ed3c8: cmp             w2, w1
    // 0x8ed3cc: b.ne            #0x8ed3d8
    // 0x8ed3d0: r2 = Null
    //     0x8ed3d0: mov             x2, NULL
    // 0x8ed3d4: b               #0x8ed3dc
    // 0x8ed3d8: mov             x2, x1
    // 0x8ed3dc: cmp             w2, NULL
    // 0x8ed3e0: b.eq            #0x8ed46c
    // 0x8ed3e4: ldur            x1, [fp, #-0x28]
    // 0x8ed3e8: r0 = value=()
    //     0x8ed3e8: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8ed3ec: b               #0x8ed454
    // 0x8ed3f0: ldur            x2, [fp, #-0x10]
    // 0x8ed3f4: ldur            x0, [fp, #-0x18]
    // 0x8ed3f8: LoadField: r3 = r2->field_1f
    //     0x8ed3f8: ldur            w3, [x2, #0x1f]
    // 0x8ed3fc: DecompressPointer r3
    //     0x8ed3fc: add             x3, x3, HEAP, lsl #32
    // 0x8ed400: stur            x3, [fp, #-0x28]
    // 0x8ed404: LoadField: r1 = r2->field_47
    //     0x8ed404: ldur            w1, [x2, #0x47]
    // 0x8ed408: DecompressPointer r1
    //     0x8ed408: add             x1, x1, HEAP, lsl #32
    // 0x8ed40c: r0 = value()
    //     0x8ed40c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8ed410: ldur            x1, [fp, #-0x28]
    // 0x8ed414: mov             x2, x0
    // 0x8ed418: r0 = get()
    //     0x8ed418: bl              #0x81f494  ; [package:nuonline/app/data/repositories/event_repository.dart] EventRepository::get
    // 0x8ed41c: mov             x1, x0
    // 0x8ed420: stur            x1, [fp, #-0x28]
    // 0x8ed424: r0 = Await()
    //     0x8ed424: bl              #0x661044  ; AwaitStub
    // 0x8ed428: mov             x3, x0
    // 0x8ed42c: ldur            x0, [fp, #-0x10]
    // 0x8ed430: stur            x3, [fp, #-0x28]
    // 0x8ed434: LoadField: r1 = r0->field_33
    //     0x8ed434: ldur            w1, [x0, #0x33]
    // 0x8ed438: DecompressPointer r1
    //     0x8ed438: add             x1, x1, HEAP, lsl #32
    // 0x8ed43c: mov             x2, x3
    // 0x8ed440: r0 = value=()
    //     0x8ed440: bl              #0x7dad58  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::value=
    // 0x8ed444: ldur            x1, [fp, #-0x18]
    // 0x8ed448: ldur            x2, [fp, #-0x20]
    // 0x8ed44c: ldur            x3, [fp, #-0x28]
    // 0x8ed450: r0 = []=()
    //     0x8ed450: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x8ed454: ldur            x1, [fp, #-0x10]
    // 0x8ed458: r0 = groupingEvent()
    //     0x8ed458: bl              #0x8ed470  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::groupingEvent
    // 0x8ed45c: r0 = Null
    //     0x8ed45c: mov             x0, NULL
    // 0x8ed460: r0 = ReturnAsyncNotFuture()
    //     0x8ed460: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ed464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed468: b               #0x8ed350
    // 0x8ed46c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8ed46c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ groupingEvent(/* No info */) {
    // ** addr: 0x8ed470, size: 0x54c
    // 0x8ed470: EnterFrame
    //     0x8ed470: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed474: mov             fp, SP
    // 0x8ed478: AllocStack(0x90)
    //     0x8ed478: sub             SP, SP, #0x90
    // 0x8ed47c: SetupParameters(CalendarController this /* r1 => r0, fp-0x8 */)
    //     0x8ed47c: mov             x0, x1
    //     0x8ed480: stur            x1, [fp, #-8]
    // 0x8ed484: CheckStackOverflow
    //     0x8ed484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed488: cmp             SP, x16
    //     0x8ed48c: b.ls            #0x8ed9ac
    // 0x8ed490: r1 = <Event>
    //     0x8ed490: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x8ed494: ldr             x1, [x1, #0xd0]
    // 0x8ed498: r2 = 0
    //     0x8ed498: movz            x2, #0
    // 0x8ed49c: r0 = _GrowableList()
    //     0x8ed49c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ed4a0: r1 = <Event>
    //     0x8ed4a0: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x8ed4a4: ldr             x1, [x1, #0xd0]
    // 0x8ed4a8: r2 = 0
    //     0x8ed4a8: movz            x2, #0
    // 0x8ed4ac: stur            x0, [fp, #-0x10]
    // 0x8ed4b0: r0 = _GrowableList()
    //     0x8ed4b0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ed4b4: r1 = <DateTime>
    //     0x8ed4b4: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x8ed4b8: ldr             x1, [x1, #0xdd8]
    // 0x8ed4bc: r2 = 0
    //     0x8ed4bc: movz            x2, #0
    // 0x8ed4c0: stur            x0, [fp, #-0x18]
    // 0x8ed4c4: r0 = _GrowableList()
    //     0x8ed4c4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ed4c8: stur            x0, [fp, #-0x20]
    // 0x8ed4cc: r16 = <String, List<EventCategory>>
    //     0x8ed4cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fae8] TypeArguments: <String, List<EventCategory>>
    //     0x8ed4d0: ldr             x16, [x16, #0xae8]
    // 0x8ed4d4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8ed4d8: stp             lr, x16, [SP]
    // 0x8ed4dc: r0 = Map._fromLiteral()
    //     0x8ed4dc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8ed4e0: mov             x2, x0
    // 0x8ed4e4: ldur            x0, [fp, #-8]
    // 0x8ed4e8: stur            x2, [fp, #-0x28]
    // 0x8ed4ec: LoadField: r1 = r0->field_33
    //     0x8ed4ec: ldur            w1, [x0, #0x33]
    // 0x8ed4f0: DecompressPointer r1
    //     0x8ed4f0: add             x1, x1, HEAP, lsl #32
    // 0x8ed4f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8ed4f4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8ed4f8: r0 = toList()
    //     0x8ed4f8: bl              #0x862c84  ; [dart:collection] ListBase::toList
    // 0x8ed4fc: mov             x3, x0
    // 0x8ed500: stur            x3, [fp, #-0x50]
    // 0x8ed504: LoadField: r4 = r3->field_7
    //     0x8ed504: ldur            w4, [x3, #7]
    // 0x8ed508: DecompressPointer r4
    //     0x8ed508: add             x4, x4, HEAP, lsl #32
    // 0x8ed50c: stur            x4, [fp, #-0x48]
    // 0x8ed510: LoadField: r0 = r3->field_b
    //     0x8ed510: ldur            w0, [x3, #0xb]
    // 0x8ed514: r5 = LoadInt32Instr(r0)
    //     0x8ed514: sbfx            x5, x0, #1, #0x1f
    // 0x8ed518: stur            x5, [fp, #-0x40]
    // 0x8ed51c: ldur            x7, [fp, #-0x20]
    // 0x8ed520: r0 = 0
    //     0x8ed520: movz            x0, #0
    // 0x8ed524: ldur            x9, [fp, #-0x10]
    // 0x8ed528: ldur            x8, [fp, #-0x18]
    // 0x8ed52c: ldur            x6, [fp, #-0x28]
    // 0x8ed530: CheckStackOverflow
    //     0x8ed530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed534: cmp             SP, x16
    //     0x8ed538: b.ls            #0x8ed9b4
    // 0x8ed53c: LoadField: r1 = r3->field_b
    //     0x8ed53c: ldur            w1, [x3, #0xb]
    // 0x8ed540: r2 = LoadInt32Instr(r1)
    //     0x8ed540: sbfx            x2, x1, #1, #0x1f
    // 0x8ed544: cmp             x5, x2
    // 0x8ed548: b.ne            #0x8ed98c
    // 0x8ed54c: cmp             x0, x2
    // 0x8ed550: b.ge            #0x8ed8ec
    // 0x8ed554: LoadField: r1 = r3->field_f
    //     0x8ed554: ldur            w1, [x3, #0xf]
    // 0x8ed558: DecompressPointer r1
    //     0x8ed558: add             x1, x1, HEAP, lsl #32
    // 0x8ed55c: ArrayLoad: r10 = r1[r0]  ; Unknown_4
    //     0x8ed55c: add             x16, x1, x0, lsl #2
    //     0x8ed560: ldur            w10, [x16, #0xf]
    // 0x8ed564: DecompressPointer r10
    //     0x8ed564: add             x10, x10, HEAP, lsl #32
    // 0x8ed568: stur            x10, [fp, #-0x38]
    // 0x8ed56c: add             x11, x0, #1
    // 0x8ed570: stur            x11, [fp, #-0x30]
    // 0x8ed574: cmp             w10, NULL
    // 0x8ed578: b.ne            #0x8ed5ac
    // 0x8ed57c: mov             x0, x10
    // 0x8ed580: mov             x2, x4
    // 0x8ed584: r1 = Null
    //     0x8ed584: mov             x1, NULL
    // 0x8ed588: cmp             w2, NULL
    // 0x8ed58c: b.eq            #0x8ed5ac
    // 0x8ed590: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8ed590: ldur            w4, [x2, #0x17]
    // 0x8ed594: DecompressPointer r4
    //     0x8ed594: add             x4, x4, HEAP, lsl #32
    // 0x8ed598: r8 = X0
    //     0x8ed598: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8ed59c: LoadField: r9 = r4->field_7
    //     0x8ed59c: ldur            x9, [x4, #7]
    // 0x8ed5a0: r3 = Null
    //     0x8ed5a0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2faf0] Null
    //     0x8ed5a4: ldr             x3, [x3, #0xaf0]
    // 0x8ed5a8: blr             x9
    // 0x8ed5ac: ldur            x0, [fp, #-0x28]
    // 0x8ed5b0: ldur            x3, [fp, #-0x38]
    // 0x8ed5b4: LoadField: r4 = r3->field_7
    //     0x8ed5b4: ldur            w4, [x3, #7]
    // 0x8ed5b8: DecompressPointer r4
    //     0x8ed5b8: add             x4, x4, HEAP, lsl #32
    // 0x8ed5bc: stur            x4, [fp, #-0x58]
    // 0x8ed5c0: r1 = Null
    //     0x8ed5c0: mov             x1, NULL
    // 0x8ed5c4: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0x8ed5c4: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0x8ed5c8: ldr             x2, [x2, #0xad0]
    // 0x8ed5cc: r0 = verifiedLocale()
    //     0x8ed5cc: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0x8ed5d0: stur            x0, [fp, #-0x60]
    // 0x8ed5d4: r0 = DateFormat()
    //     0x8ed5d4: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0x8ed5d8: mov             x3, x0
    // 0x8ed5dc: ldur            x0, [fp, #-0x60]
    // 0x8ed5e0: stur            x3, [fp, #-0x68]
    // 0x8ed5e4: StoreField: r3->field_7 = r0
    //     0x8ed5e4: stur            w0, [x3, #7]
    // 0x8ed5e8: mov             x1, x3
    // 0x8ed5ec: r2 = "ddMmyyy"
    //     0x8ed5ec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fb00] "ddMmyyy"
    //     0x8ed5f0: ldr             x2, [x2, #0xb00]
    // 0x8ed5f4: r0 = addPattern()
    //     0x8ed5f4: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0x8ed5f8: ldur            x1, [fp, #-0x68]
    // 0x8ed5fc: ldur            x2, [fp, #-0x58]
    // 0x8ed600: r0 = format()
    //     0x8ed600: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0x8ed604: mov             x4, x0
    // 0x8ed608: ldur            x3, [fp, #-0x28]
    // 0x8ed60c: stur            x4, [fp, #-0x60]
    // 0x8ed610: r0 = LoadClassIdInstr(r3)
    //     0x8ed610: ldur            x0, [x3, #-1]
    //     0x8ed614: ubfx            x0, x0, #0xc, #0x14
    // 0x8ed618: mov             x1, x3
    // 0x8ed61c: mov             x2, x4
    // 0x8ed620: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ed620: sub             lr, x0, #0x114
    //     0x8ed624: ldr             lr, [x21, lr, lsl #3]
    //     0x8ed628: blr             lr
    // 0x8ed62c: cmp             w0, NULL
    // 0x8ed630: b.ne            #0x8ed694
    // 0x8ed634: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x8ed634: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ed638: ldr             x0, [x0]
    //     0x8ed63c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ed640: cmp             w0, w16
    //     0x8ed644: b.ne            #0x8ed650
    //     0x8ed648: ldr             x2, [PP, #0x528]  ; [pp+0x528] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x8ed64c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ed650: r1 = <EventCategory>
    //     0x8ed650: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb08] TypeArguments: <EventCategory>
    //     0x8ed654: ldr             x1, [x1, #0xb08]
    // 0x8ed658: stur            x0, [fp, #-0x68]
    // 0x8ed65c: r0 = AllocateGrowableArray()
    //     0x8ed65c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8ed660: mov             x3, x0
    // 0x8ed664: ldur            x0, [fp, #-0x68]
    // 0x8ed668: stur            x3, [fp, #-0x70]
    // 0x8ed66c: StoreField: r3->field_f = r0
    //     0x8ed66c: stur            w0, [x3, #0xf]
    // 0x8ed670: StoreField: r3->field_b = rZR
    //     0x8ed670: stur            wzr, [x3, #0xb]
    // 0x8ed674: ldur            x1, [fp, #-0x28]
    // 0x8ed678: ldur            x2, [fp, #-0x60]
    // 0x8ed67c: r0 = _hashCode()
    //     0x8ed67c: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x8ed680: ldur            x1, [fp, #-0x28]
    // 0x8ed684: ldur            x2, [fp, #-0x60]
    // 0x8ed688: ldur            x3, [fp, #-0x70]
    // 0x8ed68c: mov             x5, x0
    // 0x8ed690: r0 = _set()
    //     0x8ed690: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x8ed694: ldur            x3, [fp, #-0x28]
    // 0x8ed698: r0 = LoadClassIdInstr(r3)
    //     0x8ed698: ldur            x0, [x3, #-1]
    //     0x8ed69c: ubfx            x0, x0, #0xc, #0x14
    // 0x8ed6a0: mov             x1, x3
    // 0x8ed6a4: ldur            x2, [fp, #-0x60]
    // 0x8ed6a8: r0 = GDT[cid_x0 + -0x114]()
    //     0x8ed6a8: sub             lr, x0, #0x114
    //     0x8ed6ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8ed6b0: blr             lr
    // 0x8ed6b4: mov             x3, x0
    // 0x8ed6b8: stur            x3, [fp, #-0x68]
    // 0x8ed6bc: cmp             w3, NULL
    // 0x8ed6c0: b.eq            #0x8ed724
    // 0x8ed6c4: ldur            x4, [fp, #-0x38]
    // 0x8ed6c8: LoadField: r5 = r4->field_f
    //     0x8ed6c8: ldur            w5, [x4, #0xf]
    // 0x8ed6cc: DecompressPointer r5
    //     0x8ed6cc: add             x5, x5, HEAP, lsl #32
    // 0x8ed6d0: stur            x5, [fp, #-0x60]
    // 0x8ed6d4: r0 = LoadClassIdInstr(r3)
    //     0x8ed6d4: ldur            x0, [x3, #-1]
    //     0x8ed6d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8ed6dc: mov             x1, x3
    // 0x8ed6e0: mov             x2, x5
    // 0x8ed6e4: r0 = GDT[cid_x0 + 0xf20c]()
    //     0x8ed6e4: movz            x17, #0xf20c
    //     0x8ed6e8: add             lr, x0, x17
    //     0x8ed6ec: ldr             lr, [x21, lr, lsl #3]
    //     0x8ed6f0: blr             lr
    // 0x8ed6f4: tbz             w0, #4, #0x8ed724
    // 0x8ed6f8: ldur            x0, [fp, #-0x68]
    // 0x8ed6fc: r1 = LoadClassIdInstr(r0)
    //     0x8ed6fc: ldur            x1, [x0, #-1]
    //     0x8ed700: ubfx            x1, x1, #0xc, #0x14
    // 0x8ed704: ldur            x16, [fp, #-0x60]
    // 0x8ed708: stp             x16, x0, [SP]
    // 0x8ed70c: mov             x0, x1
    // 0x8ed710: r0 = GDT[cid_x0 + 0x13254]()
    //     0x8ed710: movz            x17, #0x3254
    //     0x8ed714: movk            x17, #0x1, lsl #16
    //     0x8ed718: add             lr, x0, x17
    //     0x8ed71c: ldr             lr, [x21, lr, lsl #3]
    //     0x8ed720: blr             lr
    // 0x8ed724: ldur            x0, [fp, #-0x38]
    // 0x8ed728: LoadField: r2 = r0->field_f
    //     0x8ed728: ldur            w2, [x0, #0xf]
    // 0x8ed72c: DecompressPointer r2
    //     0x8ed72c: add             x2, x2, HEAP, lsl #32
    // 0x8ed730: stur            x2, [fp, #-0x60]
    // 0x8ed734: r16 = Instance_EventCategory
    //     0x8ed734: add             x16, PP, #9, lsl #12  ; [pp+0x92d0] Obj!EventCategory@e310a1
    //     0x8ed738: ldr             x16, [x16, #0x2d0]
    // 0x8ed73c: cmp             w2, w16
    // 0x8ed740: b.ne            #0x8ed7c4
    // 0x8ed744: ldur            x2, [fp, #-0x10]
    // 0x8ed748: LoadField: r1 = r2->field_b
    //     0x8ed748: ldur            w1, [x2, #0xb]
    // 0x8ed74c: LoadField: r3 = r2->field_f
    //     0x8ed74c: ldur            w3, [x2, #0xf]
    // 0x8ed750: DecompressPointer r3
    //     0x8ed750: add             x3, x3, HEAP, lsl #32
    // 0x8ed754: LoadField: r4 = r3->field_b
    //     0x8ed754: ldur            w4, [x3, #0xb]
    // 0x8ed758: r3 = LoadInt32Instr(r1)
    //     0x8ed758: sbfx            x3, x1, #1, #0x1f
    // 0x8ed75c: stur            x3, [fp, #-0x78]
    // 0x8ed760: r1 = LoadInt32Instr(r4)
    //     0x8ed760: sbfx            x1, x4, #1, #0x1f
    // 0x8ed764: cmp             x3, x1
    // 0x8ed768: b.ne            #0x8ed774
    // 0x8ed76c: mov             x1, x2
    // 0x8ed770: r0 = _growToNextCapacity()
    //     0x8ed770: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8ed774: ldur            x3, [fp, #-0x10]
    // 0x8ed778: ldur            x2, [fp, #-0x78]
    // 0x8ed77c: add             x0, x2, #1
    // 0x8ed780: lsl             x1, x0, #1
    // 0x8ed784: StoreField: r3->field_b = r1
    //     0x8ed784: stur            w1, [x3, #0xb]
    // 0x8ed788: LoadField: r1 = r3->field_f
    //     0x8ed788: ldur            w1, [x3, #0xf]
    // 0x8ed78c: DecompressPointer r1
    //     0x8ed78c: add             x1, x1, HEAP, lsl #32
    // 0x8ed790: ldur            x0, [fp, #-0x38]
    // 0x8ed794: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8ed794: add             x25, x1, x2, lsl #2
    //     0x8ed798: add             x25, x25, #0xf
    //     0x8ed79c: str             w0, [x25]
    //     0x8ed7a0: tbz             w0, #0, #0x8ed7bc
    //     0x8ed7a4: ldurb           w16, [x1, #-1]
    //     0x8ed7a8: ldurb           w17, [x0, #-1]
    //     0x8ed7ac: and             x16, x17, x16, lsr #2
    //     0x8ed7b0: tst             x16, HEAP, lsr #32
    //     0x8ed7b4: b.eq            #0x8ed7bc
    //     0x8ed7b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ed7bc: ldur            x3, [fp, #-0x20]
    // 0x8ed7c0: b               #0x8ed8d4
    // 0x8ed7c4: ldur            x3, [fp, #-0x10]
    // 0x8ed7c8: ldur            x0, [fp, #-0x18]
    // 0x8ed7cc: LoadField: r1 = r0->field_b
    //     0x8ed7cc: ldur            w1, [x0, #0xb]
    // 0x8ed7d0: LoadField: r4 = r0->field_f
    //     0x8ed7d0: ldur            w4, [x0, #0xf]
    // 0x8ed7d4: DecompressPointer r4
    //     0x8ed7d4: add             x4, x4, HEAP, lsl #32
    // 0x8ed7d8: LoadField: r5 = r4->field_b
    //     0x8ed7d8: ldur            w5, [x4, #0xb]
    // 0x8ed7dc: r4 = LoadInt32Instr(r1)
    //     0x8ed7dc: sbfx            x4, x1, #1, #0x1f
    // 0x8ed7e0: stur            x4, [fp, #-0x78]
    // 0x8ed7e4: r1 = LoadInt32Instr(r5)
    //     0x8ed7e4: sbfx            x1, x5, #1, #0x1f
    // 0x8ed7e8: cmp             x4, x1
    // 0x8ed7ec: b.ne            #0x8ed7f8
    // 0x8ed7f0: mov             x1, x0
    // 0x8ed7f4: r0 = _growToNextCapacity()
    //     0x8ed7f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8ed7f8: ldur            x3, [fp, #-0x18]
    // 0x8ed7fc: ldur            x2, [fp, #-0x60]
    // 0x8ed800: ldur            x4, [fp, #-0x78]
    // 0x8ed804: add             x0, x4, #1
    // 0x8ed808: lsl             x1, x0, #1
    // 0x8ed80c: StoreField: r3->field_b = r1
    //     0x8ed80c: stur            w1, [x3, #0xb]
    // 0x8ed810: LoadField: r1 = r3->field_f
    //     0x8ed810: ldur            w1, [x3, #0xf]
    // 0x8ed814: DecompressPointer r1
    //     0x8ed814: add             x1, x1, HEAP, lsl #32
    // 0x8ed818: ldur            x0, [fp, #-0x38]
    // 0x8ed81c: ArrayStore: r1[r4] = r0  ; List_4
    //     0x8ed81c: add             x25, x1, x4, lsl #2
    //     0x8ed820: add             x25, x25, #0xf
    //     0x8ed824: str             w0, [x25]
    //     0x8ed828: tbz             w0, #0, #0x8ed844
    //     0x8ed82c: ldurb           w16, [x1, #-1]
    //     0x8ed830: ldurb           w17, [x0, #-1]
    //     0x8ed834: and             x16, x17, x16, lsr #2
    //     0x8ed838: tst             x16, HEAP, lsr #32
    //     0x8ed83c: b.eq            #0x8ed844
    //     0x8ed840: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ed844: r16 = Instance_EventCategory
    //     0x8ed844: add             x16, PP, #9, lsl #12  ; [pp+0x9180] Obj!EventCategory@e31101
    //     0x8ed848: ldr             x16, [x16, #0x180]
    // 0x8ed84c: cmp             w2, w16
    // 0x8ed850: b.ne            #0x8ed8d0
    // 0x8ed854: ldur            x0, [fp, #-0x20]
    // 0x8ed858: LoadField: r1 = r0->field_b
    //     0x8ed858: ldur            w1, [x0, #0xb]
    // 0x8ed85c: LoadField: r2 = r0->field_f
    //     0x8ed85c: ldur            w2, [x0, #0xf]
    // 0x8ed860: DecompressPointer r2
    //     0x8ed860: add             x2, x2, HEAP, lsl #32
    // 0x8ed864: LoadField: r4 = r2->field_b
    //     0x8ed864: ldur            w4, [x2, #0xb]
    // 0x8ed868: r2 = LoadInt32Instr(r1)
    //     0x8ed868: sbfx            x2, x1, #1, #0x1f
    // 0x8ed86c: stur            x2, [fp, #-0x78]
    // 0x8ed870: r1 = LoadInt32Instr(r4)
    //     0x8ed870: sbfx            x1, x4, #1, #0x1f
    // 0x8ed874: cmp             x2, x1
    // 0x8ed878: b.ne            #0x8ed884
    // 0x8ed87c: mov             x1, x0
    // 0x8ed880: r0 = _growToNextCapacity()
    //     0x8ed880: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8ed884: ldur            x3, [fp, #-0x20]
    // 0x8ed888: ldur            x2, [fp, #-0x78]
    // 0x8ed88c: add             x0, x2, #1
    // 0x8ed890: lsl             x1, x0, #1
    // 0x8ed894: StoreField: r3->field_b = r1
    //     0x8ed894: stur            w1, [x3, #0xb]
    // 0x8ed898: LoadField: r1 = r3->field_f
    //     0x8ed898: ldur            w1, [x3, #0xf]
    // 0x8ed89c: DecompressPointer r1
    //     0x8ed89c: add             x1, x1, HEAP, lsl #32
    // 0x8ed8a0: ldur            x0, [fp, #-0x58]
    // 0x8ed8a4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8ed8a4: add             x25, x1, x2, lsl #2
    //     0x8ed8a8: add             x25, x25, #0xf
    //     0x8ed8ac: str             w0, [x25]
    //     0x8ed8b0: tbz             w0, #0, #0x8ed8cc
    //     0x8ed8b4: ldurb           w16, [x1, #-1]
    //     0x8ed8b8: ldurb           w17, [x0, #-1]
    //     0x8ed8bc: and             x16, x17, x16, lsr #2
    //     0x8ed8c0: tst             x16, HEAP, lsr #32
    //     0x8ed8c4: b.eq            #0x8ed8cc
    //     0x8ed8c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ed8cc: b               #0x8ed8d4
    // 0x8ed8d0: ldur            x3, [fp, #-0x20]
    // 0x8ed8d4: ldur            x0, [fp, #-0x30]
    // 0x8ed8d8: mov             x7, x3
    // 0x8ed8dc: ldur            x3, [fp, #-0x50]
    // 0x8ed8e0: ldur            x4, [fp, #-0x48]
    // 0x8ed8e4: ldur            x5, [fp, #-0x40]
    // 0x8ed8e8: b               #0x8ed524
    // 0x8ed8ec: ldur            x0, [fp, #-8]
    // 0x8ed8f0: mov             x3, x7
    // 0x8ed8f4: LoadField: r1 = r0->field_43
    //     0x8ed8f4: ldur            w1, [x0, #0x43]
    // 0x8ed8f8: DecompressPointer r1
    //     0x8ed8f8: add             x1, x1, HEAP, lsl #32
    // 0x8ed8fc: ldur            x2, [fp, #-0x28]
    // 0x8ed900: r0 = value=()
    //     0x8ed900: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ed904: ldur            x0, [fp, #-8]
    // 0x8ed908: LoadField: r1 = r0->field_3b
    //     0x8ed908: ldur            w1, [x0, #0x3b]
    // 0x8ed90c: DecompressPointer r1
    //     0x8ed90c: add             x1, x1, HEAP, lsl #32
    // 0x8ed910: r16 = <DateTime>
    //     0x8ed910: add             x16, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x8ed914: ldr             x16, [x16, #0xdd8]
    // 0x8ed918: stp             x1, x16, [SP, #8]
    // 0x8ed91c: ldur            x16, [fp, #-0x20]
    // 0x8ed920: str             x16, [SP]
    // 0x8ed924: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ed924: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ed928: r0 = ListExtension.assignAll()
    //     0x8ed928: bl              #0x7daa20  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.assignAll
    // 0x8ed92c: ldur            x0, [fp, #-8]
    // 0x8ed930: LoadField: r1 = r0->field_37
    //     0x8ed930: ldur            w1, [x0, #0x37]
    // 0x8ed934: DecompressPointer r1
    //     0x8ed934: add             x1, x1, HEAP, lsl #32
    // 0x8ed938: r16 = <Event>
    //     0x8ed938: add             x16, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x8ed93c: ldr             x16, [x16, #0xd0]
    // 0x8ed940: stp             x1, x16, [SP, #8]
    // 0x8ed944: ldur            x16, [fp, #-0x10]
    // 0x8ed948: str             x16, [SP]
    // 0x8ed94c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ed94c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ed950: r0 = ListExtension.assignAll()
    //     0x8ed950: bl              #0x7daa20  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.assignAll
    // 0x8ed954: ldur            x0, [fp, #-8]
    // 0x8ed958: LoadField: r1 = r0->field_3f
    //     0x8ed958: ldur            w1, [x0, #0x3f]
    // 0x8ed95c: DecompressPointer r1
    //     0x8ed95c: add             x1, x1, HEAP, lsl #32
    // 0x8ed960: r16 = <Event>
    //     0x8ed960: add             x16, PP, #9, lsl #12  ; [pp+0x90d0] TypeArguments: <Event>
    //     0x8ed964: ldr             x16, [x16, #0xd0]
    // 0x8ed968: stp             x1, x16, [SP, #8]
    // 0x8ed96c: ldur            x16, [fp, #-0x18]
    // 0x8ed970: str             x16, [SP]
    // 0x8ed974: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8ed974: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8ed978: r0 = ListExtension.assignAll()
    //     0x8ed978: bl              #0x7daa20  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.assignAll
    // 0x8ed97c: r0 = Null
    //     0x8ed97c: mov             x0, NULL
    // 0x8ed980: LeaveFrame
    //     0x8ed980: mov             SP, fp
    //     0x8ed984: ldp             fp, lr, [SP], #0x10
    // 0x8ed988: ret
    //     0x8ed988: ret             
    // 0x8ed98c: mov             x0, x3
    // 0x8ed990: r0 = ConcurrentModificationError()
    //     0x8ed990: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8ed994: mov             x1, x0
    // 0x8ed998: ldur            x0, [fp, #-0x50]
    // 0x8ed99c: StoreField: r1->field_b = r0
    //     0x8ed99c: stur            w0, [x1, #0xb]
    // 0x8ed9a0: mov             x0, x1
    // 0x8ed9a4: r0 = Throw()
    //     0x8ed9a4: bl              #0xec04b8  ; ThrowStub
    // 0x8ed9a8: brk             #0
    // 0x8ed9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed9ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed9b0: b               #0x8ed490
    // 0x8ed9b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed9b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed9b8: b               #0x8ed53c
  }
  _ checkUpdate(/* No info */) async {
    // ** addr: 0x8eda34, size: 0x54
    // 0x8eda34: EnterFrame
    //     0x8eda34: stp             fp, lr, [SP, #-0x10]!
    //     0x8eda38: mov             fp, SP
    // 0x8eda3c: AllocStack(0x10)
    //     0x8eda3c: sub             SP, SP, #0x10
    // 0x8eda40: SetupParameters(CalendarController this /* r1 => r1, fp-0x10 */)
    //     0x8eda40: stur            NULL, [fp, #-8]
    //     0x8eda44: stur            x1, [fp, #-0x10]
    // 0x8eda48: CheckStackOverflow
    //     0x8eda48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eda4c: cmp             SP, x16
    //     0x8eda50: b.ls            #0x8eda80
    // 0x8eda54: InitAsync() -> Future<void?>
    //     0x8eda54: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8eda58: bl              #0x661298  ; InitAsyncStub
    // 0x8eda5c: ldur            x0, [fp, #-0x10]
    // 0x8eda60: LoadField: r1 = r0->field_23
    //     0x8eda60: ldur            w1, [x0, #0x23]
    // 0x8eda64: DecompressPointer r1
    //     0x8eda64: add             x1, x1, HEAP, lsl #32
    // 0x8eda68: r0 = update()
    //     0x8eda68: bl              #0x8eda88  ; [package:nuonline/app/data/repositories/calendar_repository.dart] CalendarRepository::update
    // 0x8eda6c: mov             x1, x0
    // 0x8eda70: stur            x1, [fp, #-0x10]
    // 0x8eda74: r0 = Await()
    //     0x8eda74: bl              #0x661044  ; AwaitStub
    // 0x8eda78: r0 = Null
    //     0x8eda78: mov             x0, NULL
    // 0x8eda7c: r0 = ReturnAsyncNotFuture()
    //     0x8eda7c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8eda80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eda80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eda84: b               #0x8eda54
  }
  _ setCalenderHeader(/* No info */) {
    // ** addr: 0x8eed60, size: 0x524
    // 0x8eed60: EnterFrame
    //     0x8eed60: stp             fp, lr, [SP, #-0x10]!
    //     0x8eed64: mov             fp, SP
    // 0x8eed68: AllocStack(0x50)
    //     0x8eed68: sub             SP, SP, #0x50
    // 0x8eed6c: SetupParameters(CalendarController this /* r1 => r0, fp-0x8 */)
    //     0x8eed6c: mov             x0, x1
    //     0x8eed70: stur            x1, [fp, #-8]
    // 0x8eed74: CheckStackOverflow
    //     0x8eed74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eed78: cmp             SP, x16
    //     0x8eed7c: b.ls            #0x8ef24c
    // 0x8eed80: LoadField: r1 = r0->field_47
    //     0x8eed80: ldur            w1, [x0, #0x47]
    // 0x8eed84: DecompressPointer r1
    //     0x8eed84: add             x1, x1, HEAP, lsl #32
    // 0x8eed88: r0 = value()
    //     0x8eed88: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8eed8c: mov             x2, x0
    // 0x8eed90: ldur            x0, [fp, #-8]
    // 0x8eed94: stur            x2, [fp, #-0x18]
    // 0x8eed98: LoadField: r3 = r0->field_4f
    //     0x8eed98: ldur            w3, [x0, #0x4f]
    // 0x8eed9c: DecompressPointer r3
    //     0x8eed9c: add             x3, x3, HEAP, lsl #32
    // 0x8eeda0: mov             x1, x2
    // 0x8eeda4: stur            x3, [fp, #-0x10]
    // 0x8eeda8: r0 = DateTimeExtensions.mY()
    //     0x8eeda8: bl              #0x83a2fc  ; [package:nuonline/common/extensions/date_time_extension.dart] ::DateTimeExtensions.mY
    // 0x8eedac: ldur            x1, [fp, #-0x10]
    // 0x8eedb0: mov             x2, x0
    // 0x8eedb4: r0 = value=()
    //     0x8eedb4: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8eedb8: ldur            x2, [fp, #-0x18]
    // 0x8eedbc: r0 = LoadClassIdInstr(r2)
    //     0x8eedbc: ldur            x0, [x2, #-1]
    //     0x8eedc0: ubfx            x0, x0, #0xc, #0x14
    // 0x8eedc4: mov             x1, x2
    // 0x8eedc8: r0 = GDT[cid_x0 + -0xff6]()
    //     0x8eedc8: sub             lr, x0, #0xff6
    //     0x8eedcc: ldr             lr, [x21, lr, lsl #3]
    //     0x8eedd0: blr             lr
    // 0x8eedd4: mov             x3, x0
    // 0x8eedd8: ldur            x2, [fp, #-0x18]
    // 0x8eeddc: stur            x3, [fp, #-0x20]
    // 0x8eede0: r0 = LoadClassIdInstr(r2)
    //     0x8eede0: ldur            x0, [x2, #-1]
    //     0x8eede4: ubfx            x0, x0, #0xc, #0x14
    // 0x8eede8: mov             x1, x2
    // 0x8eedec: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8eedec: sub             lr, x0, #0xfff
    //     0x8eedf0: ldr             lr, [x21, lr, lsl #3]
    //     0x8eedf4: blr             lr
    // 0x8eedf8: mov             x2, x0
    // 0x8eedfc: r0 = BoxInt64Instr(r2)
    //     0x8eedfc: sbfiz           x0, x2, #1, #0x1f
    //     0x8eee00: cmp             x2, x0, asr #1
    //     0x8eee04: b.eq            #0x8eee10
    //     0x8eee08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eee0c: stur            x2, [x0, #7]
    // 0x8eee10: stur            x0, [fp, #-0x10]
    // 0x8eee14: r0 = DateTime()
    //     0x8eee14: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8eee18: stur            x0, [fp, #-0x28]
    // 0x8eee1c: ldur            x16, [fp, #-0x10]
    // 0x8eee20: r30 = 2
    //     0x8eee20: movz            lr, #0x2
    // 0x8eee24: stp             lr, x16, [SP]
    // 0x8eee28: mov             x1, x0
    // 0x8eee2c: ldur            x2, [fp, #-0x20]
    // 0x8eee30: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x8eee30: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x8eee34: ldr             x4, [x4, #0xe00]
    // 0x8eee38: r0 = DateTime()
    //     0x8eee38: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x8eee3c: ldur            x2, [fp, #-0x18]
    // 0x8eee40: r0 = LoadClassIdInstr(r2)
    //     0x8eee40: ldur            x0, [x2, #-1]
    //     0x8eee44: ubfx            x0, x0, #0xc, #0x14
    // 0x8eee48: mov             x1, x2
    // 0x8eee4c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8eee4c: sub             lr, x0, #0xfff
    //     0x8eee50: ldr             lr, [x21, lr, lsl #3]
    //     0x8eee54: blr             lr
    // 0x8eee58: cmp             x0, #0xc
    // 0x8eee5c: b.ne            #0x8eeeb0
    // 0x8eee60: ldur            x2, [fp, #-0x18]
    // 0x8eee64: r0 = LoadClassIdInstr(r2)
    //     0x8eee64: ldur            x0, [x2, #-1]
    //     0x8eee68: ubfx            x0, x0, #0xc, #0x14
    // 0x8eee6c: mov             x1, x2
    // 0x8eee70: r0 = GDT[cid_x0 + -0xff6]()
    //     0x8eee70: sub             lr, x0, #0xff6
    //     0x8eee74: ldr             lr, [x21, lr, lsl #3]
    //     0x8eee78: blr             lr
    // 0x8eee7c: add             x2, x0, #1
    // 0x8eee80: stur            x2, [fp, #-0x20]
    // 0x8eee84: r0 = DateTime()
    //     0x8eee84: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8eee88: stur            x0, [fp, #-0x10]
    // 0x8eee8c: r16 = 2
    //     0x8eee8c: movz            x16, #0x2
    // 0x8eee90: stp             xzr, x16, [SP]
    // 0x8eee94: mov             x1, x0
    // 0x8eee98: ldur            x2, [fp, #-0x20]
    // 0x8eee9c: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x8eee9c: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x8eeea0: ldr             x4, [x4, #0xe00]
    // 0x8eeea4: r0 = DateTime()
    //     0x8eeea4: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x8eeea8: ldur            x3, [fp, #-0x10]
    // 0x8eeeac: b               #0x8eef30
    // 0x8eeeb0: ldur            x2, [fp, #-0x18]
    // 0x8eeeb4: r0 = LoadClassIdInstr(r2)
    //     0x8eeeb4: ldur            x0, [x2, #-1]
    //     0x8eeeb8: ubfx            x0, x0, #0xc, #0x14
    // 0x8eeebc: mov             x1, x2
    // 0x8eeec0: r0 = GDT[cid_x0 + -0xff6]()
    //     0x8eeec0: sub             lr, x0, #0xff6
    //     0x8eeec4: ldr             lr, [x21, lr, lsl #3]
    //     0x8eeec8: blr             lr
    // 0x8eeecc: mov             x2, x0
    // 0x8eeed0: ldur            x1, [fp, #-0x18]
    // 0x8eeed4: stur            x2, [fp, #-0x20]
    // 0x8eeed8: r0 = LoadClassIdInstr(r1)
    //     0x8eeed8: ldur            x0, [x1, #-1]
    //     0x8eeedc: ubfx            x0, x0, #0xc, #0x14
    // 0x8eeee0: r0 = GDT[cid_x0 + -0xfff]()
    //     0x8eeee0: sub             lr, x0, #0xfff
    //     0x8eeee4: ldr             lr, [x21, lr, lsl #3]
    //     0x8eeee8: blr             lr
    // 0x8eeeec: add             x2, x0, #1
    // 0x8eeef0: r0 = BoxInt64Instr(r2)
    //     0x8eeef0: sbfiz           x0, x2, #1, #0x1f
    //     0x8eeef4: cmp             x2, x0, asr #1
    //     0x8eeef8: b.eq            #0x8eef04
    //     0x8eeefc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eef00: stur            x2, [x0, #7]
    // 0x8eef04: stur            x0, [fp, #-0x10]
    // 0x8eef08: r0 = DateTime()
    //     0x8eef08: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8eef0c: stur            x0, [fp, #-0x18]
    // 0x8eef10: ldur            x16, [fp, #-0x10]
    // 0x8eef14: stp             xzr, x16, [SP]
    // 0x8eef18: mov             x1, x0
    // 0x8eef1c: ldur            x2, [fp, #-0x20]
    // 0x8eef20: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0x8eef20: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0x8eef24: ldr             x4, [x4, #0xe00]
    // 0x8eef28: r0 = DateTime()
    //     0x8eef28: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0x8eef2c: ldur            x3, [fp, #-0x18]
    // 0x8eef30: ldur            x0, [fp, #-8]
    // 0x8eef34: stur            x3, [fp, #-0x18]
    // 0x8eef38: LoadField: r4 = r0->field_27
    //     0x8eef38: ldur            w4, [x0, #0x27]
    // 0x8eef3c: DecompressPointer r4
    //     0x8eef3c: add             x4, x4, HEAP, lsl #32
    // 0x8eef40: mov             x1, x4
    // 0x8eef44: ldur            x2, [fp, #-0x28]
    // 0x8eef48: stur            x4, [fp, #-0x10]
    // 0x8eef4c: r0 = from()
    //     0x8eef4c: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0x8eef50: ldur            x1, [fp, #-0x28]
    // 0x8eef54: r2 = Instance_Duration
    //     0x8eef54: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fae0] Obj!Duration@e3a1a1
    //     0x8eef58: ldr             x2, [x2, #0xae0]
    // 0x8eef5c: stur            x0, [fp, #-0x28]
    // 0x8eef60: r0 = add()
    //     0x8eef60: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x8eef64: ldur            x1, [fp, #-0x10]
    // 0x8eef68: mov             x2, x0
    // 0x8eef6c: r0 = from()
    //     0x8eef6c: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0x8eef70: ldur            x1, [fp, #-0x10]
    // 0x8eef74: ldur            x2, [fp, #-0x18]
    // 0x8eef78: stur            x0, [fp, #-0x10]
    // 0x8eef7c: r0 = from()
    //     0x8eef7c: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0x8eef80: stur            x0, [fp, #-0x30]
    // 0x8eef84: LoadField: r1 = r0->field_f
    //     0x8eef84: ldur            w1, [x0, #0xf]
    // 0x8eef88: DecompressPointer r1
    //     0x8eef88: add             x1, x1, HEAP, lsl #32
    // 0x8eef8c: r16 = Sentinel
    //     0x8eef8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eef90: cmp             w1, w16
    // 0x8eef94: b.eq            #0x8ef254
    // 0x8eef98: ldur            x2, [fp, #-0x28]
    // 0x8eef9c: LoadField: r3 = r2->field_f
    //     0x8eef9c: ldur            w3, [x2, #0xf]
    // 0x8eefa0: DecompressPointer r3
    //     0x8eefa0: add             x3, x3, HEAP, lsl #32
    // 0x8eefa4: r16 = Sentinel
    //     0x8eefa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eefa8: cmp             w3, w16
    // 0x8eefac: b.eq            #0x8ef260
    // 0x8eefb0: r4 = LoadInt32Instr(r1)
    //     0x8eefb0: sbfx            x4, x1, #1, #0x1f
    //     0x8eefb4: tbz             w1, #0, #0x8eefbc
    //     0x8eefb8: ldur            x4, [x1, #7]
    // 0x8eefbc: r1 = LoadInt32Instr(r3)
    //     0x8eefbc: sbfx            x1, x3, #1, #0x1f
    //     0x8eefc0: tbz             w3, #0, #0x8eefc8
    //     0x8eefc4: ldur            x1, [x3, #7]
    // 0x8eefc8: sub             x3, x4, x1
    // 0x8eefcc: LoadField: r1 = r2->field_13
    //     0x8eefcc: ldur            w1, [x2, #0x13]
    // 0x8eefd0: DecompressPointer r1
    //     0x8eefd0: add             x1, x1, HEAP, lsl #32
    // 0x8eefd4: r16 = Sentinel
    //     0x8eefd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eefd8: cmp             w1, w16
    // 0x8eefdc: b.eq            #0x8ef26c
    // 0x8eefe0: LoadField: r4 = r0->field_13
    //     0x8eefe0: ldur            w4, [x0, #0x13]
    // 0x8eefe4: DecompressPointer r4
    //     0x8eefe4: add             x4, x4, HEAP, lsl #32
    // 0x8eefe8: r16 = Sentinel
    //     0x8eefe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eefec: cmp             w4, w16
    // 0x8eeff0: b.eq            #0x8ef278
    // 0x8eeff4: r5 = LoadInt32Instr(r1)
    //     0x8eeff4: sbfx            x5, x1, #1, #0x1f
    //     0x8eeff8: tbz             w1, #0, #0x8ef000
    //     0x8eeffc: ldur            x5, [x1, #7]
    // 0x8ef000: r1 = LoadInt32Instr(r4)
    //     0x8ef000: sbfx            x1, x4, #1, #0x1f
    //     0x8ef004: tbz             w4, #0, #0x8ef00c
    //     0x8ef008: ldur            x1, [x4, #7]
    // 0x8ef00c: cmp             x5, x1
    // 0x8ef010: b.ne            #0x8ef19c
    // 0x8ef014: cbnz            x3, #0x8ef040
    // 0x8ef018: ldur            x1, [fp, #-8]
    // 0x8ef01c: LoadField: r2 = r1->field_53
    //     0x8ef01c: ldur            w2, [x1, #0x53]
    // 0x8ef020: DecompressPointer r2
    //     0x8ef020: add             x2, x2, HEAP, lsl #32
    // 0x8ef024: mov             x1, x0
    // 0x8ef028: stur            x2, [fp, #-0x18]
    // 0x8ef02c: r0 = mY()
    //     0x8ef02c: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef030: ldur            x1, [fp, #-0x18]
    // 0x8ef034: mov             x2, x0
    // 0x8ef038: r0 = value=()
    //     0x8ef038: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ef03c: b               #0x8ef23c
    // 0x8ef040: ldur            x1, [fp, #-8]
    // 0x8ef044: cmp             x3, #2
    // 0x8ef048: b.ne            #0x8ef110
    // 0x8ef04c: LoadField: r3 = r1->field_53
    //     0x8ef04c: ldur            w3, [x1, #0x53]
    // 0x8ef050: DecompressPointer r3
    //     0x8ef050: add             x3, x3, HEAP, lsl #32
    // 0x8ef054: mov             x1, x2
    // 0x8ef058: stur            x3, [fp, #-0x18]
    // 0x8ef05c: r0 = mMMM()
    //     0x8ef05c: bl              #0x8ef284  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mMMM
    // 0x8ef060: r1 = Null
    //     0x8ef060: mov             x1, NULL
    // 0x8ef064: r2 = 10
    //     0x8ef064: movz            x2, #0xa
    // 0x8ef068: stur            x0, [fp, #-0x38]
    // 0x8ef06c: r0 = AllocateArray()
    //     0x8ef06c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ef070: mov             x2, x0
    // 0x8ef074: ldur            x0, [fp, #-0x38]
    // 0x8ef078: stur            x2, [fp, #-0x40]
    // 0x8ef07c: StoreField: r2->field_f = r0
    //     0x8ef07c: stur            w0, [x2, #0xf]
    // 0x8ef080: r16 = ", "
    //     0x8ef080: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x8ef084: StoreField: r2->field_13 = r16
    //     0x8ef084: stur            w16, [x2, #0x13]
    // 0x8ef088: ldur            x1, [fp, #-0x10]
    // 0x8ef08c: r0 = mMMM()
    //     0x8ef08c: bl              #0x8ef284  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mMMM
    // 0x8ef090: ldur            x1, [fp, #-0x40]
    // 0x8ef094: ArrayStore: r1[2] = r0  ; List_4
    //     0x8ef094: add             x25, x1, #0x17
    //     0x8ef098: str             w0, [x25]
    //     0x8ef09c: tbz             w0, #0, #0x8ef0b8
    //     0x8ef0a0: ldurb           w16, [x1, #-1]
    //     0x8ef0a4: ldurb           w17, [x0, #-1]
    //     0x8ef0a8: and             x16, x17, x16, lsr #2
    //     0x8ef0ac: tst             x16, HEAP, lsr #32
    //     0x8ef0b0: b.eq            #0x8ef0b8
    //     0x8ef0b4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ef0b8: ldur            x0, [fp, #-0x40]
    // 0x8ef0bc: r16 = ", "
    //     0x8ef0bc: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x8ef0c0: StoreField: r0->field_1b = r16
    //     0x8ef0c0: stur            w16, [x0, #0x1b]
    // 0x8ef0c4: ldur            x1, [fp, #-0x30]
    // 0x8ef0c8: r0 = mY()
    //     0x8ef0c8: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef0cc: ldur            x1, [fp, #-0x40]
    // 0x8ef0d0: ArrayStore: r1[4] = r0  ; List_4
    //     0x8ef0d0: add             x25, x1, #0x1f
    //     0x8ef0d4: str             w0, [x25]
    //     0x8ef0d8: tbz             w0, #0, #0x8ef0f4
    //     0x8ef0dc: ldurb           w16, [x1, #-1]
    //     0x8ef0e0: ldurb           w17, [x0, #-1]
    //     0x8ef0e4: and             x16, x17, x16, lsr #2
    //     0x8ef0e8: tst             x16, HEAP, lsr #32
    //     0x8ef0ec: b.eq            #0x8ef0f4
    //     0x8ef0f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ef0f4: ldur            x16, [fp, #-0x40]
    // 0x8ef0f8: str             x16, [SP]
    // 0x8ef0fc: r0 = _interpolate()
    //     0x8ef0fc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ef100: ldur            x1, [fp, #-0x18]
    // 0x8ef104: mov             x2, x0
    // 0x8ef108: r0 = value=()
    //     0x8ef108: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ef10c: b               #0x8ef23c
    // 0x8ef110: LoadField: r0 = r1->field_53
    //     0x8ef110: ldur            w0, [x1, #0x53]
    // 0x8ef114: DecompressPointer r0
    //     0x8ef114: add             x0, x0, HEAP, lsl #32
    // 0x8ef118: mov             x1, x2
    // 0x8ef11c: stur            x0, [fp, #-0x10]
    // 0x8ef120: r0 = mMMM()
    //     0x8ef120: bl              #0x8ef284  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mMMM
    // 0x8ef124: r1 = Null
    //     0x8ef124: mov             x1, NULL
    // 0x8ef128: r2 = 6
    //     0x8ef128: movz            x2, #0x6
    // 0x8ef12c: stur            x0, [fp, #-0x18]
    // 0x8ef130: r0 = AllocateArray()
    //     0x8ef130: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ef134: mov             x2, x0
    // 0x8ef138: ldur            x0, [fp, #-0x18]
    // 0x8ef13c: stur            x2, [fp, #-0x38]
    // 0x8ef140: StoreField: r2->field_f = r0
    //     0x8ef140: stur            w0, [x2, #0xf]
    // 0x8ef144: r16 = " - "
    //     0x8ef144: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c180] " - "
    //     0x8ef148: ldr             x16, [x16, #0x180]
    // 0x8ef14c: StoreField: r2->field_13 = r16
    //     0x8ef14c: stur            w16, [x2, #0x13]
    // 0x8ef150: ldur            x1, [fp, #-0x30]
    // 0x8ef154: r0 = mY()
    //     0x8ef154: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef158: ldur            x1, [fp, #-0x38]
    // 0x8ef15c: ArrayStore: r1[2] = r0  ; List_4
    //     0x8ef15c: add             x25, x1, #0x17
    //     0x8ef160: str             w0, [x25]
    //     0x8ef164: tbz             w0, #0, #0x8ef180
    //     0x8ef168: ldurb           w16, [x1, #-1]
    //     0x8ef16c: ldurb           w17, [x0, #-1]
    //     0x8ef170: and             x16, x17, x16, lsr #2
    //     0x8ef174: tst             x16, HEAP, lsr #32
    //     0x8ef178: b.eq            #0x8ef180
    //     0x8ef17c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ef180: ldur            x16, [fp, #-0x38]
    // 0x8ef184: str             x16, [SP]
    // 0x8ef188: r0 = _interpolate()
    //     0x8ef188: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ef18c: ldur            x1, [fp, #-0x10]
    // 0x8ef190: mov             x2, x0
    // 0x8ef194: r0 = value=()
    //     0x8ef194: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ef198: b               #0x8ef23c
    // 0x8ef19c: ldur            x1, [fp, #-8]
    // 0x8ef1a0: LoadField: r0 = r1->field_53
    //     0x8ef1a0: ldur            w0, [x1, #0x53]
    // 0x8ef1a4: DecompressPointer r0
    //     0x8ef1a4: add             x0, x0, HEAP, lsl #32
    // 0x8ef1a8: stur            x0, [fp, #-0x10]
    // 0x8ef1ac: cbnz            x3, #0x8ef1c0
    // 0x8ef1b0: ldur            x1, [fp, #-0x30]
    // 0x8ef1b4: r0 = mY()
    //     0x8ef1b4: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef1b8: mov             x2, x0
    // 0x8ef1bc: b               #0x8ef234
    // 0x8ef1c0: mov             x1, x2
    // 0x8ef1c4: r0 = mY()
    //     0x8ef1c4: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef1c8: r1 = Null
    //     0x8ef1c8: mov             x1, NULL
    // 0x8ef1cc: r2 = 6
    //     0x8ef1cc: movz            x2, #0x6
    // 0x8ef1d0: stur            x0, [fp, #-8]
    // 0x8ef1d4: r0 = AllocateArray()
    //     0x8ef1d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ef1d8: mov             x2, x0
    // 0x8ef1dc: ldur            x0, [fp, #-8]
    // 0x8ef1e0: stur            x2, [fp, #-0x18]
    // 0x8ef1e4: StoreField: r2->field_f = r0
    //     0x8ef1e4: stur            w0, [x2, #0xf]
    // 0x8ef1e8: r16 = " - "
    //     0x8ef1e8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c180] " - "
    //     0x8ef1ec: ldr             x16, [x16, #0x180]
    // 0x8ef1f0: StoreField: r2->field_13 = r16
    //     0x8ef1f0: stur            w16, [x2, #0x13]
    // 0x8ef1f4: ldur            x1, [fp, #-0x30]
    // 0x8ef1f8: r0 = mY()
    //     0x8ef1f8: bl              #0x83a3ec  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::mY
    // 0x8ef1fc: ldur            x1, [fp, #-0x18]
    // 0x8ef200: ArrayStore: r1[2] = r0  ; List_4
    //     0x8ef200: add             x25, x1, #0x17
    //     0x8ef204: str             w0, [x25]
    //     0x8ef208: tbz             w0, #0, #0x8ef224
    //     0x8ef20c: ldurb           w16, [x1, #-1]
    //     0x8ef210: ldurb           w17, [x0, #-1]
    //     0x8ef214: and             x16, x17, x16, lsr #2
    //     0x8ef218: tst             x16, HEAP, lsr #32
    //     0x8ef21c: b.eq            #0x8ef224
    //     0x8ef220: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8ef224: ldur            x16, [fp, #-0x18]
    // 0x8ef228: str             x16, [SP]
    // 0x8ef22c: r0 = _interpolate()
    //     0x8ef22c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ef230: mov             x2, x0
    // 0x8ef234: ldur            x1, [fp, #-0x10]
    // 0x8ef238: r0 = value=()
    //     0x8ef238: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x8ef23c: r0 = Null
    //     0x8ef23c: mov             x0, NULL
    // 0x8ef240: LeaveFrame
    //     0x8ef240: mov             SP, fp
    //     0x8ef244: ldp             fp, lr, [SP], #0x10
    // 0x8ef248: ret
    //     0x8ef248: ret             
    // 0x8ef24c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef24c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef250: b               #0x8eed80
    // 0x8ef254: r9 = hMonth
    //     0x8ef254: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x8ef258: ldr             x9, [x9, #0x278]
    // 0x8ef25c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ef25c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8ef260: r9 = hMonth
    //     0x8ef260: add             x9, PP, #8, lsl #12  ; [pp+0x8278] Field <NHijriCalendar.hMonth>: late (offset: 0x10)
    //     0x8ef264: ldr             x9, [x9, #0x278]
    // 0x8ef268: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ef268: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8ef26c: r9 = hYear
    //     0x8ef26c: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x8ef270: ldr             x9, [x9, #0x270]
    // 0x8ef274: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ef274: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8ef278: r9 = hYear
    //     0x8ef278: add             x9, PP, #9, lsl #12  ; [pp+0x9270] Field <NHijriCalendar.hYear>: late (offset: 0x14)
    //     0x8ef27c: ldr             x9, [x9, #0x270]
    // 0x8ef280: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ef280: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x8ef3bc, size: 0x5c
    // 0x8ef3bc: EnterFrame
    //     0x8ef3bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8ef3c0: mov             fp, SP
    // 0x8ef3c4: AllocStack(0x8)
    //     0x8ef3c4: sub             SP, SP, #8
    // 0x8ef3c8: SetupParameters()
    //     0x8ef3c8: ldr             x0, [fp, #0x18]
    //     0x8ef3cc: ldur            w2, [x0, #0x17]
    //     0x8ef3d0: add             x2, x2, HEAP, lsl #32
    //     0x8ef3d4: stur            x2, [fp, #-8]
    // 0x8ef3d8: CheckStackOverflow
    //     0x8ef3d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ef3dc: cmp             SP, x16
    //     0x8ef3e0: b.ls            #0x8ef410
    // 0x8ef3e4: LoadField: r1 = r2->field_f
    //     0x8ef3e4: ldur            w1, [x2, #0xf]
    // 0x8ef3e8: DecompressPointer r1
    //     0x8ef3e8: add             x1, x1, HEAP, lsl #32
    // 0x8ef3ec: r0 = setCalenderHeader()
    //     0x8ef3ec: bl              #0x8eed60  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::setCalenderHeader
    // 0x8ef3f0: ldur            x0, [fp, #-8]
    // 0x8ef3f4: LoadField: r1 = r0->field_f
    //     0x8ef3f4: ldur            w1, [x0, #0xf]
    // 0x8ef3f8: DecompressPointer r1
    //     0x8ef3f8: add             x1, x1, HEAP, lsl #32
    // 0x8ef3fc: r0 = loadEvent()
    //     0x8ef3fc: bl              #0x8ed330  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::loadEvent
    // 0x8ef400: r0 = Null
    //     0x8ef400: mov             x0, NULL
    // 0x8ef404: LeaveFrame
    //     0x8ef404: mov             SP, fp
    //     0x8ef408: ldp             fp, lr, [SP], #0x10
    // 0x8ef40c: ret
    //     0x8ef40c: ret             
    // 0x8ef410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ef410: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ef414: b               #0x8ef3e4
  }
  _ onClose(/* No info */) {
    // ** addr: 0x927120, size: 0x54
    // 0x927120: EnterFrame
    //     0x927120: stp             fp, lr, [SP, #-0x10]!
    //     0x927124: mov             fp, SP
    // 0x927128: CheckStackOverflow
    //     0x927128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92712c: cmp             SP, x16
    //     0x927130: b.ls            #0x927160
    // 0x927134: LoadField: r0 = r1->field_57
    //     0x927134: ldur            w0, [x1, #0x57]
    // 0x927138: DecompressPointer r0
    //     0x927138: add             x0, x0, HEAP, lsl #32
    // 0x92713c: r16 = Sentinel
    //     0x92713c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x927140: cmp             w0, w16
    // 0x927144: b.eq            #0x927168
    // 0x927148: mov             x1, x0
    // 0x92714c: r0 = dispose()
    //     0x92714c: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0x927150: r0 = Null
    //     0x927150: mov             x0, NULL
    // 0x927154: LeaveFrame
    //     0x927154: mov             SP, fp
    //     0x927158: ldp             fp, lr, [SP], #0x10
    // 0x92715c: ret
    //     0x92715c: ret             
    // 0x927160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927164: b               #0x927134
    // 0x927168: r9 = _calendarController
    //     0x927168: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Field <CalendarController._calendarController@1913449503>: late (offset: 0x58)
    //     0x92716c: ldr             x9, [x9, #0xb20]
    // 0x927170: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x927170: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void onDaySelected(dynamic, DateTime, DateTime) {
    // ** addr: 0xa50060, size: 0x40
    // 0xa50060: EnterFrame
    //     0xa50060: stp             fp, lr, [SP, #-0x10]!
    //     0xa50064: mov             fp, SP
    // 0xa50068: ldr             x0, [fp, #0x20]
    // 0xa5006c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa5006c: ldur            w1, [x0, #0x17]
    // 0xa50070: DecompressPointer r1
    //     0xa50070: add             x1, x1, HEAP, lsl #32
    // 0xa50074: CheckStackOverflow
    //     0xa50074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50078: cmp             SP, x16
    //     0xa5007c: b.ls            #0xa50098
    // 0xa50080: ldr             x2, [fp, #0x18]
    // 0xa50084: ldr             x3, [fp, #0x10]
    // 0xa50088: r0 = onDaySelected()
    //     0xa50088: bl              #0xa500a0  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onDaySelected
    // 0xa5008c: LeaveFrame
    //     0xa5008c: mov             SP, fp
    //     0xa50090: ldp             fp, lr, [SP], #0x10
    // 0xa50094: ret
    //     0xa50094: ret             
    // 0xa50098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50098: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5009c: b               #0xa50080
  }
  _ onDaySelected(/* No info */) {
    // ** addr: 0xa500a0, size: 0x94
    // 0xa500a0: EnterFrame
    //     0xa500a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa500a4: mov             fp, SP
    // 0xa500a8: AllocStack(0x18)
    //     0xa500a8: sub             SP, SP, #0x18
    // 0xa500ac: SetupParameters(CalendarController this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xa500ac: mov             x0, x1
    //     0xa500b0: stur            x2, [fp, #-0x10]
    //     0xa500b4: mov             x16, x3
    //     0xa500b8: mov             x3, x2
    //     0xa500bc: mov             x2, x16
    //     0xa500c0: stur            x1, [fp, #-8]
    //     0xa500c4: stur            x2, [fp, #-0x18]
    // 0xa500c8: CheckStackOverflow
    //     0xa500c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa500cc: cmp             SP, x16
    //     0xa500d0: b.ls            #0xa5012c
    // 0xa500d4: LoadField: r1 = r0->field_4b
    //     0xa500d4: ldur            w1, [x0, #0x4b]
    // 0xa500d8: DecompressPointer r1
    //     0xa500d8: add             x1, x1, HEAP, lsl #32
    // 0xa500dc: r0 = value()
    //     0xa500dc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa500e0: ldur            x1, [fp, #-8]
    // 0xa500e4: mov             x2, x0
    // 0xa500e8: ldur            x3, [fp, #-0x10]
    // 0xa500ec: r0 = _isSameDay()
    //     0xa500ec: bl              #0xa50134  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::_isSameDay
    // 0xa500f0: tbz             w0, #4, #0xa5011c
    // 0xa500f4: ldur            x0, [fp, #-8]
    // 0xa500f8: LoadField: r1 = r0->field_4b
    //     0xa500f8: ldur            w1, [x0, #0x4b]
    // 0xa500fc: DecompressPointer r1
    //     0xa500fc: add             x1, x1, HEAP, lsl #32
    // 0xa50100: ldur            x2, [fp, #-0x10]
    // 0xa50104: r0 = value=()
    //     0xa50104: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa50108: ldur            x0, [fp, #-8]
    // 0xa5010c: LoadField: r1 = r0->field_47
    //     0xa5010c: ldur            w1, [x0, #0x47]
    // 0xa50110: DecompressPointer r1
    //     0xa50110: add             x1, x1, HEAP, lsl #32
    // 0xa50114: ldur            x2, [fp, #-0x18]
    // 0xa50118: r0 = value=()
    //     0xa50118: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa5011c: r0 = Null
    //     0xa5011c: mov             x0, NULL
    // 0xa50120: LeaveFrame
    //     0xa50120: mov             SP, fp
    //     0xa50124: ldp             fp, lr, [SP], #0x10
    // 0xa50128: ret
    //     0xa50128: ret             
    // 0xa5012c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5012c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50130: b               #0xa500d4
  }
  _ _isSameDay(/* No info */) {
    // ** addr: 0xa50134, size: 0x13c
    // 0xa50134: EnterFrame
    //     0xa50134: stp             fp, lr, [SP, #-0x10]!
    //     0xa50138: mov             fp, SP
    // 0xa5013c: AllocStack(0x18)
    //     0xa5013c: sub             SP, SP, #0x18
    // 0xa50140: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xa50140: stur            x2, [fp, #-8]
    //     0xa50144: mov             x16, x3
    //     0xa50148: mov             x3, x2
    //     0xa5014c: mov             x2, x16
    //     0xa50150: stur            x2, [fp, #-0x10]
    // 0xa50154: CheckStackOverflow
    //     0xa50154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50158: cmp             SP, x16
    //     0xa5015c: b.ls            #0xa50268
    // 0xa50160: r0 = LoadClassIdInstr(r3)
    //     0xa50160: ldur            x0, [x3, #-1]
    //     0xa50164: ubfx            x0, x0, #0xc, #0x14
    // 0xa50168: mov             x1, x3
    // 0xa5016c: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa5016c: sub             lr, x0, #0xff6
    //     0xa50170: ldr             lr, [x21, lr, lsl #3]
    //     0xa50174: blr             lr
    // 0xa50178: mov             x3, x0
    // 0xa5017c: ldur            x2, [fp, #-0x10]
    // 0xa50180: stur            x3, [fp, #-0x18]
    // 0xa50184: r0 = LoadClassIdInstr(r2)
    //     0xa50184: ldur            x0, [x2, #-1]
    //     0xa50188: ubfx            x0, x0, #0xc, #0x14
    // 0xa5018c: mov             x1, x2
    // 0xa50190: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa50190: sub             lr, x0, #0xff6
    //     0xa50194: ldr             lr, [x21, lr, lsl #3]
    //     0xa50198: blr             lr
    // 0xa5019c: mov             x1, x0
    // 0xa501a0: ldur            x0, [fp, #-0x18]
    // 0xa501a4: cmp             x0, x1
    // 0xa501a8: b.ne            #0xa50258
    // 0xa501ac: ldur            x3, [fp, #-8]
    // 0xa501b0: ldur            x2, [fp, #-0x10]
    // 0xa501b4: r0 = LoadClassIdInstr(r3)
    //     0xa501b4: ldur            x0, [x3, #-1]
    //     0xa501b8: ubfx            x0, x0, #0xc, #0x14
    // 0xa501bc: mov             x1, x3
    // 0xa501c0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa501c0: sub             lr, x0, #0xfff
    //     0xa501c4: ldr             lr, [x21, lr, lsl #3]
    //     0xa501c8: blr             lr
    // 0xa501cc: mov             x3, x0
    // 0xa501d0: ldur            x2, [fp, #-0x10]
    // 0xa501d4: stur            x3, [fp, #-0x18]
    // 0xa501d8: r0 = LoadClassIdInstr(r2)
    //     0xa501d8: ldur            x0, [x2, #-1]
    //     0xa501dc: ubfx            x0, x0, #0xc, #0x14
    // 0xa501e0: mov             x1, x2
    // 0xa501e4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa501e4: sub             lr, x0, #0xfff
    //     0xa501e8: ldr             lr, [x21, lr, lsl #3]
    //     0xa501ec: blr             lr
    // 0xa501f0: mov             x1, x0
    // 0xa501f4: ldur            x0, [fp, #-0x18]
    // 0xa501f8: cmp             x0, x1
    // 0xa501fc: b.ne            #0xa50258
    // 0xa50200: ldur            x1, [fp, #-8]
    // 0xa50204: ldur            x2, [fp, #-0x10]
    // 0xa50208: r0 = LoadClassIdInstr(r1)
    //     0xa50208: ldur            x0, [x1, #-1]
    //     0xa5020c: ubfx            x0, x0, #0xc, #0x14
    // 0xa50210: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa50210: sub             lr, x0, #0xfdf
    //     0xa50214: ldr             lr, [x21, lr, lsl #3]
    //     0xa50218: blr             lr
    // 0xa5021c: mov             x2, x0
    // 0xa50220: ldur            x1, [fp, #-0x10]
    // 0xa50224: stur            x2, [fp, #-0x18]
    // 0xa50228: r0 = LoadClassIdInstr(r1)
    //     0xa50228: ldur            x0, [x1, #-1]
    //     0xa5022c: ubfx            x0, x0, #0xc, #0x14
    // 0xa50230: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa50230: sub             lr, x0, #0xfdf
    //     0xa50234: ldr             lr, [x21, lr, lsl #3]
    //     0xa50238: blr             lr
    // 0xa5023c: ldur            x1, [fp, #-0x18]
    // 0xa50240: cmp             x1, x0
    // 0xa50244: r16 = true
    //     0xa50244: add             x16, NULL, #0x20  ; true
    // 0xa50248: r17 = false
    //     0xa50248: add             x17, NULL, #0x30  ; false
    // 0xa5024c: csel            x2, x16, x17, eq
    // 0xa50250: mov             x0, x2
    // 0xa50254: b               #0xa5025c
    // 0xa50258: r0 = false
    //     0xa50258: add             x0, NULL, #0x30  ; false
    // 0xa5025c: LeaveFrame
    //     0xa5025c: mov             SP, fp
    //     0xa50260: ldp             fp, lr, [SP], #0x10
    // 0xa50264: ret
    //     0xa50264: ret             
    // 0xa50268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5026c: b               #0xa50160
  }
  [closure] Future<void> onPageChanged(dynamic, DateTime) {
    // ** addr: 0xa50bc0, size: 0x3c
    // 0xa50bc0: EnterFrame
    //     0xa50bc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa50bc4: mov             fp, SP
    // 0xa50bc8: ldr             x0, [fp, #0x18]
    // 0xa50bcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa50bcc: ldur            w1, [x0, #0x17]
    // 0xa50bd0: DecompressPointer r1
    //     0xa50bd0: add             x1, x1, HEAP, lsl #32
    // 0xa50bd4: CheckStackOverflow
    //     0xa50bd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50bd8: cmp             SP, x16
    //     0xa50bdc: b.ls            #0xa50bf4
    // 0xa50be0: ldr             x2, [fp, #0x10]
    // 0xa50be4: r0 = onPageChanged()
    //     0xa50be4: bl              #0xa50bfc  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onPageChanged
    // 0xa50be8: LeaveFrame
    //     0xa50be8: mov             SP, fp
    //     0xa50bec: ldp             fp, lr, [SP], #0x10
    // 0xa50bf0: ret
    //     0xa50bf0: ret             
    // 0xa50bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50bf8: b               #0xa50be0
  }
  _ onPageChanged(/* No info */) async {
    // ** addr: 0xa50bfc, size: 0x6c
    // 0xa50bfc: EnterFrame
    //     0xa50bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xa50c00: mov             fp, SP
    // 0xa50c04: AllocStack(0x18)
    //     0xa50c04: sub             SP, SP, #0x18
    // 0xa50c08: SetupParameters(CalendarController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa50c08: stur            NULL, [fp, #-8]
    //     0xa50c0c: stur            x1, [fp, #-0x10]
    //     0xa50c10: stur            x2, [fp, #-0x18]
    // 0xa50c14: CheckStackOverflow
    //     0xa50c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50c18: cmp             SP, x16
    //     0xa50c1c: b.ls            #0xa50c60
    // 0xa50c20: InitAsync() -> Future<void?>
    //     0xa50c20: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa50c24: bl              #0x661298  ; InitAsyncStub
    // 0xa50c28: ldur            x0, [fp, #-0x10]
    // 0xa50c2c: LoadField: r1 = r0->field_47
    //     0xa50c2c: ldur            w1, [x0, #0x47]
    // 0xa50c30: DecompressPointer r1
    //     0xa50c30: add             x1, x1, HEAP, lsl #32
    // 0xa50c34: ldur            x2, [fp, #-0x18]
    // 0xa50c38: r0 = value=()
    //     0xa50c38: bl              #0x7da5fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa50c3c: ldur            x1, [fp, #-0x10]
    // 0xa50c40: r0 = loadEvent()
    //     0xa50c40: bl              #0x8ed330  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::loadEvent
    // 0xa50c44: mov             x1, x0
    // 0xa50c48: stur            x1, [fp, #-0x18]
    // 0xa50c4c: r0 = Await()
    //     0xa50c4c: bl              #0x661044  ; AwaitStub
    // 0xa50c50: ldur            x1, [fp, #-0x10]
    // 0xa50c54: r0 = setCalenderHeader()
    //     0xa50c54: bl              #0x8eed60  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::setCalenderHeader
    // 0xa50c58: r0 = Null
    //     0xa50c58: mov             x0, NULL
    // 0xa50c5c: r0 = ReturnAsyncNotFuture()
    //     0xa50c5c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa50c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50c60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50c64: b               #0xa50c20
  }
  _ isSelected(/* No info */) {
    // ** addr: 0xad5cd4, size: 0x54
    // 0xad5cd4: EnterFrame
    //     0xad5cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xad5cd8: mov             fp, SP
    // 0xad5cdc: AllocStack(0x10)
    //     0xad5cdc: sub             SP, SP, #0x10
    // 0xad5ce0: SetupParameters(CalendarController this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xad5ce0: mov             x0, x1
    //     0xad5ce4: stur            x1, [fp, #-8]
    //     0xad5ce8: stur            x2, [fp, #-0x10]
    // 0xad5cec: CheckStackOverflow
    //     0xad5cec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5cf0: cmp             SP, x16
    //     0xad5cf4: b.ls            #0xad5d20
    // 0xad5cf8: LoadField: r1 = r0->field_4b
    //     0xad5cf8: ldur            w1, [x0, #0x4b]
    // 0xad5cfc: DecompressPointer r1
    //     0xad5cfc: add             x1, x1, HEAP, lsl #32
    // 0xad5d00: r0 = value()
    //     0xad5d00: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad5d04: ldur            x1, [fp, #-8]
    // 0xad5d08: ldur            x2, [fp, #-0x10]
    // 0xad5d0c: mov             x3, x0
    // 0xad5d10: r0 = _isSameDay()
    //     0xad5d10: bl              #0xa50134  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::_isSameDay
    // 0xad5d14: LeaveFrame
    //     0xad5d14: mov             SP, fp
    //     0xad5d18: ldp             fp, lr, [SP], #0x10
    // 0xad5d1c: ret
    //     0xad5d1c: ret             
    // 0xad5d20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5d20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5d24: b               #0xad5cf8
  }
  _ onEventActionPressed(/* No info */) async {
    // ** addr: 0xad5d8c, size: 0xe4
    // 0xad5d8c: EnterFrame
    //     0xad5d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xad5d90: mov             fp, SP
    // 0xad5d94: AllocStack(0x38)
    //     0xad5d94: sub             SP, SP, #0x38
    // 0xad5d98: SetupParameters(CalendarController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xad5d98: stur            NULL, [fp, #-8]
    //     0xad5d9c: stur            x1, [fp, #-0x10]
    //     0xad5da0: stur            x2, [fp, #-0x18]
    // 0xad5da4: CheckStackOverflow
    //     0xad5da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad5da8: cmp             SP, x16
    //     0xad5dac: b.ls            #0xad5e64
    // 0xad5db0: InitAsync() -> Future<void?>
    //     0xad5db0: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xad5db4: bl              #0x661298  ; InitAsyncStub
    // 0xad5db8: ldur            x0, [fp, #-0x18]
    // 0xad5dbc: LoadField: r2 = r0->field_13
    //     0xad5dbc: ldur            w2, [x0, #0x13]
    // 0xad5dc0: DecompressPointer r2
    //     0xad5dc0: add             x2, x2, HEAP, lsl #32
    // 0xad5dc4: stur            x2, [fp, #-0x10]
    // 0xad5dc8: cmp             w2, NULL
    // 0xad5dcc: b.eq            #0xad5e6c
    // 0xad5dd0: mov             x1, x2
    // 0xad5dd4: r0 = hasMatch()
    //     0xad5dd4: bl              #0x91087c  ; [package:get/get_utils/src/get_utils/get_utils.dart] GetUtils::hasMatch
    // 0xad5dd8: tbnz            w0, #4, #0xad5e18
    // 0xad5ddc: ldur            x1, [fp, #-0x10]
    // 0xad5de0: r0 = canLaunchUrlString()
    //     0xad5de0: bl              #0x7da07c  ; [package:url_launcher/src/url_launcher_string.dart] ::canLaunchUrlString
    // 0xad5de4: mov             x1, x0
    // 0xad5de8: stur            x1, [fp, #-0x20]
    // 0xad5dec: r0 = Await()
    //     0xad5dec: bl              #0x661044  ; AwaitStub
    // 0xad5df0: r16 = true
    //     0xad5df0: add             x16, NULL, #0x20  ; true
    // 0xad5df4: cmp             w0, w16
    // 0xad5df8: b.ne            #0xad5e5c
    // 0xad5dfc: ldur            x1, [fp, #-0x10]
    // 0xad5e00: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xad5e00: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xad5e04: r0 = launchUrlString()
    //     0xad5e04: bl              #0x7d9cc8  ; [package:url_launcher/src/url_launcher_string.dart] ::launchUrlString
    // 0xad5e08: mov             x1, x0
    // 0xad5e0c: stur            x1, [fp, #-0x20]
    // 0xad5e10: r0 = Await()
    //     0xad5e10: bl              #0x661044  ; AwaitStub
    // 0xad5e14: b               #0xad5e5c
    // 0xad5e18: ldur            x0, [fp, #-0x18]
    // 0xad5e1c: r0 = InitLateStaticField(0x1338) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xad5e1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xad5e20: ldr             x0, [x0, #0x2670]
    //     0xad5e24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xad5e28: cmp             w0, w16
    //     0xad5e2c: b.ne            #0xad5e38
    //     0xad5e30: ldr             x2, [PP, #0x88]  ; [pp+0x88] Field <::.Get>: static late final (offset: 0x1338)
    //     0xad5e34: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xad5e38: ldur            x0, [fp, #-0x18]
    // 0xad5e3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad5e3c: ldur            w1, [x0, #0x17]
    // 0xad5e40: DecompressPointer r1
    //     0xad5e40: add             x1, x1, HEAP, lsl #32
    // 0xad5e44: ldur            x16, [fp, #-0x10]
    // 0xad5e48: stp             x16, NULL, [SP, #8]
    // 0xad5e4c: str             x1, [SP]
    // 0xad5e50: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xad5e50: add             x4, PP, #0x1a, lsl #12  ; [pp+0x1a478] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xad5e54: ldr             x4, [x4, #0x478]
    // 0xad5e58: r0 = GetNavigation.toNamed()
    //     0xad5e58: bl              #0x659568  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xad5e5c: r0 = Null
    //     0xad5e5c: mov             x0, NULL
    // 0xad5e60: r0 = ReturnAsyncNotFuture()
    //     0xad5e60: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xad5e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad5e64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad5e68: b               #0xad5db0
    // 0xad5e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad5e6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void onCalendarCreated(dynamic, PageController) {
    // ** addr: 0xad6428, size: 0x3c
    // 0xad6428: ldr             x1, [SP, #8]
    // 0xad642c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xad642c: ldur            w2, [x1, #0x17]
    // 0xad6430: DecompressPointer r2
    //     0xad6430: add             x2, x2, HEAP, lsl #32
    // 0xad6434: ldr             x0, [SP]
    // 0xad6438: StoreField: r2->field_57 = r0
    //     0xad6438: stur            w0, [x2, #0x57]
    //     0xad643c: ldurb           w16, [x2, #-1]
    //     0xad6440: ldurb           w17, [x0, #-1]
    //     0xad6444: and             x16, x17, x16, lsr #2
    //     0xad6448: tst             x16, HEAP, lsr #32
    //     0xad644c: b.eq            #0xad645c
    //     0xad6450: str             lr, [SP, #-8]!
    //     0xad6454: bl              #0xec0a48  ; WriteBarrierWrappersStub
    //     0xad6458: ldr             lr, [SP], #8
    // 0xad645c: ldr             x0, [SP]
    // 0xad6460: ret
    //     0xad6460: ret             
  }
  DateTime lastDay(CalendarController) {
    // ** addr: 0xad6464, size: 0x58
    // 0xad6464: EnterFrame
    //     0xad6464: stp             fp, lr, [SP, #-0x10]!
    //     0xad6468: mov             fp, SP
    // 0xad646c: AllocStack(0x18)
    //     0xad646c: sub             SP, SP, #0x18
    // 0xad6470: CheckStackOverflow
    //     0xad6470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6474: cmp             SP, x16
    //     0xad6478: b.ls            #0xad64b4
    // 0xad647c: r0 = DateTime()
    //     0xad647c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xad6480: stur            x0, [fp, #-8]
    // 0xad6484: r16 = 24
    //     0xad6484: movz            x16, #0x18
    // 0xad6488: r30 = 62
    //     0xad6488: movz            lr, #0x3e
    // 0xad648c: stp             lr, x16, [SP]
    // 0xad6490: mov             x1, x0
    // 0xad6494: r2 = 2027
    //     0xad6494: movz            x2, #0x7eb
    // 0xad6498: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xad6498: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xad649c: ldr             x4, [x4, #0xe00]
    // 0xad64a0: r0 = DateTime()
    //     0xad64a0: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0xad64a4: ldur            x0, [fp, #-8]
    // 0xad64a8: LeaveFrame
    //     0xad64a8: mov             SP, fp
    //     0xad64ac: ldp             fp, lr, [SP], #0x10
    // 0xad64b0: ret
    //     0xad64b0: ret             
    // 0xad64b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad64b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad64b8: b               #0xad647c
  }
  DateTime firstDay(CalendarController) {
    // ** addr: 0xad64bc, size: 0x58
    // 0xad64bc: EnterFrame
    //     0xad64bc: stp             fp, lr, [SP, #-0x10]!
    //     0xad64c0: mov             fp, SP
    // 0xad64c4: AllocStack(0x18)
    //     0xad64c4: sub             SP, SP, #0x18
    // 0xad64c8: CheckStackOverflow
    //     0xad64c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad64cc: cmp             SP, x16
    //     0xad64d0: b.ls            #0xad650c
    // 0xad64d4: r0 = DateTime()
    //     0xad64d4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xad64d8: stur            x0, [fp, #-8]
    // 0xad64dc: r16 = 2
    //     0xad64dc: movz            x16, #0x2
    // 0xad64e0: r30 = 2
    //     0xad64e0: movz            lr, #0x2
    // 0xad64e4: stp             lr, x16, [SP]
    // 0xad64e8: mov             x1, x0
    // 0xad64ec: r2 = 2021
    //     0xad64ec: movz            x2, #0x7e5
    // 0xad64f0: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xad64f0: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xad64f4: ldr             x4, [x4, #0xe00]
    // 0xad64f8: r0 = DateTime()
    //     0xad64f8: bl              #0x817134  ; [dart:core] DateTime::DateTime
    // 0xad64fc: ldur            x0, [fp, #-8]
    // 0xad6500: LeaveFrame
    //     0xad6500: mov             SP, fp
    //     0xad6504: ldp             fp, lr, [SP], #0x10
    // 0xad6508: ret
    //     0xad6508: ret             
    // 0xad650c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad650c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6510: b               #0xad64d4
  }
  [closure] void nextCalendarPage(dynamic) {
    // ** addr: 0xad661c, size: 0x38
    // 0xad661c: EnterFrame
    //     0xad661c: stp             fp, lr, [SP, #-0x10]!
    //     0xad6620: mov             fp, SP
    // 0xad6624: ldr             x0, [fp, #0x10]
    // 0xad6628: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad6628: ldur            w1, [x0, #0x17]
    // 0xad662c: DecompressPointer r1
    //     0xad662c: add             x1, x1, HEAP, lsl #32
    // 0xad6630: CheckStackOverflow
    //     0xad6630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6634: cmp             SP, x16
    //     0xad6638: b.ls            #0xad664c
    // 0xad663c: r0 = nextCalendarPage()
    //     0xad663c: bl              #0xad6654  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::nextCalendarPage
    // 0xad6640: LeaveFrame
    //     0xad6640: mov             SP, fp
    //     0xad6644: ldp             fp, lr, [SP], #0x10
    // 0xad6648: ret
    //     0xad6648: ret             
    // 0xad664c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad664c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6650: b               #0xad663c
  }
  _ nextCalendarPage(/* No info */) {
    // ** addr: 0xad6654, size: 0x54
    // 0xad6654: EnterFrame
    //     0xad6654: stp             fp, lr, [SP, #-0x10]!
    //     0xad6658: mov             fp, SP
    // 0xad665c: CheckStackOverflow
    //     0xad665c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6660: cmp             SP, x16
    //     0xad6664: b.ls            #0xad6694
    // 0xad6668: LoadField: r0 = r1->field_57
    //     0xad6668: ldur            w0, [x1, #0x57]
    // 0xad666c: DecompressPointer r0
    //     0xad666c: add             x0, x0, HEAP, lsl #32
    // 0xad6670: r16 = Sentinel
    //     0xad6670: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad6674: cmp             w0, w16
    // 0xad6678: b.eq            #0xad669c
    // 0xad667c: mov             x1, x0
    // 0xad6680: r0 = nextPage()
    //     0xad6680: bl              #0xad66a8  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xad6684: r0 = Null
    //     0xad6684: mov             x0, NULL
    // 0xad6688: LeaveFrame
    //     0xad6688: mov             SP, fp
    //     0xad668c: ldp             fp, lr, [SP], #0x10
    // 0xad6690: ret
    //     0xad6690: ret             
    // 0xad6694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6698: b               #0xad6668
    // 0xad669c: r9 = _calendarController
    //     0xad669c: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Field <CalendarController._calendarController@1913449503>: late (offset: 0x58)
    //     0xad66a0: ldr             x9, [x9, #0xb20]
    // 0xad66a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad66a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void prevCalendarPage(dynamic) {
    // ** addr: 0xad6788, size: 0x38
    // 0xad6788: EnterFrame
    //     0xad6788: stp             fp, lr, [SP, #-0x10]!
    //     0xad678c: mov             fp, SP
    // 0xad6790: ldr             x0, [fp, #0x10]
    // 0xad6794: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xad6794: ldur            w1, [x0, #0x17]
    // 0xad6798: DecompressPointer r1
    //     0xad6798: add             x1, x1, HEAP, lsl #32
    // 0xad679c: CheckStackOverflow
    //     0xad679c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad67a0: cmp             SP, x16
    //     0xad67a4: b.ls            #0xad67b8
    // 0xad67a8: r0 = prevCalendarPage()
    //     0xad67a8: bl              #0xad67c0  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::prevCalendarPage
    // 0xad67ac: LeaveFrame
    //     0xad67ac: mov             SP, fp
    //     0xad67b0: ldp             fp, lr, [SP], #0x10
    // 0xad67b4: ret
    //     0xad67b4: ret             
    // 0xad67b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad67b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad67bc: b               #0xad67a8
  }
  _ prevCalendarPage(/* No info */) {
    // ** addr: 0xad67c0, size: 0x54
    // 0xad67c0: EnterFrame
    //     0xad67c0: stp             fp, lr, [SP, #-0x10]!
    //     0xad67c4: mov             fp, SP
    // 0xad67c8: CheckStackOverflow
    //     0xad67c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad67cc: cmp             SP, x16
    //     0xad67d0: b.ls            #0xad6800
    // 0xad67d4: LoadField: r0 = r1->field_57
    //     0xad67d4: ldur            w0, [x1, #0x57]
    // 0xad67d8: DecompressPointer r0
    //     0xad67d8: add             x0, x0, HEAP, lsl #32
    // 0xad67dc: r16 = Sentinel
    //     0xad67dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xad67e0: cmp             w0, w16
    // 0xad67e4: b.eq            #0xad6808
    // 0xad67e8: mov             x1, x0
    // 0xad67ec: r0 = previousPage()
    //     0xad67ec: bl              #0xad6814  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xad67f0: r0 = Null
    //     0xad67f0: mov             x0, NULL
    // 0xad67f4: LeaveFrame
    //     0xad67f4: mov             SP, fp
    //     0xad67f8: ldp             fp, lr, [SP], #0x10
    // 0xad67fc: ret
    //     0xad67fc: ret             
    // 0xad6800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6804: b               #0xad67d4
    // 0xad6808: r9 = _calendarController
    //     0xad6808: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Field <CalendarController._calendarController@1913449503>: late (offset: 0x58)
    //     0xad680c: ldr             x9, [x9, #0xb20]
    // 0xad6810: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xad6810: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ appBarSubtitle(/* No info */) {
    // ** addr: 0xad6d18, size: 0x5c
    // 0xad6d18: EnterFrame
    //     0xad6d18: stp             fp, lr, [SP, #-0x10]!
    //     0xad6d1c: mov             fp, SP
    // 0xad6d20: AllocStack(0x8)
    //     0xad6d20: sub             SP, SP, #8
    // 0xad6d24: CheckStackOverflow
    //     0xad6d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6d28: cmp             SP, x16
    //     0xad6d2c: b.ls            #0xad6d6c
    // 0xad6d30: LoadField: r0 = r1->field_27
    //     0xad6d30: ldur            w0, [x1, #0x27]
    // 0xad6d34: DecompressPointer r0
    //     0xad6d34: add             x0, x0, HEAP, lsl #32
    // 0xad6d38: stur            x0, [fp, #-8]
    // 0xad6d3c: LoadField: r2 = r1->field_4b
    //     0xad6d3c: ldur            w2, [x1, #0x4b]
    // 0xad6d40: DecompressPointer r2
    //     0xad6d40: add             x2, x2, HEAP, lsl #32
    // 0xad6d44: mov             x1, x2
    // 0xad6d48: r0 = value()
    //     0xad6d48: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6d4c: ldur            x1, [fp, #-8]
    // 0xad6d50: mov             x2, x0
    // 0xad6d54: r0 = from()
    //     0xad6d54: bl              #0x815594  ; [package:nuonline/services/hijri_service.dart] HijriService::from
    // 0xad6d58: mov             x1, x0
    // 0xad6d5c: r0 = dMY()
    //     0xad6d5c: bl              #0x81f29c  ; [package:nuonline/common/utils/hijri/hijri_calendar.dart] NHijriCalendar::dMY
    // 0xad6d60: LeaveFrame
    //     0xad6d60: mov             SP, fp
    //     0xad6d64: ldp             fp, lr, [SP], #0x10
    // 0xad6d68: ret
    //     0xad6d68: ret             
    // 0xad6d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad6d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad6d70: b               #0xad6d30
  }
  get _ appBarTitle(/* No info */) {
    // ** addr: 0xad6d74, size: 0x480
    // 0xad6d74: EnterFrame
    //     0xad6d74: stp             fp, lr, [SP, #-0x10]!
    //     0xad6d78: mov             fp, SP
    // 0xad6d7c: AllocStack(0x20)
    //     0xad6d7c: sub             SP, SP, #0x20
    // 0xad6d80: SetupParameters(CalendarController this /* r1 => r0, fp-0x8 */)
    //     0xad6d80: mov             x0, x1
    //     0xad6d84: stur            x1, [fp, #-8]
    // 0xad6d88: CheckStackOverflow
    //     0xad6d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad6d8c: cmp             SP, x16
    //     0xad6d90: b.ls            #0xad71e8
    // 0xad6d94: LoadField: r1 = r0->field_4b
    //     0xad6d94: ldur            w1, [x0, #0x4b]
    // 0xad6d98: DecompressPointer r1
    //     0xad6d98: add             x1, x1, HEAP, lsl #32
    // 0xad6d9c: r0 = value()
    //     0xad6d9c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6da0: r1 = LoadClassIdInstr(r0)
    //     0xad6da0: ldur            x1, [x0, #-1]
    //     0xad6da4: ubfx            x1, x1, #0xc, #0x14
    // 0xad6da8: mov             x16, x0
    // 0xad6dac: mov             x0, x1
    // 0xad6db0: mov             x1, x16
    // 0xad6db4: r0 = GDT[cid_x0 + -0xfad]()
    //     0xad6db4: sub             lr, x0, #0xfad
    //     0xad6db8: ldr             lr, [x21, lr, lsl #3]
    //     0xad6dbc: blr             lr
    // 0xad6dc0: mov             x2, x0
    // 0xad6dc4: cmp             x2, #4
    // 0xad6dc8: b.gt            #0xad6e30
    // 0xad6dcc: cmp             x2, #2
    // 0xad6dd0: b.gt            #0xad6e10
    // 0xad6dd4: cmp             x2, #1
    // 0xad6dd8: b.gt            #0xad6e04
    // 0xad6ddc: r0 = BoxInt64Instr(r2)
    //     0xad6ddc: sbfiz           x0, x2, #1, #0x1f
    //     0xad6de0: cmp             x2, x0, asr #1
    //     0xad6de4: b.eq            #0xad6df0
    //     0xad6de8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad6dec: stur            x2, [x0, #7]
    // 0xad6df0: cmp             w0, #2
    // 0xad6df4: b.ne            #0xad6e80
    // 0xad6df8: r3 = "Senin"
    //     0xad6df8: add             x3, PP, #9, lsl #12  ; [pp+0x90f8] "Senin"
    //     0xad6dfc: ldr             x3, [x3, #0xf8]
    // 0xad6e00: b               #0xad6e88
    // 0xad6e04: r3 = "Selasa"
    //     0xad6e04: add             x3, PP, #9, lsl #12  ; [pp+0x9100] "Selasa"
    //     0xad6e08: ldr             x3, [x3, #0x100]
    // 0xad6e0c: b               #0xad6e88
    // 0xad6e10: cmp             x2, #3
    // 0xad6e14: b.gt            #0xad6e24
    // 0xad6e18: r3 = "Rabu"
    //     0xad6e18: add             x3, PP, #9, lsl #12  ; [pp+0x9108] "Rabu"
    //     0xad6e1c: ldr             x3, [x3, #0x108]
    // 0xad6e20: b               #0xad6e88
    // 0xad6e24: r3 = "Kamis"
    //     0xad6e24: add             x3, PP, #9, lsl #12  ; [pp+0x9110] "Kamis"
    //     0xad6e28: ldr             x3, [x3, #0x110]
    // 0xad6e2c: b               #0xad6e88
    // 0xad6e30: cmp             x2, #6
    // 0xad6e34: b.gt            #0xad6e58
    // 0xad6e38: cmp             x2, #5
    // 0xad6e3c: b.gt            #0xad6e4c
    // 0xad6e40: r3 = "Jumat"
    //     0xad6e40: add             x3, PP, #9, lsl #12  ; [pp+0x9118] "Jumat"
    //     0xad6e44: ldr             x3, [x3, #0x118]
    // 0xad6e48: b               #0xad6e88
    // 0xad6e4c: r3 = "Sabtu"
    //     0xad6e4c: add             x3, PP, #9, lsl #12  ; [pp+0x9120] "Sabtu"
    //     0xad6e50: ldr             x3, [x3, #0x120]
    // 0xad6e54: b               #0xad6e88
    // 0xad6e58: r0 = BoxInt64Instr(r2)
    //     0xad6e58: sbfiz           x0, x2, #1, #0x1f
    //     0xad6e5c: cmp             x2, x0, asr #1
    //     0xad6e60: b.eq            #0xad6e6c
    //     0xad6e64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad6e68: stur            x2, [x0, #7]
    // 0xad6e6c: cmp             w0, #0xe
    // 0xad6e70: b.ne            #0xad6e80
    // 0xad6e74: r3 = "Ahad"
    //     0xad6e74: add             x3, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xad6e78: ldr             x3, [x3, #0x128]
    // 0xad6e7c: b               #0xad6e88
    // 0xad6e80: r3 = "Ahad"
    //     0xad6e80: add             x3, PP, #9, lsl #12  ; [pp+0x9128] "Ahad"
    //     0xad6e84: ldr             x3, [x3, #0x128]
    // 0xad6e88: ldur            x0, [fp, #-8]
    // 0xad6e8c: stur            x3, [fp, #-0x10]
    // 0xad6e90: r1 = Null
    //     0xad6e90: mov             x1, NULL
    // 0xad6e94: r2 = 18
    //     0xad6e94: movz            x2, #0x12
    // 0xad6e98: r0 = AllocateArray()
    //     0xad6e98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xad6e9c: mov             x2, x0
    // 0xad6ea0: ldur            x0, [fp, #-0x10]
    // 0xad6ea4: stur            x2, [fp, #-0x18]
    // 0xad6ea8: StoreField: r2->field_f = r0
    //     0xad6ea8: stur            w0, [x2, #0xf]
    // 0xad6eac: r16 = " "
    //     0xad6eac: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xad6eb0: StoreField: r2->field_13 = r16
    //     0xad6eb0: stur            w16, [x2, #0x13]
    // 0xad6eb4: ldur            x0, [fp, #-8]
    // 0xad6eb8: LoadField: r1 = r0->field_4b
    //     0xad6eb8: ldur            w1, [x0, #0x4b]
    // 0xad6ebc: DecompressPointer r1
    //     0xad6ebc: add             x1, x1, HEAP, lsl #32
    // 0xad6ec0: r0 = value()
    //     0xad6ec0: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6ec4: stur            x0, [fp, #-0x10]
    // 0xad6ec8: r0 = JavaneseCalendar()
    //     0xad6ec8: bl              #0x821fe0  ; AllocateJavaneseCalendarStub -> JavaneseCalendar (size=0x14)
    // 0xad6ecc: mov             x1, x0
    // 0xad6ed0: ldur            x2, [fp, #-0x10]
    // 0xad6ed4: stur            x0, [fp, #-0x10]
    // 0xad6ed8: r0 = JavaneseCalendar.fromDate()
    //     0xad6ed8: bl              #0x821c10  ; [package:nuonline/app/modules/calendar/widgets/javanese_calendar.dart] JavaneseCalendar::JavaneseCalendar.fromDate
    // 0xad6edc: ldur            x0, [fp, #-0x10]
    // 0xad6ee0: LoadField: r2 = r0->field_f
    //     0xad6ee0: ldur            w2, [x0, #0xf]
    // 0xad6ee4: DecompressPointer r2
    //     0xad6ee4: add             x2, x2, HEAP, lsl #32
    // 0xad6ee8: LoadField: r1 = r0->field_7
    //     0xad6ee8: ldur            x1, [x0, #7]
    // 0xad6eec: sub             x3, x1, #1
    // 0xad6ef0: LoadField: r0 = r2->field_b
    //     0xad6ef0: ldur            w0, [x2, #0xb]
    // 0xad6ef4: r1 = LoadInt32Instr(r0)
    //     0xad6ef4: sbfx            x1, x0, #1, #0x1f
    // 0xad6ef8: mov             x0, x1
    // 0xad6efc: mov             x1, x3
    // 0xad6f00: cmp             x1, x0
    // 0xad6f04: b.hs            #0xad71f0
    // 0xad6f08: LoadField: r0 = r2->field_f
    //     0xad6f08: ldur            w0, [x2, #0xf]
    // 0xad6f0c: DecompressPointer r0
    //     0xad6f0c: add             x0, x0, HEAP, lsl #32
    // 0xad6f10: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xad6f10: add             x16, x0, x3, lsl #2
    //     0xad6f14: ldur            w1, [x16, #0xf]
    // 0xad6f18: DecompressPointer r1
    //     0xad6f18: add             x1, x1, HEAP, lsl #32
    // 0xad6f1c: mov             x0, x1
    // 0xad6f20: ldur            x1, [fp, #-0x18]
    // 0xad6f24: ArrayStore: r1[2] = r0  ; List_4
    //     0xad6f24: add             x25, x1, #0x17
    //     0xad6f28: str             w0, [x25]
    //     0xad6f2c: tbz             w0, #0, #0xad6f48
    //     0xad6f30: ldurb           w16, [x1, #-1]
    //     0xad6f34: ldurb           w17, [x0, #-1]
    //     0xad6f38: and             x16, x17, x16, lsr #2
    //     0xad6f3c: tst             x16, HEAP, lsr #32
    //     0xad6f40: b.eq            #0xad6f48
    //     0xad6f44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad6f48: ldur            x0, [fp, #-0x18]
    // 0xad6f4c: r16 = ", "
    //     0xad6f4c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xad6f50: StoreField: r0->field_1b = r16
    //     0xad6f50: stur            w16, [x0, #0x1b]
    // 0xad6f54: ldur            x2, [fp, #-8]
    // 0xad6f58: LoadField: r1 = r2->field_4b
    //     0xad6f58: ldur            w1, [x2, #0x4b]
    // 0xad6f5c: DecompressPointer r1
    //     0xad6f5c: add             x1, x1, HEAP, lsl #32
    // 0xad6f60: r0 = value()
    //     0xad6f60: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6f64: r1 = LoadClassIdInstr(r0)
    //     0xad6f64: ldur            x1, [x0, #-1]
    //     0xad6f68: ubfx            x1, x1, #0xc, #0x14
    // 0xad6f6c: mov             x16, x0
    // 0xad6f70: mov             x0, x1
    // 0xad6f74: mov             x1, x16
    // 0xad6f78: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xad6f78: sub             lr, x0, #0xfdf
    //     0xad6f7c: ldr             lr, [x21, lr, lsl #3]
    //     0xad6f80: blr             lr
    // 0xad6f84: mov             x2, x0
    // 0xad6f88: r0 = BoxInt64Instr(r2)
    //     0xad6f88: sbfiz           x0, x2, #1, #0x1f
    //     0xad6f8c: cmp             x2, x0, asr #1
    //     0xad6f90: b.eq            #0xad6f9c
    //     0xad6f94: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad6f98: stur            x2, [x0, #7]
    // 0xad6f9c: ldur            x1, [fp, #-0x18]
    // 0xad6fa0: ArrayStore: r1[4] = r0  ; List_4
    //     0xad6fa0: add             x25, x1, #0x1f
    //     0xad6fa4: str             w0, [x25]
    //     0xad6fa8: tbz             w0, #0, #0xad6fc4
    //     0xad6fac: ldurb           w16, [x1, #-1]
    //     0xad6fb0: ldurb           w17, [x0, #-1]
    //     0xad6fb4: and             x16, x17, x16, lsr #2
    //     0xad6fb8: tst             x16, HEAP, lsr #32
    //     0xad6fbc: b.eq            #0xad6fc4
    //     0xad6fc0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad6fc4: ldur            x0, [fp, #-0x18]
    // 0xad6fc8: r16 = " "
    //     0xad6fc8: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xad6fcc: StoreField: r0->field_23 = r16
    //     0xad6fcc: stur            w16, [x0, #0x23]
    // 0xad6fd0: ldur            x2, [fp, #-8]
    // 0xad6fd4: LoadField: r1 = r2->field_4b
    //     0xad6fd4: ldur            w1, [x2, #0x4b]
    // 0xad6fd8: DecompressPointer r1
    //     0xad6fd8: add             x1, x1, HEAP, lsl #32
    // 0xad6fdc: r0 = value()
    //     0xad6fdc: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad6fe0: r1 = LoadClassIdInstr(r0)
    //     0xad6fe0: ldur            x1, [x0, #-1]
    //     0xad6fe4: ubfx            x1, x1, #0xc, #0x14
    // 0xad6fe8: mov             x16, x0
    // 0xad6fec: mov             x0, x1
    // 0xad6ff0: mov             x1, x16
    // 0xad6ff4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xad6ff4: sub             lr, x0, #0xfff
    //     0xad6ff8: ldr             lr, [x21, lr, lsl #3]
    //     0xad6ffc: blr             lr
    // 0xad7000: mov             x2, x0
    // 0xad7004: cmp             x2, #6
    // 0xad7008: b.gt            #0xad7098
    // 0xad700c: cmp             x2, #3
    // 0xad7010: b.gt            #0xad7064
    // 0xad7014: cmp             x2, #2
    // 0xad7018: b.gt            #0xad7058
    // 0xad701c: cmp             x2, #1
    // 0xad7020: b.gt            #0xad704c
    // 0xad7024: r0 = BoxInt64Instr(r2)
    //     0xad7024: sbfiz           x0, x2, #1, #0x1f
    //     0xad7028: cmp             x2, x0, asr #1
    //     0xad702c: b.eq            #0xad7038
    //     0xad7030: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad7034: stur            x2, [x0, #7]
    // 0xad7038: cmp             w0, #2
    // 0xad703c: b.ne            #0xad7124
    // 0xad7040: r0 = "Januari"
    //     0xad7040: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xad7044: ldr             x0, [x0, #0xb60]
    // 0xad7048: b               #0xad712c
    // 0xad704c: r0 = "Februari"
    //     0xad704c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb68] "Februari"
    //     0xad7050: ldr             x0, [x0, #0xb68]
    // 0xad7054: b               #0xad712c
    // 0xad7058: r0 = "Maret"
    //     0xad7058: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb70] "Maret"
    //     0xad705c: ldr             x0, [x0, #0xb70]
    // 0xad7060: b               #0xad712c
    // 0xad7064: cmp             x2, #5
    // 0xad7068: b.gt            #0xad708c
    // 0xad706c: cmp             x2, #4
    // 0xad7070: b.gt            #0xad7080
    // 0xad7074: r0 = "April"
    //     0xad7074: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb78] "April"
    //     0xad7078: ldr             x0, [x0, #0xb78]
    // 0xad707c: b               #0xad712c
    // 0xad7080: r0 = "Mei"
    //     0xad7080: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb80] "Mei"
    //     0xad7084: ldr             x0, [x0, #0xb80]
    // 0xad7088: b               #0xad712c
    // 0xad708c: r0 = "Juni"
    //     0xad708c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb88] "Juni"
    //     0xad7090: ldr             x0, [x0, #0xb88]
    // 0xad7094: b               #0xad712c
    // 0xad7098: cmp             x2, #9
    // 0xad709c: b.gt            #0xad70d4
    // 0xad70a0: cmp             x2, #8
    // 0xad70a4: b.gt            #0xad70c8
    // 0xad70a8: cmp             x2, #7
    // 0xad70ac: b.gt            #0xad70bc
    // 0xad70b0: r0 = "Juli"
    //     0xad70b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb90] "Juli"
    //     0xad70b4: ldr             x0, [x0, #0xb90]
    // 0xad70b8: b               #0xad712c
    // 0xad70bc: r0 = "Agustus"
    //     0xad70bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb98] "Agustus"
    //     0xad70c0: ldr             x0, [x0, #0xb98]
    // 0xad70c4: b               #0xad712c
    // 0xad70c8: r0 = "September"
    //     0xad70c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fba0] "September"
    //     0xad70cc: ldr             x0, [x0, #0xba0]
    // 0xad70d0: b               #0xad712c
    // 0xad70d4: cmp             x2, #0xb
    // 0xad70d8: b.gt            #0xad70fc
    // 0xad70dc: cmp             x2, #0xa
    // 0xad70e0: b.gt            #0xad70f0
    // 0xad70e4: r0 = "Oktober"
    //     0xad70e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fba8] "Oktober"
    //     0xad70e8: ldr             x0, [x0, #0xba8]
    // 0xad70ec: b               #0xad712c
    // 0xad70f0: r0 = "November"
    //     0xad70f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbb0] "November"
    //     0xad70f4: ldr             x0, [x0, #0xbb0]
    // 0xad70f8: b               #0xad712c
    // 0xad70fc: r0 = BoxInt64Instr(r2)
    //     0xad70fc: sbfiz           x0, x2, #1, #0x1f
    //     0xad7100: cmp             x2, x0, asr #1
    //     0xad7104: b.eq            #0xad7110
    //     0xad7108: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad710c: stur            x2, [x0, #7]
    // 0xad7110: cmp             w0, #0x18
    // 0xad7114: b.ne            #0xad7124
    // 0xad7118: r0 = "Desember"
    //     0xad7118: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fbb8] "Desember"
    //     0xad711c: ldr             x0, [x0, #0xbb8]
    // 0xad7120: b               #0xad712c
    // 0xad7124: r0 = "Januari"
    //     0xad7124: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb60] "Januari"
    //     0xad7128: ldr             x0, [x0, #0xb60]
    // 0xad712c: ldur            x3, [fp, #-8]
    // 0xad7130: ldur            x2, [fp, #-0x18]
    // 0xad7134: mov             x1, x2
    // 0xad7138: ArrayStore: r1[6] = r0  ; List_4
    //     0xad7138: add             x25, x1, #0x27
    //     0xad713c: str             w0, [x25]
    //     0xad7140: tbz             w0, #0, #0xad715c
    //     0xad7144: ldurb           w16, [x1, #-1]
    //     0xad7148: ldurb           w17, [x0, #-1]
    //     0xad714c: and             x16, x17, x16, lsr #2
    //     0xad7150: tst             x16, HEAP, lsr #32
    //     0xad7154: b.eq            #0xad715c
    //     0xad7158: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad715c: r16 = " "
    //     0xad715c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xad7160: StoreField: r2->field_2b = r16
    //     0xad7160: stur            w16, [x2, #0x2b]
    // 0xad7164: LoadField: r1 = r3->field_4b
    //     0xad7164: ldur            w1, [x3, #0x4b]
    // 0xad7168: DecompressPointer r1
    //     0xad7168: add             x1, x1, HEAP, lsl #32
    // 0xad716c: r0 = value()
    //     0xad716c: bl              #0xe3ff74  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xad7170: r1 = LoadClassIdInstr(r0)
    //     0xad7170: ldur            x1, [x0, #-1]
    //     0xad7174: ubfx            x1, x1, #0xc, #0x14
    // 0xad7178: mov             x16, x0
    // 0xad717c: mov             x0, x1
    // 0xad7180: mov             x1, x16
    // 0xad7184: r0 = GDT[cid_x0 + -0xff6]()
    //     0xad7184: sub             lr, x0, #0xff6
    //     0xad7188: ldr             lr, [x21, lr, lsl #3]
    //     0xad718c: blr             lr
    // 0xad7190: mov             x2, x0
    // 0xad7194: r0 = BoxInt64Instr(r2)
    //     0xad7194: sbfiz           x0, x2, #1, #0x1f
    //     0xad7198: cmp             x2, x0, asr #1
    //     0xad719c: b.eq            #0xad71a8
    //     0xad71a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xad71a4: stur            x2, [x0, #7]
    // 0xad71a8: ldur            x1, [fp, #-0x18]
    // 0xad71ac: ArrayStore: r1[8] = r0  ; List_4
    //     0xad71ac: add             x25, x1, #0x2f
    //     0xad71b0: str             w0, [x25]
    //     0xad71b4: tbz             w0, #0, #0xad71d0
    //     0xad71b8: ldurb           w16, [x1, #-1]
    //     0xad71bc: ldurb           w17, [x0, #-1]
    //     0xad71c0: and             x16, x17, x16, lsr #2
    //     0xad71c4: tst             x16, HEAP, lsr #32
    //     0xad71c8: b.eq            #0xad71d0
    //     0xad71cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xad71d0: ldur            x16, [fp, #-0x18]
    // 0xad71d4: str             x16, [SP]
    // 0xad71d8: r0 = _interpolate()
    //     0xad71d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xad71dc: LeaveFrame
    //     0xad71dc: mov             SP, fp
    //     0xad71e0: ldp             fp, lr, [SP], #0x10
    // 0xad71e4: ret
    //     0xad71e4: ret             
    // 0xad71e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad71e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad71ec: b               #0xad6d94
    // 0xad71f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad71f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
